#!/usr/bin/env bash

BASE_PATH=/home/<USER>
LOG_CONFIG=/home/<USER>/srbuild/conf/production-logback.xml
APP_CONFIG=/home/<USER>/srbuild/conf/production.conf
JAR_PATH=/home/<USER>/srbuild/coldemail.jar

printf "free -h \n"

free -h


printf "\nps -ewf | grep java \n\n"

ps -ewf | grep java


printf "\nkillall -9 java \n"

killall -9 java


printf "\nstart worker \n"

nohup java -Dconfig.file=$APP_CONFIG -Dlogger.file=$LOG_CONFIG -cp $JAR_PATH utils.App worker > nohup.out &

printf "\nstart send_worker \n"

nohup java -Dconfig.file=$APP_CONFIG -Dlogger.file=$LOG_CONFIG -cp $JAR_PATH utils.App send_worker > nohup.out &


printf "\nstart reply_tracker \n"

nohup java -Dconfig.file=$APP_CONFIG -Dlogger.file=$LOG_CONFIG -cp $JAR_PATH utils.App reply_tracker > nohup.out &


printf "\nstart email_scheduler_worker \n"

nohup java -Dconfig.file=$APP_CONFIG -Dlogger.file=$LOG_CONFIG -cp $JAR_PATH utils.App email_scheduler_worker > nohup.out &


printf "\nstart scheduler \n"

nohup java -Dconfig.file=$APP_CONFIG -Dlogger.file=$LOG_CONFIG -cp $JAR_PATH utils.App scheduler > nohup.out &


printf "\nstart api \n"

nohup java -Dconfig.file=$APP_CONFIG -Dpidfile.path=/dev/null -Dlogger.file=$LOG_CONFIG -Dhttp.port=5000 -jar $JAR_PATH > nohup.out &

printf "\nps -ewf | grep java \n\n"

ps -ewf | grep java
