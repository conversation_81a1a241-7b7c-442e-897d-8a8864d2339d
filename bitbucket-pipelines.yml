image: bitbucketpipelines/scala-sbt:scala-2.12

pipelines:
  branches:
    develop:
      - step:
          caches:
            - maven
            - pip
            - ivy2
          script:
            - sbt sbt-version
            - apt-get install zip -y
            - sbt -J-Xms2048m -J-Xmx2048m assembly
            - sh create_package.sh
            - python --version
            - curl "https://bootstrap.pypa.io/get-pip.py" -o "get-pip.py"
            - python get-pip.py
            #    - apt-get install python-pip python-dev build-essential -y
            - pip install --upgrade pip
            - pip --version
            - pip install awsebcli --upgrade
            - eb --version
            - mkdir ~/.aws/
            - touch ~/.aws/credentials
            - printf "[eb-cli]\naws_access_key_id = %s\naws_secret_access_key = %s\n" "$AWS_ACCESS_KEY_ID" "$AWS_SECRET_ACCESS_KEY" >> ~/.aws/credentials
            - touch ~/.aws/config
            - printf "[profile eb-cli]\nregion=us-east-1\noutput=json" >> ~/.aws/config
            - eb deploy coldemail-develop-env --timeout 20 --verbose

    srteam:
      - step:
          script:
            - sbt sbt-version
            - apt-get install zip -y
            - sbt clean  -J-Xms2048m -J-Xmx2048m assembly
            - sh create_package.sh
            - python --version
            - curl "https://bootstrap.pypa.io/get-pip.py" -o "get-pip.py"
            - python get-pip.py
            #    - apt-get install python-pip python-dev build-essential -y
            - pip install --upgrade pip
            - pip --version
            - pip install awsebcli --upgrade
            - eb --version
            - mkdir ~/.aws/
            - touch ~/.aws/credentials
            - printf "[eb-cli]\naws_access_key_id = %s\naws_secret_access_key = %s\n" "$AWS_ACCESS_KEY_ID" "$AWS_SECRET_ACCESS_KEY" >> ~/.aws/credentials
            - touch ~/.aws/config
            - printf "[profile eb-cli]\nregion=us-east-1\noutput=json" >> ~/.aws/config
            - eb deploy srteam --timeout 20 --verbose
definitions:
  caches:
    ivy2: ~/.ivy2/cache
