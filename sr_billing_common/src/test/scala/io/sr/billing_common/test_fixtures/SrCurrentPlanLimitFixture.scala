package io.sr.billing_common.test_fixtures

import io.sr.billing_common.models.{PlanID, SrCurrentPlanLimit, SrLimitsForBilling}

object SrCurrentPlanLimitFixture {

  val limits: SrLimitsForBilling = SrLimitsForBilling(
    max_prospect_limit_org = 200,
    current_prospect_sent_count_org = 132,
    total_sending_email_accounts = 10,
    current_sending_email_accounts = 4,
    max_li_automation_seats = 0,
    current_li_automation_seats = 0,
    max_client_teams = 1,
    current_client_teams = 1,
    max_prospects_saved = 100000,
    current_prospects_saved = 345,
    max_crm_integrations = 1,
    current_crm_integrations = 1,
    max_calling_seats = 1,
    current_calling_accounts = 1,
    max_spam_tests = 0,

    max_purchased_domains = 0,
    current_purchased_domains = 0,

    max_purchased_email_accounts = 0,
    current_purchased_email_accounts = 0,

    max_purchased_zapmail_domains = 0,
    current_purchased_zapmail_domains = 0,

    max_purchased_zapmail_email_accounts = 0,
    current_purchased_zapmail_email_accounts = 0,

    current_active_team_inboxes = 4,

    current_spam_tests_done_count = 0
  )

  val srCurrentPlanLimit_default: SrCurrentPlanLimit = SrCurrentPlanLimit(
    plan_id = PlanID.V4_199,
    plan_text_id = "some-v4-199-text-id",
    plan_display_name = "some-v4-199-text-id",
    limits = limits
  )

}
