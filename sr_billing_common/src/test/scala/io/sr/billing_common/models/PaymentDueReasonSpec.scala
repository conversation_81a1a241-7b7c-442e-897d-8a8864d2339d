package io.sr.billing_common.models

import org.scalamock.scalatest.MockFactory
import org.scalatest.funspec.AnyFunSpec

class PaymentDueReasonSpec extends AnyFunSpec with MockFactory {

  private val invoicePaymentFailed = "invoice_payment_failed"

  private val otherReason = "other"

  describe("Test PaymentDueReason object toString") {

    it("should return valid string when PaymentDueReason.OtherReason object toString is called.") {

      assert(PaymentDueReason.OtherReason.toString == otherReason)

    }

    it("should return valid string when PaymentDueReason.InvoicePaymentFailed object toString is called.") {

      assert(PaymentDueReason.InvoicePaymentFailed.toString == invoicePaymentFailed)

    }

  }

}
