package io.sr.billing_common.services

import io.sr.billing_common.models.{PlanID, SrPlanName}



object PaymentGatewayAPI {

  def totalSendingEmailAccounts(
    plan_text_id: String,
    base_licence_count: Int,
    additional_licence_count: Int
  ): Int = {
    
    val isV2BusinessPlan = PlanID.isV2BusinessPlan(
      planName = SrPlanName(name = plan_text_id),
    )
    
    if (isV2BusinessPlan) {

      5 * base_licence_count + additional_licence_count

    } else {
      
      base_licence_count
    }
    
  }


}
