package io.sr.billing_common.models

import io.sr.billing_common.BillingAppConfig
import org.joda.time.DateTime
import play.api.libs.json.{<PERSON>s<PERSON><PERSON><PERSON>, <PERSON>sSuc<PERSON>, <PERSON>s<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>s, Writes}
import play.api.libs.json.JodaWrites._
import play.api.libs.json.JodaReads._

import scala.util.{Failure, Success, Try}

sealed trait BillingSubscriptionStatus

object BillingSubscriptionStatus {

  private val active = "active"
  private val paymentDue = "payment_due"
  private val cancelled = "cancelled"
  private val scheduledForCancellation = "scheduled_for_cancellation"

  private val daysAfterCampaignPauses = BillingAppConfig.daysAfterPaymentDueCampaignPauses

  case class Active(
    next_payment_on: DateTime,
  ) extends BillingSubscriptionStatus {
    override def toString: String = active
  }

  object Active {

    override def toString: String = active

    implicit def writes: Writes[Active] = new Writes[Active] {
      def writes(data: Active): JsValue = {
        Json.obj(
          """status_type""" -> data.toString,
          "next_payment_on" -> data.next_payment_on
        )
      }
    }
  }

  case class PaymentDue(
    payment_due_since: DateTime,
    payment_due_reason: PaymentDueReason,
    payment_due_make_payment_link: Option[String]
  ) extends BillingSubscriptionStatus {
    override def toString: String = paymentDue

    def payment_due_campaign_pause_at: DateTime = payment_due_since.plusDays(daysAfterCampaignPauses)
  }

  object PaymentDue {

    override def toString: String = paymentDue

    implicit def writes: Writes[PaymentDue] = new Writes[PaymentDue] {
      def writes(data: PaymentDue): JsValue = {
        Json.obj(
          """status_type""" -> data.toString,
          "payment_due_since" -> data.payment_due_since,
          "payment_due_reason" -> data.payment_due_reason.toString,
          "payment_due_make_payment_link" -> data.payment_due_make_payment_link
        )
      }
    }
  }

  case class Cancelled(
    canceled_at: DateTime,
    canceled_automatically: Option[Boolean], // CHECKME
    cancellation_reason: Option[String]
  ) extends BillingSubscriptionStatus {
    override def toString: String = cancelled

  }

  object Cancelled {

    override def toString: String = cancelled

    implicit def writes: Writes[Cancelled] = new Writes[Cancelled] {
      def writes(data: Cancelled): JsValue = {
        Json.obj(
          """status_type""" -> data.toString,
          "canceled_at" -> data.canceled_at,
          "canceled_automatically" -> data.canceled_automatically,
          "cancellation_reason" -> data.cancellation_reason
        )
      }
    }
  }

  case class ScheduledForCancellation(
    scheduled_for_cancellation_at: DateTime,
    cancellation_reason: CancellationReason
  ) extends BillingSubscriptionStatus {
    override def toString: String = scheduledForCancellation
  }

  object ScheduledForCancellation {

    override def toString: String = scheduledForCancellation

    implicit def writes: Writes[ScheduledForCancellation] = new Writes[ScheduledForCancellation] {
      def writes(data: ScheduledForCancellation): JsValue = {
        Json.obj(
          """status_type""" -> data.toString,
          "scheduled_for_cancellation_at" -> data.scheduled_for_cancellation_at,
          "cancellation_reason" -> data.cancellation_reason.cancellation_reason,
          "cancellation_reason_description" -> data.cancellation_reason.cancellation_reason_description,
          "next_tool_after_cancellation" -> data.cancellation_reason.next_tool_after_cancellation,
          "additional_cancellation_suggestions" -> data.cancellation_reason.additional_cancellation_suggestions
        )
      }
    }
  }

  implicit def writes: Writes[BillingSubscriptionStatus] = new Writes[BillingSubscriptionStatus] {
    def writes(ev: BillingSubscriptionStatus): JsValue = {

      ev match {
        case data: Active => Json.toJson(data)

        case data: PaymentDue => Json.toJson(data)

        case data: Cancelled => Json.toJson(data)

        case data: ScheduledForCancellation => Json.toJson(data)

      }

    }
  }

  //  def fromString(key: String): Try[Object] = Try {
  //    key match {
  //      case active => Active
  //      case paymentDue => PaymentDue
  //      case cancelled => Cancelled
  //      case scheduledForCancellation => ScheduledForCancellation
  //    }
  //  }


  implicit def reads: Reads[BillingSubscriptionStatus] = (ev: JsValue) => Try {

    (ev \ "status_type").as[String] match {

      case `active` =>

        JsSuccess(
          Active(
            next_payment_on = (ev \ "next_payment_on").as[DateTime]
          )
        )

      case `paymentDue` =>

        JsSuccess(

          PaymentDue(
            payment_due_since = (ev \ "payment_due_since").as[DateTime],
            payment_due_reason = (ev \ "payment_due_reason").as[PaymentDueReason],
            payment_due_make_payment_link = (ev \ "payment_due_make_payment_link").asOpt[String]
          )

        )

      case `cancelled` =>

        JsSuccess(

          Cancelled(
            canceled_at = (ev \ "canceled_at").as[DateTime],
            canceled_automatically = (ev \ "canceled_automatically").asOpt[Boolean], // CHECKME
            cancellation_reason = (ev \ "cancellation_reason").asOpt[String]
          )

        )

      case `scheduledForCancellation` =>

        JsSuccess(

          ScheduledForCancellation(
            scheduled_for_cancellation_at = (ev \ "scheduled_for_cancellation_at").as[DateTime],
            cancellation_reason = CancellationReason(
              cancellation_reason = (ev \ "cancellation_reason").as[String],
              cancellation_reason_description = (ev \ "cancellation_reason_description").as[String],
              next_tool_after_cancellation = (ev \ "next_tool_after_cancellation").asOpt[String],
              additional_cancellation_suggestions = (ev \ "additional_cancellation_suggestions").asOpt[String]
            )
          )

        )

    }

  } match {

    case Failure(e) => JsError(e.toString)

    case Success(v) => v

  }

}
