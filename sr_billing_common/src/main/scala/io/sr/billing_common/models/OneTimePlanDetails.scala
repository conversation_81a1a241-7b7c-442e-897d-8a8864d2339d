package io.sr.billing_common.models

import play.api.libs.json.{Json, OWrites, Reads}

case class OneTimePlanDetails(
  plan_type: OneTimePlanType,
  org_id: ProductOrgId,
  amount: Long,
  currency: SrBillingCurrencyType,
  product_id: Int
)

object OneTimePlanDetails {

  implicit val writes: OWrites[OneTimePlanDetails] = Json.writes[OneTimePlanDetails]

  implicit val reads: Reads[OneTimePlanDetails] = Json.reads[OneTimePlanDetails]

}
