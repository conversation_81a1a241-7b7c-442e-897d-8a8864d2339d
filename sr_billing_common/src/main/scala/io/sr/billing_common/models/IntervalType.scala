package io.sr.billing_common.models

import play.api.libs.json.{JsError, JsString, JsSuccess, JsValue, Reads, Writes}

import scala.util.{Failure, Success, Try}

sealed trait IntervalType

object IntervalType {

  private val month = "month"
  private val year = "year"

  case object Month extends IntervalType {
    override def toString: String = month
  }

  case object Year extends IntervalType {
    override def toString: String = year
  }

  def fromString(key: String): Try[IntervalType] = Try {
    key match {
      case `month` => Month
      case `year` => Year
    }
  }

  implicit def writes: Writes[IntervalType] = new Writes[IntervalType] {
    def writes(i: IntervalType): JsString = {
      JsString(i.toString)
    }
  }

  implicit def reads: Reads[IntervalType] = (json: JsValue) => {

    fromString(key = json.as[String]) match {

      case Failure(e) => JsError(e.toString)

      case Success(value) => JsSuccess(value)

    }

  }

}
