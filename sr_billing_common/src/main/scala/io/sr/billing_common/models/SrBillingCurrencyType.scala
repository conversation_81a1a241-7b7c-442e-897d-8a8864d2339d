package io.sr.billing_common.models

import play.api.libs.json._

import scala.util.{Failure, Success, Try}

// Note: Copy of CurrencyType from common2

sealed trait SrBillingCurrencyType

object SrBillingCurrencyType {

  private val usd = "usd"
  private val inr = "inr"
  private val eur = "eur"
  private val gbp = "gbp"
  private val aud = "aud"
  private val pln = "pln"

  case object USD extends SrBillingCurrencyType {
    override def toString: String = usd
  }

  case object INR extends SrBillingCurrencyType {
    override def toString: String = inr
  }

  case object EUR extends SrBillingCurrencyType {
    override def toString: String = eur
  }

  case object GBP extends SrBillingCurrencyType {
    override def toString: String = gbp
  }

  case object AUD extends SrBillingCurrencyType {
    override def toString: String = aud
  }


  case object PLN extends SrBillingCurrencyType {
    override def toString: String = pln
  }


  def fromKey(key: String): Try[SrBillingCurrencyType] = Try {
    key match {
      case `usd` => USD
      case `inr` => INR
      case `eur` => EUR
      case `gbp` => GBP
      case `aud` => AUD
      case `pln` => PLN
    }
  }

  implicit def format: Format[SrBillingCurrencyType] = new Format[SrBillingCurrencyType] {

    override def reads(json: JsValue): JsResult[SrBillingCurrencyType] = {
      fromKey(json.as[String]) match {
        case Failure(e) => JsError(e.toString)
        case Success(currency) => JsSuccess(currency)
      }
    }

    override def writes(o: SrBillingCurrencyType): JsValue = {
      JsString(o.toString)
    }

  }

}
