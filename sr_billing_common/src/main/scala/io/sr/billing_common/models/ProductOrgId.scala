package io.sr.billing_common.models

import play.api.libs.json.{Format, JsError, JsResult, JsString, JsSuccess, JsValue}

case class ProductOrgId(id: String) extends AnyVal {
  override def toString: String = id

}

object ProductOrgId {

  implicit val format: Format[ProductOrgId] = new Format[ProductOrgId] {

    override def reads(ev: JsValue): JsResult[ProductOrgId] = {
      ev match {
        case JsString(id) => JsSuccess(ProductOrgId(id = id))
        case randomValue => JsError(s"expected string, got some random value - $randomValue")
      }
    }

    override def writes(o: ProductOrgId): JsValue = JsString(o.id)

  }

}
