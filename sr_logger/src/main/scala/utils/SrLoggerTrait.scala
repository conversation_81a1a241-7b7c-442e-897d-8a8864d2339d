package utils

import scala.util.Random

/**
 * 16-Apr-2024: extend this trait in application code to create new sms alert types
 *  and define those in cloud monitoring using the keyword
 *
 * we have defined two generic sms alerts CRITICAL_ISSUE and SHOULD_NEVER_HAPPEN below as examples.
 * Ideally we should create specific alert keywords so as to not hit daily limits for each alert type in Cloud Monitoring
 */
trait SrSmsAlert {

  // create a Cloud monitoring notification using this keyword
  val smsAlertKeyword: String

}

trait SrLoggerTrait {


  /**
   * Used to generate a default trace-id, unless the user provides a custom trace-id
   */
  final def genDefaultLogTraceId(): String = {
    s"req_${Random.alphanumeric.take(15).mkString}"
  }


  /**
   * 25-Apr-2024: user can override this if they want to pass a custom log trace id,
   * else we auto-generate a trace-id below
   */
  val customLogTraceId: Option[String] = None


  /**
   * 25-Apr-2024: main trace id, auto generated when a new SrLogger object is initialized
   *
   * This ensures the trace id is always defined.
   */
  final lazy val logTraceId: String = customLogTraceId
    .getOrElse(
      genDefaultLogTraceId()
    )


  private case object CRITICAL_ISSUE extends SrSmsAlert {

    override val smsAlertKeyword: String = "CRITICAL_ISSUE"

  }

  private case object SHOULD_NEVER_HAPPEN extends SrSmsAlert {

    override val smsAlertKeyword: String = "SHOULD_NEVER_HAPPEN"

  }

  def info(
    msg: String,
    logInStaging: Boolean = false
  ): Unit

  def debug(
    msg: String,
    logInStaging: Boolean = false
  ): Unit

  def success(
    msg: String,
    logInStaging: Boolean = false
  ): Unit

  def warn(msg: String): Unit

  def error(msg: String): Unit

  def error(msg: String, err: Throwable): Unit

  def fatal(msg: String): Unit

  def fatal(msg: String, request: play.api.libs.ws.WSResponse): Unit

  def fatal(msg: String, err: Throwable): Unit

  def fatal(msg: String, request: play.api.libs.ws.WSResponse, err: Throwable): Unit

  final def logAndSendSmsAlert(

    smsAlert: SrSmsAlert,
    msg: String,
    err: Option[Throwable] = None

  ): Unit = {

    val keyword = smsAlert.smsAlertKeyword

    if (err.isEmpty) {

      fatal(msg = s"$keyword :: $msg")

    } else {

      fatal(msg = s"$keyword :: $msg",  err = err.get)

    }


  }

  /**
   * 16-Apr-2024: sms alert
   *
   * Usage: Setup a Cloud Monitoring SMS alert on "SHOULD_NEVER_HAPPEN" in gcp
   *
   * This is used to log impossible scenarios in the application.
   *
   * This should be used rarely. For commonly scenarios where too many alerts are coming, please setup dedicated
   *  Cloud Monitoring alerts using `logAndSendSmsAlert` fn above,
   *  because of the daily max alert of specific type limits set there.
   */
  final def shouldNeverHappen(

    msg: String, err: Option[Throwable] = None

  ): Unit = {

    logAndSendSmsAlert(

      smsAlert = SHOULD_NEVER_HAPPEN,

      msg = msg,
      err = err,
    )


  }

  /**
   * 16-Apr-2024: sms alert
   *
   * Usage: Setup a Cloud Monitoring SMS alert on "CRITICAL_ISSUE" in gcp
   *
   * This is used to log critical issues that should not happen in the application.
   *
   * This should be used rarely. For commonly scenarios where too many alerts are coming, please setup dedicated
   *  Cloud Monitoring alerts using `logAndSendSmsAlert` fn above,
   *  because of the daily max alert of specific type limits set there.
   */
  final def criticalSmsAlert(

    msg: String, err: Option[Throwable] = None

  ): Unit = {


    logAndSendSmsAlert(

      smsAlert = CRITICAL_ISSUE,

      msg = msg,
      err = err,
    )


  }

  // append additional info to logRequestId and return new instance of SRLogger
  def appendLogRequestId(appendLogReqId: String): SrLoggerTrait
}
