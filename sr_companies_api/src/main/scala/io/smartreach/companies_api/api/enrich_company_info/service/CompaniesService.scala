package io.smartreach.companies_api.api.enrich_company_info.service

import io.smartreach.companies_api.api.enrich_company_info.dao.{EnrichedCompanyData, TheCompaniesApi}
import play.api.libs.ws.WSClient
import utils.ISRLogger

import scala.concurrent.{ExecutionContext, Future}

class CompaniesService(
  theCompaniesApi: TheCompaniesApi
) {

  def getEnrichedCompanyData(
    companyDomain: String,
    apiKey: String
  )(
    implicit ws: WSClient,
    ec: ExecutionContext,
    logger: ISRLogger
  ): Future[EnrichedCompanyData] = {

    theCompaniesApi.getEnrichedCompanyData(
      companyDomain = companyDomain,
      apiKey = apiKey
    )

  }

}
