package io.smartreach.companies_api.api.enrich_company_info.models

import play.api.libs.json.{JsError, JsString, JsSuccess, JsValue, Reads, Writes}

import scala.util.{Failure, Success, Try}


sealed trait TotalEmployees

object TotalEmployees {

  private val oneToTen = "1-10"
  private val tenToFifty = "10-50"
  private val fiftyToTwoHundred = "50-200"
  private val twoHundredToFiveHundred = "200-500"
  private val fiveHundredToOneK = "500-1k"
  private val oneToFiveK = "1k-5k"
  private val fiveToTenK = "5k-10k"
  private val overTenK = "over-10k"


  case object OneToTen extends TotalEmployees {
    override def toString: String = oneToTen
  }

  case object TenToFifty extends TotalEmployees {
    override def toString: String = tenToFifty
  }

  case object FiftyToTwoHundred extends TotalEmployees {
    override def toString: String = fiftyToTwoHundred
  }

  case object TwoHundredToFiveHundred extends TotalEmployees {
    override def toString: String = twoHundredToFiveHundred
  }

  case object FiveHundredToOneK extends TotalEmployees {
    override def toString: String = fiveHundredToOneK
  }

  case object OneToFiveK extends TotalEmployees {
    override def toString: String = oneToFiveK
  }

  case object FiveToTenK extends TotalEmployees {
    override def toString: String = fiveToTenK
  }

  case object OverTenK extends TotalEmployees {
    override def toString: String = overTenK
  }

  def fromKey(key: String): Try[TotalEmployees] = Try {

    key match {
      case `oneToTen` => OneToTen
      case `tenToFifty` => TenToFifty
      case `fiftyToTwoHundred` => FiftyToTwoHundred
      case `twoHundredToFiveHundred` => TwoHundredToFiveHundred
      case `fiveHundredToOneK` => FiveHundredToOneK
      case `oneToFiveK` => OneToFiveK
      case `fiveToTenK` => FiveToTenK
      case `overTenK` => OverTenK
    }
  }

  implicit def writes: Writes[TotalEmployees] = (t: TotalEmployees) => {
    JsString(t.toString)
  }

  implicit def reads: Reads[TotalEmployees] = (json: JsValue) => {

    fromKey(key = json.as[String]) match {

      case Success(totalEmployees: TotalEmployees) => JsSuccess(totalEmployees)

      case Failure(e) => JsError(s"Invalid TotalEmployees value error = $e")
    }
  }
}
