package io.smartreach.esp.utils.email

import org.apache.pekko.actor.ActorSystem
import io.smartreach.esp.api.EmailConstants.EMAIL_API_ERROR_KEYS
import io.smartreach.esp.api.emails.{EmailInfo, EmailSentResponse, EmailSettingId, IEmailAddress, InternetMessageId, SREmailToBeSent}
import io.smartreach.esp.api.microsoftOAuth.{MicrosoftInvalidGrantException, MicrosoftOAuthApi, MicrosoftOAuthSettings, OutlookUtilsTrait}
import io.smartreach.esp.utils.FutureUtils
import io.smartreach.esp.utils.email.models.EmailReply
import play.api.libs.json.{JsObject, JsValue, Json}
import play.api.libs.ws.{WSClient, WSResponse}
import utils.ISRLogger

import java.io.ByteArrayOutputStream
import java.util.{Base64, Properties}
import javax.mail.{Message, Session}
import javax.mail.internet.{InternetAddress, MimeBodyPart, MimeMessage, MimeMultipart}
import scala.concurrent.duration.DurationInt
import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}
import play.api.libs.ws.WSBodyWritables.{writeableOf_JsValue, writeableOf_String}

case class OutlookEmailSendDetails(
                                    from_email: String,
                                    oauth2_refresh_token: Option[String],
                                    sender_email_setting_id: EmailSettingId,
                                    in_reply_to_outlook_msg_id: Option[String]
                                  )


object OutlookSendEmailService {
  def toJsonEmailAddress(emails: Seq[IEmailAddress]): Seq[JsObject] = {
    emails.map(
      em => {
        val name = em.name.getOrElse("")
        Json.obj(
          "emailAddress" -> Json.obj(
            "address" -> em.email,
            "name" -> name
          )
        )
      }
    )
  }
}

trait OutlookSendEmailService extends MicrosoftOAuthApi with OutlookSenderTrait {

  def sendEmailViaOutlookApi(
                              base_64_flow: Boolean,
                              outlookEmailSendDetails: OutlookEmailSendDetails,
                              emailToBeSent: SREmailToBeSent,
                              microsoftOAuthSettings: MicrosoftOAuthSettings,
                              send_plain_text_email: Option[Boolean]
                            )(
                              implicit ws: WSClient,
                              ec: ExecutionContext,
                              Logger: ISRLogger,
                              system: ActorSystem
                            ): Future[EmailSentResponse.MSSendEmailRes] = {

    outlookEmailSendDetails.oauth2_refresh_token match {


      // FIXME: instead of error tell the user no refresh token
      case None => Future.failed(OutlookJustLogException(s"[OutlookApiService]: No refresh token"))

      case Some(refreshToken) =>

        refreshAccessToken(
          email = outlookEmailSendDetails.from_email,
          emailSettingId = outlookEmailSendDetails.sender_email_setting_id,
          refreshToken = refreshToken,
          s = microsoftOAuthSettings
        ).flatMap(
          res => {
            sendEmailByCreatingMessage(
              accessToken = res.access_token,
              base_64_flow = base_64_flow,
              from_mail =  outlookEmailSendDetails.from_email,
              emailToBeSent = EmailInfo(
                to_emails = emailToBeSent.to_emails ,
                from_email = emailToBeSent.from_email ,
                cc_emails = emailToBeSent.cc_emails ,
                bcc_emails = emailToBeSent.bcc_emails ,
                from_name = emailToBeSent.from_name ,
                reply_to_email = emailToBeSent.reply_to_email ,
                reply_to_name = emailToBeSent.reply_to_name ,
                subject = emailToBeSent.subject ,
                text_body = emailToBeSent.textBody ,
                html_body = emailToBeSent.htmlBody ,
                message_id = emailToBeSent.message_id ,
                references_header = emailToBeSent.references_header,
                in_reply_to_id = outlookEmailSendDetails.in_reply_to_outlook_msg_id,
//                in_reply_to_sent_at = None,
                in_reply_to_sent_at = None,
                list_unsubscribe_header = None,
                gmail_fbl = None
              ),
              send_plain_text_email = send_plain_text_email
            )

          })
    }
  }


}


trait OutlookSenderTrait extends OutlookUtilsTrait {

  def getDraftMessage(
                       accessToken: String,
                       internetMessageId: String,
                       Logger: ISRLogger
                     )(implicit ws: WSClient, ec: ExecutionContext): Future[EmailReply.OutlookReplyTrackedViaAPI] = {

    val url = s"https://graph.microsoft.com/v1.0/me/messages/${internetMessageId}"

    ws.url(url)
      .addHttpHeaders(
        "Authorization" -> accessToken,
        "Content-Type" -> "application/json"
      )
      .get()
      .flatMap { response =>

        if (response.status == 504) {

          Logger.error(s"OutlookApiService getDraftMessage GATEWAY_TIMEOUT error: $response ::: ${response.body}")
          Future.failed(OutlookJustLogException(s"OutlookApiService getDraftMessage ${EMAIL_API_ERROR_KEYS.GATEWAY_TIMEOUT}"))
        } else if (response.status == 503) {

          Logger.error(s"OutlookApiService getDraftMessage OUTLOOK_SERVICE_UNAVAILABLE error: $response ::: ${response.body}")
          Future.failed(OutlookJustLogException(s"OutlookApiService getDraftMessage ${EMAIL_API_ERROR_KEYS.OUTLOOK_SERVICE_UNAVAILABLE}"))
        } else if (response.status == 401) {

          Logger.error(s"OutlookApiService getDraftMessage INVALID_OUTLOOK_ACCESS_TOKEN error: $response ::: ${response.body}")
          Future.failed(OutlookJustLogException(s"OutlookApiService getDraftMessage ${EMAIL_API_ERROR_KEYS.INVALID_OUTLOOK_ACCESS_TOKEN}"))
        }
        else if (response.status != 200) {

          Logger.error(s"OutlookApiService getDraftMessage error: ${response.status} ::: ${response.body}")
          Future.failed(OutlookJustLogException(s"OutlookApiService getDraftMessage error: $accessToken"))
        } else {
          Future.successful(parseOutlookMessage(
            m = response.json,
            Logger = Logger
          ))
        }
      }
  }


  private def createMimeMessageAndSendToURL(
                                             url: String,
                                             accessToken: String,
                                             emailToBeSent: EmailInfo,
                                             send_plain_text_email: Option[Boolean],
                                             Logger: ISRLogger
                                           )(implicit ws: WSClient, ec: ExecutionContext): Future[WSResponse] = {

    createMimeMessage(
      emailToBeSent = emailToBeSent,
      sendPlainText = send_plain_text_email.getOrElse(false)
    ) match {
      case Failure(err) =>

        Logger.shouldNeverHappen(s"_createMsg OutlookSendEmailService :: email_to_be_sent: ${emailToBeSent} send_plain_text_email : ${send_plain_text_email} ", Some(err))

        Future.failed(err)

      case Success(payloadBase64Encoded) =>
        ws.url(url)
          .addHttpHeaders(
            "Authorization" -> accessToken,
            "Content-Type" -> "text/plain",
            "Prefer" -> "IdType=ImmutableId",
          )
          .post(payloadBase64Encoded)
    }

  }


  // Convert MimeMessage to Base64 encoded string
  def mimeMessageToBase64(message: MimeMessage): String = {

    val outputStream = new ByteArrayOutputStream()
    message.writeTo(outputStream)
    val bytes = outputStream.toByteArray
    Base64.getEncoder.encodeToString(bytes)

  }


  // Create the draft email using MimeMessage
  def createMimeMessage(
                         emailToBeSent: EmailInfo,
                         sendPlainText: Boolean
                       ): Try[String] = Try {

    val props: Properties = new Properties()
    val session: Session = Session.getInstance(props, null)
    val message = new MimeMessage(session)


    message.setFrom(new InternetAddress(emailToBeSent.from_email, emailToBeSent.from_name))

    // Set recipients
    emailToBeSent.to_emails.foreach { recipient =>
      message.addRecipient(Message.RecipientType.TO, new InternetAddress(recipient.email))
    }

    // Set the subject
    message.setSubject(emailToBeSent.subject)

    // Create a MimeBodyPart for the message content
    val bodyPart = new MimeBodyPart()

    // Set the content based on plain text or HTML and explicitly set base64 encoding
    if (sendPlainText) {

      bodyPart.setText(emailToBeSent.text_body, "UTF-8")

    } else {

      bodyPart.setContent(emailToBeSent.html_body, "text/html; charset=UTF-8")

    }

    // Explicitly set the encoding to base64
    bodyPart.setHeader("Content-Transfer-Encoding", "base64")

    // Create a MimeMultipart to hold the body part
    val multipart = new MimeMultipart()
    multipart.addBodyPart(bodyPart)

    // Set the multipart content to the message
    message.setContent(multipart)

    mimeMessageToBase64(message)

  }


  def getBody(
               emailToBeSent: EmailInfo,
               send_plain_text_email: Option[Boolean] = Some(false)
             ): JsObject = {

    val msgBody = emailToBeSent.html_body
    // val toName = emailToBeSent.to_name.getOrElse("")

    val toRecipients: Seq[JsObject] = OutlookSendEmailService.toJsonEmailAddress(emails = emailToBeSent.to_emails)

    var body = Json.obj()
    if (send_plain_text_email.isDefined && send_plain_text_email.get) {

      body = Json.obj(

        "subject" -> emailToBeSent.subject,

        "body" -> Json.obj(
          "contentType" -> "TEXT",
          "content" -> emailToBeSent.text_body
        ),

        "from" -> Json.obj(
          "emailAddress" -> Json.obj(
            "address" -> emailToBeSent.from_email,
            "name" -> emailToBeSent.from_name
          )
        ),

        "toRecipients" -> Json.toJson(toRecipients)

        /*
    "toRecipients" -> Json.arr(
      Json.obj(
        "emailAddress" -> Json.obj(
          "address" -> emailToBeSent.to_email,
          "name" -> toName)
      )
    )
    */
      )


    } else {

      body = Json.obj(

        "subject" -> emailToBeSent.subject,

        "body" -> Json.obj(
          "contentType" -> "HTML",
          "content" -> msgBody
        ),

        "from" -> Json.obj(
          "emailAddress" -> Json.obj(
            "address" -> emailToBeSent.from_email,
            "name" -> emailToBeSent.from_name
          )
        ),

        "toRecipients" -> Json.toJson(toRecipients)

        /*
    "toRecipients" -> Json.arr(
      Json.obj(
        "emailAddress" -> Json.obj(
          "address" -> emailToBeSent.to_email,
          "name" -> toName)
      )
    )
    */
      )

    }


    //add cc emails
    if (emailToBeSent.cc_emails.nonEmpty) {

      val toCCEmails: Seq[JsObject] = OutlookSendEmailService.toJsonEmailAddress(emails = emailToBeSent.cc_emails)
      body = body ++ Json.obj("ccRecipients" -> Json.toJson(toCCEmails))

    }


    //add bcc emails
    if (emailToBeSent.bcc_emails.nonEmpty) {

      val toBCCEmails: Seq[JsObject] = OutlookSendEmailService.toJsonEmailAddress(emails = emailToBeSent.bcc_emails)
      body = body ++ Json.obj("bccRecipients" -> Json.toJson(toBCCEmails))

    }

    //add ReplyTo Email and Name
    if (emailToBeSent.reply_to_email.isDefined && emailToBeSent.reply_to_email.get.trim.nonEmpty && emailToBeSent.from_email.trim.toLowerCase != emailToBeSent.reply_to_email.get.trim.toLowerCase) {
      val replyTo = Json.arr(
        Json.obj(
          "emailAddress" -> Json.obj(
            "address" -> emailToBeSent.reply_to_email.get,
            "name" -> emailToBeSent.reply_to_name.get
          )
        )
      )

      body = body ++ Json.obj("replyTo" -> replyTo)
    }
    body
  }


  def getBodyAndSendEmailNewAndOldFlow(
                                        url: String,
                                        accessToken: String,
                                        base_64_flow: Boolean,
                                        emailToBeSent: EmailInfo,
                                        send_plain_text_email: Option[Boolean],
                                        Logger: ISRLogger
                                      )(implicit ws: WSClient, ec: ExecutionContext): Future[WSResponse] = {

    if (!base_64_flow) {
      val body = getBody(
        emailToBeSent = emailToBeSent,
        send_plain_text_email = send_plain_text_email
      )

      ws.url(url)
        .addHttpHeaders(
          "Authorization" -> accessToken,
          "Prefer" -> "IdType=ImmutableId",
        )
        .post(body)

    } else {

      createMimeMessageAndSendToURL(

        url = url,
        accessToken = accessToken,
        emailToBeSent = emailToBeSent,
        send_plain_text_email = send_plain_text_email,
        Logger = Logger

      )

    }

  }


  def _createMsg(
                  base_64_flow: Boolean,
                  createUrl: String,
                  newMsgCreateUrl: String,
                  accessToken: String,
                  from_mail: String,
                  emailToBeSent: EmailInfo,
                  send_plain_text_email: Option[Boolean],
                  Logger: ISRLogger
                )(implicit ws: WSClient, ec: ExecutionContext): Future[WSResponse] = {

    getBodyAndSendEmailNewAndOldFlow(
      url = createUrl,
      accessToken = accessToken,
      base_64_flow = base_64_flow,
      emailToBeSent = emailToBeSent,
      send_plain_text_email = send_plain_text_email,
      Logger = Logger
    )
      .flatMap(
        replyMsgResp => {

          if (replyMsgResp.status != 201) {

//            Try {
//              Logger.debug(s"_createMsg: replyMsgResp.body: ${replyMsgResp.body} and status: ${replyMsgResp.status}")
//
//              Logger.debug(s"_createMsg: headers: ${replyMsgResp.headers} replyMsgResp.json: ${replyMsgResp.body}")
//            } match {
//              case Success(_) => //DO NOTHING
//              case Failure(exception) => Logger.error(s"Failed to log", exception)
//            }
            val error = (replyMsgResp.json \ "error" \ "code").asOpt[String].getOrElse("")
            val cantBeReplyError = error == "ErrorInvalidOperation" || error == "ErrorItemNotFound"
            val accessDeniedError = error == "ErrorAccessDenied"

            if (!cantBeReplyError) {

              Logger.error(s"FATAL OutlookApiService CreateMessageResponse replyMsgResp cantBeReplyError: $cantBeReplyError :: error: $replyMsgResp ::: ${replyMsgResp.body} :: $createUrl :: $from_mail")

              if (accessDeniedError) {

                throw MicrosoftInvalidGrantException(s"There was an error while OutlookApiService CreateMessageResponse Error Code: ${error}")

              } else {

                throw new Exception(s"There was an error while OutlookApiService CreateMessageResponse Error Code: ${error}")

              }

            } else {

              Logger.error(s"FATAL OutlookApiService CreateMessageResponse replyMsgResp cantBeReplyError: $cantBeReplyError  :: TRYING NEXT :: error: $replyMsgResp ::: ${replyMsgResp.body} :: $createUrl :: $from_mail")

              getBodyAndSendEmailNewAndOldFlow(
                url = newMsgCreateUrl,
                accessToken = accessToken,
                base_64_flow = base_64_flow,
                emailToBeSent = emailToBeSent,
                send_plain_text_email = send_plain_text_email,
                Logger = Logger
              )
                .map(
                  newMsgResp => {
                    if (newMsgResp.status != 201) {

                      val errorCode = (newMsgResp.json \ "error" \ "code").asOpt[String].getOrElse("")

                      Logger.error(s"FATAL OutlookApiService CreateMessageResponse newMsgResp error: $newMsgResp ::: ${newMsgResp.body} :: $newMsgCreateUrl :: $from_mail")

                      if (errorCode == "ErrorAccessDenied") {

                        throw MicrosoftInvalidGrantException(s"There was an error while OutlookApiService CreateMessageResponse Error Code: ${error}")

                      } else {

                        throw new Exception(s"There was an error while OutlookApiService CreateMessageResponse Error Code: ${error}")
                      }


                    } else {

                      newMsgResp

                    }
                  }
                )
            }
          } else {
//            Logger.debug(s"_createMsg: headers: ${replyMsgResp.headers} replyMsgResp.json: ${replyMsgResp.body}")

            Future.successful(replyMsgResp)

          }
        }
      )
  }


  def updateMessage(
                     accessToken: String,
                     emailToBeSent: EmailInfo,
                     createResp: JsValue,
                     send_plain_text_email: Option[Boolean] = Some(false)
                   )(implicit ws: WSClient, ec: ExecutionContext, Logger: ISRLogger): Future[JsValue] = {


    val createId = (createResp \ "id").as[String]


    val updateUrl = s"https://graph.microsoft.com/v1.0/me/messages/${createId}"

    ws.url(updateUrl)
      .addHttpHeaders(
        "Authorization" -> accessToken,
        "Content-Type" -> "application/json",
        "Prefer" -> "IdType=ImmutableId",
      )
      .patch(getBody(
        emailToBeSent = emailToBeSent,
        send_plain_text_email = send_plain_text_email
      ))
      .map(
        updateResp => {

          if (updateResp.status != 200) {

            val errorString: String = Try {
              s"FATAL OutlookApiService UpdateMessageResponse error: $updateResp ::: ${updateResp.body} "
            } match {
              case Success(value) => value
              case Failure(exception) => Logger.error(s"Failed to log", exception)
                "Outlook Error"
            }

            throw new Exception(errorString)


          } else {

            Thread.sleep(500)

            updateResp.json
          }

        }
      )

  }

  def sendEmail(
                 accessToken: String,
                 createResp: JsValue
               )(implicit ws: WSClient, ec: ExecutionContext, Logger: ISRLogger): Future[EmailSentResponse.MSSendEmailRes] = {


    val updateId = (createResp \ "id").as[String]

    val sendUrl = s"https://graph.microsoft.com/v1.0/me/messages/${updateId}/send"

    ws.url(sendUrl)
      .addHttpHeaders(
        "Authorization" -> accessToken,
        "Prefer" -> "IdType=ImmutableId"

        /*
   28-March-2022: passing content-length header broke this api in play 2.7+
   (campaign emails stopped going for office365 users),
   therefore we are commenting it out below
  */
        // "Content-Length" -> msgBody.length.toString
      )
      .post(Json.obj())
      .map(
        sendResp => {
//
//          Try {
//            Logger.debug(s"sendResp: headers: ${sendResp.headers} sendResp.json: ${sendResp.body}")
//          } match {
//            case Success(_) => //DO NOTHING
//            case Failure(exception) => Logger.error(s"Failed to log", exception)
//          }


          if (sendResp.status != 202) {

            Try {
              Logger.fatal(s"FATAL OutlookApiService SendMessageResponse error: $sendResp ::: sendUrl: $sendUrl", request = sendResp)
            } match {

              case Success(_) => //DO NOTHING
              case Failure(exception) => Logger.error(s"Failed to log", exception)
            }

            throw new Exception("Outlook ")


          } else {

            val internetMessageIdOpt = (createResp \ "internetMessageId").asOpt[String]
              .map { id =>
                InternetMessageId(id = id)
              }

            EmailSentResponse.MSSendEmailRes(
              message_id = internetMessageIdOpt,
              outlook_msg_id = None, // This will be updated in OutlookMsgIdFetchAndUpdateCronService.scala
              outlook_conversation_id = (createResp \ "conversationId").asOpt[String],
              outlook_response_json = Some(createResp)
            )

          }

        }
      )
  }


  def sendEmailByCreatingMessage(
        accessToken: String,
        base_64_flow: Boolean,
        from_mail: String,
        emailToBeSent: EmailInfo,
        send_plain_text_email: Option[Boolean])(
                        implicit ws: WSClient,
                        ec: ExecutionContext,
                        Logger: ISRLogger,
                        system: ActorSystem
                      ): Future[EmailSentResponse.MSSendEmailRes] = {


    val newMsgUrl = "https://graph.microsoft.com/v1.0/me/messages"

    val omsgId = emailToBeSent.in_reply_to_id
    var createUrl = newMsgUrl
    if (omsgId.isDefined && omsgId.get.trim.nonEmpty) {
      createUrl = s"https://graph.microsoft.com/v1.0/me/messages/${omsgId.get}/createReply"
    }


    _createMsg(
      base_64_flow = base_64_flow,
      createUrl = createUrl,
      newMsgCreateUrl = newMsgUrl,
      accessToken = accessToken,
      from_mail = from_mail,
      emailToBeSent = emailToBeSent,
      send_plain_text_email = send_plain_text_email,
      Logger = Logger
    )
      .flatMap(
        createResp => {

//          Try {
//            Logger.debug(s"_createMsg: headers: ${createResp.headers} replyMsgResp.json: ${createResp.body}")
//          } match {
//            case Success(_) => //DO NOTHING
//            case Failure(exception) => Logger.error(s"Failed to log", exception)
//          }
          if (createResp.status != 201) {
            Try {
              Logger.error(s"FATAL OutlookApiService CreateMessageResponse error: $createResp ::: ${createResp.body} :: $createUrl :: $from_mail")
            } match {
              case Success(_) => //DO NOTHING
              case Failure(exception) => Logger.error(s"Failed to log", exception)
            }
            throw new Exception("OutlookApiService Error")

          } else {
            Thread.sleep(500)

            if (omsgId.isDefined && omsgId.get.trim.nonEmpty) {

              /*
              We are doing the old flow for when we need to send a reply to a given thread
              since if we follow the new flow, of create reply draft with the data itself, is not reflecting the
              body correctly
               */
              FutureUtils.retry(
                f = updateMessage(
                  accessToken = accessToken,
                  emailToBeSent = emailToBeSent,
                  createResp = createResp.json,
                  send_plain_text_email = send_plain_text_email
                ),
                delay = 500.millis,
                retries = 5,
                growthRate = Some(2)
              )(ec = ec, s = system.scheduler, Logger = Logger)
                .flatMap { updateResp =>

                  Try {
                    val createId = (createResp.json \ "id").as[String]
                    val updateId = (updateResp \ "id").as[String]
                    Logger.debug(s"createid --- $createId ::: updateid ---- $updateId  are they the same --- ${createId == updateId}")
                  } match {
                    case Success(_) => //DO NOTHING
                    case Failure(exception) => Logger.error(s"Failed to log", exception)
                  }
                  sendEmail(
                    accessToken = accessToken,
                    createResp = updateResp
                  )
                }

            } else {
              /*
              since we already added body while creating the draft, we don't need to update it here
               */
              val updateId = (createResp.json \ "id").as[String]

              getDraftMessage(
                accessToken = accessToken,
                internetMessageId = updateId,
                Logger = Logger
              ).flatMap { readResp =>
                sendEmail(
                  accessToken = accessToken,
                  createResp = createResp.json
                )
              }

            }

          }
        }
      )
  }
}
