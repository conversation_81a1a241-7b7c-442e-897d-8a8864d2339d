package io.smartreach.esp.utils

object EmailHelpers {

  /*

"{​{".length    Result: 3 (is not handled correctly by TemplateService, because of the invisible character)
"{{".length    Result: 2

REF: https://stackoverflow.com/questions/11305797/remove-zero-width-space-characters-from-a-javascript-string
REF: https://stackoverflow.com/questions/11020893/remove-non-ascii-non-printable-characters-from-a-string
REF: https://stackoverflow.com/a/31424164


REF: https://www.fileformat.info/info/unicode/char/200b/index.htm

NOTE TO SELF: Earlier I thought I'd remove all invisible characters with this condition
       .replaceAll("[^\\n\\r\\t\\p{Print}]", "")
       but its removing many other things like valid spaces in Jeswanths template,
       and accented characters from <PERSON>'s emails
       So I am now only removing the non white-space character ​​​\u200B which was
       specifically causing the {{ not-getting-recognized problem
       in Toptal and Jeswanths emails
 
 */
  def removeInvisibleCharactersFromTemplate(

    txt: String

  ): String = {


    txt
      .replace("\u200B", "")
      .replace("\u00AD", "") // https://www.fileformat.info/info/unicode/char/00ad/index.htm
      .replace("\u200D", "")



    // .replace(" ", " ")
    // .replace("\u00A0", " ") // try " " == " " :: the first space that is being replaced was getting removed in the next line, so we are replacing with a normal space, need to revisit later
    // .replaceAll("[^\\n\\r\\t\\p{Print}]", "")

  }

}
