package io.smartreach.esp.utils.email

import play.api.libs.json.JsValue
import play.api.libs.ws.WSClient
import utils.ISRLogger
import io.smartreach.esp.api.emails.EmailSettingId
import io.smartreach.esp.api.microsoftOAuth.{MicrosoftOAuthApi, MicrosoftOAuthSettings}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

case class InternetMsgIDForFetchOutlookMsgId(
  email_scheduled_id: Long,
  message_id: String,
  email: String,
  email_setting_id: Long,
  refresh_token: String
)

trait OutlookReplyEmailService extends MicrosoftOAuthApi {


  def updateTrackedOutlookMsgId(email_scheduled_id: Long, outlook_msg_id: String): Try[Int]


  def fetchAndUpdateOutlookMsgIdViaApi(
    data: InternetMsgIDForFetchOutlookMsgId,
    microsoftOAuthSettings: MicrosoftOAuthSettings,
    Logger: ISRLogger
  )(implicit ws: WSClient, ec: ExecutionContext): Future[String] = {

    refreshAccessToken(
      email = data.email,
      emailSettingId = EmailSettingId(emailSettingId = data.email_setting_id), // FIXME: VALUECLASS
      refreshToken = data.refresh_token,
      s = microsoftOAuthSettings
    ).flatMap { res =>

      val accessToken = res.access_token

      // val url = "https://graph.microsoft.com/v1.0/me/mailFolders/SentItems/messages?$filter internetMessageId eq" + s"'${data.message_id}'"

      val url = "https://graph.microsoft.com/v1.0/me/mailFolders/SentItems/messages?$filter=startswith(internetMessageId, " + s"'${data.message_id}')"

      ws.url(url)
        .addHttpHeaders(
          "Authorization" -> accessToken,
          "Content-Type" -> "application/json",
          "Prefer" -> "IdType=ImmutableId"
        )
        .get()
        .flatMap(
          response => {

            if (response.status != 200) {

              Logger.error(s"OutlookApiService fetchAndUpdateOutlookMsgId ${data.email_scheduled_id} :: ${data.email} error: $response ::: ${response.body}")
              Future.failed(new Exception(s"OutlookApiService fetchAndUpdateOutlookMsgId :: error: ${response.body}"))


            } else {

              val messages = (response.json \ "value").as[List[JsValue]]

              if (messages.length > 1) {

                Logger.error(s"FATAL OutlookApiService fetchAndUpdateOutlookMsgId multiple matching messages from same id $url :: (${data.message_id}) emailid: ${data.email_scheduled_id} :: email : ${data.email} :: messages: ${messages.map(msg => ((msg \ "id").asOpt[String], (msg \ "internetMessageId").asOpt[String]))}")

              } else if (messages.isEmpty) {

                Logger.warn(s"FATAL OutlookApiService fetchAndUpdateOutlookMsgId ZERO matching messages from id $url :: (${data.message_id}) emailid: ${data.email_scheduled_id} :: email : ${data.email} :: messages: ${messages.map(msg => ((msg \ "id").asOpt[String], (msg \ "internetMessageId").asOpt[String]))}")

              }

              val m = (response.json \ "value").as[List[JsValue]]
                .filter(m => (m \ "internetMessageId").asOpt[String].getOrElse("") == data.message_id)

                .find(
                  m => {
                    val isDraft = (m \ "isDraft").asOpt[Boolean].getOrElse(false)
                    !isDraft
                  }
                )

              if (m.isEmpty) {

                Logger.warn(s"FATAL OutlookApiService fetchAndUpdateOutlookMsgId USING EMPTY STRING empty messages: ${data.email_scheduled_id}")

              }

              // setting as empty string and not "null" because the sql to fetch this checks for null condition
              val outlook_msg_id = if (m.isEmpty) "" else (m.get \ "id").as[String]

              updateTrackedOutlookMsgId(email_scheduled_id = data.email_scheduled_id, outlook_msg_id = outlook_msg_id) match {

                case Failure(e1) =>

                  Future.failed(e1)


                case Success(_) =>

                  Future.successful(s"OutlookApiService fetchAndUpdateOutlookMsgId -> updateTrackedOutlookMsgId Succesdfully: ${data.email_scheduled_id}")

              }


            }

          }
        )

    }
  }


}
