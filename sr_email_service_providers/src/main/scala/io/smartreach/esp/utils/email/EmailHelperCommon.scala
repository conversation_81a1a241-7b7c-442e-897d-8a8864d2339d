package io.smartreach.esp.utils.email

import org.jsoup.Jsoup
import org.jsoup.nodes.Document.OutputSettings
import org.jsoup.parser.Parser
import org.jsoup.safety.Safelist
import utils.helpers.LogHelpers
import play.api.Logging
import scala.util.{Failure, Success, Try}

object EmailHelperCommon extends Logging {
  // https://stackoverflow.com/a/17279552
  def getTextBodyFromHtmlBody(bodyHtml: String): String = Try {
    // Logger.info(s"getTextBodyFromHtmlBody: cleaned up: $bodyHtml")

    val removedNbspHTML = bodyHtml.replace('\u00A0', ' ')

    // val cleanRandomDivs = __removeEmptyDivs(html = removedNbspHTML)

    // Logger.info(s"getTextBodyFromHtmlBody 1: cleaned up: $removedNbspHTML  from $bodyHtml")

    // get pretty printed html with preserved br and p and div tags
    val prettyPrintedBodyFragment = Jsoup.clean(removedNbspHTML, "", Safelist.none.addTags("br", "p", "div"), new OutputSettings().prettyPrint(false))

//    println(s"getTextBodyFromHtmlBody 2 :: prettyPrintedBodyFragment :: ${prettyPrintedBodyFragment.replace("\n", "\\n")}}")

    val placeholder = "PLACEHOLDER_NEWLINE"
    val modifiedHtml = prettyPrintedBodyFragment.replaceAll("<br>", placeholder)

    // Logger.info(s"getTextBodyFromHtmlBody 2: cleaned up: $prettyPrintedBodyFragment")

    // get plain text with preserved line breaks by disabled prettyPrint
    val out = Jsoup.clean(modifiedHtml, "", Safelist.none, new OutputSettings().prettyPrint(false)).trim

//    println(s"getTextBodyFromHtmlBody 3 :: out :: ${out.replace("\n", "\\n")}")

    // Logger.info(s"getTextBodyFromHtmlBody 3: cleaned up: $out")

    // NOTE: https://stackoverflow.com/a/36484799
    // should sort gabor's "&nbsp;" issue
    val out2 = Parser.unescapeEntities(out, false)

//    println(s"getTextBodyFromHtmlBody 3 :: out2 :: ${out2.replace("\n", "\\n")}}")

    // Logger.info(s"getTextBodyFromHtmlBody 3: out2: $out2")

    // Replace <br> tags with double newlines to handle multiple line breaks

    val out3 = out2.replace(placeholder, "\n")

//    println(s"getTextBodyFromHtmlBody 3 :: out3 :: ${out3}")


    out3
    // replace &nbsp; with a space " "
    // gabor issue
    // this was added on 6th June 2018 by Prateek
    // because sometimes the "&nbsp;" appears as it is in the textPart making it look ugly
    // out.replaceAll("&nbsp;", " ")
  } match {

    case Failure(e) =>
      logger.error(s"[EmailService] [getBaseBodyAndTextBody.textBody] FATAL ERROR returing empty text:: html: $bodyHtml ::error: ${LogHelpers.getStackTraceAsString(e)}")

      ""

    case Success(txt) => txt

  }
}
