package io.smartreach.esp.api.emailApi.models

import play.api.libs.json.{Format, Json}

case class SmtpEmailSetting(
                             smtp_username: String,
                             smtp_password: String,
                             smtp_host: String,
                             smtp_port: Int
                           )

object SmtpEmailSetting {
  implicit val format: Format[SmtpEmailSetting] = Json.format[SmtpEmailSetting]
}