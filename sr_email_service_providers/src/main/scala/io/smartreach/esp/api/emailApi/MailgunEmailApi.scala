package io.smartreach.esp.api.emailApi

import org.apache.pekko.actor.ActorSystem
import io.smartreach.esp.api.emailApi.models.AuthInfo.MailGunAuthInfo
import io.smartreach.esp.api.emailApi.models.{ESPMailgunRegion, UniqueLogIDForSender}
import io.smartreach.esp.api.emails.EmailSentResponse.MailGunApiSentResponse
import io.smartreach.esp.api.emails.{EmailInfo, IEmailAddress}
import play.api.Logging
import play.api.libs.ws.{WSAuthScheme, WSClient}
import utils.ISRLogger

import scala.concurrent.{ExecutionContext, Future}
import play.api.libs.ws.WSBodyWritables.writeableOf_urlEncodedForm




class MailgunEmailApi
  extends EmailApi[MailGunAuthInfo,MailGunApiSentResponse]
  with Logging {

   def sendEmailService(
                                 emailToBeSent: EmailInfo,
                                 authInfo: MailGunAuthInfo,
                                 uniqueLogId: UniqueLogIDForSender
                               )(
                         implicit wsClient: WSClient,
                         ec: ExecutionContext,
                         Logger: ISRLogger,
                         system: ActorSystem
                       ): Future[MailGunApiSentResponse] = {

    val uniqueLogID = uniqueLogId.id
    val mailgunDomain = authInfo.email_domain.email_domain
    val mailgunApiKey = authInfo.api_key.key
    val mailgunRegion = authInfo.mailgun_region

    val baseUrl = if (mailgunRegion == ESPMailgunRegion.EU) {
      s"https://api.eu.mailgun.net/v3/$mailgunDomain"
    } else {
      s"https://api.mailgun.net/v3/$mailgunDomain"
    }

    /*
    val toAddress = if (emailToBeSent.to_name.isEmpty) Seq(emailToBeSent.to_email) else Seq(s"${emailToBeSent.to_name.get} <${emailToBeSent.to_email}>")
    */

    val toAddresses: Seq[String] = IEmailAddress.toStringSeq(emails = emailToBeSent.to_emails)

    var body: Map[String, Seq[String]] = if (authInfo.send_plain_text_email.isDefined && authInfo.send_plain_text_email.get) {
      Map(
        "from" -> Seq(s"""${emailToBeSent.from_name} <${emailToBeSent.from_email}>"""),
        "to" -> toAddresses,
        "subject" -> Seq(emailToBeSent.subject),
        "text" -> Seq(emailToBeSent.text_body)
      )
    } else {
      Map(
        "from" -> Seq(s"""${emailToBeSent.from_name} <${emailToBeSent.from_email}>"""),
        "to" -> toAddresses,
        "subject" -> Seq(emailToBeSent.subject),
        "html" -> Seq(emailToBeSent.html_body),
        "text" -> Seq(emailToBeSent.text_body)
      )
    }

    // add cc to emails
    if (emailToBeSent.cc_emails.nonEmpty) {

      val ccEmails: Seq[String] = IEmailAddress.toStringSeq(emails = emailToBeSent.cc_emails)
      body += ("cc" -> ccEmails)

    }

    // add bcc to emails
    if (emailToBeSent.bcc_emails.nonEmpty) {

      val bccEmails: Seq[String] = IEmailAddress.toStringSeq(emails = emailToBeSent.bcc_emails)
      body += ("bcc" -> bccEmails)

    }


    if (emailToBeSent.message_id.isDefined) {
      body += ("h:Message-ID" -> Seq(emailToBeSent.message_id.get))
    }


    if (emailToBeSent.in_reply_to_id.isDefined) {
      body += ("h:In-Reply-To" -> Seq(emailToBeSent.in_reply_to_id.get))

      // update references header as well
      body += ("h:References" -> Seq(emailToBeSent.references_header.get))
    }


    // Gmail Feedback Loop
    // REF: https://support.google.com/mail/answer/6254652?hl=en
    if (emailToBeSent.gmail_fbl.isDefined) {
      body += ("h:Feedback-ID" -> Seq(emailToBeSent.gmail_fbl.get))
    }

    if (emailToBeSent.list_unsubscribe_header.isDefined) {
      body += ("h:List-Unsubscribe" -> Seq(emailToBeSent.list_unsubscribe_header.get))
    }

    // set reply-to email header only if reply-to email address is different from from-email address
    if (emailToBeSent.reply_to_email.isDefined && emailToBeSent.reply_to_email.get.trim.nonEmpty && emailToBeSent.from_email.trim.toLowerCase != emailToBeSent.reply_to_email.get.trim.toLowerCase) {
      body += ("h:Reply-To" -> Seq(s"${emailToBeSent.reply_to_name.get} <${emailToBeSent.reply_to_email.get}>"))
    }

    //Passing tag to filter mails in mailgun dashboard
    if (authInfo.mailgunTag.isDefined) {
      body += ("o:tag" -> Seq(authInfo.mailgunTag.get))
    }

    //enabling mailgun open tracking on demand
    if (authInfo.enableMailgunOpenTracking.isDefined) {
      body += ("o:tracking-opens" -> Seq(if (authInfo.enableMailgunOpenTracking.get) "yes" else "no"))
    }



    //    println(s"\n\n data: $body \n\n")


    logger.info(s"[MailgunService] sending: $uniqueLogID")

    wsClient.url(s"$baseUrl/messages")
      .withAuth("api", mailgunApiKey, WSAuthScheme.BASIC)
      .post(body)
      .map(response => {

        if (response.status != 200) {
          logger.error(s"[MailgunService] FATAL error: $uniqueLogID ::: $response ::: ${response.body} ::: input: $emailToBeSent ::: $authInfo")

          throw new Exception(s"[MailgunService] ${response.body}. Please check your API Key, Mailgun Email Domain and Mailgun Region")
        } else {
          logger.info(s"[MailgunService] success: $uniqueLogID ::: $response ::: ${response.body}")
          MailGunApiSentResponse(
            response = response.body
          )
        }
      })

  }

}
