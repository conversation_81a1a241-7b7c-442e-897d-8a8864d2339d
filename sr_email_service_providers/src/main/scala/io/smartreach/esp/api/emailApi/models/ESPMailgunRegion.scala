package io.smartreach.esp.api.emailApi.models

import play.api.libs.json.*
import scala.language.implicitConversions

/**
  * REF: https://gist.github.com/mikesname/5237809
  * http://stackoverflow.com/questions/15488639/how-to-write-readst-and-writest-in-scala-enumeration-play-framework-2-1
  */

object EnumUtils {
  implicit def enumReads[E <: Enumeration](enumValue: E): Reads[enumValue.Value] = new Reads[enumValue.Value] {
    def reads(json: JsValue): JsResult[enumValue.Value] = json match {
      case JsString(s) =>
        try {
          JsSuccess(enumValue.withName(s))
        } catch {
          case _: NoSuchElementException =>
            JsError(s"Enumeration expected of type: '${enumValue.getClass}', but it does not appear to contain the value: '$s'")
        }
      case _ => JsError("String value expected")
    }
  }

  implicit def enumWrites[E <: Enumeration](enumValue: E): Writes[enumValue.Value] = new Writes[enumValue.Value] {
    def writes(v: enumValue.Value): JsValue = JsString(v.toString)
  }

  implicit def enumFormat[E <: Enumeration](enumValue: E): Format[enumValue.Value] = {
    Format(enumReads(enumValue), enumWrites(enumValue))
  }
}


import play.api.libs.json.Format

object ESPMailgunRegion extends Enumeration {

  type ESPMailgunRegion = Value

  val US = Value("us")
  val EU = Value("eu")

  implicit val format: Format[ESPMailgunRegion.Value] = EnumUtils.enumFormat(ESPMailgunRegion)
}
