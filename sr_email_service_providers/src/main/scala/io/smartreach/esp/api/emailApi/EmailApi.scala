package io.smartreach.esp.api.emailApi

import org.apache.pekko.actor.ActorSystem
import io.smartreach.esp.api.emailApi.models.{AuthInfo, UniqueLogIDForSender}
import io.smartreach.esp.api.emails.{EmailInfo, EmailSentResponse}
import play.api.libs.ws.WSClient
import utils.ISRLogger

import scala.concurrent.{ExecutionContext, Future}

trait EmailApi[P <: AuthInfo , K <: EmailSentResponse] {
  def sendEmailService(
                        emailToBeSent: EmailInfo,
                        authInfo: P,
                        uniqueLogId: UniqueLogIDForSender // This can be anything and it will be used for only logging purpose
                      )(
                        implicit ws: WSClient,
                        ec: ExecutionContext,
                        Logger: ISRLogger,
                        system: ActorSystem
                      ): Future[K]
}
