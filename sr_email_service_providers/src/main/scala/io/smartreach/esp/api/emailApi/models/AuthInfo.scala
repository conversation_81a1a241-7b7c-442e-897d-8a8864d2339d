package io.smartreach.esp.api.emailApi.models

import play.api.libs.json.{Format, Json}

sealed trait AuthInfo

object AuthInfo {

  case class GmailOAuthInfo(
                             accessToken: String,
                             isAliasAddress: <PERSON><PERSON><PERSON>,
                             user: String,
                             send_plain_text_email: Option[<PERSON><PERSON><PERSON>],
                             gmail_thread_id: Option[String]
                           ) extends AuthInfo

  object GmailOAuthInfo {
    implicit val format: Format[GmailOAuthInfo] = Json.format[GmailOAuthInfo]
  }

  case class GmailSmtpAuthInfo(
                                accessToken: GmailSMTPAccessToken,
                                rep_smtp_reverse_dns_host: Option[String] // used for header only
                              ) extends AuthInfo

  object GmailSmtpAuthInfo {
    implicit val format: Format[GmailSmtpAuthInfo] = Json.format[GmailSmtpAuthInfo]
  }


  case class MailGunAuthInfo(
                              email_domain: MailgunEmailDomain,
                              api_key: MailgunApiKey,
                              mailgun_region: ESPMailgunRegion.Value,
                              enableMailgunOpenTracking: Option[Boolean] = None,
                              mailgunTag: Option[String] = None,
                              send_plain_text_email: Option[Boolean]
                            ) extends AuthInfo

  object MailGunAuthInfo {
    implicit val format: Format[MailGunAuthInfo] = Json.format[MailGunAuthInfo]
  }

  case class SendGridAuthInfo(
                               api_key: SendGridApiKey,
                               send_plain_text_email: Option[Boolean]
                             ) extends AuthInfo

  object SendGridAuthInfo {
    implicit val format: Format[SendGridAuthInfo] = Json.format[SendGridAuthInfo]
  }


  case class SmtpImapAuthInfo(
                               smtpEmailSetting: SmtpEmailSetting,
                               send_plain_text_email: Option[Boolean],
                               dkim: Option[DKIMRecord], // Check for reads
                               rep_smtp_reverse_dns_host: Option[String] // used for header only
                             ) extends AuthInfo

  object SmtpImapAuthInfo {
    implicit val format: Format[SmtpImapAuthInfo] = Json.format[SmtpImapAuthInfo]
  }

  case class OutlookOAuthInfo(
                               accessToken: String,
                               base_64_flow: Boolean,
                               from_mail: String,
                               send_plain_text_email: Option[Boolean]
                             ) extends AuthInfo

  object OutlookOAuthInfo {
    implicit val format: Format[OutlookOAuthInfo] = Json.format[OutlookOAuthInfo]
  }
}