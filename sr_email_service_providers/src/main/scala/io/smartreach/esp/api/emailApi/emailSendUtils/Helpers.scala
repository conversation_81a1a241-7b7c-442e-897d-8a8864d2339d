package io.smartreach.esp.api.emailApi.emailSendUtils

import io.smartreach.esp.api.emailApi.models.DKIMRecord
import io.smartreach.esp.api.emails.EmailInfo
import net.markenwerk.utils.mail.dkim.{Canonicalization, DkimMessage, DkimSigner, SigningAlgorithm}
import org.joda.time.DateTime

import java.security.KeyFactory
import java.security.interfaces.RSAPrivateKey
import java.security.spec.PKCS8EncodedKeySpec
import java.util.Base64
import javax.mail.internet.{MimeBodyPart, MimeMessage, MimeMultipart}
import scala.util.Try

object Helpers {


  final def parseLong(s: String): Option[Long] = if (s.isEmpty) None else Try(s.toLong).toOption


  def appendReplyToHeader(previousEmailSentAt: Option[DateTime]): Boolean = {

    if (previousEmailSentAt.isEmpty) {

      false

    } else {

      val a = new DateTime(1528906213175L) // June 13th 2018
      previousEmailSentAt.get.isAfter(a)

    }

  }


  final def parseLongStrToHex(longString: String): Option[String] = {
    parseLong(s = longString).flatMap(l => Try(l.toHexString).toOption)
  }

  final def parseHexToLongStr(hexString: String): Option[String] = {
    Try(java.lang.Long.valueOf(hexString, 16)).toOption.map(_.toString)
  }

  def dkimSignMessage(message: MimeMessage, from: String, dkimRecord: DKIMRecord): MimeMessage = {

    //converting  private_key string to RSAPrivateKey
    val kf = KeyFactory.getInstance("RSA")
    val keySpecPKCS8 = new PKCS8EncodedKeySpec(Base64.getDecoder.decode(dkimRecord.private_key))
    val key = kf.generatePrivate(keySpecPKCS8).asInstanceOf[RSAPrivateKey]

    val dkimSigner = new DkimSigner(dkimRecord.domain, dkimRecord.selector, key)
    dkimSigner.setIdentity(from.toLowerCase)
    dkimSigner.setHeaderCanonicalization(Canonicalization.SIMPLE)
    dkimSigner.setBodyCanonicalization(Canonicalization.RELAXED)
    dkimSigner.setSigningAlgorithm(SigningAlgorithm.SHA256_WITH_RSA)
    dkimSigner.setLengthParam(true)
    dkimSigner.setZParam(false)
    dkimSigner.setCheckDomainKey(false)
    val dkimMessage = new DkimMessage(message, dkimSigner)
    dkimMessage
  }

  def createMultipartForEmailMessage(
                                      emailToBeSent: EmailInfo,
                                      send_plain_text_email: Option[Boolean]
                                    ): MimeMultipart = {
    // Create multipart/alternative
    val multiPart = new MimeMultipart("alternative")

    // Create and configure the text part
    val textPart = new MimeBodyPart()
    textPart.setText(emailToBeSent.text_body, "utf-8")
    textPart.setHeader("Content-Transfer-Encoding", "quoted-printable")

    // Create and configure the HTML part
    val htmlPart = new MimeBodyPart()
    htmlPart.setContent(emailToBeSent.html_body, "text/html; charset=utf-8")
    htmlPart.setHeader("Content-Transfer-Encoding", "quoted-printable")

    // Add parts to multipart
    if (send_plain_text_email.isDefined && send_plain_text_email.get) {
      multiPart.addBodyPart(textPart)
    } else {
      multiPart.addBodyPart(textPart)
      multiPart.addBodyPart(htmlPart)
    }

    multiPart
  }

}
