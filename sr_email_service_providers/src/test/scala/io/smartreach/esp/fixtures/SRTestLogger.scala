package io.smartreach.esp.fixtures

import utils.ISRLogger


// ISRLogger Logger implementation for tests.

class SRTestLogger(

  override val logRequestId: String,

  override val defaultLogAllInStaging: Boolean = false, // added on 16 March 2023: to reduce GCP Logging costs for staging env

  override val customLogTraceId: Option[String] = None

) extends ISRLogger {

  override val isProd: Boolean = false

  // append additional info to logRequestId and return new instance of SRLogger
  override def appendLogRequestId(appendLogReqId: String): SRTestLogger = {

    new SRTestLogger(
      logRequestId = s"$appendLogReqId ::: $logRequestId",

      customLogTraceId = Some(logTraceId), // retain the original trace-id
    )

  }

}
