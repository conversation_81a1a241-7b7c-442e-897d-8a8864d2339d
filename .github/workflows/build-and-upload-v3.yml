name: Build and Upload <PERSON><PERSON>

on:
  workflow_dispatch:

permissions:
  pull-requests: write
  contents: read

jobs:
  build-and-upload:
    runs-on: ubuntu-22.04-16gb-4core-custom
    if: github.event_name == 'workflow_dispatch' || github.base_ref == 'master'
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        # If using a PAT for private repo access, uncomment below
        # with:
        #   token: ${{ secrets.PERSONAL_ACCESS_TOKEN }}

      # Configure Git safe directory
      - name: Configure Git Safe Directory
        run: git config --global --add safe.directory /workspace

      # Cache SBT Dependencies
      # 3-feb-2025: since we are using a Docker-based setup where dependencies are managed through volume mounts and the cache isn't being effectively utilized, we are removing the caching step. This step was taking 2:30 mins to run.
      - name: Cache SBT Dependencies
        id: sbt_cache
        uses: actions/cache@v4
        with:
          path: |
            ~/.ivy2/cache
            ~/.sbt
            ~/.cache/coursier
          key: sbt-${{ hashFiles('**/build.sbt', '**/project/build.properties', '**/project/plugins.sbt') }}
          restore-keys: sbt-

      # Check if cache was hit
      - name: Check Cache Hit
        run: |
          if [[ "${{ steps.sbt_cache.outputs.cache-hit }}" == "true" ]]; then
            echo "Cache found."
            echo "CACHE_HIT=true" >> $GITHUB_ENV
          else
            echo "Cache not found. Triggering save-cache workflow."
            echo "CACHE_HIT=false" >> $GITHUB_ENV
          fi

      # Set up Google Cloud credentials for authentication
      - name: Set up Google Cloud credentials
        env:
          GOOGLE_CLOUD_AUTH: ${{ secrets.GOOGLE_CLOUD_AUTH }}
        run: |
          echo "$GOOGLE_CLOUD_AUTH" > /tmp/gcloud-credentials.json
          echo "GOOGLE_APPLICATION_CREDENTIALS=/tmp/gcloud-credentials.json" >> $GITHUB_ENV

      # Authenticate using google-github-actions/auth
      - name: Authenticate Google Cloud
        uses: google-github-actions/auth@v0
        with:
          credentials_json: ${{ secrets.GOOGLE_CLOUD_AUTH }}

      # Set the authenticated account (service account) as the active account
      - name: Set active account
        run: |
          gcloud auth activate-service-account --key-file=/tmp/gcloud-credentials.json
          gcloud config set account $(gcloud auth list --filter=status:ACTIVE --format="value(account)")

      # Set up Google Cloud SDK (no need to specify credentials here anymore)
      - name: Set up Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v1
        with:
          version: 'latest'

      # Authenticate Docker to Artifact Registry
      - name: Authenticate Docker to Artifact Registry
        run: |
          export GOOGLE_APPLICATION_CREDENTIALS=/tmp/gcloud-credentials.json
          gcloud auth configure-docker us-central1-docker.pkg.dev

      # Run RabbitMQ Container
      # Used an existing rabbit mq image for the tests as adding it to scala-test-image was giving memory exceeded error
      - name: Run RabbitMQ Container
        run: |
          docker run -d \
            --name rabbitmq-container \
            --memory="2g" \
            --hostname rabbitmq \
            -p 5672:5672 -p 15672:15672 \
            -e RABBITMQ_DEFAULT_VHOST=local_db_host \
            -e RABBITMQ_DEFAULT_USER=guest \
            -e RABBITMQ_DEFAULT_PASS=guest \
            rabbitmq:3.12-management-alpine

      # Pull or Build and Push Docker Image
      # Adding the image to artifact registry in sr-staging project and directly pulling it as the container will always have
      # the same setup
      - name: Pull Docker Image
        run: |
          scala_test_image="us-central1-docker.pkg.dev/sr-staging-341312/scala-test-image/scala-test-image:latest"
          docker pull $scala_test_image

      # Run Docker Container
      # This container requires 16GB space as below this it starts giving memory exceeded error when tests are running
      - name: Run Docker Container
        run: |
          scala_test_image="us-central1-docker.pkg.dev/sr-staging-341312/scala-test-image/scala-test-image:latest"
          docker run -d \
            --name scala-test-container \
            --memory="16g" \
            -v ${{ github.workspace }}:/workspace \
            -v ~/.ivy2:/root/.ivy2 \
            -v ~/.sbt:/root/.sbt \
            -v ~/.cache/coursier:/root/.cache/coursier \
            --link rabbitmq-container:rabbitmq \
            -v /tmp/gcloud-credentials.json:/tmp/gcloud-credentials.json \
            -w /workspace \
            -e POSTGRES_USER=heaplabs \
            -e POSTGRES_PASSWORD=postgresql127 \
            -e POSTGRES_DB=coldemail_dev1 \
            -e POSTGRES_HOST_AUTH_METHOD=trust \
            -e GOOGLE_APPLICATION_CREDENTIALS=/tmp/gcloud-credentials.json \
            -p 5432:5432 \
            -p 7777:7777 \
            $scala_test_image

      # Verify the entrypoint script in the Docker container
      - name: Verify entrypoint.sh
        run: |
          docker exec scala-test-container bash -c "ls -l /usr/local/bin/entrypoint.sh"
          docker exec scala-test-container bash -c "if [ -x /usr/local/bin/entrypoint.sh ]; then echo 'entrypoint.sh is executable'; else echo 'entrypoint.sh is NOT executable'; exit 1; fi"

      # Wait for PostgreSQL, Redis, and RabbitMQ services to be ready
      - name: Wait for Services to be Ready
        run: |
          echo "Waiting for services to start..."
          until docker exec scala-test-container bash -c "pg_isready -U postgres"; do
            echo "Waiting for PostgreSQL..."
            sleep 2
          done
          echo "PostgreSQL is ready!"
          
          # Wait for Redis
          until docker exec scala-test-container bash -c "redis-cli -p 7777 ping | grep -q PONG"; do
            echo "Waiting for Redis..."
            sleep 2
          done
          echo "Redis is ready!"
          until docker exec rabbitmq-container rabbitmq-diagnostics check_running; do
            echo "Waiting for RabbitMQ..."
            sleep 2
          done
          echo "RabbitMQ is ready!"

      # Configure RabbitMQ Virtual Host and Credentials
      - name: Configure RabbitMQ Virtual Host and Credentials
        run: |
          echo "Setting up RabbitMQ virtual host and credentials..."
          docker exec rabbitmq-container rabbitmqctl set_permissions -p local_db_host guest ".*" ".*" ".*"
          docker exec rabbitmq-container rabbitmqctl set_user_tags guest administrator
          docker exec rabbitmq-container rabbitmqadmin declare exchange name=test_exchange type=direct -u guest -p guest -V local_db_host
          docker exec rabbitmq-container rabbitmqadmin declare queue name=test_queue durable=true -u guest -p guest -V local_db_host
          docker exec rabbitmq-container rabbitmqadmin declare binding source=test_exchange destination=test_queue routing_key=test_key -u guest -p guest -V local_db_host

      # Configure PostgreSQL max_connections
      - name: Configure PostgreSQL max_connections
        run: |
          docker exec scala-test-container bash -c "\
            CONFIG_PATH=\$(psql -U postgres -c 'SHOW config_file;' | grep -E '/postgresql.conf$') && \
            sed -i 's/^#max_connections =.*/max_connections = 250/' \$CONFIG_PATH && \
            service postgresql restart && \
            psql -U postgres -c 'SHOW max_connections;'"

      # Verify PostgreSQL Configuration
      - name: Verify PostgreSQL Configuration
        run: |
          docker exec scala-test-container bash -c "psql -U postgres -c 'SHOW max_connections;'"

      # Verify Redis connectivity
      - name: Run Redis check
        run: |
          docker exec scala-test-container redis-cli -p 7777 ping

      # Run tests and capture timings
      # We need a different hostname for RabbitMQ when running tests inside the Docker container. Renaming application.conf
      # to application_default.conf and github_application.conf to application.conf to run tests with the correct configuration.
      - name: Run Tests with Timings
        run: |
          docker exec scala-test-container bash -c \
          'export SBT_OPTS="-Xmx8G -Xms6G -XX:+UseG1GC -XX:MetaspaceSize=256M -XX:MaxMetaspaceSize=1024M -XX:InitialCodeCacheSize=512m -XX:ReservedCodeCacheSize=512m" && \
           mv /workspace/coldemail/src/test/resources/application.conf /workspace/coldemail/src/test/resources/application_default.conf && \
           mv /workspace/coldemail/src/test/resources/github_application.conf /workspace/coldemail/src/test/resources/application.conf && \
           sbt "coldemail/testOnly * -- -oDF" 2>&1 | tee test-timings.txt; \
           TEST_EXIT_CODE=${PIPESTATUS[0]}; \
           echo "SBT Exit Code: $TEST_EXIT_CODE"; \
           if [ $TEST_EXIT_CODE -ne 0 ]; then echo "Tests failed, exiting..."; exit 1; fi; \
           exit $TEST_EXIT_CODE' || { echo "Tests failed, exiting..."; exit 1; }

      # Upload test timings as an artifact for debugging and reference
      - name: Upload Test Timings Artifact
        uses: actions/upload-artifact@v4
        with:
          name: test-timings
          path: test-timings.txt

      # Build JAR with SBT, skipping tests since they were run earlier
      # We need a different hostname for RabbitMQ when building inside the Docker container. Renaming application.conf
      # to application_default.conf and github_application.conf to application.conf to match the test configuration.
      # Using -DskipTests=true to skip tests, assuming build.sbt supports it.

      - name: Build JAR with SBT
        run: |
          docker exec scala-test-container bash -c \
          'export SBT_OPTS="-Xmx8G -Xms6G -XX:+UseG1GC -XX:MetaspaceSize=256M -XX:MaxMetaspaceSize=1024M -XX:InitialCodeCacheSize=512m -XX:ReservedCodeCacheSize=512m" && \
           sbt -Dparallelism=4 -DskipTests=true coldemail/assembly'


      # Generate Cache Key and upload it as an artifact if cache is missing
      - name: Generate Cache Key
        if: env.CACHE_HIT == 'false'
        run: |
          CACHE_KEY="sbt-${{ hashFiles('**/build.sbt', '**/project/build.properties', '**/project/plugins.sbt') }}"
          echo "$CACHE_KEY" > cache-key.txt
          echo "CACHE_KEY=$CACHE_KEY" >> $GITHUB_ENV

      # Upload Cache Key as Artifact
      - name: Upload Cache Key as Artifact
        if: env.CACHE_HIT == 'false'
        uses: actions/upload-artifact@v4
        with:
          name: ${{ env.CACHE_KEY }}
          path: cache-key.txt

      # Determine Scala version for JAR path
      - name: Determine Scala Version
        run: |
          DEFAULT_SCALA_VERSION=$(grep "val defaultScalaVersion =" build.sbt | awk '{print $4}' | tr -d '"')
          echo "Detected defaultScalaVersion: $DEFAULT_SCALA_VERSION"
          echo "SCALA_VERSION=$DEFAULT_SCALA_VERSION" >> $GITHUB_ENV

      # Copy JAR and resources from container to host for GCS upload
      - name: Copy JAR and Resources from Container
        run: |
          JAR_PATH="/workspace/coldemail/target/scala-${{ env.SCALA_VERSION }}/coldemail.jar"
          RESOURCES_PATH="/workspace/coldemail/src/main/resources"
          RELEASES_FOLDER=sr-releases
          if [ ! -d $RELEASES_FOLDER ]; then
            echo "creating releases folder: $RELEASES_FOLDER"
            mkdir -p $RELEASES_FOLDER
          fi
          GCS_FOLDER=srjars
          BUILD_DATE=`date +%y-%m-%d-T-%H-%M-%S`
          BUILD_SUB_FOLDER=$GCS_FOLDER-$BUILD_DATE
          mkdir -p $RELEASES_FOLDER/$BUILD_SUB_FOLDER
          docker cp scala-test-container:"$JAR_PATH" $RELEASES_FOLDER/$BUILD_SUB_FOLDER/
          docker cp scala-test-container:"$RESOURCES_PATH" $RELEASES_FOLDER/$BUILD_SUB_FOLDER/
          cd $RELEASES_FOLDER
          tar -cvf $BUILD_SUB_FOLDER.tar $BUILD_SUB_FOLDER/
          ls -l $BUILD_SUB_FOLDER.tar
          gsutil cp $BUILD_SUB_FOLDER.tar gs://$GCS_FOLDER
          cd ..
          rm $RELEASES_FOLDER/$BUILD_SUB_FOLDER.tar
          rm -rf $RELEASES_FOLDER/$BUILD_SUB_FOLDER

      # Install gawk for processing test timings
      - name: Install gawk
        run: |
          sudo apt-get update
          sudo apt-get install -y gawk

      # Process test timings and post slow tests (>1 second) to PR comment
      - name: Post Test Timings to PR
        if: github.event_name == 'pull_request'
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          cat << 'EOF' > comment_script.sh
          #!/bin/bash
          # Extract tests with duration > 1 second (containing "seconds")
          SUMMARY=$(grep -E '\([0-9]+ seconds, [0-9]+ milliseconds\)' test-timings.txt | gawk '
            {
              test_name = gensub(/^[ \t]*\[info\] - (.*?)(\s+\*\*\* FAILED \*\*\*)? \([0-9]+ seconds, [0-9]+ milliseconds\).*/, "\\1", "g");
              seconds = gensub(/.*\(([0-9]+) seconds, [0-9]+ milliseconds\).*/, "\\1", "g");
              millis = gensub(/.*\([0-9]+ seconds, ([0-9]+) milliseconds\).*/, "\\1", "g");
              duration = seconds * 1000 + millis;
              print test_name " (" duration "ms)";
            }
          ' | sort | head -n 50)

          # Fallback message if no tests exceed 1 second
          if [ -z "$SUMMARY" ]; then
            SUMMARY="No tests took more than 1 second."
          fi

          # Add truncation note if more than 50 tests
          if [ $(grep -E '\([0-9]+ seconds, [0-9]+ milliseconds\)' test-timings.txt | wc -l) -gt 50 ]; then
            SUMMARY="$SUMMARY\n... (Full list truncated, see test-timings artifact for details)"
          fi

          # Extract failed tests
          FAILED_TESTS=$(grep "Failed tests:" test-timings.txt -A 10 | tail -n +2 | head -n -2 || echo "No failed tests")

          # Format the comment
          COMMENT="### Tests Taking More Than 1 Second\n\`\`\`\n$SUMMARY\n\`\`\`\n### Failed Tests\n\`\`\`\n$FAILED_TESTS\n\`\`\`\nFull timings available in the [test-timings artifact](https://github.com/${GITHUB_REPOSITORY}/actions/runs/${GITHUB_RUN_ID})"

          # Post comment to PR
          gh pr comment ${PR_NUMBER} --body "$COMMENT"
          EOF
          chmod +x comment_script.sh
          PR_NUMBER=${{ github.event.pull_request.number }} GITHUB_REPOSITORY=${{ github.repository }} GITHUB_RUN_ID=${{ github.run_id }} ./comment_script.sh

      # Stop and clean up Docker containers
      - name: Stop and Clean Up Docker Container
        if: always()
        run: |
          docker stop scala-test-container rabbitmq-container
          docker rm scala-test-container rabbitmq-container
