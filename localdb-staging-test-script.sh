############################################################
#
#LOCALDB staging script for running localdb public api test
############################################################

newline () {

  times=${1:-1}

  for ((i=1;i<=$times;i++)); do

    printf "\n"

  done

}

# prints colored text
print_style () {

    if [[ "$2" == "info" ]] ; then
        COLOR="96m";
    elif [[ "$2" == "success" ]] ; then
        COLOR="92m";
    elif [[ "$2" == "warning" ]] ; then
        COLOR="93m";
    elif [[ "$2" == "danger" ]] ; then
        COLOR="91m";
    else #default color
        COLOR="92m"; #success
    fi

    STARTCOLOR="\e[$COLOR";
    ENDCOLOR="\e[0m";

    newline

    printf "$STARTCOLOR%b$ENDCOLOR" "$1";
}

print_style "==================================" "info";
print_style "local testing script: starting ..." "info";
print_style "==================================" "info";

newline

PID=$(ps -eaf | grep "java" | grep -v grep | awk '{print $2}')
if [[ "" !=  "$PID" ]]; then
  print_style "killing java ProcessId: ${PID}" "info"
  newline
  # 01/05/2024 - The kill $PID was causing problem on sr-staging-2 so changes it to kill -9 $PID
  sudo kill -9 $PID

  ps -eaf | grep "java"

fi

#PID=$(ps -eaf | grep "coldemail/run 8000 -Dconfig.resource=application.local_db.conf" | grep -v grep | awk '{print $2}')
#if [[ "" !=  "$PID" ]]; then
#  print_style "killing ProcessId: ${PID}" "info"
#  newline
#  # 01/05/2024 - The kill $PID was causing problem on sr-staging-2 so changes it to kill -9 $PID
#  kill -9 $PID
#fi
#
#if [ $? -eq 0 ]; then
#    print_style "Process Kill Success" "success"
#    newline
#else
#    print_style "Process Kill Failed or may have not existed before" "warning"
#    newline 2
#fi
#
#sleep 5
#
#print_style "Staring localhost:8000" "info"
#newline
#
#nohup sbt -J-Xmx3G -J-Xms3G "coldemail/run 8000 -Dconfig.resource=application.local_db.conf" &
#
#if [ $? -eq 0 ]; then
#    print_style "Server for localhost:8000 started !!!!!" "success"
#    newline 2
#else
#    print_style "Server start failed" "danger"
#    newline 2
#fi
#
#sleep 5

print_style "Starting rabbitMq server" "info"
newline

sudo systemctl start rabbitmq-server

sleep 5

print_style "Adding virtual host" "info"
newline

sudo rabbitmqctl add_vhost local_db_host

sleep 5

print_style "Adding permissions to virtual host" "info"
newline

sudo rabbitmqctl set_permissions -p "local_db_host" "guest" ".*" ".*" ".*"

sleep 10

sbt -J-Xmx2G -J-Xms2G coldemail/assembly

if [ $? -eq 0 ]; then
    print_style  "assembly command Success!!!!!" "success"

else
    print_style "Error occurred while running the assembly command" "danger"
fi
