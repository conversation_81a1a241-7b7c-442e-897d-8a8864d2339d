package api.accounts


import api.accounts.models.{AccountProfileInfo, SignupType}
import api.calendar_app.models.CalendarAccountData
import org.joda.time.DateTime
import play.api.libs.json.{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Reads, Writes}
import play.api.libs.json.JodaWrites._
import play.api.libs.json.JodaReads._
import utils.uuid.SrUuidResource

case class CommonAuthAccount(
                              id: AccountUuid,
                              internal_id: Long, // Fixme: convert to value class
                              email: String,
                              created_at: DateTime,

                              first_name: Option[String],
                              last_name: Option[String],
                              company: Option[String],
                              timezone: Option[String],

                              profile: AccountProfileInfo,

                              teams: Seq[CommonAuthTeamAccount],
                              org: CommonAuthOrganization,
                              active: Boolean,
                              account_metadata: AccountMetadata,
                              email_verified: Boolean,
                              signupType: Option[SignupType],

                              calendar_account_data: Option[CalendarAccountData]


                            ) extends SrUuidResource {

  override val uuid: String = id.toString

}

object CommonAuthAccount {


  // need to write all keys to redis, unlike for the api response; else the deserialisation breaks
  implicit val writes: Writes[CommonAuthAccount] = Json.writes[CommonAuthAccount]

  implicit val reads: Reads[CommonAuthAccount] = Json.reads[CommonAuthAccount]
}
