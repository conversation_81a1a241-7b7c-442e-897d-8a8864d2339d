package api.accounts

import play.api.libs.json.{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>s<PERSON><PERSON>ult, JsString, JsSuccess, JsV<PERSON>ue, Reads, Writes}



  opaque type AccountUuid = String

object AccountUuid {
  def apply(uuid: String): AccountUuid = uuid

  def unwrap(accountUuid: AccountUuid): String = accountUuid

  implicit val reads: Reads[AccountUuid] = new Reads[AccountUuid] {
    override def reads(json: JsValue): JsResult[AccountUuid] = {
      json match {
        case JsString(uuid) => JsSuccess(AccountUuid(uuid = uuid))
        case randomValue => JsError(s"expected String, got some random value - $randomValue")
      }
    }
  }

  implicit val writes: Writes[AccountUuid] = new Writes[AccountUuid] {
    override def writes(o: AccountUuid): JsValue = JsString(o)
  }
}
