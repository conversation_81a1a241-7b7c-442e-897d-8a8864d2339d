package api.accounts.models

import play.api.libs.json.{Js<PERSON><PERSON><PERSON>, JsString, JsSuc<PERSON>, Reads}
import utils.enum_sr_utils.SREnumJsonUtils

import scala.util.{Failure, Success, Try}

sealed trait SignupType
sealed trait OAuthSignupType extends SignupType//added to make the match for NewAuthSignupThroughHostService exhaustive.

object SignupType extends SREnumJsonUtils[SignupType]{
  override protected val enumName: String = "SignupType"

  private val google = "google"
  private val microsoft = "microsoft"
  private val password = "password"

  case object Google extends OAuthSignupType{
    override def toString: String = google
  }

  case object Microsoft extends OAuthSignupType{
    override def toString: String = microsoft
  }

  case object Password extends SignupType{
    override def toString: String = password
  }

  override def fromKey(key: String): Try[SignupType] = Try{
    key match {
      case `google` => Google
      case `microsoft` => Microsoft
      case `password` => Password
    }
  }

  def fromKeyOAuth(key: String): Try[OAuthSignupType] = Try{
    key match {
      case `google` => Google
      case `microsoft` => Microsoft
    }
  }

  override def toKey(value: SignupType): String = {
    value.toString
  }

//  override given reads: Reads[SignupType] = {
//    case JsString(value) =>
//      fromKey(key = value) match {
//
//        case Failure(exception) => JsError(exception.toString)
//
//        case Success(data) => JsSuccess(value = data)
//      }
//
//    case _ =>
//      JsError(s"""Expected a SignupType string, got something else """)
//  }

}
