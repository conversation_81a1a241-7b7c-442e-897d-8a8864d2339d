package api.common_auth.models

import play.api.libs.json.{Json, Format}



/*
access_token : The access token issued by the authorization server.

expires_in	: The lifetime in seconds of the access token. For example, the value "3600" denotes that the access token will expire in one hour from the time the response was generated.

id_token	: To retrieve a refresh token request the id_token scope.

refresh_token	: The refresh token, which can be used to obtain new access tokens. To retrieve it add the scope "offline" to your access token request.

scope	: The scope of the access token

token_type : The type of the token issued


 */

//FIXME: CONVERT TO Value Class
case class AccessTokenExchangeResponse(
                                        access_token : String,
                                        expires_in: Long,
                                        id_token: Option[Long],
                                        refresh_token: Option[String],
                                        scope: String,
                                        token_type: String
                                      )

object AccessTokenExchangeResponse {
  implicit  val format: Format[AccessTokenExchangeResponse] = Json.format[AccessTokenExchangeResponse]
}
