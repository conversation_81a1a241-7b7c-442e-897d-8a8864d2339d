package api.common_auth

import play.api.libs.json.{<PERSON><PERSON>, Reads}

case class RejectConsentRequest(
                                 consent_challenge: Option[String],
                                 error: Option[String],
                                 error_debug: Option[String],
                                 error_description:Option[String],
                                 error_hint:Option[String],
                                 status_code: Int
                               )

object RejectConsentRequest {
  implicit  val reads: Reads[RejectConsentRequest] = Json.reads[RejectConsentRequest]
}