package api.calendar_app

import play.api.libs.json._

case class CalendarUserId(id: Long) extends AnyVal


object CalendarUserId {
  implicit val reads: Reads[CalendarUserId] = new Reads[CalendarUserId] {
    override def reads(json: JsValue): JsResult[CalendarUserId] = {
      json match {
        case JsNumber(id) => JsSuccess(CalendarUserId(id  = id.toLong))
        case randomValue => JsError(s"expected number, got some random value - $randomValue")
      }
    }
  }

  implicit val writes: Writes[CalendarUserId] = new Writes[CalendarUserId] {
    override def writes(o: CalendarUserId): JsValue = JsNumber(o.id)
  }
}