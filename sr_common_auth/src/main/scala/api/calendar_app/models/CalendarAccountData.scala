package api.calendar_app.models
import api.calendar_app.CalendarUserId
import play.api.libs.json.{Format, JsValue, Json, OWrites, Reads, Writes}



case class CalendarAccountData(
                                calendar_user_id : CalendarUserId,
                                calendar_username_slug :String,
                              )


object CalendarAccountData {
  implicit val writes: OWrites[CalendarAccountData] = Json.writes[CalendarAccountData]
  implicit val reads: Reads[CalendarAccountData] = Json.reads[CalendarAccountData]

}
