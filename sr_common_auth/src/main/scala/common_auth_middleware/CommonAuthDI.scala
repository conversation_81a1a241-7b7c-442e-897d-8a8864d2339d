package common_auth_middleware

import org.apache.pekko.stream.Materializer
import api.CacheServiceJedis
import api.accounts.service.UserRedisKeyService
import api.common_auth.CommonAuthService
import common_auth_middleware.common_oauth_api.CommonAuthApi
import common_auth_middleware.service.SRCommonAuthService
import play.api.mvc.BodyParsers

import scala.concurrent.ExecutionContext



trait SRCommonAuthServiceDI {
  lazy val commonAuthApi : CommonAuthApi

  lazy val srCommonAuthService = new SRCommonAuthService(
    commonAuthApi = commonAuthApi
  )
}

trait CommonAuthMiddlewareDI {


  lazy val commonAuthLoggingAction: CommonAuthLoggingAction
  lazy val srCommonAuthService : SRCommonAuthService

  lazy val commonAuthMiddleware = new CommonAuthMiddleware(
    commonAuthLoggingAction = commonAuthLoggingAction,
    srCommonAuthService = srCommonAuthService
  )
}

trait CommonAuthLoggingActionDI {

  lazy val playDefaultExecutionContext: ExecutionContext
  lazy val actorMaterializer: Materializer

  lazy val commonAuthBodyParser: BodyParsers.Default = new play.api.mvc.BodyParsers.Default()(actorMaterializer)

  lazy val commonAuthLoggingAction = new CommonAuthLoggingAction(
    parser = commonAuthBodyParser
  )(playDefaultExecutionContext)
}

trait CommonAuthApiDI {
  lazy val commonAuthApi = new CommonAuthApi
}

trait CommonAuthServiceDI {

  lazy val commonAuthApi: CommonAuthApi
  lazy val userRedisKeyService: UserRedisKeyService
  lazy val commonAuthService = new CommonAuthService(
    commonAuthApi = commonAuthApi,
    userRedisKeyService = userRedisKeyService,
  )
}

trait  UserRedisKeyService_DI {
  lazy val cacheServiceJedis: CacheServiceJedis
  lazy val userRedisKeyService = new UserRedisKeyService(
    cacheServiceJedis = cacheServiceJedis
  )
}