package common_auth_middleware.common_oauth_api

import api.AppConfig
import api.accounts.CommonAuthAccount
import api.common_auth.{CommonOauthData, NewRefreshedAccessToken, RejectConsentRequest}

import api.common_auth.models.{AccessTokenExchangeResponse, OAuth2ConsentRequest, OAuth2LoginRequest}
import api.config.AppConfigCommonAuth
import play.api.libs.ws.WSClient
import utils.ISRLogger
import common_auth_middleware.models.TokenIntrospectionResponse
import org.joda.time.DateTime
import play.api.libs.json.Json

import java.net.URLEncoder
import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}
import play.api.libs.ws.WSBodyWritables.{writeableOf_JsValue, writeableOf_String}



class CommonAuthApi {


  def introspectAccessToken(access_token: String, introspectionUrl: String)(
    implicit ws: WSClient,
    ec: ExecutionContext,
    Logger: ISRLogger
  ): Future[TokenIntrospectionResponse] = {


    val data = Map(
      "token" -> access_token
    )
    val encodedData = data.map { case (key, value) => {
      s"${URLEncoder.encode(key, "UTF-8")}=${URLEncoder.encode(value, "UTF-8")}"
    }
    }.mkString("&")

    ws.url(introspectionUrl)
      .withHttpHeaders(
        "Content-type" -> "application/x-www-form-urlencoded"
      )
      .post(encodedData)
      .flatMap(res => {
        if (res.status != 200) {

          val errorMsg = (res.json \ "error").as[String]
          Logger.fatal(s"Error occurred introspectAccessToken :: ${errorMsg}")
          Future.failed(new Exception(errorMsg))

        } else {

          Try {
            (res.json).as[TokenIntrospectionResponse]
          } match {

            case Failure(e) => Future.failed(e)

            case Success(value) => Future.successful(value)
          }

        }
      })
  }


  /*
   Api Ref Link: https://www.ory.sh/docs/hydra/reference/api#tag/oAuth2/operation/oAuth2Authorize
   */
  def authorizationUrl(
                        state: String,
                        clientId: String,
                        authBaseUrl: String,
                        queryStringMap: Map[String,String]
                      )(
                        implicit ws: WSClient,
                        ec: ExecutionContext,
                        Logger: ISRLogger
                      ): String = {

    val queryParams: Seq[(String, String)] = queryStringMap.foldLeft(Seq(
      "response_type" -> "code",
      "state" -> state,
      "client_id" -> clientId,
      "scope" -> "profile email offline_access"
    )) { (acc, entry) =>
      acc :+ (entry._1 -> entry._2)
    }

    val finalUrl = ws.url(authBaseUrl)
      .withQueryStringParameters(queryParams*)
      .uri
      .toString

    finalUrl
  }


  /*
   Api Ref Link: https://www.ory.sh/docs/hydra/reference/api#tag/oAuth2/operation/acceptOAuth2LoginRequest
  */
  def acceptLogin(
                   loginChallenge: String,
                   acceptLoginUrl: String,
                   subject: String,
                   remember: Boolean,
                   remember_for_duration_in_seconds: Long
                 )(
                   implicit ws: WSClient,
                   ec: ExecutionContext,
                   Logger: ISRLogger
                 ): Future[String] = {
    ws.url(acceptLoginUrl)
      .withQueryStringParameters(
        "login_challenge" -> loginChallenge
      )
      .put(Json.obj(
        "subject" -> subject,
        "remember" -> remember,
        "remember_for" -> remember_for_duration_in_seconds
      ))
      .flatMap(res => {
        if (res.status != 200) {
          val errorMsg = (res.json \ "error").asOpt[String]
          val errorDescriptionMsg = (res.json \ "error_description").asOpt[String]
          val errorHintMsg = (res.json \ "error_hint").asOpt[String]
          val finalErrorMessage = s" LoginFlow : CommonAuthApi acceptLogin ::: Error Occurred acceptLogin for accountId:${subject} :: Error: ${errorMsg.toString} Error_Descrption: ${errorDescriptionMsg.toString}  Error_hint: ${errorHintMsg.toString} "
          Logger.warn(finalErrorMessage)
          val redirect_to = AppConfig.dashboardDomain
          Future.successful(redirect_to)
        } else {
          val redirect_to = (res.json \ "redirect_to").as[String]
          Logger.info(s"Redirect in OauthApi is ${redirect_to}")
          Future.successful(redirect_to)

        }
      })
  }

  /*
    Api Ref Link:   https://www.ory.sh/docs/hydra/reference/api#tag/oAuth2/operation/getOAuth2LoginRequest
   */
  def getLoginRequest(
                       loginChallenge: String,
                       loginRequestUrl: String
                     )(
                       implicit ws: WSClient,
                       ec: ExecutionContext,
                       Logger: ISRLogger
                     ): Future[OAuth2LoginRequest] = {
    ws.url(loginRequestUrl)
      .withQueryStringParameters(
        "login_challenge" -> loginChallenge
      )
      .get()
      .flatMap(res => {
        if (res.status != 200) {
          val errorMsg = (res.json \ "error").asOpt[String]
          val errorDescriptionMsg = (res.json \ "error_description").asOpt[String]
          val errorHintMsg = (res.json \ "error_hint").asOpt[String]
          val finalErrorMessage = s"CommonAuthApi acceptLogin ::: Error Occurred acceptLogin :: Error: ${errorMsg.toString} Error_Descrption: ${errorDescriptionMsg.toString}  Error_hint: ${errorHintMsg.toString} "
          Logger.fatal(finalErrorMessage)
          Future.failed(new Exception(finalErrorMessage))
        } else {
          val loginRequest = (res.json).as[OAuth2LoginRequest]
          Future.successful(loginRequest)
        }
      })
  }


  /*
   Api Ref Link : https://www.ory.sh/docs/hydra/reference/api#tag/oAuth2/operation/getOAuth2ConsentRequest
  */
  def getConsentRequest(
                         consentChallenge: String,
                         consentRequestUrl: String
                       )(
                         implicit ws: WSClient,
                         ec: ExecutionContext,
                         Logger: ISRLogger
                       ): Future[OAuth2ConsentRequest] = {

    ws.url(consentRequestUrl)
      .withQueryStringParameters(
        "consent_challenge" -> consentChallenge
      )
      .get()
      .flatMap(res => {
        if (res.status != 200) {
          val errorMsg = (res.json \ "error").asOpt[String]
          val errorDescriptionMsg = (res.json \ "error_description").asOpt[String]
          val errorHintMsg = (res.json \ "error_hint").asOpt[String]
          val finalErrorMessage = s"CommonAuthApi acceptLogin ::: Error Occurred acceptLogin :: Error: ${errorMsg.toString} Error_Descrption: ${errorDescriptionMsg.toString}  Error_hint: ${errorHintMsg.toString} "
          Logger.fatal(finalErrorMessage)
          Future.failed(new Exception(finalErrorMessage))
        } else {
          Logger.info(s"Raw response ${res.json.toString()}")
          val consentRequest = (res.json).as[OAuth2ConsentRequest]
          Future.successful(consentRequest)
        }
      })

  }

  /*
   Api Ref Link: https://www.ory.sh/docs/hydra/reference/api#tag/oAuth2/operation/acceptOAuth2ConsentRequest
   */
  def acceptConsentRequest(
                            consentChallenge: String,
                            consentAcceptUrl: String,
                            remember: Boolean,
                            grantedScopes: List[String]
                          )(
                            implicit ws: WSClient,
                            ec: ExecutionContext,
                            Logger: ISRLogger
                          ): Future[String] = {

    ws.url(consentAcceptUrl)
      .withQueryStringParameters(
        "consent_challenge" -> consentChallenge
      )
      .put(Json.obj(
        "grant_scope" -> grantedScopes,
        "handled_at" -> DateTime.now().toString,
        "remember" -> remember,
        "remember_for" -> AppConfigCommonAuth.remember_for_duration_in_seconds
      ))
      .flatMap(res => {
        if (res.status != 200) {
          val errorMsg = (res.json \ "error").asOpt[String]
          val errorDescriptionMsg = (res.json \ "error_description").asOpt[String]
          val errorHintMsg = (res.json \ "error_hint").asOpt[String]
          val finalErrorMessage = s"CommonAuthApi acceptLogin ::: Error Occurred acceptLogin :: Error: ${errorMsg.toString} Error_Descrption: ${errorDescriptionMsg.toString}  Error_hint: ${errorHintMsg.toString} "
          Logger.fatal(finalErrorMessage)
          Future.failed(new Exception(finalErrorMessage))
        } else {
          val redirect_to = (res.json \ "redirect_to").as[String]
          Future.successful(redirect_to)
        }
      })

  }

  /*
   Api Ref Link : https://www.ory.sh/docs/hydra/reference/api#tag/oAuth2/operation/rejectOAuth2ConsentRequest
   */
  def rejectConsentRequest(
                            rejectConsentRequest: RejectConsentRequest,
                            consentRejectUrl: String
                          )(
                            implicit ws: WSClient,
                            ec: ExecutionContext,
                            Logger: ISRLogger
                          ): Future[String] = {

    ws.url(consentRejectUrl)
      .withQueryStringParameters(
        "consent_challenge" -> rejectConsentRequest.consent_challenge.get // Using .get because this is called only when consent_challenge is present
      )
      .put(Json.obj(
        "error" -> "request_denied",
        "error_description" -> rejectConsentRequest.error_description,
        "status_code" -> 400,
      ))
      .flatMap(res => {
        if (res.status != 200) {
          val errorMsg = (res.json \ "error").asOpt[String]
          val errorDescriptionMsg = (res.json \ "error_description").asOpt[String]
          val errorHintMsg = (res.json \ "error_hint").asOpt[String]
          val finalErrorMessage = s"CommonAuthApi acceptLogin ::: Error Occurred acceptLogin :: Error: ${errorMsg.toString} Error_Descrption: ${errorDescriptionMsg.toString}  Error_hint: ${errorHintMsg.toString} "
          Logger.fatal(finalErrorMessage)
          Future.failed(new Exception(finalErrorMessage))
        } else {
          val redirect_to = (res.json \ "redirect_to").as[String]
          Future.successful(redirect_to)
        }
      })

  }


  /*
   Api Ref Link : https://www.ory.sh/docs/hydra/reference/api#tag/oAuth2/operation/oauth2TokenExchange
   */
  def getAccessToken(
                      commonOauthData: CommonOauthData,
                      authCodeExchangeUrl: String,
                      clientId:String,
                      clientSecret:String
                    )(
    implicit ws: WSClient,
    ec: ExecutionContext,
    Logger: ISRLogger
  ): Future[AccessTokenExchangeResponse] = {


    val data = Map(
      "code" -> commonOauthData.code,
      "client_id" -> clientId,
      "grant_type" -> "authorization_code",
      "client_secret" -> clientSecret,
      "scope" -> "profile"
    )

    val encodedData = data.map { case (key, value) =>
      s"${URLEncoder.encode(key, "UTF-8")}=${URLEncoder.encode(value, "UTF-8")}"
    }.mkString("&")

    ws.url(authCodeExchangeUrl)
      .withHttpHeaders(
        "Content-type" -> "application/x-www-form-urlencoded"
      )
      .post(encodedData)
      .flatMap(res => {
        if (res.status != 200) {
          val errorMsg = (res.json \ "error").asOpt[String]
          val errorDescriptionMsg = (res.json \ "error_description").asOpt[String]
          val errorHintMsg = (res.json \ "error_hint").asOpt[String]
          val finalErrorMessage = s"CommonAuthApi acceptLogin ::: Error Occurred acceptLogin :: Error: ${errorMsg.toString} Error_Descrption: ${errorDescriptionMsg.toString}  Error_hint: ${errorHintMsg.toString} "
          Logger.fatal(finalErrorMessage)
          Future.failed(new Exception(finalErrorMessage))
        } else {
          Try {
            (res.json).as[AccessTokenExchangeResponse]
          } match {

            case Failure(e) => Future.failed(e)

            case Success(value) => {
              Future.successful(value)
            }
          }
        }
      })

  }


  /*
    This is the auth/me end point of Common Auth Controller
   */
  def getProfile(access_token: String, scope: String, state: String, smartreachProfileUrl: String)(
    implicit ws: WSClient,
    ec: ExecutionContext,
    Logger: ISRLogger
  ): Future[CommonAuthAccount] = {

    ws.url(smartreachProfileUrl)
      .withHttpHeaders(
        "Content-type" -> "application/json"
      )
      .post(Json.obj(
        "access_token" -> access_token,
        "scope" -> scope,
        "state" -> state
      ))
      .flatMap(res => {
        if (res.status != 200) {

          Logger.fatal("Error occurred: Profile not Found")
          Future.failed(new Exception("Profile not found"))
        } else {

          Try {
            (res.json \ "data" \ "account").as[CommonAuthAccount]
          } match {

            case Failure(e) => {
              Logger.fatal(s"Error Occurred getProfile :: ${e.getMessage}")
              Future.failed(e)
            }

            case Success(value) => Future.successful(value)
          }
        }
      })


  }

  /*Api Ref Link: https://www.ory.sh/docs/hydra/reference/api#tag/oAuth2/operation/oauth2TokenExchange */
  def refreshAccessToken(
                          refreshTokenUrl: String,
                          refresh_token: String,
                          scope: String,
                          client_id: String,
                          client_secret: String
                        )(
                          implicit ws: WSClient,
                          ec: ExecutionContext,
                          Logger: ISRLogger
                        ): Future[NewRefreshedAccessToken] = {


    val data = Map(
      "refresh_token" -> refresh_token,
      "scope" -> scope,
      "client_id" -> client_id,
      "client_secret" -> client_secret,
      "grant_type" -> "refresh_token"
    )
    val encodedData = data.map { case (key, value) => {
      s"${URLEncoder.encode(key, "UTF-8")}=${URLEncoder.encode(value, "UTF-8")}"
    }
    }.mkString("&")


    ws.url(refreshTokenUrl)
      .addHttpHeaders("Content-Type" -> "application/x-www-form-urlencoded")
      .post(encodedData)
      .flatMap(res => {
        if (res.status != 200) {
          val errorMsg = (res.json \ "error").asOpt[String]
          val errorDescriptionMsg = (res.json \ "error_description").asOpt[String]
          val errorHintMsg = (res.json \ "error_hint").asOpt[String]
          val finalErrorMessage = s"CommonAuthApi acceptLogin ::: Error Occurred acceptLogin :: Error: ${errorMsg.toString} Error_Descrption: ${errorDescriptionMsg.toString}  Error_hint: ${errorHintMsg.toString} "
          Logger.fatal(finalErrorMessage)
          Future.failed(new Exception(finalErrorMessage))
        } else {
          Try {
            res.json.as[NewRefreshedAccessToken]
          } match {
            case Failure(e) =>
              Logger.fatal(s"[OAuthApi] refreshAccessToken :: Json validation error occurred :: ${e.getMessage}")
              Future.failed(e)

            case Success(value) =>
              Future.successful(value)
          }
        }
      })
  }

  /*Api Ref Link : https://www.ory.sh/docs/hydra/reference/api#tag/oAuth2/operation/revokeOAuth2LoginSessions*/
  def revokeBySubject(
                       revokeSessionUrl: String
                     )(
                       implicit ws: WSClient,
                       ec: ExecutionContext,
                       Logger: ISRLogger
                     ): Future[String] = {

    ws.url(revokeSessionUrl).delete()
      .flatMap(res => {
        if (res.status != 204) {
          val errorMsg = (res.json \ "error").asOpt[String]
          val errorDescriptionMsg = (res.json \ "error_description").asOpt[String]
          val errorHintMsg = (res.json \ "error_hint").asOpt[String]
          val finalErrorMessage = s"CommonAuthApi acceptLogin ::: Error Occurred acceptLogin :: Error: ${errorMsg.toString} Error_Descrption: ${errorDescriptionMsg.toString}  Error_hint: ${errorHintMsg.toString} "
          Logger.fatal(finalErrorMessage)
          Future.failed(new Exception(finalErrorMessage))
        } else {
          Logger.info(s"Authentication sessions revoked successfully")
          Future.successful("Authentication sessions revoked successfully")
        }
      })
  }


  def checkIfUserIsLoggedIn(sessionAccountId: Long, clientId: String, clientSecret: String)(
    implicit ws: WSClient,
    ec: ExecutionContext,
    Logger: ISRLogger
  ): Future[Boolean] = {
    ws.url(AppConfigCommonAuth.idApiBaseUrl + "/api/v1/oauth/user_logged_in_common_auth")
      .withHttpHeaders(
        AppConfigCommonAuth.clientIdKeyName -> clientId,
        AppConfigCommonAuth.clientIdKeySecretKeyName -> clientSecret
      )
      .post(Json.obj(
        "account_id" -> sessionAccountId
      ))
      .map(res => {
        Try {
          (res.json \ "data" \ "is_logged_in").as[Boolean]
        } match {
          case Failure(exception) => {
            Logger.fatal(s"Validation Error Occurred, Sending is Logged in as false :: ${exception.getMessage}")
            false
          }
          case Success(value) => value
        }
      })

  }


  def logoutUserFromCommonAuth(accessToken: String, clientId: String, clientSecret: String)(
    implicit ws: WSClient,
    ec: ExecutionContext,
    Logger: ISRLogger
  ): Future[Boolean] = {

    val url = AppConfigCommonAuth.idApiBaseUrl + "/api/v1/oauth/logout_common_auth"
    ws.url(url)
      .withHttpHeaders(
        "AUTHORIZATION" -> accessToken,
        AppConfigCommonAuth.clientIdKeyName -> clientId,
        AppConfigCommonAuth.clientIdKeySecretKeyName -> clientSecret
      )
      .post(Json.obj())
      .map(res => {
        if (res.status == 200) {
          Logger.info("Logged out user from Common Auth")
          true
        } else {
          false
        }

      }).recover { case e =>
      Logger.fatal(s"CommonAuthApi.logoutUserFromCommonAuth :: An Error Occurred while trying to logout user :: ${e.getMessage}")
      throw e
    }


  }


  def setUserLoggedIn(accessToken: String,clientId: String, clientSecret: String)(
    implicit ws: WSClient,
    ec: ExecutionContext,
    Logger: ISRLogger
  ): Future[Boolean] = {
    ws.url(AppConfigCommonAuth.idApiBaseUrl + "/api/v1/oauth/set_user_logged_in")
      .withHttpHeaders(
        "AUTHORIZATION" -> accessToken,
        AppConfigCommonAuth.clientIdKeyName -> clientId,
        AppConfigCommonAuth.clientIdKeySecretKeyName -> clientSecret
      )
      .post(Json.obj())
      .flatMap(res =>{
        if(res.status != 200){
          Logger.fatal("CommonAuthApi.setUserLoggedIn Failed to set user Loggedin redis Key")
          Future.failed(new Exception("Failed to set user Loggedin redis Key"))
        }else{
          Future.successful(true)
        }
      })
  }
}