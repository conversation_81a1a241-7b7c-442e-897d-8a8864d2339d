package common_auth_middleware

import api.{AppConfig, SRAPIResponse}
import play.api.mvc.{ActionBuilder, ActionTransformer, AnyContent, BodyParsers, Request, WrappedRequest}
import play.api.routing.Router
import api.middleware.CommonAuthLogger

import scala.concurrent.{ExecutionContext, Future}
import api.middleware.Attrs



class CommonAuthLoggingAction(val parser: BodyParsers.Default)(implicit val executionContext: ExecutionContext)
  extends ActionBuilder[CommonAuthLoggingRequest, AnyContent]
    with ActionTransformer[Request, CommonAuthLoggingRequest] {

  def transform[A](request: Request[A]) = Future.successful {

    val logRequestId = {


      val logTraceId: String = request.attrs(Attrs.LOG_TRACE_ID_KEY)

      val handler = request.attrs(Router.Attrs.HandlerDef)

      s"$logTraceId :: ${handler.controller}.${handler.method} :: uri: ${request.method} ${request.uri} :: "
    }



    val Logger = new CommonAuthLogger(logRequestId = logRequestId)
    val Res = new SRAPIResponse(Logger = Logger)

    CommonAuthLoggingRequest(
      Logger = Logger,
      Response = Res,
      request = request
    )
  }

}













