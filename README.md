


#### Create a user for rabbitMq:

/usr/local/sbin/rabbitmqctl add_user heaplabs heaplabs

/usr/local/sbin/rabbitmqctl add_vhost coldemail


#### worker local

java -cp target/scala-2.11/coldemail.jar utils.App worker


#### scheduler local

java -cp target/scala-2.11/coldemail.jar utils.App worker


#### worker ec2

java -Dconfig.file=conf/production.conf -Dlogger.resource=production-logback.xml -cp target/scala-2.11/coldemail.jar utils.App worker

#### scheduler ec2

java -Dconfig.file=conf/production.conf -Dlogger.resource=production-logback.xml -cp target/scala-2.11/coldemail.jar utils.App scheduler


#### install sbt on ubuntu:

http://stackoverflow.com/a/36607900

echo "deb https://dl.bintray.com/sbt/debian /" | sudo tee -a /etc/apt/sources.list.d/sbt.list
sudo apt-key adv --keyserver hkp://keyserver.ubuntu.com:80 --recv 642AC823
sudo apt-get update
sudo apt-get install sbt



#### install rabbitmq on ubuntu:

UPDATE: http://linoxide.com/ubuntu-how-to/install-setup-rabbitmq-ubuntu-16-04/

http://stackoverflow.com/a/22195831

echo "deb http://www.rabbitmq.com/debian/ testing main"  | sudo tee  /etc/apt/sources.list.d/rabbitmq.list > /dev/null
wget https://www.rabbitmq.com/rabbitmq-signing-key-public.asc
sudo apt-key add rabbitmq-signing-key-public.asc
sudo apt-get update
sudo apt-get install rabbitmq-server -y
sudo service rabbitmq-server start
sudo rabbitmq-plugins enable rabbitmq_management
sudo service rabbitmq-server restart


#### other commands

ps -ewf | grep java




# Scheduler deploy checklist

Sometimes, scheduled emails are in mq, but not sent, and during the restart they get lost.
Need to delete those from the db, also revert the current_step_id in campaigns_prospects.
```sql

-- NOTE: THIS IS NOT ENOUGH. ALSO REVERT the current_step_id for these campaigns_prospects
delete from emails_scheduled where not sent and pushed_to_rabbitmq;
```

# scheduler crontab
second one from: https://github.com/sbt/sbt/issues/1913#issuecomment-78397581
```shell
0 */3 * * * . $HOME/.profile; bash -l -c 'sh /home/<USER>/coldemail/restart_workers_cron.sh'

*/5 * * * * /bin/rm /tmp/sbt*.log
```


# IMPORTANT ON SCHEDULER UPDATE

better MARK AS SENT

delete from emails_scheduled where not sent and pushed_to_rabbitmq and pushed_to_rabbitmq_at < now() - interval '1 day';




--- ON PUSH ----
--- THESE STEPS MUST BE CARRIED OUT ------
--- UPDATED 24th April 2018 -------
---------------------
---------------------
---------------------


-- 1.
select now() - pushed_to_rabbitmq_at, a.email, a.first_name, * from emails_scheduled e join prospects p on p.id = e.prospect_id
join accounts a on a.id = p.account_id where not sent AND pushed_to_rabbitmq;

-- 2.
select es.email, e.scheduled_at - now(), e.scheduled_at - e.added_at, e.* from emails_scheduled e
join email_settings es on es.id = e.sender_email_settings_id

where not sent ;

-- stop scheduler: read email cron, push to rabbitmq cron


-- 3.
select from_email,  sent_at, id, sender_email_settings_id from emails_scheduled where sent
and sent_at > now() - interval '200 minutes'
--and from_email = '<EMAIL>'                                                   

order by sent_at desc;

--4.
select e.campaign_id from emails_scheduled e where not sent group by campaign_id;

-- 5. NOTE: nothing should be in rabbitmq, step 1 should show empty results
with t As (select e.campaign_id from emails_scheduled e where not sent group by campaign_id order by campaign_id)
select string_agg(t.campaign_id::character varying
, ', ') from t;


--6.
select * from email_settings where in_queue_for_reply_tracking or in_queue_for_scheduling;

--7.
update email_settings set
last_read_for_replies = last_read_for_replies - interval '5 days',
in_queue_for_reply_tracking = false,
in_queue_for_scheduling = false;
where id between 450 and 500;

where account_id != 184
returning *;
--where last_read_for_replies > now() - interval '1005 hours' and last_read_for_replies < now() - interval '55 hours'

--where last_read_for_replies < now() - interval '35 hours' and last_read_for_replies > now() - interval '45 hours'
;


------ ON PUSH ------




# EMAIL SCHEDULER ENTRY POINTS

ReadEmailCronService.execute -> MQReplyTracker -> MQEmailSchedulerV2 -> ScheduleEmailCronServiceV2.scheduleEmailAccount

ScheduleEmailCronServiceV2.execute -> ScheduleEmailCronServiceV2.scheduleAllActiveEmailAccounts -> MQEmailSchedulerV2 -> ScheduleEmailCronServiceV2.scheduleEmailAccount






# start prod app

nohup java -Dconfig.file=conf/production.conf -Dpidfile.path=/dev/null -Dlogger.resource=production-logback.xml -Dhttp.port=5000 -jar target/scala-2.11/coldemail.jar &



# nginx config

sudo apt-get install nginx -y


```

server {
        listen 80;
        server_name app.smartreach.io;
        return 301 https://app.smartreach.io$request_uri;
}


server {
        listen 443;
        server_name app.smartreach.io;

        ssl on;
        # Use certificate and key provided by Let's Encrypt:
        ssl_certificate /etc/letsencrypt/live/app.smartreach.io/fullchain.pem;
        ssl_certificate_key /etc/letsencrypt/live/app.smartreach.io/privkey.pem;
        ssl_session_timeout 5m;
        ssl_protocols TLSv1 TLSv1.1 TLSv1.2;
        ssl_prefer_server_ciphers on;
        ssl_ciphers 'EECDH+AESGCM:EDH+AESGCM:AES256+EECDH:AES256+EDH';

         # Pass requests for / to localhost:3001:
        location / {
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-NginX-Proxy true;


                proxy_pass http://localhost:3001/;


                proxy_ssl_session_reuse off;
                proxy_set_header Host $http_host;
                proxy_cache_bypass $http_upgrade;
                proxy_redirect off;
        }
}

#######################


server {
        listen 80;
        server_name smartreach.io www.smartreach.io;
        return 301 https://smartreach.io$request_uri;

}


server {
        listen 443;
        server_name www.smartreach.io;

        ssl on;
        # Use certificate and key provided by Let's Encrypt:
        ssl_certificate /etc/letsencrypt/live/smartreach.io/fullchain.pem;
        ssl_certificate_key /etc/letsencrypt/live/smartreach.io/privkey.pem;
        ssl_session_timeout 5m;
        ssl_protocols TLSv1 TLSv1.1 TLSv1.2;
        ssl_prefer_server_ciphers on;
        ssl_ciphers 'EECDH+AESGCM:EDH+AESGCM:AES256+EECDH:AES256+EDH';

        return 301 https://smartreach.io$request_uri;
}


server {
        listen 443;
        server_name smartreach.io;

        ssl on;
        # Use certificate and key provided by Let's Encrypt:
        ssl_certificate /etc/letsencrypt/live/smartreach.io/fullchain.pem;
        ssl_certificate_key /etc/letsencrypt/live/smartreach.io/privkey.pem;
        ssl_session_timeout 5m;
        ssl_protocols TLSv1 TLSv1.1 TLSv1.2;
        ssl_prefer_server_ciphers on;

        ssl_ciphers 'EECDH+AESGCM:EDH+AESGCM:AES256+EECDH:AES256+EDH';

        return 301 https://smartreach.io$request_uri;
}


server {
        listen 443;
        server_name smartreach.io;

        ssl on;
        # Use certificate and key provided by Let's Encrypt:
        ssl_certificate /etc/letsencrypt/live/smartreach.io/fullchain.pem;
        ssl_certificate_key /etc/letsencrypt/live/smartreach.io/privkey.pem;
        ssl_session_timeout 5m;
        ssl_protocols TLSv1 TLSv1.1 TLSv1.2;
        ssl_prefer_server_ciphers on;
        ssl_ciphers 'EECDH+AESGCM:EDH+AESGCM:AES256+EECDH:AES256+EDH';

         # Pass requests for / to localhost:3000:
        location / {
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-NginX-Proxy true;
                proxy_set_header X-Forwarded-Proto $scheme;


                proxy_pass http://localhost:3000/;


                proxy_ssl_session_reuse off;
                proxy_set_header Host $http_host;
                proxy_cache_bypass $http_upgrade;
                proxy_redirect off;
        }
}


### smartreach prod ec2 api server: april 2018


server {
        listen 80;
        server_name api.smartreach.io;
        return 301 https://api.smartreach.io$request_uri;
}

# HTTPS - proxy requests on to local scala  app:
server {
        listen 443;
        server_name api.smartreach.io;

        ssl on;
        # Use certificate and key provided by Let's Encrypt:
        ssl_certificate /etc/letsencrypt/live/api.smartreach.io/fullchain.pem;
        ssl_certificate_key /etc/letsencrypt/live/api.smartreach.io/privkey.pem;
        ssl_session_timeout 5m;
        ssl_protocols TLSv1 TLSv1.1 TLSv1.2;
        ssl_prefer_server_ciphers on;
        ssl_ciphers 'EECDH+AESGCM:EDH+AESGCM:AES256+EECDH:AES256+EDH';

         # Pass requests for / to localhost:5000:
        location / {
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-NginX-Proxy true;
                proxy_set_header X-Forwarded-Proto $scheme;


                proxy_pass http://localhost:5000;


                proxy_ssl_session_reuse off;
                proxy_set_header Host $http_host;
                proxy_cache_bypass $http_upgrade;
                proxy_redirect off;
        }
}




```





# Create SSL Certificate With Lets Encrypt

Reference: https://certbot.eff.org/all-instructions/#ubuntu-16-04-xenial-nginx

```

sudo apt-get update -y
sudo apt-get install letsencrypt -y
sudo letsencrypt certonly --standalone -d api.smartreach.io
sudo chown ubuntu -R /etc/letsencrypt/
sudo chown ubuntu -R /var/log/letsencrypt/

```

## Configure lets encrypt for auto renewing the SSL certificate
REF: https://www.digitalocean.com/community/tutorials/how-to-secure-nginx-with-let-s-encrypt-on-ubuntu-16-04#step-6-set-up-auto-renewal

add the following to crontab:
```

crontab -e

# run job to try letsencrypt ssl certificate auto renewal every week

30 2 * * 1 /usr/bin/letsencrypt renew >> /var/log/le-renew.log
35 2 * * 1 /bin/systemctl reload nginx
```
## Configure lets encrypt for wildcard domain (Ex: *.smartreachmail.com)
REF: https://medium.com/@saurabh6790/generate-wildcard-ssl-certificate-using-lets-encrypt-certbot-273e432794d7

Run the following to the terminal
```

./certbot-auto certonly  --manual --preferred-challenges=dns --email <EMAIL> --server https://acme-v02.api.letsencrypt.org/directory --agree-tos -d *.smartreachmail.com -d smartreachmail.com

```
For wildcard certificates, the only challenge method Let’s Encrypt accepts is the DNS challenge, which we can invoke via the preferred-challenges=dns flag

After executing the above command, the Certbot will share a text record to add to your DNS.


## Configure renew the wildcard certificate manually

run the command in terminal manually and choose nginx authentication
```

./certbot-auto certonly --force-renew -d *.smartreachmail.com -d smartreachmail.com


```


## Unused (useless) db tables / columns: these should be removed
- emails_received full table
- organizations, these columns:
-- REMOVED last_payment_on
-- REMOVED stripe_plan_name
-- REMOVED subscription_order_obj
-- REMOVED last_payment_obj

- campaigns_prospects table:
-- status
-- IGNORING FOR NOW paused (paused and completed are redundant columns)
- prospects
-- REMOVED prospect_category
-- REMOVED in_blacklist
-- INGORING FOR NOW list
- prospect_events
-- old_category

-- prospect_categories
- DROP TABLE

DROP indexes:
- sr_prospects_accountid_teamid_list_null
- sr_prospects_list
- sr_prospects_list_null_btree
- sr_prospects_list_trgm

#Ref:
#https://developerinsider.co/how-to-create-and-auto-renew-lets-encrypt-wildcard-certificate/
Letsencrypt Wildcard renew  commands

export LC_ALL="en_US.UTF-8"
export LC_CTYPE="en_US.UTF-8"

./certbot-auto renew --pre-hook "service nginx stop" --post-hook "service nginx start"





=====

# setup dedicated server gcp
create gcp account with the given domain

enable Gmail API and People API in the marketplace

setup credentials -> Oauth consent screen


=====

## INSTALL FILEBEAT

mkdir /home/<USER>/srlogs/

touch /home/<USER>/srlogs/sr_app.log

wget -qO - https://artifacts.elastic.co/GPG-KEY-elasticsearch | sudo apt-key add -

echo "deb https://artifacts.elastic.co/packages/oss-7.x/apt stable main" | sudo tee -a /etc/apt/sources.list.d/elastic-7.x.list

sudo apt-get install apt-transport-https

sudo apt-get update

sudo apt-get install filebeat

sudo systemctl enable filebeat


#edit filebeat.yml
sudo nano /etc/filebeat/filebeat.yml

sudo filebeat test config
sudo filebeat test output

sudo service filebeat start
sudo service filebeat status


