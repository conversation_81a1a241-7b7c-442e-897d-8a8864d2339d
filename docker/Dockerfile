# Use Ubuntu 22.04 to match the original setup
FROM ubuntu:22.04

# Set environment variables
ENV DEBIAN_FRONTEND=noninteractive
ENV POSTGRES_PASSWORD=postgresql127
ENV POSTGRES_USER=heaplabs
ENV POSTGRES_DB=coldemail_dev1
ENV SCALA_VERSION=3.3.0
ENV SCALA_HOME=/usr/share/scala
ENV PATH="${SCALA_HOME}/bin:/usr/share/sbt/bin:${PATH}"
ENV POSTGRES_HOST_AUTH_METHOD=trust

# Update repositories and install dependencies
RUN apt-get update \
    && apt-get install -y software-properties-common wget curl gnupg \
    && add-apt-repository -y universe \
    && sh -c 'echo "deb http://apt.postgresql.org/pub/repos/apt jammy-pgdg main" > /etc/apt/sources.list.d/pgdg.list' \
    && wget -qO - https://www.postgresql.org/media/keys/ACCC4CF8.asc | apt-key add - \
    && wget -O- https://apt.corretto.aws/corretto.key | apt-key add - \
    && add-apt-repository 'deb https://apt.corretto.aws stable main' \
    && apt-get update \
    && apt-get install -y \
        java-21-amazon-corretto-jdk \
        redis \
        postgresql-14 \
        postgresql-server-dev-14 \
        supervisor \
        git \
        make \
        gcc \
        build-essential \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install Node.js (LTS) & npm
RUN curl -fsSL https://deb.nodesource.com/setup_lts.x | bash - && \
    apt-get install -y nodejs && \
    npm install -g npm@latest

# Verify Node.js and npm installation
RUN node -v && npm -v

# Install pg_partman extension
RUN git clone https://github.com/pgpartman/pg_partman.git && \
    cd pg_partman && \
    git checkout v4.5.1 && \
    make && \
    make install && \
    rm -rf pg_partman

# Copy pg_hba.conf into the container
COPY pg_hba.conf /etc/postgresql/14/main/pg_hba.conf

# Install Scala 3
RUN wget https://github.com/lampepfl/dotty/releases/download/${SCALA_VERSION}/scala3-${SCALA_VERSION}.tar.gz && \
    mkdir -p ${SCALA_HOME} && \
    tar -xvzf scala3-${SCALA_VERSION}.tar.gz -C ${SCALA_HOME} --strip-components=1 && \
    rm scala3-${SCALA_VERSION}.tar.gz

# Install sbt
RUN echo "deb https://repo.scala-sbt.org/scalasbt/debian all main" | tee /etc/apt/sources.list.d/sbt.list && \
    echo "deb https://repo.scala-sbt.org/scalasbt/debian /" | tee /etc/apt/sources.list.d/sbt_old.list && \
    curl -sL "https://keyserver.ubuntu.com/pks/lookup?op=get&search=0x99E82A75642AC823" | apt-key add && \
    apt-get update && apt-get install -y sbt

# Configure Git Safe Directory to avoid ownership issues
RUN git config --global --add safe.directory /workspace

# Expose ports for Redis, PostgreSQL, and Node.js (if needed)
EXPOSE 5432 7777 3000

# Copy entrypoint.sh from the root folder (not docker/ folder)
COPY entrypoint.sh /usr/local/bin/entrypoint.sh
RUN chmod +x /usr/local/bin/entrypoint.sh

# Set entrypoint
ENTRYPOINT ["/usr/local/bin/entrypoint.sh"]
