@startuml

participant BillingFrontend


box "Billing Backend"

participant StripBillingController


participant SmartReachWebhookProxyService

participant StripeWebhookHandlerService


participant PaymentGatewayService

participant AffiliateCommissionDAO

participant ProductWebhookService


end box

box "Smartreach Backend"

participant BillingV2Controller
participant SubscriptionService

participant AffiliateTrackingService

participant AffiliateDAO

end box



BillingFrontend -> StripBillingController: POST /api/v1/billing/stripe/webhook

' BillingBackend start

StripBillingController -> SmartReachWebhookProxyService: proxyWebhookRequest

SmartReachWebhookProxyService -> StripeWebhookHandlerService: handleWebhookNewFlow

StripeWebhookHandlerService -> PaymentGatewayService: handlePaymentReceivedWebhook

PaymentGatewayService -> AffiliateCommissionDAO: create

PaymentGatewayService <-- AffiliateCommissionDAO: affiliate_commission_id

PaymentGatewayService -> ProductWebhookService: sendPaymentReceivedEvent

ProductWebhookService -> ProductWebhookService: sendEventData

ProductWebhookService -> BillingV2Controller: POST   /api/v2/billing/webhook

' BillingBackend end

' SmartreachBackend start

BillingV2Controller -> SubscriptionService: handleBillingWebhook


SubscriptionService -> AffiliateTrackingService: updateReferralCouponStatusOnPayment

AffiliateTrackingService -> AffiliateDAO: updateReferralCouponStatusOnPayment

'SubscriptionService -> AffiliateTrackingService: trackAffiliateEvent
'
'AffiliateTrackingService -> AffiliateTrackingService: getAffiliateTrackerAndOrgDetails
'
'note right
'    Don't send payment info to FirstPromoter API,
'    we will send it when commission is approved from CSD.
'end note
'AffiliateTrackingService -> FirstPromoter: trackAffiliateEvent

' Currently in FirstPromoter, we are only sending cancel Event.


' SmartreachBackend end


@enduml
