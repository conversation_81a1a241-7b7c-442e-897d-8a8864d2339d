@startuml

participant SupportFrontend


box "Support Backend"

participant AffiliateTrackingController

participant AffiliateTrackingService

participant InternalApiCallService

end box

box "Smartreach Backend"

participant InternalSupportController

participant SRAffiliateTrackingService

participant AffiliateDAO

end box



SupportFrontend -> AffiliateTrackingController: PUT /api/v1/support/referral/coupon_status \n body: {orgId: number, approve_coupon: boolean} \n cookies


AffiliateTrackingController -> AffiliateTrackingService: updateCouponStatus

AffiliateTrackingService -> InternalApiCallService: updateCouponStatus

InternalApiCallService -> InternalSupportController: PUT /api/v2/internal_support/referral/coupon_status \n body: {orgId: number, approve_coupon: boolean} \n apiKey



InternalSupportController -> SRAffiliateTrackingService: updateCouponStatus

SRAffiliateTrackingService -> AffiliateDAO: updateCouponStatus

SRAffiliateTrackingService <-- AffiliateDAO: status

InternalSupportController <-- SRAffiliateTrackingService: status

InternalApiCallService <-- InternalSupportController: status

AffiliateTrackingService <-- InternalApiCallService: status

AffiliateTrackingController <-- AffiliateTrackingService:status

SupportFrontend <-- AffiliateTrackingController: status




@enduml
