@startuml

participant SmartreachFrontend


box "Smartreach Backend"

participant ReferralController
participant AffiliateTrackingService
participant FirstPromoter
participant FirstPromoterDAO

end box


SmartreachFrontend -> ReferralController: POST /api/v1/firstpromoter/create_account \n body: {email, first_name, last_name} \n cookies

ReferralController -> AffiliateTrackingService: createFirstPromoterAccount

AffiliateTrackingService -> FirstPromoter: createAccount

FirstPromoter -> FistPromoterAPI: POST /api/v1/promoters/create \n body: {email, first_name, last_name} \n apiKey

FirstPromoter <-- FistPromoterAPI: PromoterAccountInfo

AffiliateTrackingService <-- FirstPromoter: PromoterAccountInfo

AffiliateTrackingService -> FirstPromoterDAO: updatePromoterInfo

AffiliateTrackingService <-- FirstPromoterDAO: auth_token


ReferralController <-- AffiliateTrackingService: auth_token

SmartreachFrontend <-- ReferralController: auth_token


@enduml
