@startuml


participant SupportFrontend


box "Support Backend"

participant ReportController
participant ReportService
participant InternalAPICallService

end box


box "Smartreach Backend"

participant InternalSupportController
participant InternalCSDReportService
participant InternalCSDReportDAO

end box

SupportFrontend -> ReportController: POST /api/v1/support/reports/acquisition_report \n body: {startDate, endDate} \n cookies

ReportController -> ReportService: getAcquisitionReport

ReportService -> InternalAPICallService: getAcquisitionReport

InternalAPICallService -> InternalSupportController: POST /api/v2/internal_support/reports/acquisition_report \n body: {startDate, endDate} \n "X-API-KEY" -> apiKey


InternalSupportController -> InternalCSDReportService: getCustomerAcquisitionReport

InternalCSDReportService -> InternalCSDReportDAO: getCustomerAcquisitionReport
InternalCSDReportService <-- InternalCSDReportDAO: CustomerAcquisitionReport

InternalSupportController <-- InternalCSDReportService: CustomerAcquisitionReport

InternalAPICallService <-- InternalSupportController: CustomerAcquisitionReport

ReportService <-- InternalAPICallService: CustomerAcquisitionReport

ReportController <-- ReportService: CustomerAcquisitionReport

SupportFrontend <-- ReportController: CustomerAcquisitionReport


@enduml