@startuml

participant SupportFrontend


box "Support Backend"

participant ReportController
participant ReportService
participant InternalAPICallService

end box


box "Smartreach Backend"

participant InternalSupportController
participant InternalCSDReportService
participant InternalCSDReportDAO

end box


SupportFrontend -> ReportController: POST /api/v1/support/reports/update_acquisition_report \n body: {id, users, new_users, demo_delivered, good_fit} \n cookies

ReportController -> ReportService: updateAcquisitionReport

ReportService -> InternalAPICallService: updateAcquisitionReport

InternalAPICallService -> InternalSupportController: POST /api/v2/internal_support/reports/update_acquisition_report \n body: {id, users, new_users, demo_delivered, good_fit} \n "X-API-KEY" -> apiKey


InternalSupportController -> InternalCSDReportService: updateAcquisitionReport

InternalCSDReportService -> InternalCSDReportDAO: updateAcquisitionReport

InternalCSDReportService <-- InternalCSDReportDAO: updateCount

InternalSupportController <-- InternalCSDReportService: updateCount

InternalAPICallService <-- InternalSupportController: status

ReportService <-- InternalAPICallService: status

ReportController <-- ReportService: status

SupportFrontend <-- ReportController: status


@enduml