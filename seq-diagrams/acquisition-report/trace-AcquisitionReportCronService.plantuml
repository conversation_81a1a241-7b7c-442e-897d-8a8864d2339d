@startuml


box "AcquisitionReportCronService"

participant executeCron
participant scheduleUpdateAcquisitionReport

end box

participant InternalCSDReportDao

box "BillingAppService"

participant BillingAppService
participant getCustomerAcquisitionBillingData

end box


executeCron                     -> executeCron : executeCron
executeCron                     -> scheduleUpdateAcquisitionReport : scheduleUpdate<PERSON><PERSON><PERSON><PERSON>eport

scheduleUpdateAcquisitionReport -> InternalCSDReportDao         : getCustomerAcquisitionData
scheduleUpdateAcquisitionReport <-- InternalCSDReportDao         : CustomerAcquisitionSRData

scheduleUpdateAcquisitionReport -> BillingAppService            : getCustomerAcquisitionBillingData

getCustomerAcquisitionBillingData -> BillingBackend : POST /api/v2/smartreach/billing/acquisition_data \n headers: {"X-API-KEY" -> apiKey} \n body: {start_date, end_date}
getCustomerAcquisitionBillingData <-- BillingBackend : Response - CustomerAcquisitionBillingData

scheduleUpdateAcquisitionReport <-- BillingAppService            : CustomerAcquisitionBillingData

scheduleUpdateAcquisitionReport -> InternalCSDReportDao         : updateCustomerAcquisitionReport

@enduml



