@startuml

entity "acquisition_report" as AcquisitionReport {
  *id : number <<generated>>
  --
  users_count : integer
  new_users_count : integer
  *trial_started_count : integer
  *num_of_agencies : integer
  *num_of_team_size_greater_than_21 : integer
  *num_of_team_size_greater_than_50 : integer
  demo_delivered : integer
  good_fit : integer
  *num_of_new_subs : integer
  *num_of_churn_subs : integer
  *num_of_active_subs : integer
  *new_mrr : integer
  *reactivation_mrr : integer
  *expansion_mrr : integer
  *contraction_mrr : integer
  *churn_mrr : integer
  updated_at timestamptz
  *created_at timestamptz <<DEFAULT now()>>
}

@enduml
