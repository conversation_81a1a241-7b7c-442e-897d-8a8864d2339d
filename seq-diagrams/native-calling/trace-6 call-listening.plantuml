@startuml

actor user1
actor user2
actor prospect1 order 10
user1 -> sr_frontend: called_prospect_1\n+ custom parameter
sr_frontend -> twilio_server: request to call prospect_1 \n+ custom parameter
twilio_server -> call_service : request to take action \n+ custom parameter
group custom_parameter_check [outgoing_call_parameter_received]
   call_service -> twilio_dialer_service: request to create conf and call prospect_1
   twilio_dialer_service -> twilio_server: add prospect_1 as participant ( +91xxx.. ) to conf_1
   twilio_server -> prospect1: call to prospect
   twilio_dialer_service -> call_service: twiml code response (add user-1 to conf_1)
end
call_service -> twilio_server: twiml response
twilio_server -> sr_frontend : call_connected
sr_frontend -> user1: call connected
user2 -> sr_frontend: wants to listen to call \n + custom parameter
sr_frontend -> twilio_server: request to call anonymous number( doesn't matter )\n + custom parameter
twilio_server -> call_service : request to call anonymous number \n + custom parameter
group custom_parameter_check [listen_parameter_recieved]
   call_service -> twilio_dialer_service: request to join existing conference with mute option
   twilio_dialer_service -> call_service: twiml code response (add user2 to conf_1 mute true)
end
call_service -> twilio_server: add user2 to conf muted (true)
twilio_server -> sr_frontend: added user2 to conf muted (true)
sr_frontend -> user2: call connected where user2 can only listen
@enduml