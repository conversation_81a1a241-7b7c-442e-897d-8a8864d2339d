@startuml
User -> Frontend : Delete number
Frontend -> CallingController : deleteN<PERSON>ber(phone_setting_uuid)
CallingController -> CallingService : deleteNumber(phone_setting_uuid)
Database DB
CallingService -> DB : fetchPhoneSID(phone_Setting_uuid)
DB --> CallingService : (phoneSID)
CallingService -> TwilioService : deleteNumber(phone_sid)
TwilioService --> CallingService : (deleted_phone_number)
CallingService -> DB : deleteNumber(phone_setting_uuid)
DB --> CallingService : (deleted_phone_number)
CallingService --> CallingController : (deleted_phone_number)
CallingController --> Frontend : Success/Error message
@enduml