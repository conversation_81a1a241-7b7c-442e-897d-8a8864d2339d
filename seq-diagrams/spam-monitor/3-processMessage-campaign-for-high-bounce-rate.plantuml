@startuml
MqCampaignSpamMonitorService -> SpamMonitorService : checkIfCampaignAllowedToSendEmail
SpamMonitorService -> CampaignDAO : findCampaignForCampaignUtilsOnly
SpamMonitorService <-- CampaignDAO : Campaign
SpamMonitorService -> EmailScheduledDAO : getSentEmailCountForACampaignForLast24Hours
SpamMonitorService <-- EmailScheduledDAO : List[Boolean] (tells status of each email from campaign i.e sent or not)
SpamMonitorService -> EmailSendingStatusService : updateEmailSendingStatus
EmailSendingStatusService -> EmailSendingStatusDAO : addingEmailSendingStatusTry
SpamMonitorService <-- EmailSendingStatusDAO : Long (id of the created entry in email_send_statuses)
@enduml