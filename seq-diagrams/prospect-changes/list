trace-1-updateEmailValidationDataV2.plantuml -- done
trace-1-updateEmailValidationData.plantuml -- done

trace-1-markAsSentForValidatingEmail.plantuml -- done
trace-2-markAsSentForValidatingEmail.plantuml -- done
trace-3-markAsSentForValidatingEmail.plantuml -- done

trace-1-markProspectAsValidToForceSendEmails.plantuml -- done

trace1-to-insertTrackedRepliesV3.plantuml -- invalid

trace-1-updateProspectEmailHadBouncedEarlier.plantuml -- done

trace-1-to-update-emailvalidation-after-bouncing.plantuml -- done
trace-2-to-update-emailvalidation-after-bouncing.plantuml -- done

trace-1__createFromUploadMultiRowInsert.plantuml -- done
trace-2__createFromUploadMultiRowInsert.plantuml -- done
trace-3__createFromUploadMultiRowInsert.plantuml -- done
trace-4__createFromUploadMultiRowInsert.plantuml -- done

trace-1_insertTrackedRepliesV3.plantuml -- done

trace-1-fetchProspectsV2.plantuml -- done

trace-1-getEmailSendResultForBounceCheck.plantuml -- done

trace-1-getCampaignForBounceCheck.plantuml -- done

trace-1-findFromMasterForValidating.plantuml -- done
trace-2-findFromMasterForValidating.plantuml -- done
trace-3-findFromMasterForValidating.plantuml -- done

trace-1-findProspectIdsMatchingExcludedEmailsAndDNC.plantuml -- done

trace-1-findProspectIdsMatchingBlacklistedEmailsAndDomains.plantuml -- done
trace-2-findProspectIdsMatchingBlacklistedEmailsAndDomains.plantuml -- done

trace-1_getProspectJustSelectSQLSyntax.plantuml -- done
trace-2_getProspectJustSelectSQLSyntax.plantuml -- done

trace-1-sendProspectsForValidation.plantuml -- done
trace-2-sendProspectsForValidation.plantuml -- done
trace-3-sendProspectsForValidation.plantuml -- done

trace-1-filterOwnedProspects.plantuml -- done
trace-2-filterOwnedProspects.plantuml -- done

