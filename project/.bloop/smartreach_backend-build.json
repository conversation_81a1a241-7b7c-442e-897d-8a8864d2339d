{"version": "1.4.0", "project": {"name": "smartreach_backend-build", "directory": "/Users/<USER>/Dev/smartreach_backend/project", "workspaceDir": "/Users/<USER>/Dev/smartreach_backend/project", "sources": ["/Users/<USER>/Dev/smartreach_backend/project/src/main/scala", "/Users/<USER>/Dev/smartreach_backend/project/src/main/scala-2.12", "/Users/<USER>/Dev/smartreach_backend/project/src/main/scala-2", "/Users/<USER>/Dev/smartreach_backend/project/src/main/java", "/Users/<USER>/Dev/smartreach_backend/project/src/main/scala-sbt-1.0", "/Users/<USER>/Dev/smartreach_backend/project/target/scala-2.12/sbt-1.0/src_managed/main", "/Users/<USER>/Dev/smartreach_backend/project/PublishMore.scala"], "dependencies": [], "classpath": ["/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/sbt/1.11.2/sbt-1.11.2.jar", "/Users/<USER>/.sbt/boot/scala-2.12.20/lib/scala-library.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/sbt-assembly_2.12_1.0/2.1.0/sbt-assembly-2.1.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/playframework/sbt-plugin_2.12_1.0/3.0.7/sbt-plugin_2.12_1.0-3.0.7.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/latestbit/sbt-gcs-plugin_2.12_1.0/1.12.0/sbt-gcs-plugin_2.12_1.0-1.12.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/main_2.12/1.11.2/main_2.12-1.11.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/io_2.12/1.10.5/io_2.12-1.10.5.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/jarjarabrams/jarjar-abrams-core_2.12/1.8.1/jarjar-abrams-core_2.12-1.8.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/playframework/sbt-routes-compiler_2.12/3.0.7/sbt-routes-compiler_2.12-3.0.7.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/playframework/play-run-support_2.12/3.0.7/play-run-support_2.12-3.0.7.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/typesafe/config/1.4.3/config-1.4.3.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/slf4j/slf4j-simple/2.0.17/slf4j-simple-2.0.17.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/playframework/play-file-watch_2.12/2.0.0/play-file-watch_2.12-2.0.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/playframework/twirl/sbt-twirl_2.12_1.0/2.0.8/sbt-twirl_2.12_1.0-2.0.8.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/github/sbt/sbt-native-packager_2.12_1.0/1.11.1/sbt-native-packager_2.12_1.0-1.11.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/github/sbt/sbt-web_2.12_1.0/1.5.8/sbt-web_2.12_1.0-1.5.8.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/github/sbt/sbt-js-engine_2.12_1.0/1.3.9/sbt-js-engine_2.12_1.0-1.3.9.jar", "/Users/<USER>/.sbt/boot/scala-2.12.20/lib/scala-compiler.jar", "/Users/<USER>/.sbt/boot/scala-2.12.20/lib/scala-reflect.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/apache/ivy/ivy/2.4.0/ivy-2.4.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/cloud/google-cloud-storage/2.43.2/google-cloud-storage-2.43.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/logic_2.12/1.11.2/logic_2.12-1.11.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/actions_2.12/1.11.2/actions_2.12-1.11.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/main-settings_2.12/1.11.2/main-settings_2.12-1.11.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/run_2.12/1.11.2/run_2.12-1.11.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/command_2.12/1.11.2/command_2.12-1.11.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/collections_2.12/1.11.2/collections_2.12-1.11.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/scripted-plugin_2.12/1.11.2/scripted-plugin_2.12-1.11.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-lm-integration_2.12/1.11.2/zinc-lm-integration_2.12-1.11.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-logging_2.12/1.11.2/util-logging_2.12-1.11.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-xml_2.12/2.3.0/scala-xml_2.12-2.3.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/launcher-interface/1.4.4/launcher-interface-1.4.4.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/github/ben-manes/caffeine/caffeine/2.8.5/caffeine-2.8.5.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/get-coursier/lm-coursier-shaded_2.12/2.1.10/lm-coursier-shaded_2.12-2.1.10.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/apache/logging/log4j/log4j-api/2.17.1/log4j-api-2.17.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/apache/logging/log4j/log4j-core/2.17.1/log4j-core-2.17.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/apache/logging/log4j/log4j-slf4j-impl/2.17.1/log4j-slf4j-impl-2.17.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/librarymanagement-core_2.12/1.11.2/librarymanagement-core_2.12-1.11.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/librarymanagement-ivy_2.12/1.11.2/librarymanagement-ivy_2.12-1.11.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/compiler-interface/1.10.8/compiler-interface-1.10.8.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-compile_2.12/1.10.8/zinc-compile_2.12-1.10.8.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/swoval/file-tree-views/2.1.12/file-tree-views-2.1.12.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/jarjar/jarjar/1.8.1/jarjar-1.8.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/playframework/twirl/twirl-api_2.12/2.0.8/twirl-api_2.12-2.0.8.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-parser-combinators_2.12/1.1.2/scala-parser-combinators_2.12-1.1.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/playframework/play-build-link/3.0.7/play-build-link-3.0.7.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/methvin/directory-watcher/0.18.0/directory-watcher-0.18.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/github/pathikrit/better-files_2.12/3.9.2/better-files_2.12-3.9.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/playframework/twirl/twirl-compiler_2.12/2.0.8/twirl-compiler_2.12-2.0.8.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/apache/commons/commons-compress/1.27.1/commons-compress-1.27.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/apache/ant/ant/1.10.15/ant-1.10.15.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/github/eldis/tool-launcher/0.2.2/tool-launcher-0.2.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/webjars/webjars-locator-core/0.59/webjars-locator-core-0.59.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/spray/spray-json_2.12/1.3.6/spray-json_2.12-1.3.6.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/apigee/trireme/trireme-core/0.9.4/trireme-core-0.9.4.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/apigee/trireme/trireme-node10src/0.9.4/trireme-node10src-0.9.4.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/webjars/npm/5.0.0-2/npm-5.0.0-2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/guava/guava/33.3.1-jre/guava-33.3.1-jre.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/guava/failureaccess/1.0.2/failureaccess-1.0.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/errorprone/error_prone_annotations/2.33.0/error_prone_annotations-2.33.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/j2objc/j2objc-annotations/3.0.0/j2objc-annotations-3.0.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/http-client/google-http-client/1.45.0/google-http-client-1.45.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-context/1.67.1/grpc-context-1.67.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/opencensus/opencensus-contrib-http-util/0.31.1/opencensus-contrib-http-util-0.31.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/http-client/google-http-client-jackson2/1.45.0/google-http-client-jackson2-1.45.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/http-client/google-http-client-gson/1.45.0/google-http-client-gson-1.45.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/api-client/google-api-client/2.7.0/google-api-client-2.7.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/commons-codec/commons-codec/1.17.1/commons-codec-1.17.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/oauth-client/google-oauth-client/1.36.0/google-oauth-client-1.36.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/http-client/google-http-client-apache-v2/1.45.0/google-http-client-apache-v2-1.45.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/apis/google-api-services-storage/v1-rev20240924-2.0.0/google-api-services-storage-v1-rev20240924-2.0.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/code/gson/gson/2.11.0/gson-2.11.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/cloud/google-cloud-core/2.45.0/google-cloud-core-2.45.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/auto/value/auto-value-annotations/1.11.0/auto-value-annotations-1.11.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/cloud/google-cloud-core-http/2.45.0/google-cloud-core-http-2.45.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/http-client/google-http-client-appengine/1.45.0/google-http-client-appengine-1.45.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/api/gax-httpjson/2.55.0/gax-httpjson-2.55.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/cloud/google-cloud-core-grpc/2.45.0/google-cloud-core-grpc-2.45.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/api/gax/2.55.0/gax-2.55.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/api/gax-grpc/2.55.0/gax-grpc-2.55.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-inprocess/1.67.1/grpc-inprocess-1.67.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-alts/1.67.1/grpc-alts-1.67.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-grpclb/1.67.1/grpc-grpclb-1.67.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/conscrypt/conscrypt-openjdk-uber/2.5.2/conscrypt-openjdk-uber-2.5.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-auth/1.67.1/grpc-auth-1.67.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/auth/google-auth-library-credentials/1.28.0/google-auth-library-credentials-1.28.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/auth/google-auth-library-oauth2-http/1.28.0/google-auth-library-oauth2-http-1.28.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/api/api-common/2.38.0/api-common-2.38.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/javax/annotation/javax.annotation-api/1.3.2/javax.annotation-api-1.3.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/opencensus/opencensus-api/0.31.1/opencensus-api-0.31.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/api/grpc/proto-google-iam-v1/1.41.0/proto-google-iam-v1-1.41.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/protobuf/protobuf-java/3.25.5/protobuf-java-3.25.5.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/protobuf/protobuf-java-util/3.25.5/protobuf-java-util-3.25.5.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-core/1.67.1/grpc-core-1.67.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/android/annotations/4.1.1.4/annotations-4.1.1.4.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/codehaus/mojo/animal-sniffer-annotations/1.24/animal-sniffer-annotations-1.24.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/perfmark/perfmark-api/0.27.0/perfmark-api-0.27.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-protobuf/1.67.1/grpc-protobuf-1.67.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-protobuf-lite/1.67.1/grpc-protobuf-lite-1.67.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/api/grpc/proto-google-common-protos/2.46.0/proto-google-common-protos-2.46.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/threeten/threetenbp/1.7.0/threetenbp-1.7.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/api/grpc/proto-google-cloud-storage-v2/2.43.2-beta/proto-google-cloud-storage-v2-2.43.2-beta.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/api/grpc/grpc-google-cloud-storage-v2/2.43.2-beta/grpc-google-cloud-storage-v2-2.43.2-beta.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/api/grpc/gapic-google-cloud-storage-v2/2.43.2-beta/gapic-google-cloud-storage-v2-2.43.2-beta.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/opentelemetry/opentelemetry-sdk/1.42.1/opentelemetry-sdk-1.42.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/opentelemetry/opentelemetry-sdk-trace/1.42.1/opentelemetry-sdk-trace-1.42.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/opentelemetry/opentelemetry-sdk-logs/1.42.1/opentelemetry-sdk-logs-1.42.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-opentelemetry/1.67.1/grpc-opentelemetry-1.67.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/opentelemetry/opentelemetry-api/1.42.1/opentelemetry-api-1.42.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/opentelemetry/opentelemetry-context/1.42.1/opentelemetry-context-1.42.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/opentelemetry/opentelemetry-sdk-metrics/1.42.1/opentelemetry-sdk-metrics-1.42.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/opentelemetry/opentelemetry-api-incubator/1.42.1-alpha/opentelemetry-api-incubator-1.42.1-alpha.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/opentelemetry/opentelemetry-sdk-common/1.42.1/opentelemetry-sdk-common-1.42.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/opentelemetry/opentelemetry-sdk-extension-autoconfigure-spi/1.42.1/opentelemetry-sdk-extension-autoconfigure-spi-1.42.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/opentelemetry/semconv/opentelemetry-semconv/1.25.0-alpha/opentelemetry-semconv-1.25.0-alpha.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/cloud/opentelemetry/exporter-metrics/0.31.0/exporter-metrics-0.31.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/cloud/google-cloud-monitoring/3.31.0/google-cloud-monitoring-3.31.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/api/grpc/proto-google-cloud-monitoring-v3/3.31.0/proto-google-cloud-monitoring-v3-3.31.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/opencensus/opencensus-proto/0.2.0/opencensus-proto-0.2.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/commons-logging/commons-logging/1.2/commons-logging-1.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/cloud/opentelemetry/shared-resourcemapping/0.32.0/shared-resourcemapping-0.32.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/opentelemetry/contrib/opentelemetry-gcp-resources/1.37.0-alpha/opentelemetry-gcp-resources-1.37.0-alpha.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/cloud/opentelemetry/detector-resources-support/0.32.0/detector-resources-support-0.32.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/checkerframework/checker-qual/3.47.0/checker-qual-3.47.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-core/2.18.0/jackson-core-2.18.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-api/1.67.1/grpc-api-1.67.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-netty-shaded/1.67.1/grpc-netty-shaded-1.67.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-util/1.67.1/grpc-util-1.67.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-stub/1.67.1/grpc-stub-1.67.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-googleapis/1.67.1/grpc-googleapis-1.67.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-xds/1.67.1/grpc-xds-1.67.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-services/1.67.1/grpc-services-1.67.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/re2j/re2j/1.7/re2j-1.7.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-rls/1.67.1/grpc-rls-1.67.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-relation_2.12/1.11.2/util-relation_2.12-1.11.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/completion_2.12/1.11.2/completion_2.12-1.11.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/task-system_2.12/1.11.2/task-system_2.12-1.11.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/tasks_2.12/1.11.2/tasks_2.12-1.11.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/testing_2.12/1.11.2/testing_2.12-1.11.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-tracking_2.12/1.11.2/util-tracking_2.12-1.11.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/sjson-new-core_2.12/0.10.1/sjson-new-core_2.12-0.10.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/sjson-new-scalajson_2.12/0.10.1/sjson-new-scalajson_2.12-0.10.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/gigahorse-apache-http_2.12/0.9.3/gigahorse-apache-http_2.12-0.9.3.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-terminal/3.27.1/jline-terminal-3.27.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-classpath_2.12/1.10.8/zinc-classpath_2.12-1.10.8.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-apiinfo_2.12/1.10.8/zinc-apiinfo_2.12-1.10.8.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc_2.12/1.10.8/zinc_2.12-1.10.8.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/core-macros_2.12/1.11.2/core-macros_2.12-1.11.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-cache_2.12/1.11.2/util-cache_2.12-1.11.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-control_2.12/1.11.2/util-control_2.12-1.11.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/protocol_2.12/1.11.2/protocol_2.12-1.11.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/template-resolver/0.1/template-resolver-0.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-position_2.12/1.11.2/util-position_2.12-1.11.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-compile-core_2.12/1.10.8/zinc-compile-core_2.12-1.10.8.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-interface/1.11.2/util-interface-1.11.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/jline/jline/2.14.7-sbt-9a88bc413e2b34a4580c001c654d1a7f4f65bf18/jline-2.14.7-sbt-9a88bc413e2b34a4580c001c654d1a7f4f65bf18.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-terminal-jni/3.27.1/jline-terminal-jni-3.27.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-native/3.27.1/jline-native-3.27.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/lmax/disruptor/3.4.2/disruptor-3.4.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-collection-compat_2.12/2.13.0/scala-collection-compat_2.12-2.13.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/github/mwiede/jsch/0.2.23/jsch-0.2.23.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/ivy/ivy/2.3.0-sbt-77cc781d727b367d3761f097d89f5a4762771d41/ivy-2.3.0-sbt-77cc781d727b367d3761f097d89f5a4762771d41.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/ow2/asm/asm/9.2/asm-9.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/ow2/asm/asm-commons/9.2/asm-commons-9.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/apache/maven/maven-plugin-api/3.3.9/maven-plugin-api-3.3.9.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/apache/commons/commons-lang3/3.16.0/commons-lang3-3.16.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/playframework/play-exceptions/3.0.7/play-exceptions-3.0.7.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/net/java/dev/jna/jna/5.12.1/jna-5.12.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/playframework/twirl/twirl-parser_2.12/2.0.8/twirl-parser_2.12-2.0.8.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalameta/parsers_2.12/4.13.2/parsers_2.12-4.13.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/commons-io/commons-io/2.16.1/commons-io-2.16.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/apache/ant/ant-launcher/1.10.15/ant-launcher-1.10.15.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/github/classgraph/classgraph/4.8.173/classgraph-4.8.173.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/apigee/trireme/trireme-kernel/0.9.4/trireme-kernel-0.9.4.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/mozilla/rhino/1.7.10/rhino-1.7.10.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-reader/3.27.1/jline-reader-3.27.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-builtins/3.27.1/jline-builtins-3.27.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/test-agent/1.11.2/test-agent-1.11.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/test-interface/1.0/test-interface-1.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/shaded-scalajson_2.12/1.0.0-M4/shaded-scalajson_2.12-1.0.0-M4.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/shaded-jawn-parser_2.12/1.3.2/shaded-jawn-parser_2.12-1.3.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/gigahorse-core_2.12/0.9.3/gigahorse-core_2.12-0.9.3.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/shaded-apache-httpclient5/0.9.3/shaded-apache-httpclient5-0.9.3.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/compiler-bridge_2.12/1.10.8/compiler-bridge_2.12-1.10.8.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-classfile_2.12/1.10.8/zinc-classfile_2.12-1.10.8.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-core_2.12/1.10.8/zinc-core_2.12-1.10.8.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-persist_2.12/1.10.8/zinc-persist_2.12-1.10.8.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/sjson-new-murmurhash_2.12/0.10.1/sjson-new-murmurhash_2.12-0.10.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/ipcsocket/ipcsocket/1.6.3/ipcsocket-1.6.3.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/net/openhft/zero-allocation-hashing/0.16/zero-allocation-hashing-0.16.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/fusesource/jansi/jansi/2.4.1/jansi-2.4.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/ow2/asm/asm-tree/9.2/asm-tree-9.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/ow2/asm/asm-analysis/9.2/asm-analysis-9.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/apache/maven/maven-model/3.3.9/maven-model-3.3.9.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/apache/maven/maven-artifact/3.3.9/maven-artifact-3.3.9.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/eclipse/sisu/org.eclipse.sisu.plexus/0.3.2/org.eclipse.sisu.plexus-0.3.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalameta/trees_2.12/4.13.2/trees_2.12-4.13.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-style/3.27.1/jline-style-3.27.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/typesafe/ssl-config-core_2.12/0.6.1/ssl-config-core_2.12-0.6.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/reactivestreams/reactive-streams/1.0.3/reactive-streams-1.0.3.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-persist-core-assembly/1.10.8/zinc-persist-core-assembly-1.10.8.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/sbinary_2.12/0.5.1/sbinary_2.12-0.5.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/net/java/dev/jna/jna-platform/5.12.0/jna-platform-5.12.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/codehaus/plexus/plexus-utils/3.0.22/plexus-utils-3.0.22.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/javax/enterprise/cdi-api/1.0/cdi-api-1.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/eclipse/sisu/org.eclipse.sisu.inject/0.3.2/org.eclipse.sisu.inject-0.3.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/codehaus/plexus/plexus-component-annotations/1.5.5/plexus-component-annotations-1.5.5.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/codehaus/plexus/plexus-classworlds/2.5.2/plexus-classworlds-2.5.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalameta/common_2.12/4.13.2/common_2.12-4.13.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalameta/io_2.12/4.13.2/io_2.12-4.13.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/javax/annotation/jsr250-api/1.0/jsr250-api-1.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/javax/inject/javax.inject/1/javax.inject-1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/lihaoyi/sourcecode_2.12/0.4.2/sourcecode_2.12-0.4.2.jar"], "out": "/Users/<USER>/Dev/smartreach_backend/project/.bloop/smartreach_backend-build", "classesDir": "/Users/<USER>/Dev/smartreach_backend/project/.bloop/smartreach_backend-build/scala-2.12/sbt-1.0/classes", "resources": ["/Users/<USER>/Dev/smartreach_backend/project/src/main/resources", "/Users/<USER>/Dev/smartreach_backend/project/target/scala-2.12/sbt-1.0/resource_managed/main"], "scala": {"organization": "org.scala-lang", "name": "scala-compiler", "version": "2.12.20", "options": ["-deprecation", "-Wconf:cat=unused-nowarn:s", "-Wconf:cat=unused-nowarn:s", "-Xsource:3"], "jars": ["/Users/<USER>/.sbt/boot/scala-2.12.20/lib/scala-library.jar", "/Users/<USER>/.sbt/boot/scala-2.12.20/lib/scala-compiler.jar", "/Users/<USER>/.sbt/boot/scala-2.12.20/lib/scala-reflect.jar", "/Users/<USER>/.sbt/boot/scala-2.12.20/lib/scala-xml_2.12-2.3.0.jar"], "setup": {"order": "mixed", "addLibraryToBootClasspath": true, "addCompilerToClasspath": false, "addExtraJarsToClasspath": false, "manageBootClasspath": true, "filterLibraryFromClasspath": true}}, "java": {"options": []}, "sbt": {"sbtVersion": "1.11.2", "autoImports": ["import _root_.scala.xml.{TopScope=>$scope}", "import _root_.sbt._", "import _root_.sbt.Keys._", "import _root_.sbt.nio.Keys._", "import _root_.sbt.ScriptedPlugin.autoImport._, _root_.sbt.plugins.JUnitXmlReportPlugin.autoImport._, _root_.sbt.plugins.MiniDependencyTreePlugin.autoImport._, _root_.sbtassembly.AssemblyPlugin.autoImport._, _root_.play.sbt.Play.autoImport._, _root_.play.sbt.PlayService.autoImport._, _root_.play.sbt.routes.RoutesCompiler.autoImport._, _root_.org.latestbit.sbt.gcs.GcsPlugin.autoImport._, _root_.play.twirl.sbt.SbtTwirl.autoImport._, _root_.com.typesafe.sbt.SbtNativePackager.autoImport._, _root_.com.typesafe.sbt.packager.archetypes.JavaAppPackaging.autoImport._, _root_.com.typesafe.sbt.packager.archetypes.JavaServerAppPackaging.autoImport._, _root_.com.typesafe.sbt.packager.archetypes.jar.ClasspathJarPlugin.autoImport._, _root_.com.typesafe.sbt.packager.archetypes.jar.LauncherJarPlugin.autoImport._, _root_.com.typesafe.sbt.packager.archetypes.jlink.JlinkPlugin.autoImport._, _root_.com.typesafe.sbt.packager.archetypes.scripts.BashStartScriptPlugin.autoImport._, _root_.com.typesafe.sbt.packager.archetypes.scripts.BatStartScriptPlugin.autoImport._, _root_.com.typesafe.sbt.packager.archetypes.systemloader.SystemdPlugin.autoImport._, _root_.com.typesafe.sbt.packager.archetypes.systemloader.SystemloaderPlugin.autoImport._, _root_.com.typesafe.sbt.packager.debian.DebianPlugin.autoImport._, _root_.com.typesafe.sbt.packager.docker.DockerPlugin.autoImport._, _root_.com.typesafe.sbt.packager.graalvmnativeimage.GraalVMNativeImagePlugin.autoImport._, _root_.com.typesafe.sbt.packager.jdkpackager.JDKPackagerPlugin.autoImport._, _root_.com.typesafe.sbt.packager.linux.LinuxPlugin.autoImport._, _root_.com.typesafe.sbt.packager.rpm.RpmPlugin.autoImport._, _root_.com.typesafe.sbt.packager.universal.UniversalPlugin.autoImport._, _root_.com.typesafe.sbt.packager.windows.WindowsPlugin.autoImport._, _root_.com.typesafe.sbt.web.SbtWeb.autoImport._, _root_.com.typesafe.sbt.jse.SbtJsEngine.autoImport._, _root_.com.typesafe.sbt.jse.SbtJsTask.autoImport._", "import PublishMore.autoImport._", "import _root_.sbt.plugins.IvyPlugin, _root_.sbt.plugins.JvmPlugin, _root_.sbt.plugins.CorePlugin, _root_.sbt.ScriptedPlugin, _root_.sbt.plugins.SbtPlugin, _root_.sbt.plugins.SemanticdbPlugin, _root_.sbt.plugins.JUnitXmlReportPlugin, _root_.sbt.plugins.Giter8TemplatePlugin, _root_.sbt.plugins.MiniDependencyTreePlugin, _root_.sbtassembly.AssemblyPlugin, _root_.play.sbt.Play, _root_.play.sbt.PlayFilters, _root_.play.sbt.PlayJava, _root_.play.sbt.PlayLayoutPlugin, _root_.play.sbt.PlayLogback, _root_.play.sbt.PlayMinimalJava, _root_.play.sbt.PlayNettyServer, _root_.play.sbt.PlayPekkoHttp2Support, _root_.play.sbt.PlayPekkoHttpServer, _root_.play.sbt.PlayScala, _root_.play.sbt.PlayService, _root_.play.sbt.PlayWeb, _root_.play.sbt.routes.RoutesCompiler, _root_.org.latestbit.sbt.gcs.GcsPlugin, _root_.play.twirl.sbt.SbtTwirl, _root_.com.typesafe.sbt.SbtNativePackager, _root_.com.typesafe.sbt.packager.archetypes.JavaAppPackaging, _root_.com.typesafe.sbt.packager.archetypes.JavaServerAppPackaging, _root_.com.typesafe.sbt.packager.archetypes.jar.ClasspathJarPlugin, _root_.com.typesafe.sbt.packager.archetypes.jar.LauncherJarPlugin, _root_.com.typesafe.sbt.packager.archetypes.jlink.JlinkPlugin, _root_.com.typesafe.sbt.packager.archetypes.scripts.AshScriptPlugin, _root_.com.typesafe.sbt.packager.archetypes.scripts.BashStartScriptPlugin, _root_.com.typesafe.sbt.packager.archetypes.scripts.BatStartScriptPlugin, _root_.com.typesafe.sbt.packager.archetypes.systemloader.SystemVPlugin, _root_.com.typesafe.sbt.packager.archetypes.systemloader.SystemdPlugin, _root_.com.typesafe.sbt.packager.archetypes.systemloader.SystemloaderPlugin, _root_.com.typesafe.sbt.packager.archetypes.systemloader.UpstartPlugin, _root_.com.typesafe.sbt.packager.debian.DebianDeployPlugin, _root_.com.typesafe.sbt.packager.debian.DebianPlugin, _root_.com.typesafe.sbt.packager.debian.JDebPackaging, _root_.com.typesafe.sbt.packager.docker.DockerPlugin, _root_.com.typesafe.sbt.packager.docker.DockerSpotifyClientPlugin, _root_.com.typesafe.sbt.packager.graalvmnativeimage.GraalVMNativeImagePlugin, _root_.com.typesafe.sbt.packager.jdkpackager.JDKPackagerDeployPlugin, _root_.com.typesafe.sbt.packager.jdkpackager.JDKPackagerPlugin, _root_.com.typesafe.sbt.packager.linux.LinuxPlugin, _root_.com.typesafe.sbt.packager.rpm.RpmDeployPlugin, _root_.com.typesafe.sbt.packager.rpm.RpmPlugin, _root_.com.typesafe.sbt.packager.universal.UniversalDeployPlugin, _root_.com.typesafe.sbt.packager.universal.UniversalPlugin, _root_.com.typesafe.sbt.packager.windows.WindowsDeployPlugin, _root_.com.typesafe.sbt.packager.windows.WindowsPlugin, _root_.com.typesafe.sbt.web.SbtWeb, _root_.com.typesafe.sbt.jse.SbtJsEngine, _root_.com.typesafe.sbt.jse.SbtJsTask"]}, "test": {"frameworks": [{"names": ["org.scalacheck.ScalaCheckFramework"]}, {"names": ["org.specs2.runner.Specs2Framework", "org.specs2.runner.SpecsFramework"]}, {"names": ["org.specs.runner.SpecsFramework"]}, {"names": ["org.scalatest.tools.Framework", "org.scalatest.tools.ScalaTestFramework"]}, {"names": ["com.novocode.junit.JUnitFramework"]}, {"names": ["munit.Framework"]}, {"names": ["zio.test.sbt.ZTestFramework"]}, {"names": ["weaver.framework.CatsEffect"]}, {"names": ["hedgehog.sbt.Framework"]}], "options": {"excludes": [], "arguments": []}}, "platform": {"name": "jvm", "config": {"home": "/Library/Java/JavaVirtualMachines/amazon-corretto-21.jdk/Contents/Home", "options": ["-Duser.dir=/Users/<USER>/Dev/smartreach_backend/project"]}, "mainClass": [], "runtimeConfig": {"home": "/Library/Java/JavaVirtualMachines/amazon-corretto-21.jdk/Contents/Home", "options": ["-Duser.dir=/Users/<USER>/Dev/smartreach_backend/project"]}, "classpath": ["/Users/<USER>/Dev/smartreach_backend/project/.bloop/smartreach_backend-build/scala-2.12/sbt-1.0/classes", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/sbt-assembly_2.12_1.0/2.1.0/sbt-assembly-2.1.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/playframework/sbt-plugin_2.12_1.0/3.0.7/sbt-plugin_2.12_1.0-3.0.7.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/latestbit/sbt-gcs-plugin_2.12_1.0/1.12.0/sbt-gcs-plugin_2.12_1.0-1.12.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/jarjarabrams/jarjar-abrams-core_2.12/1.8.1/jarjar-abrams-core_2.12-1.8.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/playframework/sbt-routes-compiler_2.12/3.0.7/sbt-routes-compiler_2.12-3.0.7.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/playframework/play-run-support_2.12/3.0.7/play-run-support_2.12-3.0.7.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/typesafe/config/1.4.3/config-1.4.3.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/slf4j/slf4j-simple/2.0.17/slf4j-simple-2.0.17.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/playframework/play-file-watch_2.12/2.0.0/play-file-watch_2.12-2.0.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/playframework/twirl/sbt-twirl_2.12_1.0/2.0.8/sbt-twirl_2.12_1.0-2.0.8.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/github/sbt/sbt-native-packager_2.12_1.0/1.11.1/sbt-native-packager_2.12_1.0-1.11.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/github/sbt/sbt-web_2.12_1.0/1.5.8/sbt-web_2.12_1.0-1.5.8.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/github/sbt/sbt-js-engine_2.12_1.0/1.3.9/sbt-js-engine_2.12_1.0-1.3.9.jar", "/Users/<USER>/.sbt/boot/scala-2.12.20/lib/scala-compiler.jar", "/Users/<USER>/.sbt/boot/scala-2.12.20/lib/scala-library.jar", "/Users/<USER>/.sbt/boot/scala-2.12.20/lib/scala-reflect.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/apache/ivy/ivy/2.4.0/ivy-2.4.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/cloud/google-cloud-storage/2.43.2/google-cloud-storage-2.43.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/jarjar/jarjar/1.8.1/jarjar-1.8.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/playframework/twirl/twirl-api_2.12/2.0.8/twirl-api_2.12-2.0.8.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-parser-combinators_2.12/1.1.2/scala-parser-combinators_2.12-1.1.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/playframework/play-build-link/3.0.7/play-build-link-3.0.7.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/methvin/directory-watcher/0.18.0/directory-watcher-0.18.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/github/pathikrit/better-files_2.12/3.9.2/better-files_2.12-3.9.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/playframework/twirl/twirl-compiler_2.12/2.0.8/twirl-compiler_2.12-2.0.8.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/apache/commons/commons-compress/1.27.1/commons-compress-1.27.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/apache/ant/ant/1.10.15/ant-1.10.15.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/github/eldis/tool-launcher/0.2.2/tool-launcher-0.2.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/io_2.12/1.10.4/io_2.12-1.10.4.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-xml_2.12/2.3.0/scala-xml_2.12-2.3.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/webjars/webjars-locator-core/0.59/webjars-locator-core-0.59.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/spray/spray-json_2.12/1.3.6/spray-json_2.12-1.3.6.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/apigee/trireme/trireme-core/0.9.4/trireme-core-0.9.4.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/apigee/trireme/trireme-node10src/0.9.4/trireme-node10src-0.9.4.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/webjars/npm/5.0.0-2/npm-5.0.0-2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/guava/guava/33.3.1-jre/guava-33.3.1-jre.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/guava/failureaccess/1.0.2/failureaccess-1.0.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/errorprone/error_prone_annotations/2.33.0/error_prone_annotations-2.33.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/j2objc/j2objc-annotations/3.0.0/j2objc-annotations-3.0.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/http-client/google-http-client/1.45.0/google-http-client-1.45.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-context/1.67.1/grpc-context-1.67.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/opencensus/opencensus-contrib-http-util/0.31.1/opencensus-contrib-http-util-0.31.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/http-client/google-http-client-jackson2/1.45.0/google-http-client-jackson2-1.45.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/http-client/google-http-client-gson/1.45.0/google-http-client-gson-1.45.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/api-client/google-api-client/2.7.0/google-api-client-2.7.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/commons-codec/commons-codec/1.17.1/commons-codec-1.17.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/oauth-client/google-oauth-client/1.36.0/google-oauth-client-1.36.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/http-client/google-http-client-apache-v2/1.45.0/google-http-client-apache-v2-1.45.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/apis/google-api-services-storage/v1-rev20240924-2.0.0/google-api-services-storage-v1-rev20240924-2.0.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/code/gson/gson/2.11.0/gson-2.11.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/cloud/google-cloud-core/2.45.0/google-cloud-core-2.45.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/auto/value/auto-value-annotations/1.11.0/auto-value-annotations-1.11.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/cloud/google-cloud-core-http/2.45.0/google-cloud-core-http-2.45.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/http-client/google-http-client-appengine/1.45.0/google-http-client-appengine-1.45.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/api/gax-httpjson/2.55.0/gax-httpjson-2.55.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/cloud/google-cloud-core-grpc/2.45.0/google-cloud-core-grpc-2.45.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/api/gax/2.55.0/gax-2.55.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/api/gax-grpc/2.55.0/gax-grpc-2.55.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-inprocess/1.67.1/grpc-inprocess-1.67.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-alts/1.67.1/grpc-alts-1.67.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-grpclb/1.67.1/grpc-grpclb-1.67.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/conscrypt/conscrypt-openjdk-uber/2.5.2/conscrypt-openjdk-uber-2.5.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-auth/1.67.1/grpc-auth-1.67.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/auth/google-auth-library-credentials/1.28.0/google-auth-library-credentials-1.28.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/auth/google-auth-library-oauth2-http/1.28.0/google-auth-library-oauth2-http-1.28.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/api/api-common/2.38.0/api-common-2.38.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/javax/annotation/javax.annotation-api/1.3.2/javax.annotation-api-1.3.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/opencensus/opencensus-api/0.31.1/opencensus-api-0.31.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/api/grpc/proto-google-iam-v1/1.41.0/proto-google-iam-v1-1.41.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/protobuf/protobuf-java/3.25.5/protobuf-java-3.25.5.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/protobuf/protobuf-java-util/3.25.5/protobuf-java-util-3.25.5.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-core/1.67.1/grpc-core-1.67.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/android/annotations/4.1.1.4/annotations-4.1.1.4.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/codehaus/mojo/animal-sniffer-annotations/1.24/animal-sniffer-annotations-1.24.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/perfmark/perfmark-api/0.27.0/perfmark-api-0.27.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-protobuf/1.67.1/grpc-protobuf-1.67.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-protobuf-lite/1.67.1/grpc-protobuf-lite-1.67.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/api/grpc/proto-google-common-protos/2.46.0/proto-google-common-protos-2.46.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/threeten/threetenbp/1.7.0/threetenbp-1.7.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/api/grpc/proto-google-cloud-storage-v2/2.43.2-beta/proto-google-cloud-storage-v2-2.43.2-beta.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/api/grpc/grpc-google-cloud-storage-v2/2.43.2-beta/grpc-google-cloud-storage-v2-2.43.2-beta.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/api/grpc/gapic-google-cloud-storage-v2/2.43.2-beta/gapic-google-cloud-storage-v2-2.43.2-beta.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/opentelemetry/opentelemetry-sdk/1.42.1/opentelemetry-sdk-1.42.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/opentelemetry/opentelemetry-sdk-trace/1.42.1/opentelemetry-sdk-trace-1.42.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/opentelemetry/opentelemetry-sdk-logs/1.42.1/opentelemetry-sdk-logs-1.42.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-opentelemetry/1.67.1/grpc-opentelemetry-1.67.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/opentelemetry/opentelemetry-api/1.42.1/opentelemetry-api-1.42.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/opentelemetry/opentelemetry-context/1.42.1/opentelemetry-context-1.42.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/opentelemetry/opentelemetry-sdk-metrics/1.42.1/opentelemetry-sdk-metrics-1.42.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/opentelemetry/opentelemetry-api-incubator/1.42.1-alpha/opentelemetry-api-incubator-1.42.1-alpha.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/opentelemetry/opentelemetry-sdk-common/1.42.1/opentelemetry-sdk-common-1.42.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/opentelemetry/opentelemetry-sdk-extension-autoconfigure-spi/1.42.1/opentelemetry-sdk-extension-autoconfigure-spi-1.42.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/opentelemetry/semconv/opentelemetry-semconv/1.25.0-alpha/opentelemetry-semconv-1.25.0-alpha.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/cloud/opentelemetry/exporter-metrics/0.31.0/exporter-metrics-0.31.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/cloud/google-cloud-monitoring/3.31.0/google-cloud-monitoring-3.31.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/api/grpc/proto-google-cloud-monitoring-v3/3.31.0/proto-google-cloud-monitoring-v3-3.31.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/opencensus/opencensus-proto/0.2.0/opencensus-proto-0.2.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/commons-logging/commons-logging/1.2/commons-logging-1.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/cloud/opentelemetry/shared-resourcemapping/0.32.0/shared-resourcemapping-0.32.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/opentelemetry/contrib/opentelemetry-gcp-resources/1.37.0-alpha/opentelemetry-gcp-resources-1.37.0-alpha.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/cloud/opentelemetry/detector-resources-support/0.32.0/detector-resources-support-0.32.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/checkerframework/checker-qual/3.47.0/checker-qual-3.47.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-core/2.18.0/jackson-core-2.18.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-api/1.67.1/grpc-api-1.67.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-netty-shaded/1.67.1/grpc-netty-shaded-1.67.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-util/1.67.1/grpc-util-1.67.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-stub/1.67.1/grpc-stub-1.67.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-googleapis/1.67.1/grpc-googleapis-1.67.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-xds/1.67.1/grpc-xds-1.67.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-services/1.67.1/grpc-services-1.67.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/re2j/re2j/1.7/re2j-1.7.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-rls/1.67.1/grpc-rls-1.67.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/ow2/asm/asm/9.2/asm-9.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/ow2/asm/asm-commons/9.2/asm-commons-9.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/apache/maven/maven-plugin-api/3.3.9/maven-plugin-api-3.3.9.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/apache/commons/commons-lang3/3.16.0/commons-lang3-3.16.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/playframework/play-exceptions/3.0.7/play-exceptions-3.0.7.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/net/java/dev/jna/jna/5.12.1/jna-5.12.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/playframework/twirl/twirl-parser_2.12/2.0.8/twirl-parser_2.12-2.0.8.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalameta/parsers_2.12/4.13.2/parsers_2.12-4.13.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/commons-io/commons-io/2.16.1/commons-io-2.16.1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/apache/ant/ant-launcher/1.10.15/ant-launcher-1.10.15.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/swoval/file-tree-views/2.1.12/file-tree-views-2.1.12.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/github/classgraph/classgraph/4.8.173/classgraph-4.8.173.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/apigee/trireme/trireme-kernel/0.9.4/trireme-kernel-0.9.4.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/mozilla/rhino/1.7.10/rhino-1.7.10.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/ow2/asm/asm-tree/9.2/asm-tree-9.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/ow2/asm/asm-analysis/9.2/asm-analysis-9.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/apache/maven/maven-model/3.3.9/maven-model-3.3.9.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/apache/maven/maven-artifact/3.3.9/maven-artifact-3.3.9.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/eclipse/sisu/org.eclipse.sisu.plexus/0.3.2/org.eclipse.sisu.plexus-0.3.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalameta/trees_2.12/4.13.2/trees_2.12-4.13.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/codehaus/plexus/plexus-utils/3.0.22/plexus-utils-3.0.22.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/javax/enterprise/cdi-api/1.0/cdi-api-1.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/eclipse/sisu/org.eclipse.sisu.inject/0.3.2/org.eclipse.sisu.inject-0.3.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/codehaus/plexus/plexus-component-annotations/1.5.5/plexus-component-annotations-1.5.5.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/codehaus/plexus/plexus-classworlds/2.5.2/plexus-classworlds-2.5.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalameta/common_2.12/4.13.2/common_2.12-4.13.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalameta/io_2.12/4.13.2/io_2.12-4.13.2.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/javax/annotation/jsr250-api/1.0/jsr250-api-1.0.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/javax/inject/javax.inject/1/javax.inject-1.jar", "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/lihaoyi/sourcecode_2.12/0.4.2/sourcecode_2.12-0.4.2.jar"]}, "resolution": {"modules": [{"organization": "com.eed3si9n", "name": "sbt-assembly", "version": "2.1.0", "configurations": "default", "artifacts": [{"name": "sbt-assembly", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/sbt-assembly_2.12_1.0/2.1.0/sbt-assembly-2.1.0.jar"}, {"name": "sbt-assembly", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/sbt-assembly_2.12_1.0/2.1.0/sbt-assembly-2.1.0-sources.jar"}]}, {"organization": "org.playframework", "name": "sbt-plugin", "version": "3.0.7", "configurations": "default", "artifacts": [{"name": "sbt-plugin", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/playframework/sbt-plugin_2.12_1.0/3.0.7/sbt-plugin_2.12_1.0-3.0.7.jar"}, {"name": "sbt-plugin", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/playframework/sbt-plugin_2.12_1.0/3.0.7/sbt-plugin_2.12_1.0-3.0.7-sources.jar"}]}, {"organization": "org.latestbit", "name": "sbt-gcs-plugin", "version": "1.12.0", "configurations": "default", "artifacts": [{"name": "sbt-gcs-plugin", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/latestbit/sbt-gcs-plugin_2.12_1.0/1.12.0/sbt-gcs-plugin_2.12_1.0-1.12.0.jar"}, {"name": "sbt-gcs-plugin", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/latestbit/sbt-gcs-plugin_2.12_1.0/1.12.0/sbt-gcs-plugin_2.12_1.0-1.12.0-sources.jar"}]}, {"organization": "com.eed3si9n.jarjarabrams", "name": "jarjar-abrams-core_2.12", "version": "1.8.1", "configurations": "default", "artifacts": [{"name": "jarjar-abrams-core_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/jarjarabrams/jarjar-abrams-core_2.12/1.8.1/jarjar-abrams-core_2.12-1.8.1.jar"}, {"name": "jarjar-abrams-core_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/jarjarabrams/jarjar-abrams-core_2.12/1.8.1/jarjar-abrams-core_2.12-1.8.1-sources.jar"}]}, {"organization": "org.playframework", "name": "sbt-routes-compiler_2.12", "version": "3.0.7", "configurations": "default", "artifacts": [{"name": "sbt-routes-compiler_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/playframework/sbt-routes-compiler_2.12/3.0.7/sbt-routes-compiler_2.12-3.0.7.jar"}, {"name": "sbt-routes-compiler_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/playframework/sbt-routes-compiler_2.12/3.0.7/sbt-routes-compiler_2.12-3.0.7-sources.jar"}]}, {"organization": "org.playframework", "name": "play-run-support_2.12", "version": "3.0.7", "configurations": "default", "artifacts": [{"name": "play-run-support_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/playframework/play-run-support_2.12/3.0.7/play-run-support_2.12-3.0.7.jar"}, {"name": "play-run-support_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/playframework/play-run-support_2.12/3.0.7/play-run-support_2.12-3.0.7-sources.jar"}]}, {"organization": "com.typesafe", "name": "config", "version": "1.4.3", "configurations": "default", "artifacts": [{"name": "config", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/typesafe/config/1.4.3/config-1.4.3.jar"}, {"name": "config", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/typesafe/config/1.4.3/config-1.4.3-sources.jar"}]}, {"organization": "org.slf4j", "name": "slf4j-simple", "version": "2.0.17", "configurations": "default", "artifacts": [{"name": "slf4j-simple", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/slf4j/slf4j-simple/2.0.17/slf4j-simple-2.0.17.jar"}, {"name": "slf4j-simple", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/slf4j/slf4j-simple/2.0.17/slf4j-simple-2.0.17-sources.jar"}]}, {"organization": "org.playframework", "name": "play-file-watch_2.12", "version": "2.0.0", "configurations": "default", "artifacts": [{"name": "play-file-watch_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/playframework/play-file-watch_2.12/2.0.0/play-file-watch_2.12-2.0.0.jar"}, {"name": "play-file-watch_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/playframework/play-file-watch_2.12/2.0.0/play-file-watch_2.12-2.0.0-sources.jar"}]}, {"organization": "org.playframework.twirl", "name": "sbt-twirl", "version": "2.0.8", "configurations": "default", "artifacts": [{"name": "sbt-twirl", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/playframework/twirl/sbt-twirl_2.12_1.0/2.0.8/sbt-twirl_2.12_1.0-2.0.8.jar"}, {"name": "sbt-twirl", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/playframework/twirl/sbt-twirl_2.12_1.0/2.0.8/sbt-twirl_2.12_1.0-2.0.8-sources.jar"}]}, {"organization": "com.github.sbt", "name": "sbt-native-packager", "version": "1.11.1", "configurations": "default", "artifacts": [{"name": "sbt-native-packager", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/github/sbt/sbt-native-packager_2.12_1.0/1.11.1/sbt-native-packager_2.12_1.0-1.11.1.jar"}, {"name": "sbt-native-packager", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/github/sbt/sbt-native-packager_2.12_1.0/1.11.1/sbt-native-packager_2.12_1.0-1.11.1-sources.jar"}]}, {"organization": "com.github.sbt", "name": "sbt-web", "version": "1.5.8", "configurations": "default", "artifacts": [{"name": "sbt-web", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/github/sbt/sbt-web_2.12_1.0/1.5.8/sbt-web_2.12_1.0-1.5.8.jar"}, {"name": "sbt-web", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/github/sbt/sbt-web_2.12_1.0/1.5.8/sbt-web_2.12_1.0-1.5.8-sources.jar"}]}, {"organization": "com.github.sbt", "name": "sbt-js-engine", "version": "1.3.9", "configurations": "default", "artifacts": [{"name": "sbt-js-engine", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/github/sbt/sbt-js-engine_2.12_1.0/1.3.9/sbt-js-engine_2.12_1.0-1.3.9.jar"}, {"name": "sbt-js-engine", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/github/sbt/sbt-js-engine_2.12_1.0/1.3.9/sbt-js-engine_2.12_1.0-1.3.9-sources.jar"}]}, {"organization": "org.scala-lang", "name": "scala-compiler", "version": "2.12.20", "configurations": "default", "artifacts": [{"name": "scala-compiler", "path": "/Users/<USER>/.sbt/boot/scala-2.12.20/lib/scala-compiler.jar"}, {"name": "scala-compiler", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-compiler/2.12.20/scala-compiler-2.12.20-sources.jar"}]}, {"organization": "org.scala-lang", "name": "scala-library", "version": "2.12.20", "configurations": "default", "artifacts": [{"name": "scala-library", "path": "/Users/<USER>/.sbt/boot/scala-2.12.20/lib/scala-library.jar"}, {"name": "scala-library", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.12.20/scala-library-2.12.20-sources.jar"}]}, {"organization": "org.scala-lang", "name": "scala-reflect", "version": "2.12.20", "configurations": "default", "artifacts": [{"name": "scala-reflect", "path": "/Users/<USER>/.sbt/boot/scala-2.12.20/lib/scala-reflect.jar"}, {"name": "scala-reflect", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-reflect/2.12.20/scala-reflect-2.12.20-sources.jar"}]}, {"organization": "org.apache.ivy", "name": "ivy", "version": "2.4.0", "configurations": "default", "artifacts": [{"name": "ivy", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/apache/ivy/ivy/2.4.0/ivy-2.4.0.jar"}, {"name": "ivy", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/apache/ivy/ivy/2.4.0/ivy-2.4.0-sources.jar"}]}, {"organization": "com.google.cloud", "name": "google-cloud-storage", "version": "2.43.2", "configurations": "default", "artifacts": [{"name": "google-cloud-storage", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/cloud/google-cloud-storage/2.43.2/google-cloud-storage-2.43.2.jar"}, {"name": "google-cloud-storage", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/cloud/google-cloud-storage/2.43.2/google-cloud-storage-2.43.2-sources.jar"}]}, {"organization": "com.eed3si9n.jarjar", "name": "jarjar", "version": "1.8.1", "configurations": "default", "artifacts": [{"name": "jarjar", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/jarjar/jarjar/1.8.1/jarjar-1.8.1.jar"}, {"name": "jarjar", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/jarjar/jarjar/1.8.1/jarjar-1.8.1-sources.jar"}]}, {"organization": "org.playframework.twirl", "name": "twirl-api_2.12", "version": "2.0.8", "configurations": "default", "artifacts": [{"name": "twirl-api_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/playframework/twirl/twirl-api_2.12/2.0.8/twirl-api_2.12-2.0.8.jar"}, {"name": "twirl-api_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/playframework/twirl/twirl-api_2.12/2.0.8/twirl-api_2.12-2.0.8-sources.jar"}]}, {"organization": "org.scala-lang.modules", "name": "scala-parser-combinators_2.12", "version": "1.1.2", "configurations": "default", "artifacts": [{"name": "scala-parser-combinators_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-parser-combinators_2.12/1.1.2/scala-parser-combinators_2.12-1.1.2.jar"}, {"name": "scala-parser-combinators_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-parser-combinators_2.12/1.1.2/scala-parser-combinators_2.12-1.1.2-sources.jar"}]}, {"organization": "org.playframework", "name": "play-build-link", "version": "3.0.7", "configurations": "default", "artifacts": [{"name": "play-build-link", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/playframework/play-build-link/3.0.7/play-build-link-3.0.7.jar"}, {"name": "play-build-link", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/playframework/play-build-link/3.0.7/play-build-link-3.0.7-sources.jar"}]}, {"organization": "org.slf4j", "name": "slf4j-api", "version": "2.0.17", "configurations": "default", "artifacts": [{"name": "slf4j-api", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar"}, {"name": "slf4j-api", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17-sources.jar"}]}, {"organization": "io.methvin", "name": "directory-watcher", "version": "0.18.0", "configurations": "default", "artifacts": [{"name": "directory-watcher", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/methvin/directory-watcher/0.18.0/directory-watcher-0.18.0.jar"}, {"name": "directory-watcher", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/methvin/directory-watcher/0.18.0/directory-watcher-0.18.0-sources.jar"}]}, {"organization": "com.github.pathikrit", "name": "better-files_2.12", "version": "3.9.2", "configurations": "default", "artifacts": [{"name": "better-files_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/github/pathikrit/better-files_2.12/3.9.2/better-files_2.12-3.9.2.jar"}, {"name": "better-files_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/github/pathikrit/better-files_2.12/3.9.2/better-files_2.12-3.9.2-sources.jar"}]}, {"organization": "org.playframework.twirl", "name": "twirl-compiler_2.12", "version": "2.0.8", "configurations": "default", "artifacts": [{"name": "twirl-compiler_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/playframework/twirl/twirl-compiler_2.12/2.0.8/twirl-compiler_2.12-2.0.8.jar"}, {"name": "twirl-compiler_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/playframework/twirl/twirl-compiler_2.12/2.0.8/twirl-compiler_2.12-2.0.8-sources.jar"}]}, {"organization": "org.apache.commons", "name": "commons-compress", "version": "1.27.1", "configurations": "default", "artifacts": [{"name": "commons-compress", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/apache/commons/commons-compress/1.27.1/commons-compress-1.27.1.jar"}, {"name": "commons-compress", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/apache/commons/commons-compress/1.27.1/commons-compress-1.27.1-sources.jar"}]}, {"organization": "org.apache.ant", "name": "ant", "version": "1.10.15", "configurations": "default", "artifacts": [{"name": "ant", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/apache/ant/ant/1.10.15/ant-1.10.15.jar"}, {"name": "ant", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/apache/ant/ant/1.10.15/ant-1.10.15-sources.jar"}]}, {"organization": "com.github.eldis", "name": "tool-launcher", "version": "0.2.2", "configurations": "default", "artifacts": [{"name": "tool-launcher", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/github/eldis/tool-launcher/0.2.2/tool-launcher-0.2.2.jar"}, {"name": "tool-launcher", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/github/eldis/tool-launcher/0.2.2/tool-launcher-0.2.2-sources.jar"}]}, {"organization": "org.scala-lang.modules", "name": "scala-xml_2.12", "version": "2.3.0", "configurations": "default", "artifacts": [{"name": "scala-xml_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-xml_2.12/2.3.0/scala-xml_2.12-2.3.0.jar"}, {"name": "scala-xml_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-xml_2.12/2.3.0/scala-xml_2.12-2.3.0-sources.jar"}]}, {"organization": "org.webjars", "name": "webjars-locator-core", "version": "0.59", "configurations": "default", "artifacts": [{"name": "webjars-locator-core", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/webjars/webjars-locator-core/0.59/webjars-locator-core-0.59.jar"}, {"name": "webjars-locator-core", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/webjars/webjars-locator-core/0.59/webjars-locator-core-0.59-sources.jar"}]}, {"organization": "io.spray", "name": "spray-json_2.12", "version": "1.3.6", "configurations": "default", "artifacts": [{"name": "spray-json_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/spray/spray-json_2.12/1.3.6/spray-json_2.12-1.3.6.jar"}, {"name": "spray-json_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/spray/spray-json_2.12/1.3.6/spray-json_2.12-1.3.6-sources.jar"}]}, {"organization": "io.apigee.trireme", "name": "trireme-core", "version": "0.9.4", "configurations": "default", "artifacts": [{"name": "trireme-core", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/apigee/trireme/trireme-core/0.9.4/trireme-core-0.9.4.jar"}, {"name": "trireme-core", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/apigee/trireme/trireme-core/0.9.4/trireme-core-0.9.4-sources.jar"}]}, {"organization": "io.apigee.trireme", "name": "trireme-node10src", "version": "0.9.4", "configurations": "default", "artifacts": [{"name": "trireme-node10src", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/apigee/trireme/trireme-node10src/0.9.4/trireme-node10src-0.9.4.jar"}, {"name": "trireme-node10src", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/apigee/trireme/trireme-node10src/0.9.4/trireme-node10src-0.9.4-sources.jar"}]}, {"organization": "org.webjars", "name": "npm", "version": "5.0.0-2", "configurations": "default", "artifacts": [{"name": "npm", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/webjars/npm/5.0.0-2/npm-5.0.0-2.jar"}]}, {"organization": "com.google.guava", "name": "guava", "version": "33.3.1-jre", "configurations": "default", "artifacts": [{"name": "guava", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/guava/guava/33.3.1-jre/guava-33.3.1-jre.jar"}, {"name": "guava", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/guava/guava/33.3.1-jre/guava-33.3.1-jre-sources.jar"}]}, {"organization": "com.google.guava", "name": "failureaccess", "version": "1.0.2", "configurations": "default", "artifacts": [{"name": "failureaccess", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/guava/failureaccess/1.0.2/failureaccess-1.0.2.jar"}, {"name": "failureaccess", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/guava/failureaccess/1.0.2/failureaccess-1.0.2-sources.jar"}]}, {"organization": "com.google.guava", "name": "listenablefuture", "version": "9999.0-empty-to-avoid-conflict-with-guava", "configurations": "default", "artifacts": [{"name": "listenablefuture", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar"}]}, {"organization": "com.google.errorprone", "name": "error_prone_annotations", "version": "2.33.0", "configurations": "default", "artifacts": [{"name": "error_prone_annotations", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/errorprone/error_prone_annotations/2.33.0/error_prone_annotations-2.33.0.jar"}, {"name": "error_prone_annotations", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/errorprone/error_prone_annotations/2.33.0/error_prone_annotations-2.33.0-sources.jar"}]}, {"organization": "com.google.j2objc", "name": "j2objc-annotations", "version": "3.0.0", "configurations": "default", "artifacts": [{"name": "j2objc-annotations", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/j2objc/j2objc-annotations/3.0.0/j2objc-annotations-3.0.0.jar"}, {"name": "j2objc-annotations", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/j2objc/j2objc-annotations/3.0.0/j2objc-annotations-3.0.0-sources.jar"}]}, {"organization": "com.google.http-client", "name": "google-http-client", "version": "1.45.0", "configurations": "default", "artifacts": [{"name": "google-http-client", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/http-client/google-http-client/1.45.0/google-http-client-1.45.0.jar"}, {"name": "google-http-client", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/http-client/google-http-client/1.45.0/google-http-client-1.45.0-sources.jar"}]}, {"organization": "io.grpc", "name": "grpc-context", "version": "1.67.1", "configurations": "default", "artifacts": [{"name": "grpc-context", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-context/1.67.1/grpc-context-1.67.1.jar"}, {"name": "grpc-context", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-context/1.67.1/grpc-context-1.67.1-sources.jar"}]}, {"organization": "io.opencensus", "name": "opencensus-contrib-http-util", "version": "0.31.1", "configurations": "default", "artifacts": [{"name": "opencensus-contrib-http-util", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/opencensus/opencensus-contrib-http-util/0.31.1/opencensus-contrib-http-util-0.31.1.jar"}, {"name": "opencensus-contrib-http-util", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/opencensus/opencensus-contrib-http-util/0.31.1/opencensus-contrib-http-util-0.31.1-sources.jar"}]}, {"organization": "com.google.http-client", "name": "google-http-client-jackson2", "version": "1.45.0", "configurations": "default", "artifacts": [{"name": "google-http-client-jackson2", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/http-client/google-http-client-jackson2/1.45.0/google-http-client-jackson2-1.45.0.jar"}, {"name": "google-http-client-jackson2", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/http-client/google-http-client-jackson2/1.45.0/google-http-client-jackson2-1.45.0-sources.jar"}]}, {"organization": "com.google.http-client", "name": "google-http-client-gson", "version": "1.45.0", "configurations": "default", "artifacts": [{"name": "google-http-client-gson", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/http-client/google-http-client-gson/1.45.0/google-http-client-gson-1.45.0.jar"}, {"name": "google-http-client-gson", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/http-client/google-http-client-gson/1.45.0/google-http-client-gson-1.45.0-sources.jar"}]}, {"organization": "com.google.api-client", "name": "google-api-client", "version": "2.7.0", "configurations": "default", "artifacts": [{"name": "google-api-client", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/api-client/google-api-client/2.7.0/google-api-client-2.7.0.jar"}, {"name": "google-api-client", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/api-client/google-api-client/2.7.0/google-api-client-2.7.0-sources.jar"}]}, {"organization": "commons-codec", "name": "commons-codec", "version": "1.17.1", "configurations": "default", "artifacts": [{"name": "commons-codec", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/commons-codec/commons-codec/1.17.1/commons-codec-1.17.1.jar"}, {"name": "commons-codec", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/commons-codec/commons-codec/1.17.1/commons-codec-1.17.1-sources.jar"}]}, {"organization": "com.google.oauth-client", "name": "google-oauth-client", "version": "1.36.0", "configurations": "default", "artifacts": [{"name": "google-oauth-client", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/oauth-client/google-oauth-client/1.36.0/google-oauth-client-1.36.0.jar"}, {"name": "google-oauth-client", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/oauth-client/google-oauth-client/1.36.0/google-oauth-client-1.36.0-sources.jar"}]}, {"organization": "com.google.http-client", "name": "google-http-client-apache-v2", "version": "1.45.0", "configurations": "default", "artifacts": [{"name": "google-http-client-apache-v2", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/http-client/google-http-client-apache-v2/1.45.0/google-http-client-apache-v2-1.45.0.jar"}, {"name": "google-http-client-apache-v2", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/http-client/google-http-client-apache-v2/1.45.0/google-http-client-apache-v2-1.45.0-sources.jar"}]}, {"organization": "com.google.apis", "name": "google-api-services-storage", "version": "v1-rev20240924-2.0.0", "configurations": "default", "artifacts": [{"name": "google-api-services-storage", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/apis/google-api-services-storage/v1-rev20240924-2.0.0/google-api-services-storage-v1-rev20240924-2.0.0.jar"}, {"name": "google-api-services-storage", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/apis/google-api-services-storage/v1-rev20240924-2.0.0/google-api-services-storage-v1-rev20240924-2.0.0-sources.jar"}]}, {"organization": "com.google.code.gson", "name": "gson", "version": "2.11.0", "configurations": "default", "artifacts": [{"name": "gson", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/code/gson/gson/2.11.0/gson-2.11.0.jar"}, {"name": "gson", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/code/gson/gson/2.11.0/gson-2.11.0-sources.jar"}]}, {"organization": "com.google.cloud", "name": "google-cloud-core", "version": "2.45.0", "configurations": "default", "artifacts": [{"name": "google-cloud-core", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/cloud/google-cloud-core/2.45.0/google-cloud-core-2.45.0.jar"}, {"name": "google-cloud-core", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/cloud/google-cloud-core/2.45.0/google-cloud-core-2.45.0-sources.jar"}]}, {"organization": "com.google.auto.value", "name": "auto-value-annotations", "version": "1.11.0", "configurations": "default", "artifacts": [{"name": "auto-value-annotations", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/auto/value/auto-value-annotations/1.11.0/auto-value-annotations-1.11.0.jar"}, {"name": "auto-value-annotations", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/auto/value/auto-value-annotations/1.11.0/auto-value-annotations-1.11.0-sources.jar"}]}, {"organization": "com.google.cloud", "name": "google-cloud-core-http", "version": "2.45.0", "configurations": "default", "artifacts": [{"name": "google-cloud-core-http", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/cloud/google-cloud-core-http/2.45.0/google-cloud-core-http-2.45.0.jar"}, {"name": "google-cloud-core-http", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/cloud/google-cloud-core-http/2.45.0/google-cloud-core-http-2.45.0-sources.jar"}]}, {"organization": "com.google.http-client", "name": "google-http-client-appengine", "version": "1.45.0", "configurations": "default", "artifacts": [{"name": "google-http-client-appengine", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/http-client/google-http-client-appengine/1.45.0/google-http-client-appengine-1.45.0.jar"}, {"name": "google-http-client-appengine", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/http-client/google-http-client-appengine/1.45.0/google-http-client-appengine-1.45.0-sources.jar"}]}, {"organization": "com.google.api", "name": "gax-<PERSON><PERSON><PERSON>", "version": "2.55.0", "configurations": "default", "artifacts": [{"name": "gax-<PERSON><PERSON><PERSON>", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/api/gax-httpjson/2.55.0/gax-httpjson-2.55.0.jar"}, {"name": "gax-<PERSON><PERSON><PERSON>", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/api/gax-httpjson/2.55.0/gax-httpjson-2.55.0-sources.jar"}]}, {"organization": "com.google.cloud", "name": "google-cloud-core-grpc", "version": "2.45.0", "configurations": "default", "artifacts": [{"name": "google-cloud-core-grpc", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/cloud/google-cloud-core-grpc/2.45.0/google-cloud-core-grpc-2.45.0.jar"}, {"name": "google-cloud-core-grpc", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/cloud/google-cloud-core-grpc/2.45.0/google-cloud-core-grpc-2.45.0-sources.jar"}]}, {"organization": "com.google.api", "name": "gax", "version": "2.55.0", "configurations": "default", "artifacts": [{"name": "gax", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/api/gax/2.55.0/gax-2.55.0.jar"}, {"name": "gax", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/api/gax/2.55.0/gax-2.55.0-sources.jar"}]}, {"organization": "com.google.api", "name": "gax-grpc", "version": "2.55.0", "configurations": "default", "artifacts": [{"name": "gax-grpc", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/api/gax-grpc/2.55.0/gax-grpc-2.55.0.jar"}, {"name": "gax-grpc", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/api/gax-grpc/2.55.0/gax-grpc-2.55.0-sources.jar"}]}, {"organization": "io.grpc", "name": "grpc-inprocess", "version": "1.67.1", "configurations": "default", "artifacts": [{"name": "grpc-inprocess", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-inprocess/1.67.1/grpc-inprocess-1.67.1.jar"}, {"name": "grpc-inprocess", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-inprocess/1.67.1/grpc-inprocess-1.67.1-sources.jar"}]}, {"organization": "io.grpc", "name": "grpc-alts", "version": "1.67.1", "configurations": "default", "artifacts": [{"name": "grpc-alts", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-alts/1.67.1/grpc-alts-1.67.1.jar"}, {"name": "grpc-alts", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-alts/1.67.1/grpc-alts-1.67.1-sources.jar"}]}, {"organization": "io.grpc", "name": "grpc-grpclb", "version": "1.67.1", "configurations": "default", "artifacts": [{"name": "grpc-grpclb", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-grpclb/1.67.1/grpc-grpclb-1.67.1.jar"}, {"name": "grpc-grpclb", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-grpclb/1.67.1/grpc-grpclb-1.67.1-sources.jar"}]}, {"organization": "org.conscrypt", "name": "conscrypt-openjdk-uber", "version": "2.5.2", "configurations": "default", "artifacts": [{"name": "conscrypt-openjdk-uber", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/conscrypt/conscrypt-openjdk-uber/2.5.2/conscrypt-openjdk-uber-2.5.2.jar"}, {"name": "conscrypt-openjdk-uber", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/conscrypt/conscrypt-openjdk-uber/2.5.2/conscrypt-openjdk-uber-2.5.2-sources.jar"}]}, {"organization": "io.grpc", "name": "grpc-auth", "version": "1.67.1", "configurations": "default", "artifacts": [{"name": "grpc-auth", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-auth/1.67.1/grpc-auth-1.67.1.jar"}, {"name": "grpc-auth", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-auth/1.67.1/grpc-auth-1.67.1-sources.jar"}]}, {"organization": "com.google.auth", "name": "google-auth-library-credentials", "version": "1.28.0", "configurations": "default", "artifacts": [{"name": "google-auth-library-credentials", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/auth/google-auth-library-credentials/1.28.0/google-auth-library-credentials-1.28.0.jar"}, {"name": "google-auth-library-credentials", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/auth/google-auth-library-credentials/1.28.0/google-auth-library-credentials-1.28.0-sources.jar"}]}, {"organization": "com.google.auth", "name": "google-auth-library-oauth2-http", "version": "1.28.0", "configurations": "default", "artifacts": [{"name": "google-auth-library-oauth2-http", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/auth/google-auth-library-oauth2-http/1.28.0/google-auth-library-oauth2-http-1.28.0.jar"}, {"name": "google-auth-library-oauth2-http", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/auth/google-auth-library-oauth2-http/1.28.0/google-auth-library-oauth2-http-1.28.0-sources.jar"}]}, {"organization": "com.google.api", "name": "api-common", "version": "2.38.0", "configurations": "default", "artifacts": [{"name": "api-common", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/api/api-common/2.38.0/api-common-2.38.0.jar"}, {"name": "api-common", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/api/api-common/2.38.0/api-common-2.38.0-sources.jar"}]}, {"organization": "javax.annotation", "name": "javax.annotation-api", "version": "1.3.2", "configurations": "default", "artifacts": [{"name": "javax.annotation-api", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/javax/annotation/javax.annotation-api/1.3.2/javax.annotation-api-1.3.2.jar"}, {"name": "javax.annotation-api", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/javax/annotation/javax.annotation-api/1.3.2/javax.annotation-api-1.3.2-sources.jar"}]}, {"organization": "io.opencensus", "name": "opencensus-api", "version": "0.31.1", "configurations": "default", "artifacts": [{"name": "opencensus-api", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/opencensus/opencensus-api/0.31.1/opencensus-api-0.31.1.jar"}, {"name": "opencensus-api", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/opencensus/opencensus-api/0.31.1/opencensus-api-0.31.1-sources.jar"}]}, {"organization": "com.google.api.grpc", "name": "proto-google-iam-v1", "version": "1.41.0", "configurations": "default", "artifacts": [{"name": "proto-google-iam-v1", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/api/grpc/proto-google-iam-v1/1.41.0/proto-google-iam-v1-1.41.0.jar"}, {"name": "proto-google-iam-v1", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/api/grpc/proto-google-iam-v1/1.41.0/proto-google-iam-v1-1.41.0-sources.jar"}]}, {"organization": "com.google.protobuf", "name": "protobuf-java", "version": "3.25.5", "configurations": "default", "artifacts": [{"name": "protobuf-java", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/protobuf/protobuf-java/3.25.5/protobuf-java-3.25.5.jar"}, {"name": "protobuf-java", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/protobuf/protobuf-java/3.25.5/protobuf-java-3.25.5-sources.jar"}]}, {"organization": "com.google.protobuf", "name": "protobuf-java-util", "version": "3.25.5", "configurations": "default", "artifacts": [{"name": "protobuf-java-util", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/protobuf/protobuf-java-util/3.25.5/protobuf-java-util-3.25.5.jar"}, {"name": "protobuf-java-util", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/protobuf/protobuf-java-util/3.25.5/protobuf-java-util-3.25.5-sources.jar"}]}, {"organization": "io.grpc", "name": "grpc-core", "version": "1.67.1", "configurations": "default", "artifacts": [{"name": "grpc-core", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-core/1.67.1/grpc-core-1.67.1.jar"}, {"name": "grpc-core", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-core/1.67.1/grpc-core-1.67.1-sources.jar"}]}, {"organization": "com.google.android", "name": "annotations", "version": "4.1.1.4", "configurations": "default", "artifacts": [{"name": "annotations", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/android/annotations/4.1.1.4/annotations-4.1.1.4.jar"}, {"name": "annotations", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/android/annotations/4.1.1.4/annotations-4.1.1.4-sources.jar"}]}, {"organization": "org.codehaus.mojo", "name": "animal-sniffer-annotations", "version": "1.24", "configurations": "default", "artifacts": [{"name": "animal-sniffer-annotations", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/codehaus/mojo/animal-sniffer-annotations/1.24/animal-sniffer-annotations-1.24.jar"}, {"name": "animal-sniffer-annotations", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/codehaus/mojo/animal-sniffer-annotations/1.24/animal-sniffer-annotations-1.24-sources.jar"}]}, {"organization": "io.perfmark", "name": "perfmark-api", "version": "0.27.0", "configurations": "default", "artifacts": [{"name": "perfmark-api", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/perfmark/perfmark-api/0.27.0/perfmark-api-0.27.0.jar"}, {"name": "perfmark-api", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/perfmark/perfmark-api/0.27.0/perfmark-api-0.27.0-sources.jar"}]}, {"organization": "io.grpc", "name": "grpc-protobuf", "version": "1.67.1", "configurations": "default", "artifacts": [{"name": "grpc-protobuf", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-protobuf/1.67.1/grpc-protobuf-1.67.1.jar"}, {"name": "grpc-protobuf", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-protobuf/1.67.1/grpc-protobuf-1.67.1-sources.jar"}]}, {"organization": "io.grpc", "name": "grpc-protobuf-lite", "version": "1.67.1", "configurations": "default", "artifacts": [{"name": "grpc-protobuf-lite", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-protobuf-lite/1.67.1/grpc-protobuf-lite-1.67.1.jar"}, {"name": "grpc-protobuf-lite", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-protobuf-lite/1.67.1/grpc-protobuf-lite-1.67.1-sources.jar"}]}, {"organization": "com.google.api.grpc", "name": "proto-google-common-protos", "version": "2.46.0", "configurations": "default", "artifacts": [{"name": "proto-google-common-protos", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/api/grpc/proto-google-common-protos/2.46.0/proto-google-common-protos-2.46.0.jar"}, {"name": "proto-google-common-protos", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/api/grpc/proto-google-common-protos/2.46.0/proto-google-common-protos-2.46.0-sources.jar"}]}, {"organization": "org.threeten", "name": "threetenbp", "version": "1.7.0", "configurations": "default", "artifacts": [{"name": "threetenbp", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/threeten/threetenbp/1.7.0/threetenbp-1.7.0.jar"}, {"name": "threetenbp", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/threeten/threetenbp/1.7.0/threetenbp-1.7.0-sources.jar"}]}, {"organization": "com.google.api.grpc", "name": "proto-google-cloud-storage-v2", "version": "2.43.2-beta", "configurations": "default", "artifacts": [{"name": "proto-google-cloud-storage-v2", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/api/grpc/proto-google-cloud-storage-v2/2.43.2-beta/proto-google-cloud-storage-v2-2.43.2-beta.jar"}, {"name": "proto-google-cloud-storage-v2", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/api/grpc/proto-google-cloud-storage-v2/2.43.2-beta/proto-google-cloud-storage-v2-2.43.2-beta-sources.jar"}]}, {"organization": "com.google.api.grpc", "name": "grpc-google-cloud-storage-v2", "version": "2.43.2-beta", "configurations": "default", "artifacts": [{"name": "grpc-google-cloud-storage-v2", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/api/grpc/grpc-google-cloud-storage-v2/2.43.2-beta/grpc-google-cloud-storage-v2-2.43.2-beta.jar"}, {"name": "grpc-google-cloud-storage-v2", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/api/grpc/grpc-google-cloud-storage-v2/2.43.2-beta/grpc-google-cloud-storage-v2-2.43.2-beta-sources.jar"}]}, {"organization": "com.google.api.grpc", "name": "gapic-google-cloud-storage-v2", "version": "2.43.2-beta", "configurations": "default", "artifacts": [{"name": "gapic-google-cloud-storage-v2", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/api/grpc/gapic-google-cloud-storage-v2/2.43.2-beta/gapic-google-cloud-storage-v2-2.43.2-beta.jar"}, {"name": "gapic-google-cloud-storage-v2", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/api/grpc/gapic-google-cloud-storage-v2/2.43.2-beta/gapic-google-cloud-storage-v2-2.43.2-beta-sources.jar"}]}, {"organization": "io.opentelemetry", "name": "opentelemetry-sdk", "version": "1.42.1", "configurations": "default", "artifacts": [{"name": "opentelemetry-sdk", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/opentelemetry/opentelemetry-sdk/1.42.1/opentelemetry-sdk-1.42.1.jar"}, {"name": "opentelemetry-sdk", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/opentelemetry/opentelemetry-sdk/1.42.1/opentelemetry-sdk-1.42.1-sources.jar"}]}, {"organization": "io.opentelemetry", "name": "opentelemetry-sdk-trace", "version": "1.42.1", "configurations": "default", "artifacts": [{"name": "opentelemetry-sdk-trace", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/opentelemetry/opentelemetry-sdk-trace/1.42.1/opentelemetry-sdk-trace-1.42.1.jar"}, {"name": "opentelemetry-sdk-trace", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/opentelemetry/opentelemetry-sdk-trace/1.42.1/opentelemetry-sdk-trace-1.42.1-sources.jar"}]}, {"organization": "io.opentelemetry", "name": "opentelemetry-sdk-logs", "version": "1.42.1", "configurations": "default", "artifacts": [{"name": "opentelemetry-sdk-logs", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/opentelemetry/opentelemetry-sdk-logs/1.42.1/opentelemetry-sdk-logs-1.42.1.jar"}, {"name": "opentelemetry-sdk-logs", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/opentelemetry/opentelemetry-sdk-logs/1.42.1/opentelemetry-sdk-logs-1.42.1-sources.jar"}]}, {"organization": "io.grpc", "name": "grpc-opentelemetry", "version": "1.67.1", "configurations": "default", "artifacts": [{"name": "grpc-opentelemetry", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-opentelemetry/1.67.1/grpc-opentelemetry-1.67.1.jar"}, {"name": "grpc-opentelemetry", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-opentelemetry/1.67.1/grpc-opentelemetry-1.67.1-sources.jar"}]}, {"organization": "io.opentelemetry", "name": "opentelemetry-api", "version": "1.42.1", "configurations": "default", "artifacts": [{"name": "opentelemetry-api", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/opentelemetry/opentelemetry-api/1.42.1/opentelemetry-api-1.42.1.jar"}, {"name": "opentelemetry-api", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/opentelemetry/opentelemetry-api/1.42.1/opentelemetry-api-1.42.1-sources.jar"}]}, {"organization": "io.opentelemetry", "name": "opentelemetry-context", "version": "1.42.1", "configurations": "default", "artifacts": [{"name": "opentelemetry-context", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/opentelemetry/opentelemetry-context/1.42.1/opentelemetry-context-1.42.1.jar"}, {"name": "opentelemetry-context", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/opentelemetry/opentelemetry-context/1.42.1/opentelemetry-context-1.42.1-sources.jar"}]}, {"organization": "io.opentelemetry", "name": "opentelemetry-sdk-metrics", "version": "1.42.1", "configurations": "default", "artifacts": [{"name": "opentelemetry-sdk-metrics", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/opentelemetry/opentelemetry-sdk-metrics/1.42.1/opentelemetry-sdk-metrics-1.42.1.jar"}, {"name": "opentelemetry-sdk-metrics", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/opentelemetry/opentelemetry-sdk-metrics/1.42.1/opentelemetry-sdk-metrics-1.42.1-sources.jar"}]}, {"organization": "io.opentelemetry", "name": "opentelemetry-api-incubator", "version": "1.42.1-alpha", "configurations": "default", "artifacts": [{"name": "opentelemetry-api-incubator", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/opentelemetry/opentelemetry-api-incubator/1.42.1-alpha/opentelemetry-api-incubator-1.42.1-alpha.jar"}, {"name": "opentelemetry-api-incubator", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/opentelemetry/opentelemetry-api-incubator/1.42.1-alpha/opentelemetry-api-incubator-1.42.1-alpha-sources.jar"}]}, {"organization": "io.opentelemetry", "name": "opentelemetry-sdk-common", "version": "1.42.1", "configurations": "default", "artifacts": [{"name": "opentelemetry-sdk-common", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/opentelemetry/opentelemetry-sdk-common/1.42.1/opentelemetry-sdk-common-1.42.1.jar"}, {"name": "opentelemetry-sdk-common", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/opentelemetry/opentelemetry-sdk-common/1.42.1/opentelemetry-sdk-common-1.42.1-sources.jar"}]}, {"organization": "io.opentelemetry", "name": "opentelemetry-sdk-extension-autoconfigure-spi", "version": "1.42.1", "configurations": "default", "artifacts": [{"name": "opentelemetry-sdk-extension-autoconfigure-spi", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/opentelemetry/opentelemetry-sdk-extension-autoconfigure-spi/1.42.1/opentelemetry-sdk-extension-autoconfigure-spi-1.42.1.jar"}, {"name": "opentelemetry-sdk-extension-autoconfigure-spi", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/opentelemetry/opentelemetry-sdk-extension-autoconfigure-spi/1.42.1/opentelemetry-sdk-extension-autoconfigure-spi-1.42.1-sources.jar"}]}, {"organization": "io.opentelemetry.semconv", "name": "opentelemetry-semconv", "version": "1.25.0-alpha", "configurations": "default", "artifacts": [{"name": "opentelemetry-semconv", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/opentelemetry/semconv/opentelemetry-semconv/1.25.0-alpha/opentelemetry-semconv-1.25.0-alpha.jar"}, {"name": "opentelemetry-semconv", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/opentelemetry/semconv/opentelemetry-semconv/1.25.0-alpha/opentelemetry-semconv-1.25.0-alpha-sources.jar"}]}, {"organization": "com.google.cloud.opentelemetry", "name": "exporter-metrics", "version": "0.31.0", "configurations": "default", "artifacts": [{"name": "exporter-metrics", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/cloud/opentelemetry/exporter-metrics/0.31.0/exporter-metrics-0.31.0.jar"}, {"name": "exporter-metrics", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/cloud/opentelemetry/exporter-metrics/0.31.0/exporter-metrics-0.31.0-sources.jar"}]}, {"organization": "com.google.cloud", "name": "google-cloud-monitoring", "version": "3.31.0", "configurations": "default", "artifacts": [{"name": "google-cloud-monitoring", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/cloud/google-cloud-monitoring/3.31.0/google-cloud-monitoring-3.31.0.jar"}, {"name": "google-cloud-monitoring", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/cloud/google-cloud-monitoring/3.31.0/google-cloud-monitoring-3.31.0-sources.jar"}]}, {"organization": "com.google.api.grpc", "name": "proto-google-cloud-monitoring-v3", "version": "3.31.0", "configurations": "default", "artifacts": [{"name": "proto-google-cloud-monitoring-v3", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/api/grpc/proto-google-cloud-monitoring-v3/3.31.0/proto-google-cloud-monitoring-v3-3.31.0.jar"}, {"name": "proto-google-cloud-monitoring-v3", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/api/grpc/proto-google-cloud-monitoring-v3/3.31.0/proto-google-cloud-monitoring-v3-3.31.0-sources.jar"}]}, {"organization": "io.opencensus", "name": "opencensus-proto", "version": "0.2.0", "configurations": "default", "artifacts": [{"name": "opencensus-proto", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/opencensus/opencensus-proto/0.2.0/opencensus-proto-0.2.0.jar"}, {"name": "opencensus-proto", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/opencensus/opencensus-proto/0.2.0/opencensus-proto-0.2.0-sources.jar"}]}, {"organization": "commons-logging", "name": "commons-logging", "version": "1.2", "configurations": "default", "artifacts": [{"name": "commons-logging", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/commons-logging/commons-logging/1.2/commons-logging-1.2.jar"}, {"name": "commons-logging", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/commons-logging/commons-logging/1.2/commons-logging-1.2-sources.jar"}]}, {"organization": "com.google.cloud.opentelemetry", "name": "shared-resourcemapping", "version": "0.32.0", "configurations": "default", "artifacts": [{"name": "shared-resourcemapping", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/cloud/opentelemetry/shared-resourcemapping/0.32.0/shared-resourcemapping-0.32.0.jar"}, {"name": "shared-resourcemapping", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/cloud/opentelemetry/shared-resourcemapping/0.32.0/shared-resourcemapping-0.32.0-sources.jar"}]}, {"organization": "io.opentelemetry.contrib", "name": "opentelemetry-gcp-resources", "version": "1.37.0-alpha", "configurations": "default", "artifacts": [{"name": "opentelemetry-gcp-resources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/opentelemetry/contrib/opentelemetry-gcp-resources/1.37.0-alpha/opentelemetry-gcp-resources-1.37.0-alpha.jar"}, {"name": "opentelemetry-gcp-resources", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/opentelemetry/contrib/opentelemetry-gcp-resources/1.37.0-alpha/opentelemetry-gcp-resources-1.37.0-alpha-sources.jar"}]}, {"organization": "com.google.cloud.opentelemetry", "name": "detector-resources-support", "version": "0.32.0", "configurations": "default", "artifacts": [{"name": "detector-resources-support", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/cloud/opentelemetry/detector-resources-support/0.32.0/detector-resources-support-0.32.0.jar"}, {"name": "detector-resources-support", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/cloud/opentelemetry/detector-resources-support/0.32.0/detector-resources-support-0.32.0-sources.jar"}]}, {"organization": "org.checkerframework", "name": "checker-qual", "version": "3.47.0", "configurations": "default", "artifacts": [{"name": "checker-qual", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/checkerframework/checker-qual/3.47.0/checker-qual-3.47.0.jar"}, {"name": "checker-qual", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/checkerframework/checker-qual/3.47.0/checker-qual-3.47.0-sources.jar"}]}, {"organization": "com.fasterxml.jackson.core", "name": "jackson-core", "version": "2.18.0", "configurations": "default", "artifacts": [{"name": "jackson-core", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-core/2.18.0/jackson-core-2.18.0.jar"}, {"name": "jackson-core", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-core/2.18.0/jackson-core-2.18.0-sources.jar"}]}, {"organization": "com.google.code.findbugs", "name": "jsr305", "version": "3.0.2", "configurations": "default", "artifacts": [{"name": "jsr305", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar"}, {"name": "jsr305", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2-sources.jar"}]}, {"organization": "io.grpc", "name": "grpc-api", "version": "1.67.1", "configurations": "default", "artifacts": [{"name": "grpc-api", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-api/1.67.1/grpc-api-1.67.1.jar"}, {"name": "grpc-api", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-api/1.67.1/grpc-api-1.67.1-sources.jar"}]}, {"organization": "io.grpc", "name": "grpc-netty-shaded", "version": "1.67.1", "configurations": "default", "artifacts": [{"name": "grpc-netty-shaded", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-netty-shaded/1.67.1/grpc-netty-shaded-1.67.1.jar"}, {"name": "grpc-netty-shaded", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-netty-shaded/1.67.1/grpc-netty-shaded-1.67.1-sources.jar"}]}, {"organization": "io.grpc", "name": "grpc-util", "version": "1.67.1", "configurations": "default", "artifacts": [{"name": "grpc-util", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-util/1.67.1/grpc-util-1.67.1.jar"}, {"name": "grpc-util", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-util/1.67.1/grpc-util-1.67.1-sources.jar"}]}, {"organization": "io.grpc", "name": "grpc-stub", "version": "1.67.1", "configurations": "default", "artifacts": [{"name": "grpc-stub", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-stub/1.67.1/grpc-stub-1.67.1.jar"}, {"name": "grpc-stub", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-stub/1.67.1/grpc-stub-1.67.1-sources.jar"}]}, {"organization": "io.grpc", "name": "grpc-googleapis", "version": "1.67.1", "configurations": "default", "artifacts": [{"name": "grpc-googleapis", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-googleapis/1.67.1/grpc-googleapis-1.67.1.jar"}, {"name": "grpc-googleapis", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-googleapis/1.67.1/grpc-googleapis-1.67.1-sources.jar"}]}, {"organization": "io.grpc", "name": "grpc-xds", "version": "1.67.1", "configurations": "default", "artifacts": [{"name": "grpc-xds", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-xds/1.67.1/grpc-xds-1.67.1.jar"}, {"name": "grpc-xds", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-xds/1.67.1/grpc-xds-1.67.1-sources.jar"}]}, {"organization": "io.grpc", "name": "grpc-services", "version": "1.67.1", "configurations": "default", "artifacts": [{"name": "grpc-services", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-services/1.67.1/grpc-services-1.67.1.jar"}, {"name": "grpc-services", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-services/1.67.1/grpc-services-1.67.1-sources.jar"}]}, {"organization": "com.google.re2j", "name": "re2j", "version": "1.7", "configurations": "default", "artifacts": [{"name": "re2j", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/re2j/re2j/1.7/re2j-1.7.jar"}, {"name": "re2j", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/google/re2j/re2j/1.7/re2j-1.7-sources.jar"}]}, {"organization": "io.grpc", "name": "grpc-rls", "version": "1.67.1", "configurations": "default", "artifacts": [{"name": "grpc-rls", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-rls/1.67.1/grpc-rls-1.67.1.jar"}, {"name": "grpc-rls", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/grpc/grpc-rls/1.67.1/grpc-rls-1.67.1-sources.jar"}]}, {"organization": "org.ow2.asm", "name": "asm", "version": "9.2", "configurations": "default", "artifacts": [{"name": "asm", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/ow2/asm/asm/9.2/asm-9.2.jar"}, {"name": "asm", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/ow2/asm/asm/9.2/asm-9.2-sources.jar"}]}, {"organization": "org.ow2.asm", "name": "asm-commons", "version": "9.2", "configurations": "default", "artifacts": [{"name": "asm-commons", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/ow2/asm/asm-commons/9.2/asm-commons-9.2.jar"}, {"name": "asm-commons", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/ow2/asm/asm-commons/9.2/asm-commons-9.2-sources.jar"}]}, {"organization": "org.apache.maven", "name": "maven-plugin-api", "version": "3.3.9", "configurations": "default", "artifacts": [{"name": "maven-plugin-api", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/apache/maven/maven-plugin-api/3.3.9/maven-plugin-api-3.3.9.jar"}, {"name": "maven-plugin-api", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/apache/maven/maven-plugin-api/3.3.9/maven-plugin-api-3.3.9-sources.jar"}]}, {"organization": "org.apache.commons", "name": "commons-lang3", "version": "3.16.0", "configurations": "default", "artifacts": [{"name": "commons-lang3", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/apache/commons/commons-lang3/3.16.0/commons-lang3-3.16.0.jar"}, {"name": "commons-lang3", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/apache/commons/commons-lang3/3.16.0/commons-lang3-3.16.0-sources.jar"}]}, {"organization": "org.playframework", "name": "play-exceptions", "version": "3.0.7", "configurations": "default", "artifacts": [{"name": "play-exceptions", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/playframework/play-exceptions/3.0.7/play-exceptions-3.0.7.jar"}, {"name": "play-exceptions", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/playframework/play-exceptions/3.0.7/play-exceptions-3.0.7-sources.jar"}]}, {"organization": "net.java.dev.jna", "name": "jna", "version": "5.12.1", "configurations": "default", "artifacts": [{"name": "jna", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/net/java/dev/jna/jna/5.12.1/jna-5.12.1.jar"}, {"name": "jna", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/net/java/dev/jna/jna/5.12.1/jna-5.12.1-sources.jar"}]}, {"organization": "org.playframework.twirl", "name": "twirl-parser_2.12", "version": "2.0.8", "configurations": "default", "artifacts": [{"name": "twirl-parser_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/playframework/twirl/twirl-parser_2.12/2.0.8/twirl-parser_2.12-2.0.8.jar"}, {"name": "twirl-parser_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/playframework/twirl/twirl-parser_2.12/2.0.8/twirl-parser_2.12-2.0.8-sources.jar"}]}, {"organization": "org.scalameta", "name": "parsers_2.12", "version": "4.13.2", "configurations": "default", "artifacts": [{"name": "parsers_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalameta/parsers_2.12/4.13.2/parsers_2.12-4.13.2.jar"}, {"name": "parsers_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalameta/parsers_2.12/4.13.2/parsers_2.12-4.13.2-sources.jar"}]}, {"organization": "commons-io", "name": "commons-io", "version": "2.16.1", "configurations": "default", "artifacts": [{"name": "commons-io", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/commons-io/commons-io/2.16.1/commons-io-2.16.1.jar"}, {"name": "commons-io", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/commons-io/commons-io/2.16.1/commons-io-2.16.1-sources.jar"}]}, {"organization": "org.apache.ant", "name": "ant-launcher", "version": "1.10.15", "configurations": "default", "artifacts": [{"name": "ant-launcher", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/apache/ant/ant-launcher/1.10.15/ant-launcher-1.10.15.jar"}, {"name": "ant-launcher", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/apache/ant/ant-launcher/1.10.15/ant-launcher-1.10.15-sources.jar"}]}, {"organization": "com.swoval", "name": "file-tree-views", "version": "2.1.12", "configurations": "default", "artifacts": [{"name": "file-tree-views", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/swoval/file-tree-views/2.1.12/file-tree-views-2.1.12.jar"}, {"name": "file-tree-views", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/swoval/file-tree-views/2.1.12/file-tree-views-2.1.12-sources.jar"}]}, {"organization": "io.github.classgraph", "name": "classgraph", "version": "4.8.173", "configurations": "default", "artifacts": [{"name": "classgraph", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/github/classgraph/classgraph/4.8.173/classgraph-4.8.173.jar"}, {"name": "classgraph", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/github/classgraph/classgraph/4.8.173/classgraph-4.8.173-sources.jar"}]}, {"organization": "io.apigee.trireme", "name": "trireme-kernel", "version": "0.9.4", "configurations": "default", "artifacts": [{"name": "trireme-kernel", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/apigee/trireme/trireme-kernel/0.9.4/trireme-kernel-0.9.4.jar"}, {"name": "trireme-kernel", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/apigee/trireme/trireme-kernel/0.9.4/trireme-kernel-0.9.4-sources.jar"}]}, {"organization": "org.mozilla", "name": "rhino", "version": "1.7.10", "configurations": "default", "artifacts": [{"name": "rhino", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/mozilla/rhino/1.7.10/rhino-1.7.10.jar"}, {"name": "rhino", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/mozilla/rhino/1.7.10/rhino-1.7.10-sources.jar"}]}, {"organization": "org.ow2.asm", "name": "asm-tree", "version": "9.2", "configurations": "default", "artifacts": [{"name": "asm-tree", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/ow2/asm/asm-tree/9.2/asm-tree-9.2.jar"}, {"name": "asm-tree", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/ow2/asm/asm-tree/9.2/asm-tree-9.2-sources.jar"}]}, {"organization": "org.ow2.asm", "name": "asm-analysis", "version": "9.2", "configurations": "default", "artifacts": [{"name": "asm-analysis", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/ow2/asm/asm-analysis/9.2/asm-analysis-9.2.jar"}, {"name": "asm-analysis", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/ow2/asm/asm-analysis/9.2/asm-analysis-9.2-sources.jar"}]}, {"organization": "org.apache.maven", "name": "maven-model", "version": "3.3.9", "configurations": "default", "artifacts": [{"name": "maven-model", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/apache/maven/maven-model/3.3.9/maven-model-3.3.9.jar"}, {"name": "maven-model", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/apache/maven/maven-model/3.3.9/maven-model-3.3.9-sources.jar"}]}, {"organization": "org.apache.maven", "name": "maven-artifact", "version": "3.3.9", "configurations": "default", "artifacts": [{"name": "maven-artifact", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/apache/maven/maven-artifact/3.3.9/maven-artifact-3.3.9.jar"}, {"name": "maven-artifact", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/apache/maven/maven-artifact/3.3.9/maven-artifact-3.3.9-sources.jar"}]}, {"organization": "org.eclipse.sisu", "name": "org.eclipse.sisu.plexus", "version": "0.3.2", "configurations": "default", "artifacts": [{"name": "org.eclipse.sisu.plexus", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/eclipse/sisu/org.eclipse.sisu.plexus/0.3.2/org.eclipse.sisu.plexus-0.3.2.jar"}, {"name": "org.eclipse.sisu.plexus", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/eclipse/sisu/org.eclipse.sisu.plexus/0.3.2/org.eclipse.sisu.plexus-0.3.2-sources.jar"}]}, {"organization": "org.scalameta", "name": "trees_2.12", "version": "4.13.2", "configurations": "default", "artifacts": [{"name": "trees_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalameta/trees_2.12/4.13.2/trees_2.12-4.13.2.jar"}, {"name": "trees_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalameta/trees_2.12/4.13.2/trees_2.12-4.13.2-sources.jar"}]}, {"organization": "org.codehaus.plexus", "name": "plexus-utils", "version": "3.0.22", "configurations": "default", "artifacts": [{"name": "plexus-utils", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/codehaus/plexus/plexus-utils/3.0.22/plexus-utils-3.0.22.jar"}, {"name": "plexus-utils", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/codehaus/plexus/plexus-utils/3.0.22/plexus-utils-3.0.22-sources.jar"}]}, {"organization": "javax.enterprise", "name": "cdi-api", "version": "1.0", "configurations": "default", "artifacts": [{"name": "cdi-api", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/javax/enterprise/cdi-api/1.0/cdi-api-1.0.jar"}, {"name": "cdi-api", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/javax/enterprise/cdi-api/1.0/cdi-api-1.0-sources.jar"}]}, {"organization": "org.eclipse.sisu", "name": "org.eclipse.sisu.inject", "version": "0.3.2", "configurations": "default", "artifacts": [{"name": "org.eclipse.sisu.inject", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/eclipse/sisu/org.eclipse.sisu.inject/0.3.2/org.eclipse.sisu.inject-0.3.2.jar"}, {"name": "org.eclipse.sisu.inject", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/eclipse/sisu/org.eclipse.sisu.inject/0.3.2/org.eclipse.sisu.inject-0.3.2-sources.jar"}]}, {"organization": "org.codehaus.plexus", "name": "plexus-component-annotations", "version": "1.5.5", "configurations": "default", "artifacts": [{"name": "plexus-component-annotations", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/codehaus/plexus/plexus-component-annotations/1.5.5/plexus-component-annotations-1.5.5.jar"}, {"name": "plexus-component-annotations", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/codehaus/plexus/plexus-component-annotations/1.5.5/plexus-component-annotations-1.5.5-sources.jar"}]}, {"organization": "org.codehaus.plexus", "name": "plexus-classworlds", "version": "2.5.2", "configurations": "default", "artifacts": [{"name": "plexus-classworlds", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/codehaus/plexus/plexus-classworlds/2.5.2/plexus-classworlds-2.5.2.jar"}, {"name": "plexus-classworlds", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/codehaus/plexus/plexus-classworlds/2.5.2/plexus-classworlds-2.5.2-sources.jar"}]}, {"organization": "org.scalameta", "name": "common_2.12", "version": "4.13.2", "configurations": "default", "artifacts": [{"name": "common_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalameta/common_2.12/4.13.2/common_2.12-4.13.2.jar"}, {"name": "common_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalameta/common_2.12/4.13.2/common_2.12-4.13.2-sources.jar"}]}, {"organization": "org.scalameta", "name": "io_2.12", "version": "4.13.2", "configurations": "default", "artifacts": [{"name": "io_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalameta/io_2.12/4.13.2/io_2.12-4.13.2.jar"}, {"name": "io_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalameta/io_2.12/4.13.2/io_2.12-4.13.2-sources.jar"}]}, {"organization": "javax.annotation", "name": "jsr250-api", "version": "1.0", "configurations": "default", "artifacts": [{"name": "jsr250-api", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/javax/annotation/jsr250-api/1.0/jsr250-api-1.0.jar"}, {"name": "jsr250-api", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/javax/annotation/jsr250-api/1.0/jsr250-api-1.0-sources.jar"}]}, {"organization": "javax.inject", "name": "javax.inject", "version": "1", "configurations": "default", "artifacts": [{"name": "javax.inject", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/javax/inject/javax.inject/1/javax.inject-1.jar"}, {"name": "javax.inject", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/javax/inject/javax.inject/1/javax.inject-1-sources.jar"}]}, {"organization": "com.lihaoyi", "name": "sourcecode_2.12", "version": "0.4.2", "configurations": "default", "artifacts": [{"name": "sourcecode_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/lihaoyi/sourcecode_2.12/0.4.2/sourcecode_2.12-0.4.2.jar"}, {"name": "sourcecode_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/lihaoyi/sourcecode_2.12/0.4.2/sourcecode_2.12-0.4.2-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "sbt", "version": "1.11.2", "configurations": "default", "artifacts": [{"name": "sbt", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/sbt/1.11.2/sbt-1.11.2.jar"}, {"name": "sbt", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/sbt/1.11.2/sbt-1.11.2-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "main_2.12", "version": "1.11.2", "configurations": "default", "artifacts": [{"name": "main_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/main_2.12/1.11.2/main_2.12-1.11.2.jar"}, {"name": "main_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/main_2.12/1.11.2/main_2.12-1.11.2-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "io_2.12", "version": "1.10.5", "configurations": "default", "artifacts": [{"name": "io_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/io_2.12/1.10.5/io_2.12-1.10.5.jar"}, {"name": "io_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/io_2.12/1.10.5/io_2.12-1.10.5-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "logic_2.12", "version": "1.11.2", "configurations": "default", "artifacts": [{"name": "logic_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/logic_2.12/1.11.2/logic_2.12-1.11.2.jar"}, {"name": "logic_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/logic_2.12/1.11.2/logic_2.12-1.11.2-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "actions_2.12", "version": "1.11.2", "configurations": "default", "artifacts": [{"name": "actions_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/actions_2.12/1.11.2/actions_2.12-1.11.2.jar"}, {"name": "actions_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/actions_2.12/1.11.2/actions_2.12-1.11.2-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "main-settings_2.12", "version": "1.11.2", "configurations": "default", "artifacts": [{"name": "main-settings_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/main-settings_2.12/1.11.2/main-settings_2.12-1.11.2.jar"}, {"name": "main-settings_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/main-settings_2.12/1.11.2/main-settings_2.12-1.11.2-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "run_2.12", "version": "1.11.2", "configurations": "default", "artifacts": [{"name": "run_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/run_2.12/1.11.2/run_2.12-1.11.2.jar"}, {"name": "run_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/run_2.12/1.11.2/run_2.12-1.11.2-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "command_2.12", "version": "1.11.2", "configurations": "default", "artifacts": [{"name": "command_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/command_2.12/1.11.2/command_2.12-1.11.2.jar"}, {"name": "command_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/command_2.12/1.11.2/command_2.12-1.11.2-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "collections_2.12", "version": "1.11.2", "configurations": "default", "artifacts": [{"name": "collections_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/collections_2.12/1.11.2/collections_2.12-1.11.2.jar"}, {"name": "collections_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/collections_2.12/1.11.2/collections_2.12-1.11.2-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "scripted-plugin_2.12", "version": "1.11.2", "configurations": "default", "artifacts": [{"name": "scripted-plugin_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/scripted-plugin_2.12/1.11.2/scripted-plugin_2.12-1.11.2.jar"}, {"name": "scripted-plugin_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/scripted-plugin_2.12/1.11.2/scripted-plugin_2.12-1.11.2-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "zinc-lm-integration_2.12", "version": "1.11.2", "configurations": "default", "artifacts": [{"name": "zinc-lm-integration_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-lm-integration_2.12/1.11.2/zinc-lm-integration_2.12-1.11.2.jar"}, {"name": "zinc-lm-integration_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-lm-integration_2.12/1.11.2/zinc-lm-integration_2.12-1.11.2-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "util-logging_2.12", "version": "1.11.2", "configurations": "default", "artifacts": [{"name": "util-logging_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-logging_2.12/1.11.2/util-logging_2.12-1.11.2.jar"}, {"name": "util-logging_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-logging_2.12/1.11.2/util-logging_2.12-1.11.2-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "launcher-interface", "version": "1.4.4", "configurations": "default", "artifacts": [{"name": "launcher-interface", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/launcher-interface/1.4.4/launcher-interface-1.4.4.jar"}, {"name": "launcher-interface", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/launcher-interface/1.4.4/launcher-interface-1.4.4-sources.jar"}]}, {"organization": "com.github.ben-manes.caffeine", "name": "caffeine", "version": "2.8.5", "configurations": "default", "artifacts": [{"name": "caffeine", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/github/ben-manes/caffeine/caffeine/2.8.5/caffeine-2.8.5.jar"}, {"name": "caffeine", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/github/ben-manes/caffeine/caffeine/2.8.5/caffeine-2.8.5-sources.jar"}]}, {"organization": "io.get-coursier", "name": "lm-coursier-shaded_2.12", "version": "2.1.10", "configurations": "default", "artifacts": [{"name": "lm-coursier-shaded_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/get-coursier/lm-coursier-shaded_2.12/2.1.10/lm-coursier-shaded_2.12-2.1.10.jar"}, {"name": "lm-coursier-shaded_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/io/get-coursier/lm-coursier-shaded_2.12/2.1.10/lm-coursier-shaded_2.12-2.1.10-sources.jar"}]}, {"organization": "org.apache.logging.log4j", "name": "log4j-api", "version": "2.17.1", "configurations": "default", "artifacts": [{"name": "log4j-api", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/apache/logging/log4j/log4j-api/2.17.1/log4j-api-2.17.1.jar"}, {"name": "log4j-api", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/apache/logging/log4j/log4j-api/2.17.1/log4j-api-2.17.1-sources.jar"}]}, {"organization": "org.apache.logging.log4j", "name": "log4j-core", "version": "2.17.1", "configurations": "default", "artifacts": [{"name": "log4j-core", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/apache/logging/log4j/log4j-core/2.17.1/log4j-core-2.17.1.jar"}, {"name": "log4j-core", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/apache/logging/log4j/log4j-core/2.17.1/log4j-core-2.17.1-sources.jar"}]}, {"organization": "org.apache.logging.log4j", "name": "log4j-slf4j-impl", "version": "2.17.1", "configurations": "default", "artifacts": [{"name": "log4j-slf4j-impl", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/apache/logging/log4j/log4j-slf4j-impl/2.17.1/log4j-slf4j-impl-2.17.1.jar"}, {"name": "log4j-slf4j-impl", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/apache/logging/log4j/log4j-slf4j-impl/2.17.1/log4j-slf4j-impl-2.17.1-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "librarymanagement-core_2.12", "version": "1.11.2", "configurations": "default", "artifacts": [{"name": "librarymanagement-core_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/librarymanagement-core_2.12/1.11.2/librarymanagement-core_2.12-1.11.2.jar"}, {"name": "librarymanagement-core_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/librarymanagement-core_2.12/1.11.2/librarymanagement-core_2.12-1.11.2-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "librarymanagement-ivy_2.12", "version": "1.11.2", "configurations": "default", "artifacts": [{"name": "librarymanagement-ivy_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/librarymanagement-ivy_2.12/1.11.2/librarymanagement-ivy_2.12-1.11.2.jar"}, {"name": "librarymanagement-ivy_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/librarymanagement-ivy_2.12/1.11.2/librarymanagement-ivy_2.12-1.11.2-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "compiler-interface", "version": "1.10.8", "configurations": "default", "artifacts": [{"name": "compiler-interface", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/compiler-interface/1.10.8/compiler-interface-1.10.8.jar"}, {"name": "compiler-interface", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/compiler-interface/1.10.8/compiler-interface-1.10.8-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "zinc-compile_2.12", "version": "1.10.8", "configurations": "default", "artifacts": [{"name": "zinc-compile_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-compile_2.12/1.10.8/zinc-compile_2.12-1.10.8.jar"}, {"name": "zinc-compile_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-compile_2.12/1.10.8/zinc-compile_2.12-1.10.8-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "util-relation_2.12", "version": "1.11.2", "configurations": "default", "artifacts": [{"name": "util-relation_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-relation_2.12/1.11.2/util-relation_2.12-1.11.2.jar"}, {"name": "util-relation_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-relation_2.12/1.11.2/util-relation_2.12-1.11.2-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "completion_2.12", "version": "1.11.2", "configurations": "default", "artifacts": [{"name": "completion_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/completion_2.12/1.11.2/completion_2.12-1.11.2.jar"}, {"name": "completion_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/completion_2.12/1.11.2/completion_2.12-1.11.2-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "task-system_2.12", "version": "1.11.2", "configurations": "default", "artifacts": [{"name": "task-system_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/task-system_2.12/1.11.2/task-system_2.12-1.11.2.jar"}, {"name": "task-system_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/task-system_2.12/1.11.2/task-system_2.12-1.11.2-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "tasks_2.12", "version": "1.11.2", "configurations": "default", "artifacts": [{"name": "tasks_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/tasks_2.12/1.11.2/tasks_2.12-1.11.2.jar"}, {"name": "tasks_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/tasks_2.12/1.11.2/tasks_2.12-1.11.2-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "testing_2.12", "version": "1.11.2", "configurations": "default", "artifacts": [{"name": "testing_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/testing_2.12/1.11.2/testing_2.12-1.11.2.jar"}, {"name": "testing_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/testing_2.12/1.11.2/testing_2.12-1.11.2-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "util-tracking_2.12", "version": "1.11.2", "configurations": "default", "artifacts": [{"name": "util-tracking_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-tracking_2.12/1.11.2/util-tracking_2.12-1.11.2.jar"}, {"name": "util-tracking_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-tracking_2.12/1.11.2/util-tracking_2.12-1.11.2-sources.jar"}]}, {"organization": "com.eed3si9n", "name": "sjson-new-core_2.12", "version": "0.10.1", "configurations": "default", "artifacts": [{"name": "sjson-new-core_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/sjson-new-core_2.12/0.10.1/sjson-new-core_2.12-0.10.1.jar"}, {"name": "sjson-new-core_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/sjson-new-core_2.12/0.10.1/sjson-new-core_2.12-0.10.1-sources.jar"}]}, {"organization": "com.eed3si9n", "name": "sjson-new-scal<PERSON>son_2.12", "version": "0.10.1", "configurations": "default", "artifacts": [{"name": "sjson-new-scal<PERSON>son_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/sjson-new-scalajson_2.12/0.10.1/sjson-new-scalajson_2.12-0.10.1.jar"}, {"name": "sjson-new-scal<PERSON>son_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/sjson-new-scalajson_2.12/0.10.1/sjson-new-scalajson_2.12-0.10.1-sources.jar"}]}, {"organization": "com.eed3si9n", "name": "gigahorse-apache-http_2.12", "version": "0.9.3", "configurations": "default", "artifacts": [{"name": "gigahorse-apache-http_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/gigahorse-apache-http_2.12/0.9.3/gigahorse-apache-http_2.12-0.9.3.jar"}, {"name": "gigahorse-apache-http_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/gigahorse-apache-http_2.12/0.9.3/gigahorse-apache-http_2.12-0.9.3-sources.jar"}]}, {"organization": "org.jline", "name": "jline-terminal", "version": "3.27.1", "configurations": "default", "artifacts": [{"name": "jline-terminal", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-terminal/3.27.1/jline-terminal-3.27.1.jar"}, {"name": "jline-terminal", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-terminal/3.27.1/jline-terminal-3.27.1-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "zinc-classpath_2.12", "version": "1.10.8", "configurations": "default", "artifacts": [{"name": "zinc-classpath_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-classpath_2.12/1.10.8/zinc-classpath_2.12-1.10.8.jar"}, {"name": "zinc-classpath_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-classpath_2.12/1.10.8/zinc-classpath_2.12-1.10.8-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "zinc-apiinfo_2.12", "version": "1.10.8", "configurations": "default", "artifacts": [{"name": "zinc-apiinfo_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-apiinfo_2.12/1.10.8/zinc-apiinfo_2.12-1.10.8.jar"}, {"name": "zinc-apiinfo_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-apiinfo_2.12/1.10.8/zinc-apiinfo_2.12-1.10.8-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "zinc_2.12", "version": "1.10.8", "configurations": "default", "artifacts": [{"name": "zinc_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc_2.12/1.10.8/zinc_2.12-1.10.8.jar"}, {"name": "zinc_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc_2.12/1.10.8/zinc_2.12-1.10.8-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "core-macros_2.12", "version": "1.11.2", "configurations": "default", "artifacts": [{"name": "core-macros_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/core-macros_2.12/1.11.2/core-macros_2.12-1.11.2.jar"}, {"name": "core-macros_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/core-macros_2.12/1.11.2/core-macros_2.12-1.11.2-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "util-cache_2.12", "version": "1.11.2", "configurations": "default", "artifacts": [{"name": "util-cache_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-cache_2.12/1.11.2/util-cache_2.12-1.11.2.jar"}, {"name": "util-cache_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-cache_2.12/1.11.2/util-cache_2.12-1.11.2-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "util-control_2.12", "version": "1.11.2", "configurations": "default", "artifacts": [{"name": "util-control_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-control_2.12/1.11.2/util-control_2.12-1.11.2.jar"}, {"name": "util-control_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-control_2.12/1.11.2/util-control_2.12-1.11.2-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "protocol_2.12", "version": "1.11.2", "configurations": "default", "artifacts": [{"name": "protocol_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/protocol_2.12/1.11.2/protocol_2.12-1.11.2.jar"}, {"name": "protocol_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/protocol_2.12/1.11.2/protocol_2.12-1.11.2-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "template-resolver", "version": "0.1", "configurations": "default", "artifacts": [{"name": "template-resolver", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/template-resolver/0.1/template-resolver-0.1.jar"}, {"name": "template-resolver", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/template-resolver/0.1/template-resolver-0.1-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "util-position_2.12", "version": "1.11.2", "configurations": "default", "artifacts": [{"name": "util-position_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-position_2.12/1.11.2/util-position_2.12-1.11.2.jar"}, {"name": "util-position_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-position_2.12/1.11.2/util-position_2.12-1.11.2-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "zinc-compile-core_2.12", "version": "1.10.8", "configurations": "default", "artifacts": [{"name": "zinc-compile-core_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-compile-core_2.12/1.10.8/zinc-compile-core_2.12-1.10.8.jar"}, {"name": "zinc-compile-core_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-compile-core_2.12/1.10.8/zinc-compile-core_2.12-1.10.8-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "util-interface", "version": "1.11.2", "configurations": "default", "artifacts": [{"name": "util-interface", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-interface/1.11.2/util-interface-1.11.2.jar"}, {"name": "util-interface", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/util-interface/1.11.2/util-interface-1.11.2-sources.jar"}]}, {"organization": "org.scala-sbt.jline", "name": "jline", "version": "2.14.7-sbt-9a88bc413e2b34a4580c001c654d1a7f4f65bf18", "configurations": "default", "artifacts": [{"name": "jline", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/jline/jline/2.14.7-sbt-9a88bc413e2b34a4580c001c654d1a7f4f65bf18/jline-2.14.7-sbt-9a88bc413e2b34a4580c001c654d1a7f4f65bf18.jar"}, {"name": "jline", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/jline/jline/2.14.7-sbt-9a88bc413e2b34a4580c001c654d1a7f4f65bf18/jline-2.14.7-sbt-9a88bc413e2b34a4580c001c654d1a7f4f65bf18-sources.jar"}]}, {"organization": "org.jline", "name": "jline-terminal-jni", "version": "3.27.1", "configurations": "default", "artifacts": [{"name": "jline-terminal-jni", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-terminal-jni/3.27.1/jline-terminal-jni-3.27.1.jar"}, {"name": "jline-terminal-jni", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-terminal-jni/3.27.1/jline-terminal-jni-3.27.1-sources.jar"}]}, {"organization": "org.jline", "name": "jline-native", "version": "3.27.1", "configurations": "default", "artifacts": [{"name": "jline-native", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-native/3.27.1/jline-native-3.27.1.jar"}, {"name": "jline-native", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-native/3.27.1/jline-native-3.27.1-sources.jar"}]}, {"organization": "com.lmax", "name": "disruptor", "version": "3.4.2", "configurations": "default", "artifacts": [{"name": "disruptor", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/lmax/disruptor/3.4.2/disruptor-3.4.2.jar"}, {"name": "disruptor", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/lmax/disruptor/3.4.2/disruptor-3.4.2-sources.jar"}]}, {"organization": "org.scala-lang.modules", "name": "scala-collection-compat_2.12", "version": "2.13.0", "configurations": "default", "artifacts": [{"name": "scala-collection-compat_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-collection-compat_2.12/2.13.0/scala-collection-compat_2.12-2.13.0.jar"}, {"name": "scala-collection-compat_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/modules/scala-collection-compat_2.12/2.13.0/scala-collection-compat_2.12-2.13.0-sources.jar"}]}, {"organization": "com.github.mwiede", "name": "jsch", "version": "0.2.23", "configurations": "default", "artifacts": [{"name": "jsch", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/github/mwiede/jsch/0.2.23/jsch-0.2.23.jar"}, {"name": "jsch", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/github/mwiede/jsch/0.2.23/jsch-0.2.23-sources.jar"}]}, {"organization": "org.scala-sbt.ivy", "name": "ivy", "version": "2.3.0-sbt-77cc781d727b367d3761f097d89f5a4762771d41", "configurations": "default", "artifacts": [{"name": "ivy", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/ivy/ivy/2.3.0-sbt-77cc781d727b367d3761f097d89f5a4762771d41/ivy-2.3.0-sbt-77cc781d727b367d3761f097d89f5a4762771d41.jar"}, {"name": "ivy", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/ivy/ivy/2.3.0-sbt-77cc781d727b367d3761f097d89f5a4762771d41/ivy-2.3.0-sbt-77cc781d727b367d3761f097d89f5a4762771d41-sources.jar"}]}, {"organization": "org.jline", "name": "jline-reader", "version": "3.27.1", "configurations": "default", "artifacts": [{"name": "jline-reader", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-reader/3.27.1/jline-reader-3.27.1.jar"}, {"name": "jline-reader", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-reader/3.27.1/jline-reader-3.27.1-sources.jar"}]}, {"organization": "org.jline", "name": "jline-builtins", "version": "3.27.1", "configurations": "default", "artifacts": [{"name": "jline-builtins", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-builtins/3.27.1/jline-builtins-3.27.1.jar"}, {"name": "jline-builtins", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-builtins/3.27.1/jline-builtins-3.27.1-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "test-agent", "version": "1.11.2", "configurations": "default", "artifacts": [{"name": "test-agent", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/test-agent/1.11.2/test-agent-1.11.2.jar"}, {"name": "test-agent", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/test-agent/1.11.2/test-agent-1.11.2-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "test-interface", "version": "1.0", "configurations": "default", "artifacts": [{"name": "test-interface", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/test-interface/1.0/test-interface-1.0.jar"}, {"name": "test-interface", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/test-interface/1.0/test-interface-1.0-sources.jar"}]}, {"organization": "com.eed3si9n", "name": "shaded-s<PERSON><PERSON>son_2.12", "version": "1.0.0-M4", "configurations": "default", "artifacts": [{"name": "shaded-s<PERSON><PERSON>son_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/shaded-scalajson_2.12/1.0.0-M4/shaded-scalajson_2.12-1.0.0-M4.jar"}, {"name": "shaded-s<PERSON><PERSON>son_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/shaded-scalajson_2.12/1.0.0-M4/shaded-scalajson_2.12-1.0.0-M4-sources.jar"}]}, {"organization": "com.eed3si9n", "name": "shaded-jawn-parser_2.12", "version": "1.3.2", "configurations": "default", "artifacts": [{"name": "shaded-jawn-parser_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/shaded-jawn-parser_2.12/1.3.2/shaded-jawn-parser_2.12-1.3.2.jar"}, {"name": "shaded-jawn-parser_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/shaded-jawn-parser_2.12/1.3.2/shaded-jawn-parser_2.12-1.3.2-sources.jar"}]}, {"organization": "com.eed3si9n", "name": "gigahorse-core_2.12", "version": "0.9.3", "configurations": "default", "artifacts": [{"name": "gigahorse-core_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/gigahorse-core_2.12/0.9.3/gigahorse-core_2.12-0.9.3.jar"}, {"name": "gigahorse-core_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/gigahorse-core_2.12/0.9.3/gigahorse-core_2.12-0.9.3-sources.jar"}]}, {"organization": "com.eed3si9n", "name": "shaded-apache-httpclient5", "version": "0.9.3", "configurations": "default", "artifacts": [{"name": "shaded-apache-httpclient5", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/shaded-apache-httpclient5/0.9.3/shaded-apache-httpclient5-0.9.3.jar"}, {"name": "shaded-apache-httpclient5", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/shaded-apache-httpclient5/0.9.3/shaded-apache-httpclient5-0.9.3-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "compiler-bridge_2.12", "version": "1.10.8", "configurations": "default", "artifacts": [{"name": "compiler-bridge_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/compiler-bridge_2.12/1.10.8/compiler-bridge_2.12-1.10.8.jar"}, {"name": "compiler-bridge_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/compiler-bridge_2.12/1.10.8/compiler-bridge_2.12-1.10.8-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "zinc-classfile_2.12", "version": "1.10.8", "configurations": "default", "artifacts": [{"name": "zinc-classfile_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-classfile_2.12/1.10.8/zinc-classfile_2.12-1.10.8.jar"}, {"name": "zinc-classfile_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-classfile_2.12/1.10.8/zinc-classfile_2.12-1.10.8-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "zinc-core_2.12", "version": "1.10.8", "configurations": "default", "artifacts": [{"name": "zinc-core_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-core_2.12/1.10.8/zinc-core_2.12-1.10.8.jar"}, {"name": "zinc-core_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-core_2.12/1.10.8/zinc-core_2.12-1.10.8-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "zinc-persist_2.12", "version": "1.10.8", "configurations": "default", "artifacts": [{"name": "zinc-persist_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-persist_2.12/1.10.8/zinc-persist_2.12-1.10.8.jar"}, {"name": "zinc-persist_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-persist_2.12/1.10.8/zinc-persist_2.12-1.10.8-sources.jar"}]}, {"organization": "com.eed3si9n", "name": "sjson-new-murmurhash_2.12", "version": "0.10.1", "configurations": "default", "artifacts": [{"name": "sjson-new-murmurhash_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/sjson-new-murmurhash_2.12/0.10.1/sjson-new-murmurhash_2.12-0.10.1.jar"}, {"name": "sjson-new-murmurhash_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/eed3si9n/sjson-new-murmurhash_2.12/0.10.1/sjson-new-murmurhash_2.12-0.10.1-sources.jar"}]}, {"organization": "org.scala-sbt.ipcsocket", "name": "ipcsocket", "version": "1.6.3", "configurations": "default", "artifacts": [{"name": "ipcsocket", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/ipcsocket/ipcsocket/1.6.3/ipcsocket-1.6.3.jar"}, {"name": "ipcsocket", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/ipcsocket/ipcsocket/1.6.3/ipcsocket-1.6.3-sources.jar"}]}, {"organization": "net.openhft", "name": "zero-allocation-hashing", "version": "0.16", "configurations": "default", "artifacts": [{"name": "zero-allocation-hashing", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/net/openhft/zero-allocation-hashing/0.16/zero-allocation-hashing-0.16.jar"}, {"name": "zero-allocation-hashing", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/net/openhft/zero-allocation-hashing/0.16/zero-allocation-hashing-0.16-sources.jar"}]}, {"organization": "org.fusesource.jansi", "name": "jansi", "version": "2.4.1", "configurations": "default", "artifacts": [{"name": "jansi", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/fusesource/jansi/jansi/2.4.1/jansi-2.4.1.jar"}, {"name": "jansi", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/fusesource/jansi/jansi/2.4.1/jansi-2.4.1-sources.jar"}]}, {"organization": "org.jline", "name": "jline-style", "version": "3.27.1", "configurations": "default", "artifacts": [{"name": "jline-style", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-style/3.27.1/jline-style-3.27.1.jar"}, {"name": "jline-style", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/jline/jline-style/3.27.1/jline-style-3.27.1-sources.jar"}]}, {"organization": "com.typesafe", "name": "ssl-config-core_2.12", "version": "0.6.1", "configurations": "default", "artifacts": [{"name": "ssl-config-core_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/typesafe/ssl-config-core_2.12/0.6.1/ssl-config-core_2.12-0.6.1.jar"}, {"name": "ssl-config-core_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/com/typesafe/ssl-config-core_2.12/0.6.1/ssl-config-core_2.12-0.6.1-sources.jar"}]}, {"organization": "org.reactivestreams", "name": "reactive-streams", "version": "1.0.3", "configurations": "default", "artifacts": [{"name": "reactive-streams", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/reactivestreams/reactive-streams/1.0.3/reactive-streams-1.0.3.jar"}, {"name": "reactive-streams", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/reactivestreams/reactive-streams/1.0.3/reactive-streams-1.0.3-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "zinc-persist-core-assembly", "version": "1.10.8", "configurations": "default", "artifacts": [{"name": "zinc-persist-core-assembly", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-persist-core-assembly/1.10.8/zinc-persist-core-assembly-1.10.8.jar"}, {"name": "zinc-persist-core-assembly", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/zinc-persist-core-assembly/1.10.8/zinc-persist-core-assembly-1.10.8-sources.jar"}]}, {"organization": "org.scala-sbt", "name": "sbinary_2.12", "version": "0.5.1", "configurations": "default", "artifacts": [{"name": "sbinary_2.12", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/sbinary_2.12/0.5.1/sbinary_2.12-0.5.1.jar"}, {"name": "sbinary_2.12", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-sbt/sbinary_2.12/0.5.1/sbinary_2.12-0.5.1-sources.jar"}]}, {"organization": "net.java.dev.jna", "name": "jna-platform", "version": "5.12.0", "configurations": "default", "artifacts": [{"name": "jna-platform", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/net/java/dev/jna/jna-platform/5.12.0/jna-platform-5.12.0.jar"}, {"name": "jna-platform", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/net/java/dev/jna/jna-platform/5.12.0/jna-platform-5.12.0-sources.jar"}]}, {"organization": "org.scala-lang", "name": "scala-compiler", "version": "2.12.20", "configurations": "optional", "artifacts": [{"name": "scala-compiler", "path": "/Users/<USER>/.sbt/boot/scala-2.12.20/lib/scala-compiler.jar"}, {"name": "scala-compiler", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-compiler/2.12.20/scala-compiler-2.12.20-sources.jar"}]}, {"organization": "org.scala-lang", "name": "scala-library", "version": "2.12.20", "configurations": "optional", "artifacts": [{"name": "scala-library", "path": "/Users/<USER>/.sbt/boot/scala-2.12.20/lib/scala-library.jar"}, {"name": "scala-library", "classifier": "sources", "path": "/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scala-lang/scala-library/2.12.20/scala-library-2.12.20-sources.jar"}]}]}, "tags": ["library"]}}