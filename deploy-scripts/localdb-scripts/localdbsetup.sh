#######################
#
#LOCALDB setup for testing
#########################

# newline : prints one empty line
# newline 3 : prints three empty lines
newline () {

  times=${1:-1}

  for ((i=1;i<=$times;i++)); do

    printf "\n"

  done

}

# prints colored text
print_style () {

    if [[ "$2" == "info" ]] ; then
        COLOR="96m";
    elif [[ "$2" == "success" ]] ; then
        COLOR="92m";
    elif [[ "$2" == "warning" ]] ; then
        COLOR="93m";
    elif [[ "$2" == "danger" ]] ; then
        COLOR="91m";
    else #default color
        COLOR="92m"; #success
    fi

    STARTCOLOR="\e[$COLOR";
    ENDCOLOR="\e[0m";

    newline

    printf "$STARTCOLOR%b$ENDCOLOR" "$1";
}



print_style "===============" "info";
print_style "local testing setup: starting ..." "info";
print_style "===============" "info";

newline 2;

print_style "postgres: checking if 14 is already installed ..."

VERSION=$(postgres --version)

print_style "postgres: version found: $VERSION"


if [[ "$VERSION" != *"14."* ]]; then
   print_style "postgres: uninstalling previous version ..."
   brew remove --force postgresql
   brew remove --force postgresql@13

   rm -rf /opt/homebrew/var/postgres*

   print_style "postgres: Installing version 14!"
   brew install postgresql@14
   brew services start postgresql@14
else

  print_style "postgres: 14 is already installed"

fi

newline 2;

rm -rf pg_partman
print_style "pg_partman: installing v4.5.1 ..."

newline 2;

git clone https://github.com/pgpartman/pg_partman.git

newline 2;


print_style "pg_partman: repository cloned"

newline 2;

cd pg_partman
git checkout v4.5.1
make install

sleep 15
cd ..
rm -rf pg_partman

print_style "pg_partman: installed"


print_style "pg_partman: Creating database and partman schema"

newline 2;

psql -d template1 -c "CREATE USER heaplabs WITH PASSWORD 'postgresql127';"
psql -d template1 -c "alter role heaplabs superuser;"
psql -d template1 -c "create database coldemail_dev1;"
psql -d template1 -c "\c coldemail_dev1;"
psql -d coldemail_dev1 -c "CREATE SCHEMA partman;"
psql -d coldemail_dev1 -c "CREATE EXTENSION pg_partman SCHEMA partman;"
psql -d coldemail_dev1 -c "CREATE ROLE partman_user WITH LOGIN;"
psql -d coldemail_dev1 -c "GRANT ALL ON SCHEMA partman TO partman_user;"
psql -d coldemail_dev1 -c "GRANT ALL ON ALL TABLES IN SCHEMA partman TO partman_user;"
psql -d coldemail_dev1 -c "GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA partman TO partman_user;"
psql -d coldemail_dev1 -c "GRANT EXECUTE ON ALL PROCEDURES IN SCHEMA partman TO partman_user;"
psql -d coldemail_dev1 -c "GRANT ALL ON SCHEMA partman TO partman_user;"
psql -d coldemail_dev1 -c "GRANT TEMPORARY ON DATABASE coldemail_dev1 to partman_user;"
psql -d coldemail_dev1 -c "GRANT CREATE ON DATABASE coldemail_dev1 TO partman_user;"

newline 2;


print_style "redis: checking if installed ..."

# Check if redis-cli command exists
if command -v redis-cli &> /dev/null; then
    print_style "redis: is installed on your Mac."
else
    print_style "redis: is not installed, installing via Homebrew..."

    # Install Redis using Homebrew
    brew install redis

    # Start Redis service
    brew services start redis

    # Check if installation was successful
    if ! command -v redis-server &> /dev/null; then
        print_style "redis: Error: installation failed."
        exit 1
    else
        print_style "redis: installed successfully."
    fi
fi

print_style "redis: starting in background ..."

newline 2;

redis-server --port 7777 &


newline 2;


print_style "===============" "info";
print_style "local testing setup: done!" "info";
print_style "===============" "info";

newline 2;
