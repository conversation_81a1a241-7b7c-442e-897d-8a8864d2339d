#!/bin/bash

SERVER_NAME=$1
REGION=$2

if [ -z "$SERVER_NAME" ]; then
    echo "Error: Parameter #1 = SERVER_NAME is not set."
    exit 1  # Exit with a non-zero exit code to indicate an error
fi

if [ -z "$REGION" ]; then
    echo "Error: Parameter #2 = REGION/ZONE is not set."
    exit 1  # Exit with a non-zero exit code to indicate an error
fi

# we need this as gcloud does not accept names with upper case for disk names
formatted_date_with_hyphen=$(date +%Y-%b-%d |  tr '[:upper:]' '[:lower:]')
DISK_NAME=$SERVER_NAME-disk-$formatted_date_with_hyphen
gcloud compute disks create $DISK_NAME \
    --size=10GB \
    --type=pd-balanced \
    --zone=$REGION

gcloud compute instances attach-disk $SERVER_NAME \
    --disk=$DISK_NAME  \
    --zone=$REGION

echo "now run the following commands on the server - to complete the disk setup"
echo " ============== "
echo ""
echo "gcloud compute ssh --internal-ip --zone  $REGION    $SERVER_NAME"
echo "sudo mkfs.ext4 /dev/sdb ; sudo mkdir -p  /mnt/disks/jvm-disk-main-worker ; sudo mount /dev/sdb /mnt/disks/jvm-disk-main-worker"
echo "# this command has to be triggered after mounting the disk"
echo "sudo mkdir -p /mnt/disks/jvm-disk-main-worker/ubuntu ; sudo chown ubuntu /mnt/disks/jvm-disk-main-worker/ubuntu "
echo "#setup the disk in fstab and test by mounting again" 
echo "sudo blkid /dev/sdb"
echo "#add this line to fstab" 
echo "UUID=YOUR_UUID /mnt/disks/jvm-disk-main-worker  ext4 defaults 0 2"
echo "#test by mounting again via fstab"
echo "sudo mount -a"
