Documentation about the various directories in this deploy-scripts directory

1. build - the production build script

2. deploy - the production deploy scripts (there are 3 pairs - for background worker, api and sender workers)

3. localdb-scripts - scripts to setup local DB and local redis on mac OS 
   These are one time scripts for a one time DB setup.

4. staging-db-setup-scripts - scripts to setup local DB and local redis on ubuntu linux 20.04 and 22.04
      (tested on staging - dev4 and dev5). With tweaks it worked on production build instance for 18.04
      the location of the apt repository had changed by google and postgresql - check /etc/apt/{sources.list,sources.d}


