sudo apt-get --purge autoremove postgresql*

sleep 5

sudo sh -c 'echo "deb https://apt.postgresql.org/pub/repos/apt $(lsb_release -cs)-pgdg main" > /etc/apt/sources.list.d/pgdg.list'
wget --quiet -O - https://www.postgresql.org/media/keys/ACCC4CF8.asc | sudo apt-key add -
sudo apt-get update
sudo apt-get -y install postgresql-14
sudo -u postgres psql -c "SELECT version();"

sudo apt install postgresql-server-dev-14
sudo apt-get install clang

rm -rf pg_partman
git clone https://github.com/pgpartman/pg_partman.git
cd pg_partman
git checkout v4.5.1
sudo make install
cd ..
rm -rf pg_partman

sleep 15

sudo -u postgres psql -c "create database coldemail_dev1;"
sudo -u postgres psql -c "CREATE USER heaplabs WITH PASSWORD 'postgresql127';"
sudo -u postgres psql -c "alter role heaplabs superuser;"
sudo -u postgres psql -d coldemail_dev1 -c "\c coldemail_dev1;"
sudo -u postgres psql -d coldemail_dev1 -c "CREATE SCHEMA partman;"
sudo -u postgres psql -d coldemail_dev1 -c "CREATE EXTENSION pg_partman SCHEMA partman;"
sudo -u postgres psql -d coldemail_dev1 -c "CREATE ROLE partman_user WITH LOGIN;"
sudo -u postgres psql -d coldemail_dev1 -c "GRANT ALL ON SCHEMA partman TO partman_user;"
sudo -u postgres psql -d coldemail_dev1 -c "GRANT ALL ON ALL TABLES IN SCHEMA partman TO partman_user;"
sudo -u postgres psql -d coldemail_dev1 -c "GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA partman TO partman_user;"
sudo -u postgres psql -d coldemail_dev1 -c "GRANT EXECUTE ON ALL PROCEDURES IN SCHEMA partman TO partman_user;"
sudo -u postgres psql -d coldemail_dev1 -c "GRANT ALL ON SCHEMA partman TO partman_user;"
sudo -u postgres psql -d coldemail_dev1 -c "GRANT TEMPORARY ON DATABASE coldemail_dev1 to partman_user;"
sudo -u postgres psql -d coldemail_dev1 -c "GRANT CREATE ON DATABASE coldemail_dev1 TO partman_user;"
sudo -u postgres psql -d coldemail_dev1 -c "ALTER USER postgres PASSWORD 'postgresql127';"

cd
sleep 5

if test -d /home/<USER>/redis-4.0.0; then
  echo "Redis-4.0.0 exists."
  cd redis-4.0.0
  ./src/redis-server --port 7777 &
else
  #wget https://download.redis.io/redis-stable.tar.gz
  wget https://download.redis.io/releases/redis-4.0.0.tar.gz

  tar -xzvf redis-4.0.0.tar.gz

  cd redis-4.0.0
  make

  # the compiled binary lives in redis-4.0.0/src
  # this should start the redis binary
  ./src/redis-server --port 7777 &
fi
