#!/usr/bin/env bash

SECONDS=0
printf "SENDER WORKER NEW 22-nov-2023 echo SECONDS: $SECONDS \n"

#g!/^sh/s/\(^[a-z].*\)[ \t]\+\(.*\)/sh sr_download_run_build_sendworker_api.sh \2 \1\&/
#       s/\(^[a-z].*\)[ \t]\+\(.*\)/sh sr_download_run_build_sendworker_api.sh \2 \1\&/
# 7 servers
sh sr_download_run_build_sendworker_api.sh us-central1-b ironmailone-com  &
echo "imone"
#echo "#1 took seconds : $SECONDS"

gcloud compute ssh --internal-ip --zone us-central1-b    ironmailtwo-com   -- bash -s < sr_download_run_build_sendworker_api-jvm-runtime-disk.sh &
echo "imtwo"

gcloud compute ssh --internal-ip --zone us-central1-b    ironmailthree-com   -- bash -s < sr_download_run_build_sendworker_api-jvm-runtime-disk.sh & 
echo "imthree"

gcloud compute ssh --internal-ip --zone  us-west1-b ironmailfour-com   -- bash -s < sr_download_run_build_sendworker_api-jvm-runtime-disk.sh &
echo "imfour"

gcloud compute ssh --internal-ip --zone  us-west1-b ironmailfive-com   -- bash -s < sr_download_run_build_sendworker_api-jvm-runtime-disk.sh &
echo "imfive"

#18-dec-2023 sh sr_download_run_build_sendworker_api.sh us-central1-a ironmailsix-com &
sh sr_download_run_build_sendworker_api.sh us-west1-b ironmailseven-com  &
echo "imseven"

sh sr_download_run_build_sendworker_api.sh us-west1-b ironmaileight-com &
echo "imeight"

sh sr_download_run_build_sendworker_api.sh us-west1-b ironmailnine-com &
echo "imnine"
#18-dec-2023 sh sr_download_run_build_sendworker_api.sh us-central1-a mail-app-one-com &

wait

gcloud compute ssh --internal-ip --zone us-central1-a   mail-app-thirty-one-com    -- bash -s < sr_download_run_build_sendworker_api-jvm-runtime-disk.sh &

echo "mailappthirtyone"

gcloud compute ssh --internal-ip --zone us-central1-a    mail-app-three-com    -- bash -s < sr_download_run_build_sendworker_api-jvm-runtime-disk.sh &
echo "mailappthreecom"

gcloud compute ssh --internal-ip --zone us-central1-a    mailsuccessfive-com   -- bash -s < sr_download_run_build_sendworker_api-jvm-runtime-disk.sh &
echo "mailsuccessfive"

sh sr_download_run_build_sendworker_api.sh us-west1-b    mailsurefour-com &
echo "mailsurefour"

sh sr_download_run_build_sendworker_api.sh us-central1-b mailsurenine-com &
echo "mailsurenine"


sh sr_download_run_build_sendworker_api.sh us-central1-b mailsuresix-com &

echo "mailsuresix"

#18-dec-2023 sh sr_download_run_build_sendworker_api.sh us-central1-b mailsureten-com &

sh sr_download_run_build_sendworker_api.sh us-central1-a smartreachapp-com-clone &
echo "smartreachapp-com"

#18-dec-2023 sh sr_download_run_build_sendworker_api.sh us-central1-a sr-mail-app-com-02 &

gcloud compute ssh --internal-ip --zone  us-central1-a sr-mail-five-com  -- bash -s < sr_download_run_build_sendworker_api-jvm-runtime-disk.sh &
echo "srmailfive-com"

wait 

sh sr_download_run_build_sendworker_api.sh us-central1-a sr-mail-four-com &
echo "srmailfour-com"

gcloud compute ssh --internal-ip --zone  us-central1-a sr-mail-one-com-clone -- bash -s < sr_download_run_build_sendworker_api-jvm-runtime-disk.sh &
echo "srmailonecom-com"

gcloud compute ssh --internal-ip --zone  us-central1-a sr-mail-six-com   -- bash -s < sr_download_run_build_sendworker_api-jvm-runtime-disk.sh &
echo "srmailsix-com"

#18-dec-2023 sh sr_download_run_build_sendworker_api.sh us-west1-b    sr-mail-sure-three-01 &


sh sr_download_run_build_sendworker_api.sh us-central1-a sr-mail-three-com 
echo "srmailthree-com"

sh sr_download_run_build_sendworker_api-srmailtwo.sh us-central1-a sr-mail-two-com 
echo "srmailtwo-com"

sh sr_download_run_build_sendworker_api.sh us-central1-a sr-mailer-app1-com  &
echo "srmailerapp1-com"

sh sr_download_run_build_sendworker_api.sh us-central1-a sr-mailer-one-com-clone &
#wait
echo "srmaileronecomclone-com"

#sh sr_download_run_build_sendworker_api.sh us-central1-a sr-mails-com-01 &
#18-dec-2023 sh sr_download_run_build_sendworker_api.sh us-central1-a sr-mails-com-01-clone &

sh sr_download_run_build_sendworker_api.sh us-west1-b    veri-mail-eight-com &
#wait
echo "verimaileight-com"

# 1st server with additional disk to solve disk full problem
gcloud compute ssh --internal-ip --zone us-west1-b    veri-mail-nine-com   -- bash -s < sr_download_run_build_sendworker_api-jvm-runtime-disk.sh &
echo "verimailnine-com"

echo "done 4"

#18-dec-2023 sh sr_download_run_build_sendworker_api.sh us-west1-b    veri-mail-seven-com &

sh sr_download_run_build_sendworker_api.sh us-west1-a    verimailfive-com &
echo "verimailfive-com"

wait

sh sr_download_run_build_sendworker_api.sh us-west1-b    verimailone-com &
echo "verimailone-com"

sh sr_download_run_build_sendworker_api.sh us-west1-b    verimailseven-com-2 & 
echo "verimailseven-com-2"

sh sr_download_run_build_sendworker_api.sh us-west1-b    olivemailten1-com &
echo "olivemailten1-com"

sh sr_download_run_build_sendworker_api.sh us-west1-b    ironmailnine-com &
echo "ironmailnine-com"

#commented out 14-may-2025 as DNS expiry and we chose not to renew.
# all clients moved to other servers
#sh sr_download_run_build_sendworker_api.sh us-central1-a    srmailapp-com &
#echo "srmailapp-com"

sh sr_download_run_build_sendworker_api.sh us-central1-a    srmails-com &
echo "srmails-com"

gcloud compute ssh --internal-ip --zone  us-central1-a verimailsix-com  -- bash -s < sr_download_run_build_sendworker_api-jvm-runtime-disk.sh &
echo "verimailsix-com"

echo "#5 took seconds : $SECONDS"
# this wait is needed. because the curl check immediately follows
# and if we run it before the deployment completes 
# we are in an inconsistent state when checking if the apis are up
wait
