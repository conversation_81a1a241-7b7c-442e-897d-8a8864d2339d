#!/bin/bash

# Array to store domains with invalid certificates
declare -a FAILED_DOMAINS

check_ssl_validity() {
    # Define the domain
    local domain="$1"
    local dates 
    local not_before
    local not_after
    local start
    local end
    local now
    local thirty_days_ago

    # Get the certificate dates
    dates=$(echo | openssl s_client -connect "$domain":443 -servername "$domain" 2>/dev/null | openssl x509 -noout -dates)
    not_before=$(echo "$dates" | grep notBefore | cut -d= -f2)
    not_after=$(echo "$dates" | grep notAfter | cut -d= -f2)
    
    # Check if dates were retrieved successfully
    if [ -z "$not_before" ] || [ -z "$not_after" ]; then
        echo "Failed to retrieve certificate dates for $domain."
        FAILED_DOMAINS+=("$domain")
        return
    fi

    # Convert dates to seconds since epoch
    start=$(date -d "$not_before" +%s 2>/dev/null)
    end=$(date -d "$not_after" +%s 2>/dev/null)
    now=$(date +%s)
    thirty_days_ago=$(date -d "30 days ago" +%s)
    thirty_days_ahead=$(date -d "next month" +%s)

    # Check if date conversion failed
    if [ -z "$start" ] || [ -z "$end" ]; then
        echo "Failed to parse certificate dates for $domain."
        FAILED_DOMAINS+=("$domain")
        return
    fi
    
    # Check if certificate was valid over the last 30 days
    #if [ "$start" -le "$thirty_days_ago" ] && [ "$end" -ge "$now" ]; then
    # if the end date of the certificate exceeds 30 days from now
    # it means we are within the 30 day window 
    if [ "$end" -ge "$thirty_days_ahead" ] ; then
        echo "The certificate for $domain is valid over the last 30 days."
    else
        echo "The certificate for $domain is NOT valid over the next 30 days."
        echo "Valid from: $not_before"
        echo "Valid until: $not_after"
        FAILED_DOMAINS+=("$domain")
    fi
}

# List of domains to check
check_ssl_validity "srmailapp.com"
check_ssl_validity "srmails.com"
check_ssl_validity "srmailerapp.com"
check_ssl_validity "srmailone.com"
check_ssl_validity "srmailerone.com"
check_ssl_validity "smartreachapp.com"
check_ssl_validity "srmailtwo.com"
check_ssl_validity "mailsuccesseight.com"
check_ssl_validity "mailsurefive.com"
check_ssl_validity "mailsuretwo.com"
check_ssl_validity "srmailsix.com"
check_ssl_validity "verimailsix.com"
check_ssl_validity "mailappthirtyone.com"
check_ssl_validity "mailappthree.com"
check_ssl_validity "mailsuccesstwo.com"
check_ssl_validity "verimailnine.com"
check_ssl_validity "mailsurefour.com"
check_ssl_validity "mailsuccessfive.com"
check_ssl_validity "verimailfive.com"
check_ssl_validity "mailsurenine.com"
check_ssl_validity "mailsuresix.com"
check_ssl_validity "verimailone.com"
check_ssl_validity "verimailseven.com"
check_ssl_validity "ironmailone.com"
check_ssl_validity "ironmailtwo.com"
check_ssl_validity "ironmailthree.com"
check_ssl_validity "ironmailfour.com"
check_ssl_validity "ironmailfive.com"
check_ssl_validity "ironmailseven.com"
check_ssl_validity "ironmaileight.com"
check_ssl_validity "ironmailnine.com"
check_ssl_validity "olivemailten.com"
check_ssl_validity "auth.smartreach.io"
check_ssl_validity "app.smartreach.io"
check_ssl_validity "id.smartreach.io"
check_ssl_validity "api.smartreach-mail.com"
check_ssl_validity "warmuphero.com"
check_ssl_validity "smartreach.io"
check_ssl_validity "billingapi.smartreach.io"
check_ssl_validity "api.smartreach.io"
check_ssl_validity "csd.smartreach.io"

# Summary of failed domains
echo -e "\n=== Summary of Certificate Failures ==="
if [ ${#FAILED_DOMAINS[@]} -eq 0 ]; then
    echo "No certificate failures detected."
else
    echo "The following domains had certificate issues:"
    for domain in "${FAILED_DOMAINS[@]}"; do
        echo "- $domain"
    done
fi

# Exit with non-zero status if there were failures (for GitHub Action)
if [ ${#FAILED_DOMAINS[@]} -gt 0 ]; then
    exit 1
else
    exit 0
fi
