#!/bin/bash

success_file="curl-success.out"
failure_file="curl-failure.out"
rm -f "$success_file" "$failure_file" # Clear previous output

# Function to perform curl and handle errors
do_curl() {
  local ip_dns="$1"
  local url="$2"
  local output
  output=$(curl --max-time 2 --connect-timeout 2 "$url") # Capture the output
  if [ $? -eq 0 ]; then
    echo "$ip_dns: Success" >> "$success_file"
    echo "$output" >> "$success_file"
  else
    echo "$ip_dns: Connection failed" >> "$failure_file"
    echo "$output" >> "$failure_file" #output will be empty in case of connection timeout.
  fi
}

# ************* srmailapp.com
do_curl "************* srmailapp.com" "https://srmailapp.com"

# ************** srmails.com
do_curl "************** srmails.com" "https://srmails.com"

# *********** srmailerapp.com
do_curl "*********** srmailerapp.com" "https://srmailerapp.com"

# ************** srmailone.com
do_curl "************** srmailone.com" "https://srmailone.com"

# ************ srmailerone.com
do_curl "************ srmailerone.com" "https://srmailerone.com"

# ************* smartreachapp.com
do_curl "************* smartreachapp.com" "https://smartreachapp.com"

# ************* srmailtwo.com
do_curl "************* srmailtwo.com" "https://srmailtwo.com"

# ************** mailsuccesseight.com
do_curl "************** mailsuccesseight.com" "https://mailsuccesseight.com"

# ************* mailsurefive.com
do_curl "************* mailsurefive.com" "https://mailsurefive.com"

# 35.226.80.94 mailsuretwo.com
do_curl "35.226.80.94 mailsuretwo.com" "https://mailsuretwo.com"

# 35.192.56.244 srmailsix.com
do_curl "35.192.56.244 srmailsix.com" "https://srmailsix.com"

# 35.193.7.37 verimailsix.com
do_curl "35.193.7.37 verimailsix.com" "https://verimailsix.com"

# 34.133.107.242 mailappthirtyone.com
do_curl "34.133.107.242 mailappthirtyone.com" "https://mailappthirtyone.com"

# 35.226.71.140 mailappthree.com
do_curl "35.226.71.140 mailappthree.com" "https://mailappthree.com"

# 35.230.44.38 mailsuccesstwo.com
do_curl "35.230.44.38 mailsuccesstwo.com" "https://mailsuccesstwo.com"

# 35.197.21.20 verimailnine.com
do_curl "35.197.21.20 verimailnine.com" "https://verimailnine.com"

# 35.197.21.20 verimailten.com - aliased to verimailnine
do_curl "35.197.21.20 verimailten.com" "https://verimailten.com"

# 35.230.60.36 mailsurefour.com
do_curl "35.230.60.36 mailsurefour.com" "https://mailsurefour.com"

# 35.225.82.99 mailsuccessfive.com
do_curl "35.225.82.99 mailsuccessfive.com" "https://mailsuccessfive.com"

# 35.233.177.15 verimailfive.com
do_curl "35.233.177.15 verimailfive.com" "https://verimailfive.com"

# 34.134.6.117 mailsurenine.com
do_curl "34.134.6.117 mailsurenine.com" "https://mailsurenine.com"

# 34.133.203.176 mailsuresix.com
do_curl "34.133.203.176 mailsuresix.com" "https://mailsuresix.com"

# 35.230.86.184 verimailone.com
do_curl "35.230.86.184 verimailone.com" "https://verimailone.com"

# 35.230.15.74 verimailseven.com
do_curl "35.230.15.74 verimailseven.com" "https://verimailseven.com"

# 35.193.160.17 ironmailone.com
do_curl "35.193.160.17 ironmailone.com" "https://ironmailone.com"

# 34.134.226.160 ironmailtwo.com
do_curl "34.134.226.160 ironmailtwo.com" "https://ironmailtwo.com"

# 35.226.29.101 ironmailthree.com
do_curl "35.226.29.101 ironmailthree.com" "https://ironmailthree.com"

# 35.197.95.30 ironmailfour.com
do_curl "35.197.95.30 ironmailfour.com" "https://ironmailfour.com"

# 35.197.105.24 ironmailfive.com
do_curl "35.197.105.24 ironmailfive.com" "https://ironmailfive.com"

# 35.197.109.132 ironmailseven.com
do_curl "35.197.109.132 ironmailseven.com" "https://ironmailseven.com"

# 35.227.140.201 ironmaileight.com
do_curl "35.227.140.201 ironmaileight.com" "https://ironmaileight.com"

# 35.247.64.130 ironmailnine.com
do_curl "35.247.64.130 ironmailnine.com" "https://ironmailnine.com"

# 35.197.55.64 olivemailten.com
do_curl "35.197.55.64 olivemailten.com" "https://olivemailten.com"
