#!/usr/bin/bash
#extract server names and versions
paste -d: <(grep 'Success$' curl-success.out | cut -d: -f1) <(cat curl-success.out | grep "{" | jq -r '.data.version') > version_pairs.txt

# Extract SHA1 codes and pair with servers
while IFS=: read -r server version; do
  sha1=$(echo "$version" | grep -oP '(?<=----)[^-]+(?=-[0-9]{4}-[0-9]{2}-[0-9]{2}T)')
  echo "$server:$sha1"
done < version_pairs.txt > sha1_versions.txt

    # Check if all SHA1 codes are the same
unique_sha1s=$(cut -d: -f2 sha1_versions.txt | sort | uniq | wc -l)
if [ "$unique_sha1s" -ne 1 ]; then
   echo 'Error: Git SHA1 codes are not consistent across all servers:'
   cat sha1_versions.txt
   exit 1
else
   echo 'All servers have the same Git SHA1 code:'
   head -n 1 sha1_versions.txt | cut -d: -f2
fi
