#gcloud compute ssh --zone us-central1-a sr-mail-two-8april2023-1 --internal-ip -- bash -s < script-for-deploy-server-jvm-runtime-disk.sh.27-feb-2025 $deploy_only_param

#sh sr_download_run_build_sendworker_api-srmailtwo.sh us-central1-a sr-mail-two-com &

gcloud compute ssh --internal-ip --zone  us-central1-b ironmailtwo-com          -- bash -s < script-for-disk-space.sh ironmailtwo-com             us-central1-b
gcloud compute ssh --internal-ip --zone  us-central1-b ironmailthree-com        -- bash -s < script-for-disk-space.sh ironmailthree-com           us-central1-b
gcloud compute ssh --internal-ip --zone  us-west1-b    ironmailfour-com         -- bash -s < script-for-disk-space.sh ironmailfour-com            us-west1-b   
gcloud compute ssh --internal-ip --zone  us-west1-b    ironmailfive-com         -- bash -s < script-for-disk-space.sh ironmailfive-com            us-west1-b   
gcloud compute ssh --internal-ip --zone  us-west1-b    ironmailseven-com        -- bash -s < script-for-disk-space.sh ironmailseven-com           us-west1-b   
gcloud compute ssh --internal-ip --zone  us-west1-b    ironmaileight-com        -- bash -s < script-for-disk-space.sh ironmaileight-com           us-west1-b   
gcloud compute ssh --internal-ip --zone  us-west1-b    ironmailnine-com         -- bash -s < script-for-disk-space.sh ironmailnine-com            us-west1-b   
gcloud compute ssh --internal-ip --zone  us-central1-a mail-app-thirty-one-com  -- bash -s < script-for-disk-space.sh mail-app-thirty-one-com     us-central1-a
gcloud compute ssh --internal-ip --zone  us-central1-a mail-app-three-com       -- bash -s < script-for-disk-space.sh mail-app-three-com          us-central1-a
gcloud compute ssh --internal-ip --zone  us-central1-a mailsuccessfive-com      -- bash -s < script-for-disk-space.sh mailsuccessfive-com         us-central1-a
gcloud compute ssh --internal-ip --zone  us-west1-b    mailsurefour-com         -- bash -s < script-for-disk-space.sh mailsurefour-com            us-west1-b   
gcloud compute ssh --internal-ip --zone  us-central1-b mailsurenine-com         -- bash -s < script-for-disk-space.sh mailsurenine-com            us-central1-b
gcloud compute ssh --internal-ip --zone  us-central1-b mailsuresix-com          -- bash -s < script-for-disk-space.sh mailsuresix-com             us-central1-b
gcloud compute ssh --internal-ip --zone  us-central1-a smartreachapp-com-clone  -- bash -s < script-for-disk-space.sh smartreachapp-com-clone     us-central1-a
gcloud compute ssh --internal-ip --zone  us-central1-a sr-mail-five-com         -- bash -s < script-for-disk-space.sh sr-mail-five-com            us-central1-a
gcloud compute ssh --internal-ip --zone  us-central1-a sr-mail-four-com         -- bash -s < script-for-disk-space.sh sr-mail-four-com            us-central1-a
gcloud compute ssh --internal-ip --zone  us-central1-a sr-mail-one-com-clone    -- bash -s < script-for-disk-space.sh sr-mail-one-com-clone       us-central1-a
gcloud compute ssh --internal-ip --zone  us-central1-a sr-mail-six-com          -- bash -s < script-for-disk-space.sh sr-mail-six-com             us-central1-a
gcloud compute ssh --internal-ip --zone  us-central1-a sr-mail-three-com        -- bash -s < script-for-disk-space.sh sr-mail-three-com           us-central1-a
gcloud compute ssh --internal-ip --zone  us-central1-a sr-mailer-app1-com        -- bash -s < script-for-disk-space.sh sr-mailer-app-com           us-central1-a
gcloud compute ssh --internal-ip --zone  us-central1-a sr-mailer-one-com-clone  -- bash -s < script-for-disk-space.sh sr-mailer-one-com-clone     us-central1-a
gcloud compute ssh --internal-ip --zone  us-west1-b    veri-mail-eight-com      -- bash -s < script-for-disk-space.sh veri-mail-eight-com         us-west1-b   
gcloud compute ssh --internal-ip --zone  us-west1-b    veri-mail-nine-com       -- bash -s < script-for-disk-space.sh veri-mail-nine-com          us-west1-b   
gcloud compute ssh --internal-ip --zone  us-west1-a    verimailfive-com         -- bash -s < script-for-disk-space.sh verimailfive-com            us-west1-a   
gcloud compute ssh --internal-ip --zone  us-west1-b    verimailone-com          -- bash -s < script-for-disk-space.sh verimailone-com             us-west1-b   
gcloud compute ssh --internal-ip --zone  us-west1-b    verimailseven-com-2      -- bash -s < script-for-disk-space.sh verimailseven-com-2         us-west1-b   
