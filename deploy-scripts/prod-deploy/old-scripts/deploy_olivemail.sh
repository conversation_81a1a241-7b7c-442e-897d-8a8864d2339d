
#sleep 1 # shutdown 11-nov-2024
#sh sr_download_run_build_sendworker_api.sh us-west1-b    olivemaileight1-com       &
#sleep 1
#sh sr_download_run_build_sendworker_api.sh us-west1-b    olivemailfive1-com        &
#sleep 1
# 11-nov-2024 tid  14263 still sending
#shutdown hence commented out 2-dec-2024
#sh sr_download_run_build_sendworker_api.sh us-west1-b    olivemailfour1-com        &
#sleep 1 #shutdown 11-nov-2024
#sh sr_download_run_build_sendworker_api.sh us-central1-a    olivemailnine1-com     &
#sleep 1 #shutdown 11-nov-2024
#sh sr_download_run_build_sendworker_api.sh us-central1-a    olivemailone1-com      &
#sleep 1
#shutdown hence commented out 2-dec-2024
#sh sr_download_run_build_sendworker_api.sh us-west1-b    olivemailseven1-com      &
#sleep 1
#sh sr_download_run_build_sendworker_api.sh us-west1-b    olivemailten1-com      &
#sleep 1
#sh sr_download_run_build_sendworker_api.sh us-central1-a    verimailthree-com &
#wait
#sh sr_download_run_build_sendworker_api.sh us-central1-a    mailsureseven-com &
#sleep 1
sh sr_download_run_build_sendworker_api.sh us-west1-b    ironmailnine-com    & 
sleep 1

#shutdown hence commented out 2-dec-2024
#sh sr_download_run_build_sendworker_api.sh us-west1-b    olivemailthree1-com      &
#sleep 1
#sh sr_download_run_build_sendworker_api.sh us-central1-a    verimaileight-com &
#sleep 1
#shutdown hence commented out 2-dec-2024
#sh sr_download_run_build_sendworker_api.sh us-central1-a    olivemailtwo1-com      &
#sleep 1
#shutdown hence commented out 2-dec-2024
#sh sr_download_run_build_sendworker_api.sh us-central1-a    olivemailsix1-com      &
#sleep 1
#sh sr_download_run_build_sendworker_api.sh us-central1-a    ironmailten-com      &
#sleep 1

sh sr_download_run_build_sendworker_api.sh us-central1-a    srmailapp-com      &
sleep 1
sh sr_download_run_build_sendworker_api.sh us-central1-a    srmails-com      &
wait
