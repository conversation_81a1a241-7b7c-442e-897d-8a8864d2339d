
#shutdown hence commented out 2-dec-2024
#sh sr_download_run_build_sendworker_api.sh us-central1-a    platinummaileight1-com &
#sleep 1
#shutdown hence commented out 2-dec-2024
#sh sr_download_run_build_sendworker_api.sh us-west1-b    platinummailfive1-com &
#sleep 1
#shutdown hence commented out 2-dec-2024
#sh sr_download_run_build_sendworker_api.sh us-west1-b    platinummailfour1-com &
#sleep 1
#sh sr_download_run_build_sendworker_api.sh us-central1-a    platinummailnine1-com &
#sleep 1
#shutdown hence commented out 2-dec-2024
#sh sr_download_run_build_sendworker_api.sh us-central1-a    platinummailone1-com &
#sleep 1

#shutdown hence commented out 2-dec-2024
#sh sr_download_run_build_sendworker_api.sh us-west1-b    platinummailseven1-com &
#sleep 1 # shutdown 11-nov-2024
#sh sr_download_run_build_sendworker_api.sh us-west1-b    platinummailsix1-com &
#sleep 1
# still sending tid 9475

#shutdown hence commented out 2-dec-2024
#sh sr_download_run_build_sendworker_api.sh us-central1-a    platinummailten1-com &
#sleep 1
# still sending tid 17269
#shutdown hence commented out 2-dec-2024
#sh sr_download_run_build_sendworker_api.sh us-west1-b    platinummailthree1-com &
#sleep 1
# still sending tid 15719
#shutdown hence commented out 2-dec-2024
#sh sr_download_run_build_sendworker_api.sh us-central1-a    platinummailtwo1-com &
#wait


#sh sr_download_run_build_sendworker_api.sh us-west1-b    mailsureten1-com       &
#sleep 1
#sh sr_download_run_build_sendworker_api.sh us-west1-b    mailsureeight-com       &
#sleep 1
sh sr_download_run_build_sendworker_api.sh us-central1-a    verimailsix-com       &

#sleep 1
#sh sr_download_run_build_sendworker_api.sh us-central1-a    mailsuccesssix-com-13-sep-2024       &
wait
