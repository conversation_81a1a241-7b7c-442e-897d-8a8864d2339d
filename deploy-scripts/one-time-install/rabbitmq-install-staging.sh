#########################################
#
#RabbitMq installation script for staging
#########################################

# newline : prints one empty line
# newline 3 : prints three empty lines
newline () {

  times=${1:-1}

  for ((i=1;i<=$times;i++)); do

    printf "\n"

  done

}

# prints colored text
print_style () {

    if [[ "$2" == "info" ]] ; then
        COLOR="96m";
    elif [[ "$2" == "success" ]] ; then
        COLOR="92m";
    elif [[ "$2" == "warning" ]] ; then
        COLOR="93m";
    elif [[ "$2" == "danger" ]] ; then
        COLOR="91m";
    else #default color
        COLOR="92m"; #success
    fi

    STARTCOLOR="\e[$COLOR";
    ENDCOLOR="\e[0m";

    newline

    printf "$STARTCOLOR%b$ENDCOLOR" "$1";
}

print_style "=================================" "info";
print_style "RabbitMq installation started ..." "info";
print_style "=================================" "info";
newline 1

print_style "Install Essential Dependencies and Enabling apt HTTPS Transport" "info"

newline 1
sudo apt update && sudo apt install wget -y
sudo apt install apt-transport-https -y

print_style "Adding Repository Signing Keys" "info"
newline 1

wget -O- https://dl.bintray.com/rabbitmq/Keys/rabbitmq-release-signing-key.asc | sudo apt-key add -
wget -O- https://www.rabbitmq.com/rabbitmq-release-signing-key.asc | sudo apt-key add -

print_style "Adding a Source List File" "info"
newline 1

echo "deb https://dl.bintray.com/rabbitmq-erlang/debian focal erlang-22.x" | sudo tee /etc/apt/sources.list.d/rabbitmq.list

print_style "Update package indices" "info"
newline 1

## Update package indices
sudo apt update

print_style "Installing rabbitmq-server and its dependencies" "info"
newline 1

## Install rabbitmq-server and its dependencies
sudo apt install rabbitmq-server

#Enabling the RabbitMQ Management Dashboard.
sudo rabbitmq-plugins enable rabbitmq_management