#!/usr/bin/env bash

echo "starting build server ..."

gcloud compute instances start --zone us-central1-a sr-api-main-1

echo "started build server ..."

sleep 2

echo "going for compilation ..."

gcloud compute ssh --zone us-central1-a sr-api-main-1 --internal-ip <<'ENDSSH'

    export TERM=xterm-256color
    RED=$(tput setaf 1)
    GREEN=$(tput setaf 2)
    LIME_YELLOW=$(tput setaf 190)
    CYAN=$(tput setaf 6)
    NORMAL=$(tput sgr0)
    DIVIDER="\n\n###################################################################\n\n"

    printf "free -h \n"

    free -h


    printf "\nps -ewf | grep java \n\n"

    ps -ewf | grep java


    REPO=smartreach
    BASE_PATH=/home/<USER>
    SCALA_VERSION=scala-2.13
    JAR_PATH=$BASE_PATH/$REPO/coldemail/target/$SCALA_VERSION/coldemail.jar
    RESOURCES_FOLDER_PATH=$BASE_PATH/$REPO/coldemail/src/main/resources
    APP_CONFIG=$RESOURCES_FOLDER_PATH/production.conf
    SECRETS_PATH=$RESOURCES_FOLDER_PATH/load_secret_scripts/load_secrets_worker.sh
    CSV_EXE_PATH=$RESOURCES_FOLDER_PATH/sr_csv_parser/csv2_ubuntu.exe
    mkdir -p $BASE_PATH/$REPO/target


    printf "\ncd $REPO \n"

    cd $REPO


    printf "\npwd \n"

    pwd


    printf "\ngit pull \n"

    eval "$(ssh-agent -s)"

    ssh-add ~/.ssh/github_sr_backend_build_server_prod_bastion

    # edit the statement below and add "user:password@" so it auto authenticates
    # e.g. git pull "https://user1:<EMAIL>/heaplabs/smartreach.git" develop
    #git pull "https://mukkoju:<EMAIL>/heaplabs/smartreach_backend.git" master
#    git checkout sk/moving-session-to-common-auth
#    git pull origin sk/moving-session-to-common-auth 
    git checkout master
    git pull origin  master

    #git pull "**************:heaplabs/smartreach_backend.git" sk/moving-session-to-common-auth:sk/moving-session-to-common-auth

    echo "sbt clean `pwd`"
    sbt clean

    echo "sbt coldemail/clean `pwd`"
    sbt coldemail/clean


    printf "${CYAN} $DIVIDER $HOSTNAME :: sbt -J-Xms5120m -J-Xmx5120m coldemail/assembly $DIVIDER ${NORMAL}"

    time sbt -J-Xms5120m -J-Xmx5120m coldemail/assembly


    printf "\nstart cloud storage upload\n"


    FOLDER_NAME=`date +%s`

    printf "\nupload latest build: $FOLDER_NAME\n"


    #NOTE:
    #in future we can do
    #tar -cvzf release..tar.zip $BASE_PATH/$REPO/coldemail/$RESOURCES_FOLDER/
    #gsutil -m cp / gs://srjars/$FOLDER_NAME/
    #when deploying - you will need to untar.
    #this will save time in the deployment.

    if [ ! -f "$JAR_PATH" ]; then
       printf "${RED} $DIVIDER $JAR_PATH NOT FOUND - BUILD STOPPED  $DIVIDER"
       exit
    elif [ ! -f "$APP_CONFIG" ]; then
       printf "${RED} $DIVIDER $APP_CONFIG NOT FOUND - BUILD STOPPED  $DIVIDER"
       exit
    elif [ ! -f "$SECRETS_PATH" ]; then
       printf "${RED} $DIVIDER $SECRETS_PATH NOT FOUND - BUILD STOPPED  $DIVIDER"
       exit
    elif [ ! -f "$CSV_EXE_PATH" ]; then
       printf "${RED} $DIVIDER $CSV_EXE_PATH NOT FOUND - BUILD STOPPED  $DIVIDER"
       exit
    else

    RELEASES_FOLDER=$BASE_PATH/sr-releases
    
    if [ ! -d $RELEASES_FOLDER ]; then
            echo "creating releases folder: $RELEASES_FOLDER"
            mkdir -p $RELEASES_FOLDER
    fi

    GCS_FOLDER=srjars
    BUILD_DATE=`date +%y-%m-%d-T-%H-%M-%S`
    BUILD_SUB_FOLDER=$GCS_FOLDER-$BUILD_DATE
    SCALA_VERSION=scala-2.13
    mkdir  -p   $RELEASES_FOLDER/$BUILD_SUB_FOLDER
    cp          $JAR_PATH                $RELEASES_FOLDER/$BUILD_SUB_FOLDER/
    cp     -r   $RESOURCES_FOLDER_PATH/  $RELEASES_FOLDER/$BUILD_SUB_FOLDER/
    cd          $RELEASES_FOLDER
    tar    -cvf $BUILD_SUB_FOLDER.tar $BUILD_SUB_FOLDER/
    gsutil  cp  $RELEASES_FOLDER/$BUILD_SUB_FOLDER.tar gs://$GCS_FOLDER

      #22nov2023 echo "gsutil cp $JAR_PATH gs://srjars/$FOLDER_NAME/coldemail.jar"
      #22nov2023 gsutil cp $JAR_PATH gs://srjars/$FOLDER_NAME/coldemail.jar

      #22nov2023 echo "gsutil -m cp -r $RESOURCES_FOLDER_PATH/ gs://srjars/$FOLDER_NAME/"
      #22nov2023 gsutil -m cp -r $RESOURCES_FOLDER_PATH/ gs://srjars/$FOLDER_NAME/

      #22nov2023 echo "pwd: `pwd`"

      printf "${GREEN} $DIVIDER $HOSTNAME :: UPLOAD BUILD DONE!!! EXITING $DIVIDER"
   fi
 
ENDSSH

echo "stopping build server ..."

gcloud compute instances stop --zone us-central1-a sr-api-main-1
