#!/usr/bin/bash

BASE_PATH=/home/<USER>
unset GCS_FOLDER
if [ "$HOSTNAME" == "sr-staging" ] ; then
	GCS_FOLDER=dev-sreml-com
fi

if [ "$HOSTNAME" == "sr-staging-2" ] ; then
	GCS_FOLDER=dev2-sreml-com
fi

if [ "$HOSTNAME" == "sr-staging-3" ] ; then
	GCS_FOLDER=dev3-sreml-com
fi

if [ "$HOSTNAME" == "sr-staging-4" ] ; then
	GCS_FOLDER=dev4-sreml-com
fi

if [ "$HOSTNAME" == "sr-staging-5" ] ; then
	GCS_FOLDER=dev-sreml-com
fi


usage()
{
	echo "Usage: deploy-build.sh  [ -f | --gcs_folder <google-cloud-storage-folder> ]"
	exit 0
}

# getopt - after a paramter - no colon means does not take any addition params
#                             single colon means takes a param compulsory
PARSED_ARGUMENTS=$(getopt -a -n deploy-build.sh -o f: --longoptions gcs_folder: -- "$@")
VALID_ARGUMENTS=$?
if [ "$VALID_ARGUMENTS" != "0" ]; then
	usage
fi

echo "PARSED_ARGUMENTS is $PARSED_ARGUMENTS"
eval set -- "$PARSED_ARGUMENTS"
while :
do
	case $1 in
		-f | --gcs_folder) GCS_FOLDER="$2" ;
			shift 2;
			echo "GCS_FOLDER: $GCS_FOLDER";
			;;
		--) shift; break;;
		*) echo "unexpected option : $1 "
			usage ;;
	esac
done

if [ -z "$GCS_FOLDER" ]; then
	echo "GCS_FOLDER could not be automatically determined and was not passed as a param - exiting"
	echo "          "
	echo "          "
	usage
	exit 1
fi


#BUILD_SUB_FOLDER=$(gsutil ls -l gs://$GCS_FOLDER | sort  | tail -n 1 | cut -d '/' -f 4)
# using tar file format
#BUILD_TAR_FILE=$(gsutil ls -l gs://$GCS_FOLDER | sort | tail -n 3 | head -n 1 | cut  -d ' ' -f 6 | cut -d '/' -f 4)
BUILD_TAR_FILE=$(gsutil ls  gs://$GCS_FOLDER | sort | tail -n 1 |  cut -d '/' -f 4)
echo "deploying latest build from  gs://$GCS_FOLDER  : $BUILD_SUB_FOLDER "
rm -rf $BASE_PATH/srbuild1 $BASE_PATH/srbuild

mkdir -p $BASE_PATH/srbuild1/
mkdir -p $BASE_PATH/srlogs/
gsutil -m cp -r gs://$GCS_FOLDER/$BUILD_TAR_FILE $BASE_PATH/srbuild1/
BUILD_SUB_FOLDER=${BUILD_TAR_FILE%.tar} # chop off the extension
echo "tar -xvf $BASE_PATH/srbuild1/$BUILD_TAR_FILE --directory $BASE_PATH/srbuild1/"
tar -xvf $BASE_PATH/srbuild1/$BUILD_TAR_FILE --directory $BASE_PATH/srbuild1/
mv $BASE_PATH/srbuild1/$BUILD_SUB_FOLDER $BASE_PATH/srbuild

SR_CERTS_DIR=$BASE_PATH/staging-db-certs
if [ ! -d "$SR_CERTS_DIR" ];  then
  mkdir $BASE_PATH/staging-db-certs
fi

gcloud secrets versions access latest --secret=DB_CERTS --out-file=$BASE_PATH/staging-db-certs/staging-dbcerts.tar.gz

cd $SR_CERTS_DIR
tar -xvzf staging-dbcerts.tar.gz
SR_STAGING_DB_CERT=$SR_CERTS_DIR/server-ca.pem
if [ ! -f "$SR_STAGING_DB_CERT" ]; then
	chmod 600 $SR_CERTS_DIR/{client-key.pem,client-key.pk8}
fi

## Now we want to start the java processes

### step - kill all the old java processes
printf "\nkillall -9 java \n"
killall -9 java

TRUST_STORE=/home/<USER>/ssl_16-aug-2022/certs/java-cacerts
TRUST_STORE_PASSWORD=changeit
DEPLOY_PATH=/home/<USER>/srbuild
JAR_PATH=$DEPLOY_PATH/coldemail.jar
APP_CONFIG=$DEPLOY_PATH/resources/staging.conf
LOG_CONFIG=$DEPLOY_PATH/resources/production-logback.xml
#CSV_EXE_PATH=/home/<USER>/srbuild/resources/sr_csv_parser/csv2_ubuntu.exe

#SECRETS_PATH=/home/<USER>/smartreach/coldemail/$RESOURCES_FOLDER/load_secret_scripts/load_secrets_worker.sh

unset SECRETS_PATH
if [ "$HOSTNAME" == "sr-staging" ] ; then
	SECRETS_PATH=$DEPLOY_PATH/resources/load_secret_scripts/load_secrets_worker.sh
fi

if [ "$HOSTNAME" == "sr-staging-2" ] ; then
	SECRETS_PATH=$DEPLOY_PATH/resources/load_secret_scripts/load_secrets_worker_dev2.sh
fi

if [ "$HOSTNAME" == "sr-staging-3" ] ; then
	SECRETS_PATH=$DEPLOY_PATH/resources/load_secret_scripts/load_secrets_worker_dev3.sh
fi

if [ "$HOSTNAME" == "sr-staging-4" ] ; then
	SECRETS_PATH=$DEPLOY_PATH/resources/load_secret_scripts/load_secrets_worker_dev4.sh
fi

if [ "$HOSTNAME" == "sr-staging-5" ] ; then
	SECRETS_PATH=$DEPLOY_PATH/resources/load_secret_scripts/load_secrets_worker_dev5.sh
fi

if [ -z "$SECRETS_PATH" ]; then
	echo "SECRETS_PATH could not be automatically determined "
	echo "          "
	echo "          "
	usage
	exit 1
fi

source $SECRETS_PATH

SMARTREACH_CLIENT_ID="*************"
SMARTREACH_CLIENT_SECRET="***********"



printf "start worker \n"
nohup java -Djavax.net.ssl.trustStore=$TRUST_STORE -Djavax.net.ssl.trustStorePassword=$TRUST_STORE_PASSWORD -Dconfig.file=$APP_CONFIG -Dlogger.file=$LOG_CONFIG -cp $JAR_PATH utils.App worker > /dev/null 2>&1 &

printf "start send_worker \n"
nohup java -Djavax.net.ssl.trustStore=$TRUST_STORE -Djavax.net.ssl.trustStorePassword=$TRUST_STORE_PASSWORD -Dconfig.file=$APP_CONFIG -Dlogger.file=$LOG_CONFIG -cp $JAR_PATH utils.App send_worker > /dev/null 2>&1 &

printf "start reply_tracker \n"
nohup java -Djavax.net.ssl.trustStore=$TRUST_STORE -Djavax.net.ssl.trustStorePassword=$TRUST_STORE_PASSWORD -Dconfig.file=$APP_CONFIG -Dlogger.file=$LOG_CONFIG -cp $JAR_PATH utils.App reply_tracker > /dev/null 2>&1 &

printf "start email_scheduler_worker \n"
nohup java -Djavax.net.ssl.trustStore=$TRUST_STORE -Djavax.net.ssl.trustStorePassword=$TRUST_STORE_PASSWORD -Dconfig.file=$APP_CONFIG -Dlogger.file=$LOG_CONFIG -cp $JAR_PATH utils.App email_scheduler_worker > /dev/null 2>&1 &

printf "start scheduler \n"
nohup java -Djavax.net.ssl.trustStore=$TRUST_STORE -Djavax.net.ssl.trustStorePassword=$TRUST_STORE_PASSWORD -Dconfig.file=$APP_CONFIG -Dlogger.file=$LOG_CONFIG -cp $JAR_PATH utils.App scheduler > /dev/null 2>&1 &

printf "start api \n"
nohup java -Djavax.net.ssl.trustStore=$TRUST_STORE -Djavax.net.ssl.trustStorePassword=$TRUST_STORE_PASSWORD -Dconfig.file=$APP_CONFIG -Dpidfile.path=/dev/null -Dlogger.file=$LOG_CONFIG -Dhttp.port=5000 -jar $JAR_PATH > /dev/null 2>&1 &


