// use actix_web:: {web, App, HttpRequest, HttpResponse, HttpServer, Responder };
// use actix_web:: dev :: Server;
// use std::net::TcpListener;

pub mod configuration;
pub mod routes;
pub mod email_client;
pub mod startup;
pub mod telemetry;
pub mod domain;
//async fn health_check(req: HttpRequest) -> impl Responder 
//async fn health_check(_req: HttpRequest) -> impl Responder {
//    //todo! ()
//    HttpResponse::Ok().finish()
//}

//#[derive(serde::Deserialize)]
//struct FormData {
//    email: String,
//    name: String
//}

//async fn subscribe(_form: web::Form<FormData>) -> HttpResponse {
//    HttpResponse::Ok().finish()
//}


/*
 * we have written this fn in such a way
 * that it can be used from main
 * as well as from tests
 * to spawn a server
 */ 
//pub fn run(listener: TcpListener) -> Result<Server, std::io::Error> {
//    let server = HttpServer::new( || {
//        App::new() 
//            // rs = rust, ce = cold email
//            .route("/rs_ce_health_check", web::get().to(health_check))
//            .route("/subscriptions", web::post().to(subscribe))
//    })
//    .listen(listener)?
//    .run();
//
//    Ok(server)
//}

