//! src/email_client.rs

use crate::domain::SubscriberEmail;
use reqwest::Client;
use secrecy::Secret;
use secrecy::ExposeSecret;

#[derive(Clone)]
pub struct EmailClient {
    http_client: Client,
    base_url: String,
    sender: SubscriberEmail,
    authorization_token: Secret<String>,
}

impl EmailClient {

    pub fn new(base_url: String,
               sender: SubscriberEmail,
               authorization_token: Secret<String>
    ) -> Self {
        let http_client = Client::builder()
            .timeout(std::time::Duration::from_secs(10))
            .build()
            .unwrap();
        Self {
            http_client,
            base_url,
            sender,
            authorization_token,
        }
    }

    pub async fn send_email(
        &self,
        recipient: SubscriberEmail,
        subject: &str,
        html_content: &str,
        text_content: &str,
    ) -> Result<(), reqwest::Error> {
        //todo!()
        //Ok(())
        let url = format!("{}/email", self.base_url);
        //let request_body = SendEmailRequest {
        //    from : self.sender.as_ref().to_owned(),
        //    to : recipient.as_ref().to_owned(),
        //    subject: subject.to_owned(),
        //    html_body: html_content.to_owned(),
        //    text_body: text_content.to_owned(),
        //};
        let request_body = SendEmailRequest {
            from : self.sender.as_ref(),
            to : recipient.as_ref(),
            subject: subject,
            html_body: html_content,
            text_body: text_content,
        };
        let builder = self
            .http_client
            .post(&url)
            .header(
                "X-Postmark-Server-Token",
                self.authorization_token.expose_secret()
            )
            .json(&request_body)
            .send()
            .await?
            .error_for_status()?;

        Ok(())
    }

}

#[derive(serde::Serialize)]
#[serde(rename_all = "PascalCase")]
struct SendEmailRequest<'a> {
    from: &'a str,
    to: &'a str,
    subject: &'a str,
    html_body: &'a str,
    text_body: &'a str,
}

#[cfg(test)]
mod tests {
    use wiremock::Request;
    use crate ::domain:: SubscriberEmail;
    use crate ::email_client:: EmailClient;
    use fake::faker::internet::en:: SafeEmail;
    use fake::faker::lorem::en:: {Paragraph, Sentence};
    use fake:: {Fake, Faker};
    use wiremock::matchers:: any;
    use wiremock:: {Mock, MockServer, ResponseTemplate};
    use secrecy::Secret;
    use wiremock::matchers:: {header_exists, header, path, method} ;
    use claim::assert_ok;
    use claim::assert_err;

    struct SendEmailBodyMatcher;

    impl wiremock::Match for SendEmailBodyMatcher {
        fn matches (&self, request: & Request) -> bool {
            //unimplemented!()
            let result : Result<serde_json::Value, _> = 
                serde_json::from_slice(&request.body);

            if let Ok(body) = result {
                dbg!(&body);
                body.get("From").is_some()
                    && body.get("To").is_some()
                    && body.get("Subject").is_some()
                    && body.get("HtmlBody").is_some()
                    && body.get("TextBody").is_some()

            } else {
                false
            }
        }
    }

    #[tokio::test]
    async fn send_email_sends_the_expected_request() {
        //todo!()
        let mock_server = MockServer::start().await;
        let sender = SubscriberEmail::parse(SafeEmail().fake()).unwrap();
        let email_client = EmailClient::new(mock_server.uri(), sender,
            Secret::new(Faker.fake()));

        Mock::given(header_exists("X-PostMark-Server-Token"))
            .and(header("Content-Type", "application/json"))
            .and(path("/email"))
            .and(method("POST"))
            // use custom matcher
            .and(SendEmailBodyMatcher)
            .respond_with(ResponseTemplate::new(200))
            .expect(1)
            .mount(&mock_server)
            .await;

        //Mock::given(any())

        let subscriber_email = SubscriberEmail::parse(SafeEmail().fake()).unwrap();
        let subject: String = Sentence(1..2).fake();
        let content: String = Paragraph(1..10).fake();
        let outcome  = email_client
            .send_email(subscriber_email, &subject, &content, &content)
            .await;
        assert_ok!(outcome);
    }

    #[tokio::test]
    async fn send_email_succeeds_if_the_server_returns_200() 
    {
        // Arrange
        let mock_server = MockServer::start().await;
        let sender = SubscriberEmail::parse(SafeEmail().fake()).unwrap();
        let email_client = EmailClient::new(
            mock_server.uri(),
            sender,
            Secret::new(Faker.fake())
        );

        let subscriber_email = SubscriberEmail::parse(SafeEmail().fake()).unwrap();
        let subject : String = Sentence(1..2).fake();
        let content : String = Paragraph(1..10).fake();
        Mock::given(any())
            .respond_with(ResponseTemplate::new(200))
            .expect(1)
            .mount(&mock_server)
            .await;

        // Act
        let outcome = email_client
            .send_email(subscriber_email, &subject, & content, &content)
            .await;

        // Assert
        assert_ok!(outcome);

    }

    #[tokio::test]
    async fn send_email_fails_if_the_server_returns_500() {
        //todo!()
        let mock_server = MockServer::start().await;
        let sender = SubscriberEmail::parse(SafeEmail().fake()).unwrap();
        let email_client = EmailClient::new(
            mock_server.uri(), 
            sender,
            Secret::new(Faker.fake()));

        //Mock::given(any())
        Mock::given(any())
            // use custom matcher
            .respond_with(ResponseTemplate::new(500))
            .expect(1)
            .mount(&mock_server)
            .await;


        let subscriber_email = SubscriberEmail::parse(SafeEmail().fake()).unwrap();
        let subject: String = Sentence(1..2).fake();
        let content: String = Paragraph(1..10).fake();

        // Act
        let outcome  = email_client
            .send_email(subscriber_email, &subject, &content, &content)
            .await;

        // Assert
        assert_err!(outcome);
    }

    #[tokio::test]
    async fn send_email_times_out_if_server_takes_too_long() {
        //todo!()
        let mock_server = MockServer::start().await;
        let sender = SubscriberEmail::parse(SafeEmail().fake()).unwrap();
        let email_client = EmailClient::new(
            mock_server.uri(), 
            sender,
            Secret::new(Faker.fake()));

        let response = ResponseTemplate::new(200)
            .set_delay(std::time::Duration::from_secs(180));
        //Mock::given(any())
        Mock::given(any())
            // use custom matcher
            .respond_with(response)
            .expect(1)
            .mount(&mock_server)
            .await;


        let subscriber_email = SubscriberEmail::parse(SafeEmail().fake()).unwrap();
        let subject: String = Sentence(1..2).fake();
        let content: String = Paragraph(1..10).fake();

        // Act
        let outcome  = email_client
            .send_email(subscriber_email, &subject, &content, &content)
            .await;

        // Assert
        assert_err!(outcome);
    }

}
