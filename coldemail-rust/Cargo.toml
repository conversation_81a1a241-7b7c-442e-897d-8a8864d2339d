[package]
name = "coldemail_rust"
version = "0.1.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
path = "src/lib.rs"

[[bin]]
path = "src/main.rs"
name = "coldemail_rust"



[dependencies]
secrecy = { version ="0.8", features = ["serde"] }
actix-web = "4"
#tokio = { version = "1", features = ["macros", "rt-multi-thread"]}
tokio = { version = "1", features = ["macros", "rt"]}
serde = { version = "1", features = ["derive"]}
config = "0.13"
uuid = { version = "1", features = ["v4"]}
chrono = { version = "0.4.22", default-features = false, features = ["clock"]}
env_logger = "0.9"
log = "0.4"
tracing = { version = "0.1", features = [ "log" ] }
tracing-subscriber = { version = "0.3", features = [ "registry", "env-filter" ] }
tracing-bunyan-formatter = "0.3"
tracing-log = "0.1"
tracing-actix-web = "0.6"
unicode-segmentation = "1"
claim = "0.5"
validator = "0.14"

[dependencies.sqlx]
version = "0.6"
default-features = false
features = [
  "runtime-tokio-rustls",
  "macros",
  "postgres",
  "uuid",
  "chrono",
  "migrate"
]

[dependencies.reqwest]
version = "0.11"
default-features = false
features = ["json", "rustls-tls"]

[dev-dependencies]
once_cell = "1"
fake = "~2.3"
quickcheck = "0.9.2"
quickcheck_macros = "0.9.1"
wiremock = "0.5"
serde_json = "1"

