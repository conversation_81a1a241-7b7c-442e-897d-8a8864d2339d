package io.smartreach.sr_dns_utils

import api.accounts.email.models.SrMxCheckESPType
import org.joda.time.DateTime
import play.api.libs.json.JsValue

case class DomainPublicDNSData(
                                domain: String,
                                dns_service: DNSService,
                                raw_response: JsValue,
                                mx_inbox_provider: SrMxCheckESPType,
                                last_updated_at: DateTime,
                                first_checked_at: DateTime,
                                is_valid: Boolean
                              )
case class DomainDataForFreeEmail(
                                   domain: String,
                                   is_valid: Boolean,
                                   is_public_email_service: Option[Boolean],
                                   is_disposable: Option[Boolean]
                                 )


case class DomainPublicDNSAddOrUpdateData(
                                           domain: String,
                                           dns_service: DNSService,
                                           raw_response: JsValue,
                                           mx_inbox_provider: SrMxCheckESPType,
                                           is_valid: Boolean
                                         )
