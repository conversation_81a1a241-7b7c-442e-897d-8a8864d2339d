{"name": "pg_partman", "abstract": "Extension to manage partitioned tables by time or ID", "version": "4.5.1", "maintainer": ["<PERSON> <<EMAIL>>"], "license": "postgresql", "generated_by": "<PERSON>", "release_status": "stable", "prereqs": {"runtime": {"requires": {"PostgreSQL": "9.6.0"}, "recommends": {"pg_jobmon": "1.3.2"}}}, "provides": {"pg_partman": {"file": "sql/pg_partman--4.5.1.sql", "docfile": "doc/pg_partman.md", "version": "4.5.1", "abstract": "Extension to manage partitioned tables by time or ID"}}, "resources": {"bugtracker": {"web": "https://github.com/pgpartman/pg_partman/issues"}, "repository": {"url": "git://github.com/pgpartman/pg_partman.git", "web": "https://github.com/pgpartman/pg_partman", "type": "git"}}, "meta-spec": {"version": "1.0.0", "url": "http://pgxn.org/meta/spec.txt"}, "tags": ["partition", "partitions", "partitioning", "table", "tables", "bgw", "background worker", "custom background worker"]}