
select now() - query_start, query, state, * from pg_stat_activity ;


select count(*) from emails_scheduled where not sent;

SELECT state, count(*) FROM pg_stat_activity GROUP BY state;

SELECT count(distinct pid) FROM pg_locks WHERE granted = false;

SELECT (current_timestamp - state_change) as time, query, datname, * FROM pg_stat_activity order by time desc; WHERE state IN ('idle in transaction','active');

select pg_terminate_backend(6557);


----------------
--- check and terminate idle connections
----------------

SELECT current_timestamp - state_change, state, pid, datname, usename, query, * FROM pg_stat_activity
where state != 'idle'
;


select client_addr,  datname, query, * from pg_stat_activity;
SELECT relation::regclass, * FROM pg_locks WHERE NOT GRANTED;


WITH inactive_connections AS (
    SELECT
        pid,
        rank() over (partition by client_addr order by backend_start ASC) as rank,
        application_name,
        datname,
        query,
        (current_timestamp - state_change) as running_since
    FROM
        pg_stat_activity
    WHERE
        -- Exclude the thread owned connection (ie no auto-kill)
        pid <> pg_backend_pid( )
    AND
        -- Exclude known applications connections
        application_name !~ '(?:psql)|(?:pgAdmin.+)'

    --AND
        -- Include connections to the same database the thread is connected to
    --    datname = current_database()

    AND
        -- Include connections using the same thread username connection
        usename = current_user
    AND
    (
       (
	        -- Include inactive connections only
	        state in ('idle', 'idle in transaction', 'idle in transaction (aborted)', 'disabled')

	    	AND
	        -- Include old connections (found with the state_change field)
	        current_timestamp - state_change > interval '5 minutes'

        )

        OR

        (
        	-- Include inactive connections only
	        state = 'active'

	    	AND

	        -- Include old connections (found with the state_change field)
	        current_timestamp - state_change > interval '60 minutes'

		)

    )


)
SELECT
    pg_terminate_backend(pid), *
FROM
    inactive_connections
WHERE
    rank > 1 -- Leave one connection for each application connected to the database

    ;

 
----------------
----------------
----------------



select pid,
       usename,
       pg_blocking_pids(pid) as blocked_by,
       query as blocked_query
from pg_stat_activity
where cardinality(pg_blocking_pids(pid)) > 0;

select * from pg_stat_activity where pid = 13896;



    select pg_terminate_backend(pid) from pg_stat_activity where datname = 'coldemaildb';




        SELECT
        pid,
        rank() over (partition by client_addr order by backend_start ASC) as rank
    FROM
        pg_stat_activity
    WHERE
        -- Exclude the thread owned connection (ie no auto-kill)
        pid <> pg_backend_pid( )
    AND
        -- Exclude known applications connections
        application_name !~ '(?:psql)|(?:pgAdmin.+)'
    AND
        -- Include connections to the same database the thread is connected to
        datname = current_database()
    AND
        -- Include connections using the same thread username connection
        usename = current_user
    AND
    (
       (
	        -- Include inactive connections only
	        state in ('idle', 'idle in transaction', 'idle in transaction (aborted)', 'disabled')

	    	AND
	        -- Include old connections (found with the state_change field)
	        current_timestamp - state_change > interval '5 minutes'

        )

        OR

        (
        	-- Include inactive connections only
	        state = 'active'

	    	AND

	        -- Include old connections (found with the state_change field)
	        current_timestamp - state_change > interval '60 minutes'

		)

    )
;



select a.email, e.* from emails_scheduled e join campaigns c on c.id = e.campaign_id
join accounts a on a.id = c.account_id where not sent and pushed_to_rabbitmq;



---------------------
---------------------
---------------------
--- ON PUSH ----
--- THESE STEPS MUST BE CARRIED OUT ------
--- UPDATED 24th April 2018 -------
---------------------
---------------------
---------------------

-- 1.
select now() - pushed_to_rabbitmq_at, * from emails_scheduled e where not sent AND pushed_to_rabbitmq;

-- 2.
select es.email, e.scheduled_at - now(), e.* from emails_scheduled e
join email_settings es on es.id = e.sender_email_settings_id

where not sent ;

-- 3.
select from_email,  sent_at from emails_scheduled where sent
and sent_at > now() - interval '200 minutes'
--and from_email = '<EMAIL>'

order by sent_at desc;

--4.
select e.campaign_id from emails_scheduled e where not sent group by campaign_id;

-- 5. NOTE: nothing should be in rabbitmq, step 1 should show empty results
with t As (select e.campaign_id from emails_scheduled e where not sent group by campaign_id order by campaign_id)
select string_agg(t.campaign_id::character varying
, ', ') from t;


--6.
select * from email_settings where in_queue_for_reply_tracking or in_queue_for_scheduling;

--7.
update email_settings set
last_read_for_replies = last_read_for_replies - interval '5 hours',
in_queue_for_reply_tracking = false,
in_queue_for_scheduling = false;
where id between 450 and 500;

where account_id != 184
returning *;
--where last_read_for_replies > now() - interval '1005 hours' and last_read_for_replies < now() - interval '55 hours'

--where last_read_for_replies < now() - interval '35 hours' and last_read_for_replies > now() - interval '45 hours'
;


select * from email_settings where email = '<EMAIL>';


select * from email_settings where error_reported_at > now() - interval '12 hours';
---------------------
---------------------
---------------------
---------------------
---------------------
---------------------



select (now() - query_start) as tt, * from pg_stat_activity;

WITH inactive_connections AS (
    SELECT
        pid,
        rank() over (partition by client_addr order by backend_start ASC) as rank
    FROM
        pg_stat_activity
    WHERE
        -- Exclude the thread owned connection (ie no auto-kill)
        pid <> pg_backend_pid( )
    AND
        -- Exclude known applications connections
        application_name !~ '(?:psql)|(?:pgAdmin.+)'
    AND
        -- Include connections to the same database the thread is connected to
        datname = current_database()
    AND
        -- Include connections using the same thread username connection
        usename = current_user
    AND
        -- Include inactive connections only
        state in ('idle', 'idle in transaction', 'idle in transaction (aborted)', 'disabled')
    AND
        -- Include old connections (found with the state_change field)
        current_timestamp - state_change > interval '2 minutes'
)
SELECT
    pg_terminate_backend(pid)
FROM
    inactive_connections
WHERE
    rank > 1
;





 EXPLAIN (ANALYZE, COSTS, VERBOSE, BUFFERS, FORMAT JSON)


 select
 count(*) FILTER (WHERE es.step_id = c.head_step_id AND es.sent) as newCount,
 count(*) FILTER (WHERE es.step_id != c.head_step_id AND es.sent) AS followupCount,
 count(*) FILTER (WHERE es.step_id = c.head_step_id AND not es.sent) as newCountNotSent,
 count(*) FILTER (WHERE es.step_id != c.head_step_id AND not es.sent) as followupCountNotSent

 from emails_scheduled es inner join campaigns c on c.id = es.campaign_id
 inner join email_settings ses on ses.id = c.sender_email_settings_id

 where c.sender_email_settings_id = 135
 AND c.id = 179
 AND ( es.scheduled_at >= date_trunc('day', now() AT TIME ZONE c.timezone) OR es.sent_at >= date_trunc('day', now() AT TIME ZONE c.timezone) OR ((ses.donot_enforce_24_hour_limit_till IS NULL OR (now() > ses.donot_enforce_24_hour_limit_till)) AND (ses.quota_per_day >= 250) AND (es.scheduled_at >= now() - interval '24 hours' OR es.sent_at >= now() - interval '24 hours')) )
;




EXPLAIN (ANALYZE, COSTS, VERBOSE, BUFFERS, FORMAT JSON)

   SELECT max2500.*, COUNT(*) OVER() AS total_count FROM ( SELECT a.email AS owner_email, CASE WHEN (a.first_name IS NOT NULL OR a.last_name IS NOT NULL) THEN CONCAT(a.first_name, ' ', a.last_name) ELSE '' END AS owner_name, prospects.id, prospects.account_id, prospects.first_name, prospects.last_name, prospects.email, (CASE WHEN prospects.email_checked THEN prospects.invalid_email ELSE NULL END) AS invalid_email, prospects.list, prospects.company, prospects.city, prospects.country, prospects.timezone, prospects.created_at, prospects.last_contacted_at, prospects.last_replied_at, prospects.custom_fields, prospects.in_blacklist, prospects.will_delete, pcat.name as prospect_category, cp.to_check, cp.to_check_fields, campaigns.name AS campaign_name, campaign_steps.label AS step FROM prospects INNER JOIN accounts a ON a.id = prospects.account_id INNER JOIN prospect_categories pcat on pcat.id = prospects.prospect_category_id LEFT JOIN campaigns_prospects cp ON (prospects.id = cp.prospect_id AND cp.active = TRUE) LEFT JOIN campaigns ON campaigns.id = cp.campaign_id LEFT JOIN campaign_steps ON campaign_steps.id = cp.current_step_id WHERE prospects.account_id IN (8) AND prospects.team_id = 8 AND (prospects.list IS NULL OR prospects.list = '') LIMIT 2500 OFFSET 0 ) AS max2500 LIMIT 1000;




EXPLAIN (ANALYZE, COSTS, VERBOSE, BUFFERS, FORMAT JSON)

  select p.email AS to_email, es.prospect_id, es.campaign_id, es.step_id, es.sent_at, es.message_id, es.references_header, p.first_name, p.last_name, p.company
  from emails_scheduled es
  inner join prospects p on p.id = es.prospect_id

   where (es.reply_to_email = '<EMAIL>' or es.sender_email_settings_id IN (94) ) and es.sent and es.sent_at > now() - interval '2 week' order by es.sent_at desc;







------------------------
------------------------
-- Stop old inactive campaigns from unpaid accounts
------------------------
------------------------


with stopc as (select o.name, o.created_at, set.email, set.id as emailId, c.id as cid, c.* from campaigns c
join email_settings set on c.sender_email_settings_id = set.id
join accounts a on a.id = c.account_id
join organizations o on o.id = a.org_id
where c.status = 'running'
and o.plan_type != 'paid'
and (c.latest_email_scheduled_at is  null or c.latest_email_scheduled_at < now() - interval '30 days')
and c.status_changed_at < now() - interval '30 days'
)
update campaigns cam set
status = 'stopped'
from stopc
where stopc.cid = cam.id
returning *;

------------------------
------------------------
------------------------
------------------------

