
## Evolutions
# https://www.playframework.com/documentation/latest/Evolutions
# ~~~~~
# Evolutions allows database scripts to be automatically run on startup in dev mode
# for database migrations. You must enable this by adding to build.sbt:
#
# libraryDependencies += evolutions
#
play.evolutions {
  autoApply = false
  autoApplyDowns = false
  autocommit = false
  useLocks = true
}


## 10 June 2025: disabling play evolutions for default db during test is critical to get ParallelTestExecution to work
# We are managing the evolutions manually here: SetupDbAndRedis that gets called inside DbTestingBeforeAllAndAfterAll.beforeAll
# if we don't do this, we get this error:
# [info]   - should change the non agency to agency *** FAILED ***
# [info]     org.postgresql.util.PSQLException: ERROR: duplicate key value violates unique constraint "pg_type_typname_nsp_index"
# [info]   Detail: Key (typname, typnamespace)=(play_evolutions_lock, 18235993) already exists.
# [info]     at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2725)


play.evolutions.db.default.enabled = false

redis.keyPrefix = "srlocal"
redis.uri = "redis://localhost:7777"

rabbitmq.virtualHost = "local_db_host"
rabbitmq.username = "guest"
rabbitmq.prefix = "localdb"

## JDBC Datasource
# https://www.playframework.com/documentation/latest/JavaDatabase
# https://www.playframework.com/documentation/latest/ScalaDatabase
# ~~~~~
# Once JDBC datasource is set up, you can work with several different
# database options:
#
# Slick (Scala preferred option): https://www.playframework.com/documentation/latest/PlaySlick
# JPA (Java preferred option): https://playframework.com/documentation/latest/JavaJPA
# EBean: https://playframework.com/documentation/latest/JavaEbean
# Anorm: https://www.playframework.com/documentation/latest/ScalaAnorm
#
db {
  # You can declare as many datasources as you want.
  # By convention, the default datasource is named `default`

  # https://www.playframework.com/documentation/latest/Developing-with-the-H2-Database
  default.driver = org.postgresql.Driver

  # Update db and username for local
  default.url = "***********************************************"
  default.username = "heaplabs"
  default.password = "postgresql127"
}

# https://commons.apache.org/proper/commons-dbcp/configuration.html
db.default.initialSize = 25
db.default.maxTotal = 150
db.default.maxIdle = 5
db.default.minEvictableIdleTimeMillis = 1000
db.default.validationQueryTimeout = 1000
db.default.validationQuery = "select 1 as one"

application.istest = true
