package utils

import api.AppConfig
import db_test_spec.api.DbTestingBeforeAllAndAfterAll
import utils.email.{ClickTrackingUrlAndEmail, ClickTrackingUrlHelper}
import utils.StringUtils
import api.accounts.TeamId
import api.emails.EmailsScheduledUuid
import org.scalatest.funspec.AsyncFunSpec
import utils.testapp.TestAppTrait

class ClickTrackingUrlHelperSpec extends AsyncFunSpec with TestAppTrait {

  describe("ClickTrackingUrlHelper") {
    describe("deconstructClickTrackingUrlBase32") {
      it("should handle new format (v2) with teamId and uuid") {
        val teamId = TeamId(id = 202L)
        val uuid = EmailsScheduledUuid(uuid = srUuidUtils.generateEmailsScheduledUuid())
        val url = "https://example.com"
        val customDomain = None
        val defaultTrackingDomain = "tracking.example.com"
        
        // Generate URL using genClickTrackingUrl_V3
        val trackingUrl = ClickTrackingUrlHelper.constructClickTrackingUrl(uuid, teamId, url, customDomain, defaultTrackingDomain)
        // Extract the base32 encoded part from the URL
        val baseStr = trackingUrl.split("/").dropRight(1).last
        
        // Decode the URL
        val decoded = ClickTrackingUrlHelper.deconstructClickTrackingUrl(baseStr)
        
        assert(decoded.isSuccess)
        val result = decoded.get
        assert(result.clickTrackingUrl == url)
        assert(result.emailsScheduledUuidAndTeamId.map(_._2).contains(teamId))
        assert(result.emailsScheduledUuidAndTeamId.map(_._1).contains(uuid))
        assert(result.emailScheduledId.isEmpty)
      }

      it("should handle old format (v1) with emailScheduledId") {
        val emailScheduledId = 12345L
        val url = "https://example.com"
        
        // Create the old v2 format URL
        val baseStr = StringUtils.base32EncodeURIString(s"$emailScheduledId${AppConfig.trackingLinksDelimiter}$url").toLowerCase()
        
        // Decode the URL
        val decoded = ClickTrackingUrlHelper.deconstructClickTrackingUrl(baseStr)
        
        assert(decoded.isSuccess)
        val result = decoded.get
        assert(result.clickTrackingUrl == url)
        assert(result.emailScheduledId.contains(emailScheduledId))
        assert(result.emailsScheduledUuidAndTeamId.isEmpty)
      }

      it("should fail with invalid new format (v2)") {
        val invalidCode = "invalid-code"
        val decoded = ClickTrackingUrlHelper.deconstructClickTrackingUrl(invalidCode)
        assert(decoded.isFailure)
      }

      it("should fail with invalid old format (v1)") {
        val invalidCode = StringUtils.base32EncodeURIString("invalid-code").toLowerCase()
        val decoded = ClickTrackingUrlHelper.deconstructClickTrackingUrl(invalidCode)
        assert(decoded.isFailure)
      }

      it("should handle URLs with special characters") {
        val teamId = TeamId(id = 202L)
        val uuid = EmailsScheduledUuid(uuid = srUuidUtils.generateEmailsScheduledUuid())
        val url = "https://example.com/path?param=value&other=123"
        val customDomain = None
        val defaultTrackingDomain = "tracking.example.com"
        
        // Generate URL using genClickTrackingUrl_V3
        val trackingUrl = ClickTrackingUrlHelper.constructClickTrackingUrl(uuid, teamId, url, customDomain, defaultTrackingDomain)
        // Extract the base32 encoded part from the URL
        val baseStr = trackingUrl.split("/").dropRight(1).last
        
        // Decode the URL
        val decoded = ClickTrackingUrlHelper.deconstructClickTrackingUrl(baseStr)
        
        assert(decoded.isSuccess)
        val result = decoded.get
        assert(result.clickTrackingUrl == url)
        assert(result.emailsScheduledUuidAndTeamId.map(_._2).contains(teamId))
        assert(result.emailsScheduledUuidAndTeamId.map(_._1).contains(uuid))
        assert(result.emailScheduledId.isEmpty)
      }

      it("should handle empty uuid in new format (v2)") {
        val teamId = TeamId(id = 202L)
        val uuid = EmailsScheduledUuid(uuid = "")
        val url = "https://example.com"
        val customDomain = None
        val defaultTrackingDomain = "tracking.example.com"
        
        // Generate URL using genClickTrackingUrl_V3
        val trackingUrl = ClickTrackingUrlHelper.constructClickTrackingUrl(uuid, teamId, url, customDomain, defaultTrackingDomain)
        // Extract the base32 encoded part from the URL
        val baseStr = trackingUrl.split("/").dropRight(1).last
        
        // Decode the URL
        val decoded = ClickTrackingUrlHelper.deconstructClickTrackingUrl(baseStr)
        
        assert(decoded.isSuccess)
        val result = decoded.get
        assert(result.clickTrackingUrl == url)
        assert(result.emailsScheduledUuidAndTeamId.map(_._2).contains(teamId))
        assert(result.emailsScheduledUuidAndTeamId.map(_._1).contains(uuid))
        assert(result.emailScheduledId.isEmpty)
      }
    }
  }
} 