package utils.security

import org.scalamock.scalatest.MockFactory
import org.scalatest.funspec.AnyFunSpec
import utils.SRLogger


class SignatureValidationServiceSpec extends AnyFunSpec with MockFactory {

    implicit lazy val logger: SRLogger = new SRLogger("SignatureValidationService Spec")

    describe("SignatureValidationService") {
        describe("validateWebhookSignature") {
            //      val mockLogger = mock[SRLogger]

            it("should return false when signature header is missing") {
                // Setup
                val signingKey = "test_signing_key"
                val signatureHeader = None
                val rawBody = """{"event":"test_event"}"""
                val maxAgeSeconds = 300L

                // Expect logger to be called
                //        (logger.error _).expects("Missing Calendly-Webhook-Signature header")

                // Execute
                val result = SignatureValidationService.validateWebhookSignature(
                    signingKey,
                    signatureHeader,
                    rawBody,
                    maxAgeSeconds
                )

                // Assert
                assert(!result)
            }

            it("should return false when signature header format is invalid") {
                // Setup
                val signingKey = "test_signing_key"
                val signatureHeader = Some("invalid_format")
                val rawBody = """{"event":"test_event"}"""
                val maxAgeSeconds = 300L

                // Expect logger to be called
                //        (logger.error _).expects("Invalid signature header format: invalid_format")

                // Execute
                val result = SignatureValidationService.validateWebhookSignature(
                    signingKey,
                    signatureHeader,
                    rawBody,
                    maxAgeSeconds
                )

                // Assert
                assert(!result)
            }

            it("should return false when timestamp is too old") {
                // Setup
                val signingKey = "test_signing_key"
                val currentTime = java.time.Instant.now().getEpochSecond
                val oldTimestamp = currentTime - 600 // 10 minutes old
                val signatureHeader = Some(s"t=$oldTimestamp,v1=some_signature")
                val rawBody = """{"event":"test_event"}"""
                val maxAgeSeconds = 300L // 5 minutes

                // Expect logger to be called
                //        (mockLogger.error _).expects(s"Webhook signature timestamp too old: $oldTimestamp, current: $currentTime")

                // Execute
                val result = SignatureValidationService.validateWebhookSignature(
                    signingKey,
                    signatureHeader,
                    rawBody,
                    maxAgeSeconds
                )

                // Assert
                assert(!result)
            }

            it("should return true when signature is valid") {
                // Setup
                val signingKey = "test_signing_key"
                val currentTime = java.time.Instant.now().getEpochSecond
                val rawBody = """{"event":"test_event"}"""

                // Create a valid signature
                val dataToSign = s"$currentTime.$rawBody"
                val validSignature = SignatureValidationService.computeHmacSha256(signingKey, dataToSign)
                val signatureHeader = Some(s"t=$currentTime,v1=$validSignature")
                val maxAgeSeconds = 300L

                // Execute
                val result = SignatureValidationService.validateWebhookSignature(
                    signingKey,
                    signatureHeader,
                    rawBody,
                    maxAgeSeconds
                )

                // Assert
                assert(result)
            }

            it("should return false when signature is invalid") {
                // Setup
                val signingKey = "test_signing_key"
                val currentTime = java.time.Instant.now().getEpochSecond
                val rawBody = """{"event":"test_event"}"""
                val invalidSignature = "invalid_signature"
                val signatureHeader = Some(s"t=$currentTime,v1=$invalidSignature")
                val maxAgeSeconds = 300L

                // Execute
                val result = SignatureValidationService.validateWebhookSignature(
                    signingKey,
                    signatureHeader,
                    rawBody,
                    maxAgeSeconds
                )

                // Assert
                assert(!result)
            }
        }


    }
} 