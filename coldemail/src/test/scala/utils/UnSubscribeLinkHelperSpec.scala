package utils

import api.accounts.TeamId
import api.campaigns.services.CampaignId
import api.emails.EmailsScheduledUuid
import api.prospects.models.{ProspectId, StepId}
import db_test_spec.api.DbTestingBeforeAllAndAfterAll
import org.scalatest.funspec.AsyncFunSpec
import utils.email.{DecryptedUnsubLinkV2, UnSubscribeLinkHelper}
import utils.StringUtils
import utils.email.UnSubscribeLinkHelper.LIST_UNSUBSCRIBE_EMAIL_ALIAS_KEY
import utils.testapp.TestAppTrait

import scala.util.Try

class UnSubscribeLinkHelperSpec extends AsyncFunSpec with TestAppTrait {

  describe("UnSubscribeLinkHelper") {
    describe("decodeBase32UnsubscribedLinkV4") {
      it("should handle new format with teamId and uuid") {
        val teamId = TeamId(id = 202L)
        val uuid = EmailsScheduledUuid(uuid = srUuidUtils.generateEmailsScheduledUuid())
        val code = StringUtils.base32EncodeURIString(s"${teamId.id}___${uuid.uuid}").toLowerCase()

        val decoded: Try[DecryptedUnsubLinkV2] = UnSubscribeLinkHelper.deconstructUnsubCode(code)
        
        assert(decoded.isSuccess)
        val result = decoded.get
        assert(result.emailsScheduledUuidAndTeamId.contains((uuid, teamId)))
        assert(result.emailScheduledId.isEmpty)
        assert(result.isV2Link)
      }

      it("should handle old format with emailScheduledId") {
        val emailScheduledId = 12345L
        val code = StringUtils.base32EncodeURIString(s"v1-123-456-$emailScheduledId").toLowerCase()

        val decoded: Try[DecryptedUnsubLinkV2] = UnSubscribeLinkHelper.deconstructUnsubCode(code)
        
        assert(decoded.isSuccess)
        val result:DecryptedUnsubLinkV2 = decoded.get
        assert(result.emailsScheduledUuidAndTeamId.isEmpty)
        assert(result.emailScheduledId.contains(emailScheduledId))
        assert(!result.isV2Link)
      }

      it("should fail with invalid format") {
        val code = "invalid-code"
        val decoded: Try[DecryptedUnsubLinkV2] = UnSubscribeLinkHelper.deconstructUnsubCode(code)
        assert(decoded.isFailure)
      }
    }

    describe("deconstructListUnsubscribeHeaderEmail") {
      it("should handle new format email with teamId and uuid") {
        val teamId = TeamId(id = 202L)
        val uuid = EmailsScheduledUuid(uuid = srUuidUtils.generateEmailsScheduledUuid())
        val email = s"<mailto:test+unsubscribesrmail_${teamId.id}___${uuid.uuid}@example.com?subject=unsubscribe>"
        
        val decoded: Try[DecryptedUnsubLinkV2] = UnSubscribeLinkHelper.deconstructUnsubHeader(email)
        
        assert(decoded.isSuccess)
        val result: DecryptedUnsubLinkV2 = decoded.get
        assert(result.emailsScheduledUuidAndTeamId.contains((uuid, teamId)))
        assert(result.emailScheduledId.isEmpty)
        assert(result.isV2Link)
      }

      it("should handle old format email with emailScheduledId") {
        val emailScheduledId = 12345L
        val email = s"<mailto:test+unsubscribesrmail_123_456_789_$<EMAIL>?subject=unsubscribe>"

        val decoded: Try[DecryptedUnsubLinkV2] = UnSubscribeLinkHelper.deconstructUnsubHeader(email)
        
        assert(decoded.isSuccess)
        val result: DecryptedUnsubLinkV2 = decoded.get
        assert(result.emailsScheduledUuidAndTeamId.isEmpty)
        assert(result.emailScheduledId.contains(emailScheduledId))
        assert(!result.isV2Link)
      }

      it("should fail with invalid email format") {
        val email = "invalid-email"
        val decoded = UnSubscribeLinkHelper.deconstructUnsubHeader(email)
        assert(decoded.isFailure)
      }
    }
  }
}
