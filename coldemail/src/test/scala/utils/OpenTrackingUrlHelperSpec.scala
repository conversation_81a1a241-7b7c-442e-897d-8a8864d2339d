package utils

import api.AppConfig
import api.accounts.TeamId
import api.accounts.models.OrgId
import api.emails.EmailsScheduledUuid
import db_test_spec.api.DbTestingBeforeAllAndAfterAll
import org.scalatest.funspec.AsyncFunSpec
import utils.email.{OpenTrackingEmailIdAndUuid, OpenTrackingUrlHelper}
import utils.StringUtils
import utils.testapp.TestAppTrait

class OpenTrackingUrlHelperSpec extends AsyncFunSpec with TestAppTrait {

  describe("OpenTrackingUrlHelper") {
    describe("constructOpenTrackingCode") {
      it("should generate valid base32 encoded code for teamId and uuid") {
        val teamId = TeamId(id = 202L)
        val uuid = EmailsScheduledUuid(uuid = srUuidUtils.generateEmailsScheduledUuid())
        
        val code = OpenTrackingUrlHelper.constructOpenTrackingCode(
          emailsScheduledUuid = uuid,
          teamId = teamId
        )
        

        val parts = code.split(AppConfig.trackingLinksDelimiter)
        
        assert(parts.length == 2)
        assert(parts(0).toLong == teamId.id)
        assert(parts(1) == uuid.uuid)
      }
    }

    describe("constructOpenTrackingUrl") {
      it("should construct URL with custom domain when provided") {
        val teamId = TeamId(id = 202L)
        val uuid = EmailsScheduledUuid(uuid = "testuuid")
        val customDomain = Some("custom.example.com")
        val defaultTrackingDomain = "tracking.example.com"
        
        val url = OpenTrackingUrlHelper.constructOpenTrackingUrl(
          emailsScheduledUuid = uuid,
          teamId = teamId,
          customDomain = customDomain,
          defaultTrackingDomain = defaultTrackingDomain
        )
        
        assert(url.startsWith("https://custom.example.com/ot2/"))
        assert(url.endsWith("/open"))
      }

      it("should construct URL with default domain when no custom domain provided") {
        val teamId = TeamId(id = 202L)
        val uuid = EmailsScheduledUuid(uuid = srUuidUtils.generateEmailsScheduledUuid())
        val customDomain = None
        val defaultTrackingDomain = "tracking.example.com"
        
        val url = OpenTrackingUrlHelper.constructOpenTrackingUrl(
          emailsScheduledUuid = uuid,
          teamId = teamId,
          customDomain = customDomain,
          defaultTrackingDomain = defaultTrackingDomain
        )
        
        assert(url.startsWith("https://tracking.example.com/ot2/"))
        assert(url.endsWith("/open"))
      }
    }

    describe("constructOpenTrackingImgHtml") {

      it("should construct img tag with old format") {
        val teamId = TeamId(id = 202L)
        val uuid = EmailsScheduledUuid(uuid = srUuidUtils.generateEmailsScheduledUuid())
        val customDomain = None
        val defaultTrackingDomain = "tracking.example.com"
        val orgId = OrgId(999L) // Assuming this org is not in the rolling update list
        
        val html = OpenTrackingUrlHelper.constructOpenTrackingImgHtml(
          emailsScheduledUuid = uuid,
          customDomain = customDomain,
          defaultTrackingDomain = defaultTrackingDomain,
          teamId = teamId,
          orgId = orgId
        )
        
        assert(html.startsWith("""<img width="1" height="1" style="display: block;" alt="" src="https://tracking.example.com/ot2/"""))
        assert(html.endsWith("""/open">"""))
      }
    }

    describe("deconstructOpenTrackingUrl") {
      it("should handle new format with teamId and uuid") {
        val teamId = TeamId(id = 202L)
        val uuid = EmailsScheduledUuid(uuid = srUuidUtils.generateEmailsScheduledUuid())
        
        // Create code using the constructor
        val url = OpenTrackingUrlHelper.constructOpenTrackingUrl(
          emailsScheduledUuid = uuid,
          teamId = teamId,
          customDomain = None,
          defaultTrackingDomain = "tracking.example.com"
        )
        
        // Extract the code from the URL (between /ot2/ and /open)
        val code = url.split("/ot2/")(1).split("/open")(0)
        
        // Decode the code
        val result = OpenTrackingUrlHelper.deconstructOpenTrackingUrl(code)
        
        assert(result.isSuccess)
        val decoded = result.get
        assert(decoded.emailsScheduledUuidAndTeamId.contains((uuid, teamId)))
        assert(decoded.emailScheduledId.isEmpty)
      }

      it("should handle old format with just emailScheduledId") {
        val emailScheduledId = 12345L
        
        // Create old format code
        val code = StringUtils.base32EncodeURIString(emailScheduledId.toString).toLowerCase()
        
        // Decode the code
        val result = OpenTrackingUrlHelper.deconstructOpenTrackingUrl(code)
        
        assert(result.isSuccess)
        val decoded = result.get
        assert(decoded.emailScheduledId.contains(emailScheduledId))
        assert(decoded.emailsScheduledUuidAndTeamId.isEmpty)
      }

      it("should fail with invalid code") {
        val invalidCode = "invalid-code"
        val result = OpenTrackingUrlHelper.deconstructOpenTrackingUrl(invalidCode)
        assert(result.isFailure)
      }

      it("should fail with invalid new format (wrong number of parts)") {
        val invalidCode = StringUtils.base32EncodeURIString("part1_part2_part3").toLowerCase()
        val result = OpenTrackingUrlHelper.deconstructOpenTrackingUrl(invalidCode)
        assert(result.isFailure)
      }

      it("should handle round trip for new format (teamId + uuid)") {
        val teamId = TeamId(id = 202L)
        val uuid = EmailsScheduledUuid(uuid = srUuidUtils.generateEmailsScheduledUuid())
        
        // Create URL
        val url = OpenTrackingUrlHelper.constructOpenTrackingUrl(
          emailsScheduledUuid = uuid,
          teamId = teamId,
          customDomain = None,
          defaultTrackingDomain = "tracking.example.com"
        )
        
        // Extract the code from the URL
        val code = url.split("/ot2/")(1).split("/open")(0)
        
        // Decode code
        val result = OpenTrackingUrlHelper.deconstructOpenTrackingUrl(code)
        
        assert(result.isSuccess)
        val decoded = result.get
        
        // Verify decoded values match original
        assert(decoded.emailsScheduledUuidAndTeamId.contains((uuid, teamId)))
        assert(decoded.emailScheduledId.isEmpty)
        
        // Create new URL from decoded values
        val newUrl = OpenTrackingUrlHelper.constructOpenTrackingUrl(
          emailsScheduledUuid = decoded.emailsScheduledUuidAndTeamId.map(_._1).get,
          teamId = decoded.emailsScheduledUuidAndTeamId.map(_._2).get,
          customDomain = None,
          defaultTrackingDomain = "tracking.example.com"
        )
        
        // Extract codes from both URLs
        val newCode = newUrl.split("/ot2/")(1).split("/open")(0)
        
        // Verify codes match
        assert(code == newCode)
      }

      it("should handle round trip for old format (emailScheduledId)") {
        val emailScheduledId = 12345L
        
        // Create old format code
        val code = StringUtils.base32EncodeURIString(emailScheduledId.toString).toLowerCase()
        
        // Decode code
        val result = OpenTrackingUrlHelper.deconstructOpenTrackingUrl(code)
        
        assert(result.isSuccess)
        val decoded = result.get
        
        // Verify decoded values match original
        assert(decoded.emailScheduledId.contains(emailScheduledId))
        assert(decoded.emailsScheduledUuidAndTeamId.isEmpty)
        
        // Create new code from decoded value
        val newCode = StringUtils.base32EncodeURIString(decoded.emailScheduledId.get.toString).toLowerCase()
        
        // Verify codes match
        assert(code == newCode)
      }

      it("should handle special characters in uuid") {
        val teamId = TeamId(id = 202L)
        val uuid = EmailsScheduledUuid(uuid = "test-uuid_with.special@chars")
        
        // Create URL
        val url = OpenTrackingUrlHelper.constructOpenTrackingUrl(
          emailsScheduledUuid = uuid,
          teamId = teamId,
          customDomain = None,
          defaultTrackingDomain = "tracking.example.com"
        )
        
        // Extract the code from the URL
        val code = url.split("/ot2/")(1).split("/open")(0)
        
        // Decode code
        val result = OpenTrackingUrlHelper.deconstructOpenTrackingUrl(code)
        
        assert(result.isSuccess)
        val decoded = result.get
        
        // Verify decoded values match original
        assert(decoded.emailsScheduledUuidAndTeamId.contains((uuid, teamId)))
        assert(decoded.emailScheduledId.isEmpty)
      }

      it("should handle large teamId values") {
        val teamId = TeamId(id = Long.MaxValue)
        val uuid = EmailsScheduledUuid(uuid = srUuidUtils.generateEmailsScheduledUuid())
        
        // Create URL
        val url = OpenTrackingUrlHelper.constructOpenTrackingUrl(
          emailsScheduledUuid = uuid,
          teamId = teamId,
          customDomain = None,
          defaultTrackingDomain = "tracking.example.com"
        )
        
        // Extract the code from the URL
        val code = url.split("/ot2/")(1).split("/open")(0)
        
        // Decode code
        val result = OpenTrackingUrlHelper.deconstructOpenTrackingUrl(code)
        
        assert(result.isSuccess)
        val decoded = result.get
        
        // Verify decoded values match original
        assert(decoded.emailsScheduledUuidAndTeamId.contains((uuid, teamId)))
        assert(decoded.emailScheduledId.isEmpty)
      }
    }
  }
} 