//import api.prospects.{InternalMergeTagValues, Prospect, ProspectCategory}
//import org.joda.time.DateTime
//import org.scalatestplus.play.PlaySpec
//import play.api.libs.json.Json
//import utils.templating.TemplateService
//
//import scala.collection.mutable.ListBuffer
//import scala.util.{Failure, Success}
//
//class TemplateServiceSpec extends PlaySpec {
//
//
//  "TemplateService" should {
//
//
//    "__getAllParamsUsedInTemplate" should {
//
//      "get look up tokens" in {
//
//        val template = """<p>Hi {{ first_name }} {{last_name}},</p><p>My name is  john  and I work as a business developer at {{company}} studio.</p>"""
//
//        TemplateService.__getAllParamsUsedInTemplate(template) match {
//
//          case Success(data) =>
//            data mustBe ListBuffer("first_name", "last_name", "company")
//
//          case Failure(e) => None
//
//
//        }
//
//      }
//
//      "handle default filter" in {
//
//        val template = """<p>Hi {{ first_name |default:"hello" }} {{last_name}},</p><p>My name is  john  and I work as a business developer at {{company}} studio.</p>"""
//
//        TemplateService.__getAllParamsUsedInTemplate(template) match {
//
//          case Success(data) =>
//            data mustBe ListBuffer("last_name", "company")
//
//          case Failure(e) => None
//
//
//        }
//
//      }
//
//      "error on multiple consecutive filters" in {
//
//        val template = """<p>Hi {{ first_name |default:"hello" | default: "hey" }} {{last_name}},</p><p>My name is  john  and I work as a business developer at {{company}} studio.</p>"""
//
//        TemplateService.__getAllParamsUsedInTemplate(template) match {
//
//          case Success(data) =>
//            None
//
//          case Failure(e) =>
//            e.getMessage mustBe "Max one filter allowed :: 2"
//
//
//        }
//      }
//
//      "error on invalid filter: only default filter allowed" in {
//
//        val template = """<p>Hi {{ first_name |repeat:"hello" }} {{last_name}},</p><p>My name is  john  and I work as a business developer at {{company}} studio.</p>"""
//
//        TemplateService.__getAllParamsUsedInTemplate(template) match {
//
//          case Success(data) =>
//            None
//
//          case Failure(e) =>
//            e.getMessage mustBe "Invalid template filter"
//
//
//        }
//      }
//
//      "error on invalid param, lookup on filter" in {
//
//        val template = """<p>Hi {{ first_name |default:last_name }} {{last_name}},</p><p>My name is  john  and I work as a business developer at {{company}} studio.</p>"""
//
//        TemplateService.__getAllParamsUsedInTemplate(template) match {
//
//          case Success(data) =>
//            None
//
//          case Failure(e) =>
//            e.getMessage mustBe "Invalid template params"
//
//
//        }
//      }
//    }
//
//
//    "isValid" should {
//
//      "successful validation" in {
//
//        val template = """<p>Hi {{ first_name }} {{last_name}},</p><p>My name is  john  and I work as a business developer at {{company}} studio.</p>"""
//        val availableParams = Seq("first_name", "last_name", "email", "company")
//
//
//        TemplateService.isValid(template, availableParams) match {
//
//          case Success(data) =>
//            data mustBe true
//
//          case Failure(e) => None
//
//
//        }
//
//      }
//
//      "error on invalid param" in {
//        val template = """<p>Hi {{ first_name }} {{last_name}},</p><p>My name is  john  and I work as a business developer at {{company}} studio.</p>"""
//        val availableParams = Seq("first_name", "last_name", "email")
//
//        TemplateService.isValid(template, availableParams) match {
//
//          case Success(data) =>
//            None
//
//          case Failure(e) =>
//            e.getMessage mustBe "Invalid param: company"
//
//
//        }
//      }
//
//      "handle MismatchedTokenException" in {
//
//        val template = """<p>Hi {{ first_name |default:"hello" }} {{last_name}},</p><p>My name is  john  and {{city<span style="font-size: 14.6667px;">|default:'your area'</span><span style="font-size: 11pt;">}} I work as a business developer at {{company}} studio.</p>"""
//
//        val template2 = """
//          <!DOCTYPE html>
//            <html>
//              <head>
//              </head>
//              <body>
//                <p>Hi {{first_name}},<br /><br />My name is Prateek and I head up business development efforts with <a href="https://SmartReach.io" target="_blank" rel="noopener">SmartReach.io</a> . I went through your online profile and I believe you are the appropriate person at {{company}} to connect with.</p>
//                <p><br />You can <a href="https://calendly.com/prateek_smartreach/15min" target="_blank" rel="noopener">click here</a> to schedule a 15-minute call at your availability to discuss about the same. Looking forward to chatting!<br /><br />Let me know if you have any questions.<br /><br />PS - If you don't want to hear from me again, please just let me know with a quick reply :) Thanks!<br /><br />Best,<br /><br />Prateek Bhatt<br />Co-Founder, Business Development<br />SmartReach.io</p>
//                {{city<span style="font-size: 14.6667px;">|default:'your area'</span><span style="font-size: 11pt;">}}</span>
//              </body>
//            </html>
//        """
//        val availableParams = Seq("first_name", "last_name", "email", "city")
//
//        TemplateService.isValid(template = template2, availableTagNames = availableParams) match {
//
//          case Success(data) =>
//            data mustBe ListBuffer("last_name", "company")
//
//          case Failure(e) =>
//            e.getMessage mustBe "Only valid merge-tags (prospect column names) can be used inside double-braces (e.g. {{first_name}} ). Please check your email subject and body html source. If you can't figure it out, please contact us."
//
//
//        }
//
//      }
//
//    }
//
//
//    "checkAbsentTags" should {
//
//      "find absent tag" in {
//
//        val tagNamesInTemplate = Seq("first_name", "last_name", "email", "company", "username", "followers")
//        val prospect = Prospect(
//          id = 123,
//          account_id = 456,
//          first_name = Some("John"),
//          last_name = Some("Doe"),
//          email = "<EMAIL>",
//          custom_fields = Json.obj("username" -> "john.doe"),
//          created_at = DateTime.now().minusDays(10),
//
//          list = Some("demo_list"),
//          company = Some("demo_company"),
//          city = Some("demo_city"),
//          country = Some("India"),
//          timezone = Some("Asia/Kolkata"),
//
//
//          team_id = 0,
//          ta_id = 0,
//          email_domain = "",
//          list_id = None,
//          last_contacted_at = None,
//          prospect_category = ProspectCategory.NOT_CATEGORIZED,
//          prospect_category_id = 0
//        )
//
//
//        TemplateService.checkMissingMergeTags(tagNamesInTemplate, prospect,
//          internalMergeTags = InternalMergeTagValues(unsubscribe_link = None)) mustBe Seq("followers")
//
//      }
//
//
//      "find tags with null value" in {
//
//        val tagNamesInTemplate = Seq("first_name", "last_name", "email", "company", "username", "followers")
//        val prospect = Prospect(
//          id = 123,
//          account_id = 456,
//          first_name = None,
//          last_name = Some("Doe"),
//          email = "<EMAIL>",
//          custom_fields = Json.obj("username" -> "john.doe"),
//          created_at = DateTime.now().minusDays(10),
//
//          list = Some("demo_list"),
//          company = Some("demo_company"),
//          city = Some("demo_city"),
//          country = Some("India"),
//          timezone = Some("Asia/Kolkata"),
//
//
//
//          team_id = 0,
//          ta_id = 0,
//          email_domain = "",
//          list_id = None,
//          last_contacted_at = None,
//          prospect_category = ProspectCategory.NOT_CATEGORIZED,
//          prospect_category_id = 0
//        )
//
//
//        TemplateService.checkMissingMergeTags(tagNamesInTemplate, prospect) mustBe Seq("first_name", "followers")
//
//      }
//
//    }
//
//    "generate html template" in {
//
//      val template =
//        """
//          <p>Hi{{first_name | default: "there" }} {{last_name}},</p><p>My name is  john  and I work as a business developer at {{company}} studio.</p>
//        """.trim
//
//      val prospect = Prospect(
//        id = 123,
//        account_id = 456,
//        first_name = Some("John"),
//        last_name = Some("Doe"),
//        email = "<EMAIL>",
//        custom_fields = Json.obj("username" -> "john.doe"),
//        created_at = DateTime.now().minusDays(10),
//
//        list = Some("demo_list"),
//        company = Some("demo_company"),
//        city = Some("demo_city"),
//        country = Some("India"),
//        timezone = Some("Asia/Kolkata"),
//
//
//
//
//        team_id = 0,
//        ta_id = 0,
//        email_domain = "",
//        list_id = None,
//        last_contacted_at = None,
//        prospect_category = ProspectCategory.NOT_CATEGORIZED,
//        prospect_category_id = 0
//      )
//
//
//      val generated = TemplateService.render(template, prospect).get
//
//      generated mustBe "<p>HiJohn Doe,</p><p>My name is  john  and I work as a business developer at demo_company studio.</p>"
//
//    }
//
//
//    "empty string if first_name is absent" in {
//
//      val template =
//        """
//          <p>Hi {{first_name}} {{last_name}},</p><p></p><p>My name is  john  and I work as a business developer at Caltech studio. I found out about your product on web store and I really liked the function of the usage of product and decided to reach out.</p><p></p><p>Quick question: Are you currently looking for the new ways to grow your product sales?</p><p></p><p>I might have a solution that could become a new user acquisition channel for you, but the conversation can&#x27;t begin until I listen from you first.</p>
//        """.trim
//
//      val prospect = Prospect(
//        id = 123,
//        account_id = 456,
//        first_name = None,
//        last_name = Some("Doe"),
//        email = "<EMAIL>",
//        custom_fields = Json.obj("username" -> "john.doe"),
//        created_at = DateTime.now().minusDays(10),
//
//
//        list = Some("demo_list"),
//        company = Some("demo_company"),
//        city = Some("demo_city"),
//        country = Some("India"),
//        timezone = Some("Asia/Kolkata"),
//
//
//        team_id = 0,
//        ta_id = 0,
//        email_domain = "",
//        list_id = None,
//        last_contacted_at = None,
//        prospect_category = ProspectCategory.NOT_CATEGORIZED,
//        prospect_category_id = 0
//      )
//
//
//      val generated = TemplateService.render(template, prospect).get
//
//      generated mustBe "<p>Hi  Doe,</p><p></p><p>My name is  john  and I work as a business developer at Caltech studio. I found out about your product on web store and I really liked the function of the usage of product and decided to reach out.</p><p></p><p>Quick question: Are you currently looking for the new ways to grow your product sales?</p><p></p><p>I might have a solution that could become a new user acquisition channel for you, but the conversation can&#x27;t begin until I listen from you first.</p>"
//
//    }
//
//
//    "empty string for tag that is not present in the account" in {
//
//      val template =
//        """
//          <p>Hi {{unknown_tag}},</p><p></p><p>My name is  john  and I work as a business developer at Caltech studio. I found out about your product on web store and I really liked the function of the usage of product and decided to reach out.</p><p></p><p>Quick question: Are you currently looking for the new ways to grow your product sales?</p><p></p><p>I might have a solution that could become a new user acquisition channel for you, but the conversation can&#x27;t begin until I listen from you first.</p>
//        """.trim
//
//      val prospect = Prospect(
//        id = 123,
//        account_id = 456,
//        first_name = None,
//        last_name = Some("Doe"),
//        email = "<EMAIL>",
//        custom_fields = Json.obj("username" -> "john.doe"),
//        created_at = DateTime.now().minusDays(10),
//
//
//        list = Some("demo_list"),
//        company = Some("demo_company"),
//        city = Some("demo_city"),
//        country = Some("India"),
//        timezone = Some("Asia/Kolkata"),
//
//
//
//        team_id = 0,
//        ta_id = 0,
//        email_domain = "",
//        list_id = None,
//        last_contacted_at = None,
//        prospect_category = ProspectCategory.NOT_CATEGORIZED,
//        prospect_category_id = 0
//      )
//
//
//      val generated = TemplateService.render(template, prospect).get
//
//      generated mustBe "<p>Hi ,</p><p></p><p>My name is  john  and I work as a business developer at Caltech studio. I found out about your product on web store and I really liked the function of the usage of product and decided to reach out.</p><p></p><p>Quick question: Are you currently looking for the new ways to grow your product sales?</p><p></p><p>I might have a solution that could become a new user acquisition channel for you, but the conversation can&#x27;t begin until I listen from you first.</p>"
//
//    }
//
//  }
//
//
//  "use custom fields" in {
//
//    val template =
//      """
//              <p>Hi {{username}} {{follower_count}},</p><p></p><p>My name is  john  and I work as a business developer at Caltech studio. I found out about your product on web store and I really liked the function of the usage of product and decided to reach out.</p><p></p><p>Quick question: Are you currently looking for the new ways to grow your product sales?</p><p></p><p>I might have a solution that could become a new user acquisition channel for you, but the conversation can&#x27;t begin until I listen from you first.</p>
//            """.trim
//
//
//    val name = "john.doe"
//
//    val prospect = Prospect(
//      id = 123,
//      account_id = 456,
//      first_name = None,
//      last_name = Some("Doe"),
//      email = "<EMAIL>",
//      custom_fields = Json.obj("username" -> name, "follower_count" -> 1001),
//      created_at = DateTime.now().minusDays(10),
//
//
//      list = Some("demo_list"),
//      company = Some("demo_company"),
//      city = Some("demo_city"),
//      country = Some("India"),
//      timezone = Some("Asia/Kolkata"),
//
//
//
//      team_id = 0,
//      ta_id = 0,
//      email_domain = "",
//      list_id = None,
//      last_contacted_at = None,
//      prospect_category = ProspectCategory.NOT_CATEGORIZED,
//      prospect_category_id = 0
//    )
//
//
//    val generated = TemplateService.render(template, prospect).get
//
//    generated mustBe """<p>Hi john.doe 1001,</p><p></p><p>My name is  john  and I work as a business developer at Caltech studio. I found out about your product on web store and I really liked the function of the usage of product and decided to reach out.</p><p></p><p>Quick question: Are you currently looking for the new ways to grow your product sales?</p><p></p><p>I might have a solution that could become a new user acquisition channel for you, but the conversation can&#x27;t begin until I listen from you first.</p>"""
//
//  }
//
//
//
//
//
//  "render" should {
//    "empty custom fields should be handled in default filter" in {
//
//      val template =
//        """<span style="font-family: arial, helvetica, sans-serif; font-size: 14px;">Hi {{first_name}},</span><br /><br /><span style="font-family: arial, helvetica, sans-serif;">I was hoping to speak with someone at</span><span style="font-family: arial, helvetica, sans-serif;">{{company}} responsible for managing your online presence.</span><br /><br />You publish some very helpful resources {{free_report|default:'that I came across'}}... <span style="font-family: arial, helvetica, sans-serif;">and that's why I'm reaching out!</span><br style="font-family: arial, helvetica, sans-serif;" /><span style="font-family: arial, helvetica, sans-serif; font-size: 14px;"><span style="line-height: 14.98px;"><span style="font-family: arial, helvetica, sans-serif;"><br />Our platform <a href="http://u-2-me.com">U-2-me</a> connects knowledge seekers with knowledge providers such as yourself that distribute (free or paid) content.<br /><br />Just by having a free U-2-me profile you'll receive free exposure and more referral traffic interested in your area of expertise. We would maximize the purpose of your free content by expanding its reach. Secondly, we focus on a limited number of high-quality knowledge providers.<br /><br />I'd like to connect with someone (if it's not you) in your team to discuss whether it would be possible to set you up on our platform. I am not selling anything and my approach is purely consultative.<br /><br />Can you point me in the right direction please?<br /></span><br /></span><span style="font-family: arial, helvetica, sans-serif;">Best regards,</span></span><br /><span style="font-family: arial, helvetica, sans-serif; font-size: 14px;">Anthony</span><br /><br /><span style="font-family: arial, helvetica, sans-serif; font-size: 14px;"><span style="font-family: arial, helvetica, sans-serif;">http://u-2-me.com/join<br />07956906280</span><br /></span>""".trim
//
//
//
//      val prospect = Prospect(2983587,409,374,418,Some("Kelly"),Some("Jackson Higgins"),"fdsfsda","fdas",new DateTime("2018-12-13T21:59:44.033+05:30"),Json.parse("""{"number":"844-489-3223","website":"https://www.darkreading.com/","position":"Executive Editor","free_report":"","company_size_raw":"2-10","linkedin_personal":"https://www.linkedin.com/in/kellyj2/","linkedin_company_acc":"https://www.linkedin.com/company/dark-reading/"}"""),Some("Free report: No Personalization"),Some(2570),Some("fdsafd"),Some(""),Some(""),None,None,ProspectCategory.NOT_CATEGORIZED,1)
//
//
//      val generated = TemplateService.render(template, prospect).get
//
//      generated mustBe """<span style="font-family: arial, helvetica, sans-serif; font-size: 14px;">Hi Kelly,</span><br /><br /><span style="font-family: arial, helvetica, sans-serif;">I was hoping to speak with someone at</span><span style="font-family: arial, helvetica, sans-serif;">fdsafd responsible for managing your online presence.</span><br /><br />You publish some very helpful resources that I came across... <span style="font-family: arial, helvetica, sans-serif;">and that's why I'm reaching out!</span><br style="font-family: arial, helvetica, sans-serif;" /><span style="font-family: arial, helvetica, sans-serif; font-size: 14px;"><span style="line-height: 14.98px;"><span style="font-family: arial, helvetica, sans-serif;"><br />Our platform <a href="http://u-2-me.com">U-2-me</a> connects knowledge seekers with knowledge providers such as yourself that distribute (free or paid) content.<br /><br />Just by having a free U-2-me profile you'll receive free exposure and more referral traffic interested in your area of expertise. We would maximize the purpose of your free content by expanding its reach. Secondly, we focus on a limited number of high-quality knowledge providers.<br /><br />I'd like to connect with someone (if it's not you) in your team to discuss whether it would be possible to set you up on our platform. I am not selling anything and my approach is purely consultative.<br /><br />Can you point me in the right direction please?<br /></span><br /></span><span style="font-family: arial, helvetica, sans-serif;">Best regards,</span></span><br /><span style="font-family: arial, helvetica, sans-serif; font-size: 14px;">Anthony</span><br /><br /><span style="font-family: arial, helvetica, sans-serif; font-size: 14px;"><span style="font-family: arial, helvetica, sans-serif;">http://u-2-me.com/join<br />07956906280</span><br /></span>"""
//
//    }
//
//
//
//  }
//
//}
