package db_test_spec.api.campaigns.test_utils

import api.accounts.models.{AccountId, OrgId}
import api.accounts.{Account, OrganizationWithCurrentData, TeamAccount, TeamId}
import api.campaigns.models.CampaignTypeData.DripCampaignData
import api.campaigns.models.{CallSettingSenderDetails, CampaignAISequenceStatus, CampaignEmailSettingsId, CampaignStepData, CampaignStepId, CampaignStepType, CampaignType, CurrentStepStatusForScheduler, IgnoreProspectsInOtherCampaigns, LinkedinSettingSenderDetails, SmsSettingSenderDetails, StepContext, WhatsappSettingSenderDetails}
import api.campaigns.services.{CampaignId, ChannelSettingData}
import api.campaigns.{Campaign, CampaignCreateForm, CampaignEmailSettings, CampaignEmailSettingsUuid, CampaignEmailSettingsV2, CampaignSettings, CampaignStepVariant, CampaignStepVariantCreateOrUpdate, CampaignWithStatsAndEmail, ChannelSettingUuid, IsUpdate}
import api.emails.EmailSetting
import api.linkedin.models.{CDJobUid, CDLinkedInProfileId, CaptainDataLinkedinExtractionResults, LinkedinSettingUuid}
import api.prospects.models.{ProspectId, SrProspectColumns, UpdateProspectType}
import api.prospects.{AssignProspectsToCampaignResponse, CreateOrUpdateProspectsResult, ProspectCreateFormData}
import api.tasks.models.TaskPriority
import api.triggers.TriggerInDB
import api.whatsapp.WhatsappSettingUuid
import api.whatsapp.models.WhatsappAccountSettings
import app.db_test.CustomNotCategorizedAndDoNotContactIds
import db_test_spec.api.InitialData
import db_test_spec.api.campaigns.fixtures.CreateNewCampaignFixture.{createNewCampaign, createNewCampaignChannelSpecific, findCategorizedAndDoNotContactCustomIds}
import db_test_spec.api.campaigns.fixtures.DefaultCampaignParametersFixtures.defaultCampaignSettingsForWhatsapp
import db_test_spec.api.campaigns.fixtures.{CreateStepForCampaignFixture, StartCampaignFixture}
import db_test_spec.api.prospects.fixtures.ProspectFixtureForIntegrationTest
import db_test_spec.api.prospects.fixtures.ProspectFixtureForIntegrationTest.prospectService
import db_test_spec.api.scheduler.dao.SchedulerTestDAO
import db_test_spec.api.whatsapp.WhatsappAccount
import db_test_spec.utils.SrRandomTestUtils
import eventframework.ProspectObject
import io.smartreach.esp.api.emails.EmailSettingId
import io.sr.billing_common.models.PlanID.TRIAL
import org.joda.time.DateTime
import play.api.libs.json.{JsObject, JsValue, Json}
import scalikejdbc.{DB, scalikejdbcSQLInterpolationImplicitDef}
import sr_scheduler.CampaignStatus
import sr_scheduler.models.{CampaignEmailPriority, ChannelData, ChannelType, SelectedCalendarData}
import sr_scheduler.models.{CampaignEmailPriority, ChannelType}
import sr_scheduler.models.CampaignEmailPriority.CampaignEmailPriority
import utils.SRLogger
import utils.helpers.LogHelpers
import utils.mq.channel_scheduler.channels.ScheduleTasksData
import utils.testapp.TestAppTrait

import scala.concurrent.duration.*
import scala.concurrent.{Await, Future}
import scala.util.{Failure, Success, Try}

case class StepTypeWithPrevSubMergeTag(
  stepType: CampaignStepType,
  shouldHavePrevSubMergeTag: Boolean, // will only work if the steps supports {{previous_subject}} merge tag.
)

case class StartedCampaignDetails(campaignId: CampaignId, prospectIds: Seq[ProspectId], stepIds: Seq[Long])

case class CreateAndStartCampaignData(
                                       createCampaign: CampaignWithStatsAndEmail,
                                       addProspect: Seq[ProspectObject],
                                       addStep: CampaignStepVariant,
                                       campaign: Campaign,
                                       startCampaign: CampaignWithStatsAndEmail
                                     )
object CampaignUtils extends TestAppTrait{
  def createAndStartAutoEmailCampaign(
                                      initialData: InitialData,
                                      generateProspectCountIfNoGivenProspect: Int = 5,
                                      schedule_from_time: Int = 0,
                                      email_priority: CampaignEmailPriority = CampaignEmailPriority.EQUAL,
                                      enable_email_validation: Boolean = false,
                                      email_body: String = "Test",
                                      emailSettings:Option[EmailSetting] = None,
                                      prospects: Option[Seq[ProspectCreateFormData]] = None
                                    )(using Logger: SRLogger): Future[CreateAndStartCampaignData] = {
    val account: Account = initialData.account
    val emailSetting: EmailSetting = if(emailSettings.isDefined){
        emailSettings.get
    }else{initialData.emailSetting.get}
    val orgId: OrgId = OrgId(account.org.id)
    val accountId: AccountId = AccountId(account.internal_id)
    val teamId: TeamId = TeamId(account.teams.head.team_id)
    val taId: Long = account.teams.head.access_members.head.ta_id
    val emailSettingId = EmailSettingId(emailSetting.id.get.emailSettingId)
    val customNotCategorizedAndDoNotContactIds: CustomNotCategorizedAndDoNotContactIds = findCategorizedAndDoNotContactCustomIds(
      account.teams.head.prospect_categories_custom
    )

    for {
      // create a campaign
      createCampaign: CampaignWithStatsAndEmail <- createNewCampaign(
        orgId = orgId,
        accountId = accountId,
        teamId = teamId,
        taId = taId,
        campaignEmailSettingsId = CampaignEmailSettingsId(emailSettingId.emailSettingId),
        senderEmailSettingId = emailSettingId,
        receiverEmailSettingId = emailSettingId,
        schedule_from_time = schedule_from_time,
        email_priority = email_priority,
        enable_email_validation = enable_email_validation,
        ownerFirstName = account.first_name.get
      )

      // add prospect to campaign
      addProspect: Seq[ProspectObject] <- {
        ProspectFixtureForIntegrationTest.createUpdateOrAssignProspectFuture(
          campaignId = Option(CampaignId(createCampaign.id)),
          accountId = accountId,
          teamId = teamId,
          account = account,
          generateProspectCountIfNoGivenProspect = generateProspectCountIfNoGivenProspect,
          givenProspect = prospects
        )
      }

      //add auto email step to campaign
      addStep: CampaignStepVariant <- {
        CreateStepForCampaignFixture.createAutoEmailStepForCampaign(
          orgId = orgId,
          teamId = teamId,
          accountId = accountId,
          taId = taId,
          campaignId = CampaignId(createCampaign.id),
          email_body = email_body
        )
      }

      //campaign details
      campaign: Campaign <- Future (
        campaignDAO.findCampaignForCampaignUtilsOnly(
          id = createCampaign.id,
          teamId = teamId
        ).get
      )

      //start campaign
      startCampaign: CampaignWithStatsAndEmail <- {
        StartCampaignFixture.startCampaign(
          accountId = accountId,
          teamId = teamId,
          campaignWithStatsAndEmail = createCampaign,
          orgId = orgId,
          taId = taId,
          emailSetting = emailSetting,
          prospect_categories_custom_not_categorized = customNotCategorizedAndDoNotContactIds.not_categorized,
          prospect_categories_custom_do_not_contact = customNotCategorizedAndDoNotContactIds.do_not_contact,
          current_sending_email_accounts = emailSettingId.emailSettingId.toInt
        )
      }
    } yield {
      CreateAndStartCampaignData(
        createCampaign = createCampaign,
        addProspect = addProspect,
        addStep = addStep,
        campaign = campaign,
        startCampaign = startCampaign
      )
    }
  }

  def createNotStartedCampaignWithSteps(
    orgId: OrgId,
    accountId: AccountId,
    teamId: TeamId,
    taId: Long,
    emailSettingId: EmailSettingId,
    numberOfVariants: Int = 1,
    stepTypes: Seq[StepTypeWithPrevSubMergeTag],
    move_to_another_campaign_id: Option[CampaignId] = None,
    ownerFirstName:String
  )(using Logger: SRLogger): Future[Campaign] = {

    for {

      campaignWithStatsAndEmail: CampaignWithStatsAndEmail <- createNewCampaign(
        orgId = orgId,
        accountId = accountId,
        teamId = teamId,
        taId = taId,
        campaignEmailSettingsId = CampaignEmailSettingsId(emailSettingId.emailSettingId),
        senderEmailSettingId = emailSettingId,
        receiverEmailSettingId = emailSettingId,
        ownerFirstName = ownerFirstName
      )

      campaignHeadStepOpt: Option[CampaignStepVariant] <- {

        if (stepTypes.isEmpty) {

          Future.successful(None)

        } else {

          var stepId: Long = 0

          val seqOfFutStep = (1 to numberOfVariants).map { i =>

            // parent_id and step_id will be 0 for initial variant of the head step
            val campaignStepData = getDefaultStepData(
              stepType = stepTypes.head.stepType,
              subject = s"Head Step v-$i",
              body = s"Head Step Body v-$i",
              move_to_another_campaign_id = move_to_another_campaign_id,
              shouldHavePrevSubMergeTag = false,
            )

            val campaignStepVariantCreateOrUpdateEmail = CampaignStepVariantCreateOrUpdate(
              parent_id = 0,
              step_data = campaignStepData,
              step_delay = 0,
              notes = Some("Some Step"),
              priority = Some(TaskPriority.High),
            )

            val vf = CreateStepForCampaignFixture.createAutoEmailStepForCampaign(
              orgId = orgId,
              teamId = teamId,
              accountId = accountId,
              taId = taId,
              stepId = stepId,
              campaignId = CampaignId(id = campaignWithStatsAndEmail.id),
              campaignStepVariantCreateOrUpdateEmail = Some(campaignStepVariantCreateOrUpdateEmail),
            )

            stepId = Await.ready(vf, 5.seconds).value.get.get.step_id

            vf

          }

          seqOfFutStep.last.map(Some(_))

        }

      }

      _: List[CampaignStepVariant] <- {

        campaignHeadStepOpt match {

          case None =>

            Future.successful(List())

          case Some(campaignHeadStep) =>

            var parent_id = campaignHeadStep.step_id

            val seqOfFutStep = stepTypes.tail.zipWithIndex.flatMap { case (s, i) =>

              var stepId: Long = 0

              val seqOfFutStepVariants = (1 to numberOfVariants).map { vi =>

                val campaignStepData = getDefaultStepData(
                  stepType = s.stepType,
                  subject = s"Step $i - v$vi",
                  body = s"Step $i Body - v$vi",
                  move_to_another_campaign_id = move_to_another_campaign_id,
                  shouldHavePrevSubMergeTag = s.shouldHavePrevSubMergeTag,
                )

                val campaignStepVariantCreateOrUpdateEmail = CampaignStepVariantCreateOrUpdate(
                  parent_id = parent_id,
                  step_data = campaignStepData,
                  step_delay = 86400,
                  notes = Some("Some Step"),
                  priority = Some(TaskPriority.Normal),
                )

                val vf = CreateStepForCampaignFixture.createAutoEmailStepForCampaign(
                  orgId = orgId,
                  teamId = teamId,
                  accountId = accountId,
                  taId = taId,
                  stepId = stepId,
                  campaignId = CampaignId(id = campaignWithStatsAndEmail.id),
                  campaignHeadStepId = Some(campaignHeadStep.step_id),
                  campaignStepVariantCreateOrUpdateEmail = Some(campaignStepVariantCreateOrUpdateEmail),
                )

                val v = Await.ready(vf, 5.seconds).value.get.get

                stepId = v.step_id

                v

              }

              parent_id = seqOfFutStepVariants.last.step_id

              seqOfFutStepVariants.toList

            }

            Future.successful(seqOfFutStep.toList)

        }

      }

      campaign: Campaign <- Future.successful {

        campaignDAO.findCampaignForCampaignUtilsOnly(
          id = campaignWithStatsAndEmail.id,
          teamId = TeamId(id = campaignWithStatsAndEmail.team_id)
        ).get

      }

    } yield {

      campaign

    }

  }

  def createAndStartDripCampaign(
                                  initialData: InitialData,
                                  designDripForCase: Int,
                                  move_to_another_campaign: Option[CampaignId] = None,
                                  with_first_email_step_to_test_followup: Boolean = false
                                )(using logger: SRLogger): Future[StartedCampaignDetails] = {
    val orgId = initialData.account.org.id
    val accountId: Long = initialData.account.internal_id
    val teamId: Long = initialData.account.teams.head.team_id
    val account = initialData.account
    val emailSetting = initialData.emailSetting.get
    val taId: Long = account.teams.head.access_members.head.ta_id
    val emailSettingId: Long = emailSetting.id.get.emailSettingId
    val linkedinAccount = initialData.linkedinAccountSettings.get.settings
    val linkedinSettingUuid = linkedinAccount.uuid

    val createProspectData1 = ProspectCreateFormData(
      email = Some("<EMAIL>"),
      first_name = Some("Bruce"),
      last_name = Some("Wayne"),
      custom_fields = Json.obj(),

      list = None,
      company = None,
      city = None,
      country = None,
      timezone = None,

      state = None,
      job_title = None,
      phone = Some("+************"),
      phone_2 = None,
      phone_3 = None,
      linkedin_url = Some("https://linkedin.com/in/aditya-sadana")
    )

    val createProspectData2 = createProspectData1.copy(first_name = Some("tony"), last_name = Some("stark"),email = Some("<EMAIL>"), phone = Some("+************"), linkedin_url = None)
    val createProspectData3 = createProspectData1.copy(first_name = Some("wade"), last_name = Some("wilson"),email = Some("<EMAIL>"), phone = None, linkedin_url = Some("https://linkedin.url/in/deadpool"))
    val createProspectData4 = createProspectData1.copy(first_name = Some("arthur"), last_name = Some("fleck"),email = Some("<EMAIL>"), phone = None, linkedin_url = None)

    val prospects = designDripForCase match {
      case 1 =>
        Seq(createProspectData1.copy(linkedin_url = None), createProspectData3, createProspectData2.copy(phone = None))

      case 2 | 3 | 12 | 13 | 14 | 15 | 16 | 20 =>
        Seq(createProspectData1, createProspectData2, createProspectData3)

      case 17 | 18 | 19 =>
        Seq(createProspectData1.copy(email = Some("<EMAIL>")), createProspectData2.copy(email = Some("<EMAIL>")), createProspectData3.copy(email = Some("<EMAIL>")))


      case 4 | 6 | 7 | 8 | 9  =>
        Seq(createProspectData2, createProspectData3, createProspectData4)

      case 5 =>
        Seq(createProspectData2)

      case 10 | 11 =>
        Seq(createProspectData1.copy(phone = Some("+************")), createProspectData2.copy(phone = Some("+************")), createProspectData3.copy(phone = Some("+************")))

      case 21 =>
        Seq(createProspectData1.copy(
          email = Some("<EMAIL>"),
          phone = Some("+915467890543")),
          createProspectData2.copy(email = Some("<EMAIL>"), phone = Some("+915467890542")),
          createProspectData3.copy(email = Some("<EMAIL>"), phone = Some("+915467890542"))
        )

      case 22 =>
        Seq(createProspectData1.copy(
          email = Some("<EMAIL>"),
          phone = Some("+915467890543")),
          createProspectData2.copy(email = Some("<EMAIL>"), phone = Some("+915467890542")),
          createProspectData3.copy(email = Some("<EMAIL>"), phone = Some("+915467890542"))
        )


      case 23 =>
        /*
        This case is added for handling drip condition check for linkedin profile connected to the user-
        prospect 1 is not a connection but prospect 3 is not.
        */
        Seq(createProspectData1, createProspectData3)

    }

    val campaignSettings = CampaignSettings(
      // settings
      campaign_email_settings = List(
        CampaignEmailSettings(
          campaign_id = CampaignId(26),
          sender_email_setting_id = EmailSettingId(emailSettingId),
          receiver_email_setting_id = EmailSettingId(emailSettingId),
          team_id = TeamId(teamId),
          uuid = CampaignEmailSettingsUuid("temp_setting_id"),
          id = CampaignEmailSettingsId(emailSettingId),
          sender_email = "<EMAIL>",
          receiver_email = "<EMAIL>",
          max_emails_per_day_from_email_account = 100,
          signature = None,
          error = None,
          from_name = None
        )
      ),
      campaign_linkedin_settings = List(
        LinkedinSettingSenderDetails(
          channel_setting_uuid = ChannelSettingUuid(uuid = linkedinSettingUuid),
          team_id = TeamId(teamId),
          email = linkedinAccount.email,
          first_name = linkedinAccount.first_name,
          last_name = linkedinAccount.last_name,
          linkedin_profile_url = Some(linkedinAccount.profile_url),
          automation_enabled = linkedinAccount.captain_data_account_id.isDefined
        )
      ),
      campaign_call_settings = List(),
      campaign_whatsapp_settings = List(),
      campaign_sms_settings = List(),
      timezone = "Asia/Kolkata",
      daily_from_time = 0, // time since beginning of day in seconds
      daily_till_time = 86400, // time since beginning of day in seconds
      sending_holiday_calendar_id = None,

      // Sunday is the first day
      days_preference = List(true, true, true, true, true, true, true),

      mark_completed_after_days = 1,
      max_emails_per_day = 1000,
      open_tracking_enabled = true,
      click_tracking_enabled = true,
      enable_email_validation = false,
      ab_testing_enabled = true,

      ai_sequence_status = None,

      // warm up
      warmup_started_at = None,
      warmup_length_in_days = None,
      warmup_starting_email_count = None,
      show_soft_start_setting = false,

      // schedule start
      schedule_start_at = Option(DateTime.now()),
      schedule_start_at_tz = Option("Asia/Kolkata"),

      send_plain_text_email = Some(false),
      campaign_type = CampaignType.MultiChannel,


      email_priority = CampaignEmailPriority.EQUAL,
      append_followups = true,
      opt_out_msg = "Pivot",
      opt_out_is_text = true,
      add_prospect_to_dnc_on_opt_out = true,
      triggers = Seq(),
      sending_mode = None,
      selected_calendar_data = None
    )

    for {
      // create a campaign
      campaignWithStatsAndEmail: CampaignWithStatsAndEmail <- campaignService.createCampaign(
          orgId = orgId,
          accountId = accountId,
          teamId = teamId,
          taId = taId,
          data = CampaignCreateForm(
            name = None,
            timezone = Some("Asia/Kolkata"),
            campaign_owner_id = Some(accountId),
            campaign_type = CampaignType.MultiChannel,
          ),
          campaignSettings = Some(campaignSettings),
          permittedAccountIdsForEditCampaigns = Seq(accountId),
          ownerFirstName = "Aditya"
        )
        .flatMap{
          case Left(err) =>
            Future.failed(new Exception(s"Campaign Creation Failed :: $err"))

          case Right(value) =>
            Future.successful(value)
        }
      emailVariant0 <- campaignStepService.createVariant(
          orgId = orgId,
          data = CampaignStepVariantCreateOrUpdate(
            parent_id = 0L,
            step_data = CampaignStepData.AutoEmailStep(
              subject = "Subject",
              body = "body"
            ),
            step_delay = 0,
            notes = None,
            priority = None,
          ),
          teamId = teamId,
          userId = accountId,
          taId = taId,
          stepId = 0,
          campaignId = campaignWithStatsAndEmail.id,
          campaignHeadStepId = None
        )
        .flatMap{
          case Left(err) =>
            Future.failed(new Exception(s"Error while creating variant :: $err"))

          case Right(value) =>
            Future.successful(value)
        }

      emailVariant1 <- campaignStepService.createVariant(
          orgId = orgId,
          data = CampaignStepVariantCreateOrUpdate(
            parent_id = 0L,
            step_data = CampaignStepData.ManualEmailStep(
              subject = "Subject",
              body = "body"
            ),
            step_delay = 0,
            notes = None,
            priority = None,
          ),
          teamId = teamId,
          userId = accountId,
          taId = taId,
          stepId = 0,
          campaignId = campaignWithStatsAndEmail.id,
          campaignHeadStepId = None
        )
        .flatMap{
          case Left(err) =>
            Future.failed(new Exception(s"Error while creating variant :: $err"))

          case Right(value) =>
            Future.successful(value)
        }

      linkedinVariant1 <- campaignStepService.createVariant(
          orgId = orgId,
          data = CampaignStepVariantCreateOrUpdate(
            parent_id = emailVariant1.step_id,
            step_data = CampaignStepData.LinkedinMessageData(
              body = "Linkedin Message Body"
            ),
            step_delay = 86400,
            notes = None,
            priority = None,
          ),
          teamId = teamId,
          userId = accountId,
          taId = taId,
          stepId = 0,
          campaignId = campaignWithStatsAndEmail.id,
          campaignHeadStepId = Some(emailVariant1.step_id)
        )
        .flatMap{
          case Left(err) =>
            Future.failed(new Exception(s"Error while creating variant :: $err"))

          case Right(value) =>
            Future.successful(value)
        }

      emailVariant2 <- campaignStepService.createVariant(
          orgId = orgId,
          data = CampaignStepVariantCreateOrUpdate(
            parent_id = emailVariant1.step_id,
            step_data = CampaignStepData.ManualEmailStep(
              subject = "Subject",
              body = "body"
            ),
            step_delay = 86400,
            notes = None,
            priority = None,
          ),
          teamId = teamId,
          userId = accountId,
          taId = taId,
          stepId = 0,
          campaignId = campaignWithStatsAndEmail.id,
          campaignHeadStepId = Some(emailVariant1.step_id)
        )
        .flatMap{
          case Left(err) =>
            Future.failed(new Exception(s"Error while creating variant :: $err"))

          case Right(value) =>
            Future.successful(value)
        }

      callVariant1 <- campaignStepService.createVariant(
          orgId = orgId,
          data = CampaignStepVariantCreateOrUpdate(
            parent_id = emailVariant1.step_id,
            step_data = CampaignStepData.CallTaskData(
              body = "Call Body"
            ),
            step_delay = 86400,
            notes = None,
            priority = None,
          ),
          teamId = teamId,
          userId = accountId,
          taId = taId,
          stepId = 0,
          campaignId = campaignWithStatsAndEmail.id,
          campaignHeadStepId = Some(emailVariant1.step_id)
        )
        .flatMap{
          case Left(err) =>
            Future.failed(new Exception(s"Error while creating variant :: $err"))

          case Right(value) =>
            Future.successful(value)
        }


      callVariant2 <- campaignStepService.createVariant(
        orgId = orgId,
        data = CampaignStepVariantCreateOrUpdate(
          parent_id = 0L,
          step_data = CampaignStepData.CallTaskData(
            body = "Call Body Variant 2 "
          ),
          step_delay = 86400,
          notes = None,
          priority = None,
        ),
        teamId = teamId,
        userId = accountId,
        taId = taId,
        stepId = 0,
        campaignId = campaignWithStatsAndEmail.id,
        campaignHeadStepId = Some(emailVariant1.step_id)
      )
        .flatMap {
          case Left(err) =>
            Future.failed(new Exception(s"Error while creating variant :: $err"))

          case Right(value) =>
            Future.successful(value)
        }

      emailVariant3 <- campaignStepService.createVariant(
          orgId = orgId,
          data = CampaignStepVariantCreateOrUpdate(
            parent_id = emailVariant1.step_id,
            step_data = CampaignStepData.ManualEmailStep(
              subject = "Subject",
              body = "body"
            ),
            step_delay = 86400,
            notes = None,
            priority = None,
          ),
          teamId = teamId,
          userId = accountId,
          taId = taId,
          stepId = 0,
          campaignId = campaignWithStatsAndEmail.id,
          campaignHeadStepId = Some(emailVariant1.step_id)
        )
        .flatMap{
          case Left(err) =>
            Future.failed(new Exception(s"Error while creating variant :: $err"))

          case Right(value) =>
            Future.successful(value)
        }

      move_to_another_campaign <- {
        if(move_to_another_campaign.isDefined){
          campaignStepService.createVariant(
            orgId = orgId,
            data = CampaignStepVariantCreateOrUpdate(
              parent_id = emailVariant1.step_id,
              step_data = CampaignStepData.MoveToAnotherCampaignStepData(
                move_to_another_campaign_id = move_to_another_campaign.get
              ),
              step_delay = 86400,
              notes = None,
              priority = None,
            ),
            teamId = teamId,
            userId = accountId,
            taId = taId,
            stepId = 0,
            campaignId = campaignWithStatsAndEmail.id,
            campaignHeadStepId = Some(emailVariant1.step_id)
          )
            .flatMap {
              case Left(err) =>
                Future.failed(new Exception(s"Error while creating variant :: $err"))

              case Right(value) =>
                Future.successful(value)
            }
        } else Future.successful(emailVariant3) // Just to make it work.
      }

      account: Account <- Future {
        accountService.find(id = accountId).get
      }

      createdProspects: CreateOrUpdateProspectsResult <- Future {
        prospectService.createOrUpdateProspects(
          ownerAccountId = accountId,
          teamId = teamId,
          listName = None,
          prospects = prospects,
          updateProspectType = UpdateProspectType.ForceUpdate,
          ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads = true,

          doerAccount = account,
          prospectSource = None,
          prospectAccountId = None,

          campaign_id = Some(campaignWithStatsAndEmail.id),
          prospect_tags = None,
          ignoreProspectInOtherCampaign = IgnoreProspectsInOtherCampaigns.DoNotIgnore,
          deduplicationColumns = Some(Seq(SrProspectColumns.Email)),
          auditRequestLogId = None,
          batchInsertLimit = 1000,

          SRLogger = logger
        ).get
      }
      //
      //      assignProspect: AssignProspectsToCampaignResponse <- prospectService.assignProspectsToCampaign(
      //        teamId = teamId,
      //        campaignId = Some(campaignWithStatsAndEmail.id),
      //        prospectIds = prospectIds,
      //        permittedAccountIdsForEditingCampaigns = Seq(accountId),
      //        permittedAccountIdsForEditingProspects = Seq(accountId),
      //        account = account,
      //        ignore_prospects_in_other_campaigns = IgnoreProspectsInOtherCampaigns.DoNotIgnore,
      //        accountId = accountId,
      //        Logger = logger
      //      ) match {
      //        case Left(err) =>
      //          Future.failed(new Exception(s"Error while assigning prospects :: $err"))
      //
      //        case Right(value) =>
      //          Future.successful(value)
      //      }

      _: List[ChannelSettingData] <- campaignService.updateAccountSetting(
          channel_setting_data = ChannelSettingData(
            channel_type = ChannelType.LinkedinChannel,
            channel_setting_uuid = ChannelSettingUuid(linkedinSettingUuid),
            team_id = TeamId(teamId),
            campaign_id = CampaignId(campaignWithStatsAndEmail.id)
          ),
          team_id = teamId,
          campaign_id = campaignWithStatsAndEmail.id
        )
        .flatMap{
          case Left(err) =>
            Future.failed(new Exception(s"Error while updating call setting :: $err"))

          case Right(value) =>
            Future.successful(value)
        }

      _: List[ChannelSettingData] <- campaignService.updateAccountSetting(
          channel_setting_data = ChannelSettingData(
            channel_type = ChannelType.CallChannel,
            channel_setting_uuid = ChannelSettingUuid(initialData.callSettingUuid.get),
            team_id = TeamId(teamId),
            campaign_id = CampaignId(campaignWithStatsAndEmail.id)
          ),
          team_id = teamId,
          campaign_id = campaignWithStatsAndEmail.id
        )
        .flatMap{
          case Left(err) =>
            Future.failed(new Exception(s"Error while updating call setting :: $err"))

          case Right(value) =>
            Future.successful(value)
        }

      campaignAfterUpdatingEmailSetting: CampaignWithStatsAndEmail <- Future {
        campaignService.updateEmailSettingsV2(
            id = CampaignId(campaignWithStatsAndEmail.id),
            teamId = TeamId(teamId),
            data = List(CampaignEmailSettingsV2(
              sender_email_settings_id = emailSettingId,
              receiver_email_settings_id = emailSettingId
            )),
            campaign_status = CampaignStatus.NOT_STARTED,
              planID = TRIAL,
            orgId = OrgId(orgId)
          )
          .flatMap{ 
           case Left(err) =>
                  Failure(new Exception(s"Error while updating email settings :: $err"))
            case Right(None) => Failure(new Exception("updateEmailSettingsV2 failed"))
            case Right(Some(value)) => Success(value)
          }.get
      }

      organizationWithCurrentData: OrganizationWithCurrentData <- Future {
        organizationService.getOrgWithCurrentData(
            orgId = OrgId(orgId)
          )
          .flatMap{
            case None =>
              Failure(new Exception("Organization not found"))

            case Some(org) =>
              Success(org)
          }.get
      }

      teamAccount: TeamAccount <- Future {
        account.teams.find(_.team_id == teamId).get
      }

      /*
        the below call is for storing the connection in linkedin_profile_connections table so the drip campaign can fetch
        accurately based on the conditions.
        in case 23 (test for linkedin_profile_connected), we are adding 2 prospects - variant 1 and variant 3
        We are storing the linkedin url for prospect variant 3 (so connection is established)
        We are not storing linkedin url for prospect variant 1 (so connection is not established)
        */
      linkedinConnection: List[Long] <- Future.fromTry(
        linkedinConnectionsService.saveLinkedinConnections(
          linkedin_connections = List(CaptainDataLinkedinExtractionResults(
          linkedinProfileId = CDLinkedInProfileId(1234L),
          salesNavigatorProfileId = Some("sales-nav-98765"),
          firstName = Some("Wade"),
          lastName = Some("Wilson"),
          jobTitle = Some("Freelance Mercenary"),
          headline = Some("Merc with a Mouth | Problem Solver | Regeneration Specialist"),
          linkedinProfileHandle = "wadewilson",
          linkedinProfileUrl = "https://linkedin.url/in/deadpool",
          connectedAt = DateTime.now().minusDays(14),
          extractedAt = DateTime.now().minusHours(2),
          teamId = TeamId(initialData.account.teams.head.team_id),
          linkedinSettingUuid = LinkedinSettingUuid(initialData.linkedinAccountSettings.get.settings.uuid),
          extract_all = true,
          cdJobUid = CDJobUid("job_wx987654321")
        ))
        )
      )

      dripCampaign: Int <- Future {
        val (nodes, edges, headNodeId) = designDripForCase match {
          case 1 =>
            var node = if(with_first_email_step_to_test_followup) {
              List(
                Json.obj("id" -> emailVariant0.step_id.toString, "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "1", "type" -> "step"),"type" -> "has_phone_number"),
              )
            } else List()
            var edges = if(with_first_email_step_to_test_followup) {
              List(
                Json.obj("id" -> "xyz", "source" ->  emailVariant0.step_id.toString, "target" -> "condition_call", "label" -> "no_condition"),
              )
            } else List()
            val head = if(with_first_email_step_to_test_followup) {
              emailVariant0.step_id.toString
            } else {"condition_call"}


            edges = edges ++ List(
              Json.obj("id" -> "xyz", "source" -> "condition_call", "target" -> "condition_email_1", "label" -> "yes"),
              Json.obj("id" -> "abc", "source" -> "condition_call", "target" -> "has_linkedin_url", "label" -> "no"),
              Json.obj("id" -> "abc", "source" -> "condition_email_1", "target" -> emailVariant1.step_id.toString, "label" -> "yes"),
              Json.obj("id" -> "abc", "source" -> emailVariant1.step_id.toString, "target" -> callVariant1.step_id.toString, "label" -> "no_condition"),
              Json.obj("id" -> "abc", "source" -> "has_linkedin_url", "target" -> linkedinVariant1.step_id.toString, "label" -> "yes"),
              Json.obj("id" -> "abc", "source" -> linkedinVariant1.step_id.toString, "target" -> emailVariant2.step_id.toString, "label" -> "no_condition"),
            )

            node = node ++ List(
              Json.obj("id" -> "condition_call", "position" -> Json.obj("x" -> 0, "y" -> 0), "data" -> Json.obj("label" -> "has_phone_number", "type" -> "condition"),"type" -> "has_phone_number"),
              Json.obj("id" -> "condition_email_1", "position" -> Json.obj("x" -> 0, "y" -> 0), "data" -> Json.obj("label" -> "has_email", "type" -> "condition"),"type" -> "has_email"),
              Json.obj("id" -> "has_linkedin_url", "position" -> Json.obj("x" -> 0, "y" -> 0), "data" -> Json.obj("label" -> "has_linkedin_url", "type" -> "condition"),"type" -> "has_linkedin_url"),
              Json.obj("id" -> emailVariant1.step_id.toString, "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "1", "type" -> "step"),"type" -> "step"),
              Json.obj("id" -> linkedinVariant1.step_id.toString, "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "2", "type" -> "step"),"type" -> "step"),
              Json.obj("id" -> emailVariant2.step_id.toString, "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "3", "type" -> "step"),"type" -> "step"),
              Json.obj("id" -> callVariant1.step_id.toString, "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "4", "type" -> "step"),"type" -> "step"),
            )

            (Json.toJson(node), Json.toJson(edges), head)

          case 2 =>
            var node = if(with_first_email_step_to_test_followup) {
              List(
                Json.obj("id" -> emailVariant0.step_id.toString, "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "1", "type" -> "step"),"type" -> "has_phone_number"),
              )
            } else List()
            var edges = if(with_first_email_step_to_test_followup) {
              List(
                Json.obj("id" -> "xyz", "source" ->  emailVariant0.step_id.toString, "target" -> "condition_call", "label" -> "no_condition"),
              )
            } else List()
            val head = if(with_first_email_step_to_test_followup) {
              emailVariant0.step_id.toString
            } else {"condition_call"}

            node = node ++ List(
              Json.obj("id" -> "condition_call", "position" -> Json.obj("x" -> 0, "y" -> 0), "data" -> Json.obj("label" -> "has_linkedin_url", "type" -> "condition"),"type" -> "step"),
              Json.obj("id" -> emailVariant1.step_id.toString, "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "1", "type" -> "step"),"type" -> "step"),
              Json.obj("id" -> linkedinVariant1.step_id.toString, "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "2", "type" -> "step"),"type" -> "step"),
            )
            edges = edges ++ List(
              Json.obj("id" -> "1-2", "source" -> "condition_call", "target" -> emailVariant1.step_id.toString, "label" -> "no"),
              Json.obj("id" -> "2-3", "source" -> "condition_call", "target" -> linkedinVariant1.step_id.toString, "label" -> "yes")
            )
            (Json.toJson(node), Json.toJson(edges), head)

          case 3 =>
            var node = if(with_first_email_step_to_test_followup) {
              List(
                Json.obj("id" -> emailVariant0.step_id.toString, "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "1", "type" -> "step"),"type" -> "has_phone_number"),
              )
            } else List()
            var edges = if(with_first_email_step_to_test_followup) {
              List(
                Json.obj("id" -> "xyz", "source" ->  emailVariant0.step_id.toString, "target" -> "condition_call", "label" -> "no_condition"),
              )
            } else List()
            val head = if(with_first_email_step_to_test_followup) {
              emailVariant0.step_id.toString
            } else {emailVariant1.step_id.toString}
            node = node ++ List(
              Json.obj("id" -> emailVariant1.step_id.toString, "position" -> Json.obj("x" -> 0, "y" -> 0), "data" -> Json.obj("label" -> "1", "type" -> "step"),"type" -> "step"),
              Json.obj("id" -> "condition_call", "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "has_linkedin_url", "type" -> "condition"),"type" -> "step"),
              Json.obj("id" -> linkedinVariant1.step_id.toString, "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "2", "type" -> "step"),"type" -> "step"),
              Json.obj("id" -> emailVariant2.step_id.toString, "position" -> Json.obj("x" -> 100, "y" -> 100), "data" -> Json.obj("label" -> "3", "type" -> "step"),"type" -> "step"),
            )
            edges = edges ++ List(
              Json.obj("id" -> "1-2", "source" -> emailVariant1.step_id.toString, "target" -> "condition_call", "label" -> "no_condition"),
              Json.obj("id" -> "2-3", "source" -> "condition_call", "target" -> linkedinVariant1.step_id.toString, "label" -> "yes"),
              Json.obj("id" -> "2-4", "source" -> "condition_call", "target" -> emailVariant2.step_id.toString, "label" -> "no")
            )
            (Json.toJson(node), Json.toJson(edges), head)
          case 4 =>

            /**
             *          has_phone_number
             *    yes /				       no \
             *  has_email			      has_linkedin_url
             *  yes/							yes/            no\
             * email_step			linkedin_step      has_email
             *    |								    |          yes/
             * call_step				email_step	    email_step
             */
            var node = if(with_first_email_step_to_test_followup) {
              List(
                Json.obj("id" -> emailVariant0.step_id.toString, "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "1", "type" -> "step"),"type" -> "has_phone_number"),
              )
            } else List()
            var edges = if(with_first_email_step_to_test_followup) {
              List(
                Json.obj("id" -> "xyz", "source" ->  emailVariant0.step_id.toString, "target" -> "condition_call", "label" -> "no_condition"),
              )
            } else List()
            val head = if(with_first_email_step_to_test_followup) {
              emailVariant0.step_id.toString
            } else {"condition_call"}
            node = node ++ List(
              Json.obj("id" -> "condition_call", "position" -> Json.obj("x" -> 0, "y" -> 0), "data" -> Json.obj("label" -> "has_phone_number", "type" -> "condition"),"type" -> "step"),
              Json.obj("id" -> "condition_email_1", "position" -> Json.obj("x" -> 0, "y" -> 0), "data" -> Json.obj("label" -> "has_email", "type" -> "condition"),"type" -> "step"),
              Json.obj("id" -> "has_linkedin_url", "position" -> Json.obj("x" -> 0, "y" -> 0), "data" -> Json.obj("label" -> "has_linkedin_url", "type" -> "condition"),"type" -> "step"),
              Json.obj("id" -> emailVariant1.step_id.toString, "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "1", "type" -> "step"),"type" -> "step"),
              Json.obj("id" -> linkedinVariant1.step_id.toString, "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "2", "type" -> "step"),"type" -> "step"),
              Json.obj("id" -> "condition_email_2", "position" -> Json.obj("x" -> 0, "y" -> 0), "data" -> Json.obj("label" -> "has_email", "type" -> "condition"),"type" -> "step"),
              Json.obj("id" -> callVariant1.step_id.toString, "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "4", "type" -> "step"),"type" -> "step"),
              Json.obj("id" -> emailVariant2.step_id.toString, "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "3", "type" -> "step"),"type" -> "step"),
              Json.obj("id" -> emailVariant3.step_id.toString, "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "5", "type" -> "step"),"type" -> "step"),
            )
            edges = edges ++ List(
              Json.obj("id" -> "xyz", "source" -> "condition_call", "target" -> "condition_email_1", "label" -> "yes"),
              Json.obj("id" -> "abc", "source" -> "condition_call", "target" -> "has_linkedin_url", "label" -> "no"),
              Json.obj("id" -> "abc", "source" -> "condition_email_1", "target" -> emailVariant1.step_id.toString, "label" -> "yes"),
              Json.obj("id" -> "abc", "source" -> emailVariant1.step_id.toString, "target" -> callVariant1.step_id.toString, "label" -> "no_condition"),
              Json.obj("id" -> "abc", "source" -> "has_linkedin_url", "target" -> linkedinVariant1.step_id.toString, "label" -> "yes"),
              Json.obj("id" -> "abc", "source" -> linkedinVariant1.step_id.toString, "target" -> emailVariant2.step_id.toString, "label" -> "no_condition"),
              Json.obj("id" -> "abc", "source" -> "has_linkedin_url", "target" -> "condition_email_2", "label" -> "no"),
              Json.obj("id" -> "abc", "source" -> "condition_email_2", "target" -> emailVariant3.step_id.toString, "label" -> "no_condition"),
            )
            (Json.toJson(node), Json.toJson(edges), head)


          case 5 =>
              /*
                      has_email
                     /
                  send_email
                  /
                has_read_email
                /
              has_phone_number
              /
            create_call_task

            */


            (Json.toJson(List(
              Json.obj("id" -> "has_email", "position" -> Json.obj("x" -> 0, "y" -> 0), "data" -> Json.obj("label" -> "has_email", "type" -> "condition"),"type" -> "step"),
              Json.obj("id" -> emailVariant1.step_id.toString, "position" -> Json.obj("x" -> 0, "y" -> 0), "data" -> Json.obj("label" -> emailVariant1.step_id.toString, "type" -> "step"),"type" -> "step"),
              Json.obj("id" -> "has_read_email", "position" -> Json.obj("x" -> 0, "y" -> 0), "data" -> Json.obj("label" -> "has_read_email", "type" -> "condition"),"type" -> "step"),
              Json.obj("id" -> "has_phone_number", "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "has_phone_number", "type" -> "condition"),"type" -> "step"),
              Json.obj("id" -> callVariant2.step_id.toString, "position" -> Json.obj("x" -> 0, "y" -> 0), "data" -> Json.obj("label" -> callVariant2.step_id.toString, "type" -> "step"),"type" -> "step"),
            )), Json.toJson(List(
              Json.obj("id" -> "xyz", "source" -> "has_email", "target" -> emailVariant1.step_id.toString, "label" -> "no_condition"),
              Json.obj("id" -> "abc", "source" -> emailVariant1.step_id.toString, "target" -> "has_read_email", "label" -> "yes"),
              Json.obj("id" -> "abc", "source" -> "has_read_email", "target" -> "has_phone_number", "label" -> "yes"),
              Json.obj("id" -> "abc", "source" -> "has_phone_number", "target" -> callVariant2.step_id.toString, "label" -> "yes"),
            )),
              "has_email"
            )

          case 6 =>
            /*

                call_step
          */

            val edges: List[JsObject] = List()

            (Json.toJson(List(
              Json.obj("id" -> callVariant2.step_id.toString, "position" -> Json.obj("x" -> 0, "y" -> 0), "data" -> Json.obj("label" -> callVariant2.step_id.toString, "type" -> "step"),"type" -> "step"),
            )), Json.toJson(edges),
              callVariant2.step_id.toString
            )

          case 7 =>
            /*
              has_phone_number
               /
             call_step
           */


            (Json.toJson(List(
              Json.obj("id" -> "has_phone_number", "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "has_phone_number", "type" -> "condition"),"type" -> "step"),
              Json.obj("id" -> callVariant2.step_id.toString, "position" -> Json.obj("x" -> 0, "y" -> 0), "data" -> Json.obj("label" -> callVariant2.step_id.toString, "type" -> "step"),"type" -> "step"),
            )), Json.toJson(List(
              Json.obj("id" -> "abc", "source" -> "has_phone_number", "target" -> callVariant2.step_id.toString, "label" -> "yes")
            )
            ),
              "has_phone_number"
            )


          case 8 =>
            /*

                call_step
          */

            val edges: List[JsObject] = List()

            (Json.toJson(List(
              Json.obj("id" -> linkedinVariant1.step_id.toString, "position" -> Json.obj("x" -> 0, "y" -> 0), "data" -> Json.obj("label" -> linkedinVariant1.step_id.toString, "type" -> "step"),"type" -> "step"),
            )), Json.toJson(edges),
              linkedinVariant1.step_id.toString
            )

          case 9 =>
            /*
              has_phone_number
               /
             call_step
           */


            (Json.toJson(List(
              Json.obj("id" -> "has_linkedin_url", "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "has_linkedin_url", "type" -> "condition"),"type" -> "step"),
              Json.obj("id" -> linkedinVariant1.step_id.toString, "position" -> Json.obj("x" -> 0, "y" -> 0), "data" -> Json.obj("label" -> linkedinVariant1.step_id.toString, "type" -> "step"),"type" -> "step"),
            )),
              Json.toJson(List(
                Json.obj("id" -> "abc", "source" -> "has_linkedin_url", "target" -> linkedinVariant1.step_id.toString, "label" -> "yes")
              )
              ),
              "has_linkedin_url"
            )

          case 10 =>
            /**
             *          valid_email
             *    yes /				       no \
             * email_step			      call_step
             */


            (Json.toJson(List(
              Json.obj("id" -> "valid_email", "position" -> Json.obj("x" -> 0, "y" -> 0), "data" -> Json.obj("label" -> "valid_email", "type" -> "condition"),"type" -> "step"),
              Json.obj("id" -> emailVariant1.step_id.toString, "position" -> Json.obj("x" -> 0, "y" -> 0), "data" -> Json.obj("label" -> emailVariant1.step_id.toString, "type" -> "step"),"type" -> "step"),
              Json.obj("id" -> callVariant2.step_id.toString, "position" -> Json.obj("x" -> 0, "y" -> 0), "data" -> Json.obj("label" -> callVariant2.step_id.toString, "type" -> "step"),"type" -> "step"),
            )), Json.toJson(List(
              Json.obj("id" -> "xyz", "source" -> "valid_email", "target" -> emailVariant1.step_id.toString, "label" -> "yes"),
              Json.obj("id" -> "xyz", "source" -> "valid_email", "target" -> callVariant2.step_id.toString, "label" -> "no"),
            )),
              "valid_email"
            )

          case 11 =>
            /**
             *          invalid_email
             *    yes /				       no \
             * email_step			      call_step
             */


            (Json.toJson(List(
              Json.obj("id" -> "invalid_email", "position" -> Json.obj("x" -> 0, "y" -> 0), "data" -> Json.obj("label" -> "invalid_email", "type" -> "condition"),"type" -> "step"),
              Json.obj("id" -> emailVariant1.step_id.toString, "position" -> Json.obj("x" -> 0, "y" -> 0), "data" -> Json.obj("label" -> emailVariant1.step_id.toString, "type" -> "step"),"type" -> "step"),
              Json.obj("id" -> callVariant2.step_id.toString, "position" -> Json.obj("x" -> 0, "y" -> 0), "data" -> Json.obj("label" -> callVariant2.step_id.toString, "type" -> "step"),"type" -> "step"),
            )), Json.toJson(List(
              Json.obj("id" -> "xyz", "source" -> "valid_email", "target" -> callVariant2.step_id.toString, "label" -> "yes"),
              Json.obj("id" -> "xyz", "source" -> "valid_email", "target" -> emailVariant1.step_id.toString, "label" -> "no"),
            )),
              "invalid_email"
            )
          case 12 =>

            val node =  List(
              Json.obj("id" -> emailVariant0.step_id.toString, "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "1", "type" -> "step"),"type" -> "step"),
              Json.obj("id" -> "bounced_condition", "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "bounced", "type" -> "condition"),"type" -> "step"),
              Json.obj("id" -> linkedinVariant1.step_id.toString, "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "2", "type" -> "step"),"type" -> "step"),
              Json.obj("id" -> emailVariant1.step_id.toString, "position" -> Json.obj("x" -> 100, "y" -> 100), "data" -> Json.obj("label" -> "3", "type" -> "step"),"type" -> "step"),
            )
            val edges =  List(
              Json.obj("id" -> "xyz", "source" ->  emailVariant0.step_id.toString, "target" -> "bounced_condition", "label" -> "no_condition"),
              Json.obj("id" -> "2-3", "source" -> "bounced_condition", "target" -> linkedinVariant1.step_id.toString, "label" -> "yes"),
              Json.obj("id" -> "2-4", "source" -> "bounced_condition", "target" -> emailVariant1.step_id.toString, "label" -> "no")
            )
            (Json.toJson(node), Json.toJson(edges), emailVariant0.step_id.toString)
          case 13 =>

            val node =  List(
              Json.obj("id" -> emailVariant0.step_id.toString, "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "1", "type" -> "step"),"type" -> "step"),
              Json.obj("id" -> "has_opened", "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "has_opened", "type" -> "condition"),"type" -> "step"),
              Json.obj("id" -> linkedinVariant1.step_id.toString, "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "2", "type" -> "step"),"type" -> "step"),
              Json.obj("id" -> emailVariant1.step_id.toString, "position" -> Json.obj("x" -> 100, "y" -> 100), "data" -> Json.obj("label" -> "3", "type" -> "step"),"type" -> "step"),
            )
            val edges =  List(
              Json.obj("id" -> "xyz", "source" ->  emailVariant0.step_id.toString, "target" -> "has_opened", "label" -> "no_condition"),
              Json.obj("id" -> "2-3", "source" -> "has_opened", "target" -> linkedinVariant1.step_id.toString, "label" -> "yes"),
              Json.obj("id" -> "2-4", "source" -> "has_opened", "target" -> emailVariant1.step_id.toString, "label" -> "no")
            )
            (Json.toJson(node), Json.toJson(edges), emailVariant0.step_id.toString)

          case 14 =>

            val node = List(
              Json.obj("id" -> emailVariant0.step_id.toString, "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "1", "type" -> "step"),"type" -> "step"),
              Json.obj("id" -> "has_replied", "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "has_replied", "type" -> "condition"),"type" -> "step"),
              Json.obj("id" -> linkedinVariant1.step_id.toString, "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "2", "type" -> "step"),"type" -> "step"),
              Json.obj("id" -> emailVariant1.step_id.toString, "position" -> Json.obj("x" -> 100, "y" -> 100), "data" -> Json.obj("label" -> "3", "type" -> "step"),"type" -> "step"),
            )
            val edges = List(
              Json.obj("id" -> "xyz", "source" -> emailVariant0.step_id.toString, "target" -> "has_replied", "label" -> "no_condition"),
              Json.obj("id" -> "2-3", "source" -> "has_replied", "target" -> linkedinVariant1.step_id.toString, "label" -> "yes"),
              Json.obj("id" -> "2-4", "source" -> "has_replied", "target" -> emailVariant1.step_id.toString, "label" -> "no")
            )
            (Json.toJson(node), Json.toJson(edges), emailVariant0.step_id.toString)

          case 15 =>

            val node = List(
              Json.obj("id" -> emailVariant0.step_id.toString, "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "1", "type" -> "step"),"type" -> "step"),
              Json.obj("id" -> "has_replied_do_not_contact", "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "has_replied_do_not_contact", "type" -> "condition"),"type" -> "step"),
              Json.obj("id" -> linkedinVariant1.step_id.toString, "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "2", "type" -> "step"),"type" -> "step"),
              Json.obj("id" -> emailVariant1.step_id.toString, "position" -> Json.obj("x" -> 100, "y" -> 100), "data" -> Json.obj("label" -> "3", "type" -> "step"),"type" -> "step"),
            )
            val edges = List(
              Json.obj("id" -> "xyz", "source" -> emailVariant0.step_id.toString, "target" -> "has_replied_do_not_contact", "label" -> "no_condition"),
              Json.obj("id" -> "2-3", "source" -> "has_replied_do_not_contact", "target" -> linkedinVariant1.step_id.toString, "label" -> "yes"),
              Json.obj("id" -> "2-4", "source" -> "has_replied_do_not_contact", "target" -> emailVariant1.step_id.toString, "label" -> "no")
            )
            (Json.toJson(node), Json.toJson(edges), emailVariant0.step_id.toString)

          case 16 =>

            val node =  List(
              Json.obj("id" -> emailVariant0.step_id.toString, "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "1", "type" -> "step"),"type" -> "step"),
              Json.obj("id" -> "has_opened", "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "has_opened", "type" -> "condition"),"type" -> "step"),
              Json.obj("id" -> emailVariant1.step_id.toString, "position" -> Json.obj("x" -> 100, "y" -> 100), "data" -> Json.obj("label" -> "3", "type" -> "step"),"type" -> "step"),
            )
            val edges =  List(
              Json.obj("id" -> "xyz", "source" ->  emailVariant0.step_id.toString, "target" -> "has_opened", "label" -> "no_condition"),
              Json.obj("id" -> "2-4", "source" -> "has_opened", "target" -> emailVariant1.step_id.toString, "label" -> "until")
            )
            (Json.toJson(node), Json.toJson(edges), emailVariant0.step_id.toString)

          case 17 =>

            val node = List(
              Json.obj("id" -> emailVariant0.step_id.toString, "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "1", "type" -> "step"),"type" -> "step"),
              Json.obj("id" -> "has_opened", "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "has_opened", "type" -> "condition"),"type" -> "step"),
              Json.obj("id" -> move_to_another_campaign.step_id.toString, "position" -> Json.obj("x" -> 100, "y" -> 100), "data" -> Json.obj("label" -> "3", "type" -> "step"),"type" -> "step"),
            )
            val edges = List(
              Json.obj("id" -> "xyz", "source" -> "has_opened", "target" -> emailVariant0.step_id.toString, "label" -> "yes"),
              Json.obj("id" -> "2-4", "source" -> "has_opened", "target" -> move_to_another_campaign.step_id.toString, "label" -> "no")
            )
            (Json.toJson(node), Json.toJson(edges), "has_opened")


          case 18 =>

            val node = List(
              Json.obj("id" -> emailVariant0.step_id.toString, "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "1", "type" -> "step"),"type" -> "step"),
              Json.obj("id" -> "has_opened", "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "has_opened", "type" -> "condition"),"type" -> "step"),
              Json.obj("id" -> linkedinVariant1.step_id.toString, "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "2", "type" -> "step"),"type" -> "step"),
              Json.obj("id" -> move_to_another_campaign.step_id.toString, "position" -> Json.obj("x" -> 100, "y" -> 100), "data" -> Json.obj("label" -> "3", "type" -> "step"),"type" -> "step"),
            )
            val edges = List(
              Json.obj("id" -> "xyz", "source" -> emailVariant0.step_id.toString, "target" -> "has_opened", "label" -> "no_condition"),
              Json.obj("id" -> "2-3", "source" -> "has_opened", "target" -> linkedinVariant1.step_id.toString, "label" -> "yes"),
              Json.obj("id" -> "2-4", "source" -> "has_opened", "target" -> move_to_another_campaign.step_id.toString, "label" -> "no")
            )
            (Json.toJson(node), Json.toJson(edges), emailVariant0.step_id.toString)

          case 19 =>

            val node = List(
              Json.obj("id" -> emailVariant0.step_id.toString, "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "1", "type" -> "step"),"type" -> "step"),
              Json.obj("id" -> "has_opened", "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "has_opened", "type" -> "condition"),"type" -> "step"),
              Json.obj("id" -> linkedinVariant1.step_id.toString, "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "2", "type" -> "step"),"type" -> "step"),
              Json.obj("id" -> move_to_another_campaign.step_id.toString, "position" -> Json.obj("x" -> 100, "y" -> 100), "data" -> Json.obj("label" -> "3", "type" -> "step"),"type" -> "step"),
            )
            val edges = List(
              Json.obj("id" -> "xyz", "source" -> emailVariant0.step_id.toString, "target" -> "has_opened", "label" -> "no_condition"),
              Json.obj("id" -> "2-3", "source" -> "has_opened", "target" -> linkedinVariant1.step_id.toString, "label" -> "no"),
              Json.obj("id" -> "2-4", "source" -> "has_opened", "target" -> move_to_another_campaign.step_id.toString, "label" -> "yes")
            )
            (Json.toJson(node), Json.toJson(edges), emailVariant0.step_id.toString)

          case 20 =>


            val nodes = Json.parse(
              s"""
                |[
                |  {
                |    "id": "condition_call",
                |    "data": {
                |      "phone": "+91-7518710532",
                |      "label": "has_linkedin_url",
                |      "type": "condition"
                |    },
                |    "type": "has_phone",
                |    "style": {
                |      "opacity": 1
                |    },
                |    "measured": {
                |      "width": 129,
                |      "height": 54
                |    },
                |    "position": {
                |      "x": -64.5,
                |      "y": 285
                |    },
                |    "internals": {
                |      "z": 0,
                |      "userNode": {
                |        "id": "DfxYW",
                |        "data": {
                |          "phone": "+91-7518710532"
                |        },
                |        "type": "has_phone",
                |        "style": {
                |          "opacity": 1
                |        },
                |        "measured": {
                |          "width": 129,
                |          "height": 54
                |        },
                |        "position": {
                |          "x": -64.5,
                |          "y": 285
                |        },
                |        "internals": {
                |          "z": 0,
                |          "userNode": {
                |            "id": "DfxYW",
                |            "data": {
                |              "phone": "+91-7518710532"
                |            },
                |            "type": "has_phone",
                |            "style": {
                |              "opacity": 1
                |            },
                |            "measured": {
                |              "width": 129,
                |              "height": 54
                |            },
                |            "position": {
                |              "x": -64.5,
                |              "y": 285
                |            },
                |            "internals": {
                |              "z": 0,
                |              "userNode": {
                |                "id": "DfxYW",
                |                "data": {
                |                  "phone": "+91-7518710532"
                |                },
                |                "type": "has_phone",
                |                "measured": {
                |                  "width": 129,
                |                  "height": 54
                |                },
                |                "position": {
                |                  "x": -115.5,
                |                  "y": 292.5
                |                }
                |              },
                |              "handleBounds": {
                |                "source": [
                |                  {
                |                    "x": 61.40625,
                |                    "y": 51,
                |                    "id": "b",
                |                    "type": "source",
                |                    "width": 6,
                |                    "height": 6,
                |                    "nodeId": "DfxYW",
                |                    "position": "bottom"
                |                  }
                |                ],
                |                "target": [
                |                  {
                |                    "x": 61.40625,
                |                    "y": -3,
                |                    "id": null,
                |                    "type": "target",
                |                    "width": 6,
                |                    "height": 6,
                |                    "nodeId": "DfxYW",
                |                    "position": "top"
                |                  }
                |                ]
                |              },
                |              "positionAbsolute": {
                |                "x": -115.5,
                |                "y": 292.5
                |              }
                |            },
                |            "sourcePosition": "bottom",
                |            "targetPosition": "top"
                |          },
                |          "handleBounds": {
                |            "source": [
                |              {
                |                "x": 61.40614014894195,
                |                "y": 50.99991117284151,
                |                "id": "b",
                |                "type": "source",
                |                "width": 6,
                |                "height": 6,
                |                "nodeId": "DfxYW",
                |                "position": "bottom"
                |              }
                |            ],
                |            "target": [
                |              {
                |                "x": 61.40614014894195,
                |                "y": -2.9999817282913495,
                |                "id": null,
                |                "type": "target",
                |                "width": 6,
                |                "height": 6,
                |                "nodeId": "DfxYW",
                |                "position": "top"
                |              }
                |            ]
                |          },
                |          "positionAbsolute": {
                |            "x": -64.5,
                |            "y": 285
                |          }
                |        },
                |        "sourcePosition": "bottom",
                |        "targetPosition": "top"
                |      },
                |      "handleBounds": {
                |        "source": [
                |          {
                |            "x": 61.40614014894195,
                |            "y": 50.99991117284151,
                |            "id": "b",
                |            "type": "source",
                |            "width": 6,
                |            "height": 6,
                |            "nodeId": "DfxYW",
                |            "position": "bottom"
                |          }
                |        ],
                |        "target": [
                |          {
                |            "x": 61.40614014894195,
                |            "y": -2.9999817282913495,
                |            "id": null,
                |            "type": "target",
                |            "width": 6,
                |            "height": 6,
                |            "nodeId": "DfxYW",
                |            "position": "top"
                |          }
                |        ]
                |      },
                |      "positionAbsolute": {
                |        "x": -64.5,
                |        "y": 285
                |      }
                |    },
                |    "sourcePosition": "bottom",
                |    "targetPosition": "top"
                |  },
                |  {
                |    "id": "${emailVariant1.step_id.toString}",
                |    "data": {
                |      "phone": "+91-7518710532",
                |      "label": "1",
                |      "type": "step"
                |    },
                |    "type": "call_prospect",
                |    "style": {
                |      "opacity": 1
                |    },
                |    "measured": {
                |      "width": 129,
                |      "height": 54
                |    },
                |    "position": {
                |      "x": -154,
                |      "y": 493
                |    },
                |    "internals": {
                |      "z": 0,
                |      "userNode": {
                |        "id": "5fJod",
                |        "data": {
                |          "phone": "+91-7518710532"
                |        },
                |        "type": "call_prospect",
                |        "style": {
                |          "opacity": 1
                |        },
                |        "measured": {
                |          "width": 129,
                |          "height": 54
                |        },
                |        "position": {
                |          "x": 25,
                |          "y": 493
                |        },
                |        "internals": {
                |          "z": 0,
                |          "userNode": {
                |            "id": "5fJod",
                |            "data": {
                |              "phone": "+91-7518710532"
                |            },
                |            "type": "call_prospect",
                |            "measured": {
                |              "width": 129,
                |              "height": 54
                |            },
                |            "position": {
                |              "x": -205,
                |              "y": 500.5
                |            }
                |          },
                |          "handleBounds": {
                |            "source": [
                |              {
                |                "x": 61.40631087329139,
                |                "y": 51.00008928082737,
                |                "id": "b",
                |                "type": "source",
                |                "width": 6,
                |                "height": 6,
                |                "nodeId": "5fJod",
                |                "position": "bottom"
                |              }
                |            ],
                |            "target": [
                |              {
                |                "x": 61.40631087329139,
                |                "y": -2.9999963532501384,
                |                "id": null,
                |                "type": "target",
                |                "width": 6,
                |                "height": 6,
                |                "nodeId": "5fJod",
                |                "position": "top"
                |              }
                |            ]
                |          },
                |          "positionAbsolute": {
                |            "x": -205,
                |            "y": 500.5
                |          }
                |        },
                |        "sourcePosition": "bottom",
                |        "targetPosition": "top"
                |      },
                |      "handleBounds": {
                |        "source": [
                |          {
                |            "x": 61.40625704184457,
                |            "y": 50.99997562360758,
                |            "id": "b",
                |            "type": "source",
                |            "width": 6,
                |            "height": 6,
                |            "nodeId": "5fJod",
                |            "position": "bottom"
                |          }
                |        ],
                |        "target": [
                |          {
                |            "x": 61.40625704184457,
                |            "y": -3.0000008859094778,
                |            "id": null,
                |            "type": "target",
                |            "width": 6,
                |            "height": 6,
                |            "nodeId": "5fJod",
                |            "position": "top"
                |          }
                |        ]
                |      },
                |      "positionAbsolute": {
                |        "x": 25,
                |        "y": 493
                |      }
                |    },
                |    "sourcePosition": "bottom",
                |    "targetPosition": "top"
                |  },
                |  {
                |    "id": "${linkedinVariant1.step_id.toString}",
                |    "data": {
                |      "sender_email_address": "<EMAIL>",
                |      "label": "2",
                |      "type": "step"
                |    },
                |    "type": "send_email",
                |    "style": {
                |      "opacity": 1
                |    },
                |    "measured": {
                |      "width": 102,
                |      "height": 54
                |    },
                |    "position": {
                |      "x": 38.5,
                |      "y": 493
                |    },
                |    "internals": {
                |      "z": 0,
                |      "userNode": {
                |        "id": "Z2qxk",
                |        "data": {
                |          "sender_email_address": "<EMAIL>"
                |        },
                |        "type": "send_email",
                |        "measured": {
                |          "width": 102,
                |          "height": 54
                |        },
                |        "position": {
                |          "x": -205,
                |          "y": 500.5
                |        }
                |      },
                |      "handleBounds": {
                |        "source": [
                |          {
                |            "x": 47.929721757804515,
                |            "y": 51.00001027734136,
                |            "id": "b",
                |            "type": "source",
                |            "width": 6,
                |            "height": 6,
                |            "nodeId": "Z2qxk",
                |            "position": "bottom"
                |          }
                |        ],
                |        "target": [
                |          {
                |            "x": 47.929721757804515,
                |            "y": -2.9999982512645382,
                |            "id": null,
                |            "type": "target",
                |            "width": 6,
                |            "height": 6,
                |            "nodeId": "Z2qxk",
                |            "position": "top"
                |          }
                |        ]
                |      },
                |      "positionAbsolute": {
                |        "x": -205,
                |        "y": 500.5
                |      }
                |    },
                |    "sourcePosition": "bottom",
                |    "targetPosition": "top"
                |  }
                |]
                |""".stripMargin).as[JsValue]

            val edges = Json.parse(
              s"""
                |[
                |  {
                |    "id": "DfxYW-5fJod",
                |    "type": "yesEdge",
                |    "source": "condition_call",
                |    "target": "${emailVariant1.step_id.toString}",
                |    "label": "no"
                |  },
                |  {
                |    "id": "DfxYW-Z2qxk",
                |    "type": "noEdge",
                |    "source": "condition_call",
                |    "target": "${linkedinVariant1.step_id.toString}",
                |    "label": "yes"
                |  }
                |]
                |""".stripMargin).as[JsValue]

            val headNodeId = "condition_call"

            (nodes, edges, headNodeId)

          case 21 =>
            /*
                      email_step
                          |
                      has_bounced
                Yes /           \ No
            linkedin step       call_step
             */

            val node = List(
              Json.obj("id" -> emailVariant0.step_id.toString, "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "1", "type" -> "step"),"type" -> "step"),
              Json.obj("id" -> "bounced", "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "bounced", "type" -> "condition"),"type" -> "step"),
              Json.obj("id" -> linkedinVariant1.step_id.toString, "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "2", "type" -> "step"),"type" -> "step"),
              Json.obj("id" -> callVariant1.step_id.toString, "position" -> Json.obj("x" -> 100, "y" -> 100), "data" -> Json.obj("label" -> "3", "type" -> "step"),"type" -> "step"),
            )

            val edges = List(
              Json.obj("id" -> "xyz", "source" -> emailVariant0.step_id.toString, "target" -> "bounced", "label" -> "no_condition"),
              Json.obj("id" -> "2-3", "source" -> "bounced", "target" -> callVariant1.step_id.toString, "label" -> "no"),
              Json.obj("id" -> "2-4", "source" -> "bounced", "target" -> linkedinVariant1.step_id.toString, "label" -> "yes")
            )

            (Json.toJson(node), Json.toJson(edges), emailVariant0.step_id.toString)

          case 23 =>

            /**
             * In the test setup for linkedin_profile_connected condition:
             * we have two prospects - variant 1 and variant 3
             * variant 3 is connected but 1 is not
             * accordingly the correct steps should be scheduled. 
             *
             *          linkedin_profile_connected
             *    yes /				       no \
             * linkedin_step			      call_step
             */


            (Json.toJson(List(
              Json.obj("id" -> "linkedIn_profile_connected", "position" -> Json.obj("x" -> 0, "y" -> 0), "data" -> Json.obj("label" -> "linkedIn_profile_connected", "type" -> "condition"),"type" -> "step"),
              Json.obj("id" -> linkedinVariant1.step_id.toString, "position" -> Json.obj("x" -> 0, "y" -> 0), "data" -> Json.obj("label" -> linkedinVariant1.step_id.toString, "type" -> "step"),"type" -> "step"),
              Json.obj("id" -> callVariant2.step_id.toString, "position" -> Json.obj("x" -> 0, "y" -> 0), "data" -> Json.obj("label" -> callVariant2.step_id.toString, "type" -> "step"),"type" -> "step"),
            )), Json.toJson(List(
              Json.obj("id" -> "xyz", "source" -> "linkedIn_profile_connected", "target" -> linkedinVariant1.step_id.toString, "label" -> "yes"),
              Json.obj("id" -> "xyz", "source" -> "linkedIn_profile_connected", "target" -> callVariant2.step_id.toString, "label" -> "no"),
            )),
              "linkedIn_profile_connected"
            )


        }


        DB autoCommit {implicit session =>
          sql"""
               UPDATE campaigns
               SET
                  campaign_type = ${CampaignType.Drip.toString},
                  drip_campaign_nodes = ${nodes.toString}::jsonb,
                  drip_campaign_edges = ${edges.toString}::jsonb,
                  head_node_id_for_drip = ${headNodeId}
               WHERE
                  id = ${campaignWithStatsAndEmail.id}
               AND team_id = $teamId
               ;
               """
            .update
            .apply()
        }
      }

      campaign: Campaign <- campaignService.findCampaignForCampaignUtilsOnly(
        id = campaignAfterUpdatingEmailSetting.id,
        teamId = TeamId(teamId)
      ) match {
        case None => Future.failed(new Exception("Campaign not found"))
        case Some(c) => Future.successful(c)
      }

      startCampaign <- campaignStartService.startStopCampaign(
          status = CampaignStatus.RUNNING,
          schedule_start_at = None,
          time_zone = None,
          campaign = campaign,
          org = organizationWithCurrentData,
          team = Some(teamAccount),
          userId = AccountId(accountId),
          teamId = TeamId(teamId),
          Logger = logger
        )
        .flatMap {
          case Left(err) =>
            Future.failed(new Exception(s"Error while starting campaign :: $err"))

          case Right(value) =>
            Future.successful(value)
        }
    } yield {
      StartedCampaignDetails(
        campaignId = CampaignId(startCampaign.id),
        prospectIds = createdProspects.created_ids.map(ProspectId(_)),
        stepIds = Seq(emailVariant1.step_id, linkedinVariant1.step_id, emailVariant2.step_id)
      )
    }
  }

  def getDefaultStepData(
    stepType: CampaignStepType,
    subject: String,
    body: String,
    move_to_another_campaign_id: Option[CampaignId] = None,
    shouldHavePrevSubMergeTag: Boolean,
  ): CampaignStepData = {

    stepType match {

      case CampaignStepType.AutoEmailStep =>

        val subjectWithMergeTag = if (shouldHavePrevSubMergeTag) {
          s"$subject {{previous_subject}}"
        } else {
          subject
        }

        CampaignStepData.AutoEmailStep(
          subject = subjectWithMergeTag,
          body = body,
        )

      case CampaignStepType.ManualEmailStep =>

        val subjectWithMergeTag = if (shouldHavePrevSubMergeTag) {
          s"$subject {{previous_subject}}"
        } else {
          subject
        }

        CampaignStepData.ManualEmailStep(
          subject = subjectWithMergeTag,
          body = body,
        )

      case CampaignStepType.LinkedinConnectionRequest =>

        CampaignStepData.LinkedinConnectionRequestData(
          body = Some(body),
        )

      case CampaignStepType.LinkedinMessage =>

        CampaignStepData.LinkedinMessageData(
          body = body,
        )

      case CampaignStepType.LinkedinInmail =>

        CampaignStepData.LinkedinInmailData(
          subject = Some(subject),
          body = body,
        )

      case CampaignStepType.LinkedinViewProfile =>

        CampaignStepData.LinkedinViewProfile()

      case CampaignStepType.AutoLinkedinConnectionRequest =>

        CampaignStepData.AutoLinkedinConnectionRequest(
          body = Some(body),
        )

      case CampaignStepType.AutoLinkedinMessage =>

        CampaignStepData.AutoLinkedinMessage(
          body = body,
        )

      case CampaignStepType.AutoLinkedinInmail =>

        CampaignStepData.AutoLinkedinInmail(
          subject = subject,
          body = body,
        )

      case CampaignStepType.AutoLinkedinViewProfile =>

        CampaignStepData.AutoLinkedinViewProfile()

      case CampaignStepType.GeneralTask =>

        CampaignStepData.GeneralTaskData()

      case CampaignStepType.AutoEmailMagicContent =>

        CampaignStepData.AutoEmailMagicContentStep(
          step_context = StepContext(
            call_to_action = "book a 15 mins call",
            step_details =  "Want to tell the person how we helped one of their compititor to reduce 15% electricity costs",
            columns_to_use = List("first_name")
          ))

      case CampaignStepType.ManualEmailMagicContent =>

        CampaignStepData.ManualEmailMagicContentStep(
          step_context = StepContext(
            call_to_action = "book a 15 mins call",
            step_details =  "Want to tell the person how we helped one of their compititor to reduce 15% electricity costs",
            columns_to_use = List("first_name")
          )
        )

      case CampaignStepType.WhatsappMessage =>

        CampaignStepData.WhatsappMessageData(
          body = body,
        )

      case CampaignStepType.SmsMessage =>

        CampaignStepData.SmsMessageData(
          body = body,
        )

      case CampaignStepType.CallStep =>

        CampaignStepData.CallTaskData(
          body = body,
        )

      case CampaignStepType.MoveToAnotherCampaignStep =>

        CampaignStepData.MoveToAnotherCampaignStepData(
          move_to_another_campaign_id = move_to_another_campaign_id.get
        )

    }

  }


  def createAndStartWhatsappCampaign(
                                       initialData: InitialData,
                                       schedule_from_time: Int = 0,
                                       whatsappBody: String = "Test",
                                       whatsAppSetting: WhatsappAccountSettings
                                     )(using Logger: SRLogger): Future[CreateAndStartCampaignData] = {
    val account: Account = initialData.account

    val orgId: OrgId = OrgId(account.org.id)
    val accountId: AccountId = AccountId(account.internal_id)
    val teamId: TeamId = TeamId(account.teams.head.team_id)
    val taId: Long = account.teams.head.access_members.head.ta_id
    val customNotCategorizedAndDoNotContactIds: CustomNotCategorizedAndDoNotContactIds = findCategorizedAndDoNotContactCustomIds(
      account.teams.head.prospect_categories_custom
    )

    for {
      // create a campaign
      createCampaign: CampaignWithStatsAndEmail <- createNewCampaignChannelSpecific(
        orgId = orgId,
        accountId = accountId,
        teamId = teamId,
        taId = taId,
        campaignSettings = defaultCampaignSettingsForWhatsapp(
          whatsappSettingUuid = WhatsappSettingUuid(whatsAppSetting.uuid),
          teamId = teamId,
          daily_from_time = schedule_from_time,
          daily_till_time = 86399,
          whatsappSettingSenderDetails = Some(List(
            WhatsappSettingSenderDetails(
              channel_setting_uuid = ChannelSettingUuid(whatsAppSetting.uuid),
              team_id = teamId,
              phone_number = whatsAppSetting.whatsapp_number,
              first_name = whatsAppSetting.first_name,
              last_name = whatsAppSetting.last_name,
            )
          ))

      ).get,
        ownerFirstName = account.first_name.get
      )

      // add prospect to campaign
      addProspect: Seq[ProspectObject] <- {

        ProspectFixtureForIntegrationTest.createUpdateOrAssignProspectFuture(
          campaignId = Some(CampaignId(createCampaign.id)),
          accountId = accountId,
          teamId = teamId,
          account = account,
          givenProspect = Some(initialData.prospectsResult.map(p => {
            ProspectCreateFormData(
              email = p.email,
              first_name = p.first_name,
              last_name = p.last_name,
              custom_fields = p.custom_fields,
              owner_id = Some(p.owner_id),

              list = p.list,
              company = p.company,
              city = p.city,
              country = p.country,
              timezone = p.timezone,
              created_at = None,

              state = p.state,
              job_title = p.job_title,
              phone = p.phone,
              phone_2 = None,
              phone_3 = None,
              linkedin_url = p.linkedin_url
            )
          }))
        )
      }
      isCampaignChannelSettingsExists: IsUpdate <- campaignDAO.isCampaignChannelSettingsExists(
        team_id = TeamId(createCampaign.team_id),
        campaign_id = CampaignId(createCampaign.id),
        channel_type = ChannelType.WhatsappChannel
      )

      _ <- campaignDAO.updateOrInsertChannelSettingInCampaign(
        team_id = TeamId(createCampaign.team_id),
        campaign_id = CampaignId(createCampaign.id),
        channelType = ChannelType.WhatsappChannel,
        channel_settings_uuid = ChannelSettingUuid(whatsAppSetting.uuid),
        isUpdate = isCampaignChannelSettingsExists
      )

      //add auto email step to campaign
      addStep: CampaignStepVariant <- {
        CreateStepForCampaignFixture.createWhatsappStepForCampaign(
          orgId = orgId,
          teamId = teamId,
          accountId = accountId,
          taId = taId,
          campaignId = CampaignId(createCampaign.id),
          whatsappBody = whatsappBody
        )
      }

      //campaign details
      campaign: Campaign <- Future.successful(
        campaignDAO.findCampaignForCampaignUtilsOnly(
          id = createCampaign.id,
          teamId = teamId
        ).get
      )

      //start campaign
      startCampaign: CampaignWithStatsAndEmail <- {
        StartCampaignFixture.startCampaignChannelSpecific(
          accountId = accountId,
          teamId = teamId,
          campaignWithStatsAndEmail = createCampaign,
          orgId = orgId,
          taId = taId,
          emailSetting = None,
          prospect_categories_custom_not_categorized = customNotCategorizedAndDoNotContactIds.not_categorized,
          prospect_categories_custom_do_not_contact = customNotCategorizedAndDoNotContactIds.do_not_contact,
          current_sending_email_accounts = 0
        )
      }
    } yield {
      CreateAndStartCampaignData(
        createCampaign = createCampaign,
        addProspect = addProspect,
        addStep = addStep,
        campaign = campaign,
        startCampaign = startCampaign
      )
    }
  }

  def updateCampaignFirstStep(
                               campaignId: CampaignId,
                               emailSettingId: EmailSettingId,
                               teamId: TeamId,
                               sent_ago_in_hours: Int = 48,
                               mark_scheduler_status_done: Boolean = true,
                               prospects: Seq[Long] = Seq()
                             ): Try[Boolean] = {

    for {
      update_email_scheduled <- SchedulerTestDAO.updateEmailScheduledToMakeSentSteps(
        campaignId = campaignId,
        sent_ago_in_hours = sent_ago_in_hours,
        prospects = prospects
      )
      update_email_settings <- SchedulerTestDAO.updateEmailSettingToMakeSentSteps(
        emailSettingId = emailSettingId
      )
      update_channel_setting <- SchedulerTestDAO.updateCampaignChannelSettingsToMakeSentSteps(
        campaignId = campaignId,
        sent_ago_in_hours = sent_ago_in_hours
      )
      update_email_channel_setting <- SchedulerTestDAO.updateCampaignEmailSettingsToMakeSentSteps(
        campaignId = campaignId,
        sent_ago_in_hours = sent_ago_in_hours
      )
      _: Int <- SchedulerTestDAO.addLastScheduledAtForCampaign(
          campaignId = campaignId,
          teamId = teamId,
        sent_ago_in_hours = sent_ago_in_hours
        )
      update_campaign_prospects <- SchedulerTestDAO.updateCampaignProspectsToMakeSentSteps(
        campaignId = campaignId,
        sent_ago_in_hours = sent_ago_in_hours,
        mark_scheduled_status_done = mark_scheduler_status_done,
        prospects = prospects
      )
    } yield true

  }
}
