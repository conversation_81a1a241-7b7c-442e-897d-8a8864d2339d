package db_test_spec.api.campaigns

import api.accounts.models.{AccountId, OrgId}
import api.accounts.{Account, TeamId}
import api.campaigns.CampaignStepWithChildren
import api.campaigns.models.{CampaignStepType, CurrentStepStatusForScheduler}
import api.campaigns.services.CampaignId
import api.emails.EmailSetting
import api.emails.models.DeletionReason
import api.prospects.*
import api.prospects.models.{ProspectId, StepId}
import api.tasks.models.{RevertData, TaskStatusType}
import app.db_test.{CustomNotCategorizedAndDoNotContactIds, SchedulerTestInput}
import db_test_spec.api.accounts.fixtures.NewAccountAndEmailSettingData
import db_test_spec.api.campaigns.fixtures.CreateNewCampaignFixture.findCategorizedAndDoNotContactCustomIds
import db_test_spec.api.campaigns.test_utils.CampaignUtils
import db_test_spec.api.{AppSpecFixture, DbTestingBeforeAllAndAfterAll, InitialData, InputForInitializingCampaignCreateData}
import org.joda.time.DateTime
import scalikejdbc.*
import sr_scheduler.models.ChannelData.EmailChannelData
import sr_scheduler.models.ChannelType
import utils.helpers.LogHelpers
import utils.mq.channel_scheduler.channels.ScheduleTasksData
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService

import scala.concurrent.Future
import scala.util.{Failure, Random, Success, Try}


class CampaignProspectDAOSpec extends DbTestingBeforeAllAndAfterAll {

  lazy val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get


  def updateStepScheduledAt(
    team_id: TeamId,
    campaignId: CampaignId,
    prospectIds: Seq[ProspectId],
    step_details_that_is_completed: CampaignStepWithChildren,
    last_scheduled: DateTime,
  ): Try[Int] = Try {

    DB autoCommit { implicit session =>
      sql"""
           UPDATE campaigns_prospects
           SET
              current_step_status_for_scheduler = ${CurrentStepStatusForScheduler.Done.toKey},
              sent = true,
              sent_at = now(),
              current_step_id = ${step_details_that_is_completed.id},
              current_step_type = ${step_details_that_is_completed.step_type.toKey},
              last_scheduled = $last_scheduled,
              current_step_channel_type = ${step_details_that_is_completed.step_type.channelType.toString}
           WHERE
           team_id = ${team_id.id}
           and  campaign_id = ${campaignId.id}
           AND prospect_id in (${prospectIds.map(_.id)})
           """
        .update
        .apply()

    }

  }

  describe("findAndMarkSpecificProspectsAsCompleted") {

    it("should successfully mark eligible prospects as completed") {

      val initialData1: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val teamId = TeamId(id = initialData1.account.teams.head.team_id)

      CampaignUtils.createAndStartAutoEmailCampaign(
        initialData = initialData1,
        generateProspectCountIfNoGivenProspect = 2,
      ).map { campaignData =>

        val steps = campaignDAOService.findOrderedSteps(
          campaignId = campaignData.addStep.campaign_id,
          teamId = teamId,
        )

        val markCompletedAfterDays = 3

        updateStepScheduledAt(
          team_id = teamId,
          campaignId = CampaignId(id = campaignData.addStep.campaign_id),
          prospectIds = campaignData.addProspect.map(p => ProspectId(id = p.id)),
          step_details_that_is_completed = steps.find(s => s.id == campaignData.addStep.step_id).get,
          last_scheduled = DateTime.now().minusDays(markCompletedAfterDays),
        ).get

        campaignProspectDAO.findAndMarkSpecificProspectsAsCompleted(
          lastStepIds = List(campaignData.addStep.step_id),
          teamId = teamId,
          markCompletedAfterDays = markCompletedAfterDays,
          campaignId = CampaignId(id = campaignData.addStep.campaign_id),
          prospectIds = campaignData.addProspect.map(p => p.id).toList,
        ) match {

          case Failure(exception) =>

            println(s"Failed to mark prospects as completed. ${LogHelpers.getStackTraceAsString(exception)}")

            assert(false)

          case Success(completedProspects) =>

            val allProspectsMarkedCompleted = completedProspects.forall(p => campaignData.addProspect.map(_.id).contains(p.prospectId))

            println(s"addProspect: ${campaignData.addProspect}")
            println(s"completedProspects: $completedProspects")

            assert(
              campaignData.addProspect.length == completedProspects.length &&
                allProspectsMarkedCompleted
            )

        }

      }.recover { err =>

        println(s"Failed to create and start campaign. ${LogHelpers.getStackTraceAsString(err)}")

        assert(false)

      }

    }

    it("should return empty when lastStepIds is empty") {

      val initialData1: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val teamId = TeamId(id = initialData1.account.teams.head.team_id)

      CampaignUtils.createAndStartAutoEmailCampaign(
        initialData = initialData1,
        generateProspectCountIfNoGivenProspect = 1,
      ).map { campaignData =>

        campaignProspectDAO.findAndMarkSpecificProspectsAsCompleted(
          lastStepIds = List(),
          teamId = teamId,
          markCompletedAfterDays = 30,
          campaignId = CampaignId(id = campaignData.addStep.id),
          prospectIds = campaignData.addProspect.map(_.id).toList,
        ) match {

          case Failure(exception) =>

            println(s"Unexpected failure. ${LogHelpers.getStackTraceAsString(exception)}")

            assert(false)

          case Success(completedProspects) =>

            assert(completedProspects.isEmpty)

        }

      }.recover { err =>

        println(s"Failed to create and start campaign. ${LogHelpers.getStackTraceAsString(err)}")

        assert(false)

      }

    }

    it("should return empty when prospectIds is empty") {

      val initialData1: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val teamId = TeamId(id = initialData1.account.teams.head.team_id)

      CampaignUtils.createAndStartAutoEmailCampaign(
        initialData = initialData1,
        generateProspectCountIfNoGivenProspect = 1,
      ).map { campaignData =>

        campaignProspectDAO.findAndMarkSpecificProspectsAsCompleted(
          lastStepIds = List(campaignData.addStep.step_id),
          teamId = teamId,
          markCompletedAfterDays = 30,
          campaignId = CampaignId(id = campaignData.addStep.id),
          prospectIds = Nil,
        ) match {

          case Failure(exception) =>

            println(s"Unexpected failure. ${LogHelpers.getStackTraceAsString(exception)}")

            assert(false)

          case Success(completedProspects) =>

            assert(completedProspects.isEmpty)

        }

      }.recover { err =>

        println(s"Failed to create and start campaign. ${LogHelpers.getStackTraceAsString(err)}")

        assert(false)

      }

    }

  }

  describe("findProspectsWhichWereSetToResumeLater") {
    it("should give success") {
      val result = campaignProspectDAO.findProspectsWhichWereSetToResumeLater(
        logger = Logger
      )

      assert(result.isSuccess)
    }
  }

  describe("campaignProspectMultichannelRevert") {
    it("should give success") {

      val DbandSession = dbUtils.startLocalTx()
      implicit val session: DBSession = DbandSession.session

      //      given logger: SRLogger = AppSpecFixture.logger
      
      val categorizedAndDoNotContactCustomIds: CustomNotCategorizedAndDoNotContactIds = findCategorizedAndDoNotContactCustomIds(prospect_categories = initialData.account.teams.head.prospect_categories_custom)
      val input: InputForInitializingCampaignCreateData = SchedulerTestInput.getInputForSchedulerIntegrationTest(
        sharedData = initialData,
        categorizedAndDoNotContactCustomIds = categorizedAndDoNotContactCustomIds,
        prospect_emails = Seq(Random.alphanumeric.take(10).mkString("") + "@gmail.com"),
        linkedSettingId = None
      )
      val initializeCreateAndStartCampaignData: InitializedCreateAndStartCampaignData = AppSpecFixture.initializeCampaignDataForIntegrationTest(
        inputForInitializingCampaignCreateData = input
      )

      val scheduleTaskData = for {
        campaignCreationData: CampaignCreationData <- CampaignCreationFixtureForIntegrationTest.createAndStartCampaign(
          inputForInitializingCampaignCreateData = input,
          initializedData = initializeCreateAndStartCampaignData
        )

        result: ScheduleTasksData <- emailChannelScheduler.scheduleTasksForChannel(
          channelData = EmailChannelData(
            emailSettingId = initialData.emailSetting.get.id.get.emailSettingId
          ),
          teamId = initialData.head_team_id,
          accountService = accountService,
          //accountDAO = accountDAO,
          emailNotificationService = emailNotificationService,
          campaignService = campaignService,
          campaignProspectDAO = campaignProspectDAO,
          campaignProspectService = campaignProspectService,
          campaignStepVariantDAO = campaignStepVariantDAO,
          campaignStepDAO = campaignStepDAO,
          srShuffleUtils = srShuffleUtils,
          emailServiceCompanion = emailServiceCompanion,
          templateService = templateService,
          taskDAO = taskDAO,
          taskService = taskService,
          campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO,
          campaignsMissingMergeTagService = campaignsMissingMergeTagService,
          srRedisSimpleLockServiceV2 = srRedisSimpleLockServiceV2,
          mqWebhookCompleted = mqWebhookCompleted,
          calendarAppService = calendarAppService,
          accountOrgBillingRelatedService = accountOrgBillingRelatedService,
          srRollingUpdateCoreService = srRollingUpdateCoreService
        )

        update_campaign_prospect_status: Int <- Future.fromTry {
          campaignProspectDAO.campaignProspectMultichannelRevert(
            campaign_id = CampaignId(id = campaignCreationData.campaign.id),
            prospectId = ProspectId(id = campaignCreationData.prospects.created_ids.head),
            current_step_id = StepId(id = campaignCreationData.campaignStepVariant.step_id),
            revert_data = RevertData.TaskRevertData(
              task_due_at = DateTime.now(),
              task_id = "Task_id_dummy",
              status_type = TaskStatusType.Done,
              skipped_at = None,
              done_at = Some(DateTime.now())
            ),
            reverted_by = "campaignProspectMultichannelRevert",
            team_id = TeamId(id = campaignCreationData.campaign.team_id),
            previous_task_step_type = CampaignStepType.AutoEmailStep,
            previous_task_channel_type = ChannelType.EmailChannel,
            deletion_reason = DeletionReason.Other("campaignProspectMultichannelRevert"))
        }

      } yield {

        update_campaign_prospect_status

      }


      scheduleTaskData.map(

          res => {
            println("success path")

            dbUtils.commitAndCloseSession(DbandSession.db)
            assert(res == 1)


          }
        )
        .recover { case err => {
          print(err)

          dbUtils.commitAndCloseSession(DbandSession.db)
          assert(false)


        }
        }


    }

    it("should give success while revertCampaignProspectAfterHeadStepDeletion") {

      val DbandSession = dbUtils.startLocalTx()
      implicit val session: DBSession = DbandSession.session

      //      given logger: SRLogger = AppSpecFixture.logger
      
      val categorizedAndDoNotContactCustomIds: CustomNotCategorizedAndDoNotContactIds = findCategorizedAndDoNotContactCustomIds(prospect_categories = initialData.account.teams.head.prospect_categories_custom)
      val input: InputForInitializingCampaignCreateData = SchedulerTestInput.getInputForSchedulerIntegrationTest(
        sharedData = initialData,
        categorizedAndDoNotContactCustomIds = categorizedAndDoNotContactCustomIds,
        prospect_emails = Seq(Random.alphanumeric.take(10).mkString("") + "@gmail.com"),
        linkedSettingId = None
      )
      val initializeCreateAndStartCampaignData: InitializedCreateAndStartCampaignData = AppSpecFixture.initializeCampaignDataForIntegrationTest(
        inputForInitializingCampaignCreateData = input
      )

      val scheduleTaskData = for {
        campaignCreationData: CampaignCreationData <- CampaignCreationFixtureForIntegrationTest.createAndStartCampaign(
          inputForInitializingCampaignCreateData = input,
          initializedData = initializeCreateAndStartCampaignData
        )

        result: ScheduleTasksData <- emailChannelScheduler.scheduleTasksForChannel(
          channelData = EmailChannelData(
            emailSettingId = initialData.emailSetting.get.id.get.emailSettingId
          ),
          teamId = initialData.head_team_id,
          accountService = accountService,
          //accountDAO = accountDAO,
          emailNotificationService = emailNotificationService,
          campaignService = campaignService,
          campaignProspectDAO = campaignProspectDAO,
          campaignProspectService = campaignProspectService,
          campaignStepVariantDAO = campaignStepVariantDAO,
          campaignStepDAO = campaignStepDAO,
          srShuffleUtils = srShuffleUtils,
          emailServiceCompanion = emailServiceCompanion,
          templateService = templateService,
          taskDAO = taskDAO,
          taskService = taskService,
          campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO,
          campaignsMissingMergeTagService = campaignsMissingMergeTagService,
          srRedisSimpleLockServiceV2 = srRedisSimpleLockServiceV2,
          mqWebhookCompleted = mqWebhookCompleted,
          calendarAppService = calendarAppService,
          accountOrgBillingRelatedService = accountOrgBillingRelatedService,
          srRollingUpdateCoreService = srRollingUpdateCoreService
        )

        get_scheduled_email_data: Option[RevertData.EmailScheduledRevertData] <- Future.fromTry {

          emailScheduledDAO.getEmailDataForProspectRevert(
            campaign_id = CampaignId(id = campaignCreationData.campaign.id),
            prospect_id = ProspectId(id = campaignCreationData.prospects.created_ids.head),
            step_id = StepId(id = campaignCreationData.campaignStepVariant.step_id),
          )


        }

        update_campaign_prospect_status: Int <- Future.fromTry {

          get_scheduled_email_data match {

            case None =>

              Failure(new Exception("err emails_scheduled not found for prospect"))


            case Some(value) =>

              campaignProspectDAO.revertCampaignProspectAfterHeadStepDeletion(
                campaign_id = CampaignId(id = campaignCreationData.campaign.id),
                prospect_id = ProspectId(id = campaignCreationData.prospects.created_ids.head),
                reverted_by = "campaignProspectMultichannelRevert",
                team_id = TeamId(id = campaignCreationData.campaign.team_id),
                revert_reason = DeletionReason.Other("campaignProspectMultichannelRevert")
              )

          }

        }

      } yield {

        update_campaign_prospect_status

      }


      scheduleTaskData.map(

          res => {
            println("success path")

            dbUtils.commitAndCloseSession(DbandSession.db)
            assert(res == 1)

          }
        )
        .recover { case err => {
          print(err)

          dbUtils.commitAndCloseSession(DbandSession.db)
          assert(false)


        }
        }

    }

    it("should give success while EmailScheduledRevertData") {

      val DbandSession = dbUtils.startLocalTx()
      implicit val session: DBSession = DbandSession.session

      //      given logger: SRLogger = AppSpecFixture.logger
      
      val categorizedAndDoNotContactCustomIds: CustomNotCategorizedAndDoNotContactIds = findCategorizedAndDoNotContactCustomIds(prospect_categories = initialData.account.teams.head.prospect_categories_custom)
      val input: InputForInitializingCampaignCreateData = SchedulerTestInput.getInputForSchedulerIntegrationTest(
        sharedData = initialData,
        categorizedAndDoNotContactCustomIds = categorizedAndDoNotContactCustomIds,
        prospect_emails = Seq(Random.alphanumeric.take(10).mkString("") + "@gmail.com"),
        linkedSettingId = None
      )
      val initializeCreateAndStartCampaignData: InitializedCreateAndStartCampaignData = AppSpecFixture.initializeCampaignDataForIntegrationTest(
        inputForInitializingCampaignCreateData = input
      )

      val scheduleTaskData = for {
        campaignCreationData: CampaignCreationData <- CampaignCreationFixtureForIntegrationTest.createAndStartCampaign(
          inputForInitializingCampaignCreateData = input,
          initializedData = initializeCreateAndStartCampaignData
        )

        result: ScheduleTasksData <- emailChannelScheduler.scheduleTasksForChannel(
          channelData = EmailChannelData(
            emailSettingId = initialData.emailSetting.get.id.get.emailSettingId
          ),
          teamId = initialData.head_team_id,
          accountService = accountService,
          //accountDAO = accountDAO,
          emailNotificationService = emailNotificationService,
          campaignService = campaignService,
          campaignProspectDAO = campaignProspectDAO,
          campaignProspectService = campaignProspectService,
          campaignStepVariantDAO = campaignStepVariantDAO,
          campaignStepDAO = campaignStepDAO,
          srShuffleUtils = srShuffleUtils,
          emailServiceCompanion = emailServiceCompanion,
          templateService = templateService,
          taskDAO = taskDAO,
          taskService = taskService,
          campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO,
          campaignsMissingMergeTagService = campaignsMissingMergeTagService,
          srRedisSimpleLockServiceV2 = srRedisSimpleLockServiceV2,
          mqWebhookCompleted = mqWebhookCompleted,
          calendarAppService = calendarAppService,
          accountOrgBillingRelatedService = accountOrgBillingRelatedService,
          srRollingUpdateCoreService = srRollingUpdateCoreService
        )

        get_scheduled_email_data: Option[RevertData.EmailScheduledRevertData] <- Future.fromTry {

          emailScheduledDAO.getEmailDataForProspectRevert(
            campaign_id = CampaignId(id = campaignCreationData.campaign.id),
            prospect_id = ProspectId(id = campaignCreationData.prospects.created_ids.head),
            step_id = StepId(id = campaignCreationData.campaignStepVariant.step_id),
          )


        }

        update_campaign_prospect_status: Int <- Future.fromTry {

          get_scheduled_email_data match {

            case None =>

              Failure(new Exception("err emails_scheduled not found for prospect"))


            case Some(value) =>

              campaignProspectDAO.campaignProspectMultichannelRevert(
                campaign_id = CampaignId(id = campaignCreationData.campaign.id),
                prospectId = ProspectId(id = campaignCreationData.prospects.created_ids.head),
                current_step_id = StepId(id = campaignCreationData.campaignStepVariant.step_id),
                revert_data = value,
                reverted_by = "campaignProspectMultichannelRevert",
                team_id = TeamId(id = campaignCreationData.campaign.team_id),
                previous_task_step_type = CampaignStepType.AutoEmailStep,
                previous_task_channel_type = ChannelType.EmailChannel,
                deletion_reason = DeletionReason.Other("campaignProspectMultichannelRevert")
              )

          }

        }

      } yield {

        update_campaign_prospect_status

      }


      scheduleTaskData.map(

          res => {
            println("success path")

            dbUtils.commitAndCloseSession(DbandSession.db)
            assert(res == 1)

          }
        )
        .recover { case err => {
          print(err)

          dbUtils.commitAndCloseSession(DbandSession.db)
          assert(false)


        }
        }

    }
  }

  describe("testing hasBeenAddedToACampaign") {


    it("should return success - false as prospect not been added to campaign") {
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
      val account: Account = initialData.account
      val emailSetting: EmailSetting = initialData.emailSetting.get
      val orgId: OrgId = OrgId(account.org.id)
      val accountId: AccountId = AccountId(account.internal_id)
      val teamId: TeamId = TeamId(account.teams.head.team_id)
      val taId: Long = account.teams.head.access_members.head.ta_id

      val res = campaignProspectDAO.hasBeenAddedToACampaign(
        prospect_id = ProspectId(id = initialData.prospectsResult.head.id),
        team_id = teamId
      )

      res match {

        case Failure(e) =>

          Logger.error("error while testing hasBeenAddedToACampaign", e)
          assert(false)

        case Success(d) =>

          print(s" testing hasBeenAddedToACampaign ${d} ")
          // as prospect not been added to campaign so it should return false
          assert(!d)


      }

    }

    it("should return success - true as prospect been added to campaign") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
      val account: Account = initialData.account
      val emailSetting: EmailSetting = initialData.emailSetting.get
      val orgId: OrgId = OrgId(account.org.id)
      val accountId: AccountId = AccountId(account.internal_id)
      val teamId: TeamId = TeamId(account.teams.head.team_id)
      val taId: Long = account.teams.head.access_members.head.ta_id
      val categorizedAndDoNotContactCustomIds: CustomNotCategorizedAndDoNotContactIds = findCategorizedAndDoNotContactCustomIds(prospect_categories = initialData.account.teams.head.prospect_categories_custom)
      val input: InputForInitializingCampaignCreateData = SchedulerTestInput.getInputForSchedulerIntegrationTest(
        sharedData = initialData,
        categorizedAndDoNotContactCustomIds = categorizedAndDoNotContactCustomIds,
        prospect_emails = Seq(Random.alphanumeric.take(10).mkString("") + "@gmail.com"),
        linkedSettingId = None
      )
      val initializeCreateAndStartCampaignData: InitializedCreateAndStartCampaignData = AppSpecFixture.initializeCampaignDataForIntegrationTest(
        inputForInitializingCampaignCreateData = input
      )

      val res: Future[Boolean] = for {

        campaignCreationData: CampaignCreationData <- CampaignCreationFixtureForIntegrationTest.createAndStartCampaign(
          inputForInitializingCampaignCreateData = input,
          initializedData = initializeCreateAndStartCampaignData
        )

        has_been_added_to_a_campaign: Boolean <- Future.fromTry(campaignProspectDAO.hasBeenAddedToACampaign(
          prospect_id = ProspectId(id = campaignCreationData.prospects.assigned_ids.head),
          team_id = teamId
        ))


      } yield {
        has_been_added_to_a_campaign
      }


      res.map(r => {

          // r should be true as prospect is added to a campaign
          assert(r)


        })
        .recover(err => {
          println(s"err while :: should return success - true as prospect been added to campaign ::  -> ${err}")
          assert(false)
        })

    }


  }

}
