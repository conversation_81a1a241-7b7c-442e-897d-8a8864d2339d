package db_test_spec.api.campaigns.dao

import api.AppConfig
import api.accounts.EmailScheduledIdOrTaskId.TaskId
import api.accounts.models.AccountId
import api.accounts.{Account, TeamId}
import api.campaigns.UpdateCampaignScheduleSettings
import api.campaigns.models.{CampaignAnalysisFor, CampaignToCheckForSendingLimitNewFlow}
import api.campaigns.services.CampaignId
import api.prospects.models.ProspectId
import api.tasks.models.{ChangeStatusPermissionCheck, NewTask, TaskCreatedVia, TaskData, TaskPriority, TaskStatus, TaskType, UpdateTaskStatus}
import db_test_spec.api.campaigns.test_utils.{CampaignUtils, StartedCampaignDetails}
import db_test_spec.api.{DbTestingBeforeAllAndAfterAll, InitialData, SRSetupAndDeleteFixtures}
import org.joda.time.{DateTime, DateTimeZone}
import org.scalatest.compatible.Assertion
import utils.SRLogger
import utils.cronjobs.sender_volume_alert.model.CampaignSendingVolumeLogs
import utils.helpers.LogHelpers
import utils.mq.MqCampaignSendReportMsg

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}

class DripCampaignHealthCheckDAOSpec extends DbTestingBeforeAllAndAfterAll {

  private def createDoneTask(
    accountId: AccountId,
    teamId: TeamId,
    runningDripCampDetails: StartedCampaignDetails,
    prospectId: ProspectId,
    taskDueAt: DateTime,
    taskDoneAt: DateTime,
  )(
    implicit ec: ExecutionContext,
    Logger: SRLogger
  ): Future[TaskId] = {

    for {

      dueTaskId: TaskId <- taskService.createTask(
        accountId = accountId.id,
        teamId = teamId.id,
        task_data = NewTask(
          campaign_id = Some(runningDripCampDetails.campaignId.id),
          campaign_name = None,
          step_id = None,
          step_label = None,
          created_via = TaskCreatedVia.Scheduler,
          is_opening_step = Some(true),
          task_type = TaskType.GeneralTask,
          is_auto_task = true,
          task_data = TaskData.GeneralTaskData(
            task_notes = "Some notes",
          ),
          status = TaskStatus.Due(
            due_at = taskDueAt,
          ),
          assignee_id = Some(accountId.id),
          prospect_id = Some(prospectId.id),
          priority = TaskPriority.High,
          emailsScheduledUuid = None,
          note = None,
        )
      ).flatMap {

        case Left(err) =>

          println(s"Error while createTask :: $err")

          Future.failed(new Exception(s"Error while creating task :: $err"))

        case Right(task_id) =>

          Future.successful(TaskId(id = task_id))

      }

      doneTaskId: TaskId <- taskDAO.changeStatus(
        task_id = dueTaskId.id,
        changeTime = taskDoneAt,
        task_status = UpdateTaskStatus.Done(),
        changeStatusPermissionCheck = ChangeStatusPermissionCheck.ManualTaskCheck(
          doer = Some(accountId.id),
          permittedAccountIds = Seq(accountId.id)
        ),
        team_id = teamId.id,
      ).map { doneTaskId =>

        TaskId(id = doneTaskId)

      }

    } yield {

      doneTaskId

    }

  }


  describe("Test fetchDripCampaignForMonitoring") {

    it("should return empty list when no drip campaigns are running for the team") {

      val initialData: InitialData =
        SRSetupAndDeleteFixtures.createInitialData(create_org_calling_credits = true).get

      val teamId = TeamId(id = initialData.account.teams.head.team_id)

      dripCampaignHealthCheckService.fetchDripCampaignForMonitoring match {

        case Failure(exception) =>

          println(s"fetchDripCampaignForMonitoring failed - ${LogHelpers.getStackTraceAsString(exception)}")

          assert(false)

        case Success(dripCampaignForMonitoringList) =>

          assert(
            !dripCampaignForMonitoringList.exists(_.teamId == teamId)
          )

      }

    }

    it("should return a list of drip campaigns when there are some running") {

      val initialData: InitialData =
        SRSetupAndDeleteFixtures.createInitialData(create_org_calling_credits = true).get

      val account: Account = initialData.account

      val accountId: AccountId = AccountId(account.internal_id)

      val teamId: TeamId = TeamId(account.teams.head.team_id)

      CampaignUtils.createAndStartDripCampaign(
        initialData = initialData,
        designDripForCase = 3
      ).map { campaignDetails =>

        dripCampaignHealthCheckService.fetchDripCampaignForMonitoring match {

          case Failure(exception) =>

            println(s"fetchDripCampaignForMonitoring failed - ${LogHelpers.getStackTraceAsString(exception)}")

            assert(false)

          case Success(dripCampaignForMonitoringList) =>

            assert(
              dripCampaignForMonitoringList
                .filter(_.teamId == teamId)
                .map(_.campaignId)
                .contains(campaignDetails.campaignId)
            )

        }


      }.recover { case e =>

        println(s"createAndStartDripCampaign failed - ${LogHelpers.getStackTraceAsString(e)}")

        assert(false)

      }

    }

    it("should return empty list if its less than 15 minutes since the campaign was started for the day") {

      val initialData: InitialData =
        SRSetupAndDeleteFixtures.createInitialData(create_org_calling_credits = true).get

      val account: Account = initialData.account

      val accountId: AccountId = AccountId(account.internal_id)

      val teamId: TeamId = TeamId(account.teams.head.team_id)

      CampaignUtils.createAndStartDripCampaign(
        initialData = initialData,
        designDripForCase = 3
      ).map { campaignDetails =>

        // daily campaign start time = now()
        val daily_from_time = (DateTime.now.getHourOfDay * 3600) + (DateTime.now.getMinuteOfHour * 60)

        campaignDAO.updateScheduleSettings(
          team_id = teamId,
          id = campaignDetails.campaignId.id,
          data = UpdateCampaignScheduleSettings(
            timezone = DateTime.now().getZone.getID,
            daily_from_time = daily_from_time,
            daily_till_time = 86400,
            days_preference = List(true, true, true, true, true, true, true),
          )
        ).get

        dripCampaignHealthCheckService.fetchDripCampaignForMonitoring match {

          case Failure(exception) =>

            println(s"fetchDripCampaignForMonitoring failed - ${LogHelpers.getStackTraceAsString(exception)}")

            assert(false)

          case Success(dripCampaignForMonitoringList) =>

            assert(
              !dripCampaignForMonitoringList.exists(_.teamId == teamId)
            )

        }


      }.recover { case e =>

        println(s"createAndStartDripCampaign failed - ${LogHelpers.getStackTraceAsString(e)}")

        assert(false)

      }

    }

    it("should return list of campaigns which have not been checked and its been 15 minutes since started for the day") {

      val initialData: InitialData =
        SRSetupAndDeleteFixtures.createInitialData(create_org_calling_credits = true).get

      val account: Account = initialData.account

      val accountId: AccountId = AccountId(account.internal_id)

      val teamId: TeamId = TeamId(account.teams.head.team_id)

      CampaignUtils.createAndStartDripCampaign(
        initialData = initialData,
        designDripForCase = 3
      ).map { campaignDetails =>

        val dt = DateTime.now.minusMinutes(15)

        // daily campaign start time = now() - 15 minutes
        val daily_from_time = (dt.getHourOfDay * 3600) + (dt.getMinuteOfHour * 60)

        campaignDAO.updateScheduleSettings(
          team_id = teamId,
          id = campaignDetails.campaignId.id,
          data = UpdateCampaignScheduleSettings(
            timezone = DateTime.now().getZone.getID,
            daily_from_time = daily_from_time,
            daily_till_time = 86400,
            days_preference = List(true, true, true, true, true, true, true),
          )
        ).get

        //        campaignSendingVolumeLogsDAO.addCampaignSendingVolumeLog(
        //          campaignAnalysisFor = CampaignAnalysisFor.DripMonitor,
        //          campaignSendingVolumeLogs = CampaignSendingVolumeLogs(
        //            campaign_id = campaignDetails.campaignId,
        //            team_id = teamId,
        //            date_in_campaign_timezone = DateTime.now().toString,
        //            campaign_timezone = DateTime.now().getZone.getID,
        //            consecutive_delay_in_seconds = 23,
        //            warmup_is_on = false,
        //            warmup_started_at = None,
        //            warmup_starting_email_count = None,
        //            campaign_start_time = DateTime.now(),
        //            pushed_for_checking_at_minutes_since_campaign_start_time = 2234,
        //            actual_minutes_since_campaign_start_time_during_computing = 3,
        //            current_sent_count = 23,
        //            expected_sent_count_till_now = 23,
        //            current_sent_percent = 23,
        //            total_scheduled_for_today_till_now = 43,
        //            possible_issue_if_any = None,
        //            added_at = DateTime.now()
        //          ),
        //        ).get

        dripCampaignHealthCheckService.fetchDripCampaignForMonitoring match {

          case Failure(exception) =>

            println(s"fetchDripCampaignForMonitoring failed - ${LogHelpers.getStackTraceAsString(exception)}")

            assert(false)

          case Success(dripCampaignForMonitoringList) =>

            assert(
              dripCampaignForMonitoringList
                .filter(_.teamId == teamId)
                .map(_.campaignId)
                .contains(campaignDetails.campaignId)
            )

        }


      }.recover { case e =>

        println(s"createAndStartDripCampaign failed - ${LogHelpers.getStackTraceAsString(e)}")

        assert(false)

      }

    }

    it("should return empty list if checked once and its less than 45 minutes since the campaign was started for the day") {

      val initialData: InitialData =
        SRSetupAndDeleteFixtures.createInitialData(create_org_calling_credits = true).get

      val account: Account = initialData.account

      val accountId: AccountId = AccountId(account.internal_id)

      val teamId: TeamId = TeamId(account.teams.head.team_id)

      CampaignUtils.createAndStartDripCampaign(
        initialData = initialData,
        designDripForCase = 3
      ).map { campaignDetails =>

        val dt = DateTime.now.minusMinutes(15)

        // daily campaign start time = now() - 15 minutes
        val daily_from_time = (dt.getHourOfDay * 3600) + (dt.getMinuteOfHour * 60)

        campaignDAO.updateScheduleSettings(
          team_id = teamId,
          id = campaignDetails.campaignId.id,
          data = UpdateCampaignScheduleSettings(
            timezone = DateTime.now().getZone.getID,
            daily_from_time = daily_from_time,
            daily_till_time = 86400,
            days_preference = List(true, true, true, true, true, true, true),
          )
        ).get

        campaignSendingVolumeLogsDAO.addCampaignSendingVolumeLog(
          campaignAnalysisFor = CampaignAnalysisFor.DripMonitor,
          campaignSendingVolumeLogs = CampaignSendingVolumeLogs(
            campaign_id = campaignDetails.campaignId,
            team_id = teamId,
            date_in_campaign_timezone = DateTime.now().toString,
            campaign_timezone = DateTime.now().getZone.getID,
            consecutive_delay_in_seconds = 23,
            warmup_is_on = false,
            warmup_started_at = None,
            warmup_starting_email_count = None,
            campaign_start_time = DateTime.now(),
            pushed_for_checking_at_minutes_since_campaign_start_time = 2234,
            actual_minutes_since_campaign_start_time_during_computing = 3,
            current_sent_count = 23,
            expected_sent_count_till_now = 23,
            current_sent_percent = 23,
            total_scheduled_for_today_till_now = 43,
            possible_issue_if_any = None,
            added_at = DateTime.now()
          ),
        ).get

        dripCampaignHealthCheckService.fetchDripCampaignForMonitoring match {

          case Failure(exception) =>

            println(s"fetchDripCampaignForMonitoring failed - ${LogHelpers.getStackTraceAsString(exception)}")

            assert(false)

          case Success(dripCampaignForMonitoringList) =>

            assert(
              !dripCampaignForMonitoringList.exists(_.teamId == teamId)
            )

        }


      }.recover { case e =>

        println(s"createAndStartDripCampaign failed - ${LogHelpers.getStackTraceAsString(e)}")

        assert(false)

      }

    }

    it("should return list of campaigns which have only been checked once and its been 45 minutes since started for the day") {

      val initialData: InitialData =
        SRSetupAndDeleteFixtures.createInitialData(create_org_calling_credits = true).get

      val account: Account = initialData.account

      val accountId: AccountId = AccountId(account.internal_id)

      val teamId: TeamId = TeamId(account.teams.head.team_id)

      CampaignUtils.createAndStartDripCampaign(
        initialData = initialData,
        designDripForCase = 3
      ).map { campaignDetails =>

        val dt = DateTime.now.minusMinutes(45)

        // daily campaign start time = now() - 15 minutes
        val daily_from_time = (dt.getHourOfDay * 3600) + (dt.getMinuteOfHour * 60)

        campaignDAO.updateScheduleSettings(
          team_id = teamId,
          id = campaignDetails.campaignId.id,
          data = UpdateCampaignScheduleSettings(
            timezone = DateTime.now().getZone.getID,
            daily_from_time = daily_from_time,
            daily_till_time = 86400,
            days_preference = List(true, true, true, true, true, true, true),
          )
        ).get

        campaignSendingVolumeLogsDAO.addCampaignSendingVolumeLog(
          campaignAnalysisFor = CampaignAnalysisFor.DripMonitor,
          campaignSendingVolumeLogs = CampaignSendingVolumeLogs(
            campaign_id = campaignDetails.campaignId,
            team_id = teamId,
            date_in_campaign_timezone = DateTime.now().toString,
            campaign_timezone = DateTime.now().getZone.getID,
            consecutive_delay_in_seconds = 23,
            warmup_is_on = false,
            warmup_started_at = None,
            warmup_starting_email_count = None,
            campaign_start_time = DateTime.now(),
            pushed_for_checking_at_minutes_since_campaign_start_time = 2234,
            actual_minutes_since_campaign_start_time_during_computing = 3,
            current_sent_count = 23,
            expected_sent_count_till_now = 23,
            current_sent_percent = 23,
            total_scheduled_for_today_till_now = 43,
            possible_issue_if_any = None,
            added_at = DateTime.now()
          ),
        ).get

        dripCampaignHealthCheckService.fetchDripCampaignForMonitoring match {

          case Failure(exception) =>

            println(s"fetchDripCampaignForMonitoring failed - ${LogHelpers.getStackTraceAsString(exception)}")

            assert(false)

          case Success(dripCampaignForMonitoringList) =>

//            println(s"\n\n\ndripCampaignForMonitoringList - $dripCampaignForMonitoringList\n\n\n")
            assert(
              dripCampaignForMonitoringList
                .filter(_.teamId == teamId)
                .map(_.campaignId)
                .contains(campaignDetails.campaignId)
            )

        }

      }.recover { case e =>

        println(s"createAndStartDripCampaign failed - ${LogHelpers.getStackTraceAsString(e)}")

        assert(false)

      }

    }

    it("should return list of campaigns which have been checked twice and its been 120 minutes since started for the day") {

      val initialData: InitialData =
        SRSetupAndDeleteFixtures.createInitialData(create_org_calling_credits = true).get

      val account: Account = initialData.account

      val accountId: AccountId = AccountId(account.internal_id)

      val teamId: TeamId = TeamId(account.teams.head.team_id)

      CampaignUtils.createAndStartDripCampaign(
        initialData = initialData,
        designDripForCase = 3
      ).map { campaignDetails =>

        val dt = DateTime.now.minusMinutes(120)

        // daily campaign start time = now() - 15 minutes
        val daily_from_time = (dt.getHourOfDay * 3600) + (dt.getMinuteOfHour * 60)

        campaignDAO.updateScheduleSettings(
          team_id = teamId,
          id = campaignDetails.campaignId.id,
          data = UpdateCampaignScheduleSettings(
            timezone = DateTime.now().getZone.getID,
            daily_from_time = daily_from_time,
            daily_till_time = 86400,
            days_preference = List(true, true, true, true, true, true, true),
          )
        ).get

        campaignSendingVolumeLogsDAO.addCampaignSendingVolumeLog(
          campaignAnalysisFor = CampaignAnalysisFor.DripMonitor,
          campaignSendingVolumeLogs = CampaignSendingVolumeLogs(
            campaign_id = campaignDetails.campaignId,
            team_id = teamId,
            date_in_campaign_timezone = DateTime.now().toString,
            campaign_timezone = DateTime.now().getZone.getID,
            consecutive_delay_in_seconds = 23,
            warmup_is_on = false,
            warmup_started_at = None,
            warmup_starting_email_count = None,
            campaign_start_time = DateTime.now(),
            pushed_for_checking_at_minutes_since_campaign_start_time = 45,
            actual_minutes_since_campaign_start_time_during_computing = 3,
            current_sent_count = 23,
            expected_sent_count_till_now = 23,
            current_sent_percent = 23,
            total_scheduled_for_today_till_now = 43,
            possible_issue_if_any = None,
            added_at = DateTime.now()
          ),
        ).get

        campaignSendingVolumeLogsDAO.addCampaignSendingVolumeLog(
          campaignAnalysisFor = CampaignAnalysisFor.DripMonitor,
          campaignSendingVolumeLogs = CampaignSendingVolumeLogs(
            campaign_id = campaignDetails.campaignId,
            team_id = teamId,
            date_in_campaign_timezone = DateTime.now().toString,
            campaign_timezone = DateTime.now().getZone.getID,
            consecutive_delay_in_seconds = 23,
            warmup_is_on = false,
            warmup_started_at = None,
            warmup_starting_email_count = None,
            campaign_start_time = DateTime.now(),
            pushed_for_checking_at_minutes_since_campaign_start_time = 120,
            actual_minutes_since_campaign_start_time_during_computing = 3,
            current_sent_count = 23,
            expected_sent_count_till_now = 23,
            current_sent_percent = 23,
            total_scheduled_for_today_till_now = 43,
            possible_issue_if_any = None,
            added_at = DateTime.now()
          ),
        ).get

        dripCampaignHealthCheckService.fetchDripCampaignForMonitoring match {

          case Failure(exception) =>

            println(s"fetchDripCampaignForMonitoring failed - ${LogHelpers.getStackTraceAsString(exception)}")

            assert(false)

          case Success(dripCampaignForMonitoringList) =>

//            println(s"\n\n\ndripCampaignForMonitoringList - $dripCampaignForMonitoringList\n\n\n")
            assert(
              dripCampaignForMonitoringList
                .filter(_.teamId == teamId)
                .map(_.campaignId)
                .contains(campaignDetails.campaignId)
            )

        }

      }.recover { case e =>

        println(s"createAndStartDripCampaign failed - ${LogHelpers.getStackTraceAsString(e)}")

        assert(false)

      }

    }

  }

  describe("Test fetchCompletedTaskWithNonCompletedProspects") {

    it("should return empty list when no drip campaigns are running") {

      val invalidCampaignId = CampaignId(id = 2389)

      val invalidTeamId = TeamId(id = 2381)

      dripCampaignHealthCheckDAO.fetchCompletedTaskWithNonCompletedProspects(
        campaignId = invalidCampaignId,
        teamId = invalidTeamId,
      ) match {

        case Failure(exception) =>

          println(
            s"fetchCompletedTaskWithNonCompletedProspects failed - ${LogHelpers.getStackTraceAsString(exception)}"
          )

          assert(false)

        case Success(res) =>

          assert(res.isEmpty)

      }

    }

    it("should return the list of completed tasks with non completed prospects if it has been 2 days since the task was completed") {

      val initialData: InitialData =
        SRSetupAndDeleteFixtures.createInitialData(create_org_calling_credits = true).get

      val account: Account = initialData.account

      val accountId: AccountId = AccountId(account.internal_id)

      val teamId: TeamId = TeamId(account.teams.head.team_id)

      val maxDaysBeforeAlert = AppConfig.maxDaysToMarkProspectCompletedForDoneTaskBeforeAlert

      val f: Future[Assertion] = for {

        runningDripCampDetails: StartedCampaignDetails <- CampaignUtils.createAndStartDripCampaign(
          initialData = initialData,
          designDripForCase = 3
        )

        notCompletedProspectId: ProspectId = runningDripCampDetails.prospectIds.head

        doneTaskId: TaskId <- createDoneTask(
          accountId = accountId,
          teamId = teamId,
          runningDripCampDetails = runningDripCampDetails,
          prospectId = notCompletedProspectId,
          taskDueAt = DateTime.now.plusDays(4),
          // It's been `maxDaysBeforeAlert` days since the task was completed
          taskDoneAt = DateTime.now.minusDays(maxDaysBeforeAlert + 1),
        )

        completedTaskWithNonCompletedProspectList <- Future.fromTry {

          dripCampaignHealthCheckDAO.fetchCompletedTaskWithNonCompletedProspects(
            campaignId = runningDripCampDetails.campaignId,
            teamId = teamId,
          )

        }

      } yield {

        val res = completedTaskWithNonCompletedProspectList.find { t =>

          t.taskId == doneTaskId &&
            t.prospectId == notCompletedProspectId

        }

        assert(
          res.isDefined
        )

      }

      f.recover { case e =>

        println(s"fetchCompletedTaskWithNonCompletedProspects failed - ${LogHelpers.getStackTraceAsString(e)}")

        assert(false)

      }

    }

    it("should return empty list if there are completed task with not completed prospects but they are not more than 2 days old") {

      val initialData: InitialData =
        SRSetupAndDeleteFixtures.createInitialData(create_org_calling_credits = true).get

      val account: Account = initialData.account

      val accountId: AccountId = AccountId(account.internal_id)

      val teamId: TeamId = TeamId(account.teams.head.team_id)

      val f: Future[Assertion] = for {

        runningDripCampDetails: StartedCampaignDetails <- CampaignUtils.createAndStartDripCampaign(
          initialData = initialData,
          designDripForCase = 3
        )

        notCompletedProspectId: ProspectId = runningDripCampDetails.prospectIds.head

        doneTaskId: TaskId <- createDoneTask(
          accountId = accountId,
          teamId = teamId,
          runningDripCampDetails = runningDripCampDetails,
          prospectId = notCompletedProspectId,
          taskDueAt = DateTime.now.plusDays(4),
          // `maxDaysBeforeAlert` days have not past since the task was completed
          taskDoneAt = DateTime.now(),
        )

        completedTaskWithNonCompletedProspectList <- Future.fromTry {

          dripCampaignHealthCheckDAO.fetchCompletedTaskWithNonCompletedProspects(
            campaignId = runningDripCampDetails.campaignId,
            teamId = teamId,
          )

        }

      } yield {

        assert(
          completedTaskWithNonCompletedProspectList.isEmpty
        )

      }

      f.recover { case e =>

        println(s"fetchCompletedTaskWithNonCompletedProspects failed - ${LogHelpers.getStackTraceAsString(e)}")

        assert(false)

      }

    }

  }

}
