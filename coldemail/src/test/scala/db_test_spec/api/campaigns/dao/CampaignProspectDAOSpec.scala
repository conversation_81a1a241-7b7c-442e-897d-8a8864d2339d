package db_test_spec.api.campaigns.dao

import api.accounts.TeamId
import api.campaigns.services.CampaignId
import api.prospects.models.ProspectId
import db_test_spec.api.accounts.fixtures.NewAccountAndEmailSettingData
import db_test_spec.api.{DbTestingBeforeAllAndAfterAll, InitialData}
import db_test_spec.api.campaigns.CampaignCreationFixtureForIntegrationTest
import org.joda.time.DateTime

class CampaignProspectDAOSpec extends DbTestingBeforeAllAndAfterAll {

  lazy val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
  
  describe("getApproxCountOfProspectsThatCanGetFirstEmailToday") {
    it("should give success") {

      val result = CampaignCreationFixtureForIntegrationTest.createDefaultCampaign(initialData = initialData).map{ campaignData =>
        campaignProspectDAO.getApproxCountOfProspectsThatCanGetFirstEmailToday(
          campaignId = CampaignId(campaignData.campaign.id),
          teamId = TeamId(campaignData.campaign.team_id),
          least_amount_of_prospects_needed_to_meet_limit = 100,
          start_of_day = DateTime.now().withTimeAtStartOfDay()
        )
      }

      result.map{rs =>
        assert(rs.isSuccess)
        assert(rs.get == 1)
      }.recover{e =>
        println(s"e --------- $e")
        assert(false)
      }
    }
  }


  describe("updateNextCheckForSchedulerForCampaignProspects") {
    
    it("should give success") {

      val result = CampaignCreationFixtureForIntegrationTest.createDefaultCampaign(initialData = initialData).map { campaignData =>
        campaignProspectDAO_2.updateNextCheckForSchedulerForCampaignProspects(
          teamId = TeamId(campaignData.campaign.team_id),
          prospect_ids = campaignData.prospects.assigned_ids.map(pid => ProspectId(id = pid))
        )
      }

      result.map { rs =>
        println(s"e --------- $rs")
        assert(rs.isSuccess)
        assert(rs.get == 0)
      }.recover { e =>
        println(s"e --------- $e")
        assert(false)
      }
    }
  }

}
