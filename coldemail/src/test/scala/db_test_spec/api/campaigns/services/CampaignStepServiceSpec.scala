package db_test_spec.api.campaigns.services

import api.accounts.TeamId
import api.accounts.models.{AccountId, OrgId}
import api.campaigns.models.CampaignStepData.getSubjectAndBodyFromStepData
import api.campaigns.models.{CampaignStepId, CampaignStepType}
import api.campaigns.services.CampaignId
import db_test_spec.api.accounts.fixtures.NewAccountAndEmailSettingData
import db_test_spec.api.campaigns.test_utils.{CampaignUtils, StepTypeWithPrevSubMergeTag}
import db_test_spec.api.DbTestingBeforeAllAndAfterAll

import scala.concurrent.duration._
import scala.concurrent.{Await, Future}
import scala.util.{Failure, Success}

class CampaignStepServiceSpec extends DbTestingBeforeAllAndAfterAll {

  describe("Test reorderCampaignSteps") {

    it("should return error if trying to reorder a campaign which is not in not_started state.") {

      val initialData =
        NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val f = CampaignUtils.createAndStartAutoEmailCampaign(
        initialData = initialData,
      ).flatMap { c =>

        Future.fromTry {

          campaignStepService.reorderCampaignSteps(
            campaignId = CampaignId(id = c.campaign.id),
            teamId = TeamId(id = c.campaign.team_id),
            stepIdToBeReordered = CampaignStepId(id = 423),
            newParentStepId = CampaignStepId(id = 0)
          )

        }

      }

      Await.ready(f, 5.second).value match {

        case None =>

          assert(false)

        case Some(Failure(exception)) =>

          println(exception)

          assert(false)

        case Some(Success(reorderRes)) =>

          assert(
            reorderRes.isLeft &&
              reorderRes == Left("Cannot reorder campaign steps once the campaign has been started")
          )

      }

    }

    it("should return error if trying to reorder a campaign which has no steps.") {

      val stepsTypes = List()

      val initialData =
        NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val account = initialData.account

      val accountId = AccountId(id = account.internal_id)

      val teamId: TeamId = TeamId(id = account.teams.head.team_id)

      val orgId = OrgId(id = account.org.id)

      val taId = account.teams.head.access_members.find { t =>

        t.user_id == account.internal_id

      }.get.ta_id

      val f = CampaignUtils.createNotStartedCampaignWithSteps(
        orgId = orgId,
        accountId = accountId,
        teamId = teamId,
        taId = taId,
        emailSettingId = initialData.emailSetting.get.id.get,
        stepTypes = stepsTypes,
        ownerFirstName = account.first_name.get
      ).flatMap { c =>

        Future.fromTry {

          campaignStepService.reorderCampaignSteps(
            campaignId = CampaignId(id = c.id),
            teamId = TeamId(id = c.team_id),
            stepIdToBeReordered = CampaignStepId(id = 423),
            newParentStepId = CampaignStepId(id = 0)
          )

        }

      }

      Await.ready(f, 5.second).value match {

        case None =>

          assert(false)

        case Some(Failure(exception)) =>

          println(exception)

          assert(false)

        case Some(Success(reorderRes)) =>

          assert(
            reorderRes.isLeft &&
              reorderRes == Left("No campaign steps found for reorder")
          )

      }

    }

    it("should return error if trying to reorder a campaign which has only 1 step") {

      val numOfCampaignSteps = 1

      val stepsTypes = List.fill(numOfCampaignSteps)(
        StepTypeWithPrevSubMergeTag(
          stepType = CampaignStepType.AutoEmailStep,
          shouldHavePrevSubMergeTag = false
        )
      )

      val initialData =
        NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val account = initialData.account

      val accountId = AccountId(id = account.internal_id)

      val teamId: TeamId = TeamId(id = account.teams.head.team_id)

      val orgId = OrgId(id = account.org.id)

      val taId = account.teams.head.access_members.find { t =>

        t.user_id == account.internal_id

      }.get.ta_id

      val f = CampaignUtils.createNotStartedCampaignWithSteps(
        orgId = orgId,
        accountId = accountId,
        teamId = teamId,
        taId = taId,
        emailSettingId = initialData.emailSetting.get.id.get,
        stepTypes = stepsTypes,
        ownerFirstName = account.first_name.get
      ).flatMap { c =>

        Future.fromTry {

          campaignStepService.reorderCampaignSteps(
            campaignId = CampaignId(id = c.id),
            teamId = TeamId(id = c.team_id),
            stepIdToBeReordered = CampaignStepId(id = 423),
            newParentStepId = CampaignStepId(id = 0)
          )

        }

      }

      Await.ready(f, 5.second).value match {

        case None =>

          assert(false)

        case Some(Failure(exception)) =>

          println(exception)

          assert(false)

        case Some(Success(reorderRes)) =>

          assert(
            reorderRes.isLeft &&
              reorderRes == Left("Not enough campaign steps for reorder")
          )

      }

    }

    it("should return error if the provided stepIdToBeReordered does not belong to campaign steps") {

      val numOfCampaignSteps = 3

      val stepsTypes = List.fill(numOfCampaignSteps)(
        StepTypeWithPrevSubMergeTag(
          stepType = CampaignStepType.AutoEmailStep,
          shouldHavePrevSubMergeTag = false
        )
      )

      val initialData =
        NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val account = initialData.account

      val accountId = AccountId(id = account.internal_id)

      val teamId: TeamId = TeamId(id = account.teams.head.team_id)

      val orgId = OrgId(id = account.org.id)

      val taId = account.teams.head.access_members.find { t =>

        t.user_id == account.internal_id

      }.get.ta_id

      val f = CampaignUtils.createNotStartedCampaignWithSteps(
        orgId = orgId,
        accountId = accountId,
        teamId = teamId,
        taId = taId,
        emailSettingId = initialData.emailSetting.get.id.get,
        stepTypes = stepsTypes,
        ownerFirstName = account.first_name.get
      ).flatMap { c =>

        val steps = campaignStepService.findStepsByCampaign(campaign = c).get

        Future.fromTry {

          campaignStepService.reorderCampaignSteps(
            campaignId = CampaignId(id = c.id),
            teamId = TeamId(id = c.team_id),
            stepIdToBeReordered = CampaignStepId(id = 423),
            newParentStepId = CampaignStepId(id = steps.head.id)
          )

        }

      }

      Await.ready(f, 5.second).value match {

        case None =>

          assert(false)

        case Some(Failure(exception)) =>

          println(exception)

          assert(false)

        case Some(Success(reorderRes)) =>

          assert(
            reorderRes.isLeft &&
              reorderRes == Left("Provided campaign step ids do not belong to the campaign")
          )

      }

    }

    it("should return error if the provided newParentStepId does not belong to campaign steps") {

      val numOfCampaignSteps = 2

      val stepsTypes = List.fill(numOfCampaignSteps)(
        StepTypeWithPrevSubMergeTag(
          stepType = CampaignStepType.AutoEmailStep,
          shouldHavePrevSubMergeTag = false
        )
      )

      val initialData =
        NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val account = initialData.account

      val accountId = AccountId(id = account.internal_id)

      val teamId: TeamId = TeamId(id = account.teams.head.team_id)

      val orgId = OrgId(id = account.org.id)

      val taId = account.teams.head.access_members.find { t =>

        t.user_id == account.internal_id

      }.get.ta_id

      val f = CampaignUtils.createNotStartedCampaignWithSteps(
        orgId = orgId,
        accountId = accountId,
        teamId = teamId,
        taId = taId,
        emailSettingId = initialData.emailSetting.get.id.get,
        stepTypes = stepsTypes,
        ownerFirstName = account.first_name.get
      ).flatMap { c =>

        val steps = campaignStepService.findStepsByCampaign(campaign = c).get

        Future.fromTry {

          campaignStepService.reorderCampaignSteps(
            campaignId = CampaignId(id = c.id),
            teamId = TeamId(id = c.team_id),
            stepIdToBeReordered = CampaignStepId(id = steps.head.id),
            newParentStepId = CampaignStepId(id = 750)
          )

        }

      }

      Await.ready(f, 5.second).value match {

        case None =>

          assert(false)

        case Some(Failure(exception)) =>

          println(exception)

          assert(false)

        case Some(Success(reorderRes)) =>

          assert(
            reorderRes.isLeft &&
              reorderRes == Left("Provided campaign step ids do not belong to the campaign")
          )

      }

    }

    it("should return error if both newParentStepId and stepIdToBeReordered does not belong to campaign steps") {

      val numOfCampaignSteps = 9

      val stepsTypes = List.fill(numOfCampaignSteps)(
        StepTypeWithPrevSubMergeTag(
          stepType = CampaignStepType.AutoEmailStep,
          shouldHavePrevSubMergeTag = false
        )
      )

      val initialData =
        NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val account = initialData.account

      val accountId = AccountId(id = account.internal_id)

      val teamId: TeamId = TeamId(id = account.teams.head.team_id)

      val orgId = OrgId(id = account.org.id)

      val taId = account.teams.head.access_members.find { t =>

        t.user_id == account.internal_id

      }.get.ta_id

      val f = CampaignUtils.createNotStartedCampaignWithSteps(
        orgId = orgId,
        accountId = accountId,
        teamId = teamId,
        taId = taId,
        emailSettingId = initialData.emailSetting.get.id.get,
        stepTypes = stepsTypes,
        ownerFirstName = account.first_name.get
      ).flatMap { c =>

        Future.fromTry {

          campaignStepService.reorderCampaignSteps(
            campaignId = CampaignId(id = c.id),
            teamId = TeamId(id = c.team_id),
            stepIdToBeReordered = CampaignStepId(id = 9021),
            newParentStepId = CampaignStepId(id = 750)
          )

        }

      }

      Await.ready(f, 5.second).value match {

        case None =>

          assert(false)

        case Some(Failure(exception)) =>

          println(exception)

          assert(false)

        case Some(Success(reorderRes)) =>

          assert(
            reorderRes.isLeft &&
              reorderRes == Left("Provided campaign step ids do not belong to the campaign")
          )

      }

    }

    it("should reorder correctly, when head step is changed") {

      val numOfCampaignSteps = 9

      val stepsTypes = List.fill(numOfCampaignSteps)(
        StepTypeWithPrevSubMergeTag(
          stepType = CampaignStepType.AutoEmailStep,
          shouldHavePrevSubMergeTag = false
        )
      )

      val initialData =
        NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val account = initialData.account

      val accountId = AccountId(id = account.internal_id)

      val teamId: TeamId = TeamId(id = account.teams.head.team_id)

      val orgId = OrgId(id = account.org.id)

      val taId = account.teams.head.access_members.find { t =>

        t.user_id == account.internal_id

      }.get.ta_id

      val f = CampaignUtils.createNotStartedCampaignWithSteps(
        orgId = orgId,
        accountId = accountId,
        teamId = teamId,
        taId = taId,
        emailSettingId = initialData.emailSetting.get.id.get,
        stepTypes = stepsTypes,
        ownerFirstName = account.first_name.get
      )

      val campaign = Await.ready(f, 5.second).value.get.get

      val stepsBeforeReorder = campaignStepService.findStepsByCampaign(campaign = campaign).get

      val stepInsertCountRes = campaignStepService.reorderCampaignSteps(
        campaignId = CampaignId(id = campaign.id),
        teamId = TeamId(id = campaign.team_id),
        stepIdToBeReordered = CampaignStepId(id = stepsBeforeReorder.last.id),
        newParentStepId = CampaignStepId(id = 0)
      )

      stepInsertCountRes match {

        case Failure(exception) =>

          println(exception)

          assert(false)

        case Success(reorderRes) =>

          val headStepUpdatedCampaign = campaignDAO.findCampaignForCampaignUtilsOnly(
            id = campaign.id,
            teamId = TeamId(id = campaign.team_id)
          ).get

          val stepsAfterReorder = campaignStepService.findStepsByCampaign(campaign = headStepUpdatedCampaign).get

          val lastStep = stepsBeforeReorder.last

          val expectedOrder = lastStep +: stepsBeforeReorder.dropRight(1)

          assert(
            reorderRes.isRight &&
              stepsAfterReorder.map(_.id) == expectedOrder.map(_.id)
          )

      }

    }

    it("should reorder correctly, when head step is not changed") {

      val numOfCampaignSteps = 6

      val stepsTypes = List.fill(numOfCampaignSteps)(
        StepTypeWithPrevSubMergeTag(
          stepType = CampaignStepType.AutoEmailStep,
          shouldHavePrevSubMergeTag = false
        )
      )

      val initialData =
        NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val account = initialData.account

      val accountId = AccountId(id = account.internal_id)

      val teamId: TeamId = TeamId(id = account.teams.head.team_id)

      val orgId = OrgId(id = account.org.id)

      val taId = account.teams.head.access_members.find { t =>

        t.user_id == account.internal_id

      }.get.ta_id

      val f = CampaignUtils.createNotStartedCampaignWithSteps(
        orgId = orgId,
        accountId = accountId,
        teamId = teamId,
        taId = taId,
        emailSettingId = initialData.emailSetting.get.id.get,
        stepTypes = stepsTypes,
        ownerFirstName = account.first_name.get
      )

      val campaign = Await.ready(f, 5.second).value.get.get

      val stepsBeforeReorder = campaignStepService.findStepsByCampaign(campaign = campaign).get

      val stepIdToBeReordered = CampaignStepId(id = stepsBeforeReorder(3).id)

      val newParentStepId = CampaignStepId(id = stepsBeforeReorder(1).id)

      val stepInsertCountRes = campaignStepService.reorderCampaignSteps(
        campaignId = CampaignId(id = campaign.id),
        teamId = TeamId(id = campaign.team_id),
        stepIdToBeReordered = stepIdToBeReordered,
        newParentStepId = newParentStepId
      )

      stepInsertCountRes match {

        case Failure(exception) =>

          println(exception)

          assert(false)

        case Success(reorderRes) =>

          val reorderedStep = stepsBeforeReorder.find(_.id == stepIdToBeReordered.id)

          val stepsBeforeReorderFiltered = stepsBeforeReorder.filter(_.id != stepIdToBeReordered.id)

          val splitPos = stepsBeforeReorder.indexWhere(_.id == newParentStepId.id) + 1

          val (start, end) = stepsBeforeReorderFiltered.splitAt(splitPos)

          val res = start ++ reorderedStep.toList ++ end

          val headStepUpdatedCampaign = campaignDAO.findCampaignForCampaignUtilsOnly(
            id = campaign.id,
            teamId = TeamId(id = campaign.team_id)
          ).get

          val stepsAfterReorder = campaignStepService.findStepsByCampaign(campaign = headStepUpdatedCampaign).get

          assert(
            reorderRes.isRight &&
              stepsAfterReorder.map(_.id) == res.map(_.id)
          )

      }

    }

    it(
      "reorder 4th step which as {{previous_subject}} merge tag to head step - should fail if steps have more than 1 variants"
    ) {

      val numOfCampaignSteps = 6

      val stepNumberWithPrevSubMergeTag = 4

      val stepsTypes = List.fill(stepNumberWithPrevSubMergeTag - 1)(
        StepTypeWithPrevSubMergeTag(
          stepType = CampaignStepType.AutoEmailStep,
          shouldHavePrevSubMergeTag = false
        )
      ) ++ List(
        StepTypeWithPrevSubMergeTag(
          stepType = CampaignStepType.AutoEmailStep,
          shouldHavePrevSubMergeTag = true
        )
      ) ++ List.fill(numOfCampaignSteps - stepNumberWithPrevSubMergeTag)(
        StepTypeWithPrevSubMergeTag(
          stepType = CampaignStepType.AutoEmailStep,
          shouldHavePrevSubMergeTag = false
        )
      )

      val numOfCampaignStepVariants = 2

      val initialData =
        NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val account = initialData.account

      val accountId = AccountId(id = account.internal_id)

      val teamId: TeamId = TeamId(id = account.teams.head.team_id)

      val orgId = OrgId(id = account.org.id)

      val taId = account.teams.head.access_members.find { t =>

        t.user_id == account.internal_id

      }.get.ta_id


      val f = CampaignUtils.createNotStartedCampaignWithSteps(
        orgId = orgId,
        accountId = accountId,
        teamId = teamId,
        taId = taId,
        numberOfVariants = numOfCampaignStepVariants,
        emailSettingId = initialData.emailSetting.get.id.get,
        stepTypes = stepsTypes,
        ownerFirstName = account.first_name.get
      )

      val campaign = Await.ready(f, 5.second).value.get.get

      val stepsBeforeReorder = campaignStepService.findStepsByCampaign(campaign = campaign).get

      val newHeadStepBefore = stepsBeforeReorder(stepNumberWithPrevSubMergeTag - 1)

      val newParentStepId = CampaignStepId(id = 0)

      val stepInsertCountRes = campaignStepService.reorderCampaignSteps(
        campaignId = CampaignId(id = campaign.id),
        teamId = TeamId(id = campaign.team_id),
        stepIdToBeReordered = CampaignStepId(id = newHeadStepBefore.id),
        newParentStepId = newParentStepId
      )

      stepInsertCountRes match {

        case Failure(exception) =>

          println(exception)

          assert(false)

        case Success(Right(_)) =>

          assert(false)

        case Success(Left(errMsg)) =>

          assert(errMsg == "Cannot reorder a step with multiple variant which has previous subject merge tag")

      }

    }

    it(
      "reorder 4th step which as {{previous_subject}} merge tag to head step - should swap the subjects, if there is only 1 variant"
    ) {

      val numOfCampaignSteps = 6

      val stepNumberWithPrevSubMergeTag = 4

      val stepsTypes = List.fill(stepNumberWithPrevSubMergeTag - 1)(
        StepTypeWithPrevSubMergeTag(
          stepType = CampaignStepType.AutoEmailStep,
          shouldHavePrevSubMergeTag = false
        )
      ) ++ List(
        StepTypeWithPrevSubMergeTag(
          stepType = CampaignStepType.AutoEmailStep,
          shouldHavePrevSubMergeTag = true
        )
      ) ++ List.fill(numOfCampaignSteps - stepNumberWithPrevSubMergeTag)(
        StepTypeWithPrevSubMergeTag(
          stepType = CampaignStepType.AutoEmailStep,
          shouldHavePrevSubMergeTag = false
        )
      )

      val numOfCampaignStepVariants = 1

      val initialData =
        NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val account = initialData.account

      val accountId = AccountId(id = account.internal_id)

      val teamId: TeamId = TeamId(id = account.teams.head.team_id)

      val orgId = OrgId(id = account.org.id)

      val taId = account.teams.head.access_members.find { t =>

        t.user_id == account.internal_id

      }.get.ta_id

      val f = CampaignUtils.createNotStartedCampaignWithSteps(
        orgId = orgId,
        accountId = accountId,
        teamId = teamId,
        taId = taId,
        numberOfVariants = numOfCampaignStepVariants,
        emailSettingId = initialData.emailSetting.get.id.get,
        stepTypes = stepsTypes,
        ownerFirstName = account.first_name.get
      )

      val campaign = Await.ready(f, 5.second).value.get.get

      val stepsBeforeReorder = campaignStepService.findStepsByCampaign(campaign = campaign).get

      val oldHeadStepBefore = stepsBeforeReorder.head

      val newHeadStepBefore = stepsBeforeReorder(stepNumberWithPrevSubMergeTag - 1)

      val newParentStepId = CampaignStepId(id = 0)

      val stepInsertCountRes = campaignStepService.reorderCampaignSteps(
        campaignId = CampaignId(id = campaign.id),
        teamId = TeamId(id = campaign.team_id),
        stepIdToBeReordered = CampaignStepId(id = newHeadStepBefore.id),
        newParentStepId = newParentStepId
      )

      stepInsertCountRes match {

        case Failure(exception) =>

          println(exception)

          assert(false)

        case Success(reorderRes) =>

          val reorderedStep = stepsBeforeReorder.find(_.id == newHeadStepBefore.id)

          val stepsBeforeReorderFiltered = stepsBeforeReorder.filter(_.id != newHeadStepBefore.id)

          val splitPos = stepsBeforeReorder.indexWhere(_.id == newParentStepId.id) + 1

          val (start, end) = stepsBeforeReorderFiltered.splitAt(splitPos)

          val res = start ++ reorderedStep.toList ++ end

          val headStepUpdatedCampaign = campaignDAO.findCampaignForCampaignUtilsOnly(
            id = campaign.id,
            teamId = TeamId(id = campaign.team_id)
          ).get

          val stepsAfterReorder = campaignStepService.findStepsByCampaign(campaign = headStepUpdatedCampaign).get

          val oldHeadStepBeforeSubject = getSubjectAndBodyFromStepData(
            oldHeadStepBefore.variants.head.step_data
          ).subject
          val oldHeadStepBeforeBody = getSubjectAndBodyFromStepData(
            oldHeadStepBefore.variants.head.step_data
          ).body

          val newHeadStepBeforeSubject = getSubjectAndBodyFromStepData(
            newHeadStepBefore.variants.head.step_data
          ).subject
          val newHeadStepBeforeBody = getSubjectAndBodyFromStepData(
            newHeadStepBefore.variants.head.step_data
          ).body

          val oldHeadStepAfter = stepsAfterReorder.find(_.id == oldHeadStepBefore.id).get

          val newHeadStepAfter = stepsAfterReorder.head

          val oldHeadStepAfterSubject = getSubjectAndBodyFromStepData(
            oldHeadStepAfter.variants.head.step_data
          ).subject
          val oldHeadStepAfterBody = getSubjectAndBodyFromStepData(
            oldHeadStepAfter.variants.head.step_data
          ).body

          val newHeadStepAfterSubject = getSubjectAndBodyFromStepData(
            newHeadStepAfter.variants.head.step_data
          ).subject
          val newHeadStepAfterBody = getSubjectAndBodyFromStepData(
            newHeadStepAfter.variants.head.step_data
          ).body


          assert(
            reorderRes.isRight &&
              stepsAfterReorder.map(_.id) == res.map(_.id) &&
              oldHeadStepBeforeSubject == newHeadStepAfterSubject &&
              newHeadStepBeforeSubject == oldHeadStepAfterSubject &&
              oldHeadStepBeforeBody == oldHeadStepAfterBody &&
              newHeadStepBeforeBody == newHeadStepAfterBody
          )

      }

    }

    it(
      "reorder should fail if current head step does not support {{previous_subject}} merge tag (AutoLinkedinViewProfile) - even if there is only 1 variant"
    ) {

      val stepNumberWithPrevSubMergeTag = 3

      val stepsTypes = List(

        StepTypeWithPrevSubMergeTag(
          stepType = CampaignStepType.AutoLinkedinViewProfile,
          shouldHavePrevSubMergeTag = false
        ),
        StepTypeWithPrevSubMergeTag(
          stepType = CampaignStepType.AutoEmailStep,
          shouldHavePrevSubMergeTag = false
        ),
        StepTypeWithPrevSubMergeTag(
          stepType = CampaignStepType.AutoEmailStep,
          shouldHavePrevSubMergeTag = true
        )
      )

      val numOfCampaignStepVariants = 1

      val initialData =
        NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val account = initialData.account

      val accountId = AccountId(id = account.internal_id)

      val teamId: TeamId = TeamId(id = account.teams.head.team_id)

      val orgId = OrgId(id = account.org.id)

      val taId = account.teams.head.access_members.find { t =>

        t.user_id == account.internal_id

      }.get.ta_id

      val f = CampaignUtils.createNotStartedCampaignWithSteps(
        orgId = orgId,
        accountId = accountId,
        teamId = teamId,
        taId = taId,
        numberOfVariants = numOfCampaignStepVariants,
        emailSettingId = initialData.emailSetting.get.id.get,
        stepTypes = stepsTypes,
        ownerFirstName = account.first_name.get

      )

      val campaign = Await.ready(f, 5.second).value.get.get

      val stepsBeforeReorder = campaignStepService.findStepsByCampaign(campaign = campaign).get

      val newHeadStepBefore = stepsBeforeReorder(stepNumberWithPrevSubMergeTag - 1)

      val newParentStepId = CampaignStepId(id = 0)

      val stepInsertCountRes = campaignStepService.reorderCampaignSteps(
        campaignId = CampaignId(id = campaign.id),
        teamId = TeamId(id = campaign.team_id),
        stepIdToBeReordered = CampaignStepId(id = newHeadStepBefore.id),
        newParentStepId = newParentStepId
      )

      stepInsertCountRes match {

        case Failure(exception) =>

          println(exception)

          assert(false)

        case Success(Right(_)) =>

          assert(false)

        case Success(Left(errMsg)) =>

          val headStepUpdatedCampaign = campaignDAO.findCampaignForCampaignUtilsOnly(
            id = campaign.id,
            teamId = TeamId(id = campaign.team_id)
          ).get

          val stepsAfterReorder = campaignStepService.findStepsByCampaign(campaign = headStepUpdatedCampaign).get

          assert(
            errMsg == "Invalid order step at 1 does not have a previous concrete subject" &&
              stepsAfterReorder.map(_.id) == stepsBeforeReorder.map(_.id)
          )

      }

    }

    it(
      "reorder should fail if current head step does not support {{previous_subject}} merge tag (CallStep) - even if there is only 1 variant"
    ) {

      val stepNumberWithPrevSubMergeTag = 4

      val stepsTypes = List(

        StepTypeWithPrevSubMergeTag(
          stepType = CampaignStepType.CallStep,
          shouldHavePrevSubMergeTag = false
        ),
        StepTypeWithPrevSubMergeTag(
          stepType = CampaignStepType.AutoEmailStep,
          shouldHavePrevSubMergeTag = false
        ),
        StepTypeWithPrevSubMergeTag(
          stepType = CampaignStepType.AutoLinkedinViewProfile,
          shouldHavePrevSubMergeTag = false
        ),
        StepTypeWithPrevSubMergeTag(
          stepType = CampaignStepType.AutoEmailStep,
          shouldHavePrevSubMergeTag = true
        )
      )

      val numOfCampaignStepVariants = 1

      val initialData =
        NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val account = initialData.account

      val accountId = AccountId(id = account.internal_id)

      val teamId: TeamId = TeamId(id = account.teams.head.team_id)

      val orgId = OrgId(id = account.org.id)

      val taId = account.teams.head.access_members.find { t =>

        t.user_id == account.internal_id

      }.get.ta_id

      val f = CampaignUtils.createNotStartedCampaignWithSteps(
        orgId = orgId,
        accountId = accountId,
        teamId = teamId,
        taId = taId,
        numberOfVariants = numOfCampaignStepVariants,
        emailSettingId = initialData.emailSetting.get.id.get,
        stepTypes = stepsTypes,
        ownerFirstName = account.first_name.get

      )

      val campaign = Await.ready(f, 5.second).value.get.get

      val stepsBeforeReorder = campaignStepService.findStepsByCampaign(campaign = campaign).get

      val newHeadStepBefore = stepsBeforeReorder(stepNumberWithPrevSubMergeTag - 1)

      val newParentStepId = CampaignStepId(id = 0)

      val stepInsertCountRes = campaignStepService.reorderCampaignSteps(
        campaignId = CampaignId(id = campaign.id),
        teamId = TeamId(id = campaign.team_id),
        stepIdToBeReordered = CampaignStepId(id = newHeadStepBefore.id),
        newParentStepId = newParentStepId
      )

      stepInsertCountRes match {

        case Failure(exception) =>

          println(exception)

          assert(false)

        case Success(Right(_)) =>

          assert(false)

        case Success(Left(errMsg)) =>

          val headStepUpdatedCampaign = campaignDAO.findCampaignForCampaignUtilsOnly(
            id = campaign.id,
            teamId = TeamId(id = campaign.team_id)
          ).get

          val stepsAfterReorder = campaignStepService.findStepsByCampaign(campaign = headStepUpdatedCampaign).get

          assert(
            errMsg == "Invalid order step at 1 does not have a previous concrete subject" &&
              stepsAfterReorder.map(_.id) == stepsBeforeReorder.map(_.id)
          )

      }

    }

  }

}
