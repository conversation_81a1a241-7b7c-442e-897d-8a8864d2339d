package db_test_spec.api.campaigns.services

import api.AppConfig
import api.accounts.models.{AccountId, OrgId}
import api.accounts.{Account, TeamId}
import api.campaigns.Campaign
import api.campaigns.DataForCampaignAssignProspects.AssignProspectToCampaignDataV3
import api.campaigns.services.{AssignProspectsResponseV3, CampaignId}
import api.prospects.models.{ProspectCategory, ProspectCategoryNew, ProspectId}
import db_test_spec.api.{DbTestingBeforeAllAndAfterAll, InitialData}
import db_test_spec.api.accounts.fixtures.NewAccountAndEmailSettingData
import db_test_spec.api.campaigns.test_utils.CampaignUtils
import db_test_spec.api.prospects.dao.ProspectCategoryDAO
import db_test_spec.api.prospects.fixtures.ProspectFixtureForIntegrationTest
import eventframework.ProspectObject
import org.scalatest.ParallelTestExecution

import scala.concurrent.Future
import scala.util.{Failure, Success, Try}

class CampaignApiServiceSpec extends DbTestingBeforeAllAndAfterAll {

  describe("Test assignProspectToCampaignV3 - autoUpdateProspectCategory") {

    it("should not affect anything else even if autoUpdateProspectCategory fails") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val account: Account = initialData.account

      val orgId = OrgId(id = account.org.id)

      val accountId: AccountId = AccountId(account.internal_id)

      val teamId: TeamId = TeamId(account.teams.head.team_id)

      val assignProspectsResFut = for {

        campaign: Campaign <- CampaignUtils.createNotStartedCampaignWithSteps(
          orgId = orgId,
          teamId = teamId,
          accountId = accountId,
          taId = account.teams.head.access_members.head.ta_id,
          emailSettingId = initialData.emailSetting.get.id.get,
          stepTypes = Seq(),
          ownerFirstName = account.first_name.get
        )

        newProspects: Seq[ProspectObject] <- Future.fromTry {
          ProspectFixtureForIntegrationTest.createUpdateOrAssignProspect(
            campaignId = None,
            accountId = accountId,
            teamId = teamId,
            account = account,
            generateProspectCountIfNoGivenProspect = 4
          )
        }

        // deleting open prospect category, so that autoUpdateProspectCategory will fail
        _ <- Future.fromTry {

          ProspectCategoryDAO.deleteProspectCategory(
            teamId = teamId,
            prospectCategory = ProspectCategory.OPEN,
          )

        }

        data: AssignProspectToCampaignDataV3 = AssignProspectToCampaignDataV3(
          prospect_ids = newProspects.map(_.prospect_uuid.get).toList,
          ignore_prospects_in_other_campaigns = None
        )

        assignProspectsRes: AssignProspectsResponseV3 <- campaignApiService.assignProspectToCampaignV3(
          data = data,
          teamId = teamId,
          doer = account,
          account_id = accountId,
          permittedAccountIds = account.teams.head.access_members.map(_.user_id),
          campaignId = CampaignId(id = campaign.id),
          campaignName = campaign.name,
          campaignSettings = campaign.settings
        ) match {

          case Left(_) =>

            Future.failed(new Exception("Failed to assign prospects"))

          case Right(value) =>

            Future.successful(value)

        }


      } yield {

        assignProspectsRes

      }

      assignProspectsResFut
        .map { _ =>

          assert(true)

        }
        .recover { case e =>

          println(e)

          assert(false)

        }

    }

    it("should update the prospect category to open") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val account: Account = initialData.account

      val orgId = OrgId(id = account.org.id)

      val accountId: AccountId = AccountId(account.internal_id)

      val teamId: TeamId = TeamId(account.teams.head.team_id)

      val assignProspectsResFut = for {

        campaign: Campaign <- CampaignUtils.createNotStartedCampaignWithSteps(
          orgId = orgId,
          teamId = teamId,
          accountId = accountId,
          taId = account.teams.head.access_members.head.ta_id,
          emailSettingId = initialData.emailSetting.get.id.get,
          stepTypes = Seq(),
          ownerFirstName = account.first_name.get
        )

        newProspects: Seq[ProspectObject] <- Future.fromTry {
          ProspectFixtureForIntegrationTest.createUpdateOrAssignProspect(
            campaignId = None,
            accountId = accountId,
            teamId = teamId,
            account = account,
            generateProspectCountIfNoGivenProspect = 4
          )
        }

        data: AssignProspectToCampaignDataV3 = AssignProspectToCampaignDataV3(
          prospect_ids = newProspects.map(_.prospect_uuid.get).toList,
          ignore_prospects_in_other_campaigns = None
        )

        assignProspectsRes: AssignProspectsResponseV3 <- campaignApiService.assignProspectToCampaignV3(
          data = data,
          teamId = teamId,
          doer = account,
          account_id = accountId,
          permittedAccountIds = account.teams.head.access_members.map(_.user_id),
          campaignId = CampaignId(id = campaign.id),
          campaignName = campaign.name,
          campaignSettings = campaign.settings
        ) match {

          case Left(_) =>

            Future.failed(new Exception("Failed to assign prospects"))

          case Right(value) =>

            Future.successful(value)

        }


      } yield {

        assignProspectsRes

      }

      assignProspectsResFut
        .map { assignProspectsRes =>

          prospectDAOService.getCurrentProspectCategoryDetails(
            teamId = teamId,
            prospectIds = assignProspectsRes.totalAssignedProspectIds.map(pid => ProspectId(id = pid)),
          ) match {

            case Failure(exception) =>

              println(exception)

              assert(false)

            case Success(prsCatDetails) =>

                val prospectsWithNotCategorized = prsCatDetails.filter { prsCat =>

                  Try {
                    ProspectCategoryNew.withName(s = prsCat.prospectCategoryTextId)
                  }
                    .map(_ == ProspectCategoryNew.NOT_CONTACTED)
                    .getOrElse(false)

                }

                val areAllCatsOpen = prsCatDetails.forall { p =>

                  Try(ProspectCategoryNew.withName(p.prospectCategoryTextId))
                    .map(_ == ProspectCategoryNew.APPROACHING)
                    .getOrElse(false)

                }

                assert(
                  prospectsWithNotCategorized.isEmpty && areAllCatsOpen
                )
          }
        }
        .recover { case e =>

          println(e)

          assert(false)

        }

    }

  }

}
