package db_test_spec.api.campaigns

import api.accounts.TeamId
import api.campaigns.CampaignEmailSettings
import api.campaigns.services.CampaignId
import api.domain_health.{DomainChecks, DomainHealthCheckId}
import api.emails.EmailAddressHost
import app_services.blacklist_monitoring.models.{BlacklistCheckResult, BlacklistCheckStatus}
import db_test_spec.api.{DbTestingBeforeAllAndAfterAll, InitialData}
import db_test_spec.api.accounts.fixtures.NewAccountAndEmailSettingData
import db_test_spec.api.campaigns.test_utils.{CampaignUtils, CreateAndStartCampaignData}
import org.scalatest.ParallelTestExecution
import play.api.libs.json.Json

import scala.concurrent.Future

class CampaignEmailSettingsDAOSpec extends DbTestingBeforeAllAndAfterAll {

    describe("getCampaignSenderEmailsWithSession"){
        it("should return campaignEmailSettings"){
            val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

            val domainBlacklistResult: BlacklistCheckResult = BlacklistCheckResult(
                status = BlacklistCheckStatus.FAILED,
                failureDescription = Some("ivmURI"),
                fullResult = Json.obj(
                    "blacklist status" -> "failed"
                )
            )

            val campaignEmailSettings:Future[List[CampaignEmailSettings]] = for {
                campaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
                    initialData = initialData,
                    generateProspectCountIfNoGivenProspect = 4
                )

                _: List[DomainChecks] <- Future.fromTry(domainHealthCheckDAO.createAndUpdatePushedToQueue(List(EmailAddressHost("gmail.com"))))

                _: DomainHealthCheckId <- Future.fromTry(domainHealthCheckDAO.updateBlacklistResult(domainBlacklistResult, "gmail.com", true))


                result:List[CampaignEmailSettings] <- Future.fromTry(campaignEmailSettingsDAO.getCampaignSenderEmailsWithSession(
                    campaignId = CampaignId(campaignData.campaign.id),
                    teamId = TeamId(initialData.account.teams.head.team_id)
                ))
            }yield{
                result
            }

            campaignEmailSettings.map { ces =>
              println(ces)
                val error = ces.find(_.sender_email == initialData.emailSetting.get.email).get.error.get
                assert(error == "Your sending email domain is found in a global spam blacklist. Please check the status by going to Settings -> Team Settings -> Domain Health.")
            }.recover { e =>
                println(e)
                assert(false)
            }

        }
    }

}
