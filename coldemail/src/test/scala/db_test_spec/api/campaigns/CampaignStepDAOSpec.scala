package db_test_spec.api.campaigns

import api.accounts.{AccountWithoutOrgInternal, TeamAccount, TeamId}
import api.accounts.models.AccountId
import api.campaigns.models.{CampaignStepData, CreateCampaignStepVariantError}
import api.campaigns.{Campaign, CampaignStep, CampaignStepVariant, CampaignStepVariantCreateOrUpdate}
import api.prospects.InferredQueryTimeline
import api.tasks.models.TaskPriority
import app.db_test.{CustomNotCategorizedAndDoNotContactIds, SchedulerTestInput}
import db_test_spec.api.accounts.fixtures.NewAccountAndEmailSettingData
import db_test_spec.api.campaigns.fixtures.CreateNewCampaignFixture.findCategorizedAndDoNotContactCustomIds
import db_test_spec.api.{AppSpecFixture, DbTestingBeforeAllAndAfterAll, InitialData, InputForInitializingCampaignCreateData, SchedulerIntegrationTestResult}
import org.joda.time.DateTime
import utils.SRLogger
import utils.helpers.LogHelpers

import scala.concurrent.Future
import scala.util.{Random, Try}

class CampaignStepDAOSpec extends DbTestingBeforeAllAndAfterAll {


  lazy val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get


  describe("CampaignStepDAO") {
    it("should delete campaign step") {
      given logger: SRLogger = AppSpecFixture.logger
      val categorizedAndDoNotContactCustomIds: CustomNotCategorizedAndDoNotContactIds = findCategorizedAndDoNotContactCustomIds(prospect_categories = initialData.account.teams.head.prospect_categories_custom)
      val input: InputForInitializingCampaignCreateData = SchedulerTestInput.getInputForSchedulerIntegrationTest(
        sharedData = initialData,
        categorizedAndDoNotContactCustomIds = categorizedAndDoNotContactCustomIds,
        prospect_emails = Seq(Random.alphanumeric.take(10).mkString("") + "@gmail.com"),
        linkedSettingId = None
      )
      val initializeCreateAndStartCampaignData: InitializedCreateAndStartCampaignData = AppSpecFixture.initializeCampaignDataForIntegrationTest(
        inputForInitializingCampaignCreateData = input
      )

      val scheduleTaskData: Future[Int] = for {
        campaignCreationData: CampaignCreationData <- CampaignCreationFixtureForIntegrationTest.createAndStartCampaign(
          inputForInitializingCampaignCreateData = input,
          initializedData = initializeCreateAndStartCampaignData
        )


        add_one_more_step: Either[CreateCampaignStepVariantError, CampaignStepVariant] <- {

          campaignStepService.createVariant(
            orgId = input.org_id.id,
            data = initializeCreateAndStartCampaignData.campaignStepVariantCreateOrUpdateEmail.copy(
              parent_id = campaignCreationData.campaignStepVariant.id
            ),
            teamId = input.team_id.id,
            userId = input.account_id.id,
            taId = input.ta_id,
            stepId = 0,
            campaignId = campaignCreationData.campaign.id,
            campaignHeadStepId = Some(campaignCreationData.campaignStepVariant.id)
          )


        }

        delete_step: Int <- {

          add_one_more_step match {

            case Left(e) =>

              Future.failed(new Exception(e.toString))


            case Right(d) =>

              val campaign_step = CampaignStep(
                id = d.step_id,
                label = d.label,
                campaign_id = campaignCreationData.campaignStepVariant.campaign_id,
                delay = 84000,
                step_type = campaignCreationData.campaignStepVariant.step_data.step_type,
                created_at = DateTime.now()
              )

              Future.fromTry(campaignStepDAOService.delete(
                step = campaign_step,
                campaign = campaignCreationData.campaign.copy(
                  head_step_id = Some(campaignCreationData.campaignStepVariant.id)
                ),
                teamId = input.team_id
              ))

          }


        }


      } yield {

        delete_step

      }

      scheduleTaskData
        .map(data  => {

          println(s"data -> ${data}")

          assert(data == 1)
      })
        .recover(err => {
          println(s"err -> ${LogHelpers.getStackTraceAsString(err)}")
          assert(false)
        })


    }
  }


  describe("testing __updateVariant"){
    it("should update the campaign step"){

      given logger: SRLogger = AppSpecFixture.logger
      val categorizedAndDoNotContactCustomIds: CustomNotCategorizedAndDoNotContactIds = findCategorizedAndDoNotContactCustomIds(prospect_categories = initialData.account.teams.head.prospect_categories_custom)
      val input: InputForInitializingCampaignCreateData = SchedulerTestInput.getInputForSchedulerIntegrationTest(
        sharedData = initialData,
        categorizedAndDoNotContactCustomIds = categorizedAndDoNotContactCustomIds,
        prospect_emails = Seq(Random.alphanumeric.take(10).mkString("") + "@gmail.com"),
        linkedSettingId = None
      )
      val initializeCreateAndStartCampaignData: InitializedCreateAndStartCampaignData = AppSpecFixture.initializeCampaignDataForIntegrationTest(
        inputForInitializingCampaignCreateData = input
      )

      val updated_step_variant: Future[Option[CampaignStepVariant]] = for {

        campaignCreationData: CampaignCreationData <- CampaignCreationFixtureForIntegrationTest.createAndStartCampaign(
          inputForInitializingCampaignCreateData = input,
          initializedData = initializeCreateAndStartCampaignData
        )
        update_campaign_step: Option[CampaignStepVariant] <- {

          Future.successful(campaignStepVariantDAO.__updateVariant(

                id = campaignCreationData.campaignStepVariant.id,
                stepId = campaignCreationData.campaignStepVariant.step_id,
                campaignId = campaignCreationData.campaign.id,
                data =  CampaignStepVariantCreateOrUpdate(
                  parent_id = 0L,
                  step_data = campaignCreationData.campaignStepVariant.step_data,
                  step_delay = 84000,
                  notes = None,
                  priority = None,
                ),
                phishLinksUpdate = "",
                teamId = input.team_id
              ))

        }


      } yield {

        update_campaign_step

      }

      updated_step_variant
        .map(data => {

          data match {
            case Some(value) => assert(true)
            case None => assert(false)
          }

        })
        .recover(err => {
          println(s"err -> ${err}")
          assert(false)
        })


    }
  }

}
