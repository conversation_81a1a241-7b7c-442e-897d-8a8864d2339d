package db_test_spec.api.campaigns

import api.accounts._
import api.accounts.email.models.SrMxCheckESPType
import api.accounts.models.AccountProfileInfo
import api.campaigns._
import api.campaigns.models.IgnoreProspectsInOtherCampaigns
import api.campaigns.services.{CampaignCreationError, CreateCampaignStepVariantError, StartCampaignError}
import api.emails.EmailSetting
import api.prospects.models.{SrProspectColumns, UpdateProspectType}
import api.prospects.{CreateOrUpdateProspectsResult, ProspectCreateFormData}
import app.db_test.{CustomNotCategorizedAndDoNotContactIds, SchedulerTestInput}
import db_test_spec.api.campaigns.fixtures.CreateNewCampaignFixture.findCategorizedAndDoNotContactCustomIds
import db_test_spec.api.prospects.fixtures.ProspectFixtureForIntegrationTest.{domainPublicDNSDAO, prospectDAOService}
import db_test_spec.api.{AppSpecFixture, InitialData, InputForInitializingCampaignCreateData, SRSetupAndDeleteFixtures}
import eventframework.ProspectObject
import io.smartreach.sr_dns_utils.{DNSService, DomainPublicDNSAddOrUpdateData}
import org.joda.time.DateTime
import play.api.libs.json.Json
import utils.SRLogger
import utils.emailvalidation.EmailValidationService
import utils.helpers.LogHelpers
import utils.testapp.TestAppTrait

import scala.concurrent.Future
import scala.util.{Random, Success, Try}

case class InitializedCreateAndStartCampaignData(
                                                 campaignSettings: CampaignSettings,
                                                 createCampaignData: CampaignCreateForm,
                                                 prospectCreateFormData: Seq[ProspectCreateFormData],
                                                 accountProfileInfo: AccountProfileInfo,
                                                 account: Account,
                                                 campaignStepVariantCreateOrUpdateLinkedin: CampaignStepVariantCreateOrUpdate,
                                                 campaignStepVariantCreateOrUpdateEmail: CampaignStepVariantCreateOrUpdate,
                                                 emailSetting: EmailSetting,
                                                 teamAccount: TeamAccount,
                                                 organizationWithCurrentData: OrganizationWithCurrentData,
                                                 timezone: String
                                               )

case class CampaignCreationData(
                                 updateLastReadForReplies: Option[Long],
                                 campaignWithStatsAndEmail: CampaignWithStatsAndEmail,
                                 prospects: CreateOrUpdateProspectsResult,
                                 campaignStepVariant: CampaignStepVariant,
                                 campaign: Campaign
                               )
object CampaignCreationFixtureForIntegrationTest extends TestAppTrait{

  def createAndStartCampaign(
                              inputForInitializingCampaignCreateData: InputForInitializingCampaignCreateData,
                              initializedData: InitializedCreateAndStartCampaignData,
                            )(using Logger: SRLogger): Future[CampaignCreationData] = {
    val input = inputForInitializingCampaignCreateData
    for {
      updateLastReadForReplies: Option[Long] <- Future.fromTry {
        emailSettingDAO.updateLastReadForReplies(
          emailSetting = initializedData.emailSetting,
          lastReadForReplies = DateTime.now(),
          Logger = Logger
        )
      }
      // create a campaign
      campaignWithStatsAndEmailEither: Either[CampaignCreationError, CampaignWithStatsAndEmail] <- {
        campaignService.createCampaign(
          orgId = input.org_id.id,
          accountId = input.account_id.id,
          teamId = input.team_id.id,
          taId = input.ta_id,
          data = initializedData.createCampaignData,
          campaignSettings = Some(initializedData.campaignSettings),
          permittedAccountIdsForEditCampaigns = Seq(input.account_id.id),
          ownerFirstName ="CampaignCreationFixtureForIntegrationTest"
        )
      }

      campaignWithStatsAndEmail: CampaignWithStatsAndEmail <- campaignWithStatsAndEmailEither match {
        case Left(err) =>
          println(s"Campaign Not Created ${err}")
          Future.failed(new Exception(s"Campaign Not Created: $err"))

        case Right(campaignWithStatsAndEmail) =>
          print("Campaign With Stats and email created")
//          print(campaignWithStatsAndEmail)
          Future.successful(campaignWithStatsAndEmail)
      }

      // add a prospect to the above campaign
      prospects: CreateOrUpdateProspectsResult <- Future.fromTry {
        prospectService.createOrUpdateProspects(
          ownerAccountId = campaignWithStatsAndEmail.owner_id,
          teamId =campaignWithStatsAndEmail.team_id,
          listName = None,
          prospects = initializedData.prospectCreateFormData,
          updateProspectType = UpdateProspectType.ForceUpdate,
          ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads = true,

          doerAccount = initializedData.account,
          prospectSource = None,
          prospectAccountId = None,

          campaign_id = Some(campaignWithStatsAndEmail.id),
          prospect_tags = None,
          ignoreProspectInOtherCampaign = IgnoreProspectsInOtherCampaigns.DoNotIgnore,
          deduplicationColumns = Some(Seq(SrProspectColumns.Email)),
          auditRequestLogId = None,
          batchInsertLimit = 1000,

          SRLogger = Logger
        )
      }

      findProspect: Seq[ProspectObject] <- Future.fromTry {
        prospectDAOService.find(
          byProspectIds = prospects.created_ids ++ prospects.updated_ids ++ prospects.assigned_ids,
          teamId = campaignWithStatsAndEmail.team_id,
          Logger = Logger
        )
      }
      insert_into_DNS: Seq[String] <- Future{
        findProspect.flatMap { p =>
          val data = if (p.email.isDefined) {
            val (_, domain) = EmailValidationService.getLowercasedNameAndDomainFromEmail(email = p.email.get)

            domainPublicDNSDAO.insertOrUpdate(
              domainPublicDNSAddOrUpdateData = DomainPublicDNSAddOrUpdateData(
                domain = domain,
                dns_service = DNSService.GoogleDNS,
                raw_response = Json.obj("test" -> "test"),
                mx_inbox_provider = SrMxCheckESPType.Google,
                is_valid = true
              )
            ).map(_.toList)
          } else Success(List())
          data.get
        }
      }
      createdVariant: Either[CreateCampaignStepVariantError, CampaignStepVariant] <- {
        if (input.enable_email_scheduler) {
          campaignStepService.createVariant(
            orgId = input.org_id.id,
            data = initializedData.campaignStepVariantCreateOrUpdateEmail,
            teamId = input.team_id.id,
            userId = input.account_id.id,
            taId = input.ta_id,
            stepId = 0,
            campaignId = campaignWithStatsAndEmail.id,
            campaignHeadStepId = None
          )
        } else {
          campaignStepService.createVariant(
            orgId = input.org_id.id,
            data = initializedData.campaignStepVariantCreateOrUpdateLinkedin,
            teamId = input.team_id.id,
            userId = input.account_id.id,
            taId = input.ta_id,
            stepId = 0,
            campaignId = campaignWithStatsAndEmail.id,
            campaignHeadStepId = None
          )
        }
      }

      campaignStepVariant: CampaignStepVariant <- createdVariant match {
        case Left(err) =>
          Future.failed(new Exception(s"Campaign Not Created: $err"))

        case Right(createdVariant) =>
          Future.successful(createdVariant)
      }

      campaign: Campaign <- Future.successful(
        campaignDAO.findCampaignForCampaignUtilsOnly(
          id = campaignWithStatsAndEmail.id,
          teamId = TeamId(campaignWithStatsAndEmail.team_id)
        ).get
      )

      startedCampaign: Either[StartCampaignError, CampaignWithStatsAndEmail] <- campaignStartService.startCampaign(
        c = campaign,
        team = Some(initializedData.teamAccount),
        org = initializedData.organizationWithCurrentData,
        scheduleStartAt = None,
        scheduleStartAtTimeZone = Some(input.timezone),
        userId = input.account_id.id,
        teamId = input.team_id.id,
        Logger = Logger
      )

      startedCampaignData: CampaignWithStatsAndEmail <- startedCampaign match {
        case Left(err) => Future.failed(new Exception(s"Campaign Not started: $err"))
        case Right(c) => Future.successful(c)
      }

      campaign_step_variant: CampaignStepVariant <- createdVariant match {

        case Left(e) =>

          Future.failed(new Exception(e.toString))

        case Right(step_variant) =>

          Future.successful(step_variant)

      }

    } yield {
      CampaignCreationData(
        updateLastReadForReplies = updateLastReadForReplies,
        campaignWithStatsAndEmail = startedCampaignData,
        prospects = prospects,
        campaignStepVariant = campaignStepVariant,
        campaign = campaign
      )
    }
  }

  def createDefaultCampaign(
    initialData: InitialData
  )(using Logger: SRLogger): Future[CampaignCreationData] = {
    val categorizedAndDoNotContactCustomIds: CustomNotCategorizedAndDoNotContactIds = findCategorizedAndDoNotContactCustomIds(prospect_categories = initialData.account.teams.head.prospect_categories_custom)
    val linkedSettingId = linkedinSettingService.findLinkedinSettingIdFromUuidAndTeamId(
      uuid = initialData.linkedinAccountSettings.get.settings.uuid,
      teamId = TeamId(initialData.linkedinAccountSettings.get.settings.team_id)
    ).toOption
    val input: InputForInitializingCampaignCreateData = SchedulerTestInput.getInputForSchedulerIntegrationTest(
      sharedData = initialData,
      categorizedAndDoNotContactCustomIds = categorizedAndDoNotContactCustomIds,
      prospect_emails = Seq(Random.alphanumeric.take(10).mkString("") + "@gmail.com"),
      linkedSettingId = linkedSettingId
    )
    val initializeCreateAndStartCampaignData: InitializedCreateAndStartCampaignData = AppSpecFixture.initializeCampaignDataForIntegrationTest(
      inputForInitializingCampaignCreateData = input
    )
    CampaignCreationFixtureForIntegrationTest.createAndStartCampaign(
      inputForInitializingCampaignCreateData = input,
      initializedData = initializeCreateAndStartCampaignData
    ).map{value =>
      value
    }
  }


}
