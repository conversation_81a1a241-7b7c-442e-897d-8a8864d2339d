package db_test_spec.api.campaigns

import api.accounts.models.{AccountId, OrgId}
import api.accounts.{Account, AccountUuid, ReplyHandling, TeamId, TeamMember, UpdateTeamConfig}
import api.campaigns.models.{SendEmailFromCampaignDetails, StepDetails, WillResumeAtUpdatedBy}
import api.campaigns.{CPTuple, Campaign, CampaignStep, CampaignStepVariant, CampaignWithStatsAndEmail}
import api.campaigns.services.{CampaignId, CreateCampaignStepVariantError, DuplicateCampaignError}
import api.emails.{EmailScheduledNewAfterSaving, EmailSetting}
import api.prospects.CampaignProspectData
import api.prospects.models.ProspectId
import api.tasks.models.{NewTask, TaskCreatedVia, TaskData, TaskPriority, TaskStatus, TaskType}
import app.db_test.{CustomNotCategorizedAndDoNotContactIds, SchedulerTestInput}
import db_test_spec.api.{AppSpecFixture, DbTestingBeforeAllAndAfterAll, InitialData, InputForInitializingCampaignCreateData, SRSetupAndDeleteFixtures}
import db_test_spec.api.accounts.fixtures.{EmailScheduledNewFixture, NewAccountAndEmailSettingData}
import db_test_spec.api.campaigns.dao.CampaignTestDAO
import db_test_spec.api.campaigns.fixtures.CreateNewCampaignFixture.{createNewCampaign, findCategorizedAndDoNotContactCustomIds}
import db_test_spec.api.campaigns.fixtures.{CreateNewCampaignFixture, CreateStepForCampaignFixture, NewCampaignCreationData}
import db_test_spec.api.campaigns.test_utils.{CampaignUtils, CreateAndStartCampaignData}
import db_test_spec.api.prospects.fixtures.ProspectFixtureForIntegrationTest
import db_test_spec.api.scheduler.fixtures.{DefaultParametersFixtureForInitializingDataForReScheduling, ReSchedulingFixture, ScheduleTaskFixture}
import eventframework.ProspectObject
import io.smartreach.esp.api.emails.EmailSettingId
import org.joda.time.DateTime
import org.scalatest.ParallelTestExecution
import play.api.libs.json.JsLookupResult
import sr_scheduler.models.ChannelData.EmailChannelData
import utils.SRLogger
import utils.helpers.LogHelpers
import utils.mq.channel_scheduler.channels.{EmailChannelScheduler, ScheduleTasksData}

import scala.concurrent.{Await, Future}
import scala.util.{Failure, Random, Success}

class CampaignServiceSpec extends DbTestingBeforeAllAndAfterAll {


  describe("CampaignService") {

    /*
         20-apr-2024 : head step deletion without prospect scheduled
     */
    it("should delete campaign head step") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
      val account: Account = initialData.account
      val emailSetting: EmailSetting = initialData.emailSetting.get
      val orgId: OrgId = OrgId(account.org.id)
      val accountId: AccountId = AccountId(account.internal_id)
      val teamId: TeamId = TeamId(account.teams.head.team_id)
      val taId: Long = account.teams.head.access_members.head.ta_id
      //      val emailSettingId = EmailSettingId(emailSetting.id.get.emailSettingId)

      val scheduleTaskData = for {
        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 1
        )

        addStep_2: CampaignStepVariant <- {
          CreateStepForCampaignFixture.createAutoEmailStepForCampaign(
            orgId = orgId,
            teamId = teamId,
            accountId = accountId,
            taId = taId,
            campaignId = CampaignId(createAndStartCampaignData.createCampaign.id),
            parentId = createAndStartCampaignData.addStep.step_id
          )
        }

        campaign_step_details: CampaignStep <- {

          val campaign_step = CampaignStep(
            id = createAndStartCampaignData.addStep.step_id,
            label = createAndStartCampaignData.addStep.label,
            campaign_id = createAndStartCampaignData.addStep.campaign_id,
            delay = 84000,
            step_type = createAndStartCampaignData.addStep.step_data.step_type,
            created_at = DateTime.now()
          )


          Future.successful((campaign_step))

        }

        delete_head_step: Option[Long] <- {

          Future.fromTry(campaignService.deleteVariant(
            stepId = createAndStartCampaignData.addStep.step_id,
            campaign = createAndStartCampaignData.campaign.copy(
              head_step_id = Some(createAndStartCampaignData.addStep.step_id)
            )
            ,
            loggedinAccount = initialData.account,
            campaignId = createAndStartCampaignData.campaign.id,
            step = campaign_step_details,
            variantId = createAndStartCampaignData.addStep.id.toInt,
            teamId = teamId,
          ))

        }


      } yield {

        createAndStartCampaignData

      }

      scheduleTaskData
        .map(data => {

          println(s"data -> ${data}")


          campaignStepDAO.find(
            stepId = data.addStep.step_id,
            campaignId = data.campaign.id
          ) match {

            case None =>

              // correct we have deleted step so it can't be found
              print("step not found")

              assert(true)


            case Some(ste) =>

              assert(false)

            // this can't be possible.


          }


          //          assert(data.isDefined)
        })
        .recover(err => {
          println(s"err -> ${err}")
          assert(false)
        })


    }

    /*
           20-apr-2024 : middle step deletion without prospect scheduled
    */
    it("should delete middle step of campaign ( 2nd step here )") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
      val account: Account = initialData.account
      val emailSetting: EmailSetting = initialData.emailSetting.get
      val orgId: OrgId = OrgId(account.org.id)
      val accountId: AccountId = AccountId(account.internal_id)
      val teamId: TeamId = TeamId(account.teams.head.team_id)
      val taId: Long = account.teams.head.access_members.head.ta_id
      //      val emailSettingId = EmailSettingId(emailSetting.id.get.emailSettingId)

      val scheduleTaskData = for {
        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 1
        )

        addStep_2: CampaignStepVariant <- {
          CreateStepForCampaignFixture.createAutoEmailStepForCampaign(
            orgId = orgId,
            teamId = teamId,
            accountId = accountId,
            taId = taId,
            campaignId = CampaignId(createAndStartCampaignData.createCampaign.id),
            parentId = createAndStartCampaignData.addStep.step_id
          )
        }

        campaign_step_details: CampaignStep <- {

          val campaign_step = CampaignStep(
            id = addStep_2.step_id,
            label = addStep_2.label,
            campaign_id = addStep_2.campaign_id,
            delay = 84000,
            step_type = addStep_2.step_data.step_type,
            created_at = DateTime.now()
          )


          Future.successful((campaign_step))

        }


        add_one_more_step_2: CampaignStepVariant <- {

          CreateStepForCampaignFixture.createAutoEmailStepForCampaign(
            orgId = orgId,
            teamId = teamId,
            accountId = accountId,
            taId = taId,
            campaignId = CampaignId(createAndStartCampaignData.createCampaign.id),
            parentId = addStep_2.step_id
          )


        }

        delete_step_2: Option[Long] <- {

          Future.fromTry(campaignService.deleteVariant(
            stepId = addStep_2.step_id,
            campaign = createAndStartCampaignData.campaign.copy(
              head_step_id = Some(createAndStartCampaignData.addStep.step_id)
            ),
            loggedinAccount = initialData.account,
            campaignId = createAndStartCampaignData.campaign.id,
            step = campaign_step_details,
            variantId = addStep_2.id.toInt,
            teamId = teamId,
          ))

        }


      } yield {

        addStep_2

      }

      scheduleTaskData
        .map(data => {

          println(s"data -> ${data}")


          campaignStepDAO.find(
            stepId = data.step_id,
            campaignId = data.campaign_id
          ) match {

            case None =>

              // correct we have deleted step so it can't be found
              print("step not found")

              assert(true)


            case Some(ste) =>

              assert(false)

            // this can't be possible.


          }


          //          assert(data.isDefined)
        })
        .recover(err => {
          println(s"err -> ${err}")
          assert(false)
        })


    }

    /*
      20-apr-2024 : last step deletion without prospect scheduled
     */
    it("should delete campaign last step ( 2nd step here )") {
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
      val account: Account = initialData.account
      val emailSetting: EmailSetting = initialData.emailSetting.get
      val orgId: OrgId = OrgId(account.org.id)
      val accountId: AccountId = AccountId(account.internal_id)
      val teamId: TeamId = TeamId(account.teams.head.team_id)
      val taId: Long = account.teams.head.access_members.head.ta_id
      //      val emailSettingId = EmailSettingId(emailSetting.id.get.emailSettingId)

      val scheduleTaskData = for {
        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 1
        )

        addStep_2: CampaignStepVariant <- {
          CreateStepForCampaignFixture.createAutoEmailStepForCampaign(
            orgId = orgId,
            teamId = teamId,
            accountId = accountId,
            taId = taId,
            campaignId = CampaignId(createAndStartCampaignData.createCampaign.id),
            parentId = createAndStartCampaignData.addStep.step_id
          )
        }

        campaign_step_details: CampaignStep <- {

          val campaign_step = CampaignStep(
            id = addStep_2.step_id,
            label = addStep_2.label,
            campaign_id = addStep_2.campaign_id,
            delay = 84000,
            step_type = addStep_2.step_data.step_type,
            created_at = DateTime.now()
          )


          Future.successful((campaign_step))

        }

        delete_step_2: Option[Long] <- {

          Future.fromTry(campaignService.deleteVariant(
            stepId = addStep_2.step_id,
            campaign = createAndStartCampaignData.campaign.copy(
              head_step_id = Some(createAndStartCampaignData.addStep.step_id)
            ),
            loggedinAccount = initialData.account,
            campaignId = createAndStartCampaignData.campaign.id,
            step = campaign_step_details,
            variantId = addStep_2.id.toInt,
            teamId = teamId,
          ))

        }


      } yield {

        addStep_2

      }

      scheduleTaskData
        .map(data => {

          println(s"data -> ${data}")


          campaignStepDAO.find(
            stepId = data.step_id,
            campaignId = data.campaign_id
          ) match {

            case None =>

              // correct we have deleted step so it can't be found
              print("step not found")

              assert(true)


            case Some(ste) =>

              assert(false)

            // this can't be possible.


          }


          //          assert(data.isDefined)
        })
        .recover(err => {
          println(s"err -> ${err}")
          assert(false)
        })


    }

    ////////////////////    ----------- below test cases are with prospect reverts also ------------- /////////////


    /*
        20-apr-2024 : head step deletion without prospect scheduled
     */
    it("should delete campaign head step after running scheduler ") {
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
      val account: Account = initialData.account
      val emailSetting: EmailSetting = initialData.emailSetting.get
      val orgId: OrgId = OrgId(account.org.id)
      val accountId: AccountId = AccountId(account.internal_id)
      val teamId: TeamId = TeamId(account.teams.head.team_id)
      val taId: Long = account.teams.head.access_members.head.ta_id
      //      val emailSettingId = EmailSettingId(emailSetting.id.get.emailSettingId)

      val scheduleTaskData = for {
        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 1
        )

        addStep_2: CampaignStepVariant <- {
          CreateStepForCampaignFixture.createAutoEmailStepForCampaign(
            orgId = orgId,
            teamId = teamId,
            accountId = accountId,
            taId = taId,
            campaignId = CampaignId(createAndStartCampaignData.createCampaign.id),
            parentId = createAndStartCampaignData.addStep.step_id
          )
        }

        campaign_step_details: CampaignStep <- {

          val campaign_step = CampaignStep(
            id = createAndStartCampaignData.addStep.step_id,
            label = createAndStartCampaignData.addStep.label,
            campaign_id = createAndStartCampaignData.addStep.campaign_id,
            delay = 84000,
            step_type = createAndStartCampaignData.addStep.step_data.step_type,
            created_at = DateTime.now()
          )


          Future.successful((campaign_step))

        }

        result: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
          emailChannelData = EmailChannelData(
            emailSettingId = initialData.emailSetting.get.id.get.emailSettingId),
          teamId = initialData.emailSetting.get.team_id
        )


        delete_head_step: Option[Long] <- {

          Future.fromTry(campaignService.deleteVariant(
            stepId = createAndStartCampaignData.addStep.step_id,
            campaign = createAndStartCampaignData.campaign,
            loggedinAccount = initialData.account,
            campaignId = createAndStartCampaignData.campaign.id,
            step = campaign_step_details,
            variantId = createAndStartCampaignData.addStep.id.toInt,
            teamId = teamId,
          ))

        }


      } yield {

        createAndStartCampaignData.addStep

      }

      scheduleTaskData
        .map(data => {

          println(s"data -> ${data}")


          campaignStepDAO.find(
            stepId = data.step_id,
            campaignId = data.campaign_id
          ) match {

            case None =>

              // correct we have deleted step so it can't be found
              print("step not found")

              assert(true)


            case Some(ste) =>
              println(s"err  above -> ${ste}")
              assert(false)

            // this can't be possible.


          }


          //          assert(data.isDefined)
        })
        .recover(err => {
          println(s"err -> ${err}")
          assert(false)
        })


    }



    /*
           20-apr-2024 : middle step deletion with prospect scheduled ( at 2nd step should go to 1st step )
    */
    it("should delete middle step of campaign ( 2nd step here )  prospects at 2nd step") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData(
        max_emails_per_prospect_per_day = 100,
        max_emails_per_prospect_per_week = 500,
        max_emails_per_prospect_account_per_day = Option(100),
        max_emails_per_prospect_account_per_week = Option(500)
      ).get
      val account: Account = initialData.account
      val emailSetting: EmailSetting = initialData.emailSetting.get
      val orgId: OrgId = OrgId(account.org.id)
      val accountId: AccountId = AccountId(account.internal_id)
      val teamId: TeamId = TeamId(account.teams.head.team_id)
      val taId: Long = account.teams.head.access_members.head.ta_id

      //      val emailSettingId = EmailSettingId(emailSetting.id.get.emailSettingId)

      val scheduleTaskData = for {
        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 1
        )

        result: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
          emailChannelData = EmailChannelData(
            emailSettingId = initialData.emailSetting.get.id.get.emailSettingId),
          teamId = initialData.emailSetting.get.team_id
        )


        addStep_2: CampaignStepVariant <- {
          CreateStepForCampaignFixture.createAutoEmailStepForCampaign(
            orgId = orgId,
            teamId = teamId,
            accountId = accountId,
            taId = taId,
            campaignId = CampaignId(createAndStartCampaignData.createCampaign.id),
            parentId = createAndStartCampaignData.addStep.step_id
          )
        }

        campaign_step_details: CampaignStep <- {

          val campaign_step = CampaignStep(
            id = addStep_2.step_id,
            label = addStep_2.label,
            campaign_id = createAndStartCampaignData.addStep.campaign_id,
            delay = 84000,
            step_type = addStep_2.step_data.step_type,
            created_at = DateTime.now()
          )


          Future.successful((campaign_step))

        }

        scheduling_for_second_time: Int <- {

          ReSchedulingFixture.initializeDataForReScheduling(
            orgId = orgId,
            accountId = accountId,
            teamId = teamId,
            emailSetting = initialData.emailSetting.get,
            campaign = createAndStartCampaignData.campaign,
            prospectList = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id))
          )

        }

        result_2: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
          emailChannelData = EmailChannelData(
            emailSettingId = initialData.emailSetting.get.id.get.emailSettingId),
          teamId = initialData.emailSetting.get.team_id
        )

        addStep_3: CampaignStepVariant <- {
          CreateStepForCampaignFixture.createAutoEmailStepForCampaign(
            orgId = orgId,
            teamId = teamId,
            accountId = accountId,
            taId = taId,
            campaignId = CampaignId(createAndStartCampaignData.createCampaign.id),
            parentId = campaign_step_details.id
          )
        }


        delete_step_2: Option[Long] <- {

          Future.fromTry(campaignService.deleteVariant(
            stepId = addStep_2.step_id,
            campaign = createAndStartCampaignData.campaign,
            loggedinAccount = initialData.account,
            campaignId = createAndStartCampaignData.campaign.id,
            step = campaign_step_details,
            variantId = addStep_2.id.toInt,
            teamId = teamId,
          ))

        }


      } yield {

        println(s"result 2 : ${result_2}")

        addStep_2

      }

      scheduleTaskData
        .map(data => {

          println(s"data -> ${data}")


          campaignStepDAO.find(
            stepId = data.step_id,
            campaignId = data.campaign_id
          ) match {

            case None =>

              // correct we have deleted step so it can't be found
              print("step not found")

              assert(true)


            case Some(ste) =>

              assert(false)

            // this can't be possible.


          }


          //          assert(data.isDefined)
        })
        .recover(err => {
          println(s"err -> ${err}")
          assert(false)
        })


    }

    /*
           20-apr-2024 : middle step deletion with prospect scheduled ( at 2nd step should go to 1st step )
    */
    it("should delete last step of campaign ( 3nd step here )  prospects at 3nd step") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData(
        max_emails_per_prospect_per_day = 100,
        max_emails_per_prospect_per_week = 500,
        max_emails_per_prospect_account_per_day = Option(100),
        max_emails_per_prospect_account_per_week = Option(500),
      ).get
      val account: Account = initialData.account
      val emailSetting: EmailSetting = initialData.emailSetting.get
      val orgId: OrgId = OrgId(account.org.id)
      val accountId: AccountId = AccountId(account.internal_id)
      val teamId: TeamId = TeamId(account.teams.head.team_id)
      val taId: Long = account.teams.head.access_members.head.ta_id

      //      val emailSettingId = EmailSettingId(emailSetting.id.get.emailSettingId)

      val scheduleTaskData = for {
        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 1
        )


        scheduling_for_second_time: Int <- {

          ReSchedulingFixture.initializeDataForReScheduling(
            orgId = orgId,
            accountId = accountId,
            teamId = teamId,
            emailSetting = emailSetting,
            campaign = createAndStartCampaignData.campaign,
            prospectList = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id))
          )

        }

        result: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
          emailChannelData = EmailChannelData(
            emailSettingId = initialData.emailSetting.get.id.get.emailSettingId),
          teamId = initialData.emailSetting.get.team_id
        )


        addStep_2: CampaignStepVariant <- {
          CreateStepForCampaignFixture.createAutoEmailStepForCampaign(
            orgId = orgId,
            teamId = teamId,
            accountId = accountId,
            taId = taId,
            campaignId = CampaignId(createAndStartCampaignData.createCampaign.id),
            parentId = createAndStartCampaignData.addStep.step_id
          )
        }

        campaign_step_details: CampaignStep <- {

          val campaign_step = CampaignStep(
            id = addStep_2.step_id,
            label = addStep_2.label,
            campaign_id = createAndStartCampaignData.addStep.campaign_id,
            delay = 84000,
            step_type = addStep_2.step_data.step_type,
            created_at = DateTime.now()
          )


          Future.successful((campaign_step))

        }

        scheduling_for_second_time: Int <- {

          ReSchedulingFixture.initializeDataForReScheduling(
            orgId = orgId,
            accountId = accountId,
            teamId = teamId,
            emailSetting = initialData.emailSetting.get,
            campaign = createAndStartCampaignData.campaign,
            prospectList = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id))
          )

        }

        result_2: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
          emailChannelData = EmailChannelData(
            emailSettingId = initialData.emailSetting.get.id.get.emailSettingId),
          teamId = initialData.emailSetting.get.team_id
        )

        addStep_3: CampaignStepVariant <- {
          CreateStepForCampaignFixture.createAutoEmailStepForCampaign(
            orgId = orgId,
            teamId = teamId,
            accountId = accountId,
            taId = taId,
            campaignId = CampaignId(createAndStartCampaignData.createCampaign.id),
            parentId = campaign_step_details.id
          )
        }

        campaign_step_details_2: CampaignStep <- {

          val campaign_step = CampaignStep(
            id = addStep_3.step_id,
            label = addStep_3.label,
            campaign_id = createAndStartCampaignData.campaign.id,
            delay = 0,
            step_type = addStep_3.step_data.step_type,
            created_at = DateTime.now()
          )


          Future.successful((campaign_step))

        }

        scheduling_for_third_time: Int <- {

          ReSchedulingFixture.initializeDataForReScheduling(
            orgId = orgId,
            accountId = accountId,
            teamId = teamId,
            emailSetting = initialData.emailSetting.get,
            campaign = createAndStartCampaignData.campaign,
            prospectList = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id))
          )

        }

        result_3: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
          emailChannelData = EmailChannelData(
            emailSettingId = initialData.emailSetting.get.id.get.emailSettingId),
          teamId = initialData.emailSetting.get.team_id
        )

        delete_step_2: Option[Long] <- {

          Future.fromTry(campaignService.deleteVariant(
            stepId = addStep_3.step_id,
            campaign = createAndStartCampaignData.campaign,
            loggedinAccount = initialData.account,
            campaignId = createAndStartCampaignData.campaign.id,
            step = campaign_step_details_2,
            variantId = addStep_3.id.toInt,
            teamId = teamId,
          ))

        }


      } yield {

        println(s"result 2 : ${result_2}")

        addStep_3

      }


      scheduleTaskData
        .map(data => {

          println(s"data -> ${data}")


          campaignStepDAO.find(
            stepId = data.step_id,
            campaignId = data.campaign_id
          ) match {

            case None =>

              // correct we have deleted step so it can't be found
              print("step not found")

              assert(true)


            case Some(ste) =>

              assert(false)

            // this can't be possible.


          }


          //          assert(data.isDefined)
        })
        .recover(err => {
          println(s"err -> ${err}")
          assert(false)
        })


    }

    it("should create a new campaign and next_to_be_scheduled_at should be null") {


      val campaign = for {
        initialData: InitialData <- Future.fromTry(NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData())

        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 1
        )
      } yield {
        createAndStartCampaignData
      }


      campaign.flatMap { c => {

        for {
          result1 <- Future.fromTry(campaignDAO.checkIfLastSentEmailAtIsNull(
            campaignId = CampaignId(id = c.campaign.id),
            teamId = TeamId(c.campaign.team_id)
          ))
          result2 <- Future.fromTry(CampaignTestDAO.checkIfNextToBeScheduledAtIsNull(
            cid = CampaignId(id = c.campaign.id),
            teamId = TeamId(c.campaign.team_id)
          ))
        } yield {
          assert(result1 && result2)
        }
      }
      }.recover { e =>
        Logger.info(s"Error Occurred ${e.getMessage}")
        assert(false)
      }
    }


    it("Last email sent should update on email sent") {


      val result = for {
        initialData: InitialData <- Future.fromTry(NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData())

        createCampaign: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 1
        )

        addingEmailScheduled: Seq[EmailScheduledNewAfterSaving] <- Future.fromTry {
          emailScheduledDAOService.saveEmailsToBeScheduledAndUpdateCampaignDataV2(
            emailsToBeScheduled = Vector(EmailScheduledNewFixture.generateEmailScheduledNew3.copy(
              campaign_id = Some(createCampaign.campaign.id),
              step_id = createCampaign.campaign.head_step_id,
              from_email = createCampaign.campaign.settings.campaign_email_settings.head.sender_email,
              scheduled_from_campaign = true,
              is_opening_step = true,
              sender_email_settings_id = createCampaign.campaign.settings.campaign_email_settings.head.sender_email_setting_id.emailSettingId,
              team_id = createCampaign.campaign.team_id,
              account_id = createCampaign.campaign.account_id,
              receiver_email_settings_id = createCampaign.campaign.settings.campaign_email_settings.head.receiver_email_setting_id.emailSettingId,
              campaign_email_settings_id = createCampaign.campaign.settings.campaign_email_settings.head.id,
              prospect_id = Some(createCampaign.addProspect.head.id),
              base_body = "body"

            )
            ),
            campaign_email_setting_id = createCampaign.campaign.settings.campaign_email_settings.head.id,
            emailSendingFlow = None,
            Logger = Logger
          )
        }

        emailSent <- Future.fromTry {

          emailSenderService.onEmailSent(
            emailSentId = addingEmailScheduled.head.email_scheduled_id,
            data = DefaultParametersFixtureForInitializingDataForReScheduling.defaultEmailToBeSent(
              emailSetting = initialData.emailSetting.get,
              campaign = createCampaign.campaign
            ),
            accountId = createCampaign.campaign.account_id,
            sendEmailFromCampaignDetails = Some(SendEmailFromCampaignDetails(
              campaign_id = createCampaign.campaign.id,
              campaign_name = createCampaign.campaign.name,
              // stepDetails can be none when email is being sent manually from Inbox.
              // Example: sendNewEmailManually in InboxV3Service
              stepDetails = Some(StepDetails(
                step_id = createCampaign.addStep.step_id,
                step_name = createCampaign.addStep.label.getOrElse("step"),
                step_type = createCampaign.addStep.step_data.step_type
              ))
            )),
            prospectIdInCampaign = createCampaign.addProspect.headOption.map(_.id),
            currentBillingCycleStartedAt = DateTime.now(),
            orgId = initialData.account.org.id,
            repTrackingHostId = 1,
            teamId = createCampaign.campaign.team_id
          )
        }


        isLastEmailSentNull: Boolean <- Future.fromTry {

          campaignDAO.checkIfLastSentEmailAtIsNull(
            campaignId = CampaignId(id = createCampaign.campaign.id),
            teamId = TeamId(createCampaign.campaign.team_id)
          ) match {
            case Failure(exception) => {
              Failure(exception)
            }
            case Success(value) => {
              Success(value)
            }
          }

        }


      } yield {
        isLastEmailSentNull
      }

      result.map { isMailSentNull => {

        assert(!isMailSentNull)
      }
      }.recover { e => {
        println(s"Error Last Email Sent ${e.printStackTrace()}")
        assert(false)
      }
      }


    }

    it("should return task Count along with the other campaign stats") {

      val result = for {
        initialData: InitialData <- Future.fromTry(NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData())

        campaign: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 4
        )

        createdTask: String <- taskDAO.createNewTask(
          task_data = NewTask(
            campaign_id = Some(campaign.campaign.id),
            campaign_name = Some(campaign.campaign.name),
            step_id = None,
            step_label = None,
            created_via = TaskCreatedVia.Manual,
            is_opening_step = None,
            task_type = TaskType.SendEmail,
            is_auto_task = false,
            task_data = TaskData.SendEmailData(
              subject = "this is subject",
              body = "this is body",
              email_message_id = None,
            ),
            status = TaskStatus.Due(due_at = DateTime.now().minusDays(1)),
            assignee_id = Some(campaign.campaign.account_id),
            prospect_id = Some(campaign.addProspect.head.id),
            priority = TaskPriority.Low,
            note = None,
          ),
          taskId = "task_test_campaign_stats_service_call",
          account_id = campaign.campaign.account_id,
          team_id = campaign.campaign.team_id,
          created_at = DateTime.now()
        )

        campaignStats <- campaignService.getCampaignStatsWithTaskCount(
          teamId = TeamId(initialData.account.teams.head.team_id),
          orgId = OrgId(initialData.account.org.id),
          campaignId = CampaignId(campaign.campaign.id),
          campaignStatus = campaign.campaign.status,
          campaignCreatedAt = campaign.campaign.created_at,
          acc = initialData.account,
          permittedIds = Seq(campaign.campaign.account_id)
        )(campaignCacheService = campaignCacheService, logger = Logger, ec = executionContext)


      } yield {
        campaignStats
      }

      result.map { campaignStats =>
        println(s"campaign stats: $campaignStats")
        assert(campaignStats.task_count.due.all == 1)

      }.recover { e =>
        println(e)
        assert(false)
      }

    }

    it("should duplicate drip campaign") {
      val initialData: InitialData = SRSetupAndDeleteFixtures.createInitialData(true).get
      val account: Account = initialData.account
      val teamId: TeamId = TeamId(account.teams.head.team_id)
      val data = for {
        start_campaign_data <- CampaignUtils.createAndStartDripCampaign(
          initialData = initialData,
          designDripForCase = 15,
        )
        first_campaign <- Future {
          campaignService.findCampaignForCampaignUtilsOnly(
            id = start_campaign_data.campaignId.id,
            teamId = teamId
          )
        }
        first_drip_campaign_data <- Future.fromTry {
          campaignDAO.getDripCampaignData(
            campaignId = CampaignId(first_campaign.get.id),
            teamId = TeamId(first_campaign.get.team_id)
          )
        }
        second_drip_campaign_data <- campaignService.duplicateCampaign(
          ta = account.teams.head.access_members.find(_.user_id == account.internal_id).get,
          loggedinAccount = account,
          campaign = first_campaign.get,
          permittedAccountIds = account.teams.head.access_members.map(_.user_id),
          version = "v3"
        ).map {
          case Left(value) => Left(value)
          case Right(value) =>
            campaignDAO.getDripCampaignData(
              campaignId = CampaignId(value.id),
              teamId = TeamId(value.team_id)
            ) match {
              case Failure(exception) =>
                Left(DuplicateCampaignError.DbFailure(exception))
              case Success(value) => Right(value)
            }
        }
      } yield (first_drip_campaign_data, second_drip_campaign_data)

      val result = Await.result(data, scala.concurrent.duration.Duration.Inf)
      println(s"result ----- $result")
      assert(result._2.toOption.get.isDefined)
      assert(result._2.toOption.get.get.edges.length == result._1.get.edges.length)
      assert(result._2.toOption.get.get.nodes.length == result._1.get.nodes.length)

    }


    it("should success Testing query campaignService.getCampaignsForIndependentStepSchedulerMQ") {


      val res = campaignService.getCampaignsForIndependentStepSchedulerMQ()

      res match {

        case Failure(exception) =>

          println(s"error -> ${exception}")
          assert(false)

        case Success(value) =>

          //          println(value)
          assert(value.nonEmpty)
          assert(true)

      }

    }

  }


  describe("Test setProspectsToResumeLater") {
    
    it("should set prospects to resume later") {

      val initialData: InitialData =
        NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val account: Account = initialData.account

      val emailSetting: EmailSetting = initialData.emailSetting.get

      val orgId: OrgId = OrgId(id = account.org.id)

      val accountId: AccountId = AccountId(id = account.internal_id)

      val teamId: TeamId = TeamId(id = account.teams.head.team_id)

      val taId: Long = account.teams.head.access_members.head.ta_id

      val willResumeAt = DateTime.now.plusDays(15)

      val prospectObjListFut: Future[Seq[ProspectObject]] = for {

        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 1
        )

        cpTuple: Seq[CPTuple] = createAndStartCampaignData.addProspect.map { p =>

          CPTuple(
            prospect_id = p.id,
            campaign_id = createAndStartCampaignData.campaign.id
          )

        }

        updatedProspects: Seq[Long] <- Future.fromTry {

          campaignService.setProspectsToResumeLater(
            willResumeAt = willResumeAt,
            willResumeAtTimezone = willResumeAt.getZone.getID,
            campaignProspects = cpTuple,
            permittedAccountIdsForProspects = Seq(accountId.id),
            willResumeAtUpdatedBy = WillResumeAtUpdatedBy.User,
            teamId = teamId.id,
            logger = Logger,
          )

        }

        updatedProspectObjList: Seq[ProspectObject] <- Future.fromTry {

          prospectDAOService.findProspects(
            byProspectIds = updatedProspects,
            teamId = teamId.id,
            org_id = orgId,
            byCampaignId = Some(createAndStartCampaignData.campaign.id),
            findActiveCampaignsIfInternalRequest = true
          )

        }

      } yield {

        assert(updatedProspectObjList.map(_.id).toSet == cpTuple.map(_.prospect_id).toSet)

        updatedProspectObjList

      }

      prospectObjListFut.map { prospectObjList =>

        val campaignProspectDataList = prospectObjList.flatMap { p =>

          p.internal.active_campaigns.map { active_campaign_seq =>

            println(s"active_campaign_seq: $active_campaign_seq")


            val campaigns_data_js: JsLookupResult = active_campaign_seq.head

            campaigns_data_js.as[CampaignProspectData]

          }

        }

        println(s"campaignProspectDataList: $campaignProspectDataList")

        val isValid = campaignProspectDataList.forall { cp =>

          cp.will_resume_at.contains(willResumeAt) &&
            cp.will_resume_at_tz.contains(willResumeAt.getZone.getID)

        }

        assert(
          isValid &&
            prospectObjList.size == campaignProspectDataList.size
        )

      }.recover { err =>

        println(s"Failed to update resume at ${LogHelpers.getStackTraceAsString(err)}")

        assert(false)

      }

    }

  }
  
}
