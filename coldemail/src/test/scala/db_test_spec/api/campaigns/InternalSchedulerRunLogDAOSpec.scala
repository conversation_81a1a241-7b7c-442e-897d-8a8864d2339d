package db_test_spec.api.campaigns

import api.accounts.TeamId
import api.campaigns.models.{InternalSchedulerRunLog, InternalSchedulerRunLogData}
import db_test_spec.api.accounts.fixtures.NewAccountAndEmailSettingData
import db_test_spec.api.{DbTestingBeforeAllAndAfterAll, InitialData}
import org.joda.time.DateTime
import utils.mq.channel_scheduler.channels.model.SchedulerSteps

class InternalSchedulerRunLogDAOSpec extends DbTestingBeforeAllAndAfterAll {

  lazy val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
  

  describe("addInternalSchedulerRunLog") {

    it("should give success") {
      val result = internalSchedulerRunLogDAO.addInternalSchedulerRunLog(
        internalSchedulerRunLog = InternalSchedulerRunLog(
          team_id = initialData.emailSetting.get.team_id,
          internal_scheduler_run_log_data = InternalSchedulerRunLogData.EmailChannelSchedulerLog(
            email_setting_id = initialData.emailSetting.get.id.get
          ),
          started_at = DateTime.now(),
          completed_at = DateTime.now(),
          saved_tasks_count = 0,
          reached_scheduler_step = SchedulerSteps.NoCampaignsFoundAfterLock,
          log_trace_id =Logger.logTraceId
        )
        )
          assert (result.isSuccess)
          assert (result.get == 1)
    }
  }

  describe("updatePrevEmailUseStatus") {
    it("should give success") {

      val internalSchedulerRunLog = InternalSchedulerRunLog(

        team_id = TeamId(id = initialData.account.teams.head.team_id),

        internal_scheduler_run_log_data = InternalSchedulerRunLogData.EmailChannelSchedulerLog(
          email_setting_id = initialData.emailSetting.get.id.get
        ),

        started_at = DateTime.now(),
        completed_at = DateTime.now().plusMinutes(2),
        saved_tasks_count = 2,
        reached_scheduler_step = SchedulerSteps.PostSchedulingSuccess,
        log_trace_id = "Test_log_trace_id"
      )

      val result = internalSchedulerRunLogDAO.addInternalSchedulerRunLog(
        internalSchedulerRunLog = internalSchedulerRunLog
      )

      assert(result.isSuccess)
      assert(result.get == 1)
    }

  }


  describe("getInternalSchedulerRunLogForLast24Hours") {

    it("should give success") {
      val result = internalSchedulerRunLogDAO.getInternalSchedulerRunLogForLast24Hours

      assert(result.isSuccess)
      assert(result.get.nonEmpty)
      assert(result.get.head.team_id == initialData.emailSetting.get.team_id)
      assert(result.get.head.email_setting_id == initialData.emailSetting.get.id.get)
    }


  }

}
