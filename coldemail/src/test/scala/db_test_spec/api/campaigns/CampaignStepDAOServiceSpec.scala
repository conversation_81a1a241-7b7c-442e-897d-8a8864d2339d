package db_test_spec.api.campaigns

import api.accounts.TeamId
import api.accounts.models.{AccountId, OrgId}
import api.campaigns.CampaignStepVariantForScheduling
import api.campaigns.models.CampaignStepData.getSubjectAndBodyFromStepData
import api.campaigns.models.{CampaignStepId, CampaignStepType}
import api.campaigns.services.CampaignId
import db_test_spec.api.DbTestingBeforeAllAndAfterAll
import db_test_spec.api.accounts.fixtures.NewAccountAndEmailSettingData
import db_test_spec.api.campaigns.test_utils.{CampaignUtils, StepTypeWithPrevSubMergeTag}
import scalikejdbc.DBSession
import utils.helpers.LogHelpers

import scala.util.{Failure, Success}

class CampaignStepDAOServiceSpec extends DbTestingBeforeAllAndAfterAll {

  describe("Test swapStepSubjects") {

    it("should return failure if any step has number variants not equal to 1") {

      val numOfCampaignSteps = 6

      val stepsTypes = List.fill(numOfCampaignSteps)(
        StepTypeWithPrevSubMergeTag(
          stepType = CampaignStepType.AutoEmailStep,
          shouldHavePrevSubMergeTag = false
        )
      )

      val numOfCampaignStepVariants = 4

      val initialData =
        NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val account = initialData.account

      val accountId = AccountId(id = account.internal_id)

      val teamId: TeamId = TeamId(id = account.teams.head.team_id)

      val orgId = OrgId(id = account.org.id)

      val taId = account.teams.head.access_members.find { t =>

        t.user_id == account.internal_id

      }.get.ta_id

      CampaignUtils.createNotStartedCampaignWithSteps(
        orgId = orgId,
        accountId = accountId,
        teamId = teamId,
        taId = taId,
        numberOfVariants = numOfCampaignStepVariants,
        emailSettingId = initialData.emailSetting.get.id.get,
        stepTypes = stepsTypes,
        ownerFirstName = account.first_name.get
      ).map { campaign =>

        val stepsBeforeReorder = campaignStepService.findStepsByCampaign(campaign = campaign).get

        val dbAndSession = dbUtils.startLocalTx()

        implicit val session: DBSession = dbAndSession.session

        val step1 = stepsBeforeReorder.head

        val step2 = stepsBeforeReorder(1)

        val stepInsertCountRes = campaignStepDAOService.swapStepSubjects(
          campaignId = CampaignId(id = campaign.id),
          campaignStepId1 = CampaignStepId(id = step1.id),
          s1variants = step1.variants.map(CampaignStepVariantForScheduling.toCampaignStepVariant),
          campaignStepId2 = CampaignStepId(id = step2.id),
          s2variants = step2.variants.map(CampaignStepVariantForScheduling.toCampaignStepVariant),
        )

        dbUtils.commitAndCloseSession(db = dbAndSession.db)

        stepInsertCountRes match {

          case Failure(_) =>

            val stepsAfterReorder = campaignStepService.findStepsByCampaign(campaign = campaign).get

            assert(stepsAfterReorder == stepsBeforeReorder)

          case Success(_) =>

            assert(false)

        }

      }.recover { case e =>

        println(s"ERROR: $e")

        assert(false)

      }

    }

    it("should swap the subjects if both steps have only 1 variants") {

      val numOfCampaignSteps = 6

      val stepsTypes = List.fill(numOfCampaignSteps)(
        StepTypeWithPrevSubMergeTag(
          stepType = CampaignStepType.AutoEmailStep,
          shouldHavePrevSubMergeTag = false
        )
      )

      val numOfCampaignStepVariants = 1

      val initialData =
        NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val account = initialData.account

      val accountId = AccountId(id = account.internal_id)

      val teamId: TeamId = TeamId(id = account.teams.head.team_id)

      val orgId = OrgId(id = account.org.id)

      val taId = account.teams.head.access_members.find { t =>

        t.user_id == account.internal_id

      }.get.ta_id

      CampaignUtils.createNotStartedCampaignWithSteps(
        orgId = orgId,
        accountId = accountId,
        teamId = teamId,
        taId = taId,
        numberOfVariants = numOfCampaignStepVariants,
        emailSettingId = initialData.emailSetting.get.id.get,
        stepTypes = stepsTypes,
        ownerFirstName = account.first_name.get
      ).map { campaign =>

        val stepsBeforeReorder = campaignStepService.findStepsByCampaign(campaign = campaign).get

        val dbAndSession = dbUtils.startLocalTx()

        implicit val session: DBSession = dbAndSession.session

        val step1Before = stepsBeforeReorder.head

        val step2Before = stepsBeforeReorder(1)

        val stepInsertCountRes = campaignStepDAOService.swapStepSubjects(
          campaignId = CampaignId(id = campaign.id),
          campaignStepId1 = CampaignStepId(id = step1Before.id),
          s1variants = step1Before.variants.map(CampaignStepVariantForScheduling.toCampaignStepVariant),
          campaignStepId2 = CampaignStepId(id = step2Before.id),
          s2variants = step2Before.variants.map(CampaignStepVariantForScheduling.toCampaignStepVariant),
        )

        dbUtils.commitAndCloseSession(db = dbAndSession.db)

        stepInsertCountRes match {

          case Failure(_) =>

            assert(false)

          case Success(_) =>

            val stepsAfterReorder = campaignStepService.findStepsByCampaign(campaign = campaign).get

            val step1After = stepsAfterReorder.head

            val step2After = stepsAfterReorder(1)

            val step1BeforeSubject = getSubjectAndBodyFromStepData(step1Before.variants.head.step_data).subject
            val step2BeforeSubject = getSubjectAndBodyFromStepData(step2Before.variants.head.step_data).subject

            val step1AfterSubject = getSubjectAndBodyFromStepData(step1After.variants.head.step_data).subject
            val step2AfterSubject = getSubjectAndBodyFromStepData(step2After.variants.head.step_data).subject

            assert(
              step1BeforeSubject == step2AfterSubject &&
                step2BeforeSubject == step1AfterSubject
            )

        }

      }.recover { e =>

        println(LogHelpers.getStackTraceAsString(e))

        assert(false)

      }

    }

  }

  describe("Test reorderCampaignStepsAndUpdateLabels") {

    it("should not affect current campaign steps if the updateHeadStep query fails because of invalid campaign id.") {

      val numOfCampaignSteps = 6

      val stepsTypes = List.fill(numOfCampaignSteps)(
        StepTypeWithPrevSubMergeTag(
          stepType = CampaignStepType.AutoEmailStep,
          shouldHavePrevSubMergeTag = false
        )
      )

      val initialData =
        NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val account = initialData.account

      val accountId = AccountId(id = account.internal_id)

      val teamId: TeamId = TeamId(id = account.teams.head.team_id)

      val orgId = OrgId(id = account.org.id)

      val taId = account.teams.head.access_members.find { t =>

        t.user_id == account.internal_id

      }.get.ta_id

      CampaignUtils.createNotStartedCampaignWithSteps(
        orgId = orgId,
        accountId = accountId,
        teamId = teamId,
        taId = taId,
        numberOfVariants = 2,
        emailSettingId = initialData.emailSetting.get.id.get,
        stepTypes = stepsTypes,
        ownerFirstName = account.first_name.get
      ).map { campaign =>

        val stepsBeforeReorder = campaignStepService.findStepsByCampaign(campaign = campaign).get

        val invalidCampaignId = CampaignId(id = ********) // so that the update head step call will fail.

        val stepInsertCountRes = campaignStepDAOService.reorderCampaignStepsAndUpdateLabels(
          campaignId = invalidCampaignId,
          teamId = TeamId(id = campaign.team_id),
          newCampaignHeadStepId = CampaignStepId(id = stepsBeforeReorder.head.id),
          newCampaignStepsOrderTail = stepsBeforeReorder.tail.map(s => CampaignStepId(id = s.id)),
          prevCampaignHeadStepId = CampaignStepId(id = stepsBeforeReorder.head.id),
          newHeadStepVariants = Seq(),
          prevHeadStepVariants = Seq(),
          headStepVariantsHavePrevSubMergeTag = false,
        )

        stepInsertCountRes match {

          case Failure(_) =>

            val stepsAfterReorder = campaignStepService.findStepsByCampaign(campaign = campaign).get

            assert(stepsAfterReorder == stepsBeforeReorder)

          case Success(_) =>

            assert(false)

        }

      }.recover { e =>

        println(LogHelpers.getStackTraceAsString(e))

        assert(false)

      }

    }

    it("should not affect current campaign steps if the updateHeadStep query fails because of invalid head step id.") {

      val numOfCampaignSteps = 5

      val stepsTypes = List.fill(numOfCampaignSteps)(
        StepTypeWithPrevSubMergeTag(
          stepType = CampaignStepType.AutoEmailStep,
          shouldHavePrevSubMergeTag = false
        )
      )

      val initialData =
        NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val account = initialData.account

      val accountId = AccountId(id = account.internal_id)

      val teamId: TeamId = TeamId(id = account.teams.head.team_id)

      val orgId = OrgId(id = account.org.id)

      val taId = account.teams.head.access_members.find { t =>

        t.user_id == account.internal_id

      }.get.ta_id

      CampaignUtils.createNotStartedCampaignWithSteps(
        orgId = orgId,
        accountId = accountId,
        teamId = teamId,
        taId = taId,
        emailSettingId = initialData.emailSetting.get.id.get,
        stepTypes = stepsTypes,
        ownerFirstName = account.first_name.get
      ).map { campaign =>

        val stepsBeforeReorder = campaignStepService.findStepsByCampaign(campaign = campaign).get

        val invalidHeadStepId = CampaignStepId(id = *********) //so that the update head step call will fail.

        val stepInsertCountRes = campaignStepDAOService.reorderCampaignStepsAndUpdateLabels(
          campaignId = CampaignId(id = campaign.id),
          teamId = TeamId(id = campaign.team_id),
          newCampaignHeadStepId = invalidHeadStepId,
          newCampaignStepsOrderTail = stepsBeforeReorder.tail.map(s => CampaignStepId(id = s.id)),
          prevCampaignHeadStepId = CampaignStepId(id = stepsBeforeReorder.head.id),
          newHeadStepVariants = Seq(),
          prevHeadStepVariants = Seq(),
          headStepVariantsHavePrevSubMergeTag = false,
        )

        stepInsertCountRes match {

          case Failure(exception) =>

            val stepsAfterReorder = campaignStepService.findStepsByCampaign(campaign = campaign).get

            assert(
              exception.isInstanceOf[org.postgresql.util.PSQLException] &&
                stepsAfterReorder == stepsBeforeReorder
            )

          case Success(_) =>

            assert(false)

        }

      }.recover { e =>

        println(LogHelpers.getStackTraceAsString(e))

        assert(false)

      }

    }

    it("should reorder the steps correctly, when correct head step and order is provided") {

      val numOfCampaignSteps = 9

      val stepsTypes = List.fill(numOfCampaignSteps)(
        StepTypeWithPrevSubMergeTag(
          stepType = CampaignStepType.AutoEmailStep,
          shouldHavePrevSubMergeTag = false
        )
      )

      val initialData =
        NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val account = initialData.account

      val accountId = AccountId(id = account.internal_id)

      val teamId: TeamId = TeamId(id = account.teams.head.team_id)

      val orgId = OrgId(id = account.org.id)

      val taId = account.teams.head.access_members.find { t =>

        t.user_id == account.internal_id

      }.get.ta_id

      CampaignUtils.createNotStartedCampaignWithSteps(
        orgId = orgId,
        accountId = accountId,
        teamId = teamId,
        taId = taId,
        emailSettingId = initialData.emailSetting.get.id.get,
        stepTypes = stepsTypes,
        ownerFirstName = account.first_name.get
      ).map { campaign =>


        val stepsBeforeReorder = campaignStepService.findStepsByCampaign(campaign = campaign).get

        // move head step to last
        val currHeadStep = stepsBeforeReorder.head

        val newOrder = stepsBeforeReorder.tail :+ currHeadStep

        val stepInsertCountRes = campaignStepDAOService.reorderCampaignStepsAndUpdateLabels(
          campaignId = CampaignId(id = campaign.id),
          teamId = TeamId(id = campaign.team_id),
          newCampaignHeadStepId = CampaignStepId(id = newOrder.head.id),
          newCampaignStepsOrderTail = newOrder.tail.map(s => CampaignStepId(id = s.id)),
          prevCampaignHeadStepId = CampaignStepId(id = stepsBeforeReorder.head.id),
          newHeadStepVariants = Seq(),
          prevHeadStepVariants = Seq(),
          headStepVariantsHavePrevSubMergeTag = false,
        )

        stepInsertCountRes match {

          case Failure(exception) =>

            println(exception)

            assert(
              false
            )

          case Success(_) =>

            val headStepUpdatedCampaign = campaignDAO.findCampaignForCampaignUtilsOnly(
              id = campaign.id,
              teamId = TeamId(id = campaign.team_id)
            ).get

            val stepsAfterReorder = campaignStepService.findStepsByCampaign(campaign = headStepUpdatedCampaign).get

            assert(stepsAfterReorder.map(_.id) == newOrder.map(_.id))

        }

      }.recover { e =>

        println(LogHelpers.getStackTraceAsString(e))

        assert(false)

      }

    }

    it(
      "should reorder the steps correctly and swap subjects, if reordering head step with {{previous_subject}} merge tag and steps have only 1 variant"
    ) {

      val numOfCampaignSteps = 9

      val stepsTypes = List.fill(numOfCampaignSteps - 1)(
        StepTypeWithPrevSubMergeTag(
          stepType = CampaignStepType.AutoEmailStep,
          shouldHavePrevSubMergeTag = false
        )
      ) ++ List(
        StepTypeWithPrevSubMergeTag(
          stepType = CampaignStepType.AutoEmailStep,
          shouldHavePrevSubMergeTag = true
        )
      )

      val numOfCampaignStepVariants = 1

      val initialData =
        NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val account = initialData.account

      val accountId = AccountId(id = account.internal_id)

      val teamId: TeamId = TeamId(id = account.teams.head.team_id)

      val orgId = OrgId(id = account.org.id)

      val taId = account.teams.head.access_members.find { t =>

        t.user_id == account.internal_id

      }.get.ta_id

      CampaignUtils.createNotStartedCampaignWithSteps(
        orgId = orgId,
        accountId = accountId,
        teamId = teamId,
        taId = taId,
        numberOfVariants = numOfCampaignStepVariants,
        emailSettingId = initialData.emailSetting.get.id.get,
        stepTypes = stepsTypes,
        ownerFirstName = account.first_name.get
      ).map { campaign =>

        val stepsBeforeReorder = campaignStepService.findStepsByCampaign(campaign = campaign).get

        // move last step to head step
        val lastStep = stepsBeforeReorder.last

        val newOrder = lastStep +: stepsBeforeReorder.dropRight(1)

        val oldHeadStepBefore = stepsBeforeReorder.head

        val newHeadStepBefore = newOrder.head

        val stepInsertCountRes = campaignStepDAOService.reorderCampaignStepsAndUpdateLabels(
          campaignId = CampaignId(id = campaign.id),
          teamId = TeamId(id = campaign.team_id),
          newCampaignHeadStepId = CampaignStepId(id = newOrder.head.id),
          newCampaignStepsOrderTail = newOrder.tail.map(s => CampaignStepId(id = s.id)),
          prevCampaignHeadStepId = CampaignStepId(id = stepsBeforeReorder.head.id),
          newHeadStepVariants = newHeadStepBefore.variants.map(CampaignStepVariantForScheduling.toCampaignStepVariant),
          prevHeadStepVariants = oldHeadStepBefore.variants.map(CampaignStepVariantForScheduling.toCampaignStepVariant),
          headStepVariantsHavePrevSubMergeTag = true,
        )

        stepInsertCountRes match {

          case Failure(exception) =>

            println(exception)

            assert(
              false
            )

          case Success(_) =>

            val headStepUpdatedCampaign = campaignDAO.findCampaignForCampaignUtilsOnly(
              id = campaign.id,
              teamId = TeamId(id = campaign.team_id)
            ).get

            val stepsAfterReorder = campaignStepService.findStepsByCampaign(campaign = headStepUpdatedCampaign).get

            val oldHeadStepBeforeSubject = getSubjectAndBodyFromStepData(
              oldHeadStepBefore.variants.head.step_data
            ).subject
            val oldHeadStepBeforeBody = getSubjectAndBodyFromStepData(
              oldHeadStepBefore.variants.head.step_data
            ).body

            val newHeadStepBeforeSubject = getSubjectAndBodyFromStepData(
              newHeadStepBefore.variants.head.step_data
            ).subject
            val newHeadStepBeforeBody = getSubjectAndBodyFromStepData(
              newHeadStepBefore.variants.head.step_data
            ).body

            val oldHeadStepAfter = stepsAfterReorder.find(_.id == oldHeadStepBefore.id).get

            val newHeadStepAfter = stepsAfterReorder.head

            val oldHeadStepAfterSubject = getSubjectAndBodyFromStepData(
              oldHeadStepAfter.variants.head.step_data
            ).subject
            val oldHeadStepAfterBody = getSubjectAndBodyFromStepData(
              oldHeadStepAfter.variants.head.step_data
            ).body

            val newHeadStepAfterSubject = getSubjectAndBodyFromStepData(
              newHeadStepAfter.variants.head.step_data
            ).subject
            val newHeadStepAfterBody = getSubjectAndBodyFromStepData(
              newHeadStepAfter.variants.head.step_data
            ).body

            assert(
              oldHeadStepBeforeSubject == newHeadStepAfterSubject &&
                newHeadStepBeforeSubject == oldHeadStepAfterSubject &&
                oldHeadStepBeforeBody == oldHeadStepAfterBody &&
                newHeadStepBeforeBody == newHeadStepAfterBody
            )
        }

      }.recover { e =>

        println(LogHelpers.getStackTraceAsString(e))

        assert(false)

      }

    }

  }

}
