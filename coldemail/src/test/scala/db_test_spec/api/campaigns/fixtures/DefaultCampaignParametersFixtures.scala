package db_test_spec.api.campaigns.fixtures

import api.accounts.models.{AccountId, OrgId}
import api.accounts._
import api.campaigns.models.{CampaignEmailSettingsId, CampaignStepType, CampaignType, WhatsappSettingSenderDetails}
import api.campaigns.models.CampaignStepData.{AutoEmailStep, WhatsappMessageData}
import api.campaigns.services.CampaignId
import api.campaigns._
import api.emails.EmailSetting
import api.prospects.models.ProspectCategoryRank
import api.tasks.models.TaskPriority
import api.team.TeamUuid
import api.whatsapp.WhatsappSettingUuid
import api.whatsapp.models.WhatsappAccountSettings
import app.test_fixtures.accounts.OrgCountDataFixture
import db_test_spec.utils.SrRandomTestUtils
import io.smartreach.esp.api.emails.EmailSettingId
import io.sr.billing_common.models.{PlanID, PlanType}
import org.joda.time.DateTime
import sr_scheduler.models.CampaignEmailPriority
import sr_scheduler.models.CampaignEmailPriority.CampaignEmailPriority
import utils.featureflags.services.SrFeatureFlags

import scala.util.Random

object DefaultCampaignParametersFixtures {

  def defaultCampaignCreateForm: CampaignCreateForm = CampaignCreateForm(
    name = None,
    timezone = Some("Asia/Kolkata"),
    campaign_owner_id = Some(19L),
    campaign_type = CampaignType.MultiChannel,
  )

  def defaultCampaignSettings(
                              campaignEmailSettingsId: CampaignEmailSettingsId,
                              senderEmailSettingId: EmailSettingId,
                              receiverEmailSettingId: EmailSettingId,
                              teamId: TeamId,
                              daily_from_time: Int,
                              daily_till_time: Int,
                              email_priority: CampaignEmailPriority,
                              enable_email_validation: Boolean = false,
                              campaignEmailSettings: Option[List[CampaignEmailSettings]] = None
                            ): Option[CampaignSettings] = Some(
    CampaignSettings(
    // settings
      campaign_email_settings = if(campaignEmailSettings.isDefined){
          campaignEmailSettings.get
            }
        else{List(
        CampaignEmailSettings(
          campaign_id = CampaignId(26), // have no affect on adding campaign Id can add random id
          sender_email_setting_id = senderEmailSettingId,
          receiver_email_setting_id = receiverEmailSettingId,
          team_id = teamId,
          uuid = CampaignEmailSettingsUuid("temp_setting_id"),
          id = campaignEmailSettingsId,
          sender_email = "<EMAIL>", // does not have any affect can be random
          receiver_email = "<EMAIL>", // does not have any affect can be random
          max_emails_per_day_from_email_account = 100,
          signature = None,
          error = None,
          from_name = None
        )
      )},
      campaign_linkedin_settings = List(

      ),
      campaign_call_settings = List(

      ),
      campaign_whatsapp_settings = List(

      ),
      campaign_sms_settings = List(

      ),
      ai_sequence_status = None,
      timezone = "Asia/Kolkata",
      daily_from_time = daily_from_time, // time since beginning of day in seconds
      daily_till_time = daily_till_time, // time since beginning of day in seconds
      sending_holiday_calendar_id = None,

      // Sunday is the first day
      days_preference = List(true, true, true, true, true, true, true),

      mark_completed_after_days = 1,
      max_emails_per_day = 500,
      open_tracking_enabled = true,
      click_tracking_enabled = true,
      enable_email_validation = enable_email_validation,
      ab_testing_enabled = true,

      // warm up
      warmup_started_at = None,
      warmup_length_in_days = None,
      warmup_starting_email_count = None,
      show_soft_start_setting = false,

      // schedule start
      schedule_start_at = Some(DateTime.now()),
      schedule_start_at_tz = Some("Asia/Kolkata"),

      send_plain_text_email = Some(false),
      campaign_type = CampaignType.MultiChannel,


      email_priority = email_priority,
      append_followups = true,
      opt_out_msg = "Pivot",
      opt_out_is_text = true,
      add_prospect_to_dnc_on_opt_out = true,
      triggers = Seq(),
      sending_mode = None,
      selected_calendar_data = None
    )
  )
  def defaultCampaignSettingsForWhatsapp(
                                          whatsappSettingUuid: WhatsappSettingUuid,
                                          teamId: TeamId,
                                          daily_from_time: Int,
                                          daily_till_time: Int,
                                          whatsappSettingSenderDetails: Option[List[WhatsappSettingSenderDetails]] = None
                             ): Option[CampaignSettings] = Some(
    CampaignSettings(
      // settings
      campaign_email_settings = List(

      ),
      campaign_linkedin_settings = List(

      ),
      campaign_call_settings = List(

      ),
      campaign_whatsapp_settings =
        if(whatsappSettingSenderDetails.isDefined){
          whatsappSettingSenderDetails.get
        }else{

          List(
            WhatsappSettingSenderDetails(
              channel_setting_uuid = ChannelSettingUuid(whatsappSettingUuid.uuid),
              team_id = teamId,
              phone_number = SrRandomTestUtils.getRandomPhoneNumber,
              first_name = "Shubham",
              last_name = "Kudekar",
            )
          )
        }
      ,
      campaign_sms_settings = List(

      ),
      ai_sequence_status = None,
      timezone = "Asia/Kolkata",
      daily_from_time = daily_from_time, // time since beginning of day in seconds
      daily_till_time = daily_till_time, // time since beginning of day in seconds
      sending_holiday_calendar_id = None,

      // Sunday is the first day
      days_preference = List(true, true, true, true, true, true, true),

      mark_completed_after_days = 1,
      max_emails_per_day = 500,
      open_tracking_enabled = false,
      click_tracking_enabled = false,
      enable_email_validation = false,
      ab_testing_enabled = true,

      // warm up
      warmup_started_at = None,
      warmup_length_in_days = None,
      warmup_starting_email_count = None,
      show_soft_start_setting = false,

      // schedule start
      schedule_start_at = Some(DateTime.now()),
      schedule_start_at_tz = Some("Asia/Kolkata"),

      send_plain_text_email = Some(false),
      campaign_type = CampaignType.MultiChannel,


      email_priority =  CampaignEmailPriority.EQUAL,
      append_followups = true,
      opt_out_msg = "Pivot",
      opt_out_is_text = true,
      add_prospect_to_dnc_on_opt_out = true,
      triggers = Seq(),
      sending_mode = None,
      selected_calendar_data = None
    )
  )

  def campaignStepDataAutoEmail(
                                email_body: String
                               ): AutoEmailStep = AutoEmailStep(
    subject = "AutoEmail step for Integration test",
    body = email_body
  )

  def campaignStepDataWhatsapp(
                                 whatsappBody: String
                               ): WhatsappMessageData = WhatsappMessageData(
    step_type = CampaignStepType.WhatsappMessage,
    body = whatsappBody
  )
  def defaultCampaignStepVariantCreateOrUpdateAutoEmail(
                                                        parentId: Long,
                                                        stepDelay: Int,
                                                        email_body: String,
                                                       ): CampaignStepVariantCreateOrUpdate =
    CampaignStepVariantCreateOrUpdate(
      parent_id = parentId,
      step_data = campaignStepDataAutoEmail(
        email_body = email_body
      ),
      step_delay = stepDelay,
      notes = Some("Auto Email Step"),
      priority = Some(TaskPriority.High),
    )


  def defaultCampaignStepVariantWhatsapp(
                                                         parentId: Long,
                                                         stepDelay: Int,
                                                         whatsappBody: String,
                                                       ): CampaignStepVariantCreateOrUpdate =
    CampaignStepVariantCreateOrUpdate(
      parent_id = parentId,
      step_data =campaignStepDataWhatsapp(
        whatsappBody = whatsappBody
      ),
      step_delay = stepDelay,
      notes = Some("Whatsapp Task"),
      priority = Some(TaskPriority.High),
    )

  val adminDefaultPermissions: RolePermissionsInDBV2 = RolePermissionDataDAOV2.defaultRoles(
    role = TeamAccountRole.ADMIN,
    simpler_perm_flag = false
  )

  val rolePermissionData: RolePermissionDataV2 = RolePermissionDataV2.toRolePermissionApi(
    data = adminDefaultPermissions.copy(id = 2)
  )

  def teamMember(
                  teamId: TeamId,
                  accountId: AccountId,
                  taId: Long
                ): TeamMember = TeamMember(
    team_id = teamId.id,
    team_name = "smartreach",
    user_id = accountId.id,
    ta_id = taId, // dont send ta_id to frontend / api response, only for internal purpose, its dynamically assigned in AuthUtils
    first_name = None,
    last_name = None,
    email = "<EMAIL>",
    team_role = TeamAccountRole.ADMIN,
    api_key = Some("abcd1234"),
    zapier_key = Some("zapier_key")
  )

  def teamMemberLite(
                      accountId: AccountId,
                      ): TeamMemberLite  = TeamMemberLite(
    user_id = accountId.id,
    first_name = None,
    last_name = None,
    email = "<EMAIL>",
    active = true,
    timezone = Some("Asia/Kolkata"),
    twofa_enabled = true,
    created_at = DateTime.now(),
    user_uuid = AccountUuid(uuid = "uuid"),
    team_role = TeamAccountRole.ADMIN
  )


  def prospectCategoriesInDB(
                              teamId: TeamId,
                              prospect_categories_custom_do_not_contact: Long
                            ): ProspectCategoriesInDB = ProspectCategoriesInDB(
    id = prospect_categories_custom_do_not_contact,
    name = "Do not contact",
    text_id = "do_not_contact",
    label_color = "#d52728",
    is_custom = false,
    team_id = teamId.id,
    rank = ProspectCategoryRank(rank = 4000)
  )

  def prospectCategoriesInDB2(
                               teamId: TeamId,
                               prospect_categories_custom_not_categorized: Long
                             ): ProspectCategoriesInDB = ProspectCategoriesInDB(
    id = prospect_categories_custom_not_categorized,
    name = "Not contacted",
    text_id = "not_contacted",
    label_color = "#d52728",
    is_custom = false,
    team_id = teamId.id,
    rank = ProspectCategoryRank(rank = 3000)
  )
  def defaultTeamAccount(
                          teamId: TeamId,
                          accountId: AccountId,
                          orgId: OrgId,
                          taId: Long,
                          prospect_categories_custom_do_not_contact: Long,
                          prospect_categories_custom_not_categorized: Long
                        ): TeamAccount = TeamAccount(

    team_id = teamId.id,
    org_id = orgId.id,

    role_from_db = None, // MUST come from db (option type only for cacheservice error), should not be sent to frontend, only intermediate
    role = Some(rolePermissionData), // should be sent to frontend
    active = true,
    is_actively_used = true,
    team_name = "smartreach",
    total_members = 1,
    access_members = Seq(teamMember(
      teamId = teamId,
      accountId = accountId,
      taId = taId
    )),
    all_members = Seq(teamMemberLite(
      accountId = accountId
    )),

    prospect_categories_custom = Seq(
      prospectCategoriesInDB(
        teamId = teamId,
        prospect_categories_custom_do_not_contact = prospect_categories_custom_do_not_contact
      ),
      prospectCategoriesInDB2(
        teamId = teamId,
        prospect_categories_custom_not_categorized = prospect_categories_custom_not_categorized
      )
    ),
    max_emails_per_prospect_per_day = 100L,
    max_emails_per_prospect_per_week = 1000L,
    max_emails_per_prospect_account_per_day = 97,
    max_emails_per_prospect_account_per_week = 497,

    // ADMIN SETTINGS FOR MULTICAMPAIGN
    // allow_assigning_prospects_to_multiple_campaigns: Boolean, FORCEASSIGNISSUE
    reply_handling = ReplyHandling.PAUSE_SPECIFIC_CAMPAIGN_ON_REPLY,
    created_at = DateTime.now(),
    selected_calendar_data = None,
    team_uuid = TeamUuid("team_bvfbvfroebbkbkvcs")
  )

  def orgCountData(
    current_sending_email_accounts: Int
  ): OrgCountData = {

    OrgCountDataFixture.orgCountData_default.copy(
      current_sending_email_accounts = current_sending_email_accounts,
      max_phone_number_buying_limit_org = SrFeatureFlags.maxPhoneNumberToBuyLimitForOrg(
        calling_flag = Some(false),
        total_email_limit = 50
      ),
    )

  }

  val orgSettings: OrgSettings = OrgSettings(
    enable_ab_testing = true,
    disable_force_send = true,
    bulk_sender = true,
    allow_2fa = true,
    show_2fa_setting = true,
    enforce_2fa = true,

    // for zoho, pipedrive, hubspot crms (salesforce will have a different flag)
    allow_native_crm_integration = true,
      agency_option_allow_changing = false,
      agency_option_show = false
  )

  val orgPlan: OrgPlan = OrgPlan(
    new_prospects_paused_till = None,
    is_v2_business_plan = true,


    fs_account_id = None,
    stripe_customer_id = None,
    payment_gateway = None,
    current_cycle_started_at = DateTime.now(),
    next_billing_date = None,

    payment_due_invoice_link = None,
    payment_due_campaign_pause_at = None,

    plan_type = PlanType.PAID,
    plan_name = "ultimate-annual-user-inr-base-v2",
    plan_id = PlanID.ULTIMATE
  )


  val orgMetadata: OrgMetadata = OrgMetadata(
    allow_user_level_api_key = None,

    ff_multichannel = Some(true),
    is_onboarding_done = Some(true),
    show_agency_pricing = Some(true),

    show_promo_option = Some(true),
    show_individual_plans = Some(true),
    show_business_plans = Some(true),
    show_business_pro_plan = Some(true),
//    enable_warmup_box = Some(true),

    ff_emails_sent_report = Some(true),


    show_campaign_tags = Some(true),
    allowed_for_new_google_api_key = Some(true),
      enable_domain_health_page = None,
    increase_email_delay = Some(false)

  )

  def defaultOrganizationWithCurrentData(
                                          orgId: OrgId,
                                          accountId: AccountId,
                                          current_sending_email_accounts: Int
                                        ): OrganizationWithCurrentData = OrganizationWithCurrentData(
    id = orgId.id,
    name = "smartreach",
    owner_account_id = accountId.id,
    counts = orgCountData(
      current_sending_email_accounts = current_sending_email_accounts
    ),
    settings = orgSettings,
    plan = orgPlan,

    is_agency = false,
    trial_ends_at = DateTime.now().plusDays(9),
    error = None,
    error_code = None,
    paused_till = None,

    errors = Seq(),
    warnings = Seq(),

    via_referral = false,

    org_metadata = orgMetadata

  )


  def defaultCampaignEmailSettings(
                                  emailSettings: List[EmailSetting],
                                  teamId: TeamId

                                  ):List[CampaignEmailSettings] = {
      emailSettings.map{ es =>

          CampaignEmailSettings(
              campaign_id = CampaignId(123), // have no affect on adding campaign Id can add random id
              sender_email_setting_id = es.id.get,
              receiver_email_setting_id = es.id.get,
              team_id = teamId,
              uuid = CampaignEmailSettingsUuid("temp_setting_id"),
              id = CampaignEmailSettingsId(es.id.get.emailSettingId),
              sender_email = es.email, // does not have any affect can be random
              receiver_email = es.email, // does not have any affect can be random
              max_emails_per_day_from_email_account = 100,
              signature = None,
              error = None,
              from_name = None
          )

      }
  }

}
