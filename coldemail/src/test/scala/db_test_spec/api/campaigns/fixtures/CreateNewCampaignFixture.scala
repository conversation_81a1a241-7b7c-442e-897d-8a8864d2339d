package db_test_spec.api.campaigns.fixtures

import api.accounts.{ProspectCategoriesInDB, TeamId}
import api.accounts.models.{AccountId, OrgId}
import api.campaigns.models.{CampaignCreationError, CampaignEmailSettingsId}
import api.campaigns.{CampaignCreateForm, CampaignEmailSettings, CampaignSettings, CampaignWithStatsAndEmail}
import DefaultCampaignParametersFixtures.{defaultCampaignCreateForm, defaultCampaignSettings}
import api.emails.EmailSetting
import api.prospects.models.{ProspectCategory, ProspectCategoryNew}
import app.db_test.CustomNotCategorizedAndDoNotContactIds
import db_test_spec.api.emails.fixtures.EmailSettingFixtureForIntegrationTest
import db_test_spec.api.teams.fixtures.{NewTeamCreationData, NewTeamCreationFixture}
import io.smartreach.esp.api.emails.EmailSettingId
import sr_scheduler.models.CampaignEmailPriority
import sr_scheduler.models.CampaignEmailPriority.CampaignEmailPriority
import utils.SRLogger
import utils.testapp.TestAppTrait

import scala.concurrent.Future

case class NewCampaignCreationData(
                                    newTeamCreationData: NewTeamCreationData,
                                    newEmailSetting: EmailSetting,
                                    campaignWithStatsAndEmail: CampaignWithStatsAndEmail,
                                    customNotCategorizedAndDoNotContactIds: CustomNotCategorizedAndDoNotContactIds
                                  )

object CreateNewCampaignFixture extends TestAppTrait {

  def findCategorizedAndDoNotContactCustomIds(
                                               prospect_categories: Seq[ProspectCategoriesInDB]
                                             ): CustomNotCategorizedAndDoNotContactIds = {
    val not_categorized = {
//      if(!prospect_categories.exists(_.text_id == ProspectCategory.NOT_CATEGORIZED.toString)) {
        prospect_categories.find(_.text_id == ProspectCategoryNew.NOT_CONTACTED.toString).get.id
//      } else {
//        prospect_categories.find(_.text_id == ProspectCategory.NOT_CATEGORIZED.toString).get.id
//      }
    }
    val do_not_contact = prospect_categories.find(_.text_id == ProspectCategory.DO_NOT_CONTACT.toString).get.id
    CustomNotCategorizedAndDoNotContactIds(
      not_categorized = not_categorized,
      do_not_contact = do_not_contact
    )
  }

  def createNewCampaign(
                        orgId: OrgId,
                        accountId: AccountId,
                        teamId: TeamId,
                        taId: Long,
                        campaignEmailSettingsId: CampaignEmailSettingsId,
                        senderEmailSettingId: EmailSettingId,
                        receiverEmailSettingId: EmailSettingId,
                        schedule_from_time: Int = 0,
                        schedule_till_time: Int = 86399,
                        campaignCreateForm: CampaignCreateForm = defaultCampaignCreateForm,
                        email_priority: CampaignEmailPriority = CampaignEmailPriority.EQUAL,
                        campaignSettings: Option[CampaignSettings] = None,
                        campaignEmailSettings: Option[List[CampaignEmailSettings]] =None,
                        enable_email_validation: Boolean = false,
                        ownerFirstName : String
                       )(using Logger: SRLogger): Future[CampaignWithStatsAndEmail] = {
    val campaignSettingsData = if(campaignSettings.isDefined) {
      campaignSettings
    } else {
      defaultCampaignSettings(
        campaignEmailSettingsId = campaignEmailSettingsId,
        senderEmailSettingId = senderEmailSettingId,
        receiverEmailSettingId = receiverEmailSettingId,
        teamId = teamId,
        daily_from_time = schedule_from_time,
        daily_till_time = schedule_till_time,
        email_priority = email_priority,
        enable_email_validation = enable_email_validation,
        campaignEmailSettings = campaignEmailSettings
      )
    }

    for {
      campaignWithStatsAndEmailEither: Either[CampaignCreationError, CampaignWithStatsAndEmail] <- {
        campaignService.createCampaign(
          orgId = orgId.id,
          accountId = accountId.id,
          teamId = teamId.id,
          taId = taId,
          data = campaignCreateForm,
          campaignSettings = campaignSettingsData,
          permittedAccountIdsForEditCampaigns = Seq(accountId.id),
          ownerFirstName = ownerFirstName
        )
      }

      campaignWithStatsAndEmail: CampaignWithStatsAndEmail <- campaignWithStatsAndEmailEither match {
        case Left(err) =>
          Future.failed(new Exception(s"Campaign Not Created: $err"))

        case Right(campaignWithStatsAndEmail) =>
          print("Campaign With Stats and email created")
          Future.successful(campaignWithStatsAndEmail)
      }
    } yield {
      campaignWithStatsAndEmail
    }
  }

  def createNewCampaign(
                        orgId: OrgId,
                        accountId: AccountId,
                        accountName: String,
                       )(using Logger: SRLogger): Future[NewCampaignCreationData] = {
    for {
      createNewTeam: NewTeamCreationData <- NewTeamCreationFixture.createNewTeam(
        orgId = orgId,
        accountId = accountId,
        accountName = accountName
      )

      createNewEmailSetting: EmailSetting <- Future(EmailSettingFixtureForIntegrationTest.createEmailSetting(
        orgId = orgId,
        accountId = accountId,
        teamId = TeamId(createNewTeam.teamAccount.team_id),
        taId = createNewTeam.teamAccount.access_members.head.ta_id
      ).get)

      createNewCampaign: CampaignWithStatsAndEmail <- createNewCampaign(
        orgId = orgId,
        accountId = accountId,
        teamId = TeamId(createNewTeam.teamAccount.team_id),
        taId = createNewTeam.teamAccount.access_members.head.ta_id,
        campaignEmailSettingsId = CampaignEmailSettingsId(createNewEmailSetting.id.get.emailSettingId),
        senderEmailSettingId = EmailSettingId(createNewEmailSetting.id.get.emailSettingId),
        receiverEmailSettingId = EmailSettingId(createNewEmailSetting.id.get.emailSettingId),
        ownerFirstName = accountName

      )

      customNotCategorizedAndDoNotContactIds <- Future(findCategorizedAndDoNotContactCustomIds(
        prospect_categories = createNewTeam.teamAccount.prospect_categories_custom
      ))

    } yield {
      NewCampaignCreationData(
        newTeamCreationData = createNewTeam,
        newEmailSetting = createNewEmailSetting,
        campaignWithStatsAndEmail = createNewCampaign,
        customNotCategorizedAndDoNotContactIds = customNotCategorizedAndDoNotContactIds
      )
    }
  }


  def createNewCampaignChannelSpecific(
                         orgId: OrgId,
                         accountId: AccountId,
                         teamId: TeamId,
                         taId: Long,
                         campaignCreateForm: CampaignCreateForm = defaultCampaignCreateForm,
                         campaignSettings: CampaignSettings,
                         ownerFirstName: String
                       )(using Logger: SRLogger): Future[CampaignWithStatsAndEmail] = {


    for {
      campaignWithStatsAndEmailEither: Either[CampaignCreationError, CampaignWithStatsAndEmail] <- {
        campaignService.createCampaign(
          orgId = orgId.id,
          accountId = accountId.id,
          teamId = teamId.id,
          taId = taId,
          data = campaignCreateForm,
          campaignSettings = Some(campaignSettings),
          permittedAccountIdsForEditCampaigns = Seq(accountId.id),
          ownerFirstName = ownerFirstName
        )
      }

      campaignWithStatsAndEmail: CampaignWithStatsAndEmail <- campaignWithStatsAndEmailEither match {
        case Left(err) => {
          Future.failed(new Exception(s"Campaign Not Created: $err"))
        }
        case Right(campaignWithStatsAndEmail) =>
          Future.successful(campaignWithStatsAndEmail)
      }
    } yield {
      campaignWithStatsAndEmail
    }

  }
}
