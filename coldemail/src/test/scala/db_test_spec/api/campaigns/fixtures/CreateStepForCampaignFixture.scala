package db_test_spec.api.campaigns.fixtures

import api.accounts.TeamId
import api.accounts.models.{AccountId, OrgId}
import api.campaigns.{CampaignStepVariant, CampaignStepVariantCreateOrUpdate}
import api.campaigns.services.CampaignId
import DefaultCampaignParametersFixtures.{defaultCampaignStepVariantCreateOrUpdateAutoEmail, defaultCampaignStepVariantWhatsapp}
import api.campaigns.models.CreateCampaignStepVariantError
import utils.SRLogger
import utils.testapp.TestAppTrait

import scala.concurrent.Future

object CreateStepForCampaignFixture extends TestAppTrait {
  def createAutoEmailStepForCampaign(
                                      orgId: OrgId,
                                      teamId: TeamId,
                                      accountId: AccountId,
                                      taId: Long,
                                      campaignId: CampaignId,
                                      stepId: Long = 0,
                                      campaignHeadStepId: Option[Long] = None,
                                      parentId: Long = 0,
                                      stepDelay: Int = 0,
                                      campaignStepVariantCreateOrUpdateEmail: Option[CampaignStepVariantCreateOrUpdate] = None,
                                      email_body: String = "Test"
                                    )(using Logger: SRLogger): Future[CampaignStepVariant] = {
    val campaignStepVariantCreateOrUpdateEmailData: Option[CampaignStepVariantCreateOrUpdate] =
      if (campaignStepVariantCreateOrUpdateEmail.isDefined) {
        campaignStepVariantCreateOrUpdateEmail
      } else {
        Option(
          defaultCampaignStepVariantCreateOrUpdateAutoEmail(
            parentId = parentId,
            stepDelay = stepDelay,
            email_body = email_body
          )
        )
      }
    for {
      createdVariant: Either[CreateCampaignStepVariantError, CampaignStepVariant] <- {
        campaignStepService.createVariant(
          orgId = orgId.id,
          data = campaignStepVariantCreateOrUpdateEmailData.get,
          teamId = teamId.id,
          userId = accountId.id,
          taId = taId,
          stepId = stepId,
          campaignId = campaignId.id,
          campaignHeadStepId = campaignHeadStepId
        )
      }

      campaignStepVariant: CampaignStepVariant <- createdVariant match {
        case Left(err) =>
          Future.failed(new Exception(s"Campaign Step Not Created: $err"))

        case Right(createdVariant) =>
          Future.successful(createdVariant)
      }

    } yield {
      campaignStepVariant
    }
  }


  def createWhatsappStepForCampaign(
                                     orgId: OrgId,
                                     teamId: TeamId,
                                     accountId: AccountId,
                                     taId: Long,
                                     campaignId: CampaignId,
                                     stepId: Long = 0,
                                     campaignHeadStepId: Option[Long] = None,
                                     parentId: Long = 0,
                                     stepDelay: Int = 0,
                                     campaignStepVariantWhatsapp: Option[CampaignStepVariantCreateOrUpdate] = None,
                                     whatsappBody: String
                                   )(using Logger: SRLogger): Future[CampaignStepVariant] = {
    val campaignStepVariantWhatsappData: Option[CampaignStepVariantCreateOrUpdate] =
      if (campaignStepVariantWhatsapp.isDefined) {
        campaignStepVariantWhatsapp
      } else {
        Option(
          defaultCampaignStepVariantWhatsapp(
            parentId = parentId,
            stepDelay = stepDelay,
            whatsappBody = whatsappBody
          )
        )
      }
    for {
      createdVariant: Either[CreateCampaignStepVariantError, CampaignStepVariant] <- {
        campaignStepService.createVariant(
          orgId = orgId.id,
          data = campaignStepVariantWhatsappData.get,
          teamId = teamId.id,
          userId = accountId.id,
          taId = taId,
          stepId = stepId,
          campaignId = campaignId.id,
          campaignHeadStepId = campaignHeadStepId
        )
      }

      campaignStepVariant: CampaignStepVariant <- createdVariant match {
        case Left(err) =>
          Future.failed(new Exception(s"Campaign Step Not Created: $err"))

        case Right(createdVariant) =>
          Future.successful(createdVariant)
      }

    } yield {
      campaignStepVariant
    }
  }
}
