package db_test_spec.api.pipelines

import api.accounts.models.AccountId
import api.accounts.{Account, AccountUuid, TeamId}
import api.pipelines.dao.OpportunityData
import api.pipelines.models._
import api.prospects.dao.ProspectIdAndPotentialDuplicateProspectId
import api.prospects.models.{PotentialDuplicateProspectId, ProspectId}
import api_layer_models.CurrencyType
import db_test_spec.api.accounts.fixtures.NewAccountAndEmailSettingData
import db_test_spec.api.{DbTestingBeforeAllAndAfterAll, InitialData}
import utils.helpers.LogHelpers

import scala.util.{Failure, Success, Try}

class OpportunityDAOSpec extends DbTestingBeforeAllAndAfterAll {

  describe("getDuplicateProspectOpportunities") {
    it("should return success") {
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
      val account: Account = initialData.account
      val accountId: AccountId = AccountId(account.internal_id)
      val teamId: TeamId = TeamId(account.teams.head.team_id)

      val prospect_id: ProspectId = ProspectId(id = initialData.prospectsResult.head.id)
      val opportunityData: OpportunityData = OpportunityData(
        prospect_id = prospect_id,
        value = 234,
        currency = CurrencyType.USD,
        opportunity_type = OpportunityType.OneTime,
        opportunity_status_id = OpportunityStatusUUID(uuid = "some-status-uuid-1"),
        pipeline_id = PipelineUUID(uuid = "some-pipeline-uuid-1"),
        confidence = 34,
        campaign_id = None,
        notes = None,
        owner_id = AccountUuid(uuid = account.uuid),
        estimate_closing_date = None
      )

      val dbAndSession = dbUtils.startLocalTx()
      implicit val session = dbAndSession.session

      opportunityDAO.createOpportunity(
        teamId = teamId,
        opportunityUUID = OpportunityUUID(uuid = "some-opp-uuid-new-1"),
        opportunityData = opportunityData,
        opportunityStatusInternalId = OpportunityStatusInternalId(id = 1),
        pipelineInternalId = PipelineInternalId(id = 1),
        ownerAccountInternalId = accountId,
        newHighestOpportunityRankInStatus = OpportunityPosRank(rank = 23423)
      ) match {
        case Failure(exception) =>
          dbUtils.commitAndCloseSession(dbAndSession.db)
          println(s"Error: ${LogHelpers.getStackTraceAsString(exception)}")
          assert(false)
        case Success(createdOpportunities) =>
          dbUtils.commitAndCloseSession(dbAndSession.db)

          createdOpportunities match {
            case None => assert(false)

            case Some(uuid) =>

              val res: Try[List[OpportunityUUID]] = opportunityDAO.updateMasterProspectIdInOpportunities(
                duplicateProspects = List(ProspectIdAndPotentialDuplicateProspectId(
                  prospectId = prospect_id,
                  potentialDuplicateProspectId = PotentialDuplicateProspectId(1L),
                  isMasterProspect = false
                )),
                masterProspectId = ProspectId(initialData.prospectsResult.last.id),
                teamId = teamId
              )

              assert(res.isSuccess)
              assert(res.get.head == uuid)
          }
      }
    }
  }
}
