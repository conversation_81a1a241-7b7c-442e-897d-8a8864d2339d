package db_test_spec.api.product_onboarding

import api.accounts.TeamId
import api.accounts.models.{AccountId, OrgId}
import api.campaigns.{Campaign, CampaignStepVariant, CampaignWithStatsAndEmail}
import api.campaigns.models.{CampaignEmailSettingsId, CampaignStepType}
import api.campaigns.services.CampaignId
import api.lead_finder.models.{LeadId, LeadUuid}
import api.product_onboarding.models.ProductOnboardingFlags
import db_test_spec.api.accounts.fixtures.{LeadFinderFixture, NewAccountAndEmailSettingData}
import db_test_spec.api.campaigns.fixtures.{CreateNewCampaignFixture, CreateStepForCampaignFixture}
import db_test_spec.api.prospects.fixtures.ProspectFixtureForIntegrationTest
import db_test_spec.api.{DbTestingBeforeAllAndAfterAll, InitialData}
import io.smartreach.esp.api.emails.EmailSettingId
import play.api.libs.json.{JsO<PERSON>, Json}

import scala.concurrent.Future
import scala.util.{Failure, Success}

class ProductOnboardingDAOSpec extends DbTestingBeforeAllAndAfterAll {
    describe("getProductOnboardingFlags") {
        it("should return true for email,linkedin "){
            val initialData:InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

//            EmailSettingFixtureForIntegrationTest.createEmailSetting(
//                orgId = OrgId(initialData.account.org.id),
//                accountId = AccountId(initialData.account.internal_id),
//                teamId = TeamId(initialData.account.teams.head.team_id),
//                taId = initialData.account.teams.head.access_members.head.ta_id,
//                emailSettingForm = Option(defaultEmailSettingForm.copy(
//                    email = "<EMAIL>"
//                ))
//            )(Logger = Logger).get

            val result = productOnboardingDAO.getProductOnboardingFlags(OrgId(initialData.account.org.id),TeamId(initialData.account.teams.head.team_id))

            result match {
                case Success(flags) =>
                    assert(flags.is_email_account_added == true && flags.is_linkedin_account_added == true)

                case Failure(exception) =>
                    assert(false)
            }


        }

        it("should return true for campaign not started flag"){

            val result = for {

                initialData: InitialData <- Future {
                    NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
                }

                _: CampaignWithStatsAndEmail <- CreateNewCampaignFixture.createNewCampaign(
                    orgId = OrgId(initialData.account.org.id),
                    accountId = AccountId(initialData.account.internal_id),
                    teamId = TeamId(initialData.account.teams.head.team_id),
                    taId = initialData.account.teams.head.access_members.head.ta_id,
                    campaignEmailSettingsId = CampaignEmailSettingsId(1L),
                    senderEmailSettingId = EmailSettingId(1L),
                    receiverEmailSettingId = EmailSettingId(1L),
                    ownerFirstName = initialData.account.first_name.get

                )

                flags: ProductOnboardingFlags <- Future {
                    productOnboardingDAO.getProductOnboardingFlags(
                          orgId = OrgId(initialData.account.org.id), 
                          tid = TeamId(initialData.head_team_id)
                      )
                      .get
                }
                
            } yield {
                flags
            }

            result
              .map(flags => {

                  assert(flags.is_campaign_not_started == true)

              })
              .recover { case e =>

                  assert(false)

              }
        }

        it("should return false for lead finder used "){
            val initialData:InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

            val result = productOnboardingDAO.getProductOnboardingFlags(OrgId(initialData.account.org.id),TeamId(initialData.account.teams.head.team_id))

            result match {
                case Success(flags) =>
                    assert(!flags.is_leadfinder_used)

                case Failure(exception) =>
                    assert(false)
            }


        }

        it("should return true for whatsapp "){
            val initialData:InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

            val result = productOnboardingDAO.getProductOnboardingFlags(OrgId(initialData.account.org.id),TeamId(initialData.account.teams.head.team_id))

            result match {
                case Success(flags) =>
                    assert(!flags.is_whatsapp_account_added)

                case Failure(exception) =>
                    assert(false)
            }


        }
    }

    describe("getProductOnboardingData") {
        it("should return product_onboarding_data_accounts"){
            val initialData:InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

            val onboardingData: JsObject = Json.obj(
                "using_for" -> Json.arr("linkedin_engagement", "find_leads"),
                "sendingFor" -> "your_clients",
                "prospects_to_be_contacted_per_month" -> "1000-50000"
            )


            for{

                 _:AccountId <- Future.fromTry(accountDAO.updateAccountOnboardingData(accountId = AccountId(initialData.account.internal_id), onboardingData = onboardingData))

            } yield {
                true
            }



            val result = productOnboardingDAO.getProductOnboardingData(OrgId(initialData.account.org.id),TeamId(initialData.account.teams.head.team_id),AccountId(initialData.account.internal_id))

            result match {
                case Success(data) =>
                    println(data)
                    assert(data.use_case_flags.is_using_for_linkedin_outreach && data.use_case_flags.is_using_for_leads && !data.use_case_flags.is_using_for_whatsapp_outreach && !data.use_case_flags.is_using_for_cold_calling && !data.use_case_flags.is_using_for_cold_emailing)

                case Failure(exception) =>
                    assert(false)
            }
        }
    }

    describe("getCampaignFlagsWithId"){
        it("should return campaign id"){
            val result = for {

                initialData:InitialData <- Future {
                    
                    NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
                    
                }

                _:CampaignWithStatsAndEmail<- CreateNewCampaignFixture.createNewCampaign(
                    orgId = OrgId(initialData.account.org.id),
                    accountId = AccountId(initialData.account.internal_id),
                    teamId = TeamId(initialData.account.teams.head.team_id),
                    taId = initialData.account.teams.head.access_members.head.ta_id,
                    campaignEmailSettingsId = CampaignEmailSettingsId(1L),
                    senderEmailSettingId = EmailSettingId(1L),
                    receiverEmailSettingId = EmailSettingId(1L),
                    ownerFirstName = initialData.account.first_name.get


                )
                
                res: Option[CampaignId] <- Future {

                    productOnboardingDAO.getLatestNotStartedCampaign(TeamId(initialData.account.teams.head.team_id), AccountId(initialData.account.internal_id))
                      .get

                }
            } yield {
                res
            }

            
            result
              .map(id => {

                  println(id)
                  assert(id.isDefined)
              })
              .recover { case e =>
                  assert(false)

              }
        }

        it("should return steps created"){

            val result = for {



                initialData:InitialData <- Future {

                    NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

                }
                
                campaign:CampaignWithStatsAndEmail<- CreateNewCampaignFixture.createNewCampaign(
                    orgId = OrgId(initialData.account.org.id),
                    accountId = AccountId(initialData.account.internal_id),
                    teamId = TeamId(initialData.account.teams.head.team_id),
                    taId = initialData.account.teams.head.access_members.head.ta_id,
                    campaignEmailSettingsId = CampaignEmailSettingsId(1L),
                    senderEmailSettingId = EmailSettingId(1L),
                    receiverEmailSettingId = EmailSettingId(1L),
                    ownerFirstName = initialData.account.first_name.get


                )

                 result: List[CampaignStepType] <- Future { 
                     productOnboardingDAO.getCampaignStepTypes(CampaignId(campaign.id)) 
                       .get
                 }
            } yield {
                result
            }



            result.map { steps =>
                assert(steps.isEmpty)
            }
              .recover { case e =>
                  assert(false)
              }
        }

        it("should return prospect added"){

            val result = for {


                initialData:InitialData <- Future {

                    NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

                }


                campaign:CampaignWithStatsAndEmail<- CreateNewCampaignFixture.createNewCampaign(
                    orgId = OrgId(initialData.account.org.id),
                    accountId = AccountId(initialData.account.internal_id),
                    teamId = TeamId(initialData.account.teams.head.team_id),
                    taId = initialData.account.teams.head.access_members.head.ta_id,
                    campaignEmailSettingsId = CampaignEmailSettingsId(1L),
                    senderEmailSettingId = EmailSettingId(1L),
                    receiverEmailSettingId = EmailSettingId(1L),
                    ownerFirstName = initialData.account.first_name.get


                )

                _<- Future {
                    ProspectFixtureForIntegrationTest.createUpdateOrAssignProspect(
                        accountId = AccountId(initialData.account.internal_id),
                        teamId = TeamId(initialData.account.teams.head.team_id),
                        account = initialData.account,
                        campaignId = Some(CampaignId(campaign.id)),
                        generateProspectCountIfNoGivenProspect = 5
                    )
                      .get
                }

                result: Boolean <- Future {
                    productOnboardingDAO.hasProspects(CampaignId(campaign.id))
                      .get
                        
                }
            } yield {
                result
            }



            result.map { hasProspects =>
                assert(hasProspects)
            }
              .recover { case e =>

                  assert(false)
              }
        }
    }

}
