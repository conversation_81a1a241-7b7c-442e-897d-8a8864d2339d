package db_test_spec.api.scheduler

import api.accounts.models.{AccountId, OrgId}
import api.accounts.{Account, ReplyHandling, TeamId}
import api.blacklist.models.BlacklistCreateOrDeleteApiLevel
import api.blacklist.{Blacklist, BlacklistCreateEmailsForm, CreateOrUpdateBlacklistError}
import api.campaigns.{CampaignStepVariant, CampaignWithStatsAndEmail}
import api.campaigns.models.{CampaignName, CampaignStepType, IgnoreProspectsInOtherCampaigns}
import api.campaigns.services.CampaignId
import api.emails.models.EmailReplyType
import api.emails.{DBEmailMessagesSavedResponse, EmailMessageTracked, EmailSetting}
import api.prospects.{ProspectCreateFormData, ProspectDeduplicationColumnsData}
import api.prospects.models.{ProspectCategory, ProspectId, StepId, UpdateProspectType}
import api.prospects.service.ProspectDeduplicationDataWithId
import api.scylla.dao.BounceData
import app.testapp_dao.BlacklistTestDAO
import db_test_spec.api.{AppSpecFixture, DbTestingBeforeAllAndAfterAll, InitialData, SRSetupAndDeleteFixtures, SchedulerIntegrationTestResult}
import db_test_spec.api.accounts.fixtures.{EmailScheduledNewFixture, NewAccountAndEmailSettingData, NewAccountAndWhatsappSettingData}
import db_test_spec.api.campaigns.dao.{CampaignProspectTestDAO, CampaignTestDAO}
import db_test_spec.api.campaigns.fixtures.CreateStepForCampaignFixture
import db_test_spec.api.campaigns.test_utils.{CampaignUtils, CreateAndStartCampaignData, StartedCampaignDetails}
import db_test_spec.api.emails.dao.EmailScheduleTestDAO
import db_test_spec.api.emails.dao.EmailSettingTestDAO
import db_test_spec.api.emails.fixtures.DefaultEmailScheduledParameterFixtures.generateEmailMessageTracked
import db_test_spec.api.prospects.fixtures.{DefaultProspectParameterFixture, ProspectFixtureForIntegrationTest}
import db_test_spec.api.scheduler.dao.SchedulerTestDAO
import db_test_spec.api.scheduler.fixtures.{ReSchedulingFixture, ScheduleTaskFixture}
import db_test_spec.utils.SrRandomTestUtils
import eventframework.ProspectObject
import io.smartreach.esp.api.emails.{EmailSettingId, IEmailAddress}
import io.smartreach.esp.utils.email.EmailReplyBounceType
import org.joda.time.DateTime
import org.scalatest.ParallelTestExecution
import org.scalatest.funspec.AnyFunSpec
import org.scalatest.matchers.should.Matchers
import play.api.libs.json.Json
import sr_scheduler.models.{CampaignDataToAddNextToBeScheduledAtForEmailChannel, CampaignEmailPriority, ChannelData, ChannelType}
import sr_scheduler.models.ChannelData.{EmailChannelData, WhatsAppChannelData}
import utils.email.EmailReplyStatus
import utils.email.services.InternalTrackingNote
import utils.helpers.LogHelpers
import utils.mq.channel_scheduler.channels.ScheduleTasksData

import scala.concurrent.duration.{Duration, SECONDS}
import scala.concurrent.{Await, Future}
import scala.util.{Failure, Success}

case class ResultForBouncedEmailTestAssert(
                                            schedulerIntegrationTestResult: SchedulerIntegrationTestResult,
                                            addedProspect: Seq[String],
                                            completedProspectByReplyHandling: List[Int],
                                            bouncedProspectEmails: List[String]
                                          )

case class ResultForBlacklistedEmailTestAssert(
                                                schedulerIntegrationTestResult: SchedulerIntegrationTestResult,
                                                addedProspect: Seq[String],
                                                completedProspectByReplyHandling: List[Int],
                                                blacklistEmails: List[String]
                                              )

class SchedulerSpec extends DbTestingBeforeAllAndAfterAll with ParallelTestExecution {

  override lazy val isMainMqWorkerNeeded: Boolean = true
  
  describe("Scheduler Integration test") {
    it("Scheduler test for adding campaign with a single prospect and making that scheduled") {
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
      val account: Account = initialData.account
      val emailSetting: EmailSetting = initialData.emailSetting.get
      val teamId: TeamId = TeamId(account.teams.head.team_id)

      val scheduleTaskData: Future[SchedulerIntegrationTestResult] = for {

        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 1
        )

        //Test result after scheduling
        result: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
          emailChannelData = EmailChannelData(
            emailSettingId = emailSetting.id.get.emailSettingId
          ),
          teamId = teamId
        )

        //Verification
        scheduledDataCountInScheduler: List[Int] <- Future {
          SchedulerTestDAO.getCountOfScheduledProspectForACampaign(
            campaign_id = CampaignId(createAndStartCampaignData.campaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            step_id = StepId(createAndStartCampaignData.campaign.head_step_id.get),
          ).get
        }

        //Verification
        scheduledCampaignProspectCount: Int <- Future {
          CampaignProspectTestDAO.getCountOfCampaignProspectSchedulerStatusDue(
            campaign_id = CampaignId(createAndStartCampaignData.campaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            current_step_id = StepId(createAndStartCampaignData.campaign.head_step_id.get),
          ).get
        }
      } yield {
        SchedulerIntegrationTestResult(
          scheduleTasksData = result,
          emailScheduledCountForCampaign = scheduledDataCountInScheduler.length,
          scheduledCampaignProspectCount = scheduledCampaignProspectCount
        )
      }
      scheduleTaskData.map(p => {
        assert(p.emailScheduledCountForCampaign == 1
          && p.scheduledCampaignProspectCount == 1)
      }).recover({ case e =>
        println(e.printStackTrace())
        assert(false)
      })
    }

    it("Scheduler test for scheduling new and followups") {
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
      val account: Account = initialData.account
      val emailSetting: EmailSetting = initialData.emailSetting.get
      val orgId: OrgId = OrgId(account.org.id)
      val accountId: AccountId = AccountId(account.internal_id)
      val teamId: TeamId = TeamId(account.teams.head.team_id)
      val taId: Long = account.teams.head.access_members.head.ta_id
      val emailSettingId = EmailSettingId(emailSetting.id.get.emailSettingId)

      val scheduleTaskData: Future[(SchedulerIntegrationTestResult, SchedulerIntegrationTestResult)] = for {

        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 4
        )

        //Test result after scheduling
        result_1: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
          emailChannelData = EmailChannelData(
            emailSettingId = emailSetting.id.get.emailSettingId
          ),
          teamId = teamId
        )

        //Verification
        scheduledDataCountInScheduler_1: List[Int] <- Future {
          SchedulerTestDAO.getCountOfScheduledProspectForACampaign(
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            step_id = StepId(createAndStartCampaignData.addStep.step_id),
          ).get
        }

        //Verification
        scheduledCampaignProspectCount_1: Int <- Future {
          CampaignProspectTestDAO.getCountOfCampaignProspectSchedulerStatusDue(
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            current_step_id = StepId(createAndStartCampaignData.addStep.step_id),
          ).get
        }

        addProspect_2: Seq[ProspectObject] <- {
          ProspectFixtureForIntegrationTest.createUpdateOrAssignProspectFuture(
            campaignId = Option(CampaignId(createAndStartCampaignData.createCampaign.id)),
            accountId = accountId,
            teamId = teamId,
            account = account,
            generateProspectCountIfNoGivenProspect = 3
          )
        }

        addStep_2: CampaignStepVariant <- {
          CreateStepForCampaignFixture.createAutoEmailStepForCampaign(
            orgId = orgId,
            teamId = teamId,
            accountId = accountId,
            taId = taId,
            campaignId = CampaignId(createAndStartCampaignData.createCampaign.id),
            parentId = createAndStartCampaignData.addStep.step_id
          )
        }

        reInitializeForSecondScheduling <- ReSchedulingFixture.initializeDataForReScheduling(
          orgId = orgId,
          accountId = accountId,
          teamId = teamId,
          emailSetting = emailSetting,
          campaign = createAndStartCampaignData.campaign,
          prospectList = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id))
        )

        result_2: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
          emailChannelData = EmailChannelData(
            emailSettingId = emailSettingId.emailSettingId
          ),
          teamId = teamId
        )

        scheduledDataCountInScheduler_2: List[Int] <- Future {
          SchedulerTestDAO.getCountOfScheduledProspectForACampaign(
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            teamId = teamId,
            prospect_ids = addProspect_2.map(p => ProspectId(p.id)),
            step_id = StepId(createAndStartCampaignData.addStep.step_id),
          ).get
        }

        scheduledCampaignProspectCount_2: Int <- Future {
          CampaignProspectTestDAO.getCountOfCampaignProspectSchedulerStatusDue(
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            teamId = teamId,
            prospect_ids = addProspect_2.map(p => ProspectId(p.id)),
            current_step_id = StepId(createAndStartCampaignData.addStep.step_id),
          ).get
        }

      } yield {
        (SchedulerIntegrationTestResult(
          scheduleTasksData = result_1,
          emailScheduledCountForCampaign = scheduledDataCountInScheduler_1.length,
          scheduledCampaignProspectCount = scheduledCampaignProspectCount_1
        ),
          SchedulerIntegrationTestResult(
            scheduleTasksData = result_2,
            emailScheduledCountForCampaign = scheduledDataCountInScheduler_2.length,
            scheduledCampaignProspectCount = scheduledCampaignProspectCount_2
          ))
      }
      scheduleTaskData.map(p => {
        assert(p._1.emailScheduledCountForCampaign == 4
          && p._1.scheduledCampaignProspectCount == 4)
        assert(p._2.scheduleTasksData.saved_tasks_count == 7
          && p._2.emailScheduledCountForCampaign == 3
          && p._2.scheduledCampaignProspectCount == 3)
      }).recover({ case e =>
        println(LogHelpers.getStackTraceAsString(e))
        assert(false)
      })
    }

    it("Scheduler test for scheduling 2 campaigns with overflow prospect limit and should stop scheduling after email setting limit") {
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData(
        quota_per_day = 300,
        max_emails_per_prospect_per_day = 100,
        max_emails_per_prospect_per_week = 500,
        max_emails_per_prospect_account_per_day = Option(500),
        max_emails_per_prospect_account_per_week = Option(500)
      ).get
      val account: Account = initialData.account
      val emailSetting: EmailSetting = initialData.emailSetting.get
      val orgId: OrgId = OrgId(account.org.id)
      val accountId: AccountId = AccountId(account.internal_id)
      val teamId: TeamId = TeamId(account.teams.head.team_id)

      val scheduleTaskData: Future[(SchedulerIntegrationTestResult, SchedulerIntegrationTestResult)] = for {

        createAndStartCampaignData_1: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 200
        )

        createAndStartCampaignData_2: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 200
        )

        //Test result after scheduling
        result_1: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
          emailChannelData = EmailChannelData(
            emailSettingId = emailSetting.id.get.emailSettingId
          ),
          teamId = teamId
        )

        scheduledDataCountInScheduler_1_camp_1: List[Int] <- Future {
          SchedulerTestDAO.getCountOfScheduledProspectForACampaign(
            campaign_id = CampaignId(createAndStartCampaignData_1.createCampaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData_1.addProspect.map(p => ProspectId(p.id)),
            step_id = StepId(createAndStartCampaignData_1.addStep.step_id),
          ).get
        }

        scheduledCampaignProspectCount_1_camp_1: Int <- Future {
          CampaignProspectTestDAO.getCountOfCampaignProspectSchedulerStatusDue(
            campaign_id = CampaignId(createAndStartCampaignData_1.createCampaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData_1.addProspect.map(p => ProspectId(p.id)),
            current_step_id = StepId(createAndStartCampaignData_1.addStep.step_id),
          ).get
        }

        scheduledDataCountInScheduler_1_camp_2: List[Int] <- Future {
          SchedulerTestDAO.getCountOfScheduledProspectForACampaign(
            campaign_id = CampaignId(createAndStartCampaignData_2.createCampaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData_2.addProspect.map(p => ProspectId(p.id)),
            step_id = StepId(createAndStartCampaignData_2.addStep.step_id),
          ).get
        }

        scheduledCampaignProspectCount_1_camp_2: Int <- Future {
          CampaignProspectTestDAO.getCountOfCampaignProspectSchedulerStatusDue(
            campaign_id = CampaignId(createAndStartCampaignData_2.createCampaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData_2.addProspect.map(p => ProspectId(p.id)),
            current_step_id = StepId(createAndStartCampaignData_2.addStep.step_id),
          ).get
        }

        reInitializeForSecondScheduling_1 <- ReSchedulingFixture.initializeDataForReScheduling(
          orgId = orgId,
          accountId = accountId,
          teamId = teamId,
          emailSetting = initialData.emailSetting.get,
          campaign = createAndStartCampaignData_1.campaign,
          prospectList = createAndStartCampaignData_1.addProspect.map(p => ProspectId(p.id)),
          hoursToSubtractFromLastScheduled = 9
        )

        reInitializeForSecondScheduling_2 <- ReSchedulingFixture.initializeDataForReScheduling(
          orgId = orgId,
          accountId = accountId,
          teamId = teamId,
          emailSetting = initialData.emailSetting.get,
          campaign = createAndStartCampaignData_2.campaign,
          prospectList = createAndStartCampaignData_2.addProspect.map(p => ProspectId(p.id)),
          hoursToSubtractFromLastScheduled = 9
        )

        //Test result after scheduling
        result_2: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
          emailChannelData = EmailChannelData(
            emailSettingId = emailSetting.id.get.emailSettingId
          ),
          teamId = teamId
        )

        scheduledDataCountInScheduler_2_camp_1: List[Int] <- Future {
          SchedulerTestDAO.getCountOfScheduledProspectForACampaign(
            campaign_id = CampaignId(createAndStartCampaignData_1.createCampaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData_1.addProspect.map(p => ProspectId(p.id)),
            step_id = StepId(createAndStartCampaignData_1.addStep.step_id),
          ).get
        }

        scheduledCampaignProspectCount_2_camp_1: Int <- Future {
          CampaignProspectTestDAO.getCountOfCampaignProspectSchedulerStatusDue(
            campaign_id = CampaignId(createAndStartCampaignData_1.createCampaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData_1.addProspect.map(p => ProspectId(p.id)),
            current_step_id = StepId(createAndStartCampaignData_1.addStep.step_id),
          ).get
        }

        scheduledDataCountInScheduler_2_camp_2: List[Int] <- Future {
          SchedulerTestDAO.getCountOfScheduledProspectForACampaign(
            campaign_id = CampaignId(createAndStartCampaignData_2.createCampaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData_2.addProspect.map(p => ProspectId(p.id)),
            step_id = StepId(createAndStartCampaignData_2.addStep.step_id),
          ).get
        }

        scheduledCampaignProspectCount_2_camp_2: Int <- Future {
          CampaignProspectTestDAO.getCountOfCampaignProspectSchedulerStatusDue(
            campaign_id = CampaignId(createAndStartCampaignData_2.createCampaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData_2.addProspect.map(p => ProspectId(p.id)),
            current_step_id = StepId(createAndStartCampaignData_2.addStep.step_id),
          ).get
        }

      } yield {
        (
          SchedulerIntegrationTestResult(
            scheduleTasksData = result_1,
            emailScheduledCountForCampaign = scheduledDataCountInScheduler_1_camp_1.length + scheduledDataCountInScheduler_1_camp_2.length,
            scheduledCampaignProspectCount = scheduledCampaignProspectCount_1_camp_1 + scheduledCampaignProspectCount_1_camp_2
          ),
          SchedulerIntegrationTestResult(
            scheduleTasksData = result_2,
            emailScheduledCountForCampaign = scheduledDataCountInScheduler_2_camp_1.length + scheduledDataCountInScheduler_2_camp_2.length,
            scheduledCampaignProspectCount = scheduledCampaignProspectCount_2_camp_1 + scheduledCampaignProspectCount_2_camp_2
          )
        )
      }
      scheduleTaskData.map(p => {
        assert(
          ((p._1.emailScheduledCountForCampaign + p._2.emailScheduledCountForCampaign) == AppSpecFixture.quota_per_day_email_settings) &&
            ((p._1.scheduledCampaignProspectCount + p._2.scheduledCampaignProspectCount) == AppSpecFixture.quota_per_day_email_settings)
        )
      }).recover({ case e =>
        assert(false)
      })
    }

    it("scheduler test for scheduling a campaign out of schedule time") {
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
      val account: Account = initialData.account
      val emailSetting: EmailSetting = initialData.emailSetting.get
      val teamId: TeamId = TeamId(account.teams.head.team_id)

      val scheduleTaskData: Future[SchedulerIntegrationTestResult] = for {

        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 4,
          schedule_from_time = 86398
        )

        //Test result after scheduling
        result: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
          emailChannelData = EmailChannelData(
            emailSettingId = emailSetting.id.get.emailSettingId
          ),
          teamId = teamId
        )

        scheduledDataCountInScheduler: List[Int] <- Future {
          SchedulerTestDAO.getCountOfScheduledProspectForACampaign(
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            step_id = StepId(createAndStartCampaignData.addStep.step_id),
          ).get
        }

        scheduledCampaignProspectCount: Int <- Future {
          CampaignProspectTestDAO.getCountOfCampaignProspectSchedulerStatusDue(
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            current_step_id = StepId(createAndStartCampaignData.addStep.step_id),
          ).get
        }

      } yield {
        SchedulerIntegrationTestResult(
          scheduleTasksData = result,
          emailScheduledCountForCampaign = scheduledDataCountInScheduler.length,
          scheduledCampaignProspectCount = scheduledCampaignProspectCount
        )
      }
      scheduleTaskData.map(p => {
        assert(p.emailScheduledCountForCampaign == 0
          && p.scheduledCampaignProspectCount == 0)
      }).recover({ case e =>
        assert(false)
      })
    }

    it("scheduler test for scheduling a campaign having 200 prospects") {
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData(
        max_emails_per_prospect_per_day = 100,
        max_emails_per_prospect_per_week = 500,
        max_emails_per_prospect_account_per_day = Option(500),
        max_emails_per_prospect_account_per_week = Option(500)
      ).get
      val account: Account = initialData.account
      val emailSetting: EmailSetting = initialData.emailSetting.get
      val teamId: TeamId = TeamId(account.teams.head.team_id)

      val scheduleTaskData: Future[SchedulerIntegrationTestResult] = for {

        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 200
        )

        //Test result after scheduling
        result: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
          emailChannelData = EmailChannelData(
            emailSettingId = emailSetting.id.get.emailSettingId
          ),
          teamId = teamId
        )

        scheduledDataCountInScheduler: List[Int] <- Future {
          SchedulerTestDAO.getCountOfScheduledProspectForACampaign(
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            step_id = StepId(createAndStartCampaignData.addStep.step_id),
          ).get
        }

        scheduledCampaignProspectCount: Int <- Future {
          CampaignProspectTestDAO.getCountOfCampaignProspectSchedulerStatusDue(
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            current_step_id = StepId(createAndStartCampaignData.addStep.step_id),
          ).get
        }

      } yield {
        SchedulerIntegrationTestResult(
          scheduleTasksData = result,
          emailScheduledCountForCampaign = scheduledDataCountInScheduler.length,
          scheduledCampaignProspectCount = scheduledCampaignProspectCount
        )
      }
      scheduleTaskData.map(p => {
        assert(p.emailScheduledCountForCampaign == 100
          && p.scheduledCampaignProspectCount == 100)
      }).recover({ case e =>
        assert(false)
      })
    }

    it("Scheduler test for scheduling 500 prospect from single campaign and should stop at email_setting limit") {
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData(
        quota_per_day = 300,
        max_emails_per_prospect_per_day = 100,
        max_emails_per_prospect_per_week = 500,
        max_emails_per_prospect_account_per_day = Option(500),
        max_emails_per_prospect_account_per_week = Option(500)
      ).get
      val account: Account = initialData.account
      val emailSetting: EmailSetting = initialData.emailSetting.get
      val orgId: OrgId = OrgId(account.org.id)
      val accountId: AccountId = AccountId(account.internal_id)
      val teamId: TeamId = TeamId(account.teams.head.team_id)

      val scheduleTaskData: Future[(SchedulerIntegrationTestResult, SchedulerIntegrationTestResult)] = for {

        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 500
        )

        //Test result after scheduling
        result_1: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
          emailChannelData = EmailChannelData(
            emailSettingId = emailSetting.id.get.emailSettingId
          ),
          teamId = teamId
        )

        scheduledDataCountInScheduler_1: List[Int] <- Future {
          SchedulerTestDAO.getCountOfScheduledProspectForACampaign(
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            step_id = StepId(createAndStartCampaignData.addStep.step_id),
          ).get
        }

        scheduledCampaignProspectCount_1: Int <- Future {
          CampaignProspectTestDAO.getCountOfCampaignProspectSchedulerStatusDue(
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            current_step_id = StepId(createAndStartCampaignData.addStep.step_id),
          ).get
        }

        reInitializeForSecondScheduling_1 <- ReSchedulingFixture.initializeDataForReScheduling(
          orgId = orgId,
          accountId = accountId,
          teamId = teamId,
          emailSetting = emailSetting,
          campaign = createAndStartCampaignData.campaign,
          prospectList = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id))
        )

        //Test result after scheduling
        result_2: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
          emailChannelData = EmailChannelData(
            emailSettingId = emailSetting.id.get.emailSettingId
          ),
          teamId = teamId
        )

        scheduledDataCountInScheduler_2: List[Int] <- Future {
          SchedulerTestDAO.getCountOfScheduledProspectForACampaign(
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            step_id = StepId(createAndStartCampaignData.addStep.step_id),
          ).get
        }

        scheduledCampaignProspectCount_2: Int <- Future {
          CampaignProspectTestDAO.getCountOfCampaignProspectSchedulerStatusDue(
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            current_step_id = StepId(createAndStartCampaignData.addStep.step_id),
          ).get
        }

      } yield {
        (
          SchedulerIntegrationTestResult(
            scheduleTasksData = result_1,
            emailScheduledCountForCampaign = scheduledDataCountInScheduler_1.length,
            scheduledCampaignProspectCount = scheduledCampaignProspectCount_1
          ),
          SchedulerIntegrationTestResult(
            scheduleTasksData = result_2,
            emailScheduledCountForCampaign = scheduledDataCountInScheduler_2.length,
            scheduledCampaignProspectCount = scheduledCampaignProspectCount_2
          )
        )
      }
      scheduleTaskData.map(p => {
        assert(
          ((p._1.emailScheduledCountForCampaign + p._2.emailScheduledCountForCampaign) == 300) &&
            ((p._1.scheduledCampaignProspectCount + p._2.scheduledCampaignProspectCount) == 300)
        )
      }).recover({ case e =>
        assert(false)
      })
    }

    it("campaign with 500 prospect should stop scheduling at campaign limit 150") {
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData(
        quota_per_day = 300,
        max_emails_per_prospect_per_day = 100,
        max_emails_per_prospect_per_week = 500,
        max_emails_per_prospect_account_per_day = Option(500),
        max_emails_per_prospect_account_per_week = Option(500)
      ).get
      val account: Account = initialData.account
      val emailSetting: EmailSetting = initialData.emailSetting.get
      val teamId: TeamId = TeamId(account.teams.head.team_id)

      val scheduleTaskData: Future[SchedulerIntegrationTestResult] = for {

        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 500
        )

        updateCampaignLimit: Int <- Future {
          CampaignTestDAO.updateCampaignLimit(
            campaignId = CampaignId(createAndStartCampaignData.createCampaign.id),
            campaign_limit = 150
          ).get
        }

        //Test result after scheduling
        result: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
          emailChannelData = EmailChannelData(
            emailSettingId = emailSetting.id.get.emailSettingId
          ),
          teamId = teamId
        )

        scheduledDataCountInScheduler: List[Int] <- Future {
          SchedulerTestDAO.getCountOfScheduledProspectForACampaign(
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            step_id = StepId(createAndStartCampaignData.addStep.step_id),
          ).get
        }

        scheduledCampaignProspectCount: Int <- Future {
          CampaignProspectTestDAO.getCountOfCampaignProspectSchedulerStatusDue(
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            current_step_id = StepId(createAndStartCampaignData.addStep.step_id),
          ).get
        }

      } yield {
        SchedulerIntegrationTestResult(
          scheduleTasksData = result,
          emailScheduledCountForCampaign = scheduledDataCountInScheduler.length,
          scheduledCampaignProspectCount = scheduledCampaignProspectCount
        )
      }
      scheduleTaskData.map(p => {
        assert(p.emailScheduledCountForCampaign == 150
          && p.scheduledCampaignProspectCount == 150)
      }).recover({ case e =>
        assert(false)
      })
    }

    it("campaign with 10 prospect have 8 prospect as completed should schedule 2 prospect") {
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
      val account: Account = initialData.account
      val emailSetting: EmailSetting = initialData.emailSetting.get
      val teamId: TeamId = TeamId(account.teams.head.team_id)

      val scheduleTaskData: Future[SchedulerIntegrationTestResult] = for {

        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 10
        )


        markProspectAsCompleted: List[Int] <- Future {
          CampaignProspectTestDAO.markAsCompleted(
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData.addProspect.take(8).map(p => ProspectId(p.id))
          ).get
        }

        //Test result after scheduling
        result: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
          emailChannelData = EmailChannelData(
            emailSettingId = emailSetting.id.get.emailSettingId
          ),
          teamId = teamId
        )

        scheduledDataCountInScheduler: List[Int] <- Future {
          SchedulerTestDAO.getCountOfScheduledProspectForACampaign(
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            step_id = StepId(createAndStartCampaignData.addStep.step_id),
          ).get
        }

        scheduledCampaignProspectCount: Int <- Future {
          CampaignProspectTestDAO.getCountOfCampaignProspectSchedulerStatusDue(
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            current_step_id = StepId(createAndStartCampaignData.addStep.step_id),
          ).get
        }

      } yield {
        SchedulerIntegrationTestResult(
          scheduleTasksData = result,
          emailScheduledCountForCampaign = scheduledDataCountInScheduler.length,
          scheduledCampaignProspectCount = scheduledCampaignProspectCount
        )
      }
      scheduleTaskData.map(p => {
        assert(p.emailScheduledCountForCampaign == 2
          && p.scheduledCampaignProspectCount == 2)
      }).recover({ case e =>
        assert(false)
      })
    }

    it("campaign with 10 prospect marked 8 prospect as do_not_contact should schedule only 2 emails") {
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
      val account: Account = initialData.account
      val emailSetting: EmailSetting = initialData.emailSetting.get
      val accountId: AccountId = AccountId(account.internal_id)
      val teamId: TeamId = TeamId(account.teams.head.team_id)
      val taId: Long = account.teams.head.access_members.head.ta_id

      val scheduleTaskData: Future[(Seq[Blacklist], Seq[ProspectObject], SchedulerIntegrationTestResult)] = for {

        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 10
        )

        insertedProspectToBlacklist: Either[CreateOrUpdateBlacklistError, Seq[Blacklist]] <- Future {
          blacklistService.createOrUpdateBlacklistService(
            accountId = accountId.id,
            addedByName = "Integration test",
            data = BlacklistCreateEmailsForm(
              emails = createAndStartCampaignData.addProspect.map(p => p.email.get).take(8)
            ),
            is_req_via_dnc_form = false,
            opted_out_from_campaign_id = None,
            opted_out_from_campaign_name = None,
            account = initialData.account,
            auditRequestLogId = None,
            isApiCall = false,
            level = BlacklistCreateOrDeleteApiLevel.Team(team_id = teamId, org_id = OrgId(account.org.id), ta_id = taId),
            team_id = Some(teamId),
            Logger = Logger
          )
        }

        insertedBlacklist <- {
          insertedProspectToBlacklist match {
            case Left(value) =>
              Future.failed(new Exception(s"Error Occurred while adding prospect to blacklist"))
            case Right(blacklist) =>
              Future.successful(blacklist)
          }
        }

        //Test result after scheduling
        result: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
          emailChannelData = EmailChannelData(
            emailSettingId = emailSetting.id.get.emailSettingId
          ),
          teamId = teamId
        )

        scheduledDataCountInScheduler: List[Int] <- Future {
          SchedulerTestDAO.getCountOfScheduledProspectForACampaign(
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            step_id = StepId(createAndStartCampaignData.addStep.step_id),
          ).get
        }

        scheduledCampaignProspectCount: Int <- Future {
          CampaignProspectTestDAO.getCountOfCampaignProspectSchedulerStatusDue(
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            current_step_id = StepId(createAndStartCampaignData.addStep.step_id),
          ).get
        }

        scheduledProspectObject: Seq[ProspectObject] <- Future {
          prospectDAOService.find(
            byProspectIds = scheduledDataCountInScheduler.map(p => p.toLong),
            teamId = initialData.account.teams.head.team_id,
            Logger = Logger
          ).get
        }

      } yield {
        (insertedBlacklist,
          scheduledProspectObject,
          SchedulerIntegrationTestResult(
            scheduleTasksData = result,
            emailScheduledCountForCampaign = scheduledDataCountInScheduler.length,
            scheduledCampaignProspectCount = scheduledCampaignProspectCount
          )
        )
      }

      scheduleTaskData.map(p => {
        val scheduledProspectEmails = p._2.map(p => p.email)
        val scheduledBlacklistedEmails = p._1.filter(p => scheduledProspectEmails.map(p => p.get).contains(p.name))
        assert(scheduledBlacklistedEmails.isEmpty
          && p._3.emailScheduledCountForCampaign == 2
          && p._3.scheduledCampaignProspectCount == 2)
      }).recover({ case e =>
        assert(false)
      })
    }

    it("Scheduler test for not scheduling prospects of same domain once we get a reply from one of the domains email (5 prospects in campaign 1 prospect marked as replied so no prospects should get scheduled)") {
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
      val account: Account = initialData.account
      val emailSetting: EmailSetting = initialData.emailSetting.get
      val accountId: AccountId = AccountId(account.internal_id)
      val teamId: TeamId = TeamId(account.teams.head.team_id)
      val emailSettingId = EmailSettingId(emailSetting.id.get.emailSettingId)

      val emailReplyStatus: EmailReplyStatus = EmailReplyStatus(
        replyType = EmailReplyType.NOT_CATEGORIZED,
        isReplied = true,
        isUnsubscribeRequest = false,
        isAutoReply = false,
        isOutOfOfficeReply = false,
        isInvalidEmail = false,
        isForwarded = false,
        bouncedData = None
      )
      val scheduleTaskData: Future[(SchedulerIntegrationTestResult, List[Int], Seq[ProspectObject], List[String])] = for {

        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
        )

        emailMessageTracked: EmailMessageTracked <- Future {
          generateEmailMessageTracked(
            teamId = teamId,
            accountId = accountId,
            campaignId = CampaignId(createAndStartCampaignData.createCampaign.id),
            stepId = StepId(createAndStartCampaignData.addStep.step_id),
            campaignName = CampaignName(createAndStartCampaignData.createCampaign.name),
            emailSettingId = emailSettingId,
            iEmailAddress = IEmailAddress(
              email = emailSetting.email
            ),
            to_emails = Seq(createAndStartCampaignData.addProspect.take(1).head.email.get),
            emailReplyStatus = emailReplyStatus
          )
        }

        handleReply: DBEmailMessagesSavedResponse <- Future {
          emailReplyTrackingModelV2.saveEmailsAndRepliesFromInboxV3(
            accountId = accountId.id,
            team_id = teamId.id,
            emailMessages = Seq(
              emailMessageTracked
            ),
            inboxEmailSetting = initialData.emailSetting.get,
            replyHandling = ReplyHandling.PAUSE_ALL_PROSPECT_ACCOUNT_CAMPAIGNS_ON_REPLY,
            account = initialData.account,
            senderEmails = Seq(initialData.emailSetting.get.email),
            adminReplyFromSRInbox = true,
            auditRequestLogId = "",
            markProspectAsCompleted = true
          ).get
        }

        //Test result after scheduling
        result: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
          emailChannelData = EmailChannelData(
            emailSettingId = emailSetting.id.get.emailSettingId
          ),
          teamId = teamId
        )

        scheduledDataCountInScheduler: List[Int] <- Future {
          SchedulerTestDAO.getCountOfScheduledProspectForACampaign(
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            step_id = StepId(createAndStartCampaignData.addStep.step_id),
          ).get
        }

        scheduledCampaignProspectCount: Int <- Future {
          CampaignProspectTestDAO.getCountOfCampaignProspectSchedulerStatusDue(
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            current_step_id = StepId(createAndStartCampaignData.addStep.step_id),
          ).get
        }

        getProspectCompletedBecauseReplyHandling: List[Int] <- Future {
          CampaignProspectTestDAO.getProspectCompletedBecauseReplyHandling(
            teamId = teamId,
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            replyHandling = ReplyHandling.PAUSE_ALL_PROSPECT_ACCOUNT_CAMPAIGNS_ON_REPLY
          ).get
        }

        getRepliedProspectFromCampaign: List[String] <- Future {
          CampaignProspectTestDAO.getRepliedProspectEmailFromCampaign(
            teamId = teamId,
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id)
          ).get
        }

      } yield {
        (
          SchedulerIntegrationTestResult(
            scheduleTasksData = result,
            emailScheduledCountForCampaign = scheduledDataCountInScheduler.length,
            scheduledCampaignProspectCount = scheduledCampaignProspectCount
          ),
          getProspectCompletedBecauseReplyHandling,
          createAndStartCampaignData.addProspect,
          getRepliedProspectFromCampaign
        )
      }
      scheduleTaskData.map(p => {
        assert(p._1.emailScheduledCountForCampaign == 0
          && p._1.scheduledCampaignProspectCount == 0
          && p._2.sorted == p._3.map(p => p.id.toInt).sorted
          && p._2.length == 5
          && p._3.length == 5
          && p._4.head.toLowerCase() == p._3.take(1).head.email.get.toLowerCase()
        )
      }).recover({ case e =>
        assert(false)
      })
    }

    it("Scheduler test for scheduling other prospects of same domain once we get a reply from one of the domains email as out_off_office(5 prospects in campaign 1 prospect marked as out_of_office so 4 prospects should get scheduled )") {
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
      val account: Account = initialData.account
      val emailSetting: EmailSetting = initialData.emailSetting.get
      val accountId: AccountId = AccountId(account.internal_id)
      val teamId: TeamId = TeamId(account.teams.head.team_id)
      val emailSettingId = EmailSettingId(emailSetting.id.get.emailSettingId)

      val emailReplyStatus: EmailReplyStatus = EmailReplyStatus(
        replyType = EmailReplyType.NOT_CATEGORIZED,
        isReplied = false,
        isUnsubscribeRequest = false,
        isAutoReply = false,
        isOutOfOfficeReply = true,
        isInvalidEmail = false,
        isForwarded = false,
        bouncedData = None
      )
      val scheduleTaskData: Future[(SchedulerIntegrationTestResult, List[Int], Seq[ProspectObject], List[String])] = for {

        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData
        )

        emailMessageTracked: EmailMessageTracked <- Future {
          generateEmailMessageTracked(
            teamId = teamId,
            accountId = accountId,
            campaignId = CampaignId(createAndStartCampaignData.createCampaign.id),
            stepId = StepId(createAndStartCampaignData.addStep.step_id),
            campaignName = CampaignName(createAndStartCampaignData.createCampaign.name),
            emailSettingId = emailSettingId,
            iEmailAddress = IEmailAddress(
              email = emailSetting.email
            ),
            to_emails = Seq(createAndStartCampaignData.addProspect.take(1).head.email.get),
            emailReplyStatus = emailReplyStatus
          )
        }

        handleReply: DBEmailMessagesSavedResponse <- Future {
          emailReplyTrackingModelV2.saveEmailsAndRepliesFromInboxV3(
            accountId = accountId.id,
            team_id = teamId.id,
            emailMessages = Seq(
              emailMessageTracked
            ),
            inboxEmailSetting = initialData.emailSetting.get,
            replyHandling = ReplyHandling.PAUSE_ALL_PROSPECT_ACCOUNT_CAMPAIGNS_ON_REPLY,
            account = initialData.account,
            senderEmails = Seq(initialData.emailSetting.get.email),
            adminReplyFromSRInbox = true,
            auditRequestLogId = "",
            markProspectAsCompleted = true
          ).get
        }

        //Test result after scheduling
        result: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
          emailChannelData = EmailChannelData(
            emailSettingId = emailSetting.id.get.emailSettingId
          ),
          teamId = teamId
        )

        scheduledDataCountInScheduler: List[Int] <- Future {
          SchedulerTestDAO.getCountOfScheduledProspectForACampaign(
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            step_id = StepId(createAndStartCampaignData.addStep.step_id),
          ).get
        }

        scheduledCampaignProspectCount: Int <- Future {
          CampaignProspectTestDAO.getCountOfCampaignProspectSchedulerStatusDue(
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            current_step_id = StepId(createAndStartCampaignData.addStep.step_id),
          ).get
        }

        getProspectCompletedBecauseReplyHandling: List[Int] <- Future {
          CampaignProspectTestDAO.getProspectCompletedBecauseReplyHandling(
            teamId = teamId,
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            replyHandling = ReplyHandling.PAUSE_ALL_PROSPECT_ACCOUNT_CAMPAIGNS_ON_REPLY
          ).get
        }

        getOutOfOfficeProspectEmailFromCampaign: List[String] <- Future {
          CampaignProspectTestDAO.getOutOfOfficeProspectEmailFromCampaign(
            teamId = teamId,
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id)
          ).get
        }

      } yield {
        (
          SchedulerIntegrationTestResult(
            scheduleTasksData = result,
            emailScheduledCountForCampaign = scheduledDataCountInScheduler.length,
            scheduledCampaignProspectCount = scheduledCampaignProspectCount
          ),
          getProspectCompletedBecauseReplyHandling,
          createAndStartCampaignData.addProspect,
          getOutOfOfficeProspectEmailFromCampaign
        )
      }
      scheduleTaskData.map(p => {
        val prospectEmailMarkedAsOutOfOffice = p._3.take(1).head.email.get.toLowerCase()
        assert(p._1.emailScheduledCountForCampaign == 4
          && p._1.scheduledCampaignProspectCount == 4
          && p._2.length == 1
          && p._4.head.toLowerCase() == prospectEmailMarkedAsOutOfOffice
        )
      }).recover({ case e =>
        assert(false)
      })
    }

    it("Scheduler test for scheduling other prospects of same domain once we get a reply from one of the domains email as bounced(5 prospects in campaign 1 prospect marked as bounce so 4 prospects should get scheduled )") {
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
      val account: Account = initialData.account
      val emailSetting: EmailSetting = initialData.emailSetting.get
      val accountId: AccountId = AccountId(account.internal_id)
      val teamId: TeamId = TeamId(account.teams.head.team_id)
      val emailSettingId = EmailSettingId(emailSetting.id.get.emailSettingId)

      val emailReplyStatus: EmailReplyStatus = EmailReplyStatus(
        replyType = EmailReplyType.NOT_CATEGORIZED,
        isReplied = false,
        isUnsubscribeRequest = false,
        isAutoReply = false,
        isOutOfOfficeReply = false,
        isInvalidEmail = false,
        isForwarded = false,
        bouncedData = Option(BounceData(
          bounced_at = DateTime.now().minusDays(1),
          bounce_type = EmailReplyBounceType.EmailAddressNotFound,
          bounce_reason = "Email Address not found",
          is_soft_bounced = false
        ))
      )
      val scheduleTaskData: Future[ResultForBouncedEmailTestAssert] = for {

        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData
        )

        emailMessageTracked: EmailMessageTracked <- Future {
          generateEmailMessageTracked(
            teamId = teamId,
            accountId = accountId,
            campaignId = CampaignId(createAndStartCampaignData.createCampaign.id),
            stepId = StepId(createAndStartCampaignData.addStep.step_id),
            campaignName = CampaignName(createAndStartCampaignData.createCampaign.name),
            emailSettingId = emailSettingId,
            iEmailAddress = IEmailAddress(
              email = emailSetting.email
            ),
            to_emails = Seq(createAndStartCampaignData.addProspect.head.email.get),
            emailReplyStatus = emailReplyStatus
          )
        }

        handleReply: DBEmailMessagesSavedResponse <- Future {
          emailReplyTrackingModelV2.saveEmailsAndRepliesFromInboxV3(
            accountId = accountId.id,
            team_id = teamId.id,
            emailMessages = Seq(
              emailMessageTracked
            ),
            inboxEmailSetting = initialData.emailSetting.get,
            replyHandling = ReplyHandling.PAUSE_ALL_PROSPECT_ACCOUNT_CAMPAIGNS_ON_REPLY,
            account = initialData.account,
            senderEmails = Seq(initialData.emailSetting.get.email),
            adminReplyFromSRInbox = true,
            auditRequestLogId = "",
            markProspectAsCompleted = true
          ).get
        }

        //Test result after scheduling
        result: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
          emailChannelData = EmailChannelData(
            emailSettingId = emailSetting.id.get.emailSettingId
          ),
          teamId = teamId
        )

        scheduledDataCountInScheduler: List[Int] <- Future {
          SchedulerTestDAO.getCountOfScheduledProspectForACampaign(
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            step_id = StepId(createAndStartCampaignData.addStep.step_id),
          ).get
        }

        scheduledCampaignProspectCount: Int <- Future {
          CampaignProspectTestDAO.getCountOfCampaignProspectSchedulerStatusDue(
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            current_step_id = StepId(createAndStartCampaignData.addStep.step_id),
          ).get
        }

        getProspectCompletedBecauseReplyHandling: List[Int] <- Future {
          CampaignProspectTestDAO.getProspectCompletedBecauseReplyHandling(
            teamId = teamId,
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            replyHandling = ReplyHandling.PAUSE_ALL_PROSPECT_ACCOUNT_CAMPAIGNS_ON_REPLY
          ).get
        }

        getBouncedProspectEmailFromCampaign: List[String] <- Future {
          CampaignProspectTestDAO.getBouncedProspectEmailFromCampaign(
            teamId = teamId,
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id)
          ).get
        }

      } yield {
        ResultForBouncedEmailTestAssert(
          schedulerIntegrationTestResult = SchedulerIntegrationTestResult(
            scheduleTasksData = result,
            emailScheduledCountForCampaign = scheduledDataCountInScheduler.length,
            scheduledCampaignProspectCount = scheduledCampaignProspectCount
          ),
          addedProspect = createAndStartCampaignData.addProspect.map(p => p.email.get),
          completedProspectByReplyHandling = getProspectCompletedBecauseReplyHandling,
          bouncedProspectEmails = getBouncedProspectEmailFromCampaign
        )
      }
      scheduleTaskData.map(p => {
        val prospectEmailMarkedAsBounced = p.addedProspect.head.toLowerCase()
        assert(p.schedulerIntegrationTestResult.scheduleTasksData.saved_tasks_count == 4
          && p.schedulerIntegrationTestResult.emailScheduledCountForCampaign == 4
          && p.schedulerIntegrationTestResult.scheduledCampaignProspectCount == 4
          && p.completedProspectByReplyHandling.length == 1
          && p.bouncedProspectEmails.head.toLowerCase() == prospectEmailMarkedAsBounced
        )
      }).recover({ case e =>
        assert(false)
      })
    }

    it("Scheduler test for scheduling other prospects of same domain once we get a reply from one of the domains email as do_not_contact (5 prospects in campaign 1 prospect marked as do_not_contact so 4 prospects should get scheduled )") {
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
      val account: Account = initialData.account
      val emailSetting: EmailSetting = initialData.emailSetting.get
      val accountId: AccountId = AccountId(account.internal_id)
      val teamId: TeamId = TeamId(account.teams.head.team_id)
      val emailSettingId = EmailSettingId(emailSetting.id.get.emailSettingId)
      val emailReplyStatus: EmailReplyStatus = EmailReplyStatus(
        replyType = EmailReplyType.DO_NOT_CONTACT,
        isReplied = false,
        isUnsubscribeRequest = false,
        isAutoReply = false,
        isOutOfOfficeReply = false,
        isInvalidEmail = false,
        isForwarded = false,
        bouncedData = None
      )
      val scheduleTaskData: Future[ResultForBlacklistedEmailTestAssert] = for {

        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData
        )

        emailMessageTracked: EmailMessageTracked <- Future {
          generateEmailMessageTracked(
            teamId = teamId,
            accountId = accountId,
            campaignId = CampaignId(createAndStartCampaignData.createCampaign.id),
            stepId = StepId(createAndStartCampaignData.addStep.step_id),
            campaignName = CampaignName(createAndStartCampaignData.createCampaign.name),
            emailSettingId = emailSettingId,
            iEmailAddress = IEmailAddress(
              email = emailSetting.email
            ),
            to_emails = Seq(createAndStartCampaignData.addProspect.head.email.get),
            emailReplyStatus = emailReplyStatus
          )
        }

        handleReply: DBEmailMessagesSavedResponse <- Future {
          emailReplyTrackingModelV2.saveEmailsAndRepliesFromInboxV3(
            accountId = accountId.id,
            team_id = teamId.id,
            emailMessages = Seq(
              emailMessageTracked
            ),
            inboxEmailSetting = initialData.emailSetting.get,
            replyHandling = ReplyHandling.PAUSE_ALL_PROSPECT_ACCOUNT_CAMPAIGNS_ON_REPLY,
            account = initialData.account,
            senderEmails = Seq(initialData.emailSetting.get.email),
            adminReplyFromSRInbox = true,
            auditRequestLogId = "",
            markProspectAsCompleted = true
          ).get
        }

        //Test result after scheduling
        result: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
          emailChannelData = EmailChannelData(
            emailSettingId = emailSetting.id.get.emailSettingId
          ),
          teamId = teamId
        )

        scheduledDataCountInScheduler: List[Int] <- Future {
          SchedulerTestDAO.getCountOfScheduledProspectForACampaign(
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            step_id = StepId(createAndStartCampaignData.addStep.step_id),
          ).get
        }

        scheduledCampaignProspectCount: Int <- Future {
          CampaignProspectTestDAO.getCountOfCampaignProspectSchedulerStatusDue(
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            current_step_id = StepId(createAndStartCampaignData.addStep.step_id),
          ).get
        }

        getProspectCompletedBecauseReplyHandling: List[Int] <- Future {
          CampaignProspectTestDAO.getProspectCompletedBecauseReplyHandling(
            teamId = teamId,
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            replyHandling = ReplyHandling.PAUSE_ALL_PROSPECT_ACCOUNT_CAMPAIGNS_ON_REPLY
          ).get
        }

        getBlacklistedEmailForTeam: List[String] <- Future {
          BlacklistTestDAO.getBlacklistedEmailForTeam(
            teamId = teamId,
            accountId = accountId
          ).get
        }

      } yield {
        ResultForBlacklistedEmailTestAssert(
          schedulerIntegrationTestResult = SchedulerIntegrationTestResult(
            scheduleTasksData = result,
            emailScheduledCountForCampaign = scheduledDataCountInScheduler.length,
            scheduledCampaignProspectCount = scheduledCampaignProspectCount
          ),
          addedProspect = createAndStartCampaignData.addProspect.map(p => p.email.get),
          completedProspectByReplyHandling = getProspectCompletedBecauseReplyHandling,
          blacklistEmails = getBlacklistedEmailForTeam
        )
      }
      scheduleTaskData.map(p => {
//        val emailMarkedAsBlacklisted = p.addedProspect.head.toLowerCase()
        assert(p.schedulerIntegrationTestResult.emailScheduledCountForCampaign == 4
          && p.schedulerIntegrationTestResult.scheduledCampaignProspectCount == 4
          && p.completedProspectByReplyHandling.isEmpty
//          && p.blacklistEmails.head.toLowerCase() == emailMarkedAsBlacklisted
        )
      }).recover({ case e =>
        println(LogHelpers.getStackTraceAsString(e))
        assert(false)
      })
    }

    it("Scheduler test for scheduling new and followups and check ratio when email priority is CampaignEmailPriority.EQUAL") {
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData(
        quota_per_day = 40,
        max_emails_per_prospect_per_day = 100,
        max_emails_per_prospect_per_week = 500,
        max_emails_per_prospect_account_per_day = Option(500),
        max_emails_per_prospect_account_per_week = Option(500),
      ).get
      val account: Account = initialData.account
      val emailSetting: EmailSetting = initialData.emailSetting.get
      val orgId: OrgId = OrgId(account.org.id)
      val accountId: AccountId = AccountId(account.internal_id)
      val teamId: TeamId = TeamId(account.teams.head.team_id)
      val taId: Long = account.teams.head.access_members.head.ta_id

      val scheduleTaskData: Future[(SchedulerIntegrationTestResult, SchedulerIntegrationTestResult)] = for {

        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 20
        )
        //Test result after scheduling
        result_1: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
          emailChannelData = EmailChannelData(
            emailSettingId = emailSetting.id.get.emailSettingId
          ),
          teamId = teamId
        )

        scheduledDataCountInScheduler_1: List[Int] <- Future {
          SchedulerTestDAO.getCountOfScheduledProspectForACampaign(
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            step_id = StepId(createAndStartCampaignData.addStep.step_id),
          ).get
        }

        scheduledCampaignProspectCount_1: Int <- Future {
          CampaignProspectTestDAO.getCountOfCampaignProspectSchedulerStatusDue(
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            current_step_id = StepId(createAndStartCampaignData.addStep.step_id),
          ).get
        }

        //add auto email step to campaign
        addStep_2: CampaignStepVariant <- {
          CreateStepForCampaignFixture.createAutoEmailStepForCampaign(
            orgId = orgId,
            teamId = teamId,
            accountId = accountId,
            taId = taId,
            campaignId = CampaignId(createAndStartCampaignData.createCampaign.id),
            parentId = createAndStartCampaignData.addStep.step_id
          )
        }

        // add prospect to campaign
        addProspect_2: Seq[ProspectObject] <- {
          ProspectFixtureForIntegrationTest.createUpdateOrAssignProspectFuture(
            campaignId = Option(CampaignId(createAndStartCampaignData.createCampaign.id)),
            accountId = accountId,
            teamId = teamId,
            account = account,
            generateProspectCountIfNoGivenProspect = 20
          )
        }

        reInitializeForSecondScheduling <- ReSchedulingFixture.initializeDataForReScheduling(
          orgId = orgId,
          accountId = accountId,
          teamId = teamId,
          emailSetting = emailSetting,
          campaign = createAndStartCampaignData.campaign,
          prospectList = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id))
        )

        //Test result after scheduling
        result_2: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
          emailChannelData = EmailChannelData(
            emailSettingId = emailSetting.id.get.emailSettingId
          ),
          teamId = teamId
        )

        scheduledDataCountInScheduler_2: List[Int] <- Future {
          SchedulerTestDAO.getCountOfScheduledProspectForACampaign(
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            teamId = teamId,
            prospect_ids = addProspect_2.map(p => ProspectId(p.id)),
            step_id = StepId(addStep_2.step_id),
          ).get
        }

        scheduledCampaignProspectCount_2: Int <- Future {
          CampaignProspectTestDAO.getCountOfCampaignProspectSchedulerStatusDue(
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            teamId = teamId,
            prospect_ids = addProspect_2.map(p => ProspectId(p.id)),
            current_step_id = StepId(addStep_2.step_id),
          ).get
        }

      } yield {
        (SchedulerIntegrationTestResult(
          scheduleTasksData = result_1,
          emailScheduledCountForCampaign = scheduledDataCountInScheduler_1.length,
          scheduledCampaignProspectCount = scheduledCampaignProspectCount_1
        ),
          SchedulerIntegrationTestResult(
            scheduleTasksData = result_2,
            emailScheduledCountForCampaign = scheduledDataCountInScheduler_2.length,
            scheduledCampaignProspectCount = scheduledCampaignProspectCount_2
          )
        )
      }
      scheduleTaskData.map(p => {
        val followUpProspectScheduled = (p._2.scheduleTasksData.saved_tasks_count - p._2.scheduledCampaignProspectCount).toFloat
        val totalScheduledProspect = (p._1.scheduleTasksData.saved_tasks_count + p._2.scheduleTasksData.saved_tasks_count)
        assert((followUpProspectScheduled / totalScheduledProspect).equals(0.5.toFloat))
      }).recover({ case e =>
        assert(false)
      })
    }

    it("Scheduler test for scheduling new and followups and check ratio when email priority is CampaignEmailPriority.FIRST_EMAIL") {
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData(
        max_emails_per_prospect_per_day = 100,
        max_emails_per_prospect_per_week = 500,
        max_emails_per_prospect_account_per_day = Option(500),
        max_emails_per_prospect_account_per_week = Option(500),
      ).get
      val account: Account = initialData.account
      val emailSetting: EmailSetting = initialData.emailSetting.get
      val orgId: OrgId = OrgId(account.org.id)
      val accountId: AccountId = AccountId(account.internal_id)
      val teamId: TeamId = TeamId(account.teams.head.team_id)
      val taId: Long = account.teams.head.access_members.head.ta_id

      val scheduleTaskData: Future[(SchedulerIntegrationTestResult, SchedulerIntegrationTestResult)] = for {

        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 50,
          email_priority = CampaignEmailPriority.FIRST_EMAIL
        )

        //Test result after scheduling
        result_1: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
          emailChannelData = EmailChannelData(
            emailSettingId = emailSetting.id.get.emailSettingId
          ),
          teamId = teamId
        )

        scheduledDataCountInScheduler_1: List[Int] <- Future {
          SchedulerTestDAO.getCountOfScheduledProspectForACampaign(
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            step_id = StepId(createAndStartCampaignData.addStep.step_id),
          ).get
        }

        scheduledCampaignProspectCount_1: Int <- Future {
          CampaignProspectTestDAO.getCountOfCampaignProspectSchedulerStatusDue(
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            current_step_id = StepId(createAndStartCampaignData.addStep.step_id),
          ).get
        }

        //add auto email step to campaign
        addStep_2: CampaignStepVariant <- {
          CreateStepForCampaignFixture.createAutoEmailStepForCampaign(
            orgId = orgId,
            teamId = teamId,
            accountId = accountId,
            taId = taId,
            campaignId = CampaignId(createAndStartCampaignData.createCampaign.id),
            parentId = createAndStartCampaignData.addStep.step_id
          )
        }

        // add prospect to campaign
        addProspect_2: Seq[ProspectObject] <- {
          ProspectFixtureForIntegrationTest.createUpdateOrAssignProspectFuture(
            campaignId = Option(CampaignId(createAndStartCampaignData.createCampaign.id)),
            accountId = accountId,
            teamId = teamId,
            account = account,
            generateProspectCountIfNoGivenProspect = 100
          )
        }

        reInitializeForSecondScheduling <- ReSchedulingFixture.initializeDataForReScheduling(
          orgId = orgId,
          accountId = accountId,
          teamId = teamId,
          emailSetting = emailSetting,
          campaign = createAndStartCampaignData.campaign,
          prospectList = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id))
        )

        updateEmailScheduledScheduledAt <- Future {
          EmailScheduleTestDAO.updateScheduledAt(
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            hoursToSubtractFromLastScheduled = 36,
          ).get
        }

        //Test result after scheduling
        result_2: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
          emailChannelData = EmailChannelData(
            emailSettingId = emailSetting.id.get.emailSettingId
          ),
          teamId = teamId
        )

        scheduledDataCountInScheduler_2: List[Int] <- Future {
          SchedulerTestDAO.getCountOfScheduledProspectForACampaign(
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            teamId = teamId,
            prospect_ids = addProspect_2.map(p => ProspectId(p.id)),
            step_id = StepId(createAndStartCampaignData.addStep.step_id),
          ).get
        }

        scheduledCampaignProspectCount_2: Int <- Future {
          CampaignProspectTestDAO.getCountOfCampaignProspectSchedulerStatusDue(
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            teamId = teamId,
            prospect_ids = addProspect_2.map(p => ProspectId(p.id)),
            current_step_id = StepId(createAndStartCampaignData.addStep.step_id),
          ).get
        }

      } yield {
        (SchedulerIntegrationTestResult(
          scheduleTasksData = result_1,
          emailScheduledCountForCampaign = scheduledDataCountInScheduler_1.length,
          scheduledCampaignProspectCount = scheduledCampaignProspectCount_1
        ),
          SchedulerIntegrationTestResult(
            scheduleTasksData = result_2,
            emailScheduledCountForCampaign = scheduledDataCountInScheduler_2.length,
            scheduledCampaignProspectCount = scheduledCampaignProspectCount_2
          )
        )
      }
      scheduleTaskData.map(p => {
        val followUpProspectScheduled = (p._2.scheduleTasksData.saved_tasks_count - p._2.scheduledCampaignProspectCount).toFloat
        val totalScheduledProspect = (p._2.scheduleTasksData.saved_tasks_count)
        assert((followUpProspectScheduled / totalScheduledProspect).equals(0.3.toFloat))
      }).recover({ case e =>
        assert(false)
      })
    }

    it("Scheduler test for scheduling new and followups and check ratio when email priority is CampaignEmailPriority.FOLLOWUP_EMAILS") {
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData(
        quota_per_day = 3,
        max_emails_per_prospect_per_day = 100,
        max_emails_per_prospect_per_week = 500,
        max_emails_per_prospect_account_per_day = Option(500),
        max_emails_per_prospect_account_per_week = Option(500),
      ).get
      val account: Account = initialData.account
      val emailSetting: EmailSetting = initialData.emailSetting.get
      val orgId: OrgId = OrgId(account.org.id)
      val accountId: AccountId = AccountId(account.internal_id)
      val teamId: TeamId = TeamId(account.teams.head.team_id)
      val taId: Long = account.teams.head.access_members.head.ta_id

      val scheduleTaskData: Future[(SchedulerIntegrationTestResult, SchedulerIntegrationTestResult)] = for {

        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 20,
          email_priority = CampaignEmailPriority.FOLLOWUP_EMAILS
        )

        //Test result after scheduling
        result_1: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
          emailChannelData = EmailChannelData(
            emailSettingId = emailSetting.id.get.emailSettingId
          ),
          teamId = teamId
        )

        scheduledDataCountInScheduler_1: List[Int] <- Future {
          SchedulerTestDAO.getCountOfScheduledProspectForACampaign(
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            step_id = StepId(createAndStartCampaignData.addStep.step_id),
          ).get
        }

        scheduledCampaignProspectCount_1: Int <- Future {
          CampaignProspectTestDAO.getCountOfCampaignProspectSchedulerStatusDue(
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            current_step_id = StepId(createAndStartCampaignData.addStep.step_id),
          ).get
        }

        updateDailyQuota: Int <- Future {
          EmailSettingTestDAO.updateEmailSettingDailyQuota(
            emailSettingId = initialData.emailSetting.get.id.get,
            quota_per_day = 10
          ).get
        }

        reInitializeForSecondScheduling <- ReSchedulingFixture.initializeDataForReScheduling(
          orgId = orgId,
          accountId = accountId,
          teamId = teamId,
          emailSetting = emailSetting,
          campaign = createAndStartCampaignData.campaign,
          prospectList = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id))
        )

        //add auto email step to campaign
        addStep_2: CampaignStepVariant <- {
          CreateStepForCampaignFixture.createAutoEmailStepForCampaign(
            orgId = orgId,
            teamId = teamId,
            accountId = accountId,
            taId = taId,
            campaignId = CampaignId(createAndStartCampaignData.createCampaign.id),
            parentId = createAndStartCampaignData.addStep.step_id
          )
        }

        // add prospect to campaign
        addProspect_2: Seq[ProspectObject] <- {
          ProspectFixtureForIntegrationTest.createUpdateOrAssignProspectFuture(
            campaignId = Option(CampaignId(createAndStartCampaignData.createCampaign.id)),
            accountId = accountId,
            teamId = teamId,
            account = account,
            generateProspectCountIfNoGivenProspect = 10
          )
        }

        //Test result after scheduling
        result_2: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
          emailChannelData = EmailChannelData(
            emailSettingId = emailSetting.id.get.emailSettingId
          ),
          teamId = teamId
        )

        scheduledDataCountInScheduler_2: List[Int] <- Future {
          SchedulerTestDAO.getCountOfScheduledProspectForACampaign(
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            teamId = teamId,
            prospect_ids = addProspect_2.map(p => ProspectId(p.id)),
            step_id = StepId(createAndStartCampaignData.addStep.step_id),
          ).get
        }

        scheduledCampaignProspectCount_2: Int <- Future {
          CampaignProspectTestDAO.getCountOfCampaignProspectSchedulerStatusDue(
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            teamId = teamId,
            prospect_ids = addProspect_2.map(p => ProspectId(p.id)),
            current_step_id = StepId(createAndStartCampaignData.addStep.step_id),
          ).get
        }


      } yield {
        (SchedulerIntegrationTestResult(
          scheduleTasksData = result_1,
          emailScheduledCountForCampaign = scheduledDataCountInScheduler_1.length,
          scheduledCampaignProspectCount = scheduledCampaignProspectCount_1
        ),
          SchedulerIntegrationTestResult(
            scheduleTasksData = result_2,
            emailScheduledCountForCampaign = scheduledDataCountInScheduler_2.length,
            scheduledCampaignProspectCount = scheduledCampaignProspectCount_2
          )
        )
      }
      scheduleTaskData.map(p => {
        val followUpProspectScheduled = (p._2.scheduleTasksData.saved_tasks_count - p._2.scheduledCampaignProspectCount).toFloat
        val totalScheduledProspect = (p._1.scheduleTasksData.saved_tasks_count + p._2.scheduleTasksData.saved_tasks_count)
        assert((followUpProspectScheduled / totalScheduledProspect).equals(0.7.toFloat))
      }).recover({ case e =>
        println(s"${LogHelpers.getStackTraceAsString(e)}")
        assert(false)
      })
    }

    it("Scheduler test for respecting step delay") {
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData(
        max_emails_per_prospect_per_day = 100,
        max_emails_per_prospect_per_week = 500,
        max_emails_per_prospect_account_per_day = Option(500),
        max_emails_per_prospect_account_per_week = Option(500),
      ).get
      val account: Account = initialData.account
      val emailSetting: EmailSetting = initialData.emailSetting.get
      val orgId: OrgId = OrgId(account.org.id)
      val accountId: AccountId = AccountId(account.internal_id)
      val teamId: TeamId = TeamId(account.teams.head.team_id)
      val taId: Long = account.teams.head.access_members.head.ta_id

      val scheduleTaskData: Future[(SchedulerIntegrationTestResult, SchedulerIntegrationTestResult)] = for {

        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 10
        )

        //Test result after scheduling
        result_1: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
          emailChannelData = EmailChannelData(
            emailSettingId = emailSetting.id.get.emailSettingId
          ),
          teamId = teamId
        )

        //add auto email step to campaign
        addStep_2: CampaignStepVariant <- {
          CreateStepForCampaignFixture.createAutoEmailStepForCampaign(
            orgId = orgId,
            teamId = teamId,
            accountId = accountId,
            taId = taId,
            campaignId = CampaignId(createAndStartCampaignData.createCampaign.id),
            stepDelay = 176400,
            parentId = createAndStartCampaignData.addStep.step_id
          )
        }

        reInitializeForSecondScheduling_1: Int <- ReSchedulingFixture.initializeDataForReScheduling(
          orgId = orgId,
          accountId = accountId,
          teamId = teamId,
          emailSetting = emailSetting,
          campaign = createAndStartCampaignData.campaign,
          prospectList = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
          hoursToSubtractFromLastScheduled = 12
        )

        //Test result after scheduling
        result_2: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
          emailChannelData = EmailChannelData(
            emailSettingId = emailSetting.id.get.emailSettingId
          ),
          teamId = teamId
        )

        scheduledDataCountInScheduler_2: List[Int] <- Future {
          SchedulerTestDAO.getCountOfScheduledProspectForACampaign(
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            step_id = StepId(addStep_2.step_id),
          ).get
        }

        scheduledCampaignProspectCount_2: Int <- Future {
          CampaignProspectTestDAO.getCountOfCampaignProspectSchedulerStatusDue(
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            current_step_id = StepId(addStep_2.step_id),
          ).get
        }

        reInitializeForSecondScheduling_2: Int <- ReSchedulingFixture.initializeDataForReScheduling(
          orgId = orgId,
          accountId = accountId,
          teamId = teamId,
          emailSetting = emailSetting,
          campaign = createAndStartCampaignData.campaign,
          prospectList = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
          hoursToSubtractFromLastScheduled = 98
        )

        //Test result after scheduling
        result_3: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
          emailChannelData = EmailChannelData(
            emailSettingId = emailSetting.id.get.emailSettingId
          ),
          teamId = teamId
        )

        scheduledDataCountInScheduler_3: List[Int] <- Future {
          SchedulerTestDAO.getCountOfScheduledProspectForACampaign(
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            step_id = StepId(addStep_2.step_id),
          ).get
        }

        scheduledCampaignProspectCount_3: Int <- Future {
          CampaignProspectTestDAO.getCountOfCampaignProspectSchedulerStatusDue(
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            current_step_id = StepId(addStep_2.step_id),
          ).get
        }

      } yield {
        (SchedulerIntegrationTestResult(
          scheduleTasksData = result_2,
          emailScheduledCountForCampaign = scheduledDataCountInScheduler_2.length,
          scheduledCampaignProspectCount = scheduledCampaignProspectCount_2
        ),
          SchedulerIntegrationTestResult(
            scheduleTasksData = result_3,
            emailScheduledCountForCampaign = scheduledDataCountInScheduler_3.length,
            scheduledCampaignProspectCount = scheduledCampaignProspectCount_3
          )
        )
      }
      scheduleTaskData.map(p => {
        assert(p._1.scheduleTasksData.saved_tasks_count == 0
          && p._1.emailScheduledCountForCampaign == 0
          && p._2.scheduleTasksData.saved_tasks_count == 10
          && p._2.emailScheduledCountForCampaign == 10
        )
      }).recover({ case e =>
        println(e.getMessage)
        assert(false)
      })

    }

    it("Scheduler test for not scheduling email which are not validated") {
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
      val account: Account = initialData.account
      val emailSetting: EmailSetting = initialData.emailSetting.get
      val teamId: TeamId = TeamId(account.teams.head.team_id)

      val scheduleTaskData: Future[SchedulerIntegrationTestResult] = for {

        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 1,
          enable_email_validation = true
        )

        //Test result after scheduling
        result: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
          emailChannelData = EmailChannelData(
            emailSettingId = emailSetting.id.get.emailSettingId
          ),
          teamId = teamId
        )

        scheduledDataCountInScheduler: List[Int] <- Future {
          SchedulerTestDAO.getCountOfScheduledProspectForACampaign(
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            step_id = StepId(createAndStartCampaignData.addStep.step_id),
          ).get
        }

        scheduledCampaignProspectCount: Int <- Future {
          CampaignProspectTestDAO.getCountOfCampaignProspectSchedulerStatusDue(
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            current_step_id = StepId(createAndStartCampaignData.addStep.step_id),
          ).get
        }

      } yield {
        SchedulerIntegrationTestResult(
          scheduleTasksData = result,
          emailScheduledCountForCampaign = scheduledDataCountInScheduler.length,
          scheduledCampaignProspectCount = scheduledCampaignProspectCount
        )
      }
      scheduleTaskData.map(p => {
        assert(p.emailScheduledCountForCampaign == 0
          && p.scheduledCampaignProspectCount == 0)
      }).recover({ case e =>
        assert(false)
      })
    }

    it("Scheduler test for not scheduling email having merge tag error") {
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData(
        max_emails_per_prospect_per_day = 100,
        max_emails_per_prospect_per_week = 500,
        max_emails_per_prospect_account_per_day = Option(500),
        max_emails_per_prospect_account_per_week = Option(500),
      ).get

      val account: Account = initialData.account
      val emailSetting: EmailSetting = initialData.emailSetting.get
      val teamId: TeamId = TeamId(account.teams.head.team_id)
      val accountId: AccountId = AccountId(account.internal_id)

      val scheduleTaskData: Future[(SchedulerIntegrationTestResult, List[String])] = for {

        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          email_body = "{{first_name}}"
        )

        addProspect_1: Seq[ProspectObject] <- (ProspectFixtureForIntegrationTest.createUpdateOrAssignProspectFuture(
          account = account,
          accountId = accountId,
          teamId = teamId,
          campaignId = Some(CampaignId(createAndStartCampaignData.createCampaign.id)),
          givenProspect = Some(Seq(ProspectCreateFormData(
            email = Some("<EMAIL>"),
            first_name = Option("praveen"),
            last_name = None,
            custom_fields = Json.obj(),

            list = None,
            company = None,
            city = None,
            country = None,
            timezone = Some("Asia/Kolkata"),

            state = None,
            job_title = None,
            phone_2 = None,
            phone_3 = None,
            phone = None
          )))
        ))

        updatedCreateAndStartCampaignData: CreateAndStartCampaignData <- Future {
          createAndStartCampaignData.copy(
            addProspect = createAndStartCampaignData.addProspect.:+(addProspect_1.head)
          )
        }

        //Test result after scheduling
        result: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
          emailChannelData = EmailChannelData(
            emailSettingId = emailSetting.id.get.emailSettingId
          ),
          teamId = teamId
        )

        scheduledDataCountInScheduler: List[Int] <- Future {
          SchedulerTestDAO.getCountOfScheduledProspectForACampaign(
            campaign_id = CampaignId(updatedCreateAndStartCampaignData.createCampaign.id),
            teamId = teamId,
            prospect_ids = updatedCreateAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            step_id = StepId(updatedCreateAndStartCampaignData.addStep.step_id),
          ).get
        }

        scheduledCampaignProspectCount: Int <- Future {
          CampaignProspectTestDAO.getCountOfCampaignProspectSchedulerStatusDue(
            campaign_id = CampaignId(updatedCreateAndStartCampaignData.createCampaign.id),
            teamId = teamId,
            prospect_ids = updatedCreateAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            current_step_id = StepId(updatedCreateAndStartCampaignData.addStep.step_id),
          ).get
        }

        mergeTagFailedArray: List[String] <- Future {
          CampaignProspectTestDAO.getMergeTagFields(
            teamId = teamId,
            campaign_id = CampaignId(updatedCreateAndStartCampaignData.campaign.id),
            prospectId = updatedCreateAndStartCampaignData.addProspect.map(p => ProspectId(p.id))
          ).get
        }

      } yield {
        (
          SchedulerIntegrationTestResult(
            scheduleTasksData = result,
            emailScheduledCountForCampaign = scheduledDataCountInScheduler.length,
            scheduledCampaignProspectCount = scheduledCampaignProspectCount
          ),
          mergeTagFailedArray
        )
      }
      scheduleTaskData.map(p => {
        val error = p._2
        val expected = List("{first_name}", "{first_name}", "{first_name}", "{first_name}", "{first_name}")
        assert(p._1.emailScheduledCountForCampaign == 1
          && p._1.scheduledCampaignProspectCount == 1
          && error == expected
        )
      }).recover({ case e =>
        assert(false)
      })
    }

    it("Scheduler test for max_emails_to_prospect_per_week") {
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData(
        max_emails_per_prospect_per_day = 100,
        max_emails_per_prospect_per_week = 2,
        max_emails_per_prospect_account_per_day = Option(100),
        max_emails_per_prospect_account_per_week = Option(500),
      ).get
      val account: Account = initialData.account
      val orgId: OrgId = OrgId(account.org.id)
      val taId: Long = account.teams.head.access_members.head.ta_id
      val emailSetting: EmailSetting = initialData.emailSetting.get
      val teamId: TeamId = TeamId(account.teams.head.team_id)
      val accountId: AccountId = AccountId(account.internal_id)

      val scheduleTaskData: Future[(SchedulerIntegrationTestResult, SchedulerIntegrationTestResult, SchedulerIntegrationTestResult)] = for {

        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 1,
        )

        //Test result after scheduling
        result_1: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
          emailChannelData = EmailChannelData(
            emailSettingId = emailSetting.id.get.emailSettingId
          ),
          teamId = teamId
        )

        //add auto email step to campaign
        addStep_2: CampaignStepVariant <- {
          CreateStepForCampaignFixture.createAutoEmailStepForCampaign(
            orgId = orgId,
            teamId = teamId,
            accountId = accountId,
            taId = taId,
            campaignId = CampaignId(createAndStartCampaignData.createCampaign.id),
            parentId = createAndStartCampaignData.addStep.step_id
          )
        }

        scheduledDataCountInScheduler_1: List[Int] <- Future {
          SchedulerTestDAO.getCountOfScheduledProspectForACampaign(
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            step_id = StepId(createAndStartCampaignData.addStep.step_id),
          ).get
        }

        scheduledCampaignProspectCount_1: Int <- Future {
          CampaignProspectTestDAO.getCountOfCampaignProspectSchedulerStatusDue(
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            current_step_id = StepId(createAndStartCampaignData.addStep.step_id),
          ).get
        }

        reInitializeForSecondScheduling_1: Int <- ReSchedulingFixture.initializeDataForReScheduling(
          orgId = orgId,
          accountId = accountId,
          teamId = teamId,
          emailSetting = emailSetting,
          campaign = createAndStartCampaignData.campaign,
          prospectList = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
        )

        //Test result after scheduling
        result_2: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
          emailChannelData = EmailChannelData(
            emailSettingId = emailSetting.id.get.emailSettingId
          ),
          teamId = teamId
        )

        scheduledDataCountInScheduler_2: List[Int] <- Future {
          SchedulerTestDAO.getCountOfScheduledProspectForACampaign(
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            step_id = StepId(createAndStartCampaignData.addStep.step_id),
          ).get
        }

        scheduledCampaignProspectCount_2: Int <- Future {
          CampaignProspectTestDAO.getCountOfCampaignProspectSchedulerStatusDue(
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            current_step_id = StepId(createAndStartCampaignData.addStep.step_id),
          ).get
        }

        addStep_2: CampaignStepVariant <- {
          CreateStepForCampaignFixture.createAutoEmailStepForCampaign(
            orgId = orgId,
            teamId = teamId,
            accountId = accountId,
            taId = taId,
            campaignId = CampaignId(createAndStartCampaignData.createCampaign.id),
            parentId = createAndStartCampaignData.addStep.step_id
          )
        }

        reInitializeForSecondScheduling_2: Int <- ReSchedulingFixture.initializeDataForReScheduling(
          orgId = orgId,
          accountId = accountId,
          teamId = teamId,
          emailSetting = emailSetting,
          campaign = createAndStartCampaignData.campaign,
          prospectList = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
        )

        //Test result after scheduling
        result_3: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
          emailChannelData = EmailChannelData(
            emailSettingId = emailSetting.id.get.emailSettingId
          ),
          teamId = teamId
        )

        scheduledDataCountInScheduler_3: List[Int] <- Future {
          SchedulerTestDAO.getCountOfScheduledProspectForACampaign(
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            step_id = StepId(createAndStartCampaignData.addStep.step_id),
          ).get
        }

        scheduledCampaignProspectCount_3: Int <- Future {
          CampaignProspectTestDAO.getCountOfCampaignProspectSchedulerStatusDue(
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            current_step_id = StepId(createAndStartCampaignData.addStep.step_id),
          ).get
        }

      } yield {
        (
          SchedulerIntegrationTestResult(
            scheduleTasksData = result_1,
            emailScheduledCountForCampaign = scheduledDataCountInScheduler_1.length,
            scheduledCampaignProspectCount = scheduledCampaignProspectCount_1,
          ),
          SchedulerIntegrationTestResult(
            scheduleTasksData = result_2,
            emailScheduledCountForCampaign = scheduledDataCountInScheduler_2.length,
            scheduledCampaignProspectCount = scheduledCampaignProspectCount_2,
          ),
          SchedulerIntegrationTestResult(
            scheduleTasksData = result_3,
            emailScheduledCountForCampaign = scheduledDataCountInScheduler_3.length,
            scheduledCampaignProspectCount = scheduledCampaignProspectCount_3,
          )
        )
      }
      scheduleTaskData.map(p => {
        assert(p._1.scheduleTasksData.saved_tasks_count == 1
          && p._2.scheduleTasksData.saved_tasks_count == 1
          && p._3.scheduleTasksData.saved_tasks_count == 0
        )
      }).recover({ case e =>
        assert(false)
      })
    }

    it("Scheduler test for scheduling email base on max_emails_per_prospect_account_per_day check") {
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData(
        max_emails_per_prospect_per_day = 100,
        max_emails_per_prospect_per_week = 500,
        max_emails_per_prospect_account_per_day = Option(5),
        max_emails_per_prospect_account_per_week = Option(500),
      ).get
      val account: Account = initialData.account
      val emailSetting: EmailSetting = initialData.emailSetting.get
      val teamId: TeamId = TeamId(account.teams.head.team_id)

      val scheduleTaskData: Future[SchedulerIntegrationTestResult] = for {

        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 10,
        )

        //Test result after scheduling
        result: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
          emailChannelData = EmailChannelData(
            emailSettingId = emailSetting.id.get.emailSettingId
          ),
          teamId = teamId
        )

        scheduledDataCountInScheduler: List[Int] <- Future {
          SchedulerTestDAO.getCountOfScheduledProspectForACampaign(
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            step_id = StepId(createAndStartCampaignData.addStep.step_id),
          ).get
        }

        scheduledCampaignProspectCount: Int <- Future {
          CampaignProspectTestDAO.getCountOfCampaignProspectSchedulerStatusDue(
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            current_step_id = StepId(createAndStartCampaignData.addStep.step_id),
          ).get
        }

      } yield {
        SchedulerIntegrationTestResult(
          scheduleTasksData = result,
          emailScheduledCountForCampaign = scheduledDataCountInScheduler.length,
          scheduledCampaignProspectCount = scheduledCampaignProspectCount
        )
      }
      scheduleTaskData.map(p => {
        assert(p.emailScheduledCountForCampaign == 5
          && p.scheduledCampaignProspectCount == 5
        )
      }).recover({ case e =>
        assert(false)
      })
    }

    it("Scheduler test for scheduling email base on max_emails_per_prospect_account_per_week check") {
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData(
        max_emails_per_prospect_per_day = 100,
        max_emails_per_prospect_per_week = 500,
        max_emails_per_prospect_account_per_day = Option(100),
        max_emails_per_prospect_account_per_week = Option(30),
      ).get
      val account: Account = initialData.account
      val emailSetting: EmailSetting = initialData.emailSetting.get
      val teamId: TeamId = TeamId(account.teams.head.team_id)

      val scheduleTaskData: Future[SchedulerIntegrationTestResult] = for {

        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 50,
        )

        //Test result after scheduling
        result: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
          emailChannelData = EmailChannelData(
            emailSettingId = emailSetting.id.get.emailSettingId
          ),
          teamId = teamId
        )

        scheduledDataCountInScheduler: List[Int] <- Future {
          SchedulerTestDAO.getCountOfScheduledProspectForACampaign(
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            step_id = StepId(createAndStartCampaignData.addStep.step_id),
          ).get
        }

        scheduledCampaignProspectCount: Int <- Future {
          CampaignProspectTestDAO.getCountOfCampaignProspectSchedulerStatusDue(
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            current_step_id = StepId(createAndStartCampaignData.addStep.step_id),
          ).get
        }

      } yield {
        SchedulerIntegrationTestResult(
          scheduleTasksData = result,
          emailScheduledCountForCampaign = scheduledDataCountInScheduler.length,
          scheduledCampaignProspectCount = scheduledCampaignProspectCount
        )
      }
      scheduleTaskData.map(p => {
        assert(p.emailScheduledCountForCampaign == 30
          && p.scheduledCampaignProspectCount == 30
        )
      }).recover({ case e =>
        assert(false)
      })
    }

    it("Scheduler test for max_emails_to_prospect_per_day") {
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData(
        max_emails_per_prospect_per_day = 2,
        max_emails_per_prospect_per_week = 500,
        max_emails_per_prospect_account_per_day = Option(100),
        max_emails_per_prospect_account_per_week = Option(500),
      ).get
      val account: Account = initialData.account
      val orgId: OrgId = OrgId(account.org.id)
      val taId: Long = account.teams.head.access_members.head.ta_id
      val emailSetting: EmailSetting = initialData.emailSetting.get
      val teamId: TeamId = TeamId(account.teams.head.team_id)
      val accountId: AccountId = AccountId(account.internal_id)


      val scheduleTaskData: Future[(SchedulerIntegrationTestResult, SchedulerIntegrationTestResult, SchedulerIntegrationTestResult)] = for {

        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 1,
        )

        //Test result after scheduling
        result_1: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
          emailChannelData = EmailChannelData(
            emailSettingId = emailSetting.id.get.emailSettingId
          ),
          teamId = teamId
        )

        addStep_1: CampaignStepVariant <- {
          CreateStepForCampaignFixture.createAutoEmailStepForCampaign(
            orgId = orgId,
            teamId = teamId,
            accountId = accountId,
            taId = taId,
            campaignId = CampaignId(createAndStartCampaignData.createCampaign.id),
            parentId = createAndStartCampaignData.addStep.step_id
          )
        }

        scheduledDataCountInScheduler_1: List[Int] <- Future {
          SchedulerTestDAO.getCountOfScheduledProspectForACampaign(
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            step_id = StepId(createAndStartCampaignData.addStep.step_id),
          ).get
        }

        scheduledCampaignProspectCount_1: Int <- Future {
          CampaignProspectTestDAO.getCountOfCampaignProspectSchedulerStatusDue(
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            current_step_id = StepId(createAndStartCampaignData.addStep.step_id),
          ).get
        }

        reInitializeForSecondScheduling_1: Int <- ReSchedulingFixture.initializeDataForReScheduling(
          orgId = orgId,
          accountId = accountId,
          teamId = teamId,
          emailSetting = emailSetting,
          campaign = createAndStartCampaignData.campaign,
          prospectList = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
        )

        //Test result after scheduling
        result_2: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
          emailChannelData = EmailChannelData(
            emailSettingId = emailSetting.id.get.emailSettingId
          ),
          teamId = teamId
        )

        scheduledDataCountInScheduler_2: List[Int] <- Future {
          SchedulerTestDAO.getCountOfScheduledProspectForACampaign(
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            step_id = StepId(createAndStartCampaignData.addStep.step_id),
          ).get
        }

        scheduledCampaignProspectCount_2: Int <- Future {
          CampaignProspectTestDAO.getCountOfCampaignProspectSchedulerStatusDue(
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            current_step_id = StepId(createAndStartCampaignData.addStep.step_id),
          ).get
        }

        addStep_2: CampaignStepVariant <- {
          CreateStepForCampaignFixture.createAutoEmailStepForCampaign(
            orgId = orgId,
            teamId = teamId,
            accountId = accountId,
            taId = taId,
            campaignId = CampaignId(createAndStartCampaignData.createCampaign.id),
            parentId = createAndStartCampaignData.addStep.step_id
          )
        }

        reInitializeForSecondScheduling_2: Int <- ReSchedulingFixture.initializeDataForReScheduling(
          orgId = orgId,
          accountId = accountId,
          teamId = teamId,
          emailSetting = emailSetting,
          campaign = createAndStartCampaignData.campaign,
          prospectList = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
        )

        //Test result after scheduling
        result_3: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
          emailChannelData = EmailChannelData(
            emailSettingId = emailSetting.id.get.emailSettingId
          ),
          teamId = teamId
        )

        scheduledDataCountInScheduler_3: List[Int] <- Future {
          SchedulerTestDAO.getCountOfScheduledProspectForACampaign(
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            step_id = StepId(createAndStartCampaignData.addStep.step_id),
          ).get
        }

        scheduledCampaignProspectCount_3: Int <- Future {
          CampaignProspectTestDAO.getCountOfCampaignProspectSchedulerStatusDue(
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            current_step_id = StepId(createAndStartCampaignData.addStep.step_id),
          ).get
        }

      } yield {
        (
          SchedulerIntegrationTestResult(
            scheduleTasksData = result_1,
            emailScheduledCountForCampaign = scheduledDataCountInScheduler_1.length,
            scheduledCampaignProspectCount = scheduledCampaignProspectCount_1,
          ),
          SchedulerIntegrationTestResult(
            scheduleTasksData = result_2,
            emailScheduledCountForCampaign = scheduledDataCountInScheduler_2.length,
            scheduledCampaignProspectCount = scheduledCampaignProspectCount_2,
          ),
          SchedulerIntegrationTestResult(
            scheduleTasksData = result_3,
            emailScheduledCountForCampaign = scheduledDataCountInScheduler_3.length,
            scheduledCampaignProspectCount = scheduledCampaignProspectCount_3,
          )
        )
      }
      scheduleTaskData.map(p => {
        assert(p._1.scheduleTasksData.saved_tasks_count == 1
          && p._2.scheduleTasksData.saved_tasks_count == 1
          && p._3.scheduleTasksData.saved_tasks_count == 0
        )
      }).recover({ case e =>
        assert(false)
      })
    }
  }
  describe("Whatsapp Only Schduler IntegrationTest") {

    it("Scheduler test for adding campaign with three prospects and making that scheduled") {
      val initialData: InitialData = NewAccountAndWhatsappSettingData.createNewAccountAndWhatsappSettingData(emailNotCompulsoryOrgFlag = Some(true)).get
      val account: Account = initialData.account
      val whatsappSettingData = initialData.whatsappAccount.get
      val teamId: TeamId = TeamId(account.teams.head.team_id)


      val scheduleTaskData: Future[SchedulerIntegrationTestResult] = for {

        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartWhatsappCampaign(
          initialData = initialData,
          whatsAppSetting = whatsappSettingData.settings
        )

        //Test result after scheduling
        result: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForWhatsappChannel(
          whatsAppChannelData = WhatsAppChannelData(
            whatsAppSettingUuid = whatsappSettingData.id.uuid
          ),
          teamId = teamId
        )

        //Verification
        scheduledTaskCountInScheduler: List[Int] <- Future {
          SchedulerTestDAO.getCountOfScheduledTasksForACampaign(
            campaign_id = CampaignId(createAndStartCampaignData.campaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            step_id = StepId(createAndStartCampaignData.campaign.head_step_id.get),
          ).get
        }

        //Verification
        scheduledCampaignProspectCount: Int <- Future {
          CampaignProspectTestDAO.getCountOfCampaignProspectSchedulerStatusDue(
            campaign_id = CampaignId(createAndStartCampaignData.campaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            current_step_id = StepId(createAndStartCampaignData.campaign.head_step_id.get),
          ).get
        }
      } yield {
        SchedulerIntegrationTestResult(
          scheduleTasksData = result,
          emailScheduledCountForCampaign = scheduledTaskCountInScheduler.length,
          scheduledCampaignProspectCount = scheduledCampaignProspectCount
        )
      }
      scheduleTaskData.map(p => {
        assert(p.emailScheduledCountForCampaign == 3
          && p.scheduledCampaignProspectCount == 3)
      }).recover({ case e =>
        assert(false)
      })
    }

    it("Scheduler test for email not compulsory whatsApp campaign with some duplicate prospects") {

      val initialData: InitialData = NewAccountAndWhatsappSettingData.createNewAccountAndWhatsappSettingData(emailNotCompulsoryOrgFlag = Some(true)).get
      val account: Account = initialData.account
      val whatsappSettingData = initialData.whatsappAccount.get
      val teamId: TeamId = TeamId(account.teams.head.team_id)


      val scheduleTaskData: Future[SchedulerIntegrationTestResult] = for {

        prospects: Seq[ProspectObject] <- Future {
          initialData.prospectsResult
        }

        savedDuplicates: List[Long] <- Future {
          val (prospect1: ProspectObject, prospect2: ProspectObject) = (prospects.head, prospects.last)
          potentialDuplicateProspectService.savePotentialDuplicateProspectsLogs(
            duplicates = List(ProspectDeduplicationDataWithId(
              prospectId = ProspectId(prospect1.id),
              prospectDeduplicationColumnsData = ProspectDeduplicationColumnsData(
                email = prospect1.email,
                phone = prospect1.phone,
                linkedin_url = prospect1.linkedin_url,
                company = prospect1.company,
                first_name = None,
                last_name = None
              )
            ), ProspectDeduplicationDataWithId(
              prospectId = ProspectId(prospect2.id),
              prospectDeduplicationColumnsData = ProspectDeduplicationColumnsData(
                email = prospect2.email,
                phone = prospect2.phone,
                linkedin_url = prospect2.linkedin_url,
                company = prospect2.company,
                first_name = None,
                last_name = None
              )
            )),
            found_req_trace_id = "found_req_trace_id",
            team_id = teamId
          ).get
        }

        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartWhatsappCampaign(
          initialData = initialData,
          whatsAppSetting = whatsappSettingData.settings
        )

        //Test result after scheduling
        result: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForWhatsappChannel(
          whatsAppChannelData = WhatsAppChannelData(
            whatsAppSettingUuid = whatsappSettingData.id.uuid
          ),
          teamId = teamId
        )

        //Verification
        scheduledTaskCountInScheduler: List[Int] <- Future {
          SchedulerTestDAO.getCountOfScheduledTasksForACampaign(
            campaign_id = CampaignId(createAndStartCampaignData.campaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            step_id = StepId(createAndStartCampaignData.campaign.head_step_id.get),
          ).get
        }

        //Verification
        scheduledCampaignProspectCount: Int <- Future {
          CampaignProspectTestDAO.getCountOfCampaignProspectSchedulerStatusDue(
            campaign_id = CampaignId(createAndStartCampaignData.campaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            current_step_id = StepId(createAndStartCampaignData.campaign.head_step_id.get),
          ).get
        }
      } yield {
        SchedulerIntegrationTestResult(
          scheduleTasksData = result,
          emailScheduledCountForCampaign = scheduledTaskCountInScheduler.length,
          scheduledCampaignProspectCount = scheduledCampaignProspectCount
        )
      }
      scheduleTaskData.map(p => {
        assert(p.emailScheduledCountForCampaign == 1
          && p.scheduledCampaignProspectCount == 1
          && initialData.prospectsResult.length == 3)
      }).recover({ case e =>
        println(s"Error! ${LogHelpers.getStackTraceAsString(e)}")
        assert(false)
      })
    }

  }

  describe("Scheduler Integration tests for checking emails limit per prospect/prospect_account") {

    it("Emails should not get scheduled if the prospect has pending manual_email tasks equal to 'max_emails_per_prospect_per_day' limit ") {

      val max_emails_per_prospect_per_day = 5
      val max_emails_per_prospect_per_week = 1000
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData(
        max_emails_per_prospect_per_day = max_emails_per_prospect_per_day,
        max_emails_per_prospect_per_week = max_emails_per_prospect_per_week
      ).get
      val account: Account = initialData.account
      val emailSetting: EmailSetting = initialData.emailSetting.get
      val teamId: TeamId = TeamId(account.teams.head.team_id)

      val createProspectData1 = ProspectCreateFormData(
        email = Some("<EMAIL>"),
        first_name = Some("Parth"),
        last_name = Some("Gupta"),
        custom_fields = Json.obj(),

        list = None,
        company = None,
        city = None,
        country = None,
        timezone = None,

        state = None,
        job_title = None,
        phone = Some("+************"),
        phone_2 = None,
        phone_3 = None,
        linkedin_url = Some("https://linkedin.com/in/parth7729")
      )

      for {

        // creating a prospect which will be used in this test
        prospect <- Future(prospectService.createOrUpdateProspects(
          ownerAccountId = account.internal_id,
          teamId = teamId.id,
          listName = None,
          prospects = Seq(createProspectData1),
          updateProspectType = UpdateProspectType.ForceUpdate,
          ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads = true,

          doerAccount = account,
          prospectSource = None,
          prospectAccountId = None,

          campaign_id = None,
          prospect_tags = None,
          ignoreProspectInOtherCampaign = IgnoreProspectsInOtherCampaigns.DoNotIgnore,
          deduplicationColumns = None,
          auditRequestLogId = None,

          SRLogger = Logger
        ).get)

        // creating first campaign and assigning the prospect to this campaign
        oldCampaign: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          prospects = Some(Seq(createProspectData1))
        )

        // creating manual emails_scheduled of 'max_emails_per_prospect_per_day' number of entries
        // as it is a manual_email, sent will be false unless the user sends it manually.
        _ <- Future {
          1 to max_emails_per_prospect_per_day foreach {
            _ =>
              emailScheduledDAOService.saveEmailsToBeScheduledAndUpdateCampaignDataV2(
                emailsToBeScheduled = Vector(EmailScheduledNewFixture.generateEmailScheduledNew3.copy(
                  campaign_id = Some(oldCampaign.campaign.id),
                  scheduled_from_campaign = true,
                  team_id = oldCampaign.startCampaign.team_id,
                  prospect_id = Some(prospect.created_ids.head),
                  scheduled_at = DateTime.now().minusDays(5), // scheduled 5 days back
                  step_type = CampaignStepType.ManualEmailStep, // is_manual_task = true

                  sender_email_settings_id = oldCampaign.startCampaign.settings.campaign_email_settings.head.sender_email_setting_id.emailSettingId,
                  receiver_email_settings_id = oldCampaign.startCampaign.settings.campaign_email_settings.head.receiver_email_setting_id.emailSettingId
                )),
                campaign_email_setting_id = oldCampaign.startCampaign.settings.campaign_email_settings.head.id,
                emailSendingFlow = None,
                Logger = Logger
              )

          }
        }

        // creating a new auto-email campaign and assigning the same prospect
        newCampaign: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          prospects = Some(Seq(createProspectData1))
        )

        _ = println(s"debugging createAndStartCampaignData: ${newCampaign.addProspect}")
        _ = println(s"debugging second campaign id: ${newCampaign.campaign.id}")

        // test result after scheduling
        result: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
          emailChannelData = EmailChannelData(
            emailSettingId = emailSetting.id.get.emailSettingId
          ),
          teamId = teamId
        )

        //Verification
        scheduledDataCountInScheduler: List[Int] <- Future {
          SchedulerTestDAO.getCountOfScheduledProspectForACampaign(
            campaign_id = CampaignId(newCampaign.campaign.id),
            teamId = teamId,
            prospect_ids = newCampaign.addProspect.map(p => ProspectId(p.id)),
            step_id = StepId(newCampaign.campaign.head_step_id.get),
          ).get
        }


        _ = println(s"debugging scheduledDataCountInScheduler: $scheduledDataCountInScheduler")

        //Verification
        scheduledCampaignProspectCount: Int <- Future {
          CampaignProspectTestDAO.getCountOfCampaignProspectSchedulerStatusDue(
            campaign_id = CampaignId(newCampaign.campaign.id),
            teamId = teamId,
            prospect_ids = newCampaign.addProspect.map(p => ProspectId(p.id)),
            current_step_id = StepId(newCampaign.campaign.head_step_id.get),
          ).get
        }

        // number of emails sent for the day (should be zero)
        count <- Future(SchedulerTestDAO.getCountOfEmailsSentToProspectInTimeframe(
          prospectIds = prospect.created_ids.map(pid => ProspectId(pid)),
          teamId = teamId,
          fromTime = DateTime.now().withTimeAtStartOfDay,
          tillTime = DateTime.now()
        ).get)

        _ = println(s"debugging count: $count")

        _ = println(s"debugging scheduledCampaignProspectCount: $scheduledCampaignProspectCount")

      } yield {
        assert(count == 0)

        assert(scheduledDataCountInScheduler.isEmpty)
        assert(scheduledCampaignProspectCount == 0)

      }

    }

    it("Email should get scheduled according to 'max_emails_per_prospect_account_per_day' limit") {

      val max_emails_per_prospect_per_day = 1
      val max_emails_per_prospect_per_week = 1000
      val max_emails_per_prospect_account_per_day = Some(1)
      val max_emails_per_prospect_account_per_week = Some(1000)
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData(
        max_emails_per_prospect_per_day = max_emails_per_prospect_per_day,
        max_emails_per_prospect_per_week = max_emails_per_prospect_per_week,
        max_emails_per_prospect_account_per_day = max_emails_per_prospect_account_per_day,
        max_emails_per_prospect_account_per_week = max_emails_per_prospect_account_per_week
      ).get
      val account: Account = initialData.account
      val emailSetting: EmailSetting = initialData.emailSetting.get
      val teamId: TeamId = TeamId(account.teams.head.team_id)

      val createProspectData1 = ProspectCreateFormData(
        email = Some("<EMAIL>"),
        first_name = Some("Parth"),
        last_name = Some("Gupta"),
        custom_fields = Json.obj(),

        list = None,
        company = None,
        city = None,
        country = None,
        timezone = None,

        state = None,
        job_title = None,
        phone = Some("+************"),
        phone_2 = None,
        phone_3 = None,
        linkedin_url = Some("https://linkedin.com/in/parth7729")
      )

      val prospectAccountsCount = 10
      val prospectsPerAccountCount = 10

      // creating prospect accounts
      val allProspectAccounts = 1 to prospectAccountsCount map (_ => SrRandomTestUtils.getRandomStringOfLengthN(5) + ".com")

      // adding prospects to account
      val prospects: Seq[ProspectCreateFormData] = allProspectAccounts.flatMap(prospectAccount => {
        DefaultProspectParameterFixture.generateNProspectEmails(numberOfProspect = prospectsPerAccountCount, domainName = prospectAccount).map(prospectEmail => createProspectData1.copy(
          email = Some(prospectEmail)
        ))
      })

      for {

        // creating campaign and assigning the prospects to this campaign
        campaign: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          prospects = Some(prospects)
        )

        // test result after scheduling
        result: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
          emailChannelData = EmailChannelData(
            emailSettingId = emailSetting.id.get.emailSettingId
          ),
          teamId = teamId
        )

        //Verification
        scheduledDataCountInScheduler: List[Int] <- Future {
          SchedulerTestDAO.getCountOfScheduledProspectForACampaign(
            campaign_id = CampaignId(campaign.campaign.id),
            teamId = teamId,
            prospect_ids = campaign.addProspect.map(p => ProspectId(p.id)),
            step_id = StepId(campaign.campaign.head_step_id.get),
          ).get
        }

        _ = println(s"debugging scheduledDataCountInScheduler: $scheduledDataCountInScheduler")

        //Verification
        scheduledCampaignProspectCount: Int <- Future {
          CampaignProspectTestDAO.getCountOfCampaignProspectSchedulerStatusDue(
            campaign_id = CampaignId(campaign.campaign.id),
            teamId = teamId,
            prospect_ids = campaign.addProspect.map(p => ProspectId(p.id)),
            current_step_id = StepId(campaign.campaign.head_step_id.get),
          ).get
        }

        _ = println(s"debugging scheduledCampaignProspectCount: $scheduledCampaignProspectCount")

      } yield {

        // checking total prospects in campaign
        assert(campaign.addProspect.length == 100)

        // checking number of prospects scheduled
        val allScheduledProspects = campaign.addProspect.filter(p => scheduledDataCountInScheduler.contains(p.id))
        assert(allScheduledProspects.length == 10)

        // checking number of prospects scheduled per account
        val prospectsGroupedByProspectAccount = allScheduledProspects.groupBy(p => p.internal.prospect_account_id)
        prospectsGroupedByProspectAccount.foreach(grp => assert(grp._2.length == 1))

        assert(scheduledCampaignProspectCount == 10)

      }

    }

    it("Email should get scheduled according to 'max_emails_per_prospect_account_per_day' limit with max scheduler limit in one cycle - 100 prospects in one scheduler cycle") {

      val max_emails_per_prospect_per_day = 1
      val max_emails_per_prospect_per_week = 1000
      val max_emails_per_prospect_account_per_day = Some(1)
      val max_emails_per_prospect_account_per_week = Some(1000)
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData(
        max_emails_per_prospect_per_day = max_emails_per_prospect_per_day,
        max_emails_per_prospect_per_week = max_emails_per_prospect_per_week,
        max_emails_per_prospect_account_per_day = max_emails_per_prospect_account_per_day,
        max_emails_per_prospect_account_per_week = max_emails_per_prospect_account_per_week
      ).get
      val account: Account = initialData.account
      val emailSetting: EmailSetting = initialData.emailSetting.get
      val teamId: TeamId = TeamId(account.teams.head.team_id)

      val createProspectData1 = ProspectCreateFormData(
        email = Some("<EMAIL>"),
        first_name = Some("Parth"),
        last_name = Some("Gupta"),
        custom_fields = Json.obj(),

        list = None,
        company = None,
        city = None,
        country = None,
        timezone = None,

        state = None,
        job_title = None,
        phone = Some("+************"),
        phone_2 = None,
        phone_3 = None,
        linkedin_url = Some("https://linkedin.com/in/parth7729")
      )

      val prospectAccountsCount = 120
      val prospectsPerAccountCount = 10

      // creating prospect accounts
      val allProspectAccounts = 1 to prospectAccountsCount map (_ => SrRandomTestUtils.getRandomStringOfLengthN(5) + ".com")

      // adding prospects to account
      val prospects: Seq[ProspectCreateFormData] = allProspectAccounts.flatMap(prospectAccount => {
        DefaultProspectParameterFixture.generateNProspectEmails(numberOfProspect = prospectsPerAccountCount, domainName = prospectAccount).map(prospectEmail => createProspectData1.copy(
          email = Some(prospectEmail)
        ))
      })

      for {

        // creating campaign and assigning the prospects to this campaign
        campaign: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          prospects = Some(prospects)
        )

        // test result after scheduling
        result: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
          emailChannelData = EmailChannelData(
            emailSettingId = emailSetting.id.get.emailSettingId
          ),
          teamId = teamId
        )

        //Verification
        scheduledDataCountInScheduler: List[Int] <- Future {
          SchedulerTestDAO.getCountOfScheduledProspectForACampaign(
            campaign_id = CampaignId(campaign.campaign.id),
            teamId = teamId,
            prospect_ids = campaign.addProspect.map(p => ProspectId(p.id)),
            step_id = StepId(campaign.campaign.head_step_id.get),
          ).get
        }

        _ = println(s"debugging scheduledDataCountInScheduler: $scheduledDataCountInScheduler")

        //Verification
        scheduledCampaignProspectCount: Int <- Future {
          CampaignProspectTestDAO.getCountOfCampaignProspectSchedulerStatusDue(
            campaign_id = CampaignId(campaign.campaign.id),
            teamId = teamId,
            prospect_ids = campaign.addProspect.map(p => ProspectId(p.id)),
            current_step_id = StepId(campaign.campaign.head_step_id.get),
          ).get
        }

        _ = println(s"debugging scheduledCampaignProspectCount: $scheduledCampaignProspectCount")

      } yield {

        // checking total prospects in campaign
        //        assert(campaign.addProspect.length == 100)

        println(s"debugging campaign.addProspect.length: ${campaign.addProspect.length}")

        // checking number of prospects scheduled
        val allScheduledProspects = campaign.addProspect.filter(p => scheduledDataCountInScheduler.contains(p.id))
        assert(allScheduledProspects.length == 100)

        // checking number of prospects scheduled per account according to 'max_emails_per_prospect_account_per_day' limit
        val prospectsGroupedByProspectAccount = allScheduledProspects.groupBy(p => p.internal.prospect_account_id)
        prospectsGroupedByProspectAccount.foreach(grp => {

          println(s"debugging grp._2.length > 1: ${grp._1}, ${grp._2.map(_.id)}")
          assert(grp._2.length == max_emails_per_prospect_account_per_day.get)
        })

        assert(scheduledCampaignProspectCount == 100)

      }

    }

    ignore("Email should get scheduled according to 'max_emails_per_prospect_account_per_day' limit for more than 100 prospects - multiple scheduler cycles") {

      val max_emails_per_prospect_per_day = 1
      val max_emails_per_prospect_per_week = 1000
      val max_emails_per_prospect_account_per_day = Some(1)
      val max_emails_per_prospect_account_per_week = Some(1000)
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData(
        max_emails_per_prospect_per_day = max_emails_per_prospect_per_day,
        max_emails_per_prospect_per_week = max_emails_per_prospect_per_week,
        max_emails_per_prospect_account_per_day = max_emails_per_prospect_account_per_day,
        max_emails_per_prospect_account_per_week = max_emails_per_prospect_account_per_week,
        quota_per_day = 400
      ).get
      val account: Account = initialData.account
      val emailSetting: EmailSetting = initialData.emailSetting.get
      val teamId: TeamId = TeamId(account.teams.head.team_id)

      val createProspectData1 = ProspectCreateFormData(
        email = Some("<EMAIL>"),
        first_name = Some("Parth"),
        last_name = Some("Gupta"),
        custom_fields = Json.obj(),

        list = None,
        company = None,
        city = None,
        country = None,
        timezone = None,

        state = None,
        job_title = None,
        phone = Some("+************"),
        phone_2 = None,
        phone_3 = None,
        linkedin_url = Some("https://linkedin.com/in/parth7729")
      )

      val prospectAccountsCount = 200
      val prospectsPerAccountCount = 10

      // creating prospect accounts
      val allProspectAccounts = 1 to prospectAccountsCount map (_ => SrRandomTestUtils.getRandomStringOfLengthN(5) + ".com")

      // adding prospects to account
      val prospects: Seq[ProspectCreateFormData] = allProspectAccounts.flatMap(prospectAccount => {
        DefaultProspectParameterFixture.generateNProspectEmails(numberOfProspect = prospectsPerAccountCount, domainName = prospectAccount).map(prospectEmail => createProspectData1.copy(
          email = Some(prospectEmail)
        ))
      })

      for {

        // creating campaign and assigning the prospects to this campaign
        campaign: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          prospects = Some(prospects)
        )

        // test result after scheduling
        res1: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
          emailChannelData = EmailChannelData(
            emailSettingId = emailSetting.id.get.emailSettingId
          ),
          teamId = teamId
        )

        _ = println(s"debugging res1: $res1")

        campaignIdsWithNextToBeScheduledAt = CampaignDataToAddNextToBeScheduledAtForEmailChannel(
          campaignId = campaign.campaign.id,
          nextToBeScheduledAt = DateTime.now().minusMinutes(1),
          sender_email_setting_id = initialData.emailSetting.get.id.get.emailSettingId,
          channelType = ChannelType.EmailChannel
        )

        _updateNextToScheduledAtNew <- Future {
          campaignDAO._updateNextToScheduledAt(
            campaignIdsWithNextToBeScheduledAt = Seq(campaignIdsWithNextToBeScheduledAt),
            Logger = Logger,
            team_id = initialData.emailSetting.get.team_id,
            is_scheduler_flow = false
          ).get
        }

        res <- Future(SchedulerTestDAO.updateLatestEmailScheduledAtForEmailSetting(
          emailSettingId = emailSetting.id.get,
          teamId = teamId
        ).get)

        _ = println(s"debugging res: $res")

        _ = println(s"debugging _updateNextToScheduledAtNew: $_updateNextToScheduledAtNew")
        _ = println(s"debugging emailSettingId: ${emailSetting.id.get.emailSettingId}")

        res2: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
          emailChannelData = EmailChannelData(
            emailSettingId = emailSetting.id.get.emailSettingId
          ),
          teamId = teamId
        )

        _ = println(s"debugging res2: $res2")

        //Verification
        scheduledDataCountInScheduler: List[Int] <- Future {
          SchedulerTestDAO.getCountOfScheduledProspectForACampaign(
            campaign_id = CampaignId(campaign.campaign.id),
            teamId = teamId,
            prospect_ids = campaign.addProspect.map(p => ProspectId(p.id)),
            step_id = StepId(campaign.campaign.head_step_id.get),
          ).get
        }

        _ = println(s"debugging scheduledDataCountInScheduler: $scheduledDataCountInScheduler")

        //Verification
        scheduledCampaignProspectCount: Int <- Future {
          CampaignProspectTestDAO.getCountOfCampaignProspectSchedulerStatusDue(
            campaign_id = CampaignId(campaign.campaign.id),
            teamId = teamId,
            prospect_ids = campaign.addProspect.map(p => ProspectId(p.id)),
            current_step_id = StepId(campaign.campaign.head_step_id.get),
          ).get
        }

        _ = println(s"debugging scheduledCampaignProspectCount: $scheduledCampaignProspectCount")

      } yield {

        // checking total prospects in campaign
        assert(campaign.addProspect.length == 2000)

        println(s"debugging campaign.addProspect.length: ${campaign.addProspect.length}")

        // checking number of prospects scheduled
        val allScheduledProspects = campaign.addProspect.filter(p => scheduledDataCountInScheduler.contains(p.id))
        assert(allScheduledProspects.length == 200)

        // checking number of prospects scheduled per account according to 'max_emails_per_prospect_account_per_day' limit
        val prospectsGroupedByProspectAccount = allScheduledProspects.groupBy(p => p.internal.prospect_account_id)
        prospectsGroupedByProspectAccount.foreach(grp => {
          assert(grp._2.length == max_emails_per_prospect_account_per_day.get)
        })

        assert(scheduledCampaignProspectCount == 200)

      }

    }

    /*
    Head step is a condition - If user has phone. schedule call otherwise email.
     */
    it("should schedule linkedin or email step depending on the head step condition V1- schedule linkedin first - should read Drip campaign from FE") {
      val initialData: InitialData = SRSetupAndDeleteFixtures.createInitialData(true).get

      val account: Account = initialData.account
      val teamId: TeamId = TeamId(account.teams.head.team_id)

      val campaignFut: Future[StartedCampaignDetails] = for {
        startedCampaignDetails: StartedCampaignDetails <- CampaignUtils.createAndStartDripCampaign(
          initialData = initialData,
          designDripForCase = 6
        )


      } yield {
        startedCampaignDetails
      }

      val resFut: Future[(List[Long], List[Long])] = campaignFut.flatMap(campaign => {
        println(s"debugging Linkedin Scheduled: ${campaign}")
        for {
          _: Int <- Future.fromTry {
            SchedulerTestDAO.addLastScheduledAtForCampaign(
              campaignId = campaign.campaignId,
              teamId = teamId
            )
          }
          scheduleTaskData2: ScheduleTasksData <- {
            println("Scheduling One")
            ScheduleTaskFixture.scheduleTaskForLinkedinChannel(
              linkedinChannelData = ChannelData.LinkedinChannelData(linkedinSettingId = initialData.linkedinAccountSettings.get.settings.uuid),
              teamId = teamId
            )
          }

          printing = println(s"debugging scheduleTaskData2: ${scheduleTaskData2}")

          scheduleTaskData1: ScheduleTasksData <- {
            println("Scheduling Emails")
            ScheduleTaskFixture.scheduleTaskForEmailChannel(
              emailChannelData = EmailChannelData(
                emailSettingId = initialData.emailSetting.get.id.get.emailSettingId
              ),
              teamId = teamId
            )
          }

          printing2 = println(s"debugging scheduleTaskData1: ${scheduleTaskData1}")

          scheduledProspectIdsForLinkedin: List[Long] <- Future.fromTry {
            println("Verifying prospects are scheduled or not for Linkedin")
            SchedulerTestDAO.getProspectIdForCampaignAndChannelType(
              campaignId = campaign.campaignId,
              channelType = ChannelType.LinkedinChannel
            )
          }

          printing3 = println(s"debugging scheduledProspectIdsForLinkedin: ${scheduledProspectIdsForLinkedin}")

          scheduledProspectIdsForEmail: List[Long] <- Future.fromTry {
            println("Verifying prospects are scheduled or not for Email")
            SchedulerTestDAO.getProspectIdForCampaignAndChannelType(
              campaignId = campaign.campaignId,
              channelType = ChannelType.EmailChannel
            )
          }
          printing4 = println(s"debugging scheduledProspectIdsForEmail: ${scheduledProspectIdsForEmail}")
        } yield {
          (scheduledProspectIdsForEmail, scheduledProspectIdsForLinkedin)
        }
      })

      Await.result(
        resFut,
        Duration.create(500, SECONDS)
      )

      resFut.flatMap(res => {
          println(res)
          assert(res._1.length == 1 && res._2.length == 2)
        })
        .recover {
          case e =>
//            println("debugging", e.toString)
            println(LogHelpers.getStackTraceAsString(e))
            assert(false)
        }

//      println(s"debugging resFut: $resFut")
      resFut.onComplete {

        case Success((v1, v2)) =>
          println(s"debugging v1: ${v1} v2: ${v2}")
          assert(true)
        case Failure(er) =>
          println(s"debugging err: ${er}")
          assert(false)

      }
      assert(resFut.isCompleted)

    }

  }

  describe("Scheduler testing auto-email campaign with bounced reply it should get completed"){

    it("Simple auto-email campaign with bounced reply"){

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
      val account: Account = initialData.account
      val emailSetting: EmailSetting = initialData.emailSetting.get
      val teamId: TeamId = TeamId(account.teams.head.team_id)
      val orgId = OrgId(id = initialData.account.org.id)
      val accountId = AccountId(id = account.internal_id)
      val taId: Long = account.teams.head.access_members.head.ta_id

      val scheduleTaskData: Future[SchedulerIntegrationTestResult] = for {

        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 3
        )

        // step-2 addition
        addStep_2: CampaignStepVariant <- {
          CreateStepForCampaignFixture.createAutoEmailStepForCampaign(
            orgId = orgId,
            teamId = teamId,
            accountId = accountId,
            taId = taId,
            campaignId = CampaignId(createAndStartCampaignData.createCampaign.id),
            parentId = createAndStartCampaignData.addStep.step_id
          )
        }

        //Test results after scheduling
        result_1: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
          emailChannelData = EmailChannelData(
            emailSettingId = emailSetting.id.get.emailSettingId
          ),
          teamId = teamId
        )

        reInitializeForSecondScheduling_1: Int <- ReSchedulingFixture.initializeDataForReScheduling(
          orgId = orgId,
          accountId = accountId,
          teamId = teamId,
          emailSetting = emailSetting,
          campaign = createAndStartCampaignData.campaign,
          prospectList = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
        )

        read_email <- { // needs fixing
          Future.fromTry(emailReplyTrackingModelV2.saveEmailsAndRepliesFromInboxV3(
            account = account,
            accountId = account.internal_id,
            inboxEmailSetting = initialData.emailSetting.get,
            team_id = teamId.id,
            adminReplyFromSRInbox = false,
            markProspectAsCompleted = true,
            auditRequestLogId = "test_logger",
            replyHandling =  ReplyHandling.PAUSE_ALL_PROSPECT_CAMPAIGNS_ON_REPLY,
            senderEmails = Seq(emailSetting.email),

            emailMessages =
              Seq(EmailMessageTracked(
                inbox_email_setting_id = emailSetting.id.get.emailSettingId,
                from = IEmailAddress(
                  name = createAndStartCampaignData.addProspect.head.first_name,
                  email = createAndStartCampaignData.addProspect.head.email.get
                ), // Fixme check this

                // v3 specific start
                to_emails = Seq(IEmailAddress(
                  name = createAndStartCampaignData.addProspect.head.first_name,
                  email = createAndStartCampaignData.addProspect.head.email.get
                )), // Seq[IEmailAddress], // only for newinbox api
                // v3 specific end


                subject = "",

                body = "",
                base_body = "",
                text_body = "",

                references_header = None, //Option[String], // Main

                campaign_id = Some(createAndStartCampaignData.campaign.id),
                step_id = None,

                prospect_id_in_campaign = None, // prospect_id for whom campaign should be paused

                prospect_account_id_in_campaign = None,

                campaign_name = None,
                step_name = None,

                received_at = DateTime.now(),
                recorded_at = DateTime.now(),
                sr_inbox_read = true,  // check this
                original_inbox_folder = None, // check this
                email_status = EmailReplyStatus(
                  replyType =  EmailReplyType.DELIVERY_FAILED,
                  isReplied =  false,
                  bouncedData =Some(BounceData(
                    bounced_at = DateTime.now(),
                    bounce_type =  EmailReplyBounceType.EmailAddressNotFound,
                    bounce_reason = "",
                    is_soft_bounced = false
                  )),
                  isUnsubscribeRequest= false,
                  isAutoReply = false,
                  isOutOfOfficeReply = false,
                  isInvalidEmail = false,
                  isForwarded = false
                ),

                message_id = "",
                full_headers = Json.obj(),

                scheduled_manually = false,

                reply_to = None, // check this one

                email_thread_id = None, // check this also
                gmail_msg_id = None,
                gmail_thread_id = None,

                outlook_msg_id = None,
                outlook_conversation_id = None,
                outlook_response_json = None,

                cc_emails = Seq(),
                in_reply_to_header = None,
                team_id = teamId.id,
                account_id = account.internal_id,


                internal_tracking_note = InternalTrackingNote.NEW_LASTWEEK_DELIVERYFAILED,

                tempThreadId = Some(235)
              )),

          ))
        }

        //Test result after scheduling
        result_2: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
          emailChannelData = EmailChannelData(
            emailSettingId = emailSetting.id.get.emailSettingId
          ),
          teamId = teamId
        )

        //Verification
        scheduledDataCountInScheduler: List[Int] <- Future {
          SchedulerTestDAO.getCountOfScheduledProspectForACampaign(
            campaign_id = CampaignId(createAndStartCampaignData.campaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            step_id = StepId(createAndStartCampaignData.campaign.head_step_id.get),
          ).get
        }

        //Verification
        scheduledCampaignProspectCount: Int <- Future {
          CampaignProspectTestDAO.getCountOfCampaignProspectSchedulerStatusDue(
            campaign_id = CampaignId(createAndStartCampaignData.campaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            current_step_id = StepId(addStep_2.step_id),
            is_sent = true
          ).get
        }

      } yield {
        SchedulerIntegrationTestResult(
          scheduleTasksData = result_2,
          emailScheduledCountForCampaign = scheduledDataCountInScheduler.length,
          scheduledCampaignProspectCount = scheduledCampaignProspectCount
        )
      }
      scheduleTaskData.map(p => {
        assert(p.scheduleTasksData.saved_tasks_count == 2
          && p.scheduledCampaignProspectCount == 2)
      }).recover({ case e =>
        println(e.printStackTrace())
        assert(false)
      })



    }

    it("Simple auto-email campaign with actual reply") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
      val account: Account = initialData.account
      val emailSetting: EmailSetting = initialData.emailSetting.get
      val teamId: TeamId = TeamId(account.teams.head.team_id)
      val orgId = OrgId(id = initialData.account.org.id)
      val accountId = AccountId(id = account.internal_id)
      val taId: Long = account.teams.head.access_members.head.ta_id

      val scheduleTaskData: Future[SchedulerIntegrationTestResult] = for {

        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 3
        )

        // step-2 addition
        addStep_2: CampaignStepVariant <- {
          CreateStepForCampaignFixture.createAutoEmailStepForCampaign(
            orgId = orgId,
            teamId = teamId,
            accountId = accountId,
            taId = taId,
            campaignId = CampaignId(createAndStartCampaignData.createCampaign.id),
            parentId = createAndStartCampaignData.addStep.step_id
          )
        }

        //Test results after scheduling
        result_1: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
          emailChannelData = EmailChannelData(
            emailSettingId = emailSetting.id.get.emailSettingId
          ),
          teamId = teamId
        )

        reInitializeForSecondScheduling_1: Int <- ReSchedulingFixture.initializeDataForReScheduling(
          orgId = orgId,
          accountId = accountId,
          teamId = teamId,
          emailSetting = emailSetting,
          campaign = createAndStartCampaignData.campaign,
          prospectList = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
        )

        read_email <- { // needs fixing
          Future.fromTry(emailReplyTrackingModelV2.saveEmailsAndRepliesFromInboxV3(
            account = account,
            accountId = account.internal_id,
            inboxEmailSetting = initialData.emailSetting.get,
            team_id = teamId.id,
            adminReplyFromSRInbox = false,
            markProspectAsCompleted = true,
            auditRequestLogId = "test_logger",
            replyHandling = ReplyHandling.PAUSE_ALL_PROSPECT_CAMPAIGNS_ON_REPLY,
            senderEmails = Seq(emailSetting.email),

            emailMessages =
              Seq(EmailMessageTracked(
                inbox_email_setting_id = emailSetting.id.get.emailSettingId,
                from = IEmailAddress(
                  name = createAndStartCampaignData.addProspect.head.first_name,
                  email = createAndStartCampaignData.addProspect.head.email.get
                ), // Fixme check this

                // v3 specific start
                to_emails = Seq(IEmailAddress(
                  name = createAndStartCampaignData.addProspect.head.first_name,
                  email = createAndStartCampaignData.addProspect.head.email.get
                )), // Seq[IEmailAddress], // only for newinbox api
                // v3 specific end


                subject = "",

                body = "",
                base_body = "",
                text_body = "",

                references_header = None, //Option[String], // Main

                campaign_id = Some(createAndStartCampaignData.campaign.id),
                step_id = None,

                prospect_id_in_campaign = None, // prospect_id for whom campaign should be paused

                prospect_account_id_in_campaign = None,

                campaign_name = None,
                step_name = None,

                received_at = DateTime.now(),
                recorded_at = DateTime.now(),
                sr_inbox_read = true, // check this
                original_inbox_folder = None, // check this
                email_status = EmailReplyStatus(
                  replyType = EmailReplyType.NOT_CATEGORIZED,
                  isReplied = true,
                  bouncedData = None,
                  isUnsubscribeRequest = false,
                  isAutoReply = false,
                  isOutOfOfficeReply = false,
                  isInvalidEmail = false,
                  isForwarded = false
                ),

                message_id = "",
                full_headers = Json.obj(),

                scheduled_manually = false,

                reply_to = None, // check this one

                email_thread_id = None, // check this also
                gmail_msg_id = None,
                gmail_thread_id = None,

                outlook_msg_id = None,
                outlook_conversation_id = None,
                outlook_response_json = None,

                cc_emails = Seq(),
                in_reply_to_header = None,
                team_id = teamId.id,
                account_id = account.internal_id,


                internal_tracking_note = InternalTrackingNote.NONE,

                tempThreadId = Some(236)
              )),

          ))
        }

        //Test result after scheduling
        result_2: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
          emailChannelData = EmailChannelData(
            emailSettingId = emailSetting.id.get.emailSettingId
          ),
          teamId = teamId
        )

        //Verification
        scheduledDataCountInScheduler: List[Int] <- Future {
          SchedulerTestDAO.getCountOfScheduledProspectForACampaign(
            campaign_id = CampaignId(createAndStartCampaignData.campaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            step_id = StepId(createAndStartCampaignData.campaign.head_step_id.get),
          ).get
        }

        //Verification
        scheduledCampaignProspectCount: Int <- Future {
          CampaignProspectTestDAO.getCountOfCampaignProspectSchedulerStatusDue(
            campaign_id = CampaignId(createAndStartCampaignData.campaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            current_step_id = StepId(addStep_2.step_id),
            is_sent = true
          ).get
        }

      } yield {
        SchedulerIntegrationTestResult(
          scheduleTasksData = result_2,
          emailScheduledCountForCampaign = scheduledDataCountInScheduler.length,
          scheduledCampaignProspectCount = scheduledCampaignProspectCount
        )
      }
      scheduleTaskData.map(p => {
        assert(p.scheduleTasksData.saved_tasks_count == 2
          && p.scheduledCampaignProspectCount == 2)
      }).recover({ case e =>
        println(e.printStackTrace())
        assert(false)
      })


    }


  }


}
