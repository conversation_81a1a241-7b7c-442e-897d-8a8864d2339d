package db_test_spec.api.scheduler.fixtures

import api.campaigns.Campaign
import api.campaigns.models.SendEmailFromCampaignDetails
import api.emails.{EmailSetting, EmailToBeSent}

object DefaultParametersFixtureForInitializingDataForReScheduling {

  def defaultEmailToBeSent(
                            emailSetting: EmailSetting,
                            campaign: Campaign
                          ): EmailToBeSent = EmailToBeSent(
    to_emails = Seq(),
    from_email = emailSetting.email,
    cc_emails = Seq(),
    bcc_emails = Seq(),
    from_name = emailSetting.first_name + " " + emailSetting.last_name,
    reply_to_email = Some(emailSetting.email),
    reply_to_name = Some(emailSetting.first_name + " " + emailSetting.last_name),
    subject = "revert email subject",
    textBody = "So, Tell me how much you are enjoying writing integration test ?  ",
    htmlBody = " <div> So, Tell me how much you are enjoying writing integration test ?  </div> ",
    // baseBody: String,
    message_id = Some(campaign.name),
    references_header = None,

    in_reply_to_id = None,
    in_reply_to_references_header = None,
    in_reply_to_sent_at = None,

    sender_email_settings_id = campaign.settings.campaign_email_settings.head.sender_email_setting_id.emailSettingId,

    email_thread_id = None,
    gmail_msg_id = None,
    gmail_thread_id = None,

    outlook_msg_id = None,
    outlook_conversation_id = None,
    outlook_response_json = None,

    gmail_fbl = None,
    list_unsubscribe_header = None,
    hasCustomTrackingDomain = false,
    rep_smtp_reverse_dns_host = None
  )

  def defaultSendEmailFromCampaignDetails(
                                    campaign: Campaign
                                  ): SendEmailFromCampaignDetails = SendEmailFromCampaignDetails(
    campaign_id = campaign.id,
    campaign_name = campaign.name,
    // stepDetails can be none when email is being sent manually from Inbox.
    // Example: sendNewEmailManually in InboxV3Service
    stepDetails = None // change when using below
  )
}
