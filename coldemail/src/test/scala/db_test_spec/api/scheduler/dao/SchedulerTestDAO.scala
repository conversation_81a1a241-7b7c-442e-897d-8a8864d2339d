package db_test_spec.api.scheduler.dao

import api.accounts.TeamId
import api.campaigns.models.CurrentStepStatusForScheduler
import api.campaigns.services.CampaignId
import api.emails.models.EmailReplyType.EmailReplyType
import api.prospects.models.ProspectCategory.ProspectCategory
import api.prospects.models.{ProspectCategory, ProspectId, StepId}
import io.smartreach.esp.api.emails.EmailSettingId
import org.joda.time.DateTime
import scalikejdbc.interpolation.SQLSyntax
import scalikejdbc.jodatime.JodaWrappedResultSet.*
import scalikejdbc.{DB, scalikejdbcSQLInterpolationImplicitDef}
import sr_scheduler.models.ChannelType

import scala.util.Try

object SchedulerTestDAO {
  def getCountOfScheduledProspectForACampaign(
                                               campaign_id: CampaignId,
                                               teamId: TeamId,
                                               step_id: StepId,
                                               prospect_ids: Seq[ProspectId]
                                             ): Try[List[Int]] = Try {
    DB readOnly { implicit session =>
      sql"""
          SELECT
            prospect_id
          FROM emails_scheduled es
          WHERE team_id = ${teamId.id}
            AND campaign_id = ${campaign_id.id}
            AND prospect_id IN (${prospect_ids.map(p=>p.id)})
            AND step_id = ${step_id.id}
            AND sent = false
       """
        .map(rs =>  rs.int("prospect_id"))
        .list
        .apply()
    }
  }

  def getCountOfEmailsSentToProspectInTimeframe(
                                               prospectIds: Seq[ProspectId],
                                               teamId: TeamId,
                                               fromTime: DateTime,
                                               tillTime: DateTime
                                               ): Try[Int] = Try {
    DB readOnly { implicit session =>
      sql"""
           SELECT count(*) as total_emails_sent
           FROM emails_scheduled es
           WHERE prospect_id IN (${prospectIds.map(_.id)})
           AND sent = true
           AND team_id = ${teamId.id}
           AND scheduled_at >= $fromTime
           AND scheduled_at <= $tillTime
         """
        .map(rs => rs.int("total_emails_sent"))
        .single
        .apply()
        .get

    }
  }

  def getCountOfScheduledTasksForACampaign(
                                               campaign_id: CampaignId,
                                               teamId: TeamId,
                                               step_id: StepId,
                                               prospect_ids: Seq[ProspectId]
                                             ): Try[List[Int]] = Try {
    DB readOnly { implicit session =>
      sql"""
          SELECT
            prospect_id
          FROM tasks t
          WHERE team_id = ${teamId.id}
            AND campaign_id = ${campaign_id.id}
            AND prospect_id IN (${prospect_ids.map(p => p.id)})
            AND step_id = ${step_id.id}
            AND due_at  is not null
       """
        .map(rs => rs.int("prospect_id"))
        .list
        .apply()
    }
  }

  def selectEmailScheduledForIntegrationTest(
                                              prospect_ids: Seq[Long],
                                              campaign_id: Long,
                                              team_id: Long
                                            ): Try[List[(Int, Int, String, Long)]] = Try {

    DB.readOnly { implicit session => {

      sql"""
                SELECT
                es.step_id,
                es.id,
                es.prospect_id,
                ems.to_email
                from emails_scheduled es
                inner join email_message_data ems on ems.es_id = es.id and ems.team_id = es.team_id
                where
                es.prospect_id in  (${prospect_ids})
                and es.campaign_id = ${campaign_id}
                and es.team_id  = ${team_id}
                and es.sent = false

              """
        .map(rs => (rs.int("id"), rs.int("prospect_id"), rs.string("to_email"), rs.long("step_id")))
        .list
        .apply()

    }
    }
  }

  def checkIfCampaignsProspectsCompleted(
                                              prospect_id: Long,
                                              campaign_id: Long,
                                              team_id: Long

                                            ): Try[Boolean] = Try {

    DB.readOnly { implicit session => {

      sql"""
               SELECT
                completed
                 from campaigns_prospects where
               prospect_id = ${prospect_id}
               and campaign_id = ${campaign_id}
               and team_id  = ${team_id}

             """
        .map(rs => rs.boolean("completed"))
        .single
        .apply()
        .get

    }}

  }

  def addLastScheduledAtForCampaign(
                                   campaignId: CampaignId,
                                   teamId: TeamId,
                                   sent_ago_in_hours: Int = 48
                                   ): Try[Int] = Try{
    DB autoCommit {implicit session =>
      sql"""
                         UPDATE campaigns
                         SET last_scheduled_at = now() - interval '${SQLSyntax.createUnsafely(sent_ago_in_hours.toString)} hours',
                         last_email_sent_at = now() - interval '26 hours'
                         WHERE id = ${campaignId.id}
                         AND team_id = ${teamId.id}
                         ;
                         """
        .update
        .apply()
    }
  }

  def getProspectIdForCampaignAndChannelType(
                                              campaignId: CampaignId,
                                              channelType: ChannelType
                                            ): Try[List[Long]] = Try{
    DB readOnly { implicit session =>
      sql"""
                 SELECT prospect_id FROM campaigns_prospects
                 WHERE campaign_id = ${campaignId.id}
                 AND current_step_channel_type = ${channelType.toString}
                 and last_scheduled > now() - interval '20 hours'
                 ;
                 """
        .map(rs => rs.long("prospect_id"))
        .list
        .apply()
    }
  }

  def getProspectIdForCampaignAndChannelTypeFromTasks(
                                                       campaignId: CampaignId,
                                                       channelType: ChannelType
                                                     ): Try[List[Long]] = Try{
    DB readOnly {implicit session =>
    sql"""
                 SELECT prospect_id FROM tasks
                 WHERE campaign_id = ${campaignId.id}
                 AND channel_type = ${channelType.toString}
                 ;
                 """
      .map(rs => rs.long("prospect_id"))
      .list
      .apply()
  }
  }

  def getProspectsCount(
                                                       campaignId: CampaignId,
                                                       team_id : TeamId
                                                     ): Try[Long] = Try {
    DB readOnly { implicit session =>
      sql"""
                 SELECT count(prospect_id)
                  FROM  campaigns_prospects
                 WHERE campaign_id = ${campaignId.id}
                 AND team_id = ${team_id.id}
                 """
        .map(rs => rs.long("count"))
        .single
        .apply()
        .getOrElse(0)
    }
  }

  def getProspectIdsFromCampaign(
                         campaignId: CampaignId,
                         team_id: TeamId
                       ): Try[List[Long]] = Try {
    DB readOnly { implicit session =>
      sql"""
                 SELECT prospect_id
                  FROM  campaigns_prospects
                 WHERE campaign_id = ${campaignId.id}
                 AND team_id = ${team_id.id}
                 """
        .map(rs => rs.long("prospect_id"))
        .list
        .apply()
        }
  }

  def setLastScheduledForCampaignProspects(
                                          campaignId: CampaignId,
                                          prospectIds: Seq[ProspectId],
                                          teamId: TeamId
                                          ): Try[Int] = Try {
    DB autoCommit { implicit session =>
      sql"""
                         UPDATE campaigns_prospects
                         SET last_scheduled = now() - interval '2 days'
                         WHERE campaign_id = ${campaignId.id}
                         AND prospect_id IN (${prospectIds.map(_.id)})
                         AND team_id = ${teamId.id}
                         and sent_at > now() - interval '20 hours'
                         ;
                         """
        .update
        .apply()
    }
  }

  def updateLatestEmailScheduledAtForEmailSetting(
                                                 emailSettingId: EmailSettingId,
                                                 teamId: TeamId
                                                 ): Try[Int] = Try {
    DB autoCommit {implicit session =>
      sql"""
                         UPDATE email_settings
                         SET latest_email_scheduled_at = now() - interval '2 days',
                         last_read_for_replies = now()
                         WHERE id = ${emailSettingId.emailSettingId}
                         AND team_id = ${teamId.id}
                         ;
                         """
        .update
        .apply()
    }
  }

  def updateLastTouchedAtForProspectMetadata(
                                            prospectIds: Seq[ProspectId],
                                            teamId: TeamId
                                            ): Try[Int] = Try {
    DB autoCommit {implicit session =>
      sql"""
                         UPDATE prospects_metadata
                         SET last_touched_at = now() - interval '2 days',
                         last_prospects_touched_count_updated_at = now() - interval '2 days'
                         WHERE prospect_id IN (${prospectIds.map(_.id)})
                         AND team_id = ${teamId.id}
                         ;
                         """
        .update
        .apply()
    }
  }

  def updateEmailScheduledToMakeSentSteps(
                                         campaignId: CampaignId,
                                         sent_ago_in_hours: Int,
                                         prospects: Seq[Long] = Seq()
                                         ): Try[Int] = Try {
    val seqOfProspects = if(prospects.nonEmpty){
      sqls"""
            and prospect_id in (${prospects})
          """
    } else {
      sqls""""""
    }

    DB autoCommit { implicit session =>
      sql"""
            UPDATE emails_scheduled
              SET
                  scheduled_at = CASE
                      WHEN scheduled_at IS NULL THEN NOW() - INTERVAL '${SQLSyntax.createUnsafely(sent_ago_in_hours.toString)} hours'
                      ELSE scheduled_at - INTERVAL '${SQLSyntax.createUnsafely(sent_ago_in_hours.toString)} hours'
                  END,
                  sent_at = CASE
                      WHEN sent_at IS NULL THEN NOW() - INTERVAL '${SQLSyntax.createUnsafely(sent_ago_in_hours.toString)} hours'
                      ELSE sent_at - INTERVAL '${SQLSyntax.createUnsafely(sent_ago_in_hours.toString)} hours'
                  END,
                  sent = true
              WHERE
                  campaign_id = ${campaignId.id}
                  ${seqOfProspects};
               """
        .update
        .apply()

    }
  }


  def markAsBounced(
                     campaignId: CampaignId,
                     prospectId: ProspectId
                   ): Try[Int] = Try {
    DB autoCommit { implicit session =>
      sql"""
                 update emails_scheduled
                 set
                 bounced = true,
                 bounced_at = now() - interval '1 day'
                 where
                 campaign_id = ${campaignId.id}
                 and prospect_id = ${prospectId.id}
               """
        .update
        .apply()

    }
  }

  def markAsOpen(
                  campaignId: CampaignId,
                  prospectId: ProspectId
                ): Try[Int] = Try {
    DB autoCommit { implicit session =>
      sql"""
                 update emails_scheduled
                 set
                 opened = true,
                 opened_at = now() - interval '1 day'
                 where
                 campaign_id = ${campaignId.id}
                 and prospect_id = ${prospectId.id}
               """
        .update
        .apply()

    }
  }

  def markAsReplied(
                  campaignId: CampaignId,
                  prospectId: ProspectId
                ): Try[Int] = Try {
    DB autoCommit { implicit session =>
      sql"""
                 update emails_scheduled
                 set
                 replied = true,
                 replied_at = now() - interval '1 day'
                 where
                 campaign_id = ${campaignId.id}
                 and prospect_id = ${prospectId.id}
               """
        .update
        .apply()

    }
  }

  def updateReplyTypeForEmailScheduled(
                     campaignId: CampaignId,
                     prospectId: ProspectId,
                     reply_type : EmailReplyType
                   ): Try[Int] = Try {
    DB autoCommit { implicit session =>
      sql"""
                 update emails_scheduled
                 set
                 reply_type = ${reply_type.toString}
                 where
                 campaign_id = ${campaignId.id}
                 and prospect_id = ${prospectId.id}
               """
        .update
        .apply()

    }
  }

  def updateEmailSettingToMakeSentSteps(
                                         emailSettingId: EmailSettingId
                                       ): Try[Int] = Try {
    DB autoCommit { implicit session =>
      sql"""
                 update email_settings
                 set
                 last_read_for_replies = now()
                 where
                 id = ${emailSettingId.emailSettingId}
               """
        .update
        .apply()

    }
  }
  def updateCampaignChannelSettingsToMakeSentSteps(
                                                    campaignId: CampaignId,
                                                    sent_ago_in_hours: Int
                                                  ): Try[Int] = Try {
    DB autoCommit { implicit session =>
      sql"""
                 update campaign_channel_settings
                 set
                 next_to_be_scheduled_at = now() - interval '${SQLSyntax.createUnsafely(sent_ago_in_hours.toString)}  hours',
                 updated_at = now() - interval '${SQLSyntax.createUnsafely(sent_ago_in_hours.toString)}  hours'
                 where
                 campaign_id = ${campaignId.id}
               """
        .update
        .apply()

    }
  }
  def updateCampaignEmailSettingsToMakeSentSteps(
                                                    campaignId: CampaignId,
                                                    sent_ago_in_hours: Int
                                                  ): Try[Int] = Try {
    DB autoCommit { implicit session =>
      sql"""
                 update campaign_email_settings
                 set
                 next_to_be_scheduled_at = now() - interval '${SQLSyntax.createUnsafely(sent_ago_in_hours.toString)} hours',
                 last_schedule_done_at = now() - interval '${SQLSyntax.createUnsafely(sent_ago_in_hours.toString)} hours'
                 where
                 campaign_id = ${campaignId.id}
               """
        .update
        .apply()

    }
  }
  def updateCampaignProspectsToMakeSentSteps(
                                              campaignId: CampaignId,
                                              sent_ago_in_hours: Int,
                                              mark_scheduled_status_done: Boolean = true,
                                              prospects: Seq[Long] = Seq()
                                            ): Try[Int] = Try {

    val prospectSQL = if(prospects.nonEmpty){

      sqls"""
            AND prospect_id in ${prospects}
          """

    }else{

      sqls""""""

    }


    val schedulerStatus = if(mark_scheduled_status_done){

      sqls"""
            current_step_status_for_scheduler = ${CurrentStepStatusForScheduler.Done.toKey},
            """

    }
    else{

      sqls""""""
    }

    DB autoCommit { implicit session =>
      sql"""
                 update campaigns_prospects
                 set
                 last_scheduled = now() - interval '${SQLSyntax.createUnsafely(sent_ago_in_hours.toString)} hours',
                 created_at = now() - interval '${SQLSyntax.createUnsafely(sent_ago_in_hours.toString)} hours',
                 sent = true,
                 sent_at = now() - interval '${SQLSyntax.createUnsafely(sent_ago_in_hours.toString)} hours',
                 ${schedulerStatus}
                 latest_task_done_or_skipped_at = now() - interval '26 hours',
                 next_check_for_scheduling_at = now() - interval '10 hours'
                 where
                  campaign_id = ${campaignId.id}
                  $prospectSQL
               """
        .update
        .apply()

    }
  }
  def markAsValidForProspectEmail(
                                 ids: Seq[ProspectId],
                                 teamId: TeamId
                                 ): Try[Int] = Try {

    DB autoCommit { implicit session =>

      sql"""
          UPDATE prospects_emails
          SET
            invalid_email = false,
            email_checked = true,
            updated_at = now()
          WHERE prospect_id IN (${ids.map(p=>p.id)})
          AND is_primary
          AND team_id = ${teamId.id};
           """
        .update
        .apply()
    }


  }

  case class CampaignProspectTestData(
    prospect_id: Long,
    current_step_id: Option[Long],
    last_scheduled: Option[DateTime],
    current_step_status_for_scheduler: Option[CurrentStepStatusForScheduler],
    latest_task_done_or_skipped_at: Option[DateTime],
    current_step_type: Option[String],
    current_step_task_id: Option[String]
  )
  
  def getCampaignProspectTestData(
                                campaignId: CampaignId,
                                prospectId: ProspectId,
                                teamId: TeamId
                              ): Try[Option[CampaignProspectTestData]] = Try {
    DB readOnly { implicit session =>
      sql"""
        SELECT
          prospect_id,
          current_step_id,
          last_scheduled,
          current_step_status_for_scheduler,
          latest_task_done_or_skipped_at,
          current_step_type,
          current_step_task_id
        FROM campaigns_prospects
        WHERE campaign_id = ${campaignId.id}
          AND prospect_id = ${prospectId.id}
          AND team_id = ${teamId.id}
      """
      .map(rs => CampaignProspectTestData(
        prospect_id = rs.long("prospect_id"),
        current_step_id = rs.longOpt("current_step_id"),
        last_scheduled = rs.jodaDateTimeOpt("last_scheduled"),
        current_step_status_for_scheduler = rs.stringOpt("current_step_status_for_scheduler").map(CurrentStepStatusForScheduler.fromString(_).get),
        latest_task_done_or_skipped_at = rs.jodaDateTimeOpt("latest_task_done_or_skipped_at"),
        current_step_type = rs.stringOpt("current_step_type"),
        current_step_task_id = rs.stringOpt("current_step_task_id")
      ))
      .single
      .apply()
    }
  }
}
