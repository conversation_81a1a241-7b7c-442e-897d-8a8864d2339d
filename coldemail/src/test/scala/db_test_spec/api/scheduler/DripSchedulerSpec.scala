package db_test_spec.api.scheduler

import api.AppConfig
import api.accounts.models.{AccountId, OrgId}
import api.accounts.{Account, ReplyHandling, TeamId}
import api.blacklist.models.{BlacklistCreateOrDeleteApiLevel, BlacklistFindApiLevel}
import api.blacklist.{Blacklist, BlacklistCreateEmailsForm, BlacklistTeamLevel, CreateOrUpdateBlacklistError}
import api.campaigns.{CampaignProspectUpdateScheduleStatus, CampaignStepVariant, CampaignWithStatsAndEmail}
import api.campaigns.models.{CampaignName, CampaignStepType, CurrentStepStatusForScheduler, CurrentStepStatusForSchedulerData, IgnoreProspectsInOtherCampaigns}
import api.campaigns.services.CampaignId
import api.emails.models.EmailReplyType
import api.emails.{DBEmailMessagesSavedResponse, EmailMessageTracked, EmailSetting}
import api.prospects.{ProspectCreateFormData, ProspectDeduplicationColumnsData}
import api.prospects.models.{ProspectCategory, ProspectId, StepId, UpdateProspectType}
import api.prospects.service.ProspectDeduplicationDataWithId
import api.scylla.dao.BounceData
import app.testapp_dao.BlacklistTestDAO
import db_test_spec.api.{AppSpecFixture, DbTestingBeforeAllAndAfterAll, InitialData, SRSetupAndDeleteFixtures, SchedulerIntegrationTestResult}
import db_test_spec.api.accounts.fixtures.{EmailScheduledNewFixture, NewAccountAndEmailSettingData, NewAccountAndWhatsappSettingData}
import db_test_spec.api.campaigns.dao.{CampaignProspectTestDAO, CampaignTestDAO}
import db_test_spec.api.campaigns.fixtures.CreateStepForCampaignFixture
import db_test_spec.api.campaigns.test_utils.{CampaignUtils, CreateAndStartCampaignData, StartedCampaignDetails}
import db_test_spec.api.emails.dao.EmailScheduleTestDAO
import db_test_spec.api.emails.dao.EmailSettingTestDAO
import db_test_spec.api.emails.fixtures.DefaultEmailScheduledParameterFixtures.generateEmailMessageTracked
import db_test_spec.api.prospects.fixtures.{DefaultProspectParameterFixture, ProspectFixtureForIntegrationTest}
import db_test_spec.api.scheduler.dao.SchedulerTestDAO
import db_test_spec.api.scheduler.fixtures.{ReSchedulingFixture, ScheduleTaskFixture}
import db_test_spec.utils.SrRandomTestUtils
import eventframework.ProspectObject
import io.smartreach.esp.api.emails.{EmailSettingId, IEmailAddress}
import io.smartreach.esp.utils.email.EmailReplyBounceType
import org.joda.time.DateTime
import org.scalatest.{ParallelTestExecution, TestSuite}
import org.postgresql.jdbc.PgArray
import org.scalamock.scalatest.MockFactory
import org.scalatest.funspec.AnyFunSpec
import org.scalatest.matchers.should.Matchers
import play.api.libs.json.{JsValue, Json}
import scalikejdbc.{DB, scalikejdbcSQLInterpolationImplicitDef}
import sr_scheduler.models.{CampaignDataToAddNextToBeScheduledAtForEmailChannel, CampaignEmailPriority, ChannelData, ChannelType}
import sr_scheduler.models.ChannelData.{CallChannelData, EmailChannelData, WhatsAppChannelData}
import sr_scheduler.models.ChannelData.{EmailChannelData, WhatsAppChannelData}
import utils.SRLogger
import utils.email.EmailReplyStatus
import utils.email.services.InternalTrackingNote
import utils.helpers.LogHelpers
import utils.mq.channel_scheduler.channels.{NextStepFinderForDrip, ScheduleTasksData}
import utils_deploy.rolling_updates.models.SrRollingUpdateFeature
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService

import scala.concurrent.duration.{Duration, DurationInt, SECONDS}
import scala.concurrent.{Await, Future}
import scala.util.{Failure, Success}



  class DripSchedulerSpec extends   DbTestingBeforeAllAndAfterAll with ParallelTestExecution  {

    override lazy val isMainMqWorkerNeeded: Boolean = true

  describe("Testing Drip Campaign") {

//    println(s"isProd::${AppConfig.isProd} isTest :: ${AppConfig.isTest}puppeteerBasePath :: ${AppConfig.Linkedin_Automation.puppeteerBasePath}")
    /*
    CASE head_step_condition -> has_phone_number
      yes edge has condition -> has_email
      no edge has condition -> has_linkedin_url
      Below has_email, send_email step then send_phone
      Below has_linkedin_url, send_linkedin then send_email
      3 Prospects -> 1 has phone and email, 1 has email and linkedin & 1 has only email.
    */
    it("should schedule linkedin / call or email depending on all conditions encountered V1- schedule linkedin first") {
      val initialData: InitialData = SRSetupAndDeleteFixtures.createInitialData(true).get

      val account: Account = initialData.account
      val teamId: TeamId = TeamId(account.teams.head.team_id)

      val resFut = for {
        campaign: StartedCampaignDetails <- CampaignUtils.createAndStartDripCampaign(
          initialData = initialData,
          designDripForCase = 1
        )

        scheduleTaskData2: ScheduleTasksData <- {
          ScheduleTaskFixture.scheduleTaskForLinkedinChannel(
            linkedinChannelData = ChannelData.LinkedinChannelData(linkedinSettingId = initialData.linkedinAccountSettings.get.settings.uuid),
            teamId = teamId
          )
        }

        _: Int <- Future.fromTry {
          SchedulerTestDAO.addLastScheduledAtForCampaign(
            campaignId = campaign.campaignId,
            teamId = teamId
          )
        }

        scheduleTaskData1: ScheduleTasksData <- {
          ScheduleTaskFixture.scheduleTaskForEmailChannel(
            emailChannelData = EmailChannelData(
              emailSettingId = initialData.emailSetting.get.id.get.emailSettingId
            ),
            teamId = teamId
          )
        }

        _: Int <- Future.fromTry {
          SchedulerTestDAO.addLastScheduledAtForCampaign(
            campaignId = campaign.campaignId,
            teamId = teamId
          )
        }

        _: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForCallChannel(
          callChannelData = CallChannelData(initialData.callSettingUuid.get),
          teamId = teamId
        )

        linkedinProspects: List[Long] <- Future.fromTry {
          SchedulerTestDAO.getProspectIdForCampaignAndChannelType(
            campaignId = campaign.campaignId,
            channelType = ChannelType.LinkedinChannel
          )
        }

        emailProspects: List[Long] <- Future.fromTry {
          SchedulerTestDAO.getProspectIdForCampaignAndChannelType(
            campaignId = campaign.campaignId,
            channelType = ChannelType.EmailChannel
          )
        }

        callProspects: List[Long] <- Future.fromTry {
          SchedulerTestDAO.getProspectIdForCampaignAndChannelType(
            campaignId = campaign.campaignId,
            channelType = ChannelType.CallChannel
          )
        }
      } yield {
        (linkedinProspects, emailProspects, callProspects)
      }

      resFut.map(res => {
        println(res)
        assert(res._1.length == 1 && res._2.length == 1 && res._3.length == 0)
      })
        .recover {
          case e =>
            println(e.toString)
            println(LogHelpers.getStackTraceAsString(e))
            assert(false)
        }
    }

    it("should schedule linkedin / call or email depending on all conditions encountered V2- schedule email first") {
      val initialData: InitialData = SRSetupAndDeleteFixtures.createInitialData(true).get
      val account: Account = initialData.account
      val teamId: TeamId = TeamId(account.teams.head.team_id)

      val resFut = for {
        campaign: StartedCampaignDetails <- CampaignUtils.createAndStartDripCampaign(
          initialData = initialData,
          designDripForCase = 1
        )
        scheduleTaskData1: ScheduleTasksData <- {
          ScheduleTaskFixture.scheduleTaskForEmailChannel(
            emailChannelData = EmailChannelData(
              emailSettingId = initialData.emailSetting.get.id.get.emailSettingId
            ),
            teamId = teamId
          )
        }

        _: Int <- Future.fromTry {
          SchedulerTestDAO.addLastScheduledAtForCampaign(
            campaignId = campaign.campaignId,
            teamId = teamId
          )
        }
        scheduleTaskData2: ScheduleTasksData <- {
          ScheduleTaskFixture.scheduleTaskForLinkedinChannel(
            linkedinChannelData = ChannelData.LinkedinChannelData(linkedinSettingId = initialData.linkedinAccountSettings.get.settings.uuid),
            teamId = teamId
          )
        }

        _: Int <- Future.fromTry {
          SchedulerTestDAO.addLastScheduledAtForCampaign(
            campaignId = campaign.campaignId,
            teamId = teamId
          )
        }



        _: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForCallChannel(
          callChannelData = CallChannelData(initialData.callSettingUuid.get),
          teamId = teamId
        )

        linkedinProspects: List[Long] <- Future.fromTry {
          SchedulerTestDAO.getProspectIdForCampaignAndChannelType(
            campaignId = campaign.campaignId,
            channelType = ChannelType.LinkedinChannel
          )
        }

        emailProspects: List[Long] <- Future.fromTry {
          SchedulerTestDAO.getProspectIdForCampaignAndChannelType(
            campaignId = campaign.campaignId,
            channelType = ChannelType.EmailChannel
          )
        }

        callProspects: List[Long] <- Future.fromTry {
          SchedulerTestDAO.getProspectIdForCampaignAndChannelType(
            campaignId = campaign.campaignId,
            channelType = ChannelType.CallChannel
          )
        }
      } yield {
        (linkedinProspects, emailProspects, callProspects)
      }

      resFut.map(res => {
          println(res)
          assert(res._1.length == 1 && res._2.length == 1 && res._3.length == 0)
        })
        .recover {
          case e =>
            println(e.toString)
            println(LogHelpers.getStackTraceAsString(e))
            assert(false)
        }
    }

    it("should schedule linkedin / call or email depending on all conditions encountered V3- schedule call first") {
      val initialData: InitialData = SRSetupAndDeleteFixtures.createInitialData(true).get
      val account: Account = initialData.account
      val teamId: TeamId = TeamId(account.teams.head.team_id)

      val resFut = for {
        campaign: StartedCampaignDetails <- CampaignUtils.createAndStartDripCampaign(
          initialData = initialData,
          designDripForCase = 1
        )

        _: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForCallChannel(
          callChannelData = CallChannelData(initialData.callSettingUuid.get),
          teamId = teamId
        )

        scheduleTaskData1: ScheduleTasksData <- {
          ScheduleTaskFixture.scheduleTaskForEmailChannel(
            emailChannelData = EmailChannelData(
              emailSettingId = initialData.emailSetting.get.id.get.emailSettingId
            ),
            teamId = teamId
          )
        }

        _: Int <- Future.fromTry {
          SchedulerTestDAO.addLastScheduledAtForCampaign(
            campaignId = campaign.campaignId,
            teamId = teamId
          )
        }
        scheduleTaskData2: ScheduleTasksData <- {
          ScheduleTaskFixture.scheduleTaskForLinkedinChannel(
            linkedinChannelData = ChannelData.LinkedinChannelData(linkedinSettingId = initialData.linkedinAccountSettings.get.settings.uuid),
            teamId = teamId
          )
        }

        _: Int <- Future.fromTry {
          SchedulerTestDAO.addLastScheduledAtForCampaign(
            campaignId = campaign.campaignId,
            teamId = teamId
          )
        }


        linkedinProspects: List[Long] <- Future.fromTry {
          SchedulerTestDAO.getProspectIdForCampaignAndChannelType(
            campaignId = campaign.campaignId,
            channelType = ChannelType.LinkedinChannel
          )
        }

        emailProspects: List[Long] <- Future.fromTry {
          SchedulerTestDAO.getProspectIdForCampaignAndChannelType(
            campaignId = campaign.campaignId,
            channelType = ChannelType.EmailChannel
          )
        }

        callProspects: List[Long] <- Future.fromTry {
          SchedulerTestDAO.getProspectIdForCampaignAndChannelType(
            campaignId = campaign.campaignId,
            channelType = ChannelType.CallChannel
          )
        }
      } yield {
        (linkedinProspects, emailProspects, callProspects)
      }

      resFut.map(res => {
          println(res)
          assert(res._1.length == 1 && res._2.length == 1 && res._3.length == 0)
        })
        .recover {
          case e =>
            println(e.toString)
            println(LogHelpers.getStackTraceAsString(e))
            assert(false)
        }
    }


    it("FOLLOWUP TEST - should schedule linkedin / call or email depending on all conditions encountered") {
      val initialData: InitialData = SRSetupAndDeleteFixtures.createInitialData(true).get
      val account: Account = initialData.account
      val teamId: TeamId = TeamId(account.teams.head.team_id)

      val resFut = for {
        campaign: StartedCampaignDetails <- CampaignUtils.createAndStartDripCampaign(
          initialData = initialData,
          designDripForCase = 1,
          with_first_email_step_to_test_followup = true
        )
        scheduleTaskData0: ScheduleTasksData <- {
          ScheduleTaskFixture.scheduleTaskForEmailChannel(
            emailChannelData = EmailChannelData(
              emailSettingId = initialData.emailSetting.get.id.get.emailSettingId
            ),
            teamId = teamId
          )
        }
        db_updates <- Future.fromTry {
          CampaignUtils.updateCampaignFirstStep(
            campaignId = campaign.campaignId,
            emailSettingId = initialData.emailSetting.get.id.get,
            teamId = teamId
          )
        }

      } yield {
         for {

          scheduleTaskData2: ScheduleTasksData <- {
            ScheduleTaskFixture.scheduleTaskForLinkedinChannel(
              linkedinChannelData = ChannelData.LinkedinChannelData(linkedinSettingId = initialData.linkedinAccountSettings.get.settings.uuid),
              teamId = teamId
            )
          }

          _: Int <- Future.fromTry {
            SchedulerTestDAO.addLastScheduledAtForCampaign(
              campaignId = campaign.campaignId,
              teamId = teamId
            )
          }

          scheduleTaskData1: ScheduleTasksData <- {
            ScheduleTaskFixture.scheduleTaskForEmailChannel(
              emailChannelData = EmailChannelData(
                emailSettingId = initialData.emailSetting.get.id.get.emailSettingId
              ),
              teamId = teamId
            )
          }

          _: Int <- Future.fromTry {
            SchedulerTestDAO.addLastScheduledAtForCampaign(
              campaignId = campaign.campaignId,
              teamId = teamId
            )
          }

          _: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForCallChannel(
            callChannelData = CallChannelData(initialData.callSettingUuid.get),
            teamId = teamId
          )

          linkedinProspects: List[Long] <- Future.fromTry {
            SchedulerTestDAO.getProspectIdForCampaignAndChannelType(
              campaignId = campaign.campaignId,
              channelType = ChannelType.LinkedinChannel
            )
          }

          emailProspects: List[Long] <- Future.fromTry {
            SchedulerTestDAO.getProspectIdForCampaignAndChannelType(
              campaignId = campaign.campaignId,
              channelType = ChannelType.EmailChannel
            )
          }

          callProspects: List[Long] <- Future.fromTry {
            SchedulerTestDAO.getProspectIdForCampaignAndChannelType(
              campaignId = campaign.campaignId,
              channelType = ChannelType.CallChannel
            )
          }
        } yield {
          (linkedinProspects, emailProspects, callProspects)
        }
      }


      val resFlatten = resFut.flatten


      resFlatten.map(res => {
          println(res)
          assert(res._1.length == 1 && res._2.length == 1 && res._3.length == 0)
        })
        .recover {
          case e =>
            println(e.toString)
            println(LogHelpers.getStackTraceAsString(e))
            assert(false)
        }
    }

    /*
    Head step is a condition - If user has phone. schedule call otherwise email.
     */
    it("should schedule linkedin or email step depending on the head step condition V1- schedule linkedin first") {
      val initialData: InitialData = SRSetupAndDeleteFixtures.createInitialData(true).get

      val account: Account = initialData.account
      val teamId: TeamId = TeamId(account.teams.head.team_id)

      val campaignFut: Future[StartedCampaignDetails] = for {
        startedCampaignDetails: StartedCampaignDetails <- CampaignUtils.createAndStartDripCampaign(
          initialData = initialData,
          designDripForCase = 2
        )


      } yield {
        startedCampaignDetails
      }

      val resFut: Future[(List[Long], List[Long])] = campaignFut.flatMap(campaign => {
        println("Linkedin Scheduled")
        for {
          _: Int <- Future.fromTry {
            SchedulerTestDAO.addLastScheduledAtForCampaign(
              campaignId = campaign.campaignId,
              teamId = teamId
            )
          }
          scheduleTaskData2: ScheduleTasksData <- {
            println("Scheduling One")
            ScheduleTaskFixture.scheduleTaskForLinkedinChannel(
              linkedinChannelData = ChannelData.LinkedinChannelData(linkedinSettingId = initialData.linkedinAccountSettings.get.settings.uuid),
              teamId = teamId
            )
          }
          scheduleTaskData1: ScheduleTasksData <- {
            println("Scheduling Emails")
            ScheduleTaskFixture.scheduleTaskForEmailChannel(
              emailChannelData = EmailChannelData(
                emailSettingId = initialData.emailSetting.get.id.get.emailSettingId
              ),
              teamId = teamId
            )
          }

          scheduledProspectIdsForLinkedin: List[Long] <- Future.fromTry {
            println("Verifying prospects are scheduled or not for Linkedin")
            SchedulerTestDAO.getProspectIdForCampaignAndChannelType(
              campaignId = campaign.campaignId,
              channelType = ChannelType.LinkedinChannel
            )
          }

          scheduledProspectIdsForEmail: List[Long] <- Future.fromTry {
            println("Verifying prospects are scheduled or not for Email")
            SchedulerTestDAO.getProspectIdForCampaignAndChannelType(
              campaignId = campaign.campaignId,
              channelType = ChannelType.EmailChannel
            )
          }
        } yield {
          (scheduledProspectIdsForEmail, scheduledProspectIdsForLinkedin)
        }
      })

      resFut.map(res => {
        println(res)
        assert(res._1.length == 1 && res._2.length == 2)
      })
        .recover {
          case e =>
            println(e.toString)
            println(LogHelpers.getStackTraceAsString(e))
            assert(false)
        }
    }
    it("should schedule linkedin or email step depending on the head step condition V2- schedule email first") {
      val initialData: InitialData = SRSetupAndDeleteFixtures.createInitialData(true).get
      val account: Account = initialData.account
      val teamId: TeamId = TeamId(account.teams.head.team_id)

      val campaignFut: Future[StartedCampaignDetails] = for {
        startedCampaignDetails: StartedCampaignDetails <- CampaignUtils.createAndStartDripCampaign(
          initialData = initialData,
          designDripForCase = 2
        )


      } yield {
        startedCampaignDetails
      }

      val resFut: Future[(List[Long], List[Long])] = campaignFut.flatMap(campaign => {
        println("Linkedin Scheduled")
        for {
          _: Int <- Future.fromTry {
            SchedulerTestDAO.addLastScheduledAtForCampaign(
              campaignId = campaign.campaignId,
              teamId = teamId
            )
          }
          scheduleTaskData1: ScheduleTasksData <- {
            println("Scheduling Emails")
            ScheduleTaskFixture.scheduleTaskForEmailChannel(
              emailChannelData = EmailChannelData(
                emailSettingId = initialData.emailSetting.get.id.get.emailSettingId
              ),
              teamId = teamId
            )
          }
          scheduleTaskData2: ScheduleTasksData <- {
            println("Scheduling One")
            ScheduleTaskFixture.scheduleTaskForLinkedinChannel(
              linkedinChannelData = ChannelData.LinkedinChannelData(linkedinSettingId = initialData.linkedinAccountSettings.get.settings.uuid),
              teamId = teamId
            )
          }

          scheduledProspectIdsForLinkedin: List[Long] <- Future.fromTry {
            println("Verifying prospects are scheduled or not for Linkedin")
            SchedulerTestDAO.getProspectIdForCampaignAndChannelType(
              campaignId = campaign.campaignId,
              channelType = ChannelType.LinkedinChannel
            )
          }

          scheduledProspectIdsForEmail: List[Long] <- Future.fromTry {
            println("Verifying prospects are scheduled or not for Email")
            SchedulerTestDAO.getProspectIdForCampaignAndChannelType(
              campaignId = campaign.campaignId,
              channelType = ChannelType.EmailChannel
            )
          }
        } yield {
          (scheduledProspectIdsForEmail, scheduledProspectIdsForLinkedin)
        }
      })

      resFut.map(res => {
        println(res)
        assert(res._1.length == 1 && res._2.length == 2)
      })
        .recover {
          case e =>
            println(e.toString)
            println(LogHelpers.getStackTraceAsString(e))
            assert(false)
        }
    }

    it("FOLLOWUP TEST - should schedule linkedin or email step depending on the head step condition") {
      val initialData: InitialData = SRSetupAndDeleteFixtures.createInitialData(true).get
      val account: Account = initialData.account
      val teamId: TeamId = TeamId(account.teams.head.team_id)

      val campaignFut: Future[StartedCampaignDetails] = for {
        campaign: StartedCampaignDetails <- CampaignUtils.createAndStartDripCampaign(
          initialData = initialData,
          designDripForCase = 2,
          with_first_email_step_to_test_followup = true
        )
        scheduleTaskData0: ScheduleTasksData <- {
          ScheduleTaskFixture.scheduleTaskForEmailChannel(
            emailChannelData = EmailChannelData(
              emailSettingId = initialData.emailSetting.get.id.get.emailSettingId
            ),
            teamId = teamId
          )
        }
        db_updates <- Future.fromTry {
          CampaignUtils.updateCampaignFirstStep(
            campaignId = campaign.campaignId,
            emailSettingId = initialData.emailSetting.get.id.get,
            teamId = teamId
          )
        }

      } yield {
        campaign
      }

      val resFut: Future[(List[Long], List[Long])] = campaignFut.flatMap(campaign => {
        println("Linkedin Scheduled")
        for {
          _: Int <- Future.fromTry {
            SchedulerTestDAO.addLastScheduledAtForCampaign(
              campaignId = campaign.campaignId,
              teamId = teamId
            )
          }
          scheduleTaskData1: ScheduleTasksData <- {
            println("Scheduling Emails")
            ScheduleTaskFixture.scheduleTaskForEmailChannel(
              emailChannelData = EmailChannelData(
                emailSettingId = initialData.emailSetting.get.id.get.emailSettingId
              ),
              teamId = teamId
            )
          }
          scheduleTaskData2: ScheduleTasksData <- {
            println("Scheduling One")
            ScheduleTaskFixture.scheduleTaskForLinkedinChannel(
              linkedinChannelData = ChannelData.LinkedinChannelData(linkedinSettingId = initialData.linkedinAccountSettings.get.settings.uuid),
              teamId = teamId
            )
          }

          scheduledProspectIdsForLinkedin: List[Long] <- Future.fromTry {
            println("Verifying prospects are scheduled or not for Linkedin")
            SchedulerTestDAO.getProspectIdForCampaignAndChannelType(
              campaignId = campaign.campaignId,
              channelType = ChannelType.LinkedinChannel
            )
          }

          scheduledProspectIdsForEmail: List[Long] <- Future.fromTry {
            println("Verifying prospects are scheduled or not for Email")
            SchedulerTestDAO.getProspectIdForCampaignAndChannelType(
              campaignId = campaign.campaignId,
              channelType = ChannelType.EmailChannel
            )
          }
        } yield {
          (scheduledProspectIdsForEmail, scheduledProspectIdsForLinkedin)
        }
      })

      resFut.map(res => {
          println(res)
          assert(res._1.length == 1 && res._2.length == 2)
        })
        .recover {
          case e =>
            println(e.toString)
            println(LogHelpers.getStackTraceAsString(e))
            assert(false)
        }
    }

    it("should schedule head steps for all the prospects") {
      val initialData: InitialData = SRSetupAndDeleteFixtures.createInitialData(true).get
      val account: Account = initialData.account
      val teamId: TeamId = TeamId(account.teams.head.team_id)

      val scheduleTaskData: Future[ScheduleTasksData] = for {

        _: StartedCampaignDetails <- CampaignUtils.createAndStartDripCampaign(
          initialData = initialData,
          designDripForCase = 3
        )

        //Test result after scheduling
        result: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
          emailChannelData = EmailChannelData(
            emailSettingId = initialData.emailSetting.get.id.get.emailSettingId
          ),
          teamId = teamId
        )
      } yield {
        result
      }

      scheduleTaskData.map(p => {
        println(p)
        assert(p.saved_tasks_count == 3)
      }).recover({ case e =>
        println(LogHelpers.getStackTraceAsString(e))
        assert(false)
      })
    }

    it("should schedule linkedin step for two prospects who have linkedin_url") {
      val initialData: InitialData = SRSetupAndDeleteFixtures.createInitialData(true).get
      val account: Account = initialData.account
      val teamId: TeamId = TeamId(account.teams.head.team_id)

      val scheduledProspectIds: Future[List[Long]] = for {

        startedCampaignDetails: StartedCampaignDetails <- CampaignUtils.createAndStartDripCampaign(
          initialData = initialData,
          designDripForCase = 3
        )

        //Test result after scheduling
        result: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
          emailChannelData = EmailChannelData(
            emailSettingId = initialData.emailSetting.get.id.get.emailSettingId
          ),
          teamId = teamId
        )

        _: Seq[Long] <- Future.fromTry {
          campaignProspectDAO._updateScheduledStatus(
            scheduledCampaignProspectData = Seq(
              CampaignProspectUpdateScheduleStatus(
                current_step_status_for_scheduler_data = CurrentStepStatusForSchedulerData.Done(
                  done_at = DateTime.now().minusDays(2)
                ),
                current_step_type = CampaignStepType.ManualEmailStep,
                current_step_task_id = "",
                step_id = startedCampaignDetails.stepIds.head,
                campaign_id = startedCampaignDetails.campaignId.id,
                prospect_id = startedCampaignDetails.prospectIds.head.id,
                email_message_id = None,
                current_campaign_email_settings_id = None
              ),
              CampaignProspectUpdateScheduleStatus(
                current_step_status_for_scheduler_data = CurrentStepStatusForSchedulerData.Done(
                  done_at = DateTime.now().minusDays(2)
                ),
                current_step_type = CampaignStepType.ManualEmailStep,
                current_step_task_id = "",
                step_id = startedCampaignDetails.stepIds.head,
                campaign_id = startedCampaignDetails.campaignId.id,
                prospect_id = startedCampaignDetails.prospectIds(1).id,
                email_message_id = None,
                current_campaign_email_settings_id = None
              ),
              CampaignProspectUpdateScheduleStatus(
                current_step_status_for_scheduler_data = CurrentStepStatusForSchedulerData.Done(
                  done_at = DateTime.now().minusDays(2)
                ),
                current_step_type = CampaignStepType.ManualEmailStep,
                current_step_task_id = "",
                step_id = startedCampaignDetails.stepIds.head,
                campaign_id = startedCampaignDetails.campaignId.id,
                prospect_id = startedCampaignDetails.prospectIds(2).id,
                email_message_id = None,
                current_campaign_email_settings_id = None
              )
            )
          )
        }

        _: Int <- Future.fromTry {
          SchedulerTestDAO.setLastScheduledForCampaignProspects(
            campaignId = startedCampaignDetails.campaignId,
            prospectIds = startedCampaignDetails.prospectIds,
            teamId = teamId
          )
        }

        _: Int <- Future.fromTry {
          SchedulerTestDAO.updateLatestEmailScheduledAtForEmailSetting(
            emailSettingId = initialData.emailSetting.get.id.get,
            teamId = teamId
          )
        }

        _: Int <- Future.fromTry {
          SchedulerTestDAO.updateLastTouchedAtForProspectMetadata(
            prospectIds = startedCampaignDetails.prospectIds,
            teamId = teamId
          )
        }

        _: Int <- Future.fromTry {
          SchedulerTestDAO.addLastScheduledAtForCampaign(
            campaignId = startedCampaignDetails.campaignId,
            teamId = teamId
          )
        }

        scheduleTaskData1: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForLinkedinChannel(
          linkedinChannelData = ChannelData.LinkedinChannelData(linkedinSettingId = initialData.linkedinAccountSettings.get.settings.uuid),
          teamId = teamId
        )

        scheduledProspectIds: List[Long] <- Future.fromTry {
          SchedulerTestDAO.getProspectIdForCampaignAndChannelTypeFromTasks(
            campaignId = startedCampaignDetails.campaignId,
            channelType = ChannelType.LinkedinChannel
          )
        }
      } yield {
        scheduledProspectIds
      }

      scheduledProspectIds.map(p => {
        assert(p.length == 2)
      }).recover({ case e =>
        println(LogHelpers.getStackTraceAsString(e))
        assert(false)
      })
    }
    it("FOLLOWUP TEST - should schedule linkedin step for two prospects who have linkedin_url") {
      val initialData: InitialData = SRSetupAndDeleteFixtures.createInitialData(true).get
      val account: Account = initialData.account
      val teamId: TeamId = TeamId(account.teams.head.team_id)

      val scheduledProspectIdsFut = for {
        startedCampaignDetails: StartedCampaignDetails <- CampaignUtils.createAndStartDripCampaign(
          initialData = initialData,
          designDripForCase = 3,
          with_first_email_step_to_test_followup = true
        )
        scheduleTaskData0: ScheduleTasksData <- {
          ScheduleTaskFixture.scheduleTaskForEmailChannel(
            emailChannelData = EmailChannelData(
              emailSettingId = initialData.emailSetting.get.id.get.emailSettingId
            ),
            teamId = teamId
          )
        }
        db_updates <- Future.fromTry {
          CampaignUtils.updateCampaignFirstStep(
            campaignId = startedCampaignDetails.campaignId,
            emailSettingId = initialData.emailSetting.get.id.get,
            teamId = teamId
          )
        }

      } yield {
        for {

        //Test result after scheduling
        result: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
          emailChannelData = EmailChannelData(
            emailSettingId = initialData.emailSetting.get.id.get.emailSettingId
          ),
          teamId = teamId
        )

        _: Seq[Long] <- Future.fromTry {
          campaignProspectDAO._updateScheduledStatus(
            scheduledCampaignProspectData = Seq(
              CampaignProspectUpdateScheduleStatus(
                current_step_status_for_scheduler_data = CurrentStepStatusForSchedulerData.Done(
                  done_at = DateTime.now().minusDays(2)
                ),
                current_step_type = CampaignStepType.ManualEmailStep,
                current_step_task_id = "",
                step_id = startedCampaignDetails.stepIds.head,
                campaign_id = startedCampaignDetails.campaignId.id,
                prospect_id = startedCampaignDetails.prospectIds.head.id,
                email_message_id = None,
                current_campaign_email_settings_id = None
              ),
              CampaignProspectUpdateScheduleStatus(
                current_step_status_for_scheduler_data = CurrentStepStatusForSchedulerData.Done(
                  done_at = DateTime.now().minusDays(2)
                ),
                current_step_type = CampaignStepType.ManualEmailStep,
                current_step_task_id = "",
                step_id = startedCampaignDetails.stepIds.head,
                campaign_id = startedCampaignDetails.campaignId.id,
                prospect_id = startedCampaignDetails.prospectIds(1).id,
                email_message_id = None,
                current_campaign_email_settings_id = None
              ),
              CampaignProspectUpdateScheduleStatus(
                current_step_status_for_scheduler_data = CurrentStepStatusForSchedulerData.Done(
                  done_at = DateTime.now().minusDays(2)
                ),
                current_step_type = CampaignStepType.ManualEmailStep,
                current_step_task_id = "",
                step_id = startedCampaignDetails.stepIds.head,
                campaign_id = startedCampaignDetails.campaignId.id,
                prospect_id = startedCampaignDetails.prospectIds(2).id,
                email_message_id = None,
                current_campaign_email_settings_id = None
              )
            )
          )
        }

        _: Int <- Future.fromTry {
          SchedulerTestDAO.setLastScheduledForCampaignProspects(
            campaignId = startedCampaignDetails.campaignId,
            prospectIds = startedCampaignDetails.prospectIds,
            teamId = teamId
          )
        }

        _: Int <- Future.fromTry {
          SchedulerTestDAO.updateLatestEmailScheduledAtForEmailSetting(
            emailSettingId = initialData.emailSetting.get.id.get,
            teamId = teamId
          )
        }

        _: Int <- Future.fromTry {
          SchedulerTestDAO.updateLastTouchedAtForProspectMetadata(
            prospectIds = startedCampaignDetails.prospectIds,
            teamId = teamId
          )
        }

        _: Int <- Future.fromTry {
          SchedulerTestDAO.addLastScheduledAtForCampaign(
            campaignId = startedCampaignDetails.campaignId,
            teamId = teamId
          )
        }

        scheduleTaskData1: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForLinkedinChannel(
          linkedinChannelData = ChannelData.LinkedinChannelData(linkedinSettingId = initialData.linkedinAccountSettings.get.settings.uuid),
          teamId = teamId
        )

        scheduledProspectIds: List[Long] <- Future.fromTry {
          SchedulerTestDAO.getProspectIdForCampaignAndChannelTypeFromTasks(
            campaignId = startedCampaignDetails.campaignId,
            channelType = ChannelType.LinkedinChannel
          )
        }
      } yield {
        scheduledProspectIds
      }
      }

      val scheduledProspectIds = scheduledProspectIdsFut.flatten
      scheduledProspectIds.map(p => {
        assert(p.length == 2)
      }).recover({ case e =>
        println(LogHelpers.getStackTraceAsString(e))
        assert(false)
      })

    }


    /*
      CASE head_step_condition -> has_phone_number
        yes edge has condition -> has_email
        no edge has condition -> has_linkedin_url
        Below has_email, send_email step then send_phone
        Below has_linkedin_url, send_linkedin then send_email
        below has_linkedin_url no, check if we have email
        send email
        3 Prospects -> 1 has phone and email, 1 has email and linkedin & 1 has only email.
   */
    it("should schedule linkedin / call or email1 or email2 depending on all conditions encountered V1- schedule linkedin first") {
      val initialData: InitialData = SRSetupAndDeleteFixtures.createInitialData(true).get

      val account: Account = initialData.account
      val teamId: TeamId = TeamId(account.teams.head.team_id)

      val resFut = for {
        campaign: StartedCampaignDetails <- CampaignUtils.createAndStartDripCampaign(
          initialData = initialData,
          designDripForCase = 4
        )

        scheduleTaskData2: ScheduleTasksData <- {
          ScheduleTaskFixture.scheduleTaskForLinkedinChannel(
            linkedinChannelData = ChannelData.LinkedinChannelData(linkedinSettingId = initialData.linkedinAccountSettings.get.settings.uuid),
            teamId = teamId
          )
        }

        _: Int <- Future.fromTry {
          SchedulerTestDAO.addLastScheduledAtForCampaign(
            campaignId = campaign.campaignId,
            teamId = teamId
          )
        }

        scheduleTaskData1: ScheduleTasksData <- {
          ScheduleTaskFixture.scheduleTaskForEmailChannel(
            emailChannelData = EmailChannelData(
              emailSettingId = initialData.emailSetting.get.id.get.emailSettingId
            ),
            teamId = teamId
          )
        }

        _: Int <- Future.fromTry {
          SchedulerTestDAO.addLastScheduledAtForCampaign(
            campaignId = campaign.campaignId,
            teamId = teamId
          )
        }

        _: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForCallChannel(
          callChannelData = CallChannelData(initialData.callSettingUuid.get),
          teamId = teamId
        )

        linkedinProspects: List[Long] <- Future.fromTry {
          SchedulerTestDAO.getProspectIdForCampaignAndChannelType(
            campaignId = campaign.campaignId,
            channelType = ChannelType.LinkedinChannel
          )
        }

        emailProspects: List[Long] <- Future.fromTry {
          SchedulerTestDAO.getProspectIdForCampaignAndChannelType(
            campaignId = campaign.campaignId,
            channelType = ChannelType.EmailChannel
          )
        }

        callProspects: List[Long] <- Future.fromTry {
          SchedulerTestDAO.getProspectIdForCampaignAndChannelType(
            campaignId = campaign.campaignId,
            channelType = ChannelType.CallChannel
          )
        }
      } yield {
        (linkedinProspects, emailProspects, callProspects)
      }

      resFut.map(res => {
          println(res)
          assert(res._1.length == 1 && res._2.length == 2 && res._3.length == 0)
        })
        .recover {
          case e =>
            println(e.toString)
            println(LogHelpers.getStackTraceAsString(e))
            assert(false)
        }
    }
    it("should schedule linkedin / call or email1 or email2 depending on all conditions encountered V2- schedule email first") {
      val initialData: InitialData = SRSetupAndDeleteFixtures.createInitialData(true).get
      val account: Account = initialData.account
      val teamId: TeamId = TeamId(account.teams.head.team_id)

      val resFut = for {
        campaign: StartedCampaignDetails <- CampaignUtils.createAndStartDripCampaign(
          initialData = initialData,
          designDripForCase = 4
        )
        scheduleTaskData1: ScheduleTasksData <- {
          ScheduleTaskFixture.scheduleTaskForEmailChannel(
            emailChannelData = EmailChannelData(
              emailSettingId = initialData.emailSetting.get.id.get.emailSettingId
            ),
            teamId = teamId
          )
        }

        scheduleTaskData2: ScheduleTasksData <- {
          ScheduleTaskFixture.scheduleTaskForLinkedinChannel(
            linkedinChannelData = ChannelData.LinkedinChannelData(linkedinSettingId = initialData.linkedinAccountSettings.get.settings.uuid),
            teamId = teamId
          )
        }

        _: Int <- Future.fromTry {
          SchedulerTestDAO.addLastScheduledAtForCampaign(
            campaignId = campaign.campaignId,
            teamId = teamId
          )
        }

        _: Int <- Future.fromTry {
          SchedulerTestDAO.addLastScheduledAtForCampaign(
            campaignId = campaign.campaignId,
            teamId = teamId
          )
        }

        _: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForCallChannel(
          callChannelData = CallChannelData(initialData.callSettingUuid.get),
          teamId = teamId
        )

        linkedinProspects: List[Long] <- Future.fromTry {
          SchedulerTestDAO.getProspectIdForCampaignAndChannelType(
            campaignId = campaign.campaignId,
            channelType = ChannelType.LinkedinChannel
          )
        }

        emailProspects: List[Long] <- Future.fromTry {
          SchedulerTestDAO.getProspectIdForCampaignAndChannelType(
            campaignId = campaign.campaignId,
            channelType = ChannelType.EmailChannel
          )
        }

        callProspects: List[Long] <- Future.fromTry {
          SchedulerTestDAO.getProspectIdForCampaignAndChannelType(
            campaignId = campaign.campaignId,
            channelType = ChannelType.CallChannel
          )
        }
      } yield {
        (linkedinProspects, emailProspects, callProspects)
      }

      resFut.map(res => {
          println(res)
          assert(res._1.length == 1 && res._2.length == 2 && res._3.length == 0)
        })
        .recover {
          case e =>
            println(e.toString)
            println(LogHelpers.getStackTraceAsString(e))
            assert(false)
        }
    }

    it("should schedule linkedin / call or email1 or email2 depending on all conditions encountered V3- schedule call first") {
      val initialData: InitialData = SRSetupAndDeleteFixtures.createInitialData(true).get
      val account: Account = initialData.account
      val teamId: TeamId = TeamId(account.teams.head.team_id)

      val resFut = for {
        campaign: StartedCampaignDetails <- CampaignUtils.createAndStartDripCampaign(
          initialData = initialData,
          designDripForCase = 4
        )

        _: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForCallChannel(
          callChannelData = CallChannelData(initialData.callSettingUuid.get),
          teamId = teamId
        )
        scheduleTaskData2: ScheduleTasksData <- {
          ScheduleTaskFixture.scheduleTaskForLinkedinChannel(
            linkedinChannelData = ChannelData.LinkedinChannelData(linkedinSettingId = initialData.linkedinAccountSettings.get.settings.uuid),
            teamId = teamId
          )
        }

        _: Int <- Future.fromTry {
          SchedulerTestDAO.addLastScheduledAtForCampaign(
            campaignId = campaign.campaignId,
            teamId = teamId
          )
        }

        scheduleTaskData1: ScheduleTasksData <- {
          ScheduleTaskFixture.scheduleTaskForEmailChannel(
            emailChannelData = EmailChannelData(
              emailSettingId = initialData.emailSetting.get.id.get.emailSettingId
            ),
            teamId = teamId
          )
        }

        _: Int <- Future.fromTry {
          SchedulerTestDAO.addLastScheduledAtForCampaign(
            campaignId = campaign.campaignId,
            teamId = teamId
          )
        }


        linkedinProspects: List[Long] <- Future.fromTry {
          SchedulerTestDAO.getProspectIdForCampaignAndChannelType(
            campaignId = campaign.campaignId,
            channelType = ChannelType.LinkedinChannel
          )
        }

        emailProspects: List[Long] <- Future.fromTry {
          SchedulerTestDAO.getProspectIdForCampaignAndChannelType(
            campaignId = campaign.campaignId,
            channelType = ChannelType.EmailChannel
          )
        }

        callProspects: List[Long] <- Future.fromTry {
          SchedulerTestDAO.getProspectIdForCampaignAndChannelType(
            campaignId = campaign.campaignId,
            channelType = ChannelType.CallChannel
          )
        }
      } yield {
        (linkedinProspects, emailProspects, callProspects)
      }

      resFut.map(res => {
          println(res)
          assert(res._1.length == 1 && res._2.length == 2 && res._3.length == 0)
        })
        .recover {
          case e =>
            println(e.toString)
            println(LogHelpers.getStackTraceAsString(e))
            assert(false)
        }
    }


    it("FOLLOWUP TEST - should schedule linkedin / call or email1 or email2 depending on all conditions encountered") {
      val initialData: InitialData = SRSetupAndDeleteFixtures.createInitialData(true).get
      val account: Account = initialData.account
      val teamId: TeamId = TeamId(account.teams.head.team_id)

      val resFutFut = for {
        campaign: StartedCampaignDetails <- CampaignUtils.createAndStartDripCampaign(
          initialData = initialData,
          designDripForCase = 4,
          with_first_email_step_to_test_followup = true
        )
        scheduleTaskData0: ScheduleTasksData <- {
          ScheduleTaskFixture.scheduleTaskForEmailChannel(
            emailChannelData = EmailChannelData(
              emailSettingId = initialData.emailSetting.get.id.get.emailSettingId
            ),
            teamId = teamId
          )
        }
        db_updates <- Future.fromTry {
          CampaignUtils.updateCampaignFirstStep(
            campaignId = campaign.campaignId,
            emailSettingId = initialData.emailSetting.get.id.get,
            teamId = teamId
          )
        }

      } yield {
        for {

          _: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForCallChannel(
            callChannelData = CallChannelData(initialData.callSettingUuid.get),
            teamId = teamId
          )
          scheduleTaskData2: ScheduleTasksData <- {
            ScheduleTaskFixture.scheduleTaskForLinkedinChannel(
              linkedinChannelData = ChannelData.LinkedinChannelData(linkedinSettingId = initialData.linkedinAccountSettings.get.settings.uuid),
              teamId = teamId
            )
          }

          _: Int <- Future.fromTry {
            SchedulerTestDAO.addLastScheduledAtForCampaign(
              campaignId = campaign.campaignId,
              teamId = teamId
            )
          }

          scheduleTaskData1: ScheduleTasksData <- {
            ScheduleTaskFixture.scheduleTaskForEmailChannel(
              emailChannelData = EmailChannelData(
                emailSettingId = initialData.emailSetting.get.id.get.emailSettingId
              ),
              teamId = teamId
            )
          }

          _: Int <- Future.fromTry {
            SchedulerTestDAO.addLastScheduledAtForCampaign(
              campaignId = campaign.campaignId,
              teamId = teamId
            )
          }


          linkedinProspects: List[Long] <- Future.fromTry {
            SchedulerTestDAO.getProspectIdForCampaignAndChannelType(
              campaignId = campaign.campaignId,
              channelType = ChannelType.LinkedinChannel
            )
          }

          emailProspects: List[Long] <- Future.fromTry {
            SchedulerTestDAO.getProspectIdForCampaignAndChannelType(
              campaignId = campaign.campaignId,
              channelType = ChannelType.EmailChannel
            )
          }

          callProspects: List[Long] <- Future.fromTry {
            SchedulerTestDAO.getProspectIdForCampaignAndChannelType(
              campaignId = campaign.campaignId,
              channelType = ChannelType.CallChannel
            )
          }
        } yield {
          (linkedinProspects, emailProspects, callProspects)
        }
      }

      val resFut = resFutFut.flatten
      resFut.map(res => {
          println(res)
          assert(res._1.length == 1, "Linkedin")
          assert(res._2.length == 2, "Email")
          assert(res._3.length == 0, "Call")
        })
        .recover {
          case e =>
            println(e.toString)
            println(LogHelpers.getStackTraceAsString(e))
            assert(false)
        }
    }


    /*
      CASE head_step_condition -> has_email
       yes edge has step -> send_email

       send_email -> has_opened_email

       yes edge has condition -> has_phone

       yes edge has step -> call_prospect


       1 Prospects ->  has phone and email and also prospect has opened email
     */

    // This test is commented out as we are not supporting "has_read_email" functionality yet.

//    it("should first scheduled send_email and then mark prospect opened and scheduled the call step"){
//      val initialData: InitialData = SRSetupAndDeleteFixtures.createInitialData(true).get
//      val account: Account = initialData.account
//      val teamId: TeamId = TeamId(account.teams.head.team_id)
//
//
//      val resFut = for {
//        campaign: StartedCampaignDetails <- CampaignUtils.createAndStartDripCampaign(
//          initialData = initialData,
//          designDripForCase = 5
//        )
//
//        cheduleTaskData1: ScheduleTasksData <- {
//          ScheduleTaskFixture.scheduleTaskForEmailChannel(
//            emailChannelData = EmailChannelData(
//              emailSettingId = initialData.emailSetting.get.id.get.emailSettingId
//            ),
//            teamId = teamId
//          )
//        }
//        emailProspects: List[Long] <- Future.fromTry {
//          SchedulerTestDAO.getProspectIdForCampaignAndChannelType(
//            campaignId = campaign.campaignId,
//            channelType = ChannelType.EmailChannel
//          )
//        }
//
//      } yield {
//
//        emailProspects
//      }
//      resFut.map(resp =>{
//
//        assert(resp.length == 1)
//      })
//
//    }

    it("should first scheduled call step for all prospects irrespective of if they have phone number or not"){
      val initialData: InitialData = SRSetupAndDeleteFixtures.createInitialData(true).get
      val account: Account = initialData.account
      val teamId: TeamId = TeamId(account.teams.head.team_id)


      val resFut = for {
        campaign: StartedCampaignDetails <- CampaignUtils.createAndStartDripCampaign(
          initialData = initialData,
          designDripForCase = 6
        )

        scheduleTaskData1: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForCallChannel(
          callChannelData = CallChannelData(initialData.callSettingUuid.get),
          teamId = teamId
        )

        callProspects: List[Long] <- Future.fromTry {
          SchedulerTestDAO.getProspectIdForCampaignAndChannelType(
            campaignId = campaign.campaignId,
            channelType = ChannelType.CallChannel
          )
        }
      } yield {

        callProspects
      }
      resFut.map(resp =>{

        assert(resp.length == 3)
      })

    }


    it("should first scheduled call step for only prospects that has phone number"){
      val initialData: InitialData = SRSetupAndDeleteFixtures.createInitialData(true).get
      val account: Account = initialData.account
      val teamId: TeamId = TeamId(account.teams.head.team_id)


      val resFut = for {
        campaign: StartedCampaignDetails <- CampaignUtils.createAndStartDripCampaign(
          initialData = initialData,
          designDripForCase = 7
        )

        scheduleTaskData1: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForCallChannel(
          callChannelData = CallChannelData(initialData.callSettingUuid.get),
          teamId = teamId
        )

        callProspects: List[Long] <- Future.fromTry {
          SchedulerTestDAO.getProspectIdForCampaignAndChannelType(
            campaignId = campaign.campaignId,
            channelType = ChannelType.CallChannel
          )
        }
      } yield {

        callProspects
      }
      resFut.map(resp =>{

        assert(resp.length == 1)
      })

    }

    it("should first scheduled linkedin step for all prospects irrespective of if they have linkedin or not"){
      val initialData: InitialData = SRSetupAndDeleteFixtures.createInitialData(true).get
      val account: Account = initialData.account
      val teamId: TeamId = TeamId(account.teams.head.team_id)


      val resFut = for {
        campaign: StartedCampaignDetails <- CampaignUtils.createAndStartDripCampaign(
          initialData = initialData,
          designDripForCase = 8
        )

        scheduleTaskData2: ScheduleTasksData <- {
          ScheduleTaskFixture.scheduleTaskForLinkedinChannel(
            linkedinChannelData = ChannelData.LinkedinChannelData(linkedinSettingId = initialData.linkedinAccountSettings.get.settings.uuid),
            teamId = teamId
          )
        }

        linkedinProspects: List[Long] <- Future.fromTry {
          SchedulerTestDAO.getProspectIdForCampaignAndChannelType(
            campaignId = campaign.campaignId,
            channelType = ChannelType.LinkedinChannel
          )
        }
      } yield {

        linkedinProspects
      }
      resFut.map(resp =>{

        assert(resp.length == 3)
      })

    }


    it("should first scheduled linkedin step for only prospects that has linkedin"){
      val initialData: InitialData = SRSetupAndDeleteFixtures.createInitialData(true).get
      val account: Account = initialData.account
      val teamId: TeamId = TeamId(account.teams.head.team_id)


      val resFut = for {
        campaign: StartedCampaignDetails <- CampaignUtils.createAndStartDripCampaign(
          initialData = initialData,
          designDripForCase = 9
        )

        scheduleTaskData2: ScheduleTasksData <- {
          ScheduleTaskFixture.scheduleTaskForLinkedinChannel(
            linkedinChannelData = ChannelData.LinkedinChannelData(linkedinSettingId = initialData.linkedinAccountSettings.get.settings.uuid),
            teamId = teamId
          )
        }

        linkedinProspects: List[Long] <- Future.fromTry {
          SchedulerTestDAO.getProspectIdForCampaignAndChannelType(
            campaignId = campaign.campaignId,
            channelType = ChannelType.LinkedinChannel
          )
        }
      } yield {

        linkedinProspects
      }
      resFut.map(resp =>{

        assert(resp.length == 1)
      }).recover(e => {
        println(s"${LogHelpers.getStackTraceAsString(e)}")
        assert(false)
      })

    }

    it("should schedule email for valid email and phone for invalid email"){
      val initialData: InitialData = SRSetupAndDeleteFixtures.createInitialData(true).get
      val account: Account = initialData.account
      val teamId: TeamId = TeamId(account.teams.head.team_id)


      val resFut = for {
        campaign: StartedCampaignDetails <- CampaignUtils.createAndStartDripCampaign(
          initialData = initialData,
          designDripForCase = 10
        )
        make_prospect_invalid <- Future.fromTry {
          SchedulerTestDAO.markAsValidForProspectEmail(
            ids = campaign.prospectIds,
            teamId = teamId
          )
        }
        make_prospect_invalid <- Future.fromTry {
          prospectsEmailsDAO.markAsInvalidV2(
            id = campaign.prospectIds.head.id,
            team_id = teamId.id
          )
        }


        scheduleTaskData0: ScheduleTasksData <- {
          ScheduleTaskFixture.scheduleTaskForEmailChannel(
            emailChannelData = EmailChannelData(
              emailSettingId = initialData.emailSetting.get.id.get.emailSettingId
            ),
            teamId = teamId
          )
        }
        _: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForCallChannel(
          callChannelData = CallChannelData(initialData.callSettingUuid.get),
          teamId = teamId
        )
        emailProspects: List[Long] <- Future.fromTry {
          SchedulerTestDAO.getProspectIdForCampaignAndChannelType(
            campaignId = campaign.campaignId,
            channelType = ChannelType.EmailChannel
          )
        }

        callProspects: List[Long] <- Future.fromTry {
          SchedulerTestDAO.getProspectIdForCampaignAndChannelType(
            campaignId = campaign.campaignId,
            channelType = ChannelType.CallChannel
          )
        }
      } yield {

        (emailProspects, callProspects)
      }
      resFut.map(resp =>{

        assert(resp._1.length == 2)
        assert(resp._2.length == 1)
      }).recover(e => {
        println(s"${LogHelpers.getStackTraceAsString(e)}")
        assert(false)
      })

    }

    it("should schedule call for invalid email and email for valid email"){
      val initialData: InitialData = SRSetupAndDeleteFixtures.createInitialData(true).get
      val account: Account = initialData.account
      val teamId: TeamId = TeamId(account.teams.head.team_id)


      val resFut = for {
        campaign: StartedCampaignDetails <- CampaignUtils.createAndStartDripCampaign(
          initialData = initialData,
          designDripForCase = 10
        )
        make_prospect_invalid <- Future.fromTry {
          SchedulerTestDAO.markAsValidForProspectEmail(
            ids = campaign.prospectIds,
            teamId = teamId
          )
        }
        make_prospect_invalid <- Future.fromTry {
          prospectsEmailsDAO.markAsInvalidV2(
            id = campaign.prospectIds.head.id,
            team_id = teamId.id
          )
        }


        scheduleTaskData0: ScheduleTasksData <- {
          ScheduleTaskFixture.scheduleTaskForEmailChannel(
            emailChannelData = EmailChannelData(
              emailSettingId = initialData.emailSetting.get.id.get.emailSettingId
            ),
            teamId = teamId
          )
        }
        _: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForCallChannel(
          callChannelData = CallChannelData(initialData.callSettingUuid.get),
          teamId = teamId
        )
        emailProspects: List[Long] <- Future.fromTry {
          SchedulerTestDAO.getProspectIdForCampaignAndChannelType(
            campaignId = campaign.campaignId,
            channelType = ChannelType.EmailChannel
          )
        }

        callProspects: List[Long] <- Future.fromTry {
          SchedulerTestDAO.getProspectIdForCampaignAndChannelType(
            campaignId = campaign.campaignId,
            channelType = ChannelType.CallChannel
          )
        }
      } yield {

        (emailProspects, callProspects)
      }
      resFut.map(resp =>{

        assert(resp._1.length == 2)
        assert(resp._2.length == 1)
      }).recover(e => {
        println(s"${LogHelpers.getStackTraceAsString(e)}")
        assert(false)
      })

    }

    it("should schedule LinkedIn steps for connected profiles and call steps for non-connected profiles") {
      val initialData: InitialData = SRSetupAndDeleteFixtures.createInitialData(true).get
      val account: Account = initialData.account
      val teamId: TeamId = TeamId(account.teams.head.team_id)

      // Create dummy LinkedIn extraction data for one of the prospects
      
      val resFut = for {
        campaign: StartedCampaignDetails <- CampaignUtils.createAndStartDripCampaign(
          initialData = initialData,
          designDripForCase = 23 // Our new scenario number
        )
        
        // Schedule LinkedIn first
        scheduleTaskData1: ScheduleTasksData <- {
          ScheduleTaskFixture.scheduleTaskForLinkedinChannel(
            linkedinChannelData = ChannelData.LinkedinChannelData(
              linkedinSettingId = initialData.linkedinAccountSettings.get.settings.uuid
            ),
            teamId = teamId
          )
        }

        // Update the last scheduled timestamp so we can schedule call tasks
        _: Int <- Future.fromTry {
          SchedulerTestDAO.addLastScheduledAtForCampaign(
            campaignId = campaign.campaignId,
            teamId = teamId
          )
        }

        // Schedule call tasks
        scheduleTaskData2: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForCallChannel(
          callChannelData = CallChannelData(initialData.callSettingUuid.get),
          teamId = teamId
        )

        // Get the prospects scheduled for LinkedIn
        linkedinProspects: List[Long] <- Future.fromTry {
          SchedulerTestDAO.getProspectIdForCampaignAndChannelType(
            campaignId = campaign.campaignId,
            channelType = ChannelType.LinkedinChannel
          )
        }

        // Get the prospects scheduled for calls
        callProspects: List[Long] <- Future.fromTry {
          SchedulerTestDAO.getProspectIdForCampaignAndChannelType(
            campaignId = campaign.campaignId,
            channelType = ChannelType.CallChannel
          )
        }
      } yield {
        (linkedinProspects, callProspects)
      }

      resFut.map(resp => {
        val linkedin_step_prospects = resp._1
        val call_step_prospects = resp._2

        assert(linkedin_step_prospects.length == 1)
        assert(call_step_prospects.length == 1)

        
      }).recover(e => {
        println(s"${LogHelpers.getStackTraceAsString(e)}")
        assert(false)
      })
    }

    it("FOLLOWUP TEST - should schedule linkedin step if bounced else email step") {
      val initialData: InitialData = SRSetupAndDeleteFixtures.createInitialData(true).get
      val account: Account = initialData.account
      val teamId: TeamId = TeamId(account.teams.head.team_id)

      val scheduledProspectIdsFut = for {
        startedCampaignDetails: StartedCampaignDetails <- CampaignUtils.createAndStartDripCampaign(
          initialData = initialData,
          designDripForCase = 12,
        )
        scheduleTaskData0: ScheduleTasksData <- {
          ScheduleTaskFixture.scheduleTaskForEmailChannel(
            emailChannelData = EmailChannelData(
              emailSettingId = initialData.emailSetting.get.id.get.emailSettingId
            ),
            teamId = teamId
          )
        }
        db_updates <- Future.fromTry {
          CampaignUtils.updateCampaignFirstStep(
            campaignId = startedCampaignDetails.campaignId,
            emailSettingId = initialData.emailSetting.get.id.get,
            teamId = teamId
          )
        }
        mark_one_as_bounced <- Future.fromTry {
          SchedulerTestDAO.markAsBounced(
            campaignId = startedCampaignDetails.campaignId,
            prospectId = startedCampaignDetails.prospectIds.head
          )
        }

      } yield {
        for {

          //Test result after scheduling
          result: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
            emailChannelData = EmailChannelData(
              emailSettingId = initialData.emailSetting.get.id.get.emailSettingId
            ),
            teamId = teamId
          )


          _: Int <- Future.fromTry {
            SchedulerTestDAO.setLastScheduledForCampaignProspects(
              campaignId = startedCampaignDetails.campaignId,
              prospectIds = startedCampaignDetails.prospectIds,
              teamId = teamId
            )
          }

          _: Int <- Future.fromTry {
            SchedulerTestDAO.updateLatestEmailScheduledAtForEmailSetting(
              emailSettingId = initialData.emailSetting.get.id.get,
              teamId = teamId
            )
          }

          _: Int <- Future.fromTry {
            SchedulerTestDAO.updateLastTouchedAtForProspectMetadata(
              prospectIds = startedCampaignDetails.prospectIds,
              teamId = teamId
            )
          }

          _: Int <- Future.fromTry {
            SchedulerTestDAO.addLastScheduledAtForCampaign(
              campaignId = startedCampaignDetails.campaignId,
              teamId = teamId
            )
          }

          scheduleTaskData1: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForLinkedinChannel(
            linkedinChannelData = ChannelData.LinkedinChannelData(linkedinSettingId = initialData.linkedinAccountSettings.get.settings.uuid),
            teamId = teamId
          )

          linkedInScheduled: List[Long] <- Future.fromTry {
            SchedulerTestDAO.getProspectIdForCampaignAndChannelTypeFromTasks(
              campaignId = startedCampaignDetails.campaignId,
              channelType = ChannelType.LinkedinChannel
            )
          }
          emailScheduled: List[Long] <- Future.fromTry {
            SchedulerTestDAO.getProspectIdForCampaignAndChannelTypeFromTasks(
              campaignId = startedCampaignDetails.campaignId,
              channelType = ChannelType.EmailChannel
            )
          }
        } yield {
          (linkedInScheduled, emailScheduled)
        }
      }

      val scheduledProspectIds = scheduledProspectIdsFut.flatten
      scheduledProspectIds.map(p => {
        assert(p._2.length == 2)
        assert(p._1.length == 1)
      }).recover({ case e =>
        println(LogHelpers.getStackTraceAsString(e))
        assert(false)
      })

    }

    it("FOLLOWUP TEST - should schedule linkedin step if Opend else email step") {
      val initialData: InitialData = SRSetupAndDeleteFixtures.createInitialData(true).get
      val account: Account = initialData.account
      val teamId: TeamId = TeamId(account.teams.head.team_id)

      val scheduledProspectIdsFut = for {
        startedCampaignDetails: StartedCampaignDetails <- CampaignUtils.createAndStartDripCampaign(
          initialData = initialData,
          designDripForCase = 13,
        )
        scheduleTaskData0: ScheduleTasksData <- {
          ScheduleTaskFixture.scheduleTaskForEmailChannel(
            emailChannelData = EmailChannelData(
              emailSettingId = initialData.emailSetting.get.id.get.emailSettingId
            ),
            teamId = teamId
          )
        }
        db_updates <- Future.fromTry {
          CampaignUtils.updateCampaignFirstStep(
            campaignId = startedCampaignDetails.campaignId,
            emailSettingId = initialData.emailSetting.get.id.get,
            teamId = teamId,
            sent_ago_in_hours = 12
          )
        }
        mark_one_as_bounced <- Future.fromTry {
          SchedulerTestDAO.markAsOpen(
            campaignId = startedCampaignDetails.campaignId,
            prospectId = startedCampaignDetails.prospectIds.head
          )
        }

      } yield {
        for {

          //Test result after scheduling
          result: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
            emailChannelData = EmailChannelData(
              emailSettingId = initialData.emailSetting.get.id.get.emailSettingId
            ),
            teamId = teamId
          )


          _: Int <- Future.fromTry {
            SchedulerTestDAO.setLastScheduledForCampaignProspects(
              campaignId = startedCampaignDetails.campaignId,
              prospectIds = startedCampaignDetails.prospectIds,
              teamId = teamId
            )
          }

          _: Int <- Future.fromTry {
            SchedulerTestDAO.updateLatestEmailScheduledAtForEmailSetting(
              emailSettingId = initialData.emailSetting.get.id.get,
              teamId = teamId
            )
          }

          _: Int <- Future.fromTry {
            SchedulerTestDAO.updateLastTouchedAtForProspectMetadata(
              prospectIds = startedCampaignDetails.prospectIds,
              teamId = teamId
            )
          }

          _: Int <- Future.fromTry {
            SchedulerTestDAO.addLastScheduledAtForCampaign(
              campaignId = startedCampaignDetails.campaignId,
              teamId = teamId
            )
          }

          scheduleTaskData1: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForLinkedinChannel(
            linkedinChannelData = ChannelData.LinkedinChannelData(linkedinSettingId = initialData.linkedinAccountSettings.get.settings.uuid),
            teamId = teamId
          )

          linkedInScheduled: List[Long] <- Future.fromTry {
            SchedulerTestDAO.getProspectIdForCampaignAndChannelTypeFromTasks(
              campaignId = startedCampaignDetails.campaignId,
              channelType = ChannelType.LinkedinChannel
            )
          }
          emailScheduled: List[Long] <- Future.fromTry {
            SchedulerTestDAO.getProspectIdForCampaignAndChannelTypeFromTasks(
              campaignId = startedCampaignDetails.campaignId,
              channelType = ChannelType.EmailChannel
            )
          }
        } yield {
          (linkedInScheduled, emailScheduled)
        }
      }

      val scheduledProspectIds = scheduledProspectIdsFut.flatten
      scheduledProspectIds.map(p => {
        assert(p._2.length == 2)
        assert(p._1.length == 1)
      }).recover({ case e =>
        println(LogHelpers.getStackTraceAsString(e))
        assert(false)
      })

    }

    it("FOLLOWUP TEST - should schedule email steps even if opened if the delay is met") {
      val initialData: InitialData = SRSetupAndDeleteFixtures.createInitialData(true).get
      val account: Account = initialData.account
      val teamId: TeamId = TeamId(account.teams.head.team_id)

      val scheduledProspectIdsFut = for {
        startedCampaignDetails: StartedCampaignDetails <- CampaignUtils.createAndStartDripCampaign(
          initialData = initialData,
          designDripForCase = 13,
        )
        scheduleTaskData0: ScheduleTasksData <- {
          ScheduleTaskFixture.scheduleTaskForEmailChannel(
            emailChannelData = EmailChannelData(
              emailSettingId = initialData.emailSetting.get.id.get.emailSettingId
            ),
            teamId = teamId
          )
        }
        db_updates <- Future.fromTry {
          CampaignUtils.updateCampaignFirstStep(
            campaignId = startedCampaignDetails.campaignId,
            emailSettingId = initialData.emailSetting.get.id.get,
            teamId = teamId,
          )
        }
        mark_one_as_bounced <- Future.fromTry {
          SchedulerTestDAO.markAsOpen(
            campaignId = startedCampaignDetails.campaignId,
            prospectId = startedCampaignDetails.prospectIds.head
          )
        }

      } yield {
        for {

          //Test result after scheduling
          result: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
            emailChannelData = EmailChannelData(
              emailSettingId = initialData.emailSetting.get.id.get.emailSettingId
            ),
            teamId = teamId
          )


          _: Int <- Future.fromTry {
            SchedulerTestDAO.setLastScheduledForCampaignProspects(
              campaignId = startedCampaignDetails.campaignId,
              prospectIds = startedCampaignDetails.prospectIds,
              teamId = teamId
            )
          }

          _: Int <- Future.fromTry {
            SchedulerTestDAO.updateLatestEmailScheduledAtForEmailSetting(
              emailSettingId = initialData.emailSetting.get.id.get,
              teamId = teamId
            )
          }

          _: Int <- Future.fromTry {
            SchedulerTestDAO.updateLastTouchedAtForProspectMetadata(
              prospectIds = startedCampaignDetails.prospectIds,
              teamId = teamId
            )
          }

          _: Int <- Future.fromTry {
            SchedulerTestDAO.addLastScheduledAtForCampaign(
              campaignId = startedCampaignDetails.campaignId,
              teamId = teamId
            )
          }

          scheduleTaskData1: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForLinkedinChannel(
            linkedinChannelData = ChannelData.LinkedinChannelData(linkedinSettingId = initialData.linkedinAccountSettings.get.settings.uuid),
            teamId = teamId
          )

          linkedInScheduled: List[Long] <- Future.fromTry {
            SchedulerTestDAO.getProspectIdForCampaignAndChannelTypeFromTasks(
              campaignId = startedCampaignDetails.campaignId,
              channelType = ChannelType.LinkedinChannel
            )
          }
          emailScheduled: List[Long] <- Future.fromTry {
            SchedulerTestDAO.getProspectIdForCampaignAndChannelTypeFromTasks(
              campaignId = startedCampaignDetails.campaignId,
              channelType = ChannelType.EmailChannel
            )
          }
        } yield {
          (linkedInScheduled, emailScheduled)
        }
      }

      val scheduledProspectIds = scheduledProspectIdsFut.flatten
      scheduledProspectIds.map(p => {
        assert(p._2.length == 3)
        assert(p._1.isEmpty)
      }).recover({ case e =>
        println(LogHelpers.getStackTraceAsString(e))
        assert(false)
      })

    }

    it("FOLLOWUP TEST - should schedule email only when email is opened") {
      val initialData: InitialData = SRSetupAndDeleteFixtures.createInitialData(true).get
      val account: Account = initialData.account
      val teamId: TeamId = TeamId(account.teams.head.team_id)

      val scheduledProspectIdsFut = for {
        startedCampaignDetails: StartedCampaignDetails <- CampaignUtils.createAndStartDripCampaign(
          initialData = initialData,
          designDripForCase = 16,
        )
        scheduleTaskData0: ScheduleTasksData <- {
          ScheduleTaskFixture.scheduleTaskForEmailChannel(
            emailChannelData = EmailChannelData(
              emailSettingId = initialData.emailSetting.get.id.get.emailSettingId
            ),
            teamId = teamId
          )
        }
        db_updates <- Future.fromTry {
          CampaignUtils.updateCampaignFirstStep(
            campaignId = startedCampaignDetails.campaignId,
            emailSettingId = initialData.emailSetting.get.id.get,
            teamId = teamId,
          )
        }
        mark_one_as_bounced <- Future.fromTry {
          SchedulerTestDAO.markAsOpen(
            campaignId = startedCampaignDetails.campaignId,
            prospectId = startedCampaignDetails.prospectIds.head
          )
        }

      } yield {
        for {

          //Test result after scheduling
          result: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
            emailChannelData = EmailChannelData(
              emailSettingId = initialData.emailSetting.get.id.get.emailSettingId
            ),
            teamId = teamId
          )


          _: Int <- Future.fromTry {
            SchedulerTestDAO.setLastScheduledForCampaignProspects(
              campaignId = startedCampaignDetails.campaignId,
              prospectIds = startedCampaignDetails.prospectIds,
              teamId = teamId
            )
          }

          _: Int <- Future.fromTry {
            SchedulerTestDAO.updateLatestEmailScheduledAtForEmailSetting(
              emailSettingId = initialData.emailSetting.get.id.get,
              teamId = teamId
            )
          }

          _: Int <- Future.fromTry {
            SchedulerTestDAO.updateLastTouchedAtForProspectMetadata(
              prospectIds = startedCampaignDetails.prospectIds,
              teamId = teamId
            )
          }

          _: Int <- Future.fromTry {
            SchedulerTestDAO.addLastScheduledAtForCampaign(
              campaignId = startedCampaignDetails.campaignId,
              teamId = teamId
            )
          }

          scheduleTaskData1: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForLinkedinChannel(
            linkedinChannelData = ChannelData.LinkedinChannelData(linkedinSettingId = initialData.linkedinAccountSettings.get.settings.uuid),
            teamId = teamId
          )

          linkedInScheduled: List[Long] <- Future.fromTry {
            SchedulerTestDAO.getProspectIdForCampaignAndChannelTypeFromTasks(
              campaignId = startedCampaignDetails.campaignId,
              channelType = ChannelType.LinkedinChannel
            )
          }
          emailScheduled: List[Long] <- Future.fromTry {
            SchedulerTestDAO.getProspectIdForCampaignAndChannelTypeFromTasks(
              campaignId = startedCampaignDetails.campaignId,
              channelType = ChannelType.EmailChannel
            )
          }
        } yield {
          (linkedInScheduled, emailScheduled)
        }
      }

      val scheduledProspectIds = scheduledProspectIdsFut.flatten
      scheduledProspectIds.map(p => {
        assert(p._2.length == 1)
        assert(p._1.isEmpty)
      }).recover({ case e =>
        println(LogHelpers.getStackTraceAsString(e))
        assert(false)
      })

    }


    it("FOLLOWUP TEST - should schedule email only when email is opened even when delay is not met") {
      val initialData: InitialData = SRSetupAndDeleteFixtures.createInitialData(true).get
      val account: Account = initialData.account
      val teamId: TeamId = TeamId(account.teams.head.team_id)

      val scheduledProspectIdsFut = for {
        startedCampaignDetails: StartedCampaignDetails <- CampaignUtils.createAndStartDripCampaign(
          initialData = initialData,
          designDripForCase = 16,
        )
        scheduleTaskData0: ScheduleTasksData <- {
          ScheduleTaskFixture.scheduleTaskForEmailChannel(
            emailChannelData = EmailChannelData(
              emailSettingId = initialData.emailSetting.get.id.get.emailSettingId
            ),
            teamId = teamId
          )
        }
        db_updates <- Future.fromTry {
          CampaignUtils.updateCampaignFirstStep(
            campaignId = startedCampaignDetails.campaignId,
            emailSettingId = initialData.emailSetting.get.id.get,
            teamId = teamId,
            sent_ago_in_hours = 12
          )
        }
        mark_one_as_bounced <- Future.fromTry {
          SchedulerTestDAO.markAsOpen(
            campaignId = startedCampaignDetails.campaignId,
            prospectId = startedCampaignDetails.prospectIds.head
          )
        }

      } yield {
        for {

          //Test result after scheduling
          result: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
            emailChannelData = EmailChannelData(
              emailSettingId = initialData.emailSetting.get.id.get.emailSettingId
            ),
            teamId = teamId
          )


          _: Int <- Future.fromTry {
            SchedulerTestDAO.setLastScheduledForCampaignProspects(
              campaignId = startedCampaignDetails.campaignId,
              prospectIds = startedCampaignDetails.prospectIds,
              teamId = teamId
            )
          }

          _: Int <- Future.fromTry {
            SchedulerTestDAO.updateLatestEmailScheduledAtForEmailSetting(
              emailSettingId = initialData.emailSetting.get.id.get,
              teamId = teamId
            )
          }

          _: Int <- Future.fromTry {
            SchedulerTestDAO.updateLastTouchedAtForProspectMetadata(
              prospectIds = startedCampaignDetails.prospectIds,
              teamId = teamId
            )
          }

          _: Int <- Future.fromTry {
            SchedulerTestDAO.addLastScheduledAtForCampaign(
              campaignId = startedCampaignDetails.campaignId,
              teamId = teamId
            )
          }

          scheduleTaskData1: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForLinkedinChannel(
            linkedinChannelData = ChannelData.LinkedinChannelData(linkedinSettingId = initialData.linkedinAccountSettings.get.settings.uuid),
            teamId = teamId
          )

          linkedInScheduled: List[Long] <- Future.fromTry {
            SchedulerTestDAO.getProspectIdForCampaignAndChannelTypeFromTasks(
              campaignId = startedCampaignDetails.campaignId,
              channelType = ChannelType.LinkedinChannel
            )
          }
          emailScheduled: List[Long] <- Future.fromTry {
            SchedulerTestDAO.getProspectIdForCampaignAndChannelTypeFromTasks(
              campaignId = startedCampaignDetails.campaignId,
              channelType = ChannelType.EmailChannel
            )
          }
        } yield {
          (linkedInScheduled, emailScheduled)
        }
      }

      val scheduledProspectIds = scheduledProspectIdsFut.flatten
      scheduledProspectIds.map(p => {
        assert(p._2.length == 1)
        assert(p._1.isEmpty)
      }).recover({ case e =>
        println(LogHelpers.getStackTraceAsString(e))
        assert(false)
      })

    }

    it("FOLLOWUP TEST - should schedule linkedin step if HasReplied else email step") {
      val initialData: InitialData = SRSetupAndDeleteFixtures.createInitialData(true).get
      val account: Account = initialData.account
      val teamId: TeamId = TeamId(account.teams.head.team_id)

      val scheduledProspectIdsFut = for {
        startedCampaignDetails: StartedCampaignDetails <- CampaignUtils.createAndStartDripCampaign(
          initialData = initialData,
          designDripForCase = 14,
        )
        scheduleTaskData0: ScheduleTasksData <- {
          ScheduleTaskFixture.scheduleTaskForEmailChannel(
            emailChannelData = EmailChannelData(
              emailSettingId = initialData.emailSetting.get.id.get.emailSettingId
            ),
            teamId = teamId
          )
        }
        db_updates <- Future.fromTry {
          CampaignUtils.updateCampaignFirstStep(
            campaignId = startedCampaignDetails.campaignId,
            emailSettingId = initialData.emailSetting.get.id.get,
            teamId = teamId,
            sent_ago_in_hours = 12
          )
        }
        mark_one_as_replied <- Future.fromTry {
          SchedulerTestDAO.markAsReplied(
            campaignId = startedCampaignDetails.campaignId,
            prospectId = startedCampaignDetails.prospectIds.head
          )
        }

      } yield {
        for {

          //Test result after scheduling
          result: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
            emailChannelData = EmailChannelData(
              emailSettingId = initialData.emailSetting.get.id.get.emailSettingId
            ),
            teamId = teamId
          )


          _: Int <- Future.fromTry {
            SchedulerTestDAO.setLastScheduledForCampaignProspects(
              campaignId = startedCampaignDetails.campaignId,
              prospectIds = startedCampaignDetails.prospectIds,
              teamId = teamId
            )
          }

          _: Int <- Future.fromTry {
            SchedulerTestDAO.updateLatestEmailScheduledAtForEmailSetting(
              emailSettingId = initialData.emailSetting.get.id.get,
              teamId = teamId
            )
          }

          _: Int <- Future.fromTry {
            SchedulerTestDAO.updateLastTouchedAtForProspectMetadata(
              prospectIds = startedCampaignDetails.prospectIds,
              teamId = teamId
            )
          }

          _: Int <- Future.fromTry {
            SchedulerTestDAO.addLastScheduledAtForCampaign(
              campaignId = startedCampaignDetails.campaignId,
              teamId = teamId
            )
          }

          scheduleTaskData1: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForLinkedinChannel(
            linkedinChannelData = ChannelData.LinkedinChannelData(linkedinSettingId = initialData.linkedinAccountSettings.get.settings.uuid),
            teamId = teamId
          )

          linkedInScheduled: List[Long] <- Future.fromTry {
            SchedulerTestDAO.getProspectIdForCampaignAndChannelTypeFromTasks(
              campaignId = startedCampaignDetails.campaignId,
              channelType = ChannelType.LinkedinChannel
            )
          }
          emailScheduled: List[Long] <- Future.fromTry {
            SchedulerTestDAO.getProspectIdForCampaignAndChannelTypeFromTasks(
              campaignId = startedCampaignDetails.campaignId,
              channelType = ChannelType.EmailChannel
            )
          }
        } yield {
          (linkedInScheduled, emailScheduled)
        }
      }

      val scheduledProspectIds = scheduledProspectIdsFut.flatten
      scheduledProspectIds.map(p => {
        assert(p._2.length == 2)
        assert(p._1.length == 1)
      }).recover({ case e =>
        println(LogHelpers.getStackTraceAsString(e))
        assert(false)
      })

    }



    it("FOLLOWUP TEST - should schedule linkedin step if Has replid with do not contact else email step") {

      val initialData: InitialData = SRSetupAndDeleteFixtures.createInitialData(true).get
      val account: Account = initialData.account
      val teamId: TeamId = TeamId(account.teams.head.team_id)

      val scheduledProspectIdsFut = for {
        startedCampaignDetails: StartedCampaignDetails <- CampaignUtils.createAndStartDripCampaign(
          initialData = initialData,
          designDripForCase = 15,
        )
        scheduleTaskData0: ScheduleTasksData <- {
          ScheduleTaskFixture.scheduleTaskForEmailChannel(
            emailChannelData = EmailChannelData(
              emailSettingId = initialData.emailSetting.get.id.get.emailSettingId
            ),
            teamId = teamId
          )
        }
        db_updates <- Future.fromTry {
          CampaignUtils.updateCampaignFirstStep(
            campaignId = startedCampaignDetails.campaignId,
            emailSettingId = initialData.emailSetting.get.id.get,
            teamId = teamId
          )
        }
        mark_one_as_replied <- Future.fromTry {
          SchedulerTestDAO.markAsReplied(
            campaignId = startedCampaignDetails.campaignId,
            prospectId = startedCampaignDetails.prospectIds.head
          )
        }


        mark_one_as_reply_type_do_not_contact <- Future.fromTry {
          SchedulerTestDAO.updateReplyTypeForEmailScheduled(
            campaignId = startedCampaignDetails.campaignId,
            prospectId = startedCampaignDetails.prospectIds.head,
            reply_type = EmailReplyType.DO_NOT_CONTACT
          )
        }

      } yield {
        for {

          //Test result after scheduling
          result: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
            emailChannelData = EmailChannelData(
              emailSettingId = initialData.emailSetting.get.id.get.emailSettingId
            ),
            teamId = teamId
          )


          _: Int <- Future.fromTry {
            SchedulerTestDAO.setLastScheduledForCampaignProspects(
              campaignId = startedCampaignDetails.campaignId,
              prospectIds = startedCampaignDetails.prospectIds,
              teamId = teamId
            )
          }

          _: Int <- Future.fromTry {
            SchedulerTestDAO.updateLatestEmailScheduledAtForEmailSetting(
              emailSettingId = initialData.emailSetting.get.id.get,
              teamId = teamId
            )
          }

          _: Int <- Future.fromTry {
            SchedulerTestDAO.updateLastTouchedAtForProspectMetadata(
              prospectIds = startedCampaignDetails.prospectIds,
              teamId = teamId
            )
          }

          _: Int <- Future.fromTry {
            SchedulerTestDAO.addLastScheduledAtForCampaign(
              campaignId = startedCampaignDetails.campaignId,
              teamId = teamId
            )
          }

          scheduleTaskData1: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForLinkedinChannel(
            linkedinChannelData = ChannelData.LinkedinChannelData(linkedinSettingId = initialData.linkedinAccountSettings.get.settings.uuid),
            teamId = teamId
          )

          linkedInScheduled: List[Long] <- Future.fromTry {
            SchedulerTestDAO.getProspectIdForCampaignAndChannelTypeFromTasks(
              campaignId = startedCampaignDetails.campaignId,
              channelType = ChannelType.LinkedinChannel
            )
          }
          emailScheduled: List[Long] <- Future.fromTry {
            SchedulerTestDAO.getProspectIdForCampaignAndChannelTypeFromTasks(
              campaignId = startedCampaignDetails.campaignId,
              channelType = ChannelType.EmailChannel
            )
          }
        } yield {
          (linkedInScheduled, emailScheduled)
        }
      }

      val scheduledProspectIds = scheduledProspectIdsFut.flatten
      scheduledProspectIds.map(p => {
        assert(p._2.length == 2)
        assert(p._1.length == 1)
      }).recover({ case e =>
        println(LogHelpers.getStackTraceAsString(e))
        assert(false)
      })

    }



    it("FOLLOWUP TEST - should move all the prospects to another campaign step if Has opened is first step and no side contains move_to_another_step") {

      val initialData: InitialData = SRSetupAndDeleteFixtures.createInitialData(true).get
      val account: Account = initialData.account
      val teamId: TeamId = TeamId(account.teams.head.team_id)
      val start_time = System.currentTimeMillis()
      println(s"FOLLOWUP TEST - should move all the prospects to another campaign step if Has opened is first step teamId: ${teamId} :: accountId: ${account.id} :: initialData: ${initialData}, start_time: ${start_time} ")

      val scheduledProspectIdsFut = for {

        startedCampaignDetails_1: StartedCampaignDetails <- CampaignUtils.createAndStartDripCampaign(
          initialData = initialData,
          designDripForCase = 16,
        )

        startedCampaignDetails: StartedCampaignDetails <- {

         val a =  CampaignUtils.createAndStartDripCampaign(
          initialData = initialData,
          designDripForCase = 17,
          move_to_another_campaign = Some(startedCampaignDetails_1.campaignId)
        )


          println(s"StartedCampaignDetails -> ${a}")

          a


        }

        scheduleTaskData0: ScheduleTasksData <- {

          val a = ScheduleTaskFixture.scheduleTaskForEmailChannel(
            emailChannelData = EmailChannelData(
              emailSettingId = initialData.emailSetting.get.id.get.emailSettingId
            ),
            teamId = teamId
          ).map(data =>{
            println(s"scheduleTaskData0 : ${data}")

            data

          })


          a
        }

        db_updates <- Future.fromTry {
          CampaignUtils.updateCampaignFirstStep(
            campaignId = startedCampaignDetails.campaignId,
            // We are not marking the step as done since we are not scheduling any earlier step for this campaign.
            // Previously we were not updating the current_step_id and directly marking it as done so that was making this test fail
            // due to modification in newProspectCheck sql. hence not updating it to done currently.getTodayTasks
            mark_scheduler_status_done = false,
            emailSettingId = initialData.emailSetting.get.id.get,
            teamId = teamId
          )
        }

        mark_one_as_opened <- Future.fromTry {
          SchedulerTestDAO.markAsOpen(
            campaignId = startedCampaignDetails.campaignId,
            prospectId = startedCampaignDetails.prospectIds.head
          )
        }


      } yield {
        for {

          //Test result after scheduling
          result: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
            emailChannelData = EmailChannelData(
              emailSettingId = initialData.emailSetting.get.id.get.emailSettingId
            ),
            teamId = teamId
          )

          _: Int <- Future.fromTry {
            SchedulerTestDAO.setLastScheduledForCampaignProspects(
              campaignId = startedCampaignDetails.campaignId,
              prospectIds = startedCampaignDetails.prospectIds,
              teamId = teamId
            )
          }

          _: Int <- Future.fromTry {
            SchedulerTestDAO.updateLatestEmailScheduledAtForEmailSetting(
              emailSettingId = initialData.emailSetting.get.id.get,
              teamId = teamId
            )
          }

          _: Int <- Future.fromTry {
            SchedulerTestDAO.updateLastTouchedAtForProspectMetadata(
              prospectIds = startedCampaignDetails.prospectIds,
              teamId = teamId
            )
          }

          _: Int <- Future.fromTry {
            SchedulerTestDAO.addLastScheduledAtForCampaign(
              campaignId = startedCampaignDetails.campaignId,
              teamId = teamId
            )
          }

          scheduleTaskData1: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForLinkedinChannel(
            linkedinChannelData = ChannelData.LinkedinChannelData(linkedinSettingId = initialData.linkedinAccountSettings.get.settings.uuid),
            teamId = teamId
          )

          linkedInScheduled: List[Long] <- Future.fromTry {
            SchedulerTestDAO.getProspectIdForCampaignAndChannelTypeFromTasks(
              campaignId = startedCampaignDetails.campaignId,
              channelType = ChannelType.LinkedinChannel
            )
          }
          emailScheduled: List[Long] <- Future.fromTry {
            SchedulerTestDAO.getProspectIdForCampaignAndChannelTypeFromTasks(
              campaignId = startedCampaignDetails.campaignId,
              channelType = ChannelType.EmailChannel
            )
          }

          schedulerIndependent_steps <- Future.fromTry{independentStepSchedulerService.preExecutionStepsAndChecks(
            campaign_id = startedCampaignDetails.campaignId,
            org_id = OrgId(id = initialData.account.org.id),
            team_id = teamId
          )}

          fetchProspectCounts <- Future.fromTry {
            SchedulerTestDAO.getProspectsCount(
              campaignId = startedCampaignDetails_1.campaignId,
              team_id = teamId
            )
          }
          fetchAllProspectsInNewCampaign <- Future.fromTry {
            SchedulerTestDAO.getProspectIdsFromCampaign(
              campaignId = startedCampaignDetails_1.campaignId,
              team_id = teamId
            )
          }

        } yield {
          (fetchAllProspectsInNewCampaign, startedCampaignDetails.prospectIds)
        }
      }


      val scheduledProspectIds = scheduledProspectIdsFut.flatten
      scheduledProspectIds.map(p => {
        // add more specific assertions here about particular prospects that moved.
        assert(p._1.length == 6)
        val prospects_in_new_campaign: List[Long] = p._1
        val prospects_that_should_move: Seq[Long] = p._2.map(_.id)

        println(s"prospects_that_should_move : ${prospects_that_should_move} ")
        val elapsed_time = System.currentTimeMillis() - start_time
        println(s"FOLLOWUP TEST - should move all the prospects to another campaign step if Has opened is first step teamId: ${teamId} :: accountId: ${account.id} :: initialData: ${initialData}, elapsed_time: ${elapsed_time} ")
        assert(prospects_that_should_move.forall(prospects_in_new_campaign.contains))

      }).recover({ case e =>
        println(LogHelpers.getStackTraceAsString(e))
        assert(false)
      })

    }



    it("FOLLOWUP TEST - should move prospect to another campaign step if is_opened is false ") {

      val initialData: InitialData = SRSetupAndDeleteFixtures.createInitialData(true).get
      val account: Account = initialData.account
      val teamId: TeamId = TeamId(account.teams.head.team_id)

      println(s" teamId: ${teamId} :: accountId: ${account.id} :: initialData: ${initialData}")

      val scheduledProspectIdsFut = for {

        startedCampaignDetails_1: StartedCampaignDetails <- CampaignUtils.createAndStartDripCampaign(
          initialData = initialData,
          designDripForCase = 16,
        )

        startedCampaignDetails: StartedCampaignDetails <- {

          val a = CampaignUtils.createAndStartDripCampaign(
            initialData = initialData,
            designDripForCase = 18,
            move_to_another_campaign = Some(startedCampaignDetails_1.campaignId)
          )


          println(s"StartedCampaignDetails -> ${a}")

          a


        }

        scheduleTaskData0: ScheduleTasksData <- {

          // 1. Scheduled the first step.

          val a = ScheduleTaskFixture.scheduleTaskForEmailChannel(
            emailChannelData = EmailChannelData(
              emailSettingId = initialData.emailSetting.get.id.get.emailSettingId
            ),
            teamId = teamId
          ).map(data => {
            println(s"scheduleTaskData0 : ${data}")

            data

          })


          a
        }

        // 2.  set the time back by 20 hours

        db_updates <- Future.fromTry {
          CampaignUtils.updateCampaignFirstStep(
            campaignId = startedCampaignDetails.campaignId,
            emailSettingId = initialData.emailSetting.get.id.get,
            teamId = teamId,
            sent_ago_in_hours = 20
          )
        }

        // 2.1 marking one prospect who has opened the email, so that it will go to yes path. and will not move to
        //     another campaign.
        mark_one_as_opened <- Future.fromTry {
          SchedulerTestDAO.markAsOpen(
            campaignId = startedCampaignDetails.campaignId,
            prospectId = startedCampaignDetails.prospectIds.head
          )
        }


      } yield {
        for {

          // 3. running the scheduler again for email, Nothing should happen.
          //Test result after scheduling
          result: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
            emailChannelData = EmailChannelData(
              emailSettingId = initialData.emailSetting.get.id.get.emailSettingId
            ),
            teamId = teamId
          )

          // 3.1 Updating the last_scheduled and db for prospects so that next scheduler should run and pickup

          _: Int <- Future.fromTry {
            SchedulerTestDAO.setLastScheduledForCampaignProspects(
              campaignId = startedCampaignDetails.campaignId,
              prospectIds = startedCampaignDetails.prospectIds,
              teamId = teamId
            )
          }

          // 3.2 Updating the db for prospects so that next scheduler should run and pickup
          _: Int <- Future.fromTry {
            SchedulerTestDAO.updateLatestEmailScheduledAtForEmailSetting(
              emailSettingId = initialData.emailSetting.get.id.get,
              teamId = teamId
            )
          }

          // 3.3 Updating the db for prospects so that next scheduler should run and pickup

          _: Int <- Future.fromTry {
            SchedulerTestDAO.updateLastTouchedAtForProspectMetadata(
              prospectIds = startedCampaignDetails.prospectIds,
              teamId = teamId
            )
          }

          // 3.4 Updating the db for prospects so that next scheduler should run and pickup

          _: Int <- Future.fromTry {
            SchedulerTestDAO.addLastScheduledAtForCampaign(
              campaignId = startedCampaignDetails.campaignId,
              teamId = teamId
            )
          }

          // Running the linkedin scheduler so that, yes path prospect will be scheduled
          scheduleTaskData1: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForLinkedinChannel(
            linkedinChannelData = ChannelData.LinkedinChannelData(linkedinSettingId = initialData.linkedinAccountSettings.get.settings.uuid),
            teamId = teamId
          )


          // Checking how many linkedin tasks got scheduled
          linkedInScheduled: List[Long] <- Future.fromTry {
            SchedulerTestDAO.getProspectIdForCampaignAndChannelTypeFromTasks(
              campaignId = startedCampaignDetails.campaignId,
              channelType = ChannelType.LinkedinChannel
            )
          }

          // Checking how many email tasks got scheduled
          emailScheduled: List[Long] <- Future.fromTry {
            SchedulerTestDAO.getProspectIdForCampaignAndChannelTypeFromTasks(
              campaignId = startedCampaignDetails.campaignId,
              channelType = ChannelType.EmailChannel
            )
          }

          // 3.5 : Ran the linkedin scheduler above so updating the db again for independent step scheduler.
          //       updating the time to 2 days ago.
          db_updates <- Future.fromTry {
            CampaignUtils.updateCampaignFirstStep(
              campaignId = startedCampaignDetails.campaignId,
              emailSettingId = initialData.emailSetting.get.id.get,
              teamId = teamId,
              sent_ago_in_hours = 48
            )
          }

          // 3.6 : Db updates
          _: Int <- Future.fromTry {
            SchedulerTestDAO.setLastScheduledForCampaignProspects(
              campaignId = startedCampaignDetails.campaignId,
              prospectIds = startedCampaignDetails.prospectIds,
              teamId = teamId
            )
          }

          // 3.7 : Db updates
          _: Int <- Future.fromTry {
            SchedulerTestDAO.updateLatestEmailScheduledAtForEmailSetting(
              emailSettingId = initialData.emailSetting.get.id.get,
              teamId = teamId
            )
          }

          // 3.7 : Db updates
          _: Int <- Future.fromTry {
            SchedulerTestDAO.updateLastTouchedAtForProspectMetadata(
              prospectIds = startedCampaignDetails.prospectIds,
              teamId = teamId
            )
          }

          // 3.8 : Db updates
          _: Int <- Future.fromTry {
            SchedulerTestDAO.addLastScheduledAtForCampaign(
              campaignId = startedCampaignDetails.campaignId,
              teamId = teamId
            )
          }

          // 4 : running the independent scheduler.
          schedulerIndependent_steps <- Future.fromTry {
            independentStepSchedulerService.preExecutionStepsAndChecks(
              campaign_id = startedCampaignDetails.campaignId,
              org_id = OrgId(id = initialData.account.org.id),
              team_id = teamId
            )
          }

          fetchProspectCounts <- Future.fromTry {
            SchedulerTestDAO.getProspectsCount(
              campaignId = startedCampaignDetails_1.campaignId,
              team_id = teamId
            )
          }

          fetchAllProspectsInNewCampaign <- Future.fromTry {
            SchedulerTestDAO.getProspectIdsFromCampaign(
              campaignId = startedCampaignDetails_1.campaignId,
              team_id = teamId
            )
          }

        } yield {
          (fetchAllProspectsInNewCampaign, startedCampaignDetails.prospectIds)
        }
      }


      val scheduledProspectIds = scheduledProspectIdsFut.flatten
      scheduledProspectIds.map(p => {
        // add more specific assertions here about particular prospects that moved.
        assert(p._1.length == 5)
        val prospects_in_new_campaign: List[Long] = p._1
        val prospect_that_should_not_move: Long = p._2.head.id
        val prospects_that_should_move: Seq[Long] = p._2.filter(data => data.id != prospect_that_should_not_move).map(_.id)

        println(s"prospects_in_new_campaign : ${prospects_in_new_campaign} and prospect_that_should_not_move : ${prospect_that_should_not_move}")
        println(s"prospects_that_should_move : ${prospects_that_should_move} ")

        assert(!prospects_in_new_campaign.contains(prospect_that_should_not_move))
        val b = prospects_that_should_move.forall(prospects_in_new_campaign.contains)
        println(s"prospects_that_should_move.forall(prospects_in_new_campaign.contains  -> ${b}")
        assert(prospects_that_should_move.forall(prospects_in_new_campaign.contains))

      }).recover({ case e =>
        println(LogHelpers.getStackTraceAsString(e))
        assert(false)
      })


    }

    it("FOLLOWUP TEST - should move prospect to another campaign step if is_opened is true, else should create linkedin tasks ") {

      val initialData: InitialData = SRSetupAndDeleteFixtures.createInitialData(true).get
      val account: Account = initialData.account
      val teamId: TeamId = TeamId(account.teams.head.team_id)

      println(s" teamId: ${teamId} :: accountId: ${account.id} :: initialData: ${initialData}")

      val scheduledProspectIdsFut = for {

        startedCampaignDetails_1: StartedCampaignDetails <- CampaignUtils.createAndStartDripCampaign(
          initialData = initialData,
          designDripForCase = 16,
        )

        startedCampaignDetails: StartedCampaignDetails <- {

          val a = CampaignUtils.createAndStartDripCampaign(
            initialData = initialData,
            designDripForCase = 19,
            move_to_another_campaign = Some(startedCampaignDetails_1.campaignId)
          )


          println(s"StartedCampaignDetails -> ${a}")

          a


        }

        // 1. scheduling email step

        scheduleTaskData0: ScheduleTasksData <- {

          val a = ScheduleTaskFixture.scheduleTaskForEmailChannel(
            emailChannelData = EmailChannelData(
              emailSettingId = initialData.emailSetting.get.id.get.emailSettingId
            ),
            teamId = teamId
          ).map(data => {
            println(s"scheduleTaskData0 : ${data}")
            data

          })


          a
        }


        // 2. updating db, marking sent 26 hours ago so delay will be passed.

        db_updates <- Future.fromTry {
          CampaignUtils.updateCampaignFirstStep(
            campaignId = startedCampaignDetails.campaignId,
            emailSettingId = initialData.emailSetting.get.id.get,
            teamId = teamId,
            sent_ago_in_hours = 26,
            prospects = startedCampaignDetails.prospectIds.takeWhile(p => p.id != startedCampaignDetails.prospectIds.head.id).map(_.id)
          )
        }

        // 2.1 updating db, marking opened true for one prospect id which will be moved to another campaign.
        mark_one_as_opened <- Future.fromTry {
          SchedulerTestDAO.markAsOpen(
            campaignId = startedCampaignDetails.campaignId,
            prospectId = startedCampaignDetails.prospectIds.head
          )
        }


      } yield {
        for {


          // 3.1 running scheduler should not do anything.
          result: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
            emailChannelData = EmailChannelData(
              emailSettingId = initialData.emailSetting.get.id.get.emailSettingId
            ),
            teamId = teamId
          )

          // 3.2. Db updates as scheduler ran, so that they(prospects) will be picked next run.
          _: Int <- Future.fromTry {
            SchedulerTestDAO.setLastScheduledForCampaignProspects(
              campaignId = startedCampaignDetails.campaignId,
              prospectIds = startedCampaignDetails.prospectIds,
              teamId = teamId
            )
          }

          // 3.3. Db updates as scheduler ran, so that they(prospects) will be picked next run.
          _: Int <- Future.fromTry {
            SchedulerTestDAO.updateLatestEmailScheduledAtForEmailSetting(
              emailSettingId = initialData.emailSetting.get.id.get,
              teamId = teamId
            )
          }

          // 3.4. Db updates as scheduler ran, so that they(prospects) will be picked next run.
          _: Int <- Future.fromTry {
            SchedulerTestDAO.updateLastTouchedAtForProspectMetadata(
              prospectIds = startedCampaignDetails.prospectIds,
              teamId = teamId
            )
          }
          // 3.4. Db updates as scheduler ran, so that they(prospects) will be picked next run.
          _: Int <- Future.fromTry {
            SchedulerTestDAO.addLastScheduledAtForCampaign(
              campaignId = startedCampaignDetails.campaignId,
              teamId = teamId
            )
          }

          // 3.5 running linkeidn scheduler, expecting 2 prospects to be scheduled by this.
          scheduleTaskData1: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForLinkedinChannel(
            linkedinChannelData = ChannelData.LinkedinChannelData(linkedinSettingId = initialData.linkedinAccountSettings.get.settings.uuid),
            teamId = teamId
          )

          // 3.6 checking count of linkedin tasks, currently not used.
          linkedInScheduled: List[Long] <- Future.fromTry {
            SchedulerTestDAO.getProspectIdForCampaignAndChannelTypeFromTasks(
              campaignId = startedCampaignDetails.campaignId,
              channelType = ChannelType.LinkedinChannel
            )
          }

          // 3.6 checking count of email tasks, currently not used.
          emailScheduled: List[Long] <- Future.fromTry {
            SchedulerTestDAO.getProspectIdForCampaignAndChannelTypeFromTasks(
              campaignId = startedCampaignDetails.campaignId,
              channelType = ChannelType.EmailChannel
            )
          }

          // 3.6  Doing db update and marking sent at to 20 hours ago.
          //      We are marking it 20 hours ago because within window is set to 24 hours and if that window passed, all
          //      the prospects will move to no path. and we want to test the yes path.
          db_updates <- Future.fromTry {
            CampaignUtils.updateCampaignFirstStep(
              campaignId = startedCampaignDetails.campaignId,
              emailSettingId = initialData.emailSetting.get.id.get,
              teamId = teamId,
              sent_ago_in_hours = 20
            )
          }

          // 3.7 Db Updates for scheduler
          _: Int <- Future.fromTry {
            SchedulerTestDAO.setLastScheduledForCampaignProspects(
              campaignId = startedCampaignDetails.campaignId,
              prospectIds = startedCampaignDetails.prospectIds,
              teamId = teamId
            )
          }

          // 3.8 Db Updates for scheduler
          _: Int <- Future.fromTry {
            SchedulerTestDAO.updateLatestEmailScheduledAtForEmailSetting(
              emailSettingId = initialData.emailSetting.get.id.get,
              teamId = teamId
            )
          }

          // 3.9 Db Updates for scheduler
          _: Int <- Future.fromTry {
            SchedulerTestDAO.updateLastTouchedAtForProspectMetadata(
              prospectIds = startedCampaignDetails.prospectIds,
              teamId = teamId
            )
          }

          // 3.9.1 Db Updates for scheduler
          _: Int <- Future.fromTry {
            SchedulerTestDAO.addLastScheduledAtForCampaign(
              campaignId = startedCampaignDetails.campaignId,
              teamId = teamId
            )
          }

          // 4. running the independent scheduler.
          schedulerIndependent_steps <- Future.fromTry {
            independentStepSchedulerService.preExecutionStepsAndChecks(
              campaign_id = startedCampaignDetails.campaignId,
              org_id = OrgId(id = initialData.account.org.id),
              team_id = teamId
            )
          }

          //5.  For verification of how many prospects moved to other campaign.
          fetchProspectCounts <- Future.fromTry {
            SchedulerTestDAO.getProspectsCount(
              campaignId = startedCampaignDetails_1.campaignId,
              team_id = teamId
            )
          }

          fetchAllProspectsInNewCampaign <- Future.fromTry {
            SchedulerTestDAO.getProspectIdsFromCampaign(
              campaignId = startedCampaignDetails_1.campaignId,
              team_id = teamId
            )
          }

        } yield {
          (fetchAllProspectsInNewCampaign, startedCampaignDetails.prospectIds)
        }
      }


      val scheduledProspectIds = scheduledProspectIdsFut.flatten
      scheduledProspectIds.map(p => {
        // add more specific assertions here about particular prospects that moved.
        assert(p._1.length == 4)
        val prospects_in_new_campaign: List[Long] = p._1
        val prospect_that_is_marked_open: Long = p._2.head.id
        val prospects_that_should_not_move: Seq[Long] = p._2.filter(data => data.id != prospect_that_is_marked_open).map(_.id)

        println(s"prospects_in_new_campaign : ${prospects_in_new_campaign} and prospect_that_is_marked_open : ${prospect_that_is_marked_open}")
        println(s"prospects_that_should_not_move : ${prospects_that_should_not_move} ")

        assert(prospects_in_new_campaign.contains(prospect_that_is_marked_open))
        assert(!prospects_that_should_not_move.forall(prospects_in_new_campaign.contains))

      }).recover({ case e =>
        println(LogHelpers.getStackTraceAsString(e))
        assert(false)
      })

    }

    /*
    Head step is a condition - If user has phone. schedule call otherwise email.
     */
    it("should schedule linkedin or email step depending on the head step condition V1- schedule linkedin first - should read Drip campaign from FE") {
      val initialData: InitialData = SRSetupAndDeleteFixtures.createInitialData(true).get

      val account: Account = initialData.account
      val teamId: TeamId = TeamId(account.teams.head.team_id)

      val campaignFut: Future[StartedCampaignDetails] = for {
        startedCampaignDetails: StartedCampaignDetails <- CampaignUtils.createAndStartDripCampaign(
          initialData = initialData,
          designDripForCase = 6
        )


      } yield {
        startedCampaignDetails
      }

      val resFut: Future[(List[Long], List[Long])] = campaignFut.flatMap(campaign => {
        println(s"debugging Linkedin Scheduled: ${campaign}")
        for {
          _: Int <- Future.fromTry {
            SchedulerTestDAO.addLastScheduledAtForCampaign(
              campaignId = campaign.campaignId,
              teamId = teamId
            )
          }
          scheduleTaskData2: ScheduleTasksData <- {
            println("Scheduling One")
            ScheduleTaskFixture.scheduleTaskForLinkedinChannel(
              linkedinChannelData = ChannelData.LinkedinChannelData(linkedinSettingId = initialData.linkedinAccountSettings.get.settings.uuid),
              teamId = teamId
            )
          }

          printing = println(s"debugging scheduleTaskData2: ${scheduleTaskData2}")

          scheduleTaskData1: ScheduleTasksData <- {
            println("Scheduling Emails")
            ScheduleTaskFixture.scheduleTaskForEmailChannel(
              emailChannelData = EmailChannelData(
                emailSettingId = initialData.emailSetting.get.id.get.emailSettingId
              ),
              teamId = teamId
            )
          }

          printing2 = println(s"debugging scheduleTaskData1: ${scheduleTaskData1}")

          scheduledProspectIdsForLinkedin: List[Long] <- Future.fromTry {
            println("Verifying prospects are scheduled or not for Linkedin")
            SchedulerTestDAO.getProspectIdForCampaignAndChannelType(
              campaignId = campaign.campaignId,
              channelType = ChannelType.LinkedinChannel
            )
          }

          printing3 = println(s"debugging scheduledProspectIdsForLinkedin: ${scheduledProspectIdsForLinkedin}")

          scheduledProspectIdsForEmail: List[Long] <- Future.fromTry {
            println("Verifying prospects are scheduled or not for Email")
            SchedulerTestDAO.getProspectIdForCampaignAndChannelType(
              campaignId = campaign.campaignId,
              channelType = ChannelType.EmailChannel
            )
          }
          printing4 = println(s"debugging scheduledProspectIdsForEmail: ${scheduledProspectIdsForEmail}")
        } yield {
          (scheduledProspectIdsForEmail, scheduledProspectIdsForLinkedin)
        }
      })

      Await.result(
        resFut,
        Duration.create(500, SECONDS)
      )

      resFut.flatMap(res => {
          println(res)
          assert(res._1.length == 1 && res._2.length == 2)
        })
        .recover {
          case e =>
//            println("debugging", e.toString)
//            println(LogHelpers.getStackTraceAsString(e))
            assert(false)
        }

      println(s"debugging resFut: $resFut")
      resFut.onComplete {

        case Success((v1, v2)) =>
//          println(s"debugging v1: ${v1} v2: ${v2}")
          assert(true)
        case Failure(er) =>
//          println(s"debugging err: ${er}")
          assert(false)

      }
      assert(resFut.isCompleted)

    }

  }


    describe("test getDuplicateCampaign") {

      it("test updating campaign") {
        val nodes = List(
          Json.obj("id" -> 1, "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "1", "type" -> "step")),
          Json.obj("id" -> "has_replied", "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "has_replied", "type" -> "condition")),
          Json.obj("id" -> 11, "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "11", "type" -> "step")),
          Json.obj("id" -> 111, "position" -> Json.obj("x" -> 100, "y" -> 100), "data" -> Json.obj("label" -> "111", "type" -> "step")),
        )
        val edges = List(
          Json.obj("id" -> "xyz", "source" -> 1, "target" -> "has_replied", "label" -> "no_condition"),
          Json.obj("id" -> "xyz-11", "source" -> "has_replied", "target" -> 11, "label" -> "yes"),
          Json.obj("id" -> "xyz-111", "source" -> "has_replied", "target" -> 111, "label" -> "no")
        )
        val head_node_id = "1"

        val res = NextStepFinderForDrip.getDuplicateCampaign(
          old_head_node_id = head_node_id,
          old_edges = edges,
          old_nodes = nodes,
          ordered_step_ids_for_old_campaign = List(1, 11, 111),
          ordered_step_ids_for_new_campaign = List(2, 22, 222)
        )

        // val result = Await.result(res, 5000.millis)

        res.map { result =>

          assert(result.head_node_id == "2")

          assert(
            result.edges == List(
              Json.obj("id" -> "xyz", "source" -> 2, "target" -> "has_replied", "label" -> "no_condition"),
              Json.obj("id" -> "xyz-22", "source" -> "has_replied", "target" -> 22, "label" -> "yes"),
              Json.obj("id" -> "xyz-222", "source" -> "has_replied", "target" -> 222, "label" -> "no")
            )
          )
          assert(
            result.nodes == List(
              Json.obj("id" -> 2, "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "2", "type" -> "step")),
              Json.obj("id" -> "has_replied", "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "has_replied", "type" -> "condition")),
              Json.obj("id" -> 22, "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "22", "type" -> "step")),
              Json.obj("id" -> 222, "position" -> Json.obj("x" -> 100, "y" -> 100), "data" -> Json.obj("label" -> "222", "type" -> "step")),
            )
          )

        }.recover { case e =>
          println(s"error: ${LogHelpers.getStackTraceAsString(e)}")
          assert(false)
        }

      }

      it("test updating campaign v2") {
        val nodes = List(
          Json.obj("id" -> 1, "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "1", "type" -> "step")),
          Json.obj("id" -> "has_replied", "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "has_replied", "type" -> "condition")),
          Json.obj("id" -> 11, "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "11", "type" -> "step")),
          Json.obj("id" -> 111, "position" -> Json.obj("x" -> 100, "y" -> 100), "data" -> Json.obj("label" -> "111", "type" -> "step")),
        )
        val edges = List(
          Json.obj("id" -> "xyz", "source" -> 1, "target" -> "has_replied", "label" -> "no_condition"),
          Json.obj("id" -> "xyz-11", "source" -> "has_replied", "target" -> 11, "label" -> "yes"),
          Json.obj("id" -> "xyz-111", "source" -> "has_replied", "target" -> 111, "label" -> "no")
        )
        val head_node_id = "1"

        val res = NextStepFinderForDrip.getDuplicateCampaign(
          old_head_node_id = head_node_id,
          old_edges = edges,
          old_nodes = nodes,
          ordered_step_ids_for_old_campaign = List(1, 11, 111),
          ordered_step_ids_for_new_campaign = List(2, 43, 789)
        )

        // val result = Await.result(res, 5000.millis)

        res.map { result =>

          assert(result.head_node_id == "2")

          assert(
            result.edges == List(
              Json.obj("id" -> "xyz", "source" -> 2, "target" -> "has_replied", "label" -> "no_condition"),
              Json.obj("id" -> "xyz-43", "source" -> "has_replied", "target" -> 43, "label" -> "yes"),
              Json.obj("id" -> "xyz-789", "source" -> "has_replied", "target" -> 789, "label" -> "no")
            )
          )
          assert(
            result.nodes == List(
              Json.obj("id" -> 2, "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "2", "type" -> "step")),
              Json.obj("id" -> "has_replied", "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "has_replied", "type" -> "condition")),
              Json.obj("id" -> 43, "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "43", "type" -> "step")),
              Json.obj("id" -> 789, "position" -> Json.obj("x" -> 100, "y" -> 100), "data" -> Json.obj("label" -> "789", "type" -> "step")),
            )
          )
        }.recover { e =>
          println(s"error: ${LogHelpers.getStackTraceAsString(e)}")
          assert(false)
        }

      }

      it("test updating campaign no data") {
        val nodes = List()
        val edges = List()
        val head_node_id = ""

        val res = NextStepFinderForDrip.getDuplicateCampaign(
          old_head_node_id = head_node_id,
          old_edges = edges,
          old_nodes = nodes,
          ordered_step_ids_for_old_campaign = List(1, 11, 111),
          ordered_step_ids_for_new_campaign = List(2, 22, 222)
        )

        val result = Await.result(res, 5000.millis)

        assert(result.head_node_id == "")

        assert(result.edges == List())
        assert(result.nodes == List( ))


      }

    }

    describe("getPath") {
      it("get-child-steps.js") {
        val res = NextStepFinderForDrip.getPath(Some("get-child-steps.js"))
        val result = Await.result(res, 5000.millis)
        println(result)
        assert(result.toLowerCase.contains("coldemail/src/main/resources/puppeteer/get-child-steps.js"))
      }
    }

    describe("test the reply tracking functionality for drip campaigns"){

      it("FOLLOWUP TEST - should schedule linkedin step if email bounced") {

          val initialData: InitialData = SRSetupAndDeleteFixtures.createInitialData(true).get
          val account: Account = initialData.account
          val teamId: TeamId = TeamId(account.teams.head.team_id)
          val emailSetting: EmailSetting = initialData.emailSetting.get

          val scheduledProspectIdsFut: Future[Future[(List[Long], List[Long])]] = for {
            startedCampaignDetails: StartedCampaignDetails <- CampaignUtils.createAndStartDripCampaign(
              initialData = initialData,
              designDripForCase = 21,
            )
            scheduleTaskData0: ScheduleTasksData <- {
              ScheduleTaskFixture.scheduleTaskForEmailChannel(
                emailChannelData = EmailChannelData(
                  emailSettingId = initialData.emailSetting.get.id.get.emailSettingId
                ),
                teamId = teamId
              )
            }
            db_updates <- Future.fromTry {
              CampaignUtils.updateCampaignFirstStep(
                campaignId = startedCampaignDetails.campaignId,
                emailSettingId = initialData.emailSetting.get.id.get,
                teamId = teamId
              )
            }

            read_email <- { // needs fixing
              Future.fromTry(emailReplyTrackingModelV2.saveEmailsAndRepliesFromInboxV3(
                account = account,
                accountId = account.internal_id,
                inboxEmailSetting = initialData.emailSetting.get,
                team_id = teamId.id,
                adminReplyFromSRInbox = false,
                markProspectAsCompleted = true,
                auditRequestLogId = "test_logger",
                replyHandling =  ReplyHandling.PAUSE_ALL_PROSPECT_CAMPAIGNS_ON_REPLY,
                senderEmails = Seq(emailSetting.email),

                emailMessages =
                  Seq(EmailMessageTracked(
                    inbox_email_setting_id = emailSetting.id.get.emailSettingId,
                    from = IEmailAddress(
                      name = Some("Bruce wayne"),
                      email = "<EMAIL>"
                    ), // Fixme check this

                    // v3 specific start
                    to_emails = Seq(IEmailAddress(
                        name = Some("Bruce wayne"),
                      email = "<EMAIL>"
                    )), // Seq[IEmailAddress], // only for newinbox api
                    // v3 specific end


                    subject = "",

                    body = "",
                    base_body = "",
                    text_body = "",

                    references_header = None, //Option[String], // Main

                    campaign_id = Some(startedCampaignDetails.campaignId.id),
                    step_id = None,

                    prospect_id_in_campaign = None, // prospect_id for whom campaign should be paused

                    prospect_account_id_in_campaign = None,

                    campaign_name = None,
                    step_name = None,

                    received_at = DateTime.now(),
                    recorded_at = DateTime.now(),
                    sr_inbox_read = true,  // check this
                    original_inbox_folder = None, // check this
                    email_status = EmailReplyStatus(
                      replyType =  EmailReplyType.DELIVERY_FAILED,
                      isReplied =  false,
                      bouncedData =Some(BounceData(
                        bounced_at = DateTime.now(),
                        bounce_type =  EmailReplyBounceType.EmailAddressNotFound,
                        bounce_reason = "",
                        is_soft_bounced = false
                      )),
                      isUnsubscribeRequest= false,
                      isAutoReply = false,
                      isOutOfOfficeReply = false,
                      isInvalidEmail = true,
                      isForwarded = false
                    ),

                    message_id = "",
                    full_headers = Json.obj(),

                    scheduled_manually = false,

                    reply_to = None, // check this one

                    email_thread_id = None, // check this also
                    gmail_msg_id = None,
                    gmail_thread_id = None,

                    outlook_msg_id = None,
                    outlook_conversation_id = None,
                    outlook_response_json = None,

                    cc_emails = Seq(),
                    in_reply_to_header = None,
                    team_id = teamId.id,
                    account_id = account.internal_id,


                    internal_tracking_note = InternalTrackingNote.NEW_LASTWEEK_DELIVERYFAILED,

                    tempThreadId = Some(234)
                  )),

              ))
            }

          } yield {
            for {

              // updating for linkedin channel scheduler

              _: Int <- Future.fromTry {
                SchedulerTestDAO.setLastScheduledForCampaignProspects(
                  campaignId = startedCampaignDetails.campaignId,
                  prospectIds = startedCampaignDetails.prospectIds,
                  teamId = teamId
                )
              }

              _: Int <- Future.fromTry {
                SchedulerTestDAO.updateLastTouchedAtForProspectMetadata(
                  prospectIds = startedCampaignDetails.prospectIds,
                  teamId = teamId
                )
              }

              _: Int <- Future.fromTry{

                SchedulerTestDAO.updateEmailScheduledToMakeSentSteps(
                  campaignId = startedCampaignDetails.campaignId,
                  prospects = startedCampaignDetails.prospectIds.map(_.id),
                  sent_ago_in_hours = 48
                )
              }

              _: Int <- Future.fromTry {
                SchedulerTestDAO.addLastScheduledAtForCampaign(
                  campaignId = startedCampaignDetails.campaignId,
                  teamId = teamId
                )
              }

              // Scheduling Linkeidn

              schedulingForLinkedinChannel: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForLinkedinChannel(
                linkedinChannelData = ChannelData.LinkedinChannelData(linkedinSettingId = initialData.linkedinAccountSettings.get.settings.uuid),
                teamId = teamId
              )

              linkedinScheduledProspects: List[Long] <- Future.fromTry {
                SchedulerTestDAO.getProspectIdForCampaignAndChannelTypeFromTasks(
                  campaignId = startedCampaignDetails.campaignId,
                  channelType = ChannelType.LinkedinChannel
                )
              }

              // Updating for call channel

              _: Int <- Future.fromTry {
                SchedulerTestDAO.setLastScheduledForCampaignProspects(
                  campaignId = startedCampaignDetails.campaignId,
                  prospectIds = startedCampaignDetails.prospectIds,
                  teamId = teamId
                )
              }

              _: Int <- Future.fromTry {
                SchedulerTestDAO.updateLastTouchedAtForProspectMetadata(
                  prospectIds = startedCampaignDetails.prospectIds,
                  teamId = teamId
                )
              }

              _: Int <- Future.fromTry {
                SchedulerTestDAO.addLastScheduledAtForCampaign(
                  campaignId = startedCampaignDetails.campaignId,
                  teamId = teamId
                )
              }

              // Scheduling for call channel

              scheduleCallChannel: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForCallChannel(
                callChannelData = ChannelData.CallChannelData(
                  callSettingUuid = initialData.callSettingUuid.get
                ),
                teamId = teamId
              )

              callScheduledProspects: List[Long] <- Future.fromTry {
                SchedulerTestDAO.getProspectIdForCampaignAndChannelTypeFromTasks(
                  campaignId = startedCampaignDetails.campaignId,
                  channelType = ChannelType.CallChannel
                )
              }


            } yield {
              (linkedinScheduledProspects,  callScheduledProspects)
            }
          }

          val scheduledProspectIds: Future[(List[Long], List[Long])] = scheduledProspectIdsFut.flatten
          scheduledProspectIds.map((p: (List[Long], List[Long])) => {
            assert(p._2.length == 2) // checking for call tasks
            assert(p._1.length == 1) // checking for linkedin tasks
          }).recover({ case e =>
            println(LogHelpers.getStackTraceAsString(e))
            assert(false)
          })

        }

      it("should mark prospect completed in auto_email campaign and shouldn't mark completed in drip campaign") {

        val createProspectData1 = ProspectCreateFormData(
          email = Some("<EMAIL>"),
          first_name = Some("Bruce"),
          last_name = Some("Wayne"),
          custom_fields = Json.obj(),

          list = None,
          company = None,
          city = None,
          country = None,
          timezone = None,

          state = None,
          job_title = None,
          phone = Some("+************"),
          phone_2 = None,
          phone_3 = None,
          linkedin_url = Some("https://linkedin.com/in/aditya-sadana")
        )

        val initialData: InitialData = SRSetupAndDeleteFixtures.createInitialData(true).get
        val account: Account = initialData.account
        val teamId: TeamId = TeamId(account.teams.head.team_id)
        val emailSetting: EmailSetting = initialData.emailSetting.get

        val scheduledProspectIdsFut: Future[Future[(List[Long], List[Long], List[(Int, Int, String, Long)], Boolean)]] = for {

          // Started drip campaign
          startedCampaignDetails_drip: StartedCampaignDetails <- CampaignUtils.createAndStartDripCampaign(
            initialData = initialData,
            designDripForCase = 21,
          )


          // Started Auto Campaign
          startedCampaignDetails_auto_email <- CampaignUtils.createAndStartAutoEmailCampaign(
            initialData =  initialData,
            prospects = Some(Seq(
              createProspectData1.copy(
                email = Some("<EMAIL>"),
                phone = Some("+************"
              )),
              createProspectData1.copy(
                email = Some("<EMAIL>"), phone = Some("+************")
              )
            ))
          )

          // Scheduling first step
          scheduleTaskData0: ScheduleTasksData <- {
            ScheduleTaskFixture.scheduleTaskForEmailChannel(
              emailChannelData = EmailChannelData(
                emailSettingId = initialData.emailSetting.get.id.get.emailSettingId
              ),
              teamId = teamId
            )
          }

          db_updates <- Future.fromTry {
            CampaignUtils.updateCampaignFirstStep(
              campaignId = startedCampaignDetails_drip.campaignId,
              emailSettingId = initialData.emailSetting.get.id.get,
              teamId = teamId
            )
          }

          read_email <- { // needs fixing
            Future.fromTry(emailReplyTrackingModelV2.saveEmailsAndRepliesFromInboxV3(
              account = account,
              accountId = account.internal_id,
              inboxEmailSetting = initialData.emailSetting.get,
              team_id = teamId.id,
              adminReplyFromSRInbox = false,
              markProspectAsCompleted = true,
              auditRequestLogId = "test_logger",
              replyHandling = ReplyHandling.PAUSE_ALL_PROSPECT_CAMPAIGNS_ON_REPLY,
              senderEmails = Seq(emailSetting.email),

              emailMessages =
                Seq(EmailMessageTracked(
                  inbox_email_setting_id = emailSetting.id.get.emailSettingId,
                  from = IEmailAddress(
                    name = Some("Bruce wayne"),
                    email = "<EMAIL>"
                  ), // Fixme check this

                  // v3 specific start
                  to_emails = Seq(IEmailAddress(
                    name = Some("Bruce wayne"),
                    email = "<EMAIL>"
                  )), // Seq[IEmailAddress], // only for newinbox api
                  // v3 specific end


                  subject = "",

                  body = "",
                  base_body = "",
                  text_body = "",

                  references_header = None, //Option[String], // Main

                  campaign_id = Some(startedCampaignDetails_drip.campaignId.id),
                  step_id = None,

                  prospect_id_in_campaign = None, // prospect_id for whom campaign should be paused

                  prospect_account_id_in_campaign = None,

                  campaign_name = None,
                  step_name = None,

                  received_at = DateTime.now(),
                  recorded_at = DateTime.now(),
                  sr_inbox_read = true, // check this
                  original_inbox_folder = None, // check this
                  email_status = EmailReplyStatus(
                    replyType = EmailReplyType.DELIVERY_FAILED,
                    isReplied = false,
                    bouncedData = Some(BounceData(
                      bounced_at = DateTime.now(),
                      bounce_type = EmailReplyBounceType.EmailAddressNotFound,
                      bounce_reason = "",
                      is_soft_bounced = false
                    )),
                    isUnsubscribeRequest = false,
                    isAutoReply = false,
                    isOutOfOfficeReply = false,
                    isInvalidEmail = true,
                    isForwarded = false
                  ),

                  message_id = "",
                  full_headers = Json.obj(),

                  scheduled_manually = false,

                  reply_to = None, // check this one

                  email_thread_id = None, // check this also
                  gmail_msg_id = None,
                  gmail_thread_id = None,

                  outlook_msg_id = None,
                  outlook_conversation_id = None,
                  outlook_response_json = None,

                  cc_emails = Seq(),
                  in_reply_to_header = None,
                  team_id = teamId.id,
                  account_id = account.internal_id,


                  internal_tracking_note = InternalTrackingNote.NEW_LASTWEEK_DELIVERYFAILED,

                  tempThreadId = Some(234)
                )),

            ))
          }

        } yield {
          for {

            // updating for linkedin channel scheduler

            _: Int <- Future.fromTry {
              SchedulerTestDAO.setLastScheduledForCampaignProspects(
                campaignId = startedCampaignDetails_drip.campaignId,
                prospectIds = startedCampaignDetails_drip.prospectIds,
                teamId = teamId
              )
            }

            _: Int <- Future.fromTry {
              SchedulerTestDAO.updateLastTouchedAtForProspectMetadata(
                prospectIds = startedCampaignDetails_drip.prospectIds,
                teamId = teamId
              )
            }

            _: Int <- Future.fromTry {

              SchedulerTestDAO.updateEmailScheduledToMakeSentSteps(
                campaignId = startedCampaignDetails_drip.campaignId,
                prospects = startedCampaignDetails_drip.prospectIds.map(_.id),
                sent_ago_in_hours = 48
              )
            }

            _: Int <- Future.fromTry {
              SchedulerTestDAO.addLastScheduledAtForCampaign(
                campaignId = startedCampaignDetails_drip.campaignId,
                teamId = teamId
              )
            }

            // Scheduling Linkeidn

            schedulingForLinkedinChannel: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForLinkedinChannel(
              linkedinChannelData = ChannelData.LinkedinChannelData(linkedinSettingId = initialData.linkedinAccountSettings.get.settings.uuid),
              teamId = teamId
            )

            linkedinScheduledProspects: List[Long] <- Future.fromTry {
              SchedulerTestDAO.getProspectIdForCampaignAndChannelTypeFromTasks(
                campaignId = startedCampaignDetails_drip.campaignId,
                channelType = ChannelType.LinkedinChannel
              )
            }

            // email_scheduled
            email_scheduled <- Future.fromTry{
              SchedulerTestDAO.selectEmailScheduledForIntegrationTest(
                campaign_id = startedCampaignDetails_auto_email.campaign.id,
                team_id = teamId.id,
                prospect_ids = Seq(startedCampaignDetails_auto_email.addProspect.last.id)
              )
            }

            // check_if_scheduled in auto email campaign
            check_if_completed_in_auto_email: Boolean <- Future.fromTry{

              SchedulerTestDAO.checkIfCampaignsProspectsCompleted(
                prospect_id = startedCampaignDetails_auto_email.addProspect.find(_.email.get == "<EMAIL>").get.id,
                campaign_id = startedCampaignDetails_auto_email.campaign.id,
                team_id = teamId.id
              )

            }


            check_if_completed_in_drip: Boolean <- Future.fromTry{

              SchedulerTestDAO.checkIfCampaignsProspectsCompleted(
                prospect_id = startedCampaignDetails_auto_email.addProspect.find(_.email.get == "<EMAIL>").get.id,
                campaign_id = startedCampaignDetails_drip.campaignId.id,
                team_id = teamId.id
              )

            }

            // Updating for call channel
            _: Int <- Future.fromTry {
              SchedulerTestDAO.setLastScheduledForCampaignProspects(
                campaignId = startedCampaignDetails_drip.campaignId,
                prospectIds = startedCampaignDetails_drip.prospectIds,
                teamId = teamId
              )
            }

            _: Int <- Future.fromTry {
              SchedulerTestDAO.updateLastTouchedAtForProspectMetadata(
                prospectIds = startedCampaignDetails_drip.prospectIds,
                teamId = teamId
              )
            }

            _: Int <- Future.fromTry {
              SchedulerTestDAO.addLastScheduledAtForCampaign(
                campaignId = startedCampaignDetails_drip.campaignId,
                teamId = teamId
              )
            }

            // Scheduling for call channel

            scheduleCallChannel: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForCallChannel(
              callChannelData = ChannelData.CallChannelData(
                callSettingUuid = initialData.callSettingUuid.get
              ),
              teamId = teamId
            )

            callScheduledProspects: List[Long] <- Future.fromTry {
              SchedulerTestDAO.getProspectIdForCampaignAndChannelTypeFromTasks(
                campaignId = startedCampaignDetails_drip.campaignId,
                channelType = ChannelType.CallChannel
              )
            }


          } yield {

            val is_prospect_completed_in_auth_and_not_in_drip = check_if_completed_in_auto_email && !check_if_completed_in_drip
            (linkedinScheduledProspects, callScheduledProspects, email_scheduled, is_prospect_completed_in_auth_and_not_in_drip)
          }
        }

        val scheduledProspectIds: Future[(List[Long], List[Long], List[(Int, Int, String, Long)], Boolean)] = scheduledProspectIdsFut.flatten
        scheduledProspectIds.map((p: (List[Long], List[Long], List[(Int, Int, String, Long)], Boolean)) => {
          assert(p._2.length == 2) // checking for call tasks
          assert(p._1.length == 1) // checking for linkedin tasks
          assert(p._3.length == 1)

          // checking if that particular prospect is completed in the auto-email campaigna and not in drip campaign
          assert(p._4)

        }).recover({ case e =>
          println(LogHelpers.getStackTraceAsString(e))
          assert(false)
        })

      }


    }

}
