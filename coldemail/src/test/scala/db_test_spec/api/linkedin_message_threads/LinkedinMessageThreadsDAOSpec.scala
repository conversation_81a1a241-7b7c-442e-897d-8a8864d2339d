package db_test_spec.api.linkedin_message_threads

import api.accounts.{Account, TeamId}
import api.accounts.models.{AccountId, OrgId}
import api.emails.{ConversationsSearchResponse, EmailSetting}
import api.emails.models.{InboxType, InboxTypeData}
import api.prospects.dao.ProspectIdAndPotentialDuplicateProspectId
import api.prospects.models.{PotentialDuplicateProspectId, ProspectId}
import api.prospects.{InferredQueryTimeline, MailboxFolder, ValidatedConvReq}
import app.db_test.CustomNotCategorizedAndDoNotContactIds
import db_test_spec.api.accounts.fixtures.NewAccountAndEmailSettingData
import db_test_spec.api.campaigns.fixtures.CreateNewCampaignFixture
import db_test_spec.api.campaigns.test_utils.{CampaignUtils, CreateAndStartCampaignData}
import db_test_spec.api.{DbTestingBeforeAllAndAfterAll, InitialData}
import eventframework.ConversationObjectInboxV3
import io.smartreach.esp.api.emails.EmailSettingId
import org.joda.time.DateTime
import scalikejdbc.interpolation.SQLSyntax

import scala.concurrent.Future
import scala.util.Try

class LinkedinMessageThreadsDAOSpec extends DbTestingBeforeAllAndAfterAll {

  describe("getConversationQueryV3 - LinkedinConversations") {

    it("should give success - testing query") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
      val account: Account = initialData.account
      val emailSetting: EmailSetting = initialData.emailSetting.get
      val orgId: OrgId = OrgId(account.org.id)
      val accountId: AccountId = AccountId(account.internal_id)
      val teamId: TeamId = TeamId(account.teams.head.team_id)
      val taId: Long = account.teams.head.access_members.head.ta_id
      val emailSettingId = EmailSettingId(emailSetting.id.get.emailSettingId)
      val customNotCategorizedAndDoNotContactIds: CustomNotCategorizedAndDoNotContactIds = CreateNewCampaignFixture.findCategorizedAndDoNotContactCustomIds(
        account.teams.head.prospect_categories_custom
      )

      val result: Future[List[ConversationObjectInboxV3.LinkedinConversationObjectInboxV3]] = for {

        campaign_new: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData
        )

        result_from_query <- Future.fromTry(linkedinMessageThreadsDAO.getQueryAndGetConversations(
          teamId = teamId.id,
          validatedConvReq = ValidatedConvReq.ValidatedMailboxReqRange(
            mailboxFolderRequest = MailboxFolder.TeamInboxFolder.Prospects(
              esetIds = Seq(emailSettingId.emailSettingId),
              prospect_category = None,
              replySentimentType = None
            ),
            timeline = InferredQueryTimeline.Range.Before(
              dateTime = DateTime.now()
            ),
            pageSize = 10
          ),
          replySentimentUuid = List()
        ))



      }yield{

        result_from_query

      }

      result.map(res => {

        assert(res == List())  //FIXME:: This integration test is for testing the query currently. Will add data in db and fix it later

        assert(true)

      })
        .recover(err => {

          Logger.error(s"Error : ${err}")

          assert(false)

        })


    }
  }

  describe("EmailThreadDAO:: getQueryForSearchInboxEmailThreads") {

    val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
    val account: Account = initialData.account
    val emailSetting: EmailSetting = initialData.emailSetting.get
    val orgId: OrgId = OrgId(account.org.id)
    val accountId: AccountId = AccountId(account.internal_id)
    val teamId: TeamId = TeamId(account.teams.head.team_id)
    val taId: Long = account.teams.head.access_members.head.ta_id
    val emailSettingId = EmailSettingId(emailSetting.id.get.emailSettingId)


    it("should pass :: Single") {
      val result: Future[List[ConversationsSearchResponse]] = for {

        query: SQLSyntax <- Future.successful(linkedinMessageThreadsDAO.getQueryForSearchInboxEmailThreads(
          teamId = teamId.id,
          searchKey = "john wick",
          settingsIds = Seq(emailSettingId.emailSettingId),
          inbox_type = InboxType.SINGLE,
          date_within = None,
          are_all_esets_accessible = false,
          sent_at = None,
          last_sent_id = None,
          folder_type = None
        ))

        convos: List[ConversationsSearchResponse] <- Future.fromTry(linkedinMessageThreadsDAO.getConversationsForSearch(query = query, inbox_type = InboxType.SINGLE))

      } yield {
        convos
      }

      result.map(rs => {

        assert(rs.isEmpty) // Fixme testing only query success


      })
        .recover(err => {
          println(s"err -> $err")
          assert(false)

        })

    }

    it("should pass :: Consolidated") {
      val result: Future[List[ConversationsSearchResponse]] = for {

        query: SQLSyntax <- Future.successful(linkedinMessageThreadsDAO.getQueryForSearchInboxEmailThreads(
          teamId = teamId.id,
          searchKey = "john wick",
          settingsIds = Seq(emailSettingId.emailSettingId),
          inbox_type = InboxType.CONSOLIDATED,
          date_within = None,
          are_all_esets_accessible = false,
          sent_at = None,
          last_sent_id = None,
          folder_type = None
        ))

        convos: List[ConversationsSearchResponse] <- Future.fromTry(linkedinMessageThreadsDAO.getConversationsForSearch(query = query, inbox_type = InboxType.SINGLE))

      } yield {
        convos
      }

      result.map(rs => {

        assert(rs.isEmpty) // Fixme testing only query success


      })
        .recover(err => {
          println(s"err -> $err")
          assert(false)

        })

    }

    it("should pass :: All_Campaigns") {
      val result: Future[List[ConversationsSearchResponse]] = for {

        query: SQLSyntax <- Future.successful(linkedinMessageThreadsDAO.getQueryForSearchInboxEmailThreads(
          teamId = teamId.id,
          searchKey = "john wick",
          settingsIds = Seq(emailSettingId.emailSettingId),
          inbox_type = InboxType.AllCampaigns,
          date_within = None,
          are_all_esets_accessible = false,
          sent_at = None,
          last_sent_id = None,
          folder_type = None
        ))

        convos: List[ConversationsSearchResponse] <- Future.fromTry(linkedinMessageThreadsDAO.getConversationsForSearch(query = query, inbox_type = InboxType.SINGLE))

      } yield {
        convos
      }

      result.map(rs => {

        assert(rs.isEmpty) // Fixme testing only query success


      })
        .recover(err => {
          println(s"err -> $err")
          assert(false)

        })

    }

  }

  describe("updateMasterProspectIdAfterMerge") {
    it("should return success") {
      val res: Try[List[Long]] = linkedinMessageThreadsDAO.updateMasterProspectIdAfterMerge(
        duplicateProspects = List(ProspectIdAndPotentialDuplicateProspectId(
          prospectId = ProspectId(1L),
          potentialDuplicateProspectId = PotentialDuplicateProspectId(1L),
          isMasterProspect = false
        )),
        masterProspectId = ProspectId(2L),
        teamId = TeamId(1L)
      )

      assert(res.isSuccess)
    }
  }

}
