package db_test_spec.api.columns

import api.AppConfig
import api.accounts.TeamId
import api.accounts.models.AccountId
import api.columns.{CustomColumnDefCreateForm, FieldTypeEnum}
import api.prospects.models.ProspectId
import db_test_spec.api.accounts.fixtures.NewAccountAndEmailSettingData
import db_test_spec.api.{DbTestingBeforeAllAndAfterAll, InitialData}
import utils.Helpers
import utils.helpers.LogHelpers

import scala.util.{Failure, Success}

class ProspectColumnDefSpec extends DbTestingBeforeAllAndAfterAll {

  describe("Test getMagicColsUsingColToBeDeleted") {

    it("should return a empty list if there are no magic columns present") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val account = initialData.account

      val accountId = AccountId(id = account.internal_id)

      val team = initialData.account.teams.head

      val teamId = TeamId(id = team.team_id)

      val taId = team.access_members.find { t =>

        t.user_id == account.internal_id

      }.get.ta_id

      val nonMagicCustomColToBeDeleted = prospectColumnDef.create(
        accountId = accountId.id,
        teamId = teamId.id,
        taId = taId,
        data = CustomColumnDefCreateForm(
          name = "favorite_color",
          field_type = FieldTypeEnum.TEXT,
          magic_prompt = None,
        ),
        channel = None,
      ).get.get

      prospectColumnDef.getMagicColsUsingColToBeDeleted(
        teamId = teamId,
        colToBeDeletedColId = nonMagicCustomColToBeDeleted.id.get,
        colToBeDeletedName = nonMagicCustomColToBeDeleted.name,
      ) match {
        case Failure(exception) =>

          println(exception)

          assert(false)

        case Success(magicColsUsingColToBeDeleted) =>

          assert(
            magicColsUsingColToBeDeleted.isEmpty
          )

      }

    }

    it("should return a empty list if there are no magic columns using the custom col to be deleted") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val account = initialData.account

      val accountId = AccountId(id = account.internal_id)

      val team = initialData.account.teams.head

      val teamId = TeamId(id = team.team_id)

      val taId = team.access_members.find { t =>

        t.user_id == account.internal_id

      }.get.ta_id

      val nonMagicCustomColToBeDeleted = prospectColumnDef.create(
        accountId = accountId.id,
        teamId = teamId.id,
        taId = taId,
        data = CustomColumnDefCreateForm(
          name = "favorite_color",
          field_type = FieldTypeEnum.TEXT,
          magic_prompt = None,
        ),
        channel = None,
      ).get.get

      val magicColUsingNonMagicCustomCol1 = prospectColumnDef.create(
        accountId = accountId.id,
        teamId = teamId.id,
        taId = taId,
        data = CustomColumnDefCreateForm(
          name = "sales",
          field_type = FieldTypeEnum.MAGIC,
          magic_prompt = Some(s"Write an sales email to user"),
        ),
        channel = None,
      ).get.get

      val magicColUsingNonMagicCustomCol2 = prospectColumnDef.create(
        accountId = accountId.id,
        teamId = teamId.id,
        taId = taId,
        data = CustomColumnDefCreateForm(
          name = "invite",
          field_type = FieldTypeEnum.MAGIC,
          magic_prompt = Some(s"Write an invitation email to user"),
        ),
        channel = None,
      ).get.get


      prospectColumnDef.getMagicColsUsingColToBeDeleted(
        teamId = teamId,
        colToBeDeletedColId = nonMagicCustomColToBeDeleted.id.get,
        colToBeDeletedName = nonMagicCustomColToBeDeleted.name,
      ) match {
        case Failure(exception) =>

          println(exception)

          assert(false)

        case Success(magicColsUsingColToBeDeleted) =>

          assert(
            magicColsUsingColToBeDeleted.isEmpty
          )

      }

    }

    it("should return the list of magic columns using the custom column to be deleted") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val account = initialData.account

      val accountId = AccountId(id = account.internal_id)

      val team = initialData.account.teams.head

      val teamId = TeamId(id = team.team_id)

      val taId = team.access_members.find { t =>

        t.user_id == account.internal_id

      }.get.ta_id

      val nonMagicCustomColToBeDeleted = prospectColumnDef.create(
        accountId = accountId.id,
        teamId = teamId.id,
        taId = taId,
        data = CustomColumnDefCreateForm(
          name = "favorite_color",
          field_type = FieldTypeEnum.TEXT,
          magic_prompt = None,
        ),
        channel = None,
      ).get.get

      val magicColUsingNonMagicCustomCol1 = prospectColumnDef.create(
        accountId = accountId.id,
        teamId = teamId.id,
        taId = taId,
        data = CustomColumnDefCreateForm(
          name = "sales",
          field_type = FieldTypeEnum.MAGIC,
          magic_prompt = Some(s"Write an sales email to user with favorite color: {{${nonMagicCustomColToBeDeleted.name}}}"),
        ),
        channel = None,
      ).get.get

      val magicColUsingNonMagicCustomCol2 = prospectColumnDef.create(
        accountId = accountId.id,
        teamId = teamId.id,
        taId = taId,
        data = CustomColumnDefCreateForm(
          name = "invite",
          field_type = FieldTypeEnum.MAGIC,
          magic_prompt = Some(s"Write an invitation email to user with favorite color: {{${nonMagicCustomColToBeDeleted.name}}}"),
        ),
        channel = None,
      ).get.get


      prospectColumnDef.getMagicColsUsingColToBeDeleted(
        teamId = teamId,
        colToBeDeletedColId = nonMagicCustomColToBeDeleted.id.get,
        colToBeDeletedName = nonMagicCustomColToBeDeleted.name,
      ) match {
        case Failure(exception) =>

          println(exception)

          assert(false)

        case Success(magicColsUsingColToBeDeleted) =>

          val magicColNamesSet = Set(magicColUsingNonMagicCustomCol2.name, magicColUsingNonMagicCustomCol1.name)
          val magicColIdsSet = Set(magicColUsingNonMagicCustomCol2.id.get, magicColUsingNonMagicCustomCol1.id.get)

          assert(
            magicColsUsingColToBeDeleted.map(_.name).toSet == magicColNamesSet &&
              magicColsUsingColToBeDeleted.map(_.id.get).toSet == magicColIdsSet
          )

      }

    }

  }

  describe("Test validateMagicPromptTemplate") {

    it("should not return any validation error if no merge tags are used in the prompt") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val team = initialData.account.teams.head

      val teamId = TeamId(id = team.team_id)

      val magicPrompt = "write an invite email for tech conference"

      prospectColumnDef.validateMagicPromptTemplate(
        magicPrompt = magicPrompt,
        teamId = teamId,
        channelOpt = None,
      ) match {

        case Failure(exception) =>

          println(exception)

          assert(false)

        case Success(prompt) =>

          assert(prompt == magicPrompt)

      }

    }

    it("should not return any validation error if non magic column merge tags are used in the prompt") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val team = initialData.account.teams.head

      val teamId = TeamId(id = team.team_id)

      val magicPrompt = "write an invite email for tech conference, email: {{email}}"

      prospectColumnDef.validateMagicPromptTemplate(
        magicPrompt = magicPrompt,
        teamId = teamId,
        channelOpt = None,
      ) match {

        case Failure(exception) =>

          println(exception)

          assert(false)

        case Success(prompt) =>

          assert(prompt == magicPrompt)

      }

    }

    it("should not return any validation error if there are magic columns but not used in the prompt") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val account = initialData.account

      val accountId = AccountId(id = account.internal_id)

      val team = initialData.account.teams.head

      val teamId = TeamId(id = team.team_id)

      val taId = team.access_members.find { t =>

        t.user_id == account.internal_id

      }.get.ta_id

      val _ = prospectColumnDef.create(
        accountId = accountId.id,
        teamId = teamId.id,
        taId = taId,
        data = CustomColumnDefCreateForm(
          name = "sales",
          field_type = FieldTypeEnum.MAGIC,
          magic_prompt = Some(s"Write an sales email to user with email: {{email}}"),
        ),
        channel = None,
      ).get.get

      val magicPrompt = "write an invite email for tech conference, email: {{email}}"

      prospectColumnDef.validateMagicPromptTemplate(
        magicPrompt = magicPrompt,
        teamId = teamId,
        channelOpt = None,
      ) match {

        case Failure(exception) =>

          println(exception)

          assert(false)

        case Success(prompt) =>

          assert(prompt == magicPrompt)

      }

    }


    it("should return any validation error if magic column merge tags are used in the prompt") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val account = initialData.account

      val accountId = AccountId(id = account.internal_id)

      val team = initialData.account.teams.head

      val teamId = TeamId(id = team.team_id)

      val taId = team.access_members.find { t =>

        t.user_id == account.internal_id

      }.get.ta_id

      val magicCol = prospectColumnDef.create(
        accountId = accountId.id,
        teamId = teamId.id,
        taId = taId,
        data = CustomColumnDefCreateForm(
          name = "sales",
          field_type = FieldTypeEnum.MAGIC,
          magic_prompt = Some(s"Write an sales email to user with email: {{email}}"),
        ),
        channel = None,
      ).get.get

      val magicPrompt = s"write an invite email for tech conference, email: {{email}} and {{${magicCol.name}}}"

      prospectColumnDef.validateMagicPromptTemplate(
        magicPrompt = magicPrompt,
        teamId = teamId,
        channelOpt = None,
      ) match {

        case Failure(exception) =>

          val errMsg = AppConfig.MagicColumns.getMagicColMergeTagUsedInAnotherPromptErr(
            usedMagicColNames = List(magicCol.name),
          )

          assert(exception.getMessage == errMsg)

        case Success(_) =>

          assert(false)

      }

    }

  }

  describe("Test updateMagicPrompt") {

    it("should not allow to update magic prompt is columnId does not belong to magic column") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val account = initialData.account

      val accountId = AccountId(id = account.internal_id)

      val team = initialData.account.teams.head

      val teamId = TeamId(id = team.team_id)

      val taId = team.access_members.find { t =>

        t.user_id == account.internal_id

      }.get.ta_id

      val nonMagicCustomCol = prospectColumnDef.create(
        accountId = accountId.id,
        teamId = teamId.id,
        taId = taId,
        data = CustomColumnDefCreateForm(
          name = "favorite_color",
          field_type = FieldTypeEnum.TEXT,
          magic_prompt = None,
        ),
        channel = None,
      ).get.get

      prospectColumnDef.updateMagicPrompt(
        teamId = teamId,
        magicColumnId = nonMagicCustomCol.id.get,
        updatedMagicPrompt = "Some updated magic prompt",
        channel = None,
      ) match {

        case Failure(exception) =>

          println(exception)

          assert(true)

        case Success(_) =>

          assert(false)

      }

    }

    it("should not updated magic prompt if the validation for new prompt fails") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val account = initialData.account

      val accountId = AccountId(id = account.internal_id)

      val team = initialData.account.teams.head

      val teamId = TeamId(id = team.team_id)

      val taId = team.access_members.find { t =>

        t.user_id == account.internal_id

      }.get.ta_id

      val magicCustomCol = prospectColumnDef.create(
        accountId = accountId.id,
        teamId = teamId.id,
        taId = taId,
        data = CustomColumnDefCreateForm(
          name = "inv",
          field_type = FieldTypeEnum.MAGIC,
          magic_prompt = Some("some prompt"),
        ),
        channel = None,
      ).get.get

      prospectColumnDef.updateMagicPrompt(
        teamId = teamId,
        magicColumnId = magicCustomCol.id.get,
        updatedMagicPrompt = "Some updated magic prompt {{first_name}",
        channel = None,
      ) match {

        case Failure(exception) =>

          println(exception)

          assert(true)

        case Success(_) =>

          assert(false)

      }

    }

    it("should replace the old prompt with the new updated prompt") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val account = initialData.account

      val accountId = AccountId(id = account.internal_id)

      val team = initialData.account.teams.head

      val teamId = TeamId(id = team.team_id)

      val taId = team.access_members.find { t =>

        t.user_id == account.internal_id

      }.get.ta_id

      val magicCustomCol = prospectColumnDef.create(
        accountId = accountId.id,
        teamId = teamId.id,
        taId = taId,
        data = CustomColumnDefCreateForm(
          name = "inv2",
          field_type = FieldTypeEnum.MAGIC,
          magic_prompt = Some("some old prompt"),
        ),
        channel = None,
      ).get.get

      val updatedPrompt = "Some updated magic prompt {{first_name}}"

      prospectColumnDef.updateMagicPrompt(
        teamId = teamId,
        magicColumnId = magicCustomCol.id.get,
        updatedMagicPrompt = updatedPrompt,
        channel = None,
      ) match {

        case Failure(exception) =>

          println(LogHelpers.getStackTraceAsString(exception))

          assert(false)

        case Success(None) =>

          println("NON FOUND!")

          assert(false)

        case Success(Some(colDef)) =>


          assert(
            colDef.id.get == magicCustomCol.id.get &&
              colDef.field_type == FieldTypeEnum.MAGIC &&
              colDef.prompt.contains(updatedPrompt)
          )

      }

    }

  }

}
