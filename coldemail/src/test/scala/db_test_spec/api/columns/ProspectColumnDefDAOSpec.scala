package db_test_spec.api.columns

import api.accounts.TeamId
import api.accounts.models.AccountId
import api.columns.models.ColumnDefsProspectsId
import api.columns.{CustomColumnDefCreateForm, FieldTypeEnum}
import api.prospects.models.ProspectId
import api.tasks.models.{NewTask, TaskCreatedVia, TaskData, TaskPriority, TaskStatus, TaskType}
import api.templates.TemplateCreateUpdateForm
import db_test_spec.api.{DbTestingBeforeAllAndAfterAll, InitialData}
import db_test_spec.api.accounts.fixtures.NewAccountAndEmailSettingData
import db_test_spec.api.campaigns.test_utils.CampaignUtils
import org.joda.time.DateTime
import org.scalatest.ParallelTestExecution
import scalikejdbc.DBSession
import utils.helpers.LogHelpers

import scala.util.{Failure, Success, Try}


class ProspectColumnDefDAOSpec extends DbTestingBeforeAllAndAfterAll with ParallelTestExecution {

  describe("Test regenerateMagicColumnForProspects") {

    it("should return an empty list when 0 records are updated") {

      val invalidTeamId = TeamId(id = 2352398) // does not exist

      val invalidProspectId = ProspectId(id = ********) // does not exist

      val invalidColumnId = ******** // does not exist


      val dbAndSession = dbUtils.startLocalTx()
      implicit val session: DBSession = dbAndSession.session

      val tryOfProspectColDefsUpdatedForRegeneration: Try[List[ColumnDefsProspectsId]] =
        prospectColumnDefDAO.regenerateMagicColumnForProspects(
          teamId = invalidTeamId,
          prospectIds = List(invalidProspectId),
          columnId = invalidColumnId,
        )

      dbUtils.commitAndCloseSession(db = dbAndSession.db)

      tryOfProspectColDefsUpdatedForRegeneration match {

        case Failure(exception) =>

          println(exception)

          assert(false)

        case Success(prospectColDefsUpdatedForRegeneration) =>

          assert(
            prospectColDefsUpdatedForRegeneration == List()
          )

      }

    }

  }

  describe("Test checkIfSomeCampaignStepHasMergeTagToBeDeleted") {

    it("should return false if there are no campaigns in the team") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val account = initialData.account

      val accountId = AccountId(id = account.internal_id)

      val team = initialData.account.teams.head

      val teamId = TeamId(id = team.team_id)

      val taId = team.access_members.find { t =>

        t.user_id == account.internal_id

      }.get.ta_id


      val nonMagicCustomCol = prospectColumnDef.create(
        accountId = accountId.id,
        teamId = teamId.id,
        taId = taId,
        data = CustomColumnDefCreateForm(
          name = "favorite_color",
          field_type = FieldTypeEnum.TEXT,
          magic_prompt = None,
        ),
        channel = None,
      ).get.get


      prospectColumnDefDAO.checkIfSomeCampaignStepHasMergeTagToBeDeleted(
        teamId = teamId,
        nonMagicCustomCol.name
      ) match {

        case Failure(exception) =>

          println(LogHelpers.getStackTraceAsString(exception))

          assert(false)

        case Success(hasMergeTagToBeDeleted) =>

          assert(!hasMergeTagToBeDeleted)

      }

    }

    it("should return false if there are no campaign steps which contain the merge tag to be deleted") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val account = initialData.account

      val accountId = AccountId(id = account.internal_id)

      val team = initialData.account.teams.head

      val teamId = TeamId(id = team.team_id)

      val emailSetting = initialData.emailSetting.get

      val taId = team.access_members.find { t =>

        t.user_id == account.internal_id

      }.get.ta_id

      val nonMagicCustomCol = prospectColumnDef.create(
        accountId = accountId.id,
        teamId = teamId.id,
        taId = taId,
        data = CustomColumnDefCreateForm(
          name = "favorite_color",
          field_type = FieldTypeEnum.TEXT,
          magic_prompt = None,
        ),
        channel = None,
      ).get.get

      CampaignUtils.createAndStartAutoEmailCampaign(
        initialData = initialData,
        email_body = s"Hello {{email}} your fav color",
        emailSettings = Some(emailSetting),
        generateProspectCountIfNoGivenProspect = 1,
      ).map { _ =>

        prospectColumnDefDAO.checkIfSomeCampaignStepHasMergeTagToBeDeleted(
          teamId = teamId,
          nonMagicCustomCol.name
        ) match {

          case Failure(exception) =>

            println(LogHelpers.getStackTraceAsString(exception))

            assert(false)

          case Success(hasMergeTagToBeDeleted) =>

            assert(!hasMergeTagToBeDeleted)

        }

      }.recover { err =>

        println(LogHelpers.getStackTraceAsString(err))

        assert(false)

      }

    }

    it("should return true if there are campaign steps which contain the merge tag to be deleted - no space") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val account = initialData.account

      val accountId = AccountId(id = account.internal_id)

      val team = initialData.account.teams.head

      val teamId = TeamId(id = team.team_id)

      val emailSetting = initialData.emailSetting.get

      val taId = team.access_members.find { t =>

        t.user_id == account.internal_id

      }.get.ta_id

      val nonMagicCustomCol = prospectColumnDef.create(
        accountId = accountId.id,
        teamId = teamId.id,
        taId = taId,
        data = CustomColumnDefCreateForm(
          name = "favorite_animal",
          field_type = FieldTypeEnum.TEXT,
          magic_prompt = None,
        ),
        channel = None,
      ).get.get

      CampaignUtils.createAndStartAutoEmailCampaign(
        initialData = initialData,
        email_body = s"Hello {{email}} your fav color: {{${nonMagicCustomCol.name}}}",
        emailSettings = Some(emailSetting),
        generateProspectCountIfNoGivenProspect = 1,
      ).map { _ =>

        prospectColumnDefDAO.checkIfSomeCampaignStepHasMergeTagToBeDeleted(
          teamId = teamId,
          nonMagicCustomCol.name
        ) match {

          case Failure(exception) =>

            println(LogHelpers.getStackTraceAsString(exception))

            assert(false)

          case Success(hasMergeTagToBeDeleted) =>

            assert(hasMergeTagToBeDeleted)

        }

      }.recover { err =>

        println(LogHelpers.getStackTraceAsString(err))

        assert(false)

      }

    }

    it("should return true if there are campaign steps which contain the merge tag to be deleted - space inside") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val account = initialData.account

      val accountId = AccountId(id = account.internal_id)

      val team = initialData.account.teams.head

      val teamId = TeamId(id = team.team_id)

      val emailSetting = initialData.emailSetting.get

      val taId = team.access_members.find { t =>

        t.user_id == account.internal_id

      }.get.ta_id

      val nonMagicCustomCol = prospectColumnDef.create(
        accountId = accountId.id,
        teamId = teamId.id,
        taId = taId,
        data = CustomColumnDefCreateForm(
          name = "favorite_color",
          field_type = FieldTypeEnum.TEXT,
          magic_prompt = None,
        ),
        channel = None,
      ).get.get

      CampaignUtils.createAndStartAutoEmailCampaign(
        initialData = initialData,
        email_body = s"Hello {{email}} your fav color: {{  ${nonMagicCustomCol.name}}}",
        emailSettings = Some(emailSetting),
        generateProspectCountIfNoGivenProspect = 1,
      ).map { _ =>

        prospectColumnDefDAO.checkIfSomeCampaignStepHasMergeTagToBeDeleted(
          teamId = teamId,
          nonMagicCustomCol.name
        ) match {

          case Failure(exception) =>

            println(LogHelpers.getStackTraceAsString(exception))

            assert(false)

          case Success(hasMergeTagToBeDeleted) =>

            assert(hasMergeTagToBeDeleted)

        }

      }.recover { err =>

        println(LogHelpers.getStackTraceAsString(err))

        assert(false)

      }

    }


    it("should return false for invalid merge tag - brackets have space in between") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val account = initialData.account

      val accountId = AccountId(id = account.internal_id)

      val team = initialData.account.teams.head

      val teamId = TeamId(id = team.team_id)

      val emailSetting = initialData.emailSetting.get

      val taId = team.access_members.find { t =>

        t.user_id == account.internal_id

      }.get.ta_id

      val nonMagicCustomCol = prospectColumnDef.create(
        accountId = accountId.id,
        teamId = teamId.id,
        taId = taId,
        data = CustomColumnDefCreateForm(
          name = "fav_book",
          field_type = FieldTypeEnum.TEXT,
          magic_prompt = None,
        ),
        channel = None,
      ).get.get

      CampaignUtils.createAndStartAutoEmailCampaign(
        initialData = initialData,
        email_body = s"Hello {{email}} your fav color:  { {   ${nonMagicCustomCol.name}  }} some subj wasd  = ",
        emailSettings = Some(emailSetting),
        generateProspectCountIfNoGivenProspect = 1,
      ).map { _ =>

        prospectColumnDefDAO.checkIfSomeCampaignStepHasMergeTagToBeDeleted(
          teamId = teamId,
          nonMagicCustomCol.name
        ) match {

          case Failure(exception) =>

            println(LogHelpers.getStackTraceAsString(exception))

            assert(false)

          case Success(hasMergeTagToBeDeleted) =>

            assert(!hasMergeTagToBeDeleted)

        }

      }.recover { err =>

        println(LogHelpers.getStackTraceAsString(err))

        assert(false)

      }

    }

  }

  describe("Test checkIfSomeTaskHasMergeTagToBeDeleted") {

    it("should return false if there are no tasks in the team") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val account = initialData.account

      val accountId = AccountId(id = account.internal_id)

      val team = initialData.account.teams.head

      val teamId = TeamId(id = team.team_id)

      val taId = team.access_members.find { t =>

        t.user_id == account.internal_id

      }.get.ta_id


      val nonMagicCustomCol = prospectColumnDef.create(
        accountId = accountId.id,
        teamId = teamId.id,
        taId = taId,
        data = CustomColumnDefCreateForm(
          name = "favorite_color",
          field_type = FieldTypeEnum.TEXT,
          magic_prompt = None,
        ),
        channel = None,
      ).get.get


      prospectColumnDefDAO.checkIfSomeTaskHasMergeTagToBeDeleted(
        teamId = teamId,
        nonMagicCustomCol.name
      ) match {

        case Failure(exception) =>

          println(LogHelpers.getStackTraceAsString(exception))

          assert(false)

        case Success(hasMergeTagToBeDeleted) =>

          assert(!hasMergeTagToBeDeleted)

      }

    }

    it("should return false if there are no tasks which contain the merge tag to be deleted") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val account = initialData.account

      val accountId = AccountId(id = account.internal_id)

      val team = initialData.account.teams.head

      val teamId = TeamId(id = team.team_id)

      val taId = team.access_members.find { t =>

        t.user_id == account.internal_id

      }.get.ta_id

      val nonMagicCustomCol = prospectColumnDef.create(
        accountId = accountId.id,
        teamId = teamId.id,
        taId = taId,
        data = CustomColumnDefCreateForm(
          name = "favorite_color",
          field_type = FieldTypeEnum.TEXT,
          magic_prompt = None,
        ),
        channel = None,
      ).get.get

      taskService.createTask(
        task_data = NewTask(
          campaign_id = None,
          campaign_name = None,
          step_id = None,
          step_label = None,
          created_via = TaskCreatedVia.Manual,
          is_opening_step = None,
          task_type = TaskType.GeneralTask,
          is_auto_task = false,
          task_data = TaskData.GeneralTaskData(
            task_notes = "some message body"
          ),
          status = TaskStatus.Due(
            due_at = DateTime.now().plusDays(27)
          ),
          assignee_id = Some(accountId.id),
          prospect_id = Some(initialData.prospectsResult.head.id),
          priority = TaskPriority.Low,
          emailsScheduledUuid = None,
          note = None,
        ),
        accountId = accountId.id,
        teamId = teamId.id,
      ).map {

        case Left(err) =>

          println(s"Failed to create task: $err")

          assert(false)

        case Right(_) =>

          prospectColumnDefDAO.checkIfSomeTaskHasMergeTagToBeDeleted(
            teamId = teamId,
            nonMagicCustomCol.name
          ) match {

            case Failure(exception) =>

              println(LogHelpers.getStackTraceAsString(exception))

              assert(false)

            case Success(hasMergeTagToBeDeleted) =>

              assert(!hasMergeTagToBeDeleted)

          }

      }.recover { err =>

        println(LogHelpers.getStackTraceAsString(err))

        assert(false)

      }

    }

    it("should return true if there are tasks which contain the merge tag to be deleted - no space") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val account = initialData.account

      val accountId = AccountId(id = account.internal_id)

      val team = initialData.account.teams.head

      val teamId = TeamId(id = team.team_id)

      val taId = team.access_members.find { t =>

        t.user_id == account.internal_id

      }.get.ta_id

      val nonMagicCustomCol = prospectColumnDef.create(
        accountId = accountId.id,
        teamId = teamId.id,
        taId = taId,
        data = CustomColumnDefCreateForm(
          name = "favorite_animal",
          field_type = FieldTypeEnum.TEXT,
          magic_prompt = None,
        ),
        channel = None,
      ).get.get

      taskService.createTask(
        task_data = NewTask(
          campaign_id = None,
          campaign_name = None,
          step_id = None,
          step_label = None,
          created_via = TaskCreatedVia.Manual,
          is_opening_step = None,
          task_type = TaskType.GeneralTask,
          is_auto_task = false,
          task_data = TaskData.GeneralTaskData(
            task_notes = s"Hello {{email}} your fav color: {{${nonMagicCustomCol.name}}}"
          ),
          status = TaskStatus.Due(
            due_at = DateTime.now().plusDays(27)
          ),
          assignee_id = Some(accountId.id),
          prospect_id = Some(initialData.prospectsResult.head.id),
          priority = TaskPriority.Low,
          emailsScheduledUuid = None,
          note = None,
        ),
        accountId = accountId.id,
        teamId = teamId.id,
      ).map { _ =>

        prospectColumnDefDAO.checkIfSomeTaskHasMergeTagToBeDeleted(
          teamId = teamId,
          nonMagicCustomCol.name
        ) match {

          case Failure(exception) =>

            println(LogHelpers.getStackTraceAsString(exception))

            assert(false)

          case Success(hasMergeTagToBeDeleted) =>

            assert(hasMergeTagToBeDeleted)

        }

      }.recover { err =>

        println(LogHelpers.getStackTraceAsString(err))

        assert(false)

      }

    }

    it("should return true if there are tasks which contain the merge tag to be deleted - space inside") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val account = initialData.account

      val accountId = AccountId(id = account.internal_id)

      val team = initialData.account.teams.head

      val teamId = TeamId(id = team.team_id)

      val taId = team.access_members.find { t =>

        t.user_id == account.internal_id

      }.get.ta_id

      val nonMagicCustomCol = prospectColumnDef.create(
        accountId = accountId.id,
        teamId = teamId.id,
        taId = taId,
        data = CustomColumnDefCreateForm(
          name = "favorite_color",
          field_type = FieldTypeEnum.TEXT,
          magic_prompt = None,
        ),
        channel = None,
      ).get.get

      taskService.createTask(
        task_data = NewTask(
          campaign_id = None,
          campaign_name = None,
          step_id = None,
          step_label = None,
          created_via = TaskCreatedVia.Manual,
          is_opening_step = None,
          task_type = TaskType.GeneralTask,
          is_auto_task = false,
          task_data = TaskData.GeneralTaskData(
            task_notes = s"Hello {{email}} your fav color"
          ),
          status = TaskStatus.Due(
            due_at = DateTime.now().plusDays(27)
          ),
          assignee_id = Some(accountId.id),
          prospect_id = Some(initialData.prospectsResult.head.id),
          priority = TaskPriority.Low,
          note = Some(s"Hello {{email}} your fav color: {{  ${nonMagicCustomCol.name}}}"),
          emailsScheduledUuid = None,
        ),
        accountId = accountId.id,
        teamId = teamId.id,
      ).map { _ =>

        prospectColumnDefDAO.checkIfSomeTaskHasMergeTagToBeDeleted(
          teamId = teamId,
          nonMagicCustomCol.name
        ) match {

          case Failure(exception) =>

            println(LogHelpers.getStackTraceAsString(exception))

            assert(false)

          case Success(hasMergeTagToBeDeleted) =>

            assert(hasMergeTagToBeDeleted)

        }

      }.recover { err =>

        println(LogHelpers.getStackTraceAsString(err))

        assert(false)

      }

    }

    it("should return false for invalid merge tag - brackets have space in between") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val account = initialData.account

      val accountId = AccountId(id = account.internal_id)

      val team = initialData.account.teams.head

      val teamId = TeamId(id = team.team_id)

      val taId = team.access_members.find { t =>

        t.user_id == account.internal_id

      }.get.ta_id

      val nonMagicCustomCol = prospectColumnDef.create(
        accountId = accountId.id,
        teamId = teamId.id,
        taId = taId,
        data = CustomColumnDefCreateForm(
          name = "fav_book",
          field_type = FieldTypeEnum.TEXT,
          magic_prompt = None,
        ),
        channel = None,
      ).get.get

      taskService.createTask(
        task_data = NewTask(
          campaign_id = None,
          campaign_name = None,
          step_id = None,
          step_label = None,
          created_via = TaskCreatedVia.Manual,
          is_opening_step = None,
          task_type = TaskType.GeneralTask,
          is_auto_task = false,
          task_data = TaskData.GeneralTaskData(
            task_notes = s"Hello {{email}} your fav color:  { {   ${nonMagicCustomCol.name}  }} some subj wasd  = "
          ),
          status = TaskStatus.Due(
            due_at = DateTime.now().plusDays(27)
          ),
          assignee_id = Some(accountId.id),
          prospect_id = Some(initialData.prospectsResult.head.id),
          priority = TaskPriority.Low,
          note = None,
          emailsScheduledUuid = None,
        ),
        accountId = accountId.id,
        teamId = teamId.id,
      ).map { _ =>

        prospectColumnDefDAO.checkIfSomeTaskHasMergeTagToBeDeleted(
          teamId = teamId,
          nonMagicCustomCol.name
        ) match {

          case Failure(exception) =>

            println(LogHelpers.getStackTraceAsString(exception))

            assert(false)

          case Success(hasMergeTagToBeDeleted) =>

            assert(!hasMergeTagToBeDeleted)

        }

      }.recover { err =>

        println(LogHelpers.getStackTraceAsString(err))

        assert(false)

      }

    }

  }

  describe("Test checkIfSomeTemplateHasMergeTagToBeDeleted") {

    it("should return false if there are no templates in the team") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val account = initialData.account

      val accountId = AccountId(id = account.internal_id)

      val team = initialData.account.teams.head

      val teamId = TeamId(id = team.team_id)

      val taId = team.access_members.find { t =>

        t.user_id == account.internal_id

      }.get.ta_id


      val nonMagicCustomCol = prospectColumnDef.create(
        accountId = accountId.id,
        teamId = teamId.id,
        taId = taId,
        data = CustomColumnDefCreateForm(
          name = "favorite_color",
          field_type = FieldTypeEnum.TEXT,
          magic_prompt = None,
        ),
        channel = None,
      ).get.get


      prospectColumnDefDAO.checkIfSomeTemplateHasMergeTagToBeDeleted(
        teamId = teamId,
        nonMagicCustomCol.name
      ) match {

        case Failure(exception) =>

          println(LogHelpers.getStackTraceAsString(exception))

          assert(false)

        case Success(hasMergeTagToBeDeleted) =>

          assert(!hasMergeTagToBeDeleted)

      }

    }

    it("should return false if there are no templates which contain the merge tag to be deleted") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val account = initialData.account

      val accountId = AccountId(id = account.internal_id)

      val team = initialData.account.teams.head

      val teamId = TeamId(id = team.team_id)

      val taId = team.access_members.find { t =>

        t.user_id == account.internal_id

      }.get.ta_id

      val nonMagicCustomCol = prospectColumnDef.create(
        accountId = accountId.id,
        teamId = teamId.id,
        taId = taId,
        data = CustomColumnDefCreateForm(
          name = "favorite_color",
          field_type = FieldTypeEnum.TEXT,
          magic_prompt = None,
        ),
        channel = None,
      ).get.get

      templateDAO.create(
        accountId = accountId.id,
        teamId = teamId.id,
        taId = taId,
        data = TemplateCreateUpdateForm(
          label = "some-label-1",
          subject = Some("some subject"),
          is_from_library = None,
          body = "Some body",
          shared_with_team = None
        ),
      ) match {
        case Failure(exception) =>

          println(LogHelpers.getStackTraceAsString(exception))

          assert(false)


        case Success(None) =>

          println("NONE FOUND")

          assert(false)


        case Success(Some(_)) =>

          prospectColumnDefDAO.checkIfSomeTemplateHasMergeTagToBeDeleted(
            teamId = teamId,
            nonMagicCustomCol.name
          ) match {

            case Failure(exception) =>

              println(LogHelpers.getStackTraceAsString(exception))

              assert(false)

            case Success(hasMergeTagToBeDeleted) =>

              assert(!hasMergeTagToBeDeleted)

          }

      }

    }

    it("should return true if there are templates which contain the merge tag to be deleted - no space") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val account = initialData.account

      val accountId = AccountId(id = account.internal_id)

      val team = initialData.account.teams.head

      val teamId = TeamId(id = team.team_id)

      val taId = team.access_members.find { t =>

        t.user_id == account.internal_id

      }.get.ta_id

      val nonMagicCustomCol = prospectColumnDef.create(
        accountId = accountId.id,
        teamId = teamId.id,
        taId = taId,
        data = CustomColumnDefCreateForm(
          name = "favorite_animal",
          field_type = FieldTypeEnum.TEXT,
          magic_prompt = None,
        ),
        channel = None,
      ).get.get

      templateDAO.create(
        accountId = accountId.id,
        teamId = teamId.id,
        taId = taId,
        data = TemplateCreateUpdateForm(
          label = "some-label-1",
          subject = Some("some subject"),
          is_from_library = None,
          body = s"Hello {{email}} your fav color: {{${nonMagicCustomCol.name}}}",
          shared_with_team = None
        ),
      ) match {
        case Failure(exception) =>

          println(LogHelpers.getStackTraceAsString(exception))

          assert(false)


        case Success(None) =>

          println("NONE FOUND")

          assert(false)


        case Success(Some(_)) =>

          prospectColumnDefDAO.checkIfSomeTemplateHasMergeTagToBeDeleted(
            teamId = teamId,
            nonMagicCustomCol.name
          ) match {

            case Failure(exception) =>

              println(LogHelpers.getStackTraceAsString(exception))

              assert(false)

            case Success(hasMergeTagToBeDeleted) =>

              assert(hasMergeTagToBeDeleted)

          }

      }

    }

    it("should return true if there are tasks which contain the merge tag to be deleted - space inside") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val account = initialData.account

      val accountId = AccountId(id = account.internal_id)

      val team = initialData.account.teams.head

      val teamId = TeamId(id = team.team_id)

      val taId = team.access_members.find { t =>

        t.user_id == account.internal_id

      }.get.ta_id

      val nonMagicCustomCol = prospectColumnDef.create(
        accountId = accountId.id,
        teamId = teamId.id,
        taId = taId,
        data = CustomColumnDefCreateForm(
          name = "favorite_color",
          field_type = FieldTypeEnum.TEXT,
          magic_prompt = None,
        ),
        channel = None,
      ).get.get

      templateDAO.create(
        accountId = accountId.id,
        teamId = teamId.id,
        taId = taId,
        data = TemplateCreateUpdateForm(
          label = "some-label-1",
          subject = Some(s"Hello {{email}} your fav color: {{${nonMagicCustomCol.name}}}"),
          is_from_library = None,
          body = "some body",
          shared_with_team = None
        ),
      ) match {
        case Failure(exception) =>

          println(LogHelpers.getStackTraceAsString(exception))

          assert(false)


        case Success(None) =>

          println("NONE FOUND")

          assert(false)


        case Success(Some(_)) =>

          prospectColumnDefDAO.checkIfSomeTemplateHasMergeTagToBeDeleted(
            teamId = teamId,
            nonMagicCustomCol.name
          ) match {

            case Failure(exception) =>

              println(LogHelpers.getStackTraceAsString(exception))

              assert(false)

            case Success(hasMergeTagToBeDeleted) =>

              assert(hasMergeTagToBeDeleted)

          }

      }

    }

    it("should return false for invalid merge tag - brackets have space in between") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val account = initialData.account

      val accountId = AccountId(id = account.internal_id)

      val team = initialData.account.teams.head

      val teamId = TeamId(id = team.team_id)

      val taId = team.access_members.find { t =>

        t.user_id == account.internal_id

      }.get.ta_id

      val nonMagicCustomCol = prospectColumnDef.create(
        accountId = accountId.id,
        teamId = teamId.id,
        taId = taId,
        data = CustomColumnDefCreateForm(
          name = "fav_book",
          field_type = FieldTypeEnum.TEXT,
          magic_prompt = None,
        ),
        channel = None,
      ).get.get

      templateDAO.create(
        accountId = accountId.id,
        teamId = teamId.id,
        taId = taId,
        data = TemplateCreateUpdateForm(
          label = "some-label-1",
          subject = Some(s"Hello {{email}} your fav color:  { {   ${nonMagicCustomCol.name}  }} some subj wasd  = "),
          is_from_library = None,
          body = "some body",
          shared_with_team = None
        ),
      ) match {
        case Failure(exception) =>

          println(LogHelpers.getStackTraceAsString(exception))

          assert(false)


        case Success(None) =>

          println("NONE FOUND")

          assert(false)


        case Success(Some(_)) =>

          prospectColumnDefDAO.checkIfSomeTemplateHasMergeTagToBeDeleted(
            teamId = teamId,
            nonMagicCustomCol.name
          ) match {

            case Failure(exception) =>

              println(LogHelpers.getStackTraceAsString(exception))

              assert(false)

            case Success(hasMergeTagToBeDeleted) =>

              assert(!hasMergeTagToBeDeleted)

          }

      }

    }

  }

}
