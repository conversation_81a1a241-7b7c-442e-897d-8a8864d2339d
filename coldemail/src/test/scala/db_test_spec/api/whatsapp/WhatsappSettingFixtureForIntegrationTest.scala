package db_test_spec.api.whatsapp

import api.accounts.TeamId
import api.accounts.models.AccountId
import api.whatsapp.WhatsappSettingUuid
import api.whatsapp.models.{CreateOrUpdateWhatsappAccountSettings, WhatsappAccountSettings}
import utils.SRLogger
import utils.testapp.TestAppTrait

import scala.util.Try


case class WhatsappAccount(id: WhatsappSettingUuid, settings: WhatsappAccountSettings)

object WhatsappSettingFixtureForIntegrationTest extends TestAppTrait{
  def createWhatsappSetting(
                             accountId: AccountId,
                             teamId: TeamId,
                             whatsappAccountSettings: Option[CreateOrUpdateWhatsappAccountSettings] = None
                           )(using Logger: SRLogger): Try[WhatsappAccount] = {

    val whatsappAccountSettingData: Option[CreateOrUpdateWhatsappAccountSettings] = if (whatsappAccountSettings.isDefined) {
      whatsappAccountSettings
    } else {
      Some(DefaultWhatsappSettingParametersFixtures.defaultWhatsappAccountSettings(
        accountId = accountId
      ))
    }

    val whatsappAccountId = srUuidUtils.generateWhatsappAccountUuid

    for {

      whatsappSettingId <- Try(
        whatsappSettingDAO.createWhatsappAccount(
          data = whatsappAccountSettingData.get,
          teamId = teamId.id,
          uuid = whatsappAccountId
        ))



      whatsappAccSetting: Option[WhatsappAccountSettings] <- Try(whatsappSettingDAO.findWhatsappAccountByUuid(
        uuid = whatsappAccountId,
        teamId = teamId.id
      ).get)


    } yield {
      WhatsappAccount(
        id =WhatsappSettingUuid(whatsappAccountId),
        settings = whatsappAccSetting.get
      )
    }
  }
}
