package db_test_spec.api.whatsapp

import api.accounts.models.AccountId
import api.whatsapp.models.CreateOrUpdateWhatsappAccountSettings
import db_test_spec.utils.SrRandomTestUtils
import utils.testapp.TestAppTrait

object DefaultWhatsappSettingParametersFixtures extends  TestAppTrait{
  def defaultWhatsappAccountSettings(
                                      accountId: AccountId
                                    ): CreateOrUpdateWhatsappAccountSettings = CreateOrUpdateWhatsappAccountSettings(
    first_name = SrRandomTestUtils.getRandomStringOfLengthN(7),
    last_name = SrRandomTestUtils.getRandomStringOfLengthN(7),
    whatsapp_number = SrRandomTestUtils.getRandomPhoneNumber,
    owner_id = accountId.id,
    whatsapp_message_limit_per_day = 50
  )
}
