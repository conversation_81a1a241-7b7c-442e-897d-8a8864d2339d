package db_test_spec.api.linkedin.fixtures

import api.accounts.models.AccountId
import api.accounts.TeamId
import api.linkedin.LinkedinValidationError
import api.linkedin.models.{CreateOrUpdateLinkedinAccountSettings, LinkedinAccountSettings, LinkedinSettingId}
import utils.SRLogger
import utils.testapp.TestAppTrait

import scala.util.{Failure, Success, Try}
import db_test_spec.api.linkedin.fixtures.DefaultLinkedinSettingParametersFixtures.defaultLinkedInAccountSettings

import scala.concurrent.{Await, Future}
import scala.concurrent.duration._

case class LinkedinAccount(id: LinkedinSettingId, settings: LinkedinAccountSettings)

object LinkedInSettingFixtureForIntegrationTest extends TestAppTrait{

  def createLinkedInSetting(
                            accountId: AccountId,
                            teamId: TeamId,
                            linkedInAccountSettings: Option[CreateOrUpdateLinkedinAccountSettings] = None
                          )(using Logger: SRLogger): Try[LinkedinAccount] = {

    val linkedInAccountSettingsData: Option[CreateOrUpdateLinkedinAccountSettings] = if(linkedInAccountSettings.isDefined){
      linkedInAccountSettings
    } else {
      Some(defaultLinkedInAccountSettings(
        accountId = accountId
      ))
    }

    val res: Future[LinkedinAccount] = for {

      linkedInSettingEither:  Either[LinkedinValidationError, Long] <-
        linkedinSettingService.addLinkedinAccountSettings(
          accountSettings = linkedInAccountSettingsData.get,
          tid = teamId.id
        )

      linkedInAccountSettingId:Long <- Future.fromTry(linkedInSettingEither match {
        case Left(value) => value match {
          case LinkedinValidationError.ViewProfileLimitError(err) =>
            Failure(new Exception(err))

          case LinkedinValidationError.PlanLimitError(err) =>
            Failure(new Exception(err))

          case LinkedinValidationError.ConnectionRequestLimitError(err) =>
            Failure(new Exception(err))

          case LinkedinValidationError.MessageLimitError(err) =>
            Failure(new Exception(err))

          case LinkedinValidationError.InmailLimitError(err) =>
            Failure(new Exception(err))

          case LinkedinValidationError.LoginCountryCannotBeChanged =>
            Failure(new Exception("Login Country Cannot be changed"))

          case LinkedinValidationError.SQLException(err) =>
            Failure(err)

          case LinkedinValidationError.CaptainDataLinkingError(err) =>
            Failure(err)

          case LinkedinValidationError.CookieNotPresentError =>
            Failure(new Exception("Cookie not present"))

          case LinkedinValidationError.InvalidLinkedinProfileUrl =>
            Failure(new Exception("Invalid LinkedIn profile URL"))

          case LinkedinValidationError.DuplicateLinkedinAccount =>
            Failure(new Exception("Duplicate LinkedIn account"))

        }
        case Right(value) => Success(value)
      })

        linkedAccountSetting: LinkedinAccountSettings <- Future.successful(linkedinSettingService.findLinkedinAccount(
          id = Some(linkedInAccountSettingId),
          uuid = None,
          teamId = teamId
        ).get)


    } yield {
      LinkedinAccount(
        id = LinkedinSettingId(linkedInAccountSettingId),
        settings = linkedAccountSetting
      )


    }

    Try(Await.result(res, 2.seconds))
  }

}
