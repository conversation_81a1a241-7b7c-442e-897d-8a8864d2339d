package db_test_spec.api.linkedin

import api.accounts.models.{AccountId, OrgId}
import api.accounts.{Account, TeamId}
import api.campaigns.models.IgnoreProspectsInOtherCampaigns
import api.captain_data.{<PERSON><PERSON><PERSON><PERSON><PERSON>, Captain<PERSON><PERSON><PERSON><PERSON>unt<PERSON><PERSON>, Captain<PERSON><PERSON><PERSON><PERSON>UID}
import api.campaigns.{CampaignWithStatsAndEmail, ChannelSettingUuid}
import api.emails.EmailSetting
import api.linkedin.LinkedinSettingService
import api.linkedin.models.LinkedinSettingUuid
import api.linkedin.models.{CaptainDataWorkflowStatus, LinkedinAccountStatus}
import api.prospects.{ProspectCreateFormData, UpsertSQLProspectData}
import api.prospects.models.{SrProspectColumns, UpdateProspectType}
import api.tasks.models.{NewTask, TaskCreatedVia, TaskData, TaskPriority, TaskStatus, TaskType}
import db_test_spec.api.{DbTestingBeforeAllAndAfterAll, InitialData}
import db_test_spec.api.accounts.fixtures.NewAccountAndEmailSettingData
import db_test_spec.api.campaigns.test_utils.{CampaignUtils, CreateAndStartCampaignData}
import eventframework.ProspectObject
import org.joda.time.DateTime
import org.scalamock.scalatest.AsyncMockFactory
import org.scalatest.funspec.AsyncFunSpec
import org.scalatest.matchers.should.Matchers
import play.api.libs.json.Json
import play.api.libs.ws.WSClient
import scalikejdbc.{DB, SQL, scalikejdbcSQLInterpolationImplicitDef}
import sr_scheduler.models.ChannelType
import utils.SRLogger

import scala.concurrent.{Await, ExecutionContext, Future}
import scala.concurrent.duration.*
import scala.compiletime.uninitialized
import scala.util.{Failure, Success}

class LinkedinSettingServiceSpec extends AsyncFunSpec with DbTestingBeforeAllAndAfterAll with AsyncMockFactory with Matchers{

  // Create mocks for the dependencies
  val mockCaptainDataAPI: CaptainDataAPI = stub[api.captain_data.CaptainDataAPI]
  // We don't mock TaskService as it calls the DAO layer which we want to execute in integration tests

  // Test data setup
  var initialData: InitialData = uninitialized
  var campaignData: CreateAndStartCampaignData = uninitialized
  var account: Account = uninitialized
  var emailSetting: EmailSetting = uninitialized
  var orgId: OrgId = uninitialized
  var accountId: AccountId = uninitialized
  var teamId: TeamId = uninitialized
  var taId: Long = uninitialized
  var linkedinAccountUuid: LinkedinSettingUuid = uninitialized

  override def beforeAll(): Unit = {
    super.beforeAll()

    // Create test data
    implicit val logger: SRLogger = new SRLogger("[LinkedinSettingServiceSpec] :: ")

    initialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
    account = initialData.account
    emailSetting = initialData.emailSetting.get
    orgId = OrgId(account.org.id)
    accountId = AccountId(account.internal_id)
    teamId = TeamId(account.teams.head.team_id)
    taId = account.teams.head.access_members.head.ta_id

    // Create LinkedIn account
    linkedinAccountUuid = LinkedinSettingUuid(s"linkedin-test-${System.currentTimeMillis()}")
    DB.autoCommit { implicit session =>
      sql"""
           INSERT INTO linkedin_settings (
             uuid,
             owner_account_id,
             team_id,
             first_name,
             last_name,
             email,
             view_profile_limit_per_day,
             inmail_limit_per_day,
             message_limit_per_day,
             connection_request_limit_per_day,
             status,
             captain_data_account_id,
             captain_data_user_id,
             service_provider
           ) VALUES (
             ${linkedinAccountUuid.uuid},
             ${accountId.id},
             ${teamId.id},
             'Test',
             'LinkedIn',
             '<EMAIL>',
             100,
             50,
             50,
             50,
             ${LinkedinAccountStatus.Active.toString},
             'test-captain-data-account-id',
             'test-captain-data-user-id',
             'captain_data'
           )
           """
        .update
        .apply()
    }

    // Create campaign and prospect
    val prospectData = ProspectCreateFormData(
      email = Some("<EMAIL>"),
      first_name = Some("Test"),
      last_name = Some("Prospect"),
      custom_fields = Json.obj(),
      owner_id = Some(accountId.id),
      list = Some("test-list"),
      company = Some("Test Company"),
      city = Some("Test City"),
      country = Some("Test Country"),
      timezone = Some("Asia/Kolkata"),
      created_at = None,
      state = None,
      job_title = Some("Test Job"),
      phone = None,
      phone_2 = None,
      phone_3 = None,
      linkedin_url = Some("https://linkedin.com/in/test-prospect")
    )

    campaignData = Await.result(
      CampaignUtils.createAndStartAutoEmailCampaign(
        initialData = initialData,
        generateProspectCountIfNoGivenProspect = 0, // Don't generate, we'll use our own
        prospects = Some(Seq(prospectData))
      ),
      30.seconds
    )

    // Link campaign to LinkedIn account via campaign_channel_settings
    DB.autoCommit { implicit session =>
      sql"""
           INSERT INTO campaign_channel_settings (
             campaign_id,
             team_id,
             channel_type,
             channel_settings_uuid
           ) VALUES (
             ${campaignData.createCampaign.id},
             ${teamId.id},
             ${ChannelType.LinkedinChannel.toString},
             ${linkedinAccountUuid.uuid}
           )
           """
        .update
        .apply()
    }
  }

  // Helper method to create a unique prospect for each test
  def createUniqueProspect(): UpsertSQLProspectData = {
    val timestamp = System.currentTimeMillis()
    val prospectData = ProspectCreateFormData(
      email = Some(s"test.prospect.${timestamp}@example.com"),
      first_name = Some("Test"),
      last_name = Some(s"Prospect${timestamp}"),
      custom_fields = Json.obj(),
      owner_id = Some(accountId.id),
      list = Some("test-list"),
      company = Some("Test Company"),
      city = Some("Test City"),
      country = Some("Test Country"),
      timezone = Some("Asia/Kolkata"),
      created_at = None,
      state = None,
      job_title = Some("Test Job"),
      phone = None,
      phone_2 = None,
      phone_3 = None,
      linkedin_url = Some(s"https://linkedin.com/in/test-prospect-${timestamp}")
    )

    // Create prospect using ProspectService
    implicit val logger: SRLogger = new SRLogger("[LinkedinSettingServiceSpec] :: ")
    val createResult = prospectService.createOrUpdateProspects(
      ownerAccountId = accountId.id,
      teamId = teamId.id,
      listName = None,
      prospects = Seq(prospectData),
      updateProspectType = UpdateProspectType.ForceUpdate,
      ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads = true,
      doerAccount = account,
      prospectSource = None,
      prospectAccountId = None,
      campaign_id = Some(campaignData.createCampaign.id),
      prospect_tags = None,
      ignoreProspectInOtherCampaign = IgnoreProspectsInOtherCampaigns.DoNotIgnore,
      deduplicationColumns = Some(Seq(SrProspectColumns.Email)),
      auditRequestLogId = None,
      batchInsertLimit = 1000,
      SRLogger = logger
    ).get

    // Return the first created prospect
    createResult.inserted_or_updated_prospects.head
  }


  describe("Testing LinkedinSettingService.handleSendLinkedinConnection") {
    implicit val logger: SRLogger = new SRLogger("[LinkedinSettingServiceSpec] :: ")
    implicit val ec: ExecutionContext = scala.concurrent.ExecutionContext.global
    implicit val ws: WSClient = stub[WSClient]

    // Test for successful connection request

    it("should successfully send a LinkedIn connection request and update task status") {
      // Create a unique prospect for this test
      val testProspect = createUniqueProspect()

      // Create a NewTask object using the campaign and prospect data
      val newTask = NewTask(
        campaign_id = Some(campaignData.createCampaign.id),
        campaign_name = Some(campaignData.createCampaign.name),
        step_id = Some(campaignData.addStep.step_id),
        step_label = campaignData.addStep.label,
        created_via = TaskCreatedVia.Scheduler,
        is_opening_step = Some(true),
        task_type = TaskType.AutoLinkedinConnectionRequest,
        is_auto_task = true,
        task_data = TaskData.LinkedinConnectionRequestData(
          request_message = Some("Hello, I'd like to connect with you.")
        ),
        status = TaskStatus.Due(
          due_at = DateTime.now().plusHours(1)
        ),
        assignee_id = Some(accountId.id),
        prospect_id = Some(testProspect.prospect_data.prospect_id),
        priority = TaskPriority.Normal,
        note = Some("Test note")
      )

      // Create the task using TaskService
      val taskIdFuture = taskService.createTask(
        task_data = newTask,
        accountId = accountId.id,
        teamId = teamId.id
      ).map {
        case Right(taskId) => taskId
        case Left(error) => fail(s"Failed to create task: $error")
      }

      // Wait for the task to be created
      val taskId = Await.result(taskIdFuture, 5.seconds)

      // Test data
      val accountUid = CaptainDataAccountUID("test-account-uid")
      val linkedinProfileUrl = "https://linkedin.com/in/test-profile"
      val messageBody = Some("Hello, I'd like to connect with you.")
      val jobUid = CaptainDataJobUID("test-job-uid")

      // Set up mocks - only mock the API call, not the DAO calls
      (mockCaptainDataAPI.sendLinkedInConnection(_: CaptainDataAccountUID, _: Option[String], _: String)(_: WSClient, _: ExecutionContext))
        .when(accountUid, messageBody, linkedinProfileUrl, *, *)
        .returns(Future.successful(jobUid))

      // Create a test instance with our mocks
      val testLinkedinSettingService = new LinkedinSettingService(
        linkedinSettingDAO = linkedinSettingDAO,
        taskService = taskService, // Use the real taskService from DI
        organizationService = organizationService,
        accountService = accountService,
        emailNotificationService = emailNotificationService,
        planLimitService = planLimitService,
        phantomBusterProxyService = phantomBusterProxyService,
        brightDataService = brightDataService,
        accountOrgBillingRelatedService = accountOrgBillingRelatedService,
        mqExtractLinkedinConnectionResults = mqExtractLinkedinConnectionResults,
        srRandomUtils = srRandomUtils,
        srRedisSimpleLockServiceV2 = srRedisSimpleLockServiceV2,
        captainDataService = captainDataService,
        linkedinConnectionsDAO = linkedinConnectionsDAO,
        mqCaptainDataConversationExtractionPublisher = mqCaptainDataConversationExtractionPublisher,
        mqCaptainDataMessageExtractionPublisher = mqCaptainDataMessageExtractionPublisher,
        mqCaptainDataCookieFailurePublisher = mqCaptainDataCookieFailurePublisher,
        srUuidUtils = srUuidUtils,
        captainDataAPI = mockCaptainDataAPI
      )

      val result = testLinkedinSettingService.sendLinkedinConnectionRequest(
        accountUid, linkedinProfileUrl, messageBody, taskId, teamId
      )

      // Return the future result
      result.map { resultJobUid =>
        // Verify that the result is the expected job UID
        assert(resultJobUid == jobUid)

        // Verify that the API call was mocked correctly
        (mockCaptainDataAPI.sendLinkedInConnection(_: CaptainDataAccountUID, _: Option[String], _: String)(_: WSClient, _: ExecutionContext))
          .verify(accountUid, messageBody, linkedinProfileUrl, *, *)
          .once()

        // Verify the database state - the task should have been updated with the job UID
        // and the status should be set to INITIATED
        val updatedTask = getTaskFromDatabase(taskId, teamId)

        // The task should exist and have the correct job UID and status
        assert(updatedTask.isDefined, "Task not found in database")
        updatedTask.foreach { case (id, jobUidOpt, statusOpt, attemptsOpt) =>
          assert(id == taskId, s"Task ID mismatch: expected $taskId, got $id")
          assert(jobUidOpt.contains(jobUid.toString), s"Job UID mismatch: expected ${jobUid.toString}, got $jobUidOpt")
          assert(statusOpt.contains(CaptainDataWorkflowStatus.INITIATED.toString), s"Status mismatch: expected ${CaptainDataWorkflowStatus.INITIATED.toString}, got $statusOpt")
          assert(attemptsOpt.getOrElse(0) > 0, s"Execution attempts should be > 0, got ${attemptsOpt.getOrElse(0)}")
        }

        succeed
      }
    }

    it("should handle errors when sending a LinkedIn connection request fails") {
      // Create a unique prospect for this test
      val testProspect = createUniqueProspect()

      // Create a NewTask object using the campaign and prospect data
      val newTask = NewTask(
        campaign_id = Some(campaignData.createCampaign.id),
        campaign_name = Some(campaignData.createCampaign.name),
        step_id = Some(campaignData.addStep.step_id),
        step_label = campaignData.addStep.label,
        created_via = TaskCreatedVia.Scheduler,
        is_opening_step = Some(true),
        task_type = TaskType.AutoLinkedinConnectionRequest,
        is_auto_task = true,
        task_data = TaskData.LinkedinConnectionRequestData(
          request_message = Some("Hello, I'd like to connect with you.")
        ),
        status = TaskStatus.Due(
          due_at = DateTime.now().plusHours(1)
        ),
        assignee_id = Some(accountId.id),
        prospect_id = Some(testProspect.prospect_data.prospect_id),
        priority = TaskPriority.Normal,
        note = Some("Test note")
      )

      // Create the task using TaskService
      val taskIdFuture = taskService.createTask(
        task_data = newTask,
        accountId = accountId.id,
        teamId = teamId.id
      ).map {
        case Right(taskId) => taskId
        case Left(error) => fail(s"Failed to create task: $error")
      }

      // Wait for the task to be created
      val taskId = Await.result(taskIdFuture, 5.seconds)

      // Test data
      val accountUid = CaptainDataAccountUID("test-account-uid")
      val linkedinProfileUrl = "https://linkedin.com/in/test-profile"
      val messageBody = Some("Hello, I'd like to connect with you.")
      val testException = new RuntimeException("API call failed")

      // Set up mocks to simulate failure
      (mockCaptainDataAPI.sendLinkedInConnection(_: CaptainDataAccountUID, _: Option[String], _: String)(_: WSClient, _: ExecutionContext))
        .when(accountUid, messageBody, linkedinProfileUrl, *, *)
        .returns(Future.failed(testException))

      // Create a test instance with our mocks
      val testLinkedinSettingService = new LinkedinSettingService(
        linkedinSettingDAO = linkedinSettingDAO,
        taskService = taskService, // Use the real taskService from DI
        organizationService = organizationService,
        accountService = accountService,
        emailNotificationService = emailNotificationService,
        planLimitService = planLimitService,
        phantomBusterProxyService = phantomBusterProxyService,
        brightDataService = brightDataService,
        accountOrgBillingRelatedService = accountOrgBillingRelatedService,
        mqExtractLinkedinConnectionResults = mqExtractLinkedinConnectionResults,
        linkedinConnectionsDAO = linkedinConnectionsDAO,
        srRandomUtils = srRandomUtils,
        srRedisSimpleLockServiceV2 = srRedisSimpleLockServiceV2,
        captainDataService = captainDataService,
        mqCaptainDataConversationExtractionPublisher = mqCaptainDataConversationExtractionPublisher,
        mqCaptainDataMessageExtractionPublisher = mqCaptainDataMessageExtractionPublisher,
        mqCaptainDataCookieFailurePublisher = mqCaptainDataCookieFailurePublisher,
        srUuidUtils = srUuidUtils,
        captainDataAPI = mockCaptainDataAPI
      )

      // Call the private method using reflection
      val result = testLinkedinSettingService.sendLinkedinConnectionRequest(
        accountUid, linkedinProfileUrl, messageBody, taskId, teamId
      )

      // Return the failed future
      recoverToSucceededIf[RuntimeException] {
        result
      }.map { _ =>
        // Verify that the mocked method was called with the expected parameters
        (mockCaptainDataAPI.sendLinkedInConnection(_: CaptainDataAccountUID, _: Option[String], _: String)(_: WSClient, _: ExecutionContext))
          .verify(accountUid, messageBody, linkedinProfileUrl, *, *)
          .once()

        // Since the API call fails, the task status update should not happen
        // We don't need to verify this as we're using the real taskService

        succeed
      }
    }

    it("should handle invalid task ID scenario") {
      // Test data with an invalid task ID that doesn't exist in the database
      val accountUid = CaptainDataAccountUID("test-account-uid")
      val linkedinProfileUrl = "https://linkedin.com/in/test-profile"
      val messageBody = Some("Hello, I'd like to connect with you.")
      val invalidTaskId = "non-existent-task-id" // This task ID doesn't exist in the database
      val jobUid = CaptainDataJobUID("test-job-uid")

      // Set up mocks for the API call
      (mockCaptainDataAPI.sendLinkedInConnection(_: CaptainDataAccountUID, _: Option[String], _: String)(_: WSClient, _: ExecutionContext))
        .when(accountUid, messageBody, linkedinProfileUrl, *, *)
        .returns(Future.successful(jobUid))

      // Create a test instance with our mocks
      val testLinkedinSettingService = new LinkedinSettingService(
        linkedinSettingDAO = linkedinSettingDAO,
        taskService = taskService, // Use the real taskService from DI
        organizationService = organizationService,
        accountService = accountService,
        emailNotificationService = emailNotificationService,
        planLimitService = planLimitService,
        phantomBusterProxyService = phantomBusterProxyService,
        brightDataService = brightDataService,
        accountOrgBillingRelatedService = accountOrgBillingRelatedService,
        mqExtractLinkedinConnectionResults = mqExtractLinkedinConnectionResults,
        srRandomUtils = srRandomUtils,
        srRedisSimpleLockServiceV2 = srRedisSimpleLockServiceV2,
        linkedinConnectionsDAO = linkedinConnectionsDAO,
        captainDataService = captainDataService,
        mqCaptainDataConversationExtractionPublisher = mqCaptainDataConversationExtractionPublisher,
        mqCaptainDataMessageExtractionPublisher = mqCaptainDataMessageExtractionPublisher,
        mqCaptainDataCookieFailurePublisher = mqCaptainDataCookieFailurePublisher,
        srUuidUtils = srUuidUtils,
        captainDataAPI = mockCaptainDataAPI
      )

      // Call the private method using reflection
      val result = testLinkedinSettingService.sendLinkedinConnectionRequest(
        accountUid, linkedinProfileUrl, messageBody, invalidTaskId, teamId
      )

      // The test should either fail with a database error or return 0 rows updated
      result.map { resultJobUid =>
        // Verify that the API call was mocked correctly
        (mockCaptainDataAPI.sendLinkedInConnection(_: CaptainDataAccountUID, _: Option[String], _: String)(_: WSClient, _: ExecutionContext))
          .verify(accountUid, messageBody, linkedinProfileUrl, *, *)
          .once()

        // The job UID should be returned even if the task update fails
        assert(resultJobUid == jobUid)

        // Since we're using an invalid task ID, the database update should have affected 0 rows
        // We can verify this by checking that no task with this ID and job UID exists
        val updatedTask = getTaskFromDatabase(invalidTaskId, teamId)

        // The task should not be found
        assert(updatedTask.isEmpty, s"Task with ID $invalidTaskId should not exist but was found")
        succeed
      }
    }
  }

  // Tests for handleViewLinkedinProfile function
  describe("LinkedinSettingService.handleViewLinkedinProfile") {
    implicit val logger: SRLogger = new SRLogger("[LinkedinSettingServiceSpec] :: ")
    implicit val ec: ExecutionContext = scala.concurrent.ExecutionContext.global
    implicit val ws: WSClient = stub[WSClient]

    it("should successfully view a LinkedIn profile and update task status") {
      // Create a unique prospect for this test
      val testProspect = createUniqueProspect()

      // Create a NewTask object using the campaign and prospect data
      val newTask = NewTask(
        campaign_id = Some(campaignData.createCampaign.id),
        campaign_name = Some(campaignData.createCampaign.name),
        step_id = Some(campaignData.addStep.step_id),
        step_label = campaignData.addStep.label,
        created_via = TaskCreatedVia.Scheduler,
        is_opening_step = Some(true),
        task_type = TaskType.AutoViewLinkedinProfile,
        is_auto_task = true,
        task_data = TaskData.AutoViewLinkedinProfile(),
        status = TaskStatus.Due(
          due_at = DateTime.now().plusHours(1)
        ),
        assignee_id = Some(accountId.id),
        prospect_id = Some(testProspect.prospect_data.prospect_id),
        priority = TaskPriority.Normal,
        note = Some("Test note")
      )

      // Create the task using TaskService
      val taskIdFuture = taskService.createTask(
        task_data = newTask,
        accountId = accountId.id,
        teamId = teamId.id
      ).map {
        case Right(taskId) => taskId
        case Left(error) => fail(s"Failed to create task: $error")
      }

      // Wait for the task to be created
      val taskId = Await.result(taskIdFuture, 5.seconds)

      // Test data
      val accountUid = CaptainDataAccountUID("test-account-uid")
      val linkedinProfileUrl = "https://linkedin.com/in/test-profile"
      val jobUid = CaptainDataJobUID("test-job-uid")

      // Set up mocks to simulate a successful API call
      (mockCaptainDataAPI.viewLinkedInProfile(_: CaptainDataAccountUID, _: String)(_: WSClient, _: ExecutionContext))
        .when(accountUid, linkedinProfileUrl, *, *)
        .returns(Future.successful(jobUid))

      // Create a test instance with our mocks
      val testLinkedinSettingService = new LinkedinSettingService(
        linkedinSettingDAO = linkedinSettingDAO,
        taskService = taskService, // Use the real taskService from DI
        organizationService = organizationService,
        accountService = accountService,
        emailNotificationService = emailNotificationService,
        planLimitService = planLimitService,
        phantomBusterProxyService = phantomBusterProxyService,
        brightDataService = brightDataService,
        accountOrgBillingRelatedService = accountOrgBillingRelatedService,
        mqExtractLinkedinConnectionResults = mqExtractLinkedinConnectionResults,
        srRandomUtils = srRandomUtils,
        srRedisSimpleLockServiceV2 = srRedisSimpleLockServiceV2,
        linkedinConnectionsDAO = linkedinConnectionsDAO,
        captainDataService = captainDataService,
        mqCaptainDataConversationExtractionPublisher = mqCaptainDataConversationExtractionPublisher,
        mqCaptainDataMessageExtractionPublisher = mqCaptainDataMessageExtractionPublisher,
        mqCaptainDataCookieFailurePublisher = mqCaptainDataCookieFailurePublisher,
        srUuidUtils = srUuidUtils,
        captainDataAPI = mockCaptainDataAPI
      )

      // Call the method
      val result = testLinkedinSettingService.handleViewLinkedinProfile(
        accountUid, linkedinProfileUrl, taskId, teamId
      )

      // The result should be a successful Future with the job UID
      result.map { resultJobUid =>
        // The job UID should match what was returned by the mock
        assert(resultJobUid == jobUid, s"Expected job UID $jobUid but got $resultJobUid")

        // Verify that the API call was mocked correctly
        (mockCaptainDataAPI.viewLinkedInProfile(_: CaptainDataAccountUID, _: String)(_: WSClient, _: ExecutionContext))
          .verify(accountUid, linkedinProfileUrl, *, *)
          .once()

        // Verify the database state - the task should have been updated with the job UID
        // and the status should be set to INITIATED
        val updatedTask = getTaskFromDatabase(taskId, teamId)

        // The task should exist and have the correct job UID and status
        assert(updatedTask.isDefined, "Task not found in database")
        updatedTask.foreach { case (id, jobUidOpt, statusOpt, attemptsOpt) =>
          assert(id == taskId, s"Task ID mismatch: expected $taskId, got $id")
          assert(jobUidOpt.contains(jobUid.toString), s"Job UID mismatch: expected ${jobUid.toString}, got $jobUidOpt")
          assert(statusOpt.contains(CaptainDataWorkflowStatus.INITIATED.toString), s"Status mismatch: expected ${CaptainDataWorkflowStatus.INITIATED.toString}, got $statusOpt")
          assert(attemptsOpt.getOrElse(0) > 0, s"Execution attempts should be > 0, got ${attemptsOpt.getOrElse(0)}")
        }

        succeed
      }
    }

    it("should handle errors when viewing a LinkedIn profile fails") {
      // Create a unique prospect for this test
      val testProspect = createUniqueProspect()

      // Create a NewTask object using the campaign and prospect data
      val newTask = NewTask(
        campaign_id = Some(campaignData.createCampaign.id),
        campaign_name = Some(campaignData.createCampaign.name),
        step_id = Some(campaignData.addStep.step_id),
        step_label = campaignData.addStep.label,
        created_via = TaskCreatedVia.Scheduler,
        is_opening_step = Some(true),
        task_type = TaskType.AutoViewLinkedinProfile,
        is_auto_task = true,
        task_data = TaskData.AutoViewLinkedinProfile(),
        status = TaskStatus.Due(
          due_at = DateTime.now().plusHours(1)
        ),
        assignee_id = Some(accountId.id),
        prospect_id = Some(testProspect.prospect_data.prospect_id),
        priority = TaskPriority.Normal,
        note = Some("Test note")
      )

      // Create the task using TaskService
      val taskIdFuture = taskService.createTask(
        task_data = newTask,
        accountId = accountId.id,
        teamId = teamId.id
      ).map {
        case Right(taskId) => taskId
        case Left(error) => fail(s"Failed to create task: $error")
      }

      // Wait for the task to be created
      val taskId = Await.result(taskIdFuture, 5.seconds)

      // Test data
      val accountUid = CaptainDataAccountUID("test-account-uid")
      val linkedinProfileUrl = "https://linkedin.com/in/test-profile"
      val testException = new RuntimeException("API call failed")

      // Set up mocks to simulate failure
      (mockCaptainDataAPI.viewLinkedInProfile(_: CaptainDataAccountUID, _: String)(_: WSClient, _: ExecutionContext))
        .when(accountUid, linkedinProfileUrl, *, *)
        .returns(Future.failed(testException))

      // Create a test instance with our mocks
      val testLinkedinSettingService = new LinkedinSettingService(
        linkedinSettingDAO = linkedinSettingDAO,
        taskService = taskService, // Use the real taskService from DI
        organizationService = organizationService,
        accountService = accountService,
        emailNotificationService = emailNotificationService,
        planLimitService = planLimitService,
        phantomBusterProxyService = phantomBusterProxyService,
        brightDataService = brightDataService,
        accountOrgBillingRelatedService = accountOrgBillingRelatedService,
        mqExtractLinkedinConnectionResults = mqExtractLinkedinConnectionResults,
        linkedinConnectionsDAO = linkedinConnectionsDAO,
        srRandomUtils = srRandomUtils,
        srRedisSimpleLockServiceV2 = srRedisSimpleLockServiceV2,
        captainDataService = captainDataService,
        mqCaptainDataConversationExtractionPublisher = mqCaptainDataConversationExtractionPublisher,
        mqCaptainDataMessageExtractionPublisher = mqCaptainDataMessageExtractionPublisher,
        mqCaptainDataCookieFailurePublisher = mqCaptainDataCookieFailurePublisher,
        srUuidUtils = srUuidUtils,
        captainDataAPI = mockCaptainDataAPI
      )

      // Call the method
      val result = testLinkedinSettingService.handleViewLinkedinProfile(
        accountUid, linkedinProfileUrl, taskId, teamId
      )

      // The result should be a failed Future with the test exception
      recoverToSucceededIf[RuntimeException] {
        result
      }.map { _ =>
        // Verify that the mocked method was called with the expected parameters
        (mockCaptainDataAPI.viewLinkedInProfile(_: CaptainDataAccountUID, _: String)(_: WSClient, _: ExecutionContext))
          .verify(accountUid, linkedinProfileUrl, *, *)
          .once()

        // Since the API call fails, the task status update should not happen
        // We don't need to verify this as we're using the real taskService

        succeed
      }
    }

    it("should handle invalid task ID scenario for view profile") {
      // Test data with an invalid task ID that doesn't exist in the database
      val accountUid = CaptainDataAccountUID("test-account-uid")
      val linkedinProfileUrl = "https://linkedin.com/in/test-profile"
      val invalidTaskId = "non-existent-task-id" // This task ID doesn't exist in the database
      val jobUid = CaptainDataJobUID("test-job-uid")

      // Set up mocks for the API call
      (mockCaptainDataAPI.viewLinkedInProfile(_: CaptainDataAccountUID, _: String)(_: WSClient, _: ExecutionContext))
        .when(accountUid, linkedinProfileUrl, *, *)
        .returns(Future.successful(jobUid))

      // Create a test instance with our mocks
      val testLinkedinSettingService = new LinkedinSettingService(
        linkedinSettingDAO = linkedinSettingDAO,
        taskService = taskService, // Use the real taskService from DI
        organizationService = organizationService,
        accountService = accountService,
        emailNotificationService = emailNotificationService,
        planLimitService = planLimitService,
        phantomBusterProxyService = phantomBusterProxyService,
        brightDataService = brightDataService,
        accountOrgBillingRelatedService = accountOrgBillingRelatedService,
        mqExtractLinkedinConnectionResults = mqExtractLinkedinConnectionResults,
        linkedinConnectionsDAO = linkedinConnectionsDAO,
        srRandomUtils = srRandomUtils,
        srRedisSimpleLockServiceV2 = srRedisSimpleLockServiceV2,
        captainDataService = captainDataService,
        mqCaptainDataConversationExtractionPublisher = mqCaptainDataConversationExtractionPublisher,
        mqCaptainDataMessageExtractionPublisher = mqCaptainDataMessageExtractionPublisher,
        mqCaptainDataCookieFailurePublisher = mqCaptainDataCookieFailurePublisher,
        srUuidUtils = srUuidUtils,
        captainDataAPI = mockCaptainDataAPI
      )

      // Call the method
      val result = testLinkedinSettingService.handleViewLinkedinProfile(
        accountUid, linkedinProfileUrl, invalidTaskId, teamId
      )

      // The test should either fail with a database error or return 0 rows updated
      result.map { resultJobUid =>
        // Verify that the API call was mocked correctly
        (mockCaptainDataAPI.viewLinkedInProfile(_: CaptainDataAccountUID, _: String)(_: WSClient, _: ExecutionContext))
          .verify(accountUid, linkedinProfileUrl, *, *)
          .once()

        // The job UID should be returned even if the task update fails
        assert(resultJobUid == jobUid)

        // Since we're using an invalid task ID, the database update should have affected 0 rows
        // We can verify this by checking that no task with this ID and job UID exists
        val updatedTask = getTaskFromDatabase(invalidTaskId, teamId)

        // The task should not be found
        assert(updatedTask.isEmpty, s"Task with ID $invalidTaskId should not exist but was found")
        succeed
      }
    }
  }

  // Tests for handleSendLinkedinMessage function
  describe("LinkedinSettingService.handleSendLinkedinMessage") {
    implicit val logger: SRLogger = new SRLogger("[LinkedinSettingServiceSpec] :: ")
    implicit val ec: ExecutionContext = scala.concurrent.ExecutionContext.global
    implicit val ws: WSClient = stub[WSClient]

    it("should successfully send a LinkedIn message and update task status") {
      // Create a unique prospect for this test
      val testProspect = createUniqueProspect()

      // Create a NewTask object using the campaign and prospect data
      val newTask = NewTask(
        campaign_id = Some(campaignData.createCampaign.id),
        campaign_name = Some(campaignData.createCampaign.name),
        step_id = Some(campaignData.addStep.step_id),
        step_label = campaignData.addStep.label,
        created_via = TaskCreatedVia.Scheduler,
        is_opening_step = Some(true),
        task_type = TaskType.AutoLinkedinMessage,
        is_auto_task = true,
        task_data = TaskData.AutoLinkedinMessage(
          body = "Hello, I'd like to discuss a business opportunity."
        ),
        status = TaskStatus.Due(
          due_at = DateTime.now().plusHours(1)
        ),
        assignee_id = Some(accountId.id),
        prospect_id = Some(testProspect.prospect_data.prospect_id),
        priority = TaskPriority.Normal,
        note = Some("Test note")
      )

      // Create the task using TaskService
      val taskIdFuture = taskService.createTask(
        task_data = newTask,
        accountId = accountId.id,
        teamId = teamId.id
      ).map {
        case Right(taskId) => taskId
        case Left(error) => fail(s"Failed to create task: $error")
      }

      // Wait for the task to be created
      val taskId = Await.result(taskIdFuture, 5.seconds)

      // Test data
      val accountUid = CaptainDataAccountUID("test-account-uid")
      val linkedinProfileUrl = "https://linkedin.com/in/test-profile"
      val message = "Hello, I'd like to discuss a business opportunity."
      val jobUid = CaptainDataJobUID("test-job-uid")

      // Set up mocks to simulate a successful API call
      (mockCaptainDataAPI.sendLinkedInMessage(_: CaptainDataAccountUID, _: String, _: String)(_: WSClient, _: ExecutionContext))
        .when(accountUid, linkedinProfileUrl, message, *, *)
        .returns(Future.successful(jobUid))

      // Create a test instance with our mocks
      val testLinkedinSettingService = new LinkedinSettingService(
        linkedinSettingDAO = linkedinSettingDAO,
        taskService = taskService, // Use the real taskService from DI
        organizationService = organizationService,
        accountService = accountService,
        emailNotificationService = emailNotificationService,
        planLimitService = planLimitService,
        phantomBusterProxyService = phantomBusterProxyService,
        brightDataService = brightDataService,
        accountOrgBillingRelatedService = accountOrgBillingRelatedService,
        mqExtractLinkedinConnectionResults = mqExtractLinkedinConnectionResults,
        linkedinConnectionsDAO = linkedinConnectionsDAO,
        srRandomUtils = srRandomUtils,
        srRedisSimpleLockServiceV2 = srRedisSimpleLockServiceV2,
        captainDataService = captainDataService,
        mqCaptainDataConversationExtractionPublisher = mqCaptainDataConversationExtractionPublisher,
        mqCaptainDataMessageExtractionPublisher = mqCaptainDataMessageExtractionPublisher,
        mqCaptainDataCookieFailurePublisher = mqCaptainDataCookieFailurePublisher,
        srUuidUtils = srUuidUtils,
        captainDataAPI = mockCaptainDataAPI
      )

      // Call the method
      val result = testLinkedinSettingService.handleSendLinkedinMessage(
        accountUid, linkedinProfileUrl, message, taskId, teamId
      )

      // The result should be a successful Future with the job UID
      result.map { resultJobUid =>
        // The job UID should match what was returned by the mock
        assert(resultJobUid == jobUid, s"Expected job UID $jobUid but got $resultJobUid")

        // Verify that the API call was mocked correctly
        (mockCaptainDataAPI.sendLinkedInMessage(_: CaptainDataAccountUID, _: String, _: String)(_: WSClient, _: ExecutionContext))
          .verify(accountUid, linkedinProfileUrl, message, *, *)
          .once()

        // Verify the database state - the task should have been updated with the job UID
        // and the status should be set to INITIATED
        val updatedTask = getTaskFromDatabase(taskId, teamId)

        // The task should exist and have the correct job UID and status
        assert(updatedTask.isDefined, "Task not found in database")
        updatedTask.foreach { case (id, jobUidOpt, statusOpt, attemptsOpt) =>
          assert(id == taskId, s"Task ID mismatch: expected $taskId, got $id")
          assert(jobUidOpt.contains(jobUid.toString), s"Job UID mismatch: expected ${jobUid.toString}, got $jobUidOpt")
          assert(statusOpt.contains(CaptainDataWorkflowStatus.INITIATED.toString), s"Status mismatch: expected ${CaptainDataWorkflowStatus.INITIATED.toString}, got $statusOpt")
          assert(attemptsOpt.getOrElse(0) > 0, s"Execution attempts should be > 0, got ${attemptsOpt.getOrElse(0)}")
        }

        succeed
      }
    }

    it("should handle errors when sending a LinkedIn message fails") {
      // Create a unique prospect for this test
      val testProspect = createUniqueProspect()

      // Create a NewTask object using the campaign and prospect data
      val newTask = NewTask(
        campaign_id = Some(campaignData.createCampaign.id),
        campaign_name = Some(campaignData.createCampaign.name),
        step_id = Some(campaignData.addStep.step_id),
        step_label = campaignData.addStep.label,
        created_via = TaskCreatedVia.Scheduler,
        is_opening_step = Some(true),
        task_type = TaskType.AutoLinkedinMessage,
        is_auto_task = true,
        task_data = TaskData.AutoLinkedinMessage(
          body = "Hello, I'd like to discuss a business opportunity."
        ),
        status = TaskStatus.Due(
          due_at = DateTime.now().plusHours(1)
        ),
        assignee_id = Some(accountId.id),
        prospect_id = Some(testProspect.prospect_data.prospect_id),
        priority = TaskPriority.Normal,
        note = Some("Test note")
      )

      // Create the task using TaskService
      val taskIdFuture = taskService.createTask(
        task_data = newTask,
        accountId = accountId.id,
        teamId = teamId.id
      ).map {
        case Right(taskId) => taskId
        case Left(error) => fail(s"Failed to create task: $error")
      }

      // Wait for the task to be created
      val taskId = Await.result(taskIdFuture, 5.seconds)

      // Test data
      val accountUid = CaptainDataAccountUID("test-account-uid")
      val linkedinProfileUrl = "https://linkedin.com/in/test-profile"
      val message = "Hello, I'd like to discuss a business opportunity."
      val testException = new RuntimeException("API call failed")

      // Set up mocks to simulate failure
      (mockCaptainDataAPI.sendLinkedInMessage(_: CaptainDataAccountUID, _: String, _: String)(_: WSClient, _: ExecutionContext))
        .when(accountUid, linkedinProfileUrl, message, *, *)
        .returns(Future.failed(testException))

      // Create a test instance with our mocks
      val testLinkedinSettingService = new LinkedinSettingService(
        linkedinSettingDAO = linkedinSettingDAO,
        taskService = taskService, // Use the real taskService from DI
        organizationService = organizationService,
        accountService = accountService,
        emailNotificationService = emailNotificationService,
        planLimitService = planLimitService,
        phantomBusterProxyService = phantomBusterProxyService,
        brightDataService = brightDataService,
        accountOrgBillingRelatedService = accountOrgBillingRelatedService,
        mqExtractLinkedinConnectionResults = mqExtractLinkedinConnectionResults,
        linkedinConnectionsDAO = linkedinConnectionsDAO,
        srRandomUtils = srRandomUtils,
        srRedisSimpleLockServiceV2 = srRedisSimpleLockServiceV2,
        captainDataService = captainDataService,
        mqCaptainDataConversationExtractionPublisher = mqCaptainDataConversationExtractionPublisher,
        mqCaptainDataMessageExtractionPublisher = mqCaptainDataMessageExtractionPublisher,
        mqCaptainDataCookieFailurePublisher = mqCaptainDataCookieFailurePublisher,
        srUuidUtils = srUuidUtils,
        captainDataAPI = mockCaptainDataAPI
      )

      // Call the method
      val result = testLinkedinSettingService.handleSendLinkedinMessage(
        accountUid, linkedinProfileUrl, message, taskId, teamId
      )

      // The result should be a failed Future with the test exception
      recoverToSucceededIf[RuntimeException] {
        result
      }.map { _ =>
        // Verify that the mocked method was called with the expected parameters
        (mockCaptainDataAPI.sendLinkedInMessage(_: CaptainDataAccountUID, _: String, _: String)(_: WSClient, _: ExecutionContext))
          .verify(accountUid, linkedinProfileUrl, message, *, *)
          .once()

        // Since the API call fails, the task status update should not happen
        // We don't need to verify this as we're using the real taskService

        succeed
      }
    }

    it("should handle invalid task ID scenario for sending a message") {
      // Test data with an invalid task ID that doesn't exist in the database
      val accountUid = CaptainDataAccountUID("test-account-uid")
      val linkedinProfileUrl = "https://linkedin.com/in/test-profile"
      val message = "Hello, I'd like to discuss a business opportunity."
      val invalidTaskId = "non-existent-task-id" // This task ID doesn't exist in the database
      val jobUid = CaptainDataJobUID("test-job-uid")

      // Set up mocks for the API call
      (mockCaptainDataAPI.sendLinkedInMessage(_: CaptainDataAccountUID, _: String, _: String)(_: WSClient, _: ExecutionContext))
        .when(accountUid, linkedinProfileUrl, message, *, *)
        .returns(Future.successful(jobUid))

      // Create a test instance with our mocks
      val testLinkedinSettingService = new LinkedinSettingService(
        linkedinSettingDAO = linkedinSettingDAO,
        taskService = taskService, // Use the real taskService from DI
        organizationService = organizationService,
        accountService = accountService,
        emailNotificationService = emailNotificationService,
        planLimitService = planLimitService,
        phantomBusterProxyService = phantomBusterProxyService,
        brightDataService = brightDataService,
        accountOrgBillingRelatedService = accountOrgBillingRelatedService,
        mqExtractLinkedinConnectionResults = mqExtractLinkedinConnectionResults,
        srRandomUtils = srRandomUtils,
        srRedisSimpleLockServiceV2 = srRedisSimpleLockServiceV2,
        linkedinConnectionsDAO = linkedinConnectionsDAO,
        captainDataService = captainDataService,
        mqCaptainDataConversationExtractionPublisher = mqCaptainDataConversationExtractionPublisher,
        mqCaptainDataMessageExtractionPublisher = mqCaptainDataMessageExtractionPublisher,
        mqCaptainDataCookieFailurePublisher = mqCaptainDataCookieFailurePublisher,
        srUuidUtils = srUuidUtils,
        captainDataAPI = mockCaptainDataAPI
      )

      // Call the method
      val result = testLinkedinSettingService.handleSendLinkedinMessage(
        accountUid, linkedinProfileUrl, message, invalidTaskId, teamId
      )

      // The test should either fail with a database error or return 0 rows updated
      result.map { resultJobUid =>
        // Verify that the API call was mocked correctly
        (mockCaptainDataAPI.sendLinkedInMessage(_: CaptainDataAccountUID, _: String, _: String)(_: WSClient, _: ExecutionContext))
          .verify(accountUid, linkedinProfileUrl, message, *, *)
          .once()

        // The job UID should be returned even if the task update fails
        assert(resultJobUid == jobUid)

        // Since we're using an invalid task ID, the database update should have affected 0 rows
        // We can verify this by checking that no task with this ID and job UID exists
        val updatedTask = getTaskFromDatabase(invalidTaskId, teamId)

        // The task should not be found
        assert(updatedTask.isEmpty, s"Task with ID $invalidTaskId should not exist but was found")
        succeed
      }
    }
  }
  // Tests for getTimeZone function
  describe("LinkedinSettingService.getTimeZone") {
    implicit val logger: SRLogger = new SRLogger("[LinkedinSettingServiceSpec] :: ")
    implicit val ec: ExecutionContext = scala.concurrent.ExecutionContext.global

    it("should pass for US country code with America/Chicago timezone") {

      val result = LinkedinSettingService.getVaildTimeZoneForLinkedinSetting(
        iso_country_code = "US",
        account_timezone = Some("America/Chicago")
      )

      result match {
        case Success(timezone) =>
          // For US, getTimezoneForCountry returns None, so it should validate and use the account_timezone
          // Since US maps to America/Los_Angeles and account_timezone is also America/Los_Angeles,
          // isCountryAndTimezoneValid should return true and use the account_timezone
          timezone shouldEqual "America/Chicago"
        case Failure(exception) =>
          fail(s"Expected Success but got Failure: ${exception.getMessage}")
      }
    }

    it("should fail for US country code with Asia/Kolkata timezone") {

      val result = LinkedinSettingService.getVaildTimeZoneForLinkedinSetting(
        iso_country_code = "US",
        account_timezone = Some("Asia/Kolkata")
      )

      result match {
        case Success(timezone) =>
          fail(s"Expected Failure but got Success with timezone: $timezone")
        case Failure(exception) =>
          exception.getMessage should include("Timezone for country: US is not valid")
      }
    }

    it("should pass for GB country code and return Europe/London regardless of account_timezone") {

      val result = LinkedinSettingService.getVaildTimeZoneForLinkedinSetting(
        iso_country_code = "GB",
        account_timezone = Some("Asia/Kolkata")
      )

      result match {
        case Success(timezone) =>
          // For GB, getTimezoneForCountry returns Some("Europe/London"), so it should return that
          // regardless of account_timezone
          timezone shouldEqual "Europe/London"
        case Failure(exception) =>
          fail(s"Expected Success but got Failure: ${exception.getMessage}")
      }
    }
  }

  // Helper method to get task details from database
  private def getTaskFromDatabase(taskId: String, teamId: TeamId): Option[(String, Option[String], Option[String], Option[Int])] = {
    DB.readOnly { implicit session =>
      sql"""
           SELECT task_id, cd_job_uid, cd_job_status, execution_attempts
           FROM tasks
           WHERE task_id = ${taskId}
           AND team_id = ${teamId.id}
           """
        .map(rs => (
          rs.string("task_id"),
          rs.stringOpt("cd_job_uid"),
          rs.stringOpt("cd_job_status"),
          rs.intOpt("execution_attempts")
        ))
        .single
        .apply()
    }
  }
}
