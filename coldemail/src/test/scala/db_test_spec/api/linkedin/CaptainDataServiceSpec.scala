package db_test_spec.api.linkedin

import db_test_spec.api.DbTestingBeforeAllAndAfterAll
import org.scalamock.scalatest.AsyncMockFactory
import org.scalatest.funspec.AsyncFunSpec
import org.scalatest.matchers.should.Matchers
import api.AppConfig
import api.phantombuster.CaptainDataService
import org.joda.time.DateTime
import utils.SRLogger

import scala.concurrent.ExecutionContext

class CaptainDataServiceSpec extends AsyncFunSpec with DbTestingBeforeAllAndAfterAll with AsyncMockFactory with Matchers{


  // Tests for CaptainDataService.isConversationScrapingRequestValid
  describe("CaptainDataService.isConversationScrapingRequestValid") {
    implicit val logger: SRLogger = new SRLogger("[LinkedinSettingServiceSpec] :: ")
    implicit val ec: ExecutionContext = scala.concurrent.ExecutionContext.global

    it("should return true when lastScrapedAt is None") {
      val conversationsProcessingStartedAt = DateTime.now()
      val lastScrapedAt = None

      val result = CaptainDataService.isConversationScrapingRequestValid(
        lastScrapedAt = lastScrapedAt,
        conversationsProcessingStartedAt = conversationsProcessingStartedAt
      )

      assert(result === true)
    }

    it("should return true when lastScrapedAt is recent enough") {
      val conversationsProcessingStartedAt = DateTime.now()
      // Set lastScrapedAt to be just a few minutes before the threshold
      val lastScrapedAt = Some(conversationsProcessingStartedAt.minusMinutes(
        AppConfig.CaptainData.RetrieveLinkedinConversations.repetitiveIntervalInMinutes +
          AppConfig.CaptainData.RetrieveLinkedinConversations.deltaInMinutes - 5
      ))

      val result = CaptainDataService.isConversationScrapingRequestValid(
        lastScrapedAt = lastScrapedAt,
        conversationsProcessingStartedAt = conversationsProcessingStartedAt
      )

      assert(result === true)
    }

    it("should return false when lastScrapedAt is too old") {
      val conversationsProcessingStartedAt = DateTime.now()
      // Set lastScrapedAt to be beyond the threshold
      val lastScrapedAt = Some(conversationsProcessingStartedAt.minusMinutes(
        AppConfig.CaptainData.RetrieveLinkedinConversations.repetitiveIntervalInMinutes +
          AppConfig.CaptainData.RetrieveLinkedinConversations.deltaInMinutes + 10
      ))

      val result = CaptainDataService.isConversationScrapingRequestValid(
        lastScrapedAt = lastScrapedAt,
        conversationsProcessingStartedAt = conversationsProcessingStartedAt
      )

      assert(result === false)
    }
  }


}
