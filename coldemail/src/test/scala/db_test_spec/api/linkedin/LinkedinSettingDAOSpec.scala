package db_test_spec.api.linkedin

import api.accounts.TeamId
import api.accounts.models.AccountId
import api.linkedin.models.{LinkedinAccountAndLastInboxScrapeTime, LinkedinAccountStatus, LinkedinSettingId}
import api.phantombuster.ExecuteAutoLinkedinTasksError
import db_test_spec.api.{DbTestingBeforeAllAndAfterAll, InitialData}
import db_test_spec.api.accounts.fixtures.NewAccountAndEmailSettingData
import db_test_spec.api.linkedin.fixtures.{DefaultLinkedinSettingParametersFixtures, LinkedInSettingFixtureForIntegrationTest, LinkedinAccount}
import io.sr.billing_common.models.PlanType
import org.joda.time.DateTime
import org.scalatest.ParallelTestExecution
import scalikejdbc.{DB, scalikejdbcSQLInterpolationImplicitDef}
import utils.SRLogger

import scala.concurrent.Future

class LinkedinSettingDAOSpec extends DbTestingBeforeAllAndAfterAll with ParallelTestExecution {

  given logger: SRLogger = new SRLogger("[LinkedinSettingDAOSpec] :: ")

  describe("Testing LinkedinSettingDAO.fetchLinkedinAccountsToExecuteTasksOrScrapeMessages") {
    it("should fetch linkedin accounts that are eligible for task execution or message scraping") {

      for {
        initialData <- Future.fromTry(NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData())

        // Create a LinkedIn account with the necessary settings for the test
        linkedinAccount <- Future {
          val linkedinAccountSettings = DefaultLinkedinSettingParametersFixtures.defaultLinkedInAccountSettings(
            accountId = AccountId(initialData.account.internal_id)
          )

          val account = LinkedInSettingFixtureForIntegrationTest.createLinkedInSetting(
            accountId = AccountId(initialData.account.internal_id),
            teamId = TeamId(initialData.account.teams.head.team_id),
            linkedInAccountSettings = Some(linkedinAccountSettings)
          ).get

          // Update the LinkedIn account to match the criteria in the query
          updateLinkedinStatus(
            account = account,
            initialData = initialData
          )

          account
        }

        // Execute the function under test and verify results
        result <- Future {
          val accounts = linkedinSettingDAO.fetchLinkedinAccountsToExecuteTasksOrScrapeMessages().get

          // Verify the results
          assert(accounts.nonEmpty, "Should return at least one LinkedIn account")

          // Check if our created account is in the results
          val ourAccount = accounts.find(_.id.id == linkedinAccount.id.id)
          assert(ourAccount.isDefined, "Our created LinkedIn account should be in the results")

          // Verify the account properties
          ourAccount.foreach { account =>
            assert(account.id == linkedinAccount.id, "Account ID should match")
            assert(account.teamId.id == initialData.account.teams.head.team_id, "Team ID should match")
            assert(account.orgId.id == initialData.account.org.id, "Org ID should match")
          }

          succeed
        }
      } yield result
    }

    it("should not fetch linkedin accounts with invalid session cookie error") {
      // Create test data

      for {
        initialData <- Future.fromTry(NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData())

        // Create a LinkedIn account with the necessary settings for the test
        linkedinAccount <- Future {
          val linkedinAccountSettings = DefaultLinkedinSettingParametersFixtures.defaultLinkedInAccountSettings(
            accountId = AccountId(initialData.account.internal_id)
          )

          val account = LinkedInSettingFixtureForIntegrationTest.createLinkedInSetting(
            accountId = AccountId(initialData.account.internal_id),
            teamId = TeamId(initialData.account.teams.head.team_id),
            linkedInAccountSettings = Some(linkedinAccountSettings)
          ).get

          // Update the LinkedIn account to match the criteria but with invalid session cookie error
          updateLinkedinStatusWithInvalidSessionCookie(
            account = account,
            initialData = initialData
          )

          account
        }

        // Execute the function under test and verify results
        result <- Future {
          val accounts = linkedinSettingDAO.fetchLinkedinAccountsToExecuteTasksOrScrapeMessages().get

          // Verify that our account with invalid session cookie is not in the results
          val ourAccount = accounts.find(_.id.id == linkedinAccount.id.id)
          assert(ourAccount.isEmpty, "Account with invalid session cookie should not be in the results")

          succeed
        }
      } yield result
    }

    it("should not fetch linkedin accounts from inactive organizations") {

      for {
        initialData <- Future.fromTry(NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData())

        // Create a LinkedIn account with the necessary settings for the test
        linkedinAccount <- Future {
          val linkedinAccountSettings = DefaultLinkedinSettingParametersFixtures.defaultLinkedInAccountSettings(
            accountId = AccountId(initialData.account.internal_id)
          )

          val account = LinkedInSettingFixtureForIntegrationTest.createLinkedInSetting(
            accountId = AccountId(initialData.account.internal_id),
            teamId = TeamId(initialData.account.teams.head.team_id),
            linkedInAccountSettings = Some(linkedinAccountSettings)
          ).get

          // Update the LinkedIn account to match the criteria
          updateLinkedinStatusWithNeedToAssignProxy(
            account = account,
            initialData = initialData
          )

          account
        }

        // Execute the function under test and verify results
        result <- Future {
          val accounts = linkedinSettingDAO.fetchLinkedinAccountsToExecuteTasksOrScrapeMessages().get

          // Verify that our account from inactive organization is not in the results
          val ourAccount = accounts.find(_.id.id == linkedinAccount.id.id)
          assert(ourAccount.isEmpty, "Account from inactive organization should not be in the results")

          succeed
        }
      } yield result
    }

    it("should fetch linkedin accounts with next_task_to_be_executed_at in the past") {

      for {
        initialData <- Future.fromTry(NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData())

        // Create a LinkedIn account with the necessary settings for the test
        linkedinAccount <- Future {
          val linkedinAccountSettings = DefaultLinkedinSettingParametersFixtures.defaultLinkedInAccountSettings(
            accountId = AccountId(initialData.account.internal_id)
          )

          val account = LinkedInSettingFixtureForIntegrationTest.createLinkedInSetting(
            accountId = AccountId(initialData.account.internal_id),
            teamId = TeamId(initialData.account.teams.head.team_id),
            linkedInAccountSettings = Some(linkedinAccountSettings)
          ).get

          // Update the LinkedIn account to have next_task_to_be_executed_at in the past
          val pastTime = DateTime.now().minusHours(1)
          DB.autoCommit { implicit session =>
            sql"""
                 UPDATE linkedin_settings
                 SET
                    next_task_to_be_executed_at = $pastTime,
                    captain_data_account_id = 'test_captain_data_account_id',
                    status = ${LinkedinAccountStatus.Active.toString},
                    last_inbox_scraping_done_at = now() - interval '10 minutes'
                 WHERE id = ${account.id.id}
                 AND team_id = ${initialData.account.teams.head.team_id}
                 ;
                 """
              .update
              .apply()
          }

          account
        }

        // Execute the function under test and verify results
        result <- Future {
          val accounts = linkedinSettingDAO.fetchLinkedinAccountsToExecuteTasksOrScrapeMessages().get

          // Verify the results
          assert(accounts.nonEmpty, "Should return at least one LinkedIn account")

          // Check if our created account is in the results
          val ourAccount = accounts.find(_.id.id == linkedinAccount.id.id)
          assert(ourAccount.isDefined, "Our LinkedIn account with past execution time should be in the results")

          succeed
        }
      } yield result
    }

    it("should not fetch linkedin accounts with next_task_to_be_executed_at in the future") {
      // Create test data
      for {
        initialData <- Future.fromTry(NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData())

        // Create a LinkedIn account with the necessary settings for the test
        linkedinAccount <- Future {
          val linkedinAccountSettings = DefaultLinkedinSettingParametersFixtures.defaultLinkedInAccountSettings(
            accountId = AccountId(initialData.account.internal_id)
          )

          val account = LinkedInSettingFixtureForIntegrationTest.createLinkedInSetting(
            accountId = AccountId(initialData.account.internal_id),
            teamId = TeamId(initialData.account.teams.head.team_id),
            linkedInAccountSettings = Some(linkedinAccountSettings)
          ).get

          // Update the LinkedIn account to have next_task_to_be_executed_at in the future
          val futureTime = DateTime.now().plusMinutes(30)
          DB.autoCommit { implicit session =>
            sql"""
                 UPDATE linkedin_settings
                 SET
                    next_task_to_be_executed_at = $futureTime,
                    captain_data_account_id = 'test_captain_data_account_id',
                    status = ${LinkedinAccountStatus.Active.toString},
                    last_inbox_scraping_done_at = now() - interval '10 minutes'
                 WHERE id = ${account.id.id}
                 AND team_id = ${initialData.account.teams.head.team_id}
                 ;
                 """
              .update
              .apply()
          }

          account
        }

        // Execute the function under test and verify results
        result <- Future {
          val accounts = linkedinSettingDAO.fetchLinkedinAccountsToExecuteTasksOrScrapeMessages().get

          // Verify that our account with future execution time is not in the results
          val ourAccount = accounts.find(_.id.id == linkedinAccount.id.id)
          assert(ourAccount.isEmpty, "Account with future execution time should not be in the results")

          succeed
        }
      } yield result
    }

    it("should not fetch linkedin accounts with old last_inbox_scraping_done_at") {
      // Create test data
      for {
        initialData <- Future.fromTry(NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData())

        // Create a LinkedIn account with the necessary settings for the test
        linkedinAccount <- Future {
          val linkedinAccountSettings = DefaultLinkedinSettingParametersFixtures.defaultLinkedInAccountSettings(
            accountId = AccountId(initialData.account.internal_id)
          )

          val account = LinkedInSettingFixtureForIntegrationTest.createLinkedInSetting(
            accountId = AccountId(initialData.account.internal_id),
            teamId = TeamId(initialData.account.teams.head.team_id),
            linkedInAccountSettings = Some(linkedinAccountSettings)
          ).get

          // Update the LinkedIn account with old last_inbox_scraping_done_at (older than extraction interval)
          val oldScrapingTime = DateTime.now().minusHours(2) // Assuming extraction interval is less than 2 hours
          DB.autoCommit { implicit session =>
            sql"""
                 UPDATE linkedin_settings
                 SET
                    next_task_to_be_executed_at = NULL,
                    captain_data_account_id = 'test_captain_data_account_id',
                    status = ${LinkedinAccountStatus.Active.toString},
                    last_inbox_scraping_done_at = $oldScrapingTime
                 WHERE id = ${account.id.id}
                 AND team_id = ${initialData.account.teams.head.team_id}
                 ;
                 """
              .update
              .apply()
          }

          account
        }

        // Execute the function under test and verify results
        result <- Future {
          val accounts = linkedinSettingDAO.fetchLinkedinAccountsToExecuteTasksOrScrapeMessages().get

          // Verify that our account with old scraping time is not in the results
          val ourAccount = accounts.find(_.id.id == linkedinAccount.id.id)
          assert(ourAccount.isEmpty, "Account with old last_inbox_scraping_done_at should not be in the results")

          succeed
        }
      } yield result
    }
  }

  private def updateLinkedinStatus(
                                   account: LinkedinAccount,
                                   initialData: InitialData
                                  ) = {
    // Update LinkedIn account with required fields
    DB.autoCommit { implicit session =>
      sql"""
                 UPDATE linkedin_settings
                 SET
                    next_task_to_be_executed_at = NULL,
                    captain_data_account_id = 'test_captain_data_account_id',
                    status = ${LinkedinAccountStatus.Active.toString},
                    last_inbox_scraping_done_at = now() - interval '10 minutes'
                 WHERE id = ${account.id.id}
                 AND team_id = ${initialData.account.teams.head.team_id}
                 ;
                 """
        .update
        .apply()
    }
  }

  private def updateLinkedinStatusWithInvalidSessionCookie(
                                   account: LinkedinAccount,
                                   initialData: InitialData
                                 ): Unit = {
    DB.autoCommit { implicit session =>
      sql"""
           UPDATE linkedin_settings
           SET
              next_task_to_be_executed_at = NULL,
              captain_data_account_id = 'test_captain_data_account_id',
              status = ${LinkedinAccountStatus.Active.toString},
              last_inbox_scrape_result = ${ExecuteAutoLinkedinTasksError.InvalidOrExpiredLinkedinSessionCookie.toString},
              last_inbox_scraping_done_at = now() - interval '10 minutes'
           WHERE id = ${account.id.id}
           AND team_id = ${initialData.account.teams.head.team_id}
           ;
           """
        .update
        .apply()
    }
  }

  private def updateLinkedinStatusWithNeedToAssignProxy(
                                   account: LinkedinAccount,
                                   initialData: InitialData
                                 ): Unit = {
    DB.autoCommit { implicit session =>
      sql"""
           UPDATE linkedin_settings
           SET
              next_task_to_be_executed_at = NULL,
              captain_data_account_id = 'test_captain_data_account_id',
              status = ${LinkedinAccountStatus.NeedToAssignProxy.toString},
              last_inbox_scraping_done_at = now() - interval '10 minutes'
           WHERE id = ${account.id.id}
           AND team_id = ${initialData.account.teams.head.team_id}
           ;
           """
        .update
        .apply()
    }
  }
}