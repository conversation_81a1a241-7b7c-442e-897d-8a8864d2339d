package db_test_spec.api.accounts

import api.accounts.{Account, AccountWithoutOrgInternal, ReportCampaignStats, ReportTemplateStats, TeamAccount, TeamId}
import api.accounts.models.{AccountId, OrgId}
import api.prospects.InferredQueryTimeline
import api.team_inbox.service.ReplySentimentUuid
import db_test_spec.api.accounts.fixtures.NewAccountAndEmailSettingData
import db_test_spec.api.{DbTestingBeforeAllAndAfterAll, InitialData, SRSetupAndDeleteFixtures}
import org.joda.time.DateTime
import org.scalatest.ParallelTestExecution
import scalikejdbc.{DB, scalikejdbcSQLInterpolationImplicitDef}
import utils.SRLogger
import utils.cronjobs.SubordinatesByTeamIdForReportGeneration

import scala.util.{Failure, Success, Try}

class AccountDAOSpec extends DbTestingBeforeAllAndAfterAll  {



  describe("AccountDAO") {
    it("should get Account") {
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
      val account: Try[Option[AccountWithoutOrgInternal]] = accountDAO.findTry(id = AccountId(initialData.account.internal_id))

      assert(account.isSuccess)
      assert(account.get.isDefined)
      assert(account.get.get.id == initialData.account.id)
    }

    it("should get TeamsAccount") {
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
      val teamAccounts: Try[Seq[TeamAccount]] = accountDAO.getTeamsListing(
        accountId = AccountId(initialData.account.internal_id),
        queryTimeline = InferredQueryTimeline.Range.Before(DateTime.now()),
        active = None
      )


      assert(teamAccounts.isSuccess)
      assert(teamAccounts.get.nonEmpty)
      assert(teamAccounts.get.length == initialData.account.teams.length)
    }
  }


  describe("AccountService") {
    it("should get Account") {
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
      val account: Try[Account] = accountService.find(id = initialData.account.internal_id)

      assert(account.isSuccess)
      assert(account.get.id == initialData.account.id)
    }
  }

  describe("getEmailSummaryAccountIdsforReport") {
    it("should return sucess") {
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val res: Try[Seq[AccountId]] = accountService.getEmailSummaryAccountIdsforReport("weekly")

      assert(res.isSuccess)

    }
  }

  describe("Test findIfAnyCampaignInOrgHasDripCampaign") {

    it("should return findIfAnyCampaignInOrgHasDripCampaign") {
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val res: Try[Boolean]= accountDAO.findIfAnyCampaignInOrgHasDripCampaign(
        orgId = OrgId(initialData.account.org.id)
      )

      assert(res.isSuccess)
    }

  }

  describe("getTopCampaignStatsForReportV2") {
    it("should return success") {
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val res: Try[Seq[ReportCampaignStats]] = accountDAO.getTopCampaignStatsForReportV2(
        subordinates = Seq(SubordinatesByTeamIdForReportGeneration(
          teamId = initialData.emailSetting.get.team_id,
          accessAccountIds = Seq(AccountId(initialData.account.internal_id)),
          hasAccessToAllTeamMemberAccounts = true,
        ))
      )

      assert(res.isSuccess)
    }
  }

  describe("getTopListStatsForReportV2") {
    it("should give success") {

      val result = accountDAO.getTopListStatsForReportV2(
        subordinates = Seq(
          SubordinatesByTeamIdForReportGeneration(
            teamId = TeamId(1),
            accessAccountIds = Seq(
              AccountId(2)
            ),
            hasAccessToAllTeamMemberAccounts = true
          )
        )
      )

      println(s"$result")
      assert(result.isSuccess)

    }

  }


  describe("getAllTeamAdminReportCampaignStats") {
    it("should give success") {

      val result = accountDAO.getAllTeamAdminReportCampaignStats(
        team_member_aids = Seq(
          AccountId(2)
        ),
        orgId = OrgId(1)
      )
      println(s"$result")

      assert(result.isSuccess)

    }

  }

  describe("getTeamSummary") {
    it("should give success") {

      val initialData = SRSetupAndDeleteFixtures.createInitialData().get
      val result = accountDAO.getTeamSummary(
        orgId = OrgId(initialData.account.org.id),
        tid = initialData.emailSetting.get.team_id,
        aid = AccountId(initialData.account.internal_id),
        from = Some(DateTime.now().minusDays(1)),
        till = Some(DateTime.now()),
        positive_reply_sentiments = List(ReplySentimentUuid("dklsfnavr"))
      )
      println(s"$result")

      assert(result.isSuccess)

    }

  }
  describe("getAgencyTeamLevelStats") {
    it("should give success") {

      val result = accountDAO.getAgencyTeamLevelStats(
        orgId = OrgId(1),
        tids= Seq(TeamId(2)),
        aid = AccountId(1),
        from = Some(DateTime.now().minusDays(1)),
        till = Some(DateTime.now()),
        positive_reply_sentiments = List(ReplySentimentUuid("dklsfnavr"))
      )
      println(s"$result")

      assert(result.isSuccess)

    }

  }
  
  
  describe("getTopTemplateStatsForReport") {
      it("should give success"){
          val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

          val res: Try[Seq[ReportTemplateStats]] = accountDAO.getTopTemplateStatsForReport(
              subordinates = Seq(SubordinatesByTeamIdForReportGeneration(
                  teamId = initialData.emailSetting.get.team_id,
                  accessAccountIds = Seq(AccountId(initialData.account.internal_id)),
                  hasAccessToAllTeamMemberAccounts = true,
              ))
          )

          assert(res.isSuccess)
      }
  }

  describe("Should be able to find next_billing_date for org") {

    it("should get Account's next billing date") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
      val org_id = initialData.account.org.id
      val accountTry: Try[Account] = accountService.find(id = initialData.account.internal_id)

      accountTry match {

        case Failure(exception) =>

        case Success(value) =>

          assert(value.org.plan.next_billing_date.nonEmpty)

      }

      assert(accountTry.isSuccess)
      assert(accountTry.get.id == initialData.account.id)
    }

  }
}
