package db_test_spec.api.accounts.service

import api.accounts.models.OrgId
import db_test_spec.api.accounts.dao.OrganizationTestDAO
import db_test_spec.api.accounts.fixtures.NewAccountAndEmailSettingData
import db_test_spec.api.{DbTestingBeforeAllAndAfterAll, InitialData}
import org.scalatest.{Ignore, ParallelTestExecution}

import scala.util.{Failure, Success}

@Ignore
class OrganizationServiceSpec extends DbTestingBeforeAllAndAfterAll {

  describe("Test subtractLeadFinderCredits") {

    it("should return error if org id is invalid") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val orgId = OrgId(id = initialData.account.org.id)

      OrganizationTestDAO.addLeadFinderCredits(
        orgId = orgId,
        leadFinderCredits = 4000,
        leadFinderBasePlanCredits = 2500,
      ).get

      val invalidOrgId = OrgId(id = *********)

      organizationService.getConsumedLeadFinderCredits(
        orgId = invalidOrgId,
        creditsToBeConsumed = 500,
      ).flatMap { toBeConsumedLeadFinderCredits =>

        organizationService.subtractLeadFinderCredits(
          orgId = invalidOrgId,
          toBeConsumedLeadFinderCredits = toBeConsumedLeadFinderCredits
        )

      } match {

        case Failure(exception) =>

          println(exception)

          assert(true)

        case Success(_) =>

          assert(false)


      }

    }

    it("should first subtract all usedCredits from base plan lead finder credits") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val orgId = OrgId(id = initialData.account.org.id)

      val leadFinderCredits = 4000

      val leadFinderBasePlanCredits = 2500

      OrganizationTestDAO.addLeadFinderCredits(
        orgId = orgId,
        leadFinderCredits = leadFinderCredits,
        leadFinderBasePlanCredits = leadFinderBasePlanCredits
      ).get

      val usedCredits = 700

      organizationService.getConsumedLeadFinderCredits(
        orgId = orgId,
        creditsToBeConsumed = usedCredits,
      ).flatMap { toBeConsumedLeadFinderCredits =>

        organizationService.subtractLeadFinderCredits(
          orgId = orgId,
          toBeConsumedLeadFinderCredits = toBeConsumedLeadFinderCredits
        )

      } match {

        case Failure(exception) =>

          println(exception)

          assert(false)

        case Success(_) =>

          val remainingBasePlanLeadFinderCredits = organizationBillingDAO.getRemainingBasePlanLeadFinderCredits(
            orgId = orgId
          ).get

          val totalRemainingLeadFinderCredits = organizationService.getRemainingLeadFinderCredits(
            orgId = orgId
          ).get

          assert(
            remainingBasePlanLeadFinderCredits == (leadFinderBasePlanCredits - usedCredits) &&
              totalRemainingLeadFinderCredits == (leadFinderCredits + leadFinderBasePlanCredits) - usedCredits
          )

      }

    }

    it("should subtract usedCredits from lead finder credits, if lead usedCredits from base plan are not enough") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val orgId = OrgId(id = initialData.account.org.id)

      val leadFinderCredits = 4000

      val leadFinderBasePlanCredits = 2500

      OrganizationTestDAO.addLeadFinderCredits(
        orgId = orgId,
        leadFinderCredits = leadFinderCredits,
        leadFinderBasePlanCredits = leadFinderBasePlanCredits
      ).get

      val usedCredits = 2700

      organizationService.getConsumedLeadFinderCredits(
        orgId = orgId,
        creditsToBeConsumed = usedCredits,
      ).flatMap { toBeConsumedLeadFinderCredits =>

        organizationService.subtractLeadFinderCredits(
          orgId = orgId,
          toBeConsumedLeadFinderCredits = toBeConsumedLeadFinderCredits
        )

      } match {

        case Failure(exception) =>

          println(exception)

          assert(false)

        case Success(_) =>

          val remainingBasePlanLeadFinderCredits = organizationBillingDAO.getRemainingBasePlanLeadFinderCredits(
            orgId = orgId
          ).get

          val remainingLeadFinderCredits = organizationService.getRemainingLeadFinderCredits(
            orgId = orgId
          ).get

          assert(
            remainingBasePlanLeadFinderCredits == 0 &&
              remainingLeadFinderCredits == (leadFinderCredits + leadFinderBasePlanCredits) - usedCredits
          )

      }

    }

    it("should subtract usedCredits from purchased credits when plan credits are 0") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val orgId = OrgId(id = initialData.account.org.id)

      val leadFinderCredits = 4000

      val leadFinderBasePlanCredits = 0

      OrganizationTestDAO.addLeadFinderCredits(
        orgId = orgId,
        leadFinderCredits = leadFinderCredits,
        leadFinderBasePlanCredits = leadFinderBasePlanCredits
      ).get

      val usedCredits = 2700

      organizationService.getConsumedLeadFinderCredits(
        orgId = orgId,
        creditsToBeConsumed = usedCredits,
      ).flatMap { toBeConsumedLeadFinderCredits =>

        organizationService.subtractLeadFinderCredits(
          orgId = orgId,
          toBeConsumedLeadFinderCredits = toBeConsumedLeadFinderCredits
        )

      } match {

        case Failure(exception) =>

          println(exception)

          assert(false)

        case Success(_) =>

          val remainingBasePlanLeadFinderCredits = organizationBillingDAO.getRemainingBasePlanLeadFinderCredits(
            orgId = orgId
          ).get

          val remainingLeadFinderCredits = organizationService.getRemainingLeadFinderCredits(
            orgId = orgId
          ).get

          assert(
            remainingBasePlanLeadFinderCredits == 0 &&
              remainingLeadFinderCredits == leadFinderCredits - usedCredits
          )
      }

    }

    it("should not make lead finder credits negative") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val orgId = OrgId(id = initialData.account.org.id)

      val leadFinderCredits = 4001

      val leadFinderBasePlanCredits = 2532

      OrganizationTestDAO.addLeadFinderCredits(
        orgId = orgId,
        leadFinderCredits = leadFinderCredits,
        leadFinderBasePlanCredits = leadFinderBasePlanCredits
      ).get

      val usedCredits = 7962

      organizationService.getConsumedLeadFinderCredits(
        orgId = orgId,
        creditsToBeConsumed = usedCredits,
      ).flatMap { toBeConsumedLeadFinderCredits =>

        organizationService.subtractLeadFinderCredits(
          orgId = orgId,
          toBeConsumedLeadFinderCredits = toBeConsumedLeadFinderCredits
        )

      } match {

        case Failure(exception) =>

          println(exception)

          assert(false)

        case Success(_) =>

          val remainingBasePlanLeadFinderCredits = organizationBillingDAO.getRemainingBasePlanLeadFinderCredits(
            orgId = orgId
          ).get

          val remainingLeadFinderCredits = organizationService.getRemainingLeadFinderCredits(
            orgId = orgId
          ).get

          assert(
            remainingBasePlanLeadFinderCredits == 0 &&
              remainingLeadFinderCredits == 0
          )

      }

    }

  }

}
