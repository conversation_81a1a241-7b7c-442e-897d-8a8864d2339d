package db_test_spec.api.accounts

import api.accounts.TeamId
import api.accounts.models.AccountId
import db_test_spec.api.{DbTestingBeforeAllAndAfterAll, InitialData}
import db_test_spec.api.accounts.fixtures.NewAccountAndEmailSettingData

class AccountDAOV2Spec extends DbTestingBeforeAllAndAfterAll {

  describe("getOwnerAndTeamData") {

    it("should give data for account and team data") {

      val result = for {
        accountData <- NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData()

        ownerData <- accountDAOV2.getAccountAndTeamData(
          accountId = accountData.emailSetting.get.owner_id,
          teamId = accountData.emailSetting.get.team_id
        )

      } yield ownerData

      println(s"result ------ $result")
      assert(result.isSuccess)

    }

  }

}
