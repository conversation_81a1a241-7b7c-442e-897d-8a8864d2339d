package db_test_spec.api.accounts.fixtures

import api.AppConfig
import api.accounts.{Account, PermissionMethods, ReplyHandling, TeamId, UpdateTeamConfig}
import api.accounts.models.{AccountId, OrgId}
import api.accounts.sr_api_key_type.models.SRApiKeyType
import api.emails.EmailSetting
import api.prospects.{CreateOrUpdateProspectsResult, ProspectCreateFormData}
import db_test_spec.api.emails.fixtures.DefaultEmailSettingParametersFixtures.defaultEmailSettingForm
import db_test_spec.api.InitialData
import db_test_spec.api.emails.fixtures.EmailSettingFixtureForIntegrationTest
import db_test_spec.api.linkedin.fixtures.{LinkedInSettingFixtureForIntegrationTest, LinkedinAccount}
import db_test_spec.api.prospects.fixtures.ProspectFixtureForIntegrationTest
import db_test_spec.api.whatsapp.DefaultWhatsappSettingParametersFixtures.defaultWhatsappAccountSettings
import db_test_spec.api.whatsapp.{WhatsappAccount, WhatsappSettingFixtureForIntegrationTest}
import db_test_spec.utils.SrRandomTestUtils
import eventframework.ProspectObject
import io.smartreach.esp.api.microsoftOAuth.EmailSettingUpdateAccessToken
import org.joda.time.DateTime
import play.api.libs.json.Json
import utils.SRLogger
import utils.testapp.TestAppTrait

import scala.concurrent.Future
import scala.util.Try

object NewAccountAndWhatsappSettingData extends TestAppTrait{

  def createNewAccountAndWhatsappSettingData(emailNotCompulsoryOrgFlag : Option[Boolean])(using Logger: SRLogger): Try[InitialData] = {

    for {
      account: Account <- AccountFixtureForIntegrationTest.createAccount(emailNotCompulsoryOrgFlag = emailNotCompulsoryOrgFlag).map(_.get)

      head_team_id: Long = account.teams.head.team_id

      whatsappAccount: WhatsappAccount <- WhatsappSettingFixtureForIntegrationTest.createWhatsappSetting(
        accountId = AccountId(account.internal_id),
        teamId = TeamId(account.teams.head.team_id),
        whatsappAccountSettings = Option(defaultWhatsappAccountSettings(accountId = AccountId(account.internal_id)))
      )


      prospectsResult: Seq[ProspectObject] <- ProspectFixtureForIntegrationTest.createUpdateOrAssignProspect(
        accountId = AccountId(account.internal_id),
        teamId = TeamId(account.teams.head.access_members.head.team_id),
        account = account,
        givenProspect = Some(Seq(
          ProspectCreateFormData(
            email = Some("<EMAIL>"),
            first_name = Some("Naruto"),
            last_name = Some("Uzumaki"),
            custom_fields = Json.obj(),
            owner_id= Some(account.internal_id),
            list = None,
            company = None,
            city = None,
            country = None,
            timezone = None,
            created_at = Some(DateTime.now()),
            state = None,
            job_title = None,
            phone = Some(SrRandomTestUtils.getRandomPhoneNumber),
            phone_2 = None,
            phone_3 = None,
            linkedin_url =  None),
          ProspectCreateFormData(
            email = Some("<EMAIL>"),
            first_name = Some("John"),
            last_name = Some("Doe"),
            custom_fields = Json.obj(),
            owner_id= Some(account.internal_id),
            list = None,
            company = None,
            city = None,
            country = None,
            timezone = None,
            created_at = Some(DateTime.now()),
            state = None,
            job_title = None,
            phone = Some(SrRandomTestUtils.getRandomPhoneNumber),
            phone_2 = None,
            phone_3 = None,
            linkedin_url =  None),
          ProspectCreateFormData(
            email = Some("<EMAIL>"),
            first_name = Some("Sam"),
            last_name = Some("Sum"),
            custom_fields = Json.obj(),
            owner_id= Some(account.internal_id),
            list = None,
            company = None,
            city = None,
            country = None,
            timezone = None,
            created_at = Some(DateTime.now()),
            state = None,
            job_title = None,
            phone = Some(SrRandomTestUtils.getRandomPhoneNumber),
            phone_2 = None,
            phone_3 = None,
            linkedin_url =  None)
        )),
        emailNotCompulsoryOrgFlag = emailNotCompulsoryOrgFlag
      )


      role = PermissionMethods.getUserRole(
        loggedinAccount = account,
        teamId = account.teams.head.team_id,
        ignoreFatalLogging = true,
        Logger = Logger
      )

      teamUserLevelKey <- credentialsAuthService.updateApiKey(
        role = role,
        enable_internal_email_accounts_api_for_warmuphero = false,
        accountId = AccountId(account.internal_id),
        orgId = OrgId(account.org.id),
        teamId = Option(TeamId(account.teams.head.team_id)),
        keyType = SRApiKeyType.SRTeamUserLevelKey
      )

    } yield {

      InitialData(
        account = account,
        emailSetting = None,
        prospectsResult = prospectsResult,
        callSettingUuid = None,
        linkedinAccountSettings = None,
        whatsappAccount = Some(whatsappAccount),
        head_team_id = head_team_id,
        teamUserLevelKey = teamUserLevelKey.get
      )
    }
  }

}
