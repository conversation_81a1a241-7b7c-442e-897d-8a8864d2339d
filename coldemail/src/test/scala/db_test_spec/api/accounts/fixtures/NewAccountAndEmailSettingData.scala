package db_test_spec.api.accounts.fixtures

import api.accounts.{Account, PermissionMethods, ReplyHandling, RolePermissionDataDAOV2, TeamAccount, TeamId, UpdateTeamConfig}
import api.accounts.models.{AccountId, OrgId, SignupType}
import api.accounts.sr_api_key_type.models.SRApiKeyType
import api.emails.EmailSetting
import api.prospects.CreateOrUpdateProspectsResult
import api.spammonitor.model.SendEmailStatusData
import db_test_spec.api.emails.fixtures.DefaultEmailSettingParametersFixtures.defaultEmailSettingForm
import db_test_spec.api.InitialData
import db_test_spec.api.accounts.fixtures.AccountFixtureForIntegrationTest.accountService
import db_test_spec.api.accounts.fixtures.DefaultAccountParametersFixtures.dummyUtmData
import db_test_spec.api.emails.fixtures.EmailSettingFixtureForIntegrationTest
import db_test_spec.api.linkedin.fixtures.{LinkedInSettingFixtureForIntegrationTest, LinkedinAccount}
import db_test_spec.api.prospects.fixtures.ProspectFixtureForIntegrationTest
import db_test_spec.utils.SrRandomTestUtils
import eventframework.ProspectObject
import io.smartreach.esp.api.emails.EmailSettingId
import io.smartreach.esp.api.microsoftOAuth.EmailSettingUpdateAccessToken
import org.joda.time.DateTime
import utils.SRLogger
import utils.testapp.TestAppTrait

import scala.concurrent.duration.Duration
import scala.concurrent.{Await, Future}
import scala.util.Try


case class InitialAccountData(
  account: Account,
  team: TeamAccount, // there could be multiple teams, just taking the first team here
  head_team_id: Long, // there could be multiple teams, just taking the first
)


object NewAccountAndEmailSettingData extends TestAppTrait{

  def createBasicAccountData(
  )(using Logger: SRLogger): Try[InitialAccountData] = {

    given rolePermissionDataDAOV2Implicit: RolePermissionDataDAOV2 = rolePermissionDataDAOV2

    for {
      account: Account <- {
        accountService.create(
          logger = Logger,
          data = DefaultAccountParametersFixtures.generatedAccountCreateForm,
          orgKeyName = SrRandomTestUtils.getRandomStringOfLengthN(10),
          utmData = dummyUtmData,
          signup_type = SignupType.Google,
          sendEmailStatus = SendEmailStatusData.AllowedData(),
          admitadCreatedAt = None,
          firstPromoterCreatedAt = None
        ).map(_.get)

      }

      team: TeamAccount = account.teams.head

      head_team_id: Long = account.teams.head.team_id

    } yield {

      InitialAccountData(
        account = account,
        team = team,
        head_team_id = head_team_id,
      )
    }
  }

  def createNewAccountAndEmailSettingData(
                                           emailNotCompulsoryOrgFlag: Option[Boolean] = Some(false),
                                           quota_per_day: Int = 100,
                                           max_emails_per_prospect_per_day: Int = 10,
                                           max_emails_per_prospect_per_week: Int = 100,
                                           max_emails_per_prospect_account_per_day: Option[Int] = Some(10),
                                           max_emails_per_prospect_account_per_week: Option[Int] = Some(10),
                                           sender_email: String = SrRandomTestUtils.getRandomEmailStringOfLengthN(10)
                                         )(using Logger: SRLogger): Try[InitialData] = {

    //val startTime = System.currentTimeMillis()


    val res: Future[InitialData] = Future {
      AccountFixtureForIntegrationTest.createAccount(
          emailNotCompulsoryOrgFlag = emailNotCompulsoryOrgFlag
        )
        .get
        .get
    }
      .flatMap(account => {

        val head_team_id: Long = account.teams.head.team_id

        val emailSettingFut: Future[EmailSetting] = Future {

          EmailSettingFixtureForIntegrationTest.createEmailSetting(
              orgId = OrgId(account.org.id),
              accountId = AccountId(account.internal_id),
              teamId = TeamId(account.teams.head.team_id),
              taId = account.teams.head.access_members.head.ta_id,
              emailSettingForm = Option(defaultEmailSettingForm.copy(
                email = sender_email,
                quota_per_day = quota_per_day
              ))
            )
            .get

        }


        val updatedMicrosoftToken: Future[Option[EmailSettingId]] = emailSettingFut.map { emailSetting =>

          microsoftOAuth.updateAccessTokenAndRefreshToken(
              emailSettingId = emailSetting.id.get,
              data = EmailSettingUpdateAccessToken(
                oauth2_refresh_token = s"oauth2_refresh_token+${account.email}",
                oauth2_access_token = s"oauth2_access_token+${account.email}",
                oauth2_token_expires_in = 36000,
                oauth2_access_token_expires_at = DateTime.now().plusDays(10000)
              )
            )
            .get
        }

        val updatedTeamConfig: Future[Option[TeamId]] = Future {

          accountDAO.updateTeamConfig(
              teamId = TeamId(account.teams.head.team_id),
              accountId = AccountId(account.internal_id),
              data = UpdateTeamConfig(
                max_emails_per_prospect_per_day = max_emails_per_prospect_per_day,
                max_emails_per_prospect_per_week = max_emails_per_prospect_per_week,
                max_emails_per_prospect_account_per_day = max_emails_per_prospect_account_per_day,
                max_emails_per_prospect_account_per_week = max_emails_per_prospect_account_per_week,
                reply_handling = ReplyHandling.PAUSE_ALL_PROSPECT_ACCOUNT_CAMPAIGNS_ON_REPLY
              )
            )
            .get

        }

        val updatedLinkedinSetting: Future[LinkedinAccount] = Future {

          LinkedInSettingFixtureForIntegrationTest.createLinkedInSetting(
              accountId = AccountId(account.internal_id),
              teamId = TeamId(account.teams.head.team_id)
            )
            .get

        }


        val prospectsResultFut: Future[Seq[ProspectObject]] = Future {


          ProspectFixtureForIntegrationTest.createUpdateOrAssignProspect(
              accountId = AccountId(account.internal_id),
              teamId = TeamId(account.teams.head.access_members.head.team_id),
              account = account,
              emailNotCompulsoryOrgFlag = emailNotCompulsoryOrgFlag,
              generateProspectCountIfNoGivenProspect = 2
            )
            .get

        }

        val updatedTeamUserLevelKey: Future[String] = Future {

          val role = PermissionMethods.getUserRole(
            loggedinAccount = account,
            teamId = account.teams.head.team_id,
            ignoreFatalLogging = true,
            Logger = Logger
          )

          credentialsAuthService.updateApiKey(
              role = role,
              enable_internal_email_accounts_api_for_warmuphero = false,
              accountId = AccountId(account.internal_id),
              orgId = OrgId(account.org.id),
              teamId = Option(TeamId(account.teams.head.team_id)),
              keyType = SRApiKeyType.SRTeamUserLevelKey
            )
            .get
            .get

        }

        for {

          emailSetting: EmailSetting <- emailSettingFut

          add_token: Option[EmailSettingId] <- updatedMicrosoftToken

          updateTeamConfig: Option[TeamId] <- updatedTeamConfig

          linkedInSetting: LinkedinAccount <- updatedLinkedinSetting

          prospectsResult: Seq[ProspectObject] <- prospectsResultFut

          teamUserLevelKey: String <- updatedTeamUserLevelKey

        } yield {

          val endTime = System.currentTimeMillis()
          //println(s"\n\n===\ncreateNewAccountAndEmailSettingData took ${endTime - startTime} ms\n===\n\n")

          InitialData(
            account = account,
            emailSetting = Some(emailSetting),
            callSettingUuid = None,
            prospectsResult = prospectsResult,
            linkedinAccountSettings = Some(linkedInSetting),
            head_team_id = head_team_id,
            teamUserLevelKey = teamUserLevelKey
          )
        }


      })


    Try {
      Await.result(res, Duration.Inf)
    }

  }

}
