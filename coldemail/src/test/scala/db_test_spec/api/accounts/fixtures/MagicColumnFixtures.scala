package db_test_spec.api.accounts.fixtures

import api.accounts.TeamId
import api.accounts.models.AccountId
import api.campaigns.services.CampaignId
import api.columns.GenerateCustomColumnData
import api.columns.models.ColumnDefsProspectsId
import api.prospects.models.ProspectId
import org.joda.time.DateTime
import scalikejdbc.jodatime.JodaWrappedResultSet._
import scalikejdbc.{DB, scalikejdbcSQLInterpolationImplicitDef}
import utils.Helpers
import utils.dbutils.SQLUtils
import utils.testapp.TestAppTrait

import scala.util.Try

object MagicColumnFixtures extends TestAppTrait {

  def updateCreatedAtForColDefsProspects(
    teamId: TeamId,
    prospectIds: List[ProspectId],
    newUpdatedAt: DateTime,
  ): Try[List[ColumnDefsProspectsId]] = Try {

    DB.readOnly { implicit session =>

      sql"""
           UPDATE
              column_defs_prospects
           SET
             updated_at = $newUpdatedAt
           WHERE
              team_id = ${teamId.id}
              AND prospect_id IN ${SQLUtils.generateSQLValuesClause(arr = prospectIds.map(_.id))}
           RETURNING
              id;
         """
        .map { rs =>
          ColumnDefsProspectsId(id = rs.long("id"))
        }
        .list
        .apply()

    }

  }


  def getMagicNextToBeScheduledAt(
    teamId: TeamId,
    prospectId: ProspectId,
    campaignId: CampaignId,
  ): Try[Option[DateTime]] = Try {

    DB.readOnly { implicit session =>

      sql"""
           SELECT
              next_check_for_scheduling_at
           FROM
              campaigns_prospects
           WHERE
              team_id = ${teamId.id}
              AND prospect_id = ${prospectId.id}
              AND campaign_id = ${campaignId.id}
         """
        .map { rs =>

          rs.jodaDateTimeOpt("next_check_for_scheduling_at")

        }
        .single
        .apply()
        .flatten

    }

  }

  def generateMagicColumnForProspects(
    teamId: TeamId,
    accountId: AccountId,
    columnId: Long,
    prospectIds: List[ProspectId],
    generatedOutput: String,
  ): Try[Seq[ProspectId]] = {

    prospectColumnService.addProspectsForGeneratingMagicColumn(
      teamId = teamId,
      accountId = accountId,
      generateCustomColumnData = GenerateCustomColumnData(
        column_id = columnId,
        prospect_ids = prospectIds
      ),
    ).flatMap { _ =>

      val pen = prospectColumnDefDAO.updateAndFetchPendingMagicColumnForGenerations.get

      val cs = pen.map { p =>
        prospectColumnDefDAO.getColumnDefProspects(id = p).get.get
      }.filter(c => prospectIds.contains(c.prospect_id))

      val prsObjSeqTry = cs.map { c =>

        prospectColumnDefDAO.updateColumnDefsOutputAndProspectsStatusToCompleted(
          generatedMagicColOutput = generatedOutput,
          columnDefsProspectsId = c.coldefs_prospects_id,
          prospectId = c.prospect_id,
          teamId = teamId,
        ).map { _ =>

          c.prospect_id

        }

      }

      Helpers.seqTryToTrySeq(prsObjSeqTry)

    }

  }


  def getMagicPromptOutputBackup(
    id: ColumnDefsProspectsId,
    teamId: TeamId,
  ): Try[Option[String]] = Try {

    DB.readOnly { implicit session =>

      sql"""
          SELECT
            magic_prompt_output_backup
          FROM
            column_defs_prospects
          WHERE
            id = ${id.id}
            AND team_id = ${teamId.id}
         """
        .map { rs =>

          rs.stringOpt("magic_prompt_output_backup")

        }
        .single
        .apply()
    }.flatten

  }

}
