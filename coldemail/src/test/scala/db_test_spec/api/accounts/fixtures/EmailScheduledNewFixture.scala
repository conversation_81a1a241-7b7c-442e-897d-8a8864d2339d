package db_test_spec.api.accounts.fixtures

import api.campaigns.models.{CampaignEmailSettingsId, CampaignStepType}
import org.joda.time.DateTime
import sr_scheduler.models.EmailScheduledNew
import utils.uuid.SrUuidUtils

object EmailScheduledNewFixture {
  
  private val srUuidUtils = new SrUuidUtils()
  
  def generateEmailScheduledNew: EmailScheduledNew = EmailScheduledNew(
    campaign_id = Some(7L),
    step_id = None,
    is_opening_step = false,
    prospect_id = None,
    prospect_account_id = None,
    added_at = DateTime.now(),
    scheduled_at = DateTime.now(),
    sender_email_settings_id = 1L,
    template_id = None,
    variant_id = None,
    rep_mail_server_id = 1,
    via_gmail_smtp = None,
    step_type = CampaignStepType.AutoEmailStep,

    team_id = 1L,
    account_id = 1L,
    rep_tracking_host_id = 1L,

    to_email = "<EMAIL>",
    to_name = None,

    from_email = "<EMAIL>",
    from_name = "<PERSON>rachi",

    reply_to_email = None,
    reply_to_name = None,

    campaign_name = None,
    step_name = None,
    receiver_email_settings_id = 1L,

    scheduled_from_campaign = false,
    scheduled_manually = true,

    email_thread_id = None,

    pushed_to_rabbitmq = true,

    body = None,
    base_body = None,
    text_body = None,
    subject = None,


    has_open_tracking = false,
    has_click_tracking = false,
    has_unsubscribe_link = false,

    list_unsubscribe_header = None,
    gmail_fbl = None,
    campaign_email_settings_id = CampaignEmailSettingsId(id = 1L),
    uuid = srUuidUtils.generateEmailsScheduledUuid()
  )
}
