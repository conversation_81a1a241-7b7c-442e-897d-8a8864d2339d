package db_test_spec.api.accounts.fixtures

import api.accounts.TeamId
import api.accounts.models.{AccountId, OrgId}
import api.campaigns.Campaign
import api.campaigns.services.CampaignId
import api.lead_finder.models.{ContactType, LeadId, LeadValidationBatchReqId}
import api.prospects.ProspectCreateFormData
import api.prospects.models.{EmailForValidation, ProspectId}
import db_test_spec.api.InitialData
import db_test_spec.api.campaigns.test_utils.CampaignUtils
import db_test_spec.api.leadFinder.dao.LeadFinderTestDAO
import db_test_spec.api.prospects.fixtures.ProspectFixtureForIntegrationTest
import db_test_spec.utils.SrRandomTestUtils
import eventframework.ProspectObject
import io.smartreach.esp.api.emails.EmailSettingId
import org.joda.time.DateTime
import play.api.libs.json.Json
import scalikejdbc.jodatime.JodaWrappedResultSet.fromWrappedResultSetToJodaWrappedResultSet
import scalikejdbc.{DB, scalikejdbcSQLInterpolationImplicitDef}
import utils.SRLogger
import utils.dbutils.SQLUtils
import utils.emailvalidation.models.{EmailValidationPriority, EmailValidationProcessStatusV2, EmailValidationToolV2, EmailsForValidationWithInitiator, IdsOrEmailsForValidation}
import utils.testapp.TestAppTrait
import utils.testapp.csv_upload.CsvUploadCol

import scala.concurrent.Future
import scala.util.{Failure, Random, Success, Try}

case class LeadValidationBatchReqTest(
  id: LeadValidationBatchReqId,
  teamId: TeamId,
  campaignIdOpt: Option[CampaignId],
  isCompleted: Boolean,
  totalFrozenPurchasedCredits: Long,
  totalRevertedPurchasedCredits: Long,
  totalFrozenPlanCredits: Long,
  totalRevertedPlanCredits: Long,
)

case class LeadFinderBillingLogTest(
  teamId: TeamId,
  leadFinderProspectId: LeadId,
  leadFinderContactType: Option[ContactType],
  leadFinderContact: String,
  listId: Long,
  creditsUsed: Long,
  leadValidationBatchReqIdOpt: Option[LeadValidationBatchReqId],
  emailSentForValidation: Boolean,
  emailSentForValidationAtOpt: Option[DateTime],
  isValid: Boolean,
  checkedAt: Option[DateTime],
)

object LeadFinderFixture extends TestAppTrait {

  def createAlreadyValidatedEmails(
    emails: List[String],
  )(using logger: SRLogger): Future[Int] = {

    val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

    val orgId = OrgId(id = initialData.account.org.id)

    val accountId = AccountId(id = initialData.account.internal_id)

    val team = initialData.account.teams.head

    val teamId = TeamId(id = team.team_id)

    val taId = team.access_members.head.ta_id

    val emailSettingId: EmailSettingId = initialData.emailSetting.get.id.get

    val prospectCreateFormDataList = emails.map { email =>

      ProspectCreateFormData(
        email = Some(email),
        first_name = None,
        last_name = None,
        custom_fields = Json.obj(),
        list = None,
        company = None,
        city = None,
        country = None,
        timezone = None,
        state = None,
        job_title = None,
        phone = None,
        phone_2 = None,
        phone_3 = None,
        linkedin_url = None
      )
    }

    for {

      campaign: Campaign <- CampaignUtils.createNotStartedCampaignWithSteps(
        orgId = orgId,
        teamId = teamId,
        accountId = accountId,
        taId = taId,
        emailSettingId = emailSettingId,
        stepTypes = Seq(),
        ownerFirstName = initialData.account.first_name.get,
      )

      prospects: Seq[ProspectObject] <- Future.fromTry {
        ProspectFixtureForIntegrationTest.createUpdateOrAssignProspect(
          accountId = accountId,
          teamId = teamId,
          account = initialData.account,
          givenProspect = Some(prospectCreateFormDataList),
          campaignId = Some(CampaignId(id = campaign.id))
        )
      }

      _: Int <- Future.fromTry {
        emailValidationService.sendProspectsForValidation(
          priority = EmailValidationPriority.High,
          logger = logger,
          accountId = accountId.id,
          teamId = teamId,
          orgId = orgId.id,
          isAgency = false,
          idsOrEmailsForValidation = IdsOrEmailsForValidation.EntitiesForValidation(
            emailsForValidations = EmailsForValidationWithInitiator.ProspectEmailsForValidation(
              entitiesForValidation = prospects.map(
                p => EmailForValidation.ProspectEmailValidation(
                  id = ProspectId(id = p.id),
                  email = p.email.get,
                  email_checked = false,
                  email_sent_for_validation = false,
                  email_sent_for_validation_at = None
                )
              ),
              initiatorCampaignId = CampaignId(id = campaign.id)
            )
          )
        )
      }

      res: Int <- Future.fromTry(markEmailValidations(emails = emails))

    } yield {

      res

    }

  }

  def markEmailValidations(emails: Seq[String]): Try[Int] = Try {

    if (emails.isEmpty) {

      0

    } else {

      DB autoCommit { implicit session =>

        sql"""
             UPDATE
              email_validations
             SET
              checked_at = now() - interval '27' day,
              is_valid = random() > 0.5,
              process_status = ${EmailValidationProcessStatusV2.COMPLETED.toString},
              checked_via_tool = ${EmailValidationToolV2.BOUNCER.toString}
             WHERE
              lower(email) IN ${SQLUtils.generateSQLValuesClause(arr = emails.map(_.trim.toLowerCase()))}
           """
          .update
          .apply()

      }

    }

  }


  // TODO: Try to use the existing getBillingLogs
  def getLeadFinderBillingLogs(
    teamId: TeamId,
    leadFinderProspectIds: Seq[LeadId]
  ): Try[List[LeadFinderBillingLogTest]] = Try {
    println(s"getLeadFinderBillingLogs ${leadFinderProspectIds}")

    if(leadFinderProspectIds.isEmpty){
      List.empty[LeadFinderBillingLogTest]

    } else {

      DB readOnly { implicit session =>

        sql"""
          SELECT
            team_id,
            lead_finder_prospect_id,
            lead_finder_contact_type,
            lead_finder_contact,
            list_id,
            credits_used,
            lead_validation_batch_request_id,
            email_sent_for_validation,
            email_sent_for_validation_at,
            is_valid,
            checked_at
          FROM
            lead_finder_billing_logs
          WHERE
            team_id = ${teamId.id}
            AND lead_finder_prospect_id IN ${SQLUtils.generateSQLValuesClause(arr = leadFinderProspectIds.map(_.id))}
         """
          .map { rs =>

            LeadFinderBillingLogTest(
              teamId = TeamId(id = rs.long("team_id")),
              leadFinderProspectId = LeadId(id = rs.long("lead_finder_prospect_id")),
              leadFinderContactType = rs.stringOpt("lead_finder_contact_type").flatMap(ct => ContactType.fromKey(str = ct).toOption),
              leadFinderContact = rs.string("lead_finder_contact"),
              listId = rs.long("list_id"),
              creditsUsed = rs.long("credits_used"),
              leadValidationBatchReqIdOpt = rs.longOpt("lead_validation_batch_request_id").map(id => LeadValidationBatchReqId(id = id)),
              emailSentForValidation = rs.boolean("email_sent_for_validation"),
              emailSentForValidationAtOpt = rs.jodaDateTimeOpt("email_sent_for_validation_at"),
              isValid = rs.boolean("is_valid"),
              checkedAt = rs.jodaDateTimeOpt("checked_at"),
            )

          }
          .list
          .apply()

      }
    }

  }

  def getLeadValidationBatchRequest(
    teamId: TeamId,
    leadValidationBatchReqId: LeadValidationBatchReqId
  ): Try[Option[LeadValidationBatchReqTest]] = Try {

    DB readOnly { implicit session =>

      sql"""
          SELECT
            id,
            team_id,
            campaign_id,
            is_completed,
            total_frozen_purchased_credits,
            total_reverted_purchased_credits,
            total_frozen_plan_credits,
            total_reverted_plan_credits
          FROM
            lead_validation_batch_request
          WHERE
            id = ${leadValidationBatchReqId.id}
            AND team_id = ${teamId.id};
         """
        .map { rs =>

          LeadValidationBatchReqTest(
            id = LeadValidationBatchReqId(id = rs.long("id")),
            teamId = TeamId(id = rs.long("team_id")),
            campaignIdOpt = rs.longOpt("campaign_id").map(id => CampaignId(id = id)),
            isCompleted = rs.boolean("is_completed"),
            totalFrozenPurchasedCredits = rs.long("total_frozen_purchased_credits"),
            totalRevertedPurchasedCredits = rs.long("total_reverted_purchased_credits"),
            totalFrozenPlanCredits = rs.long("total_frozen_plan_credits"),
            totalRevertedPlanCredits = rs.long("total_reverted_plan_credits"),
          )

        }
        .single
        .apply()

    }

  }

  def addLeadFinderProspects(
    teamId: TeamId,
    taId: Long,
    accountId: AccountId,
    numOfLeads: Int,
    data: Seq[CsvUploadCol],
    alwaysAddPhoneNumberForLead: Boolean = true,
    leadShouldHaveInvalidEmail: Boolean = false,
  ): Try[List[LeadId]] = {

    val uniqueId = s"${teamId.id}_${taId}_${accountId.id}_$numOfLeads".trim.toLowerCase

    val data_res: Seq[CsvUploadCol] = if (data.isEmpty) {

      val listDomains = List("example1","example2")
      val domains = listDomains.map(str => str + ".com")


      LeadFinderTestDAO.insertIntoLeadFinderVerifiedBusinessDomain(domains = domains) match {

        case Failure(exception) => {
          println(s"An error occurred ${exception.getMessage()}")
          Seq()

        }
        case Success(value) =>


          (0 until numOfLeads).map { i =>

            val uniqueIdWithIndex = s"${i}_$uniqueId".trim.toLowerCase

            val firstName = s"first_name_$uniqueIdWithIndex".trim.toLowerCase

            val lastName = s"last_name_$uniqueIdWithIndex".trim.toLowerCase

            val linkedinUrl = s"www.linkedin.com/in/$firstName-$lastName-$i".trim.toLowerCase


            val randomDomain = domains(Random.nextInt(domains.length))

            val email = if (leadShouldHaveInvalidEmail) {


              s"$firstName@@%@.$lastName-23**##@@${randomDomain}@".trim.toLowerCase

            } else {

              s"$firstName.$lastName@${randomDomain}".trim.toLowerCase

            }
            
            val phone = SrRandomTestUtils.getRandomPhoneNumber

            val phoneOpt: Option[String] = if (alwaysAddPhoneNumberForLead) {

              Some(phone)

            } else {

              val shouldAddPhone = Random.nextBoolean()

              if (shouldAddPhone) Some(phone) else None

            }

            CsvUploadCol(
              prospect_data_hash = s"some_hash_$uniqueIdWithIndex".trim.toLowerCase,
              person_first_name = Some(firstName),
              person_last_name = Some(lastName),
              company_type = Some("public"),
              company_industry = Some("other"),
              person_business_email = Some(email),
              person_phone = phoneOpt,
              linkedin_url = Some(linkedinUrl)
            )

          }
      }

    } else {

      data

    }

    leadFinderUploadDao.insertCsvParsedCsvData(
      accountId = accountId.id,
      teamId = teamId.id,
      ta_id = taId,
      fileName = s"some_file_name_$uniqueId".trim.toLowerCase,
      dataSource = None,
      data = data_res
    ).map(_.map(id => LeadId(id = id)))

  }

}
