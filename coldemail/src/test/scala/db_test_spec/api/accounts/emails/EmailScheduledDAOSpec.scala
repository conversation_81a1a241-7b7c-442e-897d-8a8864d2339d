package db_test_spec.api.accounts.emails

import api.accounts.EmailScheduledIdOrTaskId.EmailScheduledId
import api.accounts.TeamId
import api.accounts.email.models.EmailServiceProvider
import api.accounts.{Account, AccountEmail, TeamId}
import api.accounts.models.AccountId
import api.campaigns.PreviousFollowUp
import api.accounts.models.{AccountId, OrgId}
import api.campaigns.{Campaign, CampaignCreateForm, CampaignSettings, CampaignStepVariant, CampaignWithStatsAndEmail}
import api.campaigns.models.{CampaignEmailSettingsId, CampaignStepType, InboxPlacementCheckLogId, SendEmailFromCampaignDetails, StepDetails}
import api.campaigns.services.CampaignId
import api.emails.{EmailHeaderForInboxPlacementCheckAnalysis, EmailReceivedForWebhook, EmailReceivedForWebhookV2, EmailScheduled, EmailScheduledForCheckingReplies, EmailScheduledIdsFoundForSendingViaMQ, EmailScheduledNewAfterSaving, EmailSetting, EmailThreadFoundForCheckingReplies, EmailToBeSent, InboxPlacementCheckSentEmailScheduledDetails}
import api.prospects.{FindThreadMessageFlow, ProspectCreateFormData}
import app.test_fixtures.prospect.ProspectCreateFormDataFixture
import db_test_spec.api.{AppSpecFixture, DbTestingBeforeAllAndAfterAll, InitialData, InputForInitializingCampaignCreateData, SRSetupAndDeleteFixtures}
import db_test_spec.api.campaigns.fixtures.DefaultCampaignParametersFixtures.defaultCampaignCreateForm
import db_test_spec.api.campaigns.fixtures.{CreateNewCampaignFixture, CreateStepForCampaignFixture, NewCampaignCreationData, StartCampaignFixture}
import db_test_spec.api.prospects.fixtures.ProspectFixtureForIntegrationTest
import eventframework.{MessageObject, ProspectObject}
import io.smartreach.esp.api.emails.EmailSettingId
import api.accounts.models.{AccountId, OrgId}
import api.calendar_app.models.CalendarAccountData
import api.campaigns.dao.InboxPlacementCheckTrackedEmails
import api.campaigns.{Campaign, CampaignStepVariant, CampaignWithStatsAndEmail}
import api.emails.models.EmailSendingFlow
import io.smartreach.esp.api.emails.EmailSettingId
import api.campaigns.models.{CampaignEmailSettingsId, CampaignStepType}
import api.campaigns.services.{CampaignId, GetTestStepsError}
import api.emails.models.EmailSendingFlow
import api.prospects.dao.ProspectIdAndPotentialDuplicateProspectId
import api.prospects.models.{PotentialDuplicateProspectId, ProspectId}
import app.db_test.{CustomNotCategorizedAndDoNotContactIds, SchedulerTestInput}
import db_test_spec.api.accounts.fixtures.{EmailScheduledNewFixture, NewAccountAndEmailSettingData}
import db_test_spec.api.DbTestingBeforeAllAndAfterAll
import db_test_spec.api.accounts.emails.dao.EmailScheduledDAO
import db_test_spec.api.accounts.emails.dao.EmailScheduledDAO.updateSentAtForInbpCheckTestStep
import db_test_spec.api.accounts.emails.dao.EmailThreadDAO.getExistingEmailThreadAndTeamId
import db_test_spec.api.accounts.fixtures.EmailScheduledNewFixture
import db_test_spec.api.campaigns.CampaignCreationFixtureForIntegrationTest
import db_test_spec.api.campaigns.fixtures.{CreateNewCampaignFixture, CreateStepForCampaignFixture, StartCampaignFixture}
import db_test_spec.api.campaigns.test_utils.{CampaignUtils, CreateAndStartCampaignData}
import db_test_spec.api.prospects.fixtures.ProspectFixtureForIntegrationTest
import db_test_spec.api.scheduler.fixtures.{DefaultParametersFixtureForInitializingDataForReScheduling, ReSchedulingFixture}
import eventframework.ProspectObject
import io.smartreach.esp.api.microsoftOAuth.EmailSettingUpdateAccessToken
import io.smartreach.esp.utils.email.InternetMsgIDForFetchOutlookMsgId
import org.joda.time.DateTime
import org.scalatest.ParallelTestExecution
import play.api.libs.json.Json
import scalikejdbc.DBSession
import utils.SRLogger
import utils.dbutils.DbAndSession
import utils.email.EmailSendDetail
import utils.helpers.LogHelpers

import scala.concurrent.Future
import scala.util.{Failure, Random, Success, Try}
import scala.concurrent.duration.{Duration, SECONDS}
import scala.util.{Random, Try}
import scala.concurrent.{Await, Future}


class EmailScheduledDAOSpec extends DbTestingBeforeAllAndAfterAll with ParallelTestExecution {


  describe("findEmailIdsForQueueingCampaignEmailsToRabbitMQV2") {

    it("should give success") {
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
      val result: Try[Seq[EmailScheduledIdsFoundForSendingViaMQ]] = emailScheduledDAO.findEmailIdsForQueueingCampaignEmailsToRabbitMQV2(
        emailSettingData = Map(initialData.emailSetting.get.team_id -> List((initialData.emailSetting.get.id.get, EmailServiceProvider.GMAIL_API))),
        toBeSentTill = DateTime.now(),
        logger = Logger
      )

      assert(result.isSuccess)
      //TODO: check the result why sometimes nonEmpty
      //      assert(result.get.isEmpty)
    }

  }

  describe("findAndUpdateEmailIdsForQueueingCampaignEmailsToRabbitMQ") {
    it("should return success") {
      val res = emailScheduledDAO.findAndUpdateEmailIdsForQueueingCampaignEmailsToRabbitMQ(
        toBeSentTill = DateTime.now().plusDays(1)
      )
      res match{
        case Success(value) => assert(true)
        case Failure(exception) => println(LogHelpers.getStackTraceAsString(exception))
          assert(false)
      }
    }
  }


  describe("getScheduleDetailsForSending") {

    it("getScheduleDetailsForSendingNew") {
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val result = for {

        createCampaign: NewCampaignCreationData <- CreateNewCampaignFixture.createNewCampaign(
          orgId = OrgId(initialData.account.org.id),
          accountId = AccountId(initialData.account.internal_id),
          accountName = s"${initialData.account.first_name.getOrElse("Animesh")} ${initialData.account.last_name.getOrElse("Kumar")}"
        )
        prospect <- Future.fromTry {
          ProspectFixtureForIntegrationTest.createUpdateOrAssignProspect(
            givenProspect = Some(Seq(
              ProspectCreateFormDataFixture.prospectCreateFormData.copy(
                email = Some("<EMAIL>"),
                first_name = Some("Animesh"),
                last_name = Some("Kumar"),
                owner_id = Some(createCampaign.newTeamCreationData.updatedAccount.internal_id),
                list = None,
                company = Some("Test Company"),
                city = Some("Pune"),
                country = Some("India"),
                timezone = Some("Asia/Kolkata"),
                created_at = None,

                state = Some("Maharashtra"),
                job_title = Some("SDE"),
                phone = Some("**********"),
                linkedin_url = Some("https://www.linkedin.com/in/animesh-kumar-9a1171191/")
              ))),
            campaignId = Some(CampaignId(createCampaign.campaignWithStatsAndEmail.id)),
            account = createCampaign.newTeamCreationData.updatedAccount,
            teamId = TeamId(createCampaign.campaignWithStatsAndEmail.team_id),
            accountId = AccountId(createCampaign.newTeamCreationData.updatedAccount.internal_id)
          )
        }
        //add auto email step to campaign
        addStep: CampaignStepVariant <- {
          CreateStepForCampaignFixture.createAutoEmailStepForCampaign(
            orgId = OrgId(createCampaign.newTeamCreationData.updatedAccount.org.id),
            teamId = TeamId(createCampaign.campaignWithStatsAndEmail.team_id),
            accountId = AccountId(createCampaign.newTeamCreationData.updatedAccount.internal_id),
            taId = createCampaign.newTeamCreationData.teamAccount.access_members.head.ta_id,
            campaignId = CampaignId(createCampaign.campaignWithStatsAndEmail.id),
          )
        }

        //campaign details
        campaign: Campaign <- Future.successful(
          campaignDAO.findCampaignForCampaignUtilsOnly(
            id = createCampaign.campaignWithStatsAndEmail.id,
            teamId = TeamId(createCampaign.campaignWithStatsAndEmail.team_id)
          ).get
        )

        //start campaign
        startCampaign: CampaignWithStatsAndEmail <- {
          StartCampaignFixture.startCampaign(
            accountId = AccountId(createCampaign.newTeamCreationData.updatedAccount.internal_id),
            teamId = TeamId(createCampaign.campaignWithStatsAndEmail.team_id),
            campaignWithStatsAndEmail = createCampaign.campaignWithStatsAndEmail,
            orgId = OrgId(createCampaign.newTeamCreationData.updatedAccount.org.id),
            taId = createCampaign.newTeamCreationData.teamAccount.access_members.head.ta_id,
            emailSetting = createCampaign.newEmailSetting,
            prospect_categories_custom_not_categorized = createCampaign.customNotCategorizedAndDoNotContactIds.not_categorized,
            prospect_categories_custom_do_not_contact = createCampaign.customNotCategorizedAndDoNotContactIds.do_not_contact,
            current_sending_email_accounts = createCampaign.newEmailSetting.id.get.emailSettingId.toInt
          )
        }
        addingEmailScheduled <- Future.fromTry {
          emailScheduledDAOService.saveEmailsToBeScheduledAndUpdateCampaignDataV2(
            emailsToBeScheduled = Vector(EmailScheduledNewFixture.generateEmailScheduledNew3.copy(
              campaign_id = Some(campaign.id),
              step_id = campaign.head_step_id,
              from_email = campaign.settings.campaign_email_settings.head.sender_email,
              team_id = campaign.team_id,
              sender_email_settings_id = campaign.settings.campaign_email_settings.head.sender_email_setting_id.emailSettingId,
              receiver_email_settings_id = campaign.settings.campaign_email_settings.head.receiver_email_setting_id.emailSettingId

            )
            ),
            campaign_email_setting_id = campaign.settings.campaign_email_settings.head.id,
            emailSendingFlow = None,
            Logger = Logger
          )
        }

        readNew: Option[EmailSendDetail] <- Future.fromTry {
          emailScheduledDAO.getScheduleDetailsForSendingNew(
            emailScheduledId = addingEmailScheduled.head.email_scheduled_id,
            teamId = addingEmailScheduled.head.team_id
          )
        }


      } yield {

        readNew
      }


      result.map{v =>
        assert(v.isDefined)
      }.recover{e =>
        println(s"e----- ${LogHelpers.getStackTraceAsString(e)}")
        assert(false)
      }

    }

  }


  describe("getSentEmailsForCheckingReplies") {

    it("getSentEmailsForCheckingRepliesNew") {

      val result = for {
        initialData: InitialData <- Future.fromTry(NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData())

        createCampaign: NewCampaignCreationData <- CreateNewCampaignFixture.createNewCampaign(
          orgId = OrgId(initialData.account.org.id),
          accountId = AccountId(initialData.account.internal_id),
          accountName = s"${initialData.account.first_name.getOrElse("Animesh")} ${initialData.account.last_name.getOrElse("Kumar")}"
        )

        prospect <- Future.fromTry {
          ProspectFixtureForIntegrationTest.createUpdateOrAssignProspect(
            givenProspect = Some(Seq(
              ProspectCreateFormDataFixture.prospectCreateFormData.copy(
                email = Some("<EMAIL>"),
                first_name = Some("Animesh"),
                last_name = Some("Kumar"),
                owner_id = Some(createCampaign.newTeamCreationData.updatedAccount.internal_id),
                list = None,
                company = Some("Test Company"),
                city = Some("Pune"),
                country = Some("India"),
                timezone = Some("Asia/Kolkata"),
                created_at = None,

                state = Some("Maharashtra"),
                job_title = Some("SDE"),
                phone = Some("**********"),
                linkedin_url = Some("https://www.linkedin.com/in/animesh-kumar-9a1171191/")
              ))),
            campaignId = Some(CampaignId(createCampaign.campaignWithStatsAndEmail.id)),
            account = createCampaign.newTeamCreationData.updatedAccount,
            teamId = TeamId(createCampaign.campaignWithStatsAndEmail.team_id),
            accountId = AccountId(createCampaign.newTeamCreationData.updatedAccount.internal_id)
          )
        }
        //add auto email step to campaign
        addStep: CampaignStepVariant <- {
          CreateStepForCampaignFixture.createAutoEmailStepForCampaign(
            orgId = OrgId(createCampaign.newTeamCreationData.updatedAccount.org.id),
            teamId = TeamId(createCampaign.campaignWithStatsAndEmail.team_id),
            accountId = AccountId(createCampaign.newTeamCreationData.updatedAccount.internal_id),
            taId = createCampaign.newTeamCreationData.teamAccount.access_members.head.ta_id,
            campaignId = CampaignId(createCampaign.campaignWithStatsAndEmail.id),
          )
        }


        //campaign details
        campaign: Campaign <- Future.successful(
          campaignDAO.findCampaignForCampaignUtilsOnly(
            id = createCampaign.campaignWithStatsAndEmail.id,
            teamId = TeamId(createCampaign.campaignWithStatsAndEmail.team_id)
          ).get
        )

        //start campaign
        startCampaign: CampaignWithStatsAndEmail <- {
          StartCampaignFixture.startCampaign(
            accountId = AccountId(createCampaign.newTeamCreationData.updatedAccount.internal_id),
            teamId = TeamId(createCampaign.campaignWithStatsAndEmail.team_id),
            campaignWithStatsAndEmail = createCampaign.campaignWithStatsAndEmail,
            orgId = OrgId(createCampaign.newTeamCreationData.updatedAccount.org.id),
            taId = createCampaign.newTeamCreationData.teamAccount.access_members.head.ta_id,
            emailSetting = createCampaign.newEmailSetting,
            prospect_categories_custom_not_categorized = createCampaign.customNotCategorizedAndDoNotContactIds.not_categorized,
            prospect_categories_custom_do_not_contact = createCampaign.customNotCategorizedAndDoNotContactIds.do_not_contact,
            current_sending_email_accounts = createCampaign.newEmailSetting.id.get.emailSettingId.toInt
          )
        }
        addingEmailScheduled <- Future.fromTry {
          emailScheduledDAOService.saveEmailsToBeScheduledAndUpdateCampaignDataV2(
            emailsToBeScheduled = Vector(EmailScheduledNewFixture.generateEmailScheduledNew3.copy(
              campaign_id = Some(createCampaign.campaignWithStatsAndEmail.id),
              step_id = createCampaign.campaignWithStatsAndEmail.head_step_id,
              from_email = createCampaign.campaignWithStatsAndEmail.settings.campaign_email_settings.head.sender_email,
              scheduled_from_campaign = true,
              is_opening_step = true,
              sender_email_settings_id = createCampaign.campaignWithStatsAndEmail.settings.campaign_email_settings.head.sender_email_setting_id.emailSettingId,
              team_id = createCampaign.campaignWithStatsAndEmail.team_id,
              account_id = createCampaign.campaignWithStatsAndEmail.owner_id,
              receiver_email_settings_id = createCampaign.campaignWithStatsAndEmail.settings.campaign_email_settings.head.receiver_email_setting_id.emailSettingId,
              campaign_email_settings_id = createCampaign.campaignWithStatsAndEmail.settings.campaign_email_settings.head.id,
              prospect_id = Some(prospect.head.id)

            )
            ),
            campaign_email_setting_id = createCampaign.campaignWithStatsAndEmail.settings.campaign_email_settings.head.id,
            emailSendingFlow = None,
            Logger = Logger
          )
        }

        emailSent <- Future.fromTry {
          emailSenderService.onEmailSent(
            emailSentId = addingEmailScheduled.head.email_scheduled_id,
            data = DefaultParametersFixtureForInitializingDataForReScheduling.defaultEmailToBeSent(
              emailSetting = initialData.emailSetting.get,
              campaign = campaign
            ),
            accountId = createCampaign.newTeamCreationData.updatedAccount.internal_id,
            sendEmailFromCampaignDetails = Some(SendEmailFromCampaignDetails(
              campaign_id = createCampaign.campaignWithStatsAndEmail.id,
              campaign_name = createCampaign.campaignWithStatsAndEmail.name,
              // stepDetails can be none when email is being sent manually from Inbox.
              // Example: sendNewEmailManually in InboxV3Service
              stepDetails = Some(StepDetails(
                step_id = addStep.step_id,
                step_name = addStep.label.getOrElse("step"),
                step_type =addStep.step_data.step_type
              ))
            )),
            prospectIdInCampaign = prospect.headOption.map(_.id),
            currentBillingCycleStartedAt = createCampaign.newTeamCreationData.updatedAccount.created_at,
            orgId = createCampaign.newTeamCreationData.updatedAccount.org.id,
            repTrackingHostId = 1,
            teamId = campaign.team_id
          )
        }

        readNew <- Future {
          emailScheduledDAO.getSentEmailsForCheckingRepliesNew(
            senderEmailSettingsIds = createCampaign.campaignWithStatsAndEmail.settings.campaign_email_settings.map(_.sender_email_setting_id.emailSettingId.toInt),
            teamId = createCampaign.campaignWithStatsAndEmail.team_id,
            logger = Logger
          )
        }


      } yield {

        readNew
      }


      result.map{v =>
        assert(v.nonEmpty)
      }.recover{e =>
        println(s"e --- $e")
        assert(false)
      }

    }

  }


  describe("getPreviousSentSteps") {

    it("getPreviousSentStepsNew") {

      val result = for {
        initialData: InitialData <- Future.fromTry(NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData())

        createCampaign: NewCampaignCreationData <- CreateNewCampaignFixture.createNewCampaign(
          orgId = OrgId(initialData.account.org.id),
          accountId = AccountId(initialData.account.internal_id),
          accountName = s"${initialData.account.first_name.getOrElse("Animesh")} ${initialData.account.last_name.getOrElse("Kumar")}"
        )
        prospect <- Future.fromTry {
          ProspectFixtureForIntegrationTest.createUpdateOrAssignProspect(
            givenProspect = Some(Seq(
              ProspectCreateFormDataFixture.prospectCreateFormData.copy(
                email = Some("<EMAIL>"),
                first_name = Some("Animesh"),
                last_name = Some("Kumar"),
                owner_id = Some(createCampaign.newTeamCreationData.updatedAccount.internal_id),
                list = None,
                company = Some("Test Company"),
                city = Some("Pune"),
                country = Some("India"),
                timezone = Some("Asia/Kolkata"),
                created_at = None,

                state = Some("Maharashtra"),
                job_title = Some("SDE"),
                phone = Some("**********"),
                linkedin_url = Some("https://www.linkedin.com/in/animesh-kumar-9a1171191/")
              ))),
            campaignId = Some(CampaignId(createCampaign.campaignWithStatsAndEmail.id)),
            account = createCampaign.newTeamCreationData.updatedAccount,
            teamId = TeamId(createCampaign.campaignWithStatsAndEmail.team_id),
            accountId = AccountId(createCampaign.newTeamCreationData.updatedAccount.internal_id)
          )
        }
        //add auto email step to campaign
        addStep: CampaignStepVariant <- {
          CreateStepForCampaignFixture.createAutoEmailStepForCampaign(
            orgId = OrgId(createCampaign.newTeamCreationData.updatedAccount.org.id),
            teamId = TeamId(createCampaign.campaignWithStatsAndEmail.team_id),
            accountId = AccountId(createCampaign.newTeamCreationData.updatedAccount.internal_id),
            taId = createCampaign.newTeamCreationData.teamAccount.access_members.head.ta_id,
            campaignId = CampaignId(createCampaign.campaignWithStatsAndEmail.id),
          )
        }


        //campaign details
        campaign: Campaign <- Future.successful(
          campaignDAO.findCampaignForCampaignUtilsOnly(
            id = createCampaign.campaignWithStatsAndEmail.id,
            teamId = TeamId(createCampaign.campaignWithStatsAndEmail.team_id)
          ).get
        )

        //start campaign
        startCampaign: CampaignWithStatsAndEmail <- {
          StartCampaignFixture.startCampaign(
            accountId = AccountId(createCampaign.newTeamCreationData.updatedAccount.internal_id),
            teamId = TeamId(createCampaign.campaignWithStatsAndEmail.team_id),
            campaignWithStatsAndEmail = createCampaign.campaignWithStatsAndEmail,
            orgId = OrgId(createCampaign.newTeamCreationData.updatedAccount.org.id),
            taId = createCampaign.newTeamCreationData.teamAccount.access_members.head.ta_id,
            emailSetting = createCampaign.newEmailSetting,
            prospect_categories_custom_not_categorized = createCampaign.customNotCategorizedAndDoNotContactIds.not_categorized,
            prospect_categories_custom_do_not_contact = createCampaign.customNotCategorizedAndDoNotContactIds.do_not_contact,
            current_sending_email_accounts = createCampaign.newEmailSetting.id.get.emailSettingId.toInt
          )
        }
        addingEmailScheduled <- Future.fromTry {
          emailScheduledDAOService.saveEmailsToBeScheduledAndUpdateCampaignDataV2(
            emailsToBeScheduled = Vector(EmailScheduledNewFixture.generateEmailScheduledNew3.copy(
              campaign_id = Some(createCampaign.campaignWithStatsAndEmail.id),
              step_id = createCampaign.campaignWithStatsAndEmail.head_step_id,
              from_email = createCampaign.campaignWithStatsAndEmail.settings.campaign_email_settings.head.sender_email,
              scheduled_from_campaign = true,
              is_opening_step = true,
              sender_email_settings_id = createCampaign.campaignWithStatsAndEmail.settings.campaign_email_settings.head.sender_email_setting_id.emailSettingId,
              team_id = createCampaign.campaignWithStatsAndEmail.team_id,
              account_id = createCampaign.campaignWithStatsAndEmail.owner_id,
              receiver_email_settings_id = createCampaign.campaignWithStatsAndEmail.settings.campaign_email_settings.head.receiver_email_setting_id.emailSettingId,
              campaign_email_settings_id = createCampaign.campaignWithStatsAndEmail.settings.campaign_email_settings.head.id,
              prospect_id = Some(prospect.head.id),
              base_body = "body"

            )
            ),
            campaign_email_setting_id = createCampaign.campaignWithStatsAndEmail.settings.campaign_email_settings.head.id,
            emailSendingFlow = None,
            Logger = Logger
          )
        }

        emailSent <- Future.fromTry {
          emailSenderService.onEmailSent(
            emailSentId = addingEmailScheduled.head.email_scheduled_id,
            data = DefaultParametersFixtureForInitializingDataForReScheduling.defaultEmailToBeSent(
              emailSetting = initialData.emailSetting.get,
              campaign = campaign
            ),
            accountId = createCampaign.newTeamCreationData.updatedAccount.internal_id,
            sendEmailFromCampaignDetails = Some(SendEmailFromCampaignDetails(
              campaign_id = createCampaign.campaignWithStatsAndEmail.id,
              campaign_name = createCampaign.campaignWithStatsAndEmail.name,
              // stepDetails can be none when email is being sent manually from Inbox.
              // Example: sendNewEmailManually in InboxV3Service
              stepDetails = Some(StepDetails(
                step_id = addStep.step_id,
                step_name = addStep.label.getOrElse("step"),
                step_type =addStep.step_data.step_type
              ))
            )),
            prospectIdInCampaign = prospect.headOption.map(_.id),
            currentBillingCycleStartedAt = createCampaign.newTeamCreationData.updatedAccount.created_at,
            orgId = createCampaign.newTeamCreationData.updatedAccount.org.id,
            repTrackingHostId = 1,
            teamId = campaign.team_id
          )
        }

        readNew <- Future.fromTry {
          emailScheduledDAO.getPreviousSentStepsNew(
            campaignId = campaign.id,
            prospectId = prospect.head.id,
            teamId = TeamId(campaign.team_id),
            get_skipped = false
          )
        }

        readNew2 <- Future.fromTry {
          emailScheduledDAO.getPreviousSentStepsNew(
            campaignId = campaign.id,
            prospectId = prospect.head.id,
            teamId = TeamId(campaign.team_id),
            get_skipped = true
          )
        }


      } yield {

        (readNew, readNew2)
      }


      result.map{v =>
        assert(v._1.nonEmpty)
        assert(v._2.nonEmpty)
      }.recover{e =>
        println(s"e --- $e")
        assert(false)
      }

    }

  }


  describe("getInboxPlacementCheckSentEmailScheduledDetails") {
    it("should give success and return InboxPlacementCheckSentEmailScheduledDetails") {


      val sentAtInput: DateTime  =DateTime.now()


      val res: Future[InboxPlacementCheckSentEmailScheduledDetails] = for {

        initialData: InitialData <- Future.fromTry(NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData())

        createCampaign: NewCampaignCreationData <- CreateNewCampaignFixture.createNewCampaign(
          orgId = OrgId(initialData.account.org.id),
          accountId = AccountId(initialData.account.internal_id),
          accountName = s"${initialData.account.first_name.getOrElse("Animesh")} ${initialData.account.last_name.getOrElse("Kumar")}"
        )

        // add prospect to campaign
        addProspect: Seq[ProspectObject] <- Future.fromTry {
          ProspectFixtureForIntegrationTest.createUpdateOrAssignProspect(
            campaignId = Option(CampaignId(createCampaign.campaignWithStatsAndEmail.id)),
            accountId = AccountId(createCampaign.newTeamCreationData.updatedAccount.internal_id),
            teamId = TeamId(createCampaign.campaignWithStatsAndEmail.team_id),
            account = createCampaign.newTeamCreationData.updatedAccount,
            generateProspectCountIfNoGivenProspect = 1
          )
        }

        //add auto email step to campaign
        addStep: CampaignStepVariant <- {
          CreateStepForCampaignFixture.createAutoEmailStepForCampaign(
            orgId = OrgId(initialData.account.org.id),
            teamId = TeamId(createCampaign.campaignWithStatsAndEmail.team_id),
            accountId = AccountId(createCampaign.newTeamCreationData.updatedAccount.internal_id),
            taId = createCampaign.newTeamCreationData.teamAccount.access_members.head.ta_id,
            campaignId = CampaignId(createCampaign.campaignWithStatsAndEmail.id),
          )
        }

        //campaign details
        campaign: Campaign <- Future.successful(
          campaignDAO.findCampaignForCampaignUtilsOnly(
            id = createCampaign.campaignWithStatsAndEmail.id,
            teamId = TeamId(createCampaign.campaignWithStatsAndEmail.team_id)
          ).get
        )

        //start campaign
        startCampaign: CampaignWithStatsAndEmail <- {
          StartCampaignFixture.startCampaign(
            accountId = AccountId(createCampaign.newTeamCreationData.updatedAccount.internal_id),
            teamId = TeamId(createCampaign.campaignWithStatsAndEmail.team_id),
            campaignWithStatsAndEmail = createCampaign.campaignWithStatsAndEmail,
            orgId = OrgId(initialData.account.org.id),
            taId = createCampaign.newTeamCreationData.teamAccount.access_members.head.ta_id,
            emailSetting = createCampaign.newEmailSetting,
            prospect_categories_custom_not_categorized = createCampaign.customNotCategorizedAndDoNotContactIds.not_categorized,
            prospect_categories_custom_do_not_contact = createCampaign.customNotCategorizedAndDoNotContactIds.do_not_contact,
            current_sending_email_accounts = createCampaign.newEmailSetting.id.get.emailSettingId.toInt
          )
        }

        //sent test email
        sentEmail: Either[GetTestStepsError, Unit] <- {
          campaignTestStepService.testStep(
            stepId = Some(addStep.step_id),
            body = "Test email for inbox placement check",
            subject = "Test email",
            campaign = campaign,
            toEmail = "<EMAIL>",
            campaignId = CampaignId(campaign.id.toLong),
            stepType = CampaignStepType.AutoEmailStep,
            allTrackingDomainsUsed = Seq(),
            campaign_email_settings_id = startCampaign.settings.campaign_email_settings.head.id,
            team_id = TeamId(campaign.team_id),
            owner_id = AccountId(campaign.account_id),
            ta_id = 1L,
            owner_name = startCampaign.owner_name,
            owner_email = AccountEmail(startCampaign.owner_email),
            calendar_account_data = None,
            emailSendingFlow = Some(EmailSendingFlow.InboxPlacementOut),
            org_id = OrgId(initialData.account.org.id)
          )
        }
        //updating sent_at for inbp_out email as cannot call send email mq
        updatedSentAt: Long <- Future.fromTry(updateSentAtForInbpCheckTestStep(
          campaign_id = CampaignId(campaign.id.toLong),
          teamId = TeamId(campaign.team_id),
          sentAt = sentAtInput
        ))

        //reading emails_scheduled details
        sentEmailScheduledDetails: InboxPlacementCheckSentEmailScheduledDetails <- Future.fromTry(emailScheduledDAO.getInboxPlacementCheckSentEmailScheduledDetails(
          teamId = TeamId(campaign.team_id),
          senderEmailSettingId = campaign.settings.campaign_email_settings.head.sender_email_setting_id
        )
        )

      } yield {
        sentEmailScheduledDetails
      }

      res.map { r =>
        assert(r.sent_at == sentAtInput)
      }.recover(err => {
        println(s"Error: $err")
        assert(false)
      })
    }
  }

  describe("findEmailThreadIdsWhileReplyTracking") {

    it("findEmailThreadIdsWhileReplyTrackingNew") {

      val result = for {

        initialData: InitialData <- Future.fromTry(NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData())

        createCampaign: NewCampaignCreationData <- CreateNewCampaignFixture.createNewCampaign(
          orgId = OrgId(initialData.account.org.id),
          accountId = AccountId(initialData.account.internal_id),
          accountName = s"${initialData.account.first_name.getOrElse("Animesh")} ${initialData.account.last_name.getOrElse("Kumar")}"
        )
        prospect <- Future.fromTry {
          ProspectFixtureForIntegrationTest.createUpdateOrAssignProspect(
            givenProspect = Some(Seq(
              ProspectCreateFormDataFixture.prospectCreateFormData.copy(
                email = Some("<EMAIL>"),
                first_name = Some("Animesh"),
                last_name = Some("Kumar"),
                owner_id = Some(createCampaign.newTeamCreationData.updatedAccount.internal_id),
                list = None,
                company = Some("Test Company"),
                city = Some("Pune"),
                country = Some("India"),
                timezone = Some("Asia/Kolkata"),
                created_at = None,

                state = Some("Maharashtra"),
                job_title = Some("SDE"),
                phone = Some("**********"),
                linkedin_url = Some("https://www.linkedin.com/in/animesh-kumar-9a1171191/")
              ))),
            campaignId = Some(CampaignId(createCampaign.campaignWithStatsAndEmail.id)),
            account = createCampaign.newTeamCreationData.updatedAccount,
            teamId = TeamId(createCampaign.campaignWithStatsAndEmail.team_id),
            accountId = AccountId(createCampaign.newTeamCreationData.updatedAccount.internal_id)
          )
        }
        //add auto email step to campaign
        addStep: CampaignStepVariant <- {
          CreateStepForCampaignFixture.createAutoEmailStepForCampaign(
            orgId = OrgId(createCampaign.newTeamCreationData.updatedAccount.org.id),
            teamId = TeamId(createCampaign.campaignWithStatsAndEmail.team_id),
            accountId = AccountId(createCampaign.newTeamCreationData.updatedAccount.internal_id),
            taId = createCampaign.newTeamCreationData.teamAccount.access_members.head.ta_id,
            campaignId = CampaignId(createCampaign.campaignWithStatsAndEmail.id),
          )
        }

        //campaign details
        campaign: Campaign <- Future.successful(
          campaignDAO.findCampaignForCampaignUtilsOnly(
            id = createCampaign.campaignWithStatsAndEmail.id,
            teamId = TeamId(createCampaign.campaignWithStatsAndEmail.team_id)
          ).get
        )

        //start campaign
        startCampaign: CampaignWithStatsAndEmail <- {
          StartCampaignFixture.startCampaign(
            accountId = AccountId(createCampaign.newTeamCreationData.updatedAccount.internal_id),
            teamId = TeamId(createCampaign.campaignWithStatsAndEmail.team_id),
            campaignWithStatsAndEmail = createCampaign.campaignWithStatsAndEmail,
            orgId = OrgId(createCampaign.newTeamCreationData.updatedAccount.org.id),
            taId = createCampaign.newTeamCreationData.teamAccount.access_members.head.ta_id,
            emailSetting = createCampaign.newEmailSetting,
            prospect_categories_custom_not_categorized = createCampaign.customNotCategorizedAndDoNotContactIds.not_categorized,
            prospect_categories_custom_do_not_contact = createCampaign.customNotCategorizedAndDoNotContactIds.do_not_contact,
            current_sending_email_accounts = createCampaign.newEmailSetting.id.get.emailSettingId.toInt
          )
        }
        addingEmailScheduled <- Future.fromTry {
          emailScheduledDAOService.saveEmailsToBeScheduledAndUpdateCampaignDataV2(
            emailsToBeScheduled = Vector(EmailScheduledNewFixture.generateEmailScheduledNew3.copy(
              campaign_id = Some(createCampaign.campaignWithStatsAndEmail.id),
              step_id = createCampaign.campaignWithStatsAndEmail.head_step_id,
              from_email = createCampaign.campaignWithStatsAndEmail.settings.campaign_email_settings.head.sender_email,
              scheduled_from_campaign = true,
              is_opening_step = true,
              sender_email_settings_id = createCampaign.campaignWithStatsAndEmail.settings.campaign_email_settings.head.sender_email_setting_id.emailSettingId,
              team_id = createCampaign.campaignWithStatsAndEmail.team_id,
              account_id = createCampaign.campaignWithStatsAndEmail.owner_id,
              receiver_email_settings_id = createCampaign.campaignWithStatsAndEmail.settings.campaign_email_settings.head.receiver_email_setting_id.emailSettingId,
              campaign_email_settings_id = createCampaign.campaignWithStatsAndEmail.settings.campaign_email_settings.head.id,
              prospect_id = Some(prospect.head.id),
              base_body = "body"

            )
            ),
            campaign_email_setting_id = createCampaign.campaignWithStatsAndEmail.settings.campaign_email_settings.head.id,
            emailSendingFlow = None,
            Logger = Logger
          )
        }

        emailSent <- Future.fromTry {
          emailSenderService.onEmailSent(
            emailSentId = addingEmailScheduled.head.email_scheduled_id,
            data = DefaultParametersFixtureForInitializingDataForReScheduling.defaultEmailToBeSent(
              emailSetting = initialData.emailSetting.get,
              campaign = campaign
            ),
            accountId = createCampaign.newTeamCreationData.updatedAccount.internal_id,
            sendEmailFromCampaignDetails = Some(SendEmailFromCampaignDetails(
              campaign_id = createCampaign.campaignWithStatsAndEmail.id,
              campaign_name = createCampaign.campaignWithStatsAndEmail.name,
              // stepDetails can be none when email is being sent manually from Inbox.
              // Example: sendNewEmailManually in InboxV3Service
              stepDetails = Some(StepDetails(
                step_id = addStep.step_id,
                step_name = addStep.label.getOrElse("step"),
                step_type =addStep.step_data.step_type
              ))
            )),
            prospectIdInCampaign = prospect.headOption.map(_.id),
            currentBillingCycleStartedAt = createCampaign.newTeamCreationData.updatedAccount.created_at,
            orgId = createCampaign.newTeamCreationData.updatedAccount.org.id,
            repTrackingHostId = 1,
            teamId = campaign.team_id
          )
        }

        readNew: Seq[EmailThreadFoundForCheckingReplies] <- Future.fromTry {
          emailScheduledDAO.findEmailThreadIdsWhileReplyTrackingNew(
            replyToHeaders = Seq(campaign.name),
            gmailThreadIds = Seq(),
            outlookConversationIds = Seq(),
            teamId = TeamId(campaign.team_id),
            inboxEmailSettingId = addingEmailScheduled.head.sender_email_settings_id
          )
        }


      } yield {

        readNew
      }


      result.map{v =>
        assert(v.nonEmpty)
      }.recover{e =>
        println(s"error ---- $e")
        assert(false)
      }

    }

  }

  describe("checkIfEmailIsAlreadySaved") {

    it("checkIfEmailIsAlreadySavedNew") {

      val result = for {

        initialData: InitialData <- Future.fromTry(NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData())

        createCampaign: NewCampaignCreationData <- CreateNewCampaignFixture.createNewCampaign(
          orgId = OrgId(initialData.account.org.id),
          accountId = AccountId(initialData.account.internal_id),
          accountName = s"${initialData.account.first_name.getOrElse("Animesh")} ${initialData.account.last_name.getOrElse("Kumar")}"
        )
        prospect <- Future.fromTry {
          ProspectFixtureForIntegrationTest.createUpdateOrAssignProspect(
            givenProspect = Some(Seq(
              ProspectCreateFormDataFixture.prospectCreateFormData.copy(
                email = Some("<EMAIL>"),
                first_name = Some("Animesh"),
                last_name = Some("Kumar"),
                owner_id = Some(createCampaign.newTeamCreationData.updatedAccount.internal_id),
                list = None,
                company = Some("Test Company"),
                city = Some("Pune"),
                country = Some("India"),
                timezone = Some("Asia/Kolkata"),
                created_at = None,

                state = Some("Maharashtra"),
                job_title = Some("SDE"),
                phone = Some("**********"),
                linkedin_url = Some("https://www.linkedin.com/in/animesh-kumar-9a1171191/")
              ))),
            campaignId = Some(CampaignId(createCampaign.campaignWithStatsAndEmail.id)),
            account = createCampaign.newTeamCreationData.updatedAccount,
            teamId = TeamId(createCampaign.campaignWithStatsAndEmail.team_id),
            accountId = AccountId(createCampaign.newTeamCreationData.updatedAccount.internal_id)
          )
        }
        //add auto email step to campaign
        addStep: CampaignStepVariant <- {
          CreateStepForCampaignFixture.createAutoEmailStepForCampaign(
            orgId = OrgId(createCampaign.newTeamCreationData.updatedAccount.org.id),
            teamId = TeamId(createCampaign.campaignWithStatsAndEmail.team_id),
            accountId = AccountId(createCampaign.newTeamCreationData.updatedAccount.internal_id),
            taId = createCampaign.newTeamCreationData.teamAccount.access_members.head.ta_id,
            campaignId = CampaignId(createCampaign.campaignWithStatsAndEmail.id),
          )
        }

        //campaign details
        campaign: Campaign <- Future.successful(
          campaignDAO.findCampaignForCampaignUtilsOnly(
            id = createCampaign.campaignWithStatsAndEmail.id,
            teamId = TeamId(createCampaign.campaignWithStatsAndEmail.team_id)
          ).get
        )

        //start campaign
        startCampaign: CampaignWithStatsAndEmail <- {
          StartCampaignFixture.startCampaign(
            accountId = AccountId(createCampaign.newTeamCreationData.updatedAccount.internal_id),
            teamId = TeamId(createCampaign.campaignWithStatsAndEmail.team_id),
            campaignWithStatsAndEmail = createCampaign.campaignWithStatsAndEmail,
            orgId = OrgId(createCampaign.newTeamCreationData.updatedAccount.org.id),
            taId = createCampaign.newTeamCreationData.teamAccount.access_members.head.ta_id,
            emailSetting = createCampaign.newEmailSetting,
            prospect_categories_custom_not_categorized = createCampaign.customNotCategorizedAndDoNotContactIds.not_categorized,
            prospect_categories_custom_do_not_contact = createCampaign.customNotCategorizedAndDoNotContactIds.do_not_contact,
            current_sending_email_accounts = createCampaign.newEmailSetting.id.get.emailSettingId.toInt
          )
        }
        addingEmailScheduled <- Future.fromTry {
          emailScheduledDAOService.saveEmailsToBeScheduledAndUpdateCampaignDataV2(
            emailsToBeScheduled = Vector(EmailScheduledNewFixture.generateEmailScheduledNew3.copy(
              campaign_id = Some(createCampaign.campaignWithStatsAndEmail.id),
              step_id = createCampaign.campaignWithStatsAndEmail.head_step_id,
              from_email = createCampaign.campaignWithStatsAndEmail.settings.campaign_email_settings.head.sender_email,
              scheduled_from_campaign = true,
              is_opening_step = true,
              sender_email_settings_id = createCampaign.campaignWithStatsAndEmail.settings.campaign_email_settings.head.sender_email_setting_id.emailSettingId,
              team_id = createCampaign.campaignWithStatsAndEmail.team_id,
              account_id = createCampaign.campaignWithStatsAndEmail.owner_id,
              receiver_email_settings_id = createCampaign.campaignWithStatsAndEmail.settings.campaign_email_settings.head.receiver_email_setting_id.emailSettingId,
              campaign_email_settings_id = createCampaign.campaignWithStatsAndEmail.settings.campaign_email_settings.head.id,
              prospect_id = Some(prospect.head.id),
              base_body = "body"

            )
            ),
            campaign_email_setting_id = createCampaign.campaignWithStatsAndEmail.settings.campaign_email_settings.head.id,
            emailSendingFlow = None,
            Logger = Logger
          )
        }

        emailSent <- Future.fromTry {
          emailSenderService.onEmailSent(
            emailSentId = addingEmailScheduled.head.email_scheduled_id,
            data = DefaultParametersFixtureForInitializingDataForReScheduling.defaultEmailToBeSent(
              emailSetting = initialData.emailSetting.get,
              campaign = campaign
            ),
            accountId = createCampaign.newTeamCreationData.updatedAccount.internal_id,
            sendEmailFromCampaignDetails = Some(SendEmailFromCampaignDetails(
              campaign_id = createCampaign.campaignWithStatsAndEmail.id,
              campaign_name = createCampaign.campaignWithStatsAndEmail.name,
              // stepDetails can be none when email is being sent manually from Inbox.
              // Example: sendNewEmailManually in InboxV3Service
              stepDetails = Some(StepDetails(
                step_id = addStep.step_id,
                step_name = addStep.label.getOrElse("step"),
                step_type =addStep.step_data.step_type
              ))
            )),
            prospectIdInCampaign = prospect.headOption.map(_.id),
            currentBillingCycleStartedAt = createCampaign.newTeamCreationData.updatedAccount.created_at,
            orgId = createCampaign.newTeamCreationData.updatedAccount.org.id,
            repTrackingHostId = 1,
            teamId = campaign.team_id
          )
        }

        readNew: Seq[String] <- Future.fromTry {
          emailScheduledDAO.checkIfEmailIsAlreadySavedNew(
            messageIds = Seq(campaign.name),
            teamId = TeamId(campaign.team_id),
            inboxEmailSettingId = addingEmailScheduled.head.sender_email_settings_id
          )
        }


      } yield {

         readNew
      }


      result.map{v =>
        assert(v.nonEmpty)
      }.recover{e =>
        println(s"error ---- $e")
        assert(false)
      }

    }

  }

  describe("findForWebhooks") {

    it("findForWebhooksNew") {

      val result = for {
        initialData: InitialData <- Future.fromTry(NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData())

        createCampaign: NewCampaignCreationData <- CreateNewCampaignFixture.createNewCampaign(
          orgId = OrgId(initialData.account.org.id),
          accountId = AccountId(initialData.account.internal_id),
          accountName = s"${initialData.account.first_name.getOrElse("Animesh")} ${initialData.account.last_name.getOrElse("Kumar")}"
        )
        prospect <- Future.fromTry {
          ProspectFixtureForIntegrationTest.createUpdateOrAssignProspect(
            givenProspect = Some(Seq(
              ProspectCreateFormDataFixture.prospectCreateFormData.copy(
                email = Some("<EMAIL>"),
                first_name = Some("Animesh"),
                last_name = Some("Kumar"),
                owner_id = Some(createCampaign.campaignWithStatsAndEmail.owner_id),
                company = Some("Test Company"),
                city = Some("Pune"),
                country = Some("India"),
                timezone = Some("Asia/Kolkata"),
                state = Some("Maharashtra"),
                job_title = Some("SDE"),
                phone = Some("**********"),
                linkedin_url = Some("https://www.linkedin.com/in/animesh-kumar-9a1171191/")
              ))),
            campaignId = Some(CampaignId(createCampaign.campaignWithStatsAndEmail.id)),
            account = createCampaign.newTeamCreationData.updatedAccount,
            teamId = TeamId(createCampaign.campaignWithStatsAndEmail.team_id),
            accountId = AccountId(createCampaign.newTeamCreationData.updatedAccount.internal_id)
          )
        }
        //add auto email step to campaign
        addStep: CampaignStepVariant <- {
          CreateStepForCampaignFixture.createAutoEmailStepForCampaign(
            orgId = OrgId(createCampaign.newTeamCreationData.updatedAccount.org.id),
            teamId = TeamId(createCampaign.campaignWithStatsAndEmail.team_id),
            accountId = AccountId(createCampaign.newTeamCreationData.updatedAccount.internal_id),
            taId = createCampaign.newTeamCreationData.teamAccount.access_members.head.ta_id,
            campaignId = CampaignId(createCampaign.campaignWithStatsAndEmail.id),
          )
        }

        //campaign details
        campaign: Campaign <- Future.successful(
          campaignDAO.findCampaignForCampaignUtilsOnly(
            id = createCampaign.campaignWithStatsAndEmail.id,
            teamId = TeamId(createCampaign.campaignWithStatsAndEmail.team_id)
          ).get
        )

        //start campaign
        startCampaign: CampaignWithStatsAndEmail <- {
          StartCampaignFixture.startCampaign(
            accountId = AccountId(createCampaign.newTeamCreationData.updatedAccount.internal_id),
            teamId = TeamId(createCampaign.campaignWithStatsAndEmail.team_id),
            campaignWithStatsAndEmail = createCampaign.campaignWithStatsAndEmail,
            orgId = OrgId(createCampaign.newTeamCreationData.updatedAccount.org.id),
            taId = createCampaign.newTeamCreationData.teamAccount.access_members.head.ta_id,
            emailSetting = createCampaign.newEmailSetting,
            prospect_categories_custom_not_categorized = createCampaign.customNotCategorizedAndDoNotContactIds.not_categorized,
            prospect_categories_custom_do_not_contact = createCampaign.customNotCategorizedAndDoNotContactIds.do_not_contact,
            current_sending_email_accounts = createCampaign.newEmailSetting.id.get.emailSettingId.toInt
          )
        }
        addingEmailScheduled <- Future.fromTry {
          emailScheduledDAOService.saveEmailsToBeScheduledAndUpdateCampaignDataV2(
            emailsToBeScheduled = Vector(EmailScheduledNewFixture.generateEmailScheduledNew3.copy(
              campaign_id = Some(createCampaign.campaignWithStatsAndEmail.id),
              step_id = createCampaign.campaignWithStatsAndEmail.head_step_id,
              from_email = createCampaign.campaignWithStatsAndEmail.settings.campaign_email_settings.head.sender_email,
              scheduled_from_campaign = true,
              is_opening_step = true,
              sender_email_settings_id = createCampaign.campaignWithStatsAndEmail.settings.campaign_email_settings.head.sender_email_setting_id.emailSettingId,
              team_id = createCampaign.campaignWithStatsAndEmail.team_id,
              account_id = createCampaign.campaignWithStatsAndEmail.owner_id,
              receiver_email_settings_id = createCampaign.campaignWithStatsAndEmail.settings.campaign_email_settings.head.receiver_email_setting_id.emailSettingId,
              campaign_email_settings_id = createCampaign.campaignWithStatsAndEmail.settings.campaign_email_settings.head.id,
              prospect_id = Some(prospect.head.id),
              base_body = "body"

            )
            ),
            campaign_email_setting_id = createCampaign.campaignWithStatsAndEmail.settings.campaign_email_settings.head.id,
            emailSendingFlow = None,
            Logger = Logger
          )
        }
        emailSent <- Future.fromTry {
          emailSenderService.onEmailSent(
            emailSentId = addingEmailScheduled.head.email_scheduled_id,
            data = DefaultParametersFixtureForInitializingDataForReScheduling.defaultEmailToBeSent(
              emailSetting = initialData.emailSetting.get,
              campaign = campaign
            ),
            accountId = createCampaign.newTeamCreationData.updatedAccount.internal_id,
            sendEmailFromCampaignDetails = Some(SendEmailFromCampaignDetails(
              campaign_id = createCampaign.campaignWithStatsAndEmail.id,
              campaign_name = createCampaign.campaignWithStatsAndEmail.name,
              // stepDetails can be none when email is being sent manually from Inbox.
              // Example: sendNewEmailManually in InboxV3Service
              stepDetails = Some(StepDetails(
                step_id = addStep.step_id,
                step_name = addStep.label.getOrElse("step"),
                step_type =addStep.step_data.step_type
              ))
            )),
            prospectIdInCampaign = prospect.headOption.map(_.id),
            currentBillingCycleStartedAt = createCampaign.newTeamCreationData.updatedAccount.created_at,
            orgId = createCampaign.newTeamCreationData.updatedAccount.org.id,
            repTrackingHostId = 1,
            teamId = campaign.team_id
          )
        }

        readNew: Seq[EmailReceivedForWebhook] <- Future.fromTry {
          emailScheduledDAO.findForWebhooksNew(
            emailScheduledIds = Seq(addingEmailScheduled.head.email_scheduled_id),
            teamId = (campaign.team_id),
            onlyNewReplies = false
          )
        }


      } yield {

        readNew
      }


      result.map{v =>
        assert(v.nonEmpty)
      }.recover{e =>
        println(s"error ---- $e")
        assert(false)
      }

    }

  }

  describe("findForWebhooksV2") {

    it("findForWebhooksV2New") {

      val result = for {

        initialData: InitialData <- Future.fromTry(NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData())

        createCampaign: NewCampaignCreationData <- CreateNewCampaignFixture.createNewCampaign(
          orgId = OrgId(initialData.account.org.id),
          accountId = AccountId(initialData.account.internal_id),
          accountName = s"${initialData.account.first_name.getOrElse("Animesh")} ${initialData.account.last_name.getOrElse("Kumar")}"
        )
        prospect: Seq[ProspectObject] <- Future.fromTry {
          ProspectFixtureForIntegrationTest.createUpdateOrAssignProspect(
            givenProspect = Some(Seq(
              ProspectCreateFormDataFixture.prospectCreateFormData.copy(
                email = Some("<EMAIL>"),
                first_name = Some("Animesh"),
                last_name = Some("Kumar"),
                owner_id = Some(createCampaign.campaignWithStatsAndEmail.owner_id),
                company = Some("Test Company"),
                city = Some("Pune"),
                country = Some("India"),
                timezone = Some("Asia/Kolkata"),
                state = Some("Maharashtra"),
                job_title = Some("SDE"),
                phone = Some("**********"),
                linkedin_url = Some("https://www.linkedin.com/in/animesh-kumar-9a1171191/")
              ))),
            campaignId = Some(CampaignId(createCampaign.campaignWithStatsAndEmail.id)),
            account = createCampaign.newTeamCreationData.updatedAccount,
            teamId = TeamId(createCampaign.campaignWithStatsAndEmail.team_id),
            accountId = AccountId(createCampaign.newTeamCreationData.updatedAccount.internal_id)
          )
        }
        //add auto email step to campaign
        addStep: CampaignStepVariant <- {
          CreateStepForCampaignFixture.createAutoEmailStepForCampaign(
            orgId = OrgId(createCampaign.newTeamCreationData.updatedAccount.org.id),
            teamId = TeamId(createCampaign.campaignWithStatsAndEmail.team_id),
            accountId = AccountId(createCampaign.newTeamCreationData.updatedAccount.internal_id),
            taId = createCampaign.newTeamCreationData.teamAccount.access_members.head.ta_id,
            campaignId = CampaignId(createCampaign.campaignWithStatsAndEmail.id),
          )
        }

        //campaign details
        campaign: Campaign <- Future.successful(
          campaignDAO.findCampaignForCampaignUtilsOnly(
            id = createCampaign.campaignWithStatsAndEmail.id,
            teamId = TeamId(createCampaign.campaignWithStatsAndEmail.team_id)
          ).get
        )

        //start campaign
        startCampaign: CampaignWithStatsAndEmail <- {
          StartCampaignFixture.startCampaign(
            accountId = AccountId(createCampaign.newTeamCreationData.updatedAccount.internal_id),
            teamId = TeamId(createCampaign.campaignWithStatsAndEmail.team_id),
            campaignWithStatsAndEmail = createCampaign.campaignWithStatsAndEmail,
            orgId = OrgId(createCampaign.newTeamCreationData.updatedAccount.org.id),
            taId = createCampaign.newTeamCreationData.teamAccount.access_members.head.ta_id,
            emailSetting = createCampaign.newEmailSetting,
            prospect_categories_custom_not_categorized = createCampaign.customNotCategorizedAndDoNotContactIds.not_categorized,
            prospect_categories_custom_do_not_contact = createCampaign.customNotCategorizedAndDoNotContactIds.do_not_contact,
            current_sending_email_accounts = createCampaign.newEmailSetting.id.get.emailSettingId.toInt
          )
        }
        addingEmailScheduled <- Future.fromTry {
          emailScheduledDAOService.saveEmailsToBeScheduledAndUpdateCampaignDataV2(
            emailsToBeScheduled = Vector(EmailScheduledNewFixture.generateEmailScheduledNew3.copy(
              campaign_id = Some(createCampaign.campaignWithStatsAndEmail.id),
              step_id = createCampaign.campaignWithStatsAndEmail.head_step_id,
              from_email = createCampaign.campaignWithStatsAndEmail.settings.campaign_email_settings.head.sender_email,
              scheduled_from_campaign = true,
              is_opening_step = true,
              sender_email_settings_id = createCampaign.campaignWithStatsAndEmail.settings.campaign_email_settings.head.sender_email_setting_id.emailSettingId,
              team_id = createCampaign.campaignWithStatsAndEmail.team_id,
              account_id = createCampaign.campaignWithStatsAndEmail.owner_id,
              receiver_email_settings_id = createCampaign.campaignWithStatsAndEmail.settings.campaign_email_settings.head.receiver_email_setting_id.emailSettingId,
              campaign_email_settings_id = createCampaign.campaignWithStatsAndEmail.settings.campaign_email_settings.head.id,
              prospect_id = Some(prospect.head.id),
              base_body = "body"

            )
            ),
            campaign_email_setting_id = createCampaign.campaignWithStatsAndEmail.settings.campaign_email_settings.head.id,
            emailSendingFlow = None,
            Logger = Logger
          )
        }
        emailSent <- Future.fromTry {
          emailSenderService.onEmailSent(
            emailSentId = addingEmailScheduled.head.email_scheduled_id,
            data = DefaultParametersFixtureForInitializingDataForReScheduling.defaultEmailToBeSent(
              emailSetting = initialData.emailSetting.get,
              campaign = campaign
            ),
            accountId = createCampaign.newTeamCreationData.updatedAccount.internal_id,
            sendEmailFromCampaignDetails = Some(SendEmailFromCampaignDetails(
              campaign_id = createCampaign.campaignWithStatsAndEmail.id,
              campaign_name = createCampaign.campaignWithStatsAndEmail.name,
              // stepDetails can be none when email is being sent manually from Inbox.
              // Example: sendNewEmailManually in InboxV3Service
              stepDetails = Some(StepDetails(
                step_id = addStep.step_id,
                step_name = addStep.label.getOrElse("step"),
                step_type = addStep.step_data.step_type
              ))
            )),
            prospectIdInCampaign = prospect.headOption.map(_.id),
            currentBillingCycleStartedAt = createCampaign.newTeamCreationData.updatedAccount.created_at,
            orgId = createCampaign.newTeamCreationData.updatedAccount.org.id,
            repTrackingHostId = 1,
            teamId = campaign.team_id
          )
        }

        readNew: Seq[EmailReceivedForWebhookV2] <- Future {
          emailScheduledDAO.findForWebhooksV2New(
            emailScheduledIds = Seq(addingEmailScheduled.head.email_scheduled_id),
            teamId = TeamId(campaign.team_id),
            onlyNewReplies = false
          )
        }


      } yield {

        readNew
      }


      result.map { v =>
        assert(v.nonEmpty)
      }.recover { e =>
        println(s"error ---- $e")
        assert(false)
      }

    }

  }

  describe("findNewReplyForZapier") {

    it("findNewReplyForZapierNew") {

      val result = for {

        initialData: InitialData <- Future.fromTry(NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData())

        createCampaign: NewCampaignCreationData <- CreateNewCampaignFixture.createNewCampaign(
          orgId = OrgId(initialData.account.org.id),
          accountId = AccountId(initialData.account.internal_id),
          accountName = s"${initialData.account.first_name.getOrElse("Animesh")} ${initialData.account.last_name.getOrElse("Kumar")}"
        )
        prospect: Seq[ProspectObject] <- Future.fromTry {
          ProspectFixtureForIntegrationTest.createUpdateOrAssignProspect(
            givenProspect = Some(Seq(
              ProspectCreateFormDataFixture.prospectCreateFormData.copy(
                email = Some("<EMAIL>"),
                first_name = Some("Animesh"),
                last_name = Some("Kumar"),
                owner_id = Some(createCampaign.campaignWithStatsAndEmail.owner_id),
                company = Some("Test Company"),
                city = Some("Pune"),
                country = Some("India"),
                timezone = Some("Asia/Kolkata"),
                state = Some("Maharashtra"),
                job_title = Some("SDE"),
                phone = Some("**********"),
                linkedin_url = Some("https://www.linkedin.com/in/animesh-kumar-9a1171191/")
              ))),
            campaignId = Some(CampaignId(createCampaign.campaignWithStatsAndEmail.id)),
            account = createCampaign.newTeamCreationData.updatedAccount,
            teamId = TeamId(createCampaign.campaignWithStatsAndEmail.team_id),
            accountId = AccountId(createCampaign.newTeamCreationData.updatedAccount.internal_id)
          )
        }
        //add auto email step to campaign
        addStep: CampaignStepVariant <- {
          CreateStepForCampaignFixture.createAutoEmailStepForCampaign(
            orgId = OrgId(createCampaign.newTeamCreationData.updatedAccount.org.id),
            teamId = TeamId(createCampaign.campaignWithStatsAndEmail.team_id),
            accountId = AccountId(createCampaign.newTeamCreationData.updatedAccount.internal_id),
            taId = createCampaign.newTeamCreationData.teamAccount.access_members.head.ta_id,
            campaignId = CampaignId(createCampaign.campaignWithStatsAndEmail.id),
          )
        }

        //campaign details
        campaign: Campaign <- Future.successful(
          campaignDAO.findCampaignForCampaignUtilsOnly(
            id = createCampaign.campaignWithStatsAndEmail.id,
            teamId = TeamId(createCampaign.campaignWithStatsAndEmail.team_id)
          ).get
        )

        //start campaign
        startCampaign: CampaignWithStatsAndEmail <- {
          StartCampaignFixture.startCampaign(
            accountId = AccountId(createCampaign.newTeamCreationData.updatedAccount.internal_id),
            teamId = TeamId(createCampaign.campaignWithStatsAndEmail.team_id),
            campaignWithStatsAndEmail = createCampaign.campaignWithStatsAndEmail,
            orgId = OrgId(createCampaign.newTeamCreationData.updatedAccount.org.id),
            taId = createCampaign.newTeamCreationData.teamAccount.access_members.head.ta_id,
            emailSetting = createCampaign.newEmailSetting,
            prospect_categories_custom_not_categorized = createCampaign.customNotCategorizedAndDoNotContactIds.not_categorized,
            prospect_categories_custom_do_not_contact = createCampaign.customNotCategorizedAndDoNotContactIds.do_not_contact,
            current_sending_email_accounts = createCampaign.newEmailSetting.id.get.emailSettingId.toInt
          )
        }
        addingEmailScheduled <- Future.fromTry {
          emailScheduledDAOService.saveEmailsToBeScheduledAndUpdateCampaignDataV2(
            emailsToBeScheduled = Vector(EmailScheduledNewFixture.generateEmailScheduledNew3.copy(
              campaign_id = Some(createCampaign.campaignWithStatsAndEmail.id),
              step_id = createCampaign.campaignWithStatsAndEmail.head_step_id,
              from_email = createCampaign.campaignWithStatsAndEmail.settings.campaign_email_settings.head.sender_email,
              scheduled_from_campaign = true,
              is_opening_step = true,
              sender_email_settings_id = createCampaign.campaignWithStatsAndEmail.settings.campaign_email_settings.head.sender_email_setting_id.emailSettingId,
              team_id = createCampaign.campaignWithStatsAndEmail.team_id,
              account_id = createCampaign.campaignWithStatsAndEmail.owner_id,
              receiver_email_settings_id = createCampaign.campaignWithStatsAndEmail.settings.campaign_email_settings.head.receiver_email_setting_id.emailSettingId,
              campaign_email_settings_id = createCampaign.campaignWithStatsAndEmail.settings.campaign_email_settings.head.id,
              prospect_id = Some(prospect.head.id),
              base_body = "body"

            )
            ),
            campaign_email_setting_id = createCampaign.campaignWithStatsAndEmail.settings.campaign_email_settings.head.id,
            emailSendingFlow = None,
            Logger = Logger
          )
        }
        emailSent <- Future.fromTry {
          emailSenderService.onEmailSent(
            emailSentId = addingEmailScheduled.head.email_scheduled_id,
            data = DefaultParametersFixtureForInitializingDataForReScheduling.defaultEmailToBeSent(
              emailSetting = initialData.emailSetting.get,
              campaign = campaign
            ),
            accountId = createCampaign.newTeamCreationData.updatedAccount.internal_id,
            sendEmailFromCampaignDetails = Some(SendEmailFromCampaignDetails(
              campaign_id = createCampaign.campaignWithStatsAndEmail.id,
              campaign_name = createCampaign.campaignWithStatsAndEmail.name,
              // stepDetails can be none when email is being sent manually from Inbox.
              // Example: sendNewEmailManually in InboxV3Service
              stepDetails = Some(StepDetails(
                step_id = addStep.step_id,
                step_name = addStep.label.getOrElse("step"),
                step_type = addStep.step_data.step_type
              ))
            )),
            prospectIdInCampaign = prospect.headOption.map(_.id),
            currentBillingCycleStartedAt = createCampaign.newTeamCreationData.updatedAccount.created_at,
            orgId = createCampaign.newTeamCreationData.updatedAccount.org.id,
            repTrackingHostId = 1,
            teamId = campaign.team_id
          )
        }

        readNew: Seq[EmailReceivedForWebhook] <- Future.fromTry {
          emailScheduledDAO.findNewReplyForZapierNew(
            permittedAccountIds = Seq(initialData.account.internal_id),
            teamId = (campaign.team_id),
          )
        }


      } yield {

        readNew
      }


      result.map { v =>
        assert(true) //todo: turn this into integration test
      }.recover { e =>
        println(s"error ---- $e")
        assert(false)
      }

    }

  }

  describe("findThreadMessages") {

    // first create a campaign withe email messages and threads
    val result: Future[Seq[MessageObject.EmailMessageObject]] = for {

      initialData: InitialData <- Future.fromTry(NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData())

      createCampaign: NewCampaignCreationData <- CreateNewCampaignFixture.createNewCampaign(
        orgId = OrgId(initialData.account.org.id),
        accountId = AccountId(initialData.account.internal_id),
        accountName = s"${initialData.account.first_name.getOrElse("Animesh")} ${initialData.account.last_name.getOrElse("Kumar")}"
      )
      prospect: Seq[ProspectObject] <- Future.fromTry {
        ProspectFixtureForIntegrationTest.createUpdateOrAssignProspect(
          givenProspect = Some(Seq(
            ProspectCreateFormDataFixture.prospectCreateFormData.copy(
              email = Some("<EMAIL>"),
              first_name = Some("Animesh"),
              last_name = Some("Kumar"),
              owner_id = Some(createCampaign.campaignWithStatsAndEmail.owner_id),
              company = Some("Test Company"),
              city = Some("Pune"),
              country = Some("India"),
              timezone = Some("Asia/Kolkata"),
              state = Some("Maharashtra"),
              job_title = Some("SDE"),
              phone = Some("**********"),
              linkedin_url = Some("https://www.linkedin.com/in/animesh-kumar-9a1171191/")
            ))),
          campaignId = Some(CampaignId(createCampaign.campaignWithStatsAndEmail.id)),
          account = createCampaign.newTeamCreationData.updatedAccount,
          teamId = TeamId(createCampaign.campaignWithStatsAndEmail.team_id),
          accountId = AccountId(createCampaign.newTeamCreationData.updatedAccount.internal_id)
        )
      }
      //add auto email step to campaign
      addStep: CampaignStepVariant <- {
        CreateStepForCampaignFixture.createAutoEmailStepForCampaign(
          orgId = OrgId(createCampaign.newTeamCreationData.updatedAccount.org.id),
          teamId = TeamId(createCampaign.campaignWithStatsAndEmail.team_id),
          accountId = AccountId(createCampaign.newTeamCreationData.updatedAccount.internal_id),
          taId = createCampaign.newTeamCreationData.teamAccount.access_members.head.ta_id,
          campaignId = CampaignId(createCampaign.campaignWithStatsAndEmail.id),
        )
      }

      //campaign details
      campaign: Campaign <- Future.successful(
        campaignDAO.findCampaignForCampaignUtilsOnly(
          id = createCampaign.campaignWithStatsAndEmail.id,
          teamId = TeamId(createCampaign.campaignWithStatsAndEmail.team_id)
        ).get
      )

      //start campaign
      startCampaign: CampaignWithStatsAndEmail <- {
        StartCampaignFixture.startCampaign(
          accountId = AccountId(createCampaign.newTeamCreationData.updatedAccount.internal_id),
          teamId = TeamId(createCampaign.campaignWithStatsAndEmail.team_id),
          campaignWithStatsAndEmail = createCampaign.campaignWithStatsAndEmail,
          orgId = OrgId(createCampaign.newTeamCreationData.updatedAccount.org.id),
          taId = createCampaign.newTeamCreationData.teamAccount.access_members.head.ta_id,
          emailSetting = createCampaign.newEmailSetting,
          prospect_categories_custom_not_categorized = createCampaign.customNotCategorizedAndDoNotContactIds.not_categorized,
          prospect_categories_custom_do_not_contact = createCampaign.customNotCategorizedAndDoNotContactIds.do_not_contact,
          current_sending_email_accounts = createCampaign.newEmailSetting.id.get.emailSettingId.toInt
        )
      }
      addingEmailScheduled <- Future.fromTry {
        emailScheduledDAOService.saveEmailsToBeScheduledAndUpdateCampaignDataV2(
          emailsToBeScheduled = Vector(EmailScheduledNewFixture.generateEmailScheduledNew3.copy(
            campaign_id = Some(createCampaign.campaignWithStatsAndEmail.id),
            step_id = createCampaign.campaignWithStatsAndEmail.head_step_id,
            from_email = createCampaign.campaignWithStatsAndEmail.settings.campaign_email_settings.head.sender_email,
            scheduled_from_campaign = true,
            is_opening_step = true,
            sender_email_settings_id = createCampaign.campaignWithStatsAndEmail.settings.campaign_email_settings.head.sender_email_setting_id.emailSettingId,
            team_id = createCampaign.campaignWithStatsAndEmail.team_id,
            account_id = createCampaign.campaignWithStatsAndEmail.owner_id,
            receiver_email_settings_id = createCampaign.campaignWithStatsAndEmail.settings.campaign_email_settings.head.receiver_email_setting_id.emailSettingId,
            campaign_email_settings_id = createCampaign.campaignWithStatsAndEmail.settings.campaign_email_settings.head.id,
            prospect_id = Some(prospect.head.id),
            base_body = "body"

          )
          ),
          campaign_email_setting_id = createCampaign.campaignWithStatsAndEmail.settings.campaign_email_settings.head.id,
          emailSendingFlow = None,
          Logger = Logger
        )
      }
      emailSent <- Future.fromTry {
        emailSenderService.onEmailSent(
          emailSentId = addingEmailScheduled.head.email_scheduled_id,
          data = DefaultParametersFixtureForInitializingDataForReScheduling.defaultEmailToBeSent(
            emailSetting = initialData.emailSetting.get,
            campaign = campaign
          ),
          accountId = createCampaign.newTeamCreationData.updatedAccount.internal_id,
          sendEmailFromCampaignDetails = Some(SendEmailFromCampaignDetails(
            campaign_id = createCampaign.campaignWithStatsAndEmail.id,
            campaign_name = createCampaign.campaignWithStatsAndEmail.name,
            // stepDetails can be none when email is being sent manually from Inbox.
            // Example: sendNewEmailManually in InboxV3Service
            stepDetails = Some(StepDetails(
              step_id = addStep.step_id,
              step_name = addStep.label.getOrElse("step"),
              step_type = addStep.step_data.step_type
            ))
          )),
          prospectIdInCampaign = prospect.headOption.map(_.id),
          currentBillingCycleStartedAt = createCampaign.newTeamCreationData.updatedAccount.created_at,
          orgId = createCampaign.newTeamCreationData.updatedAccount.org.id,
          repTrackingHostId = 1,
          teamId = campaign.team_id
        )
      }

      readNew: Seq[MessageObject.EmailMessageObject] <- Future.fromTry {
        emailScheduledDAO.findThreadMessagesNew(
          threadId = emailSent.email_thread_id.get,
          teamId = TeamId(campaign.team_id),
          allTrackingDomains = Seq(),
          FindThreadMessageFlow.InboxReplyDraft
        )
      }


    } yield {

      readNew
    }


    it("findThreadMessagesNew") {

      result.map{v =>
        assert(v.nonEmpty)
      }.recover{e =>
        println(s"error ---- $e")
        assert(false)
      }

    }

    it("should return threads") {

      getExistingEmailThreadAndTeamId() match {
        case Failure(_) => assert(false)

        case Success(threadAndtid) =>
          val res = emailScheduledDAOService.findThreadMessages(
            threadId = threadAndtid.id,
            allTrackingDomains = Seq(),
            teamId = threadAndtid.teamId,
            findThreadMessageFlow = FindThreadMessageFlow.GetConversationEmails
          )

          assert(res.isSuccess)
          assert(res.get.nonEmpty)
      }



    }

    it("should return single thread for inbox reply draft") {

      getExistingEmailThreadAndTeamId() match {
        case Failure(_) => assert(false)

        case Success(threadAndtid) =>
          val res = emailScheduledDAOService.findThreadMessages(
            threadId = threadAndtid.id,
            allTrackingDomains = Seq(),
            teamId = threadAndtid.teamId,
            findThreadMessageFlow = FindThreadMessageFlow.InboxReplyDraft
          )

          assert(res.isSuccess)
          assert(res.get.length == 1)
      }


    }
  }

  describe("findOneForFetchOutlookMsgId") {
    it("should be successful") {

      val result = for {

        initialData: InitialData <- Future.fromTry(NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData())


        createCampaign: NewCampaignCreationData <- CreateNewCampaignFixture.createNewCampaign(
          orgId = OrgId(initialData.account.org.id),
          accountId = AccountId(initialData.account.internal_id),
          accountName = s"${initialData.account.first_name.getOrElse("Animesh")} ${initialData.account.last_name.getOrElse("Kumar")}"
        )
        prospect: Seq[ProspectObject] <- Future.fromTry {
          ProspectFixtureForIntegrationTest.createUpdateOrAssignProspect(
            givenProspect = Some(Seq(
              ProspectCreateFormDataFixture.prospectCreateFormData.copy(
                email = Some("<EMAIL>"),
                first_name = Some("Animesh"),
                last_name = Some("Kumar"),
                owner_id = Some(createCampaign.campaignWithStatsAndEmail.owner_id),
                company = Some("Test Company"),
                city = Some("Pune"),
                country = Some("India"),
                timezone = Some("Asia/Kolkata"),
                state = Some("Maharashtra"),
                job_title = Some("SDE"),
                phone = Some("**********"),
                linkedin_url = Some("https://www.linkedin.com/in/animesh-kumar-9a1171191/")
              ))),
            campaignId = Some(CampaignId(createCampaign.campaignWithStatsAndEmail.id)),
            account = createCampaign.newTeamCreationData.updatedAccount,
            teamId = TeamId(createCampaign.campaignWithStatsAndEmail.team_id),
            accountId = AccountId(createCampaign.newTeamCreationData.updatedAccount.internal_id)
          )
        }
        //add auto email step to campaign
        addStep: CampaignStepVariant <- {
          CreateStepForCampaignFixture.createAutoEmailStepForCampaign(
            orgId = OrgId(createCampaign.newTeamCreationData.updatedAccount.org.id),
            teamId = TeamId(createCampaign.campaignWithStatsAndEmail.team_id),
            accountId = AccountId(createCampaign.newTeamCreationData.updatedAccount.internal_id),
            taId = createCampaign.newTeamCreationData.teamAccount.access_members.head.ta_id,
            campaignId = CampaignId(createCampaign.campaignWithStatsAndEmail.id),
          )
        }

        //campaign details
        campaign: Campaign <- Future.successful {
          campaignDAO.findCampaignForCampaignUtilsOnly(
            id = createCampaign.campaignWithStatsAndEmail.id,
            teamId = TeamId(createCampaign.campaignWithStatsAndEmail.team_id)
          ).get
        }

        //start campaign
        startCampaign: CampaignWithStatsAndEmail <- {
          StartCampaignFixture.startCampaign(
            accountId = AccountId(createCampaign.newTeamCreationData.updatedAccount.internal_id),
            teamId = TeamId(createCampaign.campaignWithStatsAndEmail.team_id),
            campaignWithStatsAndEmail = createCampaign.campaignWithStatsAndEmail,
            orgId = OrgId(createCampaign.newTeamCreationData.updatedAccount.org.id),
            taId = createCampaign.newTeamCreationData.teamAccount.access_members.head.ta_id,
            emailSetting = createCampaign.newEmailSetting,
            prospect_categories_custom_not_categorized = createCampaign.customNotCategorizedAndDoNotContactIds.not_categorized,
            prospect_categories_custom_do_not_contact = createCampaign.customNotCategorizedAndDoNotContactIds.do_not_contact,
            current_sending_email_accounts = createCampaign.newEmailSetting.id.get.emailSettingId.toInt
          )
        }
        addingEmailScheduled <- Future.fromTry {
          emailScheduledDAOService.saveEmailsToBeScheduledAndUpdateCampaignDataV2(
            emailsToBeScheduled = Vector(EmailScheduledNewFixture.generateEmailScheduledNew3.copy(
              campaign_id = Some(createCampaign.campaignWithStatsAndEmail.id),
              step_id = createCampaign.campaignWithStatsAndEmail.head_step_id,
              from_email = createCampaign.campaignWithStatsAndEmail.settings.campaign_email_settings.head.sender_email,
              scheduled_from_campaign = true,
              is_opening_step = true,
              sender_email_settings_id = createCampaign.campaignWithStatsAndEmail.settings.campaign_email_settings.head.sender_email_setting_id.emailSettingId,
              team_id = createCampaign.campaignWithStatsAndEmail.team_id,
              account_id = createCampaign.campaignWithStatsAndEmail.owner_id,
              receiver_email_settings_id = createCampaign.campaignWithStatsAndEmail.settings.campaign_email_settings.head.receiver_email_setting_id.emailSettingId,
              campaign_email_settings_id = createCampaign.campaignWithStatsAndEmail.settings.campaign_email_settings.head.id,
              prospect_id = Some(prospect.head.id),
              base_body = "body"

            )
            ),
            campaign_email_setting_id = createCampaign.campaignWithStatsAndEmail.settings.campaign_email_settings.head.id,
            emailSendingFlow = None,
            Logger = Logger
          )
        }
        emailSent <- Future.fromTry {
          emailSenderService.onEmailSent(
            emailSentId = addingEmailScheduled.head.email_scheduled_id,
            data = DefaultParametersFixtureForInitializingDataForReScheduling.defaultEmailToBeSent(
              emailSetting = initialData.emailSetting.get,
              campaign = campaign
            ).copy(outlook_conversation_id = Some("outlook_conversation_id+findOneForFetchOutlookMsgId")),
            accountId = createCampaign.newTeamCreationData.updatedAccount.internal_id,
            sendEmailFromCampaignDetails = Some(SendEmailFromCampaignDetails(
              campaign_id = createCampaign.campaignWithStatsAndEmail.id,
              campaign_name = createCampaign.campaignWithStatsAndEmail.name,
              // stepDetails can be none when email is being sent manually from Inbox.
              // Example: sendNewEmailManually in InboxV3Service
              stepDetails = Some(StepDetails(
                step_id = addStep.step_id,
                step_name = addStep.label.getOrElse("step"),
                step_type = addStep.step_data.step_type
              ))
            )),
            prospectIdInCampaign = prospect.headOption.map(_.id),
            currentBillingCycleStartedAt = createCampaign.newTeamCreationData.updatedAccount.created_at,
            orgId = createCampaign.newTeamCreationData.updatedAccount.org.id,
            repTrackingHostId = 1,
            teamId = campaign.team_id
          )
        }

        updateSentAt <- Future.fromTry {

          EmailScheduledDAO.updateSentAtForfindOneForFetchOutlookMsgId(
            campaign_id = CampaignId(campaign.id),
            teamId = TeamId(campaign.team_id),
            sentAt = DateTime.now().minusMinutes(20),
            email_scheduled_id = emailSent.id.get
          )
        }
        add_microsoft_refresh_token <- Future.fromTry{
          println(s"emailSettingId ${initialData.emailSetting}")
          microsoftOAuth.updateAccessTokenAndRefreshToken(
          emailSettingId = campaign.settings.campaign_email_settings.head.sender_email_setting_id,
          data = EmailSettingUpdateAccessToken(
            oauth2_refresh_token = "oauth2_refresh_token+findOneForFetchOutlookMsgId",
            oauth2_access_token = "oauth2_access_token+findOneForFetchOutlookMsgId",
            oauth2_token_expires_in = 36000,
            oauth2_access_token_expires_at = DateTime.now().plusDays(10000)
          )
        )}
        read: Option[InternetMsgIDForFetchOutlookMsgId] <- Future {
          emailScheduledDAO.findOneForFetchOutlookMsgId()

        }




      } yield {

        read
      }


      result.map { v =>
        assert(v.isDefined)
      }.recover { e =>
        println(s"error ---- ${LogHelpers.getStackTraceAsString(e)}")
        assert(false)
      }

    }
  }


  describe("getInboxPlacementCheckReceivedEmailData") {
    it("should return success") {
      val res: Try[Option[EmailHeaderForInboxPlacementCheckAnalysis]] = emailScheduledDAO.getInboxPlacementCheckReceivedEmailData(
        inboxPlacementCheckTrackedEmails = InboxPlacementCheckTrackedEmails(
          id = InboxPlacementCheckLogId(1L), receiverEmailSettingId = EmailSettingId(1L), receiverTeamId = TeamId(1L), emailMessageId = "message_id"
        )
      )

      assert(res.isSuccess)
    }
  }

  describe("updateProspectIdForMergeDuplicates") {
    it("should return updated email_scheduled_ids") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
      val outlookConvId: Option[String] = Some("outlook_conversation_id+abcd")

      val updatedIds: Future[(List[EmailScheduledId], Long)] = for {

        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 4
        )
        prospects: Seq[ProspectObject] <- Future {
          initialData.prospectsResult
        }

        (duplicate: Long, master: Long) <- Future {
          (prospects.head.id, prospects.last.id)
        }

        addingEmailScheduled <- Future.fromTry {
          emailScheduledDAOService.saveEmailsToBeScheduledAndUpdateCampaignDataV2(
            emailsToBeScheduled = Vector(EmailScheduledNewFixture.generateEmailScheduledNew3.copy(
              campaign_id = Some(createAndStartCampaignData.createCampaign.id),
              step_id = createAndStartCampaignData.createCampaign.head_step_id,
              from_email = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.sender_email,
              scheduled_from_campaign = true,
              is_opening_step = true,
              sender_email_settings_id = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.sender_email_setting_id.emailSettingId,
              team_id = createAndStartCampaignData.createCampaign.team_id,
              account_id = createAndStartCampaignData.createCampaign.owner_id,
              receiver_email_settings_id = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.receiver_email_setting_id.emailSettingId,
              campaign_email_settings_id = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.id,
              prospect_id = Some(prospects.head.id),
              base_body = "body"

            )
            ),
            campaign_email_setting_id = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.id,
            emailSendingFlow = None,
            Logger = Logger
          )
        }
        emailSent <- Future.fromTry {
          emailSenderService.onEmailSent(
            emailSentId = addingEmailScheduled.head.email_scheduled_id,
            data = DefaultParametersFixtureForInitializingDataForReScheduling.defaultEmailToBeSent(
              emailSetting = initialData.emailSetting.get,
              campaign = createAndStartCampaignData.campaign
            ).copy(outlook_conversation_id = outlookConvId, subject = "subject"),
            accountId = createAndStartCampaignData.createCampaign.owner_id,
            sendEmailFromCampaignDetails = Some(SendEmailFromCampaignDetails(
              campaign_id = createAndStartCampaignData.createCampaign.id,
              campaign_name = createAndStartCampaignData.createCampaign.name,
              // stepDetails can be none when email is being sent manually from Inbox.
              // Example: sendNewEmailManually in InboxV3Service
              stepDetails = None
            )),
            prospectIdInCampaign = Some(duplicate),
            currentBillingCycleStartedAt = initialData.account.created_at,
            orgId = initialData.account.org.id,
            repTrackingHostId = 1,
            teamId = initialData.head_team_id
          )
        }

        updatedIds: List[EmailScheduledId] <- Future.fromTry {
          val dbAndSession: DbAndSession = dbUtils.startLocalTx()
          val db = dbAndSession.db
          implicit val session: DBSession = dbAndSession.session
          val res: Try[List[EmailScheduledId]] = emailScheduledDAO.updateProspectIdForMergeDuplicates(
            duplicateProspects = List(ProspectIdAndPotentialDuplicateProspectId(
              prospectId = ProspectId(duplicate), potentialDuplicateProspectId = PotentialDuplicateProspectId(1), isMasterProspect = false
            )),
            teamId = TeamId(createAndStartCampaignData.createCampaign.team_id),
            masterProspectId = ProspectId(master)
          )
          dbUtils.commitAndCloseSession(db = db)
          res
        }

      } yield {
        (updatedIds, emailSent.id.get)
      }

      updatedIds.map(ids => {
        assert(ids._1.nonEmpty && ids._1.length == 1 && ids._1.head.id == ids._2)
      }).recover({ case e =>
        println(s"error::: ${LogHelpers.getStackTraceAsString(e)}")
        assert(false)
      })
    }
  }

}
