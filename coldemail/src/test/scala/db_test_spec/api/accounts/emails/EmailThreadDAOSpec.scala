package db_test_spec.api.accounts.emails

import api.accounts.{Account, ReplyHandling, TeamId}
import api.accounts.models.{AccountId, OrgId}
import api.campaigns.{Campaign, CampaignStepVariant, CampaignWithStatsAndEmail}
import api.campaigns.models.{CampaignName, SendEmailFromCampaignDetails, StepDetails}
import api.campaigns.services.CampaignId
import api.emails.models.InboxType
import api.emails.{ConversationsSearchResponse, DBEmailMessagesSavedResponse, EmailMessageTracked, EmailSearchQueryResponse, EmailSetting, EmailThreadUpdateLatestEmailData}
import api.prospects.dao.ProspectIdAndPotentialDuplicateProspectId
import api.prospects.models.{PotentialDuplicateProspectId, ProspectCategory, ProspectId, StepId}
import api.search.SearchQuery
import api.team_inbox.model.FolderType
import app.test_fixtures.prospect.ProspectCreateFormDataFixture
import db_test_spec.api.accounts.emails.dao.EmailScheduledDAO
import db_test_spec.api.accounts.fixtures.{EmailScheduledNewFixture, NewAccountAndEmailSettingData}
import db_test_spec.api.campaigns.dao.CampaignProspectTestDAO
import db_test_spec.api.campaigns.fixtures.{CreateNewCampaignFixture, CreateStepForCampaignFixture, NewCampaignCreationData, StartCampaignFixture}
import db_test_spec.api.campaigns.test_utils.{CampaignUtils, CreateAndStartCampaignData}
import db_test_spec.api.emails.fixtures.DefaultEmailScheduledParameterFixtures.generateEmailMessageTracked
import db_test_spec.api.prospects.fixtures.ProspectFixtureForIntegrationTest
import db_test_spec.api.scheduler.dao.SchedulerTestDAO
import db_test_spec.api.scheduler.fixtures.{DefaultParametersFixtureForInitializingDataForReScheduling, ScheduleTaskFixture}
import db_test_spec.api.{DbTestingBeforeAllAndAfterAll, InitialData, SchedulerIntegrationTestResult}
import db_test_spec.team_inbox.fixtures.TeamInboxFixtureForIntegrationTest
import db_test_spec.utils.SrRandomTestUtils
import eventframework.ProspectObject
import io.smartreach.esp.api.emails.{EmailSettingId, IEmailAddress}
import io.smartreach.esp.api.microsoftOAuth.EmailSettingUpdateAccessToken
import org.joda.time.DateTime
import sr_scheduler.models.ChannelData.EmailChannelData
import utils.SRLogger
import utils.email.EmailReplyStatus
import utils.helpers.LogHelpers
import utils.mq.channel_scheduler.channels.ScheduleTasksData

import scala.concurrent.Future
import scala.util.Try

class EmailThreadDAOSpec extends DbTestingBeforeAllAndAfterAll{


  val updateData = EmailThreadUpdateLatestEmailData(
    email_thread_id = 1L,
    by_account = true,
    latest_email_id = 30L,
    sent_at = DateTime.now(),
    latest_sent_by_admin_at = Some(DateTime.now()),
    by_prospect = false,
    folderType = FolderType.PROSPECTS
  )

  describe("_updateLatestEmailId"){
    it("should return success if there are emails by account"){

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
      val outlookConvId: Option[String] = Some("outlook_conversation_id+abcd")

      val result = for {

        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 4
        )
        prospects: Seq[ProspectObject] <- Future {
          initialData.prospectsResult
        }

        (duplicate: Long, master: Long) <- Future {
          (prospects.head.id, prospects.last.id)
        }

        addingEmailScheduled <- Future.fromTry {
          emailScheduledDAOService.saveEmailsToBeScheduledAndUpdateCampaignDataV2(
            emailsToBeScheduled = Vector(EmailScheduledNewFixture.generateEmailScheduledNew3.copy(
              campaign_id = Some(createAndStartCampaignData.createCampaign.id),
              step_id = createAndStartCampaignData.createCampaign.head_step_id,
              from_email = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.sender_email,
              scheduled_from_campaign = true,
              is_opening_step = true,
              sender_email_settings_id = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.sender_email_setting_id.emailSettingId,
              team_id = createAndStartCampaignData.createCampaign.team_id,
              account_id = createAndStartCampaignData.createCampaign.owner_id,
              receiver_email_settings_id = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.receiver_email_setting_id.emailSettingId,
              campaign_email_settings_id = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.id,
              prospect_id = Some(prospects.head.id),
              base_body = "body"

            )
            ),
            campaign_email_setting_id = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.id,
            emailSendingFlow = None,
            Logger = Logger
          )
        }
        emailSent <- Future.fromTry {
          emailSenderService.onEmailSent(
            emailSentId = addingEmailScheduled.head.email_scheduled_id,
            data = DefaultParametersFixtureForInitializingDataForReScheduling.defaultEmailToBeSent(
              emailSetting = initialData.emailSetting.get,
              campaign = createAndStartCampaignData.campaign
            ).copy(outlook_conversation_id = outlookConvId, subject = "subject"),
            accountId = createAndStartCampaignData.createCampaign.owner_id,
            sendEmailFromCampaignDetails = Some(SendEmailFromCampaignDetails(
              campaign_id = createAndStartCampaignData.createCampaign.id,
              campaign_name = createAndStartCampaignData.createCampaign.name,
              // stepDetails can be none when email is being sent manually from Inbox.
              // Example: sendNewEmailManually in InboxV3Service
              stepDetails = None
            )),
            prospectIdInCampaign = Some(duplicate),
            currentBillingCycleStartedAt = initialData.account.created_at,
            orgId = initialData.account.org.id,
            repTrackingHostId = 1,
            teamId = initialData.head_team_id
          )
        }

        res: Seq[Long] <- Future {
          emailThreadDAO._updateLatestEmailId(
            data = Seq(updateData.copy(latest_email_id = emailSent.id.get))
          )
        }
      } yield {
        res
      }

      result.map(r => {
        assert(r.nonEmpty)
      }).recover(e => {
        println(s"EmailThreadDAO.query Error: ${e.printStackTrace()}")
        assert(false)
      })
    }

    it("should return success if there are emails by others"){

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
      val outlookConvId: Option[String] = Some("outlook_conversation_id+abcd")

      val result = for {

        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 4
        )
        prospects: Seq[ProspectObject] <- Future {
          initialData.prospectsResult
        }

        (duplicate: Long, master: Long) <- Future {
          (prospects.head.id, prospects.last.id)
        }

        addingEmailScheduled <- Future.fromTry {
          emailScheduledDAOService.saveEmailsToBeScheduledAndUpdateCampaignDataV2(
            emailsToBeScheduled = Vector(EmailScheduledNewFixture.generateEmailScheduledNew3.copy(
              campaign_id = Some(createAndStartCampaignData.createCampaign.id),
              step_id = createAndStartCampaignData.createCampaign.head_step_id,
              from_email = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.sender_email,
              scheduled_from_campaign = true,
              is_opening_step = true,
              sender_email_settings_id = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.sender_email_setting_id.emailSettingId,
              team_id = createAndStartCampaignData.createCampaign.team_id,
              account_id = createAndStartCampaignData.createCampaign.owner_id,
              receiver_email_settings_id = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.receiver_email_setting_id.emailSettingId,
              campaign_email_settings_id = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.id,
              prospect_id = Some(prospects.head.id),
              base_body = "body"

            )
            ),
            campaign_email_setting_id = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.id,
            emailSendingFlow = None,
            Logger = Logger
          )
        }
        emailSent <- Future.fromTry {
          emailSenderService.onEmailSent(
            emailSentId = addingEmailScheduled.head.email_scheduled_id,
            data = DefaultParametersFixtureForInitializingDataForReScheduling.defaultEmailToBeSent(
              emailSetting = initialData.emailSetting.get,
              campaign = createAndStartCampaignData.campaign
            ).copy(outlook_conversation_id = outlookConvId, subject = "subject"),
            accountId = createAndStartCampaignData.createCampaign.owner_id,
            sendEmailFromCampaignDetails = Some(SendEmailFromCampaignDetails(
              campaign_id = createAndStartCampaignData.createCampaign.id,
              campaign_name = createAndStartCampaignData.createCampaign.name,
              // stepDetails can be none when email is being sent manually from Inbox.
              // Example: sendNewEmailManually in InboxV3Service
              stepDetails = None
            )),
            prospectIdInCampaign = Some(duplicate),
            currentBillingCycleStartedAt = initialData.account.created_at,
            orgId = initialData.account.org.id,
            repTrackingHostId = 1,
            teamId = initialData.head_team_id
          )
        }

        res: Seq[Long] <- Future {
          emailThreadDAO._updateLatestEmailId(
            data = Seq(updateData.copy(
              by_account = false,
              latest_sent_by_admin_at = None,
              by_prospect = true,
              folderType = FolderType.IRRELEVANT,
              latest_email_id = 1
            ))
          )
        }
      } yield {
        res
      }

      result.map(r => {
        assert(r.nonEmpty)
      }).recover(e => {
        println(s"EmailThreadDAO.query Error: ${e.printStackTrace()}")
        assert(false)
      })
    }

  }

  describe("updateMasterProspectInEmailThreadsProspects") {
    it("should return list of threads updated") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
      val outlookConvId: Option[String] = Some("outlook_conversation_id+abcd")

      val updatedIds: Future[(List[Long], Long)] = for {

        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 4
        )
        prospects: Seq[ProspectObject] <- Future {
          initialData.prospectsResult
        }

        (duplicate: Long, master: Long) <- Future {
          (prospects.head.id, prospects.last.id)
        }

        addingEmailScheduled <- Future.fromTry {
          emailScheduledDAOService.saveEmailsToBeScheduledAndUpdateCampaignDataV2(
            emailsToBeScheduled = Vector(EmailScheduledNewFixture.generateEmailScheduledNew3.copy(
              campaign_id = Some(createAndStartCampaignData.createCampaign.id),
              step_id = createAndStartCampaignData.createCampaign.head_step_id,
              from_email = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.sender_email,
              scheduled_from_campaign = true,
              is_opening_step = true,
              sender_email_settings_id = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.sender_email_setting_id.emailSettingId,
              team_id = createAndStartCampaignData.createCampaign.team_id,
              account_id = createAndStartCampaignData.createCampaign.owner_id,
              receiver_email_settings_id = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.receiver_email_setting_id.emailSettingId,
              campaign_email_settings_id = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.id,
              prospect_id = Some(prospects.head.id),
              base_body = "body"

            )
            ),
            campaign_email_setting_id = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.id,
            emailSendingFlow = None,
            Logger = Logger
          )
        }
        emailSent <- Future.fromTry {
          emailSenderService.onEmailSent(
            emailSentId = addingEmailScheduled.head.email_scheduled_id,
            data = DefaultParametersFixtureForInitializingDataForReScheduling.defaultEmailToBeSent(
              emailSetting = initialData.emailSetting.get,
              campaign = createAndStartCampaignData.campaign
            ).copy(outlook_conversation_id = outlookConvId, subject = "subject"),
            accountId = createAndStartCampaignData.createCampaign.owner_id,
            sendEmailFromCampaignDetails = Some(SendEmailFromCampaignDetails(
              campaign_id = createAndStartCampaignData.createCampaign.id,
              campaign_name = createAndStartCampaignData.createCampaign.name,
              // stepDetails can be none when email is being sent manually from Inbox.
              // Example: sendNewEmailManually in InboxV3Service
              stepDetails = None
            )),
            prospectIdInCampaign = Some(duplicate),
            currentBillingCycleStartedAt = initialData.account.created_at,
            orgId = initialData.account.org.id,
            repTrackingHostId = 1,
            teamId = initialData.head_team_id
          )
        }

        updatedIds: List[Long] <- Future.fromTry {
          emailThreadDAO.updateMasterProspectInEmailThreadsProspects(
            duplicateProspects = List(ProspectIdAndPotentialDuplicateProspectId(
              prospectId = ProspectId(duplicate), potentialDuplicateProspectId = PotentialDuplicateProspectId(1), isMasterProspect = false
            )),
            teamId = TeamId(createAndStartCampaignData.createCampaign.team_id),
            masterProspectId = ProspectId(master)
          )
        }

      } yield {
        (updatedIds, emailSent.email_thread_id.get)
      }

      updatedIds.map(ids => {
        assert(ids._1.nonEmpty && ids._1.length == 1 && ids._1.head == ids._2)
      }).recover({ case e =>
        println(s"error::: ${LogHelpers.getStackTraceAsString(e)}")
        assert(false)
      })
    }
  }

  describe("getConversationObjectForThreadId") {
    it("should return conversation object") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
      val outlookConvId: Option[String] = Some("outlook_conversation_id+abcd")

      val convs = for {

        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 4
        )
        prospects: Seq[ProspectObject] <- Future {
          initialData.prospectsResult
        }

        (duplicate: Long, master: Long) <- Future {
          (prospects.head.id, prospects.last.id)
        }

        addingEmailScheduled <- Future.fromTry {
          emailScheduledDAOService.saveEmailsToBeScheduledAndUpdateCampaignDataV2(
            emailsToBeScheduled = Vector(EmailScheduledNewFixture.generateEmailScheduledNew3.copy(
              campaign_id = Some(createAndStartCampaignData.createCampaign.id),
              step_id = createAndStartCampaignData.createCampaign.head_step_id,
              from_email = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.sender_email,
              scheduled_from_campaign = true,
              is_opening_step = true,
              sender_email_settings_id = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.sender_email_setting_id.emailSettingId,
              team_id = createAndStartCampaignData.createCampaign.team_id,
              account_id = createAndStartCampaignData.createCampaign.owner_id,
              receiver_email_settings_id = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.receiver_email_setting_id.emailSettingId,
              campaign_email_settings_id = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.id,
              prospect_id = Some(prospects.head.id),
              base_body = "body"

            )
            ),
            campaign_email_setting_id = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.id,
            emailSendingFlow = None,
            Logger = Logger
          )
        }
        emailSent <- Future.fromTry {
          emailSenderService.onEmailSent(
            emailSentId = addingEmailScheduled.head.email_scheduled_id,
            data = DefaultParametersFixtureForInitializingDataForReScheduling.defaultEmailToBeSent(
              emailSetting = initialData.emailSetting.get,
              campaign = createAndStartCampaignData.campaign
            ).copy(outlook_conversation_id = outlookConvId, subject = "subject"),
            accountId = createAndStartCampaignData.createCampaign.owner_id,
            sendEmailFromCampaignDetails = Some(SendEmailFromCampaignDetails(
              campaign_id = createAndStartCampaignData.createCampaign.id,
              campaign_name = createAndStartCampaignData.createCampaign.name,
              // stepDetails can be none when email is being sent manually from Inbox.
              // Example: sendNewEmailManually in InboxV3Service
              stepDetails = None
            )),
            prospectIdInCampaign = Some(duplicate),
            currentBillingCycleStartedAt = initialData.account.created_at,
            orgId = initialData.account.org.id,
            repTrackingHostId = 1,
            teamId = initialData.head_team_id
          )
        }

        res <- Future.fromTry(emailThreadDAO.getConversationObjectForThreadId(
          thread_id = emailSent.email_thread_id.get,
          teamId = initialData.emailSetting.get.team_id
        ))

      } yield {
        res
      }

      convs.map(conv => {
        assert(conv.team_id == initialData.emailSetting.get.team_id)
      }).recover({ case e =>
        println(s"error::: ${LogHelpers.getStackTraceAsString(e)}")
        assert(false)
      })
    }
  }

}
