package db_test_spec.api.accounts.emails

import api.accounts.{Account, TeamId}
import api.accounts.models.{AccountId, OrgId}
import api.emails.{EmailAddressHost, EmailSetting, EmailSettingDailyLimitAndDelay, EmailSettingForScheduling}
import api.campaigns.models.{CampaignEmailSettingsId, CampaignType}
import api.campaigns.services.CampaignId
import api.campaigns.{CampaignEmailSettings, CampaignEmailSettingsUuid, CampaignSettings, CampaignWithStatsAndEmail}
import api.domain_health.{DomainChecks, DomainHealthCheckId}
import api.team_inbox.model.TeamInboxDetails
import app.db_test.{CustomNotCategorizedAndDoNotContactIds, SchedulerTestInput}
import app_services.blacklist_monitoring.models.{BlacklistCheckResult, BlacklistCheckStatus}
import db_test_spec.api.accounts.emails.dao.EmailScheduledDAO
import db_test_spec.api.campaigns.fixtures.CreateNewCampaignFixture.{createNewCampaign, findCategorizedAndDoNotContactCustomIds}
import db_test_spec.api.accounts.fixtures.NewAccountAndEmailSettingData
import db_test_spec.api.campaigns.fixtures.{CreateNewCampaignFixture, CreateStepForCampaignFixture, StartCampaignFixture}
import db_test_spec.api.accounts.fixtures.NewAccountAndEmailSettingData
import db_test_spec.api.campaigns.fixtures.CreateNewCampaignFixture.findCategorizedAndDoNotContactCustomIds
import db_test_spec.api.campaigns.test_utils.{CampaignUtils, CreateAndStartCampaignData}
import db_test_spec.api.campaigns.{CampaignCreationFixtureForIntegrationTest, InitializedCreateAndStartCampaignData}
import db_test_spec.api.emails.fixtures.DefaultEmailSettingParametersFixtures.defaultEmailSettingForm
import db_test_spec.api.{AppSpecFixture, DbTestingBeforeAllAndAfterAll, InitialData, InputForInitializingCampaignCreateData, SRSetupAndDeleteFixtures}
import db_test_spec.api.emails.fixtures.EmailSettingFixtureForIntegrationTest
import db_test_spec.api.prospects.fixtures.ProspectFixtureForIntegrationTest
import db_test_spec.team_inbox.fixtures.TeamInboxFixtureForIntegrationTest
import io.smartreach.esp.api.emails.EmailSettingId
import io.smartreach.esp.api.emails.EmailSettingId
import org.joda.time.DateTime
import org.scalatest.ParallelTestExecution
import play.api.libs.json.{JsValue, Json}
import scalikejdbc.scalikejdbcSQLInterpolationImplicitDef
import sr_scheduler.CampaignStatus
import sr_scheduler.models.CampaignEmailPriority
import utils.SRLogger
import utils.cronjobs.email_setting_deletion.model.EmailSettingStatus
import utils.helpers.LogHelpers

import scala.concurrent.Future
import scala.util.{Failure, Random, Success}
import scala.concurrent.{Await, Future}
import scala.concurrent.duration.{Duration, SECONDS}
import scala.util.{Failure, Random, Success, Try}

class EmailSettingsDAOSpec extends DbTestingBeforeAllAndAfterAll {
  describe("addError") {
    it("should success") {

      NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData() match {
        case Failure(exception) =>
          println(s"err ---------- ${LogHelpers.getStackTraceAsString(exception)}")
          assert(false)
        case Success(initialData) =>

          val emailSetting: EmailSetting = initialData.emailSetting.get
          val result = emailSettingDAO.addError(
            emailSettingId = emailSetting.id.get.emailSettingId,
            error = "this is a test error",
            errorReportedAt = DateTime.now(),
            pausedTill = DateTime.now().plusDays(1)
          )

          assert(result.isSuccess)
          assert(result.get.nonEmpty)
          assert(result.get.head.id == emailSetting.id)
      }
    }
  }


  describe("getSenderEmailSettingIdsForReceiverId") {
    it("should success") {

      NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData() match {
        case Failure(exception) =>
          println(s"err ---------- ${LogHelpers.getStackTraceAsString(exception)}")
          assert(false)
        case Success(initialData) =>
          val result = emailSettingDAO.getSenderEmailSettingIdsForReceiverId(
            receiverEmailSettingId = initialData.emailSetting.get.id.get.emailSettingId,
            teamId = initialData.head_team_id
          )

          assert(result.isSuccess)
        // below was failing as result wasn't empty since test-case is the just test the query working
        // I'm commenting it out.
        //      assert(result.get.isEmpty)
      }
    }

    it("should return email setting ids including active in campaigns and in shared inbox") {

      //1 email settings in active campaign
      //1 email settings in team inbox

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
      val account: Account = initialData.account
      val emailSetting: EmailSetting = initialData.emailSetting.get
      val teamId: TeamId = TeamId(account.teams.head.team_id)
      val emailSettingId: EmailSettingId = emailSetting.id.get


      val initialData2: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val res: Future[(Seq[Long], Seq[Long])] = for {
        _: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 4
        )

        _: Option[TeamInboxDetails] <- Future.fromTry {
          TeamInboxFixtureForIntegrationTest.createTeamInbox(
            orgId = OrgId(initialData2.account.org.id),
            accountId = AccountId(initialData2.account.internal_id),
            emailSettingId = initialData2.emailSetting.get.id.get,
            teamId = TeamId(initialData2.account.teams.head.team_id),
            userRoleIds = initialData2.account.teams.map(_.role.get.id).toList
          )
        }

        res1 <- Future.fromTry(emailSettingDAO.getSenderEmailSettingIdsForReceiverId(
          receiverEmailSettingId = emailSettingId.emailSettingId, //used in campaign
          teamId = teamId.id
        ))

        res2 <- Future.fromTry(emailSettingDAO.getSenderEmailSettingIdsForReceiverId(
          receiverEmailSettingId = initialData2.emailSetting.get.id.get.emailSettingId, //team_inbox eset and not used in campaign
          teamId = initialData2.emailSetting.get.team_id.id
        ))

      } yield {
        (res1, res2)
      }

      res.map(r => {
        assert(r._1.length == 1 && r._2.length == 1)
      }).recover(e => {
        println(s"err ---------- ${LogHelpers.getStackTraceAsString(e)}")
        assert(false)
      })

    }
  }


  describe("isGsuiteDomainInstalled") {
    it("should success") {

      NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData() match {
        case Failure(exception) =>
          println(s"err ---------- ${LogHelpers.getStackTraceAsString(exception)}")
          assert(false)
        case Success(initialData) =>

          val result = emailSettingDAO.isGsuiteDomainInstalled(
            email = initialData.emailSetting.get.email,
            teamId = initialData.head_team_id
          )

          assert(!result)
      }
    }
  }

  describe("getAllSendingDomainsForBlacklistCheck") {
    it("should success") {
      val result = emailSettingDAO.getAllSendingDomainsForBlacklistCheck()

      assert(result.isSuccess)
      //      assert(result.get.nonEmpty)
    }
  }

  describe("getCampaignsSenderEmails") {

    it("should success and give the sender emails for matching campaign id") {


      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val emailList = List("<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>")


      val emailSettingsList: List[EmailSetting] = emailList.map { email =>

        EmailSettingFixtureForIntegrationTest.createEmailSetting(
          orgId = OrgId(initialData.account.org.id),
          accountId = AccountId(initialData.account.internal_id),
          teamId = TeamId(initialData.account.teams.head.team_id),
          taId = initialData.account.teams.head.access_members.head.ta_id,
          emailSettingForm = Option(defaultEmailSettingForm.copy(
            email = email
          ))
        ).get

      }

      val campaignEmailSettingList: List[CampaignEmailSettings] = emailSettingsList.map { es =>
        CampaignEmailSettings(
          campaign_id = CampaignId(1991), // have no affect on adding campaign Id can add random id
          sender_email_setting_id = es.id.get,
          receiver_email_setting_id = es.id.get,
          team_id = TeamId(initialData.account.teams.head.team_id),
          uuid = CampaignEmailSettingsUuid("temp_setting_id"),
          id = CampaignEmailSettingsId(es.id.get.emailSettingId),
          sender_email = es.email, // does not have any affect can be random
          receiver_email = es.email, // does not have any affect can be random
          max_emails_per_day_from_email_account = 100,
          signature = None,
          error = None,
          from_name = None
        )


      }


      val campaignSettings: Option[CampaignSettings] = Some(
        CampaignSettings(
          campaign_email_settings = campaignEmailSettingList,
          campaign_linkedin_settings = List(),
          campaign_call_settings = List(),
          campaign_whatsapp_settings = List(),
          campaign_sms_settings = List(),
          timezone = "Asia/Kolkata",
          daily_from_time = 0, // time since beginning of day in seconds
          daily_till_time = 86399, // time since beginning of day in seconds
          sending_holiday_calendar_id = None,

          // Sunday is the first day
          days_preference = List(true, true, true, true, true, true, true),

          mark_completed_after_days = 1,
          max_emails_per_day = 500,
          open_tracking_enabled = true,
          click_tracking_enabled = true,
          enable_email_validation = false,
          ab_testing_enabled = true,

          ai_sequence_status = None,

          // warm up
          warmup_started_at = None,
          warmup_length_in_days = None,
          warmup_starting_email_count = None,
          show_soft_start_setting = false,

          // schedule start
          schedule_start_at = Some(DateTime.now()),
          schedule_start_at_tz = Some("Asia/Kolkata"),

          send_plain_text_email = Some(false),
          campaign_type = CampaignType.MultiChannel,


          email_priority = CampaignEmailPriority.EQUAL,
          append_followups = true,
          opt_out_msg = "Pivot",
          opt_out_is_text = true,
          add_prospect_to_dnc_on_opt_out = true,
          triggers = Seq(),
          sending_mode = None,
          selected_calendar_data = None
        )

      )


      val campaignSenderEmailsList: Future[List[String]] = for {

        createCampaign: CampaignWithStatsAndEmail <- CreateNewCampaignFixture.createNewCampaign(
          orgId = OrgId(initialData.account.org.id),
          accountId = AccountId(initialData.account.internal_id),
          teamId = TeamId(initialData.account.teams.head.team_id),
          taId = initialData.account.teams.head.access_members.head.ta_id,
          campaignEmailSettingsId = CampaignEmailSettingsId(1L),
          senderEmailSettingId = EmailSettingId(1L),
          receiverEmailSettingId = EmailSettingId(1L),
          campaignSettings = campaignSettings,
          ownerFirstName = initialData.account.first_name.get


        )

        campaignId = CampaignId(createCampaign.id)

        result <- Future.fromTry(emailSettingDAO.getSenderEmails(
          teamId = TeamId(initialData.account.teams.head.team_id),
          campaignId = Some(campaignId)))


      } yield {
//        println(result)
        result
      }

      campaignSenderEmailsList.map { senderEmailList =>

        assert(senderEmailList.nonEmpty)


      }.recover { e =>
        println(s"err ---------- ${LogHelpers.getStackTraceAsString(e)}")
        assert(false)

      }


    }

    it("should return emails with matching team id") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val emailList1 = List("<EMAIL>", "<EMAIL>")
      val emailList2 = List("<EMAIL>", "<EMAIL>", "<EMAIL>")


      // Define a function to create CampaignEmailSettings for a given email setting and campaignId
      def createCampaignEmailSettings(emailSetting: EmailSetting, campaignId: Int): CampaignEmailSettings = {
        CampaignEmailSettings(
          campaign_id = CampaignId(campaignId),
          sender_email_setting_id = emailSetting.id.get,
          receiver_email_setting_id = emailSetting.id.get,
          team_id = TeamId(initialData.account.teams.head.team_id),
          uuid = CampaignEmailSettingsUuid("temp_setting_id"),
          id = CampaignEmailSettingsId(emailSetting.id.get.emailSettingId),
          sender_email = emailSetting.email,
          receiver_email = emailSetting.email,
          max_emails_per_day_from_email_account = 100,
          signature = None,
          error = None,
          from_name = None
        )
      }


      val emailSettingsList1: List[EmailSetting] = emailList1.map { email =>

        EmailSettingFixtureForIntegrationTest.createEmailSetting(
          orgId = OrgId(initialData.account.org.id),
          accountId = AccountId(initialData.account.internal_id),
          teamId = TeamId(initialData.account.teams.head.team_id),
          taId = initialData.account.teams.head.access_members.head.ta_id,
          emailSettingForm = Option(defaultEmailSettingForm.copy(
            email = email
          ))
        ).get

      }

      val emailSettingsList2: List[EmailSetting] = emailList2.map { email =>

        EmailSettingFixtureForIntegrationTest.createEmailSetting(
          orgId = OrgId(initialData.account.org.id),
          accountId = AccountId(initialData.account.internal_id),
          teamId = TeamId(initialData.account.teams.head.team_id),
          taId = initialData.account.teams.head.access_members.head.ta_id,
          emailSettingForm = Option(defaultEmailSettingForm.copy(
            email = email
          ))
        ).get

      }

      val campaignEmailSettingList1 = emailSettingsList1.map(es => createCampaignEmailSettings(es, 23))
      val campaignEmailSettingList2 = emailSettingsList2.map(es => createCampaignEmailSettings(es, 456))

      val campaignSettings1: Option[CampaignSettings] = Some(
        CampaignSettings(
          campaign_email_settings = campaignEmailSettingList1,
          campaign_linkedin_settings = List(),
          campaign_call_settings = List(),
          campaign_whatsapp_settings = List(),
          campaign_sms_settings = List(),
          timezone = "Asia/Kolkata",
          daily_from_time = 0, // time since beginning of day in seconds
          daily_till_time = 86399, // time since beginning of day in seconds
          sending_holiday_calendar_id = None,

          ai_sequence_status = None,

          // Sunday is the first day
          days_preference = List(true, true, true, true, true, true, true),

          mark_completed_after_days = 1,
          max_emails_per_day = 500,
          open_tracking_enabled = true,
          click_tracking_enabled = true,
          enable_email_validation = false,
          ab_testing_enabled = true,

          // warm up
          warmup_started_at = None,
          warmup_length_in_days = None,
          warmup_starting_email_count = None,
          show_soft_start_setting = false,

          // schedule start
          schedule_start_at = Some(DateTime.now()),
          schedule_start_at_tz = Some("Asia/Kolkata"),

          send_plain_text_email = Some(false),
          campaign_type = CampaignType.MultiChannel,


          email_priority = CampaignEmailPriority.EQUAL,
          append_followups = true,
          opt_out_msg = "Pivot",
          opt_out_is_text = true,
          add_prospect_to_dnc_on_opt_out = true,
          triggers = Seq(),
          sending_mode = None,
          selected_calendar_data = None
        )

      )


      val campaignSettings2: Option[CampaignSettings] = Some(
        CampaignSettings(
          campaign_email_settings = campaignEmailSettingList2,
          campaign_linkedin_settings = List(),
          campaign_call_settings = List(),
          campaign_whatsapp_settings = List(),
          campaign_sms_settings = List(),
          timezone = "Asia/Kolkata",
          daily_from_time = 0, // time since beginning of day in seconds
          daily_till_time = 86399, // time since beginning of day in seconds
          sending_holiday_calendar_id = None,

          ai_sequence_status = None,

          // Sunday is the first day
          days_preference = List(true, true, true, true, true, true, true),

          mark_completed_after_days = 1,
          max_emails_per_day = 500,
          open_tracking_enabled = true,
          click_tracking_enabled = true,
          enable_email_validation = false,
          ab_testing_enabled = true,

          // warm up
          warmup_started_at = None,
          warmup_length_in_days = None,
          warmup_starting_email_count = None,
          show_soft_start_setting = false,

          // schedule start
          schedule_start_at = Some(DateTime.now()),
          schedule_start_at_tz = Some("Asia/Kolkata"),

          send_plain_text_email = Some(false),
          campaign_type = CampaignType.MultiChannel,


          email_priority = CampaignEmailPriority.EQUAL,
          append_followups = true,
          opt_out_msg = "Pivot",
          opt_out_is_text = true,
          add_prospect_to_dnc_on_opt_out = true,
          triggers = Seq(),
          sending_mode = None,
          selected_calendar_data = None
        )

      )


      val campaignSenderEmailsList: Future[List[String]] = for {

        createCampaign1: CampaignWithStatsAndEmail <- CreateNewCampaignFixture.createNewCampaign(
          orgId = OrgId(initialData.account.org.id),
          accountId = AccountId(initialData.account.internal_id),
          teamId = TeamId(initialData.account.teams.head.team_id),
          taId = initialData.account.teams.head.access_members.head.ta_id,
          campaignEmailSettingsId = CampaignEmailSettingsId(1L),
          senderEmailSettingId = EmailSettingId(1L),
          receiverEmailSettingId = EmailSettingId(1L),
          campaignSettings = campaignSettings1,
          ownerFirstName = initialData.account.first_name.get,

        )

        createCampaign2: CampaignWithStatsAndEmail <- CreateNewCampaignFixture.createNewCampaign(
          orgId = OrgId(initialData.account.org.id),
          accountId = AccountId(initialData.account.internal_id),
          teamId = TeamId(initialData.account.teams.head.team_id),
          taId = initialData.account.teams.head.access_members.head.ta_id,
          campaignEmailSettingsId = CampaignEmailSettingsId(1L),
          senderEmailSettingId = EmailSettingId(1L),
          receiverEmailSettingId = EmailSettingId(1L),
          campaignSettings = campaignSettings2,
          ownerFirstName = initialData.account.first_name.get

        )

        result <- Future.fromTry(emailSettingDAO.getSenderEmails(
          teamId = TeamId(initialData.account.teams.head.team_id),
          campaignId = None
        ))


      } yield {
//        println(result)
        result
      }

      campaignSenderEmailsList.map { senderEmailList =>

        assert(senderEmailList.nonEmpty)


      }.recover { e =>
        println(s"err ---------- ${LogHelpers.getStackTraceAsString(e)}")
        assert(false)
      }
    }
  }


  describe("getDomainsForBlacklistCheck") {

    it("should fetch the domains ") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData(
        sender_email = "animesh@fetch_domain_test.com"
      ).get

      val domainsForBlacklist = for {
        _: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 4
        )
        add_to_blacklist <- Future.fromTry(domainHealthCheckDAO.createAndUpdatePushedToQueue(
          domainList = List(EmailAddressHost("fetch_domain_test.com"))
        ))
        update_domain_health_result <- Future.fromTry {
          EmailScheduledDAO.updateForDomainHealthCheck(
            domain = "fetch_domain_test.com",
            last_blacklist_check_at = DateTime.now().minusDays(10),
            pushed_to_queue = false
          )
        }
        result <- Future.fromTry(emailSettingDAO.getDomainsForBlacklistCheck)
      } yield {
        result
      }

      domainsForBlacklist.map { domainsList =>
//        println(s"pass domainsForBlacklist --- $domainsForBlacklist")
        assert(domainsList.contains(EmailAddressHost("fetch_domain_test.com")))

      }.recover { e =>
        println(s"err ---------- ${LogHelpers.getStackTraceAsString(e)}")
        assert(false)
      }
    }


  }


  describe("fetchEmailAccountForScheduling") {
    it("should return an empty list as domain is blacklisted") {
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val emailSettings: EmailSetting = EmailSettingFixtureForIntegrationTest.createEmailSetting(
        orgId = OrgId(initialData.account.org.id),
        accountId = AccountId(initialData.account.internal_id),
        teamId = TeamId(initialData.account.teams.head.team_id),
        taId = initialData.account.teams.head.access_members.head.ta_id,
        emailSettingForm = Option(defaultEmailSettingForm.copy(
          email = "<EMAIL>"
        ))
      ).get

      val domainBlacklistResult: BlacklistCheckResult = BlacklistCheckResult(
        status = BlacklistCheckStatus.FAILED,
        failureDescription = Some("ivmURI"),
        fullResult = Json.obj(
          "blacklist status" -> "failed"
        )
      )

      val schedulingResult: Future[Seq[EmailSettingForScheduling]] = for {
        _: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 4,
          emailSettings = Some(emailSettings)
        )

        _: List[DomainChecks] <- Future.fromTry(domainHealthCheckDAO.createAndUpdatePushedToQueue(List(EmailAddressHost("schedule.com"))))

        _: DomainHealthCheckId <- Future.fromTry(domainHealthCheckDAO.updateBlacklistResult(domainBlacklistResult, "schedule.com", true))

        result: Seq[EmailSettingForScheduling] <- Future.fromTry(emailSettingDAO.fetchEmailAccountForScheduling()
        )
      } yield {
        result
      }

      schedulingResult.map { res =>
//        println(res)
        val isScheduleDomainFetched = res.exists(_.sender_email == "<EMAIL>")
        assert(!isScheduleDomainFetched)

      }.recover { e =>
        println(s"err ---------- ${LogHelpers.getStackTraceAsString(e)}")
        assert(false)
      }
    }


    it("should return an list as domain is not blacklisted") {
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val emailSettings: EmailSetting = EmailSettingFixtureForIntegrationTest.createEmailSetting(
        orgId = OrgId(initialData.account.org.id),
        accountId = AccountId(initialData.account.internal_id),
        teamId = TeamId(initialData.account.teams.head.team_id),
        taId = initialData.account.teams.head.access_members.head.ta_id,
        emailSettingForm = Option(defaultEmailSettingForm.copy(
          email = "<EMAIL>"
        ))
      ).get

      val domainBlacklistResult: BlacklistCheckResult = BlacklistCheckResult(
        status = BlacklistCheckStatus.PASSED,
        failureDescription = None,
        fullResult = Json.obj(
          "blacklist status" -> "passed"
        )
      )

      val schedulingResult: Future[Seq[EmailSettingForScheduling]] = for {
        _: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 4,
          emailSettings = Some(emailSettings)
        )

        _: List[DomainChecks] <- Future.fromTry(domainHealthCheckDAO.createAndUpdatePushedToQueue(List(EmailAddressHost("schedule1.com"))))

        _: DomainHealthCheckId <- Future.fromTry(domainHealthCheckDAO.updateBlacklistResult(domainBlacklistResult, "schedule1.com", false))

        result: Seq[EmailSettingForScheduling] <- Future.fromTry(emailSettingDAO.fetchEmailAccountForScheduling()
        )
      } yield {
        result
      }

      schedulingResult.map { res =>
//        println(s"res ----- $res")
        val isScheduleDomainFetched = res.exists(_.sender_email == "<EMAIL>")
//        println(s"isScheduleDomainFetched ----- $isScheduleDomainFetched")


        assert(isScheduleDomainFetched)
      }.recover { e =>
//        println(s"err ---------- ${LogHelpers.getStackTraceAsString(e)}")
        assert(false)
      }
    }
  }

  describe("__findEmailSettingsSQL") {
    it("should return error for blacklist domain") {
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      EmailSettingFixtureForIntegrationTest.createEmailSetting(
        orgId = OrgId(initialData.account.org.id),
        accountId = AccountId(initialData.account.internal_id),
        teamId = TeamId(initialData.account.teams.head.team_id),
        taId = initialData.account.teams.head.access_members.head.ta_id,
        emailSettingForm = Option(defaultEmailSettingForm.copy(
          email = "<EMAIL>"
        ))
      ).get

      val domainBlacklistResult: BlacklistCheckResult = BlacklistCheckResult(
        status = BlacklistCheckStatus.FAILED,
        failureDescription = Some("ivmURI"),
        fullResult = Json.obj(
          "blacklist status" -> "failed"
        )
      )

      val whereClause = sqls" WHERE es.status = ${EmailSettingStatus.Active.toString}"

      val emailSettings: Future[Seq[EmailSetting]] = for {

        _: List[DomainChecks] <- Future.fromTry(domainHealthCheckDAO.createAndUpdatePushedToQueue(List(EmailAddressHost("mno.com"))))

        _: DomainHealthCheckId <- Future.fromTry(domainHealthCheckDAO.updateBlacklistResult(domainBlacklistResult, "mno.com", true))

        result: Seq[EmailSetting] <- Future(emailSettingDAO.__findEmailSettingsSQL(whereClause = whereClause))

      } yield {
        result
      }

      emailSettings.map { es =>
//        println(es)
        val error = es.find(_.email == "<EMAIL>").get.error.get
        assert(error == "Your sending email domain is found in a global spam blacklist. Please check the status by going to Settings -> Team Settings -> Domain Health.")

      }.recover { e =>
        println(s"err ---------- ${LogHelpers.getStackTraceAsString(e)}")
        assert(false)
      }


    }
  }

  describe("updateEmailSettingsQuotaAndDelays") {
    it("should successfully update quota and delays for valid email setting") {
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
      val emailSetting: EmailSetting = initialData.emailSetting.get

      val result = emailSettingDAO.updateEmailSettingsQuotaAndDelays(
//        emailSettingIds = Seq(emailSetting.id.get),
          data =  EmailSettingDailyLimitAndDelay(
              es_ids = List(emailSetting.id.get),
              quota_per_day = Some(150),
              min_delay_seconds = Some(105),
              max_delay_seconds = Some(305)
          ),
          teamId = TeamId(initialData.account.teams.head.team_id)
      )

        println(s"result ts 1 : $result ")

      assert(result.isSuccess)
      assert(result.get.nonEmpty)
      assert(result.get.head.quota_per_day == 150)
      assert(result.get.head.min_delay_seconds == 105)
      assert(result.get.head.max_delay_seconds == 305)
    }

    it("should successfully update only quota when delay is not provided") {
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
      val emailSetting: EmailSetting = initialData.emailSetting.get

        val result = emailSettingDAO.updateEmailSettingsQuotaAndDelays(
            //        emailSettingIds = Seq(emailSetting.id.get),
            data =  EmailSettingDailyLimitAndDelay(
                es_ids = List(emailSetting.id.get),
                quota_per_day = Some(150),
                min_delay_seconds = None,
                max_delay_seconds = None
            ),
            teamId = TeamId(initialData.account.teams.head.team_id)
        )
        println(s"result ts 2 : $result ")
      assert(result.isSuccess)
      assert(result.get.nonEmpty)
      assert(result.get.head.quota_per_day == 150)
      assert(result.get.head.min_delay_seconds == emailSetting.min_delay_seconds)
      assert(result.get.head.max_delay_seconds == emailSetting.max_delay_seconds)
    }

    it("should successfully update only delay when quota is not provided") {
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
      val emailSetting: EmailSetting = initialData.emailSetting.get

        val result = emailSettingDAO.updateEmailSettingsQuotaAndDelays(
            //        emailSettingIds = Seq(emailSetting.id.get),
            data = EmailSettingDailyLimitAndDelay(
                es_ids = List(emailSetting.id.get),
                quota_per_day = None,
                min_delay_seconds = Some(105),
                max_delay_seconds = Some(302)
            ),
            teamId = TeamId(initialData.account.teams.head.team_id)
        )

        println(s"result ts 3 : $result ")

      assert(result.isSuccess)
      assert(result.get.nonEmpty)
      assert(result.get.head.quota_per_day == emailSetting.quota_per_day) // quota should remain unchanged
      assert(result.get.head.min_delay_seconds == 105)
        assert(result.get.head.max_delay_seconds == 302)
    }

      it("should return empty list if es_ids is empty"){
          val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
          val emailSetting: EmailSetting = initialData.emailSetting.get

          val result = emailSettingDAO.updateEmailSettingsQuotaAndDelays(
              //        emailSettingIds = Seq(emailSetting.id.get),
              data = EmailSettingDailyLimitAndDelay(
                  es_ids = List(),
                  quota_per_day = None,
                  min_delay_seconds = Some(105),
                  max_delay_seconds = Some(302)
              ),
              teamId = TeamId(initialData.account.teams.head.team_id)
          )

          assert(result.isSuccess)
          assert(result.get.isEmpty)
      }

      it("should return empty list if nothing is to update") {
          val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
          val emailSetting: EmailSetting = initialData.emailSetting.get

          val result = emailSettingDAO.updateEmailSettingsQuotaAndDelays(
              //        emailSettingIds = Seq(emailSetting.id.get),
              data = EmailSettingDailyLimitAndDelay(
                  es_ids = List(emailSetting.id.get),
                  quota_per_day = None,
                  min_delay_seconds = None,
                  max_delay_seconds = None
              ),
              teamId = TeamId(initialData.account.teams.head.team_id)
          )

          assert(result.isSuccess)
          assert(result.get.isEmpty)
      }


  }
}
