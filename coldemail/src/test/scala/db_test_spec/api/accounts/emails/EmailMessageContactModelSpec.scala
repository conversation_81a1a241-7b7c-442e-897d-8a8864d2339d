package db_test_spec.api.accounts.emails

import api.accounts.EmailScheduledIdOrTaskId.EmailScheduledId
import api.accounts.TeamId
import api.campaigns.models.SendEmailFromCampaignDetails
import api.emails.EmailScheduled
import api.emails.models.EmailMessageContactsId
import api.prospects.dao.ProspectIdAndPotentialDuplicateProspectId
import api.prospects.models.{PotentialDuplicateProspectId, ProspectId}
import db_test_spec.api.accounts.emails.dao.EmailMessageContactDAO
import db_test_spec.api.{DbTestingBeforeAllAndAfterAll, InitialData}
import db_test_spec.api.accounts.fixtures.{EmailScheduledNewFixture, NewAccountAndEmailSettingData}
import db_test_spec.api.campaigns.test_utils.{CampaignUtils, CreateAndStartCampaignData}
import db_test_spec.api.scheduler.fixtures.DefaultParametersFixtureForInitializingDataForReScheduling
import eventframework.ProspectObject
import org.scalatest.ParallelTestExecution
import scalikejdbc.DBSession
import utils.SRLogger
import utils.dbutils.DbAndSession
import utils.helpers.LogHelpers

import scala.concurrent.Future
import scala.util.Try

class EmailMessageContactModelSpec extends DbTestingBeforeAllAndAfterAll with ParallelTestExecution {

  describe("updateProspectIdForMergeDuplicates") {
    it("should return updated email message contact ids") {
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
      val outlookConvId: Option[String] = Some("outlook_conversation_id+abcd")
      val emcTestDAO = new EmailMessageContactDAO()

      val updatedIds: Future[(List[Long], Long)] = for {

        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 4
        )
        prospects: Seq[ProspectObject] <- Future {
          initialData.prospectsResult
        }

        (duplicate: Long, master: Long) <- Future {
          (prospects.head.id, prospects.last.id)
        }

        addingEmailScheduled <- Future.fromTry {
          emailScheduledDAOService.saveEmailsToBeScheduledAndUpdateCampaignDataV2(
            emailsToBeScheduled = Vector(EmailScheduledNewFixture.generateEmailScheduledNew3.copy(
              campaign_id = Some(createAndStartCampaignData.createCampaign.id),
              step_id = createAndStartCampaignData.createCampaign.head_step_id,
              from_email = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.sender_email,
              scheduled_from_campaign = true,
              is_opening_step = true,
              sender_email_settings_id = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.sender_email_setting_id.emailSettingId,
              team_id = createAndStartCampaignData.createCampaign.team_id,
              account_id = createAndStartCampaignData.createCampaign.owner_id,
              receiver_email_settings_id = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.receiver_email_setting_id.emailSettingId,
              campaign_email_settings_id = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.id,
              prospect_id = Some(duplicate),
              base_body = "body"

            )
            ),
            campaign_email_setting_id = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.id,
            emailSendingFlow = None,
            Logger = Logger
          )
        }
        emailSent: EmailScheduled <- Future.fromTry {
          emailSenderService.onEmailSent(
            emailSentId = addingEmailScheduled.head.email_scheduled_id,
            data = DefaultParametersFixtureForInitializingDataForReScheduling.defaultEmailToBeSent(
              emailSetting = initialData.emailSetting.get,
              campaign = createAndStartCampaignData.campaign
            ).copy(outlook_conversation_id = outlookConvId, subject = "subject"),
            accountId = createAndStartCampaignData.createCampaign.owner_id,
            sendEmailFromCampaignDetails = Some(SendEmailFromCampaignDetails(
              campaign_id = createAndStartCampaignData.createCampaign.id,
              campaign_name = createAndStartCampaignData.createCampaign.name,
              // stepDetails can be none when email is being sent manually from Inbox.
              // Example: sendNewEmailManually in InboxV3Service
              stepDetails = None
            )),
            prospectIdInCampaign = Some(duplicate),
            currentBillingCycleStartedAt = initialData.account.created_at,
            orgId = initialData.account.org.id,
            repTrackingHostId = 1,
            teamId = initialData.head_team_id
          )
        }

        //update email_message_contact prospect_id
        //NOTE: in markEmailAsSent path for campaign we are not updating the prospect_id so doing it manually here

        updatedProspectId: Int <- Future.fromTry {
          emcTestDAO.updateProspectIdForMessageContact(
            teamId = initialData.emailSetting.get.team_id,
            emailScheduledId = EmailScheduledId(emailSent.id.get),
            prospectId = ProspectId(duplicate)
          )
        }

        updatedIds: List[EmailMessageContactsId] <- Future.fromTry {
          val dbAndSession: DbAndSession = dbUtils.startLocalTx()
          val db = dbAndSession.db
          implicit val session: DBSession = dbAndSession.session
          val res: Try[List[EmailMessageContactsId]] = emailMessageContactModel.updateProspectIdForMergeDuplicates(
            duplicateProspects = List(ProspectIdAndPotentialDuplicateProspectId(
              prospectId = ProspectId(duplicate), potentialDuplicateProspectId = PotentialDuplicateProspectId(1), isMasterProspect = false
            )),
            teamId = TeamId(createAndStartCampaignData.createCampaign.team_id),
            masterProspectId = ProspectId(master)
          )
          dbUtils.commitAndCloseSession(db = db)
          res
        }

        updatedEmailScheduledIds: List[Long] <- Future.fromTry {
          emcTestDAO.getEmailScheduledIdOfEmailMessageContactId(
            teamId = TeamId(createAndStartCampaignData.createCampaign.team_id),
            emailMessageContactIds = updatedIds.map(_.id)
          )
        }

      } yield {
        (updatedEmailScheduledIds, emailSent.id.get)
      }

      updatedIds.map(ids => {
        assert(ids._1.nonEmpty && ids._1.contains(ids._2))
      }).recover({ case e =>
        println(s"error::: ${LogHelpers.getStackTraceAsString(e)}")
        assert(false)
      })
    }
  }

}
