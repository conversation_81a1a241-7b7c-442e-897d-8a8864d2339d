package db_test_spec.api.accounts.emails.dao

import api.accounts.EmailScheduledIdOrTaskId.EmailScheduledId
import api.accounts.TeamId
import api.prospects.models.ProspectId
import scalikejdbc.{DB, scalikejdbcSQLInterpolationImplicitDef}

import scala.util.Try

class EmailMessageContactDAO {
  def updateProspectIdForMessageContact(
                                         teamId: TeamId,
                                         emailScheduledId: EmailScheduledId,
                                         prospectId: ProspectId
                                       ): Try[Int] = Try {
    DB autoCommit{implicit session =>
      sql"""
            update email_message_contacts
            set
              prospect_id = ${prospectId.id}
            where
               team_id = ${teamId.id}
               AND email_message_id = ${emailScheduledId.id};
         """
        .update
        .apply()
    }
  }

  def getEmailScheduledIdOfEmailMessageContactId(
                                                  teamId: TeamId,
                                                  emailMessageContactIds: List[Long]
                                                ): Try[List[Long]] = Try {
    DB readOnly { implicit session =>
      sql"""
            select email_message_id from email_message_contacts
            where
               team_id = ${teamId.id}
               AND id in (${emailMessageContactIds});
         """
        .map(_.long("email_message_id"))
        .list
        .apply()
    }
  }
}
