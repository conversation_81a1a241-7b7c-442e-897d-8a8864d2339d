package db_test_spec.api.accounts.dao

import api.accounts.ResetBillingMonthlyCycleForOrg
import api.accounts.models.OrgId
import api.billing.PaymentGateway
import db_test_spec.api.DbTestingBeforeAllAndAfterAll
import db_test_spec.api.accounts.fixtures.NewAccountAndEmailSettingData
import db_test_spec.utils.fixtures.PlanLimitFixture
import io.sr.billing_common.models.PlanType
import org.joda.time.DateTime
import org.scalatest.ParallelTestExecution
import utils.helpers.LogHelpers

import scala.util.{Failure, Success}

class OrganizationBillingDAOSpec extends DbTestingBeforeAllAndAfterAll with ParallelTestExecution {


  private def updateOrgSubToPaid(
    orgId: OrgId,
    currentCycleStartedAt: DateTime,
  ) = {

    OrganizationTestDAO.updateSubscriptionAndOrgMetadata(
      planLimits = PlanLimitFixture.default_plan_limits,

      maxManualLinkedinAccounts = 10000,

      currentCycleStartedAt = currentCycleStartedAt,
      nextBillingDate = Some(currentCycleStartedAt.plusMonths(1)),

      sr_plan_name = "email-one-monthly-usd-v5-email-29",

      planType = PlanType.PAID,
      paymentGateway = PaymentGateway.STRIPE,
      cancelledAt = None,

      // This needs to be 0 because lead finder integration test assume no lead finder credits are present
      defaultLeadFinderCredits = 0,

      orgId = orgId
    )

  }


  describe("Test resetBillingMonthlyCycle") {

    it("should increment current billing cycle by one month when resetting monthly cycle for a paid organization") {

      val initialData = NewAccountAndEmailSettingData.createBasicAccountData().get

      val account = initialData.account

      val orgId = OrgId(account.org.id)

      val currentCycleStartedAt = DateTime.now().minusMonths(1)

      updateOrgSubToPaid(
        orgId = orgId,
        currentCycleStartedAt = currentCycleStartedAt,
      ).get

      organizationBillingDAO.resetBillingMonthlyCycle(
        updateSpecificOrg = None,
      ) match {

        case Failure(exception) =>

          println(s"Error resetting billing cycle: ${LogHelpers.getStackTraceAsString(exception)}")

          assert(false)

        case Success(organizations) =>

          val o = organizationDAO.getOrgWithCurrentData(orgId = orgId).get.get

          assert(
            organizations.map(_.id).contains(orgId.id) &&
              o.plan.current_cycle_started_at == currentCycleStartedAt.plusMonths(1)
          )

      }

    }

    it("should set current billing cycle to the provided date when resetting for a specific organization") {

      val initialData = NewAccountAndEmailSettingData.createBasicAccountData().get

      val account = initialData.account

      val orgId = OrgId(account.org.id)

      val currentCycleStartedAt = DateTime.now()

      organizationBillingDAO.resetBillingMonthlyCycle(
        updateSpecificOrg = Some(
          ResetBillingMonthlyCycleForOrg(
            orgId = orgId,
            currentCycleStartedAt = currentCycleStartedAt,
          )
        ),
      ) match {

        case Failure(exception) =>

          println(s"Error resetting billing cycle: ${LogHelpers.getStackTraceAsString(exception)}")

          assert(false)

        case Success(organizations) =>

          val o = organizationDAO.getOrgWithCurrentData(orgId = orgId).get.get

          assert(
            organizations.map(_.id).contains(orgId.id) &&
              o.plan.current_cycle_started_at == currentCycleStartedAt
          )

      }

    }

  }

}
