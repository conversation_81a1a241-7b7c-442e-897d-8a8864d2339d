package db_test_spec.api.accounts.dao

import api.accounts.Organization
import api.accounts.models.OrgId
import api.billing.PaymentGateway
import io.sr.billing_common.models.{PlanType, SrPlanLimits}
import org.joda.time.DateTime
import scalikejdbc.{DB, scalikejdbcSQLInterpolationImplicitDef}

import scala.util.Try

object OrganizationTestDAO {

  def updateOrgToPaid(
                       orgId: OrgId,
                         ): Try[Int] = Try {
    DB autoCommit  { implicit session =>
      sql"""
            UPDATE
             organizations
            SET
              plan_type = 'paid',
              plan_name = 'paid'
            WHERE
              id = ${orgId.id};
         """
        .update
        .apply()
    }
  }

  /**
    * 9-Jul-2024
    *
    * This is only for testing
    */
  def addLeadFinderCredits(
    orgId: OrgId,
    leadFinderBasePlanCredits: Long,
    leadFinderCredits: Long,
  ): Try[Int] = Try {
    DB autoCommit { implicit session =>
      sql"""
            UPDATE
             organizations
            SET
              lead_finder_credits = $leadFinderCredits,
              plan_lead_finder_credits_max = $leadFinderBasePlanCredits
            WHERE
              id = ${orgId.id};
         """
        .update
        .apply()
    }
  }

  def updateSubscriptionAndOrgMetadata(
                          planLimits: SrPlanLimits,

                          sr_plan_name: String,

                          planType: PlanType,
                          nextBillingDate: Option[DateTime],
                          paymentGateway: PaymentGateway.Value,
                          cancelledAt: Option[DateTime],
                          defaultLeadFinderCredits: Int,
                          maxManualLinkedinAccounts: Int,
                          currentCycleStartedAt: DateTime,
                          orgId: OrgId
                        ): Try[Option[Organization]] = Try {


    DB autoCommit { implicit session =>

      sql"""
          UPDATE organizations
          SET

            metadata = metadata || '{"allow_drip_condition": true}'::jsonb,


            plan_type = ${planType.toString},

            plan_name = ${sr_plan_name},
            base_licence_count = ${planLimits.base_licence_count},
            additional_licence_count = ${planLimits.additional_licence_count},


            total_sending_email_accounts = ${planLimits.total_sending_email_account},
            plan_prospects_contacted_max = ${planLimits.max_prospects_contacted},
            plan_li_manual_seats_max = $maxManualLinkedinAccounts,
            plan_li_automation_seats_max = ${planLimits.max_automated_linkedin_accounts},
            plan_client_teams_max = ${planLimits.max_client_teams},
            plan_crm_integrations_max = ${planLimits.max_crm_integrations},
            plan_calling_seats_max = ${planLimits.max_calling_seats},
            plan_prospects_saved_max = ${planLimits.max_prospects_saved},

            plan_lead_finder_credits_max = $defaultLeadFinderCredits,

            next_billing_date = $nextBillingDate,
            current_cycle_started_at = $currentCycleStartedAt,

            payment_gateway = ${if (cancelledAt.isDefined) None else Some(paymentGateway.toString)},

            error = null,
            error_reported_at = null,
            paused_till = null,
            error_code = null,
            new_prospects_paused_till = null,
            warning_msg = null,
            warning_at = null,
            warning_code = null,

            payment_due_invoice_link = null,
            payment_due_campaign_pause_at = null,
            payment_failed_at = null,

            cancelled_at = COALESCE(cancelled_at, $cancelledAt)

          WHERE id = ${orgId.id}
          RETURNING *;
      """
        .map(Organization.fromDb)
        .single
        .apply()
    }
  }

}
