package db_test_spec.api.sr_audit_logs

import api.accounts.{AccountEmail, TeamId}
import api.accounts.models.AccountId
import api.prospects.models.ProspectId
import api.sr_audit_logs.models.{EventDataType, EventLog}
import api.triggers.CRMIntegrationInDB
import db_test_spec.api.prospects.fixtures.ProspectFixtureForIntegrationTest
import db_test_spec.api.{DbTestingBeforeAllAndAfterAll, InitialData, SRSetupAndDeleteFixtures}
import db_test_spec.trigger.fixtures.WorkflowCrmSettingForIntegrationTest
import org.joda.time.DateTime
import utils.testapp.TestAppTrait

import scala.util.{Failure, Success}

class EventLogServiceSpec extends DbTestingBeforeAllAndAfterAll {
    describe("updatePushedForAttemptCreationAt") {
      it("should successfully update last pushed for attempt created at ") {

        val result = for {
          initialData: InitialData <- SRSetupAndDeleteFixtures.createInitialData()

          initialEventLog: EventLog <- {
            EventLogFixtureForIntegrationTest.createEventLog(
                data = EventLog(
                  event_log_id = "event_log_id_insertAttemptTriesLogInDB_default",
                  audit_request_log_id = "audit_request_log_id_insertAttemptTriesLogInDB_default",
                  event_data_type = EventDataType.ActivityEventDataType.EmailInvalidEventData(
                    prospectId = ProspectId(ProspectFixtureForIntegrationTest.createUpdateOrAssignProspect(
                      campaignId = None,
                      accountId = AccountId(initialData.account.internal_id),
                      teamId = TeamId(initialData.account.teams.head.team_id),
                      account = initialData.account,
                      generateProspectCountIfNoGivenProspect = 1
                    ).get.head.id),
                    accountId = AccountId(initialData.account.internal_id),
                    teamId = initialData.emailSetting.get.team_id
                  ),
                  team_id = initialData.head_team_id,
                  account_id = initialData.account.internal_id,
                  created_at = DateTime.now()
                ),
                initialData = initialData
              )
              .map(_.get)
          }

          workflow: CRMIntegrationInDB <- WorkflowCrmSettingForIntegrationTest.createCRM(
            teamId = TeamId(initialData.account.teams.head.team_id),
            accountId = AccountId(initialData.account.internal_id),
            accountEmail = AccountEmail(initialData.account.email)
          ).map(_.get)

          update: String <- eventLogService.updatePushedForAttemptCreationAt(
            teamId = TeamId(initialData.account.teams.head.team_id),
            event_log_id = initialEventLog.event_log_id
          )
        } yield {

          update
        }

        result match {
          case Failure(exception) => {
            println(s"An error occurred ${exception.printStackTrace()}")
            assert(false)
          }
          case Success(value) => {
            assert(true)
            assert(value == "event_log_id_insertAttemptTriesLogInDB_default")
          }
        }
      }

    }


  }
