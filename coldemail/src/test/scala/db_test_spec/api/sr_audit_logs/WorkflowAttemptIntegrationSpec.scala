package db_test_spec.api.sr_audit_logs

import api.CampaignStepForApi
import api.accounts.models.AccountId
import api.accounts.{AccountEmail, TeamId}
import api.campaigns.models.CampaignStepType
import api.campaigns.services.CampaignUuid
import api.prospects.EmailEventTypeData
import api.prospects.models.ProspectId
import api.prospects.service.{ProspectEmail, ProspectObjectForApi, ProspectTagForApi}
import api.sr_audit_logs.dao.EventAndAttemptDataForProcessing
import api.sr_audit_logs.dao_service.WorkFlowAttemptJedisDAOService
import api.sr_audit_logs.models.EventDataOutputV3.ProspectEmailEventOutput.ProspectEmailEventSent
import api.sr_audit_logs.models.{AttemptLog, AttemptLogId, AttemptStatus, CreateEventLogError, EventDataType, EventLog, WorkflowSettingData}
import api.sr_audit_logs.services.WorkFlowAttemptService
import api.triggers.{CRMIntegrationInDB, IntegrationModuleType, IntegrationType, UpdateCRMModuleLevelSettingsForm}
import app.test_fixtures.prospect.ProspectFixtures
import db_test_spec.api.sr_audit_logs.WorkflowAttemptForIntegrationTest.createAttemptLog
import db_test_spec.api.{DbTestingBeforeAllAndAfterAll, InitialData, SRSetupAndDeleteFixtures}
import db_test_spec.trigger.fixtures.WorkflowCrmSettingForIntegrationTest
import eventframework.MessageObject
import eventframework.MessageObject.EmailMessageObject
import io.smartreach.esp.api.emails.IEmailAddress
import org.joda.time.DateTime
import play.api.libs.json.{JsValue, Json}
import utils.{Helpers, SRLogger}
import utils.cronjobs.AttemptRetryCronService
import utils.mq.webhook.MQNewProcessWorkflowAttemptService

import scala.concurrent.Await
import scala.util.{Failure, Success, Try}

class WorkflowAttemptIntegrationSpec extends DbTestingBeforeAllAndAfterAll {

  describe("Attempt Test") {

    it("should successfully process 5 attempts") {

      // Create 5 different accounts, each in a different team
      val listOfInitialData: Seq[Try[InitialData]] = (0 until 5).map { _ =>
        SRSetupAndDeleteFixtures.createInitialData()
      }


      Helpers.seqTryToTrySeq(listOfInitialData) match {
        case Failure(exception) =>
          println(s"An Error occurred while preparing initial data: ${exception.getMessage}")
          exception.printStackTrace()
          assert(false)

        case Success(initialDataLists) =>
          var workflow_setting_ids: List[Long] = List()
          val attemptsCreatedByTeamId: Map[Long, Seq[EventAndAttemptDataForProcessing]] = initialDataLists.map { initialData =>


              val attemptResult = for {
                workflow <- WorkflowCrmSettingForIntegrationTest.createCRM(
                  teamId = TeamId(initialData.account.teams.head.team_id),
                  accountId = AccountId(initialData.account.internal_id),
                  accountEmail = AccountEmail(initialData.account.email)
                ).map(_.get)

                _ <- {

                  workflow_setting_ids = workflow_setting_ids ++ Seq(workflow.workflow_crm_setting_id)


                  trigger.updateCRMModuleLevelSettings(
                    teamId = initialData.account.teams.head.team_id,
                    data = UpdateCRMModuleLevelSettingsForm(
                      active = true,
                      create_or_update_record_in_crm = true,
                      track_activities = true,
                      create_record_if_not_exists = true
                    ),
                    integration_type = IntegrationType.HUBSPOT,
                    module_type = IntegrationModuleType.CONTACTS,
                    logger = Logger
                  )
                }



                createdAttempts <- Helpers.seqTryToTrySeq((0 until 5).map { i =>

                  val listOfEventType = List(EventDataType.ActivityEventDataType.EmailSentEventData(
                    email_scheduled_id = i,
                    prospect_id = ProspectId(initialData.prospectsResult.head.id),
                    teamId = initialData.emailSetting.get.team_id,
                    accountId = AccountId(initialData.account.internal_id),
                    campaignName = None,
                    campaignId = None
                  ))


                  val initialEventLogOpt: Option[String] = eventLogService.createEventLog(
                    event_data_type = listOfEventType.head,
                    teamId = initialData.head_team_id,
                    accountId = initialData.account.internal_id,
                    created_at = DateTime.now().minusMinutes(i)
                  ) match {
                    case Right(value) => Some(value)
                    case Left(error) => {
                      error match {
                        case CreateEventLogError.SQLException(error) => {
                          println(s"An Error Occurred ${error.printStackTrace()}")
                          None
                        }
                        case CreateEventLogError.CreateEventLogFailedInsert(message) => {
                          println(s"CreateEventLogFailedInsert ${message}")
                          None

                        }
                      }
                    }
                  }

                  initialEventLogOpt match {
                    case Some(initialEventLog) => createAttemptLog(
                      data = AttemptLog(
                        team_id = initialData.emailSetting.get.team_id,
                        account_id = AccountId(initialData.account.internal_id),
                        attempt_log_id = AttemptLogId(
                          s"attempt_log_id_createAttemptLog_default_tid_${initialData.head_team_id}_$i"
                        ),
                        event_log_id = initialEventLog,
                        attempt_setting = WorkflowSettingData(
                          workflow_crm_setting_id = workflow.workflow_crm_setting_id,
                          crm_type = workflow.crm,
                          module_type = workflow.module
                        ),
                        created_at = DateTime.now().plusMinutes(i),
                        attempt_status = AttemptStatus.YetToAttempt
                      ),
                      initialData = initialData,
                      eventType = listOfEventType.head.eventType
                    )

                    case None => ???
                  }


                })
              } yield {
                initialData.account.teams.head.team_id -> createdAttempts.flatten
              }

              attemptResult match {
                case Failure(exception) =>
                  println(s"An Error occurred during attempt processing: ${exception.getMessage}")
                  exception.printStackTrace()
                  None

                case Success(value) => Some(value)
              }
            }.filter(_.isDefined)
            .map(_.get)
            .toMap


          val attemptRetryCronService = new AttemptRetryCronService(
            workFlowAttemptService = workFlowAttemptService,
            workFlowAttemptJedisDAOService = workFlowAttemptJedisDAOService,
            mqNewProcessWorkflowAttemptService = mqNewProcessWorkflowAttemptService
          )

          (0 until 1).foreach(_ => {
            attemptRetryCronService.executeCron()
          })

          val count = WorkflowCrmSettingForIntegrationTest.numberOfAttemptsPushedToQueue(workflow_crm_setting_ids = workflow_setting_ids)

          assert(count == workflow_setting_ids.size)



        // Assertions

      }
    }
  }

}
