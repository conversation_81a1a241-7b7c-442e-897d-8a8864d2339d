package db_test_spec.api

import api.accounts.models.{AccountId, OrgId}
import api.accounts.sr_api_key_type.models.SRApiKeyType
import api.accounts.{Account, PermissionMethods, ReplyHandling, TeamId, UpdateTeamConfig}
import api.call.controller.CreateOrUpdateCallAccountData
import api.call.models
import api.call.models.{CallingServiceProvider, PhoneNumber, PhoneNumberUuid, PhoneSID, PhoneType, SubAccountUuid}
import api.campaigns.models.IgnoreProspectsInOtherCampaigns
import api.emails.EmailSetting
import api.linkedin.models.LinkedinAccountSettings
import api.prospects.CreateOrUpdateProspectsResult
import api.spammonitor.dao.UpdateEmailSendingStatusForm
import api.spammonitor.model.EmailSendingEntityTypeData.SendingEmailData
import api.spammonitor.service.UpdateEmailSendingStatusError
import app.TestAppConfig
import app.db_test.PostgresUtils
import app_services.blacklist_monitoring.models.BlacklistCheckStatus
import db_test_spec.api.accounts.fixtures.AccountFixtureForIntegrationTest
import db_test_spec.api.emails.fixtures.EmailSettingFixtureForIntegrationTest
import db_test_spec.api.linkedin.fixtures.{LinkedInSettingFixtureForIntegrationTest, LinkedinAccount}
import db_test_spec.api.prospects.fixtures.ProspectFixtureForIntegrationTest
import db_test_spec.api.whatsapp.WhatsappAccount
import eventframework.ProspectObject
import org.joda.time.DateTime
import play.api.db.evolutions.Evolutions
import scalikejdbc.config.DBs
import scalikejdbc.{DB, scalikejdbcSQLInterpolationImplicitDef}
import utils.SRLogger
import utils.testapp.TestAppTrait

import scala.util.{Failure, Success, Try}

case class InitialData (
                         account: Account,
                         emailSetting: Option[EmailSetting],
                         linkedinAccountSettings: Option[LinkedinAccount],
                         whatsappAccount: Option[WhatsappAccount] = None,
                         prospectsResult: Seq[ProspectObject],
                         callSettingUuid: Option[String],
                         head_team_id: Long, // there could be multiple teams, just taking the first
                         teamUserLevelKey : String
                       )

object SRSetupAndDeleteFixtures extends TestAppTrait {

  //private var initialData: Option[InitialData] = None



   def createInitialData(create_org_calling_credits: Boolean = false)(using Logger: SRLogger): Try[InitialData] = {

    for {
      update_rep_servers <- Try{
        DB autoCommit {implicit session =>
          sql"""
               update rep_mail_servers
               set
               overall_blacklist_status = ${BlacklistCheckStatus.PASSED.toString},
               domain_blacklist_status = ${BlacklistCheckStatus.PASSED.toString},
               ip_blacklist_status = ${BlacklistCheckStatus.PASSED.toString}
             """
            .update
            .apply()
        }
      }
      account:Account <- AccountFixtureForIntegrationTest.createAccount().map(_.get)


      emailSetting:EmailSetting <-
        EmailSettingFixtureForIntegrationTest.createEmailSetting(
          accountId = AccountId(account.internal_id),
          orgId = OrgId(account.org.id),
          teamId = TeamId(account.teams.head.team_id),
          taId = account.teams.head.access_members.head.ta_id
        )


      linkedInSetting: LinkedinAccount <- LinkedInSettingFixtureForIntegrationTest.createLinkedInSetting(
          accountId = AccountId(account.internal_id),
          teamId = TeamId(account.teams.head.team_id)
        )

      subAccountUuid: Option[SubAccountUuid] <- Try {
        if(create_org_calling_credits) {
          DB.autoCommit { implicit session =>
            sql"""
           INSERT INTO org_calling_credits(
              sub_account_uuid,
              org_id,
              twl_sub_account_sid,
              twl_sub_account_auth_token,
              twl_sub_account_status,
              twl_sub_account_name,
              call_credits_remaining,
              call_credits_updated_at,
              call_credits,
              credit_unit,
              previous_usage_deducted,
              twl_check_usage_from,
              created_at,
              updated_at
           ) VALUES (
             ${java.util.UUID.randomUUID()},
             ${account.org.id},
             ${java.util.UUID.randomUUID()},
             ${"Token"},
             ${"active"},
             ${"ABCD"},
             ${1000},
             ${DateTime.now()},
             ${50},
             ${"USD"},
             ${10},
             ${DateTime.now()},
             ${DateTime.now()},
             ${DateTime.now()}
           )
           RETURNING sub_account_uuid
          """
              .map(rs =>
                SubAccountUuid(uuid = rs.string("sub_account_uuid")))
              .single
              .apply()
          }
        } else None
      }

      callSetting: Option[String] <- {
        if(subAccountUuid.isDefined){
          val phoneNumberUuid = s"phone_number_${java.util.UUID.randomUUID()}"

          callService.addNumberToDBAndResetCache(
            orgID = OrgId(account.org.id),
            accountId = AccountId(account.internal_id),
            teamId = TeamId(account.teams.head.team_id),
            addNumberData = CreateOrUpdateCallAccountData(
              first_name = "I",
              last_name = "ME",
              country_code = "India",
              phone_type = PhoneType.Mobile,
              enable_forward = false,
              forward_number = None,
              call_limit = 100,
              phone_uuid = Some(PhoneNumberUuid(phoneNumberUuid)),
              caller_id = None
            ),
            phoneUUID = PhoneNumberUuid(phoneNumberUuid),
            phoneNumber = models.PhoneNumber(java.util.UUID.randomUUID().toString),
            phoneNumberSID = PhoneSID("phoneSID"),
            orgSubAccountUUID = subAccountUuid.get,
            phone_number_cost_cents = 115,
            price_unit = "USD"
          ) match {
            case Left(value) => Failure(new Exception(s"Failed creating call account $value"))
            case Right(value) => Success(Some(phoneNumberUuid))
          }
        } else Success(None)

      }


      emailSendStatus <- Try({
        val updateEmailSendingStatusForm = UpdateEmailSendingStatusForm(
          entityType = SendingEmailData(
            orgId = OrgId(account.org.id),
            senderId = emailSetting.id.get.emailSettingId,
            emailSettingEmail = emailSetting.email
          ),
          orgId = OrgId(account.org.id),
          sendEmailStatus = AppSpecFixture.sendEmailStatus
        )
        emailSendingStatusDAO.addingEmailSendingStatusTry(
          updateEmailSendingStatusForm = updateEmailSendingStatusForm
        )
      })

      prospectsResult: Seq[ProspectObject] <- ProspectFixtureForIntegrationTest.createUpdateOrAssignProspect(
        accountId = AccountId(account.internal_id),
        teamId = TeamId(account.teams.head.access_members.head.team_id),
        account = account,
        generateProspectCountIfNoGivenProspect = 2
      )

      updateTeamConfig <- accountDAO.updateTeamConfig(
        teamId = TeamId(account.teams.head.team_id),
        accountId = AccountId(account.internal_id),
        data = UpdateTeamConfig(
          max_emails_per_prospect_per_day = 100,
          max_emails_per_prospect_per_week = 500,
          max_emails_per_prospect_account_per_day = Option(100),
          max_emails_per_prospect_account_per_week = Option(500),
          reply_handling = ReplyHandling.PAUSE_ALL_PROSPECT_ACCOUNT_CAMPAIGNS_ON_REPLY
        )
      )

      role = PermissionMethods.getUserRole(
        loggedinAccount = account,
        teamId = account.teams.head.team_id,
        ignoreFatalLogging = true,
        Logger = Logger
      )

      teamUserLevelKey <- credentialsAuthService.updateApiKey(
        role = role,
        enable_internal_email_accounts_api_for_warmuphero = true,
        accountId = AccountId(account.internal_id),
        orgId = OrgId(account.org.id),
        teamId = Option(TeamId(account.teams.head.team_id)),
        keyType = SRApiKeyType.SRTeamUserLevelKey
      )

    } yield {

      InitialData(
        account = account,
        emailSetting = Some(emailSetting),
        prospectsResult = prospectsResult,
        linkedinAccountSettings = Some(linkedInSetting),
        callSettingUuid = callSetting,
        head_team_id = account.teams.head.team_id,
        teamUserLevelKey = teamUserLevelKey.get
      )
    }
  }

  final def setupDbAndRedis(): Unit = {

    println("SETTING UP DB AND REDIS SERVER ...")

    DBs.setupAll()

    PostgresUtils.terminateAllConnections()
    
    
    dropAndCreateSchema()

    cacheServiceJedis.withJedis {
      jedis => jedis.flushDB()
    }

    val database = TestAppConfig.database

    Evolutions.applyEvolutions(database)

  }



  private def dropAndCreateSchema(): Unit = {
    println("DROPPING AND CREATING SCHEMA")
    DB.autoCommit(implicit session => {
      sql"""
           DROP SCHEMA public CASCADE;
           Delete from partman.part_config;
           CREATE SCHEMA public;
         """
        .update
        .apply()
    }
    )
  }

  /*
  def getInitialData()(using Logger: SRLogger): Try[InitialData] = {
    initialData match {
      case Some(value) => {
        println(s"value ${value}")
        Success(value)
      }
      case None =>
        setupDbRedisAndAccount().map{data =>
          initialData = Some(data)

          data
        }
    }
  }
  */
}
