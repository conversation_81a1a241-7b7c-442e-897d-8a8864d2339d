package db_test_spec.api.scheduler_report

import api.accounts.TeamId
import api.campaigns.{CampaignStepVariant, ChannelSettingUuid, IsUpdate}
import api.campaigns.models.{CampaignStepType, CreateCampaignStepVariantError, IgnoreProspectsInOtherCampaigns}
import api.campaigns.services.CampaignId
import api.linkedin.models.LinkedinAccountSettings
import api.prospects.models.ProspectId
import api.scheduler_report.ReportData.EmailValidationReportData
import api.scheduler_report.ReportType
import api.scheduler_report.ReportType.EMAIL_VALIDATION_APIS
import app.db_test.{CustomNotCategorizedAndDoNotContactIds, SchedulerTestInput}
import db_test_spec.api.campaigns.CampaignCreationFixtureForIntegrationTest.prospectService
import db_test_spec.api.{AppSpecFixture, DbTestingBeforeAllAndAfterAll, InitialData, InputForInitializingCampaignCreateData}
import db_test_spec.api.accounts.fixtures.NewAccountAndEmailSettingData
import db_test_spec.api.campaigns.{CampaignCreationData, CampaignCreationFixtureForIntegrationTest, InitializedCreateAndStartCampaignData}
import db_test_spec.api.campaigns.dao.CampaignProspectTestDAO
import db_test_spec.api.campaigns.fixtures.CreateNewCampaignFixture.findCategorizedAndDoNotContactCustomIds
import db_test_spec.api.scheduler.fixtures.ReSchedulingFixture
import org.joda.time.DateTime
import sr_scheduler.models.ChannelData.{EmailChannelData, LinkedinChannelData}
import sr_scheduler.models.ChannelType
import utils.SRLogger
import utils.emailvalidation.models.EmailValidationToolV2
import utils.helpers.LogHelpers
import utils.mq.channel_scheduler.channels.ScheduleTasksData

import scala.concurrent.Future
import scala.util.{Failure, Random, Success}

class SchedulerIntegrityReportDaoSpec extends DbTestingBeforeAllAndAfterAll {


  lazy val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

  describe("SchedulerIntegrityReportDaoSpec") {

    it("should run the query successfully for getCampaignsWhoseProspectHaveOptedOutButDueTasksArePresent"){
      //      given logger: SRLogger = AppSpecFixture.logger
      //      print(s"initialData: ${initialData.toString}" )
      val categorizedAndDoNotContactCustomIds: CustomNotCategorizedAndDoNotContactIds = findCategorizedAndDoNotContactCustomIds(prospect_categories = initialData.account.teams.head.prospect_categories_custom)
      val linkedSettingId = linkedinSettingService.findLinkedinSettingIdFromUuidAndTeamId(
        uuid = initialData.linkedinAccountSettings.get.settings.uuid,
        teamId = TeamId(initialData.account.teams.head.team_id)
      ).toOption
      val input: InputForInitializingCampaignCreateData = SchedulerTestInput.getInputForSchedulerIntegrationTest(
        sharedData = initialData,
        categorizedAndDoNotContactCustomIds = categorizedAndDoNotContactCustomIds,
        linkedSettingId = linkedSettingId,
        prospect_emails = Seq(Random.alphanumeric.take(10).mkString("") + "@gmail.com")
      )
      //     print(s"input: ${input.toString}" )

      val initializeCreateAndStartCampaignData: InitializedCreateAndStartCampaignData = AppSpecFixture.initializeCampaignDataForIntegrationTest(
        inputForInitializingCampaignCreateData = input
      )
      //      print(s"initializeCreateAndStartCampaignData: ${initializeCreateAndStartCampaignData.toString}")

      print(s"Account Id is ${input.account_id.id}")
      print(input.account_id.id)
      val scheduleTaskData = for {
        campaignCreationData: CampaignCreationData <- CampaignCreationFixtureForIntegrationTest.createAndStartCampaign(
          inputForInitializingCampaignCreateData = input,
          initializedData = initializeCreateAndStartCampaignData
        )
        isCampaignChannelSettingsExists: IsUpdate <- campaignDAO.isCampaignChannelSettingsExists(
          team_id =  TeamId(campaignCreationData.campaign.team_id),
          campaign_id =  CampaignId(campaignCreationData.campaign.id),
          channel_type = ChannelType.LinkedinChannel
        )

        _ <- campaignDAO.updateOrInsertChannelSettingInCampaign(
        team_id = TeamId(campaignCreationData.campaign.team_id),
        campaign_id = CampaignId(campaignCreationData.campaign.id),
        channelType = ChannelType.LinkedinChannel,
        channel_settings_uuid = ChannelSettingUuid(input.campaign_linkedin_settings_data.linkedin_account_setting_id) ,
        isUpdate = isCampaignChannelSettingsExists
        )

        add_one_more_step: Either[CreateCampaignStepVariantError, CampaignStepVariant] <- {

          campaignStepService.createVariant(
            orgId = input.org_id.id,
            data = initializeCreateAndStartCampaignData.campaignStepVariantCreateOrUpdateLinkedin.copy(
              parent_id = campaignCreationData.campaign.head_step_id.get
            ),
            teamId = input.team_id.id,
            userId = input.account_id.id,
            taId = input.ta_id,
            stepId = 0,
            campaignId = campaignCreationData.campaign.id,
            campaignHeadStepId = campaignCreationData.campaign.head_step_id
          )
        }

        result_1: ScheduleTasksData <- emailChannelScheduler.scheduleTasksForChannel(
          channelData = EmailChannelData(
            emailSettingId = input.email_setting_id.emailSettingId
          ),
          teamId = input.team_id.id,
          accountService = accountService,
          //accountDAO = accountDAO,
          emailNotificationService = emailNotificationService,
          campaignService = campaignService,
          campaignProspectDAO = campaignProspectDAO,
          campaignProspectService = campaignProspectService,
          campaignStepVariantDAO = campaignStepVariantDAO,
          campaignStepDAO = campaignStepDAO,
          srShuffleUtils = srShuffleUtils,
          emailServiceCompanion = emailServiceCompanion,
          templateService = templateService,
          taskDAO = taskDAO,
          taskService = taskService,
          campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO,
          campaignsMissingMergeTagService = campaignsMissingMergeTagService,
          srRedisSimpleLockServiceV2 = srRedisSimpleLockServiceV2,
          mqWebhookCompleted = mqWebhookCompleted,
          calendarAppService = calendarAppService,
          accountOrgBillingRelatedService = accountOrgBillingRelatedService,
          srRollingUpdateCoreService = srRollingUpdateCoreService
        )
        _ <- ReSchedulingFixture.initializeDataForReScheduling(
          orgId = input.org_id,
          accountId = input.account_id,
          teamId = input.team_id,
          emailSetting = initialData.emailSetting.get,
          campaign = campaignCreationData.campaign,
          prospectList = campaignCreationData.prospects.created_ids.map(p => ProspectId(p))
        ).map(resp =>{
          resp
        })

        result_2: ScheduleTasksData <- linkedinChannelScheduler.scheduleTasksForChannel(
          channelData = LinkedinChannelData(
            linkedinSettingId = input.campaign_linkedin_settings_data.linkedin_account_setting_id
          ),
          teamId = input.team_id.id,
          accountService = accountService,
          //accountDAO = accountDAO,
          emailNotificationService = emailNotificationService,
          campaignService = campaignService,
          campaignProspectDAO = campaignProspectDAO,
          campaignProspectService = campaignProspectService,
          campaignStepVariantDAO = campaignStepVariantDAO,
          campaignStepDAO = campaignStepDAO,
          srShuffleUtils = srShuffleUtils,
          emailServiceCompanion = emailServiceCompanion,
          templateService = templateService,
          taskDAO = taskDAO,
          taskService = taskService,
          campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO,
          campaignsMissingMergeTagService = campaignsMissingMergeTagService,
          srRedisSimpleLockServiceV2 = srRedisSimpleLockServiceV2,
          mqWebhookCompleted = mqWebhookCompleted,
          calendarAppService = calendarAppService,
          accountOrgBillingRelatedService = accountOrgBillingRelatedService,
          srRollingUpdateCoreService = srRollingUpdateCoreService
        )


        markProspectAsOptedOut <- Future.fromTry(CampaignProspectTestDAO.markProspectAsOptedOut(
          campaignId = CampaignId(id = campaignCreationData.campaign.id.toLong),
          prospectId = ProspectId(campaignCreationData.prospects.created_ids.head)
        )).recover{
          case e => throw e
        }

      } yield{
        campaignCreationData
      }

      scheduleTaskData.map{res =>{


        campaignDAO.getCampaignsWhoseProspectHaveOptedOutButDueTasksArePresent match {
          case Failure(exception) =>

            assert(false)
          case Success(value) =>
            assert(value.isDefined)
            assert(value.get.prospect_ids_list.map(_.id) == res.prospects.created_ids)
        }
      }}.recover{ case e =>
        assert(false)

      }


    }

    it("should run the query successfully for getCampaignsWhoseProspectHaveOptedOutButEmailsArePresent ") {

      //      given logger: SRLogger = AppSpecFixture.logger
      //      print(s"initialData: ${initialData.toString}" )
      val categorizedAndDoNotContactCustomIds: CustomNotCategorizedAndDoNotContactIds = findCategorizedAndDoNotContactCustomIds(prospect_categories = initialData.account.teams.head.prospect_categories_custom)

      val input: InputForInitializingCampaignCreateData = SchedulerTestInput.getInputForSchedulerIntegrationTest(
        sharedData = initialData,
        categorizedAndDoNotContactCustomIds = categorizedAndDoNotContactCustomIds,
        linkedSettingId = None,
        prospect_emails = Seq(Random.alphanumeric.take(10).mkString("") + "@gmail.com")
      )
      //     print(s"input: ${input.toString}" )

      val initializeCreateAndStartCampaignData: InitializedCreateAndStartCampaignData = AppSpecFixture.initializeCampaignDataForIntegrationTest(
        inputForInitializingCampaignCreateData = input
      )
      //      print(s"initializeCreateAndStartCampaignData: ${initializeCreateAndStartCampaignData.toString}")

      print(s"Account Id is ${input.account_id.id}")
      print(input.account_id.id)
      val scheduleTaskData = for {
        campaignCreationData: CampaignCreationData <- CampaignCreationFixtureForIntegrationTest.createAndStartCampaign(
          inputForInitializingCampaignCreateData = input,
          initializedData = initializeCreateAndStartCampaignData
        )

        result_1: ScheduleTasksData <- emailChannelScheduler.scheduleTasksForChannel(
          channelData = EmailChannelData(
            emailSettingId = input.email_setting_id.emailSettingId
          ),
          teamId = input.team_id.id,
          accountService = accountService,
          //accountDAO = accountDAO,
          emailNotificationService = emailNotificationService,
          campaignService = campaignService,
          campaignProspectDAO = campaignProspectDAO,
          campaignProspectService = campaignProspectService,
          campaignStepVariantDAO = campaignStepVariantDAO,
          campaignStepDAO = campaignStepDAO,
          srShuffleUtils = srShuffleUtils,
          emailServiceCompanion = emailServiceCompanion,
          templateService = templateService,
          taskDAO = taskDAO,
          taskService = taskService,
          campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO,
          campaignsMissingMergeTagService = campaignsMissingMergeTagService,
          srRedisSimpleLockServiceV2 = srRedisSimpleLockServiceV2,
          mqWebhookCompleted = mqWebhookCompleted,
          calendarAppService = calendarAppService,
          accountOrgBillingRelatedService = accountOrgBillingRelatedService,
          srRollingUpdateCoreService = srRollingUpdateCoreService
        )

        markProspectAsOptedOut <- Future.fromTry(CampaignProspectTestDAO.markProspectAsOptedOut(
          campaignId = CampaignId(id = campaignCreationData.campaign.id.toLong),
          prospectId = ProspectId(campaignCreationData.prospects.created_ids.head)
        )).recover {
          case e =>
            throw e
        }

      } yield {
        campaignCreationData
      }

      scheduleTaskData.map { res => {


        campaignDAO.getCampaignsWhoseProspectHaveOptedOutButEmailsArePresent match {
          case Failure(exception) =>

            assert(false)
          case Success(value) =>
            assert(value.isDefined)
            assert(value.get.prospect_ids_list.map(_.id) == res.prospects.created_ids)
        }
      }
      }.recover { case e =>

        assert(false)

      }
    }


    it("should run the query successfully for getCampaignWhoseProspectStatusIsCompletedOrPausedButDueTaskArePresents ") {


      
      //      print(s"initialData: ${initialData.toString}" )
      val categorizedAndDoNotContactCustomIds: CustomNotCategorizedAndDoNotContactIds = findCategorizedAndDoNotContactCustomIds(prospect_categories = initialData.account.teams.head.prospect_categories_custom)
      val linkedSettingId = linkedinSettingService.findLinkedinSettingIdFromUuidAndTeamId(
        uuid = initialData.linkedinAccountSettings.get.settings.uuid,
        teamId = TeamId(initialData.account.teams.head.team_id)
      ).toOption
      val input: InputForInitializingCampaignCreateData = SchedulerTestInput.getInputForSchedulerIntegrationTest(
        sharedData = initialData,
        categorizedAndDoNotContactCustomIds = categorizedAndDoNotContactCustomIds,
        linkedSettingId = linkedSettingId,
        prospect_emails = Seq(Random.alphanumeric.take(10).mkString("") + "@gmail.com")
      )
      //     print(s"input: ${input.toString}" )

      val initializeCreateAndStartCampaignData: InitializedCreateAndStartCampaignData = AppSpecFixture.initializeCampaignDataForIntegrationTest(
        inputForInitializingCampaignCreateData = input
      )
      //      print(s"initializeCreateAndStartCampaignData: ${initializeCreateAndStartCampaignData.toString}")



      val scheduleTaskData = for {
        campaignCreationData: CampaignCreationData <- CampaignCreationFixtureForIntegrationTest.createAndStartCampaign(
          inputForInitializingCampaignCreateData = input,
          initializedData = initializeCreateAndStartCampaignData
        )
        isCampaignChannelSettingsExists: IsUpdate <- campaignDAO.isCampaignChannelSettingsExists(
          team_id = TeamId(campaignCreationData.campaign.team_id),
          campaign_id = CampaignId(campaignCreationData.campaign.id),
          channel_type = ChannelType.LinkedinChannel
        )

        _ <- campaignDAO.updateOrInsertChannelSettingInCampaign(
          team_id = TeamId(campaignCreationData.campaign.team_id),
          campaign_id = CampaignId(campaignCreationData.campaign.id),
          channelType = ChannelType.LinkedinChannel,
          channel_settings_uuid = ChannelSettingUuid(input.campaign_linkedin_settings_data.linkedin_account_setting_id),
          isUpdate = isCampaignChannelSettingsExists
        )

        add_one_more_step: Either[CreateCampaignStepVariantError, CampaignStepVariant] <- {

          campaignStepService.createVariant(
            orgId = input.org_id.id,
            data = initializeCreateAndStartCampaignData.campaignStepVariantCreateOrUpdateLinkedin.copy(
              parent_id = campaignCreationData.campaign.head_step_id.get
            ),
            teamId = input.team_id.id,
            userId = input.account_id.id,
            taId = input.ta_id,
            stepId = 0,
            campaignId = campaignCreationData.campaign.id,
            campaignHeadStepId = campaignCreationData.campaign.head_step_id
          )
        }

        result_1: ScheduleTasksData <- emailChannelScheduler.scheduleTasksForChannel(
          channelData = EmailChannelData(
            emailSettingId = input.email_setting_id.emailSettingId
          ),
          teamId = input.team_id.id,
          accountService = accountService,
          //accountDAO = accountDAO,
          emailNotificationService = emailNotificationService,
          campaignService = campaignService,
          campaignProspectDAO = campaignProspectDAO,
          campaignProspectService = campaignProspectService,
          campaignStepVariantDAO = campaignStepVariantDAO,
          campaignStepDAO = campaignStepDAO,
          srShuffleUtils = srShuffleUtils,
          emailServiceCompanion = emailServiceCompanion,
          templateService = templateService,
          taskDAO = taskDAO,
          taskService = taskService,
          campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO,
          campaignsMissingMergeTagService = campaignsMissingMergeTagService,
          srRedisSimpleLockServiceV2 = srRedisSimpleLockServiceV2,
          mqWebhookCompleted = mqWebhookCompleted,
          calendarAppService = calendarAppService,
          accountOrgBillingRelatedService = accountOrgBillingRelatedService,
          srRollingUpdateCoreService = srRollingUpdateCoreService
        )
        _ <- ReSchedulingFixture.initializeDataForReScheduling(
          orgId = input.org_id,
          accountId = input.account_id,
          teamId = input.team_id,
          emailSetting = initialData.emailSetting.get,
          campaign = campaignCreationData.campaign,
          prospectList = campaignCreationData.prospects.assigned_ids.map(p => ProspectId(p))
        ).map(resp => {
          resp
        })

        result_2: ScheduleTasksData <- {
          linkedinChannelScheduler.scheduleTasksForChannel(
            channelData = LinkedinChannelData(
              linkedinSettingId = input.campaign_linkedin_settings_data.linkedin_account_setting_id
            ),
            teamId = input.team_id.id,
            accountService = accountService,
            //accountDAO = accountDAO,
            emailNotificationService = emailNotificationService,
            campaignService = campaignService,
            campaignProspectDAO = campaignProspectDAO,
            campaignProspectService = campaignProspectService,
            campaignStepVariantDAO = campaignStepVariantDAO,
            campaignStepDAO = campaignStepDAO,
            srShuffleUtils = srShuffleUtils,
            emailServiceCompanion = emailServiceCompanion,
            templateService = templateService,
            taskDAO = taskDAO,
            taskService = taskService,
            campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO,
            campaignsMissingMergeTagService = campaignsMissingMergeTagService,
            srRedisSimpleLockServiceV2 = srRedisSimpleLockServiceV2,
            mqWebhookCompleted = mqWebhookCompleted,
            calendarAppService = calendarAppService,
            accountOrgBillingRelatedService = accountOrgBillingRelatedService,
            srRollingUpdateCoreService = srRollingUpdateCoreService
          )
        }


        markProspectAsCompleted <- Future.fromTry(CampaignProspectTestDAO.markProspectAsCompleted(
          campaignId = CampaignId(id = campaignCreationData.campaign.id.toLong),
          prospectId = ProspectId(campaignCreationData.prospects.created_ids.head)
        )).recover {
          case e =>
            throw e
        }

      } yield {
        campaignCreationData
      }

      scheduleTaskData.map { res => {


        campaignDAO.getCampaignWhoseProspectStatusIsCompletedOrPausedButDueTaskArePresents match {
          case Failure(exception) =>

            assert(false)
          case Success(value) =>
            assert(value.isDefined)
            assert(value.get.prospect_ids_list.map(_.id) == res.prospects.created_ids)
        }
      }
      }.recover { case e =>

        assert(false)

      }

    }

    it("should run the query successfully for getCampaignWhoseProspectStatusIsCompletedOrPausedButEmailsArePresent ") {
      //      print(s"initialData: ${initialData.toString}" )
      val categorizedAndDoNotContactCustomIds: CustomNotCategorizedAndDoNotContactIds = findCategorizedAndDoNotContactCustomIds(prospect_categories = initialData.account.teams.head.prospect_categories_custom)

      val input: InputForInitializingCampaignCreateData = SchedulerTestInput.getInputForSchedulerIntegrationTest(
        sharedData = initialData,
        categorizedAndDoNotContactCustomIds = categorizedAndDoNotContactCustomIds,
        linkedSettingId = None,
        prospect_emails = Seq(Random.alphanumeric.take(10).mkString("") + "@gmail.com")
      )
      //     print(s"input: ${input.toString}" )

      val initializeCreateAndStartCampaignData: InitializedCreateAndStartCampaignData = AppSpecFixture.initializeCampaignDataForIntegrationTest(
        inputForInitializingCampaignCreateData = input
      )
      //      print(s"initializeCreateAndStartCampaignData: ${initializeCreateAndStartCampaignData.toString}")

      print(s"Account Id is ${input.account_id.id}")
      print(input.account_id.id)
      val scheduleTaskData = for {
        campaignCreationData: CampaignCreationData <- CampaignCreationFixtureForIntegrationTest.createAndStartCampaign(
          inputForInitializingCampaignCreateData = input,
          initializedData = initializeCreateAndStartCampaignData
        )

        result_1: ScheduleTasksData <- emailChannelScheduler.scheduleTasksForChannel(
          channelData = EmailChannelData(
            emailSettingId = input.email_setting_id.emailSettingId
          ),
          teamId = input.team_id.id,
          accountService = accountService,
          //accountDAO = accountDAO,
          emailNotificationService = emailNotificationService,
          campaignService = campaignService,
          campaignProspectDAO = campaignProspectDAO,
          campaignProspectService = campaignProspectService,
          campaignStepVariantDAO = campaignStepVariantDAO,
          campaignStepDAO = campaignStepDAO,
          srShuffleUtils = srShuffleUtils,
          emailServiceCompanion = emailServiceCompanion,
          templateService = templateService,
          taskDAO = taskDAO,
          taskService = taskService,
          campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO,
          campaignsMissingMergeTagService = campaignsMissingMergeTagService,
          srRedisSimpleLockServiceV2 = srRedisSimpleLockServiceV2,
          mqWebhookCompleted = mqWebhookCompleted,
          calendarAppService = calendarAppService,
          accountOrgBillingRelatedService = accountOrgBillingRelatedService,
          srRollingUpdateCoreService = srRollingUpdateCoreService
        )

        markProspectAsCompleted <- Future.fromTry(CampaignProspectTestDAO.markProspectAsCompleted(
          campaignId = CampaignId(id = campaignCreationData.campaign.id.toLong),
          prospectId = ProspectId(campaignCreationData.prospects.created_ids.head)
        )).recover {
          case e =>
            throw e
        }

      } yield {
        campaignCreationData
      }


      scheduleTaskData.map { res => {


        campaignDAO.getCampaignWhoseProspectStatusIsCompletedOrPausedButEmailsArePresent match {
          case Failure(exception) =>
            println(s"${LogHelpers.getStackTraceAsString(exception)}")

            assert(false)
          case Success(value) =>
            print(s"Value is ${value}")
            assert(value.isDefined)
            assert(value.get.prospect_ids_list.map(_.id) == res.prospects.created_ids)
        }
      }
      }.recover { case e =>
        println(s"${LogHelpers.getStackTraceAsString(e)}")

        assert(false)

      }

    }

    it("should  execute the query correctly") {


      val result = schedulerIntegrityReportDao.getReportDataForLast24Hours

      assert(result.isSuccess)
    }
  }


    describe("insertReportData"){

        it("should insert the report Data into the schedulerIntegrityReportDao table"){

            val reportType: ReportType = EMAIL_VALIDATION_APIS

            val apiValidationReportData : List[EmailValidationReportData] = List(
                EmailValidationReportData(
                    apiTool = EmailValidationToolV2.BOUNCER.toString,
                    totalEmailsValidated = 2000,
                    totalDeliverableEmails = 1300,
                    totalRiskyEmails = 500,
                    totalUnknownEmails = 10,
                    totalInvalidEmails = 190,
                    totalApproxAPIDownTimeInMinutes = 0,
                   report_type  = ReportType.EMAIL_VALIDATION_APIS

                ),
                EmailValidationReportData(
                    apiTool = EmailValidationToolV2.DEBOUNCE.toString,
                    totalEmailsValidated = 50000,
                    totalDeliverableEmails = 32000,
                    totalRiskyEmails = 8000,
                    totalUnknownEmails = 3000,
                    totalInvalidEmails = 7000,
                    totalApproxAPIDownTimeInMinutes = 180,
                    report_type  = ReportType.EMAIL_VALIDATION_APIS

                ),
                EmailValidationReportData(
                    apiTool = EmailValidationToolV2.LISTCLEAN.toString,
                    totalEmailsValidated = 5989,
                    totalDeliverableEmails = 5000,
                    totalRiskyEmails = 900,
                    totalUnknownEmails = 80,
                    totalInvalidEmails = 9,
                    totalApproxAPIDownTimeInMinutes = 50,
                    report_type  = ReportType.EMAIL_VALIDATION_APIS

                )


            )

            val reportAddedToTable = schedulerIntegrityReportDao.insertListOfReportData(reportType,apiValidationReportData)

            println(reportAddedToTable)
            assert(reportAddedToTable.isSuccess)


        }
    }

    describe("checkIfCronCanRunForValidationReport"){
        it("Should return false because report for last day exists in the table"){

            val startDateTime = DateTime.now().withTimeAtStartOfDay()
            val endDateTime = startDateTime.plusDays(1).minusSeconds(1)


            val isExecuteCron = schedulerIntegrityReportDao.checkIfCronCanRunForReports(startDateTime = startDateTime,endDateTime=endDateTime,reportType = EMAIL_VALIDATION_APIS)

            println(isExecuteCron)
            assert(isExecuteCron == Success(false))
        }
    }




}
