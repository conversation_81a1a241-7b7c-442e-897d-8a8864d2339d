package db_test_spec.api.leadFinder.service

import api.AppConfig
import api.accounts.TeamId
import api.accounts.models.{AccountId, OrgId}
import api.campaigns.Campaign
import api.campaigns.services.CampaignId
import api.lead_finder.DAO.AddLeadToProspectListData
import api.lead_finder.controller.AddLeadsIntoProspectListForm
import api.lead_finder.models.{ContactType, LeadId, LeadUuid}
import db_test_spec.api.accounts.dao.OrganizationTestDAO
import db_test_spec.api.accounts.fixtures.{LeadFinderFixture, NewAccountAndEmailSettingData}
import db_test_spec.api.campaigns.test_utils.CampaignUtils
import db_test_spec.api.{DbTestingBeforeAllAndAfterAll, InitialData}
import io.smartreach.esp.api.emails.EmailSettingId
import utils.emailvalidation.ValidationResultWithTeamIdAndAnalysisId
import utils.emailvalidation.ValidationResultWithTeamIdAndAnalysisId.ProspectEmailValidationResultWithTeamIdAndAnalysisId
import utils.emailvalidation.models.EmailValidationInitiator

import scala.concurrent.Future
import scala.util.{Failure, Random, Success}

class LeadFinderValidationServiceSpec extends DbTestingBeforeAllAndAfterAll {

  describe("Test addLeadsIntoProspectList") {

    it("should revert credits correctly when all leads were imported with phone number") {

      val initialData_1: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val orgId_1 = OrgId(id = initialData_1.account.org.id)

      val accountId_1 = AccountId(id = initialData_1.account.internal_id)

      val team_1 = initialData_1.account.teams.head

      val teamId_1 = TeamId(id = team_1.team_id)

      val taId_1 = team_1.access_members.head.ta_id

      val emailSettingId_1: EmailSettingId = initialData_1.emailSetting.get.id.get

      val remainingCreditsBeforeOrg1 = organizationService.getRemainingLeadFinderCredits(orgId = orgId_1).get.toLong


      // ----------

      val initialData_2: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val orgId_2 = OrgId(id = initialData_2.account.org.id)

      val accountId_2 = AccountId(id = initialData_2.account.internal_id)

      val team_2 = initialData_2.account.teams.head

      val teamId_2 = TeamId(id = team_2.team_id)

      val taId_2 = team_2.access_members.head.ta_id

      val remainingCreditsBeforeOrg2 = organizationService.getRemainingLeadFinderCredits(orgId = orgId_2).get.toLong


      // Enable LeadFinder email validation for org.
      AppConfig.RollingUpdates.allowedOrgIdsForEmailValidationForLeadFinder_local =
        AppConfig.RollingUpdates.allowedOrgIdsForEmailValidationForLeadFinder_local ++ Set(orgId_1.id, orgId_2.id)

      val leadIds_1: Seq[LeadId] = LeadFinderFixture.addLeadFinderProspects(
        teamId = teamId_1,
        taId = taId_1,
        accountId = accountId_1,
        numOfLeads = 5,
        data = Seq(),
      ).get


      val leadIds_2: Seq[LeadId] = LeadFinderFixture.addLeadFinderProspects(
        teamId = teamId_2,
        taId = taId_2,
        accountId = accountId_2,
        numOfLeads = 5,
        data = Seq(),
      ).get

      val leadUUIDs_1: Seq[LeadUuid] = srUuidService.getLeadFinderProspectUUIDsFromIds(
        teamIdOpt = Some(teamId_1),
        leadFinderIds = leadIds_1,
      ).get

      val leadUUIDs_2: Seq[LeadUuid] = srUuidService.getLeadFinderProspectUUIDsFromIds(
        teamIdOpt = Some(teamId_2),
        leadFinderIds = leadIds_2,
      ).get

      val leads_1: List[AddLeadToProspectListData] = leadFinderService.getLeadsByUuid(leadUuids = leadUUIDs_1).get

      val leads_2: List[AddLeadToProspectListData] = leadFinderService.getLeadsByUuid(leadUuids = leadUUIDs_2).get


      // Pretend validation was done, we randomly set the lead email was valid or invalid
      val validationRes_1: List[ProspectEmailValidationResultWithTeamIdAndAnalysisId] = leads_1.map { lead =>

        val isValid = Random.nextBoolean()

        ProspectEmailValidationResultWithTeamIdAndAnalysisId(
          emailDeliveryAnalysisId = None,
          teamId = teamId_1,
          email = lead.email.get,
          isValid = isValid,
          emailValidationId = 1,
          validationInitiator = EmailValidationInitiator.InitiatedByLeadFinder
        )

      }

      // Pretend validation was done, we randomly set the lead email was valid or invalid
      val validationRes_2: List[ProspectEmailValidationResultWithTeamIdAndAnalysisId] = leads_2.map { lead =>

        val isValid = Random.nextBoolean()

        ProspectEmailValidationResultWithTeamIdAndAnalysisId(
          emailDeliveryAnalysisId = None,
          teamId = teamId_2,
          email = lead.email.get,
          isValid = isValid,
          emailValidationId = 1,
          validationInitiator = EmailValidationInitiator.InitiatedByLeadFinder
        )

      }


      val res = for {

        campaign_1: Campaign <- CampaignUtils.createNotStartedCampaignWithSteps(
          orgId = orgId_1,
          teamId = teamId_1,
          accountId = accountId_1,
          taId = taId_1,
          emailSettingId = emailSettingId_1,
          stepTypes = Seq(),
          ownerFirstName = initialData_1.account.first_name.get,
        )

        addLeadsIntoProspectListForm_1: AddLeadsIntoProspectListForm = AddLeadsIntoProspectListForm(
          lead_finder_prospect_ids = leadUUIDs_1.toList,
          list_name = s"${orgId_1.id}_${teamId_1.id}_${accountId_1.id}_${leadUUIDs_1.length}_list_name",
          campaign_id = Some(CampaignId(id = campaign_1.id)),
          include_email = true,
          include_phone = true,
          include_linkedin = false
        )

        addLeadsIntoProspectListForm_2: AddLeadsIntoProspectListForm = AddLeadsIntoProspectListForm(
          lead_finder_prospect_ids = leadUUIDs_2.toList,
          list_name = s"${orgId_2.id}_${teamId_2.id}_${accountId_2.id}_${leadUUIDs_2.length}_list_name",
          campaign_id = None,
          include_email = true,
          include_phone = true,
          include_linkedin = false
        )

        _: Seq[Long] <- Future.fromTry {

          leadFinderService.addLeadsIntoProspectList(
            addLeads = addLeadsIntoProspectListForm_1,
            orgId = orgId_1,
            accountId = accountId_1,
            teamId = teamId_1,
            doerAccount = initialData_1.account,
            permittedAccountIdsForEditingProspects = initialData_1.account.teams.flatMap(_.access_members.map(_.user_id))
          ) match {

            case Left(err) =>

              println(err)

              Failure(new Exception("Failed to add leads into prospect list"))

            case Right(res) =>

              Success(res)

          }

        }

        _: Seq[Long] <- Future.fromTry {

          leadFinderService.addLeadsIntoProspectList(
            addLeads = addLeadsIntoProspectListForm_2,
            orgId = orgId_2,
            accountId = accountId_2,
            teamId = teamId_2,
            doerAccount = initialData_2.account,
            permittedAccountIdsForEditingProspects = initialData_2.account.teams.flatMap(_.access_members.map(_.user_id))
          ) match {

            case Left(err) =>

              println(err)

              Failure(new Exception("Failed to add leads into prospect list2"))

            case Right(res) =>

              Success(res)

          }

        }

        updateCount: Int <- Future.fromTry {
          leadFinderValidationService.updateLeadFinderValidationResults(
            data = (validationRes_1 ++ validationRes_2)
              .map(ValidationResultWithTeamIdAndAnalysisId.fromProspectEmailValidationResultWithTeamIdAndAnalysisId)

          )
        }

      } yield {

        updateCount

      }

      res.map { _ =>

        val charges = AppConfig.LeadFinderCharges.email + AppConfig.LeadFinderCharges.phone_number

        val validTotalCost_1 = validationRes_1.count(_.isValid) * charges
        val inValidTotalCost_1 = validationRes_1.count(_.isValid == false) * charges

        val leadFinderBillingLogs_1 = LeadFinderFixture.getLeadFinderBillingLogs(
          teamId = teamId_1,
          leadFinderProspectIds = leadIds_1
        ).get

        val totalCreditsUsed1 = leadFinderBillingLogs_1.map(_.creditsUsed).sum

        val batchRequest1 = LeadFinderFixture.getLeadValidationBatchRequest(
          teamId = teamId_1,
          leadValidationBatchReqId = leadFinderBillingLogs_1.head.leadValidationBatchReqIdOpt.get
        ).get.get

        val remainingCreditsOrg1 = organizationService.getRemainingLeadFinderCredits(orgId = orgId_1).get.toLong

        val validTotalCost_2 = validationRes_2.count(_.isValid) * charges
        val inValidTotalCost_2 = validationRes_2.count(_.isValid == false) * charges

        val leadFinderBillingLogs_2 = LeadFinderFixture.getLeadFinderBillingLogs(
          teamId = teamId_2,
          leadFinderProspectIds = leadIds_2
        ).get

        val totalCreditsUsed2 = leadFinderBillingLogs_2.map(_.creditsUsed).sum

        val batchRequest2 = LeadFinderFixture.getLeadValidationBatchRequest(
          teamId = teamId_2,
          leadValidationBatchReqId = leadFinderBillingLogs_2.head.leadValidationBatchReqIdOpt.get
        ).get.get

        val remainingCreditsOrg2 = organizationService.getRemainingLeadFinderCredits(orgId = orgId_2).get.toLong


        assert(
          totalCreditsUsed1 == validTotalCost_1 + inValidTotalCost_1 &&
            batchRequest1.totalFrozenPurchasedCredits == totalCreditsUsed1 &&
            batchRequest1.totalRevertedPurchasedCredits == inValidTotalCost_1 &&
            remainingCreditsOrg1 == ((remainingCreditsBeforeOrg1 - totalCreditsUsed1) + inValidTotalCost_1) &&

            totalCreditsUsed2 == validTotalCost_2 + inValidTotalCost_2 &&
            batchRequest2.totalFrozenPurchasedCredits == totalCreditsUsed2 &&
            batchRequest2.totalRevertedPurchasedCredits == inValidTotalCost_2 &&
            remainingCreditsOrg2 == ((remainingCreditsBeforeOrg2 - totalCreditsUsed2) + inValidTotalCost_2)
        )

      }.recover { e =>

        println(s"FAILURE - $e")

        assert(false)

      }

    }

    it(
      "should revert credits correctly when plan credits were consumed and all leads were imported with phone number"
    ) {

      val initialData_1: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val orgId_1 = OrgId(id = initialData_1.account.org.id)

      val accountId_1 = AccountId(id = initialData_1.account.internal_id)

      val team_1 = initialData_1.account.teams.head

      val teamId_1 = TeamId(id = team_1.team_id)

      val taId_1: Long = team_1.access_members.head.ta_id

      val emailSettingId_1: EmailSettingId = initialData_1.emailSetting.get.id.get

      val leadFinderBasePlanCredits_1 = 200 * 2

      OrganizationTestDAO.addLeadFinderCredits(
        orgId = orgId_1,
        leadFinderCredits = 100,
        leadFinderBasePlanCredits = leadFinderBasePlanCredits_1
      ).get

      val remainingCreditsBeforeOrg1 = organizationService.getRemainingLeadFinderCredits(orgId = orgId_1).get.toLong


      // ----------

      val initialData_2: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val orgId_2 = OrgId(id = initialData_2.account.org.id)

      val accountId_2 = AccountId(id = initialData_2.account.internal_id)

      val team_2 = initialData_2.account.teams.head

      val teamId_2 = TeamId(id = team_2.team_id)

      val taId_2 = team_2.access_members.head.ta_id

      val leadFinderBasePlanCredits_2 = 164 * 2

      OrganizationTestDAO.addLeadFinderCredits(
        orgId = orgId_2,
        leadFinderCredits = 293,
        leadFinderBasePlanCredits = leadFinderBasePlanCredits_2
      ).get

      val remainingCreditsBeforeOrg2 = organizationService.getRemainingLeadFinderCredits(orgId = orgId_2).get.toLong


      // Enable LeadFinder email validation for org.
      AppConfig.RollingUpdates.allowedOrgIdsForEmailValidationForLeadFinder_local =
        AppConfig.RollingUpdates.allowedOrgIdsForEmailValidationForLeadFinder_local ++ Set(orgId_1.id, orgId_2.id)

      val leadIds_1: Seq[LeadId] = LeadFinderFixture.addLeadFinderProspects(
        teamId = teamId_1,
        taId = taId_1,
        accountId = accountId_1,
        numOfLeads = 12,
        data = Seq(),
      ).get


      val leadIds_2: Seq[LeadId] = LeadFinderFixture.addLeadFinderProspects(
        teamId = teamId_2,
        taId = taId_2,
        accountId = accountId_2,
        numOfLeads = 14,
        data = Seq(),
      ).get

      val leadUUIDs_1: Seq[LeadUuid] = srUuidService.getLeadFinderProspectUUIDsFromIds(
        teamIdOpt = Some(teamId_1),
        leadFinderIds = leadIds_1,
      ).get

      val leadUUIDs_2: Seq[LeadUuid] = srUuidService.getLeadFinderProspectUUIDsFromIds(
        teamIdOpt = Some(teamId_2),
        leadFinderIds = leadIds_2,
      ).get

      val leads_1: List[AddLeadToProspectListData] = leadFinderService.getLeadsByUuid(leadUuids = leadUUIDs_1).get

      val leads_2: List[AddLeadToProspectListData] = leadFinderService.getLeadsByUuid(leadUuids = leadUUIDs_2).get

      val validationRes_1: List[ProspectEmailValidationResultWithTeamIdAndAnalysisId] = leads_1.map { lead =>

        val isValid = Random.nextBoolean()

        ProspectEmailValidationResultWithTeamIdAndAnalysisId(
          emailDeliveryAnalysisId = None,
          teamId = teamId_1,
          email = lead.email.get,
          isValid = isValid,
          emailValidationId = 1,
          validationInitiator = EmailValidationInitiator.InitiatedByLeadFinder
        )

      }

      val validationRes_2: List[ProspectEmailValidationResultWithTeamIdAndAnalysisId] = leads_2.map { lead =>

        val isValid = Random.nextBoolean()

        ProspectEmailValidationResultWithTeamIdAndAnalysisId(
          emailDeliveryAnalysisId = None,
          teamId = teamId_2,
          email = lead.email.get,
          isValid = isValid,
          emailValidationId = 1,
          validationInitiator = EmailValidationInitiator.InitiatedByLeadFinder
        )

      }


      val res = for {

        campaign_1: Campaign <- CampaignUtils.createNotStartedCampaignWithSteps(
          orgId = orgId_1,
          teamId = teamId_1,
          accountId = accountId_1,
          taId = taId_1,
          emailSettingId = emailSettingId_1,
          stepTypes = Seq(),
          ownerFirstName = initialData_1.account.first_name.get,
        )

        addLeadsIntoProspectListForm_1: AddLeadsIntoProspectListForm = AddLeadsIntoProspectListForm(
          lead_finder_prospect_ids = leadUUIDs_1.toList,
          list_name = s"${orgId_1.id}_${teamId_1.id}_${accountId_1.id}_${leadUUIDs_1.length}_list_name",
          campaign_id = Some(CampaignId(id = campaign_1.id)),
          include_email = true,
          include_phone = true,
          include_linkedin = false
        )

        addLeadsIntoProspectListForm_2: AddLeadsIntoProspectListForm = AddLeadsIntoProspectListForm(
          lead_finder_prospect_ids = leadUUIDs_2.toList,
          list_name = s"${orgId_2.id}_${teamId_2.id}_${accountId_2.id}_${leadUUIDs_2.length}_list_name",
          campaign_id = None,
          include_email = true,
          include_phone = true,
          include_linkedin = false
        )

        _: Seq[Long] <- Future.fromTry {

          leadFinderService.addLeadsIntoProspectList(
            addLeads = addLeadsIntoProspectListForm_1,
            orgId = orgId_1,
            accountId = accountId_1,
            teamId = teamId_1,
            doerAccount = initialData_1.account,
            permittedAccountIdsForEditingProspects = initialData_1.account.teams.flatMap(_.access_members.map(_.user_id))
          ) match {

            case Left(err) =>

              println(err)

              Failure(new Exception("Failed to add leads into prospect list"))

            case Right(res) =>

              Success(res)

          }

        }

        _: Seq[Long] <- Future.fromTry {

          leadFinderService.addLeadsIntoProspectList(
            addLeads = addLeadsIntoProspectListForm_2,
            orgId = orgId_2,
            accountId = accountId_2,
            teamId = teamId_2,
            doerAccount = initialData_2.account,
            permittedAccountIdsForEditingProspects = initialData_2.account.teams.flatMap(_.access_members.map(_.user_id))
          ) match {

            case Left(err) =>

              println(err)

              Failure(new Exception("Failed to add leads into prospect list2"))

            case Right(res) =>

              Success(res)

          }

        }

        updateCount: Int <- Future.fromTry {
          leadFinderValidationService.updateLeadFinderValidationResults(
            data = (validationRes_1 ++ validationRes_2)
          )
        }

      } yield {

        updateCount

      }

      res.map { _ =>

        val charges = AppConfig.LeadFinderCharges.email + AppConfig.LeadFinderCharges.phone_number

        val validTotalCost_1 = validationRes_1.count(_.isValid) * charges
        val inValidTotalCost_1 = validationRes_1.count(_.isValid == false) * charges

        val leadFinderBillingLogs_1 = LeadFinderFixture.getLeadFinderBillingLogs(
          teamId = teamId_1,
          leadFinderProspectIds = leadIds_1
        ).get

        val totalCreditsUsed1 = leadFinderBillingLogs_1.map(_.creditsUsed).sum

        val batchRequest1 = LeadFinderFixture.getLeadValidationBatchRequest(
          teamId = teamId_1,
          leadValidationBatchReqId = leadFinderBillingLogs_1.head.leadValidationBatchReqIdOpt.get
        ).get.get

        val remainingCreditsOrg1 = organizationService.getRemainingLeadFinderCredits(orgId = orgId_1).get.toLong

        val validTotalCost_2 = validationRes_2.count(_.isValid) * charges
        val inValidTotalCost_2 = validationRes_2.count(_.isValid == false) * charges

        val leadFinderBillingLogs_2 = LeadFinderFixture.getLeadFinderBillingLogs(
          teamId = teamId_2,
          leadFinderProspectIds = leadIds_2
        ).get

        val totalCreditsUsed2 = leadFinderBillingLogs_2.map(_.creditsUsed).sum

        val batchRequest2 = LeadFinderFixture.getLeadValidationBatchRequest(
          teamId = teamId_2,
          leadValidationBatchReqId = leadFinderBillingLogs_2.head.leadValidationBatchReqIdOpt.get
        ).get.get

        val remainingCreditsOrg2 = organizationService.getRemainingLeadFinderCredits(orgId = orgId_2).get


        val revertedCreditsCheck_1 = if (batchRequest1.totalRevertedPurchasedCredits < inValidTotalCost_1) {

          batchRequest1.totalRevertedPlanCredits == inValidTotalCost_1 - batchRequest1.totalRevertedPurchasedCredits

        } else {

          batchRequest1.totalRevertedPurchasedCredits == inValidTotalCost_1

        }

        val revertedCreditsCheck_2 = if (batchRequest2.totalRevertedPurchasedCredits < inValidTotalCost_2) {

          batchRequest2.totalRevertedPlanCredits == inValidTotalCost_2 - batchRequest2.totalRevertedPurchasedCredits

        } else {

          batchRequest2.totalRevertedPurchasedCredits == inValidTotalCost_2

        }

        assert(
          totalCreditsUsed1 == validTotalCost_1 + inValidTotalCost_1 &&
            batchRequest1.totalFrozenPlanCredits == leadFinderBasePlanCredits_1 &&
            batchRequest1.totalFrozenPurchasedCredits == totalCreditsUsed1 - batchRequest1.totalFrozenPlanCredits &&
            revertedCreditsCheck_1 &&
            remainingCreditsOrg1 == ((remainingCreditsBeforeOrg1 - totalCreditsUsed1) + inValidTotalCost_1)

            &&

            totalCreditsUsed2 == validTotalCost_2 + inValidTotalCost_2 &&
            batchRequest2.totalFrozenPlanCredits == leadFinderBasePlanCredits_2 &&
            batchRequest2.totalFrozenPurchasedCredits == totalCreditsUsed2 - batchRequest2.totalFrozenPlanCredits &&
            revertedCreditsCheck_2 &&
            remainingCreditsOrg2 == ((remainingCreditsBeforeOrg2 - totalCreditsUsed2) + inValidTotalCost_2)
        )

      }.recover { e =>

        println(s"FAILURE - $e")

        assert(false)

      }

    }

    it("should revert credits correctly when all leads were import with only email") {

      val initialData_1: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val orgId = OrgId(id = initialData_1.account.org.id)

      val accountId = AccountId(id = initialData_1.account.internal_id)

      val team = initialData_1.account.teams.head

      val teamId = TeamId(id = team.team_id)

      val taId = team.access_members.head.ta_id

      val emailSettingId: EmailSettingId = initialData_1.emailSetting.get.id.get

      val remainingCreditsBeforeOrg = organizationService.getRemainingLeadFinderCredits(orgId = orgId).get.toLong

      // Enable LeadFinder email validation for org.
      AppConfig.RollingUpdates.allowedOrgIdsForEmailValidationForLeadFinder_local =
        AppConfig.RollingUpdates.allowedOrgIdsForEmailValidationForLeadFinder_local ++ Set(orgId.id)

      val leadIds: Seq[LeadId] = LeadFinderFixture.addLeadFinderProspects(
        teamId = teamId,
        taId = taId,
        accountId = accountId,
        numOfLeads = 11,
        data = Seq(),
      ).get

      val leadUUIDs: Seq[LeadUuid] = srUuidService.getLeadFinderProspectUUIDsFromIds(
        teamIdOpt = Some(teamId),
        leadFinderIds = leadIds,
      ).get

      val leads: List[AddLeadToProspectListData] = leadFinderService.getLeadsByUuid(leadUuids = leadUUIDs).get

      val validationRes: List[ProspectEmailValidationResultWithTeamIdAndAnalysisId] = leads.map { lead =>

        val isValid = Random.nextBoolean()

        ProspectEmailValidationResultWithTeamIdAndAnalysisId(
          emailDeliveryAnalysisId = None,
          teamId = teamId,
          email = lead.email.get,
          isValid = isValid,
          emailValidationId = 1,
          validationInitiator = EmailValidationInitiator.InitiatedByLeadFinder
        )

      }

      val res = for {

        campaign: Campaign <- CampaignUtils.createNotStartedCampaignWithSteps(
          orgId = orgId,
          teamId = teamId,
          accountId = accountId,
          taId = taId,
          emailSettingId = emailSettingId,
          stepTypes = Seq(),
          ownerFirstName = initialData_1.account.first_name.get,
        )

        addLeadsIntoProspectListForm: AddLeadsIntoProspectListForm = AddLeadsIntoProspectListForm(
          lead_finder_prospect_ids = leadUUIDs.toList,
          list_name = s"${orgId.id}_${teamId.id}_${accountId.id}_${leadUUIDs.length}_list_name",
          campaign_id = Some(CampaignId(id = campaign.id)),
          include_email = true,
          include_phone = false,
          include_linkedin = false
        )

        _: Seq[Long] <- Future.fromTry {

          leadFinderService.addLeadsIntoProspectList(
            addLeads = addLeadsIntoProspectListForm,
            orgId = orgId,
            accountId = accountId,
            teamId = teamId,
            doerAccount = initialData_1.account,
            permittedAccountIdsForEditingProspects = initialData_1.account.teams.flatMap(_.access_members.map(_.user_id))
          ) match {

            case Left(err) =>

              println(err)

              Failure(new Exception("Failed to add leads into prospect list"))

            case Right(res) =>

              Success(res)

          }

        }

        updateCount: Int <- Future.fromTry {
          leadFinderValidationService.updateLeadFinderValidationResults(
            data = validationRes
              .map(ValidationResultWithTeamIdAndAnalysisId.fromProspectEmailValidationResultWithTeamIdAndAnalysisId)
          )
        }

      } yield {

        updateCount

      }

      res.map { _ =>

        val emailCharges = AppConfig.LeadFinderCharges.email

        //        val phoneCharges = AppConfig.LeadFinderCharges.phone_number

        val validTotalEmailCost = validationRes.count(_.isValid) * emailCharges
        val inValidTotalEmailCost = validationRes.count(_.isValid == false) * emailCharges

        val leadFinderBillingLogs = LeadFinderFixture.getLeadFinderBillingLogs(
          teamId = teamId,
          leadFinderProspectIds = leadIds
        ).get

        val totalCreditsUsed = leadFinderBillingLogs.map(_.creditsUsed).sum

        val batchRequest = LeadFinderFixture.getLeadValidationBatchRequest(
          teamId = teamId,
          leadValidationBatchReqId = leadFinderBillingLogs.head.leadValidationBatchReqIdOpt.get
        ).get.get

        val remainingCreditsOrg = organizationService.getRemainingLeadFinderCredits(orgId = orgId).get.toLong

        assert(
          totalCreditsUsed == validTotalEmailCost + inValidTotalEmailCost &&
            batchRequest.totalFrozenPurchasedCredits == totalCreditsUsed &&
            batchRequest.totalRevertedPurchasedCredits == inValidTotalEmailCost &&
            remainingCreditsOrg == ((remainingCreditsBeforeOrg - totalCreditsUsed) + inValidTotalEmailCost)
        )

      }.recover { e =>

        println(s"FAILURE - $e")

        assert(false)

      }

    }

    it(
      "should revert credits correctly when all leads were imported with phone number but some lead don't have phone number"
    ) {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val orgId = OrgId(id = initialData.account.org.id)

      val accountId = AccountId(id = initialData.account.internal_id)

      val team_1 = initialData.account.teams.head

      val teamId = TeamId(id = team_1.team_id)

      val taId = team_1.access_members.head.ta_id

      val emailSettingId: EmailSettingId = initialData.emailSetting.get.id.get

      val remainingCreditsBeforeOrg1 = organizationService.getRemainingLeadFinderCredits(orgId = orgId).get.toLong

      // Enable LeadFinder email validation for org.
      AppConfig.RollingUpdates.allowedOrgIdsForEmailValidationForLeadFinder_local =
        AppConfig.RollingUpdates.allowedOrgIdsForEmailValidationForLeadFinder_local ++ Set(orgId.id)

      val leadIds: Seq[LeadId] = LeadFinderFixture.addLeadFinderProspects(
        teamId = teamId,
        taId = taId,
        accountId = accountId,
        numOfLeads = 5,
        data = Seq(),
        alwaysAddPhoneNumberForLead = false,
      ).get

      val leadUUIDs: Seq[LeadUuid] = srUuidService.getLeadFinderProspectUUIDsFromIds(
        teamIdOpt = Some(teamId),
        leadFinderIds = leadIds,
      ).get

      val leads: List[AddLeadToProspectListData] = leadFinderService.getLeadsByUuid(leadUuids = leadUUIDs).get

      val validationRes: List[ProspectEmailValidationResultWithTeamIdAndAnalysisId] = leads.map { lead =>

        val isValid = Random.nextBoolean()

        ProspectEmailValidationResultWithTeamIdAndAnalysisId(
          emailDeliveryAnalysisId = None,
          teamId = teamId,
          email = lead.email.get,
          isValid = isValid,
          emailValidationId = 1,
          validationInitiator = EmailValidationInitiator.InitiatedByLeadFinder
        )

      }

      val res = for {

        campaign: Campaign <- CampaignUtils.createNotStartedCampaignWithSteps(
          orgId = orgId,
          teamId = teamId,
          accountId = accountId,
          taId = taId,
          emailSettingId = emailSettingId,
          stepTypes = Seq(),
          ownerFirstName = initialData.account.first_name.get,
        )

        addLeadsIntoProspectListForm: AddLeadsIntoProspectListForm = AddLeadsIntoProspectListForm(
          lead_finder_prospect_ids = leadUUIDs.toList,
          list_name = s"${orgId.id}_${teamId.id}_${accountId.id}_${leadUUIDs.length}_list_name",
          campaign_id = Some(CampaignId(id = campaign.id)),
          include_email = true,
          include_phone = true,
          include_linkedin = false
        )

        _: Seq[Long] <- Future.fromTry {

          leadFinderService.addLeadsIntoProspectList(
            addLeads = addLeadsIntoProspectListForm,
            orgId = orgId,
            accountId = accountId,
            teamId = teamId,
            doerAccount = initialData.account,
            permittedAccountIdsForEditingProspects = initialData.account.teams.flatMap(_.access_members.map(_.user_id))
          ) match {

            case Left(err) =>

              println(err)

              Failure(new Exception("Failed to add leads into prospect list"))

            case Right(res) =>

              Success(res)

          }

        }

        updateCount: Int <- Future.fromTry {
          leadFinderValidationService.updateLeadFinderValidationResults(
            data = validationRes
              .map(ValidationResultWithTeamIdAndAnalysisId.fromProspectEmailValidationResultWithTeamIdAndAnalysisId)
          )
        }

      } yield {

        updateCount

      }

      res.map { _ =>

        val validEmails = validationRes.filter(_.isValid).map(_.email).toSet
        val invalidEmails = validationRes.filter(_.isValid == false).map(_.email)

        val leadFinderBillingLogs = LeadFinderFixture.getLeadFinderBillingLogs(
          teamId = teamId,
          leadFinderProspectIds = leadIds
        ).get

        val validLeadIds =
          leadFinderBillingLogs
            .filter(_.leadFinderContactType.contains(ContactType.Email))
            .filter(l => validEmails.contains(l.leadFinderContact))
            .map(_.leadFinderProspectId)

        val invalidLeadIds =
          leadFinderBillingLogs
            .filter(_.leadFinderContactType.contains(ContactType.Email))
            .filter(l => invalidEmails.contains(l.leadFinderContact))
            .map(_.leadFinderProspectId)

        val validLeadEmailAndPhoneCost =
          leadFinderBillingLogs
            .filter(l => validLeadIds.contains(l.leadFinderProspectId))
            .map(_.creditsUsed)
            .sum

        val invalidLeadEmailAndPhoneCost =
          leadFinderBillingLogs
            .filter(l => invalidLeadIds.contains(l.leadFinderProspectId))
            .map(_.creditsUsed)
            .sum

        val totalCreditsUsed = leadFinderBillingLogs.map(_.creditsUsed).sum

        val batchRequest = LeadFinderFixture.getLeadValidationBatchRequest(
          teamId = teamId,
          leadValidationBatchReqId = leadFinderBillingLogs.head.leadValidationBatchReqIdOpt.get
        ).get.get

        val remainingCreditsOrg1 = organizationService.getRemainingLeadFinderCredits(orgId = orgId).get.toLong

        assert(
          totalCreditsUsed == validLeadEmailAndPhoneCost + invalidLeadEmailAndPhoneCost &&
            batchRequest.totalFrozenPurchasedCredits == totalCreditsUsed &&
            batchRequest.totalRevertedPurchasedCredits == invalidLeadEmailAndPhoneCost &&
            remainingCreditsOrg1 == ((remainingCreditsBeforeOrg1 - totalCreditsUsed) + invalidLeadEmailAndPhoneCost)
        )

      }.recover { e =>

        println(s"FAILURE - $e")

        assert(false)

      }

    }

    it(
      "should revert credits correctly when plan credits were consumed and all leads were imported with phone number but some lead don't have phone number"
    ) {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val orgId = OrgId(id = initialData.account.org.id)

      val accountId = AccountId(id = initialData.account.internal_id)

      val team = initialData.account.teams.head

      val teamId = TeamId(id = team.team_id)

      val taId = team.access_members.head.ta_id

      val emailSettingId: EmailSettingId = initialData.emailSetting.get.id.get

      val leadFinderBasePlanCredits = 273

      OrganizationTestDAO.addLeadFinderCredits(
        orgId = orgId,
        leadFinderCredits = 143,
        leadFinderBasePlanCredits = leadFinderBasePlanCredits
      ).get

      val remainingCreditsBeforeOrg = organizationService.getRemainingLeadFinderCredits(orgId = orgId).get.toLong

      // Enable LeadFinder email validation for org.
      AppConfig.RollingUpdates.allowedOrgIdsForEmailValidationForLeadFinder_local =
        AppConfig.RollingUpdates.allowedOrgIdsForEmailValidationForLeadFinder_local ++ Set(orgId.id)

      val leadIds: Seq[LeadId] = LeadFinderFixture.addLeadFinderProspects(
        teamId = teamId,
        taId = taId,
        accountId = accountId,
        numOfLeads = 13,
        data = Seq(),
        alwaysAddPhoneNumberForLead = false,
      ).get

      val leadUUIDs: Seq[LeadUuid] = srUuidService.getLeadFinderProspectUUIDsFromIds(
        teamIdOpt = Some(teamId),
        leadFinderIds = leadIds,
      ).get

      val leads: List[AddLeadToProspectListData] = leadFinderService.getLeadsByUuid(leadUuids = leadUUIDs).get

      val validationRes: List[ProspectEmailValidationResultWithTeamIdAndAnalysisId] = leads.map { lead =>

        val isValid = Random.nextBoolean()

        ProspectEmailValidationResultWithTeamIdAndAnalysisId(
          emailDeliveryAnalysisId = None,
          teamId = teamId,
          email = lead.email.get,
          isValid = isValid,
          emailValidationId = 1,
          validationInitiator = EmailValidationInitiator.InitiatedByLeadFinder
        )

      }

      val res = for {

        campaign: Campaign <- CampaignUtils.createNotStartedCampaignWithSteps(
          orgId = orgId,
          teamId = teamId,
          accountId = accountId,
          taId = taId,
          emailSettingId = emailSettingId,
          stepTypes = Seq(),
          ownerFirstName = initialData.account.first_name.get,
        )

        addLeadsIntoProspectListForm: AddLeadsIntoProspectListForm = AddLeadsIntoProspectListForm(
          lead_finder_prospect_ids = leadUUIDs.toList,
          list_name = s"${orgId.id}_${teamId.id}_${accountId.id}_${leadUUIDs.length}_list_name",
          campaign_id = Some(CampaignId(id = campaign.id)),
          include_email = true,
          include_phone = true,
          include_linkedin = false
        )

        _: Seq[Long] <- Future.fromTry {

          leadFinderService.addLeadsIntoProspectList(
            addLeads = addLeadsIntoProspectListForm,
            orgId = orgId,
            accountId = accountId,
            teamId = teamId,
            doerAccount = initialData.account,
            permittedAccountIdsForEditingProspects = initialData.account.teams.flatMap(_.access_members.map(_.user_id))
          ) match {

            case Left(err) =>

              println(err)

              Failure(new Exception("Failed to add leads into prospect list"))

            case Right(res) =>

              Success(res)

          }

        }

        updateCount: Int <- Future.fromTry {
          leadFinderValidationService.updateLeadFinderValidationResults(
            data = validationRes
          )
        }

      } yield {

        updateCount

      }

      res.map { _ =>

        val validEmails = validationRes.filter(_.isValid).map(_.email).toSet
        val invalidEmails = validationRes.filter(_.isValid == false).map(_.email)

        val leadFinderBillingLogs = LeadFinderFixture.getLeadFinderBillingLogs(
          teamId = teamId,
          leadFinderProspectIds = leadIds
        ).get

        val validLeadIds =
          leadFinderBillingLogs
            .filter(_.leadFinderContactType.contains(ContactType.Email))
            .filter(l => validEmails.contains(l.leadFinderContact))
            .map(_.leadFinderProspectId)

        val invalidLeadIds =
          leadFinderBillingLogs
            .filter(_.leadFinderContactType.contains(ContactType.Email))
            .filter(l => invalidEmails.contains(l.leadFinderContact))
            .map(_.leadFinderProspectId)

        val validLeadEmailAndPhoneCost =
          leadFinderBillingLogs
            .filter(l => validLeadIds.contains(l.leadFinderProspectId))
            .map(_.creditsUsed)
            .sum

        val invalidLeadEmailAndPhoneCost =
          leadFinderBillingLogs
            .filter(l => invalidLeadIds.contains(l.leadFinderProspectId))
            .map(_.creditsUsed)
            .sum

        val totalCreditsUsed = leadFinderBillingLogs.map(_.creditsUsed).sum

        val batchRequest = LeadFinderFixture.getLeadValidationBatchRequest(
          teamId = teamId,
          leadValidationBatchReqId = leadFinderBillingLogs.head.leadValidationBatchReqIdOpt.get
        ).get.get

        val remainingCreditsOrg = organizationService.getRemainingLeadFinderCredits(orgId = orgId).get.toLong

        val revertedCreditsCheck = if (batchRequest.totalRevertedPurchasedCredits < invalidLeadEmailAndPhoneCost) {

          batchRequest.totalRevertedPlanCredits == invalidLeadEmailAndPhoneCost - batchRequest.totalRevertedPurchasedCredits

        } else {

          batchRequest.totalRevertedPurchasedCredits == invalidLeadEmailAndPhoneCost

        }

        val frozenCreditsCheck = if (batchRequest.totalFrozenPlanCredits < totalCreditsUsed) {

          batchRequest.totalRevertedPurchasedCredits == totalCreditsUsed - batchRequest.totalFrozenPlanCredits

        } else {

          batchRequest.totalFrozenPlanCredits == totalCreditsUsed &&
            batchRequest.totalRevertedPurchasedCredits == 0

        }

        assert(
          totalCreditsUsed == validLeadEmailAndPhoneCost + invalidLeadEmailAndPhoneCost &&
            frozenCreditsCheck &&
            revertedCreditsCheck &&
            remainingCreditsOrg == ((remainingCreditsBeforeOrg - totalCreditsUsed) + invalidLeadEmailAndPhoneCost)
        )

      }.recover { e =>

        println(s"FAILURE - $e")

        assert(false)

      }

    }

    it(
      "should update lead_finder_prospects table with validation results"
    ) {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val orgId = OrgId(id = initialData.account.org.id)

      val accountId = AccountId(id = initialData.account.internal_id)

      val team_1 = initialData.account.teams.head

      val teamId = TeamId(id = team_1.team_id)

      val taId = team_1.access_members.head.ta_id

      val emailSettingId: EmailSettingId = initialData.emailSetting.get.id.get

      OrganizationTestDAO.addLeadFinderCredits(
        orgId = orgId,
        leadFinderCredits = 450,
        leadFinderBasePlanCredits = 0,
      ).get

      val remainingCreditsBeforeOrg1 = organizationService.getRemainingLeadFinderCredits(orgId = orgId).get.toLong

      // Enable LeadFinder email validation for org.
      AppConfig.RollingUpdates.allowedOrgIdsForEmailValidationForLeadFinder_local =
        AppConfig.RollingUpdates.allowedOrgIdsForEmailValidationForLeadFinder_local ++ Set(orgId.id)

      val leadIds: Seq[LeadId] = LeadFinderFixture.addLeadFinderProspects(
        teamId = teamId,
        taId = taId,
        accountId = accountId,
        numOfLeads = 15,
        data = Seq(),
        alwaysAddPhoneNumberForLead = false,
      ).get

      val leadUUIDs: Seq[LeadUuid] = srUuidService.getLeadFinderProspectUUIDsFromIds(
        teamIdOpt = Some(teamId),
        leadFinderIds = leadIds,
      ).get

      val leads: List[AddLeadToProspectListData] = leadFinderService.getLeadsByUuid(leadUuids = leadUUIDs).get

      val validationRes: List[ProspectEmailValidationResultWithTeamIdAndAnalysisId] = leads.map { lead =>

        val isValid = Random.nextBoolean()

        ProspectEmailValidationResultWithTeamIdAndAnalysisId(
          emailDeliveryAnalysisId = None,
          teamId = teamId,
          email = lead.email.get,
          isValid = isValid,
          emailValidationId = 1,
          validationInitiator = EmailValidationInitiator.InitiatedByLeadFinder
        )

      }

      val res = for {

        campaign: Campaign <- CampaignUtils.createNotStartedCampaignWithSteps(
          orgId = orgId,
          teamId = teamId,
          accountId = accountId,
          taId = taId,
          emailSettingId = emailSettingId,
          stepTypes = Seq(),
          ownerFirstName = initialData.account.first_name.get,
        )

        addLeadsIntoProspectListForm: AddLeadsIntoProspectListForm = AddLeadsIntoProspectListForm(
          lead_finder_prospect_ids = leadUUIDs.toList,
          list_name = s"${orgId.id}_${teamId.id}_${accountId.id}_${leadUUIDs.length}_list_name",
          campaign_id = Some(CampaignId(id = campaign.id)),
          include_email = true,
          include_phone = true,
          include_linkedin = false
        )

        _: Seq[Long] <- Future.fromTry {

          leadFinderService.addLeadsIntoProspectList(
            addLeads = addLeadsIntoProspectListForm,
            orgId = orgId,
            accountId = accountId,
            teamId = teamId,
            doerAccount = initialData.account,
            permittedAccountIdsForEditingProspects = initialData.account.teams.flatMap(_.access_members.map(_.user_id))
          ) match {

            case Left(err) =>

              println(err)

              Failure(new Exception("Failed to add leads into prospect list"))

            case Right(res) =>

              Success(res)

          }

        }

        updateCount: Int <- Future.fromTry {
          leadFinderValidationService.updateLeadFinderValidationResults(
            data = validationRes
              .map(ValidationResultWithTeamIdAndAnalysisId.fromProspectEmailValidationResultWithTeamIdAndAnalysisId)
          )
        }

      } yield {

        updateCount

      }

      res.map { _ =>

        val validEmails = validationRes.filter(_.isValid).map(_.email).toSet
        val invalidEmails = validationRes.filter(_.isValid == false).map(_.email)

        val leadFinderBillingLogs = LeadFinderFixture.getLeadFinderBillingLogs(
          teamId = teamId,
          leadFinderProspectIds = leadIds
        ).get

        val validLeadIds =
          leadFinderBillingLogs
            .filter(_.leadFinderContactType.contains(ContactType.Email))
            .filter(l => validEmails.contains(l.leadFinderContact))
            .map(_.leadFinderProspectId)

        val invalidLeadIds =
          leadFinderBillingLogs
            .filter(_.leadFinderContactType.contains(ContactType.Email))
            .filter(l => invalidEmails.contains(l.leadFinderContact))
            .map(_.leadFinderProspectId)

        val validLeadEmailAndPhoneCost =
          leadFinderBillingLogs
            .filter(l => validLeadIds.contains(l.leadFinderProspectId))
            .map(_.creditsUsed)
            .sum

        val invalidLeadEmailAndPhoneCost =
          leadFinderBillingLogs
            .filter(l => invalidLeadIds.contains(l.leadFinderProspectId))
            .map(_.creditsUsed)
            .sum

        val totalCreditsUsed = leadFinderBillingLogs.map(_.creditsUsed).sum

        val batchRequest = LeadFinderFixture.getLeadValidationBatchRequest(
          teamId = teamId,
          leadValidationBatchReqId = leadFinderBillingLogs.head.leadValidationBatchReqIdOpt.get
        ).get.get

        val remainingCreditsOrg1 = organizationService.getRemainingLeadFinderCredits(orgId = orgId).get.toLong


        val leadsAfterValidation = leadFinderDAO.getLeadsByUuid(leadUuids = leadUUIDs).get

        val allLeadsAreUpdatedWithValidationResult = leadsAfterValidation.forall { l =>

          if (validLeadIds.contains(LeadId(id = l.id))) {

            l.email_checked_at.isDefined && l.email_checked && !l.invalid_email

          } else {

            l.email_checked_at.isDefined && l.email_checked && l.invalid_email

          }

        }

        assert(
          allLeadsAreUpdatedWithValidationResult &&
            totalCreditsUsed == validLeadEmailAndPhoneCost + invalidLeadEmailAndPhoneCost &&
            batchRequest.totalFrozenPurchasedCredits == totalCreditsUsed &&
            batchRequest.totalRevertedPurchasedCredits == invalidLeadEmailAndPhoneCost &&
            remainingCreditsOrg1 == ((remainingCreditsBeforeOrg1 - totalCreditsUsed) + invalidLeadEmailAndPhoneCost)
        )

      }.recover { e =>

        println(s"FAILURE - $e")

        assert(false)

      }

    }

  }

}
