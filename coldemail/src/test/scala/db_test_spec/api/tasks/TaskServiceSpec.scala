package db_test_spec.api.tasks

import db_test_spec.api.DbTestingBeforeAllAndAfterAll
import db_test_spec.api.campaigns.CampaignCreationFixtureForIntegrationTest
import org.joda.time.DateTime
import api.accounts.TeamId
import api.accounts.models.{AccountId, OrgId}
import api.campaigns.models.CampaignName
import api.campaigns.services.CampaignId
import api.tasks.models.{NewTask, TaskCount, TaskCreatedVia, TaskData, TaskPriority, TaskStatus, TaskStatusType, TaskType}
import api.tasks.pgDao.{Settings_Table, TaskPgDAO}
import api.tasks.services.{GetAllTaskForUserError, TaskPagination, TaskService}
import api.team_inbox.service.ReplySentimentUuid
import app.db_test.{CustomNotCategorizedAndDoNotContactIds, SchedulerTestInput}
import app.test_fixtures.prospect.ProspectCreateFormDataFixture
import app.test_models.OrganizationWithCurrentDataTest
import db_test_spec.api.campaigns.{CampaignCreationFixtureForIntegrationTest, InitializedCreateAndStartCampaignData}
import db_test_spec.api.campaigns.fixtures.CreateNewCampaignFixture
import db_test_spec.api.prospects.fixtures.ProspectFixtureForIntegrationTest
import db_test_spec.api.tasks.dao.TasksDAO
import db_test_spec.api.{AppSpecFixture, DbTestingBeforeAllAndAfterAll, InitialData, InputForInitializingCampaignCreateData, SRSetupAndDeleteFixtures}
import eventframework.ProspectObject
import io.lemonlabs.uri.Url
import org.joda.time.DateTime
import utils.{SRLogger, StringUtilsV2}
import utils.helpers.LogHelpers
import utils.Helpers.{getNextLinkForConvThread, getPrevLinkForConvThread}

import scala.concurrent.Future
import scala.util.{Failure, Success}

/*
package db_test_spec.api.tasks

import api.accounts.{Account, TeamId}
import api.accounts.models.{AccountId, OrgId}
import api.prospects.ProspectUpdateFormData
import api.prospects.dao.{NeedDuplicateCheckProspect, ProspectIdAndPotentialDuplicateProspectId}
import api.prospects.models.{PotentialDuplicateProspectLogId, ProspectId, ProspectsMetadataUpsert}
import api.tasks.services.DeleteAndUpdateTaskResultForMergeDuplicates
import db_test_spec.api.accounts.fixtures.NewAccountAndWhatsappSettingData
import db_test_spec.api.campaigns.test_utils.{CampaignUtils, CreateAndStartCampaignData}
import db_test_spec.api.prospects.dao.PotentialDuplicateProspectDAO
import db_test_spec.api.scheduler.fixtures.ScheduleTaskFixture
import db_test_spec.api.tasks.dao.TasksDAO
import db_test_spec.api.{DbTestingBeforeAllAndAfterAll, InitialData}
import db_test_spec.utils.SrRandomTestUtils
import eventframework.ProspectObject
import sr_scheduler.models.ChannelData.WhatsAppChannelData
import utils.{Helpers, SRLogger}
import utils.helpers.LogHelpers
import utils.mq.channel_scheduler.channels.ScheduleTasksData

import scala.concurrent.Future
import scala.util.Success

class TaskServiceSpec extends DbTestingBeforeAllAndAfterAll {

  given logger: SRLogger = new SRLogger("[TaskServiceSpec] ::")

  describe("updateTasksDataForMergeDuplicates") {
    it("should return success") {
      val initialData: InitialData = NewAccountAndWhatsappSettingData.createNewAccountAndWhatsappSettingData(
        emailNotCompulsoryOrgFlag = Some(true)
      ).get

      val account: Account = initialData.account
      val accountId: AccountId = AccountId(account.internal_id)
      val whatsappSettingData = initialData.whatsappAccount.get
      val teamId: TeamId = TeamId(account.teams.head.team_id)
      val orgId: OrgId = OrgId(account.org.id)

      var prospectsTaskToBeDeleted: ProspectId = ProspectId(0)
      var masterProspect: ProspectId = ProspectId(0)

      val res = for {
        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartWhatsappCampaign(
          initialData = initialData,
          whatsAppSetting =  whatsappSettingData.settings
        )

        //Test result after scheduling
        _: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForWhatsappChannel(
          whatsAppChannelData = WhatsAppChannelData(
            whatsAppSettingUuid = whatsappSettingData.id.uuid
          ),
          teamId = teamId
        )

        prospects: Seq[ProspectObject] <- Future {
          createAndStartCampaignData.addProspect
        }

        //2. update prospect data to make it duplicate
        updateProspects: Seq[Option[Long]] <- Future.fromTry {
          val res = prospects.map(prospect => {
            val pemail = SrRandomTestUtils.getRandomGmailStringOfLengthN(8)
            prospectDAOService.update(
              permittedAccountIds = Seq(account.internal_id),
              actingAccountId = accountId.id,
              id = prospect.id,
              teamId = teamId.id,
              updateProspect = ProspectUpdateFormData(
                email = pemail,
                first_name = prospect.first_name,
                last_name = prospect.last_name,
                custom_fields = prospect.custom_fields,

                prospect_category_id_custom = None,

                list = prospect.list,
                company = prospect.company,
                city = prospect.city,
                country = prospect.country,
                timezone = prospect.timezone,

                state = prospect.state,
                job_title = prospect.job_title,
                phone = Some("+************"),
                linkedin_url = prospect.linkedin_url,

                prospect_account_id = prospect.internal.prospect_account_id
              )
            )
          })
          Helpers.seqTryToTrySeq(res)
        }

        masterProspect: ProspectObject <- Future{
          val firstProspect: ProspectObject = prospects.head
          masterProspect = ProspectId(firstProspect.id)
          firstProspect
        }

        prospectsTaskToBeUpdatedToDone: ProspectId <- Future {
          ProspectId(prospects.filterNot(p => p.id == masterProspect.id).head.id)
        }

        updatedTask: Int <- Future.fromTry {
          prospectsTaskToBeDeleted = ProspectId(prospects.find(p => p.id != masterProspect.id && p.id != prospectsTaskToBeUpdatedToDone.id).get.id)
          TasksDAO.updateTasksToDone(
            teamId = teamId,
            prospectId = prospectsTaskToBeUpdatedToDone
          )
        }

        //2. update prospect metadata to mark need duplicate check
        updateMetadata: Int <- Future.fromTry {
          accountOrgBillingRelatedInfoDAO.insertProspectsMetadataOrUpdateLastTouchedAt(
            prospectId = ProspectId(masterProspect.id),
            teamId = teamId,
            data = ProspectsMetadataUpsert.ProspectDuplicateCheckData(
              need_duplicate_check = true
            )
          )
        }

        //3. save potential duplicate logs
        updatedLogs: Int <- {
          mqPotentialDuplicateProspects.processMessage(msg = NeedDuplicateCheckProspect(
            prospectId = ProspectId(masterProspect.id), orgId = orgId, teamId = teamId
          ))
        }

        log_id: Option[Long] <- Future.fromTry {
          PotentialDuplicateProspectDAO.getPotentialDuplicateProspectLogId(teamId)
        }

        potentialDuplicateProspects: List[ProspectIdAndPotentialDuplicateProspectId] <- Future.fromTry {
          potentialDuplicateProspectsDAO.getPotentialDuplicateProspectIdForProspects(
            masterProspectId = ProspectId(masterProspect.id),
            logId =  PotentialDuplicateProspectLogId(log_id.get),
            teamId = teamId
          )
        }

        result: DeleteAndUpdateTaskResultForMergeDuplicates <-  Future.fromTry {
          taskService.updateTasksDataForMergeDuplicates(
            duplicateProspects = potentialDuplicateProspects.filterNot(_.isMasterProspect),
            masterProspectId = ProspectId(masterProspect.id),
            teamId = teamId
          )
        }
      } yield {
        result
      }

      res.map(r => {
          assert(r.deletedTasksForProspects.head == prospectsTaskToBeDeleted)
          assert(r.updatedTasksForProspects.head == masterProspect)
        })
        .recover(e => {
          println(s"error: ${LogHelpers.getStackTraceAsString(e)}")
          assert(false)
        })

    }
  }
}

 */

class TaskServiceSpec extends DbTestingBeforeAllAndAfterAll {

  describe("getTasks") {
    val baseDateTime = DateTime.now()

    
    given logger: SRLogger = new SRLogger("[TaskServiceSpec]")
    it("should return tasks in correct order") {
      /* Test flow:
       1. created 8 tasks
       2. Keeping the page size as 3:
       - fetched first page -> should return 3 tasks with only next link (link 1)
       - fetched second page with link 1 - should return 3 tasks with prev and next links (link 2 next and link 2 prev)
       - fetched third page with link 2 next - should return 2 tasks with only prev link (link 3)
       - fetched 2nd page with link 3 - should return 3 tasks with prev and next links
       */

      val query = "/api/v2/tasks/search?sub_task_type=call&time_based_task_type=today_and_due"
      val parsedQueryParams: Map[String, Vector[String]] = StringUtilsV2.getParams(query)
      val referenceTime = DateTime.now().withTime(12, 0, 0, 0)

      val res: Future[Either[GetAllTaskForUserError, TaskPagination]]= for {

        initialData <- Future.successful(SRSetupAndDeleteFixtures.createInitialData().get)

        prospect: Seq[ProspectObject] <- Future.fromTry {
          ProspectFixtureForIntegrationTest.createUpdateOrAssignProspect(
            givenProspect = Some(Seq(
              ProspectCreateFormDataFixture.prospectCreateFormData.copy(
                email = Some("<EMAIL>"),
                first_name = Some("Animesh"),
                last_name = Some("Kumar"),
                owner_id = Some(initialData.account.internal_id),
                company = Some("Test Company"),
                city = Some("Pune"),
                country = Some("India"),
                timezone = Some("Asia/Kolkata"),
                state = Some("Maharashtra"),
                job_title = Some("SDE"),
                phone = Some("**********"),
                linkedin_url = Some("https://www.linkedin.com/in/animesh-kumar-9a1171191/")
              ))),
            campaignId = None,
            account = initialData.account,
            teamId = TeamId(initialData.account.teams.head.team_id),
            accountId = AccountId(initialData.account.internal_id)
          )
        }

        insert_task <- Future.sequence((1 to 8).map { i =>
          taskDAO.createNewTask(
            task_data = NewTask(
              campaign_id = None,
              campaign_name = None,
              step_id = None,
              step_label = None,
              created_via = TaskCreatedVia.Manual,
              is_opening_step = None,
              task_type = TaskType.CallTask,
              is_auto_task = false,
              task_data = TaskData.CallTaskData(
                body = s"test body $i",
                recording_link = None
              ),
              status = TaskStatus.Due(due_at = referenceTime.plusHours(i)),
              assignee_id = Some(initialData.account.internal_id),
              prospect_id = Some(prospect.head.id),
              priority = TaskPriority.Low,
              emailsScheduledUuid = None,
              note = None,
            ),
            taskId = s"test_task_$i",
            account_id = initialData.account.internal_id,
            team_id = initialData.account.teams.head.team_id,
            created_at = referenceTime.plusMinutes(i)
          )
        }).map(_.toList)


        // 1st fetch
        searchTask1 <- Future.fromTry(
          TaskService.validateParams(parsedQueryParams))


        // Convert Either to Future
        validatedTaskReq1 <- Future.fromTry(
          TaskService.paginationValidation(
            timeBasedTaskType = searchTask1.time_based_task_type,
            older_than = None,
            newer_than = None,
            page_size = Option(3),
            duration_from = searchTask1.duration_from,
            duration_to = searchTask1.duration_to,
            timeZone = "UTC"
          ) match {

            case Left(_) => Failure(new Exception("failed to validate pagination"))

            case Right((res1, res2)) =>
              Success((res1, res2))
          })

          task_count <- taskService.getTaskFilterCount(
            team_id = initialData.account.teams.head.team_id,
            orgId = OrgId(initialData.account.teams.head.org_id),
            assignee_id = Some(initialData.account.internal_id),
            campaign_id = None,
            reply_sentiment_uuid_opt = None,
            task_priority = List(),
            timeZone =  "UTC",
            doNotFetchAutomatedDueTasks = true,
            duration_from = searchTask1.duration_from,
            duration_to = searchTask1.duration_to,
            permittedAccountIds = Seq(initialData.account.internal_id)
          )



        result1 <- taskService.getAllTasksForUser(
          isFirst = validatedTaskReq1._1,
          orgId = OrgId(initialData.account.teams.head.org_id),
          team_id = initialData.account.teams.head.team_id,
          validatedTaskReq = validatedTaskReq1._2,
          searchTask = searchTask1.copy(assignee_ids = Some(List(initialData.account.internal_id))),
          timeZone = "UTC",
          doNotFetchAutomatedDueTasks = true,
          permittedAccountIds = Seq(initialData.account.internal_id)
        )

        next_link1 <- result1 match {
            case Left(error) =>
              assert(false)
              Future.successful("some error")

            case Right(tasks) =>

              assert(task_count.todayAndDue.call == 8)
              assert(task_count.todayAndDue.all == 8)
              assert(task_count.completed.call == 0)


              assert(tasks.data.head.task_id.equals("test_task_8"))
              assert(tasks.data.length == 3)

              assert(tasks.links.next.isDefined)
              assert(tasks.links.prev.isEmpty)
              val uri = Url.parse(query)
              val page_data = tasks.links

              val next_link = getNextLinkForConvThread(uri, page_data).get
              Future.successful(next_link)

          }


        // 2nd fetch
        searchTask2 <- Future.fromTry(
            TaskService.validateParams(StringUtilsV2.getParams(next_link1)))



        // Convert Either to Future
        validatedTaskReq2 <- {
          val x = StringUtilsV2.getParams(next_link1).get("older_than").map(p => p.head.toLong)
          val y = StringUtilsV2.getParams(next_link1).get("newer_than").map(p => p.head.toLong)
          Future.fromTry(
            TaskService.paginationValidation(
              timeBasedTaskType = searchTask2.time_based_task_type,
              older_than = x,
              newer_than = y,
              page_size = Option(3),
              duration_from = searchTask2.duration_from,
              duration_to = searchTask2.duration_to,
              timeZone = "UTC"
            ) match {

              case Left(_) => Failure(new Exception("failed to validate pagination"))

              case Right((res1, res2)) =>
                Success((res1, res2))
            })
        }



        result2 <- taskService.getAllTasksForUser(
          isFirst = validatedTaskReq2._1,
          orgId = OrgId(initialData.account.teams.head.org_id),
          team_id = initialData.account.teams.head.team_id,
          validatedTaskReq = validatedTaskReq2._2,
          searchTask = searchTask2.copy(assignee_ids = Some(List(initialData.account.internal_id))),
          timeZone = "UTC",
          doNotFetchAutomatedDueTasks = true,
          permittedAccountIds = Seq(initialData.account.internal_id)
        )

        links2 <- result2 match {
          case Left(error) =>
            assert(false)
            Future.successful(("some error", "some error"))

          case Right(tasks) =>
            assert(tasks.data.head.task_id.equals("test_task_5"))
            assert(tasks.data.length == 3)

            assert(tasks.links.next.isDefined)
            assert(tasks.links.prev.isDefined)
            val uri = Url.parse(next_link1)
            val page_data = tasks.links

            val next_link = getNextLinkForConvThread(uri, page_data).get
            val prev_link = getPrevLinkForConvThread(uri, page_data).get

            Future.successful((next_link, prev_link))

        }

        // 3rd fetch
        searchTask3 <- Future.fromTry(
          TaskService.validateParams(StringUtilsV2.getParams(links2._1)))



        // Convert Either to Future
        validatedTaskReq3 <- Future.fromTry(
          TaskService.paginationValidation(
            timeBasedTaskType = searchTask3.time_based_task_type,
            older_than = StringUtilsV2.getParams(links2._1).get("older_than").map(p => p.head.toLong),
            newer_than = StringUtilsV2.getParams(links2._1).get("newer_than").map(p => p.head.toLong),
            page_size = Option(3),
            duration_from = searchTask3.duration_from,
            duration_to = searchTask3.duration_to,
            timeZone = "UTC"
          ) match {

            case Left(_) => Failure(new Exception("failed to validate pagination"))

            case Right((res1, res2)) =>
              Success((res1, res2))
          })



        result3 <- taskService.getAllTasksForUser(
          isFirst = validatedTaskReq3._1,
          orgId = OrgId(initialData.account.teams.head.org_id),
          team_id = initialData.account.teams.head.team_id,
          validatedTaskReq = validatedTaskReq3._2,
          searchTask = searchTask3.copy(assignee_ids = Some(List(initialData.account.internal_id))),
          timeZone = "UTC",
          doNotFetchAutomatedDueTasks = true,
          permittedAccountIds = Seq(initialData.account.internal_id)
        )

        prev_link3 <- result3 match {
          case Left(error) =>
            assert(false)
            Future.successful("some error")

          case Right(tasks) =>
            assert(tasks.data.head.task_id.equals("test_task_2"))
            assert(tasks.data.length == 2)

            assert(tasks.links.next.isEmpty)
            assert(tasks.links.prev.isDefined)
            val uri = Url.parse(query)
            val page_data = tasks.links

            val prev_link = getPrevLinkForConvThread(uri, page_data).get

            Future.successful(prev_link)

        }


        searchTask4 <- Future.fromTry(
          TaskService.validateParams(StringUtilsV2.getParams(prev_link3)))



        // Convert Either to Future
        validatedTaskReq4 <- Future.fromTry(
          TaskService.paginationValidation(
            timeBasedTaskType = searchTask4.time_based_task_type,
            older_than = StringUtilsV2.getParams(prev_link3).get("older_than").map(p => p.head.toLong),
            newer_than = StringUtilsV2.getParams(prev_link3).get("newer_than").map(p => p.head.toLong),
            page_size = Option(3),
            duration_from = searchTask4.duration_from,
            duration_to = searchTask4.duration_to,
            timeZone = "UTC"
          ) match {

            case Left(_) => Failure(new Exception("failed to validate pagination"))

            case Right((res1, res2)) =>
              Success((res1, res2))
          })



        result4 <- taskService.getAllTasksForUser(
          isFirst = validatedTaskReq4._1,
          orgId = OrgId(initialData.account.teams.head.org_id),
          team_id = initialData.account.teams.head.team_id,
          validatedTaskReq = validatedTaskReq4._2,
          searchTask = searchTask4.copy(assignee_ids = Some(List(initialData.account.internal_id))),
          timeZone = "UTC",
          doNotFetchAutomatedDueTasks = true,
          permittedAccountIds = Seq(initialData.account.internal_id)
        )



      } yield {
        result4
      }

      res.map {
        case Left(error) => assert(false)
        case Right(tasks) =>
          assert(tasks.data.head.task_id.equals("test_task_5"))
          assert(tasks.data.length == 3)

          assert(tasks.links.next.isDefined)
          assert(tasks.links.prev.isDefined)


      }.recover{e =>
        println(s"${LogHelpers.getStackTraceAsString(e)}")
        assert(false)
      }


    }


    it("should return tasks in correct order with date range") {
      /* Test flow:
       1. created 8 tasks
       2. marked all the tasks as completed with varying done_at time
       3. Keeping the page size as 3 and date range baseDateTime.minusDays(6).getMillis - baseDateTime.minusDays(2).getMillis
       [Only 5 tasks wil fall in this range]:
       - fetched first page -> should return 3 tasks with only next link (link 1)
       - fetched second page with link 1 - should return 2 tasks with only prev link (link2)

       */

      val date_to = baseDateTime.minusDays(2).getMillis
      val date_from = baseDateTime.minusDays(6).getMillis
      val query = "/api/v2/tasks/search?sub_task_type=call&time_based_task_type=completed&duration_from=" + date_from + "&duration_to=" + date_to
      val parsedQueryParams: Map[String, Vector[String]] = StringUtilsV2.getParams(query)

      val res: Future[TaskCount] = for {

        initialData <- Future.successful(SRSetupAndDeleteFixtures.createInitialData().get)

        prospect: Seq[ProspectObject] <- Future.fromTry {
          ProspectFixtureForIntegrationTest.createUpdateOrAssignProspect(
            givenProspect = Some(Seq(
              ProspectCreateFormDataFixture.prospectCreateFormData.copy(
                email = Some("<EMAIL>"),
                first_name = Some("Animesh"),
                last_name = Some("Kumar"),
                owner_id = Some(initialData.account.internal_id),
                company = Some("Test Company"),
                city = Some("Pune"),
                country = Some("India"),
                timezone = Some("Asia/Kolkata"),
                state = Some("Maharashtra"),
                job_title = Some("SDE"),
                phone = Some("**********"),
                linkedin_url = Some("https://www.linkedin.com/in/animesh-kumar-9a1171191/")
              ))),
            campaignId = None,
            account = initialData.account,
            teamId = TeamId(initialData.account.teams.head.team_id),
            accountId = AccountId(initialData.account.internal_id)
          )
        }

        insert_task <- Future.sequence((1 to 8).map { i =>
          taskDAO.createNewTask(
            task_data = NewTask(
              campaign_id = None,
              campaign_name = None,
              step_id = None,
              step_label = None,
              created_via = TaskCreatedVia.Manual,
              is_opening_step = None,
              task_type = TaskType.CallTask,
              is_auto_task = false,
              task_data = TaskData.CallTaskData(
                body = s"test body $i",
                recording_link = None
              ),
              status = TaskStatus.Due(
                due_at = baseDateTime.minusDays(9 - i)
              ),
              assignee_id = Some(initialData.account.internal_id),
              prospect_id = Some(prospect.head.id),
              priority = TaskPriority.Low,
              note = None,
              emailsScheduledUuid = None
            ),
            taskId = s"test_task_$i",
            account_id = initialData.account.internal_id,
            team_id = initialData.account.teams.head.team_id,
            created_at = baseDateTime.minusDays(9 - i)
          )
        }).map(_.toList)

        done_tasks <- Future.sequence((1 to 8).map { i =>
          Future.fromTry(TasksDAO.updateTaskToDoneWithTime(

            taskId = s"test_task_$i",

            teamId = TeamId(initialData.account.teams.head.team_id),
            done_at = baseDateTime.minusDays(9 - i),
            done_by = initialData.account.internal_id
          ))
        }).map(_.toList)


        // 1st fetch
        searchTask1 <- {
          val x = TaskService.validateParams(parsedQueryParams)
          Future.fromTry(
            x)
        }


        // Convert Either to Future
        validatedTaskReq1 <- Future.fromTry(
          TaskService.paginationValidation(
            timeBasedTaskType = searchTask1.time_based_task_type,
            older_than = None,
            newer_than = None,
            page_size = Option(3),
            duration_from = searchTask1.duration_from,
            duration_to = searchTask1.duration_to,
            timeZone = "UTC"
          ) match {

            case Left(_) => Failure(new Exception("failed to validate pagination"))

            case Right((res1, res2)) =>
              Success((res1, res2))
          })



        result1 <- taskService.getAllTasksForUser(
          isFirst = validatedTaskReq1._1,
          orgId = OrgId(initialData.account.teams.head.org_id),
          team_id = initialData.account.teams.head.team_id,
          validatedTaskReq = validatedTaskReq1._2,
          searchTask = searchTask1.copy(assignee_ids = Some(List(initialData.account.internal_id))),
          timeZone = "UTC",
          doNotFetchAutomatedDueTasks = true,
          permittedAccountIds = Seq(initialData.account.internal_id)
        )

        next_link1 <- result1 match {
          case Left(error) =>
            assert(false)
            Future.successful("some error")

          case Right(tasks) =>



            assert(tasks.data.head.task_id.equals("test_task_7"))
            assert(tasks.data.length == 3)

            assert(tasks.links.next.isDefined)
            assert(tasks.links.prev.isEmpty)
            val uri = Url.parse(query)
            val page_data = tasks.links

            val next_link = getNextLinkForConvThread(uri, page_data).get
            Future.successful(next_link)

        }


        searchTask2 <- Future.fromTry(
          TaskService.validateParams(StringUtilsV2.getParams(next_link1)))



        // Convert Either to Future
        validatedTaskReq2 <- {
          val x = StringUtilsV2.getParams(next_link1).get("older_than").map(p => p.head.toLong)
          val y = StringUtilsV2.getParams(next_link1).get("newer_than").map(p => p.head.toLong)
          Future.fromTry(
            TaskService.paginationValidation(
              timeBasedTaskType = searchTask2.time_based_task_type,
              older_than = x,
              newer_than = y,
              page_size = Option(3),
              duration_from = searchTask2.duration_from,
              duration_to = searchTask2.duration_to,
              timeZone = "UTC"
            ) match {

              case Left(_) => Failure(new Exception("failed to validate pagination"))

              case Right((res1, res2)) =>
                Success((res1, res2))
            })
        }



        result2 <- taskService.getAllTasksForUser(
          isFirst = validatedTaskReq2._1,
          orgId = OrgId(initialData.account.teams.head.org_id),
          team_id = initialData.account.teams.head.team_id,
          validatedTaskReq = validatedTaskReq2._2,
          searchTask = searchTask2.copy(assignee_ids = Some(List(initialData.account.internal_id))),
          timeZone = "UTC",
          doNotFetchAutomatedDueTasks = true,
          permittedAccountIds = Seq(initialData.account.internal_id)
        )

        next_link2 <- result2 match {
          case Left(error) =>
            assert(false)
            Future.successful("some error")

          case Right(tasks) =>



            assert(tasks.data.head.task_id.equals("test_task_4"))
            assert(tasks.data.length == 2)

            assert(tasks.links.next.isEmpty)
            assert(tasks.links.prev.isDefined)
            val uri = Url.parse(query)
            val page_data = tasks.links

            val prev_link = getPrevLinkForConvThread(uri, page_data).get
            Future.successful(prev_link)

        }

        task_count <- taskService.getTaskFilterCount(
          team_id = initialData.account.teams.head.team_id,
          orgId = OrgId(initialData.account.teams.head.org_id),
          assignee_id = Some(initialData.account.internal_id),
          campaign_id = None,
          reply_sentiment_uuid_opt = None,
          task_priority = List(),
          timeZone =  "UTC",
          doNotFetchAutomatedDueTasks = true,
          duration_from = searchTask1.duration_from,
          duration_to = searchTask1.duration_to,
          permittedAccountIds = Seq(initialData.account.internal_id)
        )

      } yield {
        task_count
      }

      res.map { counts => {

        assert(counts.due.call == 0)
        assert(counts.completed.call == 5)
        assert(counts.completed.all == 5)

      }


      }.recover { e =>
        println(s"${LogHelpers.getStackTraceAsString(e)}")
        assert(false)
      }


    }

    it("should return tasks for magic content step") {
      /* Test flow:
       1. created 8 tasks
       2. marked all the tasks as completed with varying done_at time
       3. Keeping the page size as 3 and date range baseDateTime.minusDays(6).getMillis - baseDateTime.minusDays(2).getMillis
       [Only 5 tasks wil fall in this range]:
       - fetched first page -> should return 3 tasks with only next link (link 1)
       - fetched second page with link 1 - should return 2 tasks with only prev link (link2)

       */

      val date_to = baseDateTime.minusDays(2).getMillis
      val date_from = baseDateTime.minusDays(6).getMillis
      val query = "/api/v2/tasks/search?sub_task_type=call&time_based_task_type=completed&duration_from=" + date_from + "&duration_to=" + date_to
      val parsedQueryParams: Map[String, Vector[String]] = StringUtilsV2.getParams(query)

      val res: Future[TaskCount] = for {

        initialData <- Future.successful(SRSetupAndDeleteFixtures.createInitialData().get)

        prospect: Seq[ProspectObject] <- Future.fromTry {
          ProspectFixtureForIntegrationTest.createUpdateOrAssignProspect(
            givenProspect = Some(Seq(
              ProspectCreateFormDataFixture.prospectCreateFormData.copy(
                email = Some("<EMAIL>"),
                first_name = Some("Animesh"),
                last_name = Some("Kumar"),
                owner_id = Some(initialData.account.internal_id),
                company = Some("Test Company"),
                city = Some("Pune"),
                country = Some("India"),
                timezone = Some("Asia/Kolkata"),
                state = Some("Maharashtra"),
                job_title = Some("SDE"),
                phone = Some("**********"),
                linkedin_url = Some("https://www.linkedin.com/in/animesh-kumar-9a1171191/")
              ))),
            campaignId = None,
            account = initialData.account,
            teamId = TeamId(initialData.account.teams.head.team_id),
            accountId = AccountId(initialData.account.internal_id)
          )
        }

        insert_task <- Future.sequence((10 to 18).map { i =>
          taskDAO.createNewTask(
            task_data = NewTask(
              campaign_id = None,
              campaign_name = None,
              step_id = None,
              step_label = None,
              created_via = TaskCreatedVia.Manual,
              is_opening_step = None,
              task_type = TaskType.CallTask,
              is_auto_task = false,
              task_data = TaskData.AutoEmailMagicContentData(
              generated_subject = "Test Subject",
              generated_body = "Test Body",
              email_scheduled_text_body = "Test Scheduled Text Body",
              email_scheduled_base_body = "Test Scheduled Base Body"
              ),
              status = TaskStatus.Due(
                due_at = baseDateTime.minusDays(19 - i)
              ),
              assignee_id = Some(initialData.account.internal_id),
              prospect_id = Some(prospect.head.id),
              priority = TaskPriority.Low,
              note = None,
              emailsScheduledUuid = None
            ),
            taskId = s"test_task_$i",
            account_id = initialData.account.internal_id,
            team_id = initialData.account.teams.head.team_id,
            created_at = baseDateTime.minusDays(19 - i)
          )
        }).map(_.toList)

        done_tasks <- Future.sequence((10 to 18).map { i =>
          Future.fromTry(TasksDAO.updateTaskToDoneWithTime(

            taskId = s"test_task_$i",

            teamId = TeamId(initialData.account.teams.head.team_id),
            done_at = baseDateTime.minusDays(19 - i),
            done_by = initialData.account.internal_id
          ))
        }).map(_.toList)


        // 1st fetch
        searchTask1 <- {
          val x = TaskService.validateParams(parsedQueryParams)
          Future.fromTry(
            x)
        }


        // Convert Either to Future
        validatedTaskReq1 <- Future.fromTry(
          TaskService.paginationValidation(
            timeBasedTaskType = searchTask1.time_based_task_type,
            older_than = None,
            newer_than = None,
            page_size = Option(3),
            duration_from = searchTask1.duration_from,
            duration_to = searchTask1.duration_to,
            timeZone = "UTC"
          ) match {

            case Left(_) => Failure(new Exception("failed to validate pagination"))

            case Right((res1, res2)) =>
              Success((res1, res2))
          })



        result1 <- taskService.getAllTasksForUser(
          isFirst = validatedTaskReq1._1,
          orgId = OrgId(initialData.account.teams.head.org_id),
          team_id = initialData.account.teams.head.team_id,
          validatedTaskReq = validatedTaskReq1._2,
          searchTask = searchTask1.copy(assignee_ids = Some(List(initialData.account.internal_id))),
          timeZone = "UTC",
          doNotFetchAutomatedDueTasks = true,
          permittedAccountIds = Seq(initialData.account.internal_id)
        )

        next_link1 <- result1 match {
          case Left(error) =>
            assert(false)
            Future.successful("some error")

          case Right(tasks) =>



            assert(tasks.data.head.task_id.equals("test_task_17"))
            assert(tasks.data.length == 3)

            assert(tasks.links.next.isDefined)
            assert(tasks.links.prev.isEmpty)
            val uri = Url.parse(query)
            val page_data = tasks.links

            val next_link = getNextLinkForConvThread(uri, page_data).get
            Future.successful(next_link)

        }


        searchTask2 <- Future.fromTry(
          TaskService.validateParams(StringUtilsV2.getParams(next_link1)))



        // Convert Either to Future
        validatedTaskReq2 <- {
          val x = StringUtilsV2.getParams(next_link1).get("older_than").map(p => p.head.toLong)
          val y = StringUtilsV2.getParams(next_link1).get("newer_than").map(p => p.head.toLong)
          Future.fromTry(
            TaskService.paginationValidation(
              timeBasedTaskType = searchTask2.time_based_task_type,
              older_than = x,
              newer_than = y,
              page_size = Option(3),
              duration_from = searchTask2.duration_from,
              duration_to = searchTask2.duration_to,
              timeZone = "UTC"
            ) match {

              case Left(_) => Failure(new Exception("failed to validate pagination"))

              case Right((res1, res2)) =>
                Success((res1, res2))
            })
        }



        result2 <- taskService.getAllTasksForUser(
          isFirst = validatedTaskReq2._1,
          orgId = OrgId(initialData.account.teams.head.org_id),
          team_id = initialData.account.teams.head.team_id,
          validatedTaskReq = validatedTaskReq2._2,
          searchTask = searchTask2.copy(assignee_ids = Some(List(initialData.account.internal_id))),
          timeZone = "UTC",
          doNotFetchAutomatedDueTasks = true,
          permittedAccountIds = Seq(initialData.account.internal_id)
        )

        next_link2 <- result2 match {
          case Left(error) =>
            assert(false)
            Future.successful("some error")

          case Right(tasks) =>



            assert(tasks.data.head.task_id.equals("test_task_14"))
            assert(tasks.data.length == 2)

            assert(tasks.links.next.isEmpty)
            assert(tasks.links.prev.isDefined)
            val uri = Url.parse(query)
            val page_data = tasks.links

            val prev_link = getPrevLinkForConvThread(uri, page_data).get
            Future.successful(prev_link)

        }

        task_count <- taskService.getTaskFilterCount(
          team_id = initialData.account.teams.head.team_id,
          orgId = OrgId(initialData.account.teams.head.org_id),
          assignee_id = Some(initialData.account.internal_id),
          campaign_id = None,
          reply_sentiment_uuid_opt = None,
          task_priority = List(),
          timeZone = "UTC",
          doNotFetchAutomatedDueTasks = true,
          duration_from = searchTask1.duration_from,
          duration_to = searchTask1.duration_to,
          permittedAccountIds = Seq(initialData.account.internal_id)
        )

      } yield {
        task_count
      }

      res.map { counts => {

        assert(counts.due.call == 0)
        assert(counts.completed.call == 5)
        assert(counts.completed.all == 5)

      }


      }.recover { e =>
        println(s"${LogHelpers.getStackTraceAsString(e)}")
        assert(false)
      }


    }


  }


}
