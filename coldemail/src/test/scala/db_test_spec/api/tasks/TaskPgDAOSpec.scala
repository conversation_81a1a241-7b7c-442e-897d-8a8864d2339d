package db_test_spec.api.tasks

import api.accounts.TeamId
import api.campaigns.models.CampaignName
import api.campaigns.services.CampaignId
import api.tasks.models.{NewTask, TaskCreatedVia, TaskData, TaskPriority, TaskStatus, TaskStatusType, TaskType}
import api.tasks.pgDao.{Settings_Table, TaskPgDAO}
import app.db_test.{CustomNotCategorizedAndDoNotContactIds, SchedulerTestInput}
import db_test_spec.api.accounts.fixtures.NewAccountAndEmailSettingData
import db_test_spec.api.campaigns.{CampaignCreationFixtureForIntegrationTest, InitializedCreateAndStartCampaignData}
import db_test_spec.api.campaigns.fixtures.CreateNewCampaignFixture
import db_test_spec.api.{AppSpecFixture, DbTestingBeforeAllAndAfterAll, InitialData, InputForInitializingCampaignCreateData, SRSetupAndDeleteFixtures}
import org.joda.time.DateTime
import utils.helpers.LogHelpers

import scala.concurrent.Future
import scala.util.{Failure, Random, Success}

class TaskPgDAOSpec  extends DbTestingBeforeAllAndAfterAll {

  lazy val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
  

  describe("deleteTaskForRevert") {
    it("should give success with no status") {
      val result = for {
        campaign <- CampaignCreationFixtureForIntegrationTest.createDefaultCampaign(initialData = initialData)
        insert_task <- taskDAO.createNewTask(
          task_data = NewTask(
            campaign_id = Some(campaign.campaign.id),
            campaign_name = Some(campaign.campaign.name),
            step_id = None,
            step_label = None,
            created_via = TaskCreatedVia.Manual,
            is_opening_step =  None,
            task_type = TaskType.SendEmail,
            is_auto_task = false,
            task_data = TaskData.SendEmailData(
              subject = "this is subject",
              body  = "this is body",
              email_message_id = None,
            ),
            status = TaskStatus.Due(due_at = DateTime.now().plusDays(11)),
            assignee_id = None,
            prospect_id = campaign.prospects.created_ids.headOption,
            priority = TaskPriority.Low,
            note = None,
          ),
          taskId = "task_deleteTaskForRevert_1",
          account_id = campaign.campaign.account_id,
          team_id =  campaign.campaign.team_id,
          created_at = DateTime.now()
        )

        result <- Future.fromTry {
          taskDAO.deleteTaskForRevert(
            taskIds = List("task_deleteTaskForRevert_1"),
            teamId = TeamId(campaign.campaign.team_id),
            status = None,
            permittedAccountIds = Seq()
          )
        }

      } yield {
        result
      }

      result.map{r =>
        assert(true)
      }.recover{e =>
        println(s"${LogHelpers.getStackTraceAsString(e)}")
        assert(false)
      }


    }

    it("should give success with status") {
      val result = for {
        campaign <- CampaignCreationFixtureForIntegrationTest.createDefaultCampaign(initialData = initialData)
        insert_task <- taskDAO.createNewTask(
          task_data = NewTask(
            campaign_id = Some(campaign.campaign.id),
            campaign_name = Some(campaign.campaign.name),
            step_id = None,
            step_label = None,
            created_via = TaskCreatedVia.Manual,
            is_opening_step =  None,
            task_type = TaskType.SendEmail,
            is_auto_task = false,
            task_data = TaskData.SendEmailData(
              subject = "this is subject",
              body  = "this is body",
              email_message_id = None,

            ),
            status = TaskStatus.Due(due_at = DateTime.now().plusDays(11)),
            assignee_id = None,
            prospect_id = campaign.prospects.created_ids.headOption,
            priority = TaskPriority.Low,
            note = None,
          ),
          taskId = "task_deleteTaskForRevert_2",
          account_id = campaign.campaign.account_id,
          team_id =  campaign.campaign.team_id,
          created_at = DateTime.now()
        )

        result <- Future.fromTry {
          taskDAO.deleteTaskForRevert(
            taskIds = List("task_deleteTaskForRevert_2"),
            teamId = TeamId(campaign.campaign.team_id),
            status = Some(TaskStatusType.Due),
            permittedAccountIds = Seq()
          )
        }

      } yield {
        result
      }

      result.map{r =>
        assert(true)
      }.recover{e =>
        assert(false)
      }


    }

    it("should give success with permittedAccountIds") {
      val result = for {
        campaign <- CampaignCreationFixtureForIntegrationTest.createDefaultCampaign(initialData = initialData)
        insert_task <- taskDAO.createNewTask(
          task_data = NewTask(
            campaign_id = Some(campaign.campaign.id),
            campaign_name = Some(campaign.campaign.name),
            step_id = None,
            step_label = None,
            created_via = TaskCreatedVia.Manual,
            is_opening_step =  None,
            task_type = TaskType.SendEmail,
            is_auto_task = false,
            task_data = TaskData.SendEmailData(
              subject = "this is subject",
              body  = "this is body",
              email_message_id = None,
            ),
            status = TaskStatus.Due(due_at = DateTime.now().plusDays(11)),
            assignee_id = Some(campaign.campaign.account_id),
            prospect_id = campaign.prospects.created_ids.headOption,
            priority = TaskPriority.Low,
            note = None,
          ),
          taskId = "task_deleteTaskForRevert_3",
          account_id = campaign.campaign.account_id,
          team_id =  campaign.campaign.team_id,
          created_at = DateTime.now()
        )

        result <- Future.fromTry {
          taskDAO.deleteTaskForRevert(
            taskIds = List("task_deleteTaskForRevert_3"),
            teamId = TeamId(campaign.campaign.team_id),
            status = Some(TaskStatusType.Due),
            permittedAccountIds = Seq(campaign.campaign.account_id)
          )
        }

      } yield {
        result
      }

      result.map{r =>
        assert(true)
      }.recover{e =>
        assert(false)
      }


    }
  }

  describe("testing getFetchTaskScheduledQuery"){
    it("should success"){

      taskDAO.fetchTasksToBeScheduled(
        table = Settings_Table.CallSetting
      ) match {

        case Failure(error) =>
          println(s"error :: ${LogHelpers.getStackTraceAsString(error)}")

          assert(false)

        case Success(value) =>

          assert(true)

      }



    }

  }

}
