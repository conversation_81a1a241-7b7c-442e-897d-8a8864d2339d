package db_test_spec.api.tasks.fixtures

import api.accounts.TeamId
import api.accounts.models.{AccountId, OrgId}
import api.campaigns.models.CampaignEmailSettingsId
import api.linkedin.models.LinkedinSettingUuid
import api.prospects.models.ProspectId
import api.tasks.models.TaskType
import db_test_spec.api.InitialData
import db_test_spec.api.accounts.fixtures.NewAccountAndEmailSettingData
import db_test_spec.api.campaigns.fixtures.CreateNewCampaignFixture
import db_test_spec.api.linkedin.fixtures.{DefaultLinkedinSettingParametersFixtures, LinkedInSettingFixtureForIntegrationTest, LinkedinAccount}
import scalikejdbc.{DB, scalikejdbcSQLInterpolationImplicitDef}
import sr_scheduler.models.ChannelType
import utils.SRLogger

import scala.concurrent.Future

import scala.concurrent.{Await, ExecutionContext, Future}
import scala.concurrent.duration.*

// Import the execution context
import scala.concurrent.ExecutionContext.Implicits.global

/**
 * Fixture class for LinkedIn tasks integration tests
 */
object LinkedinTasksFixtureForIntegrationTest {

  /**
   * Test fixture to hold common test data
   */
  case class TestFixture(
    initialData: InitialData,
    linkedinAccount: LinkedinAccount,
    campaignId: Long
  ) {
    val teamId: TeamId = TeamId(initialData.account.teams.head.team_id)
    val orgId: OrgId = OrgId(initialData.account.org.id)
    val linkedinSettingUuid: LinkedinSettingUuid = LinkedinSettingUuid(linkedinAccount.settings.uuid)
    val prospectIds: Seq[Long] = initialData.prospectsResult.map(_.id)
  }

  /**
   * Helper method to create a LinkedIn account for testing
   */
  def createLinkedinAccount(initialData: InitialData)(implicit logger: SRLogger): Future[LinkedinAccount] = {
    Future {
      val linkedinAccountSettings = DefaultLinkedinSettingParametersFixtures.defaultLinkedInAccountSettings(
        accountId = AccountId(initialData.account.internal_id)
      )

      LinkedInSettingFixtureForIntegrationTest.createLinkedInSetting(
        accountId = AccountId(initialData.account.internal_id),
        teamId = TeamId(initialData.account.teams.head.team_id),
        linkedInAccountSettings = Some(linkedinAccountSettings)
      ).get
    }
  }

  /**
   * Helper method to create a campaign and link it to a LinkedIn account
   */
  def createCampaignWithLinkedinSettings(
    initialData: InitialData,
    linkedinAccount: LinkedinAccount,
    campaignId: Long,
    campaignName: String
  )(implicit logger: SRLogger): Future[Unit] = {
    Future {
      // First create the campaign
      DB.autoCommit { implicit session =>
        sql"""
             INSERT INTO campaigns (
               id,
               name,
               team_id,
               account_id,
               created_at,
               status,
               timezone,
               daily_from_time,
               daily_till_time,
               days_preference,
               email_priority,
               opt_out_msg,
               opt_out_is_text,
               shared_with_team,
               ta_id,
               max_emails_per_day,
               mark_completed_after_days,
               open_tracking_enabled,
               click_tracking_enabled,
               append_followups,
               will_delete,
               enable_email_validation,
               ab_testing_enabled,
               add_prospect_to_dnc_on_opt_out,
               is_archived,
               campaign_type
             ) VALUES (
               $campaignId,
               $campaignName,
               ${initialData.account.teams.head.team_id},
               ${initialData.account.internal_id},
               now(),
               'active',
               'UTC',
               32400,
               64800,
               '{true,true,true,true,true,false,false}'::boolean[],
               'medium',
               'Please opt out',
               true,
               false,
               ${initialData.account.teams.head.team_id},
               100,
               3,
               true,
               false,
               false,
               false,
               true,
               false,
               true,
               false,
               'sequence'
             )
             """
          .update
          .apply()
      }

      // Then create the campaign channel settings
      DB.autoCommit { implicit session =>
        sql"""
             INSERT INTO campaign_channel_settings (
               campaign_id,
               team_id,
               channel_type,
               channel_settings_uuid
             ) VALUES (
               $campaignId,
               ${initialData.account.teams.head.team_id},
               ${ChannelType.LinkedinChannel.toString},
               ${linkedinAccount.settings.uuid}
             )
             """
          .update
          .apply()
      }
    }
  }

  /**
   * Helper method to create a test fixture with all necessary data
   *
   * @param testIdentifier A unique identifier for this test to avoid conflicts with other tests
   */
  def createTestFixture(testIdentifier: String)(implicit logger: SRLogger, ec: ExecutionContext): Future[TestFixture] = {
    // Generate a unique campaign ID based on the test identifier
    val campaignId = Math.abs(s"linkedin_tasks_$testIdentifier".hashCode % 10000) + 10000
    val campaignName = s"Test Campaign $testIdentifier"

    for {
      initialData <- Future.successful(NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get)
      linkedinAccount <- createLinkedinAccount(initialData)
      _ <- createCampaignWithLinkedinSettings(initialData, linkedinAccount, campaignId, campaignName)
    } yield TestFixture(initialData, linkedinAccount, campaignId)
  }

  /**
   * Helper method to create tasks for testing
   */
  def createTasks(
    count: Int,
    teamId: Long,
    taskType: api.tasks.models.TaskType,
    campaignId: Long,
    prospectIds: Seq[Long]
  ): List[String] = {

    val taskIds = (1 to count).map(_ => s"task_${java.util.UUID.randomUUID().toString}").toList

    taskIds.zipWithIndex.foreach { case (taskId, index) =>
      val prospectId = prospectIds(index % prospectIds.length)

      DB.autoCommit { implicit session =>
        sql"""
             INSERT INTO tasks (
               task_id,
               is_auto_task,
               task_type,
               added_by,
               task_subject,
               task_body,
               status,
               team_id,
               prospect_id,
               priority,
               created_at,
               due_at,
               campaign_id,
               channel_type,
               execution_attempts
             ) VALUES (
               $taskId,
               true,
               ${taskType.toKey},
               1,
               'Test task',
               'Test task body',
               ${api.tasks.models.TaskStatusType.Due.toKey},
               $teamId,
               $prospectId,
               'medium',
               now(),
               now(),
               $campaignId,
               ${ChannelType.LinkedinChannel.toString},
               0
             )
             """
          .update
          .apply()
      }
    }

    taskIds
  }

  /**
   * Helper method to update tasks to be stuck in the queue
   */
  def markTasksAsStuck(taskIds: List[String])(implicit logger: SRLogger): Future[Unit] = {
    Future {
      DB.autoCommit { implicit session =>
        sql"""
             UPDATE tasks
             SET
               status = ${api.tasks.models.TaskStatusType.PushedToLinkedinExecutionQueue.toKey},
               pushed_to_phantombuster_queue_at = now() - interval '60 minutes'
             WHERE task_id IN (${taskIds})
             """
          .update
          .apply()
      }
    }
  }

  /**
   * Helper method to clean up test data
   */
  def cleanupTestData(fixture: TestFixture)(implicit logger: SRLogger): Future[Unit] = {
    Future {
      // Delete tasks for this campaign
      DB.autoCommit { implicit session =>
        sql"""
             DELETE FROM tasks
             WHERE campaign_id = ${fixture.campaignId}
             """
          .update
          .apply()
      }

      // Delete campaign channel settings
      DB.autoCommit { implicit session =>
        sql"""
             DELETE FROM campaign_channel_settings
             WHERE campaign_id = ${fixture.campaignId}
             """
          .update
          .apply()
      }

      // Delete campaign
      DB.autoCommit { implicit session =>
        sql"""
             DELETE FROM campaigns
             WHERE id = ${fixture.campaignId}
             """
          .update
          .apply()
      }

      // Note: We don't delete the LinkedIn account as it might be used by other tests
      // and will be cleaned up by the test framework's cleanup process
    }
  }

  /**
   * Helper method to get task statuses from the database
   */
  def getTaskStatusesFromDatabase(taskIds: Set[String]): List[(String, String)] = {
    DB.readOnly { implicit session =>
      sql"""
           SELECT task_id, status
           FROM tasks
           WHERE task_id IN (${taskIds.toSeq})
           """
        .map(rs => (rs.string("task_id"), rs.string("status")))
        .list
        .apply()
    }
  }

  /**
   * Helper method to create a campaign for LinkedIn tasks testing
   */
  def createCampaignForLinkedinTasks(
    fixture: TestFixture,
    testIdentifier: String
  )(implicit logger: SRLogger, ec: ExecutionContext): Future[(Long, List[String])] = {
    // Create a new campaign using the fixture
    for {
      campaign <- CreateNewCampaignFixture.createNewCampaign(
        orgId = fixture.orgId,
        accountId = AccountId(fixture.initialData.account.internal_id),
        teamId = fixture.teamId,
        taId = fixture.initialData.account.teams.head.access_members.head.ta_id,
        campaignEmailSettingsId = CampaignEmailSettingsId(fixture.initialData.emailSetting.get.id.get.emailSettingId),
        senderEmailSettingId = fixture.initialData.emailSetting.get.id.get,
        receiverEmailSettingId = fixture.initialData.emailSetting.get.id.get,
        ownerFirstName = s"TestOwner_$testIdentifier"
      )
      
      // Link the campaign to the LinkedIn setting
      _ <- Future {
        DB.autoCommit { implicit session =>
          sql"""
               INSERT INTO campaign_channel_settings (
                 campaign_id,
                 team_id,
                 channel_type,
                 channel_settings_uuid
               ) VALUES (
                 ${campaign.id},
                 ${fixture.teamId.id},
                 ${ChannelType.LinkedinChannel.toString},
                 ${fixture.linkedinSettingUuid.uuid}
               )
               """
            .update
            .apply()
        }
      }
      
      // Ensure the campaign is in running state
      _ <- Future {
        DB.autoCommit { implicit session =>
          sql"""
               UPDATE campaigns
               SET status = 'running'
               WHERE id = ${campaign.id}
               """
            .update
            .apply()
        }
      }
      
      // Create connection request tasks for the campaign
      tasks = createTasks(
        count = 3,
        teamId = fixture.teamId.id,
        taskType = TaskType.AutoLinkedinConnectionRequest,
        campaignId = campaign.id,
        prospectIds = fixture.prospectIds
      )
    } yield (campaign.id, tasks)
  }

  /**
   * Helper method to create multiple campaigns for LinkedIn tasks testing
   */
  def createMultipleCampaignsForLinkedinTasks(
    fixture: TestFixture,
    count: Int
  )(implicit logger: SRLogger, ec: ExecutionContext): Future[(List[Long], List[String])] = {
    // Create additional campaigns
    val campaignFutures = (1 to count).map { i =>
      for {
        campaign <- CreateNewCampaignFixture.createNewCampaign(
          orgId = fixture.orgId,
          accountId = AccountId(fixture.initialData.account.internal_id),
          teamId = fixture.teamId,
          taId = fixture.initialData.account.teams.head.access_members.head.ta_id,
          campaignEmailSettingsId = CampaignEmailSettingsId(fixture.initialData.emailSetting.get.id.get.emailSettingId),
          senderEmailSettingId = fixture.initialData.emailSetting.get.id.get,
          receiverEmailSettingId = fixture.initialData.emailSetting.get.id.get,
          ownerFirstName = s"TestOwner$i"
        )
        
        // Link the campaign to the LinkedIn setting
        _ <- Future {
          DB.autoCommit { implicit session =>
            sql"""
                 INSERT INTO campaign_channel_settings (
                   campaign_id,
                   team_id,
                   channel_type,
                   channel_settings_uuid
                 ) VALUES (
                   ${campaign.id},
                   ${fixture.teamId.id},
                   ${ChannelType.LinkedinChannel.toString},
                   ${fixture.linkedinSettingUuid.uuid}
                 )
                 """
              .update
              .apply()
          }
        }
        
        // Ensure the campaign is in running state
        _ <- Future {
          DB.autoCommit { implicit session =>
            sql"""
                 UPDATE campaigns
                 SET status = 'running'
                 WHERE id = ${campaign.id}
                 """
              .update
              .apply()
          }
        }
        
        // Create tasks for the campaign
        tasks = createTasks(
          count = 1,
          teamId = fixture.teamId.id,
          taskType = TaskType.AutoLinkedinConnectionRequest,
          campaignId = campaign.id,
          prospectIds = fixture.prospectIds
        )
      } yield (campaign.id, tasks)
    }
    
    // Combine all futures
    Future.sequence(campaignFutures).map { results =>
      val campaignIds = results.map(_._1).toList
      val allTasks = results.flatMap(_._2).toList
      (campaignIds, allTasks)
    }
  }

  /**
   * Helper method to clean up campaign data
   */
  def cleanupCampaignData(campaignId: Long)(implicit logger: SRLogger): Future[Unit] = {
    Future {
      DB.autoCommit { implicit session =>
        sql"""
             DELETE FROM campaign_channel_settings
             WHERE campaign_id = ${campaignId}
             """
          .update
          .apply()

        sql"""
             DELETE FROM campaigns
             WHERE id = ${campaignId}
             """
          .update
          .apply()
      }
    }
  }

  /**
   * Helper method to update campaign status to running
   */
  def setCampaignStatusToRunning(campaignId: Long): Unit = {
    DB.autoCommit { implicit session =>
      sql"""
           UPDATE campaigns
           SET status = 'running'
           WHERE id = ${campaignId}
           """
        .update
        .apply()
    }
  }

  /**
   * Helper method to set LinkedIn account daily limits
   */
  def setLinkedinAccountDailyLimits(
    linkedinSettingUuid: LinkedinSettingUuid,
    viewProfileLimit: Int = 1,
    inmailLimit: Int = 1,
    messageLimit: Int = 1,
    connectionRequestLimit: Int = 1
  ): Unit = {
    DB.autoCommit { implicit session =>
      sql"""
           UPDATE linkedin_settings
           SET
             view_profile_limit_per_day = ${viewProfileLimit},
             inmail_limit_per_day = ${inmailLimit},
             message_limit_per_day = ${messageLimit},
             connection_request_limit_per_day = ${connectionRequestLimit}
           WHERE uuid = ${linkedinSettingUuid.uuid}
           """
        .update
        .apply()
    }
  }

  /**
   * Helper method to mark a task as completed
   */
  def markTaskAsCompleted(taskId: String, completionTime: org.joda.time.DateTime = org.joda.time.DateTime.now()): Unit = {
    DB.autoCommit { implicit session =>
      sql"""
           UPDATE tasks
           SET
             status = ${api.tasks.models.TaskStatusType.Done.toKey},
             done_at = ${completionTime}
           WHERE task_id = ${taskId}
           """
        .update
        .apply()
    }
  }

  /**
   * Helper method to mark multiple tasks as completed
   */
  def markTasksAsCompleted(taskIds: List[String], completionTime: org.joda.time.DateTime = org.joda.time.DateTime.now()): Unit = {
    if (taskIds.nonEmpty) {
      DB.autoCommit { implicit session =>
        sql"""
             UPDATE tasks
             SET
               status = ${api.tasks.models.TaskStatusType.Done.toKey},
               done_at = ${completionTime}
             WHERE task_id IN (${taskIds})
             """
          .update
          .apply()
      }
    }
  }

  /**
   * Helper method to mark a task as active in CaptainData
   */
  def markTaskAsActiveInCaptainData(taskId: String, jobUid: String = "test_job_uid"): Unit = {
    DB.autoCommit { implicit session =>
      sql"""
           UPDATE tasks
           SET
             status = ${api.tasks.models.TaskStatusType.PushedToLinkedinExecutionQueue.toKey},
             cd_job_uid = ${jobUid}
           WHERE task_id = ${taskId}
           """
        .update
        .apply()
    }
  }

  /**
   * Helper method to link a campaign to LinkedIn settings
   */
  def linkCampaignToLinkedinSettings(
    campaignId: Long,
    teamId: Long,
    linkedinSettingUuid: String
  ): Future[Unit] = {
    Future {
      DB.autoCommit { implicit session =>
        sql"""
             INSERT INTO campaign_channel_settings (
               campaign_id,
               team_id,
               channel_type,
               channel_settings_uuid
             ) VALUES (
               ${campaignId},
               ${teamId},
               ${ChannelType.LinkedinChannel.toString},
               ${linkedinSettingUuid}
             )
             """
          .update
          .apply()
      }
    }
  }
}
