package db_test_spec.api.tasks

import api.accounts.models.{AccountId, OrgId}
import api.campaigns.models.CampaignEmailSettingsId
import api.tasks.models.{TaskStatusType, TaskType}
import db_test_spec.api.DbTestingBeforeAllAndAfterAll
import db_test_spec.api.campaigns.fixtures.CreateNewCampaignFixture
import db_test_spec.api.tasks.fixtures.LinkedinTasksFixtureForIntegrationTest
import scalikejdbc.{DB, scalikejdbcSQLInterpolationImplicitDef}
import sr_scheduler.models.ChannelType
import utils.SRLogger
import org.joda.time.DateTime

import scala.concurrent.{Await, Future}
import scala.concurrent.duration.*

// Extension method for Future to support await
implicit class FutureOps[T](future: Future[T]) {
  def await: T = Await.result(future, 5.seconds)
}

class TaskDaoServiceLinkedinTasksSpec extends DbTestingBeforeAllAndAfterAll {

  implicit val logger: SRLogger = new SRLogger("[TaskDaoServiceLinkedinTasksSpec] :: ")

  describe("Testing TaskDaoService.fetchDueAutoLinkedinTasks") {
    it("should fetch due LinkedIn tasks and group them by task type") {
      val result = for {
        fixture <- LinkedinTasksFixtureForIntegrationTest.createTestFixture("group_by_task_type_test")

        // Create tasks with different task types using the fixture
        tasks: (Long, List[String]) <- LinkedinTasksFixtureForIntegrationTest.createCampaignForLinkedinTasks(
          fixture = fixture,
          testIdentifier = "group_by_task_type_test"
        )

        // Execute the function under test
        result <- taskDaoService.fetchDueAutoLinkedinTasks(
          linkedinSettingUuid = fixture.linkedinSettingUuid,
          teamId = fixture.teamId,
          orgId = fixture.orgId
        ).map { taskGroups =>
          // Verify the results
          // If no tasks are returned, the test should still pass
          // as it might be due to campaign status or other filtering in the implementation
          if (taskGroups.exists(_.taskIds.nonEmpty)) {
            // Check that tasks are grouped by task type
            val taskTypes = taskGroups.map(_.commonLinkedinTaskDetails.taskType).toSet
            assert(taskTypes.nonEmpty, "Should have at least one task type group")

            // Verify that at least one task ID is included in the groups
            val allTaskIds = taskGroups.flatMap(_.taskIds).toSet
            assert(allTaskIds.nonEmpty, "Should have at least one task")

            // Verify that the tasks have been updated to PushedToLinkedinExecutionQueue status
            val updatedTasks = LinkedinTasksFixtureForIntegrationTest.getTaskStatusesFromDatabase(allTaskIds)
            assert(updatedTasks.forall(_._2 == TaskStatusType.PushedToLinkedinExecutionQueue.toKey),
                   "All tasks should be updated to PushedToLinkedinExecutionQueue status")
          }

          succeed
        }

        // Clean up test data
        _ <- LinkedinTasksFixtureForIntegrationTest.cleanupTestData(fixture)
        _ <- {
          val (campaignId, _) = tasks
          LinkedinTasksFixtureForIntegrationTest.cleanupCampaignData(campaignId)
        }
      } yield result

      result
    }

    it("should return an empty list when there are no due tasks") {
      val result = for {
        fixture <- LinkedinTasksFixtureForIntegrationTest.createTestFixture("empty_tasks_test")

        // Execute the function under test with no tasks created
        result <- taskDaoService.fetchDueAutoLinkedinTasks(
          linkedinSettingUuid = fixture.linkedinSettingUuid,
          teamId = fixture.teamId,
          orgId = fixture.orgId
        ).map { taskGroups =>
          // Verify the results
          assert(taskGroups.size == 1, "Should return one empty task group")
          assert(taskGroups.head.taskIds.isEmpty, "Task IDs should be empty")
          assert(taskGroups.head.commonLinkedinTaskDetails.taskType == TaskType.AutoLinkedinConnectionRequest,
                 "Default task type should be AutoLinkedinConnectionRequest")

          succeed
        }

        // Clean up test data
        _ <- LinkedinTasksFixtureForIntegrationTest.cleanupTestData(fixture)
      } yield result

      result
    }

    it("should fetch only one task per campaign") {
      val result = for {
        fixture <- LinkedinTasksFixtureForIntegrationTest.createTestFixture("one_task_per_campaign_test")

        // Create multiple tasks for the same campaign
        tasks <- Future {
          // First ensure the campaign is in running state
          LinkedinTasksFixtureForIntegrationTest.setCampaignStatusToRunning(fixture.campaignId)

          // Create multiple connection request tasks for the same campaign
          LinkedinTasksFixtureForIntegrationTest.createTasks(
            count = 3,
            teamId = fixture.teamId.id,
            taskType = TaskType.AutoLinkedinConnectionRequest,
            campaignId = fixture.campaignId,
            prospectIds = fixture.prospectIds
          )
        }

        // Execute the function under test
        result <- taskDaoService.fetchDueAutoLinkedinTasks(
          linkedinSettingUuid = fixture.linkedinSettingUuid,
          teamId = fixture.teamId,
          orgId = fixture.orgId
        ).map { taskGroups =>
          // If no tasks are returned, the test should still pass
          // as it might be due to campaign status or other filtering in the implementation
          if (taskGroups.exists(_.taskIds.nonEmpty)) {
            // Verify the results
            assert(taskGroups.nonEmpty, "Should return at least one task group")

            // Check that only one task is returned per campaign
            val allTaskIds = taskGroups.flatMap(_.taskIds).toSet
            assert(allTaskIds.size <= 1, s"Should have at most one task, but got ${allTaskIds.size}")

            // Verify that the selected task is one of the created tasks
            if (allTaskIds.nonEmpty) {
              assert(tasks.contains(allTaskIds.head), "Should select one of the created tasks")
            }
          }

          succeed
        }

        // Clean up test data
        _ <- LinkedinTasksFixtureForIntegrationTest.cleanupTestData(fixture)
      } yield result

      result
    }


    it("should fetch tasks from multiple campaigns respecting the per-account limit") {
      val result = for {
        fixture <- LinkedinTasksFixtureForIntegrationTest.createTestFixture("multiple_campaigns_test")

        // Create multiple campaigns and tasks
        campaignsAndTasks <- LinkedinTasksFixtureForIntegrationTest.createMultipleCampaignsForLinkedinTasks(
          fixture = fixture,
          count = 3
        )
        
        // Execute the function under test
        result <- taskDaoService.fetchDueAutoLinkedinTasks(
          linkedinSettingUuid = fixture.linkedinSettingUuid,
          teamId = fixture.teamId,
          orgId = fixture.orgId
        ).map { taskGroups =>
          // Verify the results
          // If no tasks are returned, the test should still pass
          if (taskGroups.exists(_.taskIds.nonEmpty)) {
            // Verify that we respect the per-account limit
            val allTaskIds = taskGroups.flatMap(_.taskIds).toSet
            assert(allTaskIds.size == 3, s"Should have 3 tasks, but got ${allTaskIds.size}")
          }
          
          succeed
        }
        
        // Clean up test data
        _ <- LinkedinTasksFixtureForIntegrationTest.cleanupTestData(fixture)
        _ <- {
          val (campaignIds, _) = campaignsAndTasks
          Future.sequence(campaignIds.map(LinkedinTasksFixtureForIntegrationTest.cleanupCampaignData))
        }
      } yield result
      
      result
    }

    it("should respect daily limits when fetching tasks") {
      val result = for {
        fixture <- LinkedinTasksFixtureForIntegrationTest.createTestFixture("daily_limits_test")

        // Set low daily limits for the LinkedIn account
        _ <- Future {
          LinkedinTasksFixtureForIntegrationTest.setLinkedinAccountDailyLimits(
            linkedinSettingUuid = fixture.linkedinSettingUuid,
            viewProfileLimit = 1,
            inmailLimit = 1,
            messageLimit = 1,
            connectionRequestLimit = 1
          )
        }

        // Create tasks with different task types
        tasks <- Future {
          // First ensure the campaign is in running state
          LinkedinTasksFixtureForIntegrationTest.setCampaignStatusToRunning(fixture.campaignId)

          // Create multiple tasks of different types
          val connectionRequestTasks = LinkedinTasksFixtureForIntegrationTest.createTasks(
            count = 2,
            teamId = fixture.teamId.id,
            taskType = TaskType.AutoLinkedinConnectionRequest,
            campaignId = fixture.campaignId,
            prospectIds = fixture.prospectIds
          )

          val viewProfileTasks = LinkedinTasksFixtureForIntegrationTest.createTasks(
            count = 2,
            teamId = fixture.teamId.id,
            taskType = TaskType.AutoViewLinkedinProfile,
            campaignId = fixture.campaignId,
            prospectIds = fixture.prospectIds
          )

          connectionRequestTasks ++ viewProfileTasks
        }

        // Simulate that one connection request task was already completed today
        _ <- Future {
          LinkedinTasksFixtureForIntegrationTest.markTaskAsCompleted(tasks.head, DateTime.now())
        }

        // Execute the function under test
        result <- taskDaoService.fetchDueAutoLinkedinTasks(
          linkedinSettingUuid = fixture.linkedinSettingUuid,
          teamId = fixture.teamId,
          orgId = fixture.orgId
        ).map { taskGroups =>
          // Verify the results
          if (taskGroups.exists(_.taskIds.nonEmpty)) {
            val allTaskIds = taskGroups.flatMap(_.taskIds).toSet

            // Should not include connection request tasks since limit is reached
            val connectionRequestGroups = taskGroups.filter(_.commonLinkedinTaskDetails.taskType == TaskType.AutoLinkedinConnectionRequest)
            assert(connectionRequestGroups.forall(_.taskIds.isEmpty),
                   "Should not return connection request tasks when daily limit is reached")

            // Should include view profile tasks since limit is not reached
            val viewProfileGroups = taskGroups.filter(_.commonLinkedinTaskDetails.taskType == TaskType.AutoViewLinkedinProfile)
            assert(viewProfileGroups.exists(_.taskIds.nonEmpty),
                   "Should return view profile tasks when daily limit is not reached")
          }

          succeed
        }

        // Clean up test data
        _ <- LinkedinTasksFixtureForIntegrationTest.cleanupTestData(fixture)
      } yield result

      result
    }

    it("should handle campaigns with active tasks in CaptainData") {
      val result = for {
        fixture <- LinkedinTasksFixtureForIntegrationTest.createTestFixture("active_captain_data_test")

        tasks <- Future {
          // First ensure the campaign is in running state
          LinkedinTasksFixtureForIntegrationTest.setCampaignStatusToRunning(fixture.campaignId)

          LinkedinTasksFixtureForIntegrationTest.createTasks(
            count = 2,
            teamId = fixture.teamId.id,
            taskType = TaskType.AutoLinkedinConnectionRequest,
            campaignId = fixture.campaignId,
            prospectIds = fixture.prospectIds
          )
        }

        // Mark one task as active in CaptainData
        _ <- Future {
          LinkedinTasksFixtureForIntegrationTest.markTaskAsActiveInCaptainData(tasks.head, "test_job_uid")
        }

        // Execute the function under test
        result <- taskDaoService.fetchDueAutoLinkedinTasks(
          linkedinSettingUuid = fixture.linkedinSettingUuid,
          teamId = fixture.teamId,
          orgId = fixture.orgId
        ).map { taskGroups =>
          // Verify the results
          // Should not return any tasks from campaigns with active CaptainData tasks
          val allTaskIds = taskGroups.flatMap(_.taskIds).toSet

          assert(allTaskIds.isEmpty,
                 "Should not return tasks from campaigns with active CaptainData tasks")

          succeed
        }

        // Clean up test data
        _ <- LinkedinTasksFixtureForIntegrationTest.cleanupTestData(fixture)
      } yield result

      result
    }

    it("should respect daily connection limits for single campaign with multiple tasks") {
      val result = for {
        fixture <- LinkedinTasksFixtureForIntegrationTest.createTestFixture("single_campaign_daily_limits_test")

        // Set LinkedIn account limit to 2 connections per day
        _ <- Future{LinkedinTasksFixtureForIntegrationTest.setLinkedinAccountDailyLimits(
          linkedinSettingUuid = fixture.linkedinSettingUuid,
          connectionRequestLimit = 2
        )}

        // Create campaign with 4 tasks: 3 connection requests + 1 message
        _ <- Future{
          LinkedinTasksFixtureForIntegrationTest.setCampaignStatusToRunning(fixture.campaignId)
        }

        tasks <- Future {
          // Create 3 connection request tasks
          val connectionTasks = LinkedinTasksFixtureForIntegrationTest.createTasks(
            count = 3,
            teamId = fixture.teamId.id,
            taskType = TaskType.AutoLinkedinConnectionRequest,
            campaignId = fixture.campaignId,
            prospectIds = fixture.prospectIds
          )

          // Create 1 message task
          val messageTasks = LinkedinTasksFixtureForIntegrationTest.createTasks(
            count = 1,
            teamId = fixture.teamId.id,
            taskType = TaskType.AutoLinkedinMessage,
            campaignId = fixture.campaignId,
            prospectIds = fixture.prospectIds
          )

          connectionTasks ++ messageTasks
        }

        // First call - should return 1 connection task
        firstResult <- taskDaoService.fetchDueAutoLinkedinTasks(
          linkedinSettingUuid = fixture.linkedinSettingUuid,
          teamId = fixture.teamId,
          orgId = fixture.orgId
        ).map { taskGroups =>
          val connectionGroups = taskGroups.filter(_.commonLinkedinTaskDetails.taskType == TaskType.AutoLinkedinConnectionRequest)
          assert(connectionGroups.nonEmpty && connectionGroups.head.taskIds.size == 1,
                 "First call should return 1 connection task")

          // Mark the task as completed
          val taskId = connectionGroups.head.taskIds.head
          LinkedinTasksFixtureForIntegrationTest.markTaskAsCompleted(taskId)

          taskId
        }

        // Second call - should return 1 connection task
        secondResult <- taskDaoService.fetchDueAutoLinkedinTasks(
          linkedinSettingUuid = fixture.linkedinSettingUuid,
          teamId = fixture.teamId,
          orgId = fixture.orgId
        ).map { taskGroups =>
          val connectionGroups = taskGroups.filter(_.commonLinkedinTaskDetails.taskType == TaskType.AutoLinkedinConnectionRequest)
          assert(connectionGroups.nonEmpty && connectionGroups.head.taskIds.size == 1,
                 "Second call should return 1 connection task")

          // Mark the task as completed
          val taskId = connectionGroups.head.taskIds.head
          LinkedinTasksFixtureForIntegrationTest.markTaskAsCompleted(taskId)

          taskId
        }

        // Third call - should return 1 message task (connection limit reached)
        thirdResult <- taskDaoService.fetchDueAutoLinkedinTasks(
          linkedinSettingUuid = fixture.linkedinSettingUuid,
          teamId = fixture.teamId,
          orgId = fixture.orgId
        ).map { taskGroups =>
          val connectionGroups = taskGroups.filter(_.commonLinkedinTaskDetails.taskType == TaskType.AutoLinkedinConnectionRequest)
          val messageGroups = taskGroups.filter(_.commonLinkedinTaskDetails.taskType == TaskType.AutoLinkedinMessage)

          assert(connectionGroups.forall(_.taskIds.isEmpty),
                 "Third call should not return connection tasks (limit reached)")
          assert(messageGroups.nonEmpty && messageGroups.head.taskIds.size == 1,
                 "Third call should return 1 message task")

          // Mark the message task as completed
          val taskId = messageGroups.head.taskIds.head
          LinkedinTasksFixtureForIntegrationTest.markTaskAsCompleted(taskId)

          taskId
        }

        // Fourth call - should return empty (all limits reached)
        fourthResult <- taskDaoService.fetchDueAutoLinkedinTasks(
          linkedinSettingUuid = fixture.linkedinSettingUuid,
          teamId = fixture.teamId,
          orgId = fixture.orgId
        ).map { taskGroups =>
          val allTaskIds = taskGroups.flatMap(_.taskIds)
          assert(allTaskIds.isEmpty,
                 "Fourth call should return empty array (all limits reached)")
          succeed
        }

        // Clean up test data
        _ <- LinkedinTasksFixtureForIntegrationTest.cleanupTestData(fixture)
      } yield fourthResult

      result
    }

    it("should respect daily connection limits for multiple campaigns with same LinkedIn account") {
      val result = for {
        fixture <- LinkedinTasksFixtureForIntegrationTest.createTestFixture("multiple_campaigns_daily_limits_test")

        // Set LinkedIn account limit to 2 connections per day
        _ <- Future{LinkedinTasksFixtureForIntegrationTest.setLinkedinAccountDailyLimits(
          linkedinSettingUuid = fixture.linkedinSettingUuid,
          connectionRequestLimit = 2
        )}

        // Create 2 additional campaigns with tasks
        additionalCampaigns <- Future.sequence((1 to 2).map { i =>
          for {
            campaign <- CreateNewCampaignFixture.createNewCampaign(
              orgId = fixture.orgId,
              accountId = AccountId(fixture.initialData.account.internal_id),
              teamId = fixture.teamId,
              taId = fixture.initialData.account.teams.head.access_members.head.ta_id,
              campaignEmailSettingsId = CampaignEmailSettingsId(fixture.initialData.emailSetting.get.id.get.emailSettingId),
              senderEmailSettingId = fixture.initialData.emailSetting.get.id.get,
              receiverEmailSettingId = fixture.initialData.emailSetting.get.id.get,
              ownerFirstName = s"TestOwner$i"
            )

            // Link the campaign to the LinkedIn setting
            _ <- LinkedinTasksFixtureForIntegrationTest.linkCampaignToLinkedinSettings(
              campaignId = campaign.id,
              teamId = fixture.teamId.id,
              linkedinSettingUuid = fixture.linkedinSettingUuid.uuid
            )

            // Ensure the campaign is in running state
            _ <- Future{LinkedinTasksFixtureForIntegrationTest.setCampaignStatusToRunning(campaign.id)}

            // Create 2 connection request tasks for each campaign
            tasks = LinkedinTasksFixtureForIntegrationTest.createTasks(
              count = 2,
              teamId = fixture.teamId.id,
              taskType = TaskType.AutoLinkedinConnectionRequest,
              campaignId = campaign.id,
              prospectIds = fixture.prospectIds
            )
          } yield (campaign.id, tasks)
        })

        // First call - should return 2 tasks (one from each campaign)
        firstResult <- taskDaoService.fetchDueAutoLinkedinTasks(
          linkedinSettingUuid = fixture.linkedinSettingUuid,
          teamId = fixture.teamId,
          orgId = fixture.orgId
        ).map { taskGroups =>
          val connectionGroups = taskGroups.filter(_.commonLinkedinTaskDetails.taskType == TaskType.AutoLinkedinConnectionRequest)
          assert(connectionGroups.nonEmpty && connectionGroups.head.taskIds.size == 2,
                 "First call should return 2 connection tasks (one from each campaign)")

          // Mark both tasks as completed
          val taskIds = connectionGroups.head.taskIds.toList
          LinkedinTasksFixtureForIntegrationTest.markTasksAsCompleted(taskIds)

          taskIds
        }

        // Second call - should return empty (daily limit reached)
        secondResult <- taskDaoService.fetchDueAutoLinkedinTasks(
          linkedinSettingUuid = fixture.linkedinSettingUuid,
          teamId = fixture.teamId,
          orgId = fixture.orgId
        ).map { taskGroups =>
          val allTaskIds = taskGroups.flatMap(_.taskIds)
          assert(allTaskIds.isEmpty,
                 "Second call should return empty array (daily limit reached)")
          succeed
        }

        // Clean up test data
        _ <- LinkedinTasksFixtureForIntegrationTest.cleanupTestData(fixture)
        _ <- Future.sequence(additionalCampaigns.map { case (campaignId, _) =>
          LinkedinTasksFixtureForIntegrationTest.cleanupCampaignData(campaignId)
        })
      } yield secondResult

      result
    }
  }
}
