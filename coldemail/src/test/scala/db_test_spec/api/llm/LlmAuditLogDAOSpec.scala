package db_test_spec.api.llm

import api.accounts.TeamId
import api.accounts.models.OrgId
import api.campaigns.models.CampaignEmailSettingsId
import api.campaigns.services.CampaignId
import api.columns.ProspectColGenStatus
import api.columns.models.ColumnDefsProspectsId
import api.llm.dao.{LlmAuditLogInsertData, UpdateStatusData}
import api.llm.models.LlmFlow
import api.prospects.models.{ProspectId, StepId}
import api.sr_ai.models.{AiModel, GoogleAiModel, OpenAiModel}
import db_test_spec.api.DbTestingBeforeAllAndAfterAll
import io.smartreach.esp.api.emails.EmailSettingId
import org.joda.time.DateTime
import play.api.libs.json.Json
import utils.mq.ai_content_generation.MqAiContentGenerationRequest

import scala.util.Success

class LlmAuditLogDAOSpec  extends DbTestingBeforeAllAndAfterAll {


  describe("insertLlmAuditLog") {
    it("should insert") {

      val result = llmAuditLogDAO.insertLlmAuditLog(
        logs = List(
          LlmAuditLogInsertData (
            team_id = TeamId(1),
            prospect_id = ProspectId(1),
            llm_tool = OpenAiModel.Gpt4o,
            status = ProspectColGenStatus.Completed,
            flow = LlmFlow.MagicColumnFlow,
            column_def_id = Some(ColumnDefsProspectsId(1)),
            consumed_token_count_prompt_input = Some(1),
            consumed_token_count_generation_output = Some(1),
            request_log_id = "request_log_id",
            queue_message = Json.obj()
          )
        )
      )
      
      println(s"$result")

      assert(result.isSuccess)
    }
    it("should insert with No tool") {

      val result = llmAuditLogDAO.insertLlmAuditLog(
        logs = List(
          LlmAuditLogInsertData (
            team_id = TeamId(1),
            prospect_id = ProspectId(1),
            llm_tool = OpenAiModel.Gpt4o,
            status = ProspectColGenStatus.Failed,
            flow = LlmFlow.MagicColumnFlow,
            column_def_id = Some(ColumnDefsProspectsId(1)),
            consumed_token_count_prompt_input = None,
            consumed_token_count_generation_output = None,
            request_log_id = "request_log_id",
            queue_message = Json.obj()
          )
        )
      )

      assert(result.isSuccess)
    }
  }
  
  describe("AiModel") {
    it("should give Gemini25Flash") {
      val result = AiModel.fromKey("gemini-2.5-flash-preview-04-17")
      assert(result == Success(GoogleAiModel.Gemini25Flash))
    }
    it("should give Gpt4Turbo") {
      val result = AiModel.fromKey("gpt-4-turbo")
      assert(result == Success(OpenAiModel.Gpt4Turbo))
    }
    it("should give Gpt41Mini") {
      val result = AiModel.fromKey("gpt-4.1-mini")
      assert(result == Success(OpenAiModel.Gpt41Mini))
    }
    it("should give Gpt4o") {
      val result = AiModel.fromKey("gpt-4o")
      assert(result == Success(OpenAiModel.Gpt4o))
    }
  }

  describe("getAttempts") {
    it("should get data") {

      val result = for {
        insert <- llmAuditLogDAO.insertLlmAuditLog(
          logs = List(
            LlmAuditLogInsertData(
              team_id = TeamId(1),
              prospect_id = ProspectId(1),
              llm_tool = OpenAiModel.Gpt4o,
              status = ProspectColGenStatus.Pending,
              flow = LlmFlow.MagicColumnFlow,
              column_def_id = Some(ColumnDefsProspectsId(1)),
              consumed_token_count_prompt_input = Some(1),
              consumed_token_count_generation_output = Some(1),
              request_log_id = "request_log_id",
              queue_message = Json.toJson(MqAiContentGenerationRequest(
                campaignId = CampaignId(1),
                stepId = StepId(1),
                prospectId = ProspectId(1),
                variantId = 1,
                senderEmailSettingsId = EmailSettingId(1),
                campaignEmailSettingsId = CampaignEmailSettingsId(1),
                orgId = OrgId(1),
                teamId = TeamId(1),
                scheduledAt = DateTime.now(),
                logId = None
              ))
            )
          )
        )
        get <- llmAuditLogDAO.getAttempts[MqAiContentGenerationRequest](flow = LlmFlow.MagicColumnFlow)
      } yield get

      println(s"$result")

      assert(result.isSuccess)
    }

  }


  describe("updateStatus") {
    it("should update without count ") {

      val result = for {
        insert <- llmAuditLogDAO.insertLlmAuditLog(
          logs = List(
            LlmAuditLogInsertData(
              team_id = TeamId(1),
              prospect_id = ProspectId(1),
              llm_tool = OpenAiModel.Gpt4o,
              status = ProspectColGenStatus.Pending,
              flow = LlmFlow.MagicColumnFlow,
              column_def_id = Some(ColumnDefsProspectsId(1)),
              consumed_token_count_prompt_input = Some(1),
              consumed_token_count_generation_output = Some(1),
              request_log_id = "request_log_id",
              queue_message = Json.toJson(MqAiContentGenerationRequest(
                campaignId = CampaignId(1),
                stepId = StepId(1),
                prospectId = ProspectId(1),
                variantId = 1,
                senderEmailSettingsId = EmailSettingId(1),
                campaignEmailSettingsId = CampaignEmailSettingsId(1),
                orgId = OrgId(1),
                teamId = TeamId(1),
                scheduledAt = DateTime.now(),
                logId = None
              ))
            )
          )
        )
        get <- llmAuditLogDAO.getAttempts[MqAiContentGenerationRequest](flow = LlmFlow.MagicColumnFlow)
        
        update <- llmAuditLogDAO.updateStatus(
          updateStatusData = List(UpdateStatusData(
            id = get.head.id,
            teamId = get.head.team_id,
            newStatus = ProspectColGenStatus.Completed
          )), updateCount = false
        )
      } yield update

      println(s"$result")

      assert(result.isSuccess)
    }

    it("should update with count ") {

      val result = for {
        insert <- llmAuditLogDAO.insertLlmAuditLog(
          logs = List(
            LlmAuditLogInsertData(
              team_id = TeamId(1),
              prospect_id = ProspectId(1),
              llm_tool = OpenAiModel.Gpt4o,
              status = ProspectColGenStatus.Pending,
              flow = LlmFlow.MagicColumnFlow,
              column_def_id = Some(ColumnDefsProspectsId(1)),
              consumed_token_count_prompt_input = Some(1),
              consumed_token_count_generation_output = Some(1),
              request_log_id = "request_log_id",
              queue_message = Json.toJson(MqAiContentGenerationRequest(
                campaignId = CampaignId(1),
                stepId = StepId(1),
                prospectId = ProspectId(1),
                variantId = 1,
                senderEmailSettingsId = EmailSettingId(1),
                campaignEmailSettingsId = CampaignEmailSettingsId(1),
                orgId = OrgId(1),
                teamId = TeamId(1),
                scheduledAt = DateTime.now(),
                logId = None
              ))
            )
          )
        )
        get <- llmAuditLogDAO.getAttempts[MqAiContentGenerationRequest](flow = LlmFlow.MagicColumnFlow)

        update <- llmAuditLogDAO.updateStatus(
          updateStatusData = List(UpdateStatusData(
            id = get.head.id,
            teamId = get.head.team_id,
            newStatus = ProspectColGenStatus.Completed
          )), updateCount = true
        )
      } yield update

      println(s"$result")

      assert(result.isSuccess)
    }

  }

}
