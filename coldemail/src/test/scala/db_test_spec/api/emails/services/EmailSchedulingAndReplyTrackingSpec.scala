package db_test_spec.api.emails.services

import api.accounts.models.{AccountId, OrgId}
import api.accounts.{Account, ReplyHandling, TeamId}
import api.campaigns.{CPTuple, CampaignStepVariant}
import api.campaigns.models.{CampaignName, SendEmailFromCampaignDetails, StepDetails}
import api.campaigns.services.CampaignId
import api.emails.models.EmailMessageTrackedV4.*
import api.emails.models.{EmailMessageTrackedV4, EmailReplyType, PropsTrackedReply}
import api.emails.{DBEmailMessagesSavedResponse, EmailMessageTracked, EmailScheduledForCheckingReplies, EmailSetting}
import api.prospects.models.{ProspectCategory, ProspectId, StepId}
import api.prospects.ProspectCreateFormData
import api.team_inbox.model.TeamInboxDetails
import db_test_spec.api.accounts.fixtures.{EmailScheduledNewFixture, NewAccountAndEmailSettingData}
import db_test_spec.api.campaigns.dao.CampaignProspectTestDAO
import db_test_spec.api.campaigns.fixtures.CreateStepForCampaignFixture
import db_test_spec.api.campaigns.test_utils.{CampaignUtils, CreateAndStartCampaignData}
import db_test_spec.api.emails.fixtures.DefaultEmailScheduledParameterFixtures.generateEmailMessageTracked
import db_test_spec.api.emails.fixtures.EmailSettingFixtureForIntegrationTest
import db_test_spec.api.prospects.fixtures.DefaultProspectParameterFixture
import db_test_spec.api.scheduler.dao.SchedulerTestDAO
import db_test_spec.api.scheduler.fixtures.{DefaultParametersFixtureForInitializingDataForReScheduling, ReSchedulingFixture, ScheduleTaskFixture}
import db_test_spec.api.{DbTestingBeforeAllAndAfterAll, InitialData, SchedulerIntegrationTestResult}
import db_test_spec.team_inbox.fixtures.TeamInboxFixtureForIntegrationTest
import db_test_spec.utils.SrRandomTestUtils
import eventframework.ProspectObject
import io.smartreach.esp.api.emails.{EmailSettingId, IEmailAddress}
import io.smartreach.esp.utils.email.models.{CommonPropsEmailMessage, EmailReply}
import org.joda.time.DateTime
import play.api.libs.json.{JsValue, Json}
import sr_scheduler.models.ChannelData.EmailChannelData
import utils.email.EmailReplyStatus
import utils.email.services.InternalTrackingNote
import utils.email.services.InternalTrackingNote.InternalTrackingNote
import utils.helpers.LogHelpers
import utils.mq.channel_scheduler.channels.{EmailChannelScheduler, ScheduleTasksData}
import utils.{Helpers, SRLogger}

import scala.concurrent.Future

case class EmailSchedulingAndReplyTrackingSpecResult(
                                                      replyInternalTrackingNote: InternalTrackingNote,
                                                      prospectCompletedBecauseReplyHandling: List[Long],
                                                      prospectsAddedInCampaign: Seq[Long],
                                                      repliedProspectEmailFromCampaign: Seq[String]
                                                    )

case class EmailSchedulingAndReplyTrackingSpecResultV2(
                                                        replyInternalTrackingNote: InternalTrackingNote,
                                                        campaignId1: CampaignId,
                                                        campaignId2: CampaignId,
                                                        prospectId: ProspectId,
                                                        prospectsCompletedInCampaign: List[CPTuple],
                                                        repliedCamapignProspect: List[CPTuple]
                                                      )

case class ManualEmailMarksCampaignCompletedSpecRes(
                                                     replyInternalTrackingNote: InternalTrackingNote,
                                                     prospectCompletedInCampaign: List[CPTuple],
                                                     campaignId: CampaignId,
                                                     repliedCamapignProspect: List[CPTuple]
                                                   )

class EmailSchedulingAndReplyTrackingSpec extends DbTestingBeforeAllAndAfterAll {

  //NOTE: only for testing
  private def convertEmailMessageTrackedv4ToEmailMessageTracked(
                                                         emailMessageTrackedV4: EmailMessageTrackedV4):
  EmailMessageTracked = {
    val commonPropsEmailMessage = getCommonPropsEmailMessage(emailMessageTrackedV4)
    val propsTrackedReply = getPropsTrackedReply(emailMessageTrackedV4)

    val (gmail_msg_id: Option[String], gmail_thread_id: Option[String]) =
      emailMessageTrackedV4 match {

        case gmailMessageTrackedV4: GmailMessageTrackedV4 =>
          (Some(gmailMessageTrackedV4.gmailReplyTrackedViaAPI.gmail_msg_id),
            Some(gmailMessageTrackedV4.gmailReplyTrackedViaAPI.gmail_thread_id))

        case _: OutlookMessageTrackedV4 |
             _: ImapTrackedReplyV4 =>
          (None, None)
      }

    val (outlook_msg_id: Option[String], outlook_conversation_id: Option[String],
    outlook_response_json: Option[JsValue]) =
      emailMessageTrackedV4 match {

        case outlookMessageTrackedV4: OutlookMessageTrackedV4 =>
          (Some(outlookMessageTrackedV4.outlookReplyTrackedViaAPI.outlook_msg_id),
            Some(outlookMessageTrackedV4.outlookReplyTrackedViaAPI.outlook_converstation_id),
            Some(outlookMessageTrackedV4.outlookReplyTrackedViaAPI.outlook_response_json)
          )

        case _: GmailMessageTrackedV4 |
             _: ImapTrackedReplyV4 =>
          (None, None, None)
      }


    EmailMessageTracked(
      inbox_email_setting_id = propsTrackedReply.inbox_email_setting_id.emailSettingId, // FIXME VALUECLASS
      from = commonPropsEmailMessage.from,


      // v3 specific start
      to_emails = commonPropsEmailMessage.to_emails, // only for newinbox api
      // v3 specific end


      subject = commonPropsEmailMessage.subject,

      body = commonPropsEmailMessage.body,
      base_body = commonPropsEmailMessage.base_body,
      text_body = commonPropsEmailMessage.text_body,

      references_header = commonPropsEmailMessage.references_header,

      campaign_id = propsTrackedReply.campaign_id.map(_.id), // FIXME VALUECLASS
      step_id = propsTrackedReply.step_id.map(_.id), // FIXME VALUECLASS

      // FIXME VALUECLASS
      prospect_id_in_campaign = propsTrackedReply.prospect_id_in_campaign.map(_.id), // prospect_id for whom campaign should be paused

      // todo - catch this with a unit test - DONE
      prospect_account_id_in_campaign = propsTrackedReply.prospect_account_id_in_campaign.map(_.id), // FIXME VALUECLASS

      campaign_name = propsTrackedReply.campaign_name,
      step_name = propsTrackedReply.step_name,

      received_at = commonPropsEmailMessage.received_at,
      recorded_at = commonPropsEmailMessage.recorded_at,
      sr_inbox_read = propsTrackedReply.sr_inbox_read,
      original_inbox_folder = commonPropsEmailMessage.original_inbox_folder,
      email_status = propsTrackedReply.email_status,

      message_id = commonPropsEmailMessage.message_id,
      full_headers = commonPropsEmailMessage.full_headers,

      scheduled_manually = propsTrackedReply.scheduled_manually,

      reply_to = commonPropsEmailMessage.reply_to,

      email_thread_id = propsTrackedReply.email_thread_id,
      gmail_msg_id = gmail_msg_id,
      gmail_thread_id = gmail_thread_id,

      outlook_msg_id = outlook_msg_id,
      outlook_conversation_id = outlook_conversation_id,
      outlook_response_json = outlook_response_json,

      cc_emails = commonPropsEmailMessage.cc_emails,
      in_reply_to_header = commonPropsEmailMessage.in_reply_to_header,
      team_id = propsTrackedReply.team_id.id, // FIXME VALUECLASS
      account_id = propsTrackedReply.account_id.id, // FIXME VALUECLASS

      internal_tracking_note = propsTrackedReply.internal_tracking_note,

      tempThreadId = propsTrackedReply.tempThreadId // used temporarily for identifying email threads during reply tracking

    )
  }


  val createProspectData1 = ProspectCreateFormData(
    email = Some("<EMAIL>"),
    first_name = Some("Bruce"),
    last_name = Some("Wayne"),
    custom_fields = Json.obj(),

    list = None,
    company = None,
    city = None,
    country = None,
    timezone = None,

    state = None,
    job_title = None,
    phone = None,
    phone_2 = None,
    phone_3 = None,
    linkedin_url = None
  )

  describe("createCampaign, filterReplies and saveEmailsAndReplies") {

    it("Campaign email sent to a prospect and reply came from another prospect with same domain then mark as completed campaign - due to PAUSE_ALL_PROSPECT_ACCOUNT_CAMPAIGNS_ON_REPLY") {
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
      val account: Account = initialData.account
      val emailSetting: EmailSetting = initialData.emailSetting.get
      val accountId: AccountId = AccountId(account.internal_id)
      val teamId: TeamId = TeamId(account.teams.head.team_id)
      val emailSettingId = EmailSettingId(emailSetting.id.get.emailSettingId)

      val emailReplyStatus: EmailReplyStatus = EmailReplyStatus(
        replyType = EmailReplyType.NOT_CATEGORIZED,
        isReplied = true,
        isUnsubscribeRequest = false,
        isAutoReply = false,
        isOutOfOfficeReply = false,
        isInvalidEmail = false,
        isForwarded = false,
        bouncedData = None
      )

      val prospectAccount = "example.com"

      // adding prospects to account
      val prospects: Seq[ProspectCreateFormData] = DefaultProspectParameterFixture.generateNProspectEmails(
          numberOfProspect = 1,
          domainName = prospectAccount
        )
          .map(prospectEmail => createProspectData1.copy(
          email = Some(prospectEmail)
        ))

      val scheduleTaskData: Future[EmailSchedulingAndReplyTrackingSpecResult] = for {
        // 1. create and start campaign for 3 prospects of domain example.com
        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          prospects = Some(prospects)
        )

        // 2. Get first prospect from campaign
        prospect: ProspectObject <- Future {
          createAndStartCampaignData.addProspect.head
        }

        // 3. Email scheduling for campaign
        addingEmailScheduled <- Future.fromTry {
          emailScheduledDAOService.saveEmailsToBeScheduledAndUpdateCampaignDataV2(
            emailsToBeScheduled = Vector(EmailScheduledNewFixture.generateEmailScheduledNew3.copy(
              campaign_id = Some(createAndStartCampaignData.createCampaign.id),
              step_id = createAndStartCampaignData.createCampaign.head_step_id,
              from_email = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.sender_email,
              scheduled_from_campaign = true,
              is_opening_step = true,
              sender_email_settings_id = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.sender_email_setting_id.emailSettingId,
              team_id = createAndStartCampaignData.createCampaign.team_id,
              account_id = createAndStartCampaignData.createCampaign.owner_id,
              receiver_email_settings_id = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.receiver_email_setting_id.emailSettingId,
              campaign_email_settings_id = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.id,
              prospect_id = Some(prospect.id),
              base_body = "body",
              prospect_account_id = Some(prospect.internal.prospect_account_id.get),
              to_email = prospect.email.get
            )
            ),
            campaign_email_setting_id = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.id,
            emailSendingFlow = None,
            Logger = Logger
          )
        }

        // 4. add auto email step to campaign
        addStep: CampaignStepVariant <- {
          CreateStepForCampaignFixture.createAutoEmailStepForCampaign(
            orgId = emailSetting.org_id,
            teamId = teamId,
            accountId = accountId,
            taId = Helpers.getTeamMemberFromAccount(account = account, team_id = teamId).ta_id,
            campaignId = CampaignId(createAndStartCampaignData.createCampaign.id),
          )
        }

        // 5. mark campaign email as sent
        emailSent <- Future.fromTry {
          emailSenderService.onEmailSent(
            emailSentId = addingEmailScheduled.head.email_scheduled_id,
            data = DefaultParametersFixtureForInitializingDataForReScheduling.defaultEmailToBeSent(
              emailSetting = initialData.emailSetting.get,
              campaign = createAndStartCampaignData.campaign
            ).copy(
              to_emails = Seq(IEmailAddress(email = prospect.email.get)), subject = "subject"),
            accountId = createAndStartCampaignData.createCampaign.owner_id,
            sendEmailFromCampaignDetails = Some(SendEmailFromCampaignDetails(
              campaign_id = createAndStartCampaignData.createCampaign.id,
              campaign_name = createAndStartCampaignData.createCampaign.name,
              // stepDetails can be none when email is being sent manually from Inbox.
              // Example: sendNewEmailManually in InboxV3Service
              stepDetails = Some(StepDetails(
                step_id = addStep.step_id,
                step_name = addStep.label.getOrElse("step"),
                step_type = addStep.step_data.step_type
              ))
            )),
            prospectIdInCampaign = Some(prospect.id),
            currentBillingCycleStartedAt = initialData.account.created_at,
            orgId = initialData.account.org.id,
            repTrackingHostId = 1,
            teamId = initialData.head_team_id
          )
        }

        // 6. scheduled CampaignProspect Count
        scheduledCampaignProspectCount: Int <- Future {
          CampaignProspectTestDAO.getCountOfCampaignProspectSchedulerStatusDue(
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            teamId = teamId,
            prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)),
            current_step_id = StepId(createAndStartCampaignData.addStep.step_id),
          ).get
        }

        // 7. fetch prospectEmailsPastWeek
        prospectEmailsPastWeek: Seq[EmailScheduledForCheckingReplies] <- Future {
          emailScheduledDAOService.getSentEmailsForCheckingReplies(
            senderEmailSettingsIds = Seq(emailSettingId.emailSettingId.toInt),
            teamId = teamId.id,
            logger = Logger
          )
        }

        // 8. Generate email tracked by prospect with same domain which is not in campaign
        newMsg: EmailMessageTrackedV4 <- Future {
          EmailMessageTrackedV4.GmailMessageTrackedV4(
            gmailReplyTrackedViaAPI = EmailReply.GmailReplyTrackedViaAPI(
              commonPropsEmailMessage = CommonPropsEmailMessage(
                from = IEmailAddress(
                  email = "<EMAIL>" // Different email, same domain as prospect
                  ) ,

                to_emails = Seq(IEmailAddress(
                  email = emailSetting.email // email_setting email
                )),

                cc_emails = Seq(),

                reply_to = None,

                subject = "some_subject",

                body = "some_body",
                base_body = "base",
                text_body = "text",

                message_id = "some_message_id123",
                full_headers = Json.obj(),
                in_reply_to_header = None,
                references_header = None,

                received_at = DateTime.now(),
                recorded_at = DateTime.now(),
                original_inbox_folder = Some("inbox"),

                autogeneratedReplyFromHeader = false
              ),
              gmail_msg_id = "gmail_msg_id",
              gmail_thread_id = "gmail_thread_id"
            ),
            propsTrackedReply = PropsTrackedReply(
              inbox_email_setting_id = emailSettingId, // FIXME VALUECLASS
              email_status = emailReplyStatus,
              sr_inbox_read = false,
              scheduled_manually = false,

              campaign_id = None,
              step_id = None,
              prospect_id_in_campaign = None,

              prospect_account_id_in_campaign = None,
              email_thread_id = None,

              tempThreadId = None,

              internal_tracking_note = InternalTrackingNote.NONE,

              team_id = teamId,
              account_id = accountId,

              campaign_name = None,
              step_name = None
            )
          )

        }

        // 9. Pass tracked email to filterReplies with PAUSE_ALL_PROSPECT_ACCOUNT_CAMPAIGNS_ON_REPLY
        filteredReply <- Future.fromTry {
          replyFilteringService.filterRepliesV4(
            newMsgsWithHeaders = Seq(newMsg),
            inboxEmailSetting = emailSetting.copy(reply_handling = ReplyHandling.PAUSE_ALL_PROSPECT_ACCOUNT_CAMPAIGNS_ON_REPLY),
            senderEmails = Seq(emailSetting.email),
            senderMessageIdSufixes = Seq(),
            prospectEmailsPastWeek = prospectEmailsPastWeek,
            Logger: SRLogger
          )
        }

        // 10. EmailMessageTrackedV4 to EmailMessageTracked
        emailMsgs <- Future {
          filteredReply.map(msg => {
            convertEmailMessageTrackedv4ToEmailMessageTracked(emailMessageTrackedV4 = msg)
          })
        }

        // 11.Save filtered reply - here it will mark the prospect as completed
        handleReply: DBEmailMessagesSavedResponse <- Future {
          emailReplyTrackingModelV2.saveEmailsAndRepliesFromInboxV3(
            accountId = accountId.id,
            team_id = teamId.id,
            emailMessages = Seq(emailMsgs.head),
            inboxEmailSetting = initialData.emailSetting.get,
            replyHandling = ReplyHandling.PAUSE_ALL_PROSPECT_ACCOUNT_CAMPAIGNS_ON_REPLY,
            account = initialData.account,
            senderEmails = Seq(initialData.emailSetting.get.email),
            adminReplyFromSRInbox = false,
            auditRequestLogId = "",
            markProspectAsCompleted = true
          ).get
        }

        getProspectCompletedBecauseReplyHandling: List[Long] <- Future {
          CampaignProspectTestDAO.getProspectCompletedBecauseReplyHandling(
            teamId = teamId,
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
            replyHandling = ReplyHandling.PAUSE_ALL_PROSPECT_ACCOUNT_CAMPAIGNS_ON_REPLY
          ).get.map(_.toLong)
        }

        getRepliedProspectEmailFromCampaign: List[String] <- Future {
          CampaignProspectTestDAO.getRepliedProspectEmailFromCampaign(
            teamId = teamId,
            campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id)
          ).get
        }

      } yield {
        EmailSchedulingAndReplyTrackingSpecResult(
          replyInternalTrackingNote = emailMsgs.head.internal_tracking_note,
          prospectCompletedBecauseReplyHandling = getProspectCompletedBecauseReplyHandling,
          prospectsAddedInCampaign = createAndStartCampaignData.addProspect.map(_.id),
          repliedProspectEmailFromCampaign = getRepliedProspectEmailFromCampaign
        )
      }

      scheduleTaskData.map(p => {
        assert(
            p.replyInternalTrackingNote == InternalTrackingNote.NEW_LASTWEEK_DOMAIN, //last week domain
            p.prospectCompletedBecauseReplyHandling.sorted == p.prospectsAddedInCampaign.sorted && // Prospect should be marked as completed
            p.prospectCompletedBecauseReplyHandling.length == 1 && // Should have 1 completed prospect
            p.prospectsAddedInCampaign.length == 1 && // Should have 1 total prospect added in campaign
            p.repliedProspectEmailFromCampaign.contains(prospects.head.email.get.trim.toLowerCase)// Reply came from different email
        )
      }).recover({ case e =>
        println(LogHelpers.getStackTraceAsString(e))
        assert(false)
      })

    }


    it("email sent from two email accounts to one prospect and reply to only one gets marked as completed because of PAUSE_SPECIFIC_CAMPAIGN_ON_REPLY") {
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
      val account: Account = initialData.account
      val emailSetting1: EmailSetting = initialData.emailSetting.get
      val accountId: AccountId = AccountId(account.internal_id)
      val teamId: TeamId = TeamId(account.teams.head.team_id)
      val emailSettingId1 = EmailSettingId(emailSetting1.id.get.emailSettingId)

      // Create a second email setting for the same team
      val emailSetting2: EmailSetting = EmailSettingFixtureForIntegrationTest.createEmailSetting(
        accountId = AccountId(account.internal_id),
        orgId = OrgId(account.org.id),
        teamId = TeamId(account.teams.head.team_id),
        taId = account.teams.head.access_members.head.ta_id
      ).get

      val emailSettingId2 = EmailSettingId(emailSetting2.id.get.emailSettingId)

      val emailReplyStatus: EmailReplyStatus = EmailReplyStatus(
        replyType = EmailReplyType.NOT_CATEGORIZED,
        isReplied = true,
        isUnsubscribeRequest = false,
        isAutoReply = false,
        isOutOfOfficeReply = false,
        isInvalidEmail = false,
        isForwarded = false,
        bouncedData = None
      )

      // Create a single prospect
      val prospectEmail = "<EMAIL>"
      val prospects: Seq[ProspectCreateFormData] = Seq(
        createProspectData1.copy(email = Some(prospectEmail))
      )

      val scheduleTaskData: Future[EmailSchedulingAndReplyTrackingSpecResultV2] = for {
        // 1. Create and start first campaign using emailSetting1
        campaign1Data: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          prospects = Some(prospects),
          emailSettings = Some(emailSetting1)
        )

        // 2. Create and start second campaign using emailSetting2 with the same prospect
        campaign2Data: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          prospects = Some(prospects),
          emailSettings = Some(emailSetting2)
        )

        // 3. Get the prospect from first campaign
        prospect: ProspectObject <- Future {
          campaign1Data.addProspect.head
        }

        // 4. Email scheduling for campaign 1
        addingEmailScheduled1 <- Future.fromTry {
          emailScheduledDAOService.saveEmailsToBeScheduledAndUpdateCampaignDataV2(
            emailsToBeScheduled = Vector(EmailScheduledNewFixture.generateEmailScheduledNew3.copy(
              campaign_id = Some(campaign1Data.createCampaign.id),
              step_id = campaign1Data.createCampaign.head_step_id,
              from_email = campaign1Data.createCampaign.settings.campaign_email_settings.head.sender_email,
              scheduled_from_campaign = true,
              is_opening_step = true,
              sender_email_settings_id = campaign1Data.createCampaign.settings.campaign_email_settings.head.sender_email_setting_id.emailSettingId,
              team_id = campaign1Data.createCampaign.team_id,
              account_id = campaign1Data.createCampaign.owner_id,
              receiver_email_settings_id = campaign1Data.createCampaign.settings.campaign_email_settings.head.receiver_email_setting_id.emailSettingId,
              campaign_email_settings_id = campaign1Data.createCampaign.settings.campaign_email_settings.head.id,
              prospect_id = Some(prospect.id),
              base_body = "body from campaign 1",
              prospect_account_id = Some(prospect.internal.prospect_account_id.get),
              to_email = prospect.email.get
            )),
            campaign_email_setting_id = campaign1Data.createCampaign.settings.campaign_email_settings.head.id,
            emailSendingFlow = None,
            Logger = Logger
          )
        }

        // 5. Email scheduling for campaign 2
        addingEmailScheduled2 <- Future.fromTry {
          emailScheduledDAOService.saveEmailsToBeScheduledAndUpdateCampaignDataV2(
            emailsToBeScheduled = Vector(EmailScheduledNewFixture.generateEmailScheduledNew3.copy(
              campaign_id = Some(campaign2Data.createCampaign.id),
              step_id = campaign2Data.createCampaign.head_step_id,
              from_email = campaign2Data.createCampaign.settings.campaign_email_settings.head.sender_email,
              scheduled_from_campaign = true,
              is_opening_step = true,
              sender_email_settings_id = campaign2Data.createCampaign.settings.campaign_email_settings.head.sender_email_setting_id.emailSettingId,
              team_id = campaign2Data.createCampaign.team_id,
              account_id = campaign2Data.createCampaign.owner_id,
              receiver_email_settings_id = campaign2Data.createCampaign.settings.campaign_email_settings.head.receiver_email_setting_id.emailSettingId,
              campaign_email_settings_id = campaign2Data.createCampaign.settings.campaign_email_settings.head.id,
              prospect_id = Some(prospect.id),
              base_body = "body from campaign 2",
              prospect_account_id = Some(prospect.internal.prospect_account_id.get),
              to_email = prospect.email.get
            )),
            campaign_email_setting_id = campaign2Data.createCampaign.settings.campaign_email_settings.head.id,
            emailSendingFlow = None,
            Logger = Logger
          )
        }

        // 6. Add auto email step to campaign 1
        addStep1: CampaignStepVariant <- {
          CreateStepForCampaignFixture.createAutoEmailStepForCampaign(
            orgId = emailSetting1.org_id,
            teamId = teamId,
            accountId = accountId,
            taId = Helpers.getTeamMemberFromAccount(account = account, team_id = teamId).ta_id,
            campaignId = CampaignId(campaign1Data.createCampaign.id),
          )
        }

        // 7. Add auto email step to campaign 2
        addStep2: CampaignStepVariant <- {
          CreateStepForCampaignFixture.createAutoEmailStepForCampaign(
            orgId = emailSetting2.org_id,
            teamId = teamId,
            accountId = accountId,
            taId = Helpers.getTeamMemberFromAccount(account = account, team_id = teamId).ta_id,
            campaignId = CampaignId(campaign2Data.createCampaign.id),
          )
        }

        // 8. Mark campaign 1 email as sent
        emailSent1 <- Future.fromTry {
          emailSenderService.onEmailSent(
            emailSentId = addingEmailScheduled1.head.email_scheduled_id,
            data = DefaultParametersFixtureForInitializingDataForReScheduling.defaultEmailToBeSent(
              emailSetting = emailSetting1,
              campaign = campaign1Data.campaign
            ).copy(
              to_emails = Seq(IEmailAddress(email = prospect.email.get)), subject = "subject from campaign 1"),
            accountId = campaign1Data.createCampaign.owner_id,
            sendEmailFromCampaignDetails = Some(SendEmailFromCampaignDetails(
              campaign_id = campaign1Data.createCampaign.id,
              campaign_name = campaign1Data.createCampaign.name,
              stepDetails = Some(StepDetails(
                step_id = addStep1.step_id,
                step_name = addStep1.label.getOrElse("step"),
                step_type = addStep1.step_data.step_type
              ))
            )),
            prospectIdInCampaign = Some(prospect.id),
            currentBillingCycleStartedAt = initialData.account.created_at,
            orgId = initialData.account.org.id,
            repTrackingHostId = 1,
            teamId = initialData.head_team_id
          )
        }

        // 9. Mark campaign 2 email as sent
        emailSent2 <- Future.fromTry {
          emailSenderService.onEmailSent(
            emailSentId = addingEmailScheduled2.head.email_scheduled_id,
            data = DefaultParametersFixtureForInitializingDataForReScheduling.defaultEmailToBeSent(
              emailSetting = emailSetting2,
              campaign = campaign2Data.campaign
            ).copy(
              to_emails = Seq(IEmailAddress(email = prospect.email.get)), subject = "subject from campaign 2"),
            accountId = campaign2Data.createCampaign.owner_id,
            sendEmailFromCampaignDetails = Some(SendEmailFromCampaignDetails(
              campaign_id = campaign2Data.createCampaign.id,
              campaign_name = campaign2Data.createCampaign.name,
              stepDetails = Some(StepDetails(
                step_id = addStep2.step_id,
                step_name = addStep2.label.getOrElse("step"),
                step_type = addStep2.step_data.step_type
              ))
            )),
            prospectIdInCampaign = Some(prospect.id),
            currentBillingCycleStartedAt = initialData.account.created_at,
            orgId = initialData.account.org.id,
            repTrackingHostId = 1,
            teamId = initialData.head_team_id
          )
        }

        // 10. Fetch prospect emails past week for first email setting only
        prospectEmailsPastWeek: Seq[EmailScheduledForCheckingReplies] <- Future {
          emailScheduledDAOService.getSentEmailsForCheckingReplies(
            senderEmailSettingsIds = Seq(emailSettingId1.emailSettingId.toInt),
            teamId = teamId.id,
            logger = Logger
          )
        }

        // 11. Generate an email reply from the prospect to emailSetting1 only
        newMsg: EmailMessageTrackedV4 <- Future {
          EmailMessageTrackedV4.GmailMessageTrackedV4(
            gmailReplyTrackedViaAPI = EmailReply.GmailReplyTrackedViaAPI(
              commonPropsEmailMessage = CommonPropsEmailMessage(
                from = IEmailAddress(
                  email = prospectEmail
                ),
                to_emails = Seq(IEmailAddress(
                  email = emailSetting1.email // Reply only to first email setting
                )),
                cc_emails = Seq(),
                reply_to = None,
                subject = "Re: subject from campaign 1",
                body = "Thank you for reaching out",
                base_body = "base",
                text_body = "text",
                message_id = "reply_message_id123",
                full_headers = Json.obj(),
                in_reply_to_header = emailSent1.message_id,
                references_header = emailSent1.message_id,
                received_at = DateTime.now(),
                recorded_at = DateTime.now(),
                original_inbox_folder = Some("inbox"),
                autogeneratedReplyFromHeader = false
              ),
              gmail_msg_id = "gmail_msg_id_reply",
              gmail_thread_id = "gmail_thread_id_reply"
            ),
            propsTrackedReply = PropsTrackedReply(
              inbox_email_setting_id = emailSettingId1,
              email_status = emailReplyStatus,
              sr_inbox_read = false,
              scheduled_manually = false,
              campaign_id = None,
              step_id = None,
              prospect_id_in_campaign = None,
              prospect_account_id_in_campaign = None,
              email_thread_id = None,
              tempThreadId = None,
              internal_tracking_note = InternalTrackingNote.NONE,
              team_id = teamId,
              account_id = accountId,
              campaign_name = None,
              step_name = None
            )
          )
        }

        // 12. Pass tracked email to filterReplies
        filteredReply <- Future.fromTry {
          replyFilteringService.filterRepliesV4(
            newMsgsWithHeaders = Seq(newMsg),
            inboxEmailSetting = emailSetting1.copy(reply_handling = ReplyHandling.PAUSE_SPECIFIC_CAMPAIGN_ON_REPLY),
            senderEmails = Seq(emailSetting1.email),
            senderMessageIdSufixes = Seq(),
            prospectEmailsPastWeek = prospectEmailsPastWeek,
            Logger: SRLogger
          )
        }

        // 13. Convert EmailMessageTrackedV4 to EmailMessageTracked
        emailMsgs <- Future {
          filteredReply.map(msg => {
            convertEmailMessageTrackedv4ToEmailMessageTracked(emailMessageTrackedV4 = msg)
          })
        }

        // 14. Save filtered reply - here it will mark the prospect as completed for campaign 1
        handleReply: DBEmailMessagesSavedResponse <- Future {
          emailReplyTrackingModelV2.saveEmailsAndRepliesFromInboxV3(
            accountId = accountId.id,
            team_id = teamId.id,
            emailMessages = Seq(emailMsgs.head),
            inboxEmailSetting = emailSetting1,
            replyHandling = ReplyHandling.PAUSE_SPECIFIC_CAMPAIGN_ON_REPLY,
            account = initialData.account,
            senderEmails = Seq(emailSetting1.email),
            adminReplyFromSRInbox = false,
            auditRequestLogId = "",
            markProspectAsCompleted = true
          ).get
        }

        // 15. Check which prospects are marked as completed due to reply handling for campaign 1
        campaign1ProspectsCompleted: List[CPTuple] <- Future {
          CampaignProspectTestDAO.getCamapignProspectCompletedBecauseReplyHandling(
            teamId = teamId,
            campaign_id = CampaignId(campaign1Data.createCampaign.id),
            replyHandling = ReplyHandling.PAUSE_SPECIFIC_CAMPAIGN_ON_REPLY
          ).get
        }

        // 16. Check which prospects are marked as completed due to reply handling for campaign 2
        campaign2ProspectsCompleted: List[CPTuple] <- Future {
          CampaignProspectTestDAO.getCamapignProspectCompletedBecauseReplyHandling(
            teamId = teamId,
            campaign_id = CampaignId(campaign2Data.createCampaign.id),
            replyHandling = ReplyHandling.PAUSE_SPECIFIC_CAMPAIGN_ON_REPLY
          ).get
        }

        // 17. Check which prospect emails are marked as replied in campaign 1
        repliedCamapignProspect1: List[CPTuple] <- Future {
          CampaignProspectTestDAO.getRepliedCamapignProspect(
            teamId = teamId,
            campaign_id = CampaignId(campaign1Data.createCampaign.id)
          ).get
        }

      } yield {
        EmailSchedulingAndReplyTrackingSpecResultV2(
          replyInternalTrackingNote = emailMsgs.head.internal_tracking_note,
          campaignId1 = CampaignId(campaign1Data.createCampaign.id),
          campaignId2 = CampaignId(campaign2Data.createCampaign.id),
          prospectId = ProspectId(prospect.id),
          prospectsCompletedInCampaign = campaign1ProspectsCompleted ++ campaign2ProspectsCompleted,
          repliedCamapignProspect = repliedCamapignProspect1
        )
      }

      scheduleTaskData.map(p => {
        assert(
          // Should be a direct reply to campaign email
          p.replyInternalTrackingNote == InternalTrackingNote.EXISTING_INREPLYTO &&
          // Should have 1 completed prospect in campaign 1
          p.prospectsCompletedInCampaign.filter(_.campaign_id == p.campaignId1.id).head.prospect_id == p.prospectId.id &&
          // Campaign 2 should have no completed prospects
          !p.prospectsCompletedInCampaign.exists(_.campaign_id == p.campaignId2.id) &&
          // Replied prospect email should be in campaign 1's replied list
          p.repliedCamapignProspect.exists(_.campaign_id == p.campaignId1.id)
        )
      }).recover({ case e =>
        println(LogHelpers.getStackTraceAsString(e))
        assert(false)
      })
    }

    it("Campaign email sent from two email accounts to one prospect and prospect replied to one campaign and both gets marked as completed because of PAUSE_ALL_PROSPECT_CAMPAIGNS_ON_REPLY") {
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
      val account: Account = initialData.account
      val emailSetting1: EmailSetting = initialData.emailSetting.get
      val accountId: AccountId = AccountId(account.internal_id)
      val teamId: TeamId = TeamId(account.teams.head.team_id)
      val emailSettingId1 = EmailSettingId(emailSetting1.id.get.emailSettingId)

      // Create a second email setting for the same team
      val emailSetting2: EmailSetting = EmailSettingFixtureForIntegrationTest.createEmailSetting(
        accountId = AccountId(account.internal_id),
        orgId = OrgId(account.org.id),
        teamId = TeamId(account.teams.head.team_id),
        taId = account.teams.head.access_members.head.ta_id
      ).get

      val emailSettingId2 = EmailSettingId(emailSetting2.id.get.emailSettingId)

      val emailReplyStatus: EmailReplyStatus = EmailReplyStatus(
        replyType = EmailReplyType.NOT_CATEGORIZED,
        isReplied = true,
        isUnsubscribeRequest = false,
        isAutoReply = false,
        isOutOfOfficeReply = false,
        isInvalidEmail = false,
        isForwarded = false,
        bouncedData = None
      )

      // Create a single prospect
      val prospectEmail = "<EMAIL>"
      val prospects: Seq[ProspectCreateFormData] = Seq(
        createProspectData1.copy(email = Some(prospectEmail))
      )

      val scheduleTaskData: Future[EmailSchedulingAndReplyTrackingSpecResultV2] = for {
        // 1. Create and start first campaign using emailSetting1
        campaign1Data: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          prospects = Some(prospects),
          emailSettings = Some(emailSetting1)
        )

        // 2. Create and start second campaign using emailSetting2 with the same prospect
        campaign2Data: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          prospects = Some(prospects),
          emailSettings = Some(emailSetting2)
        )

        // 3. Get the prospect from first campaign
        prospect: ProspectObject <- Future {
          campaign1Data.addProspect.head
        }

        // 4. Email scheduling for campaign 1
        addingEmailScheduled1 <- Future.fromTry {
          emailScheduledDAOService.saveEmailsToBeScheduledAndUpdateCampaignDataV2(
            emailsToBeScheduled = Vector(EmailScheduledNewFixture.generateEmailScheduledNew3.copy(
              campaign_id = Some(campaign1Data.createCampaign.id),
              step_id = campaign1Data.createCampaign.head_step_id,
              from_email = campaign1Data.createCampaign.settings.campaign_email_settings.head.sender_email,
              scheduled_from_campaign = true,
              is_opening_step = true,
              sender_email_settings_id = campaign1Data.createCampaign.settings.campaign_email_settings.head.sender_email_setting_id.emailSettingId,
              team_id = campaign1Data.createCampaign.team_id,
              account_id = campaign1Data.createCampaign.owner_id,
              receiver_email_settings_id = campaign1Data.createCampaign.settings.campaign_email_settings.head.receiver_email_setting_id.emailSettingId,
              campaign_email_settings_id = campaign1Data.createCampaign.settings.campaign_email_settings.head.id,
              prospect_id = Some(prospect.id),
              base_body = "body from campaign 1",
              prospect_account_id = Some(prospect.internal.prospect_account_id.get),
              to_email = prospect.email.get
            )),
            campaign_email_setting_id = campaign1Data.createCampaign.settings.campaign_email_settings.head.id,
            emailSendingFlow = None,
            Logger = Logger
          )
        }

        // 5. Email scheduling for campaign 2
        addingEmailScheduled2 <- Future.fromTry {
          emailScheduledDAOService.saveEmailsToBeScheduledAndUpdateCampaignDataV2(
            emailsToBeScheduled = Vector(EmailScheduledNewFixture.generateEmailScheduledNew3.copy(
              campaign_id = Some(campaign2Data.createCampaign.id),
              step_id = campaign2Data.createCampaign.head_step_id,
              from_email = campaign2Data.createCampaign.settings.campaign_email_settings.head.sender_email,
              scheduled_from_campaign = true,
              is_opening_step = true,
              sender_email_settings_id = campaign2Data.createCampaign.settings.campaign_email_settings.head.sender_email_setting_id.emailSettingId,
              team_id = campaign2Data.createCampaign.team_id,
              account_id = campaign2Data.createCampaign.owner_id,
              receiver_email_settings_id = campaign2Data.createCampaign.settings.campaign_email_settings.head.receiver_email_setting_id.emailSettingId,
              campaign_email_settings_id = campaign2Data.createCampaign.settings.campaign_email_settings.head.id,
              prospect_id = Some(prospect.id),
              base_body = "body from campaign 2",
              prospect_account_id = Some(prospect.internal.prospect_account_id.get),
              to_email = prospect.email.get
            )),
            campaign_email_setting_id = campaign2Data.createCampaign.settings.campaign_email_settings.head.id,
            emailSendingFlow = None,
            Logger = Logger
          )
        }

        // 6. Add auto email step to campaign 1
        addStep1: CampaignStepVariant <- {
          CreateStepForCampaignFixture.createAutoEmailStepForCampaign(
            orgId = emailSetting1.org_id,
            teamId = teamId,
            accountId = accountId,
            taId = Helpers.getTeamMemberFromAccount(account = account, team_id = teamId).ta_id,
            campaignId = CampaignId(campaign1Data.createCampaign.id),
          )
        }

        // 7. Add auto email step to campaign 2
        addStep2: CampaignStepVariant <- {
          CreateStepForCampaignFixture.createAutoEmailStepForCampaign(
            orgId = emailSetting2.org_id,
            teamId = teamId,
            accountId = accountId,
            taId = Helpers.getTeamMemberFromAccount(account = account, team_id = teamId).ta_id,
            campaignId = CampaignId(campaign2Data.createCampaign.id),
          )
        }

        // 8. Mark campaign 1 email as sent
        emailSent1 <- Future.fromTry {
          emailSenderService.onEmailSent(
            emailSentId = addingEmailScheduled1.head.email_scheduled_id,
            data = DefaultParametersFixtureForInitializingDataForReScheduling.defaultEmailToBeSent(
              emailSetting = emailSetting1,
              campaign = campaign1Data.campaign
            ).copy(
              to_emails = Seq(IEmailAddress(email = prospect.email.get)), subject = "subject from campaign 1"),
            accountId = campaign1Data.createCampaign.owner_id,
            sendEmailFromCampaignDetails = Some(SendEmailFromCampaignDetails(
              campaign_id = campaign1Data.createCampaign.id,
              campaign_name = campaign1Data.createCampaign.name,
              stepDetails = Some(StepDetails(
                step_id = addStep1.step_id,
                step_name = addStep1.label.getOrElse("step"),
                step_type = addStep1.step_data.step_type
              ))
            )),
            prospectIdInCampaign = Some(prospect.id),
            currentBillingCycleStartedAt = initialData.account.created_at,
            orgId = initialData.account.org.id,
            repTrackingHostId = 1,
            teamId = initialData.head_team_id
          )
        }

        // 9. Mark campaign 2 email as sent
        emailSent2 <- Future.fromTry {
          emailSenderService.onEmailSent(
            emailSentId = addingEmailScheduled2.head.email_scheduled_id,
            data = DefaultParametersFixtureForInitializingDataForReScheduling.defaultEmailToBeSent(
              emailSetting = emailSetting2,
              campaign = campaign2Data.campaign
            ).copy(
              to_emails = Seq(IEmailAddress(email = prospect.email.get)), subject = "subject from campaign 2"),
            accountId = campaign2Data.createCampaign.owner_id,
            sendEmailFromCampaignDetails = Some(SendEmailFromCampaignDetails(
              campaign_id = campaign2Data.createCampaign.id,
              campaign_name = campaign2Data.createCampaign.name,
              stepDetails = Some(StepDetails(
                step_id = addStep2.step_id,
                step_name = addStep2.label.getOrElse("step"),
                step_type = addStep2.step_data.step_type
              ))
            )),
            prospectIdInCampaign = Some(prospect.id),
            currentBillingCycleStartedAt = initialData.account.created_at,
            orgId = initialData.account.org.id,
            repTrackingHostId = 1,
            teamId = initialData.head_team_id
          )
        }

        // 10. Fetch prospect emails past week for first email setting only
        prospectEmailsPastWeek: Seq[EmailScheduledForCheckingReplies] <- Future {
          emailScheduledDAOService.getSentEmailsForCheckingReplies(
            senderEmailSettingsIds = Seq(emailSettingId1.emailSettingId.toInt),
            teamId = teamId.id,
            logger = Logger
          )
        }

        // 11. Generate an email reply from the prospect to emailSetting1 only
        newMsg: EmailMessageTrackedV4 <- Future {
          EmailMessageTrackedV4.GmailMessageTrackedV4(
            gmailReplyTrackedViaAPI = EmailReply.GmailReplyTrackedViaAPI(
              commonPropsEmailMessage = CommonPropsEmailMessage(
                from = IEmailAddress(
                  email = prospectEmail
                ),
                to_emails = Seq(IEmailAddress(
                  email = emailSetting1.email // Reply only to first email setting
                )),
                cc_emails = Seq(),
                reply_to = None,
                subject = "Re: subject from campaign 1",
                body = "Thank you for reaching out",
                base_body = "base",
                text_body = "text",
                message_id = "reply_message_id123",
                full_headers = Json.obj(),
                in_reply_to_header = emailSent1.message_id,
                references_header = emailSent1.message_id,
                received_at = DateTime.now(),
                recorded_at = DateTime.now(),
                original_inbox_folder = Some("inbox"),
                autogeneratedReplyFromHeader = false
              ),
              gmail_msg_id = "gmail_msg_id_reply",
              gmail_thread_id = "gmail_thread_id_reply"
            ),
            propsTrackedReply = PropsTrackedReply(
              inbox_email_setting_id = emailSettingId1,
              email_status = emailReplyStatus,
              sr_inbox_read = false,
              scheduled_manually = false,
              campaign_id = None,
              step_id = None,
              prospect_id_in_campaign = None,
              prospect_account_id_in_campaign = None,
              email_thread_id = None,
              tempThreadId = None,
              internal_tracking_note = InternalTrackingNote.NONE,
              team_id = teamId,
              account_id = accountId,
              campaign_name = None,
              step_name = None
            )
          )
        }

        // 12. Pass tracked email to filterReplies
        filteredReply <- Future.fromTry {
          replyFilteringService.filterRepliesV4(
            newMsgsWithHeaders = Seq(newMsg),
            inboxEmailSetting = emailSetting1.copy(reply_handling = ReplyHandling.PAUSE_ALL_PROSPECT_CAMPAIGNS_ON_REPLY),
            senderEmails = Seq(emailSetting1.email),
            senderMessageIdSufixes = Seq(),
            prospectEmailsPastWeek = prospectEmailsPastWeek,
            Logger: SRLogger
          )
        }

        // 13. Convert EmailMessageTrackedV4 to EmailMessageTracked
        emailMsgs <- Future {
          filteredReply.map(msg => {
            convertEmailMessageTrackedv4ToEmailMessageTracked(emailMessageTrackedV4 = msg)
          })
        }

        // 14. Save filtered reply - here it will mark the prospect as completed for campaign 1
        handleReply: DBEmailMessagesSavedResponse <- Future {
          emailReplyTrackingModelV2.saveEmailsAndRepliesFromInboxV3(
            accountId = accountId.id,
            team_id = teamId.id,
            emailMessages = Seq(emailMsgs.head),
            inboxEmailSetting = emailSetting1,
            replyHandling = ReplyHandling.PAUSE_ALL_PROSPECT_CAMPAIGNS_ON_REPLY,
            account = initialData.account,
            senderEmails = Seq(emailSetting1.email),
            adminReplyFromSRInbox = false,
            auditRequestLogId = "",
            markProspectAsCompleted = true
          ).get
        }

        // 15. Check which prospects are marked as completed due to reply handling for campaign 1
        campaign1ProspectsCompleted: List[CPTuple] <- Future {
          CampaignProspectTestDAO.getCamapignProspectCompletedBecauseReplyHandling(
            teamId = teamId,
            campaign_id = CampaignId(campaign1Data.createCampaign.id),
            replyHandling = ReplyHandling.PAUSE_ALL_PROSPECT_CAMPAIGNS_ON_REPLY
          ).get
        }

        // 16. Check which prospects are marked as completed due to reply handling for campaign 2
        campaign2ProspectsCompleted: List[CPTuple] <- Future {
          CampaignProspectTestDAO.getCamapignProspectCompletedBecauseReplyHandling(
            teamId = teamId,
            campaign_id = CampaignId(campaign2Data.createCampaign.id),
            replyHandling = ReplyHandling.PAUSE_ALL_PROSPECT_CAMPAIGNS_ON_REPLY
          ).get
        }

        // 17. Check which prospect emails are marked as replied in campaign 1
        repliedCamapignProspect1: List[CPTuple] <- Future {
          CampaignProspectTestDAO.getRepliedCamapignProspect(
            teamId = teamId,
            campaign_id = CampaignId(campaign1Data.createCampaign.id)
          ).get
        }

      } yield {
        EmailSchedulingAndReplyTrackingSpecResultV2(
          replyInternalTrackingNote = emailMsgs.head.internal_tracking_note,
          campaignId1 = CampaignId(campaign1Data.createCampaign.id),
          campaignId2 = CampaignId(campaign2Data.createCampaign.id),
          prospectId = ProspectId(prospect.id),
          prospectsCompletedInCampaign = campaign1ProspectsCompleted ++ campaign2ProspectsCompleted,
          repliedCamapignProspect = repliedCamapignProspect1
        )
      }

      scheduleTaskData.map(p => {
        assert(
          // Should be a direct reply to campaign email
          p.replyInternalTrackingNote == InternalTrackingNote.EXISTING_INREPLYTO &&
          // Should have 1 completed prospect in campaign 1
          p.prospectsCompletedInCampaign.filter(_.campaign_id == p.campaignId1.id).head.prospect_id == p.prospectId.id &&
          // Should have 1 completed prospect in campaign 2
          p.prospectsCompletedInCampaign.filter(_.campaign_id == p.campaignId2.id).head.prospect_id == p.prospectId.id &&
          // Replied prospect email should be in campaign 1's replied list
          p.repliedCamapignProspect.exists(_.campaign_id == p.campaignId1.id) &&
          // Replied prospect email should not be in campaign 2's replied list
          !p.repliedCamapignProspect.exists(_.campaign_id == p.campaignId2.id)
        )
      }).recover({ case e =>
        println(LogHelpers.getStackTraceAsString(e))
        assert(false)
      })
    }

    it("email setting first send campaign email and then sends manual email to same prospect, should mark prospect as completed in campaign but not replied - Manual email completes campaign for the prospect") {
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
      val account: Account = initialData.account
      val emailSetting1: EmailSetting = initialData.emailSetting.get
      val accountId: AccountId = AccountId(account.internal_id)
      val teamId: TeamId = TeamId(account.teams.head.team_id)
      val emailSettingId1 = EmailSettingId(emailSetting1.id.get.emailSettingId)

      val emailReplyStatus: EmailReplyStatus = EmailReplyStatus(
        replyType = EmailReplyType.NOT_CATEGORIZED,
        isReplied = true,
        isUnsubscribeRequest = false,
        isAutoReply = false,
        isOutOfOfficeReply = false,
        isInvalidEmail = false,
        isForwarded = false,
        bouncedData = None
      )

      // Create a single prospect
      val prospectEmail = "<EMAIL>"
      val prospects: Seq[ProspectCreateFormData] = Seq(
        createProspectData1.copy(email = Some(prospectEmail))
      )

      val scheduleTaskData: Future[ManualEmailMarksCampaignCompletedSpecRes] = for {
        // 1. Create and start first campaign using emailSetting1
        campaign1Data: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          prospects = Some(prospects),
          emailSettings = Some(emailSetting1)
        )

        // 2. Get the prospect from first campaign
        prospect: ProspectObject <- Future {
          campaign1Data.addProspect.head
        }

        // 3. Email scheduling for campaign 1
        addingEmailScheduled1 <- Future.fromTry {
          emailScheduledDAOService.saveEmailsToBeScheduledAndUpdateCampaignDataV2(
            emailsToBeScheduled = Vector(EmailScheduledNewFixture.generateEmailScheduledNew3.copy(
              campaign_id = Some(campaign1Data.createCampaign.id),
              step_id = campaign1Data.createCampaign.head_step_id,
              from_email = campaign1Data.createCampaign.settings.campaign_email_settings.head.sender_email,
              scheduled_from_campaign = true,
              is_opening_step = true,
              sender_email_settings_id = campaign1Data.createCampaign.settings.campaign_email_settings.head.sender_email_setting_id.emailSettingId,
              team_id = campaign1Data.createCampaign.team_id,
              account_id = campaign1Data.createCampaign.owner_id,
              receiver_email_settings_id = campaign1Data.createCampaign.settings.campaign_email_settings.head.receiver_email_setting_id.emailSettingId,
              campaign_email_settings_id = campaign1Data.createCampaign.settings.campaign_email_settings.head.id,
              prospect_id = Some(prospect.id),
              base_body = "body from campaign 1",
              prospect_account_id = Some(prospect.internal.prospect_account_id.get),
              to_email = prospect.email.get
            )),
            campaign_email_setting_id = campaign1Data.createCampaign.settings.campaign_email_settings.head.id,
            emailSendingFlow = None,
            Logger = Logger
          )
        }

        // 4. Add auto email step to campaign 1
        addStep1: CampaignStepVariant <- {
          CreateStepForCampaignFixture.createAutoEmailStepForCampaign(
            orgId = emailSetting1.org_id,
            teamId = teamId,
            accountId = accountId,
            taId = Helpers.getTeamMemberFromAccount(account = account, team_id = teamId).ta_id,
            campaignId = CampaignId(campaign1Data.createCampaign.id),
          )
        }

        // 5. Mark campaign 1 email as sent
        emailSent1 <- Future.fromTry {
          emailSenderService.onEmailSent(
            emailSentId = addingEmailScheduled1.head.email_scheduled_id,
            data = DefaultParametersFixtureForInitializingDataForReScheduling.defaultEmailToBeSent(
              emailSetting = emailSetting1,
              campaign = campaign1Data.campaign
            ).copy(
              to_emails = Seq(IEmailAddress(email = prospect.email.get)), subject = "subject from campaign 1"),
            accountId = campaign1Data.createCampaign.owner_id,
            sendEmailFromCampaignDetails = Some(SendEmailFromCampaignDetails(
              campaign_id = campaign1Data.createCampaign.id,
              campaign_name = campaign1Data.createCampaign.name,
              stepDetails = Some(StepDetails(
                step_id = addStep1.step_id,
                step_name = addStep1.label.getOrElse("step"),
                step_type = addStep1.step_data.step_type
              ))
            )),
            prospectIdInCampaign = Some(prospect.id),
            currentBillingCycleStartedAt = initialData.account.created_at,
            orgId = initialData.account.org.id,
            repTrackingHostId = 1,
            teamId = initialData.head_team_id
          )
        }

        // 6. Fetch prospect emails past week for first email setting only
        prospectEmailsPastWeek: Seq[EmailScheduledForCheckingReplies] <- Future {
          emailScheduledDAOService.getSentEmailsForCheckingReplies(
            senderEmailSettingsIds = Seq(emailSettingId1.emailSettingId.toInt),
            teamId = teamId.id,
            logger = Logger
          )
        }

        // 7. create team inbox for email setting to send manual email
        teamInbox: Option[TeamInboxDetails] <- Future.fromTry {
          TeamInboxFixtureForIntegrationTest.createTeamInbox(
            orgId = OrgId(initialData.account.org.id),
            accountId = AccountId(initialData.account.internal_id),
            emailSettingId = emailSettingId1,
            teamId = TeamId(initialData.account.teams.head.team_id),
            userRoleIds = initialData.account.teams.map(_.role.get.id).toList
          )
        }

        // 8. Generate an manual email from emailSetting1 to prospect
        newMsg: EmailMessageTrackedV4 <- Future {
          EmailMessageTrackedV4.GmailMessageTrackedV4(
            gmailReplyTrackedViaAPI = EmailReply.GmailReplyTrackedViaAPI(
              commonPropsEmailMessage = CommonPropsEmailMessage(
                from = IEmailAddress(
                  email = emailSetting1.email
                ),
                to_emails = Seq(IEmailAddress(
                  email = prospectEmail
                )),
                cc_emails = Seq(),
                reply_to = None,
                subject = "Manual email",
                body = "manually sent email",
                base_body = "base",
                text_body = "text",
                message_id = "abmessage_id123",
                full_headers = Json.obj(),
                in_reply_to_header = None,
                references_header = None,
                received_at = DateTime.now().plusDays(1),
                recorded_at = DateTime.now().plusDays(1),
                original_inbox_folder = Some("inbox"),
                autogeneratedReplyFromHeader = false
              ),
              gmail_msg_id = "gmail_msg_id_manulally_sent",
              gmail_thread_id = "gmail_thread_id_manulally_sent"
            ),
            propsTrackedReply = PropsTrackedReply(
              inbox_email_setting_id = emailSettingId1,
              email_status = emailReplyStatus,
              sr_inbox_read = false,
              scheduled_manually = false,
              campaign_id = None,
              step_id = None,
              prospect_id_in_campaign = None,
              prospect_account_id_in_campaign = None,
              email_thread_id = None,
              tempThreadId = None,
              internal_tracking_note = InternalTrackingNote.NONE,
              team_id = teamId,
              account_id = accountId,
              campaign_name = None,
              step_name = None
            )
          )
        }

        // 9. Pass tracked email to filterReplies
        filteredReply <- Future.fromTry {
          replyFilteringService.filterRepliesV4(
            newMsgsWithHeaders = Seq(newMsg),
            inboxEmailSetting = emailSetting1.copy(reply_handling = ReplyHandling.PAUSE_ALL_PROSPECT_ACCOUNT_CAMPAIGNS_ON_REPLY),
            senderEmails = Seq(emailSetting1.email),
            senderMessageIdSufixes = Seq(),
            prospectEmailsPastWeek = prospectEmailsPastWeek,
            Logger: SRLogger
          )
        }

        // 10. Convert EmailMessageTrackedV4 to EmailMessageTracked
        emailMsgs <- Future {
          filteredReply.map(msg => {
            convertEmailMessageTrackedv4ToEmailMessageTracked(emailMessageTrackedV4 = msg)
          })
        }

        // 11. Save filtered reply - here it will mark the prospect as completed for campaign 1
        handleReply: DBEmailMessagesSavedResponse <- Future {
          emailReplyTrackingModelV2.saveEmailsAndRepliesFromInboxV3(
            accountId = accountId.id,
            team_id = teamId.id,
            emailMessages = Seq(emailMsgs.head),
            inboxEmailSetting = emailSetting1,
            replyHandling = ReplyHandling.PAUSE_ALL_PROSPECT_ACCOUNT_CAMPAIGNS_ON_REPLY,
            account = initialData.account,
            senderEmails = Seq(emailSetting1.email),
            adminReplyFromSRInbox = false,
            auditRequestLogId = "",
            markProspectAsCompleted = true
          ).get
        }

        // 12. Check which prospects are marked as completed due to reply handling for campaign 1
        campaign1ProspectsCompleted: List[CPTuple] <- Future {
          CampaignProspectTestDAO.getCamapignProspectCompletedBecauseReplyHandling(
            teamId = teamId,
            campaign_id = CampaignId(campaign1Data.createCampaign.id),
            replyHandling = ReplyHandling.PAUSE_ALL_PROSPECT_ACCOUNT_CAMPAIGNS_ON_REPLY
          ).get
        }

        // 13. Check which prospect emails are marked as replied in campaign 1
        repliedCamapignProspect1: List[CPTuple] <- Future {
          CampaignProspectTestDAO.getRepliedCamapignProspect(
            teamId = teamId,
            campaign_id = CampaignId(campaign1Data.createCampaign.id)
          ).get
        }

      } yield {
        ManualEmailMarksCampaignCompletedSpecRes(
          replyInternalTrackingNote = emailMsgs.head.internal_tracking_note,
          prospectCompletedInCampaign = campaign1ProspectsCompleted,
          campaignId = CampaignId(campaign1Data.createCampaign.id),
          repliedCamapignProspect = repliedCamapignProspect1
        )
      }

      scheduleTaskData.map(p => {
        assert(p.replyInternalTrackingNote == InternalTrackingNote.NONE && // tracked as none
          p.prospectCompletedInCampaign.exists(_.campaign_id == p.campaignId.id) && // Should complete prospect in campaign 1
          !p.repliedCamapignProspect.exists(_.campaign_id == p.campaignId.id) // prospect should not be replied in campaign 1
        )
      }).recover({ case e =>
        println(LogHelpers.getStackTraceAsString(e))
        assert(false)
      })
    }

    it("cc email should not mark the campaign as completed") {
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
      val account: Account = initialData.account
      val emailSetting1: EmailSetting = initialData.emailSetting.get
      val accountId: AccountId = AccountId(account.internal_id)
      val teamId: TeamId = TeamId(account.teams.head.team_id)
      val emailSettingId1 = EmailSettingId(emailSetting1.id.get.emailSettingId)

      // Create a second email setting for the same team
      val emailSetting2: EmailSetting = EmailSettingFixtureForIntegrationTest.createEmailSetting(
        accountId = AccountId(account.internal_id),
        orgId = OrgId(account.org.id),
        teamId = TeamId(account.teams.head.team_id),
        taId = account.teams.head.access_members.head.ta_id
      ).get

      val emailSettingId2 = EmailSettingId(emailSetting2.id.get.emailSettingId)

      val emailReplyStatus: EmailReplyStatus = EmailReplyStatus(
        replyType = EmailReplyType.NOT_CATEGORIZED,
        isReplied = true,
        isUnsubscribeRequest = false,
        isAutoReply = false,
        isOutOfOfficeReply = false,
        isInvalidEmail = false,
        isForwarded = false,
        bouncedData = None
      )

      // Create a single prospect
      val prospectEmail = "<EMAIL>"
      val prospects: Seq[ProspectCreateFormData] = Seq(
        createProspectData1.copy(email = Some(prospectEmail))
      )

      val scheduleTaskData: Future[ManualEmailMarksCampaignCompletedSpecRes] = for {
        // 1. Create and start first campaign using emailSetting1
        campaign1Data: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          prospects = Some(prospects),
          emailSettings = Some(emailSetting1)
        )

        // 2. Get the prospect from first campaign
        prospect: ProspectObject <- Future {
          campaign1Data.addProspect.head
        }

        // 3. Email scheduling for campaign 1
        addingEmailScheduled1 <- Future.fromTry {
          emailScheduledDAOService.saveEmailsToBeScheduledAndUpdateCampaignDataV2(
            emailsToBeScheduled = Vector(EmailScheduledNewFixture.generateEmailScheduledNew3.copy(
              campaign_id = Some(campaign1Data.createCampaign.id),
              step_id = campaign1Data.createCampaign.head_step_id,
              from_email = campaign1Data.createCampaign.settings.campaign_email_settings.head.sender_email,
              scheduled_from_campaign = true,
              is_opening_step = true,
              sender_email_settings_id = campaign1Data.createCampaign.settings.campaign_email_settings.head.sender_email_setting_id.emailSettingId,
              team_id = campaign1Data.createCampaign.team_id,
              account_id = campaign1Data.createCampaign.owner_id,
              receiver_email_settings_id = campaign1Data.createCampaign.settings.campaign_email_settings.head.receiver_email_setting_id.emailSettingId,
              campaign_email_settings_id = campaign1Data.createCampaign.settings.campaign_email_settings.head.id,
              prospect_id = Some(prospect.id),
              base_body = "body from campaign 1",
              prospect_account_id = Some(prospect.internal.prospect_account_id.get),
              to_email = prospect.email.get
            )),
            campaign_email_setting_id = campaign1Data.createCampaign.settings.campaign_email_settings.head.id,
            emailSendingFlow = None,
            Logger = Logger
          )
        }

        // 4. Add auto email step to campaign 1
        addStep1: CampaignStepVariant <- {
          CreateStepForCampaignFixture.createAutoEmailStepForCampaign(
            orgId = emailSetting1.org_id,
            teamId = teamId,
            accountId = accountId,
            taId = Helpers.getTeamMemberFromAccount(account = account, team_id = teamId).ta_id,
            campaignId = CampaignId(campaign1Data.createCampaign.id),
          )
        }

        // 5. Mark campaign 1 email as sent
        emailSent1 <- Future.fromTry {
          emailSenderService.onEmailSent(
            emailSentId = addingEmailScheduled1.head.email_scheduled_id,
            data = DefaultParametersFixtureForInitializingDataForReScheduling.defaultEmailToBeSent(
              emailSetting = emailSetting1,
              campaign = campaign1Data.campaign
            ).copy(
              to_emails = Seq(IEmailAddress(email = prospect.email.get)), subject = "subject from campaign 1"),
            accountId = campaign1Data.createCampaign.owner_id,
            sendEmailFromCampaignDetails = Some(SendEmailFromCampaignDetails(
              campaign_id = campaign1Data.createCampaign.id,
              campaign_name = campaign1Data.createCampaign.name,
              stepDetails = Some(StepDetails(
                step_id = addStep1.step_id,
                step_name = addStep1.label.getOrElse("step"),
                step_type = addStep1.step_data.step_type
              ))
            )),
            prospectIdInCampaign = Some(prospect.id),
            currentBillingCycleStartedAt = initialData.account.created_at,
            orgId = initialData.account.org.id,
            repTrackingHostId = 1,
            teamId = initialData.head_team_id
          )
        }

        // 6. Fetch prospect emails past week for first email setting only
        prospectEmailsPastWeek: Seq[EmailScheduledForCheckingReplies] <- Future {
          emailScheduledDAOService.getSentEmailsForCheckingReplies(
            senderEmailSettingsIds = Seq(emailSettingId1.emailSettingId.toInt),
            teamId = teamId.id,
            logger = Logger
          )
        }

        // 7. create team inbox for second email setting
        teamInbox: Option[TeamInboxDetails] <- Future.fromTry {
          TeamInboxFixtureForIntegrationTest.createTeamInbox(
            orgId = OrgId(initialData.account.org.id),
            accountId = AccountId(initialData.account.internal_id),
            emailSettingId = emailSettingId2,
            teamId = TeamId(initialData.account.teams.head.team_id),
            userRoleIds = initialData.account.teams.map(_.role.get.id).toList
          )
        }

        // 8. Generate tracked email from the emailSetting1 to prospect - assume EmailSetting1 has emailsetting2 as bcc
        newMsg: EmailMessageTrackedV4 <- Future {
          EmailMessageTrackedV4.GmailMessageTrackedV4(
            gmailReplyTrackedViaAPI = EmailReply.GmailReplyTrackedViaAPI(
              commonPropsEmailMessage = CommonPropsEmailMessage(
                from = IEmailAddress(
                  email = emailSetting1.email
                ),
                to_emails = Seq(IEmailAddress(
                  email = prospectEmail
                )),
                cc_emails = Seq(IEmailAddress(
                  email = emailSetting2.email
                )),
                reply_to = None,
                subject = "Subject from campaign 1",
                body = "campaign email",
                base_body = "base",
                text_body = "text",
                message_id = "campaign_message_id123",
                full_headers = Json.obj(),
                in_reply_to_header = None,
                references_header = None,
                received_at = DateTime.now(),
                recorded_at = DateTime.now(),
                original_inbox_folder = Some("inbox"),
                autogeneratedReplyFromHeader = false
              ),
              gmail_msg_id = "gmail_msg_id_sent",
              gmail_thread_id = "gmail_thread_id_sent"
            ),
            propsTrackedReply = PropsTrackedReply(
              inbox_email_setting_id = emailSettingId2,
              email_status = emailReplyStatus,
              sr_inbox_read = false,
              scheduled_manually = false,
              campaign_id = None,
              step_id = None,
              prospect_id_in_campaign = None,
              prospect_account_id_in_campaign = None,
              email_thread_id = None,
              tempThreadId = None,
              internal_tracking_note = InternalTrackingNote.NONE,
              team_id = teamId,
              account_id = accountId,
              campaign_name = None,
              step_name = None
            )
          )
        }

        // 9. Pass tracked email to filterReplies
        filteredReply <- Future.fromTry {
          replyFilteringService.filterRepliesV4(
            newMsgsWithHeaders = Seq(newMsg),
            inboxEmailSetting = emailSetting2.copy(reply_handling = ReplyHandling.PAUSE_ALL_PROSPECT_CAMPAIGNS_ON_REPLY),
            senderEmails = Seq(emailSetting2.email),
            senderMessageIdSufixes = Seq(),
            prospectEmailsPastWeek = prospectEmailsPastWeek,
            Logger: SRLogger
          )
        }

        // 10. Convert EmailMessageTrackedV4 to EmailMessageTracked
        emailMsgs <- Future {
          filteredReply.map(msg => {
            convertEmailMessageTrackedv4ToEmailMessageTracked(emailMessageTrackedV4 = msg)
          })
        }

        // 11. Save filtered reply - here it will mark the prospect as completed for campaign 1
        handleReply: DBEmailMessagesSavedResponse <- Future {
          emailReplyTrackingModelV2.saveEmailsAndRepliesFromInboxV3(
            accountId = accountId.id,
            team_id = teamId.id,
            emailMessages = Seq(emailMsgs.head),
            inboxEmailSetting = emailSetting1,
            replyHandling = ReplyHandling.PAUSE_ALL_PROSPECT_CAMPAIGNS_ON_REPLY,
            account = initialData.account,
            senderEmails = Seq(emailSetting1.email),
            adminReplyFromSRInbox = false,
            auditRequestLogId = "",
            markProspectAsCompleted = true
          ).get
        }

        // 12. Check which prospects are marked as completed due to reply handling for campaign 1
        campaign1ProspectsCompleted: List[CPTuple] <- Future {
          CampaignProspectTestDAO.getCamapignProspectCompletedBecauseReplyHandling(
            teamId = teamId,
            campaign_id = CampaignId(campaign1Data.createCampaign.id),
            replyHandling = ReplyHandling.PAUSE_ALL_PROSPECT_CAMPAIGNS_ON_REPLY
          ).get
        }

        // 13. Check which prospect emails are marked as replied in campaign 1
        repliedProspectEmailsFromCampaign1: List[CPTuple] <- Future {
          CampaignProspectTestDAO.getRepliedCamapignProspect(
            teamId = teamId,
            campaign_id = CampaignId(campaign1Data.createCampaign.id)
          ).get
        }

      } yield {
        ManualEmailMarksCampaignCompletedSpecRes(
          replyInternalTrackingNote = emailMsgs.head.internal_tracking_note,
          prospectCompletedInCampaign = campaign1ProspectsCompleted,
          campaignId = CampaignId(campaign1Data.createCampaign.id),
          repliedCamapignProspect = repliedProspectEmailsFromCampaign1
        )
      }

      scheduleTaskData.map(p => {
        assert(p.replyInternalTrackingNote == InternalTrackingNote.SHOULD_BE_CC_EMAIL &&
          p.prospectCompletedInCampaign.isEmpty && // Should not have completed prospect in campaign 1
          p.repliedCamapignProspect.isEmpty // Should not have replied prospect in campaign 1
        )
      }).recover({ case e =>
        println(LogHelpers.getStackTraceAsString(e))
        assert(false)
      })
    }


    it("bcc email should not mark the campaign as completed if one email setting is in bcc of another eset of team") {
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
      val account: Account = initialData.account
      val emailSetting1: EmailSetting = initialData.emailSetting.get
      val accountId: AccountId = AccountId(account.internal_id)
      val teamId: TeamId = TeamId(account.teams.head.team_id)
      val emailSettingId1 = EmailSettingId(emailSetting1.id.get.emailSettingId)

      // Create a second email setting for the same team
      val emailSetting2: EmailSetting = EmailSettingFixtureForIntegrationTest.createEmailSetting(
        accountId = AccountId(account.internal_id),
        orgId = OrgId(account.org.id),
        teamId = TeamId(account.teams.head.team_id),
        taId = account.teams.head.access_members.head.ta_id
      ).get

      val emailSettingId2 = EmailSettingId(emailSetting2.id.get.emailSettingId)

      val emailReplyStatus: EmailReplyStatus = EmailReplyStatus(
        replyType = EmailReplyType.NOT_CATEGORIZED,
        isReplied = true,
        isUnsubscribeRequest = false,
        isAutoReply = false,
        isOutOfOfficeReply = false,
        isInvalidEmail = false,
        isForwarded = false,
        bouncedData = None
      )

      // Create a single prospect
      val prospectEmail = "<EMAIL>"
      val prospects: Seq[ProspectCreateFormData] = Seq(
        createProspectData1.copy(email = Some(prospectEmail))
      )

      val scheduleTaskData: Future[ManualEmailMarksCampaignCompletedSpecRes] = for {
        // 1. Create and start first campaign using emailSetting1
        campaign1Data: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          prospects = Some(prospects),
          emailSettings = Some(emailSetting1)
        )

        // 2. Get the prospect from first campaign
        prospect: ProspectObject <- Future {
          campaign1Data.addProspect.head
        }

        // 3. Email scheduling for campaign 1
        addingEmailScheduled1 <- Future.fromTry {
          emailScheduledDAOService.saveEmailsToBeScheduledAndUpdateCampaignDataV2(
            emailsToBeScheduled = Vector(EmailScheduledNewFixture.generateEmailScheduledNew3.copy(
              campaign_id = Some(campaign1Data.createCampaign.id),
              step_id = campaign1Data.createCampaign.head_step_id,
              from_email = campaign1Data.createCampaign.settings.campaign_email_settings.head.sender_email,
              scheduled_from_campaign = true,
              is_opening_step = true,
              sender_email_settings_id = campaign1Data.createCampaign.settings.campaign_email_settings.head.sender_email_setting_id.emailSettingId,
              team_id = campaign1Data.createCampaign.team_id,
              account_id = campaign1Data.createCampaign.owner_id,
              receiver_email_settings_id = campaign1Data.createCampaign.settings.campaign_email_settings.head.receiver_email_setting_id.emailSettingId,
              campaign_email_settings_id = campaign1Data.createCampaign.settings.campaign_email_settings.head.id,
              prospect_id = Some(prospect.id),
              base_body = "body from campaign 1",
              prospect_account_id = Some(prospect.internal.prospect_account_id.get),
              to_email = prospect.email.get
            )),
            campaign_email_setting_id = campaign1Data.createCampaign.settings.campaign_email_settings.head.id,
            emailSendingFlow = None,
            Logger = Logger
          )
        }

        // 4. Add auto email step to campaign 1
        addStep1: CampaignStepVariant <- {
          CreateStepForCampaignFixture.createAutoEmailStepForCampaign(
            orgId = emailSetting1.org_id,
            teamId = teamId,
            accountId = accountId,
            taId = Helpers.getTeamMemberFromAccount(account = account, team_id = teamId).ta_id,
            campaignId = CampaignId(campaign1Data.createCampaign.id),
          )
        }

        // 5. Mark campaign 1 email as sent
        emailSent1 <- Future.fromTry {
          emailSenderService.onEmailSent(
            emailSentId = addingEmailScheduled1.head.email_scheduled_id,
            data = DefaultParametersFixtureForInitializingDataForReScheduling.defaultEmailToBeSent(
              emailSetting = emailSetting1,
              campaign = campaign1Data.campaign
            ).copy(
              to_emails = Seq(IEmailAddress(email = prospect.email.get)), subject = "subject from campaign 1"),
            accountId = campaign1Data.createCampaign.owner_id,
            sendEmailFromCampaignDetails = Some(SendEmailFromCampaignDetails(
              campaign_id = campaign1Data.createCampaign.id,
              campaign_name = campaign1Data.createCampaign.name,
              stepDetails = Some(StepDetails(
                step_id = addStep1.step_id,
                step_name = addStep1.label.getOrElse("step"),
                step_type = addStep1.step_data.step_type
              ))
            )),
            prospectIdInCampaign = Some(prospect.id),
            currentBillingCycleStartedAt = initialData.account.created_at,
            orgId = initialData.account.org.id,
            repTrackingHostId = 1,
            teamId = initialData.emailSetting.get.team_id.id
          )
        }

        // 6. Fetch prospect emails past week for first email setting only
        prospectEmailsPastWeek: Seq[EmailScheduledForCheckingReplies] <- Future {
          emailScheduledDAOService.getSentEmailsForCheckingReplies(
            senderEmailSettingsIds = Seq(emailSettingId1.emailSettingId.toInt),
            teamId = teamId.id,
            logger = Logger
          )
        }

        // 7. create team inbox for second email setting
        teamInbox: Option[TeamInboxDetails] <- Future.fromTry {
          TeamInboxFixtureForIntegrationTest.createTeamInbox(
            orgId = OrgId(initialData.account.org.id),
            accountId = AccountId(initialData.account.internal_id),
            emailSettingId = emailSettingId2,
            teamId = TeamId(initialData.account.teams.head.team_id),
            userRoleIds = initialData.account.teams.map(_.role.get.id).toList
          )
        }

        // 8. Generate tracked email from the emailSetting1 to prospect - assume EmailSetting1 has emailsetting2 as bcc
        newMsg: EmailMessageTrackedV4 <- Future {
          EmailMessageTrackedV4.GmailMessageTrackedV4(
            gmailReplyTrackedViaAPI = EmailReply.GmailReplyTrackedViaAPI(
              commonPropsEmailMessage = CommonPropsEmailMessage(
                from = IEmailAddress(
                  email = emailSetting1.email
                ),
                to_emails = Seq(IEmailAddress(
                  email = prospectEmail
                )),
                cc_emails = Seq(),
                reply_to = None,
                subject = "Subject from campaign 1",
                body = "campaign email",
                base_body = "base",
                text_body = "text",
                message_id = "campaign_message_id123",
                full_headers = Json.obj(),
                in_reply_to_header = None,
                references_header = None,
                received_at = DateTime.now(),
                recorded_at = DateTime.now(),
                original_inbox_folder = Some("inbox"),
                autogeneratedReplyFromHeader = false
              ),
              gmail_msg_id = "gmail_msg_id_sent",
              gmail_thread_id = "gmail_thread_id_sent"
            ),
            propsTrackedReply = PropsTrackedReply(
              inbox_email_setting_id = emailSettingId2,
              email_status = emailReplyStatus,
              sr_inbox_read = false,
              scheduled_manually = false,
              campaign_id = None,
              step_id = None,
              prospect_id_in_campaign = None,
              prospect_account_id_in_campaign = None,
              email_thread_id = None,
              tempThreadId = None,
              internal_tracking_note = InternalTrackingNote.NONE,
              team_id = teamId,
              account_id = accountId,
              campaign_name = None,
              step_name = None
            )
          )
        }

        // 9. Pass tracked email to filterReplies
        filteredReply <- Future.fromTry {
          replyFilteringService.filterRepliesV4(
            newMsgsWithHeaders = Seq(newMsg),
            inboxEmailSetting = emailSetting2.copy(reply_handling = ReplyHandling.PAUSE_ALL_PROSPECT_CAMPAIGNS_ON_REPLY),
            senderEmails = Seq(emailSetting2.email),
            senderMessageIdSufixes = Seq(),
            prospectEmailsPastWeek = prospectEmailsPastWeek,
            Logger: SRLogger
          )
        }

        // 10. Convert EmailMessageTrackedV4 to EmailMessageTracked
        emailMsgs <- Future {
          filteredReply.map(msg => {
            convertEmailMessageTrackedv4ToEmailMessageTracked(emailMessageTrackedV4 = msg)
          })
        }

        // 11. Save filtered reply - here it will mark the prospect as completed for campaign 1
        handleReply: DBEmailMessagesSavedResponse <- Future {
          emailReplyTrackingModelV2.saveEmailsAndRepliesFromInboxV3(
            accountId = accountId.id,
            team_id = teamId.id,
            emailMessages = Seq(emailMsgs.head),
            inboxEmailSetting = emailSetting1,
            replyHandling = ReplyHandling.PAUSE_ALL_PROSPECT_CAMPAIGNS_ON_REPLY,
            account = initialData.account,
            senderEmails = Seq(emailSetting1.email),
            adminReplyFromSRInbox = false,
            auditRequestLogId = "",
            markProspectAsCompleted = true
          ).get
        }

        // 12. Check which prospects are marked as completed due to reply handling for campaign 1
        campaign1ProspectsCompleted: List[CPTuple] <- Future {
          CampaignProspectTestDAO.getCamapignProspectCompletedBecauseReplyHandling(
            teamId = teamId,
            campaign_id = CampaignId(campaign1Data.createCampaign.id),
            replyHandling = ReplyHandling.PAUSE_ALL_PROSPECT_CAMPAIGNS_ON_REPLY
          ).get
        }

        // 13. Check which prospect emails are marked as replied in campaign 1
        repliedProspectEmailsFromCampaign1: List[CPTuple] <- Future {
          CampaignProspectTestDAO.getRepliedCamapignProspect(
            teamId = teamId,
            campaign_id = CampaignId(campaign1Data.createCampaign.id)
          ).get
        }

      } yield {
        ManualEmailMarksCampaignCompletedSpecRes(
          replyInternalTrackingNote = emailMsgs.head.internal_tracking_note,
          prospectCompletedInCampaign = campaign1ProspectsCompleted,
          campaignId = CampaignId(campaign1Data.createCampaign.id),
          repliedCamapignProspect = repliedProspectEmailsFromCampaign1
        )
      }

      scheduleTaskData.map(p => {
        assert(p.replyInternalTrackingNote == InternalTrackingNote.SHOULD_BE_BCC_EMAIL &&
          p.prospectCompletedInCampaign.isEmpty && // Should not have completed prospect in campaign 1
          p.repliedCamapignProspect.isEmpty // Should not have replied prospect in campaign 1
        )
      }).recover({ case e =>
        println(LogHelpers.getStackTraceAsString(e))
        assert(false)
      })
    }


    //a. neil sends email to Prachi via campaign and puts himself in CC - the campaign will pause due to same gmailThreadId

    it("email setting sends email to prospect via campaign and puts himself in CC - the campaign will pause due to same gmailThreadId") {
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
      val account: Account = initialData.account
      val emailSetting1: EmailSetting = initialData.emailSetting.get
      val accountId: AccountId = AccountId(account.internal_id)
      val teamId: TeamId = TeamId(account.teams.head.team_id)
      val emailSettingId1 = EmailSettingId(emailSetting1.id.get.emailSettingId)

      val emailReplyStatus: EmailReplyStatus = EmailReplyStatus(
        replyType = EmailReplyType.NOT_CATEGORIZED,
        isReplied = true,
        isUnsubscribeRequest = false,
        isAutoReply = false,
        isOutOfOfficeReply = false,
        isInvalidEmail = false,
        isForwarded = false,
        bouncedData = None
      )

      // Create a single prospect
      val prospectEmail = "<EMAIL>"
      val prospects: Seq[ProspectCreateFormData] = Seq(
        createProspectData1.copy(email = Some(prospectEmail))
      )

      val scheduleTaskData: Future[ManualEmailMarksCampaignCompletedSpecRes] = for {
        // 1. Create and start first campaign using emailSetting1
        campaign1Data: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          prospects = Some(prospects),
          emailSettings = Some(emailSetting1)
        )

        // 2. Get the prospect from first campaign
        prospect: ProspectObject <- Future {
          campaign1Data.addProspect.head
        }

        // 3. Email scheduling for campaign 1
        addingEmailScheduled1 <- Future.fromTry {
          emailScheduledDAOService.saveEmailsToBeScheduledAndUpdateCampaignDataV2(
            emailsToBeScheduled = Vector(EmailScheduledNewFixture.generateEmailScheduledNew3.copy(
              campaign_id = Some(campaign1Data.createCampaign.id),
              step_id = campaign1Data.createCampaign.head_step_id,
              from_email = campaign1Data.createCampaign.settings.campaign_email_settings.head.sender_email,
              scheduled_from_campaign = true,
              is_opening_step = true,
              sender_email_settings_id = campaign1Data.createCampaign.settings.campaign_email_settings.head.sender_email_setting_id.emailSettingId,
              team_id = campaign1Data.createCampaign.team_id,
              account_id = campaign1Data.createCampaign.owner_id,
              receiver_email_settings_id = campaign1Data.createCampaign.settings.campaign_email_settings.head.receiver_email_setting_id.emailSettingId,
              campaign_email_settings_id = campaign1Data.createCampaign.settings.campaign_email_settings.head.id,
              prospect_id = Some(prospect.id),
              base_body = "body from campaign 1",
              prospect_account_id = Some(prospect.internal.prospect_account_id.get),
              to_email = prospect.email.get
            )),
            campaign_email_setting_id = campaign1Data.createCampaign.settings.campaign_email_settings.head.id,
            emailSendingFlow = None,
            Logger = Logger
          )
        }

        // 4. Add auto email step to campaign 1
        addStep1: CampaignStepVariant <- {
          CreateStepForCampaignFixture.createAutoEmailStepForCampaign(
            orgId = emailSetting1.org_id,
            teamId = teamId,
            accountId = accountId,
            taId = Helpers.getTeamMemberFromAccount(account = account, team_id = teamId).ta_id,
            campaignId = CampaignId(campaign1Data.createCampaign.id),
          )
        }

        // 5. Mark campaign 1 email as sent
        emailSent1 <- Future.fromTry {
          emailSenderService.onEmailSent(
            emailSentId = addingEmailScheduled1.head.email_scheduled_id,
            data = DefaultParametersFixtureForInitializingDataForReScheduling.defaultEmailToBeSent(
              emailSetting = emailSetting1,
              campaign = campaign1Data.campaign
            ).copy(
              to_emails = Seq(IEmailAddress(email = prospect.email.get)), subject = "subject from campaign 1", gmail_thread_id = Some("gmail_thread_id_same")),
            accountId = campaign1Data.createCampaign.owner_id,
            sendEmailFromCampaignDetails = Some(SendEmailFromCampaignDetails(
              campaign_id = campaign1Data.createCampaign.id,
              campaign_name = campaign1Data.createCampaign.name,
              stepDetails = Some(StepDetails(
                step_id = addStep1.step_id,
                step_name = addStep1.label.getOrElse("step"),
                step_type = addStep1.step_data.step_type
              ))
            )),
            prospectIdInCampaign = Some(prospect.id),
            currentBillingCycleStartedAt = initialData.account.created_at,
            orgId = initialData.account.org.id,
            repTrackingHostId = 1,
            teamId = initialData.emailSetting.get.team_id.id
          )
        }

        // 6. Fetch prospect emails past week for first email setting only
        prospectEmailsPastWeek: Seq[EmailScheduledForCheckingReplies] <- Future {
          emailScheduledDAOService.getSentEmailsForCheckingReplies(
            senderEmailSettingsIds = Seq(emailSettingId1.emailSettingId.toInt),
            teamId = teamId.id,
            logger = Logger
          )
        }

        // 7. create team inbox for second email setting
        teamInbox: Option[TeamInboxDetails] <- Future.fromTry {
          TeamInboxFixtureForIntegrationTest.createTeamInbox(
            orgId = OrgId(initialData.account.org.id),
            accountId = AccountId(initialData.account.internal_id),
            emailSettingId = emailSettingId1,
            teamId = TeamId(initialData.account.teams.head.team_id),
            userRoleIds = initialData.account.teams.map(_.role.get.id).toList
          )
        }

        // 8. Generate tracked email from the emailSetting1 to prospect - assume EmailSetting1 has emailsetting2 as bcc
        newMsg: EmailMessageTrackedV4 <- Future {
          EmailMessageTrackedV4.GmailMessageTrackedV4(
            gmailReplyTrackedViaAPI = EmailReply.GmailReplyTrackedViaAPI(
              commonPropsEmailMessage = CommonPropsEmailMessage(
                from = IEmailAddress(
                  email = emailSetting1.email
                ),
                to_emails = Seq(IEmailAddress(
                  email = prospectEmail
                )),
                cc_emails = Seq(IEmailAddress(
                  email = emailSetting1.email
                )),
                reply_to = None,
                subject = "Subject from campaign 1",
                body = "campaign email",
                base_body = "base",
                text_body = "text",
                message_id = "campaign_message_id123",
                full_headers = Json.obj(),
                in_reply_to_header = None,
                references_header = None,
                received_at = DateTime.now(),
                recorded_at = DateTime.now(),
                original_inbox_folder = Some("inbox"),
                autogeneratedReplyFromHeader = false
              ),
              gmail_msg_id = "gmail_msg_id_sent",
              gmail_thread_id = "gmail_thread_id_same"
            ),
            propsTrackedReply = PropsTrackedReply(
              inbox_email_setting_id = emailSettingId1,
              email_status = emailReplyStatus,
              sr_inbox_read = false,
              scheduled_manually = false,
              campaign_id = None,
              step_id = None,
              prospect_id_in_campaign = None,
              prospect_account_id_in_campaign = None,
              email_thread_id = None,
              tempThreadId = None,
              internal_tracking_note = InternalTrackingNote.NONE,
              team_id = teamId,
              account_id = accountId,
              campaign_name = None,
              step_name = None
            )
          )
        }

        // 9. Pass tracked email to filterReplies
        filteredReply <- Future.fromTry {
          replyFilteringService.filterRepliesV4(
            newMsgsWithHeaders = Seq(newMsg),
            inboxEmailSetting = emailSetting1.copy(reply_handling = ReplyHandling.PAUSE_ALL_PROSPECT_CAMPAIGNS_ON_REPLY),
            senderEmails = Seq(emailSetting1.email),
            senderMessageIdSufixes = Seq(),
            prospectEmailsPastWeek = prospectEmailsPastWeek,
            Logger: SRLogger
          )
        }

        // 10. Convert EmailMessageTrackedV4 to EmailMessageTracked
        emailMsgs <- Future {
          filteredReply.map(msg => {
            convertEmailMessageTrackedv4ToEmailMessageTracked(emailMessageTrackedV4 = msg)
          })
        }

        // 11. Save filtered reply - here it will mark the prospect as completed for campaign 1
        handleReply: DBEmailMessagesSavedResponse <- Future {
          emailReplyTrackingModelV2.saveEmailsAndRepliesFromInboxV3(
            accountId = accountId.id,
            team_id = teamId.id,
            emailMessages = Seq(emailMsgs.head),
            inboxEmailSetting = emailSetting1,
            replyHandling = ReplyHandling.PAUSE_ALL_PROSPECT_CAMPAIGNS_ON_REPLY,
            account = initialData.account,
            senderEmails = Seq(emailSetting1.email),
            adminReplyFromSRInbox = false,
            auditRequestLogId = "",
            markProspectAsCompleted = true
          ).get
        }

        // 12. Check which prospects are marked as completed due to reply handling for campaign 1
        campaign1ProspectsCompleted: List[CPTuple] <- Future {
          CampaignProspectTestDAO.getCamapignProspectCompletedBecauseReplyHandling(
            teamId = teamId,
            campaign_id = CampaignId(campaign1Data.createCampaign.id),
            replyHandling = ReplyHandling.PAUSE_ALL_PROSPECT_CAMPAIGNS_ON_REPLY
          ).get
        }

        // 13. Check which prospect emails are marked as replied in campaign 1
        repliedProspectEmailsFromCampaign1: List[CPTuple] <- Future {
          CampaignProspectTestDAO.getRepliedCamapignProspect(
            teamId = teamId,
            campaign_id = CampaignId(campaign1Data.createCampaign.id)
          ).get
        }

      } yield {
        ManualEmailMarksCampaignCompletedSpecRes(
          replyInternalTrackingNote = emailMsgs.head.internal_tracking_note,
          prospectCompletedInCampaign = campaign1ProspectsCompleted,
          campaignId = CampaignId(campaign1Data.createCampaign.id),
          repliedCamapignProspect = repliedProspectEmailsFromCampaign1
        )
      }

      scheduleTaskData.map(p => {
        assert(p.replyInternalTrackingNote == InternalTrackingNote.EXISTING_GMAILTHREADID_IN_EMAILSSCHEDULED_TABLE &&
          p.prospectCompletedInCampaign.nonEmpty && // Shouldhave completed prospect in campaign 1
          p.repliedCamapignProspect.nonEmpty // Should have replied prospect in campaign 1
        )
      }).recover({ case e =>
        println(LogHelpers.getStackTraceAsString(e))
        assert(false)
      })
    }

    //b. neil sends email to Prachi via campaign and puts himself in To - the campaign should not pause
      //We cannot set own email in to for the campaign
    //c. neil sends email to Prachi via campaign and puts himself in BCC - the campaign should not pause
      //We are not reading bcc emails for message even though it is same inbox
  }
}
