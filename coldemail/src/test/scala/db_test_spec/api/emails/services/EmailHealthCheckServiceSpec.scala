package db_test_spec.api.emails.services

import api.AppConfig
import api.accounts.TeamId
import api.emails.EmailMessageTracked
import api.emails.daos.{EmailHealthCheckDAO, EmailHealthCheckRecord}
import api.emails.models.EmailHealthCheckStatus.{Failed, Pending}
import api.emails.models.{EmailAuthStatus, EmailHealthCheckRecordId, EmailHealthCheckStatus, EmailReplyType}
import api.emails.services.{EmailAuthStatusDetails, EmailHealthCheckDetails, EmailHealthCheckService, EmailHealthDnsCheckService}
import api.prospects.models.ProspectCategory
import db_test_spec.api.accounts.fixtures.NewAccountAndEmailSettingData
import db_test_spec.api.{DbTestingBeforeAllAndAfterAll, InitialData}
import io.smartreach.esp.api.emails.{EmailSettingId, IEmailAddress}
import org.joda.time.DateTime
import org.scalamock.scalatest.AsyncMockFactory
import play.api.libs.json.Json
import utils.{Help<PERSON>, PusherService}
import utils.email.{EmailReplyStatus, EmailSenderService, EmailService}
import utils.email.services.{EmailReplyTrackingService, InternalTrackingNote}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}


class EmailHealthCheckServiceSpec extends DbTestingBeforeAllAndAfterAll with AsyncMockFactory {

  describe("Test getAllExistingHealthCheckRecords") {

    it(
      "should return empty list if no health check records are available"
    ) {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val team = initialData.account.teams.head

      val teamId = TeamId(id = team.team_id)

      emailHealthCheckService.getAllExistingHealthCheckRecords(
        teamId = teamId,
      ) match {
        case Failure(exception) =>

          println(
            s"Failed to fetch all email health check records for teamId: $teamId: $exception"
          )

          assert(false)

        case Success(ehcList) =>

          assert(ehcList.isEmpty)

      }

    }

    it(
      "should should fetch all the available health check records"
    ) {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val team = initialData.account.teams.head

      val teamId = TeamId(id = team.team_id)

      val emailSettings = emailSettingDAO.findAll(
        accountIds = team.all_members.map(_.user_id),
        teamIds = Seq(teamId.id)
      ).get

      val insertedEmailHealthCheckRecords: Seq[EmailHealthCheckRecordId] = Helpers.seqTryToTrySeq {

        emailSettings.map { es =>

          emailHealthCheckDAO.createPendingEmailHealthCheckRecord(
            teamId = teamId,
            emailSettingIdForHealthCheck = es.id.get
          )

        }

      }.map(_.flatten).get


      emailHealthCheckService.getAllExistingHealthCheckRecords(
        teamId = teamId,
      ) match {
        case Failure(exception) =>

          println(
            s"Failed to fetch all email health check records for teamId: $teamId: $exception"
          )

          assert(false)

        case Success(ehcList) =>

          assert(
            ehcList.map(_.id) == insertedEmailHealthCheckRecords &&
              ehcList.map(_.email_setting_id) == emailSettings.map(_.id.get)
          )

      }

    }

  }

  describe("Test getExistingHealthCheckRecord") {

    it(
      "should return None if no health check records are available"
    ) {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val team = initialData.account.teams.head

      val teamId = TeamId(id = team.team_id)

      val emailSetting = initialData.emailSetting.get

      emailHealthCheckService.getExistingHealthCheckRecord(
        emailSettingId = emailSetting.id.get,
        teamId = teamId,
      ) match {
        case Failure(exception) =>

          println(
            s"Failed to fetch all email health check records for teamId: $teamId: $exception"
          )

          assert(false)

        case Success(ehcOpt) =>

          assert(ehcOpt.isEmpty)

      }

    }

    it(
      "should return None if non existing email setting id is provided"
    ) {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val team = initialData.account.teams.head

      val teamId = TeamId(id = team.team_id)

      val incorrectEmailSettingId = EmailSettingId(emailSettingId = 2351232)

      emailHealthCheckService.getExistingHealthCheckRecord(
        emailSettingId = incorrectEmailSettingId,
        teamId = teamId,
      ) match {
        case Failure(exception) =>

          println(
            s"Failed to fetch all email health check records for teamId: $teamId: $exception"
          )

          assert(false)

        case Success(ehcOpt) =>

          assert(ehcOpt.isEmpty)

      }

    }

    it(
      "should return correct email health check record if an existing email setting id is provided"
    ) {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val team = initialData.account.teams.head

      val teamId = TeamId(id = team.team_id)

      val es = initialData.emailSetting.get

      val emailHealthCheckRecordId = emailHealthCheckDAO.createPendingEmailHealthCheckRecord(
        teamId = teamId,
        emailSettingIdForHealthCheck = es.id.get
      ).get.get

      emailHealthCheckService.getExistingHealthCheckRecord(
        emailSettingId = es.id.get,
        teamId = teamId,
      ) match {
        case Failure(exception) =>

          println(
            s"Failed to fetch all email health check records for teamId: $teamId: $exception")

          assert(false)

        case Success(ehcOpt) =>

          assert(
            ehcOpt.map(_.id).contains(emailHealthCheckRecordId) &&
              ehcOpt.map(_.email_setting_id).contains(es.id.get)
          )

      }

    }


  }

  describe("test emailHealthSettingCheck"){
      it("should update the retry count for None emailtarcking message"){
          val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

          val team = initialData.account.teams.head

          val teamId = TeamId(id = team.team_id)

          val es = initialData.emailSetting.get

          val emailHealthCheckRecordId = emailHealthCheckDAO.createPendingEmailHealthCheckRecord(
              teamId = teamId,
              emailSettingIdForHealthCheck = es.id.get
          ).get.get

          val result: Future[Option[EmailHealthCheckRecord]] = for {
              emailHealthCheckRecord: Option[EmailHealthCheckRecord] <- Future.fromTry(emailHealthCheckDAO.getEmailHealthRecord(teamId, es.id.get))

              _ <- emailHealthCheckService.emailSettingHealthCheck(
                  emailSettingForHealthCheck = es,
                  emailHealthCheckRecord = emailHealthCheckRecord.get,
                  emailMessage = None
              )

              emailHealthCheckRecordAfterUpdate: Option[EmailHealthCheckRecord] <- Future.fromTry(emailHealthCheckDAO.getEmailHealthRecord(teamId, es.id.get))


          } yield {
              emailHealthCheckRecordAfterUpdate
          }

          result.map {
              case Some(emailHealthCheckRecord) => assert(emailHealthCheckRecord.retry_count == 1 && emailHealthCheckRecord.status == Pending)
              case None => assert(false)
          }
      }

      it("should update the status to failed ") {
          val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

          val team = initialData.account.teams.head

          val teamId = TeamId(id = team.team_id)

          val es = initialData.emailSetting.get

          val emailHealthCheckRecordId = emailHealthCheckDAO.createPendingEmailHealthCheckRecord(
              teamId = teamId,
              emailSettingIdForHealthCheck = es.id.get
          ).get.get

          val result: Future[Option[EmailHealthCheckRecord]] = for {
              emailHealthCheckRecord: Option[EmailHealthCheckRecord] <- Future.fromTry(emailHealthCheckDAO.getEmailHealthRecord(teamId, es.id.get))

              updatedEmailHealthCheckRecord = emailHealthCheckRecord
                .map(record => record.copy(retry_count = 9)) // Adjust retry_count


              _ <- emailHealthCheckService.emailSettingHealthCheck(
                  emailSettingForHealthCheck = es,
                  emailHealthCheckRecord = updatedEmailHealthCheckRecord.get,
                  emailMessage = None
              )


              emailHealthCheckRecordAfterUpdate: Option[EmailHealthCheckRecord] <- Future.fromTry(emailHealthCheckDAO.getEmailHealthRecord(teamId, es.id.get))


          } yield {
              emailHealthCheckRecordAfterUpdate
          }

          result.map {
              case Some(emailHealthCheckRecord) => assert(emailHealthCheckRecord.status == Failed)
              case None => assert(false)
          }
      }


      it("should update the status to completed") {
          val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

          val team = initialData.account.teams.head

          val teamId = TeamId(id = team.team_id)

          val es = initialData.emailSetting.get

        val emailHealthCheckDAO: EmailHealthCheckDAO = new EmailHealthCheckDAO


        val emailHealthCheckRecordId = emailHealthCheckDAO.createPendingEmailHealthCheckRecord(
              teamId = teamId,
              emailSettingIdForHealthCheck = es.id.get
          ).get.get

        val emailSenderService: EmailSenderService = mock[EmailSenderService]
        val emailService: EmailService = mock[EmailService]
        val emailReplyTrackingService: EmailReplyTrackingService = mock[EmailReplyTrackingService]
        val pusherService: PusherService = mock[PusherService]
        val emailHealthDnsCheckService: EmailHealthDnsCheckService = mock[EmailHealthDnsCheckService]

        val emailHealthCheckService: EmailHealthCheckService = new EmailHealthCheckService(
          emailHealthDnsCheckService = emailHealthDnsCheckService,
          emailSenderService = emailSenderService,
          emailSettingDAO = emailSettingDAO,
          emailService = emailService,
          emailHealthCheckDAO = emailHealthCheckDAO,
          pusherService = pusherService
        )

          val emailReplyStatusAutoReply = EmailReplyStatus(
              replyType = EmailReplyType.NOT_CATEGORIZED,
              isReplied = false,
              isUnsubscribeRequest = false,
              isAutoReply = true,
              isOutOfOfficeReply = false,
              isInvalidEmail = false,
              isForwarded = false,
              bouncedData = None
          )

          val emailMessageTracked = EmailMessageTracked(
              inbox_email_setting_id = 1,
              from = IEmailAddress(email = es.email),
              to_emails = Seq(IEmailAddress(email = "<EMAIL>"), IEmailAddress(email = "<EMAIL>")),
              subject = AppConfig.EmailHealthCheck.emailHealthCheckSubject,
              body = "SomeBody",
              base_body = "SomeBaseBody",
              text_body = "SomeTextBody",
              references_header = None,
              campaign_id = Some(123),
              step_id = Some(456),
              prospect_id_in_campaign = Some(890),
              prospect_account_id_in_campaign = Some(678),
              campaign_name = None,
              step_name = None,
              received_at = DateTime.now().minusDays(2),
              recorded_at = DateTime.now().minusDays(2),
              sr_inbox_read = true,
              original_inbox_folder = None,
              email_status = emailReplyStatusAutoReply,
              message_id = "some_message_id",
              full_headers = Json.obj(),
              scheduled_manually = false,
              reply_to = None,
              email_thread_id = None,
              gmail_msg_id = None,
              gmail_thread_id = None,
              outlook_msg_id = None,
              outlook_conversation_id = None,
              outlook_response_json = None,
              cc_emails = Seq(),
              in_reply_to_header = None,
              team_id = 1,
              account_id = 1,
              internal_tracking_note = InternalTrackingNote.EXISTING_INREPLYTO,
              tempThreadId = None
          )

        (emailHealthDnsCheckService.getSpfRecord(_: String)(_: ExecutionContext))
          .expects("gmail.com", *)
          .returns(Future.successful(Some("v=spf1 include:_spf.google.com -all")))

        /*(emailHealthDnsCheckService.getDkimRecord(_: String, _: String)(_: ExecutionContext))
          .expects("gmail.com", *, *)
          .returns(Future.successful(Some("abcd")))*/

        (emailHealthDnsCheckService.getDmarcRecord(_: String)(_: ExecutionContext))
          .expects("gmail.com", *)
          .returns(Future.successful(Some("abcd")))

          val result: Future[Option[EmailHealthCheckRecord]] = for {
              emailHealthCheckRecord: Option[EmailHealthCheckRecord] <- Future.fromTry(emailHealthCheckDAO.getEmailHealthRecord(teamId, es.id.get))

              _ <- emailHealthCheckService.emailSettingHealthCheck(
                  emailSettingForHealthCheck = es,
                  emailHealthCheckRecord = emailHealthCheckRecord.get,
                  emailMessage = Some(emailMessageTracked)
              )

              emailHealthCheckRecordAfterUpdate: Option[EmailHealthCheckRecord] <- Future.fromTry(emailHealthCheckDAO.getEmailHealthRecord(teamId, es.id.get))


          } yield {
              emailHealthCheckRecordAfterUpdate
          }

          result.map {
              case Some(emailHealthCheckRecord) => assert( emailHealthCheckRecord.status == EmailHealthCheckStatus.Completed)
              case None => assert(false)
          }
      }
  }

}
