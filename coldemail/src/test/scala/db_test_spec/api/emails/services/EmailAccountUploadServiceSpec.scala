package db_test_spec.api.emails.services

import api.accounts.models.AccountId
import api.accounts.{Account, TeamId}
import api.csv_uploads.models.CsvUploadType
import api.emails.services.CreateEmailError
import api.prospects.UploadCSVResult.UploadEmailCSVResult
import api.prospects.{CsvQueue, CsvQueueCreateFormDataV2}
import api.spammonitor.model.EmailSendStatus.{Allowed, Blocked, ManualReview, UnderReview, WarningForOneDay}
import api.spammonitor.model.EmailSendingEntityType.SendingEmail
import db_test_spec.api.accounts.fixtures.NewAccountAndEmailSettingData
import db_test_spec.api.{DbTestingBeforeAllAndAfterAll, InitialData}
import org.joda.time.DateTime
import play.api.libs.json.{JsValue, Json}
import utils.SRLogger

import scala.concurrent.Future

class EmailAccountUploadServiceSpec extends DbTestingBeforeAllAndAfterAll {

    def createCsvRow(email: String, firstName: String, lastName: String,
                     smtpHost: String, smtpPort: String, smtpUsername: String, smtpPassword: String,
                     imapHost: String, imapPort: String, imapUsername: String, imapPassword: String): Map[String, String] = {
        Map(
            "email" -> email,
            "first_name" -> firstName,
            "last_name" -> lastName,
            "smtp_host" -> smtpHost,
            "smtp_port" -> smtpPort,
            "smtp_username" -> smtpUsername,
            "smtp_password" -> smtpPassword,
            "imap_host" -> imapHost,
            "imap_port" -> imapPort,
            "imap_username" -> imapUsername,
            "imap_password" -> imapPassword
        )
    }

    val columnMap: Map[String, String] = Map(
        "email" -> "email",
        "first_name" -> "first_name",
        "last_name" -> "last_name",
        "smtp_host" -> "smtp_host",
        "smtp_port" -> "smtp_port",
        "smtp_username" -> "smtp_username",
        "smtp_password" -> "smtp_password",
        "imap_host" -> "imap_host",
        "imap_port" -> "imap_port",
        "imap_username" -> "imap_username",
        "imap_password" -> "imap_password"
    )


    describe("Bulk email Upload via csv"){
        it("should add all the emails in the csv"){
            val logger: SRLogger = new SRLogger("blacklist csvUpload ")
            val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
            val account: Account = initialData.account
            val teamId: TeamId = TeamId(initialData.account.teams.head.team_id)
            val accountId: AccountId = AccountId(initialData.account.internal_id)
            val taId: Long = initialData.account.teams.head.access_members.find(t => t.user_id == initialData.account.internal_id).get.ta_id

            val csvData: Seq[Map[String, String]] = Seq(
                createCsvRow("<EMAIL>", "John", "Doe", "smtp.example.com", "587", "<EMAIL>", "pass1", "imap.example.com", "993", "<EMAIL>", "pass1"),
                createCsvRow("<EMAIL>", "Jane", "Smith", "smtp.example.com", "587", "<EMAIL>", "pass2", "imap.example.com", "993", "<EMAIL>", "pass2"),
                createCsvRow("<EMAIL>", "Marketing", "Team", "smtp.gmail.com", "465", "<EMAIL>", "securepass", "imap.gmail.com", "993", "<EMAIL>", "securepass")
            )



            val csvCreateForm = CsvQueueCreateFormDataV2(
                file_url = "https://example.com/endgame.csv",
                column_map = Json.toJson(columnMap),
                force_update_prospects = Some(false),
                csv_upload_type = CsvUploadType.BULKEMAILUPLOAD,
                list_name = None,
                campaign_id = None,
                tags = None,
                force_change_ownership = Some(false),
                ignore_email_empty_rows = Some(false),
                ignore_prospects_active_in_other_campaigns = Some(false),
                deduplication_columns = None,
                ignore_prospects_in_other_campaigns = None
            )

            val result: Future[UploadEmailCSVResult] = for {

                _ <- Future.fromTry(CsvQueue.createV2(
                    accountId = initialData.account.internal_id,
                    teamId = Some(teamId.id),
                    loggedin_id = initialData.account.internal_id,
                    ta_id = Some(initialData.account.teams.head.access_members.find(t => t.user_id == initialData.account.internal_id).get.ta_id),
                    data = csvCreateForm
                ))

                csvQueue: Option[CsvQueue] = Some(CsvQueue(
                    id = 1,
                    account_id = accountId.id,
                    team_id = Some(teamId.id),
                    ta_id = Some(taId),
                    //                    loggedin_id = accountId.id,
                    file_name = "engame.csv",
                    file_url = "https://example.com/engame.csv",
                    list_name = None, created_at = DateTime.now(),
                    campaign_id = None,
                    tags = None,
                    uploaded_at = None,
                    has_been_uploaded = false,
                    force_update_prospects = Some(false),
                    ignore_email_empty_rows = Some(false),
                    force_change_ownership = None,
                    ignore_prospects_active_in_other_campaigns = None,
                    ignore_prospects_in_other_campaigns = None,
                    column_map = Json.toJson(columnMap),
                    error = None,
                    error_at = None,
                    deduplication_columns = None,
                    csv_upload_type = CsvUploadType.BULKEMAILUPLOAD
                ))


                res: UploadEmailCSVResult <- Future.fromTry(emailAccountUploadService.addBulkEmailsViaCSVUpload(
                    doerAccount = account, 
                    teamId = teamId, 
                    mappingFromClient = columnMap, 
                    rowMapFromCSV = csvData,
                    emailTag = None
                    
                ))


            } yield {
                res
            }

            result.flatMap { res =>
             val sendingStatus =  emailSendingStatusDAO.getEmailSendingStatusForEntityType(
                  emailSendStatuses = Seq(Allowed,UnderReview,Blocked,ManualReview,WarningForOneDay), emailSendingEntityType = SendingEmail
              )
                  println(res)
                  println(s"sending statuses: ${sendingStatus}")
                  assert(res.total_created == 3 && res.email_errors.isEmpty)

              }
              .recover { e =>
                  println(e)
                  assert(false)
              }
        }


        it("should add 2 emails and neglect one because of duplication") {
            val logger: SRLogger = new SRLogger("blacklist csvUpload ")
            val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
            val account: Account = initialData.account
            val teamId: TeamId = TeamId(initialData.account.teams.head.team_id)
            val accountId: AccountId = AccountId(initialData.account.internal_id)
            val taId: Long = initialData.account.teams.head.access_members.find(t => t.user_id == initialData.account.internal_id).get.ta_id

            val csvData: Seq[Map[String, String]] = Seq(
                createCsvRow("<EMAIL>", "John", "Doe", "smtp.example.com", "587", "<EMAIL>", "pass1", "imap.example.com", "993", "<EMAIL>", "pass1"),
                createCsvRow("<EMAIL>", "Jane", "Smith", "smtp.example.com", "587", "<EMAIL>", "pass2", "imap.example.com", "993", "<EMAIL>", "pass2"),
                createCsvRow("<EMAIL>", "Marketing", "Team", "smtp.gmail.com", "465", "<EMAIL>", "securepass", "imap.gmail.com", "993", "<EMAIL>", "securepass"),
                createCsvRow("<EMAIL>", "Jane", "Smith", "smtp.example.com", "587", "<EMAIL>", "pass2", "imap.example.com", "993", "<EMAIL>", "pass2"),
                createCsvRow("<EMAIL>", "Marketing", "Team", "smtp.gmail.com", "465", "<EMAIL>", "securepass", "imap.gmail.com", "993", "<EMAIL>", "securepass"),
                createCsvRow("<EMAIL>", "Marketing", "Team", "smtp.gmail.com", "465", "<EMAIL>", "securepass", "imap.gmail.com", "993", "<EMAIL>", "securepass"),
                createCsvRow("<EMAIL>", "Marketing", "Team", "smtp.gmail.com", "465", "<EMAIL>", "securepass", "imap.gmail.com", "993", "<EMAIL>", "securepass"),
                createCsvRow("<EMAIL>", "Marketing", "Team", "smtp.gmail.com", "465", "<EMAIL>", "securepass", "imap.gmail.com", "993", "<EMAIL>", "securepass"),

            )


            val csvCreateForm = CsvQueueCreateFormDataV2(
                file_url = "https://example.com/endgame.csv",
                column_map = Json.toJson(columnMap),
                force_update_prospects = Some(false),
                csv_upload_type = CsvUploadType.BULKEMAILUPLOAD,
                list_name = None,
                campaign_id = None,
                tags = None,
                force_change_ownership = Some(false),
                ignore_email_empty_rows = Some(false),
                ignore_prospects_active_in_other_campaigns = Some(false),
                deduplication_columns = None,
                ignore_prospects_in_other_campaigns = None
            )

            val result: Future[UploadEmailCSVResult] = for {

                _ <- Future.fromTry(CsvQueue.createV2(
                    accountId = initialData.account.internal_id,
                    teamId = Some(teamId.id),
                    loggedin_id = initialData.account.internal_id,
                    ta_id = Some(initialData.account.teams.head.access_members.find(t => t.user_id == initialData.account.internal_id).get.ta_id),
                    data = csvCreateForm
                ))

                csvQueue: Option[CsvQueue] = Some(CsvQueue(
                    id = 1,
                    account_id = accountId.id,
                    team_id = Some(teamId.id),
                    ta_id = Some(taId),
                    //                    loggedin_id = accountId.id,
                    file_name = "engame.csv",
                    file_url = "https://example.com/engame.csv",
                    list_name = None, created_at = DateTime.now(),
                    campaign_id = None,
                    tags = None,
                    uploaded_at = None,
                    has_been_uploaded = false,
                    force_update_prospects = Some(false),
                    ignore_email_empty_rows = Some(false),
                    force_change_ownership = None,
                    ignore_prospects_active_in_other_campaigns = None,
                    ignore_prospects_in_other_campaigns = None,
                    column_map = Json.toJson(columnMap),
                    error = None,
                    error_at = None,
                    deduplication_columns = None,
                    csv_upload_type = CsvUploadType.BULKEMAILUPLOAD
                ))


                res: UploadEmailCSVResult <- Future.fromTry(emailAccountUploadService.addBulkEmailsViaCSVUpload(
                    doerAccount = account, 
                    teamId = teamId, 
                    mappingFromClient = columnMap, 
                    rowMapFromCSV = csvData,
                    emailTag = None
                    
                ))
            } yield {
                res
            }

            result.flatMap { res =>
                  println(res)
                  println(Json.toJson(res))
                  assert(res.total_created == 7 && res.email_errors.head.error == CreateEmailError.DuplicateEmailError.message)

              }
              .recover { e =>
                  println(e)
                  assert(false)
              }
        }
    }
}
