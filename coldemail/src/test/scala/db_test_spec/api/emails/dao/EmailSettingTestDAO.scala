package db_test_spec.api.emails.dao

import api.accounts.email.models.EmailServiceProvider
import api.accounts.models.OrgId
import api.email_infra_integrations.models.PlatformType
import io.smartreach.esp.api.emails.EmailSettingId
import scalikejdbc.{DB, scalikejdbcSQLInterpolationImplicitDef}
import utils.cronjobs.email_setting_deletion.model.EmailSettingStatus

import scala.util.Try
object EmailSettingTestDAO {

  def updateEmailSettingDailyQuota(
                                    emailSettingId: EmailSettingId,
                                    quota_per_day: Int
                                  ): Try[Int] = Try {
    DB readOnly { implicit session =>
      sql"""
          UPDATE
           email_settings
          SET
            quota_per_day = ${quota_per_day}
          WHERE
            id = ${emailSettingId.emailSettingId}
          returning quota_per_day;
       """
        .map(rs => rs.int("quota_per_day"))
        .single
        .apply()
        .get
    }
  }


  def updateMaildosoEmailSetting(
                                  emailSettingId: EmailSettingId,
                                  domain_name: String
                                ): Try[Int] = Try {

    DB autoCommit  { implicit session =>
      sql"""
          UPDATE
           email_settings
          SET
            email_domain = $domain_name,
            domain_provider = ${PlatformType.MAILDOSO.toString},
            service_provider = ${EmailServiceProvider.OTHER.toKey},
            status = ${EmailSettingStatus.InActive.toString}
          WHERE
            id = ${emailSettingId.emailSettingId}
       """
        .update
        .apply()
    }
  }

//  def addDedecatedServerForOrg(
//                                 orgId: OrgId,
//                                 id: Long
//                              ): Try[Long] = Try {
//
//    val rep_id = DB autoCommit  { implicit session =>
//        sql"""
//          insert into rep_mail_servers(
//                    id,
//                    public_ip,
//                    reverse_dns,
//
//                    sending_score_threshold,
//                    sending_score,
//                    sending_score_updated_at,
//
//                    host,
//                    is_dedicated
//                  )
//                  VALUES (
//                    $id,
//                    '127:0:0:0:1',
//                    'reverse_dns',
//
//                    100,
//                    200,
//                    now(),
//
//                    'host',
//                    true
//                 )
//                  RETURNING id;
//
//       """
//          .map(rs => rs.long("id"))
//          .single
//          .apply()
//          .get
//    }
////    DB autoCommit  { implicit session =>
////
////
////    sql"""
////           update organizations
////           set rep_mail_server_id = $id
////           where id = ${orgId.id}
////         """
////        .update
////        .apply()
////
////
////    }
//    rep_id
//  }
}
