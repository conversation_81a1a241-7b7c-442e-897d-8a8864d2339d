package db_test_spec.api.emails.dao

import api.accounts.TeamId
import api.email_infra_integrations.models.PurchaseDomainsAndEmailsStatus
import api.prospects.ProspectSource
import db_test_spec.api.leadFinder.dao.ProspectDataForTest
import scalikejdbc.{DB, scalikejdbcSQLInterpolationImplicitDef}

import scala.util.Try

object EmailInfraDAO {

  def updateDomainPurchaseTaskId(
                            task_id: String,
                             team_id: TeamId
                           ): Try[Int] = Try {
    DB autoCommit  { implicit session =>
      sql"""
          UPDATE purchased_domains
          SET domain_status = ${PurchaseDomainsAndEmailsStatus.ACTIVE.toString}
          WHERE team_id = ${team_id.id}
          and platform_domain_purchased_task_id = ${task_id}
          """
        .update
        .apply()
    }
  }

}
