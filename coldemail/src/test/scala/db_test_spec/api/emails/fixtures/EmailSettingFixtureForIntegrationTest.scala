package db_test_spec.api.emails.fixtures

import api.accounts.models.{AccountId, OrgId}
import api.accounts.TeamId
import api.emails.{EmailSetting, EmailSettingForm}
import api.emails.services.CreateEmailViaFormError
import api.spammonitor.dao.UpdateEmailSendingStatusForm
import api.spammonitor.model.EmailSendingEntityTypeData.SendingEmailData
import api.spammonitor.model.SendEmailStatusData.AllowedData
import api.spammonitor.service.UpdateEmailSendingStatusError
import db_test_spec.api.accounts.fixtures.NewAccountAndEmailSettingData.microsoftOAuth
import db_test_spec.api.emails.fixtures.DefaultEmailSettingParametersFixtures.defaultEmailSettingForm
import io.smartreach.esp.api.microsoftOAuth.EmailSettingUpdateAccessToken
import org.joda.time.DateTime
import utils.SRLogger
import utils.testapp.TestAppTrait

import scala.util.Try

object EmailSettingFixtureForIntegrationTest extends TestAppTrait{

  def createEmailSetting(
                          orgId: OrgId,
                          accountId: AccountId,
                          teamId: TeamId,
                          taId: Long,
                          emailSettingForm: Option[EmailSettingForm] = None
                        )(using Logger: SRLogger): Try[EmailSetting] = {

    val emailSettingData:Option[EmailSettingForm] = if(emailSettingForm.isDefined){
      emailSettingForm
    } else {
      Some(defaultEmailSettingForm)
    }

    for {

      emailSettingEither: Either[CreateEmailViaFormError, EmailSetting] <- Try {
        emailAccountService.createEmailViaForm(
          accountId = accountId.id,
          teamId = teamId.id,
          taId = taId,
          data = emailSettingData.get,
          orgId = orgId
        )
      }
      add_token <- microsoftOAuth.updateAccessTokenAndRefreshToken(
        emailSettingId = emailSettingEither.toOption.get.id.get,
        data = EmailSettingUpdateAccessToken(
          oauth2_refresh_token = s"oauth2_refresh_token+${accountId}",
          oauth2_access_token = s"oauth2_access_token+${accountId}",
          oauth2_token_expires_in = 36000,
          oauth2_access_token_expires_at = DateTime.now().plusDays(10000)
        )
      )

      emailSetting: EmailSetting <- Try(emailSettingEither match {
        case Left(err) =>
          throw new Exception(s"emailSetting Created: $err")

        case Right(emailSetting) =>
          emailSetting
      })

      emailSendStatus: Try[Long] <- Try({
        val updateEmailSendingStatusForm = UpdateEmailSendingStatusForm(
          entityType = SendingEmailData(
            orgId = orgId,
            senderId = emailSetting.id.get.emailSettingId,
            emailSettingEmail = emailSetting.email
          ),
          orgId = orgId,
          sendEmailStatus = AllowedData()
        )
        emailSendingStatusDAO.addingEmailSendingStatusTry(
          updateEmailSendingStatusForm = updateEmailSendingStatusForm
        )
      })
    } yield {
      emailSetting
    }
  }
}
