package db_test_spec.api.emails

import api.accounts.TeamId
import api.campaigns.models.{CampaignEmailSettingsId, CampaignStepType}
import api.campaigns.services.CampaignId
import api.emails.EmailScheduled
import api.emails.models.{DeletionReason, EmailSendingFlow}
import api.tasks.models.RevertData
import app.db_test.{CustomNotCategorizedAndDoNotContactIds, SchedulerTestInput}
import api.prospects.models.{ProspectId, StepId}
import api.emails.models.DeletionReason
import api.prospects.models.{NewProspectAssociateWithCampaignData, ProspectId, StepId}
import api.tasks.models.RevertData
import app.db_test.{CustomNotCategorizedAndDoNotContactIds, SchedulerTestInput}
import db_test_spec.api.accounts.fixtures.{EmailScheduledNewFixture, NewAccountAndEmailSettingData}
import db_test_spec.api.accounts.fixtures.NewAccountAndEmailSettingData
import db_test_spec.api.campaigns.fixtures.CreateNewCampaignFixture.findCategorizedAndDoNotContactCustomIds
import db_test_spec.api.campaigns.test_utils.{CampaignUtils, CreateAndStartCampaignData}
import db_test_spec.api.campaigns.{CampaignCreationData, CampaignCreationFixtureForIntegrationTest, InitializedCreateAndStartCampaignData}
import db_test_spec.api.{AppSpecFixture, DbTestingBeforeAllAndAfterAll, InitialData, InputForInitializingCampaignCreateData}
import eventframework.ProspectObject
import org.joda.time.DateTime
import scalikejdbc.DBSession
import sr_scheduler.models.ChannelData.EmailChannelData
import sr_scheduler.models.{ChannelType, EmailScheduledNew}
import utils.SRLogger
import utils.mq.channel_scheduler.channels.ScheduleTasksData

import scala.concurrent.Future
import scala.util.{Failure, Random, Success}

class EmailScheduledDAOSpec extends DbTestingBeforeAllAndAfterAll{

  lazy val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
  

  describe("getEmailDataForProspectRevert") {

    it("should give success while EmailScheduledRevertData") {

      val DbandSession = dbUtils.startLocalTx()
      implicit val session: DBSession = DbandSession.session

      //      given logger: SRLogger = AppSpecFixture.logger
      
      val categorizedAndDoNotContactCustomIds: CustomNotCategorizedAndDoNotContactIds = findCategorizedAndDoNotContactCustomIds(prospect_categories = initialData.account.teams.head.prospect_categories_custom)
      val input: InputForInitializingCampaignCreateData = SchedulerTestInput.getInputForSchedulerIntegrationTest(
        sharedData = initialData,
        categorizedAndDoNotContactCustomIds = categorizedAndDoNotContactCustomIds,
        prospect_emails = Seq(Random.alphanumeric.take(10).mkString("") + "@gmail.com"),
        linkedSettingId = None
      )
      val initializeCreateAndStartCampaignData: InitializedCreateAndStartCampaignData = AppSpecFixture.initializeCampaignDataForIntegrationTest(
        inputForInitializingCampaignCreateData = input
      )

      val scheduleTaskData = for {
        campaignCreationData: CampaignCreationData <- CampaignCreationFixtureForIntegrationTest.createAndStartCampaign(
          inputForInitializingCampaignCreateData = input,
          initializedData = initializeCreateAndStartCampaignData
        )

        result: ScheduleTasksData <- emailChannelScheduler.scheduleTasksForChannel(
          channelData = EmailChannelData(
            emailSettingId = initialData.emailSetting.get.id.get.emailSettingId
          ),
          teamId = initialData.head_team_id,
          accountService = accountService,
          //accountDAO = accountDAO,
          emailNotificationService = emailNotificationService,
          campaignService = campaignService,
          campaignProspectDAO = campaignProspectDAO,
          campaignProspectService = campaignProspectService,
          campaignStepVariantDAO = campaignStepVariantDAO,
          campaignStepDAO = campaignStepDAO,
          srShuffleUtils = srShuffleUtils,
          emailServiceCompanion = emailServiceCompanion,
          templateService = templateService,
          taskDAO = taskDAO,
          taskService = taskService,
          campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO,
          campaignsMissingMergeTagService = campaignsMissingMergeTagService,
          srRedisSimpleLockServiceV2 = srRedisSimpleLockServiceV2,
          mqWebhookCompleted = mqWebhookCompleted,
          calendarAppService = calendarAppService,
          accountOrgBillingRelatedService = accountOrgBillingRelatedService,
          srRollingUpdateCoreService = srRollingUpdateCoreService
        )

        get_scheduled_email_data: Option[RevertData.EmailScheduledRevertData] <- Future.fromTry {

          emailScheduledDAO.getEmailDataForProspectRevert(
            campaign_id = CampaignId(id = campaignCreationData.campaign.id),
            prospect_id = ProspectId(id = campaignCreationData.prospects.created_ids.head),
            step_id = StepId(id = campaignCreationData.campaignStepVariant.step_id),
          )


        }

        update_campaign_prospect_status: Int <- Future.fromTry {

          get_scheduled_email_data match {

            case None =>

              Failure(new Exception("err emails_scheduled not found for prospect"))


            case Some(value) =>

              campaignProspectDAO.campaignProspectMultichannelRevert(
                campaign_id = CampaignId(id = campaignCreationData.campaign.id),
                prospectId = ProspectId(id = campaignCreationData.prospects.created_ids.head),
                current_step_id = StepId(id = campaignCreationData.campaignStepVariant.step_id),
                revert_data = value,
                reverted_by = "campaignProspectMultichannelRevert",
                team_id = TeamId(id = campaignCreationData.campaign.team_id),
                previous_task_step_type = CampaignStepType.AutoEmailStep,
                previous_task_channel_type = ChannelType.EmailChannel,
                deletion_reason = DeletionReason.Other("campaignProspectMultichannelRevert")
              )

          }

        }

      } yield {

        update_campaign_prospect_status

      }


      scheduleTaskData.map(

        res => {
          println("success path")

          dbUtils.commitAndCloseSession(DbandSession.db)
          assert(res == 1)

        }
      )
        .recover { case err => {
          print(err)

          dbUtils.commitAndCloseSession(DbandSession.db)
          assert(false)


        }
        }

    }

  }

  describe("testing findValidLatestEmailEntry"){

    it("should give success while EmailScheduledRevertData") {

      val DbandSession = dbUtils.startLocalTx()
      implicit val session: DBSession = DbandSession.session

      //      given logger: SRLogger = AppSpecFixture.logger
      
      val categorizedAndDoNotContactCustomIds: CustomNotCategorizedAndDoNotContactIds = findCategorizedAndDoNotContactCustomIds(prospect_categories = initialData.account.teams.head.prospect_categories_custom)
      val input: InputForInitializingCampaignCreateData = SchedulerTestInput.getInputForSchedulerIntegrationTest(
        sharedData = initialData,
        categorizedAndDoNotContactCustomIds = categorizedAndDoNotContactCustomIds,
        prospect_emails = Seq(Random.alphanumeric.take(10).mkString("") + "@gmail.com"),
        linkedSettingId = None
      )
      val initializeCreateAndStartCampaignData: InitializedCreateAndStartCampaignData = AppSpecFixture.initializeCampaignDataForIntegrationTest(
        inputForInitializingCampaignCreateData = input
      )

      val scheduleTaskData = for {
        campaignCreationData: CampaignCreationData <- CampaignCreationFixtureForIntegrationTest.createAndStartCampaign(
          inputForInitializingCampaignCreateData = input,
          initializedData = initializeCreateAndStartCampaignData
        )

        result: ScheduleTasksData <- emailChannelScheduler.scheduleTasksForChannel(
          channelData = EmailChannelData(
            emailSettingId = initialData.emailSetting.get.id.get.emailSettingId
          ),
          teamId = initialData.head_team_id,
          accountService = accountService,
          //accountDAO = accountDAO,
          emailNotificationService = emailNotificationService,
          campaignService = campaignService,
          campaignProspectDAO = campaignProspectDAO,
          campaignProspectService = campaignProspectService,
          campaignStepVariantDAO = campaignStepVariantDAO,
          campaignStepDAO = campaignStepDAO,
          srShuffleUtils = srShuffleUtils,
          emailServiceCompanion = emailServiceCompanion,
          templateService = templateService,
          taskDAO = taskDAO,
          taskService = taskService,
          campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO,
          campaignsMissingMergeTagService = campaignsMissingMergeTagService,
          srRedisSimpleLockServiceV2 = srRedisSimpleLockServiceV2,
          mqWebhookCompleted = mqWebhookCompleted,
          calendarAppService = calendarAppService,
          accountOrgBillingRelatedService = accountOrgBillingRelatedService,
          srRollingUpdateCoreService = srRollingUpdateCoreService
        )

        get_scheduled_email_data: Option[NewProspectAssociateWithCampaignData] <- Future.fromTry {

          emailScheduledDAO.findValidLatestEmailEntry(
            campaign_id = CampaignId(id = campaignCreationData.campaign.id),
            prospect_id = ProspectId(id = campaignCreationData.prospects.created_ids.head),
            team_id = TeamId(id = campaignCreationData.campaign.team_id)
          )


        }

        update_campaign_prospect_status: Int <- Future.fromTry {

          get_scheduled_email_data match {

            case None =>

              Failure(new Exception("err emails_scheduled not found for prospect"))


            case Some(value) =>

              campaignProspectDAO.campaignProspectMultichannelRevert(
                campaign_id = CampaignId(id = campaignCreationData.campaign.id),
                prospectId = ProspectId(id = campaignCreationData.prospects.created_ids.head),
                current_step_id = StepId(id = campaignCreationData.campaignStepVariant.step_id),
                revert_data = RevertData.EmailScheduledRevertData(
                  last_scheduled_at = value.scheduled_at,
                  emails_scheduled_id = value.es_id,
                  sent_at = value.sent_at
                ),
                reverted_by = "campaignProspectMultichannelRevert",
                team_id = TeamId(id = campaignCreationData.campaign.team_id),
                previous_task_step_type = CampaignStepType.AutoEmailStep,
                previous_task_channel_type = ChannelType.EmailChannel,
                deletion_reason = DeletionReason.Other("campaignProspectMultichannelRevert")
              )

          }

        }

      } yield {

        update_campaign_prospect_status

      }


      scheduleTaskData.map(

        res => {
          println("success path")

          dbUtils.commitAndCloseSession(DbandSession.db)
          assert(res == 1)

        }
      )
        .recover { case err => {
          print(err)

          dbUtils.commitAndCloseSession(DbandSession.db)
          assert(false)


        }
        }

    }

    it("should give success while markStepIdAsNull") {

      val DbandSession = dbUtils.startLocalTx()
      implicit val session: DBSession = DbandSession.session

      //      given logger: SRLogger = AppSpecFixture.logger
      
      val categorizedAndDoNotContactCustomIds: CustomNotCategorizedAndDoNotContactIds = findCategorizedAndDoNotContactCustomIds(prospect_categories = initialData.account.teams.head.prospect_categories_custom)
      val input: InputForInitializingCampaignCreateData = SchedulerTestInput.getInputForSchedulerIntegrationTest(
        sharedData = initialData,
        categorizedAndDoNotContactCustomIds = categorizedAndDoNotContactCustomIds,
        prospect_emails = Seq(Random.alphanumeric.take(10).mkString("") + "@gmail.com"),
        linkedSettingId = None
      )
      val initializeCreateAndStartCampaignData: InitializedCreateAndStartCampaignData = AppSpecFixture.initializeCampaignDataForIntegrationTest(
        inputForInitializingCampaignCreateData = input
      )

      val scheduleTaskData = for {
        campaignCreationData: CampaignCreationData <- CampaignCreationFixtureForIntegrationTest.createAndStartCampaign(
          inputForInitializingCampaignCreateData = input,
          initializedData = initializeCreateAndStartCampaignData
        )

        result: ScheduleTasksData <- emailChannelScheduler.scheduleTasksForChannel(
          channelData = EmailChannelData(
            emailSettingId = initialData.emailSetting.get.id.get.emailSettingId
          ),
          teamId = initialData.head_team_id,
          accountService = accountService,
          //accountDAO = accountDAO,
          emailNotificationService = emailNotificationService,
          campaignService = campaignService,
          campaignProspectDAO = campaignProspectDAO,
          campaignProspectService = campaignProspectService,
          campaignStepVariantDAO = campaignStepVariantDAO,
          campaignStepDAO = campaignStepDAO,
          srShuffleUtils = srShuffleUtils,
          emailServiceCompanion = emailServiceCompanion,
          templateService = templateService,
          taskDAO = taskDAO,
          taskService = taskService,
          campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO,
          campaignsMissingMergeTagService = campaignsMissingMergeTagService,
          srRedisSimpleLockServiceV2 = srRedisSimpleLockServiceV2,
          mqWebhookCompleted = mqWebhookCompleted,
          calendarAppService = calendarAppService,
          accountOrgBillingRelatedService = accountOrgBillingRelatedService,
          srRollingUpdateCoreService = srRollingUpdateCoreService
        )

        get_scheduled_email_data: Option[NewProspectAssociateWithCampaignData] <- Future.fromTry {

          emailScheduledDAO.findValidLatestEmailEntry(
            campaign_id = CampaignId(id = campaignCreationData.campaign.id),
            prospect_id = ProspectId(id = campaignCreationData.prospects.created_ids.head),
            team_id = TeamId(id = campaignCreationData.campaign.team_id)
          )


        }

        new_prospect_to_associate: NewProspectAssociateWithCampaignData <- Future.fromTry {

          get_scheduled_email_data match {

            case None =>

              Failure(new Exception("err emails_scheduled not found for prospect"))

            case Some(value) =>

              println(s"value: ${value}")
              
              Success(value)

          }

        }

        update_step_id_to_null: List[Long] <- Future.fromTry{

          emailScheduledDAO.markStepIdAsNull(
            campaign_id = CampaignId(id = campaignCreationData.campaign.id),
            prospectId = ProspectId(id = campaignCreationData.prospects.created_ids.head),
            team_id = TeamId(id = campaignCreationData.campaign.team_id),
            emails_scheduled = Seq(new_prospect_to_associate.es_id)
          )

        }

      } yield {

        update_step_id_to_null.contains(new_prospect_to_associate.es_id)

      }


      scheduleTaskData.map(

        res => {
          println("success path")

          dbUtils.commitAndCloseSession(DbandSession.db)
          assert(res)

        }
      )
        .recover { case err => {
          print(err)

          dbUtils.commitAndCloseSession(DbandSession.db)
          assert(false)


        }
        }

    }


  }
  
  describe("isClicked") {
    it("should pass") {
      implicit val Logger = new SRLogger("isClicked")
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val result: Future[Seq[EmailScheduled]] = for {

        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 4
        )
        prospects: Seq[ProspectObject] <- Future {
          initialData.prospectsResult
        }

        (duplicate: Long, master: Long) <- Future {
          (prospects.head.id, prospects.last.id)
        }

        addingEmailScheduled <- Future.fromTry {
          emailScheduledDAOService.saveEmailsToBeScheduledAndUpdateCampaignDataV2(
            emailsToBeScheduled = Vector(EmailScheduledNewFixture.generateEmailScheduledNew.copy(
              campaign_id = Some(createAndStartCampaignData.createCampaign.id),
              step_id = createAndStartCampaignData.createCampaign.head_step_id,
              from_email = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.sender_email,
              scheduled_from_campaign = true,
              is_opening_step = true,
              sender_email_settings_id = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.sender_email_setting_id.emailSettingId,
              team_id = createAndStartCampaignData.createCampaign.team_id,
              account_id = createAndStartCampaignData.createCampaign.owner_id,
              receiver_email_settings_id = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.receiver_email_setting_id.emailSettingId,
              campaign_email_settings_id = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.id,
              prospect_id = Some(duplicate),
              base_body = Some("body")

            )
            ),
            campaign_email_setting_id = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.id,
            emailSendingFlow = None,
            Logger = Logger
          )
        }
        result: Seq[EmailScheduled] <- Future.fromTry{
          emailScheduledDAO.isClicked(
            emailScheduledId = addingEmailScheduled.head.email_scheduled_id,
            clickedUrl = "clickedUrl",
            clicked_at = DateTime.now(),
            traceReqId = None,
            Logger = Logger
          )
        }
      } yield result

      result.map(res => {
        assert(res.nonEmpty)
      }).recover(e => 
      assert(false))
    }
  }

}
