package db_test_spec.api

import org.scalatest.BeforeAndAfterAll
import org.scalatest.funspec.AsyncFunSpec
import play.api.{Application, ApplicationLoader, Environment, Mode}
import playloader.AppComponents
import utils.{Helpers, SRLogger}
import utils.appentrypoints.workers.MainMQWorkersLoader
import utils.testapp.TestAppTrait

import scala.concurrent.ExecutionContext
import scala.util.{Failure, Success}

object TestMqWorkerManager {

  private var mainMqWorkerStarted: Boolean = false

  def checkAndStartMainMqWorker() = {

    if (!mainMqWorkerStarted) {

      println(s"checkAndStartMainMqWorker: starting")

      new MainMQWorkersLoader().start() match {

        case Failure(exception) =>

          println(s"checkAndStartMainMqWorker: Error occurred 1 $exception")

        case Success(value) =>

          mainMqWorkerStarted = true

          println(s"checkAndStartMainMqWorker: Started $value")
      }

    } else {

      println(s"checkAndStartMainMqWorker already running")

    }

  }


}

object SetupDbAndRedis {

  private val lock = new Object()
  var hasSetupDbRedisAndAccount: Boolean = false

  def setup(): Unit = {

    /*
    The lock.synchronized block ensures that only one thread
    can execute the if block at a time, eliminating race conditions.
     */
    lock.synchronized {

      if (!hasSetupDbRedisAndAccount) {

        SRSetupAndDeleteFixtures.setupDbAndRedis()

        hasSetupDbRedisAndAccount = true
      }

    }

  }


}

trait DbTestingBeforeAllAndAfterAll extends AsyncFunSpec with BeforeAndAfterAll with TestAppTrait {
  override implicit lazy val executionContext :ExecutionContext = playDefaultExecutionContext
  
  // some test suites need the main mq worker, most do not
  lazy val isMainMqWorkerNeeded: Boolean = false

   given Logger: SRLogger = new SRLogger("DbTestingBeforeAllAndAfterAll")



  lazy val testApi: Application = {

    val context: ApplicationLoader.Context = ApplicationLoader.Context.create(
      Environment.simple(mode = Mode.Test)
    )

    val components = new AppComponents(context = context)
    components.application

  }


  override def beforeAll(): Unit = {
    super.beforeAll()

    SetupDbAndRedis.setup()

    println(s"BEFORE RUNNING TESTS IN MODE: , SETTING UP FIXTURES")

    if (isMainMqWorkerNeeded) {
      TestMqWorkerManager.checkAndStartMainMqWorker()
    }

//    SRSetupAndDeleteFixtures.createInitialData()(Logger)
//    match {
//      case Failure(exception) =>
//        println(s"Error occurred 2 $exception")
//      case Success(value) =>
//        println(s"getInitialData done")
//
//    }
  }


  override protected def afterAll(): Unit = {


    println("AFTER RUNNING TESTS FUNCTION")

    super.afterAll()

  }

}
