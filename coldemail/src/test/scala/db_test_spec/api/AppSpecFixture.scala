package db_test_spec.api

import org.apache.pekko.actor.ActorSystem
import org.apache.pekko.stream.Materializer
import api.accounts.*
import api.accounts.email.models.EmailServiceProvider
import api.accounts.models.{AccountId, AccountProfileInfo, OrgId}
import api.campaigns.*
import api.campaigns.models.{CallSettingSenderDetails, CampaignEmailSettingsId, CampaignName, CampaignStepData, CampaignType, LinkedinSettingSenderDetails, SmsSettingSenderDetails, WhatsappSettingSenderDetails}
import api.campaigns.services.*
import api.emails.{EmailMessageTracked, EmailSetting, EmailSettingForm}
import api.prospects.models.{ProspectCategory, ProspectCategoryRank}
import api.prospects.ProspectCreateFormData
import api.reports.models.UtmData
import api.scheduler.model.*
import api.spammonitor.model.SendEmailStatusData.AllowedData
import api.tasks.models.TaskPriority
import api.team.TeamUuid
import app.db_test.CustomNotCategorizedAndDoNotContactIds
import app.test_fixtures.accounts.OrgCountDataFixture
import org.joda.time.DateTime
import db_test_spec.api.campaigns.{CampaignCreationData, InitializedCreateAndStartCampaignData}
import io.smartreach.esp.api.emails.{EmailSettingId, IEmailAddress}
import io.sr.billing_common.models.{PlanID, PlanType}
import play.api.libs.json.Json
import play.api.libs.ws.ahc.AhcWSClient
import sr_scheduler.models.CampaignEmailPriority
import utils.SRLogger
import utils.cache_utils.model.CampaignUseStatusForEmailSetting
import utils.email.EmailReplyStatus
import utils.email.services.InternalTrackingNote
import utils.featureflags.services.SrFeatureFlags
import utils.mq.channel_scheduler.channels.ScheduleTasksData
import utils.testapp.TestAppExecutionContext

import scala.util.Random

case class InputForInitializingCampaignCreateData(
                                                 org_id: OrgId,
                                                 account_id: AccountId,
                                                 team_id: TeamId,
                                                 ta_id: Long,
                                                 campaign_name: CampaignName,
                                                 prospect_categories_custom_not_categorized: Long,
                                                 prospect_categories_custom_do_not_contact: Long,
                                                 current_sending_email_accounts: Int,
                                                 email_setting_id: EmailSettingId,
                                                 schedule_from_time_sec: Int,
                                                 schedule_till_time_sec: Int,
                                                 prospect_emails: Seq[String],
                                                 timezone: String,
                                                 enable_email_scheduler: Boolean,
                                                 campaign_email_setting_data: CampaignEmailSettingData,
                                                 campaign_linkedin_settings_data: CampaignLinkedinSettings,
                                                 campaign_call_settings_data: CampaignCallSettings,
                                                 campaign_whatsapp_settings_data: CampaignWhatsappSettings,
                                                 campaign_sms_settings_data: CampaignSmsSettings
                                               )

case class SchedulerIntegrationTestResult(
                                           scheduleTasksData: ScheduleTasksData,
                                           emailScheduledCountForCampaign: Int,
                                           scheduledCampaignProspectCount: Int
                                         )
object AppSpecFixture {

  implicit lazy val logger: SRLogger = new SRLogger(logRequestId = "AccountCreation :: ")
  implicit lazy val system: ActorSystem = TestAppExecutionContext.actorSystem
  implicit lazy val materializer: Materializer = TestAppExecutionContext.actorMaterializer
  implicit lazy val wSClient: AhcWSClient = TestAppExecutionContext.wsClient
  val quota_per_day_email_settings = 300
  val max_emails_per_day_campaign_limit = 1000
  val accountCreateForm = AccountCreateForm(
    first_name = "praveen",
    last_name = "saraswat",
    company = "company",
    email = Random.alphanumeric.take(10).mkString("") + "@smartreach.com",
    password = None,
    invite_code = None,
    timezone = None,
    country_code = "IN",
    rs_code_used = None,
    g_response = None
  )

  val utmData = UtmData(
    utm_source = None,
    utm_medium = None,
    utm_campaign = None,
    utm_term = None,
    utm_content = None,
    utm_device = None,
    utm_agid = None,
    utm_match_type = None,
    utm_placement = None,
    utm_network = None
  )

  val emailSettingForm = EmailSettingForm(
    email = "<EMAIL>",
    service_provider = EmailServiceProvider.GMAIL_API,
    smtp_username = None,
    smtp_password = None,
    smtp_host = None,
    smtp_port = None,
    imap_username = None,
    imap_password = None,
    imap_host = None,
    imap_port = None,
    email_domain = None,
    api_key = None,
    mailgun_region = None,
    quota_per_day = quota_per_day_email_settings,
    min_delay_seconds = 30,
    max_delay_seconds = 90,
    can_send = true,
    can_receive = true,
    cc_emails = None,
    bcc_emails = None,
    first_name = "praveen",
    last_name = "saraswat",
    platform_email_id = None,
    email_tag = None
  )

  val prospectCreateFormData = ProspectCreateFormData(
    email = Some("<EMAIL>"),
    first_name = Some("John"),
    last_name = Some("Doe"),
    custom_fields = Json.obj(),
    owner_id = None,
    list = None,
    company = None,
    city = None,
    country = None,
    timezone = None,
    created_at = Some(DateTime.now()),
    state = None,
    job_title = None,
    phone = None,
    phone_2 = None,
    phone_3 = None,
    linkedin_url = Some("https://www.linkedin.com/in/shubham-kudekar/")
  )

  val sendEmailStatus = AllowedData()

  val dateNow = DateTime.now()

  val orgMetadata = OrgMetadata(
    allow_user_level_api_key = None,

    ff_multichannel = Some(true),

    is_onboarding_done = Some(true),
    show_agency_pricing = Some(true),

    show_promo_option = Some(true),
    show_individual_plans = Some(true),
    show_business_plans = Some(true),
    show_business_pro_plan = Some(true),
    //      enable_warmup_box = Some(true),

    ff_emails_sent_report = Some(true),


    show_campaign_tags = Some(true),
    allowed_for_new_google_api_key = Some(true),
    increase_email_delay = Some(true)
  )

  def initializeCampaignDataForIntegrationTest(
                                               inputForInitializingCampaignCreateData: InputForInitializingCampaignCreateData
                                             ): InitializedCreateAndStartCampaignData = {
    val input = inputForInitializingCampaignCreateData
    val campaignSettings = CampaignSettings(
      // settings
      campaign_email_settings = List(
        CampaignEmailSettings(
          campaign_id = CampaignId(26),
          sender_email_setting_id = input.campaign_email_setting_data.sender_email_setting_id,
          receiver_email_setting_id = input.campaign_email_setting_data.receiver_email_setting_id,
          team_id = input.team_id,
          uuid = CampaignEmailSettingsUuid("temp_setting_id"),
          id = CampaignEmailSettingsId(input.email_setting_id.emailSettingId),
          sender_email = "<EMAIL>",
          receiver_email = "<EMAIL>",
          max_emails_per_day_from_email_account = 100,
          signature = None,
          error = None,
          from_name = None
        )
      ),
      campaign_linkedin_settings = List(
        LinkedinSettingSenderDetails(
          channel_setting_uuid = ChannelSettingUuid(uuid = "1"),
          team_id = input.team_id,
          email = "<EMAIL>",
          first_name = "gokulnath",
          last_name = "S",
          linkedin_profile_url = Some("www.linekdin.com/SRTest"),
          automation_enabled = false
        ),
        LinkedinSettingSenderDetails(
          channel_setting_uuid = ChannelSettingUuid(uuid = "2"),
          team_id = input.team_id,
          email = "<EMAIL>",
          first_name = "Guru",
          last_name = "D",
          linkedin_profile_url = Some("www.linekdin.com/guru"),
          automation_enabled = true
        )
      ),
      campaign_call_settings = List(
        CallSettingSenderDetails(
          channel_setting_uuid = ChannelSettingUuid(uuid = "1"),
          team_id = input.team_id,
          phone_number = input.campaign_call_settings_data.phone_number,
          first_name = "gokulnath",
          last_name = "S"
        )
      ),
      campaign_whatsapp_settings = List(
        WhatsappSettingSenderDetails(
          channel_setting_uuid = ChannelSettingUuid(uuid = "1"),
          team_id = input.team_id,
          phone_number = input.campaign_whatsapp_settings_data.phone_number,
          first_name = "gokulnath",
          last_name = "S"
        )
      ),
      campaign_sms_settings = List(
        SmsSettingSenderDetails(
          channel_setting_uuid = ChannelSettingUuid(uuid = "1"),
          team_id = input.team_id,
          phone_number = input.campaign_sms_settings_data.phone_number,
          first_name = "gokulnath",
          last_name = "S"
        )
      ),
      timezone = input.timezone,
      daily_from_time = input.schedule_from_time_sec, // time since beginning of day in seconds
      daily_till_time = input.schedule_till_time_sec, // time since beginning of day in seconds
      sending_holiday_calendar_id = None,

      ai_sequence_status = None,

      // Sunday is the first day
      days_preference = List(true, true, true, true, true, true, true),

      mark_completed_after_days = 1,
      max_emails_per_day = max_emails_per_day_campaign_limit,
      open_tracking_enabled = true,
      click_tracking_enabled = true,
      enable_email_validation = false,
      ab_testing_enabled = true,

      // warm up
      warmup_started_at = None,
      warmup_length_in_days = None,
      warmup_starting_email_count = None,
      show_soft_start_setting = false,

      // schedule start
      schedule_start_at = Some(DateTime.now()),
      schedule_start_at_tz = Some(input.timezone),
      send_plain_text_email = Some(false),
      campaign_type = CampaignType.MultiChannel,


      email_priority = CampaignEmailPriority.EQUAL,
      append_followups = true,
      opt_out_msg = "Pivot",
      opt_out_is_text = true,
      add_prospect_to_dnc_on_opt_out = true,
      triggers = Seq(),
      sending_mode = None,
      selected_calendar_data = None
    )

    val createCampaignData = CampaignCreateForm(
      name = Some(input.campaign_name.name),
      timezone = Some(input.timezone),
      campaign_owner_id = Some(input.account_id.id),
      campaign_type = CampaignType.MultiChannel
    )

    val prospectCreateFormData = input.prospect_emails.map(prospect_email=>ProspectCreateFormData(
      email = Some(prospect_email),
      first_name = None,
      last_name = None,
      custom_fields = Json.obj(),

      list = None,
      company = None,
      city = None,
      country = None,
      timezone = Some(input.timezone),

      state = None,
      job_title = None,
      phone = None,
      phone_2 = None,
      phone_3 = None,
      linkedin_url  = Some("https://www.linkedin.com/in/shubham-kudekar/")
    ))

    val accountProfileInfo = AccountProfileInfo(
      first_name = "Aditya",
      last_name = "Sadana",
      company = None,
      timezone = Some(input.timezone),
      country_code = None,
      mobile_country_code = None,
      mobile_number = None,
      onboarding_phone_number = None,
      twofa_enabled = true,
      has_gauthenticator = true,
      weekly_report_emails = None,
      scheduled_for_deletion_at = None
    )

    val teamMember = TeamMember(
      team_id = input.team_id.id,
      team_name = "smartreach",
      user_id = input.account_id.id,
      ta_id = input.ta_id, // dont send ta_id to frontend / api response, only for internal purpose, its dynamically assigned in AuthUtils
      first_name = None,
      last_name = None,
      email = "<EMAIL>",
      team_role = TeamAccountRole.ADMIN,
      api_key = Some("abcd1234"),
      zapier_key = Some("zapier_key")
    )

    val teamMemberLite = TeamMemberLite(
      user_id = input.account_id.id,
      first_name = None,
      last_name = None,
      email = "<EMAIL>",
      active = true,
      timezone = Some(input.timezone),
      twofa_enabled = true,
      created_at = DateTime.now(),
      user_uuid = AccountUuid(uuid = "uuid"),
      team_role = TeamAccountRole.ADMIN
    )

    val prospectCategoriesInDB = ProspectCategoriesInDB(
      id = input.prospect_categories_custom_do_not_contact,
      name = "Do not contact",
      text_id = "do_not_contact",
      label_color = "#d52728",
      is_custom = false,
      team_id = input.team_id.id,
      rank = ProspectCategoryRank(rank = 2000)
    )

    val prospectCategoriesInDB2 = ProspectCategoriesInDB(
      id = input.prospect_categories_custom_not_categorized,
      name = "Not contacted",
      text_id = "not_contacted",
      label_color = "#d52728",
      is_custom = false,
      team_id = input.team_id.id,
      rank = ProspectCategoryRank(rank = 2000)
    )

    val adminDefaultPermissions = RolePermissionDataDAOV2.defaultRoles(
      role = TeamAccountRole.ADMIN,
      simpler_perm_flag = false
    )

    val rolePermissionData = RolePermissionDataV2.toRolePermissionApi(
      data = adminDefaultPermissions.copy(id = 2)
    )

    val teamAccount = TeamAccount(

      team_id = input.team_id.id,
      org_id = input.org_id.id,

      role_from_db = None, // MUST come from db (option type only for cacheservice error), should not be sent to frontend, only intermediate
      role = Some(rolePermissionData), // should be sent to frontend
      active = true,
      is_actively_used = true,
      team_name = "smartreach",
      total_members = 1,
      access_members = Seq(teamMember),
      all_members = Seq(teamMemberLite),

      prospect_categories_custom = Seq(prospectCategoriesInDB, prospectCategoriesInDB2),
      max_emails_per_prospect_per_day = 100L,
      max_emails_per_prospect_per_week = 1000L,
      max_emails_per_prospect_account_per_day = 97,
      max_emails_per_prospect_account_per_week = 497,

      // ADMIN SETTINGS FOR MULTICAMPAIGN
      // allow_assigning_prospects_to_multiple_campaigns: Boolean, FORCEASSIGNISSUE
      reply_handling = ReplyHandling.PAUSE_SPECIFIC_CAMPAIGN_ON_REPLY,
      created_at = DateTime.now(),
      selected_calendar_data = None,
      team_uuid = TeamUuid("team_bvfbvfroebbkbkvcs")
    )

    val orgCountData = OrgCountDataFixture.orgCountData_default.copy(
      current_sending_email_accounts = input.current_sending_email_accounts,
      max_phone_number_buying_limit_org = SrFeatureFlags.maxPhoneNumberToBuyLimitForOrg(
        calling_flag = Some(false),
        total_email_limit = 50,
      ),
    )

    val orgSettings = OrgSettings(
      enable_ab_testing = true,
      disable_force_send = true,
      bulk_sender = true,
      allow_2fa = true,
      show_2fa_setting = true,
      enforce_2fa = true,

      // for zoho, pipedrive, hubspot crms (salesforce will have a different flag)
      allow_native_crm_integration = true,
        agency_option_allow_changing = false,
        agency_option_show = false
    )

    val orgPlan = OrgPlan(
      new_prospects_paused_till = None,
      is_v2_business_plan = true,


      fs_account_id = None,
      stripe_customer_id = None,
      payment_gateway = None,
      current_cycle_started_at = DateTime.now(),
      next_billing_date = None,

      payment_due_invoice_link = None,
      payment_due_campaign_pause_at = None,

      plan_type = PlanType.PAID,
      plan_name = "ultimate-annual-user-inr-base-v2",
      plan_id = PlanID.ULTIMATE
    )


    val organizationWithCurrentData = OrganizationWithCurrentData(
      id = input.org_id.id,
      name = "smartreach",
      owner_account_id = input.account_id.id,
      counts = orgCountData,
      settings = orgSettings,
      plan = orgPlan,

      is_agency = false,
      trial_ends_at = DateTime.now().plusDays(9),
      error = None,
      error_code = None,
      paused_till = None,

      errors = Seq(),
      warnings = Seq(),

      via_referral = false,

      org_metadata = orgMetadata

    )


    val account = Account(
      id = AccountUuid("account_uuid"),
      internal_id = input.account_id.id,
      email = "<EMAIL>",
      email_verification_code = None,
      email_verification_code_created_at = None,
      created_at = DateTime.now().minusDays(7),

      first_name = None,
      last_name = None,
      company = None,
      timezone = None,

      profile = accountProfileInfo,

      org_role = Some(OrganizationRole.OWNER),

      teams = Seq(teamAccount),
      account_type = AccountType.TEAM,
      org = organizationWithCurrentData,
      active = true,
      email_notification_summary = "",
      account_metadata = AccountMetadata(is_profile_onboarding_done = Some(true)),
      email_verified = true,
      signupType = None,
      account_access = AccountAccess(inbox_access = true),
      calendar_account_data = None
    )

    val campaignStepDataLinkedin = CampaignStepData.LinkedinInmailData(
      subject = Some("Linkedin Inmail Subject Test Campaign - 1"),
      body = "Linkedin Inmail Body Test Campaign - 1"
    )

    val campaignStepDataAutoEmail = CampaignStepData.AutoEmailStep(
      subject = "Email Interagtion test",
      body = "Test"
    )

    val campaignStepVariantCreateOrUpdateEmail = CampaignStepVariantCreateOrUpdate(
      parent_id = 0,
      step_data = campaignStepDataAutoEmail,
      step_delay = 0,
      notes = Some("Auto Email Step"),
      priority = Some(TaskPriority.High),
    )

    val campaignStepVariantCreateOrUpdateLinkedin = CampaignStepVariantCreateOrUpdate(
      parent_id = 0,
      step_data = campaignStepDataLinkedin,
      step_delay = 0,
      notes = Some("Linkedin Inmail Step Note"),
      priority = Some(TaskPriority.High),
    )
    val emailSetting = EmailSetting(
      id = Option(input.email_setting_id),
      uuid = None,
      org_id = input.org_id,
      owner_id = input.account_id,
      owner_uuid = AccountUuid("uuid"),
      team_id = input.team_id,
      team_uuid = TeamUuid("uuid"),
      message_id_suffix = "message",
      email = "email",
      email_address_host = "email",
      service_provider = EmailServiceProvider.GMAIL_API,
        domain_provider = None,
      via_gmail_smtp = None,
      owner_name = "owner_name",
      sender_name = "sender_name",
      first_name = "first_name",
      last_name = "last_name",
      cc_emails = None,
      bcc_emails = None,
      smtp_username = None,
      smtp_password = None,
      smtp_host = None,
      smtp_port = None,
      imap_username = None,
      imap_password = None,
      imap_host = None,
      imap_port = None,
      oauth2_access_token = None,
      oauth2_refresh_token = None,
      oauth2_token_type = None,
      oauth2_token_expires_in = None,
      oauth2_access_token_expires_at = None,
      email_domain = None,
      api_key = None,
      mailgun_region = None,
      quota_per_day = quota_per_day_email_settings,
      reply_handling = ReplyHandling.PAUSE_SPECIFIC_CAMPAIGN_ON_REPLY,
      last_read_for_replies = None,
      latest_email_scheduled_at = None,
      error = None,
      error_reported_at = None,
      paused_till = None,
      signature = "signature",
      created_at = None,
      current_prospect_sent_count_email = 0,
      default_tracking_domain = "domain",
      default_unsubscribe_domain = "domain",
      rep_tracking_host_id = 1,
      tracking_domain_host = None,
      custom_tracking_domain = None,
      custom_tracking_cname_value = None,
      custom_tracking_domain_is_verified = None,
      custom_tracking_domain_is_ssl_enabled = None,
      rep_mail_server_id = 2,
      rep_mail_server_public_ip = "ip",
      rep_mail_server_host = "host",
      rep_mail_server_reverse_dns = None,
      min_delay_seconds = 2,
      max_delay_seconds = 2,
        tag = None,
      campaign_use_status_for_email_setting = CampaignUseStatusForEmailSetting.NoRunningCampaign,
      show_rms_ip_in_frontend = false
    )

    val timezone = "Asia/Kolkata"
    InitializedCreateAndStartCampaignData(
      campaignSettings = campaignSettings,
      createCampaignData = createCampaignData,
      prospectCreateFormData = prospectCreateFormData,
      accountProfileInfo = accountProfileInfo,
      account = account,
      campaignStepVariantCreateOrUpdateLinkedin = campaignStepVariantCreateOrUpdateLinkedin,
      campaignStepVariantCreateOrUpdateEmail = campaignStepVariantCreateOrUpdateEmail,
      emailSetting = emailSetting,
      teamAccount = teamAccount,
      organizationWithCurrentData = organizationWithCurrentData,
      timezone = timezone
    )
  }


}
