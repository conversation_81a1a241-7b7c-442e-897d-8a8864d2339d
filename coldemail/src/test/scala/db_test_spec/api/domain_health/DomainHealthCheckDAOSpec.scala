package db_test_spec.api.domain_health

import api.accounts.TeamId
import api.accounts.models.{AccountId, OrgId}
import api.domain_health.{BlacklistNotificationData, DomainChecks, DomainHealthCheckId}
import api.emails.EmailAddressHost
import app_services.blacklist_monitoring.models.{BlacklistCheckResult, BlacklistCheckStatus}
import db_test_spec.api.accounts.fixtures.NewAccountAndEmailSettingData
import db_test_spec.api.campaigns.test_utils.{CampaignUtils, CreateAndStartCampaignData}
import db_test_spec.api.emails.fixtures.DefaultEmailSettingParametersFixtures.defaultEmailSettingForm
import db_test_spec.api.emails.fixtures.EmailSettingFixtureForIntegrationTest
import db_test_spec.api.{DbTestingBeforeAllAndAfterAll, InitialData}
import org.joda.time.DateTime
import org.scalatest.ParallelTestExecution
import play.api.libs.json.{<PERSON>s<PERSON><PERSON>, <PERSON><PERSON>}

import scala.concurrent.Future
import scala.util.{Failure, Success}

class DomainHealthCheckDAOSpec extends DbTestingBeforeAllAndAfterAll {


    describe("DomainHealthCheckDAO create") {
        it("should insert domains into the table") {

            val domainList: List[EmailAddressHost] = List(EmailAddressHost("gmail.com"),EmailAddressHost("rediffmail.com"))


            val result = domainHealthCheckDAO.createAndUpdatePushedToQueue(domainList = domainList)

            result match {
                case Failure(e) =>
                    println(e)
                    assert(false)
                case Success(domainChecks) =>
                    val isGmailPresent = domainChecks.find(_.domain == "gmail.com")
                    assert(isGmailPresent.isDefined)
            }

        }


        it("should update the fields for existing domains"){

            val domainList1: List[EmailAddressHost] = List(EmailAddressHost("smartreach.io"),EmailAddressHost("example.com"))

            val domainList2: List[EmailAddressHost] = List(EmailAddressHost("smartreach.io"))

            val result1 = domainHealthCheckDAO.createAndUpdatePushedToQueue(domainList = domainList1)

            val result2 = domainHealthCheckDAO.createAndUpdatePushedToQueue(domainList = domainList2)

            result2 match {
                case Failure(exception) =>
                    println(exception)
                    assert(false)

                case Success(domainList) =>
                    println(domainList)
                    val id1 = result1.get.find(_.domain == "smartreach.io").get.id
                    val id2 = domainList.find(_.domain == "smartreach.io").get.id
                    assert(id1 ==id2)
            }


        }

    }

    describe("DomainHealthCheckDAO updateBlacklistResult"){

        it("should update the results") {

            val domainList: List[EmailAddressHost] = List(EmailAddressHost("hotmail.com"), EmailAddressHost("rediffmail.com"))

            val domainChecks = domainHealthCheckDAO.createAndUpdatePushedToQueue(domainList = domainList)

            val domainBlacklistResult: BlacklistCheckResult = BlacklistCheckResult(
                status = BlacklistCheckStatus.PASSED,
                failureDescription = None,
                fullResult = Json.obj()
            )

            val result = domainHealthCheckDAO.updateBlacklistResult(domainBlacklistCheckResult = domainBlacklistResult, domain = "hotmail.com",isFailedForCriticalBlacklist = false)

            result match {
                case Failure(exception) =>
                    assert(false)

                case Success(id) =>
                    val id1 = domainChecks.get.find(_.domain == "hotmail.com").get.id


                    assert(id1 == id)
            }
        }

    }

    describe("DomainHealthCheckDAO getDataForDailyReporting") {
        it("should fetch the domains"){
            val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

            EmailSettingFixtureForIntegrationTest.createEmailSetting(
                orgId = OrgId(initialData.account.org.id),
                accountId = AccountId(initialData.account.internal_id),
                teamId = TeamId(initialData.account.teams.head.team_id),
                taId = initialData.account.teams.head.access_members.head.ta_id,
                emailSettingForm = Option(defaultEmailSettingForm.copy(
                    email = "<EMAIL>"
                ))
            ).get


            val domainList: List[EmailAddressHost] = List(EmailAddressHost("gmail.com"),EmailAddressHost("rediffmail.com"))

            domainHealthCheckDAO.createAndUpdatePushedToQueue(domainList = domainList)

            val domainBlacklistResult: BlacklistCheckResult = BlacklistCheckResult(
                status = BlacklistCheckStatus.FAILED,
                failureDescription = Some("Smartreach Blacklist"),
                fullResult = Json.obj()
            )

            domainList.map { domain =>

                domainHealthCheckDAO.updateBlacklistResult(domainBlacklistCheckResult = domainBlacklistResult, domain = domain.emailAddressHost,isFailedForCriticalBlacklist = false)
            }

                val startOfDay = DateTime.now().withTimeAtStartOfDay()
                val endOfDay = DateTime.now().plusDays(1).withTimeAtStartOfDay().minusSeconds(1)

                val domainBlacklistReport = domainHealthCheckDAO.getDataForDailyReporting(
                    startOfDay = startOfDay,
                    endOfDay = endOfDay
                )


            domainBlacklistReport match {
                case Failure(exception) =>
                    println(exception)
                    assert(false)

                case Success(report) =>
                    println(report)
                    val isRediffDomainFound = report.find(_.domain == "rediffmail.com")

                    assert(isRediffDomainFound.isDefined)
            }

        }
    }

    describe("DomainHealthCheckDAO getBlacklistResultOfDomain"){
        it("should return an list with status as none"){
            val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

            EmailSettingFixtureForIntegrationTest.createEmailSetting(
                orgId = OrgId(initialData.account.org.id),
                accountId = AccountId(initialData.account.internal_id),
                teamId = TeamId(initialData.account.teams.head.team_id),
                taId = initialData.account.teams.head.access_members.head.ta_id,
                emailSettingForm = Option(defaultEmailSettingForm.copy(
                    email = "<EMAIL>"
                ))
            ).get

            val domainList = List("domain.com")


            val result = domainHealthCheckDAO.getBlacklistResultOfDomain(
                domainList = domainList,
                teamId = TeamId(initialData.account.teams.head.team_id)
            )

            println(result)

            result match {
                case Success(res) =>
                assert(res.isEmpty)

                case Failure(exception) =>
                    assert(false)
            }


        }

        it("should return a list with the status") {
            val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

            val emailList = List("<EMAIL>","<EMAIL>")

            emailList.map { email =>

                EmailSettingFixtureForIntegrationTest.createEmailSetting(
                    orgId = OrgId(initialData.account.org.id),
                    accountId = AccountId(initialData.account.internal_id),
                    teamId = TeamId(initialData.account.teams.head.team_id),
                    taId = initialData.account.teams.head.access_members.head.ta_id,
                    emailSettingForm = Option(defaultEmailSettingForm.copy(
                        email = email
                    ))
                ).get
            }

            val domainList: List[EmailAddressHost] = List(EmailAddressHost("sales.in"), EmailAddressHost("education.in"))

            val domainToGetBlacklistRecord = List("sales.in","education.in","marketing.com")

            val domainChecks = domainHealthCheckDAO.createAndUpdatePushedToQueue(domainList = domainList)

            val domainBlacklistResult: BlacklistCheckResult = BlacklistCheckResult(
                status = BlacklistCheckStatus.PASSED,
                failureDescription = None,
                fullResult = Json.obj()
            )

            domainList.map { domain =>

                domainHealthCheckDAO.updateBlacklistResult(domainBlacklistCheckResult = domainBlacklistResult, domain = domain.emailAddressHost,isFailedForCriticalBlacklist = false)

            }

            val result = domainHealthCheckDAO.getBlacklistResultOfDomain(
                domainList = domainToGetBlacklistRecord,
                teamId = TeamId(initialData.account.teams.head.team_id)
            )

            result match {
                case Success(res) =>
                    println(res)
                    val blacklistStatusForGmail = res.find(_.domain == "sales.in").get.domain_blacklist_status
                    assert(blacklistStatusForGmail == BlacklistCheckStatus.PASSED)

                case Failure(exception) =>
                    assert(false)
            }


        }
    }

    describe("DomainHealthCheck getDomainBlacklistReport"){
        it("should return the blacklist report of the domain"){
            val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

            EmailSettingFixtureForIntegrationTest.createEmailSetting(
                orgId = OrgId(initialData.account.org.id),
                accountId = AccountId(initialData.account.internal_id),
                teamId = TeamId(initialData.account.teams.head.team_id),
                taId = initialData.account.teams.head.access_members.head.ta_id,
                emailSettingForm = Option(defaultEmailSettingForm.copy(
                    email = "<EMAIL>"
                ))
            ).get


            val domainList = List(EmailAddressHost("cricinfo.in"))

            val domainBlacklistResult: BlacklistCheckResult = BlacklistCheckResult(
                status = BlacklistCheckStatus.PASSED,
                failureDescription = None,
                fullResult = Json.obj(
                    "blacklist status" -> "passed"
                )
            )

            domainHealthCheckDAO.createAndUpdatePushedToQueue(domainList)

            domainHealthCheckDAO.updateBlacklistResult(domainBlacklistResult,"cricinfo.in",false)

            val result = domainHealthCheckDAO.getDomainBlacklistReport("cricinfo.in",TeamId(initialData.account.teams.head.team_id))

            result match {
                case Success(report) =>
                    println(report)
                    assert(report.get.team_id == TeamId(initialData.account.teams.head.team_id))

                case Failure(exception) =>
                    println(exception)
                    assert(false)
            }
        }
    }

    describe("getUserDataForSendingBlacklistNotification"){
        it("should fetch account data for domain with campaign running"){
            val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

            val accountDetailsForDomainWithRunningCampaigns = for {
                _: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
                    initialData = initialData,
                    generateProspectCountIfNoGivenProspect = 4
                )

                result:List[BlacklistNotificationData] <- Future.fromTry(domainHealthCheckDAO.getUserDataForSendingBlacklistNotification("gmail.com"))
            }yield{
                result
            }

            accountDetailsForDomainWithRunningCampaigns.map { userAccountDetail =>
              println(userAccountDetail)
                val gmailUserDetail = userAccountDetail.find(_.domain=="gmail.com")
                if(gmailUserDetail.isDefined){
                    assert(true)
                }else {
                    assert(false)
                }


            }.recover { e =>
                println(e)
                assert(false)
            }
        }
    }


}
