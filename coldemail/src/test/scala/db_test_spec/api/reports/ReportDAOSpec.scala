package db_test_spec.api.reports

import api.AppConfig
import api.accounts.TeamId
import api.accounts.models.AccountId
import api.campaigns.models.{CampaignTagId, SendEmailFromCampaignDetails, StepDetails}
import api.campaigns.services.CampaignId
import api.emails.{EmailThreadUpdateLatestEmailData, SendNewManualEmailV3}
import api.prospects.models.ProspectListId
import api.reports.{CampaignStats, CampaignStatsByStep, CampaignStatsByTime, CampaignStatsByVariant, StatsInputQuery, StatsTimePeriod, TimewiseReportInterval, TotalEmailSentReportData, TotalEmailSentReportTimewiseData}
import api.reports.models.{LinkedinTasksCountByType, ProgressbarData, SrReportChartData, SrReportChartSeries, SrReportChartType, SrReportType, TasksAndEmailsSentChangeInPercentage, TasksAndEmailsSentCounts}
import api.team_inbox.model.{FolderType, ReplySentimentType}
import api.team_inbox.service.ReplySentimentUuid
import db_test_spec.api.accounts.fixtures.{EmailScheduledNewFixture, NewAccountAndEmailSettingData}
import db_test_spec.api.{AppSpecFixture, DbTestingBeforeAllAndAfterAll, InitialData}
import db_test_spec.api.campaigns.CampaignCreationFixtureForIntegrationTest
import db_test_spec.api.campaigns.test_utils.{CampaignUtils, CreateAndStartCampaignData}
import db_test_spec.api.scheduler.fixtures.DefaultParametersFixtureForInitializingDataForReScheduling
import eventframework.ProspectObject
import io.smartreach.esp.api.emails.IEmailAddress
import org.joda.time.DateTime
import play.api.libs.json.{JsObject, Json}
import utils.{SRLogger, StringUtils}
import utils.helpers.LogHelpers

import scala.concurrent.Future
import scala.util.Try
import scala.util.{Failure, Success}

class ReportDAOSpec extends DbTestingBeforeAllAndAfterAll {

  given logger: SRLogger = new SRLogger("[ReportDAOSpec] ::")

  lazy val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

  describe("Testing ReportDAO.getProspectsContactedReportQuery") {
    it("should get Prospects Contacted info") {
      CampaignCreationFixtureForIntegrationTest.createDefaultCampaign(initialData = initialData)
        .flatMap(campaignCreationData => {
          Future.fromTry {
            reportDAO.getProspectsContactedTimeseriesForCampaign(
              campaignIds = Seq(CampaignId(campaignCreationData.campaign.id)),
              teamId = TeamId(campaignCreationData.campaign.team_id),
              accountIds = Seq(AccountId(5L), AccountId(17L)),
              prospectListIds = Seq(7L, 19L).map(a => ProspectListId(a)),
              campaignTagIds = Seq(CampaignTagId(11L), CampaignTagId(13L)),
              dayWeek = TimewiseReportInterval.DAY,
              from = 1712995200L,
              till = 1713599999L
            )
          }
        })
        .map(pc => {
          println(pc)
          assert(pc.length == 7)
          assert(pc.map(_.prospects_contacted).sum == 0)
        })
        .recover {
          case e =>
            println(e.toString)
            assert(false)
        }
    }

    it("should get Prospects Contacted grouped by Channel info") {
      CampaignCreationFixtureForIntegrationTest.createDefaultCampaign(initialData = initialData)
        .flatMap(campaignCreationData => {
          Future.fromTry {
            reportDAO.getProspectsContactedReportByChannelForCampaign(
              campaignIds = Seq(CampaignId(campaignCreationData.campaign.id)),
              teamId = TeamId(campaignCreationData.campaign.team_id),
              accountIds = Seq(AccountId(5L), AccountId(17L)),
              prospectListIds = Seq(7L, 19L).map(a => ProspectListId(a)),
              campaignTagIds = Seq(CampaignTagId(11L), CampaignTagId(13L)),
              dayWeek = TimewiseReportInterval.DAY,
              from = 1712995200L,
              till = 1713599999L
            )
          }
        })
        .map(pc => {
          println(pc)
          assert(pc.length == 7)
          assert(pc.map(_.prospects_contacted).sum == 0)
          assert(pc.map(_.channel_type.isEmpty).forall(identity))
        })
        .recover {
          case e =>
            println(e.toString)
            assert(false)
        }
    }
  }

  describe("Testing ReportService.getProspectsContactedTimewiseReportByChannel") {
    it("should send prospects contacted for all channels") {
      CampaignCreationFixtureForIntegrationTest.createDefaultCampaign(initialData = initialData)
        .flatMap(campaignCreationData => {
          reportService.getProspectsContactedTimewiseReportByChannel(
            campaignIds = Seq(CampaignId(campaignCreationData.campaign.id)),
            teamId = TeamId(campaignCreationData.campaign.team_id),
            accountIds = Seq(AccountId(5L), AccountId(17L)),
            prospectListIds = Seq(7L, 19L).map(a => ProspectListId(a)),
            campaignTagIds = Seq(CampaignTagId(11L), CampaignTagId(13L)),
            dayWeek = TimewiseReportInterval.DAY,
            from = 1712995200L,
            till = 1713599999L
          )
        })
        .map(reportAndCounts => {
          reportAndCounts.report match {
            case data: SrReportChartData.BarChartData =>
              assert(data.report_type == SrReportType.ProspectsContactedByChannel)
              assert(data.y_axis == List(
                SrReportChartSeries("Email", List(0, 0, 0, 0, 0, 0, 0, 0), AppConfig.reportEntityColors("Email")),
                SrReportChartSeries("Linkedin", List(0, 0, 0, 0, 0, 0, 0, 0), AppConfig.reportEntityColors("Linkedin")),
                SrReportChartSeries("Call", List(0, 0, 0, 0, 0, 0, 0, 0), AppConfig.reportEntityColors("Call")),
                SrReportChartSeries("Whatsapp", List(0, 0, 0, 0, 0, 0, 0, 0), AppConfig.reportEntityColors("Whatsapp")),
                SrReportChartSeries("SMS", List(0, 0, 0, 0, 0, 0, 0, 0), AppConfig.reportEntityColors("SMS")),
                SrReportChartSeries("Independent", List(0, 0, 0, 0, 0, 0, 0, 0), AppConfig.reportEntityColors("Independent")),
                SrReportChartSeries("General", List(0, 0, 0, 0, 0, 0, 0, 0), AppConfig.reportEntityColors("General"))
              ))

            case data: SrReportChartData.LineChartData =>
              assert(false)

            case data: SrReportChartData.ProgressBarChartData =>
              assert(false)
          }
        })
        .recover {
          case e =>
            println(e.toString)
            assert(false)
        }
    }
  }

  describe("Testing ReportService.getTotalTasksCompletedCountsByChannel") {
    it("should send tasks completed counts by channel") {
      CampaignCreationFixtureForIntegrationTest.createDefaultCampaign(initialData = initialData)
        .flatMap(campaignCreationData => {
          reportService.getTotalTasksCompletedCountsByChannel(
            campaignIds = Seq(CampaignId(campaignCreationData.campaign.id)),
            teamId = TeamId(campaignCreationData.campaign.team_id),
            accountIds = Seq(AccountId(0)),
            prospectListIds = Seq(0).map(ProspectListId(_)),
            compareFrom = Some(123456L),
            compareTill = Some(654321L),
            from = 1712995200L,
            till = 1713599999L
          )
        })
        .map(tasksAndEmailsSentCountAndChange => {
          assert(tasksAndEmailsSentCountAndChange.sent_counts == TasksAndEmailsSentCounts(
            prospects_contacted = 0,
            linkedin_tasks_completed = 0,
            call_tasks_completed = 0,
            total_tasks_completed = 0,
            email_sent_count = 0
          ))

          assert(tasksAndEmailsSentCountAndChange.change_in_percentage.contains(TasksAndEmailsSentChangeInPercentage(
            prospects_contacted_change = 0,
            linkedin_tasks_change = 0,
            call_tasks_change = 0,
            total_tasks_change = 0,
            email_sent_change = 0
          )))
        })
        .recover {
          case e =>
            println(e.toString)
            assert(false)
        }
    }
  }

  describe("Testing ReportService.getProspectRepliesCount") {
    it("should return prospect replies in form of chart data") {
      CampaignCreationFixtureForIntegrationTest.createDefaultCampaign(initialData = initialData)
        .flatMap(campaignCreationData => {
          reportService.getProspectRepliesCount(
            campaignIds = Seq(CampaignId(campaignCreationData.campaign.id)),
            teamId = TeamId(campaignCreationData.campaign.team_id),
            dayWeek = TimewiseReportInterval.DAY,
            accountIds = Seq(AccountId(5L), AccountId(17L)),
            prospectListIds = Seq(7L, 19L).map(a => ProspectListId(a)),
            campaignTagIds = Seq(CampaignTagId(11L), CampaignTagId(13L)),
            compareFrom = Some(12345),
            compareTill = Some(54321),
            from = 1712995200L,
            till = 1713599999L
          )
        })
        .map (reportAndChange => {
          reportAndChange.report match {
            case data: SrReportChartData.BarChartData =>
              assert(data.report_label == "Prospect replies")
              assert(data.y_axis.map(_.data.sum).sum == 0)

              val chartLegend = data.y_axis.map(_.name)
              val res = ReplySentimentType.getAllReplySentimentTypes().map(replySentiment => {
                chartLegend.contains(replySentiment.display_name)
              })
              assert(res.forall(identity))

              assert(reportAndChange.change_in_percentage.contains(0))

            case _: SrReportChartData.LineChartData =>
              assert(false)

            case data: SrReportChartData.ProgressBarChartData =>
              assert(false)
          }
        })
        .recover {
          case e =>
            println(e.toString)
            assert(false)
        }
    }
  }

  describe("Testing ReportService.getTopCampaignsByProspectsContacted") {
    it("should return top campaigns in form of chart data") {
      reportService.getTopCampaignsByProspectsContacted(
          teamId = TeamId(3L),
          from = 1712995200L,
          till = 1713599999L
        )
        .map(reportAndChange => {
          reportAndChange.report match {
            case _: SrReportChartData.BarChartData =>
              assert(false)

            case _: SrReportChartData.LineChartData =>
              assert(false)

            case data: SrReportChartData.ProgressBarChartData =>
              assert(data.report_label == "Top campaigns by prospects contacted")
              assert(data.report_type == SrReportType.TopCampaigns)
              assert(data.data == List())
          }
        })
        .recover {
          case e =>
            println(e.toString)
            assert(false)
        }
    }
  }

  describe("Testing ReportService.getTopCampaignsByProspectsContacted") {
    it("should return top team members in form of chart data") {
      reportService.getTopTeamMembersByProspectsContacted(
          teamId = TeamId(3L),
          from = 1712995200L,
          till = 1713599999L
        )
        .map(reportAndChange => {
          reportAndChange.report match {
            case _: SrReportChartData.BarChartData =>
              assert(false)

            case _: SrReportChartData.LineChartData =>
              assert(false)

            case data: SrReportChartData.ProgressBarChartData =>
              assert(data.report_label == "Top team members by prospects contacted")
              assert(data.report_type == SrReportType.TopTeamMembers)
              assert(data.data == List())
          }
        })
        .recover {
          case e =>
            println(e.toString)
            assert(false)
        }
    }
  }

  describe("Testing ReportService.getTopProspectListsByProspectsContacted") {
    it("should return top prospect lists by Prospects Contacted") {
      reportService.getTopProspectListsByProspectsContacted(
        teamId = TeamId(3L),
        from = 1712995200L,
        till = 1713599999L
      )
        .map(reportDataAndChange => {
          reportDataAndChange.report match {
            case _: SrReportChartData.BarChartData => assert(false)
            case _: SrReportChartData.LineChartData => assert(false)
            case data: SrReportChartData.ProgressBarChartData =>
              assert(data.report_type == SrReportType.TopProspectLists)
              assert(data.report_label == "Top prospect lists by prospects contacted")
              assert(data.data == List())
          }
        })
    }
  }

  describe("Testing ReportService.fetchLinkedinTasksByStatusTimeseries") {
    it("should return Prospects Assigned and Due / Completed Linkedin Tasks timeseries counts") {
      reportService.fetchLinkedinTasksByStatusTimeseries(
        campaignIds = Seq(CampaignId(3L)),
        teamId = TeamId(initialData.account.teams.head.team_id),
        accountIds = Seq(AccountId(5L)),
        prospectListIds = Seq(7L).map(a => ProspectListId(a)),
        campaignTagIds = Seq(CampaignTagId(11L)),
        reportType = SrReportType.LinkedinTasks,
        dayWeek = TimewiseReportInterval.DAY,
        compareFrom = Some(1712995200L),
        compareTill = Some(1713599999L),
        from = 1712995200L,
        till = 1713599999L
      )
        .map(res => {
          res.report match {
            case data: SrReportChartData.BarChartData =>
              assert(data.report_label == "Linkedin tasks")
              assert(data.report_type == SrReportType.LinkedinTasks)
              assert(data.y_axis == List(
                SrReportChartSeries("Assigned", List(0, 0, 0, 0, 0, 0, 0, 0), AppConfig.reportEntityColors("Assigned")),
                SrReportChartSeries("In queue", List(0, 0, 0, 0, 0, 0, 0, 0), AppConfig.reportEntityColors("In queue")),
                SrReportChartSeries("Completed", List(0, 0, 0, 0, 0, 0, 0, 0), AppConfig.reportEntityColors("Completed")),
              ))

            case _: SrReportChartData.LineChartData =>
              assert(false)

            case _: SrReportChartData.ProgressBarChartData =>
              assert(false)
          }
        })
    }

  }

  describe("Testing ReportService.getTotalLinkedinTasksByTypes") {
    it("should get Linkedin Tasks Counts By Types") {
      reportService.getTotalLinkedinTasksByTypes(
          campaignIds = Seq(CampaignId(3L)),
          teamId = TeamId(initialData.account.teams.head.team_id),
          accountIds = Seq(AccountId(5L)),
          prospectListIds = Seq(7L).map(a => ProspectListId(a)),
          campaignTagIds = Seq(CampaignTagId(11L)),
          compareFrom = Some(1712995200L),
          compareTill = Some(1713599999L),
          from = 1712995200L,
          till = 1713599999L
        )
        .map(res => {
          assert(res.counts == LinkedinTasksCountByType(
            total_tasks = 0,
            connection_requests = 0,
            messages = 0,
            inmails = 0,
            view_profiles = 0,
            manual_tasks = 0,
            automated_tasks = 0
          ))

          assert(res.change_in_percentage.contains(LinkedinTasksCountByType(
            total_tasks = 0,
            connection_requests = 0,
            messages = 0,
            inmails = 0,
            view_profiles = 0,
            manual_tasks = 0,
            automated_tasks = 0
          )))
        })
        .recover {
          case e =>
            println(e.toString)
            logger.error("Failed", e)
            assert(false)
        }
    }
  }

  describe("Testing ReportService.getProspectsAddedTimeseriesReport") {
    it("should successfully fetch timeseries report of Prospects Added") {
      reportService.getProspectsAddedTimeseriesReport(
        campaignIds = Seq(CampaignId(0L)),
        teamId = TeamId(12L),
        accountIds = Seq(AccountId(0L)),
        prospectListIds = Seq(0L).map(a => ProspectListId(a)),
        dayWeek = TimewiseReportInterval.DAY,
        campaignTagIds = Seq(CampaignTagId(0L)),
        compareFrom = Some(1712995200L),
        compareTill = Some(1713599999L),
        from = 1712995200L,
        till = 1713599999L
      )
        .map(res => {
          res.report match {
            case data: SrReportChartData.BarChartData =>
              assert(data.report_type == SrReportType.ProspectsAdded)
              assert(data.y_axis == List(
                SrReportChartSeries(
                  name = "Prospects added",
                  data = List(0, 0, 0, 0, 0, 0, 0, 0),
                  color = AppConfig.reportEntityColors("Prospects added")
                )
              ))

            case _ =>
              assert(false)
          }
        })
    }
  }

  describe("Testing ReportService.getEngagementTouchpointsReport") {
    it("should successfully fetch timeseries engagement touchpoints by channel type") {
      reportService.getEngagementTouchpointsReport(
          campaignIds = Seq(CampaignId(0L)),
          teamId = TeamId(12L),
          accountIds = Seq(AccountId(0L)),
          prospectListIds = Seq(0L).map(a => ProspectListId(a)),
          dayWeek = TimewiseReportInterval.DAY,
          campaignTagIds = Seq(CampaignTagId(0L)),
          compareFrom = Some(1712995200L),
          compareTill = Some(1713599999L),
          from = 1712995200L,
          till = 1713599999L
        )
        .map(res => {
          res.report match {
            case data: SrReportChartData.BarChartData =>
              assert(data.report_type == SrReportType.EngagementTouchpoints)
              assert(data.y_axis == List(
                SrReportChartSeries("Email", List(0, 0, 0, 0, 0, 0, 0, 0), AppConfig.reportEntityColors("Email")),
                SrReportChartSeries("Linkedin", List(0, 0, 0, 0, 0, 0, 0, 0), AppConfig.reportEntityColors("Linkedin")),
                SrReportChartSeries("Call", List(0, 0, 0, 0, 0, 0, 0, 0), AppConfig.reportEntityColors("Call")),
                SrReportChartSeries("Whatsapp", List(0, 0, 0, 0, 0, 0, 0, 0), AppConfig.reportEntityColors("Whatsapp")),
                SrReportChartSeries("SMS", List(0, 0, 0, 0, 0, 0, 0, 0), AppConfig.reportEntityColors("SMS")),
                SrReportChartSeries("Independent", List(0, 0, 0, 0, 0, 0, 0, 0), AppConfig.reportEntityColors("Independent")),
                SrReportChartSeries("General", List(0, 0, 0, 0, 0, 0, 0, 0), AppConfig.reportEntityColors("General"))
              ))

            case _ =>
              assert(false)
          }
        })
    }
  }

  describe("Testing ReportService.getTopCampaignsByProspectReplies") {
    it("should successfully give top campaigns based on Prospect Replies") {
      reportService.getTopCampaignsByProspectReplies(
        from = 1712995200L,
        till = 1713599999L,
        teamId = TeamId(13L)
      )
        .map(res => {
          res.report match {
            case data: SrReportChartData.ProgressBarChartData =>
              assert(data.report_type == SrReportType.TopCampaignsByProspectReplies)
              assert(data.data == List())

            case _ => assert(false)
          }
        })
        .recover {
          case e =>
            println(e.toString)
            assert(false)
        }
    }
  }

  describe("Testing ReportService.getTopEmailSendingAccountsReport") {
    it("should successfully fetch top email sending accounts") {
      reportService.getTopEmailSendingAccountsReport(
        from = 1712995200L,
        till = 1713599999L,
        teamId = TeamId(13L)
      )
        .map(reportData => {
          reportData.report match {
            case data: SrReportChartData.ProgressBarChartData =>
              assert(data.report_type == SrReportType.TopEmailSendingAccounts)
              assert(data.data == List())

            case _ =>
              assert(false)
          }
        })
    }
  }

  describe("Testing ReportService.getTopChannelEngagementTouchpoints") {
    it("should successfully fetch top linkedin engagement accounts") {
      reportService.getTopChannelEngagementTouchpoints(
          reportType = SrReportType.TopLinkedinEngagementTouchpoints,
          from = 1712995200L,
          till = 1713599999L,
          teamId = TeamId(13L)
        )
        .map(reportData => {
          reportData.report match {
            case data: SrReportChartData.ProgressBarChartData =>
              assert(data.report_type == SrReportType.TopLinkedinEngagementTouchpoints)
              assert(data.data == List())

            case _ =>
              assert(false)
          }
        })
    }

    it("should successfully fetch top calling agents") {
      reportService.getTopChannelEngagementTouchpoints(
          reportType = SrReportType.TopCallingAgents,
          from = 1712995200L,
          till = 1713599999L,
          teamId = TeamId(13L)
        )
        .map(reportData => {
          reportData.report match {
            case data: SrReportChartData.ProgressBarChartData =>
              assert(data.report_type == SrReportType.TopCallingAgents)
              assert(data.data == List())

            case _ =>
              assert(false)
          }
        })
    }
  }

  describe("Testing ReportService.getTotalCallsTimeseriesReport") {
    it("should successfully number of calls Timewise report") {
      reportService.getTotalCallsTimeseriesReport(
          campaignIds = Seq(CampaignId(0L)),
          teamId = TeamId(12L),
          accountIds = Seq(AccountId(0L)),
          prospectListIds = Seq(0L).map(a => ProspectListId(a)),
          dayWeek = TimewiseReportInterval.DAY,
          campaignTagIds = Seq(CampaignTagId(0L)),
          compareFrom = Some(1712995200L),
          compareTill = Some(1713599999L),
          timezone = Some("Asia/Kolkata"),
          from = 1712995200L,
          till = 1713599999L
        )
        .map(reportData => {
          reportData.report match {
            case data: SrReportChartData.BarChartData =>
              println(data)
              assert(data.report_type == SrReportType.TotalCalls)
              assert(data.y_axis == List(
                SrReportChartSeries(name = "Total calls", data = List(0, 0, 0, 0, 0, 0, 0, 0), color = AppConfig.reportEntityColors("Total calls"))
              ))

            case _ =>
              assert(false)
          }
        })
    }
  }

  describe("Testing ReportService.getCallsDialedReport") {
    it("should successfully number of calls Dialed Timewise report") {
      reportService.getCallsDialedReport(
          campaignIds = Seq(CampaignId(0L)),
          teamId = TeamId(12L),
          accountIds = Seq(AccountId(0L)),
          prospectListIds = Seq(0L).map(a => ProspectListId(a)),
          dayWeek = TimewiseReportInterval.DAY,
          campaignTagIds = Seq(CampaignTagId(0L)),
          compareFrom = Some(1712995200L),
          compareTill = Some(1713599999L),
          timezone = Some("Asia/Kolkata"),
          from = 1712995200L,
          till = 1713599999L
        )
        .map(reportData => {
          reportData.report match {
            case data: SrReportChartData.BarChartData =>
              assert(data.report_type == SrReportType.CallsDialed)
              assert(data.y_axis == List(
                SrReportChartSeries(name = "Calls dialed", data = List(0, 0, 0, 0, 0, 0, 0, 0), color = AppConfig.reportEntityColors("Calls dialed"))
              ))

            case _ =>
              assert(false)
          }
        })
    }
  }

  describe("Testing ReportService.getCallsConnectedTimewiseReport") {
    it("should successfully number of calls Connected Timewise report") {
      reportService.getCallsConnectedTimewiseReport(
          campaignIds = Seq(CampaignId(0L)),
          teamId = TeamId(12L),
          accountIds = Seq(AccountId(0L)),
          prospectListIds = Seq(0L).map(a => ProspectListId(a)),
          dayWeek = TimewiseReportInterval.DAY,
          campaignTagIds = Seq(CampaignTagId(0L)),
          compareFrom = Some(1712995200L),
          compareTill = Some(1713599999L),
          timezone = Some("Asia/Kolkata"),
          from = 1712995200L,
          till = 1713599999L
        )
        .map(reportData => {
          reportData.report match {
            case data: SrReportChartData.BarChartData =>
              assert(data.report_type == SrReportType.CallsConnected)
              assert(data.y_axis == List(
                SrReportChartSeries(name = "Calls connected", data = List(0, 0, 0, 0, 0, 0, 0, 0), color = AppConfig.reportEntityColors("Calls connected"))
              ))

            case _ =>
              assert(false)
          }
        })
    }
  }

  describe("Testing ReportService.getTotalCallsDurationTimewiseReport") {
    it("should successfully total calls duration Timewise report") {
      reportService.getTotalCallsDurationTimewiseReport(
          campaignIds = Seq(CampaignId(0L)),
          teamId = TeamId(12L),
          accountIds = Seq(AccountId(0L)),
          prospectListIds = Seq(0L).map(a => ProspectListId(a)),
          dayWeek = TimewiseReportInterval.DAY,
          campaignTagIds = Seq(CampaignTagId(0L)),
          compareFrom = Some(1712995200L),
          compareTill = Some(1713599999L),
          timezone = Some("Asia/Kolkata"),
          from = 1712995200L,
          till = 1713599999L
        )
        .map(reportData => {
          reportData.report match {
            case data: SrReportChartData.BarChartData =>
              assert(data.report_type == SrReportType.TotalCallDuration)
              assert(data.y_axis == List(
                SrReportChartSeries(name = "Total call duration", data = List(0, 0, 0, 0, 0, 0, 0, 0), color = AppConfig.reportEntityColors("Total call duration"))
              ))

            case _ =>
              assert(false)
          }
        })
    }
  }

  describe("Testing ReportService.getAverageCallDurationTimewiseReport") {
    it("should successfully average duration of calls Timewise report") {
      reportService.getAverageCallDurationTimewiseReport(
          campaignIds = Seq(CampaignId(0L)),
          teamId = TeamId(12L),
          accountIds = Seq(AccountId(0L)),
          prospectListIds = Seq(0L).map(a => ProspectListId(a)),
          dayWeek = TimewiseReportInterval.DAY,
          campaignTagIds = Seq(CampaignTagId(0L)),
          compareFrom = Some(1712995200L),
          compareTill = Some(1713599999L),
          timezone = Some("Asia/Kolkata"),
          from = 1712995200L,
          till = 1713599999L
        )
        .map(reportData => {
          reportData.report match {
            case data: SrReportChartData.BarChartData =>
              assert(data.report_type == SrReportType.AverageCallDuration)
              assert(data.y_axis == List(
                SrReportChartSeries(name = "Avg. call duration", data =  List(0, 0, 0, 0, 0, 0, 0, 0), color = AppConfig.reportEntityColors("Avg. call duration"))
              ))

            case _ =>
              assert(false)
          }
        })
    }
  }


  describe("getSenderEmailReport") {
    it("should give success for without groupByDomain") {
      val result = reportDAO.getSenderEmailReport(
        accountIds = Set(1, 2, 3),
        tid = 321,
        data = StatsInputQuery(
          time_period = StatsTimePeriod.TODAY,
          campaign_ids = None,
          campaign_tag_ids = None,
          list_ids = None,
          account_ids = Seq(1, 2, 3),
          best_time_filter = None,
          fromDay = None,
          tillDay = None,
          from = ********,
          till = ********,
          page = None,
          is_domain_group = None
        )
      )
      assert(result.isSuccess)
    }
    it("should give success for with groupByDomain") {
      val result = reportDAO.getSenderEmailReport(
        accountIds = Set(1, 2, 3),
        tid = 321,
        data = StatsInputQuery(
          time_period = StatsTimePeriod.TODAY,
          campaign_ids = None,
          campaign_tag_ids = None,
          list_ids = None,
          account_ids = Seq(1, 2, 3),
          best_time_filter = None,
          fromDay = None,
          tillDay = None,
          from = ********,
          till = ********,
          page = None,
          is_domain_group = Some(true)
        )
      )
      assert(result.isSuccess)
    }
  }

  describe("getAccountStatsOverallOnly") {
    it("should return success") {
      val res: Option[CampaignStats] = reportDAO.getAccountStatsOverallOnly(
        accountIds = Set(initialData.account.internal_id),
        teamId = initialData.head_team_id,
        data = StatsInputQuery(
          time_period = StatsTimePeriod.MONTH,
          campaign_ids = None,
          campaign_tag_ids = None,
          list_ids = None,
          account_ids = Seq(initialData.account.internal_id),
          best_time_filter = None,
          fromDay = None,
          tillDay = None,
          from = 1712995200L,
          till = 1713599999L,
          page = Some(1),
          is_domain_group = Some(true)
        ),
        positive_reply_sentiments = List(),
          showWithManualData = false
      )

      res match {
        case None => assert(false)
        case Some(stats) => assert(stats.total_prospects > 1)
      }
    }
  }

  describe("getAccountStatsTimewiseWeeklyOnly") {
    it("should return success") {
      val res: JsObject = reportDAO.getAccountStatsTimewiseWeeklyOnly(
        accountIds = Set(initialData.account.internal_id),
        teamId = initialData.head_team_id,
        dayWeek = "week",
        data = StatsInputQuery(
          time_period = StatsTimePeriod.WEEK,
          campaign_ids = None,
          campaign_tag_ids = None,
          list_ids = None,
          account_ids = Seq(initialData.account.internal_id),
          best_time_filter = None,
          fromDay = None,
          tillDay = None,
          from = 1712995200L,
          till = 1713599999L,
          page = None,
          is_domain_group = Some(true)
        ),
        positive_reply_sentiments = List(),
          showWithManualData= false
      )
      val interval = (res \ "interval").as[String]

      assert(interval == "week")
    }
  }

  describe("getEmailsSentReportOverall") {
    it("should return success") {
      val res: Try[TotalEmailSentReportData] = reportDAO.getEmailsSentReportOverall(
        accountIds = Seq(initialData.account.internal_id),
        teamId = initialData.head_team_id,
        fromMillis = 1712995200L,
        tillMillis = 1713599999L,
        listIds = Seq(),
        campaignIds = Seq(),
          showWithManualData= false
      )
      assert(res.isSuccess && res.get.total_opted_out == 0)
    }
  }

  describe("getEmailsSentReportTimewise") {
    it("should return success") {
      val res: Try[List[TotalEmailSentReportTimewiseData]] = reportDAO.getEmailsSentReportTimewise(
        accountIds = Seq(initialData.account.internal_id),
        teamId = initialData.head_team_id,
        fromMillis = 1712995200L,
        tillMillis = 1713599999L,
        dayWeek = TimewiseReportInterval.WEEK,
        listIds = Seq(),
        campaignIds = Seq(),
          showWithManualData = false
      )
      assert(res.isSuccess && res.get.nonEmpty)
    }
  }

  describe("getOverallCampaignStatsV2") {
    it("should return success") {
      val res: Option[CampaignStats] = reportDAO.getOverallCampaignStatsV2(
        accountIds = Set(initialData.account.internal_id),
        team_id = initialData.head_team_id,
        fromMillis = 1712995200L,
        tillMillis = 1713599999L,
        positive_reply_sentiments = List()
      )
      res match {
        case None => assert(false)
        case Some(stats) => assert(stats.total_prospects >= 1)
      }
    }
  }

  describe("getCampaignStatsByTimeWeeklyV2") {
    it("should return success") {
      val res: List[CampaignStatsByTime] = reportDAO.getCampaignStatsByTimeWeeklyV2(
        accountIds = Set(initialData.account.internal_id),
        teamId = initialData.head_team_id,
        fromMillis = 1712995200L,
        tillMillis = 1713599999L,
        dayWeek = "week",
        listIds = Seq(),
        campaignIds = Seq(),
        positive_reply_sentiments = List()
      )
      assert(res.nonEmpty)
    }
  }

  describe("getStatsV2") {
    it("should return success") {

      val updateData = EmailThreadUpdateLatestEmailData(
        email_thread_id = 1L,
        by_account = true,
        latest_email_id = 30L,
        sent_at = DateTime.now(),
        latest_sent_by_admin_at = Some(DateTime.now()),
        by_prospect = false,
        folderType = FolderType.PROSPECTS
      )

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
      val outlookConvId: Option[String] = Some("outlook_conversation_id+jan25")
      var campaignId: Long = 0

      val result = for {

        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 4
        )
        prospects: Seq[ProspectObject] <- Future {
          campaignId = createAndStartCampaignData.campaign.id
          initialData.prospectsResult
        }

        (duplicate: Long, master: Long) <- Future {
          (prospects.head.id, prospects.last.id)
        }

        addingEmailScheduled <- Future.fromTry {
          emailScheduledDAOService.saveEmailsToBeScheduledAndUpdateCampaignDataV2(
            emailsToBeScheduled = Vector(EmailScheduledNewFixture.generateEmailScheduledNew3.copy(
              campaign_id = Some(createAndStartCampaignData.campaign.id),
              step_id = createAndStartCampaignData.campaign.head_step_id,
              from_email = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.sender_email,
              scheduled_from_campaign = true,
              is_opening_step = true,
              sender_email_settings_id = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.sender_email_setting_id.emailSettingId,
              team_id = createAndStartCampaignData.createCampaign.team_id,
              account_id = createAndStartCampaignData.createCampaign.owner_id,
              receiver_email_settings_id = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.receiver_email_setting_id.emailSettingId,
              campaign_email_settings_id = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.id,
              prospect_id = Some(prospects.head.id),
              base_body = "body"

            )
            ),
            campaign_email_setting_id = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.id,
            emailSendingFlow = None,
            Logger = Logger
          )
        }
        emailSent <- Future.fromTry {
          emailSenderService.onEmailSent(
            emailSentId = addingEmailScheduled.head.email_scheduled_id,
            data = DefaultParametersFixtureForInitializingDataForReScheduling.defaultEmailToBeSent(
              emailSetting = initialData.emailSetting.get,
              campaign = createAndStartCampaignData.campaign
            ).copy(outlook_conversation_id = outlookConvId, subject = "subject25"),
            accountId = createAndStartCampaignData.createCampaign.owner_id,
            sendEmailFromCampaignDetails = Some(SendEmailFromCampaignDetails(
              campaign_id = createAndStartCampaignData.createCampaign.id,
              campaign_name = createAndStartCampaignData.createCampaign.name,
              // stepDetails can be none when email is being sent manually from Inbox.
              // Example: sendNewEmailManually in InboxV3Service
              stepDetails = Some(StepDetails(step_id = createAndStartCampaignData.addStep.step_id,
                step_name = createAndStartCampaignData.addStep.label.getOrElse("step name"),
                step_type = createAndStartCampaignData.addStep.step_data.step_type)
            ))),
            prospectIdInCampaign = Some(duplicate),
            currentBillingCycleStartedAt = initialData.account.created_at,
            orgId = initialData.account.org.id,
            repTrackingHostId = 1,
            teamId = initialData.head_team_id
          )
        }

        res: Seq[Long] <- Future {
          emailThreadDAO._updateLatestEmailId(
            data = Seq(updateData.copy(latest_email_id = emailSent.id.get))
          )
        }
      } yield {
        res
      }

      result.map(res => {
        val resStat: Seq[CampaignStatsByVariant] = reportDAO.getStatsV2(
          accountIds = Set(AccountId(initialData.account.internal_id)),
          teamId = initialData.emailSetting.get.team_id,
          fromMillis = 1712995200L,
          tillMillis = 1713599999L,
          listIds = Seq(),
          campaignIds = Seq(CampaignId(campaignId)),
          positive_reply_sentiments = List()
        )
        assert(resStat.isEmpty)
      }).recover(err=> {
        println(LogHelpers.getStackTraceAsString(err))
        assert(false)
      })

    }
  }

  describe("getBestTimeOfDayReportOnly") {
    it("should return success") {
      val res: Try[JsObject] = reportDAO.getBestTimeOfDayReportOnly(
        accountIds = Set(initialData.account.internal_id),
        tid = initialData.head_team_id,
        timezone = "Asia/Kolkata",
        data = StatsInputQuery(
          time_period = StatsTimePeriod.WEEK,
          campaign_ids = None,
          campaign_tag_ids = None,
          list_ids = None,
          account_ids = Seq(initialData.account.internal_id),
          best_time_filter = None,
          fromDay = None,
          tillDay = None,
          from = 1712995200L,
          till = 1713599999L,
          page = None,
          is_domain_group = Some(true)
        )
      )
      assert(res.isSuccess)
      val stats = (res.get \ "best_time_of_day_stats")
      assert(stats.isDefined)
    }
  }

  describe("getTeamMemberReportOnly") {
    it("should return success") {

      val res = reportDAO.getTeamMemberReportOnly(
        team_member_account_ids = Seq(initialData.account.internal_id),
        tid = initialData.head_team_id,
        data = StatsInputQuery(
          time_period = StatsTimePeriod.WEEK,
          campaign_ids = Some(Seq(1L)),
          campaign_tag_ids = None,
          list_ids = None,
          account_ids = Seq(initialData.account.internal_id),
          best_time_filter = None,
          fromDay = None,
          tillDay = None,
          from = 1712995200L,
          till = 1713599999L,
          page = None,
          is_domain_group = Some(true)
        ),
          showWithManualData = false
      )

      assert(res.keys.contains("teams_stats"))

    }
  }

  describe("getTemplateReports"){
      it("should return success"){


          val res = reportDAO.getTemplateReport(
              accountIds = Set(initialData.account.internal_id),
              tid = initialData.head_team_id,
              data = StatsInputQuery(
                  time_period = StatsTimePeriod.WEEK,
                  campaign_ids = None,
                  campaign_tag_ids = None,
                  list_ids = None,
                  account_ids = Seq(initialData.account.internal_id),
                  best_time_filter = None,
                  fromDay = None,
                  tillDay = None,
                  from = 1712995200L,
                  till = 1713599999L,
                  page = None,
                  is_domain_group = Some(true)
              ),
              positiveReplySentiments = List()
          )

          res match {
              case Failure(exception) =>
                  println(exception.getMessage)
                  assert(false)

              case Success(value) =>
                  assert(res.isSuccess)

          }
      }
  }

  describe("getProspectCategoriesReport") {
    it("should return success") {


      val res = reportDAO.getProspectCategoriesReport(
        accountIds = Set(initialData.account.internal_id),
        tid = initialData.head_team_id,
        data = StatsInputQuery(
          time_period = StatsTimePeriod.WEEK,
          campaign_ids = None,
          campaign_tag_ids = None,
          list_ids = None,
          account_ids = Seq(initialData.account.internal_id),
          best_time_filter = None,
          fromDay = None,
          tillDay = None,
          from = 1712995200L,
          till = 1713599999L,
          page = None,
          is_domain_group = Some(true)
        )
      )

      res match {
        case Failure(exception) =>
          println(LogHelpers.getStackTraceAsString(exception))
          assert(false)

        case Success(value) =>
          assert(res.isSuccess)

      }
    }
  }


  describe("getOverallAccountStatsV2"){
      it("should return success"){
          val res = reportDAO.getAccountStatsOverallOnly(
              accountIds = Set(initialData.account.internal_id),
              teamId = initialData.head_team_id,
              data = StatsInputQuery(
                  time_period = StatsTimePeriod.WEEK,
                  campaign_ids = None,
                  campaign_tag_ids = None,
                  list_ids = None,
                  account_ids = Seq(initialData.account.internal_id),
                  best_time_filter = None,
                  fromDay = None,
                  tillDay = None,
                  from = 1712995200L,
                  till = 1713599999L,
                  page = None,
                  is_domain_group = Some(true)
              ),
              positive_reply_sentiments = List(),
              showWithManualData = true
          )
          println(res)
          assert(res.nonEmpty)
      }
  }


    describe("getAccountStatsByTimeWeeklyV2") {
        it("should return success") {


            val res = reportDAO.getAccountStatsTimewiseWeeklyOnly(
                accountIds = Set(initialData.account.internal_id),
                teamId = initialData.head_team_id,
                dayWeek = TimewiseReportInterval.WEEK.toString,
                data = StatsInputQuery(
                    time_period = StatsTimePeriod.WEEK,
                    campaign_ids = None,
                    campaign_tag_ids = None,
                    list_ids = None,
                    account_ids = Seq(initialData.account.internal_id),
                    best_time_filter = None,
                    fromDay = None,
                    tillDay = None,
                    from = 1712995200L,
                    till = 1713599999L,
                    page = None,
                    is_domain_group = Some(true)
                ),
                positive_reply_sentiments = List(),
                showWithManualData = true
            )
            println(res)
            assert(true)
        }
    }

}
