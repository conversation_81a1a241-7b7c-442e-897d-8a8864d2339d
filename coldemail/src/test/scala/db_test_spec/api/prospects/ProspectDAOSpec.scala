package db_test_spec.api.prospects

import api.accounts.TeamId
import api.accounts.models.{AccountId, OrgId}
import api.call.models.PhoneNumber
import api.columns.ColumnDef
import api.prospects.dao.DuplicateProspectResult
import api.prospects.dao_service.{DuplicationFindProspectData, DuplicationFindProspectDataV2}
import api.prospects.{ProspectCreateFormData, ProspectsToBeForceUpdated}
import api.prospects.models.{ProspectDeletionReason, ProspectId, SrProspectColumns}
import api.prospects.service.ProspectEmail
import org.joda.time.DateTime
import db_test_spec.api.{DbTestingBeforeAllAndAfterAll, InitialData}
import db_test_spec.api.accounts.fixtures.NewAccountAndEmailSettingData
import eventframework.{ProspectBasicInfo, ProspectObject}
import play.api.libs.json.{JsValue, Json}
import utils.SRLogger
import utils.helpers.LogHelpers

import scala.util.{Failure, Success, Try}

class ProspectDAOSpec extends DbTestingBeforeAllAndAfterAll{
  lazy val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

  describe("scheduleForDeletion") {
    it("should delete prospect") {
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData(
        emailNotCompulsoryOrgFlag = Some(true)
      ).get

      val account = initialData.account

      val accountId: AccountId = AccountId(account.internal_id)

      val teamId: TeamId = TeamId(account.teams.head.team_id)

      val res: Try[List[Long]] = prospectDAO.scheduleForDeletion(
        groupedPids = initialData.prospectsResult.map(p => ProspectId(p.id)),
        permittedAccountIds = Seq(account.internal_id),
        teamId = teamId,
        index = 0,
        deletionReason = ProspectDeletionReason.ByUser,
        will_delete_at = DateTime.now(),
        logger = Logger
      )

      assert(res.isSuccess && res.get.nonEmpty)
    }
  }

  describe("updateProspectBasicInfoForMasterProspect") {
    it("should return updated prospect_id") {
      
      val account = initialData.account
      val accountId: AccountId = AccountId(account.internal_id)
      val teamId: TeamId = TeamId(account.teams.head.team_id)
      val prospectId: Long = initialData.prospectsResult.head.id

      val res = prospectDAO.updateProspectBasicInfoForMasterProspect(
        data = ProspectBasicInfo(
          id = prospectId,
          owner_name = "Prachi",

          email = Seq(ProspectEmail(
            email = "<EMAIL>", is_valid = true, is_primary = true
          )),
          linkedin_url = Some("linkedin.in/prospect"),
          phone = Some("***********"),

          company = Some("Sales"),
          first_name = Some("John"),
          last_name = None,

          job_title = None,
          city = Some("Pune"),
          state = Some("Maharashtra"),
          country = Some("India")
        ),
        masterProspectId = ProspectId(prospectId),
        permittedAccountIds = Seq(accountId),
        doerAccountId = accountId,
        teamId = teamId
      )

      assert(res.isSuccess && res.get.nonEmpty && res.get.get.id == prospectId)
    }
  }

  describe("updateProspects") {
    it("should return success for ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads = false") {
      val account = initialData.account
      val teamId: TeamId = TeamId(account.teams.head.team_id)

      val res = prospectDAO.updateProspects(
        ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads = false,
        prospectCustomColumnDefs = Seq(),
        listIdOpt = None,
        prospectsToBeUpdated = initialData.prospectsResult.map(p=> {
          ProspectsToBeForceUpdated(
            prospect = ProspectCreateFormData(
              email = p.email,
              first_name = Some("Abc"),
              last_name = Some("Abc"),
              custom_fields = Json.obj(),

            list = None,
            company = None,
            city = None,
            country = None,
            timezone = None,

          state = Some("Maharashtra"),
          job_title = Some("Manager"),
          phone = Some("************"),
          phone_2 = Some("************"),
          phone_3 = None
            ),
            prospect_id = p.id,
            deDuplicationColumnTypes = Seq(SrProspectColumns.Email)
          )
        }),
        teamId = teamId.id
      )

      assert(res.isSuccess && res.get.nonEmpty && res.get.map(_.prospect_id).sorted == initialData.prospectsResult.map(_.id).sorted)
    }

    it("should return success for ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads = true") {
      val account = initialData.account
      val teamId: TeamId = TeamId(account.teams.head.team_id)

      val res = prospectDAO.updateProspects(
        ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads = true,
        prospectCustomColumnDefs = Seq(),
        listIdOpt = None,
        prospectsToBeUpdated = initialData.prospectsResult.map(p=> {
          ProspectsToBeForceUpdated(
            prospect = ProspectCreateFormData(
              email = p.email,
              first_name = Some("Abc"),
              last_name = Some("Abc"),
              custom_fields = Json.obj(),

              list = None,
              company = None,
              city = None,
              country = None,
              timezone = None,

              state = Some("Maharashtra"),
              job_title = Some("Manager"),
              phone = Some("************"),
              phone_2 = Some("************"),
              phone_3 = None
            ),
            prospect_id = p.id,
            deDuplicationColumnTypes = Seq(SrProspectColumns.Email)
          )
        }),
        teamId = teamId.id
      )

      assert(res.isSuccess && res.get.nonEmpty && res.get.map(_.prospect_id).sorted == initialData.prospectsResult.map(_.id).sorted)
    }

  }

  describe("findDuplicateProspectsForForceUpdate") {
    it("should return the prospect data") {
      val prospect: ProspectObject = initialData.prospectsResult.head
      val data = DuplicationFindProspectData(
        data = Seq(prospect.email.get),
        columnType = SrProspectColumns.Email
      )
      val res: Try[List[DuplicateProspectResult]] = prospectDAO.findDuplicateProspectsForForceUpdate(
        duplicationFindProspectData = Seq(data),
        teamId = TeamId(prospect.team_id)
      )

      assert(res.isSuccess && res.get.nonEmpty && res.get.head.prospectCreateFormData.email == prospect.email)

    }

    it("should return the success empty list if empty seq passed") {
      val prospect: ProspectObject = initialData.prospectsResult.head

      val res: Try[List[DuplicateProspectResult]] = prospectDAO.findDuplicateProspectsForForceUpdate(
        duplicationFindProspectData = Seq(),
        teamId = TeamId(prospect.team_id)
      )

      assert(res.isSuccess && res.get.isEmpty)

    }
  }

  describe("saveLastContactedAtPhone") {
    it("should return success 1") {
      val prospect: ProspectObject = initialData.prospectsResult.head

      val res: Try[Int] = prospectDAO.saveLastContactedAtPhone(
        phone_number = PhoneNumber("+919828287898"),
        prospect_id = ProspectId(prospect.id),
        teamId = TeamId(prospect.team_id)
      )

      res match {
        case Success(_) => assert(res.get == 1)
        case Failure(exception) =>
          println(s"${LogHelpers.getStackTraceAsString(exception)}")
          assert(false)
      }

    }
  }

  describe("findDuplicateProspectsForForceUpdateV2") {
    it("should return the prospect data") {
      val prospect: ProspectObject = initialData.prospectsResult.head

      val dataEmail = DuplicationFindProspectDataV2(
        data = prospect.email.get,
        columnType = SrProspectColumns.Email
      )

      val dataPhone = DuplicationFindProspectDataV2(
        data = prospect.phone.get,
        columnType = SrProspectColumns.Phone
      )
      val res: Try[List[DuplicateProspectResult]] = prospectDAO.findDuplicateProspectsForForceUpdateV2(
        duplicationFindProspectData = Seq(Seq(dataEmail, dataPhone)),
        teamId = TeamId(prospect.team_id)
      )

      assert(res.isSuccess && res.get.nonEmpty && res.get.exists(_.prospectCreateFormData.email.get == prospect.email.get))

    }

    it("should return the data for 2 prospects") {
      val prospect1: ProspectObject = initialData.prospectsResult.head

      val dataEmail1 = DuplicationFindProspectDataV2(
        data = prospect1.email.get,
        columnType = SrProspectColumns.Email
      )

      val dataLinkedIn1 = DuplicationFindProspectDataV2(
        data = prospect1.linkedin_url.get,
        columnType = SrProspectColumns.LinkedinUrl
      )

      val prospect2: ProspectObject = initialData.prospectsResult.last

      val dataEmail2 = DuplicationFindProspectDataV2(
        data = prospect2.email.get,
        columnType = SrProspectColumns.Email
      )

      val dataLinkedIn2 = DuplicationFindProspectDataV2(
        data = prospect2.linkedin_url.get,
        columnType = SrProspectColumns.LinkedinUrl
      )

      val res: Try[List[DuplicateProspectResult]] = prospectDAO.findDuplicateProspectsForForceUpdateV2(
        duplicationFindProspectData = Seq(
          Seq(dataEmail1, dataLinkedIn1),
          Seq(dataEmail2, dataLinkedIn2)
        ),
        teamId = TeamId(prospect1.team_id)
      )

      assert(res.isSuccess &&
        res.get.nonEmpty &&
        res.get.map(_.prospectCreateFormData.email).exists(_.get == prospect1.email.get) &&
        res.get.map(_.prospectCreateFormData.email).exists(_.get == prospect2.email.get)
      )

    }

    it("should return the success empty list if empty seq passed") {
      val prospect: ProspectObject = initialData.prospectsResult.head

      val res: Try[List[DuplicateProspectResult]] = prospectDAO.findDuplicateProspectsForForceUpdateV2(
        duplicationFindProspectData = Seq(),
        teamId = TeamId(prospect.team_id)
      )

      assert(res.isSuccess && res.get.isEmpty)

    }
  }

}
