package db_test_spec.api.prospects

import api.accounts.{Account, TeamId}
import api.accounts.models.{AccountId, OrgId}
import api.emails.EmailSetting
import api.prospects.ProspectUpdateFormData
import api.prospects.dao.{DuplicateProspectResult, NeedDuplicateCheckProspect}
import api.prospects.models.{PotentialDuplicateProspectLogId, PotentialDuplicateProspectStatus, ProspectId, ProspectsMetadataUpsert}
import api.prospects.service.ProspectEmail
import api.tags.models.{ProspectTagUuid, TagAndUuid}
import api.triggers.AddOrRemoveTagAction
import db_test_spec.api.{DbTestingBeforeAllAndAfterAll, InitialData}
import db_test_spec.api.accounts.fixtures.NewAccountAndEmailSettingData
import db_test_spec.api.campaigns.test_utils.{CampaignUtils, CreateAndStartCampaignData}
import db_test_spec.api.prospects.dao.PotentialDuplicateProspectDAO
import eventframework.{ProspectBasicInfo, ProspectObject}
import play.api.libs.json.{<PERSON>sVal<PERSON>, <PERSON>son}
import utils.{Helpers, SRLogger}
import utils.helpers.LogHelpers

import scala.concurrent.Future
import scala.util.Try

class ProspectsEmailsDAOSpec extends DbTestingBeforeAllAndAfterAll{

  describe("updateMasterProspectIdForMergeDuplicates") {
    it("should return master prospect id after update") {
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData(
        emailNotCompulsoryOrgFlag = Some(true)
      ).get

      val account: Account = initialData.account
      val accountId: AccountId = AccountId(account.internal_id)
      val emailSetting: EmailSetting = initialData.emailSetting.get
      val teamId: TeamId = TeamId(account.teams.head.team_id)
      val orgId: OrgId = OrgId(account.org.id)


      val result: Future[Int] = for {
        campaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 3,
          emailSettings = Some(emailSetting)
        )

        prospects: Seq[ProspectObject] <- Future {
          campaignData.addProspect
        }

        updateProspects: Seq[Option[Long]] <- Future.fromTry {
          val res = prospects.map(prospect => {
            prospectDAOService.update(
              permittedAccountIds = Seq(account.internal_id),
              actingAccountId = accountId.id,
              prospect_owner_account_id = accountId,
              id = prospect.id,
              teamId = teamId.id,
              updateProspect = ProspectUpdateFormData(
                email = prospect.email,
                first_name = prospect.first_name,
                last_name = prospect.last_name,
                custom_fields = prospect.custom_fields,

                prospect_category_id_custom = None,

                list = prospect.list,
                company = prospect.company,
                city = prospect.city,
                country = prospect.country,
                timezone = prospect.timezone,

                state = prospect.state,
                job_title = prospect.job_title,
                phone = Some("+************"),
                linkedin_url = prospect.linkedin_url,

                prospect_account_id = prospect.internal.prospect_account_id
              )
            )
          })
          Helpers.seqTryToTrySeq(res)
        }

        masterProspect: ProspectObject <- Future {
          prospects.head
        }

        updateMetadata: Int <- Future.fromTry {
          accountOrgBillingRelatedInfoDAO.insertProspectsMetadataOrUpdateLastTouchedAt(
            prospectId = ProspectId(masterProspect.id),
            teamId = teamId,
            data = ProspectsMetadataUpsert.ProspectDuplicateCheckData(
              need_duplicate_check = true
            )
          )
        }

        updatedLogs: Int <- {
          mqPotentialDuplicateProspects.processMessage(msg = NeedDuplicateCheckProspect(
            prospectId = ProspectId(masterProspect.id), orgId = orgId, teamId = teamId
          ))
        }

        log_id: Option[Long] <- Future.fromTry {
          PotentialDuplicateProspectDAO.getPotentialDuplicateProspectLogId(teamId)
        }

        duplicateProspects: Seq[Long] <- Future {
          prospects.filterNot(_.id == masterProspect.id).map(_.id)
        }

        createdTags: Int <- Future.fromTry {
          tagService.updateTagsForProspects(
            action = AddOrRemoveTagAction.ADD,
            tags = Seq(TagAndUuid(
              tag = "test",
              uuid = ProspectTagUuid("tag_abcd")
            )),
            prospectIdsWithPermission = duplicateProspects,
            teamId = teamId.id,
            accountId = accountId.id
          )
        }

        masterProspectBasicInfo: JsValue <- Future {
          (Json.toJson(
            ProspectBasicInfo(
              id = masterProspect.id,
              owner_name = "John doe",

              email = Seq(ProspectEmail(
                email = "<EMAIL>",
                is_valid = true,
                is_primary = true
              ), ProspectEmail(
                email = "<EMAIL>",
                is_valid = true,
                is_primary = false
              ), ProspectEmail(
                email = "<EMAIL>",
                is_valid = true,
                is_primary = false
              )),
              linkedin_url = None,
              phone = None,

              company = None,
              first_name = None,
              last_name = None,

              job_title = None,
              city = None,
              state = None,
              country = None
            )
          ))
        }

        updatedBasicInfo: Int <- Future.fromTry {
          potentialDuplicateProspectsDAO.updateStatusOfLog(
            potentialDuplicateProspectLogId = PotentialDuplicateProspectLogId(log_id.get),
            status = PotentialDuplicateProspectStatus.Merged,
            status_updated_by = accountId,
            status_updated_by_login_account_email = "<EMAIL>",
            merged_into_prospect_id = Some(ProspectId(masterProspect.id)),
            duplicate_reason = None,
            status_updated_req_trace_id = "reqTraceId",
            masterProspectBasicInfo = Some(masterProspectBasicInfo),
            teamId = teamId
          )
        }


      } yield {
        updatedBasicInfo
      }

      result.map(r => {
          assert(r == 1)
        })
        .recover(e => {
          println(s"error: ${LogHelpers.getStackTraceAsString(e)}")
          assert(false)
        })

    }
  }

  describe("findDuplicateProspectsV2") {
    it("should return the prospect data") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val prospect: ProspectObject = initialData.prospectsResult.head
      val logger: SRLogger = new SRLogger("findDuplicateProspectsV2 dao spec")

      val res: Try[List[DuplicateProspectResult]] = prospectsEmailsDAO.findDuplicateProspectsV2(
        Logger = logger,
        emails = Seq(prospect.email.get),
        teamId = prospect.team_id
      )

      assert(res.isSuccess && res.get.nonEmpty && res.get.head.prospectCreateFormData.email == prospect.email)

    }
  }


}
