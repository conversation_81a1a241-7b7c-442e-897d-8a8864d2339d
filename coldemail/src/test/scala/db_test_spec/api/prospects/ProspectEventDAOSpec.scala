package db_test_spec.api.prospects

import api.accounts.EmailScheduledIdOrTaskId.EmailScheduledId
import api.accounts.TeamId
import api.accounts.models.{AccountId, OrgId}
import api.campaigns.{Campaign, CampaignStepVariant, CampaignWithStatsAndEmail}
import api.campaigns.models.{SendEmailFromCampaignDetails, StepDetails}
import api.campaigns.services.CampaignId
import api.prospects.dao.ProspectIdAndPotentialDuplicateProspectId
import api.prospects.models.{PotentialDuplicateProspectId, ProspectEventId, ProspectId}
import api.prospects.{FindThreadMessageFlow, InferredQueryTimeline, ProspectEventDAO, ProspectEventV2}
import app.test_fixtures.prospect.ProspectCreateFormDataFixture
import db_test_spec.api.accounts.emails.dao.EmailScheduledDAO
import db_test_spec.api.{DbTestingBeforeAllAndAfterAll, InitialData}
import db_test_spec.api.accounts.fixtures.{EmailScheduledNewFixture, NewAccountAndEmailSettingData}
import db_test_spec.api.campaigns.fixtures.{CreateNewCampaignFixture, CreateStepForCampaignFixture, NewCampaignCreationData, StartCampaignFixture}
import db_test_spec.api.campaigns.test_utils.{CampaignUtils, CreateAndStartCampaignData}
import db_test_spec.api.prospects.fixtures.ProspectFixtureForIntegrationTest
import db_test_spec.api.prospects.dao.ProspectEventsDAO
import db_test_spec.api.scheduler.fixtures.DefaultParametersFixtureForInitializingDataForReScheduling
import db_test_spec.team_inbox.fixtures.TeamInboxFixtureForIntegrationTest
import eventframework.ProspectObject
import io.smartreach.esp.api.emails.EmailSettingId
import io.smartreach.esp.api.microsoftOAuth.EmailSettingUpdateAccessToken
import org.joda.time.DateTime
import eventframework.{MessageObject, ProspectObject}
import scalikejdbc.DBSession
import utils.dbutils.DbAndSession
import utils.helpers.LogHelpers

import scala.concurrent.Future
import scala.util.Try

class ProspectEventDAOSpec extends DbTestingBeforeAllAndAfterAll {

  describe("getEmailMessageObject") {

    it("getEmailMessageObjectNew") {

      val result = for {

        initialData: InitialData <- Future.fromTry(NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData())

        createCampaign: NewCampaignCreationData <- CreateNewCampaignFixture.createNewCampaign(
          orgId = OrgId(initialData.account.org.id),
          accountId = AccountId(initialData.account.internal_id),
          accountName = s"${initialData.account.first_name.getOrElse("Animesh")} ${initialData.account.last_name.getOrElse("Kumar")}"
        )
        prospect: Seq[ProspectObject] <- Future.fromTry {
          ProspectFixtureForIntegrationTest.createUpdateOrAssignProspect(
            givenProspect = Some(Seq(
              ProspectCreateFormDataFixture.prospectCreateFormData.copy(
                email = Some("<EMAIL>"),
                first_name = Some("Animesh"),
                last_name = Some("Kumar"),
                owner_id = Some(createCampaign.campaignWithStatsAndEmail.owner_id),
                company = Some("Test Company"),
                city = Some("Pune"),
                country = Some("India"),
                timezone = Some("Asia/Kolkata"),
                state = Some("Maharashtra"),
                job_title = Some("SDE"),
                phone = Some("**********"),
                linkedin_url = Some("https://www.linkedin.com/in/animesh-kumar-9a1171191/")
              ))),
            campaignId = Some(CampaignId(createCampaign.campaignWithStatsAndEmail.id)),
            account = createCampaign.newTeamCreationData.updatedAccount,
            teamId = TeamId(createCampaign.campaignWithStatsAndEmail.team_id),
            accountId = AccountId(createCampaign.newTeamCreationData.updatedAccount.internal_id)
          )
        }
        //add auto email step to campaign
        addStep: CampaignStepVariant <- {
          CreateStepForCampaignFixture.createAutoEmailStepForCampaign(
            orgId = OrgId(createCampaign.newTeamCreationData.updatedAccount.org.id),
            teamId = TeamId(createCampaign.campaignWithStatsAndEmail.team_id),
            accountId = AccountId(createCampaign.newTeamCreationData.updatedAccount.internal_id),
            taId = createCampaign.newTeamCreationData.teamAccount.access_members.head.ta_id,
            campaignId = CampaignId(createCampaign.campaignWithStatsAndEmail.id),
          )
        }

        //campaign details
        campaign: Campaign <- Future.successful(
          campaignDAO.findCampaignForCampaignUtilsOnly(
            id = createCampaign.campaignWithStatsAndEmail.id,
            teamId = TeamId(createCampaign.campaignWithStatsAndEmail.team_id)
          ).get
        )

        //start campaign
        startCampaign: CampaignWithStatsAndEmail <- {
          StartCampaignFixture.startCampaign(
            accountId = AccountId(createCampaign.newTeamCreationData.updatedAccount.internal_id),
            teamId = TeamId(createCampaign.campaignWithStatsAndEmail.team_id),
            campaignWithStatsAndEmail = createCampaign.campaignWithStatsAndEmail,
            orgId = OrgId(createCampaign.newTeamCreationData.updatedAccount.org.id),
            taId = createCampaign.newTeamCreationData.teamAccount.access_members.head.ta_id,
            emailSetting = createCampaign.newEmailSetting,
            prospect_categories_custom_not_categorized = createCampaign.customNotCategorizedAndDoNotContactIds.not_categorized,
            prospect_categories_custom_do_not_contact = createCampaign.customNotCategorizedAndDoNotContactIds.do_not_contact,
            current_sending_email_accounts = createCampaign.newEmailSetting.id.get.emailSettingId.toInt
          )
        }
        addingEmailScheduled <- Future.fromTry {
          emailScheduledDAOService.saveEmailsToBeScheduledAndUpdateCampaignDataV2(
            emailsToBeScheduled = Vector(EmailScheduledNewFixture.generateEmailScheduledNew3.copy(
              campaign_id = Some(createCampaign.campaignWithStatsAndEmail.id),
              step_id = createCampaign.campaignWithStatsAndEmail.head_step_id,
              from_email = createCampaign.campaignWithStatsAndEmail.settings.campaign_email_settings.head.sender_email,
              scheduled_from_campaign = true,
              is_opening_step = true,
              sender_email_settings_id = createCampaign.campaignWithStatsAndEmail.settings.campaign_email_settings.head.sender_email_setting_id.emailSettingId,
              team_id = createCampaign.campaignWithStatsAndEmail.team_id,
              account_id = createCampaign.campaignWithStatsAndEmail.owner_id,
              receiver_email_settings_id = createCampaign.campaignWithStatsAndEmail.settings.campaign_email_settings.head.receiver_email_setting_id.emailSettingId,
              campaign_email_settings_id = createCampaign.campaignWithStatsAndEmail.settings.campaign_email_settings.head.id,
              prospect_id = Some(prospect.head.id),
              base_body = "body"

            )
            ),
            campaign_email_setting_id = createCampaign.campaignWithStatsAndEmail.settings.campaign_email_settings.head.id,
            emailSendingFlow = None,
            Logger = Logger
          )
        }
        emailSent <- Future.fromTry {
          emailSenderService.onEmailSent(
            emailSentId = addingEmailScheduled.head.email_scheduled_id,
            data = DefaultParametersFixtureForInitializingDataForReScheduling.defaultEmailToBeSent(
              emailSetting = initialData.emailSetting.get,
              campaign = campaign
            ),
            accountId = createCampaign.newTeamCreationData.updatedAccount.internal_id,
            sendEmailFromCampaignDetails = Some(SendEmailFromCampaignDetails(
              campaign_id = createCampaign.campaignWithStatsAndEmail.id,
              campaign_name = createCampaign.campaignWithStatsAndEmail.name,
              // stepDetails can be none when email is being sent manually from Inbox.
              // Example: sendNewEmailManually in InboxV3Service
              stepDetails = Some(StepDetails(
                step_id = addStep.step_id,
                step_name = addStep.label.getOrElse("step"),
                step_type =addStep.step_data.step_type
              ))
            )),
            prospectIdInCampaign = prospect.headOption.map(_.id),
            currentBillingCycleStartedAt = createCampaign.newTeamCreationData.updatedAccount.created_at,
            orgId = createCampaign.newTeamCreationData.updatedAccount.org.id,
            repTrackingHostId = 1,
            teamId = campaign.team_id
          )
        }
        readNew: MessageObject.EmailMessageObject <- Future.fromTry {
          prospectEventDAO.getEmailMessageObjectNew(
            email_thread_id = emailSent.email_thread_id.get,
            email_scheduled_id = emailSent.id,
            teamId = TeamId(campaign.team_id),
            allTrackingDomains = Seq()
          )
        }


      } yield {

        readNew
      }


      result.map{v =>
        assert(true)
      }.recover{e =>
        println(s"error ---- $e")
        assert(false)
      }

    }
  }

  describe("getEmailMessageEventsQuery") {
    it("getEmailMessageEventsQueryNew") {

      val result = for {

        initialData: InitialData <- Future.fromTry(NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData())


        createCampaign: NewCampaignCreationData <- CreateNewCampaignFixture.createNewCampaign(
          orgId = OrgId(initialData.account.org.id),
          accountId = AccountId(initialData.account.internal_id),
          accountName = s"${initialData.account.first_name.getOrElse("Animesh")} ${initialData.account.last_name.getOrElse("Kumar")}"
        )
        prospect: Seq[ProspectObject] <- Future.fromTry {
          ProspectFixtureForIntegrationTest.createUpdateOrAssignProspect(
            givenProspect = Some(Seq(
              ProspectCreateFormDataFixture.prospectCreateFormData.copy(
                email = Some("<EMAIL>"),
                first_name = Some("Animesh"),
                last_name = Some("Kumar"),
                owner_id = Some(createCampaign.campaignWithStatsAndEmail.owner_id),
                company = Some("Test Company"),
                city = Some("Pune"),
                country = Some("India"),
                timezone = Some("Asia/Kolkata"),
                state = Some("Maharashtra"),
                job_title = Some("SDE"),
                phone = Some("**********"),
                linkedin_url = Some("https://www.linkedin.com/in/animesh-kumar-9a1171191/")
              ))),
            campaignId = Some(CampaignId(createCampaign.campaignWithStatsAndEmail.id)),
            account = createCampaign.newTeamCreationData.updatedAccount,
            teamId = TeamId(createCampaign.campaignWithStatsAndEmail.team_id),
            accountId = AccountId(createCampaign.newTeamCreationData.updatedAccount.internal_id)
          )
        }
        //add auto email step to campaign
        addStep: CampaignStepVariant <- {
          CreateStepForCampaignFixture.createAutoEmailStepForCampaign(
            orgId = OrgId(createCampaign.newTeamCreationData.updatedAccount.org.id),
            teamId = TeamId(createCampaign.campaignWithStatsAndEmail.team_id),
            accountId = AccountId(createCampaign.newTeamCreationData.updatedAccount.internal_id),
            taId = createCampaign.newTeamCreationData.teamAccount.access_members.head.ta_id,
            campaignId = CampaignId(createCampaign.campaignWithStatsAndEmail.id),
          )
        }

        //campaign details
        campaign: Campaign <- Future.successful {
          campaignDAO.findCampaignForCampaignUtilsOnly(
            id = createCampaign.campaignWithStatsAndEmail.id,
            teamId = TeamId(createCampaign.campaignWithStatsAndEmail.team_id)
          ).get
        }

        //start campaign
        startCampaign: CampaignWithStatsAndEmail <- {
          StartCampaignFixture.startCampaign(
            accountId = AccountId(createCampaign.newTeamCreationData.updatedAccount.internal_id),
            teamId = TeamId(createCampaign.campaignWithStatsAndEmail.team_id),
            campaignWithStatsAndEmail = createCampaign.campaignWithStatsAndEmail,
            orgId = OrgId(createCampaign.newTeamCreationData.updatedAccount.org.id),
            taId = createCampaign.newTeamCreationData.teamAccount.access_members.head.ta_id,
            emailSetting = createCampaign.newEmailSetting,
            prospect_categories_custom_not_categorized = createCampaign.customNotCategorizedAndDoNotContactIds.not_categorized,
            prospect_categories_custom_do_not_contact = createCampaign.customNotCategorizedAndDoNotContactIds.do_not_contact,
            current_sending_email_accounts = createCampaign.newEmailSetting.id.get.emailSettingId.toInt
          )
        }
        addingEmailScheduled <- Future.fromTry {
          emailScheduledDAOService.saveEmailsToBeScheduledAndUpdateCampaignDataV2(
            emailsToBeScheduled = Vector(EmailScheduledNewFixture.generateEmailScheduledNew3.copy(
              campaign_id = Some(createCampaign.campaignWithStatsAndEmail.id),
              step_id = createCampaign.campaignWithStatsAndEmail.head_step_id,
              from_email = createCampaign.campaignWithStatsAndEmail.settings.campaign_email_settings.head.sender_email,
              scheduled_from_campaign = true,
              is_opening_step = true,
              sender_email_settings_id = createCampaign.campaignWithStatsAndEmail.settings.campaign_email_settings.head.sender_email_setting_id.emailSettingId,
              team_id = createCampaign.campaignWithStatsAndEmail.team_id,
              account_id = createCampaign.campaignWithStatsAndEmail.owner_id,
              receiver_email_settings_id = createCampaign.campaignWithStatsAndEmail.settings.campaign_email_settings.head.receiver_email_setting_id.emailSettingId,
              campaign_email_settings_id = createCampaign.campaignWithStatsAndEmail.settings.campaign_email_settings.head.id,
              prospect_id = Some(prospect.head.id),
              base_body = "body"

            )
            ),
            campaign_email_setting_id = createCampaign.campaignWithStatsAndEmail.settings.campaign_email_settings.head.id,
            emailSendingFlow = None,
            Logger = Logger
          )
        }
        emailSent <- Future.fromTry {
          emailSenderService.onEmailSent(
            emailSentId = addingEmailScheduled.head.email_scheduled_id,
            data = DefaultParametersFixtureForInitializingDataForReScheduling.defaultEmailToBeSent(
              emailSetting = initialData.emailSetting.get,
              campaign = campaign
            ).copy(outlook_conversation_id = Some("outlook_conversation_id+findOneForFetchOutlookMsgId"), subject = "subject"),
            accountId = createCampaign.newTeamCreationData.updatedAccount.internal_id,
            sendEmailFromCampaignDetails = Some(SendEmailFromCampaignDetails(
              campaign_id = createCampaign.campaignWithStatsAndEmail.id,
              campaign_name = createCampaign.campaignWithStatsAndEmail.name,
              // stepDetails can be none when email is being sent manually from Inbox.
              // Example: sendNewEmailManually in InboxV3Service
              stepDetails = Some(StepDetails(
                step_id = addStep.step_id,
                step_name = addStep.label.getOrElse("step"),
                step_type = addStep.step_data.step_type
              ))
            )),
            prospectIdInCampaign = prospect.headOption.map(_.id),
            currentBillingCycleStartedAt = createCampaign.newTeamCreationData.updatedAccount.created_at,
            orgId = createCampaign.newTeamCreationData.updatedAccount.org.id,
            repTrackingHostId = 1,
            teamId = campaign.team_id
          )
        }

        updateSentAt <- Future.fromTry {

          EmailScheduledDAO.updateSentAtForfindOneForFetchOutlookMsgId(
            campaign_id = CampaignId(campaign.id),
            teamId = TeamId(campaign.team_id),
            sentAt = DateTime.now().minusMinutes(20),
            email_scheduled_id = emailSent.id.get
          )
        }
        add_microsoft_refresh_token <- Future.fromTry{
          println(s"emailSettingId ${initialData.emailSetting}")
          microsoftOAuth.updateAccessTokenAndRefreshToken(
            emailSettingId = campaign.settings.campaign_email_settings.head.sender_email_setting_id,
            data = EmailSettingUpdateAccessToken(
              oauth2_refresh_token = "oauth2_refresh_token+findOneForFetchOutlookMsgId",
              oauth2_access_token = "oauth2_access_token+findOneForFetchOutlookMsgId",
              oauth2_token_expires_in = 36000,
              oauth2_access_token_expires_at = DateTime.now().plusDays(10000)
            )
          )}
        add_team_inbox <- Future.fromTry {
          TeamInboxFixtureForIntegrationTest.createTeamInbox(
            orgId = OrgId(initialData.account.org.id),
            accountId = AccountId(initialData.account.internal_id),
            emailSettingId = EmailSettingId(addingEmailScheduled.head.sender_email_settings_id),
            teamId = TeamId(initialData.account.teams.head.team_id),
            userRoleIds = initialData.account.teams.map(_.role.get.id).toList
          )
        }
        repServers <- Future.fromTry(accountDAO.getRepTrackingHosts())

        readNew: List[ProspectEventV2] <- Future.fromTry {
          prospectEventDAO.getEmailMessageEvents(ProspectEventDAO.getEmailMessageEventsQueryNew(
            queryTimeline = InferredQueryTimeline.Range.Before(DateTime.now()),
            limit = 100,
            teamId = addingEmailScheduled.head.team_id.id,
            forProspectId = prospect.headOption.map(_.id),
            forEmailThreadId = None,
            forProspectAccountId = None
          ).get,
            repTrackingHosts = repServers)

        }




      } yield {

        readNew
      }


      result.map { v =>
        assert(v.nonEmpty)
      }.recover { e =>
        println(s"error ---- $e")
        assert(false)
      }

    }
  }

  describe("updateMasterProspectForMergeDuplicates") {
    it("should return prospect events ids updated") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
      val outlookConvId: Option[String] = Some("outlook_conversation_id+abcd")
      val team_id:TeamId = initialData.emailSetting.get.team_id

      val updatedIds: Future[(List[ProspectEventId], List[Long])] = for {

        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 4
        )
        prospects: Seq[ProspectObject] <- Future {
          initialData.prospectsResult
        }

        (duplicate: Long, master: Long) <- Future {
          (prospects.head.id, prospects.last.id)
        }

        addingEmailScheduled <- Future.fromTry {
          emailScheduledDAOService.saveEmailsToBeScheduledAndUpdateCampaignDataV2(
            emailsToBeScheduled = Vector(EmailScheduledNewFixture.generateEmailScheduledNew3.copy(
              campaign_id = Some(createAndStartCampaignData.createCampaign.id),
              step_id = createAndStartCampaignData.createCampaign.head_step_id,
              from_email = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.sender_email,
              scheduled_from_campaign = true,
              is_opening_step = true,
              sender_email_settings_id = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.sender_email_setting_id.emailSettingId,
              team_id = createAndStartCampaignData.createCampaign.team_id,
              account_id = createAndStartCampaignData.createCampaign.owner_id,
              receiver_email_settings_id = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.receiver_email_setting_id.emailSettingId,
              campaign_email_settings_id = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.id,
              prospect_id = Some(prospects.head.id),
              base_body = "body"

            )
            ),
            campaign_email_setting_id = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.id,
            emailSendingFlow = None,
            Logger = Logger
          )
        }
        emailSent <- Future.fromTry {
          emailSenderService.onEmailSent(
            emailSentId = addingEmailScheduled.head.email_scheduled_id,
            data = DefaultParametersFixtureForInitializingDataForReScheduling.defaultEmailToBeSent(
              emailSetting = initialData.emailSetting.get,
              campaign = createAndStartCampaignData.campaign
            ).copy(outlook_conversation_id = outlookConvId, subject = "subject"),
            accountId = createAndStartCampaignData.createCampaign.owner_id,
            sendEmailFromCampaignDetails = Some(SendEmailFromCampaignDetails(
              campaign_id = createAndStartCampaignData.createCampaign.id,
              campaign_name = createAndStartCampaignData.createCampaign.name,
              // stepDetails can be none when email is being sent manually from Inbox.
              // Example: sendNewEmailManually in InboxV3Service
              stepDetails = None
            )),
            prospectIdInCampaign = Some(duplicate),
            currentBillingCycleStartedAt = initialData.account.created_at,
            orgId = initialData.account.org.id,
            repTrackingHostId = 1,
            teamId = team_id.id
          )
        }

        prospectEvents: List[Long] <- Future.fromTry {
          new ProspectEventsDAO().getProspectEvents(
            prospects = List(ProspectId(duplicate)),
            teamId = team_id
          )
        }

        prospectEventsUpdated: List[ProspectEventId] <- Future.fromTry {
          prospectEventDAO.updateMasterProspectForMergeDuplicates(
            duplicateProspects = List(ProspectIdAndPotentialDuplicateProspectId(
              prospectId = ProspectId(duplicate),
              potentialDuplicateProspectId = PotentialDuplicateProspectId(1),
              isMasterProspect = false
            )),
            teamId = TeamId(createAndStartCampaignData.createCampaign.team_id),
            masterProspectId = ProspectId(master)
          )
        }

      } yield {
        (prospectEventsUpdated, prospectEvents)
      }

      updatedIds.map(ids => {
        assert(ids._1.nonEmpty && ids._2.nonEmpty)
        assert(ids._1.map(_.id).distinct == ids._2.distinct)
      }).recover({ case e =>
        println(s"error::: ${LogHelpers.getStackTraceAsString(e)}")
        assert(false)
      })
    }
  }


}
