package db_test_spec.api.prospects

import api.accounts.TeamId
import api.accounts.models.AccountId
import api.csv_uploads.models.CsvUploadType
import api.prospects.models.SrProspectColumns
import api.prospects.{CsvQueue, CsvQueueCreateFormDataV2}
import db_test_spec.api.{DbTestingBeforeAllAndAfterAll, InitialData}
import db_test_spec.api.accounts.fixtures.NewAccountAndEmailSettingData
import play.api.libs.json.{JsValue, Json}
import utils.SRLogger
import utils.helpers.LogHelpers

import scala.util.{Failure, Success, Try}

class CsvQueueSpec extends DbTestingBeforeAllAndAfterAll{

    describe("createV2"){
        it("should create an entry in the csvQueue table"){
            val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

            val columnMap: JsValue = Json.obj(
                "emails" -> "email"
            )

            val csvCreateForm = CsvQueueCreateFormDataV2(
                file_url =  "https://example.com/test.csv",
                column_map =  columnMap,
                force_update_prospects = Some(false),
                csv_upload_type = CsvUploadType.DNCUPLOAD,
                list_name = None,
                campaign_id = None,
                tags = None,
                force_change_ownership = Some(false),
                ignore_email_empty_rows = Some(false),
                ignore_prospects_active_in_other_campaigns = Some(false),
                deduplication_columns = None,
                ignore_prospects_in_other_campaigns = None
            )

            val result: Try[CsvQueue] = CsvQueue.createV2(
                accountId = initialData.account.internal_id,
                teamId = Some(initialData.account.teams.head.team_id),
                loggedin_id = initialData.account.internal_id,
                ta_id = Some(initialData.account.teams.head.team_id),
                data = csvCreateForm
            )

            result match {
                case Success(csvResponse) =>
                    println(csvResponse)
                    assert(true)

                case Failure(e) =>
                    println(e)
                    assert(false)
            }
        }
    }

    describe("find"){
        it("should return the data for dnc "){
            val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

            val columnMap: JsValue = Json.obj(
                "emails" -> "email"
            )

            val csvCreateForm = CsvQueueCreateFormDataV2(
                file_url =  "https://example.com/test.csv",
                column_map =  columnMap,
                force_update_prospects = Some(false),
                csv_upload_type = CsvUploadType.DNCUPLOAD,
                list_name = None,
                campaign_id = None,
                tags = None,
                force_change_ownership = Some(false),
                ignore_email_empty_rows = Some(false),
                ignore_prospects_active_in_other_campaigns = Some(false),
                deduplication_columns = None,
                ignore_prospects_in_other_campaigns = None
            )

                CsvQueue.createV2(
                    accountId = initialData.account.internal_id,
                    teamId = Some(initialData.account.teams.head.team_id),
                    loggedin_id = initialData.account.internal_id,
                    ta_id = Some(initialData.account.teams.head.team_id),
                    data = csvCreateForm
                )

                val result = CsvQueue.find(csvUploadType = CsvUploadType.DNCUPLOAD)

               result match {
                   case Success(data) =>
                       println(data)
                       assert(true)

                   case Failure(e) =>
                       println(e)
                       assert(false)
               }
        }
    }

    describe("getCsvLogs"){
        it("should return the latest csv uploaded data"){
            val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

            val columnMap: JsValue = Json.obj(
                "emails" -> "email"
            )

            val csvCreateForm: List[CsvQueueCreateFormDataV2] = List.fill(10)(
                CsvQueueCreateFormDataV2(
                file_url = "https://example.com/test.csv",
                column_map = columnMap,
                force_update_prospects = Some(false),
                csv_upload_type = CsvUploadType.PROSPECTUPLOAD,
                list_name = None,
                campaign_id = None,
                tags = None,
                force_change_ownership = Some(false),
                ignore_email_empty_rows = Some(false),
                ignore_prospects_active_in_other_campaigns = Some(false),
                deduplication_columns = Some(Seq(SrProspectColumns.Email, SrProspectColumns.Phone)),
                ignore_prospects_in_other_campaigns = None
                )
            )

            csvCreateForm.map { csvFile =>

                CsvQueue.createV2(
                    accountId = initialData.account.internal_id,
                    teamId = Some(initialData.account.teams.head.team_id),
                    loggedin_id = initialData.account.internal_id,
                    ta_id = Some(initialData.account.teams.head.team_id),
                    data = csvFile
                )
            }

            val result = CsvQueue.getCsvLogs(teamId = TeamId(initialData.account.teams.head.team_id))

            result match {
                case Success(data) =>
                    println(data)
                    assert(true)

                case Failure(e) =>
                    println(e)
                    assert(false)
            }
        }
    }

    describe("getLastCsvMapping") {
        it("should return the latest successful csv uploaded column map") {
            val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

            val columnMap: JsValue = Json.obj(
                "emails" -> "email"
            )

            val csvFile: CsvQueueCreateFormDataV2 = CsvQueueCreateFormDataV2(
                    file_url = "https://example.com/test.csv",
                    column_map = columnMap,
                    force_update_prospects = Some(false),
                    csv_upload_type = CsvUploadType.PROSPECTUPLOAD,
                    list_name = None,
                    campaign_id = None,
                    tags = None,
                    force_change_ownership = Some(false),
                    ignore_email_empty_rows = Some(false),
                    ignore_prospects_active_in_other_campaigns = Some(false),
                    deduplication_columns = Some(Seq(SrProspectColumns.Email, SrProspectColumns.Phone)),
                    ignore_prospects_in_other_campaigns = None
                )

            val csvResult = Json.obj(
                    "total_rows" -> 5,
                    "total_created" -> 5,
                    "total_assigned" -> 0,
                    "total_duplicates_found" -> 0,
                    "total_duplicates_updated" -> 0,
                    "total_dnc_prospects_created" -> 0,
                    "total_empty_or_invalid_rows" -> 0,
                    "total_duplicate_emails_in_csv" -> 0,
                    "total_ignored_internal_emails" -> 0,
                    "totaL_duplicates_ignored_for_no_edit_permission" -> 0
            )


            val result: Try[Option[JsValue]] = for {
                csvQueue: CsvQueue <- CsvQueue.createV2(
                    accountId = initialData.account.internal_id,
                    teamId = Some(initialData.account.teams.head.team_id),
                    loggedin_id = initialData.account.internal_id,
                    ta_id = Some(initialData.account.teams.head.team_id),
                    data = csvFile
                )

                _ <- CsvQueue.update(
                    id = csvQueue.id,
                    charsetDetected = None,
                    result = csvResult,
                    parserName = Some("CustomCSVParser"),
                    parserContext = None,
                    logger = Logger
                )

                res <- CsvQueue.getLastCsvMapping(
                    teamId = TeamId(initialData.account.teams.head.team_id),
                    accountId = AccountId(initialData.account.internal_id)
                )
            } yield {
                res
            }

            result match {
                case Success(data) =>
                    assert(data.isDefined)

                case Failure(e) =>
                    println(LogHelpers.getStackTraceAsString(e))
                    assert(false)
            }
        }
    }

}
