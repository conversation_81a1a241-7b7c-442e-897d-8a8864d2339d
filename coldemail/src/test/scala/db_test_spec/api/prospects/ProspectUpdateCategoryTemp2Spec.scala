package db_test_spec.api.prospects

import api.accounts.models.AccountId
import api.accounts.{Account, ProspectCategoriesInDB, TeamId}
import api.prospects.models.{ProspectCategoryId, ProspectCategoryUpdateFlow}
import db_test_spec.api.{DbTestingBeforeAllAndAfterAll, InitialData}
import db_test_spec.api.accounts.fixtures.NewAccountAndEmailSettingData
import db_test_spec.api.campaigns.test_utils.{CampaignUtils, CreateAndStartCampaignData}

import scala.concurrent.Future

class ProspectUpdateCategoryTemp2Spec extends DbTestingBeforeAllAndAfterAll{

  describe("testing ProspectUpdateCategoryTemp2_updateCategoryDB"){

    /*
    it("should fail when backorder is happening"){

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
      val account: Account = initialData.account
//      val emailSetting: EmailSetting = initialData.emailSetting
//      val orgId: OrgId = OrgId(account.org.id)
      val accountId: AccountId = AccountId(account.internal_id)
      val teamId: TeamId = TeamId(account.teams.head.team_id)
      val taId: Long = account.teams.head.access_members.head.ta_id


      val res: Future[Int] = for {
        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 1
        )

        prospect_categories: Seq[ProspectCategoriesInDB] <- Future.fromTry(prospectDAO.getProspectCategories(
          teamId = teamId
        ))

        updated_category <- Future.fromTry(prospectUpdateCategoryTemp2._updateCategoryDB(
          prospectIds = createAndStartCampaignData.addProspect.map(_.id),
          doerAccountId = accountId.id,
          teamId = teamId.id,
          accountName = account.first_name.getOrElse(""),
          prospectCategoryUpdateFlow = ProspectCategoryUpdateFlow.AutoUpdate(
            old_prospect_category_id = ProspectCategoryId(id =prospect_categories.last.id),
            new_prospect_category_id = ProspectCategoryId(id =prospect_categories.head.id)
          ),
          account = Some(account),
          logger = Logger,
          auditRequestLogId = None
        ))


      }yield{
        updated_category
      }

      res
        .map(e => {assert(false)})
        .recover(err => {
        assert(err.getMessage == "Stopping Automatic backorder movement of prospect category")
      })

    }

    it("should auto update the prospect category and doesn't depends on default prospect category") {

      // can we do it using a where clause ?

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
      val account: Account = initialData.account
      //      val emailSetting: EmailSetting = initialData.emailSetting
      //      val orgId: OrgId = OrgId(account.org.id)
      val accountId: AccountId = AccountId(account.internal_id)
      val teamId: TeamId = TeamId(account.teams.head.team_id)
      val taId: Long = account.teams.head.access_members.head.ta_id


      val res: Future[Int] = for {
        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 1
        )

        prospect_categories: Seq[ProspectCategoriesInDB] <- Future.fromTry(prospectDAO.getProspectCategories(
          teamId = teamId
        ))

        // made prospect category as last
        updated_category <- Future.fromTry(prospectUpdateCategoryTemp2._updateCategoryDB(
          prospectIds = createAndStartCampaignData.addProspect.map(_.id),
          doerAccountId = accountId.id,
          teamId = teamId.id,
          accountName = account.first_name.getOrElse(""),
          prospectCategoryUpdateFlow = ProspectCategoryUpdateFlow.AutoUpdate(
            old_prospect_category_id = ProspectCategoryId(id = prospect_categories.head.id),
            new_prospect_category_id = ProspectCategoryId(id = prospect_categories(1).id)
          ),
          account = Some(account),
          logger = Logger,
          auditRequestLogId = None
        ))

        // this should fail as automatically moving prospect category back in order
        updated_category_2 <- Future.fromTry(prospectUpdateCategoryTemp2._updateCategoryDB(
          prospectIds = createAndStartCampaignData.addProspect.map(_.id),
          doerAccountId = accountId.id,
          teamId = teamId.id,
          accountName = account.first_name.getOrElse(""),
          prospectCategoryUpdateFlow = ProspectCategoryUpdateFlow.AutoUpdate(
            old_prospect_category_id = ProspectCategoryId(id = prospect_categories(1).id),
            new_prospect_category_id = ProspectCategoryId(id = prospect_categories(2).id)
        ),
          account = Some(account),
          logger = Logger,
          auditRequestLogId = None
        ))


      } yield {
        updated_category_2
      }

      res
        .map(data => {

//          println(s"\n\nsuccess data : ${data}\n\n")
          assert(data == 1)
        })
        .recover(err => {
          println(s"\n\n err -> ${err} \n\n")
          assert(false)
        })

    }


 */

    it("should not when backorder is happening by admin itself") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
      val account: Account = initialData.account
      //      val emailSetting: EmailSetting = initialData.emailSetting
      //      val orgId: OrgId = OrgId(account.org.id)
      val accountId: AccountId = AccountId(account.internal_id)
      val teamId: TeamId = TeamId(account.teams.head.team_id)
      val taId: Long = account.teams.head.access_members.head.ta_id


      val res: Future[Int] = for {
        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 1
        )

        prospect_categories: Seq[ProspectCategoriesInDB] <- Future.fromTry(prospectDAO.getProspectCategories(
          teamId = teamId
        ))

        updated_category <- Future.fromTry(prospectUpdateCategoryTemp.updateCategoryAndCreateEvent(
          prospectIds = createAndStartCampaignData.addProspect.map(_.id),
          doerAccountId = accountId.id,
          teamId = teamId.id,
          accountName = account.first_name.getOrElse(""),
          prospectCategoryUpdateFlow = ProspectCategoryUpdateFlow.AdminUpdate(
            old_prospect_category_id = Some(ProspectCategoryId(id = prospect_categories.last.id)),
            new_prospect_category_id = ProspectCategoryId(id = prospect_categories.head.id)
          ),
          account = Some(account),
          logger = Logger,
          auditRequestLogId = None
        ))


      } yield {
        updated_category
      }

      res
        .map(data => {

//          println(s"\n\nsuccess data : ${e}\n\n")
          assert(data == 1)
        })
        .recover(err => {
          println(s"\n\n err -> ${err} \n\n")
          assert(false)
        })

    }




  }

}
