package db_test_spec.api.prospects

import api.AppConfig
import api.accounts.{Account, TeamId}
import api.accounts.models.{AccountId, OrgId}
import api.prospects.ProspectBasicForBatchUpdate
import api.prospects.models.{ProspectCategory, ProspectCategoryId, ProspectCategoryNew, ProspectCategoryUpdateFlow, ProspectId}
import db_test_spec.api.accounts.fixtures.NewAccountAndEmailSettingData
import db_test_spec.api.prospects.fixtures.ProspectFixtureForIntegrationTest
import db_test_spec.api.{DbTestingBeforeAllAndAfterAll, InitialData}
import eventframework.ProspectObject
import org.scalatest.ParallelTestExecution
import utils.SRLogger

import scala.util.{Failure, Success, Try}

class ProspectUpdateCategoryTempSpec extends DbTestingBeforeAllAndAfterAll {

  describe("Test autoUpdateProspectCategory") {

    //ALL and ARCHIVE are deleted so this case will never happen
    ignore("should fail if provided a prospect category which is not present in DB") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val account = initialData.account

      val orgId = OrgId(id = account.org.id)

      val accountId: AccountId = AccountId(id = account.internal_id)

      val teamId: TeamId = TeamId(id = account.teams.head.team_id)

      val prospects = ProspectFixtureForIntegrationTest.createUpdateOrAssignProspect(
        campaignId = None, // Don't assign the prospects to any campaign
        accountId = accountId,
        teamId = teamId,
        account = account,
      ).get

      prospectUpdateCategoryTemp.autoUpdateProspectCategory(
        teamId = teamId,
        prospectIds = prospects.map(p => ProspectId(id = p.id)),
        doerAccountId = accountId,
        newProspectCategory = ProspectCategory.OPEN
      ) match {

        case Failure(_) =>

          assert(true)

        case Success(_) =>

            assert(false)

      }

    }

    it("should return 0 if provided prospect ids are invalid") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val account = initialData.account

      val accountId: AccountId = AccountId(id = account.internal_id)

      val teamId: TeamId = TeamId(id = account.teams.head.team_id)

      val invalidProspectIds = Seq(ProspectId(id = ********), ProspectId(id = 23929))

      prospectUpdateCategoryTemp.autoUpdateProspectCategory(
        teamId = teamId,
        prospectIds = invalidProspectIds,
        doerAccountId = accountId,
        newProspectCategory = ProspectCategory.NOT_CATEGORIZED
      ) match {

        case Failure(_) =>

          assert(false)

        case Success(updateCount) =>

          assert(updateCount == 0)

      }

    }

    it("should not update prospect category if invalid move") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val account = initialData.account

      val orgId = OrgId(id = account.org.id)

      val accountId: AccountId = AccountId(id = account.internal_id)

      val teamId: TeamId = TeamId(id = account.teams.head.team_id)

      val prospects = ProspectFixtureForIntegrationTest.createUpdateOrAssignProspect(
        campaignId = None, // Don't assign the prospects to any campaign
        accountId = accountId,
        teamId = teamId,
        account = account,
      ).get


      // update category to BAD CONTACT INFO, so that it won't get updated to CONVERTED by auto update
      prospectUpdateCategoryTemp.autoUpdateProspectCategory(
        teamId = teamId,
        prospectIds = prospects.map(p => ProspectId(id = p.id)),
        doerAccountId = accountId,
        newProspectCategory = ProspectCategory.DELIVERY_FAILED,
      )

      prospectUpdateCategoryTemp.autoUpdateProspectCategory(
        teamId = teamId,
        prospectIds = prospects.map(p => ProspectId(id = p.id)),
        doerAccountId = accountId,
        newProspectCategory = ProspectCategory.CONVERTED
      ) match {

        case Failure(_) =>

          assert(true)

        case Success(_) =>

          prospectDAOService.getCurrentProspectCategoryDetails(
            teamId = teamId,
            prospectIds = prospects.map(p => ProspectId(id = p.id)),
          ) match {

            case Failure(exception) =>

              println(exception)

              assert(false)

            case Success(prsCatDetails) =>

                val prospectsWithConverted = prsCatDetails.filter { prsCat =>

                  Try {
                    ProspectCategoryNew.withName(s = prsCat.prospectCategoryTextId)
                  }
                    .map(_ == ProspectCategoryNew.CONVERTED)
                    .getOrElse(false)

                }

                val areAllCatsBadContact= prsCatDetails.forall { p =>

                  Try(ProspectCategoryNew.withName(p.prospectCategoryTextId))
                    .map(_ == ProspectCategoryNew.BAD_CONTACT_INFO)
                    .getOrElse(false)

                }

                assert(
                  prospectsWithConverted.isEmpty && areAllCatsBadContact
                )

          }

      }

    }

    it("should only update the categories which are valid moves") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val account = initialData.account

      val orgId = OrgId(id = account.org.id)

      val accountId: AccountId = AccountId(id = account.internal_id)

      val teamId: TeamId = TeamId(id = account.teams.head.team_id)

      val prospects = ProspectFixtureForIntegrationTest.createUpdateOrAssignProspect(
        campaignId = None, // Don't assign the prospects to any campaign
        accountId = accountId,
        teamId = teamId,
        account = account,
        generateProspectCountIfNoGivenProspect = 8,
      ).get

      // update category to APPROACHING, so that it won't get updated to INTERESTED by auto update
      prospectUpdateCategoryTemp.autoUpdateProspectCategory(
        teamId = teamId,
        prospectIds = prospects.map(p => ProspectId(id = p.id)),
        doerAccountId = accountId,
        newProspectCategory = ProspectCategory.CONTACTED,
      )

      prospectUpdateCategoryTemp.autoUpdateProspectCategory(
        teamId = teamId,
        prospectIds = prospects.map(p => ProspectId(id = p.id)),
        doerAccountId = accountId,
        newProspectCategory = ProspectCategory.INTERESTED
      ) match {

        case Failure(_) =>

          assert(true)

        case Success(_) =>

          prospectDAOService.getCurrentProspectCategoryDetails(
            teamId = teamId,
            prospectIds = prospects.map(p => ProspectId(id = p.id)),
          ) match {

            case Failure(exception) =>

              println(exception)

              assert(false)

            case Success(prsCatDetails) =>


                val prospectsWithInterested = prsCatDetails.filter { prsCat =>

                  Try {
                    ProspectCategoryNew.withName(s = prsCat.prospectCategoryTextId)
                  }
                    .map(_ == ProspectCategoryNew.INTERESTED)
                    .getOrElse(false)

                }

                val prospectsWithApproachingCat = prsCatDetails.filter { p =>

                  Try(ProspectCategory.withName(p.prospectCategoryTextId))
                    .map(_ == ProspectCategoryNew.APPROACHING)
                    .getOrElse(false)

                }

                assert(
                  prospectsWithInterested.length == 8 &&
                    prospectsWithApproachingCat.isEmpty
                )
          }

      }

    }

    it("should not update prospect category if it was updated manually by admin, even if it is valid") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val account = initialData.account

      val accountId: AccountId = AccountId(id = account.internal_id)

      val teamId: TeamId = TeamId(id = account.teams.head.team_id)

      val prospects = ProspectFixtureForIntegrationTest.createUpdateOrAssignProspect(
        campaignId = None, // Don't assign the prospects to any campaign
        accountId = accountId,
        teamId = teamId,
        account = account,
        generateProspectCountIfNoGivenProspect = 8,
      ).get

      // manually update category to NOT_CATEGORIZED,
      // so that it won't get updated to OPEN by auto update

      val newProspectCategoryId = prospectDAOService.getProspectCategoryId(
        teamId = teamId,
        text_id = ProspectCategory.NOT_CATEGORIZED,
        account = None
      ).get

      prospectUpdateCategoryTemp.updateCategoryAndCreateEvent(
        teamId = teamId.id,
        prospectIds = prospects.map(_.id),
        doerAccountId = accountId.id,
        accountName = "SmartReach",
        prospectCategoryUpdateFlow = ProspectCategoryUpdateFlow.AdminUpdate(
          old_prospect_category_id = None,
          new_prospect_category_id = newProspectCategoryId
        ),
        account = None,
        logger = Logger,
        auditRequestLogId = None
      )

      prospectUpdateCategoryTemp.autoUpdateProspectCategory(
        teamId = teamId,
        prospectIds = prospects.map(p => ProspectId(id = p.id)),
        doerAccountId = accountId,
        newProspectCategory = ProspectCategory.CONTACTED
      ) match {

        case Failure(_) =>

          assert(true)

        case Success(_) =>

          prospectDAOService.getCurrentProspectCategoryDetails(
            teamId = teamId,
            prospectIds = prospects.map(p => ProspectId(id = p.id)),
          ) match {

            case Failure(exception) =>

              println(exception)

              assert(false)

            case Success(prsCatDetails) =>

              val areAllCatsNotCategorized = prsCatDetails.forall { p =>

                Try(ProspectCategoryNew.withName(p.prospectCategoryTextId))
                  .map(_ == ProspectCategoryNew.NOT_CONTACTED)
                  .getOrElse(false)

              }

              assert(
                areAllCatsNotCategorized
              )

          }

      }

    }

  }

  describe("updateBatchCategory") {

    it("should update category for both email prospects and non-email prospects") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData(
        emailNotCompulsoryOrgFlag = Some(true)
      ).get

      val account = initialData.account

      val accountId: AccountId = AccountId(id = account.internal_id)

      val teamId: TeamId = TeamId(id = account.teams.head.team_id)

      val prospects: Seq[ProspectObject] = ProspectFixtureForIntegrationTest.createUpdateOrAssignProspect(
        campaignId = None, // Don't assign the prospects to any campaign
        accountId = accountId,
        teamId = teamId,
        account = account,
        generateProspectCountIfNoGivenProspect = 3,
        emailNotCompulsoryOrgFlag = Some(true)
      ).get

      //by default not categorized and now we updated to open

      val updateToProspectCategoryId = prospectDAOService.getProspectCategoryId(
        teamId = teamId,
        text_id = ProspectCategory.OPEN,
        account = None
      ).get

      prospectUpdateCategoryTemp.updateBatchCategory(
        teamId = teamId.id,
        doerAccountId = accountId.id,
        prospects = prospects.map(p => {
          ProspectBasicForBatchUpdate(
            id = p.id,
            prospect_category_id_custom = p.internal.prospect_category_id_custom,
            prospect_account_id = None
          )
        }),
        accountName = "SmartReach",
        prospectCategoryUpdateFlow = ProspectCategoryUpdateFlow.AdminUpdate(
          old_prospect_category_id = None,
          new_prospect_category_id = updateToProspectCategoryId
        ),
        account = account,
        logger = Logger,
        auditRequestLogId = None
      ) match {

        case Failure(_) =>

          assert(true)

        case Success(_) =>

          prospectDAOService.getCurrentProspectCategoryDetails(
            teamId = teamId,
            prospectIds = prospects.map(p => ProspectId(id = p.id)),
          ) match {

            case Failure(exception) =>
              assert(false)

            case Success(prsCatDetails) =>

              val areAllOpenCategories = prsCatDetails.forall { p =>

                Try(ProspectCategoryNew.withName(p.prospectCategoryTextId))
                  .map(_ == ProspectCategoryNew.APPROACHING)
                  .getOrElse(false)

              }

              assert(
                areAllOpenCategories
              )

          }

      }

    }

    it("should update category for both email prospects and non-email prospects if new category is dnc") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData(
        emailNotCompulsoryOrgFlag = Some(true)
      ).get

      val account = initialData.account

      val accountId: AccountId = AccountId(id = account.internal_id)

      val teamId: TeamId = TeamId(id = account.teams.head.team_id)

      val prospects: Seq[ProspectObject] = ProspectFixtureForIntegrationTest.createUpdateOrAssignProspect(
        campaignId = None, // Don't assign the prospects to any campaign
        accountId = accountId,
        teamId = teamId,
        account = account,
        generateProspectCountIfNoGivenProspect = 3,
        emailNotCompulsoryOrgFlag = Some(true)
      ).get

      // manually update category to NOT_CATEGORIZED,
      // so that it won't get updated to OPEN by auto update

      val newProspectCategoryId = prospectDAOService.getProspectCategoryId(
        teamId = teamId,
        text_id = ProspectCategory.OPEN,
        account = None
      ).get

      prospectUpdateCategoryTemp.updateCategoryAndCreateEvent(
        teamId = teamId.id,
        prospectIds = prospects.map(_.id),
        doerAccountId = accountId.id,
        accountName = "SmartReach",
        prospectCategoryUpdateFlow = ProspectCategoryUpdateFlow.AdminUpdate(
          old_prospect_category_id = None,
          new_prospect_category_id = newProspectCategoryId
        ),
        account = None,
        logger = Logger,
        auditRequestLogId = None
      )

      val updateToProspectCategoryId = prospectDAOService.getProspectCategoryId(
        teamId = teamId,
        text_id = ProspectCategory.DO_NOT_CONTACT,
        account = None
      ).get

      prospectUpdateCategoryTemp.updateBatchCategory(
        teamId = teamId.id,
        doerAccountId = accountId.id,
        prospects = prospects.map(p => {
          ProspectBasicForBatchUpdate(
            id = p.id,
            prospect_category_id_custom = p.internal.prospect_category_id_custom,
            prospect_account_id = None
          )
        }),
        accountName = "SmartReach",
        prospectCategoryUpdateFlow = ProspectCategoryUpdateFlow.AdminUpdate(
          old_prospect_category_id = None,
          new_prospect_category_id = updateToProspectCategoryId
        ),
        account = account,
        logger = Logger,
        auditRequestLogId = None
      ) match {

        case Failure(_) =>

          assert(true)

        case Success(_) =>

          prospectDAOService.getCurrentProspectCategoryDetails(
            teamId = teamId,
            prospectIds = prospects.map(p => ProspectId(id = p.id)),
          ) match {

            case Failure(exception) =>
              assert(false)

            case Success(prsCatDetails) =>

              val areAllDNCs = prsCatDetails.forall { p =>

                Try(ProspectCategory.withName(p.prospectCategoryTextId))
                  .map(_ == ProspectCategory.DO_NOT_CONTACT)
                  .getOrElse(false)

              }

              assert(
                areAllDNCs
              )

          }

      }

    }
  }
}
