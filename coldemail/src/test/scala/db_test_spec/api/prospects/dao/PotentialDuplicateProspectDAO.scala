package db_test_spec.api.prospects.dao

import api.accounts.TeamId
import scalikejdbc.{DB, scalikejdbcSQLInterpolationImplicitDef}

import scala.util.Try
object PotentialDuplicateProspectDAO {

  /**
   * 09-Aug-2024
   *
   * This is only for testing.
   */
  def getPotentialDuplicateProspectLogId(
                                          teamId: TeamId
                                        ): Try[Option[Long]] = Try {

    DB.autoCommit { implicit session =>

      sql"""
          SELECT id from potential_duplicate_prospects_log
          WHERE team_id = ${teamId.id};
         """
        .map(_.long("id"))
        .single
        .apply()

    }

  }
}
