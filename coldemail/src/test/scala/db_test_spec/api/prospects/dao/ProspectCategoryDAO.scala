package db_test_spec.api.prospects.dao

import api.accounts.TeamId
import api.prospects.models.ProspectCategory.ProspectCategory
import scalikejdbc.{DB, scalikejdbcSQLInterpolationImplicitDef}

import scala.util.Try


object ProspectCategoryDAO {

  /**
    * 29-Jun-2024
    *
    * This is only for testing, to simulate failure case.
    */
  def deleteProspectCategory(
    teamId: TeamId,
    prospectCategory: ProspectCategory,
  ): Try[Int] = Try {

    DB.autoCommit { implicit session =>

      sql"""
          DELETE FROM prospect_categories_custom
          WHERE team_id = ${teamId.id}
            AND text_id = ${prospectCategory.toString};
         """
        .update
        .apply()

    }

  }

}
