package db_test_spec.api.prospects

import api.AppConfig
import api.accounts.{Account, ReplyHandling, TeamId}
import api.accounts.models.{AccountId, OrgId}
import api.campaigns.models.CampaignName
import api.campaigns.services.CampaignId
import api.emails.models.EmailReplyType
import api.emails.{DBEmailMessagesSavedResponse, EmailMessageTracked, EmailSetting}
import api.prospects.dao.NeedDuplicateCheckProspect
import api.prospects.models.{PotentialDuplicateProspectLogId, PotentialDuplicateProspectStatus, ProspectCategory, ProspectCategoryNew, ProspectId, ProspectsMetadataUpsert, StepId}
import api.prospects.service.ProspectEmail
import api.prospects.ProspectUpdateFormData
import api.scylla.dao.BounceData
import api.tags.models.{ProspectTagUuid, TagAndUuid}
import api.triggers.AddOrRemoveTagAction
import app.test_fixtures.prospect.ProspectCreateFormDataFixture
import db_test_spec.api.accounts.fixtures.NewAccountAndEmailSettingData
import db_test_spec.api.campaigns.fixtures.{CreateNewCampaignFixture, NewCampaignCreationData}
import db_test_spec.api.campaigns.test_utils.{CampaignUtils, CreateAndStartCampaignData}
import db_test_spec.api.emails.fixtures.DefaultEmailScheduledParameterFixtures
import db_test_spec.api.prospects.dao.{PotentialDuplicateProspectDAO, ProspectCategoryDAO}
import db_test_spec.api.{AppSpecFixture, DbTestingBeforeAllAndAfterAll, InitialData, SRSetupAndDeleteFixtures}
import db_test_spec.api.prospects.fixtures.ProspectFixtureForIntegrationTest
import db_test_spec.api.scheduler.fixtures.{ReSchedulingFixture, ScheduleTaskFixture}
import eventframework.{ProspectBasicInfo, ProspectObject}
import io.smartreach.esp.api.emails.{EmailSettingId, IEmailAddress}
import io.smartreach.esp.utils.email.EmailReplyBounceType
import org.joda.time.DateTime
import play.api.libs.json.{JsValue, Json}
import sr_scheduler.models.ChannelData.EmailChannelData
import utils.{Helpers, SRLogger}
import utils.email.EmailReplyStatus
import utils.helpers.LogHelpers
import utils.mq.channel_scheduler.channels.ScheduleTasksData

import scala.concurrent.Future
import scala.util.{Failure, Success, Try}

class ProspectServiceSpec extends DbTestingBeforeAllAndAfterAll {


  describe("testing if prospect with correct linkedin urls are getting added"){


    it("inserting one correct linkedin url"){
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get


      val result =  ProspectFixtureForIntegrationTest.createUpdateOrAssignProspect(
        givenProspect = Option(Seq(
          AppSpecFixture.prospectCreateFormData.copy(
          linkedin_url = Some("linkedin.com/shanky99")
          ),
          AppSpecFixture.prospectCreateFormData.copy(
            email = Some("<EMAIL>"),
            linkedin_url = Some("www.linkedin.com/shanky99")
          ),
          AppSpecFixture.prospectCreateFormData.copy(
            email = Some("<EMAIL>"),
            linkedin_url = Some("in.linkedin.com/shanky99")
          ),
          AppSpecFixture.prospectCreateFormData.copy(
            email = Some("<EMAIL>"),
            linkedin_url = Some("us.linkedin.com/shanky99")
          ),
          AppSpecFixture.prospectCreateFormData.copy(
            email = Some("<EMAIL>"),
            linkedin_url = Some("https://www.linkedin.com/shanky99")
          ),
          AppSpecFixture.prospectCreateFormData.copy(
            email = Some("<EMAIL>"),
            linkedin_url = Some("https://linkedin.com/shanky99")
          ),
          AppSpecFixture.prospectCreateFormData.copy(
          email = Some("<EMAIL>"),
          linkedin_url = Some("asfjsfkadjsfk/shanky99")
          )
        )),
        campaignId = None,
        accountId = AccountId(initialData.account.internal_id),
        account = initialData.account,
        teamId = TeamId(initialData.account.teams.head.team_id)
      )


      result match {

        case Failure(err) =>

          Logger.error(s"Error while integration test : ",err)
          assert(false)


        case Success(prospects_result) =>

          Logger.error(s"prospects_result : ${prospects_result}")

          val result_map = prospects_result.map(p => (p.email -> p.linkedin_url)).toMap
          assert(result_map(Some("<EMAIL>")).contains("https://linkedin.com/shanky99"))

          //email = Some("<EMAIL>"), ->    linkedin_url = Some("www.linkedin.com/shanky99")
          assert(result_map(Some("<EMAIL>")).contains("https://linkedin.com/shanky99"))

          // email = Some("<EMAIL>"),  linkedin_url = Some("in.linkedin.com/shanky99")
          assert(result_map(Some("<EMAIL>")).contains("https://linkedin.com/shanky99"))

          // email = Some("<EMAIL>"),  linkedin_url = Some("in.linkedin.com/shanky99")
          assert(result_map(Some("<EMAIL>")).contains("https://linkedin.com/shanky99"))

          // email = Some("<EMAIL>"),  linkedin_url = Some("in.linkedin.com/shanky99")
          assert(result_map(Some("<EMAIL>")).contains("https://linkedin.com/shanky99"))

          // email = Some("<EMAIL>"),  linkedin_url = Some("in.linkedin.com/shanky99")
          assert(result_map(Some("<EMAIL>")).contains("https://linkedin.com/shanky99"))

          // email = Some("<EMAIL>"),  linkedin_url = Some("in.linkedin.com/shanky99")
          assert(result_map(Some("<EMAIL>")).contains("asfjsfkadjsfk/shanky99"))


      }

    }


  }


  describe("Test updateLastRepliedAt - autoUpdateProspectCategory") {

    it("should not update prospect category to REPLIED if reply is categorized as OUT_OF_OFFICE") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val account = initialData.account

      val orgId = OrgId(id = account.org.id)

      val accountId: AccountId = AccountId(account.internal_id)

      val teamId: TeamId = TeamId(account.teams.head.team_id)

      val emailSetting = initialData.emailSetting

      val emailSettingId = EmailSettingId(emailSetting.get.id.get.emailSettingId)

      val emailReplyStatus: EmailReplyStatus = EmailReplyStatus(
        replyType = EmailReplyType.OUT_OF_OFFICE,
        isReplied = false,
        isUnsubscribeRequest = false,
        isAutoReply = false,
        isOutOfOfficeReply = true,
        isInvalidEmail = false,
        isForwarded = false,
        bouncedData = None
      )

      val assignProspectsResFut = for {

        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 1,
        )

        _: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
          emailChannelData = EmailChannelData(
            emailSettingId = initialData.emailSetting.get.id.get.emailSettingId
          ),
          teamId = initialData.emailSetting.get.team_id
        )

        _: Int <- ReSchedulingFixture.initializeDataForReScheduling(
          orgId = orgId,
          accountId = accountId,
          teamId = teamId,
          emailSetting = initialData.emailSetting.get,
          campaign = createAndStartCampaignData.campaign,
          prospectList = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id))
        )

        emailMessageTracked: EmailMessageTracked <- Future.successful {
          DefaultEmailScheduledParameterFixtures.generateEmailMessageTracked(
            teamId = teamId,
            accountId = accountId,
            campaignId = CampaignId(createAndStartCampaignData.createCampaign.id),
            stepId = StepId(createAndStartCampaignData.addStep.step_id),
            campaignName = CampaignName(createAndStartCampaignData.createCampaign.name),
            emailSettingId = emailSettingId,
            iEmailAddress = IEmailAddress(
              email = emailSetting.get.email
            ),
            to_emails = Seq(createAndStartCampaignData.addProspect.head.email.get),
            emailReplyStatus = emailReplyStatus
          )
        }

        handleReplyRes: DBEmailMessagesSavedResponse <- Future.fromTry {
          emailReplyTrackingModelV2.saveEmailsAndRepliesFromInboxV3(
            accountId = accountId.id,
            team_id = teamId.id,
            emailMessages = Seq(
              emailMessageTracked
            ),
            inboxEmailSetting = initialData.emailSetting.get,
            replyHandling = ReplyHandling.PAUSE_ALL_PROSPECT_ACCOUNT_CAMPAIGNS_ON_REPLY,
            account = initialData.account,
            senderEmails = Seq(initialData.emailSetting.get.email),
            adminReplyFromSRInbox = true,
            auditRequestLogId = "",
            markProspectAsCompleted = true
          )
        }


      } yield {

        handleReplyRes
      }

      assignProspectsResFut
        .map { handleReplyRes =>

          val prospects = handleReplyRes.emailMessagesFromProspects.flatMap(_.to_prospects)

          val currentProspectCategoryDetails = prospectDAOService.getCurrentProspectCategoryDetails(
            teamId = teamId,
            prospectIds = prospects.map(p => ProspectId(id = p.prospect_id))
          ).get

          val prospectWithRepliedCategory = currentProspectCategoryDetails.filter { pcd =>
            Try {
              ProspectCategoryNew.withName(pcd.prospectCategoryTextId) == ProspectCategoryNew.ENGAGING
            }.getOrElse(false)
          }

          val allProspectCategoriesAreOutOfOffice = currentProspectCategoryDetails.exists { pcd =>
            Try {
              ProspectCategoryNew.withName(pcd.prospectCategoryTextId) == ProspectCategoryNew.APPROACHING
            }.getOrElse(false)
          }

          assert(
            allProspectCategoriesAreOutOfOffice &&
              prospectWithRepliedCategory.isEmpty
          )

        }
        .recover { case e =>

          println(e)

          assert(false)

        }

    }

    it("should not update prospect category to ENGAGING if reply is categorized as AUTO_REPLY") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val account = initialData.account

      val orgId = OrgId(id = account.org.id)

      val accountId: AccountId = AccountId(account.internal_id)

      val teamId: TeamId = TeamId(account.teams.head.team_id)

      val emailSetting = initialData.emailSetting

      val emailSettingId = EmailSettingId(emailSetting.get.id.get.emailSettingId)

      val emailReplyStatus: EmailReplyStatus = EmailReplyStatus(
        replyType = EmailReplyType.AUTO_REPLY,
        isReplied = false,
        isUnsubscribeRequest = false,
        isAutoReply = true,
        isOutOfOfficeReply = false,
        isInvalidEmail = false,
        isForwarded = false,
        bouncedData = None
      )

      val assignProspectsResFut = for {

        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 1,
        )

        _: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
          emailChannelData = EmailChannelData(
            emailSettingId = initialData.emailSetting.get.id.get.emailSettingId
          ),
          teamId = initialData.emailSetting.get.team_id
        )

        _: Int <- ReSchedulingFixture.initializeDataForReScheduling(
          orgId = orgId,
          accountId = accountId,
          teamId = teamId,
          emailSetting = initialData.emailSetting.get,
          campaign = createAndStartCampaignData.campaign,
          prospectList = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id))
        )

        emailMessageTracked: EmailMessageTracked <- Future.successful {
          DefaultEmailScheduledParameterFixtures.generateEmailMessageTracked(
            teamId = teamId,
            accountId = accountId,
            campaignId = CampaignId(createAndStartCampaignData.createCampaign.id),
            stepId = StepId(createAndStartCampaignData.addStep.step_id),
            campaignName = CampaignName(createAndStartCampaignData.createCampaign.name),
            emailSettingId = emailSettingId,
            iEmailAddress = IEmailAddress(
              email = emailSetting.get.email
            ),
            to_emails = Seq(createAndStartCampaignData.addProspect.head.email.get),
            emailReplyStatus = emailReplyStatus
          )
        }

        handleReplyRes: DBEmailMessagesSavedResponse <- Future.fromTry {
          emailReplyTrackingModelV2.saveEmailsAndRepliesFromInboxV3(
            accountId = accountId.id,
            team_id = teamId.id,
            emailMessages = Seq(
              emailMessageTracked
            ),
            inboxEmailSetting = initialData.emailSetting.get,
            replyHandling = ReplyHandling.PAUSE_ALL_PROSPECT_ACCOUNT_CAMPAIGNS_ON_REPLY,
            account = initialData.account,
            senderEmails = Seq(initialData.emailSetting.get.email),
            adminReplyFromSRInbox = true,
            auditRequestLogId = "",
            markProspectAsCompleted = true
          )
        }


      } yield {

        handleReplyRes
      }

      assignProspectsResFut
        .map { handleReplyRes =>

          val prospects = handleReplyRes.emailMessagesFromProspects.flatMap(_.to_prospects)

          val currentProspectCategoryDetails = prospectDAOService.getCurrentProspectCategoryDetails(
            teamId = teamId,
            prospectIds = prospects.map(p => ProspectId(id = p.prospect_id))
          ).get

          val prospectWithRepliedCategory = currentProspectCategoryDetails.filter { pcd =>
            Try {
              ProspectCategoryNew.withName(pcd.prospectCategoryTextId) == ProspectCategoryNew.ENGAGING
            }.getOrElse(false)
          }

          val allProspectCategoriesAreAutoReply = currentProspectCategoryDetails.exists { pcd =>
            Try {
              ProspectCategoryNew.withName(pcd.prospectCategoryTextId) == ProspectCategoryNew.APPROACHING
            }.getOrElse(false)
          }

          assert(
            allProspectCategoriesAreAutoReply &&
              prospectWithRepliedCategory.isEmpty
          )

        }
        .recover { case e =>

          println(e)

          assert(false)

        }

    }

    it("should not update prospect category to ENGAGING if bounce data is defined") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val account = initialData.account

      val orgId = OrgId(id = account.org.id)

      val accountId: AccountId = AccountId(account.internal_id)

      val teamId: TeamId = TeamId(account.teams.head.team_id)

      val emailSetting = initialData.emailSetting

      val emailSettingId = EmailSettingId(emailSetting.get.id.get.emailSettingId)

      val emailReplyStatus: EmailReplyStatus = EmailReplyStatus(
        replyType = EmailReplyType.DELIVERY_FAILED,
        isReplied = false,
        isUnsubscribeRequest = false,
        isAutoReply = false,
        isOutOfOfficeReply = false,
        isInvalidEmail = true,
        isForwarded = false,
        bouncedData = Some(
          BounceData(
            bounced_at = DateTime.now(),
            bounce_type = EmailReplyBounceType.EmailAddressNotFound,
            bounce_reason = "Some Bounce Reason",
            is_soft_bounced = false
          )
        )
      )

      val assignProspectsResFut = for {

        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 1,
        )

        _: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
          emailChannelData = EmailChannelData(
            emailSettingId = initialData.emailSetting.get.id.get.emailSettingId
          ),
          teamId = initialData.emailSetting.get.team_id
        )

        _: Int <- ReSchedulingFixture.initializeDataForReScheduling(
          orgId = orgId,
          accountId = accountId,
          teamId = teamId,
          emailSetting = initialData.emailSetting.get,
          campaign = createAndStartCampaignData.campaign,
          prospectList = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id))
        )

        emailMessageTracked: EmailMessageTracked <- Future.successful {
          DefaultEmailScheduledParameterFixtures.generateEmailMessageTracked(
            teamId = teamId,
            accountId = accountId,
            campaignId = CampaignId(createAndStartCampaignData.createCampaign.id),
            stepId = StepId(createAndStartCampaignData.addStep.step_id),
            campaignName = CampaignName(createAndStartCampaignData.createCampaign.name),
            emailSettingId = emailSettingId,
            iEmailAddress = IEmailAddress(
              email = emailSetting.get.email
            ),
            to_emails = Seq(createAndStartCampaignData.addProspect.head.email.get),
            emailReplyStatus = emailReplyStatus
          )
        }

        handleReplyRes: DBEmailMessagesSavedResponse <- Future.fromTry {
          emailReplyTrackingModelV2.saveEmailsAndRepliesFromInboxV3(
            accountId = accountId.id,
            team_id = teamId.id,
            emailMessages = Seq(
              emailMessageTracked
            ),
            inboxEmailSetting = initialData.emailSetting.get,
            replyHandling = ReplyHandling.PAUSE_ALL_PROSPECT_ACCOUNT_CAMPAIGNS_ON_REPLY,
            account = initialData.account,
            senderEmails = Seq(initialData.emailSetting.get.email),
            adminReplyFromSRInbox = true,
            auditRequestLogId = "",
            markProspectAsCompleted = true
          )
        }


      } yield {

        handleReplyRes
      }

      assignProspectsResFut
        .map { handleReplyRes =>

          val prospects = handleReplyRes.emailMessagesFromProspects.flatMap(_.to_prospects)

          val currentProspectCategoryDetails = prospectDAOService.getCurrentProspectCategoryDetails(
            teamId = teamId,
            prospectIds = prospects.map(p => ProspectId(id = p.prospect_id))
          ).get


            val prospectWithRepliedCategory = currentProspectCategoryDetails.filter { pcd =>
              Try {
                ProspectCategoryNew.withName(pcd.prospectCategoryTextId) == ProspectCategoryNew.ENGAGING
              }.getOrElse(false)
            }

            val allProspectCategoriesAreDeliveryFailed = currentProspectCategoryDetails.exists { pcd =>
              Try {
                ProspectCategoryNew.withName(pcd.prospectCategoryTextId) == ProspectCategoryNew.BAD_CONTACT_INFO
              }.getOrElse(false)
            }

            assert(
              allProspectCategoriesAreDeliveryFailed &&
                prospectWithRepliedCategory.isEmpty
            )

        }
        .recover { case e =>

          println(e)

          assert(false)

        }

    }

    it("should update prospect category to REPLIED when a replied comes from prospect") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val account = initialData.account

      val orgId = OrgId(id = account.org.id)

      val accountId: AccountId = AccountId(account.internal_id)

      val teamId: TeamId = TeamId(account.teams.head.team_id)

      val emailSetting = initialData.emailSetting

      val emailSettingId = EmailSettingId(emailSetting.get.id.get.emailSettingId)

      val emailReplyStatus: EmailReplyStatus = EmailReplyStatus(
        replyType = EmailReplyType.NOT_CATEGORIZED,
        isReplied = true,
        isUnsubscribeRequest = false,
        isAutoReply = false,
        isOutOfOfficeReply = false,
        isInvalidEmail = false,
        isForwarded = false,
        bouncedData = None
      )

      val assignProspectsResFut = for {

        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 1,
        )

        _: ScheduleTasksData <- ScheduleTaskFixture.scheduleTaskForEmailChannel(
          emailChannelData = EmailChannelData(
            emailSettingId = initialData.emailSetting.get.id.get.emailSettingId
          ),
          teamId = initialData.emailSetting.get.team_id
        )

        _: Int <- ReSchedulingFixture.initializeDataForReScheduling(
          orgId = orgId,
          accountId = accountId,
          teamId = teamId,
          emailSetting = initialData.emailSetting.get,
          campaign = createAndStartCampaignData.campaign,
          prospectList = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id))
        )

        emailMessageTracked: EmailMessageTracked <- Future.successful {
          DefaultEmailScheduledParameterFixtures.generateEmailMessageTracked(
            teamId = teamId,
            accountId = accountId,
            campaignId = CampaignId(createAndStartCampaignData.createCampaign.id),
            stepId = StepId(createAndStartCampaignData.addStep.step_id),
            campaignName = CampaignName(createAndStartCampaignData.createCampaign.name),
            emailSettingId = emailSettingId,
            iEmailAddress = IEmailAddress(
              email = emailSetting.get.email
            ),
            to_emails = Seq(createAndStartCampaignData.addProspect.head.email.get),
            emailReplyStatus = emailReplyStatus
          )
        }

        handleReplyRes: DBEmailMessagesSavedResponse <- Future.fromTry {
          emailReplyTrackingModelV2.saveEmailsAndRepliesFromInboxV3(
            accountId = accountId.id,
            team_id = teamId.id,
            emailMessages = Seq(
              emailMessageTracked.copy(
                recorded_at = DateTime.now().plusMinutes(2) //adding time so that there is some time before we record reply to a email
              )
            ),
            inboxEmailSetting = initialData.emailSetting.get,
            replyHandling = ReplyHandling.PAUSE_ALL_PROSPECT_ACCOUNT_CAMPAIGNS_ON_REPLY,
            account = initialData.account,
            senderEmails = Seq(initialData.emailSetting.get.email),
            adminReplyFromSRInbox = true,
            auditRequestLogId = "",
            markProspectAsCompleted = true
          )
        }


      } yield {

        handleReplyRes
      }

      assignProspectsResFut
        .map { handleReplyRes =>

          val prospects = handleReplyRes.emailMessagesFromProspects.flatMap(_.to_prospects)

          val currentProspectCategoryDetails = prospectDAOService.getCurrentProspectCategoryDetails(
            teamId = teamId,
            prospectIds = prospects.map(p => ProspectId(id = p.prospect_id))
          ).get


            val allProspectCategoriesAreReplied = currentProspectCategoryDetails.exists { pcd =>
              Try {
                ProspectCategoryNew.withName(pcd.prospectCategoryTextId) == ProspectCategoryNew.ENGAGING
              }.getOrElse(false)
            }

            assert(
              allProspectCategoriesAreReplied
            )

        }
        .recover { case e =>

          println(e)

          assert(false)

        }

    }

  }

  describe("updateSpecificProspect") {
    it("should update the email compulsory prospect") {
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
      val generateProspectsForCampaign: Int = 3

      val account: Account = initialData.account
      val accountId: AccountId = AccountId(account.internal_id)
      val emailSetting: EmailSetting = initialData.emailSetting.get
      val teamId: TeamId = TeamId(account.teams.head.team_id)
      val orgId: OrgId = OrgId(account.org.id)
      val result: Future[Seq[ProspectObject]] = for {
        campaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = generateProspectsForCampaign,
          emailSettings = Some(emailSetting)
        )

        prospects: Seq[ProspectObject] <- Future {
          campaignData.addProspect
        }

        updateProspects: Seq[ProspectObject] <- Future.fromTry {
          val res = prospects.map(prospect => {
            prospectService.updateSpecificProspect(
              data = ProspectUpdateFormData(
                email = prospect.email,
                first_name = prospect.first_name,
                last_name = prospect.last_name,
                custom_fields = prospect.custom_fields,

                prospect_category_id_custom = None,

                list = prospect.list,
                company = prospect.company,
                city = prospect.city,
                country = prospect.country,
                timezone = prospect.timezone,

                state = prospect.state,
                job_title = prospect.job_title,
                phone = Some("+************"),
                linkedin_url = prospect.linkedin_url,

                prospect_account_id = prospect.internal.prospect_account_id
              ),
              prospect = prospect,
              prospectId = prospect.id,
              team_id = teamId.id,
              user_id = accountId.id,
              doerAccount = account,
              accountName = Helpers.getAccountName(account),
              permittedAccountIds = Seq(accountId.id),
              Logger = Logger,
              auditRequestLogId = "req_log_id"
            )
          })
          Helpers.seqEitherToEitherSeq(res) match {
            case Right(value) => Success(value)
            case Left(err) => Failure(new Throwable(s"Error"))
          }
        }

      } yield {
        updateProspects
      }

      result.map(r => {
          assert(r.nonEmpty && r.length == generateProspectsForCampaign)
        })
        .recover(e => {
          println(s"error: ${LogHelpers.getStackTraceAsString(e)}")
          assert(false)
        })
    }

    it("should update the email not compulsory prospect") {
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData(
        emailNotCompulsoryOrgFlag = Some(true)
      ).get
      val generateProspectsForCampaign: Int = 3

      val account: Account = initialData.account
      val accountId: AccountId = AccountId(account.internal_id)
      val emailSetting: EmailSetting = initialData.emailSetting.get
      val teamId: TeamId = TeamId(account.teams.head.team_id)
      val orgId: OrgId = OrgId(account.org.id)
      val result: Future[Seq[ProspectObject]] = for {
        campaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = generateProspectsForCampaign,
          emailSettings = Some(emailSetting)
        )

        prospects: Seq[ProspectObject] <- Future {
          campaignData.addProspect
        }

        updateProspects: Seq[ProspectObject] <- Future.fromTry {
          val res = prospects.map(prospect => {
            prospectService.updateSpecificProspect(
              data = ProspectUpdateFormData(
                email = None,
                first_name = prospect.first_name,
                last_name = prospect.last_name,
                custom_fields = prospect.custom_fields,

                prospect_category_id_custom = None,

                list = prospect.list,
                company = prospect.company,
                city = prospect.city,
                country = prospect.country,
                timezone = prospect.timezone,

                state = prospect.state,
                job_title = prospect.job_title,
                phone = Some("+************"),
                linkedin_url = prospect.linkedin_url,

                prospect_account_id = prospect.internal.prospect_account_id
              ),
              prospect = prospect,
              prospectId = prospect.id,
              team_id = teamId.id,
              user_id = accountId.id,
              doerAccount = account,
              accountName = Helpers.getAccountName(account),
              permittedAccountIds = Seq(accountId.id),
              Logger = Logger,
              auditRequestLogId = "req_log_id"
            )
          })
          Helpers.seqEitherToEitherSeq(res) match {
            case Right(value) => Success(value)
            case Left(err) => Failure(new Throwable(s"Error"))
          }
        }

      } yield {
        updateProspects
      }

      result.map(r => {
          assert(r.nonEmpty && r.length == generateProspectsForCampaign)
        })
        .recover(e => {
          println(s"error: ${LogHelpers.getStackTraceAsString(e)}")
          assert(false)
        })
    }

  }


  describe("createOrUpdateProspects") {

    it("should make the email lowerCase") {
      val result = for {
        initialData <- Future.fromTry {
          SRSetupAndDeleteFixtures.createInitialData()
        }
        createCampaign: NewCampaignCreationData <- CreateNewCampaignFixture.createNewCampaign(
          orgId = OrgId(initialData.account.org.id),
          accountId = AccountId(initialData.account.internal_id),
          accountName = s"${initialData.account.first_name.getOrElse("Animesh")} ${initialData.account.last_name.getOrElse("Kumar")}"
        )
        prospect <- Future.fromTry {
          ProspectFixtureForIntegrationTest.createUpdateOrAssignProspect(
            givenProspect = Some(Seq(
              ProspectCreateFormDataFixture.prospectCreateFormData.copy(
                email = Some("<EMAIL>"),
                first_name = Some("Animesh"),
                last_name = Some("Kumar"),
                owner_id = Some(createCampaign.campaignWithStatsAndEmail.owner_id),
                company = Some("Test Company"),
                city = Some("Pune"),
                country = Some("India"),
                timezone = Some("Asia/Kolkata"),
                state = Some("Maharashtra"),
                job_title = Some("SDE"),
                phone = Some("**********"),
                linkedin_url = Some("https://www.linkedin.com/in/animesh-kumar-9a1171191/")
              ))),
            campaignId = Some(CampaignId(createCampaign.campaignWithStatsAndEmail.id)),
            account = createCampaign.newTeamCreationData.updatedAccount,
            teamId = TeamId(createCampaign.campaignWithStatsAndEmail.team_id),
            accountId = AccountId(createCampaign.newTeamCreationData.updatedAccount.internal_id)
          )
        }
      } yield {
        prospect
      }

      result.map(p => {
        assert(p.length == 1)
        assert(p.head.email.get == "<EMAIL>".toLowerCase)
      }).recover(e => {
        println(s"error ---- $e")
        assert(false)
      })
    }
  }

}
