package db_test_spec.api.prospects.fixtures

import api.accounts.email.models.SrMxCheckESPType
import api.accounts.models.AccountId
import api.accounts.{Account, TeamId}
import api.campaigns.models.IgnoreProspectsInOtherCampaigns
import api.campaigns.services.CampaignId
import api.prospects.models.{SrProspectColumns, UpdateProspectType}
import api.prospects.{CreateOrUpdateProspectsResult, ProspectCreateFormData}
import db_test_spec.api.prospects.fixtures.DefaultProspectParameterFixture.defaultProspects
import eventframework.ProspectObject
import io.smartreach.sr_dns_utils.{DNSService, DomainPublicDNSAddOrUpdateData}
import play.api.libs.json.{JsValue, Json}
import utils.SRLogger
import utils.emailvalidation.EmailValidationService
import utils.testapp.TestAppTrait

import scala.concurrent.duration.Duration
import scala.concurrent.{Await, Future}
import scala.util.{Failure, Success, Try}

object ProspectFixtureForIntegrationTest extends TestAppTrait {

  def createUpdateOrAssignProspectFuture(
    accountId: AccountId,
    teamId: TeamId,
    account: Account,
    campaignId: Option[CampaignId] = None,
    generateProspectCountIfNoGivenProspect: Int = 2,
    emailNotCompulsoryOrgFlag: Option[Boolean] = Some(false),
    givenProspect: Option[Seq[ProspectCreateFormData]] = None
  )(using Logger: SRLogger): Future[Seq[ProspectObject]] = {

    val prospectsData = if (givenProspect.isDefined) {
      givenProspect
    } else {
      Some(defaultProspects(
        prospectCount = generateProspectCountIfNoGivenProspect,
        emailNotCompulsoryOrgFlag = emailNotCompulsoryOrgFlag
      ))
    }

    for {
      create: CreateOrUpdateProspectsResult <- Future {
        prospectService.createOrUpdateProspects(
            ownerAccountId = accountId.id,
            teamId = teamId.id,
            listName = None,
            prospects = prospectsData.get,
            updateProspectType = UpdateProspectType.ForceUpdate,
            ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads = true,

            doerAccount = account,
            prospectSource = None,
            prospectAccountId = None,

            campaign_id = campaignId.map(p => p.id),
            prospect_tags = None,
            ignoreProspectInOtherCampaign = IgnoreProspectsInOtherCampaigns.DoNotIgnore,
            deduplicationColumns = Some(Seq(SrProspectColumns.Email)),
            auditRequestLogId = None,
            batchInsertLimit = 1000,

            SRLogger = Logger
          )
          .get
      }

      findProspect: Seq[ProspectObject] <- Future {
        prospectDAOService.find(
            byProspectIds = create.created_ids ++ create.updated_ids ++ create.assigned_ids,
            teamId = teamId.id,
            Logger = Logger,
            limit = 10000
          )
          .get
      }
      insert_into_DNS: Seq[String] <- Future {

        domainPublicDNSDAO.insertOrUpdateBatch(
            domainsData = findProspect.flatMap { p =>
                if (p.email.isDefined) {
                  val (_, domain) = EmailValidationService.getLowercasedNameAndDomainFromEmail(email = p.email.get)
                  Some(domain)

                } else None
              }
              .distinct
              .map(d => {

                DomainPublicDNSAddOrUpdateData(
                  domain = d,
                  dns_service = DNSService.GoogleDNS,
                  raw_response = Json.obj("test" -> "test"),
                  mx_inbox_provider = SrMxCheckESPType.Google,
                  is_valid = true
                )

              })
          )
          .get

      }

    } yield {
      findProspect
    }


  }


  // use the Future version above, this is a legacy fn for backward compatibility
  def createUpdateOrAssignProspect(
                                    accountId: AccountId,
                                    teamId: TeamId,
                                    account: Account,
                                    campaignId: Option[CampaignId] = None,
                                    generateProspectCountIfNoGivenProspect: Int = 2,
                                    emailNotCompulsoryOrgFlag: Option[Boolean] = Some(false),
                                    givenProspect: Option[Seq[ProspectCreateFormData]] = None
                                  )(using Logger: SRLogger): Try[Seq[ProspectObject]] = Try {

    val res: Future[Seq[ProspectObject]] = createUpdateOrAssignProspectFuture(
      accountId = accountId,
      teamId = teamId,
      account = account,
      campaignId = campaignId,
      generateProspectCountIfNoGivenProspect = generateProspectCountIfNoGivenProspect,
      emailNotCompulsoryOrgFlag = emailNotCompulsoryOrgFlag,
      givenProspect = givenProspect,
    )

    Await.result(res, Duration.Inf)

  }
}
