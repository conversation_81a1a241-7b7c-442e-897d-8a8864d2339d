package db_test_spec.api.prospects

import api.accounts.{Account, TeamId}
import api.accounts.models.{AccountId, OrgId}
import api.emails.EmailSetting
import api.prospects.ProspectUpdateFormData
import api.prospects.dao.{NeedDuplicateCheckProspect, ProspectIdAndPotentialDuplicateProspectId}
import api.prospects.models.{PotentialDuplicateProspectId, PotentialDuplicateProspectLogId, ProspectId, ProspectsMetadataUpsert}
import api.tags.models.{ProspectTag, ProspectTagData, ProspectTagUuid, TagAndUuid, TagUuid}
import api.triggers.AddOrRemoveTagAction
import db_test_spec.api.accounts.fixtures.NewAccountAndEmailSettingData
import db_test_spec.api.campaigns.test_utils.{CampaignUtils, CreateAndStartCampaignData}
import db_test_spec.api.prospects.dao.PotentialDuplicateProspectDAO
import db_test_spec.api.{DbTestingBeforeAllAndAfterAll, InitialData}
import eventframework.ProspectObject
import scalikejdbc.DBSession
import utils.dbutils.DbAndSession
import utils.{Helpers, SRLogger}
import utils.helpers.LogHelpers
import utils.mq.merge_duplicate_prospects.MergeDuplicatesLogDetails

import scala.concurrent.Future
import scala.util.Try

class ProspectTagDAOLegacySpec extends DbTestingBeforeAllAndAfterAll{

  describe("getDuplicateProspectTags and insertTagsForMasterProspect") {

    it("should return selected data") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData(
        emailNotCompulsoryOrgFlag = Some(true)
      ).get

      val account: Account = initialData.account
      val accountId: AccountId = AccountId(account.internal_id)
      val emailSetting: EmailSetting = initialData.emailSetting.get
      val teamId: TeamId = TeamId(account.teams.head.team_id)
      val orgId: OrgId = OrgId(account.org.id)


      val result: Future[(List[Long], Long)] = for {
        //1. start campaign for 3 prospects
        campaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 3,
          emailSettings = Some(emailSetting)
        )

        prospects: Seq[ProspectObject] <- Future {
          campaignData.addProspect
        }

        //2. update prospect data to make it duplicate
        updateProspects: Seq[Option[Long]] <- Future.fromTry {
          val res = prospects.map(prospect => {
            prospectDAOService.update(
              permittedAccountIds = Seq(account.internal_id),
              actingAccountId = accountId.id,
              prospect_owner_account_id = accountId,
              id = prospect.id,
              teamId = teamId.id,
              updateProspect = ProspectUpdateFormData(
                email = prospect.email,
                first_name = prospect.first_name,
                last_name = prospect.last_name,
                custom_fields = prospect.custom_fields,

                prospect_category_id_custom = None,

                list = prospect.list,
                company = prospect.company,
                city = prospect.city,
                country = prospect.country,
                timezone = prospect.timezone,

                state = prospect.state,
                job_title = prospect.job_title,
                phone = Some("+************"),
                linkedin_url = prospect.linkedin_url,

                prospect_account_id = prospect.internal.prospect_account_id
              )
            )
          })
          Helpers.seqTryToTrySeq(res)
        }

        masterProspect: ProspectObject <- Future {
          prospects.head
        }

        //2. update prospect metadata to mark need duplicate check
        updateMetadata: Int <- Future.fromTry {
          accountOrgBillingRelatedInfoDAO.insertProspectsMetadataOrUpdateLastTouchedAt(
            prospectId = ProspectId(masterProspect.id),
            teamId = teamId,
            data = ProspectsMetadataUpsert.ProspectDuplicateCheckData(
              need_duplicate_check = true
            )
          )
        }

        //3. save potential duplicate logs
        updatedLogs: Int <- {
          mqPotentialDuplicateProspects.processMessage(msg = NeedDuplicateCheckProspect(
            prospectId = ProspectId(masterProspect.id), orgId = orgId, teamId = teamId
          ))
        }

        log_id: Option[Long] <- Future.fromTry {
          PotentialDuplicateProspectDAO.getPotentialDuplicateProspectLogId(teamId)
        }


        duplicateProspects: List[ProspectIdAndPotentialDuplicateProspectId] <- Future.fromTry {
          potentialDuplicateProspectsDAO.getPotentialDuplicateProspectIdForProspects(
            masterProspectId = ProspectId(masterProspect.id),
            logId = PotentialDuplicateProspectLogId(log_id.get),
            teamId = teamId
          )
        }

        createdTags: Int <- Future.fromTry {
          tagService.updateTagsForProspects(
            action = AddOrRemoveTagAction.ADD,
            tags = Seq(TagAndUuid(
              tag = "test",
              uuid = ProspectTagUuid("tag_abcd")
            )),
            prospectIdsWithPermission = duplicateProspects.filterNot(_.isMasterProspect).map(_.prospectId.id),
            teamId = teamId.id,
            accountId = accountId.id
          )
        }

        insertedTagsForProspects: List[Long] <- Future.fromTry {
          val dbAndSession: DbAndSession = dbUtils.startLocalTx()
          val db = dbAndSession.db
          implicit val session: DBSession = dbAndSession.session
          val res: Try[List[Long]] = prospectTagDAOLegacy.getDuplicateProspectTags(
            duplicateProspects = duplicateProspects
          ).map(data => {
            prospectTagDAOLegacy.insertTagsForMasterProspect(
              prospectTagData = data,
              masterProspectId = ProspectId(masterProspect.id)
            )
          }).get
          dbUtils.commitAndCloseSession(db = db)
          res
        }

      } yield {
        (insertedTagsForProspects, masterProspect.id)
      }

      result.map(r => {
          assert(r._1.nonEmpty && r._1.contains(r._2))
        })
        .recover(e => {
          println(s"error: ${LogHelpers.getStackTraceAsString(e)}")
          assert(false)
        })

    }

  }
}
