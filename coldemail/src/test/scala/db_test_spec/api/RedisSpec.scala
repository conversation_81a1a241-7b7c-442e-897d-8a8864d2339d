package db_test_spec.api

import utils.SRLogger

class RedisSpec extends DbTestingBeforeAllAndAfterAll {

  describe("redis Lock working") {
    given Logger: SRLogger= new SRLogger("")
    it("should not lock the same thing twice") {

      val result = for {
        lock1 <- workFlowAttemptJedisDAOService.acquireAndCheckSingleLock(
          additionalKeyToLock = ("test_key_crm_lock"),
          expireInSeconds = 60
        )
        lock2 <- {
          println(s"check redis now")
          Thread.sleep(2000)
          workFlowAttemptJedisDAOService.acquireAndCheckSingleLock(
            additionalKeyToLock = ("test_key_crm_lock"),
            expireInSeconds = 60
          )
        }
      } yield (lock1, lock2)

      assert(result.isSuccess)
      assert(result.get._1)
      assert(!result.get._2)
    }
  }

}
