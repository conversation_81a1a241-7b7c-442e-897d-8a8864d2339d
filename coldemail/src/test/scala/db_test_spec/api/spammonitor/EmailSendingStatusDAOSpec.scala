package db_test_spec.api.spammonitor

import api.spammonitor.model.{EmailSendStatus, EmailSendingEntityType}
import db_test_spec.api.DbTestingBeforeAllAndAfterAll
import org.joda.time.DateTime

class EmailSendingStatusDAOSpec extends DbTestingBeforeAllAndAfterAll{


  describe("getEmailSendingStatusAll") {
    it("success") {
      val result = emailSendingStatusDAO.getEmailSendingStatusAll(
        emailSendStatuses = Seq(EmailSendStatus.UnderReview, EmailSendStatus.WarningForOneDay),
        getBefore = None
      )

      assert(result.isSuccess)
    }

    it("success with before at") {
      val result = emailSendingStatusDAO.getEmailSendingStatusAll(
        emailSendStatuses = Seq(EmailSendStatus.UnderReview, EmailSendStatus.WarningForOneDay),
        getBefore = Some(DateTime.now())
      )

      assert(result.isSuccess)
    }

  }

  describe("getEmailSendingStatusForEntityType") {
    it("success") {
      val result = emailSendingStatusDAO.getEmailSendingStatusForEntityType(
        emailSendStatuses = Seq(EmailSendStatus.UnderReview, EmailSendStatus.WarningForOneDay),
        emailSendingEntityType = EmailSendingEntityType.Campaign
      )

      assert(result.isSuccess)
    }

  }

}
