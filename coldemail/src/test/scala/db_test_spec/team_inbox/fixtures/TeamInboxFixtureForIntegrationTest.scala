package db_test_spec.team_inbox.fixtures

import api.accounts.TeamId
import api.accounts.models.{AccountId, OrgId}
import api.team_inbox.model.TeamInboxDetails
import api.team_inbox.service.TeamInboxService
import db_test_spec.utils.SrRandomTestUtils
import io.smartreach.esp.api.emails.EmailSettingId
import org.joda.time.DateTime
import scalikejdbc.DBSession
import utils.{Helpers, SRLogger}
import utils.testapp.TestAppTrait

import scala.util.{Failure, Success, Try}

object TeamInboxFixtureForIntegrationTest extends TestAppTrait {

  def createTeamInbox(
                       orgId: OrgId,
                       accountId: AccountId,
                       emailSettingId: EmailSettingId,
                       teamId: TeamId,
                       userRoleIds: List[Long],
                       teamInboxName: String = SrRandomTestUtils.getRandomStringOfLengthN(10),
                       initialSyncRequired: <PERSON>ole<PERSON> = false,
                       initialSyncRequiredTill: Option[DateTime] = None,
                       totalEmailsInInboxWhileStartingSync: Int = 0,
                     )(using Logger: SRLogger): Try[Option[TeamInboxDetails]] = {

    val dbAndSession = dbUtils.startLocalTx()
    implicit val session: DBSession = dbAndSession.session
    val db = dbAndSession.db
    val result = for {
      create: Option[Long] <- teamInboxDAO.createTeamInbox(
        org_id = orgId.id,
        email_setting_id = emailSettingId.emailSettingId,
        team_inbox_name = teamInboxName,
        initial_sync_required = initialSyncRequired,
        initial_sync_requested_till = initialSyncRequiredTill,
        total_emails_in_inbox_while_starting_sync = totalEmailsInInboxWhileStartingSync,
        team_id = teamId.id
      )

      addPermission: List[Long] <-  teamInboxDAO.insertTeamInboxPermissions(
        team_inbox_id = create.get,
        user_role_ids = userRoleIds,
        org_id = orgId.id
      )

      /*
      12-jun-2024
       Since we are adding reply sentiments for a team when team is created. This should not be required

      _: Seq[Option[Long]] <- Helpers.seqTryToTrySeq {
        TeamInboxService.defaultReplySentimentTypes.map { replySentimentType =>
          replySentimentDAOService.addReplySentimentsForATeam(
            team_id = teamId.id,
            replySentimentType = replySentimentType,
            uuid = srUuidUtils.generateReplySentimentUuid()
          )(session)
        }
      }


       */

    } yield {
      create
    }

    dbUtils.commitAndCloseSession(db)
    result match {
      case Success(value) =>
        teamInboxDAO.getTeamInboxDetails(
          team_inbox_id = value.get,
          org_id = orgId.id
        )
      case Failure(exception) => Failure(exception)
    }

  }

}
