package db_test_spec.inbox_placement

import api.campaigns.dao.{DomainInboxPlacementLog, InboxPlacementCheckDetails, TestEmailDetails}
import api.emails.{EmailSetting, InboxPlacementCheckSentEmailScheduledDetails}
import db_test_spec.api.{DbTestingBeforeAllAndAfterAll, InitialData}
import org.joda.time.DateTime
import api.accounts.{Account, TeamId}
import api.accounts.models.{AccountId, OrgId}
import api.campaigns.models.InboxPlacementCheckLogId
import api.emails.EmailSetting
import api.emails.models.{EmailSettingDomain, LandedFolderType}
import api.scheduler_report.ReportData
import db_test_spec.api.accounts.fixtures.NewAccountAndEmailSettingData
import db_test_spec.api.campaigns.CampaignCreationFixtureForIntegrationTest
import db_test_spec.api.emails.fixtures.EmailSettingFixtureForIntegrationTest
import db_test_spec.inbox_placement.dao.InboxPlacementCheckDAO.getInboxPlacementQueuedData
import io.smartreach.esp.api.emails.EmailSettingId
import play.api.libs.json.Json
import sr_scheduler.CampaignStatus
import utils.SRLogger
import utils.mq.inbox_placement.{AnalyzedDataToBeUpdated, AnalyzedHeader}

import scala.concurrent.{Await, ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

class InboxPlacementCheckDAOSpec extends DbTestingBeforeAllAndAfterAll{


  private var receiver_email_setting_id: EmailSettingId = EmailSettingId(0)

  lazy val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
  
  
  describe("InboxPlacementCheckDAO") {
    it("should select email domains") {

      //creating running campaign for test
      CampaignCreationFixtureForIntegrationTest.createDefaultCampaign(initialData = initialData).map(c => {
        assert(c.campaignWithStatsAndEmail.status == CampaignStatus.RUNNING)
        val inboxPlacementCheckDetails: Try[List[InboxPlacementCheckDetails]] = inboxPlacementCheckDAO.getUncheckedDomainForInboxPlacementCheck(
          testOrgIds = Set(OrgId(initialData.account.org.id))
        )

        assert(inboxPlacementCheckDetails.isSuccess)
        assert(inboxPlacementCheckDetails.get.nonEmpty)
        //TODO: add new org and run campaign in that to pass below assert
//        assert(inboxPlacementCheckDetails.get.head.sender_email_setting_id.emailSettingId == 1L)

      }).recover({ case e =>
        assert(false)
      })


    }

    it("should insert record in internal_inbox_placement_check_logs") {
      val inboxPlacementCheckDetails: Try[List[InboxPlacementCheckDetails]] = inboxPlacementCheckDAO.getUncheckedDomainForInboxPlacementCheck(
        testOrgIds = Set(OrgId(initialData.account.org.id)),
      )
      val account: Account = initialData.account
      val orgId: OrgId = OrgId(account.org.id)
      val teamId: TeamId = TeamId(account.teams.head.team_id)
      val accountId: AccountId = AccountId(account.internal_id)
      val taId: Long = account.teams.head.access_members.head.ta_id
      EmailSettingFixtureForIntegrationTest.createEmailSetting(
        orgId = orgId,
        teamId = teamId,
        accountId = accountId,
        taId = taId
      ) match {
        case Failure(e) =>
          println(e)
          assert(false)
        case Success(eset) =>
          receiver_email_setting_id = eset.id.get
          val res = inboxPlacementCheckDAO.queueDomainForInboxPlacementCheck(
            inboxPlacementCheckDetails = inboxPlacementCheckDetails.get,
            receiver_email_setting_id = eset.id.get
          )
          assert(res.isSuccess)
          assert(res.get.nonEmpty)
      }

    }
  }

  describe("updateSentEmailDetails") {
    it("should update successfully") {

      getInboxPlacementQueuedData() match {
        case Failure(_) => assert(false)

        case Success(inbp) =>
          val res = inboxPlacementCheckDAO.updateSentEmailDetails(
            teamId = inbp.get.teamId,
            senderEmailSettingId = inbp.get.sender_email_setting_id,
            inboxPlacementCheckSentEmailScheduledDetails = InboxPlacementCheckSentEmailScheduledDetails(
              emailScheduledId = 1L,
              sent_at = DateTime.now(),
              message_id = "abcdefghi"
            )
          )
          assert(res.isSuccess && res.get == 1)
      }

    }
  }

  describe("getSentEmailDetails"){
    it("should return success") {

      val res: Try[List[TestEmailDetails]] = inboxPlacementCheckDAO.getSentEmailDetails(
        receiver_email_setting_id = receiver_email_setting_id
      )
      assert(res.isSuccess)
      assert(res.get.nonEmpty)
    }
  }

  describe("updateLandedFolderForReceiver") {
    it("should update successfully") {
      val res: Try[Int] = inboxPlacementCheckDAO.updateLandedFolderForReceiver(
        inpb_log_id = InboxPlacementCheckLogId(1L),
        landedFolderType = LandedFolderType.PRIMARY,
        originalLandedFolder = "Inbox"
      )

      assert(res.isSuccess && res.get == 1)
    }
  }

  describe("getTrackedEmailLogsToAnalyze") {
    it("should return success") {
      val res = inboxPlacementCheckDAO.getTrackedEmailLogsToAnalyze()

      assert(res.isSuccess)
      assert(res.get.nonEmpty)
    }
  }

  describe("updateEmailHeaderAnalysis") {
    it("should return success") {
      val res: Try[Int] = inboxPlacementCheckDAO.updateEmailHeaderAnalysis(
        analyzedDataToBeUpdated = AnalyzedDataToBeUpdated(
          analyzedHeader = AnalyzedHeader(
            spf_is_valid = None, dkim_is_valid = None, dmarc_is_valid = None
          ),
          inbp_id = InboxPlacementCheckLogId(1L),
          emailHeaders = Json.obj("Authentication-Results" -> "abcd")
        )
      )

      assert(res.isSuccess)
    }
  }

  describe("getDataForDailyReporting") {
    it("should return success") {
      val startOfDay = DateTime.now().minusDays(1).withTimeAtStartOfDay()
      val endOfDay = DateTime.now().withTimeAtStartOfDay().minusSeconds(1)
      val res: Try[List[ReportData.InboxPlacementCheckLogs]] = inboxPlacementCheckDAO.getDataForDailyReporting(
        startOfDay = startOfDay,
        endOfDay = endOfDay,
        testOrgIds = Set(OrgId(initialData.account.org.id)),
      )

      assert(res.isSuccess)
    }
  }

  describe("getInboxPlacementLogForDomain") {
    it("should return success") {
      val res: Try[List[DomainInboxPlacementLog]] = inboxPlacementCheckDAO.getInboxPlacementLogForDomain(
        domain = "gmail.com",
        teamId = TeamId(1L)
      )

      assert(res.isSuccess)
    }
  }

  describe("getDomainsLastAnalyzedAt") {
    it("should return success") {
      val res = inboxPlacementCheckDAO.getDomainsLastAnalyzedAt(
        domains = List(EmailSettingDomain("smartreach.io, warmuplabs.com")),
        teamId = TeamId(1L)
      )

      assert(res.isSuccess)
    }
  }

  describe("updateStatusForSendEmailFailure") {
    it("should update successfully") {

      val res = inboxPlacementCheckDAO.updateStatusForSendEmailFailure(
        teamId = initialData.emailSetting.get.team_id,
        senderEmailSettingId = initialData.emailSetting.get.id.get,
      )
      assert(res.isSuccess)

    }
  }
}
