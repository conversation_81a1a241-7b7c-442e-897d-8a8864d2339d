package db_test_spec.utils.fixtures

import api.accounts.models.OrgId
import io.sr.billing_common.models.SrPlanLimits
import scalikejdbc.{DB, scalikejdbcSQLInterpolationImplicitDef}
import utils.testapp.TestAppTrait

import scala.util.Try


object PlanLimitFixture extends TestAppTrait {

  val default_plan_limits: SrPlanLimits = SrPlanLimits(
    base_licence_count = 1,
    additional_licence_count = 0,
    total_sending_email_account = 20,
    max_prospects_contacted = 2000,
    max_client_teams = 100,
    max_prospects_saved = 234561,
    max_crm_integrations = 200,
    max_calling_seats = 200,
    max_purchased_domains = 0,
    max_purchased_email_accounts = 0,
    max_purchased_zapmail_domains = 2,
    max_purchased_zapmail_email_accounts = 2,
    max_automated_linkedin_accounts = 200,
  )

  def updateEmailsAndDomainsAddon(
    max_purchased_email_accounts: Int,
    max_purchased_domains: Int,
    org_id: OrgId,
  ): Try[Int] = Try {

    DB autoCommit { implicit session =>

      sql"""
           UPDATE
              organizations
           SET
              plan_purchased_email_accounts_max = $max_purchased_email_accounts,
              plan_purchased_domains_max = $max_purchased_domains
           WHERE
              id = ${org_id.id}
         """
        .update
        .apply()

    }

  }

}
