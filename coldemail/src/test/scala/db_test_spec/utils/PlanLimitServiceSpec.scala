package db_test_spec.utils

import api.accounts.TeamId
import api.accounts.models.OrgId
import api.email_infra_integrations.models.PlatformType
import db_test_spec.api.{DbTestingBeforeAllAndAfterAll, InitialData}
import db_test_spec.api.accounts.fixtures.NewAccountAndEmailSettingData
import db_test_spec.utils.fixtures.PlanLimitFixture
import utils.AddonLimitReachedException
import utils.helpers.LogHelpers

import scala.util.{Failure, Success}

class PlanLimitServiceSpec extends DbTestingBeforeAllAndAfterAll {

  describe("Test getAdditionalRequiredEmailsAndDomainForPurchase") {

    it("should return required addon qty if user does not have sufficient addon quantities available") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val account = initialData.account

      val teamId: TeamId = TeamId(id = account.teams.head.team_id)

      val max_purchased_email_accounts = account.org.counts.max_purchased_email_accounts
      val max_purchased_domains = account.org.counts.max_purchased_domains

      val current_purchased_email_accounts = account.org.counts.current_purchased_email_accounts
      val current_purchased_domains = account.org.counts.current_purchased_domains

      val toBePurchasedDomainQty = max_purchased_domains + 23
      val toBePurchasedEmailQty = max_purchased_email_accounts + 12

      planLimitService.getAdditionalRequiredEmailsAndDomainForPurchase(
        teamId = teamId,
        toBePurchasedDomainQty = toBePurchasedDomainQty,
        toBePurchasedEmailQty = toBePurchasedEmailQty,
        platformType = PlatformType.MAILDOSO,
      ) match {
        case Failure(exception) =>

          println(LogHelpers.getStackTraceAsString(exception))

          assert(false)

        case Success(requiredEmailAndDomains) =>

          val req_domains_qty = (toBePurchasedDomainQty + current_purchased_domains) - max_purchased_domains
          val req_emails_qty = (toBePurchasedEmailQty + current_purchased_email_accounts) - max_purchased_email_accounts

          assert(
            requiredEmailAndDomains.required_purchased_domains_qty == req_domains_qty &&
              requiredEmailAndDomains.required_purchased_emails_qty == req_emails_qty
          )

      }

    }

    it("should pass when purchase qty is equal to available addon limit") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val account = initialData.account

      val teamId: TeamId = TeamId(id = account.teams.head.team_id)

      val orgId = OrgId(id = account.org.id)

      val toBePurchasedDomainQty = 23
      val toBePurchasedEmailQty = 12

      PlanLimitFixture.updateEmailsAndDomainsAddon(
        org_id = orgId,
        max_purchased_domains = toBePurchasedDomainQty,
        max_purchased_email_accounts = toBePurchasedEmailQty,
      ).get

      planLimitService.getAdditionalRequiredEmailsAndDomainForPurchase(
        teamId = teamId,
        toBePurchasedDomainQty = toBePurchasedDomainQty,
        toBePurchasedEmailQty = toBePurchasedEmailQty,
        platformType = PlatformType.MAILDOSO,
      ) match {
        case Failure(exception) =>

          println(LogHelpers.getStackTraceAsString(exception))

          assert(false)

        case Success(requiredEmailAndDomains) =>

          assert(
            requiredEmailAndDomains.required_purchased_domains_qty == 0 &&
              requiredEmailAndDomains.required_purchased_emails_qty == 0
          )

      }

    }

    it("should return 0 if available addon limit is greater than purchase quantity") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val account = initialData.account

      val teamId: TeamId = TeamId(id = account.teams.head.team_id)

      val orgId = OrgId(id = account.org.id)

      val toBePurchasedDomainQty = 23
      val toBePurchasedEmailQty = 12

      PlanLimitFixture.updateEmailsAndDomainsAddon(
        org_id = orgId,
        max_purchased_domains = toBePurchasedDomainQty + 2,
        max_purchased_email_accounts = toBePurchasedEmailQty + 5,
      ).get

      planLimitService.getAdditionalRequiredEmailsAndDomainForPurchase(
        teamId = teamId,
        toBePurchasedDomainQty = toBePurchasedDomainQty,
        toBePurchasedEmailQty = toBePurchasedEmailQty,
        platformType = PlatformType.MAILDOSO,
      ) match {
        case Failure(exception) =>

          println(LogHelpers.getStackTraceAsString(exception))

          assert(false)

        case Success(requiredEmailAndDomains) =>

          assert(
            requiredEmailAndDomains.required_purchased_domains_qty == 0 &&
              requiredEmailAndDomains.required_purchased_emails_qty == 0
          )

      }

    }

  }

  describe("Test checkEmailsAndDomainsAddonLimitReached") {

    it("should return error if user does not have sufficient addon quantities available") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val account = initialData.account

      val teamId: TeamId = TeamId(id = account.teams.head.team_id)

      val max_purchased_email_accounts = account.org.counts.max_purchased_email_accounts
      val max_purchased_domains = account.org.counts.max_purchased_domains

      val toBePurchasedDomainQty = max_purchased_domains + 23
      val toBePurchasedEmailQty = max_purchased_email_accounts + 12

      planLimitService.checkEmailsAndDomainsAddonLimitReached(
        teamId = teamId,
        toBePurchasedDomainQty = toBePurchasedDomainQty,
        toBePurchasedEmailQty = toBePurchasedEmailQty,
        platformType = PlatformType.MAILDOSO,
      ) match {
        case Failure(exception) =>

          exception match {

            case _: AddonLimitReachedException =>

              assert(true)

            case _ =>

              assert(false)

          }

        case Success(_) =>

          assert(false)

      }

    }

    it("should not return error when purchase qty is equal to available addon limit") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val account = initialData.account

      val teamId: TeamId = TeamId(id = account.teams.head.team_id)

      val orgId = OrgId(id = account.org.id)

      val toBePurchasedDomainQty = 23
      val toBePurchasedEmailQty = 12

      PlanLimitFixture.updateEmailsAndDomainsAddon(
        org_id = orgId,
        max_purchased_domains = toBePurchasedDomainQty,
        max_purchased_email_accounts = toBePurchasedEmailQty,
      ).get

      planLimitService.checkEmailsAndDomainsAddonLimitReached(
        teamId = teamId,
        toBePurchasedDomainQty = toBePurchasedDomainQty,
        toBePurchasedEmailQty = toBePurchasedEmailQty,
        platformType = PlatformType.MAILDOSO,
      ) match {

        case Failure(exception) =>

          println(LogHelpers.getStackTraceAsString(exception))

          assert(false)

        case Success(_) =>

          assert(true)

      }

    }

    it("should not return error if available addon limit is greater than purchase quantity") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val account = initialData.account

      val teamId: TeamId = TeamId(id = account.teams.head.team_id)

      val orgId = OrgId(id = account.org.id)

      val toBePurchasedDomainQty = 23
      val toBePurchasedEmailQty = 12

      PlanLimitFixture.updateEmailsAndDomainsAddon(
        org_id = orgId,
        max_purchased_domains = toBePurchasedDomainQty + 2,
        max_purchased_email_accounts = toBePurchasedEmailQty + 5,
      ).get

      planLimitService.checkEmailsAndDomainsAddonLimitReached(
        teamId = teamId,
        toBePurchasedDomainQty = toBePurchasedDomainQty,
        toBePurchasedEmailQty = toBePurchasedEmailQty,
        platformType = PlatformType.MAILDOSO,
      ) match {

        case Failure(exception) =>

          println(LogHelpers.getStackTraceAsString(exception))

          assert(false)

        case Success(_) =>

          assert(true)

      }

    }

  }

}
