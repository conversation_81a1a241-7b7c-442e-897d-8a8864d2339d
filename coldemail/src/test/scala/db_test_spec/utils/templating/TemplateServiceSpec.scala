package db_test_spec.utils.templating


import api.accounts.TeamId
import api.accounts.models.AccountId
import api.columns.{CustomColumnDefCreateForm, FieldTypeEnum, GenerateCustomColumnData, InternalMergeTagValuesForProspect}
import api.prospects.models.ProspectId
import db_test_spec.api.accounts.fixtures.{MagicColumnFixtures, NewAccountAndEmailSettingData}
import db_test_spec.api.{DbTestingBeforeAllAndAfterAll, InitialData}
import sr_scheduler.models.ChannelType
import utils.Helpers

import scala.util.Try

class TemplateServiceSpec extends DbTestingBeforeAllAndAfterAll {

  describe("Test getOverallMergeTags") {

    it(
      "overall merge tags should not contain magic column if not generated for the prospect"
    ) {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val accountId = AccountId(id = initialData.account.internal_id)

      val team = initialData.account.teams.head

      val teamId = TeamId(id = team.team_id)

      val taId = team.access_members.head.ta_id

      val prospectObjList = initialData.prospectsResult

      val currentProspectObj = prospectObjList.head

      val magicCol = prospectColumnDef.create(
        accountId = accountId.id,
        teamId = teamId.id,
        taId = taId,
        data = CustomColumnDefCreateForm(
          name = "book_fair",
          field_type = FieldTypeEnum.MAGIC,
          magic_prompt = Some("generate an invitation to book fair"),
        ),
        channel = None,
      ).get.get

      val prospectAfterCreatingMagicCol = prospectDAOService.findProspectAndFailIfNotPresent(
        prospectId = ProspectId(id = currentProspectObj.id),
        teamId = teamId,
        Logger = Logger,
      ).get

      val overallTags = templateService.getOverallMergeTags(
        prospect = prospectAfterCreatingMagicCol,
        internalMergeTags = InternalMergeTagValuesForProspect(
          sender_name = "",
          sender_first_name = "", sender_last_name = "",
          unsubscribe_link = None,
          previous_subject = None,
          signature = None,
          sender_phone_number = None,
          calendar_link = None,
        ),
        channel = ChannelType.EmailChannel
      )

      assert(!overallTags.containsKey(magicCol.name))

    }

    it(
      "overall merge tags should contain magic column if generated for the prospect"
    ) {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val accountId = AccountId(id = initialData.account.internal_id)

      val team = initialData.account.teams.head

      val teamId = TeamId(id = team.team_id)

      val taId = team.access_members.head.ta_id

      val prospectObjList = initialData.prospectsResult

      val currentProspectObj = prospectObjList.head

      val magicCol = prospectColumnDef.create(
        accountId = accountId.id,
        teamId = teamId.id,
        taId = taId,
        data = CustomColumnDefCreateForm(
          name = "book_fair",
          field_type = FieldTypeEnum.MAGIC,
          magic_prompt = Some("generate an invitation to book fair"),
        ),
        channel = None,
      ).get.get

      val generatedOutput = "Some magic column output - book_fair"

      val _ = MagicColumnFixtures.generateMagicColumnForProspects(
        teamId = teamId,
        accountId = accountId,
        columnId = magicCol.id.get,
        prospectIds = List(ProspectId(id = currentProspectObj.id)),
        generatedOutput = generatedOutput,
      )

      val prospectAfterCreatingMagicCol = prospectDAOService.findProspectAndFailIfNotPresent(
        prospectId = ProspectId(id = currentProspectObj.id),
        teamId = teamId,
        Logger = Logger,
      ).get

      val overallTags = templateService.getOverallMergeTags(
        prospect = prospectAfterCreatingMagicCol,
        internalMergeTags = InternalMergeTagValuesForProspect(
          sender_name = "",
          sender_first_name = "", sender_last_name = "",
          unsubscribe_link = None,
          previous_subject = None,
          signature = None,
          sender_phone_number = None,
          calendar_link = None,
        ),
        channel = ChannelType.EmailChannel
      )

      assert(
        overallTags.containsKey(magicCol.name)
          && overallTags.get(magicCol.name).toString == generatedOutput
      )

    }

    it(
      "Should Replace \n with <br/> in overall merge tags for magic columns for EmailChannel"
    ) {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val accountId = AccountId(id = initialData.account.internal_id)

      val team = initialData.account.teams.head

      val teamId = TeamId(id = team.team_id)

      val taId = team.access_members.head.ta_id

      val prospectObjList = initialData.prospectsResult

      val currentProspectObj = prospectObjList.head

      val magicCol = prospectColumnDef.create(
        accountId = accountId.id,
        teamId = teamId.id,
        taId = taId,
        data = CustomColumnDefCreateForm(
          name = "book_fair",
          field_type = FieldTypeEnum.MAGIC,
          magic_prompt = Some("generate an invitation to book fair"),
        ),
        channel = None,
      ).get.get

      val generatedOutput = "Some magic column output \n - book_fair"

      val _ = MagicColumnFixtures.generateMagicColumnForProspects(
        teamId = teamId,
        accountId = accountId,
        columnId = magicCol.id.get,
        prospectIds = List(ProspectId(id = currentProspectObj.id)),
        generatedOutput = generatedOutput,
      )

      val prospectAfterCreatingMagicCol = prospectDAOService.findProspectAndFailIfNotPresent(
        prospectId = ProspectId(id = currentProspectObj.id),
        teamId = teamId,
        Logger = Logger,
      ).get

      val overallTags = templateService.getOverallMergeTags(
        prospect = prospectAfterCreatingMagicCol,
        internalMergeTags = InternalMergeTagValuesForProspect(
          sender_name = "",
          sender_first_name = "", sender_last_name = "",
          unsubscribe_link = None,
          previous_subject = None,
          signature = None,
          sender_phone_number = None,
          calendar_link = None,
        ),
        channel = ChannelType.EmailChannel
      )

      assert(
        overallTags.containsKey(magicCol.name)
          && overallTags.get(magicCol.name).toString != generatedOutput
      )

      assert(
        overallTags.get(magicCol.name).toString == "Some magic column output <br/> - book_fair"
      )

    }


    it(
      "Should not Replace \n with <br/> in overall merge tags for magic columns for Non Email Channels"
    ) {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val accountId = AccountId(id = initialData.account.internal_id)

      val team = initialData.account.teams.head

      val teamId = TeamId(id = team.team_id)

      val taId = team.access_members.head.ta_id

      val prospectObjList = initialData.prospectsResult

      val currentProspectObj = prospectObjList.head

      val magicCol = prospectColumnDef.create(
        accountId = accountId.id,
        teamId = teamId.id,
        taId = taId,
        data = CustomColumnDefCreateForm(
          name = "book_fair",
          field_type = FieldTypeEnum.MAGIC,
          magic_prompt = Some("generate an invitation to book fair"),
        ),
        channel = None,
      ).get.get

      val generatedOutput = "Some magic column output \n - book_fair"

      val _ = MagicColumnFixtures.generateMagicColumnForProspects(
        teamId = teamId,
        accountId = accountId,
        columnId = magicCol.id.get,
        prospectIds = List(ProspectId(id = currentProspectObj.id)),
        generatedOutput = generatedOutput,
      )

      val prospectAfterCreatingMagicCol = prospectDAOService.findProspectAndFailIfNotPresent(
        prospectId = ProspectId(id = currentProspectObj.id),
        teamId = teamId,
        Logger = Logger,
      ).get

      val overallTags = templateService.getOverallMergeTags(
        prospect = prospectAfterCreatingMagicCol,
        internalMergeTags = InternalMergeTagValuesForProspect(
          sender_name = "",
          sender_first_name = "", sender_last_name = "",
          unsubscribe_link = None,
          previous_subject = None,
          signature = None,
          sender_phone_number = None,
          calendar_link = None,
        ),
        channel = ChannelType.LinkedinChannel
      )

      assert(
        overallTags.containsKey(magicCol.name)
          && overallTags.get(magicCol.name).toString == generatedOutput
      )

    }


  }

}
