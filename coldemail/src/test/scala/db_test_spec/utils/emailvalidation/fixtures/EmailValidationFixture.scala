package db_test_spec.utils.emailvalidation.fixtures

import api.campaigns.services.CampaignId
import api.lead_finder.models.LeadValidationBatchReqId
import scalikejdbc.{DB, scalikejdbcSQLInterpolationImplicitDef}
import utils.dbutils.SQLUtils
import utils.emailvalidation.models.{EmailValidationInitiator, EmailValidationProcessStatusV2}

import scala.util.Try


case class EmailValidationInfoTest(
  id: Long,
  email: String,
  initiatorWithValue: EmailValidationInitiator.EmailValidationInitiatorWithValue
)

object EmailValidationFixture {

  def getToBeQueuedEmailValidations(
    emails: Seq[String]
  ): Try[List[EmailValidationInfoTest]] = Try {

    if (emails.isEmpty) {

      List()

    } else {

      DB readOnly { implicit session =>

        sql"""
            SELECT
              id,
              email,

              validation_initiated_by,
              initiator_campaign_id,
              lead_validation_batch_request_id
            FROM
              email_validations
            WHERE
              process_status = ${EmailValidationProcessStatusV2.TO_BE_QUEUED.toString}
              AND email IN ${SQLUtils.generateSQLValuesClause(arr = emails)}
           """
          .map { rs =>

            // If validation_initiated_by is null then we will consider it as InitiatedByCampaign
            val validationInitiatedBy = EmailValidationInitiator.fromKey(
              key = rs.stringOpt("validation_initiated_by")
                .getOrElse(EmailValidationInitiator.InitiatedByCampaign.toString)
            ).get

            val id = rs.long("id")
            val email = rs.string("email")

            validationInitiatedBy match {

              case EmailValidationInitiator.InitiatedByCampaign =>

                // Reading it directly as long because in tests campaign will not be deleted
                val initiatorCampaignId = CampaignId(id = rs.long("initiator_campaign_id"))

                EmailValidationInfoTest(
                  id = id,
                  email = email,
                  initiatorWithValue = EmailValidationInitiator.InitiatedByCampaign(
                    initiatorCampaignId = initiatorCampaignId
                  )
                )

              case EmailValidationInitiator.InitiatedByLeadFinder =>

                // Reading it directly as long because in tests batch req will not be deleted
                val leadValidationBatchReqId = LeadValidationBatchReqId(
                  id = rs.long("lead_validation_batch_request_id")
                )

                EmailValidationInfoTest(
                  id = id,
                  email = email,
                  initiatorWithValue = EmailValidationInitiator.InitiatedByLeadFinder(
                    leadValidationBatchReqId = leadValidationBatchReqId
                  )
                )

            }

          }
          .list
          .apply()

      }

    }

  }

}
