package db_test_spec.utils

import scala.util.Random

object SrRandomTestUtils {

  def getRandomStringOfLengthN(
                                length: Int
                              ): String = {
    Random.alphanumeric.take(length).mkString("")
  }

  def getRandomEmailStringOfLengthN(
                                     length: Int,
                                     domainName: String = "gmail.com"
                                   ): String = {
    Random.alphanumeric.take(length).mkString("") + "@" + domainName
  }

  def getRandomSmartReachEmailStringOfLengthN(
                                               length: Int
                                             ): String = {
    Random.alphanumeric.take(length).mkString("") + "@smartreach.io"
  }

  def getRandomLinkedinUrlStringOfUserNameLengthN(
                                                   length: Int
                                                 ): String = {
    s"https://linkedin.com/in/${getRandomStringOfLengthN(length)}"
  }

  def getRandomEmail(n: Int): Seq[String] = {

    Seq.fill(n)(Random.alphanumeric.take(7).mkString("") + "@abc.com")

  }

  def getRandomDomain(n: Int): Seq[String] = {

    Seq.fill(n)(Random.alphanumeric.take(4).mkString("") + ".com")

  }

  def getRandomPhoneNumber: String = {

    val preFix = Random.between(minInclusive = 7, maxExclusive = 10).toString

    val postFix = (1 to 9)
      .map(_ => Random.between(minInclusive = 0, maxExclusive = 10))
      .mkString

    val phoneNumber = s"$preFix$postFix"

    val phoneNumberWithCountryCode = s"+91$phoneNumber"

    phoneNumberWithCountryCode

  }

}
