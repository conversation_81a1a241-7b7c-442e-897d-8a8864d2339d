package db_test_spec.sr_audit_logs

import api.prospects.models.ProspectId
import db_test_spec.api.DbTestingBeforeAllAndAfterAll
import api.sr_audit_logs.models.{EventDataType, EventLog, EventType}
import org.joda.time.DateTime
import utils.helpers.LogHelpers
import utils.mq.webhook.model.TriggerSource

import scala.util.{Failure, Success}

class EventLogDAOSpec extends DbTestingBeforeAllAndAfterAll {
  describe("EventLogDAO") {

    it("insertEventLogBatchInDB") {

      val res = eventLogDAO.insertEventLogBatchInDB(
        data = Seq(
          EventLog(
            event_log_id = "event_log_id1",
            audit_request_log_id = "audit_request_log_id1",
            event_data_type = EventDataType.PushEventDataType.CreatedProspectsEventData(
              created_id = 1L,
              ownerAccountId = 1L,
              teamId = 1L,
              triggerPath = Some(TriggerSource.OTHER)
            ),
            team_id = 1,
            account_id = 1,
            created_at = DateTime.now()
          ),
          EventLog(
            event_log_id = "event_log_id2",
            audit_request_log_id = "audit_request_log_id2",
            event_data_type = EventDataType.PushEventDataType.CreatedProspectsEventData(
              created_id = 1L,
              ownerAccountId = 1L,
              teamId = 1L,
              triggerPath = Some(TriggerSource.OTHER)
          ),
            team_id = 1,
            account_id = 1,
            created_at = DateTime.now()
          ),
          EventLog(
            event_log_id = "event_log_id3",
            audit_request_log_id = "audit_request_log_id3",
            event_data_type = EventDataType.PushEventDataType.CreatedProspectsEventData(
              created_id = 1L,
              ownerAccountId = 1L,
              teamId = 1L,
              triggerPath = Some(TriggerSource.OTHER)
          ),
            team_id = 1,
            account_id = 1,
            created_at = DateTime.now()
          )
        )
      )
      res match {
        case Success(value) =>       assert(res.isSuccess)

        case Failure(exception) =>
          println(LogHelpers.getStackTraceAsString(exception))
          assert(false)

      }
    }
  }
}
