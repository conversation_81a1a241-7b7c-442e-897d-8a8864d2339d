package test_models.tasks

import play.api.libs.json.{Js<PERSON><PERSON><PERSON>, <PERSON>s<PERSON><PERSON>, <PERSON>sSuc<PERSON>, <PERSON>s<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>s, Writes}

import scala.util.{Failure, Success, Try}


sealed trait TestTaskCreatedVia {

  def toKey: String

}

object TestTaskCreated<PERSON>ia extends play.api.Logging  {

  private val SCHEDULER = "scheduler"
  private val MANUAL = "manual"

  case object Scheduler extends TestTaskCreatedVia{
    override def toKey: String = SCHEDULER
  }

  case object Manual extends TestTaskCreatedVia {
    override def toKey: String = MANUAL
  }

  def fromString(key: String): Try[TestTaskCreatedVia] = Try {

    key match {
      case `SCHEDULER` => Scheduler
      case `MANUAL` => Manual
    }
  }

  implicit def reads: Reads[TestTaskCreatedVia] = {

    case JsString(value) =>
      fromString(key = value) match {

        case Failure(exception) => JsError(exception.toString)

        case Success(data) => JsSuccess(value = data)
      }

    case _ =>

      logger.error("we aren't intreseted in this as it will be JsString only")
      JsError(s"""Expected a TestTaskCreatedVia string, got something else """)
  }

  implicit def writes: Writes[TestTaskCreatedVia] = new Writes[TestTaskCreatedVia] {
    def writes(ev: TestTaskCreatedVia): JsValue = {

      ev match {
        case Scheduler => Json.toJson(SCHEDULER)

        case Manual => Json.toJson(MANUAL)

      }

    }
  }


}


