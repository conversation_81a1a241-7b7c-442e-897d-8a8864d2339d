package test_models.tasks

import play.api.libs.json.{<PERSON>s<PERSON><PERSON><PERSON>, <PERSON>sS<PERSON>, JsSuc<PERSON>, <PERSON>s<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Writes}

import scala.util.{Failure, Success, Try}

sealed trait TestTaskPriority{
  def toKey: String

  override def toString: String = toKey
}

object TestTaskPriority extends play.api.Logging {

  private val critical = "critical"
  private val high = "high"
  private val normal = "normal"
  private val low = "low"

  case object Critical extends TestTaskPriority {
    def toKey: String = critical
  }
  case object High extends TestTaskPriority  {
    def toKey: String = high
  }
  case object Normal extends TestTaskPriority  {
    def toKey: String = normal
  }
  case object Low extends TestTaskPriority  {
    def toKey: String = low
  }

  def fromString(key: String): Try[TestTaskPriority] = Try{

    key match {
      case `critical` => Critical
      case `high` => High
      case `normal` => Normal
      case `low` => Low
    }
  }

  implicit def reads: Reads[TestTaskPriority] ={

    case JsString(value) =>
      fromString(key = value) match {

        case Failure(exception) => JsError(exception.toString)

        case Success(data) => JsSuccess(value = data)
      }

    case _ =>

      logger.error("we aren't intreseted in this as it will be JsString only")
      JsError(s"""Expected a priority string, got something else """)
  }


  implicit def writes: Writes[TestTaskPriority] = new Writes[TestTaskPriority] {
    def writes(ev: TestTaskPriority): JsValue = {

      ev match {
        case Critical => Json.toJson(critical)

        case High => Json.toJson(high)

        case Normal => Json.toJson(normal)

        case Low => Json.toJson(low)


      }

    }
  }
}

