package test_models.tasks

import org.joda.time.DateTime
import play.api.libs.json.{Json, OFormat, Reads, Writes}
import play.api.libs.json.JodaWrites._
import play.api.libs.json.JodaReads._

case class TestTaskCount(
                          today: TestSubCount,
                          upcoming: TestSubCount,
                          due: TestSubCount,
                          completed: TestSubCount,
                          skipped: TestSubCount,
                          snoozed: TestSubCount,
                          failed: TestSubCount
                        )

case class TestSubCount(
                         all: Int,
                         email: Int,
                         linkedin: Int,
                         sms: Int,
                         whatsapp: Int,
                         call: Int,
                         generic: Int
                       )

object TestSubCount {
  given format: OFormat[TestSubCount] = Json.format[TestSubCount]
}

object TestTaskCount {
  given format: OFormat[TestTaskCount] = Json.format[TestTaskCount]
}

case class TestNavigationLinks(
                                prev: Option[String],
                                next: Option[String]
                              )

object TestNavigationLinks {
  given format: OFormat[TestNavigationLinks] = Json.format[TestNavigationLinks]
}

case class TestTaskPagination(
                               tasks: List[TestTask],
                               links: TestNavigationLinks
                             )
object TestTaskPagination {
  given format: OFormat[TestTaskPagination] = Json.format[TestTaskPagination]
}

case class TestTask( // Todo There is an extra due_at at in case class we can think about it
                     task_id: String,
                     is_auto_task: Boolean,
                     task_type: TestTaskType,
                     added_by: Long,
                     task_data: TestTaskData,
                     status: TestTaskStatus,
                     assignee: Option[TestAssignee],
                     team_id: Long,
                     prospect: Option[TestTaskProspect],
                     priority: TestTaskPriority,
                     note: Option[String],
                     created_at: DateTime,
                     updated_at: Option[DateTime],
                     due_at: DateTime,
                     campaign_id: Option[Long],
                     campaign_name: Option[String],
                     step_id: Option[Long],
                     step_label: Option[String],
                     is_opening_step: Option[Boolean],
                     created_via: TestTaskCreatedVia,
                   )


//object TestTask {
//  given format: OFormat[TestTask] = Json.format[TestTask]
//}

object TestTask {
  implicit val reads: Reads[TestTask] = Json.reads[TestTask]
  implicit val writes: Writes[TestTask] = Json.writes[TestTask]
}

case class TestAssignee(
                         id: Long,
                         name: String
                       )
object TestAssignee{
  given format: OFormat[TestAssignee] = Json.format[TestAssignee]

}

case class TestTaskProspect(
                             id: Long,
                             name: String,
                             email: String
                           )

object TestTaskProspect{
  given format: OFormat[TestTaskProspect] = Json.format[TestTaskProspect]

}