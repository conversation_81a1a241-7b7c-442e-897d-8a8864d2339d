package test_models.tasks

import play.api.libs.json.{<PERSON>s<PERSON><PERSON><PERSON>, <PERSON>sSuc<PERSON>, <PERSON>s<PERSON><PERSON><PERSON>, <PERSON>son, OFormat, Reads, Writes}

import scala.util.{Failure, Success, Try}


sealed trait TestTaskData {

  def task_type: TestTaskType

}

object TestTaskData{

  case class LinkedinConnectionRequestData(
                                            request_message: Option[String],
                                            task_type: TestTaskType = TestTaskType.SendLinkedinConnectionRequest
                                          ) extends TestTaskData

  object LinkedinConnectionRequestData {
    implicit def format: OFormat[LinkedinConnectionRequestData] = Json.format[LinkedinConnectionRequestData]
  }

  case class SendLinkedinMessageData(
                                      body: String,
                                      task_type: TestTaskType = TestTaskType.SendLinkedinMessage
                                    ) extends TestTaskData

  object SendLinkedinMessageData {
    implicit def format: OFormat[SendLinkedinMessageData] = Json.format[SendLinkedinMessageData]
  }

  case class SendEmailData(
                            subject: String,
                            body: String,
                            task_type: TestTaskType = TestTaskType.SendEmail
                          ) extends TestTaskData

  object SendEmailData {

    implicit def format: OFormat[SendEmailData] = Json.format[SendEmailData]
  }

  case class GeneralTestTaskData(
                                  task_notes: String,
                                  task_type: TestTaskType = TestTaskType.GeneralTask
                                ) extends TestTaskData

  object GeneralTestTaskData {

    implicit def format: OFormat[GeneralTestTaskData] = Json.format[GeneralTestTaskData]
  }

  case class SendSmsData(
                          body: String,
                          task_type: TestTaskType = TestTaskType.SendSms
                        ) extends TestTaskData

  object SendSmsData {
    implicit def format: OFormat[SendSmsData] = Json.format[SendSmsData]
  }

  case class  CallTestTaskData(
                                body: String,
                                task_type: TestTaskType = TestTaskType.CallTask
                              ) extends TestTaskData

  object  CallTestTaskData {
    implicit def format: OFormat[CallTestTaskData] = Json.format[CallTestTaskData]
  }

  case class SendWhatsAppMessageData(
                                      body: String,
                                      task_type: TestTaskType = TestTaskType.SendWhatsAppMessage
                                    ) extends TestTaskData

  object SendWhatsAppMessageData {
    implicit def format: OFormat[SendWhatsAppMessageData] = Json.format[SendWhatsAppMessageData]
  }

  case class SendLinkedinInMailData(
                                     subject: Option[String],
                                     body: String,
                                     task_type: TestTaskType = TestTaskType.SendLinkedinInMail
                                   ) extends TestTaskData

  object SendLinkedinInMailData {
    implicit def format: OFormat[SendLinkedinInMailData] = Json.format[SendLinkedinInMailData]
  }

  case class ViewLinkedinProfileData(
                                      task_type: TestTaskType = TestTaskType.ViewLinkedinProfile
                                    ) extends TestTaskData

  object ViewLinkedinProfileData {
    implicit def format: OFormat[ViewLinkedinProfileData] = Json.format[ViewLinkedinProfileData]
  }

  case class AutoLinkedinInmail(
                                 task_type: TestTaskType = TestTaskType.AutoLinkedinInmail,
                                 subject: String,
                                 body: String
                               ) extends TestTaskData

  object AutoLinkedinInmail {
    implicit def format: OFormat[AutoLinkedinInmail] = Json.format[AutoLinkedinInmail]
  }

  case class AutoLinkedinMessage(
                                  task_type: TestTaskType = TestTaskType.AutoLinkedinMessage,
                                  body: String
                                ) extends TestTaskData

  object AutoLinkedinMessage {
    implicit def format: OFormat[AutoLinkedinMessage] = Json.format[AutoLinkedinMessage]
  }

  case class AutoLinkedinConnectionRequest(
                                            task_type: TestTaskType = TestTaskType.AutoLinkedinConnectionRequest,
                                            body: Option[String]
                                          ) extends TestTaskData

  object AutoLinkedinConnectionRequest {
    implicit def format: OFormat[AutoLinkedinConnectionRequest] = Json.format[AutoLinkedinConnectionRequest]
  }

  case class AutoViewLinkedinProfile(
                                      task_type: TestTaskType = TestTaskType.AutoViewLinkedinProfile
                                    ) extends TestTaskData

  object AutoViewLinkedinProfile {
    implicit def format: OFormat[AutoViewLinkedinProfile] = Json.format[AutoViewLinkedinProfile]
  }

  implicit def writes: Writes[TestTaskData] = new Writes[TestTaskData] {
    def writes(ev: TestTaskData): JsValue = {

      ev match {
        case data: CallTestTaskData => Json.toJson(data)

        case data: LinkedinConnectionRequestData => Json.toJson(data)

        case data: SendEmailData => Json.toJson(data)

        case data: GeneralTestTaskData => Json.toJson(data)

        case data: SendLinkedinMessageData => Json.toJson(data)

        case data: SendSmsData => Json.toJson(data)

        case data: SendWhatsAppMessageData => Json.toJson(data)

        case data: SendLinkedinInMailData => Json.toJson(data)

        case data: ViewLinkedinProfileData => Json.toJson(data)

        case data: AutoViewLinkedinProfile => Json.toJson(data)

        case data: AutoLinkedinInmail => Json.toJson(data)

        case data: AutoLinkedinMessage => Json.toJson(data)

        case data: AutoLinkedinConnectionRequest => Json.toJson(data)

      }

    }
  }

  implicit def reads: Reads[TestTaskData] = (ev: JsValue) => Try {

    //FIXME : MATCH ON TestTaskType to make it exhaustive -> here, matching is being done on a string, so it is not exhaustive (even if cases do not include some objects of TestTaskType, the code will compile). We should match on the case class to make it exhaustive.
    (ev \ "task_type").as[String] match {

      case TestTaskType.sendEmail =>

        JsSuccess(
          SendEmailData(
            subject = (ev \ "subject").as[String],
            body = (ev \ "body").as[String]
          )
        )

      case TestTaskType.sendLinkedinConnectionRequest =>

        JsSuccess(
          LinkedinConnectionRequestData (
            request_message = (ev \ "request_message").asOpt[String],
          )
        )

      case TestTaskType.`generalTask` =>

        JsSuccess(
          GeneralTestTaskData(
            task_notes = (ev \ "task_notes").as[String]
          )
        )

      case TestTaskType.sendLinkedinMessage =>

        JsSuccess(
          SendLinkedinMessageData(
            body = (ev \ "body").as[String]
          )
        )

      case TestTaskType.sendSms =>

        JsSuccess(
          SendSmsData(
            body = (ev \ "body").as[String]
          )
        )

      case TestTaskType.callTask =>

        JsSuccess(
          CallTestTaskData(
            body = (ev \ "body").as[String]
          )
        )

      case TestTaskType.sendWhatsappMessage =>
        JsSuccess(
          SendWhatsAppMessageData(
            body = (ev \ "body").as[String]
          )
        )

      case TestTaskType.sendLinkedinInMail =>
        JsSuccess(
          SendLinkedinInMailData(
            subject = (ev \ "subject").asOpt[String],
            body = (ev \ "body").as[String]
          )
        )

      case TestTaskType.viewLinkedinProfile =>
        JsSuccess(
          ViewLinkedinProfileData()
        )

      case TestTaskType.autoViewLinkedinProfile =>
        JsSuccess(
          AutoViewLinkedinProfile()
        )

      case TestTaskType.autoLinkedinConnectionRequest =>
        JsSuccess(
          AutoLinkedinConnectionRequest(
            body = (ev \ "body").asOpt[String]
          )
        )

      case TestTaskType.autoLinkedinInmail =>
        JsSuccess(
          AutoLinkedinInmail(
            subject = (ev \ "subject").as[String],
            body = (ev \ "body").as[String]
          )
        )

      case TestTaskType.autoLinkedinMessage =>
        JsSuccess(
          AutoLinkedinMessage(
            body = (ev \ "body").as[String]
          )
        )

    }

  } match {
    case Failure(e) => JsError(e.toString)
    case Success(v) => v
  }

}
