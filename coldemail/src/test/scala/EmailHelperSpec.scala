import api.accounts.TeamId
import api.accounts.models.OrgId
import api.calendar_app.CalendarUserId
import api.calendar_app.models.CalendarAccountData
import api.campaigns.services.CampaignId
import api.prospects.models.ProspectId
import org.scalamock.scalatest.MockFactory
import org.scalatest.funspec.AnyFunSpec
import org.scalatest.matchers.should.Matchers
import utils.SRLogger
import utils.email.EmailHelper

import java.net.{URI, URL}

/*

import org.joda.time.{DateTime, DateTimeZone}
import org.joda.time.format.{DateTimeFormat, DateTimeFormatter}
import org.scalatestplus.play.PlaySpec
//import utils.email.{EmailHelper, EmailService, PreviousFollowUp}

import scala.util.{Failure, Success}

class EmailHelperSpec extends PlaySpec {
  "EmailHelper" should {

    "_makeOptoutHtml" should {

      "successfully create link" in {

        EmailHelper._makeOptoutHtmlV2("If you dont want to hear from me, {{then dont}}.",
          false,
          "smartreach.io",
          "abcde"
        ) match {

          case Failure(e) => "should not happen"
          case Success(str) => str mustBe "<br/>If you dont want to hear from me, <a href=\"smartreach.io/tv2/unsubscribe?code=abcde\" target='_blank'>then dont</a>."
        }

      }


      "fail if incorrect braces" in {

        EmailHelper._makeOptoutHtmlV2("If you dont want to hear from me, {{then dont}.",
          false,
          "smartreach.io",
          "abcde"
        ) match {
          case Success(_) => "should not happen"

          case Failure(e) => e.getMessage mustBe "Invalid opt out link"
        }

      }

      "fail if closing braces before opening braces" in {

        EmailHelper._makeOptoutHtmlV2("If you dont want to hear from me, }}then dont{{.",
          false,
          "smartreach.io",
          "abcde"
        ) match {
          case Success(_) => "should not happen"

          case Failure(e) => e.getMessage mustBe "Invalid opt out link"
        }

      }


    }


    "__makeAppendFollowupHtmlV1" should {

      "successfully create previous followup html" in {

        val exampleDate = DateTime.now()
        val exampleTZ = "Asia/Kolkata"

        val str = EmailService.__makeAppendFollowupHtmlV1(
          isManualEmail = false,
          appendFollowups = true,
          previousEmails = Seq(
            PreviousFollowUp(
              from_name = "P B",
              from_email = "<EMAIL>",
              sent_at = exampleDate,
              timezone = exampleTZ,
              baseBody = "<p>Hello, this is followup 1</p>"
            ),

            PreviousFollowUp(
              from_name = "P B",
              from_email = "<EMAIL>",
              sent_at = exampleDate,
              timezone = exampleTZ,
              baseBody = "<p>Hello, this is followup 2</p>"
            )
          )
        )

        val dtf: DateTimeFormatter = DateTimeFormat.forPattern("EEE, dd MMM YYYY")
        // val htf: DateTimeFormatter = DateTimeFormat.forPattern("HH:mm a")

        val tz = DateTimeZone.forID(exampleTZ)

        val dateStr = dtf.print(exampleDate.withZone(tz))

        str mustBe
          s"""<div class="prev-reply">
<div class='time-details'>On $dateStr, P B &lt;<EMAIL>&gt; wrote:</div>
<div class='reply'>
<p>Hello, this is followup 2</p>
<div class="prev-reply">
<div class='time-details'>On $dateStr, P B &lt;<EMAIL>&gt; wrote:</div>
<div class='reply'>
<p>Hello, this is followup 1</p>
<br /><br /><br />
</div>
</div>
</div>
</div>"""

      }
    }


    "__makeHTMLBodyV1" should {

      "successfully create body html" in {

        val prevEmailHtml =
          s"""<div class="prev-reply">
<div class='time-details'>On DATE, P B &lt;<EMAIL>&gt; wrote:</div>
<div class='reply'>
<p>Hello, this is followup 1</p>
<div class="prev-reply">
<div class='time-details'>On DATE, P B &lt;<EMAIL>&gt; wrote:</div>
<div class='reply'>
<p>Hello, this is followup 1</p>
<br /><br /><br />
</div>
</div>
</div>
</div>"""

        val str = EmailService.__makeHTMLBodyV1(
          baseBody = """<p>this is the basebody</p>""",
          prevEmailHtml = prevEmailHtml,
          openTrackingImgHtml = """<img src="otimage"></img>"""
        )


        str mustBe
          """<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
       <html xmlns="http://www.w3.org/1999/xhtml">
       <head>
       <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
       <title></title>
       <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
       <style type="text/css">
        p { margin: 0; font-size: '14px' }

        .prev-reply {
          margin-left: 1em;
          color: #500050;
          padding-top: 20px;
        }

        .time-details {}

        .reply {
          border-left: 1px solid black;
          padding-left: 1em;
        }
       </style>
       </head>
       <body><p>this is the basebody</p><div class="prev-reply">
<div class='time-details'>On DATE, P B &lt;<EMAIL>&gt; wrote:</div>
<div class='reply'>
<p>Hello, this is followup 1</p>
<div class="prev-reply">
<div class='time-details'>On DATE, P B &lt;<EMAIL>&gt; wrote:</div>
<div class='reply'>
<p>Hello, this is followup 1</p>
<br /><br /><br />
</div>
</div>
</div>
</div>
    <img src="otimage"></img>
       </body>
       </html>"""

      }
    }



    "__makeAppendFollowupHtmlV2" should {

      "successfully create previous followup html" in {

        val exampleDate = DateTime.now()
        val exampleTZ = "Asia/Kolkata"

        val str = EmailService.__makeAppendFollowupHtmlV2(
          isManualEmail = false,
          appendFollowups = true,
          previousEmails = Seq(
            PreviousFollowUp(
              from_name = "P B",
              from_email = "<EMAIL>",
              sent_at = exampleDate,
              timezone = exampleTZ,
              baseBody = "<p>Hello, this is followup 1</p>"
            ),

            PreviousFollowUp(
              from_name = "P B",
              from_email = "<EMAIL>",
              sent_at = exampleDate,
              timezone = exampleTZ,
              baseBody = "<p>Hello, this is followup 2</p>"
            )
          )
        )

        val dtf: DateTimeFormatter = DateTimeFormat.forPattern("EEE, dd MMM YYYY")
        // val htf: DateTimeFormatter = DateTimeFormat.forPattern("HH:mm a")

        val tz = DateTimeZone.forID(exampleTZ)

        val dateStr = dtf.print(exampleDate.withZone(tz))

        str mustBe
          s"""<div style="margin-left: 1em; color: #500050; padding-top: 20px;">
<div>On $dateStr, P B &lt;<EMAIL>&gt; wrote:</div>
<div style="border-left: 1px solid black; padding-left: 1em;">
<p>Hello, this is followup 2</p>
<div style="margin-left: 1em; color: #500050; padding-top: 20px;">
<div>On $dateStr, P B &lt;<EMAIL>&gt; wrote:</div>
<div style="border-left: 1px solid black; padding-left: 1em;">
<p>Hello, this is followup 1</p>
<br><br><br>
</div>
</div>
</div>
</div>"""

      }
    }


    "__makeHTMLBodyV2" should {

      "successfully create body html" in {

        val prevEmailHtml =
          s"""<div style="margin-left: 1em; color: #500050; padding-top: 20px;">
<div>On DATE, P B &lt;<EMAIL>&gt; wrote:</div>
<div style="border-left: 1px solid black; padding-left: 1em;">
<p>Hello, this is followup 2</p>
<div style="margin-left: 1em; color: #500050; padding-top: 20px;">
<div>On DATE, P B &lt;<EMAIL>&gt; wrote:</div>
<div style="border-left: 1px solid black; padding-left: 1em;">
<p>Hello, this is followup 1</p>
<br><br><br>
</div>
</div>
</div>
</div>"""


        val str = EmailService.__makeHTMLBodyV2(
          baseBody = """<p>this is the basebody</p>""",
          prevEmailHtml = prevEmailHtml,
          openTrackingImgHtml = """<img src="otimage"></img>"""
        )


        str.split(" ").mkString.split("\n").mkString mustBe
          """<html>
 <head></head>
 <body>
  <p>this is the basebody</p>
  <div style="margin-left: 1em; color: #500050; padding-top: 20px;">
   <div>
    On DATE, P B &lt;<EMAIL>&gt; wrote:</div>
  <div style="border-left: 1px solid black; padding-left: 1em;">
  <p>Hello, this is followup 2</p>
<div style="margin-left: 1em; color: #500050; padding-top: 20px;">
<div>On DATE, P B &lt;<EMAIL>&gt; wrote:</div>
<div style="border-left: 1px solid black; padding-left: 1em;">
<p>Hello, this is followup 1</p>
<br><br><br>
</div>
</div>
</div>
</div>
    <img src="otimage">
       </body>
       </html>""".split(" ").mkString.split("\n").mkString

      }
    }


    "__getBodyContentFromHtml" should {

      "successfully get body content from html" in {

        val html =
          """<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
       <html xmlns="http://www.w3.org/1999/xhtml">
       <head>
       <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
       <title></title>
       <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
       <style type="text/css">
        p { margin: 0; font-size: '14px' }

        .prev-reply {
          margin-left: 1em;
          color: #500050;
          padding-top: 20px;
        }

        .time-details {}

        .reply {
          border-left: 1px solid black;
          padding-left: 1em;
        }
       </style>
       </head>
       <body><p>this is the basebody</p><div class="prev-reply">
<div class='time-details'>On DATE, P B &lt;<EMAIL>&gt; wrote:</div>
<div class='reply'>
<p>Hello, this is followup 1</p>
<div class="prev-reply">
<div class='time-details'>On DATE, P B &lt;<EMAIL>&gt; wrote:</div>
<div class='reply'>
<p>Hello, this is followup 1</p>
<br /><br /><br />
</div>
</div>
</div>
</div>
    <img src="otimage">
       </body>
       </html>"""

        val str = EmailService.__getBodyContentFromHtml(
          html = html
        )


        str.split(" ").mkString.split("\n").mkString mustBe
          """<p>this is the basebody</p>
<div class="prev-reply">
<div class="time-details">
 On DATE, P B &lt;<EMAIL>&gt; wrote:
 </div>
  <div class="reply">
    <p>Hello, this is followup 1</p>
    <div class="prev-reply">
      <div class="time-details">
      On DATE, P B &lt;<EMAIL>&gt; wrote:
      </div>
      <div class="reply">
        <p>Hello, this is followup 1</p>
        <br>
        <br>
        <br>
      </div>
    </div>
  </div>
</div>
  <img src="otimage">""".split(" ").mkString.split("\n").mkString


      }


      "successfully get body content from Broken html" in {

        val html =
          """<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
       <html xmlns="http://www.w3.org/1999/xhtml">
       <head>
       <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
       <title></title>
       <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
       <style type="text/css">
        p { margin: 0; font-size: '14px' }

        .prev-reply {
          margin-left: 1em;
          color: #500050;
          padding-top: 20px;
        }

        .time-details {}

        .reply {
          border-left: 1px solid black;
          padding-left: 1em;
        }
       </style>
       </head>
       <body><p>this is the basebody</p>"""

        val str = EmailService.__getBodyContentFromHtml(
          html = html
        )


        str.split(" ").mkString.split("\n").mkString mustBe
          """
<p>this is the basebody</p>""".split(" ").mkString.split("\n").mkString


      }
    }
  }
}
*/
class EmailHelperSpec extends AnyFunSpec with MockFactory with Matchers {

  // Cannot mock final method like shouldNeverHappen
  given logger: SRLogger = SRLogger("EmailHelperSpec")

  val orgId: OrgId = OrgId(59L)

  describe("isSRTrackingOrUnsubscribeLink"){
    it("should take a mail input in href and not fail") {
      val res = EmailHelper.isSRTrackingOrUnsubscribeLink(href = "mailto:<EMAIL>",allTrackingDomainsUsed = Seq("example.smartreach.io"), orgId = orgId)

      res.isFailure should equal(false)
    }
    it("should take a emailto instead of mail to and log a warning"){

      val res = EmailHelper.isSRTrackingOrUnsubscribeLink(href = "emailto:<EMAIL>", allTrackingDomainsUsed = Seq("example.smartreach.io"), orgId = orgId)
      res.isFailure should equal(true)
    }

    it("should take a tel input in href and not log error") {

      val res = EmailHelper.isSRTrackingOrUnsubscribeLink(href = "tel:+91**********", allTrackingDomainsUsed = Seq("example.smartreach.io"), orgId = orgId)
      res.isFailure should equal(true)
    }
    it("should correctly take url input and don't throw error ") {

      val res = EmailHelper.isSRTrackingOrUnsubscribeLink(href = "https://smartreach.io/",allTrackingDomainsUsed = Seq("example.smartreach.io"), orgId = orgId)
      res.isFailure should equal(false)
    }
    it("should take a url with unknown protocol and log a warning"){

      val res = EmailHelper.isSRTrackingOrUnsubscribeLink(href = "UnknownProtocol://smartreach.io/?", allTrackingDomainsUsed = Seq("example.smartreach.io"), orgId = orgId)
      res.isFailure should equal(true)
    }
    it("should take a url without http and log an error") {

      val res = EmailHelper.isSRTrackingOrUnsubscribeLink(href = "smartreach.io/?", allTrackingDomainsUsed = Seq("example.smartreach.io"), orgId = orgId)
      res.isFailure should equal(true)
    }
    it("new URL() should be equal to new URI.toURL()") {
      val url = "https://smartreach.io"
      val old_method = new URL(url)
      val new_method = new URI(url).toURL
      assert(old_method.getHost == new_method.getHost)

      val url2 = "https://smartreach.io/blog/category/sales_development_leader/"
      val old_method2 = new URL(url2)
      val new_method2 = new URI(url2).toURL
      assert(old_method2.getHost == new_method2.getHost)


      val url3 = "https://smartreach.io/blog/category/sales_development_leader?hello=world"
      val old_method3 = new URL(url3)
      val new_method3 = new URI(url3).toURL
      assert(old_method3.getHost == new_method3.getHost)
    }


  }

  describe("makeCalendarLinkUrl"){
    val calendar_user_name = "shubham-kudekar-1"

    val calendarAccountData =Some(CalendarAccountData(
      calendar_user_id =CalendarUserId(id=1),
      calendar_username_slug = calendar_user_name
    ))

    it("Should return failure as calendar_username_slug is not defined"){
      val result = EmailHelper._makeCalendarLinkUrl(
        campaignId = CampaignId(id = 1L),
        prospectId = ProspectId(id = 1L),
        teamId = TeamId(id = 1L),
        calendarAccountData = None ,
        stepId = 1L,
        sender_name = "Shubham Kudekar",
        selectedCalendarData = None
      )

      assert(result.isFailure)
    }


    //Fixme the logic will change for this function so should the test case

//    it("should generate individual calendar link  "){
//      val result = EmailHelper._makeCalendarLinkUrl(
//        campaignId = CampaignId(id = 1L),
//        prospectId = ProspectId(id = 1L),
//        teamId = TeamId(id = 1L),
//        calendarAccountData = calendarAccountData,
//        stepId = 1L,
//        sender_name = "Shubham Kudekar"
//      )
//      assert(result.isSuccess )
//      assert(result.get.contains(calendar_user_name))
//    }
//
//    it("should return failure as calendar_team is None"){
//      val result = EmailHelper._makeCalendarLinkUrl(
//        campaignId = CampaignId(id = 1L),
//        prospectId = ProspectId(id = 1L),
//        teamId = TeamId(id = 1L),
//        calendarAccountData = calendarAccountData.copy(is_individual_calendar = Some(false)),
//        stepId = 1L,
//        sender_name = "Shubham Kudekar"
//      )
//      assert(result.isFailure)
//    }

    
  }

}
