package app.test_fixtures.search

import api.accounts.models.{AccountProfileInfo, SignupType}
import api.accounts.{Account, AccountAccess, AccountErrorObjectApi, AccountMetadata, AccountType, AccountUuid, AccountWarningCodeType, AccountWarningObjectApi, OrgCountData, OrgMetadata, OrgPlan, OrgSettings, OrganizationRole, OrganizationWithCurrentData}
import api.billing.PaymentGateway
import api.calendar_app.CalendarUserId
import api.calendar_app.models.CalendarAccountData
import api.columns.FieldTypeEnum
import api.search.{SearchQuery, SearchQueryColumnOperator, SearchQueryMainClause, SearchQueryOperators, SearchQuerySort, SearchQuerySubClause, SearchQuerySubFilterData}
import app.test_fixtures.accounts.OrgCountDataFixture
import app.test_fixtures.organizationa.{OrgMetadataFixture, OrgPlanFixture}
import org.joda.time.DateTime
import play.api.libs.json.Json
import utils.SRLogger

object ProspectAccountQueryFixtures {
  val searchQueryColumnOperator: SearchQueryColumnOperator = SearchQueryColumnOperator(
    display_name = "DisplayName1",
    key = SearchQueryOperators.CONTAINS
  )

  val searchQuerySubFilterData: SearchQuerySubFilterData = SearchQuerySubFilterData(
    field = "field",
    field_display_name = Option("field_name"),
    operator = SearchQueryOperators.CONTAINS,
    value = "value",
    value_display_name = Option("value_name"),
    field_type = FieldTypeEnum.NUMBER,
    is_custom = true,
    allowedFilterOperators = Option(Seq(searchQueryColumnOperator))
  )

  val searchQuerySubClause: SearchQuerySubClause = SearchQuerySubClause(
    clause = "AND",
    filters = Seq()
  )

  val searchQueryMainClause: SearchQueryMainClause = SearchQueryMainClause(
    search = Option("search"),
    owner_ids = Seq(2),
    clause = "AND",
    filters = Seq(searchQuerySubClause)
  )

  val searchQuerySort_getQuerySQL: SearchQuerySort = SearchQuerySort(
    field = "abc",
    direction = Option("ASC"),
    is_custom = true,
    is_numeric = false
  )

  val searchQuery: SearchQuery = SearchQuery(
    query = Option(searchQueryMainClause),
    page = Option(1),
    custom_flags = Option(Json.obj()),
    sort = Option(searchQuerySort_getQuerySQL),
    is_campaign = Option(3),
    older_than = None,
    newer_than = None
  )
  val dateTime : DateTime = DateTime.now()

  val accountProfileInfo : AccountProfileInfo = AccountProfileInfo(
    first_name = "first_name",
    last_name = "last_name",
    company = Option("company"),
    timezone = Option("timezone"),
    country_code = Option("country_code"),
    mobile_country_code = Option("56623"),
    mobile_number = Option(*********),
    onboarding_phone_number = Option("*********"),
    twofa_enabled = true,
    has_gauthenticator = false,
    scheduled_for_deletion_at = Option(dateTime),
    weekly_report_emails = Option("<EMAIL>")
  )

  val orgSettings : OrgSettings = OrgSettings(
    enable_ab_testing = false,
    disable_force_send = true,
    bulk_sender = false,
    allow_2fa = true,
    show_2fa_setting = true,
    enforce_2fa = false,
    allow_native_crm_integration = true,
      agency_option_allow_changing = false,
      agency_option_show = false
  )

  val accountErrorObjectApi : AccountErrorObjectApi = AccountErrorObjectApi(
    error_msg = "error_msg",
    error_code = "error_code",
    error_at = Option(dateTime),
    upgrade_now_prompt = true
  )


  val accountWarningObjectApi : AccountWarningObjectApi = AccountWarningObjectApi(
    warning_msg = "warning_msg",
    warning_code = AccountWarningCodeType.ProspectLimit80Percent,
    warning_at = dateTime,
    upgrade_now_prompt = true,
    add_call_credit_button = true,
    new_prospects_paused_till = Option(dateTime)
  )

  val organizationWithCurrentData : OrganizationWithCurrentData = OrganizationWithCurrentData(
    id = 12345,
    name = "name",
    owner_account_id = 12,
    counts = OrgCountDataFixture.orgCountData_default,
    settings = orgSettings,
    plan = OrgPlanFixture.orgPlanFixture,
    is_agency = true,
    trial_ends_at = dateTime,
    error = Option("error"),
    error_code = Option("400"),
    paused_till = Option(dateTime),
    errors = Seq(accountErrorObjectApi),
    warnings = Seq(accountWarningObjectApi),
    via_referral = true,
    org_metadata = OrgMetadataFixture.orgMetadata_default
  )

  val account_metadata: AccountMetadata = AccountMetadata(
    is_profile_onboarding_done = Option(true)
  )

  val account_access: AccountAccess = AccountAccess(
    inbox_access = true
  )

  val calendar_account_data : CalendarAccountData = CalendarAccountData(
    calendar_user_id = CalendarUserId(1),
    calendar_username_slug = "user_name"
  )
  val account: Account = Account(
    id = AccountUuid("acc_123"),
    internal_id = 4,
    email = "<EMAIL>",
    email_verification_code = Option("1234"),
    email_verification_code_created_at = Option(dateTime),
    created_at = dateTime,
    first_name = Option("first_name"),
    last_name = Option("last_name"),
    company = Option("company"),
    timezone = Option("timezone"),
    profile = accountProfileInfo,
    org_role = Option(OrganizationRole.AGENCY_ADMIN),
    teams = Seq(),
    account_type = AccountType.AGENCY,
    org = organizationWithCurrentData,
    active = true,
    email_notification_summary = "hi done",
    account_metadata = account_metadata,
    email_verified = true,
    signupType = Option(SignupType.Microsoft),
    account_access = account_access,
    calendar_account_data = Option(calendar_account_data)
  )
}
