package app.test_fixtures.prospect

import api.accounts.AccountUuid
import api.tags.models.{ProspectTag, ProspectTagUuid}
import eventframework.{ProspectObject, ProspectObjectInternal}
import org.joda.time.DateTime
import play.api.libs.json.Json


case class ProspectForScheduling(
  prospect: ProspectObject,
  last_scheduled: Option[DateTime],
  current_step_id: Option[Long],
  email_checked: <PERSON><PERSON><PERSON>,
  email_sent_for_validation: <PERSON><PERSON><PERSON>,
  email_sent_for_validation_at: Option[DateTime]
)

object ProspectFixtures {

  private val dateTime_now = DateTime.now()
  private val owner_name = "owner_name"
  private val owner_email = "owner_email"
  private val email_domain = "email_domain"
  private val prospect_category_id_custom_3 = 3
  private val prospect_category_label_color = "color"
  private val account_id_34 = Some(34L)
  private val total_open_3 = 3
  private val total_click_4 = 4
  private val flag_32 = Json.obj()
  private val prospect_id_1 = 1
  private val owner_id_35 = 35L
  private val team_id_8 = 8L
  private val first_name = Option("first_name")
  private val last_name = Option("last_name")
  private val email = "email"
  private val custom_fields = Json.obj()
  private val list = Option("list")
  private val job_title = Option("job_title")
  private val company = Option("company")
  private val linkedin_url = Option("linkedin_url")
  private val phone = Option("phone")
  private val city = Option("city")
  private val state = Option("state")
  private val country = Option("country")
  private val timezone = Option("time_zone")
  private val prospect_category = "prospect_category"
  private val last_contacted_at = Option(dateTime_now)
  private val created_at = dateTime_now
  private val owner_uuid = AccountUuid("owner_uuid")
  private val updated_at = dateTime_now
  private val last_contacted_at_phone = Some("+************")


  val prospectObjectInternal = ProspectObjectInternal(
    owner_name = owner_name,
    owner_email = owner_email,
    email_domain = Some(email_domain),
    invalid_email = None,
    last_contacted_at = None,
    last_replied_at = None,
    last_opened_at = None,
      last_call_made_at = None,
    list_id = None,
    prospect_category_id_custom = prospect_category_id_custom_3,
    prospect_category_label_color = prospect_category_label_color,
    prospect_source = None,
    prospect_account_id = account_id_34,
    prospect_account = None,
    total_opens = total_open_3,
    total_clicks = total_click_4,
    active_campaigns = None,
    current_campaign_id = None,
    magic_columns = List(),
    tags = Some(
      List(
        ProspectTag(tag_id = 123L, tag = "tag1", tag_uuid = ProspectTagUuid("tags_abcdefg")), ProspectTag(tag_id = 1234L, tag = "tag2", tag_uuid = ProspectTagUuid("tags_qwerty"))
      )
    ),
    flags = flag_32,
    latest_reply_sentiment = None,
    prospect_account_uuid = None
  )
  val  prospectObject = ProspectObject(
  id = prospect_id_1,
  owner_id = owner_id_35,
  team_id = team_id_8,
  first_name = first_name,
  last_name = last_name,
  email = Some(email),
  custom_fields = custom_fields,
  list = list,
  job_title = job_title,
  company = company,
  linkedin_url = linkedin_url,
  phone = phone,
  phone_2 = None,
  phone_3 = None,
  city = city,
  state = state,
  country = country,
  timezone = timezone,
  prospect_category = prospect_category,
  last_contacted_at = last_contacted_at,
  last_contacted_at_phone = last_contacted_at_phone,
  created_at = created_at,
  internal = prospectObjectInternal,
  latest_reply_sentiment_uuid = None,
  current_step_type = None,
  latest_task_done_at = None,
  prospect_uuid = None,
  owner_uuid = owner_uuid,
  updated_at = updated_at
  )

  val prospectObject_1: List[ProspectObject] = List(
    prospectObject
  )


  val dummyProspectForScheduling: ProspectForScheduling = ProspectForScheduling(
    prospect = prospectObject,
    last_scheduled = None,
    current_step_id = None,
    email_checked = true,
    email_sent_for_validation = true,
    email_sent_for_validation_at = None
  )
}
