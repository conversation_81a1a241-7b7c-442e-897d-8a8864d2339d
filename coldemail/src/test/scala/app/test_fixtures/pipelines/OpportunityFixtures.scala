package app.test_fixtures.pipelines

import api.accounts.AccountUuid
import api.campaigns.services.CampaignId
import api.pipelines.dao.OpportunityData
import api.pipelines.models.{OpportunityStatusUUID, OpportunityType, PipelineUUID}
import api.prospects.models.ProspectId
import api_layer_models.CurrencyType
import org.joda.time.DateTime

object OpportunityFixtures {

  private val prospectId_23 = ProspectId(id = 23)

  private val opportunityValue_311 = 311

  private val opportunityStatusId_status_uuid_234 = OpportunityStatusUUID(uuid = "status-uuid-234")

  private val pipelineId_pipeline_uuid_513 = PipelineUUID(uuid = "pipeline-uuid-513")

  private val confidence_37 = 37

  private val campaignId_643 = CampaignId(id = 643)

  private val ownerId_acc_uuid_23 = AccountUuid(uuid = "acc-uuid-23")

  private val estimateClosingDate_now_plus_3_days = DateTime.now().plusDays(3)

  val opportunityData_default: OpportunityData = OpportunityData(
    prospect_id = prospectId_23,
    value = opportunityValue_311,
    currency = CurrencyType.USD,
    opportunity_type = OpportunityType.AnnualRecurring,
    opportunity_status_id = opportunityStatusId_status_uuid_234,
    pipeline_id = pipelineId_pipeline_uuid_513,
    confidence = confidence_37,
    campaign_id = Some(campaignId_643),
    notes = None,
    owner_id = ownerId_acc_uuid_23,
    estimate_closing_date = Some(estimateClosingDate_now_plus_3_days)
  )

}
