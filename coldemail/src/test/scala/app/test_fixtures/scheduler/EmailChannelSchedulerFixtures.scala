package app.test_fixtures.scheduler

import api.accounts.email.models.EmailServiceProvider
import api.accounts.models.{AccountProfileInfo, OrgId, ProspectAccountUuid}
import api.accounts.{Account, AccountAccess, AccountMetadata, AccountType, AccountUuid, OrgCountData, OrgMetadata, OrgPlan, OrgSettings, OrganizationRole, OrganizationWithCurrentData, TeamId}
import api.calendar_app.CalendarUserId
import api.calendar_app.models.CalendarAccountData
import api.campaigns.{CampaignProspectUpdateScheduleStatus, CampaignStepVariantForScheduling, CampaignStepWithChildren}
import api.campaigns.models.{CampaignEmailSettingsId, CampaignStepType, CampaignType, CampaignTypeData, CurrentStepStatusForScheduler, CurrentStepStatusForSchedulerData}
import api.campaigns.models.CampaignStepData.{AutoEmailStep, LinkedinConnectionRequestData, LinkedinMessageData, ManualEmailStep}
import api.columns.InternalMergeTagValuesForProspect
import api.emails.{EmailScheduledNewAfterSaving, EmailScheduledNewStep2, EmailsScheduledUuid}
import api.prospects.{ProspectAccount, ProspectUuid}
import api.tasks.models.TaskData.SendEmailData
import api.tasks.models.{NewTask, TaskCreatedVia, TaskPriority, TaskStatus, TaskStatusType, TaskType}
import app.test_fixtures.accounts.OrgCountDataFixture
import app.test_fixtures.organizationa.{OrgMetadataFixture, OrgPlanFixture}
import app.test_fixtures.prospect.ProspectFixtures
import eventframework.{ProspectObject, ProspectObjectInternal}
import org.joda.time.DateTime
import play.api.libs.json.Json
import sr_scheduler.CampaignStatus
import sr_scheduler.models.CampaignForScheduling.CampaignEmailSettingForScheduler
import sr_scheduler.models.{CampaignEmailPriority, CampaignForScheduling, ChannelDataForScheduling, ChannelType, EmailScheduledNew3, EmailSettingCreateEmailSchedule}
import utils.StringUtils
import utils.email.{EmailOptionsForGetBodies, EmailServiceBody}
import utils.mq.channel_scheduler.SchedulerMapStepIdAndDelay
import utils.mq.channel_scheduler.channels.ChannelSchedulerTrait
import utils.uuid.SrUuidUtils

import java.util.UUID

object EmailChannelSchedulerFixtures {
  private val srUuidUtils = new SrUuidUtils()

  val campaign_id: Long = 3

  val campaign_owner_id = 21

  val team_id: Long = 15
  val orgId = 11

  val account_time_zone = "Asia/Kolkata"

  val channel_owner_account_id = 17

  val time = DateTime.parse("2024-04-09T15:42:17.398+05:30")

  val email_settings_data = EmailSettingCreateEmailSchedule(
    id = 23,
    team_id = team_id,
    org_id = OrgId(orgId),
    account_id = channel_owner_account_id,
    email = "<EMAIL>",
    sender_name = "Shashank Dwivedi",
    first_name = "Shashank",
    last_name = "Dwivedi",

    quota_per_day = 10,
    donot_enforce_24_hour_limit_till = None,

    min_delay_seconds = 84000,
    max_delay_seconds = 84000,

    latest_email_scheduled_at = None,
    default_tracking_domain = "www.tracking-domain.com",
    default_unsubscribe_domain = "www.unsubscribe-domain.com",
    rep_tracking_host_id = 10,
    custom_tracking_domain = None,
    signature = None,

    account_timezone = account_time_zone,
    bulk_sender = false
  )

  val email_channel_data_for_scheduling = ChannelDataForScheduling.EmailChannelDataForScheduling(

    emailSetting =  email_settings_data,
    channelTeamId = 15,
    account_timezone = account_time_zone,
    channelOwnerAccountId =  channel_owner_account_id,
    channelOrgId = OrgId(orgId)
  )

  val aDate = DateTime.parse("2022-10-28")

  val campaignId: Long = 3
  val campaignOwnerId = 2
  val oldFlowTeamId = 10
  val campaignName = "Test Email Campaign"
  val headStepId = 11
  val sendingHolidayCalendarId = 9L
  val senderEmailSettingId = 8
  val receiverEmailSettingsId = 8
  val appendFollowUps = false
  val openTrackingEnabled = false

  val prospectOwnerId = 1
  val prospectName = "Shashank Dwivedi"
  val channel_id_1: Long = 1
  val account_id_1: Long = 1
  val emailSettingId_1: Long = 3

  val emailServiceBody = EmailServiceBody(
    subject = "This is the Subject",
    textBody = "This is The Body",
    htmlBody = "This is The Body",
    baseBody = "This is The Body",
    isEditedPreviewEmail = false,
    has_unsubscribe_link = false
  )

  val profile: AccountProfileInfo = AccountProfileInfo(
    first_name = "Animesh",
    last_name = "Kumar",
    company = Some("AnimeshKumar"),
    timezone = Some("Asia/Kolkata"),
    country_code = Some("IN"),
    mobile_country_code = Some("+91"),
    mobile_number = Some(9515253545L),
    twofa_enabled = false,
    has_gauthenticator = false,
    weekly_report_emails = Some("<EMAIL>"),
    scheduled_for_deletion_at = None,
    onboarding_phone_number = Some("+************")
  )


  val orgCountData: OrgCountData = OrgCountDataFixture.orgCountData_default

  val orgPlan: OrgPlan = OrgPlanFixture.orgPlanFixture
  val orgSettings: OrgSettings = OrgSettings(
    enable_ab_testing = false,
    disable_force_send = false,
    bulk_sender = false,
    allow_2fa = false,
    show_2fa_setting = false,
    enforce_2fa = false,
    allow_native_crm_integration = false,
      agency_option_allow_changing = false,
      agency_option_show = false
  )

  val orgMetadata: OrgMetadata = OrgMetadataFixture.orgMetadataFixture2

  val org: OrganizationWithCurrentData = OrganizationWithCurrentData(

    id = 1,
    name = "AK",
    owner_account_id = account_id_1,

    counts = orgCountData,
    settings = orgSettings,
    plan = orgPlan,

    is_agency = true,
    trial_ends_at = DateTime.now().plusDays(100),
    error = None,
    error_code = None,
    paused_till = None,
    errors = Seq(),
    warnings = Seq(),
    via_referral = false,
    org_metadata = orgMetadata
  )


  val accountMetadata: AccountMetadata = AccountMetadata(
    // account_ui_version = None,
    is_profile_onboarding_done = None
  )

  val account: Account = Account(
    id = AccountUuid("account_uuid"),
    internal_id = 2,
    email = "<EMAIL>",
    email_verification_code = None,
    email_verification_code_created_at = None,
    created_at = DateTime.now().minusDays(1000),
    first_name = Some("Animesh"),
    last_name = Some("Kumar"),
    company = Some("AK"),
    timezone = None,
    profile = profile,
    org_role = Some(OrganizationRole.OWNER),
    teams = Seq(),
    account_type = AccountType.AGENCY,
    org = org,
    active = true,
    email_notification_summary = "dSFA",
    account_metadata = accountMetadata,
    email_verified = true,
    signupType = None,
    account_access = AccountAccess(
      inbox_access = false
    ),
    calendar_account_data = None

  )

  val campaignForSchedulingEmail = CampaignForScheduling.CampaignForSchedulingEmail(
    campaign_id = campaign_id,
    campaign_owner_id = campaignOwnerId,
    team_id = team_id,
    org_id = orgId,
    campaign_name = campaignName,
    status = CampaignStatus.RUNNING, //

    campaign_type_data = CampaignTypeData.MultiChannelCampaignData(head_step_id = headStepId),

    // settings

    ai_generation_context = None,

    sending_holiday_calendar_id = Some(sendingHolidayCalendarId),

    // in CampaignForScheduling, email_settings would be there because we ignore campaigns which do not have them
    campaign_email_setting = CampaignEmailSettingForScheduler(
      sender_email_settings_id = senderEmailSettingId,
      receiver_email_settings_id = receiverEmailSettingsId,
      campaign_email_settings_id = CampaignEmailSettingsId(123),
      emailServiceProvider = EmailServiceProvider.OTHER
    ),

    append_followups = appendFollowUps,
    open_tracking_enabled = false,
    click_tracking_enabled = false,
    opt_out_msg = "optMessage",
    opt_out_is_text = false,

    timezone = "UTC",
    daily_from_time = 0, // time since beginning of day in seconds
    daily_till_time = 855000, // time since beginning of day in seconds

    // Sunday is the first day
    days_preference = List(true, true, true, true, true, true, true),


    email_priority = CampaignEmailPriority.FIRST_EMAIL,

    max_emails_per_prospect_per_day = 1,
    max_emails_per_prospect_per_week = 3,

    max_emails_per_prospect_account_per_day = 100,
    max_emails_per_prospect_account_per_week = 300,

    campaign_max_emails_per_day = 100,

    // warm up
    softstart_setting = None, // making it none now

    mark_completed_after_days = 20,

    latest_email_scheduled_at = Some(aDate),


    from_email = "<EMAIL>",
    from_name = "Shashank Dwivedi",

    reply_to_email = "<EMAIL>",
    reply_to_name = "Shashank Dwivedi Reply Name",

    min_delay_seconds = 50,
    max_delay_seconds = 100,

    enable_email_validation = false, // keeping it false now

    rep_mail_server_id = 2,
    via_gmail_smtp = Some(false), // Option[DateTime]
    prospects_remaining_to_be_scheduled_exists = Some(true),
    count_of_sender_emails = 1,
    selected_calendar_data = None
  )

  val schedulerMapStepIdAndDelay = SchedulerMapStepIdAndDelay(
    is_head_step_in_the_campaign = true, // keeping it true
    currentStepType = CampaignStepType.AutoEmailStep,
    nextStepType = CampaignStepType.AutoEmailStep,
    currentStepId = 12,
    delayTillNextStep = 100
  )

  val timeZone = "Asia/Kolkata"
  val prospectId = 7

  val parsedJson = Json.parse(
    """
      {
        "name" : "Watership Down",
        "location" : {
          "lat" : 51.235685,
          "long" : -1.309197
        },
        "residents" : [ {
          "name" : "Fiver",
          "age" : 4,
          "role" : null
        }, {
          "name" : "Bigwig",
          "age" : 6,
          "role" : "Owsla"
        } ]
      }
      """)

  val prospectObjectInternal = ProspectFixtures.prospectObjectInternal.copy(
    prospect_account_id = None
  )

  val prospectObject = ProspectObject(
    id = prospectId,
    owner_id = prospectOwnerId,
    team_id = 505,

    first_name = Some("Shashank Prospect"),
    last_name = Some("dwivedi prospect"),

    email = Some("<EMAIL>"),

    custom_fields = parsedJson,

    list = None,

    job_title = None,
    company = None,
    linkedin_url = Some("https://linkedin.com/in/aditya-sadana"),
    phone = None,
    phone_2 = None,
    phone_3 = None,

    city = None,
    state = None,
    country = None,
    timezone = None,

    prospect_category = "categoryProspect", // display name

    last_contacted_at = None,
    last_contacted_at_phone = None,

    created_at = aDate,


    /* internal columns only for smartreach website, not for public api */
    internal = prospectObjectInternal,
    latest_reply_sentiment_uuid = None,
    current_step_type = None,
    latest_task_done_at = None,
    prospect_uuid = Some(ProspectUuid("prs_aa_abcdefghi")),
    owner_uuid = AccountUuid("acc_aa_abcdegfhi"),
    updated_at = aDate
  )

  val manual_email_step = ManualEmailStep(
    subject = "variant subject",
    body = "Variant body",
  )

  val step_data2 = LinkedinConnectionRequestData(
    body = Some("Linkedin Connection Request Body 2")
  )

  val step_data5 = LinkedinMessageData(
    body = "Linkedin Message Body 5"
  )


  val campaignStepVariantForScheduling_manual = CampaignStepVariantForScheduling(
    id = 1,
    step_id = 3,
    campaign_id = campaignId,
    template_id = None,
    step_data = manual_email_step,
    step_label = None,
    step_delay = 10,
    notes = Some("Test Notes"),
    priority = Some(TaskPriority.Normal),
    active = true,
    scheduled_count = 1
  )

  val internalMergeTagValuesForProspect = InternalMergeTagValuesForProspect(
    sender_name = "Monica Geller",
    sender_first_name = "Monica",
    sender_last_name = "Geller",
    unsubscribe_link = Some("dummy_link"),
    previous_subject = None,
    signature = None,
    sender_phone_number = None,
    calendar_link = None
  )

  val campaignStepWithChildren_manual_email_step = CampaignStepWithChildren(
    id = 3,
    label = None,
    campaign_id = campaignId,
    delay = 10,
    step_type = CampaignStepType.ManualEmailStep,
    created_at = DateTime.parse("2022-03-21T11:58:03.294Z"),
    children = List(2, 3, 4),
    variants = Seq(
      campaignStepVariantForScheduling_manual
    )
  )

  val calendar_user_id = CalendarUserId(
    id = 23
  )

  val calendar_user_name_slug = "calndar_userName_slug"

  val calendar_account_data = CalendarAccountData(
                                  calendar_user_id = calendar_user_id,
                                  calendar_username_slug = calendar_user_name_slug,
                                )

  val task_data_manual_task = NewTask(
    campaign_id = Some(campaignId),
    campaign_name = Some(campaignName),
    step_id = Some(3),
    step_label = None,
    created_via = TaskCreatedVia.Scheduler,
    is_opening_step = Some(false),
    task_type = TaskType.SendEmail,
    is_auto_task = false,
    task_data = SendEmailData(
      subject = "variant subject",
      body = "Variant body",
      email_message_id = None
    ),
    status = TaskStatus.Due(
      due_at = aDate.plusSeconds(60)
    ),
    assignee_id = Some(channel_owner_account_id),
    prospect_id = Some(7),
    priority = TaskPriority.Normal,
    emailsScheduledUuid = None,
    note = Some("Test Notes"),
  )


  val email_scheduled_new3 = EmailScheduledNew3(
    campaign_id = Some(3),
    step_id = Some(3),
    is_opening_step = false,
    prospect_id = Some(7), // in test emails, prospect_id is null
    prospect_account_id = None, // in test emails, prospect_account_id is null
    added_at = aDate,
    scheduled_at = aDate.plusSeconds(60),
    sender_email_settings_id = 8,
    template_id = None,
    variant_id = Some(1),
    rep_mail_server_id = 2,
    via_gmail_smtp = Some(false),
    step_type = CampaignStepType.ManualEmailStep,

    team_id = team_id,
    account_id = 2,
    rep_tracking_host_id = 10,

    to_email = "<EMAIL>",
    to_name = Some("Shashank Prospect dwivedi prospect"),

    from_email = "<EMAIL>",
    from_name = "Shashank Dwivedi",

    reply_to_email = None,
    reply_to_name = None,

    campaign_name = Some("Test Email Campaign"),
    step_name = None,
    receiver_email_settings_id = 8,

    scheduled_from_campaign = true,
    scheduled_manually = false,

    email_thread_id = None, // is present in case of manual reply from SmartReach inbox,

    // in some cases like manually sending emails, we immediately push the email to rabbitmq
    pushed_to_rabbitmq = false,


    // for manually sent emails, where we dont have step2: EmailScheduledNewStep2


    body = "this is test body",
    base_body = "this is test body",
    text_body = "this is test body",
    subject = "this is test subject",


    has_open_tracking = false,
    has_click_tracking = false,
    has_unsubscribe_link = false,

    list_unsubscribe_header = None,
    gmail_fbl = None,
    campaign_email_settings_id =  CampaignEmailSettingsId(
      123
    ),
    uuid = srUuidUtils.generateEmailsScheduledUuid(),
    is_edited_preview_email = false
  )

  val emailScheduledNewAfterSaving = EmailScheduledNewAfterSaving(
    email_scheduled_id = 3,
    emailsScheduledUuid = EmailsScheduledUuid(srUuidUtils.generateEmailsScheduledUuid()),
    campaign_id = Some(campaignId),
    step_id = Some(3),
    prospect_id = Some(prospectId),
    to_email = "<EMAIL>",
    reply_to_email = Some("<EMAIL>"),
    step_type = CampaignStepType.ManualEmailStep,
    from_email = "<EMAIL>",
    added_at = DateTime.now().minusMonths(10),
    scheduled_at = aDate,
    sender_email_settings_id = senderEmailSettingId,
    template_id = None,
    variant_id = Some(1),
    rep_mail_server_id = 1,
    campaign_email_setting_id = CampaignEmailSettingsId(123),
    team_id = TeamId(team_id),
    to_name = None, from_name = "Animesh", reply_to_name = None, body = "this is a test body", base_body = "this is a test body", text_body = "this is a test body", subject = "this is a test subject"
  )

  val email_service_options_for_get_bodies = EmailOptionsForGetBodies(
    for_editable_preview =  false,
    editedPreviewEmailAlreadyChecked = false,
    custom_tracking_domain = None,
    default_tracking_domain = "www.tracking-domain.com",
    default_unsubscribe_domain = "www.unsubscribe-domain.com",
    opt_out_msg = "optMessage",
    opt_out_is_text= false ,
    append_followups = false,
    signature = None,
    bodyTemplate = "Variant body",
    subjectTemplate = "variant subject",
    email_sender_name = "Shashank Dwivedi",
    sender_first_name = "Shashank",
    sender_last_name = "Dwivedi",
    manualEmail = false,
    trackOpens = false,
    trackClicks = false,
    isCampaignSendTestEmail = false
    , // send test email for campaign
    previousEmails = Seq()
    ,
    allTrackingDomainsUsed = Seq()
  )

  val prospectAccount = ProspectAccount(
    id = prospectId,
    owner_id = prospectOwnerId,
    owner_name = "praveen",
    team_id = 505,
    name = prospectName,
    custom_id = "custom Id",

    total_prospects = 0,

    description = None,
    source = None,
    website = None,
    industry = None,
    linkedin_url = None,
    created_at = aDate.minusDays(10),
    updated_at = aDate.minusDays(10),
    custom_fields = parsedJson,
    uuid = ProspectAccountUuid("pa_sfdsffd")
  )

  val email_service_body = EmailServiceBody(
    subject = "variant subject",
    textBody = "variant body",
    htmlBody = "body",
    baseBody = "body",
    isEditedPreviewEmail = false,
    has_unsubscribe_link = true
  )

  val email_scheduled_new_step_2 = EmailScheduledNewStep2(
    email_scheduled_id = 3,
    subject = "variant subject",
    body = "body",
    base_body = "body",
    text_body = "variant body",

    has_open_tracking = false,
    has_click_tracking = false,
    has_unsubscribe_link = true,
    is_edited_preview_email = false,

    list_unsubscribe_header =  Some("<mailto:<EMAIL>?subject=unsubscribe>"),
    gmail_fbl = None
  )

  val campaign_prospect_update_schedule_status = CampaignProspectUpdateScheduleStatus(
    current_step_status_for_scheduler_data = CurrentStepStatusForSchedulerData.Due(due_at = aDate),
    current_step_type = CampaignStepType.ManualEmailStep,
    current_step_task_id = "3",
    step_id = 3,
    campaign_id =  3,
    prospect_id = 7,
    email_message_id = Some(3),
    current_campaign_email_settings_id =  Some(CampaignEmailSettingsId(123))
  )

  val update_campaign_prospects_list = List(
    campaign_prospect_update_schedule_status,
    campaign_prospect_update_schedule_status,
    campaign_prospect_update_schedule_status,
    campaign_prospect_update_schedule_status,
    campaign_prospect_update_schedule_status,
    campaign_prospect_update_schedule_status,
    campaign_prospect_update_schedule_status

  )
}
