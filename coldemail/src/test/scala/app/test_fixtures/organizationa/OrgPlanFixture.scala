package app.test_fixtures.organizationa

import api.accounts.OrgPlan
import io.sr.billing_common.models.{PlanID, PlanType}
import org.joda.time.DateTime

object OrgPlanFixture {

  val orgPlanFixture: OrgPlan = OrgPlan(
    new_prospects_paused_till = None,
    current_cycle_started_at = DateTime.now(),
    is_v2_business_plan = true,

    next_billing_date = None,
    fs_account_id = None,
    stripe_customer_id = None,
    payment_gateway = None,
    plan_type = PlanType.PAID,
    plan_name = "pro",
    payment_due_invoice_link = None,
    payment_due_campaign_pause_at = None,
    plan_id = PlanID.PRO
  )

}
