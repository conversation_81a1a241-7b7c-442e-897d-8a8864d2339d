package app.test_fixtures.campaign_settings

import api.accounts.TeamId
import api.campaigns.ChannelSettingUuid
import api.campaigns.models.LinkedinSettingSenderDetails

object LinkedinSettingSenderDetailsFixtures {

  val teamId = TeamId(id = 37L)

  val linkedin_setting_sender_details = LinkedinSettingSenderDetails(
    channel_setting_uuid = ChannelSettingUuid(uuid = "1"),
    team_id = teamId,
    email = "<EMAIL>",
    first_name = "gokulnath",
    last_name = "S",
    linkedin_profile_url = Some("www.linekdin.com/SRTest"),
    automation_enabled = false
  )
}
