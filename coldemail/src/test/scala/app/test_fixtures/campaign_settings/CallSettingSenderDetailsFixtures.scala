package app.test_fixtures.campaign_settings

import api.accounts.TeamId
import api.campaigns.ChannelSettingUuid
import api.campaigns.models.CallSettingSenderDetails

object CallSettingSenderDetailsFixtures {

  val teamId = TeamId(id = 37L)

  val call_setting_sender_details = CallSettingSenderDetails(
    channel_setting_uuid = ChannelSettingUuid(uuid = "1"),
    team_id = teamId,
    phone_number = Some("**********"),
    first_name = "gokulnath",
    last_name = "S"
  )

}
