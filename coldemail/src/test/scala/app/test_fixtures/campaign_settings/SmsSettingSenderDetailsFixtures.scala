package app.test_fixtures.campaign_settings

import api.accounts.TeamId
import api.campaigns.ChannelSettingUuid
import api.campaigns.models.SmsSettingSenderDetails

object SmsSettingSenderDetailsFixtures {

  val teamId = TeamId(id = 37L)

  val sms_setting_sender_details = SmsSettingSenderDetails(
    channel_setting_uuid = ChannelSettingUuid(uuid = "1"),
    team_id = teamId,
    phone_number = "**********",
    first_name = "gokulnath",
    last_name = "S"
  )

}
