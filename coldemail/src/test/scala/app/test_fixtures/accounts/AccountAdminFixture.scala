package app.test_fixtures.accounts

import api.accounts.{Account, AccountAccess, AccountMetadata, AccountType, AccountUuid, OrgCountData, OrgSettings, OrganizationRole, OrganizationWithCurrentData, PermissionOwnershipV2, ProspectCategoriesInDB, ReplyHandling, RolePermissionDataDAOV2, RolePermissionDataV2, RolePermissionsInDBV2, TeamAccount, TeamAccountRole, TeamMember, TeamMemberLite}
import api.accounts.models.AccountProfileInfo
import api.prospects.models.ProspectCategoryRank
import api.team.TeamUuid
import app.test_fixtures.organizationa.{OrgMetadataFixture, OrgPlanFixture}
import org.joda.time.DateTime

object AccountAdminFixture {
  val campaign_id: Long = 121L
  val campaign_name = "CampaignName"
  val permittedAccountIds = Seq(2L)
  val teamId: Long = 37L
  val ownerId: Long = 2L

  val first_name = "first_name"
  val last_name = "last_name"
  val company = "CompanyName"
  val email = "<EMAIL>"
  val email_domain = "smartreach.com"

  val orgId = 1L

  val aDate = DateTime.parse("2022-3-27")


  val profile = AccountProfileInfo(
    first_name = first_name,
    last_name = last_name,
    company = Some(company),
    timezone = None,
    country_code = None,
    mobile_country_code = None,
    mobile_number = None,
    onboarding_phone_number= None,
    twofa_enabled = false,
    has_gauthenticator = false,
    weekly_report_emails = None,
    scheduled_for_deletion_at = None
  )

  val accountMetadata = AccountMetadata(
    // account_ui_version = None,
    is_profile_onboarding_done = None
  )

  val orgMetadata = OrgMetadataFixture.orgMetadataFixture2

  val orgCountData: OrgCountData = OrgCountDataFixture.orgCountData_default

  val orgSettings = OrgSettings(
    enable_ab_testing = false,
    disable_force_send = false,
    bulk_sender = false,
    allow_2fa = false,
    show_2fa_setting = false,
    enforce_2fa = false,
    allow_native_crm_integration = false,
      agency_option_allow_changing = false,
      agency_option_show = false
  )

  val orgPlan = OrgPlanFixture.orgPlanFixture


  val org = OrganizationWithCurrentData(

    id = orgId,
    name = company,
    owner_account_id = 49,
    /*


    fs_account_id = None,
    stripe_customer_id = None,
    payment_gateway = None,
    plan_type = PlanType.PAID,
    plan_name = "ultimate",
    plan_id = PlanID.ULTIMATE,
    */

    counts = orgCountData,
    settings = orgSettings,
    plan = orgPlan,

    is_agency = true,
    trial_ends_at = DateTime.now().plusDays(100),
    error = None,
    error_code = None,
    paused_till = None,
    errors = Seq(),
    warnings = Seq(),
    via_referral = false,
    org_metadata = orgMetadata
  )

  val rolePermissionInDb = RolePermissionsInDBV2(

    id = 11L,
    role_name = TeamAccountRole.ADMIN,

    manage_billing_v2 = PermissionOwnershipV2.All,
    view_user_management_v2 = PermissionOwnershipV2.All,
    edit_user_management_v2 = PermissionOwnershipV2.All,

    view_team_config_v2 = PermissionOwnershipV2.All,
    edit_team_config_v2 = PermissionOwnershipV2.All,

    view_prospects_v2 = PermissionOwnershipV2.All,

    edit_prospects_v2 = PermissionOwnershipV2.All,

    delete_prospects_v2 = PermissionOwnershipV2.All,


    view_campaigns_v2 = PermissionOwnershipV2.All,

    edit_campaigns_v2 = PermissionOwnershipV2.All,

    delete_campaigns_v2 = PermissionOwnershipV2.All,

    change_campaign_status_v2 = PermissionOwnershipV2.All,


    view_reports_v2 = PermissionOwnershipV2.All,

    edit_reports_v2 = PermissionOwnershipV2.All,

    download_reports_v2 = PermissionOwnershipV2.All,

    send_manual_email_v2 = PermissionOwnershipV2.All,


    view_templates_v2 = PermissionOwnershipV2.All,

    edit_templates_v2 = PermissionOwnershipV2.All,

    delete_templates_v2 = PermissionOwnershipV2.All,


    view_blacklist_v2 = PermissionOwnershipV2.All,

    edit_blacklist_v2 = PermissionOwnershipV2.All,


    view_workflows_v2 = PermissionOwnershipV2.All,

    edit_workflows_v2 = PermissionOwnershipV2.All,

    view_webhooks_v2 = PermissionOwnershipV2.All,

    edit_webhooks_v2 = PermissionOwnershipV2.All,

//    view_prospect_accounts_v2 = PermissionOwnershipV2.All,
//
//    edit_prospect_accounts_v2 = PermissionOwnershipV2.All,

    view_channels_v2 = PermissionOwnershipV2.All,
    edit_channels_v2 = PermissionOwnershipV2.All,
    delete_channels_v2 = PermissionOwnershipV2.All,

    view_tasks_v2 = PermissionOwnershipV2.All,
    edit_tasks_v2 = PermissionOwnershipV2.All,
    delete_tasks_v2 = PermissionOwnershipV2.All,

    edit_pipeline_details_v2 = PermissionOwnershipV2.All,
    delete_pipeline_details_v2 = PermissionOwnershipV2.All,

    view_opportunities_v2 = PermissionOwnershipV2.All,
    edit_opportunities_v2 = PermissionOwnershipV2.All,
    delete_opportunities_v2 = PermissionOwnershipV2.All,

  )

  val teamMember: TeamMember = TeamMember(
    team_id = teamId,
    team_name = "team_name",
    user_id = 2L,
    ta_id = 49L, // dont send ta_id to frontend / api response, only for internal purpose, its dynamically assigned in AuthUtils
    first_name = Some(first_name),
    last_name = Some(last_name),
    email = "<EMAIL>",
    team_role = TeamAccountRole.ADMIN,
    api_key = Some("apiKey"),
    zapier_key = Some("zapier_key")
  )

  val teamMemberLite = TeamMemberLite(

    user_id =  2L,
    first_name = Some("first_name"),
    last_name = Some("last_name"),
    email = "<EMAIL>",
    active = true,
    timezone = Some("campaignTimezone"),
    twofa_enabled = true,
    created_at = aDate,
    user_uuid = AccountUuid("uuid"),
    team_role = TeamAccountRole.ADMIN

  )

  val prospect_CategoriesInDB = ProspectCategoriesInDB(
    id = 22L,
    name = "Completed",
    text_id = "Done",
    label_color = "Blue",
    is_custom = true,
    team_id = teamId,
    rank = ProspectCategoryRank(rank = 2000),
  )

  val adminDefaultPermissions = RolePermissionDataDAOV2.defaultRoles(
    role = TeamAccountRole.ADMIN,
    simpler_perm_flag = false
  )

  val rolePermissionData = RolePermissionDataV2.toRolePermissionApi(
    data = adminDefaultPermissions.copy(id = 10)
  )

  val team_account: TeamAccount = TeamAccount(

    team_id = teamId,
    org_id = orgId,

    role_from_db = Some(adminDefaultPermissions), // MUST come from db (option type only for cacheservice error), should not be sent to frontend, only intermediate

    role = Some(rolePermissionData), // should be sent to frontend

    active = true,
    is_actively_used = true,
    team_name = "team_name",
    total_members = 5,
    access_members = Seq(teamMember),
    all_members = Seq(teamMemberLite),

    prospect_categories_custom = Seq(prospect_CategoriesInDB),
    max_emails_per_prospect_per_day = 100L,
    max_emails_per_prospect_per_week = 500L,
    max_emails_per_prospect_account_per_day = 97,
    max_emails_per_prospect_account_per_week = 497,

    reply_handling = ReplyHandling.PAUSE_SPECIFIC_CAMPAIGN_ON_REPLY,
    created_at = aDate,
    selected_calendar_data = None,
    team_uuid = TeamUuid("uuid")

  )

  val accountAdmin = Account(
    id = AccountUuid("account_uuid"),
    internal_id = 2,
    email = email,
    email_verification_code = None,
    email_verification_code_created_at = None,
    created_at = DateTime.now().minusDays(1000),
    first_name = Some(first_name),
    last_name = Some(last_name),
    company = Some(company),
    timezone = None,
    profile = profile,
    org_role = Some(OrganizationRole.OWNER),
    teams = Seq(team_account),
    account_type = AccountType.AGENCY,
    org = org,
    active = true,
    email_notification_summary = "dSFA",
    account_metadata = accountMetadata,
    email_verified = true,
    signupType = None,
    account_access = AccountAccess(
      inbox_access = false
    ),
    calendar_account_data = None

  )
}
