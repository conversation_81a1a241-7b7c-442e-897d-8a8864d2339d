package app.test_models

import play.api.libs.json.{Json, OFormat}



case class ReplySentimentTypeDataTest(
        reply_sentiment_type: String,
        reply_sentiment_name : String,
        reply_sentiment_channel_type : String
)

object ReplySentimentTypeDataTest {
  given format: OFormat[ReplySentimentTypeDataTest] = Json.format[ReplySentimentTypeDataTest]

}


case class ReplySentimentTest(
                               uuid: String,
                               reply_sentiment: ReplySentimentTypeDataTest
                             )

object ReplySentimentTest {
  given format: OFormat[ReplySentimentTest] = Json.format[ReplySentimentTest]

}

case class PauseCampaignResponse(
                                  total_status_changed: Long
                                )


object PauseCampaignResponse {
  given format: OFormat[PauseCampaignResponse] = Json.format[PauseCampaignResponse]

}