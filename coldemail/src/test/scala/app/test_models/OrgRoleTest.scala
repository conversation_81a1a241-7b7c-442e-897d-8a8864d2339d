package app.test_models

import play.api.libs.json.{Format, <PERSON>s<PERSON><PERSON><PERSON>, JsR<PERSON>ult, JsString, <PERSON>sSuc<PERSON>, JsValue}

sealed trait OrganizationRoleTest {
  def toString: String
}

object OrganizationRoleTest {
  private val owner = "owner"
  private val agencyAdmin = "agency_admin"

  case object OWNER extends OrganizationRoleTest {
    override def toString: String = owner
  }

  case object AGENCY_ADMIN extends OrganizationRoleTest {
    override def toString: String = agencyAdmin
  }

  def withName(key: String): Option[OrganizationRoleTest] = {
    key match {
      case `owner` => Some(OWNER)
      case `agencyAdmin` => Some(AGENCY_ADMIN)
      case _ => None
    }
  }

  given format: Format[OrganizationRoleTest] = new Format[OrganizationRoleTest] {
    override def writes(o: OrganizationRoleTest): JsValue = {
      JsString(o.toString)
    }

    override def reads(json: JsValue): JsResult[OrganizationRoleTest] = {
      withName(json.as[String]) match {
        case None => JsE<PERSON>r("Invalid org_role")
        case Some(value) => JsSuccess(value)
      }
    }
  }
}
