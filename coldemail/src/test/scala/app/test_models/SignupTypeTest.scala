package app.test_models

import play.api.libs.json.{Js<PERSON><PERSON><PERSON>, JsString, JsSuc<PERSON>, Reads}
import utils.enum_sr_utils.SREnumJsonUtils

import scala.util.{Failure, Success, Try}

sealed trait SignupTypeTest
sealed trait OAuthSignupTypeTest extends SignupTypeTest//added to make the match for NewAuthSignupThroughHostService exhaustive.

object SignupTypeTest extends SREnumJsonUtils[SignupTypeTest]{
  override protected val enumName: String = "SignupType"

  private val google = "google"
  private val microsoft = "microsoft"
  private val password = "password"

  case object Google extends OAuthSignupTypeTest{
    override def toString: String = google
  }

  case object Microsoft extends OAuthSignupTypeTest{
    override def toString: String = microsoft
  }

  case object Password extends SignupTypeTest{
    override def toString: String = password
  }

  override def fromKey(key: String): Try[SignupTypeTest] = Try{
    key match {
      case `google` => Google
      case `microsoft` => Microsoft
      case `password` => Password
    }
  }

  def fromKeyOAuth(key: String): Try[OAuthSignupTypeTest] = Try{
    key match {
      case `google` => Google
      case `microsoft` => Microsoft
    }
  }

  override def toKey(value: SignupTypeTest): String = {
    value.toString
  }


  implicit def Reads: Reads[SignupTypeTest] = {
    case JsString(value) =>
      fromKey(key = value) match {

        case Failure(exception) => JsError(exception.toString)

        case Success(data) => JsSuccess(value = data)
      }

    case _ =>
      JsError(s"""Expected a SignupType string, got something else """)
  }

}

