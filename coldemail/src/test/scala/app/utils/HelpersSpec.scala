package app.utils

import api.accounts.{Account, AccountAccess, AccountDAO, AccountErrorObjectApi, AccountMetadata, AccountType, AccountUuid, OrgCountData, OrgMetadata, OrgPlan, OrgSettings, ReplyHandling, RolePermissionDataDAOV2, TeamAccount, TeamAccountRole, TeamId, TeamMember}
import api.accounts.models.{AccountId, AccountProfileInfo, OrgId, OrgUuid, SignupType}
import api.accounts.{OrganizationRole, OrganizationWithCurrentData, TeamAccountRole, TeamId}
import api.AppConfig
import api.accounts.{Account, AccountUuidAndId}
import api.accounts.models.OrgId
import api.campaigns.models.{NavigationTime, SearchParams}
import api.prospects.InferredQueryTimeline
import api.team.TeamUuid
import app.test_fixtures.accounts.OrgCountDataFixture
import app.test_fixtures.prospect.ProspectFixtures.prospectObject
import org.joda.time.DateTime
import org.scalatest.funspec.AnyFunSpec
import org.scalatest.matchers.should.Matchers.a
import utils.{Help<PERSON>, SRLogger}
import play.api.Logger
import io.lemonlabs.uri.Url
import io.sr.billing_common.models.PlanID.PRO
import io.sr.billing_common.models.PlanType.PAID
import org.joda.time.DateTimeZone

import java.net.{URI, URL}
import scala.util.{Failure, Success, Try}
import utils.email.EmailHelper
import utils.helpers.LogHelpers

class HelpersSpec extends AnyFunSpec {

  given Logger: SRLogger = SRLogger("HelpersSpec")

  describe("Testing utils.Helpers.seqEitherToEitherSeq"){

    it("It should return left when seq of either has one left one right"){
     val eithers =  Seq(Left("Error while checking").withRight, Right("CDLDJ98XMNS").withLeft)
      val result = Helpers.seqEitherToEitherSeq(eithers)
      assert(result.isLeft)
    }

    it("It should return left when seq of either has one left two rights"){
      val eithers =  Seq(Right("CDLDJ98XMNS").withLeft, Right("XCDFRGT78LKJ").withLeft, Left("Error while checking").withRight)
      val result = Helpers.seqEitherToEitherSeq(eithers)
      assert(result.isLeft)
    }

    it("It should return right when seq of either has  all rights"){
      val eithers =  Seq(Right("CDLDJ98XMNS").withLeft, Right("XCDFRGT78LKJ").withLeft, Right("XCDFRGT78LKJ").withLeft)
      val result = Helpers.seqEitherToEitherSeq(eithers)
      assert(result.isRight)
    }

  }

  describe("testing utils.Helpers.getNextLinkForCampaignsApi"){

    val page_data = NavigationTime(
      next = Some(1696654536439L)
    )
    val dateTime = DateTime.now()

    it("should return newer than"){
      val url = Url.parse("/api/v3/campaigns?older_than=1673668700000")
      val searchParams = SearchParams(
        name = None,
        sender_email_setting = None,
        receiver_email_setting = None,
        status = None,
        range = InferredQueryTimeline.Range.Before(dateTime = dateTime),
        is_first = true
      )
      val expected_result = Some("/api/v3/campaigns?older_than=1696654536439")
      val res = Helpers.getNextLinkForCampaignsApi(
        uri = url,
        page_data = page_data,
        search_params = searchParams
      )
      assert(expected_result == res)
    }

    it("should return older than") {
      val url = Url.parse("/api/v3/campaigns?newer_than=1673668700000")

      val searchParams = SearchParams(
        name = None,
        sender_email_setting = None,
        receiver_email_setting = None,
        status = None,
        range = InferredQueryTimeline.Range.After(dateTime = dateTime),
        is_first = true
      )
      val expected_result = Some("/api/v3/campaigns?newer_than=1696654536439")
      val res = Helpers.getNextLinkForCampaignsApi(
        uri = url,
        page_data = page_data,
        search_params = searchParams
      )
      assert(expected_result == res)
    }
  }

  describe("Testing Helpers.getTimelineRange") {

    describe("Helpers.getTimelineRange fail conditions") {

      it("should fail if both older_than and newer_than is defined") {

        val parsedParams: Map[String, Vector[String]] = Map(
          "older_than" -> Vector("123"),
          "newer_than" -> Vector("123")
        )

        val res = Helpers.getTimeLineRange(parsedParams)

        res match {

          case Failure(err) =>
            assert(err.getMessage == "older than and newer than cannot be defined together")

          case Success(_) =>
            assert(false)

        }

      }

      it("should fail if older_than is defined and date.year <= 2000") {

        val parsedParams: Map[String, Vector[String]] = Map(
          "older_than" -> Vector("947415214000")
        )

        val res = Helpers.getTimeLineRange(parsedParams)

        res match {

          case Failure(err) =>
            assert(err.getMessage == "Invalid date provided")

          case Success(_) =>
            assert(false)

        }

      }

      it("should fail if newer_than is defined and date.year <= 2000") {

        val parsedParams: Map[String, Vector[String]] = Map(
          "newer_than" -> Vector("947415214000")
        )

        val res = Helpers.getTimeLineRange(parsedParams)

        res match {

          case Failure(err) =>
            assert(err.getMessage == "Invalid date provided")

          case Success(_) =>
            assert(false)

        }

      }
    }

    describe("Helpers.getTimelineRange pass conditions") {

    it("should pass if both are undefined and return is_first = true") {

        val parsedParams: Map[String, Vector[String]] = Map()

        val res = Helpers.getTimeLineRange(parsedParams)

        res match {

          case Failure(_) =>
            assert(false)

          case Success(value: (Boolean, InferredQueryTimeline.Range)) =>
            assert(value._1)
            value._2 match {
              case InferredQueryTimeline.Range.Before(_) =>
                assert(true)
              case _ => assert(false)
            }
        }

      }

      it("should pass if older_than is defined with correct value and is_first is false") {

        val dateTimeStr: String = "1704797614000"

        val parsedParams: Map[String, Vector[String]] = Map(
          "older_than" -> Vector(dateTimeStr)
        )

        val res = Helpers.getTimeLineRange(parsedParams)

        res match {

          case Failure(_) =>
            assert(false)

          case Success(value: (Boolean, InferredQueryTimeline.Range)) =>
            assert(!value._1)
            value._2 match {

              case InferredQueryTimeline.Range.Before(dateTime) =>
                assert(dateTime == new DateTime(dateTimeStr.toLong).toDateTime(DateTimeZone.forID("UTC")))

              case _ => assert(false)

            }

        }

      }

      it("should pass if newer_than is defined with correct value and is_first is false") {

        val dateTimeStr: String = "1704797614000"

        val parsedParams: Map[String, Vector[String]] = Map(
          "newer_than" -> Vector(dateTimeStr)
        )

        val res = Helpers.getTimeLineRange(parsedParams)

        res match {

          case Failure(_) =>
            assert(false)

          case Success(value: (Boolean, InferredQueryTimeline.Range)) =>
            assert(!value._1)
            value._2 match {

              case InferredQueryTimeline.Range.After(dateTime) =>
                assert(dateTime == new DateTime(dateTimeStr.toLong).toDateTime(DateTimeZone.forID("UTC")))

              case _ => assert(false)

            }

        }

      }

    }

  }


  describe("Tests for Helpers.stringToUrl") {

    it("should run via old method for orgId = None") {

      val href = s"https://smartreach.io"

      val oldMethod = new URL(href)

      val res = Helpers.stringToURL(href = href, orgIdOpt = None)

      res match {

        case Failure(exception) =>

          println(LogHelpers.getStackTraceAsString(exception))

          assert(false)

        case Success(value) =>

          assert(value == oldMethod)

      }

    }

    it("should run via new method for orgId = Some(orgId) which is in prod flagged list") {

      val href = s"https://smartreach.io"

      val newMethod = new URI(href).toURL
      val orgId = AppConfig.orgs_for_new_URI_change_prod.head

      val res = Helpers.stringToURL(href = href, orgIdOpt = Some(OrgId(orgId)))

      res match {

        case Failure(exception) =>

          println(LogHelpers.getStackTraceAsString(exception))

          assert(false)

        case Success(value) =>

          assert(value == newMethod)

      }

    }

  }





    describe("Helpers.getPercentWith1Decimal"){
        it("should return - for emailvalidated is 0"){

            val resultPercentage = Helpers.getPercentWith1Decimal(
                numerator = 12,
                denominator = 0L
            )

            assert(resultPercentage == "(-)")


        }

        it("should return some value when emailvalidated is not 0"){

            val resultPercentage = Helpers.getPercentWith1Decimal(
                numerator = 5,
                denominator = 10
            )

            println(resultPercentage)
            assert(resultPercentage == "(50.0%)")
        }
    }

  describe("Helpers.getDomainFromEmail") {
    it("should return None for an invalid email address") {
      val invalidEmail = "invalid-email"
      val resultDomain = Helpers.getDomainFromEmail(invalidEmail)

      assert(resultDomain.isEmpty) // Ensure result is None
    }

    it("should return None for an email without a domain") {
      val noDomainEmail = "user@"
      val resultDomain = Helpers.getDomainFromEmail(noDomainEmail)

      assert(resultDomain.isEmpty) // Ensure result is None
    }

    it("should return None for a malformed email") {
      val noDomainEmail = "@domain.com"
      val resultDomain = Helpers.getDomainFromEmail(noDomainEmail)

      assert(resultDomain.isEmpty) // Ensure result is None
    }

    it("should return the domain for a valid email address") {
      val validEmail = "<EMAIL>"
      val resultDomain = Helpers.getDomainFromEmail(validEmail)

      assert(resultDomain.get == "domain.com") // Ensure result is Some("domain.com")
    }

    it("should handle subdomains correctly") {
      val subdomainEmail = "<EMAIL>"
      val resultDomain = Helpers.getDomainFromEmail(subdomainEmail)

      assert(resultDomain.get == "sub.domain.com") // Ensure result is Some("sub.domain.com")
    }
  }

  describe("should give Sunday") {
    val sunday = Helpers.getLastSunday
    println(s"sunday ====== $sunday")
    assert(sunday.dayOfWeek().get() == 7)
  }

  val dummyAccountProfileInfo: AccountProfileInfo = AccountProfileInfo(
    first_name = "John",
    last_name = "Doe",
    company = Some("Example Corp"),
    timezone = Some("UTC"),
    country_code = Some("US"),
    mobile_country_code = Some("+1"),
    mobile_number = Some(1234567890L),
    onboarding_phone_number = Some("+***********"),
    twofa_enabled = false,
    has_gauthenticator = false,
    scheduled_for_deletion_at = None,
    weekly_report_emails = Some("<EMAIL>")
  )

  val dummyOrganization = OrganizationWithCurrentData(
    id = 1L,
    name = "Dummy Corp",
    owner_account_id = 1001L,
    counts = OrgCountDataFixture.orgCountData_default,
    settings = OrgSettings(
      enable_ab_testing = true,
      disable_force_send = false,
      bulk_sender = false,
      allow_2fa = true,
      show_2fa_setting = true,
      enforce_2fa = false,
      agency_option_show = true,
      agency_option_allow_changing = true,
      allow_native_crm_integration = true
    ),
    plan = OrgPlan(
      new_prospects_paused_till = None,
      is_v2_business_plan = false,
      fs_account_id = Some("FS12345"),
      stripe_customer_id = Some("cus_ABC123"),
      payment_gateway = None,
      current_cycle_started_at = DateTime.now(),
      next_billing_date = None,
      payment_due_invoice_link = Some("https://invoice.link"),
      payment_due_campaign_pause_at = None,
      plan_type = PAID,
      plan_name = "Paid",
      plan_id = PRO
    ),
    is_agency = false,
    trial_ends_at = DateTime.now().plusDays(14),
    error = Some("Some error occurred"),
    error_code = Some("ERR_001"),
    paused_till = Some(DateTime.now().plusDays(7)),
    errors = Seq(
      AccountErrorObjectApi(
        error_msg = "Invalid account settings",
        error_code = "ERR_101",
        error_at = None,
        upgrade_now_prompt = false
      )
    ),
    warnings = Seq(),
    via_referral = true,
    org_metadata = OrgMetadata(
    show_agency_pricing = Some(true),
    show_individual_plans = Some(false),
    show_business_plans = Some(true),
    show_business_pro_plan = Some(true),
    enable_v5_plans = Some(true),
    has_custom_plan = Some(false),
    v4_base_plan_id = Some("v4-199"),
    ignore_inactive_teams_for_v4_plan_limit_check = Some(false),
    min_seats = Some(5),
    show_campaign_send_start_report = Some(true),
    allowed_for_new_google_api_key = Some(true),
    allow_going_back_in_crm_status = Some(false),
    show_referral_program = Some(true),
    salesforce_sandbox_enabled = Some(false),
    show_campaign_inbox = Some(true),
    show_sending_holiday_calendar = Some(true),
    enable_opportunities_pipeline = Some(false),
    show_rms_ip_in_frontend = Some(false),
    enable_domain_health_page = Some(true),
    enable_internal_email_accounts_api_for_warmuphero = Some(false),
    enable_ai_email_generation_for_warmuphero = Some(true),
    show_leads_sent_for_validation_banner = Some(true),
    hide_google_marketplace_integration = Some(true),
    june_2024_simpler_roles_perms = Some(false),
    enable_callerid_verification = Some(true),
    show_native_calling = Some(true),
    enable_magic_column = Some(false),
    max_email_sending_quota_per_day = Some(250),
    hide_conference_call = Some(false),
    show_inbp_logs_report = Some(true),
    increase_email_delay = Some(false),
    allow_drip_condition = Some(true),
    show_send_plain_text_email = Some(true),
    show_purchased_domains_and_emails = Some(false),
    enable_wh_auto_login = Some(true),
    show_new_create_campaign_model = Some(false),
    is_team_inbox_enabled = Some(true),
    allowed_for_amf = Some(false)
    )
  )

  val dummayAccountMetadata = AccountMetadata(
    is_profile_onboarding_done = Some(true)
  )


  val baseAccount: Account = Account(
    id = AccountUuid("1"),
    internal_id = 1,
    email = "<EMAIL>",
    email_verification_code = None,
    email_verification_code_created_at = None,
    created_at = DateTime.now(),
    first_name = Some("first"),
    last_name = Some("last"),
    company = Some("company"),
    timezone = None,
    profile = dummyAccountProfileInfo,
    org_role = Some(OrganizationRole.OWNER),
    teams = List(),
    account_type = AccountType.AGENCY,
    org = dummyOrganization,
    active = true,
    email_notification_summary = "email_summary",
    account_metadata =dummayAccountMetadata,
    email_verified = true,
    signupType = None,
    account_access = AccountAccess(inbox_access = false),
    calendar_account_data = None
  )

  val baseTeamAccount: TeamAccount = TeamAccount(
    team_id = 1,
    org_id = 1,
    team_uuid = TeamUuid("1"),
    role_from_db = Some(RolePermissionDataDAOV2.defaultRoles(role = TeamAccountRole.ADMIN, simpler_perm_flag = false)),
    role = None,
    active = true,
    is_actively_used = true,
    team_name = "team_name",
    total_members = 1,
    access_members = List(),
    all_members = List(),
    prospect_categories_custom = List(),
    max_emails_per_prospect_per_day = 100,
    max_emails_per_prospect_per_week = 500,
    max_emails_per_prospect_account_per_day = 100,
    max_emails_per_prospect_account_per_week = 500,
    reply_handling = ReplyHandling.PAUSE_SPECIFIC_CAMPAIGN_ON_REPLY,
    selected_calendar_data = None,
    created_at = DateTime.now()
  )

  val baseTeamMember: TeamMember = TeamMember(
    team_id = 1,
    team_name = "team_name",
    user_id = 1,
    ta_id = 1,
    first_name = Some("first"),
    last_name = Some("last"),
    email = "<EMAIL>",
    team_role = TeamAccountRole.ADMIN,
    api_key = None,
    zapier_key = None
  )

  describe("testing getTeamMemberFromAccount"){

    it ("getTeamMemberFromAccount should return the correct team member for a matching account and team ID"){
      val account = baseAccount.copy(teams = List(baseTeamAccount.copy(access_members = List(baseTeamMember))))
      val result = Helpers.getTeamMemberFromAccount(account, TeamId(1))
      assert(result ==  baseTeamMember)
    }

    it("return the correct team member when there are multiple teams and access members") {
      val accessMembers = (1 to 10).map(i => baseTeamMember.copy(team_id = i,user_id = i, email = s"user$<EMAIL>")).toList
      val teams = (1 to 10).map(i => baseTeamAccount.copy(team_id = i, access_members = accessMembers)).toList
      val account = baseAccount.copy(teams = teams, email = "<EMAIL>")
      val result = Helpers.getTeamMemberFromAccount(account, TeamId(5))
      assert(result == baseTeamMember.copy(team_id = 5,user_id = 5, email = "<EMAIL>"))
    }

    it("throw an exception if no matching team ID is found") {
      val accessMembers = (1 to 10).map(i => baseTeamMember.copy(user_id = i, email = s"user$<EMAIL>")).toList
      val teams = (1 to 10).map(i => baseTeamAccount.copy(team_id = i, access_members = accessMembers)).toList
      val account = baseAccount.copy(teams = teams)
      intercept[NoSuchElementException] {
        Helpers.getTeamMemberFromAccount(account, TeamId(11))
      }
    }

    it( "throw an exception if no matching email is found") {
      val accessMembers = (1 to 10).map(i => baseTeamMember.copy(user_id = i, email = s"user$<EMAIL>")).toList
      val teams = (1 to 10).map(i => baseTeamAccount.copy(team_id = i, access_members = accessMembers)).toList
      val account = baseAccount.copy(email = "<EMAIL>", teams = teams)
      intercept[NoSuchElementException] {
        Helpers.getTeamMemberFromAccount(account, TeamId(5))
      }
    }

    it ("throw an exception if the account has no teams" ) {
      val account = baseAccount.copy(teams = List())
      intercept[NoSuchElementException] {
        Helpers.getTeamMemberFromAccount(account, TeamId(1))
      }
    }

    it( "throw an exception if the account has no access members" ) {
      val account = baseAccount.copy(teams = List(baseTeamAccount.copy(access_members = List())))
      intercept[NoSuchElementException] {
        Helpers.getTeamMemberFromAccount(account, TeamId(1))
      }
    }

    it ( "handle case sensitivity and trimming of emails correctly" ) {
      val account = baseAccount.copy(
        email = "  <EMAIL>  ",
        teams = List(baseTeamAccount.copy(access_members = List(baseTeamMember.copy(email = "<EMAIL>"))))
      )
      val result = Helpers.getTeamMemberFromAccount(account, TeamId(1))
      (result == baseTeamMember)
    }
  }

  describe("Testing Helpers.extractEmail") {
    it("should return the same email when there is no plus addressing") {
      val email = "<EMAIL>"
      val result = Helpers.extractEmail(email)
      assert(result == "<EMAIL>")
    }

    it("should remove plus addressing from a simple email") {
      val email = "<EMAIL>"
      val result = Helpers.extractEmail(email)
      assert(result == "<EMAIL>")
    }

    it("should handle complex local parts with dots and special characters") {
      val email = "<EMAIL>"
      val result = Helpers.extractEmail(email)
      assert(result == "<EMAIL>")
    }

    it("should handle multiple plus signs and keep only the local part") {
      val email = "<EMAIL>"
      val result = Helpers.extractEmail(email)
      assert(result == "<EMAIL>")
    }

    it("should handle complex domains") {
      val email = "<EMAIL>"
      val result = Helpers.extractEmail(email)
      assert(result == "<EMAIL>")
    }

    it("should preserve special characters in local part") {
      val email = "<EMAIL>"
      val result = Helpers.extractEmail(email)
      assert(result == "<EMAIL>")
    }

    it("should return the original email if it's invalid") {
      val invalidEmails = Seq(
        "not-an-email",
        "@domain.com",
        "no-domain@",
        "<EMAIL>",
        "+<EMAIL>"
      )

      invalidEmails.foreach { email =>
        val result = Helpers.extractEmail(email)
        assert(result == email)
      }
    }

    it("should handle empty string") {
      val email = ""
      val result = Helpers.extractEmail(email)
      assert(result == "")
    }
  }

  describe("Testing Helpers.convertNewlinesToBrTags") {
    it("should replace a single newline with <br> tag") {
      val input = "Hello\nWorld"
      val result = EmailHelper.convertNewlinesToBrTags(input)
      assert(result == "Hello<br>World")
    }

    it("should replace multiple newlines with <br> tags") {
      val input = "Line1\nLine2\nLine3"
      val result = EmailHelper.convertNewlinesToBrTags(input)
      assert(result == "Line1<br>Line2<br>Line3")
    }

    it("should handle string with no newlines") {
      val input = "No newlines here"
      val result = EmailHelper.convertNewlinesToBrTags(input)
      assert(result == "No newlines here")
    }

    it("should handle empty string") {
      val input = ""
      val result = EmailHelper.convertNewlinesToBrTags(input)
      assert(result == "")
    }

    it("should handle string with only newlines") {
      val input = "\n\n"
      val result = EmailHelper.convertNewlinesToBrTags(input)
      assert(result == "<br><br>")
    }

    it("should preserve other characters and spaces") {
      val input = "  Test\n  Line\t2\nEnd  "
      val result = EmailHelper.convertNewlinesToBrTags(input)
      assert(result == "  Test<br>  Line\t2<br>End  ")
    }
  }

}
