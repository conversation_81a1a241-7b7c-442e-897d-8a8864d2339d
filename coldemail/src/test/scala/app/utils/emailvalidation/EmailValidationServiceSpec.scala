package app.utils.emailvalidation

import api.AppConfig
import org.apache.pekko.actor.ActorSystem
import org.apache.pekko.stream.Materializer
import api.prospects.{ProspectForValidation, ProspectsWithInvalidEmail}
import api.prospects.service.ProspectServiceV2
import api.prospects.models.{EmailForValidation, ProspectCategory, ProspectId}
import utils.emailvalidation.{BatchRequestResponse, BouncerEmailValidationApi, CheckStatusBatchRequestData, DeBounceEmailValidationApi, EmailValidationAPIServiceStatusRecordCheck, EmailValidationApiToolsRecordDAO, EmailValidationBatchRequestModel, EmailValidationData, EmailValidationModel, EmailValidationResultWithAnalysisId, EmailValidationService, EmailValidationToolAssignedRecord, ListCleanEmailValidationApi, ProspectEmailValidationResult, ProspectEmailValidationResultWithTeamId, SREmailValidityStatus, TransformAndSaveEmailValidationResult}
import utils.mq.webhook.{MQWebhookEmailInvalid, MQWebhookEmailInvalidMsg}
import utils.{SRLogger, emailvalidation}
import api.accounts.TeamId
import api.accounts.email.models.EmailServiceProvider
import api.accounts.models.AccountId
import api.campaigns.models.{CampaignEmailSettingsId, CampaignType, CampaignTypeData}
import api.campaigns.services.CampaignId
import api.lead_finder.service.LeadFinderValidationService
import org.scalamock.scalatest.MockFactory
import org.scalatest.funspec.AnyFunSpec
import org.scalatest.Assertions.*
import org.joda.time.{DateTime, DateTimeZone}
import org.scalatest.concurrent.ScalaFutures
import play.api.libs.json.{JsValue, Json}
import play.api.libs.ws.WSClient
import play.api.libs.ws.ahc.AhcWSClient
import sr_scheduler.CampaignStatus
import sr_scheduler.models.{CampaignEmailPriority, CampaignForScheduling}
import sr_scheduler.models.CampaignForScheduling.CampaignEmailSettingForScheduler
import utils.GCP.CloudStorage
import utils.emailvalidation.ValidationResultWithTeamIdAndAnalysisId.{ProspectEmailValidationResultWithTeamIdAndAnalysisId, ValidationResultWithTeamIdAndAnalysisIdWithoutEmailValidationId}
import utils.emailvalidation.dao_service.EmailValidationDAOService
import utils.emailvalidation.models.EmailValidationInitiator.EmailValidationInitiatorType
import utils.emailvalidation.models.EmailValidationToolV2.{BOUNCER, DEBOUNCE, LISTCLEAN}
import utils.emailvalidation.models.{EmailDeliveryAnalysisId, EmailValidationBatchRequestStatusV2, EmailValidationInitiator, EmailValidationPriority, EmailValidationToolStatus, EmailValidationToolStatusType, EmailValidationToolV2, EmailsForValidationWithInitiator, IdsOrEmailsForValidation, InternalInvalidIEmailAddressReasons}
import utils.mq.prospect_category.{MqAutoUpdateProspectCategoryMsg, MqAutoUpdateProspectCategoryPublisher}
import utils.mq.services.MQConfig
import utils.random.SrRandomUtils
import utils.testapp.TestAppExecutionContext
import utils.uuid.SrUuidUtils

import java.sql.SQLException
import scala.concurrent.duration.Duration
import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}

class EmailValidationServiceSpec extends AnyFunSpec with MockFactory with ScalaFutures {

  implicit lazy val logger: SRLogger = new SRLogger("EmailValidationService Spec")

  implicit val system: ActorSystem = TestAppExecutionContext.actorSystem
  implicit val materializer: Materializer = TestAppExecutionContext.actorMaterializer
  implicit val wSClient: AhcWSClient = TestAppExecutionContext.wsClient
  implicit val actorContext: ExecutionContext = system.dispatcher

  val emailValidationModel: EmailValidationModel = mock[EmailValidationModel]

  val transformAndSaveEmailValidationResult: TransformAndSaveEmailValidationResult =
    mock[TransformAndSaveEmailValidationResult]

  val emailValidationBatchRequestModel: EmailValidationBatchRequestModel = mock[EmailValidationBatchRequestModel]
  val emailValidationApiToolsRecordDAO: EmailValidationApiToolsRecordDAO = mock[EmailValidationApiToolsRecordDAO]
  val bouncerEmailValidationApi: BouncerEmailValidationApi = mock[BouncerEmailValidationApi]
  val prospectServiceV2: ProspectServiceV2 = mock[ProspectServiceV2]
  val mQWebhookEmailInvalid: MQWebhookEmailInvalid = mock[MQWebhookEmailInvalid]
  val emailValidationDAOService: EmailValidationDAOService = mock[EmailValidationDAOService]
  val cloudStorage: CloudStorage = mock[CloudStorage]
  val deBounceEmailValidationApi: DeBounceEmailValidationApi = mock[DeBounceEmailValidationApi]
  val srRandomUtils: SrRandomUtils = mock[SrRandomUtils]
  val srUuidUtils: SrUuidUtils = mock[SrUuidUtils]
  val listCleanEmailValidationApi: ListCleanEmailValidationApi = mock[ListCleanEmailValidationApi]
  val leadFinderValidationService: LeadFinderValidationService = mock[LeadFinderValidationService]
  val mqAutoUpdateProspectCategoryPublisher: MqAutoUpdateProspectCategoryPublisher = mock[MqAutoUpdateProspectCategoryPublisher]

  val emailValidationService = new EmailValidationService(
    emailValidationModel = emailValidationModel,
    transformAndSaveEmailValidationResult = transformAndSaveEmailValidationResult,
    emailValidationBatchRequestModel = emailValidationBatchRequestModel,
    emailValidationApiToolsRecordDAO = emailValidationApiToolsRecordDAO,
    bouncerEmailValidationApi = bouncerEmailValidationApi,
    prospectServiceV2 = prospectServiceV2,
    mqWebhookEmailInvalid = mQWebhookEmailInvalid,
    emailValidationDAOService = emailValidationDAOService,
    cloudStorage = cloudStorage,
    deBouncerEmailValidationApi = deBounceEmailValidationApi,
    srRandomUtils = srRandomUtils,
    srUuidUtils = srUuidUtils,
    listCleanEmailValidationApi = listCleanEmailValidationApi,
    mqAutoUpdateProspectCategoryPublisher = mqAutoUpdateProspectCategoryPublisher,
    leadFinderValidationService = leadFinderValidationService,
  )

  val mockWSClient: WSClient = mock[WSClient]
  val mockExecutionContext: ExecutionContext = mock[ExecutionContext]

  val account_id_2 = 2L
  val team_id_3 = 3L
  val org_id_5 = 5L
  val initiator_campaign_id_7 = 7L

  val prospectIdsForValidation: Seq[Long] = Seq(1, 2)

  // ProspectForValidation
  val nonValidatedProspect1 = ProspectForValidation(
    id = 1,
    email = "<EMAIL>",
    email_checked = false,
    email_sent_for_validation = false,
    email_sent_for_validation_at = None
  )

  val nonValidatedProspect2 = ProspectForValidation(
    id = 2,
    email = "<EMAIL>",
    email_checked = false,
    email_sent_for_validation = false,
    email_sent_for_validation_at = None
  )

  val nonValidatedProspect3 = ProspectForValidation(
    id = 3,
    email = "<EMAIL> ",
    email_checked = false,
    email_sent_for_validation = false,
    email_sent_for_validation_at = None
  )

  val validatedProspect1 = ProspectForValidation(
    id = 4,
    email = "<EMAIL>",
    email_checked = true,
    email_sent_for_validation = false,
    email_sent_for_validation_at = Some(DateTime.now().minusDays(9))
  )

  val sentForValidationProspect1 = ProspectForValidation(
    id = 5,
    email = "<EMAIL>",
    email_checked = false,
    email_sent_for_validation = true,
    email_sent_for_validation_at = Some(DateTime.now())
  )

  // ProspectEmailValidationResult
  val validatedProspectEmail1 = ProspectEmailValidationResult(
    email = "<EMAIL>",
    isValid = false,
    emailValidationId = 1
  )

  val validatedProspectEmail2 = ProspectEmailValidationResult(
    email = "<EMAIL>",
    isValid = true,
    emailValidationId = 2
  )

  val validatedProspectEmailWithTeamId1 = ProspectEmailValidationResultWithTeamIdAndAnalysisId(
    emailDeliveryAnalysisId = None,
    teamId = TeamId(team_id_3),
    email = "<EMAIL>",
    isValid = false,
    emailValidationId = 1,
    validationInitiator = EmailValidationInitiator.InitiatedByCampaign,
  )

  val validatedProspectEmailWithTeamId2 = ProspectEmailValidationResultWithTeamIdAndAnalysisId(
    emailDeliveryAnalysisId = None,
    teamId = TeamId(team_id_3),
    email = "<EMAIL>",
    isValid = true,
    emailValidationId = 2,
    validationInitiator = EmailValidationInitiator.InitiatedByCampaign,
  )

  // ProspectsWithInvalidEmail
  val prospectWithInvalidEmail1 = ProspectsWithInvalidEmail(
    prospectId = 1,
    invalidEmail = true,
    accountId = account_id_2,
    teamId = team_id_3
  )


  val campaignForSchedulingEmail = CampaignForScheduling.CampaignForSchedulingEmail(
    campaign_id = 12,
    campaign_owner_id = 12,
    team_id = 1,
    org_id = 1,
    campaign_name = "Campaign1",
    status = CampaignStatus.RUNNING, //

    campaign_type_data = CampaignTypeData.MultiChannelCampaignData(head_step_id = 4),

    // settings
    ai_generation_context = None,

    sending_holiday_calendar_id = None,

    // in CampaignForScheduling, email_settings would be there because we ignore campaigns which do not have them
    campaign_email_setting = CampaignEmailSettingForScheduler(
      sender_email_settings_id = 24,
      receiver_email_settings_id = 24,
      campaign_email_settings_id = CampaignEmailSettingsId(123),
      emailServiceProvider = EmailServiceProvider.OTHER
    ),

    append_followups = false,
    open_tracking_enabled = false,
    click_tracking_enabled = false,
    opt_out_msg = "optMessage",
    opt_out_is_text = false,

    timezone = "Asia/Jakarta",
    daily_from_time = 21 * 60 * 60, // time since beginning of day in seconds
    daily_till_time = 85000, // time since beginning of day in seconds

    // Sunday is the first day
    days_preference = List(true, true, true, true, true, true, true),


    email_priority = CampaignEmailPriority.FIRST_EMAIL,

    max_emails_per_prospect_per_day = 1,
    max_emails_per_prospect_per_week = 3,

    max_emails_per_prospect_account_per_day = 100,
    max_emails_per_prospect_account_per_week = 300,

    campaign_max_emails_per_day = 100,

    // warm up
    softstart_setting = None, // making it none now

    mark_completed_after_days = 20,

    latest_email_scheduled_at = None,


    from_email = "<EMAIL>",
    from_name = "Ritesh Rane",

    reply_to_email = "<EMAIL>",
    reply_to_name = "Ritesh Rane Reply Name",

    min_delay_seconds = 50,
    max_delay_seconds = 100,

    enable_email_validation = false, // keeping it false now

    rep_mail_server_id = 2,
    via_gmail_smtp = Some(false), // Option[DateTime]
    prospects_remaining_to_be_scheduled_exists = Some(true),
    count_of_sender_emails = 1,
    selected_calendar_data = None
  )


  describe("Test sending prospects for validation with sendProspectsForValidation") {

    it("should return 0 if prospectIdsForValidation.isEmpty && prospectsForValidation.isEmpty") {

      val result = emailValidationService.sendProspectsForValidation(
        priority = EmailValidationPriority.Low,
        logger,
        accountId = account_id_2,
        teamId = TeamId(team_id_3),
        orgId = org_id_5,
        isAgency = true,
        idsOrEmailsForValidation = IdsOrEmailsForValidation.EntitiesForValidation(
          emailsForValidations = EmailsForValidationWithInitiator.ProspectEmailsForValidation(
            entitiesForValidation = Seq(),
            initiatorCampaignId = CampaignId(id = initiator_campaign_id_7)
          )
        )
      )

      assert(result == Success(0))
    }

    /**
     * 19 Jul 2024
     *
     * Commenting this test case because now
     * we cannot pass both prospectIdsForValidation and prospectsForValidation
     *
     * We are doing a exhaustive match on the input, so no need to throw an exception.
     *
     */
    //        it("should throw Exception when if prospectIdsForValidation.nonEmpty && prospectsForValidation.nonEmpty") {
    //
    //            val prospectsForValidation: Seq[ProspectForValidation] =
    //                Seq(nonValidatedProspect1, nonValidatedProspect2, nonValidatedProspect3)
    //
    //            val result = emailValidationService.sendProspectsForValidation(
    //                priority = EmailValidationPriority.Low,
    //                logger,
    //                accountId = account_id_2,
    //                teamId = TeamId(team_id_3),
    //                orgId = org_id_5,
    //                isAgency = true,
    //                prospectIdsForValidation = prospectIdsForValidation,
    //                prospectsForValidation = prospectsForValidation,
    //                initiatorWithValue = EmailValidationInitiator.InitiatedByCampaign(initiatorCampaignId = CampaignId(id = initiator_campaign_id_7)),
    //            )
    //
    //            assert(result.isFailure)
    //        }

    it(
      "should return prospectsForValidation.length, if none of prospects & prospect emails have been validated before"
    ) {

      // all prospects not validated
      val prospectsForValidationAllNotValidated: Seq[ProspectForValidation] =
        Seq(nonValidatedProspect1, nonValidatedProspect2, nonValidatedProspect3)

      val prospectEmails: Seq[String] =
        prospectsForValidationAllNotValidated
          .map(p => p.email.trim.toLowerCase).distinct

      // none of the prospect email are validated before, so previouslyValidatedEmailResultsEmpty is Empty
      val previouslyValidatedEmailResultsEmpty: Seq[ProspectEmailValidationResult] = Seq()

      val previouslyValidatedEmailResultsWithTeamIdEmpty: Seq[ProspectEmailValidationResultWithTeamIdAndAnalysisId] = Seq()

      //      (()=>srRandomUtils.getRandomValueForSelectingEmailValidationProvider).expects().returning(0.4)

      (emailValidationDAOService.getPreviouslyValidatedEmailResults)
        .expects(prospectEmails)
        .returning(Success(previouslyValidatedEmailResultsEmpty))

      (prospectServiceV2.updateEmailValidationDataV2)
        .expects(previouslyValidatedEmailResultsWithTeamIdEmpty, logger, EmailValidationInitiator.InitiatedByCampaign)
        .returning(Success((0, Seq())))

      // from this method, if successful
      // we are just return the same prospect: Seq[ProspectForValidation] we pass to it.

      val prospectIdsAndEmailsPublishedForValidation: Seq[(Long, String)] =
        prospectsForValidationAllNotValidated.map(a => (a.id, a.email))
      (emailValidationModel.addForBatchRequest)
        .expects(prospectEmails, account_id_2, team_id_3, EmailValidationPriority.Low, EmailValidationInitiator.InitiatedByCampaign(initiatorCampaignId = CampaignId(id = initiator_campaign_id_7)))
        .returning(Success(prospectIdsAndEmailsPublishedForValidation.toList))


      (prospectServiceV2.markAsSentForValidatingEmail)
        .expects(prospectIdsAndEmailsPublishedForValidation, TeamId(team_id_3), logger)
        .returning(Success(prospectIdsAndEmailsPublishedForValidation.length))

      val result = emailValidationService.sendProspectsForValidation(
        priority = EmailValidationPriority.Low,
        logger,
        accountId = account_id_2,
        teamId = TeamId(team_id_3),
        orgId = org_id_5,
        isAgency = true,
        // prospectIdsForValidation = prospectIdsForValidation,
        idsOrEmailsForValidation = IdsOrEmailsForValidation.EntitiesForValidation(
          emailsForValidations = EmailsForValidationWithInitiator.ProspectEmailsForValidation(
            entitiesForValidation = prospectsForValidationAllNotValidated.map(EmailForValidation.fromProspectForValidation),
            initiatorCampaignId = CampaignId(id = initiator_campaign_id_7)
          )
        )
      )

      assert(result == Success(prospectsForValidationAllNotValidated.length))
    }

    it(
      "should return (prospectsForValidation.length - prospectEmailValidationResult.length), when some prospect emails are validated in last 15 days"
    ) {

      // all prospects not validated
      val prospectsForValidationAllNotValidated: Seq[ProspectForValidation] =
        Seq(nonValidatedProspect1, nonValidatedProspect2, nonValidatedProspect3)

      val prospectEmails: Seq[String] =
        prospectsForValidationAllNotValidated
          .map(p => p.email.trim.toLowerCase).distinct

      val previouslyValidatedEmailResults: Seq[ProspectEmailValidationResult] =
        Seq(validatedProspectEmail1, validatedProspectEmail2)

      val prospectsToBeScheduledForValidation: Seq[ProspectForValidation] = Seq(nonValidatedProspect2)

      val prospectsEmails_2: Seq[String] = prospectsToBeScheduledForValidation
        .map(p => p.email.trim.toLowerCase).distinct

      val previouslyValidatedEmailWithTeamIdResults: Seq[ProspectEmailValidationResultWithTeamIdAndAnalysisId] =
        Seq(validatedProspectEmailWithTeamId1, validatedProspectEmailWithTeamId2)

      //      (()=>srRandomUtils.getRandomValueForSelectingEmailValidationProvider).expects().returning(0.4)

      (emailValidationDAOService.getPreviouslyValidatedEmailResults)
        .expects(prospectEmails)
        .returning(Success(previouslyValidatedEmailResults))

      (prospectServiceV2.updateEmailValidationDataV2)
        .expects(previouslyValidatedEmailWithTeamIdResults, logger, EmailValidationInitiator.InitiatedByCampaign)
        .returning(Success((1, Seq(prospectWithInvalidEmail1))))

      val mQWebhookEmailInvalidMsg1 = MQWebhookEmailInvalidMsg(
        accountId = account_id_2,
        teamId = team_id_3,
        prospectIds = Seq(prospectWithInvalidEmail1.prospectId)
      )

      (mQWebhookEmailInvalid.publish)
        .expects(mQWebhookEmailInvalidMsg1)
        .returning(Success((): Unit))

      // filter validated prospect emails from prospectsForValidationAllNotValidated

      //      val previouslyValidatedEmails: Seq[String] =
      //       previouslyValidatedEmailResults.map(_.email.trim.toLowerCase)
      //     // Filter out already validated prospect emails
      //     val prospectsToBeScheduledForValidation: Seq[ProspectForValidation] =
      //      prospectsForValidationAllNotValidated.filterNot { prospect =>
      //       previouslyValidatedEmails.contains(prospect.email.trim.toLowerCase)
      //      }
      //
      //     // Ensure unique emails
      //     val prospectsEmailsToSchedule: Seq[String] =
      //      prospectsToBeScheduledForValidation.map(_.email.trim.toLowerCase).distinct

      // from this method, if successful
      // we are just return the same prospect: Seq[ProspectForValidation] we pass to it.

      (mqAutoUpdateProspectCategoryPublisher.publish(_: MqAutoUpdateProspectCategoryMsg, _: String, _: Int))
        .expects(MqAutoUpdateProspectCategoryMsg(
          teamId = TeamId(team_id_3),
          doerAccountId = AccountId(account_id_2),
          prospectIds = Seq(ProspectId(prospectWithInvalidEmail1.prospectId)),
          replySentimentUuid = None,
          newProspectCategory = Some(ProspectCategory.DELIVERY_FAILED)
        ), MQConfig.autoUpdateProspectCategoryQueueBaseName, MQConfig.autoUpdateProspectCategoryPrefetchCount)
        .returning(Success(()))

      val prospectIdsAndEmailsPublishedForValidation: Seq[(Long, String)] =
        prospectsToBeScheduledForValidation.map(a => (a.id, a.email))

      (emailValidationModel.addForBatchRequest)
        .expects(prospectsEmails_2, account_id_2, team_id_3, EmailValidationPriority.Low, EmailValidationInitiator.InitiatedByCampaign(initiatorCampaignId = CampaignId(id = initiator_campaign_id_7)))
        .returning(Success(prospectIdsAndEmailsPublishedForValidation.toList))


      (prospectServiceV2.markAsSentForValidatingEmail)
        .expects(prospectIdsAndEmailsPublishedForValidation, TeamId(team_id_3), logger)
        .returning(Success(prospectIdsAndEmailsPublishedForValidation.length))

      val result = emailValidationService.sendProspectsForValidation(
        priority = EmailValidationPriority.Low,
        logger,
        accountId = account_id_2,
        teamId = TeamId(team_id_3),
        orgId = org_id_5,
        isAgency = true,
        // prospectIdsForValidation = prospectIdsForValidation,
        idsOrEmailsForValidation = IdsOrEmailsForValidation.EntitiesForValidation(
          emailsForValidations = EmailsForValidationWithInitiator.ProspectEmailsForValidation(
            entitiesForValidation = prospectsForValidationAllNotValidated.map(EmailForValidation.fromProspectForValidation),
            initiatorCampaignId = CampaignId(id = initiator_campaign_id_7)
          )
        )
      )

      assert(result == Success(prospectsForValidationAllNotValidated.length - previouslyValidatedEmailResults.length))

    }

    it("should return 0 when all the prospects are validated or sent for validation") {

      // all prospects validated or send for validation
      val prospectsForValidationValidOrSentForValidation: Seq[ProspectForValidation] =
        Seq(validatedProspect1, sentForValidationProspect1)

      val prospectEmails: Seq[String] =
        prospectsForValidationValidOrSentForValidation
          .map(p => p.email.trim.toLowerCase).distinct

      val previouslyValidatedEmailResultsEmpty: Seq[ProspectEmailValidationResult] = Seq()
      val previouslyValidatedEmailResultsWithTeamIdEmpty: Seq[ProspectEmailValidationResultWithTeamIdAndAnalysisId] = Seq()

      (emailValidationDAOService.getPreviouslyValidatedEmailResults)
        .expects(prospectEmails)
        .returning(Success(previouslyValidatedEmailResultsEmpty))

      (prospectServiceV2.updateEmailValidationDataV2)
        .expects(previouslyValidatedEmailResultsWithTeamIdEmpty, logger, EmailValidationInitiator.InitiatedByCampaign)
        .returning(Success((0, Seq())))

      // no methods will be called after this, as all prospects are validated or send for validation
      // so it will log an error and return 0

      val result = emailValidationService.sendProspectsForValidation(
        priority = EmailValidationPriority.Low,
        logger,
        accountId = account_id_2,
        teamId = TeamId(team_id_3),
        orgId = org_id_5,
        isAgency = true,
        // prospectIdsForValidation = prospectIdsForValidation,
        idsOrEmailsForValidation = IdsOrEmailsForValidation.EntitiesForValidation(
          emailsForValidations = EmailsForValidationWithInitiator.ProspectEmailsForValidation(
            entitiesForValidation = prospectsForValidationValidOrSentForValidation.map(EmailForValidation.fromProspectForValidation),
            initiatorCampaignId = CampaignId(id = initiator_campaign_id_7)
          )
        )
      )

      assert(result == Success(0))
    }

    it("should return 0 when all the prospect emails are validated in last 15 days") {

      // all prospects not validated
      val prospectsForValidationAllNotValidated: Seq[ProspectForValidation] =
        Seq(nonValidatedProspect1, nonValidatedProspect3)

      (prospectServiceV2.findFromMasterForValidating)
        .expects(prospectIdsForValidation, TeamId(team_id_3), logger)
        .returning(Success(prospectsForValidationAllNotValidated))

      val prospectEmails: Seq[String] =
        prospectsForValidationAllNotValidated
          .map(p => p.email.trim.toLowerCase).distinct

      val previouslyValidatedEmailResults: Seq[ProspectEmailValidationResult] =
        Seq(validatedProspectEmail1, validatedProspectEmail2)

      val previouslyValidatedEmailWithTeamIdResults: Seq[ProspectEmailValidationResultWithTeamIdAndAnalysisId] =
        Seq(validatedProspectEmailWithTeamId1, validatedProspectEmailWithTeamId2)

      (emailValidationDAOService.getPreviouslyValidatedEmailResults)
        .expects(prospectEmails)
        .returning(Success(previouslyValidatedEmailResults))

      (prospectServiceV2.updateEmailValidationDataV2)
        .expects(previouslyValidatedEmailWithTeamIdResults, logger, EmailValidationInitiator.InitiatedByCampaign)
        .returning(Success((1, Seq(prospectWithInvalidEmail1))))

      (mqAutoUpdateProspectCategoryPublisher.publish(_: MqAutoUpdateProspectCategoryMsg, _: String, _: Int))
        .expects(MqAutoUpdateProspectCategoryMsg(
          teamId = TeamId(team_id_3),
          doerAccountId = AccountId(account_id_2),
          prospectIds = Seq(ProspectId(prospectWithInvalidEmail1.prospectId)),
          replySentimentUuid = None,
          newProspectCategory = Some(ProspectCategory.DELIVERY_FAILED)
        ), MQConfig.autoUpdateProspectCategoryQueueBaseName, MQConfig.autoUpdateProspectCategoryPrefetchCount)
        .returning(Success(()))

      val mQWebhookEmailInvalidMsg1 = MQWebhookEmailInvalidMsg(
        accountId = account_id_2,
        teamId = team_id_3,
        prospectIds = Seq(prospectWithInvalidEmail1.prospectId)
      )

      (mQWebhookEmailInvalid.publish)
        .expects(mQWebhookEmailInvalidMsg1)
        .returning(Success((): Unit))

      // no methods will be called after this, as all prospect emails are validated in last 15 days
      // so it will log an error and return 0

      val result = emailValidationService.sendProspectsForValidation(
        priority = EmailValidationPriority.Low,
        logger,
        accountId = account_id_2,
        teamId = TeamId(team_id_3),
        orgId = org_id_5,
        isAgency = true,
        idsOrEmailsForValidation = IdsOrEmailsForValidation.ProspectIdsForValidation(
          prospectIds = prospectIdsForValidation.map(id => ProspectId(id = id)),
          initiatorCampaign = CampaignId(id = initiator_campaign_id_7),
        )
      )

      assert(result == Success(0))
    }

    it(
      "should continue with the validation by 3rd party service even if emailValidationModel.getResultsForPreviouslyValidatedEmails returns Failure"
    ) {

      val prospectsForValidation: Seq[ProspectForValidation] =
        Seq(validatedProspect1, sentForValidationProspect1, nonValidatedProspect3)

      val prospectEmails: Seq[String] =
        prospectsForValidation
          .map(p => p.email.trim.toLowerCase).distinct

      //      (()=>srRandomUtils.getRandomValueForSelectingEmailValidationProvider).expects().returning(0.05)

      // will log a SQLException
      (emailValidationDAOService.getPreviouslyValidatedEmailResults)
        .expects(prospectEmails)
        .returning(Failure(new SQLException))

      // previouslyValidatedEmailResults will be empty as
      // emailValidationDAOService.getPreviouslyValidatedEmailResults returns a failure

      // prospectServiceV2.updateEmailValidationDataV2 will not be called as
      // emailValidationDAOService.getPreviouslyValidatedEmailResults failed

      // filter validated prospect emails from prospectsForValidation
      val prospectsToBeScheduledForValidation: Seq[ProspectForValidation] = Seq(nonValidatedProspect3)
      val prospectEmails_2: Seq[String] = prospectsToBeScheduledForValidation
        .map(p => p.email.trim.toLowerCase).distinct
      // from this method, if successful
      // we are just return the same prospect: Seq[ProspectForValidation] we pass to it.


      val prospectIdsAndEmailsPublishedForValidation: Seq[(Long, String)] =
        prospectsToBeScheduledForValidation.map(a => (a.id, a.email))

      (emailValidationModel.addForBatchRequest)
        .expects(prospectEmails_2, account_id_2, team_id_3, EmailValidationPriority.Low, EmailValidationInitiator.InitiatedByCampaign(initiatorCampaignId = CampaignId(id = initiator_campaign_id_7)))
        .returning(Success(prospectIdsAndEmailsPublishedForValidation.toList))


      (prospectServiceV2.markAsSentForValidatingEmail)
        .expects(prospectIdsAndEmailsPublishedForValidation, TeamId(team_id_3), logger)
        .returning(Success(prospectIdsAndEmailsPublishedForValidation.length))

      val result = emailValidationService.sendProspectsForValidation(
        priority = EmailValidationPriority.Low,
        logger,
        accountId = account_id_2,
        teamId = TeamId(team_id_3),
        orgId = org_id_5,
        isAgency = true,
        idsOrEmailsForValidation = IdsOrEmailsForValidation.EntitiesForValidation(
          emailsForValidations = EmailsForValidationWithInitiator.ProspectEmailsForValidation(
            entitiesForValidation = prospectsForValidation.map(EmailForValidation.fromProspectForValidation),
            initiatorCampaignId = CampaignId(id = initiator_campaign_id_7)
          )
        )
      )

      assert(result == Success(1))
    }
  }

  describe("EmailValidationService.validateIEmailAddress") {
    val failEmail = "meike.\u200Dbilk@\u200Dmurrelektronik.de."
    val passEmail = "<EMAIL>"

    it("fail parse") {
      val result = EmailValidationService.validateIEmailAddress(email = failEmail)
      assert(!result)


    }

    it("pass parse") {
      val result = EmailValidationService.validateIEmailAddress(email = passEmail)
      assert(result)


    }
  }

  describe("checkIfInvalidBasedOnJavaEmailAddressBatch") {
    val failEmail = "meike.\u200Dbilk@\u200Dmurrelektronik.de."
    val passEmail = "<EMAIL>"
    val teamId = TeamId(10L)
    val failEmailValidationData = EmailValidationData(
      email_validations_id = 12L,
      email = failEmail,
      initiator_team_id = teamId,
      checked_via_tool = EmailValidationToolV2.BOUNCER,
      validationInitiator = EmailValidationInitiator.InitiatedByCampaign
    )
    val passEmailValidationData = EmailValidationData(
      email_validations_id = 14L,
      email = passEmail,
      initiator_team_id = teamId,
      checked_via_tool = EmailValidationToolV2.BOUNCER,
      validationInitiator = EmailValidationInitiator.InitiatedByCampaign
    )
    val failProspectEmailValidationResultWithTeamIdAndAnalysisId = ProspectEmailValidationResultWithTeamIdAndAnalysisId(
      emailDeliveryAnalysisId = None,
      teamId = teamId,
      email = failEmail,
      isValid = false,
      emailValidationId = 12L,
      validationInitiator = EmailValidationInitiator.InitiatedByCampaign,
    )
    val ERROR = new Throwable("Error")

    val failProspectsWithInvalidEmail = ProspectsWithInvalidEmail(
      prospectId = 16L,
      invalidEmail = true,
      accountId = 18L,
      teamId = teamId.id
    )
    val failEmailValidationResultWithAnalysisId = EmailValidationResultWithAnalysisId(
      emailDeliveryAnalysisId = None,
      email = failEmail,
      fullJson = Json.obj(),
      isValid = false,
      srEmailValidityStatus = SREmailValidityStatus.SR_INFERRED_UNDELIVERABLE,
      internal_sr_validation_fail_reason = Some(InternalInvalidIEmailAddressReasons.FailedIEmailAddressParse)
    )


    it("prospectServiceV2.updateEmailValidationDataV2 sends fail") {

      (prospectServiceV2.updateEmailValidationDataV2)
        .expects(List(failProspectEmailValidationResultWithTeamIdAndAnalysisId), *, EmailValidationInitiator.InitiatedByCampaign)
        .returning(Failure(ERROR))
      val result = emailValidationService.checkAndUpdateIfInvalidBasedOnJavaEmailAddressBatch(List(
        failEmailValidationData, passEmailValidationData
      ))


      assert(result == Failure(ERROR))

    }

    it("emailValidationModel.updateEmailValidationResultsFromBatchRequest failed") {

      (prospectServiceV2.updateEmailValidationDataV2)
        .expects(List(failProspectEmailValidationResultWithTeamIdAndAnalysisId), *, EmailValidationInitiator.InitiatedByCampaign)
        .returning(Success((1, Seq(failProspectsWithInvalidEmail))))

      (leadFinderValidationService.updateLeadFinderValidationResults(_: List[ValidationResultWithTeamIdAndAnalysisIdWithoutEmailValidationId])(_: SRLogger))
        .expects(List(), logger)
        .returning(Success(0))

      (emailValidationModel.updateEmailValidationResultsFromBatchRequest)
        .expects(Seq(failEmailValidationResultWithAnalysisId), EmailValidationToolV2.SMARTREACH_INTERNAL)
        .returning(Failure(ERROR))
      val result = emailValidationService.checkAndUpdateIfInvalidBasedOnJavaEmailAddressBatch(List(
        failEmailValidationData, passEmailValidationData
      ))


      assert(result == Failure(ERROR))

    }


    it("Should send the pass value") {
      (mQWebhookEmailInvalid.publish)
        .expects(MQWebhookEmailInvalidMsg(accountId = 18L, teamId = 10L, prospectIds = List(16L)))
        .returning(Success({}))
      (prospectServiceV2.updateEmailValidationDataV2)
        .expects(List(failProspectEmailValidationResultWithTeamIdAndAnalysisId), *, EmailValidationInitiator.InitiatedByCampaign)
        .returning(Success((1, Seq(failProspectsWithInvalidEmail))))

      (leadFinderValidationService.updateLeadFinderValidationResults(_: List[ValidationResultWithTeamIdAndAnalysisIdWithoutEmailValidationId])(_: SRLogger))
        .expects(List(), logger)
        .returning(Success(0))

      (emailValidationModel.updateEmailValidationResultsFromBatchRequest)
        .expects(Seq(failEmailValidationResultWithAnalysisId), EmailValidationToolV2.SMARTREACH_INTERNAL)
        .returning(Success(List()))
      val result = emailValidationService.checkAndUpdateIfInvalidBasedOnJavaEmailAddressBatch(List(
        failEmailValidationData, passEmailValidationData
      ))


      assert(result == Success(List(passEmailValidationData)))

    }
  }


  describe("makeBatchRequestWithBouncerApiAndReturnRequestId") {
    it("should fail when error occurs for bouncer while making batch request") {

      val emailto_be_sent_to_bouncer = List(EmailValidationData(12L, "<EMAIL>", TeamId(12L), BOUNCER, EmailValidationInitiator.InitiatedByCampaign))
      val emailsList = Seq("<EMAIL>")
      val batchIdBouncer = "49113"
      val dummyStatus = 401 // Dummy status code
      val dummyJson = Json.obj("error" -> "Dummy error message")


      (bouncerEmailValidationApi.createBatchRequest(_: Seq[String])(_: WSClient, _: ExecutionContext, _: SRLogger))
        .expects(emailsList, wSClient, actorContext, *)
        .returning(Future.failed(new Exception(s"${dummyStatus} :: ${dummyJson}")))


      (emailValidationApiToolsRecordDAO.addErrorRecordWhileCreatingBatchRequest)
        .expects(EmailValidationToolV2.BOUNCER, 401, dummyJson)
        .returning(Success(1))


      val failureresult = emailValidationService.makeBatchRequestWithBouncerApiAndReturnRequestId(emailto_be_sent_to_bouncer)(ws = wSClient, ec = actorContext, srLogger = logger)

      whenReady(failureresult.failed) { exception =>
        assert(exception.getMessage.contains(s"$dummyStatus :: ${dummyJson}"))
      }
    }

  }


  describe("makeBatchRequestWithListcleanApiAndReturnRequestId") {
    it("should fail when error occurs for listclean while making batch request") {


      val emailto_be_sent_to_listclean = List(EmailValidationData(12L, "<EMAIL>", TeamId(12L), EmailValidationToolV2.LISTCLEAN, EmailValidationInitiator.InitiatedByCampaign))
      val emailsList = Seq("<EMAIL>")
      val dummyStatus = 401 // Dummy status code
      val dummyJson = Json.obj("error" -> "Dummy error message")


      (listCleanEmailValidationApi.createBatchRequest(_: Seq[String])(_: WSClient, _: ExecutionContext, _: SRLogger))
        .expects(emailsList, wSClient, actorContext, *)
        .returning(Future.failed(new Exception(s"${dummyStatus} :: ${dummyJson}")))


      (emailValidationApiToolsRecordDAO.addErrorRecordWhileCreatingBatchRequest)
        .expects(EmailValidationToolV2.LISTCLEAN, 401, dummyJson)
        .returning(Success(1))


      val failureresult = emailValidationService.makeBatchRequestWithListCleanApiAndReturnRequestId(emailto_be_sent_to_listclean)(ws = wSClient, ec = actorContext, srLogger = logger)


      whenReady(failureresult.failed) { exception =>
        assert(exception.getMessage.contains(s"$dummyStatus :: ${dummyJson}"))
      }
    }

  }


  describe("getToolServiceStatus") {

    it("should return Processing when GetQueuedRecord returns a queued record for DEBOUNCE") {
      val api_dbc_status_will_be_processing = EmailValidationToolV2.DEBOUNCE
      val minThresholdtime = api_dbc_status_will_be_processing.minTimeToBeConsideredAsDowninMinutes

      val record1 = EmailValidationAPIServiceStatusRecordCheck(id = 1L,
        serviceStatus = EmailValidationToolStatusType.Processing,
        apiTool = EmailValidationToolV2.DEBOUNCE,
        batchRequestId = Some("12345"),
        batchCreatedAt = Some(DateTime.now().minusMinutes(minThresholdtime - 10)),
        availableSinceTime = None,
        errorOccuredAt = None
      )

      (emailValidationApiToolsRecordDAO.fetchAPIServiceStatus)
        .expects(api_dbc_status_will_be_processing)
        .returning(Success(Some(record1)))
      val queuedRecord: DateTime = DateTime.now().minusMinutes(minThresholdtime - 10)


      val result = emailValidationService.getToolServiceStatus(api_dbc_status_will_be_processing)(mockWSClient, mockExecutionContext, logger)
      assert(result == Success(EmailValidationToolStatus.Processing()))
    }

    it("should return Down for DEBOUNCE when the batch request was made 75 minutes before ") {
      val api_dbc_willbe_down = EmailValidationToolV2.DEBOUNCE
      val minThresholdtime = api_dbc_willbe_down.minTimeToBeConsideredAsDowninMinutes
      val record1 = EmailValidationAPIServiceStatusRecordCheck(id = 2L,
        serviceStatus = EmailValidationToolStatusType.Processing,
        batchRequestId = Some("12345"),
        batchCreatedAt = Some(DateTime.now().minusMinutes(minThresholdtime + 1)),
        apiTool = EmailValidationToolV2.DEBOUNCE,
        availableSinceTime = None,
        errorOccuredAt = None
      )

      (emailValidationApiToolsRecordDAO.fetchAPIServiceStatus)
        .expects(api_dbc_willbe_down)
        .returning(Success(Some(record1)))


      (emailValidationApiToolsRecordDAO.updateServiceStatus)
        .expects(*, *, EmailValidationToolStatusType.Down, api_dbc_willbe_down)
        .returning(Success(1))

      val result = emailValidationService.getToolServiceStatus(api_dbc_willbe_down)(mockWSClient, mockExecutionContext, logger)
      assert(result == Success(EmailValidationToolStatus.Down()))
    }

    it("should return Down for DEBOUNCE") {
      val api_dbc_status_will_be_down = EmailValidationToolV2.DEBOUNCE
      val minThresholdtime = api_dbc_status_will_be_down.minTimeToBeConsideredAsDowninMinutes
      val record1 = EmailValidationAPIServiceStatusRecordCheck(id = 3L,
        serviceStatus = EmailValidationToolStatusType.Down,
        apiTool = EmailValidationToolV2.DEBOUNCE,
        batchRequestId = Some("12345"),
        batchCreatedAt = Some(DateTime.now().minusMinutes(minThresholdtime + 10)),
        availableSinceTime = None,
        errorOccuredAt = None
      )
      (emailValidationApiToolsRecordDAO.fetchAPIServiceStatus)
        .expects(api_dbc_status_will_be_down)
        .returning(Success(Some(record1)))


      val result = emailValidationService.getToolServiceStatus(api_dbc_status_will_be_down)(mockWSClient, mockExecutionContext, logger)
      assert(result == Success(EmailValidationToolStatus.Down()))
    }

    it("should return Available for DEBOUNCE") {
      val api_dbc_willbe_available = EmailValidationToolV2.DEBOUNCE


      val record1 = EmailValidationAPIServiceStatusRecordCheck(id = 4L,
        serviceStatus = EmailValidationToolStatusType.Available,
        apiTool = EmailValidationToolV2.DEBOUNCE,
        batchRequestId = Some("12345"),
        batchCreatedAt = Some(DateTime.now().minusMinutes(29)),
        availableSinceTime = Some(DateTime.now().minusHours(2)),
        errorOccuredAt = None
      )
      (emailValidationApiToolsRecordDAO.fetchAPIServiceStatus)
        .expects(api_dbc_willbe_available)
        .returning(Success(Some(record1)))


      val result = emailValidationService.getToolServiceStatus(api_dbc_willbe_available)(mockWSClient, mockExecutionContext, logger)
      assert(result.isSuccess)
    }


    it("should return Available when no record is found for the DEBOUNCE") {
      val api_tool_dbc_record_not_found = EmailValidationToolV2.DEBOUNCE


      (emailValidationApiToolsRecordDAO.fetchAPIServiceStatus)
        .expects(api_tool_dbc_record_not_found)
        .returning(Success(None))

      val dbc_available_with_currenttime = emailValidationService.getToolServiceStatus(api_tool_dbc_record_not_found)(mockWSClient, mockExecutionContext, logger)
      assert(dbc_available_with_currenttime.isSuccess)
      assert(dbc_available_with_currenttime.get.status == EmailValidationToolStatusType.Available)


    }

    it("should fail when emailValidationApiToolsRecordDAO.fetchAPIServiceStatus function is failed for DEBOUNCE") {
      val api_dbc = EmailValidationToolV2.DEBOUNCE

      (emailValidationApiToolsRecordDAO.fetchAPIServiceStatus)
        .expects(api_dbc)
        .returning(Failure(new Exception("Some error occured while fetching")))


      val result = emailValidationService.getToolServiceStatus(api_dbc)(mockWSClient, mockExecutionContext, logger)
      assert(result.isFailure)
    }


    describe("checkStatusAndAssignToolForEmailValidation") {
      it("should assign emails to LISTCLEAN when BOUNCER is down and LISTCLEAN is available") {
        val emailList: List[String] = List.fill(6000)("<EMAIL>")


        val validationTool_1_bcr_will_be_down = EmailValidationToolV2.BOUNCER
        val createdSince = EmailValidationToolV2.BOUNCER.minTimeToBeConsideredAsDowninMinutes
        //by setting the queued time to 2 hours before - this tool BCR will be down
        val downRecord = EmailValidationAPIServiceStatusRecordCheck(id = 5L,
          serviceStatus = EmailValidationToolStatusType.Down,
          apiTool = EmailValidationToolV2.BOUNCER,
          batchRequestId = Some("12345"),
          batchCreatedAt = Some(DateTime.now().minusMinutes(createdSince + 10)),
          availableSinceTime = None,
          errorOccuredAt = None
        )

        (emailValidationDAOService.changingValidationStatusTo_ToBeQueued(_: EmailValidationToolV2)(using _: SRLogger))
          .expects(BOUNCER, *)
          .returning(Success(90))

        (emailValidationDAOService.switchingValidationTooltoBackupTool(_: EmailValidationToolV2, _: EmailValidationToolV2)(using _: SRLogger))
          .expects(EmailValidationToolV2.BOUNCER, EmailValidationToolV2.DEBOUNCE, *)
          .returning(Success(90))


        (emailValidationApiToolsRecordDAO.fetchAPIServiceStatus)
          .expects(validationTool_1_bcr_will_be_down)
          .returning(Success(Some(downRecord)))


        val validationTool_debounce_will_be_available = EmailValidationToolV2.DEBOUNCE
        //by setting the completed time to 2 hours before - this tool ListClean will be available from last 2 hrs
        val completedRecordTIme_for_debounce = DateTime.now().minusHours(2)

        val record1 = EmailValidationAPIServiceStatusRecordCheck(id = 6L,
          serviceStatus = EmailValidationToolStatusType.Available,
          apiTool = EmailValidationToolV2.DEBOUNCE,
          batchRequestId = Some("12345"),
          batchCreatedAt = Some(DateTime.now().minusMinutes(15)),
          availableSinceTime = Some(DateTime.now().minusHours(2)),
          errorOccuredAt = None
        )
        (emailValidationApiToolsRecordDAO.fetchAPIServiceStatus)
          .expects(validationTool_debounce_will_be_available)
          .returning(Success(Some(record1)))


        (emailValidationDAOService.assigningToolForValidation(_: List[String], _: EmailValidationToolV2)(using _: SRLogger))
          .expects(emailList, validationTool_debounce_will_be_available, *)
          .returning(Success(emailList.size))


        val result3 = emailValidationService.checkStatusAndAssignToolForEmailValidation(emailList,
          EmailValidationToolV2.BOUNCER)(mockWSClient, mockExecutionContext, logger)
        assert(result3 == Success(EmailValidationToolAssignedRecord(EmailValidationToolV2.DEBOUNCE, emailList.size)))
      }

      it("should assign emails to BOUNCER when BOUNCER is available ") {
        val emailList: List[String] = List.fill(6000)("<EMAIL>")


        val validationTool_bcr_will_be_available = EmailValidationToolV2.BOUNCER
        //by setting the completed time to 2 hours before - this tool BCR will be available from last 2 hrs
        val batchCreatedTime = DateTime.now().minusMinutes(BOUNCER.minTimeToBeConsideredAsDowninMinutes - 5)

        val record1 = EmailValidationAPIServiceStatusRecordCheck(id = 3L,
          serviceStatus = EmailValidationToolStatusType.Available,
          apiTool = EmailValidationToolV2.BOUNCER,
          batchRequestId = Some("12345"),
          batchCreatedAt = Some(batchCreatedTime),
          availableSinceTime = Some(DateTime.now().minusHours(2)),
          errorOccuredAt = None
        )


        (emailValidationApiToolsRecordDAO.fetchAPIServiceStatus)
          .expects(validationTool_bcr_will_be_available)
          .returning(Success(Some(record1)))


        (emailValidationDAOService.assigningToolForValidation(_: List[String], _: EmailValidationToolV2)(using _: SRLogger))
          .expects(emailList, validationTool_bcr_will_be_available, *)
          .returning(Success(emailList.size))


        val result = emailValidationService.checkStatusAndAssignToolForEmailValidation(emailList, EmailValidationToolV2.BOUNCER)(mockWSClient, mockExecutionContext, logger)
        assert(result == Success(EmailValidationToolAssignedRecord(EmailValidationToolV2.BOUNCER, emailList.size)))
      }
      it("should return Failure when BOUNCER and all backupTools are down") {

        val emailList: List[String] = List.fill(6000)("<EMAIL>")
        val batchCreatedTimeForBouncer = DateTime.now().minusMinutes(BOUNCER.minTimeToBeConsideredAsDowninMinutes + 10)
        val validationTool_bcr_is_down = EmailValidationToolV2.BOUNCER
        val downRecord_bcr = EmailValidationAPIServiceStatusRecordCheck(id = 3L,
          serviceStatus = EmailValidationToolStatusType.Down,
          batchRequestId = Some("12345"),
          batchCreatedAt = Some(batchCreatedTimeForBouncer),
          apiTool = EmailValidationToolV2.BOUNCER,
          availableSinceTime = None,
          errorOccuredAt = None
        )


        //    (emailValidationDAOService.changingValidationStatusTo_ToBeQueued (_: EmailValidationToolV2)(_: SRLogger))
        //     .expects(EmailValidationToolV2.BOUNCER, *)
        //     .returning(Success(90))


        (emailValidationApiToolsRecordDAO.fetchAPIServiceStatus)
          .expects(validationTool_bcr_is_down)
          .returning(Success(Some(downRecord_bcr)))

//        val validationTool_listclean_will_be_down = EmailValidationToolV2.LISTCLEAN
//        //by setting the queued time to 2 hours before - this tool listclean will be down
//
//        val batchCreatedTimeForListClean: DateTime = DateTime.now().minusHours(2)
//
//        val downRecordlistclean = EmailValidationAPIServiceStatusRecordCheck(id = 3L,
//          serviceStatus = EmailValidationToolStatusType.Down,
//          apiTool = EmailValidationToolV2.LISTCLEAN,
//          batchRequestId = Some("12345"),
//          batchCreatedAt = Some(batchCreatedTimeForListClean),
//          availableSinceTime = None,
//          errorOccuredAt = None
//        )
//        (emailValidationApiToolsRecordDAO.fetchAPIServiceStatus)
//          .expects(validationTool_listclean_will_be_down)
//          .returning(Success(Some(downRecordlistclean)))

        val validationTool_dbc_is_down = EmailValidationToolV2.DEBOUNCE
        //by setting the queued time to 2 hours before - this tool DBC will be down

        val batchCreatedForDebounce = DateTime.now().minusMinutes(DEBOUNCE.minTimeToBeConsideredAsDowninMinutes + 10)
        val downRecorddbc = EmailValidationAPIServiceStatusRecordCheck(id = 3L,
          serviceStatus = EmailValidationToolStatusType.Down,
          apiTool = EmailValidationToolV2.DEBOUNCE,
          batchRequestId = Some("2345"),
          batchCreatedAt = Some(batchCreatedForDebounce),
          availableSinceTime = None,
          errorOccuredAt = None
        )

        (emailValidationApiToolsRecordDAO.fetchAPIServiceStatus)
          .expects(validationTool_dbc_is_down)
          .returning(Success(Some(downRecorddbc)))


        val result = emailValidationService.checkStatusAndAssignToolForEmailValidation(emailList, EmailValidationToolV2.BOUNCER)(mockWSClient, mockExecutionContext, logger)


        assert(result.isFailure)
      }


      it("should return 0 when BOUNCER is processing") {

        val emailList: List[String] = List.fill(6000)("<EMAIL>")
        val validationTool_bcr_is_busy = EmailValidationToolV2.BOUNCER
        //by setting the queued time to now - this tool BCR will be processing

        val processingRecord = EmailValidationAPIServiceStatusRecordCheck(id = 3L,
          serviceStatus = EmailValidationToolStatusType.Processing,
          apiTool = EmailValidationToolV2.BOUNCER,
          batchRequestId = Some("12345"),
          batchCreatedAt = Some(DateTime.now().minusMinutes(1)),
          availableSinceTime = None,
          errorOccuredAt = None
        )

        val queuedRecordTime_for_bcr = DateTime.now()

        (emailValidationApiToolsRecordDAO.fetchAPIServiceStatus)
          .expects(validationTool_bcr_is_busy)
          .returning(Success(Some(processingRecord)))


        val result = emailValidationService.checkStatusAndAssignToolForEmailValidation(emailList, EmailValidationToolV2.BOUNCER)(mockWSClient, mockExecutionContext, logger)


        assert(result == Success(EmailValidationToolAssignedRecord(EmailValidationToolV2.BOUNCER, 0)))
      }


      it("should return failure when BOUNCER is down and there is a failure while checking status  for LISTCLEAN") {
        val emailList: List[String] = List.fill(6000)("<EMAIL>")
        val validationTool_bcr_is_down = EmailValidationToolV2.BOUNCER
        //by setting the queued time to 2 hours before - this tool BCR will be down


        //    (emailValidationDAOService.changingValidationStatusTo_ToBeQueued (_: EmailValidationToolV2)(_: SRLogger))
        //     .expects(EmailValidationToolV2.BOUNCER, *)
        //     .returning(Success(90))
        val downRecord_bcr = EmailValidationAPIServiceStatusRecordCheck(id = 3L,
          serviceStatus = EmailValidationToolStatusType.Down,
          apiTool = EmailValidationToolV2.BOUNCER,
          batchRequestId = Some("32456"),
          batchCreatedAt = Some(DateTime.now().minusMinutes(100)),
          availableSinceTime = None,
          errorOccuredAt = None
        )
        (emailValidationApiToolsRecordDAO.fetchAPIServiceStatus)
          .expects(validationTool_bcr_is_down)
          .returning(Success(Some(downRecord_bcr)))


        (emailValidationApiToolsRecordDAO.fetchAPIServiceStatus)
          .expects(EmailValidationToolV2.DEBOUNCE)
          .returning(Failure(new Exception("error while checking status of listclean")))


        val result = emailValidationService.checkStatusAndAssignToolForEmailValidation(emailList, EmailValidationToolV2.BOUNCER)(mockWSClient, mockExecutionContext, logger)


        assert(result.isFailure)
      }


      it("should return 0 when BOUNCER is down and LISTCLEAN is processing") {

        val emailList: List[String] = List.fill(6000)("<EMAIL>")
        val validationToolV2_bcr_is_down = EmailValidationToolV2.BOUNCER
        //by setting the queued time to 2 hours before - this tool BCR will be down


        //    (emailValidationDAOService.changingValidationStatusTo_ToBeQueued (_: EmailValidationToolV2)(_: SRLogger))
        //     .expects(EmailValidationToolV2.BOUNCER, *)
        //     .returning(Success(90))
        val downRecord_bcr = EmailValidationAPIServiceStatusRecordCheck(id = 3L,
          serviceStatus = EmailValidationToolStatusType.Down,
          apiTool = EmailValidationToolV2.BOUNCER,
          batchRequestId = Some("6347"),
          batchCreatedAt = Some(DateTime.now().minusMinutes(30)),
          availableSinceTime = None,
          errorOccuredAt = None
        )
        (emailValidationApiToolsRecordDAO.fetchAPIServiceStatus)
          .expects(validationToolV2_bcr_is_down)
          .returning(Success(Some(downRecord_bcr)))

        val validationToolV2_debounce_is_processing = EmailValidationToolV2.DEBOUNCE
        //by setting the queued time to currenttime - this tool BCR will be processing


        val processingRecord_debounce = EmailValidationAPIServiceStatusRecordCheck(id = 3L,
          serviceStatus = EmailValidationToolStatusType.Processing,
          apiTool = EmailValidationToolV2.DEBOUNCE,
          batchRequestId = Some("5452"),
          batchCreatedAt = Some(DateTime.now()),

          availableSinceTime = None,
          errorOccuredAt = None
        )

        (emailValidationApiToolsRecordDAO.fetchAPIServiceStatus)
          .expects(validationToolV2_debounce_is_processing)
          .returning(Success(Some(processingRecord_debounce)))


        val result = emailValidationService.checkStatusAndAssignToolForEmailValidation(emailList, EmailValidationToolV2.BOUNCER)(mockWSClient, mockExecutionContext, logger)

        assert(result == Success(EmailValidationToolAssignedRecord(EmailValidationToolV2.DEBOUNCE, 0)))
      }


      it("should return 0 if BOUNCER is Down and emaillist is below threshold limit for LISTCLEAN is available") {
        val emailList: List[String] = List.fill(15)("<EMAIL>")


        val validationToolV2_bcr_is_down = EmailValidationToolV2.BOUNCER
        //by setting the queued time to 2 hours before - this tool BCR will be down

        val queuedRecord_time_for_bcr: DateTime = DateTime.now().minusHours(2)
        val downRecord_bcr = EmailValidationAPIServiceStatusRecordCheck(id = 3L,
          serviceStatus = EmailValidationToolStatusType.Down,
          apiTool = EmailValidationToolV2.BOUNCER,
          batchRequestId = Some("12345"),
          batchCreatedAt = Some(DateTime.now().minusHours(2)),
          availableSinceTime = None,
          errorOccuredAt = None
        )
        (emailValidationDAOService.changingValidationStatusTo_ToBeQueued(_: EmailValidationToolV2)(using _: SRLogger))
          .expects(BOUNCER, *)
          .returning(Success(90))

        (emailValidationDAOService.switchingValidationTooltoBackupTool(_: EmailValidationToolV2, _: EmailValidationToolV2)(using _: SRLogger))
          .expects(EmailValidationToolV2.BOUNCER, EmailValidationToolV2.DEBOUNCE, *)
          .returning(Success(90))


        (emailValidationApiToolsRecordDAO.fetchAPIServiceStatus)
          .expects(validationToolV2_bcr_is_down)
          .returning(Success(Some(downRecord_bcr)))


        val validationToolV2_debounce_is_available = EmailValidationToolV2.DEBOUNCE
        //by setting the completed time to 2 minutes before - this tool Listclean will be available from last 2 minutes
        val upRecord_lstclean = EmailValidationAPIServiceStatusRecordCheck(id = 3L,
          serviceStatus = EmailValidationToolStatusType.Available,
          apiTool = EmailValidationToolV2.DEBOUNCE,
          batchRequestId = Some("12345"),
          batchCreatedAt = Some(DateTime.now().minusMinutes(23)),
          availableSinceTime = Some(DateTime.now().minusMinutes(2)),
          errorOccuredAt = None
        )

        (emailValidationApiToolsRecordDAO.fetchAPIServiceStatus)
          .expects(validationToolV2_debounce_is_available)
          .returning(Success(Some(upRecord_lstclean)))


        val result3 = emailValidationService.checkStatusAndAssignToolForEmailValidation(emailList, EmailValidationToolV2.BOUNCER)(mockWSClient, mockExecutionContext, logger)
        assert(result3 == Success(EmailValidationToolAssignedRecord(EmailValidationToolV2.DEBOUNCE, 0)))
      }


      it("should return a failure if getStatus failed for BOUNCER") {
        val emailList: List[String] = List.fill(6)("<EMAIL>")
        (emailValidationApiToolsRecordDAO.fetchAPIServiceStatus)
          .expects(EmailValidationToolV2.BOUNCER)
          .returning(Failure(new Exception("Some error message")))

        val result = emailValidationService.checkStatusAndAssignToolForEmailValidation(emailList, EmailValidationToolV2.BOUNCER)(mockWSClient, mockExecutionContext, logger)
        assert(result.isFailure)
      }


      it("should assign emails to LISTCLEAN when DEBOUNCE is down and LISTCLEAN is available") {
        val emailList: List[String] = List.fill(6000)("<EMAIL>")


        val validationToolV2_dbc_is_down = EmailValidationToolV2.DEBOUNCE

        val downRecord_dbc = EmailValidationAPIServiceStatusRecordCheck(id = 3L,
          serviceStatus = EmailValidationToolStatusType.Down,
          apiTool = EmailValidationToolV2.DEBOUNCE,
          batchRequestId = Some("98789"),
          batchCreatedAt = Some(DateTime.now().minusMinutes(80)),
          availableSinceTime = None,
          errorOccuredAt = None
        )

        (emailValidationDAOService.changingValidationStatusTo_ToBeQueued(_: EmailValidationToolV2)(using _: SRLogger))
          .expects(validationToolV2_dbc_is_down, *)
          .returning(Success(90))

        (emailValidationDAOService.switchingValidationTooltoBackupTool(_: EmailValidationToolV2, _: EmailValidationToolV2)(using _: SRLogger))
          .expects(EmailValidationToolV2.DEBOUNCE, EmailValidationToolV2.BOUNCER, *)
          .returning(Success(90))


        (emailValidationApiToolsRecordDAO.fetchAPIServiceStatus)
          .expects(validationToolV2_dbc_is_down)
          .returning(Success(Some(downRecord_dbc)))


        val validationToolV2_listclean_is_available = EmailValidationToolV2.BOUNCER
        //by setting the completed time to 2 hours before - this tool listclean will be available form last 2hrs

        val availableRecord_lstclean = EmailValidationAPIServiceStatusRecordCheck(id = 3L,
          serviceStatus = EmailValidationToolStatusType.Available,
          apiTool = EmailValidationToolV2.BOUNCER,
          batchRequestId = Some("45361"),
          batchCreatedAt = Some(DateTime.now().minusMinutes(34)),
          availableSinceTime = Some(DateTime.now().minusHours(2)),
          errorOccuredAt = None
        )

        val completedRecordTime_for_listclean = DateTime.now().minusHours(2)
        (emailValidationApiToolsRecordDAO.fetchAPIServiceStatus)
          .expects(validationToolV2_listclean_is_available)
          .returning(Success(Some(availableRecord_lstclean)))


        (emailValidationDAOService.assigningToolForValidation(_: List[String], _: EmailValidationToolV2)(using _: SRLogger))
          .expects(emailList, validationToolV2_listclean_is_available, *)
          .returning(Success(emailList.size))


        val result3 = emailValidationService.checkStatusAndAssignToolForEmailValidation(emailList,
          EmailValidationToolV2.DEBOUNCE)(mockWSClient, mockExecutionContext, logger)
        assert(result3 == Success(EmailValidationToolAssignedRecord(EmailValidationToolV2.BOUNCER, emailList.size)))
      }


      it("should assign emails to DEBOUNCE when LISTCLEAN is down and DEBOUNCE is available") {
        val emailList: List[String] = List.fill(6000)("<EMAIL>")


        val validationToolV2_listclean_is_down = EmailValidationToolV2.LISTCLEAN

        val downRecord_listclean = EmailValidationAPIServiceStatusRecordCheck(id = 3L,
          serviceStatus = EmailValidationToolStatusType.Down,
          apiTool = EmailValidationToolV2.LISTCLEAN,
          batchRequestId = Some("342527"),
          batchCreatedAt = Some(DateTime.now().minusMinutes(89)),
          availableSinceTime = None,
          errorOccuredAt = None
        )

        (emailValidationDAOService.changingValidationStatusTo_ToBeQueued(_: EmailValidationToolV2)(using _: SRLogger))
          .expects(validationToolV2_listclean_is_down, *)
          .returning(Success(90))

        (emailValidationDAOService.switchingValidationTooltoBackupTool(_: EmailValidationToolV2, _: EmailValidationToolV2)(using _: SRLogger))
          .expects(EmailValidationToolV2.LISTCLEAN, EmailValidationToolV2.DEBOUNCE, *)
          .returning(Success(90))


        (emailValidationApiToolsRecordDAO.fetchAPIServiceStatus)
          .expects(validationToolV2_listclean_is_down)
          .returning(Success(Some(downRecord_listclean)))


        val validationToolV2_dbc_is_available = EmailValidationToolV2.DEBOUNCE
        //by setting the completed time to 2 hours before - this tool dbc will be available from last 2 hrs

        val availableRecord_dbc = EmailValidationAPIServiceStatusRecordCheck(id = 3L,
          serviceStatus = EmailValidationToolStatusType.Available,
          apiTool = EmailValidationToolV2.DEBOUNCE,
          batchRequestId = Some("2786"),
          batchCreatedAt = Some(DateTime.now().minusMinutes(130)),
          availableSinceTime = Some(DateTime.now().minusHours(2)),
          errorOccuredAt = None
        )
        (emailValidationApiToolsRecordDAO.fetchAPIServiceStatus)
          .expects(validationToolV2_dbc_is_available)
          .returning(Success(Some(availableRecord_dbc)))


        (emailValidationDAOService.assigningToolForValidation(_: List[String], _: EmailValidationToolV2)(using _: SRLogger))
          .expects(emailList, validationToolV2_dbc_is_available, *)
          .returning(Success(emailList.size))


        val result3 = emailValidationService.checkStatusAndAssignToolForEmailValidation(emailList,
          EmailValidationToolV2.LISTCLEAN)(mockWSClient, mockExecutionContext, logger)
        assert(result3 == Success(EmailValidationToolAssignedRecord(EmailValidationToolV2.DEBOUNCE, emailList.size)))
      }


      it("should assign BOUNCER if DEBOUNCE and LISTCLEAN is Down") {
        val emailList: List[String] = List.fill(25)("<EMAIL>")
        val validationTool_dbc_is_down = EmailValidationToolV2.DEBOUNCE

        val downRecord_dbc = EmailValidationAPIServiceStatusRecordCheck(id = 3L,
          serviceStatus = EmailValidationToolStatusType.Down,
          apiTool = EmailValidationToolV2.DEBOUNCE,
          batchRequestId = Some("46578"),
          batchCreatedAt = Some(DateTime.now().minusMinutes(34)),
          availableSinceTime = None,
          errorOccuredAt = None
        )


        (emailValidationDAOService.changingValidationStatusTo_ToBeQueued(_: EmailValidationToolV2)(using _: SRLogger))
          .expects(validationTool_dbc_is_down, *)
          .returning(Success(90))

        (emailValidationDAOService.switchingValidationTooltoBackupTool(_: EmailValidationToolV2, _: EmailValidationToolV2)(using _: SRLogger))
          .expects(EmailValidationToolV2.DEBOUNCE, EmailValidationToolV2.BOUNCER, *)
          .returning(Success(90))

        (emailValidationApiToolsRecordDAO.fetchAPIServiceStatus)
          .expects(validationTool_dbc_is_down)
          .returning(Success(Some(downRecord_dbc)))

//        val validationToolV2_listclean_is_down = EmailValidationToolV2.LISTCLEAN
//
//        val downRecord_listclean = EmailValidationAPIServiceStatusRecordCheck(id = 3L,
//          serviceStatus = EmailValidationToolStatusType.Down,
//          apiTool = EmailValidationToolV2.LISTCLEAN,
//          batchRequestId = Some("18972"),
//          batchCreatedAt = Some(DateTime.now().minusMinutes(89)),
//          availableSinceTime = None,
//          errorOccuredAt = None
//        )
//
//        (emailValidationApiToolsRecordDAO.fetchAPIServiceStatus)
//          .expects(validationToolV2_listclean_is_down)
//          .returning(Success(Some(downRecord_listclean)))



        //by setting the completed time to 2 hours before - this tool bcr will be available

        val validationTool_bcr_is_available = EmailValidationToolV2.BOUNCER
        val availableRecord_bcr = EmailValidationAPIServiceStatusRecordCheck(id = 3L,
          serviceStatus = EmailValidationToolStatusType.Available,
          apiTool = EmailValidationToolV2.BOUNCER,
          batchRequestId = Some("5672"),
          batchCreatedAt = Some(DateTime.now().minusMinutes(134)),
          availableSinceTime = Some(DateTime.now().minusHours(2)),
          errorOccuredAt = None
        )
        (emailValidationApiToolsRecordDAO.fetchAPIServiceStatus)
          .expects(validationTool_bcr_is_available)
          .returning(Success(Some(availableRecord_bcr)))


        (emailValidationDAOService.assigningToolForValidation(_: List[String], _: EmailValidationToolV2)(using _: SRLogger))
          .expects(emailList, validationTool_bcr_is_available, *)
          .returning(Success(emailList.size))


        val result = emailValidationService.checkStatusAndAssignToolForEmailValidation(emailList, EmailValidationToolV2.DEBOUNCE)(mockWSClient, mockExecutionContext, logger)

        assert(result == Success(EmailValidationToolAssignedRecord(EmailValidationToolV2.BOUNCER, emailList.size)))
      }


      it("should assign BOUNCER if LISTCLEAN and DEBOUNCE is Down") {
        val emailList: List[String] = List.fill(25)("<EMAIL>")
        val validationTool_listclean_is_down = EmailValidationToolV2.LISTCLEAN

        val downRecord_listclean = EmailValidationAPIServiceStatusRecordCheck(id = 3L,
          serviceStatus = EmailValidationToolStatusType.Down,
          apiTool = EmailValidationToolV2.LISTCLEAN,
          batchRequestId = Some("6753"),
          batchCreatedAt = Some(DateTime.now().minusMinutes(50)),
          availableSinceTime = None,
          errorOccuredAt = None
        )


        (emailValidationDAOService.changingValidationStatusTo_ToBeQueued(_: EmailValidationToolV2)(using _: SRLogger))
          .expects(validationTool_listclean_is_down, *)
          .returning(Success(90))

        (emailValidationDAOService.switchingValidationTooltoBackupTool(_: EmailValidationToolV2, _: EmailValidationToolV2)(using _: SRLogger))
          .expects(EmailValidationToolV2.LISTCLEAN, EmailValidationToolV2.BOUNCER, *)
          .returning(Success(90))

        (emailValidationApiToolsRecordDAO.fetchAPIServiceStatus)
          .expects(validationTool_listclean_is_down)
          .returning(Success(Some(downRecord_listclean)))

        val validationToolV2_dbc_is_down = EmailValidationToolV2.DEBOUNCE
        //by setting the queued time to 2 hours before - this tool dbc will be down

        val downRecord_dbc = EmailValidationAPIServiceStatusRecordCheck(id = 3L,
          serviceStatus = EmailValidationToolStatusType.Down,
          apiTool = EmailValidationToolV2.DEBOUNCE,
          batchRequestId = Some("89762"),
          batchCreatedAt = Some(DateTime.now().minusMinutes(97)),
          availableSinceTime = None,
          errorOccuredAt = None
        )

        (emailValidationApiToolsRecordDAO.fetchAPIServiceStatus)
          .expects(validationToolV2_dbc_is_down)
          .returning(Success(Some(downRecord_dbc)))


        val completedRecordTIme_for_bcr = DateTime.now().minusHours(2)
        //by setting the completed time to 2 hours before - this tool bcr will be available since last 2 hrs.

        val availableRecord_bcr = EmailValidationAPIServiceStatusRecordCheck(id = 3L,
          serviceStatus = EmailValidationToolStatusType.Available,
          apiTool = EmailValidationToolV2.BOUNCER,
          batchRequestId = None,
          batchCreatedAt = None,
          availableSinceTime = None,
          errorOccuredAt = None
        )

        val validationTool_bcr_is_available = EmailValidationToolV2.BOUNCER
        (emailValidationApiToolsRecordDAO.fetchAPIServiceStatus)
          .expects(validationTool_bcr_is_available)
          .returning(Success(Some(availableRecord_bcr)))


        (emailValidationDAOService.assigningToolForValidation(_: List[String], _: EmailValidationToolV2)(using _: SRLogger))
          .expects(emailList, validationTool_bcr_is_available, *)
          .returning(Success(emailList.size))


        val result = emailValidationService.checkStatusAndAssignToolForEmailValidation(emailList, EmailValidationToolV2.LISTCLEAN)(mockWSClient, mockExecutionContext, logger)

        assert(result == Success(EmailValidationToolAssignedRecord(EmailValidationToolV2.BOUNCER, emailList.size)))
      }


      it("should assign emails to debounce when listclean status is Error") {
        val emailList: List[String] = List.fill(25)("<EMAIL>")
        val validationTool_listclean_has_error = EmailValidationToolV2.LISTCLEAN


        val errorRecord_listclean = EmailValidationAPIServiceStatusRecordCheck(id = 3L,
          serviceStatus = EmailValidationToolStatusType.Error,
          apiTool = EmailValidationToolV2.LISTCLEAN,
          batchRequestId = None,
          batchCreatedAt = None,
          availableSinceTime = None,
          errorOccuredAt = Some(DateTime.now().minusMinutes(12))
        )

        (emailValidationDAOService.changingValidationStatusTo_ToBeQueued(_: EmailValidationToolV2)(using _: SRLogger))
          .expects(validationTool_listclean_has_error, *)
          .returning(Success(90))

        (emailValidationDAOService.switchingValidationTooltoBackupTool(_: EmailValidationToolV2, _: EmailValidationToolV2)(using _: SRLogger))
          .expects(EmailValidationToolV2.LISTCLEAN, EmailValidationToolV2.DEBOUNCE, *)
          .returning(Success(90))

        (emailValidationDAOService.switchingValidationTooltoBackupTool(_: EmailValidationToolV2, _: EmailValidationToolV2)(using _: SRLogger))
          .expects(EmailValidationToolV2.LISTCLEAN, EmailValidationToolV2.DEBOUNCE, *)
          .returning(Success(90))


        (emailValidationApiToolsRecordDAO.fetchAPIServiceStatus)
          .expects(validationTool_listclean_has_error)
          .returning(Success(Some(errorRecord_listclean)))

        val validationToolV2_dbc_is_available = EmailValidationToolV2.DEBOUNCE
        //by setting the queued time to 2 hours before - this tool dbc will be down
        val availableRecord_dbc = EmailValidationAPIServiceStatusRecordCheck(id = 3L,
          serviceStatus = EmailValidationToolStatusType.Available,
          apiTool = EmailValidationToolV2.DEBOUNCE,
          batchRequestId = Some("87637"),
          batchCreatedAt = Some(DateTime.now().minusHours(3)),
          availableSinceTime = Some(DateTime.now().minusHours(2)),
          errorOccuredAt = None
        )
        (emailValidationApiToolsRecordDAO.fetchAPIServiceStatus)
          .expects(validationToolV2_dbc_is_available)
          .returning(Success(Some(availableRecord_dbc)))


        val completedRecordTIme_for_dbc = DateTime.now().minusHours(2)
        //by setting the completed time to 2 hours before - this tool bcr will be available since last 2 hrs.


        (emailValidationDAOService.assigningToolForValidation(_: List[String], _: EmailValidationToolV2)(using _: SRLogger))
          .expects(emailList, validationToolV2_dbc_is_available, *)
          .returning(Success(emailList.size))


        val result = emailValidationService.checkStatusAndAssignToolForEmailValidation(emailList, EmailValidationToolV2.LISTCLEAN)(mockWSClient, mockExecutionContext, logger)

        assert(result == Success(EmailValidationToolAssignedRecord(EmailValidationToolV2.DEBOUNCE, emailList.size)))


      }

      it("should assign emails to bouncer when debounce is down and listclean has error status") {

        val emailList: List[String] = List.fill(25)("<EMAIL>")
        val api_dbc_willbe_down = EmailValidationToolV2.DEBOUNCE

        val downRecord_dbc = EmailValidationAPIServiceStatusRecordCheck(id = 3L,
          serviceStatus = EmailValidationToolStatusType.Down,
          apiTool = EmailValidationToolV2.DEBOUNCE,
          batchRequestId = Some("17628"),
          batchCreatedAt = Some(DateTime.now().minusHours(2)),
          availableSinceTime = None,
          errorOccuredAt = None
        )


        (emailValidationApiToolsRecordDAO.fetchAPIServiceStatus)
          .expects(api_dbc_willbe_down)
          .returning(Success(Some(downRecord_dbc)))


//        val validationTool_listclean_has_error = EmailValidationToolV2.LISTCLEAN
//
//        val errorRecord_listclean = EmailValidationAPIServiceStatusRecordCheck(id = 3L,
//          serviceStatus = EmailValidationToolStatusType.Error,
//          apiTool = EmailValidationToolV2.LISTCLEAN,
//          batchRequestId = None,
//          batchCreatedAt = None,
//          availableSinceTime = None,
//          errorOccuredAt = Some(DateTime.now().minusMinutes(23))
//        )


        (emailValidationDAOService.changingValidationStatusTo_ToBeQueued(_: EmailValidationToolV2)(using _: SRLogger))
          .expects(api_dbc_willbe_down, *)
          .returning(Success(90))

        (emailValidationDAOService.switchingValidationTooltoBackupTool(_: EmailValidationToolV2, _: EmailValidationToolV2)(using _: SRLogger))
          .expects(EmailValidationToolV2.DEBOUNCE, EmailValidationToolV2.BOUNCER, *)
          .returning(Success(90))


//        (emailValidationApiToolsRecordDAO.fetchAPIServiceStatus)
//          .expects(validationTool_listclean_has_error)
//          .returning(Success(Some(errorRecord_listclean)))

        val validationToolV2_bcr_is_available = EmailValidationToolV2.BOUNCER
        //by setting the queued time to 2 hours before - this tool dbc will be down
        val availableRecord_bcr = EmailValidationAPIServiceStatusRecordCheck(id = 3L,
          serviceStatus = EmailValidationToolStatusType.Available,
          apiTool = EmailValidationToolV2.BOUNCER,
          batchRequestId = None,
          batchCreatedAt = None,
          availableSinceTime = None,
          errorOccuredAt = None
        )

        (emailValidationApiToolsRecordDAO.fetchAPIServiceStatus)
          .expects(validationToolV2_bcr_is_available)
          .returning(Success(Some(availableRecord_bcr)))


        val completedRecordTIme_for_bcr = DateTime.now().minusHours(2)
        //by setting the completed time to 2 hours before - this tool bcr will be available since last 2 hrs.



        (emailValidationDAOService.assigningToolForValidation(_: List[String], _: EmailValidationToolV2)(using _: SRLogger))
          .expects(emailList, validationToolV2_bcr_is_available, *)
          .returning(Success(emailList.size))


        val result = emailValidationService.checkStatusAndAssignToolForEmailValidation(emailList, EmailValidationToolV2.DEBOUNCE)(mockWSClient, mockExecutionContext, logger)

        assert(result == Success(EmailValidationToolAssignedRecord(EmailValidationToolV2.BOUNCER, emailList.size)))

      }

    }

    describe("findAndMakeBatchRequest") {

      it("should pass when listclean and bouncer has no error ") {


        val emptyListFordbc: Seq[CheckStatusBatchRequestData] = Seq()
        val emailValidationDataList: List[EmailValidationData] =
          List(EmailValidationData(12L, "<EMAIL>", TeamId(12L), BOUNCER, EmailValidationInitiator.InitiatedByCampaign),
            //EmailValidationData(13L,"<EMAIL>",TeamId(13L),EmailValidationToolV2.DEBOUNCE),
            EmailValidationData(14L, "<EMAIL>", TeamId(14L), EmailValidationToolV2.LISTCLEAN, EmailValidationInitiator.InitiatedByCampaign))
        val debouncedontHaveQueuedRequest = true

        (emailValidationBatchRequestModel.findQueuedBatchRequests)
          .expects(EmailValidationToolV2.DEBOUNCE)
          .returning(Success(emptyListFordbc))

        (emailValidationModel.findEmailsForMakingBatchRequest)
          .expects(debouncedontHaveQueuedRequest)
          .returning(Success(emailValidationDataList))


        val val1 = Seq(ProspectEmailValidationResultWithTeamIdAndAnalysisId(
          emailDeliveryAnalysisId = None,
          teamId = TeamId(0L),
          email = "<EMAIL>",
          isValid = false,
          emailValidationId = 12L,
          validationInitiator = EmailValidationInitiator.InitiatedByCampaign,
        ))

        val prospectsWithInvalidEmail: Seq[ProspectsWithInvalidEmail] = Seq()


        (prospectServiceV2.updateEmailValidationDataV2(_: Seq[ProspectEmailValidationResultWithTeamIdAndAnalysisId], _: SRLogger, _: EmailValidationInitiatorType))
          .expects(*, logger, EmailValidationInitiator.InitiatedByCampaign)
          .returning(Success(1, prospectsWithInvalidEmail))

        (leadFinderValidationService.updateLeadFinderValidationResults(_: List[ValidationResultWithTeamIdAndAnalysisIdWithoutEmailValidationId])(_: SRLogger))
          .expects(List(), logger)
          .returning(Success(0))

        (emailValidationModel.updateEmailValidationResultsFromBatchRequest(_: Seq[EmailValidationResultWithAnalysisId], _: EmailValidationToolV2))
          .expects(*, *)
          .returning(Success(List()))


        (bouncerEmailValidationApi.createBatchRequest(_: Seq[String])(_: WSClient, _: ExecutionContext, _: SRLogger))
          .expects(Seq("<EMAIL>"), wSClient, actorContext, logger)
          .returning(Future.successful(BatchRequestResponse("43123", Json.obj(), "up")))


        (emailValidationBatchRequestModel.createBatchRequest(_: EmailValidationToolV2, _: String))
          .expects(EmailValidationToolV2.BOUNCER, "43123")
          .returning(Success(1))

        (emailValidationApiToolsRecordDAO.addApiBatchRequest(_: String, _: Long, _: EmailValidationToolV2))
          .expects("43123", 1, EmailValidationToolV2.BOUNCER)
          .returning(Success(1))

        (emailValidationModel.updateQueuedStatusOnBatchRequest(_: Seq[Long], _: Long))
          .expects(Seq(12L), 1)
          .returning(Success(List(1)))


        val dummyJson = Json.obj("error" -> "wrong api key")
        (listCleanEmailValidationApi.createBatchRequest(_: Seq[String])(_: WSClient, _: ExecutionContext, _: SRLogger))
          .expects(Seq("<EMAIL>"), wSClient, actorContext, logger)
          .returning(Future.successful(BatchRequestResponse("43123", Json.obj(), "up")))


        (emailValidationBatchRequestModel.createBatchRequest(_: EmailValidationToolV2, _: String))
          .expects(EmailValidationToolV2.LISTCLEAN, "43123")
          .returning(Success(10))

        (emailValidationApiToolsRecordDAO.addApiBatchRequest(_: String, _: Long, _: EmailValidationToolV2))
          .expects("43123", 1, EmailValidationToolV2.LISTCLEAN)
          .returning(Success(3))

        (emailValidationModel.updateQueuedStatusOnBatchRequest(_: Seq[Long], _: Long))
          .expects(Seq(14L), 10)
          .returning(Success(List(3)))


        val result = emailValidationService.findAndMakeBatchRequest()(wSClient, actorContext, logger)

        whenReady(result) { result =>
          println(result)

          assert(result.srBatchRequestIdBouncer == Some(1))
          assert(result.srBatchRequestIdDeBounce == Some(0))
          assert(result.srBatchRequestIdListClean == Some(10))
          // Add more assertions as needed
        }
        // Ensure the test fails if the future succeeds


      }


      it("should fail when listclean catches error and bouncer has no error ") {


        val emptyListFordbc: Seq[CheckStatusBatchRequestData] = Seq()
        val emailValidationDataList: List[EmailValidationData] =
          List(EmailValidationData(12L, "<EMAIL>", TeamId(12L), BOUNCER, EmailValidationInitiator.InitiatedByCampaign),
            //EmailValidationData(13L,"<EMAIL>",TeamId(13L),EmailValidationToolV2.DEBOUNCE),
            EmailValidationData(14L, "<EMAIL>", TeamId(14L), EmailValidationToolV2.LISTCLEAN, EmailValidationInitiator.InitiatedByCampaign))
        val debouncedontHaveQueuedRequest = true

        (emailValidationBatchRequestModel.findQueuedBatchRequests)
          .expects(EmailValidationToolV2.DEBOUNCE)
          .returning(Success(emptyListFordbc))

        (emailValidationModel.findEmailsForMakingBatchRequest)
          .expects(debouncedontHaveQueuedRequest)
          .returning(Success(emailValidationDataList))


        val val1 = Seq(ProspectEmailValidationResultWithTeamIdAndAnalysisId(
          emailDeliveryAnalysisId = None,
          teamId = TeamId(0L),
          email = "<EMAIL>",
          isValid = false,
          emailValidationId = 12L,
          validationInitiator = EmailValidationInitiator.InitiatedByCampaign,
        ))

        val prospectsWithInvalidEmail: Seq[ProspectsWithInvalidEmail] = Seq()


        (prospectServiceV2.updateEmailValidationDataV2(_: Seq[ProspectEmailValidationResultWithTeamIdAndAnalysisId], _: SRLogger, _: EmailValidationInitiatorType))
          .expects(*, logger, EmailValidationInitiator.InitiatedByCampaign)
          .returning(Success(1, prospectsWithInvalidEmail))

        (leadFinderValidationService.updateLeadFinderValidationResults(_: List[ValidationResultWithTeamIdAndAnalysisIdWithoutEmailValidationId])(_: SRLogger))
          .expects(List(), logger)
          .returning(Success(0))

        (emailValidationModel.updateEmailValidationResultsFromBatchRequest(_: Seq[EmailValidationResultWithAnalysisId], _: EmailValidationToolV2))
          .expects(*, *)
          .returning(Success(List()))


        (bouncerEmailValidationApi.createBatchRequest(_: Seq[String])(_: WSClient, _: ExecutionContext, _: SRLogger))
          .expects(Seq("<EMAIL>"), wSClient, actorContext, logger)
          .returning(Future.successful(BatchRequestResponse("43123", Json.obj(), "up")))


        (emailValidationBatchRequestModel.createBatchRequest(_: EmailValidationToolV2, _: String))
          .expects(EmailValidationToolV2.BOUNCER, "43123")
          .returning(Success(1))

        (emailValidationApiToolsRecordDAO.addApiBatchRequest(_: String, _: Long, _: EmailValidationToolV2))
          .expects("43123", 1, EmailValidationToolV2.BOUNCER)
          .returning(Success(1))

        (emailValidationModel.updateQueuedStatusOnBatchRequest(_: Seq[Long], _: Long))
          .expects(Seq(12L), 1)
          .returning(Success(List(1)))


        val dummyStatus = 401 // Dummy status code

        val dummyJson = Json.obj("error" -> "wrong api key")
        (listCleanEmailValidationApi.createBatchRequest(_: Seq[String])(_: WSClient, _: ExecutionContext, _: SRLogger))
          .expects(Seq("<EMAIL>"), wSClient, actorContext, logger)
          .returning(Future.failed(new Exception(s"${dummyStatus} :: ${dummyJson}")))


        (emailValidationApiToolsRecordDAO.addErrorRecordWhileCreatingBatchRequest(_: EmailValidationToolV2, _: Int, _: JsValue))
          .expects(EmailValidationToolV2.LISTCLEAN, dummyStatus, dummyJson)
          .returning(Success(1))


        val result = emailValidationService.findAndMakeBatchRequest()(wSClient, actorContext, logger)

        whenReady(result.failed) { result =>
          println(result)
          assert(result.getMessage.contains(s"${dummyStatus} :: ${dummyJson}"))
        }
        // Ensure the test fails if the future succeeds


      }


      it("should fail when listclean catches error and bouncer and debounce has no erros but the batches will be made for bouncer and debounce ") {


        val emptyListFordbc: Seq[CheckStatusBatchRequestData] = Seq()
        val emailValidationDataList: List[EmailValidationData] =
          List(EmailValidationData(12L, "<EMAIL>", TeamId(12L), BOUNCER, EmailValidationInitiator.InitiatedByCampaign),
            EmailValidationData(13L, "<EMAIL>", TeamId(13L), EmailValidationToolV2.DEBOUNCE, EmailValidationInitiator.InitiatedByCampaign),
            EmailValidationData(14L, "<EMAIL>", TeamId(14L), EmailValidationToolV2.LISTCLEAN, EmailValidationInitiator.InitiatedByCampaign))
        val debouncedontHaveQueuedRequest = true

        (emailValidationBatchRequestModel.findQueuedBatchRequests)
          .expects(EmailValidationToolV2.DEBOUNCE)
          .returning(Success(emptyListFordbc))

        (emailValidationModel.findEmailsForMakingBatchRequest)
          .expects(debouncedontHaveQueuedRequest)
          .returning(Success(emailValidationDataList))


        val val1 = Seq(ProspectEmailValidationResultWithTeamIdAndAnalysisId(
          emailDeliveryAnalysisId = None,
          teamId = TeamId(0L),
          email = "<EMAIL>",
          isValid = false,
          emailValidationId = 12L,
          validationInitiator = EmailValidationInitiator.InitiatedByCampaign,
        ))

        val prospectsWithInvalidEmail: Seq[ProspectsWithInvalidEmail] = Seq()


        (prospectServiceV2.updateEmailValidationDataV2(_: Seq[ProspectEmailValidationResultWithTeamIdAndAnalysisId], _: SRLogger, _: EmailValidationInitiatorType))
          .expects(*, logger, EmailValidationInitiator.InitiatedByCampaign)
          .returning(Success(1, prospectsWithInvalidEmail))

        (leadFinderValidationService.updateLeadFinderValidationResults(_: List[ValidationResultWithTeamIdAndAnalysisIdWithoutEmailValidationId])(_: SRLogger))
          .expects(List(), logger)
          .returning(Success(0))

        (emailValidationModel.updateEmailValidationResultsFromBatchRequest(_: Seq[EmailValidationResultWithAnalysisId], _: EmailValidationToolV2))
          .expects(*, *)
          .returning(Success(List()))


        (bouncerEmailValidationApi.createBatchRequest(_: Seq[String])(_: WSClient, _: ExecutionContext, _: SRLogger))
          .expects(Seq("<EMAIL>"), wSClient, actorContext, logger)
          .returning(Future.successful(BatchRequestResponse("43123", Json.obj(), "up")))


        (emailValidationBatchRequestModel.createBatchRequest(_: EmailValidationToolV2, _: String))
          .expects(EmailValidationToolV2.BOUNCER, "43123")
          .returning(Success(1))

        (emailValidationApiToolsRecordDAO.addApiBatchRequest(_: String, _: Long, _: EmailValidationToolV2))
          .expects("43123", 1, EmailValidationToolV2.BOUNCER)
          .returning(Success(1))

        (emailValidationModel.updateQueuedStatusOnBatchRequest(_: Seq[Long], _: Long))
          .expects(Seq(12L), 1)
          .returning(Success(List(1)))


        val filenametest = "test.file"


        (() =>srUuidUtils.generateDeBounceUploadCSVFilename())
          .expects()
          .returning(filenametest)


        (cloudStorage.createUploadAndGenerateURLForCSV(_: Seq[Seq[String]], _: Seq[String], _: String, _: String, _: Int)(_: ExecutionContext))
          .expects(*, *, *, *, *, actorContext)
          .returning(Future.successful("<EMAIL>"))


        (deBounceEmailValidationApi.createBatchRequest(_: String)(_: WSClient, _: ExecutionContext, _: SRLogger))
          .expects("<EMAIL>", wSClient, actorContext, logger)
          .returning(Future.successful(BatchRequestResponse("43123", Json.obj(), "up")))


        (emailValidationBatchRequestModel.createBatchRequest(_: EmailValidationToolV2, _: String))
          .expects(EmailValidationToolV2.DEBOUNCE, "43123")
          .returning(Success(1L))

        (emailValidationApiToolsRecordDAO.addApiBatchRequest(_: String, _: Long, _: EmailValidationToolV2))
          .expects("43123", 1, EmailValidationToolV2.DEBOUNCE)
          .returning(Success(2))

        (emailValidationModel.updateQueuedStatusOnBatchRequest(_: Seq[Long], _: Long))
          .expects(Seq(13L), 1)
          .returning(Success(List(2)))


        (cloudStorage.deleteForCSV(_: String, _: String)(_: ExecutionContext))
          .expects(*, *, actorContext)
          .returning(Future.successful(true))



        //            val debouncerequestId = emailValidationService.makeBatchRequestWithDeBounceApiAndReturnRequestId(email_tobe_sent_to_debounce)(wSClient,actorContext,logger)


        val email_tobe_sent_to_listclean = List(EmailValidationData(14L, "<EMAIL>", TeamId(14L), EmailValidationToolV2.LISTCLEAN, EmailValidationInitiator.InitiatedByCampaign))


        val dummyStatus = 401 // Dummy status code

        val dummyJson = Json.obj("error" -> "wrong api key")
        (listCleanEmailValidationApi.createBatchRequest(_: Seq[String])(_: WSClient, _: ExecutionContext, _: SRLogger))
          .expects(Seq("<EMAIL>"), wSClient, actorContext, logger)
          .returning(Future.failed(new Exception(s"${dummyStatus} :: ${dummyJson}")))


        (emailValidationApiToolsRecordDAO.addErrorRecordWhileCreatingBatchRequest(_: EmailValidationToolV2, _: Int, _: JsValue))
          .expects(EmailValidationToolV2.LISTCLEAN, dummyStatus, dummyJson)
          .returning(Success(1))


        val result = emailValidationService.findAndMakeBatchRequest()(wSClient, actorContext, logger)

        whenReady(result.failed) { result =>
          println(result)
          assert(result.getMessage.contains(s"${dummyStatus} :: ${dummyJson}"))
        }
        // Ensure the test fails if the future succeeds


      }


      it("should pass when all three have no errors ") {


        val emptyListFordbc: Seq[CheckStatusBatchRequestData] = Seq()
        val emailValidationDataList: List[EmailValidationData] =
          List(EmailValidationData(12L, "<EMAIL>", TeamId(12L), BOUNCER, EmailValidationInitiator.InitiatedByCampaign),
            EmailValidationData(13L, "<EMAIL>", TeamId(13L), EmailValidationToolV2.DEBOUNCE, EmailValidationInitiator.InitiatedByCampaign),
            EmailValidationData(14L, "<EMAIL>", TeamId(14L), EmailValidationToolV2.LISTCLEAN, EmailValidationInitiator.InitiatedByCampaign))
        val debouncedontHaveQueuedRequest = true

        (emailValidationBatchRequestModel.findQueuedBatchRequests)
          .expects(EmailValidationToolV2.DEBOUNCE)
          .returning(Success(emptyListFordbc))

        (emailValidationModel.findEmailsForMakingBatchRequest)
          .expects(debouncedontHaveQueuedRequest)
          .returning(Success(emailValidationDataList))

        val emailto_be_sent_to_bouncer = List(EmailValidationData(12L, "<EMAIL>", TeamId(12L), BOUNCER, EmailValidationInitiator.InitiatedByCampaign))

        //val mockmakeBatchRequestWithBouncerApiAndReturnRequestId = mockFunction[emailValidationService.makeBatchRequestWithBouncerApiAndReturnRequestId(List[EmailValidationData])(_:WSClient,_:ExecutionContext,_:SRLogger)]


        //val invalidEmails:List[EmailValidationData] = List()

        //            (EmailValidationService.validateIEmailAddress)
        //              .expects(*)
        //              .returning(true)


        val val1 = Seq(ProspectEmailValidationResultWithTeamIdAndAnalysisId(
          emailDeliveryAnalysisId = None,
          teamId = TeamId(0L),
          email = "<EMAIL>",
          isValid = false,
          emailValidationId = 12L,
          validationInitiator = EmailValidationInitiator.InitiatedByCampaign,
        ))

        val prospectsWithInvalidEmail: Seq[ProspectsWithInvalidEmail] = Seq()
        //
        //
        //            val validemailslisttotest = List(EmailValidationData(12L,"<EMAIL>",TeamId(12L),EmailValidationToolV2.LISTCLEAN),
        //                                                EmailValidationData(13L,"<EMAIL>",TeamId(13L),EmailValidationToolV2.DEBOUNCE),
        //                                            EmailValidationData(14L,"<EMAIL>",TeamId(14L),EmailValidationToolV2.BOUNCER)
        //
        //            )





        (prospectServiceV2.updateEmailValidationDataV2(_: Seq[ProspectEmailValidationResultWithTeamIdAndAnalysisId], _: SRLogger, _: EmailValidationInitiatorType))
          .expects(*, logger, EmailValidationInitiator.InitiatedByCampaign)
          .returning(Success(1, prospectsWithInvalidEmail))

        (leadFinderValidationService.updateLeadFinderValidationResults(_: List[ValidationResultWithTeamIdAndAnalysisIdWithoutEmailValidationId])(_: SRLogger))
          .expects(List(), logger)
          .returning(Success(0))

        (emailValidationModel.updateEmailValidationResultsFromBatchRequest(_: Seq[EmailValidationResultWithAnalysisId], _: EmailValidationToolV2))
          .expects(*, *)
          .returning(Success(List()))






        //            (emailValidationService.checkAndUpdateIfInvalidBasedOnJavaEmailAddressBatch(_:List[EmailValidationData])(_:SRLogger))
        //              .expects(validemailslisttotest,logger)
        //              .returning(Success(validemailslisttotest))

        (bouncerEmailValidationApi.createBatchRequest(_: Seq[String])(_: WSClient, _: ExecutionContext, _: SRLogger))
          .expects(Seq("<EMAIL>"), wSClient, actorContext, logger)
          .returning(Future.successful(BatchRequestResponse("43123", Json.obj(), "up")))


        (emailValidationBatchRequestModel.createBatchRequest(_: EmailValidationToolV2, _: String))
          .expects(EmailValidationToolV2.BOUNCER, "43123")
          .returning(Success(1))

        (emailValidationApiToolsRecordDAO.addApiBatchRequest(_: String, _: Long, _: EmailValidationToolV2))
          .expects("43123", 1, EmailValidationToolV2.BOUNCER)
          .returning(Success(1))

        (emailValidationModel.updateQueuedStatusOnBatchRequest(_: Seq[Long], _: Long))
          .expects(Seq(12L), 1)
          .returning(Success(List(1)))


        val email_tobe_sent_to_debounce = List(EmailValidationData(13L, "<EMAIL>", TeamId(13L), EmailValidationToolV2.DEBOUNCE, EmailValidationInitiator.InitiatedByCampaign))

        val filenametest = "test.file"


        (() =>srUuidUtils.generateDeBounceUploadCSVFilename())
          .expects()
          .returning(filenametest)


        (cloudStorage.createUploadAndGenerateURLForCSV(_: Seq[Seq[String]], _: Seq[String], _: String, _: String, _: Int)(_: ExecutionContext))
          .expects(*, *, *, *, *, actorContext)
          .returning(Future.successful("<EMAIL>"))


        (deBounceEmailValidationApi.createBatchRequest(_: String)(_: WSClient, _: ExecutionContext, _: SRLogger))
          .expects("<EMAIL>", wSClient, actorContext, logger)
          .returning(Future.successful(BatchRequestResponse("43123", Json.obj(), "up")))


        (emailValidationBatchRequestModel.createBatchRequest(_: EmailValidationToolV2, _: String))
          .expects(EmailValidationToolV2.DEBOUNCE, "43123")
          .returning(Success(3))

        (emailValidationApiToolsRecordDAO.addApiBatchRequest(_: String, _: Long, _: EmailValidationToolV2))
          .expects("43123", 1, EmailValidationToolV2.DEBOUNCE)
          .returning(Success(2))

        (emailValidationModel.updateQueuedStatusOnBatchRequest(_: Seq[Long], _: Long))
          .expects(Seq(13L), 3)
          .returning(Success(List(2)))


        (cloudStorage.deleteForCSV(_: String, _: String)(_: ExecutionContext))
          .expects(*, *, actorContext)
          .returning(Future.successful(true))



        //            val debouncerequestId = emailValidationService.makeBatchRequestWithDeBounceApiAndReturnRequestId(email_tobe_sent_to_debounce)(wSClient,actorContext,logger)


        val email_tobe_sent_to_listclean = List(EmailValidationData(14L, "<EMAIL>", TeamId(14L), EmailValidationToolV2.LISTCLEAN, EmailValidationInitiator.InitiatedByCampaign))


        val dummyStatus = 401 // Dummy status code

        val dummyJson = Json.obj("error" -> "wrong api key")
        (listCleanEmailValidationApi.createBatchRequest(_: Seq[String])(_: WSClient, _: ExecutionContext, _: SRLogger))
          .expects(Seq("<EMAIL>"), wSClient, actorContext, logger)
          .returning(Future.successful(BatchRequestResponse("43125", Json.obj(), "up")))


        //            (emailValidationApiToolsRecordDAO.addErrorRecordWhileCreatingBatchRequest(_:EmailValidationToolV2,_:Int,_:JsValue))
        //              .expects(EmailValidationToolV2.LISTCLEAN,dummyStatus,dummyJson)
        //              .returning(Success(1))

        //            (emailValidationApiToolsRecordDAO.addErrorRecordWhileCreatingBatchRequest)
        //              .expects(EmailValidationToolV2.LISTCLEAN,401,dummyJson)
        //              .returning(Success(1))

        (emailValidationBatchRequestModel.createBatchRequest(_: EmailValidationToolV2, _: String))
          .expects(EmailValidationToolV2.LISTCLEAN, "43125")
          .returning(Success(5))

        (emailValidationApiToolsRecordDAO.addApiBatchRequest(_: String, _: Long, _: EmailValidationToolV2))
          .expects("43125", 1, EmailValidationToolV2.LISTCLEAN)
          .returning(Success(3))

        (emailValidationModel.updateQueuedStatusOnBatchRequest(_: Seq[Long], _: Long))
          .expects(Seq(14L), 5)
          .returning(Success(List(3)))

        //            val listcleanrequestId = emailValidationService.makeBatchRequestWithBouncerApiAndReturnRequestId(email_tobe_sent_to_listclean)(wSClient,actorContext,logger)


        //            (emailValidationService.makeBatchRequestWithListCleanApiAndReturnRequestId (_:List[EmailValidationData])(_:WSClient ,_:ExecutionContext,_:SRLogger))
        //              .expects(email_tobe_sent_to_listclean,wSClient,actorContext,logger)
        //              .returning(Future.failed(new Exception ("wrong api key")))


        val result = emailValidationService.findAndMakeBatchRequest()(wSClient, actorContext, logger)

        whenReady(result) { result =>
          println(result)

          assert(result.srBatchRequestIdBouncer == Some(1))
          assert(result.srBatchRequestIdDeBounce == Some(3))
          assert(result.srBatchRequestIdListClean == Some(5))
        }


      }
    }
  }


  describe("checkAndTryToMakeAPICallToCheckAPisUp") {

    it("should make a call to LISTCLEAN and return request ID") {

      val emailList = Seq("<EMAIL>", "<EMAIL>")
      val emailsList = Seq("<EMAIL>")
      val dummyStatus = 401 // Dummy status code
      val dummyJson = Json.obj("success" -> "Batch created successfully")


      (listCleanEmailValidationApi.createBatchRequest(_: Seq[String])(_: WSClient, _: ExecutionContext, _: SRLogger))
        .expects(*, wSClient, actorContext, *)
        .returning(Future.successful(BatchRequestResponse("43123", dummyJson, "1")))

      (emailValidationBatchRequestModel.createBatchRequest)
        .expects(*, *)
        .returning(Success(23L))

      (emailValidationApiToolsRecordDAO.addApiBatchRequest)
        .expects("43123", *, LISTCLEAN)
        .returning(Success(1L))

      (emailValidationModel.updateQueuedStatusOnBatchRequest)
        .expects(*, *)
        .returning(Success(List()))


      val result = emailValidationService.checkAndTryToMakeAPICallToCheckAPisUp(apiToolWhomErrorisOccured = LISTCLEAN)(wSClient, actorContext, logger)

      whenReady(result) { requestId =>
        assert(requestId == 23L)

      }
    }


    it("should throw an exception when error has occured") {


      val dummyStatus = 401 // Dummy status code
      val dummyJson = Json.obj("error" -> "Dummy error message")


      (listCleanEmailValidationApi.createBatchRequest(_: Seq[String])(_: WSClient, _: ExecutionContext, _: SRLogger))
        .expects(*, wSClient, actorContext, *)
        .returning(Future.failed(new Exception(s"${dummyStatus} :: ${dummyJson}")))


      (emailValidationApiToolsRecordDAO.addErrorRecordWhileCreatingBatchRequest)
        .expects(EmailValidationToolV2.LISTCLEAN, 401, dummyJson)
        .returning(Success(1))


      val failureResult = emailValidationService.checkAndTryToMakeAPICallToCheckAPisUp(apiToolWhomErrorisOccured = LISTCLEAN)(wSClient, actorContext, logger)

      whenReady(failureResult.failed) { exception =>
        assert(exception.getMessage.contains(s"$dummyStatus :: ${dummyJson}"))

      }
    }
  }


  describe("setEmailValidationPriorityOnBasisOfCampaignStartTime") {

    it("campaign start time is 9.00 PM and current time passed is 6.00 PM of timezone Asia/ Jakarta which is less than 4 hrs " +
      "from campaign start time so - should return medium priority") {

      val campaignForScheduling1 = campaignForSchedulingEmail.copy(
        timezone = "Asia/Jakarta"
      )

      val currentTime: DateTime = DateTime.now(DateTimeZone.forID("Asia/Jakarta")).withTimeAtStartOfDay().plusHours(18)
      val result = EmailValidationService.setEmailValidationPriorityOnBasisOfCampaignStartTime(campaignForScheduling1, currentTime)
      println(result)
      assert(result == EmailValidationPriority.Medium)
    }

    it("campaign start time is 9.00 PM and current time passed is 5.00 AM of timezone UTC which is more than 4 hrs " +
      "from campaign start time so - should return low priority") {
      val campaignForScheduling2 = campaignForSchedulingEmail.copy(
        timezone = "UTC"
      )
      val currentTime: DateTime = DateTime.now(DateTimeZone.forID("UTC")).withTimeAtStartOfDay().plusHours(5)
      val result = EmailValidationService.setEmailValidationPriorityOnBasisOfCampaignStartTime(campaignForScheduling2, currentTime)
      println(result)
      assert(result == EmailValidationPriority.Low)
    }

    it("campaign start time is 1.00 AM and campaignEndTime is 2.00 AM and current time passed is 6.00 PM of timezone asia/jakarta " +
      "which is more than 4 hrs from campaign start time for the next day so - should return low priority") {
      val campaignForScheduling3 = campaignForSchedulingEmail.copy(
        timezone = "Asia/Jakarta",
        daily_from_time = 1 * 60 * 60,
        daily_till_time = 2 * 60 * 60
      )

      val currentTime: DateTime = DateTime.now(DateTimeZone.forID("Asia/Jakarta")).withTimeAtStartOfDay().plusHours(18)
      val result = EmailValidationService.setEmailValidationPriorityOnBasisOfCampaignStartTime(campaignForScheduling3, currentTime)
      println(result)
      assert(result == EmailValidationPriority.Low)
    }

    it("campaign start time is 5.00 PM and current time passed is 6.00 PM of timezone Asia/ Jakarta which is in between the  " +
      "campaign start time and campaign end time so - should return medium priority") {
      val campaignForScheduling4 = campaignForSchedulingEmail.copy(
        timezone = "Asia/Jakarta",
        daily_from_time = 17 * 60 * 60

      )

      val currentTime: DateTime = DateTime.now(DateTimeZone.forID("Asia/Jakarta")).withTimeAtStartOfDay().plusHours(18)
      val result = EmailValidationService.setEmailValidationPriorityOnBasisOfCampaignStartTime(campaignForScheduling4, currentTime)
      println(result)
      assert(result == EmailValidationPriority.Medium)
    }

    it("campaign start time is 9.00 PM and current time passed is 6.00 PM of timezone Asia/Kolkata which is less than 4 hrs " +
      "from campaign start time so - should return medium priority") {

      val campaignForScheduling5 = campaignForSchedulingEmail.copy(
        timezone = "Asia/Kolkata"
      )

      val currentTime: DateTime = DateTime.now(DateTimeZone.forID("Asia/Kolkata")).withTimeAtStartOfDay().plusHours(18)
      val result = EmailValidationService.setEmailValidationPriorityOnBasisOfCampaignStartTime(campaignForScheduling5, currentTime)
      println(result)
      assert(result == EmailValidationPriority.Medium)
    }

    it("campaign start time is 9.00 PM and current time passed is 5.00 AM of timezone America/Los_Angeles which is more than 4 hrs " +
      "from campaign start time so - should return low priority") {
      val campaignForScheduling6 = campaignForSchedulingEmail.copy(
        timezone = "America/Los_Angeles"
      )
      val currentTime: DateTime = DateTime.now(DateTimeZone.forID("America/Los_Angeles")).withTimeAtStartOfDay().plusHours(5)
      val result = EmailValidationService.setEmailValidationPriorityOnBasisOfCampaignStartTime(campaignForScheduling6, currentTime)
      println(result)
      assert(result == EmailValidationPriority.Low)
    }

    it("campaign start time is 1.00 AM and campaignEndTime is 2.00 AM and current time passed is 6.00 PM of timezone Australia/Melbourne " +
      "which is more than 4 hrs from campaign start time for the next day so - should return low priority") {
      val campaignForScheduling7 = campaignForSchedulingEmail.copy(
        timezone = "Australia/Melbourne",
        daily_from_time = 1 * 60 * 60,
        daily_till_time = 2 * 60 * 60
      )

      val currentTime: DateTime = DateTime.now(DateTimeZone.forID("Australia/Melbourne")).withTimeAtStartOfDay().plusHours(18)
      val result = EmailValidationService.setEmailValidationPriorityOnBasisOfCampaignStartTime(campaignForScheduling7, currentTime)
      println(result)
      assert(result == EmailValidationPriority.Low)
    }


  }


}

