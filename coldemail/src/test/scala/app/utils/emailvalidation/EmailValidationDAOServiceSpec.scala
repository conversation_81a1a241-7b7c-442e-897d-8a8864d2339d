package app.utils.emailvalidation

import org.joda.time.DateTime
import org.scalamock.scalatest.MockFactory
import org.scalatest.funspec.AnyFunSpec
import utils.emailvalidation.dao_service.EmailValidationDAOService
import utils.emailvalidation.models.EmailValidationToolV2
import utils.emailvalidation.{EmailValidationApiToolsRecordDAO, EmailValidationModel}

import scala.util.Success

class EmailValidationDAOServiceSpec extends AnyFunSpec with MockFactory {

    val emailValidationModel: EmailValidationModel = mock[EmailValidationModel]
    val emailValidationApiToolsRecordDAO: EmailValidationApiToolsRecordDAO = mock[EmailValidationApiToolsRecordDAO]


    val emailValidationDAOservice = new EmailValidationDAOService(
        emailValidationModel = emailValidationModel,
        emailValidationApiToolsRecordDAO = emailValidationApiToolsRecordDAO

    )


    describe("EmailValidationDAOService.getBatchResultsAndUpdateIntoTable"){
        it("should calculate downtime and update the record in the DB "){
            val downTimeForBouncer = DateTime.now().minusMinutes(45)



            (emailValidationApiToolsRecordDAO.getwentDownTime)
              .expects(EmailValidationToolV2.BOUNCER)
              .returning(Success(Some(downTimeForBouncer)))

            (emailValidationApiToolsRecordDAO.updateBatchRequestResult)
              .expects(*,*,*,*,*,*,*)
              .returning(Success(1))


            val result = emailValidationDAOservice.getBatchResultsAndUpdateIntoTable("42345",EmailValidationToolV2.BOUNCER,10,23,3,5)

            assert(result == Success(1))



        }
    }

}
