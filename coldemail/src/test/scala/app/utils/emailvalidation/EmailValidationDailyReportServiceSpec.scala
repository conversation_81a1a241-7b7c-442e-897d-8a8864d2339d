package app.utils.emailvalidation

import org.apache.pekko.actor.ActorSystem
import org.apache.pekko.stream.Materializer
import api.scheduler_report.ReportData.EmailValidationReportData
import api.scheduler_report.ReportType.EMAIL_VALIDATION_APIS
import api.scheduler_report.{ReportData, ReportType, SchedulerIntegrityService}
import org.joda.time.DateTime
import org.scalamock.scalatest.MockFactory
import org.scalatest.funspec.AnyFunSpec
import play.api.libs.ws.WSClient
import play.api.libs.ws.ahc.AhcWSClient
import utils.SRLogger
import utils.email_notification.service.EmailNotificationService
import utils.emailvalidation.models.EmailValidationToolV2.BOUNCER
import utils.emailvalidation.{EmailValidationApiToolsRecordDAO, EmailValidationDailyReportService}
import utils.testapp.TestAppExecutionContext

import scala.concurrent.ExecutionContext
import scala.util.{Failure, Success}

class EmailValidationDailyReportServiceSpec extends AnyFunSpec with MockFactory{

    given logger: SRLogger = new SRLogger("EmailValidationDailyReportServiceSpec ")

    given system: ActorSystem = TestAppExecutionContext.actorSystem

    given materializer: Materializer = TestAppExecutionContext.actorMaterializer

    given wSClient: AhcWSClient = TestAppExecutionContext.wsClient

    given actorContext: ExecutionContext = system.dispatcher

    val emailValidationApiToolsRecordDAO: EmailValidationApiToolsRecordDAO = mock[EmailValidationApiToolsRecordDAO]
    val schedulerIntegrityService: SchedulerIntegrityService = mock[SchedulerIntegrityService]
    val emailNotificationService: EmailNotificationService = mock[EmailNotificationService]


    val emailValidationDailyReportService = new EmailValidationDailyReportService(
        emailValidationApiToolsRecordDAO = emailValidationApiToolsRecordDAO,
        schedulerIntegrityService = schedulerIntegrityService,
        emailNotificationService = emailNotificationService
    )


    describe("getValidationReportDataAndInsertIntoReportTable"){

        it("should pass and return false when checkIfCronCanRunForValidationReport returns false "){

            (schedulerIntegrityService.checkIfCronCanRunForReport(_:ReportType)(using _:SRLogger))
              .expects(EMAIL_VALIDATION_APIS,logger)
              .returning(Success(false))

            val result = emailValidationDailyReportService.getValidationReportDataAndInsertIntoReportTable(
                validationReportType = EMAIL_VALIDATION_APIS
                )

            assert(result == Success(false))



        }

        it("should pass and return true when new report is inserted into the table"){
         


            val validationReportData = List(EmailValidationReportData("bcr",2178, ReportType.EMAIL_VALIDATION_APIS,2000,100,8,70,0), EmailValidationReportData("dbc",6578, ReportType.EMAIL_VALIDATION_APIS,6000,500,70,8,10))

            (schedulerIntegrityService.checkIfCronCanRunForReport(_:ReportType)(using _:SRLogger))
              .expects(EMAIL_VALIDATION_APIS,logger)
              .returning(Success(true))

            (emailValidationApiToolsRecordDAO.getDataForDailyReporting(_:DateTime,_:DateTime))
              .expects(*,*)
              .returning(Success(validationReportData))

            (schedulerIntegrityService.insertListOfReportData(_:ReportType,_:List[ReportData])(using _:SRLogger))
              .expects(EMAIL_VALIDATION_APIS,validationReportData,logger)
              .returning(Success(12L))

            (emailNotificationService.sendEmailValidationReportToAdmin(_:List[EmailValidationReportData])(_:WSClient,_:ExecutionContext,_:SRLogger))
              .expects(validationReportData,wSClient,actorContext,logger)
              .returning(Success({}))



            val result = emailValidationDailyReportService.getValidationReportDataAndInsertIntoReportTable(
                validationReportType =EMAIL_VALIDATION_APIS)

            assert(result ==Success(true))
        }


        it("should fail when insertValidationReportData fails"){


            implicit val system = TestAppExecutionContext.actorSystem
            implicit val materializer = TestAppExecutionContext.actorMaterializer
            implicit val wSClient: AhcWSClient = TestAppExecutionContext.wsClient
            implicit val actorContext: ExecutionContext = system.dispatcher

            val validationReportData = List(EmailValidationReportData("bcr",2178, ReportType.EMAIL_VALIDATION_APIS,2000,100,8,70,0), EmailValidationReportData("dbc",6578, ReportType.EMAIL_VALIDATION_APIS,6000,500,70,8,10
            ))

            (schedulerIntegrityService.checkIfCronCanRunForReport(_:ReportType)(using _:SRLogger))
              .expects(EMAIL_VALIDATION_APIS,logger)
              .returning(Success(true))


            (emailValidationApiToolsRecordDAO.getDataForDailyReporting)
              .expects(*,*)
              .returning(Success(validationReportData))

            (schedulerIntegrityService.insertListOfReportData(_:ReportType,_:List[ReportData])(using _:SRLogger))
              .expects(EMAIL_VALIDATION_APIS,validationReportData,logger)
              .returning(Failure(new Exception("error occured while inserting the report into DB")))


            val result = emailValidationDailyReportService.getValidationReportDataAndInsertIntoReportTable(
                validationReportType = EMAIL_VALIDATION_APIS
            )

            println(result)

            assert(result.isFailure)




        }


        it("should fail when getDataForDailyReporting fails"){


            (schedulerIntegrityService.checkIfCronCanRunForReport(_:ReportType)(using _:SRLogger))
              .expects(EMAIL_VALIDATION_APIS,logger)
              .returning(Success(true))


            (emailValidationApiToolsRecordDAO.getDataForDailyReporting)
              .expects(*,*)
              .returning(Failure(new Exception("Some error occured while getting the data")))

            val result = emailValidationDailyReportService.getValidationReportDataAndInsertIntoReportTable(
                validationReportType = EMAIL_VALIDATION_APIS
            )

            println(result)

            assert(result.isFailure)

        }
    }

}
