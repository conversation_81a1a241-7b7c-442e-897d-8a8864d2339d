/* 17-jun-2025: [DO_NOT_REMOVE] [LEADFINDERUPLOAD] was slowing down compilation. Only needed for the Lead database upload.

package app.utils.testapp.csv_upload

import org.scalamock.scalatest.AsyncMockFactory
import org.scalatest.funspec.AsyncFunSpec
import utils.SRLogger
import utils.testapp.csv_upload.LeadFinderUploadService
import utils.uuid.SrUuidUtils

class LeadFinderUploadServiceSpec extends AsyncFunSpec  {

  describe("Testing createMapFromCSVHeaderAndRows") {



    val logger = new SRLogger("[LeadFinderUploadServiceSpec]")

    it("It should return a failure when a row has fewer columns than the header") {
      val csvHeaderRow = List(
        "First Name", "Last Name", "Email", "Entity Type", "Entity Type"
      )
      val dataRows = Seq(
        Seq("John", "Doe", "<EMAIL>") // Missing columns
        )
      val result = LeadFinderUploadService.createMapFromCSVHeaderAndRows(csvHeaderRow, dataRows, logger)
      assert(result.isFailure)
    }

    it("It should return correct mapping when rows have all valid data") {
      val csvHeaderRow = List(
        "First Name", "Last Name", "Email", "Entity Type", "Entity Type"
      )
      val dataRows = Seq(
        Seq("John", "Doe", "<EMAIL>", "Type 1", "Type 2")
      )
      val result = LeadFinderUploadService.createMapFromCSVHeaderAndRows(csvHeaderRow, dataRows, logger)
      assert(result.isSuccess)
      val maps = result.get
      assert(maps.size == 1)
      assert(maps.head == Map(
        "First Name" -> "John",
        "Last Name" -> "Doe",
        "Email" -> "<EMAIL>",
        "Entity Type" -> "Type 1" // Chooses the first "Entity Type" with a value
      ))
    }

    it("It should handle duplicate headers and pick the first non-empty value") {
      val csvHeaderRow = List(
        "First Name", "Last Name", "Entity Type", "Entity Type"
      )
      val dataRows = Seq(
        Seq("John", "Doe", "", "Type 2") // First "Entity Type" is empty, second has a value
      )
      val result = LeadFinderUploadService.createMapFromCSVHeaderAndRows(csvHeaderRow, dataRows, logger)
      assert(result.isSuccess)
      val maps = result.get
      assert(maps.size == 1)
      assert(maps.head == Map(
        "First Name" -> "John",
        "Last Name" -> "Doe",
        "Entity Type" -> "Type 2" // Chooses the first valid "Entity Type"
      ))
    }

    it("It should return an empty map when the row is entirely empty") {
      val csvHeaderRow = List(
        "First Name", "Last Name", "Email", "Entity Type"
      )
      val dataRows = Seq(
        Seq("", "", "", "") // All columns are empty
      )
      val result = LeadFinderUploadService.createMapFromCSVHeaderAndRows(csvHeaderRow, dataRows, logger)
      assert(result.isSuccess)
      val maps = result.get
      assert(maps.size == 1)
      assert(maps.head.isEmpty) // No values in the map
    }

    it("It should log an error for rows with fewer columns than headers") {
      val csvHeaderRow = List(
        "First Name", "Last Name", "Email", "Entity Type"
      )
      val dataRows = Seq(
        Seq("John", "Doe") // Missing columns
      )
      val result  = LeadFinderUploadService.createMapFromCSVHeaderAndRows(csvHeaderRow, dataRows, logger)
      // Check logger output (pseudo-code, implement logger validation as needed)
      assert(result.isFailure)
    }

  }


}
*/
