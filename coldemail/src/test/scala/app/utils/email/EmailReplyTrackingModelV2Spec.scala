package app.utils.email

import api.accounts.email.models.EmailServiceProvider
import api.accounts.{Account, AccountAccess, AccountMetadata, AccountType, AccountUuid, OrgCountData, OrgMetadata, OrgPlan, OrgSettings, OrganizationRole, OrganizationWithCurrentData, PermType, PermissionLevelForValidation, PermissionOwnershipV2, ProspectCategoriesInDB, ReplyHandling, RolePermV2, RolePermissionDataDAOV2, RolePermissionDataV2, RolePermissionsInDBV2, RolePermissionsV2, TeamAccount, TeamAccountRole, TeamId, TeamMember, TeamMemberLite}
import api.accounts.models.{AccountId, AccountProfileInfo, OrgId, TeamSRAIFlags}
import api.calendar_app.models.CalendarAccountData
import api.campaigns.{CPCompleted, CampaignProspectDAO}
import api.campaigns.services.CampaignProspectService2
import api.emails.dao_service.{EmailReplyTrackingDAOService, EmailScheduledDAOService, EmailThreadDAOService}
import api.emails.models.{EmailMessageContact, EmailMessageContactModel, EmailSettingUuid}
import api.emails.{AssociateEmailThreadProspect, DBEmailMessagesSavedResponse, ERIntermediateValidProspect, EmailCommonService, EmailMessageTracked, EmailReplySavedV3, EmailReplyTrackingModel, EmailReplyTrackingModelV2, EmailScheduledDAO, EmailSetting, EmailThreadDAO, EmailThreadUpdateLatestEmailData, NewEmailThreadV3, SavedEmailThread, UpdateEmailStatusOnReplyV3}
import api.integrations.IntegrationTPAccessTokenResponse
import api.prospects.dao_service.ProspectDAOService
import api.prospects.models.{ProspectCategory, ProspectCategoryRank}
import api.prospects.service.ProspectServiceV2
import api.prospects.{ProspectIdEmail, ProspectUpdateCategoryTemp}
import api.scylla.dao.BounceData
import api.sr_ai.mq.MqSrAiApiPublisher
import api.team.TeamUuid
import api.team_inbox.dao.EmailTeamInboxDAO
import api.team_inbox.service.ReplySentimentService
import api.team_inbox.model.{FolderType, ReplySentimentChannelType, ReplySentimentSubCategory, ReplySentimentType, ReplySentimentTypeData}
import api.team_inbox.service.{ReplySentimentForTeam, ReplySentimentService, ReplySentimentUuid}
import app.test_fixtures.accounts.OrgCountDataFixture
import app.test_fixtures.organizationa.{OrgMetadataFixture, OrgPlanFixture}
import eventframework.SrResourceTypes
import io.smartreach.esp.api.emails.{EmailSettingId, IEmailAddress}
import io.smartreach.esp.utils.email.EmailReplyBounceType
import org.joda.time.DateTime
import org.scalamock.scalatest.MockFactory
import org.scalatest.funspec.AnyFunSpec
import play.api.libs.json.Json
import sr_scheduler.models.ChannelType
import utils.{ISRLogger, SRLogger}
import utils.cache_utils.model.CampaignUseStatusForEmailSetting
import utils.email.EmailReplyStatus
import utils.email.services.InternalTrackingNote
import api.emails.models.EmailReplyType
import utils.helpers.LogHelpers
import utils.uuid.SrUuidUtils
import utils_deploy.rolling_updates.models.SrRollingUpdateFeature
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService

import scala.util.{Failure, Success, Try}
import api.team.service.TeamService
import io.sr.billing_common.models.PlanID
import scalikejdbc.DBSession


class EmailReplyTrackingModelV2Spec extends AnyFunSpec with MockFactory{

  val prospectDAOService = mock[ProspectDAOService]
//  val campaignProspectDAO = mock[CampaignProspectDAO]
  val prospectUpdateCategoryTemp = mock[ProspectUpdateCategoryTemp]
  val emailScheduledDAOService = mock[EmailScheduledDAOService]
  val emailReplyTrackingModel = mock[EmailReplyTrackingModel]
//  val prospectServiceV2 = mock[ProspectServiceV2]
  val emailMessageContactModel = mock[EmailMessageContactModel]
  val emailReplyTrackingDAOService = mock[EmailReplyTrackingDAOService]
  val replySentimentService = mock[ReplySentimentService]
  val campaignProspectService2 = mock[CampaignProspectService2]
  val rolePermissionDataDAOV2 = mock[RolePermissionDataDAOV2]
  val emailThreadDAOService = mock[EmailThreadDAOService]
  val srUuidUtils = mock[SrUuidUtils]
  val srRollingUpdateCoreService: SrRollingUpdateCoreService = mock[SrRollingUpdateCoreService]
  val mqSrAiApiPublisher: MqSrAiApiPublisher = mock[MqSrAiApiPublisher]
  val teamInboxDAO: EmailTeamInboxDAO = mock[EmailTeamInboxDAO]
  val emailCommonService = mock[EmailCommonService]
  val teamService: TeamService = mock[TeamService]

  val emailReplyTrackingModelV2 = new EmailReplyTrackingModelV2(
    campaignProspectService2 = campaignProspectService2,
    prospectDAOService = prospectDAOService,
//    campaignProspectDAO = campaignProspectDAO,
    srUuidUtils = srUuidUtils,
    prospectUpdateCategoryTemp = prospectUpdateCategoryTemp,
    emailReplyTrackingModel = emailReplyTrackingModel,
//    prospectServiceV2 = prospectServiceV2,
    emailMessageContactModel = emailMessageContactModel,
    emailReplyTrackingDAOService = emailReplyTrackingDAOService,
    replySentimentService = replySentimentService,
    emailThreadDAOService = emailThreadDAOService,
    srRollingUpdateCoreService = srRollingUpdateCoreService,
    teamInboxDAO = teamInboxDAO,
    emailScheduledDAOService = emailScheduledDAOService,
    mqSrAiApiPublisher = mqSrAiApiPublisher,
    emailCommonService = emailCommonService,
    teamService = teamService
  )


  given Logger: SRLogger = new SRLogger("Unit test")
  val Error = new Throwable("ERROR")
  val team_id: Long = 2
  val first_name = "Adminfirst"
  val last_name = "Adminlast"
  val company = "CompanyName"
  val email = "<EMAIL>"

  val integrationTPAccessTokenResponse = IntegrationTPAccessTokenResponse.FullTokenData(
    access_token = "some_access_token",
    refresh_token = Some("some_refresh_token"),
    expires_in = Option(150),
    expires_at = Option(DateTime.now().plusMinutes(9)),
    token_type = Some("some_token_type"),
    api_domain = Some("some_api_domain"),
    is_sandbox = Some(false)
  )

  

  val profile = AccountProfileInfo(
    first_name = first_name,
    last_name = last_name,
    company = Some(company),
    timezone = None,
    country_code = None,
    mobile_country_code = None,
    mobile_number = None,
      onboarding_phone_number= None,
    twofa_enabled = false,
    has_gauthenticator = false,
    weekly_report_emails = None,
    scheduled_for_deletion_at = None
  )

  val accountMetadata = AccountMetadata(
    // account_ui_version = None,
    is_profile_onboarding_done = None
  )

  val orgMetadata = OrgMetadataFixture.orgMetadataFixture2

  val orgCountData: OrgCountData = OrgCountDataFixture.orgCountData_default

  val orgSettings = OrgSettings(
    enable_ab_testing = false,
    disable_force_send = false,
    bulk_sender = false,
    allow_2fa = false,
    show_2fa_setting = false,
    enforce_2fa = false,
    allow_native_crm_integration = false,
      agency_option_allow_changing = false,
      agency_option_show = false
  )

  val orgPlan = OrgPlanFixture.orgPlanFixture

  val org = OrganizationWithCurrentData(

    id = 1,
    name = company,
    owner_account_id = 49,

    counts = orgCountData,
    settings = orgSettings,
    plan = orgPlan,

    is_agency = true,
    trial_ends_at = DateTime.now().plusDays(100),
    error = None,
    error_code = None,
    paused_till = None,
    errors = Seq(),
    warnings = Seq(),
    via_referral = false,
    org_metadata = orgMetadata
  )

  val teamMemberLite = TeamMemberLite(

    user_id =  2L,
    first_name = Some("first_name"),
    last_name = Some("last_name"),
    email = "<EMAIL>",
    active = true,
    timezone = Some("campaignTimezone"),
    twofa_enabled = true,
    created_at = DateTime.now(),
    user_uuid = AccountUuid("uuid"),
    team_role = TeamAccountRole.ADMIN

  )

  val prospect_CategoriesInDB = ProspectCategoriesInDB(
    id = 22L,
    name = "Completed",
    text_id = "Done",
    label_color = "Blue",
    is_custom = true,
    team_id = team_id,
    rank = ProspectCategoryRank(rank = 2000)
  )

  val teamMember: TeamMember = TeamMember(
    team_id = team_id,
    team_name = "team_name",
    user_id = 2L,
    ta_id = 49L, // dont send ta_id to frontend / api response, only for internal purpose, its dynamically assigned in AuthUtils
    first_name = Some(first_name),
    last_name = Some(last_name),
    email = "<EMAIL>",
    team_role = TeamAccountRole.ADMIN,
    api_key = Some("apiKey"),
    zapier_key = Some("zapier_key")
  )

  val adminDefaultPermissions = RolePermissionDataDAOV2.defaultRoles(
    role = TeamAccountRole.ADMIN,
    simpler_perm_flag = false
  )

  val rolePermissionData = RolePermissionDataV2.toRolePermissionApi(
    data = adminDefaultPermissions.copy(id = 10)
  )

  val team_account: TeamAccount = TeamAccount(

    team_id = team_id,
    org_id = 20L,

    role_from_db = Some(adminDefaultPermissions), // MUST come from db (option type only for cacheservice error), should not be sent to frontend, only intermediate

    role = Some(rolePermissionData), // should be sent to frontend

    active = true,
    is_actively_used = true,
    team_name = "team_name",
    total_members = 5,
    access_members = Seq(teamMember),
    all_members = Seq(teamMemberLite),

    prospect_categories_custom = Seq(prospect_CategoriesInDB),
    max_emails_per_prospect_per_day = 100L,
    max_emails_per_prospect_per_week = 500L,
    max_emails_per_prospect_account_per_day = 97,
    max_emails_per_prospect_account_per_week = 497,

    reply_handling = ReplyHandling.PAUSE_SPECIFIC_CAMPAIGN_ON_REPLY,
    created_at = DateTime.now(),
    selected_calendar_data = None,
    team_uuid = TeamUuid("uuid")
  )

  val accountAdmin = Account(
    id = AccountUuid("account_uuid"),
    internal_id = 2,
    email = email,
    email_verification_code = None,
    email_verification_code_created_at = None,
    created_at = DateTime.now().minusDays(1000),
    first_name = Some(first_name),
    last_name = Some(last_name),
    company = Some(company),
    timezone = None,
    profile = profile,
    org_role = Some(OrganizationRole.OWNER),
    teams = Seq(team_account),
    account_type = AccountType.AGENCY,
    org = org,
    active = true,
    email_notification_summary = "dSFA",
    account_metadata = accountMetadata,
    email_verified = true,
    signupType = None,
    account_access = AccountAccess(
      inbox_access = false
    ),
    calendar_account_data = None

  )

  val email_address_host = "company.com"

  val emailMessageIdSuffix = "local@smartreachio"

  val emailSetting = EmailSetting(
    id = Some(EmailSettingId(emailSettingId = 123)),
    org_id = OrgId(id = 17),
    owner_id = AccountId(id = 47),
    team_id = TeamId(id = 789),
    uuid = Some(EmailSettingUuid("test_uuid")),
    owner_uuid = AccountUuid("owner_uuid"),
    team_uuid = TeamUuid("team_uuid"),
    message_id_suffix = emailMessageIdSuffix,

    email = email,
    email_address_host = email_address_host,

    service_provider = EmailServiceProvider.OTHER,
      domain_provider = None,
    via_gmail_smtp = None,

    owner_name = "Ownername dummy",
    sender_name = "Test Sender Name",
    first_name = "John",
    last_name = "Doe",

    cc_emails = None,
    bcc_emails = None,

    smtp_username = Some(email),
    smtp_password = Some("thisispassword"),
    smtp_host = Some("this is the smtp host"),
    smtp_port = Some(12345),

    imap_username = Some(email),
    imap_password = Some("thisisimappassword"),
    imap_host = Some("imap.host.com"),
    imap_port = Some(993),

    oauth2_access_token = None,
    oauth2_refresh_token = None,
    oauth2_token_type = None,
    oauth2_token_expires_in = None,
    oauth2_access_token_expires_at = None,

    // for mailgun
    email_domain = None,
    api_key = None,
    mailgun_region = None,

    quota_per_day = 3,

    reply_handling = ReplyHandling.PAUSE_SPECIFIC_CAMPAIGN_ON_REPLY,
    last_read_for_replies = None,
    latest_email_scheduled_at = None,

    error = None,
    error_reported_at = None,
    paused_till = None,

    signature = "MySignature",

    created_at = Some(DateTime.now()),

    current_prospect_sent_count_email = 3,

    default_tracking_domain = "company.com",
    default_unsubscribe_domain = "company.com",
    rep_tracking_host_id = 123,
    tracking_domain_host = None,
    custom_tracking_domain = None,
    custom_tracking_cname_value = None,
    custom_tracking_domain_is_verified = None,
    custom_tracking_domain_is_ssl_enabled = None,

    rep_mail_server_id = 123,
    rep_mail_server_public_ip = "0.0.0.0",
    rep_mail_server_host = "randomserverhost.com",
    rep_mail_server_reverse_dns = None,

    min_delay_seconds = 30,
    max_delay_seconds = 120,
      tag = None,
    campaign_use_status_for_email_setting = CampaignUseStatusForEmailSetting.IsNotAssignedToAnyCampaign,
    show_rms_ip_in_frontend = false

  )

  val emailReplyStatus = EmailReplyStatus(
    replyType = EmailReplyType.NOT_CATEGORIZED,
    isReplied = false,
    isUnsubscribeRequest = false,
    isAutoReply = false,
    isOutOfOfficeReply = false,
    isInvalidEmail = false,
    isForwarded = false,
    bouncedData = None
  )

  val emailMessageTracked = EmailMessageTracked(
    inbox_email_setting_id = 1,
    from = IEmailAddress(email = "<EMAIL>"),
    to_emails = Seq(IEmailAddress(email = "<EMAIL>"), IEmailAddress(email = "<EMAIL>")),
    subject = "SomeSubject",
    body = "SomeBody",
    base_body = "SomeBaseBody",
    text_body = "SomeTextBody",
    references_header = None,
    campaign_id = Some(123),
    step_id = Some(456),
    prospect_id_in_campaign = Some(890),
    prospect_account_id_in_campaign = Some(678),
    campaign_name = None,
    step_name = None,
    received_at = DateTime.now().minusDays(2),
    recorded_at = DateTime.now().minusDays(2),
    sr_inbox_read = true,
    original_inbox_folder = None,
    email_status = emailReplyStatus,
    message_id = "some_message_id",
    full_headers = Json.obj(),
    scheduled_manually = false,
    reply_to = None,
    email_thread_id = None,
    gmail_msg_id = None,
    gmail_thread_id = None,
    outlook_msg_id = None,
    outlook_conversation_id = None,
    outlook_response_json = None,
    cc_emails = Seq(),
    in_reply_to_header = None,
    team_id = 1,
    account_id = 1,
    internal_tracking_note = InternalTrackingNote.EXISTING_INREPLYTO,
    tempThreadId = None
  )

  val eRIntermediateValidProspect = ERIntermediateValidProspect(
    prospect_id = 890,
    email = "<EMAIL>",
    email_domain = "email.com",
    account_id = 1, //prospect owner id
    team_id = 1,
    ta_id = 49,
    prospect_account_id = Some(1), //prospect_acccount_id => table -> prospect_accounts.id
    prospect_category_id_custom = 300,
    last_contacted_at = Some(DateTime.now().minusDays(2))
  )

  val emailReplySavedV3 = EmailReplySavedV3(
    message_id = "some_message_id",
    email_body = "test body",
    email_scheduled_id = 345,
    email_thread_id = 456,
    by_account = true,
    sent_at = DateTime.now().minusDays(1),
    campaign_id = None,
    step_id = None,
    prospect_id_in_campaign = None,
    prospect_account_id_in_campaign = None,
    reply_type = EmailReplyType.NOT_CATEGORIZED,
    from_email = IEmailAddress(email = "<EMAIL>"),
    to_emails = Seq(IEmailAddress(email = "<EMAIL>"), IEmailAddress(email = "<EMAIL>")),
    cc_emails = Seq(),
    reply_to = Some(IEmailAddress(email = "<EMAIL>")),
    inbox_email_setting_id = 1,
    from_prospect = None,
    to_prospects = Seq(),
    campaign_associated_prospect = None,
    all_prospects_involved = Seq(),
    email_status = emailReplyStatus.copy(isReplied = true),
    subject = "SomeSubject",
    base_body = "SomeBaseBody",
    text_body = "SomeTextBody",
    full_headers = Json.obj(),

    gmail_msg_id = None,

    gmail_thread_id = None,
    outlook_msg_id = None,
    outlook_conversation_id = None,

    outlook_response_json = None,
    team_id = TeamId(team_id),
    references_header = None,
    in_reply_to_header = None
  )

  describe("saveEmailsAndRepliesFromInboxV3") {
    val emailMessageTracked = EmailMessageTracked(
      inbox_email_setting_id = emailSetting.id.get.emailSettingId,
      from = IEmailAddress(email = "<EMAIL>"),
      to_emails = Seq(IEmailAddress(email = "<EMAIL>"), IEmailAddress(email = "<EMAIL>")),
      subject = "SomeSubject+NormalReplyAndAutoReply",
      body = "SomeBody+NormalReplyAndAutoReply",
      base_body = "SomeBaseBody+NormalReplyAndAutoReply",
      text_body = "SomeTextBody+NormalReplyAndAutoReply",
      references_header = None,
      campaign_id = Some(12),
      step_id = None,
      prospect_id_in_campaign = Some(122),
      prospect_account_id_in_campaign = None,
      campaign_name = None,
      step_name = None,
      received_at = DateTime.now().minusDays(2),
      recorded_at = DateTime.now().minusDays(2),
      sr_inbox_read = true,
      original_inbox_folder = None,
      email_status = EmailReplyStatus(
        replyType = EmailReplyType.NOT_CATEGORIZED,
        isReplied = true,
        isUnsubscribeRequest = false,
        isAutoReply = false,
        isOutOfOfficeReply = false,
        isInvalidEmail = false,
        isForwarded = false,
        bouncedData = None
      ),
      message_id = "some_message_id+NormalReplyAndAutoReply",
      full_headers = Json.obj(),
      scheduled_manually = false,
      reply_to = None,
      email_thread_id = None,
      gmail_msg_id = None,
      gmail_thread_id = None,
      outlook_msg_id = None,
      outlook_conversation_id = None,
      outlook_response_json = None,
      cc_emails = Seq(),
      in_reply_to_header = None,
      team_id = team_id,
      account_id = 1,
      internal_tracking_note = InternalTrackingNote.EXISTING_INREPLYTO,
      tempThreadId = Some(2)
    )

    it("emailMessages empty") {

      val result = emailReplyTrackingModelV2.saveEmailsAndRepliesFromInboxV3(
        accountId = 1,
        team_id = team_id,
        emailMessages = Seq(),
        inboxEmailSetting = emailSetting,

        replyHandling = ReplyHandling.PAUSE_SPECIFIC_CAMPAIGN_ON_REPLY,
        account = accountAdmin,

        senderEmails = Seq(),
        adminReplyFromSRInbox = false,

        auditRequestLogId = "someAuditRequestLogId",
        markProspectAsCompleted = false
      )

      Logger.info(s"result__________________$result")

      assert(result == Success(DBEmailMessagesSavedResponse(
        savedMessages = Seq(),
        emailMessagesFromProspects = Seq(),
        prospectIdsWithHardBouncedEmail = Seq(),
        prospectIdsWithSoftBouncedEmail = Seq(),
        emailScheduledIdsWithHardBouncedEmail = Seq(),
        emailScheduledIdsWithSoftBouncedEmail = Seq(),
        emailScheduledIdsWithAutoReplyEmail = Seq(),
        emailScheduledIdsWithOutOfOfficeReplyEmail = Seq(),
        completedWebhookData = Seq(),
        gmailSendingLimitErrorEmailSettingId = None,
        hardBouncedReplies = Seq(),
        softBouncedReplies = Seq()
      )))
    }

    it("emailMessages non empty and get saved") {

      (emailReplyTrackingModel._findIntermediateProspectsForReplyTracking(_:Long, _:Seq[String], _:Seq[Long])(using _:SRLogger))
        .expects(2, List("<EMAIL>", "<EMAIL>", "<EMAIL>"), List(122L), *)
        .returning(Success(List(ERIntermediateValidProspect(
          prospect_id = 122L,
          email = "<EMAIL>",
          email_domain = "email.com",
          account_id = 1L,
          team_id = team_id,
          ta_id = 1,
          prospect_account_id = None,
          prospect_category_id_custom = 14,
          last_contacted_at = Some(DateTime.now().minusDays(2))
        ))))

      (emailReplyTrackingModel.getIdAndNameForSelectedCampaignIds(_:Seq[Long]))
        .expects(Seq(12L))
        .returning(Success(Map()))

      (teamInboxDAO.getTeamInboxIdForEmailSetting(_:Long, _:Long))
        .expects(123L, 17L)
        .returning(Success(None))

      (() => srUuidUtils.generateEmailThreadsUuid())
        .expects()
        .returning("et_uuid_123")


      (emailThreadDAOService.insertNewThreads(_:Seq[NewEmailThreadV3], _:Boolean, _:SRLogger, _:Long))
        .expects(*, false, *, 2L)
        .returning(Success(Seq(SavedEmailThread(id = 666L, temp_thread_id = Some(2), folder_type = Some("prospects")))))

      (emailThreadDAOService.updateInboxFolderTypeTry(_:Seq[Long], _:TeamId, _:FolderType))
        .expects(*, TeamId(2), FolderType.IRRELEVANT)
        .returning(Success(0))

        (emailCommonService.associateProspectsWithEmailThreads(_:Seq[AssociateEmailThreadProspect],_:Long)(using _:SRLogger))
        .expects(Seq(AssociateEmailThreadProspect(666,122,Some(2))),*,*)
        .returning(Success(()))

      (emailReplyTrackingModel._insertTrackedRepliesV3(_:Seq[(EmailMessageTracked, List[ERIntermediateValidProspect])], _:Boolean)(using _:SRLogger))
        .expects(*, false, *)
        .returning(Success(Seq(emailReplySavedV3)))

      (teamService.checkIfWePushForSrAiApiCall(_: Boolean, _: TeamId, _: PlanID)(using _: SRLogger))
        .expects(*, *, *, *)
        .returning(Success(TeamSRAIFlags(
          allow_using_sr_ai_api_for_reply_sentiment = false,
          allow_using_sr_ai_api_for_ooo = false
        )))

      (emailThreadDAOService._updateLatestEmailId(_:Seq[EmailThreadUpdateLatestEmailData]))
        .expects(*)
        .returning(Seq(11L))

      (emailCommonService.saveEmailContacts(_:Seq[EmailCommonService.DataForEmailMessageContact],_:Seq[ProspectIdEmail],_:String, _: Long)(using _: SRLogger))
        .expects(*,*,*,*,*)
        .returning(())

      (prospectDAOService._updateProspectLastContactedAt(_:Seq[(Long, DateTime)], _:TeamId, _:ChannelType))
        .expects(Seq(), TeamId(2), ChannelType.EmailChannel)
        .returning(Success(Seq(19L)))

      (emailReplyTrackingDAOService.updateEmailStatusOnReplyAndCampaignProspectStatusOnReply(_:Seq[UpdateEmailStatusOnReplyV3])(using _:SRLogger))
        .expects(Seq(), *)
        .returning(Success(Seq(CPCompleted(campaignId = 12L, prospectId = 122L, completed = true))))

      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(_: TeamId, _: SrRollingUpdateFeature)(_: ISRLogger, _: Option[DBSession]))
        .expects(*, *, *, *)
        .anyNumberOfTimes()
        .returning(false)

      (replySentimentService.getReplySentimentsForTeam(_:Long, _:ReplySentimentChannelType)(_:SRLogger))
        .expects(2L, ReplySentimentChannelType.EmailChannelType, *)
        .returning(Success(List()))

      (emailReplyTrackingModel._getGmailSendingLimitReachedErrorSenderId(_:Seq[EmailReplySavedV3], _:SRLogger))
        .expects(*, *)
        .returning(None)

      val result: Try[DBEmailMessagesSavedResponse] = emailReplyTrackingModelV2.saveEmailsAndRepliesFromInboxV3(
        accountId = 1,
        team_id = team_id,
        emailMessages = Seq(emailMessageTracked),
        inboxEmailSetting = emailSetting,

        replyHandling = ReplyHandling.PAUSE_SPECIFIC_CAMPAIGN_ON_REPLY,
        account = accountAdmin,

        senderEmails = Seq(emailSetting.email),
        adminReplyFromSRInbox = false,

        auditRequestLogId = "someAuditRequestLogId",
        markProspectAsCompleted = false
      )

      result match {
        case Success(value) => //nothing
        case Failure(exception) => println(s"Error:: ${LogHelpers.getStackTraceAsString(exception)}")
      }

      assert(result.isSuccess && result.get.savedMessages.nonEmpty &&
      result.get.savedMessages.head.email_status.isReplied)
    }

    it("emailMessages non empty and not get saved becuase not having prospect/campaign/team_inbox") {

      val emailMessageTracked = EmailMessageTracked(
        inbox_email_setting_id = emailSetting.id.get.emailSettingId,
        from = IEmailAddress(email = "<EMAIL>"),
        to_emails = Seq(IEmailAddress(email = "<EMAIL>"), IEmailAddress(email = "<EMAIL>")),
        subject = "SomeSubject+NormalReplyAndAutoReply",
        body = "SomeBody+NormalReplyAndAutoReply",
        base_body = "SomeBaseBody+NormalReplyAndAutoReply",
        text_body = "SomeTextBody+NormalReplyAndAutoReply",
        references_header = None,
        campaign_id = None,
        step_id = None,
        prospect_id_in_campaign = None,
        prospect_account_id_in_campaign = None,
        campaign_name = None,
        step_name = None,
        received_at = DateTime.now().minusDays(2),
        recorded_at = DateTime.now().minusDays(2),
        sr_inbox_read = true,
        original_inbox_folder = None,
        email_status = EmailReplyStatus(
          replyType = EmailReplyType.NOT_CATEGORIZED,
          isReplied = true,
          isUnsubscribeRequest = false,
          isAutoReply = false,
          isOutOfOfficeReply = false,
          isInvalidEmail = false,
          isForwarded = false,
          bouncedData = None
        ),
        message_id = "some_message_id+NormalReplyAndAutoReply",
        full_headers = Json.obj(),
        scheduled_manually = false,
        reply_to = None,
        email_thread_id = None,
        gmail_msg_id = None,
        gmail_thread_id = None,
        outlook_msg_id = None,
        outlook_conversation_id = None,
        outlook_response_json = None,
        cc_emails = Seq(),
        in_reply_to_header = None,
        team_id = team_id,
        account_id = 1,
        internal_tracking_note = InternalTrackingNote.EXISTING_INREPLYTO,
        tempThreadId = Some(2)
      )


      (emailReplyTrackingModel._findIntermediateProspectsForReplyTracking(_: Long, _: Seq[String], _: Seq[Long])(using _: SRLogger))
        .expects(2, List("<EMAIL>", "<EMAIL>", "<EMAIL>"), List(), *)
        .returning(Success(List()))

      (teamInboxDAO.getTeamInboxIdForEmailSetting(_: Long, _: Long))
        .expects(123L, 17L)
        .returning(Success(None))

      (emailThreadDAOService.insertNewThreads(_: Seq[NewEmailThreadV3], _: Boolean, _: SRLogger, _: Long))
        .expects(*, false, *, 2L)
        .returning(Success(Seq()))

      (emailThreadDAOService.updateInboxFolderTypeTry(_: Seq[Long], _: TeamId, _: FolderType))
        .expects(*, TeamId(2), FolderType.IRRELEVANT)
        .returning(Success(0))

      (emailCommonService.associateProspectsWithEmailThreads(_: Seq[AssociateEmailThreadProspect], _: Long)(using _: SRLogger))
        .expects(Seq(), *, *)
        .returning(Success(()))

      val result: Try[DBEmailMessagesSavedResponse] = emailReplyTrackingModelV2.saveEmailsAndRepliesFromInboxV3(
        accountId = 1,
        team_id = team_id,
        emailMessages = Seq(emailMessageTracked),
        inboxEmailSetting = emailSetting,

        replyHandling = ReplyHandling.PAUSE_SPECIFIC_CAMPAIGN_ON_REPLY,
        account = accountAdmin,

        senderEmails = Seq(emailSetting.email),
        adminReplyFromSRInbox = false,

        auditRequestLogId = "someAuditRequestLogId",
        markProspectAsCompleted = false
      )

      result match {
        case Success(value) => //nothing
        case Failure(exception) => println(s"Error:: ${LogHelpers.getStackTraceAsString(exception)}")
      }

      assert(result.isSuccess && result.get.savedMessages.isEmpty)
    }


    //    it("") {
//
//      (emailReplyTrackingModel._findIntermediateProspectsForReplyTracking)
//        .expects(2, List("<EMAIL>", "<EMAIL>", "<EMAIL>"), List(890L))
//        .returning(List())
//
//     ( emailReplyTrackingModel.getIdAndLabelForSelectedStepIds)
//      .expects(List(456L))
//       .returning(Failure(Error))
//
//      (emailReplyTrackingModel.getIdAndNameForSelectedCampaignIds)
//      .expects(List(123L))
//        .returning(Failure(Error))
//
//      val result = emailReplyTrackingModelV2.saveEmailsAndRepliesFromInboxV3(
//        accountId = 1,
//        team_id = team_id,
//        emailMessages = Seq(emailMessageTracked),
//        inboxEmailSetting = emailSetting,
//
//        replyHandling = ReplyHandling.PAUSE_SPECIFIC_CAMPAIGN_ON_REPLY,
//        account = accountAdmin,
//
//        senderEmails = Seq(),
//        adminReplyFromSRInbox = false,
//
//        auditRequestLogId = Some("someAuditRequestLogId"),
//        SRLogger = Logger
//      )
//
//      Logger.info(s"result__________________$result")
//
//      assert(false)
//    }
  }

  describe("insertTrackedRepliesV3"){
    it("should throw exception when emailReplyTrackingModel._insertTrackedRepliesV3 returns failure"){

      (emailReplyTrackingModel._insertTrackedRepliesV3 (_:Seq[(EmailMessageTracked,List[ERIntermediateValidProspect])],_:Boolean)(using _:SRLogger))
        .expects(List((emailMessageTracked, List(eRIntermediateValidProspect))), true, Logger)
        .returning(Failure(new Throwable("emailReplyTrackingModel._insertTrackedRepliesV3 failed")))

      emailReplyTrackingModelV2.insertTrackedRepliesV3(
        newMsgsWithEmailThreadId = List((emailMessageTracked, List(eRIntermediateValidProspect))),
        sentManuallyFromSRInbox = true,
        inboxEmailSetting = emailSetting,
        allow_using_sr_ai_api = false,
        plan_id = accountAdmin.org.plan.plan_id,
        team_id = TeamId(team_id)) match {

        case Failure(exception) =>
          Logger.info(s"Error....... ${exception.getMessage}")
          assert(true)

        case Success(_) =>
          assert(false)
      }
    }

    it("should return seq of emailReplySavedV3 when emailReplyTrackingModel._insertTrackedRepliesV3 returns successfully"){

      val data = Seq(emailReplySavedV3).map(em => EmailThreadUpdateLatestEmailData(
          email_thread_id = em.email_thread_id,
          by_account = em.by_account,
          latest_email_id = em.email_scheduled_id,
          sent_at = em.sent_at,
          latest_sent_by_admin_at = if(emailSetting.email.trim.toLowerCase() == em.from_email.email.trim.toLowerCase()) Some(em.sent_at) else None,
          by_prospect = false,
          folderType = FolderType.NON_PROSPECTS
        ))
        .reverse // reverse chronologically

      (emailReplyTrackingModel._insertTrackedRepliesV3 (_:Seq[(EmailMessageTracked,List[ERIntermediateValidProspect])],_:Boolean)(using _:SRLogger))
        .expects(List((emailMessageTracked, List(eRIntermediateValidProspect))), true, Logger)
        .returning(Success(Seq(emailReplySavedV3)))

      (emailThreadDAOService._updateLatestEmailId)
        .expects(data)
        .returning(Seq(1))
      (teamService.checkIfWePushForSrAiApiCall(_: Boolean, _: TeamId, _: PlanID)(using _: SRLogger))
        .expects(*, *, *, *)
        .returning(Success(TeamSRAIFlags(
          allow_using_sr_ai_api_for_reply_sentiment = false,
          allow_using_sr_ai_api_for_ooo = false
        )))
      emailReplyTrackingModelV2.insertTrackedRepliesV3(
        newMsgsWithEmailThreadId = List((emailMessageTracked, List(eRIntermediateValidProspect))),
        sentManuallyFromSRInbox = true,
        inboxEmailSetting = emailSetting,
        allow_using_sr_ai_api = false,
        plan_id = accountAdmin.org.plan.plan_id,
        team_id = TeamId(team_id)) match {

        case Failure(exception) =>
          println(s"Error....... ${exception.getMessage}")
          assert(false)

        case Success(emailReplySavedV3Seq) =>
          Logger.info(s"Success: emailReplyTrackingModelV2.insertTrackedRepliesV3 result: $emailReplySavedV3Seq")
          assert(true)
      }
    }
  }


  describe("getUpdateReplySentimentFormForNewThreads"){

    val needs_more_info = "Needs more info"
    val wants_a_demo = "Wants a demo"
    val wants_to_meet = "Wants to meet"
    val miscellaneous = "Miscellaneous"
    val other_contact = "Other contact"
    val no_need = "No need"
    val already_has_a_solution = "Already has a solution"
    val not_right_fit = "Not right fit"
    val bad_timing = "Bad timing"
    val no_budget = "No Budget"
    val not_right_person = "Not right person"
    val unsubscribed = "Unsubscribed"
    val dnd = "Doesn't want to be contacted"
    val outOfOffice = "Out of office"
    val auto_reply = "Auto reply"
    val support_questions = "Support/Other Questions"
    val foreign_language = "Foreign Language"

    val listOfReplySentiment: List[ReplySentimentForTeam] = List(
      ReplySentimentForTeam(
        uuid = ReplySentimentUuid("a"),
        reply_sentiment = ReplySentimentTypeData.PositiveData(replySentimentSubCategory = ReplySentimentSubCategory.EmailPositiveNeedsMoreInfo)
        ),
      ReplySentimentForTeam(
        uuid = ReplySentimentUuid(uuid = "b"),
        reply_sentiment = ReplySentimentTypeData.PositiveData(replySentimentSubCategory = ReplySentimentSubCategory.EmailPositiveWantsDemo)
        ),
      ReplySentimentForTeam(
        uuid = ReplySentimentUuid(uuid = "c"),
        reply_sentiment = ReplySentimentTypeData.PositiveData(replySentimentSubCategory = ReplySentimentSubCategory.EmailPositiveWantsMeeting)
        ),
      ReplySentimentForTeam(
        uuid = ReplySentimentUuid(uuid = "d"),
        reply_sentiment = ReplySentimentTypeData.PositiveData(replySentimentSubCategory = ReplySentimentSubCategory.EmailFollowUpMiscellaneousNeedsClarification)
        ),
      ReplySentimentForTeam(
        uuid = ReplySentimentUuid(uuid = "e"),
        reply_sentiment = ReplySentimentTypeData.ReferralData(replySentimentSubCategory = ReplySentimentSubCategory.EmailNegativeOtherReason)
        ),
      ReplySentimentForTeam(
        uuid = ReplySentimentUuid(uuid = "f"),
        reply_sentiment = ReplySentimentTypeData.ObjectionData(replySentimentSubCategory = ReplySentimentSubCategory.EmailNegativeNoNeedBadFit)
        ),
      ReplySentimentForTeam(
        uuid = ReplySentimentUuid(uuid = "g"),
        reply_sentiment = ReplySentimentTypeData.ObjectionData(replySentimentSubCategory = ReplySentimentSubCategory.EmailNegativeUsingCompetitor)
      ),
      ReplySentimentForTeam(
        uuid = ReplySentimentUuid(uuid = "h"),
        reply_sentiment = ReplySentimentTypeData.ObjectionData(replySentimentSubCategory = ReplySentimentSubCategory.EmailNegativeNoNeedBadFit)
        ),
      ReplySentimentForTeam(
        uuid = ReplySentimentUuid(uuid = "i"),
        reply_sentiment = ReplySentimentTypeData.ObjectionData(replySentimentSubCategory = ReplySentimentSubCategory.EmailFollowUpNotRightTime)
        ),
      ReplySentimentForTeam(
        uuid = ReplySentimentUuid(uuid = "j"),
        reply_sentiment = ReplySentimentTypeData.ObjectionData(replySentimentSubCategory = ReplySentimentSubCategory.EmailNegativeNoBudget)
        ),
      ReplySentimentForTeam(
        uuid = ReplySentimentUuid(uuid = "k"),
        reply_sentiment = ReplySentimentTypeData.ObjectionData(replySentimentSubCategory = ReplySentimentSubCategory.EmailNegativeNotRightPerson)
        ),
      ReplySentimentForTeam(
        uuid = ReplySentimentUuid(uuid = "l"),
        reply_sentiment = ReplySentimentTypeData.ObjectionData(replySentimentSubCategory = ReplySentimentSubCategory.EmailFollowUpMiscellaneousNeedsClarification)
        ),
      ReplySentimentForTeam(
        uuid = ReplySentimentUuid(uuid = "m"),
        reply_sentiment = ReplySentimentTypeData.DoNotContactData(replySentimentSubCategory = ReplySentimentSubCategory.EmailNegativeUnsubscribe)
        ),
      ReplySentimentForTeam(
        uuid = ReplySentimentUuid(uuid = "n"),
        reply_sentiment = ReplySentimentTypeData.DoNotContactData(replySentimentSubCategory = ReplySentimentSubCategory.EmailNegativeDoNotContact)
        ),
      ReplySentimentForTeam(
        uuid = ReplySentimentUuid(uuid = "o"),
        reply_sentiment = ReplySentimentTypeData.OtherData(replySentimentSubCategory = ReplySentimentSubCategory.EmailFollowUpOutOfOffice)
        ),
      ReplySentimentForTeam(
        uuid = ReplySentimentUuid(uuid = "p"),
        reply_sentiment = ReplySentimentTypeData.OtherData(replySentimentSubCategory = ReplySentimentSubCategory.EmailFollowUpAutoReply)
        ),
      ReplySentimentForTeam(
        uuid = ReplySentimentUuid(uuid = "q"),
        reply_sentiment = ReplySentimentTypeData.OtherData(replySentimentSubCategory = ReplySentimentSubCategory.EmailNegativeSpeakToSupportNotSales)
        ),
      ReplySentimentForTeam(
        uuid = ReplySentimentUuid(uuid = "r"),
        reply_sentiment = ReplySentimentTypeData.OtherData(replySentimentSubCategory = ReplySentimentSubCategory.EmailFollowUpForeignLanguage)
        ),
      ReplySentimentForTeam(
        uuid = ReplySentimentUuid(uuid = "s"),
        reply_sentiment = ReplySentimentTypeData.OtherData(replySentimentSubCategory = ReplySentimentSubCategory.EmailFollowUpMiscellaneousNeedsClarification)
      )
    )

    val email_status = EmailReplyStatus(
      replyType = EmailReplyType.AUTO_REPLY,
      isReplied = false,
      bouncedData = None,
      isUnsubscribeRequest = false,
      isAutoReply = false,
      isOutOfOfficeReply = false,
      isInvalidEmail = false,
      isForwarded = false
    )
    val team_id: Long = 1234

    val newSavedMessage = EmailReplySavedV3(
      message_id = "message_id",
      email_body = "test body",
      email_scheduled_id = 1,
      email_thread_id = 2,
      by_account = true,
      sent_at = DateTime.now(),
      campaign_id = None,
      step_id = None,
      prospect_id_in_campaign = None,
      prospect_account_id_in_campaign = None,
      reply_type = EmailReplyType.AUTO_REPLY,
      from_email = IEmailAddress(email = "<EMAIL>"),
      to_emails = Seq(),
      cc_emails = Seq(),
      reply_to = None,
      inbox_email_setting_id = 3,
      from_prospect = None,
      to_prospects = Seq(),
      campaign_associated_prospect = None,
      all_prospects_involved = Seq(),
      email_status = email_status,
      subject = "SomeSubject",
      base_body = "SomeBaseBody",
      text_body = "SomeTextBody",
      full_headers = Json.obj(),

      gmail_msg_id = None,

      gmail_thread_id = None,
      outlook_msg_id = None,
      outlook_conversation_id = None,

      outlook_response_json = None,
      team_id = TeamId(team_id),
      references_header = None,
      in_reply_to_header = None
    )

    it("is out of office") {
      val result = EmailReplyTrackingModelV2.getUpdateReplySentimentFormForNewThreads(
        newSavedMessage = newSavedMessage.copy(email_status = email_status.copy(isOutOfOfficeReply = true)),
        listOfReplySentiment = listOfReplySentiment,
        orgId = emailSetting.org_id
      )

      assert(result.isDefined)
      assert(result.get._1.getCampaignProspectThreadList == Seq())
      assert(result.get._2.uuid == "o")

    }
    it("is Unsubscribe Request") {
      val result = EmailReplyTrackingModelV2.getUpdateReplySentimentFormForNewThreads(
        newSavedMessage = newSavedMessage.copy(email_status = email_status.copy(isUnsubscribeRequest = true)),
        listOfReplySentiment = listOfReplySentiment,
        orgId = emailSetting.org_id
      )

      assert(result.isDefined)
      assert(result.get._1.getCampaignProspectThreadList == Seq())
      assert(result.get._2.uuid == "m")

    }

    it("is Auto Reply") {
      val result = EmailReplyTrackingModelV2.getUpdateReplySentimentFormForNewThreads(
        newSavedMessage = newSavedMessage.copy(email_status = email_status.copy(isAutoReply = true)),
        listOfReplySentiment = listOfReplySentiment,
        orgId = emailSetting.org_id
      )

      assert(result.isDefined)
      assert(result.get._1.getCampaignProspectThreadList == Seq())
      assert(result.get._2.uuid == "p")

    }

    it("is Normal reply") {
      val result = EmailReplyTrackingModelV2.getUpdateReplySentimentFormForNewThreads(
        newSavedMessage = newSavedMessage,
        listOfReplySentiment = listOfReplySentiment,
        orgId = emailSetting.org_id
      )

      assert(result.isEmpty)

    }
  }

  describe("EmailReplyTrackingModelV2.getFolderTypeOfEmailThread") {
    it("should return irrelevant folderType if email is hardbounced") {

      val res: FolderType = EmailReplyTrackingModelV2.getFolderTypeOfEmailThread(
        email_reply_status = Some(emailReplyStatus.copy(
          bouncedData = Some(BounceData(
            bounced_at = DateTime.now().minusDays(10),
            bounce_type = EmailReplyBounceType.EmailAddressNotFound,
            bounce_reason = "Email address not found",
            is_soft_bounced = false
          ))
        )),
        has_prospect = false,
        email_subject = "New year greetings!"
      )

      assert(res == FolderType.IRRELEVANT)
    }

    it("should return irrelevant folderType if email is warmup") {

      val res: FolderType = EmailReplyTrackingModelV2.getFolderTypeOfEmailThread(
        email_reply_status = Some(emailReplyStatus.copy(
          bouncedData = Some(BounceData(
            bounced_at = DateTime.now().minusDays(10),
            bounce_type = EmailReplyBounceType.None,
            bounce_reason = "soft bounced",
            is_soft_bounced = true
          ))
        )),
        has_prospect = false,
        email_subject = "Friday deal | wrmpbx"
      )

      assert(res == FolderType.IRRELEVANT)
    }

    it("should return prospects folderType if has_prospect") {

      val res: FolderType = EmailReplyTrackingModelV2.getFolderTypeOfEmailThread(
        email_reply_status = Some(emailReplyStatus.copy(
          bouncedData = None
        )),
        has_prospect = true,
        email_subject = "Friday deal!"
      )

      assert(res == FolderType.PROSPECTS)
    }

    it("should return non-prospects folderType if has_prospect is false and no bounced data/email_reply_status ") {

      val res: FolderType = EmailReplyTrackingModelV2.getFolderTypeOfEmailThread(
        email_reply_status = None,
        has_prospect = false,
        email_subject = "Friday deal!"
      )

      assert(res == FolderType.NON_PROSPECTS)
    }
  }

}
