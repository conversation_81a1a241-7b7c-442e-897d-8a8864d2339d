package app.utils.email

import api.AppConfig
import api.accounts.models.{OrgId, ProspectAccountUuid}
import api.accounts.{AccountService, AccountUuid, TeamId}
import api.calendar_app.models.CalendarAccountData
import api.campaigns.models.PreviousFollowUpData
import api.campaigns.{CampaignEditedPreviewEmail, CampaignEditedPreviewEmailDAO, PreviousFollowUp}
import api.columns.InternalMergeTagValuesForProspect
import api.emails.{EmailScheduledDAO, EmailsScheduledUuid}
import api.emails.dao_service.EmailScheduledDAOService
import api.prospects.{ProspectAccount, ProspectUuid}
import app.test_fixtures.prospect.{ProspectAccountFixture, ProspectFixtures}
import eventframework.{ProspectObject, ProspectObjectInternal}
import org.joda.time.DateTime
import org.scalamock.scalatest.MockFactory
import org.scalatest.funspec.AnyFunSpec
import org.scalatest.flatspec.AnyFlatSpec
import play.api.Logging
import play.api.libs.json.Json
import sr_scheduler.models.ChannelType
import utils.SRLogger
import utils.email.{EmailBodyService, EmailOptionsForGetBodies, EmailService, EmailServiceBody, EmailServiceCompanion}
import utils.templating.TemplateService

import scala.util.{Failure, Success}

class EmailServiceCompanionSpec extends AnyFunSpec with MockFactory {


  val mockTemplateService = mock[TemplateService]
  val mockEmailScheduledDAOService = mock[EmailScheduledDAOService]
  val mockCampaignEditedPreviewEmailDAO = mock[CampaignEditedPreviewEmailDAO]
  val templateService = new TemplateService
  val accountService = mock[AccountService]
  //val emailService: EmailService = mock[EmailService]
  val emailBodyService : EmailBodyService = mock[EmailBodyService]
  val emailServiceCompanion = new EmailServiceCompanion(
    templateService = mockTemplateService,
    campaignEditedPreviewEmailDAO = mockCampaignEditedPreviewEmailDAO,
    emailScheduledDAOService = mockEmailScheduledDAOService,
    emailBodyService = emailBodyService
  )
  given logger: SRLogger = new SRLogger("[EmailServiceCompanionSpec]")
  val emailServiceCompanionWithoutMock = new EmailServiceCompanion(
    templateService = templateService,
    campaignEditedPreviewEmailDAO = mockCampaignEditedPreviewEmailDAO,
    emailScheduledDAOService = mockEmailScheduledDAOService,
    emailBodyService = emailBodyService
  )

  val prospectObjectInternal = ProspectFixtures.prospectObjectInternal

  val prospectObject = ProspectObject(
    id = 1,
    owner_id = 1,
    team_id = 1,
    first_name = Some("Animesh"),
    last_name = Some("Kumar"),
    email = Some("<EMAIL>"),
    custom_fields = Json.obj("customField" -> "this is a custom filed"),
    list = None,
    job_title = Some("SDE"),
    company = Some("Smartreach"),
    linkedin_url = Some("AnimeshKumar1234"),
    phone = None,
    phone_2 = None,
    phone_3 = None,
    city = Some("India"),
    state = Some("Maha"),
    country = Some("India"),
    timezone = Some("IN"),
    prospect_category = "",
    last_contacted_at = None,
    last_contacted_at_phone = None,
    created_at = DateTime.now().minusMonths(10),
    internal = prospectObjectInternal,
    latest_reply_sentiment_uuid = None,
    current_step_type = None,
    latest_task_done_at = None,
    prospect_uuid = Some(ProspectUuid("prs_aa_abcdefghi")),
    owner_uuid = AccountUuid("acc_aa_abcdegfhi"),
    updated_at = DateTime.now()
  )

  val channel_follow_up_data = PreviousFollowUpData.AutoEmailFollowUp(
    email_thread_id = Some(1), // it is 1 before itself we are just fixing compile error now Date: 16/03/2023
    from_name = "Prateek Bhat",
    base_body = "This is previous test body",
    body = "This is previous test body",
    subject = "Hey {{first_name}}",
    from_email = "<EMAIL>",
    is_edited_preview_email = false,
  )

  val previousFollowUp = PreviousFollowUp(
    channel_follow_up_data = channel_follow_up_data,
    sent_at = DateTime.now().minusDays(5),
    timezone = "IN",
    step_id = Some(1),
    completed_reason = None
  )
  val internalMergeTagValuesForProspect = InternalMergeTagValuesForProspect(
    sender_name = "Prateek Bhat",
    sender_first_name = "Prateek",
    sender_last_name = "Bhat",
    unsubscribe_link = Some("https:///tv4/GFPV6XZQGE4TONRXMZRC2OJYMMZS2N3DHFRS2OJUGU4C2MJZGNSTQYJTGZTDSZLG/optout"),
    previous_subject = Some("Hey {{first_name}}"),
    signature = Some(""),
    sender_phone_number = None,
    calendar_link = Some("http://localhost:3000/prateek-bhat/15min/?-151PRDGct87LPw16ziecQyjOb5nbEOs_JZ5YxIjmFE&tid=1")
  )

  val campaignEditedPreviewEmail = CampaignEditedPreviewEmail(
    campaignId = 1,
    prospectId = 1,
    stepId = 1,
    editedByAccountId = 1,
    editedSubject = "this is previous email",
    editedBody = "test body"
  )

  val Error = new Throwable("error")
  val emailOptions = EmailOptionsForGetBodies(
    for_editable_preview = false,
    editedPreviewEmailAlreadyChecked = false,


    custom_tracking_domain = None,
    default_tracking_domain = "",
    default_unsubscribe_domain = "",
    opt_out_msg = "{{unsubscribe_link}}",
    opt_out_is_text = false,
    append_followups = false,
    signature = None,

    bodyTemplate = "this is a test",
    subjectTemplate = "Hey {{first_name}}",
    email_sender_name = "Prateek Bhat",
    sender_first_name = "Prateek",
    sender_last_name = "Bhat",

    manualEmail = false,
    trackOpens = false,
    trackClicks = false,
    previousEmails = Seq(previousFollowUp),
    allTrackingDomainsUsed = Seq()
  )
  val calendarAccountData = None
  val calendarSettingsData = None

  val channel: ChannelType = ChannelType.EmailChannel

  describe("getBodies") {

    it("render fails and should send error find preview email <no effect on the flow>") {

      (mockTemplateService.render(_: String, _: ProspectObject, _: InternalMergeTagValuesForProspect, _: ChannelType)(using _: SRLogger))
        .expects("this is a test", prospectObject, internalMergeTagValuesForProspect.copy(calendar_link = None), channel, *)
        .returning(Failure(Error))
      (mockCampaignEditedPreviewEmailDAO.find)
        .expects(Some(2.toLong), None, 1)
        .returning(Failure(Error))

      val result = emailServiceCompanion.getBodies(
        editedPreviewEmail = None,

        org_id = 1,
        calendarAccountData = calendarAccountData,
        selectedCalendarData = None,
        head_step_id = Some(1),
        emailsScheduledUuid = EmailsScheduledUuid(AppConfig.dummy_email_tracking_uuid),
        campaign_id = Some(1),
        step_id = Some(2),

        prospect = prospectObject,
        emailOptions = emailOptions
      )

      logger.info(s"result - $result")
      assert(result == Failure(Error))

    }

    it("is with campaign_id and no default unsub domain invalid unsub link because no custom_tracking_domain") {

      val result = emailServiceCompanion.getBodies(
        editedPreviewEmail = None,

        org_id = 1,
        calendarAccountData = calendarAccountData,
        selectedCalendarData = None,
        head_step_id = Some(1),
        emailsScheduledUuid = EmailsScheduledUuid(AppConfig.dummy_email_tracking_uuid),
        campaign_id = Some(2),
        step_id = Some(2),

        prospect = prospectObject,
        emailOptions = emailOptions.copy(opt_out_msg = "")

      )

      assert(result.failed.get.getMessage == "Invalid unsubscribe link")

    }

    it("is with edited preview email should change subject and body accordingly but throws error because render failed to give subject") {


      (mockTemplateService.render(_: String, _: ProspectObject, _: InternalMergeTagValuesForProspect, _: ChannelType)(using _: SRLogger))
        .expects("test body", prospectObject, internalMergeTagValuesForProspect.copy(calendar_link = None), channel, *)
        .returning(Success("test body"))

      (mockTemplateService.render(_: String, _: ProspectObject, _: InternalMergeTagValuesForProspect, _: ChannelType)(using _: SRLogger))
        .expects("this is previous email", prospectObject, internalMergeTagValuesForProspect.copy(calendar_link = None), channel, *)
        .returning(Failure(Error))

      val result = emailServiceCompanion.getBodies(
        editedPreviewEmail = Some(campaignEditedPreviewEmail),

        org_id = 1,
        calendarAccountData = calendarAccountData,
        selectedCalendarData = None,
        head_step_id = Some(1),
        emailsScheduledUuid = EmailsScheduledUuid(AppConfig.dummy_email_tracking_uuid),
        campaign_id = Some(1),
        step_id = Some(2),


        prospect = prospectObject,

        emailOptions = emailOptions.copy(
          opt_out_msg = "{{unsubscribe_link}}",
          trackOpens = true
        )


      )

      logger.info(s"result - $result")
      assert(result == Failure(Error))

    }

    it("is with edited preview email should change subject and body accordingly and passes") {


      (mockTemplateService.render(_: String, _: ProspectObject, _: InternalMergeTagValuesForProspect, _: ChannelType)(using _: SRLogger))
        .expects("test body", prospectObject, internalMergeTagValuesForProspect.copy(calendar_link = None), channel, *)
        .returning(Success("test body"))

      (mockTemplateService.render(_: String, _: ProspectObject, _: InternalMergeTagValuesForProspect, _: ChannelType)(using _: SRLogger))
        .expects("this is previous email", prospectObject, internalMergeTagValuesForProspect.copy(calendar_link = None), channel, *)
        .returning(Success("this is previous email"))

      val result = emailServiceCompanion.getBodies(
        editedPreviewEmail = Some(campaignEditedPreviewEmail),

        org_id = 1,
        calendarAccountData = calendarAccountData,
        selectedCalendarData = None,
        head_step_id = Some(1),
        emailsScheduledUuid = EmailsScheduledUuid(AppConfig.dummy_email_tracking_uuid),
        campaign_id = Some(1),
        step_id = Some(2),

        prospect = prospectObject,

        emailOptions = emailOptions.copy(
          opt_out_msg = "{{unsubscribe_link}}",
          trackOpens = true
        )

      ).get

      logger.info(s"result - $result")
      assert(result.isEditedPreviewEmail)
      assert(result.subject == "this is previous email")
      assert(result.textBody == "test body")
      assert(result.baseBody == "test body")
      assert(result.has_unsubscribe_link)
      assert(result.htmlBody.contains("test body"))

    }

    it(" render fails should send error for subject") {

      (mockCampaignEditedPreviewEmailDAO.find)
        .expects(Some(2.toLong), None, 1)
        .returning(Success(Seq(campaignEditedPreviewEmail)))


      (mockTemplateService.render(_: String, _: ProspectObject, _: InternalMergeTagValuesForProspect, _: ChannelType)(using _: SRLogger))
        .expects("test body", prospectObject, internalMergeTagValuesForProspect.copy(calendar_link = None), channel, *)
        .returning(Success("test body"))

      (mockTemplateService.render(_: String, _: ProspectObject, _: InternalMergeTagValuesForProspect, _: ChannelType)(using _: SRLogger))
        .expects("this is previous email", prospectObject, internalMergeTagValuesForProspect.copy(calendar_link = None), channel, *)
        .returning(Failure(Error))

      val result = emailServiceCompanion.getBodies(
        editedPreviewEmail = None,

        org_id = 1,
        calendarAccountData = calendarAccountData,
        selectedCalendarData = None,
        head_step_id = Some(1),
        emailsScheduledUuid = EmailsScheduledUuid(AppConfig.dummy_email_tracking_uuid),
        campaign_id = Some(1),
        step_id = Some(2),

        prospect = prospectObject,

        emailOptions = emailOptions.copy(
          opt_out_msg = "{{unsubscribe_link}}"
        )

      )

      logger.info(s"result - $result")
      assert(result == Failure(Error))

    }

    val successResponse = EmailServiceBody(
      subject = "Hey Animesh",
      textBody = "Animesh kumar this is a test",
      htmlBody =
        """<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
          |       <html xmlns="http://www.w3.org/1999/xhtml">
          |       <head>
          |       <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
          |       <title></title>
          |       <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
          |       <style type="text/css">
          |        p { margin: 0; font-size: '14px' }
          |
          |        .prev-reply {
          |          margin-left: 1em;
          |          color: #500050;
          |          padding-top: 20px;
          |        }
          |
          |        .time-details {}
          |
          |        .reply {
          |          border-left: 1px solid black;
          |          padding-left: 1em;
          |        }
          |       </style>
          |       </head>
          |       <body>Animesh kumar this is a test
          |
          |       </body>
          |       </html>""".stripMargin,
      baseBody = "Animesh kumar this is a test",
      isEditedPreviewEmail = false,
      has_unsubscribe_link = false)


    it("is with track open and track clicks should success both body and subject") {
      (mockCampaignEditedPreviewEmailDAO.find)
        .expects(Some(2.toLong), None, 1)
        .returning(Success(Seq()))

    (emailBodyService._getSignatureWithTrackingLinks (_: String, _: EmailsScheduledUuid, _: Option[String], _: String, _: Seq[String], _: TeamId, _: OrgId)(using _: SRLogger))
      .expects("Animesh kumar", EmailsScheduledUuid(AppConfig.dummy_email_tracking_uuid), None, "", Seq(), TeamId(1), OrgId(1), *)
      .returning("Animesh kumar")

    (emailBodyService._getBodyWithTrackingLinksV1(_: String, _: EmailsScheduledUuid, _: Option[String], _: String, _: Seq[String], _: TeamId, _: OrgId)(using _: SRLogger))
      .expects("Animesh kumar this is a test", EmailsScheduledUuid(AppConfig.dummy_email_tracking_uuid), None, "", Seq(), TeamId(1), OrgId(1), *)
      .returning("Animesh kumar this is a test")

      val result = emailServiceCompanionWithoutMock.getBodies(
        editedPreviewEmail = None,

        org_id = 1,
        calendarAccountData = calendarAccountData,
        selectedCalendarData = None,

        head_step_id = Some(1),
        emailsScheduledUuid = EmailsScheduledUuid(AppConfig.dummy_email_tracking_uuid),
        campaign_id = Some(1),
        step_id = Some(2),

        prospect = prospectObject,

        emailOptions = emailOptions.copy(
          signature = Some("Animesh kumar"),
          opt_out_msg = "{{unsubscribe_link}}",
          bodyTemplate = "{{signature}} this is a test",
          trackOpens = true,
          trackClicks = true
        )

      ).get

      logger.info(s"result - $result")
      assert(result.isEditedPreviewEmail == successResponse.isEditedPreviewEmail)
      assert(result.subject == successResponse.subject)
      assert(result.textBody == successResponse.textBody)
      assert(result.baseBody == successResponse.baseBody)
      assert(!successResponse.has_unsubscribe_link)
      assert(result.htmlBody.contains("this is a test"))
      assert(result.htmlBody.contains("Animesh kumar"))
      assert(result.htmlBody.indexOf("this is a test") > result.htmlBody.indexOf("Animesh kumar"))

    }

    it("is with enableFullpagePluginForOrgIdAbove and trackOpens and !for_editable_preview should success both with different parameters") {
      (mockCampaignEditedPreviewEmailDAO.find)
        .expects(Some(2.toLong), None, 1)
        .returning(Success(Seq()))

      (emailBodyService._getSignatureWithTrackingLinks(_: String, _: EmailsScheduledUuid, _: Option[String], _: String, _: Seq[String], _: TeamId, _: OrgId)(using _: SRLogger))
        .expects("Animesh kumar", EmailsScheduledUuid(AppConfig.dummy_email_tracking_uuid), None, "", Seq(), TeamId(1), OrgId(348), *)
        .returning("Animesh kumar")

      (emailBodyService._getBodyWithTrackingLinksV2(_: String, _: EmailsScheduledUuid, _: Option[String], _: String, _: Seq[String], _:TeamId, _: OrgId)(using _: SRLogger))
        .expects("Animesh kumar this is a test", EmailsScheduledUuid(AppConfig.dummy_email_tracking_uuid), None, "", Seq(), TeamId(1), OrgId(348), *)
        .returning("Animesh kumar this is a test")

      val result = emailServiceCompanionWithoutMock.getBodies(
        editedPreviewEmail = None,

        org_id = 348,
        calendarAccountData = calendarAccountData,
        selectedCalendarData = None,
        head_step_id = Some(1),
        emailsScheduledUuid = EmailsScheduledUuid(AppConfig.dummy_email_tracking_uuid),
        campaign_id = Some(1),
        step_id = Some(2),

        prospect = prospectObject,
        emailOptions = emailOptions.copy(
          signature = Some("Animesh kumar"),
          opt_out_msg = "{{unsubscribe_link}}",
          bodyTemplate = "{{signature}} this is a test",
          trackOpens = true,
          trackClicks = true
        )

      ).get

      logger.info(s"result - ${result.textBody} :: ${successResponse.textBody}")
      assert(result.isEditedPreviewEmail == successResponse.isEditedPreviewEmail)
      assert(result.subject == successResponse.subject)
      assert(result.textBody == successResponse.textBody)
      assert(result.baseBody == successResponse.baseBody)
      assert(!successResponse.has_unsubscribe_link)
      assert(result.htmlBody.contains("Animesh kumar this is a test"))
      assert(result.htmlBody.contains("Animesh kumar"))
      assert(result.htmlBody.indexOf("this is a test") > result.htmlBody.indexOf("Animesh kumar"))
    }

    it("is with for editable preview, manual email, and opt out message should pass case with for editable preview") {
      (mockCampaignEditedPreviewEmailDAO.find)
        .expects(Some(2.toLong), None, 1)
        .returning(Success(Seq()))

      val result = emailServiceCompanionWithoutMock.getBodies(
        editedPreviewEmail = None,

        org_id = 348,
        calendarAccountData = calendarAccountData,
        selectedCalendarData = None,
        head_step_id = Some(1),
        emailsScheduledUuid = EmailsScheduledUuid(AppConfig.dummy_email_tracking_uuid),
        campaign_id = Some(1),
        step_id = Some(2),

        prospect = prospectObject,

        emailOptions = emailOptions.copy(
          for_editable_preview = true,
          signature = Some("Animesh kumar"),
          opt_out_msg = "abcd",
          opt_out_is_text = true,
          bodyTemplate = "Animesh kumar this is a test",
          manualEmail = true,
          trackOpens = true,
          trackClicks = true
        )
      ).get

      logger.info(s"result - $result")
      assert(result.isEditedPreviewEmail == successResponse.isEditedPreviewEmail)
      assert(result.subject == successResponse.subject)
      assert(result.textBody == successResponse.textBody)
      assert(result.baseBody == successResponse.baseBody)
      assert(result.has_unsubscribe_link == successResponse.has_unsubscribe_link)
      assert(result.htmlBody.contains("this is a test"))
      assert(result.htmlBody.contains("Animesh kumar"))

    }

  }

  describe("getInternalMergeTag") {

    val internalMergeTagValuesForProspect = InternalMergeTagValuesForProspect(
      sender_name = "Animesh Kumar",
      sender_first_name = "Animesh",
      sender_last_name = "Kumar",
      unsubscribe_link = None,
      previous_subject = None,
      signature = None,
      sender_phone_number = None,
      calendar_link = None
    )
    it("should give internalMergeTagValuesForProspect while we give no stepid") {
      val result = emailServiceCompanion.getInternalMergeTag(
        sender_name = "Animesh Kumar",
        sender_first_name = "Animesh",
        sender_last_name = "Kumar",
        unsubscribe_link = None,
        stepId = None,
        previousEmails = Seq(),
        campaignId = 1,
        prospectId = 1,
        head_step_id = None,
        signature = None,
        calendar_link = None,
        teamId = TeamId(id = 1)
      ).get
      assert(result == internalMergeTagValuesForProspect)
    }

    it("should give internalMergeTagValuesForProspect while we give stepId as 1") {
      val result = emailServiceCompanion.getInternalMergeTag(
        sender_name = "Animesh Kumar",
        sender_first_name = "Animesh",
        sender_last_name = "Kumar",
        unsubscribe_link = None,
        stepId = Some(1),
        previousEmails = Seq(),
        campaignId = 1,
        prospectId = 1,
        head_step_id = Some(1),
        signature = None,
        calendar_link = None,
        teamId = TeamId(id = 1)
      ).get
      assert(result == internalMergeTagValuesForProspect)
    }

    it("should give internalMergeTagValuesForProspect while we give stepId as 2 with a previous email") {
      val result = emailServiceCompanion.getInternalMergeTag(
        sender_name = "Animesh Kumar",
        sender_first_name = "Animesh",
        sender_last_name = "Kumar",
        unsubscribe_link = None,
        stepId = Some(2),
        previousEmails = Seq(previousFollowUp),
        campaignId = 1,
        prospectId = 1,
        head_step_id = Some(1),
        signature = None,
        calendar_link = None,
        teamId = TeamId(id = 1)
      ).get
      assert(result == internalMergeTagValuesForProspect.copy(previous_subject = Some(channel_follow_up_data.subject)))
    }

    it("should give internalMergeTagValuesForProspect while we give stepId as 2 with no previous email and the getPreviousSentSteps fails") {

      (mockEmailScheduledDAOService.getPreviousSentSteps(_: Long, _: Long, _: TeamId, _: Boolean)(using _: SRLogger))
        .expects(1, 1, TeamId(id = 1L), *, *)
        .returning(Failure(Error))

      val result = emailServiceCompanion.getInternalMergeTag(
        sender_name = "Animesh Kumar",
        sender_first_name = "Animesh",
        sender_last_name = "Kumar",
        unsubscribe_link = None,
        stepId = Some(2),
        previousEmails = Seq(),
        campaignId = 1,
        prospectId = 1,
        head_step_id = Some(1),
        signature = None,
        calendar_link = None,
        teamId = TeamId(id = 1)
      )

      assert(result == Failure(Error))

    }


    it("should give internalMergeTagValuesForProspect while we give stepId as 2 with no previous email and the getPreviousSentSteps send a previous emails") {

      (mockEmailScheduledDAOService.getPreviousSentSteps(_: Long, _: Long, _: TeamId, _: Boolean)(using _: SRLogger))
        .expects(1, 1, TeamId(id = 1L), *, *)
        .returning(Success(Seq(previousFollowUp)))

      val result = emailServiceCompanion.getInternalMergeTag(
        sender_name = "Animesh Kumar",
        sender_first_name = "Animesh",
        sender_last_name = "Kumar",
        unsubscribe_link = None,
        stepId = Some(2),
        previousEmails = Seq(),
        campaignId = 1,
        prospectId = 1,
        head_step_id = Some(1),
        signature = None,
        calendar_link = None,
        teamId = TeamId(id = 1)
      )

      assert(result.get == internalMergeTagValuesForProspect.copy(previous_subject = Some(channel_follow_up_data.subject)))

    }
  }
}