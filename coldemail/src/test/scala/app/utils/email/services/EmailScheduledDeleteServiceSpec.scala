package app.utils.email.services

import org.apache.pekko.actor.ActorSystem
import api.accounts.email.models.EmailServiceProvider
import api.accounts.{Account, AccountAccess, AccountDAO, AccountMetadata, AccountService, AccountType, AccountUuid, OrgCountData, OrgMetadata, OrgPlan, OrgSettings, OrganizationRole, OrganizationWithCurrentData, PermType, PermissionLevelForValidation, PermissionOwnershipV2, ProspectCategoriesInDB, ReplyHandling, RolePermV2, RolePermissionDataDAOV2, RolePermissionDataV2, RolePermissionsInDBV2, RolePermissionsV2, TeamAccount, TeamAccountRole, TeamId, TeamMember, TeamMemberLite}
import api.accounts.models.{AccountId, AccountProfileInfo, OrgId}
import api.accounts.service.AccountOrgBillingRelatedService
import api.calendar_app.models.CalendarAccountData
import api.campaigns.dao.CampaignSchedulingMetadataDAO
import api.campaigns.services.{CampaignCacheService, CampaignDAOService, CampaignId}
import api.campaigns.{CPCompleted, CPRepliedEvent, CampaignDAO, CampaignIdAndTeamId, CampaignProspectDAO, CampaignProspectUpdateScheduleStatus, CampaignStepWithChildren}
import api.campaigns.models.{CampaignProspectRevertData, CampaignStepType, CurrentStepStatusForScheduler, SendEmailFromCampaignDetails, StepDetails}
import api.columns.InternalMergeTagValuesForProspect
import api.emails.dao_service.{EmailScheduledDAOService, EmailThreadDAOService}
import api.emails.models.{DeletionReason, EmailMessageContact, EmailMessageContactModel, EmailSettingUuid, MessageContactType}
import api.emails.services.EmailSettingService
import api.emails.{AssociateEmailThreadProspect, CampaignProspectStepScheduleLogsDAO, DBEmailMessagesSavedResponse, ERIntermediateValidProspect, EmailHandleErrorService, EmailMessageTracked, EmailReplySavedV3, EmailReplyTrackingModel, EmailReplyTrackingModelV2, EmailScheduled, EmailScheduledDAO, EmailSetting, EmailSettingDAO, EmailThreadDAO, EmailThreadUuid, EmailToBeSent, NewEmailThreadV3, SavedEmailThread}
import api.linkedin_message_threads.LinkedinMessageThreadsDAO
import api.prospects.dao_service.ProspectDAOService
import api.prospects.models.{ProspectCategory, ProspectId, StepId}
import api.prospects.service.ProspectServiceV2
import api.prospects.{ProspectAccount, ProspectCheckForIsSentEmail, ProspectUuid}
import api.tasks.services.TaskService
import app.test_fixtures.accounts.OrgCountDataFixture
import app.test_fixtures.organizationa.{OrgMetadataFixture, OrgPlanFixture}
import app.test_fixtures.prospect.ProspectAccountFixture
import scalikejdbc.{DBSession, SettingsProvider}
import utils.dbutils.{DBUtils, DbAndSession}
import utils.testapp.TestAppExecutionContext
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService
//import api.scylla.service.CacheEventScyllaService
import api.sr_audit_logs.models.EventType
import api.team.TeamUuid
import api.team_inbox.model.FolderType
import eventframework.{ProspectObject, ProspectObjectInternal, SrResourceTypes}
import io.smartreach.esp.api.emails.{EmailSettingId, IEmailAddress}
import org.joda.time.DateTime
import org.scalamock.matchers.ArgCapture.CaptureOne
import org.scalamock.scalatest.AsyncMockFactory
import org.scalatest.funspec.AsyncFunSpec
import play.api.libs.json.{JsValue, Json}
import play.api.libs.ws.WSClient
import play.api.libs.ws.ahc.AhcWSClient
import sr_scheduler.models.ChannelType
import utils.SRLogger
import utils.cache_utils.model.CampaignUseStatusForEmailSetting
import utils.email.services.InternalTrackingNote
import utils.email.{EmailReplyStatus, EmailSenderService, EmailService, EmailsScheduledDeleteService, GmailApiReplyTrackingService, MailgunService, OutlookApiSendEmailService, SREmailErrors, SendGridService, SmtpImapSendEmailService}
import utils.mq.trackingapp.OpenTracker.MQOpenTrackerMessage
import utils.mq.webhook.mq_activity_trigger.{MQActivityTriggerMsgForm, MQActivityTriggerService}
import utils.mq.webhook.{MQWebhookCompleted, MQWebhookEmailInvalid}
import utils.templating.TemplateService
import utils.uuid.SrUuidUtils

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}

class EmailScheduledDeleteServiceSpec extends AsyncFunSpec with AsyncMockFactory {

  implicit lazy val system: ActorSystem = TestAppExecutionContext.actorSystem
  implicit lazy val wSClient: AhcWSClient = TestAppExecutionContext.wsClient
  implicit lazy val actorContext: ExecutionContext = system.dispatcher

  given Logger: SRLogger = new SRLogger("Unit test")
  implicit val session: DBSession = DbAndSession(null, null).session
  val Error = new Throwable("ERROR")


//  val emailScheduledDAOService = mock[EmailScheduledDAOService]
  val emailScheduledDAO = mock[EmailScheduledDAO]
  val campaignProspectDAO = mock[CampaignProspectDAO]
  val emailSettingDAO = mock[EmailSettingDAO]
  val dbUtils: DBUtils = mock[DBUtils]
  val campaignDAO = mock[CampaignDAO]
  val campaignDAOService = mock[CampaignDAOService]
//  val srRollingUpdateCoreService = mock[SrRollingUpdateCoreService]
  val taskService = mock[TaskService]
  val campaignProspectStepScheduleLogsDAO = mock[CampaignProspectStepScheduleLogsDAO]


  val emailsScheduledDeleteService = new EmailsScheduledDeleteService(
    emailScheduledDAO = emailScheduledDAO,
    campaignProspectDAO = campaignProspectDAO,
    emailSettingDAO = emailSettingDAO,
    dbUtils = dbUtils,
    campaignDAO = campaignDAO,
//    emailScheduledDAOService = emailScheduledDAOService,
    campaignDAOService = campaignDAOService,
    taskService = taskService,
//    srRollingUpdateCoreService = srRollingUpdateCoreService,
    campaignProspectStepScheduleLogsDAO = campaignProspectStepScheduleLogsDAO
  )



  describe("testing new mcRevert"){

    val step_id = StepId(id = 23)
    val team_id: TeamId = TeamId(id = 13)
    val prospect_id = ProspectId(29)
    val campaign_id = CampaignId(37)
    val reverted_by = "reverted_by_test_cases"

    val campaign_prospect_revert_data: CampaignProspectRevertData = CampaignProspectRevertData(
      current_step_id = Some(step_id),
      current_step_status_for_scheduler = Some(CurrentStepStatusForScheduler.Due),
      prospect_id = prospect_id,
      campaignId = campaign_id,
      team_id = team_id
    )

    val campaignStepWithChildren = CampaignStepWithChildren(
      id = step_id.id,
      label = Some("A"),
      campaign_id = campaign_id.id.toInt,
      delay = 1,
      step_type = CampaignStepType.ManualEmailStep,
      created_at = DateTime.now(),
      children = List(37),
      variants = Seq()
    )



    it("should check if tasks exists or not"){

      (campaignProspectDAO.getCampaignProspectDataForRevert(_: CampaignId,_: ProspectId,_: TeamId)(_: DBSession))
        .expects(campaign_id, prospect_id, team_id, *)
        .returning(Success(campaign_prospect_revert_data))

      (campaignDAOService.findOrderedSteps)
        .expects(campaign_id.id, team_id)
        .returning(Seq(campaignStepWithChildren))

      (taskService.checkIfTaskAlreadyExists(_: StepId,_: TeamId, _: ProspectId, _: Boolean)(using _: SRLogger))
        .expects(step_id, team_id, prospect_id, *, *)
        .returning(Failure(new Exception("task is present")))


      val result = emailsScheduledDeleteService.multichannelRevert(
        campaignId =  campaign_id,
        prospectId = prospect_id,
        team_id = team_id,
        reverted_by  = reverted_by,
        deletion_reason  = DeletionReason.DeletedVariantByDeleteV2
      )

      result match {

        case Failure(er) =>
          println(er)

          assert(er.getMessage == "task is present")


        case Success(_) =>


          assert(false)

      }


    }


  }

}
