package app.utils.email

import io.smartreach.esp.utils.ReplyParser
import io.smartreach.esp.utils.email.ReplyMimeType
import io.smartreach.esp.utils.email.models.EmailServiceTempCommon
import org.joda.time.DateTime
import org.scalamock.scalatest.MockFactory
import org.scalatest.funspec.AnyFunSpec
import play.api.Logging
import play.api.libs.json.Json
import utils.SRLogger
import utils.email.{EmailReplyStatus, EmailServiceTemp}

import scala.util.Success

class ReplyParserSpec extends AnyFunSpec with MockFactory with Logging {


  //FIXME: the method is working fine here, but while adding to the db, the base body, and text body is different
  describe("parseReply") {

    given Logger: SRLogger = new SRLogger("ReplyParserSpec")

    val body =
      """--00000000000002650805e9f18193
      Content-Type: multipart/related; boundary="00000000000002654905e9f18194"

      --00000000000002654905e9f18194
      Content-Type: multipart/alternative; boundary="00000000000002654d05e9f18195"

      --00000000000002654d05e9f18195
      Content-Type: text/plain; charset="UTF-8"


      ** Address not found **

      Your message wasn't <NAME_EMAIL> because the address couldn't be found, or is unable to receive mail.



      The response from the remote server was:
      550 User not found

      --00000000000002654d05e9f18195
      Content-Type: text/html; charset="UTF-8"


      <html>
      <head>
      <style>
      * {
      font-family:Roboto, "Helvetica Neue", Helvetica, Arial, sans-serif;
      }
      </style>
      </head>
      <body>
      <table cellpadding="0" cellspacing="0" class="email-wrapper" style="padding-top:32px;background-color:#ffffff;"><tbody>
      <tr><td>
      <table cellpadding=0 cellspacing=0><tbody>
      <tr><td style="max-width:560px;padding:24px 24px 32px;background-color:#fafafa;border:1px solid #e0e0e0;border-radius:2px">
      <img style="padding:0 24px 16px 0;float:left" width=72 height=72 alt="Error Icon" src="cid:icon.png">
      <table style="min-width:272px;padding-top:8px"><tbody>
      <tr><td><h2 style="font-size:20px;color:#212121;font-weight:bold;margin:0">
      Address not found
      </h2></td></tr>
      <tr><td style="padding-top:20px;color:#757575;font-size:16px;font-weight:normal;text-align:left">
      Your message wasn't delivered to <a style='color:#212121;text-decoration:none'><b><EMAIL></b></a> because the address couldn't be found, or is unable to receive mail.
      </td></tr>
      </tbody></table>
      </td></tr>
      </tbody></table>
      </td></tr>
      <tr style="border:none;background-color:#fff;font-size:12.8px;width:90%">
      <td align="left" style="padding:48px 10px">
      The response from the remote server was:<br/>
      <p style="font-family:monospace">
      550 User not found
      </p>
      </td>
      </tr>
      </tbody></table>
      </body>
      </html>

      --00000000000002654d05e9f18195--
      --00000000000002654905e9f18194
      Content-Type: image/png; name="icon.png"
      Content-Disposition: attachment; filename="icon.png"
      Content-Transfer-Encoding: base64
      Content-ID: <icon.png>

      iVBORw0KGgoAAAANSUhEUgAAAJAAAACQCAYAAADnRuK4AAAACXBIWXMAABYlAAAWJQFJUiTwAAAA
      GXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAABTdJREFUeNrsnD9sFEcUh5+PRMqZ
      yA0SPhAUQAQFUkyTgiBASARo6QApqVIkfdxGFJFSgGhJAUIiBaQB0ZIOKVCkwUgURjIg2fxL4kS+
      YDvkbC/388bi8N16Z4/d7J/5PsniuD3fyePP772ZeTsDQRAYQL/UGAJAIEAgQCBAIAAEAgQCBAIE
      AkAgyJT3Mv+Eq7vYK8mTE+MDRCAghQECAeRQA5V2ZOpmg5vDx3NPzRbmGRMEcmTrEbNNB8zWfRD+
      f/Efs2e3zCZvMjaksBg27TfbcuSNPEKP9ZyuAQKtHX2O9ncNgWC57umMPKvRNb0GEKgnLoUyxTQC
      rcns0/6uIRAs8/hGf9cQCJZpTpjdO2f25/03z+mxntM1eLtsZAgiUtX4JcaBCAQIBAgECARQ8CJa
      G5jab4J4pm4WZmO3OALVh802fIwcLkyPkcKAGggAgQCBAIEAgQCBABAIEAjKA/1AnahhbO5FdOOY
      VsrrDbPBYcYKgf5D2wLaV3p+22xh1u17tO3S+DTcvxvagUDeivPgx/a/95J/73w7Sj26Hn4pKo2M
      ehuV/KyBJM6d0f7k6RKx/R63vvL2tmf/ItDdM2ZTP6f7nkp9Y2fDx1v9akmpIU+KSCLVUghUQfSL
      zVKeTklbLxGoctw/nzC5rw8L5KRNbkpnKq6pgSqEClzNnFzY+XnYWrt6VpVk1vbwWvg+RKCKMOUw
      Q1LEOXA+/MX3mpJvGDHb265xtnzmFoUK1HaKQGlMtePYM+q2KKjXuaS1NJYIEKgI8jhEgqHt4cqy
      Ky53j3hyHz2bqSLp2o2LbJ7MxKovkGqXteoWpaOk96O9/yF/dF7NwlS36AuIQIBA5celQK4PIxBE
      4LLzrtoLgaALdSy6CJRkWQCBPGLsTHznomZ9nszUECgJ2ml3WWHe+QVFNPSQx6UdZNtxr9pbEShN
      eTTz8mQXHoHSlke7+Z+c9m6VGoHSkEfs/trLW3wQKApN1V3lGfnGu2Z6BFoLtYCs3GWBPAiUCLVh
      /HoaeRCoT9R873KLM/IgUBfapnCpe5AHgXry4pf412ihEHkQqCdxd5VqrcezhUIESsJMTJ+Pdthp
      Z0WgyNlXXPHc2Mc4IVAELl2Gnh8mhUDvCkfbIVAkcbf/aOoO3fMKhqAD3frTa4quwpn0hUDOkQhI
      YYBAgECAQAAU0QlYObl+5Ug8NcprZkZxjUCxRPVA6zmtEXHCBykskrhjgHXN09PoEcgFl4M4H11j
      nBAoApcj6ZoPGScEAgTKApcDoTw5sgWB+sGlz1n90IBAPdE6j1o21PfcC11jLagL1oFWRyGlKU3p
      OxcSJQ7NZAjkhHp/uG2HFAYIBAgECASAQIBAgECAQAAIBOkxEARBtp9wdVfAMOfIifEBIhCQwgCB
      ABAI0oV2jhxZ+nfBatuPZfgBCy0Eqqo8c01b+uu51XZvzOgDWoHNTGR+pCwpLEd5svuAZXlO2uEr
      PyEQ8hRWHgRCHmqg0sjTnLalv6crJQ8C/U8stqNO0I4+VZOHFIY8COS1PGL2ybd5yUMKK7s8zYmL
      dujyd3n+nESgcsvzZd4/KwIhDwIhT35QA6UyE1qyxZnfvJMHgdKS549JC1qvvJOHFIY8CFR5eV5O
      XimqPAhUdHnmfx+zgxdOFXkoqIGKKs/cswnb/8Oeog8HEai48nxUhiFBIORBIOShBioskkbySCLk
      IQIhDwIhj28p7FApR6b1qlEbHGpkO/rr6215vi/zH1r2x7tApSGFAQIBAgECAQIBIBAgECAQIBBA
      LK8FGADCTxYrr+EVJgAAAABJRU5ErkJggg==
      --00000000000002654905e9f18194--
      --00000000000002650805e9f18193
      Content-Type: message/delivery-status

      Reporting-MTA: dns; googlemail.com
      Received-From-MTA: dns; <EMAIL>
      Arrival-Date: Fri, 30 Sep 2022 21:25:39 -0700 (PDT)
      X-Original-Message-ID: <1664598333186.63186.159729.1711515310.mail@gEo8OQ0kfJLd6BoBP5eFWw>

      Final-Recipient: rfc822; <EMAIL>
      Action: failed
      Status: 5.0.0
      Remote-MTA: dns; ************* (*************, the server for the domain.)
      Diagnostic-Code: smtp; 550 User not found
      Last-Attempt-Date: Fri, 30 Sep 2022 21:25:44 -0700 (PDT)

      --00000000000002650805e9f18193
      Content-Type: message/global
      Content-Transfer-Encoding: quoted-printable

      X-Gm-Message-State: ACrzQf3gW1vCNaltNheQWWX3RN/Ks0sWqQCluG0vpQbXCqiI/yEXQ+Y=
      J
        Et528sQR1JzFFoCAWn9+EevzH6Ys84Mi5/vdmaa9HmhRwmrtiBrX4HkuWDk8ch2YX5YsUPt/n8=
      2
        SvAySkdqlY+JH+ttI0j9q
      X-Received: by 2002:a17:903:2282:b0:177:faeb:3606 with SMTP id b2-20020a170=
      903228200b00177faeb3606mr12166372plh.135.1664598341039;
              Fri, 30 Sep 2022 21:25:41 -0700 (PDT)
      X-Google-Smtp-Source: AMsMyM5JCqir4ZDv/55E/fsskOAWLzR4yPw15CbCxelkr9s8GNHr+=
      Xb/Hw/0whoe9TX4krSbBR9X
      X-Received: by 2002:a17:903:2282:b0:177:faeb:3606 with SMTP id b2-20020a170=
      903228200b00177faeb3606mr12166305plh.135.1664598339461;
              Fri, 30 Sep 2022 21:25:39 -0700 (PDT)
      ARC-Seal: i=3D1; a=3Drsa-sha256; t=3D1664598339; cv=3Dnone;
              d=3Dgoogle.com; s=3Darc-20160816;
              b=3DfkiHEDBdl2fIXm9uddQbdGIW1gLZSSVoONLgYozVKORVujbOdX95SAkrm73HG4A=
      6a1
               0Q9U7hLUqQ3AMnjXnGG5nhmdxTSIVDSoCklq8NASa9N34T+dWepVJsK041Cy3Qha81=
      JG
               4M0iTXiLK0dKj+h74WQBpzRw4Ijx/n+G1oCZx0uUhOEyGMjNwMPWYpAVC6y8ThK138=
      23
               bZM797/MPZNBNFdDMsyd6bNxty4XwiIGYDFrNlh9P/0TKO0wc7aRpJ5w79FDc0U7ui=
      kh
               Maqu817NeNGu41FgbWXuB/+gJrsah1g49QqnpOn9URV2jioJHFxFe6cvgIVUjltLgg=
      pA
               jltw=3D=3D
      ARC-Message-Signature: i=3D1; a=3Drsa-sha256; c=3Drelaxed/relaxed; d=3Dgoog=
      le.com; s=3Darc-20160816;
              h=3Dlist-unsubscribe:mime-version:subject:message-id:to:from:date
               :dkim-signature;
              bh=3Db8E19yFvU9ZhCtMFzNsuRnvKF0sPv+WUjrHwKNpIA2M=3D;
              b=3DYuEEt5a8ZnJvwg/Ly3E39X2rKovRJd14XrIooA5vFYcLksObaXsK8VucHvqGeTS=
      p2M
               ztAMrNl1kWVy5mgIZN421KXbG+4s+C5PMpdAFc+jx+xNr0neNfak1RvuNj3aobf9Ib=
      V/
               FtRNyVzoU6fNzrDQ18OkGWp4kv9W4mvf4gkeK03Y3bhls+sFLQKA3CEhy0YsoeQB52=
      Zc
               Qdn1qfU73sxvxUj73xt+fSjENCv6w81LJnkTaPGzd4PZlCWMjewILXaSeRpAdyERxA=
      rL
               M5fmtLLNUq3bggXztObZgz4Imxb010PBldPc3bSGQb6xTcIvwKl5OalzCTc+8vT+R2=
      4Q
               zs8A=3D=3D
      ARC-Authentication-Results: i=3D1; mx.google.com;
             dkim=3Dpass header.i=<EMAIL> header.s=3Depro header.b=
      =3DDOzZMP5W;
             spf=3Dpass (google.com: <NAME_EMAIL> designates =
      *************** as permitted sender) smtp.mailfrom=<EMAIL>
      Return-Path: <<EMAIL>>
      Received: from pro237-206.mxout.rediffmailpro.com (pro237-206.mxout.rediffm=
      ailpro.com. [***************])
              by mx.google.com with ESMTPS id j6-20020a63cf06000000b0043634245eb1=
      si4957078pgg.311.2022.***********.38
              for <<EMAIL>>
              (version=3DTLS1_2 cipher=3DECDHE-ECDSA-AES256-GCM-SHA384 bits=3D256=
      /256);
              Fri, 30 Sep 2022 21:25:39 -0700 (PDT)
      Received-SPF: pass (google.com: <NAME_EMAIL> designates=
       *************** as permitted sender) client-ip=3D***************;
      Authentication-Results: mx.google.com;
             dkim=3Dpass header.i=<EMAIL> header.s=3Depro header.b=
      =3DDOzZMP5W;
             spf=3Dpass (google.com: <NAME_EMAIL> designates =
      *************** as permitted sender) smtp.mailfrom=<EMAIL>
      X-REDIFF-Delivered-Remotely-To: <EMAIL>
      DKIM-Signature: v=3D1; a=3Drsa-sha256; c=3Drelaxed/relaxed; d=3Drediffmailp=
      ro.com;
        s=3Depro; t=3D1664598337;
        bh=3Db8E19yFvU9ZhCtMFzNsuRnvKF0sPv+WUjrHwKNpIA2M=3D;
        h=3DMIME-Version:From:Date:Message-ID:Subject:To;
        b=3DDOzZMP5WY6dSmlqY6KjUHE9YcYUHhYXdt2+2+SKGquJ2yJFTMT7rQ1RS81igei+S/
         liKccsVofNzqVqnxKKESpZm6cJPNurKX7AX2dOb07jRYhgA/sKzTDav2T1L9+7Wsfo
         A+2cicG9Sofxm1/PDT8BfqrzWojTxr9ysZ1O6LR8=3D
      Received: (qmail 1886 invoked from network); 1 Oct 2022 04:25:37 -0000
      x-m-msg: 86b94210c9427d9267aa59b21841b638; a6da7d6asas6dasd77; 5dad65ad5sd;
      X-OUT-VDRT-SpamState: 0\LEGIT
      X-OUT-VDRT-SpamScore: 0
      X-OUT-VDRT-SpamCause: gggruggvucftvghtrhhoucdtuddrgedvfedrfeehfedgkeeiucetu=
      fdoteggodetrfdotffvucfrrhhofhhilhgvmecuggftfghnshhusghstghrihgsvgenuceurghi=
      lhhouhhtmecufedttdenucenucfjughrpeffhffvkffugggtjfesrgdtregstddtjeenucfhrhh=
      omheptfgrjhhuucfmuhhmrghruceorhgrjhhusehsshgutghlohhuughinhguihgrrdgtohhmqe=
      enucggtffrrghtthgvrhhnpeehleeuheeufeeflefftefgkeehuddukeefvdegtdevhfdvtdevj=
      efhheeiuedufeenucffohhmrghinhepvgdvvghnvghtfihorhhkshdrtghomhdpshhmrghrthhr=
      vggrtghhrghpphdrtghomhenucevlhhushhtvghrufhiiigvpedtnecurfgrrhgrmhepmhhougg=
      vpehsmhhtphhouhht
      X-Dedup-Identifier: 1664598337_1809_1098_pro-237-206
      Received: from unknown (HELO smartreachapp.com) (<EMAIL>@35.=
      225.229.43)
        by mailserver with ESMTPS (AES256-GCM-SHA384 encrypted); 1 Oct 2022 04:25=
      :37 -0000
      Date: Sat, 1 Oct 2022 04:25:37 +0000 (UTC)
      From: Raju Kumar <<EMAIL>>
      To: Dear Sir  <<EMAIL>>
      Message-ID: <1664598333186.63186.159729.1711515310.mail@gEo8OQ0kfJLd6BoBP5e=
      FWw>
      Subject: Dear Sir, Exclusive CPU & High-Performance Smart Dedicated Compute
      MIME-Version: 1.0
      Content-Type: multipart/alternative;=20
        boundary=3D"----=3D_Part_13040_655505133.1664598333187"
      List-Unsubscribe: <mailto:raju+unsubscribesrmail_1711515310_63186_159729_10=
      <EMAIL>?subject=3Dunsubscribe>
      X-Gm-Spam: 0
      X-Gm-Phishy: 0

      ----- Message truncated -----
      --00000000000002650805e9f18193--"""

    it ("should pass and not remove the entire message") {
      val result = ReplyParser.parseReply(
        body = body,
        mimeType = ReplyMimeType.MULTIPART
      )(Logger)

      val result2 = EmailServiceTempCommon.getBaseBodyAndTextBody(
        bodyRaw = body,
        mimeType = ReplyMimeType.MULTIPART,
        Logger = Logger
      )

      val result3 = EmailReplyStatus.getEmailReplyStatusBeforeSaving(
        emailBaseBody = body,
        subject = "subject",
        fromEmail = "<EMAIL>",
        fromName = "Animesh",
        toEmailAddresses = Seq(),
        byAccount = false,
        fullHeaders = Json.obj(),
        received_at = DateTime.now()
      )


      //not working properly
      val result4 = EmailServiceTemp.getReplyProspectCategoryType(
        emailBaseBody = body,
        subject = "subject",
        toEmailAddresses = Seq(),
        byAccount = false,
        autoGeneratedReplyFromHeader = false)


      logger.info(s"result ____________________ ${result2.baseBody.replaceAll("\u0000", "")}")
      logger.info(s"result4 ____________________ ${result4}")
//      Thread.sleep(10000)
      logger.info(s"bounce data ____________________ ${result3.bouncedData}")

      assert(result2.baseBody == body)
      assert(result == Success(body)) //Parsing properly here but for esid ********* it was -

      // --00000000000002650805e9f18193
      //Content-Type: multipart/related; boundary="00000000000002654905e9f18194"
      // for these kind of messages that start with these code, it cuts them wrong when in db. we need to figure out why.
    }
  }

}
