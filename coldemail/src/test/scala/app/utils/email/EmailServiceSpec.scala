package app.utils.email

import org.apache.pekko.actor.ActorSystem
import api.accounts.email.models.EmailServiceProvider
import api.accounts.{Account, AccountAccess, AccountDAO, AccountMetadata, AccountService, AccountType, AccountUuid, OrgCountData, OrgMetadata, OrgPlan, OrgSettings, OrganizationRole, OrganizationWithCurrentData, PermType, PermissionLevelForValidation, PermissionOwnershipV2, ProspectCategoriesInDB, ReplyHandling, RolePermV2, RolePermissionDataDAOV2, RolePermissionDataV2, RolePermissionsInDBV2, RolePermissionsV2, TeamAccount, TeamAccountRole, TeamId, TeamMember, TeamMemberLite}
import api.accounts.models.{AccountId, AccountProfileInfo, OrgId}
import api.accounts.service.AccountOrgBillingRelatedService
import api.calendar_app.models.CalendarAccountData
import api.campaigns.dao.CampaignSchedulingMetadataDAO
import api.campaigns.services.{CampaignCacheService, CampaignId}
import api.campaigns.{CPCompleted, CPRepliedEvent, CampaignDAO, CampaignIdAndTeamId, CampaignProspectDAO, CampaignProspectUpdateScheduleStatus}
import api.campaigns.models.{CampaignStepType, CurrentStepStatusForScheduler, SendEmailFromCampaignDetails, StepDetails}
import api.columns.InternalMergeTagValuesForProspect
import api.emails.dao_service.{EmailScheduledDAOService, EmailThreadDAOService}
import api.emails.models.{EmailMessageContact, EmailMessageContactModel, EmailReplyType, EmailSettingUuid, MessageContactType}
import api.emails.services.{EmailSettingService, SelectAndPublishForDeletionService}
import api.emails.{AssociateEmailThreadProspect, DBEmailMessagesSavedResponse, ERIntermediateValidProspect, EmailCommonService, EmailHandleErrorService, EmailMessageTracked, EmailReplySavedV3, EmailReplyTrackingModel, EmailReplyTrackingModelV2, EmailScheduled, EmailScheduledDAO, EmailSetting, EmailSettingDAO, EmailThreadDAO, EmailThreadUuid, EmailToBeSent, NewEmailThreadV3, SavedEmailThread}
import api.integrations.IntegrationTPAccessTokenResponse
import api.linkedin_message_threads.LinkedinMessageThreadsDAO
import api.prospects.dao_service.ProspectDAOService
import api.prospects.models.{ProspectCategory, ProspectCategoryRank, ProspectId, ProspectTouchedType}
import api.prospects.service.ProspectServiceV2
import api.prospects.{ProspectAccount, ProspectCheckForIsSentEmail, ProspectIdEmail, ProspectUpdateCategoryTemp, ProspectUuid}
import api.scylla.dao.BounceData
import app.test_fixtures.accounts.OrgCountDataFixture
import app.test_fixtures.organizationa.{OrgMetadataFixture, OrgPlanFixture}
import app.test_fixtures.prospect.{ProspectAccountFixture, ProspectFixtures}
import utils.email.EmailBodyService
import io.smartreach.esp.utils.email.{EmailReplyBounceType, EmailReplyStatusCommon}
import utils.email.EmailServiceTemp
import utils.email.models.SendScheduleEmailType
import utils.mq.trackingapp.OpenTracker.MQOpenTrackerPublisher
import utils.mq.webhook.mq_activity_trigger.MQActivityTriggerPublisher
import utils.testapp.TestAppExecutionContext
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService
//import api.scylla.service.CacheEventScyllaService
import api.sr_audit_logs.models.EventType
import api.team.TeamUuid
import api.team_inbox.model.FolderType
import eventframework.{ProspectObject, ProspectObjectInternal, SrResourceTypes}
import io.smartreach.esp.api.emails.{EmailSettingId, IEmailAddress}
import org.joda.time.DateTime
import org.scalamock.matchers.ArgCapture.CaptureOne
import org.scalamock.scalatest.AsyncMockFactory
import org.scalatest.funspec.AsyncFunSpec
import play.api.libs.json.{JsValue, Json}
import play.api.libs.ws.WSClient
import play.api.libs.ws.ahc.AhcWSClient
import sr_scheduler.models.ChannelType
import utils.SRLogger
import utils.cache_utils.model.CampaignUseStatusForEmailSetting
import utils.email.services.InternalTrackingNote
import utils.email.{EmailReplyStatus, EmailSenderService, EmailService, EmailsScheduledDeleteService, GmailApiService, GmailService, MailgunService, OutlookApiService, SREmailErrors, SendGridService, SmtpImapService}
import utils.mq.trackingapp.OpenTracker.MQOpenTrackerMessage
import utils.mq.webhook.mq_activity_trigger.{MQActivityTriggerMsgForm, MQActivityTriggerService}
import utils.mq.webhook.{MQWebhookCompleted, MQWebhookEmailInvalid}
import utils.templating.TemplateService
import utils.uuid.SrUuidUtils

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}

class EmailServiceSpec extends AsyncFunSpec with AsyncMockFactory {

  implicit lazy val system: ActorSystem = TestAppExecutionContext.actorSystem
  implicit lazy val wSClient: AhcWSClient = TestAppExecutionContext.wsClient
  implicit lazy val actorContext: ExecutionContext = system.dispatcher

  given Logger: SRLogger = new SRLogger("Unit test")
  val Error = new Throwable("ERROR")


  val prospectDAOService = mock[ProspectDAOService]
  val prospectServiceV2 = mock[ProspectServiceV2]
  val emailScheduledDAOService = mock[EmailScheduledDAOService]
  //val accountDAO = mock[AccountDAO]
  val templateService = mock[TemplateService]
  val emailScheduledDAO = mock[EmailScheduledDAO]
  val emailSettingDAO = mock[EmailSettingDAO]
  val emailThreadDAO = mock[EmailThreadDAO]
  val campaignProspectDAO = mock[CampaignProspectDAO]
  val emailReplyTrackingModelV2 = mock[EmailReplyTrackingModelV2]
  val emailHandleErrorService = mock[EmailHandleErrorService]
  val accountService = mock[AccountService]
  val outlookApiService = mock[OutlookApiService]
  val gmailApiService = mock[GmailApiService]
  val gmailService = mock[GmailService]
  val mailgunService = mock[MailgunService]
  val sendGridService = mock[SendGridService]
  val smtpImapService = mock[SmtpImapService]
  val mqActivityTriggerPublisher = mock[MQActivityTriggerPublisher]
  val mqOpenTrackerPublisher = mock[MQOpenTrackerPublisher]
  val mqWebhookEmailInvalid = mock[MQWebhookEmailInvalid]
  val mqWebhookCompleted = mock[MQWebhookCompleted]
  val emailMessageContactModel = mock[EmailMessageContactModel]
  //  val cacheEventScyllaService = mock[CacheEventScyllaService]
  val campaignCacheService = mock[CampaignCacheService]
  val campaignDAO = mock[CampaignDAO]
  val srUuidUtils = mock[SrUuidUtils]
  val rolePermissionDataDAOV2 = mock[RolePermissionDataDAOV2]
  val emailThreadDAOService = mock[EmailThreadDAOService]
  val emailSettingService = mock[EmailSettingService]
  val linkedinMessageThreadsDAO = mock[LinkedinMessageThreadsDAO]
  val emailsScheduledDeleteService = mock[EmailsScheduledDeleteService]
  val campaignSchedulingMetadataDAO = mock[CampaignSchedulingMetadataDAO]

  val srRollingUpdateCoreService = mock[SrRollingUpdateCoreService]
  val selectAndPublishForDeletionService = mock[SelectAndPublishForDeletionService]

  val emailBodyService: EmailBodyService = mock[EmailBodyService]
  val emailCommonService = mock[EmailCommonService]
  val emailService = new EmailService(
    prospectDAOService = prospectDAOService,
    //accountDAO = accountDAO,
    templateService = templateService,
    srUuidUtils = srUuidUtils,
    emailScheduledDAO = emailScheduledDAO,
    emailThreadDAO = emailThreadDAO,
    linkedinMessageThreadsDAO = linkedinMessageThreadsDAO,
    campaignProspectDAO = campaignProspectDAO,
    emailSettingDAO = emailSettingDAO,
    campaignDAO = campaignDAO,
    emailReplyTrackingModelV2 = emailReplyTrackingModelV2,
    emailHandleErrorService = emailHandleErrorService,
    accountService = accountService,
    outlookApiService = outlookApiService,
    gmailApiService = gmailApiService,
    // gmailService = gmailService,
    mailgunService = mailgunService,
    sendGridService = sendGridService,
    smtpImapService = smtpImapService,
    //  webhookUtils = webhookUtils,
    mqActivityTriggerPublisher = mqActivityTriggerPublisher,
    mqOpenTrackerPublisher = mqOpenTrackerPublisher,
    mqWebhookEmailInvalid = mqWebhookEmailInvalid,
    mqWebhookCompleted = mqWebhookCompleted,
    prospectServiceV2 = prospectServiceV2,
    emailMessageContactModel = emailMessageContactModel,
    //  cacheEventScyllaService = cacheEventScyllaService,
    campaignCacheService = campaignCacheService,
    emailThreadDAOService = emailThreadDAOService,
    emailSettingService = emailSettingService,
    emailBodyService = emailBodyService,
  )

  val prospectUpdateCategoryTemp = mock[ProspectUpdateCategoryTemp]

  val accountOrgBillingRelatedService = mock[AccountOrgBillingRelatedService]

  val emailSenderService = new EmailSenderService(
    prospectDAOService = prospectDAOService,
    //accountDAO = accountDAO,
    emailScheduledDAO = emailScheduledDAO,
    campaignProspectDAO = campaignProspectDAO,
    emailSettingDAO = emailSettingDAO,
    srUuidUtils = srUuidUtils,
    campaignDAO = campaignDAO,
    emailHandleErrorService = emailHandleErrorService,
    //accountService = accountService,
    outlookApiService = outlookApiService,
    gmailApiService = gmailApiService,
    gmailService = gmailService,
    mailgunService = mailgunService,
    sendGridService = sendGridService,
    smtpImapService = smtpImapService,
    mqActivityTriggerPublisher = mqActivityTriggerPublisher,
    mqWebhookEmailInvalid = mqWebhookEmailInvalid,
    prospectServiceV2 = prospectServiceV2,
    emailMessageContactModel = emailMessageContactModel,
    campaignCacheService = campaignCacheService,
    emailThreadDAOService = emailThreadDAOService,
    accountOrgBillingRelatedService = accountOrgBillingRelatedService,
    selectAndPublishForDeletionService = selectAndPublishForDeletionService,
    campaignSchedulingMetadataDAO = campaignSchedulingMetadataDAO,
    emailScheduledDAOService = emailScheduledDAOService,
    srRollingUpdateCoreService = srRollingUpdateCoreService,
    prospectUpdateCategoryTemp = prospectUpdateCategoryTemp,
    emailCommonService = emailCommonService
  )


  val email = "<EMAIL>"
  val email_address_host = "company.com"
  val team_id_347: Long = 347
  val emailMessageIdSuffix = "local@smartreachio"
  val emailSetting = EmailSetting(
    id = Some(EmailSettingId(emailSettingId = 123)),
    org_id = OrgId(id = 123567),
    owner_id = AccountId(id = 47),
    team_id = TeamId(id = team_id_347), // FIXME VALUECLASS
    uuid = Some(EmailSettingUuid("test_uuid")),
    owner_uuid = AccountUuid("owner_uuid"),
    team_uuid = TeamUuid("team_uuid"),
    message_id_suffix = emailMessageIdSuffix,

    email = email,
    email_address_host = email_address_host,

    service_provider = EmailServiceProvider.OTHER,
      domain_provider = None,
    via_gmail_smtp = None,

    owner_name = "Ownername dummy",
    sender_name = "Test Sender Name",
    first_name = "John",
    last_name = "Doe",

    cc_emails = None,
    bcc_emails = None,

    smtp_username = Some(email),
    smtp_password = Some("thisispassword"),
    smtp_host = Some("this is the smtp host"),
    smtp_port = Some(12345),

    imap_username = Some(email),
    imap_password = Some("thisisimappassword"),
    imap_host = Some("imap.host.com"),
    imap_port = Some(993),

    oauth2_access_token = None,
    oauth2_refresh_token = None,
    oauth2_token_type = None,
    oauth2_token_expires_in = None,
    oauth2_access_token_expires_at = None,

    // for mailgun
    email_domain = None,
    api_key = None,
    mailgun_region = None,

    quota_per_day = 3,

    reply_handling = ReplyHandling.PAUSE_SPECIFIC_CAMPAIGN_ON_REPLY,
    last_read_for_replies = None,
    latest_email_scheduled_at = None,

    error = None,
    error_reported_at = None,
    paused_till = None,

    signature = "MySignature",

    created_at = Some(DateTime.now()),

    current_prospect_sent_count_email = 3,

    default_tracking_domain = "company.com",
    default_unsubscribe_domain = "company.com",
    rep_tracking_host_id = 123,
    tracking_domain_host = None,
    custom_tracking_domain = None,
    custom_tracking_cname_value = None,
    custom_tracking_domain_is_verified = None,
    custom_tracking_domain_is_ssl_enabled = None,

    rep_mail_server_id = 123,
    rep_mail_server_public_ip = "0.0.0.0",
    rep_mail_server_host = "randomserverhost.com",
    rep_mail_server_reverse_dns = None,

    min_delay_seconds = 30,
    max_delay_seconds = 120,
      tag = None,
    campaign_use_status_for_email_setting = CampaignUseStatusForEmailSetting.IsNotAssignedToAnyCampaign,
    show_rms_ip_in_frontend = false

  )

  val emailReplyStatus = EmailReplyStatus(
    replyType = EmailReplyType.NOT_CATEGORIZED,
    isReplied = false,
    isUnsubscribeRequest = false,
    isAutoReply = false,
    isOutOfOfficeReply = false,
    isInvalidEmail = false,
    isForwarded = false,
    bouncedData = None
  )


  val emailMessageTracked = EmailMessageTracked(
    inbox_email_setting_id = 1,
    from = IEmailAddress(email = "<EMAIL>"),
    to_emails = Seq(IEmailAddress(email = "<EMAIL>"), IEmailAddress(email = "<EMAIL>")),
    subject = "SomeSubject",
    body = "SomeBody",
    base_body = "SomeBaseBody",
    text_body = "SomeTextBody",
    references_header = None,
    campaign_id = None,
    step_id = None,
    prospect_id_in_campaign = None,
    prospect_account_id_in_campaign = None,
    campaign_name = None,
    step_name = None,
    received_at = DateTime.now().minusDays(2),
    recorded_at = DateTime.now().minusDays(2),
    sr_inbox_read = true,
    original_inbox_folder = None,
    email_status = emailReplyStatus,
    message_id = "some_message_id",
    full_headers = Json.obj(),
    scheduled_manually = false,
    reply_to = None,
    email_thread_id = None,
    gmail_msg_id = None,
    gmail_thread_id = None,
    outlook_msg_id = None,
    outlook_conversation_id = None,
    outlook_response_json = None,
    cc_emails = Seq(),
    in_reply_to_header = None,
    team_id = 1,
    account_id = 1,
    internal_tracking_note = InternalTrackingNote.EXISTING_INREPLYTO,
    tempThreadId = None
  )


  describe("readEmail") {


    //    it("emailSettingDAO.find sends back none") {
    //
    //      (emailSettingDAO.find (_:Long))
    //        .expects(1)
    //        .returning(None)
    //
    //
    //      emailService.readEmail(
    //        emailSetting = emailSetting,
    //        auditRequestLogId = "some_log_id",
    //        Logger: SRLogger
    //      ).map{result =>
    //
    //        Logger.info(s"result__________ $result")
    //        assert(false)
    //      }.recover{case e =>
    //        assert(e.getMessage.contains("email setting not found"))
    //      }
    //    }

    it("last_read_for_replies is defined and read in last 5 minutes") {

      emailService.readEmail(
        emailSetting = emailSetting.copy(
          last_read_for_replies = Some(DateTime.now.minusMinutes(3))
        ),
        auditRequestLogId = "some_log_id",
        fromDate = DateTime.now.minusMinutes(3),
        isReplyTracker = true
      ).map { result =>

        Logger.info(s"result__________ $result")
        assert(false)
      }.recover { case e =>
        assert(e.getMessage.contains("email setting was read in last 5 minutes"))
      }
    }

    it("paused_till is after now") {


      emailService.readEmail(
        emailSetting = emailSetting.copy(
          last_read_for_replies = None,
          paused_till = Some(DateTime.now().plusDays(1))
        ),
        auditRequestLogId = "some_log_id",
        fromDate = DateTime.now().plusDays(1),
        isReplyTracker = true
      ).map { result =>

        Logger.info(s"result__________ $result")
        assert(false)
      }.recover { case e =>
        assert(e.getMessage.contains(" email setting is paused"))
      }
    }


    it("paused_till is not defined and error is") {


      emailService.readEmail(
        emailSetting = emailSetting.copy(
          last_read_for_replies = None,
          paused_till = None,
          error = Some("Error")
        ),
        auditRequestLogId = "some_log_id",
        fromDate = DateTime.now().minusDays(1),
        isReplyTracker = true
      ).map { result =>

        Logger.info(s"result__________ $result")
        assert(false)
      }.recover { case e =>
        assert(e.getMessage.contains("email setting is under manual review"))
      }
    }

    it("service_provider = EmailServiceProvider.GMAIL_ALIAS") {


      emailService.readEmail(
        emailSetting = emailSetting.copy(
          last_read_for_replies = None,
          paused_till = None,
          error = None,
          service_provider = EmailServiceProvider.GMAIL_ALIAS
        ),
        auditRequestLogId = "some_log_id",
        fromDate = DateTime.now().minusDays(1),
        isReplyTracker = true
      ).map { result =>

        Logger.info(s"result__________ $result")
        assert(false)
      }.recover { case e =>
        assert(e.getMessage.contains("error mailgun / sendgrid / gmail_alias reply tracking not supported"))
      }
    }


    it("success with receivedEmails as empty") {

      (emailSettingService.findSendersForReceiver(
        _: Long,
        _: Long,
        _: SRLogger
      ))
        .expects(123, team_id_347, Logger)
        .returning(Success(Seq(emailSetting)))

      (smtpImapService.receiveEmail(_: EmailSetting, _: Seq[EmailSetting], _: SRLogger, _: DateTime, _: Option[DateTime])(_: WSClient, _: ExecutionContext))
        .expects(emailSetting.copy(last_read_for_replies = None, paused_till = None, error = None, service_provider = EmailServiceProvider.OTHER), Seq(emailSetting), *, *, *, *, *)
        .returning(Future(Vector()))

      (emailSettingDAO.updateLastReadForReplies)
        .expects(emailSetting.copy(last_read_for_replies = None, paused_till = None, error = None, service_provider = EmailServiceProvider.OTHER), *, *)
        .returning(Success(None))

      emailService.readEmail(
        emailSetting = emailSetting.copy(
          last_read_for_replies = None,
          paused_till = None,
          error = None,
          service_provider = EmailServiceProvider.OTHER
        ),
        auditRequestLogId = "some_log_id",
        fromDate = DateTime.now().minusDays(1),
        isReplyTracker = true
      ).map { result =>

        Logger.info(s"result__________ $result")
        assert(result == (emailSetting, 0, 0, List(123)))
      }
    }
    it("success with receivedEmails and emailHandleErrorService.handleEmailSettingError fail") {

      (emailSettingService.findSendersForReceiver(
        _: Long,
        _: Long,
        _: SRLogger
      ))
        .expects(123, team_id_347, Logger)
        .returning(Success(Seq(emailSetting)))

      (smtpImapService.receiveEmail(_: EmailSetting, _: Seq[EmailSetting], _: SRLogger, _: DateTime, _: Option[DateTime])(_: WSClient, _: ExecutionContext))
        .expects(emailSetting.copy(last_read_for_replies = None, paused_till = None, error = None, service_provider = EmailServiceProvider.OTHER), Seq(emailSetting), *, *, *, *, *)
        .returning(Future(Vector(emailMessageTracked)))

      (accountService.find(_: Long)(_: SRLogger))
        .expects(47, *)
        .returning(Failure(Error))

      // FIXME VAUELCLASS
      (emailHandleErrorService.handleEmailSettingError(_: EmailSettingId, _: SendScheduleEmailType, _: Throwable, _: AccountId, _: TeamId, _: String, _: SRLogger)(_: SREmailErrors.EmailSettingError)(_: WSClient, _: ExecutionContext))
        .expects(EmailSettingId(emailSettingId = 123), SendScheduleEmailType.AutoEmail, *, AccountId(id = 47), TeamId(id = team_id_347), "<EMAIL>", *, *, *, *)
        .returning(Failure(Error))

      emailService.readEmail(
        emailSetting = emailSetting.copy(
          last_read_for_replies = None,
          paused_till = None,
          error = None,
          service_provider = EmailServiceProvider.OTHER
        ),
        auditRequestLogId = "some_log_id",
        fromDate = DateTime.now().minusDays(1),
        isReplyTracker = true
      ).map { result =>

        Logger.info(s"result__________ $result")
        assert(false)
      }.recover { case e =>

        assert(e == Error)
      }
    }

    it("success with receivedEmails and emailHandleErrorService.handleEmailSettingError pass") {

      (emailSettingService.findSendersForReceiver(
        _: Long,
        _: Long,
        _: SRLogger
      ))
        .expects(123, team_id_347, Logger)
        .returning(Success(Seq(emailSetting)))

      (smtpImapService.receiveEmail(_: EmailSetting, _: Seq[EmailSetting], _: SRLogger, _: DateTime, _: Option[DateTime])(_: WSClient, _: ExecutionContext))
        .expects(emailSetting.copy(last_read_for_replies = None, paused_till = None, error = None, service_provider = EmailServiceProvider.OTHER), Seq(emailSetting), *, *, *, *, *)
        .returning(Future(Vector(emailMessageTracked)))

      (accountService.find(_: Long)(_: SRLogger))
        .expects(47, *)
        .returning(Failure(Error))

      // FIXME VALUECLASS
      (emailHandleErrorService.handleEmailSettingError(_: EmailSettingId, _: SendScheduleEmailType, _: Throwable, _: AccountId, _: TeamId, _: String, _: SRLogger)(_: SREmailErrors.EmailSettingError)(_: WSClient, _: ExecutionContext))
        .expects(EmailSettingId(emailSettingId = 123), SendScheduleEmailType.AutoEmail, *, AccountId(id = 47), TeamId(id = team_id_347), "<EMAIL>", *, *, *, *)
        .returning(Success(Seq()))

      emailService.readEmail(
        emailSetting = emailSetting.copy(
          last_read_for_replies = None,
          paused_till = None,
          error = None,
          service_provider = EmailServiceProvider.OTHER
        ),
        auditRequestLogId = "some_log_id",
        fromDate = DateTime.now().minusDays(1),
        isReplyTracker = true
      ).map { result =>

        Logger.info(s"result__________ $result")
        assert(false)
      }.recover { case e =>

        assert(e.getMessage.contains("[EmailService] error (emailSettingId: 123) email is now paused so deleted from queue"))
      }
    }


  }

  val first_name = "Adminfirst"
  val last_name = "Adminlast"
  val company = "CompanyName"
  val team_id: Long = 2

  val integrationTPAccessTokenResponse = IntegrationTPAccessTokenResponse.FullTokenData(
    access_token = "some_access_token",
    refresh_token = Some("some_refresh_token"),
    expires_in = Option(150),
    expires_at = Option(DateTime.now().plusMinutes(9)),
    token_type = Some("some_token_type"),
    api_domain = Some("some_api_domain"),
    is_sandbox = Some(false)
  )


  val profile = AccountProfileInfo(
    first_name = first_name,
    last_name = last_name,
    company = Some(company),
    timezone = None,
    country_code = None,
    mobile_country_code = None,
    mobile_number = None,
    onboarding_phone_number = None,
    twofa_enabled = false,
    has_gauthenticator = false,
    weekly_report_emails = None,
    scheduled_for_deletion_at = None
  )

  val accountMetadata = AccountMetadata(
    // account_ui_version = None,
    is_profile_onboarding_done = None
  )

  val orgMetadata = OrgMetadataFixture.orgMetadataFixture2

  val orgCountData: OrgCountData = OrgCountDataFixture.orgCountData_default

  val orgSettings = OrgSettings(
    enable_ab_testing = false,
    disable_force_send = false,
    bulk_sender = false,
    allow_2fa = false,
    show_2fa_setting = false,
    enforce_2fa = false,
    allow_native_crm_integration = false,
    agency_option_allow_changing = false,
    agency_option_show = false
  )

  val orgPlan = OrgPlanFixture.orgPlanFixture

  val org = OrganizationWithCurrentData(

    id = 1,
    name = company,
    owner_account_id = 49,

    counts = orgCountData,
    settings = orgSettings,
    plan = orgPlan,

    is_agency = true,
    trial_ends_at = DateTime.now().plusDays(100),
    error = None,
    error_code = None,
    paused_till = None,
    errors = Seq(),
    warnings = Seq(),
    via_referral = false,
    org_metadata = orgMetadata
  )

  val aDate = DateTime.now()

  val teamMemberLite = TeamMemberLite(

    user_id = 2L,
    first_name = Some("first_name"),
    last_name = Some("last_name"),
    email = "<EMAIL>",
    active = true,
    timezone = Some("campaignTimezone"),
    twofa_enabled = true,
    created_at = aDate,
    user_uuid = AccountUuid("uuid"),
    team_role = TeamAccountRole.ADMIN

  )

  val prospect_CategoriesInDB = ProspectCategoriesInDB(
    id = 22L,
    name = "Completed",
    text_id = "Done",
    label_color = "Blue",
    is_custom = true,
    team_id = team_id,
    rank = ProspectCategoryRank(rank = 2000)
  )
  val teamMember: TeamMember = TeamMember(
    team_id = team_id,
    team_name = "team_name",
    user_id = 2L,
    ta_id = 49L, // dont send ta_id to frontend / api response, only for internal purpose, its dynamically assigned in AuthUtils
    first_name = Some(first_name),
    last_name = Some(last_name),
    email = "<EMAIL>",
    team_role = TeamAccountRole.ADMIN,
    api_key = Some("apiKey"),
    zapier_key = Some("zapier_key")
  )

  val adminDefaultPermissions = RolePermissionDataDAOV2.defaultRoles(
    role = TeamAccountRole.ADMIN,
    simpler_perm_flag = false
  )

  val rolePermissionData = RolePermissionDataV2.toRolePermissionApi(
    data = adminDefaultPermissions.copy(id = 10)
  )

  val team_account: TeamAccount = TeamAccount(

    team_id = team_id,
    org_id = 20L,

    role_from_db = Some(adminDefaultPermissions), // MUST come from db (option type only for cacheservice error), should not be sent to frontend, only intermediate

    role = Some(rolePermissionData), // should be sent to frontend

    active = true,
    is_actively_used = true,
    team_name = "team_name",
    total_members = 5,
    access_members = Seq(teamMember),
    all_members = Seq(teamMemberLite),

    prospect_categories_custom = Seq(prospect_CategoriesInDB),
    max_emails_per_prospect_per_day = 100L,
    max_emails_per_prospect_per_week = 500L,
    max_emails_per_prospect_account_per_day = 97,
    max_emails_per_prospect_account_per_week = 497,

    reply_handling = ReplyHandling.PAUSE_SPECIFIC_CAMPAIGN_ON_REPLY,
    created_at = aDate,
    selected_calendar_data = None,
    team_uuid = TeamUuid("uuid")
  )

  val accountAdmin = Account(
    id = AccountUuid("account_uuid"),
    internal_id = 2,
    email = email,
    email_verification_code = None,
    email_verification_code_created_at = None,
    created_at = DateTime.now().minusDays(1000),
    first_name = Some(first_name),
    last_name = Some(last_name),
    company = Some(company),
    timezone = None,
    profile = profile,
    org_role = Some(OrganizationRole.OWNER),
    teams = Seq(team_account),
    account_type = AccountType.AGENCY,
    org = org,
    active = true,
    email_notification_summary = "dSFA",
    account_metadata = accountMetadata,
    email_verified = true,
    signupType = None,
    account_access = AccountAccess(
      inbox_access = false
    ),
    calendar_account_data = None
  )

  val emailReplySavedV3 = EmailReplySavedV3(
    message_id = "some_message_id",
    email_body = "test body",
    email_scheduled_id = 345,
    email_thread_id = 456,
    by_account = true,
    sent_at = DateTime.now().minusDays(1),
    campaign_id = None,
    step_id = None,
    prospect_id_in_campaign = None,
    prospect_account_id_in_campaign = None,
    reply_type = EmailReplyType.NOT_CATEGORIZED,
    from_email = IEmailAddress(email = "<EMAIL>"),
    to_emails = Seq(IEmailAddress(email = "<EMAIL>"), IEmailAddress(email = "<EMAIL>")),
    cc_emails = Seq(),
    reply_to = Some(IEmailAddress(email = "<EMAIL>")),
    inbox_email_setting_id = 1,
    from_prospect = None,
    to_prospects = Seq(),
    campaign_associated_prospect = None,
    all_prospects_involved = Seq(),
    email_status = emailReplyStatus.copy(isReplied = true),
    subject = "SomeSubject",
    base_body = "SomeBaseBody",
    text_body = "SomeTextBody",
    full_headers = Json.obj(),

    gmail_msg_id = None,

    gmail_thread_id = None,
    outlook_msg_id = None,
    outlook_conversation_id = None,

    outlook_response_json = None,
    team_id = TeamId(team_id),
    references_header = None,
    in_reply_to_header = None
  )


  val cpCompleted = CPCompleted(
    campaignId = 1,
    prospectId = 2,
    completed = true)
  val dBSavedRepliesRes = DBEmailMessagesSavedResponse(
    savedMessages = Seq(emailReplySavedV3),
    emailMessagesFromProspects = Seq(emailReplySavedV3),
    prospectIdsWithHardBouncedEmail = Seq(3),
    prospectIdsWithSoftBouncedEmail = Seq(),
    emailScheduledIdsWithHardBouncedEmail = Seq(4),
    emailScheduledIdsWithSoftBouncedEmail = Seq(),
    emailScheduledIdsWithAutoReplyEmail = Seq(5),
    emailScheduledIdsWithOutOfOfficeReplyEmail = Seq(6),
    completedWebhookData = Seq(cpCompleted),
    gmailSendingLimitErrorEmailSettingId = Some(12),
    hardBouncedReplies = Seq(),
    softBouncedReplies = Seq()
  )

  describe("_updateDBAndCallWebhooksOnReplyTrackingV3") {
    it("emailReplyTrackingModel.saveEmailsAndRepliesFromInboxV3 fails") {

      (accountService.find(_: Long)(_: SRLogger))
        .expects(47, *)
        .returning(Success(accountAdmin))

      (emailReplyTrackingModelV2.saveEmailsAndRepliesFromInboxV3(_: Long, _: Long, _: Seq[EmailMessageTracked], _: EmailSetting, _: ReplyHandling.ReplyHandling,
        _: Account, _: Seq[String], _: Boolean, _: String, _: Boolean)(using _: SRLogger))
        .expects(47, team_id_347, Seq(emailMessageTracked), emailSetting, ReplyHandling.PAUSE_SPECIFIC_CAMPAIGN_ON_REPLY, accountAdmin, List(), false, "some_log_id", *, *)
        .returning(Failure(Error))

      emailService._updateDBAndCallWebhooksOnReplyTrackingV3(
        inboxEmailSetting = emailSetting,
        receivedEmails = Seq(emailMessageTracked),
        senderEmailSettings = Seq(),
        auditRequestLogId = "some_log_id",
        isReplyTracker = true
      ).map { result =>
        Logger.info(s"RESULT______________________________$result")
        assert(false)

      }.recover { case e =>
        Logger.info(s"ERROR______________________________$e")
        assert(e == Error)
      }
    }

    it("emailSettingDAO.updateLastReadForReplies failed") {

      (accountService.find(_: Long)(_: SRLogger))
        .expects(47, *)
        .returning(Success(accountAdmin))

      (emailReplyTrackingModelV2.saveEmailsAndRepliesFromInboxV3(_: Long, _: Long, _: Seq[EmailMessageTracked], _: EmailSetting, _: ReplyHandling.ReplyHandling,
        _: Account, _: Seq[String], _: Boolean, _: String, _: Boolean)(using _: SRLogger))
        .expects(47, team_id_347, Seq(emailMessageTracked), emailSetting, ReplyHandling.PAUSE_SPECIFIC_CAMPAIGN_ON_REPLY, accountAdmin, List(), false, "some_log_id", *, *)
        .returning(Success(dBSavedRepliesRes))

      (emailSettingDAO.updateLastReadForReplies)
        .expects(emailSetting, *, *)
        .returning(Failure(Error))

      //      (cacheEventScyllaService.addingBouncedReplyToScylla (_: Long, _: Long, _: Long, _: Seq[EmailReplySavedV3], _: String)( _: SRLogger, _: ExecutionContext))
      //        .expects(1, team_id_347, 123L, List(), "SomeSubject", *, *)
      //        .returning(Future.successful(Seq()))
      emailService._updateDBAndCallWebhooksOnReplyTrackingV3(
        inboxEmailSetting = emailSetting,
        receivedEmails = Seq(emailMessageTracked),
        senderEmailSettings = Seq(),
        auditRequestLogId = "some_log_id",
        isReplyTracker = true
      ).map { result =>
        Logger.info(s"RESULT______________________________$result")
        assert(false)

      }.recover { case e =>
        Logger.info(s"ERROR______________________________$e")
        assert(e == Error)
      }
    }

    it("success") {

      val savedMessagesWithProspectId = dBSavedRepliesRes.savedMessages
        .zipWithIndex
        .map { case (rep, index) => {
          rep.copy(
            campaign_id = Some(777L),
            prospect_id_in_campaign = Some(1234L + index), from_prospect = Some(
              ERIntermediateValidProspect(
                prospect_id = 1234L + index,
                email = "<EMAIL>",
                email_domain = "example.com",
                account_id = 1L,
                team_id = 2L,
                ta_id = 3L,
                prospect_account_id = None,
                prospect_category_id_custom = 8L,
                last_contacted_at = Some(DateTime.now().minusDays(2))
              )
            ))
        }
        }

      val dBSavedRepliesResWithProspectId = dBSavedRepliesRes.copy(savedMessages = savedMessagesWithProspectId)

      (accountService.find(_: Long)(_: SRLogger))
        .expects(47, *)
        .returning(Success(accountAdmin))

      (emailReplyTrackingModelV2.saveEmailsAndRepliesFromInboxV3(_: Long, _: Long, _: Seq[EmailMessageTracked], _: EmailSetting, _: ReplyHandling.ReplyHandling,
        _: Account, _: Seq[String], _: Boolean, _: String, _: Boolean)(using _: SRLogger))
        .expects(47, team_id_347, Seq(emailMessageTracked), emailSetting, ReplyHandling.PAUSE_SPECIFIC_CAMPAIGN_ON_REPLY, accountAdmin, List(), false, "some_log_id", *, *)
        .returning(Success(dBSavedRepliesResWithProspectId))

      (emailSettingDAO.updateLastReadForReplies)
        .expects(emailSetting, *, *)
        .returning(Success(None))

      (mqOpenTrackerPublisher.publish)
        .expects(*, *, *)
        .returning(Success(()))
      //      (cacheEventScyllaService.addingBouncedReplyToScylla (_: Long, _: Long, _: Long, _: Seq[EmailReplySavedV3], _: String)( _: SRLogger, _: ExecutionContext))
      //        .expects(1, team_id_347, 123L, List(), "SomeSubject", *, *)
      //        .returning(Future.successful(Seq()))
      (mqWebhookEmailInvalid.publish)
        .expects(*)
        .returning(Success(()))
      (mqActivityTriggerPublisher.publishEvents)
        .expects(*)
        .returning(())

      (mqActivityTriggerPublisher.publishEvents)
        .expects(*)
        .returning(())

      (mqActivityTriggerPublisher.publishEvents)
        .expects(*)
        .returning(())

      (campaignCacheService.resetCampaignStats(_: Long, _: Long)(using _: SRLogger))
        .expects(777, team_id_347, Logger)
        .returning(()).once()

      (mqActivityTriggerPublisher.publishEvents)
        .expects(*)
        .returning(())

      (mqWebhookCompleted.publishCompletedProspects(_: Long, _: Long, _: Seq[CPCompleted])(using _: SRLogger))
        .expects(47, team_id_347, List(cpCompleted), Logger)
        .returning(Success(0))


      // FIXME VALUECLASS
      (emailHandleErrorService.handleEmailSettingError(_: EmailSettingId, _: SendScheduleEmailType, _: Throwable, _: AccountId, _: TeamId, _: String, _: SRLogger)(_: SREmailErrors.EmailSettingError)(_: WSClient, _: ExecutionContext))
        .expects(EmailSettingId(emailSettingId = 12), SendScheduleEmailType.AutoEmail, *, AccountId(id = 47), TeamId(id = team_id_347), "<EMAIL>", *, *, *, *)
        .returning(Success(Seq(emailSetting)))

      emailService._updateDBAndCallWebhooksOnReplyTrackingV3(
        inboxEmailSetting = emailSetting,
        receivedEmails = Seq(emailMessageTracked),
        senderEmailSettings = Seq(),
        auditRequestLogId = "some_log_id",
        isReplyTracker = true
      ).map { result =>
        Logger.info(s"RESULT______________________________$result")
        assert(result == (emailSetting, 1, 1, List()))

      }
    }
  }

  val emailSentId = 1L
  val to_email1 = "<EMAIL>"
  val to_email2 = "<EMAIL>"
  val from_email = "<EMAIL>"
  val from_name = "Fromname"
  val cc_emails = "<EMAIL>"
  val bcc_emails = "<EMAIL>"
  val subject = "email subject"
  val textBody = " email textBody"
  val htmlBody = "email htmlBody"
  val message_id = "email msgId"

  val sender_email_settings_id = 1L

  val ccEmails: Seq[IEmailAddress] = IEmailAddress.parse(emailsStr = cc_emails).getOrElse(Seq())


  val bccEmails: Seq[IEmailAddress] = IEmailAddress.parse(emailsStr = bcc_emails).getOrElse(Seq())


  val emailToBeSent = EmailToBeSent(
    to_emails = List(IEmailAddress(email = to_email1), IEmailAddress(email = to_email2)),
    from_email = from_email,
    cc_emails = ccEmails,
    bcc_emails = bccEmails,
    from_name = from_name,
    reply_to_email = Some(from_email),
    reply_to_name = Some(from_name),

    subject = subject,
    textBody = textBody,
    htmlBody = htmlBody,
    message_id = Some(message_id),
    references_header = None,

    in_reply_to_id = None,
    in_reply_to_references_header = None,
    in_reply_to_sent_at = None,

    sender_email_settings_id = sender_email_settings_id,

    email_thread_id = Some(1L),
    gmail_msg_id = None,
    gmail_thread_id = None,

    outlook_msg_id = None,
    outlook_conversation_id = None,
    outlook_response_json = None,

    gmail_fbl = None,
    list_unsubscribe_header = None,
    hasCustomTrackingDomain = false,
    rep_smtp_reverse_dns_host = None

  )

  val accountId: Long = 1L
  val campaignId: Long = 1L
  val prospectIdInCampaign = Some(10L)
  val repTrackingHostId = 1

  val campaignName = "campaign name"

  val allEmailsInvolved: Seq[String] = {

    val allEmails = Seq(emailToBeSent.from_email) ++
      emailToBeSent.to_emails.map(_.email) ++
      emailToBeSent.reply_to_email.toSeq ++
      ccEmails.map(_.email) ++
      bccEmails.map(_.email)

    allEmails
      .map(_.trim.toLowerCase)
      .distinct

  }

  val prospectCheckForIsSentEmail = ProspectCheckForIsSentEmail(prospect_id = prospectIdInCampaign.get, email = from_email, owner_id = 1L, list_id = None, last_contacted_at = Some(aDate))

  val allProspectsInvolved: Seq[ProspectCheckForIsSentEmail] = Seq()

  val prospectInCampaign = allProspectsInvolved.find(p => p.prospect_id == prospectIdInCampaign.get)

  val emailThreadUuid = EmailThreadUuid("email_thread_268vbdh232")

  val newEmailThread = NewEmailThreadV3(
    temp_thread_id = 0,
    uuid = emailThreadUuid,
    // FIXME VALUECLASS
    owner_id = AccountId(id = accountId),
    // FIXME VALUECLASS
    team_id = TeamId(id = team_id),
    campaign_id = None,
    campaign_name = None,
    inbox_email = emailToBeSent.from_email,
    // FIXME VALUECLASS
    inbox_email_settings_id = EmailSettingId(emailSettingId = emailToBeSent.sender_email_settings_id),
    subject = emailToBeSent.subject,
    latest_email_id = Some(emailSentId),
    gmail_msg_id = emailToBeSent.gmail_msg_id,
    gmail_thread_id = emailToBeSent.gmail_thread_id,
    outlook_msg_id = emailToBeSent.outlook_msg_id,
    outlook_conversation_id = emailToBeSent.outlook_conversation_id,
    internal_tracking_note = InternalTrackingNote.NONE,
    has_prospect = false,
    folder_type = FolderType.NON_PROSPECTS
  )

  val threads = Seq(newEmailThread)

  val toEmails: String = IEmailAddress.stringify(emails = emailToBeSent.to_emails).get
  val toCcEmails: Option[String] = IEmailAddress.stringify(emails = emailToBeSent.cc_emails)
  val toBccEmails: Option[String] = IEmailAddress.stringify(emails = emailToBeSent.bcc_emails)

  val stepId = 1

  val stepDetails = StepDetails(
    step_id = stepId,
    step_name = "First",
    step_type = CampaignStepType.AutoEmailStep
  )

  val sendEmailFromCampaignDetails = SendEmailFromCampaignDetails(
    campaign_id = campaignId,
    campaign_name = campaignName,
    stepDetails = None
  )

  val emailScheduled = EmailScheduled(
    id = Some(1L),

    subject = Some(subject),

    message_id = Some(message_id),

    references_header = None,

    sendEmailFromCampaignDetails = Some(sendEmailFromCampaignDetails),
    prospect_id = prospectIdInCampaign,
    sender_email_settings_id = Some(sender_email_settings_id),

    email_thread_id = Some(1L),
    outlook_msg_id = None,

    scheduled_at = aDate,

    sent_at = Some(aDate),

    account_id = Some(accountId),
    team_id = Some(team_id)
  )


  describe("onEmailSent") {

    it("should return failure when prospectServiceV2.findByIdOrEmail and _insertNewThreads fails") {

      val emailToBeSent1 = emailToBeSent.copy(email_thread_id = None)

      (prospectServiceV2.findByIdOrEmail)
        .expects(Seq(), allEmailsInvolved, team_id, Logger)
        .returning(Failure(new Throwable("Failure while fetching findByIdOrEmail")))

      (() => srUuidUtils.generateEmailThreadsUuid())
        .expects()
        .returning(emailThreadUuid.uuid)

      (emailThreadDAOService.insertNewThreads)
        .expects(threads, false, *, team_id)
        .returning(Failure(new Throwable("Failure while _insertNewThreads")))

      emailSenderService.onEmailSent(
        emailSentId = emailSentId,
        data = emailToBeSent1,
        accountId = accountId,
        teamId = team_id,
        sendEmailFromCampaignDetails = None,
        prospectIdInCampaign = None,
        currentBillingCycleStartedAt = aDate,
        orgId = org.id,
        repTrackingHostId = repTrackingHostId
      ) match {
        case Failure(exception) =>
          Logger.info(s"Error:....... ${exception.getMessage}")
          assert(true)
        case Success(value) =>
          assert(false)
      }
    }

    it("should return failure when emailScheduledDAO.isSent fails") {

      (prospectServiceV2.findByIdOrEmail)
        .expects(Seq(), allEmailsInvolved, team_id, Logger)
        .returning(Success(Seq(prospectCheckForIsSentEmail)))

      //      (emailThreadDAO._insertNewThreads)
      //        .expects(threads, false)
      //        .returning(Success(Seq(SavedEmailThread(1L, None))))

      (emailScheduledDAOService.markAsSent)
        .expects(emailSentId, emailToBeSent, None, None, repTrackingHostId, Logger, toEmails, toCcEmails, toBccEmails, 1L, TeamId(id = team_id), *, *, *, *)
        .returning(Failure(new Throwable("Failure while emailScheduledDAO.isSent")))

      emailSenderService.onEmailSent(
        emailSentId = emailSentId,
        data = emailToBeSent,
        accountId = accountId,
        teamId = team_id,
        sendEmailFromCampaignDetails = None,
        prospectIdInCampaign = None,
        currentBillingCycleStartedAt = aDate,
        orgId = org.id,
        repTrackingHostId = repTrackingHostId
      ) match {
        case Failure(exception) =>
          Logger.info(s"Error:....... ${exception.getMessage}")
          assert(true)
        case Success(value) =>
          assert(false)
      }
    }

    it("should call emailThreadDAOService._associateEmailThreadsAndProspectsV3 when prospectInCampaign.isDefined") {

      val allProspectsInvolved1: Seq[ProspectCheckForIsSentEmail] = Seq(prospectCheckForIsSentEmail)

      val prospectInCampaign1 = allProspectsInvolved1.find(p => p.prospect_id == prospectIdInCampaign.get)

      //val newEmailThread1 = newEmailThread.copy(prospect_id = prospectInCampaign.map(_.email.toLowerCase.trim).getOrElse(emailToBeSent.to_emails.headOption.map(_.email).getOrElse("")))

      (prospectServiceV2.findByIdOrEmail)
        .expects(Seq(prospectIdInCampaign.get), allEmailsInvolved, team_id, Logger)
        .returning(Success(Seq(prospectCheckForIsSentEmail)))

      (emailCommonService.associateProspectsWithEmailThreads(_:Seq[AssociateEmailThreadProspect],_:Long)(using _:SRLogger))
        .expects(Seq(AssociateEmailThreadProspect(
          emailThreadId = emailToBeSent.email_thread_id.get,
          prospectId = prospectInCampaign1.get.prospect_id,
          temp_thread_id = Some(-2)
        )),*,*)
        .returning(Success(()))

      (emailScheduledDAOService.markAsSent)
        .expects(emailSentId, emailToBeSent, None, None, repTrackingHostId, Logger, toEmails, toCcEmails, toBccEmails, 1L, TeamId(id = team_id), *, *, *, *)
        .returning(Failure(new Throwable("Failure while emailScheduledDAO.isSent")))

      emailSenderService.onEmailSent(
        emailSentId = emailSentId,
        data = emailToBeSent,
        accountId = accountId,
        teamId = team_id,
        sendEmailFromCampaignDetails = None,
        prospectIdInCampaign = prospectIdInCampaign,
        currentBillingCycleStartedAt = aDate,
        orgId = org.id,
        repTrackingHostId = repTrackingHostId
      ) match {
        case Failure(exception) =>
          Logger.info(s"Error:....... ${exception.getMessage}")
          assert(true)
        case Success(value) =>
          assert(false)
      }
    }

    it("should return failure when insertEmailMessageContacts fails") {

      val allProspectsInvolved1: Seq[ProspectCheckForIsSentEmail] = Seq(prospectCheckForIsSentEmail)

      val prospectInCampaign1 = allProspectsInvolved1.find(p => p.prospect_id == prospectIdInCampaign.get)

      (prospectServiceV2.findByIdOrEmail)
        .expects(Seq(prospectIdInCampaign.get), allEmailsInvolved, team_id, Logger)
        .returning(Success(Seq(prospectCheckForIsSentEmail)))


      (emailCommonService.associateProspectsWithEmailThreads(_: Seq[AssociateEmailThreadProspect], _: Long)(using _: SRLogger))
        .expects(Seq(AssociateEmailThreadProspect(
          emailThreadId = emailToBeSent.email_thread_id.get,
          prospectId = prospectInCampaign1.get.prospect_id,
          temp_thread_id = Some(-2))), *, *)
        .returning(Failure(new Throwable("emailThreadDAOService._associateEmailThreadsAndProspectsV3 failed")))


      (emailScheduledDAOService.markAsSent)
        .expects(emailSentId, emailToBeSent, None, None, repTrackingHostId, Logger, toEmails, toCcEmails, toBccEmails, 1L, TeamId(id = team_id), *, *, *, *)
        .returning(Success(Some(emailScheduled)))

      (emailCommonService.saveEmailContacts(_:Seq[EmailCommonService.DataForEmailMessageContact],_:Seq[ProspectIdEmail],_:String, _: Long)(using _: SRLogger))
        .expects(*, *, *, *, *)
        .returning(Failure(new Throwable("Failure at emailMessageContactModel.insertEmailMessageContacts")))

      emailSenderService.onEmailSent(
        emailSentId = emailSentId,
        data = emailToBeSent,
        accountId = accountId,
        teamId = team_id,
        sendEmailFromCampaignDetails = None,
        prospectIdInCampaign = prospectIdInCampaign,
        currentBillingCycleStartedAt = aDate,
        orgId = org.id,
        repTrackingHostId = repTrackingHostId
      ) match {
        case Failure(exception) =>
          Logger.info(s"Error:....... ${exception.getMessage}")
          assert(true)
        case Success(value) =>
          assert(false)
      }
    }

    it("should return emailScheduled successfully case for last_contacted_at non empty") {

      val allProspectsInvolved1: Seq[ProspectCheckForIsSentEmail] = Seq(prospectCheckForIsSentEmail)

      val prospectInCampaign1 = allProspectsInvolved1.find(p => p.prospect_id == prospectIdInCampaign.get)

      (prospectServiceV2.findByIdOrEmail)
        .expects(Seq(prospectIdInCampaign.get), allEmailsInvolved, team_id, Logger)
        .returning(Success(Seq(prospectCheckForIsSentEmail)))

      (emailCommonService.associateProspectsWithEmailThreads(_: Seq[AssociateEmailThreadProspect], _: Long)(using _: SRLogger))
        .expects(Seq(AssociateEmailThreadProspect(
          emailThreadId = emailToBeSent.email_thread_id.get,
          prospectId = prospectInCampaign1.get.prospect_id,
          temp_thread_id = Some(-2))),*,*)
        .returning(Success(Seq()))

      (emailScheduledDAOService.markAsSent)
        .expects(emailSentId, emailToBeSent, Some("campaign name"), None, repTrackingHostId, Logger, toEmails, toCcEmails, toBccEmails, 1L, TeamId(id = team_id), *, *, *, *)
        .returning(Success(Some(emailScheduled.copy(
          sendEmailFromCampaignDetails = Some(sendEmailFromCampaignDetails.copy(
            stepDetails = Some(stepDetails)
          ))
        ))))

      (emailCommonService.saveEmailContacts(_:Seq[EmailCommonService.DataForEmailMessageContact],_:Seq[ProspectIdEmail],_:String, _: Long)(using _: SRLogger))
        .expects(*, *, *, *, *)
        .returning(())

      (campaignProspectDAO._hasBeenSent)
        .expects(*, *)
        .returning(Success(1))

      (prospectUpdateCategoryTemp.autoUpdateProspectCategory(
        _: TeamId, _: Seq[ProspectId], _: AccountId, _: ProspectCategory.ProspectCategory
      )(_: SRLogger)
        )
        .expects(
          TeamId(id = team_id),
          Seq(ProspectId(id = prospectIdInCampaign.get)),
          AccountId(id = accountId),
          ProspectCategory.CONTACTED,
          Logger,
        )
        .returning(Success(1))

      (emailSettingDAO._updateLastSent)
        .expects(*, *, *, *)
        .returning(1)

      (emailThreadDAOService._updateLatestEmailId)
        .expects(*)
        .returning(Seq(1))

      (campaignCacheService.resetCampaignStats(_: Long, _: Long)(using _: SRLogger))
        .expects(campaignId, team_id, *)

      val c1 = CaptureOne[Seq[CampaignProspectUpdateScheduleStatus]]()

      (campaignDAO.updateLastEmailSentAt)
        .expects(CampaignId(1L), TeamId(2))
        .returning(Success(1)) // 1 is update count
      (campaignSchedulingMetadataDAO.scheduleForUpdateWhenCampaignIsUpdated)
        .expects(CampaignId(1L), TeamId(2L))
        .returning(Success(1))

      (campaignProspectDAO._updateScheduledStatus(_: Seq[CampaignProspectUpdateScheduleStatus])(using _:SRLogger))
        .expects(capture(c1), *)
        .returning(Success(Seq(1)))

      (accountOrgBillingRelatedService.checkAndUpdateProspectsContacted)
        .expects(ProspectId(prospectInCampaign1.get.prospect_id), TeamId(team_id), ChannelType.EmailChannel, ProspectTouchedType.TaskDone, true, Logger)
        .returning(Success(1))

      (emailCommonService.incrementSentProspectCountIfNeeded(_: Option[ProspectCheckForIsSentEmail], _: DateTime, _: Long)(using _: SRLogger))
        .expects(*, *, *, *)
        .returning(())

      emailSenderService.onEmailSent(
        emailSentId = emailSentId,
        data = emailToBeSent,
        accountId = accountId,
        teamId = team_id,
        sendEmailFromCampaignDetails = Some(sendEmailFromCampaignDetails),
        prospectIdInCampaign = prospectIdInCampaign,
        currentBillingCycleStartedAt = aDate,
        orgId = org.id,
        repTrackingHostId = repTrackingHostId
      ) match {
        case Failure(exception) =>
          Logger.info(s"Error:....... ${exception.getMessage}")
          assert(false)
        case Success(emailScheduledSuccess) =>
          Logger.info(s"emailSenderService.onEmailSent success email scheduled:...... $emailScheduled")
          val campaignProspectUpdateScheduleStatusSequence = c1.value

          campaignProspectUpdateScheduleStatusSequence.map(campaignProspectUpdateScheduleStatus => {
            assert(campaignProspectUpdateScheduleStatus.step_id == stepId)
            assert(campaignProspectUpdateScheduleStatus.current_step_status_for_scheduler_data.status_type == CurrentStepStatusForScheduler.Done)

          })

          assert(campaignProspectUpdateScheduleStatusSequence.length == 1)

          assert(emailScheduledSuccess.id == emailScheduled.id)

      }
    }

    it("should return emailScheduled successfully case for last_contacted_at empty and prospectInCampaign.list_id nonEmpty") {
      val prospectCheckForIsSentEmail1 = prospectCheckForIsSentEmail.copy(last_contacted_at = None, list_id = Some(11))

      val allProspectsInvolved1: Seq[ProspectCheckForIsSentEmail] = Seq(prospectCheckForIsSentEmail1)

      val prospectInCampaign1 = allProspectsInvolved1.find(p => p.prospect_id == prospectIdInCampaign.get)

      (prospectServiceV2.findByIdOrEmail)
        .expects(Seq(prospectIdInCampaign.get), allEmailsInvolved, team_id, Logger)
        .returning(Success(Seq(prospectCheckForIsSentEmail1)))

      (emailCommonService.associateProspectsWithEmailThreads(_: Seq[AssociateEmailThreadProspect], _: Long)(using _: SRLogger))
        .expects(Seq(AssociateEmailThreadProspect(
          emailThreadId = emailToBeSent.email_thread_id.get,
          prospectId = prospectInCampaign1.get.prospect_id,
          temp_thread_id = Some(-2))),*,*)
        .returning(Success(()))

      (emailScheduledDAOService.markAsSent)
        .expects(emailSentId, emailToBeSent, Some("campaign name"), None, repTrackingHostId, Logger, toEmails, toCcEmails, toBccEmails, 1L, TeamId(id = team_id), *, *, *, *)
        .returning(Success(Some(emailScheduled.copy(
          sendEmailFromCampaignDetails = Some(sendEmailFromCampaignDetails.copy(
            stepDetails = Some(stepDetails)
          ))
        ))))

      (prospectUpdateCategoryTemp.autoUpdateProspectCategory(
        _: TeamId, _: Seq[ProspectId], _: AccountId, _: ProspectCategory.ProspectCategory
      )(_: SRLogger)
        )
        .expects(
          TeamId(id = team_id),
          Seq(ProspectId(id = prospectIdInCampaign.get)),
          AccountId(id = accountId),
          ProspectCategory.CONTACTED,
          Logger,
        )
        .returning(Success(1))

      (emailCommonService.saveEmailContacts(_:Seq[EmailCommonService.DataForEmailMessageContact],_:Seq[ProspectIdEmail],_:String, _: Long)(using _: SRLogger))
        .expects(*,*,*,*,*)
        .returning(())

      (campaignProspectDAO._hasBeenSent)
        .expects(*, *)
        .returning(Success(1))

      (accountOrgBillingRelatedService.checkAndUpdateProspectsContacted)
        .expects(ProspectId(prospectInCampaign1.get.prospect_id), TeamId(team_id), ChannelType.EmailChannel, ProspectTouchedType.TaskDone, true, Logger)
        .returning(Success(1))

      (emailCommonService.incrementSentProspectCountIfNeeded(_: Option[ProspectCheckForIsSentEmail], _: DateTime, _: Long)(using _: SRLogger))
        .expects(*, *, *, *)
        .returning(())


      (prospectDAOService.listUpdateLastContactedAt)
        .expects(prospectInCampaign1.get.list_id.get)
        .returning(Success(1))

      (emailSettingDAO._updateLastSent)
        .expects(*, *, *, *)
        .returning(1)

      (emailThreadDAOService._updateLatestEmailId)
        .expects(*)
        .returning(Seq(1))

      (campaignCacheService.resetCampaignStats(_: Long, _: Long)(using _: SRLogger))
        .expects(campaignId, team_id, *)

      val c1 = CaptureOne[Seq[CampaignProspectUpdateScheduleStatus]]()

      (campaignDAO.updateLastEmailSentAt)
        .expects(CampaignId(1L), TeamId(2))
        .returning(Success(1)) // 1 is update count

      (campaignSchedulingMetadataDAO.scheduleForUpdateWhenCampaignIsUpdated)
        .expects(CampaignId(1L), TeamId(2L))
        .returning(Success(1))
      (campaignProspectDAO._updateScheduledStatus(_: Seq[CampaignProspectUpdateScheduleStatus])(using _:SRLogger))
        .expects(capture(c1), *)
        .returning(Success(Seq(1)))

      emailSenderService.onEmailSent(
        emailSentId = emailSentId,
        data = emailToBeSent,
        accountId = accountId,
        teamId = team_id,
        sendEmailFromCampaignDetails = Some(sendEmailFromCampaignDetails),
        prospectIdInCampaign = prospectIdInCampaign,
        currentBillingCycleStartedAt = aDate,
        orgId = org.id,
        repTrackingHostId = repTrackingHostId
      ) match {
        case Failure(exception) =>
          Logger.info(s"Error:....... ${exception.getMessage}")
          assert(false)
        case Success(emailScheduledSuccess) =>
          Logger.info(s"emailSenderService.onEmailSent success email scheduled:...... $emailScheduled")

          val campaignProspectUpdateScheduleStatusSequence = c1.value

          campaignProspectUpdateScheduleStatusSequence.map(campaignProspectUpdateScheduleStatus => {
            assert(campaignProspectUpdateScheduleStatus.step_id == stepId)
            assert(campaignProspectUpdateScheduleStatus.current_step_status_for_scheduler_data.status_type == CurrentStepStatusForScheduler.Done)

          })

          assert(campaignProspectUpdateScheduleStatusSequence.length == 1)


          assert(emailScheduledSuccess.id == emailScheduled.id)
          assert(emailScheduledSuccess.sent_at == emailScheduled.sent_at)
      }
    }

  }


  describe("test auto reply and normal reply calls") {
    val emailReplyStatusNormalReply = EmailReplyStatus(
      replyType = EmailReplyType.NOT_CATEGORIZED,
      isReplied = true,
      isUnsubscribeRequest = false,
      isAutoReply = false,
      isOutOfOfficeReply = false,
      isInvalidEmail = false,
      isForwarded = false,
      bouncedData = None
    )


    val emailMessageTrackedNormalReply = EmailMessageTracked(
      inbox_email_setting_id = 11,
      from = IEmailAddress(email = "<EMAIL>"),
      to_emails = Seq(IEmailAddress(email = "<EMAIL>"), IEmailAddress(email = "<EMAIL>")),
      subject = "SomeSubject+NormalReply",
      body = "SomeBody+NormalReply",
      base_body = "SomeBaseBody+NormalReply",
      text_body = "SomeTextBody+NormalReply",
      references_header = None,
      campaign_id = Some(12),
      step_id = None,
      prospect_id_in_campaign = Some(121),
      prospect_account_id_in_campaign = None,
      campaign_name = None,
      step_name = None,
      received_at = DateTime.now().minusDays(2),
      recorded_at = DateTime.now().minusDays(2),
      sr_inbox_read = true,
      original_inbox_folder = None,
      email_status = emailReplyStatusNormalReply,
      message_id = "some_message_id+NormalReply",
      full_headers = Json.obj(),
      scheduled_manually = false,
      reply_to = None,
      email_thread_id = None,
      gmail_msg_id = None,
      gmail_thread_id = None,
      outlook_msg_id = None,
      outlook_conversation_id = None,
      outlook_response_json = None,
      cc_emails = Seq(),
      in_reply_to_header = None,
      team_id = 12,
      account_id = 13,
      internal_tracking_note = InternalTrackingNote.EXISTING_INREPLYTO,
      tempThreadId = None
    )

    val emailReplySavedV3NormalReply = EmailReplySavedV3(
      message_id = "some_message_id+NormalReply",
      email_body = "SomeBaseBody+NormalReply",
      email_scheduled_id = 345,
      email_thread_id = 456,
      by_account = true,
      sent_at = DateTime.now().minusDays(1),
      campaign_id = Some(12),
      step_id = None,
      prospect_id_in_campaign = Some(121),
      prospect_account_id_in_campaign = None,
      reply_type = EmailReplyType.NOT_CATEGORIZED,
      from_email = IEmailAddress(email = "<EMAIL>"),
      to_emails = Seq(IEmailAddress(email = "<EMAIL>"), IEmailAddress(email = "<EMAIL>")),
      cc_emails = Seq(),
      reply_to = Some(IEmailAddress(email = "<EMAIL>")),
      inbox_email_setting_id = 1,
      from_prospect = None,
      to_prospects = Seq(),
      campaign_associated_prospect = None,
      all_prospects_involved = Seq(),
      email_status = emailReplyStatusNormalReply,
      subject = "SomeSubject",
      base_body = "SomeBaseBody",
      text_body = "SomeTextBody",
      full_headers = Json.obj(),

      gmail_msg_id = None,

      gmail_thread_id = None,
      outlook_msg_id = None,
      outlook_conversation_id = None,

      outlook_response_json = None,
      team_id = TeamId(team_id),
      references_header = None,
      in_reply_to_header = None
    )


    val emailReplyStatusNormalReplyAndAutoReply = EmailReplyStatus(
      replyType = EmailReplyType.NOT_CATEGORIZED,
      isReplied = true,
      isUnsubscribeRequest = false,
      isAutoReply = true,
      isOutOfOfficeReply = false,
      isInvalidEmail = false,
      isForwarded = false,
      bouncedData = None
    )


    val emailMessageTrackedNormalReplyAndAutoReply = EmailMessageTracked(
      inbox_email_setting_id = 22,
      from = IEmailAddress(email = "<EMAIL>"),
      to_emails = Seq(IEmailAddress(email = "<EMAIL>"), IEmailAddress(email = "<EMAIL>")),
      subject = "SomeSubject+NormalReplyAndAutoReply",
      body = "SomeBody+NormalReplyAndAutoReply",
      base_body = "SomeBaseBody+NormalReplyAndAutoReply",
      text_body = "SomeTextBody+NormalReplyAndAutoReply",
      references_header = None,
      campaign_id = Some(12),
      step_id = None,
      prospect_id_in_campaign = Some(122),
      prospect_account_id_in_campaign = None,
      campaign_name = None,
      step_name = None,
      received_at = DateTime.now().minusDays(2),
      recorded_at = DateTime.now().minusDays(2),
      sr_inbox_read = true,
      original_inbox_folder = None,
      email_status = emailReplyStatusNormalReplyAndAutoReply,
      message_id = "some_message_id+NormalReplyAndAutoReply",
      full_headers = Json.obj(),
      scheduled_manually = false,
      reply_to = None,
      email_thread_id = None,
      gmail_msg_id = None,
      gmail_thread_id = None,
      outlook_msg_id = None,
      outlook_conversation_id = None,
      outlook_response_json = None,
      cc_emails = Seq(),
      in_reply_to_header = None,
      team_id = 12,
      account_id = 13,
      internal_tracking_note = InternalTrackingNote.EXISTING_INREPLYTO,
      tempThreadId = None
    )
    val emailReplySavedV3NormalReplyAndAutoReply = EmailReplySavedV3(
      message_id = "some_message_id+NormalReplyAndAutoReply",
      email_body = "SomeBody+NormalReplyAndAutoReply",
      email_scheduled_id = 346,
      email_thread_id = 457,
      by_account = true,
      sent_at = DateTime.now().minusDays(1),
      campaign_id = Some(12),
      step_id = None,
      prospect_id_in_campaign = Some(122),
      prospect_account_id_in_campaign = None,
      reply_type = EmailReplyType.NOT_CATEGORIZED,
      from_email = IEmailAddress(email = "<EMAIL>"),
      to_emails = Seq(IEmailAddress(email = "<EMAIL>"), IEmailAddress(email = "<EMAIL>")),
      cc_emails = Seq(),
      reply_to = Some(IEmailAddress(email = "<EMAIL>")),
      inbox_email_setting_id = 1,
      from_prospect = None,
      to_prospects = Seq(),
      campaign_associated_prospect = None,
      all_prospects_involved = Seq(),
      email_status = emailReplyStatusNormalReplyAndAutoReply,
      subject = "SomeSubject",
      base_body = "SomeBaseBody",
      text_body = "SomeTextBody",
      full_headers = Json.obj(),

      gmail_msg_id = None,

      gmail_thread_id = None,
      outlook_msg_id = None,
      outlook_conversation_id = None,

      outlook_response_json = None,
      team_id = TeamId(team_id),
      references_header = None,
      in_reply_to_header = None
    )


    val emailReplyStatusAutoReply = EmailReplyStatus(
      replyType = EmailReplyType.NOT_CATEGORIZED,
      isReplied = false,
      isUnsubscribeRequest = false,
      isAutoReply = true,
      isOutOfOfficeReply = false,
      isInvalidEmail = false,
      isForwarded = false,
      bouncedData = None
    )


    val emailMessageTrackedAutoReply = EmailMessageTracked(
      inbox_email_setting_id = 33,
      from = IEmailAddress(email = "<EMAIL>"),
      to_emails = Seq(IEmailAddress(email = "<EMAIL>"), IEmailAddress(email = "<EMAIL>")),
      subject = "SomeSubject+AutoReply",
      body = "SomeBody+AutoReply",
      base_body = "SomeBaseBody+AutoReply",
      text_body = "SomeTextBody+AutoReply",
      references_header = None,
      campaign_id = Some(12),
      step_id = None,
      prospect_id_in_campaign = Some(124),
      prospect_account_id_in_campaign = None,
      campaign_name = None,
      step_name = None,
      received_at = DateTime.now().minusDays(2),
      recorded_at = DateTime.now().minusDays(2),
      sr_inbox_read = true,
      original_inbox_folder = None,
      email_status = emailReplyStatusAutoReply,
      message_id = "some_message_id+AutoReply",
      full_headers = Json.obj(),
      scheduled_manually = false,
      reply_to = None,
      email_thread_id = None,
      gmail_msg_id = None,
      gmail_thread_id = None,
      outlook_msg_id = None,
      outlook_conversation_id = None,
      outlook_response_json = None,
      cc_emails = Seq(),
      in_reply_to_header = None,
      team_id = 1,
      account_id = 1,
      internal_tracking_note = InternalTrackingNote.EXISTING_INREPLYTO,
      tempThreadId = None
    )

    val emailReplySavedV3AutoReply = EmailReplySavedV3(
      message_id = "some_message_id+AutoReply",
      email_body = "SomeBaseBody+AutoReply",
      email_scheduled_id = 347,
      email_thread_id = 458,
      by_account = true,
      sent_at = DateTime.now().minusDays(1),
      campaign_id = Some(12),
      step_id = None,
      prospect_id_in_campaign = Some(124),
      prospect_account_id_in_campaign = None,
      reply_type = EmailReplyType.NOT_CATEGORIZED,
      from_email = IEmailAddress(email = "<EMAIL>"),
      to_emails = Seq(IEmailAddress(email = "<EMAIL>"), IEmailAddress(email = "<EMAIL>")),
      cc_emails = Seq(),
      reply_to = Some(IEmailAddress(email = "<EMAIL>")),
      inbox_email_setting_id = 1,
      from_prospect = None,
      to_prospects = Seq(),
      campaign_associated_prospect = None,
      all_prospects_involved = Seq(),
      email_status = emailReplyStatusNormalReplyAndAutoReply,
      subject = "SomeSubject",
      base_body = "SomeBaseBody",
      text_body = "SomeTextBody",
      full_headers = Json.obj(),

      gmail_msg_id = None,

      gmail_thread_id = None,
      outlook_msg_id = None,
      outlook_conversation_id = None,

      outlook_response_json = None,
      team_id = TeamId(team_id),
      references_header = None,
      in_reply_to_header = None
    )


    val dBSavedRepliesRes = DBEmailMessagesSavedResponse(
      savedMessages = Seq(emailReplySavedV3NormalReply, emailReplySavedV3NormalReplyAndAutoReply, emailReplySavedV3AutoReply),
      emailMessagesFromProspects = Seq(emailReplySavedV3NormalReply, emailReplySavedV3NormalReplyAndAutoReply, emailReplySavedV3AutoReply),
      prospectIdsWithHardBouncedEmail = Seq(),
      prospectIdsWithSoftBouncedEmail = Seq(),
      emailScheduledIdsWithHardBouncedEmail = Seq(),
      emailScheduledIdsWithSoftBouncedEmail = Seq(),
      emailScheduledIdsWithAutoReplyEmail = Seq(emailReplySavedV3NormalReplyAndAutoReply.email_scheduled_id, emailReplySavedV3AutoReply.email_scheduled_id),
      emailScheduledIdsWithOutOfOfficeReplyEmail = Seq(),
      completedWebhookData = Seq(),
      gmailSendingLimitErrorEmailSettingId = None,
      hardBouncedReplies = Seq(),
      softBouncedReplies = Seq()
    )


    it("Fixing bug: Adding an email to both NEW_REPLY and AUTO_REPLY") {

      (accountService.find(_: Long)(_: SRLogger))
        .expects(47, *)
        .returning(Success(accountAdmin))

      (emailReplyTrackingModelV2.saveEmailsAndRepliesFromInboxV3(_: Long, _: Long, _: Seq[EmailMessageTracked], _: EmailSetting, _: ReplyHandling.ReplyHandling,
        _: Account, _: Seq[String], _: Boolean, _: String, _: Boolean)(using _: SRLogger))
        .expects(47, team_id_347, Seq(emailMessageTrackedNormalReply, emailMessageTrackedNormalReplyAndAutoReply, emailMessageTrackedAutoReply), *, *, *, *, *, *, *, *)
        .returning(Success(dBSavedRepliesRes))

      //      (cacheEventScyllaService.addingBouncedReplyToScylla(_: Long, _: Long, _: Long, _: Seq[EmailReplySavedV3], _: String)(_: SRLogger, _: ExecutionContext))
      //        .expects(*, team_id_347, *, List(), *, *, *)
      //        .returning(Future.successful(Seq()))

      (emailSettingDAO.updateLastReadForReplies)
        .expects(*, *, *)
        .returning(Success(None))

      (mqOpenTrackerPublisher.publish)
        .expects(*, *, *)
        .returning(Success(()))
        .repeat(3)


      (mqActivityTriggerPublisher.publishEvents)
        .expects(
          MQActivityTriggerMsgForm(
            accountId = 47,
            teamId = 347,
            campaignId = None,
            prospectIds = Seq(),
            clickedUrl = None,
            campaignName = None,
            emailScheduledIds = Some(Seq(emailReplySavedV3NormalReplyAndAutoReply.email_scheduled_id, emailReplySavedV3AutoReply.email_scheduled_id)),
            event = EventType.AUTO_REPLY.toString,
            threadId = None,
            replySentimentUuid = None)
        )
        .returning(())

      (campaignCacheService.resetCampaignStats(_: Long, _: Long)(using _: SRLogger))
        .expects(12, team_id_347, Logger)
        .returning(())
      //        .repeat(3)

      (mqActivityTriggerPublisher.publishEvents)
        .expects(
          MQActivityTriggerMsgForm(
            accountId = 47,
            teamId = 347,
            campaignId = None,
            prospectIds = Seq(),
            clickedUrl = None,
            campaignName = None,
            emailScheduledIds = Some(Seq(emailReplySavedV3NormalReply.email_scheduled_id,
              //              emailReplySavedV3NormalReplyAndAutoReply.email_scheduled_id, emailReplySavedV3AutoReply.email_scheduled_id
            )),
            event = EventType.NEW_REPLY.toString,
            threadId = None,
            replySentimentUuid = None)
        )
        .returning(())

      //      (webhookUtils.callNewRepliesHooks(_: Long, _: Long, _: Seq[Long], _: Option[Seq[CPRepliedEvent]], _: SRLogger)( _: ExecutionContext, _: WSClient))
      //        .expects(47, 347, List(emailReplySavedV3NormalReply.email_scheduled_id, emailReplySavedV3NormalReplyAndAutoReply.email_scheduled_id, emailReplySavedV3AutoReply.email_scheduled_id), None, *, *, *)
      //        .returning(Future(Seq()))

      emailService._updateDBAndCallWebhooksOnReplyTrackingV3(
        inboxEmailSetting = emailSetting,
        receivedEmails = Seq(emailMessageTrackedNormalReply, emailMessageTrackedNormalReplyAndAutoReply, emailMessageTrackedAutoReply),
        senderEmailSettings = Seq(),
        auditRequestLogId = "some_log_id",
        isReplyTracker = true
      ).map { result =>
        Logger.info(s"RESULT______________________________$result")
        assert(true)

      }.recover { case e =>
        Logger.fatal(s"ERROR______________________________$e")
        assert(e == Error)
      }
    }

    it("Fixing bug: Manual reply from user being taken as new reply from prospect") {

      val emailReplyStatusNormalReply = EmailReplyStatus(
        replyType = EmailReplyType.NOT_CATEGORIZED,
        isReplied = true,
        isUnsubscribeRequest = false,
        isAutoReply = false,
        isOutOfOfficeReply = false,
        isInvalidEmail = false,
        isForwarded = false,
        bouncedData = None
      )


      val emailMessageTrackedForManualReplyFromUser = EmailMessageTracked(
        inbox_email_setting_id = 11,
        from = IEmailAddress(email = "<EMAIL>"),
        to_emails = Seq(IEmailAddress(email = "<EMAIL>"), IEmailAddress(email = "<EMAIL>")),
        subject = "SomeSubject+NormalReply",
        body = "SomeBody+NormalReply",
        base_body = "SomeBaseBody+NormalReply",
        text_body = "SomeTextBody+NormalReply",
        references_header = None,
        campaign_id = Some(12),
        step_id = None,
        prospect_id_in_campaign = Some(121),
        prospect_account_id_in_campaign = None,
        campaign_name = None,
        step_name = None,
        received_at = DateTime.now().minusDays(2),
        recorded_at = DateTime.now().minusDays(2),
        sr_inbox_read = true,
        original_inbox_folder = None,
        email_status = emailReplyStatusNormalReply,
        message_id = "some_message_id+NormalReply",
        full_headers = Json.obj(),
        scheduled_manually = false,
        reply_to = None,
        email_thread_id = None,
        gmail_msg_id = None,
        gmail_thread_id = None,
        outlook_msg_id = None,
        outlook_conversation_id = None,
        outlook_response_json = None,
        cc_emails = Seq(),
        in_reply_to_header = None,
        team_id = 12,
        account_id = 13,
        internal_tracking_note = InternalTrackingNote.EXISTING_INREPLYTO,
        tempThreadId = None
      )

      val emailReplySavedV3ForManualReplyFromUser = EmailReplySavedV3(
        message_id = "some_message_id+NormalReply",
        email_body = "SomeBaseBody+NormalReply",
        email_scheduled_id = 345,
        email_thread_id = 456,
        by_account = true,
        sent_at = DateTime.now().minusDays(1),
        campaign_id = Some(12),
        step_id = None,
        prospect_id_in_campaign = Some(121),
        prospect_account_id_in_campaign = None,
        reply_type = EmailReplyType.NOT_CATEGORIZED,
        from_email = IEmailAddress(email = "<EMAIL>"),
        to_emails = Seq(IEmailAddress(email = "<EMAIL>"), IEmailAddress(email = "<EMAIL>")),
        cc_emails = Seq(),
        reply_to = Some(IEmailAddress(email = "<EMAIL>")),
        inbox_email_setting_id = 1,
        from_prospect = None,
        to_prospects = Seq(),
        campaign_associated_prospect = None,
        all_prospects_involved = Seq(),
        email_status = emailReplyStatusNormalReply,
        subject = "SomeSubject",
        base_body = "SomeBaseBody",
        text_body = "SomeTextBody",
        full_headers = Json.obj(),

        gmail_msg_id = None,

        gmail_thread_id = None,
        outlook_msg_id = None,
        outlook_conversation_id = None,

        outlook_response_json = None,
        team_id = TeamId(team_id),
        references_header = None,
        in_reply_to_header = None
      )


      val emailMessageTrackedNormalReply2 = EmailMessageTracked(
        inbox_email_setting_id = 22,
        from = IEmailAddress(email = "<EMAIL>"),
        to_emails = Seq(IEmailAddress(email = "<EMAIL>"), IEmailAddress(email = "<EMAIL>")),
        subject = "SomeSubject+NormalReplyAndAutoReply",
        body = "SomeBody+NormalReplyAndAutoReply",
        base_body = "SomeBaseBody+NormalReplyAndAutoReply",
        text_body = "SomeTextBody+NormalReplyAndAutoReply",
        references_header = None,
        campaign_id = Some(12),
        step_id = None,
        prospect_id_in_campaign = Some(122),
        prospect_account_id_in_campaign = None,
        campaign_name = None,
        step_name = None,
        received_at = DateTime.now().minusDays(2),
        recorded_at = DateTime.now().minusDays(2),
        sr_inbox_read = true,
        original_inbox_folder = None,
        email_status = emailReplyStatusNormalReply,
        message_id = "some_message_id+NormalReplyAndAutoReply",
        full_headers = Json.obj(),
        scheduled_manually = false,
        reply_to = None,
        email_thread_id = None,
        gmail_msg_id = None,
        gmail_thread_id = None,
        outlook_msg_id = None,
        outlook_conversation_id = None,
        outlook_response_json = None,
        cc_emails = Seq(),
        in_reply_to_header = None,
        team_id = 12,
        account_id = 13,
        internal_tracking_note = InternalTrackingNote.EXISTING_INREPLYTO,
        tempThreadId = None
      )
      val emailReplySavedV3_2 = EmailReplySavedV3(
        message_id = "some_message_id+NormalReplyAndAutoReply",
        email_body = "SomeBody+NormalReplyAndAutoReply",
        email_scheduled_id = 346,
        email_thread_id = 457,
        by_account = true,
        sent_at = DateTime.now().minusDays(1),
        campaign_id = Some(12),
        step_id = None,
        prospect_id_in_campaign = Some(122),
        prospect_account_id_in_campaign = None,
        reply_type = EmailReplyType.NOT_CATEGORIZED,
        from_email = IEmailAddress(email = "<EMAIL>"),
        to_emails = Seq(IEmailAddress(email = "<EMAIL>"), IEmailAddress(email = "<EMAIL>")),
        cc_emails = Seq(),
        reply_to = Some(IEmailAddress(email = "<EMAIL>")),
        inbox_email_setting_id = 1,
        from_prospect = None,
        to_prospects = Seq(),
        campaign_associated_prospect = None,
        all_prospects_involved = Seq(),
        email_status = emailReplyStatusNormalReply,
        subject = "SomeSubject",
        base_body = "SomeBaseBody",
        text_body = "SomeTextBody",
        full_headers = Json.obj(),

        gmail_msg_id = None,

        gmail_thread_id = None,
        outlook_msg_id = None,
        outlook_conversation_id = None,

        outlook_response_json = None,
        team_id = TeamId(team_id),
        references_header = None,
        in_reply_to_header = None
      )

      val emailMessageTrackedNormalReply3 = EmailMessageTracked(
        inbox_email_setting_id = 33,
        from = IEmailAddress(email = "<EMAIL>"),
        to_emails = Seq(IEmailAddress(email = "<EMAIL>"), IEmailAddress(email = "<EMAIL>")),
        subject = "SomeSubject+AutoReply",
        body = "SomeBody+AutoReply",
        base_body = "SomeBaseBody+AutoReply",
        text_body = "SomeTextBody+AutoReply",
        references_header = None,
        campaign_id = Some(12),
        step_id = None,
        prospect_id_in_campaign = Some(124),
        prospect_account_id_in_campaign = None,
        campaign_name = None,
        step_name = None,
        received_at = DateTime.now().minusDays(2),
        recorded_at = DateTime.now().minusDays(2),
        sr_inbox_read = true,
        original_inbox_folder = None,
        email_status = emailReplyStatusNormalReply,
        message_id = "some_message_id+AutoReply",
        full_headers = Json.obj(),
        scheduled_manually = false,
        reply_to = None,
        email_thread_id = None,
        gmail_msg_id = None,
        gmail_thread_id = None,
        outlook_msg_id = None,
        outlook_conversation_id = None,
        outlook_response_json = None,
        cc_emails = Seq(),
        in_reply_to_header = None,
        team_id = 1,
        account_id = 1,
        internal_tracking_note = InternalTrackingNote.EXISTING_INREPLYTO,
        tempThreadId = None
      )

      val emailReplySavedV3_3 = EmailReplySavedV3(
        message_id = "some_message_id+AutoReply",
        email_body = "SomeBaseBody+AutoReply",
        email_scheduled_id = 347,
        email_thread_id = 458,
        by_account = true,
        sent_at = DateTime.now().minusDays(1),
        campaign_id = Some(12),
        step_id = None,
        prospect_id_in_campaign = Some(124),
        prospect_account_id_in_campaign = None,
        reply_type = EmailReplyType.NOT_CATEGORIZED,
        from_email = IEmailAddress(email = "<EMAIL>"),
        to_emails = Seq(IEmailAddress(email = "<EMAIL>"), IEmailAddress(email = "<EMAIL>")),
        cc_emails = Seq(),
        reply_to = Some(IEmailAddress(email = "<EMAIL>")),
        inbox_email_setting_id = 1,
        from_prospect = None,
        to_prospects = Seq(),
        campaign_associated_prospect = None,
        all_prospects_involved = Seq(),
        email_status = emailReplyStatusNormalReply,
        subject = "SomeSubject",
        base_body = "SomeBaseBody",
        text_body = "SomeTextBody",
        full_headers = Json.obj(),

        gmail_msg_id = None,

        gmail_thread_id = None,
        outlook_msg_id = None,
        outlook_conversation_id = None,

        outlook_response_json = None,
        team_id = TeamId(team_id),
        references_header = None,
        in_reply_to_header = None
      )


      val dBSavedRepliesRes = DBEmailMessagesSavedResponse(
        savedMessages = Seq(emailReplySavedV3ForManualReplyFromUser, emailReplySavedV3_2, emailReplySavedV3_3),
        emailMessagesFromProspects = Seq(emailReplySavedV3_2, emailReplySavedV3_3),
        prospectIdsWithHardBouncedEmail = Seq(),
        prospectIdsWithSoftBouncedEmail = Seq(),
        emailScheduledIdsWithHardBouncedEmail = Seq(),
        emailScheduledIdsWithSoftBouncedEmail = Seq(),
        emailScheduledIdsWithAutoReplyEmail = Seq(),
        emailScheduledIdsWithOutOfOfficeReplyEmail = Seq(),
        completedWebhookData = Seq(),
        gmailSendingLimitErrorEmailSettingId = None,
        hardBouncedReplies = Seq(),
        softBouncedReplies = Seq()
      )

      (accountService.find(_: Long)(_: SRLogger))
        .expects(47, *)
        .returning(Success(accountAdmin))

      (emailReplyTrackingModelV2.saveEmailsAndRepliesFromInboxV3(_: Long, _: Long, _: Seq[EmailMessageTracked], _: EmailSetting, _: ReplyHandling.ReplyHandling,
        _: Account, _: Seq[String], _: Boolean, _: String, _: Boolean)(using _: SRLogger))
        .expects(47, team_id_347, Seq(emailMessageTrackedForManualReplyFromUser, emailMessageTrackedNormalReply2, emailMessageTrackedNormalReply3), *, *, *, *, *, *, *, *)
        .returning(Success(dBSavedRepliesRes))

      //      (cacheEventScyllaService.addingBouncedReplyToScylla(_: Long, _: Long, _: Long, _: Seq[EmailReplySavedV3], _: String)(_: SRLogger, _: ExecutionContext))
      //        .expects(*, team_id_347, *, List(), *, *, *)
      //        .returning(Future.successful(Seq()))

      (emailSettingDAO.updateLastReadForReplies)
        .expects(*, *, *)
        .returning(Success(None))

      (mqOpenTrackerPublisher.publish)
        .expects(*, *, *)
        .returning(Success(()))
        .repeat(2)

      (campaignCacheService.resetCampaignStats(_: Long, _: Long)(using _: SRLogger))
        .expects(12, team_id_347, *)
        .repeat(2)


      (mqActivityTriggerPublisher.publishEvents)
        .expects(
          MQActivityTriggerMsgForm(
            accountId = 47,
            teamId = 347,
            campaignId = None,
            prospectIds = Seq(),
            clickedUrl = None,
            campaignName = None,
            emailScheduledIds = Some(Seq(emailReplySavedV3_2.email_scheduled_id, emailReplySavedV3_3.email_scheduled_id)),
            event = EventType.NEW_REPLY.toString,
            threadId = None,
            replySentimentUuid = None)
        )
        .returning(())


      emailService._updateDBAndCallWebhooksOnReplyTrackingV3(
        inboxEmailSetting = emailSetting,
        receivedEmails = Seq(emailMessageTrackedForManualReplyFromUser, emailMessageTrackedNormalReply2, emailMessageTrackedNormalReply3),
        senderEmailSettings = Seq(
          emailSetting.copy(email = "<EMAIL>"),
          emailSetting.copy(email = "<EMAIL>")),
        auditRequestLogId = "some_log_id",
        isReplyTracker = true
      ).map { result =>
        Logger.info(s"RESULT______________________________$result")
        assert(true)

      }.recover { case e =>
        Logger.fatal(s"ERROR______________________________$e")
        assert(e == Error)
      }
    }
  }

  describe("EmailsService.generateDraftForInbox") {

    val signature = "<br><br>Thanks & Regards<br>Prachi<br>"

    val prospectObjectInternal = ProspectFixtures.prospectObjectInternal

    val prospectObject = ProspectObject(
      id = 1,
      owner_id = 1,
      team_id = 1,
      first_name = Some("Prachi"),
      last_name = Some("Mane"),
      email = Some("<EMAIL>"),
      custom_fields = Json.obj("customField" -> "this is a custom filed"),
      list = None,
      job_title = Some("SDE"),
      company = Some("Smartreach"),
      linkedin_url = Some("PrachiMane@Smartreach"),
      phone = None,
      phone_2 = None,
      phone_3 = None,
      city = Some("Pune"),
      state = Some("Maha"),
      country = Some("India"),
      timezone = Some("IN"),
      prospect_category = "",
      last_contacted_at = None,
      last_contacted_at_phone = None,
      created_at = DateTime.now().minusMonths(10),
      internal = prospectObjectInternal,
      latest_reply_sentiment_uuid = None,
      current_step_type = None,
      latest_task_done_at = None,
      prospect_uuid = Some(ProspectUuid("prs_aa_abcdefghi")),
      owner_uuid = AccountUuid("acc_aa_abcdegfhi"),
      updated_at = DateTime.now()
    )

    val bodyTemplate = "Hi, <br><br>Any update?<br><br>"
    val subjectTemplate = "Regarding something"

    val email_sender_name = "Prachi"
    val sender_first_name = "Prachi"
    val sender_last_name = "Mane"

    val channel: ChannelType = ChannelType.EmailChannel

    it("should prepend signature if it's draft and applying_email_template is false") {


      (templateService.render(_: String, _: ProspectObject, _: InternalMergeTagValuesForProspect, _: ChannelType)(using _: SRLogger))
        .expects(bodyTemplate, prospectObject, *, channel, *)
        .returning(Success(bodyTemplate))

      (templateService.render(_: String, _: ProspectObject, _: InternalMergeTagValuesForProspect, _: ChannelType)(using _: SRLogger))
        .expects(subjectTemplate, prospectObject, *, channel, *)
        .returning(Success(subjectTemplate))

      val result = emailService.generateDraftForInbox(
        signature = Some(signature),
        prospect = prospectObject,
        prospectAccount = Some(ProspectAccountFixture.prospectAccount),

        bodyTemplate = Some(bodyTemplate),
        subjectTemplate = Some(subjectTemplate),

        email_sender_name = email_sender_name,
        sender_first_name = sender_first_name,
        sender_last_name = sender_last_name,

        applying_email_template = false
      )

      val expected_result = (
        """<html>
          | <head></head>
          | <body>
          |  <br><br><br><br>
          |  Thanks &amp; Regards<br>
          |  Prachi<br>
          |  Hi, <br><br>
          |  Any update?<br><br>
          | </body>
          |</html>""".stripMargin, "Regarding something")


      assert(result == Success(expected_result))

    }

    it("should not append/prepend signature if it's draft and applying_email_template is true") {


      (templateService.render(_: String, _: ProspectObject, _: InternalMergeTagValuesForProspect, _: ChannelType)(using _: SRLogger))
        .expects(bodyTemplate, prospectObject, *, channel, *)
        .returning(Success(bodyTemplate))

      (templateService.render(_: String, _: ProspectObject, _: InternalMergeTagValuesForProspect, _: ChannelType)(using _: SRLogger))
        .expects(subjectTemplate, prospectObject, *, channel, *)
        .returning(Success(subjectTemplate))

      val result = emailService.generateDraftForInbox(
        signature = Some(signature),
        prospect = prospectObject,
        prospectAccount = Some(ProspectAccountFixture.prospectAccount),

        bodyTemplate = Some(bodyTemplate),
        subjectTemplate = Some(subjectTemplate),

        email_sender_name = email_sender_name,
        sender_first_name = sender_first_name,
        sender_last_name = sender_last_name,

        applying_email_template = true
      )

      val expected_result = (
        """<html>
          | <head></head>
          | <body>
          |  Hi, <br><br>
          |  Any update?<br><br>
          | </body>
          |</html>""".stripMargin, "Regarding something")


      assert(result == Success(expected_result))

    }

    it("should not append signature if it's append_signature false") {

      val bodyTemplate = "Hi, <br><br>Any update?<br><br>{{signature}}"


      (templateService.render(_: String, _: ProspectObject, _: InternalMergeTagValuesForProspect, _: ChannelType)(using _: SRLogger))
        .expects(bodyTemplate, prospectObject, *, channel, *)
        .returning(Success(bodyTemplate))

      (templateService.render(_: String, _: ProspectObject, _: InternalMergeTagValuesForProspect, _: ChannelType)(using _: SRLogger))
        .expects(subjectTemplate, prospectObject, *, channel, *)
        .returning(Success(subjectTemplate))

      val result = emailService.generateDraftForInbox(
        signature = None,
        prospect = prospectObject,
        prospectAccount = Some(ProspectAccountFixture.prospectAccount),

        bodyTemplate = Some(bodyTemplate),
        subjectTemplate = Some(subjectTemplate),

        email_sender_name = email_sender_name,
        sender_first_name = sender_first_name,
        sender_last_name = sender_last_name,

        applying_email_template = false
      )
      val expected_result = (
        """<html>
          | <head></head>
          | <body>
          |  Hi, <br><br>
          |  Any update?<br><br>
          |  {{signature}}
          | </body>
          |</html>""".stripMargin, "Regarding something")


      assert(result == Success(expected_result))

    }

  }

  describe("getBadOutBoundRepliesInLastXMinutes") {

    it("should give outbound error ") {
      val result = EmailServiceTemp.getBadOutBoundRepliesInLastXMinutes(
        receivedEmails = Vector(
          emailMessageTracked.copy(
            received_at = DateTime.now(),
            email_status = emailMessageTracked.email_status.copy(
              bouncedData = Some(BounceData(
                bounced_at = DateTime.now(),
                bounce_type = EmailReplyBounceType.SpamComplaint,
                bounce_reason = EmailReplyStatusCommon.BAD_OUTBOUND_SENDER,
                is_soft_bounced = false
              ))
            )
          )
        ),
        service_provider = EmailServiceProvider.OUTLOOK_API
      )

      assert(result.nonEmpty)
    }

    it("should not give outbound error - V1") {
      val result = EmailServiceTemp.getBadOutBoundRepliesInLastXMinutes(
        receivedEmails = Vector(
          emailMessageTracked.copy(
            received_at = DateTime.now(),
            email_status = emailMessageTracked.email_status.copy(
              bouncedData = Some(BounceData(
                bounced_at = DateTime.now(),
                bounce_type = EmailReplyBounceType.SpamComplaint,
                bounce_reason = "EmailReplyStatusCommon",
                is_soft_bounced = false
              ))
            )
          )
        ),
        service_provider = EmailServiceProvider.OUTLOOK_API
      )

      assert(result.isEmpty)
    }

    it("should not give outbound error - V2") {
      val result = EmailServiceTemp.getBadOutBoundRepliesInLastXMinutes(
        receivedEmails = Vector(
          emailMessageTracked.copy(
            received_at = DateTime.now(),
            email_status = emailMessageTracked.email_status.copy(
              bouncedData = Some(BounceData(
                bounced_at = DateTime.now(),
                bounce_type = EmailReplyBounceType.SpamComplaint,
                bounce_reason = EmailReplyStatusCommon.BAD_OUTBOUND_SENDER,
                is_soft_bounced = false
              ))
            )
          )
        ),
        service_provider = EmailServiceProvider.GMAIL_ASP
      )

      assert(result.isEmpty)
    }

    it("should not give outbound error - V3") {
      val result = EmailServiceTemp.getBadOutBoundRepliesInLastXMinutes(
        receivedEmails = Vector(
          emailMessageTracked.copy(
            received_at = DateTime.now(),
            email_status = emailMessageTracked.email_status.copy(
              bouncedData = Some(BounceData(
                bounced_at = DateTime.now(),
                bounce_type = EmailReplyBounceType.MsgTooLarge,
                bounce_reason = EmailReplyStatusCommon.BAD_OUTBOUND_SENDER,
                is_soft_bounced = false
              ))
            )
          )
        ),
        service_provider = EmailServiceProvider.OUTLOOK_API
      )

      assert(result.isEmpty)
    }

    it("should not give outbound error - V4") {
      val result = EmailServiceTemp.getBadOutBoundRepliesInLastXMinutes(
        receivedEmails = Vector(
          emailMessageTracked.copy(
            received_at = DateTime.now(),
            email_status = emailMessageTracked.email_status.copy(
              bouncedData = None
            )
          )
        ),
        service_provider = EmailServiceProvider.OUTLOOK_API
      )

      assert(result.isEmpty)
    }

  }


}
