package app.utils.email

import api.AppConfig
import api.accounts.email.models.EmailServiceProvider
import api.accounts.{AccountUuid, ReplyHandling, TeamId}
import api.accounts.models.{AccountId, OrgId}
import api.emails.daos.{EmailHealthCheckDAO, EmailHealthCheckRecord}
import api.emails.models.EmailAuthStatus.Pass
import api.emails.models.EmailHealthCheckStatus.{Completed, Queued}
import api.emails.models.{EmailHealthCheckRecordId, EmailReplyType, EmailSettingUuid}
import api.emails.services.{EmailHealthCheckService, EmailHealthDnsCheckService, HealthCheckEmailNotFoundException}
import api.emails.{EmailMessageTracked, EmailSetting, EmailSettingDAO}
import api.prospects.models.ProspectCategory
import api.team.TeamUuid
import io.smartreach.esp.api.emails.{EmailSettingId, IEmailAddress}
import org.apache.pekko.actor.ActorSystem
import org.apache.pekko.stream.Materializer
import org.joda.time.DateTime
import org.scalamock.scalatest.AsyncMockFactory
import org.scalatest.funspec.AsyncFunSpec
import org.scalatest.concurrent.ScalaFutures
import play.api.libs.json.Json
import play.api.libs.ws.WSClient
import play.api.libs.ws.ahc.AhcWSClient
import utils.{PusherService, SRLogger}
import utils.cache_utils.model.CampaignUseStatusForEmailSetting
import utils.cronjobs.email_setting_deletion.model.EmailSettingStatus
import utils.email.services.{EmailReplyTrackingService, InternalTrackingNote}
import utils.email.{EmailReplyStatus, EmailSenderService, EmailService}
import utils.testapp.TestAppExecutionContext

import scala.concurrent.{ExecutionContext, Future}
import scala.util.Success


class EmailHealthCheckServiceSpec extends AsyncFunSpec with ScalaFutures with AsyncMockFactory {

    implicit lazy val system: ActorSystem = TestAppExecutionContext.actorSystem
    implicit lazy val materializer: Materializer = TestAppExecutionContext.actorMaterializer
    implicit lazy val wsClient: AhcWSClient = TestAppExecutionContext.wsClient
    given Logger: SRLogger = new SRLogger("some_log_re_id")


    val emailHealthCheckRecord: EmailHealthCheckRecord = EmailHealthCheckRecord(
        id = EmailHealthCheckRecordId(1),
        team_id = TeamId(1),
        email_setting_id = EmailSettingId(1),
        spf_auth_status = Pass,
        dkim_auth_status = Pass,
        dmarc_auth_status = Pass,
        dkim_selector_opt = Some("selector_1"),
        spf_record_opt = None,
        dkim_record_opt = None,
        dmarc_record_opt = None,
        status = Queued,
        retry_count = 0,
        updated_at = DateTime.now(),
        created_at = DateTime.now()
    )

    val emailSenderService: EmailSenderService = mock[EmailSenderService]
    val emailSettingDAO: EmailSettingDAO = mock[EmailSettingDAO]
    val emailHealthCheckDAO: EmailHealthCheckDAO = mock[EmailHealthCheckDAO]
    val emailService: EmailService = mock[EmailService]
    val emailReplyTrackingService: EmailReplyTrackingService = mock[EmailReplyTrackingService]
    val pusherService: PusherService = mock[PusherService]
    val emailHealthDnsCheckService: EmailHealthDnsCheckService = mock[EmailHealthDnsCheckService]


    val emailHealthCheckService = new EmailHealthCheckService(
        emailHealthDnsCheckService = emailHealthDnsCheckService,
        emailSenderService = emailSenderService,
        emailSettingDAO = emailSettingDAO,
        emailService = emailService,
        emailHealthCheckDAO = emailHealthCheckDAO,
        pusherService = pusherService,
    )

    describe("EmailHealthCheckService#emailSettingHealthCheck") {
        it("should perform a successful health check and update the DAO") {

            val emailSettingForHealthCheck: EmailSetting = EmailSetting(
                id = Some(EmailSettingId(emailSettingId = 123)),
                org_id = OrgId(id = 1),
                owner_id = AccountId(id = 1),
                team_id = TeamId(id = 1),
                uuid = Some(EmailSettingUuid("test_uuid")),
                owner_uuid = AccountUuid("owner_uuid"),
                team_uuid = TeamUuid("team_uuid"),
                message_id_suffix = "Hello",
                email = "<EMAIL>",
                email_address_host = "smartreach.io",
                service_provider = EmailServiceProvider.GMAIL_API,
                domain_provider = None,
                via_gmail_smtp = None,
                owner_name = "Ritesh",
                sender_name = "Ritesh Rane",
                first_name = "Ritesh",
                last_name = "Rane",
                cc_emails = None,
                bcc_emails = None,
                smtp_username = None,
                smtp_password = None,
                smtp_host = None,
                smtp_port = None,
                imap_username = None,
                imap_password = None,
                imap_host = None,
                imap_port = None,
                oauth2_access_token = None,
                oauth2_refresh_token = None,
                oauth2_token_type = None,
                oauth2_token_expires_in = None,
                oauth2_access_token_expires_at = None,
                email_domain = None,
                api_key = None,
                mailgun_region = None,
                quota_per_day = 400,
                reply_handling = ReplyHandling.PAUSE_SPECIFIC_CAMPAIGN_ON_REPLY,
                last_read_for_replies = None,
                latest_email_scheduled_at = None,
                error = None,
                error_reported_at = None,
                paused_till = None,
                signature = "Ritesh Rane",
                created_at = None,
                current_prospect_sent_count_email = 10,
                default_tracking_domain = "default_tracking_domain",
                default_unsubscribe_domain = "default_unsubscribe_domain",
                rep_tracking_host_id = 1,
                tracking_domain_host = None,
                custom_tracking_domain = None,
                custom_tracking_cname_value = None,
                custom_tracking_domain_is_verified = None,
                custom_tracking_domain_is_ssl_enabled = None,
                rep_mail_server_id = 1,
                rep_mail_server_public_ip = "rep_mail_server_public_ip",
                rep_mail_server_host = "rep_mail_server_host",
                rep_mail_server_reverse_dns = None,
                min_delay_seconds = 0,
                max_delay_seconds = 0,
                tag = None,
                campaign_use_status_for_email_setting = CampaignUseStatusForEmailSetting.IsNotAssignedToAnyCampaign,
                show_rms_ip_in_frontend = false
            )

            val receiverEmailSetting = emailSettingForHealthCheck.copy(email = "<EMAIL>")
            val emailReplyStatusAutoReply = EmailReplyStatus(
                replyType = EmailReplyType.NOT_CATEGORIZED,
                isReplied = false,
                isUnsubscribeRequest = false,
                isAutoReply = true,
                isOutOfOfficeReply = false,
                isInvalidEmail = false,
                isForwarded = false,
                bouncedData = None
            )
            val emailMessageTracked = EmailMessageTracked(
                inbox_email_setting_id = 1,
                from = IEmailAddress(email = "<EMAIL>"),
                to_emails = Seq(IEmailAddress(email = "<EMAIL>"), IEmailAddress(email = "<EMAIL>")),
                subject = AppConfig.EmailHealthCheck.emailHealthCheckSubject,
                body = "SomeBody",
                base_body = "SomeBaseBody",
                text_body = "SomeTextBody",
                references_header = None,
                campaign_id = Some(123),
                step_id = Some(456),
                prospect_id_in_campaign = Some(890),
                prospect_account_id_in_campaign = Some(678),
                campaign_name = None,
                step_name = None,
                received_at = DateTime.now().minusDays(2),
                recorded_at = DateTime.now().minusDays(2),
                sr_inbox_read = true,
                original_inbox_folder = None,
                email_status = emailReplyStatusAutoReply,
                message_id = "some_message_id",
                full_headers = Json.obj(
                    "Authentication-Results" ->
                      """
                        |mx.google.com;
                        |       dkim=pass header.i=@mg.smartreach.io header.s=krs header.b=UuZETXPh;
                        |       spf=pass (google.com: domain of bounce+c1b8de.d12450-prateek=<EMAIL> designates ************ as permitted sender) smtp.mailfrom="bounce+c1b8de.d12450-prateek=<EMAIL>";
                        |       dmarc=pass (p=REJECT sp=REJECT dis=NONE) header.from=smartreach.io
                        |""".stripMargin,
                    "DKIM-Signature" -> "a=rsa-sha256; v=1; c=relaxed/relaxed; d=mg.smartreach.io; q=dns/txt; s=krs; t=1738518224; x=1738525424; h=Message-Id: To: To: Cc: From: From: Subject: Subject: Content-Type: Mime-Version: Date: Sender: Sender; bh=SkPznLqwPqNqbwypFNs2YgQzFIab+/ItTij7UNpNCb0=; b=UuZETXPhLH5glBjqVYFJuD4ZnllWGcg3G/vX24PANd7/6IGUpWlmAa5JIQ53ge6pRoB1iIsUf3xzoYAadm5u0lweKCjW1NVIIf38xkkznDjQhBrLS8nd6RabIFOgSD2jRh44c02hzYaTu9sROwuwNZb8sHnnlsB5hMTRrSuxBRg="
                ),
                scheduled_manually = false,
                reply_to = None,
                email_thread_id = None,
                gmail_msg_id = None,
                gmail_thread_id = None,
                outlook_msg_id = None,
                outlook_conversation_id = None,
                outlook_response_json = None,
                cc_emails = Seq(),
                in_reply_to_header = None,
                team_id = 1,
                account_id = 1,
                internal_tracking_note = InternalTrackingNote.EXISTING_INREPLYTO,
                tempThreadId = None
            )


            (emailSettingDAO.find(_:Long,_:EmailSettingStatus))
              .expects(523, EmailSettingStatus.Active)
              .returns(Some(receiverEmailSetting))
            (emailService.getEmailReplyTrackingService )
              .expects(*)
              .returns(emailReplyTrackingService)
            (emailReplyTrackingService.receiveEmail(_: EmailSetting, _: Seq[EmailSetting], _: SRLogger, _: DateTime, _: Option[DateTime])(_: WSClient, _: ExecutionContext))
              .expects(*, *, *, *, *, *, *)
              .returns(Future.successful(Vector(emailMessageTracked)))

            (emailHealthDnsCheckService.getSpfRecord(_: String)(_: ExecutionContext))
              .expects("smartreach.io", *)
              .returns(Future.successful(Some("v=spf1 include:_spf.google.com -all")))

            (emailHealthDnsCheckService.getDkimRecord(_: String, _: String)(_: ExecutionContext))
              .expects("smartreach.io", "krs", *)
              .returns(Future.successful(Some("abcd")))

            (emailHealthDnsCheckService.getDmarcRecord(_: String)(_: ExecutionContext))
              .expects("smartreach.io", *)
              .returns(Future.successful(Some("abcd")))

            (emailHealthCheckDAO.updateEmailHealthCheckResultOnSuccess )
              .expects(*, *, *, *, *)
              .returns(Success(EmailHealthCheckRecordId(1)))

            (emailHealthCheckDAO.getEmailHealthRecord )
              .expects(*,*)
              .returns(Success(Some(emailHealthCheckRecord)))

            (pusherService.sendMessageUsingPusher )
              .expects(*,*,*,*)
              .returns(Success("message"))


            val result = emailHealthCheckService.emailSettingHealthCheckTrack(emailSettingForHealthCheck,emailHealthCheckRecord)


            result.map { res =>
               assert(res == EmailHealthCheckRecordId(1))
            }
        }

        it("should throw an exception if the receiverEmailSetting is not found") {

            val emailSettingForHealthCheck: EmailSetting = EmailSetting(
                id = Some(EmailSettingId(emailSettingId = 123)),
                org_id = OrgId(id = 1),
                owner_id = AccountId(id = 1),
                team_id = TeamId(id = 1),
                uuid = Some(EmailSettingUuid("test_uuid")),
                owner_uuid = AccountUuid("owner_uuid"),
                team_uuid = TeamUuid("team_uuid"),
                message_id_suffix = "Hello",
                email = "<EMAIL>",
                email_address_host = "<EMAIL>",
                service_provider = EmailServiceProvider.GMAIL_API,
                domain_provider = None,
                via_gmail_smtp = None,
                owner_name = "Ritesh",
                sender_name = "Ritesh Rane",
                first_name = "Ritesh",
                last_name = "Rane",
                cc_emails = None,
                bcc_emails = None,
                smtp_username = None,
                smtp_password = None,
                smtp_host = None,
                smtp_port = None,
                imap_username = None,
                imap_password = None,
                imap_host = None,
                imap_port = None,
                oauth2_access_token = None,
                oauth2_refresh_token = None,
                oauth2_token_type = None,
                oauth2_token_expires_in = None,
                oauth2_access_token_expires_at = None,
                email_domain = None,
                api_key = None,
                mailgun_region = None,
                quota_per_day = 400,
                reply_handling = ReplyHandling.PAUSE_SPECIFIC_CAMPAIGN_ON_REPLY,
                last_read_for_replies = None,
                latest_email_scheduled_at = None,
                error = None,
                error_reported_at = None,
                paused_till = None,
                signature = "Ritesh Rane",
                created_at = None,
                current_prospect_sent_count_email = 10,
                default_tracking_domain = "default_tracking_domain",
                default_unsubscribe_domain = "default_unsubscribe_domain",
                rep_tracking_host_id = 1,
                tracking_domain_host = None,
                custom_tracking_domain = None,
                custom_tracking_cname_value = None,
                custom_tracking_domain_is_verified = None,
                custom_tracking_domain_is_ssl_enabled = None,
                rep_mail_server_id = 1,
                rep_mail_server_public_ip = "rep_mail_server_public_ip",
                rep_mail_server_host = "rep_mail_server_host",
                rep_mail_server_reverse_dns = None,
                min_delay_seconds = 0,
                max_delay_seconds = 0,
                tag = None,
                campaign_use_status_for_email_setting = CampaignUseStatusForEmailSetting.IsNotAssignedToAnyCampaign,
                show_rms_ip_in_frontend = false
            )


            (emailSettingDAO.find(_:Long,_:EmailSettingStatus))
              .expects(523, EmailSettingStatus.Active)
              .returns(None)


            val result = recoverToExceptionIf[Exception](emailHealthCheckService.emailSettingHealthCheckTrack(emailSettingForHealthCheck,emailHealthCheckRecord))


            result.map { ex =>
                assert(ex.getMessage == "Not found email health check receiver email setting")
            }
        }

        it("should retry email fetching if no message is found") {
            // Arrange

            val emailSettingForHealthCheck: EmailSetting = EmailSetting(
                id = Some(EmailSettingId(emailSettingId = 123)),
                org_id = OrgId(id = 1),
                owner_id = AccountId(id = 1),
                team_id = TeamId(id = 1),
                uuid = Some(EmailSettingUuid("test_uuid")),
                owner_uuid = AccountUuid("owner_uuid"),
                team_uuid = TeamUuid("team_uuid"),
                message_id_suffix = "Hello",
                email = "<EMAIL>",
                email_address_host = "<EMAIL>",
                service_provider = EmailServiceProvider.GMAIL_API,
                domain_provider = None,
                via_gmail_smtp = None,
                owner_name = "Ritesh",
                sender_name = "Ritesh Rane",
                first_name = "Ritesh",
                last_name = "Rane",
                cc_emails = None,
                bcc_emails = None,
                smtp_username = None,
                smtp_password = None,
                smtp_host = None,
                smtp_port = None,
                imap_username = None,
                imap_password = None,
                imap_host = None,
                imap_port = None,
                oauth2_access_token = None,
                oauth2_refresh_token = None,
                oauth2_token_type = None,
                oauth2_token_expires_in = None,
                oauth2_access_token_expires_at = None,
                email_domain = None,
                api_key = None,
                mailgun_region = None,
                quota_per_day = 400,
                reply_handling = ReplyHandling.PAUSE_SPECIFIC_CAMPAIGN_ON_REPLY,
                last_read_for_replies = None,
                latest_email_scheduled_at = None,
                error = None,
                error_reported_at = None,
                paused_till = None,
                signature = "Ritesh Rane",
                created_at = None,
                current_prospect_sent_count_email = 10,
                default_tracking_domain = "default_tracking_domain",
                default_unsubscribe_domain = "default_unsubscribe_domain",
                rep_tracking_host_id = 1,
                tracking_domain_host = None,
                custom_tracking_domain = None,
                custom_tracking_cname_value = None,
                custom_tracking_domain_is_verified = None,
                custom_tracking_domain_is_ssl_enabled = None,
                rep_mail_server_id = 1,
                rep_mail_server_public_ip = "rep_mail_server_public_ip",
                rep_mail_server_host = "rep_mail_server_host",
                rep_mail_server_reverse_dns = None,
                min_delay_seconds = 0,
                max_delay_seconds = 0,
                tag = None,
                campaign_use_status_for_email_setting = CampaignUseStatusForEmailSetting.IsNotAssignedToAnyCampaign,
                show_rms_ip_in_frontend = false
            )

            val receiverEmailSetting = emailSettingForHealthCheck.copy(email = "<EMAIL>")

            // Mock interactions
            (emailSettingDAO.find(_:Long,_:EmailSettingStatus))
              .expects(523, EmailSettingStatus.Active)
              .returns(Some(receiverEmailSetting))
            (emailService.getEmailReplyTrackingService )
              .expects(*)
              .returns(emailReplyTrackingService)
            (emailReplyTrackingService.receiveEmail(_: EmailSetting, _: Seq[EmailSetting], _: SRLogger, _: DateTime, _: Option[DateTime])(_: WSClient, _: ExecutionContext))
              .expects(*, *, *, *, *, *, *)
              .returns(Future.successful(Vector.empty[EmailMessageTracked]))

            (emailHealthCheckDAO.updateEmailHealthCheckResultOnFailure )
              .expects(*, *, *)
              .returns(Success(EmailHealthCheckRecordId(1)))

            (emailHealthCheckDAO.getEmailHealthRecord)
              .expects(*,*)
              .returns(Success(Some(emailHealthCheckRecord)))

            (pusherService.sendMessageUsingPusher )
              .expects(*,*,*,*)
              .returns(Success("message"))

            // Act
            val result = emailHealthCheckService.emailSettingHealthCheckTrack(emailSettingForHealthCheck,emailHealthCheckRecord)

            // Assert
            result.map { updated =>
               assert(updated == EmailHealthCheckRecordId(1))
            }
        }
    }
}