package app.utils.email

import api.accounts.dao.OrganizationDAO
import api.accounts.{Account, AccountAccess, AccountMetadata, AccountService, AccountType, AccountUuid, OrgCountData, OrgMetadata, OrgPlan, OrgSettings, OrganizationRole, OrganizationWithCurrentData, PermType, PermissionLevelForValidation, PermissionOwnershipV2, ProspectCategoriesInDB, ReplyHandling, RolePermV2, RolePermissionDataDAOV2, RolePermissionDataV2, RolePermissionsInDBV2, RolePermissionsV2, TeamAccount, TeamAccountRole, TeamId, TeamMember, TeamMemberLite}
import api.accounts.models.{AccountId, AccountProfileInfo, OrgId}
import api.blacklist.models.BlacklistTypeData.TeamLevelBlacklistData
import api.blacklist.models.{BlacklistCreateOrDeleteApiLevel, BlacklistEmailAndUuid, BlacklistFindApiLevel, BlacklistUuid, DoNotContactType}
import api.blacklist.{Blacklist, BlacklistCreateDomainsForm, BlacklistCreateEmailsForm, BlacklistCreateForm, BlacklistCreatePhoneForm, BlacklistCreateUpdateForm, BlacklistCreateUpdateFormWithUuid, BlacklistDAO, BlacklistService, BlacklistTeamLevel, DeleteBlacklistError, DomainsWithExcludedEmails, DomainsWithExcludedEmailsWithUuid}
import api.calendar_app.models.CalendarAccountData
import api.prospects.dao.ProspectDAO
import api.prospects.dao_service.ProspectDAOService
import api.prospects.models.{ProspectCategory, ProspectCategoryId, ProspectCategoryRank, ProspectCategoryUpdateFlow, ProspectId}
import api.prospects.service.ProspectServiceV2
import api.prospects.{ProspectBlacklistMatches, ProspectCategoryService, ProspectUpdateCategoryTemp, ProspectUpdateCategoryTemp2}
import api.team.TeamUuid
import app.api.blacklist.fixtures.BlacklistFixturesForIntegrationTest
import app.test_fixtures.accounts.OrgCountDataFixture
import app.test_fixtures.organizationa.{OrgMetadataFixture, OrgPlanFixture}
import db_test_spec.api.InitialData
import db_test_spec.api.accounts.fixtures.NewAccountAndEmailSettingData
import eventframework.SrResourceTypes
import org.joda.time.DateTime
import org.scalamock.scalatest.AsyncMockFactory
import org.scalatest.funspec.AsyncFunSpec
import utils.mq.blacklist.MqUpdateProspectCategoryForGivenTeamOfOrgForGlobalDNC
import utils.uuid.SrUuidUtils
import utils.uuid.services.SrUuidService
import utils.{Helpers, ISRLogger, SRLogger}
import utils_deploy.rolling_updates.models.SrRollingUpdateFeature
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService

import scala.util.{Failure, Success, Try}

class BlacklistServiceSpec extends AsyncFunSpec with AsyncMockFactory{

  val blacklistDAO = mock[BlacklistDAO]
  val prospectDAOService = mock[ProspectDAOService]
  val prospectUpdateCategoryTemp = mock[ProspectUpdateCategoryTemp]
  val prospectServiceV2 = mock[ProspectServiceV2]
  val rolePermissionDataDAOV2 = mock[RolePermissionDataDAOV2]
  val srUuidUtils = mock[SrUuidUtils]
  val srUuidService: SrUuidService = mock[SrUuidService]
  val prospectCategoryService: ProspectCategoryService = mock[ProspectCategoryService]
  val organizationDAO = mock[OrganizationDAO]
  val accountService =  mock[AccountService]
  val mqUpdateProspectCategoryForGivenTeamOfOrgForGlobalDNC = mock[MqUpdateProspectCategoryForGivenTeamOfOrgForGlobalDNC]
  val srRollingUpdateCoreService: SrRollingUpdateCoreService = mock[SrRollingUpdateCoreService]

  val blacklistService = new BlacklistService(
    blacklistDAO = blacklistDAO,
    prospectUpdateCategoryTemp = prospectUpdateCategoryTemp,
    prospectDAOService = prospectDAOService,
    prospectServiceV2 = prospectServiceV2,
    srUuidUtils = srUuidUtils,
    srUuidService = srUuidService,
    organizationDAO = organizationDAO,
    accountService=accountService,
    srRollingUpdateCoreService = srRollingUpdateCoreService,
    mqUpdateProspectCategoryForGivenTeamOfOrgForGlobalDNC = mqUpdateProspectCategoryForGivenTeamOfOrgForGlobalDNC
//    prospectCategoryService = prospectCategoryService
  )

  given Logger: SRLogger = new SRLogger("tests")


  val campaign_id: Long = 121L
  val campaign_name = "CampaignName"
  val permittedAccountIds = Seq(2L)
  val teamId: Long = 37L
  val ownerId: Long = 2L

  val first_name = "Adminfirst"
  val last_name = "Adminlast"
  val company = "CompanyName"
  val email = "<EMAIL>"

  val aDate = DateTime.parse("2022-4-09")

  val donotContactCatId = 269L
  val notCategorizedCatId = 268L

  val profile = AccountProfileInfo(
    first_name = first_name,
    last_name = last_name,
    company = Some(company),
    timezone = None,
    country_code = None,
    mobile_country_code = None,
    mobile_number = None,
      onboarding_phone_number= None,
    twofa_enabled = false,
    has_gauthenticator = false,
    weekly_report_emails = None,
    scheduled_for_deletion_at = None
  )

  val accountMetadata = AccountMetadata(
    // account_ui_version = None,
    is_profile_onboarding_done = None
  )

  val orgMetadata = OrgMetadataFixture.orgMetadataFixture2

  val orgCountData: OrgCountData = OrgCountDataFixture.orgCountData_default

  val orgSettings = OrgSettings(
    enable_ab_testing = false,
    disable_force_send = false,
    bulk_sender = false,
    allow_2fa = false,
    show_2fa_setting = false,
    enforce_2fa = false,
    allow_native_crm_integration = false,
      agency_option_allow_changing = false,
      agency_option_show = false
  )

  val orgPlan = OrgPlanFixture.orgPlanFixture


  val org = OrganizationWithCurrentData(

    id = 1,
    name = company,
    owner_account_id = 49,

    counts = orgCountData,
    settings = orgSettings,
    plan = orgPlan,

    is_agency = true,
    trial_ends_at = DateTime.now().plusDays(100),
    error = None,
    error_code = None,
    paused_till = None,
    errors = Seq(),
    warnings = Seq(),
    via_referral = false,
    org_metadata = orgMetadata
  )

  val teamMember: TeamMember = TeamMember(
    team_id = teamId,
    team_name = "team_name",
    user_id = 2L,
    ta_id = 49L, // dont send ta_id to frontend / api response, only for internal purpose, its dynamically assigned in AuthUtils
    first_name = Some(first_name),
    last_name = Some(last_name),
    email = "<EMAIL>",
    team_role = TeamAccountRole.ADMIN,
    api_key = Some("apiKey"),
    zapier_key = Some("zapier_key")
  )

  val teamMemberLite = TeamMemberLite(

    user_id =  2L,
    first_name = Some("first_name"),
    last_name = Some("last_name"),
    email = "<EMAIL>",
    active = true,
    timezone = Some("campaignTimezone"),
    twofa_enabled = true,
    created_at = aDate,
    user_uuid = AccountUuid("uuid"),
    team_role = TeamAccountRole.ADMIN

  )

  val prospect_CategoriesInDB = ProspectCategoriesInDB(
    id = 22L,
    name = "Completed",
    text_id = "Done",
    label_color = "Blue",
    is_custom = true,
    team_id = teamId,
    rank = ProspectCategoryRank(rank = 2000)
  )

  val adminDefaultPermissions = RolePermissionDataDAOV2.defaultRoles(
    role = TeamAccountRole.ADMIN,
    simpler_perm_flag = false
  )

  val rolePermissionData = RolePermissionDataV2.toRolePermissionApi(
    data = adminDefaultPermissions.copy(id = 10)
  )

  val team_account: TeamAccount = TeamAccount(

    team_id = teamId,
    org_id = 20L,

    role_from_db = Some(adminDefaultPermissions), // MUST come from db (option type only for cacheservice error), should not be sent to frontend, only intermediate

    role = Some(rolePermissionData), // should be sent to frontend

    active = true,
    is_actively_used = true,
    team_name = "team_name",
    total_members = 5,
    access_members = Seq(teamMember),
    all_members = Seq(teamMemberLite),

    prospect_categories_custom = Seq(prospect_CategoriesInDB),
    max_emails_per_prospect_per_day = 100L,
    max_emails_per_prospect_per_week = 500L,
    max_emails_per_prospect_account_per_day = 97,
    max_emails_per_prospect_account_per_week = 497,

    reply_handling = ReplyHandling.PAUSE_SPECIFIC_CAMPAIGN_ON_REPLY,
    created_at = aDate,
    selected_calendar_data = None,
    team_uuid = TeamUuid("uuid")
  )

  val accountAdmin = Account(
    id = AccountUuid("account_uuid"),
    internal_id = 2,
    email = email,
    email_verification_code = None,
    email_verification_code_created_at = None,
    created_at = DateTime.now().minusDays(1000),
    first_name = Some(first_name),
    last_name = Some(last_name),
    company = Some(company),
    timezone = None,
    profile = profile,
    org_role = Some(OrganizationRole.OWNER),
    teams = Seq(team_account),
    account_type = AccountType.AGENCY,
    org = org,
    active = true,
    email_notification_summary = "dSFA",
    account_metadata = accountMetadata,
    email_verified = true,
    signupType = None,
    account_access = AccountAccess(
      inbox_access = false
    ),
    calendar_account_data = None

  )

  val accountId: Long = teamMember.user_id
  val accountName = Helpers.getAccountName(teamMember)
  val auditRequestLogId = "auditReqLogId"
  val blacklist_ids = Seq(1L, 2L)
  val blacklist: BlacklistTeamLevel = BlacklistTeamLevel(id = 2L, team_id = teamId, org_id = accountAdmin.org.id, name = "<EMAIL>", excluded_emails = Seq(), created_at = DateTime.now(), uuid = "some_uuid", do_not_contact_type = DoNotContactType.EMAIL)

  describe("blacklistService.deleteBlacklist"){

    it("should fail when blacklistDAO._deleteBlacklistRows returns failure"){

      val level = BlacklistCreateOrDeleteApiLevel.Team(org_id = OrgId(accountAdmin.org.id), team_id = TeamId(teamId), ta_id = teamMember.ta_id)

      (blacklistDAO.findAll)
        .expects(*, BlacklistCreateOrDeleteApiLevel.getBlacklistFindApiLevel(level), None, None, 100)
        .returning(Success(Seq(blacklist)))

//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//        _: TeamId,
//        _: SrRollingUpdateFeature
//      )(_: ISRLogger))
//        .expects(TeamId(teamId), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .returning(false)

      (prospectServiceV2.findProspectIdsMatchingBlacklistedEmailsAndDomains(_: Long, _: BlacklistCreateForm)(using _: SRLogger))
        .expects(teamId, BlacklistCreateEmailsForm(Seq("<EMAIL>")), *)
        .returning(Success(Seq(ProspectBlacklistMatches(
          id = ProspectId(101L),
          email = "<EMAIL>",
          email_domain = "email.com",
          phone = None,
          prospect_category_id_custom = 23L
        ))))

      (prospectServiceV2.findProspectIdsMatchingBlacklistedEmailsAndDomains(_: Long, _: BlacklistCreateForm)(using _: SRLogger))
        .expects(teamId, BlacklistCreateDomainsForm(Seq()), *)
        .returning(Success(Seq()))
      (prospectServiceV2.findProspectIdsMatchingBlacklistedEmailsAndDomains(_: Long, _: BlacklistCreateForm)(using _: SRLogger))
        .expects(teamId, BlacklistCreatePhoneForm(Seq()), *)
        .returning(Success(Seq()))

      (blacklistDAO._deleteBlacklistRows)
        .expects(blacklist_ids, teamId)
        .returning(Failure(new Exception("DB failure")))

      val res = blacklistService.deleteBlacklist(
        accountId = accountId,
        blacklistIds = blacklist_ids,
        accountName = accountName,
        account = Some(accountAdmin),
        auditRequestLogId = Some(auditRequestLogId),
        Logger = Logger,
        teamId = Some(TeamId(teamId)),
        level = level
      )

      res match {
        case Left(error: DeleteBlacklistError.DbFailure) =>
          Logger.info(s"DeleteBlacklistError!... ${error.e.getMessage}")
          assert(true)
        case Right(response) =>
          Logger.info(s"DeleteBlacklistResponse... $response")
          assert(false)
      }
    }

    it("should success when blacklistDAO.delete returns Seq of Blacklist"){

      val level = BlacklistCreateOrDeleteApiLevel.Team(org_id = OrgId(accountAdmin.org.id), team_id = TeamId(teamId), ta_id = teamMember.ta_id)

      (blacklistDAO.findAll)
        .expects(*, BlacklistCreateOrDeleteApiLevel.getBlacklistFindApiLevel(level), None, None, 100)
        .returning(Success(Seq(blacklist)))

//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//        _: TeamId,
//        _: SrRollingUpdateFeature
//      )(_: ISRLogger))
//        .expects(TeamId(teamId), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .returning(false)

      (prospectServiceV2.findProspectIdsMatchingBlacklistedEmailsAndDomains(_: Long, _: BlacklistCreateForm)(using _: SRLogger))
        .expects(teamId, BlacklistCreateEmailsForm(Seq("<EMAIL>")), *)
        .returning(Success(Seq(ProspectBlacklistMatches(
          id = ProspectId(101L),
          email = prospect_email,
          email_domain = "gmail.com",
          phone = None,
          prospect_category_id_custom = 23L
        ))))

      (prospectServiceV2.findProspectIdsMatchingBlacklistedEmailsAndDomains(_: Long, _: BlacklistCreateForm)(using _: SRLogger))
        .expects(teamId, BlacklistCreateDomainsForm(Seq()), *)
        .returning(Success(Seq()))
      (prospectServiceV2.findProspectIdsMatchingBlacklistedEmailsAndDomains(_: Long, _: BlacklistCreateForm)(using _: SRLogger))
        .expects(teamId, BlacklistCreatePhoneForm(Seq()), *)
        .returning(Success(Seq()))

      (blacklistDAO._deleteBlacklistRows)
        .expects(blacklist_ids, teamId)
        .returning(Success(Seq(blacklist)))

      (prospectServiceV2.getProspectCategoryId(_: TeamId, _: ProspectCategory.Value, _: Option[Account])(using _:SRLogger))
        .expects(TeamId(teamId), ProspectCategory.DO_NOT_CONTACT, Some(accountAdmin), *)
        .returning(Success(ProspectCategoryId(1)))

      (prospectServiceV2.getProspectCategoryId(_: TeamId, _: ProspectCategory.Value, _: Option[Account])(using _:SRLogger))
        .expects(TeamId(teamId), ProspectCategory.NOT_CATEGORIZED, Some(accountAdmin), *)
        .returning(Success(ProspectCategoryId(2)))

      (prospectUpdateCategoryTemp.updateCategoryAndCreateEvent)
        .expects(Seq(101L), 2, teamId, accountName,  ProspectCategoryUpdateFlow.AdminUpdate(Some(ProspectCategoryId(1)), ProspectCategoryId(2)), Some(accountAdmin), *, *, Some(auditRequestLogId))
        .returning(Success(1))

      val res = blacklistService.deleteBlacklist(
        accountId = accountId,
        blacklistIds = blacklist_ids,
        accountName = accountName,
        account = Some(accountAdmin),
        auditRequestLogId = Some(auditRequestLogId),
        Logger = Logger,
        teamId = Some(TeamId(teamId)),
        level = level
      )

      res match {
        case Left(error: DeleteBlacklistError.DbFailure) =>
          Logger.info(s"DeleteBlacklistError!... ${error.e.getMessage}")
          assert(false)
        case Right(response) =>
          Logger.info(s"DeleteBlacklistResponse... $response")
          //DeleteBlacklistResponse... Do not contact list updated
          assert(true)
      }
    }
  }

  val email1 = "<EMAIL>"
  val email2 = "<EMAIL>"
  val prospect_email = "<EMAIL>"
  val blacklistUuid1 = BlacklistUuid(uuid = "blacklist_some_id1")
  val blacklistUuid2 = BlacklistUuid(uuid = "blacklist_some_id2")

  val blacklistCreateUpdateV3Form1 = BlacklistCreateUpdateFormWithUuid(
    emails = Seq(
      BlacklistEmailAndUuid(email = email1, blacklistUuid = blacklistUuid1),
      BlacklistEmailAndUuid(email = email2, blacklistUuid = blacklistUuid2)
    ),
    domains_with_excluded_emails = Seq(),
    phones = Seq()
  )

  val blacklistCreateUpdateV3Form2 = BlacklistCreateUpdateFormWithUuid(
    emails = Seq(),
    domains_with_excluded_emails = Seq(DomainsWithExcludedEmailsWithUuid(domain = "gmail.com", blacklistUuid = blacklistUuid1, excluded_emails = Seq(prospect_email))),
    phones = Seq()
  )

  val domainsWithExcludedEmails = DomainsWithExcludedEmails(
    domain = "gmail.com",
    excluded_emails = Seq(prospect_email)
  )

  describe("blacklistService.createOrUpdateBlacklist"){

    val prospectCategoryUpdateFlow = ProspectCategoryUpdateFlow.AdminUpdate(
      old_prospect_category_id = None,
      new_prospect_category_id = ProspectCategoryId(id = 22L)
    )

    it("should success when emails are empty for emails dnc type"){

      //Note: we are validating in frontend that user is not sending empty values

      val blacklistCreateUpdateForm1 = BlacklistCreateEmailsForm(
        emails =  Seq()
      )

//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//        _: TeamId,
//        _: SrRollingUpdateFeature
//      )(_: ISRLogger))
//        .expects(TeamId(teamId), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .returning(false)

      val res = blacklistService.createOrUpdateBlacklist(
        accountId = accountId,
        addedByName = "Unsubscribing", // Could be SmartReach.io api Unsubscribed, or user adding it
        teamId = teamId,
        taId = teamMember.ta_id,
        data = blacklistCreateUpdateForm1,
        is_req_via_dnc_form = false,
        opted_out_from_campaign_id = Some(campaign_id),
        opted_out_from_campaign_name = Some(campaign_name),
        account = Some(accountAdmin),
        auditRequestLogId = Some(auditRequestLogId),
        level= None,
        Logger= Logger
      )

      res match{
        case Right(value) =>
          assert(value.isEmpty)

        case Left(exception) =>
          assert(false)
      }
    }

    it("should success when domains are empty for domains dnc type"){

      //Note: we are validating in frontend that user is not sending empty values

      val blacklistCreateUpdateForm1 = BlacklistCreateDomainsForm(
        domains =  Seq()
      )

//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//        _: TeamId,
//        _: SrRollingUpdateFeature
//      )(_: ISRLogger))
//        .expects(TeamId(teamId), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .returning(false)

      val res = blacklistService.createOrUpdateBlacklist(
        accountId = accountId,
        addedByName = "Unsubscribing", // Could be SmartReach.io api Unsubscribed, or user adding it
        teamId = teamId,
        taId = teamMember.ta_id,
        data = blacklistCreateUpdateForm1,
        is_req_via_dnc_form = false,
        opted_out_from_campaign_id = Some(campaign_id),
        opted_out_from_campaign_name = Some(campaign_name),
        account = Some(accountAdmin),
        auditRequestLogId = Some(auditRequestLogId),
        level= None,
        Logger= Logger
      )

      res match{
        case Right(value) =>
          Logger.info(s"createOrUpdateBlacklistResponse... ${value.length}")
          //createOrUpdateBlacklistResponse... 0
          assert(true)

        case Left(exception) =>
          assert(false)
      }
    }

    it("should fail when is_req_via_dnc_form is false and domains_with_excluded_emails need to be updated/created"){

      val blacklistCreateUpdateForm1: BlacklistCreateDomainsForm = BlacklistCreateDomainsForm(
        domains = Seq(domainsWithExcludedEmails)
      )

//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//        _: TeamId,
//        _: SrRollingUpdateFeature
//      )(_: ISRLogger))
//        .expects(TeamId(teamId), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .returning(false)

      val res = blacklistService.createOrUpdateBlacklist(
        accountId = accountId,
        addedByName = "Unsubscribing", // Could be SmartReach.io api Unsubscribed, or user adding it
        teamId = teamId,
        taId = teamMember.ta_id,
        data = blacklistCreateUpdateForm1,
        is_req_via_dnc_form = false,
        opted_out_from_campaign_id = Some(campaign_id),
        opted_out_from_campaign_name = Some(campaign_name),
        account = Some(accountAdmin),
        auditRequestLogId = Some(auditRequestLogId),
        level= None,
        Logger= Logger
      )

      res match{
        case Right(value) =>
          Logger.info(s"createOrUpdateBlacklistResponse... ${value.length}")
          assert(false)
        case Left(exception) =>
          Logger.info(s"Exception... ${exception}")
          //Exception... Add domains as dnc only allowed via api
          assert(true)
      }
    }

    it("should fail when is_req_via_dnc_form is true, domains_with_excluded_emails empty and deleteEmailsFromExcludedlist returns failure"){

      val blacklistCreateUpdateForm1: BlacklistCreateEmailsForm = BlacklistCreateEmailsForm(
        emails = Seq(email1, email2)
      )

//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//        _: TeamId,
//        _: SrRollingUpdateFeature
//      )(_: ISRLogger))
//        .expects(TeamId(teamId), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .returning(false)

      (blacklistDAO.deleteEmailsFromExcludedlist)
        .expects(blacklistCreateUpdateForm1.emails, teamId, Logger)
        .returning(Failure(new Throwable("Error while deleteEmailsFromExcludedlist")))

      val res = blacklistService.createOrUpdateBlacklist(
        accountId = accountId,
        addedByName = "Unsubscribing", // Could be SmartReach.io api Unsubscribed, or user adding it
        teamId = teamId,
        taId = teamMember.ta_id,
        data = blacklistCreateUpdateForm1,
        is_req_via_dnc_form = true,
        opted_out_from_campaign_id = Some(campaign_id),
        opted_out_from_campaign_name = Some(campaign_name),
        account = Some(accountAdmin),
        auditRequestLogId = Some(auditRequestLogId),
        level= None,
        Logger= Logger
      )

      res match{
        case Right(value) =>
          Logger.info(s"createOrUpdateBlacklistResponse... ${value.length}")
          assert(false)
        case Left(exception) =>
          Logger.info(s"Exception... ${exception}")
          //Exception... There was an error.
          assert(true)
      }
    }

    it("should success when is_req_via_dnc_form is true, domains_with_excluded_emails empty and deleteEmailsFromExcludedlist returns success"){

      val blacklistCreateUpdateForm1: BlacklistCreateEmailsForm = BlacklistCreateEmailsForm(
        emails = Seq(email1, email2)
      )

//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//        _: TeamId,
//        _: SrRollingUpdateFeature
//      )(_: ISRLogger))
//        .expects(TeamId(teamId), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .returning(false)

      (blacklistDAO.deleteEmailsFromExcludedlist)
        .expects(blacklistCreateUpdateForm1.emails, teamId, Logger)
        .returning(Success(Seq()))

      val prospectBlacklistMatches = ProspectBlacklistMatches(
        id = ProspectId(101L),
        email = prospect_email,
        email_domain = "gmail.com",
        phone = None,
        prospect_category_id_custom = 23L
      )

      (prospectServiceV2.findProspectIdsMatchingBlacklistedEmailsAndDomains(_: Long, _: BlacklistCreateForm)(using _: SRLogger))
        .expects(teamId, blacklistCreateUpdateForm1, *)
        .returning(Success(Seq(prospectBlacklistMatches)))

      ((() => srUuidUtils.generateBlacklistUuid()))
        .expects()
        .returning(blacklistUuid1.uuid)

      ((() => srUuidUtils.generateBlacklistUuid()))
        .expects()
        .returning(blacklistUuid2.uuid)

      (blacklistDAO.insertBlacklistRowsForTeam)
        .expects(TeamLevelBlacklistData(AccountId(accountId), TeamId(teamId),  org_id = OrgId(accountAdmin.org.id), teamMember.ta_id, "Unsubscribing", Some(campaign_id), Some(campaign_name), blacklistCreateUpdateV3Form1))
        .returning(Success(Seq(2L)))

      (prospectDAOService.getProspectCategoryId(_: TeamId, _: ProspectCategory.Value, _: Option[Account])(using _:SRLogger))
        .expects(TeamId(id = teamId), ProspectCategory.DO_NOT_CONTACT, Some(accountAdmin), *)
        .returning(Success(ProspectCategoryId(22L)))

      (prospectUpdateCategoryTemp.updateCategoryAndCreateEvent)
        .expects(
          Seq(101L),
          accountAdmin.internal_id,
          teamId,
          "Unsubscribing",
          prospectCategoryUpdateFlow,
          Some(accountAdmin),
          *,
          *,
          Some(auditRequestLogId))
        .returning(Success(1))

      val res = blacklistService.createOrUpdateBlacklist(
        accountId = accountId,
        addedByName = "Unsubscribing", // Could be SmartReach.io api Unsubscribed, or user adding it
        teamId = teamId,
        taId = teamMember.ta_id,
        data = blacklistCreateUpdateForm1,
        is_req_via_dnc_form = true,
        opted_out_from_campaign_id = Some(campaign_id),
        opted_out_from_campaign_name = Some(campaign_name),
        account = Some(accountAdmin),
        auditRequestLogId = Some(auditRequestLogId),
        level= None,
        Logger= Logger
      )

      res match{
        case Right(value) =>
          Logger.info(s"createOrUpdateBlacklistResponse... $value")
          //createOrUpdateBlacklistResponse... List(2)
          assert(true)
        case Left(exception) =>
          Logger.info(s"Exception... ${exception}")
          assert(false)
      }
    }


    it("should fail when blacklistCreateUpdateForm.emails nonEmpty and checkEmailAlreadyExistsInExceptionList returns failure"){
      val blacklistCreateUpdateForm1: BlacklistCreateEmailsForm = BlacklistCreateEmailsForm(
        emails = Seq(email1, email2)
      )

      //checks whether the emails: email1 and email2 are in excluded_emails of any domain
      (blacklistDAO.checkEmailAlreadyExistsInExceptionList)
        .expects(teamId, Seq(email1, email2))
        .returning(Failure(new Throwable("Error while checking email already in exception list")))

//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//        _: TeamId,
//        _: SrRollingUpdateFeature
//      )(_: ISRLogger))
//        .expects(TeamId(teamId), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .returning(false)

      val res = blacklistService.createOrUpdateBlacklist(
        accountId = accountId,
        addedByName = "Unsubscribing", // Could be SmartReach.io api Unsubscribed, or user adding it
        teamId = teamId,
        taId = teamMember.ta_id,
        data = blacklistCreateUpdateForm1,
        is_req_via_dnc_form = false,
        opted_out_from_campaign_id = Some(campaign_id),
        opted_out_from_campaign_name = Some(campaign_name),
        account = Some(accountAdmin),
        auditRequestLogId = Some(auditRequestLogId),
        Logger= Logger,
        level= None,
      )

      res match{
        case Right(value) =>
          Logger.info(s"createOrUpdateBlacklistResponse... ${value.length}")
          assert(false)
        case Left(exception) =>
          Logger.info(s"Exception... ${exception}")
          //Exception... There was an error.
          assert(true)
      }
    }

    it("should success when blacklistCreateUpdateForm.emails nonEmpty and checkEmailAlreadyExistsInExceptionList returns empty"){
      val blacklistCreateUpdateForm1: BlacklistCreateEmailsForm = BlacklistCreateEmailsForm(
        emails = Seq(email1, email2)
      )

      (blacklistDAO.checkEmailAlreadyExistsInExceptionList)
        .expects(teamId, Seq(email1, email2))
        .returning(Success(Seq()))

//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//        _: TeamId,
//        _: SrRollingUpdateFeature
//      )(_: ISRLogger))
//        .expects(TeamId(teamId), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .returning(false)

      val prospectBlacklistMatches = ProspectBlacklistMatches(
        id = ProspectId(101L),
        email = prospect_email,
        email_domain = "gmail.com",
        phone = None,
        prospect_category_id_custom = 23L
      )

      (prospectServiceV2.findProspectIdsMatchingBlacklistedEmailsAndDomains(_: Long, _: BlacklistCreateForm)(using _: SRLogger))
        .expects(teamId, blacklistCreateUpdateForm1, *)
        .returning(Success(Seq(prospectBlacklistMatches)))

      ((() => srUuidUtils.generateBlacklistUuid()))
        .expects()
        .returning(blacklistUuid1.uuid)

      ((() => srUuidUtils.generateBlacklistUuid()))
        .expects()
        .returning(blacklistUuid2.uuid)

      (blacklistDAO.insertBlacklistRowsForTeam)
        .expects(TeamLevelBlacklistData(AccountId(accountId), TeamId(teamId),  org_id = OrgId(accountAdmin.org.id), teamMember.ta_id, "Unsubscribing", Some(campaign_id), Some(campaign_name), blacklistCreateUpdateV3Form1))
        .returning(Success(Seq(2L)))

      (prospectDAOService.getProspectCategoryId(_: TeamId, _: ProspectCategory.Value, _: Option[Account])(using _:SRLogger))
        .expects(TeamId(id = teamId), ProspectCategory.DO_NOT_CONTACT, Some(accountAdmin), *)
        .returning(Success(ProspectCategoryId(22L)))

      (prospectUpdateCategoryTemp.updateCategoryAndCreateEvent)
        .expects(
          Seq(101L),
          accountAdmin.internal_id,
          teamId,
          "Unsubscribing",
          prospectCategoryUpdateFlow,
          Some(accountAdmin),
          *,
          *,
          Some(auditRequestLogId))
        .returning(Success(1))

      val res = blacklistService.createOrUpdateBlacklist(
        accountId = accountId,
        addedByName = "Unsubscribing", // Could be SmartReach.io api Unsubscribed, or user adding it
        teamId = teamId,
        taId = teamMember.ta_id,
        data = blacklistCreateUpdateForm1,
        is_req_via_dnc_form = false,
        opted_out_from_campaign_id = Some(campaign_id),
        opted_out_from_campaign_name = Some(campaign_name),
        account = Some(accountAdmin),
        auditRequestLogId = Some(auditRequestLogId),
        Logger= Logger,
        level= None,
      )

      res match{
        case Right(value) =>
          Logger.info(s"createOrUpdateBlacklistResponse... $value")
          //createOrUpdateBlacklistResponse... List(2)
          assert(true)
        case Left(exception) =>
          Logger.info(s"Exception... ${exception}")
          assert(false)
      }
    }

    it("should fail when blacklistCreateUpdateForm.emails nonEmpty and findProspectIdsMatchingBlacklistedEmailsAndDomains returns failure"){
      val blacklistCreateUpdateForm1: BlacklistCreateEmailsForm = BlacklistCreateEmailsForm(
        emails = Seq(email1, email2)
      )

      (blacklistDAO.checkEmailAlreadyExistsInExceptionList)
        .expects(teamId, Seq(email1, email2))
        .returning(Success(Seq(BlacklistTeamLevel( 11L, teamId, org_id = accountAdmin.org.id, "blacklistname", Seq(), aDate, "some_uuid", do_not_contact_type = DoNotContactType.EMAIL))))

//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//        _: TeamId,
//        _: SrRollingUpdateFeature
//      )(_: ISRLogger))
//        .expects(TeamId(teamId), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .returning(false)

      (prospectServiceV2.findProspectIdsMatchingBlacklistedEmailsAndDomains(_: Long, _: BlacklistCreateForm)(using _: SRLogger))
        .expects(teamId, blacklistCreateUpdateForm1, *)
        .returning(Failure(new Throwable("Error while findProspectIdsMatchingBlacklistedEmailsAndDomains")))

      val res = blacklistService.createOrUpdateBlacklist(
        accountId = accountId,
        addedByName = "Unsubscribing", // Could be SmartReach.io api Unsubscribed, or user adding it
        teamId = teamId,
        taId = teamMember.ta_id,
        data = blacklistCreateUpdateForm1,
        is_req_via_dnc_form = false,
        opted_out_from_campaign_id = Some(campaign_id),
        opted_out_from_campaign_name = Some(campaign_name),
        account = Some(accountAdmin),
        auditRequestLogId = Some(auditRequestLogId),
        Logger= Logger,
        level= None,
      )

      res match{
        case Right(value) =>
          Logger.info(s"createOrUpdateBlacklistResponse... ${value.length}")
          assert(false)
        case Left(exception) =>
          Logger.info(s"Exception... ${exception}")
          //Exception... Error while findProspectIdsMatchingBlacklistedEmailsAndDomains.
          assert(true)
      }
    }

    it("should fail when blacklistCreateUpdateForm.emails nonEmpty and insertBlacklistRows returns failure"){
      val blacklistCreateUpdateForm1: BlacklistCreateEmailsForm = BlacklistCreateEmailsForm(
        emails = Seq(email1, email2)
      )

      (blacklistDAO.checkEmailAlreadyExistsInExceptionList)
        .expects(teamId, Seq(email1, email2))
        .returning(Success(Seq(BlacklistTeamLevel( 11L, teamId, org_id = accountAdmin.org.id, "blacklistname", Seq(), aDate, "some_uuid", do_not_contact_type = DoNotContactType.EMAIL))))

      val prospectBlacklistMatches = ProspectBlacklistMatches(
        id = ProspectId(101L),
        email = prospect_email,
        email_domain = "gmail.com",
        phone = None,
        prospect_category_id_custom = 22L,
      )

//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//        _: TeamId,
//        _: SrRollingUpdateFeature
//      )(_: ISRLogger))
//        .expects(TeamId(teamId), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .returning(false)

      (prospectServiceV2.findProspectIdsMatchingBlacklistedEmailsAndDomains(_: Long, _: BlacklistCreateForm)(using _: SRLogger))
        .expects(teamId, blacklistCreateUpdateForm1, *)
        .returning(Success(Seq(prospectBlacklistMatches)))

      (prospectDAOService.getProspectCategoryId(_: TeamId, _: ProspectCategory.Value, _: Option[Account])(using _:SRLogger))
        .expects(TeamId(id = teamId), ProspectCategory.DO_NOT_CONTACT, Some(accountAdmin), *)
        .returning(Success(ProspectCategoryId(22L)))

      ((() => srUuidUtils.generateBlacklistUuid()))
        .expects()
        .returning(blacklistUuid1.uuid)

      ((() => srUuidUtils.generateBlacklistUuid()))
        .expects()
        .returning(blacklistUuid2.uuid)

      (blacklistDAO.insertBlacklistRowsForTeam)
        .expects(TeamLevelBlacklistData(AccountId(accountId), TeamId(teamId), org_id = OrgId(accountAdmin.org.id), teamMember.ta_id, "Unsubscribing", Some(campaign_id), Some(campaign_name), blacklistCreateUpdateV3Form1))
        .returning(Failure(new Throwable("Error while insertBlacklistRows")))

      val res = blacklistService.createOrUpdateBlacklist(
        accountId = accountId,
        addedByName = "Unsubscribing", // Could be SmartReach.io api Unsubscribed, or user adding it
        teamId = teamId,
        taId = teamMember.ta_id,
        data = blacklistCreateUpdateForm1,
        is_req_via_dnc_form = false,
        opted_out_from_campaign_id = Some(campaign_id),
        opted_out_from_campaign_name = Some(campaign_name),
        account = Some(accountAdmin),
        auditRequestLogId = Some(auditRequestLogId),
        level= None,
        Logger= Logger
      )

      res match{
        case Right(value) =>
          Logger.info(s"createOrUpdateBlacklistResponse... ${value.length}")
          assert(false)
        case Left(exception) =>
          Logger.info(s"Exception... ${exception}")
          //Exception... Error while insertBlacklistRows
          assert(true)
      }
    }

    it("should successfully return created/updated blacklist when blacklistCreateUpdateForm.emails nonEmpty and empty prospectIds for _updateCategoryInDB"){
      val blacklistCreateUpdateForm1: BlacklistCreateEmailsForm = BlacklistCreateEmailsForm(
        emails = Seq(email1, email2)
      )

      val dnc_category_id = 22L

      (blacklistDAO.checkEmailAlreadyExistsInExceptionList)
        .expects(teamId, Seq(email1, email2))
        .returning(Success(Seq(BlacklistTeamLevel( 11L, teamId, org_id = accountAdmin.org.id, "blacklistname", Seq(), aDate, "some_uuid", do_not_contact_type = DoNotContactType.EMAIL))))

//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//        _: TeamId,
//        _: SrRollingUpdateFeature
//      )(_: ISRLogger))
//        .expects(TeamId(teamId), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .returning(false)

      val prospectBlacklistMatches = Seq(
        ProspectBlacklistMatches(id = ProspectId(101L), email = prospect_email, email_domain = "gmail.com", phone = None, prospect_category_id_custom = dnc_category_id),
        ProspectBlacklistMatches( id = ProspectId(101L), email = prospect_email, email_domain = "gmail.com", phone = None, prospect_category_id_custom = dnc_category_id)
      )

      val prospectIdsMatchingBlacklistNow: Seq[Long] = prospectBlacklistMatches
        .filter(_.prospect_category_id_custom != dnc_category_id)
        .map(_.id.id)

      (prospectServiceV2.findProspectIdsMatchingBlacklistedEmailsAndDomains(_: Long, _: BlacklistCreateForm)(using _: SRLogger))
        .expects(teamId, blacklistCreateUpdateForm1, *)
        .returning(Success(prospectBlacklistMatches))

      ((() => srUuidUtils.generateBlacklistUuid()))
        .expects()
        .returning(blacklistUuid1.uuid)

      ((() => srUuidUtils.generateBlacklistUuid()))
        .expects()
        .returning(blacklistUuid2.uuid)

      (blacklistDAO.insertBlacklistRowsForTeam)
        .expects(TeamLevelBlacklistData(AccountId(accountId), TeamId(teamId),  org_id = OrgId(accountAdmin.org.id), teamMember.ta_id, "Unsubscribing", Some(campaign_id), Some(campaign_name), blacklistCreateUpdateV3Form1))
        .returning(Success(Seq(2L)))

      (prospectDAOService.getProspectCategoryId(_: TeamId, _: ProspectCategory.Value, _: Option[Account])(using _:SRLogger))
        .expects(TeamId(id = teamId), ProspectCategory.DO_NOT_CONTACT, Some(accountAdmin), *)
        .returning(Success(ProspectCategoryId(22L)))

        (prospectUpdateCategoryTemp.updateCategoryAndCreateEvent)
          .expects(
            prospectIdsMatchingBlacklistNow,
            accountAdmin.internal_id,
            teamId,
            "Unsubscribing",
            prospectCategoryUpdateFlow,
            Some(accountAdmin),
            *,
            *,
            Some(auditRequestLogId))
          .returning(Success(1))

      val res = blacklistService.createOrUpdateBlacklist(
        accountId = accountId,
        addedByName = "Unsubscribing", // Could be SmartReach.io api Unsubscribed, or user adding it
        teamId = teamId,
        taId = teamMember.ta_id,
        data = blacklistCreateUpdateForm1,
        is_req_via_dnc_form = false,
        opted_out_from_campaign_id = Some(campaign_id),
        opted_out_from_campaign_name = Some(campaign_name),
        account = Some(accountAdmin),
        auditRequestLogId = Some(auditRequestLogId),
        level= None,
        Logger= Logger
      )

      res match{
        case Right(value) =>
          Logger.info(s"createOrUpdateBlacklistResponse... ${value}")
          //createOrUpdateBlacklistResponse... List(2)
          assert(true)
        case Left(exception) =>
          Logger.info(s"Exception... ${exception}")
          assert(false)
      }
    }

    it("should successfully return created/updated blacklist when blacklistCreateUpdateForm.emails nonEmpty"){
      val blacklistCreateUpdateForm1: BlacklistCreateEmailsForm = BlacklistCreateEmailsForm(
        emails = Seq(email1, email2)
      )

      (blacklistDAO.checkEmailAlreadyExistsInExceptionList)
        .expects(teamId, Seq(email1, email2))
        .returning(Success(Seq(BlacklistTeamLevel( 11L, teamId, org_id = accountAdmin.org.id, "blacklistname", Seq(), aDate, "some_uuid", do_not_contact_type = DoNotContactType.EMAIL))))

//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//        _: TeamId,
//        _: SrRollingUpdateFeature
//      )(_: ISRLogger))
//        .expects(TeamId(teamId), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .returning(false)

      val prospectBlacklistMatches = ProspectBlacklistMatches(
        id = ProspectId(101L),
        email = prospect_email,
        email_domain = "gmail.com",
        phone = None,
        prospect_category_id_custom = 23L
      )

      (prospectServiceV2.findProspectIdsMatchingBlacklistedEmailsAndDomains(_: Long, _: BlacklistCreateForm)(using _: SRLogger))
        .expects(teamId, blacklistCreateUpdateForm1, *)
        .returning(Success(Seq(prospectBlacklistMatches)))

      ((() => srUuidUtils.generateBlacklistUuid()))
        .expects()
        .returning(blacklistUuid1.uuid)

      ((() => srUuidUtils.generateBlacklistUuid()))
        .expects()
        .returning(blacklistUuid2.uuid)

      (blacklistDAO.insertBlacklistRowsForTeam)
        .expects(TeamLevelBlacklistData(AccountId(accountId), TeamId(teamId), org_id = OrgId(accountAdmin.org.id), teamMember.ta_id, "Unsubscribing", Some(campaign_id), Some(campaign_name), blacklistCreateUpdateV3Form1))
        .returning(Success(Seq(2L)))

      (prospectDAOService.getProspectCategoryId(_: TeamId, _: ProspectCategory.Value, _: Option[Account])(using _:SRLogger))
        .expects(TeamId(id = teamId), ProspectCategory.DO_NOT_CONTACT, Some(accountAdmin), *)
        .returning(Success(ProspectCategoryId(22L)))

      (prospectUpdateCategoryTemp.updateCategoryAndCreateEvent)
        .expects(
          Seq(101L), accountAdmin.internal_id, teamId, "Unsubscribing", prospectCategoryUpdateFlow, Some(accountAdmin), *, *, Some(auditRequestLogId))
        .returning(Success(1))

      val res = blacklistService.createOrUpdateBlacklist(
        accountId = accountId,
        addedByName = "Unsubscribing", // Could be SmartReach.io api Unsubscribed, or user adding it
        teamId = teamId,
        taId = teamMember.ta_id,
        data = blacklistCreateUpdateForm1,
        is_req_via_dnc_form = false,
        opted_out_from_campaign_id = Some(campaign_id),
        opted_out_from_campaign_name = Some(campaign_name),
        account = Some(accountAdmin),
        auditRequestLogId = Some(auditRequestLogId),
        level= None,
        Logger= Logger
      )

      res match{
        case Right(value) =>
          Logger.info(s"createOrUpdateBlacklistResponse... ${value}")
          //createOrUpdateBlacklistResponse... List(2)
          assert(true)
        case Left(exception) =>
          Logger.info(s"Exception... ${exception}")
          assert(false)
      }
    }

    it("should success when is_req_via_dnc_form true and domainsWithExcludedEmails.excluded_emails nonEmpty"){
      val blacklistCreateUpdateForm1: BlacklistCreateDomainsForm = BlacklistCreateDomainsForm(
        domains = Seq(domainsWithExcludedEmails)
      )

//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//        _: TeamId,
//        _: SrRollingUpdateFeature
//      )(_: ISRLogger))
//        .expects(TeamId(teamId), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .returning(false)

      (blacklistDAO.findBlacklistByEmails)
        .expects(teamId, Seq(prospect_email))
        .returning(Seq())

      val prospectBlacklistMatches = ProspectBlacklistMatches(
        id = ProspectId(101L),
        email = prospect_email,
        email_domain = "gmail.com",
        phone = None,
        prospect_category_id_custom = 23L
      )

      (prospectServiceV2.findProspectIdsMatchingBlacklistedEmailsAndDomains(_: Long, _: BlacklistCreateForm)(using _: SRLogger))
        .expects(teamId, blacklistCreateUpdateForm1, *)
        .returning(Success(Seq(prospectBlacklistMatches)))

      ((() => srUuidUtils.generateBlacklistUuid()))
        .expects()
        .returning(blacklistUuid1.uuid)

      (blacklistDAO.insertBlacklistRowsForTeam)
        .expects(TeamLevelBlacklistData(AccountId(accountId), TeamId(teamId), org_id = OrgId(accountAdmin.org.id), teamMember.ta_id, "Unsubscribing", Some(campaign_id), Some(campaign_name), blacklistCreateUpdateV3Form2))
        .returning(Success(Seq(2L)))

      (prospectDAOService.getProspectCategoryId(_: TeamId, _: ProspectCategory.Value, _: Option[Account])(using _:SRLogger))
        .expects(TeamId(id = teamId), ProspectCategory.DO_NOT_CONTACT, Some(accountAdmin), *)
        .returning(Success(ProspectCategoryId(22L)))

      (prospectUpdateCategoryTemp.updateCategoryAndCreateEvent)
        .expects(Seq(101L), accountAdmin.internal_id, teamId, "Unsubscribing", prospectCategoryUpdateFlow, Some(accountAdmin), *, *, Some(auditRequestLogId))
        .returning(Success(1))

      (prospectDAOService.getProspectCategoryId(_: TeamId, _: ProspectCategory.Value, _: Option[Account])(using _:SRLogger))
        .expects(TeamId(id = teamId), ProspectCategory.DO_NOT_CONTACT, Some(accountAdmin), *)
        .returning(Success(ProspectCategoryId(22L)))

      (prospectDAOService.findProspectIdsMatchingExcludedEmailsAndDNC)
        .expects(teamId, Seq(prospect_email), 22L, *)
        .returning(Success(Seq(prospectBlacklistMatches)))

      val res = blacklistService.createOrUpdateBlacklist(
        accountId = accountId,
        addedByName = "Unsubscribing", // Could be SmartReach.io api Unsubscribed, or user adding it
        teamId = teamId,
        taId = teamMember.ta_id,
        data = blacklistCreateUpdateForm1,
        is_req_via_dnc_form = true,
        opted_out_from_campaign_id = Some(campaign_id),
        opted_out_from_campaign_name = Some(campaign_name),
        account = Some(accountAdmin),
        auditRequestLogId = Some(auditRequestLogId),
        level= None,
        Logger= Logger
      )

      res match{
        case Right(value) =>
          Logger.info(s"createOrUpdateBlacklistResponse... ${value}")
          //createOrUpdateBlacklistResponse... List(2)
          assert(true)
        case Left(exception) =>
          Logger.info(s"Exception... ${exception}")
          assert(false)
      }
    }

    it("should success when is_req_via_dnc_form true and domainsWithExcludedEmails.excluded_emails is Empty"){

      val domainsWithExcludedEmails = DomainsWithExcludedEmails(
        domain = "somedomain.com",
        excluded_emails = Seq()
      )

      val domainsWithExcludedEmailsV3 = DomainsWithExcludedEmailsWithUuid(
        domain = "somedomain.com",
        excluded_emails = Seq(),
        blacklistUuid = blacklistUuid1
      )

      val blacklistCreateUpdateForm1 = BlacklistCreateDomainsForm(
        domains = Seq(domainsWithExcludedEmails)
      )

      val blacklistCreateUpdateV3Form1 = BlacklistCreateUpdateFormWithUuid(
        emails = Seq(),
        domains_with_excluded_emails = Seq(domainsWithExcludedEmailsV3),
        phones = Seq()
      )

      val prospectBlacklistMatches = ProspectBlacklistMatches(
        id = ProspectId(101L),
        email = prospect_email,
        email_domain = "gmail.com",
        phone = None,
        prospect_category_id_custom = 23L
      )

//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//        _: TeamId,
//        _: SrRollingUpdateFeature
//      )(_: ISRLogger))
//        .expects(TeamId(teamId), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .returning(false)

      (prospectServiceV2.findProspectIdsMatchingBlacklistedEmailsAndDomains(_: Long, _: BlacklistCreateForm)(using _: SRLogger))
        .expects(teamId, blacklistCreateUpdateForm1, *)
        .returning(Success(Seq(prospectBlacklistMatches)))


      ((() => srUuidUtils.generateBlacklistUuid()))
        .expects()
        .returning(blacklistUuid1.uuid)

      (blacklistDAO.insertBlacklistRowsForTeam)
        .expects(TeamLevelBlacklistData(AccountId(accountId), TeamId(teamId), org_id = OrgId(accountAdmin.org.id), teamMember.ta_id, "Unsubscribing", Some(campaign_id), Some(campaign_name), blacklistCreateUpdateV3Form1))
        .returning(Success(Seq(2L)))

      (prospectDAOService.getProspectCategoryId(_: TeamId, _: ProspectCategory.Value, _: Option[Account])(using _:SRLogger))
        .expects(TeamId(id = teamId), ProspectCategory.DO_NOT_CONTACT, Some(accountAdmin), *)
        .returning(Success(ProspectCategoryId(22L)))

      (prospectUpdateCategoryTemp.updateCategoryAndCreateEvent)
        .expects(Seq(101L), accountAdmin.internal_id, teamId, "Unsubscribing",prospectCategoryUpdateFlow, Some(accountAdmin), *,  *, Some(auditRequestLogId))
        .returning(Success(1))

      val res = blacklistService.createOrUpdateBlacklist(
        accountId = accountId,
        addedByName = "Unsubscribing", // Could be SmartReach.io api Unsubscribed, or user adding it
        teamId = teamId,
        taId = teamMember.ta_id,
        data = blacklistCreateUpdateForm1,
        is_req_via_dnc_form = true,
        opted_out_from_campaign_id = Some(campaign_id),
        opted_out_from_campaign_name = Some(campaign_name),
        account = Some(accountAdmin),
        auditRequestLogId = Some(auditRequestLogId),
        level= None,
        Logger= Logger
      )

      res match{
        case Right(value) =>
          Logger.info(s"createOrUpdateBlacklistResponse... $value")
          //createOrUpdateBlacklistResponse... List(2)
          assert(true)
        case Left(exception) =>
          Logger.info(s"Exception... ${exception}")
          assert(false)
      }
    }

    it("should fail when is_req_via_dnc_form true and domainsWithExcludedEmails.excluded_emails nonEmpty and blacklistDAO.delete returns failure"){
      val blacklistCreateUpdateForm1: BlacklistCreateDomainsForm = BlacklistCreateDomainsForm(
        domains = Seq(domainsWithExcludedEmails)
      )

//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//        _: TeamId,
//        _: SrRollingUpdateFeature
//      )(_: ISRLogger))
//        .expects(TeamId(teamId), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .returning(false)

      (blacklistDAO.findBlacklistByEmails)
        .expects(teamId, Seq(prospect_email))
        .returning(Seq(BlacklistTeamLevel(1L, 1L, org_id = accountAdmin.org.id, "some", Seq(), aDate, "some_uuid", do_not_contact_type = DoNotContactType.EMAIL)))

      val level = BlacklistCreateOrDeleteApiLevel.Team(org_id = OrgId(accountAdmin.org.id), team_id = TeamId(teamId), ta_id = teamMember.ta_id)

      (blacklistDAO.findAll)
        .expects(*, BlacklistCreateOrDeleteApiLevel.getBlacklistFindApiLevel(level), None, None, 100)
        .returning(Success(Seq(blacklist)))

//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//        _: TeamId,
//        _: SrRollingUpdateFeature
//      )(_: ISRLogger))
//        .expects(TeamId(teamId), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .returning(false)

      (prospectServiceV2.findProspectIdsMatchingBlacklistedEmailsAndDomains(_: Long, _: BlacklistCreateForm)(using _: SRLogger))
        .expects(teamId, BlacklistCreateEmailsForm(Seq()), *)
        .returning(Success(Seq()))

      (prospectServiceV2.findProspectIdsMatchingBlacklistedEmailsAndDomains(_: Long, _: BlacklistCreateForm)(using _: SRLogger))
        .expects(teamId, BlacklistCreateDomainsForm(Seq()), *)
        .returning(Success(Seq()))
      (prospectServiceV2.findProspectIdsMatchingBlacklistedEmailsAndDomains(_: Long, _: BlacklistCreateForm)(using _: SRLogger))
        .expects(teamId, BlacklistCreatePhoneForm(Seq()), *)
        .returning(Success(Seq()))

      (blacklistDAO._deleteBlacklistRows)
        .expects(Seq(1L), teamId)
        .returning(Failure(new Exception("DB failure")))


      val res = blacklistService.createOrUpdateBlacklist(
        accountId = accountId,
        addedByName = "Unsubscribing", // Could be SmartReach.io api Unsubscribed, or user adding it
        teamId = teamId,
        taId = teamMember.ta_id,
        data = blacklistCreateUpdateForm1,
        is_req_via_dnc_form = true,
        opted_out_from_campaign_id = Some(campaign_id),
        opted_out_from_campaign_name = Some(campaign_name),
        account = Some(accountAdmin),
        auditRequestLogId = Some(auditRequestLogId),
        level= None,
        Logger= Logger
      )

      res match{
        case Right(value) =>
          Logger.info(s"createOrUpdateBlacklistResponse... ${value}")
          assert(false)
        case Left(exception) =>
          Logger.info(s"Exception... ${exception}")
          //Exception... There was an error.
          assert(true)
      }
    }

    it("should success when is_req_via_dnc_form true and domainsWithExcludedEmails.excluded_emails nonEmpty and blacklistDAO.delete returns success"){
      val blacklistCreateUpdateForm1: BlacklistCreateDomainsForm = BlacklistCreateDomainsForm(
        domains = Seq(domainsWithExcludedEmails)
      )

//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//        _: TeamId,
//        _: SrRollingUpdateFeature
//      )(_: ISRLogger))
//        .expects(TeamId(teamId), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .returning(false)

      (blacklistDAO.findBlacklistByEmails)
        .expects(teamId, Seq(prospect_email))
        .returning(Seq(BlacklistTeamLevel(1L, 1L, org_id = accountAdmin.org.id, "some", Seq(), aDate, "some_uuid", do_not_contact_type = DoNotContactType.EMAIL)))

      val level = BlacklistCreateOrDeleteApiLevel.Team(org_id = OrgId(accountAdmin.org.id), team_id = TeamId(teamId), ta_id = teamMember.ta_id)

      (blacklistDAO.findAll)
        .expects(*, BlacklistCreateOrDeleteApiLevel.getBlacklistFindApiLevel(level), None, None, 100)
        .returning(Success(Seq(blacklist)))

//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//        _: TeamId,
//        _: SrRollingUpdateFeature
//      )(_: ISRLogger))
//        .expects(TeamId(teamId), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .returning(false)

      (prospectServiceV2.findProspectIdsMatchingBlacklistedEmailsAndDomains(_: Long, _: BlacklistCreateForm)(using _: SRLogger))
        .expects(teamId, BlacklistCreateEmailsForm(Seq()), *)
        .returning(Success(Seq()))

      (prospectServiceV2.findProspectIdsMatchingBlacklistedEmailsAndDomains(_: Long, _: BlacklistCreateForm)(using _: SRLogger))
        .expects(teamId, BlacklistCreateDomainsForm(Seq()), *)
        .returning(Success(Seq()))
      (prospectServiceV2.findProspectIdsMatchingBlacklistedEmailsAndDomains(_: Long, _: BlacklistCreateForm)(using _: SRLogger))
        .expects(teamId, BlacklistCreatePhoneForm(Seq()), *)
        .returning(Success(Seq()))

      (blacklistDAO._deleteBlacklistRows)
        .expects(Seq(1L), teamId)
        .returning(Success(Seq(blacklist)))

      (prospectServiceV2.getProspectCategoryId(_: TeamId, _: ProspectCategory.Value, _: Option[Account])(using _:SRLogger))
        .expects(TeamId(teamId), ProspectCategory.DO_NOT_CONTACT, None, *)
        .returning(Success(ProspectCategoryId(1)))

      (prospectServiceV2.getProspectCategoryId(_: TeamId, _: ProspectCategory.Value, _: Option[Account])(using _:SRLogger))
        .expects(TeamId(teamId), ProspectCategory.NOT_CATEGORIZED, None, *)
        .returning(Success(ProspectCategoryId(2)))

      (prospectUpdateCategoryTemp.updateCategoryAndCreateEvent)
        .expects(Seq(), 2, teamId, "Unsubscribing",  ProspectCategoryUpdateFlow.AdminUpdate(Some(ProspectCategoryId(1)), ProspectCategoryId(2)), None, *, *, Some(auditRequestLogId))
        .returning(Success(1))


      val prospectBlacklistMatches = ProspectBlacklistMatches(
        id = ProspectId(101L),
        email = prospect_email,
        email_domain =  "gmail.com",
        phone = None,
        prospect_category_id_custom = 23L
      )

      (prospectServiceV2.findProspectIdsMatchingBlacklistedEmailsAndDomains(_: Long, _: BlacklistCreateForm)(using _: SRLogger))
        .expects(teamId, blacklistCreateUpdateForm1, *)
        .returning(Success(Seq(prospectBlacklistMatches)))

      ((() => srUuidUtils.generateBlacklistUuid()))
        .expects()
        .returning(blacklistUuid1.uuid)

      (blacklistDAO.insertBlacklistRowsForTeam)
        .expects(TeamLevelBlacklistData(
          accountId = AccountId(accountId),
          teamId = TeamId(teamId),
          taId = teamMember.ta_id,
          addedByName = "Unsubscribing",
          opted_out_from_campaign_id = Some(campaign_id),
          opted_out_from_campaign_name =Some(campaign_name),
          data = blacklistCreateUpdateV3Form2,
          org_id = OrgId(accountAdmin.org.id)))
        .returning(Success(Seq(2L)))

      (prospectDAOService.getProspectCategoryId(_: TeamId, _: ProspectCategory.Value, _: Option[Account])(using _:SRLogger))
        .expects(TeamId(id = teamId), ProspectCategory.DO_NOT_CONTACT, Some(accountAdmin), *)
        .returning(Success(ProspectCategoryId(22L)))

      (prospectUpdateCategoryTemp.updateCategoryAndCreateEvent)
        .expects(
          Seq(101L),
          accountAdmin.internal_id,
          teamId,
          "Unsubscribing",
          prospectCategoryUpdateFlow,
          Some(accountAdmin), *,
          *,
          Some(auditRequestLogId))
        .returning(Success(1))

      (prospectDAOService.getProspectCategoryId(_: TeamId, _: ProspectCategory.Value, _: Option[Account])(using _:SRLogger))
        .expects(TeamId(id = teamId), ProspectCategory.DO_NOT_CONTACT, Some(accountAdmin), *)
        .returning(Success(ProspectCategoryId(22L)))

      (prospectDAOService.findProspectIdsMatchingExcludedEmailsAndDNC)
        .expects(teamId, Seq(prospect_email), 22L, *)
        .returning(Success(Seq(prospectBlacklistMatches)))

      val res = blacklistService.createOrUpdateBlacklist(
        accountId = accountId,
        addedByName = "Unsubscribing", // Could be SmartReach.io api Unsubscribed, or user adding it
        teamId = teamId,
        taId = teamMember.ta_id,
        data = blacklistCreateUpdateForm1,
        is_req_via_dnc_form = true,
        opted_out_from_campaign_id = Some(campaign_id),
        opted_out_from_campaign_name = Some(campaign_name),
        account = Some(accountAdmin),
        auditRequestLogId = Some(auditRequestLogId),
        level= None,
        Logger= Logger
      )

      res match{
        case Right(value) =>
          println(s"createOrUpdateBlacklistResponse... $value")
          // createOrUpdateBlacklistResponse... List(2)
          assert(value.length == 1 && value.head == 2)
        case Left(exception) =>
          println(s"Exception... ${exception}")
          assert(false)
      }
    }
  }

  describe("deleteBlacklistAndUpdateProspectCategory") {
    it("should return success for non-enc flow") {
      val level = BlacklistCreateOrDeleteApiLevel.Team(org_id = OrgId(accountAdmin.org.id), team_id = TeamId(teamId), ta_id = teamMember.ta_id)

//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//        _: TeamId,
//        _: SrRollingUpdateFeature
//      )(_: ISRLogger))
//        .expects(TeamId(teamId), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .returning(false)

      (prospectServiceV2.findProspectIdsMatchingBlacklistedEmailsAndDomains(_: Long, _: BlacklistCreateForm)(using _: SRLogger))
        .expects(teamId, BlacklistCreateEmailsForm(Seq("<EMAIL>")), *)
        .returning(Success(Seq(ProspectBlacklistMatches(
          id = ProspectId(101L),
          email = prospect_email,
          email_domain = "gmail.com",
          phone = None,
          prospect_category_id_custom = 23L
        ))))

      (prospectServiceV2.findProspectIdsMatchingBlacklistedEmailsAndDomains(_: Long, _: BlacklistCreateForm)(using _: SRLogger))
        .expects(teamId, BlacklistCreateDomainsForm(Seq()), *)
        .returning(Success(Seq()))

      (prospectServiceV2.findProspectIdsMatchingBlacklistedEmailsAndDomains(_: Long, _: BlacklistCreateForm)(using _: SRLogger))
        .expects(teamId, BlacklistCreatePhoneForm(Seq()), *)
        .returning(Success(Seq()))

      (blacklistDAO._deleteBlacklistRows)
        .expects(blacklist_ids, teamId)
        .returning(Success(Seq(blacklist)))

      (prospectServiceV2.getProspectCategoryId(_: TeamId, _: ProspectCategory.Value, _: Option[Account])(using _:SRLogger))
        .expects(TeamId(teamId), ProspectCategory.DO_NOT_CONTACT, Some(accountAdmin), *)
        .returning(Success(ProspectCategoryId(1)))

      (prospectServiceV2.getProspectCategoryId(_: TeamId, _: ProspectCategory.Value, _: Option[Account])(using _:SRLogger))
        .expects(TeamId(teamId), ProspectCategory.NOT_CATEGORIZED, Some(accountAdmin), *)
        .returning(Success(ProspectCategoryId(2)))

      (prospectUpdateCategoryTemp.updateCategoryAndCreateEvent)
        .expects(Seq(101L), 2, teamId, accountName,  ProspectCategoryUpdateFlow.AdminUpdate(Some(ProspectCategoryId(1)), ProspectCategoryId(2)), Some(accountAdmin), *, *, Some(auditRequestLogId))
        .returning(Success(1))

      val res: Try[Seq[Blacklist]] = blacklistService.deleteBlacklistAndUpdateProspectCategory(
        accountId = accountId,
        blacklistIds = blacklist_ids,
        accountName = accountName,
        teamId = Some(TeamId(teamId)),
        account = Some(accountAdmin),
        auditRequestLogId = Some(auditRequestLogId),
        level = level,
        allBlacklists = Seq(blacklist),
        Logger = Logger
      )

      assert(res.isSuccess && res.get.nonEmpty && res.get.head == blacklist)
    }

    it("should return success for enc flow") {
      val level = BlacklistCreateOrDeleteApiLevel.Team(org_id = OrgId(accountAdmin.org.id), team_id = TeamId(teamId), ta_id = teamMember.ta_id)

//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//        _: TeamId,
//        _: SrRollingUpdateFeature
//      )(_: ISRLogger))
//        .expects(TeamId(teamId), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .returning(true)

      (prospectServiceV2.findProspectIdsMatchingBlacklistedEmailsAndDomains(_: Long, _: BlacklistCreateForm)(using _: SRLogger))
        .expects(teamId, BlacklistCreateEmailsForm(Seq()), *)
        .returning(Success(Seq()))

      (prospectServiceV2.findProspectIdsMatchingBlacklistedEmailsAndDomains(_: Long, _: BlacklistCreateForm)(using _: SRLogger))
        .expects(teamId, BlacklistCreateDomainsForm(Seq()), *)
        .returning(Success(Seq()))

      (prospectServiceV2.findProspectIdsMatchingBlacklistedEmailsAndDomains(_: Long, _: BlacklistCreateForm)(using _: SRLogger))
        .expects(teamId, BlacklistCreatePhoneForm(Seq("**************")), *)
        .returning(Success(Seq(ProspectBlacklistMatches(
          id = ProspectId(101L),
          email = "null",
          email_domain = "null",
          phone = Some("+**************"),
          prospect_category_id_custom = 23L
        ))))

      (blacklistDAO._deleteBlacklistRows)
        .expects(blacklist_ids, teamId)
        .returning(Success(Seq(blacklist.copy(name = "**************", do_not_contact_type = DoNotContactType.PHONE))))

      (prospectServiceV2.getProspectCategoryId(_: TeamId, _: ProspectCategory.Value, _: Option[Account])(using _:SRLogger))
        .expects(TeamId(teamId), ProspectCategory.DO_NOT_CONTACT, Some(accountAdmin), *)
        .returning(Success(ProspectCategoryId(1)))

      (prospectServiceV2.getProspectCategoryId(_: TeamId, _: ProspectCategory.Value, _: Option[Account])(using _:SRLogger))
        .expects(TeamId(teamId), ProspectCategory.NOT_CATEGORIZED, Some(accountAdmin), *)
        .returning(Success(ProspectCategoryId(2)))

      (prospectUpdateCategoryTemp.updateCategoryAndCreateEvent)
        .expects(Seq(101L), 2, teamId, accountName,  ProspectCategoryUpdateFlow.AdminUpdate(Some(ProspectCategoryId(1)), ProspectCategoryId(2)), Some(accountAdmin), *, *, Some(auditRequestLogId))
        .returning(Success(1))

      val res: Try[Seq[Blacklist]] = blacklistService.deleteBlacklistAndUpdateProspectCategory(
        accountId = accountId,
        blacklistIds = blacklist_ids,
        accountName = accountName,
        teamId = Some(TeamId(teamId)),
        account = Some(accountAdmin),
        auditRequestLogId = Some(auditRequestLogId),
        level = level,
        allBlacklists = Seq(blacklist.copy(name = "**************", do_not_contact_type = DoNotContactType.PHONE)),
        Logger = Logger
      )

      assert(res.isSuccess && res.get.nonEmpty && res.get.head == blacklist.copy(name = "**************", do_not_contact_type = DoNotContactType.PHONE))
    }

  }

}
