package app.utils.email

import api.accounts.{Account, AccountService, SocialAuthService, TeamId}
import api.accounts.models.{AccountId, OrgId}
import api.email_infra_integrations.dao.{EmailInfraDAO, OrgPurchasedDomainAndEmailForCancellation, PurchasedDomains}
import api.email_infra_integrations.maildoso.{CreateDomainResponse, CreateEmailAccountRequest, DomainResult, TaskStateResponse}
import api.email_infra_integrations.maildoso.service.MailDosoService
import api.email_infra_integrations.models.PurchaseDomainsAndEmailsStatus.SCHEDULED_FOR_DELETION
import api.email_infra_integrations.models.{EmailInfraWorkspaceType, PlatformType, PurchaseAdditionalEmailsForm, PurchaseDomainResponseResultType, PurchaseDomainsAndEmailsStatus, PurchaseEmail, PurchasedDomain, PurchasedDomain<PERSON>orm}
import api.email_infra_integrations.maildoso.{PurchaseDomainResponse, PurchaseDomainResult}
import api.email_infra_integrations.services.EmailInfraService
import api.emails.{EmailSettingDAO, PurchasedEmailInfo}
import api.emails.models.EmailSettingIntegrationLogsStage
import api.emails.services.{EmailAccountService, EmailSettingService}
import api.prospects.dao_service.ProspectDAOService
import api.rep_mail_servers.services.SrMailServerService
import io.smartreach.esp.api.emails.EmailSettingId
import org.apache.pekko.actor.ActorSystem
import org.apache.pekko.stream.Materializer
import org.joda.time.DateTime
import org.scalamock.matchers.ArgCapture.CaptureOne
import org.scalamock.scalatest.AsyncMockFactory
import org.scalatest.funspec.AsyncFunSpec
import play.api.libs.json.{JsError, JsSuccess, JsValue, Json}
import play.api.libs.ws.WSClient
import play.api.libs.ws.ahc.AhcWSClient
import utils.{Helpers, PlanLimitService, SRLogger}
import utils.emailvalidation.EmailValidationService
import utils.helpers.LogHelpers
import utils.mq.purchased_domains_and_emails_deleter.{MqPurchasedDomainsDeleter, MqPurchasedEmailsDeleter}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}
import api.email_infra_integrations.maildoso.{CreateDomainRequest, CreateEmailAccountResponse}
import api.email_infra_integrations.zapmail.ZapMailAPI
import utils.testapp.TestAppExecutionContext
import utils.testapp.Test_TaskPgDAO.mqZapmailEmailAccountCreationPublisher

class EmailInfraServiceSpec extends AsyncFunSpec with AsyncMockFactory {
  implicit lazy val system: ActorSystem = TestAppExecutionContext.actorSystem
  implicit lazy val materializer: Materializer = TestAppExecutionContext.actorMaterializer
  implicit lazy val wsClient: AhcWSClient = TestAppExecutionContext.wsClient
  given Logger: SRLogger = new SRLogger("some_log_re_id")
  val mailDosoService = mock[MailDosoService]
  val emailInfraDao = mock[EmailInfraDAO]
  val emailSettingDAO = mock[EmailSettingDAO]
  val prospectDAOService = mock[ProspectDAOService]
  val planLimitService = mock[PlanLimitService]
  val zapMailApi = mock[ZapMailAPI]
  val srMailServerService = mock[SrMailServerService]
  val emailSettingService: EmailSettingService = mock[EmailSettingService]
  val emailAccountService: EmailAccountService = mock[EmailAccountService]
  val accountService: AccountService = mock[AccountService]
  val socialAuthService: SocialAuthService = mock[SocialAuthService]
  val mqPurchasedDomainsDeleter: MqPurchasedDomainsDeleter = mock[MqPurchasedDomainsDeleter]
  val mqPurchasedEmailsDeleter: MqPurchasedEmailsDeleter = mock[MqPurchasedEmailsDeleter]

  val emailInfraService = new EmailInfraService(
    mailDosoService = mailDosoService,
    emailInfraDAO = emailInfraDao,
    emailSettingDAO = emailSettingDAO,
    planLimitService = planLimitService,
    emailSettingService = emailSettingService,
    socialAuthService = socialAuthService,
    mqPurchasedDomainsDeleter = mqPurchasedDomainsDeleter,
    mqPurchasedEmailsDeleter = mqPurchasedEmailsDeleter,
    emailAccountService = emailAccountService,
    accountService = accountService,
    mqZapmailEmailAccountCreationPublisher = mqZapmailEmailAccountCreationPublisher,
    zapMailApi= zapMailApi
  )

  describe("unit tests for EmailInfraService") {

    describe("maildoso unit tests") {
      it("test for buy_maildoso_domains") {

        val domainsToBuy = List(
          "domain1.com",
          "domain2.com",
          "domain3.com",
          "domain4.com",
        )

        val redirect_to = List(
          "basedomain.com",
          "basedomain.com",
          "basedomain.com",
          "basedomain.com",
        )

        val accountId = AccountId(29)
        val teamId = TeamId(30)
        val maildosoUserId = 31L

        (mailDosoService.getOrCreateMaildosoUser(_: AccountId, _: TeamId))
          .expects(accountId, teamId)
          .returning(Success(maildosoUserId))

        (mailDosoService.createDomains(_: Long, _: List[String], _: List[String])(_: ExecutionContext, _: WSClient))
          .expects(maildosoUserId, domainsToBuy, redirect_to, *, *)
          .returning(Future.successful(CreateDomainResponse(results = List(), task_id = None)))

        (mailDosoService.saveMaildosoTaskId(_: AccountId, _: TeamId, _: Option[String]))
          .expects(accountId, teamId, None)
          .returning(Success({}))

        for {
          res <- emailInfraService.buy_maildoso_domains(
            domains = domainsToBuy,
            redirect_to = redirect_to,
            account_id = accountId,
            teamId = teamId
          )
        } yield {
          assert(true)
        }

      }

      it("test for buy_maildoso_emails") {

        val emailsToBuy = List(
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
        )
        val first_name = "Parth"
        val last_name = "Gupta"
        val create_email_account_request: List[CreateEmailAccountRequest] = emailsToBuy.map(em =>
          CreateEmailAccountRequest(
            email_account = em,
            first_name = first_name,
            last_name = last_name,
            password = None,
            is_active = true,
            forwarding_account_id = None,
            skip_sequence = None
          ))

        val accountId = AccountId(29)
        val teamId = TeamId(30)
        val maildosoUserId = 31

        (mailDosoService.createEmailAccount(_: List[CreateEmailAccountRequest])(_: ExecutionContext, _: WSClient, _: SRLogger))
          .expects(create_email_account_request, *, *, *)
          .returning(Future.successful(CreateEmailAccountResponse(results = List(), task_id = None)))

        (mailDosoService.saveMaildosoTaskId(_: AccountId, _: TeamId, _: Option[String]))
          .expects(accountId, teamId, None)
          .returning(Success({}))

        for {
          res <- emailInfraService.buy_maildoso_emails(
            emails = emailsToBuy,
            accountId = accountId,
            teamId = teamId,
            first_name = first_name,
            last_name = last_name
          )
        } yield {
          assert(true)
        }

      }

      it("test for search_domains") {

        def generateRandomDomains(n: Int): List[String] = {

          (1 to n).map { _ =>
            val name = Helpers.generateRandomString(10)
            val extension = ".com"
            s"$name$extension"
          }.toList
        }

        val domainsToBuy = generateRandomDomains(100)

        val validated_domains = domainsToBuy.filter(EmailValidationService.validateDomain)

        (mailDosoService.searchDomains(_: List[String], _: Option[Int])(_: ExecutionContext, _: WSClient, _: SRLogger))
          .expects(validated_domains, *, *, *, *)
          .returning(Future.successful(
            validated_domains.map(d =>
              DomainResult(
                name = d,
                status = None,
                available = Some("yes"),
                error = None
              ))))


        for {
          res <- emailInfraService.search_for_domains(
            domains = domainsToBuy
          )

          _ = println(s"debugging res - $res")

        } yield {

          assert(res.domains.length == domainsToBuy.length)
        }

      }
    }

  }

  describe("testing writes and reads of EmailSettingForm") {

    it("should write and read EmailSettingForm") {

      val taskRes = Json.parse("{\n  \"task_id\": \"11673f06-efea-46f8-9385-0d76d40a7de4\",\n  \"task_name\": \"domains.bulk_setup\",\n  \"task_state\": \"SUCCESS\",\n  \"task_meta\": {\n    \"smartreachtakesyouforward.click\": {\n      \"STARTED\": [],\n      \"SUCCESS\": [\n        {\n          \"id\": \"4f3734f7-0a20-4bbb-8e09-edac53f1ecd5\",\n          \"name\": \"domains.cloudflare.create_zone\"\n        },\n        {\n          \"id\": \"0a830c07-7b24-4551-bde8-de631bc473b0\",\n          \"name\": \"domains.dynadot.set_name_servers\"\n        },\n        {\n          \"id\": \"773439b8-ade2-4d45-b0e1-275cc17f1c90\",\n          \"name\": \"domains.cloudflare.create_dns_record\"\n        },\n        {\n          \"id\": \"0e681884-427d-4be6-892c-283045d71a28\",\n          \"name\": \"domains.cloudflare.create_dns_record\"\n        },\n        {\n          \"id\": \"e1a02be8-88dd-4aea-b219-629521745dfa\",\n          \"name\": \"domains.cloudflare.create_dns_record\"\n        },\n        {\n          \"id\": \"44e39887-0bfd-4b09-b28e-d35851d459b3\",\n          \"name\": \"domains.cloudflare.create_dns_record\"\n        },\n        {\n          \"id\": \"11090fc7-6cb0-4ffd-9704-ebc562b68c4b\",\n          \"name\": \"domains.cloudflare.create_dns_record\"\n        },\n        {\n          \"id\": \"4543ef98-c5df-4cd6-b1ad-b6bf71af5904\",\n          \"name\": \"domains.cloudflare.create_dns_record\"\n        },\n        {\n          \"id\": \"9269c67e-2ede-4d16-ad77-ceefe8d3825b\",\n          \"name\": \"domains.cloudflare.create_dns_record\"\n        },\n        {\n          \"id\": \"280d447b-f38a-4410-b120-8e5871736ed9\",\n          \"name\": \"domains.cloudflare.create_dns_record\"\n        },\n        {\n          \"id\": \"ce885c15-f257-40e4-be49-44d38b89b92e\",\n          \"name\": \"domains.cloudflare.create_dns_record\"\n        },\n        {\n          \"id\": \"6a3d2a28-12e4-4397-8020-08dcd306634d\",\n          \"name\": \"domains.cloudflare.create_dns_record\"\n        },\n        {\n          \"id\": \"81e34f92-b787-409b-bbe6-7af3fa8cad92\",\n          \"name\": \"domains.cloudflare.create_dns_record\"\n        },\n        {\n          \"id\": \"b88171e7-0367-41a3-ab62-6180e18a35a3\",\n          \"name\": \"domains.cloudflare.create_dns_record\"\n        },\n        {\n          \"id\": \"f2c07dc1-6ebe-4f26-bccf-96ac8c54e3ef\",\n          \"name\": \"domains.cloudflare.create_dns_record\"\n        },\n        {\n          \"id\": \"4e33088c-fbd3-4bb1-8e28-016da8703048\",\n          \"name\": \"domains.cloudflare.create_dns_record\"\n        },\n        {\n          \"id\": \"92266976-b612-4567-9f21-8c53783644ae\",\n          \"name\": \"domains.cloudflare.create_dns_record\"\n        },\n        {\n          \"id\": \"23ac85df-fda8-4759-941b-e2d67f1deaec\",\n          \"name\": \"domains.cloudflare.create_dns_record\"\n        },\n        {\n          \"id\": \"f7ceb838-f617-46cf-a8ca-1615059f05e6\",\n          \"name\": \"domains.cloudflare.create_dns_record\"\n        },\n        {\n          \"id\": \"8ee8142e-0e1c-4019-bc5c-857a863d3ba1\",\n          \"name\": \"domains.cloudflare.create_dns_record\"\n        },\n        {\n          \"id\": \"6fd8020f-7e8e-494d-9ab0-9cb7469c2f67\",\n          \"name\": \"domains.cloudflare.create_dns_record\"\n        },\n        {\n          \"id\": \"6019f8a4-3c56-491b-8d99-ad61a347eac5\",\n          \"name\": \"domains.cloudflare.create_dns_record\"\n        },\n        {\n          \"id\": \"0a99968e-5601-41f2-ae19-13919f19bdd7\",\n          \"name\": \"domains.cloudflare.create_ruleset\"\n        }\n      ],\n      \"FAILURE\": [],\n      \"RETRY\": [],\n      \"REVOKED\": [],\n      \"PENDING\": []\n    }\n  },\n  \"task_ready\": true,\n  \"task_args\": [\n    [\n      \"smartreachtakesyouforward.click\"\n    ],\n    [\n      \"smartreach.io\"\n    ]\n  ],\n  \"task_kwargs\": {}\n}")
      println(s"taskRes: $taskRes")
      val validated_email_setting_form = taskRes.validate[TaskStateResponse]

      validated_email_setting_form match {
        case JsSuccess(value, path) =>

          println(s"value: $value")

          assert(true)

        case JsError(errors) => assert(false)
      }
    }


  }

  describe("testing password for additional email accounts to be generated vai backend") {

    it("should generate a new password in backend and not use 'dummy_password") {

      val teamId = TeamId(1)
      val accountId = AccountId(1)
      val purchaseDomainUuid = "pd_uuid"
      val worker_task_id = "task_id_by_maildoso"

      val purchasedEmails: List[PurchaseEmail.Maildoso] = List(
        PurchaseEmail.Maildoso(
          first_name = "first_name",
          last_name = "last_name",
          email_account = "<EMAIL>",
          password = "dummy_password"
        )
      )

      val purchaseAdditionalEmailsForm: PurchaseAdditionalEmailsForm = PurchaseAdditionalEmailsForm(
        purchasedEmails = purchasedEmails
      )

      val purchasedDomains: PurchasedDomains = PurchasedDomains(
        purchased_domain_uuid = purchaseDomainUuid,
        platform_domain_id = None,
        platform_type = PlatformType.MAILDOSO,
        teamId = teamId,
        accountId = accountId,
        domain_name = "smartreachlabs.click",
        purchased_emails = List(),
        domains_purchased_at = None,
        emails_purchased_at = None,
        expire_at = None,
        created_at = DateTime.now(),
        domain_redirect_to = None,
        domain_status = PurchaseDomainsAndEmailsStatus.ACTIVE,
        emails_status = PurchaseDomainsAndEmailsStatus.ACTIVE

      )

      (planLimitService.checkEmailsAndDomainsAddonLimitReached (_: Int, _: Int, _: PlatformType, _: TeamId)(_: SRLogger))
        .expects(purchaseAdditionalEmailsForm.purchasedEmails.length, 0, PlatformType.MAILDOSO, teamId, *)
        .returning(Success(false))

      (emailInfraDao.getPurchasedDomainsForMaildosoByUuid)
        .expects(purchaseDomainUuid, teamId)
        .returning(Success(Some(purchasedDomains)))

      (emailSettingDAO.checkIfEmailExists)
        .expects(purchasedEmails.map(_.email_account), teamId)
        .returning(Success(false))

      val createEmailAccountRequest = CaptureOne[List[CreateEmailAccountRequest]]()

      (mailDosoService.createEmailAccount (_: List[CreateEmailAccountRequest])(_: ExecutionContext, _: WSClient, _: SRLogger))
        .expects(capture(createEmailAccountRequest), *, *, *)
        .returning(Future.successful(CreateEmailAccountResponse(results = List(), task_id = Some(worker_task_id))))

      (emailSettingDAO.updatePurchasedEmailSettingsTaskId)
        .expects(purchasedEmails.map(_.email_account), teamId, worker_task_id)
        .returning(Success(1))

      val res = emailInfraService.purchaseAdditionalEmails(
        purchaseAdditionalEmailsForm = purchaseAdditionalEmailsForm,
        purchasedDomainUuid = purchaseDomainUuid,
        teamId = teamId,
        accountId = accountId
      )

      val email_password = createEmailAccountRequest.value.head.password.get
      println(s"email_password: $email_password")
      assert(email_password != "dummy_password")
      assert(email_password.nonEmpty)

    }

  }

  describe("testing scheduleOrgDomainsAndEmailsForDeletionOnCancellation") {

    it("should schedule org domains and emails for deletion on cancellation") {

      val org_id = OrgId(5)
      val scheduled_for_cancellation_at = DateTime.parse("2020-01-01T00:00:00Z")

      (emailInfraDao.fetchDomainsAndEmailsForOrg(_: OrgId))
        .expects(org_id)
        .returning(Success(List(OrgPurchasedDomainAndEmailForCancellation(
          org_id = org_id,
          team_id = TeamId(1),
          accountId = AccountId(1),
          purchasedDomainUuid = "1",
          domainStatus = PurchaseDomainsAndEmailsStatus.ACTIVE,
          domainName = "smartreachlabs.click",
          domainCreatedAt = DateTime.parse("2020-01-01T00:00:00Z"),
          emailSettingId = None,
          emailStatus = None,
          emailAddress = None,
          platformEmailId = None,
          platformType = PlatformType.MAILDOSO
        ),OrgPurchasedDomainAndEmailForCancellation(
          org_id = org_id,
          team_id = TeamId(2),
          accountId = AccountId(1),
          purchasedDomainUuid = "2",
          domainStatus = PurchaseDomainsAndEmailsStatus.ACTIVE,
          domainName = "smartreachlabs.click",
          domainCreatedAt = DateTime.parse("2020-01-01T00:00:00Z"),
          emailSettingId = None,
          emailStatus = None,
          emailAddress = None,
          platformEmailId = None,
          platformType = PlatformType.MAILDOSO
        ),OrgPurchasedDomainAndEmailForCancellation(
          org_id = org_id,
          team_id = TeamId(3),
          accountId = AccountId(1),
          purchasedDomainUuid = "3",
          domainStatus = PurchaseDomainsAndEmailsStatus.ACTIVE,
          domainName = "smartreachlabs.click",
          domainCreatedAt = DateTime.parse("2020-01-01T00:00:00Z"),
          emailSettingId = None,
          emailStatus = None,
          emailAddress = None,
          platformEmailId = None,
          platformType = PlatformType.MAILDOSO
        ))))

      // -- First Domain Id -- //

      (emailInfraDao.getPlatformDomainIdsByUuids(_: List[String], _: TeamId))
        .expects(List("1"), TeamId(1))
        .returning(Success(List("platform_domain_id_1")))

      (emailSettingService.getEmailSettingIdsFromPlatformDomainIds(_: List[String], _: TeamId))
        .expects(List("platform_domain_id_1"), TeamId(1))
        .returning(Success(List(EmailSettingId(1))))

      (emailInfraDao.updatePurchasedDomainStatusByPurchaseDomainUuids(_: List[String], _: PurchaseDomainsAndEmailsStatus, _: TeamId, _: Option[AccountId], _: Option[DateTime]))
        .expects(
          List("1"),
          PurchaseDomainsAndEmailsStatus.SCHEDULED_FOR_DELETION,
          TeamId(1),
          Some(AccountId(1)),
          Some(scheduled_for_cancellation_at.minusMinutes(30))
        )
        .returning(Success(List("platform_domain_id_1")))

      // -- Second Domain Id -- //

      (emailInfraDao.getPlatformDomainIdsByUuids(_: List[String], _: TeamId))
        .expects(List("2"), TeamId(2))
        .returning(Success(List("platform_domain_id_2")))

      (emailSettingService.getEmailSettingIdsFromPlatformDomainIds(_: List[String], _: TeamId))
        .expects(List("platform_domain_id_2"), TeamId(2))
        .returning(Success(List(EmailSettingId(2))))

      (emailInfraDao.updatePurchasedDomainStatusByPurchaseDomainUuids(_: List[String], _: PurchaseDomainsAndEmailsStatus, _: TeamId, _: Option[AccountId], _: Option[DateTime]))
        .expects(
          List("2"),
          PurchaseDomainsAndEmailsStatus.SCHEDULED_FOR_DELETION,
          TeamId(2),
          Some(AccountId(1)),
          Some(scheduled_for_cancellation_at.minusMinutes(30))
        )
        .returning(Success(List("platform_domain_id_2")))

      // -- Third Domain Id -- //

      (emailInfraDao.getPlatformDomainIdsByUuids(_: List[String], _: TeamId))
        .expects(List("3"), TeamId(3))
        .returning(Success(List("platform_domain_id_3")))

      (emailSettingService.getEmailSettingIdsFromPlatformDomainIds(_: List[String], _: TeamId))
        .expects(List("platform_domain_id_3"), TeamId(3))
        .returning(Success(List(EmailSettingId(2))))

      (emailInfraDao.updatePurchasedDomainStatusByPurchaseDomainUuids(_: List[String], _: PurchaseDomainsAndEmailsStatus, _: TeamId, _: Option[AccountId], _: Option[DateTime]))
        .expects(
          List("3"),
          PurchaseDomainsAndEmailsStatus.SCHEDULED_FOR_DELETION,
          TeamId(3),
          Some(AccountId(1)),
          Some(scheduled_for_cancellation_at.minusMinutes(30))
        )
        .returning(Success(List("platform_domain_id_3")))


      val em = emailInfraService.scheduleOrgDomainsAndEmailsForDeletionOnCancellation(
        org_id = org_id,
        scheduled_for_cancellation_at = scheduled_for_cancellation_at
      )

      em.map(
       value => {
         println(s"value: $value")
         assert(true)
       }
      ).recover {
        exception =>
        println(LogHelpers.getStackTraceAsString(exception))
        assert(false)
      }
    }

    it("should schedule org domains and emails for deletion on cancellation - passing emails") {

      val org_id = OrgId(5)
      val scheduled_for_cancellation_at = DateTime.parse("2020-01-01T00:00:00Z")
      val orgPurchasedDomainAndEmailForCancellation = OrgPurchasedDomainAndEmailForCancellation(
        org_id = org_id,
        team_id = TeamId(1),
        accountId = AccountId(1),
        purchasedDomainUuid = "1",
        domainStatus = PurchaseDomainsAndEmailsStatus.ACTIVE,
        domainName = "smartreachlabs.click",
        domainCreatedAt = DateTime.parse("2020-01-01T00:00:00Z"),
        emailSettingId = None,
        emailStatus = None,
        emailAddress = None,
        platformEmailId = None,
        platformType = PlatformType.MAILDOSO
      )

      (emailInfraDao.fetchDomainsAndEmailsForOrg(_: OrgId))
        .expects(org_id)
        .returning(Success(List(

          orgPurchasedDomainAndEmailForCancellation.copy(
            team_id = TeamId(1),
            accountId = AccountId(1),
            purchasedDomainUuid = "1",
            domainStatus = PurchaseDomainsAndEmailsStatus.ACTIVE,
            domainName = "smartreachlabs.click",
            domainCreatedAt = DateTime.parse("2020-01-01T00:00:00Z"),
            emailSettingId = Some(EmailSettingId(1)),
            emailStatus = Some(SCHEDULED_FOR_DELETION),
            emailAddress = Some("<EMAIL>"),
            platformEmailId = Some(1)
          ),

          orgPurchasedDomainAndEmailForCancellation.copy(
            team_id = TeamId(2),
            accountId = AccountId(1),
            purchasedDomainUuid = "2",
            domainStatus = PurchaseDomainsAndEmailsStatus.ACTIVE,
            domainName = "smartreachlabs.click",
            domainCreatedAt = DateTime.parse("2020-01-01T00:00:00Z"),
            emailSettingId = Some(EmailSettingId(2)),
            emailStatus = Some(SCHEDULED_FOR_DELETION),
            emailAddress = Some("<EMAIL>"),
            platformEmailId = Some(2)
          ),

          orgPurchasedDomainAndEmailForCancellation.copy(
            team_id = TeamId(3),
            accountId = AccountId(1),
            purchasedDomainUuid = "3",
            domainStatus = PurchaseDomainsAndEmailsStatus.ACTIVE,
            domainName = "smartreachlabs.click",
            domainCreatedAt = DateTime.parse("2020-01-01T00:00:00Z"),
            emailSettingId = Some(EmailSettingId(3)),
            emailStatus = Some(SCHEDULED_FOR_DELETION),
            emailAddress = Some("<EMAIL>"),
            platformEmailId = Some(3)
          )

        )))

      // -- First EmailSetting scheduled for deletion -- //

      (emailSettingDAO.checkEmailSettingExists(_: EmailSettingId, _: TeamId))
        .expects(EmailSettingId(1), TeamId(1))
        .returning(Success(true))

      (emailSettingDAO.scheduleEmailForDeletion(_: EmailSettingId, _: TeamId, _: DateTime)(using _: SRLogger))
        .expects(EmailSettingId(1), TeamId(1), scheduled_for_cancellation_at.minusMinutes(30), *)
        .returning(Success(1))

      (emailSettingDAO.getDomainPurchasedInfoFromEmailSettingId(_: EmailSettingId, _: TeamId))
        .expects(EmailSettingId(1), TeamId(1))
        .returning(Success(PurchasedEmailInfo("<EMAIL>", "example.com", PlatformType.MAILDOSO)))

      // -- First EmailSetting scheduled for deletion -- //

      (emailSettingDAO.checkEmailSettingExists(_: EmailSettingId, _: TeamId))
        .expects(EmailSettingId(2), TeamId(2))
        .returning(Success(true))


      (emailSettingDAO.getDomainPurchasedInfoFromEmailSettingId(_: EmailSettingId, _: TeamId))
        .expects(EmailSettingId(2), TeamId(2))
        .returning(Success(PurchasedEmailInfo("<EMAIL>", "example.com", PlatformType.MAILDOSO)))


      (emailSettingDAO.scheduleEmailForDeletion(_: EmailSettingId, _: TeamId, _: DateTime)(using _: SRLogger))
        .expects(EmailSettingId(2), TeamId(2), scheduled_for_cancellation_at.minusMinutes(30), *)
        .returning(Success(1))


      (emailSettingDAO.getDomainPurchasedInfoFromEmailSettingId(_: EmailSettingId, _: TeamId))
        .expects(EmailSettingId(3), TeamId(3))
        .returning(Success(PurchasedEmailInfo("<EMAIL>", "example.com", PlatformType.MAILDOSO)))


      // -- First EmailSetting scheduled for deletion -- //

      (emailSettingDAO.checkEmailSettingExists(_: EmailSettingId, _: TeamId))
        .expects(EmailSettingId(3), TeamId(3))
        .returning(Success(true))

      (emailSettingDAO.scheduleEmailForDeletion(_: EmailSettingId, _: TeamId, _: DateTime)(using _: SRLogger))
        .expects(EmailSettingId(3), TeamId(3), scheduled_for_cancellation_at.minusMinutes(30), *)
        .returning(Success(1))



      (emailInfraDao.getPlatformDomainIdsByUuids(_: List[String], _: TeamId))
        .expects(List("1"), TeamId(1))
        .returning(Success(List("platform_domain_id_1")))

      (emailSettingService.getEmailSettingIdsFromPlatformDomainIds(_: List[String], _: TeamId))
        .expects(List("platform_domain_id_1"), TeamId(1))
        .returning(Success(List(EmailSettingId(1))))

      (emailInfraDao.updatePurchasedDomainStatusByPurchaseDomainUuids(_: List[String], _: PurchaseDomainsAndEmailsStatus, _: TeamId, _: Option[AccountId], _: Option[DateTime]))
        .expects(
          List("1"),
          PurchaseDomainsAndEmailsStatus.SCHEDULED_FOR_DELETION,
          TeamId(1),
          Some(AccountId(1)),
          Some(scheduled_for_cancellation_at.minusMinutes(30))
        )
        .returning(Success(List("platform_domain_id_1")))

      // -- Second Domain Id -- //

      (emailInfraDao.getPlatformDomainIdsByUuids(_: List[String], _: TeamId))
        .expects(List("2"), TeamId(2))
        .returning(Success(List("platform_domain_id_2")))

      (emailSettingService.getEmailSettingIdsFromPlatformDomainIds(_: List[String], _: TeamId))
        .expects(List("platform_domain_id_2"), TeamId(2))
        .returning(Success(List(EmailSettingId(2))))

      (emailInfraDao.updatePurchasedDomainStatusByPurchaseDomainUuids(_: List[String], _: PurchaseDomainsAndEmailsStatus, _: TeamId, _: Option[AccountId], _: Option[DateTime]))
        .expects(
          List("2"),
          PurchaseDomainsAndEmailsStatus.SCHEDULED_FOR_DELETION,
          TeamId(2),
          Some(AccountId(1)),
          Some(scheduled_for_cancellation_at.minusMinutes(30))
        )
        .returning(Success(List("platform_domain_id_2")))

      // -- Third Domain Id -- //

      (emailInfraDao.getPlatformDomainIdsByUuids(_: List[String], _: TeamId))
        .expects(List("3"), TeamId(3))
        .returning(Success(List("platform_domain_id_3")))

      (emailSettingService.getEmailSettingIdsFromPlatformDomainIds(_: List[String], _: TeamId))
        .expects(List("platform_domain_id_3"), TeamId(3))
        .returning(Success(List(EmailSettingId(2))))

      (emailInfraDao.updatePurchasedDomainStatusByPurchaseDomainUuids(_: List[String], _: PurchaseDomainsAndEmailsStatus, _: TeamId, _: Option[AccountId], _: Option[DateTime]))
        .expects(
          List("3"),
          PurchaseDomainsAndEmailsStatus.SCHEDULED_FOR_DELETION,
          TeamId(3),
          Some(AccountId(1)),
          Some(scheduled_for_cancellation_at.minusMinutes(30))
        )
        .returning(Success(List("platform_domain_id_3")))


      val em = emailInfraService.scheduleOrgDomainsAndEmailsForDeletionOnCancellation(
        org_id = org_id,
        scheduled_for_cancellation_at = scheduled_for_cancellation_at
      )

      em.map(
        value => {

          println(value)
          assert(true)
        }
      ).recover {
        exception =>
          println(LogHelpers.getStackTraceAsString(exception))
          assert(false)
      }
    }

    it("should scheduleOrgDomainsAndEmailsForDeletionOnCancellation - failure_case_1") {

      val org_id = OrgId(5)
      val scheduled_for_cancellation_at = DateTime.parse("2020-01-01T00:00:00Z")
      val error = new Throwable("Failure while fetching domains and emails for org")
      val orgPurchasedDomainAndEmailForCancellation = OrgPurchasedDomainAndEmailForCancellation(
        org_id = org_id,
        team_id = TeamId(1),
        accountId = AccountId(1),
        purchasedDomainUuid = "1",
        domainStatus = PurchaseDomainsAndEmailsStatus.ACTIVE,
        domainName = "smartreachlabs.click",
        domainCreatedAt = DateTime.parse("2020-01-01T00:00:00Z"),
        emailSettingId = None,
        emailStatus = None,
        emailAddress = None,
        platformEmailId = None,
        platformType = PlatformType.MAILDOSO
      )

      (emailInfraDao.fetchDomainsAndEmailsForOrg(_: OrgId))
        .expects(org_id)
        .returning(Failure(error))

      val em = emailInfraService.scheduleOrgDomainsAndEmailsForDeletionOnCancellation(
        org_id = org_id,
        scheduled_for_cancellation_at = scheduled_for_cancellation_at
      )

      em.map(
        value => {

          println(value)
          assert(false)
        }
      ).recover {
        exception =>
          assert(exception.getMessage == error.getMessage)
      }
    }

    it("should scheduleOrgDomainsAndEmailsForDeletionOnCancellation - failure_case_2 - When no domains emails found for org") {

      val org_id = OrgId(5)
      val scheduled_for_cancellation_at = DateTime.parse("2020-01-01T00:00:00Z")
      val error = new Throwable("Failure while fetching domains and emails for org")
      val orgPurchasedDomainAndEmailForCancellation = OrgPurchasedDomainAndEmailForCancellation(
        org_id = org_id,
        team_id = TeamId(1),
        accountId = AccountId(1),
        purchasedDomainUuid = "1",
        domainStatus = PurchaseDomainsAndEmailsStatus.ACTIVE,
        domainName = "smartreachlabs.click",
        domainCreatedAt = DateTime.parse("2020-01-01T00:00:00Z"),
        emailSettingId = None,
        emailStatus = None,
        emailAddress = None,
        platformEmailId = None,
        platformType = PlatformType.MAILDOSO
      )

      (emailInfraDao.fetchDomainsAndEmailsForOrg(_: OrgId))
        .expects(org_id)
        .returning(Success(List()))

      val em = emailInfraService.scheduleOrgDomainsAndEmailsForDeletionOnCancellation(
        org_id = org_id,
        scheduled_for_cancellation_at = scheduled_for_cancellation_at
      )

      em.map(
        value => {

          println(value)
          assert(value.platform_email_ids == List())
          assert(value.platform_domain_uuids == List())
        }
      ).recover {
        exception =>
          println(LogHelpers.getStackTraceAsString(exception))
          assert(false)
      }
    }

    it("should scheduleOrgDomainsAndEmailsForDeletionOnCancellation - failure_case_3") {

      val org_id = OrgId(5)
      val scheduled_for_cancellation_at = DateTime.parse("2020-01-01T00:00:00Z")
      val error = new Throwable("Failure while fetching domains and emails for org")
      val orgPurchasedDomainAndEmailForCancellation = OrgPurchasedDomainAndEmailForCancellation(
        org_id = org_id,
        team_id = TeamId(1),
        accountId = AccountId(1),
        purchasedDomainUuid = "1",
        domainStatus = PurchaseDomainsAndEmailsStatus.ACTIVE,
        domainName = "smartreachlabs.click",
        domainCreatedAt = DateTime.parse("2020-01-01T00:00:00Z"),
        emailSettingId = Some(EmailSettingId(1)),
        emailStatus = None,
        emailAddress = None,
        platformEmailId = None,
        platformType = PlatformType.MAILDOSO
      )

      (emailInfraDao.fetchDomainsAndEmailsForOrg(_: OrgId))
        .expects(org_id)
        .returning(Success(List(orgPurchasedDomainAndEmailForCancellation)))

      (emailSettingDAO.checkEmailSettingExists(_: EmailSettingId, _: TeamId))
        .expects(EmailSettingId(1), TeamId(1))
        .returning(Failure(error))

      (emailInfraDao.getPlatformDomainIdsByUuids(_: List[String], _: TeamId))
        .expects(List("1"), TeamId(1))
        .returning(Failure(error))

      val em = emailInfraService.scheduleOrgDomainsAndEmailsForDeletionOnCancellation(
        org_id = org_id,
        scheduled_for_cancellation_at = scheduled_for_cancellation_at
      )

      em.map(
        value => {

          println(value)
          assert(value.platform_domain_uuids == List())
          assert(value.platform_email_ids == List())
        }
      ).recover {
        exception =>
          println(LogHelpers.getStackTraceAsString(exception))
          assert(false)
      }
    }
  }

  describe("testing save email domains function") {
    it("should save email domains") {

      // Your purchased domain forms
      val purchaseDomainsForms = List(
        PurchasedDomainForm(
          domainName = "smartreachlabs1.click",
          purchasedEmails = List(
            PurchaseEmail.Maildoso(
              first_name = "Rahul",
              last_name = "Mishra",
              email_account = "<EMAIL>",
              password = "Hello123"
            ),
            PurchaseEmail.Maildoso(
              first_name = "Rahul",
              last_name = "Mishra",
              email_account = "<EMAIL>",
              password = "Hello123"
            ),
            PurchaseEmail.Maildoso(
              first_name = "Rahul",
              last_name = "Mishra",
              email_account = "<EMAIL>",
              password = "Hello123"
            ),
            PurchaseEmail.Maildoso(
              first_name = "Rahul",
              last_name = "Mishra",
              email_account = "<EMAIL>",
              password = "Hello123"
            )
          ),
          domainRedirectTo = Some("https://smartreach.io")
        ),
        PurchasedDomainForm(
          domainName = "smartreachlabs2.click",
          purchasedEmails = List(
            PurchaseEmail.Maildoso(
              first_name = "Rahul",
              last_name = "Mishra",
              email_account = "<EMAIL>",
              password = "Hello123"
            ),
            PurchaseEmail.Maildoso(
              first_name = "Rahul",
              last_name = "Mishra",
              email_account = "<EMAIL>",
              password = "Hello123"
            ),
            PurchaseEmail.Maildoso(
              first_name = "Rahul",
              last_name = "Mishra",
              email_account = "<EMAIL>",
              password = "Hello123"
            ),
            PurchaseEmail.Maildoso(
              first_name = "Rahul",
              last_name = "Mishra",
              email_account = "<EMAIL>",
              password = "Hello123"
            )
          ),
          domainRedirectTo = Some("https://smartreach.io")
        ),
        PurchasedDomainForm(
          domainName = "smartreachlabs3.click",
          purchasedEmails = List(
            PurchaseEmail.Maildoso(
              first_name = "Rahul",
              last_name = "Mishra",
              email_account = "<EMAIL>",
              password = "Hello123"
            ),
            PurchaseEmail.Maildoso(
              first_name = "Rahul",
              last_name = "Mishra",
              email_account = "<EMAIL>",
              password = "Hello123"
            ),
            PurchaseEmail.Maildoso(
              first_name = "Rahul",
              last_name = "Mishra",
              email_account = "<EMAIL>",
              password = "Hello123"
            ),
            PurchaseEmail.Maildoso(
              first_name = "Rahul",
              last_name = "Mishra",
              email_account = "<EMAIL>",
              password = "Hello123"
            ),
            PurchaseEmail.Maildoso(
              first_name = "Rahul",
              last_name = "Mishra",
              email_account = "<EMAIL>",
              password = "Hello123"
            )
          ),
          domainRedirectTo = Some("https://smartreach.io")
        ),
        PurchasedDomainForm(
          domainName = "smartreachlabs4.click",
          purchasedEmails = List(
            PurchaseEmail.Maildoso(
              first_name = "Rahul",
              last_name = "Mishra",
              email_account = "<EMAIL>",
              password = "Hello123"
            ),
            PurchaseEmail.Maildoso(
              first_name = "Rahul",
              last_name = "Mishra",
              email_account = "<EMAIL>",
              password = "Hello123"
            ),
            PurchaseEmail.Maildoso(
              first_name = "Rahul",
              last_name = "Mishra",
              email_account = "<EMAIL>",
              password = "Hello123"
            ),
            PurchaseEmail.Maildoso(
              first_name = "Rahul",
              last_name = "Mishra",
              email_account = "<EMAIL>",
              password = "Hello123"
            ),
            PurchaseEmail.Maildoso(
              first_name = "Rahul",
              last_name = "Mishra",
              email_account = "<EMAIL>",
              password = "Hello123"
            )
          ),
          domainRedirectTo = Some("https://smartreach.io")
        ),
        PurchasedDomainForm(
          domainName = "smartreachlabs5.click",
          purchasedEmails = List(
            PurchaseEmail.Maildoso(
              first_name = "Rahul",
              last_name = "Mishra",
              email_account = "<EMAIL>",
              password = "Hello123"
            ),
            PurchaseEmail.Maildoso(
              first_name = "Rahul",
              last_name = "Mishra",
              email_account = "<EMAIL>",
              password = "Hello123"
            ),
            PurchaseEmail.Maildoso(
              first_name = "Rahul",
              last_name = "Mishra",
              email_account = "<EMAIL>",
              password = "Hello123"
            ),
            PurchaseEmail.Maildoso(
              first_name = "Rahul",
              last_name = "Mishra",
              email_account = "<EMAIL>",
              password = "Hello123"
            ),
            PurchaseEmail.Maildoso(
              first_name = "Rahul",
              last_name = "Mishra",
              email_account = "<EMAIL>",
              password = "Hello123"
            )
          ),
          domainRedirectTo = Some("https://smartreach.io")
        ),
        PurchasedDomainForm(
          domainName = "smartreachlabs6.click",
          purchasedEmails = List(
            PurchaseEmail.Maildoso(
              first_name = "Rahul",
              last_name = "Mishra",
              email_account = "<EMAIL>",
              password = "Hello123"
            ),
            PurchaseEmail.Maildoso(
              first_name = "Rahul",
              last_name = "Mishra",
              email_account = "<EMAIL>",
              password = "Hello123"
            ),
            PurchaseEmail.Maildoso(
              first_name = "Rahul",
              last_name = "Mishra",
              email_account = "<EMAIL>",
              password = "Hello123"
            ),
            PurchaseEmail.Maildoso(
              first_name = "Rahul",
              last_name = "Mishra",
              email_account = "<EMAIL>",
              password = "Hello123"
            ),
            PurchaseEmail.Maildoso(
              first_name = "Rahul",
              last_name = "Mishra",
              email_account = "<EMAIL>",
              password = "Hello123"
            )
          ),
          domainRedirectTo = Some("https://smartreach.io")
        ),
        PurchasedDomainForm(
          domainName = "smartreachlabs7.click",
          purchasedEmails = List(
            PurchaseEmail.Maildoso(
              first_name = "Rahul",
              last_name = "Mishra",
              email_account = "<EMAIL>",
              password = "Hello123"
            ),
            PurchaseEmail.Maildoso(
              first_name = "Rahul",
              last_name = "Mishra",
              email_account = "<EMAIL>",
              password = "Hello123"
            ),
            PurchaseEmail.Maildoso(
              first_name = "Rahul",
              last_name = "Mishra",
              email_account = "<EMAIL>",
              password = "Hello123"
            ),
            PurchaseEmail.Maildoso(
              first_name = "Rahul",
              last_name = "Mishra",
              email_account = "<EMAIL>",
              password = "Hello123"
            ),
            PurchaseEmail.Maildoso(
              first_name = "Rahul",
              last_name = "Mishra",
              email_account = "<EMAIL>",
              password = "Hello123"
            )
          ),
          domainRedirectTo = Some("https://smartreach.io")
        ),
        PurchasedDomainForm(
          domainName = "smartreachlabs8.click",
          purchasedEmails = List(
            PurchaseEmail.Maildoso(
              first_name = "Rahul",
              last_name = "Mishra",
              email_account = "<EMAIL>",
              password = "Hello123"
            ),
            PurchaseEmail.Maildoso(
              first_name = "Rahul",
              last_name = "Mishra",
              email_account = "<EMAIL>",
              password = "Hello123"
            ),
            PurchaseEmail.Maildoso(
              first_name = "Rahul",
              last_name = "Mishra",
              email_account = "<EMAIL>",
              password = "Hello123"
            ),
            PurchaseEmail.Maildoso(
              first_name = "Rahul",
              last_name = "Mishra",
              email_account = "<EMAIL>",
              password = "Hello123"
            ),
            PurchaseEmail.Maildoso(
              first_name = "Rahul",
              last_name = "Mishra",
              email_account = "<EMAIL>",
              password = "Hello123"
            )
          ),
          domainRedirectTo = Some("https://smartreach.io")
        )
      )

      // Setup expectations before calling the service
      (planLimitService.checkEmailsAndDomainsAddonLimitReached(_: Int, _: Int, _: PlatformType, _: TeamId)(_: SRLogger))
        .expects(38, 8, PlatformType.MAILDOSO, TeamId(id = 3), Logger)
        .returning(Success(true))

      (mailDosoService.purchaseDomains(_: CreateDomainRequest)(_: ExecutionContext, _: WSClient, _: SRLogger))
        .expects(*, *, *, *)
        .returning(Future.successful(
          PurchaseDomainResponse(
            results = purchaseDomainsForms.map(d => {
              PurchaseDomainResult(
                name = d.domainName,
                result = Some(PurchaseDomainResponseResultType.SUCCESS),
                message = None,
                expire_at = None,
                id = None
              )
            }),
            task_id = "TASK-ID-AFTER-PURCHASE"
          )
        ))

      val purchasedDomain = CaptureOne[List[PurchasedDomain]]()

      (emailInfraDao.savePurchaseDomains(_:List[PurchasedDomain], _:TeamId, _: AccountId, _: EmailInfraWorkspaceType ))
        .expects(capture(purchasedDomain), TeamId(id =3), AccountId(id= 5), EmailInfraWorkspaceType.SMTP)
        .returning(Success(List()))


      val res = emailInfraService.saveDomainDetails(
        purchasedDomains = purchaseDomainsForms,
        teamId = TeamId(id = 3),
        accountId = AccountId(id = 5)
      )

      res.map(a => {


          println(s"a : ${a}")
          val pe = purchasedDomain.value
          println(s"pe : ${pe}")


          assert(true)

          val pe_to_check = purchasedDomain.value.flatMap(_.purchasedEmails.map(_.email_account))

          assert(pe_to_check == purchaseDomainsForms.flatMap(_.purchasedEmails.map(_.email_account.toLowerCase())))



        })
        .recover{case e => {

          println(s"e :: ${LogHelpers.getStackTraceAsString(e)}")

          assert(false)
        }}





    }


  }

}
