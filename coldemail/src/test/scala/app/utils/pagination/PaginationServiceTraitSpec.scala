package app.utils.pagination

import api.accounts.{TeamId, TeamMetaData}
import io.sr.billing_common.models.PlanID
import api.accounts.models.AccountId
import api.emails.EmailThreadDAO
import api.emails.dao_service.EmailThreadDAOService
import api.emails.models.{InboxType, InboxTypeData}
import api.linkedin_message_threads.LinkedinMessageThreadsDAO
import api.linkedin_messages.LinkedinMessagesDAO
import api.prospects.{ExactIdToCompareTime, InferredQueryTimeline, MailboxFolder, ThreadId, ValidatedConvReq}
import api.team.service.TeamService
import api.team_inbox.dao_service.ReplySentimentDAOService
import api.team_inbox.service.{InboxV3PaginationService, ReplySentimentUuid}
import eventframework.{ConversationObjectInboxV3, MessageObject}
import io.smartreach.esp.api.emails.IEmailAddress
import org.joda.time.DateTime
import org.scalamock.scalatest.AsyncMockFactory
import org.scalatest.funspec.AsyncFunSpec
import sr_scheduler.models.ChannelType
import utils.SRLogger
import utils.jodatimeutils.JodaTimeUtils
import utils.uuid.services.SrUuidService

import scala.util.Success

class PaginationServiceTraitSpec extends AsyncFunSpec with AsyncMockFactory {

  val replySentimentDAOService: ReplySentimentDAOService = mock[ReplySentimentDAOService]
  val teamService: TeamService = mock[TeamService]
  val srUuidService: SrUuidService = mock[SrUuidService]
  val linkedinMessageThreadsDAO: LinkedinMessageThreadsDAO = mock[LinkedinMessageThreadsDAO]
  val  emailThreadDAOService: EmailThreadDAOService = mock[EmailThreadDAOService]

  val inboxV3PaginationService = new InboxV3PaginationService(
    emailThreadDAOService = emailThreadDAOService,
    linkedinMessageThreadsDAO = linkedinMessageThreadsDAO,
    srUuidService = srUuidService,
    replySentimentDAOService = replySentimentDAOService,
    teamService = teamService
  )

  val aDate: DateTime = DateTime.now()

  val teamId: Long = 37L
  val page_size = 5
  given Logger: SRLogger = new SRLogger("tests")


  val owner_id: Long = 300101L
  val owner_name = "the owner"
  val done = false
  val snoozed = false

  val email_msg_id_start = 5001000
  val from_email: IEmailAddress = IEmailAddress(name = Some("a person"),
    email = "<EMAIL>")
  val to_emails: Seq[IEmailAddress] = Seq(IEmailAddress(name = None, email = "<EMAIL>"))
  val cc_emails: Seq[IEmailAddress] = Seq(IEmailAddress(name = None, email = "<EMAIL>"))
  val bcc_emails: Seq[IEmailAddress] = Seq(IEmailAddress(name = None, email = "<EMAIL>"))

  val subj = "Subj : Test Email"
  val body = "Body of the email"
  val body_preview = "Preview Body of the email"
  val sent_at: DateTime = DateTime.parse("2022-01-13")

  val emlMsg: MessageObject.EmailMessageObject = MessageObject.EmailMessageObject(
    uuid = Some("em_2vJVmvifsnC"),
    from_user = true,
    from = from_email,
    reply_to = None,
    to = to_emails,
    cc_emails = None,
    bcc_emails = None,
    subject = subj,
    body = body,
    body_preview = body_preview,
    sent_at = sent_at
  )

  val convObjV3: ConversationObjectInboxV3.EmailConversationObjectInboxV3 = ConversationObjectInboxV3.EmailConversationObjectInboxV3(
    uuid = "100101",
    team_id = TeamId(teamId),
    owner_id = AccountId(owner_id),
    owner_name = owner_name,
    snoozed_till = None,
    // latest_message.sent_at is used to paginate
    latest_message = emlMsg,
    latest_reply_at = Some(aDate),
    latest_sent_by_admin_at = Some(aDate),
    is_read = false,
    team_inbox_id = Some(101L),
    team_inbox_name = Some("Sales"),
    reply_sentiment_uuid = None,
    updated_at = aDate
  )

  describe("computeExactlyAt"){
    it("should computeExactlyAt with  newer_than but results are < page_size "){

      val validatedConvReq1 = ValidatedConvReq.ValidatedMailboxReqRange(
        mailboxFolderRequest = MailboxFolder.TeamInboxFolder.Prospects(esetIds = List(1L), prospect_category = None, replySentimentType = None),
        timeline = InferredQueryTimeline.Range.After(dateTime = aDate),
        pageSize = 5
      )

      val sortedConvs1 = List(
        convObjV3
        , convObjV3.copy(updated_at = aDate.minusDays(1)))
        .sortBy(_.updated_at)(JodaTimeUtils.dateTimeOrdering)


      val res1 = inboxV3PaginationService.computeExactlyAt(
        timeline = validatedConvReq1.timeline,
        page_size = validatedConvReq1.pageSize,
        sortedData = sortedConvs1
      )

      assert(res1.isEmpty)
    }

    // see the test below - the order of the convs is
    // aDate == 12-jan-22
    // [0: ("22-1-18")][1: ("22-1-17")][2: ("22-1-16")][3: ("22-1-15")][4: ("22-1-14")][5: ("22-1-13")]
    it("should return None when computeExactlyAt with more convs than page_size and newer_than because the boundary convs have different dates") {

      val validatedConvReq1 = ValidatedConvReq.ValidatedMailboxReqRange(
        mailboxFolderRequest = MailboxFolder.TeamInboxFolder.Prospects(esetIds = List(1L), prospect_category = None, replySentimentType = None),
        timeline = InferredQueryTimeline.Range.After(dateTime = aDate),
        pageSize = 5
      )

      val sortedConvs1 = List(
        convObjV3.copy(updated_at = aDate.plusDays(1))
        , convObjV3.copy(updated_at = aDate.plusDays(2))
        , convObjV3.copy(updated_at = aDate.plusDays(3))
        , convObjV3.copy(updated_at = aDate.plusDays(4))
        , convObjV3.copy(updated_at = aDate.plusDays(5))
        , convObjV3.copy(updated_at = aDate.plusDays(6)))
        .sortBy(_.updated_at)(JodaTimeUtils.dateTimeOrdering).reverse

      val res1 = inboxV3PaginationService.computeExactlyAt(
        timeline = validatedConvReq1.timeline,
        page_size = validatedConvReq1.pageSize,
        sortedData = sortedConvs1
      )

      assert(res1.isEmpty)
    }

    // see the test below - the order of the convs is
    // aDate == 12-jan-22
    // [0: ("22-1-17")][1: ("22-1-17")][2: ("22-1-16")][3: ("22-1-15")][4: ("22-1-14")][5: ("22-1-13")]
    it("should return something because the boundary convs have same date if there are more convs than page_size and newer_than") {

      val validatedConvReq1 = ValidatedConvReq.ValidatedMailboxReqRange(
        mailboxFolderRequest = MailboxFolder.TeamInboxFolder.Prospects(esetIds = List(1L), prospect_category = None, replySentimentType = None),
        timeline = InferredQueryTimeline.Range.After(dateTime = aDate),
        pageSize = 5
      )

      val sortedConvs1 = List(
        convObjV3.copy(updated_at = aDate.plusDays(1), uuid = "101")
        , convObjV3.copy(updated_at = aDate.plusDays(2), uuid = "102")
        , convObjV3.copy(updated_at = aDate.plusDays(3), uuid = "103")
        , convObjV3.copy(updated_at = aDate.plusDays(4), uuid = "104")
        , convObjV3.copy(updated_at = aDate.plusDays(5), uuid = "105")
        , convObjV3.copy(updated_at = aDate.plusDays(5), uuid = "106")
      )
        .sortBy(_.updated_at)(JodaTimeUtils.dateTimeOrdering).reverse

      val res1 = inboxV3PaginationService.computeExactlyAt(
        timeline = validatedConvReq1.timeline,
        page_size = validatedConvReq1.pageSize,
        sortedData = sortedConvs1
      )

      assert(res1.nonEmpty)
      assert(res1.get == ExactIdToCompareTime("106"))
    }

    // see the test below - the order of the convs is
    // [0: ("2022-1-11")][1: ("2022-1-10")]
    it("should return None if it's older_than but results are < page_size") {

      val validatedConvReq1 = ValidatedConvReq.ValidatedMailboxReqRange(
        mailboxFolderRequest = MailboxFolder.TeamInboxFolder.Prospects(esetIds = List(1L), prospect_category = None, replySentimentType = None),
        timeline = InferredQueryTimeline.Range.Before(dateTime = aDate),
        pageSize = 5
      )

      val sortedConvs1 = List(
        convObjV3.copy(updated_at = aDate.minusDays(1))
        , convObjV3.copy(updated_at = aDate.minusDays(2)))
        .sortBy(_.updated_at)(JodaTimeUtils.dateTimeOrdering)

      val res1 = inboxV3PaginationService.computeExactlyAt(
        timeline = validatedConvReq1.timeline,
        page_size = validatedConvReq1.pageSize,
        sortedData = sortedConvs1
      )

      assert(res1.isEmpty)
    }

    // see the test below - the order of the convs is
    // aDate == 12-jan-22
    // [0: ("22-1-11")][1: ("22-1-10")][2: ("22-1-9")][3: ("22-1-8")][4: ("22-1-7")][5: ("22-1-6)")]
    it("should return None because the boundary convs have different dates if more convs than page_size and older_than") {

      val validatedConvReq1 = ValidatedConvReq.ValidatedMailboxReqRange(
        mailboxFolderRequest = MailboxFolder.TeamInboxFolder.Prospects(esetIds = List(1L), prospect_category = None, replySentimentType = None),
        timeline = InferredQueryTimeline.Range.Before(dateTime = aDate),
        pageSize = 5
      )

      val sortedConvs1 = List(
        convObjV3.copy(updated_at = aDate.minusDays(1))
        , convObjV3.copy(updated_at = aDate.minusDays(2))
        , convObjV3.copy(updated_at = aDate.minusDays(3))
        , convObjV3.copy(updated_at = aDate.minusDays(4))
        , convObjV3.copy(updated_at = aDate.minusDays(5))
        , convObjV3.copy(updated_at = aDate.minusDays(6)))
        .sortBy(_.updated_at)(JodaTimeUtils.dateTimeOrdering).reverse

      val res1 = inboxV3PaginationService.computeExactlyAt(
        timeline = validatedConvReq1.timeline,
        page_size = validatedConvReq1.pageSize,
        sortedData = sortedConvs1
      )

      assert(res1.isEmpty)
    }

    // see the test below - the order of the convs is:
    // aDate == 12-jan-22
    // [0: ("22-1-11")][1: ("22-1-10")][2: ("22-1-9")][3: ("22-1-8")][4: ("22-1-7")][5: ("22-1-7)")]
    it("should return something because the boundary convs have same date if more convs than page_size and older_than") {

      val validatedConvReq1 = ValidatedConvReq.ValidatedMailboxReqRange(
        mailboxFolderRequest = MailboxFolder.TeamInboxFolder.Prospects(esetIds = List(1L), prospect_category = None, replySentimentType = None),
        timeline = InferredQueryTimeline.Range.Before(dateTime = aDate),
        pageSize = 5
      )

      val sortedConvs1 = List(
        convObjV3.copy(updated_at = aDate.minusDays(1), uuid = "106")
        , convObjV3.copy(updated_at = aDate.minusDays(2), uuid = "105")
        , convObjV3.copy(updated_at = aDate.minusDays(3), uuid = "104")
        , convObjV3.copy(updated_at = aDate.minusDays(4), uuid = "103")
        , convObjV3.copy(updated_at = aDate.minusDays(5), uuid = "102")
        , convObjV3.copy(updated_at = aDate.minusDays(5), uuid = "101")
      )
        .sortBy(_.updated_at)(JodaTimeUtils.dateTimeOrdering).reverse

      val res1 = inboxV3PaginationService.computeExactlyAt(
        timeline = validatedConvReq1.timeline,
        page_size = validatedConvReq1.pageSize,
        sortedData = sortedConvs1
      )

      assert(res1.nonEmpty)
      assert(res1.get == ExactIdToCompareTime("102"))
    }

  }

  val org_id = 1L

  describe("handleGetConversationsForProspectsInboxV3New") {

    val inbox_type_data = InboxTypeData.SINGLE_DATA(
      team_inbox_id = 145
    )

    it(" should give valid response for result  <=  pagesize with no next link and prev link to conv with highest date with Before") {

      (emailThreadDAOService.getQueryAndGetConversations(
        _: Long,
        _: ValidatedConvReq,
        _: List[ReplySentimentUuid],
      )(using _: SRLogger))
        .expects(teamId, *, *, Logger)
        .returning(Success(List(
          convObjV3.copy(updated_at = aDate.minusDays(1)),
          convObjV3.copy(updated_at = aDate.minusDays(2))
        )))


      val res = inboxV3PaginationService.handleGetConversationsForProspectsInboxV3New(
        team_id = teamId,
        channelType = Some(ChannelType.EmailChannel),
        getLinkedinConversations = false,
        validatedConvReq = ValidatedConvReq.ValidatedMailboxReqRange(
          mailboxFolderRequest = MailboxFolder.TeamInboxFolder.Prospects(List(1L), None, replySentimentType = None),
          timeline = InferredQueryTimeline.Range.Before(dateTime = aDate),
          pageSize = 5
        ),
        org_plan_id = PlanID.ULTIMATE,
        show_full_inbox_v3 = true,
        org_id = org_id
      )

      assert(res.isRight)
      val convSummaryResp = res.toOption.get
      assert(convSummaryResp.links.prev.get == aDate.minusDays(1))
      assert(convSummaryResp.links.next.isEmpty)
      assert(convSummaryResp.convs.length == 2)

    }

    it(" should give valid response with After for result <=  pagesize with no prev link and next link to conv with lowest date ") {

      (emailThreadDAOService.getQueryAndGetConversations(
        _: Long,
        _: ValidatedConvReq,
        _: List[ReplySentimentUuid]
      )(using _: SRLogger))
        .expects(teamId, *, *, Logger)
        .returning(Success(List(
          convObjV3.copy(updated_at = aDate.plusDays(1)),
          convObjV3.copy(updated_at = aDate.plusDays(2))
        )))


      val res = inboxV3PaginationService.handleGetConversationsForProspectsInboxV3New(
        team_id = teamId,
        channelType = Some(ChannelType.EmailChannel),
        getLinkedinConversations = false,
        validatedConvReq = ValidatedConvReq.ValidatedMailboxReqRange(
          mailboxFolderRequest = MailboxFolder.TeamInboxFolder.Prospects(List(1L), None, replySentimentType = None),
          timeline = InferredQueryTimeline.Range.After(dateTime = aDate),
          pageSize = 5
        ),
        org_plan_id = PlanID.ULTIMATE,
        show_full_inbox_v3 = true,
        org_id = org_id
      )

      assert(res.isRight)
      val convSummaryResp = res.toOption.get
      assert(convSummaryResp.links.prev.isEmpty)
      assert(convSummaryResp.links.next.get == aDate.plusDays(1))
      assert(convSummaryResp.convs.length == 2)

    }

    it(" should give valid response with Before for result > pagesize with valid next link and prev link to conv with highest date ") {

      (emailThreadDAOService.getQueryAndGetConversations(
        _: Long,
        _: ValidatedConvReq,
        _: List[ReplySentimentUuid]
      )(using _: SRLogger))
        .expects(teamId, *, *, Logger)
        .returning(Success(List(
          convObjV3.copy(updated_at = aDate.minusDays(1)),
          convObjV3.copy(updated_at = aDate.minusDays(2)),
          convObjV3.copy(updated_at = aDate.minusDays(3)),
          convObjV3.copy(updated_at = aDate.minusDays(4)),
          convObjV3.copy(updated_at = aDate.minusDays(5)),
          convObjV3.copy(updated_at = aDate.minusDays(6))
        )))

      val res = inboxV3PaginationService.handleGetConversationsForProspectsInboxV3New(
        team_id = teamId,
        channelType = Some(ChannelType.EmailChannel),
        getLinkedinConversations = false,
        validatedConvReq = ValidatedConvReq.ValidatedMailboxReqRange(
          mailboxFolderRequest = MailboxFolder.TeamInboxFolder.Prospects(List(1L), None, replySentimentType = None),
          timeline = InferredQueryTimeline.Range.Before(dateTime = aDate),
          pageSize = 5
        ),
        org_plan_id = PlanID.ULTIMATE,
        show_full_inbox_v3 = true,
        org_id = org_id
      )

      assert(res.isRight)
      val convSummaryResp = res.toOption.get
      assert(convSummaryResp.links.prev.get == aDate.minusDays(1))
      assert(convSummaryResp.links.next.get == aDate.minusDays(5))
      assert(convSummaryResp.convs.length == 5)

    }

    it(" should give valid response  with After for result > pagesize with valid prev link and next link to conv with lowest date "){

      (emailThreadDAOService.getQueryAndGetConversations(
        _: Long,
        _: ValidatedConvReq,
        _: List[ReplySentimentUuid]
      )(using _: SRLogger))
        .expects(teamId, *, *, Logger)
        .returning(Success(List(
          convObjV3.copy(updated_at = aDate.plusDays(1)),
          convObjV3.copy(updated_at = aDate.plusDays(2)),
          convObjV3.copy(updated_at = aDate.plusDays(3)),
          convObjV3.copy(updated_at = aDate.plusDays(4)),
          convObjV3.copy(updated_at = aDate.plusDays(5)),
          convObjV3.copy(updated_at = aDate.plusDays(6))
        )))

      val res = inboxV3PaginationService.handleGetConversationsForProspectsInboxV3New(
        team_id = teamId,
        channelType = Some(ChannelType.EmailChannel),
        getLinkedinConversations = false,
        validatedConvReq = ValidatedConvReq.ValidatedMailboxReqRange(
          mailboxFolderRequest = MailboxFolder.TeamInboxFolder.Prospects(List(1L), None, replySentimentType = None),
          timeline = InferredQueryTimeline.Range.After(dateTime = aDate),
          pageSize = 5
        ),
        org_plan_id = PlanID.ULTIMATE,
        show_full_inbox_v3 = true,
        org_id = org_id
      )

      assert(res.isRight)
      val convSummaryResp = res.toOption.get
      assert(convSummaryResp.links.prev.get == aDate.plusDays(5))
      assert(convSummaryResp.links.next.get == aDate.plusDays(1))
      assert(convSummaryResp.convs.length == 5)

    }

    it("should give valid response with Before with duplicates in boundary for result > pagesize with valid next link and prev link to conv with highest date ") {

      (emailThreadDAOService.getQueryAndGetConversations(
        _: Long,
        _: ValidatedConvReq,
        _: List[ReplySentimentUuid],
      )(using _: SRLogger))
        .expects(teamId, *, *, Logger)
        .returning(Success(List(
          convObjV3.copy(updated_at = aDate.minusDays(1)),
          convObjV3.copy(updated_at = aDate.minusDays(2)),
          convObjV3.copy(updated_at = aDate.minusDays(3)),
          convObjV3.copy(updated_at = aDate.minusDays(4)),
          convObjV3.copy(updated_at = aDate.minusDays(5)),
          convObjV3.copy(updated_at = aDate.minusDays(5))
        )))

      (emailThreadDAOService.getQueryAndGetConversations(
        _: Long,
        _: ValidatedConvReq,
        _: List[ReplySentimentUuid]
      )(using _: SRLogger))
        .expects(teamId, *, *, Logger)
        .returning(Success(List(
          convObjV3.copy(updated_at = aDate.minusDays(5)),
          convObjV3.copy(updated_at = aDate.minusDays(5)),
          convObjV3.copy(updated_at = aDate.minusDays(5))
        )))

      (srUuidService.getThreadIdFromUuid(
        _: String,
        _: TeamId
      )(
        _: SRLogger
      ))
        .expects(*, *, *)
        .returning(Success(ThreadId.EmailThreadId(106)))

      val res = inboxV3PaginationService.handleGetConversationsForProspectsInboxV3New(
        team_id = teamId,
        channelType = Some(ChannelType.EmailChannel),
        getLinkedinConversations = false,
        validatedConvReq = ValidatedConvReq.ValidatedMailboxReqRange(
          mailboxFolderRequest = MailboxFolder.TeamInboxFolder.Prospects(List(1L), None, replySentimentType = None),
          timeline = InferredQueryTimeline.Range.Before(dateTime = aDate),
          pageSize = 5
        ),
        org_plan_id = PlanID.ULTIMATE,
        show_full_inbox_v3 = true,
        org_id = org_id
      )

      assert(res.isRight)
      val convSummaryResp = res.toOption.get
      assert(convSummaryResp.links.prev.get == aDate.minusDays(1))
      assert(convSummaryResp.links.next.get == aDate.minusDays(5))
      assert(convSummaryResp.convs.length == 7)
    }


    it(" should give valid response with After with duplicates in boundary for result > pagesize with valid prev link and next link to conv with lowest date ") {

      (emailThreadDAOService.getQueryAndGetConversations(
        _: Long,
        _: ValidatedConvReq,
        _: List[ReplySentimentUuid],
      )(using _: SRLogger))
        .expects(teamId, *, *, Logger)
        .returning(Success(List(
          convObjV3.copy(updated_at = aDate.plusDays(1)),
          convObjV3.copy(updated_at = aDate.plusDays(2)),
          convObjV3.copy(updated_at = aDate.plusDays(3)),
          convObjV3.copy(updated_at = aDate.plusDays(4)),
          convObjV3.copy(updated_at = aDate.plusDays(5)),
          convObjV3.copy(updated_at = aDate.plusDays(5))
        )))

      (emailThreadDAOService.getQueryAndGetConversations(
        _: Long,
        _: ValidatedConvReq,
        _: List[ReplySentimentUuid]
      )(using _: SRLogger))
        .expects(teamId, *, *, Logger)
        .returning(Success(List(
          convObjV3.copy(updated_at = aDate.plusDays(5)),
          convObjV3.copy(updated_at = aDate.plusDays(5)),
          convObjV3.copy(updated_at = aDate.plusDays(5))
        )))

      (srUuidService.getThreadIdFromUuid(
        _: String,
        _: TeamId
      )(
        _: SRLogger
      ))
        .expects(*, *, *)
        .returning(Success(ThreadId.EmailThreadId(106)))

      val res = inboxV3PaginationService.handleGetConversationsForProspectsInboxV3New(
        team_id = teamId,
        channelType = Some(ChannelType.EmailChannel),
        getLinkedinConversations = false,
        validatedConvReq = ValidatedConvReq.ValidatedMailboxReqRange(
          mailboxFolderRequest = MailboxFolder.TeamInboxFolder.Prospects(List(1L), None, replySentimentType = None),
          timeline = InferredQueryTimeline.Range.After(dateTime = aDate),
          pageSize = 5
        ),
        org_plan_id = PlanID.ULTIMATE,
        show_full_inbox_v3 = true,
        org_id = org_id
      )

      assert(res.isRight)
      val convSummaryResp = res.toOption.get
      assert(convSummaryResp.links.prev.get == aDate.plusDays(5))
      assert(convSummaryResp.links.next.get == aDate.plusDays(1))
      assert(convSummaryResp.convs.length == 7)

    }

    it(" should give all conversations fetched from db and show_upgrade_inbox_prompt false with can_show_whole_inbox true for trial if all conv are within last 15 days"){

      (emailThreadDAOService.getQueryAndGetConversations(
        _: Long,
        _: ValidatedConvReq,
        _: List[ReplySentimentUuid],
      )(using _: SRLogger))
        .expects(teamId, *, *, Logger)
        .returning(Success(List(
          convObjV3.copy(updated_at = aDate.plusDays(1)),
          convObjV3.copy(updated_at = aDate.plusDays(2)),
          convObjV3.copy(updated_at = aDate.plusDays(3)),
          convObjV3.copy(updated_at = aDate.plusDays(4)),
          convObjV3.copy(updated_at = aDate.plusDays(5)),
          convObjV3.copy(updated_at = aDate.plusDays(5))
        )))

      (emailThreadDAOService.getQueryAndGetConversations(
        _: Long,
        _: ValidatedConvReq,
        _: List[ReplySentimentUuid]
      )(using _: SRLogger))
        .expects(teamId, *, *, Logger)
        .returning(Success(List(
          convObjV3.copy(updated_at = aDate.plusDays(5)),
          convObjV3.copy(updated_at = aDate.plusDays(5)),
          convObjV3.copy(updated_at = aDate.plusDays(5))
        )))

      (srUuidService.getThreadIdFromUuid(
        _: String,
        _: TeamId
      )(
        _: SRLogger
      ))
        .expects(*, *, *)
        .returning(Success(ThreadId.EmailThreadId(106)))

      val res = inboxV3PaginationService.handleGetConversationsForProspectsInboxV3New(
        team_id = teamId,
        channelType = Some(ChannelType.EmailChannel),
        getLinkedinConversations = false,
        validatedConvReq = ValidatedConvReq.ValidatedMailboxReqRange(
          mailboxFolderRequest = MailboxFolder.TeamInboxFolder.SentFolder(List(1L), replySentimentType = None),
          timeline = InferredQueryTimeline.Range.After(dateTime = aDate),
          pageSize = 5
        ),
        org_plan_id = PlanID.TRIAL,
        show_full_inbox_v3 = true,
        org_id = org_id
      )

      assert(res.isRight)
      val convSummaryResp = res.toOption.get
      assert(convSummaryResp.links.prev.get == aDate.plusDays(5))
      assert(convSummaryResp.links.next.get == aDate.plusDays(1))
      assert(convSummaryResp.convs.length == 7)

    }


    it("should give conversations fetched from db within 15 days only and show_upgrade_inbox_prompt true with can_show_whole_inbox false for trial") {

      val today = DateTime.now()

      (emailThreadDAOService.getQueryAndGetConversations(
        _: Long,
        _: ValidatedConvReq,
        _: List[ReplySentimentUuid],
      )(using _: SRLogger))
        .expects(teamId, *, *, Logger)
        .returning(Success(List(
          convObjV3.copy(updated_at = today.minusDays(1)),
          convObjV3.copy(updated_at = today.minusDays(2)),
          convObjV3.copy(updated_at = today.minusDays(23))
        )))


      val res = inboxV3PaginationService.handleGetConversationsForProspectsInboxV3New(
        team_id = teamId,
        channelType = Some(ChannelType.EmailChannel),
        getLinkedinConversations = false,
        validatedConvReq = ValidatedConvReq.ValidatedMailboxReqRange(
          mailboxFolderRequest = MailboxFolder.TeamInboxFolder.SentFolder(List(1L), replySentimentType = None),
          timeline = InferredQueryTimeline.Range.Before(dateTime = today),
          pageSize = 5
        ),
        org_plan_id = PlanID.TRIAL,
        show_full_inbox_v3 = false,
        org_id = org_id
      )

      assert(res.isRight)
      val convSummaryResp = res.toOption.get
      assert(convSummaryResp.links.prev.isEmpty)
      assert(convSummaryResp.links.next.isEmpty)
      assert(convSummaryResp.convs.length == 2)
    }


  }

}
