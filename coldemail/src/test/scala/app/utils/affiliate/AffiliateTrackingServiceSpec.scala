package app.utils.affiliate

import org.apache.pekko.actor.ActorSystem
import api.AppConfig
import api.accounts.AccountService
import api.accounts.dao.{OrganizationBillingDAO, OrganizationDAO}
import api.accounts.models.{AccountId, OrgId}
import org.scalamock.scalatest.MockFactory
import org.scalatest.funspec.AnyFunSpec
import play.api.mvc.{<PERSON><PERSON>, <PERSON>}
import utils.SRLogger
import utils.affiliate.{Admitad, AffiliateDAO, AffiliateTrackerAndCookieValue, AffiliateTrackingService, FirstPromoter, FirstPromoterReferrerAccountDetails}
import utils.email_notification.service.EmailNotificationService
import utils.testapp.TestAppExecutionContext

import java.sql.SQLException
import scala.concurrent.ExecutionContext
import scala.util.{Failure, Success}


class AffiliateTrackingServiceSpec extends AnyFunSpec with MockFactory {

  implicit lazy val logger: SRLogger = new SRLogger("AffiliateTrackingServiceSpec")

  implicit lazy val system: ActorSystem = TestAppExecutionContext.actorSystem
  implicit lazy val ec: ExecutionContext = system.dispatcher

  val organizationBillingDAO: OrganizationBillingDAO = mock[OrganizationBillingDAO]
  val firstPromoter: FirstPromoter = mock[FirstPromoter]
  val organizationDAO: OrganizationDAO = mock[OrganizationDAO]
  val admitad: Admitad = mock[Admitad]
  val accountService: AccountService = mock[AccountService]
  val affiliateDAO: AffiliateDAO = mock[AffiliateDAO]
  val emailNotificationService: EmailNotificationService = mock[EmailNotificationService]

  val affiliateTrackingService = new AffiliateTrackingService(
    organizationBillingDAO = organizationBillingDAO,
    firstPromoter = firstPromoter,
    organizationDAO = organizationDAO,
    admitad = admitad,
    accountService = accountService,
    affiliateDAO = affiliateDAO,
    emailNotificationService = emailNotificationService,
  )

  val referred_org_id_5 = 5

  val referrer_account_id_2 = 2
  val referrer_email = "<EMAIL>"

  val domainOpt_smartreach = Some(".smartreach.io")

  val fpromTrackCookieValue = "3d17029b-6bfa-4ee4-8e37-041492fd135c"

  val fpromCodeCookieValue = "_r_vivek57"
  val fpromCodeCookieInvalidValueLessThan3Chars = "_r"
  val fpromCodeCookieValueInvalidRefId = "_r_xyz"


  val fpromTrackCookie = Cookie(
    name = AppConfig.firstpromoterCookieName,
    value = fpromTrackCookieValue,
    domain = domainOpt_smartreach,
    httpOnly = false
  )

  val fpromCodeCookie = Cookie(
    name = AppConfig.firstpromoterReferIdCookieName,
    value = fpromCodeCookieValue,
    domain = domainOpt_smartreach,
    httpOnly = false
  )

  val fpromCodeCookieWithInvalidValueLessThan3Chars = Cookie(
    name = AppConfig.firstpromoterReferIdCookieName,
    value = fpromCodeCookieInvalidValueLessThan3Chars,
    domain = domainOpt_smartreach,
    httpOnly = false
  )

  val fpromCodeCookieWithInvalidRefIdValue = Cookie(
    name = AppConfig.firstpromoterReferIdCookieName,
    value = fpromCodeCookieValueInvalidRefId,
    domain = domainOpt_smartreach,
    httpOnly = false
  )


  describe("Test trackSignup with getFirstPromoterAffiliate") {

    it("should return None if firstPromoterCookie is not Defined.") {

      val cookies = Cookies(Seq(fpromCodeCookie))

      val affiliateTrackerAndCookieValue = affiliateTrackingService.getFirstPromoterAffiliate(
        orgId = OrgId(id = referred_org_id_5),
        cookies = cookies,
        logger = logger
      )

      assert(affiliateTrackerAndCookieValue.isEmpty)
    }

    it(
      "should return None for firstPromoterReferrerAccountDetails field if firstpromoterReferIdCookie is not Defined."
    ) {

      val cookies = Cookies(Seq(fpromTrackCookie))

      val affiliateTrackerAndCookieValue = affiliateTrackingService.getFirstPromoterAffiliate(
        orgId = OrgId(id = referred_org_id_5),
        cookies = cookies,
        logger = logger
      )

      val someAffiliateTrackerAndCookieValue = Some(
        AffiliateTrackerAndCookieValue(
          affiliateTracker = firstPromoter,
          cookieValue = fpromTrackCookieValue,
          firstPromoterReferrerAccountDetails = None,
          admitadGclidCookieValue = None
        )
      )

      assert(affiliateTrackerAndCookieValue == someAffiliateTrackerAndCookieValue)
    }

    it(
      "should return None for firstPromoterReferrerAccountDetails field if firstpromoterReferIdCookie value len is < 3."
    ) {

      val cookies = Cookies(Seq(fpromTrackCookie, fpromCodeCookieWithInvalidValueLessThan3Chars))

      val affiliateTrackerAndCookieValue = affiliateTrackingService.getFirstPromoterAffiliate(
        orgId = OrgId(id = referred_org_id_5),
        cookies = cookies,
        logger = logger
      )

      val someAffiliateTrackerAndCookieValue = Some(
        AffiliateTrackerAndCookieValue(
          affiliateTracker = firstPromoter,
          cookieValue = fpromTrackCookieValue,
          firstPromoterReferrerAccountDetails = None,
          admitadGclidCookieValue = None
        )
      )

      assert(affiliateTrackerAndCookieValue == someAffiliateTrackerAndCookieValue)
    }

    it(
      "should return None for firstPromoterReferrerAccountDetails field if affiliateDAO.getAccountDetailsByRefId fails."
    ) {

      val cookies = Cookies(Seq(fpromTrackCookie, fpromCodeCookie))

      (affiliateDAO.getAccountDetailsByRefId)
        .expects(fpromCodeCookieValue.substring(3))
        .returning(Failure(new SQLException))

      val affiliateTrackerAndCookieValue = affiliateTrackingService.getFirstPromoterAffiliate(
        orgId = OrgId(id = referred_org_id_5),
        cookies = cookies,
        logger = logger
      )

      val someAffiliateTrackerAndCookieValue = Some(
        AffiliateTrackerAndCookieValue(
          affiliateTracker = firstPromoter,
          cookieValue = fpromTrackCookieValue,
          firstPromoterReferrerAccountDetails = None,
          admitadGclidCookieValue = None
        )
      )

      assert(affiliateTrackerAndCookieValue == someAffiliateTrackerAndCookieValue)
    }

    it(
      "should return None for firstPromoterReferrerAccountDetails field if ref_id value doesn't match with any promoter."
    ) {

      val cookies = Cookies(Seq(fpromTrackCookie, fpromCodeCookieWithInvalidRefIdValue))

      (affiliateDAO.getAccountDetailsByRefId)
        .expects(fpromCodeCookieValueInvalidRefId.substring(3))
        .returning(Success(None))

      val affiliateTrackerAndCookieValue = affiliateTrackingService.getFirstPromoterAffiliate(
        orgId = OrgId(id = referred_org_id_5),
        cookies = cookies,
        logger = logger
      )

      val someAffiliateTrackerAndCookieValue = Some(
        AffiliateTrackerAndCookieValue(
          affiliateTracker = firstPromoter,
          cookieValue = fpromTrackCookieValue,
          firstPromoterReferrerAccountDetails = None,
          admitadGclidCookieValue = None
        )
      )

      assert(affiliateTrackerAndCookieValue == someAffiliateTrackerAndCookieValue)
    }

    it(
      "should return firstPromoterReferrerAccountDetails if ref_id value match with any promoter."
    ) {

      val cookies = Cookies(Seq(fpromTrackCookie, fpromCodeCookie))

      val someFirstPromoterReferrerAccountDetails = Some(
        FirstPromoterReferrerAccountDetails(
          accountId = AccountId(id = referrer_account_id_2), email = referrer_email
        )
      )

      (affiliateDAO.getAccountDetailsByRefId)
        .expects(fpromCodeCookieValue.substring(3))
        .returning(Success(someFirstPromoterReferrerAccountDetails))

      val affiliateTrackerAndCookieValue = affiliateTrackingService.getFirstPromoterAffiliate(
        orgId = OrgId(id = referred_org_id_5),
        cookies = cookies,
        logger = logger
      )

      val someAffiliateTrackerAndCookieValue = Some(
        AffiliateTrackerAndCookieValue(
          affiliateTracker = firstPromoter,
          cookieValue = fpromTrackCookieValue,
          firstPromoterReferrerAccountDetails = someFirstPromoterReferrerAccountDetails,
          admitadGclidCookieValue = None
        )
      )

      assert(affiliateTrackerAndCookieValue == someAffiliateTrackerAndCookieValue)
    }
  }
}
