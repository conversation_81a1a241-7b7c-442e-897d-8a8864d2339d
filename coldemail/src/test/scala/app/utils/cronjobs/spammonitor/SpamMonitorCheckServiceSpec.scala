package app.utils.cronjobs.spammonitor


import org.scalamock.matchers.ArgCapture.CaptureOne
import org.scalamock.scalatest.MockFactory
import org.scalatest.flatspec.AnyFlatSpec
import utils.SRLogger
import utils.cronjobs.spammonitor.{ActiveCampaignService, EmailDeliveryAnalysisRecord, EmailDeliveryAnalysisService, EmailSendStatus, SpamMonitorCheckService}

import scala.util.{Success, Try}


class SpamMonitorCheckServiceSpec
  extends AnyFlatSpec
  with MockFactory {

  /*
  it should "test case" in {

    val activeCampaignService = mock[ActiveCampaignService]
    (activeCampaignService.getActiveCampaignsAcrossAllAccounts)
      .expects()
      .returning(
        //List[Long](3, 5, 7, 11, 13)
        List[ActiveCampaign] (
          ActiveCampaign(campaign_id = 1, team_id = 1),
          ActiveCampaign(campaign_id = 2, team_id = 1),
          ActiveCampaign(campaign_id = 3, team_id = 2),
          ActiveCampaign(campaign_id = 4, team_id = 2)    )
      )

    val dummyCampaignForBounceCheck = CampaignForBounceCheck(
      id = 3,
      previousCheckForCampaignAt = DateTime.now.minusDays(10),
      emailBatchForChecks = List(
        EmailForBounceCheck(
          id = 4,
          sendStatus = EmailBounceSendStatus.BadIp
        ),
        EmailForBounceCheck(
          id = 2,
          sendStatus = EmailBounceSendStatus.SenderNotAvailable
        ),
        EmailForBounceCheck(
          id = 3,
          sendStatus = EmailBounceSendStatus.InvalidEmail
        )
      )
    )


    (activeCampaignService.getCampaignForBounceCheck)
      .expects(3)
      .returning(
        dummyActiveCampaigns
      )


    val res = activeCampaignService
      .getActiveCampaignsAcrossAllAccounts

    assert(
      res.contains(5)
    )

//    assert(
//      res.contains(50)
//    )

    val res1 = activeCampaignService.getCampaignForBounceCheck(3)
    assert(res1.emailBatchForChecks.head.id == 4)

    assert(
      res1.emailBatchForChecks.head.sendStatus == EmailBounceSendStatus.BadIp
    )

  }



  it should "test case 1" in {

    val activeCampaignService = mock[ActiveCampaignService]
//    (activeCampaignService.getActiveCampaignsAcrossAllAccounts)
//      .expects()
//      .returning(
//        List[Long](3, 5, 7, 11, 13)
//      )

    val dummyCampaignForBounceCheck = CampaignForBounceCheck(
      id = 3,
      previousCheckForCampaignAt = DateTime.now.minusDays(10),
      emailBatchForChecks = List(
        EmailForBounceCheck(
          id = 4,
          sendStatus = EmailBounceSendStatus.BadIp
        ),
        EmailForBounceCheck(
          id = 2,
          sendStatus = EmailBounceSendStatus.SenderNotAvailable
        ),
        EmailForBounceCheck(
          id = 3,
          sendStatus = EmailBounceSendStatus.InvalidEmail
        )
      )
    )

    (activeCampaignService.getCampaignForBounceCheck)
      .expects(3)
      .returning(
        dummyCampaignForBounceCheck
      )

    ////////


     val monitorService = new SpamMonitorCheckService(
       activeCampaignService = activeCampaignService
     )

    val res = monitorService.runBounceCheckOnCampaign(3)


    assert(
      res.contains(BounceCheckResult.TooCloseToPreviousMonitorRun)
    )


  }
  */


  val campaign_id: Long = 100101
  val team_id: Long = 1000101

  val dummyEmailSendStatus = EmailSendStatus(ev_id = 1,
    checked_via_tool= "ocr",
    reason = "deliverable",
    email_bounce_type = None,
    campaign_id = campaign_id,
    prospect_id = 100101,
    team_id = team_id)

  given logger: SRLogger = new SRLogger("Unit test")


  "runBounceCheckOnCampaign1 " should "create an analysis record and update the DB " in {
    val activeCampaignService = mock[ActiveCampaignService]
    val emailDeliveryAnalysisService: EmailDeliveryAnalysisService = mock[EmailDeliveryAnalysisService]

    val monitorService = new SpamMonitorCheckService(
      activeCampaignService = activeCampaignService,
      emailDeliveryAnalysisService = emailDeliveryAnalysisService
    )

    // NOTE: using MacWire to construct the S.U.T (System Under Testing)
//    val monitorService = wire[SpamMonitorCheckService]


    (activeCampaignService.getEmailSendResultForBounceCheck(_: Long, _: Long) (using _: SRLogger))
      .expects( campaign_id,  team_id, *)
      .returning(
        //List[Long](3, 5, 7, 11, 13)
        Success( List[EmailSendStatus] (
          dummyEmailSendStatus,
          dummyEmailSendStatus.copy(ev_id = 2, prospect_id = 100102),
          dummyEmailSendStatus.copy(ev_id = 3, prospect_id = 100103, reason = "risky"),
          dummyEmailSendStatus.copy(ev_id = 4, prospect_id = 100104, reason = "risky"),
          dummyEmailSendStatus.copy(ev_id = 5, prospect_id = 100105, reason = "unknown"),
          dummyEmailSendStatus.copy(ev_id = 6, prospect_id = 100106, reason = "unknown"),
          dummyEmailSendStatus.copy(ev_id = 7, prospect_id = 100107, reason = "risky"),
          dummyEmailSendStatus.copy(ev_id = 8, prospect_id = 100108, reason = "unknown"),
          dummyEmailSendStatus.copy(ev_id = 9, prospect_id = 100109, reason = "unknown"),
          dummyEmailSendStatus.copy(ev_id =10, prospect_id = 100110, reason = "unknown")
        ))
      )

//    (emailDeliveryAnalysisService.createEmailValidationAnalysisRecord)
//      .expects(EmailDeliveryAnalysisRecord(-1,3,1,1,6,6,0,0,0,0,0,0,2,2,2))
//      .returning(Success(-10)
//      )
    val c1 = CaptureOne[EmailDeliveryAnalysisRecord]()

    (emailDeliveryAnalysisService.createEmailValidationAnalysisRecord)
      .expects(capture(c1))
      .returning(Success(-10)
      )

//    (emailDeliveryAnalysisService.createEmailValidationAnalysisRecord)
//      .expects(capture(c1)) once()


    (emailDeliveryAnalysisService.updateEmailValidationWithAnalysisId)
      .expects(*, *)
//      .returning(Success(-10)
//      )

    monitorService.runBounceCheckOnCampaign1(
      activeCampaign = campaign_id, teamId = team_id,
      logger = logger
    )

    //println(s"c1val 2: ${c1.value}")
    val emailDeliveryAnalysisRecord = c1.value
    assert(emailDeliveryAnalysisRecord.validation_start_id == 1)
    assert(emailDeliveryAnalysisRecord.validation_end_id == 10)

    assert(emailDeliveryAnalysisRecord.total_deliverable == 2 )
    assert(emailDeliveryAnalysisRecord.total_risky == 3)
    assert(emailDeliveryAnalysisRecord.total_unknown == 5)

    assert(emailDeliveryAnalysisRecord.n_spam_deliverable == 0)
    assert(emailDeliveryAnalysisRecord.n_spam_risky == 0)
    assert(emailDeliveryAnalysisRecord.n_spam_unknown == 0)

    /*
    campaign_id = 10101,
      prospect_id = 100101,
      team_id = 1000101
     */

    assert(emailDeliveryAnalysisRecord.campaign_id == campaign_id)
    assert(emailDeliveryAnalysisRecord.team_id == team_id)

    assert(!emailDeliveryAnalysisRecord.should_pause_unknown)
    assert(!emailDeliveryAnalysisRecord.should_pause_risky)
    assert(!emailDeliveryAnalysisRecord.should_pause_deliverable)

  }

  "runBounceCheckOnCampaign1 " should " have 1 spam count in risky " in {
    val activeCampaignService = mock[ActiveCampaignService]
    val emailDeliveryAnalysisService: EmailDeliveryAnalysisService = mock[EmailDeliveryAnalysisService]
    val monitorService = new SpamMonitorCheckService(
      activeCampaignService = activeCampaignService,
      emailDeliveryAnalysisService = emailDeliveryAnalysisService
    )


    (activeCampaignService.getEmailSendResultForBounceCheck (_: Long, _: Long) (using _: SRLogger))
      .expects( campaign_id,  team_id, *)
      .returning(
        //List[Long](3, 5, 7, 11, 13)
        Success( List[EmailSendStatus] (
          dummyEmailSendStatus,
          dummyEmailSendStatus.copy(ev_id = 2, prospect_id = 100102),
          dummyEmailSendStatus.copy(ev_id = 3, prospect_id = 100103, reason = "risky", email_bounce_type = Some("spam")),
          dummyEmailSendStatus.copy(ev_id = 4, prospect_id = 100104, reason = "risky"),
          dummyEmailSendStatus.copy(ev_id = 5, prospect_id = 100105, reason = "unknown"),
          dummyEmailSendStatus.copy(ev_id = 6, prospect_id = 100106, reason = "unknown"),
          dummyEmailSendStatus.copy(ev_id = 7, prospect_id = 100107, reason = "risky"),
          dummyEmailSendStatus.copy(ev_id = 8, prospect_id = 100108, reason = "unknown"),
          dummyEmailSendStatus.copy(ev_id = 9, prospect_id = 100109, reason = "unknown"),
          dummyEmailSendStatus.copy(ev_id =10, prospect_id = 100110, reason = "unknown")
        ))
      )


    val c1 = CaptureOne[EmailDeliveryAnalysisRecord]()

    (emailDeliveryAnalysisService.createEmailValidationAnalysisRecord)
      .expects(capture(c1))
      .returning(Success(-10)
      )

    //    (emailDeliveryAnalysisService.createEmailValidationAnalysisRecord)
    //      .expects(capture(c1)) once()


    (emailDeliveryAnalysisService.updateEmailValidationWithAnalysisId)
      .expects(*, *)
//      .returning(Success(-10)
//      )

    monitorService.runBounceCheckOnCampaign1(
      activeCampaign = campaign_id, teamId = team_id, logger = logger
    )

    //println(s"c1val 2: ${c1.value}")
    val emailDeliveryAnalysisRecord = c1.value
    assert(emailDeliveryAnalysisRecord.validation_start_id == 1)
    assert(emailDeliveryAnalysisRecord.validation_end_id == 10)

    assert(emailDeliveryAnalysisRecord.total_deliverable == 2 )
    assert(emailDeliveryAnalysisRecord.total_risky == 3)
    assert(emailDeliveryAnalysisRecord.total_unknown == 5)

    assert(emailDeliveryAnalysisRecord.n_spam_deliverable == 0)
    assert(emailDeliveryAnalysisRecord.n_spam_risky == 1)
    assert(emailDeliveryAnalysisRecord.n_spam_unknown == 0)

    assert(!emailDeliveryAnalysisRecord.should_pause_unknown)
    assert(!emailDeliveryAnalysisRecord.should_pause_risky)
    assert(!emailDeliveryAnalysisRecord.should_pause_deliverable)

  }

  "runBounceCheckOnCampaign1 " should " have 1 spam count in unknown " in {
    val activeCampaignService = mock[ActiveCampaignService]
    val emailDeliveryAnalysisService: EmailDeliveryAnalysisService = mock[EmailDeliveryAnalysisService]
    val monitorService = new SpamMonitorCheckService(
      activeCampaignService = activeCampaignService,
      emailDeliveryAnalysisService = emailDeliveryAnalysisService
    )


    (activeCampaignService.getEmailSendResultForBounceCheck (_: Long, _: Long) (using _: SRLogger))
      .expects( campaign_id,  team_id, *)
      .returning(
        //List[Long](3, 5, 7, 11, 13)
        Success( List[EmailSendStatus] (
          dummyEmailSendStatus,
          dummyEmailSendStatus.copy(ev_id = 2, prospect_id = 100102),
          dummyEmailSendStatus.copy(ev_id = 3, prospect_id = 100103, reason = "risky"),
          dummyEmailSendStatus.copy(ev_id = 4, prospect_id = 100104, reason = "risky"),
          dummyEmailSendStatus.copy(ev_id = 5, prospect_id = 100105, reason = "unknown", email_bounce_type = Some("spam")),
          dummyEmailSendStatus.copy(ev_id = 6, prospect_id = 100106, reason = "unknown"),
          dummyEmailSendStatus.copy(ev_id = 7, prospect_id = 100107, reason = "risky"),
          dummyEmailSendStatus.copy(ev_id = 8, prospect_id = 100108, reason = "unknown"),
          dummyEmailSendStatus.copy(ev_id = 9, prospect_id = 100109, reason = "unknown"),
          dummyEmailSendStatus.copy(ev_id =10, prospect_id = 100110, reason = "unknown")
        ))
      )


    val c1 = CaptureOne[EmailDeliveryAnalysisRecord]()

    (emailDeliveryAnalysisService.createEmailValidationAnalysisRecord)
      .expects(capture(c1))
      .returning(Success(-10)
      )

    //    (emailDeliveryAnalysisService.createEmailValidationAnalysisRecord)
    //      .expects(capture(c1)) once()


    (emailDeliveryAnalysisService.updateEmailValidationWithAnalysisId)
      .expects(*, *)
//      .returning(Success(-10)
//      )

    monitorService.runBounceCheckOnCampaign1(
      activeCampaign = campaign_id, teamId = team_id, logger = logger
    )

    //println(s"c1val 2: ${c1.value}")
    val emailDeliveryAnalysisRecord = c1.value
    assert(emailDeliveryAnalysisRecord.validation_start_id == 1)
    assert(emailDeliveryAnalysisRecord.validation_end_id == 10)

    assert(emailDeliveryAnalysisRecord.total_deliverable == 2 )
    assert(emailDeliveryAnalysisRecord.total_risky == 3)
    assert(emailDeliveryAnalysisRecord.total_unknown == 5)

    assert(emailDeliveryAnalysisRecord.n_spam_deliverable == 0)
    assert(emailDeliveryAnalysisRecord.n_spam_risky == 0)
    assert(emailDeliveryAnalysisRecord.n_spam_unknown == 1)

    /*
    campaign_id = 10101,
      prospect_id = 100101,
      team_id = 1000101
     */
    assert(emailDeliveryAnalysisRecord.campaign_id == campaign_id)
    assert(emailDeliveryAnalysisRecord.team_id == team_id)

    assert(!emailDeliveryAnalysisRecord.should_pause_unknown)
    assert(!emailDeliveryAnalysisRecord.should_pause_risky)
    assert(!emailDeliveryAnalysisRecord.should_pause_deliverable)

  }

  "runBounceCheckOnCampaign1 " should " have 1 spam count in deliverable " in {
    val activeCampaignService = mock[ActiveCampaignService]
    val emailDeliveryAnalysisService: EmailDeliveryAnalysisService = mock[EmailDeliveryAnalysisService]
    val monitorService = new SpamMonitorCheckService(
      activeCampaignService = activeCampaignService,
      emailDeliveryAnalysisService = emailDeliveryAnalysisService
    )

    val dummyEmailSendStatus = EmailSendStatus(ev_id = 1,
      checked_via_tool= "ocr",
      reason = "deliverable",
      email_bounce_type = None,
      campaign_id = 10101,
      prospect_id = 100101,
      team_id = 1000101)

    (activeCampaignService.getEmailSendResultForBounceCheck (_: Long, _: Long) (using _: SRLogger))
      .expects( 1,  3, *)
      .returning(
        //List[Long](3, 5, 7, 11, 13)
        Success( List[EmailSendStatus] (
          dummyEmailSendStatus,
          dummyEmailSendStatus.copy(ev_id = 2, prospect_id = 100102, email_bounce_type = Some("spam")),
          dummyEmailSendStatus.copy(ev_id = 3, prospect_id = 100103, reason = "risky"),
          dummyEmailSendStatus.copy(ev_id = 4, prospect_id = 100104, reason = "risky"),
          dummyEmailSendStatus.copy(ev_id = 5, prospect_id = 100105, reason = "unknown"),
          dummyEmailSendStatus.copy(ev_id = 6, prospect_id = 100106, reason = "unknown"),
          dummyEmailSendStatus.copy(ev_id = 7, prospect_id = 100107, reason = "risky"),
          dummyEmailSendStatus.copy(ev_id = 8, prospect_id = 100108, reason = "unknown"),
          dummyEmailSendStatus.copy(ev_id = 9, prospect_id = 100109, reason = "unknown"),
          dummyEmailSendStatus.copy(ev_id =10, prospect_id = 100110, reason = "unknown")
        ))
      )


    val c1 = CaptureOne[EmailDeliveryAnalysisRecord]()

    (emailDeliveryAnalysisService.createEmailValidationAnalysisRecord)
      .expects(capture(c1))
      .returning(Success(-10)
      )

    //    (emailDeliveryAnalysisService.createEmailValidationAnalysisRecord)
    //      .expects(capture(c1)) once()


    (emailDeliveryAnalysisService.updateEmailValidationWithAnalysisId)
      .expects(*, *)
//      .returning(Success(-10)
//      )

    monitorService.runBounceCheckOnCampaign1(
      activeCampaign = 1, teamId = 3, logger = logger
    )

    //println(s"c1val 2: ${c1.value}")
    val emailDeliveryAnalysisRecord = c1.value
    assert(emailDeliveryAnalysisRecord.validation_start_id == 1)
    assert(emailDeliveryAnalysisRecord.validation_end_id == 10)

    assert(emailDeliveryAnalysisRecord.total_deliverable == 2 )
    assert(emailDeliveryAnalysisRecord.total_risky == 3)
    assert(emailDeliveryAnalysisRecord.total_unknown == 5)

    assert(emailDeliveryAnalysisRecord.n_spam_deliverable == 1)
    assert(emailDeliveryAnalysisRecord.n_spam_risky == 0)
    assert(emailDeliveryAnalysisRecord.n_spam_unknown == 0)

    assert(!emailDeliveryAnalysisRecord.should_pause_unknown)
    assert(!emailDeliveryAnalysisRecord.should_pause_risky)
    assert(!emailDeliveryAnalysisRecord.should_pause_deliverable)

  }

  "runBounceCheckOnCampaign1 " should " pause the deliverable emails" in {
    val activeCampaignService = mock[ActiveCampaignService]
    val emailDeliveryAnalysisService: EmailDeliveryAnalysisService = mock[EmailDeliveryAnalysisService]
    val monitorService = new SpamMonitorCheckService(
      activeCampaignService = activeCampaignService,
      emailDeliveryAnalysisService = emailDeliveryAnalysisService
    )


    (activeCampaignService.getEmailSendResultForBounceCheck (_: Long, _: Long) (using _: SRLogger))
      .expects( 1,  3, *)
      .returning(
        //List[Long](3, 5, 7, 11, 13)
        Success( List[EmailSendStatus] (
          dummyEmailSendStatus,
          dummyEmailSendStatus.copy(ev_id = 2, prospect_id = 100102, email_bounce_type = Some("spam")),
          dummyEmailSendStatus.copy(ev_id = 3, prospect_id = 100103, reason = "risky"),
          dummyEmailSendStatus.copy(ev_id = 4, prospect_id = 100104, reason = "risky"),
          dummyEmailSendStatus.copy(ev_id = 5, prospect_id = 100105, reason = "unknown"),
          dummyEmailSendStatus.copy(ev_id = 6, prospect_id = 100106, reason = "unknown"),
          dummyEmailSendStatus.copy(ev_id = 7, prospect_id = 100107, reason = "risky"),
          dummyEmailSendStatus.copy(ev_id = 8, prospect_id = 100108, reason = "unknown"),
          dummyEmailSendStatus.copy(ev_id = 9, prospect_id = 100109, reason = "unknown"),
          dummyEmailSendStatus.copy(ev_id =10, prospect_id = 100110, reason = "unknown"),
          dummyEmailSendStatus.copy(ev_id =11, prospect_id = 100111, email_bounce_type = Some("spam")),
          dummyEmailSendStatus.copy(ev_id =12, prospect_id = 100112, email_bounce_type = Some("spam"))
        ))
      )


    val c1 = CaptureOne[EmailDeliveryAnalysisRecord]()

    (emailDeliveryAnalysisService.createEmailValidationAnalysisRecord)
      .expects(capture(c1))
      .returning(Success(-10)
      )

    //    (emailDeliveryAnalysisService.createEmailValidationAnalysisRecord)
    //      .expects(capture(c1)) once()


    (emailDeliveryAnalysisService.updateEmailValidationWithAnalysisId)
      .expects(*, *)
//      .returning(Success(-10)
//      )

    monitorService.runBounceCheckOnCampaign1(
      activeCampaign = 1, teamId = 3, logger = logger
    )

    //println(s"c1val 2: ${c1.value}")
    val emailDeliveryAnalysisRecord = c1.value
    assert(emailDeliveryAnalysisRecord.validation_start_id == 1)
    assert(emailDeliveryAnalysisRecord.validation_end_id == 12)

    assert(emailDeliveryAnalysisRecord.total_deliverable == 4 )
    assert(emailDeliveryAnalysisRecord.total_risky == 3)
    assert(emailDeliveryAnalysisRecord.total_unknown == 5)

    assert(emailDeliveryAnalysisRecord.n_spam_deliverable == 3)
    assert(emailDeliveryAnalysisRecord.n_spam_risky == 0)
    assert(emailDeliveryAnalysisRecord.n_spam_unknown == 0)

    assert( emailDeliveryAnalysisRecord.should_pause_deliverable)
    assert(!emailDeliveryAnalysisRecord.should_pause_risky)
    assert(!emailDeliveryAnalysisRecord.should_pause_unknown)

  }

  "runBounceCheckOnCampaign1 " should " pause the risky emails" in {
    val activeCampaignService = mock[ActiveCampaignService]
    val emailDeliveryAnalysisService: EmailDeliveryAnalysisService = mock[EmailDeliveryAnalysisService]
    val monitorService = new SpamMonitorCheckService(
      activeCampaignService = activeCampaignService,
      emailDeliveryAnalysisService = emailDeliveryAnalysisService
    )

    (activeCampaignService.getEmailSendResultForBounceCheck (_: Long, _: Long) (using _: SRLogger))
      .expects( campaign_id,  team_id, *)
      .returning(
        //List[Long](3, 5, 7, 11, 13)
        Success( List[EmailSendStatus] (
          dummyEmailSendStatus,
          dummyEmailSendStatus.copy(ev_id = 2, prospect_id = 100102),
          dummyEmailSendStatus.copy(ev_id = 3, prospect_id = 100103, reason = "risky", email_bounce_type = Some("spam")),
          dummyEmailSendStatus.copy(ev_id = 4, prospect_id = 100104, reason = "risky", email_bounce_type = Some("spam")),
          dummyEmailSendStatus.copy(ev_id = 5, prospect_id = 100105, reason = "unknown"),
          dummyEmailSendStatus.copy(ev_id = 6, prospect_id = 100106, reason = "unknown"),
          dummyEmailSendStatus.copy(ev_id = 7, prospect_id = 100107, reason = "risky", email_bounce_type = Some("spam")),
          dummyEmailSendStatus.copy(ev_id = 8, prospect_id = 100108, reason = "unknown"),
          dummyEmailSendStatus.copy(ev_id = 9, prospect_id = 100109, reason = "unknown"),
          dummyEmailSendStatus.copy(ev_id =10, prospect_id = 100110, reason = "unknown"),
          dummyEmailSendStatus.copy(ev_id =11, prospect_id = 100111, email_bounce_type = Some("spam")),
          dummyEmailSendStatus.copy(ev_id =12, prospect_id = 100112, email_bounce_type = Some("spam"))
        ))
      )


    val c1 = CaptureOne[EmailDeliveryAnalysisRecord]()

    (emailDeliveryAnalysisService.createEmailValidationAnalysisRecord)
      .expects(capture(c1))
      .returning(Success(-10)
      )

    //    (emailDeliveryAnalysisService.createEmailValidationAnalysisRecord)
    //      .expects(capture(c1)) once()


    (emailDeliveryAnalysisService.updateEmailValidationWithAnalysisId)
      .expects(*, *)
//      .returning(Success(-10)
//      )

    monitorService.runBounceCheckOnCampaign1(
      activeCampaign = campaign_id, teamId = team_id, logger = logger
    )

    //println(s"c1val 2: ${c1.value}")
    val emailDeliveryAnalysisRecord = c1.value
    assert(emailDeliveryAnalysisRecord.validation_start_id == 1)
    assert(emailDeliveryAnalysisRecord.validation_end_id == 12)

    assert(emailDeliveryAnalysisRecord.total_deliverable == 4 )
    assert(emailDeliveryAnalysisRecord.total_risky == 3)
    assert(emailDeliveryAnalysisRecord.total_unknown == 5)

    assert(emailDeliveryAnalysisRecord.n_spam_deliverable == 2)
    assert(emailDeliveryAnalysisRecord.n_spam_risky == 3)
    assert(emailDeliveryAnalysisRecord.n_spam_unknown == 0)

    assert(!emailDeliveryAnalysisRecord.should_pause_deliverable)
    assert( emailDeliveryAnalysisRecord.should_pause_risky)
    assert(!emailDeliveryAnalysisRecord.should_pause_unknown)

  }

  "runBounceCheckOnCampaign1 " should " pause the unknown emails" in {
    val activeCampaignService = mock[ActiveCampaignService]
    val emailDeliveryAnalysisService: EmailDeliveryAnalysisService = mock[EmailDeliveryAnalysisService]
    val monitorService = new SpamMonitorCheckService(
      activeCampaignService = activeCampaignService,
      emailDeliveryAnalysisService = emailDeliveryAnalysisService
    )

    (activeCampaignService.getEmailSendResultForBounceCheck (_: Long, _: Long) (using _: SRLogger))
      .expects( campaign_id,  team_id, *)
      .returning(
        //List[Long](3, 5, 7, 11, 13)
        Success( List[EmailSendStatus] (
          dummyEmailSendStatus,
          dummyEmailSendStatus.copy(ev_id = 2, prospect_id = 100102),
          dummyEmailSendStatus.copy(ev_id = 3, prospect_id = 100103, reason = "risky"),
          dummyEmailSendStatus.copy(ev_id = 4, prospect_id = 100104, reason = "risky", email_bounce_type = Some("spam")),
          dummyEmailSendStatus.copy(ev_id = 5, prospect_id = 100105, reason = "unknown", email_bounce_type = Some("spam")),
          dummyEmailSendStatus.copy(ev_id = 6, prospect_id = 100106, reason = "unknown", email_bounce_type = Some("spam")),
          dummyEmailSendStatus.copy(ev_id = 7, prospect_id = 100107, reason = "risky", email_bounce_type = Some("spam")),
          dummyEmailSendStatus.copy(ev_id = 8, prospect_id = 100108, reason = "unknown", email_bounce_type = Some("spam")),
          dummyEmailSendStatus.copy(ev_id = 9, prospect_id = 100109, reason = "unknown"),
          dummyEmailSendStatus.copy(ev_id =10, prospect_id = 100110, reason = "unknown"),
          dummyEmailSendStatus.copy(ev_id =11, prospect_id = 100111, email_bounce_type = Some("spam")),
          dummyEmailSendStatus.copy(ev_id =12, prospect_id = 100112, email_bounce_type = Some("spam"))
        ))
      )


    val c1 = CaptureOne[EmailDeliveryAnalysisRecord]()

    (emailDeliveryAnalysisService.createEmailValidationAnalysisRecord)
      .expects(capture(c1))
      .returning(Success(-10)
      )

    //    (emailDeliveryAnalysisService.createEmailValidationAnalysisRecord)
    //      .expects(capture(c1)) once()


    (emailDeliveryAnalysisService.updateEmailValidationWithAnalysisId)
      .expects(*, *)
//      .returning(Success(-10)
//      )

    monitorService.runBounceCheckOnCampaign1(
      activeCampaign = campaign_id, teamId = team_id, logger = logger
    )

    //println(s"c1val 2: ${c1.value}")
    val emailDeliveryAnalysisRecord = c1.value
    assert(emailDeliveryAnalysisRecord.validation_start_id == 1)
    assert(emailDeliveryAnalysisRecord.validation_end_id == 12)

    assert(emailDeliveryAnalysisRecord.total_deliverable == 4 )
    assert(emailDeliveryAnalysisRecord.total_risky == 3)
    assert(emailDeliveryAnalysisRecord.total_unknown == 5)

    assert(emailDeliveryAnalysisRecord.n_spam_deliverable == 2)
    assert(emailDeliveryAnalysisRecord.n_spam_risky == 2)
    assert(emailDeliveryAnalysisRecord.n_spam_unknown == 3)

    assert(!emailDeliveryAnalysisRecord.should_pause_deliverable)
    assert(!emailDeliveryAnalysisRecord.should_pause_risky)
    assert( emailDeliveryAnalysisRecord.should_pause_unknown)

  }



  "runBounceCheckOnCampaign1 " should " detect undeliverable emails in categories deliverable, risky, unknown" in {
    val activeCampaignService = mock[ActiveCampaignService]
    val emailDeliveryAnalysisService: EmailDeliveryAnalysisService = mock[EmailDeliveryAnalysisService]
    val monitorService = new SpamMonitorCheckService(
      activeCampaignService = activeCampaignService,
      emailDeliveryAnalysisService = emailDeliveryAnalysisService
    )

    (activeCampaignService.getEmailSendResultForBounceCheck (_: Long, _: Long) (using _: SRLogger))
            .expects( campaign_id,  team_id, *)
            .returning(
              //List[Long](3, 5, 7, 11, 13)
              Success( List[EmailSendStatus] (
                dummyEmailSendStatus,
                dummyEmailSendStatus.copy(ev_id = 2, prospect_id = 100102),
                dummyEmailSendStatus.copy(ev_id = 3, prospect_id = 100103, reason = "risky"),
                dummyEmailSendStatus.copy(ev_id = 4, prospect_id = 100104, reason = "risky", email_bounce_type = Some("address_not_found")),
                dummyEmailSendStatus.copy(ev_id = 5, prospect_id = 100105, reason = "unknown", email_bounce_type = Some("address_not_found")),
                dummyEmailSendStatus.copy(ev_id = 6, prospect_id = 100106, reason = "unknown", email_bounce_type = Some("address_not_found")),
                dummyEmailSendStatus.copy(ev_id = 7, prospect_id = 100107, reason = "risky", email_bounce_type = Some("address_not_found")),
                dummyEmailSendStatus.copy(ev_id = 8, prospect_id = 100108, reason = "unknown", email_bounce_type = Some("address_not_found")),
                dummyEmailSendStatus.copy(ev_id = 9, prospect_id = 100109, reason = "unknown"),
                dummyEmailSendStatus.copy(ev_id =10, prospect_id = 100110, reason = "unknown"),
                dummyEmailSendStatus.copy(ev_id =11, prospect_id = 100111, email_bounce_type = Some("address_not_found")),
                dummyEmailSendStatus.copy(ev_id =12, prospect_id = 100112, email_bounce_type = Some("address_not_found"))
              ))
            )


    val c1 = CaptureOne[EmailDeliveryAnalysisRecord]()

    (emailDeliveryAnalysisService.createEmailValidationAnalysisRecord)
            .expects(capture(c1))
            .returning(Success(-10)
            )

    //    (emailDeliveryAnalysisService.createEmailValidationAnalysisRecord)
    //      .expects(capture(c1)) once()


    (emailDeliveryAnalysisService.updateEmailValidationWithAnalysisId)
            .expects(*, *)
//            .returning(Success(-10)
//            )

    monitorService.runBounceCheckOnCampaign1(
      activeCampaign = campaign_id, teamId = team_id, logger = logger
    )

    //println(s"c1val 2: ${c1.value}")
    val emailDeliveryAnalysisRecord = c1.value
    assert(emailDeliveryAnalysisRecord.validation_start_id == 1)
    assert(emailDeliveryAnalysisRecord.validation_end_id == 12)

    assert(emailDeliveryAnalysisRecord.total_deliverable == 4 )
    assert(emailDeliveryAnalysisRecord.total_risky == 3)
    assert(emailDeliveryAnalysisRecord.total_unknown == 5)

    assert(emailDeliveryAnalysisRecord.n_spam_deliverable == 0)
    assert(emailDeliveryAnalysisRecord.n_spam_risky == 0)
    assert(emailDeliveryAnalysisRecord.n_spam_unknown == 0)

    assert(emailDeliveryAnalysisRecord.n_bounced_deliverable == 2)
    assert(emailDeliveryAnalysisRecord.n_bounced_risky == 2)
    assert(emailDeliveryAnalysisRecord.n_bounced_unknown == 3)


    assert(!emailDeliveryAnalysisRecord.should_pause_deliverable)
    assert(!emailDeliveryAnalysisRecord.should_pause_risky)
    assert(!emailDeliveryAnalysisRecord.should_pause_unknown)

  }

  /*

   */
  "runBounceCheckOnCampaign1 " should " pause deliverable emails because of high bounce rate" in {
    val activeCampaignService = mock[ActiveCampaignService]
    val emailDeliveryAnalysisService: EmailDeliveryAnalysisService = mock[EmailDeliveryAnalysisService]
    val monitorService = new SpamMonitorCheckService(
      activeCampaignService = activeCampaignService,
      emailDeliveryAnalysisService = emailDeliveryAnalysisService
    )

    (activeCampaignService.getEmailSendResultForBounceCheck (_: Long, _: Long) (using _: SRLogger))
            .expects( campaign_id,  team_id, *)
            .returning(
              //List[Long](3, 5, 7, 11, 13)
              Success( List[EmailSendStatus] (
                dummyEmailSendStatus,
                dummyEmailSendStatus.copy(ev_id = 2, prospect_id = 100102),
                dummyEmailSendStatus.copy(ev_id = 3, prospect_id = 100103, reason = "risky"),
                dummyEmailSendStatus.copy(ev_id = 4, prospect_id = 100104, reason = "risky", email_bounce_type = Some("address_not_found")),
                dummyEmailSendStatus.copy(ev_id = 5, prospect_id = 100105, reason = "unknown", email_bounce_type = Some("address_not_found")),
                dummyEmailSendStatus.copy(ev_id = 6, prospect_id = 100106, reason = "unknown", email_bounce_type = Some("address_not_found")),
                dummyEmailSendStatus.copy(ev_id = 7, prospect_id = 100107, reason = "risky", email_bounce_type = Some("address_not_found")),
                dummyEmailSendStatus.copy(ev_id = 8, prospect_id = 100108, reason = "unknown", email_bounce_type = Some("address_not_found")),
                dummyEmailSendStatus.copy(ev_id = 9, prospect_id = 100109, reason = "unknown"),
                dummyEmailSendStatus.copy(ev_id =10, prospect_id = 100110, reason = "unknown"),
                dummyEmailSendStatus.copy(ev_id =11, prospect_id = 100111, email_bounce_type = Some("address_not_found")),
                dummyEmailSendStatus.copy(ev_id =12, prospect_id = 100112, email_bounce_type = Some("address_not_found")),
                dummyEmailSendStatus.copy(ev_id =13, prospect_id = 100113, email_bounce_type = Some("address_not_found")),
                dummyEmailSendStatus.copy(ev_id =14, prospect_id = 100114, email_bounce_type = Some("address_not_found")),
                dummyEmailSendStatus.copy(ev_id =15, prospect_id = 100115, email_bounce_type = Some("address_not_found")),
                dummyEmailSendStatus.copy(ev_id =16, prospect_id = 100116, email_bounce_type = Some("address_not_found")),
                dummyEmailSendStatus.copy(ev_id =17, prospect_id = 100117, email_bounce_type = Some("address_not_found")),
                dummyEmailSendStatus.copy(ev_id =18, prospect_id = 100118, email_bounce_type = Some("address_not_found")),
                dummyEmailSendStatus.copy(ev_id =19, prospect_id = 100119, email_bounce_type = Some("address_not_found")),
                dummyEmailSendStatus.copy(ev_id =20, prospect_id = 100120, email_bounce_type = Some("address_not_found")),
                dummyEmailSendStatus.copy(ev_id =21, prospect_id = 100121, email_bounce_type = Some("address_not_found")),
                dummyEmailSendStatus.copy(ev_id =22, prospect_id = 100122, email_bounce_type = Some("address_not_found")),
                dummyEmailSendStatus.copy(ev_id =23, prospect_id = 100123, email_bounce_type = Some("address_not_found")),
                dummyEmailSendStatus.copy(ev_id =24, prospect_id = 100124, email_bounce_type = Some("address_not_found")),
                dummyEmailSendStatus.copy(ev_id =25, prospect_id = 100125, email_bounce_type = Some("address_not_found"))
              ))
            )


    val c1 = CaptureOne[EmailDeliveryAnalysisRecord]()

    (emailDeliveryAnalysisService.createEmailValidationAnalysisRecord)
            .expects(capture(c1))
            .returning(Success(-10)
            )

    //    (emailDeliveryAnalysisService.createEmailValidationAnalysisRecord)
    //      .expects(capture(c1)) once()


    (emailDeliveryAnalysisService.updateEmailValidationWithAnalysisId)
            .expects(*, *)
//            .returning(Success(-10)
//            )

    monitorService.runBounceCheckOnCampaign1(
      activeCampaign = campaign_id, teamId = team_id, logger = logger
    )

    //println(s"c1val 2: ${c1.value}")
    val emailDeliveryAnalysisRecord = c1.value
    assert(emailDeliveryAnalysisRecord.validation_start_id == 1)
    assert(emailDeliveryAnalysisRecord.validation_end_id == 25)

    assert(emailDeliveryAnalysisRecord.total_deliverable == 17 )
    assert(emailDeliveryAnalysisRecord.total_risky == 3)
    assert(emailDeliveryAnalysisRecord.total_unknown == 5)

    assert(emailDeliveryAnalysisRecord.sample_size == 25)

    assert(emailDeliveryAnalysisRecord.n_spam_deliverable == 0)
    assert(emailDeliveryAnalysisRecord.n_spam_risky == 0)
    assert(emailDeliveryAnalysisRecord.n_spam_unknown == 0)

    assert(emailDeliveryAnalysisRecord.n_bounced_deliverable == 15)
    assert(emailDeliveryAnalysisRecord.n_bounced_risky == 2)
    assert(emailDeliveryAnalysisRecord.n_bounced_unknown == 3)


    assert( emailDeliveryAnalysisRecord.should_pause_deliverable)
    assert(!emailDeliveryAnalysisRecord.should_pause_risky)
    assert(!emailDeliveryAnalysisRecord.should_pause_unknown)

  }

  "runBounceCheckOnCampaign1 " should " pause risky emails because of high bounce rate" in {
    val activeCampaignService = mock[ActiveCampaignService]
    val emailDeliveryAnalysisService: EmailDeliveryAnalysisService = mock[EmailDeliveryAnalysisService]
    val monitorService = new SpamMonitorCheckService(
      activeCampaignService = activeCampaignService,
      emailDeliveryAnalysisService = emailDeliveryAnalysisService
    )

    (activeCampaignService.getEmailSendResultForBounceCheck (_: Long, _: Long) (using _: SRLogger))
            .expects( campaign_id,  team_id, *)
            .returning(
              //List[Long](3, 5, 7, 11, 13)
              Success( List[EmailSendStatus] (
                dummyEmailSendStatus,
                dummyEmailSendStatus.copy(ev_id = 2, prospect_id = 100102),
                dummyEmailSendStatus.copy(ev_id = 3, prospect_id = 100103, reason = "risky"),
                dummyEmailSendStatus.copy(ev_id = 4, prospect_id = 100104, reason = "risky", email_bounce_type = Some("address_not_found")),
                dummyEmailSendStatus.copy(ev_id = 5, prospect_id = 100105, reason = "unknown", email_bounce_type = Some("address_not_found")),
                dummyEmailSendStatus.copy(ev_id = 6, prospect_id = 100106, reason = "unknown", email_bounce_type = Some("address_not_found")),
                dummyEmailSendStatus.copy(ev_id = 7, prospect_id = 100107, reason = "risky", email_bounce_type = Some("address_not_found")),
                dummyEmailSendStatus.copy(ev_id = 8, prospect_id = 100108, reason = "unknown", email_bounce_type = Some("address_not_found")),
                dummyEmailSendStatus.copy(ev_id = 9, prospect_id = 100109, reason = "unknown"),
                dummyEmailSendStatus.copy(ev_id =10, prospect_id = 100110, reason = "unknown"),
                dummyEmailSendStatus.copy(ev_id =11, prospect_id = 100111, reason = "risky",  email_bounce_type = Some("address_not_found")),
                dummyEmailSendStatus.copy(ev_id =12, prospect_id = 100112, reason = "risky",  email_bounce_type = Some("address_not_found")),
                dummyEmailSendStatus.copy(ev_id =13, prospect_id = 100113, reason = "risky",  email_bounce_type = Some("address_not_found")),
                dummyEmailSendStatus.copy(ev_id =14, prospect_id = 100114, reason = "risky",  email_bounce_type = Some("address_not_found")),
                dummyEmailSendStatus.copy(ev_id =15, prospect_id = 100115, reason = "risky",  email_bounce_type = Some("address_not_found")),
                dummyEmailSendStatus.copy(ev_id =16, prospect_id = 100116, reason = "risky",  email_bounce_type = Some("address_not_found")),
                dummyEmailSendStatus.copy(ev_id =17, prospect_id = 100117, reason = "risky",  email_bounce_type = Some("address_not_found")),
                dummyEmailSendStatus.copy(ev_id =18, prospect_id = 100118, reason = "risky",  email_bounce_type = Some("address_not_found")),
                dummyEmailSendStatus.copy(ev_id =19, prospect_id = 100119, reason = "risky",  email_bounce_type = Some("address_not_found")),
                dummyEmailSendStatus.copy(ev_id =20, prospect_id = 100120, reason = "risky",  email_bounce_type = Some("address_not_found")),
                dummyEmailSendStatus.copy(ev_id =21, prospect_id = 100121, reason = "risky",  email_bounce_type = Some("address_not_found")),
                dummyEmailSendStatus.copy(ev_id =22, prospect_id = 100122, reason = "risky",  email_bounce_type = Some("address_not_found")),
                dummyEmailSendStatus.copy(ev_id =23, prospect_id = 100123, reason = "risky",  email_bounce_type = Some("address_not_found")),
                dummyEmailSendStatus.copy(ev_id =24, prospect_id = 100124, reason = "risky",  email_bounce_type = Some("address_not_found")),
                dummyEmailSendStatus.copy(ev_id =25, prospect_id = 100125, reason = "risky",  email_bounce_type = Some("address_not_found"))
              ))
            )


    val c1 = CaptureOne[EmailDeliveryAnalysisRecord]()

    (emailDeliveryAnalysisService.createEmailValidationAnalysisRecord)
            .expects(capture(c1))
            .returning(Success(-10)
            )

    //    (emailDeliveryAnalysisService.createEmailValidationAnalysisRecord)
    //      .expects(capture(c1)) once()


    (emailDeliveryAnalysisService.updateEmailValidationWithAnalysisId)
            .expects(*, *)
//            .returning(Success(-10)
//            )

    monitorService.runBounceCheckOnCampaign1(
      activeCampaign = campaign_id, teamId = team_id, logger = logger
    )

    //println(s"c1val 2: ${c1.value}")
    val emailDeliveryAnalysisRecord = c1.value
    assert(emailDeliveryAnalysisRecord.validation_start_id == 1)
    assert(emailDeliveryAnalysisRecord.validation_end_id == 25)

    assert(emailDeliveryAnalysisRecord.total_deliverable == 2 )
    assert(emailDeliveryAnalysisRecord.total_risky == 18)
    assert(emailDeliveryAnalysisRecord.total_unknown == 5)

    assert(emailDeliveryAnalysisRecord.sample_size == 25)

    assert(emailDeliveryAnalysisRecord.n_spam_deliverable == 0)
    assert(emailDeliveryAnalysisRecord.n_spam_risky == 0)
    assert(emailDeliveryAnalysisRecord.n_spam_unknown == 0)

    assert(emailDeliveryAnalysisRecord.n_bounced_deliverable == 0)
    assert(emailDeliveryAnalysisRecord.n_bounced_risky == 17)
    assert(emailDeliveryAnalysisRecord.n_bounced_unknown == 3)


    assert(!emailDeliveryAnalysisRecord.should_pause_deliverable)
    assert( emailDeliveryAnalysisRecord.should_pause_risky)
    assert(!emailDeliveryAnalysisRecord.should_pause_unknown)

  }

  "runBounceCheckOnCampaign1 " should " pause unknown emails because of high bounce rate" in {
    val activeCampaignService = mock[ActiveCampaignService]
    val emailDeliveryAnalysisService: EmailDeliveryAnalysisService = mock[EmailDeliveryAnalysisService]
    val monitorService = new SpamMonitorCheckService(
      activeCampaignService = activeCampaignService,
      emailDeliveryAnalysisService = emailDeliveryAnalysisService
    )

    (activeCampaignService.getEmailSendResultForBounceCheck (_: Long, _: Long) (using _: SRLogger))
            .expects( campaign_id,  team_id, *)
            .returning(
              //List[Long](3, 5, 7, 11, 13)
              Success( List[EmailSendStatus] (
                dummyEmailSendStatus,
                dummyEmailSendStatus.copy(ev_id = 2, prospect_id = 100102),
                dummyEmailSendStatus.copy(ev_id = 3, prospect_id = 100103, reason = "risky"),
                dummyEmailSendStatus.copy(ev_id = 4, prospect_id = 100104, reason = "risky", email_bounce_type = Some("address_not_found")),
                dummyEmailSendStatus.copy(ev_id = 5, prospect_id = 100105, reason = "unknown", email_bounce_type = Some("address_not_found")),
                dummyEmailSendStatus.copy(ev_id = 6, prospect_id = 100106, reason = "unknown", email_bounce_type = Some("address_not_found")),
                dummyEmailSendStatus.copy(ev_id = 7, prospect_id = 100107, reason = "risky", email_bounce_type = Some("address_not_found")),
                dummyEmailSendStatus.copy(ev_id = 8, prospect_id = 100108, reason = "unknown", email_bounce_type = Some("address_not_found")),
                dummyEmailSendStatus.copy(ev_id = 9, prospect_id = 100109, reason = "unknown"),
                dummyEmailSendStatus.copy(ev_id =10, prospect_id = 100110, reason = "unknown"),
                dummyEmailSendStatus.copy(ev_id =11, prospect_id = 100111, reason = "unknown",  email_bounce_type = Some("address_not_found")),
                dummyEmailSendStatus.copy(ev_id =12, prospect_id = 100112, reason = "unknown",  email_bounce_type = Some("address_not_found")),
                dummyEmailSendStatus.copy(ev_id =13, prospect_id = 100113, reason = "unknown",  email_bounce_type = Some("address_not_found")),
                dummyEmailSendStatus.copy(ev_id =14, prospect_id = 100114, reason = "unknown",  email_bounce_type = Some("address_not_found")),
                dummyEmailSendStatus.copy(ev_id =15, prospect_id = 100115, reason = "unknown",  email_bounce_type = Some("address_not_found")),
                dummyEmailSendStatus.copy(ev_id =16, prospect_id = 100116, reason = "unknown",  email_bounce_type = Some("address_not_found")),
                dummyEmailSendStatus.copy(ev_id =17, prospect_id = 100117, reason = "unknown",  email_bounce_type = Some("address_not_found")),
                dummyEmailSendStatus.copy(ev_id =18, prospect_id = 100118, reason = "unknown",  email_bounce_type = Some("address_not_found")),
                dummyEmailSendStatus.copy(ev_id =19, prospect_id = 100119, reason = "unknown",  email_bounce_type = Some("address_not_found")),
                dummyEmailSendStatus.copy(ev_id =20, prospect_id = 100120, reason = "unknown",  email_bounce_type = Some("address_not_found")),
                dummyEmailSendStatus.copy(ev_id =21, prospect_id = 100121, reason = "unknown",  email_bounce_type = Some("address_not_found")),
                dummyEmailSendStatus.copy(ev_id =22, prospect_id = 100122, reason = "unknown",  email_bounce_type = Some("address_not_found")),
                dummyEmailSendStatus.copy(ev_id =23, prospect_id = 100123, reason = "unknown",  email_bounce_type = Some("address_not_found")),
                dummyEmailSendStatus.copy(ev_id =24, prospect_id = 100124, reason = "unknown",  email_bounce_type = Some("address_not_found")),
                dummyEmailSendStatus.copy(ev_id =25, prospect_id = 100125, reason = "unknown",  email_bounce_type = Some("address_not_found"))
              ))
            )


    val c1 = CaptureOne[EmailDeliveryAnalysisRecord]()

    (emailDeliveryAnalysisService.createEmailValidationAnalysisRecord)
            .expects(capture(c1))
            .returning(Success(-10)
            )

    //    (emailDeliveryAnalysisService.createEmailValidationAnalysisRecord)
    //      .expects(capture(c1)) once()


    (emailDeliveryAnalysisService.updateEmailValidationWithAnalysisId)
            .expects(*, *)
//            .returning(Success(-10)
//            )

    monitorService.runBounceCheckOnCampaign1(
      activeCampaign = campaign_id, teamId = team_id, logger = logger
    )

    //println(s"c1val 2: ${c1.value}")
    val emailDeliveryAnalysisRecord = c1.value
    assert(emailDeliveryAnalysisRecord.validation_start_id == 1)
    assert(emailDeliveryAnalysisRecord.validation_end_id == 25)

    assert(emailDeliveryAnalysisRecord.total_deliverable == 2 )
    assert(emailDeliveryAnalysisRecord.total_risky == 3)
    assert(emailDeliveryAnalysisRecord.total_unknown == 20)

    assert(emailDeliveryAnalysisRecord.sample_size == 25)

    assert(emailDeliveryAnalysisRecord.n_spam_deliverable == 0)
    assert(emailDeliveryAnalysisRecord.n_spam_risky == 0)
    assert(emailDeliveryAnalysisRecord.n_spam_unknown == 0)

    assert(emailDeliveryAnalysisRecord.n_bounced_deliverable == 0)
    assert(emailDeliveryAnalysisRecord.n_bounced_risky == 2)
    assert(emailDeliveryAnalysisRecord.n_bounced_unknown == 18)


    assert(!emailDeliveryAnalysisRecord.should_pause_deliverable)
    assert(!emailDeliveryAnalysisRecord.should_pause_risky)
    assert( emailDeliveryAnalysisRecord.should_pause_unknown)

  }

  /*
  "An empty Set" should "have size 0" in {
    assert(Set.empty.size == 0)
  }

  implicit val activeCampaignService = mock[ActiveCampaignService]

  it should "produce NoSuchElementException when head is invoked" in {
    assertThrows[NoSuchElementException] {
      Set.empty.head
    }
  }

  */

}
