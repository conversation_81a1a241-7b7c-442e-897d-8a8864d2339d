package app.utils.cronjobs.spammonitor

import api.AppConfig
import org.apache.pekko.actor.ActorSystem
import api.accounts.email.models.EmailServiceProvider
import api.accounts.models.{AccountId, AccountProfileInfo, OrgId, ProspectAccountUuid}
import api.accounts.{Account, AccountAccess, AccountDAO, AccountMetadata, AccountService, AccountType, AccountUuid, OrgCountData, OrgMetadata, OrgPlan, OrgSettings, OrganizationRole, OrganizationWithCurrentData, RepTrackingHosts, ReplyHandling, TeamId}
import api.calendar_app.models.CalendarAccountData
import api.campaigns.models.{CallSettingSenderDetails, CampaignEmailSettingsId, CampaignStepData, CampaignStepType, CampaignType, LinkedinSettingSenderDetails, PreviousFollowUpData, SmsSettingSenderDetails, WhatsappSettingSenderDetails}
import api.campaigns.{CampaignBasicDetails, CampaignEditedPreviewEmail, CampaignEditedPreviewEmailDAO, CampaignEmailSettings, CampaignEmailSettingsUuid, CampaignSettings, CampaignStepVariant, CampaignStepVariantDAO, CampaignWithStatsAndEmail, ChannelSettingUuid, PreviousFollowUp}
import api.campaigns.services.{CampaignId, CampaignService}
import api.columns.InternalMergeTagValuesForProspect
import api.emails.dao_service.EmailScheduledDAOService
import api.emails.models.EmailSettingUuid
import api.emails.{EmailScheduledDAO, EmailSetting, EmailSettingDAO, EmailToBeSent, EmailsScheduledUuid}
import api.prospects.dao_service.ProspectDAOService
import api.prospects.{ProspectAccount, ProspectAccountDAO1, ProspectUuid}
import api.rep_tracking_hosts.service.RepTrackingHostService
import api.reports.{AllCampaignStats, ReplySentimentStats}
import api.spamtest.CreateSpamTestError.SQLException
import api.spamtest.{SpamTest, SpamTestDAO, SpamTestNotificationData, SpamTestType, UpdateSpamTest}
import api.tags.models.{CampaignTag, CampaignTagUuid}
import api.team.TeamUuid
import app.test_fixtures.accounts.OrgCountDataFixture
import app.test_fixtures.campaign_settings.{CallSettingSenderDetailsFixtures, LinkedinSettingSenderDetailsFixtures, SmsSettingSenderDetailsFixtures, WhatsappSettingSenderDetailsFixtures}
import app.test_fixtures.organizationa.{OrgMetadataFixture, OrgPlanFixture}
import app.test_fixtures.prospect.{ProspectAccountFixture, ProspectFixtures}
import eventframework.{ProspectObject, ProspectObjectInternal}
import io.smartreach.esp.api.emails.{EmailSettingId, IEmailAddress}
import org.joda.time.DateTime
import org.scalamock.matchers.ArgCapture.{CaptureAll, CaptureOne}
import org.scalamock.scalatest.{AsyncMockFactory, MockFactory}
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.funspec.AsyncFunSpec
import play.api.libs.json.JsString
import play.api.libs.ws.ahc.AhcWSClient
import utils.cronjobs.SpamTestCronService
import utils.{EmailSpamTester, Helpers, SRLogger, SpamTestReportResponseException, SpamTestReportResponseNotFoundException}
import utils.email.{EmailBodyService, EmailHelper, EmailSendDetail, EmailService, EmailServiceBody, EmailServiceCompanion}
import play.api.libs.json.{JsString, JsValue, Json}
import sr_scheduler.CampaignStatus
import sr_scheduler.models.{CampaignEmailPriority, ChannelType}

import scala.concurrent.duration.*
import scala.concurrent.{Await, ExecutionContext, Future}
import play.api.libs.ws.WSClient
import utils.cache_utils.model.CampaignUseStatusForEmailSetting
import utils.cronjobs.email_setting_deletion.model.EmailSettingStatus
import utils.email_notification.service.EmailNotificationService

import scala.util.{Failure, Success}
import utils.templating.TemplateService
import utils.testapp.TestAppExecutionContext


class SpamTestCronServiceSpec extends AsyncFunSpec with AsyncMockFactory {

  val spamTestDAO = mock[SpamTestDAO]
  val emailSettingDAO = mock[EmailSettingDAO]
  val accountDAO = mock[AccountDAO]
  val emailSpamTester = mock[EmailSpamTester]
  val campaignStepVariantDAO = mock[CampaignStepVariantDAO]
  val prospectDAOService = mock[ProspectDAOService]
  val prospectAccountDAO = mock[ProspectAccountDAO1]
  val campaignService = mock[CampaignService]
  val emailNotificationService = mock[EmailNotificationService]
  val campaignEditedPreviewEmailDAO = mock[CampaignEditedPreviewEmailDAO]
  val templateService = mock[TemplateService]
  val emailScheduledDAOService: EmailScheduledDAOService = mock[EmailScheduledDAOService]
  val accountServiceMock = mock[AccountService]
  val repTrackingHostService = mock[RepTrackingHostService]
  //val emailService: EmailService = mock[EmailService]
  val emailBodyService : EmailBodyService = mock[EmailBodyService]
  val emailServiceCompanion = new EmailServiceCompanion(
    templateService: TemplateService,
    campaignEditedPreviewEmailDAO: CampaignEditedPreviewEmailDAO,
    emailScheduledDAOService: EmailScheduledDAOService,
    emailBodyService = emailBodyService
  )

  implicit lazy val system: ActorSystem = TestAppExecutionContext.actorSystem
  implicit lazy val ec: ExecutionContext = TestAppExecutionContext.ec
  implicit lazy val wSClient: AhcWSClient = TestAppExecutionContext.wsClient


  given logger: SRLogger = new SRLogger(logRequestId = "SpamTestCronServiceSpec:")
  val e = new Throwable("Sql exception occurred")

  val spamTestCronService = new SpamTestCronService(
    emailSettingDAO = emailSettingDAO,
    emailNotificationService = emailNotificationService,
    accountService = accountServiceMock,
    emailSpamTester = emailSpamTester,
    emailServiceCompanion = emailServiceCompanion,
    campaignStepVariantDAO = campaignStepVariantDAO,
    prospectDAOService = prospectDAOService,
    prospectAccountDAO = prospectAccountDAO,
    spamTestDao = spamTestDAO,
    campaignService = campaignService,
    repTrackingHostService = repTrackingHostService
  )
  val someSpamTest = SpamTest(
    id = 2,
    test_name = "someTest",
    test_type = SpamTestType.AUTH,

    email_settings_id = 1,
    email = "<EMAIL>",

    campaign_id = Some(1),
    step_id = Some(1),

    body = Some("Hi"),
    subject = Some("This subject"),

    mt_id = Some("1234"),
    mt_results = JsString("success"),
    mt_results_last_checked_at = Some(DateTime.parse("2022-3-27")),
    mt_created_at = DateTime.parse("2022-3-27"),
    error = None,
    created_at = DateTime.parse("2022-3-27"),
    teamId = TeamId(1L)

  )

  val campaignBasicDetails = CampaignBasicDetails(
    id = 1,
    account_id = 1,
    team_id = 1,
    name = "Shubham",
    head_step_id = Some(1)
  )
  val prospectObjectInternal = ProspectFixtures.prospectObjectInternal.copy(
    prospect_account_id = Some(1)
  )


  val prospectObject = ProspectObject(
    id = 1,
    owner_id = 1,
    team_id = 1,
    first_name = Some("Shubham"),
    last_name = Some("Kudekar"),
    email = Some("<EMAIL>"),
    custom_fields = Json.obj(),
    list = None,
    job_title = None,
    company = None,
    linkedin_url = None,
    phone = None,
    phone_2 = None,
    phone_3 = None,
    city = None,
    state = None,
    country = None,
    timezone = None,
    prospect_category = "Dont know this",
    last_contacted_at = None,
    last_contacted_at_phone = None,
    created_at = DateTime.parse("2022-3-27"),
    internal = prospectObjectInternal,
    latest_reply_sentiment_uuid = None,
    current_step_type = None,
    latest_task_done_at = None,
    prospect_uuid = Some(ProspectUuid("prs_aa_abcdefghi")),
    owner_uuid = AccountUuid("acc_aa_abcdegfhi"),
    updated_at = DateTime.now()
  )
  val emailSetting = EmailSetting(
    id = Some(EmailSettingId(emailSettingId = 1)),
    org_id = OrgId(id = 1),
    owner_id = AccountId(id = 1),
    team_id = TeamId(id = 1),
    uuid = Some(EmailSettingUuid("test_uuid")),
    owner_uuid = AccountUuid("owner_uuid"),
    team_uuid = TeamUuid("team_uuid"),
    message_id_suffix = "Hello",
    email = "<EMAIL>",
    email_address_host = "<EMAIL>",
    service_provider = EmailServiceProvider.GMAIL_API,
    domain_provider = None,
    via_gmail_smtp = None,
    owner_name = "Shubham",
    sender_name = "Shubham Kudekar",
    first_name = "Shubham",
    last_name = "Kudekar",
    cc_emails = None,
    bcc_emails = None,
    smtp_username = None,
    smtp_password = None,
    smtp_host = None,
    smtp_port = None,
    imap_username = None,
    imap_password = None,
    imap_host = None,
    imap_port = None,
    oauth2_access_token = None,
    oauth2_refresh_token = None,
    oauth2_token_type = None,
    oauth2_token_expires_in = None,
    oauth2_access_token_expires_at = None,
    email_domain = None,
    api_key = None,
    mailgun_region = None,
    quota_per_day = 400,
    reply_handling = ReplyHandling.PAUSE_SPECIFIC_CAMPAIGN_ON_REPLY,
    last_read_for_replies = None,
    latest_email_scheduled_at = None,
    error = None,
    error_reported_at = None,
    paused_till = None,
    signature = "Shubham Kudekar",
    created_at = None,
    current_prospect_sent_count_email = 10,
    default_tracking_domain = "default_tracking_domain",
    default_unsubscribe_domain = "default_unsubscribe_domain",
    rep_tracking_host_id = 1,
    tracking_domain_host = None,
    custom_tracking_domain = None,
    custom_tracking_cname_value = None,
    custom_tracking_domain_is_verified = None,
    custom_tracking_domain_is_ssl_enabled = None,
    rep_mail_server_id = 1,
    rep_mail_server_public_ip = "rep_mail_server_public_ip",
    rep_mail_server_host = "rep_mail_server_host",
    rep_mail_server_reverse_dns = None,
    min_delay_seconds = 0,
    max_delay_seconds = 0,
      tag = None,
    campaign_use_status_for_email_setting = CampaignUseStatusForEmailSetting.IsNotAssignedToAnyCampaign,
    show_rms_ip_in_frontend = false
  )


  val campaignStepVariant = CampaignStepVariant(
    id = 1,
    step_id = 1,
    campaign_id = 1L,
    template_id = Some(1),
    step_data = CampaignStepData.ManualEmailStep(
      subject = "Subject",
      body = ""
    ),
    label = Some("someLabel"),
    active = true
  )

  val aDate = DateTime.parse("2022-3-27")

  val allCampaignStats = AllCampaignStats(
    total_sent = 1,
    total_opened = 1,
    total_clicked = 1,
    total_replied = 1,
    total_steps = 1,
    current_prospects = 1,
    current_opted_out = 1,
    current_completed = 1,
    current_bounced = 1,
    current_to_check = 1,
    current_failed_email_validation = 1,
    current_in_progress = 1,
    current_unsent_prospects = 1,
    current_do_not_contact = 1,
    reply_sentiment_stats = ReplySentimentStats(
      positive = 0
    )
  )
  val campaignSettings = CampaignSettings(

    // settings
    campaign_email_settings = List(
      CampaignEmailSettings(
        campaign_id = CampaignId(1),
        sender_email_setting_id = EmailSettingId(1),
        receiver_email_setting_id = EmailSettingId(11),
        team_id = TeamId(1),
        uuid = CampaignEmailSettingsUuid("temp_setting_id"),
        id = CampaignEmailSettingsId(123),
        sender_email = "<EMAIL>",
        receiver_email = "<EMAIL>",
        max_emails_per_day_from_email_account = 100,
        signature = Some("Shubham Kudekar"),
        error = None,
        from_name = None
      )
    ),
    campaign_linkedin_settings = List(
      LinkedinSettingSenderDetailsFixtures.linkedin_setting_sender_details
    ),
    campaign_call_settings = List(
      CallSettingSenderDetailsFixtures.call_setting_sender_details
    ),
    campaign_whatsapp_settings = List(
      WhatsappSettingSenderDetailsFixtures.whatsapp_setting_sender_details
    ),
    campaign_sms_settings = List(
      SmsSettingSenderDetailsFixtures.sms_setting_sender_details
    ),
    timezone = "thisistimezone",
    daily_from_time = 2, // time since beginning of day in seconds
    daily_till_time = 3, // time since beginning of day in seconds
    sending_holiday_calendar_id = Some(123L),

    ai_sequence_status = None,

    // Sunday is the first day
    days_preference = List(true, true, true, true, true, true, false),

    mark_completed_after_days = 33,
    max_emails_per_day = 100,
    open_tracking_enabled = true,
    click_tracking_enabled = true,
    enable_email_validation = true,
    ab_testing_enabled = true,

    // warm up
    warmup_started_at = Some(aDate.minusDays(3)),
    warmup_length_in_days = Some(2),
    warmup_starting_email_count = Some(5),
    show_soft_start_setting = false,

    // schedule start
    schedule_start_at = Some(aDate.minusDays(1)),
    schedule_start_at_tz = Some("Sometimezone"),
    send_plain_text_email = Some(false),
    campaign_type = CampaignType.MultiChannel,


    email_priority = CampaignEmailPriority.FIRST_EMAIL,
    append_followups = true,
    opt_out_msg = "opt out msg",
    opt_out_is_text = true,
    add_prospect_to_dnc_on_opt_out = true,
    triggers = Seq(),
    sending_mode = None,
    selected_calendar_data = None
  )
  val campaign_uuid = s"cmp_1_cfknacskndjcn"
  val campaignWithStatsAndEmail = CampaignWithStatsAndEmail(
    id = 1L,
    uuid = Some(campaign_uuid),
    team_id = 1,
    shared_with_team = true,
    name = "smartreach",
    owner_name = "Shubham",
    owner_email = "<EMAIL>",
    owner_id = 1,
    status = CampaignStatus.RUNNING,
    tags = Seq(CampaignTag(11L, "tag1", CampaignTagUuid("tags_abcdefgh"))),
    spam_test_exists = false,
    warmup_is_on = false,

    stats = allCampaignStats,

    head_step_id = Some(22L),
    ai_generation_context = None,

    settings = campaignSettings,

    created_at = aDate,

    error = Some("campaign error"),

    is_archived = false
  )

  val repTrackingHosts = RepTrackingHosts(
    id = 1,
    host_url = "url",
    subdomain_based = false,
    active = true
  )
  val emailServiceBody = EmailServiceBody(
    subject = "This subject ",
    textBody = "textbODY",
    htmlBody = "<h1>Hi</h1",
    baseBody = "basebody",
    isEditedPreviewEmail = true,
    has_unsubscribe_link = true
  )
  val internalMergeTagValuesForProspect = InternalMergeTagValuesForProspect(
    sender_name = "Shubham Kudekar",
    sender_first_name = "Shubham",
    sender_last_name = "Kudekar",
    unsubscribe_link = Some("https://default_unsubscribe_domain/tv4/GFPV6XZQGE4TONRXMZRC2OJYMMZS2N3DHFRS2OJUGU4C2MJZGNSTQYJTGZTDSZLG/optout"),
    previous_subject = Some("Hey {{first_name}}"),
    signature = Some("Shubham Kudekar"),
    sender_phone_number = None,
    calendar_link = Some("http://localhost:3000/shubham-kudekar/15min/?-151PRDGct87LPw16ziecZqPSae4AiIaEGgtvdayjyo&tid=1")
  )

  val channel_follow_up_data = PreviousFollowUpData.AutoEmailFollowUp(
    email_thread_id = Some(1), // it is 1 before itself we are just fixing compile error now Date: 16/03/2023
    from_name = "Prateek Bhat",
    base_body = "This is previous test body",
    body = "This is previous test body",
    subject = "Hey {{first_name}}",
    from_email = "<EMAIL>",
    is_edited_preview_email = false,
  )

  val previousFollowUp = PreviousFollowUp(
    channel_follow_up_data = channel_follow_up_data,
    sent_at = DateTime.parse("2022-3-27"),
    timezone = "IN",
    step_id = Some(1),
    completed_reason = None
  )
  val campaignEditedPreviewEmail = CampaignEditedPreviewEmail(
    campaignId = 1,
    prospectId = 1,
    stepId = 1,
    editedByAccountId = 1,
    editedSubject = "this is previous email",
    editedBody = "test body"
  )

  val emailSendDetail = EmailSendDetail(
    id = 0,
    org_id = 0,
    account_id = 1,
    team_id = 1,
    prospect_id = Some(1),
    sendEmailFromCampaignDetails = None,
    subject = "subject",
    body =
      s"""<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
     <html xmlns="http://www.w3.org/1999/xhtml">
     <head>
     <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
     <title></title>
     <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
     <style type="text/css">
      p { margin: 0; font-size: '14px' }

      .prev-reply {
        margin-left: 1em;
        color: #500050;
        padding-top: 20px;
      }

      .time-details {}

      .reply {
        border-left: 1px solid black;
        padding-left: 1em;
      }
     </style>
     </head>
     <body>test bodyemailsignature
  <img width="1" height="1" style="display: block;" alt="" src="https://default_tracking_domain/ot2/ga======/open">
     </body>
     </html>""".stripMargin.trim
    ,
    text_body = "test body",
    scheduled_from_campaign = false,
    sender_email_settings_id = 1,
    sender_message_id_suffix = "Hello",
    receiving_email_settings_id = 11,
    service_provider = EmailServiceProvider.GMAIL_API,
    via_gmail_smtp = None,
    smtp_username = None,
    smtp_host = None,
    smtp_port = None,
    smtp_password = None,
    oauth2_refresh_token = None,
    oauth2_access_token = None,
    oauth2_access_token_expires_at = None,
    email_domain = None,
    api_key = None,
    mailgun_region = None,
    from_email = "<EMAIL>",
    from_name = "Shubham Kudekar",
    to_emails = Seq(IEmailAddress(email = "<EMAIL>")),
    reply_to_email = None,
    reply_to_name = None,
    cc_emails = Seq(),
    bcc_emails = Seq(),
    in_reply_to_id = None,
    in_reply_to_references_header = None,
    in_reply_to_sent_at = None,
    in_reply_to_subject = None,
    in_reply_to_outlook_msg_id = None,
    sender_email_setting_paused_till = None,
    custom_tracking_domain = None,
    rep_tracking_host_id = 1,
    gmail_fbl = None,
    list_unsubscribe_header = None,
    email_thread_id = None,
    send_plain_text_email = Some(false),
    gmail_thread_id = None
  )
  val emailToBeSent = EmailToBeSent(
    to_emails = Seq(IEmailAddress(email = "<EMAIL>")),
    from_email = "<EMAIL>",
    cc_emails = Seq(),
    bcc_emails = Seq(),
    from_name = "Shubham",
    reply_to_email = None,
    reply_to_name = None,
    subject = "subject",
    textBody = "text body",
    htmlBody = "html body",
    message_id = Some("1"),
    references_header = None,
    in_reply_to_id = None,
    in_reply_to_references_header = None,
    in_reply_to_sent_at = None,
    sender_email_settings_id = 1,
    email_thread_id = Some(1),
    gmail_msg_id = Some("1"),
    gmail_thread_id = Some("1"),
    outlook_msg_id = None,
    outlook_conversation_id = None,
    outlook_response_json = None,
    gmail_fbl = None,
    list_unsubscribe_header = None,
    hasCustomTrackingDomain = true,
    rep_smtp_reverse_dns_host = None
  )

  val spamTestNotificationData = SpamTestNotificationData(
    first_name = "Shubham",
    last_name = "Kudekar",
    email = "<EMAIL>",
    campaign_status = CampaignStatus.NOT_STARTED,
    account_id = 1,
    team_id = 1
  )

  val channel: ChannelType = ChannelType.EmailChannel


  val orgPlan = OrgPlanFixture.orgPlanFixture

  val orgMetadata = OrgMetadataFixture.orgMetadataFixture2

  val orgSettings = OrgSettings(
    enable_ab_testing = true,
    disable_force_send = false,
    bulk_sender = false,
    allow_2fa = false,
    show_2fa_setting = false,
    enforce_2fa = false,
    allow_native_crm_integration = false,
    agency_option_allow_changing = false,
    agency_option_show = false)

  val org = OrganizationWithCurrentData(
    id = 1,
    name = "Animesh",
    owner_account_id = 1,
    counts = OrgCountDataFixture.orgCountData_default,
    settings = orgSettings,
    plan = orgPlan,
    is_agency = true,
    trial_ends_at = DateTime.now().plusDays(10),
    error = None,
    error_code = None,
    paused_till = None,
    errors = Seq(),
    warnings = Seq(),
    via_referral = true,
    org_metadata = orgMetadata
  )


  val account: Account = Account(
    id = AccountUuid("account_uuid"),
    internal_id = 1,
    email = "<EMAIL>",
    email_verification_code = None,
    email_verification_code_created_at = None,
    created_at = DateTime.now().minusDays(1000),
    first_name = Some("Shubham"),
    last_name = Some("Kudekar"),
    company = Some("SK"),
    timezone = None,
    profile = AccountProfileInfo(
      first_name = "Shubham",
      last_name = "Kudekar",
      company = None,
      timezone = None,
      country_code = None,
      mobile_country_code = None,
      mobile_number = None,
      twofa_enabled = false,
      has_gauthenticator = false,
      weekly_report_emails = None,
      scheduled_for_deletion_at = None,
      onboarding_phone_number = Some("+************")
    ),
    org_role = Some(OrganizationRole.OWNER),
    teams = Seq(),
    account_type = AccountType.AGENCY,
    org = org,

    active = true,
    email_notification_summary = "dSFA",
    account_metadata = AccountMetadata(
      is_profile_onboarding_done = Some(true)
    ),
    email_verified = true,
    signupType = None,
    account_access = AccountAccess(
      inbox_access = false
    ),
    calendar_account_data = None
  )


  describe("SpamTestCronService.createSpamTest") {


    it("should fail because no spam test found") {
      (() => spamTestDAO.findOneForSendingTests()).expects().returning(None)

      val resultF = spamTestCronService.createSpamTest()
      resultF.map { result =>
        assert(result == None)
      }.recover { case e => assert(false) }
    }

    it("should fail because it throws error while updating  error message ") {
      (() => spamTestDAO.findOneForSendingTests())
        .expects().returning(Some(someSpamTest.copy(campaign_id = None)))
      (spamTestDAO.updateError(_: Long, _: String)).expects(2, *).returning(Failure(e))

      val resultF = spamTestCronService.createSpamTest()
      resultF.map { result =>
        assert(result == None)
      }.recover { case e => assert(false) }
    }

    it("should fail because it success while updating  error message  in db") {
      (() => spamTestDAO.findOneForSendingTests())
        .expects().returning(Some(someSpamTest.copy(campaign_id = None)))
      (spamTestDAO.updateError(_: Long, _: String)).expects(2, *).returning(Success(1))

      val resultF = spamTestCronService.createSpamTest()
      resultF.map { result =>
        assert(result == None)
      }.recover { case e => assert(false) }
    }

    it("should fail because it failed while getting campaign details") {
      (() => spamTestDAO.findOneForSendingTests())
        .expects().returning(Some(someSpamTest))
      (campaignService.findBasicDetails(_: Long, _: TeamId)).expects(1, TeamId(1L))
        .returning(Failure(e))

      val resultF = spamTestCronService.createSpamTest()
      resultF.map { result =>
        assert(result == None)
      }.recover { case e => assert(false) }
    }

    it("should fail because it  prospectObject is empty and it failed to update error in db ") {
      (() => spamTestDAO.findOneForSendingTests())
        .expects().returning(Some(someSpamTest))
      (campaignService.findBasicDetails(_: Long, _: TeamId)).expects(1, TeamId(1L))
        .returning(Success(Some(campaignBasicDetails)))
      (prospectDAOService.findOneByCampaignId(_: Long, _: Long, _: SRLogger)).expects(1, 1, logger).returning(None)
      (spamTestDAO.updateError(_: Long, _: String)).expects(2, *).returning(Failure(e))


      val resultF = spamTestCronService.createSpamTest()
      resultF.map { result =>
        assert(result == None)
      }.recover { case e => assert(false) }
    }
    it("should fail because it  prospectObject is empty and it success to update error in db ") {
      (() => spamTestDAO.findOneForSendingTests())
        .expects().returning(Some(someSpamTest))
      (campaignService.findBasicDetails(_: Long, _: TeamId)).expects(1, TeamId(1L))
        .returning(Success(Some(campaignBasicDetails)))
      (prospectDAOService.findOneByCampaignId(_: Long, _: Long, _: SRLogger)).expects(1, 1, logger).returning(None)
      (spamTestDAO.updateError(_: Long, _: String)).expects(2, *).returning(Success(1))


      val resultF = spamTestCronService.createSpamTest()
      resultF.map { result =>
        assert(result == None)
      }.recover { case e => assert(false) }
    }
    it("should fail because emailservicecompanion returned failure and spamtestdAO failed to update error in db") {
      (() => spamTestDAO.findOneForSendingTests())
        .expects().returning(Some(someSpamTest))
      (campaignService.findBasicDetails(_: Long, _: TeamId)).expects(1, TeamId(1L))
        .returning(Success(Some(campaignBasicDetails)))
      (prospectDAOService.findOneByCampaignId(_: Long, _: Long, _: SRLogger)).expects(1, 1, logger).returning(Some(prospectObject))
      (prospectAccountDAO.find(_: Long, _: Long)).expects(1, 1).returning(Some(ProspectAccountFixture.prospectAccount))
      (emailSettingDAO.find(_: Long, _: EmailSettingStatus)).expects(1, EmailSettingStatus.Active).returning(Some(emailSetting))
      (campaignService.find(_: Long, _: Long, _: Option[Account])(using _: SRLogger)).expects(1, 1, None, *).returning(Some(campaignWithStatsAndEmail))
      (campaignStepVariantDAO.findByStepId(_: Long)).expects(1).returning(Seq(campaignStepVariant))
      (() => repTrackingHostService.getRepTrackingHosts())
        .expects().returning(Success(Seq(repTrackingHosts)))
      (accountServiceMock.find(_: Long)(_: SRLogger))
        .expects(1, *).returning(Success(account))
      (spamTestDAO.updateError(_: Long, _: String)).expects(2, *).returning(Failure(e))
      val resultF = spamTestCronService.createSpamTest()

      resultF.map { result =>
        assert(result == None)
      }.recover { case e => assert(false) }
    }
    it("should fail because emailservicecompanion returned failure and spamtestdAO success to update error in db") {
      (() => spamTestDAO.findOneForSendingTests())
        .expects().returning(Some(someSpamTest))
      (campaignService.findBasicDetails(_: Long, _: TeamId)).expects(1, TeamId(1L))
        .returning(Success(Some(campaignBasicDetails)))
      (prospectDAOService.findOneByCampaignId(_: Long, _: Long, _: SRLogger)).expects(1, 1, logger).returning(Some(prospectObject))
      (prospectAccountDAO.find(_: Long, _: Long)).expects(1, 1).returning(Some(ProspectAccountFixture.prospectAccount))
      (emailSettingDAO.find(_: Long, _: EmailSettingStatus)).expects(1, EmailSettingStatus.Active).returning(Some(emailSetting))
      (campaignService.find(_: Long, _: Long, _: Option[Account])(using _: SRLogger)).expects(1, 1, None, *).returning(Some(campaignWithStatsAndEmail))
      (campaignStepVariantDAO.findByStepId(_: Long)).expects(1).returning(Seq(campaignStepVariant))
      (() => repTrackingHostService.getRepTrackingHosts())
        .expects().returning(Success(Seq(repTrackingHosts)))
      (accountServiceMock.find(_: Long)(_: SRLogger))
        .expects(1, *).returning(Success(account))
      (spamTestDAO.updateError(_: Long, _: String)).expects(2, *).returning(Success(1))
      val resultF = spamTestCronService.createSpamTest()

      resultF.map { result =>
        assert(result == None)
      }.recover { case e => assert(false) }
    }

    it("should fail because emailservicecompanion returned failure") {
      (() => spamTestDAO.findOneForSendingTests())
        .expects().returning(Some(someSpamTest))
      (campaignService.findBasicDetails(_: Long, _: TeamId)).expects(1, TeamId(1L))
        .returning(Success(Some(campaignBasicDetails)))
      (prospectDAOService.findOneByCampaignId(_: Long, _: Long, _: SRLogger)).expects(1, 1, logger).returning(Some(prospectObject))
      (prospectAccountDAO.find(_: Long, _: Long)).expects(1, 1).returning(Some(ProspectAccountFixture.prospectAccount))
      (emailSettingDAO.find(_: Long, _: EmailSettingStatus)).expects(1, EmailSettingStatus.Active).returning(Some(emailSetting))
      (campaignService.find(_: Long, _: Long, _: Option[Account])(using _: SRLogger)).expects(1, 1, None, *).returning(Some(campaignWithStatsAndEmail))
      (campaignStepVariantDAO.findByStepId(_: Long)).expects(1).returning(Seq(campaignStepVariant))
      (() => repTrackingHostService.getRepTrackingHosts())
        .expects().returning(Success(Seq(repTrackingHosts)))
      (accountServiceMock.find(_: Long)(_: SRLogger))
        .expects(1, *).returning(Success(account))
      (emailScheduledDAOService.getPreviousSentSteps(_: Long, _: Long, _: TeamId, _: Boolean)(using _: SRLogger))

        .expects(1, 1, TeamId(id = 1), *, *).returning(Failure(e))
      (spamTestDAO.updateError(_: Long, _: String)).expects(2, *).returning(Failure(e))

      (emailBodyService._getSignatureWithTrackingLinks(_: String, _: EmailsScheduledUuid, _: Option[String], _: String, _: Seq[String], _: TeamId, _: OrgId)(using _: SRLogger))
        .expects("Shubham Kudekar", EmailsScheduledUuid(AppConfig.dummy_email_tracking_uuid), None, "default_tracking_domain", Seq("url"), TeamId(1), OrgId(0), *)
        .returning("Animesh kumar")


      val resultF = spamTestCronService.createSpamTest()

      resultF.map { result =>
        assert(result == None)
      }.recover { case e => assert(false) }
    }

    it("should fail because emailscheduledDao throws empty Seq of previous follow up") {
      (() => spamTestDAO.findOneForSendingTests())
        .expects().returning(Some(someSpamTest))
      (campaignService.findBasicDetails(_: Long, _: TeamId)).expects(1, TeamId(1L))
        .returning(Success(Some(campaignBasicDetails)))
      (prospectDAOService.findOneByCampaignId(_: Long, _: Long, _: SRLogger)).expects(1, 1, logger).returning(Some(prospectObject))
      (prospectAccountDAO.find(_: Long, _: Long)).expects(1, 1).returning(Some(ProspectAccountFixture.prospectAccount))
      (emailSettingDAO.find(_: Long, _: EmailSettingStatus)).expects(1, EmailSettingStatus.Active).returning(Some(emailSetting))
      (campaignService.find(_: Long, _: Long, _: Option[Account])(using _: SRLogger)).expects(1, 1, None, *).returning(Some(campaignWithStatsAndEmail))
      (campaignStepVariantDAO.findByStepId(_: Long)).expects(1).returning(Seq(campaignStepVariant))
      (() => repTrackingHostService.getRepTrackingHosts())
        .expects().returning(Success(Seq(repTrackingHosts)))
      (accountServiceMock.find(_: Long)(_: SRLogger))
        .expects(1, *).returning(Success(account))
      (emailScheduledDAOService.getPreviousSentSteps(_: Long, _: Long, _: TeamId, _: Boolean)(using _: SRLogger))

        .expects(1, 1, TeamId(id = 1), *, *).returning(Success(Seq()))
      (spamTestDAO.updateError(_: Long, _: String)).expects(2, *).returning(Failure(e))

      (emailBodyService._getSignatureWithTrackingLinks(_: String, _: EmailsScheduledUuid, _: Option[String], _: String, _: Seq[String], _: TeamId, _: OrgId)(using _: SRLogger))
        .expects("Shubham Kudekar", EmailsScheduledUuid(AppConfig.dummy_email_tracking_uuid), None, "default_tracking_domain", Seq("url"), TeamId(1), OrgId(0), *)
        .returning("Animesh kumar")

      val resultF = spamTestCronService.createSpamTest()

      resultF.map { result =>
        assert(result == None)
      }.recover { case e => assert(false) }
    }
    it("should fail because emailscheduledDao throws some previousFollowsUp and campaignEditedPreviewEmailDAO throws Failure") {
      (() => spamTestDAO.findOneForSendingTests())
        .expects().returning(Some(someSpamTest))
      (campaignService.findBasicDetails(_: Long, _: TeamId)).expects(1, TeamId(1L))
        .returning(Success(Some(campaignBasicDetails)))
      (prospectDAOService.findOneByCampaignId(_: Long, _: Long, _: SRLogger)).expects(1, 1, logger).returning(Some(prospectObject))
      (prospectAccountDAO.find(_: Long, _: Long)).expects(1, 1).returning(Some(ProspectAccountFixture.prospectAccount))
      (emailSettingDAO.find(_: Long, _: EmailSettingStatus)).expects(1, EmailSettingStatus.Active).returning(Some(emailSetting))
      (campaignService.find(_: Long, _: Long, _: Option[Account])(using _: SRLogger)).expects(1, 1, None, *).returning(Some(campaignWithStatsAndEmail))
      (campaignStepVariantDAO.findByStepId(_: Long)).expects(1).returning(Seq(campaignStepVariant))
      (() => repTrackingHostService.getRepTrackingHosts())
        .expects().returning(Success(Seq(repTrackingHosts)))
      (accountServiceMock.find(_: Long)(_: SRLogger))
        .expects(1, *).returning(Success(account))
      (emailScheduledDAOService.getPreviousSentSteps(_: Long, _: Long, _: TeamId, _: Boolean)(using _: SRLogger))

        .expects(1, 1, TeamId(id = 1), *, *).returning(Success(Seq(previousFollowUp)))
      (campaignEditedPreviewEmailDAO.find)
        .expects(Some(1.toLong), None, 1)
        .returning(Failure(e))
      (spamTestDAO.updateError(_: Long, _: String)).expects(2, *).returning(Failure(e))

      (emailBodyService._getSignatureWithTrackingLinks(_: String, _: EmailsScheduledUuid, _: Option[String], _: String, _: Seq[String], _: TeamId, _: OrgId)(using _: SRLogger))
        .expects("Shubham Kudekar", EmailsScheduledUuid(AppConfig.dummy_email_tracking_uuid), None, "default_tracking_domain", Seq("url"), TeamId(1), OrgId(0), *)
        .returning("Animesh kumar")


      val resultF = spamTestCronService.createSpamTest()

      resultF.map { result =>
        assert(result == None)
      }.recover { case e => assert(false) }
    }


    it("should fail because templateService.render throws error") {
      (() => spamTestDAO.findOneForSendingTests())
        .expects().returning(Some(someSpamTest))
      (campaignService.findBasicDetails(_: Long, _: TeamId)).expects(1, TeamId(1L))
        .returning(Success(Some(campaignBasicDetails)))
      (prospectDAOService.findOneByCampaignId(_: Long, _: Long, _: SRLogger)).expects(1, 1, logger).returning(Some(prospectObject))
      (prospectAccountDAO.find(_: Long, _: Long)).expects(1, 1).returning(Some(ProspectAccountFixture.prospectAccount))
      (emailSettingDAO.find(_: Long, _: EmailSettingStatus)).expects(1, EmailSettingStatus.Active).returning(Some(emailSetting))
      (campaignService.find(_: Long, _: Long, _: Option[Account])(using _: SRLogger)).expects(1, 1, None, *).returning(Some(campaignWithStatsAndEmail))
      (campaignStepVariantDAO.findByStepId(_: Long)).expects(1).returning(Seq(campaignStepVariant))
      (() => repTrackingHostService.getRepTrackingHosts())
        .expects().returning(Success(Seq(repTrackingHosts)))
      (accountServiceMock.find(_: Long)(_: SRLogger))
        .expects(1, *).returning(Success(account))

      (emailBodyService._getSignatureWithTrackingLinks(_: String, _: EmailsScheduledUuid, _: Option[String], _: String, _: Seq[String], _: TeamId, _: OrgId)(using _: SRLogger))
        .expects("Shubham Kudekar", EmailsScheduledUuid(AppConfig.dummy_email_tracking_uuid), None, "default_tracking_domain", Seq("url"), TeamId(1), OrgId(0), *)
        .returning("Shubham Kudekar")

      (emailScheduledDAOService.getPreviousSentSteps(_: Long, _: Long, _: TeamId, _: Boolean)(using _: SRLogger))

        .expects(1, 1, TeamId(id = 1), *, *).returning(Success(Seq(previousFollowUp)))
      (campaignEditedPreviewEmailDAO.find)
        .expects(Some(1.toLong), None, 1)
        .returning(Success(Seq(campaignEditedPreviewEmail)))

      (templateService.render(_: String, _: ProspectObject, _: InternalMergeTagValuesForProspect, _: ChannelType)(using _: SRLogger))
        .expects("test body", prospectObject, internalMergeTagValuesForProspect.copy(calendar_link = None), channel, *)
        .returning(Success("test body"))

      (templateService.render(_: String, _: ProspectObject, _: InternalMergeTagValuesForProspect, _: ChannelType)(using _: SRLogger))
        .expects("this is previous email", prospectObject, internalMergeTagValuesForProspect.copy(calendar_link = None), channel, *)
        .returning(Failure(e))
      (spamTestDAO.updateError(_: Long, _: String)).expects(2, *).returning(Failure(e))


      val resultF = spamTestCronService.createSpamTest()

      resultF.map { result =>
        assert(result == None)
      }.recover { case e => assert(false) }
    }

    it("should fail because spamTestDao on Update returns None") {

      (() => spamTestDAO.findOneForSendingTests())
        .expects().returning(Some(someSpamTest))
      (campaignService.findBasicDetails(_: Long, _: TeamId)).expects(1, TeamId(1L))
        .returning(Success(Some(campaignBasicDetails)))
      (prospectDAOService.findOneByCampaignId(_: Long, _: Long, _: SRLogger)).expects(1, 1, logger).returning(Some(prospectObject))
      (prospectAccountDAO.find(_: Long, _: Long)).expects(1, 1).returning(Some(ProspectAccountFixture.prospectAccount))
      (emailSettingDAO.find(_: Long, _: EmailSettingStatus)).expects(1, EmailSettingStatus.Active).returning(Some(emailSetting))
      (campaignService.find(_: Long, _: Long, _: Option[Account])(using _: SRLogger)).expects(1, 1, None, *).returning(Some(campaignWithStatsAndEmail))
      (campaignStepVariantDAO.findByStepId(_: Long)).expects(1).returning(Seq(campaignStepVariant))
      (() => repTrackingHostService.getRepTrackingHosts())
        .expects().returning(Success(Seq(repTrackingHosts)))
      (accountServiceMock.find(_: Long)(_: SRLogger))
        .expects(1, *).returning(Success(account))

      (emailBodyService._getSignatureWithTrackingLinks(_: String, _: EmailsScheduledUuid, _: Option[String], _: String, _: Seq[String], _: TeamId, _: OrgId)(using _: SRLogger))
        .expects("Shubham Kudekar", EmailsScheduledUuid(AppConfig.dummy_email_tracking_uuid), None, "default_tracking_domain", Seq("url"), TeamId(1), OrgId(0), *)
        .returning("Shubham Kudekar")

      (emailScheduledDAOService.getPreviousSentSteps(_: Long, _: Long, _: TeamId, _: Boolean)(using _: SRLogger))

        .expects(1, 1, TeamId(id = 1), *, *).returning(Success(Seq(previousFollowUp)))
      (campaignEditedPreviewEmailDAO.find)
        .expects(Some(1.toLong), None, 1)
        .returning(Success(Seq(campaignEditedPreviewEmail)))
      
      (templateService.render(_: String, _: ProspectObject, _: InternalMergeTagValuesForProspect, _: ChannelType)(using _: SRLogger))
        .expects("test body", prospectObject, internalMergeTagValuesForProspect.copy(calendar_link = None), channel, *)
        .returning(Success("test body"))

      (templateService.render(_: String, _: ProspectObject, _: InternalMergeTagValuesForProspect, _: ChannelType)(using _: SRLogger))
        .expects("this is previous email", prospectObject, internalMergeTagValuesForProspect.copy(calendar_link = None), channel, *)
        .returning(Success("subject"))

      (emailBodyService._getBodyWithTrackingLinksV1(_: String, _: EmailsScheduledUuid, _: Option[String], _: String, _: Seq[String], _: TeamId,  _: OrgId)(using _: SRLogger))
        .expects("test body", EmailsScheduledUuid(AppConfig.dummy_email_tracking_uuid), None, "default_tracking_domain", Seq("url"), TeamId(1), OrgId(0), *)
        .returning("test body")

      (emailSpamTester.sendEmailToMT(_: String, _: EmailSendDetail, _: String, _: Option[String])(_: WSClient, _: ExecutionContext, _: SRLogger)).
        expects("someTest", *, "rep_mail_server_host", None, *, *, *)
        .returning(Future.successful(emailToBeSent))
      (spamTestDAO.update(_: Long, _: UpdateSpamTest, _: TeamId)).expects(2, *, *).returning(Success(None))
      val resultF = spamTestCronService.createSpamTest()

      resultF.map { result =>
        assert(result == None)
      }.recover { case e => assert(false) }
    }

    it("should Success because spamTest Was Created Successfully") {

      (() => spamTestDAO.findOneForSendingTests())
        .expects().returning(Some(someSpamTest))
      (campaignService.findBasicDetails(_: Long, _: TeamId)).expects(1, TeamId(1L))
        .returning(Success(Some(campaignBasicDetails)))
      (prospectDAOService.findOneByCampaignId(_: Long, _: Long, _: SRLogger)).expects(1, 1, logger).returning(Some(prospectObject))
      (prospectAccountDAO.find(_: Long, _: Long)).expects(1, 1).returning(Some(ProspectAccountFixture.prospectAccount))
      (emailSettingDAO.find(_: Long, _: EmailSettingStatus)).expects(1, EmailSettingStatus.Active).returning(Some(emailSetting))
      (campaignService.find(_: Long, _: Long, _: Option[Account])(using _: SRLogger)).expects(1, 1, None, *).returning(Some(campaignWithStatsAndEmail))
      (campaignStepVariantDAO.findByStepId(_: Long)).expects(1).returning(Seq(campaignStepVariant))
      (() => repTrackingHostService.getRepTrackingHosts())
        .expects().returning(Success(Seq(repTrackingHosts)))
      (accountServiceMock.find(_: Long)(_: SRLogger))
        .expects(1, *).returning(Success(account))

      (emailBodyService._getSignatureWithTrackingLinks(_: String, _: EmailsScheduledUuid, _: Option[String], _: String, _: Seq[String], _: TeamId, _: OrgId)(using _: SRLogger))
        .expects("Shubham Kudekar", EmailsScheduledUuid(AppConfig.dummy_email_tracking_uuid), None, "default_tracking_domain", Seq("url"), TeamId(1), OrgId(0), *)
        .returning("Shubham Kudekar")

      (emailScheduledDAOService.getPreviousSentSteps(_: Long, _: Long, _: TeamId, _: Boolean)(using _: SRLogger))

        .expects(1, 1, TeamId(id = 1), *, *).returning(Success(Seq(previousFollowUp)))
      (campaignEditedPreviewEmailDAO.find)
        .expects(Some(1.toLong), None, 1)
        .returning(Success(Seq(campaignEditedPreviewEmail)))

      (templateService.render(_: String, _: ProspectObject, _: InternalMergeTagValuesForProspect, _: ChannelType)(using _: SRLogger))
        .expects("test body", prospectObject, internalMergeTagValuesForProspect.copy(calendar_link = None), channel, *)
        .returning(Success("test body"))

      (templateService.render(_: String, _: ProspectObject, _: InternalMergeTagValuesForProspect, _: ChannelType)(using _: SRLogger))
        .expects("this is previous email", prospectObject, internalMergeTagValuesForProspect.copy(calendar_link = None), channel, *)

        .returning(Success("subject"))

      (emailBodyService._getBodyWithTrackingLinksV1(_: String, _: EmailsScheduledUuid, _: Option[String], _: String, _: Seq[String], _: TeamId, _: OrgId)(using _: SRLogger))
        .expects("test body", EmailsScheduledUuid(AppConfig.dummy_email_tracking_uuid), None, "default_tracking_domain", Seq("url"), TeamId(1), OrgId(0), *)
        .returning("test body")

      (emailSpamTester.sendEmailToMT(_: String, _: EmailSendDetail, _: String, _: Option[String])(_: WSClient, _: ExecutionContext, _: SRLogger)).
        expects("someTest", *, "rep_mail_server_host", None, *, *, *)
        .returning(Future.successful(emailToBeSent))
      (spamTestDAO.update(_: Long, _: UpdateSpamTest, _: TeamId)).expects(2, *, *).returning(Success(Some(someSpamTest)))
      val resultF = spamTestCronService.createSpamTest()

      resultF.map { result =>
        assert(result == Some(someSpamTest))
      }.recover { case e => assert(false) }
    }

    it("should fail because spamtest creation failed and it also failed to update error in db") {

      (() => spamTestDAO.findOneForSendingTests())
        .expects().returning(Some(someSpamTest))
      (campaignService.findBasicDetails(_: Long, _: TeamId)).expects(1, TeamId(1L))
        .returning(Success(Some(campaignBasicDetails)))
      (prospectDAOService.findOneByCampaignId(_: Long, _: Long, _: SRLogger)).expects(1, 1, logger).returning(Some(prospectObject))
      (prospectAccountDAO.find(_: Long, _: Long)).expects(1, 1).returning(Some(ProspectAccountFixture.prospectAccount))
      (emailSettingDAO.find(_: Long, _: EmailSettingStatus)).expects(1, EmailSettingStatus.Active).returning(Some(emailSetting))
      (campaignService.find(_: Long, _: Long, _: Option[Account])(using _: SRLogger)).expects(1, 1, None, *).returning(Some(campaignWithStatsAndEmail))
      (campaignStepVariantDAO.findByStepId(_: Long)).expects(1).returning(Seq(campaignStepVariant))
      (() => repTrackingHostService.getRepTrackingHosts())
        .expects().returning(Success(Seq(repTrackingHosts)))
      (accountServiceMock.find(_: Long)(_: SRLogger))
        .expects(1, *).returning(Success(account))

      (emailBodyService._getSignatureWithTrackingLinks(_: String, _: EmailsScheduledUuid, _: Option[String], _: String, _: Seq[String], _: TeamId, _: OrgId)(using _: SRLogger))
        .expects("Shubham Kudekar", EmailsScheduledUuid(AppConfig.dummy_email_tracking_uuid), None, "default_tracking_domain", Seq("url"), TeamId(1), OrgId(0), *)
        .returning("Shubham Kudekar")

      (emailScheduledDAOService.getPreviousSentSteps(_: Long, _: Long, _: TeamId, _: Boolean)(using _: SRLogger))

        .expects(1, 1, TeamId(id = 1), *, *).returning(Success(Seq(previousFollowUp)))
      (campaignEditedPreviewEmailDAO.find)
        .expects(Some(1.toLong), None, 1)
        .returning(Success(Seq(campaignEditedPreviewEmail)))

      (templateService.render(_: String, _: ProspectObject, _: InternalMergeTagValuesForProspect, _: ChannelType)(using _: SRLogger))
        .expects("test body", prospectObject, internalMergeTagValuesForProspect.copy(calendar_link = None), channel, *)
        .returning(Success("test body"))

      (templateService.render(_: String, _: ProspectObject, _: InternalMergeTagValuesForProspect, _: ChannelType)(using _: SRLogger))
        .expects("this is previous email", prospectObject, internalMergeTagValuesForProspect.copy(calendar_link = None), channel, *)
        .returning(Success("subject"))

      (emailBodyService._getBodyWithTrackingLinksV1(_: String, _: EmailsScheduledUuid, _: Option[String], _: String, _: Seq[String], _: TeamId, _: OrgId)(using _: SRLogger))
        .expects("test body", EmailsScheduledUuid(AppConfig.dummy_email_tracking_uuid), None, "default_tracking_domain", Seq("url"), TeamId(1), OrgId(0), *)
        .returning("test body")

      (emailSpamTester.sendEmailToMT(_: String, _: EmailSendDetail, _: String, _: Option[String])(_: WSClient, _: ExecutionContext, _: SRLogger)).
        expects("someTest", *, "rep_mail_server_host", None, *, *, *)
        .returning(Future.successful(emailToBeSent))
      (spamTestDAO.update(_: Long, _: UpdateSpamTest, _: TeamId)).expects(2, *, *).returning(Failure(e))
      (spamTestDAO.updateError(_: Long, _: String)).expects(2, *).returning(Failure(e))

      val resultF = spamTestCronService.createSpamTest()
      assertThrows[Throwable] {
        Await.result(resultF, 3.seconds)
      }
    }
    it("should fail because spamtest creation failed and it successfully updated error in db") {

      (() => spamTestDAO.findOneForSendingTests())
        .expects().returning(Some(someSpamTest))
      (campaignService.findBasicDetails(_: Long, _: TeamId)).expects(1, TeamId(1L))
        .returning(Success(Some(campaignBasicDetails)))
      (prospectDAOService.findOneByCampaignId(_: Long, _: Long, _: SRLogger)).expects(1, 1, logger).returning(Some(prospectObject))
      (prospectAccountDAO.find(_: Long, _: Long)).expects(1, 1).returning(Some(ProspectAccountFixture.prospectAccount))
      (emailSettingDAO.find(_: Long, _: EmailSettingStatus)).expects(1, EmailSettingStatus.Active).returning(Some(emailSetting))
      (campaignService.find(_: Long, _: Long, _: Option[Account])(using _: SRLogger)).expects(1, 1, None, *).returning(Some(campaignWithStatsAndEmail))
      (campaignStepVariantDAO.findByStepId(_: Long)).expects(1).returning(Seq(campaignStepVariant))
      (() => repTrackingHostService.getRepTrackingHosts())
        .expects().returning(Success(Seq(repTrackingHosts)))
      (accountServiceMock.find(_: Long)(_: SRLogger))
        .expects(1, *).returning(Success(account))

      (emailBodyService._getSignatureWithTrackingLinks(_: String, _: EmailsScheduledUuid, _: Option[String], _: String, _: Seq[String], _: TeamId, _: OrgId)(using _: SRLogger))
        .expects("Shubham Kudekar", EmailsScheduledUuid(AppConfig.dummy_email_tracking_uuid), None, "default_tracking_domain", Seq("url"), TeamId(1), OrgId(0), *)
        .returning("Shubham Kudekar")

      (emailScheduledDAOService.getPreviousSentSteps(_: Long, _: Long, _: TeamId, _: Boolean)(using _: SRLogger))

        .expects(1, 1, TeamId(id = 1), *, *).returning(Success(Seq(previousFollowUp)))
      (campaignEditedPreviewEmailDAO.find)
        .expects(Some(1.toLong), None, 1)
        .returning(Success(Seq(campaignEditedPreviewEmail)))
      
      (templateService.render(_: String, _: ProspectObject, _: InternalMergeTagValuesForProspect, _: ChannelType)(using _: SRLogger))
        .expects("test body", prospectObject, internalMergeTagValuesForProspect.copy(calendar_link = None), channel, *)
        .returning(Success("test body"))

      (templateService.render(_: String, _: ProspectObject, _: InternalMergeTagValuesForProspect, _: ChannelType)(using _: SRLogger))
        .expects("this is previous email", prospectObject, internalMergeTagValuesForProspect.copy(calendar_link = None), channel, *)
        .returning(Success("subject"))

      (emailBodyService._getBodyWithTrackingLinksV1(_: String, _: EmailsScheduledUuid, _: Option[String], _: String, _: Seq[String], _: TeamId, _: OrgId)(using _: SRLogger))
        .expects("test body", EmailsScheduledUuid(AppConfig.dummy_email_tracking_uuid), None, "default_tracking_domain", Seq("url"), TeamId(1), OrgId(0), *)
        .returning("test body")

      (emailSpamTester.sendEmailToMT(_: String, _: EmailSendDetail, _: String, _: Option[String])(_: WSClient, _: ExecutionContext, _: SRLogger)).
        expects("someTest", *, "rep_mail_server_host", None, *, *, *)
        .returning(Future.successful(emailToBeSent))
      (spamTestDAO.update(_: Long, _: UpdateSpamTest, _: TeamId)).expects(2, *, *).returning(Failure(e))
      //      (spamTestDAO.updateError(_: Long, _: String)).expects(2, *).returning(Success(1))

      val resultF = spamTestCronService.createSpamTest()
      assertThrows[Throwable] {
        Await.result(resultF, 3.seconds)
      }
    }
  }

  describe("SpamTestCronService.spamTestCheckResults") {
    it("should fail as no spamTest is Found") {
      (() => spamTestDAO.findOneForCheckingResults())
        .expects().returning(None)

      val resultF = spamTestCronService.spamTestCheckResults()

      resultF.map { result =>
        assert(result == None)
      }.recover { case e => assert(false) }
    }
    it("should fail and send an error report email as getMtResult response not found error") {

      (() => spamTestDAO.findOneForCheckingResults())
        .expects().returning(Some(someSpamTest))
      (emailSpamTester.getMTResult(_: String)(_: WSClient, _: ExecutionContext, _: SRLogger)).
        expects("1234", *, *, *)
        .returning(Future.failed(SpamTestReportResponseNotFoundException("SomeErrorOccurred")))
      val resultF = spamTestCronService.spamTestCheckResults()

      resultF.map { result =>
        assert(result == None)
      }.recover { case e => assert(false) }
    }
    it("should fail because it failed to update error in db ") {

      (() => spamTestDAO.findOneForCheckingResults())
        .expects().returning(Some(someSpamTest))
      (emailSpamTester.getMTResult(_: String)(_: WSClient, _: ExecutionContext, _: SRLogger)).
        expects("1234", *, *, *)
        .returning(Future.failed(SpamTestReportResponseException("SomeErrorOccurred")))
      (spamTestDAO.updateError(_: Long, _: String)).expects(2, *).returning(Failure(e))
      val resultF = spamTestCronService.spamTestCheckResults()

      resultF.map { result =>
        assert(result == None)
      }.recover { case e => assert(false) }
    }
    it("should fail and send an error report email as getMtResult throws error and it fails to send error report email") {

      (() => spamTestDAO.findOneForCheckingResults())
        .expects().returning(Some(someSpamTest))
      (emailSpamTester.getMTResult(_: String)(_: WSClient, _: ExecutionContext, _: SRLogger)).
        expects("1234", *, *, *)
        .returning(Future.failed(SpamTestReportResponseException("SomeErrorOccurred")))
      (spamTestDAO.updateError(_: Long, _: String)).expects(2, *).returning(Success(1))
      (spamTestDAO.getSpamTestNotificationData)
        .expects(1).returning(Some(spamTestNotificationData))
      (emailNotificationService.sendMailFromAdmin(_: String, _: Option[String], _: String, _: String, _: Option[Boolean],
        _: Option[String], _: Option[String], _: Seq[String], _: Boolean)(_: WSClient, _: ExecutionContext, _: SRLogger)).
        expects("<EMAIL>", Some("Shubham Kudekar"), *, *, None, None, None, Seq(), true, *, *, *)
        .returning(Failure(e))
      val resultF = spamTestCronService.spamTestCheckResults()

      resultF.map { result =>
        assert(result == None)
      }.recover { case e => assert(false) }
    }
    it("should fail and send an error report email as getMtResult throws error and it successfuly sended error report email") {

      (() => spamTestDAO.findOneForCheckingResults())
        .expects().returning(Some(someSpamTest))
      (emailSpamTester.getMTResult(_: String)(_: WSClient, _: ExecutionContext, _: SRLogger)).
        expects("1234", *, *, *)
        .returning(Future.failed(SpamTestReportResponseException("SomeErrorOccurred")))
      (spamTestDAO.updateError(_: Long, _: String)).expects(2, *).returning(Success(1))
      (spamTestDAO.getSpamTestNotificationData)
        .expects(1).returning(Some(spamTestNotificationData))
      (emailNotificationService.sendMailFromAdmin(_: String, _: Option[String], _: String, _: String, _: Option[Boolean],
        _: Option[String], _: Option[String], _: Seq[String], _: Boolean)(_: WSClient, _: ExecutionContext, _: SRLogger)).
        expects("<EMAIL>", Some("Shubham Kudekar"), *, *, None, None, None, Seq(), true, *, *, *)
        .returning(Success((): Unit))
      val resultF = spamTestCronService.spamTestCheckResults()

      resultF.map { result =>
        assert(result == Some(someSpamTest))
      }.recover { case e => assert(false) }
    }
    it("should fail because update result returns None") {
      (() => spamTestDAO.findOneForCheckingResults())
        .expects().returning(Some(someSpamTest))
      (emailSpamTester.getMTResult(_: String)(_: WSClient, _: ExecutionContext, _: SRLogger)).
        expects("1234", *, *, *)
        .returning(Future.successful(Json.toJson(emailToBeSent)))
      (spamTestDAO.updateResults).expects(2, *, *).returning(Success(None))

      val resultF = spamTestCronService.spamTestCheckResults()

      resultF.map { result =>
        assert(result == None)
      }.recover { case e => assert(false) }
    }
    it("should fail because update result returns ") {
      (() => spamTestDAO.findOneForCheckingResults())
        .expects().returning(Some(someSpamTest))
      (emailSpamTester.getMTResult(_: String)(_: WSClient, _: ExecutionContext, _: SRLogger)).
        expects("1234", *, *, *)
        .returning(Future.successful(Json.toJson(emailToBeSent)))
      (spamTestDAO.updateResults)
        .expects(2, *, *).returning(Success(Some(someSpamTest)))
      (spamTestDAO.getSpamTestNotificationData)
        .expects(1).returning(None)
      val resultF = spamTestCronService.spamTestCheckResults()

      resultF.map { result =>
        assert(result == None)
      }.recover { case e => assert(false) }
    }
    it("should fail because sendEmailFromAdmin is not Successful ") {
      (() => spamTestDAO.findOneForCheckingResults())
        .expects().returning(Some(someSpamTest))
      (emailSpamTester.getMTResult(_: String)(_: WSClient, _: ExecutionContext, _: SRLogger)).
        expects("1234", *, *, *)
        .returning(Future.successful(Json.toJson(emailToBeSent)))
      (spamTestDAO.updateResults)
        .expects(2, *, *).returning(Success(Some(someSpamTest)))
      (spamTestDAO.getSpamTestNotificationData)
        .expects(1).returning(Some(spamTestNotificationData))
      (emailNotificationService.sendMailFromAdmin(_: String, _: Option[String], _: String, _: String, _: Option[Boolean],
        _: Option[String], _: Option[String], _: Seq[String], _: Boolean)(_: WSClient, _: ExecutionContext, _: SRLogger)).
        expects("<EMAIL>", Some("Shubham Kudekar"), *, *, None, None, None, Seq(), true, *, *, *)
        .returning(Failure(e))

      val resultF = spamTestCronService.spamTestCheckResults()

      resultF.map { result =>
        assert(result == None)
      }.recover { case e => assert(false) }
    }

    it("should Success  because sendEmailFromAdmin  success ") {
      (() => spamTestDAO.findOneForCheckingResults())
        .expects().returning(Some(someSpamTest))
      (emailSpamTester.getMTResult(_: String)(_: WSClient, _: ExecutionContext, _: SRLogger)).
        expects("1234", *, *, *)
        .returning(Future.successful(Json.toJson(emailToBeSent)))
      (spamTestDAO.updateResults)
        .expects(2, *, *).returning(Success(Some(someSpamTest)))
      (spamTestDAO.getSpamTestNotificationData)
        .expects(1).returning(Some(spamTestNotificationData))
      (emailNotificationService.sendMailFromAdmin(_: String, _: Option[String], _: String, _: String, _: Option[Boolean],
        _: Option[String], _: Option[String], _: Seq[String], _: Boolean)(_: WSClient, _: ExecutionContext, _: SRLogger)).
        expects("<EMAIL>", Some("Shubham Kudekar"), *, *, None, None, None, Seq(), true, *, *, *)
        .returning(Success(()))

      val resultF = spamTestCronService.spamTestCheckResults()

      resultF.map { result =>
        assert(result.isDefined)
      }.recover { case e => assert(false) }
    }


  }
}
