package app.utils.phantombuster

import org.apache.pekko.actor.ActorSystem
import api.accounts.EmailScheduledIdOrTaskId.TaskId
import api.accounts.{AccountUuid, TeamId}
import api.accounts.models.{AccountId, OrgId}
import api.accounts.service.AccountOrgBillingRelatedService
import api.phantombuster.{ExecuteAutoLinkedinTasksError, LinkedinMessageThreadUrl, LinkedinSessionCreator, PhantomBusterAgent, PhantomBusterAgentId, PhantomBusterAgentService, PhantomBusterApi, PhantomBusterApiKey, PhantomBusterApiKeyAndAgentId, PhantomBusterApiKeyDetails, PhantomBusterApiKeysService, PhantomBusterContainerId, PhantomBusterExecutionLogsService, PhantomBusterLinkedinMessage, PhantomBusterScript, PhantomBusterService, PhantomBusterWebhookTeamSecretKey, TeamIdAndInternalApiKeyId}
import api.campaigns.services.{CampaignId, CampaignProspectService, CampaignService}
import api.emails.OptionCampaignIdAndOptionProspectIdAndOptionEmailSettingId
import api.linkedin.LinkedinSettingService
import api.linkedin.models.{LinkedinSessionCookieAndUserAgent, LinkedinSettingUuid, UserAgent}
import api.linkedin_message_threads.{GroupedLinkedinThreadsForMessageScraping, LinkedinMessageThreadsService}
import api.linkedin_messages.LinkedinMessagesService
import api.phantombuster_proxy.{PhantomBusterProxyService, ProxyIPAndCredentials}
import api.prospects.ProspectUuid
import api.prospects.dao_service.ProspectDAOService
import api.prospects.models.ProspectId
import api.sr_audit_logs.services.EventLogService
import api.tasks.models.{Assignee, CommonLinkedinTaskDetails, Task, TaskCreatedVia, TaskData, TaskPriority, TaskProspect, TaskStatus, TaskType, TasksGroupedByTypeAndLinkedinSetting}
import api.tasks.services.{TaskDaoService, TaskService}
import api.team.service.TeamService
import app.test_fixtures.prospect.ProspectFixtures
import eventframework.{ProspectObject, ProspectObjectInternal}
import io.smartreach.esp.api.emails.EmailSettingId
import org.joda.time.DateTime
import org.joda.time.format.DateTimeFormat
import org.scalamock.scalatest.AsyncMockFactory
import org.scalatest.funspec.AsyncFunSpec
import play.api.libs.json.JodaWrites.*
import play.api.libs.json.JodaReads.*
import play.api.libs.json.{JsObject, JsValue, Json, __}
import play.api.libs.ws.ahc.AhcWSClient
import utils.SRLogger
import play.api.libs.ws.WSClient
import utils.GCP.CloudStorage
import utils.random.SrRandomUtils
import utils.uuid.SrUuidUtils

import scala.concurrent.ExecutionContext
import scala.util.{Failure, Success}
import scala.concurrent.Future
import api.team.service.PhantomBusterTeamService
import utils.testapp.TestAppExecutionContext

class PhantomBusterServiceSpec extends AsyncFunSpec with AsyncMockFactory {

  val teamService = mock[TeamService]
  val taskService = mock[TaskService]
  val prospectDAOService = mock[ProspectDAOService]
  val linkedinSettingService = mock[LinkedinSettingService]
  val linkedinMessagesService = mock[LinkedinMessagesService]
  val linkedinMessageThreadsService = mock[LinkedinMessageThreadsService]
  val campaignService = mock[CampaignService]
  val srUuidUtils = mock[SrUuidUtils]
  val phantomBusterApi = mock[PhantomBusterApi]
  val cloudStorage = mock[CloudStorage]
  val eventLogService = mock[EventLogService]
  val campaignProspectService = mock[CampaignProspectService]
  val phantomBusterApiKeysService = mock[PhantomBusterApiKeysService]
  val phantomBusterExecutionLogsService = mock[PhantomBusterExecutionLogsService]
  val phantomBusterAgentService = mock[PhantomBusterAgentService]
  val srRandomUtils = mock[SrRandomUtils]
  val linkedinSessionCreator = mock[LinkedinSessionCreator]
  val accountOrgBillingRelatedService = mock[AccountOrgBillingRelatedService]
  val phantomBusterProxyService = mock[PhantomBusterProxyService]
  val phantomBusterTeamService = mock[PhantomBusterTeamService]

  val phantomBusterService = new PhantomBusterService(
    teamService = teamService,
    taskService = taskService,
    phantomBusterAgentService = phantomBusterAgentService,
    phantomBusterApiKeysService = phantomBusterApiKeysService,
    phantomBusterExecutionLogsService = phantomBusterExecutionLogsService,
    phantomBusterProxyService = phantomBusterProxyService,
    prospectDAOService = prospectDAOService,
    srUuidUtils = srUuidUtils,
    srRandomUtils = srRandomUtils,
    cloudStorage = cloudStorage,
    eventLogService = eventLogService,
    linkedinSettingService = linkedinSettingService,
    linkedinMessagesService = linkedinMessagesService,
    linkedinMessageThreadsService = linkedinMessageThreadsService,
    campaignService = campaignService,
    campaignProspectService = campaignProspectService,
    linkedinSessionCreator = linkedinSessionCreator,
    accountOrgBillingRelatedService = accountOrgBillingRelatedService,
    phantomBusterApi = phantomBusterApi,
    phantomBusterTeamService = phantomBusterTeamService
  )

  implicit lazy val system: ActorSystem = TestAppExecutionContext.actorSystem
  implicit lazy val ec: ExecutionContext = system.dispatcher
  implicit lazy val wSClient: AhcWSClient = TestAppExecutionContext.wsClient
  given Logger: SRLogger = new SRLogger("PhantomBusterServiceSpec")

  val error = new Exception("-- ERROR -- DEBUGGING -- REQUIRED --")
  val aDate = DateTime.now().minusDays(3)

  val teamId = TeamId(6412L)
  val orgId = OrgId(3L)
  val teamAccountId: Long = 29L
  val accountId = AccountId(23L)
  val campaignId = CampaignId(7L)
  val taskId = "task_1234abcd5678"
  val prospectId = ProspectId(19L)
  val linkedinSessionCookieAndUserAgent = LinkedinSessionCookieAndUserAgent(
    cookie = "I'M_VENGEANCE",
    userAgent = UserAgent("BATMAN")
  )
  val linkedinSettingUuid = "linkedin_account_910efgh1112"
  val phantomBusterApiKey = PhantomBusterApiKey("TEST_API_KEY")
  val phantomBusterContainerId = PhantomBusterContainerId("************")
  val phantomBusterAgentId = PhantomBusterAgentId("**********")
  val phantomBusterSalesNavigatorInmailScript = PhantomBusterScript.SalesNavigatorInmail
  val phantomBusterLinkedinMessageSenderScript = PhantomBusterScript.LinkedinMessageSender
  val phantomBusterLinkedinInboxScraperScript = PhantomBusterScript.LinkedinInboxScraper
  val phantomBusterLinkedinMessageThreadScraperScript = PhantomBusterScript.LinkedinMessageThreadScraper
  val phantomBusterWebhookTeamSecretKey = PhantomBusterWebhookTeamSecretKey("2UVNCSJcbacb432cbjHDCC___team_3")
  val randomString = "2UVnfjsd_34njsSNC8432_vnkssd"

  val autoInmailTask = Task(
    task_id = taskId,
    is_auto_task = true,
    task_type = TaskType.AutoLinkedinInmail,
    added_by = 11L,
    task_data = TaskData.AutoLinkedinInmail(
      subject = "Avengers Assemble",
      body = "I am Iron Man."
    ),
    status = TaskStatus.Due(due_at = DateTime.now()),
    assignee = Some(Assignee(id = accountId.id, name = "Batman")),
    team_id = teamId.id,
    prospect = Some(TaskProspect(id = prospectId.id,
      name = Some("Superman"),
      email = Some("<EMAIL>"),
      company = Some("company"),
      phone_number = Some("phone_number"),
      linkedin_url = Some("linkedin_url"),
      timezone = Some("timezone"),
      designation = Some("designation"))),
    priority = TaskPriority.High,
    note = None,
    created_at = DateTime.now().minusDays(1),
    updated_at = None,
    due_at = DateTime.now(),
    campaign_id = Some(campaignId.id),
    campaign_name = None,
    step_id = Some(101L),
    step_label = Some("Day 1: Opening"),
    is_opening_step = Some(true),
    created_via = TaskCreatedVia.Scheduler,
    reply_sentiment_uuid = None
  )

  val messageBody = "I am inevitable."
  val autoMessageTask = autoInmailTask.copy(
    task_data = TaskData.AutoLinkedinMessage(
      body = messageBody
    )
  )

  val proxyIPAndCredentials = ProxyIPAndCredentials(
    ip_address = "***********",
    username = Some("aditya"),
    password = Some("I AM VENGEANCE")
  )

  val prospectObjectInternal = ProspectFixtures.prospectObjectInternal

  val prospectObject = ProspectObject(
    id = prospectId.id,
    owner_id = accountId.id,
    team_id = teamId.id,

    first_name = Some("Gal"),
    last_name = Some("Gadot"),

    email = Some("<EMAIL>"),

    custom_fields = Json.obj("custom" -> "no_custom"),

    list = None,

    job_title = None,
    company = None,
    linkedin_url = Some("https://linkedin.com/gal-gadot-dc"),
    phone = None,
    phone_2 = None,
    phone_3 = None,

    city = Some("Rosh Ha'ayin"),
    state = None,
    country = Some("Israeli"),
    timezone = Some("Asia/Jerusalem"),

    prospect_category = "not_categorized", // display name

    last_contacted_at = None,
    last_contacted_at_phone = None,

    created_at = DateTime.now().minusDays(1),


    /* internal columns only for smartreach website, not for public api */
    internal = prospectObjectInternal,
    latest_reply_sentiment_uuid = None,
    current_step_type = None,
    latest_task_done_at = None,
    prospect_uuid = Some(ProspectUuid("prs_aa_abcdefghi")),
    owner_uuid = AccountUuid("acc_aa_abcdegfhi"),
    updated_at = DateTime.now()
  )

  val phantomBusterSalesNavigatorInmailAgent = PhantomBusterAgent(
    id = phantomBusterAgentId,
    script = phantomBusterSalesNavigatorInmailScript
  )

  val tasksGroupedByTypeAndLinkedinSetting = TasksGroupedByTypeAndLinkedinSetting(
    taskIds = Seq(taskId),
    commonLinkedinTaskDetails = CommonLinkedinTaskDetails(
      teamId = teamId,
      linkedinSettingUuid = linkedinSettingUuid,
      taskType = TaskType.AutoLinkedinMessage,
      orgId = orgId
    )
  )

  val threadUrl = LinkedinMessageThreadUrl("https://linkedin.com/conversation/2Anvj-vnjsDKD-23vds")

  val groupedLinkedinThreadsForMessageScraping = GroupedLinkedinThreadsForMessageScraping(
    teamId = teamId,
    linkedinSettingUuid = linkedinSettingUuid,
    threadUrls = List(threadUrl)
  )

  val csvFileName = "phantombuster_file_1"
  val csvUploadBucketName = "phantom-buster-staging-gcs-bucket"
  val signedUrlTtlInMinutes = 10
  val publicCSVLink = "https://storage.gcs.com/Test.csv?34vnmjnv34jmvkmslkmc"

  val jsObj = Json.obj("Smelly_cats" -> "Why aren't they feeding you?")

  val messageThreadScraperJson: JsObject = Json.obj(
    "conversationUrl" -> "https://www.linkedin.com/messaging/thread/2-ZTllNmYwYzMtZjRmYy00NDc5LWE5OTctNWFjYWIwYzE2YmViXzAxMg==",
    "messages" -> Json.toJson(List(Json.obj(
      "conversationUrl" -> "https://www.linkedin.com/messaging/thread/2-ZTllNmYwYzMtZjRmYy00NDc5LWE5OTctNWFjYWIwYzE2YmViXzAxMg==",
      "date" -> aDate,
      "companyName" -> "Google Cloud",
      "companyUrl" -> "https://www.linkedin.com/company/google-cloud/",
      "url" -> "https://www.linkedin.com/messaging/thread/2-ZTllNmYwYzMtZjRmYy00NDc5LWE5OTctNWFjYWIwYzE2YmViXzAxMg==",
      "message" -> "Hi Aditya,\nInnovative DevOps are a game changer, especially in an evolving and dynamic environment.\nLearn from winning implementations across 10 categories key to DevOps success in this FREE Google Cloud DevOps Success ebook.\nYou'll discover how DevOps boost performance — like reducing deployment lead time from 4-6 months to less than an hour.\nSounds good? Let's shape the future of DevOps together!",
      "timestamp" -> "2023-11-08T09:33:45.492Z"
    ))),
    "url" -> "https://www.linkedin.com/messaging/thread/2-ZTllNmYwYzMtZjRmYy00NDc5LWE5OTctNWFjYWIwYzE2YmViXzAxMg==",
    "timestamp" -> "2023-11-08T09:33:46.073Z"
  )

  describe("Testing fetchOrUpdatePhantombusterApiKeyAndRequiredAgentId") {

    it ("should fail if getPhantomBusterApiKeyForTeam fails") {

      (phantomBusterTeamService.getPhantomBusterApiKeyForTeam)
        .expects(teamId)
        .returning(Failure(error))

      phantomBusterService.fetchPhantombusterApiKeyAndRequiredAgentIdAndUpdateStatus(
        teamId = teamId,
        linkedinSettingUuid = LinkedinSettingUuid(linkedinSettingUuid),
        requiredAgentScript = phantomBusterSalesNavigatorInmailScript
      )
        .map(_ => assert(false))
        .recover{
          case e => assert(e == error)
        }
    }

    it("should fail if team does not have a PhantomBuster API Key.") {
      (phantomBusterTeamService.getPhantomBusterApiKeyForTeam)
        .expects(teamId)
        .returning(Success(PhantomBusterApiKeyDetails.ExternalPhantomBusterApiKey(apiKey = phantomBusterApiKey)))

      phantomBusterService.fetchPhantombusterApiKeyAndRequiredAgentIdAndUpdateStatus(
        teamId = teamId,
          linkedinSettingUuid = LinkedinSettingUuid(linkedinSettingUuid),
        requiredAgentScript = phantomBusterSalesNavigatorInmailScript
      )
        .map(_ => assert(false))
        .recover {
          case e => assert(true)
        }
    }

    it ("should fail if fetchTeamsWithSamePhantomBusterOrgId fails") {
      (phantomBusterTeamService.getPhantomBusterApiKeyForTeam)
        .expects(teamId)
        .returning(Success(PhantomBusterApiKeyDetails.ExternalPhantomBusterApiKey(apiKey = phantomBusterApiKey)))

      (phantomBusterTeamService.fetchTeamsWithSamePhantomBusterOrgId)
        .expects(teamId)
        .returning(Failure(error))

      phantomBusterService.fetchPhantombusterApiKeyAndRequiredAgentIdAndUpdateStatus(
          teamId = teamId,
          linkedinSettingUuid = LinkedinSettingUuid(linkedinSettingUuid),
          requiredAgentScript = phantomBusterSalesNavigatorInmailScript
        )
        .map(_ => assert(false))
        .recover {
          case e => assert(e == error)
        }
    }

    it ("should fail if getIdlePhantomBusterAgentIdForScript fails.") {
      (phantomBusterTeamService.getPhantomBusterApiKeyForTeam)
        .expects(teamId)
        .returning(Success(PhantomBusterApiKeyDetails.ExternalPhantomBusterApiKey(apiKey = phantomBusterApiKey)))

      (phantomBusterTeamService.fetchTeamsWithSamePhantomBusterOrgId)
        .expects(teamId)
        .returning(Success(List(teamId)))

      (phantomBusterAgentService.getIdlePhantomBusterAgentIdForScript)
        .expects(List(teamId), phantomBusterSalesNavigatorInmailScript)
        .returning(Failure(error))

      phantomBusterService.fetchPhantombusterApiKeyAndRequiredAgentIdAndUpdateStatus(
          teamId = teamId,
          linkedinSettingUuid = LinkedinSettingUuid(linkedinSettingUuid),
          requiredAgentScript = phantomBusterSalesNavigatorInmailScript
        )
        .map(_ => assert(false))
        .recover {
          case e => assert(e == error)
        }
    }

    it ("should fail if saveAgent fails while updating webhook url") {
      (phantomBusterTeamService.getPhantomBusterApiKeyForTeam)
        .expects(teamId)
        .returning(Success(PhantomBusterApiKeyDetails.ExternalPhantomBusterApiKey(apiKey = phantomBusterApiKey)))

      (phantomBusterTeamService.fetchTeamsWithSamePhantomBusterOrgId)
        .expects(teamId)
        .returning(Success(List(teamId)))

      (phantomBusterAgentService.getIdlePhantomBusterAgentIdForScript)
        .expects(List(teamId), phantomBusterSalesNavigatorInmailScript)
        .returning(Success(Some(phantomBusterAgentId)))

      (srUuidUtils.generatePhantombusterTeamSecretKey)
        .expects(teamId, PhantomBusterApiKeyDetails.ExternalPhantomBusterApiKey(phantomBusterApiKey))
        .returning(phantomBusterWebhookTeamSecretKey.key)

      (linkedinSettingService.getProxyDetailsForLinkedinAccount)
        .expects(LinkedinSettingUuid(linkedinSettingUuid), teamId)
        .returning(Success(Some(proxyIPAndCredentials)))

      (phantomBusterApi.saveAgent(
        _: Option[PhantomBusterAgentId],
        _: Option[ProxyIPAndCredentials],
        _: PhantomBusterApiKey,
        _: PhantomBusterScript,
        _: PhantomBusterWebhookTeamSecretKey
      )(
        _: WSClient,
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(Some(phantomBusterAgentId), Some(proxyIPAndCredentials), phantomBusterApiKey, phantomBusterSalesNavigatorInmailScript, *, wSClient, ec, Logger)
        .returning(Future.failed(error))

      phantomBusterService.fetchPhantombusterApiKeyAndRequiredAgentIdAndUpdateStatus(
          teamId = teamId,
          linkedinSettingUuid = LinkedinSettingUuid(linkedinSettingUuid),
          requiredAgentScript = phantomBusterSalesNavigatorInmailScript
        )
        .map(_ => assert(false))
        .recover {
          case e => assert(e == error)
        }
    }

    it ("should fail if changeAgentRunningStatus fails") {
      (phantomBusterTeamService.getPhantomBusterApiKeyForTeam)
        .expects(teamId)
        .returning(Success(PhantomBusterApiKeyDetails.ExternalPhantomBusterApiKey(apiKey = phantomBusterApiKey)))

      (phantomBusterTeamService.fetchTeamsWithSamePhantomBusterOrgId)
        .expects(teamId)
        .returning(Success(List(teamId)))

      (phantomBusterAgentService.getIdlePhantomBusterAgentIdForScript)
        .expects(List(teamId), phantomBusterSalesNavigatorInmailScript)
        .returning(Success(Some(phantomBusterAgentId)))

      (srUuidUtils.generatePhantombusterTeamSecretKey)
        .expects(teamId, PhantomBusterApiKeyDetails.ExternalPhantomBusterApiKey(phantomBusterApiKey))
        .returning(phantomBusterWebhookTeamSecretKey.key)

      (linkedinSettingService.getProxyDetailsForLinkedinAccount)
        .expects(LinkedinSettingUuid(linkedinSettingUuid), teamId)
        .returning(Success(Some(proxyIPAndCredentials)))

      (phantomBusterApi.saveAgent(
        _: Option[PhantomBusterAgentId],
        _: Option[ProxyIPAndCredentials],
        _: PhantomBusterApiKey,
        _: PhantomBusterScript,
        _: PhantomBusterWebhookTeamSecretKey
      )(
        _: WSClient,
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(Some(phantomBusterAgentId), Some(proxyIPAndCredentials), phantomBusterApiKey, phantomBusterSalesNavigatorInmailScript, *, wSClient, ec, Logger)
        .returning(Future.successful(phantomBusterAgentId))

      (phantomBusterAgentService.changeAgentRunningStatus)
        .expects(phantomBusterAgentId, TeamIdAndInternalApiKeyId(teamId, None), true, phantomBusterSalesNavigatorInmailScript)
        .returning(Failure(error))

      phantomBusterService.fetchPhantombusterApiKeyAndRequiredAgentIdAndUpdateStatus(
          teamId = teamId,
          linkedinSettingUuid = LinkedinSettingUuid(linkedinSettingUuid),
          requiredAgentScript = phantomBusterSalesNavigatorInmailScript
        )
        .map(_ => assert(false))
        .recover {
          case e => assert(e == error)
        }
    }

    it("should return Api Key and Agent Id if found in DB.") {
      (phantomBusterTeamService.getPhantomBusterApiKeyForTeam)
        .expects(teamId)
        .returning(Success(PhantomBusterApiKeyDetails.ExternalPhantomBusterApiKey(apiKey = phantomBusterApiKey)))

      (phantomBusterTeamService.fetchTeamsWithSamePhantomBusterOrgId)
        .expects(teamId)
        .returning(Success(List(teamId)))

      (phantomBusterAgentService.getIdlePhantomBusterAgentIdForScript)
        .expects(List(teamId), phantomBusterSalesNavigatorInmailScript)
        .returning(Success(Some(phantomBusterAgentId)))

      (srUuidUtils.generatePhantombusterTeamSecretKey)
        .expects(teamId, PhantomBusterApiKeyDetails.ExternalPhantomBusterApiKey(phantomBusterApiKey))
        .returning(phantomBusterWebhookTeamSecretKey.key)

      (linkedinSettingService.getProxyDetailsForLinkedinAccount)
        .expects(LinkedinSettingUuid(linkedinSettingUuid), teamId)
        .returning(Success(Some(proxyIPAndCredentials)))

      (phantomBusterApi.saveAgent(
        _: Option[PhantomBusterAgentId],
        _: Option[ProxyIPAndCredentials],
        _: PhantomBusterApiKey,
        _: PhantomBusterScript,
        _: PhantomBusterWebhookTeamSecretKey
      )(
        _: WSClient,
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(Some(phantomBusterAgentId), Some(proxyIPAndCredentials), phantomBusterApiKey, phantomBusterSalesNavigatorInmailScript, *, wSClient, ec, Logger)
        .returning(Future.successful(phantomBusterAgentId))

      (phantomBusterAgentService.changeAgentRunningStatus)
        .expects(phantomBusterAgentId, TeamIdAndInternalApiKeyId(teamId, None), true, phantomBusterSalesNavigatorInmailScript)
        .returning(Success(1))

      phantomBusterService.fetchPhantombusterApiKeyAndRequiredAgentIdAndUpdateStatus(
        teamId = teamId,
          linkedinSettingUuid = LinkedinSettingUuid(linkedinSettingUuid),
        requiredAgentScript = phantomBusterSalesNavigatorInmailScript
      )
        .map(apiKeyAndAgentId => {
          assert(apiKeyAndAgentId == PhantomBusterApiKeyAndAgentId(
            apiKey = phantomBusterApiKey,
            agentId = phantomBusterAgentId
          ))
        })
        .recover {
          case _ => assert(false)
        }
    }

    it("should fail if agentId not present in DB and fetchAllPhantoms fails.") {

      (phantomBusterTeamService.getPhantomBusterApiKeyForTeam)
        .expects(teamId)
        .returning(Success(PhantomBusterApiKeyDetails.ExternalPhantomBusterApiKey(apiKey = phantomBusterApiKey)))

      (phantomBusterTeamService.fetchTeamsWithSamePhantomBusterOrgId)
        .expects(teamId)
        .returning(Success(List(teamId)))

      (phantomBusterAgentService.getIdlePhantomBusterAgentIdForScript)
        .expects(List(teamId), phantomBusterSalesNavigatorInmailScript)
        .returning(Success(None))

      (phantomBusterApi.fetchAllPhantoms(_: PhantomBusterApiKey)
      (_: WSClient,
      _: ExecutionContext,
      _: SRLogger))
        .expects(phantomBusterApiKey, wSClient, ec, Logger)
        .returning(Future.failed(error))

      phantomBusterService.fetchPhantombusterApiKeyAndRequiredAgentIdAndUpdateStatus(
        teamId = teamId,
          linkedinSettingUuid = LinkedinSettingUuid(linkedinSettingUuid),
        requiredAgentScript = phantomBusterSalesNavigatorInmailScript
      )
        .map(_ => {
          assert(false)
        })
        .recover {
          case e => assert(true)
        }
    }

    it ("should fail if getAllPhantomsForTeamsWithSameOrgIdAndScript fails") {
      (phantomBusterTeamService.getPhantomBusterApiKeyForTeam)
        .expects(teamId)
        .returning(Success(PhantomBusterApiKeyDetails.ExternalPhantomBusterApiKey(apiKey = phantomBusterApiKey)))

      (phantomBusterTeamService.fetchTeamsWithSamePhantomBusterOrgId)
        .expects(teamId)
        .returning(Success(List(teamId)))

      (phantomBusterAgentService.getIdlePhantomBusterAgentIdForScript)
        .expects(List(teamId), phantomBusterSalesNavigatorInmailScript)
        .returning(Success(None))

      (phantomBusterApi.fetchAllPhantoms(
        _: PhantomBusterApiKey
      )(_: WSClient,
        _: ExecutionContext,
        _: SRLogger))
        .expects(phantomBusterApiKey, wSClient, ec, Logger)
        .returning(Future.successful(List(
          phantomBusterSalesNavigatorInmailAgent,
          phantomBusterSalesNavigatorInmailAgent.copy(script = PhantomBusterScript.LinkedinInboxScraper)
        )))

      (phantomBusterAgentService.getAllPhantomsForTeamsWithSameOrgIdAndScript)
        .expects(List(teamId), phantomBusterSalesNavigatorInmailScript)
        .returning(Failure(error))

      phantomBusterService.fetchPhantombusterApiKeyAndRequiredAgentIdAndUpdateStatus(
          teamId = teamId,
          linkedinSettingUuid = LinkedinSettingUuid(linkedinSettingUuid),
          requiredAgentScript = phantomBusterSalesNavigatorInmailScript
        )
        .map(_ => assert(false))
        .recover {
          case e => assert(e == error)
        }
    }

    it ("should fail if addPhantomBusterAgentIdForScript fails") {
      (phantomBusterTeamService.getPhantomBusterApiKeyForTeam)
        .expects(teamId)
        .returning(Success(PhantomBusterApiKeyDetails.ExternalPhantomBusterApiKey(apiKey = phantomBusterApiKey)))

      (phantomBusterTeamService.fetchTeamsWithSamePhantomBusterOrgId)
        .expects(teamId)
        .returning(Success(List(teamId)))

      (phantomBusterAgentService.getIdlePhantomBusterAgentIdForScript)
        .expects(List(teamId), phantomBusterSalesNavigatorInmailScript)
        .returning(Success(None))

      (phantomBusterApi.fetchAllPhantoms(
        _: PhantomBusterApiKey
      )(_: WSClient,
        _: ExecutionContext,
        _: SRLogger))
        .expects(phantomBusterApiKey, wSClient, ec, Logger)
        .returning(Future.successful(List(
          phantomBusterSalesNavigatorInmailAgent,
          phantomBusterSalesNavigatorInmailAgent.copy(script = PhantomBusterScript.LinkedinInboxScraper)
        )))

      (phantomBusterAgentService.getAllPhantomsForTeamsWithSameOrgIdAndScript)
        .expects(List(teamId), phantomBusterSalesNavigatorInmailScript)
        .returning(Success(List()))

      (srUuidUtils.generatePhantombusterTeamSecretKey)
        .expects(teamId, PhantomBusterApiKeyDetails.ExternalPhantomBusterApiKey(phantomBusterApiKey))
        .returning(phantomBusterWebhookTeamSecretKey.key)

      (linkedinSettingService.getProxyDetailsForLinkedinAccount)
        .expects(LinkedinSettingUuid(linkedinSettingUuid), teamId)
        .returning(Success(Some(proxyIPAndCredentials)))

      (phantomBusterApi.saveAgent(
        _: Option[PhantomBusterAgentId],
        _: Option[ProxyIPAndCredentials],
        _: PhantomBusterApiKey,
        _: PhantomBusterScript,
        _: PhantomBusterWebhookTeamSecretKey
      )(
        _: WSClient,
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(Some(phantomBusterAgentId), Some(proxyIPAndCredentials), phantomBusterApiKey, phantomBusterSalesNavigatorInmailScript, *, wSClient, ec, Logger)
        .returning(Future.successful(phantomBusterAgentId))

      (phantomBusterAgentService.addPhantomBusterAgentIdForScript)
        .expects(teamId, phantomBusterAgentId, phantomBusterSalesNavigatorInmailScript, false)
        .returning(Failure(error))

      phantomBusterService.fetchPhantombusterApiKeyAndRequiredAgentIdAndUpdateStatus(
          teamId = teamId,
          linkedinSettingUuid = LinkedinSettingUuid(linkedinSettingUuid),
          requiredAgentScript = phantomBusterSalesNavigatorInmailScript
        )
        .map(_ => assert(false))
        .recover {
          case e => assert(e == error)
        }
    }

    it("should return and save agentId for phantom already present in dashboard (if there).") {

      (phantomBusterTeamService.getPhantomBusterApiKeyForTeam)
        .expects(teamId)
        .returning(Success(PhantomBusterApiKeyDetails.ExternalPhantomBusterApiKey(apiKey = phantomBusterApiKey)))

      (phantomBusterTeamService.fetchTeamsWithSamePhantomBusterOrgId)
        .expects(teamId)
        .returning(Success(List(teamId)))

      (phantomBusterAgentService.getIdlePhantomBusterAgentIdForScript)
        .expects(List(teamId), phantomBusterSalesNavigatorInmailScript)
        .returning(Success(None))

      (phantomBusterApi.fetchAllPhantoms(
        _: PhantomBusterApiKey
      )(_: WSClient,
        _: ExecutionContext,
        _: SRLogger))
        .expects(phantomBusterApiKey, wSClient, ec, Logger)
        .returning(Future.successful(List(
          phantomBusterSalesNavigatorInmailAgent,
          phantomBusterSalesNavigatorInmailAgent.copy(script = PhantomBusterScript.LinkedinInboxScraper)
        )))

      (phantomBusterAgentService.getAllPhantomsForTeamsWithSameOrgIdAndScript)
        .expects(List(teamId), phantomBusterSalesNavigatorInmailScript)
        .returning(Success(List()))

      (srUuidUtils.generatePhantombusterTeamSecretKey)
        .expects(teamId, PhantomBusterApiKeyDetails.ExternalPhantomBusterApiKey(phantomBusterApiKey))
        .returning(phantomBusterWebhookTeamSecretKey.key)

      (linkedinSettingService.getProxyDetailsForLinkedinAccount)
        .expects(LinkedinSettingUuid(linkedinSettingUuid), teamId)
        .returning(Success(Some(proxyIPAndCredentials)))

      (phantomBusterApi.saveAgent(
        _: Option[PhantomBusterAgentId],
        _: Option[ProxyIPAndCredentials],
        _: PhantomBusterApiKey,
        _: PhantomBusterScript,
        _: PhantomBusterWebhookTeamSecretKey
      )(
        _: WSClient,
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(Some(phantomBusterAgentId), Some(proxyIPAndCredentials), phantomBusterApiKey, phantomBusterSalesNavigatorInmailScript, *, wSClient, ec, Logger)
        .returning(Future.successful(phantomBusterAgentId))

      (phantomBusterAgentService.addPhantomBusterAgentIdForScript)
        .expects(teamId, phantomBusterAgentId, phantomBusterSalesNavigatorInmailScript, false)
        .returning(Success(phantomBusterAgentId))

      (phantomBusterAgentService.changeAgentRunningStatus)
        .expects(phantomBusterAgentId, TeamIdAndInternalApiKeyId(teamId, None), true, phantomBusterSalesNavigatorInmailScript)
        .returning(Success(1))

      phantomBusterService.fetchPhantombusterApiKeyAndRequiredAgentIdAndUpdateStatus(
          teamId = teamId,
          linkedinSettingUuid = LinkedinSettingUuid(linkedinSettingUuid),
          requiredAgentScript = phantomBusterSalesNavigatorInmailScript
        )
        .map(apiKeyAndAgentId => {
          assert(apiKeyAndAgentId == PhantomBusterApiKeyAndAgentId(
            apiKey = phantomBusterApiKey,
            agentId = phantomBusterAgentId
          ))
        })
        .recover {
          case _ => assert(false)
        }
    }

    it("should fail if saveAgent fails, agentId not present in DB and phantom not present in Dashboard.") {

      (phantomBusterTeamService.getPhantomBusterApiKeyForTeam)
        .expects(teamId)
        .returning(Success(PhantomBusterApiKeyDetails.ExternalPhantomBusterApiKey(apiKey = phantomBusterApiKey)))

      (phantomBusterTeamService.fetchTeamsWithSamePhantomBusterOrgId)
        .expects(teamId)
        .returning(Success(List(teamId)))

      (phantomBusterAgentService.getIdlePhantomBusterAgentIdForScript)
        .expects(List(teamId), phantomBusterSalesNavigatorInmailScript)
        .returning(Success(None))

      (phantomBusterApi.fetchAllPhantoms(_: PhantomBusterApiKey)
      (_: WSClient,
        _: ExecutionContext,
        _: SRLogger))
        .expects(phantomBusterApiKey, wSClient, ec, Logger)
        .returning(Future.successful(List(
          phantomBusterSalesNavigatorInmailAgent.copy(script = PhantomBusterScript.LinkedinInboxScraper)
        )))

      (phantomBusterAgentService.getAllPhantomsForTeamsWithSameOrgIdAndScript)
        .expects(List(teamId), phantomBusterSalesNavigatorInmailScript)
        .returning(Success(List()))

      (srUuidUtils.generatePhantombusterTeamSecretKey)
        .expects(teamId, PhantomBusterApiKeyDetails.ExternalPhantomBusterApiKey(phantomBusterApiKey))
        .returning(phantomBusterWebhookTeamSecretKey.key)

      (linkedinSettingService.getProxyDetailsForLinkedinAccount)
        .expects(LinkedinSettingUuid(linkedinSettingUuid), teamId)
        .returning(Success(Some(proxyIPAndCredentials)))

      (phantomBusterApi.saveAgent(
        _: Option[PhantomBusterAgentId],
        _: Option[ProxyIPAndCredentials],
        _: PhantomBusterApiKey,
        _: PhantomBusterScript,
        _: PhantomBusterWebhookTeamSecretKey
      )(
        _: WSClient,
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(None, Some(proxyIPAndCredentials), phantomBusterApiKey, phantomBusterSalesNavigatorInmailScript, *, wSClient, ec, Logger)
        .returning(Future.failed(error))

      phantomBusterService.fetchPhantombusterApiKeyAndRequiredAgentIdAndUpdateStatus(
          teamId = teamId,
          linkedinSettingUuid = LinkedinSettingUuid(linkedinSettingUuid),
          requiredAgentScript = phantomBusterSalesNavigatorInmailScript
        )
        .map(_ => {
          assert(false)
        })
        .recover {
          case e => assert(true)
        }
    }

    it ("should return apiKey and newly created agentId.") {
      (phantomBusterTeamService.getPhantomBusterApiKeyForTeam)
        .expects(teamId)
        .returning(Success(PhantomBusterApiKeyDetails.ExternalPhantomBusterApiKey(apiKey = phantomBusterApiKey)))

      (phantomBusterTeamService.fetchTeamsWithSamePhantomBusterOrgId)
        .expects(teamId)
        .returning(Success(List(teamId)))

      (phantomBusterAgentService.getIdlePhantomBusterAgentIdForScript)
        .expects(List(teamId), phantomBusterSalesNavigatorInmailScript)
        .returning(Success(None))

      (phantomBusterApi.fetchAllPhantoms(_: PhantomBusterApiKey)
      (_: WSClient,
        _: ExecutionContext,
        _: SRLogger))
        .expects(phantomBusterApiKey, wSClient, ec, Logger)
        .returning(Future.successful(List(
          phantomBusterSalesNavigatorInmailAgent.copy(script = PhantomBusterScript.LinkedinInboxScraper)
        )))

      (phantomBusterAgentService.getAllPhantomsForTeamsWithSameOrgIdAndScript)
        .expects(List(teamId), phantomBusterSalesNavigatorInmailScript)
        .returning(Success(List()))

      (srUuidUtils.generatePhantombusterTeamSecretKey)
        .expects(teamId, PhantomBusterApiKeyDetails.ExternalPhantomBusterApiKey(phantomBusterApiKey))
        .returning(phantomBusterWebhookTeamSecretKey.key)

      (linkedinSettingService.getProxyDetailsForLinkedinAccount)
        .expects(LinkedinSettingUuid(linkedinSettingUuid), teamId)
        .returning(Success(Some(proxyIPAndCredentials)))

      (phantomBusterApi.saveAgent(
        _: Option[PhantomBusterAgentId],
        _: Option[ProxyIPAndCredentials],
        _: PhantomBusterApiKey,
        _: PhantomBusterScript,
        _: PhantomBusterWebhookTeamSecretKey
      )(
        _: WSClient,
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(None, Some(proxyIPAndCredentials), phantomBusterApiKey, phantomBusterSalesNavigatorInmailScript, *, wSClient, ec, Logger)
        .returning(Future.successful(phantomBusterAgentId))

      (phantomBusterAgentService.addPhantomBusterAgentIdForScript)
        .expects(teamId, phantomBusterAgentId, phantomBusterSalesNavigatorInmailScript, true)
        .returning(Success(phantomBusterAgentId))

      (phantomBusterAgentService.changeAgentRunningStatus)
        .expects(phantomBusterAgentId, TeamIdAndInternalApiKeyId(teamId, None), true, phantomBusterSalesNavigatorInmailScript)
        .returning(Success(1))

      phantomBusterService.fetchPhantombusterApiKeyAndRequiredAgentIdAndUpdateStatus(
          teamId = teamId,
          linkedinSettingUuid = LinkedinSettingUuid(linkedinSettingUuid),
          requiredAgentScript = phantomBusterSalesNavigatorInmailScript
        )
        .map(apiKeyAndAgentId => {
          assert(apiKeyAndAgentId == PhantomBusterApiKeyAndAgentId(
            apiKey = phantomBusterApiKey,
            agentId = phantomBusterAgentId
          ))
        })
        .recover {
          case _ => assert(false)
        }
    }
  }


  describe("Testing executeLinkedinTask") {
    it("should fail if findDueTaskbyTaskIDAndTeamId fails.") {
      (taskService.findDueTaskbyTaskIDAndTeamId(
        _: String,
        _: TeamId,
        _: OrgId
      )(
      _: ExecutionContext,
      _: SRLogger
      ))
        .expects(taskId, teamId,orgId, ec,*)
        .returning(Future.failed(error))

      phantomBusterService.executeAutomatedInmailLinkedinTask(
        taskId = taskId,
        teamId = teamId,
        linkedinSettingUuid = linkedinSettingUuid,
        orgId = orgId
      )
        .map(_ => assert(false))
        .recover{
          case e => assert(e == error)
        }
    }

    it("should fail if the provided task is not in due state.") {
      (taskService.findDueTaskbyTaskIDAndTeamId(
        _: String,
        _: TeamId,
        _: OrgId
      )(
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(taskId, teamId, orgId, ec, Logger)
        .returning(Future.failed(new Exception(s"${taskId} is deleted or not in due state.")))

      phantomBusterService.executeAutomatedInmailLinkedinTask(
        taskId = taskId,
        teamId = teamId,
        linkedinSettingUuid = linkedinSettingUuid,
        orgId = orgId
      )
        .map(_ => assert(false))
        .recover {
          case e => assert(e.getMessage == s"${taskId} is deleted or not in due state.")
        }
    }

    it("should throw error and delete task if prospect not present for task.") {
      (taskService.findDueTaskbyTaskIDAndTeamId(
        _: String,
        _: TeamId,
        _: OrgId
      )(
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(taskId, teamId,orgId, ec,*)
        .returning(Future.successful(autoInmailTask.copy(prospect = None)))

      (taskService.deleteTask(_: String, _: TeamId)(_: ExecutionContext, _: SRLogger))
        .expects(taskId, teamId, ec, *)
        .returning(Future.successful(OptionCampaignIdAndOptionProspectIdAndOptionEmailSettingId(
          campaignId = Some(campaignId),
          prospectId = Some(prospectId),
          emailSettingId = None,
          team_id = teamId,
          taskId = Some(TaskId(taskId)),
          step_id = None
        )))

      phantomBusterService.executeAutomatedInmailLinkedinTask(
        taskId = taskId,
        teamId = teamId,
        linkedinSettingUuid = linkedinSettingUuid,
        orgId = orgId
      )
        .map(_ => assert(false))
        .recover {
          case e => assert(e.getMessage == s"$taskId does not have a prospect. Hence deleting task")
        }
    }

    it("should fail if getLinkedinUrlByProspectId fails") {
      (taskService.findDueTaskbyTaskIDAndTeamId(
        _: String,
        _: TeamId,
        _: OrgId
      )(
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(taskId, teamId, orgId, ec,*)
        .returning(Future.successful(autoInmailTask))

      (prospectDAOService.getLinkedinUrlByProspectId)
        .expects(teamId, prospectId, Logger)
        .returning(Failure(error))

      phantomBusterService.executeAutomatedInmailLinkedinTask(
        taskId = taskId,
        teamId = teamId,
        linkedinSettingUuid = linkedinSettingUuid,
        orgId = orgId
      )
        .map(_ => assert(false))
        .recover {
          case e => assert(e == error)
        }
    }

    it("should fail if prospect not found in DB.") {
      (taskService.findDueTaskbyTaskIDAndTeamId(
        _: String,
        _: TeamId,
        _: OrgId
      )(
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(taskId, teamId,orgId, ec,*)
        .returning(Future.successful(autoInmailTask))

      (prospectDAOService.getLinkedinUrlByProspectId)
        .expects(teamId, prospectId, Logger)
        .returning(Failure(new Exception(s"${prospectId} not present.")))

      phantomBusterService.executeAutomatedInmailLinkedinTask(
        taskId = taskId,
        teamId = teamId,
        linkedinSettingUuid = linkedinSettingUuid,
        orgId = orgId
      )
        .map(_ => assert(false))
        .recover {
          case e => assert(e.getMessage == s"${prospectId} not present.")
        }
    }

    it("should fail if prospect does not have a linkedin_url.") {
      (taskService.findDueTaskbyTaskIDAndTeamId(
        _: String,
        _: TeamId,
        _: OrgId
      )(
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(taskId, teamId, orgId, ec,*)
        .returning(Future.successful(autoInmailTask))

      (prospectDAOService.getLinkedinUrlByProspectId)
        .expects(teamId, prospectId, Logger)
        .returning(Failure(new Exception(s"Linkedin URL not present for prospect_${prospectObject.id}")))

      phantomBusterService.executeAutomatedInmailLinkedinTask(
        taskId = taskId,
        teamId = teamId,
        linkedinSettingUuid = linkedinSettingUuid,
        orgId = orgId
      )
        .map(_ => assert(false))
        .recover {
          case e => assert(e.getMessage == s"Linkedin URL not present for prospect_${prospectObject.id}")
        }
    }

    it("should fail if linkedin session cookie is not found in DB.") {
      (taskService.findDueTaskbyTaskIDAndTeamId(
        _: String,
        _: TeamId,
        _: OrgId
      )(
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(taskId, teamId, orgId, ec,*)
        .returning(Future.successful(autoInmailTask))

      (prospectDAOService.getLinkedinUrlByProspectId)
        .expects(teamId, prospectId, Logger)
        .returning(Success(prospectObject.linkedin_url.get))

      (campaignService.getSessionCookieAndUserAgentForLinkedinSetting)
        .expects(campaignId, teamId)
        .returning(Failure(new Exception(s"campaign_$campaignId does not have a session_cookie")))

      phantomBusterService.executeAutomatedInmailLinkedinTask(
        taskId = taskId,
        teamId = teamId,
        linkedinSettingUuid = linkedinSettingUuid ,
        orgId = orgId
      )
        .map(_ => assert(false))
        .recover {
          case e => assert(e.getMessage == s"campaign_$campaignId does not have a session_cookie")
        }
    }

    it("should fail if task_type does not belong to a linkedin task.") {
      (taskService.findDueTaskbyTaskIDAndTeamId(
        _: String,
        _: TeamId,
        _: OrgId
      )(
        _: ExecutionContext,
        _:SRLogger
      ))
        .expects(taskId, teamId, orgId,ec,Logger)
        .returning(Future.successful(autoInmailTask.copy(task_type = TaskType.CallTask)))

      (prospectDAOService.getLinkedinUrlByProspectId)
        .expects(teamId, prospectId, Logger)
        .returning(Success(prospectObject.linkedin_url.get))

      (campaignService.getSessionCookieAndUserAgentForLinkedinSetting)
        .expects(campaignId, teamId)
        .returning(Success(linkedinSessionCookieAndUserAgent))

      phantomBusterService.executeAutomatedInmailLinkedinTask(
        taskId = taskId,
        teamId = teamId,
        linkedinSettingUuid = linkedinSettingUuid,
        orgId = orgId
      )
        .map(_ => assert(false))
        .recover {
          case e => assert(e.getMessage == s"taskType: ${TaskType.CallTask.toKey} cannot be automated by PhantomBuster.")
        }
    }

    it("should fail if fetchOrUpdatePhantombusterApiKeyAndRequiredAgentId fails.") {
      (taskService.findDueTaskbyTaskIDAndTeamId(
        _: String,
        _: TeamId,
        _: OrgId
      )(
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(taskId, teamId,orgId, ec,Logger)
        .returning(Future.successful(autoInmailTask))

      (prospectDAOService.getLinkedinUrlByProspectId)
        .expects(teamId, prospectId, Logger)
        .returning(Success(prospectObject.linkedin_url.get))

      (campaignService.getSessionCookieAndUserAgentForLinkedinSetting)
        .expects(campaignId, teamId)
        .returning(Success(linkedinSessionCookieAndUserAgent))

      (phantomBusterTeamService.getPhantomBusterApiKeyForTeam)
        .expects(teamId)
        .returning(Failure(error))

      phantomBusterService.executeAutomatedInmailLinkedinTask(
        taskId = taskId,
        teamId = teamId,
        linkedinSettingUuid = linkedinSettingUuid,
        orgId = orgId
      )
        .map(_ => assert(false))
        .recover {
          case e => assert(e == error)
        }
    }

    it("should fail if taskData does not belong to a linkedin task.") {
      (taskService.findDueTaskbyTaskIDAndTeamId(
        _: String,
        _: TeamId,
        _: OrgId
      )(
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(taskId, teamId,orgId, ec,Logger)
        .returning(Future.successful(autoInmailTask.copy(task_data = TaskData.GeneralTaskData(task_notes = "I_AM_IRONMAN"))))

      (prospectDAOService.getLinkedinUrlByProspectId)
        .expects(teamId, prospectId, Logger)
        .returning(Success(prospectObject.linkedin_url.get))

      (campaignService.getSessionCookieAndUserAgentForLinkedinSetting)
        .expects(campaignId, teamId)
        .returning(Success(linkedinSessionCookieAndUserAgent))

      (phantomBusterTeamService.getPhantomBusterApiKeyForTeam)
        .expects(teamId)
        .returning(Success(PhantomBusterApiKeyDetails.ExternalPhantomBusterApiKey(apiKey = phantomBusterApiKey)))

      (phantomBusterTeamService.fetchTeamsWithSamePhantomBusterOrgId)
        .expects(teamId)
        .returning(Success(List(teamId)))

      (phantomBusterAgentService.getIdlePhantomBusterAgentIdForScript)
        .expects(List(teamId), phantomBusterSalesNavigatorInmailScript)
        .returning(Success(Some(phantomBusterAgentId)))

      (srUuidUtils.generatePhantombusterTeamSecretKey)
        .expects(teamId, PhantomBusterApiKeyDetails.ExternalPhantomBusterApiKey(phantomBusterApiKey))
        .returning(phantomBusterWebhookTeamSecretKey.key)

      (linkedinSettingService.getProxyDetailsForLinkedinAccount)
        .expects(LinkedinSettingUuid(linkedinSettingUuid), teamId)
        .returning(Success(Some(proxyIPAndCredentials)))

      (phantomBusterApi.saveAgent(
        _: Option[PhantomBusterAgentId],
        _: Option[ProxyIPAndCredentials],
        _: PhantomBusterApiKey,
        _: PhantomBusterScript,
        _: PhantomBusterWebhookTeamSecretKey
      )(
        _: WSClient,
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(Some(phantomBusterAgentId), Some(proxyIPAndCredentials), phantomBusterApiKey, phantomBusterSalesNavigatorInmailScript, *, wSClient, ec, Logger)
        .returning(Future.successful(phantomBusterAgentId))

      (phantomBusterAgentService.changeAgentRunningStatus)
        .expects(phantomBusterAgentId, TeamIdAndInternalApiKeyId(teamId, None), true, phantomBusterSalesNavigatorInmailScript)
        .returning(Success(1))

      phantomBusterService.executeAutomatedInmailLinkedinTask(
        taskId = taskId,
        teamId = teamId,
        linkedinSettingUuid = linkedinSettingUuid,
        orgId = orgId
      )
        .map(_ => assert(false))
        .recover{
          case e => assert(e.getMessage == "Only Automated Inmail Tasks should come here.")
        }
    }

    it("should fail if launchAgent fails.") {
      (taskService.findDueTaskbyTaskIDAndTeamId(
        _: String,
        _: TeamId,
        _: OrgId
      )(
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(taskId, teamId, orgId, ec,Logger)
        .returning(Future.successful(autoInmailTask))

      (prospectDAOService.getLinkedinUrlByProspectId)
        .expects(teamId, prospectId, Logger)
        .returning(Success(prospectObject.linkedin_url.get))

      (campaignService.getSessionCookieAndUserAgentForLinkedinSetting)
        .expects(campaignId, teamId)
        .returning(Success(linkedinSessionCookieAndUserAgent))

      (phantomBusterTeamService.getPhantomBusterApiKeyForTeam)
        .expects(teamId)
        .returning(Success(PhantomBusterApiKeyDetails.ExternalPhantomBusterApiKey(apiKey = phantomBusterApiKey)))

      (phantomBusterTeamService.fetchTeamsWithSamePhantomBusterOrgId)
        .expects(teamId)
        .returning(Success(List(teamId)))

      (phantomBusterAgentService.getIdlePhantomBusterAgentIdForScript)
        .expects(List(teamId), phantomBusterSalesNavigatorInmailScript)
        .returning(Success(Some(phantomBusterAgentId)))

      (srUuidUtils.generatePhantombusterTeamSecretKey)
        .expects(teamId, PhantomBusterApiKeyDetails.ExternalPhantomBusterApiKey(phantomBusterApiKey))
        .returning(phantomBusterWebhookTeamSecretKey.key)

      (linkedinSettingService.getProxyDetailsForLinkedinAccount)
        .expects(LinkedinSettingUuid(linkedinSettingUuid), teamId)
        .returning(Success(Some(proxyIPAndCredentials)))

      (phantomBusterApi.saveAgent(
        _: Option[PhantomBusterAgentId],
        _: Option[ProxyIPAndCredentials],
        _: PhantomBusterApiKey,
        _: PhantomBusterScript,
        _: PhantomBusterWebhookTeamSecretKey
      )(
        _: WSClient,
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(Some(phantomBusterAgentId), Some(proxyIPAndCredentials), phantomBusterApiKey, phantomBusterSalesNavigatorInmailScript, *, wSClient, ec, Logger)
        .returning(Future.successful(phantomBusterAgentId))

      (phantomBusterAgentService.changeAgentRunningStatus)
        .expects(phantomBusterAgentId, TeamIdAndInternalApiKeyId(teamId, None), true, phantomBusterSalesNavigatorInmailScript)
        .returning(Success(1))

      (phantomBusterApi.launchAgent(
        _: PhantomBusterApiKey,
        _: JsObject
      )(
        _: WSClient,
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(phantomBusterApiKey, *, wSClient, ec, Logger)
        .returning(Future.failed(error))

      phantomBusterService.executeAutomatedInmailLinkedinTask(
        taskId = taskId,
        teamId = teamId,
        linkedinSettingUuid = linkedinSettingUuid ,
        orgId = orgId
      )
        .map(_ => assert(false))
        .recover{
          case e => assert(e == error)
        }
    }

    it("should fail if updateContainerIdForTask fails.") {
      (taskService.findDueTaskbyTaskIDAndTeamId(
        _: String,
        _: TeamId,
        _: OrgId
      )(
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(taskId, teamId, orgId, ec,Logger)
        .returning(Future.successful(autoInmailTask))

      (prospectDAOService.getLinkedinUrlByProspectId)
        .expects(teamId, prospectId, Logger)
        .returning(Success(prospectObject.linkedin_url.get))

      (campaignService.getSessionCookieAndUserAgentForLinkedinSetting)
        .expects(campaignId, teamId)
        .returning(Success(linkedinSessionCookieAndUserAgent))

      (phantomBusterTeamService.getPhantomBusterApiKeyForTeam)
        .expects(teamId)
        .returning(Success(PhantomBusterApiKeyDetails.ExternalPhantomBusterApiKey(apiKey = phantomBusterApiKey)))

      (phantomBusterTeamService.fetchTeamsWithSamePhantomBusterOrgId)
        .expects(teamId)
        .returning(Success(List(teamId)))

      (phantomBusterAgentService.getIdlePhantomBusterAgentIdForScript)
        .expects(List(teamId), phantomBusterSalesNavigatorInmailScript)
        .returning(Success(Some(phantomBusterAgentId)))

      (srUuidUtils.generatePhantombusterTeamSecretKey)
        .expects(teamId, PhantomBusterApiKeyDetails.ExternalPhantomBusterApiKey(phantomBusterApiKey))
        .returning(phantomBusterWebhookTeamSecretKey.key)

      (linkedinSettingService.getProxyDetailsForLinkedinAccount)
        .expects(LinkedinSettingUuid(linkedinSettingUuid), teamId)
        .returning(Success(Some(proxyIPAndCredentials)))

      (phantomBusterApi.saveAgent(
        _: Option[PhantomBusterAgentId],
        _: Option[ProxyIPAndCredentials],
        _: PhantomBusterApiKey,
        _: PhantomBusterScript,
        _: PhantomBusterWebhookTeamSecretKey
      )(
        _: WSClient,
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(Some(phantomBusterAgentId), Some(proxyIPAndCredentials), phantomBusterApiKey, phantomBusterSalesNavigatorInmailScript, *, wSClient, ec, Logger)
        .returning(Future.successful(phantomBusterAgentId))

      (phantomBusterAgentService.changeAgentRunningStatus)
        .expects(phantomBusterAgentId, TeamIdAndInternalApiKeyId(teamId, None), true, phantomBusterSalesNavigatorInmailScript)
        .returning(Success(1))

      (phantomBusterApi.launchAgent(
        _: PhantomBusterApiKey,
        _: JsObject
      )(
        _: WSClient,
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(phantomBusterApiKey, *, wSClient, ec, Logger)
        .returning(Future.successful(phantomBusterContainerId))

      (taskService.updateContainerIdForTasks(
        _: Seq[String],
        _: PhantomBusterContainerId,
        _: TeamId
      )(
        _: ExecutionContext
      ))
        .expects(Seq(taskId), phantomBusterContainerId, teamId, ec)
        .returning(Future.failed(error))

      phantomBusterService.executeAutomatedInmailLinkedinTask(
        taskId = taskId,
        teamId = teamId,
        linkedinSettingUuid = linkedinSettingUuid,
        orgId = orgId
      )
        .map(_ => assert(false))
        .recover {
          case e => assert(e == error)
        }
    }

    it("should successfully save containerId in DB.") {
      (taskService.findDueTaskbyTaskIDAndTeamId(
        _: String,
        _: TeamId,
        _: OrgId
      )(
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(taskId, teamId, orgId, ec,Logger)
        .returning(Future.successful(autoInmailTask))

      (prospectDAOService.getLinkedinUrlByProspectId)
        .expects(teamId, prospectId, Logger)
        .returning(Success(prospectObject.linkedin_url.get))

      (campaignService.getSessionCookieAndUserAgentForLinkedinSetting)
        .expects(campaignId, teamId)
        .returning(Success(linkedinSessionCookieAndUserAgent))

      (phantomBusterTeamService.getPhantomBusterApiKeyForTeam)
        .expects(teamId)
        .returning(Success(PhantomBusterApiKeyDetails.ExternalPhantomBusterApiKey(apiKey = phantomBusterApiKey)))

      (phantomBusterTeamService.fetchTeamsWithSamePhantomBusterOrgId)
        .expects(teamId)
        .returning(Success(List(teamId)))

      (phantomBusterAgentService.getIdlePhantomBusterAgentIdForScript)
        .expects(List(teamId), phantomBusterSalesNavigatorInmailScript)
        .returning(Success(Some(phantomBusterAgentId)))

      (srUuidUtils.generatePhantombusterTeamSecretKey)
        .expects(teamId, PhantomBusterApiKeyDetails.ExternalPhantomBusterApiKey(phantomBusterApiKey))
        .returning(phantomBusterWebhookTeamSecretKey.key)

      (linkedinSettingService.getProxyDetailsForLinkedinAccount)
        .expects(LinkedinSettingUuid(linkedinSettingUuid), teamId)
        .returning(Success(Some(proxyIPAndCredentials)))

      (phantomBusterApi.saveAgent(
        _: Option[PhantomBusterAgentId],
        _: Option[ProxyIPAndCredentials],
        _: PhantomBusterApiKey,
        _: PhantomBusterScript,
        _: PhantomBusterWebhookTeamSecretKey
      )(
        _: WSClient,
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(Some(phantomBusterAgentId), Some(proxyIPAndCredentials), phantomBusterApiKey, phantomBusterSalesNavigatorInmailScript, *, wSClient, ec, Logger)
        .returning(Future.successful(phantomBusterAgentId))

      (phantomBusterApi.launchAgent(
        _: PhantomBusterApiKey,
        _: JsObject
      )(
        _: WSClient,
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(phantomBusterApiKey, *, wSClient, ec, Logger)
        .returning(Future.successful(phantomBusterContainerId))

      (taskService.updateContainerIdForTasks(
        _: Seq[String],
        _: PhantomBusterContainerId,
        _: TeamId
      )(
        _: ExecutionContext
      ))
        .expects(Seq(taskId), phantomBusterContainerId, teamId, ec)
        .returning(Future.successful(1))

      (phantomBusterAgentService.changeAgentRunningStatus)
        .expects(phantomBusterAgentId, TeamIdAndInternalApiKeyId(teamId, None), true, phantomBusterSalesNavigatorInmailScript)
        .returning(Success(1))

      phantomBusterService.executeAutomatedInmailLinkedinTask(
        taskId = taskId,
        teamId = teamId,
        linkedinSettingUuid = linkedinSettingUuid,
        orgId = orgId
      )
        .map(_ => assert(true))
        .recover {
          case e => assert(false)
        }
    }
  }

  describe("Testing createCSVAndExecuteTask") {

    it ("should fail if it is not an auto Linkedin task") {
      phantomBusterService.createCSVAndExecuteTask(
          tasksGroupedByTypeAndLinkedinSetting = tasksGroupedByTypeAndLinkedinSetting.copy(
            commonLinkedinTaskDetails = tasksGroupedByTypeAndLinkedinSetting.commonLinkedinTaskDetails.copy(
              taskType = TaskType.GeneralTask
            )
          )
        )
        .map(_ => assert(false))
        .recover {
          case e => assert(e.getMessage == s"${TaskType.GeneralTask.toKey} automation not supported.")
        }
    }

    it ("should not go in CSV flow if task type is AutoLinkedinInmail") {
      (taskService.findDueTaskbyTaskIDAndTeamId(
        _: String,
        _: TeamId,
        _: OrgId
      )(
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(taskId, teamId, orgId , ec,Logger)
        .returning(Future.successful(autoInmailTask))

      (prospectDAOService.getLinkedinUrlByProspectId)
        .expects(teamId, prospectId, Logger)
        .returning(Success(prospectObject.linkedin_url.get))

      (campaignService.getSessionCookieAndUserAgentForLinkedinSetting)
        .expects(campaignId, teamId)
        .returning(Success(linkedinSessionCookieAndUserAgent))

      (phantomBusterTeamService.getPhantomBusterApiKeyForTeam)
        .expects(teamId)
        .returning(Success(PhantomBusterApiKeyDetails.ExternalPhantomBusterApiKey(apiKey = phantomBusterApiKey)))

      (phantomBusterTeamService.fetchTeamsWithSamePhantomBusterOrgId)
        .expects(teamId)
        .returning(Success(List(teamId)))

      (phantomBusterAgentService.getIdlePhantomBusterAgentIdForScript)
        .expects(List(teamId), phantomBusterSalesNavigatorInmailScript)
        .returning(Success(Some(phantomBusterAgentId)))

      (srUuidUtils.generatePhantombusterTeamSecretKey)
        .expects(teamId, PhantomBusterApiKeyDetails.ExternalPhantomBusterApiKey(phantomBusterApiKey))
        .returning(phantomBusterWebhookTeamSecretKey.key)

      (linkedinSettingService.getProxyDetailsForLinkedinAccount)
        .expects(LinkedinSettingUuid(linkedinSettingUuid), teamId)
        .returning(Success(Some(proxyIPAndCredentials)))

      (phantomBusterApi.saveAgent(
        _: Option[PhantomBusterAgentId],
        _: Option[ProxyIPAndCredentials],
        _: PhantomBusterApiKey,
        _: PhantomBusterScript,
        _: PhantomBusterWebhookTeamSecretKey
      )(
        _: WSClient,
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(Some(phantomBusterAgentId), Some(proxyIPAndCredentials), phantomBusterApiKey, phantomBusterSalesNavigatorInmailScript, *, wSClient, ec, Logger)
        .returning(Future.successful(phantomBusterAgentId))

      (phantomBusterApi.launchAgent(
        _: PhantomBusterApiKey,
        _: JsObject
      )(
        _: WSClient,
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(phantomBusterApiKey, *, wSClient, ec, Logger)
        .returning(Future.successful(phantomBusterContainerId))

      (taskService.updateContainerIdForTasks(
        _: Seq[String],
        _: PhantomBusterContainerId,
        _: TeamId
      )(
        _: ExecutionContext
      ))
        .expects(Seq(taskId), phantomBusterContainerId, teamId, ec)
        .returning(Future.successful(1))

      (phantomBusterAgentService.changeAgentRunningStatus)
        .expects(phantomBusterAgentId, TeamIdAndInternalApiKeyId(teamId, None), true, phantomBusterSalesNavigatorInmailScript)
        .returning(Success(1))

      phantomBusterService.createCSVAndExecuteTask(
          tasksGroupedByTypeAndLinkedinSetting = tasksGroupedByTypeAndLinkedinSetting.copy(
            commonLinkedinTaskDetails = tasksGroupedByTypeAndLinkedinSetting.commonLinkedinTaskDetails.copy(
              taskType = TaskType.AutoLinkedinInmail
            )
          )
        )
        .map(_ => assert(true))
        .recover {
          case e => assert(false)
        }
    }

    it ("should fail if getLinkedinUrlOfProspectByTaskId fails") {

      (taskService.getLinkedinUrlOfProspectByTaskId(
        _: String,
        _: TeamId
      )(
        _: ExecutionContext
      ))
        .expects(taskId, teamId, ec)
        .returning(Future.failed(error))

      phantomBusterService.createCSVAndExecuteTask(
        tasksGroupedByTypeAndLinkedinSetting = tasksGroupedByTypeAndLinkedinSetting
      )
        .map(_ => assert(false))
        .recover {
          case e => assert(e == error)
        }
    }

    it ("should fail if failTask fails and linkedin profile url is not present for prospect") {

      (taskService.getLinkedinUrlOfProspectByTaskId(
        _: String,
        _: TeamId
      )(
        _: ExecutionContext
      ))
        .expects(taskId, teamId, ec)
        .returning(Future.successful(None))

      (taskService.failTask(
        _: String,
        _: TeamId,
        _: String
      )(
        _: ExecutionContext
      ))
        .expects(taskId, teamId, ExecuteAutoLinkedinTasksError.ProspectDoesNotHaveLinkedinUrl.toString, ec)
        .returning(Future.failed(error))

      phantomBusterService.createCSVAndExecuteTask(
          tasksGroupedByTypeAndLinkedinSetting = tasksGroupedByTypeAndLinkedinSetting
        )
        .map(_ => assert(false))
        .recover {
          case e => assert(e == error)
        }
    }

    it ("should fail if fetchTaskData fails") {

      (taskService.getLinkedinUrlOfProspectByTaskId(
        _: String,
        _: TeamId
      )(
        _: ExecutionContext
      ))
        .expects(taskId, teamId, ec)
        .returning(Future.successful(prospectObject.linkedin_url))

      (taskService.fetchTaskData(_: String, _: TeamId)(_: ExecutionContext))
        .expects(taskId, teamId, ec)
        .returning(Future.failed(error))

      phantomBusterService.createCSVAndExecuteTask(
          tasksGroupedByTypeAndLinkedinSetting = tasksGroupedByTypeAndLinkedinSetting
        )
        .map(_ => assert(false))
        .recover {
          case e => assert(e == error)
        }

    }

    it ("should fail if linkedin_url is not found for even a single prospect.") {

      (taskService.getLinkedinUrlOfProspectByTaskId(
        _: String,
        _: TeamId
      )(
        _: ExecutionContext
      ))
        .expects(taskId, teamId, ec)
        .returning(Future.successful(None))

      (taskService.failTask(
        _: String,
        _: TeamId,
        _: String
      )(
        _: ExecutionContext
      ))
        .expects(taskId, teamId, ExecuteAutoLinkedinTasksError.ProspectDoesNotHaveLinkedinUrl.toString, ec)
        .returning(Future.successful(1))

      phantomBusterService.createCSVAndExecuteTask(
          tasksGroupedByTypeAndLinkedinSetting = tasksGroupedByTypeAndLinkedinSetting
        )
        .map(_ => assert(false))
        .recover {
          case e => assert(e.getMessage == "Linkedin Url of Prospect not present.")
        }

    }

    it ("should fail if createUploadAndGenerateSignedURLForCSV fails") {
      (taskService.getLinkedinUrlOfProspectByTaskId(
        _: String,
        _: TeamId
      )(
        _: ExecutionContext
      ))
        .expects(taskId, teamId, ec)
        .returning(Future.successful(prospectObject.linkedin_url))

      (taskService.fetchTaskData(_: String, _: TeamId)(_: ExecutionContext))
        .expects(taskId, teamId, ec)
        .returning(Future.successful(autoMessageTask.task_data))

      (srUuidUtils.generatePhantomBusterCSVFilename)
        .expects(tasksGroupedByTypeAndLinkedinSetting.commonLinkedinTaskDetails)
        .returning(csvFileName)

      (cloudStorage.createUploadAndGenerateURLForCSV(
        _: Seq[Seq[String]],
        _: Seq[String],
        _: String,
        _: String,
        _: Int
      )(
        _: ExecutionContext
      ))
        .expects(Seq(Seq(prospectObject.linkedin_url.get, messageBody)), Seq("Profile", "Message"), csvFileName,csvUploadBucketName,signedUrlTtlInMinutes, ec)
        .returning(Future.failed(error))

      phantomBusterService.createCSVAndExecuteTask(
          tasksGroupedByTypeAndLinkedinSetting = tasksGroupedByTypeAndLinkedinSetting
        )
        .map(_ => assert(false))
        .recover {
          case e => assert(e == error)
        }
    }

    it ("should fail if session cookie is not present for linkedin account") {

      (taskService.getLinkedinUrlOfProspectByTaskId(
        _: String,
        _: TeamId
      )(
        _: ExecutionContext
      ))
        .expects(taskId, teamId, ec)
        .returning(Future.successful(prospectObject.linkedin_url))

      (taskService.fetchTaskData(_: String, _: TeamId)(_: ExecutionContext))
        .expects(taskId, teamId, ec)
        .returning(Future.successful(autoMessageTask.task_data))

      (srUuidUtils.generatePhantomBusterCSVFilename)
        .expects(tasksGroupedByTypeAndLinkedinSetting.commonLinkedinTaskDetails.copy(
          taskType = TaskType.AutoViewLinkedinProfile
        ))
        .returning(csvFileName)

      (cloudStorage.createUploadAndGenerateURLForCSV(
        _: Seq[Seq[String]],
        _: Seq[String],
        _: String,
        _: String,
        _: Int
      )(
        _: ExecutionContext
      ))
        .expects(Seq(Seq(prospectObject.linkedin_url.get)), Seq(), csvFileName,csvUploadBucketName,signedUrlTtlInMinutes,ec)
        .returning(Future.successful(publicCSVLink))

      (linkedinSettingService.getSessionCookieAndUserAgentForLinkedinSetting)
        .expects(linkedinSettingUuid, teamId)
        .returning(Failure(error))

      phantomBusterService.createCSVAndExecuteTask(
          tasksGroupedByTypeAndLinkedinSetting = tasksGroupedByTypeAndLinkedinSetting.copy(
            commonLinkedinTaskDetails = tasksGroupedByTypeAndLinkedinSetting.commonLinkedinTaskDetails.copy(
              taskType = TaskType.AutoViewLinkedinProfile
            )
          )
        )
        .map(_ => assert(false))
        .recover {
          case e => assert(e == error)
        }
    }

    it ("should fail if fetchPhantombusterApiKeyAndRequiredAgentIdAndUpdateStatus fails") {
      (taskService.getLinkedinUrlOfProspectByTaskId(
        _: String,
        _: TeamId
      )(
        _: ExecutionContext
      ))
        .expects(taskId, teamId, ec)
        .returning(Future.successful(prospectObject.linkedin_url))

      (taskService.fetchTaskData(_: String, _: TeamId)(_: ExecutionContext))
        .expects(taskId, teamId, ec)
        .returning(Future.successful(autoMessageTask.task_data))

      (srUuidUtils.generatePhantomBusterCSVFilename)
        .expects(tasksGroupedByTypeAndLinkedinSetting.commonLinkedinTaskDetails)
        .returning(csvFileName)

      (cloudStorage.createUploadAndGenerateURLForCSV(
        _: Seq[Seq[String]],
        _: Seq[String],
        _: String,
        _: String,
        _: Int
      )(
        _: ExecutionContext
      ))
        .expects(Seq(Seq(prospectObject.linkedin_url.get, messageBody)), Seq("Profile", "Message"), csvFileName,csvUploadBucketName,signedUrlTtlInMinutes, ec)
        .returning(Future.successful(publicCSVLink))

      (linkedinSettingService.getSessionCookieAndUserAgentForLinkedinSetting)
        .expects(linkedinSettingUuid, teamId)
        .returning(Success(linkedinSessionCookieAndUserAgent))

      (phantomBusterTeamService.getPhantomBusterApiKeyForTeam)
        .expects(teamId)
        .returning(Failure(error))

      phantomBusterService.createCSVAndExecuteTask(
          tasksGroupedByTypeAndLinkedinSetting = tasksGroupedByTypeAndLinkedinSetting
        )
        .map(_ => assert(false))
        .recover {
          case e => assert(e == error)
        }
    }

    it ("should fail if launchAgent API Call fails") {
      (taskService.getLinkedinUrlOfProspectByTaskId(
        _: String,
        _: TeamId
      )(
        _: ExecutionContext
      ))
        .expects(taskId, teamId, ec)
        .returning(Future.successful(prospectObject.linkedin_url))

      (taskService.fetchTaskData(_: String, _: TeamId)(_: ExecutionContext))
        .expects(taskId, teamId, ec)
        .returning(Future.successful(autoMessageTask.task_data))

      (srUuidUtils.generatePhantomBusterCSVFilename)
        .expects(tasksGroupedByTypeAndLinkedinSetting.commonLinkedinTaskDetails)
        .returning(csvFileName)

      (cloudStorage.createUploadAndGenerateURLForCSV(
        _: Seq[Seq[String]],
        _: Seq[String],
        _: String,
        _: String,
        _: Int
      )(
        _: ExecutionContext
      ))
        .expects(Seq(Seq(prospectObject.linkedin_url.get, messageBody)), Seq("Profile", "Message"), csvFileName,csvUploadBucketName,signedUrlTtlInMinutes, ec)
        .returning(Future.successful(publicCSVLink))

      (linkedinSettingService.getSessionCookieAndUserAgentForLinkedinSetting)
        .expects(linkedinSettingUuid, teamId)
        .returning(Success(linkedinSessionCookieAndUserAgent))

      (phantomBusterTeamService.getPhantomBusterApiKeyForTeam)
        .expects(teamId)
        .returning(Success(PhantomBusterApiKeyDetails.ExternalPhantomBusterApiKey(apiKey = phantomBusterApiKey)))

      (phantomBusterTeamService.fetchTeamsWithSamePhantomBusterOrgId)
        .expects(teamId)
        .returning(Success(List(teamId)))

      (phantomBusterAgentService.getIdlePhantomBusterAgentIdForScript)
        .expects(List(teamId), phantomBusterLinkedinMessageSenderScript)
        .returning(Success(Some(phantomBusterAgentId)))

      (srUuidUtils.generatePhantombusterTeamSecretKey)
        .expects(teamId, PhantomBusterApiKeyDetails.ExternalPhantomBusterApiKey(phantomBusterApiKey))
        .returning(phantomBusterWebhookTeamSecretKey.key)

      (linkedinSettingService.getProxyDetailsForLinkedinAccount)
        .expects(LinkedinSettingUuid(linkedinSettingUuid), teamId)
        .returning(Success(Some(proxyIPAndCredentials)))

      (phantomBusterApi.saveAgent(
        _: Option[PhantomBusterAgentId],
        _: Option[ProxyIPAndCredentials],
        _: PhantomBusterApiKey,
        _: PhantomBusterScript,
        _: PhantomBusterWebhookTeamSecretKey
      )(
        _: WSClient,
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(Some(phantomBusterAgentId), Some(proxyIPAndCredentials), phantomBusterApiKey, phantomBusterLinkedinMessageSenderScript, *, wSClient, ec, Logger)
        .returning(Future.successful(phantomBusterAgentId))

      (phantomBusterAgentService.changeAgentRunningStatus)
        .expects(phantomBusterAgentId, TeamIdAndInternalApiKeyId(teamId, None), true, phantomBusterLinkedinMessageSenderScript)
        .returning(Success(1))

      (phantomBusterApi.launchAgent(
        _: PhantomBusterApiKey,
        _: JsObject
      )(
        _: WSClient,
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(phantomBusterApiKey, *, wSClient, ec, Logger)
        .returning(Future.failed(error))

      phantomBusterService.createCSVAndExecuteTask(
          tasksGroupedByTypeAndLinkedinSetting = tasksGroupedByTypeAndLinkedinSetting
        )
        .map(_ => assert(false))
        .recover {
          case e => assert(e == error)
        }
    }

    it ("should fail if updateContainerIdForTasks fails") {
      (taskService.getLinkedinUrlOfProspectByTaskId(
        _: String,
        _: TeamId
      )(
        _: ExecutionContext
      ))
        .expects(taskId, teamId, ec)
        .returning(Future.successful(prospectObject.linkedin_url))

      (taskService.fetchTaskData(_: String, _: TeamId)(_: ExecutionContext))
        .expects(taskId, teamId, ec)
        .returning(Future.successful(autoMessageTask.task_data))

      (srUuidUtils.generatePhantomBusterCSVFilename)
        .expects(tasksGroupedByTypeAndLinkedinSetting.commonLinkedinTaskDetails)
        .returning(csvFileName)

      (cloudStorage.createUploadAndGenerateURLForCSV(
        _: Seq[Seq[String]],
        _: Seq[String],
        _: String,
        _: String,
        _: Int
      )(
        _: ExecutionContext
      ))
        .expects(Seq(Seq(prospectObject.linkedin_url.get, messageBody)), Seq("Profile", "Message"), csvFileName,csvUploadBucketName,signedUrlTtlInMinutes, ec)
        .returning(Future.successful(publicCSVLink))

      (linkedinSettingService.getSessionCookieAndUserAgentForLinkedinSetting)
        .expects(linkedinSettingUuid, teamId)
        .returning(Success(linkedinSessionCookieAndUserAgent))

      (phantomBusterTeamService.getPhantomBusterApiKeyForTeam)
        .expects(teamId)
        .returning(Success(PhantomBusterApiKeyDetails.ExternalPhantomBusterApiKey(apiKey = phantomBusterApiKey)))

      (phantomBusterTeamService.fetchTeamsWithSamePhantomBusterOrgId)
        .expects(teamId)
        .returning(Success(List(teamId)))

      (phantomBusterAgentService.getIdlePhantomBusterAgentIdForScript)
        .expects(List(teamId), phantomBusterLinkedinMessageSenderScript)
        .returning(Success(Some(phantomBusterAgentId)))

      (srUuidUtils.generatePhantombusterTeamSecretKey)
        .expects(teamId, PhantomBusterApiKeyDetails.ExternalPhantomBusterApiKey(phantomBusterApiKey))
        .returning(phantomBusterWebhookTeamSecretKey.key)

      (linkedinSettingService.getProxyDetailsForLinkedinAccount)
        .expects(LinkedinSettingUuid(linkedinSettingUuid), teamId)
        .returning(Success(Some(proxyIPAndCredentials)))

      (phantomBusterApi.saveAgent(
        _: Option[PhantomBusterAgentId],
        _: Option[ProxyIPAndCredentials],
        _: PhantomBusterApiKey,
        _: PhantomBusterScript,
        _: PhantomBusterWebhookTeamSecretKey
      )(
        _: WSClient,
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(Some(phantomBusterAgentId), Some(proxyIPAndCredentials), phantomBusterApiKey, phantomBusterLinkedinMessageSenderScript, *, wSClient, ec, Logger)
        .returning(Future.successful(phantomBusterAgentId))

      (phantomBusterAgentService.changeAgentRunningStatus)
        .expects(phantomBusterAgentId, TeamIdAndInternalApiKeyId(teamId, None), true, phantomBusterLinkedinMessageSenderScript)
        .returning(Success(1))

      (phantomBusterApi.launchAgent(
        _: PhantomBusterApiKey,
        _: JsObject
      )(
        _: WSClient,
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(phantomBusterApiKey, *, wSClient, ec, Logger)
        .returning(Future.successful(phantomBusterContainerId))

      (taskService.updateContainerIdForTasks(
        _: Seq[String],
        _: PhantomBusterContainerId,
        _: TeamId
      )(
        _: ExecutionContext
      ))
        .expects(List(taskId), phantomBusterContainerId, teamId, ec)
        .returning(Future.failed(error))

      phantomBusterService.createCSVAndExecuteTask(
          tasksGroupedByTypeAndLinkedinSetting = tasksGroupedByTypeAndLinkedinSetting
        )
        .map(_ => assert(false))
        .recover {
          case e => assert(e == error)
        }
    }

    it ("should successfully execute the tasks and update container id") {
      (taskService.getLinkedinUrlOfProspectByTaskId(
        _: String,
        _: TeamId
      )(
        _: ExecutionContext
      ))
        .expects(taskId, teamId, ec)
        .returning(Future.successful(prospectObject.linkedin_url))

      (taskService.fetchTaskData(_: String, _: TeamId)(_: ExecutionContext))
        .expects(taskId, teamId, ec)
        .returning(Future.successful(autoMessageTask.task_data))

      (srUuidUtils.generatePhantomBusterCSVFilename)
        .expects(tasksGroupedByTypeAndLinkedinSetting.commonLinkedinTaskDetails)
        .returning(csvFileName)

      (cloudStorage.createUploadAndGenerateURLForCSV(
        _: Seq[Seq[String]],
        _: Seq[String],
        _: String,
        _: String,
        _: Int
      )(
        _: ExecutionContext
      ))
        .expects(Seq(Seq(prospectObject.linkedin_url.get, messageBody)), Seq("Profile", "Message"), csvFileName,csvUploadBucketName,signedUrlTtlInMinutes, ec)
        .returning(Future.successful(publicCSVLink))

      (linkedinSettingService.getSessionCookieAndUserAgentForLinkedinSetting)
        .expects(linkedinSettingUuid, teamId)
        .returning(Success(linkedinSessionCookieAndUserAgent))

      (phantomBusterTeamService.getPhantomBusterApiKeyForTeam)
        .expects(teamId)
        .returning(Success(PhantomBusterApiKeyDetails.ExternalPhantomBusterApiKey(apiKey = phantomBusterApiKey)))

      (phantomBusterTeamService.fetchTeamsWithSamePhantomBusterOrgId)
        .expects(teamId)
        .returning(Success(List(teamId)))

      (phantomBusterAgentService.getIdlePhantomBusterAgentIdForScript)
        .expects(List(teamId), phantomBusterLinkedinMessageSenderScript)
        .returning(Success(Some(phantomBusterAgentId)))

      (srUuidUtils.generatePhantombusterTeamSecretKey)
        .expects(teamId, PhantomBusterApiKeyDetails.ExternalPhantomBusterApiKey(phantomBusterApiKey))
        .returning(phantomBusterWebhookTeamSecretKey.key)

      (linkedinSettingService.getProxyDetailsForLinkedinAccount)
        .expects(LinkedinSettingUuid(linkedinSettingUuid), teamId)
        .returning(Success(Some(proxyIPAndCredentials)))

      (phantomBusterApi.saveAgent(
        _: Option[PhantomBusterAgentId],
        _: Option[ProxyIPAndCredentials],
        _: PhantomBusterApiKey,
        _: PhantomBusterScript,
        _: PhantomBusterWebhookTeamSecretKey
      )(
        _: WSClient,
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(Some(phantomBusterAgentId), Some(proxyIPAndCredentials), phantomBusterApiKey, phantomBusterLinkedinMessageSenderScript, *, wSClient, ec, Logger)
        .returning(Future.successful(phantomBusterAgentId))

      (phantomBusterAgentService.changeAgentRunningStatus)
        .expects(phantomBusterAgentId, TeamIdAndInternalApiKeyId(teamId, None), true, phantomBusterLinkedinMessageSenderScript)
        .returning(Success(1))

      (phantomBusterApi.launchAgent(
        _: PhantomBusterApiKey,
        _: JsObject
      )(
        _: WSClient,
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(phantomBusterApiKey, *, wSClient, ec, Logger)
        .returning(Future.successful(phantomBusterContainerId))

      (taskService.updateContainerIdForTasks(
        _: Seq[String],
        _: PhantomBusterContainerId,
        _: TeamId
      )(
        _: ExecutionContext
      ))
        .expects(List(taskId), phantomBusterContainerId, teamId, ec)
        .returning(Future.successful(1))

      phantomBusterService.createCSVAndExecuteTask(
          tasksGroupedByTypeAndLinkedinSetting = tasksGroupedByTypeAndLinkedinSetting
        )
        .map(assert(_))
        .recover {
          case e => assert(false)
        }
    }
  }


  describe("Testing startLinkedinInboxScraping") {
    it ("should fail if fetchPhantombusterApiKeyAndRequiredAgentIdAndUpdateStatus fails") {

      (phantomBusterTeamService.getPhantomBusterApiKeyForTeam)
        .expects(teamId)
        .returning(Failure(error))

      phantomBusterService.startLinkedinInboxScraping(
        teamId = teamId,
        linkedinSettingUuid = linkedinSettingUuid,
        maxThreadsToScrape = 10
      )
        .map(_ => assert(false))
        .recover {
          case e => assert(e == error)
        }

    }

    it ("should fail if getSessionCookieForLinkedinSetting fails") {

      (phantomBusterTeamService.getPhantomBusterApiKeyForTeam)
        .expects(teamId)
        .returning(Success(PhantomBusterApiKeyDetails.ExternalPhantomBusterApiKey(apiKey = phantomBusterApiKey)))

      (phantomBusterTeamService.fetchTeamsWithSamePhantomBusterOrgId)
        .expects(teamId)
        .returning(Success(List(teamId)))

      (phantomBusterAgentService.getIdlePhantomBusterAgentIdForScript)
        .expects(List(teamId), phantomBusterLinkedinInboxScraperScript)
        .returning(Success(Some(phantomBusterAgentId)))

      (srUuidUtils.generatePhantombusterTeamSecretKey)
        .expects(teamId, PhantomBusterApiKeyDetails.ExternalPhantomBusterApiKey(phantomBusterApiKey))
        .returning(phantomBusterWebhookTeamSecretKey.key)

      (linkedinSettingService.getProxyDetailsForLinkedinAccount)
        .expects(LinkedinSettingUuid(linkedinSettingUuid), teamId)
        .returning(Success(Some(proxyIPAndCredentials)))

      (phantomBusterApi.saveAgent(
        _: Option[PhantomBusterAgentId],
        _: Option[ProxyIPAndCredentials],
        _: PhantomBusterApiKey,
        _: PhantomBusterScript,
        _: PhantomBusterWebhookTeamSecretKey
      )(
        _: WSClient,
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(Some(phantomBusterAgentId), Some(proxyIPAndCredentials), phantomBusterApiKey, phantomBusterLinkedinInboxScraperScript, *, wSClient, ec, Logger)
        .returning(Future.successful(phantomBusterAgentId))

      (phantomBusterAgentService.changeAgentRunningStatus)
        .expects(phantomBusterAgentId, TeamIdAndInternalApiKeyId(teamId, None), true, phantomBusterLinkedinInboxScraperScript)
        .returning(Success(1))

      (linkedinSettingService.getSessionCookieAndUserAgentForLinkedinSetting)
        .expects(linkedinSettingUuid, teamId)
        .returning(Failure(error))

      phantomBusterService.startLinkedinInboxScraping(
          teamId = teamId,
          linkedinSettingUuid = linkedinSettingUuid,
          maxThreadsToScrape = 10
        )
        .map(_ => assert(false))
        .recover {
          case e => assert(e == error)
        }
    }

    it ("should fail if launchAgent fails") {
      (phantomBusterTeamService.getPhantomBusterApiKeyForTeam)
        .expects(teamId)
        .returning(Success(PhantomBusterApiKeyDetails.ExternalPhantomBusterApiKey(apiKey = phantomBusterApiKey)))

      (phantomBusterTeamService.fetchTeamsWithSamePhantomBusterOrgId)
        .expects(teamId)
        .returning(Success(List(teamId)))

      (phantomBusterAgentService.getIdlePhantomBusterAgentIdForScript)
        .expects(List(teamId), phantomBusterLinkedinInboxScraperScript)
        .returning(Success(Some(phantomBusterAgentId)))

      (srUuidUtils.generatePhantombusterTeamSecretKey)
        .expects(teamId, PhantomBusterApiKeyDetails.ExternalPhantomBusterApiKey(phantomBusterApiKey))
        .returning(phantomBusterWebhookTeamSecretKey.key)

      (linkedinSettingService.getProxyDetailsForLinkedinAccount)
        .expects(LinkedinSettingUuid(linkedinSettingUuid), teamId)
        .returning(Success(Some(proxyIPAndCredentials)))

      (phantomBusterApi.saveAgent(
        _: Option[PhantomBusterAgentId],
        _: Option[ProxyIPAndCredentials],
        _: PhantomBusterApiKey,
        _: PhantomBusterScript,
        _: PhantomBusterWebhookTeamSecretKey
      )(
        _: WSClient,
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(Some(phantomBusterAgentId), Some(proxyIPAndCredentials), phantomBusterApiKey, phantomBusterLinkedinInboxScraperScript, *, wSClient, ec, Logger)
        .returning(Future.successful(phantomBusterAgentId))

      (phantomBusterAgentService.changeAgentRunningStatus)
        .expects(phantomBusterAgentId, TeamIdAndInternalApiKeyId(teamId, None), true, phantomBusterLinkedinInboxScraperScript)
        .returning(Success(1))

      (linkedinSettingService.getSessionCookieAndUserAgentForLinkedinSetting)
        .expects(linkedinSettingUuid, teamId)
        .returning(Success(linkedinSessionCookieAndUserAgent))

      (phantomBusterApi.launchAgent(
        _: PhantomBusterApiKey,
        _: JsObject
      )(
        _: WSClient,
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(phantomBusterApiKey, *, wSClient, ec, Logger)
        .returning(Future.failed(error))

      phantomBusterService.startLinkedinInboxScraping(
          teamId = teamId,
          linkedinSettingUuid = linkedinSettingUuid,
          maxThreadsToScrape = 10
        )
        .map(_ => assert(false))
        .recover {
          case e => assert(e == error)
        }
    }

    it ("should fail if updateContainerIdForInboxScraping fails") {
      (phantomBusterTeamService.getPhantomBusterApiKeyForTeam)
        .expects(teamId)
        .returning(Success(PhantomBusterApiKeyDetails.ExternalPhantomBusterApiKey(apiKey = phantomBusterApiKey)))

      (phantomBusterTeamService.fetchTeamsWithSamePhantomBusterOrgId)
        .expects(teamId)
        .returning(Success(List(teamId)))

      (phantomBusterAgentService.getIdlePhantomBusterAgentIdForScript)
        .expects(List(teamId), phantomBusterLinkedinInboxScraperScript)
        .returning(Success(Some(phantomBusterAgentId)))

      (srUuidUtils.generatePhantombusterTeamSecretKey)
        .expects(teamId, PhantomBusterApiKeyDetails.ExternalPhantomBusterApiKey(phantomBusterApiKey))
        .returning(phantomBusterWebhookTeamSecretKey.key)

      (linkedinSettingService.getProxyDetailsForLinkedinAccount)
        .expects(LinkedinSettingUuid(linkedinSettingUuid), teamId)
        .returning(Success(Some(proxyIPAndCredentials)))

      (phantomBusterApi.saveAgent(
        _: Option[PhantomBusterAgentId],
        _: Option[ProxyIPAndCredentials],
        _: PhantomBusterApiKey,
        _: PhantomBusterScript,
        _: PhantomBusterWebhookTeamSecretKey
      )(
        _: WSClient,
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(Some(phantomBusterAgentId), Some(proxyIPAndCredentials), phantomBusterApiKey, phantomBusterLinkedinInboxScraperScript, *, wSClient, ec, Logger)
        .returning(Future.successful(phantomBusterAgentId))

      (phantomBusterAgentService.changeAgentRunningStatus)
        .expects(phantomBusterAgentId, TeamIdAndInternalApiKeyId(teamId, None), true, phantomBusterLinkedinInboxScraperScript)
        .returning(Success(1))

      (linkedinSettingService.getSessionCookieAndUserAgentForLinkedinSetting)
        .expects(linkedinSettingUuid, teamId)
        .returning(Success(linkedinSessionCookieAndUserAgent))

      (phantomBusterApi.launchAgent(
        _: PhantomBusterApiKey,
        _: JsObject
      )(
        _: WSClient,
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(phantomBusterApiKey, *, wSClient, ec, Logger)
        .returning(Future.successful(phantomBusterContainerId))

      (linkedinSettingService.updateContainerIdForInboxScraping)
        .expects(linkedinSettingUuid, phantomBusterContainerId, teamId)
        .returning(Failure(error))

      phantomBusterService.startLinkedinInboxScraping(
          teamId = teamId,
          linkedinSettingUuid = linkedinSettingUuid,
          maxThreadsToScrape = 10
        )
        .map(_ => assert(false))
        .recover {
          case e => assert(e == error)
        }
    }

    it ("should successfully start inbox scraping and save container id") {
      (phantomBusterTeamService.getPhantomBusterApiKeyForTeam)
        .expects(teamId)
        .returning(Success(PhantomBusterApiKeyDetails.ExternalPhantomBusterApiKey(apiKey = phantomBusterApiKey)))

      (phantomBusterTeamService.fetchTeamsWithSamePhantomBusterOrgId)
        .expects(teamId)
        .returning(Success(List(teamId)))

      (phantomBusterAgentService.getIdlePhantomBusterAgentIdForScript)
        .expects(List(teamId), phantomBusterLinkedinInboxScraperScript)
        .returning(Success(Some(phantomBusterAgentId)))

      (srUuidUtils.generatePhantombusterTeamSecretKey)
        .expects(teamId, PhantomBusterApiKeyDetails.ExternalPhantomBusterApiKey(phantomBusterApiKey))
        .returning(phantomBusterWebhookTeamSecretKey.key)

      (linkedinSettingService.getProxyDetailsForLinkedinAccount)
        .expects(LinkedinSettingUuid(linkedinSettingUuid), teamId)
        .returning(Success(Some(proxyIPAndCredentials)))

      (phantomBusterApi.saveAgent(
        _: Option[PhantomBusterAgentId],
        _: Option[ProxyIPAndCredentials],
        _: PhantomBusterApiKey,
        _: PhantomBusterScript,
        _: PhantomBusterWebhookTeamSecretKey
      )(
        _: WSClient,
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(Some(phantomBusterAgentId), Some(proxyIPAndCredentials), phantomBusterApiKey, phantomBusterLinkedinInboxScraperScript, *, wSClient, ec, Logger)
        .returning(Future.successful(phantomBusterAgentId))

      (phantomBusterAgentService.changeAgentRunningStatus)
        .expects(phantomBusterAgentId, TeamIdAndInternalApiKeyId(teamId, None), true, phantomBusterLinkedinInboxScraperScript)
        .returning(Success(1))

      (linkedinSettingService.getSessionCookieAndUserAgentForLinkedinSetting)
        .expects(linkedinSettingUuid, teamId)
        .returning(Success(linkedinSessionCookieAndUserAgent))

      (phantomBusterApi.launchAgent(
        _: PhantomBusterApiKey,
        _: JsObject
      )(
        _: WSClient,
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(phantomBusterApiKey, *, wSClient, ec, Logger)
        .returning(Future.successful(phantomBusterContainerId))

      (linkedinSettingService.updateContainerIdForInboxScraping)
        .expects(linkedinSettingUuid, phantomBusterContainerId, teamId)
        .returning(Success(1))

      phantomBusterService.startLinkedinInboxScraping(
          teamId = teamId,
          linkedinSettingUuid = linkedinSettingUuid,
          maxThreadsToScrape = 10
        )
        .map(_ => assert(true))
        .recover {
          case _ => assert(false)
        }
    }
  }


  describe("Testing startLinkedinMessageThreadScraping") {
    it ("should fail if fetchPhantombusterApiKeyAndRequiredAgentIdAndUpdateStatus fails") {

      (phantomBusterTeamService.getPhantomBusterApiKeyForTeam)
        .expects(teamId)
        .returning(Failure(error))

      phantomBusterService.startLinkedinMessageThreadScraping(
        groupedLinkedinThreadsForMessageScraping = groupedLinkedinThreadsForMessageScraping
      )
        .map(_ => assert(false))
        .recover{
          case e => assert(e == error)
        }
    }

    it ("should fail if getSessionCookieForLinkedinSetting fails") {

      (phantomBusterTeamService.getPhantomBusterApiKeyForTeam)
        .expects(teamId)
        .returning(Success(PhantomBusterApiKeyDetails.ExternalPhantomBusterApiKey(apiKey = phantomBusterApiKey)))

      (phantomBusterTeamService.fetchTeamsWithSamePhantomBusterOrgId)
        .expects(teamId)
        .returning(Success(List(teamId)))

      (phantomBusterAgentService.getIdlePhantomBusterAgentIdForScript)
        .expects(List(teamId), phantomBusterLinkedinMessageThreadScraperScript)
        .returning(Success(Some(phantomBusterAgentId)))

      (srUuidUtils.generatePhantombusterTeamSecretKey)
        .expects(teamId, PhantomBusterApiKeyDetails.ExternalPhantomBusterApiKey(phantomBusterApiKey))
        .returning(phantomBusterWebhookTeamSecretKey.key)

      (linkedinSettingService.getProxyDetailsForLinkedinAccount)
        .expects(LinkedinSettingUuid(linkedinSettingUuid), teamId)
        .returning(Success(Some(proxyIPAndCredentials)))

      (phantomBusterApi.saveAgent(
        _: Option[PhantomBusterAgentId],
        _: Option[ProxyIPAndCredentials],
        _: PhantomBusterApiKey,
        _: PhantomBusterScript,
        _: PhantomBusterWebhookTeamSecretKey
      )(
        _: WSClient,
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(Some(phantomBusterAgentId), Some(proxyIPAndCredentials), phantomBusterApiKey, phantomBusterLinkedinMessageThreadScraperScript, *, wSClient, ec, Logger)
        .returning(Future.successful(phantomBusterAgentId))

      (phantomBusterAgentService.changeAgentRunningStatus)
        .expects(phantomBusterAgentId, TeamIdAndInternalApiKeyId(teamId, None), true, phantomBusterLinkedinMessageThreadScraperScript)
        .returning(Success(1))

      (linkedinSettingService.getSessionCookieAndUserAgentForLinkedinSetting)
        .expects(linkedinSettingUuid, teamId)
        .returning(Failure(error))

      phantomBusterService.startLinkedinMessageThreadScraping(
          groupedLinkedinThreadsForMessageScraping = groupedLinkedinThreadsForMessageScraping
        )
        .map(_ => assert(false))
        .recover {
          case e => assert(e == error)
        }
    }

    it ("should fail if createUploadAndGenerateSignedURLForCSV fails") {
      (phantomBusterTeamService.getPhantomBusterApiKeyForTeam)
        .expects(teamId)
        .returning(Success(PhantomBusterApiKeyDetails.ExternalPhantomBusterApiKey(apiKey = phantomBusterApiKey)))

      (phantomBusterTeamService.fetchTeamsWithSamePhantomBusterOrgId)
        .expects(teamId)
        .returning(Success(List(teamId)))

      (phantomBusterAgentService.getIdlePhantomBusterAgentIdForScript)
        .expects(List(teamId), phantomBusterLinkedinMessageThreadScraperScript)
        .returning(Success(Some(phantomBusterAgentId)))

      (srUuidUtils.generatePhantombusterTeamSecretKey)
        .expects(teamId, PhantomBusterApiKeyDetails.ExternalPhantomBusterApiKey(phantomBusterApiKey))
        .returning(phantomBusterWebhookTeamSecretKey.key)

      (linkedinSettingService.getProxyDetailsForLinkedinAccount)
        .expects(LinkedinSettingUuid(linkedinSettingUuid), teamId)
        .returning(Success(Some(proxyIPAndCredentials)))

      (phantomBusterApi.saveAgent(
        _: Option[PhantomBusterAgentId],
        _: Option[ProxyIPAndCredentials],
        _: PhantomBusterApiKey,
        _: PhantomBusterScript,
        _: PhantomBusterWebhookTeamSecretKey
      )(
        _: WSClient,
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(Some(phantomBusterAgentId), Some(proxyIPAndCredentials), phantomBusterApiKey, phantomBusterLinkedinMessageThreadScraperScript, *, wSClient, ec, Logger)
        .returning(Future.successful(phantomBusterAgentId))

      (phantomBusterAgentService.changeAgentRunningStatus)
        .expects(phantomBusterAgentId, TeamIdAndInternalApiKeyId(teamId, None), true, phantomBusterLinkedinMessageThreadScraperScript)
        .returning(Success(1))

      (linkedinSettingService.getSessionCookieAndUserAgentForLinkedinSetting)
        .expects(linkedinSettingUuid, teamId)
        .returning(Success(linkedinSessionCookieAndUserAgent))

      (srUuidUtils.generateFileNameForLinkedinMessageThreadScraping)
        .expects(groupedLinkedinThreadsForMessageScraping)
        .returning(randomString)

      (cloudStorage.createUploadAndGenerateURLForCSV(
        _: Seq[Seq[String]],
        _: Seq[String],
        _: String,
        _: String,
        _: Int
      )(
        _: ExecutionContext
      ))
        .expects(Seq(Seq(threadUrl.url)), Seq(), randomString,csvUploadBucketName,signedUrlTtlInMinutes, ec)
        .returning(Future.failed(error))

      phantomBusterService.startLinkedinMessageThreadScraping(
          groupedLinkedinThreadsForMessageScraping = groupedLinkedinThreadsForMessageScraping
        )
        .map(_ => assert(false))
        .recover {
          case e => assert(e == error)
        }
    }

    it ("should fail if launchAgent fails") {
      (phantomBusterTeamService.getPhantomBusterApiKeyForTeam)
        .expects(teamId)
        .returning(Success(PhantomBusterApiKeyDetails.ExternalPhantomBusterApiKey(apiKey = phantomBusterApiKey)))

      (phantomBusterTeamService.fetchTeamsWithSamePhantomBusterOrgId)
        .expects(teamId)
        .returning(Success(List(teamId)))

      (phantomBusterAgentService.getIdlePhantomBusterAgentIdForScript)
        .expects(List(teamId), phantomBusterLinkedinMessageThreadScraperScript)
        .returning(Success(Some(phantomBusterAgentId)))

      (srUuidUtils.generatePhantombusterTeamSecretKey)
        .expects(teamId, PhantomBusterApiKeyDetails.ExternalPhantomBusterApiKey(phantomBusterApiKey))
        .returning(phantomBusterWebhookTeamSecretKey.key)

      (linkedinSettingService.getProxyDetailsForLinkedinAccount)
        .expects(LinkedinSettingUuid(linkedinSettingUuid), teamId)
        .returning(Success(Some(proxyIPAndCredentials)))

      (phantomBusterApi.saveAgent(
        _: Option[PhantomBusterAgentId],
        _: Option[ProxyIPAndCredentials],
        _: PhantomBusterApiKey,
        _: PhantomBusterScript,
        _: PhantomBusterWebhookTeamSecretKey
      )(
        _: WSClient,
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(Some(phantomBusterAgentId), Some(proxyIPAndCredentials), phantomBusterApiKey, phantomBusterLinkedinMessageThreadScraperScript, *, wSClient, ec, Logger)
        .returning(Future.successful(phantomBusterAgentId))

      (phantomBusterAgentService.changeAgentRunningStatus)
        .expects(phantomBusterAgentId, TeamIdAndInternalApiKeyId(teamId, None), true, phantomBusterLinkedinMessageThreadScraperScript)
        .returning(Success(1))

      (linkedinSettingService.getSessionCookieAndUserAgentForLinkedinSetting)
        .expects(linkedinSettingUuid, teamId)
        .returning(Success(linkedinSessionCookieAndUserAgent))

      (srUuidUtils.generateFileNameForLinkedinMessageThreadScraping)
        .expects(groupedLinkedinThreadsForMessageScraping)
        .returning(randomString)

      (cloudStorage.createUploadAndGenerateURLForCSV(
        _: Seq[Seq[String]],
        _: Seq[String],
        _: String,
        _: String,
        _: Int
      )(
        _: ExecutionContext
      ))
        .expects(Seq(Seq(threadUrl.url)), Seq(), randomString,csvUploadBucketName,signedUrlTtlInMinutes, ec)
        .returning(Future.successful(publicCSVLink))

      (phantomBusterApi.launchAgent(
        _: PhantomBusterApiKey,
        _: JsObject
      )(
        _: WSClient,
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(phantomBusterApiKey, *, wSClient, ec, Logger)
        .returning(Future.failed(error))

      phantomBusterService.startLinkedinMessageThreadScraping(
          groupedLinkedinThreadsForMessageScraping = groupedLinkedinThreadsForMessageScraping
        )
        .map(_ => assert(false))
        .recover {
          case e => assert(e == error)
        }
    }

    it ("should fail if updateContainerIdForMessageThreadScraping fails") {
      (phantomBusterTeamService.getPhantomBusterApiKeyForTeam)
        .expects(teamId)
        .returning(Success(PhantomBusterApiKeyDetails.ExternalPhantomBusterApiKey(apiKey = phantomBusterApiKey)))

      (phantomBusterTeamService.fetchTeamsWithSamePhantomBusterOrgId)
        .expects(teamId)
        .returning(Success(List(teamId)))

      (phantomBusterAgentService.getIdlePhantomBusterAgentIdForScript)
        .expects(List(teamId), phantomBusterLinkedinMessageThreadScraperScript)
        .returning(Success(Some(phantomBusterAgentId)))

      (srUuidUtils.generatePhantombusterTeamSecretKey)
        .expects(teamId, PhantomBusterApiKeyDetails.ExternalPhantomBusterApiKey(phantomBusterApiKey))
        .returning(phantomBusterWebhookTeamSecretKey.key)

      (linkedinSettingService.getProxyDetailsForLinkedinAccount)
        .expects(LinkedinSettingUuid(linkedinSettingUuid), teamId)
        .returning(Success(Some(proxyIPAndCredentials)))

      (phantomBusterApi.saveAgent(
        _: Option[PhantomBusterAgentId],
        _: Option[ProxyIPAndCredentials],
        _: PhantomBusterApiKey,
        _: PhantomBusterScript,
        _: PhantomBusterWebhookTeamSecretKey
      )(
        _: WSClient,
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(Some(phantomBusterAgentId), Some(proxyIPAndCredentials), phantomBusterApiKey, phantomBusterLinkedinMessageThreadScraperScript, *, wSClient, ec, Logger)
        .returning(Future.successful(phantomBusterAgentId))

      (phantomBusterAgentService.changeAgentRunningStatus)
        .expects(phantomBusterAgentId, TeamIdAndInternalApiKeyId(teamId, None), true, phantomBusterLinkedinMessageThreadScraperScript)
        .returning(Success(1))

      (linkedinSettingService.getSessionCookieAndUserAgentForLinkedinSetting)
        .expects(linkedinSettingUuid, teamId)
        .returning(Success(linkedinSessionCookieAndUserAgent))

      (srUuidUtils.generateFileNameForLinkedinMessageThreadScraping)
        .expects(groupedLinkedinThreadsForMessageScraping)
        .returning(randomString)

      (cloudStorage.createUploadAndGenerateURLForCSV(
        _: Seq[Seq[String]],
        _: Seq[String],
        _: String,
        _: String,
        _: Int
      )(
        _: ExecutionContext
      ))
        .expects(Seq(Seq(threadUrl.url)), Seq(), randomString,csvUploadBucketName,signedUrlTtlInMinutes, ec)
        .returning(Future.successful(publicCSVLink))

      (phantomBusterApi.launchAgent(
        _: PhantomBusterApiKey,
        _: JsObject
      )(
        _: WSClient,
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(phantomBusterApiKey, *, wSClient, ec, Logger)
        .returning(Future.successful(phantomBusterContainerId))

      (linkedinMessageThreadsService.updateContainerIdForMessageThreadScraping)
        .expects(phantomBusterContainerId, groupedLinkedinThreadsForMessageScraping)
        .returning(Failure(error))

      phantomBusterService.startLinkedinMessageThreadScraping(
          groupedLinkedinThreadsForMessageScraping = groupedLinkedinThreadsForMessageScraping
        )
        .map(_ => assert(false))
        .recover {
          case e => assert(e == error)
        }
    }

    it ("should successfully start message thread scraping and save container id") {
      (phantomBusterTeamService.getPhantomBusterApiKeyForTeam)
        .expects(teamId)
        .returning(Success(PhantomBusterApiKeyDetails.ExternalPhantomBusterApiKey(apiKey = phantomBusterApiKey)))

      (phantomBusterTeamService.fetchTeamsWithSamePhantomBusterOrgId)
        .expects(teamId)
        .returning(Success(List(teamId)))

      (phantomBusterAgentService.getIdlePhantomBusterAgentIdForScript)
        .expects(List(teamId), phantomBusterLinkedinMessageThreadScraperScript)
        .returning(Success(Some(phantomBusterAgentId)))

      (srUuidUtils.generatePhantombusterTeamSecretKey)
        .expects(teamId, PhantomBusterApiKeyDetails.ExternalPhantomBusterApiKey(phantomBusterApiKey))
        .returning(phantomBusterWebhookTeamSecretKey.key)

      (linkedinSettingService.getProxyDetailsForLinkedinAccount)
        .expects(LinkedinSettingUuid(linkedinSettingUuid), teamId)
        .returning(Success(Some(proxyIPAndCredentials)))

      (phantomBusterApi.saveAgent(
        _: Option[PhantomBusterAgentId],
        _: Option[ProxyIPAndCredentials],
        _: PhantomBusterApiKey,
        _: PhantomBusterScript,
        _: PhantomBusterWebhookTeamSecretKey
      )(
        _: WSClient,
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(Some(phantomBusterAgentId), Some(proxyIPAndCredentials), phantomBusterApiKey, phantomBusterLinkedinMessageThreadScraperScript, *, wSClient, ec, Logger)
        .returning(Future.successful(phantomBusterAgentId))

      (phantomBusterAgentService.changeAgentRunningStatus)
        .expects(phantomBusterAgentId, TeamIdAndInternalApiKeyId(teamId, None), true, phantomBusterLinkedinMessageThreadScraperScript)
        .returning(Success(1))

      (linkedinSettingService.getSessionCookieAndUserAgentForLinkedinSetting)
        .expects(linkedinSettingUuid, teamId)
        .returning(Success(linkedinSessionCookieAndUserAgent))

      (srUuidUtils.generateFileNameForLinkedinMessageThreadScraping)
        .expects(groupedLinkedinThreadsForMessageScraping)
        .returning(randomString)

      (cloudStorage.createUploadAndGenerateURLForCSV(
        _: Seq[Seq[String]],
        _: Seq[String],
        _: String,
        _: String,
        _: Int
      )(
        _: ExecutionContext
      ))
        .expects(Seq(Seq(threadUrl.url)), Seq(), randomString,csvUploadBucketName,signedUrlTtlInMinutes, ec)
        .returning(Future.successful(publicCSVLink))

      (phantomBusterApi.launchAgent(
        _: PhantomBusterApiKey,
        _: JsObject
      )(
        _: WSClient,
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(phantomBusterApiKey, *, wSClient, ec, Logger)
        .returning(Future.successful(phantomBusterContainerId))

      (linkedinMessageThreadsService.updateContainerIdForMessageThreadScraping)
        .expects(phantomBusterContainerId, groupedLinkedinThreadsForMessageScraping)
        .returning(Success(1))

      phantomBusterService.startLinkedinMessageThreadScraping(
          groupedLinkedinThreadsForMessageScraping = groupedLinkedinThreadsForMessageScraping
        )
        .map(_ => assert(true))
        .recover {
          case _ => assert(false)
        }
    }
  }

  describe("Testing PhantomBusterService.parseMessageThreadScraperContainerResult") {
    it("should not fail if connectionDegree is not present in json.") {
      val expectedMessage = PhantomBusterLinkedinMessage(
        message_body = "Hi Aditya,\nInnovative DevOps are a game changer, especially in an evolving and dynamic environment.\nLearn from winning implementations across 10 categories key to DevOps success in this FREE Google Cloud DevOps Success ebook.\nYou'll discover how DevOps boost performance — like reducing deployment lead time from 4-6 months to less than an hour.\nSounds good? Let's shape the future of DevOps together!",
        sent_by_user = false,
        timestamp = aDate
      )

      PhantomBusterService.parseMessageThreadScraperContainerResult(
        result = messageThreadScraperJson
      ) match {
        case Failure(e) =>
          println(e.getMessage)
          assert(false)

        case Success(messages) =>
          assert(messages == List(expectedMessage))
      }
    }
  }
}
