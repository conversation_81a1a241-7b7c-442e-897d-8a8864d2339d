package app.utils.mq.emailscheduler

import org.apache.pekko.actor.ActorSystem
import api.AppConfig
import api.accounts.email.models.EmailServiceProvider
import api.campaigns.{CPCompleted, CampaignDAO, CampaignEditedPreviewEmailDAO, CampaignProspectDAO, CampaignProspectUpdateScheduleStatus, CampaignStepDAO, CampaignStepVariantDAO, CampaignStepVariantForScheduling, CampaignStepWithChildren, EmailsScheduledCount, PreviousFollowUp}
import api.accounts.models.{AccountProfileInfo, OrgId, ProspectAccountUuid}
import api.accounts.service.AccountOrgBillingRelatedService
import api.accounts.{Account, AccountAccess, AccountDAO, AccountMetadata, AccountService, AccountType, AccountUuid, OrgCountData, OrgMetadata, OrgPlan, OrgSettings, OrganizationRole, OrganizationWithCurrentData, TeamId, UpdateAccountProfileDuringOnboarding}
import api.calendar_app.CalendarAppService
import api.calendar_app.models.CalendarAccountData
import api.campaigns.models.CampaignStepData.{AutoEmailStep, LinkedinInmailData, getSubjectAndBodyFromStepData}
import api.campaigns.models.CampaignStepType.LinkedinInmail
import api.campaigns.models.{CampaignEmailSettingsId, CampaignSetNextToBeScheduledAtData, CampaignStepData, CampaignStepType, CampaignStepsStructure, CampaignType, CampaignTypeData, ChannelStepType, PreviousFollowUpData, SenderRotationStats}
import api.campaigns.services.{CampaignDAOService, CampaignId, CampaignProspectSchedulerService, CampaignProspectService, CampaignService, CampaignsMissingMergeTagService}
import api.columns.InternalMergeTagValuesForProspect
import api.emails.dao_service.EmailScheduledDAOService
import api.emails.{CampaignProspectStepScheduleLogsDAO, EmailMessageDataDAO, EmailScheduledDAO, EmailScheduledNewAfterSaving, EmailSettingDAO}
import api.gpt.ai_hyperpersonalized.AIHyperPersonalizedGenerator
import api.prospects.models.{EmailForValidation, ProspectAccountsId, ProspectCategory, ProspectDataForChannelScheduling, ProspectId}
import api.prospects.{ProspectAccount, ProspectForValidation, ProspectUuid}
import api.rep_tracking_hosts.service.RepTrackingHostService
import api.tasks.models.TaskData.ViewLinkedinProfileData
import api.tasks.services.TaskService
import api.tasks.models.{TaskData, TaskPriority, TaskStatusType}
import api.tasks.pgDao.TaskPgDAO
import app.test_fixtures.accounts.OrgCountDataFixture
import app.test_fixtures.organizationa.{OrgMetadataFixture, OrgPlanFixture}
import app.test_fixtures.prospect.{ProspectAccountFixture, ProspectFixtures}
import eventframework.{ProspectObject, ProspectObjectInternal}
import io.smartreach.esp.api.emails.EmailSettingId
import org.joda.time.DateTime
import org.scalamock.matchers.ArgCapture.CaptureOne
import org.scalamock.scalatest.AsyncMockFactory
import org.scalatest.flatspec.AsyncFlatSpec
import play.api.libs.json.Json
import sr_scheduler.{CampaignStatus, models}
import sr_scheduler.models.{CampaignEmailPriority, CampaignForScheduling, CampaignWarmupSetting, ChannelData, ChannelType, EmailScheduledNew, EmailSettingCreateEmailSchedule, ScheduledProspectsCountForCampaign, ScheduledProspectsCountForCampaignEmail}
import play.api.libs.ws.ahc.AhcWSClient
import sr_scheduler.models.CampaignForScheduling.CampaignEmailSettingForScheduler
import utils.{ISRLogger, SRLogger}
import utils.cache_utils.model.CacheIdKeyForLock
import utils.cache_utils.service.SrRedisSimpleLockServiceV2
import utils.dateTime.SrDateTimeUtils
import utils.dbutils.DBUtils
import utils.email.{EmailBodyService, EmailService, EmailServiceCompanion}
import utils.email_notification.service.EmailNotificationService
import utils.emailvalidation.EmailValidationService
import utils.emailvalidation.models.{EmailValidationInitiator, EmailValidationPriority, EmailsForValidationWithInitiator, IdsOrEmailsForValidation}
import utils.featureflags.dao.OrgMetadataDAO
import utils.featureflags.services.OrgMetadataService
import utils.helpers.LogHelpers
import api.campaigns.CampaignEditedPreviewEmailDAO
import api.emails.models.EmailReplyType
import api.llm.dao.LlmAuditLogDAO
import utils.mq.ai_content_generation.MqAiContentGenerationPublisher
import utils.mq.channel_scheduler.channels.model.SchedulerSteps
import utils.mq.channel_scheduler.channels.service.EmailSchedulerJedisService
import utils.mq.channel_scheduler.channels.{ChannelSchedulerTrait, Count, EmailChannelScheduler, GeneralChannelScheduler, ScheduleTasksData, StepType, StepTypeAndCount}
import utils.mq.channel_scheduler.{ChannelSchedulerService, FetchCampaignStepsData, LastSentStepData, MqCampaignSchedulingMetadataMigration, SchedulerMapStepIdAndDelay, TableTypeForScheduler}
import utils.mq.webhook.MQWebhookCompleted
import utils.random.SrRandomUtils
import utils.shuffle.SrShuffleUtils
import utils.templating.TemplateService
import utils.uuid.SrUuidUtils
import utils.testapp.TestAppExecutionContext
import utils_deploy.rolling_updates.models.SrRollingUpdateFeature
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService

import scala.Console.println
import scala.collection.immutable.{List, Set}
import scala.concurrent.ExecutionContext
import scala.util.{Failure, Success}
import scala.concurrent.Future

class ChannelSchedulerServiceSpec extends AsyncFlatSpec with AsyncMockFactory {

  implicit lazy val system: ActorSystem = TestAppExecutionContext.actorSystem
  implicit lazy val ec: ExecutionContext = system.dispatcher
  implicit lazy val wSClient: AhcWSClient = TestAppExecutionContext.wsClient
  given Logger: SRLogger = new SRLogger("ChannelSchedulerServiceSpec")
  val Error = new Throwable("Error")
  //val accountDAO: AccountDAO = mock[AccountDAO]
  val templateService: TemplateService =  mock[TemplateService]
  val campaignProspectDAO: CampaignProspectDAO = mock[CampaignProspectDAO]
  val emailSettingDAO: EmailSettingDAO =  mock[EmailSettingDAO]
  val emailScheduledDAO: EmailScheduledDAO = mock[EmailScheduledDAO]
  val campaignStepVariantDAO: CampaignStepVariantDAO = mock[CampaignStepVariantDAO]
  val mqWebhookCompleted: MQWebhookCompleted = mock[MQWebhookCompleted]
  val calendarAppService: CalendarAppService = mock[CalendarAppService]
  val campaignProspectStepScheduleLogsDAO = mock[CampaignProspectStepScheduleLogsDAO]
  val accountService: AccountService = mock[AccountService]
  val taskService: TaskService = mock[TaskService]
  val emailValidationService: EmailValidationService = mock[EmailValidationService]
  val emailNotificationService: EmailNotificationService = mock[EmailNotificationService]
  val campaignStepDAO: CampaignStepDAO = mock[CampaignStepDAO]
  val campaignEditedPreviewEmailDAO: CampaignEditedPreviewEmailDAO = mock[CampaignEditedPreviewEmailDAO]
  val campaignsMissingMergeTagService: CampaignsMissingMergeTagService = mock[CampaignsMissingMergeTagService]
  val campaignService: CampaignService = mock[CampaignService]
  val srRandomUtils = new SrRandomUtils()
  val srShuffleUtils = new SrShuffleUtils()
  val taskDAO = mock[TaskPgDAO]
  val campaignProspectService = mock[CampaignProspectService]
  val orgMetadataService = mock[OrgMetadataService]
  val srRedisSimpleLockServiceV2 = mock[SrRedisSimpleLockServiceV2]
  val srRollingUpdateCoreService = mock[SrRollingUpdateCoreService]
  val emailService: EmailService = mock[EmailService]
  val campaignDAOService: CampaignDAOService = mock[CampaignDAOService]
  val emailBodyService : EmailBodyService = mock[EmailBodyService]
  val aiHyperPersonalizedService: AIHyperPersonalizedGenerator = mock[AIHyperPersonalizedGenerator]
  val emailScheduledDAOService = mock[EmailScheduledDAOService]

  val emailServiceCompanion: EmailServiceCompanion = new EmailServiceCompanion (
    templateService = templateService,
    campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO,
    emailScheduledDAOService = emailScheduledDAOService,
    emailBodyService = emailBodyService
  )
  val emailSchedulerJedisService = mock[EmailSchedulerJedisService]
  val repTrackingHostService = mock[RepTrackingHostService]
//  val dbUtils = mock[DBUtils]

  val mqCampaignSchedulingMetadataMigration = mock[MqCampaignSchedulingMetadataMigration]
  val accountOrgBillingRelatedService = mock[AccountOrgBillingRelatedService]
  val mqAiContentGenerationPublisher = mock[MqAiContentGenerationPublisher]
  val llmAuditLogDAO = mock[LlmAuditLogDAO]

  val srDateTimeUtils = mock[SrDateTimeUtils]
  val srUuidUtils: SrUuidUtils = mock[SrUuidUtils]


  //val emailChannelScheduler: EmailChannelScheduler = mock[EmailChannelScheduler]
  val emailChannelScheduler: EmailChannelScheduler = new EmailChannelScheduler(

    emailValidationService = emailValidationService,
    emailServiceCompanion = emailServiceCompanion,
//    accountService = accountService,
    srRandomUtils = srRandomUtils,
    orgMetadataService = orgMetadataService,
    emailSettingDAO = emailSettingDAO,
    emailSchedulerJedisService = emailSchedulerJedisService,
    repTrackingHostService = repTrackingHostService,
//    dbUtils = dbUtils,
    mqCampaignSchedulingMetadataMigration = mqCampaignSchedulingMetadataMigration,
    emailScheduledDAOService = emailScheduledDAOService,
    srDateTimeUtils = srDateTimeUtils,
    campaignProspectService = campaignProspectService,
    aiHyperPersonalizedService = aiHyperPersonalizedService,
    campaignDAOService = campaignDAOService,
    srRollingUpdateCoreService = srRollingUpdateCoreService,
    llmAuditLogDAO = llmAuditLogDAO,
    srUuidUtils = srUuidUtils
  )

  val timezone = "Asia/Kolkata"

  val orgMetadata: OrgMetadata = OrgMetadataFixture.orgMetadataFixture2
  val data: UpdateAccountProfileDuringOnboarding = UpdateAccountProfileDuringOnboarding(
    first_name = "Animesh",
    last_name = "Kumar",
    org_name = Some("AnimeshKumar"),
    onboarding_phone_number = Some("+************")
  )
  val profile: AccountProfileInfo = AccountProfileInfo(
    first_name = "Animesh",
    last_name = "Kumar",
    company = Some("AnimeshKumar"),
    timezone = Some("Asia/Kolkata"),
    country_code = Some("IN"),
    mobile_country_code = Some("+91"),
    mobile_number = Some(9515253545L),
    twofa_enabled = false,
    has_gauthenticator = false,
    weekly_report_emails = Some("<EMAIL>"),
    scheduled_for_deletion_at = None,
    onboarding_phone_number = Some("+************")
  )

  val orgCountData: OrgCountData = OrgCountDataFixture.orgCountData_default

  val orgPlan: OrgPlan = OrgPlanFixture.orgPlanFixture

  val orgSettings: OrgSettings = OrgSettings(
    enable_ab_testing = false,
    disable_force_send = false,
    bulk_sender = false,
    allow_2fa = false,
    show_2fa_setting = false,
    enforce_2fa = false,
    allow_native_crm_integration = false,
      agency_option_allow_changing = false,
      agency_option_show = false
  )
  val accountMetadata: AccountMetadata = AccountMetadata(
    // account_ui_version = None,
    is_profile_onboarding_done = None
  )

  val teamId = TeamId(13L)


  val org: OrganizationWithCurrentData = OrganizationWithCurrentData(

    id = 1,
    name = "AK",
    owner_account_id = 2,

    counts = orgCountData,
    settings = orgSettings,
    plan = orgPlan,

    is_agency = true,
    trial_ends_at = DateTime.now().plusDays(100),
    error = None,
    error_code = None,
    paused_till = None,
    errors = Seq(),
    warnings = Seq(),
    via_referral = false,
    org_metadata = orgMetadata
  )
  val accountAdmin: Account = Account(
    id = AccountUuid("account_uuid"),
    internal_id = 2,
    email = "<EMAIL>",
    email_verification_code = None,
    email_verification_code_created_at = None,
    created_at = DateTime.now().minusDays(1000),
    first_name = Some("Animesh"),
    last_name = Some("Kumar"),
    company = Some("AK"),
    timezone = None,
    profile = profile,
    org_role = Some(OrganizationRole.OWNER),
    teams = Seq(),
    account_type =  AccountType.AGENCY,
    org = org,
    active = true,
    email_notification_summary = "dSFA",
    account_metadata = accountMetadata,
    email_verified = true,
    signupType = None,
    account_access = AccountAccess(
      inbox_access = false
    ),
    calendar_account_data = None

  )


  val emailSettingQuotaPerDay = 100

  val  emailSettingCreateEmailSchedule: EmailSettingCreateEmailSchedule =  EmailSettingCreateEmailSchedule(
    id = 1,
    team_id = 2,
    org_id = OrgId(org.id),
    account_id = 1,
    email = "<EMAIL>",
    sender_name = "Animesh Kumar",
    first_name = "Animesh",
    last_name = "Kumar",
    quota_per_day = emailSettingQuotaPerDay,
    donot_enforce_24_hour_limit_till = None,
    min_delay_seconds = 15,
    max_delay_seconds = 30,
    latest_email_scheduled_at = None,
    default_tracking_domain = "DCBA",
    default_unsubscribe_domain = "ABCD",
    rep_tracking_host_id = 1,
    custom_tracking_domain = None,
    signature = None,
    account_timezone = "Asia/Kolkata",
    bulk_sender = false
  )
  val campaignForScheduling: CampaignForScheduling.CampaignForSchedulingEmail = models.CampaignForScheduling.CampaignForSchedulingEmail(
    campaign_id = 1,
    campaign_owner_id = 1,
    team_id = 2,
    org_id = 1,
    campaign_name = "Animesh Kumar",
    status = CampaignStatus.RUNNING,
    campaign_type_data = CampaignTypeData.MultiChannelCampaignData(head_step_id = 1),
    ai_generation_context = None,
    sending_holiday_calendar_id = Some(123),
    campaign_email_setting = CampaignEmailSettingForScheduler(
      sender_email_settings_id = 1,
      receiver_email_settings_id = 1,
      campaign_email_settings_id = CampaignEmailSettingsId(123),
      emailServiceProvider = EmailServiceProvider.OTHER
    ),
    append_followups = false,
    open_tracking_enabled = false,
    click_tracking_enabled = false,
    opt_out_msg = "opt out {{unsubscribe_link}}",
    opt_out_is_text = false,
    timezone = "Asia/Kolkata",
    daily_from_time = 0,
    daily_till_time = 86400,
    days_preference = List(true, true, true, true, true, true, true),
    email_priority =  CampaignEmailPriority.EQUAL,
    max_emails_per_prospect_per_day = 100,
    max_emails_per_prospect_per_week = 1000,
    max_emails_per_prospect_account_per_day = 100,
    max_emails_per_prospect_account_per_week = 1000,
    campaign_max_emails_per_day = 1000,
    softstart_setting = None,
    mark_completed_after_days = 1,
    latest_email_scheduled_at = None,
    from_email = "<EMAIL>",
    from_name = "Animesh",
    reply_to_email = "<EMAIL>",
    reply_to_name = "Animesh",
    min_delay_seconds = 1,
    max_delay_seconds = 1,
    enable_email_validation = true,
    rep_mail_server_id = 1,
    via_gmail_smtp = None,
    prospects_remaining_to_be_scheduled_exists = Some(true),
    count_of_sender_emails = 1,
    selected_calendar_data = None
  )

  val emailChannelScheduledProspectsCountForCampaign = emailChannelScheduler.EmailChannelScheduledProspectsCountForCampaign(
    campaign = campaignForScheduling,
    counts = Seq(ScheduledProspectsCountForCampaign(
      campaignId = campaignForScheduling.campaign_id,
      campaignStepType = CampaignStepType.AutoEmailStep,
      newCount = 10,
      followupCount = 10,
      newCountNotSent = 10,
      followupCountNotSent = 10
    ))
  )

  val emailStep = AutoEmailStep(
    subject = "variant subject",
    body = "Variant body",
  )
  val campaignStepVariantForScheduling = CampaignStepVariantForScheduling(
    id = 1,
    step_id = 1,
    campaign_id = 1,
    template_id = None,
    step_data = emailStep,
    step_label = None,
    step_delay = 10,
    notes = Some("Test Notes"),
    priority = Some(TaskPriority.Normal),
    active = true,
    scheduled_count = 1
  )
  val  campaignStepWithChildren =  CampaignStepWithChildren(
    id = 1,
    label = None,
    campaign_id = 1,
    delay = 10,
    step_type = CampaignStepType.AutoEmailStep,
    created_at = DateTime.parse("2022-03-21T11:58:03.294Z"),
    children = List(2, 3, 4),
    variants = Seq(
      campaignStepVariantForScheduling,
      campaignStepVariantForScheduling.copy(id = 2),
      campaignStepVariantForScheduling.copy(id = 3)
    )
  )
  val stepsMappedById = Map(
    (1.toLong, campaignStepWithChildren),
    (2.toLong, campaignStepWithChildren
      .copy(
        id = 2,
        variants = Seq(
          campaignStepVariantForScheduling.copy(step_id = 2),
          campaignStepVariantForScheduling.copy(id = 2, step_id = 2),
          campaignStepVariantForScheduling.copy(id = 3, step_id = 2)
        ),
        children = List()
      )
    ),
    (3.toLong, campaignStepWithChildren.copy(id = 3,
      variants = Seq(
        campaignStepVariantForScheduling.copy(step_id = 3),
        campaignStepVariantForScheduling.copy(id = 2, step_id = 3),
        campaignStepVariantForScheduling.copy(id = 3, step_id = 3)
      ),
      children = List()
    ))
  )
  val emailScheduledNewAfterSaving =  EmailScheduledNewAfterSaving(
    email_scheduled_id = 1,
    campaign_id = Some(1),
    step_id = Some(1),
    prospect_id = Some(1),
    to_email = "<EMAIL>",
    reply_to_email = Some("<EMAIL>"),
    step_type = CampaignStepType.AutoEmailStep,
    from_email = "<EMAIL>",
    added_at = DateTime.now().minusMonths(10),
    scheduled_at = DateTime.now().plusMinutes(10),
    sender_email_settings_id = 1,
    template_id = None,
    variant_id = Some(1),
    rep_mail_server_id = 1,
    campaign_email_setting_id = CampaignEmailSettingsId(123),
    team_id = teamId,
    to_name = None, from_name = "Animesh", reply_to_name = None, body = None, base_body = None, text_body = None, subject = None
  )
  val scheduledProspectsCountForCampaignEmail = ScheduledProspectsCountForCampaignEmail(
    campaignId = 1,
    newCount = 1,
    followupCount = 1,
    newCountNotSent = 1,
    followupCountNotSent = 1
  )

  val stepType = StepType(
    step_type_value = CampaignStepType.AutoEmailStep.toKey
  )

  val emailChannelStepTypDataForScheduling = emailChannelScheduler.ChannelStepTypeDataForScheduling(
    campaignStepType = CampaignStepType.AutoEmailStep,
    totalScheduledForStepTypeTillNow = 3,
    channelStepTypeDailyLimit = 5,
    remainingToBeScheduledFromChannelStepType = 2,
    campaignStepTypeLimitHasBeenReachedForToday = false,
    stepType = stepType
  )

  val channel_follow_up_data = PreviousFollowUpData.AutoEmailFollowUp(
    email_thread_id = Some(1), // it is 1 before itself we are just fixing compile error now Date: 16/03/2023
    from_name = "Prateek Bhat",
    base_body = "This is previous test body",
    body = "This is previous test body",
    subject = "Hey {{first_name}}",
    from_email = "<EMAIL>",
    is_edited_preview_email = false,
  )

  val previousFollowUp = PreviousFollowUp(
    channel_follow_up_data = channel_follow_up_data,
    sent_at = DateTime.now().minusDays(5),
    timezone = "Asia/Kolkata",
    step_id = None,
    completed_reason = None
  )

  val prospectObjectInternal = ProspectFixtures.prospectObjectInternal

  val prospectIds = Set(ProspectId(3L), ProspectId(5L), ProspectId(7L), ProspectId(17L), ProspectId(19L))

  val prospectObject = ProspectObject(
    id = 1,
    owner_id = 1,
    team_id = 2,
    first_name = Some("Animesh"),
    last_name = Some("Kumar"),
    email = Some("<EMAIL>"),
    custom_fields = Json.obj("customField" -> "this is a custom filed"),
    list = None,
    job_title = Some("SDE"),
    company = Some("Smartreach"),
    linkedin_url = Some("AnimeshKumar1234"),
    phone = None,
    phone_2 = None,
    phone_3 = None,
    city = Some("India"),
    state = Some("Maha"),
    country = Some("India"),
    timezone = Some("Asia/Kolkata"),
    prospect_category = "",
    last_contacted_at = None,
    last_contacted_at_phone = None,
    created_at = DateTime.now().minusMonths(10),
    internal = prospectObjectInternal,
    latest_reply_sentiment_uuid = None,
    current_step_type = None,
    latest_task_done_at = None,
    prospect_uuid = Some(ProspectUuid("prs_aa_abcdefghi")),
    owner_uuid = AccountUuid("acc_aa_abcdegfhi"),
    updated_at = DateTime.now()
  )

  val internalMergeTagValuesForProspect = InternalMergeTagValuesForProspect(
    sender_name = "Prateek Bhat",
    sender_first_name = "Prateek",
    sender_last_name = "Bhat",
    unsubscribe_link = None,
    previous_subject = None,
    signature = None,
    sender_phone_number = None,
    calendar_link =  Some(AppConfig.CalendarApp.baseUrl + "/animesh-kumar/15min/gewte===")
  )

  val prospectForScheduling = ProspectDataForChannelScheduling.EmailChannelProspectForScheduling(
    prospect = prospectObject,
    current_step_status_data = None,
    current_step_id = Some(10L),
    email_checked = false,
    email_sent_for_validation = false,
    email_sent_for_validation_at = None)


  val prospectForValidation = ProspectForValidation(
    id = 1,
    email = "<EMAIL>",
    email_checked = false,
    email_sent_for_validation = false,
    email_sent_for_validation_at = None
  )

  val prospectsFoundForSchedulingByStepType = emailChannelScheduler.ProspectsFoundForSchedulingByStepType(
    prospects = List(prospectForScheduling),
    step_type = emailChannelStepTypDataForScheduling
  )

  val prospectsFoundByStepType: emailChannelScheduler.ProspectsFoundByStepType = Map(
    CampaignStepType.AutoEmailStep -> prospectsFoundForSchedulingByStepType
  )

  val flattenedProspects = emailChannelScheduler.flattenProspectsFoundForSchedulingByStepType(
    prospectsFoundByStepType = prospectsFoundByStepType
  )

  val scheduleCampaign = emailChannelScheduler.ScheduleCampaign(
    markedCompletedIds = Seq(),
    campaign = campaignForScheduling,
    stepsMappedById = stepsMappedById,
    campaignStepsStructure = CampaignStepsStructure.MultichannelCampaignStepsStructure(
      orderedStepIds = Vector(1, 2, 3)
    ),
    prospects = flattenedProspects,
    distinctTimezones = Set()
  )

  // === Tests start here

  "getNextStepId with currentStep id is empty so is ordered steps" should "give None" in {

    val orderedStepIds = Vector[Long]()

    val result = ChannelSchedulerService.getNextStepId(
      currentStepId = None,
      prospectObjectOpt = None,
      isProspectConnectedToLinkedin = false,
      campaignStepsStructure = CampaignStepsStructure.MultichannelCampaignStepsStructure(
        orderedStepIds = orderedStepIds
      ),
      lastSentSteps = List(),
      stepsMappedById = stepsMappedById
    )

    Logger.info(s"result --- $result")
    assert(result.isLeft)
  }

  "getNextStepId with currentStep id is empty " should "give 1" in {

    val orderedStepIds = Vector[Long](1)

    val result = ChannelSchedulerService.getNextStepId(
      currentStepId = None,
      prospectObjectOpt = None,
      isProspectConnectedToLinkedin = false,
      campaignStepsStructure = CampaignStepsStructure.MultichannelCampaignStepsStructure(
        orderedStepIds = orderedStepIds
      ),
      lastSentSteps = List(),
      stepsMappedById = stepsMappedById
    )

    Logger.info(s"result --- $result")
    assert(result.contains(1))
  }

  "getNextStepId with currentStep id is defined but not in ordered steps " should "give 3" in {

    val orderedStepIds = Vector[Long](3, 2, 4)

    val result = ChannelSchedulerService.getNextStepId(
      currentStepId = Some(1),
      prospectObjectOpt = None,
      isProspectConnectedToLinkedin = false,
      campaignStepsStructure = CampaignStepsStructure.MultichannelCampaignStepsStructure(
        orderedStepIds = orderedStepIds
      ),
      lastSentSteps = List(),
      stepsMappedById = stepsMappedById
    )

    Logger.info(s"result --- $result")
    assert(result.contains(3))
  }

  "getNextStepId with currentStep id is defined " should "give 2" in {

    val orderedStepIds = Vector[Long](1, 2, 4)

    val result = ChannelSchedulerService.getNextStepId(
      currentStepId = Some(1),
      prospectObjectOpt = None,
      isProspectConnectedToLinkedin = false,
      campaignStepsStructure = CampaignStepsStructure.MultichannelCampaignStepsStructure(
        orderedStepIds = orderedStepIds
      ),
      lastSentSteps = List(),
      stepsMappedById = stepsMappedById
    )

    Logger.info(s"result --- $result")
    assert(result.contains(2))
  }

  "getNextStepId with currentStep id is defined and at the end of the vector" should "give None" in {

    val orderedStepIds = Vector[Long](1, 2, 4)

    val result = ChannelSchedulerService.getNextStepId(
      currentStepId = Some(4),
      prospectObjectOpt = None,
      isProspectConnectedToLinkedin = false,
      campaignStepsStructure = CampaignStepsStructure.MultichannelCampaignStepsStructure(
        orderedStepIds = orderedStepIds
      ),
      lastSentSteps = List(),
      stepsMappedById = stepsMappedById
    )

    Logger.info(s"result --- $result")
    assert(result.isLeft)
  }

  "getNextStepId" should "give Success since delay is not met and the email is opened" in {


    val result = ChannelSchedulerService.getNextStepId(
      currentStepId = Some(1),
      isProspectConnectedToLinkedin = false,
      prospectObjectOpt = Some(prospectObject),
      campaignStepsStructure = CampaignStepsStructure.DripCampaignStepsStructure(
        nodes = List(
          Json.obj("id" -> "has_email", "position" -> Json.obj("x" -> 0, "y" -> 0), "data" -> Json.obj("label" -> "has_email", "type" -> "condition"),"type" -> "condition"),
          Json.obj("id" -> "1", "position" -> Json.obj("x" -> 0, "y" -> 0), "data" -> Json.obj("label" -> "1", "type" -> "step"),"type" -> "condition"),
          Json.obj("id" -> "has_opened", "position" -> Json.obj("x" -> 0, "y" -> 0), "data" -> Json.obj("label" -> "has_opened", "type" -> "condition"),"type" -> "condition"),
          Json.obj("id" -> "2", "position" -> Json.obj("x" -> 0, "y" -> 0), "data" -> Json.obj("label" -> "2", "type" -> "step"),"type" -> "condition"),
        ),
        edges = List(
          Json.obj("id" -> "xyz", "source" -> "has_email", "target" -> "1", "label" -> "no_condition"),
          Json.obj("id" -> "abc", "source" -> "1", "target" -> "has_opened", "label" -> "yes"),
          Json.obj("id" -> "abc", "source" -> "has_opened", "target" -> "2", "label" -> "yes"),
        ),
        head_node_id = "has_email"
        ),
      lastSentSteps = List(
        LastSentStepData(
          sent_id = "1234",
          channel_type = ChannelType.EmailChannel,
          sent_at = DateTime.now().minusDays(1),
          bounced = Some(false),
          replied = Some(false),
          replied_at = None,
          clicked = Some(false),
          opened = Some(true),
          opened_at = Some(DateTime.now().minusHours(13)),
          reply_type = Some(EmailReplyType.NOT_CATEGORIZED),
          failure_reason = None,
          call_status = None,
          reply_sentiment = None,
          table_type = TableTypeForScheduler.EmailScheduled,
          task_status = TaskStatusType.Done)
      ),
      stepsMappedById = Map(
        (1.toLong, campaignStepWithChildren),
        (2.toLong, campaignStepWithChildren.copy(delay = 172800)))
    )

    println(s"result --- $result")
    assert(result.isRight)
  }

  "getNextStepId" should "give 3 since the delay is met even when we have an open" in {


    val result = ChannelSchedulerService.getNextStepId(
      currentStepId = Some(1),
      prospectObjectOpt = Some(prospectObject),
      isProspectConnectedToLinkedin = false,
      campaignStepsStructure = CampaignStepsStructure.DripCampaignStepsStructure(
        nodes = List(
          Json.obj("id" -> "has_email", "position" -> Json.obj("x" -> 0, "y" -> 0), "data" -> Json.obj("label" -> "has_email", "type" -> "condition"),"type" -> "condition"),
          Json.obj("id" -> "1", "position" -> Json.obj("x" -> 0, "y" -> 0), "data" -> Json.obj("label" -> "1", "type" -> "step"), "type" -> "condition"),
          Json.obj("id" -> "has_opened", "position" -> Json.obj("x" -> 0, "y" -> 0), "data" -> Json.obj("label" -> "has_opened", "type" -> "condition"),"type" -> "condition"),
          Json.obj("id" -> "2", "position" -> Json.obj("x" -> 0, "y" -> 0), "data" -> Json.obj("label" -> "2", "type" -> "step"),"type" -> "condition"),
          Json.obj("id" -> "3", "position" -> Json.obj("x" -> 0, "y" -> 0), "data" -> Json.obj("label" -> "3", "type" -> "step"),"type" -> "condition"),
        ),
        edges = List(
          Json.obj("id" -> "xyz", "source" -> "has_email", "target" -> "1", "label" -> "no_condition"),
          Json.obj("id" -> "abc", "source" -> "1", "target" -> "has_opened", "label" -> "yes"),
          Json.obj("id" -> "abc", "source" -> "has_opened", "target" -> "2", "label" -> "yes"),
          Json.obj("id" -> "abc", "source" -> "has_opened", "target" -> "3", "label" -> "no"),
        ),
        head_node_id = "has_email"
      ),
      lastSentSteps = List(
        LastSentStepData(
          sent_id = "1234",
          channel_type = ChannelType.EmailChannel,
          sent_at = DateTime.now().minusDays(3),
          bounced = Some(false),
          replied = Some(false),
          replied_at = None,
          clicked = Some(false),
          opened = Some(true),
          opened_at = Some(DateTime.now().minusHours(13)),
          reply_type = Some(EmailReplyType.NOT_CATEGORIZED),
          failure_reason = None,
          call_status = None,
          reply_sentiment = None,
          table_type = TableTypeForScheduler.EmailScheduled,
          task_status = TaskStatusType.Done)
      ),
      stepsMappedById = Map(
        (1.toLong, campaignStepWithChildren),
        (2.toLong, campaignStepWithChildren.copy(delay = 172800)),
      (3.toLong, campaignStepWithChildren.copy(delay = 172800)))
    )

    Logger.info(s"result --- $result")
    assert(result.contains(3))
  }


  "getMapOfStepIdAndRequiredDelay" should "give vector of delay and id" in {
    val result = ChannelSchedulerService.getMapOfStepIdAndRequiredDelay(
      headStepId = 1,
      orderedStepIds = Vector(1,2,3),
      stepsMappedById = stepsMappedById
    )
    Logger.info(s"result -- $result")
    assert(result == Vector(
      SchedulerMapStepIdAndDelay(
        is_head_step_in_the_campaign = true,
        currentStepType = api.campaigns.models.CampaignStepType.AutoEmailStep,
        nextStepType    = api.campaigns.models.CampaignStepType.AutoEmailStep,
        currentStepId = 1,
        delayTillNextStep = 10
      ),
      SchedulerMapStepIdAndDelay(
        is_head_step_in_the_campaign = false,
        currentStepType = api.campaigns.models.CampaignStepType.AutoEmailStep,
        nextStepType    = api.campaigns.models.CampaignStepType.AutoEmailStep,
        currentStepId = 2,
        delayTillNextStep = 10
      )
    ))
  }

  val campaignStepVariantForSchedulingLinkedin = campaignStepVariantForScheduling.copy(
    step_data = LinkedinInmailData(
      subject = Some("Linkedin Inmail Subject"),
      body = "Linkedin Inmail Body"
    )
  )

  val campaignStepWithChildrenLinkedin = campaignStepWithChildren.copy(
    step_type = LinkedinInmail,
    variants = Seq(campaignStepVariantForSchedulingLinkedin),
    id = 2,
    children = List()
  )

  val cmp_step_var_var1 = CampaignStepVariantForScheduling(
    id = 290451,
    step_id = 234013,
    campaign_id = 87863,
    template_id = null,
    step_data = AutoEmailStep(
      subject = "book more sales meetings",
      body = "Hi {{first_name}},<br /><br />{{opening_lines}}<br /><br />83% of businesses are unable to maximize their reve nues vis-a-vis the resources &amp; efforts they put into generating sales qualified prospects.<br /><br />Is your team struggling with this?   <strong>Let's connect.</strong><br /><br />SmartReach.io is a multichannel sales outreach tool to help you minimize lead wastage &amp; increas e your sales team's efficiency. <br /><br />You can run outreach via Email, Linkedin, WhatsApp, SMS &amp; Calls<br /><br />Cheers<br />Bala<br />Sales Specialist<br />14 Days Free Trial"
    ),
    label = None,
    step_label = Some("Day 1: Opening"),
    step_delay = 86400,
    active = true,
    notes = null,
    priority = Some(TaskPriority.Normal),
    scheduled_count = 0
  )

  val cmp_step_var_var2 = CampaignStepVariantForScheduling(
    id = 290450,
    step_id = 234013,
    campaign_id = 87863,
    template_id = null,
    step_data = AutoEmailStep(
      subject = "are your {{kpi}} increasing?",
      body = "Hi {{first_name}},<br /><br />{{opening_lines}}<br /><br />83% of businesses are unable to maximize their revenues vis-a-vis the resources &amp; efforts they put into generating sales qualified prospects.<br /><br />Is your team struggling with this?  <a href=\"https://calendly.com/smartreachio/fact-finding-discovery-call\" target=\"_blank\" rel=\"noopener\">Let's connect</a><br /><br />SmartReach.io is a multichannel sales outreach tool to help you minimize lead wastage &amp; increase your sales team's efficiency. <br /><br />You can run outreach via Email, Linkedin, WhatsApp, SMS &amp; Calls<br /><br />Cheers<br />Bala<br />Sales Specialist<br />14 Days Free Trial"
    ),
    label = None,
    step_label = Some("Day 1: Opening"),
    step_delay = 86400,
    active = true,
    notes = null,
    priority = Some(TaskPriority.Normal),
    scheduled_count = 0
  )

  val cmp_step_var_var3 = CampaignStepVariantForScheduling(
    id = 290452,
    step_id = 234013,
    campaign_id = 87863,
    template_id = null,
    step_data = AutoEmailStep(
      subject = "focus on {{kpi}}",
      body = "Hi {{first_name}},<br /><br />{{opening_lines}}<br /><br />83% of businesses are unable to maximize their revenues vis-a-vis the resources &amp; efforts they put into generating sales qualified prospects.<br /><br />Is your team struggling with this?  <a href=\"https://calendly.com/smartreachio/fact-finding-discovery-call\" target=\"_blank\" rel=\"noopener\">Let's connect</a><br /><br />SmartReach.io is a multichannel sales outreach tool to help you minimize lead wastage &amp; increase your sales team's efficiency. <br /><br />You can run outreach via Email, Linkedin, WhatsApp, SMS &amp; Calls<br /><br />Cheers<br />Bala<br />Sales Specialist<br />14 Days Free Trial"
    ),
    label = None,
    step_label = Some("Day 1: Opening"),
    step_delay = 86400,
    active = true,
    notes = null,
    priority = Some(TaskPriority.Normal),
    scheduled_count = 0
  )


  val cmp_step_var_var4 = CampaignStepVariantForScheduling(
    id = 290447,
    step_id = 234013,
    campaign_id = 87863,
    template_id = null,
    step_data = AutoEmailStep(
      subject = "achieving {{kpi}} a challenge?",
      body = "Hi {{first_name}},<br /><br />{{opening_lines}}<br /><br />83% of businesses are unable to maximize their revenues vis-a-vis the resources &amp; efforts they put into generating sales qualified prospects.<br /><br />Is your team struggling with this?  <a href=\"https://calendly.com/smartreachio/fact-finding-discovery-call\" target=\"_blank\" rel=\"noopener\">Let's connect</a><br /><br />SmartReach.io is a multichannel sales outreach tool to help you minimize lead wastage &amp; increase your sales team's efficiency. <br /><br />You can run outreach via Email, Linkedin, WhatsApp, SMS &amp; Calls<br /><br />Cheers<br />Bala<br />Sales Specialist<br />14 Days Free Trial"
    ),
    label = None,
    step_label = Some("Day 1: Opening"),
    step_delay = 86400,
    active = true,
    notes = null,
    priority = Some(TaskPriority.Normal),
    scheduled_count = 0
  )

  val cmp_step_var_var5 = CampaignStepVariantForScheduling(
    id = 290449,
    step_id = 234013,
    campaign_id = 87863,
    template_id = null,
    step_data = AutoEmailStep(
      subject = "building a pool of sales qualified leads",
      body = "Hi {{first_name}},<br /><br />{{opening_lines}}<br /><br />83% of businesses are unable to maximize their revenues vis-a-vis the resources &amp; efforts they put into generating sales qualified prospects.<br /><br />Is your team struggling with this?  <a href=\"https://calendly.com/smartreachio/fact-finding-discovery-call\" target=\"_blank\" rel=\"noopener\">Let's connect</a><strong>.</strong><br /><br />SmartReach.io is a multichannel sales outreach tool to help you minimize lead wastage &amp; increase your sales team's efficiency. <br /><br />You can run outreach via Email, Linkedin, WhatsApp, SMS &amp; Calls<br /><br />Cheers<br />Bala<br />Sales Specialist<br />14 Days Free Trial"
    ),
    label = None,
    step_label = Some("Day 1: Opening"),
    step_delay = 86400,
    active = true,
    notes = null,
    priority = Some(TaskPriority.Normal),
    scheduled_count = 0
  )

  val variants: Seq[CampaignStepVariantForScheduling] = Seq(cmp_step_var_var1, cmp_step_var_var2, cmp_step_var_var3, cmp_step_var_var4, cmp_step_var_var5)

  val campaignStepWithChildren_balaji = CampaignStepWithChildren(
    id = 234013,
    label = Some("Day 1: Opening"),
    campaign_id = 87863,
    delay = 86400,
    step_type = CampaignStepType.AutoEmailStep,
    created_at = DateTime.parse("2023-03-28T11:49:22.067508Z"),
    children = List(234014),
    variants = variants
  )

  /*
  record - 2

  id          | 234014
  campaign_id | 87863
  delay       | 86400
  step_type   | linkedin_view_profile
  created_at  | 2023-03-28 11:51:16.000199+00
  label       | Day 2: Follow up 1
  template_id |
  notes       |
  priority    | normal
  children    | [234015]
  variants    | [{"id" : 290448, "step_id" : 234014, "campaign_id" : 87863, "template_id" : null, "step_type" : "linkedin_view_profile", "subject" : "", "body" : "", "text_preview" : "", "scheduled_count" : 0
  , "step_label" : "Day 2: Follow up 1", "step_delay" : 86400, "notes" : "", "priority" : "normal", "active" : true}]

  */

  val cmp_step2_var_var1 = CampaignStepVariantForScheduling(
    id = 290448,
    step_id = 234014,
    campaign_id = 87863,
    template_id = null,
    step_data = CampaignStepData.LinkedinViewProfile(),
    label = None,
    step_label = Some("Day 2: Follow up 1"),
    step_delay = 86400,
    active = true,
    notes = null,
    priority = Some(TaskPriority.Normal),
    scheduled_count = 0
  )

  val cam_step_record_2 = CampaignStepWithChildren(
    id = 234014,
    label = Some("Day 2: Follow up 1"),
    campaign_id = 87863,
    delay = 86400,
    step_type = CampaignStepType.LinkedinViewProfile,
    created_at = DateTime.parse("2023-03-28T11:51:16.000199Z"),
    children = List(234015),
    variants = List(cmp_step2_var_var1)
  )

  /*
  record - 3

  id          | 234015
  campaign_id | 87863
  delay       | 86400
  step_type   | send_linkedin_connection_request
  created_at  | 2023-03-28 11:56:25.860268+00
  label       | Day 3: Follow up 2
  template_id |
  notes       |
  priority    | normal
  children    | [234020]
  variants    | [{"id" : 290453, "step_id" : 234015, "campaign_id" : 87863, "template_id" : null, "step_type" : "send_linkedin_connection_request", "subject" : "", "body" : "Hi {{first_name}}, I’m on a mission to grow my connections on LinkedIn, especially with sales leaders and professionals like you. So even though we’re practically strangers, I’d love to connect.\n", "text_preview" : "",
   "scheduled_count" : 0, "step_label" : "Day 3: Follow up 2", "step_delay" : 86400, "notes" : "", "priority" : "normal", "active" : true}]

  */

  val cmp_step3_var_var1 = CampaignStepVariantForScheduling(
    id = 290448,
    step_id = 234014,
    campaign_id = 87863,
    template_id = null,
    step_data = CampaignStepData.LinkedinConnectionRequestData(
      body = Some("Hi {{first_name}}, I’m on a mission to grow my connections on LinkedIn, especially with sales leaders and professionals like you. So even though we’re practically strangers, I’d love to connect.\n")
    ),
    label = None,
    step_label = Some("Day 3: Follow up 2"),
    step_delay = 86400,
    active = true,
    notes = null,
    priority = Some(TaskPriority.Normal),
    scheduled_count = 0
  )


  val cam_step_record_3 = CampaignStepWithChildren(
    id = 234015,
    label = Some("Day 3: Follow up 2"),
    campaign_id = 87863,
    delay = 86400,
    step_type = CampaignStepType.LinkedinConnectionRequest,
    created_at = DateTime.parse("2023-03-28T11:56:25.860268Z"),
    children = List(234020),
    variants = List(cmp_step3_var_var1)
  )

  /*
  record - 4

  id          | 234020
  campaign_id | 87863
  delay       | 86400
  step_type   | send_email
  created_at  | 2023-03-28 12:14:22.156532+00
  label       | Day 4: Follow up 3
  template_id |
  notes       |
  priority    | normal
  children    | [234146]
  variants    | [{"id" : 290459, "step_id" : 234020, "campaign_id" : 87863, "template_id" : null, "step_type" : "send_email", "subject" : "{{previous_subject}}", "body" :
  "Hi {{first_name}}<br /><br />As a {{job_title}}, I’m sure the one thing that definitely matters is revenue. <br /><br />SmartReach helps businesses like yours generate qualified leads by simply increase your reply rates.<br /><br />In addition to outreach we provide a shared inbox for the outreach team. It's a crazy productivity booster.<br /><br /><a href=\"https://calendly.com/smartreachio/fact-finding-discovery-call\" target=\"_blank\" rel=\"noopener\">Lets connect</a>, if this interests you.<br /><br />Cheers<br />Bala<br />Sales Specialist<br />14 Days Free Trial", "text_preview" : "", "scheduled_count" : 0,
   "step_label" : "Day 4: Follow up 3", "step_delay" : 86400, "notes" : null, "priority" : "normal", "active" : true}]

  */

  val cmp_step4_var_var1 = CampaignStepVariantForScheduling(
    id = 290459,
    step_id = 234020,
    campaign_id = 87863,
    template_id = null,
    step_data = AutoEmailStep(
      subject = "{{previous_subject}}",
      body = "Hi {{first_name}}<br /><br />As a {{job_title}}, I’m sure the one thing that definitely matters is revenue. <br /><br />SmartReach helps businesses like yours generate qualified leads by simply increase your reply rates.<br /><br />In addition to outreach we provide a shared inbox for the outreach team. It's a crazy productivity booster.<br /><br /><a href=\"https://calendly.com/smartreachio/fact-finding-discovery-call\" target=\"_blank\" rel=\"noopener\">Lets connect</a>, if this interests you.<br /><br />Cheers<br />Bala<br />Sales Specialist<br />14 Days Free Trial"
    ),
    label = None,
    step_label = Some("Day 4: Follow up 3"),
    step_delay = 86400,
    active = true,
    notes = null,
    priority = Some(TaskPriority.Normal),
    scheduled_count = 0
  )

  val cam_step_record_4 = CampaignStepWithChildren(
    id = 234020,
    label = Some("Day 4: Follow up 3"),
    campaign_id = 87863,
    delay = 86400,
    step_type = CampaignStepType.AutoEmailStep,
    created_at = DateTime.parse("2023-03-28T12:14:22.156532Z"),
    children = List(234146),
    variants = List(cmp_step4_var_var1)
  )

  /*
  record - 4

  id          | 234020
  campaign_id | 87863
  delay       | 86400
  step_type   | send_email
  created_at  | 2023-03-28 12:14:22.156532+00
  label       | Day 4: Follow up 3
  template_id |
  notes       |
  priority    | normal
  children    | [234146]
  variants    | [{"id" : 290459, "step_id" : 234020, "campaign_id" : 87863, "template_id" : null, "step_type" : "send_email", "subject" : "{{previous_subject}}", "body" : "Hi {{first_name}}<br /><br />As a {{job_title}}, I’m sure the one thing that definitely matters is revenue. <br /><br />SmartReach helps businesses like yours generate qualified leads by simply increase your reply rates.<br /><br />In addition to outreach we provide a shared inbox for the outreach team. It's a crazy productivity booster.<br /><br /><a href=\"https://calendly.com/smartreachio/fact-finding-discovery-call\" target=\"_blank\" rel=\"noopener\">Lets connect</a>, if this interests you.<br /><br />Cheers<br />Bala<br />Sales Specialist<br />14 Days Free Trial", "text_preview" : "", "scheduled_count" : 0, "step_label" : "Day 4: Follow up 3", "step_delay" : 86400, "notes" : null, "priority" : "normal", "active" : true}]


  record - 5

  id          | 234146
  campaign_id | 87863
  delay       | 172800
  step_type   | send_email
  created_at  | 2023-03-28 14:11:18.924922+00
  label       | Day 6: Follow up 4
  template_id |
  notes       |
  priority    | normal
  children    | [234702]
  variants    | [{"id" : 290628, "step_id" : 234146, "campaign_id" : 87863, "template_id" : null, "step_type" : "send_email",
   "subject" : "{{previous_subject}}",
    "body" : "{{first_name}} I assume you are the key personnel responsible for customer acquisition from your sales channel. <br /><br /> <img src=\"https://sr-email-message-images.s3.amazonaws.com/12178_1680095583923_IowtvxOXoQlWX29b1mSGPvrRZF5g0jhL_cmpn_\" alt=\"\" width=\"329\" height=\"219\" /><br /><br />Don’t mean to bother if you’re not. Could you refer me to the right person? Thanks in advance!<br /><br />Regards<br />Bala",
     "text_preview" : "", "scheduled_count" : 0, "step_label" : "Day 6: Follow up 4", "step_delay" : 172800, "notes" : null, "priority" : "normal", "active" : true}]

*/


  val cmp_step5_var_var1 = CampaignStepVariantForScheduling(
    id = 290628,
    step_id = 234146,
    campaign_id = 87863,
    template_id = null,
    step_data = AutoEmailStep(
      subject = "{{previous_subject}}",
      body = "{{first_name}} I assume you are the key personnel responsible for customer acquisition from your sales channel. <br /><br /> <img src=\"https://sr-email-message-images.s3.amazonaws.com/12178_1680095583923_IowtvxOXoQlWX29b1mSGPvrRZF5g0jhL_cmpn_\" alt=\"\" width=\"329\" height=\"219\" /><br /><br />Don’t mean to bother if you’re not. Could you refer me to the right person? Thanks in advance!<br /><br />Regards<br />Bala"
    ),
    label = None,
    step_label = Some("Day 4: Follow up 3"),
    step_delay = 86400,
    active = true,
    notes = null,
    priority = Some(TaskPriority.Normal),
    scheduled_count = 0
  )

  val cam_step_record_5 = CampaignStepWithChildren(
    id = 234146,
    label = Some("Day 6: Follow up 4"),
    campaign_id = 87863,
    delay = 172800,
    step_type = CampaignStepType.AutoEmailStep,
    created_at = DateTime.parse("2023-03-28T14:11:18.924922Z"),
    children = List(234702),
    variants = List(cmp_step5_var_var1)
  )



  /*


record - 6

id          | 234702
campaign_id | 87863
delay       | 86400
step_type   | send_linkedin_message
created_at  | 2023-03-29 14:20:54.205749+00
label       | Day 7: Follow up 5
template_id |
notes       |
priority    | normal
children    | [234707]
variants    | [{"id" : 291376, "step_id" : 234702, "campaign_id" : 87863, "template_id" : null, "step_type" : "send_linkedin_message", "subject" : "",
 "body" : "Trust me, I know you might not have an easy day as the {{job_title}}, and your schedule might be as busy as it gets, but how about a quick 10 minute call next week? \n\nAgenda: Understand your current sales process\n\nLater, I shall connect with your team and walk them through the SmartReach.io features that address the current concerns. \n\nThis is my calendar (Message me for a slot not available on my calendar)\n\nhttps://calendly.com/smartreachio/fact-finding-discovery-call\n\nMy bad, SmartReach is a sales engagement software for multichannel outreach designed to increase sales via automation & team collaboration"
 , "text_preview" : "", "scheduled_count" : 0, "step_label" : "Day 7: Follow up 5", "step_delay" : 86400, "notes" : "", "priority" : "normal", "active" : true}]
*/

  val cmp_step6_var_var1 = CampaignStepVariantForScheduling(
    id = 291376,
    step_id = 234702,
    campaign_id = 87863,
    template_id = null,
    step_data = CampaignStepData.LinkedinMessageData(
      body = "Trust me, I know you might not have an easy day as the {{job_title}}, and your schedule might be as busy as it gets, but how about a quick 10 minute call next week? \n\nAgenda: Understand your current sales process\n\nLater, I shall connect with your team and walk them through the SmartReach.io features that address the current concerns. \n\nThis is my calendar (Message me for a slot not available on my calendar)\n\nhttps://calendly.com/smartreachio/fact-finding-discovery-call\n\nMy bad, SmartReach is a sales engagement software for multichannel outreach designed to increase sales via automation & team collaboration"
    ),
    label = None,
    step_label = Some("Day 7: Follow up 5"),
    step_delay = 86400,
    active = true,
    notes = null,
    priority = Some(TaskPriority.Normal),
    scheduled_count = 0
  )

  val cam_step_record_6 = CampaignStepWithChildren(
    id = 234702,
    label = Some("Day 7: Follow up 5"),
    campaign_id = 87863,
    delay = 86400,
    step_type = CampaignStepType.LinkedinMessage,
    created_at = DateTime.parse("2023-03-29T14:20:54.205749Z"),
    children = List(234707),
    variants = List(cmp_step6_var_var1)
  )

  /*

  record - 7

  id          | 234707
  campaign_id | 87863
  delay       | 172800
  step_type   | send_email
  created_at  | 2023-03-29 14:22:34.66118+00
  label       | Day 9: Follow up 6
  template_id |
  notes       |
  priority    | normal
  children    | []
  variants    | [{"id" : 291384, "step_id" : 234707, "campaign_id" : 87863, "template_id" : null, "step_type" : "send_email", "subject" : "{{previous_subject}}", "body" : "Bumping this up in you inbox<br /><br />{{first_name}}, give us a chance. Your team will be hitting their sales quotas in no time<br /><br />But let me know if:<br /><br />1. You’re not interested<br /><br />2. Switching your outreach platform is not a priority and we should connect later.<br /><br />3. Im targeting the wrong personnel<br /><br />4. Your dog threatens you every time you try to reply. Just joking!<br /><br />Regards<br />Bala", "text_preview" : "", "scheduled_count" : 0, "step_label" : "Day 9: Follow up 6", "step_delay" : 172800, "notes" : null, "priority" : "normal", "active" : true}]

  */

  val cmp_step7_var_var1 = CampaignStepVariantForScheduling(
    id = 291376,
    step_id = 234702,
    campaign_id = 87863,
    template_id = null,
    step_data = CampaignStepData.LinkedinMessageData(
      body = "Trust me, I know you might not have an easy day as the {{job_title}}, and your schedule might be as busy as it gets, but how about a quick 10 minute call next week? \n\nAgenda: Understand your current sales process\n\nLater, I shall connect with your team and walk them through the SmartReach.io features that address the current concerns. \n\nThis is my calendar (Message me for a slot not available on my calendar)\n\nhttps://calendly.com/smartreachio/fact-finding-discovery-call\n\nMy bad, SmartReach is a sales engagement software for multichannel outreach designed to increase sales via automation & team collaboration"
    ),
    label = None,
    step_label = Some("Day 7: Follow up 5"),
    step_delay = 86400,
    active = true,
    notes = null,
    priority = Some(TaskPriority.Normal),
    scheduled_count = 0
  )

  val cam_step_record_7 = CampaignStepWithChildren(
    id = 234707,
    label = Some("Day 9: Follow up 6"),
    campaign_id = 87863,
    delay = 172800,
    step_type = CampaignStepType.AutoEmailStep,
    created_at = DateTime.parse("2023-03-29T14:22:34.66118Z"),
    children = List(),
    variants = List(cmp_step7_var_var1)
  )

  val steps_returned_from_db = Seq(
    campaignStepWithChildren,
    cam_step_record_2,
    cam_step_record_3,
    cam_step_record_4,
    cam_step_record_5,
    cam_step_record_6,
    cam_step_record_7
  )

  val stepsMappedById_shuffled = Map(
    234014L -> cam_step_record_2,
    234013L -> campaignStepWithChildren_balaji,
    234707L -> cam_step_record_7,
    234702L -> cam_step_record_6,
    234020L -> cam_step_record_4,
    234146L -> cam_step_record_5,
    234015L -> cam_step_record_3,
  )

  val ordered_step_ids = Vector(234013L, 234014L, 234015L, 234020L, 234146L, 234702L, 234707L)

  "getMapOfStepIdAndRequiredDelay" should "should return first step along with all steps" in {

    //    val res2 = CampaignStepDAO.getOrderedSteps(
    //      steps = Seq(
    //        campaignStepWithChildren.copy(id = 5, children = List()),
    //        campaignStepWithChildren.copy(id = 2, children = List(3, 5)),
    //        campaignStepWithChildrenLinkedin.copy(id = 3, children = List(5))
    //      ),
    //      headStepId = 2
    //    )

    val result = ChannelSchedulerService.getMapOfStepIdAndRequiredDelay(
      headStepId = 234013,
      orderedStepIds = ordered_step_ids,
      stepsMappedById = stepsMappedById_shuffled
    )

    /*

    --------+-------------+--------+----------------------------------+-------------------------------+--------------------
     234013 |       87863 |  86400 | send_email                       | 2023-03-28 11:49:22.067508+00 | Day 1: Opening
     234014 |       87863 |  86400 | linkedin_view_profile            | 2023-03-28 11:51:16.000199+00 | Day 2: Follow up 1
     234015 |       87863 |  86400 | send_linkedin_connection_request | 2023-03-28 11:56:25.860268+00 | Day 3: Follow up 2
     234020 |       87863 |  86400 | send_email                       | 2023-03-28 12:14:22.156532+00 | Day 4: Follow up 3
     234146 |       87863 | 172800 | send_email                       | 2023-03-28 14:11:18.924922+00 | Day 6: Follow up 4
     234707 |       87863 | 172800 | send_email                       | 2023-03-29 14:22:34.66118+00  | Day 9: Follow up 6
     234702 |       87863 |  86400 | send_linkedin_message            | 2023-03-29 14:20:54.205749+00 | Day 7: Follow up 5

    */

    println(s"result -> $result")
    Logger.info(s"result -- $result")
    assert(result == Vector(
      SchedulerMapStepIdAndDelay(
        is_head_step_in_the_campaign = true,
        currentStepType = api.campaigns.models.CampaignStepType.AutoEmailStep,
        nextStepType = api.campaigns.models.CampaignStepType.LinkedinViewProfile,
        currentStepId = 234013,
        delayTillNextStep = 86400
      ),
      SchedulerMapStepIdAndDelay(
        is_head_step_in_the_campaign = false,
        currentStepType = api.campaigns.models.CampaignStepType.LinkedinViewProfile,
        nextStepType = api.campaigns.models.CampaignStepType.LinkedinConnectionRequest,
        currentStepId = 234014,
        delayTillNextStep = 86400
      ),
      SchedulerMapStepIdAndDelay(
        is_head_step_in_the_campaign = false,
        currentStepType = api.campaigns.models.CampaignStepType.LinkedinConnectionRequest,
        nextStepType = api.campaigns.models.CampaignStepType.AutoEmailStep,
        currentStepId = 234015,
        delayTillNextStep = 86400
      ),
      SchedulerMapStepIdAndDelay(
        is_head_step_in_the_campaign = false,
        currentStepType = api.campaigns.models.CampaignStepType.AutoEmailStep,
        nextStepType = api.campaigns.models.CampaignStepType.AutoEmailStep,
        currentStepId = 234020,
        delayTillNextStep = 172800
      ),
      SchedulerMapStepIdAndDelay(
        is_head_step_in_the_campaign = false,
        currentStepType = api.campaigns.models.CampaignStepType.AutoEmailStep,
        nextStepType = api.campaigns.models.CampaignStepType.LinkedinMessage,
        currentStepId = 234146,
        delayTillNextStep = 86400
      ),

      SchedulerMapStepIdAndDelay(
        is_head_step_in_the_campaign = false,
        currentStepType = api.campaigns.models.CampaignStepType.LinkedinMessage,
        nextStepType = api.campaigns.models.CampaignStepType.AutoEmailStep,
        currentStepId = 234702,
        delayTillNextStep = 172800
      )

      //      SchedulerMapStepIdAndDelay(
      //        is_head_step_in_the_campaign = false,
      //        currentStepType = api.campaigns.models.CampaignStepType.LinkedinMessage,
      //        nextStepType = api.campaigns.models.CampaignStepType.AutoEmailStep,
      //        currentStepId = 234702,
      //        delayTillNextStep = 86400
      //      ),


      //      ,
      //      SchedulerMapStepIdAndDelay(
      //        is_head_step_in_the_campaign = false,
      //        currentStepType = api.campaigns.models.CampaignStepType.AutoEmailStep,
      //        nextStepType = api.campaigns.models.CampaignStepType.,
      //        currentStepId = 3,
      //        delayTillNextStep = 10
      //      )
    ))
  }

  val schedulerMapStepIdAndDelay_1 = SchedulerMapStepIdAndDelay(
    is_head_step_in_the_campaign = true,
    currentStepType = api.campaigns.models.CampaignStepType.AutoEmailStep,
    nextStepType    = api.campaigns.models.CampaignStepType.AutoEmailStep,
    currentStepId = 1L,
    delayTillNextStep = 10
  )
  val schedulerMapStepIdAndDelay_2 =SchedulerMapStepIdAndDelay(
    is_head_step_in_the_campaign = false,
    currentStepType = api.campaigns.models.CampaignStepType.AutoEmailStep,
    nextStepType    = api.campaigns.models.CampaignStepType.AutoEmailStep,
    currentStepId = 2L,
    delayTillNextStep = 10
  )

  val schedulerMapStepIdAndDelay_3 = SchedulerMapStepIdAndDelay(
    is_head_step_in_the_campaign = false,
    currentStepType = api.campaigns.models.CampaignStepType.AutoEmailStep,
    nextStepType    = api.campaigns.models.CampaignStepType.AutoEmailStep,
    currentStepId = 3L,
    delayTillNextStep = 10
  )

  "getProspectsToSendEmailsTo send an empty list without warmup" should "send back an empty list" in {


    //    (campaignProspectDAO.fetchProspectsV2_PEV2(_: DateTime, _: Long, _: Long, _: Int, _: Int, _: Int, _: Vector[SchedulerMapStepIdAndDelay], _: Boolean, _: Option[DateTime])(_:SRLogger))
    //    .expects(DateTime.parse("2022-03-17T17:33:22.622+05:30"), 1, 2, 0, 100, 1000, Vector(
    //      schedulerMapStepIdAndDelay_1 ,
    //      schedulerMapStepIdAndDelay_2,
    //      schedulerMapStepIdAndDelay_3
    //    ), false, None, Logger)
    //      .returning(Success(List()))
    //
    //    (campaignProspectDAO.fetchProspectsV2_PEV2(_: DateTime, _: Long, _: Long, _: Int, _: Int, _: Int, _: Vector[SchedulerMapStepIdAndDelay], _: Boolean, _: Option[DateTime])(_:SRLogger))
    //      .expects(DateTime.parse("2022-03-17T17:33:22.622+05:30"), 1, 2, 0, 100, 1000, Vector(
    //        schedulerMapStepIdAndDelay_1,
    //        schedulerMapStepIdAndDelay_2,
    //        schedulerMapStepIdAndDelay_3
    //      ), true, None, Logger)
    //      .returning(Success(List()))

    val campaignDailyQuota: Int = campaignForScheduling.campaign_max_emails_per_day


    val channelDailyTaskLimit = 100
    val channelOrCampaignMinDailyQuota: Int = Math.min(channelDailyTaskLimit, campaignDailyQuota)

    val dailyQuota = emailChannelScheduler.getDailyQuotaConsideringWarmup(
      warmupSettingOpt = campaignForScheduling.softstart_setting,
      channelOrCampaignMinDailyQuota = channelOrCampaignMinDailyQuota
    )

    val totalScheduledFromChannelTillNow = 100

    val totalTasksDoneOrScheduledFromCampaignToday = emailChannelScheduler
      .countTotalTasksDoneOrScheduledFromCampaignToday(
        channelScheduledProspectsCountForCampaign = emailChannelScheduledProspectsCountForCampaign,
        campaignStepType = CampaignStepType.AutoEmailStep
      )

    val remainingToBeSentCountForStepType = emailChannelScheduler.getRemainingToBeSentCountForTheDay(

      remainingToBeSentCountFromCampaign = dailyQuota - totalTasksDoneOrScheduledFromCampaignToday.firstSteps,
      remainingToBeSentCountForChannelStepType = channelDailyTaskLimit - totalScheduledFromChannelTillNow,

      Logger = Logger
    )

    val maxToBeScheduledForNextHour: Int = 50
    val fetchLimit = if (List(remainingToBeSentCountForStepType, maxToBeScheduledForNextHour).min < 0) 0 else List(remainingToBeSentCountForStepType, maxToBeScheduledForNextHour).min

    (campaignProspectService.getCampaignsToLogDripFor()(using _: SRLogger))
      .expects(*)
      .returning(List())
    val result = emailChannelScheduler.getProspectsToCreateTasks(
      schedulingForStepType = CampaignStepType.AutoEmailStep,
      fetchLimitForFirstStep = fetchLimit,
      fetchLimitForFollowup = fetchLimit,
      scheduleFromTime = DateTime.parse("2022-03-17T17:33:22.622+05:30"),
      campaign = campaignForScheduling,
      fetchCampaignStepsDataResult =  FetchCampaignStepsData(
        stepsMappedById = stepsMappedById,
        campaignStepsStructure = CampaignStepsStructure.MultichannelCampaignStepsStructure(
          orderedStepIds = Vector(1L, 2L, 3L, 4L)
        ),
        allCampaignSteps = Vector[SchedulerMapStepIdAndDelay](

          schedulerMapStepIdAndDelay_1,
          schedulerMapStepIdAndDelay_2,
          schedulerMapStepIdAndDelay_3
        ),
        relevantCampaignStepsForChannel = Vector[SchedulerMapStepIdAndDelay](

          schedulerMapStepIdAndDelay_1,
          schedulerMapStepIdAndDelay_2,
          schedulerMapStepIdAndDelay_3
        )
      ),
      allowedTimezones = Success(Set("Asia/Kolkata")),



      sendOnlyToProspectsWhoWereSentInCurrentCycle = None,
//      srRollingUpdateCoreService = srRollingUpdateCoreService,

      campaignProspectDAO = campaignProspectDAO,
      campaignProspectService = campaignProspectService,
      channelType = CampaignStepType.AutoEmailStep.channelType,
      isThisChannelFirstStep = true,
      org_id = OrgId(org.id),
      srRollingUpdateCoreService = srRollingUpdateCoreService
    )
    Logger.info(s"result ---- $result")
    assert(result == Success(emailChannelScheduler.ProspectsToCreateTasksResult(List(), List())))

  }

  "getProspectsToSendEmailsTo with warmuptime" should "send an empty list" in {

    //    (campaignProspectDAO.fetchProspectsV2_PEV2(_: DateTime, _: Long, _: Long, _: Int, _: Int, _: Int, _: Vector[SchedulerMapStepIdAndDelay], _: Boolean, _: Option[DateTime])(_:SRLogger))
    //      .expects(DateTime.parse("2022-03-17T17:33:22.622+05:30"), 1, 2, 0, 100, 1000, Vector(
    //        schedulerMapStepIdAndDelay_1,
    //        schedulerMapStepIdAndDelay_2,
    //        schedulerMapStepIdAndDelay_3
    //      ), false, None, Logger)
    //      .returning(Success(List()))
    //
    //    (campaignProspectDAO.fetchProspectsV2_PEV2(_: DateTime, _: Long, _: Long, _: Int, _: Int, _: Int, _: Vector[SchedulerMapStepIdAndDelay], _: Boolean, _: Option[DateTime])(_:SRLogger))
    //      .expects(DateTime.parse("2022-03-17T17:33:22.622+05:30"), 1, 2, 0, 100, 1000, Vector(
    //        schedulerMapStepIdAndDelay_1,
    //        schedulerMapStepIdAndDelay_2,
    //        schedulerMapStepIdAndDelay_3
    //      ), true, None, Logger)
    //      .returning(Success(List()))

    val campaignDailyQuota: Int = campaignForScheduling.campaign_max_emails_per_day


    val channelDailyTaskLimit = 100
    val channelOrCampaignMinDailyQuota: Int = Math.min(channelDailyTaskLimit, campaignDailyQuota)

    val dailyQuota = emailChannelScheduler.getDailyQuotaConsideringWarmup(
      warmupSettingOpt = campaignForScheduling.softstart_setting,
      channelOrCampaignMinDailyQuota = channelOrCampaignMinDailyQuota
    )


    val totalTasksDoneOrScheduledFromCampaignToday = emailChannelScheduler
      .countTotalTasksDoneOrScheduledFromCampaignToday(
        channelScheduledProspectsCountForCampaign = emailChannelScheduledProspectsCountForCampaign,
        campaignStepType = CampaignStepType.AutoEmailStep
      )


    val totalScheduledFromChannelTillNow = 100
    val remainingToBeSentCount = emailChannelScheduler.getRemainingToBeSentCountForTheDay(

      remainingToBeSentCountFromCampaign = dailyQuota - totalTasksDoneOrScheduledFromCampaignToday.firstSteps,
      remainingToBeSentCountForChannelStepType = channelDailyTaskLimit - totalScheduledFromChannelTillNow,

      Logger = Logger
    )


    val remainingToBeSentCountForStepType = emailChannelScheduler.getRemainingToBeSentCountForTheDay(

      remainingToBeSentCountFromCampaign = dailyQuota - totalTasksDoneOrScheduledFromCampaignToday.firstSteps,
      remainingToBeSentCountForChannelStepType = channelDailyTaskLimit - totalScheduledFromChannelTillNow,

      Logger = Logger
    )
    val maxToBeScheduledForNextHour: Int = 50
    val fetchLimit = if (List(remainingToBeSentCountForStepType, maxToBeScheduledForNextHour).min < 0) 0 else List(remainingToBeSentCountForStepType, maxToBeScheduledForNextHour).min
    (campaignProspectService.getCampaignsToLogDripFor()(using _: SRLogger))
      .expects(*)
      .returning(List())

    val result = emailChannelScheduler.getProspectsToCreateTasks(
      schedulingForStepType = CampaignStepType.AutoEmailStep,
      fetchLimitForFirstStep = fetchLimit,
      fetchLimitForFollowup = fetchLimit,
      scheduleFromTime = DateTime.parse("2022-03-17T17:33:22.622+05:30"),
      campaign = campaignForScheduling.copy(softstart_setting = Some(
        CampaignWarmupSetting(
          warmup_started_at = DateTime.parse("2022-03-17T17:33:22.622+05:33"),
          warmup_starting_email_count = 5,
          warmup_length_in_days = 20
        )
      )),
      /*allCampaignSteps = Vector[SchedulerMapStepIdAndDelay](
        SchedulerMapStepIdAndDelay(

          is_head_step_in_the_campaign = true,
          currentStepType = api.campaigns.models.CampaignStepType.AutoEmailStep,
          nextStepType    = api.campaigns.models.CampaignStepType.AutoEmailStep,
          stepId = 1,
          delay = 10
        ),
        SchedulerMapStepIdAndDelay(

          is_head_step_in_the_campaign = false,
          currentStepType = api.campaigns.models.CampaignStepType.AutoEmailStep,
          nextStepType    = api.campaigns.models.CampaignStepType.AutoEmailStep,
          stepId = 2,
          delay = 10
        ),
        SchedulerMapStepIdAndDelay(
          is_head_step_in_the_campaign = false,
          currentStepType = api.campaigns.models.CampaignStepType.AutoEmailStep,
          nextStepType    = api.campaigns.models.CampaignStepType.AutoEmailStep,
          stepId = 3,
          delay = 10
        )
      ),*/

      fetchCampaignStepsDataResult =  FetchCampaignStepsData(
        stepsMappedById = stepsMappedById,
        campaignStepsStructure = CampaignStepsStructure.MultichannelCampaignStepsStructure(
          orderedStepIds = Vector(1L, 2L, 3L, 4L)
        ),
        allCampaignSteps = Vector[SchedulerMapStepIdAndDelay](

          schedulerMapStepIdAndDelay_1,
          schedulerMapStepIdAndDelay_2,
          schedulerMapStepIdAndDelay_3
        ),
        relevantCampaignStepsForChannel = Vector[SchedulerMapStepIdAndDelay](

          schedulerMapStepIdAndDelay_1,
          schedulerMapStepIdAndDelay_2,
          schedulerMapStepIdAndDelay_3
        )
      ),
      allowedTimezones = Success(Set("Asia/Kolkata")),

      sendOnlyToProspectsWhoWereSentInCurrentCycle = None,

      campaignProspectDAO = campaignProspectDAO,
      campaignProspectService = campaignProspectService,
      channelType = CampaignStepType.AutoEmailStep.channelType,
//      srRollingUpdateCoreService = srRollingUpdateCoreService,
      isThisChannelFirstStep = true,
      org_id = OrgId(org.id),
      srRollingUpdateCoreService = srRollingUpdateCoreService
    )
    Logger.info(s"result ---- $result")
    assert(result == Success(emailChannelScheduler.ProspectsToCreateTasksResult(List(), List())))

  }

  val channel_id_1: Long = 1
  val account_id_1: Long = 1
  val emailSettingId_1: Long = 1

  "scheduleEmailAccount" should "find Nothing to schedule" in {

    (srRedisSimpleLockServiceV2.checkLock(
      _: CacheIdKeyForLock
    )(
      using _: SRLogger
    ))
      .expects(*, *)
      .returning(Success(false))

    (emailSettingDAO.findForScheduling)
      .expects(channel_id_1, Logger)
      .returning(None)

    emailChannelScheduler.scheduleTasksForChannel(
      channelData = ChannelData.EmailChannelData(emailSettingId = emailSettingId_1),
      teamId = 3L,
      accountService = accountService,
      //accountDAO = accountDAO,
      emailNotificationService = emailNotificationService,
      campaignService = campaignService,
      campaignProspectDAO = campaignProspectDAO,
      campaignStepVariantDAO = campaignStepVariantDAO,
      campaignStepDAO = campaignStepDAO,
      emailServiceCompanion = emailServiceCompanion,
      templateService = templateService,
      taskDAO = taskDAO,
      srShuffleUtils = srShuffleUtils,
      campaignProspectService = campaignProspectService,
      taskService = taskService,
      campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO,
      campaignsMissingMergeTagService = campaignsMissingMergeTagService,
      srRedisSimpleLockServiceV2 = srRedisSimpleLockServiceV2,
      accountOrgBillingRelatedService = accountOrgBillingRelatedService,
      mqWebhookCompleted = mqWebhookCompleted,
        srRollingUpdateCoreService = srRollingUpdateCoreService,
      calendarAppService = calendarAppService

    ).map{ result =>

      assert(result == ScheduleTasksData(
        saved_tasks_count = 0,
        latest_task_scheduled_at = None,
        reached_scheduler_step = SchedulerSteps.InitialRedisLock
      ))

    }.recover{e =>
      assert(e.getMessage == "")
    }

  }

  "scheduleEmailAccount" should "should fail because accountService.find fail" in {

    (srRedisSimpleLockServiceV2.checkLock(
      _: CacheIdKeyForLock
    )(
      using _: SRLogger
    ))
      .expects(*, *)
      .returning(Success(false))

    (emailSettingDAO.findForScheduling)
      .expects(channel_id_1, Logger)
      .returning(Some(emailSettingCreateEmailSchedule))


    (campaignService.findCampaignsForSchedulingEA)
      .expects(emailSettingId_1, Logger, TeamId(emailSettingCreateEmailSchedule.team_id))
      .returning(Success(Seq(campaignForScheduling)))


    (accountService.find(_: Long)(_: SRLogger))
      .expects(account_id_1, *)
      .returning(Failure(Error))

    (emailSchedulerJedisService.acquireLockAndAddToSetForCampaignScheduling(_: Set[CampaignId], _: Int)(using _: SRLogger))
      .expects(Set(CampaignId(1)), 60 * 30, *)
      .returning(Success(Set(CampaignId(1))))
    (campaignProspectService.getAllDistinctTimezones(
      _: Long,
      _: Long,
      _: String
    )(
      using _: SRLogger
    ))
      .expects(1, 2, "Asia/Kolkata", *)
      .returning(Success(Set("Asia/Kolkata")))
    emailChannelScheduler.scheduleTasksForChannel(
      channelData = ChannelData.EmailChannelData(emailSettingId = emailSettingId_1),
      teamId = 3L,
      accountService = accountService,
      //accountDAO = accountDAO,
      emailNotificationService = emailNotificationService,
      campaignService = campaignService,
      campaignProspectDAO = campaignProspectDAO,
      campaignStepVariantDAO = campaignStepVariantDAO,
      campaignStepDAO = campaignStepDAO,
      emailServiceCompanion = emailServiceCompanion,
      templateService = templateService,
      taskDAO = taskDAO,
      srShuffleUtils = srShuffleUtils,
      campaignProspectService = campaignProspectService,
      taskService = taskService,
      campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO,
      campaignsMissingMergeTagService = campaignsMissingMergeTagService,
      srRedisSimpleLockServiceV2 = srRedisSimpleLockServiceV2,
      accountOrgBillingRelatedService = accountOrgBillingRelatedService,
      mqWebhookCompleted = mqWebhookCompleted,
        srRollingUpdateCoreService = srRollingUpdateCoreService,
      calendarAppService = calendarAppService
    ).map{ result =>

      Logger.info(s"result - $result")

      assert( false)
    }.recover{
      case e => Logger.info(s"error - $e")
        assert(e == Error)

    }


  }

  "scheduleEmailAccount" should "should fail because campaignDAO.findCampaignsForSchedulingEA fail" in {
    val emailSetting = emailSettingCreateEmailSchedule

    (srRedisSimpleLockServiceV2.checkLock(
      _: CacheIdKeyForLock
    )(
      using _: SRLogger
    ))
      .expects(*, *)
      .returning(Success(false))

    (emailSettingDAO.findForScheduling)
      .expects(channel_id_1, Logger)
      .returning(Some(emailSetting))

    /*
    (accountService.find(_: Long)(_: SRLogger))
      .expects(account_id_1, *)
      .returning(Success(accountAdmin))
    */

    (campaignService.findCampaignsForSchedulingEA)
      .expects(emailSettingId_1, Logger, TeamId(emailSetting.team_id))
      .returning(Failure(Error))


    emailChannelScheduler.scheduleTasksForChannel(
      channelData = ChannelData.EmailChannelData(emailSettingId = emailSettingId_1),
      teamId = 3L,
      accountService = accountService,
      //accountDAO = accountDAO,
      emailNotificationService = emailNotificationService,
      campaignService = campaignService,
      campaignProspectDAO = campaignProspectDAO,
      campaignStepVariantDAO = campaignStepVariantDAO,
      campaignStepDAO = campaignStepDAO,
      emailServiceCompanion = emailServiceCompanion,
      templateService = templateService,
      taskDAO = taskDAO,
      srShuffleUtils = srShuffleUtils,
      campaignProspectService = campaignProspectService,
      taskService = taskService,
      campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO,
      campaignsMissingMergeTagService = campaignsMissingMergeTagService,
      srRedisSimpleLockServiceV2 = srRedisSimpleLockServiceV2,
      mqWebhookCompleted = mqWebhookCompleted,
      accountOrgBillingRelatedService = accountOrgBillingRelatedService,
        srRollingUpdateCoreService = srRollingUpdateCoreService,
      calendarAppService = calendarAppService

    ).map { result =>
      Logger.info(s"result - $result")

      assert( false)
    }.recover{
      case e => Logger.info(s"error - $e :::: ${e.getMessage}")
        assert(e == Error)
        assert(e.getMessage != null)
    }


  }

  /**
   * 28-feb-2024: what the test case description says is not same as what it really is testing inside
   *
   * if `findCampaignsForSchedulingEA` returns a empty sequence, then obviously the scheduler doesnt scheduler any tasks
   * but thats not what the description says
   *
   */
  "scheduleEmailAccount" should " should not be possible current_cycle_started_at is after new_prospects_paused_til" in {

    val account = accountAdmin.copy(
      org = accountAdmin.org.copy(
        plan = accountAdmin.org.plan.copy(
          new_prospects_paused_till = Some(DateTime.now().plusHours(5)),
          current_cycle_started_at = DateTime.now().plusHours(6)
        )
      )
    )

    (emailSchedulerJedisService.acquireLockAndAddToSetForCampaignScheduling(_: Set[CampaignId], _: Int)(using _: SRLogger))
      .expects(*, 60 * 30, *)
      .returning(Success(Set()))
    (srRedisSimpleLockServiceV2.checkLock(
      _: CacheIdKeyForLock
    )(
      using _: SRLogger
    ))
      .expects(*, *)
      .returning(Success(false))

    (emailSettingDAO.findForScheduling)
      .expects(channel_id_1, Logger)
      .returning(Some(emailSettingCreateEmailSchedule))

    /*
    (accountService.find(_: Long)(_: SRLogger))
      .expects(account_id_1, *)
      .returning(Success(account))
    */

    (campaignService.findCampaignsForSchedulingEA)
      .expects(emailSettingId_1, Logger, TeamId(emailSettingCreateEmailSchedule.team_id))
      .returning(Success(Seq()))

    (emailSettingDAO._updateLastScheduled)
      .expects(1, None, Logger)
      .returning(1)


    emailChannelScheduler.scheduleTasksForChannel(
      channelData = ChannelData.EmailChannelData(emailSettingId = emailSettingId_1),
      teamId = 3L,
      accountService = accountService,
      //accountDAO = accountDAO,
      emailNotificationService = emailNotificationService,
      campaignService = campaignService,
      campaignProspectDAO = campaignProspectDAO,
      campaignStepVariantDAO = campaignStepVariantDAO,
      campaignStepDAO = campaignStepDAO,
      emailServiceCompanion = emailServiceCompanion,
      templateService = templateService,
      taskDAO = taskDAO,
      srShuffleUtils = srShuffleUtils,
      campaignProspectService = campaignProspectService,
      taskService = taskService,
      campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO,
      campaignsMissingMergeTagService = campaignsMissingMergeTagService,
      srRedisSimpleLockServiceV2 = srRedisSimpleLockServiceV2,
      accountOrgBillingRelatedService = accountOrgBillingRelatedService,
      mqWebhookCompleted = mqWebhookCompleted,
        srRollingUpdateCoreService = srRollingUpdateCoreService,
      calendarAppService = calendarAppService

    ).map { result =>
      Logger.info(s"result - $result")

      assert(result == ScheduleTasksData(
        saved_tasks_count = 0,
        latest_task_scheduled_at = None,
        reached_scheduler_step = SchedulerSteps.NoCampaignsFoundAfterLock
      ))
    }.recover {
      case e => Logger.info(s"error - $e :::: ${e.getMessage}")
        assert(e.getMessage == "")
    }


  }

  "scheduleEmailAccount" should "failed because of getSentOrScheduledProspectsCountForEmail" in {

    (srRedisSimpleLockServiceV2.checkLock(
      _: CacheIdKeyForLock
    )(
      using _: SRLogger
    ))
      .expects(*, *)
      .returning(Success(false))

    (emailSettingDAO.findForScheduling)
      .expects(channel_id_1, Logger)
      .returning(Some(emailSettingCreateEmailSchedule))
    (emailSchedulerJedisService.acquireLockAndAddToSetForCampaignScheduling(_: Set[CampaignId], _: Int)(using _: SRLogger))
      .expects(Set(CampaignId(1)), 60 * 30, *)
      .returning(Success(Set(CampaignId(1))))

    (emailSchedulerJedisService.releaseLockForCampaignScheduling(_: Set[CampaignId])(using _: SRLogger))
      .expects(Set(CampaignId(1)), *)
      .returning(Success(Map(true -> Set(CampaignId(1)))))
    (accountService.find(_: Long)(_: SRLogger))
      .expects(account_id_1, *)
      .returning(Success(accountAdmin))

    (campaignService.findCampaignsForSchedulingEA)
      .expects(emailSettingId_1, Logger, TeamId(emailSettingCreateEmailSchedule.team_id))
      .returning(Success(Seq(campaignForScheduling)))

    (campaignProspectDAO.getSentOrScheduledProspectsCountForEmail)
      .expects(Seq(EmailSettingId(1)),"Asia/Kolkata", *, Logger, TeamId(emailSettingCreateEmailSchedule.team_id))
      .returning(Failure(Error))
    (campaignProspectService.getAllDistinctTimezones(
      _: Long,
      _: Long,
      _: String
    )(
      using _: SRLogger
    ))
      .expects(1, 2, "Asia/Kolkata", *)
      .returning(Success(Set("Asia/Kolkata")))

    emailChannelScheduler.scheduleTasksForChannel(
      channelData = ChannelData.EmailChannelData(emailSettingId = emailSettingId_1),
      teamId = 3L,
      accountService = accountService,
      //accountDAO = accountDAO,
      emailNotificationService = emailNotificationService,
      campaignService = campaignService,
      campaignProspectDAO = campaignProspectDAO,
      campaignStepVariantDAO = campaignStepVariantDAO,
      campaignStepDAO = campaignStepDAO,
      emailServiceCompanion = emailServiceCompanion,
      templateService = templateService,
      taskDAO = taskDAO,
      srShuffleUtils = srShuffleUtils,
      campaignProspectService = campaignProspectService,
      accountOrgBillingRelatedService = accountOrgBillingRelatedService,
      taskService = taskService,
      campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO,
      campaignsMissingMergeTagService = campaignsMissingMergeTagService,
      srRedisSimpleLockServiceV2 = srRedisSimpleLockServiceV2,
      mqWebhookCompleted = mqWebhookCompleted,
        srRollingUpdateCoreService = srRollingUpdateCoreService,
      calendarAppService = calendarAppService

    ).map { result =>
      Logger.info(s"result - $result")

      assert( true)
    }.recover{
      case e => Logger.info(s"error - $e")
        assert(e == Error)
    }
  }

  "scheduleEmailAccount" should "till time before schedule" in {
    val emailSetting = emailSettingCreateEmailSchedule.copy(
      latest_email_scheduled_at = Some(DateTime.now().plusHours(4))
    )

    (srRedisSimpleLockServiceV2.checkLock(
      _: CacheIdKeyForLock
    )(
      using _: SRLogger
    ))
      .expects(*, *)
      .returning(Success(false))

    (emailSettingDAO.findForScheduling)
      .expects(channel_id_1, Logger)
      .returning(Some(emailSetting))
    (emailSchedulerJedisService.acquireLockAndAddToSetForCampaignScheduling(_: Set[CampaignId], _: Int)(using _: SRLogger))
      .expects(Set(CampaignId(1)), 60 * 30, *)
      .returning(Success(Set(CampaignId(1))))
    (emailSchedulerJedisService.releaseLockForCampaignScheduling(_: Set[CampaignId])(using _: SRLogger))
      .expects(Set(CampaignId(1)), *)
      .returning(Success(Map(true -> Set(CampaignId(1)))))
    (accountService.find(_: Long)(_: SRLogger))
      .expects(account_id_1, *)
      .returning(Success(accountAdmin))

    (campaignService.findCampaignsForSchedulingEA)
      .expects(emailSettingId_1, Logger, TeamId(emailSetting.team_id))
      .returning(Success(Seq(campaignForScheduling)))

    (campaignProspectService.getAllDistinctTimezones(
      _: Long,
      _: Long,
      _: String
    )(
      using _: SRLogger
    ))
      .expects(1, 2, "Asia/Kolkata", *)
      .returning(Success(Set("Asia/Kolkata")))
    emailChannelScheduler.scheduleTasksForChannel(
      channelData = ChannelData.EmailChannelData(emailSettingId = emailSettingId_1),
      teamId = 3L,
      accountService = accountService,
      //accountDAO = accountDAO,
      emailNotificationService = emailNotificationService,
      campaignService = campaignService,
      campaignProspectDAO = campaignProspectDAO,
      campaignStepVariantDAO = campaignStepVariantDAO,
      campaignStepDAO = campaignStepDAO,
      emailServiceCompanion = emailServiceCompanion,
      templateService = templateService,
      taskDAO = taskDAO,
      srShuffleUtils = srShuffleUtils,
      campaignProspectService = campaignProspectService,
      accountOrgBillingRelatedService = accountOrgBillingRelatedService,
      taskService = taskService,
      campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO,
      campaignsMissingMergeTagService = campaignsMissingMergeTagService,
      srRedisSimpleLockServiceV2 = srRedisSimpleLockServiceV2,
      mqWebhookCompleted = mqWebhookCompleted,
        srRollingUpdateCoreService = srRollingUpdateCoreService,
      calendarAppService = calendarAppService

    ).map { result =>
      Logger.info(s"result - $result")

      assert(result == ScheduleTasksData(
        saved_tasks_count = 0,
        latest_task_scheduled_at = None,
        reached_scheduler_step = SchedulerSteps.ScheduleTillTimeBeforeFromTime
      ))
    }.recover {
      case e => Logger.info(s"error - $e :::: ${e.getMessage}")
        assert(e.getMessage == "")
    }
  }

  val tz_in = "Asia/Kolkata"
  val campaign_id_1 = 1L

  "scheduleEmailAccount" should "quota met for sending email" in {
    val emailSetting = emailSettingCreateEmailSchedule.copy(
      quota_per_day = 1
    )

    (srRedisSimpleLockServiceV2.checkLock(
      _: CacheIdKeyForLock
    )(
      using _: SRLogger
    ))
      .expects(*, *)
      .returning(Success(false))

    (emailSettingDAO.findForScheduling)
      .expects(channel_id_1, Logger)
      .returning(Some(emailSetting))

    (accountService.find(_: Long)(_: SRLogger))
      .expects(account_id_1, *)
      .returning(Success(accountAdmin))

    (campaignService.findCampaignsForSchedulingEA)
      .expects(emailSettingId_1, Logger, TeamId(emailSetting.team_id))
      .returning(Success(Seq(campaignForScheduling)))

    (campaignProspectDAO.getSentOrScheduledProspectsCountForEmail)
      .expects(Seq(EmailSettingId(emailSettingId_1)),tz_in, *, Logger, TeamId(emailSettingCreateEmailSchedule.team_id))
      .returning(Success(Map(EmailSettingId(emailSettingId_1)->1)))
    (emailSchedulerJedisService.acquireLockAndAddToSetForCampaignScheduling(_: Set[CampaignId], _: Int)(using _: SRLogger))
      .expects(Set(CampaignId(1)), 60 * 30, *)
      .returning(Success(Set(CampaignId(1))))
    (emailSchedulerJedisService.releaseLockForCampaignScheduling(_: Set[CampaignId])(using _: SRLogger))
      .expects(Set(CampaignId(1)), *)
      .returning(Success(Map(true -> Set(CampaignId(1)))))
    (campaignStepDAO.getDistinctStepTypesInCampaigns)
      .expects(List(campaign_id_1))
      .returning(Map(1L -> List(CampaignStepType.AutoEmailStep), 2L -> List(CampaignStepType.AutoEmailStep), 3L -> List(CampaignStepType.AutoEmailStep)))

    (campaignProspectService.getAllDistinctTimezones(
      _: Long,
      _: Long,
      _: String
    )(
      using _: SRLogger
    ))
      .expects(1, 2, "Asia/Kolkata", *)
      .returning(Success(Set("Asia/Kolkata")))
    (srRedisSimpleLockServiceV2.acquireLock(
      _: CacheIdKeyForLock,
      _: Int
    )(
      using _: SRLogger
    ))
      .expects(*, *, *)
      .returning(Success(true))


    emailChannelScheduler.scheduleTasksForChannel(
      channelData = ChannelData.EmailChannelData(emailSettingId = emailSettingId_1),
      teamId = 3L,
      accountService = accountService,
      //accountDAO = accountDAO,
      emailNotificationService = emailNotificationService,
      campaignService = campaignService,
      campaignProspectDAO = campaignProspectDAO,
      campaignStepVariantDAO = campaignStepVariantDAO,
      campaignStepDAO = campaignStepDAO,
      emailServiceCompanion = emailServiceCompanion,
      templateService = templateService,
      taskDAO = taskDAO,
      srShuffleUtils = srShuffleUtils,
      campaignProspectService = campaignProspectService,
      accountOrgBillingRelatedService = accountOrgBillingRelatedService,
      taskService = taskService,
      campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO,
      campaignsMissingMergeTagService = campaignsMissingMergeTagService,
      srRedisSimpleLockServiceV2 = srRedisSimpleLockServiceV2,
      mqWebhookCompleted = mqWebhookCompleted,
        srRollingUpdateCoreService = srRollingUpdateCoreService,
      calendarAppService = calendarAppService

    ).map { result =>
      Logger.info(s"result - $result")

      assert(result == ScheduleTasksData(
        saved_tasks_count = 0,
        latest_task_scheduled_at = None,
        reached_scheduler_step = SchedulerSteps.StepTypesThatCanBeScheduledIsEmpty
      ))
    }.recover {
      case e => Logger.info(s"error - $e :::: ${e.getMessage}")
        assert(e.getMessage == "")
    }
  }

  val campaign_id_1_tz_in = (campaign_id_1, tz_in)

  "scheduleEmailAccount" should "getScheduledProspectsCountForCampaign failed" in {

    (srRedisSimpleLockServiceV2.checkLock(
      _: CacheIdKeyForLock
    )(
      using _: SRLogger
    ))
      .expects(*, *)
      .returning(Success(false))

    (emailSettingDAO.findForScheduling)
      .expects(channel_id_1, Logger)
      .returning(Some(emailSettingCreateEmailSchedule))

    (accountService.find(_: Long)(_: SRLogger))
      .expects(account_id_1, *)
      .returning(Success(accountAdmin))

    (campaignService.findCampaignsForSchedulingEA)
      .expects(emailSettingId_1, Logger, TeamId(emailSettingCreateEmailSchedule.team_id))
      .returning(Success(Seq(campaignForScheduling)))

    (campaignProspectDAO.getSentOrScheduledProspectsCountForEmail)
      .expects(Seq(EmailSettingId(emailSettingId_1)), tz_in, *, Logger, TeamId(emailSettingCreateEmailSchedule.team_id))
      .returning(Success(Map()))

    (campaignStepDAO.getDistinctStepTypesInCampaigns)
      .expects(List(campaign_id_1))
      .returning(Map(campaign_id_1 -> List(CampaignStepType.AutoEmailStep)))
    (campaignProspectService.getAllDistinctTimezones(
      _: Long,
      _: Long,
      _: String
    )(
      using _: SRLogger
    ))
      .expects(1, 2, "Asia/Kolkata", *)
      .returning(Success(Set("Asia/Kolkata")))
    (emailSchedulerJedisService.acquireLockAndAddToSetForCampaignScheduling(_: Set[CampaignId], _: Int)(using _: SRLogger))
      .expects(Set(CampaignId(1)), 60 * 30, *)
      .returning(Success(Set(CampaignId(1))))
    (campaignProspectDAO.getScheduledProspectsCountForCampaign)
      .expects(Logger, Seq(EmailSettingId(emailSettingId_1)),2, Seq(campaign_id_1_tz_in), false)
      .returning(Failure(Error))

    emailChannelScheduler.scheduleTasksForChannel(
      channelData = ChannelData.EmailChannelData(emailSettingId = emailSettingId_1),
      teamId = 3L,
      accountService = accountService,
      //accountDAO = accountDAO,
      emailNotificationService = emailNotificationService,
      campaignService = campaignService,
      campaignProspectDAO = campaignProspectDAO,
      campaignStepVariantDAO = campaignStepVariantDAO,
      campaignStepDAO = campaignStepDAO,
      emailServiceCompanion = emailServiceCompanion,
      templateService = templateService,
      taskDAO = taskDAO,
      srShuffleUtils = srShuffleUtils,
      campaignProspectService = campaignProspectService,
      accountOrgBillingRelatedService = accountOrgBillingRelatedService,
      taskService = taskService,
      campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO,
      campaignsMissingMergeTagService = campaignsMissingMergeTagService,
      srRedisSimpleLockServiceV2 = srRedisSimpleLockServiceV2,
      mqWebhookCompleted = mqWebhookCompleted,
        srRollingUpdateCoreService = srRollingUpdateCoreService,
      calendarAppService = calendarAppService

    ).map { result =>
      Logger.info(s"result - $result")

      assert(false)
    }.recover{
      case e => Logger.info(s"error - $e")
        assert(e == Error)
    }


  }

  "scheduleEmailAccount" should "None.get while getting ordered steps" in {

    (srRedisSimpleLockServiceV2.checkLock(
      _: CacheIdKeyForLock
    )(
      using _: SRLogger
    ))
      .expects(*, *)
      .returning(Success(false))

    (emailSettingDAO.findForScheduling)
      .expects(channel_id_1, *)
      .returning(Some(emailSettingCreateEmailSchedule))

    (accountService.find(_: Long)(_: SRLogger))
      .expects(account_id_1, *)
      .returning(Success(accountAdmin))

    (campaignService.findCampaignsForSchedulingEA)
      .expects(emailSettingId_1, *, TeamId(emailSettingCreateEmailSchedule.team_id))
      .returning(Success(Seq(campaignForScheduling)))

    (campaignProspectDAO.getSentOrScheduledProspectsCountForEmail)
      .expects(Seq(EmailSettingId(emailSettingId_1)),tz_in, *, *, TeamId(emailSettingCreateEmailSchedule.team_id))
      .returning(Success(Map()))

    (campaignStepDAO.getDistinctStepTypesInCampaigns)
      .expects(List(campaign_id_1))
      .returning(Map(campaign_id_1 -> List(CampaignStepType.AutoEmailStep)))
    (campaignProspectService.getAllDistinctTimezones(
      _: Long,
      _: Long,
      _: String
    )(
      using _: SRLogger
    ))
      .expects(1, 2, "Asia/Kolkata", *)
      .returning(Success(Set("Asia/Kolkata")))

    (campaignProspectDAO.getScheduledProspectsCountForCampaign)
      .expects(*, Seq(EmailSettingId(emailSettingId_1)),2, Seq(campaign_id_1_tz_in), false)
      .returning(Success(Seq()))

    (campaignProspectService.getAllDistinctTimezones(
      _: Long,
      _: Long,
      _: String
    )(
      using _: SRLogger
    ))
      .expects(campaign_id_1, 2, tz_in, Logger)
      .returning(Success(Set("Asia/Kolkata")))

    (emailSchedulerJedisService.acquireLockAndAddToSetForCampaignScheduling(_: Set[CampaignId], _: Int)(using _: SRLogger))
      .expects(Set(CampaignId(1)), 60 * 30, *)
      .returning(Success(Set(CampaignId(1))))
    (campaignStepVariantDAO.findByCampaignIdForSchedule)
      .expects(campaign_id_1, false, *)
      .returning(Seq())
    //    (campaignStepDAO.getOrderedSteps)
    //    .expects(List(), 1)
    //      .returning(List())
    emailChannelScheduler.scheduleTasksForChannel(
      channelData = ChannelData.EmailChannelData(emailSettingId = emailSettingId_1),
      teamId = 3L,
      accountService = accountService,
      //accountDAO = accountDAO,
      emailNotificationService = emailNotificationService,
      campaignService = campaignService,
      campaignProspectDAO = campaignProspectDAO,
      campaignStepVariantDAO = campaignStepVariantDAO,
      campaignStepDAO = campaignStepDAO,
      emailServiceCompanion = emailServiceCompanion,
      templateService = templateService,
      taskDAO = taskDAO,
      srShuffleUtils = srShuffleUtils,
      campaignProspectService = campaignProspectService,
      accountOrgBillingRelatedService = accountOrgBillingRelatedService,
      taskService = taskService,
      campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO,
      campaignsMissingMergeTagService = campaignsMissingMergeTagService,
      srRedisSimpleLockServiceV2 = srRedisSimpleLockServiceV2,
      mqWebhookCompleted = mqWebhookCompleted,
        srRollingUpdateCoreService = srRollingUpdateCoreService,
      calendarAppService = calendarAppService

    ).map { result =>
      Logger.info(s"result - $result")

      assert(false)
    }.recover{
      case e => Logger.info(s"error - $e")
        assert(e.getMessage == "key not found: 1")
    }


  }

  /* NOTE: 30 Sep 2022: This scenario is no longer possible, as head_step_id wont be null for CampaignForScheduling

  "scheduleEmailAccount" should "send None.get because head step id is none for getOrderedSteps call" in {

    (emailSettingDAO.findForScheduling)
      .expects(channel_id_1, Logger)
      .returning(Some(emailSettingCreateEmailSchedule))

    (accountService.find(_: Long)(_: SRLogger))
      .expects(account_id_1, *)
      .returning(Success(accountAdmin))

    (campaignService.findCampaignsForSchedulingEA)
      .expects(emailSettingId_1, Logger)
      .returning(Success(Seq(campaignForScheduling.copy(head_step_id = None))))

    (campaignProspectDAO.getSentOrScheduledProspectsCountForEmail)
      .expects(emailSettingId_1,tz_in, Logger)
      .returning(Success(None))


    (campaignProspectDAO.getScheduledProspectsCountForCampaign)
      .expects(Logger, emailSettingId_1, Seq(campaign_id_1_tz_in))
      .returning(Success(Seq()))

    /* this wont get called anymore because of the .get
    (campaignStepVariantDAO.findByCampaignIdForSchedule)
      .expects(1, false, Logger)
      .returning(Seq(campaignStepWithChildren))
    */

    emailChannelScheduler.scheduleTasksForChannel(channelData = ChannelData.EmailChannelData(emailSettingId = emailSettingId_1)).map{ result =>

      Logger.info(s"result - $result")

      assert(true)
    }.recover{
      case e => Logger.info(s"error - $e")
        assert(e.getMessage == "None.get")
    }


  }
  */

  /* not needed since we added a
  .groupBy(_.campaign_email_settings_id) at EmailChannelScheduler line 531
  and the call is made on the groups since we dont have anything in this group we are not making the call.
  so no possibility for the error to happen that we want in this case


  NOTE: 28-feb-2024: the test is hitting the failure path in ChannelSchedulerTrait.postSchedulingProcess
  but there is no error being returned from the mocked fns below, its confusing ...

   */
  "scheduleEmailAccount" should "No steps to be saved" in {

    (orgMetadataService.getOrgMetadata(_ : OrgId))
      .expects(OrgId(id = 1))
      .returning(Success(orgMetadata))

    val campaign = campaignForScheduling.copy(
      campaign_max_emails_per_day = 0
    )
    (srRedisSimpleLockServiceV2.checkLock(
      _: CacheIdKeyForLock
    )(
      using _: SRLogger
    ))
      .expects(CacheIdKeyForLock("channel_limit_lock_teamId_3_email_channel_1"), *)
      .returning(Success(false))
    (emailSettingDAO.findForScheduling)
      .expects(channel_id_1, Logger)
      .returning(Some(emailSettingCreateEmailSchedule))

    (accountService.find(_: Long)(_: SRLogger))
      .expects(account_id_1, *)
      .returning(Success(accountAdmin))

    (campaignService.findCampaignsForSchedulingEA)
      .expects(emailSettingId_1, Logger, TeamId(2))
      .returning(Success(Seq(campaign)))
    (emailSchedulerJedisService.acquireLockAndAddToSetForCampaignScheduling(_: Set[CampaignId], _: Int)(using _: SRLogger))
      .expects(Set(CampaignId(1)), 60 * 30, *)
      .returning(Success(Set(CampaignId(1))))
    (emailSchedulerJedisService.releaseLockForCampaignScheduling(_: Set[CampaignId])(using _: SRLogger))
      .expects(Set(CampaignId(1)), *)
      .returning(Success(Map(true -> Set(CampaignId(1)))))
    (campaignProspectDAO.getSentOrScheduledProspectsCountForEmail)
      .expects(Seq(EmailSettingId(emailSettingId_1)),tz_in, *, Logger, TeamId(emailSettingCreateEmailSchedule.team_id))
      .returning(Success(Map()))
    (campaignService._updateLastScheduled)
      .expects(Seq(1L), *, TeamId(2))
      .returning(Success(1))

    (campaignProspectService.getAllDistinctTimezones(
      _: Long,
      _: Long,
      _: String
    )(
      using _: SRLogger
    ))
      .expects(1, 2, "Asia/Kolkata", *)
      .returning(Success(Set("Asia/Kolkata")))
      .twice()
    (campaignStepDAO.getDistinctStepTypesInCampaigns)
      .expects(List(campaign_id_1))
      .returning(Map(campaign_id_1 -> List(CampaignStepType.AutoEmailStep)))


    (campaignProspectDAO.getScheduledProspectsCountForCampaign)
      .expects(Logger, Seq(EmailSettingId(emailSettingId_1)), 2, Seq(campaign_id_1_tz_in), false)
      .returning(Success(Seq()))

//    (mqWebhookCompleted.publishCompletedProspects)
//      .expects(1, 2, List())
//      .returning(Success(0))

    //    (emailScheduledDAO.saveEmailsToBeScheduledAndUpdateCampaignDataV2)
    //    .expects(Vector(), 123, Logger)
    //      .returning(Failure(Error))
    (campaignProspectDAO._updateScheduledStatus(_: Seq[CampaignProspectUpdateScheduleStatus])(using _:SRLogger))
      .expects(List(), *)
      .returning(Success(Seq()))
    emailChannelScheduler.scheduleTasksForChannel(
      channelData = ChannelData.EmailChannelData(emailSettingId = emailSettingId_1),
      teamId = 3L, // FIXME there is inconsistency here
      accountService = accountService,
      //accountDAO = accountDAO,
      emailNotificationService = emailNotificationService,
      campaignService = campaignService,
      campaignProspectDAO = campaignProspectDAO,
      campaignStepVariantDAO = campaignStepVariantDAO,
      campaignStepDAO = campaignStepDAO,
      emailServiceCompanion = emailServiceCompanion,
      templateService = templateService,
      taskDAO = taskDAO,
      srShuffleUtils = srShuffleUtils,
      campaignProspectService = campaignProspectService,
      accountOrgBillingRelatedService = accountOrgBillingRelatedService,
      taskService = taskService,
      campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO,
      campaignsMissingMergeTagService = campaignsMissingMergeTagService,
      mqWebhookCompleted = mqWebhookCompleted,
      srRedisSimpleLockServiceV2 = srRedisSimpleLockServiceV2,
        srRollingUpdateCoreService = srRollingUpdateCoreService,
      calendarAppService = calendarAppService

    ).map { result =>
      Logger.info(s"result - $result")

      assert(result == ScheduleTasksData(
        saved_tasks_count = 0,
        latest_task_scheduled_at = None,
        reached_scheduler_step = SchedulerSteps.PostSchedulingError
      ))
    }.recover{
      case e => Logger.info(s"error - $e")
        assert(e == Error)
    }


  }

  "scheduleEmailAccount" should "campaignProspectDAO._updateScheduledStatus send an error" in {

    (orgMetadataService.getOrgMetadata(_: OrgId))
      .expects(OrgId(id = 1))
      .returning(Success(orgMetadata))

    val campaign = campaignForScheduling.copy(
      campaign_max_emails_per_day = 0
    )

    (srRedisSimpleLockServiceV2.checkLock(
      _: CacheIdKeyForLock
    )(
      using _: SRLogger
    ))
      .expects(*, *)
      .returning(Success(false))

    (emailSettingDAO.findForScheduling)
      .expects(channel_id_1, Logger)
      .returning(Some(emailSettingCreateEmailSchedule))

    (accountService.find(_: Long)(_: SRLogger))
      .expects(account_id_1, *)
      .returning(Success(accountAdmin))

    (campaignService.findCampaignsForSchedulingEA)
      .expects(emailSettingId_1, Logger, TeamId(emailSettingCreateEmailSchedule.team_id))
      .returning(Success(Seq(campaign)))
    (emailSchedulerJedisService.acquireLockAndAddToSetForCampaignScheduling(_: Set[CampaignId], _: Int)(using _: SRLogger))
      .expects(Set(CampaignId(1)), 60 * 30, *)
      .returning(Success(Set(CampaignId(1))))
    (emailSchedulerJedisService.releaseLockForCampaignScheduling(_: Set[CampaignId])(using _: SRLogger))
      .expects(Set(CampaignId(1)), *)
      .returning(Success(Map(true -> Set(CampaignId(1)))))
    (campaignProspectDAO.getSentOrScheduledProspectsCountForEmail)
      .expects(Seq(EmailSettingId(emailSettingId_1)),"Asia/Kolkata", *, Logger, TeamId(emailSettingCreateEmailSchedule.team_id))
      .returning(Success(Map()))
    (campaignService._updateLastScheduled)
      .expects(Seq(1L), *, TeamId(2))
      .returning(Success(1))
    (campaignProspectService.getAllDistinctTimezones(
      _: Long,
      _: Long,
      _: String
    )(
      using _: SRLogger
    ))
      .expects(1, 2, "Asia/Kolkata", *)
      .returning(Success(Set("Asia/Kolkata")))
      .twice()
    (campaignStepDAO.getDistinctStepTypesInCampaigns)
      .expects(List(campaign_id_1))
      .returning(Map(campaign_id_1 -> List(CampaignStepType.AutoEmailStep)))

    (campaignProspectDAO.getScheduledProspectsCountForCampaign)
      .expects(Logger, Seq(EmailSettingId(emailSettingId_1)), 2, Seq(campaign_id_1_tz_in), false)
      .returning(Success(Seq()))

//    (mqWebhookCompleted.publishCompletedProspects)
//      .expects(1, 2, List())
//      .returning(Success(0))

    // will not be called since vector is empty
    //    (emailScheduledDAO.saveEmailsToBeScheduledAndUpdateCampaignDataV2)
    //      .expects(Vector(), 123, Logger)
    //      .returning(Success(Seq()))
    (campaignProspectDAO._updateScheduledStatus(_: Seq[CampaignProspectUpdateScheduleStatus])(using _:SRLogger))
      .expects(List(), *)
      .returning(Failure(Error))
    //    (() => repTrackingHostService.getRepTrackingHosts())
    //      .expects()
    //      .returning(Failure(Error))
    emailChannelScheduler.scheduleTasksForChannel(
      channelData = ChannelData.EmailChannelData(emailSettingId = emailSettingId_1),
      teamId = 3L,
      accountService = accountService,
      //accountDAO = accountDAO,
      emailNotificationService = emailNotificationService,
      campaignService = campaignService,
      campaignProspectDAO = campaignProspectDAO,
      campaignStepVariantDAO = campaignStepVariantDAO,
      campaignStepDAO = campaignStepDAO,
      emailServiceCompanion = emailServiceCompanion,
      templateService = templateService,
      taskDAO = taskDAO,
      srShuffleUtils = srShuffleUtils,
      campaignProspectService = campaignProspectService,
      accountOrgBillingRelatedService = accountOrgBillingRelatedService,
      taskService = taskService,
      campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO,
      campaignsMissingMergeTagService = campaignsMissingMergeTagService,
      srRedisSimpleLockServiceV2 = srRedisSimpleLockServiceV2,
      mqWebhookCompleted = mqWebhookCompleted,
        srRollingUpdateCoreService = srRollingUpdateCoreService,
      calendarAppService = calendarAppService

    ).map { result =>
      Logger.info(s"result - $result")

      assert(true)
    }.recover{
      case e => Logger.info(s"error - $e")
        assert(e == Error)
    }


  }

  "scheduleEmailAccount" should " fail becaues mqWebhookCompleted publishCompletedProspects failed" in {

    val campaign = campaignForScheduling.copy(
      campaign_max_emails_per_day = 0
    )

    (orgMetadataService.getOrgMetadata(_: OrgId))
      .expects(OrgId(id = 1))
      .returning(Success(orgMetadata))


    (srRedisSimpleLockServiceV2.checkLock(
      _: CacheIdKeyForLock
    )(
      using _: SRLogger
    ))
      .expects(*, *)
      .returning(Success(false))

    (emailSettingDAO.findForScheduling)
      .expects(channel_id_1, Logger)
      .returning(Some(emailSettingCreateEmailSchedule))

    (emailSchedulerJedisService.acquireLockAndAddToSetForCampaignScheduling(_: Set[CampaignId], _: Int)(using _: SRLogger))
      .expects(Set(CampaignId(1)), 60 * 30, *)
      .returning(Success(Set(CampaignId(1))))
    (emailSchedulerJedisService.releaseLockForCampaignScheduling(_: Set[CampaignId])(using _: SRLogger))
      .expects(Set(CampaignId(1)), *)
      .returning(Success(Map(true -> Set(CampaignId(1)))))
    (accountService.find(_: Long)(_: SRLogger))
      .expects(account_id_1, *)
      .returning(Success(accountAdmin))

    (campaignService.findCampaignsForSchedulingEA)
      .expects(emailSettingId_1, Logger, TeamId(emailSettingCreateEmailSchedule.team_id))
      .returning(Success(Seq(campaign)))

    (campaignProspectDAO.getSentOrScheduledProspectsCountForEmail)
      .expects(Seq(EmailSettingId(emailSettingId_1)),tz_in, *, Logger, TeamId(emailSettingCreateEmailSchedule.team_id))
      .returning(Success(Map()))

    (campaignStepDAO.getDistinctStepTypesInCampaigns)
      .expects(List(campaign_id_1))
      .returning(Map(campaign_id_1 -> List(CampaignStepType.AutoEmailStep)))


    (campaignProspectDAO.getScheduledProspectsCountForCampaign)
      .expects(Logger, Seq(EmailSettingId(1)), 2, Seq(campaign_id_1_tz_in), false)
      .returning(Success(Seq()))
    (campaignService._updateLastScheduled)
      .expects(Seq(1L), *, TeamId(2))
      .returning(Success(1))

    // will not be called since the vector is empty
    //    (emailScheduledDAO.saveEmailsToBeScheduledAndUpdateCampaignDataV2)
    //      .expects(Vector(), 123, Logger)
    //      .returning(Success(Seq()))

    //    (() => repTrackingHostService.getRepTrackingHosts())
    //      .expects()
    //      .returning(Success(Seq()))

    //    (emailScheduledDAO.addBodyToEmailsToBeScheduled)
    //    .expects(List(), Logger)
    //      .returning(Seq())
//
//    (mqWebhookCompleted.publishCompletedProspects)
//      .expects(account_id_1, 2, List())
//      .returning(Failure(Error))

    (campaignProspectDAO._updateScheduledStatus(_: Seq[CampaignProspectUpdateScheduleStatus])(using _:SRLogger))
      .expects(List(), *)
      .returning(Success(Seq()))
    (emailSettingDAO._updateLastScheduled)
      .expects(emailSettingId_1.toLong, None, *)
      .returning(0)
    (campaignService._updateLastScheduled)
      .expects(List(), *, TeamId(id = 3L))
      .returning(Success(0))
    (campaignService.setNextToBeScheduledAt)
      .expects(
        CampaignSetNextToBeScheduledAtData.SchedulerSuccessFlow(
          campaignDataWithAllTheTimezone = Set(),
          channelType = ChannelType.EmailChannel,
          total_scheduled = 0
        ), *, TeamId(id = 2L))
      .returning(Success(0))
    (campaignProspectService.getAllDistinctTimezones(
      _: Long,
      _: Long,
      _: String
    )(
      using _: SRLogger
    ))
      .expects(1, 2, "Asia/Kolkata", *)
      .returning(Success(Set("Asia/Kolkata")))
      .twice()

    emailChannelScheduler.scheduleTasksForChannel(
      channelData = ChannelData.EmailChannelData(emailSettingId = emailSettingId_1),
      teamId = 3L,
      accountService = accountService,
      //accountDAO = accountDAO,
      emailNotificationService = emailNotificationService,
      campaignService = campaignService,
      campaignProspectDAO = campaignProspectDAO,
      campaignStepVariantDAO = campaignStepVariantDAO,
      campaignStepDAO = campaignStepDAO,
      emailServiceCompanion = emailServiceCompanion,
      templateService = templateService,
      taskDAO = taskDAO,
      srShuffleUtils = srShuffleUtils,
      campaignProspectService = campaignProspectService,
      accountOrgBillingRelatedService = accountOrgBillingRelatedService,
      taskService = taskService,
      campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO,
      campaignsMissingMergeTagService = campaignsMissingMergeTagService,
      srRedisSimpleLockServiceV2 = srRedisSimpleLockServiceV2,
      mqWebhookCompleted = mqWebhookCompleted,
        srRollingUpdateCoreService = srRollingUpdateCoreService,
      calendarAppService = calendarAppService

    ).map { result =>
      Logger.info(s"result - $result")

      assert( true)
    }.recover{
      case e => Logger.info(s"error - $e")
        assert(e == Error)
    }


  }

  "scheduleEmailAccount" should "success with nothing to send" in {

    val campaign = campaignForScheduling.copy(
      campaign_max_emails_per_day = 0
    )

    (orgMetadataService.getOrgMetadata(_: OrgId))
      .expects(OrgId(id = 1))
      .returning(Success(orgMetadata))

    (srRedisSimpleLockServiceV2.checkLock(
      _: CacheIdKeyForLock
    )(
      using _: SRLogger
    ))
      .expects(*, *)
      .returning(Success(false))

    (emailSettingDAO.findForScheduling)
      .expects(channel_id_1, Logger)
      .returning(Some(emailSettingCreateEmailSchedule))

    (accountService.find(_: Long)(_: SRLogger))
      .expects(account_id_1, *)
      .returning(Success(accountAdmin))

    (campaignService.findCampaignsForSchedulingEA)
      .expects(emailSettingId_1, Logger, TeamId(emailSettingCreateEmailSchedule.team_id))
      .returning(Success(Seq(campaign)))

    (campaignProspectDAO.getSentOrScheduledProspectsCountForEmail)
      .expects(Seq(EmailSettingId(emailSettingId_1)),tz_in, *, Logger, TeamId(emailSettingCreateEmailSchedule.team_id))
      .returning(Success(Map()))

    (emailSchedulerJedisService.acquireLockAndAddToSetForCampaignScheduling(_: Set[CampaignId], _: Int)(using _: SRLogger))
      .expects(Set(CampaignId(1)), 60 * 30, *)
      .returning(Success(Set(CampaignId(1))))

    (emailSchedulerJedisService.releaseLockForCampaignScheduling(_: Set[CampaignId])(using _: SRLogger))
      .expects(Set(CampaignId(1)), *)
      .returning(Success(Map(true -> Set(CampaignId(1)))))
    (campaignStepDAO.getDistinctStepTypesInCampaigns)
      .expects(List(campaign_id_1))
      .returning(Map(campaign_id_1 -> List(CampaignStepType.AutoEmailStep)))
    (campaignProspectService.getAllDistinctTimezones(
      _: Long,
      _: Long,
      _: String
    )(
      using _: SRLogger
    ))
      .expects(1, 2, "Asia/Kolkata", *)
      .returning(Success(Set("Asia/Kolkata")))
      .twice()

    (campaignProspectDAO.getScheduledProspectsCountForCampaign)
      .expects(Logger, Seq(EmailSettingId(emailSettingId_1)), 2, Seq(campaign_id_1_tz_in), false)
      .returning(Success(Seq()))

    (campaignService._updateLastScheduled)
      .expects(Seq(1L), *, TeamId(2))
      .returning(Success(1))
    // will not be called since the vector is empty

    //    (emailScheduledDAO.saveEmailsToBeScheduledAndUpdateCampaignDataV2)
    //      .expects(Vector(), 123, Logger)
    //      .returning(Success(Seq()))
    //
    //    (() => repTrackingHostService.getRepTrackingHosts())
    //      .expects()
    //      .returning(Success(Seq()))
    //
    //    (emailScheduledDAO.addBodyToEmailsToBeScheduled)
    //      .expects(List(), Logger)
    //      .returning(Seq())
//
//    (mqWebhookCompleted.publishCompletedProspects)
//      .expects(account_id_1, 2, List())
//      .returning(Success(Success(0)))

    (campaignProspectDAO._updateScheduledStatus(_: Seq[CampaignProspectUpdateScheduleStatus])(using _:SRLogger))
      .expects(List() , *)
      .returning(Success(Seq(0)))

    (emailSettingDAO._updateLastScheduled)
      .expects(emailSettingId_1, None, Logger)
      .returning(1)
    (campaignService._updateLastScheduled)
      .expects(List(), Logger, TeamId(id = 3L))
      .returning(Success(1))

    (campaignService.setNextToBeScheduledAt)
      .expects(

        CampaignSetNextToBeScheduledAtData.SchedulerSuccessFlow(
          campaignDataWithAllTheTimezone = Set(),
          channelType = ChannelType.EmailChannel,
          total_scheduled = 0
        ), *, TeamId(id = 2L))
      .returning(Success(1))

    emailChannelScheduler.scheduleTasksForChannel(
      channelData = ChannelData.EmailChannelData(emailSettingId = emailSettingId_1),
      teamId = 3L,
      accountService = accountService,
      //accountDAO = accountDAO,
      emailNotificationService = emailNotificationService,
      campaignService = campaignService,
      campaignProspectDAO = campaignProspectDAO,
      campaignStepVariantDAO = campaignStepVariantDAO,
      campaignStepDAO = campaignStepDAO,
      emailServiceCompanion = emailServiceCompanion,
      templateService = templateService,
      taskDAO = taskDAO,
      srShuffleUtils = srShuffleUtils,
      campaignProspectService = campaignProspectService,
      accountOrgBillingRelatedService = accountOrgBillingRelatedService,
      taskService = taskService,
      campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO,
      campaignsMissingMergeTagService = campaignsMissingMergeTagService,
      srRedisSimpleLockServiceV2 = srRedisSimpleLockServiceV2,
      mqWebhookCompleted = mqWebhookCompleted,
        srRollingUpdateCoreService = srRollingUpdateCoreService,
      calendarAppService = calendarAppService

    ).map { result =>
      Logger.info(s"result - $result")

      assert(result == ScheduleTasksData(
        saved_tasks_count = 0,
        latest_task_scheduled_at = None,
        reached_scheduler_step = SchedulerSteps.PostSchedulingSuccess
      ))
    }


  }


  // not a relevant case since we dont have a vector, so no saved email
  //  "scheduleEmailAccount" should "Falied because EmailScheduledNewAfterSaving doesnt have a step_id" in {
  //
  //    val campaign = campaignForScheduling.copy(
  //      campaign_max_emails_per_day = 0
  //    )
  //    (emailSettingDAO.findForScheduling)
  //      .expects(channel_id_1, Logger)
  //      .returning(Some(emailSettingCreateEmailSchedule))
  //
  //    (accountService.find(_: Long)(_: SRLogger))
  //      .expects(account_id_1, *)
  //      .returning(Success(accountAdmin))
  //
  //    (campaignService.findCampaignsForSchedulingEA)
  //      .expects(emailSettingId_1, Logger)
  //      .returning(Success(Seq(campaign)))
  //
  //    (campaignProspectDAO.getSentOrScheduledProspectsCountForEmail)
  //      .expects(Seq(EmailSettingId(emailSettingId_1)),tz_in, *, Logger)
  //      .returning(Success(Map()))
  //
  //    (campaignStepDAO.getDistinctStepTypesInCampaigns)
  //      .expects(List(campaign_id_1))
  //      .returning(Map(campaign_id_1 -> List(CampaignStepType.AutoEmailStep)))
  //
  //
  //    (campaignProspectDAO.getScheduledProspectsCountForCampaign)
  //      .expects(Logger, Seq(EmailSettingId(emailSettingId_1)), 2, Seq(campaign_id_1_tz_in), false)
  //      .returning(Success(Seq()))
  //
  //    (mqWebhookCompleted.publishCompletedProspects)
  //      .expects(1, 2, List())
  //      .returning(Success(0))
  //
  //    // will not be called since the vector is empty
  ////    (emailScheduledDAO.saveEmailsToBeScheduledAndUpdateCampaignDataV2)
  ////      .expects(Vector(), 123, Logger)
  ////      .returning(Success(Seq(emailScheduledNewAfterSaving)))
  //
  //    (accountService.getRepTrackingHosts)
  //      .expects()
  //      .returning(Success(Seq()))
  //
  //    emailChannelScheduler.scheduleTasksForChannel(
  //      channelData = ChannelData.EmailChannelData(emailSettingId = emailSettingId_1),
  //      teamId = 3L,
  //      accountService = accountService,
  //      //accountDAO = accountDAO,
  //      emailNotificationService = emailNotificationService,
  //      campaignService = campaignService,
  //      campaignProspectDAO = campaignProspectDAO,
  //      campaignStepVariantDAO = campaignStepVariantDAO,
  //      campaignStepDAO = campaignStepDAO,
  //      emailServiceCompanion = emailServiceCompanion,
  //      templateService = templateService,
  //      taskDAO = taskDAO,
  //      srShuffleUtils = srShuffleUtils,
  //      campaignProspectService = campaignProspectService,
  //      taskService = taskService,
  //      campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO,
  //      campaignsMissingMergeTagService = campaignsMissingMergeTagService,
  //      mqWebhookCompleted = mqWebhookCompleted,
  //    ).map { result =>
  //      Logger.info(s"result - $result")
  //
  //      assert( result == (0, None))
  //    }.recover{
  //      case e =>
  //        Logger.info(s"error - $e")
  //        assert(e.getMessage.contains("NO scheduleCampaign for em"))
  //    }
  //
  //
  //  }


  // not a relevant case since we dont have a vector, so no saved email
  //  "scheduleEmailAccount" should "Falied because getScheduledProspectsCountForCampaign doesnt have a step_id" in {
  //
  //    val emailScheduledNew = emailScheduledNewAfterSaving.copy(
  //      step_id = Some(1)
  //    )
  //
  //    val campaign = campaignForScheduling.copy(
  //      campaign_max_emails_per_day = 0
  //    )
  //    (emailSettingDAO.findForScheduling)
  //      .expects(channel_id_1, Logger)
  //      .returning(Some(emailSettingCreateEmailSchedule))
  //
  //    (accountService.find(_: Long)(_: SRLogger))
  //      .expects(account_id_1, *)
  //      .returning(Success(accountAdmin))
  //
  //    (campaignService.findCampaignsForSchedulingEA)
  //      .expects(emailSettingId_1, Logger)
  //      .returning(Success(Seq(campaign)))
  //
  //    (campaignProspectDAO.getSentOrScheduledProspectsCountForEmail)
  //      .expects(Seq(EmailSettingId(emailSettingId_1)),tz_in, *, Logger)
  //      .returning(Success(Map()))
  //
  //    (campaignStepDAO.getDistinctStepTypesInCampaigns)
  //      .expects(List(campaign_id_1))
  //      .returning(Map(campaign_id_1 -> List(CampaignStepType.AutoEmailStep)))
  //
  //    (campaignProspectDAO.getScheduledProspectsCountForCampaign)
  //      .expects(Logger, Seq(EmailSettingId(emailSettingId_1)),2, Seq(campaign_id_1_tz_in), false)
  //      .returning(Success(Seq()))
  //
  //    (mqWebhookCompleted.publishCompletedProspects)
  //      .expects(1, 2, List())
  //      .returning(Success(0))
  //
  ////    (emailScheduledDAO.saveEmailsToBeScheduledAndUpdateCampaignDataV2)
  ////      .expects(Vector(), 123, Logger)
  ////      .returning(Success(Seq(emailScheduledNew)))
  //
  //    (accountService.getRepTrackingHosts)
  //      .expects()
  //      .returning(Success(Seq()))
  //
  //
  //    emailChannelScheduler.scheduleTasksForChannel(
  //      channelData = ChannelData.EmailChannelData(emailSettingId = emailSettingId_1),
  //      teamId = 3L,
  //      accountService = accountService,
  //      //accountDAO = accountDAO,
  //      emailNotificationService = emailNotificationService,
  //      campaignService = campaignService,
  //      campaignProspectDAO = campaignProspectDAO,
  //      campaignStepVariantDAO = campaignStepVariantDAO,
  //      campaignStepDAO = campaignStepDAO,
  //      emailServiceCompanion = emailServiceCompanion,
  //      templateService = templateService,
  //      taskDAO = taskDAO,
  //      srShuffleUtils = srShuffleUtils,
  //      campaignProspectService = campaignProspectService,
  //      taskService = taskService,
  //      campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO,
  //      campaignsMissingMergeTagService = campaignsMissingMergeTagService,
  //      mqWebhookCompleted = mqWebhookCompleted,
  //    ).map { result =>
  //      Logger.info(s"result - $result")
  //
  //      assert( result == (0, None))
  //    }.recover{
  //      case e =>
  //        Logger.info(s"error - $e")
  //        assert(e.getMessage.contains("NO scheduleCampaign for em"))
  //    }
  //
  //
  //  }

  "scheduleEmailAccount" should "Error while findAndMarkCompletedProspects" in {
    (srRedisSimpleLockServiceV2.checkLock(
      _: CacheIdKeyForLock
    )(
      using _: SRLogger
    ))
      .expects(*, *)
      .returning(Success(false))

    (emailSettingDAO.findForScheduling)
      .expects(channel_id_1, *)
      .returning(Some(emailSettingCreateEmailSchedule))

    (accountService.find(_: Long)(_: SRLogger))
      .expects(account_id_1, *)
      .returning(Success(accountAdmin))

    (campaignService.findCampaignsForSchedulingEA)
      .expects(emailSettingId_1, *, TeamId(emailSettingCreateEmailSchedule.team_id))
      .returning(Success(Seq(campaignForScheduling, campaignForScheduling.copy(campaign_id = 2), campaignForScheduling.copy(campaign_id = 3), campaignForScheduling.copy(campaign_id = 4))))
    (emailSchedulerJedisService.acquireLockAndAddToSetForCampaignScheduling(_: Set[CampaignId], _: Int)(using _: SRLogger))
      .expects(Set(CampaignId(1), CampaignId(2), CampaignId(3), CampaignId(4)), 60 * 30, *)
      .returning(Success(Set(CampaignId(1), CampaignId(2), CampaignId(3), CampaignId(4))))

    (campaignProspectDAO.getSentOrScheduledProspectsCountForEmail)
      .expects(Seq(EmailSettingId(1)),"Asia/Kolkata", *, *, TeamId(emailSettingCreateEmailSchedule.team_id))
      .returning(Success(Map(EmailSettingId(1)->1)))

    (campaignStepDAO.getDistinctStepTypesInCampaigns)
      .expects(List(1L, 2L, 3L, 4L))
      .returning(Map(1L -> List(CampaignStepType.AutoEmailStep), 2L -> List(CampaignStepType.AutoEmailStep), 3L -> List(CampaignStepType.AutoEmailStep), 4L -> List(CampaignStepType.AutoEmailStep)))
    (campaignProspectService.getAllDistinctTimezones(
      _: Long,
      _: Long,
      _: String
    )(
      using _: SRLogger
    ))
      .expects(1, 2, "Asia/Kolkata", *)
      .returning(Success(Set("Asia/Kolkata")))


    (campaignProspectService.getAllDistinctTimezones(
      _: Long,
      _: Long,
      _: String
    )(
      using _: SRLogger
    ))
      .expects(2, 2, "Asia/Kolkata", *)
      .returning(Success(Set("Asia/Kolkata")))
    (campaignProspectService.getAllDistinctTimezones(
      _: Long,
      _: Long,
      _: String
    )(
      using _: SRLogger
    ))
      .expects(3, 2, "Asia/Kolkata", *)
      .returning(Success(Set("Asia/Kolkata")))
    (campaignProspectService.getAllDistinctTimezones(
      _: Long,
      _: Long,
      _: String
    )(
      using _: SRLogger
    ))
      .expects(4, 2, "Asia/Kolkata", *)
      .returning(Success(Set("Asia/Kolkata")))

    (campaignProspectDAO.getScheduledProspectsCountForCampaign)
      .expects(*, Seq(EmailSettingId(1)), 2, List((1.toLong,"Asia/Kolkata"), (2.toLong,"Asia/Kolkata"), (3.toLong,"Asia/Kolkata"), (4.toLong,"Asia/Kolkata")), false)
      .returning(Success(Seq(scheduledProspectsCountForCampaignEmail, scheduledProspectsCountForCampaignEmail.copy(campaignId = 2), scheduledProspectsCountForCampaignEmail.copy(campaignId = 3), scheduledProspectsCountForCampaignEmail.copy(campaignId = 4))))


    (campaignProspectService.getAllDistinctTimezones(
      _: Long,
      _: Long,
      _: String
    )(
      using _: SRLogger
    ))
      .expects(1, 2, "Asia/Kolkata", Logger)
      .returning(Success(Set("Asia/Kolkata")))

    (campaignStepVariantDAO.findByCampaignIdForSchedule)
      .expects(1, false, *)
      .returning(Seq(campaignStepWithChildren, campaignStepWithChildren.copy(id = 2, children = List(3, 4)), campaignStepWithChildren.copy(id = 3, children = List(4)), campaignStepWithChildren.copy(id = 4, children = List())))
    //    (campaignStepDAO.getOrderedSteps)
    //    .expects(Seq(campaignStepWithChildren, campaignStepWithChildren.copy(id = 2, children = List(3, 4)), campaignStepWithChildren.copy(id = 3, children = List(4)), campaignStepWithChildren.copy(id = 4)), 1)
    //      .returning(List(campaignStepWithChildren, campaignStepWithChildren.copy(id = 2, children = List(3, 4)), campaignStepWithChildren.copy(id = 3, children = List(4)), campaignStepWithChildren.copy(id = 4)))

    (campaignProspectDAO.findAndMarkCompletedProspects(
      _: List[Long],
      _: TeamId,
      _: Int
    )(using _: SRLogger))
      .expects(List(4L), *, 1, *)
      .returning(Failure(Error))

    emailChannelScheduler.scheduleTasksForChannel(
      channelData = ChannelData.EmailChannelData(emailSettingId = emailSettingId_1),
      teamId = 3L,
      accountService = accountService,
      //accountDAO = accountDAO,
      emailNotificationService = emailNotificationService,
      campaignService = campaignService,
      campaignProspectDAO = campaignProspectDAO,
      campaignStepVariantDAO = campaignStepVariantDAO,
      campaignStepDAO = campaignStepDAO,
      emailServiceCompanion = emailServiceCompanion,
      templateService = templateService,
      taskDAO = taskDAO,
      srShuffleUtils = srShuffleUtils,
      campaignProspectService = campaignProspectService,
      accountOrgBillingRelatedService = accountOrgBillingRelatedService,
      taskService = taskService,
      campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO,
      campaignsMissingMergeTagService = campaignsMissingMergeTagService,
      srRedisSimpleLockServiceV2 = srRedisSimpleLockServiceV2,
      mqWebhookCompleted = mqWebhookCompleted,
        srRollingUpdateCoreService = srRollingUpdateCoreService,
      calendarAppService = calendarAppService

    ).map { result =>
      Logger.info(s"result - $result")

      assert(result == ScheduleTasksData(
        saved_tasks_count = 0,
        latest_task_scheduled_at = None,
        reached_scheduler_step = SchedulerSteps.Other
      ))
    }.recover{
      case e =>
        Logger.info(s"error - $e")
        assert(e.getMessage == "Error")
    }


  }

  "scheduleEmailAccount" should "Error while sendProspectsForValidation" in {

    (srRedisSimpleLockServiceV2.checkLock(
      _: CacheIdKeyForLock
    )(
      using _: SRLogger
    ))
      .expects(*, *)
      .returning(Success(false))
    (campaignProspectService.getCampaignsToLogDripFor()(using _: SRLogger))
      .expects(*)
      .returning(List())
    (emailSettingDAO.findForScheduling)
      .expects(channel_id_1, *)
      .returning(Some(emailSettingCreateEmailSchedule))

    (accountService.find(_: Long)(_: SRLogger))
      .expects(account_id_1, *)
      .returning(Success(accountAdmin))

    (campaignService.findCampaignsForSchedulingEA)
      .expects(emailSettingId_1, *, TeamId(emailSettingCreateEmailSchedule.team_id))
      .returning(Success(Seq(campaignForScheduling/*, campaignForScheduling.copy(id = 2), campaignForScheduling.copy(id = 3), campaignForScheduling.copy(id = 4)*/)))

    (emailSchedulerJedisService.acquireLockAndAddToSetForCampaignScheduling(_: Set[CampaignId], _: Int)(using _: SRLogger))
      .expects(Set(CampaignId(1)), 60 * 30, *)
      .returning(Success(Set(CampaignId(1))))
    (emailSchedulerJedisService.releaseLockForCampaignScheduling(_: Set[CampaignId])(using _: SRLogger))
      .expects(Set(CampaignId(1)), *)
      .returning(Success(Map(true -> Set(CampaignId(1)))))
    (campaignProspectDAO.getSentOrScheduledProspectsCountForEmail)
      .expects(Seq(EmailSettingId(1)),"Asia/Kolkata", *, *, TeamId(emailSettingCreateEmailSchedule.team_id))
      .returning(Success(Map(EmailSettingId(1)->1)))

    (campaignStepDAO.getDistinctStepTypesInCampaigns)
      .expects(List(campaign_id_1))
      .returning(Map(campaign_id_1 -> List(CampaignStepType.AutoEmailStep)))

    (mqWebhookCompleted.publishCompletedProspects (_: Long, _: Long, _: Seq[CPCompleted])(using _: SRLogger))
      .expects(account_id_1, 2, List(), *)
      .returning(Success(0))
    (campaignProspectService.getAllDistinctTimezones(
      _: Long,
      _: Long,
      _: String
    )(
      using _: SRLogger
    ))
      .expects(1, 2, "Asia/Kolkata", *)
      .returning(Success(Set("Asia/Kolkata")))
    (campaignProspectDAO.getScheduledProspectsCountForCampaign)
      .expects(*, Seq(EmailSettingId(1)),2, List((1.toLong,"Asia/Kolkata")/*, (2.toLong,"Asia/Kolkata"), (3.toLong,"Asia/Kolkata"), (4.toLong,"Asia/Kolkata")*/), false)
      .returning(Success(Seq(scheduledProspectsCountForCampaignEmail/*, scheduledProspectsCountForCampaign.copy(campaignId = 2), scheduledProspectsCountForCampaign.copy(campaignId = 3), scheduledProspectsCountForCampaign.copy(campaignId = 4)*/)))

    (campaignStepVariantDAO.findByCampaignIdForSchedule)
      .expects(1, false, *)
      .returning(Seq(campaignStepWithChildren, campaignStepWithChildren.copy(id = 2, children = List(3, 4)), campaignStepWithChildren.copy(id = 3, children = List(4)), campaignStepWithChildren.copy(id = 4, children = List())))
    //    (campaignStepDAO.getOrderedSteps)
    //      .expects(Seq(campaignStepWithChildren, campaignStepWithChildren.copy(id = 2, children = List(3, 4)), campaignStepWithChildren.copy(id = 3, children = List(4)), campaignStepWithChildren.copy(id = 4)), 1)
    //      .returning(List(campaignStepWithChildren, campaignStepWithChildren.copy(id = 2, children = List(3, 4)), campaignStepWithChildren.copy(id = 3, children = List(4)), campaignStepWithChildren.copy(id = 4)))

    (campaignProspectDAO.findAndMarkCompletedProspects(
      _: List[Long],
      _: TeamId,
      _: Int
    )(using _: SRLogger))
      .expects(List(4L), *, 1, *)
      .returning(Success(Seq()))

    (campaignProspectService.getAllDistinctTimezones(
      _: Long,
      _: Long,
      _: String
    )(
      using _: SRLogger
    ))
      .expects(*,*,*,*)
      .returning(Success(Set()))

    val schedulerMapStepIdAndDelay_1 = SchedulerMapStepIdAndDelay(
      is_head_step_in_the_campaign = true,
      currentStepType = api.campaigns.models.CampaignStepType.AutoEmailStep,
      nextStepType    = api.campaigns.models.CampaignStepType.AutoEmailStep,
      currentStepId = 1L,
      delayTillNextStep = 10
    )
    val schedulerMapStepIdAndDelay_2 = SchedulerMapStepIdAndDelay(
      is_head_step_in_the_campaign = false,
      currentStepType = api.campaigns.models.CampaignStepType.AutoEmailStep,
      nextStepType    = api.campaigns.models.CampaignStepType.AutoEmailStep,
      currentStepId = 2L,
      delayTillNextStep = 10
    )
    val schedulerMapStepIdAndDelay_3 =SchedulerMapStepIdAndDelay(
      is_head_step_in_the_campaign = false,
      currentStepType = api.campaigns.models.CampaignStepType.AutoEmailStep,
      nextStepType    = api.campaigns.models.CampaignStepType.AutoEmailStep,
      currentStepId = 3L,
      delayTillNextStep = 10
    )
    //    (campaignProspectDAO.fetchProspectsV2_PEV2(_: DateTime, _: Long, _: Long, _: Int, _: Int, _: Int, _: Vector[SchedulerMapStepIdAndDelay], _: Boolean, _: Option[DateTime])(_:SRLogger))
    //      .expects(*, 1, 2, 96, 100, 1000, Vector(
    //        schedulerMapStepIdAndDelay_1,
    //        schedulerMapStepIdAndDelay_2,
    //        schedulerMapStepIdAndDelay_3
    //      ), false, None, Logger)
    //      .returning(Success(List()))
    //    (campaignProspectDAO.fetchProspectsV2_PEV2(_: DateTime, _: Long, _: Long, _: Int, _: Int, _: Int, _: Vector[SchedulerMapStepIdAndDelay], _: Boolean, _: Option[DateTime])(_:SRLogger))
    //      .expects(*, 1, 2, 96, 100, 1000, Vector(
    //        schedulerMapStepIdAndDelay_1,
    //        schedulerMapStepIdAndDelay_2,
    //        schedulerMapStepIdAndDelay_3
    //      ), true, None, Logger)
    //      .returning(Success(List()))

    val idsOrEmailsForValidation7 = IdsOrEmailsForValidation.EntitiesForValidation(
      emailsForValidations = EmailsForValidationWithInitiator.ProspectEmailsForValidation(
        entitiesForValidation = Seq(),
        initiatorCampaignId = CampaignId(id = 1)
      )
    )

    (emailValidationService.sendProspectsForValidation)
      .expects(EmailValidationPriority.Medium, *, 2, TeamId(2), 1, true, idsOrEmailsForValidation7)
      .returning(Failure(Error))
    (campaignProspectService.getSenderRotationStats(_: CampaignId, _: TeamId, _: Int,  _: DateTime)(using _: SRLogger))
      .expects(CampaignId(1), TeamId(2), 100, *, *)
      .returning(Success(SenderRotationStats(prospects_not_sent_any_emails = 101, prospects_to_get_follow_up = 0)))
    emailChannelScheduler.scheduleTasksForChannel(
      channelData = ChannelData.EmailChannelData(emailSettingId = emailSettingId_1),
      teamId = 3L,
      accountService = accountService,
      //accountDAO = accountDAO,
      emailNotificationService = emailNotificationService,
      campaignService = campaignService,
      campaignProspectDAO = campaignProspectDAO,
      campaignStepVariantDAO = campaignStepVariantDAO,
      campaignStepDAO = campaignStepDAO,
      emailServiceCompanion = emailServiceCompanion,
      taskDAO = taskDAO,
      templateService = templateService,
      srShuffleUtils = srShuffleUtils,
      campaignProspectService = campaignProspectService,
      taskService = taskService,
      campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO,
      campaignsMissingMergeTagService = campaignsMissingMergeTagService,
      srRedisSimpleLockServiceV2 = srRedisSimpleLockServiceV2,
      mqWebhookCompleted = mqWebhookCompleted,
      accountOrgBillingRelatedService = accountOrgBillingRelatedService,
        srRollingUpdateCoreService = srRollingUpdateCoreService,
      calendarAppService = calendarAppService

    ).map { result =>
      Logger.info(s"result - $result")

      assert(result == ScheduleTasksData(
        saved_tasks_count = 0,
        latest_task_scheduled_at = None,
        reached_scheduler_step = SchedulerSteps.Other
      ))
    }.recover{
      case e =>
        Logger.info(s"error - $e")
        assert(e.getMessage == "Error")
    }


  }


  /**
   * 28-feb-2024: not sure why it takes the PostSchedulingError path and not the PostSchedulingSuccess path
   */
  "scheduleEmailAccount" should "NO prospectForSchedule FOUND because no prospect" in {
    (campaignProspectService.getCampaignsToLogDripFor()(using _: SRLogger))
      .expects(*)
      .returning(List())
      .repeat(3)
    (orgMetadataService.getOrgMetadata(_: OrgId))
      .expects(OrgId(id = 1))
      .returning(Success(orgMetadata))

    (srRedisSimpleLockServiceV2.checkLock(
      _: CacheIdKeyForLock
    )(
      using _: SRLogger
    ))
      .expects(*, *)
      .returning(Success(false))

    (emailSettingDAO.findForScheduling)
      .expects(1, *)
      .returning(Some(emailSettingCreateEmailSchedule))

//    (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//      _: TeamId,
//      _: SrRollingUpdateFeature
//    )(_: SRLogger))
//      .expects(TeamId(2),SrRollingUpdateFeature.EmailNotCompulsory,*)
//      .repeat(6)
//      .returning(false)

    (accountService.find(_: Long)(_: SRLogger))
      .expects(1, *)
      .returning(Success(accountAdmin))

    (campaignService.findCampaignsForSchedulingEA)
      .expects(emailSettingId_1, *, TeamId(emailSettingCreateEmailSchedule.team_id))
      .returning(
        Success(
          Seq(
            campaignForScheduling,
            campaignForScheduling
              .copy(
                campaign_id = 2,
                enable_email_validation = false,
                campaign_type_data = CampaignTypeData.MultiChannelCampaignData(head_step_id = 2)
              ),
            campaignForScheduling
              .copy(
                campaign_id = 3,
                campaign_type_data = CampaignTypeData.MultiChannelCampaignData(head_step_id = 3)
              )
          )
        )
      )

    (campaignProspectDAO.getSentOrScheduledProspectsCountForEmail)
      .expects(Seq(EmailSettingId(1)),"Asia/Kolkata", *, *, TeamId(emailSettingCreateEmailSchedule.team_id))
      .returning(Success(Map(EmailSettingId(1)->1)))

    (campaignStepDAO.getDistinctStepTypesInCampaigns)
      .expects(List(1L, 2L, 3L))
      .returning(Map(1L -> List(CampaignStepType.AutoEmailStep), 2L -> List(CampaignStepType.AutoEmailStep), 3L -> List(CampaignStepType.AutoEmailStep)))
    (emailSchedulerJedisService.acquireLockAndAddToSetForCampaignScheduling(_: Set[CampaignId], _: Int)(using _: SRLogger))
      .expects(Set(CampaignId(1), CampaignId(2), CampaignId(3)), 60 * 30, *)
      .returning(Success(Set(CampaignId(1), CampaignId(2), CampaignId(3), CampaignId(4))))
    (emailSchedulerJedisService.releaseLockForCampaignScheduling(_: Set[CampaignId])(using _: SRLogger))
      .expects(Set(CampaignId(1), CampaignId(2), CampaignId(3)), *)
      .returning(Success(Map(true -> Set(CampaignId(1), CampaignId(2), CampaignId(3)))))
    (campaignProspectDAO.getScheduledProspectsCountForCampaign)
      .expects(*, Seq(EmailSettingId(1)), 2, List((1.toLong,"Asia/Kolkata"), (2.toLong,"Asia/Kolkata"), (3.toLong,"Asia/Kolkata")), false)
      .returning(
        Success(
          Seq(
            scheduledProspectsCountForCampaignEmail,
            scheduledProspectsCountForCampaignEmail
              .copy(
                campaignId = 2
              ),
            scheduledProspectsCountForCampaignEmail
              .copy(
                campaignId = 3
              )
          )
        )
      )

    (campaignStepVariantDAO.findByCampaignIdForSchedule)
      .expects(1, false, *)
      .returning(
        Seq(
          campaignStepWithChildren,
          campaignStepWithChildren
            .copy(
              id = 2,
              variants = Seq(
                campaignStepVariantForScheduling.copy(step_id = 2),
                campaignStepVariantForScheduling.copy(id = 2, step_id = 2),
                campaignStepVariantForScheduling.copy(id = 3, step_id = 2)
              ),
              children = List(3)
            ),

          campaignStepWithChildren.copy(id = 3,
            variants = Seq(
              campaignStepVariantForScheduling.copy(step_id = 3),
              campaignStepVariantForScheduling.copy(id = 2, step_id = 3),
              campaignStepVariantForScheduling.copy(id = 3, step_id = 3)
            ),
            children = List()
          )
        )
      )
    //    (campaignStepDAO.getOrderedSteps)
    //      .expects(
    //        Seq(
    //          campaignStepWithChildren,
    //          campaignStepWithChildren
    //            .copy(
    //              id = 2,
    //              variants = Seq(
    //                campaignStepVariantForScheduling.copy(step_id = 2),
    //                campaignStepVariantForScheduling.copy(id = 2, step_id = 2),
    //                campaignStepVariantForScheduling.copy(id = 3, step_id = 2)
    //              )
    //            ),
    //
    //          campaignStepWithChildren.copy(id = 3,
    //            variants = Seq(
    //              campaignStepVariantForScheduling.copy(step_id = 3),
    //              campaignStepVariantForScheduling.copy(id = 2, step_id = 3),
    //              campaignStepVariantForScheduling.copy(id = 3, step_id = 3)
    //            ))
    //        ),
    //        1
    //      )
    //      .returning(
    //        List(
    //            campaignStepWithChildren,
    //            campaignStepWithChildren
    //              .copy(
    //                id = 2,
    //                variants = Seq(
    //                  campaignStepVariantForScheduling.copy(step_id = 2),
    //                  campaignStepVariantForScheduling.copy(id = 2, step_id = 2),
    //                  campaignStepVariantForScheduling.copy(id = 3, step_id = 2)
    //                )
    //              ),
    //
    //            campaignStepWithChildren.copy(id = 3,
    //              variants = Seq(
    //                campaignStepVariantForScheduling.copy(step_id = 3),
    //                campaignStepVariantForScheduling.copy(id = 2, step_id = 3),
    //                campaignStepVariantForScheduling.copy(id = 3, step_id = 3)
    //              ))
    //        )
    //      )

    (campaignProspectDAO.findAndMarkCompletedProspects(
      _: List[Long],
      _: TeamId,
      _: Int
    )(using _: SRLogger))
      .expects(List(3L), *, 1, *)
      .returning(Success(Seq()))


    (campaignProspectService.getAllDistinctTimezones(
      _: Long,
      _: Long,
      _: String
    )(
      using _: SRLogger
    ))
      .expects(*, *, *, *)
      .returning(Success(Set("Asia/Kolkata")))
      .atLeastOnce()

    (campaignProspectService.fetchProspectsV3MultichannelWithEmailOptionalCheck(
      _: ChannelType,
      _: List[String],
      _: Option[Long],
      _: Long,
      _: TeamId,
      _: Int,
      _: Vector[SchedulerMapStepIdAndDelay],
      _: Boolean, _: Boolean,
      _: Option[DateTime],
      _: Boolean,
      _: Option[CampaignEmailSettingForScheduler],
      _: OrgId, _: String)(using _: SRLogger))
      .expects(*, *, *, *, *, *, *, *, *, *, *, *, *, *, *)
      .returning(Success(List(prospectForScheduling)))

    (campaignProspectDAO.filterProspectWhoHaveHoliday(
      _: List[Long],
      _: String,
      _: Option[Long],
      _: DateTime,
      _: Long
    )(using _: SRLogger))
      .expects(*, *, *, *, *, *)
      .returning(Success(Set())) // no prospects have holiday today

    (campaignProspectDAO.filterByProspectValidationStatus(_: Set[Long], _: TeamId)(using _: SRLogger))
      .expects(*, *, *)
      .returning(Success(Set(1)))

    (campaignProspectDAO.filterProspectsBySentCountBasedOnProspectLimit(
      _: Set[Long],
      _: String,
      _: TeamId,
      _: Int,
      _: Int
    )(using _: SRLogger))
      .expects(*, *, *, *, *, *)
      .returning(Success(Set(1)))

    (orgMetadataService.filterProspectsBasedOnDomainLimit)
      .expects(*)
      .returning(Success(true))

    (campaignProspectDAO.mapProspectIdsWithProspectAccountIds)
      .expects(*, *)
      .returning(Success(Map(ProspectId(1L) -> Some(ProspectAccountsId(5L)))))

    (campaignProspectDAO.mapProspectIdsWithProspectAccountIds)
      .expects(*, *)
      .returning(Success(Map()))

    (campaignProspectDAO.getEmailsScheduledInLast24HoursAnd7DaysForAProspectAccount)
      .expects(*, *, *)
      .returning(Success(Map(ProspectAccountsId(5L) -> EmailsScheduledCount(3, 3))))
    (campaignProspectService.getSenderRotationStats(_: CampaignId, _: TeamId, _: Int,  _: DateTime)(using _: SRLogger))
      .expects(CampaignId(1), TeamId(2), 100, *, *)
      .returning(Success(SenderRotationStats(prospects_not_sent_any_emails = 101, prospects_to_get_follow_up = 0)))
    (campaignProspectService.getSenderRotationStats(_: CampaignId, _: TeamId, _: Int,  _: DateTime)(using _: SRLogger))
      .expects(CampaignId(2), TeamId(2), 100, *, *)
      .returning(Success(SenderRotationStats(prospects_not_sent_any_emails = 101, prospects_to_get_follow_up = 0)))
    (campaignProspectService.getSenderRotationStats(_: CampaignId, _: TeamId, _: Int,  _: DateTime)(using _: SRLogger))
      .expects(CampaignId(3), TeamId(2), 100, *, *)
      .returning(Success(SenderRotationStats(prospects_not_sent_any_emails = 101, prospects_to_get_follow_up = 0)))
    (campaignProspectService.fetchProspectsV3MultichannelWithEmailOptionalCheck(
      _: ChannelType,
      _: List[String],
      _: Option[Long],
      _: Long,
      _: TeamId,
      _: Int,
      _: Vector[SchedulerMapStepIdAndDelay],
      _: Boolean, _: Boolean,
      _: Option[DateTime],
      _: Boolean,
      _: Option[CampaignEmailSettingForScheduler],
      _: OrgId, _: String)(using _: SRLogger))
      .expects(*, *, *, *, *, *, *, *, *, *, *, *, *, *, *)
      .returning(Success(List()))

    (campaignProspectDAO.filterProspectWhoHaveHoliday(
      _: List[Long],
      _: String,
      _: Option[Long],
      _: DateTime,
      _: Long
    )(using _: SRLogger))
      .expects(*, *, *, *, *, *)
      .returning(Success(Set())) // no prospects have holiday today

    (campaignProspectDAO.filterByProspectValidationStatus(_: Set[Long], _: TeamId)(using _: SRLogger))
      .expects(*, *, *)
      .returning(Success(Set()))

    (campaignProspectDAO.filterProspectsBySentCountBasedOnProspectLimit(
      _: Set[Long],
      _: String,
      _: TeamId,
      _: Int,
      _: Int
    )(using _: SRLogger))
      .expects(*, *, *, *, *, *)
      .returning(Success(Set()))

    (orgMetadataService.filterProspectsBasedOnDomainLimit)
      .expects(*)
      .returning(Success(true))

    (campaignProspectDAO.mapProspectIdsWithProspectAccountIds(
      _: Set[ProspectId],
      _: TeamId
    ))
      .expects(*, *)
      .returning(Success(Map()))

    (campaignProspectDAO.mapProspectIdsWithProspectAccountIds(
      _: Set[ProspectId],
      _: TeamId
    ))
      .expects(*, *)
      .returning(Success(Map()))

    (campaignProspectDAO.getEmailsScheduledInLast24HoursAnd7DaysForAProspectAccount)
      .expects(*, *, *)
      .returning(Success(Map()))

    //    (campaignProspectDAO.fetchProspectsV2_PEV2(_: DateTime, _: Long, _: Long, _: Int, _: Int, _: Int, _: Vector[SchedulerMapStepIdAndDelay], _: Boolean, _: Option[DateTime])(_:SRLogger))
    //      .expects(*, 1, 2, 96, 100, 1000, Vector(
    //        SchedulerMapStepIdAndDelay(
    //          is_head_step_in_the_campaign = true,
    //          currentStepType = api.campaigns.models.CampaignStepType.AutoEmailStep,
    //          nextStepType    = api.campaigns.models.CampaignStepType.AutoEmailStep,
    //          currentStepId = 1.toLong,
    //          delayTillNextStep = 10
    //        ),
    //        SchedulerMapStepIdAndDelay(
    //          is_head_step_in_the_campaign = false,
    //          currentStepType = api.campaigns.models.CampaignStepType.AutoEmailStep,
    //          nextStepType    = api.campaigns.models.CampaignStepType.AutoEmailStep,
    //          currentStepId = 2.toLong,
    //          delayTillNextStep = 10
    //        )
    //      ), false, None, Logger)
    //      .returning(Success(List(prospectForScheduling)))
    //
    //    (campaignProspectDAO.fetchProspectsV2_PEV2(_: DateTime, _: Long, _: Long, _: Int, _: Int, _: Int, _: Vector[SchedulerMapStepIdAndDelay], _: Boolean, _: Option[DateTime])(_:SRLogger))
    //      .expects(*, 1, 2, 96, 100, 1000, Vector(
    //        SchedulerMapStepIdAndDelay(
    //          is_head_step_in_the_campaign = true,
    //          currentStepType = api.campaigns.models.CampaignStepType.AutoEmailStep,
    //          nextStepType    = api.campaigns.models.CampaignStepType.AutoEmailStep,
    //          currentStepId = 1.toLong,
    //          delayTillNextStep = 10
    //        ),
    //        SchedulerMapStepIdAndDelay(
    //          is_head_step_in_the_campaign = false,
    //          currentStepType = api.campaigns.models.CampaignStepType.AutoEmailStep,
    //          nextStepType    = api.campaigns.models.CampaignStepType.AutoEmailStep,
    //          currentStepId = 2.toLong,
    //          delayTillNextStep = 10
    //        )
    //      ), true, None, Logger)
    //      .returning(Success(List()))


    val res = IdsOrEmailsForValidation.EntitiesForValidation(
      emailsForValidations = EmailsForValidationWithInitiator.ProspectEmailsForValidation(
        entitiesForValidation = Seq(EmailForValidation.fromProspectForValidation(p = prospectForValidation)),
        initiatorCampaignId = CampaignId(id = 1)
      )
    )

    (emailValidationService.sendProspectsForValidation)
      .expects(EmailValidationPriority.Medium, *, 2, TeamId(2), 1, true, res)
      .returning(Success(1))

    (campaignStepVariantDAO.findByCampaignIdForSchedule)
      .expects(2, false, *)
      .returning(
        List(
          campaignStepWithChildren
            .copy(
              id = 2,
              variants = Seq(
                campaignStepVariantForScheduling.copy(step_id = 2),
                campaignStepVariantForScheduling.copy(id = 2, step_id = 2),
                campaignStepVariantForScheduling.copy(id = 3, step_id = 2)
              ),
              children = List(3)
            ),

          campaignStepWithChildren.copy(id = 3,
            variants = Seq(
              campaignStepVariantForScheduling.copy(step_id = 3),
              campaignStepVariantForScheduling.copy(id = 2, step_id = 3),
              campaignStepVariantForScheduling.copy(id = 3, step_id = 3)
            ),
            children = List())
        )
      )


    //    (campaignStepDAO.getOrderedSteps)
    //      .expects(
    //        Seq(
    //          campaignStepWithChildren
    //            .copy(
    //              id = 2,
    //              variants = Seq(
    //                campaignStepVariantForScheduling.copy(step_id = 2),
    //                campaignStepVariantForScheduling.copy(id = 2, step_id = 2),
    //                campaignStepVariantForScheduling.copy(id = 3, step_id = 2)
    //              )
    //            ),
    //
    //          campaignStepWithChildren.copy(id = 3,
    //            variants = Seq(
    //              campaignStepVariantForScheduling.copy(step_id = 3),
    //              campaignStepVariantForScheduling.copy(id = 2, step_id = 3),
    //              campaignStepVariantForScheduling.copy(id = 3, step_id = 3)
    //            ))
    //        ),
    //        1)
    //      .returning(
    //        List(
    //          campaignStepWithChildren
    //            .copy(
    //              id = 2,
    //              variants = Seq(
    //                campaignStepVariantForScheduling.copy(step_id = 2),
    //                campaignStepVariantForScheduling.copy(id = 2, step_id = 2),
    //                campaignStepVariantForScheduling.copy(id = 3, step_id = 2)
    //              )
    //            ),
    //
    //          campaignStepWithChildren.copy(id = 3,
    //            variants = Seq(
    //              campaignStepVariantForScheduling.copy(step_id = 3),
    //              campaignStepVariantForScheduling.copy(id = 2, step_id = 3),
    //              campaignStepVariantForScheduling.copy(id = 3, step_id = 3)
    //            ))
    //        )
    //      )

    (campaignProspectDAO.findAndMarkCompletedProspects(
      _: List[Long],
      _: TeamId,
      _: Int
    )(using _: SRLogger))
      .expects(List(3L), *, 1, *)
      .returning(Success(Seq()))

    (campaignProspectService.fetchProspectsV3MultichannelWithEmailOptionalCheck(
      _: ChannelType,
      _: List[String],
      _: Option[Long],
      _: Long,
      _: TeamId,
      _: Int,
      _: Vector[SchedulerMapStepIdAndDelay],
      _: Boolean, _: Boolean,
      _: Option[DateTime],
      _: Boolean,
      _: Option[CampaignEmailSettingForScheduler],
      _: OrgId, _: String)(using _: SRLogger))
      .expects(*, *, *, *, *, *, *, *, *, *, *, *, *, *, *)
      .returning(Success(List()))

    (campaignProspectDAO.filterProspectWhoHaveHoliday(
      _: List[Long],
      _: String,
      _: Option[Long],
      _: DateTime,
      _: Long
    )(using _: SRLogger))
      .expects(*, *, *, *, *, *)
      .returning(Success(Set())) // no prospects have holiday today

    (campaignProspectDAO.filterByProspectValidationStatus(_: Set[Long], _: TeamId)(using _: SRLogger))
      .expects(*, *, *)
      .returning(Success(Set()))

    (campaignProspectDAO.filterProspectsBySentCountBasedOnProspectLimit(
      _: Set[Long],
      _: String,
      _: TeamId,
      _: Int,
      _: Int
    )(using _: SRLogger))
      .expects(*, *, *, *, *, *)
      .returning(Success(Set()))

    (orgMetadataService.filterProspectsBasedOnDomainLimit)
      .expects(*)
      .returning(Success(true))

    (campaignProspectDAO.mapProspectIdsWithProspectAccountIds(
      _: Set[ProspectId],
      _: TeamId
    ))
      .expects(*, *)
      .returning(Success(Map()))

    (campaignProspectDAO.mapProspectIdsWithProspectAccountIds(
      _: Set[ProspectId],
      _: TeamId
    ))
      .expects(*, *)
      .returning(Success(Map()))

    (campaignProspectDAO.getEmailsScheduledInLast24HoursAnd7DaysForAProspectAccount)
      .expects(*, *, *)
      .returning(Success(Map()))

    (campaignProspectService.fetchProspectsV3MultichannelWithEmailOptionalCheck(
      _: ChannelType,
      _: List[String],
      _: Option[Long],
      _: Long,
      _: TeamId,
      _: Int,
      _: Vector[SchedulerMapStepIdAndDelay],
      _: Boolean, _: Boolean,
      _: Option[DateTime],
      _: Boolean,
      _: Option[CampaignEmailSettingForScheduler],
      _: OrgId, _: String)(using _: SRLogger))
      .expects(*, *, *, *, *, *, *, *, *, *, *, *, *, *, *)
      .returning(Success(List()))

    (campaignProspectDAO.filterProspectWhoHaveHoliday(
      _: List[Long],
      _: String,
      _: Option[Long],
      _: DateTime,
      _: Long
    )(using _: SRLogger))
      .expects(*, *, *, *, *, *)
      .returning(Success(Set())) // no prospects have holiday today

    (campaignProspectDAO.filterByProspectValidationStatus(_: Set[Long], _: TeamId)(using _: SRLogger))
      .expects(*, *, *)
      .returning(Success(Set()))

    (campaignProspectDAO.filterProspectsBySentCountBasedOnProspectLimit(
      _: Set[Long],
      _: String,
      _: TeamId,
      _: Int,
      _: Int
    )(using _: SRLogger))
      .expects(*, *, *, *, *, *)
      .returning(Success(Set()))

    (orgMetadataService.filterProspectsBasedOnDomainLimit)
      .expects(*)
      .returning(Success(true))

    (campaignProspectDAO.mapProspectIdsWithProspectAccountIds(
      _: Set[ProspectId],
      _: TeamId
    ))
      .expects(*, *)
      .returning(Success(Map()))

    (campaignProspectDAO.mapProspectIdsWithProspectAccountIds(
      _: Set[ProspectId],
      _: TeamId
    ))
      .expects(*, *)
      .returning(Success(Map()))

    (campaignProspectDAO.getEmailsScheduledInLast24HoursAnd7DaysForAProspectAccount)
      .expects(*, *, *)
      .returning(Success(Map()))

    //    (campaignProspectDAO.fetchProspectsV2_PEV2(_: DateTime, _: Long, _: Long, _: Int, _: Int, _: Int, _: Vector[SchedulerMapStepIdAndDelay], _: Boolean, _: Option[DateTime])(_:SRLogger))
    //      .expects(*, 2, 2, 96, 100, 1000, Vector(
    //        SchedulerMapStepIdAndDelay(
    //          is_head_step_in_the_campaign = false,
    //          currentStepType = api.campaigns.models.CampaignStepType.AutoEmailStep,
    //          nextStepType    = api.campaigns.models.CampaignStepType.AutoEmailStep,
    //          currentStepId = 2.toLong,
    //          delayTillNextStep = 10
    //        )
    //      ), false, None, Logger)
    //      .returning(Success(List()))
    //
    //    (campaignProspectDAO.fetchProspectsV2_PEV2(_: DateTime, _: Long, _: Long, _: Int, _: Int, _: Int, _: Vector[SchedulerMapStepIdAndDelay], _: Boolean, _: Option[DateTime])(_:SRLogger))
    //      .expects(*, 2, 2, 96, 100, 1000, Vector(
    //        SchedulerMapStepIdAndDelay(
    //          is_head_step_in_the_campaign = false,
    //          currentStepType = api.campaigns.models.CampaignStepType.AutoEmailStep,
    //          nextStepType    = api.campaigns.models.CampaignStepType.AutoEmailStep,
    //          currentStepId = 2.toLong,
    //          delayTillNextStep = 10
    //        )
    //      ), true, None, Logger)
    //      .returning(Success(List()))

    val prospectForValidation2 = IdsOrEmailsForValidation.EntitiesForValidation(
      emailsForValidations = EmailsForValidationWithInitiator.ProspectEmailsForValidation(
        entitiesForValidation = Seq(),
        initiatorCampaignId = CampaignId(id = 2)
      )
    )

    (emailValidationService.sendProspectsForValidation)
      .expects(EmailValidationPriority.Medium, *, 2, TeamId(2), 1, true, prospectForValidation2)
      .returning(Success(1))

    (campaignStepVariantDAO.findByCampaignIdForSchedule)
      .expects(3, false, *)
      .returning(List(
        campaignStepWithChildren.copy(id = 3,
          variants = Seq(
            campaignStepVariantForScheduling.copy(step_id = 3),
            campaignStepVariantForScheduling.copy(id = 2, step_id = 3),
            campaignStepVariantForScheduling.copy(id = 3, step_id = 3)
          ),
          children = List())
      ))

    //    (campaignStepDAO.getOrderedSteps)
    //    .expects(List(), 1)
    //      .returning(List( campaignStepWithChildren.copy(id = 3, children = List())))

    (campaignProspectDAO.findAndMarkCompletedProspects(
      _: List[Long],
      _: TeamId,
      _: Int
    )(using _: SRLogger))
      .expects(List(3L), *, 1, *)
      .returning(Success(Seq()))

    (campaignProspectService.fetchProspectsV3MultichannelWithEmailOptionalCheck(
      _: ChannelType,
      _: List[String],
      _: Option[Long],
      _: Long,
      _: TeamId,
      _: Int,
      _: Vector[SchedulerMapStepIdAndDelay],
      _: Boolean, _: Boolean,
      _: Option[DateTime],
      _: Boolean,
      _: Option[CampaignEmailSettingForScheduler],
      _: OrgId, _: String)(using _: SRLogger))
      .expects(*, *, *, *, *, *, *, *, *, *, *, *, *, *, *)
      .returning(Success(List()))

    (campaignProspectDAO.filterProspectWhoHaveHoliday(
      _: List[Long],
      _: String,
      _: Option[Long],
      _: DateTime,
      _: Long
    )(using _: SRLogger))
      .expects(*, *, *, *, *, *)
      .returning(Success(Set())) // no prospects have holiday today

    (campaignProspectDAO.filterByProspectValidationStatus(_: Set[Long], _: TeamId)(using _: SRLogger))
      .expects(*, *, *)
      .returning(Success(Set()))

    (campaignProspectDAO.filterProspectsBySentCountBasedOnProspectLimit(
      _: Set[Long],
      _: String,
      _: TeamId,
      _: Int,
      _: Int
    )(using _: SRLogger))
      .expects(*, *, *, *, *, *)
      .returning(Success(Set()))

    (orgMetadataService.filterProspectsBasedOnDomainLimit)
      .expects(*)
      .returning(Success(true))

    (campaignProspectDAO.mapProspectIdsWithProspectAccountIds(
      _: Set[ProspectId],
      _: TeamId
    ))
      .expects(*, *)
      .returning(Success(Map()))

    (campaignProspectDAO.mapProspectIdsWithProspectAccountIds(
      _: Set[ProspectId],
      _: TeamId
    ))
      .expects(*, *)
      .returning(Success(Map()))

    (campaignProspectDAO.getEmailsScheduledInLast24HoursAnd7DaysForAProspectAccount)
      .expects(*, *, *)
      .returning(Success(Map()))

    (campaignProspectService.fetchProspectsV3MultichannelWithEmailOptionalCheck(
      _: ChannelType,
      _: List[String],
      _: Option[Long],
      _: Long,
      _: TeamId,
      _: Int,
      _: Vector[SchedulerMapStepIdAndDelay],
      _: Boolean, _: Boolean,
      _: Option[DateTime],
      _: Boolean,
      _: Option[CampaignEmailSettingForScheduler],
      _: OrgId, _: String)(using _: SRLogger))
      .expects(*, *, *, *, *, *, *, *, *, *, *, *, *, *, *)
      .returning(Success(List()))

    (campaignProspectDAO.filterProspectWhoHaveHoliday(
      _: List[Long],
      _: String,
      _: Option[Long],
      _: DateTime,
      _: Long
    )(using _: SRLogger))
      .expects(*, *, *, *, *, *)
      .returning(Success(Set())) // no prospects have holiday today

    (campaignProspectDAO.filterByProspectValidationStatus(_: Set[Long], _: TeamId)(using _: SRLogger))
      .expects(*, *, *)
      .returning(Success(Set()))

    (campaignProspectDAO.filterProspectsBySentCountBasedOnProspectLimit(
      _: Set[Long],
      _: String,
      _: TeamId,
      _: Int,
      _: Int
    )(using _: SRLogger))
      .expects(*, *, *, *, *, *)
      .returning(Success(Set()))

    (orgMetadataService.filterProspectsBasedOnDomainLimit)
      .expects(*)
      .returning(Success(true))

    (campaignProspectDAO.mapProspectIdsWithProspectAccountIds(
      _: Set[ProspectId],
      _: TeamId
    ))
      .expects(*, *)
      .returning(Success(Map()))

    (campaignProspectDAO.mapProspectIdsWithProspectAccountIds(
      _: Set[ProspectId],
      _: TeamId
    ))
      .expects(*, *)
      .returning(Success(Map()))

    (campaignProspectDAO.getEmailsScheduledInLast24HoursAnd7DaysForAProspectAccount)
      .expects(*, *, *)
      .returning(Success(Map()))

    //    (campaignProspectDAO.fetchProspectsV2_PEV2(_: DateTime, _: Long, _: Long, _: Int, _: Int, _: Int, _: Vector[SchedulerMapStepIdAndDelay], _: Boolean, _: Option[DateTime])(_:SRLogger))
    //      .expects(*, 3, 2, 96, 100, 1000, Vector(), false, None, Logger)
    //      .returning(Success(List()))
    //
    //    (campaignProspectDAO.fetchProspectsV2_PEV2(_: DateTime, _: Long, _: Long, _: Int, _: Int, _: Int, _: Vector[SchedulerMapStepIdAndDelay], _: Boolean, _: Option[DateTime])(_: SRLogger))
    //      .expects(*, 3, 2, 96, 100, 1000, Vector(), true, None, Logger)
    //      .returning(Success(List()))

    val prospectForValidation3 = IdsOrEmailsForValidation.EntitiesForValidation(
      emailsForValidations = EmailsForValidationWithInitiator.ProspectEmailsForValidation(
        entitiesForValidation = Seq(),
        initiatorCampaignId = CampaignId(id = 3)
      )
    )

    (emailValidationService.sendProspectsForValidation)
      .expects(EmailValidationPriority.Medium, *, 2, TeamId(2), 1, true, prospectForValidation3)
      .returning(Success(0))

    (mqWebhookCompleted.publishCompletedProspects (_: Long, _: Long, _: Seq[CPCompleted])(using _: SRLogger))
      .expects(1, 2, List(), *)
      .returning(Success(0))
    (mqWebhookCompleted.publishCompletedProspects (_: Long, _: Long, _: Seq[CPCompleted])(using _: SRLogger))
      .expects(1, 2, List(), *)
      .returning(Success(0))
    (mqWebhookCompleted.publishCompletedProspects (_: Long, _: Long, _: Seq[CPCompleted])(using _: SRLogger))
      .expects(1, 2, List(), *)
      .returning(Success(0))

    //   ( emailScheduledDAO.saveEmailsToBeScheduledAndUpdateCampaignDataV2)
    //     .expects(
    //       Vector(), 123,
    //       *)
    //     .returning(Success(Seq(emailScheduledNewAfterSaving)))

    //    (emailScheduledDAO.addBodyToEmailsToBeScheduled)
    //      .expects(List(), *)
    //      .returning(Seq())
    (campaignProspectDAO._updateScheduledStatus(_: Seq[CampaignProspectUpdateScheduleStatus])(using _:SRLogger))
      .expects(List(), *)
      .returning(Success(Seq()))
    //
    //    (() => repTrackingHostService.getRepTrackingHosts())
    //      .expects()
    //      .returning(Success(Seq()))

    emailChannelScheduler.scheduleTasksForChannel(
      channelData = ChannelData.EmailChannelData(emailSettingId = emailSettingId_1),
      teamId = 3L,
      accountService = accountService,
      //accountDAO = accountDAO,
      emailNotificationService = emailNotificationService,
      campaignService = campaignService,
      campaignProspectDAO = campaignProspectDAO,
      campaignStepVariantDAO = campaignStepVariantDAO,
      campaignStepDAO = campaignStepDAO,
      emailServiceCompanion = emailServiceCompanion,
      templateService = templateService,
      taskDAO = taskDAO,
      srShuffleUtils = srShuffleUtils,
      campaignProspectService = campaignProspectService,
      taskService = taskService,
      campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO,
      campaignsMissingMergeTagService = campaignsMissingMergeTagService,
      srRedisSimpleLockServiceV2 = srRedisSimpleLockServiceV2,
      mqWebhookCompleted = mqWebhookCompleted,
      accountOrgBillingRelatedService = accountOrgBillingRelatedService,
        srRollingUpdateCoreService = srRollingUpdateCoreService,
      calendarAppService = calendarAppService
    ).map { result =>
      Logger.info(s"result - $result")
      assert(result == ScheduleTasksData(
        saved_tasks_count = 0,
        latest_task_scheduled_at = None,
        reached_scheduler_step = SchedulerSteps.PostSchedulingError
      ))
    }.recover{
      case e =>
        Logger.info(s"error - $e")
        assert(e.getMessage == "NO prospectForSchedule found for campaign_id 1 prospect_id 1")
    }


  }




  // TODO: Fix this test ;test was wrong, we were creating an entry in email_scheduled without vector EmailScheduledNew
  //  "scheduleEmailAccount with append followup success (silent)" should "success creating and adding emails" in {
  //
  //    (srRedisSimpleLockServiceV2.checkLock(
  //      _: CacheIdKeyForLock
  //    )(
  //      _: SRLogger
  //    ))
  //      .expects(*, *)
  //      .returning(Success(false))
  //
  //    (emailSettingDAO.findForScheduling)
  //      .expects(channel_id_1, *)
  //      .returning(Some(emailSettingCreateEmailSchedule))
  //
  //
  //    (accountService.find(_: Long)(_: SRLogger))
  //      .expects(account_id_1, *)
  //      .returning(Success(accountAdmin))
  //
  //
  //    (campaignService.findCampaignsForSchedulingEA)
  //      .expects(emailSettingId_1, *, TeamId(emailSettingCreateEmailSchedule.team_id))
  //      .returning(
  //        Success(
  //          Seq(
  //            campaignForScheduling.copy(
  //              enable_email_validation = false,
  //              append_followups = true
  //            ),
  //            campaignForScheduling
  //              .copy(
  //                campaign_id = 2,
  //                enable_email_validation = false,
  //                append_followups = true
  //              ),
  //            campaignForScheduling
  //              .copy(
  //                campaign_id = 3,
  //                enable_email_validation = false
  //              )
  //          )
  //        )
  //      )
  //
  //
  //    (campaignProspectDAO.getSentOrScheduledProspectsCountForEmail)
  //      .expects(Seq(EmailSettingId(1)), "Asia/Kolkata", *, *)
  //      .returning(Success(Map(EmailSettingId(1) -> 1)))
  //
  //    (campaignStepDAO.getDistinctStepTypesInCampaigns)
  //      .expects(List(1L, 2L, 3L))
  //      .returning(Map(1L -> List(CampaignStepType.AutoEmailStep), 2L -> List(CampaignStepType.AutoEmailStep), 3L -> List(CampaignStepType.AutoEmailStep)))
  //
  //    // ====
  //
  //    (campaignProspectDAO.getScheduledProspectsCountForCampaign)
  //      .expects(*, Seq(EmailSettingId(1)), 2, List((1.toLong, "Asia/Kolkata"), (2.toLong, "Asia/Kolkata"), (3.toLong, "Asia/Kolkata")), false)
  //      .returning(
  //        Success(
  //          Seq(
  //            scheduledProspectsCountForCampaignEmail,
  //            scheduledProspectsCountForCampaignEmail
  //              .copy(
  //                campaignId = 2
  //              ),
  //            scheduledProspectsCountForCampaignEmail
  //              .copy(
  //                campaignId = 3
  //              )
  //          )
  //        )
  //      )
  //
  //    val campaignStepWithChildren_1 = campaignStepWithChildren
  //    val campaignStepWithChildren_2 = campaignStepWithChildren
  //      .copy(
  //        id = 2,
  //        variants = Seq(
  //          campaignStepVariantForScheduling.copy(step_id = 2),
  //          campaignStepVariantForScheduling.copy(id = 2, step_id = 2),
  //          campaignStepVariantForScheduling.copy(id = 3, step_id = 2)
  //        ),
  //        children = List(3)
  //      )
  //    val campaignStepWithChildren_3 = campaignStepWithChildren.copy(id = 3,
  //      variants = Seq(
  //        campaignStepVariantForScheduling.copy(step_id = 3),
  //        campaignStepVariantForScheduling.copy(id = 2, step_id = 3),
  //        campaignStepVariantForScheduling.copy(id = 3, step_id = 3)
  //      ),
  //      children = List()
  //    )
  //
  //
  //    (campaignStepVariantDAO.findByCampaignIdForSchedule)
  //      .expects(1, false, *)
  //      .returning(
  //        Seq(
  //          campaignStepWithChildren_1,
  //          campaignStepWithChildren_2,
  //          campaignStepWithChildren_3
  //        )
  //      )
  //    //    (campaignStepDAO.getOrderedSteps)
  //    //      .expects(
  //    //        Seq(
  //    //          campaignStepWithChildren_1,
  //    //          campaignStepWithChildren_2,
  //    //          campaignStepWithChildren_3
  //    //        ),
  //    //        1
  //    //      )
  //    //      .returning(
  //    //        List(
  //    //          campaignStepWithChildren_1,
  //    //          campaignStepWithChildren_2,
  //    //          campaignStepWithChildren_3
  //    //        )
  //    //      )
  //
  //    (campaignProspectDAO.findAndMarkCompletedProspects)
  //      .expects(3, 1)
  //      .returning(Success(Seq()))
  //
  //    (campaignProspectService.getAllDistinctTimezones(
  //      _: Long,
  //      _: Long,
  //      _: String
  //    )(
  //      _: SRLogger
  //    ))
  //      .expects(*, *, *, *)
  //      .returning(Success(List("Asia/Kolkata")))
  //      .atLeastOnce()
  //
  //    //    (emailScheduledDAO.getPreviousSentSteps)
  //    //    .expects(3, 1)
  //    //      .returning(Success(Seq(previousFollowUp)))
  //    //    (emailScheduledDAO.getPreviousSentSteps)
  //    //      .expects(2, 1)
  //    //      .returning(Success(Seq(previousFollowUp)))
  //
  //    (campaignProspectDAO.fetchProspectsV3Multichannel(
  //      _: ChannelType,
  //      _: List[String],
  //      _: Option[Long],
  //      _: Long,
  //      _: Long,
  //      _: Int,
  //      _: Vector[SchedulerMapStepIdAndDelay],
  //      _: Boolean,
  //      _: Option[DateTime])(_: SRLogger))
  //      .expects(*, *, *, *, *, *, *, *, *, *)
  //      .returning(Success(List(prospectForScheduling)))
  //
  //    (campaignProspectDAO.filterProspectWhoHaveHoliday(
  //      _: List[Long],
  //      _: String,
  //      _: Option[Long],
  //      _: DateTime,
  //      _: Long
  //    )(_: SRLogger))
  //      .expects(*, *, *, *, *, *)
  //      .returning(Success(Set())) // no prospects have holiday today
  //
  //    (campaignProspectDAO.filterByProspectValidationStatus(_: Set[Long], _: Long)(_: SRLogger))
  //      .expects(*, *, *)
  //      .returning(Success(Set(1)))
  //
  //    (campaignProspectDAO.filterProspectsBySentCountBasedOnProspectLimit(
  //      _: Set[Long],
  //      _: String,
  //      _: Long,
  //      _: Int,
  //      _: Int
  //    )(_: SRLogger))
  //      .expects(*, *, *, *, *, *)
  //      .returning(Success(Set(1)))
  //
  //    (orgMetadataService.filterProspectsBasedOnDomainLimit)
  //      .expects(*)
  //      .returning(Success(true))
  //
  //    (campaignProspectDAO.mapProspectIdsWithProspectAccountIds(
  //      _: Set[ProspectId],
  //      _: TeamId
  //    ))
  //      .expects(*, *)
  //      .returning(Success(Map(ProspectId(1L) -> Some(ProspectAccountsId(5L)))))
  //
  //    (campaignProspectDAO.getEmailsScheduledInLast24HoursAnd7DaysForAProspectAccount(
  //      _: List[ProspectAccountsId],
  //      _: String,
  //      _: TeamId
  //    ))
  //      .expects(*, *, *)
  //      .returning(Success(Map(ProspectAccountsId(5L) -> EmailsScheduledCount(3, 3))))
  //
  //    (campaignProspectDAO.fetchProspectsV3Multichannel(
  //      _: ChannelType,
  //      _: List[String],
  //      _: Option[Long],
  //      _: Long,
  //      _: Long,
  //      _: Int,
  //      _: Vector[SchedulerMapStepIdAndDelay],
  //      _: Boolean,
  //      _: Option[DateTime])(_: SRLogger))
  //      .expects(*, *, *, *, *, *, *, *, *, *)
  //      .returning(Success(List()))
  //
  //    (campaignProspectDAO.filterProspectWhoHaveHoliday(
  //      _: List[Long],
  //      _: String,
  //      _: Option[Long],
  //      _: DateTime,
  //      _: Long
  //    )(_: SRLogger))
  //      .expects(*, *, *, *, *, *)
  //      .returning(Success(Set())) // no prospects have holiday today
  //
  //    (campaignProspectDAO.filterByProspectValidationStatus(_: Set[Long], _: Long)(_: SRLogger))
  //      .expects(*, *, *)
  //      .returning(Success(Set()))
  //
  //    (campaignProspectDAO.filterProspectsBySentCountBasedOnProspectLimit(
  //      _: Set[Long],
  //      _: String,
  //      _: Long,
  //      _: Int,
  //      _: Int
  //    )(_: SRLogger))
  //      .expects(*, *, *, *, *, *)
  //      .returning(Success(Set()))
  //
  //    (orgMetadataService.filterProspectsBasedOnDomainLimit)
  //      .expects(*)
  //      .returning(Success(true))
  //
  //    (campaignProspectDAO.mapProspectIdsWithProspectAccountIds(
  //      _: Set[ProspectId],
  //      _: TeamId
  //    ))
  //      .expects(*, *)
  //      .returning(Success(Map()))
  //
  //    (campaignProspectDAO.getEmailsScheduledInLast24HoursAnd7DaysForAProspectAccount)
  //      .expects(*, *, *)
  //      .returning(Success(Map()))
  //
  //    //    (campaignProspectDAO.fetchProspectsV2_PEV2(_: DateTime, _: Long, _: Long, _: Int, _: Int, _: Int, _: Vector[SchedulerMapStepIdAndDelay], _: Boolean, _: Option[DateTime])(_:SRLogger))
  //    //      .expects(*, 1, 2, 96, 100, 1000, Vector(
  //    //        SchedulerMapStepIdAndDelay(
  //    //          is_head_step_in_the_campaign = true,
  //    //          currentStepType = api.campaigns.models.CampaignStepType.AutoEmailStep,
  //    //          nextStepType    = api.campaigns.models.CampaignStepType.AutoEmailStep,
  //    //          currentStepId = 1.toLong,
  //    //          delayTillNextStep = 10
  //    //        ),
  //    //        SchedulerMapStepIdAndDelay(
  //    //          is_head_step_in_the_campaign = false,
  //    //          currentStepType = api.campaigns.models.CampaignStepType.AutoEmailStep,
  //    //          nextStepType    = api.campaigns.models.CampaignStepType.AutoEmailStep,
  //    //          currentStepId = 2.toLong,
  //    //          delayTillNextStep = 10
  //    //        )
  //    //      ), false, None, *)
  //    //      .returning(Success(List(prospectForScheduling)))
  //    //
  //    //    (campaignProspectDAO.fetchProspectsV2_PEV2(_: DateTime, _: Long, _: Long, _: Int, _: Int, _: Int, _: Vector[SchedulerMapStepIdAndDelay], _: Boolean, _: Option[DateTime])(_:SRLogger))
  //    //      .expects(*, 1, 2, 96, 100, 1000, Vector(
  //    //        SchedulerMapStepIdAndDelay(
  //    //          is_head_step_in_the_campaign = true,
  //    //          currentStepType = api.campaigns.models.CampaignStepType.AutoEmailStep,
  //    //          nextStepType    = api.campaigns.models.CampaignStepType.AutoEmailStep,
  //    //          currentStepId = 1.toLong,
  //    //          delayTillNextStep = 10
  //    //        ),
  //    //        SchedulerMapStepIdAndDelay(
  //    //          is_head_step_in_the_campaign = false,
  //    //          currentStepType = api.campaigns.models.CampaignStepType.AutoEmailStep,
  //    //          nextStepType    = api.campaigns.models.CampaignStepType.AutoEmailStep,
  //    //          currentStepId = 2.toLong,
  //    //          delayTillNextStep = 10
  //    //        )
  //    //      ), true, None, Logger)
  //    //      .returning(Success(List()))
  //
  //
  //    (emailValidationService.sendProspectsForValidation)
  //      .expects(*, 2, 2, 1, true, List(), List(), 1)
  //      .returning(Success(1))
  //
  //    (campaignStepVariantDAO.findByCampaignIdForSchedule)
  //      .expects(2, false, *)
  //      .returning(
  //        Seq(
  //          campaignStepWithChildren,
  //          campaignStepWithChildren
  //            .copy(
  //              id = 2,
  //              variants = Seq(
  //                campaignStepVariantForScheduling.copy(step_id = 2),
  //                campaignStepVariantForScheduling.copy(id = 2, step_id = 2),
  //                campaignStepVariantForScheduling.copy(id = 3, step_id = 2)
  //              ),
  //              children = List(3)
  //            ),
  //
  //          campaignStepWithChildren.copy(id = 3,
  //            variants = Seq(
  //              campaignStepVariantForScheduling.copy(step_id = 3),
  //              campaignStepVariantForScheduling.copy(id = 2, step_id = 3),
  //              campaignStepVariantForScheduling.copy(id = 3, step_id = 3)
  //            ),
  //            children = List())
  //        )
  //      )
  //    //    (campaignStepDAO.getOrderedSteps)
  //    //      .expects(        Seq(
  //    //        campaignStepWithChildren,
  //    //        campaignStepWithChildren
  //    //          .copy(
  //    //            id = 2,
  //    //            variants = Seq(
  //    //              campaignStepVariantForScheduling.copy(step_id = 2),
  //    //              campaignStepVariantForScheduling.copy(id = 2, step_id = 2),
  //    //              campaignStepVariantForScheduling.copy(id = 3, step_id = 2)
  //    //            )
  //    //          ),
  //    //
  //    //        campaignStepWithChildren.copy(id = 3,
  //    //          variants = Seq(
  //    //            campaignStepVariantForScheduling.copy(step_id = 3),
  //    //            campaignStepVariantForScheduling.copy(id = 2, step_id = 3),
  //    //            campaignStepVariantForScheduling.copy(id = 3, step_id = 3)
  //    //          ))
  //    //      ), 1)
  //    //      .returning(List( campaignStepWithChildren.copy(id = 3, children = List())))
  //
  //    (campaignProspectDAO.findAndMarkCompletedProspects)
  //      .expects(3, 1)
  //      .returning(Success(Seq()))
  //
  //    (campaignProspectDAO.fetchProspectsV3Multichannel(
  //      _: ChannelType,
  //      _: List[String],
  //      _: Option[Long],
  //      _: Long,
  //      _: Long,
  //      _: Int,
  //      _: Vector[SchedulerMapStepIdAndDelay],
  //      _: Boolean,
  //      _: Option[DateTime])(_: SRLogger))
  //      .expects(*, *, *, *, *, *, *, *, *, *)
  //      .returning(Success(List(prospectForScheduling)))
  //
  //    (campaignProspectDAO.filterProspectWhoHaveHoliday(
  //      _: List[Long],
  //      _: String,
  //      _: Option[Long],
  //      _: DateTime,
  //      _: Long
  //    )(_: SRLogger))
  //      .expects(*, *, *, *, *, *)
  //      .returning(Success(Set())) // no prospects have holiday today
  //
  //    (campaignProspectDAO.filterByProspectValidationStatus(_: Set[Long], _: Long)(_: SRLogger))
  //      .expects(*, *, *)
  //      .returning(Success(Set(1)))
  //
  //    (campaignProspectDAO.filterProspectsBySentCountBasedOnProspectLimit(
  //      _: Set[Long],
  //      _: String,
  //      _: Long,
  //      _: Int,
  //      _: Int
  //    )(_: SRLogger))
  //      .expects(*, *, *, *, *, *)
  //      .returning(Success(Set(1)))
  //
  //    (orgMetadataService.filterProspectsBasedOnDomainLimit)
  //      .expects(*)
  //      .returning(Success(true))
  //
  //    (campaignProspectDAO.mapProspectIdsWithProspectAccountIds(
  //      _: Set[ProspectId],
  //      _: TeamId
  //    ))
  //      .expects(*, *)
  //      .returning(Success(Map(ProspectId(1L) -> Some(ProspectAccountsId(5L)))))
  //
  //    (campaignProspectDAO.getEmailsScheduledInLast24HoursAnd7DaysForAProspectAccount(
  //      _: List[ProspectAccountsId],
  //      _: String,
  //      _: TeamId
  //    ))
  //      .expects(*, *, *)
  //      .returning(Success(Map(ProspectAccountsId(5L) -> EmailsScheduledCount(3, 3))))
  //
  //    (campaignProspectDAO.fetchProspectsV3Multichannel(
  //      _: ChannelType,
  //      _: List[String],
  //      _: Option[Long],
  //      _: Long,
  //      _: Long,
  //      _: Int,
  //      _: Vector[SchedulerMapStepIdAndDelay],
  //      _: Boolean,
  //      _: Option[DateTime])(_: SRLogger))
  //      .expects(*, *, *, *, *, *, *, *, *, *)
  //      .returning(Success(List()))
  //
  //    (campaignProspectDAO.filterProspectWhoHaveHoliday(
  //      _: List[Long],
  //      _: String,
  //      _: Option[Long],
  //      _: DateTime,
  //      _: Long
  //    )(_: SRLogger))
  //      .expects(*, *, *, *, *, *)
  //      .returning(Success(Set())) // no prospects have holiday today
  //
  //    (campaignProspectDAO.filterByProspectValidationStatus(_: Set[Long], _: Long)(_: SRLogger))
  //      .expects(*, *, *)
  //      .returning(Success(Set()))
  //
  //    (campaignProspectDAO.filterProspectsBySentCountBasedOnProspectLimit(
  //      _: Set[Long],
  //      _: String,
  //      _: Long,
  //      _: Int,
  //      _: Int
  //    )(_: SRLogger))
  //      .expects(*, *, *, *, *, *)
  //      .returning(Success(Set()))
  //
  //    (orgMetadataService.filterProspectsBasedOnDomainLimit)
  //      .expects(*)
  //      .returning(Success(true))
  //
  //    (campaignProspectDAO.mapProspectIdsWithProspectAccountIds(
  //      _: Set[ProspectId],
  //      _: TeamId
  //    ))
  //      .expects(*, *)
  //      .returning(Success(Map()))
  //
  //    (campaignProspectDAO.getEmailsScheduledInLast24HoursAnd7DaysForAProspectAccount)
  //      .expects(*, *, *)
  //      .returning(Success(Map()))
  //
  //    //    (campaignProspectDAO.fetchProspectsV2_PEV2(_: DateTime, _: Long, _: Long, _: Int, _: Int, _: Int, _: Vector[SchedulerMapStepIdAndDelay], _: Boolean, _: Option[DateTime])(_:SRLogger))
  //    //    .expects(*, 2, 2, 96, 100, 1000, Vector(
  //    //      SchedulerMapStepIdAndDelay(
  //    //        is_head_step_in_the_campaign = true,
  //    //        currentStepType = api.campaigns.models.CampaignStepType.AutoEmailStep,
  //    //        nextStepType    = api.campaigns.models.CampaignStepType.AutoEmailStep,
  //    //        currentStepId = 1L,
  //    //        delayTillNextStep = 10
  //    //      ),
  //    //      SchedulerMapStepIdAndDelay(
  //    //        is_head_step_in_the_campaign = false,
  //    //        currentStepType = api.campaigns.models.CampaignStepType.AutoEmailStep,
  //    //        nextStepType    = api.campaigns.models.CampaignStepType.AutoEmailStep,
  //    //        currentStepId = 2L,
  //    //        delayTillNextStep = 10
  //    //      )
  //    //    ), false, None, Logger)
  //    //      .returning(Success(List(prospectForScheduling)))
  //    //
  //    //    (campaignProspectDAO.fetchProspectsV2_PEV2(_: DateTime, _: Long, _: Long, _: Int, _: Int, _: Int, _: Vector[SchedulerMapStepIdAndDelay], _: Boolean, _: Option[DateTime])(_:SRLogger))
  //    //      .expects(*, 2, 2, 96, 100, 1000, Vector(
  //    //        SchedulerMapStepIdAndDelay(
  //    //          is_head_step_in_the_campaign = true,
  //    //          currentStepType = api.campaigns.models.CampaignStepType.AutoEmailStep,
  //    //          nextStepType    = api.campaigns.models.CampaignStepType.AutoEmailStep,
  //    //          currentStepId = 1L,
  //    //          delayTillNextStep = 10
  //    //        ),
  //    //        SchedulerMapStepIdAndDelay(
  //    //          is_head_step_in_the_campaign = false,
  //    //          currentStepType = api.campaigns.models.CampaignStepType.AutoEmailStep,
  //    //          nextStepType    = api.campaigns.models.CampaignStepType.AutoEmailStep,
  //    //          currentStepId = 2L,
  //    //          delayTillNextStep = 10
  //    //        )
  //    //      ), true, None, Logger)
  //    //      .returning(Success(List()))
  //
  //    (emailValidationService.sendProspectsForValidation)
  //      .expects(*, 2, 2, 1, true, List(), List(), 2)
  //      .returning(Success(1))
  //    (campaignStepVariantDAO.findByCampaignIdForSchedule)
  //      .expects(3, false, *)
  //      .returning(
  //        Seq(
  //          campaignStepWithChildren,
  //          campaignStepWithChildren
  //            .copy(
  //              id = 2,
  //              variants = Seq(
  //                campaignStepVariantForScheduling.copy(step_id = 2),
  //                campaignStepVariantForScheduling.copy(id = 2, step_id = 2),
  //                campaignStepVariantForScheduling.copy(id = 3, step_id = 2)
  //              ),
  //              children = List(3)
  //            ),
  //
  //          campaignStepWithChildren.copy(id = 3,
  //            variants = Seq(
  //              campaignStepVariantForScheduling.copy(step_id = 3),
  //              campaignStepVariantForScheduling.copy(id = 2, step_id = 3),
  //              campaignStepVariantForScheduling.copy(id = 3, step_id = 3)
  //            ),
  //            children = List())
  //        ))
  //    //    (campaignStepDAO.getOrderedSteps)
  //    //      .expects(
  //    //        Seq(
  //    //        campaignStepWithChildren,
  //    //        campaignStepWithChildren
  //    //          .copy(
  //    //            id = 2,
  //    //            variants = Seq(
  //    //              campaignStepVariantForScheduling.copy(step_id = 2),
  //    //              campaignStepVariantForScheduling.copy(id = 2, step_id = 2),
  //    //              campaignStepVariantForScheduling.copy(id = 3, step_id = 2)
  //    //            )
  //    //          ),
  //    //
  //    //        campaignStepWithChildren.copy(id = 3,
  //    //          variants = Seq(
  //    //            campaignStepVariantForScheduling.copy(step_id = 3),
  //    //            campaignStepVariantForScheduling.copy(id = 2, step_id = 3),
  //    //            campaignStepVariantForScheduling.copy(id = 3, step_id = 3)
  //    //          ))
  //    //      ), 1)
  //    //      .returning(List( campaignStepWithChildren.copy(id = 3, children = List())))
  //
  //    (campaignProspectDAO.findAndMarkCompletedProspects)
  //      .expects(3, 1)
  //      .returning(Success(Seq()))
  //
  //    (campaignProspectDAO.fetchProspectsV3Multichannel(
  //      _: ChannelType,
  //      _: List[String],
  //      _: Option[Long],
  //      _: Long,
  //      _: Long,
  //      _: Int,
  //      _: Vector[SchedulerMapStepIdAndDelay],
  //      _: Boolean,
  //      _: Option[DateTime])(_: SRLogger))
  //      .expects(*, *, *, *, *, *, *, *, *, *)
  //      .returning(Success(List(prospectForScheduling)))
  //
  //    (campaignProspectDAO.filterProspectWhoHaveHoliday(
  //      _: List[Long],
  //      _: String,
  //      _: Option[Long],
  //      _: DateTime,
  //      _: Long
  //    )(_: SRLogger))
  //      .expects(*, *, *, *, *, *)
  //      .returning(Success(Set())) // no prospects have holiday today
  //
  //    (campaignProspectDAO.filterByProspectValidationStatus(_: Set[Long], _: Long)(_: SRLogger))
  //      .expects(*, *, *)
  //      .returning(Success(Set(1)))
  //
  //    (campaignProspectDAO.filterProspectsBySentCountBasedOnProspectLimit(
  //      _: Set[Long],
  //      _: String,
  //      _: Long,
  //      _: Int,
  //      _: Int
  //    )(_: SRLogger))
  //      .expects(*, *, *, *, *, *)
  //      .returning(Success(Set(1)))
  //
  //    (orgMetadataService.filterProspectsBasedOnDomainLimit)
  //      .expects(*)
  //      .returning(Success(true))
  //
  //    (campaignProspectDAO.mapProspectIdsWithProspectAccountIds(
  //      _: Set[ProspectId],
  //      _: TeamId
  //    ))
  //      .expects(*, *)
  //      .returning(Success(Map(ProspectId(1L) -> Some(ProspectAccountsId(5L)))))
  //
  //    (campaignProspectDAO.getEmailsScheduledInLast24HoursAnd7DaysForAProspectAccount(
  //      _: List[ProspectAccountsId],
  //      _: String,
  //      _: TeamId
  //    ))
  //      .expects(*, *, *)
  //      .returning(Success(Map(ProspectAccountsId(5L) -> EmailsScheduledCount(3, 3))))
  //
  //    (campaignProspectDAO.fetchProspectsV3Multichannel(
  //      _: ChannelType,
  //      _: List[String],
  //      _: Option[Long],
  //      _: Long,
  //      _: Long,
  //      _: Int,
  //      _: Vector[SchedulerMapStepIdAndDelay],
  //      _: Boolean,
  //      _: Option[DateTime])(_: SRLogger))
  //      .expects(*, *, *, *, *, *, *, *, *, *)
  //      .returning(Success(List()))
  //
  //    (campaignProspectDAO.filterProspectWhoHaveHoliday(
  //      _: List[Long],
  //      _: String,
  //      _: Option[Long],
  //      _: DateTime,
  //      _: Long
  //    )(_: SRLogger))
  //      .expects(*, *, *, *, *, *)
  //      .returning(Success(Set())) // no prospects have holiday today
  //
  //    (campaignProspectDAO.filterByProspectValidationStatus(_: Set[Long], _: Long)(_: SRLogger))
  //      .expects(*, *, *)
  //      .returning(Success(Set()))
  //
  //    (campaignProspectDAO.filterProspectsBySentCountBasedOnProspectLimit(
  //      _: Set[Long],
  //      _: String,
  //      _: Long,
  //      _: Int,
  //      _: Int
  //    )(_: SRLogger))
  //      .expects(*, *, *, *, *, *)
  //      .returning(Success(Set()))
  //
  //    (orgMetadataService.filterProspectsBasedOnDomainLimit)
  //      .expects(*)
  //      .returning(Success(true))
  //
  //    (campaignProspectDAO.mapProspectIdsWithProspectAccountIds(
  //      _: Set[ProspectId],
  //      _: TeamId
  //    ))
  //      .expects(*, *)
  //      .returning(Success(Map()))
  //
  //    (campaignProspectDAO.getEmailsScheduledInLast24HoursAnd7DaysForAProspectAccount)
  //      .expects(*, *, *)
  //      .returning(Success(Map()))
  //
  //    //    (campaignProspectDAO.fetchProspectsV2_PEV2(_: DateTime, _: Long, _: Long, _: Int, _: Int, _: Int, _: Vector[SchedulerMapStepIdAndDelay], _: Boolean, _: Option[DateTime])(_:SRLogger))
  //    //      .expects(*, 3, 2, 96, 100, 1000, Vector(
  //    //        SchedulerMapStepIdAndDelay(
  //    //          is_head_step_in_the_campaign = true,
  //    //          currentStepType = api.campaigns.models.CampaignStepType.AutoEmailStep,
  //    //          nextStepType    = api.campaigns.models.CampaignStepType.AutoEmailStep,
  //    //          currentStepId = 1L,
  //    //          delayTillNextStep = 10
  //    //        ),
  //    //        SchedulerMapStepIdAndDelay(
  //    //          is_head_step_in_the_campaign = false,
  //    //          currentStepType = api.campaigns.models.CampaignStepType.AutoEmailStep,
  //    //          nextStepType    = api.campaigns.models.CampaignStepType.AutoEmailStep,
  //    //          currentStepId = 2L,
  //    //          delayTillNextStep = 10
  //    //        )
  //    //      ), false, None, Logger)
  //    //      .returning(Success(List(prospectForScheduling)))
  //    //
  //    //    (campaignProspectDAO.fetchProspectsV2_PEV2(_: DateTime, _: Long, _: Long, _: Int, _: Int, _: Int, _: Vector[SchedulerMapStepIdAndDelay], _: Boolean, _: Option[DateTime])(_:SRLogger))
  //    //      .expects(*, 3, 2, 96, 100, 1000, Vector(
  //    //        SchedulerMapStepIdAndDelay(
  //    //          is_head_step_in_the_campaign = true,
  //    //          currentStepType = api.campaigns.models.CampaignStepType.AutoEmailStep,
  //    //          nextStepType    = api.campaigns.models.CampaignStepType.AutoEmailStep,
  //    //          currentStepId = 1L,
  //    //          delayTillNextStep = 10
  //    //        ),
  //    //        SchedulerMapStepIdAndDelay(
  //    //          is_head_step_in_the_campaign = false,
  //    //          currentStepType = api.campaigns.models.CampaignStepType.AutoEmailStep,
  //    //          nextStepType    = api.campaigns.models.CampaignStepType.AutoEmailStep,
  //    //          currentStepId = 2L,
  //    //          delayTillNextStep = 10
  //    //        )
  //    //      ), true, None, Logger)
  //    //      .returning(Success(List()))
  //
  //    (emailValidationService.sendProspectsForValidation)
  //      .expects(*, 2, 2, 1, true, List(), List(), 3)
  //      .returning(Success(1))
  //
  //    //    (templateService.checkMissingMergeTags)
  //    //      .expects("Variant body", "variant subject", prospectObject, InternalMergeTagValuesForProspect("Animesh Kumar","Animesh","Kumar",Some("dummy_link"), Some("Hey {{first_name}}"), None, None), Some(prospectAccount), ChannelType.EmailChannel)
  //    //      .returning(Success(Seq()))
  //
  //    //    (templateService.checkMissingMergeTags)
  //    //      .expects("Variant body", "variant subject", prospectObject, InternalMergeTagValuesForProspect("Animesh Kumar","Animesh","Kumar",Some("dummy_link"),  Some("Hey {{first_name}}"), None, None), Some(prospectAccount), ChannelType.EmailChannel)
  //    //      .returning(Success(Seq()))
  //
  //    (emailScheduledDAO.saveEmailsToBeScheduledAndUpdateCampaignDataV2)
  //      .expects(
  //        *
  //        //        Vector(
  //        //          EmailScheduledNew(Some(3),Some(3),false,Some(1),Some(1),DateTime.parse("2022-03-21T17:26:59.299+05:30"),DateTime.parse("2022-03-21T11:57:15.294Z"),1,None,Some(2),1,None,1,1,1,"<EMAIL>",Some("Animesh Kumar"),"<EMAIL>","Animesh",None,None,Some("Animesh Kumar"),None,1,true,false,None,false,None,None,None,None,false,false,false,None,None),
  //        //          EmailScheduledNew(Some(1),Some(1),true,Some(1),Some(1),DateTime.parse("2022-03-21T17:26:59.301+05:30"),DateTime.parse("2022-03-21T11:57:40.294Z"),1,None,Some(1),1,None,1,1,1,"<EMAIL>",Some("Animesh Kumar"),"<EMAIL>","Animesh",None,None,Some("Animesh Kumar"),None,1,true,false,None,false,None,None,None,None,false,false,false,None,None),
  //        //          EmailScheduledNew(Some(2),Some(3),false,Some(1),Some(1),DateTime.parse("2022-03-21T17:26:59.299+05:30"),DateTime.parse("2022-03-21T11:57:15.294Z"),1,None,Some(2),1,None,1,1,1,"<EMAIL>",Some("Animesh Kumar"),"<EMAIL>","Animesh",None,None,Some("Animesh Kumar"),None,1,true,false,None,false,None,None,None,None,false,false,false,None,None))
  //        , *)
  //      .returning(Success(Seq(emailScheduledNewAfterSaving)))
  //
  //    (() => repTrackingHostService.getRepTrackingHosts())
  //      .expects()
  //      .returning(Success(Seq()))
  //    (emailScheduledDAO.getPreviousSentSteps)
  //      .expects(1, 1)
  //      .returning(Success(Seq()))
  //
  //    (campaignEditedPreviewEmailDAO.find)
  //      .expects(Some(1L), None, 1)
  //      .returning(Success(Seq()))
  //    (templateService.render(_: String, _: ProspectObject, _: InternalMergeTagValuesForProspect, _: Option[ProspectAccount], _: ChannelType)(_: SRLogger))
  //      .expects("Variant body", prospectObject, *, Some(prospectAccount), *, *)
  //      .returning(Success("Variant body"))
  //    (templateService.render(_: String, _: ProspectObject, _: InternalMergeTagValuesForProspect, _: Option[ProspectAccount], _: ChannelType)(_: SRLogger))
  //      .expects("variant subject", prospectObject, *, Some(prospectAccount), *, *)
  //      .returning(Success("variant subject"))
  //
  //    (emailScheduledDAO.addBodyToEmailsToBeScheduled)
  //      .expects(*, *)
  //      .returning(Seq(1))
  //    (mqWebhookCompleted.publishCompletedProspects)
  //      .expects(1, 2, List())
  //      .returning(Success(0))
  //
  //    //    (emailSettingDAO._updateLastScheduled)
  //    //    .expects(1, *, *)
  //    //      .returning(1)
  //
  //    //    (campaignService._updateLastScheduled)
  //    //    .expects(List[Long](1, 2, 3), *)
  //    //      .returning(1)
  //    //
  //    //    (campaignService.setNextToBeScheduledAt)
  //    //      .expects(List[Long](1, 2, 3), true, *)
  //    //      .returning(Success(1))
  //
  //    // using * match below because scheduled at in CampaignProspectUpdateScheduleStatus is DateTime.now()
  //
  //    val c1 = CaptureOne[Seq[CampaignProspectUpdateScheduleStatus]]()
  //
  //    (campaignProspectDAO._updateScheduledStatus)
  //      .expects(capture(c1))
  //      .returning(Success(Seq(1)))
  //
  //    emailChannelScheduler.scheduleTasksForChannel(
  //        channelData = ChannelData.EmailChannelData(emailSettingId = emailSettingId_1),
  //        teamId = 3L,
  //        accountService = accountService,
  //        //accountDAO = accountDAO,
  //        emailNotificationService = emailNotificationService,
  //        campaignService = campaignService,
  //        campaignProspectDAO = campaignProspectDAO,
  //        campaignStepVariantDAO = campaignStepVariantDAO,
  //        campaignStepDAO = campaignStepDAO,
  //        emailServiceCompanion = emailServiceCompanion,
  //        templateService = templateService,
  //        taskDAO = taskDAO,
  //        srShuffleUtils = srShuffleUtils,
  //        taskService = taskService,
  //        campaignProspectService = campaignProspectService,
  //        campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO,
  //        campaignsMissingMergeTagService = campaignsMissingMergeTagService,
  //        srRedisSimpleLockServiceV2 = srRedisSimpleLockServiceV2,
  //        mqWebhookCompleted = mqWebhookCompleted,
  //      ).map { result =>
  //        Logger.info(s"result - $result")
  //
  //        c1.value.foreach(campaignProspectUpdateScheduleStatus => {
  //          assert(campaignProspectUpdateScheduleStatus.prospect_id == 1)
  //          assert(campaignProspectUpdateScheduleStatus.campaign_id == 1)
  //          assert(campaignProspectUpdateScheduleStatus.current_step_type == CampaignStepType.AutoEmailStep)
  //          assert(campaignProspectUpdateScheduleStatus.step_id == 1)
  //          assert(campaignProspectUpdateScheduleStatus.current_step_task_id == emailSettingId_1.toString)
  //        })
  //
  //        assert(result._1 == 1)
  //      }
  //      .recover {
  //        case e =>
  //          Logger.error(s"scheduleTasksForChannel failed with error: $e")
  //
  //          assert(false)
  //      }
  //
  //
  //  }


  "filterCampaignStepsRelevantToChannel " should " eliminate steps not relevant to channel last case is not email channel" in {

    val allCampaignSteps: Vector[SchedulerMapStepIdAndDelay] = Vector(
      SchedulerMapStepIdAndDelay(
        is_head_step_in_the_campaign =  true,
        currentStepType = CampaignStepType.AutoEmailStep,
        nextStepType    = api.campaigns.models.CampaignStepType.SmsMessage,
        currentStepId = 1L, delayTillNextStep =  1
      ),
      SchedulerMapStepIdAndDelay(
        is_head_step_in_the_campaign =  false,
        currentStepType = CampaignStepType.SmsMessage,
        nextStepType    = api.campaigns.models.CampaignStepType.AutoEmailStep,
        currentStepId = 2L, delayTillNextStep =  3
      ),
      SchedulerMapStepIdAndDelay(
        is_head_step_in_the_campaign =  false,
        currentStepType = CampaignStepType.AutoEmailStep,
        nextStepType    = api.campaigns.models.CampaignStepType.SmsMessage,
        currentStepId = 3L, delayTillNextStep =  5
      ),
      //      SchedulerMapStepIdAndDelay(
      //        is_head_step_in_the_campaign =  false,
      //        currentStepType = CampaignStepType.PhoneMessage,
      //        nextStepType    = api.campaigns.models.CampaignStepType.AutoEmailStep,
      //        stepId = 4L, delay =  7
      //      )

    )

    val res = ChannelSchedulerService.filterCampaignStepsRelevantToChannel(
      allCampaignSteps = allCampaignSteps,
      channelType = CampaignStepType.AutoEmailStep.channelType,
    )
    Future.successful( res).map( steps => {
      val found_incorrect_steps = steps.exists(step => {

        step.nextStepType != CampaignStepType.AutoEmailStep
      })

      assert(found_incorrect_steps == false)
      assert(steps.length == 1)
    }).recover {
      case e => Logger.info(s"error - $e :::: ${e.getMessage}")
        assert(e.getMessage == "")
    }

  }

  "filterCampaignStepsRelevantToChannel " should " eliminate steps not relevant to channel last case is  email channel" in {

    val allCampaignSteps: Vector[SchedulerMapStepIdAndDelay] = Vector(
      SchedulerMapStepIdAndDelay(
        is_head_step_in_the_campaign =  true,
        currentStepType = CampaignStepType.AutoEmailStep,
        nextStepType    = api.campaigns.models.CampaignStepType.SmsMessage,
        currentStepId = 1L, delayTillNextStep =  1
      ),
      SchedulerMapStepIdAndDelay(
        is_head_step_in_the_campaign =  false,
        currentStepType = CampaignStepType.SmsMessage,
        nextStepType    = api.campaigns.models.CampaignStepType.AutoEmailStep,
        currentStepId = 2L, delayTillNextStep =  3
      ),
      SchedulerMapStepIdAndDelay(
        is_head_step_in_the_campaign =  false,
        currentStepType = CampaignStepType.AutoEmailStep,
        nextStepType    = api.campaigns.models.CampaignStepType.SmsMessage,
        currentStepId = 3L, delayTillNextStep =  5
      ),
      SchedulerMapStepIdAndDelay(
        is_head_step_in_the_campaign =  false,
        currentStepType = CampaignStepType.SmsMessage,
        nextStepType    = api.campaigns.models.CampaignStepType.AutoEmailStep,
        currentStepId = 4L, delayTillNextStep =  7
      ),
      //      SchedulerMapStepIdAndDelay(
      //        is_head_step_in_the_campaign =  false,
      //        currentStepType = CampaignStepType.AutoEmailStep,
      //        nextStepType    = api.campaigns.models.CampaignStepType.PhoneMessage,
      //        stepId = 4L, delay =  7
      //      )


    )

    val res = ChannelSchedulerService.filterCampaignStepsRelevantToChannel(
      allCampaignSteps = allCampaignSteps,
      channelType = ChannelType.EmailChannel,
    )
    Future.successful( res).map( steps => {
      val found_incorrect_steps = steps.exists(step => {
        step.nextStepType != CampaignStepType.AutoEmailStep
      })

      Logger.info(s"step(0): ${steps(0)}")
      //Logger.info(s"step(1): ${steps(1)}")
      //Logger.info(s"step(2): ${steps(2)}")

      assert(found_incorrect_steps == false)
      assert(steps.length == 2)

    }).recover {
      case e => Logger.info(s"error - $e :::: ${e.getMessage}")
        assert(e.getMessage == "")
    }

  }

  "getEmailsCountThatCanBeScheduled" should "return 0 if daily limit is reached" in {
    val result = EmailChannelScheduler.getEmailsCountThatCanBeScheduled(
      emailsScheduledInLast24Hours = 11,
      emailsScheduledInLastWeek = 93,
      dailyLimit = 11,
      weeklyLimit = 97,
      previouslyProspectsSelectedCount = 0,
      prospectAccountId = ProspectAccountsId(1)
    )

    assert(result == 0)
  }

  "getEmailsCountThatCanBeScheduled" should "return 0 if weekly limit is reached" in {
    val result = EmailChannelScheduler.getEmailsCountThatCanBeScheduled(
      emailsScheduledInLast24Hours = 7,
      emailsScheduledInLastWeek = 93,
      dailyLimit = 11,
      weeklyLimit = 93,
      previouslyProspectsSelectedCount = 0,
      prospectAccountId = ProspectAccountsId(1)
    )

    assert(result == 0)
  }

  "getEmailsCountThatCanBeScheduled" should "return 4 constraint on daily limit" in {
    val result = EmailChannelScheduler.getEmailsCountThatCanBeScheduled(
      emailsScheduledInLast24Hours = 7,
      emailsScheduledInLastWeek = 93,
      dailyLimit = 11,
      weeklyLimit = 101,
      previouslyProspectsSelectedCount = 0,
      prospectAccountId = ProspectAccountsId(1)
    )

    assert(result == 4)
  }

  "getEmailsCountThatCanBeScheduled" should "return 3 constraint on weekly limit" in {
    val result = EmailChannelScheduler.getEmailsCountThatCanBeScheduled(
      emailsScheduledInLast24Hours = 7,
      emailsScheduledInLastWeek = 98,
      dailyLimit = 11,
      weeklyLimit = 101,
      previouslyProspectsSelectedCount = 0,
      prospectAccountId = ProspectAccountsId(1)
    )

    assert(result == 3)
  }

  "filterProspectsBySentCountBasedOnProspectAccountLimit" should "return empty set if prospect_account_limit is reached and all prospects belong to same prospect_account" in {

    (campaignProspectDAO.mapProspectIdsWithProspectAccountIds)
      .expects(prospectIds, teamId)
      .returning(Success(Map(ProspectId(3L) -> Some(ProspectAccountsId(23L)),
        ProspectId(5L) -> Some(ProspectAccountsId(23L)),
        ProspectId(7L) -> Some(ProspectAccountsId(23L)),
        ProspectId(17L) -> Some(ProspectAccountsId(23L)),
        ProspectId(19L) -> Some(ProspectAccountsId(23L)))))

    (campaignProspectDAO.mapProspectIdsWithProspectAccountIds)
      .expects(*, *)
      .returning(Success(Map()))

    (campaignProspectDAO.getEmailsScheduledInLast24HoursAnd7DaysForAProspectAccount)
      .expects(List(ProspectAccountsId(23L)), timezone, teamId)
      .returning(Success(Map(ProspectAccountsId(23L) -> EmailsScheduledCount(11, 23))))

    val result = emailChannelScheduler.filterProspectsBySentCountBasedOnProspectAccountLimit(
      prospectIds = prospectIds,
      campaignTimezone = "Asia/Kolkata",
      maxEmailsPerProspectAccountPerDay = 11,
      maxEmailsPerProspectAccountPerWeek = 53,
      team_id = teamId,
      campaignProspectDAO = campaignProspectDAO,
      previousSelectedProspectIds = Set()
    )

    result match {
      case Success(set) =>
        assert(set.isEmpty)

      case _ =>
        assert(false)
    }
  }

  "filterProspectsBySentCountBasedOnProspectAccountLimit" should "return prospectIds not associated with any prospect_account if prospect_account_limit is reached." in {

    (campaignProspectDAO.mapProspectIdsWithProspectAccountIds)
      .expects(prospectIds, teamId)
      .returning(Success(Map(ProspectId(3L) -> Some(ProspectAccountsId(23L)),
        ProspectId(5L) -> None,
        ProspectId(7L) -> None,
        ProspectId(17L) -> Some(ProspectAccountsId(23L)),
        ProspectId(19L) -> None)))

    (campaignProspectDAO.mapProspectIdsWithProspectAccountIds)
      .expects(*, *)
      .returning(Success(Map()))

    (campaignProspectDAO.getEmailsScheduledInLast24HoursAnd7DaysForAProspectAccount)
      .expects(List(ProspectAccountsId(23L)), timezone, teamId)
      .returning(Success(Map(ProspectAccountsId(23L) -> EmailsScheduledCount(scheduled_in_last_24hours = 2, scheduled_in_last_week = 29))))

    val result = emailChannelScheduler.filterProspectsBySentCountBasedOnProspectAccountLimit(
      prospectIds = prospectIds,
      campaignTimezone = "Asia/Kolkata",
      maxEmailsPerProspectAccountPerDay = 2,
      maxEmailsPerProspectAccountPerWeek = 31,
      team_id = teamId,
      campaignProspectDAO = campaignProspectDAO,
      previousSelectedProspectIds = Set()
    )

    result match {
      case Success(set) =>
        assert(set.contains(5L) && set.contains(7L) && set.contains(19L))

      case _ =>
        assert(false)
    }
  }

  "filterProspectsBySentCountBasedOnProspectAccountLimit" should "return some prospectIds if prospect_account_limit is will reach after scheduling these" in {

    (campaignProspectDAO.mapProspectIdsWithProspectAccountIds)
      .expects(prospectIds, teamId)
      .returning(Success(Map(ProspectId(3L) -> Some(ProspectAccountsId(23L)),
        ProspectId(5L) -> Some(ProspectAccountsId(23L)),
        ProspectId(7L) -> Some(ProspectAccountsId(23L)),
        ProspectId(17L) -> Some(ProspectAccountsId(23L)),
        ProspectId(19L) -> Some(ProspectAccountsId(23L)))))

    (campaignProspectDAO.mapProspectIdsWithProspectAccountIds)
      .expects(*, *)
      .returning(Success(Map()))

    (campaignProspectDAO.getEmailsScheduledInLast24HoursAnd7DaysForAProspectAccount)
      .expects(List(ProspectAccountsId(23L)), timezone, teamId)
      .returning(Success(Map(ProspectAccountsId(23L) -> EmailsScheduledCount(scheduled_in_last_24hours = 3, scheduled_in_last_week = 51))))

    val result = emailChannelScheduler.filterProspectsBySentCountBasedOnProspectAccountLimit(
      prospectIds = prospectIds,
      campaignTimezone = "Asia/Kolkata",
      maxEmailsPerProspectAccountPerDay = 11,
      maxEmailsPerProspectAccountPerWeek = 53,
      team_id = teamId,
      campaignProspectDAO = campaignProspectDAO,
      previousSelectedProspectIds = Set()
    )

    result match {
      case Success(set) =>
        assert(set.size == 2) // maxEmailsPerProspectAccountPerWeek - scheduled_in_last_week i.e. 53 - 51

      case _ =>
        assert(false)
    }
  }

  "filterProspectsBySentCountBasedOnProspectAccountLimit" should "return prospectIds from different prospect_account if prospect_account_limit is reached for one." in {

    (campaignProspectDAO.mapProspectIdsWithProspectAccountIds)
      .expects(prospectIds, teamId)
      .returning(Success(Map(ProspectId(3L) -> Some(ProspectAccountsId(29L)),
        ProspectId(5L) -> Some(ProspectAccountsId(23L)),
        ProspectId(7L) -> Some(ProspectAccountsId(29L)),
        ProspectId(17L) -> Some(ProspectAccountsId(23L)),
        ProspectId(19L) -> Some(ProspectAccountsId(29L)))))

    (campaignProspectDAO.mapProspectIdsWithProspectAccountIds)
      .expects(*, *)
      .returning(Success(Map()))

    (campaignProspectDAO.getEmailsScheduledInLast24HoursAnd7DaysForAProspectAccount)
      .expects(List(ProspectAccountsId(23L), ProspectAccountsId(29L)), timezone, teamId)
      .returning(Success(Map(ProspectAccountsId(23L) -> EmailsScheduledCount(scheduled_in_last_24hours = 3, scheduled_in_last_week = 31),
        ProspectAccountsId(29L) -> EmailsScheduledCount(scheduled_in_last_24hours = 11, scheduled_in_last_week = 51))))

    val result = emailChannelScheduler.filterProspectsBySentCountBasedOnProspectAccountLimit(
      prospectIds = prospectIds,
      campaignTimezone = "Asia/Kolkata",
      maxEmailsPerProspectAccountPerDay = 11,
      maxEmailsPerProspectAccountPerWeek = 53,
      team_id = teamId,
      campaignProspectDAO = campaignProspectDAO,
      previousSelectedProspectIds = Set()
    )

    result match {
      case Success(set) =>
        assert(set.contains(5L) && set.contains(17L))

      case _ =>
        assert(false)
    }
  }

  "filterProspectsBySentCountBasedOnProspectAccountLimit" should "return some prospectIds from both prospect_accounts if limit for both will be reached after scheduling these." in {

    val prospectToAccountMap = Map(ProspectId(3L) -> Some(ProspectAccountsId(29L)),
      ProspectId(5L) -> Some(ProspectAccountsId(23L)),
      ProspectId(7L) -> Some(ProspectAccountsId(29L)),
      ProspectId(17L) -> Some(ProspectAccountsId(23L)),
      ProspectId(19L) -> Some(ProspectAccountsId(29L)))

    (campaignProspectDAO.mapProspectIdsWithProspectAccountIds)
      .expects(prospectIds, teamId)
      .returning(Success(prospectToAccountMap))

    (campaignProspectDAO.mapProspectIdsWithProspectAccountIds)
      .expects(*, *)
      .returning(Success(Map()))

    (campaignProspectDAO.getEmailsScheduledInLast24HoursAnd7DaysForAProspectAccount)
      .expects(List(ProspectAccountsId(23L), ProspectAccountsId(29L)), timezone, teamId)
      .returning(Success(Map(ProspectAccountsId(23L) -> EmailsScheduledCount(scheduled_in_last_24hours = 10, scheduled_in_last_week = 31),
        ProspectAccountsId(29L) -> EmailsScheduledCount(scheduled_in_last_24hours = 7, scheduled_in_last_week = 51))))

    val result = emailChannelScheduler.filterProspectsBySentCountBasedOnProspectAccountLimit(
      prospectIds = prospectIds,
      campaignTimezone = "Asia/Kolkata",
      maxEmailsPerProspectAccountPerDay = 11,
      maxEmailsPerProspectAccountPerWeek = 53,
      team_id = teamId,
      campaignProspectDAO = campaignProspectDAO,
      previousSelectedProspectIds = Set()
    )

    result match {
      case Success(set) =>
        assert(set.count(prospectId => prospectToAccountMap(ProspectId(prospectId)).get.id == 23L) == 1 && // 1 prospect from prospect_account_id = 23L and
          set.count(prospectId => prospectToAccountMap(ProspectId(prospectId)).get.id == 29L) == 2)   // 2 prospect from prospect_account_id = 29L

      case _ =>
        assert(false)
    }
  }

  "filterProspectsBySentCountBasedOnProspectAccountLimit" should "return all prospectIds if limit will not be reached." in {

    (campaignProspectDAO.mapProspectIdsWithProspectAccountIds)
      .expects(prospectIds, teamId)
      .returning(Success(Map(ProspectId(3L) -> Some(ProspectAccountsId(29L)),
        ProspectId(5L) -> Some(ProspectAccountsId(23L)),
        ProspectId(7L) -> Some(ProspectAccountsId(29L)),
        ProspectId(17L) -> Some(ProspectAccountsId(23L)),
        ProspectId(19L) -> Some(ProspectAccountsId(29L)))))

    (campaignProspectDAO.mapProspectIdsWithProspectAccountIds)
      .expects(*, *)
      .returning(Success(Map()))

    (campaignProspectDAO.getEmailsScheduledInLast24HoursAnd7DaysForAProspectAccount)
      .expects(List(ProspectAccountsId(23L), ProspectAccountsId(29L)), timezone, teamId)
      .returning(Success(Map(ProspectAccountsId(23L) -> EmailsScheduledCount(scheduled_in_last_24hours = 5, scheduled_in_last_week = 31),
        ProspectAccountsId(29L) -> EmailsScheduledCount(scheduled_in_last_24hours = 7, scheduled_in_last_week = 47))))

    val result = emailChannelScheduler.filterProspectsBySentCountBasedOnProspectAccountLimit(
      prospectIds = prospectIds,
      campaignTimezone = timezone,
      maxEmailsPerProspectAccountPerDay = 11,
      maxEmailsPerProspectAccountPerWeek = 53,
      team_id = teamId,
      campaignProspectDAO = campaignProspectDAO,
      previousSelectedProspectIds = Set()
    )

    result match {
      case Success(set) =>
        assert(set == prospectIds.map(_.id))

      case _ =>
        assert(false)
    }
  }
}











