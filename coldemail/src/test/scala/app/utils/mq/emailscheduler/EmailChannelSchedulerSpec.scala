package app.utils.mq.emailscheduler

import api.accounts.AccountUuid
import api.campaigns.models.CampaignStepType.AutoEmailMagicContent
import api.campaigns.models.{CampaignStepData, CampaignStepType, StepContext}
import app.test_fixtures.prospect.ProspectFixtures
import eventframework.{ProspectObject, ProspectObjectInternal}
import org.joda.time.DateTime
import org.scalamock.scalatest.AsyncMockFactory
import org.scalatest.funspec.AsyncFunSpec
import play.api.libs.json.Json
import sr_scheduler.models.CampaignEmailPriority
import utils.SRLogger
import utils.mq.channel_scheduler.channels.EmailChannelScheduler

class EmailChannelSchedulerSpec extends AsyncFunSpec with AsyncMockFactory {

  given Logger: SRLogger = new SRLogger("EmailChannelSchedulerSpec")

  describe("EmailChannelScheduler.takeProspectsBasedOnEmailPriority") {
    val dummyProspectForScheduling = ProspectFixtures.dummyProspectForScheduling

    describe("fetch limit = total found") {
      it("should give 10 prospect, 5 of each new and followup should be given") {
        val result = EmailChannelScheduler.takeProspectsBasedOnEmailPriority(
          foundFollowUpProspects = List.fill(10)(dummyProspectForScheduling.copy(current_step_id = Some(321))),
          foundNewProspects =  List.fill(10)(dummyProspectForScheduling),
          fetchLimitForFirstStep = 10,
          dailyQuota = 10,
          fetchLimitForFollowupStep = 10,
          followUpCampaignSentCount = 0,
          overallFetchLimit = 10,
          newCampaignSentCount = 0,
          campaignEmailPriority = CampaignEmailPriority.EQUAL,
          campaignId = 1234,
          logger = Logger
        )

        val pickedFollowup = result.filter(_.current_step_id.isDefined)
        val pickedNewProspects = result.filter(_.current_step_id.isEmpty)
        assert(result.length == 10)
        assert(pickedFollowup.length == 5)
        assert(pickedNewProspects.length == 5)
      }

      it("should give 10 prospect, 3 followup and 7 new") {
        val result = EmailChannelScheduler.takeProspectsBasedOnEmailPriority(
          foundFollowUpProspects = List.fill(10)(dummyProspectForScheduling.copy(current_step_id = Some(321))),
          foundNewProspects =  List.fill(10)(dummyProspectForScheduling),
          fetchLimitForFirstStep = 10,
          dailyQuota = 10,
          fetchLimitForFollowupStep = 10,
          followUpCampaignSentCount = 0,
          overallFetchLimit = 10,
          newCampaignSentCount = 0,
          campaignEmailPriority = CampaignEmailPriority.FIRST_EMAIL,
          campaignId = 1234,
          logger = Logger
        )

        val pickedFollowup = result.filter(_.current_step_id.isDefined)
        val pickedNewProspects = result.filter(_.current_step_id.isEmpty)
        assert(result.length == 10)
        assert(pickedFollowup.length == 3)
        assert(pickedNewProspects.length == 7)
      }

      it("should give 10 prospect, 7 followup and 3 new") {
        val result = EmailChannelScheduler.takeProspectsBasedOnEmailPriority(
          foundFollowUpProspects = List.fill(10)(dummyProspectForScheduling.copy(current_step_id = Some(321))),
          foundNewProspects =  List.fill(10)(dummyProspectForScheduling),
          fetchLimitForFirstStep = 10,
          dailyQuota = 10,
          fetchLimitForFollowupStep = 10,
          followUpCampaignSentCount = 0,
          overallFetchLimit = 10,
          newCampaignSentCount = 0,
          campaignEmailPriority = CampaignEmailPriority.FOLLOWUP_EMAILS,
          campaignId = 1234,
          logger = Logger
        )

        val pickedFollowup = result.filter(_.current_step_id.isDefined)
        val pickedNewProspects = result.filter(_.current_step_id.isEmpty)
        assert(result.length == 10)
        assert(pickedFollowup.length == 7 || pickedFollowup.length == 6)
        assert(pickedNewProspects.length == 3 || pickedNewProspects.length == 4)
      }
    }


    describe("fetch limit < total found") {
      it("should give 10 prospect, 5 of each new and followup should be given") {
        val result = EmailChannelScheduler.takeProspectsBasedOnEmailPriority(
          foundFollowUpProspects = List.fill(20)(dummyProspectForScheduling.copy(current_step_id = Some(321))),
          foundNewProspects =  List.fill(20)(dummyProspectForScheduling),
          fetchLimitForFirstStep = 10,
          dailyQuota = 10,
          fetchLimitForFollowupStep = 10,
          followUpCampaignSentCount = 0,
          overallFetchLimit = 10,
          newCampaignSentCount = 0,
          campaignEmailPriority = CampaignEmailPriority.EQUAL,
          campaignId = 1234,
          logger = Logger
        )

        val pickedFollowup = result.filter(_.current_step_id.isDefined)
        val pickedNewProspects = result.filter(_.current_step_id.isEmpty)
        assert(result.length == 10)
        assert(pickedFollowup.length == 5)
        assert(pickedNewProspects.length == 5)
      }

      it("should give 10 prospect, 3 followup and 7 new") {
        val result = EmailChannelScheduler.takeProspectsBasedOnEmailPriority(
          foundFollowUpProspects = List.fill(20)(dummyProspectForScheduling.copy(current_step_id = Some(321))),
          foundNewProspects =  List.fill(20)(dummyProspectForScheduling),
          fetchLimitForFirstStep = 10,
          dailyQuota = 10,
          fetchLimitForFollowupStep = 10,
          followUpCampaignSentCount = 0,
          overallFetchLimit = 10,
          newCampaignSentCount = 0,
          campaignEmailPriority = CampaignEmailPriority.FIRST_EMAIL,
          campaignId = 1234,
          logger = Logger
        )

        val pickedFollowup = result.filter(_.current_step_id.isDefined)
        val pickedNewProspects = result.filter(_.current_step_id.isEmpty)
        assert(result.length == 10)
        assert(pickedFollowup.length == 3)
        assert(pickedNewProspects.length == 7)
      }

      it("should give 10 prospect, 7 followup and 3 new") {
        val result = EmailChannelScheduler.takeProspectsBasedOnEmailPriority(
          foundFollowUpProspects = List.fill(20)(dummyProspectForScheduling.copy(current_step_id = Some(321))),
          foundNewProspects =  List.fill(20)(dummyProspectForScheduling),
          fetchLimitForFirstStep = 10,
          dailyQuota = 10,
          fetchLimitForFollowupStep = 10,
          followUpCampaignSentCount = 0,
          overallFetchLimit = 10,
          newCampaignSentCount = 0,
          campaignEmailPriority = CampaignEmailPriority.FOLLOWUP_EMAILS,
          campaignId = 1234,
          logger = Logger
        )

        val pickedFollowup = result.filter(_.current_step_id.isDefined)
        val pickedNewProspects = result.filter(_.current_step_id.isEmpty)
        assert(result.length == 10)
        assert(pickedFollowup.length == 7 || pickedFollowup.length == 6)
        assert(pickedNewProspects.length == 3 || pickedNewProspects.length == 4)
      }

      it("should give 10 prospect, 5 each if total found is more than limit") {
        val result = EmailChannelScheduler.takeProspectsBasedOnEmailPriority(
          foundFollowUpProspects = List.fill(20)(dummyProspectForScheduling.copy(current_step_id = Some(321))),
          foundNewProspects =  List.fill(20)(dummyProspectForScheduling),
          fetchLimitForFirstStep = 10,
          dailyQuota = 100,
          fetchLimitForFollowupStep = 40,
          followUpCampaignSentCount = 0,
          overallFetchLimit = 10,
          newCampaignSentCount = 0,
          campaignEmailPriority = CampaignEmailPriority.EQUAL,
          campaignId = 1234,
          logger = Logger
        )

        val pickedFollowup = result.filter(_.current_step_id.isDefined)
        val pickedNewProspects = result.filter(_.current_step_id.isEmpty)
        assert(result.length == 10)
      }


      it("should give 10 prospect, followup found is less than half") {
        val result = EmailChannelScheduler.takeProspectsBasedOnEmailPriority(
          foundFollowUpProspects = List.fill(4)(dummyProspectForScheduling.copy(current_step_id = Some(321))),
          foundNewProspects =  List.fill(20)(dummyProspectForScheduling),
          fetchLimitForFirstStep = 10,
          dailyQuota = 13,
          fetchLimitForFollowupStep = 40,
          followUpCampaignSentCount = 0,
          overallFetchLimit = 10,
          newCampaignSentCount = 0,
          campaignEmailPriority = CampaignEmailPriority.EQUAL,
          campaignId = 1234,
          logger = Logger
        )

        val pickedFollowup = result.filter(_.current_step_id.isDefined)
        val pickedNewProspects = result.filter(_.current_step_id.isEmpty)
        assert(result.length == 10)
        assert(pickedFollowup.length == 4)
        assert(pickedNewProspects.length == 6)
      }

      it("should give 10 prospect, new found is less than half") {
        val result = EmailChannelScheduler.takeProspectsBasedOnEmailPriority(
          foundFollowUpProspects = List.fill(20)(dummyProspectForScheduling.copy(current_step_id = Some(321))),
          foundNewProspects =  List.fill(4)(dummyProspectForScheduling),
          fetchLimitForFirstStep = 10,
          dailyQuota = 13,
          fetchLimitForFollowupStep = 40,
          followUpCampaignSentCount = 0,
          overallFetchLimit = 10,
          newCampaignSentCount = 0,
          campaignEmailPriority = CampaignEmailPriority.EQUAL,
          campaignId = 1234,
          logger = Logger
        )

        val pickedFollowup = result.filter(_.current_step_id.isDefined)
        val pickedNewProspects = result.filter(_.current_step_id.isEmpty)
        assert(result.length == 10)
        assert(pickedFollowup.length <= 10)
        assert(pickedNewProspects.length <= 4)
      }
    }

    describe("fetch limit > total found") {
      it("should give 10 prospect, 5 of each new and followup should be given") {
        val result = EmailChannelScheduler.takeProspectsBasedOnEmailPriority(
          foundFollowUpProspects = List.fill(20)(dummyProspectForScheduling.copy(current_step_id = Some(321))),
          foundNewProspects =  List.fill(20)(dummyProspectForScheduling),
          fetchLimitForFirstStep = 50,
          dailyQuota = 100,
          fetchLimitForFollowupStep = 50,
          followUpCampaignSentCount = 0,
          overallFetchLimit = 50,
          newCampaignSentCount = 0,
          campaignEmailPriority = CampaignEmailPriority.EQUAL,
          campaignId = 1234,
          logger = Logger
        )

        val pickedFollowup = result.filter(_.current_step_id.isDefined)
        val pickedNewProspects = result.filter(_.current_step_id.isEmpty)
        assert(result.length == 40)
        assert(pickedFollowup.length == 20)
        assert(pickedNewProspects.length == 20)
      }

    }

  }

  describe("EmailChannelScheduler.generateTaskId") {
    it("should generate correct task id with all positive values") {
      val taskId = EmailChannelScheduler.generateTaskId(
        campaignId = 123L,
        stepId = 456L,
        prospectId = 789L
      )
      assert(taskId == "123_456_789")
    }

    it("should handle zero values") {
      val taskId = EmailChannelScheduler.generateTaskId(
        campaignId = 0L,
        stepId = 0L,
        prospectId = 0L
      )
      assert(taskId == "0_0_0")
    }

    it("should handle negative values") {
      val taskId = EmailChannelScheduler.generateTaskId(
        campaignId = -123L,
        stepId = -456L,
        prospectId = -789L
      )
      assert(taskId == "-123_-456_-789")
    }

    it("should handle mixed positive and negative values") {
      val taskId = EmailChannelScheduler.generateTaskId(
        campaignId = -123L,
        stepId = 0L,
        prospectId = 789L
      )
      assert(taskId == "-123_0_789")
    }

    it("should handle maximum long values") {
      val taskId = EmailChannelScheduler.generateTaskId(
        campaignId = Long.MaxValue,
        stepId = Long.MaxValue,
        prospectId = Long.MaxValue
      )
      assert(taskId == s"${Long.MaxValue}_${Long.MaxValue}_${Long.MaxValue}")
    }
  }

  describe("EmailChannelScheduler.getSubjectAndBodyForMagicContent") {
    describe("when handling AutoEmailMagicContent") {
      it("should return generated content when available") {
        val generatedContent = Some(("Generated Subject", "Generated Body"))
        val stepData = CampaignStepData.AutoEmailMagicContentStep(
          step_context = StepContext(
            call_to_action = "Test CTA",
            step_details = "Test Details",
            columns_to_use = List()
          )
        )

        val (subject, body) = EmailChannelScheduler.getSubjectAndBodyForMagicContent(
          CampaignStepType.AutoEmailMagicContent,
          generatedContent,
          stepData
        )

        assert(subject == "Generated Subject")
        assert(body == "Generated Body")
      }

      it("should fallback to original content when generated content is None") {
        val originalStepData = CampaignStepData.AutoEmailStep(
          subject = "Original Subject",
          body = "Original Body"
        )

        val (subject, body) = EmailChannelScheduler.getSubjectAndBodyForMagicContent(
          CampaignStepType.AutoEmailMagicContent,
          None,
          originalStepData
        )

        assert(subject == "Original Subject")
        assert(body == "Original Body")
      }
    }

    describe("when handling ManualEmailMagicContent") {
      it("should return generated content when available") {
        val generatedContent = Some(("Generated Subject", "Generated Body"))
        val stepData = CampaignStepData.ManualEmailMagicContentStep(
          step_context = StepContext(
            call_to_action = "Test CTA",
            step_details = "Test Details",
            columns_to_use = List()
          )
        )

        val (subject, body) = EmailChannelScheduler.getSubjectAndBodyForMagicContent(
          CampaignStepType.ManualEmailMagicContent,
          generatedContent,
          stepData
        )

        assert(subject == "Generated Subject")
        assert(body == "Generated Body")
      }

      it("should fallback to original content when generated content is None") {
        val originalStepData = CampaignStepData.ManualEmailStep(
          subject = "Original Subject",
          body = "Original Body"
        )

        val (subject, body) = EmailChannelScheduler.getSubjectAndBodyForMagicContent(
          CampaignStepType.ManualEmailMagicContent,
          None,
          originalStepData
        )

        assert(subject == "Original Subject")
        assert(body == "Original Body")
      }
    }

    describe("when handling non-magic content types") {
      it("should return original content for AutoEmailStep regardless of generated content") {
        val generatedContent = Some(("Generated Subject", "Generated Body"))
        val originalStepData = CampaignStepData.AutoEmailStep(
          subject = "Original Subject",
          body = "Original Body"
        )

        val (subject, body) = EmailChannelScheduler.getSubjectAndBodyForMagicContent(
          CampaignStepType.AutoEmailStep,
          generatedContent,
          originalStepData
        )

        assert(subject == "Original Subject")
        assert(body == "Original Body")
      }

      it("should return original content for ManualEmailStep regardless of generated content") {
        val generatedContent = Some(("Generated Subject", "Generated Body"))
        val originalStepData = CampaignStepData.ManualEmailStep(
          subject = "Original Subject",
          body = "Original Body"
        )

        val (subject, body) = EmailChannelScheduler.getSubjectAndBodyForMagicContent(
          CampaignStepType.ManualEmailStep,
          generatedContent,
          originalStepData
        )

        assert(subject == "Original Subject")
        assert(body == "Original Body")
      }
    }

    describe("edge cases") {
      it("should handle empty strings in generated content") {
        val generatedContent = Some(("", ""))
        val stepData = CampaignStepData.AutoEmailMagicContentStep(
          step_context = StepContext(
            call_to_action = "Test CTA",
            step_details = "Test Details",
            columns_to_use = List()
          )
        )

        val (subject, body) = EmailChannelScheduler.getSubjectAndBodyForMagicContent(
          CampaignStepType.AutoEmailMagicContent,
          generatedContent,
          stepData
        )

        assert(subject == "")
        assert(body == "")
      }

      it("should handle very long content") {
        val longString = "a" * 10000
        val generatedContent = Some((longString, longString))
        val stepData = CampaignStepData.AutoEmailMagicContentStep(
          step_context = StepContext(
            call_to_action = "Test CTA",
            step_details = "Test Details",
            columns_to_use = List()
          )
        )

        val (subject, body) = EmailChannelScheduler.getSubjectAndBodyForMagicContent(
          CampaignStepType.AutoEmailMagicContent,
          generatedContent,
          stepData
        )

        assert(subject.length == 10000)
        assert(body.length == 10000)
      }
    }
  }


}
