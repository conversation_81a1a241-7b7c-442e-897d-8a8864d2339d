package app.utils.mq.channels

import api.accounts.AccountUuid
import api.call.models.ParticipantCallStatus
import api.campaigns.CampaignStepWithChildren
import api.campaigns.models.{CampaignStepType, CampaignStepsStructure}
import api.emails.RejectionReasonForCampaignProspectStepSchedule
import api.emails.models.EmailReplyType
import api.prospects.ProspectUuid
import api.tasks.models.TaskStatusType
import eventframework.{ProspectObject, ProspectObjectInternal}
import org.joda.time.DateTime
import org.scalatest.funspec.AsyncFunSpec
import play.api.libs.json.{JsVal<PERSON>, <PERSON>son}
import sr_scheduler.models.ChannelType
import utils.SRLogger
import utils.helpers.LogHelpers
import utils.mq.channel_scheduler.{ChannelSchedulerService, LastSentStepData, TableTypeForScheduler}
import utils.mq.channel_scheduler.channels.{CampaignStepCondition, ChildStepIdWithCondition, NextStepFinderForDrip}
import utils.testapp.TestAppTrait

import scala.concurrent.{Await, ExecutionContext}
import scala.concurrent.duration.Duration
import scala.util.{Failure, Success}

// TODO: only extend the workerActorSystem dependency
class NextStepFinderForDrip extends AsyncFunSpec with TestAppTrait {

  override implicit lazy val executionContext: ExecutionContext = workerActorSystem.dispatcher


  /*

drip_campaign_nodes |
[
{"id": "496641", "data": {"type": "step", "label": "auto_linkedin_view_profile"}, "type": "auto_linkedin_view_profile", "position": {"x": 0, "y": 0}},
{"id": "496642", "data": {"type": "step", "label": "auto_send_linkedin_connection_request"}, "type": "auto_send_linkedin_connection_request", "position": {"x": 0, "y": 0}},
{"id": "PBfJT", "data": {"type": "condition", "label": "linkedIn_profile_connected"}, "type": "linkedIn_profile_connected", "position": {"x": 0, "y": 0}},
{"id": "496648", "data": {"type": "step", "label": "auto_send_linkedin_message"}, "type": "auto_send_linkedin_message", "position": {"x": 0, "y": 0}},
{"id": "496653", "data": {"type": "step", "label": "auto_linkedin_view_profile"}, "type": "auto_linkedin_view_profile", "position": {"x": 0, "y": 0}},
{"id": "sLYGi", "data": {"type": "condition", "label": "linkedIn_message_not_sent"}, "type": "linkedIn_message_not_sent", "position": {"x": 0, "y": 0}},
{"id": "496889", "data": {"type": "step", "label": "send_email"}, "type": "send_email", "position": {"x": 0, "y": 0}},
{"id": "496909", "data": {"type": "step", "label": "general_task"}, "type": "general_task", "position": {"x": 0, "y": 0}},
{"id": "tmzwT", "data": {"type": "condition", "label": "linkedIn_profile_connected"}, "type": "linkedIn_profile_connected", "position": {"x": 0, "y": 0}},
{"id": "IuINH", "data": {"type": "condition", "label": "has_phone_number"}, "type": "has_phone_number", "position": {"x": 0, "y": 0}},
{"id": "496654", "data": {"type": "step", "label": "send_sms"}, "type": "send_sms", "position": {"x": 0, "y": 0}},
{"id": "496859", "data": {"type": "step", "label": "send_email"}, "type": "send_email", "position": {"x": 0, "y": 0}},
{"id": "nuo9f", "data": {"type": "condition", "label": "has_replied_interested"}, "type": "has_replied_interested", "position": {"x": 0, "y": 0}},
{"id": "496890", "data": {"type": "step", "label": "general_task"}, "type": "general_task", "position": {"x": 0, "y": 0}},
{"id": "496895", "data": {"type": "step", "label": "send_email"}, "type": "send_email", "position": {"x": 0, "y": 0}},
{"id": "eJLGx", "data": {"type": "condition", "label": "has_replied"}, "type": "has_replied", "position": {"x": 0, "y": 0}},
{"id": "NPCE9", "data": {"type": "condition", "label": "linkedIn_profile_connected"}, "type": "linkedIn_profile_connected", "position": {"x": 0, "y": 0}},
{"id": "496897", "data": {"type": "step", "label": "manual_send_email"}, "type": "manual_send_email", "position": {"x": 0, "y": 0}},
{"id": "Ht3aQ", "data": {"type": "condition", "label": "has_replied_interested"}, "type": "has_replied_interested", "position": {"x": 0, "y": 0}},
{"id": "496910", "data": {"type": "step", "label": "general_task"}, "type": "general_task", "position": {"x": 0, "y": 0}},
{"id": "Af3j1", "data": {"type": "condition", "label": "has_phone_number"}, "type": "has_phone_number", "position": {"x": 0, "y": 0}},
{"id": "496917", "data": {"type": "step", "label": "general_task"}, "type": "general_task", "position": {"x": 0, "y": 0}}]

drip_campaign_edges | [
{"id": "496641-496642", "type": "nonConditionalEdge", "label": "no_condition", "style": {"opacity": 1}, "source": "496641", "target": "496642", "selected": false},
{"id": "496642-PBfJT", "type": "nonConditionalEdge", "label": "no_condition", "style": {"opacity": 1}, "source": "496642", "target": "PBfJT", "selected": false},
{"id": "PBfJT-496648", "type": "yesEdge", "label": "yes", "style": {"opacity": 1}, "source": "PBfJT", "target": "496648", "selected": false},
{"id": "PBfJT-496653", "type": "noEdge", "label": "no", "style": {"opacity": 1}, "source": "PBfJT", "target": "496653", "selected": false},
{"id": "496648-sLYGi", "type": "nonConditionalEdge", "label": "no_condition", "style": {"opacity": 1}, "source": "496648", "target": "sLYGi", "selected": false},
{"id": "sLYGi-496889", "type": "yesEdge", "label": "yes", "style": {"opacity": 1}, "source": "sLYGi", "target": "496889"},
{"id": "sLYGi-496909", "type": "noEdge", "label": "no", "style": {"opacity": 1}, "source": "sLYGi", "target": "496909"},
{"id": "496653-tmzwT", "type": "nonConditionalEdge", "label": "no_condition", "style": {"opacity": 1}, "source": "496653", "target": "tmzwT", "selected": false},

{"id": "IuINH-496654", "type": "yesEdge", "label": "yes", "style": {"opacity": 1}, "source": "IuINH", "target": "496654"},
{"id": "496859-IuINH", "type": "nonConditionalEdge", "label": "no_condition", "style": {"opacity": 1}, "source": "496859", "target": "IuINH"},
{"id": "496889-nuo9f", "type": "nonConditionalEdge", "label": "no_condition", "style": {"opacity": 1}, "source": "496889", "target": "nuo9f"},
{"id": "nuo9f-496890", "type": "yesEdge", "label": "yes", "style": {"opacity": 1}, "source": "nuo9f", "target": "496890"},
{"id": "tmzwT-496859", "type": "yesEdge", "label": "yes", "style": {"opacity": 1}, "source": "tmzwT", "target": "496859"},
{"id": "tmzwT-496895", "type": "noEdge", "label": "no", "style": {"opacity": 1}, "source": "tmzwT", "target": "496895"},
{"id": "496895-eJLGx", "type": "nonConditionalEdge", "label": "no_condition", "style": {"opacity": 1}, "source": "496895", "target": "eJLGx"},
{"id": "eJLGx-NPCE9", "type": "noEdge", "label": "no", "style": {"opacity": 1}, "source": "eJLGx", "target": "NPCE9"},
{"id": "NPCE9-496897", "type": "noEdge", "label": "no", "style": {"opacity": 1}, "source": "NPCE9", "target": "496897"},
{"id": "496909-Ht3aQ", "type": "nonConditionalEdge", "label": "no_condition", "style": {"opacity": 1}, "source": "496909", "target": "Ht3aQ"},
{"id": "Ht3aQ-496910", "type": "yesEdge", "label": "yes", "style": {"opacity": 1}, "source": "Ht3aQ", "target": "496910"},
{"id": "496897-Af3j1", "type": "nonConditionalEdge", "label": "no_condition", "style": {"opacity": 1}, "source": "496897", "target": "Af3j1"},
{"id": "Af3j1-496917", "type": "yesEdge", "label": "yes", "style": {"opacity": 1}, "source": "Af3j1", "target": "496917"}
]

   id   | delay  |               step_type               | campaign_id
--------+--------+---------------------------------------+-------------
 496648 | 259200 | auto_send_linkedin_message            |      176688
 496859 |  86400 | send_email                            |      176688
 496654 | 172800 | send_sms                              |      176688
 496653 | 259200 | auto_linkedin_view_profile            |      176688
 496889 | 259200 | send_email                            |      176688
 496897 | 259200 | manual_send_email                     |      176688
 496917 |  86400 | general_task                          |      176688
 496910 |  86400 | general_task                          |      176688
 496641 |  86400 | auto_linkedin_view_profile            |      176688
 496642 |  86400 | auto_send_linkedin_connection_request |      176688
 496890 | 345600 | general_task                          |      176688
 496895 |  86400 | send_email                            |      176688
 496909 | 259200 | general_task                          |      176688


Identified last steps: List(496890, 496910, 496654, 496917)


496641 - 496642

AVLIP (496641)
 |
ASLICR (496642)
 |
linkedIn_profile_connected? (PBfJT)
 |
 | - Yes (496648)
        |
        auto_send_linkedin_message (496648)
        |
        linkedIn_message_not_sent? (sLYGi)
        |
        | - Yes (496889)
             |
            send_email (496889)
             |
            has_replied_interested?  (nuo9f)
             |
             | - Yes (496890)
                  |
                 general_task  (496890)
             |
             | - No
                  |
                  general_task (500000) // TODO: Delete this - this was not actually part of the original campaign
        |
        | - No (496909)
             |
            general_task (496909)
             |
            has_replied_interested ? (Ht3aQ)
             |
             | - Yes (496910)
                   |
                  general_task
             |
             | - No

 |
 | - No (496653)
         |
        auto_linkedin_view_profile (496653)
         |
        linkedIn_profile_connected ? (tmzwT)
         |
         | - Yes (496859)
              |
             send_email
              |
             has_phone_number ? (IuINH)
              |
              | - Yes (496654)
              |
              | - No
         |
         | - No (496895)
              |
             send_email (496895)
              |
             has_replied? (eJLGx)
              |
              | - Yes
              |
              | - No ( NPCE9)
                  |
                 linkedIn_profile_connected ?
                  |
                  | - Yes
                  |
                  | - No (496897)
                      |
                     manual_send_email
                      |
                     has_phone_number ? (Af3j1)
                      |
                      | - Yes  (496917)
                          |
                          general_task (496917)

                      |
                      | - No

   */

  private val nodesJSON = Json.parse(
    """
      |[{"id":"496641","data":{"type":"step","label":"auto_linkedin_view_profile"},"type":"auto_linkedin_view_profile","position":{"x":0,"y":0}},{"id":"496642","data":{"type":"step","label":"auto_send_linkedin_connection_request"},"type":"auto_send_linkedin_connection_request","position":{"x":0,"y":0}},{"id":"PBfJT","data":{"type":"condition","label":"linkedIn_profile_connected"},"type":"linkedIn_profile_connected","position":{"x":0,"y":0}},{"id":"496648","data":{"type":"step","label":"auto_send_linkedin_message"},"type":"auto_send_linkedin_message","position":{"x":0,"y":0}},{"id":"496653","data":{"type":"step","label":"auto_linkedin_view_profile"},"type":"auto_linkedin_view_profile","position":{"x":0,"y":0}},{"id":"sLYGi","data":{"type":"condition","label":"linkedIn_message_not_sent"},"type":"linkedIn_message_not_sent","position":{"x":0,"y":0}},{"id":"496889","data":{"type":"step","label":"send_email"},"type":"send_email","position":{"x":0,"y":0}},{"id":"496909","data":{"type":"step","label":"general_task"},"type":"general_task","position":{"x":0,"y":0}},{"id":"tmzwT","data":{"type":"condition","label":"linkedIn_profile_connected"},"type":"linkedIn_profile_connected","position":{"x":0,"y":0}},{"id":"IuINH","data":{"type":"condition","label":"has_phone_number"},"type":"has_phone_number","position":{"x":0,"y":0}},{"id":"496654","data":{"type":"step","label":"send_sms"},"type":"send_sms","position":{"x":0,"y":0}},{"id":"496859","data":{"type":"step","label":"send_email"},"type":"send_email","position":{"x":0,"y":0}},{"id":"nuo9f","data":{"type":"condition","label":"has_replied_interested"},"type":"has_replied_interested","position":{"x":0,"y":0}},{"id":"496890","data":{"type":"step","label":"general_task"},"type":"general_task","position":{"x":0,"y":0}},{"id":"496895","data":{"type":"step","label":"send_email"},"type":"send_email","position":{"x":0,"y":0}},{"id":"eJLGx","data":{"type":"condition","label":"has_replied"},"type":"has_replied","position":{"x":0,"y":0}},{"id":"NPCE9","data":{"type":"condition","label":"linkedIn_profile_connected"},"type":"linkedIn_profile_connected","position":{"x":0,"y":0}},{"id":"496897","data":{"type":"step","label":"manual_send_email"},"type":"manual_send_email","position":{"x":0,"y":0}},{"id":"Ht3aQ","data":{"type":"condition","label":"has_replied_interested"},"type":"has_replied_interested","position":{"x":0,"y":0}},{"id":"496910","data":{"type":"step","label":"general_task"},"type":"general_task","position":{"x":0,"y":0}},{"id":"Af3j1","data":{"type":"condition","label":"has_phone_number"},"type":"has_phone_number","position":{"x":0,"y":0}},{"id":"496917","data":{"type":"step","label":"general_task"},"type":"general_task","position":{"x":0,"y":0}}]
      |""".stripMargin
  ).validate[List[JsValue]].get

  private val edgesJSON = Json.parse(
    """
      |[{"id":"496641-496642","type":"nonConditionalEdge","label":"no_condition","style":{"opacity":1},"source":"496641","target":"496642","selected":false},{"id":"496642-PBfJT","type":"nonConditionalEdge","label":"no_condition","style":{"opacity":1},"source":"496642","target":"PBfJT","selected":false},{"id":"PBfJT-496648","type":"yesEdge","label":"yes","style":{"opacity":1},"source":"PBfJT","target":"496648","selected":false},{"id":"PBfJT-496653","type":"noEdge","label":"no","style":{"opacity":1},"source":"PBfJT","target":"496653","selected":false},{"id":"496648-sLYGi","type":"nonConditionalEdge","label":"no_condition","style":{"opacity":1},"source":"496648","target":"sLYGi","selected":false},{"id":"sLYGi-496889","type":"yesEdge","label":"yes","style":{"opacity":1},"source":"sLYGi","target":"496889"},{"id":"sLYGi-496909","type":"noEdge","label":"no","style":{"opacity":1},"source":"sLYGi","target":"496909"},{"id":"496653-tmzwT","type":"nonConditionalEdge","label":"no_condition","style":{"opacity":1},"source":"496653","target":"tmzwT","selected":false},{"id":"IuINH-496654","type":"yesEdge","label":"yes","style":{"opacity":1},"source":"IuINH","target":"496654"},{"id":"496859-IuINH","type":"nonConditionalEdge","label":"no_condition","style":{"opacity":1},"source":"496859","target":"IuINH"},{"id":"496889-nuo9f","type":"nonConditionalEdge","label":"no_condition","style":{"opacity":1},"source":"496889","target":"nuo9f"},{"id":"nuo9f-496890","type":"yesEdge","label":"yes","style":{"opacity":1},"source":"nuo9f","target":"496890"},{"id":"tmzwT-496859","type":"yesEdge","label":"yes","style":{"opacity":1},"source":"tmzwT","target":"496859"},{"id":"tmzwT-496895","type":"noEdge","label":"no","style":{"opacity":1},"source":"tmzwT","target":"496895"},{"id":"496895-eJLGx","type":"nonConditionalEdge","label":"no_condition","style":{"opacity":1},"source":"496895","target":"eJLGx"},{"id":"eJLGx-NPCE9","type":"noEdge","label":"no","style":{"opacity":1},"source":"eJLGx","target":"NPCE9"},{"id":"NPCE9-496897","type":"noEdge","label":"no","style":{"opacity":1},"source":"NPCE9","target":"496897"},{"id":"496909-Ht3aQ","type":"nonConditionalEdge","label":"no_condition","style":{"opacity":1},"source":"496909","target":"Ht3aQ"},{"id":"Ht3aQ-496910","type":"yesEdge","label":"yes","style":{"opacity":1},"source":"Ht3aQ","target":"496910"},{"id":"496897-Af3j1","type":"nonConditionalEdge","label":"no_condition","style":{"opacity":1},"source":"496897","target":"Af3j1"},{"id":"Af3j1-496917","type":"yesEdge","label":"yes","style":{"opacity":1},"source":"Af3j1","target":"496917"}]
      |""".stripMargin
  ).validate[List[JsValue]].get


  private val nodesJSON2 = Json.parse(
    """
      |[{"id":"496641","data":{"type":"step","label":"auto_linkedin_view_profile"},"type":"auto_linkedin_view_profile","position":{"x":0,"y":0}},{"id":"496642","data":{"type":"step","label":"auto_send_linkedin_connection_request"},"type":"auto_send_linkedin_connection_request","position":{"x":0,"y":0}},{"id":"PBfJT","data":{"type":"condition","label":"linkedIn_profile_connected"},"type":"linkedIn_profile_connected","position":{"x":0,"y":0}},{"id":"496648","data":{"type":"step","label":"auto_send_linkedin_message"},"type":"auto_send_linkedin_message","position":{"x":0,"y":0}},{"id":"496653","data":{"type":"step","label":"auto_linkedin_view_profile"},"type":"auto_linkedin_view_profile","position":{"x":0,"y":0}},{"id":"sLYGi","data":{"type":"condition","label":"linkedIn_message_not_sent"},"type":"linkedIn_message_not_sent","position":{"x":0,"y":0}},{"id":"496889","data":{"type":"step","label":"send_email"},"type":"send_email","position":{"x":0,"y":0}},{"id":"496909","data":{"type":"step","label":"general_task"},"type":"general_task","position":{"x":0,"y":0}},{"id":"tmzwT","data":{"type":"condition","label":"linkedIn_profile_connected"},"type":"linkedIn_profile_connected","position":{"x":0,"y":0}},{"id":"IuINH","data":{"type":"condition","label":"has_phone_number"},"type":"has_phone_number","position":{"x":0,"y":0}},{"id":"496654","data":{"type":"step","label":"send_sms"},"type":"send_sms","position":{"x":0,"y":0}},{"id":"496859","data":{"type":"step","label":"send_email"},"type":"send_email","position":{"x":0,"y":0}},{"id":"nuo9f","data":{"type":"condition","label":"has_replied_interested"},"type":"has_replied_interested","position":{"x":0,"y":0}},{"id":"496890","data":{"type":"step","label":"general_task"},"type":"general_task","position":{"x":0,"y":0}},{"id":"496895","data":{"type":"step","label":"send_email"},"type":"send_email","position":{"x":0,"y":0}},{"id":"eJLGx","data":{"type":"condition","label":"has_replied"},"type":"has_replied","position":{"x":0,"y":0}},{"id":"NPCE9","data":{"type":"condition","label":"linkedIn_profile_connected"},"type":"linkedIn_profile_connected","position":{"x":0,"y":0}},{"id":"496897","data":{"type":"step","label":"manual_send_email"},"type":"manual_send_email","position":{"x":0,"y":0}},{"id":"Ht3aQ","data":{"type":"condition","label":"has_replied_interested"},"type":"has_replied_interested","position":{"x":0,"y":0}},{"id":"496910","data":{"type":"step","label":"general_task"},"type":"general_task","position":{"x":0,"y":0}},{"id":"Af3j1","data":{"type":"condition","label":"has_phone_number"},"type":"has_phone_number","position":{"x":0,"y":0}},{"id":"496917","data":{"type":"step","label":"general_task"},"type":"general_task","position":{"x":0,"y":0}},{"id":"500000","data":{"type":"step","label":"general_task"},"type":"general_task","position":{"x":0,"y":0}}]
      |""".stripMargin
  ).validate[List[JsValue]].get

  private val edgesJSON2 = Json.parse(
    """
      |[{"id":"496641-496642","type":"nonConditionalEdge","label":"no_condition","style":{"opacity":1},"source":"496641","target":"496642","selected":false},{"id":"496642-PBfJT","type":"nonConditionalEdge","label":"no_condition","style":{"opacity":1},"source":"496642","target":"PBfJT","selected":false},{"id":"PBfJT-496648","type":"yesEdge","label":"yes","style":{"opacity":1},"source":"PBfJT","target":"496648","selected":false},{"id":"PBfJT-496653","type":"noEdge","label":"no","style":{"opacity":1},"source":"PBfJT","target":"496653","selected":false},{"id":"496648-sLYGi","type":"nonConditionalEdge","label":"no_condition","style":{"opacity":1},"source":"496648","target":"sLYGi","selected":false},{"id":"sLYGi-496889","type":"yesEdge","label":"yes","style":{"opacity":1},"source":"sLYGi","target":"496889"},{"id":"sLYGi-496909","type":"noEdge","label":"no","style":{"opacity":1},"source":"sLYGi","target":"496909"},{"id":"496653-tmzwT","type":"nonConditionalEdge","label":"no_condition","style":{"opacity":1},"source":"496653","target":"tmzwT","selected":false},{"id":"IuINH-496654","type":"yesEdge","label":"yes","style":{"opacity":1},"source":"IuINH","target":"496654"},{"id":"496859-IuINH","type":"nonConditionalEdge","label":"no_condition","style":{"opacity":1},"source":"496859","target":"IuINH"},{"id":"496889-nuo9f","type":"nonConditionalEdge","label":"no_condition","style":{"opacity":1},"source":"496889","target":"nuo9f"},{"id":"nuo9f-496890","type":"yesEdge","label":"yes","style":{"opacity":1},"source":"nuo9f","target":"496890"},{"id":"tmzwT-496859","type":"yesEdge","label":"yes","style":{"opacity":1},"source":"tmzwT","target":"496859"},{"id":"tmzwT-496895","type":"noEdge","label":"no","style":{"opacity":1},"source":"tmzwT","target":"496895"},{"id":"496895-eJLGx","type":"nonConditionalEdge","label":"no_condition","style":{"opacity":1},"source":"496895","target":"eJLGx"},{"id":"eJLGx-NPCE9","type":"noEdge","label":"no","style":{"opacity":1},"source":"eJLGx","target":"NPCE9"},{"id":"NPCE9-496897","type":"noEdge","label":"no","style":{"opacity":1},"source":"NPCE9","target":"496897"},{"id":"496909-Ht3aQ","type":"nonConditionalEdge","label":"no_condition","style":{"opacity":1},"source":"496909","target":"Ht3aQ"},{"id":"Ht3aQ-496910","type":"yesEdge","label":"yes","style":{"opacity":1},"source":"Ht3aQ","target":"496910"},{"id":"496897-Af3j1","type":"nonConditionalEdge","label":"no_condition","style":{"opacity":1},"source":"496897","target":"Af3j1"},{"id":"Af3j1-496917","type":"yesEdge","label":"yes","style":{"opacity":1},"source":"Af3j1","target":"496917"},{"id":"nuo9f-500000","type":"noEdge","label":"no","style":{"opacity":1},"source":"nuo9f","target":"500000"}]
      |""".stripMargin
  ).validate[List[JsValue]].get

  private val campaign_id: Long = 176688

  private val head_node_id: Long = 496641

  private val allCampaignSteps = Map(
    496648L -> CampaignStepWithChildren(
      id = 496648L,
      label = None,
      campaign_id = campaign_id,
      delay = 259200,
      step_type = CampaignStepType.AutoLinkedinMessage,
      created_at = DateTime.now(),
      children = List(),
      variants = Seq()
    ),
    496859L -> CampaignStepWithChildren(
      id = 496859L,
      label = None,
      campaign_id = campaign_id,
      delay = 86400,
      step_type = CampaignStepType.AutoEmailStep,
      created_at = DateTime.now(),
      children = List(),
      variants = Seq()
    ),
    496654L -> CampaignStepWithChildren(
      id = 496654L,
      label = None,
      campaign_id = campaign_id,
      delay = 172800,
      step_type = CampaignStepType.SmsMessage,
      created_at = DateTime.now(),
      children = List(),
      variants = Seq()
    ),
    496653L -> CampaignStepWithChildren(
      id = 496653L,
      label = None,
      campaign_id = campaign_id,
      delay = 259200,
      step_type = CampaignStepType.AutoLinkedinViewProfile,
      created_at = DateTime.now(),
      children = List(),
      variants = Seq()
    ),
    496889L -> CampaignStepWithChildren(
      id = 496889L,
      label = None,
      campaign_id = campaign_id,
      delay = 259200,
      step_type = CampaignStepType.AutoEmailStep,
      created_at = DateTime.now(),
      children = List(),
      variants = Seq()
    ),
    496897L -> CampaignStepWithChildren(
      id = 496897L,
      label = None,
      campaign_id = campaign_id,
      delay = 259200,
      step_type = CampaignStepType.ManualEmailStep,
      created_at = DateTime.now(),
      children = List(),
      variants = Seq()
    ),
    496917L -> CampaignStepWithChildren(
      id = 496917L,
      label = None,
      campaign_id = campaign_id,
      delay = 86400,
      step_type = CampaignStepType.GeneralTask,
      created_at = DateTime.now(),
      children = List(),
      variants = Seq()
    ),
    496910L -> CampaignStepWithChildren(
      id = 496910L,
      label = None,
      campaign_id = campaign_id,
      delay = 86400,
      step_type = CampaignStepType.GeneralTask,
      created_at = DateTime.now(),
      children = List(),
      variants = Seq()
    ),
    head_node_id -> CampaignStepWithChildren(
      id = head_node_id,
      label = None,
      campaign_id = campaign_id,
      delay = 86400,
      step_type = CampaignStepType.AutoLinkedinViewProfile,
      created_at = DateTime.now(),
      children = List(),
      variants = Seq()
    ),
    496642L -> CampaignStepWithChildren(
      id = 496642L,
      label = None,
      campaign_id = campaign_id,
      delay = 86400,
      step_type = CampaignStepType.AutoLinkedinConnectionRequest,
      created_at = DateTime.now(),
      children = List(),
      variants = Seq()
    ),
    496890L -> CampaignStepWithChildren(
      id = 496890L,
      label = None,
      campaign_id = campaign_id,
      delay = 345600,
      step_type = CampaignStepType.GeneralTask,
      created_at = DateTime.now(),
      children = List(),
      variants = Seq()
    ),
    496895L -> CampaignStepWithChildren(
      id = 496895L,
      label = None,
      campaign_id = campaign_id,
      delay = 86400,
      step_type = CampaignStepType.AutoEmailStep,
      created_at = DateTime.now(),
      children = List(),
      variants = Seq()
    ),
    496909L -> CampaignStepWithChildren(
      id = 496909L,
      label = None,
      campaign_id = campaign_id,
      delay = 259200,
      step_type = CampaignStepType.GeneralTask,
      created_at = DateTime.now(),
      children = List(),
      variants = Seq()
    ),
    500000L -> CampaignStepWithChildren(
      id = 500000L,
      label = None,
      campaign_id = campaign_id,
      delay = 86400,
      step_type = CampaignStepType.GeneralTask,
      created_at = DateTime.now(),
      children = List(),
      variants = Seq()
    )
  )


  private implicit val Logger: SRLogger = SRLogger("NextStepFinderForDrip")

  describe("Test findNextStep") {

    it("should return empty list for leaf nodes") {

      val res = NextStepFinderForDrip.findNextStep(
        edges = edgesJSON,
        nodes = nodesJSON,
        parent_step_id = 496890
      )

      res.map { value =>

        assert(
          value == List()
        )

      }.recover { case e =>

        println(s"Failed to find next step. ${LogHelpers.getStackTraceAsString(e)}")

        assert(false)

      }

    }

    it("should return the next step when parent has only 1 child") {

      val res = NextStepFinderForDrip.findNextStep(
        edges = edgesJSON,
        nodes = nodesJSON,
        parent_step_id = head_node_id
      )

      res.map { value =>

        assert(
          value == List(ChildStepIdWithCondition(496642, List(CampaignStepCondition.NoCondition)))
        )

      }.recover { case e =>

        println(s"Failed to find next step. ${LogHelpers.getStackTraceAsString(e)}")

        assert(false)

      }

    }

    it("should return the next step when parent has only 2 children") {

      val res = NextStepFinderForDrip.findNextStep(
        edges = edgesJSON,
        nodes = nodesJSON,
        parent_step_id = 496642
      )

      val expectedRes = List(
        ChildStepIdWithCondition(496648, List(CampaignStepCondition.LinkedInProfileConnected)),
        ChildStepIdWithCondition(496653, List(CampaignStepCondition.LinkedinProfileNotConnected))
      )

      res.map { value =>

        val containsAllExpectedValues = value.forall(er => expectedRes.contains(er))

        assert(
          expectedRes.length == value.length &&
            containsAllExpectedValues
        )

      }.recover { case e =>

        println(s"Failed to find next step. ${LogHelpers.getStackTraceAsString(e)}")

        assert(false)

      }

    }

  }

  describe("Test getLastSteps") {

    it("should return the last steps of the campaign") {

      val lastStepsFut = NextStepFinderForDrip.getLastSteps(
        head_node_id = s"$head_node_id",
        edges = edgesJSON
      )

      lastStepsFut.map { value =>

        assert(value.toSet == Set(496890, 496910, 496654, 496917))

      }.recover { case e =>

        println(s"Failed to find last steps. ${LogHelpers.getStackTraceAsString(e)}")

        assert(false)

      }

    }

  }

  describe("Test getNextStepForDrip") {

    it("should return the head step when current step id is None") {

      val prospectObjectInternal = ProspectObjectInternal(
        owner_name = "Animesh",
        owner_email = "<EMAIL>",
        email_domain = Some("gmail.com"),
        invalid_email = None,
        last_contacted_at = None,
        last_replied_at = None,
        last_opened_at = None,
        last_call_made_at = None,
        list_id = None,
        prospect_category_id_custom = 214,
        prospect_category_label_color = "red",
        prospect_source = None,
        prospect_account_id = Some(23),
        prospect_account_uuid = Some("uuid"),
        prospect_account = None,
        total_opens = 0,
        total_clicks = 0,
        active_campaigns = None,
        current_campaign_id = None,
        magic_columns = List(),
        tags = None,
        flags = Json.obj(),
        latest_reply_sentiment = None
      )

      val prospectObj = ProspectObject(
        id = 1,
        owner_id = 1,
        team_id = 1,
        first_name = Some("Animesh"),
        last_name = Some("Kumar"),
        email = Some("<EMAIL>"),
        custom_fields = Json.obj(),
        list = None,
        job_title = None,
        company = None,
        linkedin_url = None,
        phone = None,
        phone_2 = None,
        phone_3 = None,
        city = None,
        state = None,
        country = None,
        timezone = None,
        prospect_category = "Dont know this",
        last_contacted_at = None,
        last_contacted_at_phone = None,
        created_at = DateTime.now().minusMonths(5),
        internal = prospectObjectInternal,
        latest_reply_sentiment_uuid = None,
        current_step_type = None,
        latest_task_done_at = None,
        prospect_uuid = Some(ProspectUuid("prs_aa_abcdefghi")),
        owner_uuid = AccountUuid("acc_aa_abcdegfhi"),
        updated_at = DateTime.now()
      )

      val data = CampaignStepsStructure.DripCampaignStepsStructure(
        nodes = nodesJSON,
        edges = edgesJSON,
        head_node_id = s"$head_node_id"
      )

      val res = ChannelSchedulerService.getNextStepForDrip(
        currentStepId = None,
        prospectObjectOpt = Some(prospectObj),
        lastSentSteps = List(),
        stepsMappedById = allCampaignSteps,
        isProspectConnectedToLinkedin = false,
        data = data
      )

      res match {

        case Left(err) =>

          println(s"Failed to find next step. err: $err}")

          assert(false)

        case Right(value) =>

          assert(value == head_node_id)

      }

    }

    it("should return next step when only 1 child step available and parent step is sent") {

      val prospectObjectInternal = ProspectObjectInternal(
        owner_name = "Animesh",
        owner_email = "<EMAIL>",
        email_domain = Some("gmail.com"),
        invalid_email = None,
        last_contacted_at = None,
        last_replied_at = None,
        last_opened_at = None,
        last_call_made_at = None,
        list_id = None,
        prospect_category_id_custom = 214,
        prospect_category_label_color = "red",
        prospect_source = None,
        prospect_account_id = Some(23),
        prospect_account_uuid = Some("uuid"),
        prospect_account = None,
        total_opens = 0,
        total_clicks = 0,
        active_campaigns = None,
        current_campaign_id = None,
        magic_columns = List(),
        tags = None,
        flags = Json.obj(),
        latest_reply_sentiment = None
      )

      val prospectObj = ProspectObject(
        id = 1,
        owner_id = 1,
        team_id = 1,
        first_name = Some("Animesh"),
        last_name = Some("Kumar"),
        email = Some("<EMAIL>"),
        custom_fields = Json.obj(),
        list = None,
        job_title = None,
        company = None,
        linkedin_url = None,
        phone = None,
        phone_2 = None,
        phone_3 = None,
        city = None,
        state = None,
        country = None,
        timezone = None,
        prospect_category = "Dont know this",
        last_contacted_at = None,
        last_contacted_at_phone = None,
        created_at = DateTime.now().minusMonths(5),
        internal = prospectObjectInternal,
        latest_reply_sentiment_uuid = None,
        current_step_type = None,
        latest_task_done_at = None,
        prospect_uuid = Some(ProspectUuid("prs_aa_abcdefghi")),
        owner_uuid = AccountUuid("acc_aa_abcdegfhi"),
        updated_at = DateTime.now()
      )

      val data = CampaignStepsStructure.DripCampaignStepsStructure(
        nodes = nodesJSON,
        edges = edgesJSON,
        head_node_id = s"$head_node_id"
      )

      val autoViewLinkedinProfileStep = LastSentStepData(
        sent_id = "some-sent-id",
        channel_type = ChannelType.LinkedinChannel,
        sent_at = DateTime.now.minusSeconds(allCampaignSteps(496642).delay), // should have been sent before now - child_step_delay
        bounced = None,
        replied = None,
        replied_at = None,
        clicked = None,
        opened = None,
        opened_at = None,
        reply_type = None,
        failure_reason = None,
        call_status = None,
        reply_sentiment = None,
        table_type = TableTypeForScheduler.Tasks,
        task_status = TaskStatusType.Done,
      )

      val res = ChannelSchedulerService.getNextStepForDrip(
        currentStepId = Some(head_node_id),
        prospectObjectOpt = Some(prospectObj),
        lastSentSteps = List(autoViewLinkedinProfileStep),
        stepsMappedById = allCampaignSteps,
        isProspectConnectedToLinkedin = false,
        data = data
      )

      res match {

        case Left(err) =>

          println(s"Failed to find next step. err: $err}")

          assert(false)

        case Right(value) =>

          assert(value == 496642)

      }

    }

    it("should return the yes flow child step id if linkedin profile is connected") {

      val current_sent_step_id: Long = 496642

      // assuming the prospect accepted our LinkedIn connection req.
      val expected_next_step_id: Long = 496648

      val prospectObjectInternal = ProspectObjectInternal(
        owner_name = "Animesh",
        owner_email = "<EMAIL>",
        email_domain = Some("gmail.com"),
        invalid_email = None,
        last_contacted_at = None,
        last_replied_at = None,
        last_opened_at = None,
        last_call_made_at = None,
        list_id = None,
        prospect_category_id_custom = 214,
        prospect_category_label_color = "red",
        prospect_source = None,
        prospect_account_id = Some(23),
        prospect_account_uuid = Some("uuid"),
        prospect_account = None,
        total_opens = 0,
        total_clicks = 0,
        active_campaigns = None,
        current_campaign_id = None,
        magic_columns = List(),
        tags = None,
        flags = Json.obj(),
        latest_reply_sentiment = None
      )

      val prospectObj = ProspectObject(
        id = 1,
        owner_id = 1,
        team_id = 1,
        first_name = Some("Animesh"),
        last_name = Some("Kumar"),
        email = Some("<EMAIL>"),
        custom_fields = Json.obj(),
        list = None,
        job_title = None,
        company = None,

        // TODO: check how the test return StepIdNotFound(2025-06-13T13:28:03.972+05:30) when linkedin url is empty
        linkedin_url = Some("https://www.linkedin.com/in/animesh-kumar/"),
        phone = None,
        phone_2 = None,
        phone_3 = None,
        city = None,
        state = None,
        country = None,
        timezone = None,
        prospect_category = "Dont know this",
        last_contacted_at = None,
        last_contacted_at_phone = None,
        created_at = DateTime.now().minusMonths(5),
        internal = prospectObjectInternal,
        latest_reply_sentiment_uuid = None,
        current_step_type = None,
        latest_task_done_at = None,
        prospect_uuid = Some(ProspectUuid("prs_aa_abcdefghi")),
        owner_uuid = AccountUuid("acc_aa_abcdegfhi"),
        updated_at = DateTime.now()
      )

      val data = CampaignStepsStructure.DripCampaignStepsStructure(
        nodes = nodesJSON,
        edges = edgesJSON,
        head_node_id = s"$head_node_id"
      )

      val autoViewLinkedinProfileStep = LastSentStepData(
        sent_id = "some-sent-id",
        channel_type = ChannelType.LinkedinChannel,
        sent_at = DateTime.now.minusSeconds(allCampaignSteps(expected_next_step_id).delay), // should have been sent before now - child_step_delay
        bounced = None,
        replied = None,
        replied_at = None,
        clicked = None,
        opened = None,
        opened_at = None,
        reply_type = None,
        failure_reason = None,
        call_status = None,
        reply_sentiment = None,
        table_type = TableTypeForScheduler.Tasks,
        task_status = TaskStatusType.Done,
      )

      val res = ChannelSchedulerService.getNextStepForDrip(
        currentStepId = Some(current_sent_step_id),
        prospectObjectOpt = Some(prospectObj),
        lastSentSteps = List(autoViewLinkedinProfileStep),
        stepsMappedById = allCampaignSteps,
        isProspectConnectedToLinkedin = true,
        data = data
      )

      res match {

        case Left(err) =>

          println(s"Failed to find next step. err: $err}")

          assert(false)

        case Right(value) =>

          assert(value == expected_next_step_id)

      }

    }

    it("should return the no flow child step id if linkedin profile is not connected") {

      val current_sent_step_id: Long = 496642

      // assuming the prospect did not accept our LinkedIn connection req.
      val expected_next_step_id: Long = 496653

      val prospectObjectInternal = ProspectObjectInternal(
        owner_name = "Animesh",
        owner_email = "<EMAIL>",
        email_domain = Some("gmail.com"),
        invalid_email = None,
        last_contacted_at = None,
        last_replied_at = None,
        last_opened_at = None,
        last_call_made_at = None,
        list_id = None,
        prospect_category_id_custom = 214,
        prospect_category_label_color = "red",
        prospect_source = None,
        prospect_account_id = Some(23),
        prospect_account_uuid = Some("uuid"),
        prospect_account = None,
        total_opens = 0,
        total_clicks = 0,
        active_campaigns = None,
        current_campaign_id = None,
        magic_columns = List(),
        tags = None,
        flags = Json.obj(),
        latest_reply_sentiment = None
      )

      val prospectObj = ProspectObject(
        id = 1,
        owner_id = 1,
        team_id = 1,
        first_name = Some("Animesh"),
        last_name = Some("Kumar"),
        email = Some("<EMAIL>"),
        custom_fields = Json.obj(),
        list = None,
        job_title = None,
        company = None,

        // TODO: check how the test return StepIdNotFound(2025-06-13T13:28:03.972+05:30) when linkedin url is empty
        linkedin_url = Some("https://www.linkedin.com/in/animesh-kumar/"),
        phone = None,
        phone_2 = None,
        phone_3 = None,
        city = None,
        state = None,
        country = None,
        timezone = None,
        prospect_category = "Dont know this",
        last_contacted_at = None,
        last_contacted_at_phone = None,
        created_at = DateTime.now().minusMonths(5),
        internal = prospectObjectInternal,
        latest_reply_sentiment_uuid = None,
        current_step_type = None,
        latest_task_done_at = None,
        prospect_uuid = Some(ProspectUuid("prs_aa_abcdefghi")),
        owner_uuid = AccountUuid("acc_aa_abcdegfhi"),
        updated_at = DateTime.now()
      )

      val data = CampaignStepsStructure.DripCampaignStepsStructure(
        nodes = nodesJSON,
        edges = edgesJSON,
        head_node_id = s"$head_node_id"
      )

      val autoViewLinkedinProfileStep = LastSentStepData(
        sent_id = "some-sent-id",
        channel_type = ChannelType.LinkedinChannel,
        sent_at = DateTime.now.minusSeconds(allCampaignSteps(expected_next_step_id).delay), // should have been sent before now - child_step_delay
        bounced = None,
        replied = None,
        replied_at = None,
        clicked = None,
        opened = None,
        opened_at = None,
        reply_type = None,
        failure_reason = None,
        call_status = None,
        reply_sentiment = None,
        table_type = TableTypeForScheduler.Tasks,
        task_status = TaskStatusType.Done,
      )

      val res = ChannelSchedulerService.getNextStepForDrip(
        currentStepId = Some(current_sent_step_id),
        prospectObjectOpt = Some(prospectObj),
        lastSentSteps = List(autoViewLinkedinProfileStep),
        stepsMappedById = allCampaignSteps,
        isProspectConnectedToLinkedin = false,
        data = data
      )

      res match {

        case Left(err) =>

          println(s"Failed to find next step. err: $err}")

          assert(false)

        case Right(value) =>

          assert(value == expected_next_step_id)

      }

    }

    it("should return the yes flow child step id if linkedin message is not sent") {

      val current_sent_step_id: Long = 496648

      // assuming the LinkedIn message was not sent.
      val expected_next_step_id: Long = 496889

      val prospectObjectInternal = ProspectObjectInternal(
        owner_name = "Animesh",
        owner_email = "<EMAIL>",
        email_domain = Some("gmail.com"),
        invalid_email = None,
        last_contacted_at = None,
        last_replied_at = None,
        last_opened_at = None,
        last_call_made_at = None,
        list_id = None,
        prospect_category_id_custom = 214,
        prospect_category_label_color = "red",
        prospect_source = None,
        prospect_account_id = Some(23),
        prospect_account_uuid = Some("uuid"),
        prospect_account = None,
        total_opens = 0,
        total_clicks = 0,
        active_campaigns = None,
        current_campaign_id = None,
        magic_columns = List(),
        tags = None,
        flags = Json.obj(),
        latest_reply_sentiment = None
      )

      val prospectObj = ProspectObject(
        id = 1,
        owner_id = 1,
        team_id = 1,
        first_name = Some("Animesh"),
        last_name = Some("Kumar"),
        email = Some("<EMAIL>"),
        custom_fields = Json.obj(),
        list = None,
        job_title = None,
        company = None,

        linkedin_url = Some("https://www.linkedin.com/in/animesh-kumar/"),
        phone = None,
        phone_2 = None,
        phone_3 = None,
        city = None,
        state = None,
        country = None,
        timezone = None,
        prospect_category = "Dont know this",
        last_contacted_at = None,
        last_contacted_at_phone = None,
        created_at = DateTime.now().minusMonths(5),
        internal = prospectObjectInternal,
        latest_reply_sentiment_uuid = None,
        current_step_type = None,
        latest_task_done_at = None,
        prospect_uuid = Some(ProspectUuid("prs_aa_abcdefghi")),
        owner_uuid = AccountUuid("acc_aa_abcdegfhi"),
        updated_at = DateTime.now()
      )

      val data = CampaignStepsStructure.DripCampaignStepsStructure(
        nodes = nodesJSON,
        edges = edgesJSON,
        head_node_id = s"$head_node_id"
      )

      val autoViewLinkedinProfileStep = LastSentStepData(
        sent_id = "some-sent-id",
        channel_type = ChannelType.LinkedinChannel,
        sent_at = DateTime.now.minusSeconds(allCampaignSteps(expected_next_step_id).delay), // should have been sent before now - child_step_delay
        bounced = None,
        replied = None,
        replied_at = None,
        clicked = None,
        opened = None,
        opened_at = None,
        reply_type = None,
        failure_reason = None,
        call_status = None,
        reply_sentiment = None,
        table_type = TableTypeForScheduler.Tasks,
        task_status = TaskStatusType.Done,
      )

      val autoSendLinkedinMessageStep = LastSentStepData(
        sent_id = "some-sent-id",
        channel_type = ChannelType.LinkedinChannel,
        sent_at = DateTime.now.minusSeconds(allCampaignSteps(expected_next_step_id).delay), // should have been sent before now - child_step_delay
        bounced = None,
        replied = None,
        replied_at = None,
        clicked = None,
        opened = None,
        opened_at = None,
        reply_type = None,
        failure_reason = Some("Unable to send linkedin message for some reason."),
        call_status = None,
        reply_sentiment = None,
        table_type = TableTypeForScheduler.Tasks,
        task_status = TaskStatusType.Done,
      )

      val res = ChannelSchedulerService.getNextStepForDrip(
        currentStepId = Some(current_sent_step_id),
        prospectObjectOpt = Some(prospectObj),
        lastSentSteps = List(
          autoSendLinkedinMessageStep,
          autoViewLinkedinProfileStep,
        ),
        stepsMappedById = allCampaignSteps,
        isProspectConnectedToLinkedin = true,
        data = data
      )

      res match {

        case Left(err) =>

          println(s"Failed to find next step. err: $err}")

          assert(false)

        case Right(value) =>

          assert(value == expected_next_step_id)

      }

    }

    it("should return the no flow child step id if linkedin message is sent") {

      val current_sent_step_id: Long = 496648

      // assuming the LinkedIn message was not sent.
      val expected_next_step_id: Long = 496909

      val prospectObjectInternal = ProspectObjectInternal(
        owner_name = "Animesh",
        owner_email = "<EMAIL>",
        email_domain = Some("gmail.com"),
        invalid_email = None,
        last_contacted_at = None,
        last_replied_at = None,
        last_opened_at = None,
        last_call_made_at = None,
        list_id = None,
        prospect_category_id_custom = 214,
        prospect_category_label_color = "red",
        prospect_source = None,
        prospect_account_id = Some(23),
        prospect_account_uuid = Some("uuid"),
        prospect_account = None,
        total_opens = 0,
        total_clicks = 0,
        active_campaigns = None,
        current_campaign_id = None,
        magic_columns = List(),
        tags = None,
        flags = Json.obj(),
        latest_reply_sentiment = None
      )

      val prospectObj = ProspectObject(
        id = 1,
        owner_id = 1,
        team_id = 1,
        first_name = Some("Animesh"),
        last_name = Some("Kumar"),
        email = Some("<EMAIL>"),
        custom_fields = Json.obj(),
        list = None,
        job_title = None,
        company = None,

        linkedin_url = Some("https://www.linkedin.com/in/animesh-kumar/"),
        phone = None,
        phone_2 = None,
        phone_3 = None,
        city = None,
        state = None,
        country = None,
        timezone = None,
        prospect_category = "Dont know this",
        last_contacted_at = None,
        last_contacted_at_phone = None,
        created_at = DateTime.now().minusMonths(5),
        internal = prospectObjectInternal,
        latest_reply_sentiment_uuid = None,
        current_step_type = None,
        latest_task_done_at = None,
        prospect_uuid = Some(ProspectUuid("prs_aa_abcdefghi")),
        owner_uuid = AccountUuid("acc_aa_abcdegfhi"),
        updated_at = DateTime.now()
      )

      val data = CampaignStepsStructure.DripCampaignStepsStructure(
        nodes = nodesJSON,
        edges = edgesJSON,
        head_node_id = s"$head_node_id"
      )

      val autoViewLinkedinProfileStep = LastSentStepData(
        sent_id = "some-sent-id",
        channel_type = ChannelType.LinkedinChannel,
        sent_at = DateTime.now.minusSeconds(allCampaignSteps(expected_next_step_id).delay), // should have been sent before now - child_step_delay
        bounced = None,
        replied = None,
        replied_at = None,
        clicked = None,
        opened = None,
        opened_at = None,
        reply_type = None,
        failure_reason = None,
        call_status = None,
        reply_sentiment = None,
        table_type = TableTypeForScheduler.Tasks,
        task_status = TaskStatusType.Done,
      )

      val autoSendLinkedinMessageStep = LastSentStepData(
        sent_id = "some-sent-id",
        channel_type = ChannelType.LinkedinChannel,
        sent_at = DateTime.now.minusSeconds(allCampaignSteps(expected_next_step_id).delay), // should have been sent before now - child_step_delay
        bounced = None,
        replied = None,
        replied_at = None,
        clicked = None,
        opened = None,
        opened_at = None,
        reply_type = None,
        failure_reason = None,
        call_status = None,
        reply_sentiment = None,
        table_type = TableTypeForScheduler.Tasks,
        task_status = TaskStatusType.Done,
      )

      val res = ChannelSchedulerService.getNextStepForDrip(
        currentStepId = Some(current_sent_step_id),
        prospectObjectOpt = Some(prospectObj),
        lastSentSteps = List(
          autoSendLinkedinMessageStep,
          autoViewLinkedinProfileStep,
        ),
        stepsMappedById = allCampaignSteps,
        isProspectConnectedToLinkedin = true,
        data = data
      )

      res match {

        case Left(err) =>

          println(s"Failed to find next step. err: $err}")

          assert(false)

        case Right(value) =>

          assert(value == expected_next_step_id)

      }

    }

    it("should return the no flow child step id if email reply not received") {

      val current_sent_step_id: Long = 496889

      val expected_next_step_id: Long = 500000

      val prospectObjectInternal = ProspectObjectInternal(
        owner_name = "Animesh",
        owner_email = "<EMAIL>",
        email_domain = Some("gmail.com"),
        invalid_email = None,
        last_contacted_at = None,
        last_replied_at = None,
        last_opened_at = None,
        last_call_made_at = None,
        list_id = None,
        prospect_category_id_custom = 214,
        prospect_category_label_color = "red",
        prospect_source = None,
        prospect_account_id = Some(23),
        prospect_account_uuid = Some("uuid"),
        prospect_account = None,
        total_opens = 0,
        total_clicks = 0,
        active_campaigns = None,
        current_campaign_id = None,
        magic_columns = List(),
        tags = None,
        flags = Json.obj(),
        latest_reply_sentiment = None
      )

      val prospectObj = ProspectObject(
        id = 1,
        owner_id = 1,
        team_id = 1,
        first_name = Some("Animesh"),
        last_name = Some("Kumar"),
        email = Some("<EMAIL>"),
        custom_fields = Json.obj(),
        list = None,
        job_title = None,
        company = None,

        linkedin_url = Some("https://www.linkedin.com/in/animesh-kumar/"),
        phone = None,
        phone_2 = None,
        phone_3 = None,
        city = None,
        state = None,
        country = None,
        timezone = None,
        prospect_category = "Dont know this",
        last_contacted_at = None,
        last_contacted_at_phone = None,
        created_at = DateTime.now().minusMonths(5),
        internal = prospectObjectInternal,
        latest_reply_sentiment_uuid = None,
        current_step_type = None,
        latest_task_done_at = None,
        prospect_uuid = Some(ProspectUuid("prs_aa_abcdefghi")),
        owner_uuid = AccountUuid("acc_aa_abcdegfhi"),
        updated_at = DateTime.now()
      )

      val data = CampaignStepsStructure.DripCampaignStepsStructure(
        nodes = nodesJSON2,
        edges = edgesJSON2,
        head_node_id = s"$head_node_id"
      )

      val autoViewLinkedinProfileStep = LastSentStepData(
        sent_id = "some-sent-id",
        channel_type = ChannelType.LinkedinChannel,
        // TODO: revert this
        sent_at = DateTime.now.minusDays(7), // should have been sent before now - child_step_delay

        bounced = None,
        replied = None,
        replied_at = None,
        clicked = None,
        opened = None,
        opened_at = None,
        reply_type = None,
        failure_reason = None,
        call_status = None,
        reply_sentiment = None,
        table_type = TableTypeForScheduler.Tasks,
        task_status = TaskStatusType.Done,
      )

      val autoSendLinkedinMessageStep = LastSentStepData(
        sent_id = "some-sent-id",
        channel_type = ChannelType.LinkedinChannel,

        // TODO: revert this
        sent_at = DateTime.now.minusDays(7), // should have been sent before now - child_step_delay

        bounced = None,
        replied = None,
        replied_at = None,
        clicked = None,
        opened = None,
        opened_at = None,
        reply_type = None,
        failure_reason = Some("Unable to send linkedin message for some reason."),
        call_status = None,
        reply_sentiment = None,
        table_type = TableTypeForScheduler.Tasks,
        task_status = TaskStatusType.Done,
      )

      val autoSendEmailStep = LastSentStepData(
        sent_id = "some-sent-id",
        channel_type = ChannelType.EmailChannel,

        // TODO: revert this
        sent_at = DateTime.now.minusDays(7), // should have been sent before now - child_step_delay

        bounced = None,
        replied = None,
        replied_at = None,
        clicked = None,
        opened = None,
        opened_at = None,
        reply_type = None,
        failure_reason = None,
        call_status = None,
        reply_sentiment = None,
        table_type = TableTypeForScheduler.EmailScheduled,
        task_status = TaskStatusType.Done,
      )

      val res = ChannelSchedulerService.getNextStepForDrip(
        currentStepId = Some(current_sent_step_id),
        prospectObjectOpt = Some(prospectObj),
        lastSentSteps = List(
          autoSendEmailStep,
          autoSendLinkedinMessageStep,
          autoViewLinkedinProfileStep,
        ),
        stepsMappedById = allCampaignSteps,
        isProspectConnectedToLinkedin = true,
        data = data
      )

      res match {

        case Left(err) =>

          println(s"Failed to find next step. err: $err}")

          assert(false)

        case Right(value) =>

          assert(value == expected_next_step_id)

      }

    }

    it("should return the yes flow child step id if the prospect replied as interested with an email") {

      val current_sent_step_id: Long = 496889

      // assuming the LinkedIn message was not sent.
      val expected_next_step_id: Long = 496890

      val prospectObjectInternal = ProspectObjectInternal(
        owner_name = "Animesh",
        owner_email = "<EMAIL>",
        email_domain = Some("gmail.com"),
        invalid_email = None,
        last_contacted_at = None,
        last_replied_at = None,
        last_opened_at = None,
        last_call_made_at = None,
        list_id = None,
        prospect_category_id_custom = 214,
        prospect_category_label_color = "red",
        prospect_source = None,
        prospect_account_id = Some(23),
        prospect_account_uuid = Some("uuid"),
        prospect_account = None,
        total_opens = 0,
        total_clicks = 0,
        active_campaigns = None,
        current_campaign_id = None,
        magic_columns = List(),
        tags = None,
        flags = Json.obj(),
        latest_reply_sentiment = None
      )

      val prospectObj = ProspectObject(
        id = 1,
        owner_id = 1,
        team_id = 1,
        first_name = Some("Animesh"),
        last_name = Some("Kumar"),
        email = Some("<EMAIL>"),
        custom_fields = Json.obj(),
        list = None,
        job_title = None,
        company = None,

        linkedin_url = Some("https://www.linkedin.com/in/animesh-kumar/"),
        phone = None,
        phone_2 = None,
        phone_3 = None,
        city = None,
        state = None,
        country = None,
        timezone = None,
        prospect_category = "Dont know this",
        last_contacted_at = None,
        last_contacted_at_phone = None,
        created_at = DateTime.now().minusMonths(5),
        internal = prospectObjectInternal,
        latest_reply_sentiment_uuid = None,
        current_step_type = None,
        latest_task_done_at = None,
        prospect_uuid = Some(ProspectUuid("prs_aa_abcdefghi")),
        owner_uuid = AccountUuid("acc_aa_abcdegfhi"),
        updated_at = DateTime.now()
      )

      val data = CampaignStepsStructure.DripCampaignStepsStructure(
        nodes = nodesJSON,
        edges = edgesJSON,
        head_node_id = s"$head_node_id"
      )

      val autoViewLinkedinProfileStep = LastSentStepData(
        sent_id = "some-sent-id",
        channel_type = ChannelType.LinkedinChannel,
        sent_at = DateTime.now.minusSeconds(allCampaignSteps(expected_next_step_id).delay), // should have been sent before now - child_step_delay
        bounced = None,
        replied = None,
        replied_at = None,
        clicked = None,
        opened = None,
        opened_at = None,
        reply_type = None,
        failure_reason = None,
        call_status = None,
        reply_sentiment = None,
        table_type = TableTypeForScheduler.Tasks,
        task_status = TaskStatusType.Done,
      )

      val autoSendLinkedinMessageStep = LastSentStepData(
        sent_id = "some-sent-id",
        channel_type = ChannelType.LinkedinChannel,
        sent_at = DateTime.now.minusSeconds(allCampaignSteps(expected_next_step_id).delay), // should have been sent before now - child_step_delay
        bounced = None,
        replied = None,
        replied_at = None,
        clicked = None,
        opened = None,
        opened_at = None,
        reply_type = None,
        failure_reason = Some("Unable to send linkedin message for some reason."),
        call_status = None,
        reply_sentiment = None,
        table_type = TableTypeForScheduler.Tasks,
        task_status = TaskStatusType.Done,
      )

      val sendAutoEmailStep = LastSentStepData(
        sent_id = "some-sent-id",
        channel_type = ChannelType.EmailChannel,
        sent_at = DateTime.now.minusSeconds(allCampaignSteps(expected_next_step_id).delay), // should have been sent before now - child_step_delay
        bounced = None,
        replied = Some(true),
        replied_at = None,
        clicked = None,
        opened = None,
        opened_at = None,
        reply_type = Some(EmailReplyType.INTERESTED),
        failure_reason = None,
        call_status = None,
        reply_sentiment = None,
        table_type = TableTypeForScheduler.EmailScheduled,
        task_status = TaskStatusType.Done,
      )

      val res = ChannelSchedulerService.getNextStepForDrip(
        currentStepId = Some(current_sent_step_id),
        prospectObjectOpt = Some(prospectObj),
        lastSentSteps = List(
          sendAutoEmailStep,
          autoSendLinkedinMessageStep,
          autoViewLinkedinProfileStep,
        ),
        stepsMappedById = allCampaignSteps,
        isProspectConnectedToLinkedin = true,
        data = data
      )

      res match {

        case Left(err) =>

          println(s"Failed to find next step. err: $err}")

          assert(false)

        case Right(value) =>

          assert(value == expected_next_step_id)

      }

    }

    it("should return the delay not met error if the delay is not met for the next step") {

      val current_sent_step_id: Long = 496889

      val prospectObjectInternal = ProspectObjectInternal(
        owner_name = "Animesh",
        owner_email = "<EMAIL>",
        email_domain = Some("gmail.com"),
        invalid_email = None,
        last_contacted_at = None,
        last_replied_at = None,
        last_opened_at = None,
        last_call_made_at = None,
        list_id = None,
        prospect_category_id_custom = 214,
        prospect_category_label_color = "red",
        prospect_source = None,
        prospect_account_id = Some(23),
        prospect_account_uuid = Some("uuid"),
        prospect_account = None,
        total_opens = 0,
        total_clicks = 0,
        active_campaigns = None,
        current_campaign_id = None,
        magic_columns = List(),
        tags = None,
        flags = Json.obj(),
        latest_reply_sentiment = None
      )

      val prospectObj = ProspectObject(
        id = 1,
        owner_id = 1,
        team_id = 1,
        first_name = Some("Animesh"),
        last_name = Some("Kumar"),
        email = Some("<EMAIL>"),
        custom_fields = Json.obj(),
        list = None,
        job_title = None,
        company = None,

        linkedin_url = Some("https://www.linkedin.com/in/animesh-kumar/"),
        phone = None,
        phone_2 = None,
        phone_3 = None,
        city = None,
        state = None,
        country = None,
        timezone = None,
        prospect_category = "Dont know this",
        last_contacted_at = None,
        last_contacted_at_phone = None,
        created_at = DateTime.now().minusMonths(5),
        internal = prospectObjectInternal,
        latest_reply_sentiment_uuid = None,
        current_step_type = None,
        latest_task_done_at = None,
        prospect_uuid = Some(ProspectUuid("prs_aa_abcdefghi")),
        owner_uuid = AccountUuid("acc_aa_abcdegfhi"),
        updated_at = DateTime.now()
      )

      val data = CampaignStepsStructure.DripCampaignStepsStructure(
        nodes = nodesJSON,
        edges = edgesJSON,
        head_node_id = s"$head_node_id"
      )

      val autoViewLinkedinProfileStep = LastSentStepData(
        sent_id = "some-sent-id",
        channel_type = ChannelType.LinkedinChannel,

        // Setting sent_at to 7 seconds ago, So that we get the delay not met error.
        sent_at = DateTime.now.minusSeconds(7),

        bounced = None,
        replied = None,
        replied_at = None,
        clicked = None,
        opened = None,
        opened_at = None,
        reply_type = None,
        failure_reason = None,
        call_status = None,
        reply_sentiment = None,
        table_type = TableTypeForScheduler.Tasks,
        task_status = TaskStatusType.Done,
      )

      val autoSendLinkedinMessageStep = LastSentStepData(
        sent_id = "some-sent-id",
        channel_type = ChannelType.LinkedinChannel,

        // Setting sent_at to 5 seconds ago, So that we get the delay not met error.
        sent_at = DateTime.now.minusSeconds(5),

        bounced = None,
        replied = None,
        replied_at = None,
        clicked = None,
        opened = None,
        opened_at = None,
        reply_type = None,
        failure_reason = Some("Unable to send linkedin message for some reason."),
        call_status = None,
        reply_sentiment = None,
        table_type = TableTypeForScheduler.Tasks,
        task_status = TaskStatusType.Done,
      )

      val autoSendEmailStep = LastSentStepData(
        sent_id = "some-sent-id",
        channel_type = ChannelType.EmailChannel,

        // Setting sent_at to 5 seconds ago, So that we get the delay not met error.
        sent_at = DateTime.now.minusSeconds(5),

        bounced = None,
        replied = None,
        replied_at = None,
        clicked = None,
        opened = None,
        opened_at = None,
        reply_type = None,
        failure_reason = None,
        call_status = None,
        reply_sentiment = None,
        table_type = TableTypeForScheduler.EmailScheduled,
        task_status = TaskStatusType.Done,
      )

      val res = ChannelSchedulerService.getNextStepForDrip(
        currentStepId = Some(current_sent_step_id),
        prospectObjectOpt = Some(prospectObj),
        lastSentSteps = List(
          autoSendEmailStep,
          autoSendLinkedinMessageStep,
          autoViewLinkedinProfileStep,
        ),
        stepsMappedById = allCampaignSteps,
        isProspectConnectedToLinkedin = true,
        data = data
      )

      res match {

        case Left(err) =>

          println(s"Failed to find next step. err: $err")

          assert(err.isInstanceOf[RejectionReasonForCampaignProspectStepSchedule.StepDelayNotMet])

        case Right(value) =>

          println(s"Found next step. value: $value")

          assert(false)

      }

    }

    it("should return the step not found error if no more child steps are present in the path") {

      val current_sent_step_id: Long = 496889

      val prospectObjectInternal = ProspectObjectInternal(
        owner_name = "Animesh",
        owner_email = "<EMAIL>",
        email_domain = Some("gmail.com"),
        invalid_email = None,
        last_contacted_at = None,
        last_replied_at = None,
        last_opened_at = None,
        last_call_made_at = None,
        list_id = None,
        prospect_category_id_custom = 214,
        prospect_category_label_color = "red",
        prospect_source = None,
        prospect_account_id = Some(23),
        prospect_account_uuid = Some("uuid"),
        prospect_account = None,
        total_opens = 0,
        total_clicks = 0,
        active_campaigns = None,
        current_campaign_id = None,
        magic_columns = List(),
        tags = None,
        flags = Json.obj(),
        latest_reply_sentiment = None
      )

      val prospectObj = ProspectObject(
        id = 1,
        owner_id = 1,
        team_id = 1,
        first_name = Some("Animesh"),
        last_name = Some("Kumar"),
        email = Some("<EMAIL>"),
        custom_fields = Json.obj(),
        list = None,
        job_title = None,
        company = None,

        linkedin_url = Some("https://www.linkedin.com/in/animesh-kumar/"),
        phone = None,
        phone_2 = None,
        phone_3 = None,
        city = None,
        state = None,
        country = None,
        timezone = None,
        prospect_category = "Dont know this",
        last_contacted_at = None,
        last_contacted_at_phone = None,
        created_at = DateTime.now().minusMonths(5),
        internal = prospectObjectInternal,
        latest_reply_sentiment_uuid = None,
        current_step_type = None,
        latest_task_done_at = None,
        prospect_uuid = Some(ProspectUuid("prs_aa_abcdefghi")),
        owner_uuid = AccountUuid("acc_aa_abcdegfhi"),
        updated_at = DateTime.now()
      )

      val data = CampaignStepsStructure.DripCampaignStepsStructure(
        nodes = nodesJSON,
        edges = edgesJSON,
        head_node_id = s"$head_node_id"
      )

      val autoViewLinkedinProfileStep = LastSentStepData(
        sent_id = "some-sent-id",
        channel_type = ChannelType.LinkedinChannel,
        // TODO: revert this
        sent_at = DateTime.now.minusDays(7), // should have been sent before now - child_step_delay

        bounced = None,
        replied = None,
        replied_at = None,
        clicked = None,
        opened = None,
        opened_at = None,
        reply_type = None,
        failure_reason = None,
        call_status = None,
        reply_sentiment = None,
        table_type = TableTypeForScheduler.Tasks,
        task_status = TaskStatusType.Done,
      )

      val autoSendLinkedinMessageStep = LastSentStepData(
        sent_id = "some-sent-id",
        channel_type = ChannelType.LinkedinChannel,

        // TODO: revert this
        sent_at = DateTime.now.minusDays(7), // should have been sent before now - child_step_delay

        bounced = None,
        replied = None,
        replied_at = None,
        clicked = None,
        opened = None,
        opened_at = None,
        reply_type = None,
        failure_reason = Some("Unable to send linkedin message for some reason."),
        call_status = None,
        reply_sentiment = None,
        table_type = TableTypeForScheduler.Tasks,
        task_status = TaskStatusType.Done,
      )

      val autoSendEmailStep = LastSentStepData(
        sent_id = "some-sent-id",
        channel_type = ChannelType.EmailChannel,

        // TODO: revert this
        sent_at = DateTime.now.minusDays(7), // should have been sent before now - child_step_delay

        bounced = None,
        replied = None,
        replied_at = None,
        clicked = None,
        opened = None,
        opened_at = None,
        reply_type = None,
        failure_reason = None,
        call_status = None,
        reply_sentiment = None,
        table_type = TableTypeForScheduler.EmailScheduled,
        task_status = TaskStatusType.Done,
      )

      val res = ChannelSchedulerService.getNextStepForDrip(
        currentStepId = Some(current_sent_step_id),
        prospectObjectOpt = Some(prospectObj),
        lastSentSteps = List(
          autoSendEmailStep,
          autoSendLinkedinMessageStep,
          autoViewLinkedinProfileStep,
        ),
        stepsMappedById = allCampaignSteps,
        isProspectConnectedToLinkedin = true,
        data = data
      )

      res match {

        case Left(err) =>

          println(s"Failed to find next step. err: $err}")

          assert(err.isInstanceOf[RejectionReasonForCampaignProspectStepSchedule.StepIdNotFound])

        case Right(value) =>

          println(s"Found next step. value: $value}")

          assert(false)

      }

    }

    // Custom nodes for HasEmail condition test
    val testNodesJSON = Json.parse(
      """
        |[
        |  {"id":"1000","data":{"type":"step","label":"initial_step"},"type":"general_task","position":{"x":0,"y":0}},
        |  {"id":"email_condition","data":{"type":"condition","label":"has_email"},"type":"has_email","position":{"x":0,"y":0}},
        |  {"id":"1002","data":{"type":"step","label":"send_email"},"type":"send_email","position":{"x":0,"y":0}},
        |  {"id":"1003","data":{"type":"step","label":"general_task"},"type":"general_task","position":{"x":0,"y":0}}
        |]
        |""".stripMargin
    ).validate[List[JsValue]].get

    // Custom edges for HasEmail condition test
    val testEdgesJSON = Json.parse(
      """
        |[
        |  {"id":"1000-email_condition","type":"nonConditionalEdge","source":"1000","target":"email_condition"},
        |  {"id":"email_condition-1002","type":"yesEdge","label":"yes","source":"email_condition","target":"1002"},
        |  {"id":"email_condition-1003","type":"noEdge","label":"no","source":"email_condition","target":"1003"}
        |]
        |""".stripMargin
    ).validate[List[JsValue]].get

    // Custom steps mapped by ID for this test
    val testStepsMappedById = Map(
      1000L -> CampaignStepWithChildren(
        id = 1000L,
        label = None,
        campaign_id = campaign_id,
        delay = 0, // No delay for this test
        step_type = CampaignStepType.GeneralTask,
        created_at = DateTime.now(),
        children = List(),
        variants = Seq()
      ),
      1002L -> CampaignStepWithChildren(
        id = 1002L,
        label = None,
        campaign_id = campaign_id,
        delay = 0,
        step_type = CampaignStepType.AutoEmailStep,
        created_at = DateTime.now(),
        children = List(),
        variants = Seq()
      ),
      1003L -> CampaignStepWithChildren(
        id = 1003L,
        label = None,
        campaign_id = campaign_id,
        delay = 0,
        step_type = CampaignStepType.GeneralTask,
        created_at = DateTime.now(),
        children = List(),
        variants = Seq()
      )
    )


    it("should follow HasEmail path when prospect has email") {

      val current_sent_step_id: Long = 1000L // Starting step
      val expected_next_step_id: Long = 1002L // Should go to email step because prospect has email

      // Create a prospect WITH email
      val prospectObjectInternal = ProspectObjectInternal(
        owner_name = "John",
        owner_email = "<EMAIL>",
        email_domain = Some("example.com"),
        invalid_email = None,
        last_contacted_at = None,
        last_replied_at = None,
        last_opened_at = None,
        last_call_made_at = None,
        list_id = None,
        prospect_category_id_custom = 214,
        prospect_category_label_color = "red",
        prospect_source = None,
        prospect_account_id = Some(23),
        prospect_account_uuid = Some("uuid"),
        prospect_account = None,
        total_opens = 0,
        total_clicks = 0,
        active_campaigns = None,
        current_campaign_id = None,
        magic_columns = List(),
        tags = None,
        flags = Json.obj(),
        latest_reply_sentiment = None
      )

      val prospectObj = ProspectObject(
        id = 1,
        owner_id = 1,
        team_id = 1,
        first_name = Some("John"),
        last_name = Some("Doe"),
        email = Some("<EMAIL>"), // HAS EMAIL - this is the key part
        custom_fields = Json.obj(),
        list = None,
        job_title = None,
        company = None,
        linkedin_url = None,
        phone = None,
        phone_2 = None,
        phone_3 = None,
        city = None,
        state = None,
        country = None,
        timezone = None,
        prospect_category = "Test Category",
        last_contacted_at = None,
        last_contacted_at_phone = None,
        created_at = DateTime.now().minusMonths(1),
        internal = prospectObjectInternal,
        latest_reply_sentiment_uuid = None,
        current_step_type = None,
        latest_task_done_at = None,
        prospect_uuid = Some(ProspectUuid("prs_aa_test123")),
        owner_uuid = AccountUuid("acc_aa_test123"),
        updated_at = DateTime.now()
      )

      val data = CampaignStepsStructure.DripCampaignStepsStructure(
        nodes = testNodesJSON,
        edges = testEdgesJSON,
        head_node_id = "1000"
      )

      // Create lastSentSteps with proper timing to avoid delay issues
      val lastSentStep = LastSentStepData(
        sent_id = "test-sent-id",
        channel_type = ChannelType.GeneralChannel,
        sent_at = DateTime.now.minusDays(7), // Sent long ago to satisfy delay requirements
        bounced = None,
        replied = None,
        replied_at = None,
        clicked = None,
        opened = None,
        opened_at = None,
        reply_type = None,
        failure_reason = None,
        call_status = None,
        reply_sentiment = None,
        table_type = TableTypeForScheduler.Tasks,
        task_status = TaskStatusType.Done,
      )

      val res = ChannelSchedulerService.getNextStepForDrip(
        currentStepId = Some(current_sent_step_id),
        prospectObjectOpt = Some(prospectObj),
        lastSentSteps = List(lastSentStep),
        stepsMappedById = testStepsMappedById,
        isProspectConnectedToLinkedin = false,
        data = data
      )

      res match {
        case Left(err) =>
          println(s"Test failed with error: $err")
          assert(false)

        case Right(stepId) =>
          println(s"Expected: $expected_next_step_id, Got: $stepId")
          assert(stepId == expected_next_step_id)
      }

    }

    it("should not follow HasEmail path when prospect has email") {

      val current_sent_step_id: Long = 1000L // Starting step
      val expected_next_step_id: Long = 1003L // Should go to general step because prospect does not have email

      // Create a prospect WITH email
      val prospectObjectInternal = ProspectObjectInternal(
        owner_name = "John",
        owner_email = "<EMAIL>",
        email_domain = Some("example.com"),
        invalid_email = None,
        last_contacted_at = None,
        last_replied_at = None,
        last_opened_at = None,
        last_call_made_at = None,
        list_id = None,
        prospect_category_id_custom = 214,
        prospect_category_label_color = "red",
        prospect_source = None,
        prospect_account_id = Some(23),
        prospect_account_uuid = Some("uuid"),
        prospect_account = None,
        total_opens = 0,
        total_clicks = 0,
        active_campaigns = None,
        current_campaign_id = None,
        magic_columns = List(),
        tags = None,
        flags = Json.obj(),
        latest_reply_sentiment = None
      )

      val prospectObj = ProspectObject(
        id = 1,
        owner_id = 1,
        team_id = 1,
        first_name = Some("John"),
        last_name = Some("Doe"),
        email = None, // No EMAIL - this is the key part
        custom_fields = Json.obj(),
        list = None,
        job_title = None,
        company = None,
        linkedin_url = None,
        phone = None,
        phone_2 = None,
        phone_3 = None,
        city = None,
        state = None,
        country = None,
        timezone = None,
        prospect_category = "Test Category",
        last_contacted_at = None,
        last_contacted_at_phone = None,
        created_at = DateTime.now().minusMonths(1),
        internal = prospectObjectInternal,
        latest_reply_sentiment_uuid = None,
        current_step_type = None,
        latest_task_done_at = None,
        prospect_uuid = Some(ProspectUuid("prs_aa_test123")),
        owner_uuid = AccountUuid("acc_aa_test123"),
        updated_at = DateTime.now()
      )


      val data = CampaignStepsStructure.DripCampaignStepsStructure(
        nodes = testNodesJSON,
        edges = testEdgesJSON,
        head_node_id = "1000"
      )

      // Create lastSentSteps with proper timing to avoid delay issues
      val lastSentStep = LastSentStepData(
        sent_id = "test-sent-id",
        channel_type = ChannelType.GeneralChannel,
        sent_at = DateTime.now.minusDays(7), // Sent long ago to satisfy delay requirements
        bounced = None,
        replied = None,
        replied_at = None,
        clicked = None,
        opened = None,
        opened_at = None,
        reply_type = None,
        failure_reason = None,
        call_status = None,
        reply_sentiment = None,
        table_type = TableTypeForScheduler.Tasks,
        task_status = TaskStatusType.Done,
      )

      val res = ChannelSchedulerService.getNextStepForDrip(
        currentStepId = Some(current_sent_step_id),
        prospectObjectOpt = Some(prospectObj),
        lastSentSteps = List(lastSentStep),
        stepsMappedById = testStepsMappedById,
        isProspectConnectedToLinkedin = false,
        data = data
      )

      res match {
        case Left(err) =>
          println(s"Test failed with error: $err")
          assert(false)

        case Right(stepId) =>
          println(s"Expected: $expected_next_step_id, Got: $stepId")
          assert(stepId == expected_next_step_id)
      }

    }

    // Custom nodes for HasPhoneNumber condition test
    val testHasPhoneNodesJSON = Json.parse(
      """
        |[
        |  {"id":"2000","data":{"type":"step","label":"initial_step"},"type":"general_task","position":{"x":0,"y":0}},
        |  {"id":"phone_condition","data":{"type":"condition","label":"has_phone_number"},"type":"has_phone_number","position":{"x":0,"y":0}},
        |  {"id":"2002","data":{"type":"step","label":"send_sms"},"type":"send_sms","position":{"x":0,"y":0}},
        |  {"id":"2003","data":{"type":"step","label":"send_email"},"type":"send_email","position":{"x":0,"y":0}}
        |]
        |""".stripMargin
    ).validate[List[JsValue]].get

    // Custom edges for HasPhoneNumber condition test
    val testHasPhoneEdgesJSON = Json.parse(
      """
        |[
        |  {"id":"2000-phone_condition","type":"nonConditionalEdge","source":"2000","target":"phone_condition"},
        |  {"id":"phone_condition-2002","type":"yesEdge","label":"yes","source":"phone_condition","target":"2002"},
        |  {"id":"phone_condition-2003","type":"noEdge","label":"no","source":"phone_condition","target":"2003"}
        |]
        |""".stripMargin
    ).validate[List[JsValue]].get

    // Custom steps mapped by ID for this test
    val testHasPoneStepsMappedById = Map(
      2000L -> CampaignStepWithChildren(
        id = 2000L,
        label = None,
        campaign_id = campaign_id,
        delay = 0,
        step_type = CampaignStepType.GeneralTask,
        created_at = DateTime.now(),
        children = List(),
        variants = Seq()
      ),
      2002L -> CampaignStepWithChildren(
        id = 2002L,
        label = None,
        campaign_id = campaign_id,
        delay = 0,
        step_type = CampaignStepType.SmsMessage,
        created_at = DateTime.now(),
        children = List(),
        variants = Seq()
      ),
      2003L -> CampaignStepWithChildren(
        id = 2003L,
        label = None,
        campaign_id = campaign_id,
        delay = 0,
        step_type = CampaignStepType.AutoEmailStep,
        created_at = DateTime.now(),
        children = List(),
        variants = Seq()
      )
    )

    it("should not follow HasPhoneNumber path when prospect does not have phone") {

      val current_sent_step_id: Long = 2000L // Starting step
      val expected_next_step_id: Long = 2003L // Should go to Email step because prospect does not have phone

      // Create a prospect WITH phone number
      val prospectObjectInternal = ProspectObjectInternal(
        owner_name = "Mike",
        owner_email = "<EMAIL>",
        email_domain = Some("example.com"),
        invalid_email = None,
        last_contacted_at = None,
        last_replied_at = None,
        last_opened_at = None,
        last_call_made_at = None,
        list_id = None,
        prospect_category_id_custom = 214,
        prospect_category_label_color = "red",
        prospect_source = None,
        prospect_account_id = Some(23),
        prospect_account_uuid = Some("uuid"),
        prospect_account = None,
        total_opens = 0,
        total_clicks = 0,
        active_campaigns = None,
        current_campaign_id = None,
        magic_columns = List(),
        tags = None,
        flags = Json.obj(),
        latest_reply_sentiment = None
      )

      val prospectObj = ProspectObject(
        id = 3,
        owner_id = 1,
        team_id = 1,
        first_name = Some("Mike"),
        last_name = Some("Johnson"),
        email = Some("<EMAIL>"),
        custom_fields = Json.obj(),
        list = None,
        job_title = None,
        company = None,
        linkedin_url = None,
        phone = None, // No PHONE - this is the key part
        phone_2 = None,
        phone_3 = None,
        city = None,
        state = None,
        country = None,
        timezone = None,
        prospect_category = "Test Category",
        last_contacted_at = None,
        last_contacted_at_phone = None,
        created_at = DateTime.now().minusMonths(1),
        internal = prospectObjectInternal,
        latest_reply_sentiment_uuid = None,
        current_step_type = None,
        latest_task_done_at = None,
        prospect_uuid = Some(ProspectUuid("prs_aa_test789")),
        owner_uuid = AccountUuid("acc_aa_test789"),
        updated_at = DateTime.now()
      )


      val data = CampaignStepsStructure.DripCampaignStepsStructure(
        nodes = testHasPhoneNodesJSON,
        edges = testHasPhoneEdgesJSON,
        head_node_id = "2000"
      )

      val lastSentStep = LastSentStepData(
        sent_id = "test-sent-id-3",
        channel_type = ChannelType.GeneralChannel,
        sent_at = DateTime.now.minusDays(7),
        bounced = None,
        replied = None,
        replied_at = None,
        clicked = None,
        opened = None,
        opened_at = None,
        reply_type = None,
        failure_reason = None,
        call_status = None,
        reply_sentiment = None,
        table_type = TableTypeForScheduler.Tasks,
        task_status = TaskStatusType.Done,
      )

      val res = ChannelSchedulerService.getNextStepForDrip(
        currentStepId = Some(current_sent_step_id),
        prospectObjectOpt = Some(prospectObj),
        lastSentSteps = List(lastSentStep),
        stepsMappedById = testHasPoneStepsMappedById,
        isProspectConnectedToLinkedin = false,
        data = data
      )

      res match {
        case Left(err) =>
          println(s"Test failed with error: $err")
          assert(false)

        case Right(stepId) =>
          println(s"Expected: $expected_next_step_id, Got: $stepId")
          assert(stepId == expected_next_step_id)
      }

    }

    it("should return NoProspectFound when prospectObjectOpt is None") {

      val current_sent_step_id: Long = head_node_id

      val data = CampaignStepsStructure.DripCampaignStepsStructure(
        nodes = nodesJSON,
        edges = edgesJSON,
        head_node_id = s"$head_node_id"
      )

      val res = ChannelSchedulerService.getNextStepForDrip(
        currentStepId = Some(current_sent_step_id),
        prospectObjectOpt = None, // NO PROSPECT - this is the key part
        lastSentSteps = List(),
        stepsMappedById = allCampaignSteps,
        isProspectConnectedToLinkedin = false,
        data = data
      )

      res match {
        case Left(err) =>

          println(s"Got expected error: $err")

          assert(err.isInstanceOf[RejectionReasonForCampaignProspectStepSchedule.NoProspectFound])

        case Right(stepId) =>

          println(s"Unexpected success with stepId: $stepId")

          assert(false) // Should not succeed

      }

    }

    it("should follow Opened path when prospect opened email within delay period") {

      val current_sent_step_id: Long = 5000L
      val expected_next_step_id: Long = 5002L // Should go to "opened" path

      // Create a prospect with email open history
      val prospectObjectInternal = ProspectObjectInternal(
        owner_name = "Alice",
        owner_email = "<EMAIL>",
        email_domain = Some("example.com"),
        invalid_email = None,
        last_contacted_at = None,
        last_replied_at = None,
        last_opened_at = Some(DateTime.now().minusHours(1)), // Opened email 1 hour ago
        last_call_made_at = None,
        list_id = None,
        prospect_category_id_custom = 214,
        prospect_category_label_color = "red",
        prospect_source = None,
        prospect_account_id = Some(23),
        prospect_account_uuid = Some("uuid"),
        prospect_account = None,
        total_opens = 1, // Has opened emails
        total_clicks = 0,
        active_campaigns = None,
        current_campaign_id = None,
        magic_columns = List(),
        tags = None,
        flags = Json.obj(),
        latest_reply_sentiment = None
      )

      val prospectObj = ProspectObject(
        id = 6,
        owner_id = 1,
        team_id = 1,
        first_name = Some("Alice"),
        last_name = Some("Johnson"),
        email = Some("<EMAIL>"),
        custom_fields = Json.obj(),
        list = None,
        job_title = None,
        company = None,
        linkedin_url = None,
        phone = None,
        phone_2 = None,
        phone_3 = None,
        city = None,
        state = None,
        country = None,
        timezone = None,
        prospect_category = "Test Category",
        last_contacted_at = None,
        last_contacted_at_phone = None,
        created_at = DateTime.now().minusMonths(1),
        internal = prospectObjectInternal,
        latest_reply_sentiment_uuid = None,
        current_step_type = None,
        latest_task_done_at = None,
        prospect_uuid = Some(ProspectUuid("prs_aa_test111")),
        owner_uuid = AccountUuid("acc_aa_test111"),
        updated_at = DateTime.now()
      )

      // Custom nodes for email opened condition test
      // not opened edge node is removed
      val testNodesJSON = Json.parse(
        """
          |[
          |  {"id":"5000","data":{"type":"step","label":"send_email"},"type":"send_email","position":{"x":0,"y":0}},
          |  {"id":"opened_condition","data":{"type":"condition","label":"has_opened"},"type":"has_opened","position":{"x":0,"y":0}},
          |  {"id":"5002","data":{"type":"step","label":"follow_up_email"},"type":"send_email","position":{"x":0,"y":0}}
          |]
          |""".stripMargin
      ).validate[List[JsValue]].get

      // Custom edges for email opened condition test
      // not opened edge is removed
      val testEdgesJSON = Json.parse(
        """
          |[
          |  {"id":"5000-opened_condition","type":"nonConditionalEdge","source":"5000","target":"opened_condition"},
          |  {"id":"opened_condition-5002","type":"yesEdge","label":"yes","source":"opened_condition","target":"5002"}
          |]
          |""".stripMargin
      ).validate[List[JsValue]].get

      val testStepsMappedById = Map(
        5000L -> CampaignStepWithChildren(
          id = 5000L,
          label = None,
          campaign_id = campaign_id,
          delay = 64800, // 18 hour delay
          step_type = CampaignStepType.AutoEmailStep,
          created_at = DateTime.now(),
          children = List(),
          variants = Seq()
        ),
        5002L -> CampaignStepWithChildren(
          id = 5002L,
          label = None,
          campaign_id = campaign_id,
          delay = 64800,
          step_type = CampaignStepType.AutoEmailStep,
          created_at = DateTime.now(),
          children = List(),
          variants = Seq()
        ),
      )

      val data = CampaignStepsStructure.DripCampaignStepsStructure(
        nodes = testNodesJSON,
        edges = testEdgesJSON,
        head_node_id = "5000"
      )

      // Create email step that was sent and opened
      val emailStep = LastSentStepData(
        sent_id = "email-sent-id",
        channel_type = ChannelType.EmailChannel,
        sent_at = DateTime.now.minusSeconds(testStepsMappedById(expected_next_step_id).delay),
        bounced = None,
        replied = None,
        replied_at = None,
        clicked = None,
        opened = Some(true), // EMAIL WAS OPENED - this is the key part
        opened_at = Some(DateTime.now().minusHours(1)), // Opened 1 hour ago
        reply_type = None,
        failure_reason = None,
        call_status = None,
        reply_sentiment = None,
        table_type = TableTypeForScheduler.EmailScheduled,
        task_status = TaskStatusType.Done,
      )

      val res = ChannelSchedulerService.getNextStepForDrip(
        currentStepId = Some(current_sent_step_id),
        prospectObjectOpt = Some(prospectObj),
        lastSentSteps = List(emailStep),
        stepsMappedById = testStepsMappedById,
        isProspectConnectedToLinkedin = false,
        data = data
      )

      res match {
        case Left(err) =>
          println(s"Test failed with error: $err")
          assert(false)

        case Right(stepId) =>
          println(s"Expected: $expected_next_step_id, Got: $stepId")
          assert(stepId == expected_next_step_id)
      }
    }

    it("should follow NotOpened path when prospect hasn't opened email after delay") {

      val current_sent_step_id: Long = 5000L
      val expected_next_step_id: Long = 5003L // Should go to "not opened" path

      // Create a prospect with NO email open history
      val prospectObjectInternal = ProspectObjectInternal(
        owner_name = "Charlie",
        owner_email = "<EMAIL>",
        email_domain = Some("example.com"),
        invalid_email = None,
        last_contacted_at = None,
        last_replied_at = None,
        last_opened_at = None, // NO email opens
        last_call_made_at = None,
        list_id = None,
        prospect_category_id_custom = 214,
        prospect_category_label_color = "red",
        prospect_source = None,
        prospect_account_id = Some(23),
        prospect_account_uuid = Some("uuid"),
        prospect_account = None,
        total_opens = 0, // NO opens
        total_clicks = 0,
        active_campaigns = None,
        current_campaign_id = None,
        magic_columns = List(),
        tags = None,
        flags = Json.obj(),
        latest_reply_sentiment = None
      )

      val prospectObj = ProspectObject(
        id = 7,
        owner_id = 1,
        team_id = 1,
        first_name = Some("Charlie"),
        last_name = Some("Brown"),
        email = Some("<EMAIL>"),
        custom_fields = Json.obj(),
        list = None,
        job_title = None,
        company = None,
        linkedin_url = None,
        phone = None,
        phone_2 = None,
        phone_3 = None,
        city = None,
        state = None,
        country = None,
        timezone = None,
        prospect_category = "Test Category",
        last_contacted_at = None,
        last_contacted_at_phone = None,
        created_at = DateTime.now().minusMonths(1),
        internal = prospectObjectInternal,
        latest_reply_sentiment_uuid = None,
        current_step_type = None,
        latest_task_done_at = None,
        prospect_uuid = Some(ProspectUuid("prs_aa_test222")),
        owner_uuid = AccountUuid("acc_aa_test222"),
        updated_at = DateTime.now()
      )

      // Same nodes as previous test
      val testNodesJSON = Json.parse(
        """
          |[
          |  {"id":"5000","data":{"type":"step","label":"send_email"},"type":"send_email","position":{"x":0,"y":0}},
          |  {"id":"opened_condition","data":{"type":"condition","label":"has_opened"},"type":"has_opened","position":{"x":0,"y":0}},
          |  {"id":"5002","data":{"type":"step","label":"follow_up_email"},"type":"send_email","position":{"x":0,"y":0}},
          |  {"id":"5003","data":{"type":"step","label":"reminder_email"},"type":"send_email","position":{"x":0,"y":0}}
          |]
          |""".stripMargin
      ).validate[List[JsValue]].get

      // Same edges as previous test
      val testEdgesJSON = Json.parse(
        """
          |[
          |  {"id":"5000-opened_condition","type":"nonConditionalEdge","source":"5000","target":"opened_condition"},
          |  {"id":"opened_condition-5002","type":"yesEdge","label":"yes","source":"opened_condition","target":"5002"},
          |  {"id":"opened_condition-5003","type":"noEdge","label":"no","source":"opened_condition","target":"5003"}
          |]
          |""".stripMargin
      ).validate[List[JsValue]].get

      val testStepsMappedById = Map(
        5000L -> CampaignStepWithChildren(
          id = 5000L,
          label = None,
          campaign_id = campaign_id,
          delay = 64800,
          step_type = CampaignStepType.AutoEmailStep,
          created_at = DateTime.now(),
          children = List(),
          variants = Seq()
        ),
        5002L -> CampaignStepWithChildren(
          id = 5002L,
          label = None,
          campaign_id = campaign_id,
          delay = 64800,
          step_type = CampaignStepType.AutoEmailStep,
          created_at = DateTime.now(),
          children = List(),
          variants = Seq()
        ),
        5003L -> CampaignStepWithChildren(
          id = 5003L,
          label = None,
          campaign_id = campaign_id,
          delay = 64800,
          step_type = CampaignStepType.AutoEmailStep,
          created_at = DateTime.now(),
          children = List(),
          variants = Seq()
        )
      )

      val data = CampaignStepsStructure.DripCampaignStepsStructure(
        nodes = testNodesJSON,
        edges = testEdgesJSON,
        head_node_id = "5000"
      )

      // Create email step that was sent but NOT opened
      val emailStep = LastSentStepData(
        sent_id = "email-sent-id-2",
        channel_type = ChannelType.EmailChannel,
        sent_at = DateTime.now.minusSeconds(testStepsMappedById(expected_next_step_id).delay),
        bounced = None,
        replied = None,
        replied_at = None,
        clicked = None,
        opened = None, // EMAIL WAS NOT OPENED - this is the key part
        opened_at = None, // No open timestamp
        reply_type = None,
        failure_reason = None,
        call_status = None,
        reply_sentiment = None,
        table_type = TableTypeForScheduler.EmailScheduled,
        task_status = TaskStatusType.Done,
      )

      val res = ChannelSchedulerService.getNextStepForDrip(
        currentStepId = Some(current_sent_step_id),
        prospectObjectOpt = Some(prospectObj),
        lastSentSteps = List(emailStep),
        stepsMappedById = testStepsMappedById,
        isProspectConnectedToLinkedin = false,
        data = data
      )

      res match {
        case Left(err) =>
          println(s"Test failed with error: $err")
          assert(false)

        case Right(stepId) =>
          println(s"Expected: $expected_next_step_id, Got: $stepId")
          assert(stepId == expected_next_step_id)
      }
    }

  }

}
