package app.utils.mq.channels

import org.apache.pekko.actor.ActorSystem
import api.accounts.email.models.EmailServiceProvider
import api.accounts.models.{AccountProfileInfo, OrgId, ProspectAccountUuid}
import api.accounts.service.AccountOrgBillingRelatedService
import api.accounts.{Account, AccountAccess, AccountDAO, AccountMetadata, AccountService, AccountType, AccountUuid, OrgCountData, OrgMetadata, OrgPlan, OrgSettings, OrganizationRole, OrganizationWithCurrentData, RepTrackingHosts, TeamId}
import api.calendar_app.{CalendarAppService, CalendarUserId}
import api.calendar_app.models.CalendarAccountData
import api.campaigns.models.CampaignStepData.{AutoEmailStep, GeneralTaskData, LinkedinConnectionRequestData, LinkedinInmailData, LinkedinMessageData, LinkedinViewProfile, getSubjectAndBodyFromStepData}
import api.campaigns.models.CampaignStepType.{LinkedinConnectionRequest, LinkedinInmail, ManualEmailStep}
import sr_scheduler.models.{CampaignAIGenerationContext, CampaignEmailPriority, CampaignForScheduling, CampaignWarmupSetting, ChannelData, ChannelType, EmailScheduledNew, EmailSettingCreateEmailSchedule, GeneratedContent, LinkedinSettingCreateSchedule, ScheduledProspectsCountForCampaign, ScheduledProspectsCountForCampaignEmail, SelectedCalendarData}
import api.campaigns.models.{CampaignEmailSettingsId, CampaignStepData, CampaignStepType, CampaignStepsStructure, CampaignType, CampaignTypeData, ChannelStepType, PreviousFollowUpData, SenderRotationStats, StepContext, SubjectAndBody}
import api.campaigns.services.{CampaignDAOService, CampaignId, CampaignProspectService, CampaignProspectTimezonesJedisService, CampaignService, CampaignsMissingMergeTagService}
import api.campaigns.{CPCompleted, CampaignEditedPreviewEmail, CampaignEditedPreviewEmailDAO, CampaignProspectDAO, CampaignProspectUpdateScheduleStatus, CampaignStepDAO, CampaignStepVariantDAO, CampaignStepVariantForScheduling, CampaignStepWithChildren, EmailsScheduledCount, PreviousFollowUp}
import api.columns.InternalMergeTagValuesForProspect
import api.emails.dao_service.EmailScheduledDAOService
import api.emails.models.EmailSendingFlow
import api.emails.{CampaignProspectStepScheduleLogsDAO, EmailMessageDataDAO, EmailScheduledDAO, EmailScheduledNewAfterSaving, EmailSettingDAO}
import api.general.GeneralSettingDAO
import api.gpt.CreateStepsRequest
import api.gpt.ai_hyperpersonalized.AIHyperPersonalizedGenerator
import api.linkedin.LinkedinSettingDAO
import api.linkedin.models.LinkedInServiceProvider
import api.prospects.models.{ProspectAccountsId, ProspectDataForChannelScheduling, ProspectId, ProspectTouchedType, StepId}
import api.prospects.service.ProspectServiceV2
import org.joda.time.DateTime
import org.scalamock.scalatest.AsyncMockFactory
import org.scalatest.funspec.AsyncFunSpec
import play.api.libs.ws.ahc.AhcWSClient
import sr_scheduler.{CampaignStatus, models}
import utils.{ISRLogger, SRLogger}
import utils.email.{EmailOptionsForGetBodies, EmailServiceBody, EmailServiceCompanion}
import utils.emailvalidation.EmailValidationService
import utils.mq.channel_scheduler.channels.{ChannelId, ChannelSchedulerTrait, ContentValidationError, Count, EmailChannelScheduler, GeneralChannelScheduler, LinkedinChannelScheduler, NextStepFinderForDrip, StepType, StepTypeAndCount}
import utils.mq.channel_scheduler.{ChannelSchedulerService, FetchCampaignStepsData, MqCampaignSchedulingMetadataMigration, SchedulerMapStepIdAndDelay}
import utils.mq.webhook.MQWebhookCompleted
import utils.templating.TemplateService

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}
import eventframework.{ProspectFieldsResult, ProspectObject, ProspectObjectInternal}
import api.rep_tracking_hosts.service.RepTrackingHostService
import api.tasks
import api.tasks.models.TaskType.SendLinkedinConnectionRequest
import api.tasks.models.{NewTask, TaskPriority}
import api.tasks.pgDao.TaskPgDAO
import api.tasks.services.{CreateTaskError, TaskService}
import app.test_fixtures.accounts.OrgCountDataFixture
import app.test_fixtures.organizationa.{OrgMetadataFixture, OrgPlanFixture}
import app.test_fixtures.prospect.ProspectAccountFixture.prospectAccount
import app.test_fixtures.prospect.ProspectFixtures
import app.test_fixtures.scheduler.EmailChannelSchedulerFixtures
import api.campaigns.CampaignEditedPreviewEmailDAO
import api.llm.dao.LlmAuditLogDAO
import api.prospects.ProspectUuid
import io.smartreach.esp.api.emails.EmailSettingId
import org.apache.pekko.stream.Materializer
import play.api.libs.json.{Json, __}
import play.api.libs.ws.WSClient
import play.test.Helpers
import scalikejdbc.DBSession
import sr_scheduler.models.CampaignForScheduling.CampaignEmailSettingForScheduler
import sr_scheduler.models.ChannelDataForScheduling.{EmailChannelDataForScheduling, LinkedinChannelDataForScheduling}
import utils.cache_utils.model.CacheIdKeyForLock
import utils.cache_utils.service.SrRedisSimpleLockServiceV2
import utils.dateTime.SrDateTimeUtils
import utils.dbutils.{DBUtils, DbAndSession}
import utils.email_notification.service.EmailNotificationService
import utils.emailvalidation.models.EmailValidationPriority
import utils.featureflags.dao.OrgMetadataDAO
import utils.featureflags.services.OrgMetadataService
import utils.helpers.LogHelpers
import utils.mq.ai_content_generation.{MqAiContentGenerationPublisher, MqAiContentGenerationRequest}
import utils.mq.channel_scheduler.channels.service.EmailSchedulerJedisService
import utils.random.SrRandomUtils
import utils.shuffle.SrShuffleUtils
import utils.uuid.SrUuidUtils
import utils.testapp.TestAppExecutionContext
import utils_deploy.rolling_updates.models.SrRollingUpdateFeature
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService

import scala.collection.immutable.Vector

class ChannelSchedulerTraitSpec extends AsyncFunSpec with AsyncMockFactory {

  def getSubjectAndBody(
    campaignStepsStructure: CampaignStepsStructure,
    stepsMappedById: Map[Long, CampaignStepWithChildren],
  ): SubjectAndBody = {

    val orderedSteps = campaignStepsStructure match {
      case e: CampaignStepsStructure.EmailCampaignStepsStructure => e.orderedStepIds
      case c: CampaignStepsStructure.MultichannelCampaignStepsStructure => c.orderedStepIds
      case _: CampaignStepsStructure.DripCampaignStepsStructure => Vector()
    }

    val headVariant = stepsMappedById(orderedSteps.head).variants.head

    CampaignStepData.getSubjectAndBodyFromStepData(
      stepData = headVariant.step_data
    )

  }


  given system: ActorSystem = TestAppExecutionContext.actorSystem
  given ec: ExecutionContext = system.dispatcher
  given wSClient: AhcWSClient = TestAppExecutionContext.wsClient
  given Logger: SRLogger = new SRLogger("ChannelSchedulerTraitSpec")
  val Error = new Throwable("Error")
  val accountDAO: AccountDAO = mock[AccountDAO]
  val templateService: TemplateService = new TemplateService
  val campaignProspectDAO: CampaignProspectDAO = mock[CampaignProspectDAO]
  val emailSettingDAO: EmailSettingDAO = mock[EmailSettingDAO]
  val mqCampaignSchedulingMetadataMigration = mock[MqCampaignSchedulingMetadataMigration]
  val campaignStepVariantDAO: CampaignStepVariantDAO = mock[CampaignStepVariantDAO]
  val mqWebhookCompleted: MQWebhookCompleted = mock[MQWebhookCompleted]
  val accountService: AccountService = mock[AccountService]
  val emailValidationService: EmailValidationService = mock[EmailValidationService]
  val emailNotificationService: EmailNotificationService = mock[EmailNotificationService]
  val campaignStepDAO: CampaignStepDAO = mock[CampaignStepDAO]
  val generalSettingDAO = mock[GeneralSettingDAO]
  val linkedinSettingDAO = mock[LinkedinSettingDAO]
  val srRandomUtils = mock[SrRandomUtils]
  val srShuffleUtils = mock[SrShuffleUtils]
  val taskDAO = mock[TaskPgDAO]
  val taskService = mock[TaskService]
  val campaignEditedPreviewEmailDAO: CampaignEditedPreviewEmailDAO = mock[CampaignEditedPreviewEmailDAO]
  val campaignProspectStepScheduleLogsDAO = mock[CampaignProspectStepScheduleLogsDAO]
  val campaignService: CampaignService = mock[CampaignService]
  val emailServiceCompanion: EmailServiceCompanion = mock[EmailServiceCompanion]
  val campaignProspectService = mock[CampaignProspectService]
  val orgMetadataService = mock[OrgMetadataService]
  val campaignsMissingMergeTagService = mock[CampaignsMissingMergeTagService]
  val calendarAppService = mock[CalendarAppService]
  val emailSchedulerJedisService = mock[EmailSchedulerJedisService]
  val srRedisSimpleLockServiceV2 = mock[SrRedisSimpleLockServiceV2]
  val repTrackingHostService = mock[RepTrackingHostService]
  val accountOrgBillingRelatedService = mock[AccountOrgBillingRelatedService]
  val srRollingUpdateCoreService = mock[SrRollingUpdateCoreService]
//  val dbUtils = mock[DBUtils]
  val emailScheduledDAOService = mock[EmailScheduledDAOService]
  val srDateTimeUtils = mock[SrDateTimeUtils]
  val campaignDAOService: CampaignDAOService = mock[CampaignDAOService]
  val aiHyperPersonalizedService = mock[AIHyperPersonalizedGenerator]
  val llmAuditLogDAO = mock[LlmAuditLogDAO]
  val srUuidUtils: SrUuidUtils = mock[SrUuidUtils]

  //  (
//    templateService = templateService,
//    campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO,
//    emailScheduledDAO = emailScheduledDAO
//  )

  //val emailChannelScheduler: EmailChannelScheduler = mock[EmailChannelScheduler]
  val emailChannelScheduler: EmailChannelScheduler = new EmailChannelScheduler(

    emailValidationService = emailValidationService,
    emailServiceCompanion = emailServiceCompanion,
//    accountService = accountService,
    srRandomUtils = srRandomUtils,
    orgMetadataService = orgMetadataService,
    emailSettingDAO = emailSettingDAO,
    emailSchedulerJedisService = emailSchedulerJedisService,
    repTrackingHostService = repTrackingHostService,
//    dbUtils = dbUtils,
    mqCampaignSchedulingMetadataMigration = mqCampaignSchedulingMetadataMigration,
    emailScheduledDAOService = emailScheduledDAOService,
    srDateTimeUtils = srDateTimeUtils,
    campaignProspectService = campaignProspectService,
    aiHyperPersonalizedService = aiHyperPersonalizedService,
    campaignDAOService = campaignDAOService,
    srRollingUpdateCoreService = srRollingUpdateCoreService,
    llmAuditLogDAO = llmAuditLogDAO,
    srUuidUtils = srUuidUtils
  )

  val generalChannelScheduler = new GeneralChannelScheduler(
    generalSettingDAO = generalSettingDAO,
    taskDAO = taskDAO,
    mqCampaignSchedulingMetadataMigration = mqCampaignSchedulingMetadataMigration
    //    taskService = taskService
  )

  val linkedinChannelScheduler = new LinkedinChannelScheduler(
    linkedinSettingDAO = linkedinSettingDAO,
    taskDAO = taskDAO,
    mqCampaignSchedulingMetadataMigration = mqCampaignSchedulingMetadataMigration
    //    taskService: TaskService
  )

  val aDate = DateTime.parse("2022-10-28")

  val campaignId: Long = 3
  val campaignOwnerId = 2
  val teamId: Long = 505
  val oldFlowTeamId = 10
  val orgId = 11
  val campaignName = "Test Email Campaign"
  val headStepId = 11
  val sendingHolidayCalendarId = 9L
  val senderEmailSettingId = 8
  val receiverEmailSettingsId = 8
  val appendFollowUps = false
  val openTrackingEnabled = false

  val prospectOwnerId = 1
  val prospectName = "Shashank Dwivedi"

  val channel_id_1: Long = 1
  val account_id_1: Long = 1
  val emailSettingId_1: Long = 3

  val emailServiceBody = EmailServiceBody(
    subject = "This is the Subject",
    textBody = "This is The Body",
    htmlBody = "This is The Body",
    baseBody = "This is The Body",
    isEditedPreviewEmail = false,
    has_unsubscribe_link = false
  )

  val profile: AccountProfileInfo = AccountProfileInfo(
    first_name = "Animesh",
    last_name = "Kumar",
    company = Some("AnimeshKumar"),
    timezone = Some("Asia/Kolkata"),
    country_code = Some("IN"),
    mobile_country_code = Some("+91"),
    mobile_number = Some(9515253545L),
    twofa_enabled = false,
    has_gauthenticator = false,
    weekly_report_emails = Some("<EMAIL>"),
    scheduled_for_deletion_at = None,
    onboarding_phone_number = Some("+************")
  )


  val orgCountData: OrgCountData = OrgCountDataFixture.orgCountData_default

  val orgPlan: OrgPlan = OrgPlanFixture.orgPlanFixture
  val orgSettings: OrgSettings = OrgSettings(
    enable_ab_testing = false,
    disable_force_send = false,
    bulk_sender = false,
    allow_2fa = false,
    show_2fa_setting = false,
    enforce_2fa = false,
    allow_native_crm_integration = false,
      agency_option_allow_changing = false,
      agency_option_show = false
  )

  val orgMetadata: OrgMetadata = OrgMetadataFixture.orgMetadataFixture2

  val org: OrganizationWithCurrentData = OrganizationWithCurrentData(

    id = 1,
    name = "AK",
    owner_account_id = account_id_1,

    counts = orgCountData,
    settings = orgSettings,
    plan = orgPlan,

    is_agency = true,
    trial_ends_at = DateTime.now().plusDays(100),
    error = None,
    error_code = None,
    paused_till = None,
    errors = Seq(),
    warnings = Seq(),
    via_referral = false,
    org_metadata = orgMetadata
  )


  val accountMetadata: AccountMetadata = AccountMetadata(
    // account_ui_version = None,
    is_profile_onboarding_done = None
  )

  val account: Account = Account(
    id = AccountUuid("account_uuid"),
    internal_id = 2,
    email = "<EMAIL>",
    email_verification_code = None,
    email_verification_code_created_at = None,
    created_at = DateTime.now().minusDays(1000),
    first_name = Some("Animesh"),
    last_name = Some("Kumar"),
    company = Some("AK"),
    timezone = None,
    profile = profile,
    org_role = Some(OrganizationRole.OWNER),
    teams = Seq(),
    account_type = AccountType.AGENCY,
    org = org,
    active = true,
    email_notification_summary = "dSFA",
    account_metadata = accountMetadata,
    email_verified = true,
    signupType = None,
    account_access = AccountAccess(
      inbox_access = false
    ),
    calendar_account_data = None

  )

//  val channelScheduler: ChannelSchedulerTrait  = emailChannelScheduler


  val campaignForSchedulingEmail = CampaignForScheduling.CampaignForSchedulingEmail(
    campaign_id = campaignId,
    campaign_owner_id = campaignOwnerId,
    team_id = teamId,
    org_id = orgId,
    campaign_name = campaignName,
    status = CampaignStatus.RUNNING, //

    campaign_type_data = CampaignTypeData.MultiChannelCampaignData(head_step_id = headStepId),

    // settings

    ai_generation_context = None,


    sending_holiday_calendar_id = Some(sendingHolidayCalendarId),

    // in CampaignForScheduling, email_settings would be there because we ignore campaigns which do not have them
    campaign_email_setting = CampaignEmailSettingForScheduler(
      sender_email_settings_id = senderEmailSettingId,
      receiver_email_settings_id = receiverEmailSettingsId,
      campaign_email_settings_id = CampaignEmailSettingsId(123),
      emailServiceProvider = EmailServiceProvider.OTHER

    ),

    append_followups = appendFollowUps,
    open_tracking_enabled = false,
    click_tracking_enabled = false,
    opt_out_msg = "optMessage",
    opt_out_is_text = false,

    timezone = "UTC",
    daily_from_time = 0, // time since beginning of day in seconds
    daily_till_time = 855000, // time since beginning of day in seconds

    // Sunday is the first day
    days_preference = List(true, true, true, true, true, true, true),


    email_priority = CampaignEmailPriority.FIRST_EMAIL,

    max_emails_per_prospect_per_day =  1,
    max_emails_per_prospect_per_week =  3,

    max_emails_per_prospect_account_per_day = 100,
    max_emails_per_prospect_account_per_week = 300,

    campaign_max_emails_per_day = 100,

    // warm up
    softstart_setting = None,  // making it none now

    mark_completed_after_days = 20,

    latest_email_scheduled_at = Some(aDate),


    from_email = "<EMAIL>",
    from_name = "Shashank Dwivedi",

    reply_to_email = "<EMAIL>",
    reply_to_name = "Shashank Dwivedi Reply Name",

    min_delay_seconds = 50,
    max_delay_seconds = 100,

    enable_email_validation = false, // keeping it false now

    rep_mail_server_id = 2,
    via_gmail_smtp = Some(false),  // Option[DateTime]
    prospects_remaining_to_be_scheduled_exists = Some(true),
    count_of_sender_emails = 1,
    selected_calendar_data = None
  )

  val schedulerMapStepIdAndDelay =  SchedulerMapStepIdAndDelay(
    is_head_step_in_the_campaign = true, // keeping it true
    currentStepType =  CampaignStepType.AutoEmailStep,
    nextStepType = CampaignStepType.AutoEmailStep,
    currentStepId = 12,
    delayTillNextStep = 100
  )

  val timeZone = "Asia/Kolkata"
  val prospectId = 7L

  val parsedJson = Json.parse(
    """
      {
        "name" : "Watership Down",
        "location" : {
          "lat" : 51.235685,
          "long" : -1.309197
        },
        "residents" : [ {
          "name" : "Fiver",
          "age" : 4,
          "role" : null
        }, {
          "name" : "Bigwig",
          "age" : 6,
          "role" : "Owsla"
        } ]
      }
      """)

  val prospectObjectInternal = ProspectFixtures.prospectObjectInternal.copy(
    prospect_account_id = None
  )

  val prospectObject = ProspectObject(
    id = prospectId,
    owner_id = prospectOwnerId,
    team_id = teamId,

    first_name = Some("Shashank Prospect"),
    last_name = Some("dwivedi prospect"),

    email =  Some("<EMAIL>"),

    custom_fields = parsedJson,

    list = None,

    job_title = None,
    company = None,
    linkedin_url = Some("https://linkedin.com/in/aditya-sadana"),
    phone = None,
    phone_2 = None,
    phone_3 = None,

    city = None,
    state = None,
    country = None,
    timezone = None,

    prospect_category = "categoryProspect", // display name

    last_contacted_at = None,
    last_contacted_at_phone = None,

    created_at =  aDate,


    /* internal columns only for smartreach website, not for public api */
    internal =  prospectObjectInternal,
    latest_reply_sentiment_uuid = None,
    current_step_type = None,
    latest_task_done_at = None,
    prospect_uuid = Some(ProspectUuid("prs_aa_abcdefghi")),
    owner_uuid = AccountUuid("acc_aa_abcdegfhi"),
    updated_at = aDate
  )

  val emailStep = AutoEmailStep(
    subject = "variant subject",
    body = "Variant body",
  )

  val step_data2 = LinkedinConnectionRequestData(
    body = Some("Linkedin Connection Request Body 2")
  )

  val step_data5 = LinkedinMessageData(
    body = "Linkedin Message Body 5"
  )



  val campaignStepVariantForScheduling = CampaignStepVariantForScheduling(
    id = 1,
    step_id = 3,
    campaign_id = campaignId,
    template_id = None,
    step_data = emailStep,
    step_label = None,
    step_delay = 10,
    notes = Some("Test Notes"),
    priority = Some(TaskPriority.Normal),
    active = true,
    scheduled_count = 1
  )

  val internalMergeTagValuesForProspect = InternalMergeTagValuesForProspect(
    sender_name = "Monica Geller",
    sender_first_name = "Monica",
    sender_last_name = "Geller",
    unsubscribe_link = Some("dummy_link"),
    previous_subject = None,
    signature = None,
    sender_phone_number = None,
    calendar_link = None
  )

  val campaignStepWithChildren = CampaignStepWithChildren(
    id = 3,
    label = None,
    campaign_id = campaignId,
    delay = 10,
    step_type = CampaignStepType.AutoEmailStep,
    created_at = DateTime.parse("2022-03-21T11:58:03.294Z"),
    children = List(2, 3, 4),
    variants = Seq(
      campaignStepVariantForScheduling
    )
  )

  val campaignStepVariantForSchedulingLinkedin = campaignStepVariantForScheduling.copy(
    step_data = LinkedinInmailData(
      subject = Some("Linkedin Inmail Subject"),
      body = "Linkedin Inmail Body"
    )
  )

  val campaignStepWithChildrenLinkedin = campaignStepWithChildren.copy(
    step_type = LinkedinInmail,
    variants = Seq(campaignStepVariantForSchedulingLinkedin),
    id = 2,
    children = List()
  )

  val stepsMappedById = Map(3L -> campaignStepWithChildren)
  val stepsMappedByIdLinkedin = Map(3L -> campaignStepWithChildrenLinkedin)
  val orderedStepsById = Vector(3L)

  val allCampaignSteps = Vector(schedulerMapStepIdAndDelay)

  val relevantCampaignStepsForChannel = Vector(schedulerMapStepIdAndDelay)

  val fetchCampaignStepsData = FetchCampaignStepsData(
    stepsMappedById = stepsMappedById,
    campaignStepsStructure = CampaignStepsStructure.MultichannelCampaignStepsStructure(
      orderedStepIds = orderedStepsById
    ),
    allCampaignSteps = allCampaignSteps,
    relevantCampaignStepsForChannel = relevantCampaignStepsForChannel,
  )

  val cpCompleted = CPCompleted(
    campaignId = campaignId,
    prospectId = prospectId,
    completed = false
  )

  val emailScheduledNewAfterSaving = EmailScheduledNewAfterSaving(
    email_scheduled_id = 1,
    campaign_id = Some(campaignId),
    step_id = Some(3),
    prospect_id = Some(prospectId),
    to_email = "<EMAIL>",
    reply_to_email = Some("<EMAIL>"),
    step_type = CampaignStepType.ManualEmailStep,
    from_email = "<EMAIL>",
    added_at = DateTime.now().minusMonths(10),
    scheduled_at = DateTime.now().plusMinutes(10),
    sender_email_settings_id = senderEmailSettingId,
    template_id = None,
    variant_id = Some(1),
    rep_mail_server_id = 1,
    campaign_email_setting_id = CampaignEmailSettingsId(123),
    team_id = TeamId(teamId),
    to_name = None, from_name = "Animesh", reply_to_name = None, body = None, base_body = None, text_body = None, subject = None
  )

  val emailSettingCreateEmailSchedule = EmailSettingCreateEmailSchedule(
    id = emailSettingId_1,
    team_id = teamId,
    org_id = OrgId(orgId),
    account_id = account_id_1,
    email = "<EMAIL>",
    sender_name = "Rachel Green",
    first_name = "Monica",
    last_name = "Geller",
    quota_per_day = 100,
    donot_enforce_24_hour_limit_till = None,
    min_delay_seconds = 30,
    max_delay_seconds = 60,
    latest_email_scheduled_at = None,
    default_tracking_domain = "goDaddy",
    default_unsubscribe_domain = "friends",
    rep_tracking_host_id = 1,
    custom_tracking_domain = None,
    signature = None,
    account_timezone = "Asia/Kolkata",
    bulk_sender = false
  )

  val emailChannelDataForScheduling = EmailChannelDataForScheduling(
    emailSetting = emailSettingCreateEmailSchedule,
    channelTeamId = teamId,
    account_timezone = "Asia/Kolkata",
    channelOwnerAccountId = account_id_1,
    channelOrgId = OrgId(orgId)
  )

  val linkedinSettingCreateSchedule = LinkedinSettingCreateSchedule(
    uuid = "linkedin_account_33nvjnw1",
    team_id = teamId,
    account_id = account_id_1,
    first_name = "Rachel",
    last_name = "Green",
    email = "<EMAIL>",

    linkedin_message_limit_per_day = 5,
    linkedin_view_profile_limit_per_day = 103,
    linkedin_inmail_limit_per_day = 20,
    linkedin_connection_request_limit_per_day = 40,

    account_timezone = "Asia/Kolkata",
    latest_task_scheduled_at = None,
    min_delay_seconds = 30,
    max_delay_seconds = 60
  )

  val linkedinChannelDataForScheduling = LinkedinChannelDataForScheduling(
    linkedinSetting = linkedinSettingCreateSchedule,
    channelTeamId = 3L,
    channelOwnerAccountId = 3L,
    account_timezone = "Asia/Kolkata",
    channelOrgId = OrgId(orgId)
  )


  val linkedinStepType = StepType(
    step_type_value = CampaignStepType.LinkedinInmail.toKey
  )

  val channelStepTypeDataForSchedulingLinkedin = linkedinChannelScheduler.ChannelStepTypeDataForScheduling(
    campaignStepType = CampaignStepType.LinkedinInmail,
    totalScheduledForStepTypeTillNow = 0,
    channelStepTypeDailyLimit = 53,
    remainingToBeScheduledFromChannelStepType = 97,
    campaignStepTypeLimitHasBeenReachedForToday = false,
    stepType = linkedinStepType
  )

  // NOTE: Added asInstanceOf to resolve following errors
  // [error] Note: api.campaigns.models.CampaignStepType.LinkedinInmail.type <: api.campaigns.models.CampaignStepType, but trait Map is invariant in type K.
  // [error] You may wish to investigate a wildcard type such as `_ <: api.campaigns.models.CampaignStepType`. (SLS 3.2.10)
  val channelStepTypeDataForSchedulingMapLinkedin = Map(CampaignStepType.LinkedinInmail.asInstanceOf[CampaignStepType] -> channelStepTypeDataForSchedulingLinkedin)

  val stepType: StepType = StepType(
    step_type_value = CampaignStepType.AutoEmailStep.toKey
  )

  val count: Count = Count(
    count_value = 2
  )

  val stepTypeAndCount: StepTypeAndCount = StepTypeAndCount(
    stepType = stepType,
    count = count
  )

  val channelStepTypeDataForScheduling = emailChannelScheduler.ChannelStepTypeDataForScheduling(
    campaignStepType = CampaignStepType.AutoEmailStep,
    totalScheduledForStepTypeTillNow = 0,
    channelStepTypeDailyLimit = 7,
    remainingToBeScheduledFromChannelStepType = 11,
    campaignStepTypeLimitHasBeenReachedForToday = false,
    stepType = stepType
  )

  val channelStepTypeDataForSchedulingMap = Map(CampaignStepType.AutoEmailStep.asInstanceOf[CampaignStepType] -> channelStepTypeDataForScheduling)

  val campaignForScheduling: CampaignForScheduling.CampaignForSchedulingEmail = models.CampaignForScheduling.CampaignForSchedulingEmail(
    campaign_id = campaignId,
    campaign_owner_id = campaignOwnerId,
    team_id = teamId,
    org_id = 1,
    campaign_name = "Animesh Kumar",
    ai_generation_context = None,
    status = CampaignStatus.RUNNING,
    campaign_type_data = CampaignTypeData.MultiChannelCampaignData(head_step_id = 3),
    sending_holiday_calendar_id = Some(123),
    campaign_email_setting = CampaignEmailSettingForScheduler(
      sender_email_settings_id = 1,
      receiver_email_settings_id = 1,
      campaign_email_settings_id = CampaignEmailSettingsId(123),
      emailServiceProvider = EmailServiceProvider.OTHER
    ),
    append_followups = false,
    open_tracking_enabled = false,
    click_tracking_enabled = false,
    opt_out_msg = "opt out {{unsubscribe_link}}",
    opt_out_is_text = false,
    timezone = "IN",
    daily_from_time = 1,
    daily_till_time = 1,
    days_preference = List(),
    email_priority = CampaignEmailPriority.EQUAL,
    max_emails_per_prospect_per_day = 100,
    max_emails_per_prospect_per_week = 1000,
    max_emails_per_prospect_account_per_day = 100,
    max_emails_per_prospect_account_per_week = 1000,
    campaign_max_emails_per_day = 1000,
    softstart_setting = None,
    mark_completed_after_days = 1,
    latest_email_scheduled_at = None,
    from_email = "<EMAIL>",
    from_name = "Rachel",
    reply_to_email = "<EMAIL>",
    reply_to_name = "Monica",
    min_delay_seconds = 1,
    max_delay_seconds = 1,
    enable_email_validation = true,
    rep_mail_server_id = 1,
    via_gmail_smtp = None,
    prospects_remaining_to_be_scheduled_exists = Some(true),
    count_of_sender_emails = 1,
    selected_calendar_data = None
  )


  val prospectForSchedulingEmail = ProspectDataForChannelScheduling.EmailChannelProspectForScheduling(
    prospect =  prospectObject,
    current_step_status_data = None,
    current_step_id = None, // make it some values and then check
    email_checked = false,
    email_sent_for_validation = false,  // can try making it true
    email_sent_for_validation_at =  None, // when above one is true, set this too.
  )

  val prospectForSchedulingLinkedin = ProspectDataForChannelScheduling.LinkedinChannelProspectForScheduling(
    prospect =  prospectObject,
    current_step_status_data = None,
    current_step_id = None // make it some values and then check
  )

  val prospectsFoundForSchedulingByStepType = emailChannelScheduler.ProspectsFoundForSchedulingByStepType(
    prospects = List(prospectForSchedulingEmail),
    step_type = channelStepTypeDataForScheduling
  )

  val prospectFoundForSchedulingByStepType = emailChannelScheduler.ProspectFoundForSchedulingByStepType(
    prospectForScheduling = prospectForSchedulingEmail,
    step_type = channelStepTypeDataForScheduling
  )

  val prospectsFoundByStepType: emailChannelScheduler.ProspectsFoundByStepType = Map(
    CampaignStepType.AutoEmailStep -> prospectsFoundForSchedulingByStepType
  )

  val flattenedProspects = emailChannelScheduler.flattenProspectsFoundForSchedulingByStepType(
    prospectsFoundByStepType = prospectsFoundByStepType
  )

  val channelStepTypeDataForSchedulingManual = emailChannelScheduler.ChannelStepTypeDataForScheduling(
    campaignStepType = CampaignStepType.ManualEmailStep,
    totalScheduledForStepTypeTillNow = 0,
    channelStepTypeDailyLimit = 7,
    remainingToBeScheduledFromChannelStepType = 11,
    campaignStepTypeLimitHasBeenReachedForToday = false,
    stepType = stepType
  )

  val prospectsFoundForSchedulingByStepTypeManual = emailChannelScheduler.ProspectsFoundForSchedulingByStepType(
    prospects = List(prospectForSchedulingEmail),
    step_type = channelStepTypeDataForSchedulingManual
  )

  val prospectsFoundByStepTypeManual: emailChannelScheduler.ProspectsFoundByStepType = Map(
    CampaignStepType.ManualEmailStep -> prospectsFoundForSchedulingByStepTypeManual
  )

  val flattenedProspectsManual = emailChannelScheduler.flattenProspectsFoundForSchedulingByStepType(
    prospectsFoundByStepType = prospectsFoundByStepTypeManual
  )

  val campaignStepVariantManual = campaignStepVariantForScheduling.copy(
    step_data = CampaignStepData.ManualEmailStep(
      subject = "Manual Email Step Subject",
      body = "Manual Email Step Body"
    )
  )

  val campaignStepWithChildrenManual = campaignStepWithChildren.copy(
    step_type = CampaignStepType.ManualEmailStep,
    variants = Seq(campaignStepVariantManual),
    id = 3,
    children = List()
  )

  val prospectsFoundForSchedulingByStepTypeLinkedin = linkedinChannelScheduler.ProspectsFoundForSchedulingByStepType(
    prospects = List(prospectForSchedulingLinkedin),
    step_type = channelStepTypeDataForSchedulingLinkedin
  )

  val prospectsFoundForSchedulingByStepTypeLinkedin4 = linkedinChannelScheduler.ProspectsFoundForSchedulingByStepType(
    prospects = List(prospectForSchedulingLinkedin,
      prospectForSchedulingLinkedin.copy(prospect = prospectObject.copy(id = 5)),
      prospectForSchedulingLinkedin.copy(prospect = prospectObject.copy(id = 11)),
      prospectForSchedulingLinkedin.copy(prospect = prospectObject.copy(id = 17))
    ),
    step_type = channelStepTypeDataForSchedulingLinkedin
  )

  val prospectsFoundForSchedulingByStepTypeLinkedin5 = linkedinChannelScheduler.ProspectsFoundForSchedulingByStepType(
    prospects = List(prospectForSchedulingLinkedin,
      prospectForSchedulingLinkedin.copy(prospect = prospectObject.copy(id = 5)),
      prospectForSchedulingLinkedin.copy(prospect = prospectObject.copy(id = 11)),
      prospectForSchedulingLinkedin.copy(prospect = prospectObject.copy(id = 17)),
      prospectForSchedulingLinkedin.copy(prospect = prospectObject.copy(id = 23))
    ),
    step_type = channelStepTypeDataForSchedulingLinkedin
  )

  val scheduledProspectsCountForCampaignEmail = ScheduledProspectsCountForCampaignEmail(
    campaignId = campaignId,
    newCount = 1,
    followupCount = 1,
    newCountNotSent = 1,
    followupCountNotSent = 1
  )

  val repTrackingHosts = RepTrackingHosts(
    id = 1,
    host_url = "url",
    subdomain_based = false,
    active = true
  )

  val prospectFoundForSchedulingByStepTypeLinkedin = linkedinChannelScheduler.ProspectFoundForSchedulingByStepType(
    prospectForScheduling = prospectForSchedulingLinkedin,
    step_type = channelStepTypeDataForSchedulingLinkedin
  )

  val prospectsFoundByStepTypeLinkedin: linkedinChannelScheduler.ProspectsFoundByStepType = Map(
    CampaignStepType.LinkedinInmail -> prospectsFoundForSchedulingByStepTypeLinkedin
  )

  val flattenedProspectsLinkedin = linkedinChannelScheduler.flattenProspectsFoundForSchedulingByStepType(
    prospectsFoundByStepType = prospectsFoundByStepTypeLinkedin
  )

  val prospectsFoundByStepTypeLinkedin4: linkedinChannelScheduler.ProspectsFoundByStepType = Map(
    CampaignStepType.LinkedinInmail -> prospectsFoundForSchedulingByStepTypeLinkedin4,
  )

  val flattenedProspectsLinkedin4 = linkedinChannelScheduler.flattenProspectsFoundForSchedulingByStepType(
    prospectsFoundByStepType = prospectsFoundByStepTypeLinkedin4
  )

  val prospectsFoundByStepTypeLinkedin5: linkedinChannelScheduler.ProspectsFoundByStepType = Map(
    CampaignStepType.LinkedinInmail -> prospectsFoundForSchedulingByStepTypeLinkedin5,
  )

  val flattenedProspectsLinkedin5 = linkedinChannelScheduler.flattenProspectsFoundForSchedulingByStepType(
    prospectsFoundByStepType = prospectsFoundByStepTypeLinkedin5
  )

  val campaignForSchedulingLinkedin = models.CampaignForScheduling.CampaignForSchedulingLinkedin(
    campaign_id = campaignId.toLong,
    campaign_name = "FRIENDS",
    campaign_owner_id = 3L,
    team_id = teamId,
    campaign_type_data = CampaignTypeData.MultiChannelCampaignData(head_step_id = 3L),

    // Sunday is the first day
    days_preference = List(true, true, true, true, true, true, true),
    timezone = "Asia/Kolkata",
    daily_from_time = 0, // time since beginning of day in seconds
    daily_till_time = 86400, // time since beginning of day in seconds
    sending_holiday_calendar_id = None,
    softstart_setting = None,
    email_priority = CampaignEmailPriority.EQUAL,
    mark_completed_after_days = 1,
    prospects_remaining_to_be_scheduled_exists = Some(true),
    selected_calendar_data = None,
    campaign_channel_setting_uuid = "campaignForSchedulingLinkedin_campaign_channel_setting_uuid",
    captain_data_user_id = None,
    captain_data_account_id = None,
    service_provider = None
  )

  val scheduleCampaign = emailChannelScheduler.ScheduleCampaign(
    markedCompletedIds = Seq(),
    campaign = campaignForScheduling,
    stepsMappedById = stepsMappedById,
    campaignStepsStructure = CampaignStepsStructure.MultichannelCampaignStepsStructure(
      orderedStepIds = Vector(3)
    ),
    prospects = flattenedProspects,
    distinctTimezones = Set()
  )

  val scheduleCampaignLinkedin = linkedinChannelScheduler.ScheduleCampaign(
    markedCompletedIds = Seq(),
    campaign = campaignForSchedulingLinkedin,
    stepsMappedById = stepsMappedByIdLinkedin,
    campaignStepsStructure = CampaignStepsStructure.MultichannelCampaignStepsStructure(
      orderedStepIds = Vector(3)
    ),
    prospects = flattenedProspectsLinkedin,
    distinctTimezones = Set()

  )

  val linkedinChannelScheduledProspectsCountForCampaign = linkedinChannelScheduler.LinkedinChannelScheduledProspectsCountForCampaign(
    counts = Seq(ScheduledProspectsCountForCampaign(
      campaignId = campaignId,
      campaignStepType = CampaignStepType.LinkedinViewProfile,
      newCount = 2,
      followupCount = 3,
      newCountNotSent = 7,
      followupCountNotSent = 9
    )),
    campaign = campaignForSchedulingLinkedin
  )

  val campaignForScheduling2 = campaignForScheduling.copy(campaign_id = 2L)

  val campaignForScheduling3 = campaignForScheduling.copy(campaign_id = 3L)

  val campaignStepWithChildren2 = campaignStepWithChildrenLinkedin.copy(
    id = 2L,
    campaign_id = 2,
    variants = Seq(campaignStepVariantForSchedulingLinkedin.copy(step_data = step_data2)))

  val campaignStepWithChildren5 = campaignStepWithChildrenLinkedin.copy(
    id = 5L,
    campaign_id = 2,
    variants = Seq(campaignStepVariantForSchedulingLinkedin.copy(step_data = step_data5)))

  val scheduleCampaign2 = scheduleCampaign.copy(
    campaign = campaignForScheduling.copy(campaign_id = 2L),
    stepsMappedById = Map(2L -> campaignStepWithChildren2, 5L -> campaignStepWithChildren5),
    campaignStepsStructure = CampaignStepsStructure.MultichannelCampaignStepsStructure(
      orderedStepIds = Vector(2, 5)
    ),
  )

  val scheduleCampaign2LinkedinProspects4 = scheduleCampaignLinkedin.copy(
    campaign = campaignForSchedulingLinkedin.copy(campaign_id = 2L),
    prospects = flattenedProspectsLinkedin4
  )

  val scheduleCampaignManual = scheduleCampaign.copy(
    stepsMappedById = Map(3L -> campaignStepWithChildrenManual),
    prospects = flattenedProspectsManual
  )

  val generateScheduleTaskData = emailChannelScheduler.GenerateScheduleTaskData(
    currentCampaign = scheduleCampaign,
    nextStep = campaignStepWithChildren,
    currentProspect = prospectFoundForSchedulingByStepType,
    currentVariant = campaignStepVariantForScheduling,
    schedulerDateTime = aDate.plusSeconds(60),
  )

  val generateScheduleTaskDataLinkedin = linkedinChannelScheduler.GenerateScheduleTaskData(
    currentCampaign = scheduleCampaignLinkedin,
    nextStep = campaignStepWithChildrenLinkedin,
    currentProspect = prospectFoundForSchedulingByStepTypeLinkedin,
    currentVariant = campaignStepVariantForSchedulingLinkedin,
    schedulerDateTime = aDate.plusSeconds(60),
  )

  val channel_follow_up_data = PreviousFollowUpData.AutoEmailFollowUp(
    email_thread_id = Some(3),
    from_name = "Prateek Bhat",
    base_body = "This is previous test body",
    body = "This is previous test body",
    subject = "Hey {{first_name}}",
    from_email = "<EMAIL>",
    is_edited_preview_email = false,
  )

  val previousFollowUp = PreviousFollowUp(
    channel_follow_up_data = channel_follow_up_data,
    sent_at = DateTime.now().minusDays(5),
    timezone = "IN",
    step_id = None,
    completed_reason = None
  )

//  val prospectId = 2


  val prospectForScheduling = ProspectDataForChannelScheduling.EmailChannelProspectForScheduling(
  prospect =  prospectObject,
    current_step_status_data = None,
  current_step_id = None, // make it some values and then check
  email_checked = false,
  email_sent_for_validation = false,  // can try making it true
  email_sent_for_validation_at =  None, // when above one is true, set this too.
  )


  val error = new Exception("try failed")

  describe("testing ChannelSchedulerTrait.fetchProspect"){
    it("team_id 505 flag is true for multichannel so it will go to new flow and should success with empty prospect list as timeZonesInCampaign is Empty"){

//      (campaignProspectDAO.getCampaignProspectsTimezoneMetadata(_: Long, _:Long))
//        .expects(teamId,campaignId)
//        .returning(Success(List()))

      val res = emailChannelScheduler.fetchProspects(
        channelType = ChannelType.EmailChannel,
        campaignProspectDAO =  campaignProspectDAO,
        campaignProspectService = campaignProspectService,
        scheduleFromTime =  aDate,
        campaign = campaignForSchedulingEmail,
        allowedTimezonesTry = Try(Set()),
        prospectsToFetch =  50,
        channelRelevantStepIdAndDelay = Vector(schedulerMapStepIdAndDelay),
        newProspectsInCampaign = false,  // also test for true
        firstStepIsMagicContent = false,
        sendOnlyToProspectsWhoWereSentInCurrentCycle = Some(aDate),
        org_id = OrgId(orgId.toLong),
        srRollingUpdateCoreService= srRollingUpdateCoreService

      )

      assert(res == Success(List()))

    }

//    it("team_id 505 flag is true for multichannel so it will go to new flow and fail as campaignProspectDAO.getCampaignProspectsTimezoneMetadata fails") {
//
//      (campaignProspectDAO.getCampaignProspectsTimezoneMetadata(_: Long, _: Long))
//        .expects(teamId, campaignId)
//        .returning(Failure(error))
//
//      val res = emailChannelScheduler.fetchProspects(
//        campaignProspectDAO = campaignProspectDAO,
//        scheduleFromTime = aDate,
//        campaign = campaignForSchedulingEmail,
//        limit = 50,
//        channelRelevantStepIdAndDelay = Vector(schedulerMapStepIdAndDelay),
//        newProspectsInCampaign = false, // also test for true
//        sendOnlyToProspectsWhoWereSentInCurrentCycle = Some(aDate)
//      )
//
//      assert(res == Failure(error))
//
//    }

    it("team_id 505 flag is true for multichannel so it will go to new flow and should return empty prospect result as there are all days_preferences false") {

//      (campaignProspectDAO.getCampaignProspectsTimezoneMetadata(_: Long, _: Long))
//        .expects(teamId, campaignId)
//        .returning(Success(List(campaignProspectsTimezoneMetadata)))

      val res = emailChannelScheduler.fetchProspects(
        channelType = ChannelType.EmailChannel,
        campaignProspectDAO =  campaignProspectDAO,
        campaignProspectService = campaignProspectService,
        scheduleFromTime = aDate,
        campaign = campaignForSchedulingEmail.copy(days_preference = List(false,false,false, false, false, false, false)),
        allowedTimezonesTry = Success(Set(timeZone)),
        prospectsToFetch = 50,
        channelRelevantStepIdAndDelay = Vector(schedulerMapStepIdAndDelay),
        newProspectsInCampaign = false, // also test for true
        firstStepIsMagicContent = false,
        //        srRollingUpdateCoreService = srRollingUpdateCoreService,
        sendOnlyToProspectsWhoWereSentInCurrentCycle = Some(aDate),
        org_id = OrgId(orgId.toLong),
        srRollingUpdateCoreService = srRollingUpdateCoreService

      )

//      println(s"\nres: $res\n")

      assert(res == Success(List()))
    }

    it("team_id 505 flag is true for multichannel so it will go to new flow and should return empty prospect result as there are no scheduler_window available") {

//      (campaignProspectDAO.getCampaignProspectsTimezoneMetadata(_: Long, _: Long))
//        .expects(teamId, campaignId)
//        .returning(Success(List(campaignProspectsTimezoneMetadata)))

      val res = emailChannelScheduler.fetchProspects(
        channelType = ChannelType.EmailChannel,
        campaignProspectDAO =  campaignProspectDAO,
        campaignProspectService = campaignProspectService,
        scheduleFromTime = aDate,
        campaign = campaignForSchedulingEmail.copy(daily_from_time = 0, daily_till_time = 0),
        allowedTimezonesTry = Success(Set(timeZone)),
        prospectsToFetch = 50,
        channelRelevantStepIdAndDelay = Vector(schedulerMapStepIdAndDelay),
        newProspectsInCampaign = false, // also test for true
        firstStepIsMagicContent = false,
        //        srRollingUpdateCoreService = srRollingUpdateCoreService,
        sendOnlyToProspectsWhoWereSentInCurrentCycle = Some(aDate),
        org_id = OrgId(orgId.toLong),
        srRollingUpdateCoreService = srRollingUpdateCoreService
      )

      assert(res == Success(List()))
    }

    it("should fail if campaignProspectDAO.filterProspectWhoHaveHoliday fails") {

//      (campaignProspectDAO.getCampaignProspectsTimezoneMetadata(_: Long, _: Long))
//        .expects(teamId, campaignId)
//        .returning(Success(List(campaignProspectsTimezoneMetadata)))



//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(_: TeamId, _: SrRollingUpdateFeature)(_: ISRLogger))
//        .expects(TeamId(teamId), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .returning(false)

      (campaignProspectService.fetchProspectsV3MultichannelWithEmailOptionalCheck(
        _: ChannelType,
        _: List[String],
        _: Option[Long],
        _: Long,
        _: TeamId,
        _: Int,
        _: Vector[SchedulerMapStepIdAndDelay],
        _: Boolean, _: Boolean,
        _: Option[DateTime],
        _: Boolean,
        _: Option[CampaignEmailSettingForScheduler],
        _: OrgId, _:String)(using _: SRLogger))
        .expects(*, *, *, *, *, *, *, *, *, *, *, *, *, *, *)
        .returning(Try(List(prospectForScheduling)))

      (campaignProspectDAO.filterProspectWhoHaveHoliday(
        _: List[Long],
        _: String,
        _: Option[Long],
        _: DateTime,
        _: Long
      )(using _: SRLogger))
        .expects(*, *,* , *, *, *)
        .returning(Failure(error)) // no prospects have filter

      val res = emailChannelScheduler.fetchProspects(
        channelType = ChannelType.EmailChannel,
        campaignProspectDAO =  campaignProspectDAO,
        campaignProspectService = campaignProspectService,
        scheduleFromTime = aDate,
        campaign = campaignForSchedulingEmail,
        allowedTimezonesTry = Success(Set(timeZone)),
        prospectsToFetch = 50,
        channelRelevantStepIdAndDelay = Vector(schedulerMapStepIdAndDelay),
        newProspectsInCampaign = false, // also test for true
        firstStepIsMagicContent = false,
        //        srRollingUpdateCoreService = srRollingUpdateCoreService,
        sendOnlyToProspectsWhoWereSentInCurrentCycle = Some(aDate),
        org_id = OrgId(orgId.toLong),
        srRollingUpdateCoreService = srRollingUpdateCoreService
      )

      assert(res == Failure(error))
    }

    it("should success and return empty list if campaignProspectDAO.filterProspectWhoHaveHoliday returns empty set") {

//      (campaignProspectDAO.getCampaignProspectsTimezoneMetadata(_: Long, _: Long))
//        .expects(teamId, campaignId)
//        .returning(Success(List(campaignProspectsTimezoneMetadata)))

//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(_: TeamId, _: SrRollingUpdateFeature)(_: ISRLogger))
//        .expects(TeamId(teamId), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .returning(false)

      (campaignProspectService.fetchProspectsV3MultichannelWithEmailOptionalCheck(
        _: ChannelType,
        _: List[String],
        _: Option[Long],
        _: Long,
        _: TeamId,
        _: Int,
        _: Vector[SchedulerMapStepIdAndDelay],
        _: Boolean, _: Boolean,
        _: Option[DateTime],
        _: Boolean,
        _: Option[CampaignEmailSettingForScheduler],
        _: OrgId, _:String)(using _: SRLogger))
        .expects(*, *, *, *, *, *, *, *, *, *, *, *, *, *, *)
        .returning(Try(List(prospectForScheduling)))

      (campaignProspectDAO.filterProspectWhoHaveHoliday(
        _: List[Long],
        _: String,
        _: Option[Long],
        _: DateTime,
        _: Long
      )(using _: SRLogger))
        .expects(*, *, *, *, *, *)
        .returning(Success(Set())) // no prospects have are on holiday

      (campaignProspectDAO.filterByProspectValidationStatus(_: Set[Long], _: TeamId)(using _: SRLogger))
        .expects(*,*,*)
        .returning(Success(Set()))

      (campaignProspectDAO.filterProspectsBySentCountBasedOnProspectLimit(
        _: Set[Long],
        _: String,
        _: TeamId,
        _: Int,
        _: Int
      )(using _: SRLogger))
        .expects(*, *,*, *, *, *)
        .returning(Success(Set()))

      (orgMetadataService.filterProspectsBasedOnDomainLimit)
        .expects(*)
        .returning(Success(true))

      (campaignProspectDAO.mapProspectIdsWithProspectAccountIds)
        .expects(*, *)
        .returning(Success(Map()))

      (campaignProspectDAO.mapProspectIdsWithProspectAccountIds)
        .expects(*, *)
        .returning(Success(Map()))

      (campaignProspectDAO.getEmailsScheduledInLast24HoursAnd7DaysForAProspectAccount(
        _: List[ProspectAccountsId],
        _: String,
        _: TeamId
      ))
        .expects(*, *, *)
        .returning(Success(Map()))

      val res = emailChannelScheduler.fetchProspects(
        channelType = ChannelType.EmailChannel,
        campaignProspectDAO =  campaignProspectDAO,
        campaignProspectService = campaignProspectService,
        scheduleFromTime = aDate,
        campaign = campaignForSchedulingEmail,
        allowedTimezonesTry = Success(Set(timeZone)),
        prospectsToFetch = 50,
        channelRelevantStepIdAndDelay = Vector(schedulerMapStepIdAndDelay),
        newProspectsInCampaign = false, // also test for true
        firstStepIsMagicContent = false,
        //        srRollingUpdateCoreService = srRollingUpdateCoreService,
        sendOnlyToProspectsWhoWereSentInCurrentCycle = Some(aDate),
        org_id = OrgId(orgId.toLong),
        srRollingUpdateCoreService = srRollingUpdateCoreService
      )


      assert(res == Success(List()))
    }

    it("should fails as campaignProspectDAO.filterByProspectValidationStatus fails") {

//      (campaignProspectDAO.getCampaignProspectsTimezoneMetadata(_: Long, _: Long))
//        .expects(teamId, campaignId)
//        .returning(Success(List(campaignProspectsTimezoneMetadata)))

//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(_: TeamId, _: SrRollingUpdateFeature)(_: ISRLogger))
//        .expects(TeamId(teamId), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .returning(false)

      (campaignProspectService.fetchProspectsV3MultichannelWithEmailOptionalCheck(
        _: ChannelType,
        _: List[String],
        _: Option[Long],
        _: Long,
        _: TeamId,
        _: Int,
        _: Vector[SchedulerMapStepIdAndDelay],
        _: Boolean, _: Boolean,
        _: Option[DateTime],
        _: Boolean,
        _: Option[CampaignEmailSettingForScheduler],
        _: OrgId, _:String)(using _: SRLogger))
        .expects(*, *, *, *, *, *, *, *, *, *, *, *, *, *, *)
        .returning(Try(List(prospectForScheduling)))

      (campaignProspectDAO.filterProspectWhoHaveHoliday(
        _: List[Long],
        _: String,
        _: Option[Long],
        _: DateTime,
        _: Long
      )(using _: SRLogger))
        .expects(*, *, *, *, *, *)
        .returning(Success(Set())) // no prospects have are on holiday

      (campaignProspectDAO.filterByProspectValidationStatus(_: Set[Long], _: TeamId)(using _: SRLogger))
        .expects(*, *, *)
        .returning(Failure(error))

      val res = emailChannelScheduler.fetchProspects(
        channelType = ChannelType.EmailChannel,
        campaignProspectDAO =  campaignProspectDAO,
        campaignProspectService = campaignProspectService,
        scheduleFromTime = aDate,
        campaign = campaignForSchedulingEmail,
        allowedTimezonesTry = Success(Set(timeZone)),
        prospectsToFetch = 50,
        channelRelevantStepIdAndDelay = Vector(schedulerMapStepIdAndDelay),
        newProspectsInCampaign = false, // also test for true
        firstStepIsMagicContent = false,
        //        srRollingUpdateCoreService = srRollingUpdateCoreService,
        sendOnlyToProspectsWhoWereSentInCurrentCycle = Some(aDate),
        org_id = OrgId(orgId.toLong),
        srRollingUpdateCoreService = srRollingUpdateCoreService
      )

//      println(s"\nres: $res\n")

      assert(res == Failure(error))
    }

    it("should fails as campaignProspectDAO.filterProspectsBySentCount fails") {

//      (campaignProspectDAO.getCampaignProspectsTimezoneMetadata(_: Long, _: Long))
//        .expects(teamId, campaignId)
//        .returning(Success(List(campaignProspectsTimezoneMetadata)))

//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(_: TeamId, _: SrRollingUpdateFeature)(_: ISRLogger))
//        .expects(TeamId(teamId), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .returning(false)

      (campaignProspectService.fetchProspectsV3MultichannelWithEmailOptionalCheck(
        _: ChannelType,
        _: List[String],
        _: Option[Long],
        _: Long,
        _: TeamId,
        _: Int,
        _: Vector[SchedulerMapStepIdAndDelay],
        _: Boolean, _: Boolean,
        _: Option[DateTime],
        _: Boolean,
        _: Option[CampaignEmailSettingForScheduler],
        _: OrgId, _:String)(using _: SRLogger))
        .expects(*, *, *, *, *, *, *, *, *, *, *, *, *, *, *)
        .returning(Try(List(prospectForScheduling)))

      (campaignProspectDAO.filterProspectWhoHaveHoliday(
        _: List[Long],
        _: String,
        _: Option[Long],
        _: DateTime,
        _: Long
      )(using _: SRLogger))
        .expects(*, *, *, *, *, *)
        .returning(Success(Set())) // no prospects have are on holiday

      (campaignProspectDAO.filterByProspectValidationStatus(_: Set[Long], _: TeamId)(using _: SRLogger))
        .expects(*, *, *)
        .returning(Success(Set()))

      (campaignProspectDAO.filterProspectsBySentCountBasedOnProspectLimit(
        _: Set[Long],
        _: String,
        _: TeamId,
        _: Int,
        _: Int
      )(using _: SRLogger))
        .expects(*, *, *, *, *, *)
        .returning(Failure(error))

      val res = emailChannelScheduler.fetchProspects(
        channelType = ChannelType.EmailChannel,
        campaignProspectDAO =  campaignProspectDAO,
        campaignProspectService = campaignProspectService,
        scheduleFromTime = aDate,
        campaign = campaignForSchedulingEmail,
        allowedTimezonesTry = Success(Set(timeZone)),
        prospectsToFetch = 50,
        channelRelevantStepIdAndDelay = Vector(schedulerMapStepIdAndDelay),
        newProspectsInCampaign = false, // also test for true
        firstStepIsMagicContent = false,
        //        srRollingUpdateCoreService = srRollingUpdateCoreService,
        sendOnlyToProspectsWhoWereSentInCurrentCycle = Some(aDate),
        org_id = OrgId(orgId.toLong),
        srRollingUpdateCoreService = srRollingUpdateCoreService
      )

//      println(s"\nres: $res\n")

      assert(res == Failure(error))
    }


    it("should success and return list of prospectIds") {

//      (campaignProspectDAO.getCampaignProspectsTimezoneMetadata(_: Long, _: Long))
//        .expects(teamId, campaignId)
//        .returning(Success(List(campaignProspectsTimezoneMetadata)))

//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(_: TeamId, _: SrRollingUpdateFeature)(_: ISRLogger))
//        .expects(TeamId(teamId), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .returning(false)

      (campaignProspectService.fetchProspectsV3MultichannelWithEmailOptionalCheck(
        _: ChannelType,
        _: List[String],
        _: Option[Long],
        _: Long,
        _: TeamId,
        _: Int,
        _: Vector[SchedulerMapStepIdAndDelay],
        _: Boolean, _: Boolean,
        _: Option[DateTime],
        _: Boolean,
        _: Option[CampaignEmailSettingForScheduler],
        _: OrgId, _:String)(using _: SRLogger))
        .expects(*, *, *, *, *, *, *, *, *, *, *, *, *, *, *)
        .returning(Success(List(prospectForScheduling)))

      (campaignProspectDAO.filterProspectWhoHaveHoliday(
        _: List[Long],
        _: String,
        _: Option[Long],
        _: DateTime,
        _: Long
      )(using _: SRLogger))
        .expects(*, *, *, *, *, *)
        .returning(Success(Set())) // no prospects have holiday today

      (campaignProspectDAO.filterByProspectValidationStatus(_: Set[Long], _: TeamId)(using _: SRLogger))
        .expects(*, *, *)
        .returning(Success(Set(prospectId)))

      (campaignProspectDAO.filterProspectsBySentCountBasedOnProspectLimit(
        _: Set[Long],
        _: String,
        _: TeamId,
        _: Int,
        _: Int
      )(using _: SRLogger))
        .expects(*, *, *, *, *, *)
        .returning(Success(Set(prospectId)))

      (orgMetadataService.filterProspectsBasedOnDomainLimit)
        .expects(*)
        .returning(Success(true))

      (campaignProspectDAO.mapProspectIdsWithProspectAccountIds)
        .expects(*, *)
        .returning(Success(Map(ProspectId(prospectId) -> Some(ProspectAccountsId(5L)))))

      (campaignProspectDAO.mapProspectIdsWithProspectAccountIds)
        .expects(*, *)
        .returning(Success(Map()))

      (campaignProspectDAO.getEmailsScheduledInLast24HoursAnd7DaysForAProspectAccount(
        _: List[ProspectAccountsId],
        _: String,
        _: TeamId
      ))
        .expects(*, *, *)
        .returning(Success(Map(ProspectAccountsId(5L) -> EmailsScheduledCount(3, 3))))

      val res = emailChannelScheduler.fetchProspects(
        channelType = ChannelType.EmailChannel,
        campaignProspectDAO =  campaignProspectDAO,
        campaignProspectService = campaignProspectService,
        scheduleFromTime = aDate,
        campaign = campaignForSchedulingEmail,
        allowedTimezonesTry = Success(Set(timeZone)),
        prospectsToFetch = 50,
        channelRelevantStepIdAndDelay = Vector(schedulerMapStepIdAndDelay),
        newProspectsInCampaign = false, // also test for true
        firstStepIsMagicContent = false,
        //        srRollingUpdateCoreService = srRollingUpdateCoreService,
        sendOnlyToProspectsWhoWereSentInCurrentCycle = Some(aDate),
        org_id = OrgId(orgId.toLong),
        srRollingUpdateCoreService = srRollingUpdateCoreService
      )

//      println(s"\nres: $res\n")

      assert(res == Success(List(prospectForSchedulingEmail)))
    }


    it("should filter out prospects having holiday prospectIds success and return list of prospectIds") {

      val holidayProspectIds = Set(1L, 2L)

//      (campaignProspectDAO.getCampaignProspectsTimezoneMetadata(_: Long, _: Long))
//        .expects(teamId, campaignId)
//        .returning(Success(List(campaignProspectsTimezoneMetadata)))

//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(_: TeamId, _: SrRollingUpdateFeature)(_: ISRLogger))
//        .expects(TeamId(teamId), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .returning(false)

      (campaignProspectService.fetchProspectsV3MultichannelWithEmailOptionalCheck(
        _: ChannelType,
        _: List[String],
        _: Option[Long],
        _: Long,
        _: TeamId,
        _: Int,
        _: Vector[SchedulerMapStepIdAndDelay],
        _: Boolean, _: Boolean,
        _: Option[DateTime],
        _: Boolean,
        _: Option[CampaignEmailSettingForScheduler],
        _: OrgId, _:String)(using _: SRLogger))
        .expects(*, *, *, *, *, *, *, *, *, *, *, *, *, *, *)
        .returning(Try(List(prospectForScheduling,
          prospectForScheduling.copy(prospect = prospectObject.copy(id = 1L)),
          prospectForScheduling.copy(prospect = prospectObject.copy(id = 4L)))
        ))

      (campaignProspectDAO.filterProspectWhoHaveHoliday(
        _: List[Long],
        _: String,
        _: Option[Long],
        _: DateTime,
        _: Long
      )(using _: SRLogger))
        .expects(*, *, *, *, *, *)
        .returning(Success(holidayProspectIds)) // prospects with prospectId 1L have holiday today

      (campaignProspectDAO.filterByProspectValidationStatus(_: Set[Long], _: TeamId)(using _: SRLogger))
        .expects(Set(prospectId, 4L), *, *)
        .returning(Success(Set(prospectId, 4L)))

      (campaignProspectDAO.filterProspectsBySentCountBasedOnProspectLimit(
        _: Set[Long],
        _: String,
        _: TeamId,
        _: Int,
        _: Int
      )(using _: SRLogger))
        .expects(Set(prospectId, 4L), *, *, *, *, *)
        .returning(Success(Set(prospectId, 4L)))

      (orgMetadataService.filterProspectsBasedOnDomainLimit)
        .expects(*)
        .returning(Success(true))

      (campaignProspectDAO.mapProspectIdsWithProspectAccountIds(
        _: Set[ProspectId],
        _: TeamId
      ))
        .expects(*, *)
        .returning(Success(Map(ProspectId(prospectId) -> Some(ProspectAccountsId(5L)), ProspectId(4L) -> Some(ProspectAccountsId(5L)))))

      (campaignProspectDAO.mapProspectIdsWithProspectAccountIds)
        .expects(*, *)
        .returning(Success(Map()))

      (campaignProspectDAO.getEmailsScheduledInLast24HoursAnd7DaysForAProspectAccount(
        _: List[ProspectAccountsId],
        _: String,
        _: TeamId
      ))
      .expects(*, *, *)
        .returning(Success(Map(ProspectAccountsId(5L) -> EmailsScheduledCount(3, 3))))

      val res = emailChannelScheduler.fetchProspects(
        channelType = ChannelType.EmailChannel,
        campaignProspectDAO =  campaignProspectDAO,
        campaignProspectService = campaignProspectService,
        scheduleFromTime = aDate,
        campaign = campaignForSchedulingEmail,
        allowedTimezonesTry = Success(Set(timeZone)),
        prospectsToFetch = 50,
        channelRelevantStepIdAndDelay = Vector(schedulerMapStepIdAndDelay),
//        srRollingUpdateCoreService = srRollingUpdateCoreService,
        newProspectsInCampaign = false, // also test for true
        firstStepIsMagicContent = false,
        sendOnlyToProspectsWhoWereSentInCurrentCycle = Some(aDate),
        org_id = OrgId(orgId.toLong),
        srRollingUpdateCoreService = srRollingUpdateCoreService
      )

//      println(s"\nres: $res\n")

      assert(res == Success(List(prospectForSchedulingEmail,
        prospectForSchedulingEmail.copy(prospect = prospectObject.copy(id = 4L)))
      ))
    }

    it("should filter prospects having prospect ids in  filterByProspectValidationStatus success and return list of prospectIds") {

      val holidayProspectIds = Set(1L, 2L)

//      (campaignProspectDAO.getCampaignProspectsTimezoneMetadata(_: Long, _: Long))
//        .expects(teamId, campaignId)
//        .returning(Success(List(campaignProspectsTimezoneMetadata)))

//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(_: TeamId, _: SrRollingUpdateFeature)(_: ISRLogger))
//        .expects(TeamId(teamId), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .returning(true)

      (campaignProspectService.fetchProspectsV3MultichannelWithEmailOptionalCheck(
        _: ChannelType,
        _: List[String],
        _: Option[Long],
        _: Long,
        _: TeamId,
        _: Int,
        _: Vector[SchedulerMapStepIdAndDelay],
        _: Boolean, _: Boolean,
        _: Option[DateTime],
        _: Boolean,
        _: Option[CampaignEmailSettingForScheduler],
        _: OrgId, _:String)(using _: SRLogger))
        .expects(*, *, *, *, *, *, *, *, *, *, *, *, *, *, *)
        .returning(Try(List(prospectForScheduling,
          prospectForScheduling.copy(prospect = prospectObject.copy(id = 1L)),
          prospectForScheduling.copy(prospect = prospectObject.copy(id = 4L)))
        ))

      (campaignProspectDAO.filterProspectWhoHaveHoliday(
        _: List[Long],
        _: String,
        _: Option[Long],
        _: DateTime,
        _: Long
      )(using _: SRLogger))
        .expects(*, *, *, *, *, *)
        .returning(Success(Set())) // prospects with prospectId 1L have holiday today

      (campaignProspectDAO.filterByProspectValidationStatus(_: Set[Long], _: TeamId)(using _: SRLogger))
        .expects(Set(prospectId,1L, 4L), *, *)
        .returning(Success(Set(1L,4L)))

      (campaignProspectDAO.filterProspectsBySentCountBasedOnProspectLimit(
        _: Set[Long],
        _: String,
        _: TeamId,
        _: Int,
        _: Int
      )(using _: SRLogger))
        .expects(Set( 1L,4L), *, *, *, *, *)
        .returning(Success(Set(1L, 4L)))

      (orgMetadataService.filterProspectsBasedOnDomainLimit)
        .expects(*)
        .returning(Success(true))

      (campaignProspectDAO.mapProspectIdsWithProspectAccountIds(
        _: Set[ProspectId],
        _: TeamId
      ))
        .expects(*, *)
        .returning(Success(Map(ProspectId(1L) -> Some(ProspectAccountsId(5L)), ProspectId(4L) -> Some(ProspectAccountsId(5L)))))

      (campaignProspectDAO.mapProspectIdsWithProspectAccountIds)
        .expects(*, *)
        .returning(Success(Map()))

      (campaignProspectDAO.getEmailsScheduledInLast24HoursAnd7DaysForAProspectAccount(
        _: List[ProspectAccountsId],
        _: String,
        _: TeamId
      ))
      .expects(*, *, *)
        .returning(Success(Map(ProspectAccountsId(5L) -> EmailsScheduledCount(3, 3))))

      val res = emailChannelScheduler.fetchProspects(
        channelType = ChannelType.EmailChannel,
        campaignProspectDAO =  campaignProspectDAO,
        campaignProspectService = campaignProspectService,
        scheduleFromTime = aDate,
        campaign = campaignForSchedulingEmail,
        allowedTimezonesTry = Success(Set(timeZone)),
        prospectsToFetch = 50,
        channelRelevantStepIdAndDelay = Vector(schedulerMapStepIdAndDelay),
//        srRollingUpdateCoreService = srRollingUpdateCoreService,
        newProspectsInCampaign = false, // also test for true
        firstStepIsMagicContent = false,
        sendOnlyToProspectsWhoWereSentInCurrentCycle = Some(aDate),
        org_id = OrgId(orgId.toLong),
        srRollingUpdateCoreService = srRollingUpdateCoreService
      )

      //      println(s"\nres: $res\n")

      assert(res == Success(List(
        prospectForSchedulingEmail.copy(prospect = prospectObject.copy(id = 1L)),
        prospectForSchedulingEmail.copy(prospect = prospectObject.copy(id = 4L)))
      ))
    }

    it("should run while loop for 100 times then returns empty ProspectIds") {

      val holidayProspectIds = Set(1L, 2L)

//      (campaignProspectDAO.getCampaignProspectsTimezoneMetadata(_: Long, _: Long))
//        .expects(teamId, campaignId)
//        .returning(Success(List(campaignProspectsTimezoneMetadata)))
//        .once()

//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(_: TeamId, _: SrRollingUpdateFeature)(_: ISRLogger))
//        .expects(TeamId(teamId), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .atLeastTwice()
//        .returning(false)


      (campaignProspectService.fetchProspectsV3MultichannelWithEmailOptionalCheck(
        _: ChannelType,
        _: List[String],
        _: Option[Long],
        _: Long,
        _: TeamId,
        _: Int,
        _: Vector[SchedulerMapStepIdAndDelay],
        _: Boolean, _: Boolean,
        _: Option[DateTime],
        _: Boolean,
        _: Option[CampaignEmailSettingForScheduler],
        _: OrgId, _:String)(using _: SRLogger))
        .expects(*, *, *, *, *, *, *, *, *, *, *, *, *, *, *)
        .returning(Try(List(prospectForScheduling,
          prospectForScheduling.copy(prospect = prospectObject.copy(id = 1L)),
          prospectForScheduling.copy(prospect = prospectObject.copy(id = 4L)))
        ))
        .atLeastTwice()

      (campaignProspectDAO.filterProspectWhoHaveHoliday(
        _: List[Long],
        _: String,
        _: Option[Long],
        _: DateTime,
        _: Long
      )(using _: SRLogger))
        .expects(*, *, *, *, *, *)
        .returning(Success(Set(prospectId, 1L, 4L)))
      .atLeastTwice()// prospects with prospectId 1L have holiday today

      (campaignProspectDAO.filterByProspectValidationStatus(_: Set[Long], _: TeamId)(using _: SRLogger))
        .expects(*, *, *)
        .returning(Success(Set()))
        .atLeastTwice()

      (campaignProspectDAO.filterProspectsBySentCountBasedOnProspectLimit(
        _: Set[Long],
        _: String,
        _: TeamId,
        _: Int,
        _: Int
      )(using _: SRLogger))
        .expects(*, *, *, *, *, *)
        .returning(Success(Set()))
        .atLeastTwice()

      (orgMetadataService.filterProspectsBasedOnDomainLimit)
        .expects(*)
        .returning(Success(true))
        .atLeastOnce()

      (campaignProspectDAO.mapProspectIdsWithProspectAccountIds)
        .expects(*, *)
        .returning(Success(Map()))
        .atLeastOnce()

      (campaignProspectDAO.getEmailsScheduledInLast24HoursAnd7DaysForAProspectAccount(
        _: List[ProspectAccountsId],
        _: String,
        _: TeamId
      ))
        .expects(*, *, *)
        .returning(Success(Map()))
        .atLeastOnce()

      val res = emailChannelScheduler.fetchProspects(
        channelType = ChannelType.EmailChannel,
        campaignProspectDAO =  campaignProspectDAO,
        campaignProspectService = campaignProspectService,
        scheduleFromTime = aDate,
        campaign = campaignForSchedulingEmail,
        allowedTimezonesTry = Success(Set(timeZone)),
        prospectsToFetch = 2,
        channelRelevantStepIdAndDelay = Vector(schedulerMapStepIdAndDelay),
//        srRollingUpdateCoreService = srRollingUpdateCoreService,
        newProspectsInCampaign = false, // also test for true
        firstStepIsMagicContent = false,
        sendOnlyToProspectsWhoWereSentInCurrentCycle = Some(aDate),
        org_id = OrgId(orgId.toLong),
        srRollingUpdateCoreService = srRollingUpdateCoreService
      )

      //      println(s"\nres: $res\n")

      assert(res == Success(List()))
    }



  }

  describe("testing ChannelSchedulerTrait.getProspectsToCreateTasks") {

    it("should fail when fetchProspects fails while fetching followUp Prospects") {

//      (campaignProspectDAO.getCampaignProspectsTimezoneMetadata(_: Long, _: Long))
//        .expects(teamId, campaignId)
//        .returning(Failure(error))

      val result = emailChannelScheduler.getProspectsToCreateTasks(
        fetchLimitForFirstStep = 50,
        fetchLimitForFollowup = 50,
        scheduleFromTime = aDate,
        campaign = campaignForSchedulingEmail,
        fetchCampaignStepsDataResult = fetchCampaignStepsData,
        allowedTimezones = Failure(error),
        schedulingForStepType = CampaignStepType.AutoEmailStep,
        sendOnlyToProspectsWhoWereSentInCurrentCycle = None,
        campaignProspectDAO = campaignProspectDAO,
        campaignProspectService = campaignProspectService,
//        srRollingUpdateCoreService = srRollingUpdateCoreService,
        channelType = ChannelType.EmailChannel,
        isThisChannelFirstStep = true,
        org_id = OrgId(orgId.toLong),
        srRollingUpdateCoreService = srRollingUpdateCoreService
      )(Logger)

      assert(result == Failure(error))

    }

    it ("should fail when fetchProspects fails while fetching newProspects") {

      val result = emailChannelScheduler.getProspectsToCreateTasks(
        fetchLimitForFirstStep = 50,
        fetchLimitForFollowup = 50,
        scheduleFromTime = aDate,
        campaign = campaignForSchedulingEmail,
        fetchCampaignStepsDataResult = fetchCampaignStepsData,
        allowedTimezones = Failure(error),
        schedulingForStepType = CampaignStepType.AutoEmailStep,
        sendOnlyToProspectsWhoWereSentInCurrentCycle = None,
        campaignProspectDAO = campaignProspectDAO,
        campaignProspectService = campaignProspectService,
//        srRollingUpdateCoreService = srRollingUpdateCoreService,
        channelType = ChannelType.EmailChannel,
        isThisChannelFirstStep = true,
        org_id = OrgId(orgId.toLong),
        srRollingUpdateCoreService = srRollingUpdateCoreService
      )(Logger)

      assert(result == Failure(error))

    }

    it ("should not fetchProspects and return Empty list for newProspects if currentStepType does not match") {

      val fetchCampaignStepsDataGeneral = fetchCampaignStepsData.copy(
        allCampaignSteps = Vector(schedulerMapStepIdAndDelay.copy(
          currentStepType = CampaignStepType.LinkedinInmail)
        )
      )

//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(_: TeamId, _: SrRollingUpdateFeature)(_: ISRLogger))
//        .expects(TeamId(teamId), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .returning(false)



      (campaignProspectService.fetchProspectsV3MultichannelWithEmailOptionalCheck(
        _: ChannelType,
        _: List[String],
        _: Option[Long],
        _: Long,
        _: TeamId,
        _: Int,
        _: Vector[SchedulerMapStepIdAndDelay],
        _: Boolean, _: Boolean,
        _: Option[DateTime],
        _: Boolean,
        _: Option[CampaignEmailSettingForScheduler],
        _: OrgId, _:String)(using _: SRLogger))
        .expects(*, *, *, *, *, *, *, *, *, *, *, *, *, *, *)
        .returning(Try(List(prospectForScheduling)))

      (campaignProspectDAO.filterProspectWhoHaveHoliday(
        _: List[Long],
        _: String,
        _: Option[Long],
        _: DateTime,
        _: Long
      )(using _: SRLogger))
        .expects(*, *, *, *, *, *)
        .returning(Success(Set())) // no prospects have holiday today

      (campaignProspectDAO.filterByProspectValidationStatus(_: Set[Long], _: TeamId)(using _: SRLogger))
        .expects(*, *, *)
        .returning(Success(Set(prospectId)))

      (campaignProspectDAO.filterProspectsBySentCountBasedOnProspectLimit(
        _: Set[Long],
        _: String,
        _: TeamId,
        _: Int,
        _: Int
      )(using _: SRLogger))
        .expects(*, *, *, *, *, *)
        .returning(Success(Set(prospectId)))

      (orgMetadataService.filterProspectsBasedOnDomainLimit)
        .expects(*)
        .returning(Success(true))

      (campaignProspectDAO.mapProspectIdsWithProspectAccountIds(
        _: Set[ProspectId],
        _: TeamId
      ))
        .expects(*, *)
        .returning(Success(Map(ProspectId(prospectId) -> Some(ProspectAccountsId(5L)))))

      (campaignProspectDAO.mapProspectIdsWithProspectAccountIds)
        .expects(*, *)
        .returning(Success(Map()))

      (campaignProspectDAO.getEmailsScheduledInLast24HoursAnd7DaysForAProspectAccount(
        _: List[ProspectAccountsId],
        _: String,
        _: TeamId
      ))
      .expects(*, *, *)
        .returning(Success(Map(ProspectAccountsId(5L) -> EmailsScheduledCount(3, 3))))

      emailChannelScheduler.getProspectsToCreateTasks(
        fetchLimitForFirstStep = 50,
        fetchLimitForFollowup = 50,
        scheduleFromTime = aDate,
        campaign = campaignForSchedulingEmail,
        fetchCampaignStepsDataResult = fetchCampaignStepsDataGeneral,
        allowedTimezones = Success(Set(timeZone)),
        schedulingForStepType = CampaignStepType.AutoEmailStep,
        sendOnlyToProspectsWhoWereSentInCurrentCycle = None,
        campaignProspectDAO = campaignProspectDAO,
        campaignProspectService = campaignProspectService,
//        srRollingUpdateCoreService = srRollingUpdateCoreService,
        channelType = ChannelType.EmailChannel,
        isThisChannelFirstStep = false,
        org_id = OrgId(orgId.toLong),
        srRollingUpdateCoreService = srRollingUpdateCoreService
      )(Logger) match {

        case Failure(e) => assert(false)

        case Success(value) =>
          assert(value.foundFollowUpProspects == List(prospectForSchedulingEmail))
          assert(value.foundNewProspects == List())

      }

    }

    it ("should return empty list of followup and new prospects when fetchProspects returns empty list for both") {

//      (campaignProspectDAO.getCampaignProspectsTimezoneMetadata(_: Long, _: Long))
//        .expects(teamId, campaignId)
//        .returning(Success(List()))
//        .atLeastTwice()
      (campaignProspectService.getCampaignsToLogDripFor()(using _:SRLogger))
        .expects(*)
        .returning(List())
      val result = emailChannelScheduler.getProspectsToCreateTasks(
        fetchLimitForFirstStep = 50,
        fetchLimitForFollowup = 50,
        scheduleFromTime = aDate,
        campaign = campaignForSchedulingEmail,
        fetchCampaignStepsDataResult = fetchCampaignStepsData,
        allowedTimezones = Success(Set()),
        schedulingForStepType = CampaignStepType.AutoEmailStep,
        sendOnlyToProspectsWhoWereSentInCurrentCycle = None,
        campaignProspectDAO = campaignProspectDAO,
        campaignProspectService = campaignProspectService,
//        srRollingUpdateCoreService = srRollingUpdateCoreService,
        channelType = ChannelType.EmailChannel,
        isThisChannelFirstStep = true,
        org_id = OrgId(orgId.toLong),
        srRollingUpdateCoreService = srRollingUpdateCoreService
      )(Logger)

      assert(result == Success(emailChannelScheduler.ProspectsToCreateTasksResult(
        foundNewProspects = List(), foundFollowUpProspects = List()
      )))

    }

    it ("should fetch newProspects if this is first step.") {

      val fetchCampaignStepsDataGeneral = fetchCampaignStepsData.copy(
        allCampaignSteps = Vector(schedulerMapStepIdAndDelay.copy(
          currentStepType = CampaignStepType.LinkedinInmail)
        )
      )

      // Setting allCampaignSteps to empty vector will force the isFirstStep flow.
      val fetchCampaignStepsDataResult = fetchCampaignStepsDataGeneral.copy(
        allCampaignSteps = Vector()
      )

//      (campaignProspectDAO.getCampaignProspectsTimezoneMetadata(_: Long, _: Long))
//        .expects(teamId, campaignId)
//        .returning(Success(List(campaignProspectsTimezoneMetadata)))
//        .atLeastTwice()

//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(_: TeamId, _: SrRollingUpdateFeature)(_: ISRLogger))
//        .expects(TeamId(teamId), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .atLeastTwice()
//        .returning(false)
      (campaignProspectService.getCampaignsToLogDripFor()(using _:SRLogger))
        .expects(*)
        .returning(List())
      (campaignProspectService.fetchProspectsV3MultichannelWithEmailOptionalCheck(
        _: ChannelType,
        _: List[String],
        _: Option[Long],
        _: Long,
        _: TeamId,
        _: Int,
        _: Vector[SchedulerMapStepIdAndDelay],
        _: Boolean, _: Boolean,
        _: Option[DateTime],
        _: Boolean,
        _: Option[CampaignEmailSettingForScheduler],
        _: OrgId, _:String)(using _: SRLogger))
        .expects(*, *, *, *, *, *, *, *, *, *, *, *, *, *, *)
        .returning(Try(List(prospectForScheduling)))
        .atLeastTwice()

      (campaignProspectDAO.filterProspectWhoHaveHoliday(
        _: List[Long],
        _: String,
        _: Option[Long],
        _: DateTime,
        _: Long
      )(using _: SRLogger))
        .expects(*, *, *, *, *, *)
        .returning(Success(Set())) // no prospects have holiday today
        .atLeastTwice()

      (campaignProspectDAO.filterByProspectValidationStatus(_: Set[Long], _: TeamId)(using _: SRLogger))
        .expects(*, *, *)
        .returning(Success(Set(prospectId)))
        .atLeastTwice()

      (campaignProspectDAO.filterProspectsBySentCountBasedOnProspectLimit(
        _: Set[Long],
        _: String,
        _: TeamId,
        _: Int,
        _: Int
      )(using _: SRLogger))
        .expects(*, *, *, *, *, *)
        .returning(Success(Set(prospectId)))
        .atLeastTwice()

      (orgMetadataService.filterProspectsBasedOnDomainLimit)
        .expects(*)
        .returning(Success(true))
        .atLeastOnce()

      (campaignProspectDAO.mapProspectIdsWithProspectAccountIds(
        _: Set[ProspectId],
        _: TeamId
      ))
        .expects(*, *)
        .returning(Success(Map(ProspectId(prospectId) -> Some(ProspectAccountsId(5L)))))
        .atLeastOnce()

      (campaignProspectDAO.getEmailsScheduledInLast24HoursAnd7DaysForAProspectAccount(
        _: List[ProspectAccountsId],
        _: String,
        _: TeamId
      ))
      .expects(*, *, *)
        .returning(Success(Map(ProspectAccountsId(5L) -> EmailsScheduledCount(3, 3))))
        .atLeastOnce()

      emailChannelScheduler.getProspectsToCreateTasks(
        fetchLimitForFirstStep = 50,
        fetchLimitForFollowup = 50,
        scheduleFromTime = aDate,
        campaign = campaignForSchedulingEmail,
        fetchCampaignStepsDataResult = fetchCampaignStepsDataResult,
        allowedTimezones = Success(Set(timeZone)),
        schedulingForStepType = CampaignStepType.AutoEmailStep,
        sendOnlyToProspectsWhoWereSentInCurrentCycle = None,
        campaignProspectDAO = campaignProspectDAO,
        campaignProspectService = campaignProspectService,
        //        srRollingUpdateCoreService = srRollingUpdateCoreService,
        channelType = ChannelType.EmailChannel,
        isThisChannelFirstStep = true,
        org_id = OrgId(orgId.toLong),
        srRollingUpdateCoreService = srRollingUpdateCoreService
      )(Logger) match {

        case Failure(e) => assert(false)

        case Success(value) =>
          assert(value.foundFollowUpProspects == List(prospectForSchedulingEmail))
          assert(value.foundNewProspects == List(prospectForSchedulingEmail))

      }

    }

  }

  describe("testing ChannelSchedulerTrait.getChannelForScheduling") {

    it ("should return None when ChannelId cannot be converted to Long") {

      val result = emailChannelScheduler.getChannelForScheduling(
        channelId = ChannelId("A"),
        teamId = TeamId(3L),
        logger = Logger
      )

      assert(result.isEmpty)
    }

    it ("should return empty when emailSettingDAO.findForScheduling returns None") {

      (emailSettingDAO.findForScheduling)
        .expects(1L, Logger)
        .returning(None)

      val result = emailChannelScheduler.getChannelForScheduling(
        channelId = ChannelId("1"),
        teamId = TeamId(3L),
        logger = Logger
      )

      assert(result.isEmpty)

    }

    it ("should return ChannelDataForScheduling when emailSettingDAO.findForScheduling return emailSetting") {

      (emailSettingDAO.findForScheduling)
        .expects(1L, Logger)
        .returning(Some(emailSettingCreateEmailSchedule))

      emailChannelScheduler.getChannelForScheduling(
        channelId = ChannelId("1"),
        teamId = TeamId(teamId),
        logger = Logger
      ) match {

        case None => assert(false)

        case Some(channelDataFromDB) =>
          assert(emailChannelDataForScheduling == channelDataFromDB)

      }

    }
  }

  describe("testing ChannelSchedulerTrait.flattenStepTypes") {

    it ("should return distinct union of all campaign_steps") {

      val result = emailChannelScheduler.flattenStepTypes(
        campaignStepTypesMap = Map(3L -> List(CampaignStepType.AutoEmailStep, CampaignStepType.LinkedinInmail),
          2L -> List(CampaignStepType.AutoEmailStep, CampaignStepType.GeneralTask))
      )

      assert(result == Set(CampaignStepType.AutoEmailStep, CampaignStepType.LinkedinInmail, CampaignStepType.GeneralTask))

    }

  }

  describe("testing CampaignStepDAO.getCampaignStepTypesMap") {

    it("should return distinct union of all campaign_steps") {

      val result = CampaignStepDAO.getCampaignStepTypesMap(
        campaignStepTypes = List((2L, CampaignStepType.AutoEmailStep), (3L, CampaignStepType.ManualEmailStep),
          (2L, CampaignStepType.GeneralTask))
      )

      Logger.info(s"CampaignStepDAO.getCampaignStepTypesMap result: $result")
      assert(result == Map(2L -> List(CampaignStepType.AutoEmailStep, CampaignStepType.GeneralTask), 3L -> List(CampaignStepType.ManualEmailStep)))

    }

  }

  describe("testing ChannelSchedulerTrait.getChannelTasksToBeScheduled") {

    it("should successfully return Vector of GenerateScheduleTaskData") {

      val step_data2 = AutoEmailStep(
        subject = "Email Subject 2",
        body = "Email Body 2"
      )

      val step_data5 = AutoEmailStep(
        subject = "Email Subject 5",
        body = "Email Body 5"
      )

      val stepId2 = 2L
      val stepId5 = 5L

      val campaignStepWithChildren2 = campaignStepWithChildren.copy(
        id = stepId2,
        campaign_id = 2,
        variants = Seq(campaignStepVariantForScheduling.copy(step_data = step_data2)),
        children = List())

      val campaignStepWithChildren5 = campaignStepWithChildren.copy(
        id = stepId5,
        campaign_id = 2,
        variants = Seq(campaignStepVariantForScheduling.copy(step_data = step_data5)),
        children = List())

      val scheduleCampaign2 = scheduleCampaign.copy(
        campaign = campaignForScheduling.copy(campaign_id = 2L),
        stepsMappedById = Map(stepId2 -> campaignStepWithChildren2, stepId5 -> campaignStepWithChildren5),
        campaignStepsStructure = CampaignStepsStructure.MultichannelCampaignStepsStructure(
          orderedStepIds = Vector(stepId2, stepId5)
        ),
      )

      val campaignListForScheduling = Seq(scheduleCampaign, scheduleCampaign2)

      (srShuffleUtils.shuffleList[emailChannelScheduler.ScheduleCampaign])
        .expects(
          Seq(scheduleCampaign, scheduleCampaign2))
        .returning(Seq(scheduleCampaign2, scheduleCampaign))


      val subjectAndBody = getSubjectAndBody(
        stepsMappedById = scheduleCampaign2.stepsMappedById,
        campaignStepsStructure = scheduleCampaign2.campaignStepsStructure,
      )

      (campaignsMissingMergeTagService.checkPendingMagicColumn(_: TeamId, _: String, _: String, _: ProspectObject, _: CampaignId, _: ChannelType)(_: SRLogger))
        .expects(
          TeamId(id = teamId),
          subjectAndBody.subject,
          subjectAndBody.body,
          scheduleCampaign2.prospects.head.prospectForScheduling.prospect,
          CampaignId(id = scheduleCampaign2.campaign.campaign_id),
          ChannelType.EmailChannel,
          Logger,
        )
        .returning(Success(List()))

      (campaignsMissingMergeTagService.checkMagicColumnWithErrors(_: TeamId, _: String, _: String, _: ProspectObject, _: ChannelType)(_: SRLogger))
        .expects(
          TeamId(id = teamId),
          subjectAndBody.subject,
          subjectAndBody.body,
          scheduleCampaign2.prospects.head.prospectForScheduling.prospect,
          ChannelType.EmailChannel,
          Logger,
        )
        .returning(Success(List()))


      val subjectAndBody2 = getSubjectAndBody(
        stepsMappedById = scheduleCampaign.stepsMappedById,
        campaignStepsStructure = scheduleCampaign.campaignStepsStructure,
      )

      (campaignsMissingMergeTagService.checkPendingMagicColumn(_: TeamId, _: String, _: String, _: ProspectObject, _: CampaignId, _: ChannelType)(_: SRLogger))
        .expects(
          TeamId(id = teamId),
          subjectAndBody2.subject,
          subjectAndBody2.body,
          scheduleCampaign.prospects.head.prospectForScheduling.prospect,
          CampaignId(id = scheduleCampaign.campaign.campaign_id),
          ChannelType.EmailChannel,
          Logger,
        )
        .returning(Success(List()))

      (campaignsMissingMergeTagService.checkMagicColumnWithErrors(_: TeamId, _: String, _: String, _: ProspectObject, _: ChannelType)(_: SRLogger))
        .expects(
          TeamId(id = teamId),
          subjectAndBody2.subject,
          subjectAndBody2.body,
          scheduleCampaign.prospects.head.prospectForScheduling.prospect,
          ChannelType.EmailChannel,
          Logger,
        )
        .returning(Success(List()))


      (emailServiceCompanion.getInternalMergeTag(_: String, _: String, _: String, _: Option[String], _: Option[String], _: Option[Long], _: Option[Long], _: Seq[PreviousFollowUp], _: Long, _: Long, _: TeamId, _: Boolean, _: Option[String])(using _: SRLogger))
        .expects(*, *, *, *, *, *, *, *, *, *, *, *, *, *)
        .returning(Success(internalMergeTagValuesForProspect))
        .atLeastTwice()



//      (templateService.checkMissingMergeTags)
//        .expects(*, *, *, *, *, *)
//        .returning(Success(Seq()))
//        .atLeastTwice()

      (srRandomUtils.getRandomDelay)
        .expects(*, *)
        .returning(60)
        .atLeastTwice()

      val result = emailChannelScheduler.getChannelTasksToBeScheduled(
        channelDataForScheduling = emailChannelDataForScheduling,
        channelStepTypeDataForScheduling = channelStepTypeDataForSchedulingMap,
        campaignListForScheduling = campaignListForScheduling,
        scheduleFromTime = aDate,
        scheduleTillTime = aDate.plusHours(3),
        maxToBeScheduledForNextHour = 5,
        emailServiceCompanion = emailServiceCompanion,
        templateService = templateService,
        srShuffleUtils = srShuffleUtils,
        campaignProspectDAO = campaignProspectDAO,
        campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO,
        campaignsMissingMergeTagService = campaignsMissingMergeTagService,
        isCalendarEnabled = account.org.org_metadata.enable_calendar.getOrElse(false),
        calendarAccountData = account.calendar_account_data,
        campaignProspectService = campaignProspectService,
        Logger = Logger
      )

      result match {
        case Success(Vector(v1, v2)) =>

          assert(v1.currentProspect.prospectForScheduling.prospect.id == prospectId)
          assert(v2.currentProspect.prospectForScheduling.prospect.id == prospectId)

        case _ =>
          assert(false)
      }

    }

    it("should not schedule tasks more than remainingToBeScheduledFromChannelStepType") {

      val channelStepTypeDataForSchedulingMap1 = Map(CampaignStepType.LinkedinInmail.asInstanceOf[CampaignStepType] ->
        channelStepTypeDataForSchedulingLinkedin.copy(
          remainingToBeScheduledFromChannelStepType = 2
        ))

      (srShuffleUtils.shuffleList[linkedinChannelScheduler.ScheduleCampaign])
        .expects(Seq(scheduleCampaign2LinkedinProspects4))
        .returning(Seq(scheduleCampaign2LinkedinProspects4))


      val subjectAndBody = getSubjectAndBody(
        stepsMappedById = scheduleCampaign2LinkedinProspects4.stepsMappedById,
        campaignStepsStructure = scheduleCampaign2LinkedinProspects4.campaignStepsStructure,
      )

      (campaignsMissingMergeTagService.checkPendingMagicColumn(_: TeamId, _: String, _: String, _: ProspectObject, _: CampaignId, _: ChannelType)(_: SRLogger))
        .expects(
          TeamId(id = teamId),
          subjectAndBody.subject,
          subjectAndBody.body,
          scheduleCampaign2LinkedinProspects4.prospects.head.prospectForScheduling.prospect,
          CampaignId(id = scheduleCampaign2LinkedinProspects4.campaign.campaign_id),
          ChannelType.LinkedinChannel,
          Logger,
        )
        .returning(Success(List()))

      (campaignsMissingMergeTagService.checkMagicColumnWithErrors(_: TeamId, _: String, _: String, _: ProspectObject, _: ChannelType)(_: SRLogger))
        .expects(
          TeamId(id = teamId),
          subjectAndBody.subject,
          subjectAndBody.body,
          scheduleCampaign2LinkedinProspects4.prospects.head.prospectForScheduling.prospect,
          ChannelType.LinkedinChannel,
          Logger,
        )
        .returning(Success(List()))


      (campaignsMissingMergeTagService.checkPendingMagicColumn(_: TeamId, _: String, _: String, _: ProspectObject, _: CampaignId, _: ChannelType)(_: SRLogger))
        .expects(
          TeamId(id = teamId),
          subjectAndBody.subject,
          subjectAndBody.body,
          scheduleCampaign2LinkedinProspects4.prospects.tail.head.prospectForScheduling.prospect,
          CampaignId(id = scheduleCampaign2LinkedinProspects4.campaign.campaign_id),
          ChannelType.LinkedinChannel,
          Logger,
        )
        .returning(Success(List()))

      (campaignsMissingMergeTagService.checkMagicColumnWithErrors(_: TeamId, _: String, _: String, _: ProspectObject, _: ChannelType)(_: SRLogger))
        .expects(
          TeamId(id = teamId),
          subjectAndBody.subject,
          subjectAndBody.body,
          scheduleCampaign2LinkedinProspects4.prospects.tail.head.prospectForScheduling.prospect,
          ChannelType.LinkedinChannel,
          Logger,
        )
        .returning(Success(List()))

      (emailServiceCompanion.getInternalMergeTag(_: String, _: String, _: String, _: Option[String], _: Option[String], _: Option[Long], _: Option[Long], _: Seq[PreviousFollowUp], _: Long, _: Long, _: TeamId, _: Boolean, _: Option[String])(using _: SRLogger))
        .expects(*, *, *, *, *, *, *, *, *, *, *, *, *, *)
        .returning(Success(internalMergeTagValuesForProspect))
        .atLeastTwice()

//      (templateService.checkMissingMergeTags)
//        .expects(*, *, *, *, *, *)
//        .returning(Success(Seq()))
//        .atLeastTwice()

      val result = linkedinChannelScheduler.getChannelTasksToBeScheduled(
        channelDataForScheduling = linkedinChannelDataForScheduling,
        channelStepTypeDataForScheduling = channelStepTypeDataForSchedulingMap1,
        campaignListForScheduling = Seq(scheduleCampaign2LinkedinProspects4),
        scheduleFromTime = aDate,
        scheduleTillTime = aDate.plusHours(3),
        maxToBeScheduledForNextHour = 5,
        emailServiceCompanion = emailServiceCompanion,
        templateService = templateService,
        srShuffleUtils = srShuffleUtils,
        campaignProspectDAO = campaignProspectDAO,
        campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO,
        campaignsMissingMergeTagService = campaignsMissingMergeTagService,
        isCalendarEnabled = account.org.org_metadata.enable_calendar.getOrElse(false),
        calendarAccountData = account.calendar_account_data,
        campaignProspectService = campaignProspectService,
        Logger = Logger
      )

      result match {
        case Success(v) =>
          assert(v.size == 2)

        case _ =>
          assert(false)
      }

      assert(result == Success(Vector(
        generateScheduleTaskDataLinkedin.copy(
          currentCampaign = scheduleCampaign2LinkedinProspects4,
          schedulerDateTime = aDate.plusMinutes(1),
        ),
        generateScheduleTaskDataLinkedin.copy(
          currentCampaign = scheduleCampaign2LinkedinProspects4,
          currentProspect = prospectFoundForSchedulingByStepTypeLinkedin.copy(
            prospectForScheduling = prospectForSchedulingLinkedin.copy(prospect = prospectObject.copy(id = 5))
          ),
          schedulerDateTime = aDate.plusMinutes(2)
        ),
      )))

    }

    it ("should schedule only allowed number of tasks within limit") {
      val step_data2 = LinkedinConnectionRequestData(
        body = Some("Linkedin Connection Request Body 2")
      )

      val step_data5 = LinkedinMessageData(
        body = "Linkedin Message Body 5"
      )

      val campaignStepWithChildren2 = campaignStepWithChildrenLinkedin.copy(
        id = 2L,
        campaign_id = 2,
        variants = Seq(campaignStepVariantForSchedulingLinkedin.copy(step_data = step_data2)))

      val campaignStepWithChildren5 = campaignStepWithChildrenLinkedin.copy(
        id = 5L,
        campaign_id = 2,
        variants = Seq(campaignStepVariantForSchedulingLinkedin.copy(step_data = step_data5)))

      val campaignListForScheduling = Seq(scheduleCampaignLinkedin,
        scheduleCampaignLinkedin.copy(
          campaign = campaignForSchedulingLinkedin.copy(campaign_id = 2L),
          stepsMappedById = Map(2L -> campaignStepWithChildren2, 5L -> campaignStepWithChildren5),
          campaignStepsStructure = CampaignStepsStructure.MultichannelCampaignStepsStructure(
            orderedStepIds = Vector(2, 5)
          ),
        )
      )

      val channelStepTypeDataForSchedulingMap1 = Map(CampaignStepType.LinkedinInmail.asInstanceOf[CampaignStepType] ->
        channelStepTypeDataForSchedulingLinkedin.copy(
          remainingToBeScheduledFromChannelStepType = 1
        ))

      (srShuffleUtils.shuffleList[linkedinChannelScheduler.ScheduleCampaign])
        .expects(campaignListForScheduling)
        .returning(campaignListForScheduling)

      val subjectAndBody = getSubjectAndBody(
        campaignStepsStructure = campaignListForScheduling.head.campaignStepsStructure,
        stepsMappedById = campaignListForScheduling.head.stepsMappedById,
      )

      (campaignsMissingMergeTagService.checkPendingMagicColumn(_: TeamId, _: String, _: String, _: ProspectObject, _: CampaignId, _: ChannelType)(_: SRLogger))
        .expects(
          TeamId(id = teamId),
          subjectAndBody.subject,
          subjectAndBody.body,
          campaignListForScheduling.head.prospects.head.prospectForScheduling.prospect,
          CampaignId(id = campaignListForScheduling.head.campaign.campaign_id),
          ChannelType.LinkedinChannel,
          Logger,
        )
        .returning(Success(List()))

      (campaignsMissingMergeTagService.checkMagicColumnWithErrors(_: TeamId, _: String, _: String, _: ProspectObject, _: ChannelType)(_: SRLogger))
        .expects(
          TeamId(id = teamId),
          subjectAndBody.subject,
          subjectAndBody.body,
          campaignListForScheduling.head.prospects.head.prospectForScheduling.prospect,
          ChannelType.LinkedinChannel,
          Logger,
        )
        .returning(Success(List()))


      (emailServiceCompanion.getInternalMergeTag(_: String, _: String, _: String, _: Option[String], _: Option[String], _: Option[Long], _: Option[Long], _: Seq[PreviousFollowUp], _: Long, _: Long, _: TeamId, _: Boolean, _: Option[String])(using _: SRLogger))
        .expects(*, *, *, *, *, *, *, *, *, *, *, *, *, *)
        .returning(Success(internalMergeTagValuesForProspect))

//      (templateService.checkMissingMergeTags)
//        .expects(*, *, *, *, *, *)
//        .returning(Success(Seq()))

      val result = linkedinChannelScheduler.getChannelTasksToBeScheduled(
        channelDataForScheduling = linkedinChannelDataForScheduling,
        channelStepTypeDataForScheduling = channelStepTypeDataForSchedulingMap1,
        campaignListForScheduling = campaignListForScheduling,
        scheduleFromTime = aDate,
        scheduleTillTime = aDate.plusHours(3),
        maxToBeScheduledForNextHour = 5,
        emailServiceCompanion = emailServiceCompanion,
        templateService = templateService,
        srShuffleUtils = srShuffleUtils,
        campaignProspectDAO = campaignProspectDAO,
        campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO,
        campaignsMissingMergeTagService = campaignsMissingMergeTagService,
        isCalendarEnabled = account.org.org_metadata.enable_calendar.getOrElse(false),
        calendarAccountData = account.calendar_account_data,
        campaignProspectService = campaignProspectService,
        Logger = Logger
      )

      assert(result == Success(Vector(generateScheduleTaskDataLinkedin)))

    }

    it("should successfully return Vector of GenerateScheduleTaskData when step types are different") {

      val step_data2 = LinkedinConnectionRequestData(
        body = Some("Linkedin Connection Request Body 2")
      )

      val step_data5 = LinkedinMessageData(
        body = "Linkedin Message Body 5"
      )

      val campaignStepWithChildren2 = campaignStepWithChildrenLinkedin.copy(
        id = 2L,
        campaign_id = 2,
        variants = Seq(campaignStepVariantForSchedulingLinkedin.copy(step_data = step_data2)))

      val campaignStepWithChildren5 = campaignStepWithChildrenLinkedin.copy(
        id = 5L,
        campaign_id = 2,
        variants = Seq(campaignStepVariantForSchedulingLinkedin.copy(step_data = step_data5)))

      val campaignListForScheduling = Seq(scheduleCampaignLinkedin,
        scheduleCampaignLinkedin.copy(
          campaign = campaignForSchedulingLinkedin.copy(campaign_id = 2L),
          stepsMappedById = Map(2L -> campaignStepWithChildren2, 5L -> campaignStepWithChildren5),
          campaignStepsStructure = CampaignStepsStructure.MultichannelCampaignStepsStructure(
            orderedStepIds = Vector(2, 5)
          ),
        )
      )

      val campaignLI = scheduleCampaignLinkedin.copy(
        campaign = campaignForSchedulingLinkedin.copy(campaign_id = 2L),
        stepsMappedById = Map(2L -> campaignStepWithChildren2, 5L -> campaignStepWithChildren5),
        campaignStepsStructure = CampaignStepsStructure.MultichannelCampaignStepsStructure(
          orderedStepIds = Vector(2, 5)
        ),
      )

      (srShuffleUtils.shuffleList[linkedinChannelScheduler.ScheduleCampaign])
        .expects(*)
        .returning(Seq(
          campaignLI,
          scheduleCampaignLinkedin
        ))

      val subjectAndBody = getSubjectAndBody(
        stepsMappedById = campaignLI.stepsMappedById,
        campaignStepsStructure = campaignLI.campaignStepsStructure,
      )

      (campaignsMissingMergeTagService.checkPendingMagicColumn(_: TeamId, _: String, _: String, _: ProspectObject, _: CampaignId, _: ChannelType)(_: SRLogger))
        .expects(
          TeamId(id = teamId),
          subjectAndBody.subject,
          subjectAndBody.body,
          campaignLI.prospects.head.prospectForScheduling.prospect,
          CampaignId(id = campaignLI.campaign.campaign_id),
          ChannelType.LinkedinChannel,
          Logger,
        )
        .returning(Success(List()))

      (campaignsMissingMergeTagService.checkMagicColumnWithErrors(_: TeamId, _: String, _: String, _: ProspectObject, _: ChannelType)(_: SRLogger))
        .expects(
          TeamId(id = teamId),
          subjectAndBody.subject,
          subjectAndBody.body,
          campaignLI.prospects.head.prospectForScheduling.prospect,
          ChannelType.LinkedinChannel,
          Logger,
        )
        .returning(Success(List()))

      val subjectAndBody2 = getSubjectAndBody(
        stepsMappedById = scheduleCampaignLinkedin.stepsMappedById,
        campaignStepsStructure = scheduleCampaignLinkedin.campaignStepsStructure,
      )

      (campaignsMissingMergeTagService.checkPendingMagicColumn(_: TeamId, _: String, _: String, _: ProspectObject, _: CampaignId, _: ChannelType)(_: SRLogger))
        .expects(
          TeamId(id = teamId),
          subjectAndBody2.subject,
          subjectAndBody2.body,
          scheduleCampaignLinkedin.prospects.head.prospectForScheduling.prospect,
          CampaignId(id = scheduleCampaignLinkedin.campaign.campaign_id),
          ChannelType.LinkedinChannel,
          Logger,
        )
        .returning(Success(List()))

      (campaignsMissingMergeTagService.checkMagicColumnWithErrors(_: TeamId, _: String, _: String, _: ProspectObject, _: ChannelType)(_: SRLogger))
        .expects(
          TeamId(id = teamId),
          subjectAndBody2.subject,
          subjectAndBody2.body,
          scheduleCampaignLinkedin.prospects.head.prospectForScheduling.prospect,
          ChannelType.LinkedinChannel,
          Logger,
        )
        .returning(Success(List()))

      (emailServiceCompanion.getInternalMergeTag(_: String, _: String, _: String, _: Option[String], _: Option[String], _: Option[Long], _: Option[Long], _: Seq[PreviousFollowUp], _: Long, _: Long, _: TeamId, _: Boolean, _: Option[String])(using _: SRLogger))
        .expects(*, *, *, *, *, *, *, *, *, *, *, *, *, *)
        .returning(Success(internalMergeTagValuesForProspect))
        .atLeastTwice()

//      (templateService.checkMissingMergeTags)
//        .expects(*, *, *, *, *, *)
//        .returning(Success(Seq()))
//        .atLeastTwice()

      val result = linkedinChannelScheduler.getChannelTasksToBeScheduled(
        channelDataForScheduling = linkedinChannelDataForScheduling,
        channelStepTypeDataForScheduling = channelStepTypeDataForSchedulingMapLinkedin,
        campaignListForScheduling = campaignListForScheduling,
        scheduleFromTime = aDate,
        scheduleTillTime = aDate.plusHours(3),
        maxToBeScheduledForNextHour = 5,
        emailServiceCompanion = emailServiceCompanion,
        templateService = templateService,
        srShuffleUtils = srShuffleUtils,
        campaignProspectDAO = campaignProspectDAO,
        campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO,
        campaignsMissingMergeTagService = campaignsMissingMergeTagService,
        isCalendarEnabled = account.org.org_metadata.enable_calendar.getOrElse(false),
        calendarAccountData = account.calendar_account_data,
        campaignProspectService = campaignProspectService,
        Logger = Logger
      )

      assert(result == Success(Vector(
        generateScheduleTaskDataLinkedin.copy(
          currentCampaign = scheduleCampaignLinkedin.copy(
            campaign = campaignForSchedulingLinkedin.copy(campaign_id = 2L),
            stepsMappedById = Map(2L -> campaignStepWithChildren2, 5L -> campaignStepWithChildren5),
            campaignStepsStructure = CampaignStepsStructure.MultichannelCampaignStepsStructure(
              orderedStepIds = Vector(2, 5)
            ),
          ),
          currentVariant = campaignStepVariantForSchedulingLinkedin.copy(
            step_data = step_data2
          ),
          nextStep = campaignStepWithChildrenLinkedin.copy(
            id = 2L,
            campaign_id = 2,
            variants = Seq(campaignStepVariantForSchedulingLinkedin.copy(step_data = step_data2))
          ),
          schedulerDateTime = aDate.plusSeconds(60),
        ),
        generateScheduleTaskDataLinkedin.copy(
          schedulerDateTime = aDate.plusSeconds(120),
        )
      )))

    }

    it("should all prospects in campaigns within limits (maxToBeScheduledForNextHour)") {

      val scheduleCampaign5Prospects5 = scheduleCampaignLinkedin.copy(
        campaign = campaignForSchedulingLinkedin.copy(campaign_id = 5),
        prospects = flattenedProspectsLinkedin5
      )

      val campaignListForScheduling = Seq(scheduleCampaign2LinkedinProspects4, scheduleCampaign5Prospects5)

      (srShuffleUtils.shuffleList[linkedinChannelScheduler.ScheduleCampaign])
        .expects(*)
        .returning(Seq(
          scheduleCampaign2LinkedinProspects4,
          scheduleCampaign5Prospects5
        ))

      val subjectAndBody = getSubjectAndBody(
        stepsMappedById = scheduleCampaign2LinkedinProspects4.stepsMappedById,
        campaignStepsStructure = scheduleCampaign2LinkedinProspects4.campaignStepsStructure,
      )

      val subjectAndBody2 = getSubjectAndBody(
        stepsMappedById = scheduleCampaign5Prospects5.stepsMappedById,
        campaignStepsStructure = scheduleCampaign5Prospects5.campaignStepsStructure,
      )


      (campaignsMissingMergeTagService.checkPendingMagicColumn(_: TeamId, _: String, _: String, _: ProspectObject, _: CampaignId, _: ChannelType)(_: SRLogger))
        .expects(
          TeamId(id = teamId),
          subjectAndBody.subject,
          subjectAndBody.body,
          scheduleCampaign2LinkedinProspects4.prospects.find(_.prospectForScheduling.prospect.id == 5).get.prospectForScheduling.prospect,
          CampaignId(id = scheduleCampaign2LinkedinProspects4.campaign.campaign_id),
          ChannelType.LinkedinChannel,
          Logger,
        )
        .returning(Success(List()))

      (campaignsMissingMergeTagService.checkMagicColumnWithErrors(_: TeamId, _: String, _: String, _: ProspectObject, _: ChannelType)(_: SRLogger))
        .expects(
          TeamId(id = teamId),
          subjectAndBody.subject,
          subjectAndBody.body,
          scheduleCampaign2LinkedinProspects4.prospects.find(_.prospectForScheduling.prospect.id == 5).get.prospectForScheduling.prospect,
          ChannelType.LinkedinChannel,
          Logger,
        )
        .returning(Success(List()))

      (campaignsMissingMergeTagService.checkPendingMagicColumn(_: TeamId, _: String, _: String, _: ProspectObject, _: CampaignId, _: ChannelType)(_: SRLogger))
        .expects(
          TeamId(id = teamId),
          subjectAndBody2.subject,
          subjectAndBody2.body,
          scheduleCampaign5Prospects5.prospects.find(_.prospectForScheduling.prospect.id == 5).get.prospectForScheduling.prospect,
          CampaignId(id = scheduleCampaign5Prospects5.campaign.campaign_id),
          ChannelType.LinkedinChannel,
          Logger,
        )
        .returning(Success(List()))

      (campaignsMissingMergeTagService.checkMagicColumnWithErrors(_: TeamId, _: String, _: String, _: ProspectObject, _: ChannelType)(_: SRLogger))
        .expects(
          TeamId(id = teamId),
          subjectAndBody2.subject,
          subjectAndBody2.body,
          scheduleCampaign5Prospects5.prospects.find(_.prospectForScheduling.prospect.id == 5).get.prospectForScheduling.prospect,
          ChannelType.LinkedinChannel,
          Logger,
        )
        .returning(Success(List()))

      (campaignsMissingMergeTagService.checkPendingMagicColumn(_: TeamId, _: String, _: String, _: ProspectObject, _: CampaignId, _: ChannelType)(_: SRLogger))
        .expects(
          TeamId(id = teamId),
          subjectAndBody.subject,
          subjectAndBody.body,
          scheduleCampaign2LinkedinProspects4.prospects.find(_.prospectForScheduling.prospect.id == 11).get.prospectForScheduling.prospect,
          CampaignId(id = scheduleCampaign2LinkedinProspects4.campaign.campaign_id),
          ChannelType.LinkedinChannel,
          Logger,
        )
        .returning(Success(List()))

      (campaignsMissingMergeTagService.checkMagicColumnWithErrors(_: TeamId, _: String, _: String, _: ProspectObject, _: ChannelType)(_: SRLogger))
        .expects(
          TeamId(id = teamId),
          subjectAndBody.subject,
          subjectAndBody.body,
          scheduleCampaign2LinkedinProspects4.prospects.find(_.prospectForScheduling.prospect.id == 11).get.prospectForScheduling.prospect,
          ChannelType.LinkedinChannel,
          Logger,
        )
        .returning(Success(List()))

      (campaignsMissingMergeTagService.checkPendingMagicColumn(_: TeamId, _: String, _: String, _: ProspectObject, _: CampaignId, _: ChannelType)(_: SRLogger))
        .expects(
          TeamId(id = teamId),
          subjectAndBody.subject,
          subjectAndBody.body,
          scheduleCampaign2LinkedinProspects4.prospects.find(_.prospectForScheduling.prospect.id == 7).get.prospectForScheduling.prospect,
          CampaignId(id = scheduleCampaign2LinkedinProspects4.campaign.campaign_id),
          ChannelType.LinkedinChannel,
          Logger,
        )
        .returning(Success(List()))

      (campaignsMissingMergeTagService.checkMagicColumnWithErrors(_: TeamId, _: String, _: String, _: ProspectObject, _: ChannelType)(_: SRLogger))
        .expects(
          TeamId(id = teamId),
          subjectAndBody.subject,
          subjectAndBody.body,
          scheduleCampaign2LinkedinProspects4.prospects.find(_.prospectForScheduling.prospect.id == 7).get.prospectForScheduling.prospect,
          ChannelType.LinkedinChannel,
          Logger,
        )
        .returning(Success(List()))

      (campaignsMissingMergeTagService.checkPendingMagicColumn(_: TeamId, _: String, _: String, _: ProspectObject, _: CampaignId, _: ChannelType)(_: SRLogger))
        .expects(
          TeamId(id = teamId),
          subjectAndBody2.subject,
          subjectAndBody2.body,
          scheduleCampaign5Prospects5.prospects.find(_.prospectForScheduling.prospect.id == 7).get.prospectForScheduling.prospect,
          CampaignId(id = scheduleCampaign5Prospects5.campaign.campaign_id),
          ChannelType.LinkedinChannel,
          Logger,
        )
        .returning(Success(List()))

      (campaignsMissingMergeTagService.checkMagicColumnWithErrors(_: TeamId, _: String, _: String, _: ProspectObject, _: ChannelType)(_: SRLogger))
        .expects(
          TeamId(id = teamId),
          subjectAndBody2.subject,
          subjectAndBody2.body,
          scheduleCampaign5Prospects5.prospects.find(_.prospectForScheduling.prospect.id == 7).get.prospectForScheduling.prospect,
          ChannelType.LinkedinChannel,
          Logger,
        )
        .returning(Success(List()))

      (emailServiceCompanion.getInternalMergeTag(_: String, _: String, _: String, _: Option[String], _: Option[String], _: Option[Long], _: Option[Long], _: Seq[PreviousFollowUp], _: Long, _: Long, _: TeamId, _: Boolean, _: Option[String])(using _: SRLogger))
        .expects(*, *, *, *, *, *, *, *, *, *, *, *, *, *)
        .returning(Success(internalMergeTagValuesForProspect))
        .atLeastTwice()

//      (templateService.checkMissingMergeTags)
//        .expects(*, *, *, *, *, *)
//        .returning(Success(Seq()))
//        .atLeastTwice()

      val result = linkedinChannelScheduler.getChannelTasksToBeScheduled(
        channelDataForScheduling = linkedinChannelDataForScheduling,
        channelStepTypeDataForScheduling = channelStepTypeDataForSchedulingMapLinkedin,
        campaignListForScheduling = campaignListForScheduling,
        scheduleFromTime = aDate,
        scheduleTillTime = aDate.plusHours(3),
        maxToBeScheduledForNextHour = 5,
        emailServiceCompanion = emailServiceCompanion,
        templateService = templateService,
        srShuffleUtils = srShuffleUtils,
        campaignProspectDAO = campaignProspectDAO,
        campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO,
        campaignsMissingMergeTagService = campaignsMissingMergeTagService,
        isCalendarEnabled = account.org.org_metadata.enable_calendar.getOrElse(false),
        calendarAccountData = account.calendar_account_data,
        campaignProspectService = campaignProspectService,
        Logger = Logger
      )


      assert(result == Success(Vector(
        generateScheduleTaskDataLinkedin.copy(
          currentCampaign = scheduleCampaign2LinkedinProspects4,
          schedulerDateTime = aDate.plusMinutes(1),
        ),
        generateScheduleTaskDataLinkedin.copy(
          currentCampaign = scheduleCampaign5Prospects5,
          schedulerDateTime = aDate.plusMinutes(2),
        ),
        generateScheduleTaskDataLinkedin.copy(
          currentCampaign = scheduleCampaign2LinkedinProspects4,
          currentProspect = prospectFoundForSchedulingByStepTypeLinkedin.copy(
            prospectForScheduling = prospectForSchedulingLinkedin.copy(prospect = prospectObject.copy(id = 5))
          ),
          schedulerDateTime = aDate.plusMinutes(3)
        ),
        generateScheduleTaskDataLinkedin.copy(
          currentCampaign = scheduleCampaign5Prospects5,
          currentProspect = prospectFoundForSchedulingByStepTypeLinkedin.copy(
            prospectForScheduling = prospectForSchedulingLinkedin.copy(prospect = prospectObject.copy(id = 5))
          ),
          schedulerDateTime = aDate.plusMinutes(4),
        ),
        generateScheduleTaskDataLinkedin.copy(
          currentCampaign = scheduleCampaign2LinkedinProspects4,
          currentProspect = prospectFoundForSchedulingByStepTypeLinkedin.copy(
            prospectForScheduling = prospectForSchedulingLinkedin.copy(prospect = prospectObject.copy(id = 11))
          ),
          schedulerDateTime = aDate.plusMinutes(5)
        )
      )))

    }

    it("should all prospects in campaigns in alternation") {

      val scheduleCampaign5Prospects5 = scheduleCampaignLinkedin.copy(
        campaign = campaignForSchedulingLinkedin.copy(campaign_id = 5),
        prospects = flattenedProspectsLinkedin5
      )

      val campaignListForScheduling = Seq(scheduleCampaign2LinkedinProspects4, scheduleCampaign5Prospects5)

      (srShuffleUtils.shuffleList[linkedinChannelScheduler.ScheduleCampaign])
        .expects(*)
        .returning(Seq(
          scheduleCampaign2LinkedinProspects4,
          scheduleCampaign5Prospects5
        ))

      val subjectAndBody = getSubjectAndBody(
        stepsMappedById = scheduleCampaign2LinkedinProspects4.stepsMappedById,
        campaignStepsStructure = scheduleCampaign2LinkedinProspects4.campaignStepsStructure,
      )

      val subjectAndBody2 = getSubjectAndBody(
        stepsMappedById = scheduleCampaign5Prospects5.stepsMappedById,
        campaignStepsStructure = scheduleCampaign5Prospects5.campaignStepsStructure,
      )

      (campaignsMissingMergeTagService.checkPendingMagicColumn(_: TeamId, _: String, _: String, _: ProspectObject, _: CampaignId, _: ChannelType)(_: SRLogger))
        .expects(
          TeamId(id = teamId),
          subjectAndBody.subject,
          subjectAndBody.body,
          scheduleCampaign2LinkedinProspects4.prospects.find(_.prospectForScheduling.prospect.id == 5).get.prospectForScheduling.prospect,
          CampaignId(id = scheduleCampaign2LinkedinProspects4.campaign.campaign_id),
          ChannelType.LinkedinChannel,
          Logger,
        )
        .returning(Success(List()))

      (campaignsMissingMergeTagService.checkMagicColumnWithErrors(_: TeamId, _: String, _: String, _: ProspectObject, _: ChannelType)(_: SRLogger))
        .expects(
          TeamId(id = teamId),
          subjectAndBody.subject,
          subjectAndBody.body,
          scheduleCampaign2LinkedinProspects4.prospects.find(_.prospectForScheduling.prospect.id == 5).get.prospectForScheduling.prospect,
          ChannelType.LinkedinChannel,
          Logger,
        )
        .returning(Success(List()))

      (campaignsMissingMergeTagService.checkPendingMagicColumn(_: TeamId, _: String, _: String, _: ProspectObject, _: CampaignId, _: ChannelType)(_: SRLogger))
        .expects(
          TeamId(id = teamId),
          subjectAndBody2.subject,
          subjectAndBody2.body,
          scheduleCampaign5Prospects5.prospects.find(_.prospectForScheduling.prospect.id == 5).get.prospectForScheduling.prospect,
          CampaignId(id = scheduleCampaign5Prospects5.campaign.campaign_id),
          ChannelType.LinkedinChannel,
          Logger,
        )
        .returning(Success(List()))

      (campaignsMissingMergeTagService.checkMagicColumnWithErrors(_: TeamId, _: String, _: String, _: ProspectObject, _: ChannelType)(_: SRLogger))
        .expects(
          TeamId(id = teamId),
          subjectAndBody2.subject,
          subjectAndBody2.body,
          scheduleCampaign5Prospects5.prospects.find(_.prospectForScheduling.prospect.id == 5).get.prospectForScheduling.prospect,
          ChannelType.LinkedinChannel,
          Logger,
        )
        .returning(Success(List()))

      (campaignsMissingMergeTagService.checkPendingMagicColumn(_: TeamId, _: String, _: String, _: ProspectObject, _: CampaignId, _: ChannelType)(_: SRLogger))
        .expects(
          TeamId(id = teamId),
          subjectAndBody.subject,
          subjectAndBody.body,
          scheduleCampaign2LinkedinProspects4.prospects.find(_.prospectForScheduling.prospect.id == 11).get.prospectForScheduling.prospect,
          CampaignId(id = scheduleCampaign2LinkedinProspects4.campaign.campaign_id),
          ChannelType.LinkedinChannel,
          Logger,
        )
        .returning(Success(List()))

      (campaignsMissingMergeTagService.checkMagicColumnWithErrors(_: TeamId, _: String, _: String, _: ProspectObject, _: ChannelType)(_: SRLogger))
        .expects(
          TeamId(id = teamId),
          subjectAndBody.subject,
          subjectAndBody.body,
          scheduleCampaign2LinkedinProspects4.prospects.find(_.prospectForScheduling.prospect.id == 11).get.prospectForScheduling.prospect,
          ChannelType.LinkedinChannel,
          Logger,
        )
        .returning(Success(List()))

      (campaignsMissingMergeTagService.checkPendingMagicColumn(_: TeamId, _: String, _: String, _: ProspectObject, _: CampaignId, _: ChannelType)(_: SRLogger))
        .expects(
          TeamId(id = teamId),
          subjectAndBody2.subject,
          subjectAndBody2.body,
          scheduleCampaign5Prospects5.prospects.find(_.prospectForScheduling.prospect.id == 11).get.prospectForScheduling.prospect,
          CampaignId(id = scheduleCampaign5Prospects5.campaign.campaign_id),
          ChannelType.LinkedinChannel,
          Logger,
        )
        .returning(Success(List()))

      (campaignsMissingMergeTagService.checkMagicColumnWithErrors(_: TeamId, _: String, _: String, _: ProspectObject, _: ChannelType)(_: SRLogger))
        .expects(
          TeamId(id = teamId),
          subjectAndBody2.subject,
          subjectAndBody2.body,
          scheduleCampaign5Prospects5.prospects.find(_.prospectForScheduling.prospect.id == 11).get.prospectForScheduling.prospect,
          ChannelType.LinkedinChannel,
          Logger,
        )
        .returning(Success(List()))

      (campaignsMissingMergeTagService.checkPendingMagicColumn(_: TeamId, _: String, _: String, _: ProspectObject, _: CampaignId, _: ChannelType)(_: SRLogger))
        .expects(
          TeamId(id = teamId),
          subjectAndBody.subject,
          subjectAndBody.body,
          scheduleCampaign2LinkedinProspects4.prospects.find(_.prospectForScheduling.prospect.id == 17).get.prospectForScheduling.prospect,
          CampaignId(id = scheduleCampaign2LinkedinProspects4.campaign.campaign_id),
          ChannelType.LinkedinChannel,
          Logger,
        )
        .returning(Success(List()))

      (campaignsMissingMergeTagService.checkMagicColumnWithErrors(_: TeamId, _: String, _: String, _: ProspectObject, _: ChannelType)(_: SRLogger))
        .expects(
          TeamId(id = teamId),
          subjectAndBody.subject,
          subjectAndBody.body,
          scheduleCampaign2LinkedinProspects4.prospects.find(_.prospectForScheduling.prospect.id == 17).get.prospectForScheduling.prospect,
          ChannelType.LinkedinChannel,
          Logger,
        )
        .returning(Success(List()))

      (campaignsMissingMergeTagService.checkPendingMagicColumn(_: TeamId, _: String, _: String, _: ProspectObject, _: CampaignId, _: ChannelType)(_: SRLogger))
        .expects(
          TeamId(id = teamId),
          subjectAndBody2.subject,
          subjectAndBody2.body,
          scheduleCampaign5Prospects5.prospects.find(_.prospectForScheduling.prospect.id == 17).get.prospectForScheduling.prospect,
          CampaignId(id = scheduleCampaign5Prospects5.campaign.campaign_id),
          ChannelType.LinkedinChannel,
          Logger,
        )
        .returning(Success(List()))

      (campaignsMissingMergeTagService.checkMagicColumnWithErrors(_: TeamId, _: String, _: String, _: ProspectObject, _: ChannelType)(_: SRLogger))
        .expects(
          TeamId(id = teamId),
          subjectAndBody2.subject,
          subjectAndBody2.body,
          scheduleCampaign5Prospects5.prospects.find(_.prospectForScheduling.prospect.id == 17).get.prospectForScheduling.prospect,
          ChannelType.LinkedinChannel,
          Logger,
        )
        .returning(Success(List()))


      (campaignsMissingMergeTagService.checkPendingMagicColumn(_: TeamId, _: String, _: String, _: ProspectObject, _: CampaignId, _: ChannelType)(_: SRLogger))
        .expects(
          TeamId(id = teamId),
          subjectAndBody.subject,
          subjectAndBody.body,
          scheduleCampaign2LinkedinProspects4.prospects.find(_.prospectForScheduling.prospect.id == 7).get.prospectForScheduling.prospect,
          CampaignId(id = scheduleCampaign2LinkedinProspects4.campaign.campaign_id),
          ChannelType.LinkedinChannel,
          Logger,
        )
        .returning(Success(List()))

      (campaignsMissingMergeTagService.checkMagicColumnWithErrors(_: TeamId, _: String, _: String, _: ProspectObject, _: ChannelType)(_: SRLogger))
        .expects(
          TeamId(id = teamId),
          subjectAndBody.subject,
          subjectAndBody.body,
          scheduleCampaign2LinkedinProspects4.prospects.find(_.prospectForScheduling.prospect.id == 7).get.prospectForScheduling.prospect,
          ChannelType.LinkedinChannel,
          Logger,
        )
        .returning(Success(List()))


      (campaignsMissingMergeTagService.checkPendingMagicColumn(_: TeamId, _: String, _: String, _: ProspectObject, _: CampaignId, _: ChannelType)(_: SRLogger))
        .expects(
          TeamId(id = teamId),
          subjectAndBody2.subject,
          subjectAndBody2.body,
          scheduleCampaign5Prospects5.prospects.find(_.prospectForScheduling.prospect.id == 7).get.prospectForScheduling.prospect,
          CampaignId(id = scheduleCampaign5Prospects5.campaign.campaign_id),
          ChannelType.LinkedinChannel,
          Logger,
        )
        .returning(Success(List()))

      (campaignsMissingMergeTagService.checkMagicColumnWithErrors(_: TeamId, _: String, _: String, _: ProspectObject, _: ChannelType)(_: SRLogger))
        .expects(
          TeamId(id = teamId),
          subjectAndBody2.subject,
          subjectAndBody2.body,
          scheduleCampaign5Prospects5.prospects.find(_.prospectForScheduling.prospect.id == 7).get.prospectForScheduling.prospect,
          ChannelType.LinkedinChannel,
          Logger,
        )
        .returning(Success(List()))


      (campaignsMissingMergeTagService.checkPendingMagicColumn(_: TeamId, _: String, _: String, _: ProspectObject, _: CampaignId, _: ChannelType)(_: SRLogger))
        .expects(
          TeamId(id = teamId),
          subjectAndBody2.subject,
          subjectAndBody2.body,
          scheduleCampaign5Prospects5.prospects.find(_.prospectForScheduling.prospect.id == 23).get.prospectForScheduling.prospect,
          CampaignId(id = scheduleCampaign5Prospects5.campaign.campaign_id),
          ChannelType.LinkedinChannel,
          Logger,
        )
        .returning(Success(List()))

      (campaignsMissingMergeTagService.checkMagicColumnWithErrors(_: TeamId, _: String, _: String, _: ProspectObject, _: ChannelType)(_: SRLogger))
        .expects(
          TeamId(id = teamId),
          subjectAndBody2.subject,
          subjectAndBody2.body,
          scheduleCampaign5Prospects5.prospects.find(_.prospectForScheduling.prospect.id == 23).get.prospectForScheduling.prospect,
          ChannelType.LinkedinChannel,
          Logger,
        )
        .returning(Success(List()))

      (emailServiceCompanion.getInternalMergeTag(_: String, _: String, _: String, _: Option[String], _: Option[String], _: Option[Long], _: Option[Long], _: Seq[PreviousFollowUp], _: Long, _: Long, _: TeamId, _: Boolean, _: Option[String])(using _: SRLogger))
        .expects(*, *, *, *, *, *, *, *, *, *, *, *, *, *)
        .returning(Success(internalMergeTagValuesForProspect))
        .atLeastTwice()

//      (templateService.checkMissingMergeTags)
//        .expects(*, *, *, *, *, *)
//        .returning(Success(Seq()))
//        .atLeastTwice()

      val result = linkedinChannelScheduler.getChannelTasksToBeScheduled(
        channelDataForScheduling = linkedinChannelDataForScheduling,
        channelStepTypeDataForScheduling = channelStepTypeDataForSchedulingMapLinkedin,
        campaignListForScheduling = campaignListForScheduling,
        scheduleFromTime = aDate,
        scheduleTillTime = aDate.plusHours(3),
        maxToBeScheduledForNextHour = 10,
        emailServiceCompanion = emailServiceCompanion,
        templateService = templateService,
        srShuffleUtils = srShuffleUtils,
        campaignProspectDAO = campaignProspectDAO,
        campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO,
        campaignProspectService = campaignProspectService,
        campaignsMissingMergeTagService = campaignsMissingMergeTagService,
        isCalendarEnabled = account.org.org_metadata.enable_calendar.getOrElse(false),
        calendarAccountData = account.calendar_account_data,
        Logger = Logger
      )

      assert(result == Success(Vector(
        generateScheduleTaskDataLinkedin.copy(
          currentCampaign = scheduleCampaign2LinkedinProspects4,
          schedulerDateTime = aDate.plusMinutes(1),
        ),
        generateScheduleTaskDataLinkedin.copy(
          currentCampaign = scheduleCampaign5Prospects5,
          schedulerDateTime = aDate.plusMinutes(2),
        ),
        generateScheduleTaskDataLinkedin.copy(
          currentCampaign = scheduleCampaign2LinkedinProspects4,
          currentProspect = prospectFoundForSchedulingByStepTypeLinkedin.copy(
            prospectForScheduling = prospectForSchedulingLinkedin.copy(prospect = prospectObject.copy(id = 5))
          ),
          schedulerDateTime = aDate.plusMinutes(3)
        ),
        generateScheduleTaskDataLinkedin.copy(
          currentCampaign = scheduleCampaign5Prospects5,
          currentProspect = prospectFoundForSchedulingByStepTypeLinkedin.copy(
            prospectForScheduling = prospectForSchedulingLinkedin.copy(prospect = prospectObject.copy(id = 5))
          ),
          schedulerDateTime = aDate.plusMinutes(4),
        ),
        generateScheduleTaskDataLinkedin.copy(
          currentCampaign = scheduleCampaign2LinkedinProspects4,
          currentProspect = prospectFoundForSchedulingByStepTypeLinkedin.copy(
            prospectForScheduling = prospectForSchedulingLinkedin.copy(prospect = prospectObject.copy(id = 11))
          ),
          schedulerDateTime = aDate.plusMinutes(5)
        ),
        generateScheduleTaskDataLinkedin.copy(
          currentCampaign = scheduleCampaign5Prospects5,
          currentProspect = prospectFoundForSchedulingByStepTypeLinkedin.copy(
            prospectForScheduling = prospectForSchedulingLinkedin.copy(prospect = prospectObject.copy(id = 11))
          ),
          schedulerDateTime = aDate.plusMinutes(6),
        ),
        generateScheduleTaskDataLinkedin.copy(
          currentCampaign = scheduleCampaign2LinkedinProspects4,
          currentProspect = prospectFoundForSchedulingByStepTypeLinkedin.copy(
            prospectForScheduling = prospectForSchedulingLinkedin.copy(prospect = prospectObject.copy(id = 17))
          ),
          schedulerDateTime = aDate.plusMinutes(7)
        ),
        generateScheduleTaskDataLinkedin.copy(
          currentCampaign = scheduleCampaign5Prospects5,
          currentProspect = prospectFoundForSchedulingByStepTypeLinkedin.copy(
            prospectForScheduling = prospectForSchedulingLinkedin.copy(prospect = prospectObject.copy(id = 17))
          ),
          schedulerDateTime = aDate.plusMinutes(8),
        ),
        generateScheduleTaskDataLinkedin.copy(
          currentCampaign = scheduleCampaign5Prospects5,
          currentProspect = prospectFoundForSchedulingByStepTypeLinkedin.copy(
            prospectForScheduling = prospectForSchedulingLinkedin.copy(prospect = prospectObject.copy(id = 23))
          ),
          schedulerDateTime = aDate.plusMinutes(9),
        ),
      )))

    }

  }

//  describe("testing ChannelSchedulerTrait.getProspectThatCanBeScheduled") {
//
//    it("should return Some distinct prospect (which is not scheduled) for same campaign") {
//
//      val result = emailChannelScheduler.getProspectThatCanBeScheduled(
//        prospectIdsSeq = scala.collection.mutable.LinkedHashSet(23L),
//        prospectsWithMissingMergeTags = Set()
//      )
//
//      assert(result.contains(23))
//
//    }
//
//    it("should return Some distinct prospect (which does not have missing merge tag) for same campaign") {
//
//      val result = emailChannelScheduler.getProspectThatCanBeScheduled(
//        prospectIdsSeq = scala.collection.mutable.LinkedHashSet(17,19,23),
//        prospectsWithMissingMergeTags = Set(23, 17)
//      )
//
//      assert(result.contains(
//        19))
//
//      assert(result.get == 19)
//
//    }
//
//  }

  describe("testing ChannelSchedulerTrait.scheduleTasksForChannel") {

    it ("should fail because checkLock fails") {
      (srRedisSimpleLockServiceV2.checkLock(
        _: CacheIdKeyForLock
      )(
        using _: SRLogger
      ))
        .expects(*, *)
        .returning(Failure(error))

      emailChannelScheduler.scheduleTasksForChannel(
          channelData = ChannelData.EmailChannelData(emailSettingId = emailSettingId_1),
          teamId = teamId,
          accountService,
          //accountDAO,
          emailNotificationService,
          campaignService,
          campaignProspectDAO,
          campaignProspectService,
          campaignStepVariantDAO,
          campaignStepDAO,
          srShuffleUtils,
          emailServiceCompanion,
          templateService,
          taskDAO,
          taskService,
          campaignEditedPreviewEmailDAO,
          mqWebhookCompleted,
          srRedisSimpleLockServiceV2,
          campaignsMissingMergeTagService,
          accountOrgBillingRelatedService,
        srRollingUpdateCoreService,
          calendarAppService,
        )
        .map(res => assert(false))
        .recover {
          case e => assert(e == error)
        }
    }

    it ("should return 0 savedTasks if lock is present on channelId") {
      (srRedisSimpleLockServiceV2.checkLock(
        _: CacheIdKeyForLock
      )(
        using _: SRLogger
      ))
        .expects(*, *)
        .returning(Failure(error))

      emailChannelScheduler.scheduleTasksForChannel(
          channelData = ChannelData.EmailChannelData(emailSettingId = emailSettingId_1),
          teamId = teamId,
          accountService,
          //accountDAO,
          emailNotificationService,
          campaignService,
          campaignProspectDAO,
          campaignProspectService,
          campaignStepVariantDAO,
          campaignStepDAO,
          srShuffleUtils,
          emailServiceCompanion,
          templateService,
          taskDAO,
          taskService,
          campaignEditedPreviewEmailDAO,
          mqWebhookCompleted,
          srRedisSimpleLockServiceV2,
          campaignsMissingMergeTagService,
          accountOrgBillingRelatedService,
        srRollingUpdateCoreService,
          calendarAppService
        )
        .map(res => assert(res.saved_tasks_count == 0))
        .recover {
          case e => assert(e == error)
        }
    }

    it("should not schedule Manual Email because limit is reached") {

      (srRedisSimpleLockServiceV2.checkLock(
        _: CacheIdKeyForLock
      )(
        using _: SRLogger
      ))
        .expects(*, *)
        .returning(Success(false))

      (emailSettingDAO.findForScheduling)
        .expects(*, *)
        .returning(Some(emailSettingCreateEmailSchedule.copy(quota_per_day = 2)))

      (accountService.find(_: Long)(_: SRLogger))
        .expects(*, *)
        .returning(Success(account))

      (campaignProspectService.getAllDistinctTimezones(
        _: Long,
        _: Long,
        _: String
      )(
        using _: SRLogger
      ))
        .expects(3, 505, "IN", *)
        .returning(Success(Set("Asia/Kolkata")))


      (emailSchedulerJedisService.acquireLockAndAddToSetForCampaignScheduling(_: Set[CampaignId], _: Int)(using _: SRLogger))
        .expects(Set(CampaignId(3)), 60 * 30, *)
        .returning(Success(Set(CampaignId(3))))

      (emailSchedulerJedisService.releaseLockForCampaignScheduling(_: Set[CampaignId])(using _: SRLogger))
        .expects(Set(CampaignId(3)), *)
        .returning(Success(Map(true -> Set(CampaignId(3)))))
      (campaignService.findCampaignsForSchedulingEA)
        .expects(*, *, TeamId(teamId))
        .returning(Success(Seq(campaignForScheduling)))

      (campaignProspectDAO.getSentOrScheduledProspectsCountForEmail)
        .expects(*, *, *, *, TeamId(teamId))
        .returning(Success(Map(EmailSettingId(emailSettingId_1) -> 2)))

      (campaignStepDAO.getDistinctStepTypesInCampaigns)
        .expects(*)
        .returning(Map(campaignId.toLong -> List(CampaignStepType.ManualEmailStep)))

      (srRedisSimpleLockServiceV2.acquireLock(
        _: CacheIdKeyForLock,
        _: Int
      )(
        using _: SRLogger
      ))
        .expects(*, *, *)
        .returning(Success(true))

      emailChannelScheduler.scheduleTasksForChannel(
        channelData = ChannelData.EmailChannelData(emailSettingId = emailSettingId_1),
        teamId = teamId,
        accountService,
        //accountDAO,
        emailNotificationService,
        campaignService,
        campaignProspectDAO,
        campaignProspectService,
        campaignStepVariantDAO,
        campaignStepDAO,
        srShuffleUtils,
        emailServiceCompanion,
        templateService,
        taskDAO,
        taskService,
        campaignEditedPreviewEmailDAO,
        mqWebhookCompleted,
          srRedisSimpleLockServiceV2,
        campaignsMissingMergeTagService,
          accountOrgBillingRelatedService,
        srRollingUpdateCoreService,
        calendarAppService

        )
        .map(res => assert(res.saved_tasks_count == 0))

    }

    it("should successfully schedule Manual Email") {

      (orgMetadataService.getOrgMetadata(_: OrgId))
        .expects(OrgId(id = 1))
        .returning(Success(orgMetadata))

      (campaignProspectService.getCampaignsToLogDripFor()(using _:SRLogger))
        .expects(*)
        .returning(List())
      (srRedisSimpleLockServiceV2.checkLock(
        _: CacheIdKeyForLock
      )(
        using _: SRLogger
      ))
        .expects(*, *)
        .returning(Success(false))

      (emailSettingDAO.findForScheduling)
        .expects(*, *)
        .returning(Some(emailSettingCreateEmailSchedule))

      (() => srDateTimeUtils.getDateTimeNow())
        .expects()
        .returning(DateTime.now())
      (campaignProspectService.getAllDistinctTimezones(
        _: Long,
        _: Long,
        _: String
      )(
        using _: SRLogger
      ))
        .expects(3, 505, "UTC", *)
        .returning(Success(Set("Asia/Kolkata")))

      (accountService.find(_: Long)(_: SRLogger))
        .expects(*, *)
        .returning(Success(account))
      (emailSchedulerJedisService.acquireLockAndAddToSetForCampaignScheduling(_: Set[CampaignId], _: Int)(using _: SRLogger))
        .expects(Set(CampaignId(3)), 60 * 30, *)
        .returning(Success(Set(CampaignId(3))))
      (emailSchedulerJedisService.releaseLockForCampaignScheduling(_: Set[CampaignId])(using _: SRLogger))
        .expects(Set(CampaignId(3)), *)
        .returning(Success(Map(true -> Set(CampaignId(3)))))
      (campaignService.findCampaignsForSchedulingEA)
        .expects(*, *, TeamId(teamId))
        .returning(Success(Seq(campaignForSchedulingEmail.copy(campaign_type_data = CampaignTypeData.MultiChannelCampaignData(head_step_id = 3)))))

      (campaignProspectDAO.getSentOrScheduledProspectsCountForEmail)
        .expects(*, *, *, *, TeamId(teamId))
        .returning(Success(Map(EmailSettingId(campaignForSchedulingEmail.campaign_email_setting.sender_email_settings_id) -> 2)))

      (campaignStepDAO.getDistinctStepTypesInCampaigns)
        .expects(*)
        .returning(Map(campaignId.toLong -> List(CampaignStepType.ManualEmailStep)))

      (campaignProspectDAO.getScheduledProspectsCountForCampaign)
        .expects(*, *, *, *, *)
        .returning(Success(Seq(scheduledProspectsCountForCampaignEmail)))

      (campaignStepVariantDAO.findByCampaignIdForSchedule)
        .expects(*, *, *)
        .returning(Seq(campaignStepWithChildrenManual.copy( id = 3)))



//      (campaignStepDAO.getOrderedSteps)
//        .expects(*, *)
//        .returning(List(campaignStepWithChildrenManual))

      (campaignProspectDAO.findAndMarkCompletedProspects(
        _: List[Long],
        _: TeamId,
        _: Int
      )(using _: SRLogger))
        .expects(*, *, *, *)
        .returning(Success(Seq(cpCompleted)))

      (campaignProspectService.getAllDistinctTimezones(
        _: Long,
        _: Long,
        _: String
      )(
      using _: SRLogger
      ))
        .expects(*, *, *, *)
        .returning(Success(Set("Asia/Kolkata"))).twice()
      (srUuidUtils.generateEmailsScheduledUuid _)
        .expects()
        .returning("test-uuid-12345")
      (campaignProspectService.fetchProspectsV3MultichannelWithEmailOptionalCheck(
        _: ChannelType,
        _: List[String],
        _: Option[Long],
        _: Long,
        _: TeamId,
        _: Int,
        _: Vector[SchedulerMapStepIdAndDelay],
        _: Boolean, _: Boolean,
        _: Option[DateTime],
        _: Boolean,
        _: Option[CampaignEmailSettingForScheduler],
        _: OrgId, _:String)(using _: SRLogger))
        .expects(*, *, *, *, *, *, *, *, *, *, *, *, *, *, *)
        .returning(Success(List(prospectForScheduling)))

      (campaignProspectDAO.filterProspectWhoHaveHoliday(
        _: List[Long],
        _: String,
        _: Option[Long],
        _: DateTime,
        _: Long
      )(using _: SRLogger))
        .expects(*, *, *, *, *, *)
        .returning(Success(Set()))

      (campaignProspectDAO.filterByProspectValidationStatus(_: Set[Long], _: TeamId)(using _: SRLogger))
        .expects(*, *, *)
        .returning(Success(Set(prospectId)))

      (campaignProspectDAO.filterProspectsBySentCountBasedOnProspectLimit(
        _: Set[Long],
        _: String,
        _: TeamId,
        _: Int,
        _: Int
      )(using _: SRLogger))
        .expects(*, *, *, *, *, *)
        .returning(Success(Set(prospectId)))

      (orgMetadataService.filterProspectsBasedOnDomainLimit)
        .expects(*)
        .returning(Success(true))

      (campaignProspectDAO.mapProspectIdsWithProspectAccountIds)
        .expects(*, *)
        .returning(Success(Map(ProspectId(prospectId) -> Some(ProspectAccountsId(5L)))))

      (campaignProspectDAO.mapProspectIdsWithProspectAccountIds)
        .expects(*, *)
        .returning(Success(Map()))

      (campaignProspectDAO.getEmailsScheduledInLast24HoursAnd7DaysForAProspectAccount(
        _: List[ProspectAccountsId],
        _: String,
        _: TeamId
      ))
        .expects(*, *, *)
        .returning(Success(Map(ProspectAccountsId(5L) -> EmailsScheduledCount(3, 3))))

      (campaignProspectService.fetchProspectsV3MultichannelWithEmailOptionalCheck(
        _: ChannelType,
        _: List[String],
        _: Option[Long],
        _: Long,
        _: TeamId,
        _: Int,
        _: Vector[SchedulerMapStepIdAndDelay],
        _: Boolean, _: Boolean,
        _: Option[DateTime],
        _: Boolean,
        _: Option[CampaignEmailSettingForScheduler],
        _: OrgId, _:String)(using _: SRLogger))
        .expects(*, *, *, *, *, *, *, *, *, *, *, *, *, *, *)
        .returning(Success(List()))

      (campaignProspectDAO.filterProspectWhoHaveHoliday(
        _: List[Long],
        _: String,
        _: Option[Long],
        _: DateTime,
        _: Long
      )(using _: SRLogger))
        .expects(*, *, *, *, *, *)
        .returning(Success(Set()))

      (campaignProspectDAO.filterByProspectValidationStatus(_: Set[Long], _: TeamId)(using _: SRLogger))
        .expects(*, *, *)
        .returning(Success(Set()))

      (campaignProspectDAO.filterProspectsBySentCountBasedOnProspectLimit(
        _: Set[Long],
        _: String,
        _: TeamId,
        _: Int,
        _: Int
      )(using _: SRLogger))
        .expects(*, *, *, *, *, *)
        .returning(Success(Set()))

      (orgMetadataService.filterProspectsBasedOnDomainLimit)
        .expects(*)
        .returning(Success(true))

      (campaignProspectDAO.mapProspectIdsWithProspectAccountIds)
        .expects(*, *)
        .returning(Success(Map()))

      (campaignProspectDAO.mapProspectIdsWithProspectAccountIds)
        .expects(*, *)
        .returning(Success(Map()))

      (campaignProspectDAO.getEmailsScheduledInLast24HoursAnd7DaysForAProspectAccount(
        _: List[ProspectAccountsId],
        _: String,
        _: TeamId
      ))
        .expects(*, *, *)
        .returning(Success(Map()))

      (emailValidationService.sendProspectsForValidation)
        .expects(EmailValidationPriority.Medium, *, *, *, *, *, *)
        .returning(Success(1))

      (mqWebhookCompleted.publishCompletedProspects (_: Long, _: Long, _: Seq[CPCompleted])(using _: SRLogger) )
        .expects(*, *, *, *)
        .returning(Success(0))

      (srShuffleUtils.shuffleList[emailChannelScheduler.ScheduleCampaign])
        .expects(*)
        .returning(Seq(scheduleCampaignManual))

      val subjectAndBody = getSubjectAndBody(
        campaignStepsStructure = scheduleCampaignManual.campaignStepsStructure,
        stepsMappedById = scheduleCampaignManual.stepsMappedById,
      )

      (campaignsMissingMergeTagService.checkPendingMagicColumn(_: TeamId, _: String, _: String, _: ProspectObject, _: CampaignId, _: ChannelType)(_: SRLogger))
        .expects(
          TeamId(id = teamId),
          subjectAndBody.subject,
          subjectAndBody.body,
          scheduleCampaignManual.prospects.head.prospectForScheduling.prospect,
          CampaignId(id = scheduleCampaignManual.campaign.campaign_id),
          ChannelType.EmailChannel,
          Logger,
        )
        .returning(Success(List()))

      (campaignsMissingMergeTagService.checkMagicColumnWithErrors(_: TeamId, _: String, _: String, _: ProspectObject, _: ChannelType)(_: SRLogger))
        .expects(
          TeamId(id = teamId),
          subjectAndBody.subject,
          subjectAndBody.body,
          scheduleCampaignManual.prospects.head.prospectForScheduling.prospect,
          ChannelType.EmailChannel,
          Logger,
        )
        .returning(Success(List()))


//      (templateService.checkMissingMergeTags)
//        .expects(*, *, *, *, *, *)
//        .returning(Success(Seq()))

      (srRandomUtils.getRandomDelay)
        .expects(*, *)
        .returning(30)

      (accountOrgBillingRelatedService.checkAndUpdateProspectsContacted)
        .expects(ProspectId(prospectId), TeamId(teamId), ChannelType.EmailChannel, ProspectTouchedType.ManualTaskScheduled, false, Logger)
        .returning(Success(1))

      (taskService.createTask(
        _: NewTask,
        _: Long,
        _: Long
      )(
      _: ExecutionContext,
      _: SRLogger
      ))
        .expects(*, *, *, *, *)
        .returning(Future(Right("task_5239nadjsc")))


      (emailScheduledDAOService.saveEmailsToBeScheduledAndUpdateCampaignDataV2 (_: Vector[EmailScheduledNew], _: CampaignEmailSettingsId, _: Option[EmailSendingFlow], _: SRLogger))
        .expects(*, //FIXME need to use capture here since this has dateTime in it that will change each time
          CampaignEmailSettingsId(123),
          None,
          *)
        .returning(Success(Seq(emailScheduledNewAfterSaving)))


      (emailScheduledDAOService.addBodyToEmailsToBeScheduled)
        .expects(*, *, *)
        .returning(Success(Seq(1L)))

      (() => repTrackingHostService.getRepTrackingHosts())
        .expects()
        .returning(Success(Seq(repTrackingHosts)))

      (campaignProspectService.getSenderRotationStats(_: CampaignId, _: TeamId, _: Int, _: DateTime)(using _: SRLogger))
        .expects(CampaignId(3), TeamId(505), 100, *, *)
        .returning(Success(SenderRotationStats(prospects_not_sent_any_emails = 101, prospects_to_get_follow_up = 0)))
      (emailServiceCompanion.getBodies(

        // to optimize the prospect preview api, we are passing this directly from there
        _: Option[CampaignEditedPreviewEmail],
        _: Long,
        _: Option[CalendarAccountData],
        _: Option[SelectedCalendarData],
         // TODO: check where this could be None
        _: Option[Long],
        _: Long,
        _: Option[Long],
        _: Option[Long],
        _: ProspectObject,
        _: EmailOptionsForGetBodies,
      )(
      _: SRLogger
      ))
        .expects(*, *, *, * , *, *, *, *, *, *, *)
        .returning(Success(emailServiceBody))

      (campaignProspectDAO._updateScheduledStatus(_: Seq[CampaignProspectUpdateScheduleStatus])(using _:SRLogger))
        .expects(*, *)
        .returning(Success(Seq(1L)))

      (emailServiceCompanion.getInternalMergeTag(_: String, _: String, _: String, _: Option[String], _: Option[String], _: Option[Long], _: Option[Long], _: Seq[PreviousFollowUp], _: Long, _: Long, _: TeamId, _: Boolean, _: Option[String])(using _: SRLogger))
        .expects(*, *, *, *, *, *, *, *, *, *, *, *, *, *)
        .returning(Success(internalMergeTagValuesForProspect))

//      (() => srDateTimeUtils.getDateTimeNow())
//        .expects()
//        .returning(DateTime.now())

//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(_: TeamId, _: SrRollingUpdateFeature)(_: ISRLogger))
//        .expects(TeamId(teamId), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .twice()
//        .returning(false)

      emailChannelScheduler.scheduleTasksForChannel(
        channelData = ChannelData.EmailChannelData(emailSettingId = senderEmailSettingId),
        teamId = teamId,
        accountService,
        //accountDAO,
        emailNotificationService,
        campaignService,
        campaignProspectDAO,
        campaignProspectService,
        campaignStepVariantDAO,
        campaignStepDAO,
        srShuffleUtils,
        emailServiceCompanion,
        templateService,
        taskDAO,
        taskService,
        campaignEditedPreviewEmailDAO,
        mqWebhookCompleted,
          srRedisSimpleLockServiceV2,
        campaignsMissingMergeTagService,
          accountOrgBillingRelatedService,
        srRollingUpdateCoreService,
        calendarAppService

        )
        .map(res => assert(res.saved_tasks_count == 1))

    }

  }


  val stepsMappedById_debug = Map(
    2L -> campaignStepWithChildren.copy(id = 2, children = List(3,5), delay = 100),
    3L -> campaignStepWithChildrenLinkedin.copy(id = 3, children = List(5), delay = 100),
    5L -> campaignStepWithChildren.copy(id = 5, children = List(), delay  = 100),
  )

  val result_expected = FetchCampaignStepsData(
    stepsMappedById = stepsMappedById_debug,
    campaignStepsStructure = CampaignStepsStructure.MultichannelCampaignStepsStructure(
      orderedStepIds = Vector(2,3,5)
    ),
    allCampaignSteps =  Vector(schedulerMapStepIdAndDelay.copy(currentStepId = 2,
      nextStepType = CampaignStepType.LinkedinInmail),
      schedulerMapStepIdAndDelay.copy(currentStepId = 3,
      nextStepType = CampaignStepType.AutoEmailStep,
        currentStepType = CampaignStepType.LinkedinInmail, is_head_step_in_the_campaign = false)
//      schedulerMapStepIdAndDelay.copy(currentStepId = 5,
//      nextStepType = CampaignStepType.AutoEmailStep, is_head_step_in_the_campaign = false)
    ),
    relevantCampaignStepsForChannel =  Vector(
      schedulerMapStepIdAndDelay.copy(currentStepId = 3,
        currentStepType = CampaignStepType.LinkedinInmail,
        nextStepType = CampaignStepType.AutoEmailStep,
        is_head_step_in_the_campaign = false
      ))
  )

  describe("testing channelSchedulertrait.fetchCampaignStepsData") {

//     Alternate step 1 email 2 linkedin 3 email
    it("should find all campaign steps ") {


      (campaignStepVariantDAO.findByCampaignIdForSchedule)
        .expects(campaignId, account.org.settings.enable_ab_testing, * )
        .returning(Seq(campaignStepWithChildren.copy(id = 2, children = List(3,5), delay = 100),
          campaignStepWithChildrenLinkedin.copy(id = 3,  children = List(5), delay = 100),
          campaignStepWithChildren.copy(id = 5, children = List(), delay = 100)))

//      (campaignStepDAO.getOrderedSteps)
//        .expects(Seq(campaignStepWithChildren), campaignForSchedulingEmail.head_step_id)
//        .returning(List(campaignStepWithChildren, campaignStepWithChildrenLinkedin, campaignStepWithChildren2))


      val res = emailChannelScheduler.fetchCampaignStepsData(
        c = campaignForSchedulingEmail.copy(campaign_type_data = CampaignTypeData.MultiChannelCampaignData(head_step_id = 2)),
        account = account,
        channelType =  ChannelType.EmailChannel,
        campaignStepVariantDAO = campaignStepVariantDAO,
        campaignStepDAO = campaignStepDAO
      )

      res match {

        case Failure(e) =>
          println(s"error -> ${e}")
          assert(false)

        case Success(data) =>
          println(s"\n\ndata -> ${data}\n\n")
          assert(data == result_expected)

      }



    }

    it("getOrderedSteps testing ") {

      val res2 = CampaignStepDAO.getOrderedSteps(
        steps = Seq(
          campaignStepWithChildren.copy(id = 5, children = List()),
          campaignStepWithChildren.copy(id = 2, children = List(3, 5)),
          campaignStepWithChildrenLinkedin.copy(id = 3, children = List(5))
        ),
        headStepId = 2
      )

      assert(res2 == List(
        campaignStepWithChildren.copy(id = 2, children = List(3, 5)),
        campaignStepWithChildrenLinkedin.copy(id = 3, children = List(5)),
        campaignStepWithChildren.copy(id = 5, children = List())

      ))

    }

  }

  describe("testing channelSchedulerTrait.isCampaignFirstStep") {

    it("should give is_channel_first step true because allCampaignSteps are empty") {

      (campaignStepVariantDAO.findByCampaignIdForSchedule)
        .expects(campaignId, account.org.settings.enable_ab_testing, *)
        .returning(Seq(campaignStepWithChildren.copy(id = 2, children = List(3, 5)),
          campaignStepWithChildrenLinkedin.copy(id = 3, children = List(5)),
          campaignStepWithChildren.copy(id = 5, children = List())))

      //      (campaignStepDAO.getOrderedSteps)
      //        .expects(Seq(campaignStepWithChildren), campaignForSchedulingEmail.head_step_id)
      //        .returning(List(campaignStepWithChildren, campaignStepWithChildrenLinkedin, campaignStepWithChildren2))


      val steps  = emailChannelScheduler.fetchCampaignStepsData(
        c = campaignForSchedulingEmail.copy(campaign_type_data = CampaignTypeData.MultiChannelCampaignData(head_step_id = 2)),
        account = account,
        channelType = ChannelType.EmailChannel,
        campaignStepVariantDAO = campaignStepVariantDAO,
        campaignStepDAO = campaignStepDAO
      )

      val res = emailChannelScheduler.isCampaignFirstStep(
        fetchCampaignStepsDataResult = steps.get,
        channelType = ChannelType.EmailChannel,
        schedulingForStepType = CampaignStepType.AutoEmailStep
      )

      assert(res == true)

    }


    it("nxd : should give is_channel_first step true because as we are scheduling for email channel ") {

      /*
      record - 1
id          | 234013
campaign_id | 87863
delay       | 86400
step_type   | send_email
created_at  | 2023-03-28 11:49:22.067508+00
label       | Day 1: Opening
template_id |
notes       |
priority    | normal
children    | [234014]
variants    | [
{"id" : 290451, "step_id" : 234013, "campaign_id" : 87863, "template_id" : null, "step_type" : "send_email", "subject" : "book more sales meetings", "body" : "Hi {{first_name}},<br /><br />{{opening_lines}}<br /><br />83% of businesses are unable to maximize their reve nues vis-a-vis the resources &amp; efforts they put into generating sales qualified prospects.<br /><br />Is your team struggling with this?   <strong>Let's connect.</strong><br /><br />SmartReach.io is a multichannel sales outreach tool to help you minimize lead wastage &amp; increas e your sales team's efficiency. <br /><br />You can run outreach via Email, Linkedin, WhatsApp, SMS &amp; Calls<br /><br />Cheers<br />Bala<br />Sales Specialist<br />14 Days Free Trial", "text_preview" : "", "scheduled_count" : 0, "step_label" : "Day 1: Opening", "step_delay" : 86400, "notes" : null, "priority" : "normal", "active" : true},
{"id" : 290450, "step_id" : 234013, "campaign_id" : 87863, "template_id" : null, "step_type" : "send_email", "subject" : "are your {{kpi}} increasing?", "body" : "Hi {{first_name}},<br /><br />{{opening_lines}}<br /><br />83% of businesses are unable to maximize their revenues vis-a-vis the resources &amp; efforts they put into generating sales qualified prospects.<br /><br />Is your team struggling with this?  <a href=\"https://calendly.com/smartreachio/fact-finding-discovery-call\" target=\"_blank\" rel=\"noopener\">Let's connect</a><br /><br />SmartReach.io is a multichannel sales outreach tool to help you minimize lead wastage &amp; increase your sales team's efficiency. <br /><br />You can run outreach via Email, Linkedin, WhatsApp, SMS &amp; Calls<br /><br />Cheers<br />Bala<br />Sales Specialist<br />14 Days Free Trial", "text_preview" : "", "scheduled_count" : 0, "step_label" : "Day 1: Opening", "step_delay" : 86400, "notes" : null, "priority" : "normal", "active" : true},


{"id" : 290452, "step_id" : 234013, "campaign_id" : 87863, "template_id" : null, "step_type" : "send_email", "subject" : "focus on {{kpi}}", "body" : "Hi {{first_name}},<br /><br />{{opening_lines}}<br /><br />83% of businesses are unable to maximize their revenues vis-a-vis the resources &amp; efforts they put into generating sales qualified prospects.<br /><br />Is your team struggling with this?  <a href=\"https://calendly.com/smartreachio/fact-finding-discovery-call\" target=\"_blank\" rel=\"noopener\">Let's connect</a><br /><br />SmartReach.io is a multichannel sales outreach tool to help you minimize lead wastage &amp; increase your sales team's efficiency. <br /><br />You can run outreach via Email, Linkedin, WhatsApp, SMS &amp; Calls<br /><br />Cheers<br />Bala<br />Sales Specialist<br />14 Days Free Trial", "text_preview" : "", "scheduled_count" : 0, "step_label" : "Day 1: Opening", "step_delay" : 86400, "notes" : null, "priority" : "normal", "active" : true},
{"id" : 290447, "step_id" : 234013, "campaign_id" : 87863, "template_id" : null, "step_type" : "send_email", "subject" : "achieving {{kpi}} a challenge?", "body" : "Hi {{first_name}},<br /><br />{{opening_lines}}<br /><br />83% of businesses are unable to maximize their revenues vis-a-vis the resources &amp; efforts they put into generating sales qualified prospects.<br /><br />Is your team struggling with this?  <a href=\"https://calendly.com/smartreachio/fact-finding-discovery-call\" target=\"_blank\" rel=\"noopener\">Let's connect</a><br /><br />SmartReach.io is a multichannel sales outreach tool to help you minimize lead wastage &amp; increase your sales team's efficiency. <br /><br />You can run outreach via Email, Linkedin, WhatsApp, SMS &amp; Calls<br /><br />Cheers<br />Bala<br />Sales Specialist<br />14 Days Free Trial", "text_preview" : "", "scheduled_count" : 0, "step_label" : "Day 1: Opening", "step_delay" : 86400, "notes" : null, "priority" : "normal", "active" : true},
{"id" : 290449, "step_id" : 234013, "campaign_id" : 87863, "template_id" : null, "step_type" : "send_email", "subject" : "building a pool of sales qualified leads ", "body" : "Hi {{first_name}},<br /><br />{{opening_lines}}<br /><br />83% of businesses are unable to maximize their revenues vis-a-vis the resources &amp; efforts they put into generating sales qualified prospects.<br /><br />Is your team struggling with this?  <a href=\"https://calendly.com/smartreachio/fact-finding-discovery-call\" target=\"_blank\" rel=\"noopener\">Let's connect</a><strong>.</strong><br /><br />SmartReach.io is a multichannel sales outreach tool to help you minimize lead wastage &amp; increase your sales team's efficiency. <br /><br />You can run outreach via Email, Linkedin, WhatsApp, SMS &amp; Calls<br /><br />Cheers<br />Bala<br />Sales Specialist<br />14 Days Free Trial", "text_preview" : "", "scheduled_count" : 0, "step_label" : "Day 1: Opening", "step_delay" : 86400, "notes" : null, "priority" : "normal", "active" : true}]

record - 2

id          | 234014
campaign_id | 87863
delay       | 86400
step_type   | linkedin_view_profile
created_at  | 2023-03-28 11:51:16.000199+00
label       | Day 2: Follow up 1
template_id |
notes       |
priority    | normal
children    | [234015]
variants    | [{"id" : 290448, "step_id" : 234014, "campaign_id" : 87863, "template_id" : null, "step_type" : "linkedin_view_profile", "subject" : "", "body" : "", "text_preview" : "", "scheduled_count" : 0, "step_label" : "Day 2: Follow up 1", "step_delay" : 86400, "notes" : "", "priority" : "normal", "active" : true}]

record - 3

id          | 234015
campaign_id | 87863
delay       | 86400
step_type   | send_linkedin_connection_request
created_at  | 2023-03-28 11:56:25.860268+00
label       | Day 3: Follow up 2
template_id |
notes       |
priority    | normal
children    | [234020]
variants    | [{"id" : 290453, "step_id" : 234015, "campaign_id" : 87863, "template_id" : null, "step_type" : "send_linkedin_connection_request", "subject" : "", "body" : "Hi {{first_name}}, I’m on a mission to grow my connections on LinkedIn, especially with sales leaders and professionals like you. So even though we’re practically strangers, I’d love to connect.\n", "text_preview" : "", "scheduled_count" : 0, "step_label" : "Day 3: Follow up 2", "step_delay" : 86400, "notes" : "", "priority" : "normal", "active" : true}]


record - 4

id          | 234020
campaign_id | 87863
delay       | 86400
step_type   | send_email
created_at  | 2023-03-28 12:14:22.156532+00
label       | Day 4: Follow up 3
template_id |
notes       |
priority    | normal
children    | [234146]
variants    | [{"id" : 290459, "step_id" : 234020, "campaign_id" : 87863, "template_id" : null, "step_type" : "send_email", "subject" : "{{previous_subject}}", "body" : "Hi {{first_name}}<br /><br />As a {{job_title}}, I’m sure the one thing that definitely matters is revenue. <br /><br />SmartReach helps businesses like yours generate qualified leads by simply increase your reply rates.<br /><br />In addition to outreach we provide a shared inbox for the outreach team. It's a crazy productivity booster.<br /><br /><a href=\"https://calendly.com/smartreachio/fact-finding-discovery-call\" target=\"_blank\" rel=\"noopener\">Lets connect</a>, if this interests you.<br /><br />Cheers<br />Bala<br />Sales Specialist<br />14 Days Free Trial", "text_preview" : "", "scheduled_count" : 0, "step_label" : "Day 4: Follow up 3", "step_delay" : 86400, "notes" : null, "priority" : "normal", "active" : true}]


record - 5

id          | 234146
campaign_id | 87863
delay       | 172800
step_type   | send_email
created_at  | 2023-03-28 14:11:18.924922+00
label       | Day 6: Follow up 4
template_id |
notes       |
priority    | normal
children    | [234702]
variants    | [{"id" : 290628, "step_id" : 234146, "campaign_id" : 87863, "template_id" : null, "step_type" : "send_email", "subject" : "{{previous_subject}}", "body" : "{{first_name}} I assume you are the key personnel responsible for customer acquisition from your sales channel. <br /><br /> <img src=\"https://sr-email-message-images.s3.amazonaws.com/12178_1680095583923_IowtvxOXoQlWX29b1mSGPvrRZF5g0jhL_cmpn_\" alt=\"\" width=\"329\" height=\"219\" /><br /><br />Don’t mean to bother if you’re not. Could you refer me to the right person? Thanks in advance!<br /><br />Regards<br />Bala", "text_preview" : "", "scheduled_count" : 0, "step_label" : "Day 6: Follow up 4", "step_delay" : 172800, "notes" : null, "priority" : "normal", "active" : true}]

record - 6

id          | 234702
campaign_id | 87863
delay       | 86400
step_type   | send_linkedin_message
created_at  | 2023-03-29 14:20:54.205749+00
label       | Day 7: Follow up 5
template_id |
notes       |
priority    | normal
children    | [234707]
variants    | [{"id" : 291376, "step_id" : 234702, "campaign_id" : 87863, "template_id" : null, "step_type" : "send_linkedin_message", "subject" : "", "body" : "Trust me, I know you might not have an easy day as the {{job_title}}, and your schedule might be as busy as it gets, but how about a quick 10 minute call next week? \n\nAgenda: Understand your current sales process\n\nLater, I shall connect with your team and walk them through the SmartReach.io features that address the current concerns. \n\nThis is my calendar (Message me for a slot not available on my calendar)\n\nhttps://calendly.com/smartreachio/fact-finding-discovery-call\n\nMy bad, SmartReach is a sales engagement software for multichannel outreach designed to increase sales via automation & team collaboration", "text_preview" : "", "scheduled_count" : 0, "step_label" : "Day 7: Follow up 5", "step_delay" : 86400, "notes" : "", "priority" : "normal", "active" : true}]


record - 7

id          | 234707
campaign_id | 87863
delay       | 172800
step_type   | send_email
created_at  | 2023-03-29 14:22:34.66118+00
label       | Day 9: Follow up 6
template_id |
notes       |
priority    | normal
children    | []
variants    | [{"id" : 291384, "step_id" : 234707, "campaign_id" : 87863, "template_id" : null, "step_type" : "send_email", "subject" : "{{previous_subject}}", "body" : "Bumping this up in you inbox<br /><br />{{first_name}}, give us a chance. Your team will be hitting their sales quotas in no time<br /><br />But let me know if:<br /><br />1. You’re not interested<br /><br />2. Switching your outreach platform is not a priority and we should connect later.<br /><br />3. Im targeting the wrong personnel<br /><br />4. Your dog threatens you every time you try to reply. Just joking!<br /><br />Regards<br />Bala", "text_preview" : "", "scheduled_count" : 0, "step_label" : "Day 9: Follow up 6", "step_delay" : 172800, "notes" : null, "priority" : "normal", "active" : true}]

      */


      val cmp_step_var_var1 = CampaignStepVariantForScheduling(
        id = 290451,
        step_id = 234013,
        campaign_id = 87863,
        template_id = null,
        step_data = AutoEmailStep(
          subject = "book more sales meetings",
          body = "Hi {{first_name}},<br /><br />{{opening_lines}}<br /><br />83% of businesses are unable to maximize their reve nues vis-a-vis the resources &amp; efforts they put into generating sales qualified prospects.<br /><br />Is your team struggling with this?   <strong>Let's connect.</strong><br /><br />SmartReach.io is a multichannel sales outreach tool to help you minimize lead wastage &amp; increas e your sales team's efficiency. <br /><br />You can run outreach via Email, Linkedin, WhatsApp, SMS &amp; Calls<br /><br />Cheers<br />Bala<br />Sales Specialist<br />14 Days Free Trial"
        ),
        label = None,
        step_label = Some("Day 1: Opening"),
        step_delay = 86400,
        active = true,
        notes = null,
        priority = Some(tasks.models.TaskPriority.Normal),
        scheduled_count = 0
      )

      val cmp_step_var_var2 = CampaignStepVariantForScheduling(
        id = 290450,
        step_id = 234013,
        campaign_id = 87863,
        template_id = null,
        step_data = AutoEmailStep(
          subject = "are your {{kpi}} increasing?",
          body = "Hi {{first_name}},<br /><br />{{opening_lines}}<br /><br />83% of businesses are unable to maximize their revenues vis-a-vis the resources &amp; efforts they put into generating sales qualified prospects.<br /><br />Is your team struggling with this?  <a href=\"https://calendly.com/smartreachio/fact-finding-discovery-call\" target=\"_blank\" rel=\"noopener\">Let's connect</a><br /><br />SmartReach.io is a multichannel sales outreach tool to help you minimize lead wastage &amp; increase your sales team's efficiency. <br /><br />You can run outreach via Email, Linkedin, WhatsApp, SMS &amp; Calls<br /><br />Cheers<br />Bala<br />Sales Specialist<br />14 Days Free Trial"
        ),
        label = None,
        step_label = Some("Day 1: Opening"),
        step_delay = 86400,
        active = true,
        notes = null,
        priority = Some(tasks.models.TaskPriority.Normal),
        scheduled_count = 0
      )

      val cmp_step_var_var3 = CampaignStepVariantForScheduling(
        id = 290452,
        step_id = 234013,
        campaign_id = 87863,
        template_id = null,
        step_data = AutoEmailStep(
          subject = "focus on {{kpi}}",
          body = "Hi {{first_name}},<br /><br />{{opening_lines}}<br /><br />83% of businesses are unable to maximize their revenues vis-a-vis the resources &amp; efforts they put into generating sales qualified prospects.<br /><br />Is your team struggling with this?  <a href=\"https://calendly.com/smartreachio/fact-finding-discovery-call\" target=\"_blank\" rel=\"noopener\">Let's connect</a><br /><br />SmartReach.io is a multichannel sales outreach tool to help you minimize lead wastage &amp; increase your sales team's efficiency. <br /><br />You can run outreach via Email, Linkedin, WhatsApp, SMS &amp; Calls<br /><br />Cheers<br />Bala<br />Sales Specialist<br />14 Days Free Trial"
        ),
        label = None,
        step_label = Some("Day 1: Opening"),
        step_delay = 86400,
        active = true,
        notes = null,
        priority = Some(tasks.models.TaskPriority.Normal),
        scheduled_count = 0
      )


      val cmp_step_var_var4 = CampaignStepVariantForScheduling(
        id = 290447,
        step_id = 234013,
        campaign_id = 87863,
        template_id = null,
        step_data = AutoEmailStep(
          subject = "achieving {{kpi}} a challenge?",
          body = "Hi {{first_name}},<br /><br />{{opening_lines}}<br /><br />83% of businesses are unable to maximize their revenues vis-a-vis the resources &amp; efforts they put into generating sales qualified prospects.<br /><br />Is your team struggling with this?  <a href=\"https://calendly.com/smartreachio/fact-finding-discovery-call\" target=\"_blank\" rel=\"noopener\">Let's connect</a><br /><br />SmartReach.io is a multichannel sales outreach tool to help you minimize lead wastage &amp; increase your sales team's efficiency. <br /><br />You can run outreach via Email, Linkedin, WhatsApp, SMS &amp; Calls<br /><br />Cheers<br />Bala<br />Sales Specialist<br />14 Days Free Trial"
        ),
        label = None,
        step_label = Some("Day 1: Opening"),
        step_delay = 86400,
        active = true,
        notes = null,
        priority = Some(tasks.models.TaskPriority.Normal),
        scheduled_count = 0
      )

      val cmp_step_var_var5 = CampaignStepVariantForScheduling(
        id = 290449,
        step_id = 234013,
        campaign_id = 87863,
        template_id = null,
        step_data = AutoEmailStep(
          subject = "building a pool of sales qualified leads",
          body = "Hi {{first_name}},<br /><br />{{opening_lines}}<br /><br />83% of businesses are unable to maximize their revenues vis-a-vis the resources &amp; efforts they put into generating sales qualified prospects.<br /><br />Is your team struggling with this?  <a href=\"https://calendly.com/smartreachio/fact-finding-discovery-call\" target=\"_blank\" rel=\"noopener\">Let's connect</a><strong>.</strong><br /><br />SmartReach.io is a multichannel sales outreach tool to help you minimize lead wastage &amp; increase your sales team's efficiency. <br /><br />You can run outreach via Email, Linkedin, WhatsApp, SMS &amp; Calls<br /><br />Cheers<br />Bala<br />Sales Specialist<br />14 Days Free Trial"
        ),
        label = None,
        step_label = Some("Day 1: Opening"),
        step_delay = 86400,
        active = true,
        notes = null,
        priority = Some(tasks.models.TaskPriority.Normal),
        scheduled_count = 0
      )

      val variants: Seq[CampaignStepVariantForScheduling] = Seq(cmp_step_var_var1, cmp_step_var_var2, cmp_step_var_var3, cmp_step_var_var4, cmp_step_var_var5)

      val campaignStepWithChildren = CampaignStepWithChildren(
        id = 234013,
        label = Some("Day 1: Opening"),
        campaign_id = 87863,
        delay = 86400,
        step_type = CampaignStepType.AutoEmailStep,
        created_at = DateTime.parse("2023-03-28T11:49:22.067508Z"),
        children = List(234014),
        variants = variants
      )

      /*
      record - 2

      id          | 234014
      campaign_id | 87863
      delay       | 86400
      step_type   | linkedin_view_profile
      created_at  | 2023-03-28 11:51:16.000199+00
      label       | Day 2: Follow up 1
      template_id |
      notes       |
      priority    | normal
      children    | [234015]
      variants    | [{"id" : 290448, "step_id" : 234014, "campaign_id" : 87863, "template_id" : null, "step_type" : "linkedin_view_profile", "subject" : "", "body" : "", "text_preview" : "", "scheduled_count" : 0
      , "step_label" : "Day 2: Follow up 1", "step_delay" : 86400, "notes" : "", "priority" : "normal", "active" : true}]

      */

      val cmp_step2_var_var1 = CampaignStepVariantForScheduling(
        id = 290448,
        step_id = 234014,
        campaign_id = 87863,
        template_id = null,
        step_data = LinkedinViewProfile(),
        label = None,
        step_label = Some("Day 2: Follow up 1"),
        step_delay = 86400,
        active = true,
        notes = null,
        priority = Some(tasks.models.TaskPriority.Normal),
        scheduled_count = 0
      )

      val cam_step_record_2 = CampaignStepWithChildren(
        id = 234014,
        label = Some("Day 2: Follow up 1"),
        campaign_id = 87863,
        delay = 86400,
        step_type = CampaignStepType.LinkedinViewProfile,
        created_at = DateTime.parse("2023-03-28T11:51:16.000199Z"),
        children = List(234015),
        variants = List(cmp_step2_var_var1)
      )

      /*
      record - 3

      id          | 234015
      campaign_id | 87863
      delay       | 86400
      step_type   | send_linkedin_connection_request
      created_at  | 2023-03-28 11:56:25.860268+00
      label       | Day 3: Follow up 2
      template_id |
      notes       |
      priority    | normal
      children    | [234020]
      variants    | [{"id" : 290453, "step_id" : 234015, "campaign_id" : 87863, "template_id" : null, "step_type" : "send_linkedin_connection_request", "subject" : "", "body" : "Hi {{first_name}}, I’m on a mission to grow my connections on LinkedIn, especially with sales leaders and professionals like you. So even though we’re practically strangers, I’d love to connect.\n", "text_preview" : "",
       "scheduled_count" : 0, "step_label" : "Day 3: Follow up 2", "step_delay" : 86400, "notes" : "", "priority" : "normal", "active" : true}]

      */

      val cmp_step3_var_var1 = CampaignStepVariantForScheduling(
        id = 290448,
        step_id = 234014,
        campaign_id = 87863,
        template_id = null,
        step_data = LinkedinConnectionRequestData(
          body = Some("Hi {{first_name}}, I’m on a mission to grow my connections on LinkedIn, especially with sales leaders and professionals like you. So even though we’re practically strangers, I’d love to connect.\n")
        ),
        label = None,
        step_label = Some("Day 3: Follow up 2"),
        step_delay = 86400,
        active = true,
        notes = null,
        priority = Some(tasks.models.TaskPriority.Normal),
        scheduled_count = 0
      )



      val cam_step_record_3 = CampaignStepWithChildren(
        id = 234015,
        label = Some("Day 3: Follow up 2"),
        campaign_id = 87863,
        delay = 86400,
        step_type = CampaignStepType.LinkedinConnectionRequest,
        created_at = DateTime.parse("2023-03-28T11:56:25.860268Z"),
        children = List(234020),
        variants = List(cmp_step3_var_var1)
      )

      /*
      record - 4

      id          | 234020
      campaign_id | 87863
      delay       | 86400
      step_type   | send_email
      created_at  | 2023-03-28 12:14:22.156532+00
      label       | Day 4: Follow up 3
      template_id |
      notes       |
      priority    | normal
      children    | [234146]
      variants    | [{"id" : 290459, "step_id" : 234020, "campaign_id" : 87863, "template_id" : null, "step_type" : "send_email", "subject" : "{{previous_subject}}", "body" :
      "Hi {{first_name}}<br /><br />As a {{job_title}}, I’m sure the one thing that definitely matters is revenue. <br /><br />SmartReach helps businesses like yours generate qualified leads by simply increase your reply rates.<br /><br />In addition to outreach we provide a shared inbox for the outreach team. It's a crazy productivity booster.<br /><br /><a href=\"https://calendly.com/smartreachio/fact-finding-discovery-call\" target=\"_blank\" rel=\"noopener\">Lets connect</a>, if this interests you.<br /><br />Cheers<br />Bala<br />Sales Specialist<br />14 Days Free Trial", "text_preview" : "", "scheduled_count" : 0,
       "step_label" : "Day 4: Follow up 3", "step_delay" : 86400, "notes" : null, "priority" : "normal", "active" : true}]

      */

      val cmp_step4_var_var1 = CampaignStepVariantForScheduling(
        id = 290459,
        step_id = 234020,
        campaign_id = 87863,
        template_id = null,
        step_data = AutoEmailStep(
          subject = "{{previous_subject}}",
          body = "Hi {{first_name}}<br /><br />As a {{job_title}}, I’m sure the one thing that definitely matters is revenue. <br /><br />SmartReach helps businesses like yours generate qualified leads by simply increase your reply rates.<br /><br />In addition to outreach we provide a shared inbox for the outreach team. It's a crazy productivity booster.<br /><br /><a href=\"https://calendly.com/smartreachio/fact-finding-discovery-call\" target=\"_blank\" rel=\"noopener\">Lets connect</a>, if this interests you.<br /><br />Cheers<br />Bala<br />Sales Specialist<br />14 Days Free Trial"
        ),
        label = None,
        step_label = Some("Day 4: Follow up 3"),
        step_delay = 86400,
        active = true,
        notes = null,
        priority = Some(tasks.models.TaskPriority.Normal),
        scheduled_count = 0
      )

      val cam_step_record_4 = CampaignStepWithChildren(
        id = 234020,
        label = Some("Day 4: Follow up 3"),
        campaign_id = 87863,
        delay = 86400,
        step_type = CampaignStepType.AutoEmailStep,
        created_at = DateTime.parse("2023-03-28T12:14:22.156532Z"),
        children = List(234146),
        variants = List(cmp_step4_var_var1)
      )

      /*
      record - 4

      id          | 234020
      campaign_id | 87863
      delay       | 86400
      step_type   | send_email
      created_at  | 2023-03-28 12:14:22.156532+00
      label       | Day 4: Follow up 3
      template_id |
      notes       |
      priority    | normal
      children    | [234146]
      variants    | [{"id" : 290459, "step_id" : 234020, "campaign_id" : 87863, "template_id" : null, "step_type" : "send_email", "subject" : "{{previous_subject}}", "body" : "Hi {{first_name}}<br /><br />As a {{job_title}}, I’m sure the one thing that definitely matters is revenue. <br /><br />SmartReach helps businesses like yours generate qualified leads by simply increase your reply rates.<br /><br />In addition to outreach we provide a shared inbox for the outreach team. It's a crazy productivity booster.<br /><br /><a href=\"https://calendly.com/smartreachio/fact-finding-discovery-call\" target=\"_blank\" rel=\"noopener\">Lets connect</a>, if this interests you.<br /><br />Cheers<br />Bala<br />Sales Specialist<br />14 Days Free Trial", "text_preview" : "", "scheduled_count" : 0, "step_label" : "Day 4: Follow up 3", "step_delay" : 86400, "notes" : null, "priority" : "normal", "active" : true}]


      record - 5

      id          | 234146
      campaign_id | 87863
      delay       | 172800
      step_type   | send_email
      created_at  | 2023-03-28 14:11:18.924922+00
      label       | Day 6: Follow up 4
      template_id |
      notes       |
      priority    | normal
      children    | [234702]
      variants    | [{"id" : 290628, "step_id" : 234146, "campaign_id" : 87863, "template_id" : null, "step_type" : "send_email",
       "subject" : "{{previous_subject}}",
        "body" : "{{first_name}} I assume you are the key personnel responsible for customer acquisition from your sales channel. <br /><br /> <img src=\"https://sr-email-message-images.s3.amazonaws.com/12178_1680095583923_IowtvxOXoQlWX29b1mSGPvrRZF5g0jhL_cmpn_\" alt=\"\" width=\"329\" height=\"219\" /><br /><br />Don’t mean to bother if you’re not. Could you refer me to the right person? Thanks in advance!<br /><br />Regards<br />Bala",
         "text_preview" : "", "scheduled_count" : 0, "step_label" : "Day 6: Follow up 4", "step_delay" : 172800, "notes" : null, "priority" : "normal", "active" : true}]

*/


      val cmp_step5_var_var1 = CampaignStepVariantForScheduling(
        id = 290628,
        step_id = 234146,
        campaign_id = 87863,
        template_id = null,
        step_data = AutoEmailStep(
          subject = "{{previous_subject}}",
          body = "{{first_name}} I assume you are the key personnel responsible for customer acquisition from your sales channel. <br /><br /> <img src=\"https://sr-email-message-images.s3.amazonaws.com/12178_1680095583923_IowtvxOXoQlWX29b1mSGPvrRZF5g0jhL_cmpn_\" alt=\"\" width=\"329\" height=\"219\" /><br /><br />Don’t mean to bother if you’re not. Could you refer me to the right person? Thanks in advance!<br /><br />Regards<br />Bala"
        ),
        label = None,
        step_label = Some("Day 4: Follow up 3"),
        step_delay = 86400,
        active = true,
        notes = null,
        priority = Some(tasks.models.TaskPriority.Normal),
        scheduled_count = 0
      )

      val cam_step_record_5 = CampaignStepWithChildren(
        id = 234146,
        label = Some("Day 6: Follow up 4"),
        campaign_id = 87863,
        delay = 172800,
        step_type = CampaignStepType.AutoEmailStep,
        created_at = DateTime.parse("2023-03-28T14:11:18.924922Z"),
        children = List(234702),
        variants = List(cmp_step5_var_var1)
      )



        /*


      record - 6

      id          | 234702
      campaign_id | 87863
      delay       | 86400
      step_type   | send_linkedin_message
      created_at  | 2023-03-29 14:20:54.205749+00
      label       | Day 7: Follow up 5
      template_id |
      notes       |
      priority    | normal
      children    | [234707]
      variants    | [{"id" : 291376, "step_id" : 234702, "campaign_id" : 87863, "template_id" : null, "step_type" : "send_linkedin_message", "subject" : "",
       "body" : "Trust me, I know you might not have an easy day as the {{job_title}}, and your schedule might be as busy as it gets, but how about a quick 10 minute call next week? \n\nAgenda: Understand your current sales process\n\nLater, I shall connect with your team and walk them through the SmartReach.io features that address the current concerns. \n\nThis is my calendar (Message me for a slot not available on my calendar)\n\nhttps://calendly.com/smartreachio/fact-finding-discovery-call\n\nMy bad, SmartReach is a sales engagement software for multichannel outreach designed to increase sales via automation & team collaboration"
       , "text_preview" : "", "scheduled_count" : 0, "step_label" : "Day 7: Follow up 5", "step_delay" : 86400, "notes" : "", "priority" : "normal", "active" : true}]
*/

      val cmp_step6_var_var1 = CampaignStepVariantForScheduling(
        id = 291376,
        step_id = 234702,
        campaign_id = 87863,
        template_id = null,
        step_data = LinkedinMessageData(
          body = "Trust me, I know you might not have an easy day as the {{job_title}}, and your schedule might be as busy as it gets, but how about a quick 10 minute call next week? \n\nAgenda: Understand your current sales process\n\nLater, I shall connect with your team and walk them through the SmartReach.io features that address the current concerns. \n\nThis is my calendar (Message me for a slot not available on my calendar)\n\nhttps://calendly.com/smartreachio/fact-finding-discovery-call\n\nMy bad, SmartReach is a sales engagement software for multichannel outreach designed to increase sales via automation & team collaboration"
        ),
        label = None,
        step_label = Some("Day 7: Follow up 5"),
        step_delay = 86400,
        active = true,
        notes = null,
        priority = Some(tasks.models.TaskPriority.Normal),
        scheduled_count = 0
      )

      val cam_step_record_6 = CampaignStepWithChildren(
        id = 234702,
        label = Some("Day 7: Follow up 5"),
        campaign_id = 87863,
        delay = 86400,
        step_type = CampaignStepType.LinkedinMessage,
        created_at = DateTime.parse("2023-03-29T14:20:54.205749Z"),
        children = List(234707),
        variants = List(cmp_step6_var_var1)
      )

      /*

      record - 7

      id          | 234707
      campaign_id | 87863
      delay       | 172800
      step_type   | send_email
      created_at  | 2023-03-29 14:22:34.66118+00
      label       | Day 9: Follow up 6
      template_id |
      notes       |
      priority    | normal
      children    | []
      variants    | [{"id" : 291384, "step_id" : 234707, "campaign_id" : 87863, "template_id" : null, "step_type" : "send_email", "subject" : "{{previous_subject}}", "body" : "Bumping this up in you inbox<br /><br />{{first_name}}, give us a chance. Your team will be hitting their sales quotas in no time<br /><br />But let me know if:<br /><br />1. You’re not interested<br /><br />2. Switching your outreach platform is not a priority and we should connect later.<br /><br />3. Im targeting the wrong personnel<br /><br />4. Your dog threatens you every time you try to reply. Just joking!<br /><br />Regards<br />Bala", "text_preview" : "", "scheduled_count" : 0, "step_label" : "Day 9: Follow up 6", "step_delay" : 172800, "notes" : null, "priority" : "normal", "active" : true}]

      */

      val cmp_step7_var_var1 = CampaignStepVariantForScheduling(
        id = 291376,
        step_id = 234702,
        campaign_id = 87863,
        template_id = null,
        step_data = LinkedinMessageData(
          body = "Trust me, I know you might not have an easy day as the {{job_title}}, and your schedule might be as busy as it gets, but how about a quick 10 minute call next week? \n\nAgenda: Understand your current sales process\n\nLater, I shall connect with your team and walk them through the SmartReach.io features that address the current concerns. \n\nThis is my calendar (Message me for a slot not available on my calendar)\n\nhttps://calendly.com/smartreachio/fact-finding-discovery-call\n\nMy bad, SmartReach is a sales engagement software for multichannel outreach designed to increase sales via automation & team collaboration"
        ),
        label = None,
        step_label = Some("Day 7: Follow up 5"),
        step_delay = 86400,
        active = true,
        notes = null,
        priority = Some(tasks.models.TaskPriority.Normal),
        scheduled_count = 0
      )

      val cam_step_record_7 = CampaignStepWithChildren(
        id = 234707,
        label = Some("Day 9: Follow up 6"),
        campaign_id = 87863,
        delay = 172800,
        step_type = CampaignStepType.AutoEmailStep,
        created_at = DateTime.parse("2023-03-29T14:22:34.66118Z"),
        children = List(),
        variants = List(cmp_step7_var_var1)
      )

      val steps_returned_from_db = Seq(
        campaignStepWithChildren,
        cam_step_record_2,
        cam_step_record_3,
        cam_step_record_4,
        cam_step_record_5,
        cam_step_record_6,
        cam_step_record_7
      )

      val campaignForSchedulingEmail_balaji = CampaignForScheduling.CampaignForSchedulingEmail(
        campaign_id = 87863,
        campaign_owner_id = campaignOwnerId, // needed -
        team_id = 6412,
        org_id = orgId,
        campaign_name = campaignName,
        status = CampaignStatus.RUNNING, //

        campaign_type_data = CampaignTypeData.MultiChannelCampaignData(head_step_id = 234013),

        // settings

        ai_generation_context = None,

        sending_holiday_calendar_id = Some(sendingHolidayCalendarId),

        // in CampaignForScheduling, email_settings would be there because we ignore campaigns which do not have them
        campaign_email_setting = CampaignEmailSettingForScheduler(
          sender_email_settings_id = senderEmailSettingId,
          receiver_email_settings_id = receiverEmailSettingsId,
          campaign_email_settings_id = CampaignEmailSettingsId(123),
          emailServiceProvider = EmailServiceProvider.OTHER
        ),

        append_followups = appendFollowUps,
        open_tracking_enabled = false,
        click_tracking_enabled = false,
        opt_out_msg = "optMessage",
        opt_out_is_text = false,

        timezone = "Asia/KolKata",
        daily_from_time = 0, // time since beginning of day in seconds
        daily_till_time = 855000, // time since beginning of day in seconds

        // Sunday is the first day
        days_preference = List(false, true, true, true, true, true, false),


        email_priority = CampaignEmailPriority.FIRST_EMAIL,

        max_emails_per_prospect_per_day = 1,
        max_emails_per_prospect_per_week = 3,

        max_emails_per_prospect_account_per_day = 100,
        max_emails_per_prospect_account_per_week = 300,

        campaign_max_emails_per_day = 100,

        // warm up
        softstart_setting = None, // making it none now

        mark_completed_after_days = 20,

        latest_email_scheduled_at = Some(aDate),


        from_email = "<EMAIL>",
        from_name = "Shashank Dwivedi",

        reply_to_email = "<EMAIL>",
        reply_to_name = "Shashank Dwivedi Reply Name",

        min_delay_seconds = 50,
        max_delay_seconds = 100,

        enable_email_validation = false, // keeping it false now

        rep_mail_server_id = 2,
        via_gmail_smtp = Some(false), // Option[DateTime]
        prospects_remaining_to_be_scheduled_exists = Some(true),
        count_of_sender_emails = 1,
        selected_calendar_data = None

      )

      (campaignStepVariantDAO.findByCampaignIdForSchedule)
        .expects(87863, account.org.settings.enable_ab_testing, *)
        .returning(steps_returned_from_db)

      //      (campaignStepDAO.getOrderedSteps)
      //        .expects(Seq(campaignStepWithChildren), campaignForSchedulingEmail.head_step_id)
      //        .returning(List(campaignStepWithChildren, campaignStepWithChildrenLinkedin, campaignStepWithChildren2))


      val steps_balaji  = emailChannelScheduler.fetchCampaignStepsData(
        c = campaignForSchedulingEmail_balaji,
        account = account,
        channelType = ChannelType.EmailChannel,
        campaignStepVariantDAO = campaignStepVariantDAO,
        campaignStepDAO = campaignStepDAO
      )

//      println(s"steps : ${steps_balaji}")
      val res = emailChannelScheduler.isCampaignFirstStep(
        fetchCampaignStepsDataResult = steps_balaji.get,
        channelType = ChannelType.EmailChannel,
        schedulingForStepType = CampaignStepType.AutoEmailStep
      )

      assert(res == true)



    }


//    it("should give return true") {
//
//      val res = emailChannelScheduler.isCampaignFirstStep(
//        fetchCampaignStepsDataResult = fetchCampaignStepsData.copy(allCampaignSteps = Vector(
//
//        )),
//        channelType = ChannelType.EmailChannel,
//        schedulingForStepType = CampaignStepType.AutoEmailStep
//      )
//
//    }

  }

  describe ("Testing getProspectsByStepType") {
    it ("should create ProspectObject only for steps present in the campaign") {

      /*
      21-feb-2024: this call was moved earlier in the chain
      (campaignProspectService.getAllDistinctTimezones(
        _: Long,
        _: Long,
        _: String
      )(
        _: SRLogger
      ))
        .expects(campaignId, teamId, timeZone, Logger)
        .returning(Success(List(timeZone)))
      */

      (campaignProspectService.fetchProspectsV3MultichannelWithEmailOptionalCheck(
        _: ChannelType,
        _: List[String],
        _: Option[Long],
        _: Long,
        _: TeamId,
        _: Int,
        _: Vector[SchedulerMapStepIdAndDelay],
        _: Boolean, _: Boolean,
        _: Option[DateTime],
        _: Boolean,
        _: Option[CampaignEmailSettingForScheduler],
        _: OrgId, _:String)(using _: SRLogger))
        .expects(*, *, *, *, *, *, *, *, *, *, *, *, *, *, *)
        .returning(Try(List(prospectForScheduling)))

//
//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(_: TeamId, _: SrRollingUpdateFeature)(_: ISRLogger))
//        .expects(TeamId(teamId), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .returning(false)

      (campaignProspectDAO.filterProspectWhoHaveHoliday(
        _: List[Long],
        _: String,
        _: Option[Long],
        _: DateTime,
        _: Long,
      )(
      using _: SRLogger
      ))
        .expects(*, *, *, *, *, *)
        .returning(Success(Set()))

      val result = linkedinChannelScheduler.getProspectsByStepTypeForCampaign(
        allowedTimezones = Success(Set(timeZone)),
        c = linkedinChannelScheduledProspectsCountForCampaign,
        channelDataForScheduling = linkedinChannelDataForScheduling,
        stepTypesThatCanBeScheduled = Map(
          CampaignStepType.LinkedinViewProfile -> channelStepTypeDataForSchedulingLinkedin.copy(
            campaignStepType = CampaignStepType.LinkedinViewProfile,
            stepType = StepType(CampaignStepType.LinkedinViewProfile.toKey)
          ),
          CampaignStepType.LinkedinMessage -> channelStepTypeDataForSchedulingLinkedin.copy(
            campaignStepType = CampaignStepType.LinkedinMessage,
            stepType = StepType(CampaignStepType.LinkedinMessage.toKey)
          ),
          CampaignStepType.LinkedinConnectionRequest -> channelStepTypeDataForSchedulingLinkedin.copy(
            campaignStepType = CampaignStepType.LinkedinConnectionRequest,
            stepType = StepType(CampaignStepType.LinkedinConnectionRequest.toKey)
          )
        ),
        allStepTypesToBeScheduledByCampaign = Map(campaignId.toLong -> List(CampaignStepType.LinkedinViewProfile)),
        maxToBeScheduledForNextHour = 97,
        scheduleFromTime = DateTime.now().minusMinutes(1),
        fetchCampaignStepsDataResult = fetchCampaignStepsData,
        sendOnlyToProspectsWhoWereSentInCurrentCycle = None,
        srRollingUpdateCoreService = srRollingUpdateCoreService,
        campaignProspectDAO = campaignProspectDAO,
        campaignProspectService = campaignProspectService,
        channelData = ChannelData.LinkedinChannelData("linkedin_account_1234getontheDanceFloor"),
        calendarAppService = calendarAppService,
      )

      assert(result.size == 1 &&
        result.contains(CampaignStepType.LinkedinViewProfile) &&
        result(CampaignStepType.LinkedinViewProfile).prospects.size == 1 &&
        result(CampaignStepType.LinkedinViewProfile).step_type.campaignStepType == CampaignStepType.LinkedinViewProfile)
    }
  }

  describe("testing checkAndReturnMissingMergeTags"){
    it("should give empty Seq as calendar_link mergetag is used and calendar_link is not none"){

      val teamId = TeamId(id = prospectForSchedulingEmail.prospect.team_id)

      val stepData = AutoEmailStep(
        subject = "variant subject {{calendar_link}}",
        body = "body"
      )

      val subjectAndBody = CampaignStepData.getSubjectAndBodyFromStepData(
        stepData = stepData,
      )

      val currProspect = prospectForSchedulingEmail.prospect

      val chann = campaignStepVariantForScheduling.step_data.step_type.channelType


      (campaignsMissingMergeTagService.checkPendingMagicColumn(_: TeamId, _: String, _: String, _: ProspectObject, _: CampaignId, _: ChannelType)(_: SRLogger))
        .expects(
          teamId,
          subjectAndBody.subject,
          subjectAndBody.body,
          currProspect,
          CampaignId(id = scheduleCampaign.campaign.campaign_id),
          chann,
          Logger,
        )
        .returning(Success(List()))


      (emailServiceCompanion.getInternalMergeTag(_: String, _: String, _: String, _: Option[String], _: Option[String], _: Option[Long], _: Option[Long], _: Seq[PreviousFollowUp], _: Long, _: Long, _: TeamId, _: Boolean, _: Option[String])(using _: SRLogger))
        .expects(
          emailSettingCreateEmailSchedule.sender_name, emailSettingCreateEmailSchedule.first_name,
          emailSettingCreateEmailSchedule.last_name, Some("dummy_link"),
          None, Some(3L), Some(3L), List(), 3, 7, teamId, false, None, *
        )
        .returning(Success(internalMergeTagValuesForProspect.copy(calendar_link = Some("dummy_link"))))


      (campaignsMissingMergeTagService.checkMagicColumnWithErrors(_: TeamId, _: String, _: String, _: ProspectObject, _: ChannelType)(_: SRLogger))
        .expects(
          teamId,
          subjectAndBody.subject,
          subjectAndBody.body,
          currProspect,
          chann,
          Logger,
        )
        .returning(Success(List()))


//      val subjectAndBody= getSubjectAndBodyFromStepData(campaignStepVariantForScheduling.step_data)
//      (templateService.checkMissingMergeTags)
//        .expects(subjectAndBody.body,subjectAndBody.subject,prospectForScheduling.prospect,internalMergeTagValuesForProspect,Some(prospectAccount),ChannelType.EmailChannel)
//        .returning(Success(Seq("calendar_link")))

//      (campaignEditedPreviewEmailDAO.find)
//        .expects(*,*,*)
//        .returning(Success(Seq()))
//
//      (campaignsMissingMergeTagService.addMissingMergeTagAndResetCache(_:CampaignId,_:ProspectId,_:TeamId,_:Seq[String])(_:SRLogger))
//        .expects(*,*,*,*,*)
//        .returning(Success(1))



      val result = emailChannelScheduler.checkAndReturnMissingFields(
        sender_name = emailSettingCreateEmailSchedule.sender_name ,
        sender_first_name = emailSettingCreateEmailSchedule.first_name,
        sender_last_name = emailSettingCreateEmailSchedule.last_name ,
        unsubscribe_link = Some("dummy_link"),
        calendar_link = None,
        nextStepId = {
          ChannelSchedulerService.getNextStepId(
            currentStepId = None,
            prospectObjectOpt = None,
            isProspectConnectedToLinkedin = false,
            campaignStepsStructure = scheduleCampaign.campaignStepsStructure,
            lastSentSteps = List(),
            stepsMappedById = stepsMappedById
          ) match {
            case Left(value) => None
            case Right(value) => Some(value)
          }
        }.get,
        currentCampaign = scheduleCampaign,
        currentProspect = prospectForSchedulingEmail,
        signature =  None,
        step_data = stepData,
        currentVariant = campaignStepVariantForScheduling,
        emailServiceCompanion  = emailServiceCompanion,
        templateService = templateService ,
        campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO ,
        campaignProspectDAO = campaignProspectDAO,
        campaignsMissingMergeTagService = campaignsMissingMergeTagService ,
        Logger = Logger
      )

      Logger.warn(result.toString)

      result match {

        case Failure(exception) =>

          println(LogHelpers.getStackTraceAsString(exception))

          assert(false)

        case Success(pendingOrMissingMergeTags) =>

          assert(
            !pendingOrMissingMergeTags.skipSchedulingProspect &&
              pendingOrMissingMergeTags.missingMergeTags == List() &&
              pendingOrMissingMergeTags.pendingMagicMergeTags == List()
          )

      }

    }

    it("should give  Seq(calendar_link) as calendar_link mergetag is used and calendar_link is none") {

      val teamId = TeamId(id = prospectForSchedulingEmail.prospect.team_id)

      val stepData = AutoEmailStep(
        subject = "variant subject {{calendar_link}}",
        body = "body"
      )

      val subjectAndBody = CampaignStepData.getSubjectAndBodyFromStepData(
        stepData = stepData,
      )

      val currProspect = prospectForSchedulingEmail.prospect

      val chann = campaignStepVariantForScheduling.step_data.step_type.channelType

      (campaignsMissingMergeTagService.checkPendingMagicColumn(_: TeamId, _: String, _: String, _: ProspectObject, _: CampaignId, _: ChannelType)(_: SRLogger))
        .expects(
          teamId,
          subjectAndBody.subject,
          subjectAndBody.body,
          currProspect,
          CampaignId(id = scheduleCampaign.campaign.campaign_id),
          chann,
          Logger,
        )
        .returning(Success(List()))


      (emailServiceCompanion.getInternalMergeTag(_: String, _: String, _: String, _: Option[String], _: Option[String], _: Option[Long], _: Option[Long], _: Seq[PreviousFollowUp], _: Long, _: Long, _: TeamId, _: Boolean, _: Option[String])(using _: SRLogger))
        .expects(emailSettingCreateEmailSchedule.sender_name, emailSettingCreateEmailSchedule.first_name,
          emailSettingCreateEmailSchedule.last_name, Some("dummy_link"),
          None, Some(3L), Some(3L), List(), 3, 7, teamId, false, None, *)
        .returning(Success(internalMergeTagValuesForProspect))


      (campaignsMissingMergeTagService.checkMagicColumnWithErrors(_: TeamId, _: String, _: String, _: ProspectObject, _: ChannelType)(_: SRLogger))
        .expects(
          teamId,
          subjectAndBody.subject,
          subjectAndBody.body,
          currProspect,
          chann,
          Logger,
        )
        .returning(Success(List()))


//      val subjectAndBody = getSubjectAndBodyFromStepData(campaignStepVariantForScheduling.step_data)
//      (templateService.checkMissingMergeTags)
//        .expects(subjectAndBody.body, subjectAndBody.subject, prospectForScheduling.prospect, internalMergeTagValuesForProspect, Some(prospectAccount), ChannelType.EmailChannel)
//        .returning(Success(Seq()))


            (campaignEditedPreviewEmailDAO.find)
              .expects(*,*,*)
              .returning(Success(Seq()))

            (campaignsMissingMergeTagService.addMissingOrInvalidFieldsAndResetCache(_:CampaignId,_:ProspectId,_:TeamId,_:Seq[String])(using _:SRLogger))
              .expects(*,*,*,*,*)
              .returning(Success(1))



      val result = emailChannelScheduler.checkAndReturnMissingFields(
        sender_name = emailSettingCreateEmailSchedule.sender_name,
        sender_first_name = emailSettingCreateEmailSchedule.first_name,
        sender_last_name = emailSettingCreateEmailSchedule.last_name,
        unsubscribe_link = Some("dummy_link"),
        calendar_link = None,
        nextStepId = {
          ChannelSchedulerService.getNextStepId(
            currentStepId = None,
            prospectObjectOpt = None,
            isProspectConnectedToLinkedin = false,
            campaignStepsStructure = scheduleCampaign.campaignStepsStructure,
            lastSentSteps = List(),
            stepsMappedById = stepsMappedById
          ) match {
            case Left(value) => None
            case Right(value) => Some(value)
          }
        }.get,
        currentCampaign = scheduleCampaign,
        currentProspect = prospectForSchedulingEmail,
        signature = None,
        step_data = stepData,
        currentVariant = campaignStepVariantForScheduling,
        emailServiceCompanion = emailServiceCompanion,
        templateService = templateService,
        campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO,
        campaignProspectDAO = campaignProspectDAO,
        campaignsMissingMergeTagService = campaignsMissingMergeTagService,
        Logger = Logger
      )

      result match {

        case Failure(exception) =>

          println(LogHelpers.getStackTraceAsString(exception))

          assert(false)

        case Success(pendingOrMissingMergeTags) =>

          assert(
            pendingOrMissingMergeTags.skipSchedulingProspect &&
              pendingOrMissingMergeTags.missingMergeTags == List("calendar_link") &&
              pendingOrMissingMergeTags.pendingMagicMergeTags == List()
          )

      }

    }

    it("should give  Seq() as calendar_link mergetag is not used and calendar_link is not none") {

      val teamId = TeamId(id = prospectForSchedulingEmail.prospect.team_id)

      val stepData = campaignStepVariantForScheduling.step_data

      val subjectAndBody = CampaignStepData.getSubjectAndBodyFromStepData(
        stepData = stepData,
      )

      val currProspect = prospectForSchedulingEmail.prospect

      val chann = campaignStepVariantForScheduling.step_data.step_type.channelType

      (campaignsMissingMergeTagService.checkPendingMagicColumn(_: TeamId, _: String, _: String, _: ProspectObject, _: CampaignId, _: ChannelType)(_: SRLogger))
        .expects(
          teamId,
          subjectAndBody.subject,
          subjectAndBody.body,
          currProspect,
          CampaignId(id = scheduleCampaign.campaign.campaign_id),
          chann,
          Logger,
        )
        .returning(Success(List()))

      (emailServiceCompanion.getInternalMergeTag(_: String, _: String, _: String, _: Option[String], _: Option[String], _: Option[Long], _: Option[Long], _: Seq[PreviousFollowUp], _: Long, _: Long, _: TeamId, _: Boolean, _: Option[String])(using _: SRLogger))
        .expects(emailSettingCreateEmailSchedule.sender_name, emailSettingCreateEmailSchedule.first_name,
          emailSettingCreateEmailSchedule.last_name, Some("dummy_link"),
          None, Some(3L), Some(3L), List(), 3, 7, teamId, false, None, *)
        .returning(Success(internalMergeTagValuesForProspect.copy(calendar_link = Some("dummy_link"))))

      (campaignsMissingMergeTagService.checkMagicColumnWithErrors(_: TeamId, _: String, _: String, _: ProspectObject, _: ChannelType)(_: SRLogger))
        .expects(
          teamId,
          subjectAndBody.subject,
          subjectAndBody.body,
          currProspect,
          chann,
          Logger,
        )
        .returning(Success(List()))


      //      val subjectAndBody = getSubjectAndBodyFromStepData(campaignStepVariantForScheduling.step_data)
      //      (templateService.checkMissingMergeTags)
      //        .expects(subjectAndBody.body, subjectAndBody.subject, prospectForScheduling.prospect, internalMergeTagValuesForProspect, Some(prospectAccount), ChannelType.EmailChannel)
      //        .returning(Success(Seq()))


      val result = emailChannelScheduler.checkAndReturnMissingFields(
        sender_name = emailSettingCreateEmailSchedule.sender_name,
        sender_first_name = emailSettingCreateEmailSchedule.first_name,
        sender_last_name = emailSettingCreateEmailSchedule.last_name,
        unsubscribe_link = Some("dummy_link"),
        calendar_link = None,
        nextStepId = {
          ChannelSchedulerService.getNextStepId(
            currentStepId = None,
            prospectObjectOpt = None,
            isProspectConnectedToLinkedin = false,
            campaignStepsStructure = scheduleCampaign.campaignStepsStructure,
            lastSentSteps = List(),
            stepsMappedById = stepsMappedById
          ) match {
            case Left(value) => None
            case Right(value) => Some(value)
          }
        }.get,
        currentCampaign = scheduleCampaign,
        currentProspect = prospectForSchedulingEmail,
        signature = None,
        step_data = stepData,
        currentVariant = campaignStepVariantForScheduling,
        emailServiceCompanion = emailServiceCompanion,
        templateService = templateService,
        campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO,
        campaignProspectDAO = campaignProspectDAO,
        campaignsMissingMergeTagService = campaignsMissingMergeTagService,
        Logger = Logger
      )

      result match {

        case Failure(exception) =>

          println(LogHelpers.getStackTraceAsString(exception))

          assert(false)

        case Success(pendingOrMissingMergeTags) =>

          assert(
            !pendingOrMissingMergeTags.skipSchedulingProspect &&
              pendingOrMissingMergeTags.missingMergeTags == List() &&
              pendingOrMissingMergeTags.pendingMagicMergeTags == List()
          )

      }

    }

    it("should give  Seq() as calendar_link mergetag is not used and calendar_link is  none") {

      val teamId = TeamId(id = prospectForSchedulingEmail.prospect.team_id)

      val stepData = campaignStepVariantForScheduling.step_data

      val subjectAndBody = CampaignStepData.getSubjectAndBodyFromStepData(
        stepData = stepData,
      )

      val currProspect = prospectForSchedulingEmail.prospect

      val chann = campaignStepVariantForScheduling.step_data.step_type.channelType

      (campaignsMissingMergeTagService.checkPendingMagicColumn(_: TeamId, _: String, _: String, _: ProspectObject, _: CampaignId, _: ChannelType)(_: SRLogger))
        .expects(
          teamId,
          subjectAndBody.subject,
          subjectAndBody.body,
          currProspect,
          CampaignId(id = scheduleCampaign.campaign.campaign_id),
          chann,
          Logger,
        )
        .returning(Success(List()))


      (emailServiceCompanion.getInternalMergeTag(_: String, _: String, _: String, _: Option[String], _: Option[String], _: Option[Long], _: Option[Long], _: Seq[PreviousFollowUp], _: Long, _: Long, _: TeamId, _: Boolean, _: Option[String])(using _: SRLogger))
        .expects(emailSettingCreateEmailSchedule.sender_name, emailSettingCreateEmailSchedule.first_name,
          emailSettingCreateEmailSchedule.last_name, Some("dummy_link"),
          None, Some(3L), Some(3L), List(), 3, 7, teamId, false, None, *)
        .returning(Success(internalMergeTagValuesForProspect))


      (campaignsMissingMergeTagService.checkMagicColumnWithErrors(_: TeamId, _: String, _: String, _: ProspectObject, _: ChannelType)(_: SRLogger))
        .expects(
          teamId,
          subjectAndBody.subject,
          subjectAndBody.body,
          currProspect,
          chann,
          Logger,
        )
        .returning(Success(List()))

      val result = emailChannelScheduler.checkAndReturnMissingFields(
        sender_name = emailSettingCreateEmailSchedule.sender_name,
        sender_first_name = emailSettingCreateEmailSchedule.first_name,
        sender_last_name = emailSettingCreateEmailSchedule.last_name,
        unsubscribe_link = Some("dummy_link"),
        calendar_link = None,
        nextStepId = {
          ChannelSchedulerService.getNextStepId(
            currentStepId = None,
            prospectObjectOpt = None,
            isProspectConnectedToLinkedin = false,
            campaignStepsStructure = scheduleCampaign.campaignStepsStructure,
            lastSentSteps = List(),
            stepsMappedById = stepsMappedById
          ) match {
            case Left(value) => None
            case Right(value) => Some(value)
          }
        }.get,
        currentCampaign = scheduleCampaign,
        currentProspect = prospectForSchedulingEmail,
        signature = None,
        step_data = stepData,
        currentVariant = campaignStepVariantForScheduling,
        emailServiceCompanion = emailServiceCompanion,
        templateService = templateService,
        campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO,
        campaignProspectDAO = campaignProspectDAO,
        campaignsMissingMergeTagService = campaignsMissingMergeTagService,
        Logger = Logger
      )

      result match {

        case Failure(exception) =>

          println(LogHelpers.getStackTraceAsString(exception))

          assert(false)

        case Success(pendingOrMissingMergeTags) =>

          assert(
            !pendingOrMissingMergeTags.skipSchedulingProspect &&
              pendingOrMissingMergeTags.missingMergeTags == List() &&
              pendingOrMissingMergeTags.pendingMagicMergeTags == List()
          )

      }

    }
  }

  describe("getFinalDailyQuotaAlsoConsideringSenderRotationRoundRobin") {

    it("when we have 3 sender email and 100 per_sender_per_day and 100 total prospects but campaignProspectService.getSenderRotationStats fails") {
      (campaignProspectService.getSenderRotationStats (_: CampaignId, _: TeamId, _: Int, _: DateTime)(using _: SRLogger))
        .expects(CampaignId(campaignId), TeamId(505), 300, *, *)
        .returning(Failure(error))
      val result = emailChannelScheduler.getFinalDailyQuotaAlsoConsideringSenderRotationRoundRobin(
        warmupSettingOpt = None,
        channelOrCampaignMinDailyQuota = 100,
        campaign = campaignForSchedulingEmail.copy(
          count_of_sender_emails = 3
        ),
        campaignProspectService = campaignProspectService,
        campaignStepType = CampaignStepType.AutoEmailStep,
        isFirstStep = true
      )
      println(s"result $result")
      assert(result ==Failure(error))
    }


    it("when we have 3 sender email and 100 per_sender_per_day and 100 total prospects") {
      (campaignProspectService.getSenderRotationStats (_: CampaignId, _: TeamId, _: Int, _: DateTime)(using _: SRLogger))
        .expects(CampaignId(campaignId), TeamId(505), 300, *, *)
        .returning(Success(SenderRotationStats(prospects_not_sent_any_emails = 100, prospects_to_get_follow_up = 0)))
      val result = emailChannelScheduler.getFinalDailyQuotaAlsoConsideringSenderRotationRoundRobin(
        warmupSettingOpt = None,
        channelOrCampaignMinDailyQuota = 100,
        campaign = campaignForSchedulingEmail.copy(
          count_of_sender_emails = 3
        ),
        campaignProspectService = campaignProspectService,
        campaignStepType = CampaignStepType.AutoEmailStep,
        isFirstStep = true
      )
      println(s"result $result")
      assert(result == Success(33))
    }


    it("when we have 3 sender email and 100 per_sender_per_day and 300 total prospects") {
      (campaignProspectService.getSenderRotationStats (_: CampaignId, _: TeamId, _: Int, _: DateTime)(using _: SRLogger))
        .expects(CampaignId(campaignId), TeamId(505), 300, *, *)
        .returning(Success(SenderRotationStats(prospects_not_sent_any_emails = 300, prospects_to_get_follow_up = 0)))
      val result = emailChannelScheduler.getFinalDailyQuotaAlsoConsideringSenderRotationRoundRobin(
        warmupSettingOpt = None,
        channelOrCampaignMinDailyQuota = 100,
        campaign = campaignForSchedulingEmail.copy(
          count_of_sender_emails = 3
        ),
        campaignProspectService = campaignProspectService,
        campaignStepType = CampaignStepType.AutoEmailStep,
        isFirstStep = true
      )
      println(s"result $result")
      assert(result == Success(100))
    }



    it("when we have 3 sender email and 100 per_sender_per_day and 3 total prospects") {
      (campaignProspectService.getSenderRotationStats (_: CampaignId, _: TeamId, _: Int, _: DateTime)(using _: SRLogger))
        .expects(CampaignId(campaignId), TeamId(505), 300, *, *)
        .returning(Success(SenderRotationStats(prospects_not_sent_any_emails = 3, prospects_to_get_follow_up = 0)))
      val result = emailChannelScheduler.getFinalDailyQuotaAlsoConsideringSenderRotationRoundRobin(
        warmupSettingOpt = None,
        channelOrCampaignMinDailyQuota = 100,
        campaign = campaignForSchedulingEmail.copy(
          count_of_sender_emails = 3
        ),
        campaignProspectService = campaignProspectService,
        campaignStepType = CampaignStepType.AutoEmailStep,
        isFirstStep = true
      )
      println(s"result $result")
      assert(result == Success(1))
    }

    it("when we have 3 sender email and 100 per_sender_per_day and 1 total prospects") {
      (campaignProspectService.getSenderRotationStats (_: CampaignId, _: TeamId, _: Int, _: DateTime)(using _: SRLogger))
        .expects(CampaignId(campaignId), TeamId(505), 300, *, *)
        .returning(Success(SenderRotationStats(prospects_not_sent_any_emails = 1, prospects_to_get_follow_up = 0)))
      val result = emailChannelScheduler.getFinalDailyQuotaAlsoConsideringSenderRotationRoundRobin(
        warmupSettingOpt = None,
        channelOrCampaignMinDailyQuota = 100,
        campaign = campaignForSchedulingEmail.copy(
          count_of_sender_emails = 3
        ),
        campaignProspectService = campaignProspectService,
        campaignStepType = CampaignStepType.AutoEmailStep,
        isFirstStep = true
      )
      println(s"result $result")
      assert(result == Success(1))
    }


    it("when we have 1 sender email and 100 per_sender_per_day and 1 total prospects") {
      (campaignProspectService.getSenderRotationStats (_: CampaignId, _: TeamId, _: Int, _: DateTime)(using _: SRLogger))
        .expects(CampaignId(campaignId), TeamId(505), 100, *, *)
        .returning(Success(SenderRotationStats(prospects_not_sent_any_emails = 1, prospects_to_get_follow_up = 0)))
      val result = emailChannelScheduler.getFinalDailyQuotaAlsoConsideringSenderRotationRoundRobin(
        warmupSettingOpt = None,
        channelOrCampaignMinDailyQuota = 100,
        campaign = campaignForSchedulingEmail.copy(
          count_of_sender_emails = 1
        ),
        campaignProspectService = campaignProspectService,
        campaignStepType = CampaignStepType.AutoEmailStep,
        isFirstStep = true
      )
      println(s"result $result")
      assert(result == Success(1))
    }




    it("if not first step") {
      val result = emailChannelScheduler.getFinalDailyQuotaAlsoConsideringSenderRotationRoundRobin(
        warmupSettingOpt = None,
        channelOrCampaignMinDailyQuota = 100,
        campaign = campaignForSchedulingEmail.copy(
          count_of_sender_emails = 1
        ),
        campaignProspectService = campaignProspectService,
        campaignStepType = CampaignStepType.AutoEmailStep,
        isFirstStep = false
      )
      println(s"result $result")
      assert(result == Success(100))
    }

    it("with warmup setting not limiting the quota") {
      val warmupSetting = CampaignWarmupSetting(
        warmup_started_at = DateTime.now().minusDays(3),
        warmup_length_in_days = 3,
        warmup_starting_email_count = 50
      )
      // Expected quota today: start_quota + days_passed * increase = 50 + 3 * 50 = 200
      // This reaches max_daily_quota_for_warmup (200), which is higher than channelOrCampaignMinDailyQuota (100)
      val result = emailChannelScheduler.getFinalDailyQuotaAlsoConsideringSenderRotationRoundRobin(
        warmupSettingOpt = Some(warmupSetting),
        channelOrCampaignMinDailyQuota = 100,
        campaign = campaignForSchedulingEmail.copy(count_of_sender_emails = 1),
        campaignProspectService = campaignProspectService,
        campaignStepType = CampaignStepType.AutoEmailStep,
        isFirstStep = false
      )
      println(s"Warmup not limited result: $result")
      assert(result == Success(100)) // Should be limited by channelOrCampaignMinDailyQuota
    }

    // --- Tests for AutoEmailMagicContent ---

    it("AutoEmailMagicContent, not first step, with pending approvals reducing quota") {
      (campaignProspectService.getPendingApprovalCountForCampaign (_: CampaignId, _: TeamId, _: CampaignStepType)(using _: SRLogger))
        .expects(CampaignId(campaignId), TeamId(505), CampaignStepType.AutoEmailMagicContent,  *)
        .returning(Success(20)) // 20 prospects pending approval

      val result = emailChannelScheduler.getFinalDailyQuotaAlsoConsideringSenderRotationRoundRobin(
        warmupSettingOpt = None,
        channelOrCampaignMinDailyQuota = 100,
        campaign = campaignForSchedulingEmail.copy(count_of_sender_emails = 1),
        campaignProspectService = campaignProspectService,
        campaignStepType = CampaignStepType.AutoEmailMagicContent, // Magic content step
        isFirstStep = false // Not the first step, so no sender rotation
      )
      println(s"Magic content pending approval result (not first step): $result")
      assert(result == Success(80)) // 100 (base) - 20 (pending) = 80
    }

    it("AutoEmailMagicContent, not first step, pending approvals make quota zero") {
      (campaignProspectService.getPendingApprovalCountForCampaign (_: CampaignId, _: TeamId,  _: CampaignStepType)(using _: SRLogger))
        .expects(CampaignId(campaignId), TeamId(505), CampaignStepType.AutoEmailMagicContent,  *)
        .returning(Success(150)) // More pending approvals than base quota

      val result = emailChannelScheduler.getFinalDailyQuotaAlsoConsideringSenderRotationRoundRobin(
        warmupSettingOpt = None,
        channelOrCampaignMinDailyQuota = 100,
        campaign = campaignForSchedulingEmail.copy(count_of_sender_emails = 1),
        campaignProspectService = campaignProspectService,
        campaignStepType = CampaignStepType.AutoEmailMagicContent,
        isFirstStep = false
      )
      println(s"Magic content pending approval makes quota zero result: $result")
      assert(result == Success(0)) // Should clamp to 0
    }

    it("AutoEmailMagicContent, first step, with pending approvals and sufficient new prospects") {
      (campaignProspectService.getPendingApprovalCountForCampaign (_: CampaignId, _: TeamId,  _: CampaignStepType)(using _: SRLogger))
        .expects(CampaignId(campaignId), TeamId(505), CampaignStepType.AutoEmailMagicContent, *)
        .returning(Success(10)) // 10 pending approval
      (campaignProspectService.getSenderRotationStats (_: CampaignId, _: TeamId, _: Int, _: DateTime)(using _: SRLogger))
        .expects(CampaignId(campaignId), TeamId(505), 270, *, *) // least_amount_needed = min(100*3, (100-10)*3) = min(300, 270) = 270
        .returning(Success(SenderRotationStats(prospects_not_sent_any_emails = 300, prospects_to_get_follow_up = 0))) // Enough prospects

      val result = emailChannelScheduler.getFinalDailyQuotaAlsoConsideringSenderRotationRoundRobin(
        warmupSettingOpt = None,
        channelOrCampaignMinDailyQuota = 100,
        campaign = campaignForSchedulingEmail.copy(count_of_sender_emails = 3),
        campaignProspectService = campaignProspectService,
        campaignStepType = CampaignStepType.AutoEmailMagicContent,
        isFirstStep = true // First step
      )
      println(s"Magic content pending approval + first step + sufficient prospects result: $result")
      // Base = 100. After pending = 90. Sender rotation doesn't limit further as 300 >= 270.
      assert(result == Success(90))
    }

     it("AutoEmailMagicContent, first step, with pending approvals and insufficient new prospects") {
      (campaignProspectService.getPendingApprovalCountForCampaign (_: CampaignId, _: TeamId,  _: CampaignStepType)(using _: SRLogger))
        .expects(CampaignId(campaignId), TeamId(505),CampaignStepType.AutoEmailMagicContent, *)
        .returning(Success(10)) // 10 pending approval
      (campaignProspectService.getSenderRotationStats (_: CampaignId, _: TeamId, _: Int, _: DateTime)(using _: SRLogger))
        .expects(CampaignId(campaignId), TeamId(505), 270, *, *) // least_amount_needed = 270
        .returning(Success(SenderRotationStats(prospects_not_sent_any_emails = 60, prospects_to_get_follow_up = 0))) // Only 60 new prospects

      val result = emailChannelScheduler.getFinalDailyQuotaAlsoConsideringSenderRotationRoundRobin(
        warmupSettingOpt = None,
        channelOrCampaignMinDailyQuota = 100,
        campaign = campaignForSchedulingEmail.copy(count_of_sender_emails = 3),
        campaignProspectService = campaignProspectService,
        campaignStepType = CampaignStepType.AutoEmailMagicContent,
        isFirstStep = true
      )
      println(s"Magic content pending approval + first step + insufficient prospects result: $result")
      // Base = 100. After pending = 90.
      // Sender rotation applies: 60 < 270. count = floor(60 / 3) = 20.
      assert(result == Success(20))
    }

    it("AutoEmailMagicContent, first step, failure fetching pending approvals") {
       val pendingError = new Exception("DB error fetching pending count")
      (campaignProspectService.getPendingApprovalCountForCampaign (_: CampaignId, _: TeamId,  _: CampaignStepType)(using _: SRLogger))
        .expects(CampaignId(campaignId), TeamId(505),CampaignStepType.AutoEmailMagicContent, *)
        .returning(Failure(pendingError)) // Fails

      // getSenderRotationStats should not be called if the first part fails
      // (campaignProspectService.getSenderRotationStats ...).expects(...) is omitted

      val result = emailChannelScheduler.getFinalDailyQuotaAlsoConsideringSenderRotationRoundRobin(
        warmupSettingOpt = None,
        channelOrCampaignMinDailyQuota = 100,
        campaign = campaignForSchedulingEmail.copy(count_of_sender_emails = 3),
        campaignProspectService = campaignProspectService,
        campaignStepType = CampaignStepType.AutoEmailMagicContent,
        isFirstStep = true
      )
      println(s"Magic content pending approval failure result: $result")
      assert(result == Failure(pendingError)) // Should propagate the failure
    }

     it("AutoEmailMagicContent, first step, pending approvals reduce quota, sender rotation applied (edge case: 1 prospect)") {
      (campaignProspectService.getPendingApprovalCountForCampaign (_: CampaignId, _: TeamId,  _: CampaignStepType)(using _: SRLogger))
        .expects(CampaignId(campaignId), TeamId(505), CampaignStepType.AutoEmailMagicContent,  *)
        .returning(Success(5)) // 5 pending approval
      (campaignProspectService.getSenderRotationStats (_: CampaignId, _: TeamId, _: Int, _: DateTime)(using _: SRLogger))
        .expects(CampaignId(campaignId), TeamId(505), 285, *, *) // least_amount_needed = min(100*3, (100-5)*3) = min(300, 285) = 285
        .returning(Success(SenderRotationStats(prospects_not_sent_any_emails = 1, prospects_to_get_follow_up = 0))) // Only 1 new prospect

      val result = emailChannelScheduler.getFinalDailyQuotaAlsoConsideringSenderRotationRoundRobin(
        warmupSettingOpt = None,
        channelOrCampaignMinDailyQuota = 100,
        campaign = campaignForSchedulingEmail.copy(count_of_sender_emails = 3),
        campaignProspectService = campaignProspectService,
        campaignStepType = CampaignStepType.AutoEmailMagicContent,
        isFirstStep = true
      )
      println(s"Magic content pending approval + first step + 1 prospect result: $result")
      // Base = 100. After pending = 95.
      // Sender rotation applies: 1 < 285. count = floor(1 / 3) = 0. Logic returns 1 in this case.
      assert(result == Success(1))
    }

     it("ManualEmailStep, first step, ensures magic content logic is not applied") {
       // getPendingApprovalCountForCampaign should NOT be called
       (campaignProspectService.getSenderRotationStats (_: CampaignId, _: TeamId, _: Int, _: DateTime)(using _: SRLogger))
         .expects(CampaignId(campaignId), TeamId(505), 300, *, *) // least_amount_needed = min(100*3, 100*3) = 300
         .returning(Success(SenderRotationStats(prospects_not_sent_any_emails = 300, prospects_to_get_follow_up = 0))) // Enough prospects

       val result = emailChannelScheduler.getFinalDailyQuotaAlsoConsideringSenderRotationRoundRobin(
         warmupSettingOpt = None,
         channelOrCampaignMinDailyQuota = 100,
         campaign = campaignForSchedulingEmail.copy(count_of_sender_emails = 3),
         campaignProspectService = campaignProspectService,
         campaignStepType = CampaignStepType.ManualEmailStep, // NOT magic content
         isFirstStep = true
       )
       println(s"ManualEmailStep result (first step): $result")
       // Base = 100. No pending approval check. Sender rotation doesn't limit.
       assert(result == Success(100))
     }

  }

  val prospectFoundForSchedulingByStepType_2 = emailChannelScheduler.ProspectFoundForSchedulingByStepType(
    prospectForScheduling = prospectForSchedulingEmail,
    step_type = channelStepTypeDataForScheduling
  )

  val prospectFoundForSchedulingByStepType_2_failing = emailChannelScheduler.ProspectFoundForSchedulingByStepType(
    prospectForScheduling = prospectForSchedulingEmail.copy(
      prospect = prospectObject.copy(id = 999)
    ),
    step_type = channelStepTypeDataForScheduling
  )

  val scheduleCampaign_2 = emailChannelScheduler.ScheduleCampaign(
    markedCompletedIds = Seq(),
    campaign = EmailChannelSchedulerFixtures.campaignForSchedulingEmail,
    stepsMappedById = Map(3L -> EmailChannelSchedulerFixtures.campaignStepWithChildren_manual_email_step),
    campaignStepsStructure = CampaignStepsStructure.MultichannelCampaignStepsStructure(
      orderedStepIds = Vector(3)
    ),
    prospects = flattenedProspects,
    distinctTimezones = Set()

  )

  val generateScheduleTaskData_2 = emailChannelScheduler.GenerateScheduleTaskData(
    currentCampaign = scheduleCampaign_2,
    nextStep = EmailChannelSchedulerFixtures.campaignStepWithChildren_manual_email_step,
    currentProspect = prospectFoundForSchedulingByStepType_2,
    currentVariant = EmailChannelSchedulerFixtures.campaignStepVariantForScheduling_manual,
    schedulerDateTime = aDate.plusSeconds(60),
  )

  val email_scheduled_new = EmailChannelSchedulerFixtures.email_scheduled_new

  describe("testing EmailChannelScheduler.saveTasksToBeScheduledAndUpdateCampaignDataV2"){

    val manual_task_data = EmailChannelSchedulerFixtures.task_data_manual_task
    val account_id: Long = EmailChannelSchedulerFixtures.channel_owner_account_id
    val team_id: Long = EmailChannelSchedulerFixtures.team_id
    val emails_scheduled_after_saving = EmailChannelSchedulerFixtures.emailScheduledNewAfterSaving
    val date_time_now = EmailChannelSchedulerFixtures.aDate
    val email_service_body_fixture = EmailChannelSchedulerFixtures.emailServiceBody
    val email_scheduled_new_step_2 = EmailChannelSchedulerFixtures.email_scheduled_new_step_2
    val campaign_prospect_list_to_be_updated = EmailChannelSchedulerFixtures.update_campaign_prospects_list

    it("tried failing one task insertion still all the flow went ahead smoothly"){

      (orgMetadataService.getOrgMetadata(_: OrgId))
        .expects(OrgId(id = 11))
        .returning(Success(orgMetadata))

      (accountOrgBillingRelatedService.checkAndUpdateProspectsContacted)
        .expects(ProspectId(7), TeamId(15), ChannelType.EmailChannel, ProspectTouchedType.ManualTaskScheduled, false, Logger)
        .returning(Success(1))
        .repeat(7)

      (taskService.createTask(_: NewTask, _: Long, _: Long)(_: ExecutionContext, _: SRLogger))
        .expects(manual_task_data, account_id, team_id, *, *)
        .returning(Future.successful(Left(CreateTaskError.MightBeDuplicateTaskError(error))))

      (taskService.createTask(_: NewTask, _: Long, _: Long)(_: ExecutionContext, _: SRLogger))
        .expects(manual_task_data,account_id, team_id, *, *)
        .returning(Future.successful(Right("task_id"))).repeat(6)

      (() => srDateTimeUtils.getDateTimeNow())
        .expects()
        .returning(date_time_now)
        .atLeastTwice()
      (srUuidUtils.generateEmailsScheduledUuid _)
        .expects()
        .returning(email_scheduled_new.uuid)
        .atLeastTwice()
      (emailScheduledDAOService.saveEmailsToBeScheduledAndUpdateCampaignDataV2(_:Vector[EmailScheduledNew], _: CampaignEmailSettingsId, _: Option[EmailSendingFlow], _: SRLogger))
        .expects(Vector(
          email_scheduled_new,
          email_scheduled_new,
            email_scheduled_new,
            email_scheduled_new,
            email_scheduled_new,
            email_scheduled_new,
            email_scheduled_new
        ),
          generateScheduleTaskData_2.currentCampaign.campaign.campaign_email_setting.campaign_email_settings_id,
          None,
          *)
        .returning(Success(Seq(
          emails_scheduled_after_saving,
          emails_scheduled_after_saving,
          emails_scheduled_after_saving,
          emails_scheduled_after_saving,
          emails_scheduled_after_saving,
          emails_scheduled_after_saving,
          emails_scheduled_after_saving
        )))

      (() => repTrackingHostService.getRepTrackingHosts())
        .expects()
        .returning(Success(Seq()))
        .anyNumberOfTimes()

      (emailServiceCompanion.getBodies(
        _:Option[CampaignEditedPreviewEmail],
        _: Long, _: Option[CalendarAccountData],
        _: Option[SelectedCalendarData],
        _: Option[Long],
        _: Long,
        _: Option[Long],
        _: Option[Long],
        _: ProspectObject,
        _: EmailOptionsForGetBodies
      )(_: SRLogger))
        .expects(None, 11,
          Some(EmailChannelSchedulerFixtures.calendar_account_data),
          None,
          Some(11L),
          3,
          Some(3L),
          Some(3L),
          EmailChannelSchedulerFixtures.prospectObject,
          EmailChannelSchedulerFixtures.email_service_options_for_get_bodies,
          *
        )
        .returning(Success(
          EmailServiceBody(
            subject = "variant subject",
            textBody = "variant body",
            htmlBody = "body",
            baseBody = "body",
            isEditedPreviewEmail = false,
            has_unsubscribe_link = true
        )))
        .repeat(7)


      (emailScheduledDAOService.addBodyToEmailsToBeScheduled)
        .expects(Seq(
          email_scheduled_new_step_2,
          email_scheduled_new_step_2,
          email_scheduled_new_step_2,
          email_scheduled_new_step_2,
          email_scheduled_new_step_2,
          email_scheduled_new_step_2,
          email_scheduled_new_step_2
        ),
          TeamId(id = team_id),
          *
        )
        .returning(Success(Seq()))


      val result = emailChannelScheduler.saveTasksToBeScheduledAndUpdateCampaignDataV2(
        channelTasks = Vector(
          generateScheduleTaskData_2,
          generateScheduleTaskData_2,
          generateScheduleTaskData_2,
          generateScheduleTaskData_2,
          generateScheduleTaskData_2,
          generateScheduleTaskData_2,
          generateScheduleTaskData_2
        ),
        channelDataForScheduling = EmailChannelSchedulerFixtures.email_channel_data_for_scheduling,
        scheduleCampaigns = Seq(scheduleCampaign_2),
        orgId = orgId,
        taskService = taskService,
        templateService = templateService,
        accountOrgBillingRelatedService = accountOrgBillingRelatedService,
        calendarAccountData = Some(EmailChannelSchedulerFixtures.calendar_account_data)
      )

      result.map(campaign_prospects => {

        println(s"cp : ${campaign_prospects}")
        assert(campaign_prospects == campaign_prospect_list_to_be_updated)

      })
    }

    it("failing first task_creation_in_db and checking if other tasks are created successfully") {

      (orgMetadataService.getOrgMetadata(_: OrgId))
        .expects(OrgId(id = 11))
        .returning(Success(orgMetadata))

      (accountOrgBillingRelatedService.checkAndUpdateProspectsContacted)
        .expects(ProspectId(999), TeamId(15), ChannelType.EmailChannel, ProspectTouchedType.ManualTaskScheduled, false, Logger)
        .returning(Success(1))

      (accountOrgBillingRelatedService.checkAndUpdateProspectsContacted)
        .expects(ProspectId(7), TeamId(15), ChannelType.EmailChannel, ProspectTouchedType.ManualTaskScheduled, false, Logger)
        .returning(Success(1))
        .repeat(6)

      (taskService.createTask(_: NewTask, _: Long, _: Long)(_: ExecutionContext, _: SRLogger))
        .expects(manual_task_data.copy(
          prospect_id = Some(999)
        )
          , account_id, team_id, *, *)
        .returning(Future.successful(Left(CreateTaskError.MightBeDuplicateTaskError(error))))

      (taskService.createTask(_: NewTask, _: Long, _: Long)(_: ExecutionContext, _: SRLogger))
        .expects(manual_task_data, account_id, team_id, *, *)
        .returning(Future.successful(Right("task_id"))).repeat(6)

      (() => srDateTimeUtils.getDateTimeNow())
        .expects()
        .returning(date_time_now)
        .atLeastTwice()

      (emailScheduledDAOService.saveEmailsToBeScheduledAndUpdateCampaignDataV2(_: Vector[EmailScheduledNew], _: CampaignEmailSettingsId, _: Option[EmailSendingFlow], _: SRLogger))
        .expects(Vector(
          email_scheduled_new.copy(prospect_id = Some(999)),
          email_scheduled_new,
          email_scheduled_new,
          email_scheduled_new,
          email_scheduled_new,
          email_scheduled_new,
          email_scheduled_new
        ),
          generateScheduleTaskData_2.currentCampaign.campaign.campaign_email_setting.campaign_email_settings_id,
          None,
          *)
        .returning(Success(Seq(
          emails_scheduled_after_saving,
          emails_scheduled_after_saving,
          emails_scheduled_after_saving,
          emails_scheduled_after_saving,
          emails_scheduled_after_saving,
          emails_scheduled_after_saving,
          emails_scheduled_after_saving
        )))

      (() => repTrackingHostService.getRepTrackingHosts())
        .expects()
        .returning(Success(Seq()))
        .anyNumberOfTimes()

      (emailServiceCompanion.getBodies(
        _: Option[CampaignEditedPreviewEmail],
        _: Long, _: Option[CalendarAccountData],
        _: Option[SelectedCalendarData],
        _: Option[Long],
        _: Long,
        _: Option[Long],
        _: Option[Long],
        _: ProspectObject,
        _: EmailOptionsForGetBodies
      )(_: SRLogger))
        .expects(None, 11,
          Some(EmailChannelSchedulerFixtures.calendar_account_data),
          None,
          Some(11L),
          3,
          Some(3L),
          Some(3L),
          EmailChannelSchedulerFixtures.prospectObject,
          EmailChannelSchedulerFixtures.email_service_options_for_get_bodies,
          *
        )
        .returning(Success(
          EmailServiceBody(
            subject = "variant subject",
            textBody = "variant body",
            htmlBody = "body",
            baseBody = "body",
            isEditedPreviewEmail = false,
            has_unsubscribe_link = true
          )))
        .repeat(7)


      (emailScheduledDAOService.addBodyToEmailsToBeScheduled)
        .expects(Seq(
          email_scheduled_new_step_2,
          email_scheduled_new_step_2,
          email_scheduled_new_step_2,
          email_scheduled_new_step_2,
          email_scheduled_new_step_2,
          email_scheduled_new_step_2,
          email_scheduled_new_step_2
        ),
          TeamId(id = team_id),
          *
        )
        .returning(Success(Seq()))
      (srUuidUtils.generateEmailsScheduledUuid _)
        .expects()
        .returning(email_scheduled_new.uuid)
        .atLeastTwice()

      val result = emailChannelScheduler.saveTasksToBeScheduledAndUpdateCampaignDataV2(
        channelTasks = Vector(
          generateScheduleTaskData_2.copy(
            currentProspect = prospectFoundForSchedulingByStepType_2_failing
          ),
          generateScheduleTaskData_2,
          generateScheduleTaskData_2,
          generateScheduleTaskData_2,
          generateScheduleTaskData_2,
          generateScheduleTaskData_2,
          generateScheduleTaskData_2
        ),
        channelDataForScheduling = EmailChannelSchedulerFixtures.email_channel_data_for_scheduling,
        scheduleCampaigns = Seq(scheduleCampaign_2),
        orgId = orgId,
        taskService = taskService,
        templateService = templateService,
        accountOrgBillingRelatedService = accountOrgBillingRelatedService,
        calendarAccountData = Some(EmailChannelSchedulerFixtures.calendar_account_data)
      )

      result.map(campaign_prospects => {

        println(s"cp : ${campaign_prospects}")
        assert(campaign_prospects == campaign_prospect_list_to_be_updated)

      })
    }
  }

  // FIXME DRIP: Breaking in production. need to figure out different way to test this.
//  describe("Testing getLastSteps") {
//    it("should return all the last steps of the campaign") {
//      NextStepFinderForDrip.getLastSteps(
//        head_step_id = headStepId,
//        edges = List(
//          Json.obj("id" -> "1-2", "source" -> "11", "target" -> "condition_1", "label" -> " "),
//          Json.obj("id" -> "2-3", "source" -> "condition_1", "target" -> "13", "label" -> "yes"),
//          Json.obj("id" -> "2-4", "source" -> "condition_1", "target" -> "17", "label" -> "no")
//        )
//      )
//        .map(res => {
//          println(res)
//          assert(res == List(13, 17))
//        })
//        .recover {
//          case e =>
//            println(e.toString)
//            assert(false)
//        }
//    }
//  }

//  describe("testing getMapOfStepIdAndRequiredDelay"){
//
//    it("should return all campaign steps"){
//
//      Channel.getMap
//
//    }
//  }

  describe("generateEmailScheduledNew with new parameter") {
    // Test Data Setup
    val testDateTime = DateTime.now()

    // Create CPCompleted
    val testCPCompleted = CPCompleted(
      campaignId = 123L,
      prospectId = 456L,
      completed = false
    )



    // Create CampaignForSchedulingEmail
    val testCampaignForScheduling = CampaignForScheduling.CampaignForSchedulingEmail(
      campaign_id = 123L,
      campaign_owner_id = 456L,
      team_id = 789L,
      org_id = 101L,
      campaign_name = "Test Campaign",
      status = CampaignStatus.RUNNING,
      ai_generation_context = None,
      campaign_type_data = CampaignTypeData.EmailChannelData(head_step_id = 1L),
      sending_holiday_calendar_id = None,
      campaign_email_setting = CampaignEmailSettingForScheduler(
        sender_email_settings_id = senderEmailSettingId,
        receiver_email_settings_id = receiverEmailSettingsId,
        campaign_email_settings_id = CampaignEmailSettingsId(123),
        emailServiceProvider = EmailServiceProvider.OTHER

      ),
      append_followups = false,
      open_tracking_enabled = true,
      click_tracking_enabled = true,
      opt_out_msg = "Test opt out",
      opt_out_is_text = false,
      timezone = "UTC",
      daily_from_time = 32400, // 9 AM
      daily_till_time = 64800, // 6 PM
      days_preference = List(true, true, true, true, true, false, false),
      email_priority = CampaignEmailPriority.EQUAL,
      max_emails_per_prospect_per_day = 2,
      max_emails_per_prospect_per_week = 10,
      max_emails_per_prospect_account_per_day = 5,
      max_emails_per_prospect_account_per_week = 20,
      campaign_max_emails_per_day = 100,
      softstart_setting = None,
      mark_completed_after_days = 30,
      latest_email_scheduled_at = None,
      from_email = "<EMAIL>",
      from_name = "Test Sender",
      reply_to_email = "<EMAIL>",
      reply_to_name = "Test Reply",
      min_delay_seconds = 300,
      max_delay_seconds = 900,
      enable_email_validation = true,
      rep_mail_server_id = 1,
      via_gmail_smtp = Some(true),
      prospects_remaining_to_be_scheduled_exists = Some(true),
      count_of_sender_emails = 0,
      selected_calendar_data = None
    )

    // Create CampaignStepWithChildren
    val testStep = CampaignStepWithChildren(
      id = 1L,
      label = Some("Test Step"),
      campaign_id = 123L,
      delay = 3600,
      step_type = CampaignStepType.AutoEmailStep,
      created_at = testDateTime,
      children = List(2, 3),
      variants = Seq.empty
    )

    // Create CampaignStepVariantForScheduling
    val testVariant = CampaignStepVariantForScheduling(
      id = 1L,
      step_id = 1L,
      campaign_id = 123L,
      template_id = Some(456L),
      step_data = CampaignStepData.AutoEmailStep(
        subject = "Test Subject",
        body = "Test Body"
      ),
      label = Some("Test Variant"),
      step_label = Some("Test Step Label"),
      step_delay = 3600L,
      notes = None,
      priority = None,
      active = true,
      scheduled_count = 0
    )

    // Create ProspectObject
    val testProspectObj = ProspectObject(
      id = 123L,
      owner_id = 456L,
      team_id = 789L,
      first_name = Some("Test"),
      last_name = Some("User"),
      email = Some("<EMAIL>"),
      custom_fields = Json.obj(),
      list = None,
      job_title = Some("Manager"),
      company = Some("Test Company"),
      linkedin_url = None,
      phone = None,
      phone_2 = None,
      phone_3 = None,
      city = Some("Test City"),
      state = Some("Test State"),
      country = Some("Test Country"),
      timezone = Some("UTC"),
      prospect_category = "default",
      last_contacted_at = None,
      last_contacted_at_phone = None,
      created_at = testDateTime,
      internal = prospectObjectInternal,
      latest_reply_sentiment_uuid = None,
      current_step_type = None,
      latest_task_done_at = None,
      prospect_uuid = Some(ProspectUuid("test-uuid")),
      owner_uuid = AccountUuid("test-account-uuid"),
      updated_at = testDateTime
    )

    // Create EmailChannelProspectForScheduling
    val testProspectData = ProspectDataForChannelScheduling.EmailChannelProspectForScheduling(
      prospect = testProspectObj,
      current_step_id = None,
      current_step_status_data = None,
      email_checked = true,
      email_sent_for_validation = false,
      email_sent_for_validation_at = None
    )

    // Create ChannelStepTypeDataForScheduling
    val testStepTypeData = emailChannelScheduler.ChannelStepTypeDataForScheduling(
      campaignStepType = CampaignStepType.AutoEmailStep,
      totalScheduledForStepTypeTillNow = 0,
      channelStepTypeDailyLimit = 100,
      remainingToBeScheduledFromChannelStepType = 100,
      campaignStepTypeLimitHasBeenReachedForToday = false,
      stepType = StepType(CampaignStepType.AutoEmailMagicContent.toKey)
    )

    // Create ProspectFoundForSchedulingByStepType
    val testProspect = emailChannelScheduler.ProspectFoundForSchedulingByStepType(
      prospectForScheduling = testProspectData,
      step_type = testStepTypeData
    )

    // Create ScheduleCampaign
    val testScheduleCampaign = emailChannelScheduler.ScheduleCampaign(
      markedCompletedIds = Seq(testCPCompleted),
      campaign = testCampaignForScheduling,
      stepsMappedById = Map(1L -> testStep),
      campaignStepsStructure = CampaignStepsStructure.EmailCampaignStepsStructure(
        orderedStepIds = Vector(1L, 2L, 3L)
      ),
      prospects = Seq(testProspect),
      distinctTimezones = Set()

    )

    it("should generate correct EmailScheduledNew with generated content") {
      val date_time_now = EmailChannelSchedulerFixtures.aDate
      val generatedContent = Some(GeneratedContent("Generated Subject", "Generated Body"))

      (() => srDateTimeUtils.getDateTimeNow())
        .expects()
        .returning(date_time_now)

      (srUuidUtils.generateEmailsScheduledUuid _)
        .expects()
        .returning("test-uuid-12345")
      val result = emailChannelScheduler.generateEmailScheduledNew(
        currentCampaign = testScheduleCampaign,
        nextStep = testStep,
        currentProspect = testProspect,
        currentVariant = testVariant,
        emailSetting = emailSettingCreateEmailSchedule,
        schedulerDateTime = date_time_now,
        generatedContent = generatedContent
      )

      // Verify all fields
      assert(result.campaign_id.contains(testScheduleCampaign.campaign.campaign_id))
      assert(result.step_id.contains(testStep.id))
      assert(result.is_opening_step) // Since this is head step
      assert(result.prospect_id.contains(testProspectObj.id))
      assert(result.prospect_account_id.isEmpty)
      assert(result.scheduled_at == date_time_now)
      assert(result.sender_email_settings_id == senderEmailSettingId)
      assert(result.template_id == testVariant.template_id)
      assert(result.variant_id.contains(testVariant.id))
      assert(result.step_type == testVariant.step_data.step_type)
      assert(result.to_email == testProspectObj.email.get)
      assert(result.campaign_name.contains(testScheduleCampaign.campaign.campaign_name))
    }

    it("should maintain existing behavior when generatedContent is None") {
      val date_time_now = EmailChannelSchedulerFixtures.aDate

      (() => srDateTimeUtils.getDateTimeNow())
        .expects()
        .returning(date_time_now)
        .atLeastTwice()
      (srUuidUtils.generateEmailsScheduledUuid _)
        .expects()
        .returning("test-uuid-12345")
        .twice()
      val result = emailChannelScheduler.generateEmailScheduledNew(
        currentCampaign = testScheduleCampaign,
        nextStep = testStep,
        currentProspect = testProspect,
        currentVariant = testVariant,
        emailSetting = emailSettingCreateEmailSchedule,
        schedulerDateTime = date_time_now,
        generatedContent = None
      )

      // Verify it matches the version without generatedContent parameter
      val resultWithoutParam = emailChannelScheduler.generateEmailScheduledNew(
        currentCampaign = testScheduleCampaign,
        nextStep = testStep,
        currentProspect = testProspect,
        currentVariant = testVariant,
        emailSetting = emailSettingCreateEmailSchedule,
        schedulerDateTime = date_time_now
      )

      assert(result == resultWithoutParam)
    }
  }

  def createTestTask(stepType: CampaignStepType, campaignId: Option[CampaignId] = None): emailChannelScheduler.GenerateScheduleTaskData = {

    val step_data = stepType match {
      case CampaignStepType.AutoEmailMagicContent =>
        CampaignStepData.AutoEmailMagicContentStep(
          step_context = StepContext(
            call_to_action = "Test CTA",
            step_details = "Test Details",
            columns_to_use = List()
          )
        )
      case CampaignStepType.ManualEmailMagicContent =>
        CampaignStepData.ManualEmailMagicContentStep(
          step_context = StepContext(
            call_to_action = "Test CTA",
            step_details = "Test Details",
            columns_to_use = List()
          )
        )
      case CampaignStepType.AutoEmailStep =>
        CampaignStepData.AutoEmailStep(
          subject = "Test Subject",
          body = "Test Body"
        )
      case _ =>
        CampaignStepData.ManualEmailStep(
          subject = "Test Subject",
          body = "Test Body"
        )
    }

    val a = generateScheduleTaskData_2.copy(
      currentCampaign = generateScheduleTaskData_2
        .currentCampaign.copy(
          campaign = generateScheduleTaskData_2.currentCampaign.campaign.copy(campaign_id = campaignId
            .getOrElse(
              CampaignId(
                generateScheduleTaskData_2
                  .currentCampaign.campaign
                  .campaign_id
              )
            ).id,

            ai_generation_context = Some(CampaignAIGenerationContext(
              team_type = "",
              industry = "",
              motive = "",
              solution = "",
              reason_of_reaching_out = "",
              prospects_designation = "",
              language = "",
              tone = "",
              campaign_offer = "",
              max_number_of_steps = 6
            )
          ))

          ),

      currentProspect = generateScheduleTaskData_2.currentProspect.copy(
        prospectForScheduling = generateScheduleTaskData_2.currentProspect.prospectForScheduling.copy(
          prospect = generateScheduleTaskData_2.currentProspect.prospectForScheduling.prospect.copy(
            company = Some("Test_Company")
          )
        )
      ),

      currentVariant = generateScheduleTaskData_2
        .currentVariant.copy(
          step_data = step_data
        )
    )

//    println(s"a -> ${a}")

    a

  }

  describe("Magic content task identification") {
    describe("isMagicContentTask") {
      it("should return true for AutoEmailMagicContent") {
        assert(EmailChannelScheduler.isMagicContentTask(CampaignStepType.AutoEmailMagicContent))
      }

      it("should return true for ManualEmailMagicContent") {
        assert(EmailChannelScheduler.isMagicContentTask(CampaignStepType.ManualEmailMagicContent))
      }

      it("should return false for regular email steps") {
        assert(!EmailChannelScheduler.isMagicContentTask(CampaignStepType.AutoEmailStep))
        assert(!EmailChannelScheduler.isMagicContentTask(CampaignStepType.ManualEmailStep))
      }

      it("should return false for non-email steps") {
        assert(!EmailChannelScheduler.isMagicContentTask(CampaignStepType.LinkedinMessage))
        assert(!EmailChannelScheduler.isMagicContentTask(CampaignStepType.WhatsappMessage))
        assert(!EmailChannelScheduler.isMagicContentTask(CampaignStepType.CallStep))
      }
    }

    describe("splitMagicContentTasks") {


      it("should correctly split mixed tasks") {
        val tasks = Vector(
          createTestTask(CampaignStepType.AutoEmailMagicContent),
          createTestTask(CampaignStepType.ManualEmailStep),
          createTestTask(CampaignStepType.ManualEmailMagicContent),
          createTestTask(CampaignStepType.AutoEmailStep)
        )

        val (magicTasks, regularTasks) = emailChannelScheduler.splitMagicContentTasks(tasks)

        assert(magicTasks.length == 2)
        assert(regularTasks.length == 2)
        assert(magicTasks.forall(t => EmailChannelScheduler.isMagicContentTask(t.currentVariant.step_data.step_type)))
        assert(regularTasks.forall(t => !EmailChannelScheduler.isMagicContentTask(t.currentVariant.step_data.step_type)))
      }

      it("should handle all magic content tasks") {
        val tasks = Vector(
          createTestTask(CampaignStepType.AutoEmailMagicContent),
          createTestTask(CampaignStepType.ManualEmailMagicContent)
        )

        val (magicTasks, regularTasks) = emailChannelScheduler.splitMagicContentTasks(tasks)

        assert(magicTasks.length == 2)
        assert(regularTasks.isEmpty)
      }

      it("should handle all regular tasks") {
        val tasks = Vector(
          createTestTask(CampaignStepType.AutoEmailStep),
          createTestTask(CampaignStepType.ManualEmailStep)
        )

        val (magicTasks, regularTasks) = emailChannelScheduler.splitMagicContentTasks(tasks)

        assert(magicTasks.isEmpty)
        assert(regularTasks.length == 2)
      }

      it("should handle empty task list") {
        val (magicTasks, regularTasks) = emailChannelScheduler.splitMagicContentTasks(Vector.empty)

        assert(magicTasks.isEmpty)
        assert(regularTasks.isEmpty)
      }
    }
  }

  describe("Magic content generation") {
    describe("createTaskContentId") {
      it("should create consistent IDs for the same task") {
        val task = createTestTask(CampaignStepType.AutoEmailMagicContent)
        val id1 = emailChannelScheduler.createTaskContentId(task)
        val id2 = emailChannelScheduler.createTaskContentId(task)

        assert(id1 == id2)
      }

      it("should create different IDs for different tasks") {
        val task1 = createTestTask(CampaignStepType.AutoEmailMagicContent, campaignId = Some(CampaignId(id = 1L)))
        val task2 = createTestTask(CampaignStepType.AutoEmailMagicContent, campaignId = Some(CampaignId(id = 2L)))

        val id1 = emailChannelScheduler.createTaskContentId(task1)
        val id2 = emailChannelScheduler.createTaskContentId(task2)

        assert(id1 != id2)
      }
    }

//    describe("generateMagicContentMap") {
//      it("should return empty map for empty task list") {
//        val result = emailChannelScheduler.generateMagicContentMap(Vector.empty, orgId = OrgId(id = 3))
//
//        result.map { contentMap =>
//          assert(contentMap.isEmpty)
//        }
//      }
//
//      it("should generate content for all magic tasks") {
//        val tasks = Vector(
//          createTestTask(CampaignStepType.AutoEmailMagicContent, campaignId = Some(CampaignId(id = 1L))),
//          createTestTask(CampaignStepType.ManualEmailMagicContent,  campaignId = Some(CampaignId(id = 2L)))
//        )
//
//        (aiHyperPersonalizedService.generateContentForTask (
//          _: StepId,
//          _: CampaignStepData,
//          _: ProspectObject,
//          _: Option[CampaignAIGenerationContext],
//          _: CampaignId,
//          _: EmailSettingId,
//          _: OrgId,
//          _: Seq[PreviousFollowUp],
//          _: Seq[CampaignStepWithChildren],
//        )(
//          _: ExecutionContext,
//          _: WSClient,
//          _: Materializer,
//          _: ActorSystem,
//          _: SRLogger
//        ))
//          .expects(*, *, *, *, *, *, *, *, *, *, *, *, *, *)
//          .returning(Future.successful(("Hi how are you?", "I'm good what about you?")))
//          .anyNumberOfTimes()
//
//        (emailScheduledDAOService.getPreviousSentSteps(
//          _: Long,
//          _: Long,
//          _: TeamId,
//          _: Boolean)(using _: SRLogger))
//          .expects(*, *, *, *, *)
//          .returning(Success(Seq()))
//          .anyNumberOfTimes()
//
//        (campaignDAOService.findOrderedSteps)
//          .expects(1, TeamId(id = 15))
//          .returning(
//          Seq(
//            campaignStepWithChildren
//          ))
//          .anyNumberOfTimes()
//
//        val result = emailChannelScheduler.generateMagicContentMap(tasks, orgId = OrgId(3))
//
//        result.map { contentMap =>
//          assert(contentMap.size == 2)
//          assert(contentMap.values.forall { case (subject, body) =>
//            subject.nonEmpty && body.nonEmpty
//          })
//          assert(contentMap.values.forall { case (subject, body) =>
//            subject == "Hi how are you?" && body == "I'm good what about you?"
//          })
//        }
//      }
//
//      it("should create unique content for each task") {
//        val tasks = Vector(
//          createTestTask(CampaignStepType.AutoEmailMagicContent, campaignId = Some(CampaignId(id = 1L))),
//          createTestTask(CampaignStepType.AutoEmailMagicContent, campaignId = Some(CampaignId(id = 2L)))
//        )
//
//        (aiHyperPersonalizedService.generateContentForTask (
//          _: StepId,
//          _: CampaignStepData,
//          _: ProspectObject,
//          _: Option[CampaignAIGenerationContext],
//          _: CampaignId,
//          _: EmailSettingId,
//          _: OrgId,
//          _: Seq[PreviousFollowUp],
//          _: Seq[CampaignStepWithChildren],
//        )(
//          _: ExecutionContext,
//          _: WSClient,
//          _: Materializer,
//          _: ActorSystem,
//          _: SRLogger
//        ))
//          .expects(*, *, *, *, *, *, *, *, *, *, *, *, *, *)
//          .returning(Future.successful(("Hi how are you?", "I'm good what about you?")))
//          .anyNumberOfTimes()
//
//        (emailScheduledDAOService.getPreviousSentSteps(
//          _: Long,
//          _: Long,
//          _: TeamId,
//          _: Boolean)(using _: SRLogger))
//          .expects(*, *, *, *, *)
//          .returning(Success(Seq()))
//          .anyNumberOfTimes()
//
//        (campaignDAOService.findOrderedSteps)
//          .expects(1, TeamId(id = 15))
//          .returning(
//            Seq(
//              campaignStepWithChildren
//            ))
//          .anyNumberOfTimes()
//
//        val result = emailChannelScheduler.generateMagicContentMap(tasks, orgId = OrgId(3))
//
//        result.map { contentMap =>
//          val taskIds = tasks.map(emailChannelScheduler.createTaskContentId)
//          assert(contentMap.keySet == taskIds.toSet)
//          assert(contentMap.size == tasks.size)
//          assert(contentMap.values.forall { case (subject, body) =>
//            subject == "Hi how are you?" && body == "I'm good what about you?"
//          })
//        }
//      }
//
//      it("should handle large number of tasks") {
//        val tasks = (1 to 100).map { i =>
//          createTestTask(CampaignStepType.AutoEmailMagicContent, campaignId = Some(CampaignId(id = i.toLong)))
//        }.toVector
//
//        (aiHyperPersonalizedService.generateContentForTask (
//          _: StepId,
//          _: CampaignStepData,
//          _: ProspectObject,
//          _: Option[CampaignAIGenerationContext],
//          _: CampaignId,
//          _: EmailSettingId,
//          _: OrgId,
//          _: Seq[PreviousFollowUp],
//          _: Seq[CampaignStepWithChildren],
//        )(
//          _: ExecutionContext,
//          _: WSClient,
//          _: Materializer,
//          _: ActorSystem,
//          _: SRLogger
//        ))
//          .expects(*, *, *, *, *, *, *, *, *, *, *, *, *, *)
//          .returning(Future.successful(("Hi how are you?", "I'm good what about you?")))
//          .anyNumberOfTimes()
//
//        (emailScheduledDAOService.getPreviousSentSteps(
//          _: Long,
//          _: Long,
//          _: TeamId,
//          _: Boolean)(using _: SRLogger))
//          .expects(*, *, *, *, *)
//          .returning(Success(Seq()))
//          .anyNumberOfTimes()
//
//        (campaignDAOService.findOrderedSteps)
//          .expects(1, TeamId(id = 15))
//          .returning(
//            Seq(
//              campaignStepWithChildren
//            ))
//          .anyNumberOfTimes()
//
//        val result = emailChannelScheduler.generateMagicContentMap(tasks, orgId = OrgId(3))
//
//        result.map { contentMap =>
//          assert(contentMap.size == 100)
//          assert(contentMap.values.forall { case (subject, body) =>
//            subject.nonEmpty && body.nonEmpty
//          })
//
//          assert(contentMap.values.forall { case (subject, body) =>
//            subject == "Hi how are you?" && body == "I'm good what about you?"
//          })
//        }
//      }
//    }

    describe("Content Validation") {
      it("should reject empty content") {
        val task = createTestTask(CampaignStepType.AutoEmailMagicContent)

        val result = emailChannelScheduler.validateGeneratedContent(("", ""), task)

        recoverToSucceededIf[ContentValidationError](result)
      }

      it("should reject oversized content") {
        val task = createTestTask(CampaignStepType.AutoEmailMagicContent)
        val longSubject = "a" * (100 + 1)
        val longBody = "a" * (5000 + 1)

        val result1 = emailChannelScheduler.validateGeneratedContent((longSubject, "body"), task)
        val result2 = emailChannelScheduler.validateGeneratedContent(("subject", longBody), task)

        recoverToSucceededIf[ContentValidationError](result1)
        recoverToSucceededIf[ContentValidationError](result2)
      }
    }

    describe("Context Preparation") {
      it("should extract prospect info correctly") {
        val task = createTestTask(CampaignStepType.AutoEmailMagicContent)
        val prospectInfo = ProspectObject.extractAllFields(task.currentProspect.prospectForScheduling.prospect)

        assert(prospectInfo.standardFields.firstName.nonEmpty && prospectInfo.standardFields.firstName.get.trim().nonEmpty)
        assert(prospectInfo.standardFields.lastName.nonEmpty && prospectInfo.standardFields.lastName.get.trim().nonEmpty)
        assert(prospectInfo.standardFields.company.nonEmpty && prospectInfo.standardFields.company.get.trim().nonEmpty)


      }


    }

  }

}
