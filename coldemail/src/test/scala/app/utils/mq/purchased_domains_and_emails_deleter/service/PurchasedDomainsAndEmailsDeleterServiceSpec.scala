package app.utils.mq.purchased_domains_and_emails_deleter.service

import api.accounts.models.AccountId
import api.accounts.{AccountService, TeamId}
import api.email_infra_integrations.dao.EmailInfraDAO
import api.email_infra_integrations.maildoso.service.MailDosoService
import api.email_infra_integrations.models.PlatformType.MAILDOSO
import api.email_infra_integrations.models.PurchaseDomainsAndEmailsStatus
import api.email_infra_integrations.services.EmailInfraService
import api.email_infra_integrations.zapmail.ZapMailAPI
import api.emails.EmailSettingDAO
import api.emails.services.{EmailAccountService, EmailSettingService}
import api.prospects.dao_service.ProspectDAOService
import api.rep_mail_servers.services.SrMailServerService
import io.smartreach.esp.api.emails.EmailSettingId
import org.apache.pekko.actor.ActorSystem
import org.apache.pekko.stream.Materializer
import org.joda.time.DateTime
import org.scalamock.scalatest.AsyncMockFactory
import org.scalatest.funspec.AsyncFunSpec
import play.api.libs.ws.WSClient
import play.api.libs.ws.ahc.AhcWSClient
import utils.helpers.LogHelpers
import utils.mq.purchased_domains_and_emails_deleter.service.PurchasedDomainsAndEmailsDeleterService
import utils.{PlanLimitService, SRLogger}
import utils.mq.purchased_domains_and_emails_deleter.{DomainUuidWithPlatformType, MqPurchasedDomainsDeleter, MqPurchasedEmailsDeleter}
import utils.testapp.TestAppExecutionContext

import scala.concurrent.{ExecutionContext, Future}
import scala.concurrent.impl.Promise
import scala.util.{Failure, Success}

class PurchasedDomainsAndEmailsDeleterServiceSpec extends AsyncFunSpec with AsyncMockFactory {
  implicit lazy val system: ActorSystem = TestAppExecutionContext.actorSystem
  implicit lazy val materializer: Materializer = TestAppExecutionContext.actorMaterializer
  implicit lazy val wsClient: AhcWSClient = TestAppExecutionContext.wsClient
  given Logger: SRLogger = new SRLogger("some_log_re_id")
  val mailDosoService = mock[MailDosoService]
  val emailInfraDao = mock[EmailInfraDAO]
  val emailSettingService: EmailSettingService = mock[EmailSettingService]
  val zapmailApi: ZapMailAPI = mock[ZapMailAPI]

  val purchasedDomainsAndEmailsDeleterService = new PurchasedDomainsAndEmailsDeleterService(
    maildosoService = mailDosoService,
    zapMailAPI =  zapmailApi,
    emailInfraDao = emailInfraDao,
    emailSettingService = emailSettingService
  )

  describe("unit tests for PurchasedDomainsAndEmailsDeleterService.deleteDomains") {

    it("should be able to delete domains and emails") {

      val platform_domain_ids = List("1", "2")
      val team_id = TeamId(11L)

      (emailInfraDao.getPlatformDomainIdsByTeam(_: List[String], _: TeamId))
        .expects(platform_domain_ids, team_id)
        .returns(Success(List("1", "2")))

      (mailDosoService.deleteDomainsPurchased(_: List[Long])(_: ExecutionContext, _: WSClient, _: SRLogger))
        .expects(List(1L, 2L), *, *, *)
        .returns(Future("a.com"))

      (emailInfraDao.updatePurchasedDomainStatusByPurchaseDomainUuids(
        _: List[String],
        _: PurchaseDomainsAndEmailsStatus,
        _: TeamId,
        _: Option[AccountId],
        _: Option[DateTime]
      ))
        .expects(platform_domain_ids, PurchaseDomainsAndEmailsStatus.DELETED, team_id, None, None)
        .returns(Success(List("a.com")))

      val res = purchasedDomainsAndEmailsDeleterService
        .deleteDomains(
          platform_domain_ids.map(
            domainId => DomainUuidWithPlatformType(
              domainUuid = domainId, platformType = MAILDOSO
            )
          ),
          team_id
        )

      res
        .map(_ => assert(true))
        .recover(e =>
          {

            println(LogHelpers.getStackTraceAsString(e))
            assert(false)

          }
        )

    }

    it("should return an empty list when platformDomainIds is empty") {
      val platform_domain_ids = List.empty[String]
      val team_id = TeamId(11L)

      val res = purchasedDomainsAndEmailsDeleterService
        .deleteDomains(platform_domain_ids.map(
          domainId => DomainUuidWithPlatformType(
            domainUuid = domainId, platformType = MAILDOSO
          )
        ), team_id)

      res.map(result => assert(result.isEmpty))
    }

    it("should successfully delete domains and update their status when all domains are valid") {
      val platform_domain_ids = List("1", "2")
      val team_id = TeamId(11L)

      (emailInfraDao.getPlatformDomainIdsByTeam(_: List[String], _: TeamId))
        .expects(platform_domain_ids.map(_.toString), team_id)
        .returns(Success(List("1", "2")))

      (mailDosoService.deleteDomainsPurchased(_: List[Long])(_: ExecutionContext, _: WSClient, _: SRLogger))
        .expects(platform_domain_ids.map(_.toLong), *, *, *)
        .returns(Future.successful(("")))

      (emailInfraDao.updatePurchasedDomainStatusByPurchaseDomainUuids(
        _: List[String],
        _: PurchaseDomainsAndEmailsStatus,
        _: TeamId,
        _: Option[AccountId],
        _: Option[DateTime]
      ))
        .expects(platform_domain_ids, PurchaseDomainsAndEmailsStatus.DELETED, team_id, None, None)
        .returns(Success(platform_domain_ids))

      val res = purchasedDomainsAndEmailsDeleterService
        .deleteDomains(platform_domain_ids.map(
          domainId => DomainUuidWithPlatformType(
            domainUuid = domainId, platformType = MAILDOSO
          )
        ), team_id)

      res.map(result => assert(result == platform_domain_ids))
    }
/*
  26-03-2025
  We are commenting this as we no longer throwing the exception , instead we are marking it as critical error
  Reasons:
  - throwing exception in maildoso deletion flow will impact the zapmail deletion flow
*/
//    it("should throw an exception when some domains do not belong to the team") {
//      val platform_domain_ids = List("1", "2", "3")
//      val team_id = TeamId(11L)
//
//      (emailInfraDao.getPlatformDomainIdsByTeam(_: List[String], _: TeamId))
//        .expects(platform_domain_ids.map(_.toString), team_id)
//        .returns(Success(List("1", "2")))
//
//      val res = purchasedDomainsAndEmailsDeleterService
//        .deleteDomains(platform_domain_ids.map(
//          domainId => DomainUuidWithPlatformType(
//            domainUuid = domainId, platformType = MAILDOSO
//          )
//        ), team_id)
//
//      recoverToSucceededIf[Exception](res)
//    }
//
//    it("should propagate the exception when deleteDomainsPurchased fails") {
//      val platform_domain_ids = List("1", "2")
//      val team_id = TeamId(11L)
//
//      (emailInfraDao.getPlatformDomainIdsByTeam(_: List[String], _: TeamId))
//        .expects(platform_domain_ids.map(_.toString), team_id)
//        .returns(Success(List("1", "2")))
//
//      (mailDosoService.deleteDomainsPurchased(_: List[Long])(_: ExecutionContext, _: WSClient, _: SRLogger))
//        .expects(platform_domain_ids.map(_.toLong), *, *, *)
//        .returns(Future.failed(new RuntimeException("deleteDomainsPurchased failed")))
//
//      val res = purchasedDomainsAndEmailsDeleterService
//        .deleteDomains(platform_domain_ids.map(
//          domainId => DomainUuidWithPlatformType(
//            domainUuid = domainId, platformType = MAILDOSO
//          )
//        ), team_id)
//
//      recoverToSucceededIf[RuntimeException](res)
//    }

    it("should propagate the exception when updatePurchasedDomainStatus fails") {
      val platform_domain_ids = List("1", "2")
      val team_id = TeamId(11L)

      (emailInfraDao.getPlatformDomainIdsByTeam(_: List[String], _: TeamId))
        .expects(platform_domain_ids.map(_.toString), team_id)
        .returns(Success(List("1", "2")))

      (mailDosoService.deleteDomainsPurchased(_: List[Long])(_: ExecutionContext, _: WSClient, _: SRLogger))
        .expects(platform_domain_ids.map(_.toLong), *, *, *)
        .returns(Future.successful(("")))

      (emailInfraDao.updatePurchasedDomainStatusByPurchaseDomainUuids(
        _: List[String],
        _: PurchaseDomainsAndEmailsStatus,
        _: TeamId,
        _: Option[AccountId],
        _: Option[DateTime]
      ))
        .expects(platform_domain_ids.map(_.toString), PurchaseDomainsAndEmailsStatus.DELETED, team_id, None, None)
        .returns(Failure(new RuntimeException("updatePurchasedDomainStatus failed")))

      val res = purchasedDomainsAndEmailsDeleterService
        .deleteDomains(platform_domain_ids.map(
          domainId => DomainUuidWithPlatformType(
            domainUuid = domainId, platformType = MAILDOSO
          )
        ), team_id)

      recoverToSucceededIf[RuntimeException](res)
    }

  }

  describe("unit tests for PurchasedDomainsAndEmailsDeleterService.deleteEmails") {

    it("should successfully delete an email") {
      val emailSettingId = EmailSettingId(1)
      val teamId = TeamId(11L)
      val platformEmailId = 123L

      (emailSettingService.getPlatformEmailIdFromEmailSettingId (_: EmailSettingId, _: TeamId))
        .expects(emailSettingId, teamId)
        .returns(Success(Some(platformEmailId.toString)))

      (mailDosoService.deleteEmailsPurchased(_: List[Long])(_: ExecutionContext, _: WSClient, _: SRLogger))
        .expects(List(platformEmailId), *, *, *)
        .returns(Future.successful(("")))

      (emailSettingService.deleteEmailSetting (_: EmailSettingId, _: TeamId)(using _: SRLogger))
        .expects(emailSettingId, teamId, *)
        .returns(Success(()))

      val res = purchasedDomainsAndEmailsDeleterService
        .deleteMaildosoEmails(emailSettingId, teamId)

      res.map(result => assert(result == ())).recover { case e =>

        assert(false)

      }
    }

    it("should fail when getPlatformEmailIdFromEmailSettingId returns a failure") {
      val emailSettingId = EmailSettingId(1)
      val teamId = TeamId(11L)

      (emailSettingService.getPlatformEmailIdFromEmailSettingId (_: EmailSettingId, _: TeamId))
        .expects(emailSettingId, teamId)
        .returns(Failure(new RuntimeException("Failed to get platformEmailId")))

      val res = purchasedDomainsAndEmailsDeleterService
        .deleteMaildosoEmails(emailSettingId, teamId)

      recoverToSucceededIf[RuntimeException](res)
    }

    it("should fail when getPlatformEmailIdFromEmailSettingId returns None") {
      val emailSettingId = EmailSettingId(1)
      val teamId = TeamId(11L)

      (emailSettingService.getPlatformEmailIdFromEmailSettingId (_: EmailSettingId, _: TeamId))
        .expects(emailSettingId, teamId)
        .returns(Success(None))

      val res = purchasedDomainsAndEmailsDeleterService
        .deleteMaildosoEmails(emailSettingId, teamId)

      recoverToSucceededIf[Exception](res)
    }

    it("should fail when getPlatformEmailIdFromEmailSettingId returns an invalid platformEmailId") {
      val emailSettingId = EmailSettingId(1)
      val teamId = TeamId(11L)

      (emailSettingService.getPlatformEmailIdFromEmailSettingId (_: EmailSettingId, _: TeamId))
        .expects(emailSettingId, teamId)
        .returns(Success(Some("invalid_id")))

      val res = purchasedDomainsAndEmailsDeleterService
        .deleteMaildosoEmails(emailSettingId, teamId)

      recoverToSucceededIf[Exception](res)
    }

    it("should fail when deleteEmailsPurchased fails") {
      val emailSettingId = EmailSettingId(1)
      val teamId = TeamId(11L)
      val platformEmailId = 123L

      (emailSettingService.getPlatformEmailIdFromEmailSettingId (_: EmailSettingId, _: TeamId))
        .expects(emailSettingId, teamId)
        .returns(Success(Some(platformEmailId.toString)))

      (mailDosoService.deleteEmailsPurchased(_: List[Long])(_: ExecutionContext, _: WSClient, _: SRLogger))
        .expects(List(platformEmailId), *, *, *)
        .returns(Future.failed(new RuntimeException("Failed to delete emails")))

      val res = purchasedDomainsAndEmailsDeleterService
        .deleteMaildosoEmails(emailSettingId, teamId)

      recoverToSucceededIf[RuntimeException](res)
    }

    it("should fail when deleteEmailSetting fails") {
      val emailSettingId = EmailSettingId(1)
      val teamId = TeamId(11L)
      val platformEmailId = 123L

      (emailSettingService.getPlatformEmailIdFromEmailSettingId (_: EmailSettingId, _: TeamId))
        .expects(emailSettingId, teamId)
        .returns(Success(Some(platformEmailId.toString)))

      (mailDosoService.deleteEmailsPurchased(_: List[Long])(_: ExecutionContext, _: WSClient, _: SRLogger))
        .expects(List(platformEmailId), *, *, *)
        .returns(Future.successful(("")))

      (emailSettingService.deleteEmailSetting(_: EmailSettingId, _: TeamId)(using _: SRLogger))
        .expects(emailSettingId, teamId, *)
        .returns(Failure(new RuntimeException("Failed to delete email setting")))

      val res = purchasedDomainsAndEmailsDeleterService
        .deleteMaildosoEmails(emailSettingId, teamId)

      recoverToSucceededIf[RuntimeException](res)
    }
  }

}
