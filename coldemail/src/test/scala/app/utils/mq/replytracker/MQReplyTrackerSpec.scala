package app.utils.mq.replytracker

import org.apache.pekko.actor.ActorSystem
import api.CacheServiceJedis
import api.accounts.email.models.EmailServiceProvider
import api.accounts.models.{AccountId, OrgId}
import api.accounts.{AccountUuid, ReplyHandling, TeamId}
import api.emails.models.EmailSettingUuid
import api.emails.{EmailSetting, EmailSettingDAO}
import api.team.TeamUuid
import io.smartreach.esp.api.emails.EmailSettingId
import org.joda.time.DateTime
import org.scalamock.scalatest.AsyncMockFactory
import org.scalatest.funspec.AsyncFunSpec
import play.api.libs.ws.WSClient
import play.api.libs.ws.ahc.AhcWSClient
import utils.SRLogger
import utils.cache_utils.model.CampaignUseStatusForEmailSetting
import utils.cronjobs.email_setting_deletion.model.EmailSettingStatus
import utils.email.EmailService
import utils.mq.channel_scheduler.{MqEmailChannelScheduler, MqEmailChannelSchedulerMsg}
import utils.mq.replytracker.mq.{MQReplyTracker, MQReplyTrackerMessage, MQReplyTrackerPublisher}
import utils.mq.replytracker.ReplyTrackerService
import utils.testapp.TestAppExecutionContext
import utils.uuid.SrUuidUtils

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}

class MQReplyTrackerSpec extends AsyncFunSpec with AsyncMockFactory {

  val emailService = mock[EmailService]
  val emailSettingDAO = mock[EmailSettingDAO]
//  val mqEmailSchedulerV2 = mock[MqEmailChannelScheduler]

  val replyTrackerService = mock[ ReplyTrackerService]
  val srUuidUtils = mock[SrUuidUtils]

  val mqReplyTrackerPublisher: MQReplyTrackerPublisher = mock[MQReplyTrackerPublisher]
  val mqReplyTracker = new MQReplyTracker(
    srUuidUtils = srUuidUtils,
    emailService = emailService,
    emailSettingDAO = emailSettingDAO,
//    mqEmailSchedulerV2 = mqEmailSchedulerV2,
    replyTrackerService = replyTrackerService,
    mqReplyTrackerPublisher = mqReplyTrackerPublisher
  )


  implicit lazy val system: ActorSystem = TestAppExecutionContext.actorSystem
  implicit lazy val wSClient: AhcWSClient = TestAppExecutionContext.wsClient
  implicit lazy val actorContext: ExecutionContext = system.dispatcher

  given Logger: SRLogger = new SRLogger("Unit test")
  val Error = new Throwable("ERROR")

  val  mqReplyTrackerMessage =  MQReplyTrackerMessage(
    emailSettingId = 1,
    senderMessageIdSuffix = "some_suffix",
    serviceProvider = EmailServiceProvider.GMAIL_API,
    providedBy = None
  )
  val email = "<EMAIL>"
  val email_address_host = "company.com"

  val emailMessageIdSuffix = "local@smartreachio"
  val emailSetting = EmailSetting(
    id = Some(EmailSettingId(emailSettingId = 123)),
    org_id = OrgId(id = 123567),
    owner_id = AccountId(id = 47),
    team_id = TeamId(id = 789),
    uuid = Some(EmailSettingUuid("test_uuid")),
    owner_uuid = AccountUuid("owner_uuid"),
    team_uuid = TeamUuid("team_uuid"),
    message_id_suffix = emailMessageIdSuffix,

    email = email,
    email_address_host = email_address_host,

    service_provider = EmailServiceProvider.OTHER,
      domain_provider =None,
    via_gmail_smtp = None,

    owner_name = "Ownername dummy",
    sender_name = "Test Sender Name",
    first_name = "John",
    last_name = "Doe",

    cc_emails = None,
    bcc_emails = None,

    smtp_username = Some(email),
    smtp_password = Some("thisispassword"),
    smtp_host = Some("this is the smtp host"),
    smtp_port = Some(12345),

    imap_username = Some(email),
    imap_password = Some("thisisimappassword"),
    imap_host = Some("imap.host.com"),
    imap_port = Some(993),

    oauth2_access_token = None,
    oauth2_refresh_token = None,
    oauth2_token_type = None,
    oauth2_token_expires_in = None,
    oauth2_access_token_expires_at = None,

    // for mailgun
    email_domain = None,
    api_key = None,
    mailgun_region = None,

    quota_per_day = 3,

    reply_handling = ReplyHandling.PAUSE_SPECIFIC_CAMPAIGN_ON_REPLY,
    last_read_for_replies = None,
    latest_email_scheduled_at = None,

    error = None,
    error_reported_at = None,
    paused_till = None,

    signature = "MySignature",

    created_at = Some(DateTime.now()),

    current_prospect_sent_count_email = 3,

    default_tracking_domain = "company.com",
    default_unsubscribe_domain = "company.com",
    rep_tracking_host_id = 123,
    tracking_domain_host = None,
    custom_tracking_domain = None,
    custom_tracking_cname_value = None,
    custom_tracking_domain_is_verified = None,
    custom_tracking_domain_is_ssl_enabled = None,

    rep_mail_server_id = 123,
    rep_mail_server_public_ip = "0.0.0.0",
    rep_mail_server_host = "randomserverhost.com",
    rep_mail_server_reverse_dns = None,

    min_delay_seconds = 30,
    max_delay_seconds = 120,
      tag = None,
    campaign_use_status_for_email_setting = CampaignUseStatusForEmailSetting.IsNotAssignedToAnyCampaign,
    show_rms_ip_in_frontend = false

  )

  describe("processMessage") {


    it("emailService.readEmail failed") {

      (() => srUuidUtils.generateMqRequestLogId())
        .expects()
        .returning("abcd_efgh_ijkl")

      (emailSettingDAO.find(_: Long, _: EmailSettingStatus))
        .expects(1, EmailSettingStatus.Active)
        .returning(Some(emailSetting))


      (emailService.readEmail (_: EmailSetting, _: String, _: DateTime, _: Option[DateTime], _: Boolean)( _: ExecutionContext, _: SRLogger, _: WSClient))
        .expects(emailSetting, "abcd_efgh_ijkl", * , None, true,  *, *, *)
        .returning(Future.failed(Error))
      (replyTrackerService.acquireLockOnInboxForRead(_: Long, _: SRLogger))
        .expects(1, *)
        .returning( Success(true))
      (replyTrackerService.releaseLockOnInboxForRead(_: Long,  _: SRLogger))
        .expects(1, *)
        .returning(Success(true))

      mqReplyTracker.processMessage(
        msg = mqReplyTrackerMessage
      ).map{result =>
        Logger.info(s"result_____________________$result")
        assert(false)
      }.recover{case e =>
      assert(e == Error)
      }
    }



    it("emailSettingDAO.sendingEmailAccountsForScheduling failed") {


      (() => srUuidUtils.generateMqRequestLogId())
        .expects()
        .returning("abcd_efgh_ijkl")

      (replyTrackerService.acquireLockOnInboxForRead(_: Long, _: SRLogger))
        .expects(1, *)
        .returning( Success(true))
      (emailSettingDAO.find(_: Long, _: EmailSettingStatus))
        .expects(1, EmailSettingStatus.Active)
        .returning(Some(emailSetting))


      (emailService.readEmail (_: EmailSetting, _: String, _: DateTime, _: Option[DateTime], _: Boolean)( _: ExecutionContext, _: SRLogger, _: WSClient))
        .expects(emailSetting, "abcd_efgh_ijkl", *, None, true, *, *, *)
        .returning(Future(emailSetting, 2, 3, Seq(4)))
      (replyTrackerService.releaseLockOnInboxForRead(_: Long,  _: SRLogger))
        .expects(1, *)
        .returning(Success(true))

//      (emailSettingDAO.sendingEmailAccountsForScheduling)
//      .expects(List(4))
//        .returning(Failure(Error))

      mqReplyTracker.processMessage(
        msg = mqReplyTrackerMessage
      ).map{result =>
        Logger.info(s"result_____________________$result")
        assert(result == true)
      }.recover(e => {
        assert(false)
      })
    }



    it("mqEmailSchedulerV2.publish failed") {

      (() => srUuidUtils.generateMqRequestLogId())
        .expects()
        .returning("abcd_efgh_ijkl")


      (replyTrackerService.acquireLockOnInboxForRead(_: Long, _: SRLogger))
        .expects(1, *)
        .returning( Success(true))

      (emailSettingDAO.find(_: Long, _: EmailSettingStatus))
        .expects(1, EmailSettingStatus.Active)
        .returning(Some(emailSetting))



      (emailService.readEmail(_: EmailSetting, _: String, _: DateTime, _: Option[DateTime], _: Boolean)(_: ExecutionContext, _: SRLogger, _: WSClient))
        .expects(emailSetting,"abcd_efgh_ijkl", *, None, true, *, *, *)
        .returning(Future(emailSetting, 2, 3, Seq(4)))

//      (emailSettingDAO.sendingEmailAccountsForScheduling)
//        .expects(List(4))
//        .returning(Success(Seq(4)))
//
//      (mqEmailSchedulerV2.publish)
//      .expects(MqEmailChannelSchedulerMsg(teamId = 789L, emailSettingId = 4))
//        .returning(Failure(Error))

      (replyTrackerService.releaseLockOnInboxForRead(_: Long,  _: SRLogger))
        .expects(1, *)
        .returning(Success(true))

      mqReplyTracker.processMessage(
        msg = mqReplyTrackerMessage
      ).map{result =>
        Logger.info(s"result_____________________$result")
        assert(result == true)
      }.recover(e => {
        assert(false)
      })
    }



        it("all pass") {

          (() => srUuidUtils.generateMqRequestLogId())
            .expects()
            .returning("abcd_efgh_ijkl")


          (replyTrackerService.acquireLockOnInboxForRead(_: Long, _: SRLogger))
            .expects(1, *)
            .returning( Success(true))


          (emailSettingDAO.find(_: Long, _: EmailSettingStatus))
            .expects(1, EmailSettingStatus.Active)
            .returning(Some(emailSetting))


          (emailService.readEmail(_: EmailSetting, _: String, _: DateTime, _: Option[DateTime], _: Boolean)(_: ExecutionContext, _: SRLogger, _: WSClient))
            .expects(emailSetting, "abcd_efgh_ijkl", *, None, true, *, *, *)
            .returning(Future(emailSetting, 2, 3, Seq(4)))

//          (emailSettingDAO.sendingEmailAccountsForScheduling)
//            .expects(List(4))
//            .returning(Success(Seq(4)))
//
//          (mqEmailSchedulerV2.publish)
//            .expects(MqEmailChannelSchedulerMsg(teamId = 789L, emailSettingId = 4))
//            .returning(Success(()))
          
          (replyTrackerService.releaseLockOnInboxForRead(_: Long,  _: SRLogger))
            .expects(1, *)
            .returning(Success(true))


          mqReplyTracker.processMessage(
            msg = mqReplyTrackerMessage
          ).map{result =>
            Logger.info(s"result_____________________$result")
            assert(result == true )
          }.recover(e => {
            assert(false)
          })
        }





  }

}
