package app.utils.mq.webhook

import org.apache.pekko.actor.ActorSystem
import api.accounts.{Account, AccountAccess, AccountMetadata, AccountService, AccountType, AccountUuid, OrgCountData, OrgMetadata, OrgPlan, OrgSettings, OrganizationRole, OrganizationWithCurrentData, ProspectCategoriesInDB, ReplyHandling, RolePermissionDataDAOV2, RolePermissionDataV2, TeamAccount, TeamAccountRole, TeamId, TeamMember, TeamMemberLite}
import api.accounts.models.AccountProfileInfo
import api.calendar_app.models.CalendarAccountData
import api.campaigns.services.CampaignService
import api.columns.ProspectColumnDef
import api.integrations.services.TIntegrationCRMService
import api.integrations.{HubSpotOAuth, IntegrationTPAccessTokenResponse, TIntegrationCRMTrait}
import api.prospects.ProspectService
import api.prospects.models.ProspectCategoryRank
import api.sr_audit_logs.models.EventType
import api.team.TeamUuid
import api.triggers.{HandleCRMContactDataArguments, IntegrationModuleType, IntegrationType, SRTriggerActionType, SRTriggerAllowedCombos, Trigger, TriggerAction, TriggerInDB}
import app.test_fixtures.accounts.OrgCountDataFixture
import app.test_fixtures.organizationa.{OrgMetadataFixture, OrgPlanFixture}
import org.joda.time.DateTime
import org.scalamock.scalatest.AsyncMockFactory
import org.scalatest.funspec.AsyncFunSpec
import play.api.libs.json.JsValue
import play.api.libs.ws.{WSClient, WSResponse}
import play.api.libs.ws.ahc.AhcWSClient
import utils.SRLogger
import utils.email_notification.service.EmailNotificationService
import utils.mq.do_not_contact.{MQDoNotContactConsumer, MQDoNotContactPublisher}
import utils.mq.webhook.HandleSyncTriggerEventService
import utils.testapp.TestAppExecutionContext
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

class HandleSyncTriggerEventServiceSpec extends AsyncFunSpec with AsyncMockFactory {


  implicit lazy val system: ActorSystem = TestAppExecutionContext.actorSystem
  implicit lazy val wSClient: AhcWSClient = TestAppExecutionContext.wsClient
  implicit lazy val actorContext: ExecutionContext = system.dispatcher

  given Logger: SRLogger = new SRLogger("Unit test")
  val Error = new Throwable("ERROR")

  val team_id: Long = 2
  val accountId: Long = 2
  val prospect_id = 4L

  val triggerDAO = mock[Trigger]
  val emailNotificationService = mock[EmailNotificationService]
  val campaignService = mock[CampaignService]
  val prospectService = mock[ProspectService]
  val accountService = mock[AccountService]
  val mqDoNotContactPublisher = mock[MQDoNotContactPublisher]
  val prospectColumnDef = mock[ProspectColumnDef]
  val srTriggerAllowedCombos = mock[SRTriggerAllowedCombos]
  val hubSpotOAuth = mock[HubSpotOAuth]
  val tIntegrationCRMService = mock[TIntegrationCRMService]
  val rolePermissionDataDAOV2 = mock[RolePermissionDataDAOV2]
  val srRollingUpdateCoreService = mock[SrRollingUpdateCoreService]

  val handleSyncTriggerEventService = new HandleSyncTriggerEventService(
    triggerDAO = triggerDAO,
    emailNotificationService = emailNotificationService,
    campaignService = campaignService,
    prospectService = prospectService,
    accountService = accountService,
    mqDoNotContactPublisher = mqDoNotContactPublisher,
    srTriggerAllowedCombos = srTriggerAllowedCombos,
    prospectColumnDef = prospectColumnDef,
    tIntegrationCRMService = tIntegrationCRMService,
    srRollingUpdateCoreService = srRollingUpdateCoreService
  )

  val crmIntegrationService = mock[TIntegrationCRMTrait]
  val triggerId = 1
  val triggerAction = TriggerAction(
    action_type = SRTriggerActionType.COMPLETE_CAMPAIGN_FOR_PROSPECT,
    action_app_type = IntegrationType.ZOHO,
    fields = None,
    campaign_id = None,
    ignore_prospects_active_in_other_campaigns = None,
    ignore_prospects_in_other_campaigns = None,
    tags = None,
    create_contact_or_lead_if_not_exists = None,
    prospect_category_id_custom = None
  )

  val triggerInDB = TriggerInDB(
    id = 1,
    owner_id = 1,
    owner_name = "owner",
    team_id = team_id,
    campaign_id = Some(1),
    label = "someLabel",
    event = Some(EventType.CREATED_PROSPECT_IN_SMARTREACH),
    event_app_type = Some(IntegrationType.ZOHO),
    tp_filter_id = Some("some_tp_filter_id"),
    conditions = None,
    actions = Some(Seq(triggerAction)),
    created_at = DateTime.now(),
    active = true,
    shared_with_team = true,
    error = None,
    error_at = None,
    error_retries_count = 5,
    last_ran_at = None,
    in_queue_for_sync = true,
    pushed_to_queue_for_sync_at = None
  )
  val orgId: Long = 28


  val counts: OrgCountData = OrgCountDataFixture.orgCountData_default

  val orgPlan = OrgPlanFixture.orgPlanFixture

  val orgMetadata = OrgMetadataFixture.orgMetadataFixture2

  val orgSettings = OrgSettings(
    enable_ab_testing = true,
    disable_force_send = false,
    bulk_sender = false,
    allow_2fa = false,
    show_2fa_setting = false,
    enforce_2fa = false,
    allow_native_crm_integration = false,
    agency_option_allow_changing = false,
    agency_option_show = false)

  val org = OrganizationWithCurrentData(
    id = orgId,
    name = "Animesh",
    owner_account_id = accountId,
    counts = counts,
    settings = orgSettings,
    plan = orgPlan,
    is_agency = true,
    trial_ends_at = DateTime.now().plusDays(10),
    error = None,
    error_code = None,
    paused_till = None,
    errors = Seq(),
    warnings = Seq(),
    via_referral = true,
    org_metadata = orgMetadata
  )
  val accountMetadata: AccountMetadata = AccountMetadata(
    // account_ui_version = None,
    is_profile_onboarding_done = None
  )
  val profile: AccountProfileInfo = AccountProfileInfo(
    first_name = "Animesh",
    last_name = "Kumar",
    company = Some("AnimeshKumar"),
    timezone = Some("IN"),
    country_code = Some("IN"),
    mobile_country_code = Some("+91"),
    mobile_number = Some(9515253545L),
    twofa_enabled = false,
    has_gauthenticator = false,
    weekly_report_emails = Some("<EMAIL>"),
    scheduled_for_deletion_at = None,
    onboarding_phone_number = Some("+************")
  )


  val teamMember = TeamMember(
    team_id = team_id,
    team_name = "team_name",
    user_id = accountId,
    ta_id = 6,
    first_name = None,
    last_name = None,
    email = "<EMAIL>",
    team_role = TeamAccountRole.ADMIN,
    api_key = Some("some_api_key"),
    zapier_key = Some("zapier_key")
  )

  val teamAccount = TeamAccount(
    team_id = team_id,
    org_id = orgId,
    role_from_db = None,
    role = None,
    active = true,
    is_actively_used = true,
    team_name = "team_name",
    total_members = 5,
    access_members = Seq(teamMember),
    all_members = Seq(),
    prospect_categories_custom = Seq(),
    max_emails_per_prospect_per_day = 100,
    max_emails_per_prospect_per_week = 1000,
    max_emails_per_prospect_account_per_day = 97,
    max_emails_per_prospect_account_per_week = 497,
    reply_handling = ReplyHandling.PAUSE_SPECIFIC_CAMPAIGN_ON_REPLY,
    created_at = DateTime.now(),
    selected_calendar_data = None,
    team_uuid = TeamUuid("uuid")
  )

  val accountAdmin: Account = Account(
    id = AccountUuid("account_uuid"),
    internal_id = accountId,
    email = "<EMAIL>",
    email_verification_code = None,
    email_verification_code_created_at = None,
    created_at = DateTime.now().minusDays(1000),
    first_name = Some("Animesh"),
    last_name = Some("Kumar"),
    company = Some("AK"),
    timezone = None,
    profile = profile,
    org_role = Some(OrganizationRole.OWNER),
    teams = Seq(teamAccount),
    account_type = AccountType.AGENCY,
    org = org,
    active = true,
    email_notification_summary = "dSFA",
    account_metadata = accountMetadata,
    email_verified = true,
    signupType = None,
    account_access = AccountAccess(
      inbox_access = false
    ),
    calendar_account_data = None

  )

  describe("_handleEventAction") {

    it("Case doesnt match anything") {

      handleSyncTriggerEventService._handleEventAction(
        msg = triggerId,
        teamId = team_id,
        accountId = accountId,
        t = triggerInDB,
        ac = triggerAction,
        parentEventTypeTrigger = EventType.CREATED_PROSPECT_IN_SMARTREACH,
      ).map { result =>
        Logger.info(s"result________________$result")

        assert(result == Right(0))
      }.recover { case e =>

        assert(false)
      }
    }

    it("case match with HUBSPOT_PROSPECT_SYNC and CREATE_OR_UPDATE_PROSPECT_IN_SMARTREACH") {
      trait IntegrationsCRMTestTrait extends TIntegrationCRMTrait {
        val name = IntegrationType.HUBSPOT
      }
      val crmIntegrationService = mock[IntegrationsCRMTestTrait]
      (srTriggerAllowedCombos.getIntegrationServiceByEvent)
        .expects(EventType.HUBSPOT_PROSPECT_SYNC)
        .returning(Success(crmIntegrationService.name))

      val integrationTPAccessTokenResponse = IntegrationTPAccessTokenResponse.FullTokenData(
        access_token = "access_token",
        refresh_token = None,
        expires_in = Some(1),
        expires_at = Some(DateTime.now().plusDays(1)),
        token_type = Some("token_type"),
        api_domain = Some("api_domain"),
        is_sandbox = Some(false)
      )

      (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken(_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
        .expects(team_id, IntegrationType.HUBSPOT, *, *, *)
        .returning(Future.successful(Right(integrationTPAccessTokenResponse)))

      (accountService.find(_: Long)(_: SRLogger))
        .expects(accountId, *)
        .returning(Success(accountAdmin))

      (prospectColumnDef.findCustomColumns)
        .expects(team_id)
        .returning(Seq())

      (tIntegrationCRMService.getRecentContacts
      (_: HandleCRMContactDataArguments => Try[Boolean], _: IntegrationType, _: IntegrationModuleType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: Long, _: Long, _: Option[DateTime], _: Option[DateTime], _: Seq[String], _: Long, _: Option[WSResponse], _: Seq[JsValue], _: Option[String], _: SrRollingUpdateCoreService)(_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger))
        .expects(*, IntegrationType.HUBSPOT, IntegrationModuleType.CONTACTS, integrationTPAccessTokenResponse, team_id, 1, None, None, List(), 0, *, *, *, *, *, *, *, *)
        .returning(Future.successful(None))

      (triggerDAO.updateInQueueForSync)
        .expects(1)
        .returning(Success(None))

      handleSyncTriggerEventService._handleEventAction(
        msg = triggerId,
        teamId = team_id,
        accountId = accountId,
        t = triggerInDB.copy(event = Some(EventType.HUBSPOT_PROSPECT_SYNC)),
        ac = triggerAction.copy(action_type = SRTriggerActionType.CREATE_OR_UPDATE_PROSPECT_IN_SMARTREACH),
        parentEventTypeTrigger = EventType.SALESFORCE_PROSPECT_SYNC,
      ).map { result =>
        Logger.info(s"result________________$result")

        assert(result.isLeft)
      }.recover { case e =>

        assert(false)
      }
    }


    it("case match with HUBSPOT_PROSPECT_SYNC and ADD_TO_DO_NOT_CONTACT_LIST") {

      trait IntegrationsCRMTestTrait extends TIntegrationCRMTrait {
        val name = IntegrationType.HUBSPOT
      }
      val crmIntegrationService = mock[IntegrationsCRMTestTrait]
      (srTriggerAllowedCombos.getIntegrationServiceByEvent)
        .expects(EventType.HUBSPOT_PROSPECT_SYNC)
        .returning(Success(crmIntegrationService.name))


      val integrationTPAccessTokenResponse = IntegrationTPAccessTokenResponse.FullTokenData(
        access_token = "access_token",
        refresh_token = None,
        expires_in = Some(1),
        expires_at = Some(DateTime.now().plusDays(1)),
        token_type = Some("token_type"),
        api_domain = Some("api_domain"),
        is_sandbox = Some(false)
      )

      (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken(_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
        .expects(team_id, IntegrationType.HUBSPOT, *, *, *)
        .returning(Future.successful(Right(integrationTPAccessTokenResponse)))


      (accountService.find(_: Long)(_: SRLogger))
        .expects(accountId, *)
        .returning(Success(accountAdmin))

      (prospectColumnDef.findCustomColumns)
        .expects(team_id)
        .returning(Seq())

      (tIntegrationCRMService.getRecentContacts
      (_: HandleCRMContactDataArguments => Try[Boolean], _: IntegrationType, _: IntegrationModuleType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: Long, _: Long, _: Option[DateTime], _: Option[DateTime], _: Seq[String], _: Long, _: Option[WSResponse], _: Seq[JsValue], _: Option[String], _: SrRollingUpdateCoreService)(_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger))
        .expects(*, IntegrationType.HUBSPOT, IntegrationModuleType.CONTACTS, integrationTPAccessTokenResponse, team_id, 1, None, None, List(), 0, *, *, *, *, *, *, *, *)
        .returning(Future.successful(None))

      (triggerDAO.updateInQueueForSync)
        .expects(1)
        .returning(Success(None))

      handleSyncTriggerEventService._handleEventAction(
        msg = triggerId,
        teamId = team_id,
        accountId = accountId,
        t = triggerInDB.copy(event = Some(EventType.HUBSPOT_PROSPECT_SYNC)),
        ac = triggerAction.copy(action_type = SRTriggerActionType.ADD_TO_DO_NOT_CONTACT_LIST),
        parentEventTypeTrigger = EventType.SALESFORCE_PROSPECT_SYNC,
      ).map { result =>
        Logger.info(s"FAILED result________________$result")

        assert(result.isLeft)
      }.recover { case e =>

        assert(false)
      }
    }

  }


  describe("_syncContactDataFromCRM") {
    val first_name = "Adminfirst"
    val last_name = "Adminlast"
    val company = "CompanyName"
    val email = "<EMAIL>"

    val integrationTPAccessTokenResponse = IntegrationTPAccessTokenResponse.FullTokenData(
      access_token = "some_access_token",
      refresh_token = Some("some_refresh_token"),
      expires_in = Option(150),
      expires_at = Option(DateTime.now().plusMinutes(9)),
      token_type = Some("some_token_type"),
      api_domain = Some("some_api_domain"),
      is_sandbox = Some(false)
    )


    val profile = AccountProfileInfo(
      first_name = first_name,
      last_name = last_name,
      company = Some(company),
      timezone = None,
      country_code = None,
      mobile_country_code = None,
      mobile_number = None,
      onboarding_phone_number = None,
      twofa_enabled = false,
      has_gauthenticator = false,
      weekly_report_emails = None,
      scheduled_for_deletion_at = None
    )

    val accountMetadata = AccountMetadata(
      // account_ui_version = None,
      is_profile_onboarding_done = None
    )

    val orgMetadata = OrgMetadataFixture.orgMetadataFixture2

    val orgCountData: OrgCountData = OrgCountDataFixture.orgCountData_default

    val orgSettings = OrgSettings(
      enable_ab_testing = false,
      disable_force_send = false,
      bulk_sender = false,
      allow_2fa = false,
      show_2fa_setting = false,
      enforce_2fa = false,
      allow_native_crm_integration = false,
      agency_option_allow_changing = false,
      agency_option_show = false
    )

    val orgPlan = OrgPlanFixture.orgPlanFixture

    val org = OrganizationWithCurrentData(

      id = 1,
      name = company,
      owner_account_id = 49,

      counts = orgCountData,
      settings = orgSettings,
      plan = orgPlan,

      is_agency = true,
      trial_ends_at = DateTime.now().plusDays(100),
      error = None,
      error_code = None,
      paused_till = None,
      errors = Seq(),
      warnings = Seq(),
      via_referral = false,
      org_metadata = orgMetadata
    )

    val teamMemberLite = TeamMemberLite(

      user_id = 2L,
      first_name = Some("first_name"),
      last_name = Some("last_name"),
      email = "<EMAIL>",
      active = true,
      timezone = Some("campaignTimezone"),
      twofa_enabled = true,
      created_at = DateTime.now(),
      user_uuid = AccountUuid("uuid"),
      team_role = TeamAccountRole.ADMIN

    )

    val prospect_CategoriesInDB = ProspectCategoriesInDB(
      id = 22L,
      name = "Completed",
      text_id = "Done",
      label_color = "Blue",
      is_custom = true,
      team_id = team_id,
      rank = ProspectCategoryRank(rank = 2000)
    )
    val teamMember: TeamMember = TeamMember(
      team_id = team_id,
      team_name = "team_name",
      user_id = 2L,
      ta_id = 49L, // dont send ta_id to frontend / api response, only for internal purpose, its dynamically assigned in AuthUtils
      first_name = Some(first_name),
      last_name = Some(last_name),
      email = "<EMAIL>",
      team_role = TeamAccountRole.ADMIN,
      api_key = Some("apiKey"),
      zapier_key = Some("zapier_key")
    )

    val adminDefaultPermissions = RolePermissionDataDAOV2.defaultRoles(
      role = TeamAccountRole.ADMIN,
      simpler_perm_flag = false
    )

    val rolePermissionData = RolePermissionDataV2.toRolePermissionApi(
      data = adminDefaultPermissions.copy(id = 10)
    )

    val team_account: TeamAccount = TeamAccount(

      team_id = team_id,
      org_id = 20L,

      role_from_db = Some(adminDefaultPermissions), // MUST come from db (option type only for cacheservice error), should not be sent to frontend, only intermediate

      role = Some(rolePermissionData), // should be sent to frontend

      active = true,
      is_actively_used = true,
      team_name = "team_name",
      total_members = 5,
      access_members = Seq(teamMember),
      all_members = Seq(teamMemberLite),

      prospect_categories_custom = Seq(prospect_CategoriesInDB),
      max_emails_per_prospect_per_day = 100L,
      max_emails_per_prospect_per_week = 500L,
      max_emails_per_prospect_account_per_day = 97,
      max_emails_per_prospect_account_per_week = 497,

      reply_handling = ReplyHandling.PAUSE_SPECIFIC_CAMPAIGN_ON_REPLY,
      created_at = DateTime.now(),
      selected_calendar_data = None,
      team_uuid = TeamUuid("uuid")
    )

    val accountAdmin = Account(
      id = AccountUuid("account_uuid"),
      internal_id = 2,
      email = email,
      email_verification_code = None,
      email_verification_code_created_at = None,
      created_at = DateTime.now().minusDays(1000),
      first_name = Some(first_name),
      last_name = Some(last_name),
      company = Some(company),
      timezone = None,
      profile = profile,
      org_role = Some(OrganizationRole.OWNER),
      teams = Seq(team_account),
      account_type = AccountType.AGENCY,
      org = org,
      active = true,
      email_notification_summary = "dSFA",
      account_metadata = accountMetadata,
      email_verified = true,
      signupType = None,
      account_access = AccountAccess(
        inbox_access = false
      ),
      calendar_account_data = None

    )

    it("accountService.find returns none") {

      (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken(_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
        .expects(2, null, *, *, *)
        .returning(Future(Right(integrationTPAccessTokenResponse)))

      (accountService.find(_: Long)(_: SRLogger))
        .expects(2, *)
        .returning(Failure(Error))
      handleSyncTriggerEventService._syncContactDataFromCRM(
        integrationType = crmIntegrationService.name,
        syncModule = IntegrationModuleType.CONTACTS,
        teamId = team_id,
        accountId = accountId,
        trigger = triggerInDB,
        action = triggerAction,
      ).map { result =>
        Logger.info(s"result_____________ $result")
        assert(result.isLeft)
      }.recover { case e =>

        assert(false)
      }


    }

    it("triggerDAO.updateInQueueForSync failed") {

      (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken(_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
        .expects(2, null, *, *, *)
        .returning(Future(Right(integrationTPAccessTokenResponse)))

      (accountService.find(_: Long)(_: SRLogger))
        .expects(2, *)
        .returning(Success(accountAdmin))

      (prospectColumnDef.findCustomColumns)
        .expects(2)
        .returning(Seq())

      (tIntegrationCRMService.getRecentContacts
      (_: HandleCRMContactDataArguments => Try[Boolean], _: IntegrationType, _: IntegrationModuleType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: Long, _: Long, _: Option[DateTime], _: Option[DateTime], _: Seq[String], _: Long, _: Option[WSResponse], _: Seq[JsValue], _: Option[String], _: SrRollingUpdateCoreService)(_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger))
        .expects(*, null, IntegrationModuleType.CONTACTS, integrationTPAccessTokenResponse, 2, 1, None, None, List(), 0, *, *, *, *, *, *, *, *)
        .returning(Future(None))

      (triggerDAO.updateInQueueForSync)
        .expects(1)
        .returning(Failure(Error))

      handleSyncTriggerEventService._syncContactDataFromCRM(
        integrationType = crmIntegrationService.name,
        syncModule = IntegrationModuleType.CONTACTS,
        teamId = team_id,
        accountId = accountId,
        trigger = triggerInDB,
        action = triggerAction,
      ).map { result =>
        Logger.info(s"result_____________ $result")
        assert(result.isLeft)
      }.recover { case e =>

        assert(false)
      }


    }

    it("triggerDAO.updateInQueueForSync with some campaign id but no basic details") {

      (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken(_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
        .expects(2, null, *, *, *)
        .returning(Future(Right(integrationTPAccessTokenResponse)))

      (accountService.find(_: Long)(_: SRLogger))
        .expects(2, *)
        .returning(Success(accountAdmin))

      (campaignService.findBasicDetails(_: Long, _: TeamId))
        .expects(1, TeamId(2L))
        .returning(Success(None))
      //
      //      (prospectColumnDef.findCustomColumns)
      //        .expects(2)
      //        .returning(Seq())
      //
      //      (crmIntegrationService.getRecentContacts (_: (HandleCRMContactDataArguments) => Try[Boolean], _: IntegrationType, _: IntegrationModuleType, _: IntegrationTPAccessTokenResponse, _: Long, _: Long, _: Option[String], _: Option[DateTime], _: Option[DateTime], _: Seq[String], _: Long, _: Option[Long], _: Option[Long], _: Option[String])(_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger))
      //        .expects(*, null,  IntegrationModuleType.CONTACTS, integrationTPAccessTokenResponse, 2, 1, Some("some_tp_filter_id"), None, None, List(), 0, None, None, None, *, *, *, *)
      //        .returning(Future(None))
      //
      //      (triggerDAO.updateInQueueForSync)
      //        .expects(1)
      //        .returning(Failure(Error))

      handleSyncTriggerEventService._syncContactDataFromCRM(
        integrationType = crmIntegrationService.name,
        syncModule = IntegrationModuleType.CONTACTS,
        teamId = team_id,
        accountId = accountId,
        trigger = triggerInDB,
        action = triggerAction.copy(campaign_id = Some(1)),
      ).map { result =>
        Logger.info(s"result_____________ $result")
        assert(result == Right(0))
      }.recover { case e =>
        assert(e.getMessage == "SmartReach is not able to connect to your null account. Please connect / integrate the null account under Settings -> Team Settings -> Integrations")
      }


    }


    it("triggerDAO.updateInQueueForSync sends None") {

      (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken(_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
        .expects(2, null, *, *, *)
        .returning(Future(Right(integrationTPAccessTokenResponse)))

      (accountService.find(_: Long)(_: SRLogger))
        .expects(2, *)
        .returning(Success(accountAdmin))

      (prospectColumnDef.findCustomColumns)
        .expects(2)
        .returning(Seq())

      (tIntegrationCRMService.getRecentContacts
      (_: HandleCRMContactDataArguments => Try[Boolean], _: IntegrationType, _: IntegrationModuleType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: Long, _: Long, _: Option[DateTime], _: Option[DateTime], _: Seq[String], _: Long, _: Option[WSResponse], _: Seq[JsValue], _: Option[String], _: SrRollingUpdateCoreService)(_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger))
        .expects(*, null, IntegrationModuleType.CONTACTS, integrationTPAccessTokenResponse, 2, 1, None, None, List(), 0, *, *, *, *, *, *, *, *)
        .returning(Future(None))

      (triggerDAO.updateInQueueForSync)
        .expects(1)
        .returning(Success(None))

      handleSyncTriggerEventService._syncContactDataFromCRM(
        integrationType = crmIntegrationService.name,
        syncModule = IntegrationModuleType.CONTACTS,
        teamId = team_id,
        accountId = accountId,
        trigger = triggerInDB,
        action = triggerAction,
      ).map { result =>
        Logger.info(s"result_____________ $result")
        assert(result.isLeft)
      }.recover { case e =>

        assert(false)
      }


    }

    it("triggerDAO.updateInQueueForSync sends Some and triggerDAO.updateLastSyncAt failed") {

      (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken(_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
        .expects(2, null, *, *, *)
        .returning(Future(Right(integrationTPAccessTokenResponse)))

      (accountService.find(_: Long)(_: SRLogger))
        .expects(2, *)
        .returning(Success(accountAdmin))

      (prospectColumnDef.findCustomColumns)
        .expects(2)
        .returning(Seq())

      (tIntegrationCRMService.getRecentContacts
      (_: HandleCRMContactDataArguments => Try[Boolean], _: IntegrationType, _: IntegrationModuleType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: Long, _: Long, _: Option[DateTime], _: Option[DateTime], _: Seq[String], _: Long, _: Option[WSResponse], _: Seq[JsValue], _: Option[String], _: SrRollingUpdateCoreService)(_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger))
        .expects(*, null, IntegrationModuleType.CONTACTS, integrationTPAccessTokenResponse, 2, 1, None, None, List(), 0, *, *, *, *, *, *, *, *)
        .returning(Future(None))

      (triggerDAO.updateInQueueForSync)
        .expects(1)
        .returning(Success(Some(101)))

      (triggerDAO.updateLastSyncAt)
        .expects(1, triggerAction, None, *)
        .returning(Failure(Error))

      handleSyncTriggerEventService._syncContactDataFromCRM(
        integrationType = crmIntegrationService.name,
        syncModule = IntegrationModuleType.CONTACTS,
        teamId = team_id,
        accountId = accountId,
        trigger = triggerInDB,
        action = triggerAction,
      ).map { result =>
        Logger.info(s"result_____________ $result")
        assert(result.isLeft)
      }.recover { case e =>

        assert(false)
      }


    }

    it("triggerDAO.updateInQueueForSync sends Some and triggerDAO.updateLastSyncAt sends None") {

      (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken(_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
        .expects(2, null, *, *, *)
        .returning(Future(Right(integrationTPAccessTokenResponse)))

      (accountService.find(_: Long)(_: SRLogger))
        .expects(2, *)
        .returning(Success(accountAdmin))

      (prospectColumnDef.findCustomColumns)
        .expects(2)
        .returning(Seq())

      (tIntegrationCRMService.getRecentContacts
      (_: HandleCRMContactDataArguments => Try[Boolean], _: IntegrationType, _: IntegrationModuleType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: Long, _: Long, _: Option[DateTime], _: Option[DateTime], _: Seq[String], _: Long, _: Option[WSResponse], _: Seq[JsValue], _: Option[String], _: SrRollingUpdateCoreService)(_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger))
        .expects(*, null, IntegrationModuleType.CONTACTS, integrationTPAccessTokenResponse, 2, 1, None, None, List(), 0, *, *, *, *, *, *, *, *)
        .returning(Future(None))

      (triggerDAO.updateInQueueForSync)
        .expects(1)
        .returning(Success(Some(101)))

      (triggerDAO.updateLastSyncAt)
        .expects(1, triggerAction, None, *)
        .returning(Success(None))

      handleSyncTriggerEventService._syncContactDataFromCRM(
        integrationType = crmIntegrationService.name,
        syncModule = IntegrationModuleType.CONTACTS,
        teamId = team_id,
        accountId = accountId,
        trigger = triggerInDB,
        action = triggerAction,
      ).map { result =>
        Logger.info(s"result_____________ $result")
        assert(result.isLeft)
      }.recover { case e =>

        assert(false)
      }


    }

    it("triggerDAO.updateInQueueForSync sends Some and triggerDAO.updateLastSyncAt sends some") {

      (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken(_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
        .expects(2, null, *, *, *)
        .returning(Future(Right(integrationTPAccessTokenResponse)))

      (accountService.find(_: Long)(_: SRLogger))
        .expects(2, *)
        .returning(Success(accountAdmin))

      (prospectColumnDef.findCustomColumns)
        .expects(2)
        .returning(Seq())

      (tIntegrationCRMService.getRecentContacts
      (_: HandleCRMContactDataArguments => Try[Boolean], _: IntegrationType, _: IntegrationModuleType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: Long, _: Long, _: Option[DateTime], _: Option[DateTime], _: Seq[String], _: Long, _: Option[WSResponse], _: Seq[JsValue], _: Option[String], _: SrRollingUpdateCoreService)(_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger))
        .expects(*, null, IntegrationModuleType.CONTACTS, integrationTPAccessTokenResponse, 2, 1, None, None, List(), 0, *, *, *, *, *, *, *, *)
        .returning(Future(None))

      (triggerDAO.updateInQueueForSync)
        .expects(1)
        .returning(Success(Some(101)))

      (triggerDAO.updateLastSyncAt)
        .expects(1, triggerAction, None, *)
        .returning(Success(Some(1111)))

      handleSyncTriggerEventService._syncContactDataFromCRM(
        integrationType = crmIntegrationService.name,
        syncModule = IntegrationModuleType.CONTACTS,
        teamId = team_id,
        accountId = accountId,
        trigger = triggerInDB,
        action = triggerAction,

      ).map { result =>
        Logger.info(s"result_____________ $result")
        assert(result == Right(1))
      }.recover { case e =>

        assert(false)
      }


    }


  }

}
