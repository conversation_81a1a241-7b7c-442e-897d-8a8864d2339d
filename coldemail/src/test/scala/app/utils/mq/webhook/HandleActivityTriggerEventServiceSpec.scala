package app.utils.mq.webhook

import org.apache.pekko.actor.ActorSystem
import api.accounts.models.AccountId
import api.accounts.{AccountService, AccountUuid, RepTrackingHosts, TeamId}
import api.campaigns.models.CampaignName
import api.campaigns.services.{CampaignId, CampaignProspectDAOService, CampaignProspectService, CampaignService}
import api.campaigns.CampaignProspectDAO
import api.emails.dao_service.EmailScheduledDAOService
import api.emails.{EmailReceivedForUpdateLeadStatus, EmailReceivedForWebhook, EmailScheduledDAO, EmailThreadDAO}
import api.integrations.services.{CrmLeadStatusUpdate, TIntegrationCRMService}
import api.integrations.{CommonCRMAPIErrors, CreateOrUpdateSingleContactsError, FetchTokensFromDBAndRefreshAccessTokenError, IntegrationContactResponse, IntegrationTPAccessTokenResponse, TIntegrationCRMTrait, UpdateActivityError, UpdateLeadStatusError}
import api.prospects.dao_service.{ProspectDAOService, ProspectDAOServiceV2}
import api.prospects.service.ProspectServiceV2
import api.prospects.{ProspectBasicDetails, ProspectUuid}
import api.prospects.models.{ProspectCategoryId, ProspectId}
import api.rep_tracking_hosts.service.RepTrackingHostService
import api.sr_audit_logs.models.EventDataType.{ActivityEventDataType, writes}
import api.sr_audit_logs.services.WorkFlowAttemptService
import api.triggers.*
import api.tags.TagService
import eventframework.{ConversationCampaign, ConversationObject, ConversationProspect, MessageObject, ProspectObject, ProspectObjectInternal}
import org.joda.time.{DateTime, Minutes}
import org.scalamock.matchers.ArgCapture.CaptureOne
import org.scalamock.scalatest.AsyncMockFactory
import org.scalatest.funspec.AsyncFunSpec
import play.api.libs.json.Json
import play.api.libs.ws.WSClient
import play.api.libs.ws.ahc.AhcWSClient
import utils.SRLogger
import utils.mq.webhook.{HandleActivityTriggerEventService, LeadStatusService}
import api.sr_audit_logs.models.{EventType, WorkflowAttemptTryErrorReason}
import api.team_inbox.dao.ReplySentimentDAO
import api.team_inbox.model.{ReplySentimentChannelType, ReplySentimentSubCategory, ReplySentimentTypeData}
import api.team_inbox.service.{ReplySentimentForTeam, ReplySentimentUuid}
import app.test_fixtures.prospect.ProspectFixtures
import io.smartreach.esp.api.emails.IEmailAddress
import utils.email_notification.service.EmailNotificationService
import utils.mq.webhook.model.CreateInCRMJedisDAO
import utils.uuid.SrUuidUtils
import utils_deploy.rolling_updates.models.SrRollingUpdateFeature
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService
import org.scalatest.matchers.should.Matchers.shouldBe
import utils.testapp.TestAppExecutionContext

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}


class HandleActivityTriggerEventServiceSpec
  extends AsyncFunSpec
    with AsyncMockFactory {

  implicit lazy val system: ActorSystem = TestAppExecutionContext.actorSystem
  implicit lazy val wSClient: AhcWSClient = TestAppExecutionContext.wsClient
  implicit lazy val actorContext: ExecutionContext = system.dispatcher

  given Logger: SRLogger = new SRLogger("Unit test")
  val Error = new Throwable("ERROR")

  val team_id: Long = 2
  val accountId: Long = 3
  val prospect_id: Long = 4L

  val integration_type = IntegrationType.SALESFORCE

  val moduleType = IntegrationModuleType.LEADS
  val empty_email = ""
  val access_token = "abcd-1234"
  val refresh_token = Some("wxyz-********")
  val expires_in = Some(********);
  val expires_at = Some(DateTime.now())
  val token_type = Some("acc-tok")
  val api_domain = Some("api.domain.com")
  val intg_acc_tok = IntegrationTPAccessTokenResponse.FullTokenData(
    access_token = access_token,
    refresh_token = refresh_token,
    expires_in = expires_in,
    expires_at = expires_at,
    token_type = token_type,
    api_domain = api_domain,
    is_sandbox = Some(false)
  )

  val prospect_email = "<EMAIL>"
  val status_col_name = "lead_status"
  val status_col_value = "Closed - Converted"
  val new_status = "Nurturing - Contacted"
  val new_status_with_options = CRMStatusColumnNameAndOptions(
    column_name = status_col_name,
    column_options = Seq(
      CRMStatusColumnOptions(
        label = "Open - Not Contacted",
        text_id = "Open - Not Contacted"
      ),
      CRMStatusColumnOptions(
        label = "Working - Contacted",
        text_id = "Working - Contacted"
      ),
      CRMStatusColumnOptions(
        label = "Closed - Converted",
        text_id = "Closed - Converted"
      ),
      CRMStatusColumnOptions(
        label = "Closed - Not Converted",
        text_id = "Closed - Not Converted"
      ),
      CRMStatusColumnOptions(
        label = "Closed - not interested",
        text_id = "Closed - not interested"
      ),
      CRMStatusColumnOptions(
        label = "Nurturing - Contacted",
        text_id = "Nurturing - Contacted"
      )
    )
  )
  val status_col_with_options = CRMStatusColumnNameAndOptions(
    column_name = status_col_name,
    column_options = Seq(
      CRMStatusColumnOptions(
        label = "Closed - Converted",
        text_id = "Closed - Converted"
      )
    )
  )
  // ==== Dummy Prospect
  val dummyProspectObj = ProspectObject(
    id = 1,

    owner_id = 0,

    team_id = 0,

    first_name = Some(""),
    last_name = None,

    email = Some(prospect_email),

    last_contacted_at = None,
    last_contacted_at_phone = None,

    created_at = DateTime.now,

    custom_fields = Json.obj(),

    list = None,

    company = None,
    job_title = None,
    phone = None,
    phone_2 = None,
    phone_3 = None,

    linkedin_url = None,

    city = None,
    state = None,
    country = None,
    timezone = None,


    prospect_category = "",
    latest_reply_sentiment_uuid = None,

    internal = {

      ProspectFixtures.prospectObjectInternal
    },
    current_step_type = None,
    latest_task_done_at = None,

    prospect_uuid = Some(ProspectUuid("prs_aa_abcdefghi")),
    owner_uuid = AccountUuid("acc_aa_abcdegfhi"),
    updated_at = DateTime.now()

  )

  val person_id = "wxzy-991013"

  val triggerFields = TriggerFields(
    sr_field = "first_name",
    tp_field = "firstName",
    tp_field_default_value = None,
    field_label = "First Name",
    tp_field_label = Some("First Name"),
    field_type = "text"
  )
  val updateFieldsMappingForm = UpdateFieldsMappingForm(
    fields = Some(Seq(triggerFields))
  )

  val triggerUsers = TriggerUsers(
    sr_user_id = 24,
    sr_user_email = "<EMAIL>",
    tp_user_id = "154DFR87FHWJ",
    tp_user_email = "<EMAIL>"
  )
  val updatedUserMappingMappingForm = UpdateUserMappingForm(
    users = Some(Seq(triggerUsers))
  )

  val workflow_crm_setting_id = 123456

  val triggerDAO = mock[Trigger]
  val emailScheduledDAO = mock[EmailScheduledDAO]
  val emailScheduledDAOService = mock[EmailScheduledDAOService]
  val prospectDAOService = mock[ProspectDAOService]
  val prospectDAOServiceV2 = mock[ProspectDAOServiceV2]
  val triggerService = mock[TriggerService]
  val campaignService = mock[CampaignService]
  val accountService = mock[AccountService]
  val campaignProspectDAOService = mock[CampaignProspectDAOService]
  val emailNotificationService = mock[EmailNotificationService]
  val workFlowAttemptService = mock[WorkFlowAttemptService]
  val leadStatusServiceMock = mock[LeadStatusService]
  val campaignProspectService = mock[CampaignProspectService]
  val tagService = mock[TagService]
  val prospectServiceV2 = mock[ProspectServiceV2]
  val srTriggerAllowedCombos = mock[SRTriggerAllowedCombos]
  val tIntegrationCRMService = mock[TIntegrationCRMService]
  val emailThreadDAO = mock[EmailThreadDAO]
  val srUuidUtils = mock[SrUuidUtils]
  val repTrackingHostService = mock[RepTrackingHostService]
  val srRollingUpdateCoreService = mock[SrRollingUpdateCoreService]
  val createInCRMJedisDAO = mock[CreateInCRMJedisDAO]
  val replySentimentDAO = mock[ReplySentimentDAO]

  val leadStatusService = new LeadStatusService(
    triggerDAO = triggerDAO,
    emailScheduledDAO = emailScheduledDAO,
    prospectDAOService = prospectDAOService,
    accountService = accountService,
    tIntegrationCRMService = tIntegrationCRMService,
    srRollingUpdateCoreService = srRollingUpdateCoreService,
    createInCRMJedisDAO = createInCRMJedisDAO
  )

  val handleActivityTriggerEventServiceSpec = new HandleActivityTriggerEventService(
    triggerDAO = triggerDAO,
    emailScheduledDAOService = emailScheduledDAOService,
    prospectDAOService = prospectDAOService,
    prospectDAOServiceV2 = prospectDAOServiceV2,
    campaignService = campaignService,
    accountService = accountService,
    //    triggerService = triggerService,
    leadStatusService = leadStatusService,
    emailNotificationService = emailNotificationService,
    campaignProspectDAOService = campaignProspectDAOService,
    campaignProspectService = campaignProspectService,
    tagService = tagService,
    prospectServiceV2 = prospectServiceV2,
    srTriggerAllowedCombos = srTriggerAllowedCombos,
    tIntegrationCRMService = tIntegrationCRMService,
    emailThreadDAO = emailThreadDAO,
    srUuidUtils = srUuidUtils,
    repTrackingHostService = repTrackingHostService,
    replySentimentDAO = replySentimentDAO
  )

  val repTrackingHosts = RepTrackingHosts(
    id = 1,
    host_url = "devapi.sreml.com",
    subdomain_based = false,
    active = true
  )

  val currentStatusCRM = Some("Closed - Converted")
  val newStatusToBeUpdate = "temp"

  val crMIntegrationInDB = CRMIntegrationInDB(
    workflow_crm_setting_id = workflow_crm_setting_id,
    team_id = team_id,
    owner_id = accountId,
    module_id = 1,
    crm = integration_type,
    module = moduleType,
    user_mapping = None,
    field_mapping = None,
    activity_to_status_mapping = None,
    category_to_status_mapping = None,
    status_column_in_crm = Some(status_col_with_options),
    create_record_if_not_exists = true,
    track_activities = false,
    create_or_update_record_in_crm = false,
    crm_filters_for_add_to_do_not_contact_in_sr = None,
    allow_going_back_in_crm_status = false,
    sentiment_to_status_mapping = None,
    update_reply_sentiment_for_all_associated_prospects = false,
    error = None, error_at = None, last_alert_for_error_sent_at = None
  )

  describe("UPDATE LEAD STATUS handleUpdateLeadStatusInCRM") {

    trait IntegrationsCRMTestTrait extends TIntegrationCRMTrait {
      val name = IntegrationType.SALESFORCE
    }

    val crmIntegrationService = mock[IntegrationsCRMTestTrait]

    describe("handleUpdateLeadStatusInCRM with EMAIL_INVALID") {
      /* #SET 1 EMAIL_INVALID START */
      val eventFOrEmailInvalid = EventType.EMAIL_INVALID

      describe("PATH 1.4 When prospect found in CRM") {

        it("should 1: update activity to crm status tokens not found") {

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))

//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Left(FetchTokensFromDBAndRefreshAccessTokenError.InvalidRefreshTokenError(msg = "InvalidRefreshTokenError"))
            ))

          leadStatusService.handleUpdateLeadStatusInCRM(
            event = eventFOrEmailInvalid,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = None,
            statusValue = status_col_value,
            intg = crMIntegrationInDB,
            accountId = accountId
          ).map(leadStatusRes => {
            assert(leadStatusRes.isLeft)
          })

        }

        it("should 2: update activity to crm status tokens found but prospect did't found with given id") {

//          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
//          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
//            .expects(team_id, integration_type, *, *, *)
//            .returning(Future.successful(
//              Right(intg_acc_tok)
//            ))

//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(
              Failure(new Exception("Error while fetching prospect. Please try again, or contact support."))
            )

          leadStatusService.handleUpdateLeadStatusInCRM(
            event = eventFOrEmailInvalid,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = None,
            statusValue = status_col_value,
            intg = crMIntegrationInDB,
            accountId = accountId
          ).map(leadStatusRes => {
            assert(leadStatusRes.isLeft)
          })

        }

        it("should 3 update activity to crm status: found tokens, gets Prospects, prospects fond in CRM and updateLeadStatus is Success") {

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            )).repeat(2)

//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))

          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name, intg_acc_tok, dummyProspectObj, moduleType, Some(status_col_name),
              *, *, *, *)
            .returning(Future.successful(Right(Seq(IntegrationContactResponse(
              id = person_id, email = Some(prospect_email), status = Some(status_col_value)
            )))))

          val person1 = CaptureOne[CrmLeadStatusUpdate]()

          (tIntegrationCRMService.updateLeadStatus
          (_: IntegrationType,_: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType, _: CrmLeadStatusUpdate)
          (_: WSClient, _: ExecutionContext, _: SRLogger)
            )
            .expects(crmIntegrationService.name,
              intg_acc_tok, moduleType, capture(person1),
              *, *, *
            ).returning(
            Future.successful(
              Right(person_id)
            )
          )

          leadStatusService.handleUpdateLeadStatusInCRM(
            event = eventFOrEmailInvalid,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = None,
            statusValue = new_status,
            intg = crMIntegrationInDB.copy(status_column_in_crm = Some(new_status_with_options)),
            accountId = accountId,
          ).map(leadStatusRes => {

            assert(person1.value.person_id == person_id)
            assert(person1.value.statusColumn == new_status_with_options)
            assert(person1.value.statusValue == new_status)

            assert(leadStatusRes === Right(Seq(1)))
          })

        }

        it("should 4: update activity to crm status: found tokens, gets Prospects, prospects fond in CRM and updateLeadStatus is Failure InternalServerError") {

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            )).repeat(2)

//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))


          (tIntegrationCRMService.findAllByProspectAndModule
            (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name, intg_acc_tok, dummyProspectObj, moduleType, Some(status_col_name),
              *, *, *, *)
            .returning(Future.successful(Right(Seq(IntegrationContactResponse(
              id = person_id, email = Some(prospect_email), status = Some(status_col_value)
            )))))


          val person1 = CaptureOne[CrmLeadStatusUpdate]()
          (tIntegrationCRMService.updateLeadStatus
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType, _: CrmLeadStatusUpdate)
          (_: WSClient, _: ExecutionContext, _: SRLogger)
            )
            .expects(crmIntegrationService.name,
              intg_acc_tok, moduleType, capture(person1),
              *, *, *
            ).returning(
            Future.successful(

              Left(UpdateLeadStatusError.CommonCRMAPIError(err = CommonCRMAPIErrors.InternalServerError(msg = "Internal server error")))
            ))

          leadStatusService.handleUpdateLeadStatusInCRM(
            event = eventFOrEmailInvalid,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = None,
            statusValue = new_status,
            intg = crMIntegrationInDB.copy(status_column_in_crm = Some(new_status_with_options)),
            accountId = accountId,
          ).map(leadStatusRes => {
            assert(person1.value.person_id == person_id)
            assert(person1.value.statusColumn == new_status_with_options)
            assert(person1.value.statusValue == new_status)
            assert(leadStatusRes.isLeft)
          })

        }

        it("should 5: update activity to crm status: found tokens, gets Prospects, prospects fond in CRM and updateLeadStatus is Failure NotFoundError") {

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            )).repeat(2)

//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))


          (tIntegrationCRMService.findAllByProspectAndModule
            (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name, intg_acc_tok, dummyProspectObj, moduleType, Some(status_col_name),
              *, *, *, *)
            .returning(Future.successful(Right(Seq(IntegrationContactResponse(
              id = person_id, email = Some(prospect_email), status = Some(status_col_value)
            )))))

          val person1 = CaptureOne[CrmLeadStatusUpdate]()
          (tIntegrationCRMService.updateLeadStatus
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType, _: CrmLeadStatusUpdate)
          (_: WSClient, _: ExecutionContext, _: SRLogger)
            )
            .expects(crmIntegrationService.name,
              intg_acc_tok, moduleType, capture(person1),
              *, *, *
            ).returning(
            Future.successful(
              Left(UpdateLeadStatusError.CommonCRMAPIError(err = CommonCRMAPIErrors.NotFoundError(msg = "Resource not found error")))
            ))

          leadStatusService.handleUpdateLeadStatusInCRM(
            event = eventFOrEmailInvalid,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = None,
            statusValue = new_status,
            intg = crMIntegrationInDB.copy(status_column_in_crm = Some(new_status_with_options)),
            accountId = accountId,
          ).map(leadStatusRes => {

            assert(person1.value.person_id == person_id)
            assert(person1.value.statusColumn == new_status_with_options)
            assert(person1.value.statusValue == new_status)

            assert(leadStatusRes.isLeft)
          })

        }

        it("should 6: update activity to crm status: found tokens, gets Prospects, prospects fond in CRM and updateLeadStatus is Failure UnAuthorizedError") {

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            )).repeat(2)

//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))


          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name, intg_acc_tok, dummyProspectObj, moduleType, Some(status_col_name),
              *, *, *, *)
            .returning(Future.successful(Right(Seq(IntegrationContactResponse(
              id = person_id, email = Some(prospect_email), status = Some(status_col_value)
            )))))

          val person1 = CaptureOne[CrmLeadStatusUpdate]()
          (tIntegrationCRMService.updateLeadStatus
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType, _: CrmLeadStatusUpdate)
          (_: WSClient, _: ExecutionContext, _: SRLogger)
            )
            .expects(crmIntegrationService.name,
              intg_acc_tok, moduleType, capture(person1),
              *, *, *
            ).returning(
            Future.successful(
              Left(UpdateLeadStatusError.CommonCRMAPIError(err = CommonCRMAPIErrors.UnAuthorizedError(msg = "UnAuthorizedError")))
            ))

          leadStatusService.handleUpdateLeadStatusInCRM(
            event = eventFOrEmailInvalid,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = None,
            statusValue = new_status,
            intg = crMIntegrationInDB.copy(status_column_in_crm = Some(new_status_with_options)),
            accountId = accountId,
          ).map(leadStatusRes => {

            assert(person1.value.person_id == person_id)
            assert(person1.value.statusColumn == new_status_with_options)
            assert(person1.value.statusValue == new_status)

            assert(leadStatusRes.isLeft)
          })

        }

        it("should 7: update activity to crm status: found tokens, gets Prospects, prospects fond in CRM and updateLeadStatus is Failure TooManyRequestsError") {

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            )).repeat(2)

//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))


          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name, intg_acc_tok, dummyProspectObj, moduleType, Some(status_col_name),
              *, *, *, *)
            .returning(Future.successful(Right(Seq(IntegrationContactResponse(
              id = person_id, email = Some(prospect_email), status = Some(status_col_value)
            )))))

          val person1 = CaptureOne[CrmLeadStatusUpdate]()
          (tIntegrationCRMService.updateLeadStatus
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType, _: CrmLeadStatusUpdate)
          (_: WSClient, _: ExecutionContext, _: SRLogger)
            )
            .expects(crmIntegrationService.name,
              intg_acc_tok, moduleType, capture(person1),
              *, *, *
            ).returning(
            Future.successful(
              Left(UpdateLeadStatusError.CommonCRMAPIError(err = CommonCRMAPIErrors.TooManyRequestsError(msg = "TooManyRequestsError")))
            ))

          leadStatusService.handleUpdateLeadStatusInCRM(
            event = eventFOrEmailInvalid,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = None,
            statusValue = new_status,
            intg = crMIntegrationInDB.copy(status_column_in_crm = Some(new_status_with_options)),
            accountId = accountId,
          ).map(leadStatusRes => {

            assert(person1.value.person_id == person_id)
            assert(person1.value.statusColumn == new_status_with_options)
            assert(person1.value.statusValue == new_status)

            assert(leadStatusRes.isLeft)
          })

        }


      }

      describe("PATH 2.1 When prospect not found in CRM AND CREATE_IF_NOT_EXISTS IS TRUE") {

        it("should 1: update activity to crm status: found tokens, gets Prospects, prospects fond in CRM and create_record_if_not_exists true nd findFieldMapping exception throws") {

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            ))

//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)


          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))

          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name, intg_acc_tok, dummyProspectObj, moduleType, Some(status_col_name),
              *, *, *, *)
            .returning(Future.successful(
              Right(Seq())
            ))
          ( createInCRMJedisDAO.getLock (_: ProspectId, _: IntegrationType, _: TeamId,_:IntegrationModuleType)(using _: SRLogger))
            .expects(*, *, *, *,*)
            .returning(false)

          (triggerDAO.findFieldMapping)
            .expects(team_id, integration_type, moduleType)
            .returning(Failure(new Exception("findFieldMapping error")))

          leadStatusService.handleUpdateLeadStatusInCRM(
            event = eventFOrEmailInvalid,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = None,
            statusValue = status_col_value,
            intg = crMIntegrationInDB,
            accountId = accountId,
          ).map(leadStatusRes => {
            assert(leadStatusRes.isLeft)
          })

        }

        it("should 2: update activity to crm status: found tokens, gets Prospects, prospects fond in CRM and create_record_if_not_exists true and findFieldMapping returns NONE") {


          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            ))

//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)


          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))

          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name,intg_acc_tok, dummyProspectObj, moduleType, Some(status_col_name),
              *, *, *, *)
            .returning(Future.successful(
              Right(Seq())
            ))
          ( createInCRMJedisDAO.getLock (_: ProspectId, _: IntegrationType, _: TeamId,_:IntegrationModuleType)(using _: SRLogger))
            .expects(*, *, *, *, *)
            .returning(false)

          (triggerDAO.findFieldMapping)
            .expects(team_id, integration_type, moduleType)
            .returning(Success(None))

          leadStatusService.handleUpdateLeadStatusInCRM(
            event = eventFOrEmailInvalid,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = None,
            statusValue = status_col_value,
            intg = crMIntegrationInDB,
            accountId = accountId,
          ).map(leadStatusRes => {
            assert(leadStatusRes.isLeft)
          })

        }

        it("should 3: update activity to crm status: found tokens, gets Prospects, prospects fond in CRM and create_record_if_not_exists true and findFieldMapping returns success and usermapping success and buildcontactonject success and createOrUpdateSingleContact return None") {

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            ))

//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))

          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name, intg_acc_tok, dummyProspectObj, moduleType, Some(status_col_name),
              *, *, *, *)
            .returning(Future.successful(
              Right(Seq())
            ))
          ( createInCRMJedisDAO.getLock (_: ProspectId, _: IntegrationType, _: TeamId,_:IntegrationModuleType)(using _: SRLogger))
            .expects(*, *, *, *, *)
            .returning(false)
          (triggerDAO.findFieldMapping)
            .expects(team_id, integration_type, moduleType)
            .returning(Success(Some(updateFieldsMappingForm)))


          (tIntegrationCRMService.findUpdatedUserMapping
          (_: IntegrationTPAccessTokenResponse.FullTokenData, _: Long, _: Long, _: IntegrationType)(_: WSClient, _: ExecutionContext, _: SRLogger))
            .expects(intg_acc_tok, team_id, accountId, integration_type, *, *, *)
            .returning(Future.successful(Right(updatedUserMappingMappingForm)))


          val person_email_capt = CaptureOne[String]()
          (tIntegrationCRMService.createOrUpdateSingleContact
          (_: IntegrationType,_: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType, _: ProspectObject, _: UpdateFieldsMappingForm, _: UpdateUserMappingForm, _: Long, _: Long, _: String)
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name,
              intg_acc_tok, moduleType, dummyProspectObj, updateFieldsMappingForm, updatedUserMappingMappingForm, accountId, team_id, capture(person_email_capt),
              *, *, *, *
            ).returning(Future.successful(Left(
            CreateOrUpdateSingleContactsError.CommonCRMAPIError(err = CommonCRMAPIErrors.UnknownError(msg = "Unknown error"))
          )))


          leadStatusService.handleUpdateLeadStatusInCRM(
            event = eventFOrEmailInvalid,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = None,
            statusValue = status_col_value,
            intg = crMIntegrationInDB,
            accountId = accountId,
          ).map(leadStatusRes => {
            assert(leadStatusRes.isLeft)
          })

        }

        it("should 4: update activity to crm status: found tokens, gets Prospects, prospects fond in CRM and create_record_if_not_exists true and findFieldMapping returns success and usermapping success and buildcontactonject success and createOrUpdateSingleContact success and then update lead status Failure InternalServerError") {


          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            )).repeat(2)

//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))

          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name,intg_acc_tok, dummyProspectObj, moduleType, Some(status_col_name),
              *, *, *, *)
            .returning(Future.successful(
              Right(Seq())
            ))
          ( createInCRMJedisDAO.getLock (_: ProspectId, _: IntegrationType, _: TeamId,_:IntegrationModuleType)(using _: SRLogger))
            .expects(*, *, *, *, *)
            .returning(false)
          (triggerDAO.findFieldMapping)
            .expects(team_id, integration_type, moduleType)
            .returning(Success(Some(updateFieldsMappingForm)))


          (tIntegrationCRMService.findUpdatedUserMapping
          (_: IntegrationTPAccessTokenResponse.FullTokenData, _: Long, _: Long, _: IntegrationType)(_: WSClient, _: ExecutionContext, _: SRLogger))
            .expects(intg_acc_tok, team_id, accountId, integration_type, *, *, *)
            .returning(Future.successful(Right(updatedUserMappingMappingForm)))

          val person_email_capt = CaptureOne[String]()
          (tIntegrationCRMService.createOrUpdateSingleContact
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType, _: ProspectObject, _: UpdateFieldsMappingForm, _: UpdateUserMappingForm, _: Long, _: Long, _: String)
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name,
              intg_acc_tok, moduleType, dummyProspectObj, updateFieldsMappingForm, updatedUserMappingMappingForm, accountId, team_id, capture(person_email_capt),
              *, *, *, *
            )
            .returning(Future.successful(Right(person_id)))

          val person1 = CaptureOne[CrmLeadStatusUpdate]()
          (tIntegrationCRMService.updateLeadStatus
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType, _: CrmLeadStatusUpdate)
          (_: WSClient, _: ExecutionContext, _: SRLogger)
            )
            .expects(crmIntegrationService.name,
              intg_acc_tok, moduleType, capture(person1),
              *, *, *
            ).returning(
            Future.successful(
              Left(UpdateLeadStatusError.CommonCRMAPIError(err = CommonCRMAPIErrors.InternalServerError(msg = "Internal server error")))
            ))


          leadStatusService.handleUpdateLeadStatusInCRM(
            event = eventFOrEmailInvalid,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = None,
            statusValue = new_status,
            intg = crMIntegrationInDB.copy(status_column_in_crm = Some(new_status_with_options)),
            accountId = accountId,
          ).map(leadStatusRes => {

            assert(person1.value.person_id == person_id)
            assert(person1.value.statusColumn == new_status_with_options)
            assert(person1.value.statusValue == new_status)

            assert(leadStatusRes.isLeft)
          })

        }

        it("should 5: update activity to crm status: found tokens, gets Prospects, prospects fond in CRM and create_record_if_not_exists true and findFieldMapping returns success and usermapping success and buildcontactonject success and createOrUpdateSingleContact success and then update lead status Failure NotFoundError") {


          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            )).repeat(2)

//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))

          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name, intg_acc_tok, dummyProspectObj, moduleType, Some(status_col_name),
              *, *, *, *)
            .returning(Future.successful(
              Right(Seq())
            ))
          ( createInCRMJedisDAO.getLock (_: ProspectId, _: IntegrationType, _: TeamId,_:IntegrationModuleType)(using _: SRLogger))
            .expects(*, *, *, *, *)
            .returning(false)
          (triggerDAO.findFieldMapping)
            .expects(team_id, integration_type, moduleType)
            .returning(Success(Some(updateFieldsMappingForm)))


          (tIntegrationCRMService.findUpdatedUserMapping
          (_: IntegrationTPAccessTokenResponse.FullTokenData, _: Long, _: Long, _: IntegrationType)(_: WSClient, _: ExecutionContext, _: SRLogger))
            .expects(intg_acc_tok, team_id, accountId, integration_type, *, *, *)
            .returning(Future.successful(Right(updatedUserMappingMappingForm)))

          val person_email_capt = CaptureOne[String]()
          (tIntegrationCRMService.createOrUpdateSingleContact
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType, _: ProspectObject, _: UpdateFieldsMappingForm, _: UpdateUserMappingForm, _: Long, _: Long, _: String)
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name,
              intg_acc_tok, moduleType, dummyProspectObj, updateFieldsMappingForm, updatedUserMappingMappingForm, accountId, team_id, capture(person_email_capt),
              *, *, *, *
            ).returning(Future.successful(Right(person_id)))


          val person1 = CaptureOne[CrmLeadStatusUpdate]()
          (tIntegrationCRMService.updateLeadStatus
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType, _: CrmLeadStatusUpdate)
          (_: WSClient, _: ExecutionContext, _: SRLogger)
            )
            .expects(crmIntegrationService.name,
              intg_acc_tok, moduleType, capture(person1),
              *, *, *
            ).returning(
            Future.successful(
              Left(UpdateLeadStatusError.CommonCRMAPIError(err = CommonCRMAPIErrors.NotFoundError(msg = "Resource not found error")))
            ))


          leadStatusService.handleUpdateLeadStatusInCRM(
            event = eventFOrEmailInvalid,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = None,
            statusValue = new_status,
            intg = crMIntegrationInDB.copy(status_column_in_crm = Some(new_status_with_options)),
            accountId = accountId,
          ).map(leadStatusRes => {

            assert(person1.value.person_id == person_id)
            assert(person1.value.statusColumn == new_status_with_options)
            assert(person1.value.statusValue == new_status)

            assert(leadStatusRes.isLeft)
          })

        }

        it("should 6: update activity to crm status: found tokens, gets Prospects, prospects fond in CRM and create_record_if_not_exists true and findFieldMapping returns success and usermapping success and buildcontactonject success and createOrUpdateSingleContact success and then update lead status Failure UnAuthorizedError") {


          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            )).repeat(2)

//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)


          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))

          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name, intg_acc_tok, dummyProspectObj, moduleType, Some(status_col_name),
              *, *, *, *)
            .returning(Future.successful(
              Right(Seq())
            ))
          ( createInCRMJedisDAO.getLock (_: ProspectId, _: IntegrationType, _: TeamId,_:IntegrationModuleType)(using _: SRLogger))
            .expects(*, *, *, *, *)
            .returning(false)
          (triggerDAO.findFieldMapping)
            .expects(team_id, integration_type, moduleType)
            .returning(Success(Some(updateFieldsMappingForm)))


          (tIntegrationCRMService.findUpdatedUserMapping
          (_: IntegrationTPAccessTokenResponse.FullTokenData, _: Long, _: Long, _: IntegrationType)(_: WSClient, _: ExecutionContext, _: SRLogger))
            .expects(intg_acc_tok, team_id, accountId, integration_type, *, *, *)
            .returning(Future.successful(Right(updatedUserMappingMappingForm)))

          val person_email_capt = CaptureOne[String]()
          (tIntegrationCRMService.createOrUpdateSingleContact
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType, _: ProspectObject, _: UpdateFieldsMappingForm, _: UpdateUserMappingForm, _: Long, _: Long, _: String)
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name,
              intg_acc_tok, moduleType, dummyProspectObj, updateFieldsMappingForm, updatedUserMappingMappingForm, accountId, team_id, capture(person_email_capt),
              *, *, *, *
            ).returning(Future.successful(Right(person_id)))


          val person1 = CaptureOne[CrmLeadStatusUpdate]()
          (tIntegrationCRMService.updateLeadStatus
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType, _: CrmLeadStatusUpdate)
          (_: WSClient, _: ExecutionContext, _: SRLogger)
            )
            .expects(crmIntegrationService.name,
              intg_acc_tok, moduleType, capture(person1),
              *, *, *
            ).returning(
            Future.successful(
              Left(UpdateLeadStatusError.CommonCRMAPIError(err = CommonCRMAPIErrors.UnAuthorizedError(msg = "UnAuthorizedError")))
            ))


          leadStatusService.handleUpdateLeadStatusInCRM(
            intg = crMIntegrationInDB.copy(status_column_in_crm = Some(new_status_with_options)),
            event = eventFOrEmailInvalid,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = None,
            statusValue = new_status,
            accountId = accountId,
          ).map(leadStatusRes => {

            assert(person1.value.person_id == person_id)
            assert(person1.value.statusColumn == new_status_with_options)
            assert(person1.value.statusValue == new_status)

            assert(leadStatusRes.isLeft)
          })

        }

        it("should 7: update activity to crm status: found tokens, gets Prospects, prospects fond in CRM and create_record_if_not_exists true and findFieldMapping returns success and usermapping success and buildcontactonject success and createOrUpdateSingleContact success and then update lead status Failure TooManyRequestsError") {


          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            )).repeat(2)

//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))

          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name,intg_acc_tok, dummyProspectObj, moduleType, Some(status_col_name),
              *, *, *, *)
            .returning(Future.successful(
              Right(Seq())
            ))
          ( createInCRMJedisDAO.getLock (_: ProspectId, _: IntegrationType, _: TeamId,_:IntegrationModuleType)(using _: SRLogger))
            .expects(*, *, *, *, *)
            .returning(false)
          (triggerDAO.findFieldMapping)
            .expects(team_id, integration_type, moduleType)
            .returning(Success(Some(updateFieldsMappingForm)))


          (tIntegrationCRMService.findUpdatedUserMapping
          (_: IntegrationTPAccessTokenResponse.FullTokenData, _: Long, _: Long, _: IntegrationType)(_: WSClient, _: ExecutionContext, _: SRLogger))
            .expects(intg_acc_tok, team_id, accountId, integration_type, *, *, *)
            .returning(Future.successful(Right(updatedUserMappingMappingForm)))

          val person_email_capt = CaptureOne[String]()
          (tIntegrationCRMService.createOrUpdateSingleContact
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType, _: ProspectObject, _: UpdateFieldsMappingForm, _: UpdateUserMappingForm, _: Long, _: Long, _: String)
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name,
              intg_acc_tok, moduleType, dummyProspectObj, updateFieldsMappingForm, updatedUserMappingMappingForm, accountId, team_id, capture(person_email_capt),
              *, *, *, *
            ).returning(Future.successful(Right(person_id)))

          val person1 = CaptureOne[CrmLeadStatusUpdate]()
          (tIntegrationCRMService.updateLeadStatus
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType, _: CrmLeadStatusUpdate)
          (_: WSClient, _: ExecutionContext, _: SRLogger)
            )
            .expects(crmIntegrationService.name,
              intg_acc_tok, moduleType, capture(person1),
              *, *, *
            ).returning(
            Future.successful(
              Left(UpdateLeadStatusError.CommonCRMAPIError(err = CommonCRMAPIErrors.TooManyRequestsError(msg = "TooManyRequestsError")))
            ))


          leadStatusService.handleUpdateLeadStatusInCRM(
            event = eventFOrEmailInvalid,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = None,
            statusValue = new_status,
            intg = crMIntegrationInDB.copy(status_column_in_crm = Some(new_status_with_options)),
            accountId = accountId,
          ).map(leadStatusRes => {

            assert(person1.value.person_id == person_id)
            assert(person1.value.statusColumn == new_status_with_options)
            assert(person1.value.statusValue == new_status)

            assert(leadStatusRes.isLeft)
          })

        }

        it("should 8: update activity to crm status: found tokens, gets Prospects, prospects fond in CRM and create_record_if_not_exists true and findFieldMapping returns success and usermapping success and buildcontactonject success and createOrUpdateSingleContact success and then update lead status Failure UnknownError") {


          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            )).repeat(2)

//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))

          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name, intg_acc_tok, dummyProspectObj, moduleType, Some(status_col_name),
              *, *, *, *)
            .returning(Future.successful(
              Right(Seq())
            ))
          ( createInCRMJedisDAO.getLock (_: ProspectId, _: IntegrationType, _: TeamId,_:IntegrationModuleType)(using _: SRLogger))
            .expects(*, *, *, *, *)
            .returning(false)
          (triggerDAO.findFieldMapping)
            .expects(team_id, integration_type, moduleType)
            .returning(Success(Some(updateFieldsMappingForm)))


          (tIntegrationCRMService.findUpdatedUserMapping
          (_: IntegrationTPAccessTokenResponse.FullTokenData, _: Long, _: Long, _: IntegrationType)(_: WSClient, _: ExecutionContext, _: SRLogger))
            .expects(intg_acc_tok, team_id, accountId, integration_type, *, *, *)
            .returning(Future.successful(Right(updatedUserMappingMappingForm)))

          val person_email_capt = CaptureOne[String]()
          (tIntegrationCRMService.createOrUpdateSingleContact
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType, _: ProspectObject, _: UpdateFieldsMappingForm, _: UpdateUserMappingForm, _: Long, _: Long, _: String)
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name,
              intg_acc_tok, moduleType, dummyProspectObj, updateFieldsMappingForm, updatedUserMappingMappingForm, accountId, team_id, capture(person_email_capt),
              *, *, *, *
            ).returning(Future.successful(Right(person_id)))

          val person1 = CaptureOne[CrmLeadStatusUpdate]()
          (tIntegrationCRMService.updateLeadStatus
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType, _: CrmLeadStatusUpdate)
          (_: WSClient, _: ExecutionContext, _: SRLogger)
            )
            .expects(crmIntegrationService.name,
              intg_acc_tok, moduleType, capture(person1),
              *, *, *
            ).returning(
            Future.successful(
              Left(UpdateLeadStatusError.CommonCRMAPIError(err = CommonCRMAPIErrors.UnknownError(msg = "UnknownError")))
            ))


          leadStatusService.handleUpdateLeadStatusInCRM(
            event = eventFOrEmailInvalid,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = None,
            statusValue = new_status,
            intg = crMIntegrationInDB.copy(status_column_in_crm = Some(new_status_with_options)),
            accountId = accountId,
          ).map(leadStatusRes => {

            assert(person1.value.person_id == person_id)
            assert(person1.value.statusColumn == new_status_with_options)
            assert(person1.value.statusValue == new_status)

            assert(leadStatusRes.isLeft)
          })

        }

        it("should 9: update activity to crm status: found tokens, gets Prospects, prospects fond in CRM and create_record_if_not_exists true and findFieldMapping returns success and usermapping success and buildcontactonject success and createOrUpdateSingleContact success and then update lead status success") {

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            )).repeat(2)

//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))

          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name, intg_acc_tok, dummyProspectObj, moduleType, Some(status_col_name),
              *, *, *, *)
            .returning(Future.successful(
              Right(Seq())
            ))
          ( createInCRMJedisDAO.getLock (_: ProspectId, _: IntegrationType, _: TeamId,_:IntegrationModuleType)(using _: SRLogger))
            .expects(*, *, *, *, *)
            .returning(false)
          (triggerDAO.findFieldMapping)
            .expects(team_id, integration_type, moduleType)
            .returning(Success(Some(updateFieldsMappingForm)))


          (tIntegrationCRMService.findUpdatedUserMapping
          (_: IntegrationTPAccessTokenResponse.FullTokenData, _: Long, _: Long, _: IntegrationType)(_: WSClient, _: ExecutionContext, _: SRLogger))
            .expects(intg_acc_tok, team_id, accountId, integration_type, *, *, *)
            .returning(Future.successful(Right(updatedUserMappingMappingForm)))


          val person_email_capt = CaptureOne[String]()
          (tIntegrationCRMService.createOrUpdateSingleContact
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType, _: ProspectObject, _: UpdateFieldsMappingForm, _: UpdateUserMappingForm, _: Long, _: Long, _: String)
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name,
              intg_acc_tok, moduleType, dummyProspectObj, updateFieldsMappingForm, updatedUserMappingMappingForm, accountId, team_id, capture(person_email_capt),
              *, *, *, *
            ).returning(Future.successful(Right(person_id)))

          val person1 = CaptureOne[CrmLeadStatusUpdate]()
          (tIntegrationCRMService.updateLeadStatus
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType, _: CrmLeadStatusUpdate)
          (_: WSClient, _: ExecutionContext, _: SRLogger)
            )
            .expects(crmIntegrationService.name,
              intg_acc_tok, moduleType, capture(person1),
              *, *, *
            ).returning(
            Future.successful(
              Right(person_id)
            ))

          leadStatusService.handleUpdateLeadStatusInCRM(
            event = eventFOrEmailInvalid,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = None,
            statusValue = new_status,
            intg = crMIntegrationInDB.copy(status_column_in_crm = Some(new_status_with_options)),
            accountId = accountId,
          ).map(leadStatusRes => {

            assert(person1.value.person_id == person_id)
            assert(person1.value.statusColumn == new_status_with_options)
            assert(person1.value.statusValue == new_status)

            assert(leadStatusRes === Right(Seq(1)))
          })

        }

      }

      describe("PATH 3.1 When prospect not found in CRM AND CREATE_IF_NOT_EXISTS IS FALSE") {

        it("should 10: update activity to crm status: found tokens, gets Prospects, prospects fond in CRM and create_record_if_not_exists false") {

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            ))

//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))

          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name, intg_acc_tok, dummyProspectObj, moduleType, Some(status_col_name),
              *, *, *, *)
            .returning(Future.successful(
              Right(Seq())
            ))

          leadStatusService.handleUpdateLeadStatusInCRM(
            event = eventFOrEmailInvalid,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = None,
            statusValue = status_col_value,
            intg = crMIntegrationInDB.copy(create_record_if_not_exists = false),
            accountId = accountId,
          )
            .map(leadStatusRes => {
              assert(leadStatusRes.isLeft)
            })

        }

      }
    }

    describe("handleUpdateLeadStatusInCRM with NEW_REPLY and emailScheduledids empty") {

      val eventForNewReplyWithPids = EventType.NEW_REPLY
      val emptyEmailSchduledIds: Option[Seq[Long]] = None

      describe("PATH 1.5 When prospect found in CRM") {

        it("should 1: update activity to crm status tokens not found") {

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))

//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.failed(new Exception("Error while fetching tokens. Please try again, or contact support.")))

          leadStatusService.handleUpdateLeadStatusInCRM(
            event = eventForNewReplyWithPids,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = emptyEmailSchduledIds,
            statusValue = status_col_value,
            intg = crMIntegrationInDB,
            accountId = accountId,
          ).map(_ => {
            assert(false)
          })
            .recover { case e =>

              val exeMatch = e.getMessage.contains("Error while fetching tokens")

              Logger.info(s"\n\n\n\n in recover $exeMatch")

              exeMatch shouldBe true
            }

        }

        it("should 2: update activity to crm status tokens found but prospect did't found with given id") {

          /*(tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            ))*/

//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Failure(new Exception("Error while fetching prospect. Please try again, or contact support.")))

          leadStatusService.handleUpdateLeadStatusInCRM(
            event = eventForNewReplyWithPids,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = emptyEmailSchduledIds,
            statusValue = status_col_value,
            intg = crMIntegrationInDB,
            accountId = accountId,
          ).map(leadStatusRes => {
            assert(leadStatusRes.isLeft)
          })

        }

        it("should 3 update activity to crm status: found tokens, gets Prospects, prospects fond in CRM and updateLeadStatus is Success") {

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            )).repeat(2)

//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))


          val status_col_name = CaptureOne[Option[String]]()
          val email_capt = CaptureOne[String]()
          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name, intg_acc_tok, capture(email_capt), moduleType, capture(status_col_name),
              *, *, *, *)
            .returning(Future.successful(
              Right(
                Seq(IntegrationContactResponse(
                  id = person_id, email = Some(prospect_email), status = Some(status_col_value)
                )
                )
              )))

          val person1 = CaptureOne[CrmLeadStatusUpdate]()

          (tIntegrationCRMService.updateLeadStatus
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType, _: CrmLeadStatusUpdate)
          (_: WSClient, _: ExecutionContext, _: SRLogger)
            )
            .expects(crmIntegrationService.name,
              intg_acc_tok, moduleType, capture(person1),
              *, *, *
            ).returning(
            Future.successful(
              Right(person_id)
            )
          )

          leadStatusService.handleUpdateLeadStatusInCRM(
            event = eventForNewReplyWithPids,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = emptyEmailSchduledIds,
            statusValue = new_status,
            intg = crMIntegrationInDB.copy(status_column_in_crm = Some(new_status_with_options)),
            accountId = accountId,
          ).map(leadStatusRes => {
            assert(person1.value.person_id == person_id)
            assert(person1.value.statusColumn == new_status_with_options)
            assert(person1.value.statusValue == new_status)

            assert(leadStatusRes === Right(Seq(1)))
          })

        }

        it("should 4: update activity to crm status: found tokens, gets Prospects, prospects fond in CRM and updateLeadStatus is Failure") {

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            )).repeat(2)

//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))


          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name, intg_acc_tok, dummyProspectObj, moduleType, Some(status_col_name),
              *, *, *, *)
            .returning(Future.successful(Right(Seq(IntegrationContactResponse(
              id = person_id, email = Some(prospect_email), status = Some(status_col_value)
            )))))

          val person1 = CaptureOne[CrmLeadStatusUpdate]()

          (tIntegrationCRMService.updateLeadStatus
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType, _: CrmLeadStatusUpdate)
          (_: WSClient, _: ExecutionContext, _: SRLogger)
            )
            .expects(crmIntegrationService.name,
              intg_acc_tok, moduleType, capture(person1),
              *, *, *
            ).returning(Future.successful(
            Left(UpdateLeadStatusError.CommonCRMAPIError(err = CommonCRMAPIErrors.InternalServerError(msg = "Internal server error")))
          ))

          leadStatusService.handleUpdateLeadStatusInCRM(
            event = eventForNewReplyWithPids,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = emptyEmailSchduledIds,
            statusValue = new_status,
            intg = crMIntegrationInDB.copy(status_column_in_crm = Some(new_status_with_options)),
            accountId = accountId,
          ).map(leadStatusRes => {
            assert(person1.value.person_id == person_id)
            assert(person1.value.statusColumn == new_status_with_options)
            assert(person1.value.statusValue == new_status)

            assert(leadStatusRes.isLeft)
          })

        }

      }

      describe("PATH 2.2 When prospect not found in CRM AND CREATE_IF_NOT_EXISTS IS TRUE") {

        it("should 1: update activity to crm status: found tokens, gets Prospects, prospects fond in CRM and create_record_if_not_exists true nd findFieldMapping exception throws") {


          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            ))

//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)


          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))

          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name, intg_acc_tok, dummyProspectObj, moduleType, Some(status_col_name),
              *, *, *, *)
            .returning(Future.successful(
              Right(Seq())
            ))
          ( createInCRMJedisDAO.getLock (_: ProspectId, _: IntegrationType, _: TeamId,_:IntegrationModuleType)(using _: SRLogger))
            .expects(*, *, *, *, *)
            .returning(false)

          (triggerDAO.findFieldMapping)
            .expects(team_id, integration_type, moduleType)
            .returning(Failure(new Exception("findFieldMapping error")))

          leadStatusService.handleUpdateLeadStatusInCRM(
            event = eventForNewReplyWithPids,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = emptyEmailSchduledIds,
            statusValue = status_col_value,
            intg = crMIntegrationInDB,
            accountId = accountId,
          ).map(leadStatusRes => {
            assert(leadStatusRes.isLeft)
          })

        }

        it("should 2: update activity to crm status: found tokens, gets Prospects, prospects fond in CRM and create_record_if_not_exists true and findFieldMapping returns NONE") {

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            ))


//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)


          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))

          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name, intg_acc_tok, dummyProspectObj, moduleType, Some(status_col_name),
              *, *, *, *)
            .returning(Future.successful(
              Right(Seq())
            ))
          ( createInCRMJedisDAO.getLock (_: ProspectId, _: IntegrationType, _: TeamId,_:IntegrationModuleType)(using _: SRLogger))
            .expects(*, *, *, *, *)
            .returning(false)

          (triggerDAO.findFieldMapping)
            .expects(team_id, integration_type, moduleType)
            .returning(Success(None))

          leadStatusService.handleUpdateLeadStatusInCRM(
            event = eventForNewReplyWithPids,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = emptyEmailSchduledIds,
            statusValue = status_col_value,
            intg = crMIntegrationInDB,
            accountId = accountId,
          ).map(leadStatusRes => {
            assert(leadStatusRes.isLeft)
          })

        }

        it("should 3: update activity to crm status: found tokens, gets Prospects, prospects fond in CRM and create_record_if_not_exists true and findFieldMapping returns success and usermapping success and buildcontactonject success and createOrUpdateSingleContact return None") {

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            ))

//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))

          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name, intg_acc_tok, dummyProspectObj, moduleType, Some(status_col_name),
              *, *, *, *)
            .returning(Future.successful(
              Right(Seq())
            ))
          ( createInCRMJedisDAO.getLock (_: ProspectId, _: IntegrationType, _: TeamId,_:IntegrationModuleType)(using _: SRLogger))
            .expects(*, *, *, *, *)
            .returning(false)
          (triggerDAO.findFieldMapping)
            .expects(team_id, integration_type, moduleType)
            .returning(Success(Some(updateFieldsMappingForm)))


          (tIntegrationCRMService.findUpdatedUserMapping
          (_: IntegrationTPAccessTokenResponse.FullTokenData, _: Long, _: Long, _: IntegrationType)(_: WSClient, _: ExecutionContext, _: SRLogger))
            .expects(intg_acc_tok, team_id, accountId, integration_type, *, *, *)
            .returning(Future.successful(Right(updatedUserMappingMappingForm)))



          val person_email_capt = CaptureOne[String]()
          (tIntegrationCRMService.createOrUpdateSingleContact
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType, _: ProspectObject, _: UpdateFieldsMappingForm, _: UpdateUserMappingForm, _: Long, _: Long, _: String)
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name,
              intg_acc_tok, moduleType, dummyProspectObj, updateFieldsMappingForm, updatedUserMappingMappingForm, accountId, team_id, capture(person_email_capt),
              *, *, *, *
            ).returning(Future.successful(Left(
            CreateOrUpdateSingleContactsError.CommonCRMAPIError(err = CommonCRMAPIErrors.UnknownError(msg = "Unknown error"))
          )))


          leadStatusService.handleUpdateLeadStatusInCRM(
            event = eventForNewReplyWithPids,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = emptyEmailSchduledIds,
            statusValue = status_col_value,
            intg = crMIntegrationInDB,
            accountId = accountId,
          ).map(leadStatusRes => {
            assert(leadStatusRes.isLeft)
          })

        }

        it("should 4: update activity to crm status: found tokens, gets Prospects, prospects fond in CRM and create_record_if_not_exists true and findFieldMapping returns success and usermapping success and buildcontactonject success and createOrUpdateSingleContact success and then update lead status Failure InternalServerError") {

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            )).repeat(2)

//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))

          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name, intg_acc_tok, dummyProspectObj, moduleType, Some(status_col_name),
              *, *, *, *)
            .returning(Future.successful(
              Right(Seq())
            ))
          ( createInCRMJedisDAO.getLock (_: ProspectId, _: IntegrationType, _: TeamId,_:IntegrationModuleType)(using _: SRLogger))
            .expects(*, *, *, *, *)
            .returning(false)
          (triggerDAO.findFieldMapping)
            .expects(team_id, integration_type, moduleType)
            .returning(Success(Some(updateFieldsMappingForm)))


          (tIntegrationCRMService.findUpdatedUserMapping
          (_: IntegrationTPAccessTokenResponse.FullTokenData, _: Long, _: Long, _: IntegrationType)(_: WSClient, _: ExecutionContext, _: SRLogger))
            .expects(intg_acc_tok, team_id, accountId, integration_type, *, *, *)
            .returning(Future.successful(Right(updatedUserMappingMappingForm)))


          val person_email_capt = CaptureOne[String]()
          (tIntegrationCRMService.createOrUpdateSingleContact
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType, _: ProspectObject, _: UpdateFieldsMappingForm, _: UpdateUserMappingForm, _: Long, _: Long, _: String)
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name,
              intg_acc_tok, moduleType, dummyProspectObj, updateFieldsMappingForm, updatedUserMappingMappingForm, accountId, team_id, capture(person_email_capt),
              *, *, *, *
            ).returning(Future.successful(Right(person_id)))

          val person1 = CaptureOne[CrmLeadStatusUpdate]()

          (tIntegrationCRMService.updateLeadStatus
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType, _: CrmLeadStatusUpdate)
          (_: WSClient, _: ExecutionContext, _: SRLogger)
            )
            .expects(crmIntegrationService.name,
              intg_acc_tok, moduleType, capture(person1),
              *, *, *
            ).returning(
            Future.successful(
              Left(UpdateLeadStatusError.CommonCRMAPIError(err = CommonCRMAPIErrors.InternalServerError(msg = "Internal server error")))
            ))


          leadStatusService.handleUpdateLeadStatusInCRM(
            event = eventForNewReplyWithPids,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = emptyEmailSchduledIds,
            statusValue = new_status,
            intg = crMIntegrationInDB.copy(status_column_in_crm = Some(new_status_with_options)),
            accountId = accountId,
          ).map(leadStatusRes => {
            assert(person1.value.person_id == person_id)
            assert(person1.value.statusColumn == new_status_with_options)
            assert(person1.value.statusValue == new_status)

            assert(leadStatusRes.isLeft)
          })

        }

        it("should 5: update activity to crm status: found tokens, gets Prospects, prospects fond in CRM and create_record_if_not_exists true and findFieldMapping returns success and usermapping success and buildcontactonject success and createOrUpdateSingleContact success and then update lead status Failure NotFoundError") {

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            )).repeat(2)


//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))

          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name, intg_acc_tok, dummyProspectObj, moduleType, Some(status_col_name),
              *, *, *, *)
            .returning(Future.successful(
              Right(Seq())
            ))
          ( createInCRMJedisDAO.getLock (_: ProspectId, _: IntegrationType, _: TeamId, _:IntegrationModuleType)(using _: SRLogger))
            .expects(*, *, *, *, *)
            .returning(false)
          (triggerDAO.findFieldMapping)
            .expects(team_id, integration_type, moduleType)
            .returning(Success(Some(updateFieldsMappingForm)))


          (tIntegrationCRMService.findUpdatedUserMapping
          (_: IntegrationTPAccessTokenResponse.FullTokenData, _: Long, _: Long, _: IntegrationType)(_: WSClient, _: ExecutionContext, _: SRLogger))
            .expects(intg_acc_tok, team_id, accountId, integration_type, *, *, *)
            .returning(Future.successful(Right(updatedUserMappingMappingForm)))


          val person_email_capt = CaptureOne[String]()
          (tIntegrationCRMService.createOrUpdateSingleContact
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType, _: ProspectObject, _: UpdateFieldsMappingForm, _: UpdateUserMappingForm, _: Long, _: Long, _: String)
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name,
              intg_acc_tok, moduleType, dummyProspectObj, updateFieldsMappingForm, updatedUserMappingMappingForm, accountId, team_id, capture(person_email_capt),
              *, *, *, *
            ).returning(Future.successful(Right(person_id)))

          val person1 = CaptureOne[CrmLeadStatusUpdate]()

          (tIntegrationCRMService.updateLeadStatus
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType, _: CrmLeadStatusUpdate)
          (_: WSClient, _: ExecutionContext, _: SRLogger)
            )
            .expects(crmIntegrationService.name,
              intg_acc_tok, moduleType, capture(person1),
              *, *, *
            ).returning(
            Future.successful(
              Left(UpdateLeadStatusError.CommonCRMAPIError(err = CommonCRMAPIErrors.NotFoundError(msg = "Resource not error")))
            ))


          leadStatusService.handleUpdateLeadStatusInCRM(
            event = eventForNewReplyWithPids,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = emptyEmailSchduledIds,
            statusValue = new_status,
            intg = crMIntegrationInDB.copy(status_column_in_crm = Some(new_status_with_options)),
            accountId = accountId,
          ).map(leadStatusRes => {
            assert(person1.value.person_id == person_id)
            assert(person1.value.statusColumn == new_status_with_options)
            assert(person1.value.statusValue == new_status)

            assert(leadStatusRes.isLeft)
          })

        }

        it("should 6: update activity to crm status: found tokens, gets Prospects, prospects fond in CRM and create_record_if_not_exists true and findFieldMapping returns success and usermapping success and buildcontactonject success and createOrUpdateSingleContact success and then update lead status Failure UnAuthorizedError") {

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            )).repeat(2)


//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))

          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name, intg_acc_tok, dummyProspectObj, moduleType, Some(status_col_name),
              *, *, *, *)
            .returning(Future.successful(
              Right(Seq())
            ))
          ( createInCRMJedisDAO.getLock (_: ProspectId, _: IntegrationType, _: TeamId, _:IntegrationModuleType)(using _: SRLogger))
            .expects(*, *, *, *, *)
            .returning(false)
          (triggerDAO.findFieldMapping)
            .expects(team_id, integration_type, moduleType)
            .returning(Success(Some(updateFieldsMappingForm)))


          (tIntegrationCRMService.findUpdatedUserMapping
          (_: IntegrationTPAccessTokenResponse.FullTokenData, _: Long, _: Long, _: IntegrationType)(_: WSClient, _: ExecutionContext, _: SRLogger))
            .expects(intg_acc_tok, team_id, accountId, integration_type, *, *, *)
            .returning(Future.successful(Right(updatedUserMappingMappingForm)))


          val person_email_capt = CaptureOne[String]()
          (tIntegrationCRMService.createOrUpdateSingleContact
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType, _: ProspectObject, _: UpdateFieldsMappingForm, _: UpdateUserMappingForm, _: Long, _: Long, _: String)
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name,
              intg_acc_tok, moduleType, dummyProspectObj, updateFieldsMappingForm, updatedUserMappingMappingForm, accountId, team_id, capture(person_email_capt),
              *, *, *, *
            ).returning(Future.successful(Right(person_id)))

          val person1 = CaptureOne[CrmLeadStatusUpdate]()

          (tIntegrationCRMService.updateLeadStatus
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType, _: CrmLeadStatusUpdate)
          (_: WSClient, _: ExecutionContext, _: SRLogger)
            )
            .expects(crmIntegrationService.name,
              intg_acc_tok, moduleType, capture(person1),
              *, *, *
            ).returning(
            Future.successful(
              Left(UpdateLeadStatusError.CommonCRMAPIError(err = CommonCRMAPIErrors.UnAuthorizedError(msg = "UnAuthorizedError")))
            ))


          leadStatusService.handleUpdateLeadStatusInCRM(
            event = eventForNewReplyWithPids,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = emptyEmailSchduledIds,
            statusValue = new_status,
            intg = crMIntegrationInDB.copy(status_column_in_crm = Some(new_status_with_options)),
            accountId = accountId,
          ).map(leadStatusRes => {
            assert(person1.value.person_id == person_id)
            assert(person1.value.statusColumn == new_status_with_options)
            assert(person1.value.statusValue == new_status)

            assert(leadStatusRes.isLeft)
          })

        }

        it("should 7: update activity to crm status: found tokens, gets Prospects, prospects fond in CRM and create_record_if_not_exists true and findFieldMapping returns success and usermapping success and buildcontactonject success and createOrUpdateSingleContact success and then update lead status Failure TooManyRequestsError") {

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            )).repeat(2)


//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))

          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name, intg_acc_tok, dummyProspectObj, moduleType, Some(status_col_name),
              *, *, *, *)
            .returning(Future.successful(
              Right(Seq())
            ))
          ( createInCRMJedisDAO.getLock (_: ProspectId, _: IntegrationType, _: TeamId,_:IntegrationModuleType)(using _: SRLogger))
            .expects(*, *, *, *, *)
            .returning(false)
          (triggerDAO.findFieldMapping)
            .expects(team_id, integration_type, moduleType)
            .returning(Success(Some(updateFieldsMappingForm)))


          (tIntegrationCRMService.findUpdatedUserMapping
          (_: IntegrationTPAccessTokenResponse.FullTokenData, _: Long, _: Long, _: IntegrationType)(_: WSClient, _: ExecutionContext, _: SRLogger))
            .expects(intg_acc_tok, team_id, accountId, integration_type, *, *, *)
            .returning(Future.successful(Right(updatedUserMappingMappingForm)))


          val person_email_capt = CaptureOne[String]()
          (tIntegrationCRMService.createOrUpdateSingleContact
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType, _: ProspectObject, _: UpdateFieldsMappingForm, _: UpdateUserMappingForm, _: Long, _: Long, _: String)
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name,
              intg_acc_tok, moduleType, dummyProspectObj, updateFieldsMappingForm, updatedUserMappingMappingForm, accountId, team_id, capture(person_email_capt),
              *, *, *, *
            ).returning(Future.successful(Right(person_id)))

          val person1 = CaptureOne[CrmLeadStatusUpdate]()

          (tIntegrationCRMService.updateLeadStatus
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType, _: CrmLeadStatusUpdate)
          (_: WSClient, _: ExecutionContext, _: SRLogger)
            )
            .expects(crmIntegrationService.name,
              intg_acc_tok, moduleType, capture(person1),
              *, *, *
            ).returning(
            Future.successful(
              Left(UpdateLeadStatusError.CommonCRMAPIError(err = CommonCRMAPIErrors.TooManyRequestsError(msg = "TooManyRequestsError")))
            ))


          leadStatusService.handleUpdateLeadStatusInCRM(
            event = eventForNewReplyWithPids,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = emptyEmailSchduledIds,
            statusValue = new_status,
            intg = crMIntegrationInDB.copy(status_column_in_crm = Some(new_status_with_options)),
            accountId = accountId,
          ).map(leadStatusRes => {
            assert(person1.value.person_id == person_id)
            assert(person1.value.statusColumn == new_status_with_options)
            assert(person1.value.statusValue == new_status)

            assert(leadStatusRes.isLeft)
          })

        }

        it("should 9: update activity to crm status: found tokens, gets Prospects, prospects fond in CRM and create_record_if_not_exists true and findFieldMapping returns success and usermapping success and buildcontactonject success and createOrUpdateSingleContact success and then update lead status Failure UnknownError") {

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            )).repeat(2)


//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))

          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name, intg_acc_tok, dummyProspectObj, moduleType, Some(status_col_name),
              *, *, *, *)
            .returning(Future.successful(
              Right(Seq())
            ))
          ( createInCRMJedisDAO.getLock (_: ProspectId, _: IntegrationType, _: TeamId,_:IntegrationModuleType)(using _: SRLogger))
            .expects(*, *, *, *, *)
            .returning(false)
          (triggerDAO.findFieldMapping)
            .expects(team_id, integration_type, moduleType)
            .returning(Success(Some(updateFieldsMappingForm)))


          (tIntegrationCRMService.findUpdatedUserMapping
          (_: IntegrationTPAccessTokenResponse.FullTokenData, _: Long, _: Long, _: IntegrationType)(_: WSClient, _: ExecutionContext, _: SRLogger))
            .expects(intg_acc_tok, team_id, accountId, integration_type, *, *, *)
            .returning(Future.successful(Right(updatedUserMappingMappingForm)))


          val person_email_capt = CaptureOne[String]()
          (tIntegrationCRMService.createOrUpdateSingleContact
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType, _: ProspectObject, _: UpdateFieldsMappingForm, _: UpdateUserMappingForm, _: Long, _: Long, _: String)
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name,
              intg_acc_tok, moduleType, dummyProspectObj, updateFieldsMappingForm, updatedUserMappingMappingForm, accountId, team_id, capture(person_email_capt),
              *, *, *, *
            ).returning(Future.successful(Right(person_id)))

          val person1 = CaptureOne[CrmLeadStatusUpdate]()

          (tIntegrationCRMService.updateLeadStatus
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType, _: CrmLeadStatusUpdate)
          (_: WSClient, _: ExecutionContext, _: SRLogger)
            )
            .expects(crmIntegrationService.name,
              intg_acc_tok, moduleType, capture(person1),
              *, *, *
            ).returning(
            Future.successful(
              Left(UpdateLeadStatusError.CommonCRMAPIError(err = CommonCRMAPIErrors.UnknownError(msg = "UnknownError")))
            ))


          leadStatusService.handleUpdateLeadStatusInCRM(
            event = eventForNewReplyWithPids,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = emptyEmailSchduledIds,
            statusValue = new_status,
            intg = crMIntegrationInDB.copy(status_column_in_crm = Some(new_status_with_options)),
            accountId = accountId,
          ).map(leadStatusRes => {
            assert(person1.value.person_id == person_id)
            assert(person1.value.statusColumn == new_status_with_options)
            assert(person1.value.statusValue == new_status)

            assert(leadStatusRes.isLeft)
          })

        }

        it("should 10: update activity to crm status: found tokens, gets Prospects, prospects fond in CRM and create_record_if_not_exists true and findFieldMapping returns success and usermapping success and buildcontactonject success and createOrUpdateSingleContact success and then update lead status success") {

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            )).repeat(2)


//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))

          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name, intg_acc_tok, dummyProspectObj, moduleType, Some(status_col_name),
              *, *, *, *)
            .returning(Future.successful(
              Right(Seq())
            ))
          ( createInCRMJedisDAO.getLock (_: ProspectId, _: IntegrationType, _: TeamId, _:IntegrationModuleType)(using _: SRLogger))
            .expects(*, *, *, *, *)
            .returning(false)
          (triggerDAO.findFieldMapping)
            .expects(team_id, integration_type, moduleType)
            .returning(Success(Some(updateFieldsMappingForm)))


          (tIntegrationCRMService.findUpdatedUserMapping
          (_: IntegrationTPAccessTokenResponse.FullTokenData, _: Long, _: Long, _: IntegrationType)(_: WSClient, _: ExecutionContext, _: SRLogger))
            .expects(intg_acc_tok, team_id, accountId, integration_type, *, *, *)
            .returning(Future.successful(Right(updatedUserMappingMappingForm)))


          val person_email_capt = CaptureOne[String]()
          (tIntegrationCRMService.createOrUpdateSingleContact
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType, _: ProspectObject, _: UpdateFieldsMappingForm, _: UpdateUserMappingForm, _: Long, _: Long, _: String)
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name,
              intg_acc_tok, moduleType, dummyProspectObj, updateFieldsMappingForm, updatedUserMappingMappingForm, accountId, team_id, capture(person_email_capt),
              *, *, *, *
            ).returning(Future.successful(Right(person_id)))

          val person1 = CaptureOne[CrmLeadStatusUpdate]()

          (tIntegrationCRMService.updateLeadStatus
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType, _: CrmLeadStatusUpdate)
          (_: WSClient, _: ExecutionContext, _: SRLogger)
            )
            .expects(crmIntegrationService.name,
              intg_acc_tok, moduleType, capture(person1),
              *, *, *
            ).returning(
            Future.successful(
              Right(person_id)
            )
          )

          leadStatusService.handleUpdateLeadStatusInCRM(
            event = eventForNewReplyWithPids,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = emptyEmailSchduledIds,
            statusValue = new_status,
            intg = crMIntegrationInDB.copy(status_column_in_crm = Some(new_status_with_options)),
            accountId = accountId,
          ).map(leadStatusRes => {
            assert(person1.value.person_id == person_id)
            assert(person1.value.statusColumn == new_status_with_options)
            assert(person1.value.statusValue == new_status)

            assert(leadStatusRes === Right(Seq(1)))
          })

        }

      }

      describe("PATH 3.2 When prospect not found in CRM AND CREATE_IF_NOT_EXISTS IS FALSE") {

        it("should 10: update activity to crm status: found tokens, gets Prospects, prospects fond in CRM and create_record_if_not_exists false") {

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            ))


//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)


          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))

          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name, intg_acc_tok, dummyProspectObj, moduleType, Some(status_col_name),
              *, *, *, *)
            .returning(Future.successful(
              Right(Seq())
            ))
          leadStatusService.handleUpdateLeadStatusInCRM(
            event = eventForNewReplyWithPids,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = emptyEmailSchduledIds,
            statusValue = status_col_value,
            intg = crMIntegrationInDB.copy(create_record_if_not_exists = false),
            accountId = accountId,
          )
            .map(leadStatusRes => {
              assert(leadStatusRes.isLeft)
            })

        }

      }

    }

    describe("handleUpdateLeadStatusInCRM with EMAIL_OPEN and emailScheduledids not empty") {

      val emailOpenEvent = EventType.EMAIL_OPENED
      val emailSchduledIdsForEmailOpen: Option[Seq[Long]] = Some(Seq(786786))
      val getOnlyNewReplies = false
      val dummyEmailScheduledObjForUpdateLeadSts = EmailReceivedForUpdateLeadStatus(
        id = emailSchduledIdsForEmailOpen.get.head,
        prospect_id = prospect_id
      )

      describe("PATH 1.6 When prospect found in CRM") {

        it("should 1: update activity to crm status tokens not found") {

          (emailScheduledDAO.fetchProspectIdByEmailScheduledIdForUpdateLeadStatus)
            .expects(emailSchduledIdsForEmailOpen.get, getOnlyNewReplies)
            .returning(Seq(dummyEmailScheduledObjForUpdateLeadSts))

//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.failed(new Exception("Error while fetching tokens. Please try again, or contact support.")))

          leadStatusService.handleUpdateLeadStatusInCRM(
            event = emailOpenEvent,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = emailSchduledIdsForEmailOpen,
            statusValue = status_col_value,
            intg = crMIntegrationInDB,
            accountId = accountId,
          ).map(_ => {
            assert(false)
          })
            .recover { case e =>

              val exeMatch = e.getMessage.contains("Error while fetching tokens")

              Logger.info(s"\n\n\n\n in recover $exeMatch")

              exeMatch shouldBe true
            }

        }

        it("should 2: update activity to crm status tokens found but prospect did't found with given id") {

          (emailScheduledDAO.fetchProspectIdByEmailScheduledIdForUpdateLeadStatus)
            .expects(emailSchduledIdsForEmailOpen.get, getOnlyNewReplies)
            .returning(Seq(dummyEmailScheduledObjForUpdateLeadSts))

//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          /*(tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            ))*/

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Failure(new Exception("Error while fetching prospect. Please try again, or contact support.")))

          leadStatusService.handleUpdateLeadStatusInCRM(
            event = emailOpenEvent,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = emailSchduledIdsForEmailOpen,
            statusValue = status_col_value,
            intg = crMIntegrationInDB,
            accountId = accountId,
          ).map(leadStatusRes => {
            assert(leadStatusRes.isLeft)
          })

        }

        it("should 3 update activity to crm status: found tokens, gets Prospects, prospects fond in CRM and updateLeadStatus is Success") {

          (emailScheduledDAO.fetchProspectIdByEmailScheduledIdForUpdateLeadStatus)
            .expects(emailSchduledIdsForEmailOpen.get, getOnlyNewReplies)
            .returning(Seq(dummyEmailScheduledObjForUpdateLeadSts))

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            )).repeat(2)

//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))


          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name, intg_acc_tok, dummyProspectObj, moduleType, Some(status_col_name),
              *, *, *, *)
            .returning(Future.successful(Right(Seq(IntegrationContactResponse(
              id = person_id, email = Some(prospect_email), status = Some(status_col_value)
            )))))


          val person1 = CaptureOne[CrmLeadStatusUpdate]()

          (tIntegrationCRMService.updateLeadStatus
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType, _: CrmLeadStatusUpdate)
          (_: WSClient, _: ExecutionContext, _: SRLogger)
            )
            .expects(crmIntegrationService.name,
              intg_acc_tok, moduleType, capture(person1),
              *, *, *
            ).returning(
            Future.successful(
              Right(person_id)
            )
          )

          leadStatusService.handleUpdateLeadStatusInCRM(
            event = emailOpenEvent,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = emailSchduledIdsForEmailOpen,
            statusValue = new_status,
            intg = crMIntegrationInDB.copy(status_column_in_crm = Some(new_status_with_options)),
            accountId = accountId,
          ).map(leadStatusRes => {
            assert(person1.value.person_id == person_id)
            assert(person1.value.statusColumn == new_status_with_options)
            assert(person1.value.statusValue == new_status)

            assert(leadStatusRes === Right(Seq(1)))
          })

        }

        it("should 4: update activity to crm status: found tokens, gets Prospects, prospects fond in CRM and updateLeadStatus is Failure InternalServerError") {

          (emailScheduledDAO.fetchProspectIdByEmailScheduledIdForUpdateLeadStatus)
            .expects(emailSchduledIdsForEmailOpen.get, getOnlyNewReplies)
            .returning(Seq(dummyEmailScheduledObjForUpdateLeadSts))

//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            )).repeat(2)

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))


          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name, intg_acc_tok, dummyProspectObj, moduleType, Some(status_col_name),
              *, *, *, *)
            .returning(Future.successful(Right(Seq(IntegrationContactResponse(
              id = person_id, email = Some(prospect_email), status = Some(status_col_value)
            )))))

          val person1 = CaptureOne[CrmLeadStatusUpdate]()

          (tIntegrationCRMService.updateLeadStatus
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType, _: CrmLeadStatusUpdate)
          (_: WSClient, _: ExecutionContext, _: SRLogger)
            )
            .expects(crmIntegrationService.name,
              intg_acc_tok, moduleType, capture(person1),
              *, *, *
            ).returning(
            Future.successful(
              Left(UpdateLeadStatusError.CommonCRMAPIError(err = CommonCRMAPIErrors.InternalServerError(msg = "Internal server error")))
            ))

          leadStatusService.handleUpdateLeadStatusInCRM(
            event = emailOpenEvent,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = emailSchduledIdsForEmailOpen,
            statusValue = new_status,
            intg = crMIntegrationInDB.copy(status_column_in_crm = Some(new_status_with_options)),
            accountId = accountId,
          ).map(leadStatusRes => {
            assert(person1.value.person_id == person_id)
            assert(person1.value.statusColumn == new_status_with_options)
            assert(person1.value.statusValue == new_status)

            assert(leadStatusRes.isLeft)
          })

        }

        it("should 5: update activity to crm status: found tokens, gets Prospects, prospects fond in CRM and updateLeadStatus is Failure NotFoundError") {

          (emailScheduledDAO.fetchProspectIdByEmailScheduledIdForUpdateLeadStatus)
            .expects(emailSchduledIdsForEmailOpen.get, getOnlyNewReplies)
            .returning(Seq(dummyEmailScheduledObjForUpdateLeadSts))


//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            )).repeat(2)

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))


          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name, intg_acc_tok, dummyProspectObj, moduleType, Some(status_col_name),
              *, *, *, *)
            .returning(Future.successful(Right(Seq(IntegrationContactResponse(
              id = person_id, email = Some(prospect_email), status = Some(status_col_value)
            )))))

          val person1 = CaptureOne[CrmLeadStatusUpdate]()

          (tIntegrationCRMService.updateLeadStatus
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType, _: CrmLeadStatusUpdate)
          (_: WSClient, _: ExecutionContext, _: SRLogger)
            )
            .expects(crmIntegrationService.name,
              intg_acc_tok, moduleType, capture(person1),
              *, *, *
            ).returning(
            Future.successful(
              Left(UpdateLeadStatusError.CommonCRMAPIError(err = CommonCRMAPIErrors.NotFoundError(msg = "Resource not found error")))
            ))

          leadStatusService.handleUpdateLeadStatusInCRM(
            event = emailOpenEvent,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = emailSchduledIdsForEmailOpen,
            statusValue = new_status,
            intg = crMIntegrationInDB.copy(status_column_in_crm = Some(new_status_with_options)),
            accountId = accountId,
          ).map(leadStatusRes => {
            assert(person1.value.person_id == person_id)
            assert(person1.value.statusColumn == new_status_with_options)
            assert(person1.value.statusValue == new_status)

            assert(leadStatusRes.isLeft)
          })

        }

        it("should 6: update activity to crm status: found tokens, gets Prospects, prospects fond in CRM and updateLeadStatus is Failure UnAuthorizedError") {

          (emailScheduledDAO.fetchProspectIdByEmailScheduledIdForUpdateLeadStatus)
            .expects(emailSchduledIdsForEmailOpen.get, getOnlyNewReplies)
            .returning(Seq(dummyEmailScheduledObjForUpdateLeadSts))

//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            )).repeat(2)

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))


          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name, intg_acc_tok, dummyProspectObj, moduleType, Some(status_col_name),
              *, *, *, *)
            .returning(Future.successful(Right(Seq(IntegrationContactResponse(
              id = person_id, email = Some(prospect_email), status = Some(status_col_value)
            )))))

          val person1 = CaptureOne[CrmLeadStatusUpdate]()

          (tIntegrationCRMService.updateLeadStatus
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType, _: CrmLeadStatusUpdate)
          (_: WSClient, _: ExecutionContext, _: SRLogger)
            )
            .expects(crmIntegrationService.name,
              intg_acc_tok, moduleType, capture(person1),
              *, *, *
            ).returning(
            Future.successful(
              Left(UpdateLeadStatusError.CommonCRMAPIError(err = CommonCRMAPIErrors.UnAuthorizedError(msg = "UnAuthorizedError")))
            ))

          leadStatusService.handleUpdateLeadStatusInCRM(
            event = emailOpenEvent,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = emailSchduledIdsForEmailOpen,
            statusValue = new_status,
            intg = crMIntegrationInDB.copy(status_column_in_crm = Some(new_status_with_options)),
            accountId = accountId,
          ).map(leadStatusRes => {
            assert(person1.value.person_id == person_id)
            assert(person1.value.statusColumn == new_status_with_options)
            assert(person1.value.statusValue == new_status)

            assert(leadStatusRes.isLeft)
          })

        }

        it("should 7: update activity to crm status: found tokens, gets Prospects, prospects fond in CRM and updateLeadStatus is Failure TooManyRequestsError") {

          (emailScheduledDAO.fetchProspectIdByEmailScheduledIdForUpdateLeadStatus)
            .expects(emailSchduledIdsForEmailOpen.get, getOnlyNewReplies)
            .returning(Seq(dummyEmailScheduledObjForUpdateLeadSts))


//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            )).repeat(2)

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))


          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name, intg_acc_tok, dummyProspectObj, moduleType, Some(status_col_name),
              *, *, *, *)
            .returning(Future.successful(Right(Seq(IntegrationContactResponse(
              id = person_id, email = Some(prospect_email), status = Some(status_col_value)
            )))))

          val person1 = CaptureOne[CrmLeadStatusUpdate]()

          (tIntegrationCRMService.updateLeadStatus
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType, _: CrmLeadStatusUpdate)
          (_: WSClient, _: ExecutionContext, _: SRLogger)
            )
            .expects(crmIntegrationService.name,
              intg_acc_tok, moduleType, capture(person1),
              *, *, *
            ).returning(
            Future.successful(
              Left(UpdateLeadStatusError.CommonCRMAPIError(err = CommonCRMAPIErrors.TooManyRequestsError(msg = "TooManyRequestsError")))
            ))

          leadStatusService.handleUpdateLeadStatusInCRM(
            event = emailOpenEvent,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = emailSchduledIdsForEmailOpen,
            statusValue = new_status,
            intg = crMIntegrationInDB.copy(status_column_in_crm = Some(new_status_with_options)),
            accountId = accountId,
          ).map(leadStatusRes => {
            assert(person1.value.person_id == person_id)
            assert(person1.value.statusColumn == new_status_with_options)
            assert(person1.value.statusValue == new_status)

            assert(leadStatusRes.isLeft)
          })

        }

        it("should 8: update activity to crm status: found tokens, gets Prospects, prospects fond in CRM and updateLeadStatus is Failure UnknownError") {

          (emailScheduledDAO.fetchProspectIdByEmailScheduledIdForUpdateLeadStatus)
            .expects(emailSchduledIdsForEmailOpen.get, getOnlyNewReplies)
            .returning(Seq(dummyEmailScheduledObjForUpdateLeadSts))

//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            )).repeat(2)

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))


          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name, intg_acc_tok, dummyProspectObj, moduleType, Some(status_col_name),
              *, *, *, *)
            .returning(Future.successful(Right(Seq(IntegrationContactResponse(
              id = person_id, email = Some(prospect_email), status = Some(status_col_value)
            )))))

          val person1 = CaptureOne[CrmLeadStatusUpdate]()

          (tIntegrationCRMService.updateLeadStatus
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType, _: CrmLeadStatusUpdate)
          (_: WSClient, _: ExecutionContext, _: SRLogger)
            )
            .expects(crmIntegrationService.name,
              intg_acc_tok, moduleType, capture(person1),
              *, *, *
            ).returning(
            Future.successful(
              Left(UpdateLeadStatusError.CommonCRMAPIError(err = CommonCRMAPIErrors.UnknownError(msg = "UnknownError")))
            ))

          leadStatusService.handleUpdateLeadStatusInCRM(
            event = emailOpenEvent,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = emailSchduledIdsForEmailOpen,
            statusValue = new_status,
            intg = crMIntegrationInDB.copy(status_column_in_crm = Some(new_status_with_options)),
            accountId = accountId,
          ).map(leadStatusRes => {
            assert(person1.value.person_id == person_id)
            assert(person1.value.statusColumn == new_status_with_options)
            assert(person1.value.statusValue == new_status)

            assert(leadStatusRes.isLeft)
          })

        }

      }

      describe("PATH 2.3 When prospect not found in CRM AND CREATE_IF_NOT_EXISTS IS TRUE") {

        it("should 1: update activity to crm status: found tokens, gets Prospects, prospects fond in CRM and create_record_if_not_exists true nd findFieldMapping exception throws") {

          (emailScheduledDAO.fetchProspectIdByEmailScheduledIdForUpdateLeadStatus)
            .expects(emailSchduledIdsForEmailOpen.get, getOnlyNewReplies)
            .returning(Seq(dummyEmailScheduledObjForUpdateLeadSts))

//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            ))


          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))

          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name, intg_acc_tok, dummyProspectObj, moduleType, Some(status_col_name),
              *, *, *, *)
            .returning(Future.successful(
              Right(Seq())
            ))
          ( createInCRMJedisDAO.getLock (_: ProspectId, _: IntegrationType, _: TeamId,_:IntegrationModuleType)(using _: SRLogger))
            .expects(*, *, *, *, *)
            .returning(false)

          (triggerDAO.findFieldMapping)
            .expects(team_id, integration_type, moduleType)
            .returning(Failure(new Exception("findFieldMapping error")))

          leadStatusService.handleUpdateLeadStatusInCRM(
            event = emailOpenEvent,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = emailSchduledIdsForEmailOpen,
            statusValue = status_col_value,
            intg = crMIntegrationInDB,
            accountId = accountId,
          ).map(leadStatusRes => {
            assert(leadStatusRes.isLeft)
          })

        }

        it("should 2: update activity to crm status: found tokens, gets Prospects, prospects fond in CRM and create_record_if_not_exists true and findFieldMapping returns NONE") {

          (emailScheduledDAO.fetchProspectIdByEmailScheduledIdForUpdateLeadStatus)
            .expects(emailSchduledIdsForEmailOpen.get, getOnlyNewReplies)
            .returning(Seq(dummyEmailScheduledObjForUpdateLeadSts))

//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            ))


          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))

          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name, intg_acc_tok, dummyProspectObj, moduleType, Some(status_col_name),
              *, *, *, *)
            .returning(Future.successful(
              Right(Seq())
            ))
          ( createInCRMJedisDAO.getLock (_: ProspectId, _: IntegrationType, _: TeamId,_:IntegrationModuleType)(using _: SRLogger))
            .expects(*, *, *, *, *)
            .returning(false)

          (triggerDAO.findFieldMapping)
            .expects(team_id, integration_type, moduleType)
            .returning(Success(None))

          leadStatusService.handleUpdateLeadStatusInCRM(
            event = emailOpenEvent,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = emailSchduledIdsForEmailOpen,
            statusValue = status_col_value,
            intg = crMIntegrationInDB,
            accountId = accountId,
          ).map(leadStatusRes => {
            assert(leadStatusRes.isLeft)
          })

        }

        it("should 3: update activity to crm status: found tokens, gets Prospects, prospects fond in CRM and create_record_if_not_exists true and findFieldMapping returns success and usermapping success and buildcontactonject success and createOrUpdateSingleContact return None") {

          (emailScheduledDAO.fetchProspectIdByEmailScheduledIdForUpdateLeadStatus)
            .expects(emailSchduledIdsForEmailOpen.get, getOnlyNewReplies)
            .returning(Seq(dummyEmailScheduledObjForUpdateLeadSts))

//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            ))

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))

          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name, intg_acc_tok, dummyProspectObj, moduleType, Some(status_col_name),
              *, *, *, *)
            .returning(Future.successful(
              Right(Seq())
            ))
          ( createInCRMJedisDAO.getLock (_: ProspectId, _: IntegrationType, _: TeamId,_:IntegrationModuleType)(using _: SRLogger))
            .expects(*, *, *, *, *)
            .returning(false)
          (triggerDAO.findFieldMapping)
            .expects(team_id, integration_type, moduleType)
            .returning(Success(Some(updateFieldsMappingForm)))


          (tIntegrationCRMService.findUpdatedUserMapping
          (_: IntegrationTPAccessTokenResponse.FullTokenData, _: Long, _: Long, _: IntegrationType)(_: WSClient, _: ExecutionContext, _: SRLogger))
            .expects(intg_acc_tok, team_id, accountId, integration_type, *, *, *)
            .returning(Future.successful(Right(updatedUserMappingMappingForm)))


          val person_email_capt = CaptureOne[String]()
          (tIntegrationCRMService.createOrUpdateSingleContact
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType, _: ProspectObject, _: UpdateFieldsMappingForm, _: UpdateUserMappingForm, _: Long, _: Long, _: String)
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name,
              intg_acc_tok, moduleType, dummyProspectObj, updateFieldsMappingForm, updatedUserMappingMappingForm, accountId, team_id, capture(person_email_capt),
              *, *, *, *
            ).returning(Future.successful(Left(
            CreateOrUpdateSingleContactsError.CommonCRMAPIError(err = CommonCRMAPIErrors.UnknownError(msg = "Unknown error"))
          )))


          leadStatusService.handleUpdateLeadStatusInCRM(
            event = emailOpenEvent,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = emailSchduledIdsForEmailOpen,
            statusValue = status_col_value,
            intg = crMIntegrationInDB,
            accountId = accountId,
          ).map(leadStatusRes => {
            assert(leadStatusRes.isLeft)
          })

        }

        it("should 4: update activity to crm status: found tokens, gets Prospects, prospects fond in CRM and create_record_if_not_exists true and findFieldMapping returns success and usermapping success and buildcontactonject success and createOrUpdateSingleContact success and then update lead status Failure InternalServerError") {

          (emailScheduledDAO.fetchProspectIdByEmailScheduledIdForUpdateLeadStatus)
            .expects(emailSchduledIdsForEmailOpen.get, getOnlyNewReplies)
            .returning(Seq(dummyEmailScheduledObjForUpdateLeadSts))

//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            )).repeat(2)

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))

          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name, intg_acc_tok, dummyProspectObj, moduleType, Some(status_col_name),
              *, *, *, *)
            .returning(Future.successful(
              Right(Seq())
            ))
          ( createInCRMJedisDAO.getLock (_: ProspectId, _: IntegrationType, _: TeamId,_:IntegrationModuleType)(using _: SRLogger))
            .expects(*, *, *, *, *)
            .returning(false)
          (triggerDAO.findFieldMapping)
            .expects(team_id, integration_type, moduleType)
            .returning(Success(Some(updateFieldsMappingForm)))


          (tIntegrationCRMService.findUpdatedUserMapping
          (_: IntegrationTPAccessTokenResponse.FullTokenData, _: Long, _: Long, _: IntegrationType)(_: WSClient, _: ExecutionContext, _: SRLogger))
            .expects(intg_acc_tok, team_id, accountId, integration_type, *, *, *)
            .returning(Future.successful(Right(updatedUserMappingMappingForm)))


          val person_email_capt = CaptureOne[String]()
          (tIntegrationCRMService.createOrUpdateSingleContact
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType, _: ProspectObject, _: UpdateFieldsMappingForm, _: UpdateUserMappingForm, _: Long, _: Long, _: String)
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name,
              intg_acc_tok, moduleType, dummyProspectObj, updateFieldsMappingForm, updatedUserMappingMappingForm, accountId, team_id, capture(person_email_capt),
              *, *, *, *
            ).returning(Future.successful(Right(person_id)))

          val person1 = CaptureOne[CrmLeadStatusUpdate]()

          (tIntegrationCRMService.updateLeadStatus
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType, _: CrmLeadStatusUpdate)
          (_: WSClient, _: ExecutionContext, _: SRLogger)
            )
            .expects(crmIntegrationService.name,
              intg_acc_tok, moduleType, capture(person1),
              *, *, *
            ).returning(
            Future.successful(
              Left(UpdateLeadStatusError.CommonCRMAPIError(err = CommonCRMAPIErrors.InternalServerError(msg = "Internal server error")))
            ))


          leadStatusService.handleUpdateLeadStatusInCRM(
            event = emailOpenEvent,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = emailSchduledIdsForEmailOpen,
            statusValue = new_status,
            intg = crMIntegrationInDB.copy(status_column_in_crm = Some(new_status_with_options)),
            accountId = accountId,
          ).map(leadStatusRes => {
            assert(person1.value.person_id == person_id)
            assert(person1.value.statusColumn == new_status_with_options)
            assert(person1.value.statusValue == new_status)

            assert(leadStatusRes.isLeft)
          })

        }

        it("should 5: update activity to crm status: found tokens, gets Prospects, prospects fond in CRM and create_record_if_not_exists true and findFieldMapping returns success and usermapping success and buildcontactonject success and createOrUpdateSingleContact success and then update lead status Failure NotFoundError") {

          (emailScheduledDAO.fetchProspectIdByEmailScheduledIdForUpdateLeadStatus)
            .expects(emailSchduledIdsForEmailOpen.get, getOnlyNewReplies)
            .returning(Seq(dummyEmailScheduledObjForUpdateLeadSts))

//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            )).repeat(2)

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))

          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name,intg_acc_tok, dummyProspectObj, moduleType, Some(status_col_name),
              *, *, *, *)
            .returning(Future.successful(
              Right(Seq())
            ))
          ( createInCRMJedisDAO.getLock (_: ProspectId, _: IntegrationType, _: TeamId,_:IntegrationModuleType)(using _: SRLogger))
            .expects(*, *, *, *, *)
            .returning(false)
          (triggerDAO.findFieldMapping)
            .expects(team_id, integration_type, moduleType)
            .returning(Success(Some(updateFieldsMappingForm)))


          (tIntegrationCRMService.findUpdatedUserMapping
          (_: IntegrationTPAccessTokenResponse.FullTokenData, _: Long, _: Long, _: IntegrationType)(_: WSClient, _: ExecutionContext, _: SRLogger))
            .expects(intg_acc_tok, team_id, accountId, integration_type, *, *, *)
            .returning(Future.successful(Right(updatedUserMappingMappingForm)))


          val person_email_capt = CaptureOne[String]()
          (tIntegrationCRMService.createOrUpdateSingleContact
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType, _: ProspectObject, _: UpdateFieldsMappingForm, _: UpdateUserMappingForm, _: Long, _: Long, _: String)
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name,
              intg_acc_tok, moduleType, dummyProspectObj, updateFieldsMappingForm, updatedUserMappingMappingForm, accountId, team_id, capture(person_email_capt),
              *, *, *, *
            ).returning(Future.successful(Right(person_id)))

          val person1 = CaptureOne[CrmLeadStatusUpdate]()

          (tIntegrationCRMService.updateLeadStatus
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType, _: CrmLeadStatusUpdate)
          (_: WSClient, _: ExecutionContext, _: SRLogger)
            )
            .expects(crmIntegrationService.name,
              intg_acc_tok, moduleType, capture(person1),
              *, *, *
            ).returning(
            Future.successful(
              Left(UpdateLeadStatusError.CommonCRMAPIError(err = CommonCRMAPIErrors.NotFoundError(msg = "Resource not found error")))
            ))


          leadStatusService.handleUpdateLeadStatusInCRM(
            event = emailOpenEvent,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = emailSchduledIdsForEmailOpen,
            statusValue = new_status,
            intg = crMIntegrationInDB.copy(status_column_in_crm = Some(new_status_with_options)),
            accountId = accountId,
          ).map(leadStatusRes => {
            assert(person1.value.person_id == person_id)
            assert(person1.value.statusColumn == new_status_with_options)
            assert(person1.value.statusValue == new_status)

            assert(leadStatusRes.isLeft)
          })

        }

        it("should 5: update activity to crm status: found tokens, gets Prospects, prospects fond in CRM and create_record_if_not_exists true and findFieldMapping returns success and usermapping success and buildcontactonject success and createOrUpdateSingleContact success and then update lead status Failure UnAuthorizedError") {

          (emailScheduledDAO.fetchProspectIdByEmailScheduledIdForUpdateLeadStatus)
            .expects(emailSchduledIdsForEmailOpen.get, getOnlyNewReplies)
            .returning(Seq(dummyEmailScheduledObjForUpdateLeadSts))


//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            )).repeat(2)

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))

          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name, intg_acc_tok, dummyProspectObj, moduleType, Some(status_col_name),
              *, *, *, *)
            .returning(Future.successful(
              Right(Seq())
            ))
          ( createInCRMJedisDAO.getLock (_: ProspectId, _: IntegrationType, _: TeamId,_:IntegrationModuleType)(using _: SRLogger))
            .expects(*, *, *, *, *)
            .returning(false)
          (triggerDAO.findFieldMapping)
            .expects(team_id, integration_type, moduleType)
            .returning(Success(Some(updateFieldsMappingForm)))


          (tIntegrationCRMService.findUpdatedUserMapping
          (_: IntegrationTPAccessTokenResponse.FullTokenData, _: Long, _: Long, _: IntegrationType)(_: WSClient, _: ExecutionContext, _: SRLogger))
            .expects(intg_acc_tok, team_id, accountId, integration_type, *, *, *)
            .returning(Future.successful(Right(updatedUserMappingMappingForm)))


          val person_email_capt = CaptureOne[String]()
          (tIntegrationCRMService.createOrUpdateSingleContact
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType, _: ProspectObject, _: UpdateFieldsMappingForm, _: UpdateUserMappingForm, _: Long, _: Long, _: String)
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name,
              intg_acc_tok, moduleType, dummyProspectObj, updateFieldsMappingForm, updatedUserMappingMappingForm, accountId, team_id, capture(person_email_capt),
              *, *, *, *
            ).returning(Future.successful(Right(person_id)))

          val person1 = CaptureOne[CrmLeadStatusUpdate]()

          (tIntegrationCRMService.updateLeadStatus
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType, _: CrmLeadStatusUpdate)
          (_: WSClient, _: ExecutionContext, _: SRLogger)
            )
            .expects(crmIntegrationService.name,
              intg_acc_tok, moduleType, capture(person1),
              *, *, *
            ).returning(
            Future.successful(
              Left(UpdateLeadStatusError.CommonCRMAPIError(err = CommonCRMAPIErrors.UnAuthorizedError(msg = "UnAuthorizedError")))
            ))


          leadStatusService.handleUpdateLeadStatusInCRM(
            event = emailOpenEvent,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = emailSchduledIdsForEmailOpen,
            statusValue = new_status,
            intg = crMIntegrationInDB.copy(status_column_in_crm = Some(new_status_with_options)),
            accountId = accountId,
          ).map(leadStatusRes => {
            assert(person1.value.person_id == person_id)
            assert(person1.value.statusColumn == new_status_with_options)
            assert(person1.value.statusValue == new_status)

            assert(leadStatusRes.isLeft)
          })

        }

        it("should 6: update activity to crm status: found tokens, gets Prospects, prospects fond in CRM and create_record_if_not_exists true and findFieldMapping returns success and usermapping success and buildcontactonject success and createOrUpdateSingleContact success and then update lead status Failure TooManyRequestsError") {

          (emailScheduledDAO.fetchProspectIdByEmailScheduledIdForUpdateLeadStatus)
            .expects(emailSchduledIdsForEmailOpen.get, getOnlyNewReplies)
            .returning(Seq(dummyEmailScheduledObjForUpdateLeadSts))


//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            )).repeat(2)

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))

          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name, intg_acc_tok, dummyProspectObj, moduleType, Some(status_col_name),
              *, *, *, *)
            .returning(Future.successful(
              Right(Seq())
            ))
          ( createInCRMJedisDAO.getLock (_: ProspectId, _: IntegrationType, _: TeamId,_:IntegrationModuleType)(using _: SRLogger))
            .expects(*, *, *, *, *)
            .returning(false)
          (triggerDAO.findFieldMapping)
            .expects(team_id, integration_type, moduleType)
            .returning(Success(Some(updateFieldsMappingForm)))


          (tIntegrationCRMService.findUpdatedUserMapping
          (_: IntegrationTPAccessTokenResponse.FullTokenData, _: Long, _: Long, _: IntegrationType)(_: WSClient, _: ExecutionContext, _: SRLogger))
            .expects(intg_acc_tok, team_id, accountId, integration_type, *, *, *)
            .returning(Future.successful(Right(updatedUserMappingMappingForm)))


          val person_email_capt = CaptureOne[String]()
          (tIntegrationCRMService.createOrUpdateSingleContact
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType, _: ProspectObject, _: UpdateFieldsMappingForm, _: UpdateUserMappingForm, _: Long, _: Long, _: String)
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name,
              intg_acc_tok, moduleType, dummyProspectObj, updateFieldsMappingForm, updatedUserMappingMappingForm, accountId, team_id, capture(person_email_capt),
              *, *, *, *
            ).returning(Future.successful(Right(person_id)))

          val person1 = CaptureOne[CrmLeadStatusUpdate]()

          (tIntegrationCRMService.updateLeadStatus
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType, _: CrmLeadStatusUpdate)
          (_: WSClient, _: ExecutionContext, _: SRLogger)
            )
            .expects(crmIntegrationService.name,
              intg_acc_tok, moduleType, capture(person1),
              *, *, *
            ).returning(
            Future.successful(
              Left(UpdateLeadStatusError.CommonCRMAPIError(err = CommonCRMAPIErrors.TooManyRequestsError(msg = "TooManyRequestsError")))
            ))


          leadStatusService.handleUpdateLeadStatusInCRM(
            event = emailOpenEvent,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = emailSchduledIdsForEmailOpen,
            statusValue = new_status,
            intg = crMIntegrationInDB.copy(status_column_in_crm = Some(new_status_with_options)),
            accountId = accountId,
          ).map(leadStatusRes => {
            assert(person1.value.person_id == person_id)
            assert(person1.value.statusColumn == new_status_with_options)
            assert(person1.value.statusValue == new_status)

            assert(leadStatusRes.isLeft)
          })

        }

        it("should 7: update activity to crm status: found tokens, gets Prospects, prospects fond in CRM and create_record_if_not_exists true and findFieldMapping returns success and usermapping success and buildcontactonject success and createOrUpdateSingleContact success and then update lead status Failure UnknownError") {

          (emailScheduledDAO.fetchProspectIdByEmailScheduledIdForUpdateLeadStatus)
            .expects(emailSchduledIdsForEmailOpen.get, getOnlyNewReplies)
            .returning(Seq(dummyEmailScheduledObjForUpdateLeadSts))

//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            )).repeat(2)

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))

          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name, intg_acc_tok, dummyProspectObj, moduleType, Some(status_col_name),
              *, *, *, *)
            .returning(Future.successful(
              Right(Seq())
            ))
          ( createInCRMJedisDAO.getLock (_: ProspectId, _: IntegrationType, _: TeamId,_:IntegrationModuleType)(using _: SRLogger))
            .expects(*, *, *, *, *)
            .returning(false)
          (triggerDAO.findFieldMapping)
            .expects(team_id, integration_type, moduleType)
            .returning(Success(Some(updateFieldsMappingForm)))


          (tIntegrationCRMService.findUpdatedUserMapping
          (_: IntegrationTPAccessTokenResponse.FullTokenData, _: Long, _: Long, _: IntegrationType)(_: WSClient, _: ExecutionContext, _: SRLogger))
            .expects(intg_acc_tok, team_id, accountId, integration_type, *, *, *)
            .returning(Future.successful(Right(updatedUserMappingMappingForm)))


          val person_email_capt = CaptureOne[String]()
          (tIntegrationCRMService.createOrUpdateSingleContact
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType, _: ProspectObject, _: UpdateFieldsMappingForm, _: UpdateUserMappingForm, _: Long, _: Long, _: String)
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name,
              intg_acc_tok, moduleType, dummyProspectObj, updateFieldsMappingForm, updatedUserMappingMappingForm, accountId, team_id, capture(person_email_capt),
              *, *, *, *
            ).returning(Future.successful(Right(person_id)))

          val person1 = CaptureOne[CrmLeadStatusUpdate]()

          (tIntegrationCRMService.updateLeadStatus
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType, _: CrmLeadStatusUpdate)
          (_: WSClient, _: ExecutionContext, _: SRLogger)
            )
            .expects(crmIntegrationService.name,
              intg_acc_tok, moduleType, capture(person1),
              *, *, *
            ).returning(
            Future.successful(
              Left(UpdateLeadStatusError.CommonCRMAPIError(err = CommonCRMAPIErrors.UnknownError(msg = "UnknownError")))
            ))


          leadStatusService.handleUpdateLeadStatusInCRM(
            event = emailOpenEvent,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = emailSchduledIdsForEmailOpen,
            statusValue = new_status,
            intg = crMIntegrationInDB.copy(status_column_in_crm = Some(new_status_with_options)),
            accountId = accountId,
          ).map(leadStatusRes => {
            assert(person1.value.person_id == person_id)
            assert(person1.value.statusColumn == new_status_with_options)
            assert(person1.value.statusValue == new_status)

            assert(leadStatusRes.isLeft)
          })

        }

        it("should 8: update activity to crm status: found tokens, gets Prospects, prospects fond in CRM and create_record_if_not_exists true and findFieldMapping returns success and usermapping success and buildcontactonject success and createOrUpdateSingleContact success and then update lead status success") {

          (emailScheduledDAO.fetchProspectIdByEmailScheduledIdForUpdateLeadStatus)
            .expects(emailSchduledIdsForEmailOpen.get, getOnlyNewReplies)
            .returning(Seq(dummyEmailScheduledObjForUpdateLeadSts))


//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            )).repeat(2)

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))

          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name, intg_acc_tok, dummyProspectObj, moduleType, Some(status_col_name),
              *, *, *, *)
            .returning(Future.successful(
              Right(Seq())
            ))
          ( createInCRMJedisDAO.getLock (_: ProspectId, _: IntegrationType, _: TeamId, _:IntegrationModuleType)(using _: SRLogger))
            .expects(*, *, *, *, *)
            .returning(false)
          (triggerDAO.findFieldMapping)
            .expects(team_id, integration_type, moduleType)
            .returning(Success(Some(updateFieldsMappingForm)))


          (tIntegrationCRMService.findUpdatedUserMapping
          (_: IntegrationTPAccessTokenResponse.FullTokenData, _: Long, _: Long, _: IntegrationType)(_: WSClient, _: ExecutionContext, _: SRLogger))
            .expects(intg_acc_tok, team_id, accountId, integration_type, *, *, *)
            .returning(Future.successful(Right(updatedUserMappingMappingForm)))


          val person_email_capt = CaptureOne[String]()
          (tIntegrationCRMService.createOrUpdateSingleContact
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType, _: ProspectObject, _: UpdateFieldsMappingForm, _: UpdateUserMappingForm, _: Long, _: Long, _: String)
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name,
              intg_acc_tok, moduleType, dummyProspectObj, updateFieldsMappingForm, updatedUserMappingMappingForm, accountId, team_id, capture(person_email_capt),
              *, *, *, *
            ).returning(Future.successful(Right(person_id)))

          val person1 = CaptureOne[CrmLeadStatusUpdate]()
          (tIntegrationCRMService.updateLeadStatus
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType, _: CrmLeadStatusUpdate)
          (_: WSClient, _: ExecutionContext, _: SRLogger)
            )
            .expects(crmIntegrationService.name,
              intg_acc_tok, moduleType, capture(person1),
              *, *, *
            ).returning(
            Future.successful(
              Right(person_id)
            )
          )

          leadStatusService.handleUpdateLeadStatusInCRM(
            event = emailOpenEvent,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = emailSchduledIdsForEmailOpen,
            statusValue = new_status,
            intg = crMIntegrationInDB.copy(status_column_in_crm = Some(new_status_with_options)),
            accountId = accountId,
          ).map(leadStatusRes => {

            assert(person1.value.person_id == person_id)
            assert(person1.value.statusColumn == new_status_with_options)
            assert(person1.value.statusValue == new_status)

            assert(leadStatusRes === Right(Seq(1)))
          })

        }

      }

      describe("PATH 3.3 When prospect not found in CRM AND CREATE_IF_NOT_EXISTS IS FALSE") {

        it("should 10: update activity to crm status: found tokens, gets Prospects, prospects fond in CRM and create_record_if_not_exists false") {

          (emailScheduledDAO.fetchProspectIdByEmailScheduledIdForUpdateLeadStatus)
            .expects(emailSchduledIdsForEmailOpen.get, getOnlyNewReplies)
            .returning(Seq(dummyEmailScheduledObjForUpdateLeadSts))


//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            ))


          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))

          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name, intg_acc_tok, dummyProspectObj, moduleType, Some(status_col_name),
              *, *, *, *)
            .returning(Future.successful(
              Right(Seq())
            ))

          leadStatusService.handleUpdateLeadStatusInCRM(
            event = emailOpenEvent,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = emailSchduledIdsForEmailOpen,
            statusValue = status_col_value,
            intg = crMIntegrationInDB.copy(create_record_if_not_exists = false),
            accountId = accountId,
          )
            .map(leadStatusRes => {
              assert(leadStatusRes.isLeft)
            })

        }

      }

    }
  }

  //=======================================================================================================================================
  //=======================================================================================================================================


  describe("UPDATE ACTIVITY handleUpdateActivityInCRM") {



    trait IntegrationsCRMTestTrait extends TIntegrationCRMTrait {
      val name = IntegrationType.SALESFORCE
    }
    val crmIntegrationService = mock[IntegrationsCRMTestTrait]

    val campaignName:Option[String] = Some("Demo campaign")

    describe("handleUpdateActivityInCRM with INVALID_EMAIL") {

      val activitySubject = "Email is invalid"

      val detailedActivitySubject = s"""Invalid email on email from Demo campaign campaign"""

      val activityType = TPActivityType.NOTE

      val activityAt = DateTime.now()

      val emailSubject:Option[String] = None
      val emailSenderAddress:Option[String] = None
      val emailSenderFirstName:Option[String] = None
      val emailSenderLastName:Option[String] = None
      val emailBody:Option[String] = None
      val emailTextBody:Option[String] = None

      val event = EventType.EMAIL_INVALID
      val emailScheduledIds = None

      describe("PATH 1.1 When prospect found in CRM") {

        it("should 1: update activity in crm tokens not found") {

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))

//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.failed(new Exception("Error while fetching tokens. Please try again, or contact support.")))

          /*(() => repTrackingHostService.getRepTrackingHosts())
            .expects()
            .returning(Success(Seq(repTrackingHosts)))*/

          handleActivityTriggerEventServiceSpec.handleUpdateActivityInCRM(
            crmType = integration_type,
            moduleType = moduleType,
            event = event,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = emailScheduledIds,
            create_record_if_not_exists = true,
            teamId = team_id,
            accountId = accountId,
            campaignName = campaignName,

            workflow_crm_setting_id = Some(workflow_crm_setting_id),
          ).map(_ => {
            assert(false)
          })
            .recover { case e =>

              val exeMatch = e.getMessage.contains("Error while fetching tokens")

              Logger.info(s"\n\n\n\n in recover $exeMatch")

              exeMatch shouldBe true
            }

        }

        it("should 2: update activity in crm tokens found but prospect did't found with given id") {

          /*(tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            ))*/

//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          /*(() => repTrackingHostService.getRepTrackingHosts())
            .expects()
            .returning(Success(Seq(repTrackingHosts)))*/

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(
              Failure(new Exception("Error while fetching prospect. Please try again, or contact support."))
            )

          handleActivityTriggerEventServiceSpec.handleUpdateActivityInCRM(
            crmType = integration_type,
            moduleType = moduleType,
            event = event,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = emailScheduledIds,
            create_record_if_not_exists = true,
            teamId = team_id,
            accountId = accountId,
            campaignName = campaignName,

            workflow_crm_setting_id = Some(workflow_crm_setting_id),
          ).map(ActivityRes => {
            assert(ActivityRes.isLeft)
          })

        }

        it("should 3 update activity in crm: found tokens, gets Prospects, prospects fond in CRM and updateActivity is Success") {

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            ))

//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))

          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name, intg_acc_tok, dummyProspectObj, moduleType, None,
              *, *, *, *)
            .returning(Future.successful(Right(Seq(IntegrationContactResponse(
              id = person_id, email = Some(prospect_email), status = Some(status_col_value)
            )))))

          (() => repTrackingHostService.getRepTrackingHosts())
            .expects()
            .returning(Success(Seq(repTrackingHosts)))

          val person_id_capt = CaptureOne[String]()
          val activity_at_capt = CaptureOne[DateTime]()
          (tIntegrationCRMService.updateActivity
          (
            _: IntegrationType,
            _: String,
            _: String,
            _: TPActivityType.Value,
            _: String,
            _: DateTime,
            _: Option[String],
            _: Option[String],
            _: Option[String],
            _: Option[String],
            _: Option[String],
            _: Option[String],
            _: IntegrationModuleType,
            _: Long,
            _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(
              crmIntegrationService.name,
              capture(person_id_capt),
              activitySubject,
              activityType,
              detailedActivitySubject,
              capture(activity_at_capt),
              emailSubject,
              emailSenderAddress,
              emailSenderFirstName,
              emailSenderLastName,
              emailBody,
              emailTextBody,
              moduleType,
              team_id,
              *, *, *, *, *
            ).returning(Future.successful(Right(person_id)))

          handleActivityTriggerEventServiceSpec.handleUpdateActivityInCRM(
            crmType = integration_type,
            moduleType = moduleType,
            event = event,
            create_record_if_not_exists = true,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = None,
            teamId = team_id,
            accountId = accountId,
            campaignName = campaignName,

            workflow_crm_setting_id = Some(workflow_crm_setting_id),
          ).map(ActivityRes => {
            assert(ActivityRes === Right(Seq(1)))
            assert(person_id_capt.value === person_id)
            assert(Minutes.minutesBetween(activity_at_capt.value, activityAt).getMinutes < 1)
          })

        }

        it("should 4: update activity to crm status: found tokens, gets Prospects, prospects fond in CRM and updateLeadStatus is Failure") {

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            ))

//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))

          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name, intg_acc_tok, dummyProspectObj, moduleType, None,
              *, *, *, *)
            .returning(Future.successful(Right(Seq(IntegrationContactResponse(
              id = person_id, email = Some(prospect_email), status = Some(status_col_value)
            )))))

          (() => repTrackingHostService.getRepTrackingHosts())
            .expects()
            .returning(Success(Seq(repTrackingHosts)))

          val person_id_capt = CaptureOne[String]()
          val activity_at_capt = CaptureOne[DateTime]()
          (tIntegrationCRMService.updateActivity
          (
            _: IntegrationType,
            _: String,
            _: String,
            _: TPActivityType.Value,
            _: String,
            _: DateTime,
            _: Option[String],
            _: Option[String],
            _: Option[String],
            _: Option[String],
            _: Option[String],
            _: Option[String],
            _: IntegrationModuleType,
            _: Long,
            _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(
              crmIntegrationService.name,
              capture(person_id_capt),
              activitySubject,
              activityType,
              detailedActivitySubject,
              capture(activity_at_capt),
              emailSubject,
              emailSenderAddress,
              emailSenderFirstName,
              emailSenderLastName,
              emailBody,
              emailTextBody,
              moduleType,
              team_id,
              *, *, *, *, *
            ).returning(Future.successful(
            Left(UpdateActivityError.CommonCRMAPIError(err = CommonCRMAPIErrors.InternalServerError(msg = "Internal Server error")))
          ))

          handleActivityTriggerEventServiceSpec.handleUpdateActivityInCRM(
            crmType = integration_type,
            moduleType = moduleType,
            event = event,
            create_record_if_not_exists = true,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = None,
            teamId = team_id,
            accountId = accountId,
            campaignName = campaignName,

            workflow_crm_setting_id = Some(workflow_crm_setting_id),
          ).map(ActivityRes => {
            assert(ActivityRes.isLeft)
            assert(person_id_capt.value === person_id)
            assert(Minutes.minutesBetween(activity_at_capt.value, activityAt).getMinutes < 1)
          })


        }

      }


      describe("PATH 2.4 When prospect not found in CRM AND CREATE_IF_NOT_EXISTS IS TRUE") {

        it("should 5: update activity to crm status: found tokens, gets Prospects, prospects fond in CRM and create_record_if_not_exists true nd findFieldMapping exception throws") {

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            ))


//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)


          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))

          val person_email_capt = CaptureOne[String]()
          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name, intg_acc_tok, capture(person_email_capt), moduleType, None,
              *, *, *, *)
            .returning(Future.successful(
              Right(Seq())
            ))
          ( createInCRMJedisDAO.getLock (_: ProspectId, _: IntegrationType, _: TeamId, _:IntegrationModuleType)(using _: SRLogger))
            .expects(*, *, *, *, *)
            .returning(false)
          /*(() => repTrackingHostService.getRepTrackingHosts())
            .expects()
            .returning(Success(Seq(repTrackingHosts)))*/

          (triggerDAO.findFieldMapping)
            .expects(team_id, integration_type, moduleType)
            .returning(Failure(new Exception("findFieldMapping error")))

          handleActivityTriggerEventServiceSpec.handleUpdateActivityInCRM(
            crmType = integration_type,
            moduleType = moduleType,
            event = event,
            create_record_if_not_exists = true,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = None,
            teamId = team_id,
            accountId = accountId,
            campaignName = campaignName,

            workflow_crm_setting_id = Some(workflow_crm_setting_id),
          )
            .map(ActivityRes => {
              assert(ActivityRes.isLeft)
            })

        }

        it("should 6: update activity to crm status: found tokens, gets Prospects, prospects fond in CRM and create_record_if_not_exists true and findFieldMapping returns NONE") {

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            ))

//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))

          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name, intg_acc_tok, dummyProspectObj, moduleType, None,
              *, *, *, *)
            .returning(Future.successful(
              Right(Seq())
            ))
          ( createInCRMJedisDAO.getLock (_: ProspectId, _: IntegrationType, _: TeamId,_:IntegrationModuleType)(using _: SRLogger))
            .expects(*, *, *, *, *)
            .returning(false)
          /*(() => repTrackingHostService.getRepTrackingHosts())
            .expects()
            .returning(Success(Seq(repTrackingHosts)))*/

          (triggerDAO.findFieldMapping)
            .expects(team_id, integration_type, moduleType)
            .returning(Success(None))

          handleActivityTriggerEventServiceSpec.handleUpdateActivityInCRM(
            crmType = integration_type,
            moduleType = moduleType,
            event = event,
            create_record_if_not_exists = true,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = None,
            teamId = team_id,
            accountId = accountId,
            campaignName = campaignName,

            workflow_crm_setting_id = Some(workflow_crm_setting_id),
          )
            .map(ActivityRes => {
              assert(ActivityRes.isLeft)
            })

        }

        it("should 7: update activity to crm status: found tokens, gets Prospects, prospects fond in CRM and create_record_if_not_exists true and findFieldMapping returns success and usermapping success and buildcontactonject success and createOrUpdateSingleContact return None") {

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            ))

//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))

          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name, intg_acc_tok, dummyProspectObj, moduleType, None,
              *, *, *, *)
            .returning(Future.successful(
              Right(Seq())
            ))
          ( createInCRMJedisDAO.getLock (_: ProspectId, _: IntegrationType, _: TeamId,_:IntegrationModuleType)(using _: SRLogger))
            .expects(*, *, *, *, *)
            .returning(false)
          /*(() => repTrackingHostService.getRepTrackingHosts())
            .expects()
            .returning(Success(Seq(repTrackingHosts)))*/

          (triggerDAO.findFieldMapping)
            .expects(team_id, integration_type, moduleType)
            .returning(Success(Some(updateFieldsMappingForm)))

          (tIntegrationCRMService.findUpdatedUserMapping
          (_: IntegrationTPAccessTokenResponse.FullTokenData, _: Long, _: Long, _: IntegrationType)(_: WSClient, _: ExecutionContext, _: SRLogger))
            .expects(intg_acc_tok, team_id, accountId, integration_type, *, *, *)
            .returning(Future.successful(Right(updatedUserMappingMappingForm)))

          val person_email_capt = CaptureOne[String]()
          (tIntegrationCRMService.createOrUpdateSingleContact
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType, _: ProspectObject, _: UpdateFieldsMappingForm, _: UpdateUserMappingForm, _: Long, _: Long, _: String)
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name,
              intg_acc_tok, moduleType, dummyProspectObj, updateFieldsMappingForm, updatedUserMappingMappingForm, accountId, team_id, capture(person_email_capt),
              *, *, *, *
            ).returning(Future.successful(Left(
            CreateOrUpdateSingleContactsError.CommonCRMAPIError(err = CommonCRMAPIErrors.UnknownError(msg = "Unknown error"))
          )))

          handleActivityTriggerEventServiceSpec.handleUpdateActivityInCRM(
            crmType = integration_type,
            moduleType = moduleType,
            event = event,
            create_record_if_not_exists = true,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = None,
            teamId = team_id,
            accountId = accountId,
            campaignName = campaignName,

            workflow_crm_setting_id = Some(workflow_crm_setting_id),
          )
            .map(ActivityRes => {
              assert(ActivityRes.isLeft)
            })

        }

        it("should 8: update activity to crm status: found tokens, gets Prospects, prospects fond in CRM and create_record_if_not_exists true and findFieldMapping returns success and usermapping success and buildcontactonject success and createOrUpdateSingleContact success and then update lead status Failure") {

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            ))

//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))

          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name, intg_acc_tok, dummyProspectObj, moduleType, None,
              *, *, *, *)
            .returning(Future.successful(
              Right(Seq())
            ))
          ( createInCRMJedisDAO.getLock (_: ProspectId, _: IntegrationType, _: TeamId,_:IntegrationModuleType)(using _: SRLogger))
            .expects(*, *, *, *, *)
            .returning(false)
          (() => repTrackingHostService.getRepTrackingHosts())
            .expects()
            .returning(Success(Seq(repTrackingHosts)))

          (triggerDAO.findFieldMapping)
            .expects(team_id, integration_type, moduleType)
            .returning(Success(Some(updateFieldsMappingForm)))

          (tIntegrationCRMService.findUpdatedUserMapping
          (_: IntegrationTPAccessTokenResponse.FullTokenData, _: Long, _: Long, _: IntegrationType)(_: WSClient, _: ExecutionContext, _: SRLogger))
            .expects(intg_acc_tok, team_id, accountId, integration_type, *, *, *)
            .returning(Future.successful(Right(updatedUserMappingMappingForm)))

          val person_email_capt = CaptureOne[String]()
          (tIntegrationCRMService.createOrUpdateSingleContact
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType, _: ProspectObject, _: UpdateFieldsMappingForm, _: UpdateUserMappingForm, _: Long, _: Long, _: String)
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name,
              intg_acc_tok, moduleType, dummyProspectObj, updateFieldsMappingForm, updatedUserMappingMappingForm, accountId, team_id, capture(person_email_capt),
              *, *, *, *
            ).returning(Future.successful(Right(person_id)))

          val person_id_capt = CaptureOne[String]()
          val activity_at_capt = CaptureOne[DateTime]()
          (tIntegrationCRMService.updateActivity
          (
            _: IntegrationType,
            _: String,
            _: String,
            _: TPActivityType.Value,
            _: String,
            _: DateTime,
            _: Option[String],
            _: Option[String],
            _: Option[String],
            _: Option[String],
            _: Option[String],
            _: Option[String],
            _: IntegrationModuleType,
            _: Long,
            _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(
              crmIntegrationService.name,
              capture(person_id_capt),
              activitySubject,
              activityType,
              detailedActivitySubject,
              capture(activity_at_capt),
              emailSubject,
              emailSenderAddress,
              emailSenderFirstName,
              emailSenderLastName,
              emailBody,
              emailTextBody,
              moduleType,
              team_id,
              *, *, *, *, *
            ).returning(Future.successful(
            Left(UpdateActivityError.CommonCRMAPIError(err = CommonCRMAPIErrors.InternalServerError(msg = "Internal Server error")))
          ))

          handleActivityTriggerEventServiceSpec.handleUpdateActivityInCRM(
            crmType = integration_type,
            moduleType = moduleType,
            event = event,
            create_record_if_not_exists = true,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = None,
            teamId = team_id,
            accountId = accountId,
            campaignName = campaignName,

            workflow_crm_setting_id = Some(workflow_crm_setting_id),
          )
            .map(ActivityRes => {
              assert(ActivityRes.isLeft)
              assert(person_id_capt.value === person_id)
              assert(Minutes.minutesBetween(activity_at_capt.value, activityAt).getMinutes < 1)
            })

        }

        it("should 9: update activity to crm status: found tokens, gets Prospects, prospects fond in CRM and create_record_if_not_exists true and findFieldMapping returns success and usermapping success and buildcontactonject success and createOrUpdateSingleContact success and then update lead status success") {

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            ))


//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))

          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name, intg_acc_tok, dummyProspectObj, moduleType, None,
              *, *, *, *)
            .returning(Future.successful(
              Right(Seq())
            ))
          ( createInCRMJedisDAO.getLock (_: ProspectId, _: IntegrationType, _: TeamId,_:IntegrationModuleType)(using _: SRLogger))
            .expects(*, *, *, *, *)
            .returning(false)
          (() => repTrackingHostService.getRepTrackingHosts())
            .expects()
            .returning(Success(Seq(repTrackingHosts)))

          (triggerDAO.findFieldMapping)
            .expects(team_id, integration_type, moduleType)
            .returning(Success(Some(updateFieldsMappingForm)))

          (tIntegrationCRMService.findUpdatedUserMapping
          (_: IntegrationTPAccessTokenResponse.FullTokenData, _: Long, _: Long, _: IntegrationType)(_: WSClient, _: ExecutionContext, _: SRLogger))
            .expects(intg_acc_tok, team_id, accountId, integration_type, *, *, *)
            .returning(Future.successful(Right(updatedUserMappingMappingForm)))

          val person_email_capt = CaptureOne[String]()
          (tIntegrationCRMService.createOrUpdateSingleContact
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType, _: ProspectObject, _: UpdateFieldsMappingForm, _: UpdateUserMappingForm, _: Long, _: Long, _: String)
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name,
              intg_acc_tok, moduleType, dummyProspectObj, updateFieldsMappingForm, updatedUserMappingMappingForm, accountId, team_id, capture(person_email_capt),
              *, *, *, *
            ).returning(Future.successful(Right(person_id)))

          val person_id_capt = CaptureOne[String]()
          val activity_at_capt = CaptureOne[DateTime]()
          (tIntegrationCRMService.updateActivity
          (
            _: IntegrationType,
            _: String,
            _: String,
            _: TPActivityType.Value,
            _: String,
            _: DateTime,
            _: Option[String],
            _: Option[String],
            _: Option[String],
            _: Option[String],
            _: Option[String],
            _: Option[String],
            _: IntegrationModuleType,
            _: Long,
            _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(
              crmIntegrationService.name,
              capture(person_id_capt),
              activitySubject,
              activityType,
              detailedActivitySubject,
              capture(activity_at_capt),
              emailSubject,
              emailSenderAddress,
              emailSenderFirstName,
              emailSenderLastName,
              emailBody,
              emailTextBody,
              moduleType,
              team_id,
              *, *, *, *, *
            ).returning(Future.successful(Right(person_id)))

          handleActivityTriggerEventServiceSpec.handleUpdateActivityInCRM(
            crmType = integration_type,
            moduleType = moduleType,
            event = event,
            create_record_if_not_exists = true,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = None,
            teamId = team_id,
            accountId = accountId,
            campaignName = campaignName,

            workflow_crm_setting_id = Some(workflow_crm_setting_id),
          )
            .map(ActivityRes => {
              assert(ActivityRes === Right(Seq(1)))
              assert(person_id_capt.value === person_id)
              assert(Minutes.minutesBetween(activity_at_capt.value, activityAt).getMinutes < 1)
            })

        }

      }

      describe(" PATH 3.4 When prospect not found in CRM AND CREATE_IF_NOT_EXISTS IS FALSE") {

        it("should 10: update activity to crm status: found tokens, gets Prospects, prospects fond in CRM and create_record_if_not_exists false") {
          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            ))

//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))

          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name, intg_acc_tok, dummyProspectObj, moduleType, None,
              *, *, *, *)
            .returning(Future.successful(
              Right(Seq())
            ))

          /*(() => repTrackingHostService.getRepTrackingHosts())
            .expects()
            .returning(Success(Seq(repTrackingHosts)))*/

          handleActivityTriggerEventServiceSpec.handleUpdateActivityInCRM(
            crmType = integration_type,
            moduleType = moduleType,
            event = event,
            create_record_if_not_exists = false,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = None,
            teamId = team_id,
            accountId = accountId,
            campaignName = campaignName,

            workflow_crm_setting_id = Some(workflow_crm_setting_id),
          )
            .map(ActivityRes => {
              assert(ActivityRes.isLeft)
            })

        }

      }
    }

    describe("handleUpdateActivityInCRM with NEW_REPLY and emailScheduledids empty") {

      val activitySubject = "New reply"

      val detailedActivitySubject = s"""Replied to email from Demo campaign campaign"""

      val activityType = TPActivityType.EMAIL

      val activityAt = DateTime.now()

      val emailSubject:Option[String] = Some("Manually marked as replied by admin")
      val emailSenderAddress:Option[String] = Some(prospect_email)
      val emailSenderFirstName:Option[String] = Some("")
      val emailSenderLastName:Option[String] = None
      val emailBody:Option[String] = None
      val emailTextBody:Option[String] = None

      val dummyBasicProspectDetailsObj = ProspectBasicDetails.EmailProspectBasicDetails(
        id = prospect_id,
        first_name = Some(""),
        last_name = None,
        email = prospect_email
      )

      val event = EventType.NEW_REPLY
      val emailScheduledIds:Option[Seq[Long]] = None

      describe("PATH 1.2 When prospect found in CRM") {

        it("should 1: update activity in crm tokens not found") {

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))
          (prospectServiceV2.getProspectBasicDetailsById)
            .expects(Seq(prospect_id), team_id, *)
            .returns(Success(List(dummyBasicProspectDetailsObj)))

//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.failed(new Exception("Error while fetching tokens. Please try again, or contact support.")))

          /*(() => repTrackingHostService.getRepTrackingHosts())
            .expects()
            .returning(Success(Seq(repTrackingHosts)))*/

          handleActivityTriggerEventServiceSpec.handleUpdateActivityInCRM(
            crmType = integration_type,
            moduleType = moduleType,
            event = event,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = emailScheduledIds,
            create_record_if_not_exists = true,
            teamId = team_id,
            accountId = accountId,
            campaignName = campaignName,

            workflow_crm_setting_id = Some(workflow_crm_setting_id),
          ).map(_ => {
            assert(false)
          })
            .recover { case e =>

              val exeMatch = e.getMessage.contains("Error while fetching tokens")

              Logger.info(s"\n\n\n\n in recover $exeMatch")

              exeMatch shouldBe true
            }

        }

        it("should 2: update activity in crm tokens found but prospect did't found with given id") {

          (prospectServiceV2.getProspectBasicDetailsById)
            .expects(Seq(prospect_id), team_id, *)
            .returns(Success(List(dummyBasicProspectDetailsObj)))

//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          /*(tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            ))*/

          /*(() => repTrackingHostService.getRepTrackingHosts())
            .expects()
            .returning(Success(Seq(repTrackingHosts)))*/

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Failure(new Exception("Error while fetching prospect. Please try again, or contact support.")))

          handleActivityTriggerEventServiceSpec.handleUpdateActivityInCRM(
            crmType = integration_type,
            moduleType = moduleType,
            event = event,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = emailScheduledIds,
            create_record_if_not_exists = true,
            teamId = team_id,
            accountId = accountId,
            campaignName = campaignName,

            workflow_crm_setting_id = Some(workflow_crm_setting_id),
          ).map(ActivityRes => {
            assert(ActivityRes.isLeft)
          })

        }

        it("should 3 update activity in crm: found tokens, gets Prospects, prospects fond in CRM and updateActivity is Success") {

          (prospectServiceV2.getProspectBasicDetailsById)
            .expects(Seq(prospect_id), team_id, *)
            .returns(Success(List(dummyBasicProspectDetailsObj)))

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            ))

//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))

          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name,intg_acc_tok, dummyProspectObj, moduleType, None,
              *, *, *, *)
            .returning(Future.successful(Right(Seq(IntegrationContactResponse(
              id = person_id, email = Some(prospect_email), status = Some(status_col_value)
            )))))

          (() => repTrackingHostService.getRepTrackingHosts())
            .expects()
            .returning(Success(Seq(repTrackingHosts)))

          val person_id_capt = CaptureOne[String]()
          val activity_at_capt = CaptureOne[DateTime]()
          (tIntegrationCRMService.updateActivity
          (
            _: IntegrationType,
            _: String,
            _: String,
            _: TPActivityType.Value,
            _: String,
            _: DateTime,
            _: Option[String],
            _: Option[String],
            _: Option[String],
            _: Option[String],
            _: Option[String],
            _: Option[String],
            _: IntegrationModuleType,
            _: Long,
            _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(
              crmIntegrationService.name,
              capture(person_id_capt),
              activitySubject,
              activityType,
              detailedActivitySubject,
              capture(activity_at_capt),
              emailSubject,
              emailSenderAddress,
              emailSenderFirstName,
              emailSenderLastName,
              emailBody,
              emailTextBody,
              moduleType,
              team_id,
              *, *, *, *, *
            ).returning(Future.successful(Right(person_id)))

          handleActivityTriggerEventServiceSpec.handleUpdateActivityInCRM(
            crmType = integration_type,
            moduleType = moduleType,
            event = event,
            create_record_if_not_exists = true,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = None,
            teamId = team_id,
            accountId = accountId,
            campaignName = campaignName,

            workflow_crm_setting_id = Some(workflow_crm_setting_id),
          ).map(ActivityRes => {
            assert(ActivityRes === Right(Seq(1)))
            assert(person_id_capt.value === person_id)
            assert(Minutes.minutesBetween(activity_at_capt.value, activityAt).getMinutes < 1)
          })

        }

        it("should 4: update activity to crm status: found tokens, gets Prospects, prospects fond in CRM and updateLeadStatus is Failure") {

          (prospectServiceV2.getProspectBasicDetailsById)
            .expects(Seq(prospect_id), team_id, *)
            .returns(Success(List(dummyBasicProspectDetailsObj)))

//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            ))

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))

          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name, intg_acc_tok, dummyProspectObj, moduleType, None,
              *, *, *, *)
            .returning(Future.successful(Right(Seq(IntegrationContactResponse(
              id = person_id, email = Some(prospect_email), status = Some(status_col_value)
            )))))

          (() => repTrackingHostService.getRepTrackingHosts())
            .expects()
            .returning(Success(Seq(repTrackingHosts)))

          val person_id_capt = CaptureOne[String]()
          val activity_at_capt = CaptureOne[DateTime]()
          (tIntegrationCRMService.updateActivity
          (
            _: IntegrationType,
            _: String,
            _: String,
            _: TPActivityType.Value,
            _: String,
            _: DateTime,
            _: Option[String],
            _: Option[String],
            _: Option[String],
            _: Option[String],
            _: Option[String],
            _: Option[String],
            _: IntegrationModuleType,
            _: Long,
            _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(
              crmIntegrationService.name,
              capture(person_id_capt),
              activitySubject,
              activityType,
              detailedActivitySubject,
              capture(activity_at_capt),
              emailSubject,
              emailSenderAddress,
              emailSenderFirstName,
              emailSenderLastName,
              emailBody,
              emailTextBody,
              moduleType,
              team_id,
              *, *, *, *, *
            ).returning(Future.successful(
            Left(UpdateActivityError.CommonCRMAPIError(err = CommonCRMAPIErrors.InternalServerError(msg = "Internal Server error")))
          ))

          handleActivityTriggerEventServiceSpec.handleUpdateActivityInCRM(
            crmType = integration_type,
            moduleType = moduleType,
            event = event,
            create_record_if_not_exists = true,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = None,
            teamId = team_id,
            accountId = accountId,
            campaignName = campaignName,

            workflow_crm_setting_id = Some(workflow_crm_setting_id),
          ).map(ActivityRes => {
            assert(ActivityRes.isLeft)
            assert(person_id_capt.value === person_id)
            assert(Minutes.minutesBetween(activity_at_capt.value, activityAt).getMinutes < 1)
          })


        }

      }

      describe("PATH 2.5 When prospect not found in CRM AND CREATE_IF_NOT_EXISTS IS TRUE") {

        it("should 5: update activity to crm status: found tokens, gets Prospects, prospects fond in CRM and create_record_if_not_exists true nd findFieldMapping exception throws") {

          (prospectServiceV2.getProspectBasicDetailsById)
            .expects(Seq(prospect_id), team_id, *)
            .returns(Success(List(dummyBasicProspectDetailsObj)))

//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            ))

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))

          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name, intg_acc_tok, dummyProspectObj, moduleType, None,
              *, *, *, *)
            .returning(Future.successful(
              Right(Seq())
            ))
          ( createInCRMJedisDAO.getLock (_: ProspectId, _: IntegrationType, _: TeamId,_:IntegrationModuleType)(using _: SRLogger))
            .expects(*, *, *, *, *)
            .returning(false)
          /*(() => repTrackingHostService.getRepTrackingHosts())
            .expects()
            .returning(Success(Seq(repTrackingHosts)))*/

          (triggerDAO.findFieldMapping)
            .expects(team_id, integration_type, moduleType)
            .returning(Failure(new Exception("findFieldMapping error")))

          handleActivityTriggerEventServiceSpec.handleUpdateActivityInCRM(
            crmType = integration_type,
            moduleType = moduleType,
            event = event,
            create_record_if_not_exists = true,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = None,
            teamId = team_id,
            accountId = accountId,
            campaignName = campaignName,

            workflow_crm_setting_id = Some(workflow_crm_setting_id),
          )
            .map(ActivityRes => {
              assert(ActivityRes.isLeft)
            })

        }

        it("should 6: update activity to crm status: found tokens, gets Prospects, prospects fond in CRM and create_record_if_not_exists true and findFieldMapping returns NONE") {

          (prospectServiceV2.getProspectBasicDetailsById)
            .expects(Seq(prospect_id), team_id, *)
            .returns(Success(List(dummyBasicProspectDetailsObj)))

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            ))

//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))

          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name,intg_acc_tok, dummyProspectObj, moduleType, None,
              *, *, *, *)
            .returning(Future.successful(
              Right(Seq())
            ))
          ( createInCRMJedisDAO.getLock (_: ProspectId, _: IntegrationType, _: TeamId,_:IntegrationModuleType)(using _: SRLogger))
            .expects(*, *, *, *, *)
            .returning(false)
          /*(() => repTrackingHostService.getRepTrackingHosts())
            .expects()
            .returning(Success(Seq(repTrackingHosts)))*/

          (triggerDAO.findFieldMapping)
            .expects(team_id, integration_type, moduleType)
            .returning(Success(None))

          handleActivityTriggerEventServiceSpec.handleUpdateActivityInCRM(
            crmType = integration_type,
            moduleType = moduleType,
            event = event,
            create_record_if_not_exists = true,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = None,
            teamId = team_id,
            accountId = accountId,
            campaignName = campaignName,

            workflow_crm_setting_id = Some(workflow_crm_setting_id),
          )
            .map(ActivityRes => {
              assert(ActivityRes.isLeft)
            })

        }

        it("should 7: update activity to crm status: found tokens, gets Prospects, prospects fond in CRM and create_record_if_not_exists true and findFieldMapping returns success and usermapping success and buildcontactonject success and createOrUpdateSingleContact return None") {

          (prospectServiceV2.getProspectBasicDetailsById)
            .expects(Seq(prospect_id), team_id, *)
            .returns(Success(List(dummyBasicProspectDetailsObj)))


//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            ))

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))

          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name, intg_acc_tok, dummyProspectObj, moduleType, None,
              *, *, *, *)
            .returning(Future.successful(
              Right(Seq())
            ))
          ( createInCRMJedisDAO.getLock (_: ProspectId, _: IntegrationType, _: TeamId,_:IntegrationModuleType)(using _: SRLogger))
            .expects(*, *, *, *, *)
            .returning(false)
          /*(() => repTrackingHostService.getRepTrackingHosts())
            .expects()
            .returning(Success(Seq(repTrackingHosts)))*/

          (triggerDAO.findFieldMapping)
            .expects(team_id, integration_type, moduleType)
            .returning(Success(Some(updateFieldsMappingForm)))

          (tIntegrationCRMService.findUpdatedUserMapping
          (_: IntegrationTPAccessTokenResponse.FullTokenData, _: Long, _: Long, _: IntegrationType)(_: WSClient, _: ExecutionContext, _: SRLogger))
            .expects(intg_acc_tok, team_id, accountId, integration_type, *, *, *)
            .returning(Future.successful(Right(updatedUserMappingMappingForm)))

          val person_email_capt = CaptureOne[String]()
          (tIntegrationCRMService.createOrUpdateSingleContact
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType, _: ProspectObject, _: UpdateFieldsMappingForm, _: UpdateUserMappingForm, _: Long, _: Long, _: String)
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name,
              intg_acc_tok, moduleType, dummyProspectObj, updateFieldsMappingForm, updatedUserMappingMappingForm, accountId, team_id, capture(person_email_capt),
              *, *, *, *
            ).returning(Future.successful(Left(
            CreateOrUpdateSingleContactsError.CommonCRMAPIError(err = CommonCRMAPIErrors.UnknownError(msg = "Unknown error"))
          )))

          handleActivityTriggerEventServiceSpec.handleUpdateActivityInCRM(
            crmType = integration_type,
            moduleType = moduleType,
            event = event,
            create_record_if_not_exists = true,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = None,
            teamId = team_id,
            accountId = accountId,
            campaignName = campaignName,

            workflow_crm_setting_id = Some(workflow_crm_setting_id),
          )
            .map(ActivityRes => {
              assert(ActivityRes.isLeft)
            })

        }

        it("should 8: update activity to crm status: found tokens, gets Prospects, prospects fond in CRM and create_record_if_not_exists true and findFieldMapping returns success and usermapping success and buildcontactonject success and createOrUpdateSingleContact success and then update lead status Failure") {

          (prospectServiceV2.getProspectBasicDetailsById)
            .expects(Seq(prospect_id), team_id, *)
            .returns(Success(List(dummyBasicProspectDetailsObj)))


//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            ))

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))

          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name, intg_acc_tok, dummyProspectObj, moduleType, None,
              *, *, *, *)
            .returning(Future.successful(
              Right(Seq())
            ))
          ( createInCRMJedisDAO.getLock (_: ProspectId, _: IntegrationType, _: TeamId,_:IntegrationModuleType)(using _: SRLogger))
            .expects(*, *, *, *, *)
            .returning(false)
          (() => repTrackingHostService.getRepTrackingHosts())
            .expects()
            .returning(Success(Seq(repTrackingHosts)))

          (triggerDAO.findFieldMapping)
            .expects(team_id, integration_type, moduleType)
            .returning(Success(Some(updateFieldsMappingForm)))

          (tIntegrationCRMService.findUpdatedUserMapping
          (_: IntegrationTPAccessTokenResponse.FullTokenData, _: Long, _: Long, _: IntegrationType)(_: WSClient, _: ExecutionContext, _: SRLogger))
            .expects(intg_acc_tok, team_id, accountId, integration_type, *, *, *)
            .returning(Future.successful(Right(updatedUserMappingMappingForm)))

          val person_email_capt = CaptureOne[String]()
          (tIntegrationCRMService.createOrUpdateSingleContact
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType, _: ProspectObject, _: UpdateFieldsMappingForm, _: UpdateUserMappingForm, _: Long, _: Long, _: String)
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name,
              intg_acc_tok, moduleType, dummyProspectObj, updateFieldsMappingForm, updatedUserMappingMappingForm, accountId, team_id, capture(person_email_capt),
              *, *, *, *
            ).returning(Future.successful(Right(person_id)))

          val person_id_capt = CaptureOne[String]()
          val activity_at_capt = CaptureOne[DateTime]()
          (tIntegrationCRMService.updateActivity
          (
            _: IntegrationType,
            _: String,
            _: String,
            _: TPActivityType.Value,
            _: String,
            _: DateTime,
            _: Option[String],
            _: Option[String],
            _: Option[String],
            _: Option[String],
            _: Option[String],
            _: Option[String],
            _: IntegrationModuleType,
            _: Long,
            _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(
              crmIntegrationService.name,
              capture(person_id_capt),
              activitySubject,
              activityType,
              detailedActivitySubject,
              capture(activity_at_capt),
              emailSubject,
              emailSenderAddress,
              emailSenderFirstName,
              emailSenderLastName,
              emailBody,
              emailTextBody,
              moduleType,
              team_id,
              *, *, *, *, *
            ).returning(Future.successful(
            Left(UpdateActivityError.CommonCRMAPIError(err = CommonCRMAPIErrors.InternalServerError(msg = "Internal Server error")))
          ))

          handleActivityTriggerEventServiceSpec.handleUpdateActivityInCRM(
            crmType = integration_type,
            moduleType = moduleType,
            event = event,
            create_record_if_not_exists = true,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = None,
            teamId = team_id,
            accountId = accountId,
            campaignName = campaignName,

            workflow_crm_setting_id = Some(workflow_crm_setting_id),
          )
            .map(ActivityRes => {
              assert(ActivityRes.isLeft)
              /*assert(person_id_capt.value === person_id)
              assert(Minutes.minutesBetween(activity_at_capt.value, activityAt).getMinutes < 1)*/
            })

        }

        it("should 9: update activity to crm status: found tokens, gets Prospects, prospects fond in CRM and create_record_if_not_exists true and findFieldMapping returns success and usermapping success and buildcontactonject success and createOrUpdateSingleContact success and then update lead status success") {

          (prospectServiceV2.getProspectBasicDetailsById)
            .expects(Seq(prospect_id), team_id, *)
            .returns(Success(List(dummyBasicProspectDetailsObj)))


//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            ))

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))

          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name, intg_acc_tok, dummyProspectObj, moduleType, None,
              *, *, *, *)
            .returning(Future.successful(
              Right(Seq())
            ))
          ( createInCRMJedisDAO.getLock (_: ProspectId, _: IntegrationType, _: TeamId,_:IntegrationModuleType)(using _: SRLogger))
            .expects(*, *, *, *, *)
            .returning(false)
          (() => repTrackingHostService.getRepTrackingHosts())
            .expects()
            .returning(Success(Seq(repTrackingHosts)))

          (triggerDAO.findFieldMapping)
            .expects(team_id, integration_type, moduleType)
            .returning(Success(Some(updateFieldsMappingForm)))

          (tIntegrationCRMService.findUpdatedUserMapping
          (_: IntegrationTPAccessTokenResponse.FullTokenData, _: Long, _: Long, _: IntegrationType)(_: WSClient, _: ExecutionContext, _: SRLogger))
            .expects(intg_acc_tok, team_id, accountId, integration_type, *, *, *)
            .returning(Future.successful(Right(updatedUserMappingMappingForm)))

          val person_email_capt = CaptureOne[String]()
          (tIntegrationCRMService.createOrUpdateSingleContact
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType, _: ProspectObject, _: UpdateFieldsMappingForm, _: UpdateUserMappingForm, _: Long, _: Long, _: String)
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name,
              intg_acc_tok, moduleType, dummyProspectObj, updateFieldsMappingForm, updatedUserMappingMappingForm, accountId, team_id, capture(person_email_capt),
              *, *, *, *
            ).returning(Future.successful(Right(person_id)))

          val person_id_capt = CaptureOne[String]()
          val activity_at_capt = CaptureOne[DateTime]()
          (tIntegrationCRMService.updateActivity
          (
            _: IntegrationType,
            _: String,
            _: String,
            _: TPActivityType.Value,
            _: String,
            _: DateTime,
            _: Option[String],
            _: Option[String],
            _: Option[String],
            _: Option[String],
            _: Option[String],
            _: Option[String],
            _: IntegrationModuleType,
            _: Long,
            _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(
              crmIntegrationService.name,
              capture(person_id_capt),
              activitySubject,
              activityType,
              detailedActivitySubject,
              capture(activity_at_capt),
              emailSubject,
              emailSenderAddress,
              emailSenderFirstName,
              emailSenderLastName,
              emailBody,
              emailTextBody,
              moduleType,
              team_id,
              *, *, *, *, *
            ).returning(Future.successful(Right(person_id)))

          handleActivityTriggerEventServiceSpec.handleUpdateActivityInCRM(
            crmType = integration_type,
            moduleType = moduleType,
            event = event,
            create_record_if_not_exists = true,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = None,
            teamId = team_id,
            accountId = accountId,
            campaignName = campaignName,

            workflow_crm_setting_id = Some(workflow_crm_setting_id),
          )
            .map(ActivityRes => {
              assert(ActivityRes === Right(Seq(1)))
              assert(person_id_capt.value === person_id)
              assert(Minutes.minutesBetween(activity_at_capt.value, activityAt).getMinutes < 1)
            })

        }

      }

      describe("PATH 3.5 When prospect not found in CRM AND CREATE_IF_NOT_EXISTS IS FALSE") {

        it("should 10: update activity to crm status: found tokens, gets Prospects, prospects fond in CRM and create_record_if_not_exists false") {

          (prospectServiceV2.getProspectBasicDetailsById)
            .expects(Seq(prospect_id), team_id, *)
            .returns(Success(List(dummyBasicProspectDetailsObj)))

//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            ))

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))

          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name, intg_acc_tok, dummyProspectObj, moduleType, None,
              *, *, *, *)
            .returning(Future.successful(
              Right(Seq())
            ))

          /*(() => repTrackingHostService.getRepTrackingHosts())
            .expects()
            .returning(Success(Seq(repTrackingHosts)))*/

          handleActivityTriggerEventServiceSpec.handleUpdateActivityInCRM(
            crmType = integration_type,
            moduleType = moduleType,
            event = event,
            create_record_if_not_exists = false,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = None,
            teamId = team_id,
            accountId = accountId,
            campaignName = campaignName,

            workflow_crm_setting_id = Some(workflow_crm_setting_id),
          )
            .map(ActivityRes => {
              assert(ActivityRes.isLeft)
            })

        }

      }

    }

    describe("handleUpdateLeadStatusInCRM with EMAIL_OPEN and emailScheduledids not empty") {

      val activitySubject = "Email opened"

      val detailedActivitySubject = s"""Opened email from Demo campaign campaign"""

      val activityType = TPActivityType.EMAIL

      val activityAt = DateTime.now()

      val emailSubject:Option[String] = None
      val emailSenderAddress:Option[String] = None
      val emailSenderFirstName:Option[String] = Some("")
      val emailSenderLastName:Option[String] = None
      val emailBody:Option[String] = None
      val emailTextBody:Option[String] = None

      val event = EventType.EMAIL_OPENED
      val emailScheduledIds:Option[Seq[Long]] = Some(Seq(786786))

      val getOnlyNewReplies = false
      val dummyEmailScheduledObjForWebhooks = EmailReceivedForWebhook(
        id = emailScheduledIds.get.head,

        campaign_name = campaignName,

        prospect_id = prospect_id,
        email = prospect_email,
        first_name = Some(""),
        last_name = None,
        prospect_list = None,
        custom_fields = Json.obj(),
        company = None,
        city = None,
        country = None,

        subject = "",
        body = "",
        body_text = "",
        email_thread_id = None,
        from_email = "",
        received_at = DateTime.now(),
        step_name = None,

        sent_at = None,
        opened_at = None,
        clicked_at = None,
        bounced_at = None,
        replied_at = None,
        auto_reply_at = None,
        out_of_office_reply_at = None
      )

      describe("PATH 1.3 When prospect found in CRM") {

        it("should 1: update activity in crm tokens not found") {

          (emailScheduledDAOService.findForWebhooks (_: Seq[Long], _: Boolean, _: Long)(using _: SRLogger))
            .expects(emailScheduledIds.get, getOnlyNewReplies, team_id, *)
            .returns(Success(Seq(dummyEmailScheduledObjForWebhooks)))
          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))

//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.failed(new Exception("Error while fetching tokens. Please try again, or contact support.")))

          /*(() => repTrackingHostService.getRepTrackingHosts())
            .expects()
            .returning(Success(Seq(repTrackingHosts)))*/

          handleActivityTriggerEventServiceSpec.handleUpdateActivityInCRM(
            crmType = integration_type,
            moduleType = moduleType,
            event = event,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = emailScheduledIds,
            create_record_if_not_exists = true,
            teamId = team_id,
            accountId = accountId,
            campaignName = campaignName,

            workflow_crm_setting_id = Some(workflow_crm_setting_id),
          ).map(_ => {
            assert(false)
          })
            .recover { case e =>

              val exeMatch = e.getMessage.contains("Error while fetching tokens")

              Logger.info(s"\n\n\n\n in recover $exeMatch")

              exeMatch shouldBe true
            }

        }

        it("should 2: update activity in crm tokens found but prospect did't found with given id") {

          (emailScheduledDAOService.findForWebhooks (_: Seq[Long], _: Boolean, _: Long)(using _: SRLogger))
            .expects(emailScheduledIds.get, getOnlyNewReplies, team_id, *)
            .returns(Success(Seq(dummyEmailScheduledObjForWebhooks)))

//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          /*(tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            ))*/

          /*(() => repTrackingHostService.getRepTrackingHosts())
            .expects()
            .returning(Success(Seq(repTrackingHosts)))*/

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Failure(new Exception("Error while fetching prospect. Please try again, or contact support.")))

          handleActivityTriggerEventServiceSpec.handleUpdateActivityInCRM(
            crmType = integration_type,
            moduleType = moduleType,
            event = event,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = emailScheduledIds,
            create_record_if_not_exists = true,
            teamId = team_id,
            accountId = accountId,
            campaignName = campaignName,

            workflow_crm_setting_id = Some(workflow_crm_setting_id),
          ).map(ActivityRes => {
            assert(ActivityRes.isLeft)
          })

        }

        it("should 3 update activity in crm: found tokens, gets Prospects, prospects fond in CRM and updateActivity is Success") {

          (emailScheduledDAOService.findForWebhooks (_: Seq[Long], _: Boolean, _: Long)(using _: SRLogger))
            .expects(emailScheduledIds.get, getOnlyNewReplies, team_id, *)
            .returns(Success(Seq(dummyEmailScheduledObjForWebhooks)))

//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            ))

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))

          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name, intg_acc_tok, dummyProspectObj, moduleType, None,
              *, *, *, *)
            .returning(Future.successful(Right(Seq(IntegrationContactResponse(
              id = person_id, email = Some(prospect_email), status = Some(status_col_value)
            )))))

          (() => repTrackingHostService.getRepTrackingHosts())
            .expects()
            .returning(Success(Seq(repTrackingHosts)))

          val person_id_capt = CaptureOne[String]()
          val activity_at_capt = CaptureOne[DateTime]()
          (tIntegrationCRMService.updateActivity
          (
            _: IntegrationType,
            _: String,
            _: String,
            _: TPActivityType.Value,
            _: String,
            _: DateTime,
            _: Option[String],
            _: Option[String],
            _: Option[String],
            _: Option[String],
            _: Option[String],
            _: Option[String],
            _: IntegrationModuleType,
            _: Long,
            _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(
              crmIntegrationService.name,
              capture(person_id_capt),
              activitySubject,
              activityType,
              detailedActivitySubject,
              capture(activity_at_capt),
              emailSubject,
              emailSenderAddress,
              emailSenderFirstName,
              emailSenderLastName,
              emailBody,
              emailTextBody,
              moduleType,
              team_id,
              *, *, *, *, *
            ).returning(Future.successful(Right(person_id)))

          handleActivityTriggerEventServiceSpec.handleUpdateActivityInCRM(
            crmType = integration_type,
            moduleType = moduleType,
            event = event,
            create_record_if_not_exists = true,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = emailScheduledIds,
            teamId = team_id,
            accountId = accountId,
            campaignName = campaignName,

            workflow_crm_setting_id = Some(workflow_crm_setting_id),
          ).map(ActivityRes => {
            assert(ActivityRes === Right(Seq(1)))
            assert(person_id_capt.value === person_id)
            assert(Minutes.minutesBetween(activity_at_capt.value, activityAt).getMinutes < 1)
          })

        }

        it("should 4: update activity to crm status: found tokens, gets Prospects, prospects fond in CRM and updateLeadStatus is Failure") {

          (emailScheduledDAOService.findForWebhooks (_: Seq[Long], _: Boolean, _: Long)(using _: SRLogger))
            .expects(emailScheduledIds.get, getOnlyNewReplies, team_id, *)
            .returns(Success(Seq(dummyEmailScheduledObjForWebhooks)))

//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            ))

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))

          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name, intg_acc_tok, dummyProspectObj, moduleType, None,
              *, *, *, *)
            .returning(Future.successful(Right(Seq(IntegrationContactResponse(
              id = person_id, email = Some(prospect_email), status = Some(status_col_value)
            )))))

          (() => repTrackingHostService.getRepTrackingHosts())
            .expects()
            .returning(Success(Seq(repTrackingHosts)))

          val person_id_capt = CaptureOne[String]()
          val activity_at_capt = CaptureOne[DateTime]()
          (tIntegrationCRMService.updateActivity
          (
            _: IntegrationType,
            _: String,
            _: String,
            _: TPActivityType.Value,
            _: String,
            _: DateTime,
            _: Option[String],
            _: Option[String],
            _: Option[String],
            _: Option[String],
            _: Option[String],
            _: Option[String],
            _: IntegrationModuleType,
            _: Long,
            _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(
              crmIntegrationService.name,
              capture(person_id_capt),
              activitySubject,
              activityType,
              detailedActivitySubject,
              capture(activity_at_capt),
              emailSubject,
              emailSenderAddress,
              emailSenderFirstName,
              emailSenderLastName,
              emailBody,
              emailTextBody,
              moduleType,
              team_id,
              *, *, *, *, *
            ).returning(Future.successful(
            Left(UpdateActivityError.CommonCRMAPIError(err = CommonCRMAPIErrors.InternalServerError(msg = "Internal Server error")))
          ))

          handleActivityTriggerEventServiceSpec.handleUpdateActivityInCRM(
            crmType = integration_type,
            moduleType = moduleType,
            event = event,
            create_record_if_not_exists = true,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = emailScheduledIds,
            teamId = team_id,
            accountId = accountId,
            campaignName = campaignName,

            workflow_crm_setting_id = Some(workflow_crm_setting_id),
          ).map(ActivityRes => {
            assert(ActivityRes.isLeft)
            assert(person_id_capt.value === person_id)
            assert(Minutes.minutesBetween(activity_at_capt.value, activityAt).getMinutes < 1)
          })


        }

      }

      describe("PATH 2.6 When prospect not found in CRM AND CREATE_IF_NOT_EXISTS IS TRUE") {

        it("should 5: update activity to crm status: found tokens, gets Prospects, prospects fond in CRM and create_record_if_not_exists true nd findFieldMapping exception throws") {

          (emailScheduledDAOService.findForWebhooks (_: Seq[Long], _: Boolean, _: Long)(using _: SRLogger))
            .expects(emailScheduledIds.get, getOnlyNewReplies, team_id, *)
            .returns(Success(Seq(dummyEmailScheduledObjForWebhooks)))

//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            ))

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))

          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name, intg_acc_tok, dummyProspectObj, moduleType, None,
              *, *, *, *)
            .returning(Future.successful(
              Right(Seq())
            ))
          ( createInCRMJedisDAO.getLock (_: ProspectId, _: IntegrationType, _: TeamId, _:IntegrationModuleType)(using _: SRLogger))
            .expects(*, *, *, *, *)
            .returning(false)
          /*(() => repTrackingHostService.getRepTrackingHosts())
            .expects()
            .returning(Success(Seq(repTrackingHosts)))*/

          (triggerDAO.findFieldMapping)
            .expects(team_id, integration_type, moduleType)
            .returning(Failure(new Exception("findFieldMapping error")))

          handleActivityTriggerEventServiceSpec.handleUpdateActivityInCRM(
            crmType = integration_type,
            moduleType = moduleType,
            event = event,
            create_record_if_not_exists = true,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = emailScheduledIds,
            teamId = team_id,
            accountId = accountId,
            campaignName = campaignName,

            workflow_crm_setting_id = Some(workflow_crm_setting_id),
          )
            .map(ActivityRes => {
              assert(ActivityRes.isLeft)
            })

        }

        it("should 6: update activity to crm status: found tokens, gets Prospects, prospects fond in CRM and create_record_if_not_exists true and findFieldMapping returns NONE") {

          (emailScheduledDAOService.findForWebhooks (_: Seq[Long], _: Boolean, _: Long)(using _: SRLogger))
            .expects(emailScheduledIds.get, getOnlyNewReplies, team_id, *)
            .returns(Success(Seq(dummyEmailScheduledObjForWebhooks)))


//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            ))

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))

          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name, intg_acc_tok, dummyProspectObj, moduleType, None,
              *, *, *, *)
            .returning(Future.successful(
              Right(Seq())
            ))
          ( createInCRMJedisDAO.getLock (_: ProspectId, _: IntegrationType, _: TeamId,_:IntegrationModuleType)(using _: SRLogger))
            .expects(*, *, *, *, *)
            .returning(false)
          /*(() => repTrackingHostService.getRepTrackingHosts())
            .expects()
            .returning(Success(Seq(repTrackingHosts)))*/

          (triggerDAO.findFieldMapping)
            .expects(team_id, integration_type, moduleType)
            .returning(Success(None))

          handleActivityTriggerEventServiceSpec.handleUpdateActivityInCRM(
            crmType = integration_type,
            moduleType = moduleType,
            event = event,
            create_record_if_not_exists = true,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = emailScheduledIds,
            teamId = team_id,
            accountId = accountId,
            campaignName = campaignName,

            workflow_crm_setting_id = Some(workflow_crm_setting_id),
          )
            .map(ActivityRes => {
              assert(ActivityRes.isLeft)
            })

        }

        it("should 7: update activity to crm status: found tokens, gets Prospects, prospects fond in CRM and create_record_if_not_exists true and findFieldMapping returns success and usermapping success and buildcontactonject success and createOrUpdateSingleContact return None") {

          (emailScheduledDAOService.findForWebhooks (_: Seq[Long], _: Boolean, _: Long)(using _: SRLogger))
            .expects(emailScheduledIds.get, getOnlyNewReplies, team_id, *)
            .returns(Success(Seq(dummyEmailScheduledObjForWebhooks)))


//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            ))

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))

          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name, intg_acc_tok, dummyProspectObj, moduleType, None,
              *, *, *, *)
            .returning(Future.successful(
              Right(Seq())
            ))
          ( createInCRMJedisDAO.getLock (_: ProspectId, _: IntegrationType, _: TeamId,_:IntegrationModuleType)(using _: SRLogger))
            .expects(*, *, *, *, *)
            .returning(false)
          /*(() => repTrackingHostService.getRepTrackingHosts())
            .expects()
            .returning(Success(Seq(repTrackingHosts)))*/

          (triggerDAO.findFieldMapping)
            .expects(team_id, integration_type, moduleType)
            .returning(Success(Some(updateFieldsMappingForm)))

          (tIntegrationCRMService.findUpdatedUserMapping
          (_: IntegrationTPAccessTokenResponse.FullTokenData, _: Long, _: Long, _: IntegrationType)(_: WSClient, _: ExecutionContext, _: SRLogger))
            .expects(intg_acc_tok, team_id, accountId, integration_type, *, *, *)
            .returning(Future.successful(Right(updatedUserMappingMappingForm)))

          val person_email_capt = CaptureOne[String]()
          (tIntegrationCRMService.createOrUpdateSingleContact
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType, _: ProspectObject, _: UpdateFieldsMappingForm, _: UpdateUserMappingForm, _: Long, _: Long, _: String)
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name,
              intg_acc_tok, moduleType, dummyProspectObj, updateFieldsMappingForm, updatedUserMappingMappingForm, accountId, team_id, capture(person_email_capt),
              *, *, *, *
            ).returning(Future.successful(Left(
            CreateOrUpdateSingleContactsError.CommonCRMAPIError(err = CommonCRMAPIErrors.UnknownError(msg = "Unknown error"))
          )))

          handleActivityTriggerEventServiceSpec.handleUpdateActivityInCRM(
            crmType = integration_type,
            moduleType = moduleType,
            event = event,
            create_record_if_not_exists = true,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = emailScheduledIds,
            teamId = team_id,
            accountId = accountId,
            campaignName = campaignName,

            workflow_crm_setting_id = Some(workflow_crm_setting_id),
          )
            .map(ActivityRes => {
              assert(ActivityRes.isLeft)
            })

        }

        it("should 8: update activity to crm status: found tokens, gets Prospects, prospects fond in CRM and create_record_if_not_exists true and findFieldMapping returns success and usermapping success and buildcontactonject success and createOrUpdateSingleContact success and then update lead status Failure") {

          (emailScheduledDAOService.findForWebhooks (_: Seq[Long], _: Boolean, _: Long)(using _: SRLogger))
            .expects(emailScheduledIds.get, getOnlyNewReplies, team_id, *)
            .returns(Success(Seq(dummyEmailScheduledObjForWebhooks)))


//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            ))

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))

          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name, intg_acc_tok, dummyProspectObj, moduleType, None,
              *, *, *, *)
            .returning(Future.successful(
              Right(Seq())
            ))
          ( createInCRMJedisDAO.getLock (_: ProspectId, _: IntegrationType, _: TeamId,_:IntegrationModuleType)(using _: SRLogger))
            .expects(*, *, *, *, *)
            .returning(false)
          (() => repTrackingHostService.getRepTrackingHosts())
            .expects()
            .returning(Success(Seq(repTrackingHosts)))

          (triggerDAO.findFieldMapping)
            .expects(team_id, integration_type, moduleType)
            .returning(Success(Some(updateFieldsMappingForm)))

          (tIntegrationCRMService.findUpdatedUserMapping
          (_: IntegrationTPAccessTokenResponse.FullTokenData, _: Long, _: Long, _: IntegrationType)(_: WSClient, _: ExecutionContext, _: SRLogger))
            .expects(intg_acc_tok, team_id, accountId, integration_type, *, *, *)
            .returning(Future.successful(Right(updatedUserMappingMappingForm)))

          val person_email_capt = CaptureOne[String]()
          (tIntegrationCRMService.createOrUpdateSingleContact
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType, _: ProspectObject, _: UpdateFieldsMappingForm, _: UpdateUserMappingForm, _: Long, _: Long, _: String)
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name,
              intg_acc_tok, moduleType, dummyProspectObj, updateFieldsMappingForm, updatedUserMappingMappingForm, accountId, team_id, capture(person_email_capt),
              *, *, *, *
            ).returning(Future.successful(Right(person_id)))

          val person_id_capt = CaptureOne[String]()
          val activity_at_capt = CaptureOne[DateTime]()
          (tIntegrationCRMService.updateActivity
          (
            _: IntegrationType,
            _: String,
            _: String,
            _: TPActivityType.Value,
            _: String,
            _: DateTime,
            _: Option[String],
            _: Option[String],
            _: Option[String],
            _: Option[String],
            _: Option[String],
            _: Option[String],
            _: IntegrationModuleType,
            _: Long,
            _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(
              crmIntegrationService.name,
              capture(person_id_capt),
              activitySubject,
              activityType,
              detailedActivitySubject,
              capture(activity_at_capt),
              emailSubject,
              emailSenderAddress,
              emailSenderFirstName,
              emailSenderLastName,
              emailBody,
              emailTextBody,
              moduleType,
              team_id,
              *, *, *, *, *
            ).returning(Future.successful(
            Left(UpdateActivityError.CommonCRMAPIError(err = CommonCRMAPIErrors.InternalServerError(msg = "Internal Server error")))
          ))

          handleActivityTriggerEventServiceSpec.handleUpdateActivityInCRM(
            crmType = integration_type,
            moduleType = moduleType,
            event = event,
            create_record_if_not_exists = true,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = emailScheduledIds,
            teamId = team_id,
            accountId = accountId,
            campaignName = campaignName,

            workflow_crm_setting_id = Some(workflow_crm_setting_id),
          )
            .map(ActivityRes => {
              assert(ActivityRes.isLeft)
              assert(person_id_capt.value === person_id)
              assert(Minutes.minutesBetween(activity_at_capt.value, activityAt).getMinutes < 1)
            })

        }

        it("should 9: update activity to crm status: found tokens, gets Prospects, prospects fond in CRM and create_record_if_not_exists true and findFieldMapping returns success and usermapping success and buildcontactonject success and createOrUpdateSingleContact success and then update lead status success") {

          (emailScheduledDAOService.findForWebhooks (_: Seq[Long], _: Boolean, _: Long)(using _: SRLogger))
            .expects(emailScheduledIds.get, getOnlyNewReplies, team_id, *)
            .returns(Success(Seq(dummyEmailScheduledObjForWebhooks)))


//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            ))

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))

          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name, intg_acc_tok, dummyProspectObj, moduleType, None,
              *, *, *, *)
            .returning(Future.successful(
              Right(Seq())
            ))
          ( createInCRMJedisDAO.getLock (_: ProspectId, _: IntegrationType, _: TeamId,_:IntegrationModuleType)(using _: SRLogger))
            .expects(*, *, *, *, *)
            .returning(false)
          (() => repTrackingHostService.getRepTrackingHosts())
            .expects()
            .returning(Success(Seq(repTrackingHosts)))

          (triggerDAO.findFieldMapping)
            .expects(team_id, integration_type, moduleType)
            .returning(Success(Some(updateFieldsMappingForm)))

          (tIntegrationCRMService.findUpdatedUserMapping
          (_: IntegrationTPAccessTokenResponse.FullTokenData, _: Long, _: Long, _: IntegrationType)(_: WSClient, _: ExecutionContext, _: SRLogger))
            .expects(intg_acc_tok, team_id, accountId, integration_type, *, *, *)
            .returning(Future.successful(Right(updatedUserMappingMappingForm)))

          val person_email_capt = CaptureOne[String]()
          (tIntegrationCRMService.createOrUpdateSingleContact
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType, _: ProspectObject, _: UpdateFieldsMappingForm, _: UpdateUserMappingForm, _: Long, _: Long, _: String)
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name,
              intg_acc_tok, moduleType, dummyProspectObj, updateFieldsMappingForm, updatedUserMappingMappingForm, accountId, team_id, capture(person_email_capt),
              *, *, *, *
            ).returning(Future.successful(Right(person_id)))

          val person_id_capt = CaptureOne[String]()
          val activity_at_capt = CaptureOne[DateTime]()
          (tIntegrationCRMService.updateActivity
          (
            _: IntegrationType,
            _: String,
            _: String,
            _: TPActivityType.Value,
            _: String,
            _: DateTime,
            _: Option[String],
            _: Option[String],
            _: Option[String],
            _: Option[String],
            _: Option[String],
            _: Option[String],
            _: IntegrationModuleType,
            _: Long,
            _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(
              crmIntegrationService.name,
              capture(person_id_capt),
              activitySubject,
              activityType,
              detailedActivitySubject,
              capture(activity_at_capt),
              emailSubject,
              emailSenderAddress,
              emailSenderFirstName,
              emailSenderLastName,
              emailBody,
              emailTextBody,
              moduleType,
              team_id,
              *, *, *, *, *
            ).returning(Future.successful(Right(person_id)))

          handleActivityTriggerEventServiceSpec.handleUpdateActivityInCRM(
            crmType = integration_type,
            moduleType = moduleType,
            event = event,
            create_record_if_not_exists = true,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = emailScheduledIds,
            teamId = team_id,
            accountId = accountId,
            campaignName = campaignName,

            workflow_crm_setting_id = Some(workflow_crm_setting_id),
          )
            .map(ActivityRes => {
              assert(ActivityRes === Right(Seq(1)))
              assert(person_id_capt.value === person_id)
              assert(Minutes.minutesBetween(activity_at_capt.value, activityAt).getMinutes < 1)
            })

        }

      }

      describe("PATH 3.6 When prospect not found in CRM AND CREATE_IF_NOT_EXISTS IS FALSE") {

        it("should 10: update activity to crm status: found tokens, gets Prospects, prospects fond in CRM and create_record_if_not_exists false") {

          (emailScheduledDAOService.findForWebhooks (_: Seq[Long], _: Boolean, _: Long)(using _: SRLogger))
            .expects(emailScheduledIds.get, getOnlyNewReplies, team_id, *)
            .returns(Success(Seq(dummyEmailScheduledObjForWebhooks)))


//          (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//            _: TeamId,
//            _: SrRollingUpdateFeature
//          )(_: SRLogger))
//            .expects(TeamId(team_id),SrRollingUpdateFeature.EmailNotCompulsory,*)

          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
          (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, integration_type, *, *, *)
            .returning(Future.successful(
              Right(intg_acc_tok)
            ))

          (prospectDAOService.find)
            .expects(Seq(prospect_id), Seq(), None, None, team_id, None, 500, 0, true, false, None, *)
            .returning(Success(Seq(dummyProspectObj)))

          (tIntegrationCRMService.findAllByProspectAndModule
          (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: ProspectObject, _: IntegrationModuleType, _: Option[String])
          (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
            )
            .expects(crmIntegrationService.name, intg_acc_tok, dummyProspectObj, moduleType, None,
              *, *, *, *)
            .returning(Future.successful(
              Right(Seq())
            ))

         /* (() => repTrackingHostService.getRepTrackingHosts())
            .expects()
            .returning(Success(Seq(repTrackingHosts)))*/

          handleActivityTriggerEventServiceSpec.handleUpdateActivityInCRM(
            crmType = integration_type,
            moduleType = moduleType,
            event = event,
            create_record_if_not_exists = false,
            prospectIds = Seq(prospect_id),
            emailScheduledIds = emailScheduledIds,
            teamId = team_id,
            accountId = accountId,
            campaignName = campaignName,
            workflow_crm_setting_id = Some(workflow_crm_setting_id),
          )
            .map(ActivityRes => {
              assert(ActivityRes.isLeft)
            })

        }

      }



    }

  }




  describe("HandleActivityTriggerEventService.matchEventAndTriggerCRMNewFlow") {

    val leadStatusService = mock[LeadStatusService]

    val handleActivityTriggerEventServiceSpec = new HandleActivityTriggerEventService(
      triggerDAO = triggerDAO,
      emailScheduledDAOService = emailScheduledDAOService,
      prospectDAOService = prospectDAOService,
      prospectDAOServiceV2 = prospectDAOServiceV2,
      campaignService = campaignService,
      accountService = accountService,
      //    triggerService = triggerService,
      leadStatusService = leadStatusService,
      emailNotificationService = emailNotificationService,
      campaignProspectDAOService = campaignProspectDAOService,
      campaignProspectService = campaignProspectService,
      tagService = tagService,
      prospectServiceV2 = prospectServiceV2,
      srTriggerAllowedCombos = srTriggerAllowedCombos,
      tIntegrationCRMService = tIntegrationCRMService,
      emailThreadDAO = emailThreadDAO,
      srUuidUtils = srUuidUtils,
      repTrackingHostService = repTrackingHostService,
      replySentimentDAO = replySentimentDAO
    )


    val status_column_in_crm = CRMStatusColumnNameAndOptions(
      column_name = "column_name",
      column_options = Seq(
        CRMStatusColumnOptions(
          label = "label", text_id = "text_id"
        )
      )
    )
    val activity_to_status_mapping = Seq(
      LeadStatusMapping(
        sr_activity = EventType.EMAIL_LINK_CLICKED, sr_activity_label = "sr_activity_label", crm_status = "crm_status"
      )
    )
    val crMIntegrationInDB = CRMIntegrationInDB(
      workflow_crm_setting_id = workflow_crm_setting_id,
      team_id = team_id,
      owner_id = 1,
      module_id = 1,
      crm = IntegrationType.ZOHO,
      module = IntegrationModuleType.CONTACTS,
      user_mapping = None,
      field_mapping = None,
      activity_to_status_mapping = Some(activity_to_status_mapping),
      category_to_status_mapping = None,
      status_column_in_crm = Some(status_column_in_crm),
      create_record_if_not_exists = true,
      track_activities = false,
      create_or_update_record_in_crm = false,
      crm_filters_for_add_to_do_not_contact_in_sr = None,
      allow_going_back_in_crm_status = false,
      sentiment_to_status_mapping = None,
      update_reply_sentiment_for_all_associated_prospects = false,
      error = None, error_at = None, last_alert_for_error_sent_at = None
    )

    val message = ActivityEventDataType.EmailLinkClickedEventData(
      accountId = AccountId(accountId),
      teamId = TeamId(team_id),
      campaignId = Some(CampaignId(1)),
      prospectId =  ProspectId(3),
      clickedUrl = "SomeClickedUrl",
      campaignName = Some(CampaignName("SomeCampaignName")),
      email_scheduled_id = 6L
    )

    describe("status_column_in_crm and activity_to_status_mapping is defined") {
      it("leadStatusService.handleUpdateLeadStatusInCRM send left this should be sent up") {

        (leadStatusService.handleUpdateLeadStatusInCRM(_: EventType, _: Seq[Long], _: Option[Seq[Long]], _: String, _: CRMIntegrationInDB, _: Long)(
          _: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger
        ))
          .expects(EventType.EMAIL_LINK_CLICKED, Seq(3L), Some(Seq(6L)), "crm_status", crMIntegrationInDB, accountId, *, *, *, *)
          .returning(Future.successful(Left(WorkflowAttemptTryErrorReason.UnKnownError(""))))

        (triggerDAO.findAllforMQProcess)
          .expects(accountId, team_id, EventType.EMAIL_LINK_CLICKED, Some(IntegrationType.ZOHO))
          .returning(Success(Seq()))

//        (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//          _: TeamId,
//          _: SrRollingUpdateFeature,
//
//        )(_: SRLogger))
//          .expects(*,*,*)
//          .atLeastTwice()
//          .returning(true)


        handleActivityTriggerEventServiceSpec.matchEventAndTriggerCRMNewFlow(
          intg = crMIntegrationInDB,
          message = message,
          event = EventType.EMAIL_LINK_CLICKED
        ).map { result =>
          println(s"$result")
          assert(result.isLeft)
        }.recover { case e =>
          println(e.getMessage)
          assert(false)
        }

      }


      it("matchedEvent is empty, so no call to leadStatusService (Success Path)") {

        (triggerDAO.findAllforMQProcess)
          .expects(accountId, team_id, EventType.CREATED_PROSPECT_IN_SMARTREACH, Some(IntegrationType.ZOHO))
          .returning(Success(Seq()))
        handleActivityTriggerEventServiceSpec.matchEventAndTriggerCRMNewFlow(
          intg = crMIntegrationInDB.copy(activity_to_status_mapping = Some(Seq(
            LeadStatusMapping(
              sr_activity = EventType.EMAIL_INVALID, sr_activity_label = "sr_activity_label", crm_status = "crm_status"
            )
          ))),
          message = message,
          event = EventType.CREATED_PROSPECT_IN_SMARTREACH
        ).map { result =>
          println(s"$result")
          assert(result.isRight)
        }.recover { case e =>
          println(e.getMessage)
          assert(false)
        }

      }


      it("status_column_in_crm is empty, so no call to leadStatusService (Success Path)") {

        (triggerDAO.findAllforMQProcess)
          .expects(accountId, team_id, EventType.CREATED_PROSPECT_IN_SMARTREACH, Some(IntegrationType.ZOHO))
          .returning(Success(Seq()))
        handleActivityTriggerEventServiceSpec.matchEventAndTriggerCRMNewFlow(
          intg = crMIntegrationInDB.copy(status_column_in_crm = None),
          message = message,
          event = EventType.CREATED_PROSPECT_IN_SMARTREACH
        ).map { result =>
          println(s"$result")
          assert(result.isRight)
        }.recover { case e =>
          println(e.getMessage)
          assert(false)
        }

      }


      it("track_activities true it should run findAndRunSmartreachOnlyTriggers") {

        (triggerDAO.findAllforMQProcess)
          .expects(accountId, team_id, EventType.EMAIL_LINK_CLICKED, Some(IntegrationType.SMARTREACH))
          .returning(Success(Seq()))
        (emailScheduledDAOService.findForWebhooks(_: Seq[Long], _: Boolean, _: Long)(using _: SRLogger))
          .expects(Seq(6L), false, 2L, *)
          .returning(Success(Seq()))
        handleActivityTriggerEventServiceSpec.matchEventAndTriggerCRMNewFlow(
          intg = crMIntegrationInDB.copy(status_column_in_crm = None, track_activities = true),
          message = message,
          event = EventType.EMAIL_LINK_CLICKED
        ).map { result =>
          println(s"$result")
          assert(result.isRight)
        }.recover { case e =>
          println(e.getMessage)
          assert(false)
        }

      }
      describe("For ReplySentimentUpdate") {
        val message = ActivityEventDataType.ReplySentimentUpdatedEventData.ReplySentimentUpdatedEventDataEmail(
          accountId = AccountId(accountId),
          teamId = TeamId(team_id),
          threadId = 6L,
          replySentimentUuid = ReplySentimentUuid("sr_sentiment_uuid")
        )
        val sentiment_to_status_mapping = Some(Seq(SentimentToStatusMapping(
          sr_sentiment_uuid = "sr_sentiment_uuid", sr_sentiment_label = "sr_sentiment_label", crm_status = "crm_status"
        )))

        val conversationObject = ConversationObject(
          id = 234L,
          team_id = TeamId(2),
          owner_id = AccountId(accountId),
          owner_name = "owner_name",
          folder_type = None,
          snoozed_till = None,
          latest_message = MessageObject.EmailMessageObject(
            uuid = Some("em_2UVHDrjenvc"),
            from_user = true,
            from = IEmailAddress(email = "<EMAIL>"),
            reply_to = None,
            to = Seq(IEmailAddress(email = "<EMAIL>")),
            cc_emails = None,
            bcc_emails = None,
            subject = "subject",
            body = "body",
            body_preview = "body_preview",
            sent_at = DateTime.now()),
          latest_reply_at = Some(DateTime.now()),
          is_read = true,
          contacts = Seq(),
          campaign = ConversationCampaign(
            campaign_id = Some(1),
            campaign_uuid = Some("uuid"),
            campaign_name = Some("SomeCampaignName"),
            campaign_tz = Some("campaign_tz"),
            show_rescheduling_option = false,
            will_resume_at = None,
            will_resume_at_tz = None),
          prospects = Seq(),
          primary_prospect =Some( ConversationProspect(
            prospect_email: String,
            prospect_name = None, prospect_id: Long,
            prospect_owner_id = accountId, prospect_tags = Seq(),
            prospect_category = "prospect_category",
            prospect_category_label_color = "prospect_category_label_color",
            prospect_category_id = 678,prospect_uuid = "prospect_uuid"))
        )
        it("getConversationObjectForThreadId sends Error") {
          (replySentimentDAO.getReplySentimentsForUUID(_: Long, _: ReplySentimentUuid)(_: SRLogger))
            .expects(team_id, ReplySentimentUuid("sr_sentiment_uuid"),*)
            .returning(Success(Some(
              ReplySentimentForTeam(
                uuid = ReplySentimentUuid("sr_sentiment_uuid"),
                reply_sentiment = ReplySentimentTypeData.ReferralData(
                  replySentimentSubCategory = ReplySentimentSubCategory.EmailFollowUpReferredSomeone,
              )))))
          (emailThreadDAO.getConversationObjectForThreadId (_: Long, _: TeamId)(using _: SRLogger))
            .expects(6L, TeamId(2L), *)
            .returning(Failure(Error))

          handleActivityTriggerEventServiceSpec.matchEventAndTriggerCRMNewFlow(
            intg = crMIntegrationInDB.copy(
              update_reply_sentiment_for_all_associated_prospects = false,
              sentiment_to_status_mapping = sentiment_to_status_mapping, activity_to_status_mapping = None),
            message = message,
            event = EventType.REPLY_SENTIMENT_UPDATED
          ).map { result =>
            println(s"$result")
            assert(false)
          }.recover { case e =>
            println(e.getMessage)
            assert(true)
          }

        }

        it("matchedSentimentMapping is empty") {
          (replySentimentDAO.getReplySentimentsForUUID(_: Long, _: ReplySentimentUuid)(_: SRLogger))
            .expects(team_id, ReplySentimentUuid("reply_sentiment_uuid"),*)
            .returning(Success(None))
          (triggerDAO.findAllforMQProcess)
            .expects(accountId, team_id, EventType.REPLY_SENTIMENT_UPDATED, Some(IntegrationType.ZOHO))
            .returning(Success(Seq()))

          (emailThreadDAO.getConversationObjectForThreadId(_: Long, _: TeamId)(using _: SRLogger))
            .expects(6L, TeamId(2L), *)
            .returning(Success(conversationObject))

          handleActivityTriggerEventServiceSpec.matchEventAndTriggerCRMNewFlow(
            intg = crMIntegrationInDB.copy(
              update_reply_sentiment_for_all_associated_prospects = false,
              sentiment_to_status_mapping = sentiment_to_status_mapping, activity_to_status_mapping = None),
            message = message.copy(replySentimentUuid = ReplySentimentUuid("reply_sentiment_uuid")),
            event = EventType.REPLY_SENTIMENT_UPDATED
          ).map { result =>
            println(s"$result")
            assert(result.isRight)
          }.recover { case e =>
            println(e.getMessage)
            assert(false)
          }

        }

        it("matchedSentimentMapping is non empty") {
          (replySentimentDAO.getReplySentimentsForUUID(_: Long, _: ReplySentimentUuid)(_: SRLogger))
            .expects(team_id, ReplySentimentUuid("sr_sentiment_uuid"),*)
            .returning(Success(Some(
              ReplySentimentForTeam(
                uuid = ReplySentimentUuid("sr_sentiment_uuid"),
                reply_sentiment = ReplySentimentTypeData.ReferralData(
                  replySentimentSubCategory = ReplySentimentSubCategory.EmailPositiveOtherPositive,
                )))))
          
          (triggerDAO.findAllforMQProcess)
            .expects(accountId, team_id, EventType.REPLY_SENTIMENT_UPDATED, Some(IntegrationType.ZOHO))
            .returning(Success(Seq()))

          (emailThreadDAO.getConversationObjectForThreadId(_: Long, _: TeamId)(using _: SRLogger))
            .expects(6L, TeamId(2L), *)
            .returning(Success(conversationObject))

          (leadStatusService.handleUpdateLeadStatusInCRM(
            _: EventType,
            _: Seq[Long],
            _: Option[Seq[Long]],
            _: String,
            _: CRMIntegrationInDB,
            _: Long)(_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger))
            .expects(EventType.REPLY_SENTIMENT_UPDATED, Seq(prospect_id), None, "crm_status", crMIntegrationInDB.copy(update_reply_sentiment_for_all_associated_prospects = false, sentiment_to_status_mapping = sentiment_to_status_mapping, activity_to_status_mapping = None),
              3L, *, *, *, *)
            .returning(Future.successful(Right(Seq(0))))

            handleActivityTriggerEventServiceSpec.matchEventAndTriggerCRMNewFlow(
            intg = crMIntegrationInDB.copy(
              update_reply_sentiment_for_all_associated_prospects = false,
              sentiment_to_status_mapping = sentiment_to_status_mapping, activity_to_status_mapping = None),
            message = message.copy(replySentimentUuid = ReplySentimentUuid("sr_sentiment_uuid")),
            event = EventType.REPLY_SENTIMENT_UPDATED
          ).map { result =>
            println(s"$result")
            assert(result.isRight)
          }.recover { case e =>
            println(e.getMessage)
            assert(false)
          }

        }

      }
    }


  }

}
