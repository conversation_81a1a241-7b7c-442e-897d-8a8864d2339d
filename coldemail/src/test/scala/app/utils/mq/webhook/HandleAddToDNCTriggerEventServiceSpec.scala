package app.utils.mq.webhook

import org.apache.pekko.actor.ActorSystem
import org.apache.pekko.stream.ActorMaterializer
import api.accounts.models.AccountProfileInfo
import api.accounts.{Account, AccountAccess, AccountMetadata, AccountService, AccountType, AccountUuid, OrgCountData, OrgMetadata, OrgPlan, OrgSettings, OrganizationRole, OrganizationWithCurrentData, ReplyHandling, TeamAccount, TeamAccountRole, TeamMember, TeamMemberLite}
import api.blacklist.BlacklistService
import api.calendar_app.models.CalendarAccountData
import api.integrations.services.TIntegrationCRMService
import api.integrations.{CommonCRMAPIErrors, CrmSpecificData, FetchTokensFromDBAndRefreshAccessTokenError, IntegrationTPAccessTokenResponse, RefreshAccessTokenError, TIntegrationCRMTrait}
import api.sr_audit_logs.models.{WorkflowAttemptInternalServerError, WorkflowAttemptTryErrorReason}
import api.team.TeamUuid
import api.triggers.*
import app.test_fixtures.accounts.OrgCountDataFixture
import app.test_fixtures.organizationa.{OrgMetadataFixture, OrgPlanFixture}
import org.joda.time.DateTime
import org.scalamock.matchers.ArgCapture.CaptureOne
import org.scalamock.scalatest.AsyncMockFactory
import org.scalatest.BeforeAndAfter
import org.scalatest.funspec.AsyncFunSpec
import play.api.libs.json.JsValue
import play.api.libs.ws.{WSClient, WSResponse}
import play.api.libs.ws.ahc.AhcWSClient
import utils.SRLogger
import utils.mq.do_not_contact.{MQDoNotContactConsumer, MQDoNotContactPublisher}
import utils.mq.webhook.HandleAddToDNCTriggerEventService
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

class HandleAddToDNCTriggerEventServiceSpec
  extends AsyncFunSpec
    with AsyncMockFactory
    with BeforeAndAfter  {

  implicit lazy val system: ActorSystem = ActorSystem()
  implicit lazy val wSClient: AhcWSClient = AhcWSClient()
  implicit lazy val actorContext: ExecutionContext = system.dispatcher

  given Logger: SRLogger = new SRLogger("Unit test")


  describe("_addToDoNotContactList") {

    val triggerDAO = mock[Trigger]
    val accountService = mock[AccountService]
    val blacklistService = mock[BlacklistService]
    val mqDoNotContactPublisher = new MQDoNotContactPublisher
    val integrationTypeService = mock[IntegrationTypeService]
    val tIntegrationCRMService = mock[TIntegrationCRMService]
    val srRollingUpdateCoreService = mock[ SrRollingUpdateCoreService]



    val handleAddToDNCTriggerEventService = new HandleAddToDNCTriggerEventService(
      accountService = accountService,
      triggerDAO = triggerDAO,
      mqDoNotContactPublisher = mqDoNotContactPublisher,
      tIntegrationCRMService = tIntegrationCRMService,
      srRollingUpdateCoreService = srRollingUpdateCoreService
    )

    val empty_email = ""
    val integration_type = IntegrationType.SALESFORCE
    val moduleType = IntegrationModuleType.LEADS

    trait IntegrationsCRMTestTrait extends TIntegrationCRMTrait {
      val name = IntegrationType.SALESFORCE
    }
    val crmIntegrationService = mock[IntegrationsCRMTestTrait]

    val access_token = "abcd-1234"
    val refresh_token = Some("wxyz-********")
    val expires_in = Some(********);
    val expires_at = Some(DateTime.now())
    val token_type = Some("acc-tok")
    val api_domain = Some("api.domain.com")
    val intg_acc_tok = IntegrationTPAccessTokenResponse.FullTokenData(
      access_token = access_token,
      refresh_token = refresh_token,
      expires_in = expires_in,
      expires_at = expires_at,
      token_type = token_type,
      api_domain = api_domain,
      is_sandbox = Some(false)
    )

    val team_id: Long = 2
    val accountId: Long = 3
    val module_id: Long = 9

    describe("PATH 1 tokens not found ") {

      it("_addToDoNotContactList should return exception while fetching tokens CommonCRMAPIError") {
        (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
        (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
          .expects(team_id, integration_type, *, *, *)
          .returning(Future.successful(
            Left(
              FetchTokensFromDBAndRefreshAccessTokenError.RefreshAccessTokenAPIError(
                err = RefreshAccessTokenError.MalformedRefreshAccessTokenResponseError(msg = "Malformed response error")
              ))
          ))
        handleAddToDNCTriggerEventService._addToDoNotContactList(
          crm_type = integration_type,
          module_type = moduleType,
          module_id = module_id,
          teamId = team_id,
          accountId = accountId,
          tp_filter_id = None,
          last_sync_at = None,
        ).map(res => {
          assert(res.isLeft)
        })
      }

      it("_addToDoNotContactList should return exception while fetching tokens SQLException") {
        (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
        (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
          .expects(team_id, integration_type, *, *, *)
          .returning(Future.successful(
            Left(FetchTokensFromDBAndRefreshAccessTokenError.SQLException(error = new Exception("SQL EXCEPTION")))
          ))
        handleAddToDNCTriggerEventService._addToDoNotContactList(
          crm_type = integration_type,
          module_type = moduleType,
          module_id = module_id,
          teamId = team_id,
          accountId = accountId,
          tp_filter_id = None,
          last_sync_at = None,
        ).map(res => {
          assert(res.isLeft)
        })
      }

      it("_addToDoNotContactList should return exception while fetching tokens TokensNotFoundInDBError") {
        (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
        (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
          .expects(team_id, integration_type, *, *, *)
          .returning(Future.successful(
            Left(FetchTokensFromDBAndRefreshAccessTokenError.TokensNotFoundInDBError(msg = "TokensNotFoundInDBError"))
          ))

        handleAddToDNCTriggerEventService._addToDoNotContactList(
          crm_type = integration_type,
          module_type = moduleType,
          module_id = module_id,
          teamId = team_id,
          accountId = accountId,
          tp_filter_id = None,
          last_sync_at = None,
        ).map(res => {
          assert(res.isLeft)
        })
      }

      it("_addToDoNotContactList should return exception while fetching tokens InvalidRefreshTokenError") {
        (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
        (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
          .expects(team_id, integration_type, *, *, *)
          .returning(Future.successful(
            Left(FetchTokensFromDBAndRefreshAccessTokenError.InvalidRefreshTokenError(msg = "InvalidRefreshTokenError"))
          ))

        handleAddToDNCTriggerEventService._addToDoNotContactList(
          crm_type = integration_type,
          module_type = moduleType,
          module_id = module_id,
          teamId = team_id,
          accountId = accountId,
          tp_filter_id = None,
          last_sync_at = None,
        ).map(res => {
          assert(res.isLeft)
        })
      }

    }

    describe("PATH 2 tokens found and account not found ") {
      it("should return tokens and exception while accountService.find") {
        val SQLERROR = new Exception("accountService.find error")

        (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
        (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
          .expects(team_id, integration_type, *, *, *)
          .returning(Future.successful(
            Right(intg_acc_tok)
          ))

        (accountService.find(_: Long)(_: SRLogger))
          .expects(accountId, *)
          .returning(Failure(SQLERROR))

        handleAddToDNCTriggerEventService._addToDoNotContactList(
          crm_type = integration_type,
          module_type = moduleType,
          module_id = module_id,
          teamId = team_id,
          accountId = accountId,
          tp_filter_id = None,
          last_sync_at = None,
        ).map(dncRes => {
          assert(dncRes.isLeft)
        })
      }
    }

    describe("PATH 3 tokens found and account found get recent contacts success ") {

      val account_email = "<EMAIL>"

      val first_name = "Huge"
      val last_name = "Jhon"

      val dummyAccountProfileInfo = AccountProfileInfo(
        first_name = first_name,
        last_name = last_name,
        company = None,
        timezone = None,
        country_code = None,
        mobile_country_code = None,
        mobile_number = None,
      onboarding_phone_number= None,
        twofa_enabled = false,
        has_gauthenticator = false,
        weekly_report_emails = None,
        scheduled_for_deletion_at = None
      )

      val dummyAccountMetadata = AccountMetadata(
        is_profile_onboarding_done = None
      )

      val dummyOrgPlan = OrgPlanFixture.orgPlanFixture

      val dummyOrgCountData: OrgCountData = OrgCountDataFixture.orgCountData_default


      val dummyOrgSettings = OrgSettings (
        enable_ab_testing = true,
        disable_force_send = true,
        bulk_sender = true,
        allow_2fa = true,
        show_2fa_setting = true,
        enforce_2fa = true,
        allow_native_crm_integration = true,
          agency_option_allow_changing = false,
          agency_option_show = false
      )

      val dummyOrgMetadata = OrgMetadataFixture.orgMetadataFixture2

      val dummyOrganizationWithCurrentData = OrganizationWithCurrentData(

        id = 24,
        name = "new org",

        owner_account_id = accountId,

        counts = dummyOrgCountData,
        settings = dummyOrgSettings,
        plan = dummyOrgPlan,

        is_agency = false,
        trial_ends_at = DateTime.now(),

        error = None,
        error_code = None,
        paused_till = None,

        errors = Seq(),
        warnings = Seq(),
        via_referral = false,
        org_metadata = dummyOrgMetadata

      )

      val dummyTeamMember = TeamMember(

        team_id = team_id,
        team_name = "Test Company",
        user_id = accountId,
        ta_id = accountId,
        first_name = Some(first_name),
        last_name = Some(last_name),
        email = account_email,
        team_role = TeamAccountRole.ADMIN,
        api_key = None,
        zapier_key = Some("zapier_key")
      )

      val dummyTeamMemberLite = TeamMemberLite(
        user_id = accountId,
        first_name = Some(first_name),
        last_name = Some(last_name),
        email = account_email,
        active = true,
        timezone = Some("campaignTimezone"),
        twofa_enabled = true,
        created_at = DateTime.now(),
        user_uuid = AccountUuid("uuid"),
        team_role = TeamAccountRole.ADMIN
      )



      val dummyTeamAccount = TeamAccount(

        team_id = team_id,
        org_id = 24,

        role_from_db = None,
        role = None,
        active = true,
        is_actively_used = true,
        team_name = "Test Company",
        total_members = 2,
        access_members = Seq(dummyTeamMember),
        all_members = Seq(dummyTeamMemberLite),

        prospect_categories_custom = Seq(),
        max_emails_per_prospect_per_day = 50,
        max_emails_per_prospect_per_week = 100,
        max_emails_per_prospect_account_per_day = 97,
        max_emails_per_prospect_account_per_week = 497,
        reply_handling = ReplyHandling.PAUSE_SPECIFIC_CAMPAIGN_ON_REPLY,
        created_at = DateTime.now(),
        selected_calendar_data = None,
        team_uuid = TeamUuid("uuid")
      )
      val dummyAccount = Account(
        id = AccountUuid("account_uuid"),
        internal_id = accountId,
        email = account_email,
        email_verification_code = None,
        email_verification_code_created_at = None,
        created_at = DateTime.now(),

        first_name = Some(first_name),
        last_name = Some(last_name),
        company = None,
        timezone = None,

        profile = dummyAccountProfileInfo,

        org_role = Some(OrganizationRole.OWNER),

        teams = Seq(dummyTeamAccount),
        account_type = AccountType.INDIVIDUAL,

        org = dummyOrganizationWithCurrentData,

        active = true,

        email_notification_summary = "",

        account_metadata = dummyAccountMetadata,

        email_verified = true,
        signupType = None,
        account_access = AccountAccess(
          inbox_access = false
        ),
        calendar_account_data = None
      )


      val dummyCRMIntegrationInDB = CRMIntegrationInDB(
        team_id = team_id,
        owner_id = accountId,
        module_id = module_id,
        crm = integration_type,
        module = moduleType,
        user_mapping = None,
        field_mapping = None,
        activity_to_status_mapping = None,
        category_to_status_mapping = None,
        status_column_in_crm = None,
        create_record_if_not_exists = false,
        track_activities = false,
        create_or_update_record_in_crm = false,
        crm_filters_for_add_to_do_not_contact_in_sr = None,
        workflow_crm_setting_id = 12345,
        allow_going_back_in_crm_status = false,
        sentiment_to_status_mapping = None,
        update_reply_sentiment_for_all_associated_prospects = false,
        error = None, error_at = None, last_alert_for_error_sent_at = None

      )

      it("should return tokens and accountService.find success getRecentContacts success and updateInQueueForAddToDNCAndLastRan failure") {

        (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
        (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
          .expects(team_id, integration_type, *, *, *)
          .returning(Future.successful(
            Right(intg_acc_tok)
          ))

        (accountService.find(_: Long)(_: SRLogger))
          .expects(accountId, *)
          .returning(Success(dummyAccount))

        val tp_filter_id = "00B2w000002gLULEA2"
        val cap_logger = CaptureOne[SRLogger]()
        val last_sync_at = DateTime.now()

        (tIntegrationCRMService.getRecentContacts
        (_: HandleCRMContactDataArguments => Try[Boolean],
          _: IntegrationType,
          _: IntegrationModuleType,
          _: IntegrationTPAccessTokenResponse.FullTokenData,
          _: Long,
          _: Long,
          _: Option[DateTime],
          _: Option[DateTime],
          _: Seq[String],
          _: Long,
          _: Option[WSResponse],
          _: Seq[JsValue],
          _: Option[String],
          _: SrRollingUpdateCoreService
        )
        (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
          )
          .expects(
            *,
            integration_type,
            moduleType,
            intg_acc_tok,
            team_id,
            accountId,
            Some(last_sync_at),
            None,
            Seq(),
            0,
            *, *, *, *, *, *, *,*
          ).returning(Future.successful(Some(last_sync_at)))

        (triggerDAO.updateInQueueForAddToDNCAndLastRan)
          .expects(team_id, module_id, capture(cap_logger))
          .returning(Failure(new Exception("updateInQueueForAddToDNCAndLastRan error")))

        handleAddToDNCTriggerEventService._addToDoNotContactList(
          crm_type = integration_type,
          module_type = moduleType,
          module_id = module_id,
          teamId = team_id,
          accountId = accountId,
          tp_filter_id = Some(tp_filter_id),
          last_sync_at = Some(last_sync_at),
        ).map(dncRes => {
          assert(dncRes.isLeft)
        })
      }

      it("should return tokens and accountService.find success getRecentContacts success and updateInQueueForAddToDNCAndLastRan None") {

        (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
        (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
          .expects(team_id, integration_type, *, *, *)
          .returning(Future.successful(
            Right(intg_acc_tok)
          ))

        (accountService.find(_: Long)(_: SRLogger))
          .expects(accountId, *)
          .returning(Success(dummyAccount))

        val tp_filter_id = "00B2w000002gLULEA2"
        val cap_logger = CaptureOne[SRLogger]()
        val last_sync_at = DateTime.now()

        (tIntegrationCRMService.getRecentContacts
        (_: HandleCRMContactDataArguments => Try[Boolean],
          _: IntegrationType,
          _: IntegrationModuleType,
          _: IntegrationTPAccessTokenResponse.FullTokenData,
          _: Long,
          _: Long,
          _: Option[DateTime],
          _: Option[DateTime],
          _: Seq[String],
          _: Long,
          _: Option[WSResponse],
          _: Seq[JsValue],
          _: Option[String],
          _: SrRollingUpdateCoreService

        )
        (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
          )
          .expects(
            *,
            integration_type,
            moduleType,
            intg_acc_tok,
            team_id,
            accountId,
            Some(last_sync_at),
            None,
            Seq(),
            0,
            *, *, *, *, *, *, *,*
          ).returning(Future.successful(Some(last_sync_at)))

        (triggerDAO.updateInQueueForAddToDNCAndLastRan)
          .expects(team_id, module_id, capture(cap_logger))
          .returning(Success(None))

        handleAddToDNCTriggerEventService._addToDoNotContactList(
          crm_type = integration_type,
          module_type = moduleType,
          module_id = module_id,
          teamId = team_id,
          accountId = accountId,
          tp_filter_id = Some(tp_filter_id),
          last_sync_at = Some(last_sync_at),
        ).map(dncRes => {
          assert(dncRes.isLeft)
        })
      }

      it("should return tokens and accountService.find success getRecentContacts success and updateInQueueForAddToDNCAndLastRan and updateAddToDoNotContactLastSyncAt failure") {

        (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
        (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
          .expects(team_id, integration_type, *, *, *)
          .returning(Future.successful(
            Right(intg_acc_tok)
          ))

        (accountService.find(_: Long)(_: SRLogger))
          .expects(accountId, *)
          .returning(Success(dummyAccount))

        val tp_filter_id = "00B2w000002gLULEA2"
        val cap_logger = CaptureOne[SRLogger]()
        val last_sync_at = DateTime.now()

        (tIntegrationCRMService.getRecentContacts
        (_: HandleCRMContactDataArguments => Try[Boolean],
          _: IntegrationType,
          _: IntegrationModuleType,
          _: IntegrationTPAccessTokenResponse.FullTokenData,
          _: Long,
          _: Long,
          _: Option[DateTime],
          _: Option[DateTime],
          _: Seq[String],
          _: Long,
          _: Option[WSResponse],
          _: Seq[JsValue],
          _: Option[String],
          _: SrRollingUpdateCoreService

        )
        (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
          )
          .expects(
            *,
            integration_type,
            moduleType,
            intg_acc_tok,
            team_id,
            accountId,
            Some(last_sync_at),
            None,
            Seq(),
            0,
            *, *, *, *, *, *, *, *
          ).returning(Future.successful(Some(last_sync_at)))

        (triggerDAO.updateInQueueForAddToDNCAndLastRan)
          .expects(team_id, module_id, capture(cap_logger))
          .returning(Success(Some(1)))

        (triggerDAO.updateAddToDoNotContactLastSyncAt)
          .expects(team_id, module_id, tp_filter_id, Some(last_sync_at), capture(cap_logger))
          .returning(Failure(new Exception("updateAddToDoNotContactLastSyncAt error")))

        handleAddToDNCTriggerEventService._addToDoNotContactList(
          crm_type = integration_type,
          module_type = moduleType,
          module_id = module_id,
          teamId = team_id,
          accountId = accountId,
          tp_filter_id = Some(tp_filter_id),
          last_sync_at = Some(last_sync_at),
        ).map(dncRes => {
          assert(dncRes.isLeft)
        })
      }

      it("should return tokens and accountService.find success getRecentContacts success and updateInQueueForAddToDNCAndLastRan and updateAddToDoNotContactLastSyncAt None") {

        (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
        (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
          .expects(team_id, integration_type, *, *, *)
          .returning(Future.successful(
            Right(intg_acc_tok)
          ))

        (accountService.find(_: Long)(_: SRLogger))
          .expects(accountId, *)
          .returning(Success(dummyAccount))

        val tp_filter_id = "00B2w000002gLULEA2"
        val cap_logger = CaptureOne[SRLogger]()
        val last_sync_at = DateTime.now()

        (tIntegrationCRMService.getRecentContacts
        (_: HandleCRMContactDataArguments => Try[Boolean],
          _: IntegrationType,
          _: IntegrationModuleType,
          _: IntegrationTPAccessTokenResponse.FullTokenData,
          _: Long,
          _: Long,
          _: Option[DateTime],
          _: Option[DateTime],
          _: Seq[String],
          _: Long,
          _: Option[WSResponse],
          _: Seq[JsValue],
          _: Option[String],
          _: SrRollingUpdateCoreService

        )
        (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
          )
          .expects(
            *,
            integration_type,
            moduleType,
            intg_acc_tok,
            team_id,
            accountId,
            Some(last_sync_at),
            None,
            Seq(),
            0,
            *, *, *, *, *, *, *, *
          ).returning(Future.successful(Some(last_sync_at)))

        (triggerDAO.updateInQueueForAddToDNCAndLastRan)
          .expects(team_id, module_id, capture(cap_logger))
          .returning(Success(Some(1)))

        (triggerDAO.updateAddToDoNotContactLastSyncAt)
          .expects(team_id, module_id, tp_filter_id, Some(last_sync_at), capture(cap_logger))
          .returning(Success(None))

        handleAddToDNCTriggerEventService._addToDoNotContactList(
          crm_type = integration_type,
          module_type = moduleType,
          module_id = module_id,
          teamId = team_id,
          accountId = accountId,
          tp_filter_id = Some(tp_filter_id),
          last_sync_at = Some(last_sync_at),
        ).map(dncRes => {
          assert(dncRes.isLeft)
        })
      }

      it("should return tokens and accountService.find success getRecentContacts success and _addToDoNotContactList success") {

        (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
        (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
          .expects(team_id, integration_type, *, *, *)
          .returning(Future.successful(
            Right(intg_acc_tok)
          ))

        (accountService.find(_: Long)(_: SRLogger))
          .expects(accountId, *)
          .returning(Success(dummyAccount))

        val tp_filter_id = "00B2w000002gLULEA2"
        val cap_logger = CaptureOne[SRLogger]()
        val last_sync_at = DateTime.now()

        (tIntegrationCRMService.getRecentContacts
        (_: HandleCRMContactDataArguments => Try[Boolean],
          _: IntegrationType,
          _: IntegrationModuleType,
          _: IntegrationTPAccessTokenResponse.FullTokenData,
          _: Long,
          _: Long,
          _: Option[DateTime],
          _: Option[DateTime],
          _: Seq[String],
          _: Long,
          _: Option[WSResponse],
          _: Seq[JsValue],
          _: Option[String],
          _: SrRollingUpdateCoreService

        )
        (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
          )
          .expects(
            *,
            integration_type,
            moduleType,
            intg_acc_tok,
            team_id,
            accountId,
            Some(last_sync_at),
            None,
            Seq(),
            0,
            *, *, *, *, *, *, *, *
          ).returning(Future.successful(Some(last_sync_at)))

        (triggerDAO.updateInQueueForAddToDNCAndLastRan)
          .expects(team_id, module_id, capture(cap_logger))
          .returning(Success(Some(1)))

        (triggerDAO.updateAddToDoNotContactLastSyncAt)
          .expects(team_id, module_id, tp_filter_id, Some(last_sync_at), capture(cap_logger))
          .returning(Success(Some(1)))


        handleAddToDNCTriggerEventService._addToDoNotContactList(
          crm_type = integration_type,
          module_type = moduleType,
          module_id = module_id,
          teamId = team_id,
          accountId = accountId,
          tp_filter_id = Some(tp_filter_id),
          last_sync_at = Some(last_sync_at),
        ).map(dncRes => {
          assert(dncRes === Right(1))
        })
      }
    }

  }

}

