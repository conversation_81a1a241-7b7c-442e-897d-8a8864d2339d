package app.utils.mq.webhook

import org.apache.pekko.actor.ActorSystem
import api.accounts.{AccountService, AccountUuid, TeamId}
import api.emails.EmailScheduledDAO
import api.emails.dao_service.EmailScheduledDAOService
import api.integrations.services.TIntegrationCRMService
import api.integrations.{BatchContactResponse, CommonCRMAPIErrors, CreateOrUpdateBatchContactsError, FetchTokensFromDBAndRefreshAccessTokenError, IntegrationTPAccessTokenResponse, RefreshAccessTokenError, TIntegrationCRMTrait}
import api.prospects.ProspectUuid
import api.prospects.dao_service.ProspectDAOService
import api.prospects.models.ProspectId
import api.sr_audit_logs.models.EventDataType.PushEventDataType
import api.sr_audit_logs.models.{EventType, ProspectIdWithOldProspectDeduplicationColumn, ProspectObjectWithOldProspectDeduplicationColumn, WorkflowAttemptTryErrorReason}
import api.triggers.{CRMIntegrationInDB, CRMStatusColumnNameAndOptions, CategoryToStatusMapping, FindUpdatedUserMappingError, IntegrationModuleType, IntegrationType, SRTriggerActionType, SRTriggerAllowedCombos, Trigger, TriggerAction, TriggerFields, TriggerInDB, TriggerUsers, UpdateFieldsMappingForm, UpdateUserMappingForm}
import app.test_fixtures.prospect.ProspectFixtures
import eventframework.{ProspectObject, ProspectObjectInternal}
import org.joda.time.DateTime
import org.scalamock.scalatest.AsyncMockFactory
import org.scalatest.BeforeAndAfter
import org.scalatest.funspec.AsyncFunSpec
import play.api.libs.json.Json
import play.api.libs.ws.WSClient
import play.api.libs.ws.ahc.AhcWSClient
import utils.SRLogger
import utils.email_notification.service.EmailNotificationService
import utils.mq.webhook.model.CreateInCRMJedisDAO
import utils.mq.webhook.{HandlePushTriggerEventService, LeadStatusService}
import utils.testapp.TestAppExecutionContext
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}

class HandlePushTriggerEventServiceSpec
  extends AsyncFunSpec
    with AsyncMockFactory
    with BeforeAndAfter  {

  implicit lazy val system: ActorSystem = TestAppExecutionContext.actorSystem
  implicit lazy val wSClient: AhcWSClient = TestAppExecutionContext.wsClient
  implicit lazy val actorContext: ExecutionContext = system.dispatcher

  given Logger: SRLogger = new SRLogger("Unit test")


  describe("MQTrigger processEventMessage") {

    val triggerDAO = mock[Trigger]
    val emailScheduledDAO = mock[EmailScheduledDAO]
    val emailScheduledDAOService = mock[EmailScheduledDAOService]
    val prospectDAOService = mock[ProspectDAOService]
    val accountService = mock[AccountService]
    val emailNotificationService = mock[EmailNotificationService]
    val srTriggerAllowedCombos = mock[SRTriggerAllowedCombos]
    val tIntegrationCRMService = mock[TIntegrationCRMService]
    val srRollingUpdateCoreService = mock[SrRollingUpdateCoreService]
    val createInCRMJedisDAO = mock[CreateInCRMJedisDAO]

    val leadStatusService = new LeadStatusService(
      triggerDAO = triggerDAO,
      emailScheduledDAO = emailScheduledDAO,
      prospectDAOService = prospectDAOService,
      accountService = accountService,
      tIntegrationCRMService = tIntegrationCRMService,
      srRollingUpdateCoreService = srRollingUpdateCoreService,
      createInCRMJedisDAO = createInCRMJedisDAO
    )

    val handlePushTriggerEventService = new HandlePushTriggerEventService(
      accountService = accountService,
      emailScheduledDAOService = emailScheduledDAOService,
      prospectDAOService = prospectDAOService,
      triggerDAO = triggerDAO,
      emailNotificationService = emailNotificationService,
      leadStatusService = leadStatusService,
      srTriggerAllowedCombos = srTriggerAllowedCombos,
      tIntegrationCRMService = tIntegrationCRMService,
    )

    val empty_email = ""
    val integration_type = IntegrationType.SALESFORCE
    val moduleType = IntegrationModuleType.LEADS

    trait IntegrationsCRMTestTrait extends TIntegrationCRMTrait {
      val name = IntegrationType.SALESFORCE
    }
    val crmIntegrationService = mock[IntegrationsCRMTestTrait]

    val access_token = "abcd-1234"
    val refresh_token = Some("wxyz-********")
    val expires_in = Some(********);
    val expires_at = Some(DateTime.now())
    val token_type = Some("acc-tok")
    val api_domain = Some("api.domain.com")
    val intg_acc_tok = IntegrationTPAccessTokenResponse.FullTokenData(
      access_token = access_token,
      refresh_token = refresh_token,
      expires_in = expires_in,
      expires_at = expires_at,
      token_type = token_type,
      api_domain = api_domain,
      is_sandbox = Some(false)
    )

    val team_id: Long = 2
    val accountId: Long = 3
    val prospect_id = 4L
    val event = EventType.CREATED_OR_UPDATED_PROSPECT_IN_SMARTREACH

    val triggerId = 1
    val workflow_crm_setting_id = 7777

    val dummyFields = TriggerFields(
      sr_field = "first_name",
      tp_field = "firstName",
      tp_field_default_value = None,
      field_label = "First name",
      tp_field_label = Some("First name"),
      field_type = "text"
    )
    val dummyAction  = TriggerAction(
      action_type = SRTriggerActionType.CREATE_OR_UPDATE_PROSPECT_IN_SMARTREACH,
      action_app_type = integration_type,
      fields = Some(Seq(dummyFields)),
      campaign_id = None,
      ignore_prospects_active_in_other_campaigns = None,
      ignore_prospects_in_other_campaigns = None,
      tags = None,
      create_contact_or_lead_if_not_exists = Some("yes"),
      last_sync_at = None,
      prospect_category_id_custom = None
    )
    val dummyTriggerInDB = TriggerInDB(
      id = triggerId,
      owner_id = accountId,
      owner_name = "owner name",
      team_id = team_id,
      campaign_id = None,
      label = "test trigger",
      event = Some(event),
      event_app_type = Some(IntegrationType.SMARTREACH),
      tp_filter_id = None,
      conditions = None, // Option[Seq[TriggerCondition]],
      actions = Some(Seq(dummyAction)),
      created_at = DateTime.now(),
      active = true,
      shared_with_team = true,
      error = None,
      error_at = None,
      error_retries_count = 0,
      last_ran_at = None,
      in_queue_for_sync = false,
      pushed_to_queue_for_sync_at = None
    )


    describe("createContactDataInCRM") {

      val prospect_email = "<EMAIL>"
      // ==== Dummy Prospect
      val dummyProspectObj = ProspectObject(
        id = 1,

        owner_id = 0,

        team_id = team_id,

        first_name = Some(""),
        last_name = None,

        email = Some(prospect_email),

        last_contacted_at = None,
        last_contacted_at_phone = None,

        created_at = DateTime.now,

        custom_fields = Json.obj(),

        list = None,

        company = None,
        job_title = None,
        phone = None,
        phone_2 = None,
        phone_3 = None,

        linkedin_url = None,

        city = None,
        state = None,
        country = None,
        timezone = None,

        prospect_category = "",
        latest_reply_sentiment_uuid = None,

        internal = {

          ProspectFixtures.prospectObjectInternal
        },
        current_step_type = None,
        latest_task_done_at = None,
        prospect_uuid = Some(ProspectUuid("prs_aa_abcdefghi")),
        owner_uuid = AccountUuid("acc_aa_abcdegfhi"),
        updated_at = DateTime.now()
      )
      val triggerFields = TriggerFields(
        sr_field = "first_name",
        tp_field = "firstName",
        tp_field_default_value = None,
        field_label = "First Name",
        tp_field_label = Some("First Name"),
        field_type = "text"
      )
      val updateFieldsMappingForm = UpdateFieldsMappingForm(
        fields = Some(Seq(triggerFields))
      )

      val triggerUsers = TriggerUsers(
        sr_user_id = 24,
        sr_user_email = "<EMAIL>",
        tp_user_id = "154DFR87FHWJ",
        tp_user_email = "<EMAIL>"
      )
      val updatedUserMappingMappingForm = UpdateUserMappingForm(
        users = Some(Seq(triggerUsers))
      )


      /*TODO: Planing to test processMessage after all the triggers migrated to global setting*/
      //
      //      val dummyCRMIntegrationInDB = CRMIntegrationInDB(
      //        crm = integration_type,
      //        module = moduleType,
      //        user_mapping = None,
      //        field_mapping = None,
      //        status_mapping = None,
      //        status_column_in_crm = None,
      //        create_record_if_not_exists = true,
      //        track_activities = true,
      //        create_or_update_record_in_crm = true
      //      )
      //      var dummyMessage = MQTriggerMsg(
      //        accountId = accountId,
      //        teamId = team_id,
      //        prospectIds = Seq(prospect_id),
      //        updatedProspectCategoryId = None,
      //        event = event.toString
      //      )
      //
      //
      //      val cap_logger = CaptureOne[SRLogger]()
      //      it("") {
      //
      //        (triggerDAO.findIntegrations)
      //          .expects(team_id, capture(cap_logger))
      //          .returning(Failure(new Exception("FATAL findIntegrations error")))
      //
      //        handlePushTriggerEventService.processEventMessage(
      //          message = dummyMessage
      //        ).map(res => {
      //          assert(res.head === 0)
      //        })
      //
      //      }
      //
      //      it("2") {
      //
      //        (triggerDAO.findIntegrations)
      //          .expects(team_id, capture(cap_logger))
      //          .returning(Success(Seq()))
      //
      //        handlePushTriggerEventService.processEventMessage(
      //          message = dummyMessage
      //        ).map(res => {
      //          assert(res.head === 0)
      //        })
      //
      //      }
      //
      //      it("3") {
      //
      //        (triggerDAO.findIntegrations)
      //          .expects(team_id, capture(cap_logger))
      //          .returning(Success(Seq(dummyCRMIntegrationInDB)))
      //
      //        val integration_type_capt = CaptureOne[IntegrationType]()
      //        (crmIntegrationService.fetchTokensFromDBAndRefreshAccessToken
      //        (_: Long, _: IntegrationType, _: String)(_: SRLogger, _: ExecutionContext, _: WSClient))
      //          .expects(team_id, capture(integration_type_capt), empty_email, *, *, *)
      //          .returning(Future.successful(intg_acc_tok))
      //
      ////        (handlePushTriggerEventService.__createContactDataInCRM
      ////          (_: IntegrationsCRMTestTrait, _: IntegrationModuleType, _: Seq[Long], _: Long, _: Long)(_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger))
      ////          .expects(crmIntegrationService, moduleType, Seq(prospect_id), team_id, accountId, *,*,*,*)
      ////          .returning(Future.successful(1))
      //
      //        handlePushTriggerEventService.processEventMessage(
      //          message = dummyMessage
      //        ).map(res => {
      //          assert(res.isEmpty)
      //        })
      //
      //      }


      it("should return exception while fetching tokens CommonCRMAPIError") {
        (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
        (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
          .expects(team_id, integration_type, *, *, *)
          .returning(
            Future.successful(
              Left(FetchTokensFromDBAndRefreshAccessTokenError.RefreshAccessTokenAPIError(err = RefreshAccessTokenError.MalformedRefreshAccessTokenResponseError(msg = "Malformed RefreshAccess Token Response"))
              ))
          )

        handlePushTriggerEventService.__createContactDataInCRM(
          integration_type = integration_type,
          integrationModuleType = moduleType,
          prospectIds = Seq(ProspectIdWithOldProspectDeduplicationColumn(
            prospectId = ProspectId(prospect_id),
            oldProspectDeduplicationColumn = None
          )),
          teamId = team_id,
          accountId = accountId,
          workflow_crm_setting_id = Some(workflow_crm_setting_id),
        ).map(res => {
          assert(res.isLeft)
        })
      }

      it("should return exception while fetching token SQLException") {
        (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
        (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
          .expects(team_id, integration_type, *, *, *)
          .returning(
            Future.successful(
              Left(FetchTokensFromDBAndRefreshAccessTokenError.SQLException(error = new Exception("Sql exception"))
              ))
          )

        handlePushTriggerEventService.__createContactDataInCRM(
          integration_type = integration_type,
          integrationModuleType = moduleType,
          prospectIds = Seq(ProspectIdWithOldProspectDeduplicationColumn(
            prospectId = ProspectId(prospect_id),
            oldProspectDeduplicationColumn = None
          )),
          teamId = team_id,
          accountId = accountId,
          workflow_crm_setting_id = Some(workflow_crm_setting_id),
        ).map(res => {
          assert(res.isLeft)
        })
      }


      it("should return exception while fetching tokens TokensNotFoundInDBError") {
        (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
        (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
          .expects(team_id, integration_type, *, *, *)
          .returning(
            Future.successful(
              Left(FetchTokensFromDBAndRefreshAccessTokenError.TokensNotFoundInDBError(msg = "TokensNotFoundInDBError")
              ))
          )

        handlePushTriggerEventService.__createContactDataInCRM(
          integration_type = integration_type,
          integrationModuleType = moduleType,
          prospectIds = Seq(ProspectIdWithOldProspectDeduplicationColumn(
            prospectId = ProspectId(prospect_id),
            oldProspectDeduplicationColumn = None
          )),
          teamId = team_id,
          accountId = accountId,
          workflow_crm_setting_id = Some(workflow_crm_setting_id),
        ).map(res => {
          assert(res.isLeft)
        })
      }


      it("should return exception while fetching tokens InvalidRefreshTokenError") {
        (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
        (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
          .expects(team_id, integration_type, *, *, *)
          .returning(
            Future.successful(
              Left(FetchTokensFromDBAndRefreshAccessTokenError.InvalidRefreshTokenError(msg = "InvalidRefreshTokenError")
              ))
          )

        handlePushTriggerEventService.__createContactDataInCRM(
          integration_type = integration_type,
          integrationModuleType = moduleType,
          prospectIds = Seq(ProspectIdWithOldProspectDeduplicationColumn(
            prospectId = ProspectId(prospect_id),
            oldProspectDeduplicationColumn = None
          )),
          teamId = team_id,
          accountId = accountId,
          workflow_crm_setting_id = Some(workflow_crm_setting_id),
        ).map(res => {
          assert(res.isLeft)
        })
      }


      it("should return prospectIds empty") {

        handlePushTriggerEventService.__createContactDataInCRM(
          integration_type = integration_type,
          integrationModuleType = moduleType,
          prospectIds = Seq(),
          teamId = team_id,
          accountId = accountId,
          workflow_crm_setting_id = Some(workflow_crm_setting_id),
        ).map(crUpRes => {
          assert(crUpRes.isLeft)
        })
      }

      it("should return tokens and exception while findFieldMapping") {

        (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
        (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
          .expects(team_id, integration_type, *, *, *)
          .returning(Future.successful(
            Right(intg_acc_tok)
          ))

        (triggerDAO.findFieldMapping)
          .expects(team_id, integration_type, moduleType)
          .returning(Failure(new Exception("findFieldMapping error")))

        handlePushTriggerEventService.__createContactDataInCRM(
          integration_type = integration_type,
          integrationModuleType = moduleType,
          prospectIds = Seq(ProspectIdWithOldProspectDeduplicationColumn(
            prospectId = ProspectId(prospect_id),
            oldProspectDeduplicationColumn = None
          )),
          teamId = team_id,
          accountId = accountId,
          workflow_crm_setting_id = Some(workflow_crm_setting_id),
        ).map(crUpRes => {
          assert(crUpRes.isLeft)
        })
      }


      it("should return tokens and None while findFieldMapping") {

        (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
        (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
          .expects(team_id, integration_type, *, *, *)
          .returning(Future.successful(
            Right(intg_acc_tok)
          ))

        (triggerDAO.findFieldMapping)
          .expects(team_id, integration_type, moduleType)
          .returning(Success(None))

        handlePushTriggerEventService.__createContactDataInCRM(
          integration_type = integration_type,
          integrationModuleType = moduleType,
          prospectIds = Seq(ProspectIdWithOldProspectDeduplicationColumn(
            prospectId = ProspectId(prospect_id),
            oldProspectDeduplicationColumn = None
          )),
          teamId = team_id,
          accountId = accountId,
          workflow_crm_setting_id = Some(workflow_crm_setting_id),
        ).map(crUpRes => {
          assert(crUpRes.isLeft)
        })
      }


      it("should return tokens and findFieldMapping success and findUpdatedUserMapping success and find prospect scs and createOrUpdateBatchContacts Error Internal server") {

        (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
        (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
          .expects(team_id, integration_type, *, *, *)
          .returning(Future.successful(
            Right(intg_acc_tok)
          ))

        (triggerDAO.findFieldMapping)
          .expects(team_id, integration_type, moduleType)
          .returning(Success(Some(updateFieldsMappingForm)))

        (tIntegrationCRMService.findUpdatedUserMapping
        (_: IntegrationTPAccessTokenResponse.FullTokenData, _: Long, _: Long, _: IntegrationType)(_: WSClient, _: ExecutionContext, _: SRLogger))
          .expects(intg_acc_tok, team_id, accountId, integration_type, *, *, *)
          .returning(Future.successful(Right(updatedUserMappingMappingForm)))

        (prospectDAOService.find)
          .expects(Seq(prospect_id), *, *, *, team_id, *, *, *, *, *, None, *)
          .returning(Success(Seq(dummyProspectObj)))

        (tIntegrationCRMService.createOrUpdateBatchContacts
        (_: IntegrationType, _: IntegrationModuleType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: Seq[ProspectObjectWithOldProspectDeduplicationColumn], _: UpdateFieldsMappingForm, _: UpdateUserMappingForm, _: Long, _: Long)
        (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
          )
          .expects(crmIntegrationService.name,
            moduleType, intg_acc_tok, *, updateFieldsMappingForm, updatedUserMappingMappingForm, accountId, team_id,
            *, *, *, *
          ).returning(Future.successful(
          Left(CreateOrUpdateBatchContactsError.CommonCRMAPIError(err = CommonCRMAPIErrors.InternalServerError("Internal server error")))
        ))

        handlePushTriggerEventService.__createContactDataInCRM(
          integration_type = integration_type,
          integrationModuleType = moduleType,
          prospectIds = Seq(ProspectIdWithOldProspectDeduplicationColumn(
            prospectId = ProspectId(prospect_id),
            oldProspectDeduplicationColumn = None
          )),
          teamId = team_id,
          accountId = accountId,
          workflow_crm_setting_id = Some(workflow_crm_setting_id),
        ).map(crUpRes => {
          assert(crUpRes.isLeft)
        })
      }


      it("should return tokens and findFieldMapping success and findUpdatedUserMapping success and find prospect scs and createOrUpdateBatchContacts Error UnAuthorizedError") {

        (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
        (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
          .expects(team_id, integration_type, *, *, *)
          .returning(Future.successful(
            Right(intg_acc_tok)
          ))

        (triggerDAO.findFieldMapping)
          .expects(team_id, integration_type, moduleType)
          .returning(Success(Some(updateFieldsMappingForm)))

        (tIntegrationCRMService.findUpdatedUserMapping
        (_: IntegrationTPAccessTokenResponse.FullTokenData, _: Long, _: Long, _: IntegrationType)(_: WSClient, _: ExecutionContext, _: SRLogger))
          .expects(intg_acc_tok, team_id, accountId, integration_type, *, *, *)
          .returning(Future.successful(Right(updatedUserMappingMappingForm)))

        (prospectDAOService.find)
          .expects(Seq(prospect_id), *, *, *, team_id, *, *, *, *, *, None, *)
          .returning(Success(Seq(dummyProspectObj)))

        (tIntegrationCRMService.createOrUpdateBatchContacts
        (_: IntegrationType, _: IntegrationModuleType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: Seq[ProspectObjectWithOldProspectDeduplicationColumn], _: UpdateFieldsMappingForm, _: UpdateUserMappingForm, _: Long, _: Long)
        (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
          )
          .expects(crmIntegrationService.name,
            moduleType, intg_acc_tok, *, updateFieldsMappingForm, updatedUserMappingMappingForm, accountId, team_id,
            *, *, *, *
          ).returning(Future.successful(
          Left(CreateOrUpdateBatchContactsError.CommonCRMAPIError(err = CommonCRMAPIErrors.UnAuthorizedError("UnAuthorizedError")))
        ))

        handlePushTriggerEventService.__createContactDataInCRM(
          integration_type = integration_type,
          integrationModuleType = moduleType,
          prospectIds = Seq(ProspectIdWithOldProspectDeduplicationColumn(
            prospectId = ProspectId(prospect_id),
            oldProspectDeduplicationColumn = None
          )),
          teamId = team_id,
          accountId = accountId,
          workflow_crm_setting_id = Some(workflow_crm_setting_id),
        ).map(crUpRes => {
          assert(crUpRes.isLeft)
        })
      }

      it("should return tokens and findFieldMapping success and findUpdatedUserMapping success and find prospect scs and createOrUpdateBatchContacts Error TooManyRequestsError") {

        (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
        (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
          .expects(team_id, integration_type, *, *, *)
          .returning(Future.successful(
            Right(intg_acc_tok)
          ))

        (triggerDAO.findFieldMapping)
          .expects(team_id, integration_type, moduleType)
          .returning(Success(Some(updateFieldsMappingForm)))

        (tIntegrationCRMService.findUpdatedUserMapping
        (_: IntegrationTPAccessTokenResponse.FullTokenData, _: Long, _: Long, _: IntegrationType)(_: WSClient, _: ExecutionContext, _: SRLogger))
          .expects(intg_acc_tok, team_id, accountId, integration_type, *, *, *)
          .returning(Future.successful(Right(updatedUserMappingMappingForm)))

        (prospectDAOService.find)
          .expects(Seq(prospect_id), *, *, *, team_id, *, *, *, *, *, None, *)
          .returning(Success(Seq(dummyProspectObj)))

        (tIntegrationCRMService.createOrUpdateBatchContacts
        (_: IntegrationType, _: IntegrationModuleType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: Seq[ProspectObjectWithOldProspectDeduplicationColumn], _: UpdateFieldsMappingForm, _: UpdateUserMappingForm, _: Long, _: Long)
        (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
          )
          .expects(crmIntegrationService.name,
            moduleType, intg_acc_tok,*, updateFieldsMappingForm, updatedUserMappingMappingForm, accountId, team_id,
            *, *, *, *
          ).returning(Future.successful(
          Left(CreateOrUpdateBatchContactsError.CommonCRMAPIError(err = CommonCRMAPIErrors.TooManyRequestsError("TooManyRequestsError")))
        ))

        handlePushTriggerEventService.__createContactDataInCRM(
          integration_type = integration_type,
          integrationModuleType = moduleType,
          prospectIds = Seq(ProspectIdWithOldProspectDeduplicationColumn(
            prospectId = ProspectId(prospect_id),
            oldProspectDeduplicationColumn = None
          )),
          teamId = team_id,
          accountId = accountId,
          workflow_crm_setting_id = Some(workflow_crm_setting_id),
        ).map(crUpRes => {
          assert(crUpRes.isLeft)
        })
      }


      it("should return tokens and findFieldMapping success and findUpdatedUserMapping success and find prospect scs and createOrUpdateBatchContacts Error Resource NotFoundError") {

        (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
        (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
          .expects(team_id, integration_type, *, *, *)
          .returning(Future.successful(
            Right(intg_acc_tok)
          ))

        (triggerDAO.findFieldMapping)
          .expects(team_id, integration_type, moduleType)
          .returning(Success(Some(updateFieldsMappingForm)))

        (tIntegrationCRMService.findUpdatedUserMapping
        (_: IntegrationTPAccessTokenResponse.FullTokenData, _: Long, _: Long, _: IntegrationType)(_: WSClient, _: ExecutionContext, _: SRLogger))
          .expects(intg_acc_tok, team_id, accountId, integration_type, *, *, *)
          .returning(Future.successful(Right(updatedUserMappingMappingForm)))

        (prospectDAOService.find)
          .expects(Seq(prospect_id), *, *, *, team_id, *, *, *, *, *, None, *)
          .returning(Success(Seq(dummyProspectObj)))

        (tIntegrationCRMService.createOrUpdateBatchContacts
        (_: IntegrationType, _: IntegrationModuleType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: Seq[ProspectObjectWithOldProspectDeduplicationColumn], _: UpdateFieldsMappingForm, _: UpdateUserMappingForm, _: Long, _: Long)
        (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
          )
          .expects(crmIntegrationService.name,
            moduleType, intg_acc_tok, *, updateFieldsMappingForm, updatedUserMappingMappingForm, accountId, team_id,
            *, *, *, *
          ).returning(Future.successful(
          Left(CreateOrUpdateBatchContactsError.CommonCRMAPIError(err = CommonCRMAPIErrors.NotFoundError("Resource NotFoundError")))
        ))

        handlePushTriggerEventService.__createContactDataInCRM(
          integration_type = integration_type,
          integrationModuleType = moduleType,
          prospectIds = Seq(ProspectIdWithOldProspectDeduplicationColumn(
            prospectId = ProspectId(prospect_id),
            oldProspectDeduplicationColumn = None
          )),
          teamId = team_id,
          accountId = accountId,
          workflow_crm_setting_id = Some(workflow_crm_setting_id),
        ).map(crUpRes => {
          assert(crUpRes.isLeft)
        })
      }

      it("should return tokens and findFieldMapping success and findUpdatedUserMapping success and find prospect scs and createOrUpdateBatchContacts Error UnknownError") {

        (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
        (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
          .expects(team_id, integration_type, *, *, *)
          .returning(Future.successful(
            Right(intg_acc_tok)
          ))

        (triggerDAO.findFieldMapping)
          .expects(team_id, integration_type, moduleType)
          .returning(Success(Some(updateFieldsMappingForm)))

        (tIntegrationCRMService.findUpdatedUserMapping
        (_: IntegrationTPAccessTokenResponse.FullTokenData, _: Long, _: Long, _: IntegrationType)(_: WSClient, _: ExecutionContext, _: SRLogger))
          .expects(intg_acc_tok, team_id, accountId, integration_type, *, *, *)
          .returning(Future.successful(Right(updatedUserMappingMappingForm)))

        (prospectDAOService.find)
          .expects(Seq(prospect_id), *, *, *, team_id, *, *, *, *, *, None, *)
          .returning(Success(Seq(dummyProspectObj)))

        (tIntegrationCRMService.createOrUpdateBatchContacts
        (_: IntegrationType, _: IntegrationModuleType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: Seq[ProspectObjectWithOldProspectDeduplicationColumn], _: UpdateFieldsMappingForm, _: UpdateUserMappingForm, _: Long, _: Long)
        (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
          )
          .expects(crmIntegrationService.name,
            moduleType, intg_acc_tok, *, updateFieldsMappingForm, updatedUserMappingMappingForm, accountId, team_id,
            *, *, *, *
          ).returning(Future.successful(
          Left(CreateOrUpdateBatchContactsError.CommonCRMAPIError(err = CommonCRMAPIErrors.UnknownError("UnknownError")))
        ))

        handlePushTriggerEventService.__createContactDataInCRM(
          integration_type = integration_type,
          integrationModuleType = moduleType,
          prospectIds = Seq(ProspectIdWithOldProspectDeduplicationColumn(
            prospectId = ProspectId(prospect_id),
            oldProspectDeduplicationColumn = None
          )),
          teamId = team_id,
          accountId = accountId,
          workflow_crm_setting_id = Some(workflow_crm_setting_id),
        ).map(crUpRes => {
          assert(crUpRes.isLeft)
        })
      }


      it("should return tokens and findFieldMapping success and findUpdatedUserMapping success and find prospect scs and createOrUpdateBatchContacts Error UnknownErrorWithResponseBody") {

        (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
        (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
          .expects(team_id, integration_type, *, *, *)
          .returning(Future.successful(
            Right(intg_acc_tok)
          ))

        (triggerDAO.findFieldMapping)
          .expects(team_id, integration_type, moduleType)
          .returning(Success(Some(updateFieldsMappingForm)))

        (tIntegrationCRMService.findUpdatedUserMapping
        (_: IntegrationTPAccessTokenResponse.FullTokenData, _: Long, _: Long, _: IntegrationType)(_: WSClient, _: ExecutionContext, _: SRLogger))
          .expects(intg_acc_tok, team_id, accountId, integration_type, *, *, *)
          .returning(Future.successful(Right(updatedUserMappingMappingForm)))

        (prospectDAOService.find)
          .expects(Seq(prospect_id), *, *, *, team_id, *, *, *, *, *, None, *)
          .returning(Success(Seq(dummyProspectObj)))

        (tIntegrationCRMService.createOrUpdateBatchContacts
        (_: IntegrationType, _: IntegrationModuleType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: Seq[ProspectObjectWithOldProspectDeduplicationColumn], _: UpdateFieldsMappingForm, _: UpdateUserMappingForm, _: Long, _: Long)
        (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
          )
          .expects(crmIntegrationService.name,
            moduleType, intg_acc_tok, *, updateFieldsMappingForm, updatedUserMappingMappingForm, accountId, team_id,
            *, *, *, *
          ).returning(Future.successful(
          Left(CreateOrUpdateBatchContactsError.CommonCRMAPIError(err = CommonCRMAPIErrors.UnknownErrorWithResponseBody("UnknownErrorWithResponseBody", "Res", 504)))
        ))

        handlePushTriggerEventService.__createContactDataInCRM(
          integration_type = integration_type,
          integrationModuleType = moduleType,
          prospectIds = Seq(ProspectIdWithOldProspectDeduplicationColumn(
            prospectId = ProspectId(prospect_id),
            oldProspectDeduplicationColumn = None
          )),
          teamId = team_id,
          accountId = accountId,
          workflow_crm_setting_id = Some(workflow_crm_setting_id),
        ).map(crUpRes => {
          assert(crUpRes.isLeft)
        })
      }


      it("should return tokens and findFieldMapping success and findUpdatedUserMapping success and find prospect scs and createOrUpdateBatchContacts Error InvalidModule") {

        (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
        (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
          .expects(team_id, integration_type, *, *, *)
          .returning(Future.successful(
            Right(intg_acc_tok)
          ))

        (triggerDAO.findFieldMapping)
          .expects(team_id, integration_type, moduleType)
          .returning(Success(Some(updateFieldsMappingForm)))

        (tIntegrationCRMService.findUpdatedUserMapping
        (_: IntegrationTPAccessTokenResponse.FullTokenData, _: Long, _: Long, _: IntegrationType)(_: WSClient, _: ExecutionContext, _: SRLogger))
          .expects(intg_acc_tok, team_id, accountId, integration_type, *, *, *)
          .returning(Future.successful(Right(updatedUserMappingMappingForm)))

        (prospectDAOService.find)
          .expects(Seq(prospect_id), *, *, *, team_id, *, *, *, *, *, None, *)
          .returning(Success(Seq(dummyProspectObj)))

        (tIntegrationCRMService.createOrUpdateBatchContacts
        (_: IntegrationType, _: IntegrationModuleType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: Seq[ProspectObjectWithOldProspectDeduplicationColumn], _: UpdateFieldsMappingForm, _: UpdateUserMappingForm, _: Long, _: Long)
        (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
          )
          .expects(crmIntegrationService.name,
            moduleType, intg_acc_tok, *, updateFieldsMappingForm, updatedUserMappingMappingForm, accountId, team_id,
            *, *, *, *
          ).returning(Future.successful(
          Left(CreateOrUpdateBatchContactsError.InvalidModuleError("InvalidModule"))
        ))

        handlePushTriggerEventService.__createContactDataInCRM(
          integration_type = integration_type,
          integrationModuleType = moduleType,
          prospectIds = Seq(ProspectIdWithOldProspectDeduplicationColumn(
            prospectId = ProspectId(prospect_id),
            oldProspectDeduplicationColumn = None
          )),
          teamId = team_id,
          accountId = accountId,
          workflow_crm_setting_id = Some(workflow_crm_setting_id),
        ).map(crUpRes => {
          assert(crUpRes.isLeft)
        })
      }




      it("should return tokens and findFieldMapping success and findUpdatedUserMapping success and find prospect scs and createOrUpdateBatchContacts success") {

        (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
        (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
          .expects(team_id, integration_type, *, *, *)
          .returning(Future.successful(
            Right(intg_acc_tok)
          ))

        (triggerDAO.findFieldMapping)
          .expects(team_id, integration_type, moduleType)
          .returning(Success(Some(updateFieldsMappingForm)))

        (tIntegrationCRMService.findUpdatedUserMapping
        (_: IntegrationTPAccessTokenResponse.FullTokenData, _: Long, _: Long, _: IntegrationType)(_: WSClient, _: ExecutionContext, _: SRLogger))
          .expects(intg_acc_tok, team_id, accountId, integration_type, *, *, *)
          .returning(Future.successful(Right(updatedUserMappingMappingForm)))

        (prospectDAOService.find)
          .expects(Seq(prospect_id), *, *, *, team_id, *, *, *, *, *, None, *)
          .returning(Success(Seq(dummyProspectObj)))

        (tIntegrationCRMService.createOrUpdateBatchContacts
        (_: IntegrationType, _: IntegrationModuleType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: Seq[ProspectObjectWithOldProspectDeduplicationColumn], _: UpdateFieldsMappingForm, _: UpdateUserMappingForm, _: Long, _: Long)
        (_: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger)
          )
          .expects(crmIntegrationService.name,
            moduleType, intg_acc_tok, *, updateFieldsMappingForm, updatedUserMappingMappingForm, accountId, team_id,
            *, *, *, *
          ).returning(Future.successful(
          Right(Seq(
            BatchContactResponse(
              email = Some("<EMAIL>"),
              error = None,
              phone = None,
              linkedinUrl = None
            )
          ))
        ))

        handlePushTriggerEventService.__createContactDataInCRM(
          integration_type = integration_type,
          integrationModuleType = moduleType,
          prospectIds = Seq(ProspectIdWithOldProspectDeduplicationColumn(
            prospectId = ProspectId(prospect_id),
            oldProspectDeduplicationColumn = None
          )),
          teamId = team_id,
          accountId = accountId,
          workflow_crm_setting_id = Some(workflow_crm_setting_id),
        ).map(crUpRes => {
          assert(crUpRes === Right(1))
        })
      }

    }


    describe("findIntegrationsForInvokeNewFlowV2") {
      val leadStatusService =  mock[LeadStatusService]
      val handlePushTriggerEventService = new HandlePushTriggerEventService(
        accountService = accountService,
        emailScheduledDAOService = emailScheduledDAOService,
        prospectDAOService = prospectDAOService,
        triggerDAO = triggerDAO,
        emailNotificationService = emailNotificationService,
        leadStatusService = leadStatusService,
        srTriggerAllowedCombos = srTriggerAllowedCombos,
        tIntegrationCRMService = tIntegrationCRMService
      )
      val messageUpdate = PushEventDataType.UpdatedProspectsEventData(
        ownerAccountId = accountId,
        teamId = team_id,
        updated_id = 2,
        triggerPath = None,
        oldProspectDeduplicationColumn = None
      )
      val messageCategory = PushEventDataType.UpdateProspectCategoryEventData(
        doerAccountId = accountId,
        teamId = team_id,
        prospectId = 2,
        newProspectCategoryIdCustom = 12,
        triggerPath = None
      )
      val crMIntegrationInDB = CRMIntegrationInDB(
        workflow_crm_setting_id = workflow_crm_setting_id,
        team_id = team_id,
        owner_id = accountId,
        module_id = 1,
        crm = IntegrationType.HUBSPOT,
        module = moduleType,
        user_mapping = None,
        field_mapping = None,
        activity_to_status_mapping = None,
        category_to_status_mapping = None,
        status_column_in_crm = None,
        create_record_if_not_exists = true,
        track_activities = false,
        create_or_update_record_in_crm = false,
        crm_filters_for_add_to_do_not_contact_in_sr = None,
        allow_going_back_in_crm_status = false,
        sentiment_to_status_mapping = None,
        update_reply_sentiment_for_all_associated_prospects = false,
        error = None, error_at = None, last_alert_for_error_sent_at = None
      )

      describe("Should run the triggers when create_or_update_record_in_crm is false") {
        it("should send Left when triggerDAO.findAllforMQProcess fails") {

          (triggerDAO.findAllforMQProcess)
            .expects(accountId, team_id, EventType.UPDATED_PROSPECT_IN_SMARTREACH, Some(IntegrationType.HUBSPOT))
            .returning(Failure(new Throwable("SQL ERROR")))
          handlePushTriggerEventService.findIntegrationsForInvokeNewFlowV2(
            message = List(messageUpdate),
            event = EventType.UPDATED_PROSPECT_IN_SMARTREACH,
            integration = crMIntegrationInDB.copy(create_or_update_record_in_crm = false),
            accountId = accountId,
            teamId = team_id
          ).map { result =>
            println(s"result ----------- $result")

            assert(result.isLeft)
          }.recover { e =>
            println(s"ERROR  -----------${e.getMessage}")
            assert(false)
          }
        }

        it("should send Left when triggerDAO.findAllforMQProcess sends empty Seq") {
          (triggerDAO.findAllforMQProcess)
            .expects(accountId, team_id, EventType.UPDATED_PROSPECT_IN_SMARTREACH, Some(IntegrationType.HUBSPOT))
            .returning(Success(Seq()))
          handlePushTriggerEventService.findIntegrationsForInvokeNewFlowV2(
            message = List(messageUpdate),
            event = EventType.UPDATED_PROSPECT_IN_SMARTREACH,
            integration = crMIntegrationInDB.copy(create_or_update_record_in_crm = false),
            accountId = accountId,
            teamId = team_id
          ).map { result =>
            println(s"result ----------- $result")

            assert(result == Right(Seq(0)))
          }.recover { e =>
            println(s"ERROR  -----------${e.getMessage}")
            assert(false)
          }
        }

        it("when triggerDAO.findAllforMQProcess sends non empty Seq (Success Path)") {

          (triggerDAO.findAllforMQProcess)
            .expects(accountId, team_id, EventType.UPDATED_PROSPECT_IN_SMARTREACH, Some(IntegrationType.HUBSPOT))
            .returning(Success(Seq(dummyTriggerInDB.copy(event = Some(EventType.UPDATED_PROSPECT_IN_SMARTREACH)))))

          (triggerDAO.updateLastRanAt)
            .expects(1)
            .returning(Success(None))
          (triggerDAO.clearErrorV2)
            .expects(1)
            .returning(Success(None))

          handlePushTriggerEventService.findIntegrationsForInvokeNewFlowV2(
            message = List(messageUpdate),
            event = EventType.UPDATED_PROSPECT_IN_SMARTREACH,
            integration = crMIntegrationInDB.copy(create_or_update_record_in_crm = false),
            accountId = accountId,
            teamId = team_id
          ).map { result =>
            println(s"result ----------- $result")

            assert(result == Right(Seq(0, 0)))
          }.recover { e =>
            println(s"ERROR  -----------${e.getMessage}")
            assert(false)
          }
        }
      }

      describe("Should run the triggers when create_or_update_record_in_crm is true") {

        it("should send Left when triggerDAO.findAllforMQProcess fails") {

          (triggerDAO.findAllforMQProcess)
            .expects(accountId, team_id, EventType.UPDATED_PROSPECT_IN_SMARTREACH, Some(IntegrationType.SMARTREACH))
            .returning(Failure(new Throwable("SQL ERROR")))
          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken(_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, IntegrationType.HUBSPOT, *, *, *)
            .returning(Future.successful(Right(intg_acc_tok)))
          (triggerDAO.findFieldMapping)
            .expects(team_id, IntegrationType.HUBSPOT, moduleType)
            .returning(Success(None))
          handlePushTriggerEventService.findIntegrationsForInvokeNewFlowV2(
            message = List(messageUpdate),
            event = EventType.UPDATED_PROSPECT_IN_SMARTREACH,
            integration = crMIntegrationInDB.copy(create_or_update_record_in_crm = true),
            accountId = accountId,
            teamId = team_id
          ).map { result =>
            println(s"result ----------- $result")

            assert(result.isLeft)
          }.recover { e =>
            println(s"ERROR  -----------${e.getMessage}")
            assert(false)
          }
        }



        it("should send left when fetchTokensFromDBAndRefreshAccessToken failed") {

          (triggerDAO.findAllforMQProcess)
            .expects(accountId, team_id, EventType.UPDATED_PROSPECT_IN_SMARTREACH, Some(IntegrationType.SMARTREACH))
            .returning(Success(Seq()))
          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, IntegrationType.HUBSPOT, *, *, *)
            .returning(Future.successful(Left(FetchTokensFromDBAndRefreshAccessTokenError.SQLException(new Throwable("SQL ERROR")))))
          handlePushTriggerEventService.findIntegrationsForInvokeNewFlowV2(
            message = List(messageUpdate),
            event = EventType.UPDATED_PROSPECT_IN_SMARTREACH,
            integration = crMIntegrationInDB.copy(create_or_update_record_in_crm = true),
            accountId = accountId,
            teamId = team_id
          ).map { result =>
            println(s"result ----------- $result")

            assert(result.isLeft)
          }.recover { e =>
            println(s"ERROR  -----------${e.getMessage}")
            assert(false)
          }
        }

        it("triggerDAO.findFieldMapping  sends empty (Left path)") {

          (triggerDAO.findAllforMQProcess)
            .expects(accountId, team_id, EventType.PROSPECT_CATEGORY_UPDATED, Some(IntegrationType.SMARTREACH))
            .returning(Success(Seq()))
          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken(_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, IntegrationType.HUBSPOT, *, *, *)
            .returning(Future.successful(Right(intg_acc_tok)))
          (triggerDAO.findFieldMapping)
            .expects(team_id, IntegrationType.HUBSPOT, moduleType)
            .returning(Success(None))
          handlePushTriggerEventService.findIntegrationsForInvokeNewFlowV2(
            message = List(messageCategory),
            event = EventType.PROSPECT_CATEGORY_UPDATED,
            integration = crMIntegrationInDB.copy(
              create_or_update_record_in_crm = true,
              status_column_in_crm = Some(CRMStatusColumnNameAndOptions(column_name = "column_name", column_options = Seq())),
              category_to_status_mapping = Some(Seq(CategoryToStatusMapping(
                sr_category_id = 16, sr_category_label = "sr_category_label", crm_status = "crm_status"
              )))
            ),
            accountId = accountId,
            teamId = team_id
          ).map { result =>
            println(s"result ----------- $result")

            assert(result.isLeft)
          }.recover { e =>
            println(s"ERROR  -----------${e.getMessage}")
            assert(false)
          }
        }


        it("tIntegrationCRMService.findUpdatedUserMapping failed (Left path)") {

          (triggerDAO.findAllforMQProcess)
            .expects(accountId, team_id, EventType.PROSPECT_CATEGORY_UPDATED, Some(IntegrationType.SMARTREACH))
            .returning(Success(Seq()))
          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken(_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, IntegrationType.HUBSPOT, *, *, *)
            .returning(Future.successful(Right(intg_acc_tok)))
          (triggerDAO.findFieldMapping)
            .expects(team_id, IntegrationType.HUBSPOT, moduleType)
            .returning(Success(Some(UpdateFieldsMappingForm(fields = None))))
          (tIntegrationCRMService.findUpdatedUserMapping(_: IntegrationTPAccessTokenResponse.FullTokenData, _: Long, _: Long, _: IntegrationType)(_: WSClient, _: ExecutionContext, _: SRLogger))
            .expects(intg_acc_tok, team_id, accountId, IntegrationType.HUBSPOT, *, *, *)
            .returning(Future(Left(FindUpdatedUserMappingError.SQLException(new Throwable("SQL ERROR")))))
          handlePushTriggerEventService.findIntegrationsForInvokeNewFlowV2(
            message = List(messageCategory),
            event = EventType.PROSPECT_CATEGORY_UPDATED,
            integration = crMIntegrationInDB.copy(
              create_or_update_record_in_crm = true,
              status_column_in_crm = Some(CRMStatusColumnNameAndOptions(column_name = "column_name", column_options = Seq())),
              category_to_status_mapping = Some(Seq(CategoryToStatusMapping(
                sr_category_id = 16, sr_category_label = "sr_category_label", crm_status = "crm_status"
              )))
            ),
            accountId = accountId,
            teamId = team_id
          ).map { result =>
            println(s"result ----------- $result")

            assert(result.isLeft)
          }.recover { e =>
            println(s"ERROR  -----------${e.getMessage}")
            assert(false)
          }
        }

        val updateUserMappingForm = UpdateUserMappingForm(users = Some(Seq(TriggerUsers(sr_user_id = 18, sr_user_email = "sr_user_email", tp_user_id = "tp_user_id", tp_user_email = "tp_user_email"
        )))
        )
        val updateFieldsMappingForm = UpdateFieldsMappingForm(fields = None)

        it("Success Path (matchedCategoryMapping not defined so success)") {

          (triggerDAO.findAllforMQProcess)
            .expects(accountId, team_id, EventType.PROSPECT_CATEGORY_UPDATED, Some(IntegrationType.SMARTREACH))
            .returning(Success(Seq()))
          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken(_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, IntegrationType.HUBSPOT, *, *, *)
            .returning(Future.successful(Right(intg_acc_tok)))
          (triggerDAO.findFieldMapping)
            .expects(team_id, IntegrationType.HUBSPOT, moduleType)
            .returning(Success(Some(updateFieldsMappingForm)))
          (tIntegrationCRMService.findUpdatedUserMapping(_: IntegrationTPAccessTokenResponse.FullTokenData, _: Long, _: Long, _: IntegrationType)(_: WSClient, _: ExecutionContext, _: SRLogger))
            .expects(intg_acc_tok, team_id, accountId, IntegrationType.HUBSPOT, *, *, *)
            .returning(Future(Right(updateUserMappingForm)))

          (prospectDAOService.find)
            .expects(Seq(2L), Seq(), None, None, 2, None, 500, 0, true, false, None, *)
            .returning(Success(Seq()))
          (tIntegrationCRMService.createOrUpdateBatchContacts(_: IntegrationType, _: IntegrationModuleType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: Seq[ProspectObjectWithOldProspectDeduplicationColumn], _: UpdateFieldsMappingForm, _: UpdateUserMappingForm, _: Long, _: Long)(
            _: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger
          ))
            .expects(IntegrationType.HUBSPOT, IntegrationModuleType.LEADS, intg_acc_tok, Seq(), updateFieldsMappingForm, updateUserMappingForm, accountId, team_id, *, *, *, *)
            .returning(Future(Right(Seq())))
          handlePushTriggerEventService.findIntegrationsForInvokeNewFlowV2(
            message = List(messageCategory),
            event = EventType.PROSPECT_CATEGORY_UPDATED,
            integration = crMIntegrationInDB.copy(
              create_or_update_record_in_crm = true,
              status_column_in_crm = Some(CRMStatusColumnNameAndOptions(column_name = "column_name", column_options = Seq())),
              category_to_status_mapping = Some(Seq(CategoryToStatusMapping(
                sr_category_id = 16, sr_category_label = "sr_category_label", crm_status = "crm_status"
              )))
            ),
            accountId = accountId,
            teamId = team_id
          ).map { result =>
            println(s"result ----------- $result")

            assert(result == Right(Seq(0, 0)))
          }.recover { e =>
            println(s"ERROR  -----------${e.getMessage}")
            assert(false)
          }
        }


        it("Future fail tIntegrationCRMService.createOrUpdateBatchContacts (Left)") {

          (triggerDAO.findAllforMQProcess)
            .expects(accountId, team_id, EventType.PROSPECT_CATEGORY_UPDATED, Some(IntegrationType.SMARTREACH))
            .returning(Success(Seq()))
          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken(_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, IntegrationType.HUBSPOT, *, *, *)
            .returning(Future.successful(Right(intg_acc_tok)))
          (triggerDAO.findFieldMapping)
            .expects(team_id, IntegrationType.HUBSPOT, moduleType)
            .returning(Success(Some(updateFieldsMappingForm)))
          (tIntegrationCRMService.findUpdatedUserMapping(_: IntegrationTPAccessTokenResponse.FullTokenData, _: Long, _: Long, _: IntegrationType)(_: WSClient, _: ExecutionContext, _: SRLogger))
            .expects(intg_acc_tok, team_id, accountId, IntegrationType.HUBSPOT, *, *, *)
            .returning(Future(Right(updateUserMappingForm)))

          (prospectDAOService.find)
            .expects(Seq(2L), Seq(), None, None, 2, None, 500, 0, true, false, None, *)
            .returning(Success(Seq()))
          (tIntegrationCRMService.createOrUpdateBatchContacts(_: IntegrationType, _: IntegrationModuleType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: Seq[ProspectObjectWithOldProspectDeduplicationColumn], _: UpdateFieldsMappingForm, _: UpdateUserMappingForm, _: Long, _: Long)(
            _: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger
          ))
            .expects(IntegrationType.HUBSPOT, IntegrationModuleType.LEADS, intg_acc_tok, Seq(), updateFieldsMappingForm, updateUserMappingForm, accountId, team_id, *, *, *, *)
            .returning(Future.failed(new Throwable("FUTURE FAILED")))
          handlePushTriggerEventService.findIntegrationsForInvokeNewFlowV2(
            message = List(messageCategory),
            event = EventType.PROSPECT_CATEGORY_UPDATED,
            integration = crMIntegrationInDB.copy(
              create_or_update_record_in_crm = true,
              status_column_in_crm = Some(CRMStatusColumnNameAndOptions(column_name = "column_name", column_options = Seq())),
              category_to_status_mapping = Some(Seq(CategoryToStatusMapping(
                sr_category_id = 16, sr_category_label = "sr_category_label", crm_status = "crm_status"
              )))
            ),
            accountId = accountId,
            teamId = team_id
          ).map { result =>
            println(s"result ----------- $result")

            assert(result.isLeft)
          }.recover { e =>
            println(s"ERROR  -----------${e.getMessage}")
            assert(false)
          }
        }


        it("leadStatusService.handleUpdateLeadStatusInCRM fail (should send Left)") {

          (triggerDAO.findAllforMQProcess)
            .expects(accountId, team_id, EventType.PROSPECT_CATEGORY_UPDATED, Some(IntegrationType.SMARTREACH))
            .returning(Success(Seq()))
          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken(_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, IntegrationType.HUBSPOT, *, *, *)
            .returning(Future.successful(Right(intg_acc_tok)))
          (triggerDAO.findFieldMapping)
            .expects(team_id, IntegrationType.HUBSPOT, moduleType)
            .returning(Success(Some(updateFieldsMappingForm)))
          (tIntegrationCRMService.findUpdatedUserMapping(_: IntegrationTPAccessTokenResponse.FullTokenData, _: Long, _: Long, _: IntegrationType)(_: WSClient, _: ExecutionContext, _: SRLogger))
            .expects(intg_acc_tok, team_id, accountId, IntegrationType.HUBSPOT, *, *, *)
            .returning(Future(Right(updateUserMappingForm)))

          (prospectDAOService.find)
            .expects(Seq(2L), Seq(), None, None, 2, None, 500, 0, true, false, None, *)
            .returning(Success(Seq()))
          (tIntegrationCRMService.createOrUpdateBatchContacts(_: IntegrationType, _: IntegrationModuleType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: Seq[ProspectObjectWithOldProspectDeduplicationColumn], _: UpdateFieldsMappingForm, _: UpdateUserMappingForm, _: Long, _: Long)(
            _: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger
          ))
            .expects(IntegrationType.HUBSPOT, IntegrationModuleType.LEADS, intg_acc_tok, Seq(), updateFieldsMappingForm, updateUserMappingForm, accountId, team_id, *, *, *, *)
            .returning(Future(Right(Seq())))

          (leadStatusService.handleUpdateLeadStatusInCRM(_: EventType, _: Seq[Long], _: Option[Seq[Long]], _: String, _: CRMIntegrationInDB, _: Long)(
            _: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger
          ))
            .expects(EventType.PROSPECT_CATEGORY_UPDATED, Seq(2L), None, "crm_status", *, accountId, *, *, *, *)
            .returning(Future(Left(WorkflowAttemptTryErrorReason.UnKnownError("ERROR"))))
          handlePushTriggerEventService.findIntegrationsForInvokeNewFlowV2(
            message = List(messageCategory),
            event = EventType.PROSPECT_CATEGORY_UPDATED,
            integration = crMIntegrationInDB.copy(
              create_or_update_record_in_crm = true,
              status_column_in_crm = Some(CRMStatusColumnNameAndOptions(column_name = "column_name", column_options = Seq())),
              category_to_status_mapping = Some(Seq(CategoryToStatusMapping(
                sr_category_id = 12, sr_category_label = "sr_category_label", crm_status = "crm_status"
              )))
            ),
            accountId = accountId,
            teamId = team_id
          ).map { result =>
            println(s"result ----------- $result")

            assert(result.isLeft)
          }.recover { e =>
            println(s"ERROR  -----------${e.getMessage}")
            assert(false)
          }
        }


        it("leadStatusService.handleUpdateLeadStatusInCRM Success (Success Path)") {

          (triggerDAO.findAllforMQProcess)
            .expects(accountId, team_id, EventType.PROSPECT_CATEGORY_UPDATED, Some(IntegrationType.SMARTREACH))
            .returning(Success(Seq()))
          (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken(_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
            .expects(team_id, IntegrationType.HUBSPOT, *, *, *)
            .returning(Future.successful(Right(intg_acc_tok)))
          (triggerDAO.findFieldMapping)
            .expects(team_id, IntegrationType.HUBSPOT, moduleType)
            .returning(Success(Some(updateFieldsMappingForm)))
          (tIntegrationCRMService.findUpdatedUserMapping(_: IntegrationTPAccessTokenResponse.FullTokenData, _: Long, _: Long, _: IntegrationType)(_: WSClient, _: ExecutionContext, _: SRLogger))
            .expects(intg_acc_tok, team_id, accountId, IntegrationType.HUBSPOT, *, *, *)
            .returning(Future(Right(updateUserMappingForm)))

          (prospectDAOService.find)
            .expects(Seq(2L), Seq(), None, None, 2, None, 500, 0, true, false, None, *)
            .returning(Success(Seq()))
          (tIntegrationCRMService.createOrUpdateBatchContacts(_: IntegrationType, _: IntegrationModuleType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: Seq[ProspectObjectWithOldProspectDeduplicationColumn], _: UpdateFieldsMappingForm, _: UpdateUserMappingForm, _: Long, _: Long)(
            _: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger
          ))
            .expects(IntegrationType.HUBSPOT, IntegrationModuleType.LEADS, intg_acc_tok, Seq(), updateFieldsMappingForm, updateUserMappingForm, accountId, team_id, *, *, *, *)
            .returning(Future(Right(Seq())))

          (leadStatusService.handleUpdateLeadStatusInCRM(_: EventType, _: Seq[Long], _: Option[Seq[Long]], _: String, _: CRMIntegrationInDB, _: Long)(
            _: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger
          ))
            .expects(EventType.PROSPECT_CATEGORY_UPDATED, Seq(2L), None, "crm_status", *, accountId, *, *, *, *)
            .returning(Future(Right(Seq(0))))
          handlePushTriggerEventService.findIntegrationsForInvokeNewFlowV2(
            message = List(messageCategory),
            event = EventType.PROSPECT_CATEGORY_UPDATED,
            integration = crMIntegrationInDB.copy(
              create_or_update_record_in_crm = true,
              status_column_in_crm = Some(CRMStatusColumnNameAndOptions(column_name = "column_name", column_options = Seq())),
              category_to_status_mapping = Some(Seq(CategoryToStatusMapping(
                sr_category_id = 12, sr_category_label = "sr_category_label", crm_status = "crm_status"
              )))
            ),
            accountId = accountId,
            teamId = team_id
          ).map { result =>
            println(s"result ----------- $result")

            assert(result == Right(Seq(0, 0)))
          }.recover { e =>
            println(s"ERROR  -----------${e.getMessage}")
            assert(false)
          }
        }

      }
    }
  }
}