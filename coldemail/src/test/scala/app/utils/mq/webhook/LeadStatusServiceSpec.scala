package app.utils.mq.webhook

import org.apache.pekko.actor.ActorSystem
import org.apache.pekko.stream.Materializer
import api.accounts.AccountService
import api.emails.EmailScheduledDAO
import api.integrations.IntegrationContactResponse
import api.integrations.IntegrationTPAccessTokenResponse.FullTokenData
import api.integrations.services.TIntegrationCRMService
import api.prospects.dao_service.ProspectDAOService
import api.sr_audit_logs.models.{WorkflowAttemptInternalServerError, WorkflowAttemptTryErrorReason}
import api.sr_audit_logs.services.WorkFlowAttemptService
import api.triggers.*
import org.scalamock.scalatest.AsyncMockFactory
import org.scalatest.funspec.AsyncFunSpec
import play.api.libs.ws.{WSClient, WSResponse}
import utils.SRLogger
import utils.mq.webhook.{LeadStatusOrderingError, LeadStatusService}
import play.api.libs.ws.ahc.AhcWSClient
import utils.mq.webhook.LeadStatusOrderingError.CurrentStatusIsAheadOfNewStatus
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService
import utils.mq.webhook.model.CreateInCRMJedisDAO
import org.scalatest.matchers.should.Matchers.shouldBe
import utils.testapp.TestAppExecutionContext

import scala.concurrent.{ExecutionContext, Future}

class LeadStatusServiceSpec
  extends AsyncFunSpec
    with AsyncMockFactory {
  implicit lazy val system: ActorSystem = TestAppExecutionContext.actorSystem
  implicit lazy val materializer: Materializer = TestAppExecutionContext.actorMaterializer
  implicit lazy val wsClient: WSClient = TestAppExecutionContext.wsClient
  given Logger: SRLogger = new SRLogger("LeadStatusServiceSpec")
  val triggerDAO = mock[Trigger]
  val emailScheduledDAO = mock[EmailScheduledDAO]
  val prospectDAOService = mock[ProspectDAOService]
  val accountService = mock[AccountService]
  val workFlowAttemptService = mock[WorkFlowAttemptService]
  val tIntegrationCRMService = mock[TIntegrationCRMService]
  val srRollingUpdateCoreService = mock[SrRollingUpdateCoreService]
  val createInCRMJedisDAO = mock[CreateInCRMJedisDAO]

  val leadStatusService = new LeadStatusService(
    triggerDAO = triggerDAO,
    emailScheduledDAO = emailScheduledDAO,
    prospectDAOService = prospectDAOService,
    accountService = accountService,
    tIntegrationCRMService = tIntegrationCRMService,
    srRollingUpdateCoreService = srRollingUpdateCoreService,
    createInCRMJedisDAO = createInCRMJedisDAO
  )

  val column_options = Seq(
    CRMStatusColumnOptions(
      label = "Open - Not Contacted",
      text_id = "Open - Not Contacted"
    ),
    CRMStatusColumnOptions(
      label = "Working - Contacted",
      text_id = "Working - Contacted"
    ),
    CRMStatusColumnOptions(
      label = "Closed - Converted",
      text_id = "Closed - Converted"
    ),
    CRMStatusColumnOptions(
      label = "Closed - Not Converted",
      text_id = "Closed - Not Converted"
    ),
    CRMStatusColumnOptions(
      label = "Closed - not interested",
      text_id = "Closed - not interested"
    ),
    CRMStatusColumnOptions(
      label = "Nurturing - Contacted",
      text_id = "Nurturing - Contacted"
    )
  )

  describe("leadStatusOrdering check") {

    it("should 1: CurrentStatusNotFoundInColumnOptions") {

      val currentStatusCRM = Some("temp")
      val newStatusToBeUpdate = "Closed - Converted"

      val result = LeadStatusService.__leadStatusOrderingCheck(
        currentStatusCRM = currentStatusCRM,
        newStatusToBeUpdate = newStatusToBeUpdate,
        column_options = column_options,
        allow_going_back_in_crm_status = false
      )

      result shouldBe Left(LeadStatusOrderingError.CurrentStatusNotFoundInColumnOptions)

    }

    it("should 2: NewStatusNotFoundInColumnOptions") {

      val currentStatusCRM = Some("Closed - Converted")
      val newStatusToBeUpdate = "temp"

      val result = LeadStatusService.__leadStatusOrderingCheck(
        currentStatusCRM = currentStatusCRM,
        newStatusToBeUpdate = newStatusToBeUpdate,
        column_options = column_options,
        allow_going_back_in_crm_status = false
      )

      result shouldBe Left(LeadStatusOrderingError.NewStatusNotFoundInColumnOptions)

    }

    it("should 3: CurrentStatusIsAheadOfNewStatus") {

      val currentStatusCRM = Some("Closed - Converted")
      val newStatusToBeUpdate = "Open - Not Contacted"

      val result = LeadStatusService.__leadStatusOrderingCheck(
        currentStatusCRM = currentStatusCRM,
        newStatusToBeUpdate = newStatusToBeUpdate,
        column_options = column_options,
        allow_going_back_in_crm_status = false
      )

      result shouldBe Left(LeadStatusOrderingError.CurrentStatusIsAheadOfNewStatus)

    }

    it("should 4: Lead status is in correct in order to update") {

      val currentStatusCRM = Some("Open - Not Contacted")
      val newStatusToBeUpdate = "Closed - Converted"

      val result = LeadStatusService.__leadStatusOrderingCheck(
        currentStatusCRM = currentStatusCRM,
        newStatusToBeUpdate = newStatusToBeUpdate,
        column_options = column_options,
        allow_going_back_in_crm_status = false
      )

      result shouldBe Right(true)

    }



  }


  describe("checkOrderingAndUpdateLeadStatus") {

    val tokenData = FullTokenData(
      access_token = "access_token",
      refresh_token = Some("refresh_token"),
      expires_in = None,
      expires_at = None,
      token_type = None,
      api_domain = None,
      is_sandbox = Some(false)
    )
    val columnOptions: Seq[CRMStatusColumnOptions] = Seq(
      CRMStatusColumnOptions(
        label = "Mktg: Do not contact",
        text_id ="Do not contact"
      ),
      CRMStatusColumnOptions(
        label = "Mktg: Delivery failed",
        text_id = "Delivery failed"
      ),
      CRMStatusColumnOptions(
        label = "Mktg: Auto reply",
        text_id = "Auto Reply"
      ),
      CRMStatusColumnOptions(
        label =  "Mktg: Not categorized",
        text_id = "Not categorized"
      ),
      CRMStatusColumnOptions(
        label = "Mktg: Delegated or referred comm",
        text_id = "Mktg: Delegated or referred comm"
      ),
      CRMStatusColumnOptions(
        label = "Not now",
        text_id ="Not Now"
      ),
      CRMStatusColumnOptions(
        label = "Not yet interested lead",
        text_id = "Not yet interested"
      ),
      CRMStatusColumnOptions(
        label = "Interested lead",
        text_id = "Interested"
      ),
      CRMStatusColumnOptions(
        label = "Meeting Interest",
        text_id = "Meeting Interest"
      ),
      CRMStatusColumnOptions(
        label = "Meeting scheduled",
        text_id = "Meeting Scheduled"
      ),
      CRMStatusColumnOptions(
        label = "Proposal sent",
        text_id = "Sale in Progress"
      ),
      CRMStatusColumnOptions(
        label = "Signing verbally confirmed",
        text_id = "Signing verbally confirmed"
      ),
      CRMStatusColumnOptions(
        label = "Signed up",
        text_id = "Signed up"
      ),
      CRMStatusColumnOptions(
        label = "Project in progress",
        text_id = "Project in Progress"
      ),
      CRMStatusColumnOptions(
        label = "Project concluded",
        text_id = "Past Client"
      ),
      CRMStatusColumnOptions(
        label = "Not decision maker/Administrative",
        text_id = "Not Decision Maker/Administrative"
      ),
      CRMStatusColumnOptions(
        label = "Workflow: Using another agency",
        text_id = "Using another vendor/agency"
      ),
      CRMStatusColumnOptions(
        label = "Workflow: Not responding to sales",
        text_id ="Ignoring Comm"
      ),
      CRMStatusColumnOptions(
        label = "Workflow: No current need or interest",
        text_id = "Workflow: No Current Need or Interest"
      ),
      CRMStatusColumnOptions(
        label = "Workflow: Meeting No-show",
        text_id = "Missed Connection/No show"
      )
    )

    it("hopefully fails") {

      (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken (_: Long, _: IntegrationType)(_ : SRLogger, _: ExecutionContext, _: WSClient))
        .expects(8866, IntegrationType.HUBSPOT, *, *, *)
        .returns(Future.successful(Right(tokenData)))

      leadStatusService.checkOrderingAndUpdateLeadStatus(
        integrationType = IntegrationType.HUBSPOT,
        contactOrLead = IntegrationContactResponse(id = "2", email = Some("<EMAIL>"), status = Some("Interested")),
        moduleType = IntegrationModuleType.LEADS,
        newStatusToBeUpdate = "Not categorized",
        columnOptions = columnOptions,
        statusColumnInCRM = CRMStatusColumnNameAndOptions(column_name = "hs_lead_status", column_options = columnOptions),
        teamId = 8866,
        allow_going_back_in_crm_status = false
      ).map { result =>
        Logger.info(s"result :: $result")
        assert(result == Left(WorkflowAttemptTryErrorReason.SRInternalServerError(WorkflowAttemptInternalServerError.ErrorLeadStatusOrdering(CurrentStatusIsAheadOfNewStatus))))
      }.recover{ case e =>
        Logger.fatal(s"$e", e)
        assert(false)
      }

    }

  }

}
