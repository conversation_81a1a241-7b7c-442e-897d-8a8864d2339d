package app.encryption_service

import org.scalamock.scalatest.MockFactory
import org.scalatest.funspec.AnyFunSpec
import org.scalatest.matchers.should.Matchers
import utils.SRLogger
import utils.security.EncryptionService

import scala.util.Random

class EncryptionServiceSpec extends AnyFunSpec with MockFactory with Matchers {

  given logger: SRLogger = new SRLogger("EncryptionServiceSpec :: ")
  val random = new Random()

  describe("Testing encryption and decryption.") {
    it ("should return the original string after encrypting and decrypting it back again.") {

      val allChars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_!@#$%^&*"

      for (i <- 1 to 1024) {
        var randomStr = ""
        for (_ <- 1 to i) {
          val randomIndex = random.nextInt(allChars.length - 1)
          randomStr += allChars(randomIndex)
        }
        logger.info(s"randomStr -> $randomStr")

        var encryptionKey = ""
        for (_ <- -16 to random.nextInt(16)) {
          val randomIndex = random.nextInt(allChars.length - 1)
          encryptionKey += allChars(randomIndex)
        }
        logger.info(s"encryptionKey -> $encryptionKey")

        val encryptedString = EncryptionService.encrypt(
          key = encryptionKey,
          value = randomStr
        )
        logger.info(s"encryptedString -> $encryptedString :: length -> ${encryptedString.length}")

        val decryptedString = EncryptionService.decrypt(
          key = encryptionKey,
          encryptedValue = encryptedString
        )
        logger.info(s"decryptedString -> $decryptedString")

        assert(decryptedString == randomStr)
      }
    }
  }

}
