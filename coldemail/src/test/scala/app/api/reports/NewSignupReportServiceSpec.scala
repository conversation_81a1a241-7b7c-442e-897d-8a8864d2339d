package app.api.reports

import api.accounts.TeamId
import api.accounts.models.{AccountId, OrgId}
import api.product_onboarding.ProductOnboardingService
import api.product_onboarding.models.{UpdateAndGetProductOnboardingData, UseCaseFlags}
import api.reports.{NewSignUpReportDAO, NewSignupReportData, NewSignupReportService}
import api.scheduler_report.{ReportData, ReportType, SchedulerIntegrityService}
import db_test_spec.api.AppSpecFixture.logger
import org.joda.time.DateTime
import org.scalamock.scalatest.{AsyncMockFactory, MockFactory}
import org.scalatest.funspec.AnyFunSpec
import play.api.libs.json.JsError
import play.api.libs.json.JsResult.Exception
import utils.email_notification.service.EmailNotificationService

import scala.util.{Failure, Success, Try}

class NewSignupReportServiceSpec extends AnyFunSpec with MockFactory{

    val newSignUpReportDAO = mock[NewSignUpReportDAO]
    val productOnboardingService = mock[ProductOnboardingService]
    val schedulerIntegrityService= mock[SchedulerIntegrityService]
    val emailNotificationService =  mock[EmailNotificationService]

    val newSignupReportService = new NewSignupReportService(
        newSignUpReportDAO = newSignUpReportDAO,
        productOnboardingService = productOnboardingService,
        schedulerIntegrityService = schedulerIntegrityService,
        emailNotificationService = emailNotificationService
    )

    describe("dailyNewSignupReport"){
        it("should return the list of new signup checks"){
            val newSignupList:List[NewSignupReportData] = List(
                NewSignupReportData("<EMAIL>",DateTime.now,false,OrgId(1),TeamId(1),AccountId(34)),
                NewSignupReportData("<EMAIL>",DateTime.now,true,OrgId(3),TeamId(3),AccountId(234)))
            val useCaseFlags: UseCaseFlags = UseCaseFlags(
                is_using_for_linkedin_outreach = false,
                is_using_for_cold_emailing   = false,
                is_using_for_cold_calling = false,
                is_using_for_whatsapp_outreach = false,
                is_using_for_leads = false
            )
            val newSignupCheckUser1: UpdateAndGetProductOnboardingData = UpdateAndGetProductOnboardingData(
                is_intro_completed = true,
                is_inbox_setup_completed = false,
                is_multi_channel_setup_completed = false,
                is_team_invite_sent = true,
                is_spam_test_done = true,
                is_prospect_list_validated = false,
                is_warm_up_hero_setup_done = true,
                is_installing_prospect_daddy_done = true,
                is_email_finder_setup_done = false,
                is_connect_crm_done = true,
                is_zapier_integration_done = false,
                is_campaign_created_for_org = true,
                is_sender_email_added = true,
                is_linkedin_added = false,
                is_calling_account_added = true,
                is_campaign_not_started = false,
                is_whatsapp_account_added =false,
                use_case_flags = useCaseFlags,
                is_leadfinder_used = false
            )

            val newSignupCheckUser2:  UpdateAndGetProductOnboardingData = UpdateAndGetProductOnboardingData(
                is_intro_completed = true,
                is_inbox_setup_completed = false,
                is_multi_channel_setup_completed = false,
                is_team_invite_sent = true,
                is_spam_test_done = true,
                is_prospect_list_validated = false,
                is_warm_up_hero_setup_done = true,
                is_installing_prospect_daddy_done = true,
                is_email_finder_setup_done = false,
                is_connect_crm_done = true,
                is_zapier_integration_done = false,
                is_campaign_created_for_org = true,
                is_sender_email_added = true,
                is_linkedin_added = false,
                is_calling_account_added = true,
                is_campaign_not_started = false,
                is_whatsapp_account_added = false,
                use_case_flags = useCaseFlags,
                is_leadfinder_used = false
            )


            (() => newSignUpReportDAO.getNewSignupData)
              .expects()
              .returning(Success(newSignupList))

            (productOnboardingService.getProductOnboardingData)
              .expects(OrgId(1),TeamId(1),AccountId(34))
              .returning(Success(newSignupCheckUser1))

            (productOnboardingService.getProductOnboardingData)
              .expects(OrgId(3),TeamId(3),AccountId(234))
              .returning(Success(newSignupCheckUser2))

            val result:Try[List[ReportData.NewSignupReportData]] = newSignupReportService.dailyNewSignupReport(reportType = ReportType.NEW_SIGNUP_REPORT)

            result match {
                case Success(data) =>
                    println(data)
                    assert(data.exists(_.email == "<EMAIL>"))

                case Failure(exception) =>
                    println(exception)
                    assert(false)
            }
        }

        it("should return the failure"){
            val newSignupList:List[NewSignupReportData] = List(
                NewSignupReportData("<EMAIL>",DateTime.now,false,OrgId(1),TeamId(1),AccountId(34)),
                NewSignupReportData("<EMAIL>",DateTime.now,true,OrgId(3),TeamId(3),AccountId(234)))
            val useCaseFlags: UseCaseFlags = UseCaseFlags(
                is_using_for_linkedin_outreach = false,
                is_using_for_cold_emailing   = false,
                is_using_for_cold_calling = false,
                is_using_for_whatsapp_outreach = false,
                is_using_for_leads = false
            )
            val newSignupCheckUser1: UpdateAndGetProductOnboardingData = UpdateAndGetProductOnboardingData(
                is_intro_completed = true,
                is_inbox_setup_completed = false,
                is_multi_channel_setup_completed = false,
                is_team_invite_sent = true,
                is_spam_test_done = true,
                is_prospect_list_validated = false,
                is_warm_up_hero_setup_done = true,
                is_installing_prospect_daddy_done = true,
                is_email_finder_setup_done = false,
                is_connect_crm_done = true,
                is_zapier_integration_done = false,
                is_campaign_created_for_org = true,
                is_sender_email_added = true,
                is_linkedin_added = false,
                is_calling_account_added = true,
                is_campaign_not_started = false,
                is_whatsapp_account_added = false,
                use_case_flags = useCaseFlags,
                is_leadfinder_used = false
            )

            (() => newSignUpReportDAO.getNewSignupData)
              .expects()
              .returning(Success(newSignupList))

            (productOnboardingService.getProductOnboardingData)
              .expects(OrgId(1),TeamId(1),AccountId(34))
              .returning(Success(newSignupCheckUser1))

            (productOnboardingService.getProductOnboardingData)
              .expects(OrgId(3),TeamId(3),AccountId(234))
              .returning(Failure(new Exception(JsError("error occured"))))

            val result:Try[List[ReportData.NewSignupReportData]] = newSignupReportService.dailyNewSignupReport(reportType = ReportType.NEW_SIGNUP_REPORT)
            println(result)
            assert(result.isFailure)
        }
    }

}
