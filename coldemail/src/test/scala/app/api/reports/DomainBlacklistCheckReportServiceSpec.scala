package app.api.reports

import org.apache.pekko.actor.ActorSystem
import org.apache.pekko.stream.Materializer
import api.accounts.TeamId
import api.accounts.models.OrgId
import api.domain_health.DomainHealthCheckDAO
import api.reports.services.DomainBlacklistCheckReportService
import api.scheduler_report.ReportData.{DomainBlacklistCheckLogs, EmailValidationReportData}
import api.scheduler_report.ReportType.EMAIL_VALIDATION_APIS
import api.scheduler_report.{ReportData, ReportType, SchedulerIntegrityService}
import org.joda.time.DateTime
import org.scalamock.scalatest.MockFactory
import org.scalatest.funspec.AnyFunSpec
import play.api.libs.ws.WSClient
import play.api.libs.ws.ahc.AhcWSClient
import utils.SRLogger
import utils.email_notification.service.EmailNotificationService
import utils.testapp.TestAppExecutionContext

import scala.concurrent.ExecutionContext
import scala.util.{Failure, Success}

class DomainBlacklistCheckReportServiceSpec extends AnyFunSpec with MockFactory{

    given logger: SRLogger = new SRLogger("EmailValidationDailyReportServiceSpec ")

    val domainHealthCheckDAO: DomainHealthCheckDAO = mock[DomainHealthCheckDAO]
    val schedulerIntegrityService: SchedulerIntegrityService = mock[SchedulerIntegrityService]
    val emailNotificationService: EmailNotificationService = mock[EmailNotificationService]


    given system: ActorSystem = TestAppExecutionContext.actorSystem

    given materializer: Materializer = TestAppExecutionContext.actorMaterializer

    given wSClient: AhcWSClient = TestAppExecutionContext.wsClient

    given actorContext: ExecutionContext = system.dispatcher

    val domainBlacklistCheckReportService = new DomainBlacklistCheckReportService(
        domainHealthCheckDAO = domainHealthCheckDAO,
        schedulerIntegrityService = schedulerIntegrityService,
        emailNotificationService = emailNotificationService
    )


    describe("DomainBlacklistCheckReportService getDomainBlacklistCheckDataAndInsertIntoReportTable"){
        it("should pass and return false when checkIfReportTypeHasRunInLastOneDay returns false "){


            (schedulerIntegrityService.checkIfCronCanRunForReport (_:ReportType)(using _:SRLogger))
              .expects(ReportType.DOMAIN_BLACKLIST_REPORT,logger)
              .returning(Success(false))

            val result = domainBlacklistCheckReportService.getDomainBlacklistCheckDataAndInsertIntoReportTable(
                reportType = ReportType.DOMAIN_BLACKLIST_REPORT
            )

            assert(result.isSuccess)



        }

        it("should pass and return true when new report is inserted into the table"){
          
            val blacklistReport =List(
                DomainBlacklistCheckLogs(
                    "gmail.com",
                    OrgId(1),
                    TeamId(1),
                    "Smartreach Blacklist",
                    false,
                    ReportType.DOMAIN_BLACKLIST_REPORT),
                DomainBlacklistCheckLogs(
                    "rediffmail.com",
                    OrgId(2),
                    TeamId(2),
                    "Smartreach Blacklist",
                    false,
                    ReportType.DOMAIN_BLACKLIST_REPORT)
            )

            (schedulerIntegrityService.checkIfCronCanRunForReport (_:ReportType)(using _:SRLogger))
              .expects(ReportType.DOMAIN_BLACKLIST_REPORT,logger)
              .returning(Success(true))

            (domainHealthCheckDAO.getDataForDailyReporting)
              .expects(*,*)
              .returning(Success(blacklistReport))

            (schedulerIntegrityService.insertListOfReportData(_:ReportType,_:List[ReportData])(using _:SRLogger))
              .expects(ReportType.DOMAIN_BLACKLIST_REPORT,blacklistReport,logger)
              .returning(Success(12L))

            (emailNotificationService.sendDomainBlacklistCheckReport(_:List[DomainBlacklistCheckLogs])(_:WSClient,_:ExecutionContext,_:SRLogger))
              .expects(blacklistReport,wSClient,actorContext,logger)
              .returning(Success({}))



            val result = domainBlacklistCheckReportService.getDomainBlacklistCheckDataAndInsertIntoReportTable(
                reportType = ReportType.DOMAIN_BLACKLIST_REPORT
            )

            assert(result.isSuccess)
        }

        it("should fail when getDataForDailyReporting fails"){
           

            val blacklistReport =List(
                DomainBlacklistCheckLogs(
                    "gmail.com",
                    OrgId(1),
                    TeamId(1),
                    "Smartreach Blacklist",
                    false,
                    ReportType.DOMAIN_BLACKLIST_REPORT),
                DomainBlacklistCheckLogs(
                    "rediffmail.com",
                    OrgId(2),
                    TeamId(2),
                    "Smartreach Blacklist",
                    false,
                    ReportType.DOMAIN_BLACKLIST_REPORT)
            )

            (schedulerIntegrityService.checkIfCronCanRunForReport (_:ReportType)(using _:SRLogger))
              .expects(ReportType.DOMAIN_BLACKLIST_REPORT,logger)
              .returning(Success(true))

            (domainHealthCheckDAO.getDataForDailyReporting)
              .expects(*,*)
              .returning(Failure(new Exception("Error occured while fetching data")))

            val result = domainBlacklistCheckReportService.getDomainBlacklistCheckDataAndInsertIntoReportTable(
                reportType = ReportType.DOMAIN_BLACKLIST_REPORT
            )

            assert(result.isFailure)
        }
    }

}
