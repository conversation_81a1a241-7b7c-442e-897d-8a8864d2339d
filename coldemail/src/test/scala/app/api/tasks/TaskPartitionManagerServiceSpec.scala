package app.api.tasks

import org.apache.pekko.actor.ActorSystem
import api.tasks.models.PartitionedParentTableName
import api.tasks.pgDao.TaskPgDAO
import api.tasks.services.TaskPartitionManagerService
import org.scalamock.scalatest.AsyncMockFactory
import org.scalatest.funspec.AsyncFunSpec
import utils.SRLogger
import utils.testapp.TestAppExecutionContext

import scala.concurrent.{ExecutionContext, Future}

class TaskPartitionManagerServiceSpec  extends AsyncFunSpec
  with AsyncMockFactory {

  val taskPgDAO: TaskPgDAO = mock[TaskPgDAO]

  val taskPartitionManagerService = new TaskPartitionManagerService(
    taskPgDAO = taskPgDAO,
  )
  implicit lazy val system: ActorSystem = TestAppExecutionContext.actorSystem
  implicit lazy val ec: ExecutionContext = system.dispatcher


  val retention = "3 months"
  val except_status = List("done", "archived")
  val error = new Throwable("Dummy Error")

  describe("Testing move_to_archive_function"){
    it("It should fail if taskPgDAO.moveToArchive fails"){

      (taskPgDAO.moveToArchive(_: String, _: List[String])(_: ExecutionContext))
        .expects(retention, except_status, *)
        .returning(Future.failed(error))

      val res = taskPartitionManagerService.moveToArchive(
        retention_time = retention,
        except_status = except_status
      )

      res.map(result => assert(result == 1))
        .recover{case e => assert(e == error)}
    }

    it("It should succeed if taskPgDAO.moveToArchive succeeds"){

      (taskPgDAO.moveToArchive(_: String, _: List[String])(_: ExecutionContext))
        .expects(retention, except_status, *)
        .returning(Future.successful(1))

      val res = taskPartitionManagerService.moveToArchive(
        retention_time = retention,
        except_status = except_status
      )

      res.map(result => assert(result == 1))
        .recover{case e => assert(e == error)}
    }
  }
  given logger: SRLogger = new SRLogger("Partition Maintenance Logger")

  describe("Testing partition_maintenance function"){

    val parent_table = PartitionedParentTableName(
      parent_table = "public.task_other"
    )

    it("It should fail if taskPgDAO.moveToArchive fails"){

      (taskPgDAO.moveToArchive(_: String, _: List[String])(_: ExecutionContext))
        .expects(retention, except_status, *)
        .returning(Future.failed(error))

      val res = taskPartitionManagerService.partition_maintenance(
        retention_time = retention,
        except_status = except_status
      )

      res.map(result => assert(result == 1))
        .recover{case e => assert(e == error)}
    }

    it("It should fail if taskPgDAO.checkIfOldPartitionsAreEmpty returns int > 0"){


      (taskPgDAO.checkIfOldPartitionsAreEmpty(_: String, _: List[String])(_: ExecutionContext))
        .expects(retention, except_status, *)
        .returning(Future.successful(1))

      (taskPgDAO.moveToArchive(_: String, _: List[String])(_: ExecutionContext))
        .expects(retention, except_status, *)
        .returning(Future.successful(1))

      val res = taskPartitionManagerService.partition_maintenance(
        retention_time = retention,
        except_status = except_status
      )

      res.map(result => assert(result == 1))
        .recover{case e => assert(e.getMessage == "Future.filter predicate is not satisfied")}
    }

    it("It should fail if taskPgDAO.getPartitionedTableNames fails") {

      (taskPgDAO.checkIfOldPartitionsAreEmpty(_: String, _: List[String])(_: ExecutionContext))
        .expects(retention, except_status, *)
        .returning(Future.successful(0))

      (taskPgDAO.getPartitionedTableNames()(_: ExecutionContext))
        .expects(*)
        .returning(Future.failed(error))

      (taskPgDAO.moveToArchive(_: String, _: List[String])(_: ExecutionContext))
        .expects(retention, except_status, *)
        .returning(Future.successful(1))

      val res = taskPartitionManagerService.partition_maintenance(
        retention_time = retention,
        except_status = except_status
      )

      res.map(result => assert(result == 1))
        .recover { case e => assert(e == error) }
    }

    it("It should fail if taskPgDAO.getPartitionedTableNames returns empty list") {

      (taskPgDAO.checkIfOldPartitionsAreEmpty(_: String, _: List[String])(_: ExecutionContext))
        .expects(retention, except_status, *)
        .returning(Future.successful(0))

      (taskPgDAO.getPartitionedTableNames()(_: ExecutionContext))
        .expects(*)
        .returning(Future.successful(List()))

      (taskPgDAO.moveToArchive(_: String, _: List[String])(_: ExecutionContext))
        .expects(retention, except_status, *)
        .returning(Future.successful(1))

      val res = taskPartitionManagerService.partition_maintenance(
        retention_time = retention,
        except_status = except_status
      )

      res.map(result => assert(result == 1))
        .recover { case e => assert(e.getMessage == "Future.filter predicate is not satisfied") }
    }

    it("It should fail  if taskPgDAO.moveDataToTemporaryTableFromDefaultPartition fails for one table") {

      (taskPgDAO.checkIfOldPartitionsAreEmpty(_: String, _: List[String])(_: ExecutionContext))
        .expects(retention, except_status, *)
        .returning(Future.successful(0))

      (taskPgDAO.getPartitionedTableNames()(_: ExecutionContext))
        .expects(*)
        .returning(Future.successful(List(
          parent_table,
          parent_table.copy(parent_table = "public.email_notification_logs"),
          parent_table.copy(parent_table = "public.event_logs")
        )))

      (taskPgDAO.moveDataToTemporaryTableFromDefaultPartition(_: PartitionedParentTableName)(_: ExecutionContext))
        .expects(parent_table, *)
        .returning(Future.successful(true))
        .once()

      (taskPgDAO.moveDataToTemporaryTableFromDefaultPartition(_: PartitionedParentTableName)(_: ExecutionContext))
        .expects(parent_table.copy(parent_table = "public.email_notification_logs"), *)
        .returning(Future.failed(error))
        .once()

      (taskPgDAO.moveDataToTemporaryTableFromDefaultPartition(_: PartitionedParentTableName)(_: ExecutionContext))
        .expects(parent_table.copy(parent_table = "public.event_logs"), *)
        .returning(Future.successful(true))
        .once()

      (taskPgDAO.moveToArchive(_: String, _: List[String])(_: ExecutionContext))
        .expects(retention, except_status, *)
        .returning(Future.successful(1))

      val res = taskPartitionManagerService.partition_maintenance(
        retention_time = retention,
        except_status = except_status
      )

      res.map(result => assert(result == 1))
        .recover { case e => assert(e == error) }
    }

    it("It should fail if taskPgDAO.callPartitionManager fails"){

      (taskPgDAO.checkIfOldPartitionsAreEmpty(_: String, _: List[String])(_: ExecutionContext))
        .expects(retention, except_status, *)
        .returning(Future.successful(0))

      (taskPgDAO.getPartitionedTableNames()(_: ExecutionContext))
        .expects(*)
        .returning(Future.successful(List(
          parent_table,
          parent_table.copy(parent_table = "public.email_notification_logs"),
          parent_table.copy(parent_table = "public.event_logs")
        )))

      (taskPgDAO.moveDataToTemporaryTableFromDefaultPartition(_: PartitionedParentTableName)(_: ExecutionContext))
        .expects(parent_table, *)
        .returning(Future.successful(true))
        .once()

      (taskPgDAO.moveDataToTemporaryTableFromDefaultPartition(_: PartitionedParentTableName)(_: ExecutionContext))
        .expects(parent_table.copy(parent_table = "public.email_notification_logs"), *)
        .returning(Future.successful(true))
        .once()

      (taskPgDAO.moveDataToTemporaryTableFromDefaultPartition(_: PartitionedParentTableName)(_: ExecutionContext))
        .expects(parent_table.copy(parent_table = "public.event_logs"), *)
        .returning(Future.successful(true))
        .once()

      (taskPgDAO.callPartitionManager()(_: ExecutionContext))
        .expects(*)
        .returning(Future.failed(error))

      (taskPgDAO.moveToArchive(_: String, _: List[String])(_: ExecutionContext))
        .expects(retention, except_status, *)
        .returning(Future.successful(1))

      val res = taskPartitionManagerService.partition_maintenance(
        retention_time = retention,
        except_status = except_status
      )

      res.map(result => assert(result == 1))
        .recover{case e => assert(e == error)}
    }

    it("It should succeeds if taskPgDAO.callPartitionManager succeeds"){

      (taskPgDAO.checkIfOldPartitionsAreEmpty(_: String, _: List[String])(_: ExecutionContext))
        .expects(retention, except_status, *)
        .returning(Future.successful(0))

      (taskPgDAO.getPartitionedTableNames()(_: ExecutionContext))
        .expects(*)
        .returning(Future.successful(List(
          parent_table,
          parent_table.copy(parent_table = "public.email_notification_logs"),
          parent_table.copy(parent_table = "public.event_logs")
        )))

      (taskPgDAO.moveDataToTemporaryTableFromDefaultPartition(_: PartitionedParentTableName)(_: ExecutionContext))
        .expects(parent_table, *)
        .returning(Future.successful(true))
        .once()

      (taskPgDAO.moveDataToTemporaryTableFromDefaultPartition(_: PartitionedParentTableName)(_: ExecutionContext))
        .expects(parent_table.copy(parent_table = "public.email_notification_logs"), *)
        .returning(Future.successful(true))
        .once()

      (taskPgDAO.moveDataToTemporaryTableFromDefaultPartition(_: PartitionedParentTableName)(_: ExecutionContext))
        .expects(parent_table.copy(parent_table = "public.event_logs"), *)
        .returning(Future.successful(true))
        .once()

      (taskPgDAO.callPartitionManager()(_: ExecutionContext))
        .expects(*)
        .returning(Future.successful(true))

      (taskPgDAO.partitionGapFill(_: PartitionedParentTableName)(_: ExecutionContext))
        .expects(parent_table, *)
        .returning(Future.successful((parent_table) -> 0))
        .once()

      (taskPgDAO.partitionGapFill(_: PartitionedParentTableName)(_: ExecutionContext))
        .expects(parent_table.copy(parent_table = "public.event_logs"), *)
        .returning(Future.successful((parent_table.copy(parent_table = "public.event_logs")) -> 0))
        .once()

      (taskPgDAO.partitionGapFill(_: PartitionedParentTableName)(_: ExecutionContext))
        .expects(parent_table.copy(parent_table = "public.email_notification_logs"), *)
        .returning(Future.successful(parent_table.copy(parent_table = "public.email_notification_logs") -> 0))
        .once()

      (taskPgDAO.moveToArchive(_: String, _: List[String])(_: ExecutionContext))
        .expects(retention, except_status, *)
        .returning(Future.successful(1))

      val res = taskPartitionManagerService.partition_maintenance(
        retention_time = retention,
        except_status = except_status
      )

      res.map(result => assert(result == 1))
        .recover{case e => assert(e == error)}
    }
  }


}
