package app.api.tasks

import org.apache.pekko.actor.ActorSystem
import api.accounts.TeamId
import api.accounts.models.{AccountId, OrgId}
import api.call.DAO.CallDAO
import api.call.models.{CallSID, CallSidOrConfUuid}
import api.campaigns.models.{CampaignStepType, CurrentStepStatusForScheduler, CurrentStepStatusForSchedulerData}
import api.campaigns.services.{CampaignCacheService, CampaignId}
import api.campaigns.{CampaignDAO, CampaignProspectDAO, CampaignProspectUpdateScheduleStatus}
import api.emails.{CampaignProspectStepScheduleLogData, CampaignProspectStepScheduleLogsDAO}
import api.prospects.dao.{ProspectAddEventDAO, ProspectDAO}
import api.prospects.dao_service.{ProspectDAOService, ProspectDAOServiceV2}
import api.prospects.{CreateProspectEventDB, ExactIdToCompareTime, InferredQueryTimeline, NavigationLinks, ProspectUpdateCategoryTemp}
import api.sr_audit_logs.models.{EventDataType, EventType}
import api.sr_audit_logs.services.EventLogService
import api.tasks.models.TaskType.ViewLinkedinProfile
import api.tasks.models.{Assignee, ChangeStatusPermissionCheck, SearchTask, SubCount, Task, TaskCount, TaskCreatedVia, TaskData, TaskPriority, TaskProspect, TaskStatus, TaskType, TimeBasedTaskType, UpdateTaskStatus}
import api.tasks.pgDao.{DateRangeBetween, TaskPgDAO, ValidatedTaskReq}
import api.team_inbox.dao.ReplySentimentDAO
import api.team_inbox.model.ReplySentimentTypeData.NegativeData
import api.team_inbox.model.{ReplySentimentChannelType, ReplySentimentSubCategory, ReplySentimentTypeData, ReplySentimentUpdatedBy}
import api.team_inbox.service.ReplySentimentForTeam
import org.scalamock.matchers.ArgCapture.CaptureOne
import utils.dateTime.SrDateTimeUtils
import utils.helpers.LogHelpers
import utils.mq.campaigns.{MqPauseCampaignOnReplySentimentSelect, PauseCampaignData}
import utils.mq.prospect_category.{MqAutoUpdateProspectCategoryMsg, MqAutoUpdateProspectCategoryPublisher}
import utils.mq.services.MQConfig
import utils.testapp.TestAppExecutionContext
import utils_deploy.rolling_updates.models.SrRollingUpdateFeature
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService

import scala.concurrent.duration.*
import scala.concurrent.Await
import scala.util.Try
//import api.tasks.scylladb.dao.TaskCacheDAO
import api.prospects.models.ProspectId
import api.tasks.services.{GetAllTaskForUserError, ParamValidationError, TaskAccessError, TaskDaoService, TaskPagination, TaskService, TaskUuid}
import api.team_inbox.dao_service.ReplySentimentDAOService
import api.team_inbox.service.{ReplySentimentService, ReplySentimentUuid}
import org.joda.time.DateTime
import org.scalamock.scalatest.AsyncMockFactory
import org.scalatest.funspec.AsyncFunSpec
import play.api.libs.ws.ahc.AhcWSClient
import scalikejdbc.DBSession
import utils.dbutils.{DBUtils, DbAndSession}
import utils.{SRLogger, StringUtilsV2}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}


class TaskServiceSpec extends AsyncFunSpec
  with AsyncMockFactory {

  val taskDaoService: TaskDaoService = mock[TaskDaoService]
  val campaignProspectDAO: CampaignProspectDAO = mock[CampaignProspectDAO]
  val dbUtils = mock[DBUtils]
  val prospectDAO = mock[ProspectDAO]
  val srDateTimeUtils = mock[SrDateTimeUtils]
  val prospectDAOService: ProspectDAOService = mock[ProspectDAOService]
  val prospectDAOServiceV2: ProspectDAOServiceV2 = mock[ProspectDAOServiceV2]
  val conferenceDAO: CallDAO = mock[CallDAO]
  val srRollingUpdateCoreService = mock[SrRollingUpdateCoreService]
  val campaignProspectStepScheduleLogsDAO: CampaignProspectStepScheduleLogsDAO = mock[CampaignProspectStepScheduleLogsDAO]
  val campaignCacheService: CampaignCacheService = mock[CampaignCacheService]
  val eventLogService: EventLogService = mock[EventLogService]
  val mqPauseCampaignOnReplySentimentSelect: MqPauseCampaignOnReplySentimentSelect = mock[MqPauseCampaignOnReplySentimentSelect]
  val prospectAddEventDAO: ProspectAddEventDAO = mock[ProspectAddEventDAO]
  val campaignDAO = mock[CampaignDAO]
  val replySentimentDAO: ReplySentimentDAO = mock[ReplySentimentDAO]
  val mqAutoUpdateProspectCategoryPublisher: MqAutoUpdateProspectCategoryPublisher = mock[MqAutoUpdateProspectCategoryPublisher]

  val taskService = new TaskService(
    taskDaoService = taskDaoService,
    campaignProspectDAO = campaignProspectDAO,
    dbUtils = dbUtils,
    prospectDAO = prospectDAO,
    srDateTimeUtils = srDateTimeUtils,
    prospectDAOService = prospectDAOService,
    prospectDAOServiceV2 = prospectDAOServiceV2,
    callDAO = conferenceDAO,
    srRollingUpdateCoreService = srRollingUpdateCoreService,
    campaignProspectStepScheduleLogsDAO = campaignProspectStepScheduleLogsDAO,
    prospectAddEventDAO = prospectAddEventDAO,
    mqPauseCampaignOnReplySentimentSelect = mqPauseCampaignOnReplySentimentSelect,
    campaignCacheService = campaignCacheService,
    eventLogService = eventLogService,
    replySentimentDAO = replySentimentDAO,
    mqAutoUpdateProspectCategoryPublisher = mqAutoUpdateProspectCategoryPublisher,
    campaignDAO = campaignDAO
  )

  implicit lazy val system: ActorSystem = TestAppExecutionContext.actorSystem
  implicit lazy val ec: ExecutionContext = system.dispatcher
  implicit lazy val wSClient: AhcWSClient = TestAppExecutionContext.wsClient
  implicit lazy val logger: SRLogger = new SRLogger("TaskService Spec")

  val aDate = DateTime.parse("2022-10-12")
  val dateRangeA = DateTime.parse("2022-9-9")
  val dateRangeB = DateTime.parse("2025-01-01")
  val doneDate = DateTime.parse("2024-07-26")
  val skippedDate = DateTime.parse("2024-07-27")
  val snoozedDate = DateTime.parse("2024-07-28")
  val archivedDate = DateTime.parse("2024-07-29")
  val failedDate = DateTime.parse("2024-07-30")
  val team_id_9 = 9L
  val org_id_9 = OrgId(9)

  val team_id_VC = TeamId(
    id = team_id_9
  )

  val task_uuid_VC = TaskUuid(
    uuid = "task 123"
  )

  val error = new Throwable("Future Got Failed")


  val todayTask = Task(
    task_id = "todayTask", // Todo - stringUtils.randomString
    task_type = TaskType.SendEmail,
    task_data = TaskData.SendEmailData(
      subject = "Hi this is the subject",
      body = "hello, this is the body",
      email_message_id = None,
      task_type = TaskType.SendEmail
    ),
    status = TaskStatus.Due(
      due_at = aDate
    ),
    assignee = Some(Assignee(
      id = 1,
      name = "Shashank"
    )),
    added_by = 1,
    is_auto_task = false,
    team_id = team_id_9,
    prospect = Some(
      TaskProspect(
        id = 1,
        name = Some("Name"),
        email = Some("email"),
        company = Some("company"),
        phone_number = Some("phone_number"),
        linkedin_url = Some("linkedin_url"),
        timezone = Some("timezone"),
        designation = Some("designation")
      )
    ),
    priority = TaskPriority.Critical,
    note = Some("This is Note"),
    created_at = DateTime.now(),
    updated_at = Some(DateTime.now()),
    due_at = aDate,
    campaign_id = None,
    campaign_name = None,
    step_id = None,
    step_label = None,
    is_opening_step = None,
    created_via = TaskCreatedVia.Manual,
    reply_sentiment_uuid = None
  )

  val upcomingTask = Task(
    task_id = "upcoming task", // Todo - stringUtils.randomString
    task_type = TaskType.SendEmail,
    task_data = TaskData.SendEmailData(
      subject = "Hi this is the subject", body = "hello, this is the body",
      email_message_id = None,
      task_type = TaskType.SendEmail
    ),
    status = TaskStatus.Due(
      due_at = aDate.plusDays(1)
    ),
    assignee = Some(Assignee(
      id = 1,
      name = "Shashank"
    )),
    added_by = 1,
    is_auto_task = false,
    team_id = team_id_9,
    prospect = Some(
      TaskProspect(
        id = 1,
        name = Some("Name"),
        email = Some("email"),
        company = Some("company"),
        phone_number = Some("phone_number"),
        linkedin_url = Some("linkedin_url"),
        timezone = Some("timezone"),
        designation = Some("designation")
      )
    ),
    priority = TaskPriority.Critical,
    note = Some("This is Note"),
    created_at = DateTime.now(),
    updated_at = Some(DateTime.now()),
    due_at = aDate.plusDays(1),
    campaign_id = None,
    campaign_name = None,
    step_id = None,
    step_label = None,
    is_opening_step = None,
    created_via = TaskCreatedVia.Manual,
    reply_sentiment_uuid = None
  )

  val dueTask = Task(
    task_id = "Due Task", // Todo - stringUtils.randomString
    task_type = TaskType.SendEmail,
    task_data = TaskData.SendEmailData(
      subject = "Hi this is the subject", body = "hello, this is the body",
      email_message_id = None,
      task_type = TaskType.SendEmail
    ),
    status = TaskStatus.Due(
      due_at = aDate.minusDays(1)
    ),
    assignee = Some(Assignee(
      id = 1,
      name = "Shashank"
    )),
    added_by = 1,
    is_auto_task = false,
    team_id = team_id_9,
    prospect = Some(
      TaskProspect(
        id = 1,
        name = Some("Name"),
        email = Some("email"),
        company = Some("company"),
        phone_number = Some("phone_number"),
        linkedin_url = Some("linkedin_url"),
        timezone = Some("timezone"),
        designation = Some("designation")
      )
    ),
    priority = TaskPriority.Critical,
    note = Some("This is Note"),
    created_at = DateTime.now(),
    updated_at = Some(DateTime.now()),
    due_at = aDate.minusDays(1),
    campaign_id = None,
    campaign_name = None,
    step_id = None,
    step_label = None,
    is_opening_step = None,
    created_via = TaskCreatedVia.Manual,
    reply_sentiment_uuid = None
  )

  val listOfTasks: List[Task] = List(todayTask, upcomingTask, dueTask)
  val searchTask = SearchTask(
    assignee_ids = None,
    campaign_ids = None,
    reply_sentiment_uuids = None,
    task_status = None,
    task_priority = None,
    task_types = None,
    time_based_task_type = None
  )

  val lastTwoTasksWithSameDueDate = List(upcomingTask,
    todayTask, dueTask, dueTask.copy(due_at = aDate.minusDays(2)), dueTask.copy(due_at = aDate.minusDays(2))
  )

  val validatedTaskReq = ValidatedTaskReq.ValidatedTaskReqRange(
    timeline = InferredQueryTimeline.Range.After(DateTime.now()),
    pageSize = 100
  )

  val tasks = List(
    todayTask,
    todayTask.copy(task_type = TaskType.ViewLinkedinProfile, task_data = TaskData.ViewLinkedinProfileData(task_type = TaskType.ViewLinkedinProfile)),
    todayTask.copy(task_type = TaskType.ViewLinkedinProfile, task_data = TaskData.ViewLinkedinProfileData(task_type = TaskType.ViewLinkedinProfile)),
    todayTask.copy(task_type = TaskType.SendSms, task_data = TaskData.SendSmsData(task_type = TaskType.SendSms, body = "hi")),
    todayTask.copy(task_type = TaskType.SendSms, task_data = TaskData.SendSmsData(task_type = TaskType.SendSms, body = "hi")),
    todayTask.copy(task_type = TaskType.SendSms, task_data = TaskData.SendSmsData(task_type = TaskType.SendSms, body = "hi")),
    todayTask.copy(task_type = TaskType.SendWhatsAppMessage, task_data = TaskData.SendWhatsAppMessageData(task_type = TaskType.SendWhatsAppMessage, body = "hi")),
    todayTask.copy(task_type = TaskType.SendWhatsAppMessage, task_data = TaskData.SendWhatsAppMessageData(task_type = TaskType.SendWhatsAppMessage, body = "hi")),
    todayTask.copy(task_type = TaskType.SendWhatsAppMessage, task_data = TaskData.SendWhatsAppMessageData(task_type = TaskType.SendWhatsAppMessage, body = "hi")),
    todayTask.copy(task_type = TaskType.SendWhatsAppMessage, task_data = TaskData.SendWhatsAppMessageData(task_type = TaskType.SendWhatsAppMessage, body = "hi")),
    todayTask.copy(task_type = TaskType.GeneralTask, task_data = TaskData.GeneralTaskData(task_type = TaskType.GeneralTask, task_notes = "hi")),
    todayTask.copy(task_type = TaskType.GeneralTask, task_data = TaskData.GeneralTaskData(task_type = TaskType.GeneralTask, task_notes = "hi")),
    todayTask.copy(task_type = TaskType.GeneralTask, task_data = TaskData.GeneralTaskData(task_type = TaskType.GeneralTask, task_notes = "hi")),
    todayTask.copy(task_type = TaskType.GeneralTask, task_data = TaskData.GeneralTaskData(task_type = TaskType.GeneralTask, task_notes = "hi")),
    todayTask.copy(task_type = TaskType.GeneralTask, task_data = TaskData.GeneralTaskData(task_type = TaskType.GeneralTask, task_notes = "hi")),
  )

  val taskSubCount = SubCount(
    all = tasks.length,
    email = tasks.count(t => t.task_type == TaskType.SendEmail),
    linkedin = tasks.count(t => t.task_type == TaskType.ViewLinkedinProfile),
    sms = tasks.count(t => t.task_type == TaskType.SendSms),
    whatsapp = tasks.count(t => t.task_type == TaskType.SendWhatsAppMessage),
    call = tasks.count(t => t.task_type == TaskType.CallTask),
    generic = tasks.count(t => t.task_type == TaskType.GeneralTask),
      approval_email = 0,
      approval_call = 0,
      approval_linkedin = 0,
      approval_sms = 0,
      approval_whatsapp = 0,
      approval_generic = 0
  )

  val zeroCount = SubCount(
    all = 0,
    email = 0,
    linkedin = 0,
    sms = 0,
    whatsapp = 0,
    call = 0,
    generic = 0,
      approval_email = 0,
      approval_call = 0,
      approval_linkedin = 0,
      approval_sms = 0,
      approval_whatsapp = 0,
      approval_generic = 0
  )

  val taskCount = TaskCount(
    todayAndDue = zeroCount,
    today = taskSubCount,
    upcoming = zeroCount,
    due = zeroCount,
    completed = zeroCount,
    skipped = zeroCount,
    snoozed = zeroCount,
    failed = zeroCount
  )


  val sortedTasks_asc = List(
    todayTask.copy(due_at = aDate.plusDays(1)),
    todayTask.copy(due_at = aDate.plusDays(2)),
    todayTask.copy(due_at = aDate.plusDays(3)),
    todayTask.copy(due_at = aDate.plusDays(4)),
    todayTask.copy(due_at = aDate.plusDays(5)),
    todayTask.copy(due_at = aDate.plusDays(6)),
    todayTask.copy(due_at = aDate.plusDays(7)),
    todayTask.copy(due_at = aDate.plusDays(8)),
    todayTask.copy(due_at = aDate.plusDays(9)),
    todayTask.copy(due_at = aDate.plusDays(10)),
    todayTask.copy(due_at = aDate.plusDays(11)),
    todayTask.copy(due_at = aDate.plusDays(12)),
    todayTask.copy(due_at = aDate.plusDays(13)),
    todayTask.copy(due_at = aDate.plusDays(14))
  )


  val sortedTask_asc_last_two_same = List(
    todayTask.copy(due_at = aDate.plusDays(1)),
    todayTask.copy(due_at = aDate.plusDays(2)),
    todayTask.copy(due_at = aDate.plusDays(3)),
    todayTask.copy(due_at = aDate.plusDays(4)),
    todayTask.copy(due_at = aDate.plusDays(4)),
  )

  val sortedTask_desc_last_two_same = List(
    todayTask.copy(due_at = aDate.plusDays(4)),
    todayTask.copy(due_at = aDate.plusDays(3)),
    todayTask.copy(due_at = aDate.plusDays(2)),
    todayTask.copy(due_at = aDate.plusDays(1)),
    todayTask.copy(due_at = aDate.plusDays(1)),
  )

  val atSameTimeTasks = List(
    todayTask.copy(due_at = aDate.plusDays(4)),
    todayTask.copy(due_at = aDate.plusDays(4)),
    todayTask.copy(due_at = aDate.plusDays(4)),
    todayTask.copy(due_at = aDate.plusDays(4))
  )

  val sortedTask_desc: List[Task] = sortedTasks_asc.reverse


  val taskPagination = TaskPagination(
    data = sortedTasks_asc.dropRight(1),
    links = NavigationLinks(
      prev = Some(sortedTasks_asc.head.due_at),
      next = Some(sortedTasks_asc.dropRight(1).last.due_at)
    )
  )

  describe("testing taskService.computeExactlyAt") {

    it("if taskSize < pageSize it should return None") {

      val res = taskService.computeExactlyAt(
        validatedTaskReq = validatedTaskReq,
        page_size = 20,
        tasks = listOfTasks
      )

      assert(res.isEmpty)

    }


    it("should return date if last two tasks have same due_at ") {

      val res = taskService.computeExactlyAt(
        validatedTaskReq = ValidatedTaskReq.ValidatedTaskReqRange(
          timeline = InferredQueryTimeline.Range.Before(DateTime.now()),
          pageSize = 100
        ),
        page_size = 2,
        tasks = lastTwoTasksWithSameDueDate
      )

      assert(res.contains(ExactIdToCompareTime("Due Task")))
    }

    /*  Test Above
        aDate = ['2022-10-12']
        tasks = ['2022-10-13','2022-10-12','2022-10-11','2022-10-10','2022-10-10']
        returns:['2022-10-10]
        */

    it("should return similar date if first two tasks have same due_at") {

      val res = taskService.computeExactlyAt(
        validatedTaskReq = validatedTaskReq,
        page_size = 2,
        tasks = lastTwoTasksWithSameDueDate.reverse // ['2022-10-10','2022-10-10','2022-10-11','2022-10-12','2022-10-13']
      )

      assert(res.contains(ExactIdToCompareTime("Due Task")))

    }

    /*      Test Above
            aDate = ['2022-10-12']
            tasks = ['2022-10-10','2022-10-10','2022-10-11','2022-10-12','2022-10-13']
            returns:['2022-10-10]
    */

    it("should return None if all tasks have distinct due_at") {

      val res = taskService.computeExactlyAt(
        validatedTaskReq = validatedTaskReq,
        page_size = 2,
        tasks = List(
          todayTask.copy(due_at = aDate.plusDays(1)), // 2022-10-13
          todayTask.copy(due_at = aDate.plusDays(2)), // 2022-10-14
          todayTask.copy(due_at = aDate.plusDays(3)),
          todayTask.copy(due_at = aDate.plusDays(4)),
          todayTask.copy(due_at = aDate.plusDays(5)), // 2022-10-17

        )
      )

      assert(res.isEmpty)

    }
    /*       Test Above
                aDate  :  ['2022-10-12']
                tasks  : ['2022-10-13','2022-10-14','2022-10-15','2022-10-16','2022-10-17']
                returns: None
    */

  }

  val permittedAccountIds = Seq(11L, 12L, 2L, 3L)
  val accountId = AccountId(11L)

  val changeStatusPermissionCheck = ChangeStatusPermissionCheck.ManualTaskCheck(
    doer = None,
    permittedAccountIds = permittedAccountIds
  )

  val exactlyIdToCompare = ExactIdToCompareTime(id = todayTask.task_id)

  describe("testing taskService.getAllTasksForUser") {
    it("should return Left(GetAllTaskForUserError.ErrorWhileGettingTasks because we failed the db call )") {

      (taskDaoService.getAllTaskForUserV2(
        _: Long,
        _: OrgId,
        _: String,
        _: ValidatedTaskReq,
        _: Option[List[ReplySentimentUuid]],
        _: Option[List[Long]],
        _: Option[List[CampaignId]],
        _: Option[List[String]],
        _: Option[List[String]],
        _: Option[List[String]],
        _: Option[TimeBasedTaskType],
        _: Boolean,
        _: Seq[Long],
//        _: Boolean
      )
      (_: ExecutionContext,
        _: SRLogger))
        .expects(team_id_9, *, *, *, None, None, None, None, None, *, *, false, permittedAccountIds, *, *)
        .returning(Future.successful(Left(GetAllTaskForUserError.ErrorWhileGettingTasks)))

//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(_: TeamId, _: SrRollingUpdateFeature)(_: SRLogger))
//        .expects(TeamId(team_id_9), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .returning(false)

      val res = taskService.getAllTasksForUser(
        isFirst = false,
        team_id = team_id_9,
        orgId = org_id_9,
        validatedTaskReq = ValidatedTaskReq.ValidatedTaskReqRange(
          timeline = InferredQueryTimeline.Range.After(DateTime.now()),
          pageSize = 100
        ),
        searchTask = searchTask,
        timeZone = "UTC",
        doNotFetchAutomatedDueTasks = false,
        permittedAccountIds = permittedAccountIds
      )

      res.map(result => {
        assert(result == Left(GetAllTaskForUserError.ErrorWhileGettingTasks))
      })
    }

    it("should return prev only as sortedTasks.length < pageSize + 1 and timeline is InferredQueryTimeline.Range.Before") {

      (taskDaoService.getAllTaskForUserV2(
        _: Long,
        _: OrgId,
        _: String,
        _: ValidatedTaskReq,
        _: Option[List[ReplySentimentUuid]],
        _: Option[List[Long]],
        _: Option[List[CampaignId]],
        _: Option[List[String]],
        _: Option[List[String]],
        _: Option[List[String]],
        _: Option[TimeBasedTaskType],
        _: Boolean,
        _: Seq[Long],
//        _: Boolean
      )
      (_: ExecutionContext,
        _: SRLogger))
        .expects(team_id_9, *, *, *, None, None, None, None, None, *, *, false, permittedAccountIds, *, *)
        .returning(Future.successful(Right(sortedTask_desc)))

//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(_: TeamId, _: SrRollingUpdateFeature)(_: SRLogger))
//        .expects(TeamId(team_id_9), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .returning(false)

      val res = taskService.getAllTasksForUser(
        isFirst = false,
        team_id = team_id_9,
        orgId = org_id_9,
        validatedTaskReq = ValidatedTaskReq.ValidatedTaskReqRange(
          timeline = InferredQueryTimeline.Range.Before(aDate),
          pageSize = 20
        ),
        searchTask = searchTask,
        timeZone = "UTC",
        doNotFetchAutomatedDueTasks = false,
        permittedAccountIds = permittedAccountIds
      )

      val prev = Some(sortedTask_desc.head.due_at) // it will be used as newer_than

      val expectedResponse = TaskPagination(
        data = sortedTask_desc,
        links = NavigationLinks(
          prev = prev,
          next = None
        )
      )

      res.map { result => assert(result == Right(expectedResponse)) }

    }

    // Above Test
    // Task Size < Page Size ThereFore Directly giving TaskPagination Response
    // isFirst is false so prev is present

    it("should return taskPagination without prev/next as isFirst is true  and only as sortedTasks.length < pageSize + 1 and timeline is InferredQueryTimeline.Range.Before") {

      (taskDaoService.getAllTaskForUserV2(
        _: Long,
        _: OrgId,
        _: String,
        _: ValidatedTaskReq,
        _: Option[List[ReplySentimentUuid]],
        _: Option[List[Long]],
        _: Option[List[CampaignId]],
        _: Option[List[String]],
        _: Option[List[String]],
        _: Option[List[String]],
        _: Option[TimeBasedTaskType],
        _: Boolean,
        _: Seq[Long],
//        _: Boolean
      )
      (_: ExecutionContext,
        _: SRLogger))
        .expects(team_id_9, *, *, *, None, None, None, None, None, *, *, false, permittedAccountIds, *, *)
        .returning(Future.successful(Right(sortedTask_desc)))

//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(_: TeamId, _: SrRollingUpdateFeature)(_: SRLogger))
//        .expects(TeamId(team_id_9), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .returning(false)

      val res = taskService.getAllTasksForUser(
        isFirst = true,
        team_id = team_id_9,
        orgId = org_id_9,
        validatedTaskReq = ValidatedTaskReq.ValidatedTaskReqRange(
          timeline = InferredQueryTimeline.Range.Before(aDate),
          pageSize = 20
        ),
        searchTask = searchTask,
        timeZone = "UTC",
        doNotFetchAutomatedDueTasks = false,
        permittedAccountIds = permittedAccountIds
      )

      val expectedResponse = TaskPagination(
        data = sortedTask_desc,
        links = NavigationLinks(
          prev = None,
          next = None
        )
      )

      res.map { result => assert(result == Right(expectedResponse)) }

    }

    // Above Test
    // timeline queried for before dateTime so returning descending tasks
    // Task Size < Page Size ThereFore Directly giving TaskPagination Response
    // isFirst is true so prev and next is None

    it("should return next only as sortedTasks.length < pageSize + 1 and timeline is InferredQueryTimeline.Range.After") {

      (taskDaoService.getAllTaskForUserV2(
        _: Long,
        _: OrgId,
        _: String,
        _: ValidatedTaskReq,
        _: Option[List[ReplySentimentUuid]],
        _: Option[List[Long]],
        _: Option[List[CampaignId]],
        _: Option[List[String]],
        _: Option[List[String]],
        _: Option[List[String]],
        _: Option[TimeBasedTaskType],
        _: Boolean,
        _: Seq[Long],
//        _: Boolean
      )
      (_: ExecutionContext,
        _: SRLogger))
        .expects(team_id_9, *, *, *, None, None, None, None, None, *, *, false, permittedAccountIds, *, *)
        .returning(Future.successful(Right(sortedTasks_asc)))


//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(_: TeamId, _: SrRollingUpdateFeature)(_: SRLogger))
//        .expects(TeamId(team_id_9), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .returning(false)

      val res = taskService.getAllTasksForUser(
        isFirst = false,
        team_id = team_id_9,
        orgId = org_id_9,
        validatedTaskReq = ValidatedTaskReq.ValidatedTaskReqRange(
          timeline = InferredQueryTimeline.Range.After(aDate),
          pageSize = 20 // important
        ),
        searchTask = searchTask,
        timeZone = "UTC",
        doNotFetchAutomatedDueTasks = false,
        permittedAccountIds = permittedAccountIds
      )

      val next = Some(sortedTasks_asc.head.due_at) // it will be used as older_than

      val expectedResponse = TaskPagination(
        data = sortedTasks_asc.reverse, // doing reverse because result will be reversed in function logic on line
        links = NavigationLinks(
          prev = None,
          next = next
        )
      )

      res.map { result => assert(result == Right(expectedResponse)) }

    }

    // Above Test
    // timeline queried for After dateTime so returning ascending tasks
    // ascendingTasks Size(14) < Page Size(20) ThereFore Directly giving TaskPagination Response
    // isFirst is true so prev and next is None

    it("should return empty TaskPagination as dao returning empty tasks") {

      (taskDaoService.getAllTaskForUserV2(
        _: Long,
        _: OrgId,
        _: String,
        _: ValidatedTaskReq,
        _: Option[List[ReplySentimentUuid]],
        _: Option[List[Long]],
        _: Option[List[CampaignId]],
        _: Option[List[String]],
        _: Option[List[String]],
        _: Option[List[String]],
        _: Option[TimeBasedTaskType],
        _: Boolean,
        _: Seq[Long],
//        _: Boolean
      )
      (_: ExecutionContext,
        _: SRLogger))
        .expects(team_id_9, *, *, *, None, None, None, None, None, *, *, false, permittedAccountIds, *, *)
        .returning(Future.successful(Right(List())))


//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(_: TeamId, _: SrRollingUpdateFeature)(_: SRLogger))
//        .expects(TeamId(team_id_9), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .returning(false)

      val res = taskService.getAllTasksForUser(
        isFirst = false,
        team_id = team_id_9,
        orgId = org_id_9,
        validatedTaskReq = ValidatedTaskReq.ValidatedTaskReqRange(
          timeline = InferredQueryTimeline.Range.After(aDate),
          pageSize = 20
        ),
        searchTask = searchTask,
        timeZone = "UTC",
        doNotFetchAutomatedDueTasks = false,
        permittedAccountIds = permittedAccountIds
      )

      val expectedResponse = TaskPagination(
        data = List(),
        links = NavigationLinks(
          prev = None,
          next = None
        )
      )

      res.map { result => assert(result == Right(expectedResponse)) }

    }

    // Above Test
    // tasks found is zero in this case so returning data = List()

    it("should go to else part as tasksFound >= limit + 1 is true then return TaskPagination from InferredQueryTimeline.Range.After part in getTaskResponse atSameTaskType is None") {

      (taskDaoService.getAllTaskForUserV2(
        _: Long,
        _: OrgId,
        _: String,
        _: ValidatedTaskReq,
        _: Option[List[ReplySentimentUuid]],
        _: Option[List[Long]],
        _: Option[List[CampaignId]],
        _: Option[List[String]],
        _: Option[List[String]],
        _: Option[List[String]],
        _: Option[TimeBasedTaskType],
        _: Boolean,
        _: Seq[Long],
//        _: Boolean
      )
      (_: ExecutionContext,
        _: SRLogger))
        .expects(team_id_9, *, *, *, None, None, None, None, None, *, *, false, permittedAccountIds, *, *)
        .returning(Future.successful(Right(sortedTasks_asc)))

//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(_: TeamId, _: SrRollingUpdateFeature)(_: SRLogger))
//        .expects(TeamId(team_id_9), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .twice()
//        .returning(false)
      val res = taskService.getAllTasksForUser(
        isFirst = false,
        team_id = team_id_9,
        orgId = org_id_9,
        validatedTaskReq = ValidatedTaskReq.ValidatedTaskReqRange(
          timeline = InferredQueryTimeline.Range.After(aDate),
          pageSize = 13
        ),
        searchTask = searchTask,
        timeZone = "UTC",
        doNotFetchAutomatedDueTasks = false,
        permittedAccountIds = permittedAccountIds
      )

      val final_tasks = sortedTasks_asc.reverse.drop(1)
      val prev = Some(final_tasks.head.due_at) // used to find newer_than this date
      val next = Some(final_tasks.last.due_at) // used to find older_than this date

      val expectedResponse = TaskPagination(
        data = final_tasks,
        links = NavigationLinks(
          prev = prev,
          next = next
        )
      )

      res.map { result => assert(result == Right(expectedResponse)) }

    }

    // Above Test
    // timeline queried for After dateTime so returning ascending tasks
    // ascendingTasks Size(14) >= Page Size(13) ThereFore checking for same time tasks
    // But all tasks are unique so returning TaskPagination isFirst is false so prev and next wil be present

    it("should go to else part as tasksFound >= limit + 1 then return TaskPagination without prev as isFirst is true from InferredQueryTimeline.Range.After part in getTaskResponse atSameTaskType is None") {

      (taskDaoService.getAllTaskForUserV2(
        _: Long,
        _: OrgId,
        _: String,
        _: ValidatedTaskReq,
        _: Option[List[ReplySentimentUuid]],
        _: Option[List[Long]],
        _: Option[List[CampaignId]],
        _: Option[List[String]],
        _: Option[List[String]],
        _: Option[List[String]],
        _: Option[TimeBasedTaskType],
        _: Boolean,
        _: Seq[Long],
//        _: Boolean
      )
      (_: ExecutionContext,
        _: SRLogger))
        .expects(team_id_9, *, *, *, None, None, None, None, None, *, *, false, permittedAccountIds, *, *)
        .returning(Future.successful(Right(sortedTasks_asc)))


//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(_: TeamId, _: SrRollingUpdateFeature)(_: SRLogger))
//        .expects(TeamId(team_id_9), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .twice()
//        .returning(false)

      val res = taskService.getAllTasksForUser(
        isFirst = true,
        team_id = team_id_9,
        orgId = org_id_9,
        validatedTaskReq = ValidatedTaskReq.ValidatedTaskReqRange(
          timeline = InferredQueryTimeline.Range.After(aDate),
          pageSize = 13
        ),
        searchTask = searchTask,
        timeZone = "UTC",
        doNotFetchAutomatedDueTasks = false,
        permittedAccountIds = permittedAccountIds
      )

      val final_tasks = sortedTasks_asc.reverse.drop(1)
      //      val prev = Some(final_tasks.head.due_at) // used to find newer_than this date
      val next = Some(final_tasks.last.due_at) // used to find older_than this date

      val expectedResponse = TaskPagination(
        data = final_tasks,
        links = NavigationLinks(
          prev = None,
          next = next
        )
      )

      res.map { result => assert(result == Right(expectedResponse)) }

    }

    // Above Test
    // timeline queried for Before dateTime so returning ascending tasks
    // ascendingTasks Size(14) >= Page Size(13) ThereFore checking for same time tasks
    // But all tasks are unique so returning TaskPagination isFirst is true so prev will be null and next wil be present

    it("should go to else part as tasksFound >= limit + 1 is true then return TaskPagination from InferredQueryTimeline.Range.Before part in getTaskResponse atSameTaskType is None and should return prev and next as isFirstTask is false") {

      (taskDaoService.getAllTaskForUserV2(
        _: Long,
        _: OrgId,
        _: String,
        _: ValidatedTaskReq,
        _: Option[List[ReplySentimentUuid]],
        _: Option[List[Long]],
        _: Option[List[CampaignId]],
        _: Option[List[String]],
        _: Option[List[String]],
        _: Option[List[String]],
        _: Option[TimeBasedTaskType],
        _: Boolean,
        _: Seq[Long],
//        _: Boolean
      )
      (_: ExecutionContext,
        _: SRLogger))
        .expects(team_id_9, *, *, *, None, None, None, None, None, *, *, false, permittedAccountIds, *, *)
        .returning(Future.successful(Right(sortedTask_desc)))


//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(_: TeamId, _: SrRollingUpdateFeature)(_: SRLogger))
//        .expects(TeamId(team_id_9), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .twice()
//        .returning(false)

      val res = taskService.getAllTasksForUser(
        isFirst = false,
        team_id = team_id_9,
        orgId = org_id_9,
        validatedTaskReq = ValidatedTaskReq.ValidatedTaskReqRange(
          timeline = InferredQueryTimeline.Range.Before(aDate),
          pageSize = 13
        ),
        searchTask = searchTask,
        timeZone = "UTC",
        doNotFetchAutomatedDueTasks = false,
        permittedAccountIds = permittedAccountIds
      )

      val final_tasks = sortedTask_desc.dropRight(1)
      val prev = Some(final_tasks.head.due_at) // used to find newer_than this date
      val next = Some(final_tasks.last.due_at) // used to find older_than this date

      val expectedResponse = TaskPagination(
        data = final_tasks,
        links = NavigationLinks(
          prev = prev,
          next = next
        )
      )

      res.map { result => assert(result == Right(expectedResponse)) }

    }

    // Above Test
    // timeline queried for Before dateTime so returning descending tasks
    // descendingTasks Size(14) >= Page Size(13) ThereFore checking for same time tasks
    // But all tasks are unique so returning TaskPagination isFirst is false so prev will be null and next wil be present

    it("should go to else part as tasksFound >= limit + 1 is true then return TaskPagination from InferredQueryTimeline.Range.Before part in getTaskResponse atSameTaskType is None and isFirst True") {

      (taskDaoService.getAllTaskForUserV2(
        _: Long,
        _: OrgId,
        _: String,
        _: ValidatedTaskReq,
        _: Option[List[ReplySentimentUuid]],
        _: Option[List[Long]],
        _: Option[List[CampaignId]],
        _: Option[List[String]],
        _: Option[List[String]],
        _: Option[List[String]],
        _: Option[TimeBasedTaskType],
        _: Boolean,
        _: Seq[Long],
//        _: Boolean
      )
      (_: ExecutionContext,
        _: SRLogger))
        .expects(team_id_9, *, *, *, None, None, None, None, None, *, *, false, permittedAccountIds, *, *)
        .returning(Future.successful(Right(sortedTask_desc)))

//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(_: TeamId, _: SrRollingUpdateFeature)(_: SRLogger))
//        .expects(TeamId(team_id_9), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .twice()
//        .returning(false)

      val res = taskService.getAllTasksForUser(
        isFirst = true,
        team_id = team_id_9,
        orgId = org_id_9,
        validatedTaskReq = ValidatedTaskReq.ValidatedTaskReqRange(
          timeline = InferredQueryTimeline.Range.Before(aDate),
          pageSize = 13
        ),
        searchTask = searchTask,
        timeZone = "UTC",
        doNotFetchAutomatedDueTasks = false,
        permittedAccountIds = permittedAccountIds
      )

      val final_tasks = sortedTask_desc.dropRight(1)
      //      val prev = Some(final_tasks.head.due_at) // used to find newer_than this date
      val next = Some(final_tasks.last.due_at) // used to find older_than this date

      val expectedResponse = TaskPagination(
        data = final_tasks,
        links = NavigationLinks(
          prev = None,
          next = next
        )
      )

      res.map { result => assert(result == Right(expectedResponse)) }

    }

    // Above Test
    // timeline queried for Before dateTime so returning descending tasks
    // descendingTasks Size(14) >= Page Size(13) ThereFore checking for same time tasks
    // But all tasks are unique so returning TaskPagination isFirst is true so prev will be null and next wil be present

    it("should go to else part as tasksFound >= limit + 1 is true then return TaskPagination from InferredQueryTimeline.Range.After part in getTaskResponse atSameTaskType is Present and should return prev and next as isFirstTask is false") {

      (taskDaoService.getAllTaskForUserV2(
        _: Long,
        _: OrgId,
        _: String,
        _: ValidatedTaskReq,
        _: Option[List[ReplySentimentUuid]],
        _: Option[List[Long]],
        _: Option[List[CampaignId]],
        _: Option[List[String]],
        _: Option[List[String]],
        _: Option[List[String]],
        _: Option[TimeBasedTaskType],
        _: Boolean,
        _: Seq[Long],
//        _: Boolean
      )
      (_: ExecutionContext,
        _: SRLogger))
        .expects(team_id_9, *, *, *, None, None, None, None, None, *, *, false, permittedAccountIds, *, *)
        .returning(Future.successful(Right(sortedTask_asc_last_two_same)))

//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(_: TeamId, _: SrRollingUpdateFeature)(_: SRLogger))
//        .expects(TeamId(team_id_9), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .twice()
//        .returning(false)

      (taskDaoService.getAtSameTimeTasksFromDB(
        _: Long,
        _: OrgId,
        _: ExactIdToCompareTime,
        _: String,
        _: Option[List[Long]],
        _: Option[List[CampaignId]],
        _: Option[List[ReplySentimentUuid]],
        _: Option[List[String]],
        _: Option[List[String]],
        _: Option[List[String]],
        _: Option[TimeBasedTaskType],
        _: Boolean,
        _: Seq[Long],
//        _: Boolean
      )(_: ExecutionContext,
        _: SRLogger))
        .expects(team_id_9, *, *, *, None, None, None, None, None, *, *, false, permittedAccountIds, *, *)
        .returning(Future.successful(Right(atSameTimeTasks)))

      val res = taskService.getAllTasksForUser(
        isFirst = false,
        team_id = team_id_9,
        orgId = org_id_9,
        validatedTaskReq = ValidatedTaskReq.ValidatedTaskReqRange(
          timeline = InferredQueryTimeline.Range.After(aDate),
          pageSize = 3
        ),
        searchTask = searchTask,
        timeZone = "UTC",
        doNotFetchAutomatedDueTasks = false,
        permittedAccountIds = permittedAccountIds
      )

      val first_task = sortedTask_asc_last_two_same.reverse.head
      val final_tasks = atSameTimeTasks ++ sortedTask_asc_last_two_same.reverse.filterNot { case task => task.due_at == first_task.due_at }
      val prev = Some(final_tasks.head.due_at) // used to find newer_than this date
      val next = Some(final_tasks.last.due_at) // used to find older_than this date

      val expectedResponse = TaskPagination(
        data = final_tasks,
        links = NavigationLinks(
          prev = prev,
          next = next
        )
      )

      res.map { result => assert(result == Right(expectedResponse)) }

    }

    // Above Test

    // aDate = ['2022-10-12']

    // sortedTask_desc_first_two_same = List(
    //    todayTask.copy(due_at = aDate.plusDays(1)),
    //    todayTask.copy(due_at = aDate.plusDays(2)),
    //    todayTask.copy(due_at = aDate.plusDays(3)),
    //    todayTask.copy(due_at = aDate.plusDays(4)),
    //    todayTask.copy(due_at = aDate.plusDays(4)),
    //  ).reverse

    // atSameTimeTask = List(
    //    todayTask.copy(due_at = aDate.plusDays(4)),
    //    todayTask.copy(due_at = aDate.plusDays(4)),
    //    todayTask.copy(due_at = aDate.plusDays(4)),
    //    todayTask.copy(due_at = aDate.plusDays(4))
    //  )

    //     expected Task = List(
    //     todayTask.copy(due_at = aDate.plusDays(1)),
    //     todayTask.copy(due_at = aDate.plusDays(2)),
    //     todayTask.copy(due_at = aDate.plusDays(3)),
    //     todayTask.copy(due_at = aDate.plusDays(4)),
    //     todayTask.copy(due_at = aDate.plusDays(4)),
    //     todayTask.copy(due_at = aDate.plusDays(4)),
    //     todayTask.copy(due_at = aDate.plusDays(4))
    //    )

    it("should go to else part as tasksFound >= limit + 1 is true then return TaskPagination from InferredQueryTimeline.Range.Before part in getTaskResponse atSameTaskType is Present and should return prev and next as isFirstTask is false") {

      val atSameTaskType2 = List(
        todayTask.copy(due_at = aDate.plusDays(1)),
        todayTask.copy(due_at = aDate.plusDays(1)),
        todayTask.copy(due_at = aDate.plusDays(1)),
        todayTask.copy(due_at = aDate.plusDays(1))
      )

      (taskDaoService.getAllTaskForUserV2(
        _: Long,
        _: OrgId,
        _: String,
        _: ValidatedTaskReq,
        _: Option[List[ReplySentimentUuid]],
        _: Option[List[Long]],
        _: Option[List[CampaignId]],
        _: Option[List[String]],
        _: Option[List[String]],
        _: Option[List[String]],
        _: Option[TimeBasedTaskType],
        _: Boolean,
        _: Seq[Long],
//        _: Boolean
      )
      (_: ExecutionContext,
        _: SRLogger))
        .expects(team_id_9, *, *, *, None, None, None, None, None, *, *, false, permittedAccountIds, *, *)
        .returning(Future.successful(Right(sortedTask_desc_last_two_same)))


//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(_: TeamId, _: SrRollingUpdateFeature)(_: SRLogger))
//        .expects(TeamId(team_id_9), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .twice()
//        .returning(false)

      (taskDaoService.getAtSameTimeTasksFromDB(
        _: Long,
        _: OrgId,
        _: ExactIdToCompareTime,
        _: String,
        _: Option[List[Long]],
        _: Option[List[CampaignId]],
        _: Option[List[ReplySentimentUuid]],
        _: Option[List[String]],
        _: Option[List[String]],
        _: Option[List[String]],
        _: Option[TimeBasedTaskType],
        _: Boolean,
        _: Seq[Long],
//        _: Boolean
      )(_: ExecutionContext,
        _: SRLogger))
        .expects(team_id_9, *, *, *, None, None, None, None, None, *, *, false, permittedAccountIds, *, *)
        .returning(Future.successful(Right(atSameTaskType2)))

      val res = taskService.getAllTasksForUser(
        isFirst = false,
        team_id = team_id_9,
        orgId = org_id_9,
        validatedTaskReq = ValidatedTaskReq.ValidatedTaskReqRange(
          timeline = InferredQueryTimeline.Range.Before(aDate),
          pageSize = 4
        ),
        searchTask = searchTask,
        timeZone = "UTC",
        doNotFetchAutomatedDueTasks = false,
        permittedAccountIds = permittedAccountIds
      )

      val lastTask = sortedTask_desc_last_two_same.last
      val final_tasks = sortedTask_desc_last_two_same.filterNot { case task => task.due_at == lastTask.due_at } ++ atSameTaskType2
      val prev = Some(final_tasks.head.due_at) // used to find newer_than this date
      val next = Some(final_tasks.last.due_at) // used to find older_than this date

      val expectedResponse = TaskPagination(
        data = final_tasks,
        links = NavigationLinks(
          prev = prev,
          next = next
        )
      )

      res.map { result => assert(result == Right(expectedResponse)) }

    }

    //Above Test
    /*

    aDate = ['2022-10-12']

    sortedTask_desc_last_two_same = List(
        todayTask.copy(due_at = aDate.plusDays(4)),
        todayTask.copy(due_at = aDate.plusDays(3)),
        todayTask.copy(due_at = aDate.plusDays(2)),
        todayTask.copy(due_at = aDate.plusDays(1)),   // Same due_at tasks are present
        todayTask.copy(due_at = aDate.plusDays(1)),   // Same due_at tasks are present
      )

    fetched same time tasks

    val atSameTaskType2= List(
      todayTask.copy(due_at = aDate.plusDays(1)),
      todayTask.copy(due_at = aDate.plusDays(1)),
      todayTask.copy(due_at = aDate.plusDays(1)),
      todayTask.copy(due_at = aDate.plusDays(1))
    )

    expected tasks =

    List(
            todayTask.copy(due_at = aDate.plusDays(4)),
            todayTask.copy(due_at = aDate.plusDays(3)),
            todayTask.copy(due_at = aDate.plusDays(2)),
            todayTask.copy(due_at = aDate.plusDays(1)),
            todayTask.copy(due_at = aDate.plusDays(1)),
            todayTask.copy(due_at = aDate.plusDays(1)),
            todayTask.copy(due_at = aDate.plusDays(1)),
          )

    */


    /* ---------------------------------- Description ------------------------------------*/
    /* atSameTaskType Returning Error so not proceeding ahead with it, and returning error then only*/

    it("should go to else part as tasksFound >= limit + 1 then return Left(GetAllTaskForUserError.ErrorWhileGettingTasks) as atSameTaskType Will be giving Left Error") {

      (taskDaoService.getAllTaskForUserV2(
        _: Long,
        _: OrgId,
        _: String,
        _: ValidatedTaskReq,
        _: Option[List[ReplySentimentUuid]],
        _: Option[List[Long]],
        _: Option[List[CampaignId]],
        _: Option[List[String]],
        _: Option[List[String]],
        _: Option[List[String]],
        _: Option[TimeBasedTaskType],
        _: Boolean,
        _: Seq[Long],
//        _: Boolean
      )
      (_: ExecutionContext,
        _: SRLogger))
        .expects(team_id_9, *, *, *, None, None, None, None, None, *, *, false, permittedAccountIds, *, *)
        .returning(Future.successful(Right(sortedTask_desc_last_two_same)))


//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(_: TeamId, _: SrRollingUpdateFeature)(_: SRLogger))
//        .expects(TeamId(team_id_9), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .twice()
//        .returning(false)

      (taskDaoService.getAtSameTimeTasksFromDB(
        _: Long,
        _: OrgId,
        _: ExactIdToCompareTime,
        _: String,
        _: Option[List[Long]],
        _: Option[List[CampaignId]],
        _: Option[List[ReplySentimentUuid]],
        _: Option[List[String]],
        _: Option[List[String]],
        _: Option[List[String]],
        _: Option[TimeBasedTaskType],
        _: Boolean,
        _: Seq[Long],
//        _: Boolean
      )(_: ExecutionContext,
        _: SRLogger))
        .expects(team_id_9, *, *, *, None, None, None, None, None, *, *, false, permittedAccountIds, *, *)
        .returning(Future.successful(Left(GetAllTaskForUserError.ErrorWhileGettingTasks)))

      val res = taskService.getAllTasksForUser(
        isFirst = false,
        team_id = team_id_9,
        orgId = org_id_9,
        validatedTaskReq = ValidatedTaskReq.ValidatedTaskReqRange(
          timeline = InferredQueryTimeline.Range.Before(aDate),
          pageSize = 4
        ),
        searchTask = searchTask,
        timeZone = "UTC",
        doNotFetchAutomatedDueTasks = false,
        permittedAccountIds = permittedAccountIds
      )

      val expectedResponse = Left(GetAllTaskForUserError.ErrorWhileGettingTasks)

      res.map { result => assert(result == expectedResponse) }

    }

    it("should go to else part as tasksFound >= limit + 1 then Fail as taskDaoService.getAtSameTimeTasksFromDB Will be failing") {

      (taskDaoService.getAllTaskForUserV2(
        _: Long,
        _: OrgId,
        _: String,
        _: ValidatedTaskReq,
        _: Option[List[ReplySentimentUuid]],
        _: Option[List[Long]],
        _: Option[List[CampaignId]],
        _: Option[List[String]],
        _: Option[List[String]],
        _: Option[List[String]],
        _: Option[TimeBasedTaskType],
        _: Boolean,
        _: Seq[Long],
//        _: Boolean
      )
      (_: ExecutionContext,
        _: SRLogger))
        .expects(team_id_9, *, *, *, None, None, None, None, None, *, *, false, permittedAccountIds, *, *)
        .returning(Future.successful(Right(sortedTask_desc_last_two_same)))


//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(_: TeamId, _: SrRollingUpdateFeature)(_: SRLogger))
//        .expects(TeamId(team_id_9), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .twice()
//        .returning(false)

      (taskDaoService.getAtSameTimeTasksFromDB(
        _: Long,
        _: OrgId,
        _: ExactIdToCompareTime,
        _: String,
        _: Option[List[Long]],
        _: Option[List[CampaignId]],
        _: Option[List[ReplySentimentUuid]],
        _: Option[List[String]],
        _: Option[List[String]],
        _: Option[List[String]],
        _: Option[TimeBasedTaskType],
        _: Boolean,
        _: Seq[Long],
//        _: Boolean
      )(_: ExecutionContext,
        _: SRLogger))
        .expects(team_id_9, *, *, *, None, None, None, None, None, *, *, false, permittedAccountIds, *, *)
        .returning(Future.failed(error)) // Here failing the future.

      val res = taskService.getAllTasksForUser(
        isFirst = false,
        team_id = team_id_9,
        orgId = org_id_9,
        validatedTaskReq = ValidatedTaskReq.ValidatedTaskReqRange(
          timeline = InferredQueryTimeline.Range.Before(aDate),
          pageSize = 4
        ),
        searchTask = searchTask,
        timeZone = "UTC",
        doNotFetchAutomatedDueTasks = false,
        permittedAccountIds = permittedAccountIds
      )


      res.map { result => assert(result == Left(GetAllTaskForUserError.ServerError(error))) } // Now Future fail is passing as Future[Left]

    }


    it("should give done tasks time when finding tasks in done section") {

      val sortedTasks_due_tasks_over_limit_asc = List(
        todayTask.copy(due_at = aDate.plusDays(1)),
        todayTask.copy(due_at = aDate.plusDays(2)),
        todayTask.copy(due_at = aDate.plusDays(3)),
        todayTask.copy(due_at = aDate.plusDays(4)),
        todayTask.copy(due_at = aDate.plusDays(5)),
        todayTask.copy(due_at = aDate.plusDays(6)),
        todayTask.copy(due_at = aDate.plusDays(7)),
        todayTask.copy(due_at = aDate.plusDays(8)),
        todayTask.copy(due_at = aDate.plusDays(9)),
        todayTask.copy(due_at = aDate.plusDays(10)),
        todayTask.copy(due_at = aDate.plusDays(11)),
        todayTask.copy(due_at = aDate.plusDays(12)),
        todayTask.copy(due_at = aDate.plusDays(13)),
        todayTask.copy(due_at = aDate.plusDays(14)),
        todayTask.copy(due_at = aDate.plusDays(15)),
        todayTask.copy(due_at = aDate.plusDays(16)),
        todayTask.copy(due_at = aDate.plusDays(17)),
        todayTask.copy(due_at = aDate.plusDays(18)),
        todayTask.copy(due_at = aDate.plusDays(19)),
        todayTask.copy(due_at = aDate.plusDays(20)),
        todayTask.copy(due_at = aDate.plusDays(21)),
        todayTask.copy(due_at = aDate.plusDays(22)),
        todayTask.copy(due_at = aDate.plusDays(23)),
        todayTask.copy(due_at = aDate.plusDays(24)),
        todayTask.copy(due_at = aDate.plusDays(25)),
        todayTask.copy(due_at = aDate.plusDays(26))
      )

      val done_tasks = sortedTasks_due_tasks_over_limit_asc.zipWithIndex.map(task => task._1.copy(
        status = TaskStatus.Done(
          done_at = doneDate,
          done_by = Some(31L)
        )
      ))

//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(_: TeamId, _: SrRollingUpdateFeature)(_: SRLogger))
//        .expects(TeamId(team_id_9), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .twice()
//        .returning(false)

      (taskDaoService.getAllTaskForUserV2(
        _: Long,
        _: OrgId,
        _: String,
        _: ValidatedTaskReq,
        _: Option[List[ReplySentimentUuid]],
        _: Option[List[Long]],
        _: Option[List[CampaignId]],
        _: Option[List[String]],
        _: Option[List[String]],
        _: Option[List[String]],
        _: Option[TimeBasedTaskType],
        _: Boolean,
        _: Seq[Long],
//        _: Boolean
      )
      (_: ExecutionContext,
        _: SRLogger))
        .expects(team_id_9, *, "UTC", ValidatedTaskReq.ValidatedTaskReqRange(
          timeline = InferredQueryTimeline.Range.Before(aDate),
          pageSize = 21
        ), None, None, None, None, None, None, Some(TimeBasedTaskType.Completed), false, permittedAccountIds, *, *)
        .returning(Future.successful(Right(done_tasks)))

      (taskDaoService.getAtSameTimeTasksFromDB(
        _: Long,
        _: OrgId,
        _: ExactIdToCompareTime,
        _: String,
        _: Option[List[Long]],
        _: Option[List[CampaignId]],
        _: Option[List[ReplySentimentUuid]],
        _: Option[List[String]],
        _: Option[List[String]],
        _: Option[List[String]],
        _: Option[TimeBasedTaskType],
        _: Boolean,
        _: Seq[Long],
//        _: Boolean
      )(_: ExecutionContext,
        _: SRLogger))
        .expects(team_id_9, *, exactlyIdToCompare, "UTC", None, None, None, None, None, None, Some(TimeBasedTaskType.Completed), false, permittedAccountIds, *, *)
        .returning(Future.successful(Right(done_tasks)))

      val res = taskService.getAllTasksForUser(
        isFirst = false,
        team_id = team_id_9,
        orgId = org_id_9,
        validatedTaskReq = ValidatedTaskReq.ValidatedTaskReqRange(
          timeline = InferredQueryTimeline.Range.Before(aDate),
          pageSize = 20
        ),
        searchTask = searchTask.copy(time_based_task_type = Some(TimeBasedTaskType.Completed)),
        timeZone = "UTC",
        doNotFetchAutomatedDueTasks = false,
        permittedAccountIds = permittedAccountIds
      )


      res.map { result => {
        result match {

          case Left(e) =>

            //            println(s"\n\nerror :: ${e}\n\n")

            assert(result == Left(GetAllTaskForUserError.ServerError(error)))


          case Right(d) =>

            //            println(s"\n\ncheck the links :: ${d.links}\n\n")

            assert(d.links.next.get == doneDate)

        }
      }
      }
    }

    it("should give skipped tasks time when finding tasks in skipped section") {

      val sortedTasks_due_tasks_over_limit_asc = List(
        todayTask.copy(due_at = aDate.plusDays(1)),
        todayTask.copy(due_at = aDate.plusDays(2)),
        todayTask.copy(due_at = aDate.plusDays(3)),
        todayTask.copy(due_at = aDate.plusDays(4)),
        todayTask.copy(due_at = aDate.plusDays(5)),
        todayTask.copy(due_at = aDate.plusDays(6)),
        todayTask.copy(due_at = aDate.plusDays(7)),
        todayTask.copy(due_at = aDate.plusDays(8)),
        todayTask.copy(due_at = aDate.plusDays(9)),
        todayTask.copy(due_at = aDate.plusDays(10)),
        todayTask.copy(due_at = aDate.plusDays(11)),
        todayTask.copy(due_at = aDate.plusDays(12)),
        todayTask.copy(due_at = aDate.plusDays(13)),
        todayTask.copy(due_at = aDate.plusDays(14)),
        todayTask.copy(due_at = aDate.plusDays(15)),
        todayTask.copy(due_at = aDate.plusDays(16)),
        todayTask.copy(due_at = aDate.plusDays(17)),
        todayTask.copy(due_at = aDate.plusDays(18)),
        todayTask.copy(due_at = aDate.plusDays(19)),
        todayTask.copy(due_at = aDate.plusDays(20)),
        todayTask.copy(due_at = aDate.plusDays(21)),
        todayTask.copy(due_at = aDate.plusDays(22)),
        todayTask.copy(due_at = aDate.plusDays(23)),
        todayTask.copy(due_at = aDate.plusDays(24)),
        todayTask.copy(due_at = aDate.plusDays(25)),
        todayTask.copy(due_at = aDate.plusDays(26))
      )

      val done_tasks = sortedTasks_due_tasks_over_limit_asc.zipWithIndex.map(task => task._1.copy(
        status = TaskStatus.Skipped(
          skipped_at = skippedDate,
          skipped_by = Some(31L)
        )
      ))


      (taskDaoService.getAllTaskForUserV2(
        _: Long,
        _: OrgId,
        _: String,
        _: ValidatedTaskReq,
        _: Option[List[ReplySentimentUuid]],
        _: Option[List[Long]],
        _: Option[List[CampaignId]],
        _: Option[List[String]],
        _: Option[List[String]],
        _: Option[List[String]],
        _: Option[TimeBasedTaskType],
        _: Boolean,
        _: Seq[Long],
//        _: Boolean
      )
      (_: ExecutionContext,
        _: SRLogger))
        .expects(team_id_9, org_id_9, "UTC", ValidatedTaskReq.ValidatedTaskReqRange(
          timeline = InferredQueryTimeline.Range.Before(aDate),
          pageSize = 21
        ), None, None, None, None, None, None, Some(TimeBasedTaskType.Skipped), false, permittedAccountIds, *, *)
        .returning(Future.successful(Right(done_tasks)))

//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(_: TeamId, _: SrRollingUpdateFeature)(_: SRLogger))
//        .expects(TeamId(team_id_9), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .twice()
//        .returning(false)

      (taskDaoService.getAtSameTimeTasksFromDB(
        _: Long,
        _: OrgId,
        _: ExactIdToCompareTime,
        _: String,
        _: Option[List[Long]],
        _: Option[List[CampaignId]],
        _: Option[List[ReplySentimentUuid]],
        _: Option[List[String]],
        _: Option[List[String]],
        _: Option[List[String]],
        _: Option[TimeBasedTaskType],
        _: Boolean,
        _: Seq[Long],
//        _: Boolean
      )(_: ExecutionContext,
        _: SRLogger))
        .expects(team_id_9, org_id_9, exactlyIdToCompare, "UTC", None, None, None, None, None, None, Some(TimeBasedTaskType.Skipped), false, permittedAccountIds, *, *)
        .returning(Future.successful(Right(done_tasks)))

      val res = taskService.getAllTasksForUser(
        isFirst = false,
        team_id = team_id_9,
        orgId = org_id_9,
        validatedTaskReq = ValidatedTaskReq.ValidatedTaskReqRange(
          timeline = InferredQueryTimeline.Range.Before(aDate),
          pageSize = 20
        ),
        searchTask = searchTask.copy(time_based_task_type = Some(TimeBasedTaskType.Skipped)),
        timeZone = "UTC",
        doNotFetchAutomatedDueTasks = false,
        permittedAccountIds = permittedAccountIds,

      )


      res.map { result => {
        result match {

          case Left(e) =>

            //            println(s"\n\nerror :: ${e}\n\n")

            assert(result == Left(GetAllTaskForUserError.ServerError(error)))


          case Right(d) =>

            //            println(s"\n\ncheck the links :: ${d.links}\n\n")

            assert(d.links.next.get == skippedDate)

        }
      }
      }
    }
    it("should give snoozed tasks time when finding tasks in snoozed section") {

      val sortedTasks_due_tasks_over_limit_asc = List(
        todayTask.copy(due_at = aDate.plusDays(1)),
        todayTask.copy(due_at = aDate.plusDays(2)),
        todayTask.copy(due_at = aDate.plusDays(3)),
        todayTask.copy(due_at = aDate.plusDays(4)),
        todayTask.copy(due_at = aDate.plusDays(5)),
        todayTask.copy(due_at = aDate.plusDays(6)),
        todayTask.copy(due_at = aDate.plusDays(7)),
        todayTask.copy(due_at = aDate.plusDays(8)),
        todayTask.copy(due_at = aDate.plusDays(9)),
        todayTask.copy(due_at = aDate.plusDays(10)),
        todayTask.copy(due_at = aDate.plusDays(11)),
        todayTask.copy(due_at = aDate.plusDays(12)),
        todayTask.copy(due_at = aDate.plusDays(13)),
        todayTask.copy(due_at = aDate.plusDays(14)),
        todayTask.copy(due_at = aDate.plusDays(15)),
        todayTask.copy(due_at = aDate.plusDays(16)),
        todayTask.copy(due_at = aDate.plusDays(17)),
        todayTask.copy(due_at = aDate.plusDays(18)),
        todayTask.copy(due_at = aDate.plusDays(19)),
        todayTask.copy(due_at = aDate.plusDays(20)),
        todayTask.copy(due_at = aDate.plusDays(21)),
        todayTask.copy(due_at = aDate.plusDays(22)),
        todayTask.copy(due_at = aDate.plusDays(23)),
        todayTask.copy(due_at = aDate.plusDays(24)),
        todayTask.copy(due_at = aDate.plusDays(25)),
        todayTask.copy(due_at = aDate.plusDays(26))
      )

      val done_tasks = sortedTasks_due_tasks_over_limit_asc.zipWithIndex.map(task => task._1.copy(
        status = TaskStatus.Snoozed(
          snoozed_till = snoozedDate,
          snoozed_at = snoozedDate,
          snoozed_by = 31L
        )
      ))

//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(_: TeamId, _: SrRollingUpdateFeature)(_: SRLogger))
//        .expects(TeamId(team_id_9), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .twice()
//        .returning(false)

      (taskDaoService.getAllTaskForUserV2(
        _: Long,
        _: OrgId,
        _: String,
        _: ValidatedTaskReq,
        _: Option[List[ReplySentimentUuid]],
        _: Option[List[Long]],
        _: Option[List[CampaignId]],
        _: Option[List[String]],
        _: Option[List[String]],
        _: Option[List[String]],
        _: Option[TimeBasedTaskType],
        _: Boolean,
        _: Seq[Long],
//        _: Boolean
      )
      (_: ExecutionContext,
        _: SRLogger))
        .expects(team_id_9, *, "UTC", ValidatedTaskReq.ValidatedTaskReqRange(
          timeline = InferredQueryTimeline.Range.Before(aDate),
          pageSize = 21
        ), None, None, None, None, None, None, Some(TimeBasedTaskType.Snoozed), false, permittedAccountIds, *, *)
        .returning(Future.successful(Right(done_tasks)))

      (taskDaoService.getAtSameTimeTasksFromDB(
        _: Long,
        _: OrgId,
        _: ExactIdToCompareTime,
        _: String,
        _: Option[List[Long]],
        _: Option[List[CampaignId]],
        _: Option[List[ReplySentimentUuid]],
        _: Option[List[String]],
        _: Option[List[String]],
        _: Option[List[String]],
        _: Option[TimeBasedTaskType],
        _: Boolean,
        _: Seq[Long],
//        _: Boolean
      )(_: ExecutionContext,
        _: SRLogger))
        .expects(team_id_9, *, exactlyIdToCompare, "UTC", None, None, None, None, None, None, Some(TimeBasedTaskType.Snoozed), false, permittedAccountIds, *, *)
        .returning(Future.successful(Right(done_tasks)))

      val res = taskService.getAllTasksForUser(
        isFirst = false,
        team_id = team_id_9,
        orgId = org_id_9,
        validatedTaskReq = ValidatedTaskReq.ValidatedTaskReqRange(
          timeline = InferredQueryTimeline.Range.Before(aDate),
          pageSize = 20
        ),
        searchTask = searchTask.copy(time_based_task_type = Some(TimeBasedTaskType.Snoozed)),
        timeZone = "UTC",
        doNotFetchAutomatedDueTasks = false,
        permittedAccountIds = permittedAccountIds
      )


      res.map { result => {
        result match {

          case Left(e) =>

            //            println(s"\n\nerror :: ${e}\n\n")

            assert(result == Left(GetAllTaskForUserError.ServerError(error)))


          case Right(d) =>

            //            println(s"\n\ncheck the links :: ${d.links}\n\n")

            assert(d.links.next.get == snoozedDate)

        }
      }
      }
    }

    it("should give archived tasks time when finding tasks in archived section") {

      val sortedTasks_due_tasks_over_limit_asc = List(
        todayTask.copy(due_at = aDate.plusDays(1)),
        todayTask.copy(due_at = aDate.plusDays(2)),
        todayTask.copy(due_at = aDate.plusDays(3)),
        todayTask.copy(due_at = aDate.plusDays(4)),
        todayTask.copy(due_at = aDate.plusDays(5)),
        todayTask.copy(due_at = aDate.plusDays(6)),
        todayTask.copy(due_at = aDate.plusDays(7)),
        todayTask.copy(due_at = aDate.plusDays(8)),
        todayTask.copy(due_at = aDate.plusDays(9)),
        todayTask.copy(due_at = aDate.plusDays(10)),
        todayTask.copy(due_at = aDate.plusDays(11)),
        todayTask.copy(due_at = aDate.plusDays(12)),
        todayTask.copy(due_at = aDate.plusDays(13)),
        todayTask.copy(due_at = aDate.plusDays(14)),
        todayTask.copy(due_at = aDate.plusDays(15)),
        todayTask.copy(due_at = aDate.plusDays(16)),
        todayTask.copy(due_at = aDate.plusDays(17)),
        todayTask.copy(due_at = aDate.plusDays(18)),
        todayTask.copy(due_at = aDate.plusDays(19)),
        todayTask.copy(due_at = aDate.plusDays(20)),
        todayTask.copy(due_at = aDate.plusDays(21)),
        todayTask.copy(due_at = aDate.plusDays(22)),
        todayTask.copy(due_at = aDate.plusDays(23)),
        todayTask.copy(due_at = aDate.plusDays(24)),
        todayTask.copy(due_at = aDate.plusDays(25)),
        todayTask.copy(due_at = aDate.plusDays(26))
      )

      val done_tasks = sortedTasks_due_tasks_over_limit_asc.zipWithIndex.map(task => task._1.copy(
        status = TaskStatus.Archive(
          archive_at = archivedDate
        )
      ))


      (taskDaoService.getAllTaskForUserV2(
        _: Long,
        _: OrgId,
        _: String,
        _: ValidatedTaskReq,
        _: Option[List[ReplySentimentUuid]],
        _: Option[List[Long]],
        _: Option[List[CampaignId]],
        _: Option[List[String]],
        _: Option[List[String]],
        _: Option[List[String]],
        _: Option[TimeBasedTaskType],
        _: Boolean,
        _: Seq[Long],
//        _: Boolean
      )
      (_: ExecutionContext,
        _: SRLogger))
        .expects(team_id_9, *, "UTC", ValidatedTaskReq.ValidatedTaskReqRange(
          timeline = InferredQueryTimeline.Range.Before(aDate),
          pageSize = 21
        ), None, None, None, None, None, None, Some(TimeBasedTaskType.Archived), false, permittedAccountIds, *, *)
        .returning(Future.successful(Right(done_tasks)))

//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(_: TeamId, _: SrRollingUpdateFeature)(_: SRLogger))
//        .expects(TeamId(team_id_9), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .twice()
//        .returning(false)

      (taskDaoService.getAtSameTimeTasksFromDB(
        _: Long,
        _: OrgId,
        _: ExactIdToCompareTime,
        _: String,
        _: Option[List[Long]],
        _: Option[List[CampaignId]],
        _: Option[List[ReplySentimentUuid]],
        _: Option[List[String]],
        _: Option[List[String]],
        _: Option[List[String]],
        _: Option[TimeBasedTaskType],
        _: Boolean,
        _: Seq[Long],
//        _: Boolean
      )(_: ExecutionContext,
        _: SRLogger))
        .expects(team_id_9, *, exactlyIdToCompare, "UTC", None, None, None, None, None, None, Some(TimeBasedTaskType.Archived), false, permittedAccountIds, *, *)
        .returning(Future.successful(Right(done_tasks)))

      val res = taskService.getAllTasksForUser(
        isFirst = false,
        team_id = team_id_9,
        orgId = org_id_9,
        validatedTaskReq = ValidatedTaskReq.ValidatedTaskReqRange(
          timeline = InferredQueryTimeline.Range.Before(aDate),
          pageSize = 20
        ),
        searchTask = searchTask.copy(time_based_task_type = Some(TimeBasedTaskType.Archived)),
        timeZone = "UTC",
        doNotFetchAutomatedDueTasks = false,
        permittedAccountIds = permittedAccountIds
      )


      res.map { result => {
        result match {

          case Left(e) =>

            //            println(s"\n\nerror :: ${e}\n\n")

            assert(result == Left(GetAllTaskForUserError.ServerError(error)))


          case Right(d) =>

            //            println(s"\n\ncheck the links :: ${d.links}\n\n")

            assert(d.links.next.get == archivedDate)

        }
      }
      }
    }

    it("should give failed tasks time when finding tasks in failed section") {

      val sortedTasks_due_tasks_over_limit_asc = List(
        todayTask.copy(due_at = aDate.plusDays(1)),
        todayTask.copy(due_at = aDate.plusDays(2)),
        todayTask.copy(due_at = aDate.plusDays(3)),
        todayTask.copy(due_at = aDate.plusDays(4)),
        todayTask.copy(due_at = aDate.plusDays(5)),
        todayTask.copy(due_at = aDate.plusDays(6)),
        todayTask.copy(due_at = aDate.plusDays(7)),
        todayTask.copy(due_at = aDate.plusDays(8)),
        todayTask.copy(due_at = aDate.plusDays(9)),
        todayTask.copy(due_at = aDate.plusDays(10)),
        todayTask.copy(due_at = aDate.plusDays(11)),
        todayTask.copy(due_at = aDate.plusDays(12)),
        todayTask.copy(due_at = aDate.plusDays(13)),
        todayTask.copy(due_at = aDate.plusDays(14)),
        todayTask.copy(due_at = aDate.plusDays(15)),
        todayTask.copy(due_at = aDate.plusDays(16)),
        todayTask.copy(due_at = aDate.plusDays(17)),
        todayTask.copy(due_at = aDate.plusDays(18)),
        todayTask.copy(due_at = aDate.plusDays(19)),
        todayTask.copy(due_at = aDate.plusDays(20)),
        todayTask.copy(due_at = aDate.plusDays(21)),
        todayTask.copy(due_at = aDate.plusDays(22)),
        todayTask.copy(due_at = aDate.plusDays(23)),
        todayTask.copy(due_at = aDate.plusDays(24)),
        todayTask.copy(due_at = aDate.plusDays(25)),
        todayTask.copy(due_at = aDate.plusDays(26))
      )

      val done_tasks = sortedTasks_due_tasks_over_limit_asc.zipWithIndex.map(task => task._1.copy(
        status = TaskStatus.Failed(
          failure_reason = "reason",
          failed_at = failedDate
        )
      ))

//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(_: TeamId, _: SrRollingUpdateFeature)(_: SRLogger))
//        .expects(TeamId(team_id_9), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .twice()
//        .returning(false)

      (taskDaoService.getAllTaskForUserV2(
        _: Long,
        _: OrgId,
        _: String,
        _: ValidatedTaskReq,
        _: Option[List[ReplySentimentUuid]],
        _: Option[List[Long]],
        _: Option[List[CampaignId]],
        _: Option[List[String]],
        _: Option[List[String]],
        _: Option[List[String]],
        _: Option[TimeBasedTaskType],
        _: Boolean,
        _: Seq[Long],
//        _: Boolean
      )
      (_: ExecutionContext,
        _: SRLogger))
        .expects(team_id_9, *, "UTC", ValidatedTaskReq.ValidatedTaskReqRange(
          timeline = InferredQueryTimeline.Range.Before(aDate),
          pageSize = 21
        ), None, None, None, None, None, None, Some(TimeBasedTaskType.Failed), false, permittedAccountIds, *, *)
        .returning(Future.successful(Right(done_tasks)))


      (taskDaoService.getAtSameTimeTasksFromDB(
        _: Long,
        _: OrgId,
        _: ExactIdToCompareTime,
        _: String,
        _: Option[List[Long]],
        _: Option[List[CampaignId]],
        _: Option[List[ReplySentimentUuid]],
        _: Option[List[String]],
        _: Option[List[String]],
        _: Option[List[String]],
        _: Option[TimeBasedTaskType],
        _: Boolean,
        _: Seq[Long],
//        _: Boolean
      )(_: ExecutionContext,
        _: SRLogger))
        .expects(team_id_9, *, exactlyIdToCompare, "UTC", None, None, None, None, None, None, Some(TimeBasedTaskType.Failed), false, permittedAccountIds, *, *)
        .returning(Future.successful(Right(done_tasks)))

      val res = taskService.getAllTasksForUser(
        isFirst = false,
        team_id = team_id_9,
        orgId = org_id_9,
        validatedTaskReq = ValidatedTaskReq.ValidatedTaskReqRange(
          timeline = InferredQueryTimeline.Range.Before(aDate),
          pageSize = 20
        ),
        searchTask = searchTask.copy(time_based_task_type = Some(TimeBasedTaskType.Failed)),
        timeZone = "UTC",
        doNotFetchAutomatedDueTasks = false,
        permittedAccountIds = permittedAccountIds
      )


      res.map { result => {
        result match {

          case Left(e) =>

            //            println(s"\n\nerror :: ${e}\n\n")

            assert(result == Left(GetAllTaskForUserError.ServerError(error)))


          case Right(d) =>

            //            println(s"\n\ncheck the links :: ${d.links}\n\n")

            assert(d.links.next.get == failedDate)

        }
      }
      }
    }
  }

  describe("testing TaskPgDao.getTimeLineQuery") {

    it("should return query for today tasks Before") {

      // "...t.team_id = ? AND t.[assignee_id IN (?, ?, ?, ?) AND t.]due_at < ? AND t.due..." -> "...t.team_id = ? AND t.[]due_at < ? AND t.due..."

      val expected =
        """

          |SELECT t.*,
          |   pe.email AS prospect_email,
          |   CONCAT(p.first_name, ' ', p.last_name) AS prospect_name,
          |   CONCAT(a.first_name, ' ', a.last_name) AS assignee_name,
          |   ( SELECT ccl.recording_url as latest_recording_url FROM call_conference_logs ccl WHERE ccl.team_id = t.team_id AND ccl.task_uuid = t.task_id AND ccl.recording_url is not null ORDER BY ccl.log_created_at DESC LIMIT 1 ) AS latest_recording_url
          |FROM tasks t
          |   INNER JOIN prospects p on (p.id = t.prospect_id AND p.team_id = t.team_id)
          |   LEFT JOIN prospects_emails pe ON ((pe.prospect_id = p.id) AND (pe.team_id = p.team_id) AND pe.is_primary)
          |   INNER JOIN accounts a on a.id = t.assignee_id
          |WHERE
          |   t.team_id = ?
          |   AND t.assignee_id IN (?, ?, ?, ?)
          |   AND t.created_via != ?
          |   AND t.due_at < ?
          |   AND t.due_at >?
          |   AND t.status = ?
          |ORDER BY
          |   t.due_at desc
          |limit ?
          |
          |""".stripMargin
      val qry_str = TaskPgDAO.getAllTaskQuery(
          teamId = team_id_9,
          orgId = org_id_9,
          timeZone = "UTC",
          validatedTaskReq = ValidatedTaskReq.ValidatedTaskReqRange(
            timeline = InferredQueryTimeline.Range.Before(aDate),
            pageSize = 100
          ),
          reply_sentiment_uuids = None,
          time_based_task_type = Some(TimeBasedTaskType.Today),
          assignee_ids = None,
          campaign_ids = None,
          task_type = None,
          task_status = None,
          task_priority = None,
          doNotFetchAutomatedDueTasks = false,
          permittedAccountIds = permittedAccountIds,
//          emailNotCompulsoryEnabled = false
        )
        .statement

      val split = expected.split(s"\\s+")
      val rhs = split.reduce((a1, a2) => {
        a1 + " " + a2
      })
      val lhs = qry_str.split("\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })

      assert(lhs == rhs)
    }

    it("should return query for today tasks After") {

      val expected =
        """
          |SELECT t.*,
          |   pe.email AS prospect_email,
          |   CONCAT(p.first_name, ' ', p.last_name) AS prospect_name,
          |   CONCAT(a.first_name, ' ', a.last_name) AS assignee_name,
          |    ( SELECT ccl.recording_url as latest_recording_url FROM call_conference_logs ccl WHERE ccl.team_id = t.team_id AND ccl.task_uuid = t.task_id AND ccl.recording_url is not null ORDER BY ccl.log_created_at DESC LIMIT 1 ) AS latest_recording_url
          |FROM tasks t
          |   INNER JOIN prospects p on (p.id = t.prospect_id AND p.team_id = t.team_id)
          |   LEFT JOIN prospects_emails pe ON ((pe.prospect_id = p.id) AND (pe.team_id = p.team_id) AND pe.is_primary)
          |   INNER JOIN accounts a on a.id = t.assignee_id
          |WHERE
          |   t.team_id = ?
          |   AND t.assignee_id IN (?, ?, ?, ?)
          |   AND t.created_via != ?
          |   AND t.due_at > ?
          |   AND t.due_at < ?
          |   AND t.status = ?
          |ORDER BY
          |   t.due_at asc
          |limit ?
          |
          |""".stripMargin
      val qry_str = TaskPgDAO.getAllTaskQuery(
          teamId = team_id_9,
          orgId = org_id_9,
          timeZone = "UTC",
          validatedTaskReq = ValidatedTaskReq.ValidatedTaskReqRange(
            timeline = InferredQueryTimeline.Range.After(aDate),
            pageSize = 100
          ),
          reply_sentiment_uuids = None,
          time_based_task_type = Some(TimeBasedTaskType.Today),
          assignee_ids = None,
          campaign_ids = None,
          task_type = None,
          task_status = None,
          task_priority = None,
          doNotFetchAutomatedDueTasks = false,
          permittedAccountIds = permittedAccountIds,
//          emailNotCompulsoryEnabled = false
        )
        .statement

      val split = expected.split(s"\\s+")
      val rhs = split.reduce((a1, a2) => {
        a1 + " " + a2
      })
      val lhs = qry_str.split("\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })

      assert(lhs == rhs)
    }

    it("should return query for due tasks Before") {

      val expected =
        """
          |SELECT t.*,
          |   pe.email AS prospect_email,
          |   CONCAT(p.first_name, ' ', p.last_name) AS prospect_name,
          |   CONCAT(a.first_name, ' ', a.last_name) AS assignee_name,
          |       ( SELECT ccl.recording_url as latest_recording_url FROM call_conference_logs ccl WHERE ccl.team_id = t.team_id AND ccl.task_uuid = t.task_id AND ccl.recording_url is not null ORDER BY ccl.log_created_at DESC LIMIT 1 ) AS latest_recording_url
          |FROM tasks t
          |   INNER JOIN prospects p on (p.id = t.prospect_id AND p.team_id = t.team_id)
          |   LEFT JOIN prospects_emails pe ON ((pe.prospect_id = p.id) AND (pe.team_id = p.team_id) AND pe.is_primary)
          |   INNER JOIN accounts a on a.id = t.assignee_id
          |WHERE
          |   t.team_id = ?
          |   AND t.assignee_id IN (?, ?, ?, ?)
          |   AND t.created_via != ?
          |   AND t.due_at < ?
          |   AND t.status = ?
          |ORDER BY
          |   t.due_at desc
          |limit ?
          |
          |""".stripMargin
      val qry_str = TaskPgDAO.getAllTaskQuery(
          teamId = team_id_9,
          orgId = org_id_9,
          timeZone = "UTC",
          validatedTaskReq = ValidatedTaskReq.ValidatedTaskReqRange(
            timeline = InferredQueryTimeline.Range.Before(aDate),
            pageSize = 100
          ),
          reply_sentiment_uuids = None,
          time_based_task_type = Some(TimeBasedTaskType.Due),
          assignee_ids = None,
          campaign_ids = None,
          task_type = None,
          task_status = None,
          task_priority = None,
          doNotFetchAutomatedDueTasks = false,
          permittedAccountIds = permittedAccountIds,
//          emailNotCompulsoryEnabled = false
        )
        .statement

      val split = expected.split(s"\\s+")
      val rhs = split.reduce((a1, a2) => {
        a1 + " " + a2
      })
      val lhs = qry_str.split("\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })

      assert(lhs == rhs)
    }

    it("should return query for due tasks After") {

      val expected =
        """
          |SELECT t.*,
          |   pe.email AS prospect_email,
          |   CONCAT(p.first_name, ' ', p.last_name) AS prospect_name,
          |   CONCAT(a.first_name, ' ', a.last_name) AS assignee_name,
          |       ( SELECT ccl.recording_url as latest_recording_url FROM call_conference_logs ccl WHERE ccl.team_id = t.team_id AND ccl.task_uuid = t.task_id AND ccl.recording_url is not null ORDER BY ccl.log_created_at DESC LIMIT 1 ) AS latest_recording_url
          |FROM tasks t
          |   INNER JOIN prospects p on (p.id = t.prospect_id AND p.team_id = t.team_id)
          |   LEFT JOIN prospects_emails pe ON ((pe.prospect_id = p.id) AND (pe.team_id = p.team_id) AND pe.is_primary)
          |   INNER JOIN accounts a on a.id = t.assignee_id
          |WHERE
          |   t.team_id = ?
          |   AND t.assignee_id IN (?, ?, ?, ?)
          |   AND t.created_via != ?
          |   AND t.due_at > ?
          |   AND t.due_at < ?
          |   AND t.status = ?
          |ORDER BY
          |   t.due_at asc
          |limit ?
          |
          |""".stripMargin
      val qry_str = TaskPgDAO.getAllTaskQuery(
          teamId = team_id_9,
          orgId = org_id_9,
          timeZone = "UTC",
          validatedTaskReq = ValidatedTaskReq.ValidatedTaskReqRange(
            timeline = InferredQueryTimeline.Range.After(aDate),
            pageSize = 100
          ),
          reply_sentiment_uuids = None,
          time_based_task_type = Some(TimeBasedTaskType.Due),
          assignee_ids = None,
          campaign_ids = None,
          task_type = None,
          task_status = None,
          task_priority = None,
          doNotFetchAutomatedDueTasks = false,
          permittedAccountIds = permittedAccountIds,
//          emailNotCompulsoryEnabled = false
        )
        .statement

      val split = expected.split(s"\\s+")
      val rhs = split.reduce((a1, a2) => {
        a1 + " " + a2
      })
      val lhs = qry_str.split("\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })

      assert(lhs == rhs)
    }

    it("should return query for upcoming tasks After") {

      val expected =
        """
          |SELECT t.*,
          |   pe.email AS prospect_email,
          |   CONCAT(p.first_name, ' ', p.last_name) AS prospect_name,
          |   CONCAT(a.first_name, ' ', a.last_name) AS assignee_name,
          |       ( SELECT ccl.recording_url as latest_recording_url FROM call_conference_logs ccl WHERE ccl.team_id = t.team_id AND ccl.task_uuid = t.task_id AND ccl.recording_url is not null ORDER BY ccl.log_created_at DESC LIMIT 1 ) AS latest_recording_url
          |FROM tasks t
          |   INNER JOIN prospects p on (p.id = t.prospect_id AND p.team_id = t.team_id)
          |   LEFT JOIN prospects_emails pe ON ((pe.prospect_id = p.id) AND (pe.team_id = p.team_id) AND pe.is_primary)
          |   INNER JOIN accounts a on a.id = t.assignee_id
          |WHERE
          |   t.team_id = ?
          |   AND t.assignee_id IN (?, ?, ?, ?)
          |   AND t.created_via != ?
          |   AND t.due_at > ?
          |   AND t.status = ?
          |ORDER BY
          |   t.due_at asc
          |limit ?
          |
          |""".stripMargin
      val qry_str = TaskPgDAO.getAllTaskQuery(
          teamId = team_id_9,
          orgId = org_id_9,
          timeZone = "UTC",
          validatedTaskReq = ValidatedTaskReq.ValidatedTaskReqRange(
            timeline = InferredQueryTimeline.Range.After(aDate),
            pageSize = 100
          ),
          reply_sentiment_uuids = None,
          time_based_task_type = Some(TimeBasedTaskType.Upcoming),
          assignee_ids = None,
          campaign_ids = None,
          task_type = None,
          task_status = None,
          task_priority = None,
          doNotFetchAutomatedDueTasks = false,
          permittedAccountIds = permittedAccountIds,
//          emailNotCompulsoryEnabled = false
        )
        .statement

      val split = expected.split(s"\\s+")
      val rhs = split.reduce((a1, a2) => {
        a1 + " " + a2
      })
      val lhs = qry_str.split("\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })

      assert(lhs == rhs)
    }

    it("should return query for upcoming tasks Before") {

      val expected =
        """
          |SELECT t.*,
          |   pe.email AS prospect_email,
          |   CONCAT(p.first_name, ' ', p.last_name) AS prospect_name,
          |   CONCAT(a.first_name, ' ', a.last_name) AS assignee_name,
          |       ( SELECT ccl.recording_url as latest_recording_url FROM call_conference_logs ccl WHERE ccl.team_id = t.team_id AND ccl.task_uuid = t.task_id AND ccl.recording_url is not null ORDER BY ccl.log_created_at DESC LIMIT 1 ) AS latest_recording_url
          |FROM tasks t
          |   INNER JOIN prospects p on (p.id = t.prospect_id AND p.team_id = t.team_id)
          |   LEFT JOIN prospects_emails pe ON ((pe.prospect_id = p.id) AND (pe.team_id = p.team_id) AND pe.is_primary)
          |   INNER JOIN accounts a on a.id = t.assignee_id
          |WHERE
          |   t.team_id = ?
          |   AND t.assignee_id IN (?, ?, ?, ?)
          |   AND t.created_via != ?
          |   AND t.due_at < ?
          |   AND t.due_at > ?
          |   AND t.status = ?
          |ORDER BY
          |   t.due_at desc
          |limit ?
          |
          |""".stripMargin
      val qry_str = TaskPgDAO.getAllTaskQuery(
          teamId = team_id_9,
          orgId = org_id_9,
          timeZone = "UTC",
          validatedTaskReq = ValidatedTaskReq.ValidatedTaskReqRange(
            timeline = InferredQueryTimeline.Range.Before(aDate),
            pageSize = 100
          ),
          reply_sentiment_uuids = None,
          time_based_task_type = Some(TimeBasedTaskType.Upcoming),
          assignee_ids = None,
          campaign_ids = None,
          task_type = None,
          task_status = None,
          task_priority = None,
          doNotFetchAutomatedDueTasks = false,
          permittedAccountIds = permittedAccountIds,
//          emailNotCompulsoryEnabled = false
        )
        .statement

      val split = expected.split(s"\\s+")
      val rhs = split.reduce((a1, a2) => {
        a1 + " " + a2
      })
      val lhs = qry_str.split("\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })

      assert(lhs == rhs)
    }

    it("should return query for completed tasks Before") {

      val expected =
        """
          |SELECT t.*,
          |   pe.email AS prospect_email,
          |   CONCAT(p.first_name, ' ', p.last_name) AS prospect_name,
          |   CONCAT(a.first_name, ' ', a.last_name) AS assignee_name,
          |       ( SELECT ccl.recording_url as latest_recording_url FROM call_conference_logs ccl WHERE ccl.team_id = t.team_id AND ccl.task_uuid = t.task_id AND ccl.recording_url is not null ORDER BY ccl.log_created_at DESC LIMIT 1 ) AS latest_recording_url
          |FROM tasks t
          |   INNER JOIN prospects p on (p.id = t.prospect_id AND p.team_id = t.team_id)
          |   LEFT JOIN prospects_emails pe ON ((pe.prospect_id = p.id) AND (pe.team_id = p.team_id) AND pe.is_primary)
          |   INNER JOIN accounts a on a.id = t.assignee_id
          |WHERE
          |   t.team_id = ?
          |   AND t.assignee_id IN (?, ?, ?, ?)
          |   AND t.done_at < ?
          |   AND t.status IN (? , ?)
          |ORDER BY
          |   t.done_at desc
          |limit ?
          |
          |""".stripMargin
      val qry_str = TaskPgDAO.getAllTaskQuery(
          teamId = team_id_9,
          orgId = org_id_9,
          timeZone = "UTC",
          validatedTaskReq = ValidatedTaskReq.ValidatedTaskReqRange(
            timeline = InferredQueryTimeline.Range.Before(aDate),
            pageSize = 100
          ),
          reply_sentiment_uuids = None,
          time_based_task_type = Some(TimeBasedTaskType.Completed),
          assignee_ids = None,
          campaign_ids = None,
          task_type = None,
          task_status = None,
          task_priority = None,
          doNotFetchAutomatedDueTasks = false,
          permittedAccountIds = permittedAccountIds,
//          emailNotCompulsoryEnabled = false

        )
        .statement

      val split = expected.split(s"\\s+")
      val rhs = split.reduce((a1, a2) => {
        a1 + " " + a2
      })
      val lhs = qry_str.split("\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })

      assert(lhs == rhs)
    }

    it("should return query for completed tasks After") {

      val expected =
        """
          |SELECT t.*,
          |   pe.email AS prospect_email,
          |   CONCAT(p.first_name, ' ', p.last_name) AS prospect_name,
          |   CONCAT(a.first_name, ' ', a.last_name) AS assignee_name,
          |       ( SELECT ccl.recording_url as latest_recording_url FROM call_conference_logs ccl WHERE ccl.team_id = t.team_id AND ccl.task_uuid = t.task_id AND ccl.recording_url is not null ORDER BY ccl.log_created_at DESC LIMIT 1 ) AS latest_recording_url
          |FROM tasks t
          |   INNER JOIN prospects p on (p.id = t.prospect_id AND p.team_id = t.team_id)
          |   LEFT JOIN prospects_emails pe ON ((pe.prospect_id = p.id) AND (pe.team_id = p.team_id) AND pe.is_primary)
          |   INNER JOIN accounts a on a.id = t.assignee_id
          |WHERE
          |   t.team_id = ?
          |   AND t.assignee_id IN (?, ?, ?, ?)
          |   AND t.done_at > ?
          |   AND t.status IN (? , ?)
          |ORDER BY
          |   t.done_at asc
          |limit ?
          |
          |""".stripMargin
      val qry_str = TaskPgDAO.getAllTaskQuery(
          teamId = team_id_9,
          orgId = org_id_9,
          timeZone = "UTC",
          validatedTaskReq = ValidatedTaskReq.ValidatedTaskReqRange(
            timeline = InferredQueryTimeline.Range.After(aDate),
            pageSize = 100
          ),
          reply_sentiment_uuids = None,
          time_based_task_type = Some(TimeBasedTaskType.Completed),
          assignee_ids = None,
          campaign_ids = None,
          task_type = None,
          task_status = None,
          task_priority = None,
          doNotFetchAutomatedDueTasks = false,
          permittedAccountIds = permittedAccountIds,
//          emailNotCompulsoryEnabled = false

        )
        .statement

      val split = expected.split(s"\\s+")
      val rhs = split.reduce((a1, a2) => {
        a1 + " " + a2
      })
      val lhs = qry_str.split("\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })

      assert(lhs == rhs)
    }


    it("should return query for completed tasks After with date range"){

      val expected =
        """
          |SELECT t.*,
          |   pe.email AS prospect_email,
          |   CONCAT(p.first_name, ' ', p.last_name) AS prospect_name,
          |   CONCAT(a.first_name, ' ', a.last_name) AS assignee_name,
          |       ( SELECT ccl.recording_url as latest_recording_url FROM call_conference_logs ccl WHERE ccl.team_id = t.team_id AND ccl.task_uuid = t.task_id AND ccl.recording_url is not null ORDER BY ccl.log_created_at DESC LIMIT 1 ) AS latest_recording_url
          |FROM tasks t
          |   INNER JOIN prospects p on (p.id = t.prospect_id AND p.team_id = t.team_id)
          |   LEFT JOIN prospects_emails pe ON ((pe.prospect_id = p.id) AND (pe.team_id = p.team_id) AND pe.is_primary)
          |   INNER JOIN accounts a on a.id = t.assignee_id
          |WHERE
          |   t.team_id = ?
          |   AND t.assignee_id IN (?, ?, ?, ?)
          |   AND t.done_at BETWEEN ? AND ?
          |   AND t.done_at > ?
          |   AND t.status IN (? , ?)
          |ORDER BY
          |   t.done_at asc
          |limit ?
          |
          |""".stripMargin
      val qry_str = TaskPgDAO.getAllTaskQuery(
          teamId = team_id_9,
          orgId = org_id_9,
          timeZone = "UTC",
          validatedTaskReq = ValidatedTaskReq.ValidatedTaskReqRange(
            timeline = InferredQueryTimeline.Range.After(aDate),
            pageSize = 100,
            dateRange = Some(DateRangeBetween(
              startDate = dateRangeA,
              endDate = dateRangeB
            ))
          ),
          reply_sentiment_uuids = None,
          time_based_task_type = Some(TimeBasedTaskType.Completed),
          assignee_ids = None,
          campaign_ids = None,
          task_type = None,
          task_status = None,
          task_priority = None,
          doNotFetchAutomatedDueTasks = false,
          permittedAccountIds = permittedAccountIds,
          //          emailNotCompulsoryEnabled = false

        )
        .statement

      val split = expected.split(s"\\s+")
      val rhs = split.reduce((a1, a2) => {
        a1 + " " + a2
      })
      val lhs = qry_str.split("\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })

      assert(lhs == rhs)
    }

    it("should return query for completed tasks Before with date range") {

      val expected =
        """
          |SELECT t.*,
          |   pe.email AS prospect_email,
          |   CONCAT(p.first_name, ' ', p.last_name) AS prospect_name,
          |   CONCAT(a.first_name, ' ', a.last_name) AS assignee_name,
          |       ( SELECT ccl.recording_url as latest_recording_url FROM call_conference_logs ccl WHERE ccl.team_id = t.team_id AND ccl.task_uuid = t.task_id AND ccl.recording_url is not null ORDER BY ccl.log_created_at DESC LIMIT 1 ) AS latest_recording_url
          |FROM tasks t
          |   INNER JOIN prospects p on (p.id = t.prospect_id AND p.team_id = t.team_id)
          |   LEFT JOIN prospects_emails pe ON ((pe.prospect_id = p.id) AND (pe.team_id = p.team_id) AND pe.is_primary)
          |   INNER JOIN accounts a on a.id = t.assignee_id
          |WHERE
          |   t.team_id = ?
          |   AND t.assignee_id IN (?, ?, ?, ?)
          |   AND t.done_at BETWEEN ? AND ?
          |   AND t.done_at < ?
          |   AND t.status IN (? , ?)
          |ORDER BY
          |   t.done_at desc
          |limit ?
          |
          |""".stripMargin
      val qry_str = TaskPgDAO.getAllTaskQuery(
          teamId = team_id_9,
          orgId = org_id_9,
          timeZone = "UTC",
          validatedTaskReq = ValidatedTaskReq.ValidatedTaskReqRange(
            timeline = InferredQueryTimeline.Range.Before(aDate),
            pageSize = 100,
            dateRange = Some(DateRangeBetween(
              startDate = dateRangeA,
              endDate = dateRangeB
            ))
          ),
          reply_sentiment_uuids = None,
          time_based_task_type = Some(TimeBasedTaskType.Completed),
          assignee_ids = None,
          campaign_ids = None,
          task_type = None,
          task_status = None,
          task_priority = None,
          doNotFetchAutomatedDueTasks = false,
          permittedAccountIds = permittedAccountIds,
          //          emailNotCompulsoryEnabled = false

        )
        .statement

      val split = expected.split(s"\\s+")
      val rhs = split.reduce((a1, a2) => {
        a1 + " " + a2
      })
      val lhs = qry_str.split("\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })

      assert(lhs == rhs)
    }

    it("should return query for Skipped tasks Before") {

      val expected =
        """
          |SELECT t.*,
          |   pe.email AS prospect_email,
          |   CONCAT(p.first_name, ' ', p.last_name) AS prospect_name,
          |   CONCAT(a.first_name, ' ', a.last_name) AS assignee_name,
          |       ( SELECT ccl.recording_url as latest_recording_url FROM call_conference_logs ccl WHERE ccl.team_id = t.team_id AND ccl.task_uuid = t.task_id AND ccl.recording_url is not null ORDER BY ccl.log_created_at DESC LIMIT 1 ) AS latest_recording_url
          |FROM tasks t
          |   INNER JOIN prospects p on (p.id = t.prospect_id AND p.team_id = t.team_id)
          |   LEFT JOIN prospects_emails pe ON ((pe.prospect_id = p.id) AND (pe.team_id = p.team_id) AND pe.is_primary)
          |   INNER JOIN accounts a on a.id = t.assignee_id
          |WHERE
          |   t.team_id = ?
          |   AND t.assignee_id IN (?, ?, ?, ?)
          |   AND t.created_via != ?
          |   AND t.skipped_at < ?
          |   AND t.status = ?
          |ORDER BY
          |   t.skipped_at desc
          |limit ?
          |
          |""".stripMargin
      val qry_str = TaskPgDAO.getAllTaskQuery(
          teamId = team_id_9,
          orgId = org_id_9,
          timeZone = "UTC",
          validatedTaskReq = ValidatedTaskReq.ValidatedTaskReqRange(
            timeline = InferredQueryTimeline.Range.Before(aDate),
            pageSize = 100
          ),
          reply_sentiment_uuids = None,
          time_based_task_type = Some(TimeBasedTaskType.Skipped),
          assignee_ids = None,
          campaign_ids = None,
          task_type = None,
          task_status = None,
          task_priority = None,
          doNotFetchAutomatedDueTasks = false,
          permittedAccountIds = permittedAccountIds,
//          emailNotCompulsoryEnabled = false

        )
        .statement

      val split = expected.split(s"\\s+")
      val rhs = split.reduce((a1, a2) => {
        a1 + " " + a2
      })
      val lhs = qry_str.split("\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })

      assert(lhs == rhs)
    }

    it("should return query for Skipped tasks After") {

      val expected =
        """
          |SELECT t.*,
          |   pe.email AS prospect_email,
          |   CONCAT(p.first_name, ' ', p.last_name) AS prospect_name,
          |   CONCAT(a.first_name, ' ', a.last_name) AS assignee_name,
          |       ( SELECT ccl.recording_url as latest_recording_url FROM call_conference_logs ccl WHERE ccl.team_id = t.team_id AND ccl.task_uuid = t.task_id AND ccl.recording_url is not null ORDER BY ccl.log_created_at DESC LIMIT 1 ) AS latest_recording_url
          |FROM tasks t
          |   INNER JOIN prospects p on (p.id = t.prospect_id AND p.team_id = t.team_id)
          |   LEFT JOIN prospects_emails pe ON ((pe.prospect_id = p.id) AND (pe.team_id = p.team_id) AND pe.is_primary)
          |   INNER JOIN accounts a on a.id = t.assignee_id
          |WHERE
          |   t.team_id = ?
          |   AND t.assignee_id IN (?, ?, ?, ?)
          |   AND t.created_via != ?
          |   AND t.skipped_at > ?
          |   AND t.status = ?
          |ORDER BY
          |    t.skipped_at asc
          |limit ?
          |
          |""".stripMargin
      val qry_str = TaskPgDAO.getAllTaskQuery(
          teamId = team_id_9,
          orgId = org_id_9,
          timeZone = "UTC",
          validatedTaskReq = ValidatedTaskReq.ValidatedTaskReqRange(
            timeline = InferredQueryTimeline.Range.After(aDate),
            pageSize = 100
          ),
          reply_sentiment_uuids = None,
          time_based_task_type = Some(TimeBasedTaskType.Skipped),
          assignee_ids = None,
          campaign_ids = None,
          task_type = None,
          task_status = None,
          task_priority = None,
          doNotFetchAutomatedDueTasks = false,
          permittedAccountIds = permittedAccountIds,
//          emailNotCompulsoryEnabled = false

        )
        .statement

      val split = expected.split(s"\\s+")
      val rhs = split.reduce((a1, a2) => {
        a1 + " " + a2
      })
      val lhs = qry_str.split("\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })

      assert(lhs == rhs)
    }

    it("should return query for Skipped tasks After without date range") {

      val expected =
        """
          |SELECT t.*,
          |   pe.email AS prospect_email,
          |   CONCAT(p.first_name, ' ', p.last_name) AS prospect_name,
          |   CONCAT(a.first_name, ' ', a.last_name) AS assignee_name,
          |       ( SELECT ccl.recording_url as latest_recording_url FROM call_conference_logs ccl WHERE ccl.team_id = t.team_id AND ccl.task_uuid = t.task_id AND ccl.recording_url is not null ORDER BY ccl.log_created_at DESC LIMIT 1 ) AS latest_recording_url
          |FROM tasks t
          |   INNER JOIN prospects p on (p.id = t.prospect_id AND p.team_id = t.team_id)
          |   LEFT JOIN prospects_emails pe ON ((pe.prospect_id = p.id) AND (pe.team_id = p.team_id) AND pe.is_primary)
          |   INNER JOIN accounts a on a.id = t.assignee_id
          |WHERE
          |   t.team_id = ?
          |   AND t.assignee_id IN (?, ?, ?, ?)
          |   AND t.created_via != ?
          |   AND t.skipped_at > ?
          |   AND t.status = ?
          |ORDER BY
          |    t.skipped_at asc
          |limit ?
          |
          |""".stripMargin
      val qry_str = TaskPgDAO.getAllTaskQuery(
          teamId = team_id_9,
          orgId = org_id_9,
          timeZone = "UTC",
          validatedTaskReq = ValidatedTaskReq.ValidatedTaskReqRange(
            timeline = InferredQueryTimeline.Range.After(aDate),
            pageSize = 100,
            dateRange = Some(DateRangeBetween(
              startDate = dateRangeA,
              endDate = dateRangeB
            ))
          ),
          reply_sentiment_uuids = None,
          time_based_task_type = Some(TimeBasedTaskType.Skipped),
          assignee_ids = None,
          campaign_ids = None,
          task_type = None,
          task_status = None,
          task_priority = None,
          doNotFetchAutomatedDueTasks = false,
          permittedAccountIds = permittedAccountIds,
          //          emailNotCompulsoryEnabled = false

        )
        .statement

      val split = expected.split(s"\\s+")
      val rhs = split.reduce((a1, a2) => {
        a1 + " " + a2
      })
      val lhs = qry_str.split("\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })

      assert(lhs == rhs)
    }



    it("should return query for Today tasks Exact") {

      val expected =
        """
          |SELECT t.*,
          |   pe.email AS prospect_email,
          |   CONCAT(p.first_name, ' ', p.last_name) AS prospect_name,
          |   CONCAT(a.first_name, ' ', a.last_name) AS assignee_name,
          |       ( SELECT ccl.recording_url as latest_recording_url FROM call_conference_logs ccl WHERE ccl.team_id = t.team_id AND ccl.task_uuid = t.task_id AND ccl.recording_url is not null ORDER BY ccl.log_created_at DESC LIMIT 1 ) AS latest_recording_url
          |FROM tasks t
          |   INNER JOIN prospects p on (p.id = t.prospect_id AND p.team_id = t.team_id)
          |   LEFT JOIN prospects_emails pe ON ((pe.prospect_id = p.id) AND (pe.team_id = p.team_id) AND pe.is_primary)
          |   INNER JOIN accounts a on a.id = t.assignee_id
          |WHERE
          |   t.team_id = ?
          |   AND t.assignee_id IN (?, ?, ?, ?)
          |   AND t.created_via != ?
          |   AND t.due_at = (select due_at from tasks where task_id = ? and team_id = ?)
          |   AND t.status = ?
          |limit ?
          |
          |""".stripMargin
      val qry_str = TaskPgDAO.getAllTaskQuery(
          teamId = team_id_9,
          orgId = org_id_9,
          timeZone = "UTC",
          validatedTaskReq = ValidatedTaskReq.ValidatedTaskReqExact(
            timeline = InferredQueryTimeline.Exact(exactIdToCompareTime = ExactIdToCompareTime("24")),
            pageSize = 100
          ),
          reply_sentiment_uuids = None,
          time_based_task_type = Some(TimeBasedTaskType.Today),
          assignee_ids = None,
          campaign_ids = None,
          task_type = None,
          task_status = None,
          task_priority = None,
          doNotFetchAutomatedDueTasks = false,
          permittedAccountIds = permittedAccountIds,
//          emailNotCompulsoryEnabled = false

        )
        .statement

      val split = expected.split(s"\\s+")
      val rhs = split.reduce((a1, a2) => {
        a1 + " " + a2
      })
      val lhs = qry_str.split("\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })

      assert(lhs == rhs)
    }

    it("should return query for Due tasks Exact") {

      val expected =
        """
          |SELECT t.*,
          |   pe.email AS prospect_email,
          |   CONCAT(p.first_name, ' ', p.last_name) AS prospect_name,
          |   CONCAT(a.first_name, ' ', a.last_name) AS assignee_name,
          |       ( SELECT ccl.recording_url as latest_recording_url FROM call_conference_logs ccl WHERE ccl.team_id = t.team_id AND ccl.task_uuid = t.task_id AND ccl.recording_url is not null ORDER BY ccl.log_created_at DESC LIMIT 1 ) AS latest_recording_url
          |FROM tasks t
          |   INNER JOIN prospects p on (p.id = t.prospect_id AND p.team_id = t.team_id)
          |   LEFT JOIN prospects_emails pe ON ((pe.prospect_id = p.id) AND (pe.team_id = p.team_id) AND pe.is_primary)
          |   INNER JOIN accounts a on a.id = t.assignee_id
          |WHERE
          |   t.team_id = ?
          |   AND t.assignee_id IN (?, ?, ?, ?)
          |   AND t.created_via != ?
          |   AND t.due_at = (select due_at from tasks where task_id = ? and team_id = ?)
          |   AND t.status = ?
          |limit ?
          |
          |""".stripMargin
      val qry_str = TaskPgDAO.getAllTaskQuery(
          teamId = team_id_9,
          orgId = org_id_9,
          timeZone = "UTC",
          validatedTaskReq = ValidatedTaskReq.ValidatedTaskReqExact(
            timeline = InferredQueryTimeline.Exact(exactIdToCompareTime = ExactIdToCompareTime("24")),
            pageSize = 100
          ),
          reply_sentiment_uuids = None,
          time_based_task_type = Some(TimeBasedTaskType.Due),
          assignee_ids = None,
          campaign_ids = None,
          task_type = None,
          task_status = None,
          task_priority = None,
          doNotFetchAutomatedDueTasks = false,
          permittedAccountIds = permittedAccountIds,
//          emailNotCompulsoryEnabled = false
        )
        .statement

      val split = expected.split(s"\\s+")
      val rhs = split.reduce((a1, a2) => {
        a1 + " " + a2
      })
      val lhs = qry_str.split("\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })

      assert(lhs == rhs)
    }

    it("should return query for Upcoming tasks Exact") {

      val expected =
        """
          |SELECT t.*,
          |   pe.email AS prospect_email,
          |   CONCAT(p.first_name, ' ', p.last_name) AS prospect_name,
          |   CONCAT(a.first_name, ' ', a.last_name) AS assignee_name,
          |       ( SELECT ccl.recording_url as latest_recording_url FROM call_conference_logs ccl WHERE ccl.team_id = t.team_id AND ccl.task_uuid = t.task_id AND ccl.recording_url is not null ORDER BY ccl.log_created_at DESC LIMIT 1 ) AS latest_recording_url
          |FROM tasks t
          |   INNER JOIN prospects p on (p.id = t.prospect_id AND p.team_id = t.team_id)
          |   LEFT JOIN prospects_emails pe ON ((pe.prospect_id = p.id) AND (pe.team_id = p.team_id) AND pe.is_primary)
          |   INNER JOIN accounts a on a.id = t.assignee_id
          |WHERE
          |   t.team_id = ?
          |   AND t.assignee_id IN (?, ?, ?, ?)
          |   AND t.created_via != ?
          |   AND t.due_at = (select due_at from tasks where task_id = ? and team_id = ?)
          |   AND t.status = ?
          |limit ?
          |
          |""".stripMargin
      val qry_str = TaskPgDAO.getAllTaskQuery(
          teamId = team_id_9,
          orgId = org_id_9,
          timeZone = "UTC",
          validatedTaskReq = ValidatedTaskReq.ValidatedTaskReqExact(
            timeline = InferredQueryTimeline.Exact(exactIdToCompareTime = ExactIdToCompareTime("24")),
            pageSize = 100
          ),
          reply_sentiment_uuids = None,
          time_based_task_type = Some(TimeBasedTaskType.Upcoming),
          assignee_ids = None,
          campaign_ids = None,
          task_type = None,
          task_status = None,
          task_priority = None,
          doNotFetchAutomatedDueTasks = false,
          permittedAccountIds = permittedAccountIds,
//          emailNotCompulsoryEnabled = false

        )
        .statement

      val split = expected.split(s"\\s+")
      val rhs = split.reduce((a1, a2) => {
        a1 + " " + a2
      })
      val lhs = qry_str.split("\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })

      assert(lhs == rhs)
    }

    it("should return query for Completed tasks Exact") {

      val expected =
        """
          |SELECT t.*,
          |  pe.email AS prospect_email,
          |  CONCAT(p.first_name, ' ', p.last_name) AS prospect_name,
          |  CONCAT(a.first_name, ' ', a.last_name) AS assignee_name,
          |      ( SELECT ccl.recording_url as latest_recording_url FROM call_conference_logs ccl WHERE ccl.team_id = t.team_id AND ccl.task_uuid = t.task_id AND ccl.recording_url is not null ORDER BY ccl.log_created_at DESC LIMIT 1 ) AS latest_recording_url
          |FROM tasks t
          |  INNER JOIN prospects p on (p.id = t.prospect_id AND p.team_id = t.team_id)
          |  LEFT JOIN prospects_emails pe ON ((pe.prospect_id = p.id) AND (pe.team_id = p.team_id) AND pe.is_primary)
          |  INNER JOIN accounts a on a.id = t.assignee_id
          |WHERE
          |  t.team_id = ?
          |   AND t.assignee_id IN (?, ?, ?, ?)
          |  AND t.done_at = (select done_at from tasks where task_id = ? and team_id = ?)
          |  AND t.status IN (? , ?)
          |limit ?
          |
          |""".stripMargin
      val qry_str = TaskPgDAO.getAllTaskQuery(
          teamId = team_id_9,
          orgId = org_id_9,
          timeZone = "UTC",
          validatedTaskReq = ValidatedTaskReq.ValidatedTaskReqExact(
            timeline = InferredQueryTimeline.Exact(exactIdToCompareTime = ExactIdToCompareTime("24")),
            pageSize = 100
          ),
          reply_sentiment_uuids = None,
          time_based_task_type = Some(TimeBasedTaskType.Completed),
          assignee_ids = None,
          campaign_ids = None,
          task_type = None,
          task_status = None,
          task_priority = None,
          doNotFetchAutomatedDueTasks = false,
          permittedAccountIds = permittedAccountIds,
//          emailNotCompulsoryEnabled = false

        )
        .statement

      val split = expected.split(s"\\s+")
      val rhs = split.reduce((a1, a2) => {
        a1 + " " + a2
      })
      val lhs = qry_str.split("\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })

      assert(lhs == rhs)
    }

    it("should return query for Skipped tasks Exact") {

      val expected =
        """
          |SELECT t.*,
          |    pe.email AS prospect_email,
          |   CONCAT(p.first_name, ' ', p.last_name) AS prospect_name,
          |   CONCAT(a.first_name, ' ', a.last_name) AS assignee_name,
          |       ( SELECT ccl.recording_url as latest_recording_url FROM call_conference_logs ccl WHERE ccl.team_id = t.team_id AND ccl.task_uuid = t.task_id AND ccl.recording_url is not null ORDER BY ccl.log_created_at DESC LIMIT 1 ) AS latest_recording_url
          |FROM tasks t
          |   INNER JOIN prospects p on (p.id = t.prospect_id AND p.team_id = t.team_id)
          |   LEFT JOIN prospects_emails pe ON ((pe.prospect_id = p.id) AND (pe.team_id = p.team_id) AND pe.is_primary)
          |   INNER JOIN accounts a on a.id = t.assignee_id
          |WHERE
          |   t.team_id = ?
          |   AND t.assignee_id IN (?, ?, ?, ?)
          |   AND t.created_via != ?
          |   AND t.skipped_at = (select skipped_at from tasks where task_id = ? and team_id = ?)
          |   AND t.status = ?
          |limit ?
          |
          |""".stripMargin
      val qry_str = TaskPgDAO.getAllTaskQuery(
          teamId = team_id_9,
          orgId = org_id_9,
          timeZone = "UTC",
          validatedTaskReq = ValidatedTaskReq.ValidatedTaskReqExact(
            timeline = InferredQueryTimeline.Exact(exactIdToCompareTime = ExactIdToCompareTime("24")),
            pageSize = 100
          ),
          reply_sentiment_uuids = None,
          time_based_task_type = Some(TimeBasedTaskType.Skipped),
          assignee_ids = None,
          campaign_ids = None,
          task_type = None,
          task_status = None,
          task_priority = None,
          doNotFetchAutomatedDueTasks = false,
          permittedAccountIds = permittedAccountIds,
//          emailNotCompulsoryEnabled = false

        )
        .statement

      val split = expected.split(s"\\s+")
      val rhs = split.reduce((a1, a2) => {
        a1 + " " + a2
      })
      val lhs = qry_str.split("\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })

      assert(lhs == rhs)
    }

    it("should return query for archived tasks Before") {

      val expected =
        """
          |SELECT t.*,
          |   pe.email AS prospect_email,
          |   CONCAT(p.first_name, ' ', p.last_name) AS prospect_name,
          |   CONCAT(a.first_name, ' ', a.last_name) AS assignee_name,
          |       ( SELECT ccl.recording_url as latest_recording_url FROM call_conference_logs ccl WHERE ccl.team_id = t.team_id AND ccl.task_uuid = t.task_id AND ccl.recording_url is not null ORDER BY ccl.log_created_at DESC LIMIT 1 ) AS latest_recording_url
          |FROM tasks t
          |   INNER JOIN prospects p on (p.id = t.prospect_id AND p.team_id = t.team_id)
          |   LEFT JOIN prospects_emails pe ON ((pe.prospect_id = p.id) AND (pe.team_id = p.team_id) AND pe.is_primary)
          |   INNER JOIN accounts a on a.id = t.assignee_id
          |WHERE
          |   t.team_id = ?
          |   AND t.assignee_id IN (?, ?, ?, ?)
          |   AND t.created_via != ?
          |   AND t.archive_at < ?
          |   AND t.status = ?
          |ORDER BY
          |   t.archive_at desc
          |limit ?
          |
          |""".stripMargin
      val qry_str = TaskPgDAO.getAllTaskQuery(
          teamId = team_id_9,
          orgId = org_id_9,
          timeZone = "UTC",
          validatedTaskReq = ValidatedTaskReq.ValidatedTaskReqRange(
            timeline = InferredQueryTimeline.Range.Before(aDate),
            pageSize = 100
          ),
          reply_sentiment_uuids = None,
          time_based_task_type = Some(TimeBasedTaskType.Archived),
          assignee_ids = None,
          campaign_ids = None,
          task_type = None,
          task_status = None,
          task_priority = None,
          doNotFetchAutomatedDueTasks = false,
          permittedAccountIds = permittedAccountIds,
//          emailNotCompulsoryEnabled = false
        )
        .statement

      val split = expected.split(s"\\s+")
      val rhs = split.reduce((a1, a2) => {
        a1 + " " + a2
      })
      val lhs = qry_str.split("\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })

      assert(lhs == rhs)
    }

    it("should return query for archived tasks After") {

      val expected =
        """
          |SELECT t.*,
          |   pe.email AS prospect_email,
          |   CONCAT(p.first_name, ' ', p.last_name) AS prospect_name,
          |   CONCAT(a.first_name, ' ', a.last_name) AS assignee_name,
          |       ( SELECT ccl.recording_url as latest_recording_url FROM call_conference_logs ccl WHERE ccl.team_id = t.team_id AND ccl.task_uuid = t.task_id AND ccl.recording_url is not null ORDER BY ccl.log_created_at DESC LIMIT 1 ) AS latest_recording_url
          |FROM tasks t
          |   INNER JOIN prospects p on (p.id = t.prospect_id AND p.team_id = t.team_id)
          |   LEFT JOIN prospects_emails pe ON ((pe.prospect_id = p.id) AND (pe.team_id = p.team_id) AND pe.is_primary)
          |   INNER JOIN accounts a on a.id = t.assignee_id
          |WHERE
          |   t.team_id = ?
          |   AND t.assignee_id IN (?, ?, ?, ?)
          |   AND t.created_via != ?
          |   AND t.archive_at > ?
          |   AND t.status = ?
          |ORDER BY
          |   t.archive_at asc
          |limit ?
          |
          |""".stripMargin
      val qry_str = TaskPgDAO.getAllTaskQuery(
          teamId = team_id_9,
          orgId = org_id_9,
          timeZone = "UTC",
          reply_sentiment_uuids = None,
          validatedTaskReq = ValidatedTaskReq.ValidatedTaskReqRange(
            timeline = InferredQueryTimeline.Range.After(aDate),
            pageSize = 100
          ),
          time_based_task_type = Some(TimeBasedTaskType.Archived),
          assignee_ids = None,
          campaign_ids = None,
          task_type = None,
          task_status = None,
          task_priority = None,
          doNotFetchAutomatedDueTasks = false,
          permittedAccountIds = permittedAccountIds,
//          emailNotCompulsoryEnabled = false
        )
        .statement

      val split = expected.split(s"\\s+")
      val rhs = split.reduce((a1, a2) => {
        a1 + " " + a2
      })
      val lhs = qry_str.split("\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })

      assert(lhs == rhs)
    }

    it("should return query for archived tasks Exact") {

      val expected =
        """
          |SELECT t.*,
          |   pe.email AS prospect_email,
          |   CONCAT(p.first_name, ' ', p.last_name) AS prospect_name,
          |   CONCAT(a.first_name, ' ', a.last_name) AS assignee_name,
          |       ( SELECT ccl.recording_url as latest_recording_url FROM call_conference_logs ccl WHERE ccl.team_id = t.team_id AND ccl.task_uuid = t.task_id AND ccl.recording_url is not null ORDER BY ccl.log_created_at DESC LIMIT 1 ) AS latest_recording_url
          |FROM tasks t
          |   INNER JOIN prospects p on (p.id = t.prospect_id AND p.team_id = t.team_id)
          |   LEFT JOIN prospects_emails pe ON ((pe.prospect_id = p.id) AND (pe.team_id = p.team_id) AND pe.is_primary)
          |   INNER JOIN accounts a on a.id = t.assignee_id
          |WHERE
          |   t.team_id = ?
          |   AND t.assignee_id IN (?, ?, ?, ?)
          |   AND t.created_via != ?
          |   AND t.reply_sentiment_uuid IN (?)
          |   AND t.archive_at = (select archived_at from tasks where task_id = ? and team_id = ?)
          |   AND t.status = ?
          |limit ?
          |
          |""".stripMargin

      val qry_str = TaskPgDAO.getAllTaskQuery(
          teamId = team_id_9,
          orgId = org_id_9,
          timeZone = "UTC",
          reply_sentiment_uuids = Some(List(ReplySentimentUuid(uuid = "reply_sentiment_uuid_1"))),
          validatedTaskReq = ValidatedTaskReq.ValidatedTaskReqExact(
            timeline = InferredQueryTimeline.Exact(exactIdToCompareTime = ExactIdToCompareTime("24")),
            pageSize = 100
          ),
          time_based_task_type = Some(TimeBasedTaskType.Archived),
          assignee_ids = None,
          campaign_ids = None,
          task_type = None,
          task_status = None,
          task_priority = None,
          doNotFetchAutomatedDueTasks = false,
          permittedAccountIds = permittedAccountIds,
//          emailNotCompulsoryEnabled = false
        )
        .statement

      val split = expected.split(s"\\s+")
      val rhs = split.reduce((a1, a2) => {
        a1 + " " + a2
      })
      val lhs = qry_str.split("\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })

      assert(lhs == rhs)
    }

    it("should include reply_sentiment_uuid in the query") {

      val expected =
        """
          |SELECT t.*,
          |   pe.email AS prospect_email,
          |   CONCAT(p.first_name, ' ', p.last_name) AS prospect_name,
          |   CONCAT(a.first_name, ' ', a.last_name) AS assignee_name,
          |       ( SELECT ccl.recording_url as latest_recording_url FROM call_conference_logs ccl WHERE ccl.team_id = t.team_id AND ccl.task_uuid = t.task_id AND ccl.recording_url is not null ORDER BY ccl.log_created_at DESC LIMIT 1 ) AS latest_recording_url
          |FROM tasks t
          |   INNER JOIN prospects p on (p.id = t.prospect_id AND p.team_id = t.team_id)
          |   LEFT JOIN prospects_emails pe ON ((pe.prospect_id = p.id) AND (pe.team_id = p.team_id) AND pe.is_primary)
          |   INNER JOIN accounts a on a.id = t.assignee_id
          |WHERE
          |   t.team_id = ?
          |   AND t.assignee_id IN (?, ?, ?, ?)
          |   AND t.created_via != ?
          |   AND t.assignee_id IN (?, ?, ?, ?, ?)
          |   AND t.reply_sentiment_uuid IN (?, ?)
          |   AND t.archive_at > ?
          |   AND t.status = ?
          |ORDER BY
          |   t.archive_at asc
          |limit ?
          |
          |""".stripMargin

      val qry_str = TaskPgDAO.getAllTaskQuery(
          teamId = team_id_9,
          orgId = org_id_9,
          timeZone = "UTC",
          reply_sentiment_uuids = Some(List(ReplySentimentUuid(uuid = "reply_sentiment_uuid_2"), ReplySentimentUuid(uuid = "reply_sentiment_uuid_4"))),
          validatedTaskReq = ValidatedTaskReq.ValidatedTaskReqRange(
            timeline = InferredQueryTimeline.Range.After(aDate),
            pageSize = 100
          ),
          time_based_task_type = Some(TimeBasedTaskType.Archived),
          assignee_ids = Some(List(3, 5, 1, 9, 4)),
          campaign_ids = None,
          task_type = None,
          task_status = None,
          task_priority = None,
          doNotFetchAutomatedDueTasks = false,
          permittedAccountIds = permittedAccountIds,
//          emailNotCompulsoryEnabled = false
        )
        .statement

      val split = expected.split(s"\\s+")
      val rhs = split.reduce(
        (a1, a2) => {
          a1 + " " + a2
        }
      )
      val lhs = qry_str.split("\\s+").reduce(
        (s1, s2) => {
          s1 + " " + s2
        }
      )

      assert(lhs == rhs)
    }

  }
  describe("testing taskService.getTaskResponse") {
    // in case of Before it should be in ascending

    it("should return TaskPagination if atSameTimeTasksRes is None in case of InferredQueryTimeline.Range.Before") {

      val res = taskService.getTaskResponse(
        isFirst = false,
        validatedTaskReq = ValidatedTaskReq.ValidatedTaskReqRange(
          timeline = InferredQueryTimeline.Range.Before(DateTime.now()),
          pageSize = 100 // This doesn't matter here
        ),
        atSameTimeTasksRes = None,
        sortedTask = sortedTasks_asc
      )


      assert(res == TaskPagination(
        data = sortedTasks_asc.dropRight(1),
        links = NavigationLinks(
          prev = Some(sortedTasks_asc.head.due_at),
          next = Some(sortedTasks_asc.dropRight(1).last.due_at)
        )
      ))

    }

    it("should return TaskPagination if atSameTimeTasksRes is None in case of InferredQueryTimeline.Range.After") {

      val final_task = sortedTask_desc.drop(1)
      val prev = Some(final_task.head.due_at)
      val next = Some(final_task.last.due_at)
      val expectedResponse = TaskPagination(
        data = final_task,
        links = NavigationLinks(
          prev = prev,
          next = next
        )
      )

      val res = taskService.getTaskResponse(
        isFirst = false,
        validatedTaskReq = ValidatedTaskReq.ValidatedTaskReqRange(
          timeline = InferredQueryTimeline.Range.After(aDate),
          pageSize = 100
        ),
        atSameTimeTasksRes = None,
        sortedTask = sortedTask_desc
      )

      assert(res == expectedResponse)

    }

    it("should return Task Pagination with final_task i.e sorted_task ++ atSameTimeTask concatenated with proper adjustments in case of InferredQueryTimeline.Range.Before") {


      val lastTask = atSameTimeTasks.last
      val final_task = sortedTask_asc_last_two_same.filterNot {
        case (task) =>
          task.due_at == lastTask.due_at
      } ++ atSameTimeTasks
      val expectedResponse = TaskPagination(
        data = final_task,
        links = NavigationLinks(
          prev = Some(sortedTask_asc_last_two_same.head.due_at),
          next = Some(lastTask.due_at)
        )
      )

      val res = taskService.getTaskResponse(
        isFirst = false,
        validatedTaskReq = ValidatedTaskReq.ValidatedTaskReqRange(
          timeline = InferredQueryTimeline.Range.Before(aDate),
          pageSize = 100
        ),
        atSameTimeTasksRes = Some(atSameTimeTasks),
        sortedTask = sortedTask_asc_last_two_same
      )

      assert(res == expectedResponse)

    }

    it("should return Task Pagination with final_task i.e sorted_task ++ atSameTimeTask concatenated with proper adjustments in case of InferredQueryTimeline.Range.After") {

      val firstTask = atSameTimeTasks.head
      val final_task = atSameTimeTasks ++ sortedTask_desc_last_two_same.filterNot {
        case (task) =>
          task.due_at == firstTask.due_at
      }

      val expectedResponse = TaskPagination(
        data = final_task,
        links = NavigationLinks(
          prev = Some(final_task.head.due_at),
          next = Some(final_task.last.due_at)
        )
      )

      val res = taskService.getTaskResponse(
        isFirst = false,
        validatedTaskReq = ValidatedTaskReq.ValidatedTaskReqRange(
          timeline = InferredQueryTimeline.Range.After(aDate),
          pageSize = 100
        ),
        atSameTimeTasksRes = Some(atSameTimeTasks),
        sortedTask = sortedTask_desc_last_two_same
      )

      assert(res == expectedResponse)

    }

  }


  describe("Testing taskService.getTaskFilterCount") {

    it("should fail when date range does not have to but has from") {


      val res = Try(
        Await.result(taskService.getTaskFilterCount(
        team_id = team_id_9,
        orgId = org_id_9,
        assignee_id = None,
        reply_sentiment_uuid_opt = None,
        campaign_id = None,
        task_priority = List(),
        duration_from = Some(1736533800000L),
        duration_to = None,
        timeZone = "UTC",
        doNotFetchAutomatedDueTasks = false,
        permittedAccountIds = permittedAccountIds), 1.second
        ))


     assert(res.isFailure)

    }

    it("should fail when date range does not have from but has to") {


      val res = Try(
        Await.result(
        taskService.getTaskFilterCount(
        team_id = team_id_9,
        orgId = org_id_9,
        assignee_id = None,
        reply_sentiment_uuid_opt = None,
        campaign_id = None,
        task_priority = List(),
        duration_from = None,
        duration_to = Some(1739298599999L),
        timeZone = "UTC",
        doNotFetchAutomatedDueTasks = false,
        permittedAccountIds = permittedAccountIds), 1.second
      ))

      assert(res.isFailure)

    }

    it("should give count for tasks if to and from present") {

      val dateRange = DateRangeBetween(
        startDate = new DateTime(1736533800000L),
        endDate = new DateTime(1739298599999L)
      )

      (taskDaoService.getAllTasksForCount(
        _: Long,
        _: OrgId,
        _: Option[Long],
        _: Option[Long],
        _: Option[ReplySentimentUuid],
        _: List[TaskPriority],
        _: Boolean,
        _: Option[DateRangeBetween],
        _: Seq[Long],
      )(_: ExecutionContext, _: SRLogger))
        .expects(team_id_9, *, None, None, None, List(), false, Some(dateRange), permittedAccountIds, *, *)
        .returning(Future.successful(tasks))


      val res = taskService.getTaskFilterCount(
        team_id = team_id_9,
        orgId = org_id_9,
        assignee_id = None,
        reply_sentiment_uuid_opt = None,
        campaign_id = None,
        task_priority = List(),
        duration_from = Some(1736533800000L),
        duration_to = Some(1739298599999L),
        timeZone = "UTC",
        doNotFetchAutomatedDueTasks = false,
        permittedAccountIds = permittedAccountIds
      )

      res.map(taskCount => {
        assert(taskCount == taskCount)
      })

    }

    it("should give count for tasks") {

      (taskDaoService.getAllTasksForCount(
        _: Long,
        _: OrgId,
        _: Option[Long],
        _: Option[Long],
        _: Option[ReplySentimentUuid],
        _: List[TaskPriority],
        _: Boolean,
        _: Option[DateRangeBetween],
        _: Seq[Long],
//        _: Boolean
      )(_: ExecutionContext, _: SRLogger))
        .expects(team_id_9, *, None, None, None, List(), false, None, permittedAccountIds, *,*)
        .returning(Future.successful(tasks))


//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(_: TeamId, _: SrRollingUpdateFeature)(_: SRLogger))
//        .expects(TeamId(team_id_9), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .returning(false)

      val res = taskService.getTaskFilterCount(
        team_id = team_id_9,
        orgId = org_id_9,
        assignee_id = None,
        reply_sentiment_uuid_opt = None,
        campaign_id = None,
        task_priority = List(),
        duration_from = None,
        duration_to = None,
        timeZone = "UTC",
        doNotFetchAutomatedDueTasks = false,
        permittedAccountIds = permittedAccountIds
      )

      res.map(taskCount => {
        assert(taskCount == taskCount)
      })

    }

    it("should count completed task with reply sentiment") {

      val replySentimentUuid = Some(ReplySentimentUuid(uuid = "rep_sen_uuid_1"))

      val completedTaskWithReplySentiment = todayTask.copy(
        task_id = "task_rep_1",
        reply_sentiment_uuid = replySentimentUuid,
        status = TaskStatus.Done(done_at = DateTime.now(), done_by = Some(3))
      )

      (taskDaoService.getAllTasksForCount(
        _: Long,
        _: OrgId,
        _: Option[Long],
        _: Option[Long],
        _: Option[ReplySentimentUuid],
        _: List[TaskPriority],
        _: Boolean,
        _: Option[DateRangeBetween],
        _: Seq[Long],
//        _: Boolean
      )(_: ExecutionContext, _: SRLogger))
        .expects(team_id_9, *, None, None, replySentimentUuid, List(), false, None, permittedAccountIds, *, *)
        .returning(Future.successful(tasks :+ completedTaskWithReplySentiment))

//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(_: TeamId, _: SrRollingUpdateFeature)(_: SRLogger))
//        .expects(TeamId(team_id_9), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .returning(false)

      val res = taskService.getTaskFilterCount(
        team_id = team_id_9,
        orgId = org_id_9,
        assignee_id = None,
        reply_sentiment_uuid_opt = replySentimentUuid,
        campaign_id = None,
        task_priority = List(),
        timeZone = "UTC",
        duration_from = None,
        duration_to = None,
        doNotFetchAutomatedDueTasks = false,
        permittedAccountIds = permittedAccountIds
      )

      res.map(
        taskCount => {
          assert(taskCount.completed.all == 1)
        }
      )

    }

    val due_tasks = List(
      dueTask.copy(task_type = TaskType.ViewLinkedinProfile, task_data = TaskData.ViewLinkedinProfileData(task_type = TaskType.ViewLinkedinProfile)),
    )

    val due_sub_count = SubCount(
      all = due_tasks.length,
      email = due_tasks.count(t => t.task_type == TaskType.SendEmail),
      linkedin = due_tasks.count(t => t.task_type == TaskType.ViewLinkedinProfile),
      sms = due_tasks.count(t => t.task_type == TaskType.SendSms),
      whatsapp = due_tasks.count(t => t.task_type == TaskType.SendWhatsAppMessage),
      call = due_tasks.count(t => t.task_type == TaskType.CallTask),
      generic = due_tasks.count(t => t.task_type == TaskType.GeneralTask),
        approval_email = 0,
        approval_call = 0,
        approval_linkedin = 0,
        approval_sms = 0,
        approval_whatsapp = 0,
        approval_generic = 0
    )

    val due_task_count = TaskCount(
      todayAndDue = due_sub_count,
      today = zeroCount,
      upcoming = zeroCount,
      due = due_sub_count,
      completed = zeroCount,
      skipped = zeroCount,
      snoozed = zeroCount,
      failed = zeroCount
    )

    it("should give count for due tasks") {

      (taskDaoService.getAllTasksForCount(
        _: Long,
        _: OrgId,
        _: Option[Long],
        _: Option[Long],
        _: Option[ReplySentimentUuid],
        _: List[TaskPriority],
        _: Boolean,
        _: Option[DateRangeBetween],
        _: Seq[Long],
//        _: Boolean
      )(_: ExecutionContext, _: SRLogger))
        .expects(team_id_9, *, None, None, None, List(), false, None, permittedAccountIds, *, *)
        .returning(Future.successful(due_tasks))

//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(_: TeamId, _: SrRollingUpdateFeature)(_: SRLogger))
//        .expects(TeamId(team_id_9), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .returning(false)

      val res = taskService.getTaskFilterCount(
        team_id = team_id_9,
        orgId = org_id_9,
        assignee_id = None,
        reply_sentiment_uuid_opt = None,
        campaign_id = None,
        task_priority = List(),
        timeZone = "UTC",
        duration_from = None,
        duration_to = None,
        doNotFetchAutomatedDueTasks = false,
        permittedAccountIds = permittedAccountIds
      )

      res.map(taskCount => {
        assert(taskCount == due_task_count)
      })

    }

    val upcoming_tasks = List(
      todayTask.copy(status = TaskStatus.Due(due_at = DateTime.now().plusDays(2)), due_at = DateTime.now().plusDays(2), task_type = TaskType.ViewLinkedinProfile, task_data = TaskData.ViewLinkedinProfileData(task_type = TaskType.ViewLinkedinProfile)),
    )

    val upcoming_subCount = SubCount(
      all = upcoming_tasks.length,
      email = upcoming_tasks.count(t => t.task_type == TaskType.SendEmail),
      linkedin = upcoming_tasks.count(t => t.task_type == TaskType.ViewLinkedinProfile),
      sms = upcoming_tasks.count(t => t.task_type == TaskType.SendSms),
      whatsapp = upcoming_tasks.count(t => t.task_type == TaskType.SendWhatsAppMessage),
      call = upcoming_tasks.count(t => t.task_type == TaskType.CallTask),
      generic = upcoming_tasks.count(t => t.task_type == TaskType.GeneralTask),
        approval_email = 0,
        approval_call = 0,
        approval_linkedin = 0,
        approval_sms = 0,
        approval_whatsapp = 0,
        approval_generic = 0
    )

    val upcoming_task_count = TaskCount(
      todayAndDue = zeroCount,
      today = zeroCount,
      due = zeroCount,
      upcoming = upcoming_subCount,
      completed = zeroCount,
      skipped = zeroCount,
      snoozed = zeroCount,
      failed = zeroCount
    )

    it("should give count for upcoming tasks") {

      (taskDaoService.getAllTasksForCount(
        _: Long,
        _: OrgId,
        _: Option[Long],
        _: Option[Long],
        _: Option[ReplySentimentUuid],
        _: List[TaskPriority],
        _: Boolean,
        _: Option[DateRangeBetween],
        _: Seq[Long],
//        _: Boolean
      )(_: ExecutionContext, _: SRLogger))
        .expects(team_id_9, *, None, None, None, List(), false, None, permittedAccountIds, *, *)
        .returning(Future.successful(upcoming_tasks))


//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(_: TeamId, _: SrRollingUpdateFeature)(_: SRLogger))
//        .expects(TeamId(team_id_9), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .returning(false)

      val res = taskService.getTaskFilterCount(
        team_id = team_id_9,
        orgId = org_id_9,
        assignee_id = None,
        campaign_id = None,
        reply_sentiment_uuid_opt = None,
        task_priority = List(),
        timeZone = "UTC",
        duration_from = None,
        duration_to = None,
        doNotFetchAutomatedDueTasks = false,
        permittedAccountIds = permittedAccountIds
      )

      res.map(taskCount => {
        assert(taskCount == upcoming_task_count)
      })

    }

    val completed_task = List(todayTask.copy(status = TaskStatus.Done(done_at = aDate, done_by = Some(11L))))

    val completed_subCount = SubCount(
      all = completed_task.length,
      email = completed_task.count(t => t.task_type == TaskType.SendEmail),
      linkedin = completed_task.count(t => t.task_type == TaskType.ViewLinkedinProfile),
      sms = completed_task.count(t => t.task_type == TaskType.SendSms),
      whatsapp = completed_task.count(t => t.task_type == TaskType.SendWhatsAppMessage),
      call = completed_task.count(t => t.task_type == TaskType.CallTask),
      generic = completed_task.count(t => t.task_type == TaskType.GeneralTask),
        approval_email = 0,
        approval_call = 0,
        approval_linkedin = 0,
        approval_sms = 0,
        approval_whatsapp = 0,
        approval_generic = 0
    )

    val completed_task_count = TaskCount(
      todayAndDue = zeroCount,
      today = zeroCount,
      upcoming = zeroCount,
      due = zeroCount,
      completed = completed_subCount,
      skipped = zeroCount,
      snoozed = zeroCount,
      failed = zeroCount
    )

    it("should give count for completed tasks") {

      (taskDaoService.getAllTasksForCount(
        _: Long,
        _: OrgId,
        _: Option[Long],
        _: Option[Long],
        _: Option[ReplySentimentUuid],
        _: List[TaskPriority],
        _: Boolean,
        _: Option[DateRangeBetween],
        _: Seq[Long])(_: ExecutionContext, _: SRLogger))
        .expects(team_id_9, *, None, None, None, List(), false, None, permittedAccountIds, *, *)
        .returning(Future.successful(completed_task))


//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(_: TeamId, _: SrRollingUpdateFeature)(_: SRLogger))
//        .expects(TeamId(team_id_9), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .returning(false)

      val res = taskService.getTaskFilterCount(
        team_id = team_id_9,
        orgId = org_id_9,
        assignee_id = None,
        campaign_id = None,
        reply_sentiment_uuid_opt = None,
        task_priority = List(),
        timeZone = "UTC",
        duration_from = None,
        duration_to = None,
        doNotFetchAutomatedDueTasks = false,
        permittedAccountIds = permittedAccountIds
      )

      res.map(taskCount => {
        assert(taskCount == completed_task_count)
      })

    }

    val skipped_task = List(todayTask.copy(status = TaskStatus.Skipped(skipped_at = aDate, skipped_by = Some(11L))))

    val skipped_subCount = SubCount(
      all = skipped_task.length,
      email = skipped_task.count(t => t.task_type == TaskType.SendEmail),
      linkedin = skipped_task.count(t => t.task_type == TaskType.ViewLinkedinProfile),
      sms = skipped_task.count(t => t.task_type == TaskType.SendSms),
      whatsapp = skipped_task.count(t => t.task_type == TaskType.SendWhatsAppMessage),
      call = skipped_task.count(t => t.task_type == TaskType.CallTask),
      generic = skipped_task.count(t => t.task_type == TaskType.GeneralTask),
        approval_email = 0,
        approval_call = 0,
        approval_linkedin = 0,
        approval_sms = 0,
        approval_whatsapp = 0,
        approval_generic = 0
    )

    val skipped_task_count = TaskCount(
      todayAndDue = zeroCount,
      today = zeroCount,
      upcoming = zeroCount,
      due = zeroCount,
      completed = zeroCount,
      skipped = skipped_subCount,
      snoozed = zeroCount,
      failed = zeroCount
    )

    it("should give count for skipped tasks") {

      (taskDaoService.getAllTasksForCount(
        _: Long,
        _: OrgId,
        _: Option[Long],
        _: Option[Long],
        _: Option[ReplySentimentUuid],
        _: List[TaskPriority],
        _: Boolean,
        _: Option[DateRangeBetween],
        _: Seq[Long],
//        _: Boolean
      )(_: ExecutionContext, _: SRLogger))
        .expects(team_id_9, *, None, None, None, List(), false, None, permittedAccountIds, *, *)
        .returning(Future.successful(skipped_task))


//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(_: TeamId, _: SrRollingUpdateFeature)(_: SRLogger))
//        .expects(TeamId(team_id_9), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .returning(false)

      val res = taskService.getTaskFilterCount(
        team_id = team_id_9,
        orgId = org_id_9,
        assignee_id = None,
        campaign_id = None,
        reply_sentiment_uuid_opt = None,
        task_priority = List(),
        timeZone = "UTC",
        duration_from= None,
        duration_to = None,
        doNotFetchAutomatedDueTasks = false,
        permittedAccountIds = permittedAccountIds
      )

      res.map(taskCount => {
        assert(taskCount == skipped_task_count)
      })

    }

    val snoozed_task = List(todayTask.copy(status = TaskStatus.Snoozed(snoozed_till = aDate, snoozed_at = aDate, snoozed_by = 11L)))

    val snoozed_subCount = SubCount(
      all = skipped_task.length,
      email = skipped_task.count(t => t.task_type == TaskType.SendEmail),
      linkedin = skipped_task.count(t => t.task_type == TaskType.ViewLinkedinProfile),
      sms = skipped_task.count(t => t.task_type == TaskType.SendSms),
      whatsapp = skipped_task.count(t => t.task_type == TaskType.SendWhatsAppMessage),
      call = skipped_task.count(t => t.task_type == TaskType.CallTask),
      generic = skipped_task.count(t => t.task_type == TaskType.GeneralTask),
        approval_email = 0,
        approval_call = 0,
        approval_linkedin = 0,
        approval_sms = 0,
        approval_whatsapp = 0,
        approval_generic = 0
    )

    val snoozed_task_count = TaskCount(
      todayAndDue = zeroCount,
      today = zeroCount,
      upcoming = zeroCount,
      due = zeroCount,
      completed = zeroCount,
      skipped = zeroCount,
      snoozed = snoozed_subCount,
      failed = zeroCount
    )

    it("should give count for Snoozed Tasks") {

      (taskDaoService.getAllTasksForCount(
        _: Long,
        _: OrgId,
        _: Option[Long],
        _: Option[Long],
        _: Option[ReplySentimentUuid],
        _: List[TaskPriority],
        _: Boolean,
        _: Option[DateRangeBetween],
        _: Seq[Long],
//        _: Boolean

      )(_: ExecutionContext, _: SRLogger))
        .expects(team_id_9, *, None, None, None, List(), false, None, permittedAccountIds, *, *)
        .returning(Future.successful(snoozed_task))


//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(_: TeamId, _: SrRollingUpdateFeature)(_: SRLogger))
//        .expects(TeamId(team_id_9), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .returning(false)

      val res = taskService.getTaskFilterCount(
        team_id = team_id_9,
        orgId = org_id_9,
        assignee_id = None,
        reply_sentiment_uuid_opt = None,
        campaign_id = None,
        task_priority = List(),
        timeZone = "UTC",
        duration_from = None,
        duration_to = None,
        doNotFetchAutomatedDueTasks = false,
        permittedAccountIds = permittedAccountIds
      )

      res.map(taskCount => {
        assert(taskCount == snoozed_task_count)
      })

    }
  }


  describe("testing taskService.hasTaskCheck") {

    it("should fail if empty list of tasks is returned by findBatchTasksFromScyllaAndPg ") {
      (taskDaoService.findBatchTasksFromScyllaAndPg(
        _: List[String],
        _: Long,
        _: ChangeStatusPermissionCheck,
        _: OrgId,
      )(_: ExecutionContext, _: SRLogger))
        .expects(List(todayTask.task_id), 9L, changeStatusPermissionCheck, *, *, *)
        .returning(Future.successful(List()))


//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(_: TeamId, _: SrRollingUpdateFeature)(_: SRLogger))
//        .expects(TeamId(team_id_9), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .returning(false)

      val res = taskService.hasTaskCheck(
        taskIds = List(todayTask.task_id),
        teamId = 9L,
        orgId = org_id_9,
        permittedAccountIds = permittedAccountIds
      )

      res.map(result => assert(result == Left(TaskAccessError.AccessNotFound)))

    }

    it("should fail if permittedAccountIds doesn't contains returned task assignee Id") {
      (taskDaoService.findBatchTasksFromScyllaAndPg(
        _: List[String],
        _: Long,
        _: ChangeStatusPermissionCheck,
        _: OrgId)(_: ExecutionContext, _: SRLogger))
        .expects(List(todayTask.task_id), 9L, changeStatusPermissionCheck, *, *, *)
        .returning(Future.successful(List(todayTask)))

//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(_: TeamId, _: SrRollingUpdateFeature)(_: SRLogger))
//        .expects(TeamId(team_id_9), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .returning(false)

      val res = taskService.hasTaskCheck(
        taskIds = List(todayTask.task_id),
        teamId = 9L,
        orgId = org_id_9,
        permittedAccountIds = permittedAccountIds // Seq(11L, 12L, 2L, 3L)
      )

      res.map(result => assert(result == Left(TaskAccessError.AccessNotFound)))

    }

    it("should succeed if permittedAccountIds contains returned task assignee Id") {
      (taskDaoService.findBatchTasksFromScyllaAndPg(
        _: List[String],
        _: Long,
        _: ChangeStatusPermissionCheck,
        _: OrgId)(_: ExecutionContext, _: SRLogger))
        .expects(List(todayTask.task_id), 9L, changeStatusPermissionCheck.copy(permittedAccountIds = (permittedAccountIds ++ Seq(1L))), *, *, *)
        .returning(Future.successful(List(todayTask)))


//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(_: TeamId, _: SrRollingUpdateFeature)(_: SRLogger))
//        .expects(TeamId(team_id_9), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .returning(false)

      val res = taskService.hasTaskCheck(
        taskIds = List(todayTask.task_id),
        teamId = 9L,
        orgId = org_id_9,
        permittedAccountIds = permittedAccountIds ++ Seq(1L) // Seq(11L, 12L, 2L, 3L)
      )

      res.map(result => assert(result == Right(List(todayTask))))

    }
  }

  val vectorString = Vector("1", "2", "3")
  val parsedParams: Map[String, Vector[String]] = Map("assignee_id" -> vectorString)


  describe("testing TaskService.assigneeValidation") {
    it("should return parmittedAccountIds if no assignee ids are found") {


      val res = TaskService.assigneeValidation(
        parsedParams = Map(),
        permittedAccountIds = permittedAccountIds.toList
      )

      assert(res == Right(permittedAccountIds))
    }

    it("should fail if unwanted assignee_ids are send via api like string") {


      val res = TaskService.assigneeValidation(
        parsedParams = Map("assignee_id" -> Vector("a", "b", "C")),
        permittedAccountIds = permittedAccountIds.toList
      )

      assert(res == Left(ParamValidationError.ParamValidationFailed(msg = "Error while validating assignee Ids")))

    }


    it("should fail if permittedAccountIds doesn't contain assignee_ids send via api") {

      val res = TaskService.assigneeValidation(
        parsedParams = Map("assignee_id" -> Vector("31", "22", "32")),
        permittedAccountIds = permittedAccountIds.toList // Seq(11L, 12L, 2L, 3L).toList
      )

      assert(res == Left(ParamValidationError.ParamValidationFailed(msg = "Assignee Access Error")))

    }

    it("should succeed if permittedAccountIds contains assignee_ids send via api and return intersection on assignee_id and permittedAccount ids") {

      val res = TaskService.assigneeValidation(
        parsedParams = Map("assignee_id" -> Vector("11", "12")),
        permittedAccountIds = permittedAccountIds.toList // Seq(11L, 12L, 2L, 3L).toList
      )

      val validAssigneeIds = List(11L, 12L)

      assert(res == Right(validAssigneeIds))

    }

  }


  describe("testing TaskService.changeStatus ") {

    val changeStatusPermissionCheck = ChangeStatusPermissionCheck.ManualTaskCheck(
      doer = Some(1L),
      permittedAccountIds = permittedAccountIds,
    )


    it("whats") {

      (() => srDateTimeUtils.getDateTimeNow())
        .expects()
        .returning(aDate)

//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(_: TeamId, _: SrRollingUpdateFeature)(_: SRLogger))
//        .expects(TeamId(team_id_9), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .returning(false)

      (taskDaoService.changeStatus(
        _: String,
        _: OrgId,
        _: UpdateTaskStatus,
        _: DateTime,
        _: Long,
        _: ChangeStatusPermissionCheck
//        _: Boolean
      )(
        _: ExecutionContext,
        _: SRLogger))
        .expects(todayTask.task_id, *, *, aDate, todayTask.team_id, changeStatusPermissionCheck, *, *)
        .returning(Future.successful(Right(todayTask.task_id)))

      (taskDaoService.fetchTask(_: String, _: Long, _: OrgId, _: ChangeStatusPermissionCheck)(_: ExecutionContext, _: SRLogger))
        .expects(todayTask.task_id, todayTask.team_id, *, changeStatusPermissionCheck, *, *)
        .returning(Future.successful(List()))

      val res = taskService.changeStatus(
        task_id = todayTask.task_id,
        task_status = UpdateTaskStatus.Done(),
        team_id = todayTask.team_id,
        changeStatusPermissionCheck = ChangeStatusPermissionCheck.ManualTaskCheck(
          doer = Some(1L),
          permittedAccountIds = permittedAccountIds,
        ),
        orgId = org_id_9
      )


      res.map(result => assert(false))
        .recover {
          case e => logger.error(s"Future failed successfully $e")
            assert(true)
        }

    }


    it("should simply pass if changing status is other than done") {

      (() => srDateTimeUtils.getDateTimeNow())
        .expects()
        .returning(aDate)

//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(_: TeamId, _: SrRollingUpdateFeature)(_: SRLogger))
//        .expects(TeamId(team_id_9), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .returning(false)

      (taskDaoService.changeStatus(

        _: String,
        _: OrgId,
        _: UpdateTaskStatus,
        _: DateTime,
        _: Long,
        _: ChangeStatusPermissionCheck)(
        _: ExecutionContext,
        _: SRLogger))
        .expects(todayTask.task_id, *, *, aDate, todayTask.team_id, changeStatusPermissionCheck, *, *)
        .returning(Future.successful(Right(todayTask.task_id)))


      val res = taskService.changeStatus(
        task_id = todayTask.task_id,
        task_status = UpdateTaskStatus.Snoozed(snoozed_till = aDate.plusDays(2)),
        changeStatusPermissionCheck = ChangeStatusPermissionCheck.ManualTaskCheck(
          doer = Some(1L),
          permittedAccountIds = permittedAccountIds,
        ),
        orgId = org_id_9,
        team_id = todayTask.team_id,
      )


      res.map(result => assert(result == Right(todayTask.task_id)))
        .recover {
          case e => logger.error(s"test Failed $e")
            assert(false)
        }


    }

    it("should simply pass if status is done and because created_via is manual ") {

      (() => srDateTimeUtils.getDateTimeNow())
        .expects()
        .returning(aDate)

//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(_: TeamId, _: SrRollingUpdateFeature)(_: SRLogger))
//        .expects(TeamId(team_id_9), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .returning(false)

      (taskDaoService.changeStatus(
        _: String,
        _: OrgId,
        _: UpdateTaskStatus,
        _: DateTime,
        _: Long,
        _: ChangeStatusPermissionCheck)(
        _: ExecutionContext,
        _: SRLogger))
        .expects(todayTask.task_id, *, *, aDate, todayTask.team_id, changeStatusPermissionCheck, *, *)
        .returning(Future.successful(Right(todayTask.task_id)))

      (taskDaoService.fetchTask(_: String, _: Long, _: OrgId, _: ChangeStatusPermissionCheck)(_: ExecutionContext, _: SRLogger))
        .expects(todayTask.task_id, todayTask.team_id, *, changeStatusPermissionCheck, *, *)
        .returning(Future.successful(List(todayTask)))

      val res = taskService.changeStatus(
        task_id = todayTask.task_id,
        task_status = UpdateTaskStatus.Done(),
        changeStatusPermissionCheck = ChangeStatusPermissionCheck.ManualTaskCheck(
          doer = Some(1L),
          permittedAccountIds = permittedAccountIds,
        ),
        team_id = todayTask.team_id,
        orgId = org_id_9
      )


      res.map(result => assert(result == Right(todayTask.task_id)))
        .recover {
          case e => logger.error(s"test Failed $e")
            assert(false)
        }


    }

    it("should update prospect values and succeed if status is done and because created_via is scheduler") {


//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(_: TeamId, _: SrRollingUpdateFeature)(_: SRLogger))
//        .expects(TeamId(team_id_9), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .returning(false)


      (() => srDateTimeUtils.getDateTimeNow())
        .expects()
        .returning(aDate)
      (campaignProspectStepScheduleLogsDAO.insert(_: Seq[CampaignProspectStepScheduleLogData])(using _: SRLogger))
        .expects(*, *)
        .returning(Success(List(1)))

      (taskDaoService.changeStatus(
        _: String,
        _: OrgId,
        _: UpdateTaskStatus,
        _: DateTime,
        _: Long,
        _: ChangeStatusPermissionCheck)(
        _: ExecutionContext,
        _: SRLogger))
        .expects( todayTask.task_id, *, *, aDate, todayTask.team_id, changeStatusPermissionCheck, *, *)
        .returning(Future.successful(Right(todayTask.task_id)))

      (taskDaoService.fetchTask(_: String, _: Long, _: OrgId, _: ChangeStatusPermissionCheck)(_: ExecutionContext, _: SRLogger))
        .expects(todayTask.task_id, todayTask.team_id, *, changeStatusPermissionCheck, *, *)
        .returning(Future.successful(List(todayTask.copy(
          status = TaskStatus.Done(done_at = aDate, done_by = Some(1L)),
          created_via = TaskCreatedVia.Scheduler,
          campaign_id = Some(1L),
          step_id = Some(2L)))))

      (campaignCacheService.resetCampaignStats(_: Long, _: Long)(using _: SRLogger))
        .expects(*, *, *)
        .returning(())

      (prospectDAOService.updateLatestTaskDoneAt)
        .expects(ProspectId(todayTask.prospect.get.id), TeamId(todayTask.team_id), aDate)
        .returning(Success(1))

      (campaignProspectDAO._hasBeenSent)
        .expects(1L, todayTask.prospect.get.id)
        .returning(Success(0))

      (campaignProspectDAO._updateScheduledStatus(_: Seq[CampaignProspectUpdateScheduleStatus])(using _:SRLogger))
        .expects(Seq(CampaignProspectUpdateScheduleStatus(
          current_step_status_for_scheduler_data = CurrentStepStatusForSchedulerData.Done(done_at = aDate),
          current_step_type = CampaignStepType.fromKey(todayTask.task_type.toKey).get,
          current_step_task_id = todayTask.task_id,
          step_id = 2L,
          campaign_id = 1L,
          prospect_id = todayTask.prospect.get.id,
          email_message_id = None,
          current_campaign_email_settings_id = None
        )), *)
        .returning(Success(Seq(todayTask.prospect.get.id)))

      val res = taskService.changeStatus(
        task_id = todayTask.task_id,
        task_status = UpdateTaskStatus.Done(),
        changeStatusPermissionCheck = ChangeStatusPermissionCheck.ManualTaskCheck(
          doer = Some(1L),
          permittedAccountIds = permittedAccountIds,
        ),
        team_id = todayTask.team_id,
        orgId = org_id_9
      )


      res.map(result => assert(result == Right(todayTask.task_id)))
        .recover {
          case e => logger.error(s"test Failed $e")
            assert(false)
        }


    }

    it("should update prospect values and succeed if status is skipped and because created_via is scheduler") {

      (() => srDateTimeUtils.getDateTimeNow())
        .expects()
        .returning(aDate)

//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(_: TeamId, _: SrRollingUpdateFeature)(_: SRLogger))
//        .expects(TeamId(team_id_9), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .returning(false)

      (taskDaoService.changeStatus(
        _: String,
        _: OrgId,
        _: UpdateTaskStatus,
        _: DateTime,
        _: Long,
        _: ChangeStatusPermissionCheck)(
        _: ExecutionContext,
        _: SRLogger))
        .expects(todayTask.task_id, *, *, aDate, todayTask.team_id, changeStatusPermissionCheck.copy(doer= Some(3L)), *, *)
        .returning(Future.successful(Right(todayTask.task_id)))

      (taskDaoService.fetchTask(_: String, _: Long, _: OrgId, _: ChangeStatusPermissionCheck)(_: ExecutionContext, _: SRLogger))
        .expects(todayTask.task_id, todayTask.team_id, *, changeStatusPermissionCheck.copy(doer= Some(3L)), *, *)
        .returning(Future.successful(List(todayTask.copy(
          status = TaskStatus.Skipped(skipped_at = aDate, skipped_by = Some(3L)),
          created_via = TaskCreatedVia.Scheduler,
          campaign_id = Some(5L),
          step_id = Some(7L)))))


      (campaignProspectDAO._updateScheduledStatus(_: Seq[CampaignProspectUpdateScheduleStatus])(using _:SRLogger))
        .expects(Seq(CampaignProspectUpdateScheduleStatus(
          current_step_status_for_scheduler_data = CurrentStepStatusForSchedulerData.Skipped(skipped_at = aDate),
          current_step_type = CampaignStepType.fromKey(todayTask.task_type.toKey).get,
          current_step_task_id = todayTask.task_id,
          step_id = 7L,
          campaign_id = 5L,
          prospect_id = todayTask.prospect.get.id,
          email_message_id = None,
          current_campaign_email_settings_id = None
        )), *)
        .returning(Success(Seq(todayTask.prospect.get.id)))

      (campaignCacheService.resetCampaignStats(_: Long, _: Long)(using _: SRLogger))
        .expects(*, *, *)
        .returning(())

      val res = taskService.changeStatus(
        task_id = todayTask.task_id,
        task_status = UpdateTaskStatus.Skipped(),
        changeStatusPermissionCheck = ChangeStatusPermissionCheck.ManualTaskCheck(
          doer = Some(3L),
          permittedAccountIds = permittedAccountIds,
        ),
        team_id = todayTask.team_id,
        orgId = org_id_9
      )


      res.map(result => assert(result == Right(todayTask.task_id)))
        .recover {
          case e => logger.error(s"test Failed $e")
            assert(false)
        }
    }

    it("should fail as CurrentStepStatusForScheduler.fromString fails to convert ") {

      (() => srDateTimeUtils.getDateTimeNow())
        .expects()
        .returning(aDate)

//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(_: TeamId, _: SrRollingUpdateFeature)(_: SRLogger))
//        .expects(TeamId(team_id_9), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .returning(false)

      (taskDaoService.changeStatus(
        _: String,
        _: OrgId,
        _: UpdateTaskStatus,
        _: DateTime,
        _: Long,
        _: ChangeStatusPermissionCheck)(
        _: ExecutionContext,
        _: SRLogger))
        .expects(todayTask.task_id, *, *, aDate, todayTask.team_id, changeStatusPermissionCheck.copy(doer = Some(3L)), *, *)
        .returning(Future.successful(Right(todayTask.task_id)))

      (taskDaoService.fetchTask(_: String, _: Long, _: OrgId, _: ChangeStatusPermissionCheck)(_: ExecutionContext, _: SRLogger))
        .expects(todayTask.task_id, todayTask.team_id, *, changeStatusPermissionCheck.copy(doer= Some(3L)), *, *)
        .returning(Future.successful(List(todayTask.copy(
          status = TaskStatus.Skipped(skipped_at = aDate, skipped_by = Some(3L)),
          created_via = TaskCreatedVia.Scheduler,
          campaign_id = Some(5L),
          step_id = Some(7L)))))


      (campaignProspectDAO._updateScheduledStatus(_: Seq[CampaignProspectUpdateScheduleStatus])(using _:SRLogger))
        .expects(Seq(CampaignProspectUpdateScheduleStatus(
          current_step_status_for_scheduler_data = CurrentStepStatusForSchedulerData.Skipped(skipped_at = aDate),
          current_step_type = CampaignStepType.fromKey(todayTask.task_type.toKey).get,
          current_step_task_id = todayTask.task_id,
          step_id = 7L,
          campaign_id = 5L,
          prospect_id = todayTask.prospect.get.id,
          email_message_id = None,
          current_campaign_email_settings_id = None
        )), *)
        .returning(Success(Seq(todayTask.prospect.get.id)))

      val res = taskService.changeStatus(
        task_id = todayTask.task_id,
        task_status = UpdateTaskStatus.Skipped(),
        changeStatusPermissionCheck = ChangeStatusPermissionCheck.ManualTaskCheck(
          doer = Some(3L),
          permittedAccountIds = permittedAccountIds,
        ),
        orgId = org_id_9,
        team_id = todayTask.team_id,
      )


      res.map(result => assert(result == Right(todayTask.task_id)))
        .recover {
          case e => logger.error(s"test Failed $e")
            assert(false)
        }
    }
  }

  val reply_sentiment_uuid_VC = ReplySentimentUuid(
    uuid = "reply sentiment uuid"
  )

  val positiveSentiment = ReplySentimentForTeam(
    uuid = reply_sentiment_uuid_VC,
    reply_sentiment = ReplySentimentTypeData.PositiveData(
      replySentimentSubCategory = ReplySentimentSubCategory.EmailPositiveWantsDemo,
      replySentimentChannelType = ReplySentimentChannelType.EmailChannelType
    )
  )

  val propsect_id_VC = ProspectId(
    id = 3
  )

  val campaign_id: Long = 3
  val campaign_id_VC = CampaignId(
    id = campaign_id
  )


  describe("testing taskService.addTaskSentiment") {

    val pe = CaptureOne[Seq[CreateProspectEventDB]]()


    it("should fail if taskDaoService.saveSentimentForTask fails") {

      (() => dbUtils.startLocalTx())
        .expects()
        .returning(DbAndSession(null, null))

      (taskDaoService.saveSentimentForTask(_: TaskUuid, _: TeamId, _: ReplySentimentUuid)(_: DBSession, _: SRLogger))
        .expects(task_uuid_VC, team_id_VC, reply_sentiment_uuid_VC, *, *)
        .returning(Failure(error))

      (dbUtils.commitAndCloseSession)
        .expects(*)
        .returning({})

      val res = taskService.addReplySentimentForTask(
        accountId = accountId,
        campaignId = None,
        teamId = team_id_VC,
        taskUuid = task_uuid_VC,
        replySentimentUuid = reply_sentiment_uuid_VC,
        permittedAccountIds = Seq(accountId.id),
        prospect_id = propsect_id_VC,
        call_sp_sid = None,
        task_type = TaskType.SendSms,
        conference_uuid = None
      )

      //      res.map(s => assert(false))
      //        .recover(e => assert(e.getMessage == error.getMessage))
      res match {

        case Success(res) =>

          assert(false)

        case Failure(e) =>
          assert(e.getMessage == error.getMessage)
      }

    }
    it("should fail if prospectDAO.addingLatestReplySentimentForProspect fails") {

      (() => dbUtils.startLocalTx())
        .expects()
        .returning(DbAndSession(null, null))

      (taskDaoService.saveSentimentForTask(_: TaskUuid, _: TeamId, _: ReplySentimentUuid)(_: DBSession, _: SRLogger))
        .expects(task_uuid_VC, team_id_VC, reply_sentiment_uuid_VC, *, *)
        .returning(Success(task_uuid_VC))
      (prospectDAO.addingLatestReplySentimentForProspect(_: Long, _: Seq[Long], _: ReplySentimentUuid)(_: DBSession))
        .expects(team_id_VC.id, Seq(propsect_id_VC.id), reply_sentiment_uuid_VC, *)
        .returning(Failure(error))

      (replySentimentDAO.getReplySentimentsForUUID(_:Long,_:ReplySentimentUuid)(_:SRLogger))
        .expects(team_id_VC.id, reply_sentiment_uuid_VC,*)
        .returns(Success(None))

      (dbUtils.commitAndCloseSession)
        .expects(*)
        .returning({})

      val res = taskService.addReplySentimentForTask(
        accountId = accountId,
        campaignId = None,
        teamId = team_id_VC,
        taskUuid = task_uuid_VC,
        replySentimentUuid = reply_sentiment_uuid_VC,
        permittedAccountIds = Seq(accountId.id),
        prospect_id = propsect_id_VC,
        call_sp_sid = None,
        conference_uuid = None,
        task_type = TaskType.SendSms
      )

      //      res.map(s => assert(false))
      //        .recover(e => assert(e.getMessage == error.getMessage))
      res match {

        case Success(res) =>

          assert(false)

        case Failure(e) =>
          assert(e.getMessage == error.getMessage)
      }

    }

    it("should success if campaign id is present") {

      (() => dbUtils.startLocalTx())
        .expects()
        .returning(DbAndSession(null, null))

      (taskDaoService.saveSentimentForTask(_: TaskUuid, _: TeamId, _: ReplySentimentUuid)(_: DBSession, _: SRLogger))
        .expects(task_uuid_VC, team_id_VC, reply_sentiment_uuid_VC, *, *)
        .returning(Success(task_uuid_VC))
      (campaignProspectDAO.addReplySentimentViaTask(_: CampaignId, _: ReplySentimentUuid, _: ProspectId, _: TeamId, _: ReplySentimentUpdatedBy)(_: DBSession))
        .expects(campaign_id_VC, reply_sentiment_uuid_VC, propsect_id_VC, team_id_VC, *, *)
        .returning(Success(None))
      (replySentimentDAO.getReplySentimentsForUUID(_:Long,_:ReplySentimentUuid)(_:SRLogger))
        .expects(team_id_VC.id, reply_sentiment_uuid_VC,*)
        .returns(Success(None))
      (prospectDAO.addingLatestReplySentimentForProspect(_: Long, _: Seq[Long], _: ReplySentimentUuid)(_: DBSession))
        .expects(team_id_VC.id, Seq(propsect_id_VC.id), reply_sentiment_uuid_VC, *)
        .returning(Success(List(9)))

      (mqAutoUpdateProspectCategoryPublisher.publish(_: MqAutoUpdateProspectCategoryMsg, _: String, _: Int))
        .expects(MqAutoUpdateProspectCategoryMsg(
          teamId = team_id_VC,
          doerAccountId = accountId,
          prospectIds = Seq(propsect_id_VC),
          replySentimentUuid = Some(reply_sentiment_uuid_VC),
          newProspectCategory = None
        ), MQConfig.autoUpdateProspectCategoryQueueBaseName, MQConfig.autoUpdateProspectCategoryPrefetchCount)
        .returning(Success(()))

      (eventLogService.createEventLog(_: EventDataType, _: Long, _: Long, _: DateTime)(using _: SRLogger))
        .expects(*, team_id_VC.id,accountId.id, *, *)
        .returning(Right("success"))

      (prospectAddEventDAO.addEvents(_: Seq[CreateProspectEventDB]))
        .expects(capture(pe))
        .returning(Success(List(1L)))

      (dbUtils.commitAndCloseSession)
        .expects(*)
        .returning({})


      val res = taskService.addReplySentimentForTask(
        accountId = accountId,
        campaignId = Some(campaign_id_VC),
        teamId = team_id_VC,
        taskUuid = task_uuid_VC,
        replySentimentUuid = reply_sentiment_uuid_VC,
        permittedAccountIds = Seq(accountId.id),
        prospect_id = propsect_id_VC,
        call_sp_sid = None,
        conference_uuid = None,
        task_type = TaskType.SendSms
      )

      //      res.map(s => assert(s == true))
      //        .recover(e => assert(false))

      res match {

        case Success(res) =>

          assert(res == true)

        case Failure(e) =>
          logger.fatal(e.toString)
          println(LogHelpers.getStackTraceAsString(e))
          assert(false)
      }

    }


    it("should publish to mq when positive and campaign id is present") {

      val pauseCampaignData: PauseCampaignData = PauseCampaignData(
        campaignId = campaign_id_VC,
        accountId = accountId,
        teamId = team_id_VC,
        taskUuid = task_uuid_VC,
        replySentimentUuid = reply_sentiment_uuid_VC,
        permittedAccountIds = Seq(accountId.id),
        prospect_id = propsect_id_VC,
        task_type = TaskType.SendSms
      )

      (() => dbUtils.startLocalTx())
        .expects()
        .returning(DbAndSession(null, null))

      (taskDaoService.saveSentimentForTask(_: TaskUuid, _: TeamId, _: ReplySentimentUuid)(_: DBSession, _: SRLogger))
        .expects(task_uuid_VC, team_id_VC, reply_sentiment_uuid_VC, *, *)
        .returning(Success(task_uuid_VC))
      (campaignProspectDAO.addReplySentimentViaTask(_: CampaignId, _: ReplySentimentUuid, _: ProspectId, _: TeamId,  _: ReplySentimentUpdatedBy)(_: DBSession))
        .expects(campaign_id_VC, reply_sentiment_uuid_VC, propsect_id_VC, team_id_VC, *, *)
        .returning(Success(None))
      (replySentimentDAO.getReplySentimentsForUUID(_:Long,_:ReplySentimentUuid)(_:SRLogger))
        .expects(team_id_VC.id, reply_sentiment_uuid_VC,*)
        .returns(Success(Some(positiveSentiment)))

      (prospectDAO.addingLatestReplySentimentForProspect(_: Long, _: Seq[Long], _: ReplySentimentUuid)(_: DBSession))
        .expects(team_id_VC.id, Seq(propsect_id_VC.id), reply_sentiment_uuid_VC, *)
        .returning(Success(List(9)))

      (mqAutoUpdateProspectCategoryPublisher.publish(_: MqAutoUpdateProspectCategoryMsg, _: String, _: Int))
        .expects(MqAutoUpdateProspectCategoryMsg(
          teamId = team_id_VC,
          doerAccountId = accountId,
          prospectIds = Seq(propsect_id_VC),
          replySentimentUuid = Some(reply_sentiment_uuid_VC),
          newProspectCategory = None
        ), MQConfig.autoUpdateProspectCategoryQueueBaseName, MQConfig.autoUpdateProspectCategoryPrefetchCount)
        .returning(Success(()))

      (prospectAddEventDAO.addEvents(_: Seq[CreateProspectEventDB]))
        .expects(capture(pe))
        .returning(Success(List(1L)))

      (eventLogService.createEventLog(_: EventDataType, _: Long, _: Long, _: DateTime)(using _: SRLogger))
        .expects(*, team_id_VC.id, accountId.id, *, *)
        .returning(Right("success"))

      (mqPauseCampaignOnReplySentimentSelect.publish(_: PauseCampaignData))
        .expects(pauseCampaignData)
        .returning(Success(()))

      (dbUtils.commitAndCloseSession)
        .expects(*)
        .returning({})


      val res = taskService.addReplySentimentForTask(
        accountId = accountId,
        campaignId = Some(campaign_id_VC),
        teamId = team_id_VC,
        taskUuid = task_uuid_VC,
        replySentimentUuid = reply_sentiment_uuid_VC,
        permittedAccountIds = Seq(accountId.id),
        prospect_id = propsect_id_VC,
        call_sp_sid = None,
        conference_uuid = None,
        task_type = TaskType.SendSms
      )

      //      res.map(s => assert(s == true))
      //        .recover(e => assert(false))

      res match {

        case Success(res) =>

          assert(res == true)

        case Failure(e) =>
          logger.fatal(e.toString)
          assert(false)
      }

    }

    it("should publish to mq when negative and campaign id is present") {

      val pauseCampaignData: PauseCampaignData = PauseCampaignData(
        campaignId = campaign_id_VC,
        accountId = accountId,
        teamId = team_id_VC,
        taskUuid = task_uuid_VC,
        replySentimentUuid = reply_sentiment_uuid_VC,
        permittedAccountIds = Seq(accountId.id),
        prospect_id = propsect_id_VC,
        task_type = TaskType.SendSms
      )

      (() => dbUtils.startLocalTx())
        .expects()
        .returning(DbAndSession(null, null))

      (taskDaoService.saveSentimentForTask(_: TaskUuid, _: TeamId, _: ReplySentimentUuid)(_: DBSession, _: SRLogger))
        .expects(task_uuid_VC, team_id_VC, reply_sentiment_uuid_VC, *, *)
        .returning(Success(task_uuid_VC))
      (campaignProspectDAO.addReplySentimentViaTask(_: CampaignId, _: ReplySentimentUuid, _: ProspectId, _: TeamId,  _: ReplySentimentUpdatedBy)(_: DBSession))
        .expects(campaign_id_VC, reply_sentiment_uuid_VC, propsect_id_VC, team_id_VC, *, *)
        .returning(Success(None))

      (replySentimentDAO.getReplySentimentsForUUID(_:Long,_:ReplySentimentUuid)(_:SRLogger))
        .expects(team_id_VC.id, reply_sentiment_uuid_VC,*)
        .returns(Success(Some(positiveSentiment.copy(reply_sentiment = NegativeData(
          replySentimentSubCategory = ReplySentimentSubCategory.EmailNegativeOtherReason,
          replySentimentChannelType = ReplySentimentChannelType.EmailChannelType
        )))))

      (prospectDAO.addingLatestReplySentimentForProspect(_: Long, _: Seq[Long], _: ReplySentimentUuid)(_: DBSession))
        .expects(team_id_VC.id, Seq(propsect_id_VC.id), reply_sentiment_uuid_VC, *)
        .returning(Success(List(9)))

      (mqAutoUpdateProspectCategoryPublisher.publish(_: MqAutoUpdateProspectCategoryMsg, _: String, _: Int))
        .expects(MqAutoUpdateProspectCategoryMsg(
          teamId = team_id_VC,
          doerAccountId = accountId,
          prospectIds = Seq(propsect_id_VC),
          replySentimentUuid = Some(reply_sentiment_uuid_VC),
          newProspectCategory = None
        ), MQConfig.autoUpdateProspectCategoryQueueBaseName, MQConfig.autoUpdateProspectCategoryPrefetchCount)
        .returning(Success(()))

      (eventLogService.createEventLog(_: EventDataType, _: Long, _: Long, _: DateTime)(using _: SRLogger))
        .expects(*, team_id_VC.id, accountId.id, *, *)
        .returning(Right("success"))
      (prospectAddEventDAO.addEvents(_: Seq[CreateProspectEventDB]))
        .expects(capture(pe))
        .returning(Success(List(1L)))

      (mqPauseCampaignOnReplySentimentSelect.publish(_: PauseCampaignData))
        .expects(pauseCampaignData)
        .returning(Success(()))

      (dbUtils.commitAndCloseSession)
        .expects(*)
        .returning({})


      val res = taskService.addReplySentimentForTask(
        accountId = accountId,
        campaignId = Some(campaign_id_VC),
        teamId = team_id_VC,
        taskUuid = task_uuid_VC,
        replySentimentUuid = reply_sentiment_uuid_VC,
        permittedAccountIds = Seq(accountId.id),
        prospect_id = propsect_id_VC,
        call_sp_sid = None,
        conference_uuid = None,
        task_type = TaskType.SendSms
      )

      //      res.map(s => assert(s == true))
      //        .recover(e => assert(false))

      res match {

        case Success(res) =>

          assert(res == true)

        case Failure(e) =>
          logger.fatal(e.toString)
          assert(false)
      }

    }

    it("should publish to mq when referral and campaign id is present") {

      val pauseCampaignData: PauseCampaignData = PauseCampaignData(
        campaignId = campaign_id_VC,
        accountId = accountId,
        teamId = team_id_VC,
        taskUuid = task_uuid_VC,
        replySentimentUuid = reply_sentiment_uuid_VC,
        permittedAccountIds = Seq(accountId.id),
        prospect_id = propsect_id_VC,
        task_type = TaskType.SendSms
      )

      (() => dbUtils.startLocalTx())
        .expects()
        .returning(DbAndSession(null, null))

      (taskDaoService.saveSentimentForTask(_: TaskUuid, _: TeamId, _: ReplySentimentUuid)(_: DBSession, _: SRLogger))
        .expects(task_uuid_VC, team_id_VC, reply_sentiment_uuid_VC, *, *)
        .returning(Success(task_uuid_VC))

      (campaignProspectDAO.addReplySentimentViaTask(_: CampaignId, _: ReplySentimentUuid, _: ProspectId, _: TeamId,  _: ReplySentimentUpdatedBy)(_: DBSession))
        .expects(campaign_id_VC, reply_sentiment_uuid_VC, propsect_id_VC, team_id_VC, *, *)
        .returning(Success(None))

      (replySentimentDAO.getReplySentimentsForUUID(_:Long,_:ReplySentimentUuid)(_:SRLogger))
        .expects(team_id_VC.id, reply_sentiment_uuid_VC,*)
        .returns(Success(Some(positiveSentiment.copy(reply_sentiment = ReplySentimentTypeData.ReferralData(
          replySentimentSubCategory = ReplySentimentSubCategory.EmailFollowUpReferredSomeone,
          replySentimentChannelType = ReplySentimentChannelType.EmailChannelType
        )))))

      (prospectDAO.addingLatestReplySentimentForProspect(_: Long, _: Seq[Long], _: ReplySentimentUuid)(_: DBSession))
        .expects(team_id_VC.id, Seq(propsect_id_VC.id), reply_sentiment_uuid_VC, *)
        .returning(Success(List(9)))

      (mqAutoUpdateProspectCategoryPublisher.publish(_: MqAutoUpdateProspectCategoryMsg, _: String, _: Int))
        .expects(MqAutoUpdateProspectCategoryMsg(
          teamId = team_id_VC,
          doerAccountId = accountId,
          prospectIds = Seq(propsect_id_VC),
          replySentimentUuid = Some(reply_sentiment_uuid_VC),
          newProspectCategory = None
        ), MQConfig.autoUpdateProspectCategoryQueueBaseName, MQConfig.autoUpdateProspectCategoryPrefetchCount)
        .returning(Success(()))

      (eventLogService.createEventLog(_: EventDataType, _: Long, _: Long, _: DateTime)(using _: SRLogger))
        .expects(*, team_id_VC.id, accountId.id, *, *)
        .returning(Right("success"))
      (prospectAddEventDAO.addEvents(_: Seq[CreateProspectEventDB]))
        .expects(capture(pe))
        .returning(Success(List(1L)))

      (mqPauseCampaignOnReplySentimentSelect.publish(_: PauseCampaignData))
        .expects(pauseCampaignData)
        .returning(Success(()))

      (dbUtils.commitAndCloseSession)
        .expects(*)
        .returning({})


      val res = taskService.addReplySentimentForTask(
        accountId = accountId,
        campaignId = Some(campaign_id_VC),
        teamId = team_id_VC,
        taskUuid = task_uuid_VC,
        replySentimentUuid = reply_sentiment_uuid_VC,
        permittedAccountIds = Seq(accountId.id),
        prospect_id = propsect_id_VC,
        call_sp_sid = None,
        conference_uuid = None,
        task_type = TaskType.SendSms
      )

      //      res.map(s => assert(s == true))
      //        .recover(e => assert(false))

      res match {

        case Success(res) =>

          assert(res == true)

        case Failure(e) =>
          logger.fatal(e.toString)
          assert(false)
      }

    }

    it("should publish to mq when objection and campaign id is present") {

      val pauseCampaignData: PauseCampaignData = PauseCampaignData(
        campaignId = campaign_id_VC,
        accountId = accountId,
        teamId = team_id_VC,
        taskUuid = task_uuid_VC,
        replySentimentUuid = reply_sentiment_uuid_VC,
        permittedAccountIds = Seq(accountId.id),
        prospect_id = propsect_id_VC,
        task_type = TaskType.SendSms
      )

      (() => dbUtils.startLocalTx())
        .expects()
        .returning(DbAndSession(null, null))

      (taskDaoService.saveSentimentForTask(_: TaskUuid, _: TeamId, _: ReplySentimentUuid)(_: DBSession, _: SRLogger))
        .expects(task_uuid_VC, team_id_VC, reply_sentiment_uuid_VC, *, *)
        .returning(Success(task_uuid_VC))

      (campaignProspectDAO.addReplySentimentViaTask(_: CampaignId, _: ReplySentimentUuid, _: ProspectId, _: TeamId,  _: ReplySentimentUpdatedBy)(_: DBSession))
        .expects(campaign_id_VC, reply_sentiment_uuid_VC, propsect_id_VC, team_id_VC, *, *)
        .returning(Success(None))

      (replySentimentDAO.getReplySentimentsForUUID(_:Long,_:ReplySentimentUuid)(_:SRLogger))
        .expects(team_id_VC.id, reply_sentiment_uuid_VC,*)
        .returns(Success(Some(positiveSentiment.copy(reply_sentiment = ReplySentimentTypeData.ObjectionData(
          replySentimentSubCategory = ReplySentimentSubCategory.EmailNegativeNoBudget,
          replySentimentChannelType = ReplySentimentChannelType.EmailChannelType,
        )))))

      (prospectDAO.addingLatestReplySentimentForProspect(_: Long, _: Seq[Long], _: ReplySentimentUuid)(_: DBSession))
        .expects(team_id_VC.id, Seq(propsect_id_VC.id), reply_sentiment_uuid_VC, *)
        .returning(Success(List(9)))

      (mqAutoUpdateProspectCategoryPublisher.publish(_: MqAutoUpdateProspectCategoryMsg, _: String, _: Int))
        .expects(MqAutoUpdateProspectCategoryMsg(
          teamId = team_id_VC,
          doerAccountId = accountId,
          prospectIds = Seq(propsect_id_VC),
          replySentimentUuid = Some(reply_sentiment_uuid_VC),
          newProspectCategory = None
        ), MQConfig.autoUpdateProspectCategoryQueueBaseName, MQConfig.autoUpdateProspectCategoryPrefetchCount)
        .returning(Success(()))

      (eventLogService.createEventLog(_: EventDataType, _: Long, _: Long, _: DateTime)(using _: SRLogger))
        .expects(*, team_id_VC.id, accountId.id, *, *)
        .returning(Right("success"))

      (prospectAddEventDAO.addEvents(_: Seq[CreateProspectEventDB]))
        .expects(capture(pe))
        .returning(Success(List(1L)))

      (mqPauseCampaignOnReplySentimentSelect.publish(_: PauseCampaignData))
        .expects(pauseCampaignData)
        .returning(Success(()))

      (dbUtils.commitAndCloseSession)
        .expects(*)
        .returning({})


      val res = taskService.addReplySentimentForTask(
        accountId = accountId,
        campaignId = Some(campaign_id_VC),
        teamId = team_id_VC,
        taskUuid = task_uuid_VC,
        replySentimentUuid = reply_sentiment_uuid_VC,
        permittedAccountIds = Seq(accountId.id),
        prospect_id = propsect_id_VC,
        call_sp_sid = None,
        conference_uuid = None,
        task_type = TaskType.SendSms
      )

      //      res.map(s => assert(s == true))
      //        .recover(e => assert(false))

      res match {

        case Success(res) =>

          assert(res == true)

        case Failure(e) =>
          logger.fatal(e.toString)
          assert(false)
      }

    }

    it("should publish to mq when do not contact and campaign id is present") {

      val pauseCampaignData: PauseCampaignData = PauseCampaignData(
        campaignId = campaign_id_VC,
        accountId = accountId,
        teamId = team_id_VC,
        taskUuid = task_uuid_VC,
        replySentimentUuid = reply_sentiment_uuid_VC,
        permittedAccountIds = Seq(accountId.id),
        prospect_id = propsect_id_VC,
        task_type = TaskType.SendSms
      )

      (() => dbUtils.startLocalTx())
        .expects()
        .returning(DbAndSession(null, null))

      (taskDaoService.saveSentimentForTask(_: TaskUuid, _: TeamId, _: ReplySentimentUuid)(_: DBSession, _: SRLogger))
        .expects(task_uuid_VC, team_id_VC, reply_sentiment_uuid_VC, *, *)
        .returning(Success(task_uuid_VC))

      (campaignProspectDAO.addReplySentimentViaTask(_: CampaignId, _: ReplySentimentUuid, _: ProspectId, _: TeamId,  _: ReplySentimentUpdatedBy)(_: DBSession))
        .expects(campaign_id_VC, reply_sentiment_uuid_VC, propsect_id_VC, team_id_VC, *, *)
        .returning(Success(None))

      (replySentimentDAO.getReplySentimentsForUUID(_:Long,_:ReplySentimentUuid)(_:SRLogger))
        .expects(team_id_VC.id, reply_sentiment_uuid_VC,*)
        .returns(Success(Some(positiveSentiment.copy(reply_sentiment = ReplySentimentTypeData.DoNotContactData(
          replySentimentSubCategory = ReplySentimentSubCategory.EmailNegativeUnsubscribe,
          replySentimentChannelType = ReplySentimentChannelType.EmailChannelType,
        )))))

      (prospectDAO.addingLatestReplySentimentForProspect(_: Long, _: Seq[Long], _: ReplySentimentUuid)(_: DBSession))
        .expects(team_id_VC.id, Seq(propsect_id_VC.id), reply_sentiment_uuid_VC, *)
        .returning(Success(List(9)))

      (mqAutoUpdateProspectCategoryPublisher.publish(_: MqAutoUpdateProspectCategoryMsg, _: String, _: Int))
        .expects(MqAutoUpdateProspectCategoryMsg(
          teamId = team_id_VC,
          doerAccountId = accountId,
          prospectIds = Seq(propsect_id_VC),
          replySentimentUuid = Some(reply_sentiment_uuid_VC),
          newProspectCategory = None
        ), MQConfig.autoUpdateProspectCategoryQueueBaseName, MQConfig.autoUpdateProspectCategoryPrefetchCount)
        .returning(Success(()))

      (eventLogService.createEventLog(_: EventDataType, _: Long, _: Long, _: DateTime)(using _: SRLogger))
        .expects(*, team_id_VC.id, accountId.id, *, *)
        .returning(Right("success"))

      (prospectAddEventDAO.addEvents(_: Seq[CreateProspectEventDB]))
        .expects(capture(pe))
        .returning(Success(List(1L)))

      (mqPauseCampaignOnReplySentimentSelect.publish(_: PauseCampaignData))
        .expects(pauseCampaignData)
        .returning(Success(()))

      (dbUtils.commitAndCloseSession)
        .expects(*)
        .returning({})


      val res = taskService.addReplySentimentForTask(
        accountId = accountId,
        campaignId = Some(campaign_id_VC),
        teamId = team_id_VC,
        taskUuid = task_uuid_VC,
        replySentimentUuid = reply_sentiment_uuid_VC,
        permittedAccountIds = Seq(accountId.id),
        prospect_id = propsect_id_VC,
        call_sp_sid = None,
        conference_uuid = None,
        task_type = TaskType.SendSms
      )

      //      res.map(s => assert(s == true))
      //        .recover(e => assert(false))

      res match {

        case Success(res) =>

          assert(res == true)

        case Failure(e) =>
          logger.fatal(e.toString)
          assert(false)
      }

    }

    it("should not publish to mq when positive and campaign id is absent") {

      val pauseCampaignData: PauseCampaignData = PauseCampaignData(
        campaignId = campaign_id_VC,
        accountId = accountId,
        teamId = team_id_VC,
        taskUuid = task_uuid_VC,
        replySentimentUuid = reply_sentiment_uuid_VC,
        permittedAccountIds = Seq(accountId.id),
        prospect_id = propsect_id_VC,
        task_type = TaskType.SendSms
      )

      (() => dbUtils.startLocalTx())
        .expects()
        .returning(DbAndSession(null, null))

      (taskDaoService.saveSentimentForTask(_: TaskUuid, _: TeamId, _: ReplySentimentUuid)(_: DBSession, _: SRLogger))
        .expects(task_uuid_VC, team_id_VC, reply_sentiment_uuid_VC, *, *)
        .returning(Success(task_uuid_VC))

      (replySentimentDAO.getReplySentimentsForUUID(_:Long,_:ReplySentimentUuid)(_:SRLogger))
        .expects(team_id_VC.id, reply_sentiment_uuid_VC,*)
        .returns(Success(Some(positiveSentiment)))

      (prospectDAO.addingLatestReplySentimentForProspect(_: Long, _: Seq[Long], _: ReplySentimentUuid)(_: DBSession))
        .expects(team_id_VC.id, Seq(propsect_id_VC.id), reply_sentiment_uuid_VC, *)
        .returning(Success(List(9)))

      (mqAutoUpdateProspectCategoryPublisher.publish(_: MqAutoUpdateProspectCategoryMsg, _: String, _: Int))
        .expects(MqAutoUpdateProspectCategoryMsg(
          teamId = team_id_VC,
          doerAccountId = accountId,
          prospectIds = Seq(propsect_id_VC),
          replySentimentUuid = Some(reply_sentiment_uuid_VC),
          newProspectCategory = None
        ), MQConfig.autoUpdateProspectCategoryQueueBaseName, MQConfig.autoUpdateProspectCategoryPrefetchCount)
        .returning(Success(()))

      (eventLogService.createEventLog(_: EventDataType, _: Long, _: Long, _: DateTime)(using _: SRLogger))
        .expects(*, team_id_VC.id, accountId.id, *, *)
        .returning(Right("success"))

      (prospectAddEventDAO.addEvents(_: Seq[CreateProspectEventDB]))
        .expects(capture(pe))
        .returning(Success(List(1L)))


      (dbUtils.commitAndCloseSession)
        .expects(*)
        .returning({})


      val res = taskService.addReplySentimentForTask(
        accountId = accountId,
        campaignId = None,
        teamId = team_id_VC,
        taskUuid = task_uuid_VC,
        replySentimentUuid = reply_sentiment_uuid_VC,
        permittedAccountIds = Seq(accountId.id),
        prospect_id = propsect_id_VC,
        call_sp_sid = None,
        conference_uuid = None,
        task_type = TaskType.SendSms
      )

      //      res.map(s => assert(s == true))
      //        .recover(e => assert(false))

      res match {

        case Success(res) =>

          assert(res == true)

        case Failure(e) =>
          logger.fatal(e.toString)
          assert(false)
      }

    }

    it("should not publish to mq when other and campaign id is present") {

      val pauseCampaignData: PauseCampaignData = PauseCampaignData(
        campaignId = campaign_id_VC,
        accountId = accountId,
        teamId = team_id_VC,
        taskUuid = task_uuid_VC,
        replySentimentUuid = reply_sentiment_uuid_VC,
        permittedAccountIds = Seq(accountId.id),
        prospect_id = propsect_id_VC,
        task_type = TaskType.SendSms
      )

      (() => dbUtils.startLocalTx())
        .expects()
        .returning(DbAndSession(null, null))

      (taskDaoService.saveSentimentForTask(_: TaskUuid, _: TeamId, _: ReplySentimentUuid)(_: DBSession, _: SRLogger))
        .expects(task_uuid_VC, team_id_VC, reply_sentiment_uuid_VC, *, *)
        .returning(Success(task_uuid_VC))

      (campaignProspectDAO.addReplySentimentViaTask(_: CampaignId, _: ReplySentimentUuid, _: ProspectId, _: TeamId,  _: ReplySentimentUpdatedBy)(_: DBSession))
        .expects(campaign_id_VC, reply_sentiment_uuid_VC, propsect_id_VC, team_id_VC, *, *)
        .returning(Success(None))

      (replySentimentDAO.getReplySentimentsForUUID(_:Long,_:ReplySentimentUuid)(_:SRLogger))
        .expects(team_id_VC.id, reply_sentiment_uuid_VC,*)
        .returns(Success(Some(positiveSentiment.copy(reply_sentiment = ReplySentimentTypeData.OtherData(
          replySentimentSubCategory = ReplySentimentSubCategory.EmailPositiveOtherPositive,
          replySentimentChannelType = ReplySentimentChannelType.EmailChannelType,
        )))))

      (prospectDAO.addingLatestReplySentimentForProspect(_: Long, _: Seq[Long], _: ReplySentimentUuid)(_: DBSession))
        .expects(team_id_VC.id, Seq(propsect_id_VC.id), reply_sentiment_uuid_VC, *)
        .returning(Success(List(9)))

      (mqAutoUpdateProspectCategoryPublisher.publish(_: MqAutoUpdateProspectCategoryMsg, _: String, _: Int))
        .expects(MqAutoUpdateProspectCategoryMsg(
          teamId = team_id_VC,
          doerAccountId = accountId,
          prospectIds = Seq(propsect_id_VC),
          replySentimentUuid = Some(reply_sentiment_uuid_VC),
          newProspectCategory = None
        ), MQConfig.autoUpdateProspectCategoryQueueBaseName, MQConfig.autoUpdateProspectCategoryPrefetchCount)
        .returning(Success(()))

      (eventLogService.createEventLog(_: EventDataType, _: Long, _: Long, _: DateTime)(using _: SRLogger))
        .expects(*, team_id_VC.id, accountId.id, *, *)
        .returning(Right("success"))

      (prospectAddEventDAO.addEvents(_: Seq[CreateProspectEventDB]))
        .expects(capture(pe))
        .returning(Success(List(1L)))

      (dbUtils.commitAndCloseSession)
        .expects(*)
        .returning({})


      val res = taskService.addReplySentimentForTask(
        accountId = accountId,
        campaignId = Some(campaign_id_VC),
        teamId = team_id_VC,
        taskUuid = task_uuid_VC,
        replySentimentUuid = reply_sentiment_uuid_VC,
        permittedAccountIds = Seq(accountId.id),
        prospect_id = propsect_id_VC,
        call_sp_sid = None,
        conference_uuid = None,
        task_type = TaskType.SendSms
      )

      //      res.map(s => assert(s == true))
      //        .recover(e => assert(false))

      res match {

        case Success(res) =>

          assert(res == true)

        case Failure(e) =>
          logger.fatal(e.toString)
          assert(false)
      }

    }

    it("should not publish to mq when not classified and campaign id is present") {

      val pauseCampaignData: PauseCampaignData = PauseCampaignData(
        campaignId = campaign_id_VC,
        accountId = accountId,
        teamId = team_id_VC,
        taskUuid = task_uuid_VC,
        replySentimentUuid = reply_sentiment_uuid_VC,
        permittedAccountIds = Seq(accountId.id),
        prospect_id = propsect_id_VC,
        task_type = TaskType.SendSms
      )

      (() => dbUtils.startLocalTx())
        .expects()
        .returning(DbAndSession(null, null))

      (taskDaoService.saveSentimentForTask(_: TaskUuid, _: TeamId, _: ReplySentimentUuid)(_: DBSession, _: SRLogger))
        .expects(task_uuid_VC, team_id_VC, reply_sentiment_uuid_VC, *, *)
        .returning(Success(task_uuid_VC))

      (campaignProspectDAO.addReplySentimentViaTask(_: CampaignId, _: ReplySentimentUuid, _: ProspectId, _: TeamId,  _: ReplySentimentUpdatedBy)(_: DBSession))
        .expects(campaign_id_VC, reply_sentiment_uuid_VC, propsect_id_VC, team_id_VC, *, *)
        .returning(Success(None))

      (replySentimentDAO.getReplySentimentsForUUID(_:Long,_:ReplySentimentUuid)(_:SRLogger))
        .expects(team_id_VC.id, reply_sentiment_uuid_VC,*)
        .returns(Success(Some(positiveSentiment.copy(reply_sentiment = ReplySentimentTypeData.OtherData(
          replySentimentSubCategory = ReplySentimentSubCategory.EmailPositiveOtherPositive,
          replySentimentChannelType = ReplySentimentChannelType.EmailChannelType,
        )))))

      (prospectDAO.addingLatestReplySentimentForProspect(_: Long, _: Seq[Long], _: ReplySentimentUuid)(_: DBSession))
        .expects(team_id_VC.id, Seq(propsect_id_VC.id), reply_sentiment_uuid_VC, *)
        .returning(Success(List(9)))

      (mqAutoUpdateProspectCategoryPublisher.publish(_: MqAutoUpdateProspectCategoryMsg, _: String, _: Int))
        .expects(MqAutoUpdateProspectCategoryMsg(
          teamId = team_id_VC,
          doerAccountId = accountId,
          prospectIds = Seq(propsect_id_VC),
          replySentimentUuid = Some(reply_sentiment_uuid_VC),
          newProspectCategory = None
        ), MQConfig.autoUpdateProspectCategoryQueueBaseName, MQConfig.autoUpdateProspectCategoryPrefetchCount)
        .returning(Success(()))

      (eventLogService.createEventLog(_: EventDataType, _: Long, _: Long, _: DateTime)(using _: SRLogger))
        .expects(*, team_id_VC.id, accountId.id, *, *)
        .returning(Right("success"))

      (prospectAddEventDAO.addEvents(_: Seq[CreateProspectEventDB]))
        .expects(capture(pe))
        .returning(Success(List(1L)))

      (dbUtils.commitAndCloseSession)
        .expects(*)
        .returning({})


      val res = taskService.addReplySentimentForTask(
        accountId = accountId,
        campaignId = Some(campaign_id_VC),
        teamId = team_id_VC,
        taskUuid = task_uuid_VC,
        replySentimentUuid = reply_sentiment_uuid_VC,
        permittedAccountIds = Seq(accountId.id),
        prospect_id = propsect_id_VC,
        call_sp_sid = None,
        conference_uuid = None,
        task_type = TaskType.SendSms
      )

      //      res.map(s => assert(s == true))
      //        .recover(e => assert(false))

      res match {

        case Success(res) =>

          assert(res == true)

        case Failure(e) =>
          logger.fatal(e.toString)
          assert(false)
      }

    }

    it("should not publish to mq when not classified and campaign id is absent") {

      val pauseCampaignData: PauseCampaignData = PauseCampaignData(
        campaignId = campaign_id_VC,
        accountId = accountId,
        teamId = team_id_VC,
        taskUuid = task_uuid_VC,
        replySentimentUuid = reply_sentiment_uuid_VC,
        permittedAccountIds = Seq(accountId.id),
        prospect_id = propsect_id_VC,
        task_type = TaskType.SendSms
      )

      (() => dbUtils.startLocalTx())
        .expects()
        .returning(DbAndSession(null, null))

      (taskDaoService.saveSentimentForTask(_: TaskUuid, _: TeamId, _: ReplySentimentUuid)(_: DBSession, _: SRLogger))
        .expects(task_uuid_VC, team_id_VC, reply_sentiment_uuid_VC, *, *)
        .returning(Success(task_uuid_VC))



      (replySentimentDAO.getReplySentimentsForUUID(_:Long,_:ReplySentimentUuid)(_:SRLogger))
        .expects(team_id_VC.id, reply_sentiment_uuid_VC,*)
        .returns(Success(Some(positiveSentiment.copy(reply_sentiment = ReplySentimentTypeData.OtherData(
          replySentimentSubCategory = ReplySentimentSubCategory.EmailPositiveOtherPositive,
          replySentimentChannelType = ReplySentimentChannelType.EmailChannelType,
        )))))

      (prospectDAO.addingLatestReplySentimentForProspect(_: Long, _: Seq[Long], _: ReplySentimentUuid)(_: DBSession))
        .expects(team_id_VC.id, Seq(propsect_id_VC.id), reply_sentiment_uuid_VC, *)
        .returning(Success(List(9)))

      (mqAutoUpdateProspectCategoryPublisher.publish(_: MqAutoUpdateProspectCategoryMsg, _: String, _: Int))
        .expects(MqAutoUpdateProspectCategoryMsg(
          teamId = team_id_VC,
          doerAccountId = accountId,
          prospectIds = Seq(propsect_id_VC),
          replySentimentUuid = Some(reply_sentiment_uuid_VC),
          newProspectCategory = None
        ), MQConfig.autoUpdateProspectCategoryQueueBaseName, MQConfig.autoUpdateProspectCategoryPrefetchCount)
        .returning(Success(()))

      (eventLogService.createEventLog(_: EventDataType, _: Long, _: Long, _: DateTime)(using _: SRLogger))
        .expects(*, team_id_VC.id, accountId.id, *, *)
        .returning(Right("success"))

      (prospectAddEventDAO.addEvents(_: Seq[CreateProspectEventDB]))
        .expects(capture(pe))
        .returning(Success(List(1L)))

      (dbUtils.commitAndCloseSession)
        .expects(*)
        .returning({})


      val res = taskService.addReplySentimentForTask(
        accountId = accountId,
        campaignId = None,
        teamId = team_id_VC,
        taskUuid = task_uuid_VC,
        replySentimentUuid = reply_sentiment_uuid_VC,
        permittedAccountIds = Seq(accountId.id),
        prospect_id = propsect_id_VC,
        call_sp_sid = None,
        conference_uuid = None,
        task_type = TaskType.SendSms
      )

      //      res.map(s => assert(s == true))
      //        .recover(e => assert(false))

      res match {

        case Success(res) =>

          assert(res == true)

        case Failure(e) =>
          logger.fatal(e.toString)
          assert(false)
      }

    }

    val call_sp_sid = CallSID(sid = "call-sid-hun-main")
    it("should fail when task_type is call and saving reply_sentiment uuid in call_participants_logs fails") {

      (() => dbUtils.startLocalTx())
        .expects()
        .returning(DbAndSession(null, null))

      (taskDaoService.saveSentimentForTask(_: TaskUuid, _: TeamId, _: ReplySentimentUuid)(_: DBSession, _: SRLogger))
        .expects(task_uuid_VC, team_id_VC, reply_sentiment_uuid_VC, *, *)
        .returning(Success(task_uuid_VC))
      (campaignProspectDAO.addReplySentimentViaTask(_: CampaignId, _: ReplySentimentUuid, _: ProspectId, _: TeamId, _: ReplySentimentUpdatedBy)(_: DBSession))
        .expects(campaign_id_VC, reply_sentiment_uuid_VC, propsect_id_VC, team_id_VC, *, *)
        .returning(Success(None))
      (prospectDAO.addingLatestReplySentimentForProspect(_: Long, _: Seq[Long], _: ReplySentimentUuid)(_: DBSession))
        .expects(team_id_VC.id, Seq(propsect_id_VC.id), reply_sentiment_uuid_VC, *)
        .returning(Success(List(9)))

      (replySentimentDAO.getReplySentimentsForUUID(_:Long, _: ReplySentimentUuid)(_:SRLogger))
        .expects(team_id_VC.id, reply_sentiment_uuid_VC, *)
        .returns(Success(None))

      (conferenceDAO.addReplySentimentUUIDToConferenceLogs(_: ReplySentimentUuid, _: CallSidOrConfUuid, _: TeamId)(_: DBSession))
        .expects(reply_sentiment_uuid_VC, CallSidOrConfUuid.WithCallSid(call_sp_sid), team_id_VC, *)
        .returning(Success(1))

      (conferenceDAO.addReplySentimentUUID(_: ReplySentimentUuid, _: CallSID, _: TeamId)(_: DBSession))
        .expects(reply_sentiment_uuid_VC, call_sp_sid, team_id_VC, *)
        .returning(Failure(error))

      (dbUtils.commitAndCloseSession)
        .expects(*)
        .returning({})


      val res = taskService.addReplySentimentForTask(
        accountId = accountId,
        campaignId = Some(campaign_id_VC),
        teamId = team_id_VC,
        taskUuid = task_uuid_VC,
        replySentimentUuid = reply_sentiment_uuid_VC,
        permittedAccountIds = Seq(accountId.id),
        prospect_id = propsect_id_VC,
        call_sp_sid = Some(call_sp_sid),
        conference_uuid = None,
        task_type = TaskType.CallTask
      )

      //      res.map(s => assert(s == true))
      //        .recover(e => assert(false))

      res match {

        case Success(res) =>

          assert(res == false)

        case Failure(e) =>
          logger.fatal(e.toString)
          assert(true)
      }

    }

    it("should success when task_type is call and saving reply_sentiment uuid in call_participants_logs success") {

      (() => dbUtils.startLocalTx())
        .expects()
        .returning(DbAndSession(null, null))

      (taskDaoService.saveSentimentForTask(_: TaskUuid, _: TeamId, _: ReplySentimentUuid)(_: DBSession, _: SRLogger))
        .expects(task_uuid_VC, team_id_VC, reply_sentiment_uuid_VC, *, *)
        .returning(Success(task_uuid_VC))
      (campaignProspectDAO.addReplySentimentViaTask(_: CampaignId, _: ReplySentimentUuid, _: ProspectId, _: TeamId, _: ReplySentimentUpdatedBy)(_: DBSession))
        .expects(campaign_id_VC, reply_sentiment_uuid_VC, propsect_id_VC, team_id_VC, *, *)
        .returning(Success(None))
      (prospectDAO.addingLatestReplySentimentForProspect(_: Long, _: Seq[Long], _: ReplySentimentUuid)(_: DBSession))
        .expects(team_id_VC.id, Seq(propsect_id_VC.id), reply_sentiment_uuid_VC, *)
        .returning(Success(List(9)))

      (mqAutoUpdateProspectCategoryPublisher.publish(_: MqAutoUpdateProspectCategoryMsg, _: String, _: Int))
        .expects(MqAutoUpdateProspectCategoryMsg(
          teamId = team_id_VC,
          doerAccountId = accountId,
          prospectIds = Seq(propsect_id_VC),
          replySentimentUuid = Some(reply_sentiment_uuid_VC),
          newProspectCategory = None
        ), MQConfig.autoUpdateProspectCategoryQueueBaseName, MQConfig.autoUpdateProspectCategoryPrefetchCount)
        .returning(Success(()))

      (conferenceDAO.addReplySentimentUUIDToConferenceLogs(_: ReplySentimentUuid, _: CallSidOrConfUuid, _: TeamId)(_: DBSession))
        .expects(reply_sentiment_uuid_VC, CallSidOrConfUuid.WithCallSid(call_sp_sid), team_id_VC, *)
        .returning(Success(1))

      (replySentimentDAO.getReplySentimentsForUUID(_:Long, _:ReplySentimentUuid)(_:SRLogger))
        .expects(team_id_VC.id,reply_sentiment_uuid_VC,* )
        .returns(Success(None))

      (conferenceDAO.addReplySentimentUUID(_: ReplySentimentUuid, _: CallSID, _: TeamId)(_: DBSession))
        .expects(reply_sentiment_uuid_VC, call_sp_sid, team_id_VC, *)
        .returning(Success(1))


      (eventLogService.createEventLog(_: EventDataType, _: Long, _: Long, _: DateTime)(using _: SRLogger))
        .expects(*, team_id_VC.id,accountId.id, *, *)
        .returning(Right("success"))

      (prospectAddEventDAO.addEvents(_: Seq[CreateProspectEventDB]))
        .expects(capture(pe))
        .returning(Success(List(1L)))

      (dbUtils.commitAndCloseSession)
        .expects(*)
        .returning({})


      val res = taskService.addReplySentimentForTask(
        accountId = accountId,
        campaignId = Some(campaign_id_VC),
        teamId = team_id_VC,
        taskUuid = task_uuid_VC,
        replySentimentUuid = reply_sentiment_uuid_VC,
        permittedAccountIds = Seq(accountId.id),
        prospect_id = propsect_id_VC,
        call_sp_sid = Some(call_sp_sid),
        conference_uuid = None,
        task_type = TaskType.CallTask
      )

      //      res.map(s => assert(s == true))
      //        .recover(e => assert(false))

      res match {

        case Success(res) =>

          assert(res == true)

        case Failure(e) =>
          logger.fatal(e.toString)
          assert(false)
      }

    }

  }

  describe("testing dueDateValidation") {

    it("should return false as due date is greater than 360 days") {

      val res = TaskService.dueDateValidation(

        due_at = DateTime.now().plusDays(361)

      )

      assert(res == false)


    }

    it("should return true as due date is less than 180 days") {

      val res = TaskService.dueDateValidation(

        due_at = DateTime.now().plusDays(170)

      )

      assert(res == true)


    }

  }


}
