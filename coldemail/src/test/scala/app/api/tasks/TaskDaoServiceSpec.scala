package app.api.tasks

import org.apache.pekko.actor.ActorSystem
import api.AppConfig
import api.accounts.EmailScheduledIdOrTaskId.TaskId
import api.accounts.{AccountDAO, TeamId}
import api.accounts.models.{AccountId, OrgId}
import api.accounts.service.AccountOrgBillingRelatedService
import api.campaigns.CampaignProspectDAO
import api.campaigns.services.CampaignId
import api.emails.{CampaignProspectStepScheduleLogData, CampaignProspectStepScheduleLogsDAO, OptionCampaignIdAndOptionProspectIdAndOptionEmailSettingId}
import api.prospects.{CreateProspectEventDB, InferredQueryTimeline, ProspectEventDAO}
import api.prospects.dao.{ProspectAddEventDAO, ProspectDAO}
import api.prospects.models.{ProspectId, StepId}
import api.tasks.models.{Assignee, ChangeStatusPermissionCheck, CommonLinkedinTaskDetails, LinkedinTaskToBeGrouped, NewTask, Task, TaskCreatedVia, TaskData, TaskPriority, TaskProspect, TaskStatus, TaskStatusType, TaskType, TasksGroupedByTypeAndLinkedinSetting, TimeBasedTaskType, UpdateTask, UpdateTaskStatus}
import api.tasks.pgDao.{Settings_Table, TaskPgDAO, ValidatedTaskReq}
import api.tasks.services.TaskUuid
import io.smartreach.esp.api.emails.EmailSettingId
import utils.dateTime.SrDateTimeUtils
import utils.dbutils.DBUtils
import utils.testapp.TestAppExecutionContext
import utils.testapp.Test_TaskPgDAO.linkedinSettingDAO
//import api.tasks.scylladb.dao.{GetTaskError, TaskCacheDAO}
import api.tasks.scylladb.model.TaskCache
import api.tasks.services.{AddNoteError, ChangePriorityError, ChangeStatusTypeError, CreateTaskError, DeleteTaskError, GetAllTaskForUserError, TaskDaoService, TaskService}
import org.joda.time.DateTime
import org.scalamock.matchers.ArgCapture.CaptureOne
import org.scalamock.scalatest.AsyncMockFactory
import org.scalatest.funspec.AsyncFunSpec
import play.api.libs.json.{JsError, JsPath, JsResult, JsSuccess, Json, JsonValidationError}
import play.api.libs.ws.ahc.AhcWSClient
import scalikejdbc.{NoExtractor, SQL}
import sr_scheduler.models.ChannelType
import utils.random.SrRandomUtils
import utils.uuid.SrUuidUtils
import utils.{SRLogger, StringUtils}

import scala.util.{Failure, Success}
import scala.concurrent.{ExecutionContext, Future}

class TaskDaoServiceSpec extends AsyncFunSpec
  with AsyncMockFactory {

  //  val srEventService: SrEventService = mock[SrEventService]
  val taskPgDAO: TaskPgDAO = mock[TaskPgDAO]
  //  val taskCacheDAO: TaskCacheDAO = mock[TaskCacheDAO]
  //  val prospectDAO: ProspectDAO = mock[ProspectDAO]
  //  val campaignProspectDAO:CampaignProspectDAO = mock[CampaignProspectDAO]
  val prospectAddEventDAO: ProspectAddEventDAO = mock[ProspectAddEventDAO]
  val accountDAO: AccountDAO = mock[AccountDAO]
  val srUuidUtils: SrUuidUtils = mock[SrUuidUtils]
  //  val srDateTimeUtils = mock[SrDateTimeUtils]
  val accountOrgBillingRelatedService = mock[AccountOrgBillingRelatedService]
  val srRandomUtils = mock[SrRandomUtils]
  val campaignProspectStepScheduleLogsDAO = mock[CampaignProspectStepScheduleLogsDAO]
  val dbUtils: DBUtils = mock[DBUtils]

  val taskDaoService = new TaskDaoService(
    //    srEventService = srEventService,
    taskPgDAO = taskPgDAO,
    //    taskCacheDAO = taskCacheDAO,
    srRandomUtils = srRandomUtils,
    srUuidUtils = srUuidUtils,
    //    srDateTimeUtils = srDateTimeUtils,
    //    prospectDAO = prospectDAO,
    //    campaignProspectDAO = campaignProspectDAO,
    accountOrgBillingRelatedService = accountOrgBillingRelatedService,
    prospectAddEventDAO = prospectAddEventDAO,
    accountDAO = accountDAO,
    dbUtils = dbUtils,
    linkedinSettingDAO = linkedinSettingDAO,
    campaignProspectStepScheduleLogsDAO = campaignProspectStepScheduleLogsDAO
  )

  implicit lazy val system: ActorSystem = TestAppExecutionContext.actorSystem
  implicit lazy val ec: ExecutionContext = system.dispatcher
  implicit lazy val wSClient: AhcWSClient = TestAppExecutionContext.wsClient

  val currentTime = DateTime.parse("2024-05-18")

  val task = Task(
    task_id = "randomString", // Todo - stringUtils.randomString
    task_type = TaskType.SendEmail,
    task_data = TaskData.SendEmailData(
      subject = "Hi this is the subject",
      body = "hello, this is the body",
      email_message_id = None,
      task_type = TaskType.SendEmail
    ),
    status = TaskStatus.Due(
      due_at = DateTime.now().plus(10)
    ),
    assignee = Some(Assignee(
      id = 1,
      name = "Shashank"
    )),
    added_by = 1,
    is_auto_task = false,
    team_id = 9,
    prospect = Some(
      TaskProspect(
        id = 1,
        name = Some("Name"),
        email = Some("email"),
        company = Some("company"),
        phone_number = Some("phone_number"),
        linkedin_url = Some("linkedin_url"),
        timezone = Some("timezone"),
        designation = Some("designation")
      )
    ),
    priority = TaskPriority.Critical,
    note = Some("This is Note"),
    created_at = DateTime.now(),
    updated_at = Some(DateTime.now()),
    due_at = DateTime.now(),
    campaign_id = None,
    campaign_name = None,
    step_id = None,
    step_label = None,
    is_opening_step = None,
    created_via = TaskCreatedVia.Manual,
    reply_sentiment_uuid = None
  )

  val newTask = NewTask(
    campaign_id = Some(1),
    campaign_name = Some("Campaign"),
    step_id = Some(1),
    step_label = Some("Day 1: Opening"),
    created_via = TaskCreatedVia.Manual,
    is_opening_step = Some(true),
    task_type = TaskType.SendEmail,
    task_data = TaskData.SendEmailData(
      subject = "Hi this is the subject",
      body = "hello, this is the body",
      email_message_id = None,
      task_type = TaskType.SendEmail
    ),
    status = TaskStatus.Due(
      due_at = DateTime.now().plus(10)
    ),
    assignee_id = Some(1),
    prospect_id = Some(1),
    priority = TaskPriority.Critical,
    is_auto_task = false,
    emailsScheduledUuid = None,
    note = Some("This is Note"),
  )

  val accountId: Long = 2
  val teamId: Long = 2
  val orgId = OrgId(2L)

  val updateStatus = UpdateTaskStatus.Due(
    due_at = DateTime.now().plus(10)
  )

  val updateTask = UpdateTask(
    task_data = task.task_data,
    status = task.status,
    assignee_id = newTask.assignee_id,
    prospect_id = newTask.prospect_id,
    priority = newTask.priority,
    note = newTask.note,
  )

  val linkedinSettingUuid = "linkedin_account_batman"

  val permittedAccountIds = Seq(11L, 1L, 2L, 3L, 4L, 5L, 6L)

  val taskId = "task_1"

  val commonLinkedinTaskDetails = CommonLinkedinTaskDetails(
    teamId = TeamId(teamId),
    linkedinSettingUuid = linkedinSettingUuid,
    taskType = TaskType.AutoViewLinkedinProfile,
    orgId = orgId
  )

  val linkedinTaskToBeGrouped = LinkedinTaskToBeGrouped(
    commonLinkedinTaskDetails = commonLinkedinTaskDetails,
    taskId = taskId
  )

  val error = new Throwable("Error while performing certain action")
  given logger: SRLogger = new SRLogger("testcase")
  val step_id_VC = StepId(id = 1)
  val team_id_VC = TeamId(id = 2)
  val prospect_id_VC = ProspectId(id = 1)


  describe("TaskService.createTask testing") {

    it("should fail if task already exists") {

      val taskId = "task_1231321321kjkj"

      (taskPgDAO.checkIfTaskAlreadyExists(_: StepId, _: ProspectId, _: TeamId, _: Boolean))
        .expects(step_id_VC, prospect_id_VC, team_id_VC, *)
        .returning(Success(Some(TaskUuid(uuid = taskId))))

      (() =>srUuidUtils.generateTaskUuid())
        .expects()
        .returning(taskId)

      val res = taskDaoService.createTask(
        task_data = newTask,
        accountId = accountId,
        teamId = teamId
      )

      println(s"res : ${res}")

      res.flatMap {

        case Left(CreateTaskError.MightBeDuplicateTaskError(err)) =>
          assert(true)

        case _ => assert(false)

      }.recover(err => assert(false))

    }

    it("should fail if taskCreation in DB fails") {

      val taskId = "task_1231321321kjkj"

      (taskPgDAO.checkIfTaskAlreadyExists(_: StepId, _: ProspectId, _: TeamId, _: Boolean))
        .expects(step_id_VC, prospect_id_VC, team_id_VC, *)
        .returning(Success(None))

      (() =>srUuidUtils.generateTaskUuid())
        .expects()
        .returning(taskId)

      (taskPgDAO.createNewTask(_: NewTask, _: String, _: Long, _: Long, _: DateTime)(_: ExecutionContext))
        .expects(newTask, taskId, accountId, teamId, *, *)
        .returning(Future.failed(exception = error))

      val res = taskDaoService.createTask(
        task_data = newTask,
        accountId = accountId,
        teamId = teamId
      )

      res.map(
        result => {
          println("this means unit test is wrong")
          assert(result == Left(CreateTaskError.ErrorWhileCreatingTask))
        }
      ).recover {
        case e =>
          println("This is where I want it to go")
          assert(e.getMessage.contains(error.getMessage))
      }

    }

    //    it("should fail if srEventCreation fails") {
    //
    //      (taskPgDAO.createNewTask ( _: NewTask, _: Long, _: Long )(_: ExecutionContext))
    //        .expects(newTask, accountId, teamId, *)
    //        .returning(Future.successful(task))
    //
    //      (srEventService.createEvent ( _: SrEventData , _: SrEventType )(_: ExecutionContext, _:SRLogger))
    //        .expects(*,eventType,*,*)
    //        .returning(Future.failed(error))
    //
    //
    //      val res = taskDaoService.createTask(
    //        task_data = newTask,
    //        accountId = accountId,
    //        teamId = teamId
    //      )
    //
    //      res.map(
    //        result => {
    //          println("this means unit test is wrong")
    //          assert(result == Left(CreateTaskError.ErrorWhileCreatingTask))
    //        }
    //      ).recover {
    //        case e =>
    //          println("This is where I want it to go")
    //          assert(e == error)
    //      }
    //
    //    }

    // As we are not adding Tasks to Cache while creating task now, So I'm commenting this unit test out
    //    it("should not fail if taskCreation in Cache fails") {
    //
    //      (taskDAO.createNewTask)
    //        .expects(newTask)
    //        .returning(Future.successful(task))
    //
    //      (srEventService.createEvent ( _: SrEventData[Task] , _: String )(_: ExecutionContext))
    //        .expects(*,eventType,*)
    //        .returning(Future.successful(srEventObject))
    //
    //      val res = taskService.createTask(
    //        task_data = newTask
    //      )
    //
    //      res.map(
    //        result => {
    //          println("this is where I want it to go")
    //          assert(result == Right(task))
    //        }
    //      ).recover {
    //        case e =>
    //          println("this means unit test is wrong")
    //          assert(e != error)
    //      }
    //
    //    }

  }

  describe("TaskService.findSingleTaskFromScyllaAndPG testing") {

    val changeStatusPermissionCheck = ChangeStatusPermissionCheck.ManualTaskCheck(
      doer = None,
      permittedAccountIds = permittedAccountIds
    )

    it("should succeed even if task not found in cache and if found in db") {

      given logger: SRLogger = new SRLogger("testcase")

      //      (taskCacheDAO.findTaskInCache (_:String)(_: ExecutionContext, _: SRLogger))
      //        .expects(task.task_id,*,*)
      //        .returning(Future.successful(Left(GetTaskError.TaskNotFoundInCache)))

      (taskPgDAO.findTaskbyTaskID(_: String, _: Long, _: OrgId, _: ChangeStatusPermissionCheck)(_: ExecutionContext))
        .expects(task.task_id, task.team_id, *, changeStatusPermissionCheck, *)
        .returning(Future.successful(Some(task)))
      //
      //      (taskCacheDAO.createNewTaskInCache ( _: Task )(_: ExecutionContext , _: SRLogger))
      //        .expects(task,*,*)
      //        .returning(Future.successful(0))

      val res = taskDaoService.findSingleTaskFromScyllaAndPG(
        taskId = task.task_id,
        teamId = task.team_id,
        permittedAccountIds = permittedAccountIds,
        orgId = orgId,
         //emailNotCompulsoryEnabled = false
      )

      res.map {
        result => assert(result == Right(task))
      }

    }

    it("should succeed even if cache gives JsValidation error and found in db") {
      given logger: SRLogger = new SRLogger("testcase")

      //      (taskCacheDAO.findTaskInCache (_:String)(_: ExecutionContext, _: SRLogger))
      //        .expects(task.task_id,*,*)
      //        .returning(Future.successful(Left(GetTaskError.JsValidationErrorFromCache)))

      (taskPgDAO.findTaskbyTaskID(_: String, _: Long, _: OrgId, _: ChangeStatusPermissionCheck)(_: ExecutionContext))
        .expects(task.task_id, task.team_id, *, changeStatusPermissionCheck, *)
        .returning(Future.successful(Some(task)))

      //
      //      (taskCacheDAO.createNewTaskInCache ( _: Task )(_: ExecutionContext, _: SRLogger))
      //        .expects(task,*, *)
      //        .returning(Future.successful(0))

      val res = taskDaoService.findSingleTaskFromScyllaAndPG(
        taskId = task.task_id,
        teamId = task.team_id,
        permittedAccountIds = permittedAccountIds,
        orgId = orgId,
         //emailNotCompulsoryEnabled = false

      )

      res.map {
        result => assert(result == Right(task))
      }

    }

    it("should fail if task not found in cache and db") {

      given logger: SRLogger = new SRLogger("testcase")

      //      (taskCacheDAO.findTaskInCache (_:String)(_: ExecutionContext,_: SRLogger))
      //        .expects(task.task_id,*,*)
      //        .returning(Future.successful(Left(GetTaskError.TaskNotFoundInCache)))

      (taskPgDAO.findTaskbyTaskID(_: String, _: Long, _: OrgId, _: ChangeStatusPermissionCheck)(_: ExecutionContext))
        .expects(task.task_id, task.team_id, *, changeStatusPermissionCheck, *)
        .returning(Future.successful(None))


      val res = taskDaoService.findSingleTaskFromScyllaAndPG(
        taskId = task.task_id,
        teamId = task.team_id,
        permittedAccountIds = permittedAccountIds,
        orgId = orgId,
         //emailNotCompulsoryEnabled = false

      )

      res.map {
        result => assert(result == Left(GetAllTaskForUserError.ErrorWhileGettingTasks))
      }

    }

    //    it("should succeed if task found in cache") {
    //
    //      given logger: SRLogger = new SRLogger("testcase")
    //
    ////      (taskCacheDAO.findTaskInCache (_:String)(_: ExecutionContext,_: SRLogger))
    ////        .expects(task.task_id,*,*)
    ////        .returning(Future.successful(Right(task)))
    //
    //      val res = taskDaoService.findSingleTaskFromScyllaAndPG(
    //        taskId = task.task_id,
    //        teamId = task.team_id,
    //        permittedAccountIds = permittedAccountIds
    //      )
    //
    //      res.map{
    //        result => assert(result == Right(task))
    //      }
    //
    //    }

  }

  describe("TaskDaoService.changePriority testing") {

    it("should fail if taskPgDAO.changePriority fails") {

      (taskPgDAO.changePriority(_: String, _: TaskPriority, _: Long, _: Seq[Long])(_: ExecutionContext))
        .expects(task.task_id, task.priority, task.team_id, permittedAccountIds, *)
        .returning(Future.failed(error))

      val res = taskDaoService.changePriority(
        task_id = task.task_id,
        task_priority = task.priority,
        team_id = task.team_id,
        permittedAccountIds = permittedAccountIds
      )

      res.map {
        result => assert(result == Left(ChangePriorityError.ErrorWhileChangingPriority))
      }.recover {
        case e =>
          println("This is where I want it to go")
          assert(e == error)
      }

    }

    it("should succeed if taskCacheDAO.changePriority fails") {

      (taskPgDAO.changePriority(_: String, _: TaskPriority, _: Long, _: Seq[Long])(_: ExecutionContext))
        .expects(task.task_id, task.priority, task.team_id, permittedAccountIds, *)
        .returning(Future.successful(Some(task.task_id)))
      //
      //      (taskCacheDAO.deleteTask (_: String)( _: ExecutionContext, _:SRLogger))
      //        .expects(task.task_id, *,*)
      //        .returning(Future.failed(error))

      val res = taskDaoService.changePriority(
        task_id = task.task_id,
        task_priority = task.priority,
        team_id = task.team_id,
        permittedAccountIds = permittedAccountIds
      )

      res.map {
        result => assert(result == Right(Some(task.task_id)))
      }.recover {
        case e =>
          println("This is where I want it to go")
          assert(e == error)
      }

    }
  }

  describe("TaskDaoService.addNote testing") {

    it("should fail if taskPgDAO.addNote fails") {

      (taskPgDAO.addNote(_: String, _: String, _: Long, _: Seq[Long])(_: ExecutionContext))
        .expects(task.task_id, task.note.get, task.team_id, permittedAccountIds, *)
        .returning(Future.failed(error))

      val res = taskDaoService.addNote(
        task_id = task.task_id,
        note = task.note.get,
        team_id = task.team_id,
        permittedAccountIds = permittedAccountIds
      )

      res.map {
        result => assert(result == Left(AddNoteError.ErrorWhileAddingNote))
      }.recover {
        case e =>
          println("This is where I want it to go")
          assert(e == error)
      }

    }

    it("should succeed if taskCacheDAO.addNote fails") {

      //
      //      (taskCacheDAO.deleteTask (_: String)( _: ExecutionContext, _:SRLogger))
      //        .expects(task.task_id, *,*)
      //        .returning(Future.failed(error))

      (taskPgDAO.addNote(_: String, _: String, _: Long, _: Seq[Long])(_: ExecutionContext))
        .expects(task.task_id, task.note.get, task.team_id, permittedAccountIds, *)
        .returning(Future.successful(Some(task.task_id)))

      val res = taskDaoService.addNote(
        task_id = task.task_id,
        note = task.note.get,
        team_id = task.team_id,
        permittedAccountIds = permittedAccountIds
      )

      res.map {
        result => assert(result == Right(Some(task.task_id)))
      }.recover {
        case e =>
          println("This is where I want it to go")
          assert(e == error)
      }

    }
  }

  describe("TaskDaoService.changeStatus testing") {

    it("should fail if taskPgDAO.changeStatus fails") {

      (taskPgDAO.changeStatus(_: String, _: DateTime, _: UpdateTaskStatus, _: Long, _: ChangeStatusPermissionCheck)(_: ExecutionContext))
        .expects(task.task_id, currentTime, updateStatus, task.team_id, *, *)
        .returning(Future.failed(error))

      val res = taskDaoService.changeStatus(
        task_id = task.task_id,
        task_status = updateStatus,
        changeTime = currentTime,
        team_id = task.team_id,
        changeStatusPermissionCheck = ChangeStatusPermissionCheck.ManualTaskCheck(
          doer = Some(accountId),
          permittedAccountIds = permittedAccountIds,
        ),
        orgId = orgId,
         //emailNotCompulsoryEnabled = false
      )

      res.map {
        result => assert(result == Left(ChangeStatusTypeError.ErrorWhileChangingStatus))
      }.recover {
        case e =>
          println("This is where I want it to go")
          assert(e == error)
      }

    }

    it("should fail without creating any event when taskPgDAO.findTaskbyTaskID returns Failure") {

      (taskPgDAO.changeStatus( _: String, _: DateTime, _: UpdateTaskStatus, _: Long, _: ChangeStatusPermissionCheck)(_: ExecutionContext))
        .expects(task.task_id, currentTime, updateStatus, task.team_id, *, *)
        .returning(Future.successful(task.task_id))

      (taskPgDAO.findTaskbyTaskID(_: String, _: Long, _: OrgId, _: ChangeStatusPermissionCheck)(_: ExecutionContext))
        .expects(task.task_id, task.team_id, *, *, *)
        .returning(Future.failed(error))

      val res = taskDaoService.changeStatus(
        task_id = task.task_id,
        task_status = updateStatus,
        changeTime = currentTime,
        team_id = task.team_id,
        changeStatusPermissionCheck = ChangeStatusPermissionCheck.ManualTaskCheck(
          doer = Some(accountId),
          permittedAccountIds = permittedAccountIds,
        ),
        orgId = orgId,
         //emailNotCompulsoryEnabled = false

      )

      res.map {
        result => assert(false)
      }.recover {
        case e =>
          println("This is where I want it to go")
          assert(e == error)
      }

    }

    it("should succeed without creating any event when taskPgDAO.findTaskbyTaskID returns Failure ") {

      (taskPgDAO.changeStatus( _: String, _: DateTime, _: UpdateTaskStatus, _: Long, _: ChangeStatusPermissionCheck)(_: ExecutionContext))
        .expects(task.task_id, currentTime, updateStatus, task.team_id, *, *)
        .returning(Future.successful(task.task_id))

      (taskPgDAO.findTaskbyTaskID(_: String, _: Long, _: OrgId, _: ChangeStatusPermissionCheck)(_: ExecutionContext))
        .expects(task.task_id, task.team_id, *, *, *)
        .returning(Future.successful(None))

      (accountDAO.getAccountNameById(_: AccountId))
        .expects(AccountId(id = accountId))
        .returning(Success(Some("Shubham Kudekar")))

      //      (taskCacheDAO.deleteTask(_: String)(_: ExecutionContext, _: SRLogger))
      //        .expects(task.task_id, *, *)
      //        .returning(Future.failed(error))

      val res = taskDaoService.changeStatus(
        task_id = task.task_id,
        task_status = updateStatus,
        changeTime = currentTime,
        team_id = task.team_id,
        changeStatusPermissionCheck = ChangeStatusPermissionCheck.ManualTaskCheck(
          doer = Some(accountId),
          permittedAccountIds = permittedAccountIds,
        ),
        orgId = orgId,
         //emailNotCompulsoryEnabled = false
      )

      res.map {
        println("This is where I want it to go")
        result => assert(result == Right(task.task_id))
      }.recover {
        case e =>
          assert(e == error)
      }

    }

    it("should succeed if taskCacheDAO.changeStatus fails") {

      //      (taskCacheDAO.deleteTask (_: String)( _: ExecutionContext, _:SRLogger))
      //        .expects(task.task_id, *,*)
      //        .returning(Future.failed(error))

      (taskPgDAO.changeStatus(_: String, _: DateTime, _: UpdateTaskStatus, _: Long, _: ChangeStatusPermissionCheck)(_: ExecutionContext))
        .expects( task.task_id, currentTime, updateStatus, task.team_id, *, *)
        .returning(Future.successful(task.task_id))

      (taskPgDAO.findTaskbyTaskID(_: String, _: Long, _: OrgId, _: ChangeStatusPermissionCheck)(_: ExecutionContext))
        .expects(task.task_id, task.team_id, *, *, *)
        .returning(Future.successful(Some(task)))

      (accountDAO.getAccountNameById(_: AccountId))
        .expects(AccountId(id = accountId))
        .returning(Success(Some("Shubham Kudekar")))

      (prospectAddEventDAO.addEvents(_: Seq[CreateProspectEventDB]))
        .expects(*)
        .returning(Success(List(1L)))

      val res = taskDaoService.changeStatus(
        task_id = task.task_id,
        task_status = updateStatus,
        changeTime = currentTime,
        team_id = task.team_id,
        changeStatusPermissionCheck = ChangeStatusPermissionCheck.ManualTaskCheck(
          doer = Some(accountId),
          permittedAccountIds = permittedAccountIds,
        ),
        orgId = orgId,
         //emailNotCompulsoryEnabled = false
      )

      res.map {
        result => assert(result == Right(task.task_id))
      }.recover {
        case e =>
          println("This is where I want it to go")
          assert(e == error)
      }

    }
    it("should fail as task status is mismatched") {
      val pe = CaptureOne[Seq[CreateProspectEventDB]]()
      val newTask = task.copy(status = TaskStatus.Archive(
        archive_at = DateTime.now().plus(10),
        status_type = TaskStatusType.Archive
      ))
      val newUpdateStatus = UpdateTaskStatus.Archive(
        status_type = TaskStatusType.Archive
      )
      //
      //      (taskCacheDAO.deleteTask(_: String)(_: ExecutionContext, _: SRLogger))
      //        .expects(newTask.task_id, *, *)
      //        .returning(Future(1))

      (taskPgDAO.changeStatus (_: String, _: DateTime, _: UpdateTaskStatus, _: Long, _: ChangeStatusPermissionCheck)(_: ExecutionContext))
        .expects(task.task_id, currentTime, newUpdateStatus, task.team_id, *, *)
        .returning(Future.successful(newTask.task_id))

      (taskPgDAO.findTaskbyTaskID(_: String, _: Long, _: OrgId, _: ChangeStatusPermissionCheck)(_: ExecutionContext))
        .expects(newTask.task_id, newTask.team_id, *, *, *)
        .returning(Future.successful(Some(newTask)))

      (accountDAO.getAccountNameById(_: AccountId))
        .expects(AccountId(id = accountId))
        .returning(Success(Some("Shubham Kudekar")))


      (prospectAddEventDAO.addEvents(_: Seq[CreateProspectEventDB]))
        .expects(capture(pe))
        .returning(Success(List(1L)))


      taskDaoService.changeStatus(
        task_id = newTask.task_id,
        task_status = newUpdateStatus,
        changeTime = currentTime,
        team_id = newTask.team_id,
        changeStatusPermissionCheck = ChangeStatusPermissionCheck.ManualTaskCheck(
          doer = Some(accountId),
          permittedAccountIds = permittedAccountIds,
        ),
        orgId = orgId,
         //emailNotCompulsoryEnabled = false
      ).map(res => {
        assert(pe.value.head.event_type != ProspectEventDAO.getEventFromTaskType(taskStatusType = task.status.status_type).get)
      }).recover(e => {
        assert(false)
      })


    }
    it("should match the task status type of task obj and the generated event") {
      val pe = CaptureOne[Seq[CreateProspectEventDB]]()

      //      (taskCacheDAO.deleteTask(_: String)(_: ExecutionContext, _: SRLogger))
      //        .expects(task.task_id, *, *)
      //        .returning(Future(1))

      (taskPgDAO.changeStatus(_: String, _: DateTime, _: UpdateTaskStatus, _: Long, _: ChangeStatusPermissionCheck)(_: ExecutionContext))
        .expects(task.task_id, currentTime, updateStatus, task.team_id,*, *)
        .returning(Future.successful(task.task_id))

      (taskPgDAO.findTaskbyTaskID(_: String, _: Long, _: OrgId, _: ChangeStatusPermissionCheck)(_: ExecutionContext))
        .expects(task.task_id, task.team_id, *, *, *)
        .returning(Future.successful(Some(task)))

      (accountDAO.getAccountNameById(_: AccountId))
        .expects(AccountId(id = accountId))
        .returning(Success(Some("Shubham Kudekar")))


      (prospectAddEventDAO.addEvents(_: Seq[CreateProspectEventDB]))
        .expects(capture(pe))
        .returning(Success(List(1L)))


      taskDaoService.changeStatus(
        task_id = task.task_id,
        task_status = updateStatus,
        changeTime = currentTime,
        team_id = task.team_id,
        changeStatusPermissionCheck = ChangeStatusPermissionCheck.ManualTaskCheck(
          doer = Some(accountId),
          permittedAccountIds = permittedAccountIds,
        ),
        orgId = orgId,
         //emailNotCompulsoryEnabled = false
      ).map(res => {
        assert(pe.value.head.event_type != ProspectEventDAO.getEventFromTaskType(taskStatusType = task.status.status_type).get)
      }).recover(e => {
        assert(true)
      })


    }
  }

  describe("TaskDaoService.deleteTask testing") {

    it("should fail if taskPgDAO.deleteTask fails") {

      (taskPgDAO.deleteTaskForRevert(_: List[String], _: TeamId, _: Option[TaskStatusType], _: Seq[Long])(using _: SRLogger))
        .expects(List(task.task_id), TeamId(task.team_id), None, permittedAccountIds, *)
        .returning(Failure(error))

      val res = taskDaoService.deleteTask(
        taskId = task.task_id,
        team_id = task.team_id,
        permittedAccountIds = permittedAccountIds
      )

      res.map {
        result => assert(result.taskId.map(_.id).contains(task.task_id))
      }.recover {
        case e => assert(e == error)
      }

    }

    //    it("should fail if taskPgDAO.deleteTask return None"){
    //
    //      (taskPgDAO.deleteTask(_: String, _: Long)( _: ExecutionContext))
    //        .expects(task.task_id, task.team_id, *)
    //        .returning(Future.successful(None))
    //
    //      val res = taskDaoService.deleteTask(
    //        taskId = task.task_id,
    //        team_id = task.team_id
    //      )
    //
    //      res.map{
    //        result => assert(result == Left(DeleteTaskError.TaskNotFoundWhileDeleting))
    //      }
    //
    //    }

    it("should succeed even if taskCacheDAO.deleteTask fails") {

      //      (taskCacheDAO.deleteTask (_: String)( _: ExecutionContext , _:SRLogger))
      //        .expects(task.task_id, *, *)
      //        .returning(Future.failed(error))

      (taskPgDAO.deleteTaskForRevert(_: List[String], _: TeamId, _: Option[TaskStatusType], _: Seq[Long])(using _: SRLogger))
        .expects(List(task.task_id), TeamId(task.team_id), None, permittedAccountIds, *)
        .returning(Success(List(OptionCampaignIdAndOptionProspectIdAndOptionEmailSettingId(
          campaignId = None,
          prospectId = None,
          emailSettingId = None,
          taskId = Some(TaskId(task.task_id)),
          team_id = TeamId(task.team_id),
          step_id = Some(step_id_VC)
        ))))
      (campaignProspectStepScheduleLogsDAO.insert(_: Seq[CampaignProspectStepScheduleLogData])(using _: SRLogger))
        .expects(List(), *)
        .returning(Success(List()))
      val res = taskDaoService.deleteTask(
        taskId = task.task_id,
        team_id = task.team_id,
        permittedAccountIds = permittedAccountIds
      )

      res.map {
        result => assert(result.taskId.map(_.id).contains(task.task_id))
      }

    }

    it("should succeed if everything succeeds") {
      //
      //      (taskCacheDAO.deleteTask (_: String)( _: ExecutionContext , _:SRLogger))
      //        .expects(task.task_id, *, *)
      //        .returning(Future.successful(1))

      (taskPgDAO.deleteTaskForRevert(_: List[String], _: TeamId, _: Option[TaskStatusType], _: Seq[Long])(using _: SRLogger))
        .expects(List(task.task_id), TeamId(task.team_id), None, permittedAccountIds, *)
        .returning(Success(List(OptionCampaignIdAndOptionProspectIdAndOptionEmailSettingId(
          campaignId = None,
          prospectId = None,
          emailSettingId = None,
          taskId = Some(TaskId(task.task_id)),
          team_id = TeamId(task.team_id),
          step_id = Some(step_id_VC)
        ))))
      (campaignProspectStepScheduleLogsDAO.insert(_: Seq[CampaignProspectStepScheduleLogData])(using _: SRLogger))
        .expects(List(), *)
        .returning(Success(List()))
      val res = taskDaoService.deleteTask(
        taskId = task.task_id,
        team_id = task.team_id,
        permittedAccountIds = permittedAccountIds
      )

      res.map {
        result => assert(result.taskId.map(_.id).contains(task.task_id))
      }

    }
  }

  val taskCache = TaskCache(
    task_id = "1", task_json = Json.toJson(task.copy(task_id = "1")).toString(), created_at = DateTime.now())

  val listTaskCache = List(taskCache,
    taskCache.copy(task_id = "2", task_json = Json.toJson(task.copy(task_id = "2")).toString()),
    taskCache.copy(task_id = "5", task_json = Json.toJson(task.copy(task_id = "5")).toString())
  )

  val foundTasks = List(task.copy("1"), task.copy("2"), task.copy("5"))
  val notFoundTask = List(task.copy(task_id = "3"),
    task.copy(task_id = "4"))
  val taskIds = List("1", "2", "3", "4", "5")

  val listTaskGarbageCache = List(taskCache,
    taskCache.copy(task_id = "2", task_json = Json.toJson("hi").toString()),
    taskCache.copy(task_id = "5", task_json = Json.toJson(task.copy(task_id = "5")).toString())
  )

  describe("TaskDaoService.fetchTaskFromScyllaAndPg testing") {


    //      it("Should find tasks not found in scylla and succeed") {
    //
    ////        (taskCacheDAO.batchFetchTasks(_: List[String])(_: ExecutionContext, _: SRLogger))
    ////          .expects(taskIds, *, *)
    ////          .returning(Future.successful(listTaskCache)) // returning ids 1, 2, 5
    //
    //        (taskPgDAO.findBatchTasksFromDB(_: List[String], _: Long, _: Seq[Long])(_: ExecutionContext))
    //          .expects(*, *,permittedAccountIds, *)
    //          .returning(Future.successful(notFoundTask))
    //
    ////        (taskCacheDAO.updateTask(_: TaskCache)(_: ExecutionContext, _: SRLogger))
    ////          .expects(*, *, *)
    ////          .returning(Future.successful(1)).twice()
    //
    //        val res = taskDaoService.findBatchTasksFromScyllaAndPg(
    //          taskIds = taskIds, // 1. 2. 3. 4. 5
    //          team_id = task.team_id,
    //          permittedAccountIds = permittedAccountIds
    //        )
    //
    //        res.map {
    //          result => assert(result == notFoundTask ++ foundTasks)
    //        }
    //
    //      }

    //      it("Should get JsError then find from db add to scylla and succeed") {
    //
    ////
    ////        (taskCacheDAO.batchFetchTasks(_: List[String])(_: ExecutionContext, _: SRLogger))
    ////          .expects(taskIds, *, *)
    ////          .returning(Future.successful(listTaskGarbageCache)) // returning ids 1, 2, 5
    //
    //        (taskPgDAO.findTaskbyTaskID(_: String, _: Long, _: Seq[Long])(_: ExecutionContext))
    //          .expects("2", task.team_id,permittedAccountIds, *)
    //          .returning(Future.successful(Some(task.copy(task_id = "2"))))
    //
    //        (taskPgDAO.findBatchTasksFromDB(_: List[String], _: Long, _: Seq[Long])(_: ExecutionContext))
    //          .expects(*, *, permittedAccountIds, *)
    //          .returning(Future.successful(notFoundTask))
    //
    ////        (taskCacheDAO.updateTask(_: TaskCache)(_: ExecutionContext, _: SRLogger))
    ////          .expects(*, *, *)
    ////          .returning(Future.successful(1)).atLeastTwice()
    //
    //        val res = taskDaoService.findBatchTasksFromScyllaAndPg(
    //          taskIds = taskIds, // 1. 2. 3. 4. 5
    //          team_id = task.team_id,
    //          permittedAccountIds = permittedAccountIds
    //        )
    //
    //        res.map {
    //          result => assert(result == notFoundTask ++ foundTasks)
    //        }
    //
    //      }

    //      it("Should get JsError then find from db add get scylla insertion error and succeed") {
    //
    //        val listTaskGarbageCache = List(taskCache,
    //          taskCache.copy(task_id = "2", task_json = Json.toJson("hi").toString()),
    //          taskCache.copy(task_id = "5", task_json = Json.toJson(task.copy(task_id = "5")).toString())
    //        )
    //
    ////        (taskCacheDAO.batchFetchTasks(_: List[String])(_: ExecutionContext, _: SRLogger))
    ////          .expects(taskIds, *, *)
    ////          .returning(Future.successful(listTaskGarbageCache)) // returning ids 1, 2, 5
    //
    //        (taskPgDAO.findTaskbyTaskID(_: String, _: Long, _: Seq[Long])(_: ExecutionContext))
    //          .expects("2", task.team_id,permittedAccountIds, *)
    //          .returning(Future.successful(Some(task.copy(task_id = "2"))))
    //
    //        (taskPgDAO.findBatchTasksFromDB(_: List[String], _: Long, _: Seq[Long])(_: ExecutionContext))
    //          .expects(*, *,permittedAccountIds, *)
    //          .returning(Future.successful(notFoundTask))
    ////
    ////        (taskCacheDAO.updateTask(_: TaskCache)(_: ExecutionContext, _: SRLogger))
    ////          .expects(*, *, *)
    ////          .returning(Future.failed(error)).atLeastTwice()
    //
    //        val res = taskDaoService.findBatchTasksFromScyllaAndPg(
    //          taskIds = taskIds, // 1. 2. 3. 4. 5
    //          team_id = task.team_id,
    //          permittedAccountIds = permittedAccountIds
    //        )
    //
    //        res.map {
    //          result => assert(result == notFoundTask ++ foundTasks)
    //        }
    //
    //      }

  }

  describe("TaskDaoService.updateTask testing") {

    it("should fail if taskPgDAO.updateTask fails") {

      (taskPgDAO.updateTask(_: UpdateTask, _: String, _: Long, _: Seq[Long])(_: ExecutionContext))
        .expects(updateTask, task.task_id, task.team_id, permittedAccountIds, *)
        .returning(Future.failed(error))

      val res = taskDaoService.updateTask(
        update_task_data = updateTask,
        taskId = task.task_id,
        teamId = task.team_id,
        permittedAccountIds = permittedAccountIds
      )

      res.map {
        result => assert(result.contains(task.task_id))
      }.recover {
        case e =>
          println("This is where I want it to go")
          assert(e == error)
      }

    }

    it("should succeed if taskCacheDAO.updateTask fails") {

      (taskPgDAO.updateTask(_: UpdateTask, _: String, _: Long, _: Seq[Long])(_: ExecutionContext))
        .expects(updateTask, task.task_id, task.team_id, permittedAccountIds, *)
        .returning(Future.successful(Some(task.task_id)))

      //      (taskCacheDAO.deleteTask (_: String)( _: ExecutionContext, _:SRLogger))
      //        .expects(task.task_id, *,*)
      //        .returning(Future.failed(error))

      val res = taskDaoService.updateTask(
        update_task_data = updateTask,
        taskId = task.task_id,
        teamId = task.team_id,
        permittedAccountIds = permittedAccountIds
      )

      res.map {
        result => assert(result.contains(task.task_id))
      }.recover {
        case e =>
          println("This is where I want it to go")
          assert(e == error)
      }

    }
  }

  describe("TaskDaoService.deleteBatchTasks testing") {

    it("should fail if taskPgDAO.deleteBatchTask fails") {


      (taskPgDAO.deleteTaskForRevert(_: List[String], _: TeamId, _: Option[TaskStatusType], _: Seq[Long])(using _: SRLogger))
        .expects(taskIds, TeamId(task.team_id), None, permittedAccountIds, *)
        .returning(Failure(error))

      val res = taskDaoService.deleteBatchTasks(
        taskIds = taskIds,
        teamId = task.team_id,
        permittedAccountIds = permittedAccountIds
      )

      res.map {
        result => assert(result == taskIds)
      }.recover {
        case e =>
          println("This is where I want it to go")
          assert(e == error)
      }

    }

    it("should succeed if taskCacheDAO.deleteBatchTask fails") {

      (taskPgDAO.deleteTaskForRevert(_: List[String], _: TeamId, _: Option[TaskStatusType], _: Seq[Long])(using _: SRLogger))
        .expects(taskIds, TeamId(task.team_id), None, permittedAccountIds, *)
        .returning(Success(taskIds.map(taskId => OptionCampaignIdAndOptionProspectIdAndOptionEmailSettingId(
          campaignId = None,
          prospectId = None,
          emailSettingId = None,
          taskId = Some(TaskId(taskId)),
          team_id = TeamId(task.team_id),
          step_id = Some(step_id_VC)
        ))))

      //      (taskCacheDAO.batchDeleteTasks (_: List[String])( _: ExecutionContext, _:SRLogger))
      //        .expects(taskIds, *,*)
      //        .returning(Future.failed(error))
      (campaignProspectStepScheduleLogsDAO.insert(_: Seq[CampaignProspectStepScheduleLogData])(using _: SRLogger))
        .expects(List(), *)
        .returning(Success(List()))
      val res = taskDaoService.deleteBatchTasks(
        taskIds = taskIds,
        teamId = task.team_id,
        permittedAccountIds = permittedAccountIds
      )

      res.map {
        result => assert(result == taskIds)
      }.recover {
        case e =>
          println("This is where I want it to go")
          assert(e == error)
      }

    }
  }

  /* describe("TaskDaoService.getAllTasksForUser testing") {

 //    it("should fail if taskPgDAO.getAllTasksForUser fails") {

 //      (taskPgDAO.getTasksForUser(_: Long, _: Option[List[Long]], _: Option[List[String]], _: Option[List[String]], _: Option[List[String]], _: Seq[Long])(_: ExecutionContext))
 //        .expects(task.team_id, *, *, *, *,permittedAccountIds, *)
 //        .returning(Future.failed(error))

       val res = taskDaoService.getAllTasksForUser(
         team_id = task.team_id,
         assignee_ids = None,
         task_type = None,
         task_status = None,
         task_priority = None,
         permittedAccountIds = permittedAccountIds
       )

       res.map {
         result => assert(result == taskIds)
       }.recover {
         case e =>
           println("This is where I want it to go")
           assert(e == error)
       }

     }*/

  /*it("should succeed if taskCacheDAO.updateTask while inserting data fails") {

    (taskPgDAO.getTasksForUser(_: Long, _: Option[List[Long]], _: Option[List[String]], _: Option[List[String]], _: Option[List[String]], _:Seq[Long])(_: ExecutionContext))
      .expects(task.team_id, *, *, *, *,permittedAccountIds, *)
      .returning(Future.successful(foundTasks))

    (taskCacheDAO.updateTask(_: TaskCache)(_: ExecutionContext, _: SRLogger))
      .expects(*, *, *)
      .returning(Future.failed(error)).atLeastTwice()

    val res = taskDaoService.getAllTasksForUser(
      team_id = task.team_id,
      assignee_ids = None,
      task_type = None,
      task_status = None,
      task_priority = None,
      permittedAccountIds = permittedAccountIds
    )

    res.map {
      result => assert(result == foundTasks)
    }.recover {
      case e =>
        println("This is where I want it to go")
        assert(e == error)
    }

  }*/

  /*it("should succeed when everything succeeds ") {

    (taskPgDAO.getTasksForUser(_: Long, _: Option[List[Long]], _: Option[List[String]], _: Option[List[String]], _: Option[List[String]], _: Seq[Long])(_: ExecutionContext))
      .expects(task.team_id, *, *, *, *,permittedAccountIds, *)
      .returning(Future.successful(foundTasks))

    (taskCacheDAO.updateTask(_: TaskCache)(_: ExecutionContext, _: SRLogger))
      .expects(*, *, *)
      .returning(Future.successful(1)).atLeastTwice()

    val res = taskDaoService.getAllTasksForUser(
      team_id = task.team_id,
      assignee_ids = None,
      task_type = None,
      task_status = None,
      task_priority = None,
      permittedAccountIds = permittedAccountIds
    )

    res.map {
      result => assert(result == foundTasks)
    }.recover {
      case e =>
        println("This is where I want it to go")
        assert(e == error)
    }

  }
}*/

  describe("TaskDaoService.getAllTasksForUserV2 testing") {

    it("should fail if taskPgDAO.getTaskIdsForUser fails") {


      (taskPgDAO.getTaskIdsForUserV2(_: SQL[Nothing, NoExtractor])(_: ExecutionContext))
        .expects(*, *)
        .returning(Future.failed(error))

      val res = taskDaoService.getAllTaskForUserV2(
        team_id = task.team_id,
        orgId = orgId,
        assignee_ids = None,
        campaign_ids = None,
        reply_sentiment_uuids = None,
        task_type = None,
        task_status = None,
        task_priority = None,
        timeZone = "UTC",
        validatedTaskReq = ValidatedTaskReq.ValidatedTaskReqRange(
          timeline = InferredQueryTimeline.Range.After(DateTime.now()),
          pageSize = 100
        ),
        doNotFetchAutomatedDueTasks = false,
        timeBasedTaskType = Some(TimeBasedTaskType.Today),
        permittedAccountIds = permittedAccountIds,
         //emailNotCompulsoryEnabled = false
      )

      res.map {
        result => assert(result == Right(List(task)))
      }.recover {
        case e =>
          println("This is where I want it to go")
          assert(e == error)
      }

    }

    //    it("Should find tasks based on taskId provided by pg then find tasks which are  not found in scylla from pg and succeed") {
    //
    //      (taskPgDAO.getTaskIdsForUserV2(_: SQL[Nothing, NoExtractor])(_: ExecutionContext))
    //        .expects(*, *)
    //        .returning(Future.successful(taskIds))
    ////
    ////      (taskCacheDAO.batchFetchTasks(_: List[String])(_: ExecutionContext, _: SRLogger))
    ////        .expects(taskIds, *, *)
    ////        .returning(Future.successful(listTaskCache)) // returning ids 1, 2, 5
    //
    //      (taskPgDAO.findBatchTasksFromDB(_: List[String], _: Long, _: Seq[Long])(_: ExecutionContext))
    //        .expects(*, *,permittedAccountIds,  *)
    //        .returning(Future.successful(notFoundTask))
    //
    ////      (taskCacheDAO.updateTask(_: TaskCache)(_: ExecutionContext, _: SRLogger))
    ////        .expects(*, *, *)
    ////        .returning(Future.successful(1)).twice()
    //
    //      val res = taskDaoService.getAllTaskForUserV2(
    //        team_id = task.team_id,
    //        assignee_ids = None,
    //        task_type = None,
    //        task_status = None,
    //        task_priority = None,
    //        timeZone = "UTC",
    //        validatedTaskReq = ValidatedTaskReq.ValidatedTaskReqRange(
    //          timeline = InferredQueryTimeline.Range.After(DateTime.now()),
    //          pageSize = 100
    //        ),
    //        timeBasedTaskType = Some(TimeBasedTaskType.Today),
    //        permittedAccountIds = permittedAccountIds
    //      )
    //
    //      res.map {
    //        result => assert(result == Right(notFoundTask ++  foundTasks ))
    //      }
    //
    //    }
  }

  describe("testing fetchTasksToBeScheduled") {
    it("should return query for general_channel_settings") {

      val expected =
        """
          |UPDATE
          |  general_channel_settings s_rows
          |SET
          |  in_queue_for_scheduling = true,
          |  pushed_to_queue_for_scheduling_at = now(),
          |  updated_at = now()
          |FROM
          |  (
          |    SELECT
          |      s.uuid,
          |      s.team_id
          |    FROM
          |      general_channel_settings s
          |      INNER JOIN campaigns c ON (
          |      c.team_id = s.team_id AND
          |      c.status in (?, ?))
          |      INNER JOIN teams t ON t.id = s.team_id
          |      INNER JOIN accounts a ON a.id = s.owner_account_id
          |      INNER JOIN organizations o ON o.id = a.org_id
          |    WHERE
          |      o.plan_type != ?
          |      AND t.active
          |      AND a.active
          |      -- if payment due for a while: no need to schedule
          |      AND (o.payment_due_campaign_pause_at IS NULL OR o.payment_due_campaign_pause_at > now())
          |      AND (o.paused_till IS NULL
          |        OR o.paused_till < now())
          |      AND (
          |        (
          |          s.in_queue_for_scheduling = FALSE
          |                      AND EXISTS (
          |                        SELECT ces1.campaign_id from campaign_channel_settings ces1
          |                        INNER JOIN campaigns c1 ON (c1.id = ces1.campaign_id and c1.team_id = ces1.team_id AND c1.status = ?)
          |                        where ces1.channel_settings_uuid = s.uuid
          |                        and ces1.team_id = s.team_id
          |                        and (
          |                              ces1.next_to_be_scheduled_at IS NULL OR
          |                              ces1.next_to_be_scheduled_at < now()
          |                            )
          |                        LIMIT 1
          |
          |                      )
          |        )
          |        OR s.pushed_to_queue_for_scheduling_at < now() - interval '60 minutes'
          |      )
          |    ORDER BY
          |      s.uuid,
          |      s.pushed_to_queue_for_scheduling_at ASC
          |  ) filtered_campaign_rows
          |WHERE
          |  s_rows.uuid = filtered_campaign_rows.uuid RETURNING filtered_campaign_rows.uuid, filtered_campaign_rows.team_id
          |
          |""".stripMargin
      val qry_str = TaskPgDAO.getFetchTaskScheduledQuery(Settings_Table.GeneralSetting)
        .statement

      val split = expected.split(s"\\s+")
      val rhs = split.reduce((a1, a2) => {
        a1 + " " + a2
      })
      val lhs = qry_str.split("\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })

      assert(lhs == rhs)
    }

    it("should return query for whatsapp_settings") {

      val expected =
        """
          |UPDATE
          | whatsapp_settings s_rows
          |SET
          |  in_queue_for_scheduling = true,
          |  pushed_to_queue_for_scheduling_at = now(),
          |  updated_at = now()
          |FROM
          |  (
          |    SELECT
          |      s.uuid,
          |      s.team_id
          |    FROM
          |      whatsapp_settings s
          |      INNER JOIN campaigns c ON (
          |      c.team_id = s.team_id AND
          |      c.status in (?, ?))
          |      INNER JOIN teams t ON t.id = s.team_id
          |      INNER JOIN accounts a ON a.id = s.owner_account_id
          |      INNER JOIN organizations o ON o.id = a.org_id
          |    WHERE
          |      o.plan_type != ?
          |      AND t.active
          |      AND a.active
          |      -- if payment due for a while: no need to schedule
          |      AND (o.payment_due_campaign_pause_at IS NULL OR o.payment_due_campaign_pause_at > now())
          |      AND (o.paused_till IS NULL
          |        OR o.paused_till < now())
          |      AND (
          |        (
          |          s.in_queue_for_scheduling = FALSE
          |                      AND EXISTS (
          |                        SELECT ces1.campaign_id from campaign_channel_settings ces1
          |                        INNER JOIN campaigns c1 ON (c1.id = ces1.campaign_id and c1.team_id = ces1.team_id AND c1.status = ?)
          |                        where ces1.channel_settings_uuid = s.uuid
          |                        and ces1.team_id = s.team_id
          |                        and (
          |                              ces1.next_to_be_scheduled_at IS NULL OR
          |                              ces1.next_to_be_scheduled_at < now()
          |                            )
          |                        LIMIT 1
          |
          |                      )
          |        )
          |        OR s.pushed_to_queue_for_scheduling_at < now() - interval '60 minutes'
          |      )
          |    ORDER BY
          |      s.uuid,
          |      s.pushed_to_queue_for_scheduling_at ASC
          |  ) filtered_campaign_rows
          |WHERE
          |  s_rows.uuid = filtered_campaign_rows.uuid RETURNING filtered_campaign_rows.uuid, filtered_campaign_rows.team_id
          |
          |""".stripMargin
      val qry_str = TaskPgDAO.getFetchTaskScheduledQuery(Settings_Table.WhatsAppSetting)
        .statement

      val split = expected.split(s"\\s+")
      val rhs = split.reduce((a1, a2) => {
        a1 + " " + a2
      })
      val lhs = qry_str.split("\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })

      assert(lhs == rhs)
    }

    it("should return query for linkedin_settings") {

      val expected =
        """
          |UPDATE
          | linkedin_settings s_rows
          |SET
          |  in_queue_for_scheduling = true,
          |  pushed_to_queue_for_scheduling_at = now(),
          |  updated_at = now()
          |FROM
          |  (
          |    SELECT
          |      s.uuid,
          |      s.team_id
          |    FROM
          |      linkedin_settings s
          |      INNER JOIN campaigns c ON (
          |      c.team_id = s.team_id AND
          |      c.status in (?, ?))
          |      INNER JOIN teams t ON t.id = s.team_id
          |      INNER JOIN accounts a ON a.id = s.owner_account_id
          |      INNER JOIN organizations o ON o.id = a.org_id
          |    WHERE
          |      o.plan_type != ?
          |      AND t.active
          |      AND a.active
          |      -- if payment due for a while: no need to schedule
          |      AND (o.payment_due_campaign_pause_at IS NULL OR o.payment_due_campaign_pause_at > now())
          |      AND (o.paused_till IS NULL
          |        OR o.paused_till < now())
          |      AND (
          |        (
          |          s.in_queue_for_scheduling = FALSE
          |                      AND EXISTS (
          |                        SELECT ces1.campaign_id from campaign_channel_settings ces1
          |                        INNER JOIN campaigns c1 ON (c1.id = ces1.campaign_id and c1.team_id = ces1.team_id AND c1.status = ?)
          |                        where ces1.channel_settings_uuid = s.uuid
          |                        and ces1.team_id = s.team_id
          |                        and (
          |                              ces1.next_to_be_scheduled_at IS NULL OR
          |                              ces1.next_to_be_scheduled_at < now()
          |                            )
          |                        LIMIT 1
          |
          |                      )
          |        )
          |        OR s.pushed_to_queue_for_scheduling_at < now() - interval '60 minutes'
          |      )
          |    ORDER BY
          |      s.uuid,
          |      s.pushed_to_queue_for_scheduling_at ASC
          |  ) filtered_campaign_rows
          |WHERE
          |  s_rows.uuid = filtered_campaign_rows.uuid RETURNING filtered_campaign_rows.uuid, filtered_campaign_rows.team_id
          |
          |""".stripMargin
      val qry_str = TaskPgDAO.getFetchTaskScheduledQuery(Settings_Table.LinkedinSetting)
        .statement

      val split = expected.split(s"\\s+")
      val rhs = split.reduce((a1, a2) => {
        a1 + " " + a2
      })
      val lhs = qry_str.split("\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })

      assert(lhs == rhs)
    }

    it("should return query for sms_settings") {
      val expected =
        """
          |UPDATE sms_settings s_rows
          |           SET
          |             in_queue_for_scheduling = true,
          |             pushed_to_queue_for_scheduling_at = now(),
          |             updated_at = now()
          |           FROM (
          |             SELECT s.uuid, s.team_id
          |             FROM sms_settings s
          |             INNER JOIN campaigns c ON (
          |             c.team_id = s.team_id AND
          |             c.status in (?, ?))
          |             INNER JOIN teams t ON t.id = s.team_id
          |             INNER JOIN accounts a ON a.id = s.owner_account_id
          |             INNER JOIN organizations o ON o.id = a.org_id
          |             WHERE  o.plan_type != ? AND t.active AND a.active
          |
          |             -- if payment due for a while: no need to schedule
          |             AND (o.payment_due_campaign_pause_at IS NULL OR o.payment_due_campaign_pause_at > now())
          |
          |
          |             AND (o.paused_till IS NULL OR o.paused_till < now())
          |             AND (
          |                    (
          |                      s.in_queue_for_scheduling = FALSE
          |                      AND EXISTS (
          |                        SELECT ces1.campaign_id from campaign_channel_settings ces1
          |                        INNER JOIN campaigns c1 ON (c1.id = ces1.campaign_id and c1.team_id = ces1.team_id AND c1.status = ?)
          |                        where ces1.channel_settings_uuid = s.uuid
          |                        and ces1.team_id = s.team_id
          |                        and (
          |                              ces1.next_to_be_scheduled_at IS NULL OR
          |                              ces1.next_to_be_scheduled_at < now()
          |                            )
          |                        LIMIT 1
          |
          |                      )
          |                    )
          |                    OR
          |                    s.pushed_to_queue_for_scheduling_at < now() - interval '60 minutes'
          |                 )
          |
          |              ORDER BY s.uuid, s.pushed_to_queue_for_scheduling_at ASC
          |
          |             ) filtered_campaign_rows
          |           WHERE s_rows.uuid = filtered_campaign_rows.uuid
          |           RETURNING filtered_campaign_rows.uuid, filtered_campaign_rows.team_id
          |""".stripMargin

      val qry_str = TaskPgDAO.getFetchTaskScheduledQuery(Settings_Table.SmsSetting)
        .statement

      val split = expected.split(s"\\s+")
      val rhs = split.reduce((a1, a2) => {
        a1 + " " + a2
      })
      val lhs = qry_str.split("\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })

      assert(lhs == rhs)


    }

    it("should return query for call_settings") {
      val expected =
        """
          |UPDATE call_settings s_rows
          |           SET
          |             in_queue_for_scheduling = true,
          |             pushed_to_queue_for_scheduling_at = now(),
          |             updated_at = now()
          |           FROM (
          |             SELECT s.uuid, s.team_id
          |             FROM call_settings s
          |             INNER JOIN campaigns c ON (
          |             c.team_id = s.team_id AND
          |             c.status in (?, ?))
          |             INNER JOIN teams t ON t.id = s.team_id
          |             INNER JOIN accounts a ON a.id = s.owner_account_id
          |             INNER JOIN organizations o ON o.id = a.org_id
          |             WHERE  o.plan_type != ? AND t.active AND a.active and s.is_active
          |
          |             -- if payment due for a while: no need to schedule
          |             AND (o.payment_due_campaign_pause_at IS NULL OR o.payment_due_campaign_pause_at > now())
          |
          |
          |             AND (o.paused_till IS NULL OR o.paused_till < now())
          |             AND (
          |                    (
          |                      s.in_queue_for_scheduling = FALSE
          |                      AND EXISTS (
          |                        SELECT ces1.campaign_id from campaign_channel_settings ces1
          |                        INNER JOIN campaigns c1 ON (c1.id = ces1.campaign_id and c1.team_id = ces1.team_id AND c1.status = ?)
          |                        where ces1.channel_settings_uuid = s.uuid
          |                        and ces1.team_id = s.team_id
          |                        and (
          |                              ces1.next_to_be_scheduled_at IS NULL OR
          |                              ces1.next_to_be_scheduled_at < now()
          |                            )
          |                        LIMIT 1
          |
          |                      )
          |                    )
          |                    OR
          |                    s.pushed_to_queue_for_scheduling_at < now() - interval '60 minutes'
          |                 )
          |
          |              ORDER BY s.uuid, s.pushed_to_queue_for_scheduling_at ASC
          |
          |             ) filtered_campaign_rows
          |           WHERE s_rows.uuid = filtered_campaign_rows.uuid
          |           RETURNING filtered_campaign_rows.uuid, filtered_campaign_rows.team_id
          |""".stripMargin

      val qry_str = TaskPgDAO.getFetchTaskScheduledQuery(Settings_Table.CallSetting)
        .statement

      val split = expected.split(s"\\s+")
      val rhs = split.reduce((a1, a2) => {
        a1 + " " + a2
      })
      val lhs = qry_str.split("\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })

      assert(lhs == rhs)


    }
  }

}
