package app.api.free_email_domain.service



import api.emails.daos.DomainPublicDNSDAO
import api.{CacheServiceJedis, CacheServiceJedisTTLCheckError}
import api.free_email_domain.dao.FreeEmailDomainListDAO
import api.free_email_domain.service.{AddingDomainToFreeListError, AddingDomainToWhitelistError, FreeEmailDomainListService, UserCheckApiService}
import api.gpt.services.GptApiService
import org.scalamock.scalatest.MockFactory
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.funspec.AnyFunSpec
import utils.SRLogger

import scala.util.{Failure, Success, Try}

class FreeEmailDomainListServiceSpec extends AnyFunSpec with MockFactory{

  val SQLError = new Throwable("SQLERROR")

  val mockFreeEmailDomainListDAO = mock[FreeEmailDomainListDAO]
  val mockCacheServiceJedis= mock[CacheServiceJedis]
  val mockDomainPublicDNSDAO= mock[DomainPublicDNSDAO]
  val mockUserCheckApiService = mock[UserCheckApiService]



  given Logger: SRLogger = new SRLogger("FreeEmailDomainListServiceSpec")

  val freeEmailDomainListService = new FreeEmailDomainListService(
    freeEmailDomainListDAO = mockFreeEmailDomainListDAO,
    cacheServiceJedis = mockCacheServiceJedis,
    domainPublicDNSDAO = mockDomainPublicDNSDAO,
    userCheckApiService = mockUserCheckApiService


  )

  describe("FreeEmailDomainListService.isFreeEmailDomain") {
    it("Data is found in db") {

      (mockCacheServiceJedis.ttl)
      .expects("free_email_service_domains")
        .returning(Right(10))
      (mockCacheServiceJedis.sismember)
        .expects("free_email_service_domains", "gmail.com")
        .returning(Success(true))
      val result = freeEmailDomainListService.isFreeEmailDomain("gmail.com")

      assert(result == Success(true))
    }
    it("should fail for mockFreeEmailDomainListDAO.checkIfDomainIsFree") {
      (mockCacheServiceJedis.ttl)
        .expects("free_email_service_domains")
        .returning(Right(10))
      (mockCacheServiceJedis.sismember)
      .expects("free_email_service_domains", "gmail.com")
        .returning(Failure(SQLError))
      (mockFreeEmailDomainListDAO.checkIfDomainIsFree)
        .expects("gmail.com")
        .returning(Failure(SQLError))
      (mockDomainPublicDNSDAO.getForFreeDomain)
        .expects("gmail.com")
        .returning(Failure(SQLError))
      val result = freeEmailDomainListService.isFreeEmailDomain("gmail.com")

      assert(result == Failure(SQLError))
    }

    it("should Success for mockFreeEmailDomainListDAO.checkIfDomainIsFree") {
      (mockCacheServiceJedis.ttl)
        .expects("free_email_service_domains")
        .returning(Right(10))
      (mockCacheServiceJedis.deleteKey)
        .expects("free_email_service_domains")
        .returning(Success(10))
      (() => mockFreeEmailDomainListDAO.getAllInFreeList)
      .expects()
        .returning(Success(Set("gmail.com")))
      (mockCacheServiceJedis.sismember)
        .expects("free_email_service_domains", "gmail.com")
        .returning(Failure(SQLError))
      (mockFreeEmailDomainListDAO.checkIfDomainIsFree)
        .expects("gmail.com")
        .returning(Success(true))
      val result = freeEmailDomainListService.isFreeEmailDomain("gmail.com")

      assert(result == Success(true))
    }
  }

  describe("FreeEmailDomainListService.checkIfFreeEmailService") {
    it("should success because found in cache") {
      (mockCacheServiceJedis.ttl)
        .expects("free_email_service_domains")
        .returning(Right(10))
      (mockCacheServiceJedis.sismember)
        .expects("free_email_service_domains", "gmail.com")
        .returning(Success(true))
      (mockFreeEmailDomainListDAO.checkIfWhiteListedForSignUp)
        .expects("<EMAIL>")
        .returning(Success(false))

      val result = freeEmailDomainListService.checkIfFreeEmailService("<EMAIL>")

      assert(result == Success(true))
    }

    it("should fail for mockFreeEmailDomainListDAO.checkIfDomainIsFree") {
      (mockCacheServiceJedis.ttl)
        .expects("free_email_service_domains")
        .returning(Right(10))
      (mockCacheServiceJedis.sismember)
        .expects("free_email_service_domains", "gmail.com")
        .returning(Failure(SQLError))
      (mockFreeEmailDomainListDAO.checkIfDomainIsFree)
        .expects("gmail.com")
        .returning(Failure(SQLError))
      (mockDomainPublicDNSDAO.getForFreeDomain)
        .expects("gmail.com")
        .returning(Failure(SQLError))
      val result = freeEmailDomainListService.checkIfFreeEmailService("<EMAIL>")

      assert(result == Failure(SQLError))
    }

    it("should Success for mockFreeEmailDomainListDAO.checkIfDomainIsFree") {
      (mockCacheServiceJedis.ttl)
        .expects("free_email_service_domains")
        .returning(Right(10))
      (mockCacheServiceJedis.deleteKey)
        .expects("free_email_service_domains")
        .returning(Success(10))
      (() => mockFreeEmailDomainListDAO.getAllInFreeList)
        .expects()
        .returning(Success(Set("gmail.com")))
      (mockCacheServiceJedis.sismember)
        .expects("free_email_service_domains", "gmail.com")
        .returning(Failure(SQLError))
      (mockFreeEmailDomainListDAO.checkIfDomainIsFree)
        .expects("gmail.com")
        .returning(Success(true))
      (mockFreeEmailDomainListDAO.checkIfWhiteListedForSignUp)
      .expects("<EMAIL>" )
        .returning(Success(false))
      val result = freeEmailDomainListService.checkIfFreeEmailService("<EMAIL>")

      assert(result == Success(true))
    }


  }

  describe("FreeEmailDomainListService.isWhitelistedForSignup") {
    it ("should SQL Error") {

      (mockFreeEmailDomainListDAO.checkIfWhiteListedForSignUp)
        .expects("<EMAIL>")
        .returning(Failure(SQLError))
      val result = freeEmailDomainListService.isWhitelistedForSignupAndSendingEmail("<EMAIL>")

      assert(result == Failure(SQLError))
    }

    it("should not found") {

      (mockFreeEmailDomainListDAO.checkIfWhiteListedForSignUp)
        .expects("<EMAIL>")
        .returning(Success(false))
      val result = freeEmailDomainListService.isWhitelistedForSignupAndSendingEmail("<EMAIL>")

      assert(result == Success(false))
    }


    it("should found") {

      (mockFreeEmailDomainListDAO.checkIfWhiteListedForSignUp)
        .expects("<EMAIL>")
        .returning(Success(true))
      val result = freeEmailDomainListService.isWhitelistedForSignupAndSendingEmail("<EMAIL>")

      assert(result == Success(true))
    }

  }

  describe("FreeEmailDomainListService.addToFreeDomain") {

    it("should fail because domain not valid") {

      val result = freeEmailDomainListService.addToFreeDomain("gmailcom")

      assert(result == Left(AddingDomainToFreeListError.InvalidDomainError))
    }

    it("should fail because already in the list") {
      (mockCacheServiceJedis.ttl)
        .expects("free_email_service_domains")
        .returning(Right(10))
      (mockCacheServiceJedis.sismember)
        .expects("free_email_service_domains", "gmail.com")
        .returning(Success(true))

      val result = freeEmailDomainListService.addToFreeDomain("gmail.com")

      assert(result == Left(AddingDomainToFreeListError.AlreadyInTheList))
    }

    it("should fail because FreeEmailDomainListDAO.checkIfDomainIsFree") {
      (mockCacheServiceJedis.ttl)
        .expects("free_email_service_domains")
        .returning(Right(10))
      (mockCacheServiceJedis.sismember)
        .expects("free_email_service_domains", "gmail.com")
        .returning(Failure(SQLError))

      (mockFreeEmailDomainListDAO.checkIfDomainIsFree)
        .expects("gmail.com")
        .returning(Failure(SQLError))
      val result = freeEmailDomainListService.addToFreeDomain("gmail.com")

      assert(result == Left(AddingDomainToFreeListError.SQLErrorWhileFindingInTheList(SQLError)))
    }

    it("should fail because failed to add to the list") {
      (mockCacheServiceJedis.ttl)
        .expects("free_email_service_domains")
        .returning(Right(10))
      (mockCacheServiceJedis.sismember)
        .expects("free_email_service_domains", "gmail.com")
        .returning(Success(false))

      (mockFreeEmailDomainListDAO.addDomainToFree)
        .expects("gmail.com")
        .returning(Failure(SQLError))

      val result = freeEmailDomainListService.addToFreeDomain("gmail.com")

      assert(result == Left(AddingDomainToFreeListError.SQLErrorWhileAddingToFreeList(SQLError)))
    }

    it("should add but not found fail") {
      (mockCacheServiceJedis.ttl)
        .expects("free_email_service_domains")
        .returning(Right(10))
      (mockCacheServiceJedis.sismember)
        .expects("free_email_service_domains", "gmail.com")
        .returning(Success(false))

      (mockFreeEmailDomainListDAO.addDomainToFree)
        .expects("gmail.com")
        .returning(Success(None))

      val result = freeEmailDomainListService.addToFreeDomain("gmail.com")

      assert(result == Left(AddingDomainToFreeListError.CouldNotAddToTheList))
    }


    it("should add") {
      (mockCacheServiceJedis.ttl)
        .expects("free_email_service_domains")
        .returning(Right(10))
      (mockCacheServiceJedis.sismember)
        .expects("free_email_service_domains", "gmail.com")
        .returning(Success(false))

      (mockFreeEmailDomainListDAO.addDomainToFree)
        .expects("gmail.com")
        .returning(Success(Some(2)))

      val result = freeEmailDomainListService.addToFreeDomain("gmail.com")

      assert(result == Right(2))
    }
  }


  describe("FreeEmailDomainListService.addToWhiteList") {
    it("failed because domain not valid") {

      val result = freeEmailDomainListService.addToWhiteList("animesh@gmailcom")

      assert(result == Left(AddingDomainToWhitelistError.InvalidEmailError))
    }

    it("failed because FreeEmailDomainListDAO.checkIfWhiteListedForSignUp failed") {

      (mockFreeEmailDomainListDAO.checkIfWhiteListedForSignUp)
        .expects("<EMAIL>")
        .returning(Failure(SQLError))
      val result = freeEmailDomainListService.addToWhiteList("<EMAIL>")

      assert(result == Left(AddingDomainToWhitelistError.SQLErrorWhileFindingInTheList(SQLError)))
    }

    it("failed because already in list") {

      (mockFreeEmailDomainListDAO.checkIfWhiteListedForSignUp)
        .expects("<EMAIL>")
        .returning(Success(true))
      val result = freeEmailDomainListService.addToWhiteList("<EMAIL>")

      assert(result == Left(AddingDomainToWhitelistError.AlreadyInTheList))
    }

    it("failed while adding to the list") {

      (mockFreeEmailDomainListDAO.checkIfWhiteListedForSignUp)
        .expects("<EMAIL>")
        .returning(Success(false))

      (mockFreeEmailDomainListDAO.addEmailToWhileList)
        .expects("<EMAIL>")
        .returning(Failure(SQLError))
      val result = freeEmailDomainListService.addToWhiteList("<EMAIL>")

      assert(result == Left(AddingDomainToWhitelistError.SQLErrorWhileAddingToWhiteList(SQLError)))
    }

    it("added to list but not found") {

      (mockFreeEmailDomainListDAO.checkIfWhiteListedForSignUp)
        .expects("<EMAIL>")
        .returning(Success(false))

      (mockFreeEmailDomainListDAO.addEmailToWhileList)
        .expects("<EMAIL>")
        .returning(Success(None))
      val result = freeEmailDomainListService.addToWhiteList("<EMAIL>")

      assert(result == Left(AddingDomainToWhitelistError.CouldNotAddToTheList))
    }

    it("added") {

      (mockFreeEmailDomainListDAO.checkIfWhiteListedForSignUp)
        .expects("<EMAIL>")
        .returning(Success(false))
      (() => mockFreeEmailDomainListDAO.getAllInFreeList)
        .expects()
        .returning(Success(Set("gmail.com")))
      (mockCacheServiceJedis.deleteKey)
        .expects("free_email_service_domains")
        .returning(Success(10))
      (mockFreeEmailDomainListDAO.addEmailToWhileList)
        .expects("<EMAIL>")
        .returning(Success(Some(1)))

      val result = freeEmailDomainListService.addToWhiteList("<EMAIL>")

      assert(result == Right(1))
    }
  }


}
