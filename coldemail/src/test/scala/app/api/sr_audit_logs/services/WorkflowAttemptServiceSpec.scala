package app.api.sr_audit_logs.services

import org.apache.pekko.actor.ActorSystem
import org.apache.pekko.stream.ActorMaterializer
import api.accounts.TeamId
import api.accounts.models.AccountId
import api.AppConfig
import api.sr_audit_logs.dao.{AttemptDAOTrait, WorkFlowCrmSettingWithTeamId, WorkflowAttemptDAO}
import api.sr_audit_logs.models.{AttemptData, AttemptLog, AttemptLogId, AttemptLogsFoundForTryOrRetry, AttemptStatus, AttemptTriesLog, AttemptTriesLogId, AttemptTryErrorReason, AttemptType, EventType, LogTracedId, WorkflowAttemptTryErrorReason, WorkflowSettingData}
import api.sr_audit_logs.services.WorkFlowAttemptService
import api.triggers.{IntegrationModuleType, IntegrationType}
import org.joda.time.DateTime
import org.scalamock.matchers.ArgCapture.CaptureOne
import org.scalamock.scalatest.AsyncMockFactory
import org.scalatest.funspec.AsyncFunSpec
import play.api.libs.ws.ahc.AhcWSClient
import scalikejdbc.DBSession
import utils.SRLogger
import utils.dbutils.{DBUtils, DbAndSession}
import utils.testapp.TestAppExecutionContext
import utils.uuid.SrUuidUtils

import scala.concurrent.ExecutionContext
import scala.util.{Failure, Success}


class WorkflowAttemptServiceSpec
  extends AsyncFunSpec
    with AsyncMockFactory {


  implicit lazy val system: ActorSystem = TestAppExecutionContext.actorSystem
  implicit lazy val wSClient: AhcWSClient = TestAppExecutionContext.wsClient
  implicit lazy val actorContext: ExecutionContext = system.dispatcher

  given Logger: SRLogger = new SRLogger("AttemptServiceSpec Unit test")

  val attemptDAO = mock[WorkflowAttemptDAO]
  val dbUtils = mock[DBUtils]
  val srUuidUtils = mock[SrUuidUtils]
  val attemptService = new WorkFlowAttemptService(
    attemptDAO = attemptDAO,
    dbUtils = dbUtils,
    srUuidUtils = srUuidUtils
  )

  describe("createAttempt") {


    val event_id = "evt_1642675705646_42_24_4WNxCJjZqU"
    val attempt_log_id = AttemptLogId("atmp_1642675705646_42_24_4WNxCJjZqU")
    val attempt_tries_log_id = "atmp_tr_1642675705646_42_24_4WNxCJjZqU"
    val account_id = AccountId(2)
    val team_id = if (AppConfig.isProd) {
      TeamId(42)
    } else {
      TeamId(6)
    }
    val attemptData = WorkflowSettingData(
      workflow_crm_setting_id = 5L,
      crm_type = IntegrationType.HUBSPOT,
      module_type = IntegrationModuleType.CONTACTS,
    )

    it("should throw sql exception while createAttempt") {
      (srUuidUtils.generateAttemptLogId)
        .expects(team_id, account_id)
        .returning(attempt_log_id)
      val attemptLog = CaptureOne[AttemptLog[WorkflowSettingData]]()
      (attemptDAO.insertAttemptLogInDB
      (_: AttemptLog[WorkflowSettingData]
      ))
        .expects(
          capture(attemptLog)
        )
        .returning(Failure(new Exception("Sql Exception")))

      val res = attemptService.createAttemptLog(
        teamId  = team_id,
        accountId = account_id,
        event_log_id = event_id,
        attempt_data = attemptData,
        created_at = DateTime.now()
      )
      assert(res.isLeft)
    }

    it("should return None while attemptLog") {
      (srUuidUtils.generateAttemptLogId)
        .expects(team_id, account_id)
        .returning(attempt_log_id)
      val attemptLog = CaptureOne[AttemptLog[WorkflowSettingData]]()
      (attemptDAO.insertAttemptLogInDB
      (_: AttemptLog[WorkflowSettingData]
      ))
        .expects(
          capture(attemptLog)
        )
        .returning(Success(None))

      val res = attemptService.createAttemptLog(
        teamId  = team_id,
        accountId = account_id,
        event_log_id = event_id,
        attempt_data = attemptData,
        created_at = DateTime.now()

      )

      assert(res.isLeft)
    }

    it("should return success while createAttempt") {

      val attemptLog = CaptureOne[AttemptLog[WorkflowSettingData]]()
      (attemptDAO.insertAttemptLogInDB
      (_: AttemptLog[WorkflowSettingData]
      ))
        .expects(
          capture(attemptLog)
        )
        .returning(Success(Some(attempt_log_id)))
      (srUuidUtils.generateAttemptLogId)
        .expects(team_id, account_id)
        .returning(attempt_log_id)
      val res = attemptService.createAttemptLog(
        teamId  = team_id,
        accountId = account_id,
        event_log_id = event_id,
        attempt_data = attemptData,
        created_at = DateTime.now()
      )

      assert(res.toOption.get.nonEmpty)
    }



  }


  describe("getAttemptsLogs") {

    val event_log_id = Some("evt_1642675705646_42_24_4WNxCJjZqU")
    val attempt_log_id = Some(AttemptLogId("atmpt_1642675705646_42_24_4WNxCJjZqU"))
    val team_id: TeamId = TeamId(2)
    val account_id = AccountId(2)


    it("should throw sql exception while getAttemptsLogs") {

      (attemptDAO.findAttemptLogs
      (_: TeamId, _: AccountId, _: Option[AttemptLogId], _: Option[String], _: Option[Long],  _: Option[Long], _: Option[AttemptType], _: Int))
        .expects(
          team_id,
          account_id,
          attempt_log_id,
          event_log_id,
          None,
          None,
          None,
          0
        )
        .returning(Failure(new Exception("Sql Exception")))

      val res = attemptService.getAttemptsLogs(
        teamId = team_id,
        accountId = account_id,
        attempt_log_id = attempt_log_id,
        event_log_id = event_log_id,
        created_greater_than = None,
        created_less_than = None,
        attempt_type = None,
        page = 0
      )

      assert(res.isLeft)

    }


    it("should return seq of AttemptsLogs") {


      val attemptData = WorkflowSettingData(
        workflow_crm_setting_id = 5L,
        crm_type = IntegrationType.HUBSPOT,
        module_type = IntegrationModuleType.CONTACTS,
      )

      val dummyAttemptLog = AttemptLog(
        team_id = team_id,
        account_id = account_id,
        attempt_log_id = attempt_log_id.get,
        event_log_id = event_log_id.get,
        attempt_status = AttemptStatus.SuccessAttempt,
        attempt_setting = attemptData,
        created_at = DateTime.now()
      )
      (attemptDAO.findAttemptLogs
      (_: TeamId, _: AccountId, _: Option[AttemptLogId], _: Option[String], _: Option[Long],  _: Option[Long], _: Option[AttemptType], _: Int))
        .expects(
          team_id,
          account_id,
          attempt_log_id,
          event_log_id,
          None,
          None,
          None,
          0
        )
        .returning(Success(Seq(dummyAttemptLog)))

      val res = attemptService.getAttemptsLogs(
        teamId = team_id,
        accountId = account_id,
        attempt_log_id = attempt_log_id,
        event_log_id = event_log_id,
        created_greater_than = None,
        created_less_than = None,
        attempt_type = None,
        page = 0
      )

      assert(res.isRight)
      assert(res.toOption.get.nonEmpty)

    }
  }




  describe("getAttemptsTriesLogs") {

    val attempt_log_id = AttemptLogId("atmpt_1642675705646_42_24_4WNxCJjZqU")
    val attempt_tries_log_id = AttemptTriesLogId("atmp_tr_1642675705646_42_24_4WNxCJjZqU")
    val team_id: TeamId = TeamId(2)
    val account_id = AccountId(2)
    val isSuccess= Some(true)


    it("should throw sql exception while getAttemptsTriesLogs") {

      (attemptDAO.findAttemptTriesLogs
      (_: TeamId, _: AccountId, _: AttemptLogId,  _: Option[Boolean])(using _: SRLogger))
        .expects(
          team_id,
          account_id,
          attempt_log_id,
          isSuccess,
          *
        )
        .returning(Failure(new Exception("Sql Exception")))

      val res = attemptService.getAttemptsTriesLogs(
        teamId = team_id,
        accountId = account_id,
        attempt_log_id = attempt_log_id,
        isSuccess = isSuccess
      )

      assert(res.isLeft)

    }


    it("should return seq of getAttemptsTriesLogs") {

      val dummyAttemptTriesLog = AttemptTriesLog[WorkflowAttemptTryErrorReason](
        team_id = team_id,
        account_id = account_id,
        attempt_tries_log_id = attempt_tries_log_id,
        attempt_log_id = attempt_log_id,
        is_success = isSuccess.get,
        failure_reason = None,
        created_at = DateTime.now(),
        audit_request_log_id = Some(LogTracedId("test_audit_request_log_id"))
      )

      (attemptDAO.findAttemptTriesLogs
      (_: TeamId, _: AccountId, _: AttemptLogId,  _: Option[Boolean])(using _: SRLogger))
        .expects(
          team_id,
          account_id,
          attempt_log_id,
          isSuccess,
          *
        )
        .returning(Success(Seq(dummyAttemptTriesLog)))

      val res = attemptService.getAttemptsTriesLogs(
        teamId = team_id,
        accountId = account_id,
        attempt_log_id = attempt_log_id,
        isSuccess = isSuccess
      )

      assert(res.isRight)
      assert(res.toOption.get.nonEmpty)

    }
  }

  describe("updateAttemptLogStatus") {

    val attempt_log_id = AttemptLogId("atmpt_1642675705646_42_24_4WNxCJjZqU")
    val team_id: TeamId = TeamId(2)
    val account_id = AccountId(2)
    val attempt_status = AttemptStatus.SuccessAttempt


    it("should return ssuccess right") {

      val attempt_tries_log_id = AttemptTriesLogId("atmp_tr_1642675705646_42_24_4WNxCJjZqU")

      (srUuidUtils.generateAttemptTriesLogId)
      .expects(team_id, account_id)
        .returning(attempt_tries_log_id)
      (() => dbUtils.startLocalTx())
        .expects()
        .returning(DbAndSession(null, null))
      val attemptTriesLog = CaptureOne[AttemptTriesLog[WorkflowAttemptTryErrorReason]]()
      (attemptDAO.insertAttemptTriesLogInDB
      (_: AttemptTriesLog[WorkflowAttemptTryErrorReason]
      )(_: DBSession))
        .expects(
          capture(attemptTriesLog), *
        )
        .returning(Success(Some(attempt_tries_log_id)))

      (attemptDAO.updateAttemptLogStatus
      (_: AttemptLogId, _: TeamId, _: AttemptStatus, _: AttemptTriesLogId, _: Int, _: SRLogger)( _: DBSession))
        .expects(attempt_log_id, team_id, attempt_status, attempt_tries_log_id, 2, Logger, *)
        .returning(Success(Some(attempt_log_id)))
      (dbUtils.commitAndCloseSession)
        .expects(*)
        .returning(())
      val res = attemptService.updateAttemptLogStatus(
        attempt_log_id = attempt_log_id,
        teamId = team_id,
        accountId = account_id,
        Logger = Logger
      )(attempt_status, None, 2)

      assert(res.isRight)

    }
  }

  describe("findAttemptLogsForQueuing") {

    val workflowSettingData = WorkflowSettingData(
      workflow_crm_setting_id = 8,
      crm_type = IntegrationType.HUBSPOT,
      module_type = IntegrationModuleType.CONTACTS
    )
    val numberofAttemptsInTheFirstFetch = 39
    val dummyAttemptData = (1 to numberofAttemptsInTheFirstFetch).map{ index =>

      AttemptData(
        attempt_log_id = AttemptLogId(s"AttemptLogId_$index"),
        event_log_id = s"event_log_id_$index",
        attempt_retry_count = 0
      )
    }.toList

    val attemptLog = AttemptLogsFoundForTryOrRetry(
      team_id = TeamId(2),
      account_id = AccountId(6),
      attempt_setting = workflowSettingData,
      attempt_data = dummyAttemptData,
      eventType = EventType.CREATED_PROSPECT_IN_SMARTREACH
    )

    it("should get a batch") {

      val amountToFetchForTheBatch = CaptureOne[Int]()

//      (attemptDAO.findAttemptLogsForQueuingBasedOnSyncDirection)
//        .expects(*, *,*)
//        .returning(Success(Seq(attemptLog)))
//      (attemptDAO.findAttemptLogsForQueuingBasedOnSyncDirection)
//        .expects(*, *,*)
//        .returning(Success(Seq()))

      (attemptDAO.findWorkflowCrmSettingIdsByLimit )
        .expects(*)
        .returning(Success(List(WorkFlowCrmSettingWithTeamId(
          workflow_crm_setting_id = 8L,
          teamId = TeamId(2)
        ))))


      (attemptDAO.findSingleAttemptLogBasedOnSyncDirectionAndWorkflowCRMSetting(
        _: Set[String],
        _: Long,
        _: TeamId,
        _: SRLogger,
        _: Boolean
      ))
        .expects(*,8L,*,*,*)
        .returning(Success(Some(attemptLog)))

//      (attemptDAO.findSingleAttemptLogBasedOnSyncDirectionAndWorkflowCRMSetting _)
//        .expects(*,*, *,*)
//        .returning(Success(Some(attemptLog)))
//      (attemptDAO.findSingleAttemptLogBasedOnSyncDirectionAndWorkflowCRMSetting _)
//        .expects(*,*, *,*)
//        .returning(Success(None))

      (attemptDAO.findWorkflowCrmSettingIdsByLimit)
        .expects(*)
        .returning(Success(List(WorkFlowCrmSettingWithTeamId(
          workflow_crm_setting_id = 8L,
          teamId = TeamId(2)
        ))))
      (attemptDAO.findSingleAttemptLogBasedOnSyncDirectionAndWorkflowCRMSetting(
        _: Set[String],
        _: Long,
        _: TeamId,
        _: SRLogger,
        _: Boolean
      ))
        .expects(*,8L,*,*,*)
        .returning(Success(None))


      (attemptDAO.updateLastProcessedAt)
        .expects(*,*)
        .returning(Success(Some(workflowSettingData.workflow_crm_setting_id)))


      (attemptDAO.getBatchAttemptLogsForQueuing)
        .expects(8L, TeamId(2), capture(amountToFetchForTheBatch), EventType.CREATED_PROSPECT_IN_SMARTREACH, *)
        .returning(Success(Seq()))

      val result = attemptService.findAttemptLogsForQueuing( logger = Logger)
      println(s"amountToFetchForTheBatch : ${amountToFetchForTheBatch.value}")
      assert(amountToFetchForTheBatch.value == 100 - numberofAttemptsInTheFirstFetch)
    }
  }
}