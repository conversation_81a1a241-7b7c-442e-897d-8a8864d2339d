package app.api.sr_audit_logs.services

import org.apache.pekko.actor.ActorSystem
import api.accounts.TeamId
import api.accounts.models.AccountId
import api.prospects.models.ProspectId
import api.sr_audit_logs.dao.EventAndAttemptDataForProcessing
import api.sr_audit_logs.models.EventDataType.ActivityEventDataType
import api.sr_audit_logs.models.{AttemptData, AttemptLogId, AttemptLogsFoundForTryOrRetry, AttemptStatus, AttemptTriesLogId, EventDataType, EventType, OldProspectDeduplicationColumn, UpdateAttemptLogStatusError, WorkflowAttemptInternalServerError, WorkflowAttemptTryErrorReason, WorkflowSettingData}
import api.sr_audit_logs.services.{ProcessAttemptService, WorkFlowAttemptService, WorkflowJedisService}
import api.triggers.{CRMIntegrationInDB, IntegrationModuleType, IntegrationType, Trigger}
import org.scalamock.scalatest.AsyncMockFactory
import org.scalatest.funspec.AsyncFunSpec
import play.api.libs.ws.ahc.AhcWSClient
import play.api.libs.ws.WSClient
import utils.SRLogger
import utils.mq.webhook.{HandleActivityTriggerEventService, HandleAddToDNCTriggerEventService, HandlePushTriggerEventService, HandleSyncTriggerEventService, LeadStatusOrderingError}
import utils.testapp.TestAppExecutionContext

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}
class ProcessAttemptServiceSpec extends AsyncFunSpec with AsyncMockFactory {
  implicit lazy val system: ActorSystem = TestAppExecutionContext.actorSystem
  implicit lazy val wSClient: AhcWSClient = TestAppExecutionContext.wsClient
  implicit lazy val actorContext: ExecutionContext = system.dispatcher
  val workFlowAttemptService = mock[WorkFlowAttemptService]
  val handleActivityTriggerEventService =  mock[HandleActivityTriggerEventService]
  val handlePushTriggerEventService = mock[HandlePushTriggerEventService]
  val triggerDAO = mock[Trigger]
  val handleSyncTriggerEventService = mock[HandleSyncTriggerEventService]
  val handleAddToDNCTriggerEventService = mock[HandleAddToDNCTriggerEventService]
  val workflowJedisService = mock[WorkflowJedisService]

  val processAttemptService = new ProcessAttemptService(
    workFlowAttemptService = workFlowAttemptService,
    handleActivityTriggerEventService = handleActivityTriggerEventService,
    handlePushTriggerEventService = handlePushTriggerEventService,
    triggerDAO = triggerDAO,
    handleSyncTriggerEventService = handleSyncTriggerEventService,
    handleAddToDNCTriggerEventService = handleAddToDNCTriggerEventService,
    workflowJedisService = workflowJedisService
  )
  val teamId = TeamId(4)
  val accountId = AccountId(6)
  given Logger: SRLogger = new SRLogger("ProcessAttemptServiceSpec")
  val workflow_crm_setting_id = 8L
  val attempt_log_id = AttemptLogId("some_attempt_log_id")
  val attempt_tries_log_id = AttemptTriesLogId("some_attempt_tries_log_id")

  describe("ProcessAttemptService.getNewAttemptStatusForProcessedAttempt") {


    it("AttemptStatus.YetToAttempt should send Error") {
      val result = ProcessAttemptService.getNewAttemptStatusForProcessedAttempt(
        attempt_status = AttemptStatus.YetToAttempt,
        attempt_retry_count = 0,
        workflowAttemptTryErrorReason = WorkflowAttemptTryErrorReason.UnKnownError(""))

      assert(result.isFailure)
      assert(result.failed.get.getMessage == "Wrong Attempt Status")
    }

    it("AttemptStatus.SuccessAttempt should send Error") {
      val result = ProcessAttemptService.getNewAttemptStatusForProcessedAttempt(
        attempt_status = AttemptStatus.SuccessAttempt,
        attempt_retry_count = 0,
        workflowAttemptTryErrorReason = WorkflowAttemptTryErrorReason.UnKnownError(""))

      assert(result.isFailure)
      assert(result.failed.get.getMessage == "Wrong Attempt Status")
    }

    it("AttemptStatus.StartedAttempt should send Error") {
      val result = ProcessAttemptService.getNewAttemptStatusForProcessedAttempt(
        attempt_status = AttemptStatus.StartedAttempt,
        attempt_retry_count = 0,
        workflowAttemptTryErrorReason = WorkflowAttemptTryErrorReason.UnKnownError(""))

      assert(result.isFailure)
      assert(result.failed.get.getMessage == "Wrong Attempt Status")
    }

    it("AttemptStatus.IgnoredAttempt should send Error") {
      val result = ProcessAttemptService.getNewAttemptStatusForProcessedAttempt(
        attempt_status = AttemptStatus.IgnoredAttempt,
        attempt_retry_count = 0,
        workflowAttemptTryErrorReason = WorkflowAttemptTryErrorReason.UnKnownError(""))

      assert(result.get == AttemptStatus.IgnoredAttempt)
    }

    describe ("AttemptStatus.FailedAttempt") {

      describe("should send IgnoreAttempt") {
        it("when given TokensNotFoundInDBError") {
          val result = ProcessAttemptService.getNewAttemptStatusForProcessedAttempt(
            attempt_status = AttemptStatus.FailedAttempt,
            attempt_retry_count = 0,
            workflowAttemptTryErrorReason = WorkflowAttemptTryErrorReason.SRInternalServerError(
              WorkflowAttemptInternalServerError.TokensNotFoundInDBError("")
            )
          )

          assert(result.get == AttemptStatus.IgnoredAttempt)

        }

        it("when given InvalidModuleError") {
          val result = ProcessAttemptService.getNewAttemptStatusForProcessedAttempt(
            attempt_status = AttemptStatus.FailedAttempt,
            attempt_retry_count = 0,
            workflowAttemptTryErrorReason = WorkflowAttemptTryErrorReason.SRInternalServerError(
              WorkflowAttemptInternalServerError.InvalidModuleError("")
            )
          )

          assert(result.get == AttemptStatus.IgnoredAttempt)

        }


        it("when given InvalidEventError") {
          val result = ProcessAttemptService.getNewAttemptStatusForProcessedAttempt(
            attempt_status = AttemptStatus.FailedAttempt,
            attempt_retry_count = 0,
            workflowAttemptTryErrorReason = WorkflowAttemptTryErrorReason.SRInternalServerError(
              WorkflowAttemptInternalServerError.InvalidEventError("")
            )
          )

          assert(result.get == AttemptStatus.IgnoredAttempt)

        }

        it("when given IntegrationNotFound") {
          val result = ProcessAttemptService.getNewAttemptStatusForProcessedAttempt(
            attempt_status = AttemptStatus.FailedAttempt,
            attempt_retry_count = 0,
            workflowAttemptTryErrorReason = WorkflowAttemptTryErrorReason.SRInternalServerError(
              WorkflowAttemptInternalServerError.IntegrationNotFound("")
            )
          )

          assert(result.get == AttemptStatus.IgnoredAttempt)

        }

        it("when given TriggerNotFound") {
          val result = ProcessAttemptService.getNewAttemptStatusForProcessedAttempt(
            attempt_status = AttemptStatus.FailedAttempt,
            attempt_retry_count = 0,
            workflowAttemptTryErrorReason = WorkflowAttemptTryErrorReason.SRInternalServerError(
              WorkflowAttemptInternalServerError.TriggerNotFound("")
            )
          )

          assert(result.get == AttemptStatus.IgnoredAttempt)

        }

        it("when given FieldMappingNotFound") {
          val result = ProcessAttemptService.getNewAttemptStatusForProcessedAttempt(
            attempt_status = AttemptStatus.FailedAttempt,
            attempt_retry_count = 0,
            workflowAttemptTryErrorReason = WorkflowAttemptTryErrorReason.SRInternalServerError(
              WorkflowAttemptInternalServerError.FieldMappingNotFound("")
            )
          )

          assert(result.get == AttemptStatus.IgnoredAttempt)

        }

        it("when given ProspectIdEmpty") {
          val result = ProcessAttemptService.getNewAttemptStatusForProcessedAttempt(
            attempt_status = AttemptStatus.FailedAttempt,
            attempt_retry_count = 0,
            workflowAttemptTryErrorReason = WorkflowAttemptTryErrorReason.SRInternalServerError(
              WorkflowAttemptInternalServerError.ProspectIdEmpty("")
            )
          )

          assert(result.get == AttemptStatus.IgnoredAttempt)

        }
        it("when given CampaignIdEmpty") {
          val result = ProcessAttemptService.getNewAttemptStatusForProcessedAttempt(
            attempt_status = AttemptStatus.FailedAttempt,
            attempt_retry_count = 0,
            workflowAttemptTryErrorReason = WorkflowAttemptTryErrorReason.SRInternalServerError(
              WorkflowAttemptInternalServerError.CampaignIdEmpty("")
            )
          )

          assert(result.get == AttemptStatus.IgnoredAttempt)

        }
        it("when given UpdateSentNone") {
          val result = ProcessAttemptService.getNewAttemptStatusForProcessedAttempt(
            attempt_status = AttemptStatus.FailedAttempt,
            attempt_retry_count = 0,
            workflowAttemptTryErrorReason = WorkflowAttemptTryErrorReason.SRInternalServerError(
              WorkflowAttemptInternalServerError.UpdateSentNone("")
            )
          )

          assert(result.get == AttemptStatus.IgnoredAttempt)

        }

        it("when given ErrorLeadStatusOrdering") {
          val result = ProcessAttemptService.getNewAttemptStatusForProcessedAttempt(
            attempt_status = AttemptStatus.FailedAttempt,
            attempt_retry_count = 0,
            workflowAttemptTryErrorReason = WorkflowAttemptTryErrorReason.SRInternalServerError(
              WorkflowAttemptInternalServerError.ErrorLeadStatusOrdering(LeadStatusOrderingError.CurrentStatusIsAheadOfNewStatus)
            )
          )

          assert(result.get == AttemptStatus.IgnoredAttempt)

        }
      }

      describe("should send YetToAttempt") {
        it("when given SqlException") {
          val result = ProcessAttemptService.getNewAttemptStatusForProcessedAttempt(
            attempt_status = AttemptStatus.FailedAttempt,
            attempt_retry_count = 0,
            workflowAttemptTryErrorReason = WorkflowAttemptTryErrorReason.SRInternalServerError(
              WorkflowAttemptInternalServerError.SqlException(new Throwable("ERROR"))
            )
          )

          assert(result.get == AttemptStatus.YetToAttempt)

        }
        it("when given FutureException") {
          val result = ProcessAttemptService.getNewAttemptStatusForProcessedAttempt(
            attempt_status = AttemptStatus.FailedAttempt,
            attempt_retry_count = 0,
            workflowAttemptTryErrorReason = WorkflowAttemptTryErrorReason.SRInternalServerError(
              WorkflowAttemptInternalServerError.FutureException(new Throwable("ERROR"))
            )
          )

          assert(result.get == AttemptStatus.YetToAttempt)

        }

        it("when given HubspotFailureBatchError") {
          val result = ProcessAttemptService.getNewAttemptStatusForProcessedAttempt(
            attempt_status = AttemptStatus.FailedAttempt,
            attempt_retry_count = 0,
            workflowAttemptTryErrorReason = WorkflowAttemptTryErrorReason.SRInternalServerError(
              WorkflowAttemptInternalServerError.HubspotFailureBatchError("", Seq())
            )
          )

          assert(result.get == AttemptStatus.YetToAttempt)

        }

        it("when given UnableToLockRow") {
          val result = ProcessAttemptService.getNewAttemptStatusForProcessedAttempt(
            attempt_status = AttemptStatus.FailedAttempt,
            attempt_retry_count = 0,
            workflowAttemptTryErrorReason = WorkflowAttemptTryErrorReason.SRInternalServerError(
              WorkflowAttemptInternalServerError.UnableToLockRow("")
            )
          )

          assert(result.get == AttemptStatus.YetToAttempt)

        }


        it("when given Others") {
          val result = ProcessAttemptService.getNewAttemptStatusForProcessedAttempt(
            attempt_status = AttemptStatus.FailedAttempt,
            attempt_retry_count = 0,
            workflowAttemptTryErrorReason = WorkflowAttemptTryErrorReason.SRInternalServerError(
              WorkflowAttemptInternalServerError.Others("")
            )
          )

          assert(result.get == AttemptStatus.YetToAttempt)

        }

        it("when given OAuthError") {
          val result = ProcessAttemptService.getNewAttemptStatusForProcessedAttempt(
            attempt_status = AttemptStatus.FailedAttempt,
            attempt_retry_count = 0,
            workflowAttemptTryErrorReason = WorkflowAttemptTryErrorReason.OAuthError("")
          )

          assert(result.get == AttemptStatus.YetToAttempt)

        }

        it("when given CRMInternalServerError") {
          val result = ProcessAttemptService.getNewAttemptStatusForProcessedAttempt(
            attempt_status = AttemptStatus.FailedAttempt,
            attempt_retry_count = 0,
            workflowAttemptTryErrorReason = WorkflowAttemptTryErrorReason.CRMInternalServerError("")
          )

          assert(result.get == AttemptStatus.YetToAttempt)

        }


        it("when given TooManyRequestsError") {
          val result = ProcessAttemptService.getNewAttemptStatusForProcessedAttempt(
            attempt_status = AttemptStatus.FailedAttempt,
            attempt_retry_count = 0,
            workflowAttemptTryErrorReason = WorkflowAttemptTryErrorReason.TooManyRequestsError("")
          )

          assert(result.get == AttemptStatus.YetToAttempt)

        }


        it("when given UnAuthorizedError") {
          val result = ProcessAttemptService.getNewAttemptStatusForProcessedAttempt(
            attempt_status = AttemptStatus.FailedAttempt,
            attempt_retry_count = 0,
            workflowAttemptTryErrorReason = WorkflowAttemptTryErrorReason.UnAuthorizedError("")
          )

          assert(result.get == AttemptStatus.YetToAttempt)

        }


        it("when given UnKnownError") {
          val result = ProcessAttemptService.getNewAttemptStatusForProcessedAttempt(
            attempt_status = AttemptStatus.FailedAttempt,
            attempt_retry_count = 0,
            workflowAttemptTryErrorReason = WorkflowAttemptTryErrorReason.UnKnownError("")
          )

          assert(result.get == AttemptStatus.YetToAttempt)

        }

        it("when given MalformedResponseStructureError") {
          val result = ProcessAttemptService.getNewAttemptStatusForProcessedAttempt(
            attempt_status = AttemptStatus.FailedAttempt,
            attempt_retry_count = 0,
            workflowAttemptTryErrorReason = WorkflowAttemptTryErrorReason.MalformedResponseStructureError("")
          )

          assert(result.get == AttemptStatus.YetToAttempt)

        }


        it("when given UnableToFindPersonIdError") {
          val result = ProcessAttemptService.getNewAttemptStatusForProcessedAttempt(
            attempt_status = AttemptStatus.FailedAttempt,
            attempt_retry_count = 0,
            workflowAttemptTryErrorReason = WorkflowAttemptTryErrorReason.UnableToFindPersonIdError("")
          )

          assert(result.get == AttemptStatus.YetToAttempt)

        }

        it("when given InvalidRefreshTokenError") {
          val result = ProcessAttemptService.getNewAttemptStatusForProcessedAttempt(
            attempt_status = AttemptStatus.FailedAttempt,
            attempt_retry_count = 0,
            workflowAttemptTryErrorReason = WorkflowAttemptTryErrorReason.InvalidRefreshTokenError("")
          )

          assert(result.get == AttemptStatus.YetToAttempt)

        }
      }


    }

  }

  describe("ProcessAttemptService.getYetToAttemptOrFailedAttempt") {
    it("should give AttemptStatus.FailedAttempt when we give attempt_retry_count > 4") {
      assert(ProcessAttemptService.getYetToAttemptOrFailedAttempt(5) == AttemptStatus.FailedAttempt)
    }

    it("should give YetToAttempt when we give attempt_retry_count <= 4") {
      assert(ProcessAttemptService.getYetToAttemptOrFailedAttempt(3) == AttemptStatus.YetToAttempt)
    }

  }


  describe("ProcessAttemptService.sideEffectsOfWorkflowAttempt") {
    describe("No WorkflowAttemptTryErrorReason sent for post processing") {
      it("workFlowAttemptService.updateAttemptLogStatus failed (silent failure, just a log)") {

        (workFlowAttemptService.updateAttemptLogStatus(
          _: AttemptLogId,
          _: TeamId,
          _: AccountId,
          _: SRLogger
        )(_: AttemptStatus,
          _: Option[WorkflowAttemptTryErrorReason],
          _: Int
        )).expects(attempt_log_id, teamId, accountId, Logger, AttemptStatus.SuccessAttempt, None, 3)
          .returning(Left(UpdateAttemptLogStatusError.SQLException(new Throwable("SQLERROR"))))
        (triggerDAO.updateErrorCount)
          .expects(teamId, workflow_crm_setting_id, 0)
          .returning(Success(None))
        val result = processAttemptService.sideEffectsOfWorkflowAttempt(
          workflowAttemptTryErrorReason = None,
          attempt_log_id = attempt_log_id,
          teamId = teamId,
          accountId = accountId,
          Logger = Logger,
          workflow_crm_setting_id = workflow_crm_setting_id,
          attempt_retry_count = 3
        )

        assert(result == Success(attempt_log_id))

      }

      it("workFlowAttemptService.updateAttemptLogStatus Success (Silent success)") {

        (workFlowAttemptService.updateAttemptLogStatus(
          _: AttemptLogId,
          _: TeamId,
          _: AccountId,
          _: SRLogger
        )(_: AttemptStatus,
          _: Option[WorkflowAttemptTryErrorReason],
          _: Int
        )).expects(attempt_log_id, teamId, accountId, Logger, AttemptStatus.SuccessAttempt, None, 3)
          .returning(Right(attempt_tries_log_id))
        (triggerDAO.updateErrorCount)
          .expects(teamId, workflow_crm_setting_id, 0)
          .returning(Success(None))
        val result = processAttemptService.sideEffectsOfWorkflowAttempt(
          workflowAttemptTryErrorReason = None,
          attempt_log_id = attempt_log_id,
          teamId = teamId,
          accountId = accountId,
          Logger = Logger,
          workflow_crm_setting_id = workflow_crm_setting_id,
          attempt_retry_count = 3
        )

        assert(result == Success(attempt_log_id))

      }
    }

    describe("WorkflowAttemptTryErrorReason sent for post processing") {

      describe("getting TooManyRequestsError") {
        it("Error in triggerDAO.setNextAttemptAt fails the process") {
          (workflowJedisService.acquireLockAndAddToSetForRatelimit(_: Set[String], _: Int)(using _: SRLogger))
            .expects(Set(workflow_crm_setting_id.toString), *, *)
            .returning(Failure(new Throwable("JEDIS ERROR")))
          (triggerDAO.setNextAttemptAt)
            .expects(*, workflow_crm_setting_id, teamId)
            .returning(Failure(new Throwable("SQL ERROR")))
          (triggerDAO.updateErrorCount)
            .expects(teamId, workflow_crm_setting_id, 0)
            .returning(Success(None))
          val result = processAttemptService.sideEffectsOfWorkflowAttempt(
            workflowAttemptTryErrorReason = Some(WorkflowAttemptTryErrorReason.TooManyRequestsError("Test Error")),
            attempt_log_id = attempt_log_id,
            teamId = teamId,
            accountId = accountId,
            Logger = Logger,
            workflow_crm_setting_id = workflow_crm_setting_id,
            attempt_retry_count = 3
          )

          assert(result.isFailure)

        }

        it("Success in triggerDAO.setNextAttemptAt (Success path)") {
          (workflowJedisService.acquireLockAndAddToSetForRatelimit(_: Set[String], _: Int)(using _: SRLogger))
            .expects(Set(workflow_crm_setting_id.toString), *, *)
            .returning(Success(Set()))
          (triggerDAO.setNextAttemptAt)
            .expects(*, workflow_crm_setting_id, teamId)
            .returning(Success(10))
          (triggerDAO.updateErrorCount)
            .expects(teamId, workflow_crm_setting_id, 0)
            .returning(Success(None))
          val result = processAttemptService.sideEffectsOfWorkflowAttempt(
            workflowAttemptTryErrorReason = Some(WorkflowAttemptTryErrorReason.TooManyRequestsError("Test Error")),
            attempt_log_id = attempt_log_id,
            teamId = teamId,
            accountId = accountId,
            Logger = Logger,
            workflow_crm_setting_id = workflow_crm_setting_id,
            attempt_retry_count = 3
          )

          assert(result == Success(attempt_log_id))

        }

      }

      describe("getting any other error that TooManyRequests") {

        it("workFlowAttemptService.updateAttemptLogStatus failed (Silent failure)") {
          (workFlowAttemptService.updateAttemptLogStatus(
            _: AttemptLogId,
            _: TeamId,
            _: AccountId,
            _: SRLogger
          )(_: AttemptStatus,
            _: Option[WorkflowAttemptTryErrorReason],
            _: Int
          )).expects(attempt_log_id, teamId, accountId, Logger, AttemptStatus.YetToAttempt, *, 3)
            .returning(Left(UpdateAttemptLogStatusError.SQLException(new Throwable("SQLERROR"))))
          (triggerDAO.updateErrorCount)
            .expects(teamId, workflow_crm_setting_id, 0)
            .returning(Success(None))
          val result = processAttemptService.sideEffectsOfWorkflowAttempt(
            workflowAttemptTryErrorReason = Some(WorkflowAttemptTryErrorReason.SRInternalServerError(WorkflowAttemptInternalServerError.Others("Test Error"))),
            attempt_log_id = attempt_log_id,
            teamId = teamId,
            accountId = accountId,
            Logger = Logger,
            workflow_crm_setting_id = workflow_crm_setting_id,
            attempt_retry_count = 3
          )

          assert(result == Success(attempt_log_id))

        }

      }


    }
  }

  describe("ProcessAttemptService.processActivityAttempt") {
    val messageEvent = EventDataType.ActivityEventDataType.EmailSentEventData(
      accountId = accountId,
      teamId = teamId,
      prospect_id = ProspectId(22L),
      email_scheduled_id = 44,
      campaignName = None,
      campaignId = None
    )
    val message = ActivityEventDataType.EmailSentEventData(
      accountId = accountId,
      teamId = teamId,
      prospect_id = ProspectId(22),
      email_scheduled_id = 44L,
      campaignName = None,
      campaignId = None
    )

    val crMIntegrationInDB = CRMIntegrationInDB(
      workflow_crm_setting_id = workflow_crm_setting_id,
      team_id = teamId.id,
      owner_id = accountId.id,
      module_id = 1,
      crm = IntegrationType.HUBSPOT,
      module = IntegrationModuleType.CONTACTS,
      user_mapping = None,
      field_mapping = None,
      activity_to_status_mapping = None,
      category_to_status_mapping = None,
      status_column_in_crm = None,
      create_record_if_not_exists = true,
      track_activities = false,
      create_or_update_record_in_crm = false,
      crm_filters_for_add_to_do_not_contact_in_sr = None,
      allow_going_back_in_crm_status = false,
      sentiment_to_status_mapping = None,
      update_reply_sentiment_for_all_associated_prospects = false,
      error = None, error_at = None, last_alert_for_error_sent_at = None
    )


    it("Should give left when triggerDAO.findIntegrationByTeamIdAndCRMTypeAndModuleType sent failure"){

      (triggerDAO.findIntegrationByTeamIdAndCRMTypeAndModuleType)
        .expects(teamId.id, IntegrationType.HUBSPOT, IntegrationModuleType.CONTACTS, *)
        .returning(Failure(new Throwable("SQL ERROR")))

      processAttemptService.processActivityAttempt(
        message = messageEvent,
        crm_type = IntegrationType.HUBSPOT,
        module_type = IntegrationModuleType.CONTACTS,
        event = EventType.EMAIL_SENT
      ).map { result =>
        assert(result.isLeft)
      }.recover{e =>
        assert(false)
      }
    }

    it("Should give left when triggerDAO.findIntegrationByTeamIdAndCRMTypeAndModuleType sent None") {

      (triggerDAO.findIntegrationByTeamIdAndCRMTypeAndModuleType)
        .expects(teamId.id, IntegrationType.HUBSPOT, IntegrationModuleType.CONTACTS, *)
        .returning(Success(None))
      processAttemptService.processActivityAttempt(
        message = messageEvent,
        crm_type = IntegrationType.HUBSPOT,
        module_type = IntegrationModuleType.CONTACTS,
        event = EventType.EMAIL_SENT
      ).map { result =>
        assert(result.isLeft)
      }.recover { e =>
        assert(false)
      }
    }


    it("should give Right triggerDAO.findIntegrationByTeamIdAndCRMTypeAndModuleType sent Some (Success Path)") {

      (triggerDAO.findIntegrationByTeamIdAndCRMTypeAndModuleType)
        .expects(teamId.id, IntegrationType.HUBSPOT, IntegrationModuleType.CONTACTS, *)
        .returning(Success(Some(crMIntegrationInDB)))

      (handleActivityTriggerEventService.matchEventAndTriggerCRMNewFlow(_: CRMIntegrationInDB, _: ActivityEventDataType, _: EventType)(
      _: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger))
      .expects(
        crMIntegrationInDB,
        message,
        EventType.EMAIL_SENT, *, *, *, *
      ).returning(Future.successful(Right(Seq())))
      processAttemptService.processActivityAttempt(
        message = messageEvent,
        crm_type = IntegrationType.HUBSPOT,
        module_type = IntegrationModuleType.CONTACTS,
        event = EventType.EMAIL_SENT
      ).map { result =>
        assert(result.isRight)
      }.recover { e =>
        assert(false)
      }
    }

  }


  describe("ProcessAttemptService.processSingleAttempt") {
    val eventType = EventType.EMAIL_SENT
    val crm_type = IntegrationType.HUBSPOT
    val module_type = IntegrationModuleType.CONTACTS
    val attempt_setting = WorkflowSettingData(
      workflow_crm_setting_id = workflow_crm_setting_id,
      crm_type = crm_type,
      module_type = module_type
    )
    val attempt_data: List[AttemptData] = List (
      AttemptData(
        attempt_log_id = attempt_log_id,
        event_log_id = "some_event_log_id",
        attempt_retry_count = 3
      )
    )
    val atmpt = AttemptLogsFoundForTryOrRetry(
      team_id = teamId,
      account_id = accountId,
      attempt_setting = attempt_setting,
      attempt_data = attempt_data,
      eventType: EventType
    )
    val mqActivityTriggerMsg = ActivityEventDataType.EmailSentEventData(
      accountId = accountId,
      teamId = teamId,
      prospect_id = ProspectId(55),
      email_scheduled_id = 33L,
      campaignName = None,
      campaignId = None
    )
    val eventDataActivity = EventDataType.ActivityEventDataType.EmailSentEventData(
      email_scheduled_id = 33,
      prospect_id = ProspectId(55),
      teamId = teamId,
      campaignName = None,
      campaignId = None,
      accountId = accountId,
    )
    val eventAndAttemptDataForProcessingActivity: EventAndAttemptDataForProcessing = EventAndAttemptDataForProcessing(
      event_data = eventDataActivity,
      attempt_data = attempt_data.head
    )
    val mqPushTriggerMsgUpdated = EventDataType.PushEventDataType.UpdatedProspectsEventData(
      ownerAccountId = accountId.id,
      teamId = teamId.id,
      updated_id = 55,
      triggerPath = None,
      oldProspectDeduplicationColumn = None
    )

    val mqPushTriggerMsgCreated = EventDataType.PushEventDataType.CreatedProspectsEventData(
      ownerAccountId = accountId.id,
      teamId = teamId.id,
      created_id = 55,
      triggerPath = None
    )

    val eventDataPush = EventDataType.PushEventDataType.CreatedProspectsEventData(
      created_id = 55,
      ownerAccountId = accountId.id,
      teamId = teamId.id,
      triggerPath = None
    )
    val eventAndAttemptDataForProcessingPush: EventAndAttemptDataForProcessing = EventAndAttemptDataForProcessing(
      event_data = eventDataPush,
      attempt_data = attempt_data.head
    )

    val eventDataDNC = EventDataType.AddToDoNotContact(
      teamId: TeamId,
      module_id = 12
    )
    val eventAndAttemptDataForProcessingDNC: EventAndAttemptDataForProcessing = EventAndAttemptDataForProcessing(
      event_data = eventDataDNC,
      attempt_data = attempt_data.head
    )


    val eventDataSync = EventDataType.SyncEventDataType.HubspotProspectSyncEventData(
      triggerId = 14
    )
    val eventAndAttemptDataForProcessingSync: EventAndAttemptDataForProcessing = EventAndAttemptDataForProcessing(
      event_data = eventDataSync,
      attempt_data = attempt_data.head
    )
    val crMIntegrationInDB = CRMIntegrationInDB(
      workflow_crm_setting_id = workflow_crm_setting_id,
      team_id = teamId.id,
      owner_id = accountId.id,
      module_id = 1,
      crm = IntegrationType.HUBSPOT,
      module = IntegrationModuleType.CONTACTS,
      user_mapping = None,
      field_mapping = None,
      activity_to_status_mapping = None,
      category_to_status_mapping = None,
      status_column_in_crm = None,
      create_record_if_not_exists = true,
      track_activities = false,
      create_or_update_record_in_crm = false,
      crm_filters_for_add_to_do_not_contact_in_sr = None,
      allow_going_back_in_crm_status = false,
      sentiment_to_status_mapping = None,
      update_reply_sentiment_for_all_associated_prospects = false,
      error = None, error_at = None, last_alert_for_error_sent_at = None
    )

    it("should give future failed when workflowJedisService.checkLockForRateLimit fails"){

      (workflowJedisService.checkLockForRateLimit(_: String)(using _: SRLogger))
      .expects(atmpt.attempt_setting.workflow_crm_setting_id.toString, *)
        .returning(Failure(new Throwable("SQL ERROR")))
      processAttemptService.processSingleAttempt(
        atmpt = atmpt
      ).map{result =>
        assert(false)
      }.recover{e =>
        assert(e.getMessage == "SQL ERROR")
      }
    }


    it("should send a Left when workflowJedisService.checkLockForRateLimit sent isLocked True") {

      (workflowJedisService.checkLockForRateLimit(_: String)(using _: SRLogger))
        .expects(atmpt.attempt_setting.workflow_crm_setting_id.toString, *)
        .returning(Success(true))
      processAttemptService.processSingleAttempt(
        atmpt = atmpt
      ).map { result =>
        assert(result.isLeft)
      }.recover { e =>
        assert(false)
      }
    }

    describe("when workflowJedisService.checkLockForRateLimit sent isLocked false") {
      it("should send Left when workFlowAttemptService.getSingleMqMsgForWorkflowCRM failed") {

        (workflowJedisService.checkLockForRateLimit(_: String)(using _: SRLogger))
          .expects(atmpt.attempt_setting.workflow_crm_setting_id.toString, *)
          .returning(Success(false))
        (workFlowAttemptService.getSingleMsgSetForProcessing)
          .expects(atmpt, *)
          .returning(Failure(new Throwable("SQL ERROR")))


        (workFlowAttemptService.updateAttemptLogStatus(
          _: AttemptLogId,
          _: TeamId,
          _: AccountId,
          _: SRLogger
        )(_: AttemptStatus,
          _: Option[WorkflowAttemptTryErrorReason],
          _: Int
        )).expects(attempt_log_id, teamId, accountId, *, AttemptStatus.YetToAttempt, *, 3)
          .returning(Right(attempt_tries_log_id))
        (triggerDAO.updateErrorCount)
          .expects(teamId, workflow_crm_setting_id, 0)
          .returning(Success(None))
        processAttemptService.processSingleAttempt(
          atmpt = atmpt
        ).map { result =>
          println(s"result ----------- $result")

          assert(result.isLeft)
        }.recover { e =>
          println(s"ERROR  -----------${e.getMessage}")
          assert(false)
        }
      }

      it("when workFlowAttemptService.getSingleMqMsgForWorkflowCRM sends MQActivityTriggerMsg handleActivityTriggerEventService.matchEventAndTriggerCRMNewFlow(Success path)") {

        (workflowJedisService.checkLockForRateLimit(_: String)(using _: SRLogger))
          .expects(atmpt.attempt_setting.workflow_crm_setting_id.toString, *)
          .returning(Success(false))
        (workFlowAttemptService.getSingleMsgSetForProcessing)
          .expects(atmpt, *)
          .returning(Success(List(eventAndAttemptDataForProcessingActivity)))

        (workflowJedisService.removeFromHashMapForRateLimit (_: Set[String])(using _:SRLogger))
        .expects(Set(workflow_crm_setting_id.toString), *)
          .returning(Success(Set()))


        (triggerDAO.findIntegrationByTeamIdAndCRMTypeAndModuleType)
          .expects(teamId.id, IntegrationType.HUBSPOT, IntegrationModuleType.CONTACTS, *)
          .returning(Success(Some(crMIntegrationInDB)))

        (handleActivityTriggerEventService.matchEventAndTriggerCRMNewFlow(_: CRMIntegrationInDB, _: ActivityEventDataType, _: EventType)(
          _: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger))
          .expects(
            crMIntegrationInDB,
            mqActivityTriggerMsg,
            EventType.EMAIL_SENT, *, *, *, *
          ).returning(Future.successful(Right(Seq())))
        (workFlowAttemptService.updateAttemptLogStatus(
          _: AttemptLogId,
          _: TeamId,
          _: AccountId,
          _: SRLogger
        )(_: AttemptStatus,
          _: Option[WorkflowAttemptTryErrorReason],
          _: Int
        )).expects(attempt_log_id, teamId, accountId, Logger, AttemptStatus.SuccessAttempt, None, 3)
          .returning(Right(attempt_tries_log_id))
        (triggerDAO.updateErrorCount)
          .expects(teamId, workflow_crm_setting_id, 0)
          .returning(Success(None))
        processAttemptService.processSingleAttempt(
          atmpt = atmpt
        ).map { result =>
          println(s"result ----------- $result")

          assert(result.isRight)
        }.recover { e =>
          println(s"ERROR  -----------${e.getMessage}")
          assert(false)
        }
      }


      it("when workFlowAttemptService.getSingleMqMsgForWorkflowCRM sends mqPushTriggerMsg handlePushTriggerEventService.findIntegrationsForInvokeNewFlowV2 (Success path)") {

        (workflowJedisService.checkLockForRateLimit(_: String)(using _: SRLogger))
          .expects(atmpt.attempt_setting.workflow_crm_setting_id.toString, *)
          .returning(Success(false))
        (workFlowAttemptService.getSingleMsgSetForProcessing)
          .expects(atmpt, *)
          .returning(Success(List(eventAndAttemptDataForProcessingActivity)))

        (triggerDAO.findIntegrationByTeamIdAndCRMTypeAndModuleType)
          .expects(4, IntegrationType.HUBSPOT, IntegrationModuleType.CONTACTS, *)
          .returning(Success(Some(crMIntegrationInDB)))
        (workflowJedisService.removeFromHashMapForRateLimit(_: Set[String])(using _: SRLogger))
          .expects(Set(workflow_crm_setting_id.toString), *)
          .returning(Success(Set()))
        (handleActivityTriggerEventService.matchEventAndTriggerCRMNewFlow(_: CRMIntegrationInDB, _: ActivityEventDataType, _: EventType)(
          _: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger))
          .expects(
            crMIntegrationInDB,
            *,
            EventType.EMAIL_SENT, *, *, *, *
          ).returning(Future.successful(Right(Seq())))

//        (handlePushTriggerEventService.findIntegrationsForInvokeNewFlowV2(_: MQPushTriggerMsg, _: EventType, _: SRLogger)(
//          _: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger))
//          .expects(
//            mqPushTriggerMsg,
//            EventType.EMAIL_SENT,
//            *, *, *, *, *
//          ).returning(Future.successful(Right(Seq())))
        (workFlowAttemptService.updateAttemptLogStatus(
          _: AttemptLogId,
          _: TeamId,
          _: AccountId,
          _: SRLogger
        )(_: AttemptStatus,
          _: Option[WorkflowAttemptTryErrorReason],
          _: Int
        )).expects(attempt_log_id, teamId, accountId, Logger, AttemptStatus.SuccessAttempt, *, 3)
          .returning(Right(attempt_tries_log_id))
        (triggerDAO.updateErrorCount)
          .expects(teamId, workflow_crm_setting_id, 0)
          .returning(Success(None))
        processAttemptService.processSingleAttempt(
          atmpt = atmpt
        ).map { result =>
          println(s"result ----------- $result")

          assert(result.isRight)
        }.recover { e =>
          println(s"ERROR  -----------${e.getMessage}")
          assert(false)
        }
      }

      it("when workFlowAttemptService.getSingleMqMsgForWorkflowCRM sends mqAddToDNCMessage handleAddToDNCTriggerEventService.processEventMessageNewFlow (Success Path)") {

        (triggerDAO.findIntegrationByTeamIdAndModuleId)
          .expects(4, 12, *)
          .returning(Success(Some(crMIntegrationInDB)))
        (workflowJedisService.checkLockForRateLimit(_: String)(using _: SRLogger))
          .expects(atmpt.attempt_setting.workflow_crm_setting_id.toString, *)
          .returning(Success(false))
        (workFlowAttemptService.getSingleMsgSetForProcessing)
          .expects(atmpt, *)
          .returning(Success(List(eventAndAttemptDataForProcessingDNC)))

        (workflowJedisService.removeFromHashMapForRateLimit(_: Set[String])(using _: SRLogger))
          .expects(Set(workflow_crm_setting_id.toString), *)
          .returning(Success(Set()))


        (handleAddToDNCTriggerEventService.processEventMessageNewFlow(_: CRMIntegrationInDB)(
        _: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger))
          .expects(
            crMIntegrationInDB, *, *, *, *
          ).returning(Future.successful(Right(Seq())))
        (workFlowAttemptService.updateAttemptLogStatus(
          _: AttemptLogId,
          _: TeamId,
          _: AccountId,
          _: SRLogger
        )(_: AttemptStatus,
          _: Option[WorkflowAttemptTryErrorReason],
          _: Int
        )).expects(attempt_log_id, teamId, accountId, Logger, AttemptStatus.SuccessAttempt, None, 3)
          .returning(Right(attempt_tries_log_id))
        (triggerDAO.updateErrorCount)
          .expects(teamId, workflow_crm_setting_id, 0)
          .returning(Success(None))
        processAttemptService.processSingleAttempt(
          atmpt = atmpt
        ).map { result =>
          println(s"result ----------- $result")

          assert(result.isRight)
        }.recover { e =>
          println(s"ERROR  -----------${e.getMessage}")
          assert(false)
        }
      }

      it("when workFlowAttemptService.getSingleMqMsgForWorkflowCRM sends mqTriggerSyncMessage handleSyncTriggerEventService.processEventMessageForNewAuditFlow (Success Path)") {

        (workflowJedisService.checkLockForRateLimit(_: String)(using _: SRLogger))
          .expects(atmpt.attempt_setting.workflow_crm_setting_id.toString, *)
          .returning(Success(false))
        (workFlowAttemptService.getSingleMsgSetForProcessing)
          .expects(atmpt, *)
          .returning(Success(List(eventAndAttemptDataForProcessingSync)))

        (workflowJedisService.removeFromHashMapForRateLimit(_: Set[String])(using _: SRLogger))
          .expects(Set(workflow_crm_setting_id.toString), *)
          .returning(Success(Set()))


        (handleSyncTriggerEventService.processEventMessageForNewAuditFlow(_: Long)(
          _: WSClient, _: ExecutionContext, _: ActorSystem,_:SRLogger))
          .expects(
            eventDataSync.triggerId, *, *, *,*
          ).returning(Future.successful(Right(Seq())))
        (workFlowAttemptService.updateAttemptLogStatus(
          _: AttemptLogId,
          _: TeamId,
          _: AccountId,
          _: SRLogger
        )(_: AttemptStatus,
          _: Option[WorkflowAttemptTryErrorReason],
          _: Int
        )).expects(attempt_log_id, teamId, accountId, Logger, AttemptStatus.SuccessAttempt, None, 3)
          .returning(Right(attempt_tries_log_id))
        (triggerDAO.updateErrorCount)
          .expects(teamId, workflow_crm_setting_id, 0)
          .returning(Success(None))
        processAttemptService.processSingleAttempt(
          atmpt = atmpt
        ).map { result =>
          println(s"result ----------- $result")

          assert(result.isRight)
        }.recover { e =>
          println(s"ERROR  -----------${e.getMessage}")
          assert(false)
        }
      }

      it("when workFlowAttemptService.getSingleMqMsgForWorkflowCRM sends mqPushTriggerMsg handlePushTriggerEventService.findIntegrationsForInvokeNewFlowV2 (Future Failed path)") {

        (triggerDAO.findIntegrationByTeamIdAndCRMType)
          .expects(4, IntegrationType.HUBSPOT, *)
          .returning(Success(Seq(crMIntegrationInDB)))
        (workflowJedisService.checkLockForRateLimit(_: String)(using _: SRLogger))
          .expects(atmpt.attempt_setting.workflow_crm_setting_id.toString, *)
          .returning(Success(false))
        (workFlowAttemptService.getSingleMsgSetForProcessing)
          .expects(atmpt.copy(eventType = EventType.CREATED_PROSPECT_IN_SMARTREACH), *)
          .returning(Success(List(eventAndAttemptDataForProcessingPush)))
        (triggerDAO.updateErrorCount)
          .expects(teamId, workflow_crm_setting_id, 0)
          .returning(Success(None))
        (workflowJedisService.removeFromHashMapForRateLimit(_: Set[String])(using _: SRLogger))
          .expects(Set(workflow_crm_setting_id.toString), *)
          .returning(Success(Set()))


        (handlePushTriggerEventService.findIntegrationsForInvokeNewFlowV2(_:  List[EventDataType.PushEventDataType], _: EventType, _: Long, _: Long, _:CRMIntegrationInDB)(
          _: WSClient, _: ExecutionContext, _: ActorSystem, _: SRLogger))
          .expects(
            List(mqPushTriggerMsgCreated),
            EventType.CREATED_PROSPECT_IN_SMARTREACH,
            accountId.id,
             teamId.id,
            crMIntegrationInDB, *, *, *, *
          ).returning(Future.failed(new Throwable("FUTURE ERROR")))
        (workFlowAttemptService.updateAttemptLogStatus(
          _: AttemptLogId,
          _: TeamId,
          _: AccountId,
          _: SRLogger
        )(_: AttemptStatus,
          _: Option[WorkflowAttemptTryErrorReason],
          _: Int
        )).expects(attempt_log_id, teamId, accountId, Logger, AttemptStatus.YetToAttempt, *, 3)
          .returning(Right(attempt_tries_log_id))
        (triggerDAO.setNextAttemptAt)
          .expects(*, workflow_crm_setting_id, teamId)
          .returning(Success(10))
        processAttemptService.processSingleAttempt(
          atmpt = atmpt.copy(eventType = EventType.CREATED_PROSPECT_IN_SMARTREACH)
        ).map { result =>
          println(s"result ----------- $result")

          assert(result.isLeft)
        }.recover { e =>
          println(s"ERROR  -----------${e.getMessage}")
          assert(false)
        }
      }
    }


  }

}
