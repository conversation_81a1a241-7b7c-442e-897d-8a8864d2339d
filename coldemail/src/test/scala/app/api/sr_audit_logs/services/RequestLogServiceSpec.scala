package app.api.sr_audit_logs.services

import org.apache.pekko.actor.ActorSystem
import org.apache.pekko.stream.Materializer
import org.apache.pekko.util.ByteString
import api.sr_audit_logs.dao.RequestLogDAO
import api.sr_audit_logs.models.{APIRequestMethod, RequestLog}
import api.sr_audit_logs.services.RequestLogService
import org.joda.time.DateTime
import org.scalamock.matchers.ArgCapture.CaptureOne
import org.scalamock.scalatest.AsyncMockFactory
import org.scalatest.funspec.AsyncFunSpec
import play.api.http.HttpEntity
import play.api.libs.ws.ahc.AhcWSClient
import play.api.mvc.{Request, ResponseHeader, Result, Results}
import play.api.test.FakeRequest
import utils.SRLogger
import utils.testapp.TestAppExecutionContext

import scala.concurrent.ExecutionContext
import scala.util.{Failure, Success}

class RequestLogServiceSpec[A]
  extends Async<PERSON>unSpec
    with AsyncMockFactory
    with Results {

  implicit lazy val system: ActorSystem = TestAppExecutionContext.actorSystem
  implicit lazy val materializer: Materializer = TestAppExecutionContext.actorMaterializer
  implicit lazy val wSClient: AhcWSClient = TestAppExecutionContext.wsClient
  implicit lazy val actorContext: ExecutionContext = system.dispatcher

  given Logger: SRLogger = new SRLogger("RequestLogServiceSpec Unit test")

  val requestLogDAO = mock[RequestLogDAO]
  val requestLogService = new RequestLogService(requestLogDAO = requestLogDAO, materializer = materializer, ec = actorContext)

  describe("addRequestLog") {

    val team_id: Long = 2
    val accountId: Long = 2
    val actor_name = "Sathish Kumar Mukkoju"
    val request_id = "req_QlWOq1KxPHOZa3zPasSTy35K6dGZrF"
    val request = FakeRequest().asInstanceOf[Request[A]]
    val result = Result(
      header = ResponseHeader(200, Map.empty),
      body = HttpEntity.Strict(ByteString("Hello world!"), Some("text/plain"))
    )
    val request_end_point = "/api/v2/prospects/batch"
    val request_origin = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36"
    val request_ip = "**************"
    val version = "v2"
    val  sr_object = "prospect"

    val requestBody = ""
    val responseBody = ""//result.body.dataStream.runWith(Sink.fold(ByteString.empty)(_ ++ _)).map(_.utf8String)
    val responseHTTPStatus = 200 //httpResponse.header.status

    val requestLog = RequestLog(
      audit_request_log_id = request_id,
      request_body = requestBody,
      request_ip = request_ip,
      request_end_point = request_end_point,
      request_method = APIRequestMethod.POST,
      request_version = version,
      request_origin = request_origin,
      response_body = responseBody,
      response_http_status = responseHTTPStatus,
      team_id = Some(team_id),
      account_id = Some(accountId),
      actor_name = actor_name,
      created_at = DateTime.now()
    )


    it("should throw sql exception while createRequestLog") {

      val requestLog = CaptureOne[RequestLog]()
      (requestLogDAO.insertRequestLogInDB
      (_: RequestLog
      ))
        .expects(
          capture(requestLog)
        )
        .returning(Failure(new Exception("Sql Exception")))

      val res = requestLogService.createRequestLogInDb(
        audit_request_log_id =  request_id,
        teamId = Some(team_id),
        accountId = Some(accountId),
        actor_name = actor_name,
        request = request,
        httpResponse = result
      )

      assert(res.isLeft)


    }

    it("should return None while insert Request log") {

      val requestLog = CaptureOne[RequestLog]()
      (requestLogDAO.insertRequestLogInDB
      (_: RequestLog
      ))
        .expects(
          capture(requestLog)
        )
        .returning(Success(None))

      val res = requestLogService.createRequestLogInDb(
        audit_request_log_id =  request_id,
        teamId = Some(team_id),
        accountId = Some(accountId),
        actor_name = actor_name,
        request = request,
        httpResponse = result
      )

      assert(res.isLeft)
    }

    it("should insert Request log") {

      val requestLog = CaptureOne[RequestLog]()
      (requestLogDAO.insertRequestLogInDB
      (_: RequestLog
      ))
        .expects(
          capture(requestLog)
        )
        .returning(Success(Some(request_id)))

      val res = requestLogService.createRequestLogInDb(
        audit_request_log_id =  request_id,
        teamId = Some(team_id),
        accountId = Some(accountId),
        actor_name = actor_name,
        request = request,
        httpResponse = result
      )

      assert(res.isRight)
      assert(res.toOption.get.nonEmpty)
    }


  }

  describe("getRequestLogs") {

    val request_id = Some("req_1642675705646_42_24_4WNxCJjZqU")
    val team_id: Long = 2L
    val account_id: Long = 2L

    it("should throw sql exception while getRequestLogs") {

      (requestLogDAO.findRequestLogs
      (_: Long, _: Long, _: Option[String], _: Option[Long], _: Option[Long], _: Option[APIRequestMethod], _: Option[Boolean], _: Int
      ))
        .expects(
          team_id,
          account_id,
          request_id,
          None,
          None,
          None,
          None,
          0
        )
        .returning(Failure(new Exception("Sql Exception")))

      val res = requestLogService.getRequestLogs(
        teamId = team_id,
        accountId = account_id,
        audit_request_log_id = request_id,
        created_greater_than = None,
        created_less_than = None,
        method = None,
        is_success = None,
        page = 0
      )

      assert(res.isLeft)

    }

    it("should return seq of getRequestLogs") {


      val dummyRequestLog = RequestLog(
        audit_request_log_id = request_id.get,
        request_body = "",
        request_ip = "**********",
        request_end_point = "api/v2/prospects",
        request_method = APIRequestMethod.POST,
        request_version = "v2",
        request_origin = "",
        response_body = "",
        response_http_status = 200,
        team_id = Some(team_id),
        account_id = Some(account_id),
        actor_name = "Sathish Kumar Mukkoju",
        created_at = DateTime.now()
      )

      (requestLogDAO.findRequestLogs
      (_: Long, _: Long, _: Option[String], _: Option[Long], _: Option[Long], _: Option[APIRequestMethod], _: Option[Boolean], _: Int
      ))
        .expects(
          team_id,
          account_id,
          request_id,
          None,
          None,
          None,
          None,
          0
        )
        .returning(Success(Seq(dummyRequestLog)))

      val res = requestLogService.getRequestLogs(
        teamId = team_id,
        accountId = account_id,
        audit_request_log_id = request_id,
        created_greater_than = None,
        created_less_than = None,
        method = None,
        is_success = None,
        page = 0
      )

      assert(res.isRight)
      assert(res.toOption.get.nonEmpty)

    }
  }

}

