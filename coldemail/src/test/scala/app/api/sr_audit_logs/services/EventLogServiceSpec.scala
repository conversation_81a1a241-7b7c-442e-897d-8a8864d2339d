package app.api.sr_audit_logs.services


import org.apache.pekko.actor.ActorSystem
import org.apache.pekko.stream.ActorMaterializer
import api.sr_audit_logs.dao.EventLogDAO
import api.sr_audit_logs.models.{EventDataType, EventLog, EventObjectType, EventType}
import api.sr_audit_logs.services.{EventLogService, MqHandleEventLogMessage}
import org.joda.time.DateTime
import org.scalamock.matchers.ArgCapture.CaptureOne
import org.scalamock.scalatest.AsyncMockFactory
import org.scalatest.funspec.AsyncFunSpec
import play.api.libs.json.Json
import play.api.libs.ws.ahc.AhcWSClient
import utils.SRLogger
import utils.mq.webhook.handle_event_log.MqHandleEventLogPublisher
import utils.testapp.TestAppExecutionContext
import utils.uuid.SrUuidUtils

import scala.concurrent.ExecutionContext
import scala.util.{Failure, Success}


class EventLogServiceSpec
  extends AsyncFunSpec
    with AsyncMockFactory {


  implicit lazy val system: ActorSystem = TestAppExecutionContext.actorSystem
  implicit lazy val wSClient: AhcWSClient = TestAppExecutionContext.wsClient
  implicit lazy val actorContext: ExecutionContext = system.dispatcher

  given Logger: SRLogger = new SRLogger("EventLogServiceSpec Unit test")

  val eventLogDAO = mock[EventLogDAO]
  val mqHandleEventLogPublisher = mock[MqHandleEventLogPublisher]
  val srUuidUtils = mock[SrUuidUtils]
  val eventLogService = new EventLogService(eventLogDAO = eventLogDAO, mqHandleEventLogPublisher = mqHandleEventLogPublisher, srUuidUtils = srUuidUtils)
  val updateProspectCategoryEventData = EventDataType.PushEventDataType.UpdateProspectCategoryEventData(
    prospectId = 123,
    doerAccountId = 2,
    teamId = 59,
    newProspectCategoryIdCustom = 7777,
    triggerPath = None
  )

  describe("createEventLog") {


    val request_id = Some("req_1642675705646_42_24_4WNxCJjZqU")
    val event_log_id = "evt_1642675705646_42_24_4WNxCJjZqU"
    val team_id: Long = 59
    val account_id: Long = 2
    val event_type = EventType.EMAIL_OPENED
    val event_object_type = EventObjectType.WORKFLOW
    val data = Json.obj()

    it("should throw sql exception while createEventLog") {

      val eventLog = CaptureOne[EventLog]()
      (eventLogDAO.insertEventLogInDB
      (_: EventLog
      ))
        .expects(
          capture(eventLog)
        )
        .returning(Failure(new Exception("Sql Exception")))
      (() => srUuidUtils.generateMqRequestLogId())
        .expects()
        .returning(request_id.get)
      val res = eventLogService.createEventLog(
        event_data_type = updateProspectCategoryEventData,
        teamId = team_id,
        accountId = account_id
      )
      assert(res.isLeft)
    }

    it("should return None while createEventLog") {

      val eventLog = CaptureOne[EventLog]()
      (eventLogDAO.insertEventLogInDB
      (_: EventLog
      ))
        .expects(
          capture(eventLog)
        )
        .returning(Success(None))
      (() => srUuidUtils.generateMqRequestLogId())
        .expects()
        .returning(request_id.get)
      val res = eventLogService.createEventLog(
        event_data_type = updateProspectCategoryEventData,
        teamId = team_id,
        accountId = account_id
      )

      assert(res.isLeft)
    }

    it("should return success while createEventLog") {

      val eventLog = CaptureOne[EventLog]()
      (eventLogDAO.insertEventLogInDB
      (_: EventLog
      ))
        .expects(
          capture(eventLog)
        )
        .returning(Success(Some(event_log_id)))
      (() => srUuidUtils.generateMqRequestLogId())
        .expects()
        .returning(request_id.get)

      (mqHandleEventLogPublisher.publish)
        .expects(*, *, *)
        .returning(Success(()))

      val res = eventLogService.createEventLog(
        event_data_type = updateProspectCategoryEventData,
        teamId = team_id,
        accountId = account_id
      )

      assert(res.toOption.get.nonEmpty)
    }



  }

  describe("getEventLogs") {

    val event_log_id = Some("evt_1642675705646_42_24_4WNxCJjZqU")
    val audit_request_log_id = Some("req_1642675705646_42_24_4WNxCJjZqU")
    val team_id: Long = 2
    val account_id: Long = 2

    it("should throw sql exception while getEventLogs") {

      (eventLogDAO.findEventLogs
      (_: Long, _: Long, _: Option[String], _: Option[String], _: Option[Long], _: Option[EventType], _: Int))
        .expects(
          team_id,
          account_id,
          audit_request_log_id,
          event_log_id,
          None,
          None,
          0
        )
        .returning(Failure(new Exception("Sql Exception")))

      val res = eventLogService.getEventLogs(
        teamId = team_id,
        accountId = account_id,
        audit_request_log_id = audit_request_log_id,
        event_log_id = event_log_id,
        created_greatar_than = None,
        event_type = None,
        page = 0
      )

      assert(res.isLeft)

    }

    it("should return seq of EventLogs") {

      val dummyEventLog = EventLog(
        event_log_id = event_log_id.get,
        audit_request_log_id = audit_request_log_id.get,
        team_id = team_id,
        account_id = account_id,
        event_data_type = updateProspectCategoryEventData,
        created_at = DateTime.now()
      )

      (eventLogDAO.findEventLogs
      (_: Long, _: Long, _: Option[String], _: Option[String], _: Option[Long], _: Option[EventType], _: Int))
        .expects(
          team_id,
          account_id,
          audit_request_log_id,
          event_log_id,
          None,
          None,
          0
        )
        .returning(Success(Seq(dummyEventLog)))

      val res = eventLogService.getEventLogs(
        teamId = team_id,
        accountId = account_id,
        audit_request_log_id = audit_request_log_id,
        event_log_id = event_log_id,
        created_greatar_than = None,
        event_type = None,
        page = 0
      )

      assert(res.isRight)
      assert(res.toOption.get.nonEmpty)

    }

  }

}
