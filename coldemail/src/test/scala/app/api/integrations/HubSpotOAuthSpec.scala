package app.api.integrations

import org.apache.pekko.actor.ActorSystem
import org.apache.pekko.http.scaladsl.model.HttpHeader.ParsingResult.Ok
import org.apache.pekko.stream.scaladsl.Source
import org.apache.pekko.util.ByteString
import api.accounts.TeamId
import api.integrations.BatchContactResponseTypes.InvalidEmailError
import api.integrations.CreateOrUpdateBatchContactsError.{CommonCRMAPIError, HubspotFailureBatchError}
import api.integrations.{BatchContactResponse, BatchContactResponseTypes, CommonCRMAPIErrors, CreateOrUpdateBatchContactsError, HubSpotOAuth, IntegrationTPAccessTokenResponse, NewProspectObject, OldAndNewProspectObject, OldProspectObject, SearchBatchContactsError}
import api.integrations.crmapis.HubspotApi
import api.prospects.models.ProspectId
import api.sr_audit_logs.models.ProspectObjectWithOldProspectDeduplicationColumn
import api.triggers.IntegrationModuleType.{CONTACTS, LEADS}
import api.triggers.dao_service.TriggerDAOService
import api.triggers.{IntegrationModuleType, IntegrationType, Trigger, TriggerFields, TriggerServiceV2, TriggerUsers, UpdateFieldsMappingForm, UpdateUserMappingForm}
import app.test_fixtures.crm.CRMFixtures
import app.test_fixtures.prospect.ProspectFixtures
import eventframework.ProspectObject
import mockws.MockWSHelpers.Action
import mockws.{FakeAhcResponse, MockWS, MockWSHelpers, Route}
import org.joda.time.DateTime
import org.scalamock.scalatest.AsyncMockFactory
import org.scalatest.funspec.AsyncFunSpec
import play.api.http.HeaderNames
import play.api.http.HttpEntity.Strict
import play.api.libs.json.{JsValue, Json}
import play.api.libs.ws.{BodyReadable, BodyWritable, WSClient, WSRequest, WSResponse}
import play.api.libs.ws.ahc.AhcWSClient
import play.api.mvc.{AnyContent, Request, ResponseHeader, Result, Results}
import play.api.test.FakeRequest
import utils.SRLogger
import utils.testapp.{TestAppExecutionContext, TestAppTrait}
import play.api.test.Helpers.{GET, POST}
import utils.mq.webhook.model.CreateInCRMJedisDAO

import java.nio.charset.Charset
import scala.concurrent.duration.FiniteDuration
import scala.concurrent.{ExecutionContext, Future}

class HubSpotOAuthSpec extends  AsyncFunSpec  with AsyncMockFactory {



  val triggerDAO = mock[Trigger]
  val triggerServiceV2 = mock[TriggerServiceV2]
  val triggerDAOService  = mock[TriggerDAOService]
  val hubspotApi = mock[HubspotApi]
  val createInCRMJedisDAO = mock[CreateInCRMJedisDAO]

  val hubSpotOAuth = new HubSpotOAuth(
    triggerDAO = triggerDAO,
    triggerServiceV2 = triggerServiceV2 ,
    triggerDAOService = triggerDAOService,
    hubspotApi = hubspotApi,
    createInCRMJedisDAO = createInCRMJedisDAO
  )
  implicit lazy val system: ActorSystem = TestAppExecutionContext.actorSystem
  implicit lazy val ec: ExecutionContext = system.dispatcher
  implicit lazy val wSClient: AhcWSClient = TestAppExecutionContext.wsClient
  given Logger: SRLogger= new SRLogger("[HubSpotOAuthSpec]")


  describe("HubSpotOAuth.getFilterGroupsByContacts"){

    it("should match if phone and email both are defined"){

      val contacts = Seq(ProspectFixtures.prospectObject)
      val result = HubSpotOAuth.getFilterGroupsByContacts(
        contacts = contacts
      )

      val expectedResult = Json.arr(
        Json.obj(
          "filters" -> Json.arr(
            Json.obj(
              "propertyName" -> "email",
              "operator" -> "IN",
              "values" -> contacts.map(_.email).filter(_.isDefined).map(_.get)
            )
          )
        ),
        Json.obj(
          "filters" -> Json.arr(
            Json.obj(
              "propertyName" -> "phone",
              "operator" ->"IN",
              "values"-> contacts.map(_.phone).filter(_.isDefined).map(_.get)
            )
          )
        )

      )
      println(s"expectedResult ${expectedResult}")
      assert(result == expectedResult)


    }

    it("should match if phone is only  defined"){

      val contacts = Seq(ProspectFixtures.prospectObject.copy(email = None))
      val result = HubSpotOAuth.getFilterGroupsByContacts(
        contacts = contacts
      )

      val expectedResult = Json.arr(
        Json.obj(
          "filters" -> Json.arr(
            Json.obj(
              "propertyName" -> "phone",
              "operator" ->"IN",
              "values"-> contacts.map(_.phone).filter(_.isDefined).map(_.get)
            )
          )
        )

      )
      println(s"expectedResult ${expectedResult}")
      assert(result == expectedResult)


    }
    it("should match if email is only   defined"){

      val contacts = Seq(ProspectFixtures.prospectObject.copy(phone =None))
      val result = HubSpotOAuth.getFilterGroupsByContacts(
        contacts = contacts
      )

      val expectedResult = Json.arr(
        Json.obj(
          "filters" -> Json.arr(
            Json.obj(
              "propertyName" -> "email",
              "operator" -> "IN",
              "values" -> contacts.map(_.email).filter(_.isDefined).map(_.get)
            )
          )
        )

      )
      println(s"expectedResult ${expectedResult}")
      assert(result == expectedResult)


    }
    it("should match if email & phone are undefined"){

      val contacts = Seq(ProspectFixtures.prospectObject.copy(phone =None,email = None))
      val result = HubSpotOAuth.getFilterGroupsByContacts(
        contacts = contacts
      )

      val expectedResult = Json.arr(
        Json.obj(
          "filters" -> Json.arr(
            Json.obj(
              "propertyName" -> "phone",
              "operator" -> "IN",
              "values" -> contacts.map(_.email).filter(_.isDefined).map(_.get)
            )
          )
        )

      )
      println(s"expectedResult ${expectedResult}")
      assert(result == expectedResult)


    }


  }

  describe("HubspotOauth.batchSearchAndReturnJsValue "){

    //EmailNotCompulsory Enabled




    it("should return js value as expect") {

      (hubspotApi.batchSearchForProspects(
        _:IntegrationModuleType,
        _:IntegrationTPAccessTokenResponse.FullTokenData,
        _: Seq[ProspectObject],
        _: WSResponse => Boolean,
        _: (=> Future[WSResponse], FiniteDuration) => Future[WSResponse],
        _: FiniteDuration
      )(_: WSClient,
      _: ExecutionContext,
      _: ActorSystem,
      _: SRLogger))
        .expects(IntegrationModuleType.CONTACTS,CRMFixtures.fullAccessTokenData,Seq(ProspectFixtures.prospectObject),*,*,*,*,*,*,*)
        .returning(Future.successful(Right(Seq(
          BatchContactResponse(
            email = ProspectFixtures.prospectObject.email,
            phone = ProspectFixtures.prospectObject.phone,
            linkedinUrl  = ProspectFixtures.prospectObject.linkedin_url,
            error = None,
            contactId = None
          )
        ))))

      val expectedResult = Seq(
        Json.obj(
          "inputs"-> Json.arr(
            Json.obj(
              "properties" ->Json.obj(
                "email" -> "email",
                "firstname" -> "first_name",
                "lastname" -> "last_name",
                "phone" -> "phone"
              ),
              "prospectId" -> 1
            )
          )
        )
      )

      val result = hubSpotOAuth.batchSearchAndReturnJsValue(
        moduleType = IntegrationModuleType.CONTACTS,
        accessTokenData = CRMFixtures.fullAccessTokenData,
        contacts = Seq(OldAndNewProspectObject(
          oldProspectObject = OldProspectObject(ProspectFixtures.prospectObject),
          newProspectObject = NewProspectObject(ProspectFixtures.prospectObject)
        )),
        fieldMapping = CRMFixtures.fieldMappingForm,
        userMapping = CRMFixtures.userMappingForm,
         //emailNotCompulsoryEnabled = true
      )

      result.map {
        case Left(value) => assert(false)
        case Right(value) =>{
          println(value)
          assert(value == expectedResult)
        }
      }


    }


    it("should return js value as expected without phone , because there is no phone mapping") {

      (hubspotApi.batchSearchForProspects(
        _:IntegrationModuleType,
        _:IntegrationTPAccessTokenResponse.FullTokenData,
        _: Seq[ProspectObject],
        _: WSResponse => Boolean,
        _: (=> Future[WSResponse], FiniteDuration) => Future[WSResponse],
        _: FiniteDuration
      )(_: WSClient,
        _: ExecutionContext,
        _: ActorSystem,
        _: SRLogger))
        .expects(IntegrationModuleType.CONTACTS,CRMFixtures.fullAccessTokenData,Seq(ProspectFixtures.prospectObject),*,*,*,*,*,*,*)
        .returning(Future.successful(Right(Seq(
          BatchContactResponse(
            email = ProspectFixtures.prospectObject.email,
            phone = ProspectFixtures.prospectObject.phone,
            linkedinUrl  = ProspectFixtures.prospectObject.linkedin_url,
            error = None,
            contactId = Some("hubspot_2_spec")
          )
        ))))

      val expectedResult = Seq(
        Json.obj(
          "inputs"-> Json.arr(
            Json.obj(
              "properties" ->Json.obj(
                "email" -> "email",
                "firstname" -> "first_name",
                "lastname" -> "last_name",
              ),
              "contactId" -> "hubspot_2_spec",
              "prospectId" -> 1



            )
          )
        )
      )

      val result = hubSpotOAuth.batchSearchAndReturnJsValue(
        moduleType = IntegrationModuleType.CONTACTS,
        accessTokenData = CRMFixtures.fullAccessTokenData,
        contacts = Seq(OldAndNewProspectObject(
          oldProspectObject = OldProspectObject(ProspectFixtures.prospectObject),
          newProspectObject = NewProspectObject(ProspectFixtures.prospectObject)
        )),
        fieldMapping = CRMFixtures.fieldMappingForm.copy(fields = CRMFixtures.fieldMappingForm.fields.map(_.filter(_.field_label != "Phone"))),
        userMapping = CRMFixtures.userMappingForm,
         //emailNotCompulsoryEnabled = true
      )

      result.map {
        case Left(value) => assert(false)
        case Right(value) =>{
          println(value)
          assert(value == expectedResult)
        }
      }


    }


    it("should return js value as expected without email , because there is no email mapping") {

      (hubspotApi.batchSearchForProspects(
        _:IntegrationModuleType,
        _:IntegrationTPAccessTokenResponse.FullTokenData,
        _: Seq[ProspectObject],
        _: WSResponse => Boolean,
        _: (=> Future[WSResponse], FiniteDuration) => Future[WSResponse],
        _: FiniteDuration
      )(_: WSClient,
        _: ExecutionContext,
        _: ActorSystem,
        _: SRLogger))
        .expects(IntegrationModuleType.CONTACTS,CRMFixtures.fullAccessTokenData,Seq(ProspectFixtures.prospectObject),*,*,*,*,*,*,*)
        .returning(Future.successful(Right(Seq(
          BatchContactResponse(
            email = ProspectFixtures.prospectObject.email,
            phone = ProspectFixtures.prospectObject.phone,
            linkedinUrl  = ProspectFixtures.prospectObject.linkedin_url,
            error = None,
            contactId = Some("hubspot_2_spec")
          )
        ))))

      val expectedResult = Seq(
        Json.obj(
          "inputs"-> Json.arr(
            Json.obj(
              "properties" ->Json.obj(
                "firstname" -> "first_name",
                "lastname" -> "last_name",
                "phone" -> "phone"
              ),
              "contactId" -> "hubspot_2_spec",
              "prospectId" -> 1



            )
          )
        )
      )

      val result = hubSpotOAuth.batchSearchAndReturnJsValue(
        moduleType = IntegrationModuleType.CONTACTS,
        accessTokenData = CRMFixtures.fullAccessTokenData,
        contacts = Seq(OldAndNewProspectObject(
          oldProspectObject = OldProspectObject(ProspectFixtures.prospectObject),
          newProspectObject = NewProspectObject(ProspectFixtures.prospectObject)
        )),
        fieldMapping = CRMFixtures.fieldMappingForm.copy(fields = CRMFixtures.fieldMappingForm.fields.map(_.filter(_.field_label != "Email"))),
        userMapping = CRMFixtures.userMappingForm,
         //emailNotCompulsoryEnabled = true
      )

      result.map {
        case Left(value) => assert(false)
        case Right(value) =>{
          println(value)
          assert(value == expectedResult)
        }
      }


    }


    //EmailNotCompulsory Disabled
//
//    it("should return js value as expect also emailNotCompulsory is disabled") {
//
//      (hubspotApi.batchSearchForProspects(
//        _:IntegrationModuleType,
//        _:IntegrationTPAccessTokenResponse.FullTokenData,
//        _: Seq[ProspectObject],
//        _: WSResponse => Boolean,
//        _: (=> Future[WSResponse], FiniteDuration) => Future[WSResponse],
//        _: FiniteDuration
//      )(_: WSClient,
//        _: ExecutionContext,
//        _: ActorSystem,
//        _: SRLogger))
//        .expects(IntegrationModuleType.CONTACTS,CRMFixtures.fullAccessTokenData,Seq(ProspectFixtures.prospectObject),*,*,*,*,*,*,*)
//        .returning(Future.successful(Right(Seq(
//          BatchContactResponse(
//            email = ProspectFixtures.prospectObject.email,
//            phone = ProspectFixtures.prospectObject.phone,
//            linkedinUrl  = ProspectFixtures.prospectObject.linkedin_url,
//            error = None,
//            contactId = Some("hubspot_contact_id")
//          )
//        ))))
//
//
//
//      val expectedResult = Seq(
//        Json.obj(
//          "vid"->"hubspot_contact_id",
//          "phone" -> "phone",
//          "linkedin_url"->"linkedin_url",
//          "properties" -> Json.arr(
//            Json.obj(
//              "property" ->"email",
//              "value" -> "email"
//            ),
//            Json.obj(
//              "property" -> "firstname",
//              "value" -> "first_name"
//            ),
//            Json.obj(
//              "property" -> "lastname",
//              "value" -> "last_name"
//            ),
//            Json.obj(
//              "property"->"phone",
//              "value"->"phone"
//            )
//
//          )
//
//        )
//      )
//
//      val result = hubSpotOAuth.batchSearchAndReturnJsValue(
//        moduleType = IntegrationModuleType.CONTACTS,
//        accessTokenData = CRMFixtures.fullAccessTokenData,
//        contacts = Seq(OldAndNewProspectObject(
//          oldProspectObject = OldProspectObject(ProspectFixtures.prospectObject),
//          newProspectObject = NewProspectObject(ProspectFixtures.prospectObject)
//        )),
//        fieldMapping = CRMFixtures.fieldMappingForm,
//        userMapping = CRMFixtures.userMappingForm,
//         //emailNotCompulsoryEnabled = false
//      )
//
//      result.map {
//        case Left(value) => assert(false)
//        case Right(value) =>{
//          println(value)
//          assert(value == expectedResult)
//        }
//      }
//
//
//    }
//
//
//    it("should return js value as expected without phone , because there is no phone mapping also emailNotCompulsory is disabled") {
//
//      (hubspotApi.batchSearchForProspects(
//        _:IntegrationModuleType,
//        _:IntegrationTPAccessTokenResponse.FullTokenData,
//        _: Seq[ProspectObject],
//        _: WSResponse => Boolean,
//        _: (=> Future[WSResponse], FiniteDuration) => Future[WSResponse],
//        _: FiniteDuration
//      )(_: WSClient,
//        _: ExecutionContext,
//        _: ActorSystem,
//        _: SRLogger))
//        .expects(IntegrationModuleType.CONTACTS,CRMFixtures.fullAccessTokenData,Seq(ProspectFixtures.prospectObject),*,*,*,*,*,*,*)
//        .returning(Future.successful(Right(Seq(
//          BatchContactResponse(
//            email = ProspectFixtures.prospectObject.email,
//            phone = ProspectFixtures.prospectObject.phone,
//            linkedinUrl  = ProspectFixtures.prospectObject.linkedin_url,
//            error = None,
//            contactId = Some("hubspot_contact_id")
//          )
//        ))))
//
//      val expectedResult = Seq(
//        Json.obj(
//          "vid" -> "hubspot_contact_id",
//          "phone" -> "phone",
//          "linkedin_url"->"linkedin_url",
//          "properties" -> Json.arr(
//            Json.obj(
//              "property" ->"email",
//              "value" -> "email"
//            ),
//            Json.obj(
//              "property" -> "firstname",
//              "value" -> "first_name"
//            ),
//            Json.obj(
//              "property" -> "lastname",
//              "value" -> "last_name"
//            )
//
//          )
//
//        )
//      )
//
//      val result = hubSpotOAuth.batchSearchAndReturnJsValue(
//        moduleType = IntegrationModuleType.CONTACTS,
//        accessTokenData = CRMFixtures.fullAccessTokenData,
//        contacts = Seq(OldAndNewProspectObject(
//          oldProspectObject = OldProspectObject(ProspectFixtures.prospectObject),
//          newProspectObject = NewProspectObject(ProspectFixtures.prospectObject)
//        )),
//        fieldMapping = CRMFixtures.fieldMappingForm.copy(fields = CRMFixtures.fieldMappingForm.fields.map(_.filter(_.field_label != "Phone"))),
//        userMapping = CRMFixtures.userMappingForm,
//         //emailNotCompulsoryEnabled = false
//      )
//
//      result.map {
//        case Left(value) => assert(false)
//        case Right(value) =>{
//          println(value)
//          assert(value == expectedResult)
//        }
//      }
//
//
//    }
//
//
//    it("should return js value as expected without email , because there is no email mapping also emailNotCompulsory is disabled") {
//
//      (hubspotApi.batchSearchForProspects(
//        _:IntegrationModuleType,
//        _:IntegrationTPAccessTokenResponse.FullTokenData,
//        _: Seq[ProspectObject],
//        _: WSResponse => Boolean,
//        _: (=> Future[WSResponse], FiniteDuration) => Future[WSResponse],
//        _: FiniteDuration
//      )(_: WSClient,
//        _: ExecutionContext,
//        _: ActorSystem,
//        _: SRLogger))
//        .expects(IntegrationModuleType.CONTACTS,CRMFixtures.fullAccessTokenData,Seq(ProspectFixtures.prospectObject),*,*,*,*,*,*,*)
//        .returning(Future.successful(Right(Seq(
//          BatchContactResponse(
//            email = ProspectFixtures.prospectObject.email,
//            phone = ProspectFixtures.prospectObject.phone,
//            linkedinUrl  = ProspectFixtures.prospectObject.linkedin_url,
//            error = None,
//            contactId = Some("hubspot_contact_id")
//          )
//        ))))
//
//      val expectedResult = Seq(
//        Json.obj(
//          "vid" -> "hubspot_contact_id",
//          "phone" -> "phone",
//          "linkedin_url"->"linkedin_url",
//          "properties" -> Json.arr(
//            Json.obj(
//              "property" -> "firstname",
//              "value" -> "first_name"
//            ),
//            Json.obj(
//              "property" -> "lastname",
//              "value" -> "last_name"
//            ),
//            Json.obj(
//              "property"->"phone",
//              "value"->"phone"
//            )
//
//          )
//
//        )
//      )
//
//      val result = hubSpotOAuth.batchSearchAndReturnJsValue(
//        moduleType = IntegrationModuleType.CONTACTS,
//        accessTokenData = CRMFixtures.fullAccessTokenData,
//        contacts = Seq(OldAndNewProspectObject(
//          oldProspectObject = OldProspectObject(ProspectFixtures.prospectObject),
//          newProspectObject = NewProspectObject(ProspectFixtures.prospectObject)
//        )),
//        fieldMapping = CRMFixtures.fieldMappingForm.copy(fields = CRMFixtures.fieldMappingForm.fields.map(_.filter(_.field_label != "Email"))),
//        userMapping = CRMFixtures.userMappingForm,
//         //emailNotCompulsoryEnabled = false
//      )
//
//      result.map {
//        case Left(value) => assert(false)
//        case Right(value) =>{
//          println(value)
//          assert(value == expectedResult)
//        }
//      }
//
//
//    }
//

  }

  describe("HubspotOauth.createOrUpdateBatchContacts"){

    it("should return a left with the error type for Unauthorized"){

      (hubspotApi.batchSearchForProspects(
        _:IntegrationModuleType,
        _:IntegrationTPAccessTokenResponse.FullTokenData,
        _: Seq[ProspectObject],
        _: WSResponse => Boolean,
        _: (=> Future[WSResponse], FiniteDuration) => Future[WSResponse],
        _: FiniteDuration
      )(_: WSClient,
        _: ExecutionContext,
        _: ActorSystem,
        _: SRLogger))
        .expects(IntegrationModuleType.CONTACTS,CRMFixtures.fullAccessTokenData,Seq(ProspectFixtures.prospectObject),*,*,*,*,*,*,*)
        .returning(Future.successful(Left(
          SearchBatchContactsError.CommonCRMAPIError(CommonCRMAPIErrors.UnAuthorizedError(
            "Invalid Token"
          ))
          )
        ))

      val result = hubSpotOAuth.createOrUpdateBatchContacts(
        moduleType = CONTACTS,
        accessTokenData = CRMFixtures.fullAccessTokenData,
        contacts = Seq(ProspectObjectWithOldProspectDeduplicationColumn(
          prospectObject = ProspectFixtures.prospectObject,
          oldProspectDeduplicationColumn = None
        )),
        fieldMapping = CRMFixtures.fieldMappingForm,
        userMapping = CRMFixtures.userMappingForm,
        accountId = 1L,
        teamId= 599L,
         //emailNotCompulsoryEnabled = true
      )

      result.map(items =>{

        items.head match {


          case Left(value) => {
            println(value)

            assert(value == CreateOrUpdateBatchContactsError.CommonCRMAPIError(CommonCRMAPIErrors.UnAuthorizedError(
              "Invalid Token"
            )))
            assert(true)
          }
          case Right(value) =>{
            assert(false)
          }
        }

      })

    }

    it("should return a left with the error type for Not Found") {

      (hubspotApi.batchSearchForProspects(
        _: IntegrationModuleType,
        _: IntegrationTPAccessTokenResponse.FullTokenData,
        _: Seq[ProspectObject],
        _: WSResponse => Boolean,
        _: (=> Future[WSResponse], FiniteDuration) => Future[WSResponse],
        _: FiniteDuration
      )(_: WSClient,
        _: ExecutionContext,
        _: ActorSystem,
        _: SRLogger))
        .expects(IntegrationModuleType.CONTACTS, CRMFixtures.fullAccessTokenData, Seq(ProspectFixtures.prospectObject), *, *, *, *, *, *, *)
        .returning(Future.successful(Left(
          SearchBatchContactsError.CommonCRMAPIError(CommonCRMAPIErrors.NotFoundError(
            "Not Found"
          ))
        )
        ))

      val result = hubSpotOAuth.createOrUpdateBatchContacts(
        moduleType = CONTACTS,
        accessTokenData = CRMFixtures.fullAccessTokenData,
        contacts = Seq(ProspectObjectWithOldProspectDeduplicationColumn(
          prospectObject = ProspectFixtures.prospectObject,
          oldProspectDeduplicationColumn = None
        )),
        fieldMapping = CRMFixtures.fieldMappingForm,
        userMapping = CRMFixtures.userMappingForm,
        accountId = 1L,
        teamId = 599L,
         //emailNotCompulsoryEnabled = true
      )

      result.map(items => {

        items.head match {


          case Left(value) => {
            println(value)

            assert(value == CreateOrUpdateBatchContactsError.CommonCRMAPIError(CommonCRMAPIErrors.NotFoundError(
              "Not Found"
            )))
            assert(true)
          }
          case Right(value) => {
            assert(false)
          }
        }

      })
    }


    it("should return a left with the error type for Too Many Request"){

      (hubspotApi.batchSearchForProspects(
        _:IntegrationModuleType,
        _:IntegrationTPAccessTokenResponse.FullTokenData,
        _: Seq[ProspectObject],
        _: WSResponse => Boolean,
        _: (=> Future[WSResponse], FiniteDuration) => Future[WSResponse],
        _: FiniteDuration
      )(_: WSClient,
        _: ExecutionContext,
        _: ActorSystem,
        _: SRLogger))
        .expects(IntegrationModuleType.CONTACTS,CRMFixtures.fullAccessTokenData,Seq(ProspectFixtures.prospectObject),*,*,*,*,*,*,*)
        .returning(Future.successful(Left(
          SearchBatchContactsError.CommonCRMAPIError(CommonCRMAPIErrors.TooManyRequestsError(
            "Too Many Request"
          ))
        )
        ))

      val result = hubSpotOAuth.createOrUpdateBatchContacts(
        moduleType = CONTACTS,
        accessTokenData = CRMFixtures.fullAccessTokenData,
        contacts = Seq(ProspectObjectWithOldProspectDeduplicationColumn(
          prospectObject = ProspectFixtures.prospectObject,
          oldProspectDeduplicationColumn = None
        )),
        fieldMapping = CRMFixtures.fieldMappingForm,
        userMapping = CRMFixtures.userMappingForm,
        accountId = 1L,
        teamId= 599L,
         //emailNotCompulsoryEnabled = true
      )

      result.map(items =>{

        items.head match {


          case Left(value) => {
            println(value)

            assert(value == CreateOrUpdateBatchContactsError.CommonCRMAPIError(CommonCRMAPIErrors.TooManyRequestsError(
              "Too Many Request"
            )))
            assert(true)
          }
          case Right(value) =>{
            assert(false)
          }
        }

      })

    }

    it("should return a left with the error type for Unknown"){

      (hubspotApi.batchSearchForProspects(
        _:IntegrationModuleType,
        _:IntegrationTPAccessTokenResponse.FullTokenData,
        _: Seq[ProspectObject],
        _: WSResponse => Boolean,
        _: (=> Future[WSResponse], FiniteDuration) => Future[WSResponse],
        _: FiniteDuration
      )(_: WSClient,
        _: ExecutionContext,
        _: ActorSystem,
        _: SRLogger))
        .expects(IntegrationModuleType.CONTACTS,CRMFixtures.fullAccessTokenData,Seq(ProspectFixtures.prospectObject),*,*,*,*,*,*,*)
        .returning(Future.successful(Left(
          SearchBatchContactsError.CommonCRMAPIError(CommonCRMAPIErrors.UnknownError(
            "Unknown Error"
          ))
        )
        ))

      val result = hubSpotOAuth.createOrUpdateBatchContacts(
        moduleType = CONTACTS,
        accessTokenData = CRMFixtures.fullAccessTokenData,
        contacts = Seq(ProspectObjectWithOldProspectDeduplicationColumn(
          prospectObject = ProspectFixtures.prospectObject,
          oldProspectDeduplicationColumn = None
        )),
        fieldMapping = CRMFixtures.fieldMappingForm,
        userMapping = CRMFixtures.userMappingForm,
        accountId = 1L,
        teamId= 599L,
         //emailNotCompulsoryEnabled = true
      )

      result.map(items =>{

        items.head match {


          case Left(value) => {
            println(value)

            assert(value == CreateOrUpdateBatchContactsError.CommonCRMAPIError(CommonCRMAPIErrors.UnknownError(
              "Unknown Error"
            )))
            assert(true)
          }
          case Right(value) =>{
            assert(false)
          }
        }

      })

    }

    it("should return a left with the error type for Invalid Module"){

      (hubspotApi.batchSearchForProspects(
        _:IntegrationModuleType,
        _:IntegrationTPAccessTokenResponse.FullTokenData,
        _: Seq[ProspectObject],
        _: WSResponse => Boolean,
        _: (=> Future[WSResponse], FiniteDuration) => Future[WSResponse],
        _: FiniteDuration
      )(_: WSClient,
        _: ExecutionContext,
        _: ActorSystem,
        _: SRLogger))
        .expects(IntegrationModuleType.CONTACTS,CRMFixtures.fullAccessTokenData,Seq(ProspectFixtures.prospectObject),*,*,*,*,*,*,*)
        .returning(Future.successful(Left(
          SearchBatchContactsError.InvalidModuleError("Requested Module is Invalid")
        )
        ))

      val result = hubSpotOAuth.createOrUpdateBatchContacts(
        moduleType = CONTACTS,
        accessTokenData = CRMFixtures.fullAccessTokenData,
        contacts = Seq(ProspectObjectWithOldProspectDeduplicationColumn(
          prospectObject = ProspectFixtures.prospectObject,
          oldProspectDeduplicationColumn = None
        )),
        fieldMapping = CRMFixtures.fieldMappingForm,
        userMapping = CRMFixtures.userMappingForm,
        accountId = 1L,
        teamId= 599L,
         //emailNotCompulsoryEnabled = true
      )

      result.map(items =>{

        items.head match {


          case Left(value) => {
            println(value)

            assert(value == CreateOrUpdateBatchContactsError.InvalidModuleError("Requested Module is Invalid"))
            assert(true)
          }
          case Right(value) =>{
            assert(false)
          }
        }

      })

    }


    it("should return left of invalid module as candidate module is used"){

      val result = hubSpotOAuth.createOrUpdateBatchContacts(
        moduleType = IntegrationModuleType.CANDIDATES,
        accessTokenData = CRMFixtures.fullAccessTokenData,
        contacts = Seq(ProspectObjectWithOldProspectDeduplicationColumn(
          prospectObject = ProspectFixtures.prospectObject,
          oldProspectDeduplicationColumn = None
        )),
        fieldMapping = CRMFixtures.fieldMappingForm,
        userMapping = CRMFixtures.userMappingForm,
        accountId = 1L,
        teamId= 599L,
         //emailNotCompulsoryEnabled = true
      )

      result.map(items =>{

        items.head match {


          case Left(value) => {
            println(value)

            assert(value == CreateOrUpdateBatchContactsError.InvalidModuleError(
              "Invalid module candidates"
            ))
            assert(true)
          }
          case Right(value) =>{
            assert(false)
          }
        }

      })

    }

    /****************************************** EmailNotCompulsory Enabled *******************************************/

    it("should return Left as response was 500 enc enabled") {

      (hubspotApi.batchSearchForProspects(
        _:IntegrationModuleType,
        _:IntegrationTPAccessTokenResponse.FullTokenData,
        _: Seq[ProspectObject],
        _: WSResponse => Boolean,
        _: (=> Future[WSResponse], FiniteDuration) => Future[WSResponse],
        _: FiniteDuration
      )(_: WSClient,
        _: ExecutionContext,
        _: ActorSystem,
        _: SRLogger))
        .expects(IntegrationModuleType.CONTACTS,CRMFixtures.fullAccessTokenData,Seq(ProspectFixtures.prospectObject),*,*,*,*,*,*,*)
        .returning(Future.successful(Right(Seq(
          BatchContactResponse(
            email = ProspectFixtures.prospectObject.email,
            phone = ProspectFixtures.prospectObject.phone,
            linkedinUrl  = ProspectFixtures.prospectObject.linkedin_url,
            error = None,
            contactId = Some("hubspot_2_cid")
          )
        ))))

      (hubspotApi.updateContactInHubspot(
        _ : JsValue,
        _: String,
        _:  WSResponse => Boolean
      )(
        _: WSClient,
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(*,*,*,*,*,*)
        .returning(Future.successful(Left(CommonCRMAPIError(CommonCRMAPIErrors.NotFoundError(msg = "404 Not Found")))))



      val resp = Action.async { (req: Request[AnyContent]) =>
        Future.successful(
          Results.Status(404)(
            Json.obj("message" -> "Not Error")
          ).withHeaders((HeaderNames.CONTENT_TYPE.toString, "application/json") , "X-HubSpot-RateLimit-Interval-Milliseconds" -> "60000")
        )
      }

      val ws: MockWS = MockWS {
        case (POST, "https://api.hubapi.com/contacts/v1/contact/batch") => resp
        case (POST, "https://api.hubapi.com/crm/v3/objects/contacts/batch/create") => resp
      }



      hubSpotOAuth.createOrUpdateBatchContacts(
        moduleType = IntegrationModuleType.CONTACTS,
        accessTokenData = CRMFixtures.fullAccessTokenData,
        contacts= Seq(ProspectObjectWithOldProspectDeduplicationColumn(
          prospectObject = ProspectFixtures.prospectObject,
          oldProspectDeduplicationColumn = None
        )),
        fieldMapping = CRMFixtures.fieldMappingForm,
        userMapping = CRMFixtures.userMappingForm,
        accountId = 1L,
        teamId = 599L,
//        emailNotCompulsoryEnabled  = true
      )(ws = ws,
      ec = ec,
      system = system,
      Logger = Logger) .map(items =>{

        items.head match {


          case Left(value) => {
            println(value)

            assert(value == CreateOrUpdateBatchContactsError.CommonCRMAPIError(CommonCRMAPIErrors.NotFoundError("404 Not Found")))
            assert(true)
          }
          case Right(value) =>{
            assert(false)
          }
        }

      })

    }

    it("should return Left as response was 400 enc enabled") {

      (hubspotApi.batchSearchForProspects(
        _:IntegrationModuleType,
        _:IntegrationTPAccessTokenResponse.FullTokenData,
        _: Seq[ProspectObject],
        _: WSResponse => Boolean,
        _: (=> Future[WSResponse], FiniteDuration) => Future[WSResponse],
        _: FiniteDuration
      )(_: WSClient,
        _: ExecutionContext,
        _: ActorSystem,
        _: SRLogger))
        .expects(IntegrationModuleType.CONTACTS,CRMFixtures.fullAccessTokenData,Seq(ProspectFixtures.prospectObject),*,*,*,*,*,*,*)
        .returning(Future.successful(Right(Seq(
          BatchContactResponse(
            email = ProspectFixtures.prospectObject.email,
            phone = ProspectFixtures.prospectObject.phone,
            linkedinUrl  = ProspectFixtures.prospectObject.linkedin_url,
            error = None
          )
        ))))

      (createInCRMJedisDAO.getLock(
        _: ProspectId,
        _: IntegrationType,
        _: TeamId,
        _: IntegrationModuleType
      )(using _:SRLogger))
        .expects(*, IntegrationType.HUBSPOT,TeamId(599L),IntegrationModuleType.CONTACTS,*)
        .returning(false)


      (createInCRMJedisDAO.acquireLockForProspect(
        _: ProspectId,
        _: IntegrationType,
        _: TeamId,
        _: IntegrationModuleType
      )(using _:SRLogger))
        .expects(*, IntegrationType.HUBSPOT,TeamId(599L),IntegrationModuleType.CONTACTS,*)
        .returning(true)


      (createInCRMJedisDAO.deleteLock(
        _: ProspectId,
        _: IntegrationType,
        _: TeamId,
        _: IntegrationModuleType
      )(using _:SRLogger))
        .expects(*, IntegrationType.HUBSPOT,TeamId(599L),IntegrationModuleType.CONTACTS,*)
        .returning(true)


      (hubspotApi.createContactInHubspot(
        _ : JsValue,
        _: String,
        _:  WSResponse => Boolean
      )(
        _: WSClient,
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(*,*,*,*,*,*)
        .returning(Future.successful(Left(CreateOrUpdateBatchContactsError.HubspotFailureBatchError(
           "Hello", List(BatchContactResponse(Some("email"), Some("phone"), Some("linkedin_url"), Some(InvalidEmailError(
            message = "You lost here",
            errorObj = Json.obj("error"->"INVALID_EMAIL","message" -> "You lost here").toString()
          )), None))
        ))))


      val resp = Action.async { (req: Request[AnyContent]) =>
          Future.successful(
            Results.Status(400)(
              Json.obj("message" -> "Hello",
                "failureMessages" -> Json.arr(
                  Json.obj(
                    "index"->0,
                    "propertyValidationResult" -> Json.obj(
                      "error" ->"INVALID_EMAIL",
                      "message" -> "You lost here"
                    )
                  )
                ))
            ).withHeaders((HeaderNames.CONTENT_TYPE.toString, "application/json"))
          )
        }

      val ws: MockWS = MockWS {
        case (POST, "https://api.hubapi.com/contacts/v1/contact/batch") => resp
        case (POST, "https://api.hubapi.com/crm/v3/objects/contacts/batch/create") => resp
      }


      hubSpotOAuth.createOrUpdateBatchContacts(
        moduleType = IntegrationModuleType.CONTACTS,
        accessTokenData = CRMFixtures.fullAccessTokenData,
        contacts= Seq(ProspectObjectWithOldProspectDeduplicationColumn(
          prospectObject = ProspectFixtures.prospectObject,
          oldProspectDeduplicationColumn = None
        )),
        fieldMapping = CRMFixtures.fieldMappingForm,
        userMapping = CRMFixtures.userMappingForm,
        accountId = 1L,
        teamId = 599L,
//        emailNotCompulsoryEnabled  = true
      )(ws = ws,
        ec = ec,
        system = system,
        Logger = Logger).map(items =>{

        items.head match {


          case Left(value) => {
            println(value)

            assert(value == HubspotFailureBatchError(message ="Hello", Seq(BatchContactResponse(
              email = ProspectFixtures.prospectObject.email ,
              phone = ProspectFixtures.prospectObject.phone,
              linkedinUrl = ProspectFixtures.prospectObject.linkedin_url,
              error = Some(InvalidEmailError(
                message = "You lost here",
                errorObj = Json.obj("error"->"INVALID_EMAIL","message" -> "You lost here").toString()
              ))
            ))))
            assert(true)
          }
          case Right(value) =>{
            assert(false)
          }
        }

      })

    }


    it("should return Left as response was 429  enc enabled") {

      (hubspotApi.batchSearchForProspects(
        _:IntegrationModuleType,
        _:IntegrationTPAccessTokenResponse.FullTokenData,
        _: Seq[ProspectObject],
        _: WSResponse => Boolean,
        _: (=> Future[WSResponse], FiniteDuration) => Future[WSResponse],
        _: FiniteDuration
      )(_: WSClient,
        _: ExecutionContext,
        _: ActorSystem,
        _: SRLogger))
        .expects(IntegrationModuleType.CONTACTS,CRMFixtures.fullAccessTokenData,Seq(ProspectFixtures.prospectObject),*,*,*,*,*,*,*)
        .returning(Future.successful(Right(Seq(
          BatchContactResponse(
            email = ProspectFixtures.prospectObject.email,
            phone = ProspectFixtures.prospectObject.phone,
            linkedinUrl  = ProspectFixtures.prospectObject.linkedin_url,
            error = None
          )
        ))))

      (createInCRMJedisDAO.getLock(
        _: ProspectId,
        _: IntegrationType,
        _: TeamId,
        _: IntegrationModuleType
      )(using _:SRLogger))
        .expects(*, IntegrationType.HUBSPOT,TeamId(599L),IntegrationModuleType.CONTACTS,*)
        .returning(false)


      (createInCRMJedisDAO.acquireLockForProspect(
        _: ProspectId,
        _: IntegrationType,
        _: TeamId,
        _: IntegrationModuleType
      )(using _:SRLogger))
        .expects(*, IntegrationType.HUBSPOT,TeamId(599L),IntegrationModuleType.CONTACTS,*)
        .returning(true)


      (createInCRMJedisDAO.deleteLock(
        _: ProspectId,
        _: IntegrationType,
        _: TeamId,
        _: IntegrationModuleType
      )(using _:SRLogger))
        .expects(*, IntegrationType.HUBSPOT,TeamId(599L),IntegrationModuleType.CONTACTS,*)
        .returning(true)


      (hubspotApi.createContactInHubspot(
        _ : JsValue,
        _: String,
        _:  WSResponse => Boolean
      )(
        _: WSClient,
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(*,*,*,*,*,*)
        .returning(Future.successful(Left(CommonCRMAPIError(CommonCRMAPIErrors.TooManyRequestsError(msg = "Too many requests")))))






      val resp = Action.async { (req: Request[AnyContent]) =>
        Future.successful(
          Results.Status(429)(
            Json.obj("message" -> "Too Many Request Error")
          ).withHeaders((HeaderNames.CONTENT_TYPE.toString, "application/json") , "X-HubSpot-RateLimit-Interval-Milliseconds" -> "60000")
        )
      }

      val ws: MockWS = MockWS {
        case (POST, "https://api.hubapi.com/contacts/v1/contact/batch") => resp
        case (POST, "https://api.hubapi.com/crm/v3/objects/contacts/batch/create") => resp
      }


      hubSpotOAuth.createOrUpdateBatchContacts(
        moduleType = IntegrationModuleType.CONTACTS,
        accessTokenData = CRMFixtures.fullAccessTokenData,
        contacts= Seq(ProspectObjectWithOldProspectDeduplicationColumn(
          prospectObject = ProspectFixtures.prospectObject,
          oldProspectDeduplicationColumn = None
        )),
        fieldMapping = CRMFixtures.fieldMappingForm,
        userMapping = CRMFixtures.userMappingForm,
        accountId = 1L,
        teamId = 599L,
//        emailNotCompulsoryEnabled  = true
      )(ws = ws,
        ec = ec,
        system = system,
        Logger = Logger).map(items =>{

        items.head match {


          case Left(value) => {
            println(value)

            assert(value match {
              case CommonCRMAPIError(err) => err match {

                case CommonCRMAPIErrors.TooManyRequestsError(msg, nextAttemptAt) => true
                case _ => false
              }
              case _ => false
            })
            assert(true)
          }
          case Right(value) =>{
            assert(false)
          }
        }

      })
    }


    it("should return Left as response was 403 - Forbidden  enc enabled") {

      (hubspotApi.batchSearchForProspects(
        _:IntegrationModuleType,
        _:IntegrationTPAccessTokenResponse.FullTokenData,
        _: Seq[ProspectObject],
        _: WSResponse => Boolean,
        _: (=> Future[WSResponse], FiniteDuration) => Future[WSResponse],
        _: FiniteDuration
      )(_: WSClient,
        _: ExecutionContext,
        _: ActorSystem,
        _: SRLogger))
        .expects(IntegrationModuleType.CONTACTS,CRMFixtures.fullAccessTokenData,Seq(ProspectFixtures.prospectObject),*,*,*,*,*,*,*)
        .returning(Future.successful(Right(Seq(
          BatchContactResponse(
            email = ProspectFixtures.prospectObject.email,
            phone = ProspectFixtures.prospectObject.phone,
            linkedinUrl  = ProspectFixtures.prospectObject.linkedin_url,
            error = None
          )
        ))))


      (createInCRMJedisDAO.getLock(
        _: ProspectId,
        _: IntegrationType,
        _: TeamId,
        _: IntegrationModuleType
      )(using _:SRLogger))
        .expects(*, IntegrationType.HUBSPOT,TeamId(599L),IntegrationModuleType.CONTACTS,*)
        .returning(false)


      (createInCRMJedisDAO.acquireLockForProspect(
        _: ProspectId,
        _: IntegrationType,
        _: TeamId,
        _: IntegrationModuleType
      )(using _:SRLogger))
        .expects(*, IntegrationType.HUBSPOT,TeamId(599L),IntegrationModuleType.CONTACTS,*)
        .returning(true)


      (createInCRMJedisDAO.deleteLock(
        _: ProspectId,
        _: IntegrationType,
        _: TeamId,
        _: IntegrationModuleType
      )(using _:SRLogger))
        .expects(*, IntegrationType.HUBSPOT,TeamId(599L),IntegrationModuleType.CONTACTS,*)
        .returning(true)



      (hubspotApi.createContactInHubspot(
        _ : JsValue,
        _: String,
        _:  WSResponse => Boolean
      )(
        _: WSClient,
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(*,*,*,*,*,*)
        .returning(Future.successful(Left(CommonCRMAPIError(CommonCRMAPIErrors.UnknownError("Forbidden Error")))))


      val resp = Action.async { (req: Request[AnyContent]) =>
        Future.successful(
          Results.Status(403)(
            Json.obj("message" -> "Forbidden Error")
          ).withHeaders((HeaderNames.CONTENT_TYPE.toString, "application/json") , "X-HubSpot-RateLimit-Interval-Milliseconds" -> "60000")
        )
      }

      val ws: MockWS = MockWS {
        case (POST, "https://api.hubapi.com/contacts/v1/contact/batch") => resp
        case (POST, "https://api.hubapi.com/crm/v3/objects/contacts/batch/create") => resp
      }


      hubSpotOAuth.createOrUpdateBatchContacts(
        moduleType = IntegrationModuleType.CONTACTS,
        accessTokenData = CRMFixtures.fullAccessTokenData,
        contacts= Seq(ProspectObjectWithOldProspectDeduplicationColumn(
          prospectObject = ProspectFixtures.prospectObject,
          oldProspectDeduplicationColumn = None
        )),
        fieldMapping = CRMFixtures.fieldMappingForm,
        userMapping = CRMFixtures.userMappingForm,
        accountId = 1L,
        teamId = 599L,
//        emailNotCompulsoryEnabled  = true
      )(ws = ws,
        ec = ec,
        system = system,
        Logger = Logger).map(items =>{

        items.head match {


          case Left(value) => {
            println(value)

            assert(value match {
              case CommonCRMAPIError(err) => err match {

                case CommonCRMAPIErrors.UnknownError(msg) => msg == "Forbidden Error"
                case _ => false
              }
              case  _ => false
            })
            assert(true)
          }
          case Right(value) =>{
            assert(false)
          }
        }

      })
    }


    it("should return Left as response was 401 - Unauthorized  enc enabled") {

      (hubspotApi.batchSearchForProspects(
        _:IntegrationModuleType,
        _:IntegrationTPAccessTokenResponse.FullTokenData,
        _: Seq[ProspectObject],
        _: WSResponse => Boolean,
        _: (=> Future[WSResponse], FiniteDuration) => Future[WSResponse],
        _: FiniteDuration
      )(_: WSClient,
        _: ExecutionContext,
        _: ActorSystem,
        _: SRLogger))
        .expects(IntegrationModuleType.CONTACTS,CRMFixtures.fullAccessTokenData,Seq(ProspectFixtures.prospectObject),*,*,*,*,*,*,*)
        .returning(Future.successful(Right(Seq(
          BatchContactResponse(
            email = ProspectFixtures.prospectObject.email,
            phone = ProspectFixtures.prospectObject.phone,
            linkedinUrl  = ProspectFixtures.prospectObject.linkedin_url,
            error = None
          )
        ))))


      (createInCRMJedisDAO.getLock(
        _: ProspectId,
        _: IntegrationType,
        _: TeamId,
        _: IntegrationModuleType
      )(using _:SRLogger))
        .expects(*, IntegrationType.HUBSPOT,TeamId(599L),IntegrationModuleType.CONTACTS,*)
        .returning(false)


      (createInCRMJedisDAO.acquireLockForProspect(
        _: ProspectId,
        _: IntegrationType,
        _: TeamId,
        _: IntegrationModuleType
      )(using _:SRLogger))
        .expects(*, IntegrationType.HUBSPOT,TeamId(599L),IntegrationModuleType.CONTACTS,*)
        .returning(true)


      (createInCRMJedisDAO.deleteLock(
        _: ProspectId,
        _: IntegrationType,
        _: TeamId,
        _: IntegrationModuleType
      )(using _:SRLogger))
        .expects(*, IntegrationType.HUBSPOT,TeamId(599L),IntegrationModuleType.CONTACTS,*)
        .returning(true)

      (hubspotApi.createContactInHubspot(
        _ : JsValue,
        _: String,
        _:  WSResponse => Boolean
      )(
        _: WSClient,
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(*,*,*,*,*,*)
        .returning(Future.successful(Left(CommonCRMAPIError(CommonCRMAPIErrors.UnknownError("Forbidden Error")))))


      val resp = Action.async { (req: Request[AnyContent]) =>
        Future.successful(
          Results.Status(403)(
            Json.obj("message" -> "Forbidden Error")
          ).withHeaders((HeaderNames.CONTENT_TYPE.toString, "application/json") , "X-HubSpot-RateLimit-Interval-Milliseconds" -> "60000")
        )
      }

      val ws: MockWS = MockWS {
        case (POST, "https://api.hubapi.com/contacts/v1/contact/batch") => resp
        case (POST, "https://api.hubapi.com/crm/v3/objects/contacts/batch/create") => resp
      }


      hubSpotOAuth.createOrUpdateBatchContacts(
        moduleType = IntegrationModuleType.CONTACTS,
        accessTokenData = CRMFixtures.fullAccessTokenData,
        contacts= Seq(ProspectObjectWithOldProspectDeduplicationColumn(
          prospectObject = ProspectFixtures.prospectObject,
          oldProspectDeduplicationColumn = None
        )),
        fieldMapping = CRMFixtures.fieldMappingForm,
        userMapping = CRMFixtures.userMappingForm,
        accountId = 1L,
        teamId = 599L,
//        emailNotCompulsoryEnabled  = true
      )(ws = ws,
        ec = ec,
        system = system,
        Logger = Logger).map(items =>{

        items.head match {


          case Left(value) => {
            println(value)

            assert(value match {
              case CommonCRMAPIError(err) => err match {

                case CommonCRMAPIErrors.UnknownError(msg) => msg == "Forbidden Error"
                case _ => false
              }
              case  _ => false
            })
            assert(true)
          }
          case Right(value) =>{
            assert(false)
          }
        }

      })
    }


    it("should return Error  as response was 504 - TimeOut  enc enabled") {

      (hubspotApi.batchSearchForProspects(
        _:IntegrationModuleType,
        _:IntegrationTPAccessTokenResponse.FullTokenData,
        _: Seq[ProspectObject],
        _: WSResponse => Boolean,
        _: (=> Future[WSResponse], FiniteDuration) => Future[WSResponse],
        _: FiniteDuration
      )(_: WSClient,
        _: ExecutionContext,
        _: ActorSystem,
        _: SRLogger))
        .expects(IntegrationModuleType.CONTACTS,CRMFixtures.fullAccessTokenData,Seq(ProspectFixtures.prospectObject),*,*,*,*,*,*,*)
        .returning(Future.successful(Right(Seq(
          BatchContactResponse(
            email = ProspectFixtures.prospectObject.email,
            phone = ProspectFixtures.prospectObject.phone,
            linkedinUrl  = ProspectFixtures.prospectObject.linkedin_url,
            error = None
          )
        ))))


      (createInCRMJedisDAO.getLock(
        _: ProspectId,
        _: IntegrationType,
        _: TeamId,
        _: IntegrationModuleType
      )(using _:SRLogger))
        .expects(*, IntegrationType.HUBSPOT,TeamId(599L),IntegrationModuleType.CONTACTS,*)
        .returning(false)


      (createInCRMJedisDAO.acquireLockForProspect(
        _: ProspectId,
        _: IntegrationType,
        _: TeamId,
        _: IntegrationModuleType
      )(using _:SRLogger))
        .expects(*, IntegrationType.HUBSPOT,TeamId(599L),IntegrationModuleType.CONTACTS,*)
        .returning(true)


      (createInCRMJedisDAO.deleteLock(
        _: ProspectId,
        _: IntegrationType,
        _: TeamId,
        _: IntegrationModuleType
      )(using _:SRLogger))
        .expects(*, IntegrationType.HUBSPOT,TeamId(599L),IntegrationModuleType.CONTACTS,*)
        .returning(true)



      (hubspotApi.createContactInHubspot(
        _ : JsValue,
        _: String,
        _:  WSResponse => Boolean
      )(
        _: WSClient,
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(*,*,*,*,*,*)
        .returning(Future.successful(Left(CommonCRMAPIError(CommonCRMAPIErrors.UnknownError("Timeout Error")))))


      val resp = Action.async { (req: Request[AnyContent]) =>
        Future.successful(
          Results.Status(504)(
            Json.obj("message" -> "TimeOut Error")
          ).withHeaders((HeaderNames.CONTENT_TYPE.toString, "application/json") )
        )
      }

      val ws: MockWS = MockWS {
        case (POST, "https://api.hubapi.com/contacts/v1/contact/batch") => resp
        case (POST, "https://api.hubapi.com/crm/v3/objects/contacts/batch/create") => resp
      }


      hubSpotOAuth.createOrUpdateBatchContacts(
        moduleType = IntegrationModuleType.CONTACTS,
        accessTokenData = CRMFixtures.fullAccessTokenData,
        contacts= Seq(ProspectObjectWithOldProspectDeduplicationColumn(
          prospectObject = ProspectFixtures.prospectObject,
          oldProspectDeduplicationColumn = None
        )),
        fieldMapping = CRMFixtures.fieldMappingForm,
        userMapping = CRMFixtures.userMappingForm,
        accountId = 1L,
        teamId = 599L,
//        emailNotCompulsoryEnabled  = true
      )(ws = ws,
        ec = ec,
        system = system,
        Logger = Logger).map(items =>{

        items.head match {


          case Left(value) => {
            println(value)

            assert(value match {
              case CommonCRMAPIError(err) => err match {

                case CommonCRMAPIErrors.UnknownError(msg) => msg == "TimeOut Error"
                case _ => false
              }
              case  _ => false
            })
            assert(true)
          }
          case Right(value) =>{
            assert(false)
          }
        }

      }).recover{case err =>{
        assert(true)
      }

      }
    }



    it("should return Success of Batch Contact Response") {

      (hubspotApi.batchSearchForProspects(
        _:IntegrationModuleType,
        _:IntegrationTPAccessTokenResponse.FullTokenData,
        _: Seq[ProspectObject],
        _: WSResponse => Boolean,
        _: (=> Future[WSResponse], FiniteDuration) => Future[WSResponse],
        _: FiniteDuration
      )(_: WSClient,
        _: ExecutionContext,
        _: ActorSystem,
        _: SRLogger))
        .expects(IntegrationModuleType.CONTACTS,CRMFixtures.fullAccessTokenData,Seq(ProspectFixtures.prospectObject),*,*,*,*,*,*,*)
        .returning(Future.successful(Right(Seq(
          BatchContactResponse(
            email = ProspectFixtures.prospectObject.email,
            phone = ProspectFixtures.prospectObject.phone,
            linkedinUrl  = ProspectFixtures.prospectObject.linkedin_url,
            error = None
          )
        ))))

      (createInCRMJedisDAO.getLock(
        _: ProspectId,
        _: IntegrationType,
        _: TeamId,
        _: IntegrationModuleType
      )(using _:SRLogger))
        .expects(*, IntegrationType.HUBSPOT,TeamId(599L),IntegrationModuleType.CONTACTS,*)
        .returning(false)


      (createInCRMJedisDAO.acquireLockForProspect(
        _: ProspectId,
        _: IntegrationType,
        _: TeamId,
        _: IntegrationModuleType
      )(using _:SRLogger))
        .expects(*, IntegrationType.HUBSPOT,TeamId(599L),IntegrationModuleType.CONTACTS,*)
        .returning(true)




      (hubspotApi.createContactInHubspot(
        _ : JsValue,
        _: String,
        _:  WSResponse => Boolean
      )(
        _: WSClient,
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(*,*,*,*,*,*)
        .returning(Future.successful(Right(BatchContactResponse(
          email = ProspectFixtures.prospectObject.email,
          phone = ProspectFixtures.prospectObject.phone,
          linkedinUrl  = ProspectFixtures.prospectObject.linkedin_url,
          error = None
        ))))


      val resp = Action.async { req =>
        Future.successful(
          Results.Status(200)(
            Json.obj("message" -> "Success")
          ).withHeaders((HeaderNames.CONTENT_TYPE.toString, "application/json") )
        )
      }

      val ws: MockWS = MockWS {
        case (POST, "https://api.hubapi.com/contacts/v1/contact/batch") => resp
        case (POST, "https://api.hubapi.com/crm/v3/objects/contacts/batch/create") => resp
      }


      hubSpotOAuth.createOrUpdateBatchContacts(
        moduleType = IntegrationModuleType.CONTACTS,
        accessTokenData = CRMFixtures.fullAccessTokenData,
        contacts= Seq(ProspectObjectWithOldProspectDeduplicationColumn(
          prospectObject = ProspectFixtures.prospectObject,
          oldProspectDeduplicationColumn = None
        )),
        fieldMapping = CRMFixtures.fieldMappingForm,
        userMapping = CRMFixtures.userMappingForm,
        accountId = 1L,
        teamId = 599L,
//        emailNotCompulsoryEnabled  = true
      )(ws = ws,
        ec = ec,
        system = system,
        Logger = Logger).map(items =>{

        items.head match {


          case Left(value) => {
            println(value)

            assert(false)

          }
          case Right(value) =>{
            println(value)
            assert(value ==
              BatchContactResponse(
                email = ProspectFixtures.prospectObject.email,
                phone = ProspectFixtures.prospectObject.phone,
                linkedinUrl = ProspectFixtures.prospectObject.linkedin_url,
                error = None

              )
            )
          }
        }

      })
    }












    /****************************************** EmailNotCompulsory Disabled *******************************************/


//    it("should return Left as response was 400") {
//
//      (hubspotApi.batchSearchForProspects(
//        _:IntegrationModuleType,
//        _:IntegrationTPAccessTokenResponse.FullTokenData,
//        _: Seq[ProspectObject],
//        _: WSResponse => Boolean,
//        _: (=> Future[WSResponse], FiniteDuration) => Future[WSResponse],
//        _: FiniteDuration
//      )(_: WSClient,
//        _: ExecutionContext,
//        _: ActorSystem,
//        _: SRLogger))
//        .expects(IntegrationModuleType.CONTACTS,CRMFixtures.fullAccessTokenData,Seq(ProspectFixtures.prospectObject),*,*,*,*,*,*,*)
//        .returning(Future.successful(Right(Seq(
//          BatchContactResponse(
//            email = ProspectFixtures.prospectObject.email,
//            phone = ProspectFixtures.prospectObject.phone,
//            linkedinUrl  = ProspectFixtures.prospectObject.linkedin_url,
//            error = None,
//            contactId = Some("hubspot_contact_id")
//          )
//        ))))
//
//
//      val ws: MockWS = MockWS {
//        case (POST, "https://api.hubapi.com/contacts/v1/contact/batch") =>
//          Action.async { (req: Request[AnyContent]) =>
//            Future.successful(
//              Results.Status(400)(
//                Json.obj("message" -> "Hello",
//                "failureMessages" -> Json.arr(
//                  Json.obj(
//                    "index"->0,
//                    "propertyValidationResult" -> Json.obj(
//                      "error" ->"INVALID_EMAIL",
//                      "message" -> "You lost here"
//                    )
//                  )
//                )
//                )
//              ).withHeaders((HeaderNames.CONTENT_TYPE.toString, "application/json"))
//            )
//          }
//      }
//
//      hubSpotOAuth.createOrUpdateBatchContacts(
//        moduleType = IntegrationModuleType.CONTACTS,
//        accessTokenData = CRMFixtures.fullAccessTokenData,
//        contacts= Seq(ProspectObjectWithOldProspectDeduplicationColumn(
//          prospectObject = ProspectFixtures.prospectObject,
//          oldProspectDeduplicationColumn = None
//        )),
//        fieldMapping = CRMFixtures.fieldMappingForm,
//        userMapping = CRMFixtures.userMappingForm,
//        accountId = 1L,
//        teamId = 599L,
////        emailNotCompulsoryEnabled  = false
//      )(ws = ws,
//        ec = ec,
//        system = system,
//        Logger = Logger).map(items =>{
//
//        items.head match {
//
//
//          case Left(value) => {
//            println(value)
//
//            assert(value == HubspotFailureBatchError(message ="Hello", Seq(BatchContactResponse(
//              email = ProspectFixtures.prospectObject.email ,
//              phone = ProspectFixtures.prospectObject.phone,
//              linkedinUrl = ProspectFixtures.prospectObject.linkedin_url,
//              error = Some(InvalidEmailError(
//                message = "You lost here",
//                errorObj = Json.obj("error"->"INVALID_EMAIL","message" -> "You lost here").toString()
//              ))
//            ))))
//
//          }
//          case Right(value) =>{
//            println(value)
//            assert(false)
//          }
//        }
//
//      })
//
//    }



  }
}
