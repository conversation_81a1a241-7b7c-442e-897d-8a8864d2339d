package app.api.integrations.integration_tests

import api.AppConfig
import api.accounts.TeamId
import api.accounts.models.AccountId
import api.integrations.{IntegrationTPAccessTokenResponse, IntegrationTPUsersList}
import api.prospects.ProspectCreateFormData
import api.prospects.models.ProspectId
import api.sr_audit_logs.dao.WorkflowAttemptDAO
import api.sr_audit_logs.models.{AttemptLogsFoundForTryOrRetry, AttemptStatus, EventDataType, EventLog, EventType, FindAttemptLogsForQueuingError, OldProspectDeduplicationColumn, WorkflowAttemptTryErrorReason, WorkflowSettingData}
import api.triggers.IntegrationModuleType.{CANDIDATES, CONTACTS}
import api.triggers.IntegrationType.{PIPEDRIVE, SALESFORCE}
import api.triggers.{IntegrationModuleType, IntegrationType, SRTriggerActionType, SRTriggerSource, TriggerAction, TriggerCondition, TriggerFields, UpdateCRMModuleLevelSettingsForm, UpdateFieldsMappingForm}
import app.test_fixtures.crm.CRMFixtures
import db_test_spec.api.accounts.fixtures.NewAccountAndEmailSettingData
import db_test_spec.api.campaigns.test_utils.{CampaignUtils, CreateAndStartCampaignData}
import db_test_spec.api.integrations.dao.AuditEventLogTestDAO
import db_test_spec.api.{DbTestingBeforeAllAndAfterAll, InitialData}
import mockws.MockWS
import mockws.MockWSHelpers.Action
import org.joda.time.DateTime
import play.api.http.HeaderNames
import play.api.libs.json.Json
import play.api.libs.ws.ahc.AhcWSClient
import play.api.mvc.{Action, AnyContent, Request, Results}
import play.api.test.Helpers.{GET, POST}
import utils.SRLogger
import utils.mq.services.MQDoNotNackException
import utils.mq.webhook.model.TriggerSource

import scala.concurrent.{Await, Future}
import scala.util.{Failure, Random, Success, Try}
import play.api.libs.json.JodaWrites._
import play.api.libs.ws.WSClient
import scalikejdbc.scalikejdbcSQLInterpolationImplicitDef
import utils.cache_utils.CacheKeyGen
import utils.dbutils.DBUtils

import scala.concurrent.duration.Duration


class PipedriveIntegrationTestSpec extends DbTestingBeforeAllAndAfterAll {


  def generateEventLogId(teamId: Long, accountId: Long): String = {
    val preFix = "evt"
    val mills = DateTime.now().getMillis
    val rand = Random.alphanumeric.take(10).mkString
    val eventId = s"${preFix}_${mills}_${accountId}_${teamId}_$rand"
    eventId
  }

  def getAttemptsToBeMadeInThisCycle(
                                      logger: SRLogger
                                    ): Either[FindAttemptLogsForQueuingError, Seq[AttemptLogsFoundForTryOrRetry[WorkflowSettingData]]] = {


    // here we are getting attempts that are already grouped, and ready to be pushed
    workFlowAttemptService.findAttemptLogsForQueuing(logger = logger) match {
      case Right(groupedList) =>

        // we are making the attempt_status to StartedAttempt so that no other thread goes and picks it
        workFlowAttemptService.addAttemptToQueing(attempt_log_ids = groupedList.flatMap(_.attempt_data.map(_.attempt_log_id))) match {
          case Success(_) => Right(groupedList)
          case Failure(exception) => Left(FindAttemptLogsForQueuingError.SQLExceptionAddToQueue(exception))
        }
      case Left(value) =>
        Left(value)
    }

  }

  private def justUsingToPopulateDb(
                                     initialData: InitialData,
                                     eventLog: EventLog,
                                     workflow_crm_settings: Option[Long],
                                     Logger: SRLogger
                                   ) = {
    val eventLogData = eventLogDAO.findEventLogByTeamIdAndEventLogId(
      teamId = eventLog.team_id,
      event_log_id = eventLog.event_log_id,
      logger = Logger
    )
    eventLogData match {
      case Failure(err) => {
        Failure(err)
      }
      case Success(value) =>
        if (value.isEmpty) {

          for {

            addEvent: Option[String] <- eventLogDAO.insertEventLogInDB(data = eventLog)

            workflowAttemptLog <- Try(workFlowAttemptService.createAttemptLog(
              teamId = TeamId(initialData.head_team_id), // FIXME VALUECLASS
              accountId = AccountId(eventLog.account_id), // FIXME VALUECLASS
              event_log_id = eventLog.event_log_id,
              attempt_data = WorkflowSettingData(
                workflow_crm_setting_id = workflow_crm_settings.get,
                crm_type = IntegrationType.PIPEDRIVE,
                module_type = CONTACTS,
              ),
              created_at = DateTime.now()
            ))


            // creation event


            // update field mapping in crm setting
            updatedFieldMapping <- trigger.updateFieldMapping(
              teamId = initialData.account.teams.head.team_id,
              data = CRMFixtures.fieldMappingForm,
              integration_type = IntegrationType.PIPEDRIVE,
              module_type = CONTACTS
            )

          } yield ()

        } else {
          Success({})
        }
    }


  }

  def getAttemptsLogs(
                       initialData: InitialData,
                     ): Try[Seq[AttemptLogsFoundForTryOrRetry[WorkflowSettingData]]] = {

    for {

      // so i have covered tables workflow_crm_settings audit_event_logs, workflow_crm_module_settings, audit_workflow_event_logs

      allAttempts <- getAttemptsToBeMadeInThisCycle(logger = Logger) match {

        case Left(err) =>
          err match {
            case FindAttemptLogsForQueuingError.SQLExceptionFind(error) =>
              println(s"AttemptRetryCronService workFlowAttemptService.findAttemptLogsForQueuing error: ${err.toString}")
              Failure(error)
            case FindAttemptLogsForQueuingError.SQLExceptionAddToQueue(error) =>
              println(s"AttemptRetryCronService workFlowAttemptService.addingToQueue error: ${err.toString}")

              Failure(error)
          }


        case Right(atmpts) =>

          println(s"AttemptRetryCronService workFlowAttemptService.findAttemptLogsForQueuing success: attempt_log_ids :: ${atmpts.map(_.attempt_data.map(_.attempt_log_id))}")


          // publish each of the events to the mqNewProcessWorkflowAttemptService one at a time
          // note - batch attempts have been grouped together in 1 attempt
          Success(atmpts)
      }


    } yield {
      allAttempts
    }

  }


  def processAttempts(
                       eventLogSeq: Seq[EventLog],
                       initialData: InitialData,
                       workflow_crm_settings: Option[Long],
                       attemptNumber: Int = 0,
                       deleteLockForceFully: Boolean = false,
                       prospectId: ProspectId,
                       maxAttempts: Int = 5,
                       deleteLockForceFullAttemptNumber: Integer = 3
                     )(implicit ws: WSClient): Future[Unit] = {

    println(s"Called with attemptNumber ${attemptNumber}")
    AuditEventLogTestDAO.updateNextAttemptAt(
      workflow_crm_setting_id = workflow_crm_settings.get,
      teamId = TeamId(initialData.account.teams.head.team_id)
    ) match {
      case Failure(e) => Future.successful(println(s"updateNextAttemptAt failed ${e.printStackTrace()}"))
      case Success(value) => {
        Future.successful(println("Successs updateNextAttemptAt"))

      }
    }


    if (attemptNumber == deleteLockForceFullAttemptNumber && deleteLockForceFully) {
      val checkLock = createInCRMJedisDAO.getLock(
        prospectId = prospectId,
        crmType = PIPEDRIVE,
        teamId = TeamId(initialData.account.teams.head.team_id),
        module = IntegrationModuleType.CONTACTS
      )

      println(s"Is a lock present ${checkLock}")


      val resp = createInCRMJedisDAO.deleteLock(
        prospectId = prospectId,
        crmType = PIPEDRIVE,
        teamId = TeamId(initialData.account.teams.head.team_id),
        module = IntegrationModuleType.CONTACTS
      )

      println(s"Delete lock called it returned ${resp}")
    }


    val attempts: Try[Seq[AttemptLogsFoundForTryOrRetry[WorkflowSettingData]]] = getAttemptsLogs(
      initialData = initialData,
    )


    attempts match {
      case Failure(exception) =>
        println(s"attempts failed :: ${exception.printStackTrace()}")
        Future.failed(exception)

      case Success(allAttempts) =>
        println(s"Current Attempts attempt_log_id::${allAttempts.map(_.attempt_data.map(_.attempt_log_id))}")

        Future.sequence(allAttempts.filter(atmpt => atmpt.team_id.id == initialData.head_team_id).map { msg =>
          workFlowAttemptJedisDAOService.getLockedKeysFromSet().map { keys =>
            println(s"all locked keys are ${keys}")
          }

          println(s"${msg.attempt_setting.workflow_crm_setting_id.toString}")

          workFlowAttemptJedisDAOService.acquireAndCheckSingleLock(
            additionalKeyToLock = msg.attempt_setting.workflow_crm_setting_id.toString,
            expireInSeconds = AppConfig.AuditTrail.maxLockInSecondsForProcessingEventInQueue
          ) match {
            case Failure(err) =>
              println(s"Error while acquireAndCheckSingleLock ${err.printStackTrace()}")
              Future.failed(err)

            case Success(false) =>
              println("Could not acquire lock - From test workFlowAttemptJedisDAOService")
              Future.fromTry {
                workFlowAttemptService
                  .removeAttemptFromQueuing(
                    attempt_log_ids = msg.attempt_data.map(_.attempt_log_id),
                    teamId = msg.team_id
                  )
              }.map { _ =>
                Future.successful(true)
              }.recover { e =>
                println(s"Failed to removeAttemptFromQueuing ${msg.attempt_data.map(_.attempt_log_id)} ${Some(e)}")
                Future.successful(true)
              }.flatten

            case Success(true) =>
              println(s"Success: acquired lock")
              println(s"processing attempt atmpt_${msg} tid ${initialData.head_team_id}")
              processAttemptService.processSingleAttempt(atmpt = msg)(
                ws = ws,
                ec = executionContext,
                system = workerActorSystem,
                logger = Logger
              ).flatMap {
                case Right(_) =>
                  // Removing from the lock after we are done processing the message
                  workFlowAttemptJedisDAOService.removeFromHashMap(
                    itemsToRemoveFromSet = Set(msg.attempt_setting.workflow_crm_setting_id.toString)
                  ) match {
                    case Failure(err) =>
                      println(s"Error while removeFromHashMap for SUCCESS ${err.printStackTrace()}")
                      Future.unit
                    case Success(_) =>
                      println("Success: removeFromHashMap for SUCCESS")
                      workFlowAttemptService.updateLastProcessedAt(
                        workflowCRMSettingId = msg.attempt_setting.workflow_crm_setting_id,
                        teamId = msg.team_id
                      )(Logger = Logger)


                      if (attemptNumber == 0) {
                        justUsingToPopulateDb(
                          initialData = initialData,
                          eventLog = eventLogSeq(1),
                          workflow_crm_settings = workflow_crm_settings,
                          Logger = Logger
                        ) match {
                          case Failure(e) => println(s"justUsingToPopulateDb  error occurred  ${e.printStackTrace()}")
                          case Success(value) => println(s"sideeffect success pipedrive")
                        }
                      }

                      if (attemptNumber < maxAttempts) {


                        val res = processAttempts(
                          eventLogSeq = eventLogSeq,
                          initialData = initialData,
                          workflow_crm_settings = workflow_crm_settings,
                          attemptNumber = attemptNumber + 1,
                          maxAttempts = maxAttempts,
                          deleteLockForceFully = deleteLockForceFully,
                          deleteLockForceFullAttemptNumber = deleteLockForceFullAttemptNumber,
                          prospectId = prospectId
                        )(ws) // Second attempt call

                        Await.result(res, Duration.Inf)

                        println(s"Future completed for attempt number ${attemptNumber}")


                        res

                      } else {
                        Future.unit
                      }
                  }

                case Left(err) =>
                  workFlowAttemptService.updateLastProcessedAt(
                    workflowCRMSettingId = msg.attempt_setting.workflow_crm_setting_id,
                    teamId = msg.team_id
                  )(Logger = Logger)
                  err match {
                    case _: WorkflowAttemptTryErrorReason.TooManyRequestsError =>
                      workFlowAttemptService.removeAttemptFromQueuing(
                        attempt_log_ids = msg.attempt_data.map(_.attempt_log_id),
                        teamId = msg.team_id
                      ) match {
                        case Failure(er) =>
                          println(s"error while removeAttemptFromQueuing ${er.printStackTrace()}")
                          Future.failed(MQDoNotNackException(s"Rate limit reached wf_${msg.attempt_setting.workflow_crm_setting_id}", log = false))

                        case Success(_) =>
                          Future.failed(MQDoNotNackException(s"Rate limit reached wf_${msg.attempt_setting.workflow_crm_setting_id}", log = false))
                      }

                    case err =>

                      workFlowAttemptJedisDAOService.removeFromHashMap(
                        itemsToRemoveFromSet = Set(msg.attempt_setting.workflow_crm_setting_id.toString)
                      ) match {
                        case Failure(err) =>
                          println(s"Error while removeFromHashMap FOR FAILED ${err.printStackTrace()}")
                          Future.unit
                        case Success(_) =>
                          println(s"Success: removeFromHashMap FOR FAILED  ${err}")
                          if (attemptNumber < maxAttempts) {

                            val res = processAttempts(
                              eventLogSeq = eventLogSeq,
                              initialData = initialData,
                              workflow_crm_settings = workflow_crm_settings,
                              attemptNumber = attemptNumber + 1,
                              deleteLockForceFully = deleteLockForceFully,
                              maxAttempts = maxAttempts,
                              deleteLockForceFullAttemptNumber = deleteLockForceFullAttemptNumber,
                              prospectId = prospectId
                            )(ws) // Second attempt call
                            Await.result(res, Duration.Inf)
                            res

                          } else {
                            Future.unit
                          }

                      }
                  }
              }
          }
        }).map(_ => ())
    }
  }


  describe("Event creation and updation") {


    //FIXME COMMENTED OUT THIS TEST BECAUSE PROSPECT ID WAS HARDCODED WILL FIX LATER TODAY-30OCT
    //    it("should create a event successfully"){
    //      val resp1 = Action.async { req =>
    //        Future.successful(
    //          Results.Status(200)(
    //            Json.obj(
    //              "access_token" -> "access_token_refreshed",
    //              "refresh_token" -> "new_refresh_token",
    //              "expires_in" -> 3000,
    //              "expires_at" -> Json.toJson(DateTime.now().plusDays(2)) ,
    //              "api_domain" -> "https://api.pipedrive.com" ,
    //              "token_type" -> "Bearer"
    //            )
    //          ).withHeaders((HeaderNames.CONTENT_TYPE.toString, "application/json") , "X-HubSpot-RateLimit-Interval-Milliseconds" -> "60000")
    //        )
    //      }
    //
    //      val resp2 = Action.async { req =>
    //        Future.successful(
    //          Results.Status(200)(
    //            Json.obj(
    //              "data" -> Json.arr(Json.obj(
    //                "id" -> 123435,
    //                "email" -> "<EMAIL>"
    //              ))
    //            )
    //          ).withHeaders((HeaderNames.CONTENT_TYPE.toString, "application/json") , "X-HubSpot-RateLimit-Interval-Milliseconds" -> "60000")
    //        )
    //      }
    //
    //      val resp3 = Action { req =>
    //
    //        Results.Status(200)(
    //          Json.obj(
    //            "data" -> Json.arr(Json.obj(
    //              "id" -> 123435,
    //              "email" -> "<EMAIL>"
    //            ))
    //          )
    //        ).withHeaders((HeaderNames.CONTENT_TYPE.toString, "application/json") , "X-HubSpot-RateLimit-Interval-Milliseconds" -> "60000")
    //
    //      }
    //
    //
    //      val resp4 = {
    //        println("I am in resp4")
    //        Action{
    //          Results.Status(200)(
    //            Json.obj(
    //              "data" -> Json.obj(
    //                "items" -> Json.arr()
    //              )
    //            )
    //          ).withHeaders((HeaderNames.CONTENT_TYPE.toString, "application/json") )
    //
    //        }
    //      }
    //      val resp5 = {
    //        println("I am in resp5")
    //        Action{
    //          Results.Status(200).withHeaders((HeaderNames.CONTENT_TYPE.toString, "application/json") )
    //
    //        }
    //      }
    //
    //
    //
    //      implicit val ws  = MockWS {
    //        case (POST, "https://oauth.pipedrive.com/oauth/token") => {
    //          println("Matched case 1")
    //          resp1
    //        }
    //        case (GET, "https://api.pipedrive.com/v1/users")  =>  {
    //          println("Matched case 2 ")
    //          resp2
    //        }
    //        case (GET, "https://api.pipedrive.com/v1/persons")  =>  {
    //          println("Matched case 3")
    //          resp3
    //        }
    //
    //        case (GET, url) if url.startsWith("https://api.pipedrive.com/v1/persons/search")  => {
    //          println("Matched case 4")
    //          resp4
    //        }
    //
    //        case (POST, "https://api.pipedrive.com/v1/persons")  =>  {
    //          println("Matched case 5")
    //          resp5
    //        }
    //
    //      }
    //
    //
    //
    //
    //
    //      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData(
    //        quota_per_day = 300,
    //        max_emails_per_prospect_per_day = 100,
    //        max_emails_per_prospect_per_week = 500,
    //        max_emails_per_prospect_account_per_day = Option(500),
    //        max_emails_per_prospect_account_per_week = Option(500)
    //      ).get
    //
    //
    //      val audit_request_log_id = srUuidUtils.generateMqRequestLogId()
    //      val pid = 4L
    //      val event_log_id = generateEventLogId(teamId = initialData.account.teams.head.team_id, accountId = initialData.account.internal_id)
    //      val eventLog = EventLog(
    //        event_log_id = event_log_id,
    //        audit_request_log_id = audit_request_log_id,
    //        event_data_type = EventDataType.PushEventDataType.CreatedProspectsEventData(
    //          created_id = pid,
    //          ownerAccountId = initialData.account.internal_id,
    //          teamId = initialData.account.teams.head.team_id,
    //          triggerPath = Some(TriggerSource.OTHER)
    //        ),
    //        team_id = initialData.account.teams.head.team_id,
    //        account_id = initialData.account.internal_id,
    //        created_at = DateTime.now()
    //      )
    //
    //      val attempts = for {
    //        // workflow crm settings to create a crm setting
    //        // it creates entry workflow_crm_settings table ( user mapping, access token etc)
    //        // and also creates entry in workflow_crm_module_settings(field mapping)
    //        workflow_crm_settings <- workflowCrmSettingsDAO.insertOrUpdateTPAccessTokens(
    //          teamId = initialData.account.teams.head.team_id,
    //          accountId = initialData.account.internal_id,
    //          tokens = IntegrationTPAccessTokenResponse.FullTokenData(
    //            access_token = "access_token_pipedrive",
    //            refresh_token= Some("refresh_token_pipedrive"),
    //            expires_in = Some(0),
    //            expires_at = Some(DateTime.now().plusDays(2)),
    //            token_type = Some("Bearer"),
    //            api_domain = Some("https://api.pipedrive.com"), //this is only for zoho case bcz zoho has multi DC API's URLS with  (.eu, .au., .in, .com)
    //            is_sandbox = Some(true)
    //          ),
    //          service_provider = IntegrationType.PIPEDRIVE,
    //          tp_user_id = Some("pipedrive_user_id"),
    //          tp_company_id = None,  //in Hubpost case company id will be portal id,
    //          tp_owner_id = None, //this param specific for Hubspot
    //          tp_users = Seq(IntegrationTPUsersList(
    //            id="123435",
    //            email = "<EMAIL>"
    //          )),
    //          is_sandbox  = true
    //        )
    //
    //        _ <- triggerService.updateCRMModuleLevelSettings(
    //          crm_type = IntegrationType.PIPEDRIVE,
    //            module_type = IntegrationModuleType.CONTACTS,
    //          team_id = initialData.account.teams.head.team_id,
    //          data = UpdateCRMModuleLevelSettingsForm(
    //            active = true,
    //            create_or_update_record_in_crm =true,
    //            track_activities = true,
    //            create_record_if_not_exists = true
    //          ),
    //          Logger = Logger
    //        )
    //
    //
    //        addEvent: Option[String] <- eventLogDAO.insertEventLogInDB(data = eventLog)
    //
    //        workflowAttemptLog <- Try(workFlowAttemptService.createAttemptLog(
    //          teamId = TeamId(initialData.account.teams.head.team_id), // FIXME VALUECLASS
    //          accountId = AccountId(eventLog.account_id), // FIXME VALUECLASS
    //          event_log_id = eventLog.event_log_id,
    //          attempt_data = WorkflowSettingData(
    //          workflow_crm_setting_id = workflow_crm_settings.get,
    //          crm_type = IntegrationType.PIPEDRIVE,
    //          module_type = CONTACTS,
    //        )
    //        ))
    //
    //
    //        // creation event
    //
    //
    //      // update field mapping in crm setting
    //       updatedFieldMapping <- trigger.updateFieldMapping(
    //         teamId = initialData.account.teams.head.team_id,
    //         data = CRMFixtures.fieldMappingForm,
    //         integration_type = IntegrationType.PIPEDRIVE,
    //         module_type = CONTACTS
    //       )
    //
    //        // so i have covered tables workflow_crm_settings audit_event_logs, workflow_crm_module_settings, audit_workflow_event_logs
    //
    //      allAttempts <- getAttemptsToBeMadeInThisCycle(logger = Logger) match {
    //
    //        case Left(err) =>
    //        err match {
    //        case FindAttemptLogsForQueuingError.SQLExceptionFind(error) =>
    //          Logger.error(s"AttemptRetryCronService workFlowAttemptService.findAttemptLogsForQueuing error: ${err.toString}")
    //          Failure(error)
    //         case FindAttemptLogsForQueuingError.SQLExceptionAddToQueue(error) =>
    //        Logger.error(s"AttemptRetryCronService workFlowAttemptService.addingToQueue error: ${err.toString}")
    //          Failure(error)
    //      }
    //
    //
    //      case Right(atmpts) =>
    //
    //        Logger.debug(s"AttemptRetryCronService workFlowAttemptService.findAttemptLogsForQueuing success: attempt_log_ids :: ${atmpts.map(_.attempt_data.map(_.attempt_log_id))}")
    //
    //        // publish each of the events to the mqNewProcessWorkflowAttemptService one at a time
    //        // note - batch attempts have been grouped together in 1 attempt
    //        Success(atmpts)
    //      }
    //
    //
    //
    //    } yield {
    //        allAttempts
    //    }
    //
    //    attempts match {
    //        case Failure(exception) => {
    //
    //          println(s"attempts failed :: ${exception.printStackTrace()}")
    //          assert(false)
    //        }
    //        case Success(allattempts) =>{
    //
    //          println(s"All attempts are :: ${allattempts}")
    //
    //          val results = Future.sequence(allattempts.map(msg => {
    //            workFlowAttemptJedisDAOService.acquireAndCheckSingleLock(
    //              additionalKeyToLock = msg.attempt_setting.workflow_crm_setting_id.toString,
    //              expireInSeconds = AppConfig.AuditTrail.maxLockInSecondsForProcessingEventInQueue,
    //            ) match {
    //
    //              case Failure(err) =>
    //                Logger.error("Error while acquireAndCheckSingleLock", err)
    //
    //                Future.failed(err)
    //
    //              case Success(false) =>
    //
    //                Logger.debug("Could not acquire lock")
    //
    //                Future.fromTry {
    //                  workFlowAttemptService
    //                    .removeAttemptFromQueuing(
    //                      attempt_log_ids = msg.attempt_data.map(_.attempt_log_id),
    //                      teamId = msg.team_id,
    //                    )
    //                }.map{value => //DO Nothing
    //                  Future.successful(true)
    //
    //                }.recover{ e =>
    //                  println(s"Failed to removeAttemptFromQueuing ${msg.attempt_data.map(_.attempt_log_id)} error:: ${e.printStackTrace()}")
    //                  Future.successful(true)
    //
    //                  //Failed to remove from queue
    //                }.flatten
    //
    //
    //              case Success(true) =>
    //
    //                println(s"Success: acquired lock")
    //
    //                // going for processing the messages that is one attempt, which can be a single attempt Event type or a grouped attempt event type
    //                processAttemptService.processSingleAttempt(atmpt = msg)(
    //                  ws = ws, ec = executionContext, system = actorSystem, logger = Logger
    //                ).flatMap {
    //                  case Right(_) =>
    //                    //removing from the lock after we are done processing the message
    //                    workFlowAttemptJedisDAOService.removeFromHashMap(
    //                      itemsToRemoveFromSet = Set(msg.attempt_setting.workflow_crm_setting_id.toString)
    //                    ) match {
    //                      case Failure(err) =>
    //                        println(s"Error while removeFromHashMap for SUCCESS ${err.printStackTrace()}")
    //                      case Success(_) =>
    //                        println("Success: removeFromHashMap for SUCCESS")
    //                    }
    //                    println(s"Ran process successfully with a Either[Right] for atmpt - $msg")
    //                    Future({})
    //                  case Left(err) =>
    //
    //                    err match {
    //                      case _: WorkflowAttemptTryErrorReason.TooManyRequestsError =>
    //
    //
    //                        // 2.1. CRM is Rate-Limited
    //                        // updating the attempt status as yet_to_attempt, retry count is not incremented - fn2
    //                        // we will not reque this event
    //
    //                        workFlowAttemptService
    //                          .removeAttemptFromQueuing(
    //                            attempt_log_ids = msg.attempt_data.map(_.attempt_log_id),
    //                            teamId = msg.team_id,
    //                          ) match {
    //
    //                          case Failure(er) =>
    //                            println(s"error while removeAttemptFromQueuing ${er.printStackTrace()}")
    //
    //                            Future.failed(MQDoNotNackException(s"Rate limit reached wf_${msg.attempt_setting.workflow_crm_setting_id}", log = false))
    //
    //
    //                          case Success(_) =>
    //                            //                    Logger.debug("removeAttemptFromQueuing success because crm rate-limited")
    //
    //                            Future.failed(MQDoNotNackException(s"Rate limit reached wf_${msg.attempt_setting.workflow_crm_setting_id}", log = false))
    //
    //                        }
    //
    //
    //                      case e =>
    //
    //                        //FIXME: we need to remove from the hash when we get TooManyRequestsError error too
    //                        workFlowAttemptJedisDAOService.removeFromHashMap(
    //                          itemsToRemoveFromSet = Set(msg.attempt_setting.workflow_crm_setting_id.toString)
    //                        ) match {
    //                          case Failure(err) =>
    //                            println(s"Error while removeFromHashMap FOR FAILED ${err.printStackTrace()}" )
    //                          case Success(_) =>
    //                            println("Success: removeFromHashMap FOR FAILED")
    //                        }
    //                        println(s"Ran process successfully with a Either[LEFT] for atmpt ${e.message}- $msg")
    //                        Future({})
    //                    }
    //                }
    //            }
    //
    //          }))
    //
    //            .map(value => {
    //              println(s"reached here Success case ${value}")
    //
    //              val attempt_status = AuditEventLogTestDAO.getAttemptStatusByEventLogID(
    //                event_log_id = event_log_id
    //              ).get
    //
    //
    //
    //              println("I am checking redis key with following values")
    //              println(s"prospectId = ${pid}")
    //              println(s"teamId = ${initialData.account.teams.head.team_id}")
    //
    //
    //              val made_create_call_in_lastHr = createInCRMJedisDAO.getLock(
    //                prospectId = ProspectId(pid),
    //                crmType = IntegrationType.PIPEDRIVE,
    //                teamId = TeamId(initialData.account.teams.head.team_id),
    //                module = IntegrationModuleType.CONTACTS
    //              )
    //
    //              println(s"key in redis ::${CacheKeyGen.getCrmLockKeyForProspectId(
    //                prospectId =  ProspectId(pid),
    //                crmType = IntegrationType.PIPEDRIVE,
    //                teamId = TeamId(initialData.account.teams.head.team_id),
    //                module = IntegrationModuleType.CONTACTS
    //              )}")
    //
    //              println(s"made_create_call_in_lastHr ${made_create_call_in_lastHr}")
    //
    //              Seq(attempt_status,made_create_call_in_lastHr)
    //
    //
    //
    //
    //
    //
    //            })
    //
    //
    //          results.map(resp =>{
    //            println("finally::"+ resp.toString())
    //
    //            assert(resp.length == 2  && resp.head.equals(Some("success_attempt")) && resp(1).equals(true))
    //          }).recover(e =>{
    //            println(s"finalResults error ${e.printStackTrace()}")
    //            assert(false)
    //          })
    //
    //        }
    //      }
    //
    //    }


    /*Note:
     The prospect if after a create, is  updated will just get ignored and just get marked as yet_to_attempt showing that the bug is fixed"
     */
    it("should not create one creation event and second one should be updation event and second one should get marked as yet_to_attempt showing that the bug is fixed") {


      val resp1: Action[AnyContent] = Action.async { (req: Request[AnyContent]) =>
        Future.successful(
          Results.Status(200)(
            Json.obj(
              "access_token" -> "access_token_refreshed",
              "refresh_token" -> "new_refresh_token",
              "expires_in" -> 3000,
              "expires_at" -> Json.toJson(DateTime.now().plusDays(2)),
              "api_domain" -> "https://api.pipedrive.com",
              "token_type" -> "Bearer"
            )
          ).withHeaders((HeaderNames.CONTENT_TYPE, "application/json"), "X-HubSpot-RateLimit-Interval-Milliseconds" -> "60000")
        )
      }

      val resp2: Action[AnyContent] = Action.async { (req: Request[AnyContent]) =>
        Future.successful(
          Results.Status(200)(
            Json.obj(
              ("data", Json.arr(
                Json.obj(
                  ("id", 123435),
                  ("email", "<EMAIL>")
                )
              ))
            )
          ).withHeaders((HeaderNames.CONTENT_TYPE, "application/json"), "X-HubSpot-RateLimit-Interval-Milliseconds" -> "60000")
        )
      }

      val resp3: Action[AnyContent] = Action { (req: Request[AnyContent]) =>

        Results.Status(200)(
          Json.obj(
            ("data", Json.arr(Json.obj(
              ("id", 123435),
              ("email", "<EMAIL>")
            )))
          )
        ).withHeaders((HeaderNames.CONTENT_TYPE, "application/json"), "X-HubSpot-RateLimit-Interval-Milliseconds" -> "60000")

      }


      val resp4: Action[AnyContent] = {
        println("I am in resp4")
        Action {
          Results.Status(200)(
            Json.obj(
              ("data", Json.obj(
                ("items", Json.arr())
              ))
            )
          ).withHeaders((HeaderNames.CONTENT_TYPE, "application/json"))

        }
      }
      val resp5: Action[AnyContent] = {
        println("I am in resp5")
        Action {
          Results.Status(200).withHeaders((HeaderNames.CONTENT_TYPE, "application/json"))

        }
      }


      implicit val ws: MockWS = MockWS {
        case (POST, "https://oauth.pipedrive.com/oauth/token") => {
          // access token call
          println("Matched case 1")
          resp1
        }
        case (GET, "https://api.pipedrive.com/v1/users") => {
          //get tp users
          println("Matched case 2 ")
          resp2
        }
        case (GET, "https://api.pipedrive.com/v1/persons") => {
          println("Matched case 3")
          resp3
        }

        case (GET, url) if url.startsWith("https://api.pipedrive.com/v1/persons/search") => {
          // findall by prospect module call
          println("Matched case 4")
          resp4
        }

        case (POST, "https://api.pipedrive.com/v1/persons") => {
          // creation call
          println("Matched case 5")
          resp5
        }

      }


      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData(
        quota_per_day = 300,
        max_emails_per_prospect_per_day = 100,
        max_emails_per_prospect_per_week = 500,
        max_emails_per_prospect_account_per_day = Option(500),
        max_emails_per_prospect_account_per_week = Option(500)
      ).get


      val createAndStartCampaignData = CampaignUtils.createAndStartAutoEmailCampaign(
        initialData = initialData,
        prospects = Some(Seq(
          ProspectCreateFormData(
            email = Some("<EMAIL>"),
            first_name = Some("arjun"),
            last_name = Some("sharma"),
            custom_fields = Json.obj(),
            owner_id = Some(initialData.account.internal_id),
            list = None,
            company = Some("leadperfectapp"),
            city = Some("Pune"),
            country = None,
            timezone = None,
            created_at = Some(DateTime.now()),
            state = None,
            job_title = None,
            phone = None,
            phone_2 = None,
            phone_3 = None,
            linkedin_url = None
          )
        ))
      )

      val data = Await.result(createAndStartCampaignData, Duration.Inf)
      val prospectId = data.addProspect.head.id;


      val eventLogSeq = Seq(EventLog(
        event_log_id = generateEventLogId(teamId = initialData.account.teams.head.team_id, accountId = initialData.account.internal_id),
        audit_request_log_id = srUuidUtils.generateMqRequestLogId(),
        event_data_type = EventDataType.PushEventDataType.CreatedProspectsEventData(
          created_id = prospectId,
          ownerAccountId = initialData.account.internal_id,
          teamId = initialData.account.teams.head.team_id,
          triggerPath = Some(TriggerSource.OTHER)
        ),
        team_id = initialData.account.teams.head.team_id,
        account_id = initialData.account.internal_id,
        created_at = DateTime.now()
      ), EventLog(
        event_log_id = generateEventLogId(teamId = initialData.account.teams.head.team_id, accountId = initialData.account.internal_id),
        audit_request_log_id = srUuidUtils.generateMqRequestLogId(),
        event_data_type = EventDataType.PushEventDataType.UpdatedProspectsEventData(
          updated_id = prospectId,
          ownerAccountId = initialData.account.internal_id,
          teamId = initialData.account.teams.head.team_id,
          triggerPath = Some(TriggerSource.OTHER),
          oldProspectDeduplicationColumn = Some(
            OldProspectDeduplicationColumn(
              email = data.addProspect.head.email,
              phone = data.addProspect.head.phone
            )
          )

        ),
        team_id = initialData.account.teams.head.team_id,
        account_id = initialData.account.internal_id,
        created_at = DateTime.now()
      ))

      workflowCrmSettingsDAO.insertOrUpdateTPAccessTokens(
        teamId = initialData.account.teams.head.team_id,
        accountId = initialData.account.internal_id,
        tokens = IntegrationTPAccessTokenResponse.FullTokenData(
          access_token = "access_token_pipedrive",
          refresh_token = Some("refresh_token_pipedrive"),
          expires_in = Some(0),
          expires_at = Some(DateTime.now().plusDays(2)),
          token_type = Some("Bearer"),
          api_domain = Some("https://api.pipedrive.com"), //this is only for zoho case bcz zoho has multi DC API's URLS with  (.eu, .au., .in, .com)
          is_sandbox = Some(true)
        ),
        service_provider = IntegrationType.PIPEDRIVE,
        tp_user_id = Some("pipedrive_user_id"),
        tp_company_id = None, //in Hubpost case company id will be portal id,
        tp_owner_id = None, //this param specific for Hubspot
        tp_users = Seq(IntegrationTPUsersList(
          id = "123435",
          email = "<EMAIL>"
        )),
        is_sandbox = true
      ) match {
        case Failure(exception) => {
          assert(false)
        }
        case Success(workflow_crm_settings) => {

          triggerService.updateCRMModuleLevelSettings(
            crm_type = IntegrationType.PIPEDRIVE,
            module_type = IntegrationModuleType.CONTACTS,
            team_id = initialData.account.teams.head.team_id,
            data = UpdateCRMModuleLevelSettingsForm(
              active = true,
              create_or_update_record_in_crm = true,
              track_activities = true,
              create_record_if_not_exists = true
            ),
            Logger = Logger
          )

          justUsingToPopulateDb(
            initialData = initialData,
            eventLog = eventLogSeq.head,
            workflow_crm_settings = workflow_crm_settings,
            Logger = Logger
          ) match {
            case Failure(e) => println(s"justUsingToPopulateDb  error occurred  ${e.printStackTrace()}")
            case Success(value) => println(s"sideeffect success :: ")
          }


          val results =
            processAttempts(
              initialData = initialData,
              eventLogSeq = eventLogSeq,
              workflow_crm_settings = workflow_crm_settings,
              prospectId = ProspectId(prospectId)

            )(ws)

          Await.result(results, Duration.Inf)


          val finalResults = results
            .map(value => {
              println(s"reached here Success case ${value}")

              val attempt_status = eventLogSeq.map(eventLog => {
                val event_log_id = eventLog.event_log_id

                println("calling getAttemptStatusByEventLogID ")
                AuditEventLogTestDAO.getAttemptStatusByEventLogID(
                  event_log_id = event_log_id
                ).get

              })


              println("I am checking redis key with following values")
              println(s"prospectId = ${prospectId}")
              println(s"teamId = ${initialData.account.teams.head.team_id}")


              val made_create_call_in_lastHr = createInCRMJedisDAO.getLock(
                prospectId = ProspectId(prospectId),
                crmType = IntegrationType.PIPEDRIVE,
                teamId = TeamId(initialData.account.teams.head.team_id),
                module = IntegrationModuleType.CONTACTS
              )

              println(s"key in redis  test 2 ::${
                CacheKeyGen.getCrmLockKeyForProspectId(
                  prospectId = ProspectId(prospectId),
                  crmType = IntegrationType.PIPEDRIVE,
                  teamId = TeamId(initialData.account.teams.head.team_id),
                  module = IntegrationModuleType.CONTACTS
                )
              }")

              println(s"made_create_call_in_lastHr test 2 ${made_create_call_in_lastHr}")

              Seq(attempt_status, made_create_call_in_lastHr)


            })

          finalResults.map(resp => {
            println("finally::" + resp.toString())
            assert(resp.length == 2 && resp.head == (List(Some("success_attempt"), Some("yet_to_attempt"))) && resp(1).equals(true))
          }).recover(e => {
            println(s"finalResults error msg_${e.getMessage} ${e.printStackTrace()}")
            assert(false)
          })
        }
      }


    }


    it("should not create one creation event and second one should be updation event and second one should get marked as success_to_attempt  if i release lock in between") {


      val resp1: Action[AnyContent] = Action.async { (req: Request[AnyContent]) =>
        Future.successful(
          Results.Status(200)(
            Json.obj(
              "access_token" -> "access_token_refreshed",
              "refresh_token" -> "new_refresh_token",
              "expires_in" -> 3000,
              "expires_at" -> Json.toJson(DateTime.now().plusDays(2)),
              "api_domain" -> "https://api.pipedrive.com",
              "token_type" -> "Bearer"
            )
          ).withHeaders((HeaderNames.CONTENT_TYPE, "application/json"), "X-HubSpot-RateLimit-Interval-Milliseconds" -> "60000")
        )
      }

      val resp2: Action[AnyContent] = Action.async { (req: Request[AnyContent]) =>
        Future.successful(
          Results.Status(200)(
            Json.obj(
              ("data" , Json.arr(Json.obj(
                ("id", 123435),
                ("email", "<EMAIL>")
              )))
            )
          ).withHeaders((HeaderNames.CONTENT_TYPE, "application/json"), "X-HubSpot-RateLimit-Interval-Milliseconds" -> "60000")
        )
      }

      val resp3: Action[AnyContent] = Action { (req: Request[AnyContent]) =>

        Results.Status(200)(
          Json.obj(
            ("data", Json.arr(Json.obj(
              ("id", 123435),
              ("email", "<EMAIL>")
            )))
          )
        ).withHeaders((HeaderNames.CONTENT_TYPE, "application/json"), "X-HubSpot-RateLimit-Interval-Milliseconds" -> "60000")

      }


      val resp4: Action[AnyContent] = {
        println("I am in resp4")
        Action {
          Results.Status(200)(
            Json.obj(
              ("data", Json.obj(
                ("items", Json.arr())
              ))
            )
          ).withHeaders((HeaderNames.CONTENT_TYPE, "application/json"))

        }
      }
      val resp5: Action[AnyContent] = {
        println("I am in resp5")
        Action {
          Results.Status(200).withHeaders((HeaderNames.CONTENT_TYPE, "application/json"))

        }
      }


      implicit val ws: MockWS = MockWS {
        case (POST, "https://oauth.pipedrive.com/oauth/token") => {
          // access token call
          println("Matched case 1")
          resp1
        }
        case (GET, "https://api.pipedrive.com/v1/users") => {
          //get tp users
          println("Matched case 2 ")
          resp2
        }
        case (GET, "https://api.pipedrive.com/v1/persons") => {
          println("Matched case 3")
          resp3
        }

        case (GET, url) if url.startsWith("https://api.pipedrive.com/v1/persons/search") => {
          // findall by prospect module call
          println("Matched case 4")
          resp4
        }

        case (POST, "https://api.pipedrive.com/v1/persons") => {
          // creation call
          println("Matched case 5")
          resp5
        }

      }


      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData(
        quota_per_day = 300,
        max_emails_per_prospect_per_day = 100,
        max_emails_per_prospect_per_week = 500,
        max_emails_per_prospect_account_per_day = Option(500),
        max_emails_per_prospect_account_per_week = Option(500)
      ).get


      val createAndStartCampaignData = CampaignUtils.createAndStartAutoEmailCampaign(
        initialData = initialData,
        prospects = Some(Seq(
          ProspectCreateFormData(
            email = Some("<EMAIL>"),
            first_name = Some("arjun"),
            last_name = Some("sharma"),
            custom_fields = Json.obj(),
            owner_id = Some(initialData.account.internal_id),
            list = None,
            company = Some("leadperfectapp"),
            city = Some("Pune"),
            country = None,
            timezone = None,
            created_at = Some(DateTime.now()),
            state = None,
            job_title = None,
            phone = None,
            phone_2 = None,
            phone_3 = None,
            linkedin_url = None
          )
        ))
      )

      val data = Await.result(createAndStartCampaignData, Duration.Inf)
      val prospectId = data.addProspect.head.id;


      val eventLogSeq = Seq(EventLog(
        event_log_id = generateEventLogId(teamId = initialData.account.teams.head.team_id, accountId = initialData.account.internal_id),
        audit_request_log_id = srUuidUtils.generateMqRequestLogId(),
        event_data_type = EventDataType.PushEventDataType.CreatedProspectsEventData(
          created_id = prospectId,
          ownerAccountId = initialData.account.internal_id,
          teamId = initialData.account.teams.head.team_id,
          triggerPath = Some(TriggerSource.OTHER)
        ),
        team_id = initialData.account.teams.head.team_id,
        account_id = initialData.account.internal_id,
        created_at = DateTime.now()
      ), EventLog(
        event_log_id = generateEventLogId(teamId = initialData.account.teams.head.team_id, accountId = initialData.account.internal_id),
        audit_request_log_id = srUuidUtils.generateMqRequestLogId(),
        event_data_type = EventDataType.PushEventDataType.UpdatedProspectsEventData(
          updated_id = prospectId,
          ownerAccountId = initialData.account.internal_id,
          teamId = initialData.account.teams.head.team_id,
          triggerPath = Some(TriggerSource.OTHER),
          oldProspectDeduplicationColumn = Some(OldProspectDeduplicationColumn(
            email = data.addProspect.head.email,
            phone = data.addProspect.head.phone
          ))

        ),
        team_id = initialData.account.teams.head.team_id,
        account_id = initialData.account.internal_id,
        created_at = DateTime.now()
      ))

      workflowCrmSettingsDAO.insertOrUpdateTPAccessTokens(
        teamId = initialData.account.teams.head.team_id,
        accountId = initialData.account.internal_id,
        tokens = IntegrationTPAccessTokenResponse.FullTokenData(
          access_token = "access_token_pipedrive",
          refresh_token = Some("refresh_token_pipedrive"),
          expires_in = Some(0),
          expires_at = Some(DateTime.now().plusDays(2)),
          token_type = Some("Bearer"),
          api_domain = Some("https://api.pipedrive.com"), //this is only for zoho case bcz zoho has multi DC API's URLS with  (.eu, .au., .in, .com)
          is_sandbox = Some(true)
        ),
        service_provider = IntegrationType.PIPEDRIVE,
        tp_user_id = Some("pipedrive_user_id"),
        tp_company_id = None, //in Hubpost case company id will be portal id,
        tp_owner_id = None, //this param specific for Hubspot
        tp_users = Seq(IntegrationTPUsersList(
          id = "123435",
          email = "<EMAIL>"
        )),
        is_sandbox = true
      ) match {
        case Failure(exception) => {
          assert(false)
        }
        case Success(workflow_crm_settings) => {

          triggerService.updateCRMModuleLevelSettings(
            crm_type = IntegrationType.PIPEDRIVE,
            module_type = IntegrationModuleType.CONTACTS,
            team_id = initialData.account.teams.head.team_id,
            data = UpdateCRMModuleLevelSettingsForm(
              active = true,
              create_or_update_record_in_crm = true,
              track_activities = true,
              create_record_if_not_exists = true
            ),
            Logger = Logger
          )

          justUsingToPopulateDb(
            initialData = initialData,
            eventLog = eventLogSeq.head,
            workflow_crm_settings = workflow_crm_settings,
            Logger = Logger
          ) match {
            case Failure(e) => println(s"justUsingToPopulateDb  error occurred  ${e.printStackTrace()}")
            case Success(value) => println(s"sideeffect success")
          }


          val results =
            processAttempts(
              initialData = initialData,
              eventLogSeq = eventLogSeq,
              workflow_crm_settings = workflow_crm_settings,
              deleteLockForceFully = true,
              prospectId = ProspectId(prospectId)
            )(ws)

          Await.result(results, Duration.Inf)


          val finalResults = results
            .map(value => {
              println(s"reached here Success case ${value}")

              val attempt_status = eventLogSeq.map(eventLog => {
                val event_log_id = eventLog.event_log_id

                AuditEventLogTestDAO.getAttemptStatusByEventLogID(
                  event_log_id = event_log_id
                ).get

              })


              println("I am checking redis key with following values")
              println(s"prospectId = ${prospectId}")
              println(s"teamId = ${initialData.account.teams.head.team_id}")


              val made_create_call_in_lastHr = createInCRMJedisDAO.getLock(
                prospectId = ProspectId(prospectId),
                crmType = IntegrationType.PIPEDRIVE,
                teamId = TeamId(initialData.account.teams.head.team_id),
                module = IntegrationModuleType.CONTACTS
              )

              println(s"key in redis  test 2 ::${
                CacheKeyGen.getCrmLockKeyForProspectId(
                  prospectId = ProspectId(prospectId),
                  crmType = IntegrationType.PIPEDRIVE,
                  teamId = TeamId(initialData.account.teams.head.team_id),
                  module = IntegrationModuleType.CONTACTS
                )
              }")

              println(s"made_create_call_in_lastHr test 2 ${made_create_call_in_lastHr}")

              Seq(attempt_status, made_create_call_in_lastHr)


            })

          finalResults.map(resp => {
            println("finally::" + resp.toString())

            assert(resp.length == 2 && resp.head.equals(List(Some("success_attempt"), Some("success_attempt"))) && resp(1).equals(true))
          }).recover(e => {
            println(s"finalResults error ${e.printStackTrace()}")
            assert(false)
          })
        }
      }


    }


    it("should call the attempts 60 times in 1 minute with the lock duration being 65s") {


      val resp1: Action[AnyContent] = Action.async { (req: Request[AnyContent]) =>
        Future.successful(
          Results.Status(200)(
            Json.obj(
              "access_token" -> "access_token_refreshed",
              "refresh_token" -> "new_refresh_token",
              "expires_in" -> 3000,
              "expires_at" -> Json.toJson(DateTime.now().plusDays(2)),
              "api_domain" -> "https://api.pipedrive.com",
              "token_type" -> "Bearer"
            )
          ).withHeaders((HeaderNames.CONTENT_TYPE, "application/json"), "X-HubSpot-RateLimit-Interval-Milliseconds" -> "60000")
        )
      }

      val resp2: Action[AnyContent] = Action.async { (req: Request[AnyContent]) =>
        Future.successful(
          Results.Status(200)(
            Json.obj(
              ("data", Json.arr(Json.obj(
                ("id", 123435),
                ("email", "<EMAIL>")
              )))
            )
          ).withHeaders((HeaderNames.CONTENT_TYPE, "application/json"), "X-HubSpot-RateLimit-Interval-Milliseconds" -> "60000")
        )
      }

      val resp3: Action[AnyContent] = Action { (req: Request[AnyContent]) =>

        Results.Status(200)(
          Json.obj(
            ("data", Json.arr(Json.obj(
              ("id", 123435),
              ("email", "<EMAIL>")
            )))
          )
        ).withHeaders((HeaderNames.CONTENT_TYPE, "application/json"), "X-HubSpot-RateLimit-Interval-Milliseconds" -> "60000")

      }


      val resp4: Action[AnyContent] = {
        println("I am in resp4")
        Action {
          Results.Status(200)(
            Json.obj(
              ("data", Json.obj(
                ("items", Json.arr())
              ))
            )
          ).withHeaders((HeaderNames.CONTENT_TYPE, "application/json"))

        }
      }
      val resp5: Action[AnyContent] = {
        println("I am in resp5")
        Action {
          Results.Status(200).withHeaders((HeaderNames.CONTENT_TYPE, "application/json"))

        }
      }


      implicit val ws: MockWS = MockWS {
        case (POST, "https://oauth.pipedrive.com/oauth/token") => {
          // access token call
          println("Matched case 1")
          resp1
        }
        case (GET, "https://api.pipedrive.com/v1/users") => {
          //get tp users
          println("Matched case 2 ")
          resp2
        }
        case (GET, "https://api.pipedrive.com/v1/persons") => {
          println("Matched case 3")
          resp3
        }

        case (GET, url) if url.startsWith("https://api.pipedrive.com/v1/persons/search") => {
          // findall by prospect module call
          println("Matched case 4")
          resp4
        }

        case (POST, "https://api.pipedrive.com/v1/persons") => {
          // creation call
          println("Matched case 5")
          resp5
        }

      }


      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData(
        quota_per_day = 300,
        max_emails_per_prospect_per_day = 100,
        max_emails_per_prospect_per_week = 500,
        max_emails_per_prospect_account_per_day = Option(500),
        max_emails_per_prospect_account_per_week = Option(500)
      ).get


      val createAndStartCampaignData = CampaignUtils.createAndStartAutoEmailCampaign(
        initialData = initialData,
        prospects = Some(Seq(
          ProspectCreateFormData(
            email = Some("<EMAIL>"),
            first_name = Some("arjun"),
            last_name = Some("sharma"),
            custom_fields = Json.obj(),
            owner_id = Some(initialData.account.internal_id),
            list = None,
            company = Some("leadperfectapp"),
            city = Some("Pune"),
            country = None,
            timezone = None,
            created_at = Some(DateTime.now()),
            state = None,
            job_title = None,
            phone = None,
            phone_2 = None,
            phone_3 = None,
            linkedin_url = None
          )
        ))
      )

      val data = Await.result(createAndStartCampaignData, Duration.Inf)
      val prospectId = data.addProspect.head.id;


      val eventLogSeq = Seq(EventLog(
        event_log_id = generateEventLogId(teamId = initialData.account.teams.head.team_id, accountId = initialData.account.internal_id),
        audit_request_log_id = srUuidUtils.generateMqRequestLogId(),
        event_data_type = EventDataType.PushEventDataType.CreatedProspectsEventData(
          created_id = prospectId,
          ownerAccountId = initialData.account.internal_id,
          teamId = initialData.account.teams.head.team_id,
          triggerPath = Some(TriggerSource.OTHER)
        ),
        team_id = initialData.account.teams.head.team_id,
        account_id = initialData.account.internal_id,
        created_at = DateTime.now()
      ), EventLog(
        event_log_id = generateEventLogId(teamId = initialData.account.teams.head.team_id, accountId = initialData.account.internal_id),
        audit_request_log_id = srUuidUtils.generateMqRequestLogId(),
        event_data_type = EventDataType.PushEventDataType.UpdatedProspectsEventData(
          updated_id = prospectId,
          ownerAccountId = initialData.account.internal_id,
          teamId = initialData.account.teams.head.team_id,
          triggerPath = Some(TriggerSource.OTHER),
          oldProspectDeduplicationColumn = Some(
            OldProspectDeduplicationColumn(
              email = data.addProspect.head.email,
              phone = data.addProspect.head.phone
            )
          )

        ),
        team_id = initialData.account.teams.head.team_id,
        account_id = initialData.account.internal_id,
        created_at = DateTime.now()
      ))

      workflowCrmSettingsDAO.insertOrUpdateTPAccessTokens(
        teamId = initialData.account.teams.head.team_id,
        accountId = initialData.account.internal_id,
        tokens = IntegrationTPAccessTokenResponse.FullTokenData(
          access_token = "access_token_pipedrive",
          refresh_token = Some("refresh_token_pipedrive"),
          expires_in = Some(0),
          expires_at = Some(DateTime.now().plusDays(2)),
          token_type = Some("Bearer"),
          api_domain = Some("https://api.pipedrive.com"), //this is only for zoho case bcz zoho has multi DC API's URLS with  (.eu, .au., .in, .com)
          is_sandbox = Some(true)
        ),
        service_provider = IntegrationType.PIPEDRIVE,
        tp_user_id = Some("pipedrive_user_id"),
        tp_company_id = None, //in Hubpost case company id will be portal id,
        tp_owner_id = None, //this param specific for Hubspot
        tp_users = Seq(IntegrationTPUsersList(
          id = "123435",
          email = "<EMAIL>"
        )),
        is_sandbox = true
      ) match {
        case Failure(exception) => {
          assert(false)
        }
        case Success(workflow_crm_settings) => {

          triggerService.updateCRMModuleLevelSettings(
            crm_type = IntegrationType.PIPEDRIVE,
            module_type = IntegrationModuleType.CONTACTS,
            team_id = initialData.account.teams.head.team_id,
            data = UpdateCRMModuleLevelSettingsForm(
              active = true,
              create_or_update_record_in_crm = true,
              track_activities = true,
              create_record_if_not_exists = true
            ),
            Logger = Logger
          )

          justUsingToPopulateDb(
            initialData = initialData,
            eventLog = eventLogSeq.head,
            workflow_crm_settings = workflow_crm_settings,
            Logger = Logger
          ) match {
            case Failure(e) => println(s"justUsingToPopulateDb  error occurred  ${e.printStackTrace()}")
            case Success(value) => println(s"sideeffect success")
          }


          val results =
            processAttempts(
              initialData = initialData,
              eventLogSeq = eventLogSeq,
              workflow_crm_settings = workflow_crm_settings,
              deleteLockForceFully = true,
              deleteLockForceFullAttemptNumber = 4,
              prospectId = ProspectId(prospectId),
              maxAttempts = 160
            )(ws)

          Await.result(results, Duration.Inf)


          val finalResults = results
            .map(value => {
              println(s"reached here Success case ${value}")

              println(s"eventLogSeq ${eventLogSeq}")
              val attempt_status = eventLogSeq.map(eventLog => {
                val event_log_id = eventLog.event_log_id

                AuditEventLogTestDAO.getAttemptStatusByEventLogID(
                  event_log_id = event_log_id
                ).get

              })
              println(s"attempt_status ${attempt_status}")


              println("I am checking redis key with following values")
              println(s"prospectId = ${prospectId}")
              println(s"teamId = ${initialData.account.teams.head.team_id}")


              val made_create_call_in_lastHr = createInCRMJedisDAO.getLock(
                prospectId = ProspectId(prospectId),
                crmType = IntegrationType.PIPEDRIVE,
                teamId = TeamId(initialData.account.teams.head.team_id),
                module = IntegrationModuleType.CONTACTS
              )

              println(s"key in redis  test 2 ::${
                CacheKeyGen.getCrmLockKeyForProspectId(
                  prospectId = ProspectId(prospectId),
                  crmType = IntegrationType.PIPEDRIVE,
                  teamId = TeamId(initialData.account.teams.head.team_id),
                  module = IntegrationModuleType.CONTACTS
                )
              }")

              println(s"made_create_call_in_lastHr test 2 ${made_create_call_in_lastHr}")

              Seq(attempt_status, made_create_call_in_lastHr)


            })

          finalResults.map(resp => {
            println("finally::" + resp.toString())
            assert(resp.length == 2 && resp.head.equals(List(Some("success_attempt"), Some("success_attempt"))) && resp(1).equals(true))
          }).recover(e => {
            println(s"finalResults error ${e.printStackTrace()}")
            assert(false)
          })
        }
      }


    }


  }

}
