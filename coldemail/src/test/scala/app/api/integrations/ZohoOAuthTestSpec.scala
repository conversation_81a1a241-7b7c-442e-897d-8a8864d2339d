package app.api.integrations

import api.integrations.crmapis.ZohoApi
import org.apache.pekko.actor.ActorSystem
import api.integrations.{BatchContactResponse, BatchContactResponseTypes, CommonCRMAPIErrors, FindBatchError, FindOneByEmailAndModuleError, IntegrationContactResponse, IntegrationTPAccessTokenResponse, ZohoOAuth}
import api.sr_audit_logs.models.{OldProspectDeduplicationColumn, ProspectObjectWithOldProspectDeduplicationColumn}
import api.triggers.dao_service.TriggerDAOService
import api.triggers.{IntegrationModuleType, Trigger, TriggerFields, TriggerServiceV2, UpdateFieldsMappingForm, UpdateUserMappingForm}
import app.test_fixtures.crm.CRMFixtures
import app.test_fixtures.prospect.ProspectFixtures
import eventframework.ProspectObject
import mockws.MockWS
import mockws.MockWSHelpers.Action
import org.joda.time.DateTime
import org.scalamock.scalatest.AsyncMockFactory
import org.scalatest.funspec.AsyncFunSpec
import play.api.http.HeaderNames
import play.api.http.HttpVerbs.GET
import play.api.libs.json.{JsValue, Json}
import play.api.libs.ws.{WSClient, WSResponse}
import play.api.libs.ws.ahc.AhcWSClient
import play.api.mvc.Results
import play.api.test.Helpers.POST
import utils.SRLogger
import utils.mq.webhook.model.CreateInCRMJedisDAO
import utils.testapp.TestAppExecutionContext

import scala.concurrent.{ExecutionContext, ExecutionContextExecutor, Future}

class ZohoOAuthTestSpec extends  AsyncFunSpec  with AsyncMockFactory {

  val triggerDAO: Trigger = mock[Trigger]
  val triggerServiceV2: TriggerServiceV2 = mock[TriggerServiceV2]
  val triggerDAOService: TriggerDAOService = mock[TriggerDAOService]
  val createInCRMJedisDAO:CreateInCRMJedisDAO = mock[CreateInCRMJedisDAO]
  val zohoApi = mock[ZohoApi]

  val zohoOAuth = new ZohoOAuth(
    triggerDAO = triggerDAO,
    triggerServiceV2 = triggerServiceV2,
    triggerDAOService = triggerDAOService,
    createInCRMJedisDAO = createInCRMJedisDAO,
    zohoApi = zohoApi
  )


  implicit lazy val system: ActorSystem = TestAppExecutionContext.actorSystem
  implicit lazy val ec: ExecutionContextExecutor = system.dispatcher
  implicit lazy val wSClient: AhcWSClient = TestAppExecutionContext.wsClient
  given Logger: SRLogger = new SRLogger("[ZohoOAuthTestSpec]")


  describe("ZohoOAuthTestSpec.findAllByProspectAndModule"){

    it("should test return Bad request with response code 400"){

      val mockedWs: MockWS = MockWS {
        case (GET, _)  =>
          Action {
            Results.BadRequest("You sent a bad request")
          }
      }


      val result = zohoOAuth.findAllByProspectAndModule(
        accessTokenData = CRMFixtures.fullAccessTokenData,
        prospect = ProspectFixtures.prospectObject,
        module = IntegrationModuleType.CONTACTS,
        statusColumn = None
      )(ws = mockedWs,
        ec = ec,
        system = system,
        Logger = Logger)

      result.map({
        case Left(value) => {
          println(value )
          assert(value match {
            case FindBatchError.CommonCRMAPIError(err) => err match {
              case CommonCRMAPIErrors.InternalServerError(msg) => true
              case _ =>  false
            }
            case _ => false
          })
        }
        case Right(value) => {
          assert(false)
        }

      })

    }

    it("should test return Not Found  with response code 404"){

      val mockedWs: MockWS = MockWS {
        case (GET, _)  =>
          Action {
            Results.NotFound("Not Found")
          }
      }


      val result = zohoOAuth.findAllByProspectAndModule(
        accessTokenData = CRMFixtures.fullAccessTokenData,
        prospect = ProspectFixtures.prospectObject,
        module = IntegrationModuleType.CONTACTS,
        statusColumn = None
      )(ws = mockedWs,
        ec = ec,
        system = system,
        Logger = Logger)

      result.map({
        case Left(value) => {
          println(value )
          assert(value match {
            case FindBatchError.CommonCRMAPIError(err) => err match {
              case CommonCRMAPIErrors.InternalServerError(msg) => true
              case _ =>  false
            }
            case _ => false
          })
        }
        case Right(value) => {
          assert(false)
        }

      })

    }

    it("should test return Gateway TimeOut with response code 500"){

      val mockedWs: MockWS = MockWS {
        case (GET, _)  =>
          Action {
            Results.GatewayTimeout("Timeout")
          }
      }


      val result = zohoOAuth.findAllByProspectAndModule(
        accessTokenData = CRMFixtures.fullAccessTokenData,
        prospect = ProspectFixtures.prospectObject,
        module = IntegrationModuleType.CONTACTS,
        statusColumn = None
      )(ws = mockedWs,
        ec = ec,
        system = system,
        Logger = Logger)

      result.map({
        case Left(value) => {
          println(value )
          assert(value match {
            case FindBatchError.CommonCRMAPIError(err) => err match {
              case CommonCRMAPIErrors.InternalServerError(msg) => true
              case _ =>  false
            }
            case _ => false
          })
        }
        case Right(value) => {
          assert(false)
        }

      })

    }

    it("should test return ForBidden with response code 403"){

      val mockedWs: MockWS = MockWS {
        case (GET, _)  =>
          Action {
            Results.Forbidden("Forbidden Error")
          }
      }


      val result = zohoOAuth.findAllByProspectAndModule(
        accessTokenData = CRMFixtures.fullAccessTokenData,
        prospect = ProspectFixtures.prospectObject,
        module = IntegrationModuleType.CONTACTS,
        statusColumn = None
      )(ws = mockedWs,
        ec = ec,
        system = system,
        Logger = Logger)

      result.map({
        case Left(value) => {
          println(value )
          assert(value match {
            case FindBatchError.CommonCRMAPIError(err) => err match {
              case CommonCRMAPIErrors.InternalServerError(msg) => true
              case _ =>  false
            }
            case _ => false
          })
        }
        case Right(value) => {
          assert(false)
        }

      })

    }

    it("should return success with empty result"){
      val mockedWs: MockWS = MockWS {
        case (GET, _)  =>
          Action {
            Results.Status(204)
          }
      }


      val result = zohoOAuth.findAllByProspectAndModule(
        accessTokenData = CRMFixtures.fullAccessTokenData,
        prospect = ProspectFixtures.prospectObject,
        module = IntegrationModuleType.CONTACTS,
        statusColumn = None
      )(ws = mockedWs,
        ec = ec,
        system = system,
        Logger = Logger)

      result.map({
        case Left(value) => {
          println(value )
          assert(value match {
            case FindBatchError.CommonCRMAPIError(err) => err match {
              case CommonCRMAPIErrors.InternalServerError(msg) => false
              case _ =>  false
            }
            case _ => false
          })
        }
        case Right(value) => {
          assert(value.isEmpty)
        }

      })
    }






    it("should return success with 1 as phone matches"){
      val mockedWs: MockWS = MockWS {
        case (GET, _)  =>
          Action {
            Results.Status(200)(Json.obj("data" -> Json.arr(
                  Json.obj(
                    "id" -> "1",
                    "Email"-> "email",
                    "Phone" -> "phone"
                  )
                ))
            ).withHeaders((HeaderNames.CONTENT_TYPE.toString, "application/json"))
          }
      }


      val result = zohoOAuth.findAllByProspectAndModule(
        accessTokenData = CRMFixtures.fullAccessTokenData,
        prospect = ProspectFixtures.prospectObject,
        module = IntegrationModuleType.CONTACTS,
        statusColumn = None
      )(ws = mockedWs,
        ec = ec,
        system = system,
        Logger = Logger)

      result.map({
        case Left(value) => {
          println(value )
          assert(value match {
            case FindBatchError.CommonCRMAPIError(err) => err match {
              case CommonCRMAPIErrors.InternalServerError(msg) => false
              case _ =>  false
            }
            case _ => false
          })
        }
        case Right(value) => {
          assert(value.length == 1) // 1 because it matches with phone
        }

      })
    }


    it("should return success with 1 result") {
      val mockedWs: MockWS = MockWS {
        case (GET, _) =>
          Action {
            Results.Status(200)(Json.obj("data" -> Json.arr(
              Json.obj(
                "id" -> "1",
                "Email" -> "email",
                "Phone" -> "+919860841349"
              )
            ))
            ).withHeaders((HeaderNames.CONTENT_TYPE.toString, "application/json"))
          }
      }


      val result = zohoOAuth.findAllByProspectAndModule(
        accessTokenData = CRMFixtures.fullAccessTokenData,
        prospect = ProspectFixtures.prospectObject.copy(email = None,phone = Some("+919860841349")),
        module = IntegrationModuleType.CONTACTS,
        statusColumn = None
      )(ws = mockedWs,
        ec = ec,
        system = system,
        Logger = Logger)

      result.map({
        case Left(value) => {
          println(value)
          assert(value match {
            case FindBatchError.CommonCRMAPIError(err) => err match {
              case CommonCRMAPIErrors.InternalServerError(msg) => false
              case _ => false
            }
            case _ => false
          })
        }
        case Right(value) => {
          assert(value.length == 1) // 1 because a valid phone and matches prospects phone
        }

      })
    }


    it("should return success with 1 result even when prospect has only phone"){
      val mockedWs: MockWS = MockWS {
        case (GET, _)  =>
          Action {
            Results.Status(200)(Json.obj("data" -> Json.arr(
              Json.obj(
                "id" -> "1",
                "Phone" -> "+919860841349"
              )
            ))
            ).withHeaders((HeaderNames.CONTENT_TYPE.toString, "application/json"))
          }
      }


      val result = zohoOAuth.findAllByProspectAndModule(
        accessTokenData = CRMFixtures.fullAccessTokenData,
        prospect = ProspectFixtures.prospectObject.copy(email = None, phone = Some("+919860841349")),
        module = IntegrationModuleType.CONTACTS,
        statusColumn = None
      )(ws = mockedWs,
        ec = ec,
        system = system,
        Logger = Logger)

      result.map({
        case Left(value) => {
          println(value )
          assert(value match {
            case FindBatchError.CommonCRMAPIError(err) => err match {
              case CommonCRMAPIErrors.InternalServerError(msg) => false
              case _ =>  false
            }
            case _ => false
          })
        }
        case Right(value) => {
          assert(value.length == 1)
        }

      })
    }





  }

  describe("Test findAllByEmailsAndPhones"){
    it("should return distinct contacts for email and phone search") {

      val mockApiDomain = "https://zohoapis.in"

      val mockAccessTokenData = IntegrationTPAccessTokenResponse.FullTokenData(
        access_token = "mockAccessToken",
        expires_in = Some(12345678),
        refresh_token = Some("mockRefreshToken"),
        expires_at = Some(DateTime.now().plusMinutes(60)),
        token_type = Some("Bearer"),
        api_domain= Some(mockApiDomain), //this is only for zoho case bcz zoho has multi DC API's URLS with  (.eu, .au., .in, .com)
        is_sandbox = Some(false)
      )

      val mockEmails = Seq("<EMAIL>")
      val mockPhones = Seq("12345")

      val mockModule = IntegrationModuleType.CONTACTS

      val mockContact = IntegrationContactResponse(
        email = Some("<EMAIL>"),
        phone = Some("12345"),
        id = "1",
        status = None
      )
      val emailCriteria = "Email:in:<EMAIL>"
      val phoneCriteria =  "Phone:in:12345"

      (zohoApi.findAllByCriteria(
        _: IntegrationTPAccessTokenResponse.FullTokenData,
        _:WSResponse => Boolean,
        _:String,
        _:IntegrationModuleType,
        _:String,
        _:String
      )(
        _:WSClient,
        _:ExecutionContext,
        _:SRLogger
      ))
        .expects(
          mockAccessTokenData,
          *,
          emailCriteria,
          mockModule,
          mockApiDomain,
          *,
          *,
          *,
          *
        )
        .returning(Future.successful(Right(Seq(mockContact))))

      (zohoApi.findAllByCriteria(
        _: IntegrationTPAccessTokenResponse.FullTokenData,
        _:WSResponse => Boolean,
        _:String,
        _:IntegrationModuleType,
        _:String,
        _:String
      )(
        _:WSClient,
        _:ExecutionContext,
        _:SRLogger
      ))
        .expects(
          mockAccessTokenData,
          *,
          phoneCriteria,
          mockModule,
          mockApiDomain,
          *,
          *,
          *,
          *
        )
        .returning(Future.successful(Right(Seq(mockContact))))

      val result = zohoOAuth.findAllByEmailsAndPhones(
        accessTokenData = mockAccessTokenData,
        emails = mockEmails,
        phones = mockPhones,
        module = mockModule,
        apiDomain = mockApiDomain,
        zohoModule = "Contacts"
      )

      result.map {
        case Left(error) => assert(false)
        case Right(contacts) =>
          assert(contacts.size == 1)
          assert(contacts.head == mockContact)
      }
    }

    it("should return an error when both email and phone searches fail") {

      val mockEmails = Seq("<EMAIL>")
      val mockPhones = Seq("12345")

      val mockModule = IntegrationModuleType.CONTACTS
      val mockApiDomain = "https://zohoapis.in"

      val mockContact = IntegrationContactResponse(
        email = Some("<EMAIL>"),
        phone = Some("12345"),
        id = "1",
        status = None
      )
      val emailCriteria =  "Email:in:<EMAIL>,<EMAIL>"
      val phoneCriteria =  "Phone:in:1234567890,9876543210"

      (zohoApi.findAllByCriteria(
        _: IntegrationTPAccessTokenResponse.FullTokenData,
        _:WSResponse => Boolean,
        _:String,
        _:IntegrationModuleType,
        _:String,
        _:String
      )(
        _:WSClient,
        _:ExecutionContext,
        _:SRLogger
      ))
        .expects(
          CRMFixtures.fullAccessTokenData.copy(api_domain = Some("https://zohoapis.in")),
          *,
          emailCriteria,
          mockModule,
          mockApiDomain,
          *,
          *,
          *,
          *
        )
        .returning(Future.successful(Left(FindBatchError.CommonCRMAPIError(CommonCRMAPIErrors.UnknownErrorWithResponseBody(
          msg = "An Error Occurred", responseBody = "response", responseCode = 404
        )))))


      (zohoApi.findAllByCriteria(
        _: IntegrationTPAccessTokenResponse.FullTokenData,
        _:WSResponse => Boolean,
        _:String,
        _:IntegrationModuleType,
        _:String,
        _:String
      )(
        _:WSClient,
        _:ExecutionContext,
        _:SRLogger
      ))
        .expects(
          CRMFixtures.fullAccessTokenData.copy(api_domain = Some("https://zohoapis.in")),
          *,
          phoneCriteria,
          mockModule,
          mockApiDomain,
          *,
          *,
          *,
          *
        )
        .returning(Future.successful(Left(FindBatchError.CommonCRMAPIError(CommonCRMAPIErrors.UnknownErrorWithResponseBody(
          msg = "An Error Occurred", responseBody = "response", responseCode = 404
        )))))


      val result = zohoOAuth.findAllByEmailsAndPhones(
        CRMFixtures.fullAccessTokenData.copy(api_domain = Some("https://zohoapis.in")),
        Seq("<EMAIL>", "<EMAIL>"),
        Seq("1234567890", "9876543210"),
        IntegrationModuleType.CONTACTS,
        "https://zohoapis.in",
        "Contacts"
      )

      result.map {
        case Left(error) =>
          println(error)
          assert(error == FindBatchError.CommonCRMAPIError(CommonCRMAPIErrors.UnknownErrorWithResponseBody(
            msg = "An Error Occurred", responseBody = "response", responseCode = 404
          )))
        case Right(_) => assert(false)
      }
    }

    it("should return an error when email fails and phone succeeds ") {

      val mockEmails = Seq("<EMAIL>")
      val mockPhones = Seq("12345")

      val mockModule = IntegrationModuleType.CONTACTS
      val mockApiDomain = "https://zohoapis.in"

      val mockContact = IntegrationContactResponse(
        email = Some("<EMAIL>"),
        phone = Some("12345"),
        id = "1",
        status = None
      )
      val emailCriteria =  "Email:in:<EMAIL>,<EMAIL>"
      val phoneCriteria =  "Phone:in:1234567890,9876543210"

      (zohoApi.findAllByCriteria(
        _: IntegrationTPAccessTokenResponse.FullTokenData,
        _:WSResponse => Boolean,
        _:String,
        _:IntegrationModuleType,
        _:String,
        _:String
      )(
        _:WSClient,
        _:ExecutionContext,
        _:SRLogger
      ))
        .expects(
          CRMFixtures.fullAccessTokenData.copy(api_domain = Some("https://zohoapis.in")),
          *,
          emailCriteria,
          mockModule,
          mockApiDomain,
          "Contacts",
          *,
          *,
          *,

        )
        .returning(Future.successful(Left(FindBatchError.CommonCRMAPIError(CommonCRMAPIErrors.UnknownErrorWithResponseBody(
          msg = "An Error Occurred", responseBody = "response", responseCode = 404
        )))))



      (zohoApi.findAllByCriteria(
        _: IntegrationTPAccessTokenResponse.FullTokenData,
        _:WSResponse => Boolean,
        _:String,
        _:IntegrationModuleType,
        _:String,
        _:String
      )(
        _:WSClient,
        _:ExecutionContext,
        _:SRLogger
      ))
        .expects(
          CRMFixtures.fullAccessTokenData.copy(api_domain = Some("https://zohoapis.in")),
          *,
          phoneCriteria,
          mockModule,
          mockApiDomain,
          "Contacts",
          *,
          *,
          *
        )
        .returning(Future.successful(Right(Seq(mockContact))))


      val result = zohoOAuth.findAllByEmailsAndPhones(
        CRMFixtures.fullAccessTokenData.copy(api_domain = Some("https://zohoapis.in")),
        Seq("<EMAIL>", "<EMAIL>"),
        Seq("1234567890", "9876543210"),
        IntegrationModuleType.CONTACTS,
        "https://zohoapis.in",
        "Contacts"
      )

      result.map {
        case Left(error) =>
          println(error)
          assert(error == FindBatchError.CommonCRMAPIError(CommonCRMAPIErrors.UnknownErrorWithResponseBody(
            msg = "An Error Occurred", responseBody = "response", responseCode = 404
          )))
        case Right(_) => assert(false)
      }
    }


    it("should return an error when phone fails and email succeeds ") {

      val mockEmails = Seq("<EMAIL>")
      val mockPhones = Seq("12345")

      val mockModule = IntegrationModuleType.CONTACTS
      val mockApiDomain = "https://zohoapis.in"

      val mockContact = IntegrationContactResponse(
        email = Some("<EMAIL>"),
        phone = Some("12345"),
        id = "1",
        status = None
      )
      val emailCriteria =  "Email:in:<EMAIL>,<EMAIL>"
      val phoneCriteria =  "Phone:in:1234567890,9876543210"

      (zohoApi.findAllByCriteria(
        _: IntegrationTPAccessTokenResponse.FullTokenData,
        _:WSResponse => Boolean,
        _:String,
        _:IntegrationModuleType,
        _:String,
        _:String
      )(
        _:WSClient,
        _:ExecutionContext,
        _:SRLogger
      ))
        .expects(
          CRMFixtures.fullAccessTokenData.copy(api_domain = Some("https://zohoapis.in")),
          *,
          emailCriteria,
          mockModule,
          mockApiDomain,
          "Contacts",
          *,
          *,
          *
        )
        .returning(Future.successful(Right(Seq(mockContact))))



      (zohoApi.findAllByCriteria(
        _: IntegrationTPAccessTokenResponse.FullTokenData,
        _:WSResponse => Boolean,
        _:String,
        _:IntegrationModuleType,
        _:String,
        _:String
      )(
        _:WSClient,
        _:ExecutionContext,
        _:SRLogger
      ))
        .expects(
          CRMFixtures.fullAccessTokenData.copy(api_domain = Some("https://zohoapis.in")),
          *,
          phoneCriteria,
          mockModule,
          mockApiDomain,
          "Contacts",
          *,
          *,
          *
        )
        .returning(Future.successful(Left(FindBatchError.CommonCRMAPIError(CommonCRMAPIErrors.UnknownErrorWithResponseBody(
          msg = "An Error Occurred", responseBody = "response", responseCode = 404
        )))))


      val result = zohoOAuth.findAllByEmailsAndPhones(
        CRMFixtures.fullAccessTokenData.copy(api_domain = Some("https://zohoapis.in")),
        Seq("<EMAIL>", "<EMAIL>"),
        Seq("1234567890", "9876543210"),
        IntegrationModuleType.CONTACTS,
        "https://zohoapis.in",
        "Contacts"

      )

      result.map {
        case Left(error) =>
          println(error)
          assert(error == FindBatchError.CommonCRMAPIError(CommonCRMAPIErrors.UnknownErrorWithResponseBody(
            msg = "An Error Occurred", responseBody = "response", responseCode = 404
          )))
        case Right(_) => assert(false)
      }
    }



  }
  describe("createOrUpdateBatchContacts "){
    it("should create or update batch contacts when both email and phone searches succeed") {
      val mockEmails = Seq("<EMAIL>", "<EMAIL>")
      val mockPhones = Seq("1234567890", "9876543210")
      val mockModule = IntegrationModuleType.CONTACTS
      val mockApiDomain = "https://zohoapis.in"

      val mockContactResponse = Seq(
        IntegrationContactResponse(email = Some("<EMAIL>"), phone = Some("1234567890"), id = "1", status = None),
        IntegrationContactResponse(email = Some("<EMAIL>"), phone = Some("9876543210"), id = "2", status = None)
      )

      val mockedWs: MockWS = MockWS {
        case (POST, _) =>
          Action {
            Results.Status(200)(Json.obj(
              "data" -> Json.arr(
                Json.obj(
                  "code" -> "SUCCESS"
                ),
                Json.obj(
                  "code" -> "SUCCESS"
                )
              )
            ))
          }
      }

      val mockProspects = Seq(
        ProspectObjectWithOldProspectDeduplicationColumn(
          prospectObject =  ProspectFixtures.prospectObject.copy(id = 101, email = Some("<EMAIL>"), phone = Some("1234567890")),
          oldProspectDeduplicationColumn = Some(OldProspectDeduplicationColumn(email = Some("<EMAIL>"), phone = Some("1234567890")))
        ),
        ProspectObjectWithOldProspectDeduplicationColumn(
          prospectObject = ProspectFixtures.prospectObject.copy(id = 102, email = Some("<EMAIL>"), phone = Some("9876543210")),
          oldProspectDeduplicationColumn = Some(OldProspectDeduplicationColumn(email = Some("<EMAIL>"), phone = Some("9876543210")))
        )
      )



      (zohoApi.findAllByCriteria(
        _: IntegrationTPAccessTokenResponse.FullTokenData,
        _:WSResponse => Boolean,
        _:String,
        _:IntegrationModuleType,
        _:String,
        _:String
      )(
        _:WSClient,
        _:ExecutionContext,
        _:SRLogger
      ))
        .expects(*, *, *, mockModule, mockApiDomain,*, *, *, *)
        .returning(Future.successful(Right(mockContactResponse)))

      (zohoApi.findAllByCriteria(
        _: IntegrationTPAccessTokenResponse.FullTokenData,
        _:WSResponse => Boolean,
        _:String,
        _:IntegrationModuleType,
        _:String,
        _:String
      )(
        _:WSClient,
        _:ExecutionContext,
        _:SRLogger
      ))
        .expects(*, *, *, mockModule, mockApiDomain,*, *, *, *)
        .returning(Future.successful(Right(mockContactResponse)))


      


      val result = zohoOAuth.createOrUpdateBatchContacts(
        moduleType = mockModule,
        accessTokenData = CRMFixtures.fullAccessTokenData.copy(api_domain = Some(mockApiDomain)),
        contacts = mockProspects,
        fieldMapping = CRMFixtures.fieldMappingForm,
        userMapping = CRMFixtures.userMappingForm,
        accountId = 1L,
        teamId = 1L,
      )(
        ws = mockedWs,
        ec = executionContext,
        system = system,
        Logger = Logger)

      result.map { res =>
        assert(res.forall(_.isRight)) // Ensure all contacts are processed successfully
      }
    }

    it("should return left if email returned Left") {

      (zohoApi.findAllByCriteria(
        _: IntegrationTPAccessTokenResponse.FullTokenData,
        _: WSResponse => Boolean,
        _: String,
        _: IntegrationModuleType,
        _: String,
        _: String
      )(
        _: WSClient,
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(*, *, *, IntegrationModuleType.CONTACTS, "https://zohoapis.in",*, *, *, *)
        .returning(Future.successful(Left(FindBatchError.MalformedResponseStructureError("Some Unexpected Response"))))




      val mockProspects = Seq(
        ProspectObjectWithOldProspectDeduplicationColumn(
          prospectObject = ProspectFixtures.prospectObject.copy(id = 101, email = Some("<EMAIL>"), phone = None),
          oldProspectDeduplicationColumn = Some(OldProspectDeduplicationColumn(email = Some("<EMAIL>"), phone = None))
        ),
        ProspectObjectWithOldProspectDeduplicationColumn(
          prospectObject = ProspectFixtures.prospectObject.copy(id = 102, email = Some("<EMAIL>"), phone = None),
          oldProspectDeduplicationColumn = Some(OldProspectDeduplicationColumn(email = Some("<EMAIL>"), phone = None))
        )
      )

      val result = zohoOAuth.createOrUpdateBatchContacts(
        moduleType = IntegrationModuleType.CONTACTS,
        accessTokenData = CRMFixtures.fullAccessTokenData.copy(api_domain = Some("https://zohoapis.in")),
        contacts = Seq(mockProspects.head),
        fieldMapping = CRMFixtures.fieldMappingForm,
        userMapping = CRMFixtures.userMappingForm,
        accountId = 1L,
        teamId = 1L,
         //emailNotCompulsoryEnabled = true
      )

      result.map { res =>
        println(s"res is ${res} :: right ${res.count(_.isRight)} left ${res.count(_.isLeft)}")
        assert(res.count(_.isRight) == 0) // Expect one contact processed successfully
        assert(res.count(_.isLeft) == 1)  // Expect one contact failed
      }
    }





  }

  describe("Testing ZohoOAuth.extractQueryParams") {

    it("should return only email if phone is invalid") {
      val prospect = ProspectFixtures.prospectObject.copy(email = Some("<EMAIL>"), phone = Some("invalid_phone"))
      val result = ZohoOAuth.extractQueryParams(prospect)
      assert(result == Seq("email" -> "<EMAIL>"))
    }

    it("should return only phone if email is None and phone is valid") {
      val prospect = ProspectFixtures.prospectObject.copy(email = None, phone = Some("+************"))
      val result = ZohoOAuth.extractQueryParams(prospect)
      assert(result == Seq("phone" -> "+************"))
    }

    it("should return both email and phone if both are valid") {
      val prospect =  ProspectFixtures.prospectObject.copy(email = Some("<EMAIL>"), phone = Some("+************"))
      val result = ZohoOAuth.extractQueryParams(prospect)
      assert(result == Seq(
        "email" -> "<EMAIL>",
        "phone" -> "+************"
      ))
    }

    it("should return an empty sequence if both email and phone are invalid") {
      val prospect = ProspectFixtures.prospectObject.copy(email = None, phone = Some("invalid_phone"))
      val result = ZohoOAuth.extractQueryParams(prospect)
      assert(result.isEmpty)
    }

    it("should handle None values gracefully") {
      val prospect = ProspectFixtures.prospectObject.copy(email = None, phone = None)
      val result = ZohoOAuth.extractQueryParams(prospect)
      assert(result.isEmpty)
    }
    

    it("should not include phone if it's an empty string or invalid") {
      val prospect = ProspectFixtures.prospectObject.copy(email = Some("<EMAIL>"), phone = Some("Mobile"))
      val result = ZohoOAuth.extractQueryParams(prospect)
      assert(result == Seq("email" -> "<EMAIL>"))
    }
  }


}

