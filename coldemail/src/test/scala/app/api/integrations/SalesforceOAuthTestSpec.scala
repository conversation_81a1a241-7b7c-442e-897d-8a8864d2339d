package app.api.integrations

import org.apache.pekko.actor.ActorSystem
import api.accounts.TeamId
import api.integrations.CommonCRMAPIErrors.{TooManyRequestsError, UnAuthorizedError, UnknownErrorWithResponseBody}
import api.integrations.CreateOrUpdateBatchContactsError.{CommonCRMAPIError, InvalidModuleError}
import api.integrations.crmapis.SalesForceApi
import api.integrations.{BatchContactResponse, CommonCRMAPIErrors, CreateOrUpdateBatchContactsError, IntegrationTPAccessTokenResponse, SalesforceOAuth, SearchBatchContactsError}
import api.prospects.models.ProspectId
import api.sr_audit_logs.models.ProspectObjectWithOldProspectDeduplicationColumn
import api.triggers.IntegrationModuleType.{CANDIDATES, CONTACTS}
import api.triggers.dao_service.TriggerDAOService
import api.triggers.{IntegrationModuleType, IntegrationType, Trigger, TriggerServiceV2, UpdateFieldsMappingForm, UpdateUserMappingForm}
import app.test_fixtures.crm.CRMFixtures
import app.test_fixtures.prospect.ProspectFixtures
import eventframework.ProspectObject
import mockws.MockWS
import mockws.MockWSHelpers.Action
import org.scalamock.scalatest.AsyncMockFactory
import org.scalatest.funspec.AsyncFunSpec
import play.api.http.HeaderNames
import play.api.http.HttpVerbs.{GET, POST}
import play.api.libs.json.{JsValue, Json}
import play.api.libs.ws.WSClient
import play.api.libs.ws.ahc.AhcWSClient
import play.api.mvc.Results
import play.api.test.Helpers.PATCH
import utils.SRLogger
import utils.mq.webhook.model.CreateInCRMJedisDAO
import utils.testapp.TestAppExecutionContext

import scala.concurrent.{ExecutionContext, ExecutionContextExecutor, Future}

class SalesforceOAuthTestSpec  extends  AsyncFunSpec  with AsyncMockFactory {

  val triggerDAO: Trigger = mock[Trigger]
  val triggerServiceV2: TriggerServiceV2 = mock[TriggerServiceV2]
  val triggerDAOService: TriggerDAOService = mock[TriggerDAOService]

  val createInCRMJedisDAO: CreateInCRMJedisDAO = mock[CreateInCRMJedisDAO]

  val salesforceApi: SalesForceApi = mock[SalesForceApi]

  val salesforceOAuth = new SalesforceOAuth(
    triggerDAO = triggerDAO,
    triggerServiceV2 = triggerServiceV2,
    triggerDAOService = triggerDAOService,
    createInCRMJedisDAO = createInCRMJedisDAO,
    salesforceApi = salesforceApi
  )
  implicit lazy val system: ActorSystem = TestAppExecutionContext.actorSystem
  implicit lazy val ec: ExecutionContextExecutor = system.dispatcher
  implicit lazy val wSClient: AhcWSClient = TestAppExecutionContext.wsClient
  given Logger: SRLogger = new SRLogger("[SalesforceOAuthTestSpec]")

  describe("SalesForceOAuth.CreateOrUpdateBatchContacts"){

    it("should return Unauthorized Error when response from api is Error "){

      (salesforceApi.batchSearchForProspects(
        _: IntegrationModuleType,
        _: IntegrationTPAccessTokenResponse.FullTokenData,
        _: Seq[ProspectObject],
      )(
       _: WSClient,
      _: ExecutionContext,
      _: ActorSystem,
      _: SRLogger
      ))
        .expects(*,*,*,*,*,*,*)
        .returning(Future.successful(Left(SearchBatchContactsError.CommonCRMAPIError(CommonCRMAPIErrors.UnAuthorizedError("Unauthorized error")))))



      salesforceOAuth.createOrUpdateBatchContacts(
        moduleType = CONTACTS,
        accessTokenData = CRMFixtures.fullAccessTokenData,
        contacts = Seq(ProspectObjectWithOldProspectDeduplicationColumn(
          prospectObject = ProspectFixtures.prospectObject,
          oldProspectDeduplicationColumn = None
        )),
        fieldMapping = CRMFixtures.fieldMappingForm,
        userMapping = CRMFixtures.userMappingForm,
        accountId = 1L,
        teamId = 599L,
         //emailNotCompulsoryEnabled = true
      ).map(items => {

        println(s"items are ${items}")

        items.head match {

            case Left(value) => {
              println(value)
              assert(value == CreateOrUpdateBatchContactsError.CommonCRMAPIError(UnAuthorizedError("Unauthorized error")))
            }
            case Right(value) => {
              println(value)
              assert(false)
            }
        }

      })
    }


    it("should return Invalid Module Error when response from api is Error "){

      (salesforceApi.batchSearchForProspects(
        _: IntegrationModuleType,
        _: IntegrationTPAccessTokenResponse.FullTokenData,
        _: Seq[ProspectObject],
      )(
        _: WSClient,
        _: ExecutionContext,
        _: ActorSystem,
        _: SRLogger
      ))
        .expects(*,*,*,*,*,*,*)
        .returning(Future.successful(Left(SearchBatchContactsError.InvalidModuleError("Invalid Module Error"))))



      salesforceOAuth.createOrUpdateBatchContacts(
        moduleType = CANDIDATES,
        accessTokenData = CRMFixtures.fullAccessTokenData,
        contacts = Seq(ProspectObjectWithOldProspectDeduplicationColumn(
          prospectObject = ProspectFixtures.prospectObject,
          oldProspectDeduplicationColumn = None
        )),
        fieldMapping = CRMFixtures.fieldMappingForm,
        userMapping = CRMFixtures.userMappingForm,
        accountId = 1L,
        teamId = 599L,
         //emailNotCompulsoryEnabled = true
      ).map(items => {

        println(s"items are ${items}")

        items.head match {

          case Left(value) => {
            println(value)
            assert(value == CreateOrUpdateBatchContactsError.InvalidModuleError("Invalid Module Error"))
          }
          case Right(value) => {
            println(value)
            assert(false)
          }
        }

      })
    }

    it("should return Not Found Error when response from api is Error "){

      (salesforceApi.batchSearchForProspects(
        _: IntegrationModuleType,
        _: IntegrationTPAccessTokenResponse.FullTokenData,
        _: Seq[ProspectObject],
      )(
        _: WSClient,
        _: ExecutionContext,
        _: ActorSystem,
        _: SRLogger
      ))
        .expects(*,*,*,*,*,*,*)
        .returning(Future.successful(Left(SearchBatchContactsError.CommonCRMAPIError(CommonCRMAPIErrors.NotFoundError("Not Found error")))))



      salesforceOAuth.createOrUpdateBatchContacts(
        moduleType = CONTACTS,
        accessTokenData = CRMFixtures.fullAccessTokenData,
        contacts = Seq(ProspectObjectWithOldProspectDeduplicationColumn(
          prospectObject = ProspectFixtures.prospectObject,
          oldProspectDeduplicationColumn = None
        )),
        fieldMapping = CRMFixtures.fieldMappingForm,
        userMapping = CRMFixtures.userMappingForm,
        accountId = 1L,
        teamId = 599L,
         //emailNotCompulsoryEnabled = true
      ).map(items => {

        items.head match {

          case Left(value) => {
            println(value)
            assert(value == CommonCRMAPIError(CommonCRMAPIErrors.NotFoundError("Not Found error")))
          }
          case Right(value) => {
            println(value)
            assert(false)
          }
        }

      })
    }

    it("should return Left of Bad Request Error as POST response was Bad Request ") {

      (salesforceApi.batchSearchForProspects(
        _: IntegrationModuleType,
        _: IntegrationTPAccessTokenResponse.FullTokenData,
        _: Seq[ProspectObject],
      )(
        _: WSClient,
        _: ExecutionContext,
        _: ActorSystem,
        _: SRLogger
      ))
        .expects(*,*,*,*,*,*,*)
        .returning(Future.successful(Right(
          Seq(Some(BatchContactResponse(
            email = ProspectFixtures.prospectObject.email,
            phone =  ProspectFixtures.prospectObject.phone,
            linkedinUrl = ProspectFixtures.prospectObject.linkedin_url,
            error = None,
            contactId = Some("ContactId")
          )))
        )))


      (salesforceApi.updateContactInSalesforce(
        _: Seq[JsValue],
        _: String,
        _: String,
        _: IntegrationTPAccessTokenResponse.FullTokenData,
//        _: Boolean,
        _: SRLogger
      )(
        _:WSClient,
        _:ExecutionContext
      ))
        .expects(*,*,*,*,*,*,*)
        .returning(Future.successful(
          Seq(Left(CommonCRMAPIError(UnknownErrorWithResponseBody("The request couldn’t be understood, usually because the JSON or XML body contains an error.","Creation of Contact failed because of bad request",400))))
        ))

      val mockedWs: MockWS = MockWS {
        case (POST, _)  => Action {
              Results.BadRequest("Creation of Contact failed because of bad request")
            }
      }



      salesforceOAuth.createOrUpdateBatchContacts(
        moduleType = CONTACTS,
        accessTokenData = CRMFixtures.fullAccessTokenData.copy(api_domain =Some("https://api.salesforce.com")),
        contacts = Seq(ProspectObjectWithOldProspectDeduplicationColumn(
          prospectObject = ProspectFixtures.prospectObject,
          oldProspectDeduplicationColumn = None
        )),
        fieldMapping = CRMFixtures.fieldMappingForm,
        userMapping = CRMFixtures.userMappingForm,
        accountId = 1L,
        teamId = 599L,
         //emailNotCompulsoryEnabled = true
      )(
        ws = mockedWs,
        ec = ec,
        system = system,
        Logger = Logger
      ).map(items => {
        println(s"items are ${items}")


        items.head match {

          case Left(value) => {
            println(value)
            assert(value == CommonCRMAPIError(UnknownErrorWithResponseBody("The request couldn’t be understood, usually because the JSON or XML body contains an error.","Creation of Contact failed because of bad request",400)))
          }
          case Right(value) => {
            println(value)
            assert(false)
          }
        }

      })
    }


    it("should return Left of Too Many Requests as POST response was Bad Request ") {

      (salesforceApi.batchSearchForProspects(
        _: IntegrationModuleType,
        _: IntegrationTPAccessTokenResponse.FullTokenData,
        _: Seq[ProspectObject],
      )(
        _: WSClient,
        _: ExecutionContext,
        _: ActorSystem,
        _: SRLogger
      ))
        .expects(*,*,*,*,*,*,*)
        .returning(Future.successful(Right(
          Seq(Some(BatchContactResponse(
            email = ProspectFixtures.prospectObject.email,
            phone =  ProspectFixtures.prospectObject.phone,
            linkedinUrl = ProspectFixtures.prospectObject.linkedin_url,
            error = None,
            contactId = Some("ContactId")
          )))
        )))

      (salesforceApi.updateContactInSalesforce(
        _: Seq[JsValue],
        _: String,
        _: String,
        _: IntegrationTPAccessTokenResponse.FullTokenData,
//        _: Boolean,
        _: SRLogger
      )(
        _:WSClient,
        _:ExecutionContext
      ))
        .expects(*,*,*,*,*,*,*)
        .returning(Future.successful(
          Seq(Left(CommonCRMAPIError(TooManyRequestsError("The request has been refused. Verify that the logged-in user has appropriate permissions. If the error code is REQUEST_LIMIT_EXCEEDED, you’ve exceeded API request limits in your org.",None)))
        )))


      val mockedWs: MockWS = MockWS {
        case (POST, _)  => Action {
          Results.Status(403)("Creation of Contact failed because of too many request")
        }
      }



      salesforceOAuth.createOrUpdateBatchContacts(
        moduleType = CONTACTS,
        accessTokenData = CRMFixtures.fullAccessTokenData.copy(api_domain =Some("https://api.salesforce.com")),
        contacts = Seq(ProspectObjectWithOldProspectDeduplicationColumn(
          prospectObject = ProspectFixtures.prospectObject,
          oldProspectDeduplicationColumn = None
        )),
        fieldMapping = CRMFixtures.fieldMappingForm,
        userMapping = CRMFixtures.userMappingForm,
        accountId = 1L,
        teamId = 599L,
         //emailNotCompulsoryEnabled = true
      )(
        ws = mockedWs,
        ec = ec,
        system = system,
        Logger = Logger
      ).map(items => {

        items.head match {

          case Left(value) => {
            println(value)
            assert(value == CommonCRMAPIError(TooManyRequestsError("The request has been refused. Verify that the logged-in user has appropriate permissions. If the error code is REQUEST_LIMIT_EXCEEDED, you’ve exceeded API request limits in your org.",None)))
          }
          case Right(value) => {
            println(value)
            assert(false)
          }
        }

      })
    }

    it("should return success and go via updation flow") {

      (salesforceApi.batchSearchForProspects(
        _: IntegrationModuleType,
        _: IntegrationTPAccessTokenResponse.FullTokenData,
        _: Seq[ProspectObject],
      )(
        _: WSClient,
        _: ExecutionContext,
        _: ActorSystem,
        _: SRLogger
      ))
        .expects(*,*,*,*,*,*,*)
        .returning(Future.successful(Right(
          Seq(Some(BatchContactResponse(
            email = ProspectFixtures.prospectObject.email,
            phone =  ProspectFixtures.prospectObject.phone,
            linkedinUrl = ProspectFixtures.prospectObject.linkedin_url,
            error = None,
            contactId = Some("ContactId")
          )))
        )))

      (salesforceApi.updateContactInSalesforce(
        _: Seq[JsValue],
        _: String,
        _: String,
        _: IntegrationTPAccessTokenResponse.FullTokenData,
//        _: Boolean,
        _: SRLogger
      )(
        _:WSClient,
        _:ExecutionContext
      ))
        .expects(*,*,*,*,*,*,*)
        .returning(Future.successful(
          Seq(Right(BatchContactResponse(Some("email"),Some("phone"),None,None,None))
          )))


      val mockedWs: MockWS = MockWS {
        case (POST, _)  => Action {
          Results.Status(200)(Json.obj(
            "compositeResponse" -> Json.arr(Json.obj(
              "Email" ->Json.toJson(ProspectFixtures.prospectObject.email.getOrElse("")),
              "phone" ->Json.toJson(ProspectFixtures.prospectObject.phone.getOrElse("")),
              "httpStatusCode" -> 200
            ))
          ))
        }
      }



      salesforceOAuth.createOrUpdateBatchContacts(
        moduleType = CONTACTS,
        accessTokenData = CRMFixtures.fullAccessTokenData.copy(api_domain =Some("https://api.salesforce.com")),
        contacts = Seq(ProspectObjectWithOldProspectDeduplicationColumn(
          prospectObject = ProspectFixtures.prospectObject,
          oldProspectDeduplicationColumn = None
        )),
        fieldMapping = CRMFixtures.fieldMappingForm,
        userMapping = CRMFixtures.userMappingForm,
        accountId = 1L,
        teamId = 599L,
         //emailNotCompulsoryEnabled = true
      )(
        ws = mockedWs,
        ec = ec,
        system = system,
        Logger = Logger
      ).map(items => {

        println(s"items are ${items}")

        items.head match {

          case Left(value) => {
            println(value)
            assert(false)
          }
          case Right(value) => {
            println(value)
            assert(value == BatchContactResponse(Some("email"),Some("phone"),None,None,None))
          }
        }

      })
    }

    it("should return success and go via creation flow") {

      (salesforceApi.batchSearchForProspects(
        _: IntegrationModuleType,
        _: IntegrationTPAccessTokenResponse.FullTokenData,
        _: Seq[ProspectObject],
      )(
        _: WSClient,
        _: ExecutionContext,
        _: ActorSystem,
        _: SRLogger
      ))
        .expects(*,*,*,*,*,*,*)
        .returning(Future.successful(Right(
          Seq(None)
        )))


      (salesforceApi.createContactInSalesforce(
        _: Seq[JsValue],
        _: String,
        _: String,
        _: IntegrationTPAccessTokenResponse.FullTokenData,
        _: TeamId,
        _:IntegrationModuleType,
        _: SRLogger
      )(
        _:WSClient,
        _:ExecutionContext
      ))
        .expects(*,*,*,*,*,*,*,*,*)
        .returning(Future.successful(
          Seq(Right( BatchContactResponse(Some("email"),Some("phone"),None,None,None))
          )))

//      (createInCRMJedisDAO.getLock(_:ProspectId, _: IntegrationType , _: TeamId , _:IntegrationModuleType)(_:SRLogger))
//        .expects(*,*,*,*,*)
//        .atLeastOnce()
//        .returning(false)

      (createInCRMJedisDAO.acquireLockForProspect(_:ProspectId, _: IntegrationType , _: TeamId , _:IntegrationModuleType)(using _:SRLogger))
        .expects(*,*,*,*,*)
        .atLeastOnce()
        .returning(true)


      val mockedWs: MockWS = MockWS {
        case (POST, _)  => Action {
          Results.Status(200)(Json.obj(
            "compositeResponse" -> Json.arr(Json.obj(
              "Email" ->Json.toJson(ProspectFixtures.prospectObject.email.getOrElse("")),
              "phone" ->Json.toJson(ProspectFixtures.prospectObject.phone.getOrElse("")),
              "httpStatusCode" -> 200
            ))
          ))
        }
      }



      salesforceOAuth.createOrUpdateBatchContacts(
        moduleType = CONTACTS,
        accessTokenData = CRMFixtures.fullAccessTokenData.copy(api_domain =Some("https://api.salesforce.com")),
        contacts =Seq(ProspectObjectWithOldProspectDeduplicationColumn(
          prospectObject = ProspectFixtures.prospectObject,
          oldProspectDeduplicationColumn = None
        )),
        fieldMapping = CRMFixtures.fieldMappingForm,
        userMapping = CRMFixtures.userMappingForm,
        accountId = 1L,
        teamId = 599L,
         //emailNotCompulsoryEnabled = true
      )(
        ws = mockedWs,
        ec = ec,
        system = system,
        Logger = Logger
      ).map(items => {

        items.head match {

          case Left(value) => {
            println(value)
            assert(false)
          }
          case Right(value) => {
            println(value)
            assert(value == BatchContactResponse(Some("email"),Some("phone"),None,None,None))
          }
        }

      })
    }



  }





}
