package app.api.integrations.crmapis

import org.apache.pekko.actor.ActorSystem
import api.integrations.{BatchContactResponse, BatchContactResponseTypes, CommonCRMAPIErrors}
import api.integrations.CreateOrUpdateBatchContactsError.CommonCRMAPIError
import api.integrations.crmapis.HubspotApi
import mockws.MockWS
import mockws.MockWSHelpers.Action
import org.scalamock.scalatest.AsyncMockFactory
import org.scalatest.funspec.AsyncFunSpec
import play.api.http.HeaderNames
import play.api.libs.json.{JsVal<PERSON>, Json}
import play.api.libs.ws.WSResponse
import play.api.libs.ws.ahc.AhcWSClient
import play.api.mvc.{AnyContent, Request, Results}
import play.api.test.Helpers.{PATCH, POST}
import utils.SRLogger
import utils.testapp.TestAppExecutionContext

import scala.concurrent.{ExecutionContext, Future}

class HubspotApiSpec  extends  Async<PERSON>unSpec  with AsyncMockFactory {


  def isSuccessResponse(response: WSResponse): Boolean = response.status >= 200 && response.status < 300

  implicit lazy val system: ActorSystem = TestAppExecutionContext.actorSystem
  implicit lazy val ec: ExecutionContext = system.dispatcher
  implicit lazy val wSClient: AhcWSClient = TestAppExecutionContext.wsClient
  given Logger: SRLogger= new SRLogger("[HubspotApiSpec]")

  describe("Hubspot.createContactInHubspot"){

    it("should return left as response was 403"){

      val hubspotApi = new HubspotApi
      val resp = Action.async { (req: Request[AnyContent]) =>
        Future.successful(
          Results.Forbidden(
            Json.obj("message" -> "Forbidden Error")
          ).withHeaders((HeaderNames.CONTENT_TYPE.toString, "application/json") , "X-HubSpot-RateLimit-Interval-Milliseconds" -> "60000")
        )
      }

      val ws: MockWS = MockWS {
        case (POST, "https://api.hubapi.com/crm/v3/objects/contacts") =>  resp
      }

      val contact = Json.obj(
        "properties" -> Json.obj(
          "email" -> "email",
          "firstname" -> "first_name",
          "lastname"  -> "last_name",
          "phone" -> "phone"
        ),
        "prospectId" -> 1
      )
      hubspotApi.createContactInHubspot(
        contact  =contact ,
        accessToken = "access_token",
        isSuccessResponse= isSuccessResponse
      )(
        ws = ws,
        ec = executionContext,
        Logger = Logger
      ).map {
        case Left(value) => {
          println(value)
          assert(value == CommonCRMAPIError(CommonCRMAPIErrors.UnknownError(msg ="Forbidden Error")))
        }
        case Right(value) => {
          assert(false)
        }
      }
    }


    it("should return left as response was 401"){

      val hubspotApi = new HubspotApi
      val resp = Action.async { (req: Request[AnyContent]) =>
        Future.successful(
          Results.Status(401)(
            Json.obj("message" -> "Unauthorized Error")
          ).withHeaders((HeaderNames.CONTENT_TYPE.toString, "application/json") , "X-HubSpot-RateLimit-Interval-Milliseconds" -> "60000")
        )
      }

      val ws: MockWS = MockWS {
        case (POST, "https://api.hubapi.com/crm/v3/objects/contacts") =>  resp
      }

      val contact = Json.obj(
        "properties" -> Json.obj(
          "email" -> "email",
          "firstname" -> "first_name",
          "lastname"  -> "last_name",
          "phone" -> "phone"
        ),
        "prospectId" -> 1
      )
      hubspotApi.createContactInHubspot(
        contact  =contact ,
        accessToken = "access_token",
        isSuccessResponse= isSuccessResponse
      )(
        ws = ws,
        ec = executionContext,
        Logger = Logger
      ).map {
        case Left(value) => {
          println(value)
          assert(value == CommonCRMAPIError(CommonCRMAPIErrors.UnAuthorizedError(msg ="Unauthorized Error")))
        }
        case Right(value) => {
          assert(false)
        }
      }
    }


    it("should return Success as response was 200"){

      val hubspotApi = new HubspotApi
      val resp = Action.async { (req: Request[AnyContent]) =>
        Future.successful(
          Results.Status(200)(
            Json.obj("message" -> "Success")
          ).withHeaders((HeaderNames.CONTENT_TYPE.toString, "application/json") , "X-HubSpot-RateLimit-Interval-Milliseconds" -> "60000")
        )
      }

      val ws: MockWS = MockWS {
        case (POST, "https://api.hubapi.com/crm/v3/objects/contacts") =>  resp
      }

      val contact = Json.obj(
        "properties" -> Json.obj(
          "email" -> "email",
          "firstname" -> "first_name",
          "lastname"  -> "last_name",
          "phone" -> "phone"
        ),
        "prospectId" -> 1
      )
      hubspotApi.createContactInHubspot(
        contact  =contact ,
        accessToken = "access_token",
        isSuccessResponse= isSuccessResponse
      )(
        ws = ws,
        ec = executionContext,
        Logger = Logger
      ).map {
        case Left(value) => {
          println(value)
          assert(false)
        }
        case Right(value) => {
          assert(value == BatchContactResponse(
            email = Some("email"),
            phone = Some("phone"),
            linkedinUrl  =None,
            error = None
          ))
        }
      }
    }



  }


  describe("Hubspot.updateContactInHubspot"){

    it("should return left as response was 403"){

      val hubspotApi = new HubspotApi
      val resp = Action.async { (req: Request[AnyContent]) =>
        Future.successful(
          Results.Forbidden(
            Json.obj("message" -> "Forbidden Error")
          ).withHeaders((HeaderNames.CONTENT_TYPE.toString, "application/json") , "X-HubSpot-RateLimit-Interval-Milliseconds" -> "60000")
        )
      }

      val ws: MockWS = MockWS {
        case (PATCH, _) =>  resp
      }

      val contact = Json.obj(
        "properties" -> Json.obj(
          "email" -> "email",
          "firstname" -> "first_name",
          "lastname"  -> "last_name",
          "phone" -> "phone"
        ),
        "prospectId" -> 1,
        "contactId" -> "1"
      )
      hubspotApi.updateContactInHubspot(
        contact  =contact ,
        accessToken = "access_token",
        isSuccessResponse= isSuccessResponse
      )(
        ws = ws,
        ec = executionContext,
        Logger = Logger
      ).map {
        case Left(value) => {
          println(value)
          assert(value == CommonCRMAPIError(CommonCRMAPIErrors.UnknownError(msg ="Forbidden Error")))
        }
        case Right(value) => {
          assert(false)
        }
      }
    }


    it("should return left as response was 401"){

      val hubspotApi = new HubspotApi
      val resp = Action.async { (req: Request[AnyContent]) =>
        Future.successful(
          Results.Status(401)(
            Json.obj("message" -> "Unauthorized Error")
          ).withHeaders((HeaderNames.CONTENT_TYPE.toString, "application/json") , "X-HubSpot-RateLimit-Interval-Milliseconds" -> "60000")
        )
      }

      val ws: MockWS = MockWS {
        case (PATCH, _) =>  resp
      }

      val contact = Json.obj(
        "properties" -> Json.obj(
          "email" -> "email",
          "firstname" -> "first_name",
          "lastname"  -> "last_name",
          "phone" -> "phone"
        ),
        "prospectId" -> 1,
        "contactId" -> "1"
      )
      hubspotApi.updateContactInHubspot(
        contact  =contact ,
        accessToken = "access_token",
        isSuccessResponse= isSuccessResponse
      )(
        ws = ws,
        ec = executionContext,
        Logger = Logger
      ).map {
        case Left(value) => {
          println(value)
          assert(value == CommonCRMAPIError(CommonCRMAPIErrors.UnAuthorizedError(msg ="Unauthorized Error")))
        }
        case Right(value) => {
          assert(false)
        }
      }
    }


    it("should return Success as response was 200"){

      val hubspotApi = new HubspotApi
      val resp = Action.async { (req: Request[AnyContent]) =>
        Future.successful(
          Results.Status(200)(
            Json.obj("message" -> "Success")
          ).withHeaders((HeaderNames.CONTENT_TYPE.toString, "application/json") , "X-HubSpot-RateLimit-Interval-Milliseconds" -> "60000")
        )
      }

      val ws: MockWS = MockWS {
        case (PATCH, _) =>  resp
      }

      val contact = Json.obj(
        "properties" -> Json.obj(
          "email" -> "email",
          "firstname" -> "first_name",
          "lastname"  -> "last_name",
          "phone" -> "phone"
        ),
        "prospectId" -> 1,
        "contactId" -> "1"
      )
      hubspotApi.updateContactInHubspot(
        contact  =contact ,
        accessToken = "access_token",
        isSuccessResponse= isSuccessResponse
      )(
        ws = ws,
        ec = executionContext,
        Logger = Logger
      ).map {
        case Left(value) => {
          println(value)
          assert(false)
        }
        case Right(value) => {
          assert(value == BatchContactResponse(
            email = Some("email"),
            phone = Some("phone"),
            linkedinUrl  =None,
            error = None,
            contactId = Some("1")
          ))
        }
      }
    }



  }

}
