//package app.api.integrations
//
//import akka.actor.ActorSystem
//import akka.util.ByteString
//import api.accounts.TeamId
//import api.columns.ProspectColumnDef
//import api.integrations.CommonCRMAPIErrors.{ContactCreationDelayInCRM, NotFoundError, UnknownError}
//import api.integrations.CreateOrUpdateBatchContactsError.CommonCRMAPIError
//import api.integrations.{BatchContactResponse, CommonCRMAPIErrors, CreateOrUpdateBatchContactsError, IntegrationTPAccessTokenResponse, PipedriveOAuth}
//import api.integrations.crmapis.PipedriveApi
//import api.prospects.models.ProspectId
//import api.sr_audit_logs.models.ProspectObjectWithOldProspectDeduplicationColumn
//import api.triggers.dao_service.TriggerDAOService
//import api.triggers.{IntegrationModuleType, IntegrationType, Trigger, TriggerServiceV2, UpdateFieldsMappingForm, UpdateUserMappingForm}
//import app.test_fixtures.crm.CRMFixtures
//import app.test_fixtures.prospect.ProspectFixtures
//import eventframework.ProspectObject
//import mockws.MockWS
//import mockws.MockWSHelpers.Action
//import org.scalamock.scalatest.AsyncMockFactory
//import org.scalatest.funspec.AsyncFunSpec
//import play.api.http.HeaderNames
//import play.api.libs.json.Json
//import play.api.libs.ws.ahc.AhcWSClient
//import play.api.mvc.{RequestHeader, Results}
//import play.api.http.HttpVerbs.{GET, POST}
//import utils.SRLogger
//import utils.mq.webhook.model.CreateInCRMJedisDAO
//
//import scala.concurrent.Future
//
//class PipedriveOAuthUnitTestSpec  extends  AsyncFunSpec  with AsyncMockFactory {
//
//  describe("PipedriveOAuth.createOrUpdateBatchContacts"){
//
//
//
//    val prospectColumnDef: ProspectColumnDef = mock[ProspectColumnDef]
//    val triggerDAO: Trigger = mock[Trigger]
//    val triggerServiceV2: TriggerServiceV2 = mock[TriggerServiceV2]
//    val triggerDAOService: TriggerDAOService= mock[TriggerDAOService]
//    val createInCRMJedisDAO: CreateInCRMJedisDAO = mock[CreateInCRMJedisDAO]
//    val pipedriveApi: PipedriveApi = mock[PipedriveApi]
//
//    val pipedriveOAuth = new PipedriveOAuth(
//      prospectColumnDef = prospectColumnDef,
//      triggerDAO = triggerDAO,
//      triggerServiceV2 = triggerServiceV2,
//      triggerDAOService = triggerDAOService,
//      createInCRMJedisDAO = createInCRMJedisDAO,
//      pipedriveApi  = pipedriveApi
//    )
//
//    given Logger: SRLogger= new SRLogger("[PipedriveOAuthUnitTestSpec]")
//
//    it("should return left of invalid module as module type candidate is not supported") {
//
//      pipedriveOAuth.createOrUpdateBatchContacts(
//
//        moduleType = IntegrationModuleType.CANDIDATES,
//        accessTokenData = CRMFixtures.fullAccessTokenData,
//        contacts= Seq(ProspectObjectWithOldProspectDeduplicationColumn(
//          prospectObject = ProspectFixtures.prospectObject,
//          oldProspectDeduplicationColumn = None
//        )),
//        fieldMapping = CRMFixtures.fieldMappingForm,
//        userMapping = CRMFixtures.userMappingForm,
//        accountId = 1L,
//        teamId = 599L,
//         //emailNotCompulsoryEnabled = true
//
//      ).map(items =>{
//
//        items.head match {
//
//
//          case Left(value) => {
//            println(value)
//
//            value match {
//              case CreateOrUpdateBatchContactsError.InvalidModuleError(message) => assert(true)
//              case _ => assert(false)
//
//            }
//          }
//          case Right(value) =>{
//            assert(false)
//          }
//        }
//
//      })
//    }
//
//    it("should catch the error in the batch contact response and log it, and send the success data"){
//
//
//      var callCountPost = 0
//      val mockedWs: MockWS = MockWS {
//        case (GET, _) =>
//          Action {
//            Results.Forbidden("")
//          }
//
//
//        case (POST, _) => {
//          callCountPost += 1
//          callCountPost match {
//            case 1 => Action {
//              Results.Status(200)(
//                Json.obj("data" -> Json.obj("items" -> Json.arr()))
//              ).withHeaders((HeaderNames.CONTENT_TYPE.toString, "application/json"))
//            }
//
//            case 2 => Action {
//              Results.BadRequest("Creation of Contact failed because of bad request")
//            }
//            //              case _ => Action{
//            //                Results.NotFound
//            //              }
//          }
//
//
//        }
//      }
//
//
//
//      (createInCRMJedisDAO.getLock(_:ProspectId, _: IntegrationType , _: TeamId , _:IntegrationModuleType)(_:SRLogger))
//        .expects(*,*,*,*,*)
//        .atLeastOnce()
//        .returning(false)
//
//      (createInCRMJedisDAO.acquireLockForProspect(_:ProspectId, _: IntegrationType , _: TeamId , _:IntegrationModuleType)(_:SRLogger))
//        .expects(*,*,*,*,*)
//        .atLeastOnce()
//        .returning(true)
//
//      pipedriveOAuth.createOrUpdateBatchContacts(
//
//        moduleType = IntegrationModuleType.CONTACTS,
//        accessTokenData = CRMFixtures.fullAccessTokenData.copy(api_domain = Some("https://api.pipedrive.com")),
//        contacts= Seq(ProspectObjectWithOldProspectDeduplicationColumn(
//          prospectObject = ProspectFixtures.prospectObject,
//          oldProspectDeduplicationColumn = None
//        )),
//        fieldMapping = CRMFixtures.fieldMappingForm,
//        userMapping = CRMFixtures.userMappingForm,
//        accountId = 1L,
//        teamId = 599L,
//         //emailNotCompulsoryEnabled = true
//
//      )(
//        ws = mockedWs,
//        ec = ec,
//        system = system,
//        Logger = Logger
//      ).map(items =>{
//
//        println(s"items are ${items}")
//
//        items.head match {
//
//
//          case Left(value) => {
//            value match {
//              case CreateOrUpdateBatchContactsError.CommonCRMAPIError(err) => err match {
//
//                case CommonCRMAPIErrors.UnknownErrorWithResponseBody(msg, responseBody, responseCode) => {
//                  println(msg)
//                  assert(false)
//
//                }
//                case _ => assert(false)
//              }
//              case _ => assert(false)
//
//            }
//          }
//          case Right(value) =>{
//            println(value)
//            assert(value == BatchContactResponse(Some("email"),Some("phone"),Some("linkedin_url"),None,None))
//          }
//        }
//
//      })
//
//    }
//
//
//    it("should catch the Bad request Error in the batch contact response and log it , and send only the success contacts"){
//
//      var callCountPost = 0
//      val mockedWs: MockWS = MockWS {
//        case (GET, _) =>
//          Action {
//            Results.BadRequest("You sent a bad request")
//          }
//
//
//        case (POST, _) => {
//          callCountPost += 1
//          callCountPost match {
//            case 1 => Action {
//              Results.Status(200)(
//                Json.obj("data" -> Json.obj("items" -> Json.arr()))
//              ).withHeaders((HeaderNames.CONTENT_TYPE.toString, "application/json"))
//            }
//
//            case 2 => Action {
//              Results.BadRequest("Creation of Contact failed because of bad request")
//            }
//            //              case _ => Action{
//            //                Results.NotFound
//            //              }
//          }
//
//
//        }
//      }
//
//
//
//      (createInCRMJedisDAO.getLock(_:ProspectId, _: IntegrationType , _: TeamId , _:IntegrationModuleType)(_:SRLogger))
//        .expects(*,*,*,*,*)
//        .atLeastOnce()
//        .returning(false)
//
//      (createInCRMJedisDAO.acquireLockForProspect(_:ProspectId, _: IntegrationType , _: TeamId , _:IntegrationModuleType)(_:SRLogger))
//        .expects(*,*,*,*,*)
//        .atLeastOnce()
//        .returning(true)
//
//
//      pipedriveOAuth.createOrUpdateBatchContacts(
//
//        moduleType = IntegrationModuleType.CONTACTS,
//        accessTokenData = CRMFixtures.fullAccessTokenData.copy(api_domain = Some("https://api.pipedrive.com")),
//        contacts= Seq(ProspectObjectWithOldProspectDeduplicationColumn(
//          prospectObject = ProspectFixtures.prospectObject,
//          oldProspectDeduplicationColumn = None
//        )),
//        fieldMapping = CRMFixtures.fieldMappingForm,
//        userMapping = CRMFixtures.userMappingForm,
//        accountId = 1L,
//        teamId = 599L,
//         //emailNotCompulsoryEnabled = true
//
//      )(
//        ws = mockedWs,
//        ec = ec,
//        system = system,
//        Logger = Logger
//      ).map(items =>{
//
//        println(s"items are ${items}")
//        items.head match {
//
//
//
//          case Left(value) => {
//            value match {
//              case CreateOrUpdateBatchContactsError.CommonCRMAPIError(err) => err match {
//
//                case CommonCRMAPIErrors.UnknownErrorWithResponseBody(msg, responseBody, responseCode) => {
//                  println(msg)
//                  assert(true)
//
//                }
//                case _ => assert(false)
//              }
//              case _ => assert(false)
//
//            }
//          }
//          case Right(value) =>{
//            assert(value == BatchContactResponse(Some("email"),Some("phone"),Some("linkedin_url"),None,None))
//          }
//        }
//
//      })
//
//    }
//
//
//    it("should catch the Not Found Error in the batch contact response and send, that is the error field for batch contact won't be empty"){
//
//
//      var callCountPost = 0
//      val mockedWs: MockWS = MockWS {
//        case (GET, _) =>
//          Action {
//            Results.NotFound("Not Found Error")
//          }
//
//
//        case (POST, _) => {
//          callCountPost += 1
//          callCountPost match {
//            case 1 => Action {
//              Results.Status(200)(
//                Json.obj("data" -> Json.obj("items" -> Json.arr()))
//              ).withHeaders((HeaderNames.CONTENT_TYPE.toString, "application/json"))
//            }
//
//            case 2 => Action {
//              Results.BadRequest("Creation of Contact failed because of bad request")
//            }
//            //              case _ => Action{
//            //                Results.NotFound
//            //              }
//          }
//
//
//        }
//      }
//
//
//
//      (createInCRMJedisDAO.getLock(_:ProspectId, _: IntegrationType , _: TeamId , _:IntegrationModuleType)(_:SRLogger))
//        .expects(*,*,*,*,*)
//        .atLeastOnce()
//        .returning(false)
//
//      (createInCRMJedisDAO.acquireLockForProspect(_:ProspectId, _: IntegrationType , _: TeamId , _:IntegrationModuleType)(_:SRLogger))
//        .expects(*,*,*,*,*)
//        .atLeastOnce()
//        .returning(true)
//      pipedriveOAuth.createOrUpdateBatchContacts(
//
//        moduleType = IntegrationModuleType.CONTACTS,
//        accessTokenData = CRMFixtures.fullAccessTokenData.copy(api_domain = Some("https://api.pipedrive.com")),
//        contacts= Seq(ProspectObjectWithOldProspectDeduplicationColumn(
//          prospectObject = ProspectFixtures.prospectObject,
//          oldProspectDeduplicationColumn = None
//        )),
//        fieldMapping = CRMFixtures.fieldMappingForm,
//        userMapping = CRMFixtures.userMappingForm,
//        accountId = 1L,
//        teamId = 599L,
//         //emailNotCompulsoryEnabled = true
//
//      )(
//        ws = mockedWs,
//        ec = ec,
//        system = system,
//        Logger = Logger
//      ).map(items =>{
//        println(s"items are ${items}")
//
//        items.head match {
//
//
//          case Left(value) => {
//            value match {
//              case CreateOrUpdateBatchContactsError.CommonCRMAPIError(err) => err match {
//
//                case CommonCRMAPIErrors.UnknownErrorWithResponseBody(msg, responseBody, responseCode) => {
//                  println(msg)
//                  assert(true)
//
//                }
//                case _ => assert(false)
//              }
//              case _ => assert(false)
//
//            }
//          }
//          case Right(value) =>{
//            assert(value == BatchContactResponse(Some("email"),Some("phone"),Some("linkedin_url"),None,None))
//          }
//        }
//
//      })
//
//    }
//
//
//    it("should catch the Too Many Requests  in the batch contact response and send, that is the error field for batch contact won't be empty"){
//
//      var callCountPost = 0
//      val mockedWs: MockWS = MockWS {
//        case (GET, _) =>
//          Action {
//            Results.TooManyRequests("Too Many Requests")
//          }
//
//
//        case (POST, _) => {
//          callCountPost += 1
//          callCountPost match {
//            case 1 => Action {
//              Results.Status(200)(
//                Json.obj("data" -> Json.obj("items" -> Json.arr()))
//              ).withHeaders((HeaderNames.CONTENT_TYPE.toString, "application/json"))
//            }
//
//            case 2 => Action {
//              Results.BadRequest("Creation of Contact failed because of bad request")
//            }
//            //              case _ => Action{
//            //                Results.NotFound
//            //              }
//          }
//
//
//        }
//      }
//
//
//
//      (createInCRMJedisDAO.getLock(_:ProspectId, _: IntegrationType , _: TeamId , _:IntegrationModuleType)(_:SRLogger))
//        .expects(*,*,*,*,*)
//        .atLeastOnce()
//        .returning(false)
//
//      (createInCRMJedisDAO.acquireLockForProspect(_:ProspectId, _: IntegrationType , _: TeamId , _:IntegrationModuleType)(_:SRLogger))
//        .expects(*,*,*,*,*)
//        .atLeastOnce()
//        .returning(true)
//
//
//      pipedriveOAuth.createOrUpdateBatchContacts(
//
//        moduleType = IntegrationModuleType.CONTACTS,
//        accessTokenData = CRMFixtures.fullAccessTokenData.copy(api_domain = Some("https://api.pipedrive.com")),
//        contacts= Seq(ProspectObjectWithOldProspectDeduplicationColumn(
//          prospectObject = ProspectFixtures.prospectObject,
//          oldProspectDeduplicationColumn = None
//        )),
//        fieldMapping = CRMFixtures.fieldMappingForm,
//        userMapping = CRMFixtures.userMappingForm,
//        accountId = 1L,
//        teamId = 599L,
//         //emailNotCompulsoryEnabled = true
//
//      )(
//        ws = mockedWs,
//        ec = ec,
//        system = system,
//        Logger = Logger
//      ).map(items =>{
//
//        println(s"items are ${items}")
//
//
//          assert(items == List(Right(BatchContactResponse(Some("email"),Some("phone"),Some("linkedin_url"),None,None))))
//      })
//
//    }
//
//
//    it("Trying with 2 prospect , it will fail because there is already a lock on prospect so result will be empty"){
//
//      (createInCRMJedisDAO.getLock(_:ProspectId, _: IntegrationType , _: TeamId , _:IntegrationModuleType)(_:SRLogger))
//        .expects(*,*,*,*,*)
//        .atLeastOnce()
//        .returning(true)
//
//      var callCount = 0
//
//      val mockedWs: MockWS = MockWS {
//        case (GET, _)  => {
//          callCount += 1
//          callCount match {
//            case 1 => Action {
//              Results.BadRequest("Too Many Requests")
//            }
//            case 2 => Action{
//              Results.Status(200)(
//                Json.obj("data" -> Json.obj("items" -> Json.arr()))
//              ).withHeaders((HeaderNames.CONTENT_TYPE.toString, "application/json") , "X-HubSpot-RateLimit-Interval-Milliseconds" -> "60000")
//            }
//            case _ => Action{
//              Results.NotFound
//            }
//          }
//
//
//        }
//      }
//      pipedriveOAuth.createOrUpdateBatchContacts(
//
//        moduleType = IntegrationModuleType.CONTACTS,
//        accessTokenData = CRMFixtures.fullAccessTokenData.copy(api_domain = Some("https://api.pipedrive.com")),
//        contacts= Seq(ProspectObjectWithOldProspectDeduplicationColumn(
//          prospectObject = ProspectFixtures.prospectObject,
//          oldProspectDeduplicationColumn = None
//        ),ProspectObjectWithOldProspectDeduplicationColumn(
//          prospectObject = ProspectFixtures.prospectObject.copy(email = Some("<EMAIL>"),id = 2L),
//          oldProspectDeduplicationColumn = None
//        )),
//
//        fieldMapping = CRMFixtures.fieldMappingForm,
//        userMapping = CRMFixtures.userMappingForm,
//        accountId = 1L,
//        teamId = 599L,
//         //emailNotCompulsoryEnabled = true
//
//      )(
//        ws = mockedWs,
//        ec = ec,
//        system = system,
//        Logger = Logger
//      ).map(items =>{
//
//        println(s"items are ${items}")
//
//        assert(items == List(Left(CommonCRMAPIError(ContactCreationDelayInCRM("Not doing batch updateOrCreate because created in last hour and its still not created in CRM"))), Left(CommonCRMAPIError(ContactCreationDelayInCRM("Not doing batch updateOrCreate because created in last hour and its still not created in CRM")))))
//
//        })
//
//    }
//
//
//
//    it("Trying with 2 prospect ,  but since creation failed we got empty result"){
//
//      (createInCRMJedisDAO.getLock(_:ProspectId, _: IntegrationType , _: TeamId , _:IntegrationModuleType)(_:SRLogger))
//        .expects(*,*,*,*,*)
//        .atLeastOnce()
//        .returning(false)
//
//      (createInCRMJedisDAO.acquireLockForProspect(_:ProspectId, _: IntegrationType , _: TeamId , _:IntegrationModuleType)(_:SRLogger))
//        .expects(*,*,*,*,*)
//        .atLeastOnce()
//        .returning(true)
//
//      (createInCRMJedisDAO.deleteLock(_:ProspectId, _: IntegrationType , _: TeamId , _:IntegrationModuleType)(_:SRLogger))
//        .expects(*,*,*,*,*)
//        .atLeastOnce()
//        .returning(true)
//
//
//      var callCount = 0
//
//      val mockedWs: MockWS = MockWS {
//        case (GET, _)  => {
//          callCount += 1
//          callCount match {
//            case 1 => Action {
//              Results.BadRequest("Too Many Requests")
//            }
//            case 2 => Action{
//              Results.Status(200)(
//                Json.obj("data" -> Json.obj("items" -> Json.arr()))
//              ).withHeaders((HeaderNames.CONTENT_TYPE.toString, "application/json") , "X-HubSpot-RateLimit-Interval-Milliseconds" -> "60000")
//            }
//            case _ => Action{
//              Results.NotFound
//            }
//          }
//
//
//        }
//      }
//      pipedriveOAuth.createOrUpdateBatchContacts(
//
//        moduleType = IntegrationModuleType.CONTACTS,
//        accessTokenData = CRMFixtures.fullAccessTokenData.copy(api_domain = Some("https://api.pipedrive.com")),
//        contacts= Seq(ProspectObjectWithOldProspectDeduplicationColumn(
//          prospectObject = ProspectFixtures.prospectObject,
//          oldProspectDeduplicationColumn = None
//        ),ProspectObjectWithOldProspectDeduplicationColumn(
//          prospectObject = ProspectFixtures.prospectObject.copy(email = Some("<EMAIL>"),id = 2L),
//          oldProspectDeduplicationColumn = None
//        )),
//        fieldMapping = CRMFixtures.fieldMappingForm,
//        userMapping = CRMFixtures.userMappingForm,
//        accountId = 1L,
//        teamId = 599L,
//         //emailNotCompulsoryEnabled = true
//
//      )(
//        ws = mockedWs,
//        ec = ec,
//        system = system,
//        Logger = Logger
//      ).map(items =>{
//
//        println(s"items are ${items}")
//
//        assert(items == List(Left(CommonCRMAPIError(NotFoundError("Resource unavailable"))), Left(CommonCRMAPIError(NotFoundError("Resource unavailable")))))
//
//      })
//
//    }
//
//    it("Trying with 2 prospect , one will fail other will succeed so there should be one contact in the result response"){
//
//      (createInCRMJedisDAO.getLock(_:ProspectId, _: IntegrationType , _: TeamId , _:IntegrationModuleType)(_:SRLogger))
//        .expects(*,*,*,*,*)
//        .atLeastOnce()
//        .returning(false)
//
//      (createInCRMJedisDAO.acquireLockForProspect(_:ProspectId, _: IntegrationType , _: TeamId , _:IntegrationModuleType)(_:SRLogger))
//        .expects(*,*,*,*,*)
//        .atLeastOnce()
//        .returning(true)
//
//      (createInCRMJedisDAO.deleteLock(_:ProspectId, _: IntegrationType , _: TeamId , _:IntegrationModuleType)(_:SRLogger))
//        .expects(*,*,*,*,*)
//        .atLeastOnce()
//        .returning(true)
//
//
//      var callCountPost = 0
//
//      val mockedWs: MockWS = MockWS {
//        case (GET, _)  => Action {
//              Results.Status(200)(
//                Json.obj("data" -> Json.obj("items" -> Json.arr()))
//              ).withHeaders((HeaderNames.CONTENT_TYPE.toString, "application/json"))
//          }
//
//
//
//          case (POST, _)  => {
//            callCountPost += 1
//            callCountPost match {
//              case 1 => Action {
//                Results.Status(200)(
//                  Json.obj("data" -> Json.obj("items" -> Json.arr()))
//                ).withHeaders((HeaderNames.CONTENT_TYPE.toString, "application/json"))
//              }
//
//              case 2 => Action {
//                Results.BadRequest("Creation of Contact failed because of bad request")
//              }
//              //              case _ => Action{
//              //                Results.NotFound
//              //              }
//            }
//
//
//          }
//      }
//      pipedriveOAuth.createOrUpdateBatchContacts(
//
//        moduleType = IntegrationModuleType.CONTACTS,
//        accessTokenData = CRMFixtures.fullAccessTokenData.copy(api_domain = Some("https://api.pipedrive.com")),
//        contacts= Seq(ProspectObjectWithOldProspectDeduplicationColumn(
//          prospectObject = ProspectFixtures.prospectObject,
//          oldProspectDeduplicationColumn = None
//        ),ProspectObjectWithOldProspectDeduplicationColumn(
//          prospectObject = ProspectFixtures.prospectObject.copy(email = Some("<EMAIL>"),id = 2L),
//          oldProspectDeduplicationColumn = None
//        )),
//        fieldMapping = CRMFixtures.fieldMappingForm,
//        userMapping = CRMFixtures.userMappingForm,
//        accountId = 1L,
//        teamId = 599L,
//         //emailNotCompulsoryEnabled = true
//
//      )(
//        ws = mockedWs,
//        ec = ec,
//        system = system,
//        Logger = Logger
//      ).map(items =>{
//
//        println(s"items are ${items}")
//
//
//        assert(items == List(Right(BatchContactResponse(Some("email"),Some("phone"),Some("linkedin_url"),None,None)), Left(CommonCRMAPIError(UnknownError("Request not understood")))))
//
//        })
//
//    }
//
//
//
//    it("Trying with 2 prospect , contact creation succeeded for both"){
//
//      (createInCRMJedisDAO.getLock(_:ProspectId, _: IntegrationType , _: TeamId , _:IntegrationModuleType)(_:SRLogger))
//        .expects(*,*,*,*,*)
//        .atLeastOnce()
//        .returning(false)
//
//      (createInCRMJedisDAO.acquireLockForProspect(_:ProspectId, _: IntegrationType , _: TeamId , _:IntegrationModuleType)(_:SRLogger))
//        .expects(*,*,*,*,*)
//        .atLeastOnce()
//        .returning(true)
//
//
//
//
//      var callCountPost = 0
//
//      val mockedWs: MockWS = MockWS {
//        case (GET, _)  => Action {
//          Results.Status(200)(
//            Json.obj("data" -> Json.obj("items" -> Json.arr()))
//          ).withHeaders((HeaderNames.CONTENT_TYPE.toString, "application/json"))
//        }
//
//
//
//        case (POST, _)  => {
//          callCountPost += 1
//          callCountPost match {
//            case _ => Action {
//              Results.Status(200)(
//                Json.obj("data" -> Json.obj("items" -> Json.arr()))
//              ).withHeaders((HeaderNames.CONTENT_TYPE.toString, "application/json"))
//            }
//          }
//
//
//        }
//      }
//      pipedriveOAuth.createOrUpdateBatchContacts(
//
//        moduleType = IntegrationModuleType.CONTACTS,
//        accessTokenData = CRMFixtures.fullAccessTokenData.copy(api_domain = Some("https://api.pipedrive.com")),
//        contacts= Seq(ProspectObjectWithOldProspectDeduplicationColumn(
//          prospectObject = ProspectFixtures.prospectObject,
//          oldProspectDeduplicationColumn = None
//        ),ProspectObjectWithOldProspectDeduplicationColumn(
//          prospectObject = ProspectFixtures.prospectObject.copy(email = Some("<EMAIL>"),id = 2L),
//          oldProspectDeduplicationColumn = None
//        )),
//        fieldMapping = CRMFixtures.fieldMappingForm,
//        userMapping = CRMFixtures.userMappingForm,
//        accountId = 1L,
//        teamId = 599L,
//         //emailNotCompulsoryEnabled = true
//
//      )(
//        ws = mockedWs,
//        ec = ec,
//        system = system,
//        Logger = Logger
//      ).map(items =>{
//        println(s"items are ${items}")
//
//        items.head match {
//
//
//          case Left(value) => {
//            value match {
//              case CreateOrUpdateBatchContactsError.CommonCRMAPIError(err) => err match {
//
//                case CommonCRMAPIErrors.UnknownErrorWithResponseBody(msg, responseBody, responseCode) => {
//                  println(msg)
//                  assert(true)
//
//                }
//                case _ => assert(false)
//              }
//              case _ => assert(false)
//
//            }
//          }
//          case Right(value) =>{
//            assert(value == BatchContactResponse(Some("email"),Some("phone"),Some("linkedin_url"),None,None))
//          }
//        }
//
//      })
//
//    }
//
//    it(" Trying with 2 prospect , i am using one prospect which doesnot have email so only the email prospect must get created"){
//
//      (createInCRMJedisDAO.getLock(_:ProspectId, _: IntegrationType , _: TeamId , _:IntegrationModuleType)(_:SRLogger))
//        .expects(*,*,*,*,*)
//        .atLeastOnce()
//        .returning(false)
//
//      (createInCRMJedisDAO.acquireLockForProspect(_:ProspectId, _: IntegrationType , _: TeamId , _:IntegrationModuleType)(_:SRLogger))
//        .expects(*,*,*,*,*)
//        .atLeastOnce()
//        .returning(true)
//
//
//
//
//      var callCountPost = 0
//
//      val mockedWs: MockWS = MockWS {
//        case (GET, _)  => Action {
//          Results.Status(200)(
//            Json.obj("data" -> Json.obj("items" -> Json.arr()))
//          ).withHeaders((HeaderNames.CONTENT_TYPE.toString, "application/json"))
//        }
//
//
//
//        case (POST, _)  => {
//          callCountPost += 1
//          callCountPost match {
//            case _ => Action {
//              Results.Status(200)(
//                Json.obj("data" -> Json.obj("items" -> Json.arr()))
//              ).withHeaders((HeaderNames.CONTENT_TYPE.toString, "application/json"))
//            }
//          }
//
//
//        }
//      }
//      pipedriveOAuth.createOrUpdateBatchContacts(
//
//        moduleType = IntegrationModuleType.CONTACTS,
//        accessTokenData = CRMFixtures.fullAccessTokenData.copy(api_domain = Some("https://api.pipedrive.com")),
//        contacts= Seq(ProspectObjectWithOldProspectDeduplicationColumn(
//          prospectObject = ProspectFixtures.prospectObject,
//          oldProspectDeduplicationColumn = None
//        ),ProspectObjectWithOldProspectDeduplicationColumn(
//          prospectObject = ProspectFixtures.prospectObject.copy(email = Some("<EMAIL>"),id = 2L),
//          oldProspectDeduplicationColumn = None
//        )),
//        fieldMapping = CRMFixtures.fieldMappingForm,
//        userMapping = CRMFixtures.userMappingForm,
//        accountId = 1L,
//        teamId = 599L,
//         //emailNotCompulsoryEnabled = true
//
//      )(
//        ws = mockedWs,
//        ec = ec,
//        system = system,
//        Logger = Logger
//      ).map(items =>{
//        println(s"items are ${items}")
//
//        items.head match {
//
//
//          case Left(value) => {
//            value match {
//              case CreateOrUpdateBatchContactsError.CommonCRMAPIError(err) => err match {
//
//                case CommonCRMAPIErrors.UnknownErrorWithResponseBody(msg, responseBody, responseCode) => {
//                  println(msg)
//                  assert(true)
//
//                }
//                case _ => assert(false)
//              }
//              case _ => assert(false)
//
//            }
//          }
//          case Right(value) =>{
//            assert(value == BatchContactResponse(Some("email"),Some("phone"),Some("linkedin_url"),None,None))
//          }
//        }
//
//      })
//
//    }
//
//
//
//
//    /**************************************** EmailNotCompulsoryEnabled **************************************************/
//
//
//    /* NOTE:
//    7 Nov : Commenting out this test for now  will fix it after deployment
//     */
////    it("Enc Not Enabled : Trying with 2 prospect , it will fail because there is already a lock on prospect so result will be empty"){
////
////      (createInCRMJedisDAO.getLock(_:ProspectId, _: IntegrationType , _: TeamId , _:IntegrationModuleType)(_:SRLogger))
////        .expects(*,*,*,*,*)
////        .atLeastOnce()
////        .returning(true)
////
////      var callCount = 0
////
////      val mockedWs: MockWS = MockWS {
////        case (GET, _)  => {
////          callCount += 1
////          callCount match {
////            case 1 => Action {
////              Results.BadRequest("Too Many Requests")
////            }
////            case 2 => Action{
////              Results.Status(200)(
////                Json.obj("data" -> Json.obj("items" -> Json.arr()))
////              ).withHeaders((HeaderNames.CONTENT_TYPE.toString, "application/json") , "X-HubSpot-RateLimit-Interval-Milliseconds" -> "60000")
////            }
////            case _ => Action{
////              Results.NotFound
////            }
////          }
////
////
////        }
////      }
////      pipedriveOAuth.createOrUpdateBatchContacts(
////
////        moduleType = IntegrationModuleType.CONTACTS,
////        accessTokenData = CRMFixtures.fullAccessTokenData.copy(api_domain = Some("https://api.pipedrive.com")),
////        contacts= Seq(ProspectObjectWithOldProspectDeduplicationColumn(
////          prospectObject = ProspectFixtures.prospectObject,
////          oldProspectDeduplicationColumn = None
////        ),ProspectObjectWithOldProspectDeduplicationColumn(
////          prospectObject = ProspectFixtures.prospectObject.copy(email = Some("<EMAIL>"),id = 2L),
////          oldProspectDeduplicationColumn = None
////        )),
////        fieldMapping = CRMFixtures.fieldMappingForm,
////        userMapping = CRMFixtures.userMappingForm,
////        accountId = 1L,
////        teamId = 599L,
////        emailNotCompulsoryEnabled = false
////
////      )(
////        ws = mockedWs,
////        ec = ec,
////        system = system,
////        Logger = Logger
////      ).map(items =>{
////        println(s"items are ${items}")
////
////        assert(items == List(Left(CommonCRMAPIError(UnknownError("CommonCRMAPIError.UnknownErrorWithResponseBody.Request not understood:: responseBody: Too Many Requests:: responseCode: 400"))), Left(CommonCRMAPIError(ContactCreationDelayInCRM("Not doing batch updateOrCreate because created in last hour and its still not created in CRM")))))
////
////      })
////    }
//
//
//
//    it("Enc Not Enabled :Trying with 2 prospect ,  but since creation failed we got empty result"){
//
//      (createInCRMJedisDAO.getLock(_:ProspectId, _: IntegrationType , _: TeamId , _:IntegrationModuleType)(_:SRLogger))
//        .expects(*,*,*,*,*)
//        .atLeastOnce()
//        .returning(false)
//
//      (createInCRMJedisDAO.acquireLockForProspect(_:ProspectId, _: IntegrationType , _: TeamId , _:IntegrationModuleType)(_:SRLogger))
//        .expects(*,*,*,*,*)
//        .atLeastOnce()
//        .returning(true)
//
//      (createInCRMJedisDAO.deleteLock(_:ProspectId, _: IntegrationType , _: TeamId , _:IntegrationModuleType)(_:SRLogger))
//        .expects(*,*,*,*,*)
//        .atLeastOnce()
//        .returning(true)
//
//
//      var callCount = 0
//
//      val mockedWs: MockWS = MockWS {
//        case (GET, _)  => {
//          callCount += 1
//          callCount match {
//            case 1 => Action {
//              Results.BadRequest("Too Many Requests")
//            }
//            case 2 => Action{
//              Results.Status(200)(
//                Json.obj("data" -> Json.obj("items" -> Json.arr()))
//              ).withHeaders((HeaderNames.CONTENT_TYPE.toString, "application/json") , "X-HubSpot-RateLimit-Interval-Milliseconds" -> "60000")
//            }
//            case _ => Action{
//              Results.NotFound
//            }
//          }
//
//
//        }
//      }
//      pipedriveOAuth.createOrUpdateBatchContacts(
//
//        moduleType = IntegrationModuleType.CONTACTS,
//        accessTokenData = CRMFixtures.fullAccessTokenData.copy(api_domain = Some("https://api.pipedrive.com")),
//        contacts= Seq(ProspectObjectWithOldProspectDeduplicationColumn(
//          prospectObject = ProspectFixtures.prospectObject,
//          oldProspectDeduplicationColumn = None
//        ),ProspectObjectWithOldProspectDeduplicationColumn(
//          prospectObject = ProspectFixtures.prospectObject.copy(email = Some("<EMAIL>"),id = 2L),
//          oldProspectDeduplicationColumn = None
//        )),
//        fieldMapping = CRMFixtures.fieldMappingForm,
//        userMapping = CRMFixtures.userMappingForm,
//        accountId = 1L,
//        teamId = 599L,
//        emailNotCompulsoryEnabled = false
//
//      )(
//        ws = mockedWs,
//        ec = ec,
//        system = system,
//        Logger = Logger
//      ).map(items =>{
//        println(s"items are ${items}")
//
//        assert(items == List(Left(CommonCRMAPIError(UnknownError("CommonCRMAPIError.UnknownErrorWithResponseBody.Request not understood:: responseBody: Too Many Requests:: responseCode: 400"))), Left(CommonCRMAPIError(NotFoundError("Resource unavailable")))))
//
//      })
//
//    }
//
//    it("Enc Not Enabled: Trying with 2 prospect , one will fail other will succeed so there should be one contact in the result response"){
//
//      (createInCRMJedisDAO.getLock(_:ProspectId, _: IntegrationType , _: TeamId , _:IntegrationModuleType)(_:SRLogger))
//        .expects(*,*,*,*,*)
//        .atLeastOnce()
//        .returning(false)
//
//      (createInCRMJedisDAO.acquireLockForProspect(_:ProspectId, _: IntegrationType , _: TeamId , _:IntegrationModuleType)(_:SRLogger))
//        .expects(*,*,*,*,*)
//        .atLeastOnce()
//        .returning(true)
//
//      (createInCRMJedisDAO.deleteLock(_:ProspectId, _: IntegrationType , _: TeamId , _:IntegrationModuleType)(_:SRLogger))
//        .expects(*,*,*,*,*)
//        .atLeastOnce()
//        .returning(true)
//
//
//      var callCountPost = 0
//
//      val mockedWs: MockWS = MockWS {
//        case (GET, _)  => Action {
//          Results.Status(200)(
//            Json.obj("data" -> Json.obj("items" -> Json.arr()))
//          ).withHeaders((HeaderNames.CONTENT_TYPE.toString, "application/json"))
//        }
//
//
//
//        case (POST, _)  => {
//          callCountPost match {
//            case 0 => {
//              callCountPost += 1
//              Action {
//                Results.Status(200)(
//                  Json.obj("data" -> Json.obj("items" -> Json.arr()))
//                ).withHeaders((HeaderNames.CONTENT_TYPE.toString, "application/json"))
//              }
//            }
//
//            case _ => Action {
//              Results.BadRequest("Creation of Contact failed because of bad request")
//            }
//            //              case _ => Action{
//            //                Results.NotFound
//            //              }
//          }
//
//
//        }
//      }
//      pipedriveOAuth.createOrUpdateBatchContacts(
//
//        moduleType = IntegrationModuleType.CONTACTS,
//        accessTokenData = CRMFixtures.fullAccessTokenData.copy(api_domain = Some("https://api.pipedrive.com")),
//        contacts= Seq(ProspectObjectWithOldProspectDeduplicationColumn(
//          prospectObject = ProspectFixtures.prospectObject,
//          oldProspectDeduplicationColumn = None
//        ),ProspectObjectWithOldProspectDeduplicationColumn(
//          prospectObject = ProspectFixtures.prospectObject.copy(email = Some("<EMAIL>"),id = 2L),
//          oldProspectDeduplicationColumn = None
//        )),
//        fieldMapping = CRMFixtures.fieldMappingForm,
//        userMapping = CRMFixtures.userMappingForm,
//        accountId = 1L,
//        teamId = 599L,
//        emailNotCompulsoryEnabled = false
//
//      )(
//        ws = mockedWs,
//        ec = ec,
//        system = system,
//        Logger = Logger
//      ).map(items =>{
//        println(s"items are ${items}")
//
//        items.head match {
//
//
//          case Left(value) => {
//            println(s"left value is ${value}")
//            value match {
//              case CreateOrUpdateBatchContactsError.CommonCRMAPIError(err) => err match {
//
//                case CommonCRMAPIErrors.UnknownErrorWithResponseBody(msg, responseBody, responseCode) => {
//                  println(msg)
//                  assert(true)
//
//                }
//                case _ => assert(false)
//              }
//              case _ => assert(false)
//
//            }
//          }
//          case Right(value) =>{
//            println(s"Value is ${value}")
//            assert(value == BatchContactResponse(Some("email"),Some("phone"),Some("linkedin_url"),None,None))
//          }
//        }
//
//      })
//
//    }
//
//    it("Enc Not Enabled : Trying with 2 prospect , contact creation succeeded for both"){
//
//      (createInCRMJedisDAO.getLock(_:ProspectId, _: IntegrationType , _: TeamId , _:IntegrationModuleType)(_:SRLogger))
//        .expects(*,*,*,*,*)
//        .atLeastOnce()
//        .returning(false)
//
//      (createInCRMJedisDAO.acquireLockForProspect(_:ProspectId, _: IntegrationType , _: TeamId , _:IntegrationModuleType)(_:SRLogger))
//        .expects(*,*,*,*,*)
//        .atLeastOnce()
//        .returning(true)
//
//
//
//
//      var callCountPost = 0
//
//      val mockedWs: MockWS = MockWS {
//        case (GET, _)  => Action {
//          Results.Status(200)(
//            Json.obj("data" -> Json.obj("items" -> Json.arr()))
//          ).withHeaders((HeaderNames.CONTENT_TYPE.toString, "application/json"))
//        }
//
//
//
//        case (POST, _)  => {
//          callCountPost += 1
//          callCountPost match {
//            case _ => Action {
//              Results.Status(200)(
//                Json.obj("data" -> Json.obj("items" -> Json.arr()))
//              ).withHeaders((HeaderNames.CONTENT_TYPE.toString, "application/json"))
//            }
//          }
//
//
//        }
//      }
//      pipedriveOAuth.createOrUpdateBatchContacts(
//
//        moduleType = IntegrationModuleType.CONTACTS,
//        accessTokenData = CRMFixtures.fullAccessTokenData.copy(api_domain = Some("https://api.pipedrive.com")),
//        contacts= Seq(ProspectObjectWithOldProspectDeduplicationColumn(
//          prospectObject = ProspectFixtures.prospectObject,
//          oldProspectDeduplicationColumn = None
//        ),ProspectObjectWithOldProspectDeduplicationColumn(
//          prospectObject = ProspectFixtures.prospectObject.copy(email = Some("<EMAIL>"),id = 2L),
//          oldProspectDeduplicationColumn = None
//        )),
//        fieldMapping = CRMFixtures.fieldMappingForm,
//        userMapping = CRMFixtures.userMappingForm,
//        accountId = 1L,
//        teamId = 599L,
//        emailNotCompulsoryEnabled = false
//
//      )(
//        ws = mockedWs,
//        ec = ec,
//        system = system,
//        Logger = Logger
//      ).map(items =>{
//        println(s"items are ${items}")
//
//        items.head match {
//
//
//          case Left(value) => {
//            value match {
//              case CreateOrUpdateBatchContactsError.CommonCRMAPIError(err) => err match {
//
//                case CommonCRMAPIErrors.UnknownErrorWithResponseBody(msg, responseBody, responseCode) => {
//                  println(msg)
//                  assert(true)
//
//                }
//                case _ => assert(false)
//              }
//              case _ => assert(false)
//
//            }
//          }
//          case Right(value) =>{
//            assert(value == BatchContactResponse(Some("email"),Some("phone"),Some("linkedin_url"),None,None))
//          }
//        }
//
//      })
//
//    }
//
//    it("Enc Not Enabled : Trying with 2 prospect , i am using one prospect which doesnot have email so both prospect must get created"){
//
//      (createInCRMJedisDAO.getLock(_:ProspectId, _: IntegrationType , _: TeamId , _:IntegrationModuleType)(_:SRLogger))
//        .expects(*,*,*,*,*)
//        .atLeastOnce()
//        .returning(false)
//
//      (createInCRMJedisDAO.acquireLockForProspect(_:ProspectId, _: IntegrationType , _: TeamId , _:IntegrationModuleType)(_:SRLogger))
//        .expects(*,*,*,*,*)
//        .atLeastOnce()
//        .returning(true)
//
//
//
//
//      var callCountPost = 0
//
//      val mockedWs: MockWS = MockWS {
//        case (GET, _)  => Action {
//          Results.Status(200)(
//            Json.obj("data" -> Json.obj("items" -> Json.arr()))
//          ).withHeaders((HeaderNames.CONTENT_TYPE.toString, "application/json"))
//        }
//
//
//
//        case (POST, _)  => {
//          callCountPost += 1
//          callCountPost match {
//            case _ => Action {
//              Results.Status(200)(
//                Json.obj("data" -> Json.obj("items" -> Json.arr()))
//              ).withHeaders((HeaderNames.CONTENT_TYPE.toString, "application/json"))
//            }
//          }
//
//
//        }
//      }
//      pipedriveOAuth.createOrUpdateBatchContacts(
//
//        moduleType = IntegrationModuleType.CONTACTS,
//        accessTokenData = CRMFixtures.fullAccessTokenData.copy(api_domain = Some("https://api.pipedrive.com")),
//        contacts= Seq(ProspectObjectWithOldProspectDeduplicationColumn(
//          prospectObject = ProspectFixtures.prospectObject,
//          oldProspectDeduplicationColumn = None
//        ),ProspectObjectWithOldProspectDeduplicationColumn(
//          prospectObject = ProspectFixtures.prospectObject.copy(email = Some("<EMAIL>"),id = 2L),
//          oldProspectDeduplicationColumn = None
//        )),
//        fieldMapping = CRMFixtures.fieldMappingForm,
//        userMapping = CRMFixtures.userMappingForm,
//        accountId = 1L,
//        teamId = 599L,
//        emailNotCompulsoryEnabled = false
//
//      )(
//        ws = mockedWs,
//        ec = ec,
//        system = system,
//        Logger = Logger
//      ).map(items =>{
//        println(s"items are ${items}")
//
//        items.head match {
//
//
//          case Left(value) => {
//            value match {
//              case CreateOrUpdateBatchContactsError.CommonCRMAPIError(err) => err match {
//
//                case CommonCRMAPIErrors.UnknownErrorWithResponseBody(msg, responseBody, responseCode) => {
//                  println(msg)
//                  assert(true)
//
//                }
//                case _ => assert(false)
//              }
//              case _ => assert(false)
//
//            }
//          }
//          case Right(value) =>{
//            assert(value == BatchContactResponse(Some("email"),Some("phone"),Some("linkedin_url"),None,None))
//          }
//        }
//
//      })
//
//    }
//
//
//
//  }
//
//}
