package app.api.calling

import api.accounts.TeamId
import api.accounts.models.AccountId
import api.call.models.{CallStatus, CallingServiceProvider, PhoneNumber, PhoneNumberUuid}
import api.prospects.models.ProspectId
import api.twilio.service.{NewConfCallLog, ParticipantCallUpdateLog}
import api.tasks.services.TaskUuid
import db_test_spec.api.DbTestingBeforeAllAndAfterAll
import org.joda.time.DateTime
import utils.SRLogger

import scala.util.{Failure, Success}
import scala.concurrent.Future


class CallLogServiceTest extends DbTestingBeforeAllAndAfterAll{
  describe("Testing the saving of call log service layer"){
    it("Log added should be same as in db"){
      val phoneNumber="+************"
      val task_uuid=TaskUuid("UniqueTaskUUID")
      val serviceProvider = CallingServiceProvider.TWILIO
      val status = CallStatus.ACTIVE
      val prospect_id = ProspectId(1)
      val phoneNumberUuid = PhoneNumberUuid(phone_number_uuid = "randomPhoneNumberUUID")
      val teamId = TeamId(1)
      val accountId = AccountId(1)

      val phoneUuid = callDAO.addNativeCallingNumber(
        firstName = "Guru",
        lastName = "D",
        call_limit_per_day = 50,
        teamId = teamId,
        accountId = accountId,
        phone_uuid = phoneNumberUuid
      )

      assert(phoneUuid.get==phoneNumberUuid)

      val newCallLog =  NewConfCallLog(
        conference_sid = None,
        conference_name = None,
        from_phone_number = PhoneNumber(phone_number = phoneNumber),
        service_provider = serviceProvider,
        status = status,
        task_uuid = task_uuid,
        primary_prospect_id = prospect_id,
        to_phone_number = PhoneNumber(phone_number="+*************"),
        call_setting_uuid = phoneUuid.get
      )

      given Logger: SRLogger = new SRLogger("CALL LOG API TEST")
      val callLog= callService.addNewCallLog(
        newCallLog = newCallLog,
        teamId = teamId,
        accountId = accountId
      )
      callLog match {
        case Failure(err) =>
          Future.failed(new Exception(s"Error while creating the log with this request : ${err}"))
          assert(false)
        case Success(log) =>
          assert(log.conference_sid.isEmpty)
          assert(log.conference_name.isEmpty)
          assert(log.initiated_by==PhoneNumber(phone_number = phoneNumber))
          assert(log.service_provider==serviceProvider)
          assert(log.status==status)
          assert(log.task_uuid==task_uuid)
          assert(log.primary_prospect_id.get==prospect_id)

          val endLog =  ParticipantCallUpdateLog(
              call_uuid = log.conference_uuid,
              call_ended_at = DateTime.now().plusMinutes(1),
              picked_up_at = Some(DateTime.now().minusMinutes(2)),
              call_status = CallStatus.COMPLETED
          )

          val callEndLog = callService.updateCallLog(
            callUUID = endLog.call_uuid,
            completedAt = endLog.call_ended_at,
            pickedUpAt = endLog.picked_up_at,
            teamId = TeamId(1),
            callStatus = endLog.call_status
          )

          callEndLog match {
            case Left(err) =>
              Future.failed(new Exception(s"Error while updated the log with this request : ${err}"))
//              print(s"Error while updated the log with this request : ${err}")
              assert(false)
            case Right(log)=>
              assert(log==endLog)
          }
      }


      Future.successful("Validation success")
      assert(true)
    }
  }

}
