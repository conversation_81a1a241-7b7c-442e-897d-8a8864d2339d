package app.api.spamtest

import org.apache.pekko.util.Timeout
import api.AppConfig
import api.accounts.email.models.EmailServiceProvider
import api.accounts.models.{AccountId, OrgId}
import api.accounts.{AccountUuid, ReplyHandling, TeamId}
import api.campaigns.dao.InboxPlacementCheckDAO
import io.sr.billing_common.models.PlanID
import api.campaigns.{Campaign, CampaignEmailSettings, CampaignEmailSettingsUuid, CampaignSettings, CampaignStep, CampaignStepDAO}
import sr_scheduler.models.CampaignEmailPriority
import api.campaigns.{Campaign, CampaignEmailSettings, CampaignEmailSettingsUuid, CampaignSettings, CampaignStep, CampaignStepDAO, ChannelSettingUuid}
import sr_scheduler.models.CampaignEmailPriority
import sr_scheduler.CampaignStatus
import api.campaigns.models.{CallSettingSenderDetails, CampaignEmailSettingsId, CampaignStepType, CampaignType, LinkedinSettingSenderDetails, SmsSettingSenderDetails, WhatsappSettingSenderDetails}
import api.campaigns.services.CampaignId
import api.domain_health.{DomainBlacklistRecord, DomainHealthCheckDAO}
import api.emails.{EmailSetting, EmailSettingDAO}
import api.emails.models.EmailSettingUuid
import api.prospects.ProspectUuid
import api.spamtest.{CreateSpamTest, CreateSpamTestError, DomainSpamTestResult, GetSpamTestServiceError, SpamTest, SpamTestDAO, SpamTestService, SpamTestStatusType, SpamTestType}
import api.prospects.dao_service.ProspectDAOService
import api.spamtest.CreateSpamTestError.SQLException
import api.spamtest.GetSpamTestServiceError.FetchException
import api.team.TeamUuid
import app.test_fixtures.campaign_settings.{CallSettingSenderDetailsFixtures, LinkedinSettingSenderDetailsFixtures, SmsSettingSenderDetailsFixtures, WhatsappSettingSenderDetailsFixtures}
import app.test_fixtures.prospect.ProspectFixtures
import app_services.blacklist_monitoring.models.BlacklistCheckStatus
import eventframework.{ProspectObject, ProspectObjectInternal}
import io.smartreach.esp.api.emails.EmailSettingId
import org.joda.time.DateTime
import org.scalamock.scalatest.AsyncMockFactory
import org.scalatest.funspec.AsyncFunSpec
import play.api.libs.json.{JsString, JsValue, Json}
import utils.SRLogger
import org.scalatest.OneInstancePerTest
import org.scalatest.concurrent.ScalaFutures
import utils.cache_utils.model.CampaignUseStatusForEmailSetting
import utils.cronjobs.email_setting_deletion.model.EmailSettingStatus

import java.time.Clock
import scala.concurrent.Await
import scala.util.{Failure, Success}
import scala.concurrent.duration.DurationInt


class SpamTestServiceSpec extends AsyncFunSpec with AsyncMockFactory {
  val emailSettingDAO = mock[EmailSettingDAO]
  val prospectDAOService = mock[ProspectDAOService]
  val campaignStepDAO = mock[CampaignStepDAO]
  val spamTestDAO = mock[SpamTestDAO]
  val domainHealthCheckDAO = mock[DomainHealthCheckDAO]
  val inboxPlacementCheckDAO = mock[InboxPlacementCheckDAO]

  val spamTestService = new SpamTestService(
    emailSettingDAO = emailSettingDAO,
    campaignStepDAO = campaignStepDAO,
    prospectDAOService = prospectDAOService,
    spamTestDAO = spamTestDAO,
    domainHealthCheckDAO = domainHealthCheckDAO,
    inboxPlacementCheckDAO = inboxPlacementCheckDAO
  )

  val campaignSettings = CampaignSettings(
    campaign_email_settings = List(
      CampaignEmailSettings(
        campaign_id = CampaignId(1),
        sender_email_setting_id = EmailSettingId(1),
        receiver_email_setting_id = EmailSettingId(1),
        team_id = TeamId(1),
        uuid = CampaignEmailSettingsUuid("temp_setting_id"),
        id = CampaignEmailSettingsId(123),
        sender_email = "<EMAIL>",
        receiver_email = "<EMAIL>",
        max_emails_per_day_from_email_account = 400,
        signature = Some("emailsignature"),
        error = None,
        from_name = None
      )
    ),
    campaign_linkedin_settings = List(
      LinkedinSettingSenderDetailsFixtures.linkedin_setting_sender_details
    ),
    campaign_call_settings = List(
      CallSettingSenderDetailsFixtures.call_setting_sender_details
    ),
    campaign_whatsapp_settings = List(
      WhatsappSettingSenderDetailsFixtures.whatsapp_setting_sender_details
    ),
    campaign_sms_settings = List(
      SmsSettingSenderDetailsFixtures.sms_setting_sender_details
    ),
    timezone = "UTC",
    daily_from_time = 8,
    daily_till_time = 12,
    sending_holiday_calendar_id = None,
    ai_sequence_status = None,
    days_preference = List(false, true, true, true, true, false, false),
    mark_completed_after_days = 4,
    max_emails_per_day = 40,
    open_tracking_enabled = false,
    click_tracking_enabled = false,
    enable_email_validation = false,
    ab_testing_enabled = false,
    warmup_started_at = None,
    warmup_length_in_days = None,
    warmup_starting_email_count = None,
    show_soft_start_setting = false,
    schedule_start_at = None,
    schedule_start_at_tz = None,
    email_priority = CampaignEmailPriority.FIRST_EMAIL,
    append_followups = true,
    opt_out_msg = "{{unsubscribe_link}}",
    opt_out_is_text = false,
    add_prospect_to_dnc_on_opt_out = true,
    send_plain_text_email = Some(false),
    campaign_type = CampaignType.MultiChannel,
    triggers = Seq(),
    sending_mode = None,
    selected_calendar_data = None
  )
  val prospectObjectInternal = ProspectFixtures.prospectObjectInternal


  val prospectObject = ProspectObject(
    id = 1,
    owner_id = 1,
    team_id = 1,
    first_name = Some("Shubham"),
    last_name = Some("Kudekar"),
    email = Some("<EMAIL>"),
    custom_fields = Json.obj(),
    list = None,
    job_title = None,
    company = None,
    linkedin_url = None,
    phone = None,
    phone_2 = None,
    phone_3 = None,
    city = None,
    state = None,
    country = None,
    timezone = None,
    prospect_category = "Dont know this",
    last_contacted_at = None,
    last_contacted_at_phone = None,
    created_at = DateTime.now().minusMonths(5),
    internal = prospectObjectInternal,
    latest_reply_sentiment_uuid = None,
    current_step_type = None,
    latest_task_done_at = None,
    prospect_uuid = Some(ProspectUuid("prs_aa_abcdefghi")),
    owner_uuid = AccountUuid("acc_aa_abcdegfhi"),
    updated_at = DateTime.now()
  )
  val emailSetting = EmailSetting(
    id = Some(EmailSettingId(emailSettingId = 1)),
    org_id = OrgId(id = 1),
    owner_id = AccountId(id = 1),
    team_id = TeamId(id = 1),
    uuid = Some(EmailSettingUuid("test_uuid")),
    owner_uuid = AccountUuid("owner_uuid"),
    team_uuid = TeamUuid("team_uuid"),
    message_id_suffix = "Hello",
    email = "<EMAIL>",
    email_address_host = "<EMAIL>",
    service_provider = EmailServiceProvider.GMAIL_API,
      domain_provider = None,
    via_gmail_smtp = None,
    owner_name = "Shubham",
    sender_name = "Shubham Kudekar",
    first_name = "Shubham",
    last_name = "Kudekar",
    cc_emails = None,
    bcc_emails = None,
    smtp_username = None,
    smtp_password = None,
    smtp_host = None,
    smtp_port = None,
    imap_username = None,
    imap_password = None,
    imap_host = None,
    imap_port = None,
    oauth2_access_token = None,
    oauth2_refresh_token = None,
    oauth2_token_type = None,
    oauth2_token_expires_in = None,
    oauth2_access_token_expires_at = None,
    email_domain = None,
    api_key = None,
    mailgun_region = None,
    quota_per_day = 400,
    reply_handling = ReplyHandling.PAUSE_SPECIFIC_CAMPAIGN_ON_REPLY,
    last_read_for_replies = None,
    latest_email_scheduled_at = None,
    error = None,
    error_reported_at = None,
    paused_till = None,
    signature = "Shubham Kudekar",
    created_at = None,
    current_prospect_sent_count_email = 10,
    default_tracking_domain = "default_tracking_domain",
    default_unsubscribe_domain = "default_unsubscribe_domain",
    rep_tracking_host_id = 1,
    tracking_domain_host = None,
    custom_tracking_domain = None,
    custom_tracking_cname_value = None,
    custom_tracking_domain_is_verified = None,
    custom_tracking_domain_is_ssl_enabled = None,
    rep_mail_server_id = 1,
    rep_mail_server_public_ip = "rep_mail_server_public_ip",
    rep_mail_server_host = "rep_mail_server_host",
    rep_mail_server_reverse_dns = None,
    min_delay_seconds = 0,
    max_delay_seconds = 0,
      tag = None,
    campaign_use_status_for_email_setting = CampaignUseStatusForEmailSetting.IsNotAssignedToAnyCampaign,
    show_rms_ip_in_frontend = false

  )

  val campaign_uuid = s"cmp_1_cfknacskndjcn"
  val campaign = Campaign(
    id = 1L,
    uuid = Some(campaign_uuid),
    account_id = 1L,
    team_id = 1L,
    shared_with_team = true,
    name = "New Campaign 0161",
    status = CampaignStatus.RUNNING,
    head_step_id = Some(1L),
    settings = campaignSettings,
    last_scheduled_at = None,
    created_at = DateTime.now()
  )


  val campaignStep = CampaignStep(
    id = 1,
    label = None,
    campaign_id = campaign.id,
    delay = 10,
    step_type = CampaignStepType.AutoEmailStep,
    created_at = DateTime.now()
  )

  val logger = new SRLogger(logRequestId = "SpamTestServiceSpec:")
  val e = new Throwable("Sql exception occurred")


  val newSpamTest = CreateSpamTest(
    test_name = s"${AppConfig.mailtesterUsername}-${1}_${DateTime.now().getMillis}",
    test_type = SpamTestType.AUTH,
    email_settings_id = 1,
    email = "<EMAIL>",
    campaign_id = campaign.id,
    step_id = 1,
    email_domain = "smartreach.io"
  )
  val someSpamTest = SpamTest(
    id = 2,
    test_name = "someTest",
    test_type = SpamTestType.AUTH,

    email_settings_id = 1,
    email = "<EMAIL>",

    campaign_id = Some(1),
    step_id = Some(1),

    body = Some("Hi"),
    subject = Some("This subject"),

    mt_id = Some("1234"),
    mt_results = JsString("success"),
    mt_results_last_checked_at = Some(DateTime.now()),
    mt_created_at = DateTime.now(),
    error = Some("Sending failed. Your email account is not connected properly. Please reconnect, or contact support"),
    created_at = DateTime.now(),
    teamId = TeamId(1L)

  )


  describe("createSpamTest") {
    it("should fail because plan Id is Inactive") {

      val result = spamTestService.createSpamTest(
        planID = PlanID.INACTIVE,
        test_type = "auth",
        maxSpamTests = 10,
        org_Id = 12,
        sender_email_settings_id = campaign.settings.campaign_email_settings.headOption.map(_.sender_email_setting_id.emailSettingId.toInt),
        head_step_id = campaign.head_step_id,
        campaign_id = campaign.id,
        team_id = 1,
        logger = logger,
        isAfterDomainChanges = false
      )
      assert(result.isLeft)
    }
    it("should fail because test_type is not auth)") {

      val result = spamTestService.createSpamTest(
        planID = PlanID.STANDARD,
        test_type = "deliver",
        maxSpamTests = 10,
        org_Id = 12,
        sender_email_settings_id = campaign.settings.campaign_email_settings.headOption.map(_.sender_email_setting_id.emailSettingId.toInt),
        head_step_id = campaign.head_step_id,
        campaign_id = campaign.id,
        team_id = 1,
        logger = logger,
        isAfterDomainChanges = false
      )
      assert(result.isLeft)
    }
    it("should fail because spam tests done in last month is greater then maxSpamTest") {
      (spamTestDAO.getSpamTestsDoneInCurrentCycle(_: Long)).expects(*).returning(20)
      val result = spamTestService.createSpamTest(
        planID = PlanID.STANDARD,
        test_type = "auth",
        maxSpamTests = 10,
        org_Id = 12,
        sender_email_settings_id = campaign.settings.campaign_email_settings.headOption.map(_.sender_email_setting_id.emailSettingId.toInt),
        head_step_id = campaign.head_step_id,
        campaign_id = campaign.id,
        team_id = 1,
        logger = logger,
        isAfterDomainChanges = false
      )
      assert(result.isLeft)
    }

    it("campaign should fail because campaign has running test") {
      (spamTestDAO.getSpamTestsDoneInCurrentCycle(_: Long)).expects(*).returning(10)
      (spamTestDAO.campaignHasRunningTest(_: Long)).expects(1).returning(true)
      val result = spamTestService.createSpamTest(
        planID = PlanID.STANDARD,
        test_type = "auth",
        maxSpamTests = 14,
        org_Id = 12,
        sender_email_settings_id = campaign.settings.campaign_email_settings.headOption.map(_.sender_email_setting_id.emailSettingId.toInt),
        head_step_id = campaign.head_step_id,
        campaign_id = campaign.id,
        team_id = 1,
        logger = logger,
        isAfterDomainChanges = false
      )

      assert(result.isLeft)
    }
    it("should fail because sender_email_settings_id is empty") {
      (spamTestDAO.getSpamTestsDoneInCurrentCycle(_: Long)).expects(*).returning(10)
      (spamTestDAO.campaignHasRunningTest(_: Long)).expects(1).returning(false)
      val result = spamTestService.createSpamTest(
        planID = PlanID.STANDARD,
        test_type = "auth",
        maxSpamTests = 14,
        org_Id = 12,
        sender_email_settings_id = None,
        head_step_id = campaign.head_step_id,
        campaign_id = campaign.id,
        team_id = 1,
        logger = logger,
        isAfterDomainChanges = false
      )
      assert(result.isLeft)
    }
    it("should fail because no steps are added to campaign before starting a spam test") {
      (spamTestDAO.getSpamTestsDoneInCurrentCycle(_: Long)).expects(*).returning(10)
      (spamTestDAO.campaignHasRunningTest(_: Long)).expects(1).returning(false)
      val result = spamTestService.createSpamTest(
        planID = PlanID.STANDARD,
        test_type = "auth",
        maxSpamTests = 14,
        org_Id = 12,
        sender_email_settings_id = campaign.settings.campaign_email_settings.headOption.map(_.sender_email_setting_id.emailSettingId.toInt),
        head_step_id = None,
        campaign_id = campaign.id,
        team_id = 1,
        logger = logger,
        isAfterDomainChanges = false
      )
      assert(result.isLeft)
    }
    it("should fail because it returns empty prospect object") {
      (spamTestDAO.getSpamTestsDoneInCurrentCycle(_: Long)).expects(*).returning(10)
      (spamTestDAO.campaignHasRunningTest(_: Long)).expects(1).returning(false)
      (emailSettingDAO.find(_: Long, _: EmailSettingStatus)).expects(campaign.id, EmailSettingStatus.Active).returning(Some(emailSetting))
      (campaignStepDAO.findById)
        .expects(campaign.head_step_id.get, campaign.id).returning(Some(campaignStep))
      (prospectDAOService.findOneByCampaignId)
        .expects(campaign.id.toLong, 1, logger).returning(None)
      val result = spamTestService.createSpamTest(
        planID = PlanID.STANDARD,
        test_type = "auth",
        maxSpamTests = 14,
        org_Id = 12,
        sender_email_settings_id = campaign.settings.campaign_email_settings.headOption.map(_.sender_email_setting_id.emailSettingId.toInt),
        head_step_id = campaign.head_step_id,
        campaign_id = campaign.id,
        team_id = 1,
        logger = logger,
        isAfterDomainChanges = false
      )
      assert(result.isLeft)

    }

    it("should fail because Sql exception") {
      (spamTestDAO.getSpamTestsDoneInCurrentCycle(_: Long)).expects(*).returning(10)
      (spamTestDAO.campaignHasRunningTest(_: Long)).expects(1).returning(false)
      (emailSettingDAO.find(_: Long, _: EmailSettingStatus)).expects(campaign.id, EmailSettingStatus.Active).returning(Some(emailSetting))
      (campaignStepDAO.findById)
        .expects(campaign.head_step_id.get, campaign.id).returning(Some(campaignStep))
      (prospectDAOService.findOneByCampaignId)
        .expects(campaign.id.toLong, 1, logger).returning(Some(prospectObject))
      (spamTestDAO.create)
        .expects(*, *).returning(Failure(e))

      val result = spamTestService.createSpamTest(
        planID = PlanID.STANDARD,
        test_type = "auth",
        maxSpamTests = 14,
        org_Id = 12,
        sender_email_settings_id = campaign.settings.campaign_email_settings.headOption.map(_.sender_email_setting_id.emailSettingId.toInt),
        head_step_id = campaign.head_step_id,
        campaign_id = campaign.id,
        team_id = 1,
        logger = logger,
        isAfterDomainChanges = false
      )

      assert(result.isLeft)
    }
    it("should success") {
      (spamTestDAO.getSpamTestsDoneInCurrentCycle(_: Long)).expects(*).returning(10)
      (spamTestDAO.campaignHasRunningTest(_: Long)).expects(1).returning(false)
      (emailSettingDAO.find(_: Long, _: EmailSettingStatus)).expects(campaign.id, EmailSettingStatus.Active).returning(Some(emailSetting))
      (campaignStepDAO.findById)
        .expects(campaign.head_step_id.get, campaign.id).returning(Some(campaignStep))
      (prospectDAOService.findOneByCampaignId)
        .expects(campaign.id.toLong, 1, logger).returning(Some(prospectObject))
      (spamTestDAO.create)
        .expects(*, *).returning(Success(someSpamTest))

      val result = spamTestService.createSpamTest(
        planID = PlanID.STANDARD,
        test_type = "auth",
        maxSpamTests = 14,
        org_Id = 12,
        sender_email_settings_id = campaign.settings.campaign_email_settings.headOption.map(_.sender_email_setting_id.emailSettingId.toInt),
        head_step_id = campaign.head_step_id,
        campaign_id = campaign.id,
        team_id = 1,
        logger = logger,
        isAfterDomainChanges = false
      )
      assert(result.isRight)

    }

  }

  describe("getSpamTestReport asynchronously") {
    it("should fail because email setting dao was not called") {
      (spamTestDAO.findAll(_: Long)).expects(1).returning(Some(someSpamTest))
      (spamTestDAO.getSpamTestsDoneInCurrentCycle(_: Long)).expects(1).returning(10)

      spamTestService.getSpamTestReport(
        id = 1,
        maxSpamTests = -1,
        org_Id = 1
      ).map { result =>
        assert(result.isLeft)
      }

    }

    it("should success with no spam test found") {
      (spamTestDAO.findAll(_: Long)).expects(1).returning(None)
      (spamTestDAO.getSpamTestsDoneInCurrentCycle(_: Long)).expects(1).returning(10)

      spamTestService.getSpamTestReport(
        id = 1,
        maxSpamTests = 10,
        org_Id = 1
      ).map { result =>
        assert(result.isRight)
      }
    }

    it("should success") {
      (spamTestDAO.findAll(_: Long)).expects(1).returning(Some(someSpamTest))
      (spamTestDAO.getSpamTestsDoneInCurrentCycle(_: Long)).expects(1).returning(10)
      (emailSettingDAO.find(_: Long, _: EmailSettingStatus)).expects(1, EmailSettingStatus.Active).returning(Some(emailSetting))
      spamTestService.getSpamTestReport(
        id = 1,
        maxSpamTests = 10,
        org_Id = 1
      ).map { result =>
        assert(result.isRight)
      }
    }
  }

  describe("getDomainLatestCheckRecord") {
    it("should success") {

      val senderEmailList = List("<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>")


      val domainSpamTestResult1 = List(
        DomainSpamTestResult(
          domain = "example.com", lastSpamTestRanAt = None, status = SpamTestStatusType.NOT_STARTED
        ),
        DomainSpamTestResult(
          domain = "gmail.com", lastSpamTestRanAt = Some(DateTime.now()), status = SpamTestStatusType.COMPLETED
        ),
        DomainSpamTestResult(
          domain = "rediffmail.com", lastSpamTestRanAt = None, status = SpamTestStatusType.TESTING
        )
      )

      val domainBlacklistRecord = List(
        DomainBlacklistRecord("gmail.com", BlacklistCheckStatus.PASSED),
        DomainBlacklistRecord("rediffmail.com", BlacklistCheckStatus.PASSED),
        DomainBlacklistRecord("example.com", BlacklistCheckStatus.NOT_FOUND))

      (spamTestDAO.getSpamTestsDoneInCurrentCycle)
        .expects(*)
        .returning(1500)

      (emailSettingDAO.getSenderEmails)
        .expects(*, Some(CampaignId(123L)))
        .returning(Success(senderEmailList))

      (spamTestDAO.getLatestSenderDomainSpamTestRanAt)
        .expects(*, *)
        .returning(Success(domainSpamTestResult1))

      (domainHealthCheckDAO.getBlacklistResultOfDomain)
        .expects(*, *)
        .returning(Success(domainBlacklistRecord))


      val result = spamTestService.getDomainLatestCheckRecord(Some(CampaignId(123L)), OrgId(12L), TeamId(12L), 2000, logger)

      println(result)

      assert(result.isRight)
    }

    it("should fail when error occured while fetching data through email settings DAO") {

      (spamTestDAO.getSpamTestsDoneInCurrentCycle)
        .expects(*)
        .returning(1500)

      (emailSettingDAO.getSenderEmails)
        .expects(*, Some(CampaignId(981L)))
        .returning(Failure(new Exception("Some error occured while fetching in emailsetting table")))

      val result = spamTestService.getDomainLatestCheckRecord(Some(CampaignId(981L)), OrgId(12L), TeamId(34L), 2000, logger)

      println(result)

      assert(result.isLeft)
    }

    it("should fail when error occured while fetching spamtest reports") {

      val campaignSenderEmailList = List("<EMAIL>", "<EMAIL>")

      (spamTestDAO.getSpamTestsDoneInCurrentCycle)
        .expects(*)
        .returning(1500)

      (emailSettingDAO.getSenderEmails)
        .expects(*, Some(CampaignId(45L)))
        .returning(Success(campaignSenderEmailList))

      (spamTestDAO.getLatestSenderDomainSpamTestRanAt)
        .expects(*, *)
        .returning(Failure(new Exception("SpamTestDAO getLatestDomainSpamTestDate  error occured")))


      val result = spamTestService.getDomainLatestCheckRecord(Some(CampaignId(45L)), OrgId(12L), TeamId(65L), 2000, logger)

      println(result)

      assert(result.isLeft)
    }


    it("should fail when no sender emails are found for the campaign") {


      (spamTestDAO.getSpamTestsDoneInCurrentCycle)
        .expects(*)
        .returning(1500)

      (emailSettingDAO.getSenderEmails)
        .expects(*, Some(CampaignId(45L)))
        .returning(Success(List()))


      val result = spamTestService.getDomainLatestCheckRecord(Some(CampaignId(45L)), OrgId(12L), TeamId(98L), 2000, logger)

      println(result)

      assert(result.isLeft)

    }
  }


  describe("getSpecificDomainSpamTestReport") {

    it("should success") {


      val spamTestForGmail = Some(SpamTest(id = 1,
        test_name = "<EMAIL>",
        test_type = SpamTestType.AUTH,
        email_settings_id = 2,
        email = "<EMAIL>",
        campaign_id = Some(1),
        step_id = Some(1),
        body = None,
        subject = None,
        mt_id = Some("mt_id1"),
        mt_results = Json.obj(),
        mt_results_last_checked_at = Some(DateTime.now),
        mt_created_at = DateTime.now.minusMinutes(23),
        error = null,
        created_at = DateTime.now.minusMinutes(30),
        teamId = TeamId(123L)))
      (spamTestDAO.findSpamTestViaDomain)
        .expects("gmail.com", TeamId(123L))
        .returning(Success(spamTestForGmail))

      (emailSettingDAO.find(_: Long, _: EmailSettingStatus))
        .expects(*, EmailSettingStatus.Active)
        .returning(Some(emailSetting))

      val result = spamTestService.getSpecificDomainSpamTestReport("gmail.com", TeamId(123L))

      assert(result.isRight)

    }


    it("should fail for sql exception") {

      (spamTestDAO.findSpamTestViaDomain)
        .expects("gmail.com", TeamId(45L))
        .returning(Failure(new Exception("Error while fetching the reports")))

      val result = spamTestService.getSpecificDomainSpamTestReport("gmail.com", TeamId(45L))

      assert(result.isLeft)
    }
  }


}
