package app.api

import api.{ApiService, ApiVersion}
import api.accounts.{AccountDAO, TeamId}
import api.accounts.models.OrgId
import api.blacklist.BlacklistApiService
import api.call.DAO.CallDAO
import api.call.service.CallApiService
import api.campaigns.models.{CallSettingSenderDetails, CampaignEmailSettingsId, CampaignType, LinkedinSettingSenderDetails, SmsSettingSenderDetails, WhatsappSettingSenderDetails}
import api.campaigns.services.CampaignResource.{CampaignDataForApiV2, CampaignDataForApiV3, CampaignWithStatsAndEmailInternal}
import api.campaigns.{CampaignBasicInfo, CampaignEmailSettings, CampaignEmailSettingsUuid, CampaignSettings, CampaignStepDAO, CampaignWithStatsAndEmail, ChannelSettingUuid}
import api.campaigns.services.{CampaignApiError, CampaignApiService, CampaignId, CampaignResource, CampaignSettingsDataForV3}
import api.campaigns.services.CampaignResource.{CampaignBasicInfoInternal, CampaignDataForApiV2, CampaignDataForApiV2WithoutStats, CampaignDataForApiV3, CampaignWithStatsAndEmailInternal}
import api.prospects.dao_service.ProspectDAOService
import api.prospects.{ProspectAccountDAO1, ProspectEventDAO, ProspectEventService, ProspectService}
import api.prospects.service.ProspectApiService
import api.reports.{AllCampaignStats, ReplySentimentStats}
import api.tags.models.{CampaignTag, CampaignTagUuid}
import api.tasks.pgDao.TaskPgDAO
import api.tasks.services.TaskService
import api.team.service.TeamsApiService
import app.test_fixtures.campaign_settings.{CallSettingSenderDetailsFixtures, CampaignEmailSettingsSenderDetailsFixtures, LinkedinSettingSenderDetailsFixtures, WhatsappSettingSenderDetailsFixtures}
import io.smartreach.esp.api.emails.EmailSettingId
import org.joda.time.DateTime
import org.scalamock.scalatest.AsyncMockFactory
import org.scalatest.funspec.AsyncFunSpec
import sr_scheduler.CampaignStatus
import sr_scheduler.models.CampaignEmailPriority
import utils.SRLogger
import utils.uuid.services.SrUuidService

class ApiServiceSpec extends AsyncFunSpec with AsyncMockFactory {

  given logger: SRLogger = new SRLogger(logRequestId = "ApiServiceSpec: ")


  val campaignApiService: CampaignApiService = mock[CampaignApiService]
  val prospectService: ProspectService = mock[ProspectService]
  val prospectApiService: ProspectApiService = mock[ProspectApiService]
  val callApiService: CallApiService = mock[CallApiService]
  val blacklistApiService: BlacklistApiService = mock[BlacklistApiService]
  val teamsApiService: TeamsApiService = mock[TeamsApiService]
  val prospectEventService: ProspectEventService = mock[ProspectEventService]
  val prospectEventDAO: ProspectEventDAO = mock[ProspectEventDAO]
  val accountDAO: AccountDAO = mock[AccountDAO]
  val prospectDAOService: ProspectDAOService = mock[ProspectDAOService]
  val campaignStepDAO: CampaignStepDAO = mock[CampaignStepDAO]
  val srUuidService: SrUuidService = mock[SrUuidService]
  val taskService: TaskService = mock[TaskService]
  val callDAO: CallDAO = mock[CallDAO]
  val prospectAccountDAO1: ProspectAccountDAO1 = mock[ProspectAccountDAO1]

  val apiService= new ApiService(
    campaignApiService = campaignApiService,
    prospectService = prospectService,
    prospectApiService = prospectApiService,
    callApiService = callApiService,
    blacklistApiService = blacklistApiService,
    teamsApiService = teamsApiService,
    prospectEventService = prospectEventService,
    prospectEventDAO = prospectEventDAO,
    accountDAO = accountDAO,
    prospectDAOService = prospectDAOService,
    campaignStepDAO = campaignStepDAO,
    srUuidService = srUuidService,
    taskService = taskService,
    callDAO = callDAO,
    prospectAccountDAO1 = prospectAccountDAO1
  )


    val campaign_id: Long = 121L
    val campaign_name = "CampaignName"
    val permittedAccountIds = Seq(2L, 1L)
    val teamId = TeamId(id = 37L)
    val orgId: OrgId = OrgId(id = 99L)
    val ownerId: Long = 2L

    val first_name = "Adminfirst"
    val last_name = "Adminlast"
    val company = "CompanyName"
    val email = "<EMAIL>"
    val campaign_uuid = "cmp_aa_campaign_uuid"

    val aDate = DateTime.parse("2022-3-27")

    val allCampaignStats = AllCampaignStats(
      total_sent = 1,
      total_opened = 1,
      total_clicked = 1,
      total_replied = 1,
      total_steps = 1,
      current_prospects = 1,
      current_opted_out = 1,
      current_completed = 1,
      current_bounced = 1,
      current_to_check = 1,
      current_failed_email_validation = 1,
      current_in_progress = 1,
      current_unsent_prospects = 1,
      current_do_not_contact = 1,
      reply_sentiment_stats = ReplySentimentStats(
        positive = 0
      )
    )

    val campaignSettings = CampaignSettings(
      campaign_email_settings = List(
        CampaignEmailSettingsSenderDetailsFixtures.campaign_email_setting_sender_details
      ),
      campaign_linkedin_settings = List(
        LinkedinSettingSenderDetailsFixtures.linkedin_setting_sender_details
      ),
      campaign_call_settings = List(
        CallSettingSenderDetailsFixtures.call_setting_sender_details
      ),
      campaign_whatsapp_settings = List(
        WhatsappSettingSenderDetailsFixtures.whatsapp_setting_sender_details
      ),
      campaign_sms_settings = List(
      ),
      ai_sequence_status = None,
      timezone = "UTC",
      daily_from_time = 8,
      daily_till_time = 12,
      sending_holiday_calendar_id = None,
      days_preference = List(false, true, true, true, true, false, false),
      mark_completed_after_days = 4,
      max_emails_per_day = 40,
      open_tracking_enabled = false,
      click_tracking_enabled = false,
      enable_email_validation = false,
      ab_testing_enabled = false,
      warmup_started_at = None,
      warmup_length_in_days = None,
      warmup_starting_email_count = None,
      show_soft_start_setting = false,
      schedule_start_at = None,
      schedule_start_at_tz = None,
      email_priority = CampaignEmailPriority.FIRST_EMAIL,
      send_plain_text_email = Some(false),
      campaign_type = CampaignType.MultiChannel,
      append_followups = true,
      opt_out_msg = "{{unsubscribe_link}}",
      opt_out_is_text = false,
      add_prospect_to_dnc_on_opt_out = true,
      triggers = Seq(),
      sending_mode = None,
      selected_calendar_data = None
    )

    val campaignWithStatsAndEmail = CampaignWithStatsAndEmail(
      id = 121L,
      uuid = Some(campaign_uuid),
      team_id = teamId.id,
      shared_with_team = true,
      name = campaign_name,
      owner_name = first_name,
      owner_email = email,
      owner_id = ownerId,
      status = CampaignStatus.RUNNING,
      tags = Seq(CampaignTag(11L, "tag1", CampaignTagUuid("tags_abcdefgh"))),
      spam_test_exists = false,
      warmup_is_on = false,

      stats = allCampaignStats,

      head_step_id = Some(22L),

      ai_generation_context = None,

      settings = campaignSettings,

      created_at = aDate,

      error = Some("campaign error"),
      is_archived = false

    )
    val team_uuid = "team_teamuuid"

    val campaignSettingsDataForV3 : CampaignSettingsDataForV3 = CampaignSettingsDataForV3(
        timezone = campaignSettings.timezone,
        daily_from_time = campaignSettings.daily_from_time, // time since beginning of day in seconds
        daily_till_time = campaignSettings.daily_till_time, // time since beginning of day in seconds

        // Sunday is the first day
        days_preference = campaignSettings.days_preference,

        // delay between two consecutive in seconds

//        consecutive_email_delay = campaignSettings.consecutive_email_delay,
//
//        max_emails_per_day = campaignSettings.max_emails_per_day,
//        max_emails_per_day_from_email_account = campaignSettings.campaign_email_settings.headOption.map(_.max_emails_per_day_from_email_account),
//        open_tracking_enabled = campaignSettings.open_tracking_enabled,
//        click_tracking_enabled = campaignSettings.click_tracking_enabled,
//        ab_testing_enabled = campaignSettings.ab_testing_enabled,
//
//        // warm up
//        warmup_started_at = campaignSettings.warmup_started_at,
//        warmup_length_in_days = campaignSettings.warmup_length_in_days,
//        warmup_starting_email_count = campaignSettings.warmup_starting_email_count,
//
//        // schedule start
//        schedule_start_at = campaignSettings.schedule_start_at,
//        schedule_start_at_tz = campaignSettings.schedule_start_at_tz,
//
//        email_priority = campaignSettings.email_priority,
//        opt_out_msg = campaignSettings.opt_out_msg,
//        opt_out_is_text = campaignSettings.opt_out_is_text,
//        add_prospect_to_dnc_on_opt_out = campaignSettings.add_prospect_to_dnc_on_opt_out,
//        from_email = campaignSettings.campaign_email_settings.headOption.map(_.sender_email),
//        to_email = campaignSettings.campaign_email_settings.headOption.map(_.receiver_email),
//        signature = campaignSettings.campaign_email_settings.headOption.flatMap(_.signature)
    )

    val campaignDataForApiV3 = CampaignDataForApiV3(

      uuid= campaignWithStatsAndEmail.uuid,
      name = campaignWithStatsAndEmail.name,
      created_at = campaignWithStatsAndEmail.created_at,
      status = campaignWithStatsAndEmail.status,
      tags= campaignWithStatsAndEmail.tags,
      owner_name= campaignWithStatsAndEmail.owner_name,
      owner_email = campaignWithStatsAndEmail.owner_name,
      team_uuid = team_uuid,
      settings = campaignSettingsDataForV3
    )

    val campaignApiData = CampaignDataForApiV2(
      id = campaignWithStatsAndEmail.id,
      name = campaignWithStatsAndEmail.name,
      owner_id = campaignWithStatsAndEmail.owner_id,
      status = campaignWithStatsAndEmail.status,
      created_at = campaignWithStatsAndEmail.created_at,
      stats = campaignWithStatsAndEmail.stats
    )

    val campaignWithStatsAndEmailInternal: CampaignWithStatsAndEmailInternal = CampaignWithStatsAndEmailInternal(
      id = campaignWithStatsAndEmail.id,
      uuid = campaignWithStatsAndEmail.uuid,
      team_id = campaignWithStatsAndEmail.team_id,
      shared_with_team = campaignWithStatsAndEmail.shared_with_team,
      name = campaignWithStatsAndEmail.name,
      owner_name = campaignWithStatsAndEmail.owner_name,
      owner_email = campaignWithStatsAndEmail.owner_email,
      owner_id = campaignWithStatsAndEmail.owner_id,
      status = campaignWithStatsAndEmail.status,
      tags = campaignWithStatsAndEmail.tags,
      spam_test_exists = campaignWithStatsAndEmail.spam_test_exists,
      warmup_is_on = campaignWithStatsAndEmail.warmup_is_on,
      stats = campaignWithStatsAndEmail.stats,
      head_step_id = campaignWithStatsAndEmail.head_step_id,
      ai_generation_context = None,
      settings = campaignWithStatsAndEmail.settings,
      created_at = campaignWithStatsAndEmail.created_at,
      error = campaignWithStatsAndEmail.error
    )

  val campaignBasicInfoSeq: Seq[CampaignBasicInfo] = Seq(CampaignBasicInfo(
    id = 121L,
    uuid = Some(campaign_uuid),
    team_id = teamId.id,
    shared_with_team = true,
    name = campaign_name,
    owner_name = first_name,
    owner_email = email,
    owner_id = ownerId,
    status = CampaignStatus.RUNNING,
    ai_generation_context = None,
    tags = Seq(CampaignTag(11L, "tag1", CampaignTagUuid("tag_abcdefgh"))),
    spam_test_exists = false,
    head_step_id = Some(22L),
    settings = campaignSettings,
    campaign_has_email_step = true,
    created_at = aDate,
    error = Some("campaign error"),
    is_archived = false,
    warmup_is_on = false
  ))

  val campaignBasicInfoInternalSeq = campaignBasicInfoSeq.map(info => CampaignBasicInfoInternal(
    id = info.id,
    uuid = info.uuid,
    team_id = info.team_id,
    shared_with_team = info.shared_with_team,
    name = info.name,
    owner_name = info.owner_name,
    owner_email = info.owner_email,
    owner_id = info.owner_id,
    status = info.status,
    tags = info.tags,
    spam_test_exists = info.spam_test_exists,
    warmup_is_on = info.warmup_is_on,
    head_step_id = info.head_step_id,
    settings = info.settings,
    created_at = info.created_at,
    error = info.error
  ))

  val campaignApiDataWithoutStatsSeq =campaignBasicInfoSeq.map(info => CampaignDataForApiV2WithoutStats(
    id = info.id,
    name = info.name,
    owner_id = info.owner_id,
    status = info.status,
    created_at = info.created_at,
  ))

  val campaignListingApiV3ResponseSeq = campaignBasicInfoSeq.map(info => CampaignDataForApiV3(
    uuid = info.uuid,
    name = info.name,
    created_at = info.created_at,
    status = info.status,
    tags = info.tags,
    owner_name = info.owner_name,
    owner_email = info.owner_name,
    team_uuid = team_uuid,
    settings = campaignSettingsDataForV3
  ))


  describe("campaign api structure tests"){
    it("should fail when v3 api and campaign api service returns error"){

      (campaignApiService.getCampaignApiResponse (_: String, _: CampaignWithStatsAndEmail, _: OrgId, _: TeamId)(using _: SRLogger))
        .expects("v3", campaignWithStatsAndEmail, orgId, teamId, logger)
        .returning(Left(CampaignApiError.ErrorWhileFetchingDataForTeamId(new Exception("some error"))))

      val result = apiService.getCampaignApiResponse(
        v = "v3",
        campaign = campaignWithStatsAndEmail,
        orgId = orgId,
        teamId = teamId,
        isPublicApi = true
      )
      result match {

        case Left(err) => err match {
          case CampaignApiError.ErrorWhileFetchingDataForTeamId(_) => assert(true)
        }

        case Right(_) => assert(false)
      }

    }

    it("should pass when v3 api and campaign api service returns CampaignResource") {

      (campaignApiService.getCampaignApiResponse (_: String, _: CampaignWithStatsAndEmail, _: OrgId, _: TeamId)(using _: SRLogger))
        .expects("v3", campaignWithStatsAndEmail, orgId, teamId, logger)
        .returning(Right(campaignDataForApiV3))

      val result = apiService.getCampaignApiResponse(
        v = "v3",
        campaign = campaignWithStatsAndEmail,
        teamId = teamId,
        orgId = orgId,
        isPublicApi = true
      )
      result match {

        case Left(err) => err match {
          case CampaignApiError.ErrorWhileFetchingDataForTeamId(_) => assert(false)
        }

        case Right(value) => value match {
          case _: CampaignResource.CampaignDataForApiV3 => assert(true)
          case _: CampaignResource.CampaignDataForApiV2 => assert(false)
          case _: CampaignResource.CampaignWithStatsAndEmailInternal => assert(false)
          case _: CampaignResource.CampaignBasicInfoInternal => assert(false)
          case _: CampaignResource.CampaignDataForApiV2WithoutStats => assert(false)
        }
      }

    }

    it("should pass when v2 api and campaign api service returns CampaignResource") {

      (campaignApiService.getCampaignApiResponse (_: String, _: CampaignWithStatsAndEmail, _: OrgId, _: TeamId)(using _: SRLogger))
        .expects("v2", campaignWithStatsAndEmail, orgId, teamId, logger)
        .returning(Right(campaignApiData))

      val result = apiService.getCampaignApiResponse(
        v = "v2",
        campaign = campaignWithStatsAndEmail,
        teamId = teamId,
        orgId = orgId,
        isPublicApi = true
      )
      result match {

        case Left(err) => err match {
          case CampaignApiError.ErrorWhileFetchingDataForTeamId(_) => assert(false)
        }

        case Right(value) => value match {
          case _: CampaignResource.CampaignDataForApiV3 => assert(false)
          case _: CampaignResource.CampaignDataForApiV2 => assert(true)
          case _: CampaignResource.CampaignWithStatsAndEmailInternal => assert(false)
          case _: CampaignResource.CampaignBasicInfoInternal => assert(false)
          case _: CampaignResource.CampaignDataForApiV2WithoutStats => assert(false)
        }
      }

    }

    it("should pass when v1 api and campaign api service returns CampaignResource") {

      (campaignApiService.getCampaignApiResponse (_: String, _: CampaignWithStatsAndEmail, _: OrgId, _: TeamId)(using _: SRLogger))
        .expects("v1", campaignWithStatsAndEmail, orgId, teamId, logger)
        .returning(Right(campaignApiData))

      val result = apiService.getCampaignApiResponse(
        v = "v1",
        campaign = campaignWithStatsAndEmail,
        teamId = teamId,
        orgId = orgId,
        isPublicApi = true
      )
      result match {

        case Left(err) => err match {
          case CampaignApiError.ErrorWhileFetchingDataForTeamId(_) => assert(false)
        }

        case Right(value) => value match {
          case _: CampaignResource.CampaignDataForApiV3 => assert(false)
          case _: CampaignResource.CampaignDataForApiV2 => assert(true)
          case _: CampaignResource.CampaignWithStatsAndEmailInternal => assert(false)
          case _: CampaignResource.CampaignBasicInfoInternal => assert(false)
          case _: CampaignResource.CampaignDataForApiV2WithoutStats => assert(false)
        }
      }

    }

    it("should pass when v2 and campaign api service returns CampaignResource") {

      (campaignApiService.getCampaignWithStatsAndEmailInternal)
        .expects(campaignWithStatsAndEmail)
        .returning(campaignWithStatsAndEmailInternal)

      val result = apiService.getCampaignApiResponse(
        v = "v2",
        campaign = campaignWithStatsAndEmail,
        orgId = orgId,
        teamId = teamId,
        isPublicApi = false
      )
      result match {

        case Left(err) => err match {
          case CampaignApiError.ErrorWhileFetchingDataForTeamId(_) => assert(false)
        }

        case Right(value) => value match {
          case _: CampaignResource.CampaignDataForApiV3 => assert(false)
          case _: CampaignResource.CampaignDataForApiV2 => assert(false)
          case _: CampaignResource.CampaignWithStatsAndEmailInternal => assert(true)
          case _: CampaignResource.CampaignBasicInfoInternal => assert(false)
          case _: CampaignResource.CampaignDataForApiV2WithoutStats => assert(false)
        }
      }

    }
  }

  describe("campaign listing api structure tests") {
    it("should fail when v3 api and campaign api service returns error") {

      (campaignApiService.getCampaignListingApiResponse(_: ApiVersion, _: Seq[CampaignBasicInfo], _: OrgId, _: TeamId)(using _: SRLogger))
        .expects(ApiVersion.V3, campaignBasicInfoSeq, orgId, teamId, logger)
        .returning(Left(CampaignApiError.ErrorWhileFetchingDataForTeamId(new Exception("some error"))))

      val result = apiService.getCampaignListingApiResponse(
        v = ApiVersion.V3,
        campaigns = campaignBasicInfoSeq,
        orgId = orgId,
        teamId = teamId,
        isPublicApi = true
      )
      result match {

        case Left(err) => err match {
          case CampaignApiError.ErrorWhileFetchingDataForTeamId(_) => assert(true)
        }

        case Right(_) => assert(false)
      }

    }

    it("should pass when v3 api and campaign api service returns Seq[CampaignResource]") {

      (campaignApiService.getCampaignListingApiResponse(_: ApiVersion, _: Seq[CampaignBasicInfo], _: OrgId, _: TeamId)(using _: SRLogger))
        .expects(ApiVersion.V3, campaignBasicInfoSeq, orgId, teamId, logger)
        .returning(Right(campaignListingApiV3ResponseSeq))

      val result = apiService.getCampaignListingApiResponse(
        v = ApiVersion.V3,
        campaigns = campaignBasicInfoSeq,
        orgId = orgId,
        teamId = teamId,
        isPublicApi = true
      )
      result match {

        case Left(err) => err match {
          case CampaignApiError.ErrorWhileFetchingDataForTeamId(_) => assert(false)
        }

        case Right(value) => value.head match {
          case _: CampaignResource.CampaignDataForApiV3 => assert(true)
          case _: CampaignResource.CampaignDataForApiV2 => assert(false)
          case _: CampaignResource.CampaignWithStatsAndEmailInternal => assert(false)
          case _: CampaignResource.CampaignBasicInfoInternal => assert(false)
          case _: CampaignResource.CampaignDataForApiV2WithoutStats => assert(false)
        }
      }

    }

    it("should pass when v2 api and campaign api service returns Seq[CampaignResource]") {

      (campaignApiService.getCampaignListingApiResponse(_: ApiVersion, _: Seq[CampaignBasicInfo], _: OrgId, _: TeamId)(using _: SRLogger))
        .expects(ApiVersion.V2, campaignBasicInfoSeq, orgId, teamId, logger)
        .returning(Right(campaignApiDataWithoutStatsSeq))

      val result = apiService.getCampaignListingApiResponse(
        v = ApiVersion.V2,
        campaigns = campaignBasicInfoSeq,
        orgId = orgId,
        teamId = teamId,
        isPublicApi = true
      )
      result match {

        case Left(err) => err match {
          case CampaignApiError.ErrorWhileFetchingDataForTeamId(_) => assert(false)
        }

        case Right(value) => value.head match {
          case _: CampaignResource.CampaignDataForApiV3 => assert(false)
          case _: CampaignResource.CampaignDataForApiV2 => assert(false)
          case _: CampaignResource.CampaignWithStatsAndEmailInternal => assert(false)
          case _: CampaignResource.CampaignBasicInfoInternal => assert(false)
          case _: CampaignResource.CampaignDataForApiV2WithoutStats => assert(true)
        }
      }

    }

    it("should pass when v1 api and campaign api service returns Seq[CampaignResource]") {

      (campaignApiService.getCampaignListingApiResponse(_: ApiVersion, _: Seq[CampaignBasicInfo], _: OrgId, _: TeamId)(using _: SRLogger))
        .expects(ApiVersion.V1, campaignBasicInfoSeq, orgId, teamId, logger)
        .returning(Right(campaignApiDataWithoutStatsSeq))

      val result = apiService.getCampaignListingApiResponse(
        v = ApiVersion.V1,
        campaigns = campaignBasicInfoSeq,
        orgId = orgId,
        teamId = teamId,
        isPublicApi = true
      )
      result match {

        case Left(err) => err match {
          case CampaignApiError.ErrorWhileFetchingDataForTeamId(_) => assert(false)
        }

        case Right(value) => value.head match {
          case _: CampaignResource.CampaignDataForApiV3 => assert(false)
          case _: CampaignResource.CampaignDataForApiV2 => assert(false)
          case _: CampaignResource.CampaignWithStatsAndEmailInternal => assert(false)
          case _: CampaignResource.CampaignBasicInfoInternal => assert(false)
          case _: CampaignResource.CampaignDataForApiV2WithoutStats => assert(true)
        }
      }

    }

    it("should pass when v2 and campaign api service returns Seq[CampaignResource]") {

      (campaignApiService.getCampaignBasicInfoInternal)
        .expects(campaignBasicInfoSeq)
        .returning(campaignBasicInfoInternalSeq)

      val result = apiService.getCampaignListingApiResponse(
        v = ApiVersion.V2,
        campaigns = campaignBasicInfoSeq,
        orgId = orgId,
        teamId = teamId,
        isPublicApi = false
      )
      result match {

        case Left(err) => err match {
          case CampaignApiError.ErrorWhileFetchingDataForTeamId(_) => assert(false)
        }

        case Right(value) => value.head match {
          case _: CampaignResource.CampaignDataForApiV3 => assert(false)
          case _: CampaignResource.CampaignDataForApiV2 => assert(false)
          case _: CampaignResource.CampaignWithStatsAndEmailInternal => assert(false)
          case _: CampaignResource.CampaignBasicInfoInternal => assert(true)
          case _: CampaignResource.CampaignDataForApiV2WithoutStats => assert(false)
        }
      }

    }
  }

}
