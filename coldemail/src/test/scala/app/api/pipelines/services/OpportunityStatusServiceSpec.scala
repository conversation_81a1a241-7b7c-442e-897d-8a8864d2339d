package app.api.pipelines.services

import api.pipelines.models.{OpportunityStatusUUID, StatusType}
import api.pipelines.services.OpportunityStatusService
import org.scalamock.scalatest.MockFactory
import org.scalatest.funspec.AnyFunSpec
import utils.SRLogger


class OpportunityStatusServiceSpec extends AnyFunSpec with MockFactory {

  implicit lazy val logger: SRLogger = new SRLogger("OpportunityStatusServiceSpec")


  val fromOpportunityStatusUUID: OpportunityStatusUUID = OpportunityStatusUUID(uuid = "from-opp-status-uuid")

  val toOpportunityStatusUUID: OpportunityStatusUUID = OpportunityStatusUUID(uuid = "to-opp-status-uuid")

  val opportunityStatusType_active: StatusType = StatusType.Active

  val opportunityStatusType_won: StatusType = StatusType.Won


  describe("Test validateTransferOpportunitiesAndDeleteStatus") {

    it("should return error same opportunity status id message if from and to opportunity status id is same.") {

      val errMsgOpt = OpportunityStatusService.validateTransferOpportunitiesAndDeleteStatus(
        fromOpportunityStatusId = fromOpportunityStatusUUID,
        toOpportunityStatusId = fromOpportunityStatusUUID,
        fromOpportunityStatusType = opportunityStatusType_active,
        toOpportunityStatusType = opportunityStatusType_active
      )

      assert(
        errMsgOpt.isDefined &&
          errMsgOpt.get == OpportunityStatusService.SameOpportunityStatusIdErrorMsg
      )

    }

    it("should return error status type changed message if from and to opportunity status id is same.") {

      val errMsgOpt = OpportunityStatusService.validateTransferOpportunitiesAndDeleteStatus(
        fromOpportunityStatusId = fromOpportunityStatusUUID,
        toOpportunityStatusId = toOpportunityStatusUUID,
        fromOpportunityStatusType = opportunityStatusType_active,
        toOpportunityStatusType = opportunityStatusType_won
      )

      assert(
        errMsgOpt.isDefined &&
          errMsgOpt.get == OpportunityStatusService.DifferentStatusTypeErrorMsg
      )

    }

    it("should return None if all validations pass.") {

      val errMsgOpt = OpportunityStatusService.validateTransferOpportunitiesAndDeleteStatus(
        fromOpportunityStatusId = fromOpportunityStatusUUID,
        toOpportunityStatusId = toOpportunityStatusUUID,
        fromOpportunityStatusType = opportunityStatusType_active,
        toOpportunityStatusType = opportunityStatusType_active
      )

      assert(
        errMsgOpt.isEmpty
      )

    }

  }

}

