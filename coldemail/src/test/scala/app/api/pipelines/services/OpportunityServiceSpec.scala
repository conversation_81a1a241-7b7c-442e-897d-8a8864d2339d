package app.api.pipelines.services

import api.AppConfig
import api.pipelines.dao.OpportunityData
import api.pipelines.models.StatusType
import api.pipelines.services.OpportunityService
import app.test_fixtures.pipelines.OpportunityFixtures
import org.scalamock.scalatest.MockFactory
import org.scalatest.funspec.AnyFunSpec
import utils.SRLogger


class OpportunityServiceSpec extends AnyFunSpec with MockFactory {

  implicit lazy val logger: SRLogger = new SRLogger("OpportunityServiceSpec")

  private val confidence_current_7 = 7

  private val confidence_current_53 = 53

  private val opportunityStatusType_active = StatusType.Active

  private val opportunityStatusType_non_active = StatusType.Lost

  private val opportunityValue_neg_53 = -53

  private val confidence_max_plus_937 = AppConfig.OpportunityConfidenceLimits.maxConfidenceFieldValue + 937

  private val confidence_min_minus_93 = AppConfig.OpportunityConfidenceLimits.minConfidenceFieldValue - 93


  private val opportunityData_exceeds_confidence_upper_limit: OpportunityData =
    OpportunityFixtures.opportunityData_default.copy(
      confidence = confidence_max_plus_937,
    )

  private val opportunityData_confidence_less_than_lower_limit: OpportunityData =
    OpportunityFixtures.opportunityData_default.copy(
      confidence = confidence_min_minus_93,
    )

  private val opportunityData_neg_value: OpportunityData =
    OpportunityFixtures.opportunityData_default.copy(
      value = opportunityValue_neg_53,
    )


  private val opportunityData_valid: OpportunityData = OpportunityFixtures.opportunityData_default

  describe("Test validateOpportunityData") {

    it("should return opportunity confidence limits error if confidence is more than the upper limit.") {

      val errMsgOpt = OpportunityService.validateOpportunityData(
        opportunityData = opportunityData_exceeds_confidence_upper_limit
      )

      assert(
        errMsgOpt.isDefined &&
          errMsgOpt.get == AppConfig.OpportunityServiceErrors.OpportunityConfidenceLimitsErrorMessage
      )

    }

    it("should return opportunity confidence limits error if confidence is less than the lower limit.") {

      val errMsgOpt = OpportunityService.validateOpportunityData(
        opportunityData = opportunityData_confidence_less_than_lower_limit
      )

      assert(
        errMsgOpt.isDefined &&
          errMsgOpt.get == AppConfig.OpportunityServiceErrors.OpportunityConfidenceLimitsErrorMessage
      )

    }

    it("should return negative opportunity value error if value is negative.") {

      val errMsgOpt = OpportunityService.validateOpportunityData(
        opportunityData = opportunityData_neg_value
      )

      assert(
        errMsgOpt.isDefined &&
          errMsgOpt.get == AppConfig.OpportunityServiceErrors.NegativeOpportunityValueError
      )

    }

    it("should return None if all validations pass.") {

      val errMsgOpt = OpportunityService.validateOpportunityData(
        opportunityData = opportunityData_valid
      )

      assert(
        errMsgOpt.isEmpty
      )

    }

  }

  describe("Test validateConfidenceUpdate") {

    it(
      "should return cannot update confidence in non active status error if confidence is updated in non active status."
    ) {

      val errMsgOpt = OpportunityService.validateConfidenceUpdate(
        opportunityData = opportunityData_valid,
        currConfindenceValue = confidence_current_7,
        opportunityStatusType = opportunityStatusType_non_active
      )

      assert(
        errMsgOpt.isDefined &&
          errMsgOpt.get == AppConfig.OpportunityServiceErrors.ConfidenceUpdatedInNonActiveStatusErrorMessage
      )

    }

    it("should return None if confidence is updated but opportunity status type is active.") {

      val errMsgOpt = OpportunityService.validateConfidenceUpdate(
        opportunityData = opportunityData_valid,
        currConfindenceValue = confidence_current_53,
        opportunityStatusType = opportunityStatusType_active
      )

      assert(
        errMsgOpt.isEmpty
      )

    }

    it("should return None if confidence is not updated.") {

      val errMsgOpt = OpportunityService.validateConfidenceUpdate(
        opportunityData = opportunityData_valid,
        currConfindenceValue = opportunityData_valid.confidence,
        opportunityStatusType = opportunityStatusType_non_active
      )

      assert(
        errMsgOpt.isEmpty
      )

    }

  }

}
