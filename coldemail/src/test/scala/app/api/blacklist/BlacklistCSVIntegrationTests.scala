package app.api.blacklist

import org.apache.pekko.actor.ActorSystem
import org.apache.pekko.stream.Materializer
import api.AppConfig
import api.blacklist.models.DoNotContactType
import db_test_spec.api.{DbTestingBeforeAllAndAfterAll, InitialData}
import db_test_spec.api.accounts.fixtures.NewAccountAndEmailSettingData
import play.api.libs.json.{JsObject, JsValue, Json}
import play.api.libs.ws.ahc.AhcWSClient
import play.api.test.FakeRequest
import play.api.test.Helpers.*
import utils.SRLogger
import utils.helpers.LogHelpers
import utils.testapp.TestAppExecutionContext

class BlacklistCSVIntegrationTests extends DbTestingBeforeAllAndAfterAll {


  describe("BlacklistController.saveUploadedDncCsv") {
    it("should create an entry in a csvQueue table") {
      implicit lazy val system: ActorSystem = TestAppExecutionContext.actorSystem
      implicit lazy val materializer: Materializer = TestAppExecutionContext.actorMaterializer
      implicit lazy val wSClient: AhcWSClient = TestAppExecutionContext.wsClient

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val columnMap: JsValue = Json.obj(
        "emails" -> "email"
      )

      val requestBody: JsValue = Json.obj(
        "column_map" -> columnMap,
        "file_url" -> "https://example.com/test.csv",
        "force_update_prospects" -> false,
        "csv_upload_type" -> "dnc"
      )

      val sendingUrl = s"/api/v3/do_not_contact/save_uploaded_csv"

      val request = FakeRequest(play.api.test.Helpers.POST, sendingUrl)
        .withHeaders(
          "X-API-KEY" -> initialData.teamUserLevelKey,
          "Content-Type" -> "application/json")
        .withJsonBody(requestBody)

      val final_result = play.api.test.Helpers.route(testApi, request).get
      val status: Int = play.api.test.Helpers.status(final_result)
      val json: JsValue = play.api.test.Helpers.contentAsJson(final_result)
      final_result
        .map { res =>
          println(res)
          println(json)
          if (status == 200) {
            val columnMap = (json \ "data" \ "csv_file" \ "column_map").as[JsValue]
            val hasBeenUploaded = (json \ "data" \ "csv_file" \ "has_been_uploaded").as[Boolean]
            val fileUrl = (json \ "data" \ "csv_file" \ "file_url").as[String]
            val fileName = (json \ "data" \ "csv_file" \ "file_name").as[String]

            assert(hasBeenUploaded == false)
          } else {
            assert(false)
          }

        }.recover { e =>
          println(s"recovery ${LogHelpers.getStackTraceAsString(e)}")
          assert(false)
        }
    }
  }

}