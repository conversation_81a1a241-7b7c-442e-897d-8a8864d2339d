package app.api.campaigns.services

import api.AppConfig
import api.accounts.email.models.EmailServiceProvider
import api.accounts.models.{AccountId, AccountProfileInfo, OrgId, ProspectAccountUuid}
import api.accounts.{Account, AccountAccess, AccountMetadata, AccountService, AccountType, AccountUuid, OrgCountData, OrgMetadata, OrgPlan, OrgSettings, OrganizationRole, OrganizationWithCurrentData, ReplyHandling, TeamAccountRole, TeamId, TeamMember, UpdateAccountProfileDuringOnboarding}
import api.calendar_app.CalendarAppService
import api.calendar_app.models.CalendarAccountData
import api.campaigns.models.{CampaignEmailSettingsId, CampaignStepData, CampaignStepType, CampaignType, PreviousFollowUpData}
import api.campaigns.services.{CallSettingSenderDetails, CampaignId, CampaignPreviewService, ChannelSettingData, ChannelSettingSenderDetails, ChannelSettingService, LinkedinSettingSenderDetails, PreviewGetStepsForProspectError, SmsSettingSenderDetails, StepAndVariantId, WhatsappSettingSenderDetails}
import api.campaigns.services.{CampaignDAOService, CampaignId, CampaignPreviewService, CampaignService, ChannelSettingData, ChannelSettingSenderDetails, ChannelSettingService, PreviewGetStepsForProspectError, StepAndVariantId}
import api.campaigns.{Campaign, CampaignEditedPreviewEmail, CampaignEditedPreviewEmailDAO, CampaignEmailSettings, CampaignEmailSettingsUuid, CampaignSettings, CampaignStepVariantDAO, CampaignStepVariantForScheduling, CampaignStepWithChildren, ChannelSettingUuid, PreviousFollowUp}
import api.columns.InternalMergeTagValuesForProspect
import api.emails.models.EmailSettingUuid
import api.emails.{EmailScheduledDAO, EmailSetting, EmailSettingDAO, EmailsScheduledUuid}
import api.prospects.{ProspectAccount, ProspectAccountDAO1, ProspectUuid}
import api.prospects.dao_service.ProspectDAOService
import api.tasks.models.TaskPriority
import api.tasks.services.TaskService
import api.team.TeamUuid
import app.test_fixtures.accounts.OrgCountDataFixture
import app.test_fixtures.campaign_settings.{CallSettingSenderDetailsFixtures, LinkedinSettingSenderDetailsFixtures, SmsSettingSenderDetailsFixtures, WhatsappSettingSenderDetailsFixtures}
import app.test_fixtures.organizationa.{OrgMetadataFixture, OrgPlanFixture}
import eventframework.{ProspectObject, ProspectObjectInternal}
import io.smartreach.esp.api.emails.EmailSettingId
import org.scalamock.scalatest.AsyncMockFactory
import org.scalatest.funspec.AsyncFunSpec
import sr_scheduler.models.{CampaignEmailPriority, ChannelType, SelectedCalendarData}
import utils.SRLogger
import utils.email.{EmailOptionsForGetBodies, EmailServiceBody, EmailServiceCompanion}
import utils.templating.TemplateService
import org.joda.time.DateTime
import sr_scheduler.CampaignStatus
import play.api.libs.json.Json
import utils.cache_utils.model.CampaignUseStatusForEmailSetting

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}
import app.test_fixtures.calendar_app.CalendarFixtures
import app.test_fixtures.prospect.{ProspectAccountFixture, ProspectFixtures}
import api.emails.dao_service.EmailScheduledDAOService
import api.gpt.ai_hyperpersonalized.AIHyperPersonalizedGenerator


class CampaignPreviewServiceSpec extends AsyncFunSpec with AsyncMockFactory{

  given logger: SRLogger = new SRLogger(logRequestId = "CampaignPreviewServiceSpec: ")

  val prospectDAOService: ProspectDAOService = mock[ProspectDAOService]
  val prospectAccountDAO = mock[ProspectAccountDAO1]
  val campaignStepVariantDAO = mock[CampaignStepVariantDAO]
  val emailScheduledDAOService = mock[EmailScheduledDAOService]
  val campaignEditedPreviewEmailDAO = mock[CampaignEditedPreviewEmailDAO]
  val emailSettingDAO = mock[EmailSettingDAO]
  val templateService = mock[TemplateService]
  val taskService = mock[TaskService]
  val channelSettingService = mock[ChannelSettingService]
  val emailServiceCompanion = mock[EmailServiceCompanion]
  val accountService = mock[AccountService]
  val campaignDAOService: CampaignDAOService = mock[CampaignDAOService]
  val calendarAppService: CalendarAppService = mock[CalendarAppService]
  val campaignService: CampaignService = mock[CampaignService]
  val aiHyperPersonalizedGenerator: AIHyperPersonalizedGenerator =  mock[AIHyperPersonalizedGenerator]


  val campaignPreviewService = new CampaignPreviewService(
    campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO,
    prospectDAOService = prospectDAOService,
    emailScheduledDAOService = emailScheduledDAOService,
    campaignStepVariantDAO = campaignStepVariantDAO,
    prospectAccountDAO = prospectAccountDAO,
    channelSettingService = channelSettingService,
    templateService = templateService,
    emailSettingDAO = emailSettingDAO,
    emailServiceCompanion = emailServiceCompanion,
    taskService = taskService,
    accountService = accountService,
    campaignDAOService = campaignDAOService,
    calendarAppService = calendarAppService,
    campaignService  = campaignService,
    aiHyperPersonalizedGenerator =  aiHyperPersonalizedGenerator
  )


  val counts: OrgCountData = OrgCountDataFixture.orgCountData_default

  val permittedAccountIds = Seq(11L, 13L, 2L, 3L)

  val account_id: Long = 1
  val team_id: Long = 1 // All ids are 1 in this test, we can take this up later to make them prime
  val campaign_id: Long = 1 // All ids are 1 in this test, we can take this up later to make them prime
  val team_id_VC = TeamId(id = team_id)
  val campaign_id_VC = CampaignId(id = campaign_id)
  val campaignSettings = CampaignSettings(
    campaign_email_settings = List(
      CampaignEmailSettings(
        campaign_id = CampaignId(campaign_id),
        sender_email_setting_id = EmailSettingId(1),
        receiver_email_setting_id = EmailSettingId(1),
        team_id = TeamId(team_id),
        uuid = CampaignEmailSettingsUuid("temp_setting_id"),
        id = CampaignEmailSettingsId(123),
        sender_email = "<EMAIL>",
        receiver_email = "<EMAIL>",
        max_emails_per_day_from_email_account = 400,
        signature = Some("emailsignature"),
        error = None,
        from_name = None
      )
    ),
    campaign_linkedin_settings = List(
      LinkedinSettingSenderDetailsFixtures.linkedin_setting_sender_details
    ),
    campaign_call_settings = List(
      CallSettingSenderDetailsFixtures.call_setting_sender_details
    ),
    campaign_whatsapp_settings = List(
      WhatsappSettingSenderDetailsFixtures.whatsapp_setting_sender_details
    ),
    campaign_sms_settings = List(
      SmsSettingSenderDetailsFixtures.sms_setting_sender_details
    ),
    ai_sequence_status = None,
    timezone = "UTC",
    daily_from_time = 8,
    daily_till_time = 12,
    sending_holiday_calendar_id = None,
    days_preference = List(false, true, true, true, true, false, false),
    mark_completed_after_days = 4,
    max_emails_per_day = 40,
    open_tracking_enabled = false,
    click_tracking_enabled = false,
    enable_email_validation = false,
    ab_testing_enabled = false,
    warmup_started_at = None,
    warmup_length_in_days = None,
    warmup_starting_email_count = None,
    show_soft_start_setting = false,
    schedule_start_at = None,
    schedule_start_at_tz = None,
    email_priority = CampaignEmailPriority.FIRST_EMAIL,
    send_plain_text_email = Some(false),
    campaign_type = CampaignType.MultiChannel,
    append_followups = true,
    opt_out_msg = "{{unsubscribe_link}}",
    opt_out_is_text = false,
    add_prospect_to_dnc_on_opt_out = true,
    triggers = Seq(),
    sending_mode = None,
    selected_calendar_data = None
  )

  val orgPlan = OrgPlanFixture.orgPlanFixture

  val orgMetadata = OrgMetadataFixture.orgMetadataFixture2

  val orgSettings = OrgSettings(
    enable_ab_testing = true,
    disable_force_send = false,
    bulk_sender = false,
    allow_2fa = false,
    show_2fa_setting = false,
    enforce_2fa = false,
    allow_native_crm_integration = false,
      agency_option_allow_changing = false,
      agency_option_show = false
  )

  val org = OrganizationWithCurrentData(
    id = 1,
    name = "Animesh",
    owner_account_id = 1,
    counts = counts,
    settings = orgSettings,
    plan = orgPlan,
    is_agency = true,
    trial_ends_at = DateTime.now().plusDays(10),
    error = None,
    error_code = None,
    paused_till = None,
    errors = Seq(),
    warnings = Seq(),
    via_referral = true,
    org_metadata = orgMetadata
  )

  val Error = new Throwable("ERROR")
  val campaign_uuid = s"cmp_1_cfknacskndjcn"
  val campaign = Campaign(
    id = 1L,
    uuid = Some(campaign_uuid),
    account_id = 1L,
    team_id = 1L,
    shared_with_team = true,
    name = "New Campaign 0161",
    status = CampaignStatus.RUNNING,
    head_step_id = Some(1L),
    settings = campaignSettings,
    last_scheduled_at = None,
    created_at = DateTime.now()
  )

  val prospectObjectInternal = ProspectFixtures.prospectObjectInternal

  val prospectObject = ProspectObject(
    id = 1,
    owner_id = 1,
    team_id = 1,
    first_name = Some("Animesh"),
    last_name = Some("Kumar"),
    email = Some("<EMAIL>"),
    custom_fields = Json.obj(),
    list = None,
    job_title = None,
    company = None,
    linkedin_url = None,
    phone = None,
    phone_2 = None,
    phone_3 = None,
    city = None,
    state = None,
    country = None,
    timezone = None,
    prospect_category = "Dont know this",
    last_contacted_at = None,
    last_contacted_at_phone = None,
    created_at = DateTime.now().minusMonths(5),
    internal = prospectObjectInternal,
    latest_reply_sentiment_uuid = None,
    current_step_type = None,
    latest_task_done_at = None,
    prospect_uuid = Some(ProspectUuid("prs_aa_abcdefghi")),
    owner_uuid = AccountUuid("acc_aa_abcdegfhi"),
    updated_at = DateTime.now()
  )

  val campaignStepVariantForScheduling = CampaignStepVariantForScheduling(
    id = 1,
    step_id = 1,
    campaign_id = 1,
    template_id = None,
    step_data = CampaignStepData.AutoEmailStep(
      subject = "variant subject",
      body = "Variant body",
    ),
    step_label = None,
    step_delay = 10,
    notes = Some("Note"),
    priority = Some(TaskPriority.Normal),
    active = true,
    scheduled_count = 1
  )


  val campaignStepWithChildren = CampaignStepWithChildren(
    id = 1,
    label = None,
    campaign_id = 1,
    delay = 10,
    step_type = CampaignStepType.AutoEmailStep,
    created_at = DateTime.parse("2022-03-21T11:58:03.294Z"),
    children = List(2, 3, 4),
    variants = Seq(
      campaignStepVariantForScheduling,
      campaignStepVariantForScheduling.copy(id = 2, step_data = CampaignStepData.AutoEmailStep(body = "Variant body", subject = "Variant subject V2")),
      campaignStepVariantForScheduling.copy(id = 3)
    )
  )

  val channel_follow_up_data = PreviousFollowUpData.AutoEmailFollowUp(
    email_thread_id = Some(1), // it is 1 everywhere, we are just fixing compile error now Date: 16/03/2023
    from_name = "Prateek Bhat",
    base_body = "This is previous test body",
    body = "This is previous test body",
    subject = "Hey {{first_name}}",
    from_email = "<EMAIL>",
    is_edited_preview_email = false,

  )

  val previousFollowUp = PreviousFollowUp(
    channel_follow_up_data = channel_follow_up_data,
    sent_at = DateTime.now().minusDays(5),
    timezone = "IN",
    step_id = Some(1),
    completed_reason = None
  )

  val campaignEditedPreviewEmail = CampaignEditedPreviewEmail(
    campaignId = 1,
    prospectId = 1,
    stepId = 1,
    editedByAccountId = 1,
    editedSubject = "This is a Subject",
    editedBody = "This is the body")

  val emailSetting = EmailSetting(
    id = Some(EmailSettingId(emailSettingId = 1)),
    org_id = OrgId(id = 1),
    owner_id = AccountId(id = 1),
    team_id = TeamId(id = 1),
    uuid = Some(EmailSettingUuid("test_uuid")),
    owner_uuid = AccountUuid("owner_uuid"),
    team_uuid = TeamUuid("team_uuid"),
    message_id_suffix = "Hello",
    email = "<EMAIL>",
    email_address_host = "<EMAIL>",
    service_provider = EmailServiceProvider.GMAIL_API,
      domain_provider = None,
    via_gmail_smtp = None,
    owner_name = "Animesh",
    sender_name = "Animesh Kumar",
    first_name = "Animesh",
    last_name = "Kumar",
    cc_emails = None,
    bcc_emails = None,
    smtp_username = None,
    smtp_password = None,
    smtp_host = None,
    smtp_port = None,
    imap_username = None,
    imap_password = None,
    imap_host = None,
    imap_port = None,
    oauth2_access_token = None,
    oauth2_refresh_token = None,
    oauth2_token_type = None,
    oauth2_token_expires_in = None,
    oauth2_access_token_expires_at = None,
    email_domain = None,
    api_key = None,
    mailgun_region = None,
    quota_per_day = 400,
    reply_handling = ReplyHandling.PAUSE_SPECIFIC_CAMPAIGN_ON_REPLY,
    last_read_for_replies = None,
    latest_email_scheduled_at = None,
    error = None,
    error_reported_at = None,
    paused_till = None,
    signature = "Animesh Kumar",
    created_at = None,
    current_prospect_sent_count_email = 10,
    default_tracking_domain = "default_tracking_domain",
    default_unsubscribe_domain = "default_unsubscribe_domain",
    rep_tracking_host_id = 1,
    tracking_domain_host = None,
    custom_tracking_domain = None,
    custom_tracking_cname_value = None,
    custom_tracking_domain_is_verified = None,
    custom_tracking_domain_is_ssl_enabled = None,
    rep_mail_server_id = 1,
    rep_mail_server_public_ip = "rep_mail_server_public_ip",
    rep_mail_server_host = "rep_mail_server_host",
    rep_mail_server_reverse_dns = None,
    min_delay_seconds = 0,
    max_delay_seconds = 0,
      tag = None,
    campaign_use_status_for_email_setting = CampaignUseStatusForEmailSetting.IsNotAssignedToAnyCampaign,
    show_rms_ip_in_frontend = false
  )


  val account: Account = Account(
    id = AccountUuid("account_uuid"),
    internal_id = campaign.account_id,
    email = "<EMAIL>",
    email_verification_code = None,
    email_verification_code_created_at = None,
    created_at = DateTime.now().minusDays(1000),
    first_name = Some("Shubham"),
    last_name = Some("Kudekar"),
    company = Some("SK"),
    timezone = None,
    profile = AccountProfileInfo(
      first_name = "Shubham",
      last_name = "Kudekar",
      company = None,
      timezone = None,
      country_code = None,
      mobile_country_code = None,
      mobile_number = None,
      twofa_enabled = false,
      has_gauthenticator = false,
      weekly_report_emails = None,
      scheduled_for_deletion_at = None,
      onboarding_phone_number = Some("+************")
    ),
    org_role = Some(OrganizationRole.OWNER),
    teams = Seq(),
    account_type = AccountType.AGENCY,
    org = org,

    active = true,
    email_notification_summary = "dSFA",
    account_metadata = AccountMetadata(
      is_profile_onboarding_done = Some(true)
    ),
    email_verified = true,
    signupType = None,
    account_access = AccountAccess(
      inbox_access = false
    ),
    calendar_account_data = None
  )

  val successResponse = EmailServiceBody(
    subject = "Hey Animesh",
    textBody = "Animesh kumar this is a test",
    htmlBody =
      """<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
        |       <html xmlns="http://www.w3.org/1999/xhtml">
        |       <head>
        |       <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
        |       <title></title>
        |       <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
        |       <style type="text/css">
        |        p { margin: 0; font-size: '14px' }
        |
        |        .prev-reply {
        |          margin-left: 1em;
        |          color: #500050;
        |          padding-top: 20px;
        |        }
        |
        |        .time-details {}
        |
        |        .reply {
        |          border-left: 1px solid black;
        |          padding-left: 1em;
        |        }
        |       </style>
        |       </head>
        |       <body>Animesh kumar this is a test
        |
        |       </body>
        |       </html>""".stripMargin,
    baseBody = "Animesh kumar this is a test",
    isEditedPreviewEmail = false,
    has_unsubscribe_link = false)

  val emailOptionsForGetBodies = EmailOptionsForGetBodies(
    isCampaignSendTestEmail = false,
    for_editable_preview = false,
    editedPreviewEmailAlreadyChecked = true,
    custom_tracking_domain = emailSetting.custom_tracking_domain,
    default_tracking_domain = emailSetting.default_tracking_domain,
    default_unsubscribe_domain = emailSetting.default_unsubscribe_domain,
    signature = Some(emailSetting.signature),

    opt_out_msg = campaign.settings.opt_out_msg,
    opt_out_is_text = campaign.settings.opt_out_is_text,
    append_followups = true,
    bodyTemplate = "This is the body",
    subjectTemplate = "This is a Subject",

    email_sender_name = emailSetting.sender_name,
    sender_first_name = emailSetting.first_name,
    sender_last_name = emailSetting.last_name,
    manualEmail = false,

    trackOpens = campaign.settings.open_tracking_enabled,
    trackClicks = campaign.settings.click_tracking_enabled,

    previousEmails = Seq(),
    allTrackingDomainsUsed = Seq()
  )

  val teamIdValue = TeamId(id = team_id.toLong)
  val orgIdValue = OrgId(id = org.id)
  val campaignIdValue = Some(CampaignId(id = campaign.id.toLong))

  val selectedCalendarData = CalendarFixtures.teamSelectedCalendarData


  describe("previewGetStepsForProspect") {

    it("should fail because head_step_id is empty") {

      campaignPreviewService.previewGetStepsForProspect(
        campaign = campaign.copy(head_step_id = None),
        prospectId = 1,
        teamId = 1,
        enable_ab_testing = true,
        allTrackingDomainsUsed = Seq(),
        org = org,
        stepAndVariantIds = List(),
        permittedAccountIdsForViewingTasks = permittedAccountIds,
        selected_campaign_email_setting_id = Some(CampaignEmailSettingsId(123L))
      ).map { result =>
        assert(result == Left(PreviewGetStepsForProspectError.NoStepInCampaign))
      }
    }

    it("Error while getting the prospect") {

      (prospectDAOService.find)
        .expects(List(1L), List(), None, Some(1L), 1, None, 1, 0, true, false, None, logger)
        .returning(Failure(Error))

      campaignPreviewService.previewGetStepsForProspect(
        campaign = campaign,
        prospectId = 1,
        teamId = 1,
        enable_ab_testing = true,
        allTrackingDomainsUsed = Seq(),
        org = org,
        stepAndVariantIds = List(),
        permittedAccountIdsForViewingTasks = permittedAccountIds,
        selected_campaign_email_setting_id = Some(CampaignEmailSettingsId(123L))
      ).map { result =>
        assert(result == Left(PreviewGetStepsForProspectError.ErrorWhileGettingProspectObjects(Error)))
      }
    }

    it("no prospect sent back") {

      (prospectDAOService.find)
        .expects(List(1L), List(), None, Some(1L), 1, None, 1, 0, true, false, None, logger)
        .returning(Success(Seq()))

      campaignPreviewService.previewGetStepsForProspect(
        campaign = campaign,
        prospectId = 1,
        teamId = 1,
        enable_ab_testing = true,
        allTrackingDomainsUsed = Seq(),
        org = org,
        stepAndVariantIds = List(),
        permittedAccountIdsForViewingTasks = permittedAccountIds,
        selected_campaign_email_setting_id = Some(CampaignEmailSettingsId(123L))
      ).map { result =>
        assert(result == Left(PreviewGetStepsForProspectError.ProspectNotFoundInCampaign))
      }
    }

    it("getPreviousSentSteps sends error") {

      (prospectDAOService.find)
        .expects(List(1L), List(), None, Some(1L), 1, None, 1, 0, true, false, None, logger)
        .returning(Success(Seq(prospectObject)))

      (prospectAccountDAO.find)
        .expects(1, 1)
        .returning(Some(ProspectAccountFixture.prospectAccount))
      (campaignDAOService.findOrderedSteps)
        .expects(1L, TeamId(id = team_id))
        .returning(Seq(campaignStepWithChildren))

      (emailScheduledDAOService.getPreviousSentSteps(_: Long, _: Long, _: TeamId, _: Boolean)(using _: SRLogger))
        .expects(1, 1, TeamId(id = team_id), *, *)
        .returning(Failure(Error))


      campaignPreviewService.previewGetStepsForProspect(
        campaign = campaign,
        prospectId = 1,
        teamId = 1,
        enable_ab_testing = true,
        allTrackingDomainsUsed = Seq(),
        org = org,
        stepAndVariantIds = List(),
        permittedAccountIdsForViewingTasks = permittedAccountIds,
        selected_campaign_email_setting_id = Some(CampaignEmailSettingsId(123L))
      ).map { result =>
        assert(true)
      }.recover {
        case e =>
          assert(e == Error)
      }
    }

    it("campaignEditedPreviewEmailDAO sent error") {

      (prospectDAOService.find)
        .expects(List(1L), List(), None, Some(1L), 1, None, 1, 0, true, false, None, logger)
        .returning(Success(Seq(prospectObject)))

      (prospectAccountDAO.find)
        .expects(1, 1)
        .returning(Some(ProspectAccountFixture.prospectAccount))
      (campaignDAOService.findOrderedSteps)
        .expects(1L, TeamId(id = team_id))
        .returning(Seq(campaignStepWithChildren))

      (emailScheduledDAOService.getPreviousSentSteps(_: Long, _: Long, _: TeamId, _: Boolean)(using _: SRLogger))

        .expects(1, 1, TeamId(id = team_id), *, *)
        .returning(Success(Seq(previousFollowUp)))

      (campaignEditedPreviewEmailDAO.find)
        .expects(None, Some(1L), 1)
        .returning(Failure(Error))

      campaignPreviewService.previewGetStepsForProspect(
        campaign = campaign,
        prospectId = 1,
        teamId = 1,
        enable_ab_testing = true,
        allTrackingDomainsUsed = Seq(),
        org = org,
        stepAndVariantIds = List(),
        permittedAccountIdsForViewingTasks = permittedAccountIds,
        selected_campaign_email_setting_id = Some(CampaignEmailSettingsId(123L))
      ).map { result =>
        assert(true)
      }.recover {
        case e =>
          assert(e == Error)
      }
    }

    it("emailSettingDAO.find send back none") {

      (prospectDAOService.find)
        .expects(List(1L), List(), None, Some(1L), 1, None, 1, 0, true, false, None, logger)
        .returning(Success(Seq(prospectObject)))

      (prospectAccountDAO.find)
        .expects(1, 1)
        .returning(Some(ProspectAccountFixture.prospectAccount))
      (campaignDAOService.findOrderedSteps)
        .expects(1L, TeamId(id = team_id))
        .returning(Seq(campaignStepWithChildren))

      (emailScheduledDAOService.getPreviousSentSteps(_: Long, _: Long, _: TeamId, _: Boolean)(using _: SRLogger))

        .expects(1, 1, TeamId(id = team_id), *, *)
        .returning(Success(Seq(previousFollowUp)))

      (campaignEditedPreviewEmailDAO.find)
        .expects(None, Some(1L), 1)
        .returning(Success(Seq(campaignEditedPreviewEmail)))

      (channelSettingService.getChannelSettingSenderData(
        _: TeamId,
        _: CampaignId
      )(
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(TeamId(1L), CampaignId(1L), *, *)
        .returning(Future(List()))

      (emailSettingDAO.find(_: EmailSettingId, _: TeamId))
        .expects(EmailSettingId(1), team_id_VC)
        .returning(None)

      campaignPreviewService.previewGetStepsForProspect(
        campaign = campaign,
        prospectId = 1,
        teamId = 1,
        enable_ab_testing = true,
        allTrackingDomainsUsed = Seq(),
        org = org,
        stepAndVariantIds = List(),
        permittedAccountIdsForViewingTasks = permittedAccountIds,
        selected_campaign_email_setting_id = Some(CampaignEmailSettingsId(123L))
      ).map { result =>
        assert(true)
      }.recover {
        case e =>
          assert(e.getMessage == "None.get")
      }
    }

    it("templateService.render fail for body") {

      (prospectDAOService.find)
        .expects(List(1L), List(), None, Some(1L), 1, None, 1, 0, true, false, None, logger)
        .returning(Success(Seq(prospectObject)))

      (prospectAccountDAO.find)
        .expects(1, 1)
        .returning(Some(ProspectAccountFixture.prospectAccount))
      (campaignDAOService.findOrderedSteps)
        .expects(1L, TeamId(id = team_id))
        .returning(Seq(campaignStepWithChildren.copy(children = List())))

      (emailScheduledDAOService.getPreviousSentSteps(_: Long, _: Long, _: TeamId, _: Boolean)(using _: SRLogger))

        .expects(1, 1, TeamId(id = team_id), *, *)
        .returning(Success(Seq(previousFollowUp.copy(step_id = None))))

      (campaignEditedPreviewEmailDAO.find)
        .expects(None, Some(1L), 1)
        .returning(Success(Seq(campaignEditedPreviewEmail)))

      (channelSettingService.getChannelSettingSenderData(
        _: TeamId,
        _: CampaignId
      )(
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(TeamId(1L), CampaignId(1L), *, *)
        .returning(Future(List()))

      (emailSettingDAO.find(_: EmailSettingId, _: TeamId))
        .expects(EmailSettingId(1), team_id_VC)
        .returning(Some(emailSetting))

      (taskService.getCompletedTasksForProspectInCampaign(
        _: Long,
        _: Long,
        _: OrgId,
        _: Long,
        _: Seq[Long],
        _: String,
        _: List[ChannelSettingSenderDetails],
        _: Option[EmailSetting]
      )(
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(*, *, *, *, *, *, *, *, *, *)
        .returning(Future(Seq()))



      (calendarAppService.getSelectedCalendarDataFromDB(_:TeamId,_:OrgId,_:Option[CampaignId])(using _:SRLogger))
        .expects(
          teamIdValue,
          orgIdValue,
          campaignIdValue,
          *
        ).returning(Right(selectedCalendarData))


      (emailServiceCompanion.getBodies(_: Option[CampaignEditedPreviewEmail], _: Long,_:Option[CalendarAccountData],_:Option[SelectedCalendarData], _: Option[Long], _: EmailsScheduledUuid, _: Option[Long], _: Option[Long], _: ProspectObject, _: EmailOptionsForGetBodies)(_: SRLogger))
        .expects(
          Some(campaignEditedPreviewEmail),
          org.id,
          account.calendar_account_data,
          Some(selectedCalendarData),
          Some(1L),
          EmailsScheduledUuid(AppConfig.dummy_email_tracking_uuid),
          Some(campaign.id.toLong),
          Some(1L),
          prospectObject,
          emailOptionsForGetBodies,
          *
        )
        .returning(Failure(Error))

      (accountService.find(_: Long)(_: SRLogger))
        .expects(campaign.account_id, *)
        .returning(Success(account))


      campaignPreviewService.previewGetStepsForProspect(
        campaign = campaign,
        prospectId = 1,
        teamId = 1,
        enable_ab_testing = true,
        allTrackingDomainsUsed = Seq(),
        org = org,
        stepAndVariantIds = List(),
        permittedAccountIdsForViewingTasks = permittedAccountIds,
        selected_campaign_email_setting_id = Some(CampaignEmailSettingsId(123L))
      ).map { result =>
        logger.info(s"result ---------- $result")
        assert(result == Left(PreviewGetStepsForProspectError.InternalServerError(Error)))
      }
    }

    it("Success Right") {

      (prospectDAOService.find)
        .expects(List(1L), List(), None, Some(1L), 1, None, 1, 0, true, false, None, logger)
        .returning(Success(Seq(prospectObject)))

      (prospectAccountDAO.find)
        .expects(1, 1)
        .returning(Some(ProspectAccountFixture.prospectAccount))
      (campaignDAOService.findOrderedSteps)
        .expects(1L, TeamId(id = team_id))
        .returning(Seq(campaignStepWithChildren, campaignStepWithChildren.copy(id = 2, children = List())))

      (emailScheduledDAOService.getPreviousSentSteps(_: Long, _: Long, _: TeamId, _: Boolean)(using _: SRLogger))

        .expects(1, 1, TeamId(id = team_id), *, *)
        .returning(Success(Seq(previousFollowUp.copy(step_id = None))))

      (campaignEditedPreviewEmailDAO.find)
        .expects(None, Some(1L), 1)
        .returning(Success(Seq(campaignEditedPreviewEmail)))

      (channelSettingService.getChannelSettingSenderData(
        _: TeamId,
        _: CampaignId
      )(
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(TeamId(1L), CampaignId(1L), *, *)
        .returning(Future(List()))

      (emailSettingDAO.find(_: EmailSettingId, _: TeamId))
        .expects(EmailSettingId(1), team_id_VC)
        .returning(Some(emailSetting))

      (taskService.getCompletedTasksForProspectInCampaign(
        _: Long,
        _: Long,
        _: OrgId,
        _: Long,
        _: Seq[Long],
        _: String,
        _: List[ChannelSettingSenderDetails],
        _: Option[EmailSetting]
      )(
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(*, *, *, *, *, *, *, *, *, *)
        .returning(Future(Seq()))

      //      (campaignDAO.getCampaignChannelSetupData(_: TeamId, _: CampaignId)(_: ExecutionContext, _: SRLogger))
      //        .expects(team_id_VC, campaign_id_VC, *, *)
      //        .returning(Future.successful(List()))

      //      (campaignStepDAO.getOrderedSteps)
      //        .expects(Seq(campaignStepWithChildren, campaignStepWithChildren.copy(id = 2)), 1)
      //        .returning(List(campaignStepWithChildren, campaignStepWithChildren.copy(id = 2)))

      (calendarAppService.getSelectedCalendarDataFromDB(_: TeamId, _: OrgId, _: Option[CampaignId])(using _: SRLogger))
        .expects(
          teamIdValue,
          orgIdValue,
          campaignIdValue,
          *
        ).returning(Right(selectedCalendarData))

      (emailServiceCompanion.getBodies(_: Option[CampaignEditedPreviewEmail], _: Long,_:Option[CalendarAccountData],_:Option[SelectedCalendarData], _: Option[Long], _: EmailsScheduledUuid, _: Option[Long], _: Option[Long], _: ProspectObject, _: EmailOptionsForGetBodies)(_: SRLogger))
        .expects(
          Some(campaignEditedPreviewEmail),
          org.id,
          account.calendar_account_data,
          Some(selectedCalendarData),
          Some(1L),
          EmailsScheduledUuid(AppConfig.dummy_email_tracking_uuid),
          Some(campaign.id.toLong),
          Some(1L),
          prospectObject,
          emailOptionsForGetBodies,
          *
        )
        .returning(Success(successResponse))

      (emailServiceCompanion.getBodies(_: Option[CampaignEditedPreviewEmail], _: Long,_:Option[CalendarAccountData],_:Option[SelectedCalendarData], _: Option[Long], _: EmailsScheduledUuid, _: Option[Long], _: Option[Long], _: ProspectObject, _: EmailOptionsForGetBodies)(_: SRLogger))
        .expects(
          Some(campaignEditedPreviewEmail),
          org.id,
          account.calendar_account_data,
          Some(selectedCalendarData),
          Some(1L),
          EmailsScheduledUuid(AppConfig.dummy_email_tracking_uuid),
          Some(campaign.id.toLong),
          Some(1L),
          prospectObject,
          emailOptionsForGetBodies.copy(for_editable_preview = true),
          *
        )
        .returning(Success(successResponse))

      (emailServiceCompanion.getBodies(_: Option[CampaignEditedPreviewEmail], _: Long,_:Option[CalendarAccountData],_:Option[SelectedCalendarData], _: Option[Long], _: EmailsScheduledUuid, _: Option[Long], _: Option[Long], _: ProspectObject, _: EmailOptionsForGetBodies)(_: SRLogger))
        .expects(
          None,
          org.id,
          account.calendar_account_data,
          Some(selectedCalendarData),
          Some(1L),
          EmailsScheduledUuid(AppConfig.dummy_email_tracking_uuid),
          Some(campaign.id.toLong),
          Some(2L),
          prospectObject,
          *,
          *
        )
        .returning(Success(successResponse))

      (emailServiceCompanion.getBodies(_: Option[CampaignEditedPreviewEmail], _: Long,_:Option[CalendarAccountData],_:Option[SelectedCalendarData], _: Option[Long], _: EmailsScheduledUuid, _: Option[Long], _: Option[Long], _: ProspectObject, _: EmailOptionsForGetBodies)(_: SRLogger))
        .expects(
          None,
          org.id,
          account.calendar_account_data,
          Some(selectedCalendarData),
          Some(1L),
          EmailsScheduledUuid(AppConfig.dummy_email_tracking_uuid),
          Some(campaign.id.toLong),
          Some(2L),
          prospectObject,
          *,
          *
        )
        .returning(Success(successResponse))

      (accountService.find(_: Long)(_: SRLogger))
        .expects(campaign.account_id, *)
        .returning(Success(account))

      campaignPreviewService.previewGetStepsForProspect(
        campaign = campaign,
        prospectId = 1,
        teamId = 1,
        enable_ab_testing = true,
        allTrackingDomainsUsed = Seq(),
        org = org,
        stepAndVariantIds = List(StepAndVariantId(stepId = 1, variantId = 2), StepAndVariantId(stepId = 2, variantId = 2)),
        permittedAccountIdsForViewingTasks = permittedAccountIds,
        selected_campaign_email_setting_id = Some(CampaignEmailSettingsId(123L))
      ).map { result =>
        logger.info(s"result ---------- $result")
        assert(result.isRight)
      }
    }

    it("success with returning the previous sent email") {

      (prospectDAOService.find)
        .expects(List(1L), List(), None, Some(1L), 1, None, 1, 0, true, false, None, logger)
        .returning(Success(Seq(prospectObject)))

      (prospectAccountDAO.find)
        .expects(1, 1)
        .returning(Some(ProspectAccountFixture.prospectAccount))
      (campaignDAOService.findOrderedSteps)
        .expects(1L, TeamId(id = team_id))
        .returning(Seq(campaignStepWithChildren.copy(children = List())))

      (emailScheduledDAOService.getPreviousSentSteps(_: Long, _: Long, _: TeamId, _: Boolean)(using _: SRLogger))

        .expects(1, 1, TeamId(id = team_id), *, *)
        .returning(Success(Seq(previousFollowUp)))

      (campaignEditedPreviewEmailDAO.find)
        .expects(None, Some(1L), 1)
        .returning(Success(Seq(campaignEditedPreviewEmail)))

      (channelSettingService.getChannelSettingSenderData(
        _: TeamId,
        _: CampaignId
      )(
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(TeamId(1L), CampaignId(1L), *, *)
        .returning(Future(List()))

      (emailSettingDAO.find(_: EmailSettingId, _: TeamId))
        .expects(EmailSettingId(1), team_id_VC)
        .returning(Some(emailSetting))

      (taskService.getCompletedTasksForProspectInCampaign(
        _: Long,
        _: Long,
        _: OrgId,
        _: Long,
        _: Seq[Long],
        _: String,
        _: List[ChannelSettingSenderDetails],
        _: Option[EmailSetting]
      )(
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(*, *, *, *, *, *, *, *, *, *)
        .returning(Future(Seq()))
      (calendarAppService.getSelectedCalendarDataFromDB(_: TeamId, _: OrgId, _: Option[CampaignId])(using _: SRLogger))
        .expects(
          teamIdValue,
          orgIdValue,
          campaignIdValue,
          *
        ).returning(Right(selectedCalendarData))


      (accountService.find(_: Long)(_: SRLogger))
        .expects(campaign.account_id, *)
        .returning(Success(account))

      campaignPreviewService.previewGetStepsForProspect(
        campaign = campaign,
        prospectId = 1,
        teamId = 1,
        enable_ab_testing = true,
        allTrackingDomainsUsed = Seq(),
        org = org,
        permittedAccountIdsForViewingTasks = permittedAccountIds,
        selected_campaign_email_setting_id = Some(CampaignEmailSettingsId(123L)),
        stepAndVariantIds = List(StepAndVariantId(stepId = 1, variantId = 2))
      ).map { result =>
        logger.info(s"result ---------- $result")
        assert(result.isRight)
      }
    }

    it("Success with previously sent step") {

      (prospectDAOService.find)
        .expects(List(1L), List(), None, Some(1L), 1, None, 1, 0, true, false, None, logger)
        .returning(Success(Seq(prospectObject)))

      (prospectAccountDAO.find)
        .expects(1, 1)
        .returning(Some(ProspectAccountFixture.prospectAccount))
      (campaignDAOService.findOrderedSteps)
        .expects(1L, TeamId(id = team_id))
        .returning(Seq(campaignStepWithChildren, campaignStepWithChildren.copy(id = 2, children = List())))

      (emailScheduledDAOService.getPreviousSentSteps(_: Long, _: Long, _: TeamId, _: Boolean)(using _: SRLogger))

        .expects(1, 1, TeamId(id = team_id), *, *)
        .returning(Success(Seq(previousFollowUp)))

      (campaignEditedPreviewEmailDAO.find)
        .expects(None, Some(1L), 1)
        .returning(Success(Seq(campaignEditedPreviewEmail)))

      (channelSettingService.getChannelSettingSenderData(
        _: TeamId,
        _: CampaignId
      )(
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(TeamId(1L), CampaignId(1L), *, *)
        .returning(Future(List()))


      (emailSettingDAO.find(_: EmailSettingId, _: TeamId))
        .expects(EmailSettingId(1), team_id_VC)
        .returning(Some(emailSetting))

      (taskService.getCompletedTasksForProspectInCampaign(
        _: Long,
        _: Long,
        _: OrgId,
        _: Long,
        _: Seq[Long],
        _: String,
        _: List[ChannelSettingSenderDetails],
        _: Option[EmailSetting]
      )(
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(*, *, *, *, *, *, *, *, *, *)
        .returning(Future(Seq()))


      (calendarAppService.getSelectedCalendarDataFromDB(_: TeamId, _: OrgId, _: Option[CampaignId])(using _: SRLogger))
        .expects(
          teamIdValue,
          orgIdValue,
          campaignIdValue,
          *
        ).returning(Right(selectedCalendarData))

      (emailServiceCompanion.getBodies(_: Option[CampaignEditedPreviewEmail], _: Long,_:Option[CalendarAccountData],_:Option[SelectedCalendarData], _: Option[Long], _: EmailsScheduledUuid, _: Option[Long], _: Option[Long], _: ProspectObject, _: EmailOptionsForGetBodies)(_: SRLogger))
        .expects(
          None,
          org.id,
          account.calendar_account_data,
          Some(selectedCalendarData),
          Some(1L),
          EmailsScheduledUuid(AppConfig.dummy_email_tracking_uuid),
          Some(campaign.id.toLong),
          Some(2L),
          prospectObject,
          *,
          *
        )
        .returning(Success(successResponse))

      (emailServiceCompanion.getBodies(_: Option[CampaignEditedPreviewEmail], _: Long,_:Option[CalendarAccountData],_:Option[SelectedCalendarData], _: Option[Long], _: EmailsScheduledUuid, _: Option[Long], _: Option[Long], _: ProspectObject, _: EmailOptionsForGetBodies)(_: SRLogger))
        .expects(
          None,
          org.id,
          account.calendar_account_data,
          Some(selectedCalendarData),
          Some(1L),
          EmailsScheduledUuid(AppConfig.dummy_email_tracking_uuid),
          Some(campaign.id.toLong),
          Some(2L),
          prospectObject,
          *,
          *
        )
        .returning(Success(successResponse))

      (accountService.find(_: Long)(_: SRLogger))
        .expects(campaign.account_id, *)
        .returning(Success(account))

      campaignPreviewService.previewGetStepsForProspect(
        campaign = campaign,
        prospectId = 1,
        teamId = 1,
        enable_ab_testing = true,
        allTrackingDomainsUsed = Seq(),
        org = org,
        permittedAccountIdsForViewingTasks = permittedAccountIds,
        stepAndVariantIds = List(StepAndVariantId(stepId = 1, variantId = 1), StepAndVariantId(stepId = 2, variantId = 1)),
        selected_campaign_email_setting_id = Some(CampaignEmailSettingsId(123L))
      ).map { result =>
        logger.info(s"result ---------- $result")
        assert(result.isRight)
      }
    }

    it("no previous step sent with success") {

      (prospectDAOService.find)
        .expects(List(1L), List(), None, Some(1L), 1, None, 1, 0, true, false, None, logger)
        .returning(Success(Seq(prospectObject)))

      (prospectAccountDAO.find)
        .expects(1, 1)
        .returning(Some(ProspectAccountFixture.prospectAccount))
      (campaignDAOService.findOrderedSteps)
        .expects(1L, TeamId(id = team_id))
        .returning(Seq(campaignStepWithChildren, campaignStepWithChildren.copy(id = 2, children = List())))

      (emailScheduledDAOService.getPreviousSentSteps(_: Long, _: Long, _: TeamId, _: Boolean)(using _: SRLogger))

        .expects(1, 1, TeamId(id = team_id), *, *)
        .returning(Success(Seq()))

      (campaignEditedPreviewEmailDAO.find)
        .expects(None, Some(1L), 1)
        .returning(Success(Seq(campaignEditedPreviewEmail)))

      (channelSettingService.getChannelSettingSenderData(
        _: TeamId,
        _: CampaignId
      )(
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(TeamId(1L), CampaignId(1L), *, *)
        .returning(Future(List()))

      (emailSettingDAO.find(_: EmailSettingId, _: TeamId))
        .expects(EmailSettingId(1), team_id_VC)
        .returning(Some(emailSetting))

      (taskService.getCompletedTasksForProspectInCampaign(
        _: Long,
        _: Long,
        _: OrgId,
        _: Long,
        _: Seq[Long],
        _: String,
        _: List[ChannelSettingSenderDetails],
        _: Option[EmailSetting]
      )(
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(*, *, *, *, *, *, *, *, *, *)
        .returning(Future(Seq()))

      (calendarAppService.getSelectedCalendarDataFromDB(_: TeamId, _: OrgId, _: Option[CampaignId])(using _: SRLogger))
        .expects(
          teamIdValue,
          orgIdValue,
          campaignIdValue,
          *
        ).returning(Right(selectedCalendarData))

      (emailServiceCompanion.getBodies(_: Option[CampaignEditedPreviewEmail], _: Long,_:Option[CalendarAccountData],_:Option[SelectedCalendarData], _: Option[Long], _: EmailsScheduledUuid, _: Option[Long], _: Option[Long], _: ProspectObject, _: EmailOptionsForGetBodies)(_: SRLogger))
        .expects(
          Some(campaignEditedPreviewEmail),
          org.id,
          account.calendar_account_data,
          Some(selectedCalendarData),
          Some(1L),
          EmailsScheduledUuid(AppConfig.dummy_email_tracking_uuid),
          Some(campaign.id.toLong),
          Some(1L),
          prospectObject,
          emailOptionsForGetBodies,
          *
        )
        .returning(Success(successResponse))

      (emailServiceCompanion.getBodies(_: Option[CampaignEditedPreviewEmail], _: Long,_:Option[CalendarAccountData],_:Option[SelectedCalendarData], _: Option[Long], _: EmailsScheduledUuid, _: Option[Long], _: Option[Long], _: ProspectObject, _: EmailOptionsForGetBodies)(_: SRLogger))
        .expects(
          Some(campaignEditedPreviewEmail),
          org.id,
          account.calendar_account_data,
          Some(selectedCalendarData),
          Some(1L),
          EmailsScheduledUuid(AppConfig.dummy_email_tracking_uuid),
          Some(campaign.id.toLong),
          Some(1L),
          prospectObject,
          emailOptionsForGetBodies.copy(for_editable_preview = true),
          *
        )
        .returning(Success(successResponse))

      (emailServiceCompanion.getBodies(_: Option[CampaignEditedPreviewEmail], _: Long,_:Option[CalendarAccountData],_:Option[SelectedCalendarData], _: Option[Long], _: EmailsScheduledUuid, _: Option[Long], _: Option[Long], _: ProspectObject, _: EmailOptionsForGetBodies)(_: SRLogger))
        .expects(
          None,
          org.id,
          account.calendar_account_data,
          Some(selectedCalendarData),
          Some(1L),
          EmailsScheduledUuid(AppConfig.dummy_email_tracking_uuid),
          Some(campaign.id.toLong),
          Some(2L),
          prospectObject,
          *,
          *
        )
        .returning(Success(successResponse))

      (emailServiceCompanion.getBodies(_: Option[CampaignEditedPreviewEmail], _: Long,_:Option[CalendarAccountData],_:Option[SelectedCalendarData], _: Option[Long], _: EmailsScheduledUuid, _: Option[Long], _: Option[Long], _: ProspectObject, _: EmailOptionsForGetBodies)(_: SRLogger))
        .expects(
          None,
          org.id,
          account.calendar_account_data,
          Some(selectedCalendarData),
          Some(1L),
          EmailsScheduledUuid(AppConfig.dummy_email_tracking_uuid),
          Some(campaign.id.toLong),
          Some(2L),
          prospectObject,
          *,
          *
        )
        .returning(Success(successResponse))

      (accountService.find(_: Long)(_: SRLogger))
        .expects(campaign.account_id, *)
        .returning(Success(account))

      campaignPreviewService.previewGetStepsForProspect(
        campaign = campaign,
        prospectId = 1,
        teamId = 1,
        enable_ab_testing = true,
        allTrackingDomainsUsed = Seq(),
        org = org,
        permittedAccountIdsForViewingTasks = permittedAccountIds,
        stepAndVariantIds = List(StepAndVariantId(stepId = 1, variantId = 1), StepAndVariantId(stepId = 2, variantId = 1)),
        selected_campaign_email_setting_id = Some(CampaignEmailSettingsId(123L))
      ).map { result =>
        logger.info(s"result ---------- $result")
        assert(result.isRight)
      }
    }

    it("success with no previous sent step or edited step") {

      (prospectDAOService.find)
        .expects(List(1L), List(), None, Some(1L), 1, None, 1, 0, true, false, None, logger)
        .returning(Success(Seq(prospectObject)))

      (prospectAccountDAO.find)
        .expects(1, 1)
        .returning(Some(ProspectAccountFixture.prospectAccount))
      (campaignDAOService.findOrderedSteps)
        .expects(1L, TeamId(id = team_id))
        .returning(Seq(campaignStepWithChildren, campaignStepWithChildren.copy(id = 2, children = List())))

      (emailScheduledDAOService.getPreviousSentSteps(_: Long, _: Long, _: TeamId, _: Boolean)(using _: SRLogger))

        .expects(1, 1, TeamId(id = team_id), *, *)
        .returning(Success(Seq()))

      (campaignEditedPreviewEmailDAO.find)
        .expects(None, Some(1L), 1)
        .returning(Success(Seq(campaignEditedPreviewEmail.copy(stepId = 4))))

      (channelSettingService.getChannelSettingSenderData(
        _: TeamId,
        _: CampaignId
      )(
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(TeamId(1L), CampaignId(1L), *, *)
        .returning(Future(List()))

      (emailSettingDAO.find(_: EmailSettingId, _: TeamId))
        .expects(EmailSettingId(1), team_id_VC)
        .returning(Some(emailSetting))

      (taskService.getCompletedTasksForProspectInCampaign(
        _: Long,
        _: Long,
        _: OrgId,
        _: Long,
        _: Seq[Long],
        _: String,
        _: List[ChannelSettingSenderDetails],
        _: Option[EmailSetting]
      )(
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(*, *, *, *, *, *, *, *, *, *)
        .returning(Future(Seq()))

      (calendarAppService.getSelectedCalendarDataFromDB(_: TeamId, _: OrgId, _: Option[CampaignId])(using _: SRLogger))
        .expects(
          teamIdValue,
          orgIdValue,
          campaignIdValue,
          *
        ).returning(Right(selectedCalendarData))

      (emailServiceCompanion.getBodies(_: Option[CampaignEditedPreviewEmail], _: Long,_:Option[CalendarAccountData],_:Option[SelectedCalendarData], _: Option[Long], _: EmailsScheduledUuid, _: Option[Long], _: Option[Long], _: ProspectObject, _: EmailOptionsForGetBodies)(_: SRLogger))
        .expects(
          None,
          org.id,
          account.calendar_account_data,
          Some(selectedCalendarData),
          Some(1L),
          EmailsScheduledUuid(AppConfig.dummy_email_tracking_uuid),
          Some(campaign.id.toLong),
          Some(1L),
          prospectObject,
          emailOptionsForGetBodies.copy(bodyTemplate = "Variant body", subjectTemplate = "variant subject"),
          *
        )
        .returning(Success(successResponse))

      (emailServiceCompanion.getBodies(_: Option[CampaignEditedPreviewEmail], _: Long,_:Option[CalendarAccountData],_:Option[SelectedCalendarData], _: Option[Long], _: EmailsScheduledUuid, _: Option[Long], _: Option[Long], _: ProspectObject, _: EmailOptionsForGetBodies)(_: SRLogger))
        .expects(
          None,
          org.id,
          account.calendar_account_data,
          Some(selectedCalendarData),
          Some(1L),
          EmailsScheduledUuid(AppConfig.dummy_email_tracking_uuid),
          Some(campaign.id.toLong),
          Some(1L),
          prospectObject,
          *,
          *
        )
        .returning(Success(successResponse))

      (templateService.render(_: String, _: ProspectObject, _: InternalMergeTagValuesForProspect, _: ChannelType)(using _: SRLogger))
        .expects("Hey Animesh", prospectObject, *, *, *)
        .returning(Success("variant subject"))

      (templateService.render(_: String, _: ProspectObject, _: InternalMergeTagValuesForProspect, _: ChannelType)(using _: SRLogger))
        .expects("Variant body", prospectObject, *, *, *)
        .returning(Success("Variant body"))

      (emailServiceCompanion.getBodies(_: Option[CampaignEditedPreviewEmail], _: Long,_:Option[CalendarAccountData],_:Option[SelectedCalendarData], _: Option[Long], _: EmailsScheduledUuid, _: Option[Long], _: Option[Long], _: ProspectObject, _: EmailOptionsForGetBodies)(_: SRLogger))
        .expects(
          None,
          org.id,
          account.calendar_account_data,
          Some(selectedCalendarData),
          Some(1L),
          EmailsScheduledUuid(AppConfig.dummy_email_tracking_uuid),
          Some(campaign.id.toLong),
          Some(2L),
          prospectObject,
          *,
          *
        )
        .returning(Success(successResponse))

      (emailServiceCompanion.getBodies(_: Option[CampaignEditedPreviewEmail], _: Long,_:Option[CalendarAccountData],_:Option[SelectedCalendarData], _: Option[Long], _: EmailsScheduledUuid, _: Option[Long], _: Option[Long], _: ProspectObject, _: EmailOptionsForGetBodies)(_: SRLogger))
        .expects(
          None,
          org.id,
          account.calendar_account_data,
          Some(selectedCalendarData),
          Some(1L),
          EmailsScheduledUuid(AppConfig.dummy_email_tracking_uuid),
          Some(campaign.id.toLong),
          Some(2L),
          prospectObject,
          *,
          *
        )
        .returning(Success(successResponse))

      (accountService.find(_: Long)(_: SRLogger))
      .expects(campaign.account_id, *)
        .returning(Success(account))

      campaignPreviewService.previewGetStepsForProspect(
        campaign = campaign,
        prospectId = 1,
        teamId = 1,
        enable_ab_testing = true,
        allTrackingDomainsUsed = Seq(),
        org = org,
        permittedAccountIdsForViewingTasks = permittedAccountIds,
        stepAndVariantIds = List(StepAndVariantId(stepId = 1, variantId = 1), StepAndVariantId(stepId = 2, variantId = 1)),
        selected_campaign_email_setting_id = Some(CampaignEmailSettingsId(123L))
      ).map { result =>
        logger.info(s"result ---------- $result")
        assert(result.isRight)
      }
    }

    it("should fail when no stepAndVariantIds are passed") {

      (prospectDAOService.find)
        .expects(List(1L), List(), None, Some(1L), 1, None, 1, 0, true, false, None, logger)
        .returning(Success(Seq(prospectObject)))

      (prospectAccountDAO.find)
        .expects(1, 1)
        .returning(Some(ProspectAccountFixture.prospectAccount))
      (campaignDAOService.findOrderedSteps)
        .expects(1L, TeamId(id = team_id))
        .returning(Seq(campaignStepWithChildren, campaignStepWithChildren.copy(id = 2, children = List())))

      (emailScheduledDAOService.getPreviousSentSteps(_: Long, _: Long, _: TeamId, _: Boolean)(using _: SRLogger))

        .expects(1, 1, TeamId(id = team_id), *, *)
        .returning(Success(Seq()))

      (campaignEditedPreviewEmailDAO.find)
        .expects(None, Some(1L), 1)
        .returning(Success(Seq(campaignEditedPreviewEmail.copy(stepId = 4))))

      (channelSettingService.getChannelSettingSenderData(
        _: TeamId,
        _: CampaignId
      )(
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(TeamId(1L), CampaignId(1L), *, *)
        .returning(Future(List()))

      (emailSettingDAO.find(_: EmailSettingId, _: TeamId))
        .expects(EmailSettingId(1), team_id_VC)
        .returning(Some(emailSetting))

      (taskService.getCompletedTasksForProspectInCampaign(
        _: Long,
        _: Long,
        _: OrgId,
        _: Long,
        _: Seq[Long],
        _: String,
        _: List[ChannelSettingSenderDetails],
        _: Option[EmailSetting]
      )(
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(*, *, *, *, *, *, *, *, *, *)
        .returning(Future(Seq()))

      //      (campaignDAO.getCampaignChannelSetupData(_: TeamId, _: CampaignId)(_: ExecutionContext, _: SRLogger))
      //        .expects(team_id_VC, campaign_id_VC, *, *)
      //        .returning(Future.successful(List()))

      //      (campaignStepDAO.getOrderedSteps)
      //        .expects(Seq(campaignStepWithChildren, campaignStepWithChildren.copy(id = 2)), 1)
      //        .returning(List(campaignStepWithChildren, campaignStepWithChildren.copy(id = 2)))

      (accountService.find(_:Long)(_: SRLogger))
        .expects(campaign.account_id, *)
        .returning(Success(account))

      (calendarAppService.getSelectedCalendarDataFromDB(_: TeamId, _: OrgId, _: Option[CampaignId])(using _: SRLogger))
        .expects(
          teamIdValue,
          orgIdValue,
          campaignIdValue,
          *
        ).returning(Right(selectedCalendarData))



      campaignPreviewService.previewGetStepsForProspect(
        campaign = campaign,
        prospectId = 1,
        teamId = 1,
        enable_ab_testing = true,
        allTrackingDomainsUsed = Seq(),
        org = org,
        stepAndVariantIds = List(),
        permittedAccountIdsForViewingTasks = permittedAccountIds,
        selected_campaign_email_setting_id = Some(CampaignEmailSettingsId(123L))
      ).map {
        case Left(PreviewGetStepsForProspectError.ValidationError(err_message)) =>
          assert(err_message == "Current variant can't be empty")

        case _ =>
          assert(false)
      }
    }

  }

  describe("Test makeHTML") {

    it("should replace newlines with HTML <br /> tags") {

      val body =
        """
          |We are excited to introduce our latest innovation in the world of photography - a 35mm SLR camera that is set to revolutionize the way you capture life's precious moments. With its sleek design, advanced features, and unparalleled image quality, this camera is a game-changer for both professionals and enthusiasts alike.
          |
          |Our new camera boasts a range of innovative features, including a high-precision shutter, interchangeable lenses, and a built-in light meter. Whether you're a seasoned photographer or just starting out, this camera is designed to help you take your photography to the next level. We believe that this camera has the potential to make a significant impact on the photography industry, and we would love the opportunity to discuss how it can benefit your business.
          |
          |If you're interested in learning more, please don't hesitate to contact us.
          |
          |We look forward to hearing from you soon.
          |
          |""".stripMargin

      val htmlBody = CampaignPreviewService.makeHTML(
        text = body
      ).stripMargin

      val expectedOutput =
        """<html>
          | <head></head>
          | <body>
          |  <br>
          |  We are excited to introduce our latest innovation in the world of photography - a 35mm SLR camera that is set to revolutionize the way you capture life's precious moments. With its sleek design, advanced features, and unparalleled image quality, this camera is a game-changer for both professionals and enthusiasts alike.<br><br>
          |  Our new camera boasts a range of innovative features, including a high-precision shutter, interchangeable lenses, and a built-in light meter. Whether you're a seasoned photographer or just starting out, this camera is designed to help you take your photography to the next level. We believe that this camera has the potential to make a significant impact on the photography industry, and we would love the opportunity to discuss how it can benefit your business.<br><br>
          |  If you're interested in learning more, please don't hesitate to contact us.<br><br>
          |  We look forward to hearing from you soon.<br><br>
          | </body>
          |</html>""".stripMargin


      assert(expectedOutput == htmlBody)

    }

  }

}
