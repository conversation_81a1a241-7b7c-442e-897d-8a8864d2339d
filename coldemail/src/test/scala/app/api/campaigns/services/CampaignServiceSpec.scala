package app.api.campaigns.services

import org.apache.pekko.actor.ActorSystem
import org.apache.pekko.stream.{ActorMaterializer, Materializer}
import api.accounts.email.models.EmailServiceProvider
import api.accounts.models.{AccountId, OrgId, ProspectAccountUuid}
import api.campaigns.services.{CallSettingSenderDetails, CampaignCacheService, CampaignDAOService, CampaignId, CampaignService, ChannelSettingSenderDetails, ChannelSettingService, GetTestStepsError, LinkedinSettingSenderDetails, PreviewGetStepsForProspectError, SmsSettingSenderDetails, StepAndVariantId, ValidateCampaignTemplateError, WhatsappSettingSenderDetails}
import api.accounts.{Account, AccountMetadata, AccountService, AccountType, OrgCountData, OrgMetadata, OrgPlan, OrgSettings, OrganizationRole, OrganizationWithCurrentData, ReplyHandling, TeamAccountRole, TeamId, TeamMember, UpdateAccountProfileDuringOnboarding}
import api.accounts.{Account, AccountMetadata, AccountService, AccountType, AccountUuid, OrgCountData, OrgMetadata, OrgPlan, OrgSettings, OrganizationRole, OrganizationWithCurrentData, ReplyHandling, TeamAccountRole, TeamId, TeamMember, UpdateAccountProfileDuringOnboarding}
import api.emails.{EmailScheduled, EmailScheduledDAO, EmailScheduledNewAfterSaving, EmailSetting, EmailSettingDAO}
import api.accounts.service.ResetUserCacheUtil
import api.campaigns.{Campaign, CampaignDAO, CampaignEditedPreviewEmail, CampaignEmailSettings, CampaignEmailSettingsUuid, CampaignIdAndTeamId, CampaignProspectDAO, CampaignSendReportsDAO, CampaignSettings, CampaignStepDAO, CampaignStepDAOService, CampaignStepVariantDAO, CampaignStepVariantForScheduling, CampaignStepWithChildren, ChannelSettingUuid, PreviousFollowUp}
import api.campaigns.dao.{CampaignEmailSettingsDAO, CampaignSchedulingMetadataDAO, CampaignSendingVolumeLogsDAO, InternalSchedulerRunLogDAO}
import api.campaigns.models.{CampaignEmailSettingsId, CampaignStepData, CampaignStepType, CampaignType, ChannelStepType, InactiveCampaignCheckType, PreviousFollowUpData}
import api.columns.InternalMergeTagValuesForProspect
import api.emails.models.EmailSettingUuid
import api.emails.services.{EmailScheduledService, EmailSettingJedisService, SelectAndPublishForDeletionService}
import api.prospects.dao_service.{ProspectDAOService, ProspectDAOServiceV2}
import api.prospects.models.ProspectCategory
import api.prospects.{ProspectAccount, ProspectService, ProspectUpdateCategoryTemp, ProspectUuid}
import api.scheduler_report.SchedulerIntegrityService
import api.spammonitor.dao.EmailSendingStatusDAO
import api.team_inbox.service.ReplySentimentService
import api.tasks.models.TaskPriority
import api.tasks.services.TaskService
import api.team.TeamUuid
import api.team_inbox.dao_service.ReplySentimentDAOService
import api.triggers.Trigger
import app.test_fixtures.accounts.OrgCountDataFixture
import app.test_fixtures.organizationa.{OrgMetadataFixture, OrgPlanFixture}
import app.test_fixtures.prospect.ProspectFixtures
import eventframework.{ProspectObject, ProspectObjectInternal}
import io.smartreach.esp.api.emails.EmailSettingId
import org.joda.time.DateTime
import org.scalamock.scalatest.AsyncMockFactory
import org.scalatest.funspec.AsyncFunSpec
import play.api.libs.json.Json
import play.api.libs.ws.WSClient
import play.api.libs.ws.ahc.AhcWSClient
import sr_scheduler.CampaignStatus
import sr_scheduler.models.{CampaignEmailPriority, ChannelType}
import utils.SRLogger
import utils.cache_utils.model.CampaignUseStatusForEmailSetting
import utils.cronjobs.InactiveCampaignData
import utils.dbutils.DBUtils
import utils.email.{EmailService, EmailsScheduledDeleteService}
import utils.email_notification.service.EmailNotificationService
import utils.mq.MQCampaignAISequenceGenerator
import utils.mq.email.MQEmailMessage
import utils.sr_product_usage_data.services.SrUserFeatureUsageEventService
import utils.uuid.SrUuidUtils
import utils.uuid.services.SrUuidService
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}
import org.scalactic.Prettifier.default
import utils.GCP.CloudStorage
import utils.mq.webhook.mq_activity_trigger.MQActivityTriggerPublisher
import utils.testapp.TestAppExecutionContext


class CampaignServiceSpec extends AsyncFunSpec with AsyncMockFactory {

  given logger: SRLogger = new SRLogger(logRequestId = "CampaignServiceSpec: ")

  val campaignProspectDAO = mock[CampaignProspectDAO]
  val prospectDAOService: ProspectDAOService = mock[ProspectDAOService]
  val prospectDAOServiceV2: ProspectDAOServiceV2 = mock[ProspectDAOServiceV2]
  val campaignStepVariantDAO = mock[CampaignStepVariantDAO]
  val emailScheduledDAO = mock[EmailScheduledDAO]
  val emailSettingDAO = mock[EmailSettingDAO]
  val emailService = mock[EmailService]
  implicit lazy val system: ActorSystem = TestAppExecutionContext.actorSystem
  implicit lazy val materializer: Materializer = TestAppExecutionContext.actorMaterializer
  val resetUserCacheUtil = mock[ResetUserCacheUtil]
  val triggerDAO = mock[Trigger]
  val campaignDAO = mock[CampaignDAO]
  val srUserFeatureUsageEventService = mock[SrUserFeatureUsageEventService]
  val emailSendingStatusDAO = mock[EmailSendingStatusDAO]
  val campaignCacheServiceMock = mock[CampaignCacheService]
  val accountService = mock[AccountService]
  val replySentimentDAOService = mock[ReplySentimentDAOService]
  val campaignSendReportsDAO = mock[CampaignSendReportsDAO]
  val taskService = mock[TaskService]
  val channelSettingService = mock[ChannelSettingService]
  val srUuidUtils = mock[SrUuidUtils]
  val campaignDAOService = mock[CampaignDAOService]
  val campaignSchedulingMetadataDAO = mock[CampaignSchedulingMetadataDAO]
  val campaignSendingVolumeLogsDAO = mock[CampaignSendingVolumeLogsDAO]
  val cloudStorage = mock[CloudStorage]
  val emailScheduledService = mock[EmailScheduledService]
  val mqActivityTriggerPublisher = mock[MQActivityTriggerPublisher]


  val campaignStepDAO = mock[CampaignStepDAO]
  val dbUtils = mock[DBUtils]
  val emailSettingJedisService = mock[EmailSettingJedisService]
  //  val prospectService = mock[ProspectService]
  //  val campaignProspectAssign = mock[CampaignProspectAssign]
  val srUuidService = mock[SrUuidService]
  val srRollingUpdateCoreService = mock[SrRollingUpdateCoreService]
  val internalSchedulerRunLogDAO = mock[InternalSchedulerRunLogDAO]

  val schedulerIntegrityService = mock[SchedulerIntegrityService]
  val emailNotificationService = mock[EmailNotificationService]

  val selectAndPublishForDeletionService = mock[SelectAndPublishForDeletionService]

  val campaignStepDAOService = mock[CampaignStepDAOService]
  val emailsScheduledDeleteService = mock[EmailsScheduledDeleteService]
  val prospectUpdateCategoryTemp = mock[ProspectUpdateCategoryTemp]

  val campaignService = new CampaignService(
    srRollingUpdateCoreService = srRollingUpdateCoreService,
    campaignProspectDAO = campaignProspectDAO,
    prospectDAOService = prospectDAOService,
    prospectDAOServiceV2 = prospectDAOServiceV2,
    campaignStepVariantDAO = campaignStepVariantDAO,
    emailSettingJedisService = emailSettingJedisService,
    emailScheduledDAO = emailScheduledDAO,
    emailSettingDAO = emailSettingDAO,
    resetUserCacheUtil = resetUserCacheUtil,
    cloudStorage = cloudStorage,
    campaignSendReportsDAO = campaignSendReportsDAO,
    triggerDAO = triggerDAO,
    campaignDAO = campaignDAO,
    emailSendingStatusDAO = emailSendingStatusDAO,
    srUserFeatureUsageEventService = srUserFeatureUsageEventService,
    campaignCacheService = campaignCacheServiceMock,
    accountService = accountService,
    replySentimentDAOService = replySentimentDAOService,
    taskService = taskService,
    dbUtils = dbUtils,
    channelSettingService = channelSettingService,
    srUuidUtils = srUuidUtils,
    campaignDAOService = campaignDAOService,
    campaignStepDAO = campaignStepDAO,
    campaignSchedulingMetadataDAO = campaignSchedulingMetadataDAO,
    srUuidService = srUuidService,
    campaignSendingVolumeLogsDAO = campaignSendingVolumeLogsDAO,
    internalSchedulerRunLogDAO = internalSchedulerRunLogDAO,
    schedulerIntegrityService = schedulerIntegrityService,
    emailNotificationService = emailNotificationService,
    selectAndPublishForDeletionService = selectAndPublishForDeletionService,
    campaignStepDaoService = campaignStepDAOService,
    mqActivityTriggerPublisher = mqActivityTriggerPublisher,
    emailScheduledService = emailScheduledService,
    prospectUpdateCategoryTemp = prospectUpdateCategoryTemp,
    emailsScheduledDeleteService = emailsScheduledDeleteService
  )


  val counts: OrgCountData = OrgCountDataFixture.orgCountData_default

  val permittedAccountIds = Seq(11L, 13L, 2L, 3L)
  val team_id: Long = 1 // All ids are 1 in this test, we can take this up later to make them prime

  val campaign_id: Long = 1 // All ids are 1 in this test, we can take this up later to make them prime
  val team_id_VC = TeamId(id = team_id)
  val campaign_id_VC = CampaignId(id = campaign_id)
  val campaignSettings = CampaignSettings(
    campaign_email_settings = List(
      CampaignEmailSettings(
        campaign_id = CampaignId(campaign_id),
        sender_email_setting_id = EmailSettingId(1),
        receiver_email_setting_id = EmailSettingId(1),
        team_id = TeamId(team_id),
        uuid = CampaignEmailSettingsUuid("temp_setting_id"),
        id = CampaignEmailSettingsId(123),
        sender_email = "<EMAIL>",
        receiver_email = "<EMAIL>",
        max_emails_per_day_from_email_account = 400,
        signature = Some("emailsignature"),
        error = None,
        from_name = None
      )
    ),
    campaign_linkedin_settings = List(
      LinkedinSettingSenderDetails(
        channel_setting_uuid = ChannelSettingUuid(uuid = "1"),
        team_id = TeamId(id = team_id),
        email = "<EMAIL>",
        first_name = "gokulnath",
        last_name = "S",
        linkedin_profile_url = Some("www.linekdin.com/SRTest"),
        automation_enabled = false
      ), LinkedinSettingSenderDetails(
        channel_setting_uuid = ChannelSettingUuid(uuid = "2"),
        team_id = TeamId(id = team_id),
        email = "<EMAIL>",
        first_name = "Guru",
        last_name = "D",
        linkedin_profile_url = Some("www.linekdin.com/guru"),
        automation_enabled = true
      )
    ),
    campaign_call_settings = List(
      CallSettingSenderDetails(
        channel_setting_uuid = ChannelSettingUuid(uuid = "1"),
        team_id = TeamId(id = team_id),
        phone_number = Some("0123456789"),
        first_name = "gokulnath",
        last_name = "S"
      )
    ),
    campaign_whatsapp_settings = List(
      WhatsappSettingSenderDetails(
        channel_setting_uuid = ChannelSettingUuid(uuid = "1"),
        team_id = TeamId(id = team_id),
        phone_number = "0123456789",
        first_name = "gokulnath",
        last_name = "S"
      )
    ),
    campaign_sms_settings = List(
      SmsSettingSenderDetails(
        channel_setting_uuid = ChannelSettingUuid(uuid = "1"),
        team_id = TeamId(id = team_id),
        phone_number = "0123456789",
        first_name = "gokulnath",
        last_name = "S"
      )
    ),
    ai_sequence_status = None,
    timezone = "UTC",
    daily_from_time = 8,
    daily_till_time = 12,
    sending_holiday_calendar_id = None,
    days_preference = List(false, true, true, true, true, false, false),
    mark_completed_after_days = 4,
    max_emails_per_day = 40,
    open_tracking_enabled = false,
    click_tracking_enabled = false,
    enable_email_validation = false,
    ab_testing_enabled = false,
    warmup_started_at = None,
    warmup_length_in_days = None,
    warmup_starting_email_count = None,
    show_soft_start_setting = false,
    schedule_start_at = None,
    schedule_start_at_tz = None,
    email_priority = CampaignEmailPriority.FIRST_EMAIL,
    send_plain_text_email = Some(false),
    campaign_type = CampaignType.MultiChannel,
    append_followups = true,
    opt_out_msg = "{{unsubscribe_link}}",
    opt_out_is_text = false,
    add_prospect_to_dnc_on_opt_out = true,
    triggers = Seq(),
    sending_mode = None,
    selected_calendar_data = None
  )

  val orgPlan = OrgPlanFixture.orgPlanFixture

  val orgMetadata = OrgMetadataFixture.orgMetadataFixture2

  val orgSettings = OrgSettings(
    enable_ab_testing = true,
    disable_force_send = false,
    bulk_sender = false,
    allow_2fa = false,
    show_2fa_setting = false,
    enforce_2fa = false,
    allow_native_crm_integration = false,
    agency_option_allow_changing = false,
    agency_option_show = false)

  val org = OrganizationWithCurrentData(
    id = 1,
    name = "Animesh",
    owner_account_id = 1,
    counts = counts,
    settings = orgSettings,
    plan = orgPlan,
    is_agency = true,
    trial_ends_at = DateTime.now().plusDays(10),
    error = None,
    error_code = None,
    paused_till = None,
    errors = Seq(),
    warnings = Seq(),
    via_referral = true,
    org_metadata = orgMetadata
  )

  val Error = new Throwable("ERROR")
  val campaign_uuid = s"cmp_1_cfknacskndjcn"
  val campaign = Campaign(
    id = 1L,
    uuid = Some(campaign_uuid),
    account_id = 1L,
    team_id = 1L,
    shared_with_team = true,
    name = "New Campaign 0161",
    status = CampaignStatus.RUNNING,
    head_step_id = Some(1L),
    settings = campaignSettings,
    last_scheduled_at = None,
    created_at = DateTime.now()
  )

  val prospectObjectInternal = ProspectFixtures.prospectObjectInternal

  val prospectObject = ProspectObject(
    id = 1,
    owner_id = 1,
    team_id = 1,
    first_name = Some("Animesh"),
    last_name = Some("Kumar"),
    email = Some("<EMAIL>"),
    custom_fields = Json.obj(),
    list = None,
    job_title = None,
    company = None,
    linkedin_url = None,
    phone = None,
    phone_2 = None,
    phone_3 = None,
    city = None,
    state = None,
    country = None,
    timezone = None,
    prospect_category = "Dont know this",
    last_contacted_at = None,
    last_contacted_at_phone = None,
    created_at = DateTime.now().minusMonths(5),
    internal = prospectObjectInternal,
    latest_reply_sentiment_uuid = None,
    current_step_type = None,
    latest_task_done_at = None,
    prospect_uuid = Some(ProspectUuid("prs_aa_abcdefghi")),
    owner_uuid = AccountUuid("acc_aa_abcdegfhi"),
    updated_at = DateTime.now()
  )

  val campaignStepVariantForScheduling = CampaignStepVariantForScheduling(
    id = 1,
    step_id = 1,
    campaign_id = 1,
    template_id = None,
    step_data = CampaignStepData.AutoEmailStep(
      subject = "variant subject",
      body = "Variant body",
    ),
    step_label = None,
    step_delay = 10,
    notes = Some("Note"),
    priority = Some(TaskPriority.Normal),
    active = true,
    scheduled_count = 1
  )


  val campaignStepWithChildren = CampaignStepWithChildren(
    id = 1,
    label = None,
    campaign_id = 1,
    delay = 10,
    step_type = CampaignStepType.AutoEmailStep,
    created_at = DateTime.parse("2022-03-21T11:58:03.294Z"),
    children = List(2, 3, 4),
    variants = Seq(
      campaignStepVariantForScheduling,
      campaignStepVariantForScheduling.copy(id = 2, step_data = CampaignStepData.AutoEmailStep(body = "Variant body", subject = "Variant subject V2")),
      campaignStepVariantForScheduling.copy(id = 3)
    )
  )

  val channel_follow_up_data = PreviousFollowUpData.AutoEmailFollowUp(
    email_thread_id = Some(1), // it is 1 everywhere, we are just fixing compile error now Date: 16/03/2023
    from_name = "Prateek Bhat",
    base_body = "This is previous test body",
    body = "This is previous test body",
    subject = "Hey {{first_name}}",
    from_email = "<EMAIL>",
    is_edited_preview_email = false,

  )

  val previousFollowUp = PreviousFollowUp(
    channel_follow_up_data = channel_follow_up_data,
    sent_at = DateTime.now().minusDays(5),
    timezone = "IN",
    step_id = Some(1),
    completed_reason = None
  )

  val campaignEditedPreviewEmail = CampaignEditedPreviewEmail(
    campaignId = 1,
    prospectId = 1,
    stepId = 1,
    editedByAccountId = 1,
    editedSubject = "This is a Subject",
    editedBody = "This is the body")

  val emailSetting = EmailSetting(
    id = Some(EmailSettingId(emailSettingId = 1)),
    org_id = OrgId(id = 1),
    owner_id = AccountId(id = 1),
    team_id = TeamId(id = 1),
    uuid = Some(EmailSettingUuid("test_uuid")),
    owner_uuid = AccountUuid("owner_uuid"),
    team_uuid = TeamUuid("team_uuid"),
    message_id_suffix = "Hello",
    email = "<EMAIL>",
    email_address_host = "<EMAIL>",
    service_provider = EmailServiceProvider.GMAIL_API,
      domain_provider = None,
    via_gmail_smtp = None,
    owner_name = "Animesh",
    sender_name = "Animesh Kumar",
    first_name = "Animesh",
    last_name = "Kumar",
    cc_emails = None,
    bcc_emails = None,
    smtp_username = None,
    smtp_password = None,
    smtp_host = None,
    smtp_port = None,
    imap_username = None,
    imap_password = None,
    imap_host = None,
    imap_port = None,
    oauth2_access_token = None,
    oauth2_refresh_token = None,
    oauth2_token_type = None,
    oauth2_token_expires_in = None,
    oauth2_access_token_expires_at = None,
    email_domain = None,
    api_key = None,
    mailgun_region = None,
    quota_per_day = 400,
    reply_handling = ReplyHandling.PAUSE_SPECIFIC_CAMPAIGN_ON_REPLY,
    last_read_for_replies = None,
    latest_email_scheduled_at = None,
    error = None,
    error_reported_at = None,
    paused_till = None,
    signature = "Animesh Kumar",
    created_at = None,
    current_prospect_sent_count_email = 10,
    default_tracking_domain = "default_tracking_domain",
    default_unsubscribe_domain = "default_unsubscribe_domain",
    rep_tracking_host_id = 1,
    tracking_domain_host = None,
    custom_tracking_domain = None,
    custom_tracking_cname_value = None,
    custom_tracking_domain_is_verified = None,
    custom_tracking_domain_is_ssl_enabled = None,
    rep_mail_server_id = 1,
    rep_mail_server_public_ip = "rep_mail_server_public_ip",
    rep_mail_server_host = "rep_mail_server_host",
    rep_mail_server_reverse_dns = None,
    min_delay_seconds = 0,
    max_delay_seconds = 0,
      tag = None,
    campaign_use_status_for_email_setting = CampaignUseStatusForEmailSetting.IsNotAssignedToAnyCampaign,
    show_rms_ip_in_frontend = false

  )


  describe("testing CampaignDAO.findCampaignsForSchedulingTasks") {

    it("should return query for general_channel_settings") {

      val expected: String =
        """
          |SELECT
          |
          |  c.*,
          |
          |  cs.max_tasks_per_prospect_per_day,
          |
          |  cs.max_tasks_per_prospect_per_week,
          |
          |  cs.latest_task_scheduled_at,
          |  csm.prospects_remaining_to_be_scheduled_exists,
          |  ccs.channel_settings_uuid as campaign_channel_setting_uuid,
          |  o.id as org_id
          |
          |
          |  FROM campaigns c
          |  INNER JOIN campaign_channel_settings ccs ON (ccs.campaign_id = c.id AND ccs.team_id = c.team_id)
          |  INNER JOIN general_channel_settings cs ON (cs.uuid = ccs.channel_settings_uuid AND cs.team_id = ccs.team_id)
          |  INNER JOIN teams t ON t.id = c.team_id
          |  INNER JOIN accounts a ON a.id = c.account_id
          |  INNER JOIN organizations o ON o.id = a.org_id
          |  LEFT JOIN campaign_scheduling_metadata csm ON (c.id = csm.campaign_id AND c.team_id = csm.team_id)
          |
          |  WHERE cs.uuid = ?
          |
          |    AND c.status = ?
          |    AND c.head_step_id IS NOT NULL
          |    -- AND (gs.paused_till IS NULL OR gs.paused_till < now())
          |
          |    AND (o.paused_till IS NULL OR o.paused_till < now())
          |    AND (
          |        (
          |          csm.prospects_remaining_to_be_scheduled_exists OR
          |          csm.prospects_remaining_to_be_scheduled_exists IS NULL
          |        )
          |        OR o.is_agency
          |
          |    )
          |
          |    AND (
          |      ccs.next_to_be_scheduled_at IS NULL OR
          |       (
          |          ccs.next_to_be_scheduled_at < now()
          |          -- NOTE Multichannel: Skipping For general / linkedin / whatsapp task because there is no reply tracking
          |          -- AND
          |          -- res.last_read_for_replies > now() - interval '15 minutes'
          |        )
          |    )
          |
          |    -- helps prioritize older campaigns slightly, requested by E2E in May 2020
          |    ORDER BY c.id;
          |    """.stripMargin

      val query = CampaignDAO.findCampaignsForSchedulingTasksQuery(
        channelStepType = CampaignStepType.GeneralTask.channelStepType,
        channelSettingUuid = "general_channel_setting_243fvnjsd"
      ).statement

      val split = expected.split(s"\\s+")
      val rhs = split.reduce((a1, a2) => {
        a1 + " " + a2
      })
      val lhs = query.split("\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })

      logger.info(s"\n\n\nlhs: $lhs")
      logger.info(s"\n\n\nrhs: $rhs")

      assert(lhs == rhs)

    }
  }


  it("should return query for whatsapp_settings") {

    val expected: String =
      """
        |SELECT
        |
        |  c.*,
        |
        |  cs.max_tasks_per_prospect_per_day,
        |
        |  cs.max_tasks_per_prospect_per_week,
        |
        |  cs.latest_task_scheduled_at,
        |  csm.prospects_remaining_to_be_scheduled_exists,
        |  ccs.channel_settings_uuid as campaign_channel_setting_uuid,
        |
        |  o.id as org_id
        |
        |
        |  FROM campaigns c
        |  INNER JOIN campaign_channel_settings ccs ON (ccs.campaign_id = c.id AND ccs.team_id = c.team_id)
        |  INNER JOIN whatsapp_settings cs ON (cs.uuid = ccs.channel_settings_uuid AND cs.team_id = ccs.team_id)
        |  INNER JOIN teams t ON t.id = c.team_id
        |  INNER JOIN accounts a ON a.id = c.account_id
        |  INNER JOIN organizations o ON o.id = a.org_id
        |  LEFT JOIN campaign_scheduling_metadata csm ON (c.id = csm.campaign_id AND c.team_id = csm.team_id)
        |
        |  WHERE cs.uuid = ?
        |
        |    AND c.status = ?
        |    AND c.head_step_id IS NOT NULL
        |    -- AND (gs.paused_till IS NULL OR gs.paused_till < now())
        |
        |    AND (o.paused_till IS NULL OR o.paused_till < now())
        |    AND (
        |        (
        |          csm.prospects_remaining_to_be_scheduled_exists OR
        |          csm.prospects_remaining_to_be_scheduled_exists IS NULL
        |        )
        |        OR o.is_agency
        |
        |    )
        |
        |    AND (
        |      ccs.next_to_be_scheduled_at IS NULL OR
        |       (
        |          ccs.next_to_be_scheduled_at < now()
        |          -- NOTE Multichannel: Skipping For general / linkedin / whatsapp task because there is no reply tracking
        |          -- AND
        |          -- res.last_read_for_replies > now() - interval '15 minutes'
        |        )
        |    )
        |
        |    -- helps prioritize older campaigns slightly, requested by E2E in May 2020
        |    ORDER BY c.id;
        |    """.stripMargin

    val query = CampaignDAO.findCampaignsForSchedulingTasksQuery(
      channelStepType = CampaignStepType.WhatsappMessage.channelStepType,
      channelSettingUuid = "whatsapp_account_243fvnjsd"
    ).statement

    val split = expected.split(s"\\s+")
    val rhs = split.reduce((a1, a2) => {
      a1 + " " + a2
    })
    val lhs = query.split("\\s+").reduce((s1, s2) => {
      s1 + " " + s2
    })

    logger.info(s"\n\n\nlhs: $lhs")
    logger.info(s"\n\n\nrhs: $rhs")

    assert(lhs == rhs)

  }

  it("should return query for linkedin_settings") {

    val expected: String =
      """
        |SELECT
        |
        |  c.*,
        |  captain_data_user_id, 
        |  captain_data_account_id,
        |  service_provider,
        |  cs.max_tasks_per_prospect_per_day,
        |
        |  cs.max_tasks_per_prospect_per_week,
        |
        |  cs.latest_task_scheduled_at,
        |  csm.prospects_remaining_to_be_scheduled_exists,
        |  ccs.channel_settings_uuid as campaign_channel_setting_uuid,
        |
        |  o.id as org_id
        |
        |
        |  FROM campaigns c
        |  INNER JOIN campaign_channel_settings ccs ON (ccs.campaign_id = c.id AND ccs.team_id = c.team_id)
        |  INNER JOIN linkedin_settings cs ON (cs.uuid = ccs.channel_settings_uuid AND cs.team_id = ccs.team_id)
        |  INNER JOIN teams t ON t.id = c.team_id
        |  INNER JOIN accounts a ON a.id = c.account_id
        |  INNER JOIN organizations o ON o.id = a.org_id
        |  LEFT JOIN campaign_scheduling_metadata csm ON (c.id = csm.campaign_id AND c.team_id = csm.team_id)
        |
        |  WHERE cs.uuid = ?
        |
        |    AND c.status = ?
        |    AND c.head_step_id IS NOT NULL
        |    -- AND (gs.paused_till IS NULL OR gs.paused_till < now())
        |
        |    AND (o.paused_till IS NULL OR o.paused_till < now())
        |    AND (
        |        (
        |          csm.prospects_remaining_to_be_scheduled_exists OR
        |          csm.prospects_remaining_to_be_scheduled_exists IS NULL
        |        )
        |        OR o.is_agency
        |
        |    )
        |    AND (
        |      ccs.next_to_be_scheduled_at IS NULL OR
        |       (
        |          ccs.next_to_be_scheduled_at < now()
        |          -- NOTE Multichannel: Skipping For general / linkedin / whatsapp task because there is no reply tracking
        |          -- AND
        |          -- res.last_read_for_replies > now() - interval '15 minutes'
        |        )
        |    )
        |
        |    -- helps prioritize older campaigns slightly, requested by E2E in May 2020
        |    ORDER BY c.id;
        |    """.stripMargin

    val query = CampaignDAO.findCampaignsForSchedulingTasksQuery(
      channelStepType = CampaignStepType.LinkedinInmail.channelStepType,
      channelSettingUuid = "linkedin_account_243fvnjsd"
    ).statement

    val split = expected.split(s"\\s+")
    val rhs = split.reduce((a1, a2) => {
      a1 + " " + a2
    })
    val lhs = query.split("\\s+").reduce((s1, s2) => {
      s1 + " " + s2
    })

    logger.info(s"\n\n\nlhs: $lhs")
    logger.info(s"\n\n\nrhs: $rhs")

    assert(lhs == rhs)

  }

  describe("testing CampaignService.addNewLineBreaks") {
    it("should convert \n to <br/>") {

      val text = "hi \n there \n you \n are \n amazing"
      val expected = "hi <br /> there <br /> you <br /> are <br /> amazing"
      val res = CampaignService.addNewLineBreaks(
        body = text
      )

      assert(res == expected)

    }
  }


  describe("CampaignService.extractFilenameFromUrlVoicemail") {

    it("should extract filename from simple URL") {
      val url = "http://example.com/voicemails/message.mp3"
      assert(CampaignService.extractFilenameFromUrlVoicemail(url = url) === "message.mp3")
    }

    it("should extract filename from URL with query parameters") {
      val url = "http://example.com/voicemails/message.mp3?user=123&time=1615478956"
      assert(CampaignService.extractFilenameFromUrlVoicemail(url = url) === "message.mp3?user=123&time=1615478956")
    }

    it("should extract filename from URL with multiple path segments") {
      val url = "http://example.com/api/v1/voicemails/user/123/message.mp3"
      assert(CampaignService.extractFilenameFromUrlVoicemail(url = url) === "message.mp3")
    }

    it("should extract filename from URL with no file extension") {
      val url = "http://example.com/voicemails/message"
      assert(CampaignService.extractFilenameFromUrlVoicemail(url = url) === "message")
    }

    it("should extract filename from URL with URL encoded characters") {
      val url = "http://example.com/voicemails/message%20with%20spaces.mp3"
      assert(CampaignService.extractFilenameFromUrlVoicemail(url = url) === "message%20with%20spaces.mp3")
    }

    it("should extract filename from HTTPS URL") {
      val url = "https://example.com/voicemails/secure-message.mp3"
      assert(CampaignService.extractFilenameFromUrlVoicemail(url = url) === "secure-message.mp3")
    }

    it("should extract filename from file URL") {
      val url = "file:///path/to/local/voicemails/local-message.mp3"
      assert(CampaignService.extractFilenameFromUrlVoicemail(url = url) === "local-message.mp3")
    }
  }



  describe("checkCampaignSchedulingMetadata") {

    val campaignIdAndTeamId = CampaignIdAndTeamId(
      campaign_id = campaign_id_VC.id, team_id = team_id_VC.id
    )

    it("fail getInactiveCampaignsForStopping") {

      (campaignDAOService.findOrderedSteps)
        .expects(campaignIdAndTeamId.campaign_id, TeamId(id = team_id))
        .returns(Seq(CampaignStepWithChildren(
          id = 1,
          label = None,
          campaign_id,
          delay = 3,
          step_type = CampaignStepType.AutoEmailStep,
          created_at = DateTime.now(),
          children = List(),
          variants = Seq()
        )))
      (campaignDAO.getInactiveCampaignsForStopping)
        .expects(InactiveCampaignCheckType.OnHoldCheck, campaignIdAndTeamId, *)
        .returns(Failure(Error))

      val result = campaignService.checkCampaignSchedulingMetadata(
        campaignIdAndTeamId = campaignIdAndTeamId
      )

      assert(result == Failure(Error))

    }

    describe("Getting empty list for inactive check, so that means the campaign is active so prospects_remaining_to_be_scheduled_exists is true") {
      it("fail updateCampaignSchedulingMetadata") {

        (campaignDAOService.findOrderedSteps)
          .expects(campaignIdAndTeamId.campaign_id, TeamId(id = team_id))
          .returns(Seq(CampaignStepWithChildren(
            id = 1,
            label = None,
            campaign_id,
            delay = 3,
            step_type = CampaignStepType.AutoEmailStep,
            created_at = DateTime.now(),
            children = List(),
            variants = Seq()
          )))
        (campaignDAO.getInactiveCampaignsForStopping)
          .expects(InactiveCampaignCheckType.OnHoldCheck, campaignIdAndTeamId, *)
          .returns(Success(None))

        (campaignSchedulingMetadataDAO.updateCampaignSchedulingMetadata)
          .expects(CampaignId(campaignIdAndTeamId.campaign_id), TeamId(campaignIdAndTeamId.team_id), true)
          .returns(Failure(Error))


        val result = campaignService.checkCampaignSchedulingMetadata(
          campaignIdAndTeamId = campaignIdAndTeamId
        )

        assert(result == Failure(Error))

      }

      it("Success updateCampaignSchedulingMetadata") {

        (campaignDAOService.findOrderedSteps)
          .expects(campaignIdAndTeamId.campaign_id, TeamId(id = team_id))
          .returns(Seq(CampaignStepWithChildren(
            id = 1,
            label = None,
            campaign_id,
            delay = 3,
            step_type = CampaignStepType.AutoEmailStep,
            created_at = DateTime.now(),
            children = List(),
            variants = Seq()
          )))
        (campaignDAO.getInactiveCampaignsForStopping)
          .expects(InactiveCampaignCheckType.OnHoldCheck, campaignIdAndTeamId, *)
          .returns(Success(None))

        (campaignSchedulingMetadataDAO.updateCampaignSchedulingMetadata)
          .expects(CampaignId(campaignIdAndTeamId.campaign_id), TeamId(campaignIdAndTeamId.team_id), true)
          .returns(Success(1))
        (campaignDAO.findCampaignForCampaignUtilsOnly)
          .expects(campaignIdAndTeamId.campaign_id, TeamId(campaignIdAndTeamId.team_id))
          .returns(Some(campaign.copy(status = CampaignStatus.RUNNING)))

        val result = campaignService.checkCampaignSchedulingMetadata(
          campaignIdAndTeamId = campaignIdAndTeamId
        )

        assert(result == Success(1))

      }
    }


    describe("Getting list for inactive check, so that means the campaign is inactive so prospects_remaining_to_be_scheduled_exists is false") {
      it("fail updateCampaignSchedulingMetadata") {

        (campaignDAOService.findOrderedSteps)
          .expects(campaignIdAndTeamId.campaign_id, TeamId(id = team_id))
          .returns(Seq(CampaignStepWithChildren(
            id = 1,
            label = None,
            campaign_id,
            delay = 3,
            step_type = CampaignStepType.AutoEmailStep,
            created_at = DateTime.now(),
            children = List(),
            variants = Seq()
          )))
        (campaignDAO.getInactiveCampaignsForStopping)
          .expects(InactiveCampaignCheckType.OnHoldCheck, campaignIdAndTeamId, *)
          .returns(Success(Some(InactiveCampaignData(campaign_id = CampaignId(campaignIdAndTeamId.campaign_id), team_id = TeamId(campaignIdAndTeamId.team_id), org_name = "Some Name", campaign_owner_email = "<EMAIL>", team_name = "smartreach", campaign_name = "test campaign"))))

        (campaignSchedulingMetadataDAO.updateCampaignSchedulingMetadata)
          .expects(CampaignId(campaignIdAndTeamId.campaign_id), TeamId(campaignIdAndTeamId.team_id), false)
          .returns(Failure(Error))


        val result = campaignService.checkCampaignSchedulingMetadata(
          campaignIdAndTeamId = campaignIdAndTeamId
        )

        assert(result == Failure(Error))

      }

      it("Success updateCampaignSchedulingMetadata") {

        (campaignDAOService.findOrderedSteps)
          .expects(campaignIdAndTeamId.campaign_id, TeamId(id = team_id))
          .returns(Seq(CampaignStepWithChildren(
            id = 1,
            label = None,
            campaign_id,
            delay = 3,
            step_type = CampaignStepType.AutoEmailStep,
            created_at = DateTime.now(),
            children = List(),
            variants = Seq()
          )))
        (campaignDAO.getInactiveCampaignsForStopping)
          .expects(InactiveCampaignCheckType.OnHoldCheck, campaignIdAndTeamId, *)
          .returns(Success(Some(InactiveCampaignData(campaign_id = CampaignId(campaignIdAndTeamId.campaign_id), team_id = TeamId(campaignIdAndTeamId.team_id), org_name = "Some Name", campaign_owner_email = "<EMAIL>", team_name = "smartreach", campaign_name = "test campaign"))))

        (campaignSchedulingMetadataDAO.updateCampaignSchedulingMetadata)
          .expects(CampaignId(campaignIdAndTeamId.campaign_id), TeamId(campaignIdAndTeamId.team_id), false)
          .returns(Success(1))
        (campaignDAO.findCampaignForCampaignUtilsOnly)
          .expects(campaignIdAndTeamId.campaign_id, TeamId(campaignIdAndTeamId.team_id))
          .returns(Some(campaign.copy(status = CampaignStatus.RUNNING)))
        (campaignDAO.updateStatus)
          .expects(campaignIdAndTeamId.campaign_id, CampaignStatus.ON_HOLD, team_id_VC, None, None)
          .returns(Success(None))

        val result = campaignService.checkCampaignSchedulingMetadata(
          campaignIdAndTeamId = campaignIdAndTeamId
        )

        assert(result == Success(1))

      }
    }


  }

}
