package app.api.campaigns.services

import org.apache.pekko.actor.ActorSystem
import org.apache.pekko.stream.{ActorMaterializer, Materializer}
import api.AppConfig
import api.accounts.email.models.EmailServiceProvider
import api.accounts.models.{AccountId, AccountProfileInfo, OrgId, ProspectAccountUuid}
import api.accounts.{Account, AccountAccess, AccountEmail, AccountMetadata, AccountType, AccountUuid, OrgCountData, OrgMetadata, OrgPlan, OrgSettings, OrganizationRole, OrganizationWithCurrentData, ReplyHandling, TeamAccountRole, TeamId, TeamMember}
import api.accounts.models.AccountProfileInfo
import api.accounts.service.ResetUserCacheUtil
import api.calendar_app.models.CalendarAccountData
import api.campaigns.{Campaign, CampaignDAO, CampaignEditedPreviewEmail, CampaignEditedPreviewEmailDAO, CampaignEmailSettings, CampaignEmailSettingsUuid, CampaignProspectDAO, CampaignSettings, CampaignStepDAO, CampaignStepVariantForScheduling, CampaignStepWithChildren, ChannelSettingUuid, PreviousFollowUp}
import api.campaigns.models.{CallSettingSenderDetails, CampaignEmailSettingsId, CampaignStepData, CampaignStepType, CampaignType, GetTestStepsError, LinkedinSettingSenderDetails, PreviousFollowUpData, SendEmailFromCampaignDetails, SmsSettingSenderDetails, StepDetails, ValidateCampaignTemplateError, ValidateStepData, WhatsappSettingSenderDetails}
import api.campaigns.services.{CampaignDAOService, CampaignId, CampaignService, CampaignTemplateService, CampaignTestStepService}
import api.columns.InternalMergeTagValuesForProspect
import api.emails.dao_service.EmailScheduledDAOService
import api.emails.models.{EmailSendingFlow, EmailServiceProviderSendEmail, EmailSettingUuid}
import api.emails.{EmailScheduled, EmailScheduledDAO, EmailScheduledNewAfterSaving, EmailSetting, EmailSettingDAO, EmailsScheduledUuid}
import api.prospects.dao_service.ProspectDAOService
import api.prospects.models.{ProspectCategory, ProspectCategoryId}
import api.prospects.{ProspectAccount, ProspectAccountDAO1, ProspectUuid}
import api.spammonitor.dao.EmailSendingStatusDAO
import api.tasks.models.TaskPriority
import api.team.TeamUuid
import api.triggers.Trigger
import app.test_fixtures.accounts.OrgCountDataFixture
import app.test_fixtures.campaign_settings.{CallSettingSenderDetailsFixtures, LinkedinSettingSenderDetailsFixtures, SmsSettingSenderDetailsFixtures, WhatsappSettingSenderDetailsFixtures}
import app.test_fixtures.organizationa.{OrgMetadataFixture, OrgPlanFixture}
import app.test_fixtures.prospect.{ProspectAccountFixture, ProspectFixtures}
import eventframework.{ProspectObject, ProspectObjectInternal}
import io.smartreach.esp.api.emails.EmailSettingId
import org.joda.time.DateTime
import org.scalamock.scalatest.AsyncMockFactory
import org.scalatest.funspec.AsyncFunSpec
import play.api.libs.json.Json
import play.api.libs.ws.WSClient
import play.api.libs.ws.ahc.AhcWSClient
import scalikejdbc.DBSession
import sr_scheduler.CampaignStatus
import sr_scheduler.models.{CampaignEmailPriority, ChannelType, EmailScheduledNew3, SelectedCalendarData}
import utils.{Helpers, SRLogger}
import utils.cache_utils.model.CampaignUseStatusForEmailSetting
import utils.cronjobs.email_setting_deletion.model.EmailSettingStatus
import utils.dbutils.{DBUtils, DbAndSession}
import utils.email.models.SendScheduleEmailType
import utils.email.{EmailOptionsForGetBodies, EmailSenderService, EmailService, EmailServiceBody, EmailServiceCompanion}
import utils.mq.email.MQEmailMessage
import utils.sr_product_usage_data.services.SrUserFeatureUsageEventService
import utils.templating.TemplateService
import utils.uuid.SrUuidUtils
import utils.testapp.TestAppExecutionContext

import java.util.UUID
import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}

class CampaignTestStepServiceSpec  extends AsyncFunSpec with AsyncMockFactory  {

  given Logger: SRLogger = new SRLogger("Test Logger")
  val campaignProspectDAO: CampaignProspectDAO = mock[CampaignProspectDAO]
  val prospectDAOService: ProspectDAOService = mock[ProspectDAOService]
  val prospectAccountDAO: ProspectAccountDAO1 = mock[ProspectAccountDAO1]
  val campaignEditedPreviewEmailDAO: CampaignEditedPreviewEmailDAO = mock[CampaignEditedPreviewEmailDAO]
  val emailSettingDAO: EmailSettingDAO = mock[EmailSettingDAO]
  //  val campaignStepDAO = mock[CampaignStepDAO]
  val templateService: TemplateService = mock[TemplateService]
  val campaignTemplateService: CampaignTemplateService =  mock[CampaignTemplateService]
  val emailService: EmailSenderService = mock[EmailSenderService]
  implicit lazy val system: ActorSystem = TestAppExecutionContext.actorSystem
  implicit lazy val materializer: Materializer = TestAppExecutionContext.actorMaterializer
  implicit lazy val wsClient: AhcWSClient = TestAppExecutionContext.wsClient
  val resetUserCacheUtil: ResetUserCacheUtil = mock[ResetUserCacheUtil]
  val triggerDAO: Trigger = mock[Trigger]
  val campaignDAO: CampaignDAO = mock[CampaignDAO]
  val srUserFeatureUsageEventService: SrUserFeatureUsageEventService = mock[SrUserFeatureUsageEventService]
  val emailSendingStatusDAO: EmailSendingStatusDAO = mock[EmailSendingStatusDAO]
  val emailServiceCompanion: EmailServiceCompanion = mock[EmailServiceCompanion]
  val emailScheduledDAOService: EmailScheduledDAOService = mock[EmailScheduledDAOService]
  val campaignDAOService: CampaignDAOService = mock[CampaignDAOService]
  val srUuidUtils: SrUuidUtils = mock[SrUuidUtils]
  val campaignTestStepService: CampaignTestStepService = new CampaignTestStepService(
    prospectDAOService = prospectDAOService,
    campaignTemplateService = campaignTemplateService,
    prospectAccountDAO = prospectAccountDAO,
    //    campaignStepDAO = campaignStepDAO,
    templateService = templateService,
    emailSettingDAO = emailSettingDAO,
    emailScheduledDAOService = emailScheduledDAOService,
    emailSenderService = emailService,
    emailServiceCompanion = emailServiceCompanion,
    campaignDAOService = campaignDAOService,
    srUuidUtils = srUuidUtils
  )


  val counts: OrgCountData = OrgCountDataFixture.orgCountData_default

  val orgId: Long = 28L
  val accountId: Long = 30L
  val campaignId: Long = 54L
  val teamId: Long = 34L
  val prospectId: Long = 46L
  val headStepId: Long = 40L
  val emailSettingId: Long = 16L

  val campaignSettings: CampaignSettings = CampaignSettings(
    campaign_email_settings = List(
      CampaignEmailSettings(
        campaign_id = CampaignId(campaignId),
        sender_email_setting_id = EmailSettingId(emailSettingId),
        receiver_email_setting_id = EmailSettingId(emailSettingId),
        team_id = TeamId(teamId),
        uuid = CampaignEmailSettingsUuid("temp_setting_id"),
        id = CampaignEmailSettingsId(123),
        sender_email = "<EMAIL>",
        receiver_email = "<EMAIL>",
        max_emails_per_day_from_email_account = 400,
        signature = Some("emailsignature"),
        error = None,
        from_name = None
      )
    ),
    campaign_linkedin_settings = List(
      LinkedinSettingSenderDetailsFixtures.linkedin_setting_sender_details
    ),
    campaign_call_settings = List(
      CallSettingSenderDetailsFixtures.call_setting_sender_details
    ),
    campaign_whatsapp_settings = List(
      WhatsappSettingSenderDetailsFixtures.whatsapp_setting_sender_details
    ),
    campaign_sms_settings = List(
      SmsSettingSenderDetailsFixtures.sms_setting_sender_details
    ),
    ai_sequence_status = None,
    timezone = "UTC",
    daily_from_time = 18,
    daily_till_time = 20,
    sending_holiday_calendar_id = None,
    days_preference = List(false, true, true, true, true, false, false),
    mark_completed_after_days = 24,
    max_emails_per_day = 26,
    open_tracking_enabled = false,
    click_tracking_enabled = false,
    enable_email_validation = false,
    ab_testing_enabled = false,
    warmup_started_at = None,
    warmup_length_in_days = None,
    warmup_starting_email_count = None,
    show_soft_start_setting = false,
    schedule_start_at = None,
    schedule_start_at_tz = None,
    email_priority = CampaignEmailPriority.FIRST_EMAIL,
    append_followups = true,
    opt_out_msg = "{{unsubscribe_link}}",
    opt_out_is_text = false,
    add_prospect_to_dnc_on_opt_out = true,
    send_plain_text_email = Some(false),
    campaign_type = CampaignType.MultiChannel,
    triggers = Seq(),
    sending_mode = None,
    selected_calendar_data = None
  )

  val orgPlan: OrgPlan = OrgPlanFixture.orgPlanFixture

  val orgMetadata: OrgMetadata = OrgMetadataFixture.orgMetadataFixture2

  val orgSettings: OrgSettings = OrgSettings(
    enable_ab_testing = true,
    disable_force_send = false,
    bulk_sender = false,
    allow_2fa = false,
    show_2fa_setting = false,
    enforce_2fa = false,
    allow_native_crm_integration = false,
    agency_option_allow_changing = false,
    agency_option_show = false)

  val org: OrganizationWithCurrentData = OrganizationWithCurrentData(
    id = orgId,
    name = "Animesh",
    owner_account_id = accountId,
    counts = counts,
    settings = orgSettings,
    plan = orgPlan,
    is_agency = true,
    trial_ends_at = DateTime.now().plusDays(10),
    error = None,
    error_code = None,
    paused_till = None,
    errors = Seq(),
    warnings = Seq(),
    via_referral = true,
    org_metadata = orgMetadata
  )

  val Error = new Throwable("ERROR")
  val campaign_uuid = s"cmp_${teamId}_cfknacskndjcn"
  val campaign: Campaign = Campaign(
    id = campaignId.toInt,
    uuid = Some(campaign_uuid),
    account_id = accountId,
    team_id = teamId,
    shared_with_team = true,
    name = "New Campaign 0161",
    status = CampaignStatus.RUNNING,
    head_step_id = Some(headStepId),
    settings = campaignSettings,
    last_scheduled_at = None,
    created_at = DateTime.now()
  )

  val prospectObjectInternal: ProspectObjectInternal = ProspectFixtures.prospectObjectInternal

  val prospectObject: ProspectObject = ProspectObject(
    id = prospectId,
    owner_id = accountId,
    team_id = teamId,
    first_name = Some("Animesh"),
    last_name = Some("Kumar"),
    email = Some("<EMAIL>"),
    custom_fields = Json.obj(),
    list = None,
    job_title = None,
    company = None,
    linkedin_url = None,
    phone = None,
    phone_2 = None,
    phone_3 = None,
    city = None,
    state = None,
    country = None,
    timezone = None,
    prospect_category = "Dont know this",
    last_contacted_at = None,
    last_contacted_at_phone = None,
    created_at = DateTime.now().minusMonths(5),
    internal = prospectObjectInternal,
    latest_reply_sentiment_uuid = None,
    current_step_type = None,
    latest_task_done_at = None,
    prospect_uuid = Some(ProspectUuid("prs_aa_abcdefghi")),
    owner_uuid = AccountUuid("acc_aa_abcdegfhi"),
    updated_at = DateTime.now()
  )

  val campaignStepVariantForScheduling: CampaignStepVariantForScheduling = CampaignStepVariantForScheduling(
    id = headStepId,
    step_id = headStepId,
    campaign_id = campaignId.toInt,
    template_id = None,
    step_data = CampaignStepData.AutoEmailStep(
      subject = "variant subject",
      body = "Variant body",
    ),
    step_label = None,
    step_delay = 10,
    notes = Some("Note"),
    priority = Some(TaskPriority.Normal),
    active = true,
    scheduled_count = 1
  )


  val campaignStepWithChildren: CampaignStepWithChildren = CampaignStepWithChildren(
    id = headStepId,
    label = None,
    campaign_id = campaignId.toInt,
    delay = 10,
    step_type = CampaignStepType.AutoEmailStep,
    created_at = DateTime.parse("2022-03-21T11:58:03.294Z"),
    children = List(2, 3, 4),
    variants = Seq(
      campaignStepVariantForScheduling,
      campaignStepVariantForScheduling.copy(id = 53, step_data = CampaignStepData.AutoEmailStep(body = "Variant body", subject = "Variant subject V2")),
      campaignStepVariantForScheduling.copy(id = 54)
    )
  )

  val channel_follow_up_data: PreviousFollowUpData = PreviousFollowUpData.AutoEmailFollowUp(
    email_thread_id = Some(58), // it is 58 before itself we are just fixing compile error now Date: 16/03/2023
    from_name = "Prateek Bhat",
    base_body = "This is previous test body",
    body = "This is previous test body",
    subject = "Hey {{first_name}}",
    from_email = "<EMAIL>",
    is_edited_preview_email = false,

  )

  val previousFollowUp: PreviousFollowUp = PreviousFollowUp(
    channel_follow_up_data = channel_follow_up_data,
    sent_at = DateTime.now().minusDays(5),
    timezone = "IN",
    step_id = Some(headStepId),
    completed_reason = None
  )

  val campaignEditedPreviewEmail: CampaignEditedPreviewEmail = CampaignEditedPreviewEmail(
    campaignId = campaignId,
    prospectId = prospectId,
    stepId = headStepId,
    editedByAccountId = 1,
    editedSubject = "This is a Subject",
    editedBody = "This is the body")

  val emailSetting: EmailSetting = EmailSetting(
    id = Some(EmailSettingId(emailSettingId = emailSettingId)), // FIXME VALUECLASS
    org_id = OrgId(id = orgId), // FIXME VALUECLASS
    owner_id = AccountId(id = accountId), // FIXME VALUECLASS
    team_id = TeamId(id = teamId), // FIXME VALUECLASS
    uuid = Some(EmailSettingUuid("test_uuid")),
    owner_uuid = AccountUuid("owner_uuid"),
    team_uuid = TeamUuid("team_uuid"),
    message_id_suffix = "Hello",
    email = "<EMAIL>",
    email_address_host = "<EMAIL>",
    service_provider = EmailServiceProvider.GMAIL_API,
      domain_provider = None,
    via_gmail_smtp = None,
    owner_name = "Animesh",
    sender_name = "Animesh Kumar",
    first_name = "Animesh",
    last_name = "Kumar",
    cc_emails = None,
    bcc_emails = None,
    smtp_username = None,
    smtp_password = None,
    smtp_host = None,
    smtp_port = None,
    imap_username = None,
    imap_password = None,
    imap_host = None,
    imap_port = None,
    oauth2_access_token = None,
    oauth2_refresh_token = None,
    oauth2_token_type = None,
    oauth2_token_expires_in = None,
    oauth2_access_token_expires_at = None,
    email_domain = None,
    api_key = None,
    mailgun_region = None,
    quota_per_day = 400,
    reply_handling = ReplyHandling.PAUSE_SPECIFIC_CAMPAIGN_ON_REPLY,
    last_read_for_replies = None,
    latest_email_scheduled_at = None,
    error = None,
    error_reported_at = None,
    paused_till = None,
    signature = "Animesh Kumar",
    created_at = None,
    current_prospect_sent_count_email = 10,
    default_tracking_domain = "default_tracking_domain",
    default_unsubscribe_domain = "default_unsubscribe_domain",
    rep_tracking_host_id = 1,
    tracking_domain_host = None,
    custom_tracking_domain = None,
    custom_tracking_cname_value = None,
    custom_tracking_domain_is_verified = None,
    custom_tracking_domain_is_ssl_enabled = None,
    rep_mail_server_id = 1,
    rep_mail_server_public_ip = "rep_mail_server_public_ip",
    rep_mail_server_host = "rep_mail_server_host",
    rep_mail_server_reverse_dns = None,
    min_delay_seconds = 0,
    max_delay_seconds = 0,
      tag = None,
    campaign_use_status_for_email_setting = CampaignUseStatusForEmailSetting.IsNotAssignedToAnyCampaign,
    show_rms_ip_in_frontend = false

  )
  val ta: TeamMember = TeamMember(
    team_id = teamId,
    team_name = "AK",
    user_id = accountId,
    ta_id = 38,
    first_name = Some("Animesh"),
    last_name = Some("Kumar"),
    email = "<EMAIL>",
    team_role = TeamAccountRole.ADMIN,
    api_key = Some("DoNotKnowWhatThisIs"),
    zapier_key = Some("zapier_key")
  )

  implicit lazy val ec: ExecutionContext = system.dispatcher

  val profile: AccountProfileInfo = AccountProfileInfo(
    first_name = "Animesh",
    last_name = "Kumar",
    company = Some("AnimeshKumar"),
    timezone = Some("IN"),
    country_code = Some("IN"),
    mobile_country_code = Some("+91"),
    mobile_number = Some(9515253545L),
    twofa_enabled = false,
    has_gauthenticator = false,
    weekly_report_emails = Some("<EMAIL>"),
    scheduled_for_deletion_at = None,
    onboarding_phone_number = Some("+************")
  )

  val orgCountData: OrgCountData = OrgCountDataFixture.orgCountData_default

  val accountMetadata: AccountMetadata = AccountMetadata(
    // account_ui_version = None,
    is_profile_onboarding_done = None
  )



  val accountAdmin: Account = Account(
    id = AccountUuid("account_uuid"),
    internal_id = accountId,
    email = "<EMAIL>",
    email_verification_code = None,
    email_verification_code_created_at = None,
    created_at = DateTime.now().minusDays(1000),
    first_name = Some("Animesh"),
    last_name = Some("Kumar"),
    company = Some("AK"),
    timezone = None,
    profile = profile,
    org_role = Some(OrganizationRole.OWNER),
    teams = Seq(),
    account_type = AccountType.AGENCY,
    org = org,
    active = true,
    email_notification_summary = "dSFA",
    account_metadata = accountMetadata,
    email_verified = true,
    signupType = None,
    account_access = AccountAccess(
      inbox_access = false
    ),
    calendar_account_data = None
  )

  val emailScheduledNewAfterSaving: EmailScheduledNewAfterSaving = EmailScheduledNewAfterSaving(
    email_scheduled_id = 62,
    emailsScheduledUuid = EmailsScheduledUuid(UUID.randomUUID().toString),
    campaign_id = Some(campaignId),
    step_id = Some(headStepId),
    prospect_id = Some(prospectId),
    to_email = "<EMAIL>",
    reply_to_email = Option("<EMAIL>"),
    step_type = CampaignStepType.AutoEmailStep,
    from_email = "<EMAIL>",
    added_at = DateTime.now(),
    scheduled_at = DateTime.now(),
    sender_email_settings_id = emailSettingId,
    template_id = Some(1),
    variant_id = Some(1),
    rep_mail_server_id = 1,
    campaign_email_setting_id = CampaignEmailSettingsId(123),
    team_id = TeamId(teamId),
    to_name = None, from_name = "Animesh", reply_to_name = None, body = "this is a test body", base_body = "this is a test body", text_body = "this is a test body", subject = "this is a test subject"
  )
  val emailScheduled: EmailScheduled = EmailScheduled(
    id = Some(64),
    subject = Some("This is the Subject"),
    message_id = Some("someMessageId"),
    references_header = Some("DontKnowWhatThisIs"),
    sendEmailFromCampaignDetails = Some(SendEmailFromCampaignDetails(
      campaign_id = campaignId,
      campaign_name = "AnimeshCampaign",
      stepDetails = Some(StepDetails(
        step_id = headStepId,
        step_name = "First",
        step_type = CampaignStepType.AutoEmailStep
      ))
    )),
    prospect_id = Some(prospectId),
    sender_email_settings_id = Some(emailSettingId),
    email_thread_id = Some(64),
    outlook_msg_id = Some("DontKnowWhatThisIs"),
    scheduled_at = DateTime.now(),
    sent_at = Option(DateTime.now()),
    account_id = Some(accountId),
    team_id = Some(teamId)
  )



  describe("testStep") {
    it("Failed in validation because of restriction in template") {

      val error = new Exception("Body cant have previous_subject")

      val validateStepVariantData = ValidateStepData.ValidateStepVariantData(
        campaignStepData = CampaignStepData.AutoEmailStep(body = "This is The Body", subject = "This is the Subject"),
        stepId =  Some(2L),
        head_step_id = Some(headStepId),
        campaignId = CampaignId(id = campaign.id.toLong)
      )

      (campaignTemplateService.validateCampaignTemplate)
        .expects(
          teamId,
          validateStepVariantData,
          None
        )
        .returning(Left(ValidateCampaignTemplateError.BodyCantHavePreviousSubject(error)))

      campaignTestStepService.testStep(
        stepId = Some(2),
        body = "This is The Body",
        subject = "This is the Subject",
        campaign = campaign,
        toEmail = "<EMAIL>",
        campaignId = CampaignId(campaignId),
        stepType = CampaignStepType.AutoEmailStep,
        allTrackingDomainsUsed = Seq(),
        campaign_email_settings_id = CampaignEmailSettingsId(123),
        team_id = TeamId(ta.team_id),
        owner_id = AccountId(ta.user_id),
        ta_id = ta.ta_id,
        owner_name = Helpers.getAccountName(a = accountAdmin),
        owner_email = AccountEmail(accountAdmin.email),
        calendar_account_data = accountAdmin.calendar_account_data,
        emailSendingFlow = None,
        org_id = OrgId(accountAdmin.org.id)
      ).map { result =>

        assert(
          result ==
            Left(
              GetTestStepsError.
                ErrorWhileValidateCampaignTemplate(
                  ValidateCampaignTemplateError
                    .BodyCantHavePreviousSubject(error)
                )
            )
        )
      }.recover{ case e =>
        assert(false)
      }
    }


    it("Failed in validation wring template") {

      val validateStepVariantData = ValidateStepData.ValidateStepVariantData(
        campaignStepData = CampaignStepData.AutoEmailStep(body = "This is The Body", subject = "This is the Subject"),
        stepId = Some(2L),
        head_step_id = Some(headStepId),
        campaignId = CampaignId(id = campaign.id.toLong)
      )

      (campaignTemplateService.validateCampaignTemplate)
        .expects(
          teamId,
          validateStepVariantData,
          None
        )
        .returning(Right(false))

      campaignTestStepService.testStep(
        stepId = Some(2),
        body = "This is The Body",
        subject = "This is the Subject",
        campaign = campaign,
        toEmail = "<EMAIL>",
        stepType = CampaignStepType.AutoEmailStep,
        campaignId = CampaignId(campaignId),
        allTrackingDomainsUsed = Seq(),
        campaign_email_settings_id = CampaignEmailSettingsId(123),
        team_id = TeamId(ta.team_id),
        owner_id = AccountId(ta.user_id),
        ta_id = ta.ta_id,
        owner_name = Helpers.getAccountName(a = accountAdmin),
        owner_email = AccountEmail(accountAdmin.email),
        calendar_account_data = accountAdmin.calendar_account_data,
        emailSendingFlow = None,
        org_id = OrgId(accountAdmin.org.id)
      ).map { result =>

        assert(
          result == Left(GetTestStepsError.InvalidEmailTemplate))
      }.recover{ case e =>
        assert(false)
      }
    }


    it("No email setting account found") {

      val validateStepVariantData = ValidateStepData.ValidateStepVariantData(
        campaignStepData = CampaignStepData.AutoEmailStep(body = "This is The Body", subject = "This is the Subject"),
        stepId = Some(2L),
        head_step_id = Some(headStepId),
        campaignId = CampaignId(id = campaign.id)
      )

      (campaignTemplateService.validateCampaignTemplate)
        .expects(
          teamId,
          validateStepVariantData,
          None
        )
        .returning(Right(true))

      (emailSettingDAO.find (_:Long, _: EmailSettingStatus))
        .expects(emailSettingId, EmailSettingStatus.Active)
        .returning(None)
        .repeat(2)

      campaignTestStepService.testStep(
        stepId = Some(2),
        body = "This is The Body",
        subject = "This is the Subject",
        campaign = campaign,
        toEmail = "<EMAIL>",
        stepType = CampaignStepType.AutoEmailStep,
        campaignId = CampaignId(campaignId),
        allTrackingDomainsUsed = Seq(),
        campaign_email_settings_id = CampaignEmailSettingsId(123),
        team_id = TeamId(ta.team_id),
        owner_id = AccountId(ta.user_id),
        ta_id = ta.ta_id,
        owner_name = Helpers.getAccountName(a = accountAdmin),
        owner_email = AccountEmail(accountAdmin.email),
        calendar_account_data = accountAdmin.calendar_account_data,
        emailSendingFlow = None,
        org_id = OrgId(accountAdmin.org.id)
      ).map { result =>

        assert(
          result == Left(GetTestStepsError.ReceiveReplyToEmailNotGiven))
      }.recover{
        case e =>
          Logger.info(s"Error --------- ${e}")

          assert(false)
      }
    }

    it("failed processSendEmailRequest with the dao sending a prospect account when get bodies fail") {

      val validateStepVariantData = ValidateStepData.ValidateStepVariantData(
        campaignStepData = CampaignStepData.AutoEmailStep(body = "This is The Body", subject = "This is the Subject"),
        stepId = Some(2L),
        head_step_id = None,
        campaignId = CampaignId(id = campaign.id.toLong)
      )

      (campaignTemplateService.validateCampaignTemplate)
        .expects(
          teamId,
          validateStepVariantData,
          None
        )
        .returning(Right(true))

      (emailSettingDAO.find (_:Long, _: EmailSettingStatus))
        .expects(emailSettingId,  EmailSettingStatus.Active)
        .returning(Some(emailSetting))
        .repeat(2)


      (prospectDAOService.findOneByCampaignId)
        .expects(campaignId , teamId, Logger)
        .returning(Some(prospectObject))

      (prospectAccountDAO.find)
        .expects(prospectId, teamId)
        .returning(Some(ProspectAccountFixture.prospectAccount))


      (emailServiceCompanion.getBodies(_: Option[CampaignEditedPreviewEmail], _: Long,_:Option[CalendarAccountData],_:Option[SelectedCalendarData], _: Option[Long], _: EmailsScheduledUuid, _: Option[Long], _: Option[Long], _: ProspectObject, _: EmailOptionsForGetBodies)(_: SRLogger))
        .expects(
          None,
          org.id,
          accountAdmin.calendar_account_data,
          None,
          None,
          EmailsScheduledUuid(AppConfig.dummy_email_tracking_uuid),
          Some(campaign.id.toLong),
          Some(2L),
          prospectObject,
          EmailOptionsForGetBodies(
            isCampaignSendTestEmail = true,
            for_editable_preview = false,
            editedPreviewEmailAlreadyChecked = true,
            custom_tracking_domain = emailSetting.custom_tracking_domain,
            default_tracking_domain = emailSetting.default_tracking_domain,
            default_unsubscribe_domain = emailSetting.default_unsubscribe_domain,
            signature = Some(emailSetting.signature),

            opt_out_msg = campaign.settings.opt_out_msg,
            opt_out_is_text = campaign.settings.opt_out_is_text,
            append_followups = false,
            bodyTemplate = "This is The Body",
            subjectTemplate = "This is the Subject",

            email_sender_name = emailSetting.sender_name,
            sender_first_name = emailSetting.first_name,
            sender_last_name = emailSetting.last_name,
            manualEmail = false,

            trackOpens = campaign.settings.open_tracking_enabled,
            trackClicks = campaign.settings.click_tracking_enabled,

            previousEmails = Seq(),
            allTrackingDomainsUsed = Seq()
          ),
          *
        )
        .returning(Failure(Error))


      campaignTestStepService.testStep(
        stepId =  Some(2L),
        body = "This is The Body",
        subject = "This is the Subject",
        campaign = campaign.copy(head_step_id = None, settings = campaign.settings.copy(append_followups = false)),
        toEmail = "<EMAIL>",
        stepType = CampaignStepType.AutoEmailStep,
        campaignId = CampaignId(campaignId),
        allTrackingDomainsUsed = Seq(),
        campaign_email_settings_id = CampaignEmailSettingsId(123),
        team_id = TeamId(ta.team_id),
        owner_id = AccountId(ta.user_id),
        ta_id = ta.ta_id,
        owner_name = Helpers.getAccountName(a = accountAdmin),
        owner_email = AccountEmail(accountAdmin.email),
        calendar_account_data = accountAdmin.calendar_account_data,
        emailSendingFlow = None,
        org_id = OrgId(accountAdmin.org.id)
      ).map { result =>

        assert(
          result == Left(GetTestStepsError.ErrorWhileGettingEmailBody(Error)))
      }
    }

    it("Success scenario with no prospect account found when findOneByCampaignId called and getBodies success and processSendEmailRequest success") {
      val emailServiceBody = EmailServiceBody(
        subject = "This is the Subject",
        textBody = "This is The Body",
        htmlBody = "This is The Body",
        baseBody = "This is The Body",
        isEditedPreviewEmail = false,
        has_unsubscribe_link = false
      )

      val validateStepVariantData = ValidateStepData.ValidateStepVariantData(
        campaignStepData = CampaignStepData.AutoEmailStep(body = "This is The Body", subject = "This is the Subject"),
        stepId = Some(2L),
        head_step_id = Some(headStepId),
        campaignId = CampaignId(id = campaign.id.toLong)
      )

      (campaignTemplateService.validateCampaignTemplate)
        .expects(
          teamId,
          validateStepVariantData,
          None
        )
        .returning(Right(true))

      (emailSettingDAO.find (_:Long, _: EmailSettingStatus))
        .expects(emailSettingId,  EmailSettingStatus.Active)
        .returning(Some(emailSetting))
        .repeat(2)


      (prospectDAOService.findOneByCampaignId)
        .expects(campaignId , teamId, Logger)
        .returning(None)
      (prospectDAOService.getProspectCategoryId (_: TeamId, _: ProspectCategory.Value, _: Option[Account])(using _:SRLogger))
        .expects(TeamId(id = teamId), ProspectCategory.DO_NOT_CONTACT, None, *)
        .returning(Success(ProspectCategoryId(101)))

      (campaignDAOService.findOrderedSteps)
        .expects(campaignId.toLong, TeamId(id = teamId))
        .returning(Seq(campaignStepWithChildren, campaignStepWithChildren.copy(id = 2, children = List())))

//      (campaignStepDAO.getOrderedSteps)
//        .expects(Seq(campaignStepWithChildren, campaignStepWithChildren.copy(id = 2)), headStepId)
//        .returning(List(campaignStepWithChildren, campaignStepWithChildren.copy(id = 2)))

      (templateService.render(_:String, _:ProspectObject, _:InternalMergeTagValuesForProspect, _: ChannelType)(using _:SRLogger))
        .expects("variant subject", *, *, *, *)
        .returning(Success("variant subject"))

      (emailServiceCompanion.getBodies(_: Option[CampaignEditedPreviewEmail], _: Long,_:Option[CalendarAccountData],_:Option[SelectedCalendarData], _: Option[Long], _: EmailsScheduledUuid, _: Option[Long], _: Option[Long], _: ProspectObject, _: EmailOptionsForGetBodies)(_: SRLogger))
        .expects(
          None,
          org.id,
          accountAdmin.calendar_account_data,
          None,
          Some(headStepId),
          EmailsScheduledUuid(AppConfig.dummy_email_tracking_uuid),
          Some(campaign.id.toLong),
          Some(2L),
          *,
          EmailOptionsForGetBodies(
            isCampaignSendTestEmail = true,
            for_editable_preview = false,
            editedPreviewEmailAlreadyChecked = true,
            custom_tracking_domain = emailSetting.custom_tracking_domain,
            default_tracking_domain = emailSetting.default_tracking_domain,
            default_unsubscribe_domain = emailSetting.default_unsubscribe_domain,
            signature = Some(emailSetting.signature),

            opt_out_msg = campaign.settings.opt_out_msg,
            opt_out_is_text = campaign.settings.opt_out_is_text,
            append_followups = campaign.settings.append_followups,
            bodyTemplate = "This is The Body",
            subjectTemplate = "This is the Subject",

            email_sender_name = emailSetting.sender_name,
            sender_first_name = emailSetting.first_name,
            sender_last_name = emailSetting.last_name,
            manualEmail = false,

            trackOpens = campaign.settings.open_tracking_enabled,
            trackClicks = campaign.settings.click_tracking_enabled,

            previousEmails = Seq(),
            allTrackingDomainsUsed = Seq()
          ),
          *
        )
        .returning(Success(emailServiceBody))

      (() => srUuidUtils.generateEmailsScheduledUuid())
        .expects()
        .returning("test-uuid-12345")

      (emailScheduledDAOService.saveEmailsToBeScheduledAndUpdateCampaignDataV2 (_: Vector[EmailScheduledNew3], _: CampaignEmailSettingsId, _: Option[EmailSendingFlow],  _: SRLogger))
        .expects(
          *, //FIXME need to use capture here since this has dateTime in it that will change each time
          CampaignEmailSettingsId(123),
          None,
          *)
        .returning(Success(Seq(emailScheduledNewAfterSaving)))

      (emailService.sendScheduleEmail(_: MQEmailMessage, _: String, _: SendScheduleEmailType)( _: ExecutionContext, _:WSClient,_: SRLogger, _: ActorSystem) )
        .expects(MQEmailMessage(emailScheduledNewAfterSaving.email_scheduled_id, team_id = TeamId(teamId), EmailServiceProviderSendEmail.OTHER, None), "",SendScheduleEmailType.TestEmail,  *, *, *, *)
        .returning(Future(emailScheduled))


      campaignTestStepService.testStep(
        stepId = Some(2),
        body = "This is The Body",
        subject = "This is the Subject",
        campaign = campaign,
        toEmail = "<EMAIL>",
        stepType = CampaignStepType.AutoEmailStep,
        campaignId = CampaignId(campaignId),
        allTrackingDomainsUsed = Seq(),
        campaign_email_settings_id = CampaignEmailSettingsId(123),
        team_id = TeamId(ta.team_id),
        owner_id = AccountId(ta.user_id),
        ta_id = ta.ta_id,
        owner_name = Helpers.getAccountName(a = accountAdmin),
        owner_email = AccountEmail(accountAdmin.email),
        calendar_account_data = accountAdmin.calendar_account_data,
        emailSendingFlow = None,
        org_id = OrgId(accountAdmin.org.id)
      ).map { result =>

        assert(
          result == Right(()))
      }
    }


  }

}
