package app.api.campaigns.services

import api.accounts.models.{AccountProfileInfo, OrgId}
import api.accounts.{Account, AccountAccess, AccountMetadata, AccountService, AccountType, AccountUuid, OrgCountData, OrgMetadata, OrgPlan, OrgSettings, OrganizationRole, OrganizationWithCurrentData, TeamId}
import api.accounts.service.ResetUserCacheUtil
import api.calendar_app.models.CalendarAccountData
import api.campaigns.dao.CampaignSchedulingMetadataDAO
import api.campaigns.models.CampaignTypeData.DripCampaignData
import api.campaigns.models.{CallSettingSenderDetails, CampaignEmailSettingsId, CampaignStepData, CampaignStepType, CampaignType, LinkedinSettingSenderDetails, SmsSettingSenderDetails, WhatsappSettingSenderDetails}
import api.campaigns.{Campaign, CampaignDAO, CampaignEmailSettings, CampaignEmailSettingsUuid, CampaignSettings, CampaignStepVariantDAO, CampaignStepVariantForScheduling, CampaignStepWithChildren, ChannelSettingUuid}
import org.scalamock.scalatest.AsyncMockFactory
import org.scalatest.funspec.AsyncFunSpec
import api.campaigns.services.{CampaignCacheService, CampaignDAOService, CampaignId, CampaignService, CampaignStartService, ChannelSettingData, EmailSendingStatusForEmail, GetOrgEmailSendingStatusError}
import api.emails.EmailSettingDAO
import api.general.{GeneralSettingDAO, GeneralSettingService}
import api.spammonitor.dao.EmailSendingStatusDAO
import api.tasks.models.TaskPriority
import org.joda.time.DateTime
import sr_scheduler.CampaignStatus
import sr_scheduler.models.{CampaignEmailPriority, ChannelType}
import utils.{Helpers, PlanLimitService, SRLogger}
import utils.mq.channel_scheduler.MqEmailChannelScheduler
import utils.phishing.PhishingCheckService
import utils.sr_product_usage_data.services.SrUserFeatureUsageEventService
import api.spammonitor.dao.EmailSendingStatus
import api.spammonitor.model.{EmailSendingEntityTypeData, SendEmailStatusData, UnderReviewReason}
import api.spammonitor.service.SpamMonitorService
import app.test_fixtures.accounts.OrgCountDataFixture
import app.test_fixtures.campaign_settings.{CallSettingSenderDetailsFixtures, LinkedinSettingSenderDetailsFixtures, SmsSettingSenderDetailsFixtures, WhatsappSettingSenderDetailsFixtures}
import app.test_fixtures.organizationa.{OrgMetadataFixture, OrgPlanFixture}
import io.smartreach.esp.api.emails.EmailSettingId
import io.sr.billing_common.models.{AddonLicenceType, PlanID}
import play.api.libs.json.Json

import scala.util.{Failure, Success}

class CampaignStartServiceSpec extends AsyncFunSpec with AsyncMockFactory {


  given logger: SRLogger = new SRLogger(logRequestId = "CampaignStartServiceSpec: ")

//  val phishingCheckService=  mock[PhishingCheckService]
  val resetUserCacheUtil= mock[ResetUserCacheUtil]

  val campaignService= mock[CampaignService]
  val mqEmailSchedulerV2= mock[MqEmailChannelScheduler]
  val srUserFeatureUsageEventService= mock[SrUserFeatureUsageEventService]
  val accountService= mock[AccountService]
  val emailSettingDAO= mock[EmailSettingDAO]
  val emailSendingStatusDAO= mock[EmailSendingStatusDAO]
  val campaignStepVariantDAO= mock[CampaignStepVariantDAO]
  val campaignDAO= mock[CampaignDAO]
  val generalSettingService = mock[GeneralSettingService]
  val campaignSchedulingMetadataDAO = mock[CampaignSchedulingMetadataDAO]
  val planLimitService = mock[PlanLimitService]
  val campaignCacheService = mock[CampaignCacheService]
  val campaignDAOService = mock[CampaignDAOService]

  val campaignStartService= new CampaignStartService(
//    phishingCheckService =phishingCheckService,
    resetUserCacheUtil =resetUserCacheUtil,
    campaignService =campaignService,
    mqEmailSchedulerV2 =mqEmailSchedulerV2,
    srUserFeatureUsageEventService =srUserFeatureUsageEventService,
    accountService =accountService,
    campaignCacheService = campaignCacheService,
    emailSettingDAO =emailSettingDAO,
    emailSendingStatusDAO =emailSendingStatusDAO,
    campaignStepVariantDAO =campaignStepVariantDAO,
    planLimitService = planLimitService,
    generalSettingService = generalSettingService,
    campaignDAO =campaignDAO,
    campaignSchedulingMetadataDAO = campaignSchedulingMetadataDAO,
    campaignDAOService = campaignDAOService
  )
  val campaignId: Long = 5
  val team_id: Long = 3

  val campaignSettings = CampaignSettings(
    campaign_email_settings = List(
      CampaignEmailSettings(
        campaign_id = CampaignId(campaignId),
        sender_email_setting_id = EmailSettingId(1),
        receiver_email_setting_id = EmailSettingId(1),
        team_id = TeamId(team_id),
        uuid = CampaignEmailSettingsUuid("temp_setting_id"),
        id = CampaignEmailSettingsId(123),
        sender_email = "<EMAIL>",
        receiver_email = "<EMAIL>",
        max_emails_per_day_from_email_account = 400,
        signature = Some("emailsignature"),
        error = None,
        from_name = None
      )
    ),
    campaign_linkedin_settings = List(
      LinkedinSettingSenderDetailsFixtures.linkedin_setting_sender_details
    ),
    campaign_call_settings = List(
      CallSettingSenderDetailsFixtures.call_setting_sender_details
    ),
    campaign_whatsapp_settings = List(
      WhatsappSettingSenderDetailsFixtures.whatsapp_setting_sender_details
    ),
    campaign_sms_settings = List(
      SmsSettingSenderDetailsFixtures.sms_setting_sender_details
    ),
    ai_sequence_status = None,
    timezone = "UTC",
    daily_from_time = 8,
    daily_till_time = 12,
    sending_holiday_calendar_id = None,
    days_preference = List(false, true, true, true, true, false, false),
    mark_completed_after_days = 4,
    max_emails_per_day = 40,
    open_tracking_enabled = false,
    click_tracking_enabled = false,
    enable_email_validation = false,
    ab_testing_enabled = false,
    warmup_started_at = None,
    warmup_length_in_days = None,
    warmup_starting_email_count = None,
    show_soft_start_setting = false,
    schedule_start_at = None,
    schedule_start_at_tz = None,
    email_priority = CampaignEmailPriority.FIRST_EMAIL,
    send_plain_text_email = Some(false),
    campaign_type = CampaignType.MultiChannel,
    append_followups = true,
    opt_out_msg = "{{unsubscribe_link}}",
    opt_out_is_text = false,
    add_prospect_to_dnc_on_opt_out = true,
    triggers = Seq(),
    sending_mode = None,
    selected_calendar_data = None
  )

  // Note: 2 tests were failing if counts were directly replaced with `orgCountData_default`
  val counts: OrgCountData = OrgCountDataFixture.orgCountData_default.copy(
    base_licence_count = 1,
    additional_licence_count = 1,
    current_sending_email_accounts = 3,
    total_sending_email_accounts = 3,
    additional_spam_tests = 1,
    max_prospect_limit_org = 1,
    current_prospect_sent_count_org = 1,
    max_li_automation_seats = 50,
    current_li_automation_seats = 25,
    max_phone_number_buying_limit_org = 0
  )

  val orgSettings = OrgSettings(
    enable_ab_testing = true,
    disable_force_send = false,
    bulk_sender = false,
    allow_2fa = false,
    show_2fa_setting = false,
    enforce_2fa = false,
    allow_native_crm_integration = false,
      agency_option_allow_changing = false,
      agency_option_show = false)

  val orgPlan = OrgPlanFixture.orgPlanFixture

  val orgMetadata = OrgMetadataFixture.orgMetadataFixture2

  val org = OrganizationWithCurrentData(
    id = 5,
    name = "Shashank Dwivedi",
    owner_account_id = 7,
    counts = counts,
    settings = orgSettings,
    plan = orgPlan,
    is_agency = true,
    trial_ends_at = DateTime.now().plusDays(13),
    error = None,
    error_code = None,
    paused_till = None,
    errors = Seq(),
    warnings = Seq(),
    via_referral = true,
    org_metadata = orgMetadata
  )

  val campaign_id_VC = CampaignId(
    id = 3
  )

  val team_id_VC = TeamId(
    id = team_id
  )
  val campaign_uuid = s"cmp_${team_id_VC.id}_cfknacskndjcn"
  val campaign = Campaign(
    id = campaign_id_VC.id,
    uuid = Some(campaign_uuid),
    account_id = 3L,
    team_id = team_id_VC.id,
    shared_with_team = true,
    name = "New Campaign 0161",
    status = CampaignStatus.RUNNING,
    head_step_id = Some(17L),
    settings = campaignSettings,
    last_scheduled_at = None,
    created_at = DateTime.now()
  )

  val accountMetadata: AccountMetadata = AccountMetadata(
    // account_ui_version = None,
    is_profile_onboarding_done = None
  )

  val accountId: Long = 3

  val profile: AccountProfileInfo = AccountProfileInfo(
    first_name = "Shashank",
    last_name = "Dwivedi",
    company = Some("ShashankDwivedi"),
    timezone = Some("IN"),
    country_code = Some("IN"),
    mobile_country_code = Some("+91"),
    mobile_number = Some(9515253545L),
    twofa_enabled = false,
    has_gauthenticator = false,
    weekly_report_emails = Some("<EMAIL>"),
    scheduled_for_deletion_at = None,
    onboarding_phone_number = Some("+************")
  )

  val accountAdmin: Account = Account(
    id = AccountUuid("account_uuid"),
    internal_id = accountId,
    email = "<EMAIL>",
    email_verification_code = None,
    email_verification_code_created_at = None,
    created_at = DateTime.now().minusDays(1000),
    first_name = Some("Shashank"),
    last_name = Some("Dwivdi"),
    company = Some("SD"),
    timezone = None,
    profile = profile,
    org_role = Some(OrganizationRole.OWNER),
    teams = Seq(),
    account_type = AccountType.AGENCY,
    org = org,
    active = true,
    email_notification_summary = "dSFA",
    account_metadata = accountMetadata,
    email_verified = true,
    signupType = None,
    account_access = AccountAccess(
      inbox_access = false
    ),
    calendar_account_data = None

  )

  val campaignStepVariantForScheduling = CampaignStepVariantForScheduling(
    id = 1,
    step_id = 1,
    campaign_id = 1,
    template_id = None,
    step_data = CampaignStepData.AutoEmailStep(
      subject = "variant subject",
      body = "Variant body",
    ),
    step_label = None,
    step_delay = 10,
    notes = Some("Note"),
    priority = Some(TaskPriority.Normal),
    active = true,
    scheduled_count = 1
  )

  val headStepId = 3

  val campaignStepWithChildren = CampaignStepWithChildren(
    id = headStepId,
    label = None,
    campaign_id = campaignId,
    delay = 10,
    step_type = CampaignStepType.AutoEmailStep,
    created_at = DateTime.parse("2022-03-21T11:58:03.294Z"),
    children = List(2, 3, 4),
    variants = Seq(
      campaignStepVariantForScheduling,
      campaignStepVariantForScheduling.copy(id = 53, step_data = CampaignStepData.AutoEmailStep(body = "Variant body", subject = "Variant subject V2")),
      campaignStepVariantForScheduling.copy(id = 54)
    )
  )




  val emailSendingStatusForEmail = Seq(EmailSendingStatusForEmail(
    sending_email_status = EmailSendingStatus(
      id = 5,
      entity_type =  EmailSendingEntityTypeData.SendingEmailData(
        orgId = OrgId(3),
        senderId = 3,
        emailSettingEmail = "email_setting_id"
    ),
      send_email_status =  SendEmailStatusData.AllowedData(),
      created_at = DateTime.parse("2022-03-21T11:58:03.294Z"),
      email =  "<EMAIL>"
    )
  ))

  val emailSendingStatusForEmail_no_email_setting_id = Left(GetOrgEmailSendingStatusError.NoEmailSettingId)
  val channel_setting_data = ChannelSettingData(
    channel_type = ChannelType.LinkedinChannel,
    channel_setting_uuid = ChannelSettingUuid(uuid = "linkeidn_uuid"),
    team_id = team_id_VC,
    campaign_id = campaign_id_VC
  )

  val activeEmailSettingIds = Seq(
    EmailSettingId(emailSettingId = 7L),
    EmailSettingId(emailSettingId = 11L),
    EmailSettingId(emailSettingId = 17L)
  )

  val error = new Exception("An Error Occured")

  describe("testing campaignStartService.validateEmailSendingSettings"){

    // Email Channel Tests
    it("should throw a please select a valid email addressed if sender_email_setting_id is not present"){

      val res = campaignStartService.validateMultiChannelSettings(
        c = campaign.copy(settings = campaign.settings.copy(campaign_email_settings = List())),
        planName = "base-v3",
        campaignSteps = Seq(campaignStepWithChildren),
        campaignOwner = accountAdmin,
        activeSenderEmailSettingIds = Seq(EmailSettingId(7L)),
        emailSendingStatusForEmail = Right(emailSendingStatusForEmail),
        channelSettingData = Seq()
      )

      res match {
        case Failure(err) =>

//          println(s"err -> ${err.getMessage} stack -> ${Helpers.getStackTraceAsString(err)}err")
          assert(err.getMessage == "Please select a valid email address to send emails from")
        case Success(value) =>
          println(value)
          assert(false)

      }
    }

    it("should throw a please select a valid email addressed if receiver_email_setting_id is not present") {

      val res = campaignStartService.validateMultiChannelSettings(
        c = campaign.copy(settings = campaign.settings.copy(campaign_email_settings = List())),
        planName = "base-v3",
        campaignSteps = Seq(campaignStepWithChildren),
        campaignOwner = accountAdmin,
        activeSenderEmailSettingIds = Seq(EmailSettingId(7L)),
        emailSendingStatusForEmail = Right(emailSendingStatusForEmail),
        channelSettingData = Seq()
      )

      res match {
        case Failure(err) =>

//          println(s"err -> ${err.getMessage} stack -> ${Helpers.getStackTraceAsString(err)}err")
          assert(err.getMessage == "Please select a valid email address to send emails from")
        case Success(value) =>
          println(value)
          assert(false)

      }
    }

//    it("should throw a sending email under review error") {
//
//      val res = campaignStartService.validateMultiChannelSettings(
//        c = campaign,
//        planName = "base-v3",
//        campaignSteps = Seq(campaignStepWithChildren),
//        campaignOwner = accountAdmin,
//        activeSenderEmailSettingIds = Seq(EmailSettingId(7L)),
//        emailSendingStatusForEmail = Right(emailSendingStatusForEmail.map(st =>
//          st.copy(sending_email_status = st.sending_email_status.copy(send_email_status = SendEmailStatusData
//            .ManualReviewData(
//              underReviewReasons = Seq(UnderReviewReason.SendingEmailDomainAge),
//              stepLabels = Seq("Day1: Opening")
//            ))
//          ))),
//        channelSettingData = Seq()
//      )
//
//      res match {
//        case Failure(err) =>
//
//          //          println(s"err -> ${err.getMessage} stack -> ${Helpers.getStackTraceAsString(err)}err")
//          assert(err.getMessage == "Your sending is under review. It can take upto 1 business day for our team to review, and then you will be able to start the campaign.")
//        case Success(value) =>
//          println(value)
//          assert(false)
//
//      }
//    }

//    it("should throw a sending email sending is blocked") {
//
//      val res = campaignStartService.validateMultiChannelSettings(
//        c = campaign,
//        planName = "base-v3",
//        campaignSteps = Seq(campaignStepWithChildren),
//        campaignOwner = accountAdmin,
//        activeSenderEmailSettingIds = Seq(EmailSettingId(7L)),
//        emailSendingStatusForEmail = Right(emailSendingStatusForEmail.map(st =>
//          st.copy(sending_email_status = st.sending_email_status.copy(send_email_status = SendEmailStatusData
//            .BlockedData(
//              blockedReasons = Seq(UnderReviewReason.SendingEmailDomainAge),
//              stepLabels = Seq("Day1: Opening")
//            ))
//          ))),
//        channelSettingData = Seq()
//      )
//
//      res match {
//        case Failure(err) =>
//
//          //          println(s"err -> ${err.getMessage} stack -> ${Helpers.getStackTraceAsString(err)}err")
//          assert(err.getMessage == "Your Organization is blocked from sending campaigns")
//        case Success(value) =>
//          println(value)
//          assert(false)
//
//      }
//    }

    it("should throw limit exceeded error via sending email account limit is 3") {

      (planLimitService.checkLimitAccordingToAddonLicenseTypeUsingTeamId(
        _: TeamId,
        _: AddonLicenceType,
        _: Boolean,
      )(
        using _: SRLogger
      ))
        .expects(team_id_VC, AddonLicenceType.EmailAccounts, false, logger)
        .returning(Failure(new Exception("To send from more than 3 email accounts, you need to add additional sending email accounts to your subscription. You can do this under Settings -> Billing")))

      val res = campaignStartService.validateMultiChannelSettings(
        c = campaign,
        planName = "base-v3",
        campaignSteps = Seq(campaignStepWithChildren),
        campaignOwner = accountAdmin,
        activeSenderEmailSettingIds = activeEmailSettingIds,
        emailSendingStatusForEmail = Right(emailSendingStatusForEmail.map(st =>
          st.copy(
            sending_email_status = st.sending_email_status.copy(send_email_status = SendEmailStatusData
              .AllowedData()
            )))),
        channelSettingData = Seq()
      )

      res match {
        case Failure(err) =>

          //          println(s"err -> ${err.getMessage} stack -> ${Helpers.getStackTraceAsString(err)}err")
          assert(err.getMessage == "To send from more than 3 email accounts, you need to add additional sending email accounts to your subscription. You can do this under Settings -> Billing")
        case Success(value) =>
          println(value)
          assert(false)

      }
    }
    
    // testing errors via emailSendingStatus
    it("should throw no email setting id") {

      val res = campaignStartService.validateMultiChannelSettings(
        c = campaign,
        planName = "base-v3",
        campaignSteps = Seq(campaignStepWithChildren),
        campaignOwner = accountAdmin,
        activeSenderEmailSettingIds = activeEmailSettingIds,
        emailSendingStatusForEmail = Left(GetOrgEmailSendingStatusError.NoEmailSettingId),
        channelSettingData = Seq()
      )

      res match {
        case Failure(err) =>

          //          println(s"err -> ${err.getMessage} stack -> ${Helpers.getStackTraceAsString(err)}err")
          assert(err.getMessage == "No email setting id")
        case Success(value) =>
          println(value)
          assert(false)

      }
    }

    it("should throw no campaign found") {

      val res = campaignStartService.validateMultiChannelSettings(
        c = campaign,
        planName = "base-v3",
        campaignSteps = Seq(campaignStepWithChildren),
        campaignOwner = accountAdmin,
        activeSenderEmailSettingIds = activeEmailSettingIds,
        emailSendingStatusForEmail = Left(GetOrgEmailSendingStatusError.NoCampaignFound),
        channelSettingData = Seq()
      )

      res match {
        case Failure(err) =>

          //          println(s"err -> ${err.getMessage} stack -> ${Helpers.getStackTraceAsString(err)}err")
          assert(err.getMessage == "No campaign found")
        case Success(value) =>
          println(value)
          assert(false)

      }
    }

    it("should throw Error While Getting Sending Email Status") {

      val res = campaignStartService.validateMultiChannelSettings(
        c = campaign,
        planName = "base-v3",
        campaignSteps = Seq(campaignStepWithChildren),
        campaignOwner = accountAdmin,
        activeSenderEmailSettingIds = activeEmailSettingIds,
        emailSendingStatusForEmail = Left(GetOrgEmailSendingStatusError.ErrorWhileGettingSendingEmailStatus(err  = error)),
        channelSettingData = Seq()
      )

      res match {
        case Failure(err) =>

          //          println(s"err -> ${err.getMessage} stack -> ${Helpers.getStackTraceAsString(err)}err")
          assert(err.getMessage == "Error while getting sending email status")
        case Success(value) =>
          println(value)
          assert(false)

      }
    }

    it("should throw Error While Getting Organisation Status") {

      val res = campaignStartService.validateMultiChannelSettings(
        c = campaign,
        planName = "base-v3",
        campaignSteps = Seq(campaignStepWithChildren),
        campaignOwner = accountAdmin,
        activeSenderEmailSettingIds = activeEmailSettingIds,
        emailSendingStatusForEmail = Left(GetOrgEmailSendingStatusError.ErrorWhileGettingOrganizationStatus(err = error)),
        channelSettingData = Seq()
      )

      res match {
        case Failure(err) =>

          //          println(s"err -> ${err.getMessage} stack -> ${Helpers.getStackTraceAsString(err)}err")
          assert(err.getMessage == "Error while getting sending email status for organisation")
        case Success(value) =>
          println(value)
          assert(false)

      }
    }

    it("should throw a linkedin setting id isn't present error as channelSettingData is empty") {

      val res = campaignStartService.validateMultiChannelSettings(
        c = campaign,
        planName = "base-v3",
        campaignSteps = Seq(campaignStepWithChildren.copy(step_type = CampaignStepType.LinkedinInmail)),
        campaignOwner = accountAdmin,
        activeSenderEmailSettingIds = activeEmailSettingIds,
        emailSendingStatusForEmail = Right(emailSendingStatusForEmail),
        channelSettingData = Seq()
      )

      res match {
        case Failure(err) =>

          assert(err.getMessage == "Please add a linkedin setting id in Campaign")
        case Success(value) =>
          println(value)
          assert(false)

      }
    }

    it("should success in linkedin step") {

      val res = campaignStartService.validateMultiChannelSettings(
        c = campaign,
        planName = "base-v3",
        campaignSteps = Seq(campaignStepWithChildren.copy(step_type = CampaignStepType.LinkedinInmail)),
        campaignOwner = accountAdmin,
        activeSenderEmailSettingIds = activeEmailSettingIds,
        emailSendingStatusForEmail = Right(emailSendingStatusForEmail),
        channelSettingData = Seq(channel_setting_data)
      )

      res match {
        case Failure(err) =>
//          println(s"err -> ${err.getMessage} stack -> ${Helpers.getStackTraceAsString(err)}err")
          assert(false)
        case Success(value) =>
//          println(value)
          assert(true)

      }
    }

    it("should throw a whatsapp setting id isn't present error as channelSettingData is empty") {

      val res = campaignStartService.validateMultiChannelSettings(
        c = campaign,
        planName = "base-v3",
        campaignSteps = Seq(campaignStepWithChildren.copy(step_type = CampaignStepType.WhatsappMessage)),
        campaignOwner = accountAdmin,
        activeSenderEmailSettingIds = activeEmailSettingIds,
        emailSendingStatusForEmail = Right(emailSendingStatusForEmail),
        channelSettingData = Seq()
      )

      res match {
        case Failure(err) =>

          assert(err.getMessage == "Please add a whatsapp setting id in Campaign")
        case Success(value) =>
          println(value)
          assert(false)

      }
    }

    it("should success in whatsapp step") {

      val res = campaignStartService.validateMultiChannelSettings(
        c = campaign,
        planName = "base-v3",
        campaignSteps = Seq(campaignStepWithChildren.copy(step_type = CampaignStepType.WhatsappMessage)),
        campaignOwner = accountAdmin,
        activeSenderEmailSettingIds = activeEmailSettingIds,
        emailSendingStatusForEmail = Right(emailSendingStatusForEmail),
        channelSettingData = Seq(channel_setting_data.copy(channel_type = ChannelType.WhatsappChannel))
      )

      res match {
        case Failure(err) =>
          //          println(s"err -> ${err.getMessage} stack -> ${Helpers.getStackTraceAsString(err)}err")
          assert(false)
        case Success(value) =>
          //          println(value)
          assert(true)

      }
    }

    it("should throw a sms setting id isn't present error as channelSettingData is empty") {

      val res = campaignStartService.validateMultiChannelSettings(
        c = campaign,
        planName = "base-v3",
        campaignSteps = Seq(campaignStepWithChildren.copy(step_type = CampaignStepType.SmsMessage)),
        campaignOwner = accountAdmin,
        activeSenderEmailSettingIds = activeEmailSettingIds,
        emailSendingStatusForEmail = Right(emailSendingStatusForEmail),
        channelSettingData = Seq()
      )

      res match {
        case Failure(err) =>

          assert(err.getMessage == "Please add a sms setting id in Campaign")
        case Success(value) =>
          println(value)
          assert(false)

      }
    }

    it("should success in sms step") {

      val res = campaignStartService.validateMultiChannelSettings(
        c = campaign,
        planName = "base-v3",
        campaignSteps = Seq(campaignStepWithChildren.copy(step_type = CampaignStepType.SmsMessage)),
        campaignOwner = accountAdmin,
        activeSenderEmailSettingIds = activeEmailSettingIds,
        emailSendingStatusForEmail = Right(emailSendingStatusForEmail),
        channelSettingData = Seq(channel_setting_data.copy(channel_type = ChannelType.SmsChannel))
      )

      res match {
        case Failure(err) =>
          //          println(s"err -> ${err.getMessage} stack -> ${Helpers.getStackTraceAsString(err)}err")
          assert(false)
        case Success(value) =>
          //          println(value)
          assert(true)

      }
    }

    it("should success in general step") {

      val res = campaignStartService.validateMultiChannelSettings(
        c = campaign,
        planName = "base-v3",
        campaignSteps = Seq(campaignStepWithChildren.copy(step_type = CampaignStepType.GeneralTask)),
        campaignOwner = accountAdmin,
        activeSenderEmailSettingIds = activeEmailSettingIds,
        emailSendingStatusForEmail = Right(emailSendingStatusForEmail),
        channelSettingData = Seq()
      )

      res match {
        case Failure(err) =>
          //          println(s"err -> ${err.getMessage} stack -> ${Helpers.getStackTraceAsString(err)}err")
          assert(false)
        case Success(value) =>
          //          println(value)
          assert(true)

      }
    }

    // First step success 2nd step fail
    it("should success in linkedin step then fail in whatsapp step") {

      val res = campaignStartService.validateMultiChannelSettings(
        c = campaign,
        planName = "base-v3",
        campaignSteps = Seq(
          campaignStepWithChildren.copy(id = 2, step_type = CampaignStepType.LinkedinInmail, children = List()),
          campaignStepWithChildren.copy(id = 3, step_type = CampaignStepType.WhatsappMessage, children = List())
        ),
        campaignOwner = accountAdmin,
        activeSenderEmailSettingIds = activeEmailSettingIds,
        emailSendingStatusForEmail = Right(emailSendingStatusForEmail),
        channelSettingData = Seq(channel_setting_data.copy(channel_type = ChannelType.LinkedinChannel))
      )

      res match {
        case Failure(err) =>
          //          println(s"err -> ${err.getMessage} stack -> ${Helpers.getStackTraceAsString(err)}err")
          assert(err.getMessage == "Please add a whatsapp setting id in Campaign")
        case Success(value) =>
          //          println(value)
          assert(true)

      }
    }

    // First step success 2nd step fail
    it("should success in whatsapp step then fail in linkedin step") {

      val res = campaignStartService.validateMultiChannelSettings(
        c = campaign,
        planName = "base-v3",
        campaignSteps = Seq(
          campaignStepWithChildren.copy(id = 2, step_type = CampaignStepType.WhatsappMessage, children = List()),
          campaignStepWithChildren.copy(id = 3, step_type = CampaignStepType.LinkedinInmail, children = List())
        ),
        campaignOwner = accountAdmin,
        activeSenderEmailSettingIds = activeEmailSettingIds,
        emailSendingStatusForEmail = Right(emailSendingStatusForEmail),
        channelSettingData = Seq(channel_setting_data.copy(channel_type = ChannelType.WhatsappChannel))
      )

      res match {
        case Failure(err) =>
          //          println(s"err -> ${err.getMessage} stack -> ${Helpers.getStackTraceAsString(err)}err")
          assert(err.getMessage == "Please add a linkedin setting id in Campaign")
        case Success(value) =>
          //          println(value)
          assert(true)

      }
    }


    //1 step - whatsapp, 2nd step - linkedin success  3rd sms - fail
    it("should success in whatsapp step then sucess in linkedin step then fail in sms step") {

      val res = campaignStartService.validateMultiChannelSettings(
        c = campaign,
        planName = "base-v3",
        campaignSteps = Seq(
          campaignStepWithChildren.copy(id = 2, step_type = CampaignStepType.WhatsappMessage, children = List()),
          campaignStepWithChildren.copy(id = 3, step_type = CampaignStepType.LinkedinInmail, children = List()),
          campaignStepWithChildren.copy(id = 4, step_type = CampaignStepType.SmsMessage, children = List())
        ),
        campaignOwner = accountAdmin,
        activeSenderEmailSettingIds = activeEmailSettingIds,
        emailSendingStatusForEmail = Right(emailSendingStatusForEmail),
        channelSettingData = Seq(
          channel_setting_data.copy(channel_type = ChannelType.WhatsappChannel),
          channel_setting_data.copy(channel_type = ChannelType.LinkedinChannel),
        )
      )

      res match {
        case Failure(err) =>
          //          println(s"err -> ${err.getMessage} stack -> ${Helpers.getStackTraceAsString(err)}err")
          assert(err.getMessage == "Please add a sms setting id in Campaign")
        case Success(value) =>
          //          println(value)
          assert(true)

      }
    }


    // 1step Email - Success 2nd Whatsapp - Success 3rd Linkedin - Success 4th Smss - Fail
    it("should success in email, whatsapp, linkeidn step then fail in sms step") {

      (planLimitService.checkLimitAccordingToAddonLicenseTypeUsingTeamId(
        _: TeamId,
        _: AddonLicenceType,
        _: Boolean,
      )(
        using _: SRLogger
      ))
        .expects(team_id_VC, AddonLicenceType.EmailAccounts, false, logger)
        .returning(Success(OrgId(org.id)))

      val res = campaignStartService.validateMultiChannelSettings(
        c = campaign,
        planName = "base-v3",
        campaignSteps = Seq(
          campaignStepWithChildren,
          campaignStepWithChildren.copy(step_type = CampaignStepType.WhatsappMessage),
          campaignStepWithChildren.copy(step_type = CampaignStepType.LinkedinInmail),
          campaignStepWithChildren.copy(step_type = CampaignStepType.SmsMessage)
        ),
        campaignOwner = accountAdmin.copy(org = org.copy(counts = counts.copy(current_sending_email_accounts = 2))),
        activeSenderEmailSettingIds = activeEmailSettingIds,
        emailSendingStatusForEmail = Right(emailSendingStatusForEmail),
        channelSettingData = Seq(
          channel_setting_data.copy(channel_type = ChannelType.WhatsappChannel),
          channel_setting_data.copy(channel_type = ChannelType.LinkedinChannel),
        )
      )

      res match {
        case Failure(err) =>
          //          println(s"err -> ${err.getMessage} stack -> ${Helpers.getStackTraceAsString(err)}err")
          assert(err.getMessage == "Please add a sms setting id in Campaign")
        case Success(value) =>
          //          println(value)
          assert(true)

      }
    }
  }

  describe("CampaignStartService.hasDripConditions") {
    it("should give true") {
      val head = "condition_call"


      val edges = List(
        Json.obj("id" -> "xyz", "source" -> "condition_call", "target" -> "condition_email_1", "label" -> "yes"),
        Json.obj("id" -> "abc", "source" -> "condition_call", "target" -> "has_linkedin_url", "label" -> "no"),
        Json.obj("id" -> "abc", "source" -> "condition_email_1", "target" -> "emailVariant1.step_id.toString", "label" -> "yes"),
        Json.obj("id" -> "abc", "source" -> "emailVariant1.step_id.toString", "target" -> "callVariant1.step_id.toString", "label" -> "no_condition"),
        Json.obj("id" -> "abc", "source" -> "has_linkedin_url", "target" -> "linkedinVariant1.step_id.toString", "label" -> "yes"),
        Json.obj("id" -> "abc", "source" -> "linkedinVariant1.step_id.toString", "target" -> "emailVariant2.step_id.toString", "label" -> "no_condition"),
      )

      val node = List(
        Json.obj("id" -> "condition_call", "position" -> Json.obj("x" -> 0, "y" -> 0), "data" -> Json.obj("label" -> "has_phone_number", "type" -> "condition"), "type" -> "has_phone_number"),
        Json.obj("id" -> "condition_email_1", "position" -> Json.obj("x" -> 0, "y" -> 0), "data" -> Json.obj("label" -> "has_email", "type" -> "condition"), "type" -> "has_email"),
        Json.obj("id" -> "has_linkedin_url", "position" -> Json.obj("x" -> 0, "y" -> 0), "data" -> Json.obj("label" -> "has_linkedin_url", "type" -> "condition"), "type" -> "has_linkedin_url"),
        Json.obj("id" -> "emailVariant1.step_id.toString", "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "1", "type" -> "step"), "type" -> "step"),
        Json.obj("id" -> "linkedinVariant1.step_id.toString", "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "2", "type" -> "step"), "type" -> "step"),
        Json.obj("id" -> "emailVariant2.step_id.toString", "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "3", "type" -> "step"), "type" -> "step"),
        Json.obj("id" -> "callVariant1.step_id.toString", "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "4", "type" -> "step"), "type" -> "step"),
      )

      
      
      assert(CampaignStartService.hasDripConditions(dripCampaignData = Some(
        DripCampaignData(
          nodes = node,
          edges = edges,
          head_node_id = head
        )
      )))
    }

    it("should give false") {
      val head = "condition_call"


      val edges = List(
        Json.obj("id" -> "xyz", "source" -> "condition_call", "target" -> "condition_email_1", "label" -> "yes"),
        Json.obj("id" -> "abc", "source" -> "condition_call", "target" -> "has_linkedin_url", "label" -> "no"),
        Json.obj("id" -> "abc", "source" -> "condition_email_1", "target" -> "emailVariant1.step_id.toString", "label" -> "yes"),
        Json.obj("id" -> "abc", "source" -> "emailVariant1.step_id.toString", "target" -> "callVariant1.step_id.toString", "label" -> "no_condition"),
        Json.obj("id" -> "abc", "source" -> "has_linkedin_url", "target" -> "linkedinVariant1.step_id.toString", "label" -> "yes"),
        Json.obj("id" -> "abc", "source" -> "linkedinVariant1.step_id.toString", "target" -> "emailVariant2.step_id.toString", "label" -> "no_condition"),
      )

      val node = List(
        Json.obj("id" -> "emailVariant1.step_id.toString", "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "1", "type" -> "step"), "type" -> "step"),
        Json.obj("id" -> "linkedinVariant1.step_id.toString", "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "2", "type" -> "step"), "type" -> "step"),
        Json.obj("id" -> "emailVariant2.step_id.toString", "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "3", "type" -> "step"), "type" -> "step"),
        Json.obj("id" -> "callVariant1.step_id.toString", "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "4", "type" -> "step"), "type" -> "step"),
      )


      assert(!CampaignStartService.hasDripConditions(dripCampaignData = Some(
        DripCampaignData(
          nodes = node,
          edges = edges,
          head_node_id = head
        )
      )))
    }
  }


}
