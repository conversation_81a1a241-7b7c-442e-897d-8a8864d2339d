package app.api.campaigns.services

import api.APIErrorResponse.ErrorResponseProspectsAssignApi
import api.ApiVersion
import api.accounts.{Account, AccountAccess, AccountMetadata, AccountService, AccountType, AccountUuid, OrgCountData, OrgMetadata, OrgPlan, OrgSettings, OrganizationRole, OrganizationWithCurrentData, TeamId}
import api.campaigns.{CampaignBasicInfo, CampaignEmailSettings, CampaignEmailSettingsUuid, CampaignSettings, CampaignWithStatsAndEmail}
import api.accounts.models.{AccountId, AccountProfileInfo, OrgId}
import api.accounts.{Account, AccountAccess, AccountMetadata, AccountType, AccountUuid, OrgCountData, OrgMetadata, OrgPlan, OrgSettings, OrganizationRole, OrganizationWithCurrentData, TeamId}
import api.campaigns.{CampaignBasicInfo, CampaignEmailSettings, CampaignEmailSettingsUuid, CampaignSettings, CampaignWithStatsAndEmail, ChannelSettingUuid}
import api.calendar_app.models.CalendarAccountData
import api.campaigns.DataForCampaignAssignProspects.AssignProspectToCampaignDataV3
import api.campaigns.models.{CallSettingSenderDetails, CampaignEmailSettingsId, CampaignType, IgnoreProspectsInOtherCampaigns, LinkedinSettingSenderDetails, SmsSettingSenderDetails, WhatsappSettingSenderDetails}
import api.campaigns.{CampaignBasicInfo, CampaignEmailSettings, CampaignEmailSettingsUuid, CampaignSettings, CampaignWithStatsAndEmail}
import api.campaigns.services.{AssignProspectsError, AssignProspectsErrorV3, AssignProspectsResponseV3, CampaignApiError, CampaignApiService, CampaignId, CampaignProspectAssign, CampaignResource, CampaignService, GetProspectIdFromUuidError, ProspectAssignErrorType}
import api.campaigns.services.{AssignProspectsError, AssignProspectsErrorV3, AssignProspectsResponseV3, CampaignApiError, CampaignApiService, CampaignId, CampaignProspectAssign, CampaignProspectService, CampaignResource, CampaignService, GetProspectIdFromUuidError, ProspectAssignErrorType}
import api.emails.services.EmailSettingService
import api.prospects.dao_service.ProspectDAOService
import api.prospects.models.ProspectId
import api.prospects.{OwnedProspectsForAssigning, ProspectService, ProspectUuid, ValidAndInvalidProspectUuidIdList}
import api.prospects.service.ProspectServiceV2
import api.reports.{AllCampaignStats, ReplySentimentStats}
import api.tags.models.{CampaignTag, CampaignTagUuid}
import api.team.TeamUuid
import api.team.service.TeamService
import app.test_fixtures.accounts.OrgCountDataFixture
import app.test_fixtures.campaign_settings.{CallSettingSenderDetailsFixtures, LinkedinSettingSenderDetailsFixtures, SmsSettingSenderDetailsFixtures, WhatsappSettingSenderDetailsFixtures}
import app.test_fixtures.organizationa.{OrgMetadataFixture, OrgPlanFixture}
import io.smartreach.esp.api.emails.EmailSettingId
import org.joda.time.DateTime
import org.scalamock.scalatest.AsyncMockFactory
import org.scalatest.funspec.AsyncFunSpec
import play.api.libs.json.{JsValue, Json}
import sr_scheduler.CampaignStatus
import sr_scheduler.models.CampaignEmailPriority
import utils.SRLogger
import play.api.libs.json.JodaReads._
import play.api.libs.json.JodaWrites._

import scala.util.{Failure, Success}

class CampaignApiServiceSpec extends AsyncFunSpec with AsyncMockFactory {

  given logger: SRLogger = new SRLogger(logRequestId = "CampaignServiceSpec: ")

  val teamService: TeamService = mock[TeamService]
  val prospectService: ProspectService = mock[ProspectService]
  val campaignProspectAssign: CampaignProspectAssign = mock[CampaignProspectAssign]
  val prospectServiceV2: ProspectServiceV2 = mock[ProspectServiceV2]
  val accountService: AccountService = mock[AccountService]
  val prospectDAOService: ProspectDAOService = mock[ProspectDAOService]
  val campaignProspectService: CampaignProspectService = mock[CampaignProspectService]
  val emailSettingService: EmailSettingService = mock[EmailSettingService]
  val campaignApiService = new CampaignApiService(
    teamService = teamService,
    prospectService = prospectService,
    campaignProspectAssign = campaignProspectAssign,
    prospectServiceV2 = prospectServiceV2,
    accountService = accountService,
    prospectDAOService = prospectDAOService,
    campaignProspectService = campaignProspectService,
    emailSettingService = emailSettingService
  )

  val campaign_id: Long = 121L
  val campaign_name = "CampaignName"
  val permittedAccountIds = Seq(2L, 1L)
  val teamId = TeamId(id = 37L)
  val orgId: OrgId = OrgId(id = 99L)
  val ownerId: Long = 2L
  val accountId = AccountId(2L)

  val first_name = "Adminfirst"
  val last_name = "Adminlast"
  val company = "CompanyName"
  val email = "<EMAIL>"
  val campaign_uuid = "cmp_aa_campaign_uuid"

  val aDate = DateTime.parse("2022-3-27")

  val allCampaignStats = AllCampaignStats(
    total_sent = 1,
    total_opened = 1,
    total_clicked = 1,
    total_replied = 1,
    total_steps = 1,
    current_prospects = 1,
    current_opted_out = 1,
    current_completed = 1,
    current_bounced = 1,
    current_to_check = 1,
    current_failed_email_validation = 1,
    current_in_progress = 1,
    current_unsent_prospects = 1,
    current_do_not_contact = 1,
    reply_sentiment_stats = ReplySentimentStats(
      positive = 0
    )
  )

  val campaignSettings = CampaignSettings(
    campaign_email_settings = List(
      CampaignEmailSettings(
        campaign_id = CampaignId(campaign_id),
        sender_email_setting_id = EmailSettingId(1),
        receiver_email_setting_id = EmailSettingId(1),
        team_id = teamId,
        uuid = CampaignEmailSettingsUuid("temp_setting_id"),
        id = CampaignEmailSettingsId(123),
        sender_email = "<EMAIL>",
        receiver_email = "<EMAIL>",
        max_emails_per_day_from_email_account = 400,
        signature = Some("emailsignature"),
        error = None,
        from_name = None
      )
    ),
    campaign_linkedin_settings = List(
      LinkedinSettingSenderDetailsFixtures.linkedin_setting_sender_details
    ),
    campaign_call_settings = List(
      CallSettingSenderDetailsFixtures.call_setting_sender_details
    ),
    campaign_whatsapp_settings = List(
      WhatsappSettingSenderDetailsFixtures.whatsapp_setting_sender_details
    ),
    campaign_sms_settings = List(
      SmsSettingSenderDetailsFixtures.sms_setting_sender_details
    ),
    timezone = "UTC",
    daily_from_time = 8,
    daily_till_time = 12,
    ai_sequence_status = None,
    sending_holiday_calendar_id = None,
    days_preference = List(false, true, true, true, true, false, false),
    mark_completed_after_days = 4,
    max_emails_per_day = 40,
    open_tracking_enabled = false,
    click_tracking_enabled = false,
    enable_email_validation = false,
    ab_testing_enabled = false,
    warmup_started_at = None,
    warmup_length_in_days = None,
    warmup_starting_email_count = None,
    show_soft_start_setting = false,
    schedule_start_at = None,
    schedule_start_at_tz = None,
    email_priority = CampaignEmailPriority.FIRST_EMAIL,
    send_plain_text_email = Some(false),
    campaign_type = CampaignType.MultiChannel,
    append_followups = true,
    opt_out_msg = "{{unsubscribe_link}}",
    opt_out_is_text = false,
    add_prospect_to_dnc_on_opt_out = true,
    triggers = Seq(),
    sending_mode = None,
    selected_calendar_data = None
  )

  val campaignWithStatsAndEmail = CampaignWithStatsAndEmail(
    id = 121L,
    uuid = Some(campaign_uuid),
    team_id = teamId.id,
    shared_with_team = true,
    name = campaign_name,
    owner_name = first_name,
    owner_email = email,
    owner_id = ownerId,
    status = CampaignStatus.RUNNING,
    tags = Seq(CampaignTag(11L, "tag1", CampaignTagUuid("tags_abcefg"))),
    spam_test_exists = false,
    warmup_is_on = false,

    stats = allCampaignStats,

    head_step_id = Some(22L),

    ai_generation_context = None,

    settings = campaignSettings,

    created_at = aDate,

    error = Some("campaign error"),
    is_archived = false

  )
  val team_uuid = "team_teamuuid"

  val campaignBasicInfoSeq: Seq[CampaignBasicInfo] = Seq(CampaignBasicInfo(
    id = 121L,
    uuid = Some(campaign_uuid),
    team_id = teamId.id,
    shared_with_team = true,
    name = campaign_name,
    owner_name = first_name,
    owner_email = email,
    owner_id = ownerId,
    status = CampaignStatus.RUNNING,
    tags = Seq(CampaignTag(11L, "tag1", CampaignTagUuid("tags_abcefg"))),
    spam_test_exists = false,
    ai_generation_context = None,
    head_step_id = Some(22L),
    settings = campaignSettings,
    created_at = aDate,
    error = Some("campaign error"),
    campaign_has_email_step = true,
    is_archived = false,
    warmup_is_on = false
  ))

  val counts: OrgCountData = OrgCountDataFixture.orgCountData_default

  val orgPlan = OrgPlanFixture.orgPlanFixture

  val orgSettings = OrgSettings(
    enable_ab_testing = true,
    disable_force_send = false,
    bulk_sender = false,
    allow_2fa = false,
    show_2fa_setting = false,
    enforce_2fa = false,
    allow_native_crm_integration = false,
    agency_option_allow_changing = false,
    agency_option_show = false)

  val orgMetadata = OrgMetadataFixture.orgMetadataFixture2

  val org = OrganizationWithCurrentData(
    id = orgId.id,
    name = "Animesh",
    owner_account_id = 1,
    counts = counts,
    settings = orgSettings,
    plan = orgPlan,
    is_agency = true,
    trial_ends_at = DateTime.now().plusDays(10),
    error = None,
    error_code = None,
    paused_till = None,
    errors = Seq(),
    warnings = Seq(),
    via_referral = true,
    org_metadata = orgMetadata
  )

  val doer: Account = Account(
    id = AccountUuid("account_uuid"),
    internal_id = 3,
    email = "<EMAIL>",
    email_verification_code = None,
    email_verification_code_created_at = None,
    created_at = DateTime.now().minusDays(1000),
    first_name = Some("Shubham"),
    last_name = Some("Kudekar"),
    company = Some("SK"),
    timezone = None,
    profile = AccountProfileInfo(
      first_name = "Shubham",
      last_name = "Kudekar",
      company = None,
      timezone = None,
      country_code = None,
      mobile_country_code = None,
      mobile_number = None,
      twofa_enabled = false,
      has_gauthenticator = false,
      weekly_report_emails = None,
      scheduled_for_deletion_at = None,
      onboarding_phone_number = Some("+************")
    ),
    org_role = Some(OrganizationRole.OWNER),
    teams = Seq(),
    account_type = AccountType.AGENCY,
    org = org,

    active = true,
    email_notification_summary = "dSFA",
    account_metadata = AccountMetadata(
      is_profile_onboarding_done = Some(true)
    ),
    email_verified = true,
    signupType = None,
    account_access = AccountAccess(
      inbox_access = false
    ),
    calendar_account_data = None
  )

  describe("tests for campaign fetch api v1,v2,v3") {

    it("gives error when error while fetching team_uuid") {

      (teamService.getTeamUuidFromTeamId(_: TeamId, _: OrgId)(using _: SRLogger))
        .expects(teamId, orgId, logger)
        .returning(Failure(new Exception("some error")))

      val result = campaignApiService.getCampaignApiResponse(
        v = "v3",
        campaign = campaignWithStatsAndEmail,
        teamId = teamId,
        orgId = orgId,
      )

      result match {
        case Right(value) => value match {
          case _: CampaignResource.CampaignDataForApiV3 => assert(false)
          case _: CampaignResource.CampaignDataForApiV2 => assert(false)
          case _: CampaignResource.CampaignWithStatsAndEmailInternal => assert(false)
          case _: CampaignResource.CampaignBasicInfoInternal => assert(false)
          case _: CampaignResource.CampaignDataForApiV2WithoutStats => assert(false)
        }
        case Left(error) => error match {
          case CampaignApiError.ErrorWhileFetchingDataForTeamId(_) => assert(true)
        }
      }

    }

    it("gives correct output for v3") {

      (teamService.getTeamUuidFromTeamId(_: TeamId, _: OrgId)(using _: SRLogger))
        .expects(teamId, orgId, logger)
        .returning(Success(TeamUuid(uuid = team_uuid)))

      val result = campaignApiService.getCampaignApiResponse(
        v = "v3",
        campaign = campaignWithStatsAndEmail,
        teamId = teamId,
        orgId = orgId,
      )

      result match {
        case Right(value) => value match {
          case _: CampaignResource.CampaignDataForApiV3 => assert(true)
          case _: CampaignResource.CampaignDataForApiV2 => assert(false)
          case _: CampaignResource.CampaignWithStatsAndEmailInternal => assert(false)
          case _: CampaignResource.CampaignBasicInfoInternal => assert(false)
          case _: CampaignResource.CampaignDataForApiV2WithoutStats => assert(false)
        }
        case Left(_) => assert(false)
      }

    }

    it("gives correct output for v2") {

      val result = campaignApiService.getCampaignApiResponse(
        v = "v2",
        campaign = campaignWithStatsAndEmail,
        teamId = teamId,
        orgId = orgId,
      )

      result match {
        case Right(value) => value match {
          case _: CampaignResource.CampaignDataForApiV3 => assert(false)
          case _: CampaignResource.CampaignDataForApiV2 => assert(true)
          case _: CampaignResource.CampaignWithStatsAndEmailInternal => assert(false)
          case _: CampaignResource.CampaignBasicInfoInternal => assert(false)
          case _: CampaignResource.CampaignDataForApiV2WithoutStats => assert(false)
        }
        case Left(_) => assert(false)
      }

    }

    it("gives correct output for v1") {

      val result = campaignApiService.getCampaignApiResponse(v = "v1", campaign = campaignWithStatsAndEmail, teamId = teamId, orgId = orgId)

      result match {
        case Right(value) => value match {
          case _: CampaignResource.CampaignDataForApiV3 => assert(false)
          case _: CampaignResource.CampaignDataForApiV2 => assert(true)
          case _: CampaignResource.CampaignWithStatsAndEmailInternal => assert(false)
          case _: CampaignResource.CampaignBasicInfoInternal => assert(false)
          case _: CampaignResource.CampaignDataForApiV2WithoutStats => assert(false)
        }
        case Left(_) => assert(false)
      }

    }

  }
  describe("api v3 structure tests") {


    val settings_json = Json.obj(
      "timezone" -> campaignSettings.timezone,
      "daily_from_time" -> campaignSettings.daily_from_time, // time since beginning of day in seconds
      "daily_till_time" -> campaignSettings.daily_till_time, // time since beginning of day in seconds

      // Sunday is the first day
      "days_preference" -> campaignSettings.days_preference,

      // delay between two consecutive in seconds

      //      "consecutive_email_delay" -> campaignSettings.consecutive_email_delay,
      //
      //      "max_emails_per_day" -> campaignSettings.max_emails_per_day,
      //      "max_emails_per_day_from_email_account" -> campaignSettings.campaign_email_settings.headOption.map(_.max_emails_per_day_from_email_account),
      //      "open_tracking_enabled" -> campaignSettings.open_tracking_enabled,
      //      "click_tracking_enabled" -> campaignSettings.click_tracking_enabled,
      //      "ab_testing_enabled" -> campaignSettings.ab_testing_enabled,
      //
      //      // warm up
      //      "warmup_started_at" -> campaignSettings.warmup_started_at,
      //      "warmup_length_in_days" -> campaignSettings.warmup_length_in_days,
      //      "warmup_starting_email_count" -> campaignSettings.warmup_starting_email_count,
      //
      //      // schedule start
      //      "schedule_start_at" -> campaignSettings.schedule_start_at,
      //      "schedule_start_at_tz" -> campaignSettings.schedule_start_at_tz,
      //
      //
      //      "email_priority" -> campaignSettings.email_priority.toString,
      //      "opt_out_msg" -> campaignSettings.opt_out_msg,
      //      "opt_out_is_text" -> campaignSettings.opt_out_is_text,
      //      "add_prospect_to_dnc_on_opt_out" -> campaignSettings.add_prospect_to_dnc_on_opt_out,
      //
      //      "from_email" -> campaignSettings.campaign_email_settings.headOption.map(_.sender_email),
      //      "to_email" -> campaignSettings.campaign_email_settings.headOption.map(_.receiver_email),
      //      "signature" -> campaignSettings.campaign_email_settings.headOption.flatMap(_.signature)
    )

    it("should pass when result matches the data") {

      val data: JsValue = Json.obj(
        "object" -> "campaign",
        "id" -> campaignWithStatsAndEmail.uuid,
        "name" -> campaignWithStatsAndEmail.name,
        "created_at" -> campaignWithStatsAndEmail.created_at,
        "status" -> campaignWithStatsAndEmail.status.toString,
        "team_id" -> team_uuid,
        "settings" -> settings_json)

      (teamService.getTeamUuidFromTeamId(_: TeamId, _: OrgId)(using _: SRLogger))
        .expects(teamId, orgId, logger)
        .returning(Success(TeamUuid(uuid = team_uuid)))
      val result = campaignApiService.getCampaignApiResponse(
        v = "v3",
        campaign = campaignWithStatsAndEmail,
        teamId = teamId,
        orgId = orgId,
      )

      result match {
        case Right(value) => value match {
          case c: CampaignResource.CampaignDataForApiV3 =>
            assert(Json.toJson(c).toString() == data.toString())

          case _: CampaignResource.CampaignDataForApiV2 => assert(false)
          case _: CampaignResource.CampaignWithStatsAndEmailInternal => assert(false)
          case _: CampaignResource.CampaignBasicInfoInternal => assert(false)
          case _: CampaignResource.CampaignDataForApiV2WithoutStats => assert(false)
        }
        case Left(error) => error match {
          case CampaignApiError.ErrorWhileFetchingDataForTeamId(_) => assert(true)
        }

      }
    }

    it("should fail when object is changed") {

      val data: JsValue = Json.obj(
        "object" -> "not campaign",
        "id" -> campaignWithStatsAndEmail.uuid,
        "name" -> campaignWithStatsAndEmail.name,
        "created_at" -> campaignWithStatsAndEmail.created_at,
        "status" -> campaignWithStatsAndEmail.status.toString,
        "team_id" -> team_uuid,
        "settings" -> settings_json)

      (teamService.getTeamUuidFromTeamId(_: TeamId, _: OrgId)(using _: SRLogger))
        .expects(teamId, orgId, logger)
        .returning(Success(TeamUuid(uuid = team_uuid)))
      val result = campaignApiService.getCampaignApiResponse(
        v = "v3",
        campaign = campaignWithStatsAndEmail,
        teamId = teamId,
        orgId = orgId,
      )

      result match {
        case Right(value) => value match {
          case c: CampaignResource.CampaignDataForApiV3 =>
            assert(Json.toJson(c).toString() != data.toString())

          case _: CampaignResource.CampaignDataForApiV2 => assert(false)
          case _: CampaignResource.CampaignWithStatsAndEmailInternal => assert(false)
          case _: CampaignResource.CampaignBasicInfoInternal => assert(false)
          case _: CampaignResource.CampaignDataForApiV2WithoutStats => assert(false)
        }
        case Left(error) => error match {
          case CampaignApiError.ErrorWhileFetchingDataForTeamId(_) => assert(true)
        }

      }
    }

    it("should fail when structure changes") {

      val data: JsValue = Json.obj(
        "object" -> "campaign",
        "id" -> campaignWithStatsAndEmail.uuid,
        "name" -> campaignWithStatsAndEmail.name,
        "created_at" -> campaignWithStatsAndEmail.created_at,
        "status" -> campaignWithStatsAndEmail.status.toString,
        "team_id" -> team_uuid,
        "settings" -> settings_json)

      (teamService.getTeamUuidFromTeamId(_: TeamId, _: OrgId)(using _: SRLogger))
        .expects(teamId, orgId, logger)
        .returning(Success(TeamUuid(uuid = team_uuid)))
      val result = campaignApiService.getCampaignApiResponse(
        v = "v3",
        campaign = campaignWithStatsAndEmail,
        teamId = teamId,
        orgId = orgId,
      )

      result match {
        case Right(value) => value match {
          case c: CampaignResource.CampaignDataForApiV3 =>
            assert(Json.toJson("extra layer" -> c).toString() != data.toString())

          case _: CampaignResource.CampaignDataForApiV2 => assert(false)
          case _: CampaignResource.CampaignWithStatsAndEmailInternal => assert(false)
          case _: CampaignResource.CampaignBasicInfoInternal => assert(false)
          case _: CampaignResource.CampaignDataForApiV2WithoutStats => assert(false)
        }
        case Left(error) => error match {
          case CampaignApiError.ErrorWhileFetchingDataForTeamId(_) => assert(true)
        }

      }
    }

  }

  describe("listing api structure tests") {
    it("gives error when error while fetching team_uuid") {

      (teamService.getTeamUuidFromTeamId(_: TeamId, _: OrgId)(using _: SRLogger))
        .expects(teamId, orgId, logger)
        .returning(Failure(new Exception("some error")))

      val result = campaignApiService.getCampaignListingApiResponse(v = ApiVersion.V3, campaign = campaignBasicInfoSeq, teamId = teamId, orgId = orgId)

      result match {
        case Right(value) => value match {
          case _: CampaignResource.CampaignDataForApiV3 => assert(false)
          case _: CampaignResource.CampaignDataForApiV2 => assert(false)
          case _: CampaignResource.CampaignWithStatsAndEmailInternal => assert(false)
          case _: CampaignResource.CampaignBasicInfoInternal => assert(false)
          case _: CampaignResource.CampaignDataForApiV2WithoutStats => assert(false)
        }
        case Left(error) => error match {
          case CampaignApiError.ErrorWhileFetchingDataForTeamId(_) => assert(true)
        }
      }

    }

    it("gives correct output for v3") {

      (teamService.getTeamUuidFromTeamId(_: TeamId, _: OrgId)(using _: SRLogger))
        .expects(teamId, orgId, logger)
        .returning(Success(TeamUuid(uuid = team_uuid)))

      val result = campaignApiService.getCampaignListingApiResponse(v = ApiVersion.V3, campaign = campaignBasicInfoSeq, teamId = teamId, orgId = orgId)

      result match {
        case Right(value) => value.head match {
          case _: CampaignResource.CampaignDataForApiV3 => assert(true)
          case _: CampaignResource.CampaignDataForApiV2 => assert(false)
          case _: CampaignResource.CampaignWithStatsAndEmailInternal => assert(false)
          case _: CampaignResource.CampaignBasicInfoInternal => assert(false)
          case _: CampaignResource.CampaignDataForApiV2WithoutStats => assert(false)
        }
        case Left(_) => assert(false)
      }

    }

    it("gives correct output for v2") {

      val result = campaignApiService.getCampaignListingApiResponse(
        v = ApiVersion.V2,
        campaign = campaignBasicInfoSeq,
        orgId = orgId,
        teamId = teamId
      )

      result match {
        case Right(value) => value.head match {
          case _: CampaignResource.CampaignDataForApiV3 => assert(false)
          case _: CampaignResource.CampaignDataForApiV2 => assert(false)
          case _: CampaignResource.CampaignWithStatsAndEmailInternal => assert(false)
          case _: CampaignResource.CampaignBasicInfoInternal => assert(false)
          case _: CampaignResource.CampaignDataForApiV2WithoutStats => assert(true)
        }
        case Left(_) => assert(false)
      }

    }

    it("gives correct output for v1") {

      val result = campaignApiService.getCampaignListingApiResponse(
        v = ApiVersion.V1,
        orgId = orgId,
        campaign = campaignBasicInfoSeq,
        teamId = teamId)

      result match {
        case Right(value) => value.head match {
          case _: CampaignResource.CampaignDataForApiV3 => assert(false)
          case _: CampaignResource.CampaignDataForApiV2 => assert(false)
          case _: CampaignResource.CampaignWithStatsAndEmailInternal => assert(false)
          case _: CampaignResource.CampaignBasicInfoInternal => assert(false)
          case _: CampaignResource.CampaignDataForApiV2WithoutStats => assert(true)
        }
        case Left(_) => assert(false)
      }

    }


  }

  describe("prospect assign api tests") {
    val uuid = ProspectUuid("prospect_80_2QmiI33zSRU3AuydC4PusYvmO")

    val ownedProspectsForAssigning = OwnedProspectsForAssigning(
      prospect_id = 3,
      invalid_email = Some(false),
      email_bounced_at = None,
      email_bounced = Some(false),
      prospect_category_id_custom = 11,
      synced_previously_sent_step_for_deleted_prospect_step_id = None
    )
    val data: AssignProspectToCampaignDataV3 = AssignProspectToCampaignDataV3(
      prospect_ids = List(uuid),
      ignore_prospects_in_other_campaigns = None,
    )


    it("should return error list from validateProspectAssignData for limit exceeded") {

      val data: AssignProspectToCampaignDataV3 = AssignProspectToCampaignDataV3(
        prospect_ids = List.fill(101)(uuid),
        ignore_prospects_in_other_campaigns = None,
      )
      //      val ownedProspectsForAssigningList = List.fill(101)(ownedProspectsForAssigning)

      val e = List(ErrorResponseProspectsAssignApi(
        message = "Limit exceeded. Cannot assign more than 100 prospects.",
        error_type = ProspectAssignErrorType.LimitExceeded,
        data = None
      ))

      val res = campaignApiService.assignProspectToCampaignV3(
        data = data,
        teamId = teamId,
        doer = doer,
        account_id = accountId,
        permittedAccountIds = permittedAccountIds,
        campaignId = CampaignId(id = campaign_id),
        campaignName = campaign_name,
        campaignSettings = campaignSettings
      )

      res match {

        case Left(error) => error match {
          case AssignProspectsErrorV3.AssignProspectError(_) => assert(false)
          case AssignProspectsErrorV3.GetIgnoreProspectsInOtherCampaignsError(_) => assert(false)
          case AssignProspectsErrorV3.GetProspectIdFromUuidErrors(_) => assert(false)
          case AssignProspectsErrorV3.FilterOwnedProspectError(_) => assert(false)
          case AssignProspectsErrorV3.AssignProspectsValidationErrors(err) => assert(err == e)
          case AssignProspectsErrorV3.GetProspectUuidFromId(_) => assert(false)
        }

        case Right(_) => assert(false)
      }
    }


    it("should return error list from validateProspectAssignData for empty list") {

      val data: AssignProspectToCampaignDataV3 = AssignProspectToCampaignDataV3(
        prospect_ids = List(),
        ignore_prospects_in_other_campaigns = None,
      )

      val e = List(ErrorResponseProspectsAssignApi(
        message = "Please send a list of prospect ids. Empty request found.",
        error_type = ProspectAssignErrorType.EmptyList,
        data = None
      ))

      val id_data: ValidAndInvalidProspectUuidIdList = ValidAndInvalidProspectUuidIdList(
        invalid_uuids = List(),
        valid_prospect_ids = List()
      )

      val res = campaignApiService.assignProspectToCampaignV3(
        data = data,
        teamId = teamId,
        doer = doer,
        account_id = accountId,
        permittedAccountIds = permittedAccountIds,
        campaignId = CampaignId(id = campaign_id),
        campaignName = campaign_name,
        campaignSettings = campaignSettings
      )

      res match {

        case Left(error) => error match {
          case AssignProspectsErrorV3.AssignProspectError(_) => assert(false)
          case AssignProspectsErrorV3.GetIgnoreProspectsInOtherCampaignsError(_) => assert(false)
          case AssignProspectsErrorV3.GetProspectIdFromUuidErrors(_) => assert(false)
          case AssignProspectsErrorV3.FilterOwnedProspectError(_) => assert(false)
          case AssignProspectsErrorV3.AssignProspectsValidationErrors(err) => assert(err == e)
          case AssignProspectsErrorV3.GetProspectUuidFromId(_) => assert(false)
        }

        case Right(_) => assert(false)
      }
    }

    it("should return error AssignProspectsErrorV3.GetIgnoreProspectsInOtherCampaignsError") {

      val data: AssignProspectToCampaignDataV3 = AssignProspectToCampaignDataV3(
        prospect_ids = List(uuid),
        ignore_prospects_in_other_campaigns = Some("random string"),
      )

      val res = campaignApiService.assignProspectToCampaignV3(
        data = data,
        teamId = teamId,
        doer = doer,
        account_id = accountId,
        permittedAccountIds = permittedAccountIds,
        campaignId = CampaignId(id = campaign_id),
        campaignName = campaign_name,
        campaignSettings = campaignSettings
      )

      res match {

        case Left(error) => error match {
          case AssignProspectsErrorV3.AssignProspectError(_) => assert(false)
          case AssignProspectsErrorV3.GetIgnoreProspectsInOtherCampaignsError(_) => assert(true)
          case AssignProspectsErrorV3.GetProspectIdFromUuidErrors(_) => assert(false)
          case AssignProspectsErrorV3.FilterOwnedProspectError(_) => assert(false)
          case AssignProspectsErrorV3.AssignProspectsValidationErrors(err) => assert(false)
          case AssignProspectsErrorV3.GetProspectUuidFromId(_) => assert(false)
        }

        case Right(_) => assert(false)
      }
    }

    it("should return error AssignProspectsErrorV3.GetProspectIdFromUuidError") {

      val data: AssignProspectToCampaignDataV3 = AssignProspectToCampaignDataV3(
        prospect_ids = List(uuid),
        ignore_prospects_in_other_campaigns = None,
      )

      (prospectService.getProspectIdsFromUuid(_: List[ProspectUuid], _: TeamId)(using _: SRLogger))
        .expects(data.prospect_ids, teamId, logger)
        .returning(Left(GetProspectIdFromUuidError.GetProspectIdError(err = new Exception("some error"))))

      val res = campaignApiService.assignProspectToCampaignV3(
        data = data,
        teamId = teamId,
        doer = doer,
        account_id = accountId,
        permittedAccountIds = permittedAccountIds,
        campaignId = CampaignId(id = campaign_id),
        campaignName = campaign_name,
        campaignSettings = campaignSettings
      )

      res match {

        case Left(error) => error match {
          case AssignProspectsErrorV3.AssignProspectError(_) => assert(false)
          case AssignProspectsErrorV3.GetIgnoreProspectsInOtherCampaignsError(_) => assert(false)
          case AssignProspectsErrorV3.GetProspectIdFromUuidErrors(err) => err match {
            case GetProspectIdFromUuidError.GetProspectIdError(_) => assert(true)
          }
          case AssignProspectsErrorV3.FilterOwnedProspectError(_) => assert(false)
          case AssignProspectsErrorV3.AssignProspectsValidationErrors(err) => assert(false)
          case AssignProspectsErrorV3.GetProspectUuidFromId(_) => assert(false)

        }

        case Right(_) => assert(false)
      }
    }

    it("should return error list invalid prospect_ids") {

      val data: AssignProspectToCampaignDataV3 = AssignProspectToCampaignDataV3(
        prospect_ids = List(uuid),
        ignore_prospects_in_other_campaigns = None,
      )


      val id_data: ValidAndInvalidProspectUuidIdList = ValidAndInvalidProspectUuidIdList(
        invalid_uuids = List(uuid),
        valid_prospect_ids = List()
      )

      val e = List(ErrorResponseProspectsAssignApi(
        message = "Invalid prospect ids passed",
        data = Some(id_data.invalid_uuids.mkString(",")),
        error_type = ProspectAssignErrorType.BadRequest
      ))

      (prospectService.getProspectIdsFromUuid(_: List[ProspectUuid], _: TeamId)(using _: SRLogger))
        .expects(data.prospect_ids, teamId, logger)
        .returning(Right(id_data))

      (prospectServiceV2.filterOwnedProspects)
        .expects(Seq(), permittedAccountIds, teamId.id, orgId, logger)
        .returning(Success(List()))

      val res = campaignApiService.assignProspectToCampaignV3(
        data = data,
        teamId = teamId,
        doer = doer,
        account_id = accountId,
        permittedAccountIds = permittedAccountIds,
        campaignId = CampaignId(id = campaign_id),
        campaignName = campaign_name,
        campaignSettings = campaignSettings
      )

      res match {

        case Left(error) => error match {
          case AssignProspectsErrorV3.AssignProspectError(_) => assert(false)
          case AssignProspectsErrorV3.GetIgnoreProspectsInOtherCampaignsError(_) => assert(false)
          case AssignProspectsErrorV3.GetProspectIdFromUuidErrors(err) => assert(false)
          case AssignProspectsErrorV3.FilterOwnedProspectError(_) => assert(false)
          case AssignProspectsErrorV3.AssignProspectsValidationErrors(err) => assert(err == e)
          case AssignProspectsErrorV3.GetProspectUuidFromId(_) => assert(false)

        }

        case Right(_) => assert(false)
      }
    }

    it("should return error list forbidden error with uuids") {

      val data: AssignProspectToCampaignDataV3 = AssignProspectToCampaignDataV3(
        prospect_ids = List.fill(10)(uuid),
        ignore_prospects_in_other_campaigns = None,
      )

      val prospect_uuid_list = List.fill(5)(ProspectUuid("uuid"))
      val prospect_id_not_owned_list = List.fill(5)(ProspectId(5L))
      val prospect_ids = Seq.fill(10)(3L) ++ Seq.fill(5)(5L)


      val id_data: ValidAndInvalidProspectUuidIdList = ValidAndInvalidProspectUuidIdList(
        invalid_uuids = List(),
        valid_prospect_ids = List.fill(10)(ProspectId(3L)) ++ List.fill(5)(ProspectId(5L))
      )

      val e = List(ErrorResponseProspectsAssignApi(
        message = "Given prospects do not exist in your account",
        data = Some(prospect_uuid_list.mkString(",")),
        error_type = ProspectAssignErrorType.Forbidden
      ))

      (prospectService.getProspectIdsFromUuid(_: List[ProspectUuid], _: TeamId)(using _: SRLogger))
        .expects(data.prospect_ids, teamId, logger)
        .returning(Right(id_data))

      (prospectServiceV2.filterOwnedProspects)
        .expects(prospect_ids, permittedAccountIds, teamId.id, orgId, logger)
        .returning(Success(List.fill(5)(ownedProspectsForAssigning)))

      (prospectService.getProspectUuidFromId(_: List[ProspectId], _: TeamId)(using _: SRLogger))
        .expects(prospect_id_not_owned_list, teamId, logger)
        .returning(Success(prospect_uuid_list))

      val res = campaignApiService.assignProspectToCampaignV3(
        data = data,
        teamId = teamId,
        doer = doer,
        account_id = accountId,
        permittedAccountIds = permittedAccountIds,
        campaignId = CampaignId(id = campaign_id),
        campaignName = campaign_name,
        campaignSettings = campaignSettings
      )

      res match {

        case Left(error) => error match {
          case AssignProspectsErrorV3.AssignProspectError(_) => assert(false)
          case AssignProspectsErrorV3.GetIgnoreProspectsInOtherCampaignsError(_) => assert(false)
          case AssignProspectsErrorV3.GetProspectIdFromUuidErrors(err) => assert(false)
          case AssignProspectsErrorV3.FilterOwnedProspectError(_) => assert(true)
          case AssignProspectsErrorV3.AssignProspectsValidationErrors(err) => assert(err == e)
          case AssignProspectsErrorV3.GetProspectUuidFromId(_) => assert(false)
        }

        case Right(_) => assert(false)
      }
    }

    it("should return error list invalid uuids and forbidden error with uuids") {

      val data: AssignProspectToCampaignDataV3 = AssignProspectToCampaignDataV3(
        prospect_ids = List.fill(10)(uuid) ++ List.fill(5)(ProspectUuid("abc uuid")),
        ignore_prospects_in_other_campaigns = None)

      val prospect_uuid_list = List.fill(5)(ProspectUuid("uuid"))
      val prospect_id_not_owned_list = List.fill(5)(ProspectId(5L))
      val prospect_ids = Seq.fill(10)(3L) ++ Seq.fill(5)(5L)


      val id_data: ValidAndInvalidProspectUuidIdList = ValidAndInvalidProspectUuidIdList(
        invalid_uuids = List.fill(5)(ProspectUuid("abc uuid")),
        valid_prospect_ids = List.fill(10)(ProspectId(3L)) ++ List.fill(5)(ProspectId(5L))
      )

      val e = List(
        ErrorResponseProspectsAssignApi(
          message = "Invalid prospect ids passed",
          data = Some(id_data.invalid_uuids.mkString(",")),
          error_type = ProspectAssignErrorType.BadRequest
        ),
        ErrorResponseProspectsAssignApi(
          message = "Given prospects do not exist in your account",
          data = Some(prospect_uuid_list.mkString(",")),
          error_type = ProspectAssignErrorType.Forbidden
        )
      )

      (prospectService.getProspectIdsFromUuid(_: List[ProspectUuid], _: TeamId)(using _: SRLogger))
        .expects(data.prospect_ids, teamId, logger)
        .returning(Right(id_data))

      (prospectServiceV2.filterOwnedProspects)
        .expects(prospect_ids, permittedAccountIds, teamId.id, orgId, logger)
        .returning(Success(List.fill(5)(ownedProspectsForAssigning)))

      (prospectService.getProspectUuidFromId(_: List[ProspectId], _: TeamId)(using _: SRLogger))
        .expects(prospect_id_not_owned_list, teamId, logger)
        .returning(Success(prospect_uuid_list))

      val res = campaignApiService.assignProspectToCampaignV3(
        data = data,
        teamId = teamId,
        doer = doer,
        account_id = accountId,
        permittedAccountIds = permittedAccountIds,
        campaignId = CampaignId(id = campaign_id),
        campaignName = campaign_name,
        campaignSettings = campaignSettings
      )

      res match {

        case Left(error) => error match {
          case AssignProspectsErrorV3.AssignProspectError(_) => assert(false)
          case AssignProspectsErrorV3.GetIgnoreProspectsInOtherCampaignsError(_) => assert(false)
          case AssignProspectsErrorV3.GetProspectIdFromUuidErrors(err) => assert(false)
          case AssignProspectsErrorV3.FilterOwnedProspectError(_) => assert(true)
          case AssignProspectsErrorV3.AssignProspectsValidationErrors(err) => assert(err == e)
          case AssignProspectsErrorV3.GetProspectUuidFromId(_) => assert(false)
        }

        case Right(_) => assert(false)
      }
    }


    it("should return error AssignProspectsErrorV3.GetProspectUuidFromId") {

      val data: AssignProspectToCampaignDataV3 = AssignProspectToCampaignDataV3(
        prospect_ids = List.fill(10)(uuid) ++ List.fill(5)(ProspectUuid("uuid")),
        ignore_prospects_in_other_campaigns = None)

      val prospect_id_not_owned_list = List.fill(5)(ProspectId(5L))
      val prospect_ids = Seq.fill(10)(3L) ++ Seq.fill(5)(5L)


      val id_data: ValidAndInvalidProspectUuidIdList = ValidAndInvalidProspectUuidIdList(
        invalid_uuids = List(),
        valid_prospect_ids = List.fill(10)(ProspectId(3L)) ++ List.fill(5)(ProspectId(5L))
      )

      (prospectService.getProspectIdsFromUuid(_: List[ProspectUuid], _: TeamId)(using _: SRLogger))
        .expects(data.prospect_ids, teamId, logger)
        .returning(Right(id_data))

      (prospectServiceV2.filterOwnedProspects)
        .expects(prospect_ids, permittedAccountIds, teamId.id, orgId, logger)
        .returning(Success(List.fill(5)(ownedProspectsForAssigning)))

      (prospectService.getProspectUuidFromId(_: List[ProspectId], _: TeamId)(using _: SRLogger))
        .expects(prospect_id_not_owned_list, teamId, logger)
        .returning(Failure(new Exception("some exception")))

      val res = campaignApiService.assignProspectToCampaignV3(
        data = data,
        teamId = teamId,
        doer = doer,
        account_id = accountId,
        permittedAccountIds = permittedAccountIds,
        campaignId = CampaignId(id = campaign_id),
        campaignName = campaign_name,
        campaignSettings = campaignSettings
      )

      res match {

        case Left(error) => error match {
          case AssignProspectsErrorV3.AssignProspectError(_) => assert(false)
          case AssignProspectsErrorV3.GetIgnoreProspectsInOtherCampaignsError(_) => assert(false)
          case AssignProspectsErrorV3.GetProspectIdFromUuidErrors(err) => assert(false)
          case AssignProspectsErrorV3.FilterOwnedProspectError(_) => assert(true)
          case AssignProspectsErrorV3.AssignProspectsValidationErrors(err) => assert(false)
          case AssignProspectsErrorV3.GetProspectUuidFromId(_) => assert(true)
        }

        case Right(_) => assert(false)
      }
    }

    it("should fail AssignProspectsErrorV3.AssignProspectError") {

      val data: AssignProspectToCampaignDataV3 = AssignProspectToCampaignDataV3(
        prospect_ids = List.fill(10)(uuid),
        ignore_prospects_in_other_campaigns = None)

      val prospect_ids = Seq.fill(10)(3L)
      val ownedProspectsForAssigningList = List.fill(10)(ownedProspectsForAssigning)


      val id_data: ValidAndInvalidProspectUuidIdList = ValidAndInvalidProspectUuidIdList(
        invalid_uuids = List(),
        valid_prospect_ids = List.fill(10)(ProspectId(3L))
      )

      val assignProspectsResponse = AssignProspectsResponseV3(
        assignedProspectIdsLength = 10,
        totalAssignedProspectIds = prospect_ids.toList,
        campaignId = campaign_id.toInt
      )

      (prospectService.getProspectIdsFromUuid(_: List[ProspectUuid], _: TeamId)(using _: SRLogger))
        .expects(data.prospect_ids, teamId, logger)
        .returning(Right(id_data))

      (prospectServiceV2.filterOwnedProspects)
        .expects(prospect_ids, permittedAccountIds, teamId.id, orgId, logger)
        .returning(Success(List.fill(10)(ownedProspectsForAssigning)))

      (campaignProspectAssign.assignProspectsV3)
        .expects(doer, accountId.id, teamId.id, IgnoreProspectsInOtherCampaigns.DoNotIgnore,
          permittedAccountIds, campaign_id.toInt, campaign_name, campaignSettings,
          prospect_ids.toList, ownedProspectsForAssigningList,
          logger)
        .returning(Left(AssignProspectsError.ErrorWhileAssigningProspect("some error")))

      val res = campaignApiService.assignProspectToCampaignV3(
        data = data,
        teamId = teamId,
        doer = doer,
        account_id = accountId,
        permittedAccountIds = permittedAccountIds,
        campaignId = CampaignId(id = campaign_id),
        campaignName = campaign_name,
        campaignSettings = campaignSettings
      )

      res match {

        case Left(error) => error match {
          case AssignProspectsErrorV3.AssignProspectError(_) => assert(true)
          case AssignProspectsErrorV3.GetIgnoreProspectsInOtherCampaignsError(_) => assert(false)
          case AssignProspectsErrorV3.GetProspectIdFromUuidErrors(err) => assert(false)
          case AssignProspectsErrorV3.FilterOwnedProspectError(_) => assert(false)
          case AssignProspectsErrorV3.AssignProspectsValidationErrors(err) => assert(false)
          case AssignProspectsErrorV3.GetProspectUuidFromId(_) => assert(false)

        }

        case Right(_) => assert(false)
      }
    }

    it("should pass") {

      val data: AssignProspectToCampaignDataV3 = AssignProspectToCampaignDataV3(
        prospect_ids = List.fill(10)(uuid),
        ignore_prospects_in_other_campaigns = None)

      val prospect_ids = Seq.fill(10)(3L)
      val ownedProspectsForAssigningList = List.fill(10)(ownedProspectsForAssigning)


      val id_data: ValidAndInvalidProspectUuidIdList = ValidAndInvalidProspectUuidIdList(
        invalid_uuids = List(),
        valid_prospect_ids = List.fill(10)(ProspectId(3L))
      )

      val assignProspectsResponse = AssignProspectsResponseV3(
        assignedProspectIdsLength = 10,
        totalAssignedProspectIds = prospect_ids.toList,
        campaignId = campaign_id.toInt
      )

      (prospectService.getProspectIdsFromUuid(_: List[ProspectUuid], _: TeamId)(using _: SRLogger))
        .expects(data.prospect_ids, teamId, logger)
        .returning(Right(id_data))

      (prospectServiceV2.filterOwnedProspects)
        .expects(prospect_ids, permittedAccountIds, teamId.id, orgId, logger)
        .returning(Success(List.fill(10)(ownedProspectsForAssigning)))

      (campaignProspectAssign.assignProspectsV3)
        .expects(doer, accountId.id, teamId.id, IgnoreProspectsInOtherCampaigns.DoNotIgnore,
          permittedAccountIds, campaign_id.toInt, campaign_name, campaignSettings,
          prospect_ids.toList, ownedProspectsForAssigningList,
          logger)
        .returning(Right(assignProspectsResponse))

      val res = campaignApiService.assignProspectToCampaignV3(
        data = data,
        teamId = teamId,
        doer = doer,
        account_id = accountId,
        permittedAccountIds = permittedAccountIds,
        campaignId = CampaignId(id = campaign_id),
        campaignName = campaign_name,
        campaignSettings = campaignSettings
      )

      res match {

        case Left(error) => error match {
          case AssignProspectsErrorV3.AssignProspectError(_) => assert(false)
          case AssignProspectsErrorV3.GetIgnoreProspectsInOtherCampaignsError(_) => assert(false)
          case AssignProspectsErrorV3.GetProspectIdFromUuidErrors(err) => assert(false)
          case AssignProspectsErrorV3.FilterOwnedProspectError(_) => assert(false)
          case AssignProspectsErrorV3.AssignProspectsValidationErrors(err) => assert(false)
          case AssignProspectsErrorV3.GetProspectUuidFromId(_) => assert(false)
        }

        case Right(_) => assert(true)
      }
    }


  }

}
