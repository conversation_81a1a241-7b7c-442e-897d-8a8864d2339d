package app.api.campaigns.services

import api.accounts.{ReplyHandling, TeamId}
import api.emails.{EmailScheduled, EmailScheduledDAO, EmailScheduledNewStep2}
import db_test_spec.api.{DbTestingBeforeAllAndAfterAll, InitialData}
import db_test_spec.api.accounts.fixtures.{EmailScheduledNewFixture, NewAccountAndEmailSettingData}
import db_test_spec.api.campaigns.test_utils.{CampaignUtils, CreateAndStartCampaignData}
import eventframework.ProspectObject
import io.smartreach.esp.api.emails.EmailSettingId
import org.joda.time.DateTime
import org.scalamock.scalatest.AsyncMockFactory
import org.scalatest.funspec.AsyncFunSpec
import utils.SRLogger
import utils.email.models.DeleteEmailsScheduledType

import scala.concurrent.Future
import scala.util.{Failure, Success, Try}

class EmailScheduledDAOServiceSpec extends DbTestingBeforeAllAndAfterAll {

  describe("EmailScheduledDAO.selectEmailScheduledToDeleteQueryDependingOnWhereClause 2") {

    val teamId = TeamId(id = 10L)
    val emailSettingId = EmailSettingId(
      emailSettingId = 10L
    )
    val Logger = new SRLogger("EmailScheduledDAOServiceSpec")

    it("should generate query for delete all unsent by email setting id") {


      val expected =
        """
          |
          |SELECT
          |  es.id as es_scheduled_id,
          |  es.step_id as es_step_id,
          |  es.campaign_id as es_campaign_id,
          |  es.inbox_email_setting_id as es_inbox_setting_id,
          |  es.prospect_id as es_prospect_id
          |FROM emails_scheduled es
          |WHERE
          |  es.inbox_email_setting_id in (?)
          |  AND es.team_id = ?
          |  AND es.sent = false
          |  AND es.scheduled_from_campaign
          |""".stripMargin

      val qry = EmailScheduledDAO.selectEmailScheduledIdAndTaskIdToPublishForDelete(
        deleteEmailsScheduledType = DeleteEmailsScheduledType.DeleteAllUnSentByEmailSettingId(
          emailSettingData = Map(
            teamId -> Seq(emailSettingId)
          )
        ),
        task_deletion_required = false
      )


      val qry_str = qry.statement
      val split = expected.split(s"\\s+")
      val rhs = split.reduce((a1, a2) => {
        a1 + " " + a2
      })
      val lhs = qry_str.split("\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })

      assert(lhs == rhs)

    }


    it("should generate query for delete all unsent by email scheduled id") {


      val expected =
        """
          |
          |SELECT
          |  es.id as es_scheduled_id,
          |  es.step_id as es_step_id,
          |  es.campaign_id as es_campaign_id,
          |  es.inbox_email_setting_id as es_inbox_setting_id,
          |  es.prospect_id as es_prospect_id
          |FROM emails_scheduled es
          |WHERE
          |  es.id = ?
          |  AND es.team_id = ?
          |  AND es.sent = false
          |  AND es.scheduled_from_campaign
          |""".stripMargin

      val qry = EmailScheduledDAO.selectEmailScheduledIdAndTaskIdToPublishForDelete(
        deleteEmailsScheduledType = DeleteEmailsScheduledType.DeleteUnsentByEmailScheduledId(
          emailScheduledId = 10L,
          teamId = teamId,
          senderEmailSettingId = Some(10L)
        ),
        task_deletion_required = false
      )


      val qry_str = qry.statement
      val split = expected.split(s"\\s+")
      val rhs = split.reduce((a1, a2) => {
        a1 + " " + a2
      })
      val lhs = qry_str.split("\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })

      assert(lhs == rhs)

    }


    it("should generate query for delete all unsent by prospect id PAUSE_SPECIFIC_CAMPAIGN_ON_REPLY") {


      val expected =
        """
          |
          |       WITH selected_emails AS (
          |          SELECT
          |              es.id AS es_id,
          |              es.step_id AS es_step_id,
          |              es.campaign_id AS es_campaign_id,
          |              es.sent AS es_sent,
          |              es.prospect_id as es_prospect_id,
          |              es.inbox_email_setting_id as es_inbox_setting_id
          |          FROM
          |              emails_scheduled es
          |          WHERE
          |          es.campaign_id = ?
          |          AND es.prospect_id IN (?)
          |          AND es.team_id = ?
          |          AND es.sent = false
          |          AND es.scheduled_from_campaign
          |      ),
          |      selected_tasks AS (
          |          SELECT
          |              t.task_id AS t_task_id,
          |              t.step_id AS t_step_id,
          |              t.campaign_id AS t_campaign_id,
          |              t.status AS t_status,
          |              t.prospect_id  as t_prospect_id
          |          FROM
          |              tasks t
          |          where
          |          t.campaign_id = ?
          |          AND t.prospect_id IN (?)
          |          AND t.team_id = ?
          |          AND t.status = ?
          |          AND t.created_via = ?
          |      )
          |
          |
          |      SELECT
          |          se.es_id as es_scheduled_id,
          |          se.es_step_id as es_step_id,
          |          se.es_campaign_id as es_campaign_id,
          |          se.es_inbox_setting_id as es_inbox_setting_id,
          |          se.es_prospect_id as es_prospect_id,
          |          st.t_task_id as t_task_id,
          |          st.t_step_id as t_step_id,
          |          st.t_campaign_id as t_campaign_id,
          |          st.t_status as t_status,
          |          st.t_prospect_id as t_prospect_id
          |      FROM
          |          selected_emails se
          |      FULL JOIN
          |          selected_tasks st
          |      ON
          |          st.t_step_id = se.es_step_id
          |          AND st.t_campaign_id = se.es_campaign_id
          |          AND st.t_prospect_id = se.es_prospect_id
          |""".stripMargin

      val qry = EmailScheduledDAO.selectEmailScheduledIdAndTaskIdToPublishForDelete(
        deleteEmailsScheduledType = DeleteEmailsScheduledType.DeleteUnsentByProspectId(
          campaignId = 11L,
          prospectIds = Seq(11L),
          prospectAccountIds = None,
          replyHandling = ReplyHandling.PAUSE_SPECIFIC_CAMPAIGN_ON_REPLY,
          teamId = teamId
        ),
        task_deletion_required = true
      )


      val qry_str = qry.statement
      val split = expected.split(s"\\s+")
      val rhs = split.reduce((a1, a2) => {
        a1 + " " + a2
      })
      val lhs = qry_str.split("\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })

      assert(lhs == rhs)

    }

    it("should generate query for delete all unsent by prospect id PAUSE_ALL_PROSPECT_CAMPAIGNS_ON_REPLY") {


      val expected =
        """
          |
          |       WITH selected_emails AS (
          |          SELECT
          |              es.id AS es_id,
          |              es.step_id AS es_step_id,
          |              es.campaign_id AS es_campaign_id,
          |              es.sent AS es_sent,
          |              es.prospect_id as es_prospect_id,
          |              es.inbox_email_setting_id as es_inbox_setting_id
          |          FROM
          |              emails_scheduled es
          |          WHERE
          |          es.prospect_id IN (?)
          |          AND es.team_id = ?
          |          AND es.sent = false
          |          AND es.scheduled_from_campaign
          |      ),
          |      selected_tasks AS (
          |          SELECT
          |              t.task_id AS t_task_id,
          |              t.step_id AS t_step_id,
          |              t.campaign_id AS t_campaign_id,
          |              t.status AS t_status,
          |              t.prospect_id  as t_prospect_id
          |          FROM
          |              tasks t
          |          where
          |          t.prospect_id IN (?)
          |          AND t.team_id = ?
          |          AND t.status = ?
          |          AND t.created_via = ?
          |      )
          |
          |
          |      SELECT
          |          se.es_id as es_scheduled_id,
          |          se.es_step_id as es_step_id,
          |          se.es_campaign_id as es_campaign_id,
          |          se.es_inbox_setting_id as es_inbox_setting_id,
          |          se.es_prospect_id as es_prospect_id,
          |          st.t_task_id as t_task_id,
          |          st.t_step_id as t_step_id,
          |          st.t_campaign_id as t_campaign_id,
          |          st.t_status as t_status,
          |          st.t_prospect_id as t_prospect_id
          |      FROM
          |          selected_emails se
          |      FULL JOIN
          |          selected_tasks st
          |      ON
          |          st.t_step_id = se.es_step_id
          |          AND st.t_campaign_id = se.es_campaign_id
          |          AND st.t_prospect_id = se.es_prospect_id
          |""".stripMargin

      val qry = EmailScheduledDAO.selectEmailScheduledIdAndTaskIdToPublishForDelete(
        deleteEmailsScheduledType = DeleteEmailsScheduledType.DeleteUnsentByProspectId(
          campaignId = 11L,
          prospectIds = Seq(11L),
          prospectAccountIds = None,
          replyHandling = ReplyHandling.PAUSE_ALL_PROSPECT_CAMPAIGNS_ON_REPLY,
          teamId = teamId,
        ),
        task_deletion_required = true
      )


      val qry_str = qry.statement
      val split = expected.split(s"\\s+")
      val rhs = split.reduce((a1, a2) => {
        a1 + " " + a2
      })
      val lhs = qry_str.split("\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })

      assert(lhs == rhs)

    }


    it("should generate query for delete all unsent by prospect id PAUSE_ALL_PROSPECT_ACCOUNT_CAMPAIGNS_ON_REPLY And Prospect_Account_id is defined") {


      val expected =
        """
          |
          |       WITH selected_emails AS (
          |          SELECT
          |              es.id AS es_id,
          |              es.step_id AS es_step_id,
          |              es.campaign_id AS es_campaign_id,
          |              es.sent AS es_sent,
          |              es.prospect_id as es_prospect_id,
          |              es.inbox_email_setting_id as es_inbox_setting_id
          |          FROM
          |              emails_scheduled es
          |          WHERE
          |          ( es.prospect_account_id IN (?) OR es.prospect_id IN (?) )
          |          AND es.team_id = ?
          |          AND es.sent = false
          |          AND es.scheduled_from_campaign
          |      ),
          |      selected_tasks AS (
          |          SELECT
          |              t.task_id AS t_task_id,
          |              t.step_id AS t_step_id,
          |              t.campaign_id AS t_campaign_id,
          |              t.status AS t_status,
          |              t.prospect_id  as t_prospect_id
          |          FROM
          |              tasks t
          |              join prospects p on ( p.id = t.prospect_id and t.team_id = p.team_id )
          |          where
          |          ( p.prospect_account_id IN (?) OR t.prospect_id IN (?) )
          |          AND t.team_id = ?
          |          AND t.status = ?
          |          AND t.created_via = ?
          |      )
          |
          |
          |      SELECT
          |          se.es_id as es_scheduled_id,
          |          se.es_step_id as es_step_id,
          |          se.es_campaign_id as es_campaign_id,
          |          se.es_inbox_setting_id as es_inbox_setting_id,
          |          se.es_prospect_id as es_prospect_id,
          |          st.t_task_id as t_task_id,
          |          st.t_step_id as t_step_id,
          |          st.t_campaign_id as t_campaign_id,
          |          st.t_status as t_status,
          |          st.t_prospect_id as t_prospect_id
          |      FROM
          |          selected_emails se
          |      FULL JOIN
          |          selected_tasks st
          |      ON
          |          st.t_step_id = se.es_step_id
          |          AND st.t_campaign_id = se.es_campaign_id
          |          AND st.t_prospect_id = se.es_prospect_id
          |""".stripMargin

      val qry = EmailScheduledDAO.selectEmailScheduledIdAndTaskIdToPublishForDelete(
        deleteEmailsScheduledType = DeleteEmailsScheduledType.DeleteUnsentByProspectId(
          campaignId = 11L,
          prospectIds = Seq(11L),
          prospectAccountIds = Some(Seq(11L)),
          replyHandling = ReplyHandling.PAUSE_ALL_PROSPECT_ACCOUNT_CAMPAIGNS_ON_REPLY,
          teamId = teamId,
        ),
        task_deletion_required = true
      )


      val qry_str = qry.statement
      val split = expected.split(s"\\s+")
      val rhs = split.reduce((a1, a2) => {
        a1 + " " + a2
      })
      val lhs = qry_str.split("\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })

      assert(lhs == rhs)

    }

    it("should generate query for delete all unsent by campaign id") {


      val expected =
        """
          |
          |       WITH selected_emails AS (
          |          SELECT
          |              es.id AS es_id,
          |              es.step_id AS es_step_id,
          |              es.campaign_id AS es_campaign_id,
          |              es.sent AS es_sent,
          |              es.prospect_id as es_prospect_id,
          |              es.inbox_email_setting_id as es_inbox_setting_id
          |          FROM
          |              emails_scheduled es
          |          WHERE
          |          es.campaign_id = ?
          |          AND es.team_id = ?
          |          AND es.sent = false
          |          AND es.scheduled_from_campaign
          |      ),
          |      selected_tasks AS (
          |          SELECT
          |              t.task_id AS t_task_id,
          |              t.step_id AS t_step_id,
          |              t.campaign_id AS t_campaign_id,
          |              t.status AS t_status,
          |              t.prospect_id  as t_prospect_id
          |          FROM
          |              tasks t
          |          where
          |          t.campaign_id = ?
          |          AND t.team_id = ?
          |          AND t.status = ?
          |          AND t.created_via = ?
          |      )
          |
          |
          |      SELECT
          |          se.es_id as es_scheduled_id,
          |          se.es_step_id as es_step_id,
          |          se.es_campaign_id as es_campaign_id,
          |          se.es_inbox_setting_id as es_inbox_setting_id,
          |          se.es_prospect_id as es_prospect_id,
          |          st.t_task_id as t_task_id,
          |          st.t_step_id as t_step_id,
          |          st.t_campaign_id as t_campaign_id,
          |          st.t_status as t_status,
          |          st.t_prospect_id as t_prospect_id
          |      FROM
          |          selected_emails se
          |      FULL JOIN
          |          selected_tasks st
          |      ON
          |          st.t_step_id = se.es_step_id
          |          AND st.t_campaign_id = se.es_campaign_id
          |          AND st.t_prospect_id = se.es_prospect_id
          |""".stripMargin

      val qry = EmailScheduledDAO.selectEmailScheduledIdAndTaskIdToPublishForDelete(
        deleteEmailsScheduledType = DeleteEmailsScheduledType.DeleteUnsentByCampaignId(
          campaignId = 11L,
          teamId = teamId,
          senderEmailSettingIds = Seq(EmailSettingId(emailSettingId = 11)),
        ),
        task_deletion_required = true
      )


      val qry_str = qry.statement
      val split = expected.split(s"\\s+")
      val rhs = split.reduce((a1, a2) => {
        a1 + " " + a2
      })
      val lhs = qry_str.split("\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })

      assert(lhs == rhs)

    }


    it("should generate query for delete all unsent by DeleteUnSentByEmailSettingIdNotManual") {


      val expected =
        """
          |
          |SELECT
          |  es.id as es_scheduled_id,
          |  es.step_id as es_step_id,
          |  es.campaign_id as es_campaign_id,
          |  es.inbox_email_setting_id as es_inbox_setting_id,
          |  es.prospect_id as es_prospect_id
          |FROM emails_scheduled es
          |WHERE
          |  es.inbox_email_setting_id in (?)
          |  AND es.team_id = ?
          |  AND es.is_manual_task = false
          |  AND es.sent = false
          |  AND es.scheduled_from_campaign
          |""".stripMargin

      val qry = EmailScheduledDAO.selectEmailScheduledIdAndTaskIdToPublishForDelete(
        deleteEmailsScheduledType = DeleteEmailsScheduledType.DeleteUnSentByEmailSettingIdNotManual(
          emailSettingData = Map(
            teamId -> Seq(emailSettingId)
          ),
        ),
        task_deletion_required = false
      )


      val qry_str = qry.statement
      val split = expected.split(s"\\s+")
      val rhs = split.reduce((a1, a2) => {
        a1 + " " + a2
      })
      val lhs = qry_str.split("\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })

      assert(lhs == rhs)

    }
  }

  describe("addBodyToEmailsToBeScheduled") {
    it("should pass") {
      implicit val Logger = new SRLogger("isClicked")
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val result: Future[Seq[Long]] = for {

        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 4
        )
        prospects: Seq[ProspectObject] <- Future {
          initialData.prospectsResult
        }

        (duplicate: Long, master: Long) <- Future {
          (prospects.head.id, prospects.last.id)
        }

        addingEmailScheduled <- Future.fromTry {
          emailScheduledDAOService.saveEmailsToBeScheduledAndUpdateCampaignDataV2(
            emailsToBeScheduled = Vector(EmailScheduledNewFixture.generateEmailScheduledNew.copy(
              campaign_id = Some(createAndStartCampaignData.createCampaign.id),
              step_id = createAndStartCampaignData.createCampaign.head_step_id,
              from_email = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.sender_email,
              scheduled_from_campaign = true,
              is_opening_step = true,
              sender_email_settings_id = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.sender_email_setting_id.emailSettingId,
              team_id = createAndStartCampaignData.createCampaign.team_id,
              account_id = createAndStartCampaignData.createCampaign.owner_id,
              receiver_email_settings_id = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.receiver_email_setting_id.emailSettingId,
              campaign_email_settings_id = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.id,
              prospect_id = Some(duplicate),
              base_body = Some("body")

            )
            ),
            campaign_email_setting_id = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.id,
            emailSendingFlow = None,
            Logger = Logger
          )
        }
        result: Seq[Long] <- Future.fromTry{
          emailScheduledDAOService.addBodyToEmailsToBeScheduled(
            emails = Seq(
            EmailScheduledNewStep2(
              email_scheduled_id = addingEmailScheduled.head.email_scheduled_id,
              subject = "new Subject",
              body = "new Body",
              base_body = "new Body",
              text_body = "new Body",

              has_open_tracking = false,
              has_click_tracking = false,
              has_unsubscribe_link = false,
              is_edited_preview_email = false,

              list_unsubscribe_header = None,
              gmail_fbl = None
            )
          ),
            teamId = addingEmailScheduled.head.team_id,
            Logger = Logger
          )
        }
      } yield result

      result.map(res => {
        assert(res.nonEmpty)
      }).recover(e =>
        assert(false))
    }
  }

}
