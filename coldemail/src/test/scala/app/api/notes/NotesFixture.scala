package app.api.notes

import api.accounts.TeamId
import api.accounts.models.AccountId
import api.notes.models.CreateNotesForm
import api.prospects.models.ProspectId
import api.tasks.services.TaskUuid
import db_test_spec.utils.SrRandomTestUtils

object NotesFixture {

  def getCreateNotesForm(
                          note: String = SrRandomTestUtils.getRandomStringOfLengthN(10),
                          addedBy: AccountId = AccountId(1L),
                          teamId: TeamId = TeamId(1L),
                          prospectId: Option[ProspectId] = None,
                          taskUuid: Option[TaskUuid] = None
                        ): CreateNotesForm = {

    CreateNotesForm(
      note = note,
      added_by = addedBy,
      team_id = teamId,
      prospect_id = prospectId,
      task_uuid = taskUuid
    )

  }

}
