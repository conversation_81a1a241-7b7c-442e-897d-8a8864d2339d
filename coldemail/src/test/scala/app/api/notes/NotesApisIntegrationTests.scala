package app.api.notes

import org.apache.pekko.actor.ActorSystem
import api.accounts.{PermissionMethods, TeamId}
import api.accounts.models.{AccountId, OrgId}
import api.accounts.sr_api_key_type.models.SRApiKeyType
import api.notes.models.CreateNotesForm
import api.prospects.models.ProspectId
import db_test_spec.api.{DbTestingBeforeAllAndAfterAll, InitialData}
import db_test_spec.api.accounts.fixtures.{AccountFixtureForIntegrationTest, NewAccountAndEmailSettingData}
import db_test_spec.api.prospects.fixtures.ProspectFixtureForIntegrationTest
import org.scalatest.funspec.AsyncFunSpec
import play.api.libs.json.{JsValue, Json}

import scala.concurrent.duration.{Duration, SECONDS}
import scala.concurrent.{Await, ExecutionContext}
import play.api.test.FakeRequest
import play.api.test.Helpers.*
import utils.SRLogger

class NotesApisIntegrationTests extends DbTestingBeforeAllAndAfterAll {

  given logger: SRLogger = new SRLogger("NotesApisIntegrationTests ")
  lazy val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get


  describe("Notes APIs Integration tests") {

    val sendingUrl = s"/api/v2/notes"

    describe("tests for note create API") {

      it("should create and return with note object") {

        val apiKey = initialData.teamUserLevelKey
        val sampleNote = "have to watch euro semifinal today."
        val teamId = TeamId(initialData.head_team_id)
        val accountId = AccountId(initialData.account.internal_id)

        val prospect = initialData.prospectsResult.head

        val createNotesForm = CreateNotesForm(
          note = sampleNote,
          added_by = AccountId(initialData.account.internal_id),
          team_id = teamId,
          prospect_id = Some(ProspectId(prospect.id)),
          task_uuid = None
        )

        val request = FakeRequest(play.api.test.Helpers.POST, sendingUrl)
          .withHeaders("X-API-KEY" -> apiKey,
            "Content-Type" -> "application/json")
          .withJsonBody(Json.toJson(
            createNotesForm
          ))

        val final_result = play.api.test.Helpers.route(testApi, request).get

        val status: Int = play.api.test.Helpers.status(final_result)
        val json: JsValue = play.api.test.Helpers.contentAsJson(final_result)

        final_result.flatMap( res => {

            if(status == 200) {

              val data = json \ "data"
              val objectType = (data \ "object").as[String]
              val note = (data \ "note").as[String]
              val added_by = (data \ "added_by").as[Long]
              val team_id: Long = (data \ "team_id").as[Long]
              val prospect_id = (data \ "prospect_id").asOpt[Long]
              val task_uuid = (data \ "task_uuid").asOpt[Long]

              assert(objectType == "note")
              assert(note == sampleNote)
              assert(added_by == initialData.account.internal_id)
              assert(team_id == initialData.head_team_id)
              assert(prospect_id.nonEmpty)
              assert(prospect_id.get == prospect.id)
              assert(task_uuid.isEmpty)

            } else assert(false)

          })

        Await.result(
          final_result,
          Duration.create(15, SECONDS)
        )
        assert(final_result.isCompleted)

      }

      it("should not create and return error as user does not have permission") {

        val sampleNote = "have to watch euro semifinal today."
        val teamId = TeamId(initialData.head_team_id)
        val accountId = AccountId(initialData.account.internal_id)

        val prospect = initialData.prospectsResult.head

        val createNotesForm = CreateNotesForm(
          note = sampleNote,
          added_by = AccountId(initialData.account.internal_id),
          team_id = teamId,
          prospect_id = Some(ProspectId(prospect.id)),
          task_uuid = None
        )

        val acc = AccountFixtureForIntegrationTest.createMemberAccount(
          account = initialData.account
        ).get.get

        val role = PermissionMethods.getUserRole(
          loggedinAccount = initialData.account,
          teamId = teamId.id,
          ignoreFatalLogging = true,
          Logger = Logger
        )

        val apiKey = credentialsAuthService.updateApiKey(
          role = role,
          enable_internal_email_accounts_api_for_warmuphero = false,
          accountId = AccountId(acc.internal_id),
          orgId = OrgId(acc.org.id),
          teamId = Some(teamId),
          keyType = SRApiKeyType.SRTeamUserLevelKey
        ).get.get

        val request = FakeRequest(play.api.test.Helpers.POST, sendingUrl)
          .withHeaders("X-API-KEY" -> apiKey,
            "Content-Type" -> "application/json")
          .withJsonBody(Json.toJson(
            createNotesForm
          ))

        val final_result = play.api.test.Helpers.route(testApi, request).get

        val status: Int = play.api.test.Helpers.status(final_result)
        val json: JsValue = play.api.test.Helpers.contentAsJson(final_result)

        final_result.flatMap(res => {

            if(status == 403) {

              val errMsg = (json \ "message").as[String]
              assert(errMsg == "You do not have permission to view notes for this prospect as the prospect owner is not in your permitted_accounts list")

            } else assert(false)

          })

        Await.result(
          final_result,
          Duration.create(15, SECONDS)
        )
        assert(final_result.isCompleted)

      }

    }

    describe("tests for note update API") {

      it("should update and return with note object") {

        val apiKey = initialData.teamUserLevelKey
        val sampleNote = "have to watch Euro semifinal today."
        val teamId = TeamId(initialData.head_team_id)
        val accountId = AccountId(initialData.account.internal_id)

        val prospect = initialData.prospectsResult.head

        val createNotesForm = CreateNotesForm(
          note = sampleNote,
          added_by = AccountId(initialData.account.internal_id),
          team_id = teamId,
          prospect_id = Some(ProspectId(prospect.id)),
          task_uuid = None
        )

        val request = FakeRequest(play.api.test.Helpers.POST, sendingUrl)
          .withHeaders("X-API-KEY" -> apiKey,
            "Content-Type" -> "application/json")
          .withJsonBody(Json.toJson(
            createNotesForm
          ))

        val final_result = play.api.test.Helpers.route(testApi, request).get

        val status: Int = play.api.test.Helpers.status(final_result)
        val json: JsValue = play.api.test.Helpers.contentAsJson(final_result)

        final_result.flatMap(res => {

            if (status == 200) {

              val data = json \ "data"
              val note_id = (data \ "id").as[Long]
              val updatedNote = "Spain has won the Euro 2024!"

              val requestUpdateNote = FakeRequest(play.api.test.Helpers.PUT, sendingUrl + s"/$note_id")
                .withHeaders("X-API-KEY" -> apiKey,
                  "Content-Type" -> "application/json")
                .withJsonBody(Json.obj("note" -> updatedNote))

              val final_result_updated_note = play.api.test.Helpers.route(testApi, requestUpdateNote).get

              val statusUpdatedNote: Int = play.api.test.Helpers.status(final_result_updated_note)
              val jsonUpdatedNote: JsValue = play.api.test.Helpers.contentAsJson(final_result_updated_note)

              final_result_updated_note.flatMap(res => {

                  if(statusUpdatedNote == 200) {

                    val data = jsonUpdatedNote \ "data"
                    val objectType = (data \ "object").as[String]
                    val note = (data \ "note").as[String]
                    val added_by = (data \ "added_by").as[Long]
                    val team_id: Long = (data \ "team_id").as[Long]
                    val prospect_id = (data \ "prospect_id").asOpt[Long]
                    val task_uuid = (data \ "task_uuid").asOpt[Long]
                    val id = (data \ "id").as[Long]

                    assert(objectType == "note")
                    assert(note == updatedNote)
                    assert(id == note_id)
                    assert(added_by == initialData.account.internal_id)
                    assert(team_id == initialData.head_team_id)
                    assert(prospect_id.nonEmpty)
                    assert(task_uuid.isEmpty)

                  } else assert(false)

                })

            } else assert(false)

          })

        Await.result(
          final_result,
          Duration.create(15, SECONDS)
        )
        assert(final_result.isCompleted)

      }

    }

    describe("tests for note delete API") {

      it("should delete and return with note_id of deleted note") {

        val apiKey = initialData.teamUserLevelKey
        val sampleNote = "have to watch Euro semifinal today."
        val teamId = TeamId(initialData.head_team_id)
        val accountId = AccountId(initialData.account.internal_id)

        val prospect = initialData.prospectsResult.head

        val createNotesForm = CreateNotesForm(
          note = sampleNote,
          added_by = AccountId(initialData.account.internal_id),
          team_id = teamId,
          prospect_id = Some(ProspectId(prospect.id)),
          task_uuid = None
        )

        val request = FakeRequest(play.api.test.Helpers.POST, sendingUrl)
          .withHeaders("X-API-KEY" -> apiKey,
            "Content-Type" -> "application/json")
          .withJsonBody(Json.toJson(
            createNotesForm
          ))

        val final_result = play.api.test.Helpers.route(testApi, request).get

        val status: Int = play.api.test.Helpers.status(final_result)
        val json: JsValue = play.api.test.Helpers.contentAsJson(final_result)

        final_result.flatMap(res => {

            if (status == 200) {

              val data = json \ "data"
              val note_id = (data \ "id").as[Long]

              val requestDeleteNote = FakeRequest(play.api.test.Helpers.DELETE, sendingUrl + s"/$note_id")
                .withHeaders("X-API-KEY" -> apiKey,
                  "Content-Type" -> "application/json")
                .withJsonBody(Json.obj())


              val final_result_deleted_note = play.api.test.Helpers.route(testApi, requestDeleteNote).get

              val statusDeletedNote: Int = play.api.test.Helpers.status(final_result_deleted_note)
              val jsonDeletedNote: JsValue = play.api.test.Helpers.contentAsJson(final_result_deleted_note)

              final_result_deleted_note.flatMap(res => {

                  if (statusDeletedNote == 200) {

                    val data = jsonDeletedNote \ "data"
                    val id = (data \ "note_id").as[Long]

                    assert(id == note_id)

                  } else assert(false)

                })

            } else assert(false)

          })

        Await.result(
          final_result,
          Duration.create(15, SECONDS)
        )
        assert(final_result.isCompleted)

      }

    }

  }

}
