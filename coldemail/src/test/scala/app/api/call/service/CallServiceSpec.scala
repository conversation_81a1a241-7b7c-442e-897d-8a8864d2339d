package app.api.call.service

import api_layer_models.{ConvertedCurrency, CurrencyType, ErrorWhileCurrencyConversion}
import api_layer_service.ApiLayerService
import org.apache.pekko.actor.ActorSystem
import org.apache.pekko.stream.Materializer
import api.{AppConfig, CONSTANTS}
import api.accounts.dao.OrganizationDAO
import api.accounts.dao_service.OrganizationDAOService
import api.accounts.{AccountDAO, AccountWarningCodeType, OrgPlan, OrganizationWithCurrentData, TeamId}
import api.call.DAO.{CallDAO, CallHistoryLogDAO, ParticipantDetails, ProspectDetailsForIncomingCalls, RemainingParticipantsDetails}
import io.sr.billing_common.models.PlanID
import api.call.models.{AddCallingCreditDetails, CallAccountSettings, CallDetails, CallFeedBack, CallFeedBackReasons, CallFeedBackResponse, CallFeedBackType, CallParticipantUuid, CallParticipationMode, CallParticipationModeData, CallSID, CallSidOrConfUuid, CallSidToCoach, CallStatus, CallType, CallerIdentity, CallingServiceProvider, ConfSidOrConfUuid, ConferenceFriendlyName, ConferenceSid, ConferenceUuid, InitialCallParticipationModeData, InitiatorCallSid, NewSubAccountDetails, NumberPriceObject, OngoingCallParticipantNotificationObject, OngoingCallProspectDetails, ParticipantCallStatus, ParticipantDetailsToNotify, ParticipantStatusCallbackData, PhoneNumber, PhoneNumberFormatData, PhoneNumberUuid, PhoneNumberValidationError, PhoneSID, PhoneType, PriceObject, StatusCallbackData, StatusCallbackEvents, SubAccountDetails, SubAccountDetailsFound, SubAccountStatus, SubAccountUuid, TaskUuidAndProspectID, TeamIdAndAccountDetails, TwilioUsageLimitCrossedDetails, TwlAuthToken, TwlSubAccountSid, UsageDetails, WebhookValidateData}
import api.accounts.models.{AccountId, OrgId}
import api.call.service.CallService
import api.accounts.models.{AccountId, OrgId}
import api.accounts.service.{OrganizationService, ResetUserCacheUtil}
import api.call.controller.CreateOrUpdateCallAccountData
import api.prospects.dao.ProspectAddEventDAO
import api.call.traits.ConferenceParticipantStatusUpdateError.{ErrorWhileUpdatingConferenceStatusInDB, ErrorWhileUpdatingParticipantStatus}
import api.call.traits.{ActivatingSubAccountError, AddCallingCreditsError, CallQualityFeedBackError, CreateUsageTriggerError, DeleteNumberError, DeleteUsageTriggerError, GetCallDetailsError, GetNumberPriceError, GetSubAccountUsageError, HandleCallError, HandlingUsageWebhookError, ParticipantStatusUpdateError, ProspectCallStatusUpdateError, SubAccountCreationError, UpdateCallSettingsError, UpdateSubAccountCreditError, ValidateWebhookError}
import api.notes.dao.NotesDAO
import api.prospects.dao.ProspectDAO
import api.prospects.dao_service.ProspectDAOService
import api.prospects.models.ProspectId
import api.tasks.services.TaskUuid
import api.twilio.service.{ApiKey, ApiSecret, CallLog, ConferenceDetailsFromWebhook, ParticipantDetailsFromWebhook, SubAccountApiKeyAndSecret, SubAccountTwimlApplication, TwilioConferenceAndParticipantDetails, TwilioDialerService, TwilioSubAccountDetails, TwilioUsageTrigger, TwlTwimlAppName, TwlTwimlAppSid}
import app.test_fixtures.inbox_service.InboxV3ServiceFixtures
import app.test_fixtures.organizationa.OrgPlanFixture
import db_test_spec.api.campaigns.fixtures.DefaultCampaignParametersFixtures
import eventframework.ProspectObject
import org.joda.time.DateTime
import org.scalamock.scalatest.{AsyncMockFactory, MockFactory}
import org.scalatest.funspec.{AnyFunSpec, AsyncFunSpec}
import play.api.libs.ws.WSClient
import play.api.libs.ws.ahc.AhcWSClient
import utils.cache.models.SrResetCacheInterval
import utils.cache_utils.model.CacheIdKeyForLock
import utils.cache_utils.service.SrRedisSimpleLockServiceV2
import utils.email_notification.models.{EmailNotificationLog, NotificationType}
import utils.email_notification.service.EmailNotificationService
import utils.helpers.LogHelpers
import utils.phonenumber.PhoneNumberValidator.{isValidPhoneNumber, parseNumber}
import utils.testapp.TestAppExecutionContext
import utils.{CallUtils, ISRLogger, PlanLimitService, PusherService, SRLogger}
import utils.uuid.SrUuidUtils

import scala.concurrent.duration.{Duration, SECONDS}
import scala.concurrent.{Await, ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

class CallServiceSpec extends AsyncFunSpec with AsyncMockFactory {

  val twilioDialerService: TwilioDialerService = mock[TwilioDialerService]
  val srUuidUtils: SrUuidUtils = mock[SrUuidUtils]
  val callDAO: CallDAO = mock[CallDAO]
  val prospectAddEventDAO = mock[ProspectAddEventDAO]
  val pusherService: PusherService = mock[PusherService]
  val organizationDAO: OrganizationDAO = mock[OrganizationDAO]
  val prospectDAO: ProspectDAO = mock[ProspectDAO]
  val resetUserCacheUtil: ResetUserCacheUtil = mock[ResetUserCacheUtil]
  val accountDAO: AccountDAO = mock[AccountDAO]
  val emailNotificationService: EmailNotificationService = mock[EmailNotificationService]
  val srRedisSimpleLockServiceV2 = mock[SrRedisSimpleLockServiceV2]
  val callUtils = mock[CallUtils]
  val callHistoryLogDAO = mock[CallHistoryLogDAO]
  val planLimitService = mock[PlanLimitService]
  val prospectDAOSErvice = mock[ProspectDAOService]
  val organizationDAOService = mock[OrganizationDAOService]
  val notesDAO = mock[NotesDAO]

  given system: ActorSystem = TestAppExecutionContext.actorSystem
  given materializer: Materializer = TestAppExecutionContext.actorMaterializer
  given wsClient: AhcWSClient = TestAppExecutionContext.wsClient
  given ec: ExecutionContext = TestAppExecutionContext.ec

  val apiLayerService = mock[ApiLayerService]

  val callService: CallService = new CallService(
    twilioDialerService = twilioDialerService,
    srUuidUtils = srUuidUtils,
    callDAO = callDAO,
    prospectAddEventDAO = prospectAddEventDAO,
    pusherService = pusherService,
    organisationDAO = organizationDAO,
    prospectDAO = prospectDAO,
    resetCacheUtil = resetUserCacheUtil,
    accountDAO = accountDAO,
    planLimitService = planLimitService,
    srRedisSimpleLockServiceV2 = srRedisSimpleLockServiceV2,
    emailNotificationService = emailNotificationService,
    apiLayerService = apiLayerService,
    callUtils = callUtils,
    organizationDAOService = organizationDAOService,
    callHistoryLogDAO = callHistoryLogDAO,
    prospectDAOService = prospectDAOSErvice,
    notesDAO = notesDAO
  )

  val priceObject: List[PriceObject] = List(PriceObject(
    phone_type = PhoneType.Local.toString,
    base_price = 1.0,
    current_price = 1.0
  ))

  val emailaddress = "<EMAIL>"



  // WEBHOOK CALL

  val numberPriceObject: NumberPriceObject = NumberPriceObject(
    country = "United States",
    countryISO = "US",
    prices = priceObject,
    currency = "USD"
  )

  val conferenceAndParticipantDetails: TwilioConferenceAndParticipantDetails = TwilioConferenceAndParticipantDetails(
    conference_details = Some(ConferenceDetailsFromWebhook(
      conference_sid = Some("some sid"),
      conference_name = Some("some name"),
      initiated_by = PhoneNumber("+************"),
      service_provider = CallingServiceProvider.TWILIO,
      callType = CallType.TWL_CONF
    )),
    participantDetails = Seq(),
    response = "some response"
  )

  val twl_signature = "twilio_signature_hun_main"
  val webhook_url = "webhook_url_hun_main"
  val incoming_call_to = PhoneNumber(phone_number = "+***********")
  val client_identity = CallerIdentity(identity = "phone_number_2QYQMx8Xw1Yh81AlA380EBT66ei")
  val outgoing_params: Map[String, String] = Map("From" -> s"client:${client_identity.identity}", "To" -> "+91**********", "CallSid" -> "Hi_I'm Call sid", "task_id" -> "3")
  val incoming_params: Map[String, String] = Map("From" -> "+91**********", "To" -> s"${incoming_call_to}", "CallSid" -> "Hi_I'm Call sid")

  val validate_webhook_data = WebhookValidateData(
    twilio_signature = Some(twl_signature),
    webhook_url = webhook_url,
    params = outgoing_params
  )

  val call_sid = CallSID(
    sid = "Hi_I'm Call sid"
  )
  val callSidOrConfUuid = CallSidOrConfUuid.WithCallSid(call_sid)
  val org = InboxV3ServiceFixtures.org.copy(id = org_id.id)


  val call_part_uuid = CallParticipantUuid("part_uuid")

  val statusCallbackData: StatusCallbackData = StatusCallbackData(
    conferenceSid = ConferenceSid("conf_sid"),
    statusCallbackEvent = StatusCallbackEvents.CONFERENCE_START,
    callSID = Some(CallSID("call_sid")),
    participant_label = None
  )

  val conference_uuid = ConferenceUuid("conf_uuid")

  val conf_sid = ConferenceSid(sid = "conf_sid")
  val conf_sid_or_conf_uuid = ConfSidOrConfUuid.WithConfSid(conf_sid = conf_sid)


  val participantStatusCallbackData: ParticipantStatusCallbackData = ParticipantStatusCallbackData(
    status = ParticipantCallStatus.IN_PROGRESS,
    call_sid = call_sid
  )


  describe("conference participant status changes") {

    it("should call updateConferenceStatus and return an error if conference start and dao layer returns an error") {
      (callDAO.updateConferenceStatus)
        .expects(conf_sid_or_conf_uuid, CallStatus.ACTIVE, *, *)
        .returning(Failure(new Exception("some exception")))

      val result = callService.handleConferenceParticipantStatusChange(
        data = statusCallbackData,
        teamId = team_id
      )

      result match {
        case Left(ErrorWhileUpdatingConferenceStatusInDB(_)) => assert(true)
        case _ => assert(false)
      }
    }

    it("should call updateConferenceStatus and pass when conference start and dao layer does not return error") {
      (callDAO.updateConferenceStatus)
        .expects(conf_sid_or_conf_uuid, CallStatus.ACTIVE, *, *)
        .returning(Success(ConferenceUuid("some_uuid")))

      val result = callService.handleConferenceParticipantStatusChange(
        data = statusCallbackData,
        teamId = team_id
      )

      result match {
        case Right(_) => assert(true)
        case Left(_) => assert(false)
      }
    }

    it("should call updateConferenceStatus and return an error if conference end and dao layer returns an error") {
      val data = statusCallbackData.copy(statusCallbackEvent = StatusCallbackEvents.CONFERENCE_END)
      (callDAO.updateConferenceStatus)
        .expects(conf_sid_or_conf_uuid, CallStatus.COMPLETED, *, *)
        .returning(Failure(new Exception("some exception")))

      val result = callService.handleConferenceParticipantStatusChange(data = data, teamId = team_id)

      result match {
        case Left(ErrorWhileUpdatingConferenceStatusInDB(_)) => assert(true)
        case _ => assert(false)
      }
    }

    it("should call updateConferenceStatus and pass when conference end and dao layer does not return error") {
      val data = statusCallbackData.copy(statusCallbackEvent = StatusCallbackEvents.CONFERENCE_END)
      (callDAO.updateConferenceStatus)
        .expects(conf_sid_or_conf_uuid, CallStatus.COMPLETED, *, *)
        .returning(Success(ConferenceUuid("some_uuid")))

      val result = callService.handleConferenceParticipantStatusChange(data = data, teamId = team_id)

      result match {
        case Right(_) => assert(true)
        case Left(_) => assert(false)
      }
    }

    it("should return an error if participant join and and no call_sid") {
      val data = statusCallbackData.copy(statusCallbackEvent = StatusCallbackEvents.PARTICIPANT_JOIN, callSID = None)


      val result = callService.handleConferenceParticipantStatusChange(data = data, teamId = team_id)

      result match {
        case Left(ErrorWhileUpdatingParticipantStatus(err)) => err match {
          case ParticipantStatusUpdateError.NoCallSIDFoundForParticipantStatusUpdate => assert(true)
          case _ => assert(false)
        }
        case _ => assert(false)
      }
    }

    it("should return an error if participant join and and dao returns error") {
      val data = statusCallbackData.copy(statusCallbackEvent = StatusCallbackEvents.PARTICIPANT_JOIN)

      (callDAO.updateParticipantStatus)
        .expects(CallSidOrConfUuid.WithCallSid(CallSID("call_sid")), ParticipantCallStatus.IN_PROGRESS, *, *)
        .returning(Failure(new Exception("some error")))


      val result = callService.handleConferenceParticipantStatusChange(data = data, teamId = team_id)

      result match {
        case Left(ErrorWhileUpdatingParticipantStatus(err)) => err match {
          case ParticipantStatusUpdateError.ErrorWhileUpdatingParticipantStatusInDB(err) => assert(true)
          case _ => assert(false)
        }
        case _ => assert(false)
      }
    }

    it("should pass if participant join ") {
      val data = statusCallbackData.copy(statusCallbackEvent = StatusCallbackEvents.PARTICIPANT_JOIN)

      (callDAO.updateParticipantStatus)
        .expects(CallSidOrConfUuid.WithCallSid(CallSID("call_sid")), ParticipantCallStatus.IN_PROGRESS, *, *)
        .returning(Success(CallParticipantUuid("part_uuid")))
      (callDAO.getActiveParticipantAndConferenceDetails)
        .expects(*, *, *, *)
        .returning(Success(Seq(ongoingCallParticipantNotificationObject)))
      (organizationDAO.getOrgIdFromTeamId)
        .expects(*)
        .returning(Success(OrgId(3L)))
      (pusherService.sendMessageUsingPusher)
        .expects(*, *, *, *)
        .returning(Success("success"))


      val result = callService.handleConferenceParticipantStatusChange(data = data, teamId = team_id)

      result match {
        case Left(_) => assert(false)
        case _ => assert(true)
      }
    }


    it("should return an error if participant leave and and no call_sid") {
      val data = statusCallbackData.copy(statusCallbackEvent = StatusCallbackEvents.PARTICIPANT_LEAVE, callSID = None)


      val result = callService.handleConferenceParticipantStatusChange(data = data, teamId = team_id)

      result match {
        case Left(ErrorWhileUpdatingParticipantStatus(err)) => err match {
          case ParticipantStatusUpdateError.NoCallSIDFoundForParticipantStatusUpdate => assert(true)
          case _ => assert(false)
        }
        case _ => assert(false)
      }
    }

    it("should return an error if participant leave and and dao returns error") {
      val data = statusCallbackData.copy(statusCallbackEvent = StatusCallbackEvents.PARTICIPANT_LEAVE)

      (callDAO.updateParticipantStatus)
        .expects(CallSidOrConfUuid.WithCallSid(CallSID("call_sid")), ParticipantCallStatus.COMPLETED, *, *)
        .returning(Failure(new Exception("some error")))


      val result = callService.handleConferenceParticipantStatusChange(data = data, teamId = team_id)

      result match {
        case Left(ErrorWhileUpdatingParticipantStatus(err)) => err match {
          case ParticipantStatusUpdateError.ErrorWhileUpdatingParticipantStatusInDB(err) => assert(true)
          case _ => assert(false)
        }
        case _ => assert(false)
      }
    }

    it("should pass if participant leave ") {
      val data = statusCallbackData.copy(statusCallbackEvent = StatusCallbackEvents.PARTICIPANT_LEAVE)
      val remaining_participants_data: List[RemainingParticipantsDetails] = List(RemainingParticipantsDetails(
        latest_participation_mode = CallParticipationMode.InitiatorMode, participant_label = "initiator"
      ), RemainingParticipantsDetails(
        latest_participation_mode = CallParticipationMode.InitiatorMode, participant_label = "customer"
      ))

      (callDAO.updateParticipantStatus)
        .expects(CallSidOrConfUuid.WithCallSid(CallSID("call_sid")), ParticipantCallStatus.COMPLETED, *, *)
        .returning(Success(CallParticipantUuid("part_uuid")))
      (callDAO.getActiveParticipantAndConferenceDetails)
        .expects(*, *, *, *)
        .returning(Success(Seq(ongoingCallParticipantNotificationObject)))
      (callDAO.getRemainingParticipantDetails)
        .expects(*, *)
        .returning(Success(remaining_participants_data))

      (organizationDAO.getOrgIdFromTeamId)
        .expects(*)
        .returning(Success(OrgId(3L)))
      (pusherService.sendMessageUsingPusher)
        .expects(*, *, *, *)
        .returning(Success("success"))


      val result = callService.handleConferenceParticipantStatusChange(data = data, teamId = team_id)

      result match {
        case Left(_) => assert(false)

        case _ => assert(true)
      }
    }
  }

  describe("mark conference as complete") {


    it("should fail when getOrgIdFromId fails") {

      (organizationDAO.getOrgIdFromTeamId)
        .expects(team_id)
        .returning(Failure(new Exception("some exception")))

      val res = callService.markConferenceAsComplete(conferenceSid = conf_sid, teamId = team_id)

      res match {

        case Failure(_) => assert(true)

        case Success(_: ConferenceSid) => assert(false)
      }
    }

    it("should fail when getSubAccountDetailsForOrg fails") {

      (organizationDAO.getOrgIdFromTeamId)
        .expects(team_id)
        .returning(Success(org_id))

      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Failure(new Exception("some exception")))

      val res = callService.markConferenceAsComplete(conferenceSid = conf_sid, teamId = team_id)

      res match {

        case Failure(_) => assert(true)

        case Success(_: ConferenceSid) => assert(false)
      }
    }

    it("should fail when updateConferenceStatus fails") {

      (organizationDAO.getOrgIdFromTeamId)
        .expects(team_id)
        .returning(Success(org_id))

      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Success(Some(sub_account_details)))

      (callDAO.updateConferenceStatus)
        .expects(conf_sid_or_conf_uuid, CallStatus.COMPLETED, *, *)
        .returning(Failure(new Exception("some exception")))


      val res = callService.markConferenceAsComplete(conferenceSid = conf_sid, teamId = team_id)

      res match {

        case Failure(_) => assert(true)

        case Success(_: ConferenceSid) => assert(false)
      }
    }

    it("should succeed even when completeConference fails") {

      (organizationDAO.getOrgIdFromTeamId)
        .expects(team_id)
        .returning(Success(org_id))

      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Success(Some(sub_account_details)))

      (callDAO.updateConferenceStatus)
        .expects(conf_sid_or_conf_uuid, CallStatus.COMPLETED, *, *)
        .returning(Success(ConferenceUuid("some_uuid")))

      (twilioDialerService.completeConference)
        .expects(sub_account_details, conf_sid)
        .returning(Failure(new Exception("some exception")))


      val res = callService.markConferenceAsComplete(conferenceSid = conf_sid, teamId = team_id)

      res match {

        case Failure(_) => assert(false)

        case Success(_: ConferenceSid) => assert(true)
      }
    }

    it("should succeed") {

      (organizationDAO.getOrgIdFromTeamId)
        .expects(team_id)
        .returning(Success(org_id))

      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Success(Some(sub_account_details)))

      (callDAO.updateConferenceStatus)
        .expects(conf_sid_or_conf_uuid, CallStatus.COMPLETED, *, *)
        .returning(Success(ConferenceUuid("some_uuid")))

      (twilioDialerService.completeConference)
        .expects(sub_account_details, conf_sid)
        .returning(Success(conf_sid))

      val res = callService.markConferenceAsComplete(conferenceSid = conf_sid, teamId = team_id)

      res match {

        case Failure(_) => assert(false)

        case Success(_: ConferenceSid) => assert(true)
      }
    }
  }


  describe("handleProspectNoAnswerAndBusy") {

    it("should fail when error while getting conf sid from call sid") {

      (callDAO.getConferenceSIDFromCallSID)
        .expects(call_sid, team_id)
        .returning(Failure(new Exception("some error")))


      val res = callService.handleProspectNoAnswerAndBusyAndCancelledAndFailed(data = participantStatusCallbackData, teamId = team_id)

      res match {
        case Right(_) => assert(false)
        case Left(error) => error match {
          case ProspectCallStatusUpdateError.ErrorWhileUpdatingParticipantStatus(_) => assert(false)
          case ProspectCallStatusUpdateError.ErrorWhileCompletingConference(_) => assert(false)
          case ProspectCallStatusUpdateError.ErrorWhileFetchingConferenceSid(_) => assert(true)
        }
      }
    }

    it("should fail when error while marking conference as complete - getOrgIdFromTeamId") {

      (callDAO.getConferenceSIDFromCallSID)
        .expects(call_sid, team_id)
        .returning(Success(conf_sid))

      (organizationDAO.getOrgIdFromTeamId)
        .expects(team_id)
        .returning(Failure(new Exception("some exception")))


      val res = callService.handleProspectNoAnswerAndBusyAndCancelledAndFailed(data = participantStatusCallbackData, teamId = team_id)

      res match {
        case Right(_) => assert(false)
        case Left(error) => error match {
          case ProspectCallStatusUpdateError.ErrorWhileUpdatingParticipantStatus(_) => assert(false)
          case ProspectCallStatusUpdateError.ErrorWhileCompletingConference(_) => assert(true)
          case ProspectCallStatusUpdateError.ErrorWhileFetchingConferenceSid(_) => assert(false)
        }
      }
    }

    it("should fail when error while marking conference as complete - getSubAccountDetailsForOrg") {

      (callDAO.getConferenceSIDFromCallSID)
        .expects(call_sid, team_id)
        .returning(Success(conf_sid))

      (organizationDAO.getOrgIdFromTeamId)
        .expects(team_id)
        .returning(Success(org_id))

      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Failure(new Exception("some exception")))


      val res = callService.handleProspectNoAnswerAndBusyAndCancelledAndFailed(data = participantStatusCallbackData, teamId = team_id)

      res match {
        case Right(_) => assert(false)
        case Left(error) => error match {
          case ProspectCallStatusUpdateError.ErrorWhileUpdatingParticipantStatus(_) => assert(false)
          case ProspectCallStatusUpdateError.ErrorWhileCompletingConference(_) => assert(true)
          case ProspectCallStatusUpdateError.ErrorWhileFetchingConferenceSid(_) => assert(false)
        }
      }
    }

    it("should fail when error while marking conference as complete - updateConferenceStatus") {

      (callDAO.getConferenceSIDFromCallSID)
        .expects(call_sid, team_id)
        .returning(Success(conf_sid))

      (organizationDAO.getOrgIdFromTeamId)
        .expects(team_id)
        .returning(Success(org_id))

      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Success(Some(sub_account_details)))

      (callDAO.updateConferenceStatus)
        .expects(conf_sid_or_conf_uuid, CallStatus.COMPLETED, None, team_id)
        .returning(Failure(new Exception("some exception")))


      val res = callService.handleProspectNoAnswerAndBusyAndCancelledAndFailed(data = participantStatusCallbackData, teamId = team_id)

      res match {
        case Right(_) => assert(false)
        case Left(error) => error match {
          case ProspectCallStatusUpdateError.ErrorWhileUpdatingParticipantStatus(_) => assert(false)
          case ProspectCallStatusUpdateError.ErrorWhileCompletingConference(_) => assert(true)
          case ProspectCallStatusUpdateError.ErrorWhileFetchingConferenceSid(_) => assert(false)
        }
      }
    }

    it("should succeed even when error while marking conference as complete - completeConference") {

      (callDAO.getConferenceSIDFromCallSID)
        .expects(call_sid, team_id)
        .returning(Success(conf_sid))

      (organizationDAO.getOrgIdFromTeamId)
        .expects(team_id)
        .returning(Success(org_id))

      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Success(Some(sub_account_details)))

      (callDAO.updateConferenceStatus)
        .expects(conf_sid_or_conf_uuid, CallStatus.COMPLETED, None, team_id)
        .returning(Success(conference_uuid))

      (twilioDialerService.completeConference)
        .expects(sub_account_details, conf_sid)
        .returning(Failure(new Exception("some exception")))

      (callDAO.updateParticipantStatus)
        .expects(callSidOrConfUuid, ParticipantCallStatus.IN_PROGRESS, None, team_id)
        .returning(Success(call_part_uuid))


      (callDAO.getActiveParticipantAndConferenceDetails)
        .expects(team_id, Some(call_part_uuid), None, CallType.TWL_CONF)
        .returning(Success(Seq(ongoingCallParticipantNotificationObject)))

      (organizationDAO.getOrgIdFromTeamId)
        .expects(team_id)
        .returning(Success(org_id))

      (pusherService.sendMessageUsingPusher)
        .expects(*, *, *, *)
        .returning(Success("Pusher message"))
      //      (callDAO.updateParticipantStatus(CallSID(Hi_I'm Call sid), in-progress, 7))


      val res = callService.handleProspectNoAnswerAndBusyAndCancelledAndFailed(data = participantStatusCallbackData, teamId = team_id)

      res match {
        case Right(_) => assert(true)
        case Left(error) => error match {
          case ProspectCallStatusUpdateError.ErrorWhileUpdatingParticipantStatus(_) => assert(false)
          case ProspectCallStatusUpdateError.ErrorWhileCompletingConference(_) => assert(false)
          case ProspectCallStatusUpdateError.ErrorWhileFetchingConferenceSid(_) => assert(false)
        }
      }
    }

    it("should fail when error while handleParticipantStatusChange - updateParticipantStatus") {

      (callDAO.getConferenceSIDFromCallSID)
        .expects(call_sid, team_id)
        .returning(Success(conf_sid))

      (organizationDAO.getOrgIdFromTeamId)
        .expects(team_id)
        .returning(Success(org_id))

      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Success(Some(sub_account_details)))

      (callDAO.updateConferenceStatus)
        .expects(conf_sid_or_conf_uuid, CallStatus.COMPLETED, None, team_id)
        .returning(Success(conference_uuid))

      (twilioDialerService.completeConference)
        .expects(sub_account_details, conf_sid)
        .returning(Success(conf_sid))

      (callDAO.updateParticipantStatus)
        .expects(callSidOrConfUuid, ParticipantCallStatus.IN_PROGRESS, None, team_id)
        .returning(Failure(new Exception("some exception")))


      val res = callService.handleProspectNoAnswerAndBusyAndCancelledAndFailed(data = participantStatusCallbackData, teamId = team_id)

      res match {
        case Right(_) => assert(false)
        case Left(error) => error match {
          case ProspectCallStatusUpdateError.ErrorWhileUpdatingParticipantStatus(error) => error match {
            case ParticipantStatusUpdateError.ErrorWhileUpdatingParticipantStatusInDB(_) => assert(true)
            case _ => assert(false)
          }
          case ProspectCallStatusUpdateError.ErrorWhileCompletingConference(_) => assert(false)
          case ProspectCallStatusUpdateError.ErrorWhileFetchingConferenceSid(_) => assert(false)
        }
      }
    }

    it("should fail when error while handleParticipantStatusChange - getActiveParticipantAndConferenceDetails") {

      (callDAO.getConferenceSIDFromCallSID)
        .expects(call_sid, team_id)
        .returning(Success(conf_sid))

      (organizationDAO.getOrgIdFromTeamId)
        .expects(team_id)
        .returning(Success(org_id))

      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Success(Some(sub_account_details)))

      (callDAO.updateConferenceStatus)
        .expects(conf_sid_or_conf_uuid, CallStatus.COMPLETED, None, team_id)
        .returning(Success(conference_uuid))

      (twilioDialerService.completeConference)
        .expects(sub_account_details, conf_sid)
        .returning(Success(conf_sid))

      (callDAO.updateParticipantStatus)
        .expects(callSidOrConfUuid, ParticipantCallStatus.IN_PROGRESS, None, team_id)
        .returning(Success(call_part_uuid))

      (callDAO.getActiveParticipantAndConferenceDetails)
        .expects(team_id, Some(call_part_uuid), None, CallType.TWL_CONF)
        .returning(Failure(new Exception("some exception")))


      val res = callService.handleProspectNoAnswerAndBusyAndCancelledAndFailed(data = participantStatusCallbackData, teamId = team_id)

      res match {
        case Right(_) => assert(false)
        case Left(error) => error match {
          case ProspectCallStatusUpdateError.ErrorWhileUpdatingParticipantStatus(error) => error match {
            case ParticipantStatusUpdateError.ErrorWhileFetchingNotificationObjectDetails(_) => assert(true)
            case _ => assert(false)
          }
          case ProspectCallStatusUpdateError.ErrorWhileCompletingConference(_) => assert(false)
          case ProspectCallStatusUpdateError.ErrorWhileFetchingConferenceSid(_) => assert(false)
        }
      }
    }

    it("should fail when error while handleParticipantStatusChange - getOrgIdFromTeamId") {

      (callDAO.getConferenceSIDFromCallSID)
        .expects(call_sid, team_id)
        .returning(Success(conf_sid))

      (organizationDAO.getOrgIdFromTeamId)
        .expects(team_id)
        .returning(Success(org_id))

      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Success(Some(sub_account_details)))

      (callDAO.updateConferenceStatus)
        .expects(conf_sid_or_conf_uuid, CallStatus.COMPLETED, None, team_id)
        .returning(Success(conference_uuid))

      (twilioDialerService.completeConference)
        .expects(sub_account_details, conf_sid)
        .returning(Success(conf_sid))

      (callDAO.updateParticipantStatus)
        .expects(callSidOrConfUuid, ParticipantCallStatus.IN_PROGRESS, None, team_id)
        .returning(Success(call_part_uuid))

      (callDAO.getActiveParticipantAndConferenceDetails)
        .expects(team_id, Some(call_part_uuid), None, CallType.TWL_CONF)
        .returning(Success(Seq(ongoingCallParticipantNotificationObject)))

      (organizationDAO.getOrgIdFromTeamId)
        .expects(team_id)
        .returning(Failure(new Exception("some exception")))


      val res = callService.handleProspectNoAnswerAndBusyAndCancelledAndFailed(data = participantStatusCallbackData, teamId = team_id)

      res match {
        case Right(_) => assert(false)
        case Left(error) => error match {
          case ProspectCallStatusUpdateError.ErrorWhileUpdatingParticipantStatus(error) => error match {
            case ParticipantStatusUpdateError.ErrorWhileFetchingOrgId(_) => assert(true)
            case _ => assert(false)
          }
          case ProspectCallStatusUpdateError.ErrorWhileCompletingConference(_) => assert(false)
          case ProspectCallStatusUpdateError.ErrorWhileFetchingConferenceSid(_) => assert(false)
        }
      }
    }

    it("should fail when error while handleParticipantStatusChange - sendMessageUsingPusher") {

      (callDAO.getConferenceSIDFromCallSID)
        .expects(call_sid, team_id)
        .returning(Success(conf_sid))

      (organizationDAO.getOrgIdFromTeamId)
        .expects(team_id)
        .returning(Success(org_id))

      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Success(Some(sub_account_details)))

      (callDAO.updateConferenceStatus)
        .expects(conf_sid_or_conf_uuid, CallStatus.COMPLETED, None, team_id)
        .returning(Success(conference_uuid))

      (twilioDialerService.completeConference)
        .expects(sub_account_details, conf_sid)
        .returning(Success(conf_sid))

      (callDAO.updateParticipantStatus)
        .expects(callSidOrConfUuid, ParticipantCallStatus.IN_PROGRESS, None, team_id)
        .returning(Success(call_part_uuid))

      (callDAO.getActiveParticipantAndConferenceDetails)
        .expects(team_id, Some(call_part_uuid), None, CallType.TWL_CONF)
        .returning(Success(Seq(ongoingCallParticipantNotificationObject)))

      (organizationDAO.getOrgIdFromTeamId)
        .expects(team_id)
        .returning(Success(org_id))

      (pusherService.sendMessageUsingPusher)
        .expects(*, *, *, *)
        .returning(Failure(new Exception("some exception")))


      val res = callService.handleProspectNoAnswerAndBusyAndCancelledAndFailed(data = participantStatusCallbackData, teamId = team_id)

      res match {
        case Right(_) => assert(false)
        case Left(error) => error match {
          case ProspectCallStatusUpdateError.ErrorWhileUpdatingParticipantStatus(error) => error match {
            case ParticipantStatusUpdateError.ErrorWhileSendingPusherNotification(_) => assert(true)
            case _ => assert(false)
          }
          case ProspectCallStatusUpdateError.ErrorWhileCompletingConference(_) => assert(false)
          case ProspectCallStatusUpdateError.ErrorWhileFetchingConferenceSid(_) => assert(false)
        }
      }
    }

    it("should succeed") {

      (callDAO.getConferenceSIDFromCallSID)
        .expects(call_sid, team_id)
        .returning(Success(conf_sid))


      (organizationDAO.getOrgIdFromTeamId)
        .expects(team_id)
        .returning(Success(org_id))

      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Success(Some(sub_account_details)))

      (callDAO.updateConferenceStatus)
        .expects(conf_sid_or_conf_uuid, CallStatus.COMPLETED, None, team_id)
        .returning(Success(conference_uuid))

      (twilioDialerService.completeConference)
        .expects(sub_account_details, conf_sid)
        .returning(Success(conf_sid))

      (callDAO.updateParticipantStatus)
        .expects(callSidOrConfUuid, ParticipantCallStatus.IN_PROGRESS, None, team_id)
        .returning(Success(call_part_uuid))

      (callDAO.getActiveParticipantAndConferenceDetails)
        .expects(team_id, Some(call_part_uuid), None, CallType.TWL_CONF)
        .returning(Success(Seq(ongoingCallParticipantNotificationObject)))

      (organizationDAO.getOrgIdFromTeamId)
        .expects(team_id)
        .returning(Success(org_id))

      (pusherService.sendMessageUsingPusher)
        .expects(*, *, *, *)
        .returning(Success("Pusher message"))

      val res = callService.handleProspectNoAnswerAndBusyAndCancelledAndFailed(data = participantStatusCallbackData, teamId = team_id)

      res match {
        case Right(_) => assert(true)
        case Left(_) => assert(false)
      }
    }
  }

  describe("handleProspectStatusChange") {

    it("should fail when error while fetching conf sid") {
      (callDAO.getConferenceSIDFromCallSID)
        .expects(call_sid, team_id)
        .returning(Failure(new Exception("some error")))


      val res = callService.handleProspectLeave(data = participantStatusCallbackData, teamId = team_id)

      res match {
        case Right(_) => assert(false)
        case Left(error) => error match {
          case ProspectCallStatusUpdateError.ErrorWhileUpdatingParticipantStatus(_) => assert(false)
          case ProspectCallStatusUpdateError.ErrorWhileCompletingConference(_) => assert(false)
          case ProspectCallStatusUpdateError.ErrorWhileFetchingConferenceSid(_) => assert(true)
        }
      }
    }

    it("should fail when error while handleParticipantStatusChange - updateParticipantStatus") {

      (callDAO.getConferenceSIDFromCallSID)
        .expects(call_sid, team_id)
        .returning(Success(conf_sid))

      (callDAO.updateParticipantStatus)
        .expects(callSidOrConfUuid, ParticipantCallStatus.IN_PROGRESS, None, team_id)
        .returning(Failure(new Exception("some exception")))


      val res = callService.handleProspectLeave(data = participantStatusCallbackData, teamId = team_id)

      res match {
        case Right(_) => assert(false)
        case Left(error) => error match {
          case ProspectCallStatusUpdateError.ErrorWhileUpdatingParticipantStatus(error) => error match {
            case ParticipantStatusUpdateError.ErrorWhileUpdatingParticipantStatusInDB(_) => assert(true)
            case _ => assert(false)
          }
          case ProspectCallStatusUpdateError.ErrorWhileCompletingConference(_) => assert(false)
          case ProspectCallStatusUpdateError.ErrorWhileFetchingConferenceSid(_) => assert(false)
        }
      }
    }

    it("should fail when error while handleParticipantStatusChange - getActiveParticipantAndConferenceDetails") {

      (callDAO.getConferenceSIDFromCallSID)
        .expects(call_sid, team_id)
        .returning(Success(conf_sid))

      (callDAO.updateParticipantStatus)
        .expects(callSidOrConfUuid, ParticipantCallStatus.IN_PROGRESS, None, team_id)
        .returning(Success(call_part_uuid))

      (callDAO.getActiveParticipantAndConferenceDetails)
        .expects(team_id, Some(call_part_uuid), None, CallType.TWL_CONF)
        .returning(Failure(new Exception("some exception")))


      val res = callService.handleProspectLeave(data = participantStatusCallbackData, teamId = team_id)

      res match {
        case Right(_) => assert(false)
        case Left(error) => error match {
          case ProspectCallStatusUpdateError.ErrorWhileUpdatingParticipantStatus(error) => error match {
            case ParticipantStatusUpdateError.ErrorWhileFetchingNotificationObjectDetails(_) => assert(true)
            case _ => assert(false)
          }
          case ProspectCallStatusUpdateError.ErrorWhileCompletingConference(_) => assert(false)
          case ProspectCallStatusUpdateError.ErrorWhileFetchingConferenceSid(_) => assert(false)
        }
      }
    }

    it("should fail when error while handleParticipantStatusChange - getOrgIdFromTeamId") {

      (callDAO.getConferenceSIDFromCallSID)
        .expects(call_sid, team_id)
        .returning(Success(conf_sid))

      (callDAO.updateParticipantStatus)
        .expects(callSidOrConfUuid, ParticipantCallStatus.IN_PROGRESS, None, team_id)
        .returning(Success(call_part_uuid))

      (callDAO.getActiveParticipantAndConferenceDetails)
        .expects(team_id, Some(call_part_uuid), None, CallType.TWL_CONF)
        .returning(Success(Seq(ongoingCallParticipantNotificationObject)))

      (organizationDAO.getOrgIdFromTeamId)
        .expects(team_id)
        .returning(Failure(new Exception("some exception")))


      val res = callService.handleProspectLeave(data = participantStatusCallbackData, teamId = team_id)

      res match {
        case Right(_) => assert(false)
        case Left(error) => error match {
          case ProspectCallStatusUpdateError.ErrorWhileUpdatingParticipantStatus(error) => error match {
            case ParticipantStatusUpdateError.ErrorWhileFetchingOrgId(_) => assert(true)
            case _ => assert(false)
          }
          case ProspectCallStatusUpdateError.ErrorWhileCompletingConference(_) => assert(false)
          case ProspectCallStatusUpdateError.ErrorWhileFetchingConferenceSid(_) => assert(false)
        }
      }
    }


    it("should fail when error while handleParticipantStatusChange - sendMessageUsingPusher") {

      (callDAO.getConferenceSIDFromCallSID)
        .expects(call_sid, team_id)
        .returning(Success(conf_sid))

      (callDAO.updateParticipantStatus)
        .expects(callSidOrConfUuid, ParticipantCallStatus.IN_PROGRESS, None, team_id)
        .returning(Success(call_part_uuid))

      (callDAO.getActiveParticipantAndConferenceDetails)
        .expects(team_id, Some(call_part_uuid), None, CallType.TWL_CONF)
        .returning(Success(Seq(ongoingCallParticipantNotificationObject)))

      (organizationDAO.getOrgIdFromTeamId)
        .expects(team_id)
        .returning(Success(org_id))

      (pusherService.sendMessageUsingPusher)
        .expects(*, *, *, *)
        .returning(Failure(new Exception("some exception")))


      val res = callService.handleProspectLeave(data = participantStatusCallbackData, teamId = team_id)

      res match {
        case Right(_) => assert(false)
        case Left(error) => error match {
          case ProspectCallStatusUpdateError.ErrorWhileUpdatingParticipantStatus(error) => error match {
            case ParticipantStatusUpdateError.ErrorWhileSendingPusherNotification(_) => assert(true)
            case _ => assert(false)
          }
          case ProspectCallStatusUpdateError.ErrorWhileCompletingConference(_) => assert(false)
          case ProspectCallStatusUpdateError.ErrorWhileFetchingConferenceSid(_) => assert(false)
        }
      }
    }

    it("should succeed") {

      (callDAO.getConferenceSIDFromCallSID)
        .expects(call_sid, team_id)
        .returning(Success(conf_sid))

      (callDAO.updateParticipantStatus)
        .expects(callSidOrConfUuid, ParticipantCallStatus.IN_PROGRESS, None, team_id)
        .returning(Success(call_part_uuid))

      (callDAO.getActiveParticipantAndConferenceDetails)
        .expects(team_id, Some(call_part_uuid), None, CallType.TWL_CONF)
        .returning(Success(Seq(ongoingCallParticipantNotificationObject)))

      (organizationDAO.getOrgIdFromTeamId)
        .expects(team_id)
        .returning(Success(org_id))

      (pusherService.sendMessageUsingPusher)
        .expects(*, *, *, *)
        .returning(Success("Pusher message"))

      val res = callService.handleProspectLeave(data = participantStatusCallbackData, teamId = team_id)

      res match {
        case Right(_) => assert(true)
        case Left(_) => assert(false)
      }

    }
  }

  describe("checkConferenceParticipants") {
    val conf_sid: ConferenceSid = ConferenceSid(sid = "conf_sid")

    it("should return Failure when callDAO sends a failure") {

      (callDAO.getRemainingParticipantDetails)
        .expects(conf_sid, team_id)
        .returning(Failure(new Exception("some failure")))

      val res = callService.checkConferenceParticipants(
        conferenceSid = conf_sid,
        teamId = team_id
      )

      res match {
        case Success(value) => assert(false)
        case Failure(exception) => assert(true)
      }
    }

    it("should return false when remaining participants includes customer and initiator") {

      val remainingParticipantsDetails: List[RemainingParticipantsDetails] = List(
        RemainingParticipantsDetails(
          latest_participation_mode = CallParticipationMode.InitiatorMode,
          participant_label = "customer"
        ),
        RemainingParticipantsDetails(
          latest_participation_mode = CallParticipationMode.InitiatorMode,
          participant_label = "initiator"
        )
      )

      (callDAO.getRemainingParticipantDetails)
        .expects(conf_sid, team_id)
        .returning(Success(remainingParticipantsDetails))

      val res = callService.checkConferenceParticipants(
        conferenceSid = conf_sid,
        teamId = team_id
      )

      res match {
        case Success(value) => assert(value == false)
        case Failure(exception) => assert(false)
      }
    }

    it("should return false when remaining participants is an empty list") {

      val remainingParticipantsDetails: List[RemainingParticipantsDetails] = List()

      (callDAO.getRemainingParticipantDetails)
        .expects(conf_sid, team_id)
        .returning(Success(remainingParticipantsDetails))

      val res = callService.checkConferenceParticipants(
        conferenceSid = conf_sid,
        teamId = team_id
      )

      res match {
        case Success(value) => assert(value == false)
        case Failure(exception) => assert(false)
      }
    }

    it("should return false when remaining participants includes customer and a barge-in participant") {

      val remainingParticipantsDetails: List[RemainingParticipantsDetails] = List(
        RemainingParticipantsDetails(
          latest_participation_mode = CallParticipationMode.InitiatorMode,
          participant_label = "customer"
        ),
        RemainingParticipantsDetails(
          latest_participation_mode = CallParticipationMode.BargeInMode,
          participant_label = "listener"
        )
      )

      (callDAO.getRemainingParticipantDetails)
        .expects(conf_sid, team_id)
        .returning(Success(remainingParticipantsDetails))

      val res = callService.checkConferenceParticipants(
        conferenceSid = conf_sid,
        teamId = team_id
      )

      res match {
        case Success(value) => assert(value == false)
        case Failure(exception) => assert(false)
      }
    }

    it("should return true when remaining participants includes customer and participants in listen mode") {

      val remainingParticipantsDetails: List[RemainingParticipantsDetails] = List(
        RemainingParticipantsDetails(
          latest_participation_mode = CallParticipationMode.InitiatorMode,
          participant_label = "customer"
        ),
        RemainingParticipantsDetails(
          latest_participation_mode = CallParticipationMode.ListenMode,
          participant_label = "initiator"
        )
      )

      (callDAO.getRemainingParticipantDetails)
        .expects(conf_sid, team_id)
        .returning(Success(remainingParticipantsDetails))

      val res = callService.checkConferenceParticipants(
        conferenceSid = conf_sid,
        teamId = team_id
      )

      res match {
        case Success(value) => assert(value == true)
        case Failure(exception) => assert(false)
      }
    }

    it("should return false when remaining participants includes customer and participant in whisper mode") {

      val remainingParticipantsDetails: List[RemainingParticipantsDetails] = List(
        RemainingParticipantsDetails(
          latest_participation_mode = CallParticipationMode.InitiatorMode,
          participant_label = "customer"
        ),
        RemainingParticipantsDetails(
          latest_participation_mode = CallParticipationMode.WhisperMode,
          participant_label = "initiator"
        )
      )

      (callDAO.getRemainingParticipantDetails)
        .expects(conf_sid, team_id)
        .returning(Success(remainingParticipantsDetails))

      val res = callService.checkConferenceParticipants(
        conferenceSid = conf_sid,
        teamId = team_id
      )

      res match {
        case Success(value) => assert(value == true)
        case Failure(exception) => assert(false)
      }
    }

    it("should return true when remaining participants only includes customer") {

      val remainingParticipantsDetails: List[RemainingParticipantsDetails] = List(
        RemainingParticipantsDetails(
          latest_participation_mode = CallParticipationMode.InitiatorMode,
          participant_label = "customer"
        )
      )

      (callDAO.getRemainingParticipantDetails)
        .expects(conf_sid, team_id)
        .returning(Success(remainingParticipantsDetails))

      val res = callService.checkConferenceParticipants(
        conferenceSid = conf_sid,
        teamId = team_id
      )

      res match {
        case Success(value) => assert(value == true)
        case Failure(exception) => assert(false)
      }
    }
  }

  describe("save call details tests") {

    it("should pass -> no task id passed") {

      val params: Map[String, String] = Map()

      (callDAO.saveConferenceDetails)
        .expects(*, *, *, *, *, *)
        .returning(Success(
          CallLog(
            conference_uuid = ConferenceUuid("uuid"),
            conference_sid = None,
            conference_name = None,
            initiated_by = PhoneNumber(phone_number = "+************"),
            service_provider = CallingServiceProvider.TWILIO,
            status = CallStatus.ACTIVE,
            task_uuid = TaskUuid("task_uuid"),
            primary_prospect_id = Some(ProspectId(163))
          )
        ))

      (() => srUuidUtils.generateConferenceUuid())
        .expects()
        .returning("some uuid")


      val res = callService.saveCallDetails(
        call_details = call_details,
        org_sub_account_uuid = org_sub_account_uuid,
        call_account_settings = call_account_settings,
        conferenceAndParticipantDetails = conferenceAndParticipantDetails,
        taskUuidAndProspectID = None,
        is_incoming = false
      )

      res match {
        case Success(value) => assert(true)

        case Failure(_) => assert(false)
      }

    }

    // irrelevant case after the change
    //    it("should pass -> task id passed but error while fetching task uuid and prospect id") {
    //
    //      (callDAO.getPrimaryProspectIdFromTask)
    //        .expects(*,*)
    //        .returning(Failure(new Exception("some error")))
    //
    //
    //      val res = callService.saveCallDetails(
    //        call_details = call_details,
    //        org_sub_account_uuid = org_sub_account_uuid,
    //        call_account_settings = call_account_settings,
    //        conferenceAndParticipantDetails = conferenceAndParticipantDetails,
    //        taskUuidAndProspectID = Some(TaskUuidAndProspectID()),
    //        is_incoming = false
    //      )
    //
    //      res match {
    //        case Success(_) => assert(false)
    //
    //        case Failure(_) => assert(true)
    //      }
    //
    //    }

    it("should pass -> no conference details present") {

      val call_details_temp = call_details.copy(
        call_participant_data = InitialCallParticipationModeData.ListenModeData(ConferenceUuid("some conf uuid"))
      )

      val conf_and_part_details = conferenceAndParticipantDetails.copy(
        conference_details = None
      )

      //      (callDAO.getPrimaryProspectIdFromTask)
      //        .expects(*, *)
      //        .returning(Success(TaskUuidAndProspectID(task_uuid = "task_uuid", prospect_id = ProspectId(3L))))


      val res = callService.saveCallDetails(
        call_details = call_details_temp,
        org_sub_account_uuid = org_sub_account_uuid,
        call_account_settings = call_account_settings,
        conferenceAndParticipantDetails = conf_and_part_details,
        taskUuidAndProspectID = Some(TaskUuidAndProspectID(task_uuid = "task_uuid", prospect_id = ProspectId(3L))),
        is_incoming = false
      )

      res match {
        case Success(value) => assert(true)

        case Failure(_) => assert(false)
      }

    }

    it("should fail -> no conference details present") {

      val call_details_temp = call_details.copy(
        call_participant_data = InitialCallParticipationModeData.ListenModeData(ConferenceUuid("some conf uuid"))
      )

      val conf_and_part_details = conferenceAndParticipantDetails.copy(
        conference_details = None
      )

      //      (callDAO.getPrimaryProspectIdFromTask)
      //        .expects(*, *)
      //        .returning(Success(TaskUuidAndProspectID(task_uuid = "task_uuid", prospect_id = ProspectId(3L))))


      val res = callService.saveCallDetails(
        call_details = call_details_temp,
        org_sub_account_uuid = org_sub_account_uuid,
        call_account_settings = call_account_settings,
        conferenceAndParticipantDetails = conf_and_part_details,
        taskUuidAndProspectID = Some(TaskUuidAndProspectID(task_uuid = "task_uuid", prospect_id = ProspectId(3L))),
        is_incoming = false
      )

      res match {
        case Success(value) => assert(true)

        case Failure(_) => assert(false)
      }

    }

    it("should pass -> successfully stored conference details") {

      val conf_and_part_details = conferenceAndParticipantDetails.copy(
        conference_details = None
      )

      //      (callDAO.getPrimaryProspectIdFromTask)
      //        .expects(*, *)
      //        .returning(Success(TaskUuidAndProspectID(task_uuid = "task_uuid", prospect_id = ProspectId(3L))))

      (callDAO.saveConferenceDetails)
        .expects(*, *, *, *, *, *)
        .returning(Success(CallLog(
          conference_uuid = ConferenceUuid("uuid"),
          conference_sid = None,
          conference_name = None,
          initiated_by = PhoneNumber(phone_number = "+************"),
          service_provider = CallingServiceProvider.TWILIO,
          status = CallStatus.ACTIVE,
          task_uuid = TaskUuid("task_uuid"),
          primary_prospect_id = Some(ProspectId(163))
        )))

      (() => srUuidUtils.generateConferenceUuid())
        .expects()
        .returning("some uuid")


      val res = callService.saveCallDetails(
        call_details = call_details,
        org_sub_account_uuid = org_sub_account_uuid,
        call_account_settings = call_account_settings,
        conferenceAndParticipantDetails = conferenceAndParticipantDetails,
        taskUuidAndProspectID = Some(TaskUuidAndProspectID(task_uuid = "task_uuid", prospect_id = ProspectId(3L))),
        is_incoming = false
      )

      res match {
        case Success(value) => assert(true)

        case Failure(_) => assert(false)
      }

    }


  }


  describe("getPricing api tests") {

    it("should return Left(GetNumberPriceError.GetNumberPriceApiError) when Twilio dialer service returns the error") {

      val country_code = "US"

      (twilioDialerService.getPricing(_: String))
        .expects(country_code)
        .returning(Left(GetNumberPriceError.GetNumberPriceApiError(new Exception("some error"))))

      val res = callService.getPricing(countryCode = country_code)

      assert(
        res match {
          case Left(error) => error match {
            case GetNumberPriceError.GetNumberPriceApiError(_) => true
            case GetNumberPriceError.NoPriceObjectFoundError(_) => false
            case GetNumberPriceError.ErrorWhileParsingJavaList(_) => false
          }
          case Right(_) => false
        })

    }

    it("should return Left(GetNumberPriceError.NoPriceObjectFoundError) when Twilio dialer service returns the error") {

      val country_code = "US"

      (twilioDialerService.getPricing(_: String))
        .expects(country_code)
        .returning(Left(GetNumberPriceError.NoPriceObjectFoundError("some error")))

      val res = callService.getPricing(countryCode = country_code)

      assert(
        res match {
          case Left(error) => error match {
            case GetNumberPriceError.GetNumberPriceApiError(_) => false
            case GetNumberPriceError.NoPriceObjectFoundError(_) => true
            case GetNumberPriceError.ErrorWhileParsingJavaList(_) => false
          }
          case Right(_) => false
        })

    }

    it("should return Left(GetNumberPriceError.ErrorWhileParsingJavaList) when Twilio dialer service returns the error") {

      val country_code = "US"

      (twilioDialerService.getPricing(_: String))
        .expects(country_code)
        .returning(Left(GetNumberPriceError.ErrorWhileParsingJavaList(new Exception("some error"))))

      val res = callService.getPricing(countryCode = country_code)

      assert(
        res match {
          case Left(error) => error match {
            case GetNumberPriceError.GetNumberPriceApiError(_) => false
            case GetNumberPriceError.NoPriceObjectFoundError(_) => false
            case GetNumberPriceError.ErrorWhileParsingJavaList(_) => true
          }
          case Right(_) => false
        })

    }

    it("should return Right(NumberPriceObject) when Twilio dialer service returns success") {

      val country_code = "US"

      (twilioDialerService.getPricing(_: String))
        .expects(country_code)
        .returning(Right(numberPriceObject))

      val res = callService.getPricing(countryCode = country_code)

      assert(
        res match {
          case Left(error) => error match {
            case GetNumberPriceError.GetNumberPriceApiError(_) => false
            case GetNumberPriceError.NoPriceObjectFoundError(_) => false
            case GetNumberPriceError.ErrorWhileParsingJavaList(_) => false

          }
          case Right(_: NumberPriceObject) => true
        })

    }


  }

  describe("delete number api tests") {
    val phone_sid: PhoneSID = PhoneSID(phone_sid = "phone_sid")
    val uuid: PhoneNumberUuid = PhoneNumberUuid(phone_number_uuid = "phone_number_uuid")
    val teamId: TeamId = TeamId(id = 3L)
    val exception = new Exception("some error")

    it("should fail when error while fetching phone sid") {

      (callDAO.getPhoneSIDFromUuid(_: PhoneNumberUuid, _: TeamId))
        .expects(uuid, teamId)
        .returning(Failure(exception))

      val res = callService.updateCallAccountToInActive(uuid = uuid, teamId = teamId, org_id = org_id)

      assert(
        res match {
          case Left(error) => error match {
            case DeleteNumberError.ErrorWhileFetchingPhoneSID(_) => true
            case DeleteNumberError.ErrorWhileDeletingFromTwilio(_) => false
            case DeleteNumberError.NumberCouldNotBeDeletedFromTwilio(_) => false
            case DeleteNumberError.ErrorWhileResettingCache(_) => false
            case DeleteNumberError.SQLExceptionWhileDeleting(_) => false
            case DeleteNumberError.AccountNotFoundError(_) => false
            case DeleteNumberError.ErrorWhileFetchingAccountFromDB(err) => false
          }
          case Right(_) => false
        }
      )

    }

    it("should fail when error while deleting number from twilio") {

      (callDAO.getPhoneSIDFromUuid(_: PhoneNumberUuid, _: TeamId))
        .expects(uuid, teamId)
        .returning(Success(Some(phone_sid)))

      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Success(Some(sub_account_details)))

      (twilioDialerService.deleteNumber(_: PhoneSID, _: TwlSubAccountSid, _: TwlAuthToken))
        .expects(phone_sid, twl_sub_account_sid, twl_sub_account_auth_token)
        .returning(Failure(exception))

      val res = callService.updateCallAccountToInActive(uuid = uuid, teamId = teamId, org_id = org_id)

      assert(
        res match {
          case Left(error) => error match {
            case DeleteNumberError.ErrorWhileFetchingPhoneSID(_) => false
            case DeleteNumberError.ErrorWhileDeletingFromTwilio(_) => true
            case DeleteNumberError.ErrorWhileResettingCache(_) => false
            case DeleteNumberError.NumberCouldNotBeDeletedFromTwilio(_) => false
            case DeleteNumberError.SQLExceptionWhileDeleting(_) => false
            case DeleteNumberError.AccountNotFoundError(_) => false
            case DeleteNumberError.ErrorWhileFetchingAccountFromDB(err) => false
          }
          case Right(_) => false
        }
      )

    }

    it("should fail when while deleting number from twilio returns false") {

      (callDAO.getPhoneSIDFromUuid(_: PhoneNumberUuid, _: TeamId))
        .expects(uuid, teamId)
        .returning(Success(Some(phone_sid)))
      (twilioDialerService.deleteNumber(_: PhoneSID, _: TwlSubAccountSid, _: TwlAuthToken))
        .expects(phone_sid, twl_sub_account_sid, twl_sub_account_auth_token)
        .returning(Success(false))

      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Success(Some(sub_account_details)))

      val res = callService.updateCallAccountToInActive(uuid = uuid, teamId = teamId, org_id = org_id)

      assert(
        res match {
          case Left(error) => error match {
            case DeleteNumberError.ErrorWhileFetchingPhoneSID(_) => false
            case DeleteNumberError.ErrorWhileDeletingFromTwilio(_) => false
            case DeleteNumberError.ErrorWhileResettingCache(_) => false
            case DeleteNumberError.NumberCouldNotBeDeletedFromTwilio(_) => true
            case DeleteNumberError.SQLExceptionWhileDeleting(_) => false
            case DeleteNumberError.AccountNotFoundError(_) => false
            case DeleteNumberError.ErrorWhileFetchingAccountFromDB(err) => false
          }
          case Right(_) => false
        }
      )

    }
    it("should fail when error while deleting number from database") {

      (callDAO.getPhoneSIDFromUuid(_: PhoneNumberUuid, _: TeamId))
        .expects(uuid, teamId)
        .returning(Success(Some(phone_sid)))
      (twilioDialerService.deleteNumber(_: PhoneSID, _: TwlSubAccountSid, _: TwlAuthToken))
        .expects(phone_sid, twl_sub_account_sid, twl_sub_account_auth_token)
        .returning(Success(true))

      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Success(Some(sub_account_details)))

      (callDAO.updateCallAccountStatusToInActive(_: PhoneNumberUuid, _: TeamId))
        .expects(uuid, teamId)
        .returning(Failure(exception))


      val res = callService.updateCallAccountToInActive(uuid = uuid, teamId = teamId, org_id = org_id)

      assert(
        res match {
          case Left(error) => error match {
            case DeleteNumberError.ErrorWhileFetchingPhoneSID(_) => false
            case DeleteNumberError.ErrorWhileDeletingFromTwilio(_) => false
            case DeleteNumberError.ErrorWhileResettingCache(_) => false
            case DeleteNumberError.NumberCouldNotBeDeletedFromTwilio(_) => false
            case DeleteNumberError.SQLExceptionWhileDeleting(_) => true
            case DeleteNumberError.AccountNotFoundError(_) => false
            case DeleteNumberError.ErrorWhileFetchingAccountFromDB(err) => false
          }
          case Right(_) => false
        }
      )

    }

    it("should fail when DAO layer returned a value other than 1") {

      (callDAO.getPhoneSIDFromUuid(_: PhoneNumberUuid, _: TeamId))
        .expects(uuid, teamId)
        .returning(Success(Some(phone_sid)))

      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Success(Some(sub_account_details)))

      (twilioDialerService.deleteNumber(_: PhoneSID, _: TwlSubAccountSid, _: TwlAuthToken))
        .expects(phone_sid, twl_sub_account_sid, twl_sub_account_auth_token)
        .returning(Success(true))
      (callDAO.updateCallAccountStatusToInActive(_: PhoneNumberUuid, _: TeamId))
        .expects(uuid, teamId)
        .returning(Success(3))


      val res = callService.updateCallAccountToInActive(uuid = uuid, teamId = teamId, org_id = org_id)

      assert(
        res match {
          case Left(error) => error match {
            case DeleteNumberError.ErrorWhileFetchingPhoneSID(_) => false
            case DeleteNumberError.ErrorWhileDeletingFromTwilio(_) => false
            case DeleteNumberError.ErrorWhileResettingCache(_) => false
            case DeleteNumberError.NumberCouldNotBeDeletedFromTwilio(_) => false
            case DeleteNumberError.SQLExceptionWhileDeleting(_) => false
            case DeleteNumberError.AccountNotFoundError(_) => true
            case DeleteNumberError.ErrorWhileFetchingAccountFromDB(err) => false
          }
          case Right(_) => false
        }
      )

    }

    it("should pass") {

      (callDAO.getPhoneSIDFromUuid(_: PhoneNumberUuid, _: TeamId))
        .expects(uuid, teamId)
        .returning(Success(Some(phone_sid)))

      (twilioDialerService.deleteNumber(_: PhoneSID, _: TwlSubAccountSid, _: TwlAuthToken))
        .expects(phone_sid, twl_sub_account_sid, twl_sub_account_auth_token)
        .returning(Success(true))

      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Success(Some(sub_account_details)))

      (callDAO.updateCallAccountStatusToInActive(_: PhoneNumberUuid, _: TeamId))
        .expects(uuid, teamId)
        .returning(Success(1))

      (organizationDAOService.resetOrgDataFromCache(
        _: OrgId,
        _: SrResetCacheInterval
      )(using _: SRLogger))
        .expects(org_id, SrResetCacheInterval.Immediately, logger)
        .returning(Success(true))


      val res = callService.updateCallAccountToInActive(uuid = uuid, teamId = teamId, org_id = org_id)

      assert(
        res match {
          case Left(error) => error match {
            case DeleteNumberError.ErrorWhileFetchingPhoneSID(_) => false
            case DeleteNumberError.ErrorWhileDeletingFromTwilio(_) => false
            case DeleteNumberError.ErrorWhileResettingCache(_) => false
            case DeleteNumberError.NumberCouldNotBeDeletedFromTwilio(_) => false
            case DeleteNumberError.SQLExceptionWhileDeleting(_) => false
            case DeleteNumberError.AccountNotFoundError(_) => false
            case DeleteNumberError.ErrorWhileFetchingAccountFromDB(err) => false
          }
          case Right(_) => true
        }
      )

    }

  }


  val call_details = CallDetails(
    is_incoming = false,
    from = PhoneNumber(
      phone_number = "+***********"
    ),
    to = PhoneNumber(
      phone_number = "+***********"
    ),
    call_sid = call_sid,
    calling_device = None,
    call_participant_data = InitialCallParticipationModeData.InitiatorModeData,
    call_type = CallType.TWL_CONF
  )
  val incoming_call_from = PhoneNumber(phone_number = "+***********")
  val outgoing_call_to = PhoneNumber(phone_number = "+91**********")
  val incoming_uri: Map[String, String] = Map(
    "ApiVersion" -> "2010-04-01",
    "From" -> s"%2B${incoming_call_from.phone_number.substring(1)}",
    "To" -> s"2B${incoming_call_to.phone_number.substring(1)}",
    "CallSid" -> s"${call_sid.sid}"
  )

  val outgoing_uri: Map[String, String] = Map(
    "From" -> s"client%3A${client_identity.identity}",
    "To" -> s"%2B${outgoing_call_to.phone_number.substring(1)}",
    "CallSid" -> s"${call_sid.sid}"
  )

  val sub_account_uuid = SubAccountUuid(
    uuid = "sub_account_Ssfskjecoise234*skdlfj"
  )
  val call_account_settings = CallAccountSettings(
    uuid = PhoneNumberUuid(
      phone_number_uuid = client_identity.identity
    ),
    phone_number = Some(PhoneNumber(
      phone_number = "+***********"
    )),
    first_name = "Shashank",
    last_name = "Dwivedi",
    owner_account_id = AccountId(
      id = 17
    ),
    team_id = TeamId(
      id = 15
    ),
    call_limit_per_day = 10,
    phone_type = Some(PhoneType.Local),
    country = Some("India"),
    enable_forwarding = false,
    forward_to = None,
    service_provider = CallingServiceProvider.TWILIO,
    org_sub_account_uuid = Some(sub_account_uuid),
    record_call = false,
    is_active = true,
    caller_id = None
  )
  
  val taskUuid = TaskUuid(
    "task_uuid"
  )

  val error = new Exception("Error while executing")


  val org_id = OrgId(
    id = 20
  )

  val account_id = AccountId(
    id = 13
  )

  val notificationEmailSendLog = EmailNotificationLog(
    notification_type = NotificationType.CallingFeatureSuspended,
    org_id = org_id,
    team_id = None,
    account_id = account_id,
    sent_at = DateTime.now().minusDays(2),
    sent_to_email_address = emailaddress
  )

  val twl_sub_account_sid = TwlSubAccountSid(
    id = "twl_sub_account_sid"
  )

  val twl_sub_account_auth_token = TwlAuthToken(
    token = "twl_sub_account_auth_token"
  )
  val sub_account_name = "Sub_account_name_hun_main"

  val org_sub_account_uuid = sub_account_uuid


  val sub_account_details = SubAccountDetails(
    uuid = sub_account_uuid,
    sub_account_id = twl_sub_account_sid,
    sub_auth_token = twl_sub_account_auth_token,
    sub_account_name = sub_account_name,
    call_remaining_credit_cents = 3000,
    call_credit_updated_at = None,
    credit_unit = CurrencyType.USD,
    previous_usage_deducted_cents = None,
    check_usage_from = DateTime.now(), // not currently in use
    trigger_name = None,
    trigger_webhook = None,
    trigger_recurring = None,
    trigger_usage_category = None,
    trigger_last_fired = None,
    created_at = DateTime.now(),
    updated_at = DateTime.now(),
    org_id = org_id,
    status = SubAccountStatus.Active,
    call_credits_cents = 3000,
    trigger_id = None,
    trigger_value = None,
    trigger_by = None,
    already_subtracted_cents = Some(250),
    twl_idempotency_token = None,
    twl_trigger_current_value_cents = None,
    twl_api_key = ApiKey(key = "api-key"),
    twl_secret_key = ApiSecret(secret = "api-secret"),
    twl_twiml_app_sid = TwlTwimlAppSid(sid = "app-sid"),
    twl_twiml_app_voice_url = "voice-url",
    twl_twiml_app_name = TwlTwimlAppName(name = "app-name"),
    last_call_history_updated_at = None
  )

  val twl_usage_details = UsageDetails(
    sub_account_sid = sub_account_details.sub_account_id,
    total_usage_in_cents = 250,
    usage_unit = CurrencyType.USD,
    usage_from = DateTime.now(),
    usage_till = DateTime.now(

    )
  )

  val participant_details_to_notify: ParticipantDetailsToNotify = ParticipantDetailsToNotify(
    participantUuid = CallParticipantUuid("some participant uuid"),
    conferenceUuid = ConferenceUuid("some conf uuid"),
    latest_participation_mode = CallParticipationMode.InitiatorMode,
    prospectId = Some(ProspectId(3L)),
    accountId = Some(AccountId(3L))
  )

  val prospect_details: OngoingCallProspectDetails = OngoingCallProspectDetails(
    prospect_id = ProspectId(3L),
    first_name = Some("ABC"),
    last_name = Some("XYZ"),
    phone_number = Some("+************")
  )

  val ongoingCallParticipantNotificationObject: OngoingCallParticipantNotificationObject = OngoingCallParticipantNotificationObject(
    participant_uuid = CallParticipantUuid("some participant uuid"),
    conference_uuid = conference_uuid,
    participant_account_id = Some(AccountId(3L)),
    ongoingCallProspectDetails = Some(prospect_details),
    task_id = Some("task uuid"),
    latest_participation_mode = CallParticipationMode.InitiatorMode,
    participant_first_name = Some("FirstName"),
    participant_last_name = Some("LastName"),
    calling_device = None,
    call_sid = None
  )

  val subaccountTwilmlApplication = SubAccountTwimlApplication(
    app_sid = TwlTwimlAppSid(sid = "app-sid"),
    app_name = TwlTwimlAppName(name = "app-name"),
    voice_url = "voice-url"
  )

  val subAccountApiKeyAndSecret = SubAccountApiKeyAndSecret(
    api_key = ApiKey(key = "api-key"),
    api_secret = ApiSecret(secret = "api-secret")

  )

  given logger: SRLogger = new SRLogger("[CallServiceSpec] LoggerID")

  describe("testing CallDetails.getCallFromAndToFromURI") {

    it("should return error, when from is not defined ") {

      val result = CallDetails.getCallDetailsFromParams(
        params = Map()
      )

      assert(result == Left(GetCallDetailsError.FromNotFound)) // checking if its empty
    }

    it("should return error, when to is not defined ") {

      val result = CallDetails.getCallDetailsFromParams(
        params = Map("From" -> "+91")
      )

      assert(result == Left(GetCallDetailsError.ToNotFound)) // checking if its empty
    }

    it("should return is_incoming = true") {

      val result = CallDetails.getCallDetailsFromParams(
        params = incoming_uri
      )

      result match {
        case Left(err) =>
          assert(false)

        case Right(details) =>
          assert(details.is_incoming)
      }

    }

    it("should return is_incoming = false") {

      val result = CallDetails.getCallDetailsFromParams(
        params = outgoing_uri
      )

      result match {
        case Left(err) =>
          assert(false)

        case Right(details) =>
          assert(!details.is_incoming)
      }

    }

    it("should return error when call_type is whisper") {

      val outgoing_uri: Map[String, String] = Map(
        "From" -> s"client%3A${client_identity.identity}",
        "To" -> s"%2B${outgoing_call_to.phone_number.substring(1)}",
        "CallSid" -> s"${call_sid.sid}",
        "CallType" -> "whisper"
      )

      val result = CallDetails.getCallDetailsFromParams(
        params = outgoing_uri
      )

      result match {
        case Left(err) =>
          assert(true)

        case Right(details) =>
          assert(false)
      }

    }

    it("should return error when call_type is barge-in") {

      val outgoing_uri: Map[String, String] = Map(
        "From" -> s"client%3A${client_identity.identity}",
        "To" -> s"%2B${outgoing_call_to.phone_number.substring(1)}",
        "CallSid" -> s"${call_sid.sid}",
        "CallType" -> "barge-in"
      )

      val result = CallDetails.getCallDetailsFromParams(
        params = outgoing_uri
      )

      result match {
        case Left(err) =>
          assert(true)

        case Right(details) =>
          assert(false)
      }

    }

  }


  describe("testing CallService.getCallParticipationModeAndData") {

    it("listen - should fail when not conference_uuid not found") {

      val result = CallParticipationModeData.getInitialCallParticipationModeAndData(
        parsed_params = Map(),
        call_type = CallParticipationMode.ListenMode.toString
      )

      assert(result == Left(GetCallDetailsError.ConferenceUuidNotFound))
    }

    it("whisper - should fail when not conference_uuid not found") {

      val result = CallParticipationModeData.getInitialCallParticipationModeAndData(
        parsed_params = Map(),
        call_type = CallParticipationMode.WhisperMode.toString
      )

      result match {
        case Right(_) => assert(false)
        case Left(_) => assert(true)
      }


    }

    it("barge-in - should fail when not conference_uuid not found") {

      val result = CallParticipationModeData.getInitialCallParticipationModeAndData(
        parsed_params = Map(),
        call_type = CallParticipationMode.BargeInMode.toString
      )

      result match {
        case Right(_) => assert(false)
        case Left(_) => assert(true)
      }
    }

    it("whisper - should fail when not initiator_call_sid not found") {

      val result = CallParticipationModeData.getInitialCallParticipationModeAndData(
        parsed_params = Map("conference_uuid" -> " "),
        call_type = CallParticipationMode.WhisperMode.toString
      )

      result match {
        case Right(_) => assert(false)
        case Left(_) => assert(true)
      }
    }

    it("bargein - should fail when not initiator_call_sid not found") {

      val result = CallParticipationModeData.getInitialCallParticipationModeAndData(
        parsed_params = Map("conference_uuid" -> " "),
        call_type = CallParticipationMode.BargeInMode.toString
      )

      result match {
        case Right(_) => assert(false)
        case Left(_) => assert(true)
      }
    }

    it("whisper - should fail when not call_sid_to_coach not found") {

      val result = CallParticipationModeData.getInitialCallParticipationModeAndData(
        parsed_params = Map("conference_uuid" -> " ", "initiator_call_sid" -> " "),
        call_type = CallParticipationMode.WhisperMode.toString
      )

      result match {
        case Right(_) => assert(false)
        case Left(_) => assert(true)
      }
    }

    //-- success cases -- //

    //

    it("listen - should success") {

      val result = CallParticipationModeData.getInitialCallParticipationModeAndData(
        parsed_params = Map("conference_uuid" -> "cnf_u"),
        call_type = CallParticipationMode.ListenMode.toString
      )

      assert(result == Right(InitialCallParticipationModeData.ListenModeData(conference_uuid = ConferenceUuid(conf_uuid = "cnf_u"))))
    }

    it("whisper - should fail") {

      val result = CallParticipationModeData.getInitialCallParticipationModeAndData(
        parsed_params = Map("conference_uuid" -> "cnf_u", "initiator_call_sid" -> "ini_call_sid", "call_sid_to_coach" -> "call_coach"),
        call_type = CallParticipationMode.WhisperMode.toString
      )

      result match {
        case Right(_) => assert(false)
        case Left(_) => assert(true)
      }
    }

    it("barge-in - should fail") {

      val result = CallParticipationModeData.getInitialCallParticipationModeAndData(
        parsed_params = Map("conference_uuid" -> "cnf_u", "initiator_call_sid" -> "ini_call_sid", "call_sid_to_coach" -> "call_coach"),
        call_type = CallParticipationMode.BargeInMode.toString
      )

      result match {
        case Right(_) => assert(false)
        case Left(_) => assert(true)
      }
    }


  }


  describe("testing CallService.handleCall outgoing calls") {

    it("should go to outgoing call and fail as Dao call fails") {

      (callDAO.getCallSettingDataFromCallerIdentity(_))
        .expects(PhoneNumberUuid(phone_number_uuid = client_identity.identity))
        .returning(Failure(error))


      val result = callService.handleCall(
        validate_webhook_data = validate_webhook_data
      )

      result match {
        case Left(HandleCallError.SQLExceptionError(err)) =>
          assert(err.getMessage == error.getMessage)

        case _ =>

          assert(false)
      }

      //      assert(result. == Left(HandleCallError.SQLExceptionError(error)))
    }

    it("should go to outgoing call and fail as Dao call returns None") {

      (callDAO.getCallSettingDataFromCallerIdentity)
        .expects(*)
        .returning(Success(None))


      val result = callService.handleCall(
        validate_webhook_data = validate_webhook_data
      )

      result match {
        case Left(HandleCallError.NoSettingIdFoundError) =>
          assert(true)

        case _ =>

          assert(false)
      }

      //      assert(result. == Left(HandleCallError.SQLExceptionError(error)))
    }

    it("should go to outgoing call and fail as twilioService fails") {

      (callDAO.getCallSettingDataFromCallerIdentity)
        .expects(PhoneNumberUuid(phone_number_uuid = client_identity.identity))
        .returning(Success(Some(call_account_settings)))

      (callDAO.getSubAccountDetailsForOrgUsingSubAccountUuid)
        .expects(sub_account_uuid)
        .returning(Success(Some(sub_account_details)))

      (twilioDialerService.authenticateTwilioWebhook(_: TwlAuthToken, _: Map[String, String], _: String, _: String)(using _: SRLogger))
        .expects(twl_sub_account_auth_token, outgoing_params, webhook_url, twl_signature, *)
        .returning(Success(true))

      (twilioDialerService.handleOutGoingCall(_: TwlSubAccountSid, _: TwlAuthToken, _: CallDetails, _: String, _: Boolean, _: TeamId, _: Option[ConferenceFriendlyName])(using _: SRLogger))
        .expects(
          sub_account_details_found.sub_account_sid,
          sub_account_details_found.sub_auth_token,
          call_details.copy(
            from = PhoneNumber(phone_number = client_identity.identity),
            to = PhoneNumber(phone_number = "+91**********"),
            call_type = CallType.TWL_CONF
          ), call_details.from.phone_number, false,
          call_account_settings.team_id,
          *,
          *
        )
        .returning(Failure(error))


      val result = callService.handleCall(
        validate_webhook_data = validate_webhook_data
      )

      result match {
        case Left(HandleCallError.TwilioServiceError(err)) =>
          assert(err.getMessage == error.getMessage)

        case _ =>

          assert(false)
      }

      //      assert(result. == Left(HandleCallError.SQLExceptionError(error)))
    }

    it("should go to outgoing call and success as twilioService response success") {

      val dialer_service_result: TwilioConferenceAndParticipantDetails = TwilioConferenceAndParticipantDetails(
        conference_details = Some(ConferenceDetailsFromWebhook(
          conference_sid = Some("conference_sid"),
          conference_name = Some("test_conference"),
          initiated_by = PhoneNumber("+************"),
          service_provider = CallingServiceProvider.TWILIO,
          callType = CallType.TWL_CONF
        )),
        participantDetails = Seq(ParticipantDetailsFromWebhook(
          call_from = PhoneNumber("+************"),
          participant_label = "initiator",
          call_participant_phone = PhoneNumber("+************"),
          is_prospect = false,
          call_sid = Some(CallSID(sid = "call_Sid"))
        )),
        response = "Response aarha hai"
      )


      (callDAO.getCallSettingDataFromCallerIdentity)
        .expects(PhoneNumberUuid(phone_number_uuid = client_identity.identity))
        .returning(Success(Some(call_account_settings)))

      

      (twilioDialerService.handleOutGoingCall(_: TwlSubAccountSid, _: TwlAuthToken, _: CallDetails, _: String, _: Boolean, _: TeamId, _: Option[ConferenceFriendlyName])(using _: SRLogger))
        .expects(
          sub_account_details_found.sub_account_sid,
          sub_account_details_found.sub_auth_token,
          call_details.copy(
            from = PhoneNumber(phone_number = client_identity.identity),
            to = PhoneNumber(phone_number = "+91**********")
          ), call_details.from.phone_number, false,
          call_account_settings.team_id,
          *,
          *
        )
        .returning(Success(dialer_service_result))

      (callDAO.getSubAccountDetailsForOrgUsingSubAccountUuid)
        .expects(sub_account_uuid)
        .returning(Success(Some(sub_account_details)))

      (callDAO.getPrimaryProspectIdFromTask)
        .expects(*, *)
        .returning(Success(TaskUuidAndProspectID(task_uuid = taskUuid.uuid, prospect_id = ProspectId(3L))))

      (callDAO.updateDoneByForCallTask)
        .expects(call_account_settings.owner_account_id,
          taskUuid,
          call_account_settings.team_id)
        .returning(Success(1))

      (prospectDAO.getProspectDetailsForPusherNotification)
        .expects(*, *)
        .returning(Success(prospect_details))


      (twilioDialerService.authenticateTwilioWebhook(_: TwlAuthToken, _: Map[String, String], _: String, _: String)(using _: SRLogger))
        .expects(twl_sub_account_auth_token, outgoing_params, webhook_url, twl_signature, *)
        .returning(Success(true))

      (() => srUuidUtils.generateConferenceUuid())
        .expects()
        .returning("conf_123")

      (() => srUuidUtils.generateParticipantUuid())
        .expects()
        .returning("part_123")

      (callDAO.saveParticipantDetails)
        .expects(*, *, *, *, *, *, *, *, *)
        .returning(Success(participant_details_to_notify))

      (callDAO.saveConferenceDetails)
        .expects(*, *, *, *, *, *)
        .returning(Success(CallLog(
          conference_uuid = ConferenceUuid("uuid"),
          conference_sid = None,
          conference_name = None,
          initiated_by = PhoneNumber(phone_number = "+************"),
          service_provider = CallingServiceProvider.TWILIO,
          status = CallStatus.ACTIVE,
          task_uuid = TaskUuid("task_uuid"),
          primary_prospect_id = Some(ProspectId(163))
        )))
      (prospectAddEventDAO.addEvents)
        .expects(*)
        .returning(Success(List()))
      (pusherService.sendMessageUsingPusher)
        .expects(*, *, *, *)
        .returning(Success("Pusher message"))


      val result = callService.handleCall(
        validate_webhook_data = validate_webhook_data
      )

      result match {
        case Right(response) =>
          assert(response == "Response aarha hai")

        case res =>
          println(s"${res.toString}, ${result.toString}")

          assert(false)
      }

      //      assert(result. == Left(HandleCallError.SQLExceptionError(error)))
    }

    it("should go to outgoing call and success as twilioService response success but for listen mode so no prospect event created") {

      val outgoing_params_with_call = Map("From" -> s"client:${client_identity.identity}", "To" -> "+91**********", "CallSid" -> "Hi_I'm Call sid", "task_id" -> "3", "CallType" -> "listen", "conference_uuid" -> s"${conference_uuid}")

      val conference_friendly_name = ConferenceFriendlyName(name = "conf_friendly_name")

      val dialer_service_result: TwilioConferenceAndParticipantDetails = TwilioConferenceAndParticipantDetails(
        conference_details = Some(ConferenceDetailsFromWebhook(
          conference_sid = Some("conference_sid"),
          conference_name = Some("test_conference"),
          initiated_by = PhoneNumber("+************"),
          service_provider = CallingServiceProvider.TWILIO,
          callType = CallType.TWL_CONF
        )),
        participantDetails = Seq(ParticipantDetailsFromWebhook(
          call_from = PhoneNumber("+************"),
          participant_label = "initiator",
          call_participant_phone = PhoneNumber("+************"),
          is_prospect = false,
          call_sid = Some(CallSID(sid = "call_Sid"))
        )),
        response = "Response aarha hai"
      )


      (callDAO.getCallSettingDataFromCallerIdentity)
        .expects(PhoneNumberUuid(phone_number_uuid = client_identity.identity))
        .returning(Success(Some(call_account_settings)))

      (callDAO.getSubAccountDetailsForOrgUsingSubAccountUuid)
        .expects(sub_account_uuid)
        .returning(Success(Some(sub_account_details)))

      (callDAO.getConferenceNameFromConferenceUuid)
        .expects(conference_uuid, call_account_settings.team_id)
        .returning(Success(Some(conference_friendly_name)))

      (twilioDialerService.handleOutGoingCall(_: TwlSubAccountSid, _: TwlAuthToken, _: CallDetails, _: String, _: Boolean, _: TeamId, _: Option[ConferenceFriendlyName])(using _: SRLogger))
        .expects(
          sub_account_details_found.sub_account_sid,
          sub_account_details_found.sub_auth_token,
          call_details.copy(
            from = PhoneNumber(phone_number = client_identity.identity),
            to = PhoneNumber(phone_number = "+91**********"),
            call_participant_data = InitialCallParticipationModeData.ListenModeData(
              conference_uuid = conference_uuid
            )
          ), call_details.from.phone_number, false,
          call_account_settings.team_id,
          *,
          *
        )
        .returning(Success(dialer_service_result))


      (callDAO.getPrimaryProspectIdFromTask)
        .expects(*, *)
        .returning(Success(TaskUuidAndProspectID(task_uuid = "task_uuid", prospect_id = ProspectId(3L))))

      (prospectDAO.getProspectDetailsForPusherNotification)
        .expects(*, *)
        .returning(Success(prospect_details))


      (twilioDialerService.authenticateTwilioWebhook(_: TwlAuthToken, _: Map[String, String], _: String, _: String)(using _: SRLogger))
        .expects(twl_sub_account_auth_token, outgoing_params_with_call, webhook_url, twl_signature, *)
        .returning(Success(true))

      (() => srUuidUtils.generateConferenceUuid())
        .expects()
        .returning("conf_123")

      (() => srUuidUtils.generateParticipantUuid())
        .expects()
        .returning("part_123")

      (callDAO.saveParticipantDetails)
        .expects(*, *, *, *, *, *, *, *, *)
        .returning(Success(participant_details_to_notify))

      (callDAO.saveConferenceDetails)
        .expects(*, *, *, *, *, *)
        .returning(Success(CallLog(
          conference_uuid = ConferenceUuid("uuid"),
          conference_sid = None,
          conference_name = None,
          initiated_by = PhoneNumber(phone_number = "+************"),
          service_provider = CallingServiceProvider.TWILIO,
          status = CallStatus.ACTIVE,
          task_uuid = TaskUuid("task_uuid"),
          primary_prospect_id = Some(ProspectId(163))
        )))
      //      (prospectAddEventDAO.addEvents)
      //        .expects(*)
      //        .returning(List())
      (pusherService.sendMessageUsingPusher)
        .expects(*, *, *, *)
        .returning(Success("Pusher message"))


      val result = callService.handleCall(
        validate_webhook_data = validate_webhook_data.copy(
          params = outgoing_params_with_call
        )
      )

      result match {
        case Right(response) =>
          assert(response == "Response aarha hai")

        case res =>
          println(s"${res.toString}, ${result.toString}")

          assert(false)
      }

      //      assert(result. == Left(HandleCallError.SQLExceptionError(error)))
    }

    it("should go to outgoing call and fail as prospectDAO.getProspectDetailsForPusherNotification fails") {

      val dialer_service_result: TwilioConferenceAndParticipantDetails = TwilioConferenceAndParticipantDetails(
        conference_details = Some(ConferenceDetailsFromWebhook(
          conference_sid = Some("conference_sid"),
          conference_name = Some("test_conference"),
          initiated_by = PhoneNumber("+************"),
          service_provider = CallingServiceProvider.TWILIO,
          callType = CallType.TWL_CONF
        )),
        participantDetails = Seq(ParticipantDetailsFromWebhook(
          call_from = PhoneNumber("+************"),
          participant_label = "initiator",
          call_participant_phone = PhoneNumber("+************"),
          is_prospect = false,
          call_sid = Some(CallSID(sid = "call_Sid"))
        )),
        response = "Response aarha hai"
      )


      (callDAO.getCallSettingDataFromCallerIdentity)
        .expects(PhoneNumberUuid(phone_number_uuid = client_identity.identity))
        .returning(Success(Some(call_account_settings)))

      (twilioDialerService.handleOutGoingCall(_: TwlSubAccountSid, _: TwlAuthToken, _: CallDetails, _: String, _: Boolean, _: TeamId, _: Option[ConferenceFriendlyName])(using _: SRLogger))
        .expects(
          sub_account_details_found.sub_account_sid,
          sub_account_details_found.sub_auth_token,
          call_details.copy(
            from = PhoneNumber(phone_number = client_identity.identity),
            to = PhoneNumber(phone_number = "+91**********")
          ), call_details.from.phone_number, false,
          call_account_settings.team_id,
          *,
          *
        )
        .returning(Success(dialer_service_result))

      (callDAO.getSubAccountDetailsForOrgUsingSubAccountUuid)
        .expects(sub_account_uuid)
        .returning(Success(Some(sub_account_details)))

      (callDAO.getPrimaryProspectIdFromTask)
        .expects(*, *)
        .returning(Success(TaskUuidAndProspectID(task_uuid = taskUuid.uuid, prospect_id = ProspectId(3L))))

      (callDAO.updateDoneByForCallTask)
        .expects(call_account_settings.owner_account_id,
          taskUuid,
          call_account_settings.team_id)
        .returning(Success(1))

      (prospectDAO.getProspectDetailsForPusherNotification)
        .expects(*, *)
        .returning(Failure(new Exception("some error")))


      (twilioDialerService.authenticateTwilioWebhook(_: TwlAuthToken, _: Map[String, String], _: String, _: String)(using _: SRLogger))
        .expects(twl_sub_account_auth_token, outgoing_params, webhook_url, twl_signature, *)
        .returning(Success(true))

      (() => srUuidUtils.generateConferenceUuid())
        .expects()
        .returning("conf_123")

      (() => srUuidUtils.generateParticipantUuid())
        .expects()
        .returning("part_123")

      (callDAO.saveParticipantDetails)
        .expects(*, *, *, *, *, *, *, *, *)
        .returning(Success(participant_details_to_notify))

      (callDAO.saveConferenceDetails)
        .expects(*, *, *, *, *, *)
        .returning(Success(CallLog(
          conference_uuid = ConferenceUuid("uuid"),
          conference_sid = None,
          conference_name = None,
          initiated_by = PhoneNumber(phone_number = "+************"),
          service_provider = CallingServiceProvider.TWILIO,
          status = CallStatus.ACTIVE,
          task_uuid = TaskUuid("task_uuid"),
          primary_prospect_id = Some(ProspectId(163))
        )))


      val result = callService.handleCall(
        validate_webhook_data = validate_webhook_data
      )

      result match {
        case Left(HandleCallError.ErrorWhileSavingCallingDetails(_)) =>
          assert(true)

        case _ =>
          //          println(s"${res.toString}, ${result.toString}")

          assert(false)
      }

      //      assert(result. == Left(HandleCallError.SQLExceptionError(error)))
    }

  }


  describe("testing CallService.handleCall incoming calls") {

    val prospectDetails: ProspectDetailsForIncomingCalls = ProspectDetailsForIncomingCalls(
      prospectId = None,
      prospectNotes = None
    )

    it("should go to incoming calls and fail and DAO layer fails") {

      (callDAO.getCallSettingDataFromPhoneNumber)
        .expects(incoming_call_to)
        .returning(Failure(error))

      //      (twilioDialerService.handleIncomingCall)
      //        .expects(
      //          call_details.copy(
      //            to = Some("+" + incoming_call_to),
      //            is_incoming = true
      //          ),
      //          CallerIdentity(
      //            identity = client_identity
      //          ),
      //          call_account_settings.enable_forwarding,
      //          call_account_settings.forward_to)
      //        .returning(Success("Response aarha hai"))


      val result = callService.handleCall(
        validate_webhook_data = validate_webhook_data.copy(params = incoming_params)
      )

      result match {
        case Left(HandleCallError.SQLExceptionError(err)) =>
          assert(err.getMessage == error.getMessage)

        case _ =>

          assert(false)
      }

      //      assert(result. == Left(HandleCallError.SQLExceptionError(error)))
    }

    it("should go to incoming calls and fail and DAO layer returns None") {

      (callDAO.getCallSettingDataFromPhoneNumber)
        .expects(incoming_call_to)
        .returning(Success(None))

      //      (twilioDialerService.handleIncomingCall)
      //        .expects(
      //          call_details.copy(
      //            to = Some("+" + incoming_call_to),
      //            is_incoming = true
      //          ),
      //          CallerIdentity(
      //            identity = client_identity
      //          ),
      //          call_account_settings.enable_forwarding,
      //          call_account_settings.forward_to)
      //        .returning(Success("Response aarha hai"))


      val result = callService.handleCall(
        validate_webhook_data = validate_webhook_data.copy(params = incoming_params)
      )

      result match {
        case Left(HandleCallError.NoSettingIdFoundError) =>
          assert(true)

        case _ =>

          assert(false)
      }

      //      assert(result. == Left(HandleCallError.SQLExceptionError(error)))
    }


    it("should go to incoming calls and fail as twilio fail") {

      (callDAO.getCallSettingDataFromPhoneNumber)
        .expects(incoming_call_to)
        .returning(Success(Some(call_account_settings)))

      (callDAO.getSubAccountDetailsForOrgUsingSubAccountUuid)
        .expects(sub_account_uuid)
        .returning(Success(Some(sub_account_details)))

      (twilioDialerService.authenticateTwilioWebhook(_: TwlAuthToken, _: Map[String, String], _: String, _: String)(using _: SRLogger))
        .expects(twl_sub_account_auth_token, incoming_params, webhook_url, twl_signature, *)
        .returning(Success(true))


      (accountDAO.getOwnerAccountIdByOrgId)
        .expects(org_id)
        .returning(Success(Some(account_id)))

      (organizationDAO.getOrgIdFromTeamId)
        .expects(call_account_settings.team_id)
        .returning(Success(org_id))

      (emailNotificationService.sendIncomingCallReceivedMail(
        _: AccountId,
        _: AccountId,
        _: Option[Try[Seq[ProspectObject]]],
        _: PhoneNumber,
        _: PhoneNumber)
      (_: WSClient,
        _: ExecutionContext,
        _: SRLogger))

        .expects(account_id, call_account_settings.owner_account_id, None, incoming_call_to, incoming_call_from.copy(phone_number = "+91**********"), *, *, *)
        .returning(Success(println("hi")))

      (prospectDAOSErvice.getProspectDetailsForIncomingCallFromNumber)
        .expects(
          incoming_call_from.copy(phone_number = "**********"),
          call_account_settings.team_id)
        .returning(Success(prospectDetails))


      (twilioDialerService.handleIncomingCall(_: CallerIdentity, _: Boolean, _: Option[String], _: CallDetails, _: TwlSubAccountSid, _: Boolean, _: TeamId, _: TwlAuthToken, _: OrgId)(using _: SRLogger))
        .expects(
          client_identity,
          call_account_settings.enable_forwarding,
          call_account_settings.forward_to,
          call_details.copy(is_incoming = true, from = PhoneNumber("+91**********")),
          twl_sub_account_sid,
          false,
          call_account_settings.team_id,
          twl_sub_account_auth_token,
          org_id,
          logger
        )
        .returning(Failure(error))


      val result = callService.handleCall(
        validate_webhook_data = validate_webhook_data.copy(params = incoming_params)
      )

      result match {
        case Left(HandleCallError.TwilioServiceError(err)) =>
          assert(err.getMessage == error.getMessage)

        case d =>
          println(s"errorr : ${d}")


          assert(false)
      }

      //      assert(result. == Left(HandleCallError.SQLExceptionError(error)))
    }

    it("should go to incoming calls and don't add any Call_RECIEVED prospect event when its listen mode and success") {

      val incoming_call_param = Map("From" -> "+91**********", "To" -> s"${incoming_call_to}", "CallSid" -> "Hi_I'm Call sid", "CallType" -> "listen", "conference_uuid" -> s"${conference_uuid}")

      val dialer_service_result: TwilioConferenceAndParticipantDetails = TwilioConferenceAndParticipantDetails(
        conference_details = Some(ConferenceDetailsFromWebhook(
          conference_sid = Some("conference_sid"),
          conference_name = Some("test_conference"),
          initiated_by = PhoneNumber("+************"),
          service_provider = CallingServiceProvider.TWILIO,
          callType = CallType.TWL_CONF
        )),
        participantDetails = Seq(ParticipantDetailsFromWebhook(
          call_from = PhoneNumber("+************"),
          participant_label = "initiator",
          call_participant_phone = PhoneNumber("+************"),
          is_prospect = false,
          call_sid = Some(CallSID(sid = "call_Sid"))
        )),
        response = "Response aarha hai"
      )

      (callDAO.getCallSettingDataFromPhoneNumber)
        .expects(incoming_call_to)
        .returning(Success(Some(call_account_settings)))

      (callDAO.getSubAccountDetailsForOrgUsingSubAccountUuid)
        .expects(sub_account_uuid)
        .returning(Success(Some(sub_account_details)))

      (twilioDialerService.authenticateTwilioWebhook(_: TwlAuthToken, _: Map[String, String], _: String, _: String)(using _: SRLogger))
        .expects(twl_sub_account_auth_token, incoming_call_param, webhook_url, twl_signature, *)
        .returning(Success(true))

      /* (() => srUuidUtils.generateParticipantUuid())
         .expects()
         .returning("part_123")

       (callDAO.saveParticipantDetails)
         .expects(*, *, *, *, *, *)
         .returning(Success(participant_details_to_notify))

       (prospectDAO.getProspectDetailsForPusherNotification)
         .expects(*, *)
         .returning(Success(prospect_details))*/
      (prospectDAOSErvice.getProspectDetailsForIncomingCallFromNumber)
        .expects(
          incoming_call_from.copy(phone_number = "**********"),
          call_account_settings.team_id)
        .returning(Success(prospectDetails))


      (accountDAO.getOwnerAccountIdByOrgId)
        .expects(org_id)
        .returning(Success(Some(account_id)))

      (organizationDAO.getOrgIdFromTeamId)
        .expects(call_account_settings.team_id)
        .returning(Success(org_id))

      (emailNotificationService.sendIncomingCallReceivedMail(
        _: AccountId,
        _: AccountId,
        _: Option[Try[Seq[ProspectObject]]],
        _: PhoneNumber,
        _: PhoneNumber)
      (_: WSClient,
        _: ExecutionContext,
        _: SRLogger))

        .expects(account_id, call_account_settings.owner_account_id, None, incoming_call_to, incoming_call_from.copy(phone_number = "+91**********"), *, *, *)
        .returning(Success(println("hi")))

      (twilioDialerService.handleIncomingCall(_: CallerIdentity, _: Boolean, _: Option[String], _: CallDetails, _: TwlSubAccountSid, _: Boolean, _: TeamId, _: TwlAuthToken, _: OrgId)(using _: SRLogger))
        .expects(
          client_identity,
          call_account_settings.enable_forwarding,
          call_account_settings.forward_to,
          call_details.copy(
            is_incoming = true,
            from = PhoneNumber("+91**********"),
            call_participant_data = InitialCallParticipationModeData.ListenModeData(
              conference_uuid = conference_uuid
            )
          ),
          twl_sub_account_sid,
          false,
          call_account_settings.team_id,
          twl_sub_account_auth_token,
          org_id,
          logger
        )
        .returning(Success(dialer_service_result))

      /*(() => srUuidUtils.generateConferenceUuid())
        .expects()
        .returning("conf_123")

      (callDAO.saveConferenceDetails)
        .expects(*, *, *, *, *, *, *)
        .returning(Success(ConferenceUuid("conf_123")))*/


      val result = callService.handleCall(
        validate_webhook_data = validate_webhook_data.copy(params = incoming_call_param)
      )

      result match {
        case Right(response) =>
          assert(response == "Response aarha hai")

        case _ =>

          assert(false)
      }

      //      assert(result. == Left(HandleCallError.SQLExceptionError(error)))
    }

    it("should go to incoming calls and success as twilioService response success") {

      val dialer_service_result: TwilioConferenceAndParticipantDetails = TwilioConferenceAndParticipantDetails(
        conference_details = Some(ConferenceDetailsFromWebhook(
          conference_sid = Some("conference_sid"),
          conference_name = Some("test_conference"),
          initiated_by = PhoneNumber("+************"),
          service_provider = CallingServiceProvider.TWILIO,
          callType = CallType.TWL_CONF
        )),
        participantDetails = Seq(ParticipantDetailsFromWebhook(
          call_from = PhoneNumber("+************"),
          participant_label = "initiator",
          call_participant_phone = PhoneNumber("+************"),
          is_prospect = false,
          call_sid = Some(CallSID(sid = "call_Sid"))
        )),
        response = "Response aarha hai"
      )

      (callDAO.getCallSettingDataFromPhoneNumber)
        .expects(incoming_call_to)
        .returning(Success(Some(call_account_settings)))

      (callDAO.getSubAccountDetailsForOrgUsingSubAccountUuid)
        .expects(sub_account_uuid)
        .returning(Success(Some(sub_account_details)))

      (prospectDAOSErvice.getProspectDetailsForIncomingCallFromNumber)
        .expects(
          incoming_call_from.copy(phone_number = "**********"),
          call_account_settings.team_id)
        .returning(Success(prospectDetails))


      (twilioDialerService.authenticateTwilioWebhook(_: TwlAuthToken, _: Map[String, String], _: String, _: String)(using _: SRLogger))
        .expects(twl_sub_account_auth_token, incoming_params, webhook_url, twl_signature, *)
        .returning(Success(true))

      /*(() => srUuidUtils.generateParticipantUuid())
        .expects()
        .returning("part_123")

      (callDAO.saveParticipantDetails)
        .expects(*, *, *, *, *, *)
        .returning(Success(participant_details_to_notify))

      (prospectDAO.getProspectDetailsForPusherNotification)
        .expects(*, *)
        .returning(Success(prospect_details))*/


      (accountDAO.getOwnerAccountIdByOrgId)
        .expects(org_id)
        .returning(Success(Some(account_id)))

      (organizationDAO.getOrgIdFromTeamId)
        .expects(call_account_settings.team_id)
        .returning(Success(org_id))

      (emailNotificationService.sendIncomingCallReceivedMail(
        _: AccountId,
        _: AccountId,
        _: Option[Try[Seq[ProspectObject]]],
        _: PhoneNumber,
        _: PhoneNumber)
      (_: WSClient,
        _: ExecutionContext,
        _: SRLogger))

        .expects(account_id, call_account_settings.owner_account_id, None, incoming_call_to, incoming_call_from.copy(phone_number = "+91**********"), *, *, *)
        .returning(Success(println("hi")))

      (twilioDialerService.handleIncomingCall(_: CallerIdentity, _: Boolean, _: Option[String], _: CallDetails, _: TwlSubAccountSid, _: Boolean, _: TeamId, _: TwlAuthToken, _: OrgId)(using _: SRLogger))
        .expects(
          client_identity,
          call_account_settings.enable_forwarding,
          call_account_settings.forward_to,
          call_details.copy(is_incoming = true, from = PhoneNumber("+91**********")),
          twl_sub_account_sid,
          false,
          call_account_settings.team_id,
          twl_sub_account_auth_token,
          org_id,
          logger
        )
        .returning(Success(dialer_service_result))

      /*(() => srUuidUtils.generateConferenceUuid())
        .expects()
        .returning("conf_123")

      (callDAO.saveConferenceDetails)
        .expects(*, *, *, *, *, *, *)
        .returning(Success(ConferenceUuid("conf_123")))
    */

      val result = callService.handleCall(
        validate_webhook_data = validate_webhook_data.copy(params = incoming_params)
      )

      result match {
        case Right(response) =>
          assert(response == "Response aarha hai")

        case _ =>

          assert(false)
      }

      //      assert(result. == Left(HandleCallError.SQLExceptionError(error)))
    }

    it("should go to incoming calls and fail as call_to is undefined ") {


      val result = callService.handleCall(
        validate_webhook_data = validate_webhook_data.copy(params = Map("From" -> ""))
      )

      result match {
        case Left(HandleCallError.BadRequestError(GetCallDetailsError.ToNotFound)) =>
          assert(true)

        case _ =>
          println(s"result ${result}")

          assert(false)
      }

      //      assert(result. == Left(HandleCallError.SQLExceptionError(error)))
    }

    it("should fail as From  is undefined ") {


      val result = callService.handleCall(
        validate_webhook_data = validate_webhook_data.copy(params = Map())

      )

      result match {
        case Left(HandleCallError.BadRequestError(GetCallDetailsError.FromNotFound)) =>
          assert(true)

        case _ =>
          //          println(s"result ${result}")

          assert(false)
      }

      //      assert(result. == Left(HandleCallError.SQLExceptionError(error)))
    }
  }

  describe("updateCallAccountData related test") {

    val exception = new Exception("some error")
    val data: CreateOrUpdateCallAccountData = CreateOrUpdateCallAccountData(
      first_name = "FirstName",
      last_name = "LastName",
      country_code = "US",
      phone_type = PhoneType.Local,
      enable_call_recording = false,
      enable_forward = false,
      forward_number = None,
      call_limit = 11,
      phone_uuid = None,
      caller_id = None
    )
    val phoneNumberUuid: PhoneNumberUuid = PhoneNumberUuid(phone_number_uuid = "uuid")
    val teamId: TeamId = TeamId(id = 3L)

    it("should fail when there is an exception") {
      (callDAO.updateCallAccountSettings)
        .expects(data, phoneNumberUuid, teamId)
        .returning(Failure(exception = exception))

      val res = callService.updateCallAccountData(data = data, uuid = phoneNumberUuid, teamId = teamId)

      assert(
        res match {
          case Left(err) =>
            err match {
              case UpdateCallSettingsError.SQLException(_) => true
              case UpdateCallSettingsError.AccountNotFoundError(_) => false
            }
          case Right(_) => false
        }
      )
    }

    it("should fail if DAO returns an integer other than 1") {
      (callDAO.updateCallAccountSettings)
        .expects(data, phoneNumberUuid, teamId)
        .returning(Success(3))

      val res = callService.updateCallAccountData(data = data, uuid = phoneNumberUuid, teamId = teamId)

      assert(
        res match {
          case Left(err) =>
            err match {
              case UpdateCallSettingsError.SQLException(_) => false
              case UpdateCallSettingsError.AccountNotFoundError(_) => true
            }
          case Right(_) => false
        }
      )
    }

    it("should pass") {
      (callDAO.updateCallAccountSettings)
        .expects(data, phoneNumberUuid, teamId)
        .returning(Success(1))

      (srRedisSimpleLockServiceV2.releaseLock(
        _: CacheIdKeyForLock
      )(
        using _: SRLogger
      ))
        .expects(*, *)
        .returning(Success(true))

      val res = callService.updateCallAccountData(data = data, uuid = phoneNumberUuid, teamId = teamId)

      assert(
        res match {
          case Left(err) =>
            err match {
              case UpdateCallSettingsError.SQLException(_) => false
              case UpdateCallSettingsError.AccountNotFoundError(_) => false
            }
          case Right(_) => true
        }
      )
    }
  }

  describe("testing CallService.calculateCredit ") {

    val total_usage = 10
    val previously_deducted = 3
    val current_credit_balance = 20

    it("should give correct values for remaining_balance for given values") {

      val result = CallService.calculateCredit(
        previously_deducted_cents = previously_deducted,
        usage_till_now_cents = total_usage, // Total_usage_till_date
        current_credits_left_cents = current_credit_balance
      )

      assert(result.updated_credit_for_org_cents == 13) // 20 - (10 - 3 ) : current_balance - ( usage - already_deducted )
      assert(result.current_deducted_credit_cents == 7) // 10 - 3 : usage - already_deducted
      assert(result.current_already_subtracted_credit_cents == 10) // 3 + (10 - 3) = 10 already_subtracted + new_usage_subtraction

    }

  }

  //  val twl_sub_account_id = TwlSubAccountSid(
  //    id = "twl_sub_id_27_june_2023"
  //  )
  //  val org_id = OrgId(id = 20)
  //  val token = TwlAuthToken(
  //    token = "twl_token_i_am"
  //  )

  //  val sub_account_uuid = SubAccountUuid(
  //    uuid = "sub_account_Ssfskjecoise234*skdlfj"
  //  )
  //  val org_id = OrgId(
  //    id = 20
  //  )
  //  val twl_sub_account_sid = TwlSubAccountSid(
  //    id = "twl_sub_account_sid"
  //  )
  //  val twl_sub_account_auth_token = TwlAuthToken(
  //    token = "twl_sub_account_auth_token"
  //  )
  //  val sub_account_name = "Sub_account_name_hun_main"
  //
  //  val sub_account_details = SubAccountDetails(
  //    uuid = sub_account_uuid,
  //    sub_account_id = twl_sub_account_sid,
  //    sub_auth_token = twl_sub_account_auth_token,
  //    sub_account_name = sub_account_name,
  //    call_remaining_credit = 3000,
  //    call_credit_updated_at = None,
  //    credit_unit = "USD",
  //    previous_usage_deducted = None,
  //    check_usage_from = DateTime.now(), // not currently in use
  //    trigger_name = None,
  //    trigger_webhook = None,
  //    trigger_recurring = None,
  //    trigger_usage_category = None,
  //    trigger_last_fired = None,
  //    created_at = DateTime.now(),
  //    updated_at = DateTime.now(),
  //    org_id = org_id,
  //    status = "Active",
  //    call_credit = 3000,
  //    trigger_id = None,
  //    trigger_value = None,
  //    trigger_by = None,
  //    already_subtracted = Some(250),
  //  )
  //  val usage_till = DateTime.now()
  //
  //  val twl_usage_details = UsageDetails(
  //    sub_account_sid = sub_account_details.sub_account_id,
  //    total_usage = 250,
  //    usage_unit = "USD",
  //    usage_from = DateTime.now(),
  //    usage_till = usage_till
  //  )

  val lowerCreditWarningMessage = "Your calling balance is low. Add $6 or more to maintain at least $2 per number for smooth calling."
  val callingFeatureSuspendedWarningMessage = "Your credit limit for calling has been reached. Please add more credits to ensure smooth calling with prospects"

  describe("testing callService.updateSubAccoundCredit") {
    it("should fail when fetching sub-account details fail from db") {

      (callDAO.getSubAccountDetailsForOrg)
        .expects(Some(sub_account_uuid), org_id)
        .returning(Failure(error))


      val res = callService.updateSubAccountCredit(
        sub_account_uuid = sub_account_uuid,
        org_id = org_id,
      )


      res match {
        case Left(UpdateSubAccountCreditError.ErrorWhileGettingSubAccountFromDB(err)) =>

          assert(err.getMessage == error.getMessage)


        case _ =>

          assert(false)

      }

    }

    it("should fail when db returns none while fetching sub-account details ") {

      (callDAO.getSubAccountDetailsForOrg)
        .expects(Some(sub_account_uuid), org_id)
        .returning(Success(None))


      val res = callService.updateSubAccountCredit(
        sub_account_uuid = sub_account_uuid,
        org_id = org_id,
      )


      res match {
        case Left(UpdateSubAccountCreditError.ErrorWhileGettingSubAccountFromDB(err)) =>

          assert(true)
          assert(err.getMessage == CONSTANTS.API_MSGS.ERR_NO_SUBACCOUNTS_FOUND)


        case _ =>

          assert(false)

      }

    }

    it("should fail when twilio fails to fetch usage of sub-account") {

      (callDAO.getSubAccountDetailsForOrg)
        .expects(Some(sub_account_uuid), org_id)
        .returning(Success(Some(sub_account_details)))


      (twilioDialerService.getUsageOfSubAccount(_: TwlSubAccountSid, _: TwlAuthToken)(using _: SRLogger))
        .expects(twl_sub_account_sid, twl_sub_account_auth_token, *)
        .returning(Left(GetSubAccountUsageError.ErrorWhileFetchingFromTwilio(error)))


      val res = callService.updateSubAccountCredit(
        sub_account_uuid = sub_account_uuid,
        org_id = org_id
      )


      res match {
        case Left(UpdateSubAccountCreditError.ErrorWhileGettingUsageFromTwilio(err)) =>

          err match {
            case GetSubAccountUsageError.ErrorWhileFetchingFromTwilio(err) =>
              assert(err.getMessage == error.getMessage)

            case _ =>

              assert(false)

          }

        case _ =>

          assert(false)

      }

    }

    it("should fail when twilio returns usage such that we couldn't parse") {

      (callDAO.getSubAccountDetailsForOrg)
        .expects(Some(sub_account_uuid), org_id)
        .returning(Success(Some(sub_account_details)))


      (twilioDialerService.getUsageOfSubAccount(_: TwlSubAccountSid, _: TwlAuthToken)(using _: SRLogger))
        .expects(twl_sub_account_sid, twl_sub_account_auth_token, *)
        .returning(Left(GetSubAccountUsageError.ErrorWhileParsingUsage(error)))


      val res = callService.updateSubAccountCredit(
        sub_account_uuid = sub_account_uuid,
        org_id = org_id
      )


      res match {
        case Left(UpdateSubAccountCreditError.ErrorWhileGettingUsageFromTwilio(err)) =>

          err match {
            case GetSubAccountUsageError.ErrorWhileParsingUsage(err) =>
              assert(err.getMessage == error.getMessage)

            case _ =>

              assert(false)

          }

        case _ =>

          assert(false)

      }

    }

    it("should not update Calling balance and return 0 rows_updated when there is no new usage from user") {

      (callDAO.getSubAccountDetailsForOrg)
        .expects(Some(sub_account_uuid), org_id)
        .returning(Success(Some(sub_account_details)))


      (twilioDialerService.getUsageOfSubAccount(_: TwlSubAccountSid, _: TwlAuthToken)(using _: SRLogger))
        .expects(twl_sub_account_sid, twl_sub_account_auth_token, *)
        .returning(Right(twl_usage_details))

      (callDAO.getTotalCallAccountsCountInOrg)
        .expects(org_id)
        .returning(Success(3))


      val res = callService.updateSubAccountCredit(
        sub_account_uuid = sub_account_uuid,
        org_id = org_id
      )


      res match {
        case Right(sub_account_details.call_remaining_credit_cents) =>

          assert(true)

        case _ =>

          assert(false)

      }

    }

    it("should fail when there is an error while updating credits for user") {

      (callDAO.getSubAccountDetailsForOrg)
        .expects(Some(sub_account_uuid), org_id)
        .returning(Success(Some(sub_account_details)))


      (twilioDialerService.getUsageOfSubAccount(_: TwlSubAccountSid, _: TwlAuthToken)(using _: SRLogger))
        .expects(twl_sub_account_sid, twl_sub_account_auth_token, *)
        .returning(Right(twl_usage_details.copy(
          total_usage_in_cents = 500 // here already_subtracted usage is 250, so, here we have 50 credits as new usage, should go to db to update this
        )))


      (callDAO.updateCreditForSubAccount)
        .expects(sub_account_uuid,
          500 - 250,
          500,
          sub_account_details.org_id,
          sub_account_details.call_remaining_credit_cents - (500 - 250)
        )
        .returning(Failure(error))


      val res = callService.updateSubAccountCredit(
        sub_account_uuid = sub_account_uuid,
        org_id = org_id,
      )


      res match {
        case Left(UpdateSubAccountCreditError.CreditSaveErrorInDB(err)) =>

          assert(err.getMessage == error.getMessage)

        case _ =>

          assert(false)

      }

    }

    it("should success and update new credits for user") {

      (callDAO.getSubAccountDetailsForOrg)
        .expects(Some(sub_account_uuid), org_id)
        .returning(Success(Some(sub_account_details)))

      (twilioDialerService.getUsageOfSubAccount(_: TwlSubAccountSid, _: TwlAuthToken)(using _: SRLogger))
        .expects(twl_sub_account_sid, twl_sub_account_auth_token, *)
        .returning(Right(twl_usage_details.copy(
          total_usage_in_cents = 500 // here already_subtracted usage is 250, so, here we have 50 credits as new usage, should go to db to update this
        )))


      (callDAO.updateCreditForSubAccount)
        .expects(sub_account_uuid,
          500 - 250,
          500,
          sub_account_details.org_id,
          sub_account_details.call_remaining_credit_cents - (500 - 250)
        )
        .returning(Success(sub_account_details.call_remaining_credit_cents))

      (callDAO.getTotalCallAccountsCountInOrg)
        .expects(org_id)
        .returning(Success(3))

      //      (callDAO.addWarningMessageInOrgCallingCredits)
      //        .expects(org_id,lowerCreditWarningMessage, *,  AccountWarningCodeType.LowCallingCredit)
      //        .returning(Success(Some(lowerCreditWarningMessage)))
      //
      //      (accountDAO.getOwnerAccountIdByOrgId)
      //        .expects(org_id)
      //        .returning(Success(Some(account_id)))
      //
      //      ((resetUserCacheUtil._resetTeamCache _))
      //        .expects(account_id.id)
      //        .returning(*)


      val res = callService.updateSubAccountCredit(
        sub_account_uuid = sub_account_uuid,
        org_id = org_id
      )


      res match {
        case Right(sub_account_details.call_remaining_credit_cents) =>
          //updated one -rows hence true

          assert(true)

        case _ =>

          assert(false)

      }

    }


  }

  describe("testing CallService.updateSubAccountCredit ") {

    it("should fail when sql exception comes while fetching sub-account data") {


      (callDAO.getSubAccountDetailsForOrg)
        .expects(Some(sub_account_uuid), org_id)
        .returning(Failure(error))

      val res = callService.updateSubAccountCredit(
        sub_account_uuid = sub_account_uuid,
        org_id = org_id
      )

      res match {
        case Left(UpdateSubAccountCreditError.ErrorWhileGettingSubAccountFromDB(err)) =>

          assert(true)

        case _ =>

          assert(false)
      }

    }

    it("should fail when None is returned while fetching sub-account data") {


      (callDAO.getSubAccountDetailsForOrg)
        .expects(Some(sub_account_uuid), org_id)
        .returning(Success(None))

      val res = callService.updateSubAccountCredit(
        sub_account_uuid = sub_account_uuid,
        org_id = org_id
      )

      res match {
        case Left(UpdateSubAccountCreditError.ErrorWhileGettingSubAccountFromDB(err)) =>

          assert(true)
          assert(err.getMessage == CONSTANTS.API_MSGS.ERR_NO_SUBACCOUNTS_FOUND)

        case _ =>

          assert(false)
      }

    }


    it("should fail when usage is not found for the sub-account from twilio") {


      (callDAO.getSubAccountDetailsForOrg)
        .expects(Some(sub_account_uuid), org_id)
        .returning(Success(Some(sub_account_details)))

      (twilioDialerService.getUsageOfSubAccount(_: TwlSubAccountSid, _: TwlAuthToken)(using _: SRLogger))
        .expects(sub_account_details.sub_account_id, sub_account_details.sub_auth_token, *)
        .returning(Left(GetSubAccountUsageError.ErrorWhileFetchingFromTwilio(error)))


      val res = callService.updateSubAccountCredit(
        sub_account_uuid = sub_account_uuid,
        org_id = org_id
      )

      res match {
        case Left(UpdateSubAccountCreditError.ErrorWhileGettingUsageFromTwilio(err)) =>

          assert(true)

        case _ =>

          assert(false)
      }

    }


    it("should success and updates 0 rows as already_subtracted is equual to total_usage of sub_account, that means no new usage hence 0 updates in db") {

      (callDAO.getSubAccountDetailsForOrg)
        .expects(Some(sub_account_uuid), org_id)
        .returning(Success(Some(sub_account_details)))

      (twilioDialerService.getUsageOfSubAccount(_: TwlSubAccountSid, _: TwlAuthToken)(using _: SRLogger))
        .expects(sub_account_details.sub_account_id, sub_account_details.sub_auth_token, *)
        .returning(Right(twl_usage_details))

      (callDAO.getTotalCallAccountsCountInOrg)
        .expects(org_id)
        .returning(Success(3))

      //      (callDAO.addWarningMessageInOrgCallingCredits)
      //        .expects(org_id, lowerCreditWarningMessage, *, AccountWarningCodeType.LowCallingCredit)
      //        .returning(Success(Some(lowerCreditWarningMessage)))

      val res = callService.updateSubAccountCredit(
        sub_account_uuid = sub_account_uuid,
        org_id = org_id
      )

      res match {
        case Right(sub_account_details.call_remaining_credit_cents) =>

          assert(true)

        case _ =>

          assert(false)
      }

    }

    it("should fail as updating the credit in db fails") {

      (callDAO.getSubAccountDetailsForOrg)
        .expects(Some(sub_account_uuid), org_id)
        .returning(Success(Some(sub_account_details)))

      (twilioDialerService.getUsageOfSubAccount(_: TwlSubAccountSid, _: TwlAuthToken)(using _: SRLogger))
        .expects(sub_account_details.sub_account_id, sub_account_details.sub_auth_token, *)
        .returning(Right(twl_usage_details
          .copy(total_usage_in_cents = 300)) // 50 more than already_subtracted, hence that needs to be updated
        )

      (callDAO.updateCreditForSubAccount)
        .expects(sub_account_details.uuid,
          50, // previous_subtracted = 50 ( currently_subtracting )
          300, // already_subtracted_value = 300
          sub_account_details.org_id,
          3000 - (300 - 250)) // updated_credit = > previous_balance( 3000 ) - ( total_usage ( 300)  - already_subtracted ( 250 )) = 2950
        .returning(Failure(error))


      val res = callService.updateSubAccountCredit(
        sub_account_uuid = sub_account_uuid,
        org_id = org_id
      )

      res match {
        case Left(UpdateSubAccountCreditError.CreditSaveErrorInDB(err)) =>

          assert(true)

        case _ =>

          assert(false)
      }

    }

    it("should success and updates 1 rows as total_usage is greater than already_subtracted hence new usage is there and db needs to be updated") {

      (callDAO.getSubAccountDetailsForOrg)
        .expects(Some(sub_account_uuid), org_id)
        .returning(Success(Some(sub_account_details)))

      (twilioDialerService.getUsageOfSubAccount(_: TwlSubAccountSid, _: TwlAuthToken)(using _: SRLogger))
        .expects(sub_account_details.sub_account_id, sub_account_details.sub_auth_token, *)
        .returning(Right(twl_usage_details
          .copy(total_usage_in_cents = 300) // 50 more than already_subtracted, hence that needs to be updated
        ))

      (callDAO.updateCreditForSubAccount)
        .expects(sub_account_details.uuid,
          50, // previous_subtracted = 50 ( currently_subtracting )
          300, // already_subtracted_value = 300
          sub_account_details.org_id,
          3000 - (300 - 250)) // updated_credit = > previous_balance( 3000 ) - ( total_usage ( 300)  - already_subtracted ( 250 )) = 2950
        .returning(Success(1)) // updated_credit_from_db


      (callDAO.getTotalCallAccountsCountInOrg)
        .expects(org_id)
        .returning(Success(3))

      (callDAO.addWarningMessageInOrgCallingCredits)
        .expects(org_id, lowerCreditWarningMessage, *, AccountWarningCodeType.LowCallingCredit)
        .returning(Success(Some(lowerCreditWarningMessage)))

      (accountDAO.getOwnerAccountIdByOrgId)
        .expects(org_id)
        .returning(Success(Some(account_id)))

      (emailNotificationService.sentLimitReachedWarning(_: OrgId, _: AccountId,_:AccountWarningCodeType,_:Option[Int], _: Option[DateTime],_:Option[Int])(_: WSClient, _: ExecutionContext, _: SRLogger))
        .expects(org_id, account_id,AccountWarningCodeType.LowCallingCredit,None,None, Some(0), *, *, *)
        .returning(Success(true))

      (emailNotificationService.getLatestEmailSendLog(_: NotificationType, _: OrgId, _: Option[TeamId]))
        .expects(NotificationType.LowCallingCreditAvailable, org_id, None)
        .returning(Right(Some(notificationEmailSendLog)))

      val orgWithCurrentData = DefaultCampaignParametersFixtures.defaultOrganizationWithCurrentData(
        accountId = account_id,
        orgId = org_id,
        current_sending_email_accounts = 21,
      )

      (organizationDAOService.getOrgWithCurrentData(_: OrgId)(_: SRLogger))
        .expects(org_id, *)
        .returning(
          Success(Some(orgWithCurrentData))
        )

      val res = callService.updateSubAccountCredit(
        sub_account_uuid = sub_account_uuid,
        org_id = org_id
      )

      res match {
        case Right(value) =>

          assert(value == 1) // updated one row

        case _ =>

          assert(false)
      }

    }

  }


  val org_plan = OrgPlanFixture.orgPlanFixture

  val total_seats = 3

  val owner_account_sid = "owner_account_sid_hun_main"
  val credit_unit = CurrencyType.USD

  val twilio_sub_account_details = TwilioSubAccountDetails(
    sub_account_sid = twl_sub_account_sid,
    auth_token = twl_sub_account_auth_token,
    sub_account_name = sub_account_name,
    status = "active",
    owner_account_sid = owner_account_sid,
  )


  val new_sub_account_details = NewSubAccountDetails(
    uuid = sub_account_uuid,
    sub_account_sid = twl_sub_account_sid,
    auth_token = twl_sub_account_auth_token,
    sub_account_name = sub_account_name,
    status = "active",
    owner_account_sid = owner_account_sid,
    call_remaining_credit = 3000,
    call_credit_updated_at = DateTime.now(),
    credit_unit = credit_unit,
    org_id = org_id,
    call_credit = 3000,
    previous_usage_deducted = None,
    check_usage_from = DateTime.now()
  )

  val trigger_id = "trigger_id_hun_main"
  val trigger_by = "price"
  val trigger_value = 250
  val trigger_name = "trigger_name_hun_main"
  val trigger_webhook = "trigger_webhook"
  val trigger_recurring = "trigger_recurring_hun_main"
  val trigger_current_value = "trigger_current_value_hun_main"
  val trigger_usage_category = "trigger_usage_category_hun_main"
  val trigger_last_fired = DateTime.now()


  val twilio_usage_trigger_details = TwilioUsageTrigger(
    trigger_id = trigger_id,
    trigger_value = trigger_value.toString,
    trigger_by = trigger_by,
    trigger_name = trigger_name,
    trigger_webhook = trigger_webhook,
    trigger_recurring = trigger_recurring,
    trigger_current_value = trigger_current_value,
    trigger_usage_category = trigger_usage_category,
    trigger_last_fired = trigger_last_fired,
    sub_account_sid = twl_sub_account_sid.id
  )

  val sub_account_details_found = SubAccountDetailsFound(
    sub_account_sid = twl_sub_account_sid,
    sub_auth_token = twl_sub_account_auth_token,
    org_sub_account_uuid = sub_account_uuid,
    call_remaining_credit_cents = 3000L
  )


  describe("testig callService.createSubAccountIfNotExist") {

    it("should fail when fetching sub-account details fails") {

      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Failure(error))

      val res = callService.createSubAccountIfNotExist(
        org_id = org_id,
        org_plan = org_plan
      )

      res match {

        case Left(SubAccountCreationError.ErrorWhileGettingSubAccount(err)) =>

          assert(err.getMessage == error.getMessage)


        case _ =>

          assert(false)

      }

    }

    it("should directly return sub_account_sid_and_auth_token when sub_account found for the organization") {

      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Success(Some(sub_account_details)))

      val res = callService.createSubAccountIfNotExist(
        org_id = org_id,
        org_plan = org_plan
      )

      res match {

        case Right(data) =>

          assert(data == sub_account_details_found)


        case _ =>

          assert(false)

      }

    }


    it("should go to creation of new sub-account when no-sub account found for the org and fail since twilioDialerService.createSubAccount fails") {

      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Success(None))

      (() => twilioDialerService.createSubAccount())
        .expects()
        .returning(Failure(error))

      val res = callService.createSubAccountIfNotExist(
        org_id = org_id,
        org_plan = org_plan
      )

      println(res)

      res match {

        case Left(SubAccountCreationError.ErrorWhileCreatingSubAccountForOrg(err)) =>

          assert(err.getMessage == error.getMessage)


        case _ =>

          assert(false)

      }

    }

    it("should go to creation of new sub-account when no-sub account found for the org and fail since callDao.saveSubAccountDetails fails") {

      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Success(None))

      (() => twilioDialerService.createSubAccount())
        .expects()
        .returning(Success(twilio_sub_account_details))

      (() => srUuidUtils.generateSubAccountUuid())
        .expects()
        .returning(sub_account_uuid.uuid)

      (callDAO.saveSubAccountDetails)
        .expects(*)
        .returning(Failure(error))

      val res = callService.createSubAccountIfNotExist(
        org_id = org_id,
        org_plan = org_plan
      )

      println(res)

      res match {

        case Left(SubAccountCreationError.ErrorWhileSaveSubAccountDetailsInDB(err)) =>

          assert(err.getMessage == error.getMessage)


        case _ =>

          assert(false)

      }

    }

    it("should go to creation of new sub-account when no-sub account found for the org and fail since twilioDialerService.createUsageTrigger fails") {

      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Success(None))

      (() => twilioDialerService.createSubAccount())
        .expects()
        .returning(Success(twilio_sub_account_details))

      (() => srUuidUtils.generateSubAccountUuid())
        .expects()
        .returning(sub_account_uuid.uuid)

      (callDAO.saveSubAccountDetails)
        .expects(*)
        .returning(Success(sub_account_uuid))

      (twilioDialerService.createUsageTrigger(_: OrgId, _: String, _: TwlSubAccountSid, _: TwlAuthToken)(using _: SRLogger))
        .expects(org_id, "0", twl_sub_account_sid, twl_sub_account_auth_token, *)
        .returning(Failure(error))

      val res = callService.createSubAccountIfNotExist(
        org_id = org_id,
        org_plan = org_plan
      )

      println(res)

      res match {

        case Left(SubAccountCreationError.FailureWhileCreatingUsageTrigger(err)) =>

          assert(err.getMessage == error.getMessage)


        case _ =>

          assert(false)

      }

    }

    it("should go to creation of new sub-account when no-sub account found for the org and fail since callDao.saveTwilioTriggerDetails fails") {

      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Success(None))

      (() => twilioDialerService.createSubAccount())
        .expects()
        .returning(Success(twilio_sub_account_details))

      (() => srUuidUtils.generateSubAccountUuid())
        .expects()
        .returning(sub_account_uuid.uuid)

      (callDAO.saveSubAccountDetails)
        .expects(*)
        .returning(Success(sub_account_uuid))

      (twilioDialerService.createUsageTrigger(_: OrgId, _: String, _: TwlSubAccountSid, _: TwlAuthToken)(using _: SRLogger))
        .expects(org_id, "0", twl_sub_account_sid, twl_sub_account_auth_token, *)
        .returning(Success(twilio_usage_trigger_details))

      (callDAO.saveTwilioTriggerDetails)
        .expects(twilio_usage_trigger_details)
        .returning(Failure(error))

      val res = callService.createSubAccountIfNotExist(
        org_id = org_id,
        org_plan = org_plan
      )

      println(res)

      res match {

        case Left(SubAccountCreationError.ErrorWhileSaveTriggerDetailsInDB(err)) =>

          assert(err.getMessage == error.getMessage)


        case _ =>

          assert(false)

      }

    }


    it("should success save trigger details successfully") {

      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Success(None))

      (() => twilioDialerService.createSubAccount())
        .expects()
        .returning(Success(twilio_sub_account_details))

      (() => srUuidUtils.generateSubAccountUuid())
        .expects()
        .returning(sub_account_uuid.uuid)

      (callDAO.saveSubAccountDetails)
        .expects(*)
        .returning(Success(sub_account_uuid))

      (twilioDialerService.createUsageTrigger(_: OrgId, _: String, _: TwlSubAccountSid, _: TwlAuthToken)(using _: SRLogger))
        .expects(org_id, "0", twl_sub_account_sid, twl_sub_account_auth_token, *)
        .returning(Success(twilio_usage_trigger_details))

      (callDAO.saveTwilioTriggerDetails)
        .expects(twilio_usage_trigger_details)
        .returning(Success(trigger_id))

      (twilioDialerService.createApiKeySecretForSubAccount(_: TwlSubAccountSid, _: TwlAuthToken, _: SubAccountUuid)(using _: SRLogger))
        .expects(twl_sub_account_sid, twl_sub_account_auth_token, sub_account_uuid, *)
        .returning(Success(subAccountApiKeyAndSecret))

      (callDAO.saveSubAccountEncryptionKeys)
        .expects(subAccountApiKeyAndSecret, sub_account_uuid, org_id)
        .returning(Success(Some(subAccountApiKeyAndSecret.api_key)))

      (twilioDialerService.createTwimlAppForSubAccounts(_: TwlSubAccountSid, _: TwlAuthToken, _: SubAccountUuid)(using _: SRLogger))
        .expects(twl_sub_account_sid, twl_sub_account_auth_token, sub_account_uuid, *)
        .returning(Success(subaccountTwilmlApplication))

      (callDAO.saveSubAccountTwimlApp)
        .expects(subaccountTwilmlApplication, sub_account_uuid, org_id)
        .returning(Success(Some(subaccountTwilmlApplication.app_name)))


      val res = callService.createSubAccountIfNotExist(
        org_id = org_id,
        org_plan = org_plan
      )

      println(res)

      res match {

        case Right(data) =>

          assert(true)


        case _ =>

          assert(false)

      }

    }


  }


  describe("testing callService.handleUsageLimitCrossedWebhook ") {


    //    it("should fail when AccountSid ain't present in twilio body"){
    //
    //      val body_without_acc_sid = AnyContentAsFormUrlEncoded((ListMap("UsageCategory" -> List("totalprice"))))
    //
    //
    //      val res = callService.handleUsageLimitCrossedWebhook(body_without_acc_sid, org_id)
    //
    //      res match {
    //
    //        case Left(HandlingUsageWebhookError.ErrorWhileParsingBody(err)) =>
    //
    //          assert(err.getMessage == "account_sid not present")
    //
    //        case _ =>
    //
    //          assert(false)
    //
    //      }

  }

  val twl_trigger_id = "twl_trigger_id_hun_main"

  val date_fired = DateTime.now()

  val current_value = 250

  val idempotency_token = "idempotency_token_hun_main"
  val idempotency_token_different = idempotency_token + "_different"


  val twl_usasge_limit_crossed = TwilioUsageLimitCrossedDetails(
    twl_sub_account_sid = twl_sub_account_sid,
    twl_usage_trigger_sid = twl_trigger_id,
    date_fired = date_fired,
    trigger_value = trigger_value,
    current_value = current_value,
    idempotency_token = idempotency_token
  )

  val twl_sub_account_details = TwilioSubAccountDetails(
    sub_account_sid = twl_sub_account_sid,
    auth_token = twl_sub_account_auth_token,
    sub_account_name = sub_account_details.sub_account_name,
    status = "suspended",
    owner_account_sid = "owner_account_sid",
  )

  describe("testing callService.handleUsageLimitCrossedWebhook") {

    it("should fail when fetching sub-account details fail") {

      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Failure(error))

      val res = callService.handleUsageLimitCrossedWebhook(
        webhook_details = twl_usasge_limit_crossed,
        org_id = org_id
      )

      assert(res == Left(HandlingUsageWebhookError.ErrorWhileGettingSubAccountError(error)))

      //      res match {
      //
      //        case Left(HandlingUsageWebhookError.ErrorWhileGettingSubAccountError(err)) =>
      //
      //          assert(err.getMessage == error.getMessage)
      //
      //        case _ =>
      //
      //          assert(false)
      //
      //      }

    }

    it("should fail as db returns none while fetching sub-account details fail") {

      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Success(None))

      val res = callService.handleUsageLimitCrossedWebhook(
        webhook_details = twl_usasge_limit_crossed,
        org_id = org_id
      )

      res match {
        case Right(value) => assert(false)
        case Left(err) =>
          err match {
            case HandlingUsageWebhookError.ErrorWhileGettingSubAccountError(err) =>
              assert(err.getMessage == CONSTANTS.API_MSGS.ERR_NO_SUBACCOUNTS_FOUND)

            case HandlingUsageWebhookError.SameTriggerIdempotencyTokenError => assert(false)
            case HandlingUsageWebhookError.ErrorWhileAddingWarningMessage(e) => assert(false)
            case HandlingUsageWebhookError.ErrorWhileSuspendingAccount(err) => assert(false)
            case HandlingUsageWebhookError.ErrorWhileUpdatingAccountStatusInDB(err) => assert(false)
            }
      }


    }

    it("should fail when same idempotency_token comes in twilio-trigger-webhook") {

      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Success(Some(sub_account_details.copy(
          twl_idempotency_token = Some(idempotency_token)
        ))))

      val res = callService.handleUsageLimitCrossedWebhook(
        webhook_details = twl_usasge_limit_crossed,
        org_id = org_id
      )

      res match {

        case Left(HandlingUsageWebhookError.SameTriggerIdempotencyTokenError) =>

          assert(true)

        case _ =>

          assert(false)

      }

    }

    it("should proceed ahead when different idempotency_tokens are present and fail when twilio throws error while suspending the account") {

      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Success(Some(sub_account_details.copy(
          twl_idempotency_token = Some(idempotency_token_different)
        ))))

      (twilioDialerService.suspendSubAccount(_: TwlSubAccountSid)(using _: SRLogger))
        .expects(twl_sub_account_sid, *)
        .returning(Failure(error))

      val res = callService.handleUsageLimitCrossedWebhook(
        webhook_details = twl_usasge_limit_crossed,
        org_id = org_id
      )

      res match {

        case Left(HandlingUsageWebhookError.ErrorWhileSuspendingAccount(err)) =>

          assert(err.getMessage == error.getMessage)

        case _ =>

          assert(false)

      }

    }

    it("should proceed ahead when different idempotency_tokens are present and fail when saveSuspendedSubAccountDetails fails while saving account data") {

      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Success(Some(sub_account_details.copy(
          twl_idempotency_token = Some(idempotency_token_different)
        ))))

      (twilioDialerService.suspendSubAccount(_: TwlSubAccountSid)(using _: SRLogger))
        .expects(twl_sub_account_sid, *)
        .returning(Success(twl_sub_account_details))

      (callDAO.saveSuspendedSubAccountDetails)
        .expects(twl_sub_account_details, twl_usasge_limit_crossed, org_id)
        .returning(Failure(error))


      val res = callService.handleUsageLimitCrossedWebhook(
        webhook_details = twl_usasge_limit_crossed,
        org_id = org_id
      )

      res match {

        case Left(HandlingUsageWebhookError.ErrorWhileUpdatingAccountStatusInDB(err)) =>

          assert(err.getMessage == error.getMessage)

        case _ =>

          assert(false)

      }

    }

    it("should proceed ahead when different idempotency_tokens are present and succeed") {

      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Success(Some(sub_account_details.copy(
          twl_idempotency_token = Some(idempotency_token_different)
        ))))

      (twilioDialerService.suspendSubAccount(_: TwlSubAccountSid)(using _: SRLogger))
        .expects(twl_sub_account_sid, *)
        .returning(Success(twl_sub_account_details))

      (callDAO.saveSuspendedSubAccountDetails)
        .expects(twl_sub_account_details, twl_usasge_limit_crossed, org_id)
        .returning(Success(1))

      //      (callDAO.getTotalCallAccountsCountInOrg)
      //        .expects(org_id)
      //        .returning(Success(3))

      (callDAO.addWarningMessageInOrgCallingCredits)
        .expects(org_id, callingFeatureSuspendedWarningMessage, *, AccountWarningCodeType.CallingFeatureSuspended)
        .returning(Success(Some(callingFeatureSuspendedWarningMessage)))

      (accountDAO.getOwnerAccountIdByOrgId)
        .expects(org_id)
        .returning(Success(Some(account_id)))

      (emailNotificationService.sentLimitReachedWarning(_: OrgId, _: AccountId,_:AccountWarningCodeType,_:Option[Int], _: Option[DateTime],_:Option[Int])(_: WSClient, _: ExecutionContext, _: SRLogger))
        .expects(org_id, account_id,AccountWarningCodeType.CallingFeatureSuspended,None,None, None, *, *, *)
        .returning(Success(true))

      (emailNotificationService.getLatestEmailSendLog(_: NotificationType, _: OrgId, _: Option[TeamId]))
        .expects(NotificationType.CallingFeatureSuspended, org_id, None)
        .returning(Right(Some(notificationEmailSendLog)))

      val res = callService.handleUsageLimitCrossedWebhook(
        webhook_details = twl_usasge_limit_crossed,
        org_id = org_id
      )

      res match {

        case Right(1) =>

          assert(true)

        case _ =>

          assert(false)

      }

    }

  }

  val body_full = Option(
    Map("UsageCategory" -> Seq("totalprice"), "IdempotencyToken" -> Seq("AC4fc9b2565bf905e16e7206bc764bdfcb - FIRES - UT416825379a374533af337cd71760aa4f - 2023 - 06"), "TriggerValue" -> Seq("3"), "Recurring" -> Seq("monthly"), "TriggerBy" -> Seq("price"), "CurrentValue" -> Seq("3.45000"), "UsageTriggerSid" -> Seq("UT416825379a374533af337cd71760aa4f"),
      "UsageRecordUri" -> Seq("/2010 - 04 - 01 / Accounts / AC4fc9b2565bf905e16e7206bc764bdfcb / Usage / Records / Monthly ? Category = totalprice"), "AccountSid" -> Seq("AC4fc9b2565bf905e16e7206bc764bdfcb")
    ))

  val account_sid_missing_body = Option(
    Map("UsageCategory" -> Seq("totalprice"), "IdempotencyToken" -> Seq("AC4fc9b2565bf905e16e7206bc764bdfcb - FIRES - UT416825379a374533af337cd71760aa4f - 2023 - 06"), "TriggerValue" -> Seq("3"), "Recurring" -> Seq("monthly"), "TriggerBy" -> Seq("price"), "CurrentValue" -> Seq("3.45000"), "UsageTriggerSid" -> Seq("UT416825379a374533af337cd71760aa4f"),
      "UsageRecordUri" -> Seq("/2010 - 04 - 01 / Accounts / AC4fc9b2565bf905e16e7206bc764bdfcb / Usage / Records / Monthly ? Category = totalprice")
    ))

  val usage_trigger_sid_missing_body = Option(
    Map("UsageCategory" -> Seq("totalprice"), "IdempotencyToken" -> Seq("AC4fc9b2565bf905e16e7206bc764bdfcb - FIRES - UT416825379a374533af337cd71760aa4f - 2023 - 06"), "TriggerValue" -> Seq("3"), "Recurring" -> Seq("monthly"), "TriggerBy" -> Seq("price"), "CurrentValue" -> Seq("3.45000"),
      "UsageRecordUri" -> Seq("/2010 - 04 - 01 / Accounts / AC4fc9b2565bf905e16e7206bc764bdfcb / Usage / Records / Monthly ? Category = totalprice"), "AccountSid" -> Seq("AC4fc9b2565bf905e16e7206bc764bdfcb")
    )
  )

  val trigger_value_missing_body = Option(
    Map("UsageCategory" -> Seq("totalprice"), "IdempotencyToken" -> Seq("AC4fc9b2565bf905e16e7206bc764bdfcb - FIRES - UT416825379a374533af337cd71760aa4f - 2023 - 06"), "Recurring" -> Seq("monthly"), "TriggerBy" -> Seq("price"), "CurrentValue" -> Seq("3.45000"), "UsageTriggerSid" -> Seq("UT416825379a374533af337cd71760aa4f"),
      "UsageRecordUri" -> Seq("/2010 - 04 - 01 / Accounts / AC4fc9b2565bf905e16e7206bc764bdfcb / Usage / Records / Monthly ? Category = totalprice"), "AccountSid" -> Seq("AC4fc9b2565bf905e16e7206bc764bdfcb")
    ))

  val current_value_missing_body = Option(
    Map("UsageCategory" -> Seq("totalprice"), "IdempotencyToken" -> Seq("AC4fc9b2565bf905e16e7206bc764bdfcb - FIRES - UT416825379a374533af337cd71760aa4f - 2023 - 06"), "TriggerValue" -> Seq("3"), "Recurring" -> Seq("monthly"), "TriggerBy" -> Seq("price"), "UsageTriggerSid" -> Seq("UT416825379a374533af337cd71760aa4f"),
      "UsageRecordUri" -> Seq("/2010 - 04 - 01 / Accounts / AC4fc9b2565bf905e16e7206bc764bdfcb / Usage / Records / Monthly ? Category = totalprice"), "AccountSid" -> Seq("AC4fc9b2565bf905e16e7206bc764bdfcb")
    ))

  val idempotency_token_missing_body = Option(
    Map("UsageCategory" -> Seq("totalprice"), "TriggerValue" -> Seq("3"), "Recurring" -> Seq("monthly"), "TriggerBy" -> Seq("price"), "CurrentValue" -> Seq("3.45000"), "UsageTriggerSid" -> Seq("UT416825379a374533af337cd71760aa4f"),
      "UsageRecordUri" -> Seq("/2010 - 04 - 01 / Accounts / AC4fc9b2565bf905e16e7206bc764bdfcb / Usage / Records / Monthly ? Category = totalprice"), "AccountSid" -> Seq("AC4fc9b2565bf905e16e7206bc764bdfcb")
    ))

  val current_value_parse_error_body = Option(
    Map("UsageCategory" -> Seq("totalprice"), "IdempotencyToken" -> Seq("AC4fc9b2565bf905e16e7206bc764bdfcb - FIRES - UT416825379a374533af337cd71760aa4f - 2023 - 06"), "TriggerValue" -> Seq("3"), "Recurring" -> Seq("monthly"), "TriggerBy" -> Seq("price"), "CurrentValue" -> Seq("ThisWillThrowError"), "UsageTriggerSid" -> Seq("UT416825379a374533af337cd71760aa4f"),
      "UsageRecordUri" -> Seq("/2010 - 04 - 01 / Accounts / AC4fc9b2565bf905e16e7206bc764bdfcb / Usage / Records / Monthly ? Category = totalprice"), "AccountSid" -> Seq("AC4fc9b2565bf905e16e7206bc764bdfcb")
    ))


  describe("testing CallService.getParsedAttributesFromBody") {
    it("should throw error when AccountSid not found ") {

      val res = CallService.getParsedAttributesFromBody(
        body = account_sid_missing_body
      )

      res match {

        case Failure(exception) =>

          assert(exception.getMessage == "AccountSid not present")

        case _ =>

          assert(false)

      }

    }

    it("should throw error when UsageTriggerSid not found ") {

      val res = CallService.getParsedAttributesFromBody(
        body = usage_trigger_sid_missing_body
      )

      res match {

        case Failure(exception) =>

          assert(exception.getMessage == "UsageTriggerSid not present")

        case _ =>

          assert(false)

      }

    }

    it("should throw error when TriggerValue not found ") {

      val res = CallService.getParsedAttributesFromBody(
        body = trigger_value_missing_body
      )

      res match {

        case Failure(exception) =>

          assert(exception.getMessage == "TriggerValue not present")

        case _ =>

          assert(false)

      }

    }

    it("should throw error when CurrentValue not found ") {
      //current_value_parse_error_body

      val res = CallService.getParsedAttributesFromBody(
        body = current_value_missing_body
      )

      res match {

        case Failure(exception) =>

          assert(exception.getMessage == "CurrentValue not present")

        case _ =>

          assert(false)

      }

    }

    it("should throw error when CurrentValue isn't parsable") {
      //current_value_parse_error_body

      val res = CallService.getParsedAttributesFromBody(
        body = current_value_parse_error_body
      )

      res match {

        case Failure(e) =>

          assert(true)

        case _ =>

          assert(false)

      }

    }

    it("should throw error when IdempotencyToken not found ") {

      val res = CallService.getParsedAttributesFromBody(
        body = idempotency_token_missing_body
      )

      res match {

        case Failure(exception) =>

          assert(exception.getMessage == "IdempotencyToken not present")

        case _ =>

          assert(false)

      }

    }


    it("should success  when full_body is passed") {

      val res = CallService.getParsedAttributesFromBody(
        body = body_full
      )

      res match {

        case Failure(exception) =>

          assert(false)

        case Success(data) =>

          assert(true)

      }

    }
  }

  describe("testing callService.activateSubAccountForOrg") {

    it("should fail while fetching sub-account") {

      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Failure(error))

      val res = callService.activateSubAccountForOrg(
        org_id = org_id
      )

      res match {
        case Left(ActivatingSubAccountError.ErrorWhileFetchingDetailsFromDB(err)) =>

          assert(err.getMessage == error.getMessage)


          /*
          err match {

            case GetSubAccountDetailsError.SQLExceptionError(e) =>

              assert(e.getMessage == error.getMessage)

            case _ =>

              assert(false)
          }
          */

        case _ =>

          assert(false)

      }

    }

    it("should fail while fetching sub-account when callDAO.getSubAccountDetailsForOrg returns None") {

      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Success(None))

      val res = callService.activateSubAccountForOrg(
        org_id = org_id
      )

      res match {
        case Left(ActivatingSubAccountError.ErrorWhileFetchingDetailsFromDB(err)) =>

          assert(true)
          assert(err.getMessage == CONSTANTS.API_MSGS.ERR_NO_SUBACCOUNTS_FOUND)

          /*
          err match {

            case GetSubAccountDetailsError.NoSubAccontDetailsFound =>

              assert(true)

            case _ =>

              assert(false)
          }
          */

        case _ =>

          assert(false)

      }

    }

    it("should fail if account already active ") {

      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Success(Some(sub_account_details)))

      //      (twilioDialerService.activateSubAccount (_:TwlSubAccountSid)(_: SRLogger))
      //        .expects(sub_account_details.sub_account_id, *)
      //        .returning(Failure(error))

      val res = callService.activateSubAccountForOrg(
        org_id = org_id
      )

      res match {
        case Left(ActivatingSubAccountError.AccountNotSuspended) =>

          assert(true)

        case _ =>

          assert(false)

      }

    }


    it("should fail while activating sub-account  ") {

      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Success(Some(sub_account_details.copy(status = SubAccountStatus.Suspended))))

      (twilioDialerService.activateSubAccount(_: TwlSubAccountSid)(using _: SRLogger))
        .expects(sub_account_details.sub_account_id, *)
        .returning(Failure(error))

      val res = callService.activateSubAccountForOrg(
        org_id = org_id
      )

      res match {
        case Left(ActivatingSubAccountError.ErrorWhileActivatingSubAccount(err)) =>

          assert(error.getMessage == err.getMessage)

        case _ =>

          assert(false)

      }

    }

    it("should fail while saving sus-pended account data in db  ") {

      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Success(Some(sub_account_details.copy(status = SubAccountStatus.Suspended))))

      (twilioDialerService.activateSubAccount(_: TwlSubAccountSid)(using _: SRLogger))
        .expects(sub_account_details.sub_account_id, *)
        .returning(Success(twilio_sub_account_details))

      (callDAO.updateSubAccountStatus)
        .expects(twilio_sub_account_details, org_id)
        .returning(Failure(error))

      val res = callService.activateSubAccountForOrg(
        org_id = org_id
      )

      res match {
        case Left(ActivatingSubAccountError.ErrorWhileUpdatingStatusInDB(err)) =>

          assert(error.getMessage == err.getMessage)

        case _ =>

          assert(false)

      }

    }

    it("should success in activating sub-account and saving data in db ") {

      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Success(Some(sub_account_details.copy(status = SubAccountStatus.Suspended))))

      (twilioDialerService.activateSubAccount(_: TwlSubAccountSid)(using _: SRLogger))
        .expects(sub_account_details.sub_account_id, *)
        .returning(Success(twilio_sub_account_details))

      (callDAO.updateSubAccountStatus)
        .expects(twilio_sub_account_details, org_id)
        .returning(Success(1))

      val res = callService.activateSubAccountForOrg(
        org_id = org_id
      )

      res match {

        case Right(rows_updated) =>

          assert(true)

        case _ =>

          assert(false)

      }

    }


  }

  val add_calling_credit = AddCallingCreditDetails(
    org_id = OrgId(
      id = 20
    ),
    credit_added_dollar = 100L,
    credit_added_cents = 4300,
    credit_unit = CurrencyType.USD,
    credit_added_at = DateTime.now()
  )


  describe("testing callService.addCallingCredits when account is active ") {

    val usage_limit_for_trigger = ((sub_account_details.call_credits_cents + add_calling_credit.credit_added_cents) / 100).toString
    println(usage_limit_for_trigger)

    it("should fail while fetching sub-accounts from db") {

      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Failure(error))

      (organizationDAOService.getOrgWithCurrentData(_: OrgId)(_: SRLogger))
        .expects(org_id, *)
        .returning(Success(Some(org)))


      val res = callService.addCallingCredits(
        org_id = org_id,
        credit_details = add_calling_credit
      )

      println(s"res : ${res}")

      res.map {

        case Left(AddCallingCreditsError.SubAccountError(err)) =>
          assert(true)


        case _ =>
          assert(false)

      }.recover { e =>

        assert(false)

      }
    }
    //FIXME: FIX THIS TEST

    it("should fail while fetching sub-accounts as db returns None") {

      (organizationDAOService.getOrgWithCurrentData(_: OrgId)(_: SRLogger))
        .expects(org_id, *)
        .returning(Success(Some(org)))

      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Success(None))

      (() => twilioDialerService.createSubAccount())
        .expects()
        .returning(Success(twilio_sub_account_details))

      (() => srUuidUtils.generateSubAccountUuid())
        .expects()
        .returning(sub_account_uuid.uuid)

      (callDAO.saveSubAccountDetails)
        .expects(*)
        .returning(Success(sub_account_uuid))

      (twilioDialerService.createUsageTrigger(_: OrgId, _: String, _: TwlSubAccountSid, _: TwlAuthToken)(using _: SRLogger))
        .expects(org_id, "0", twl_sub_account_sid, twl_sub_account_auth_token, *)
        .returning(Success(twilio_usage_trigger_details))

      (callDAO.saveTwilioTriggerDetails)
        .expects(twilio_usage_trigger_details)
        .returning(Success(trigger_id))

      (twilioDialerService.createApiKeySecretForSubAccount(_: TwlSubAccountSid, _: TwlAuthToken, _: SubAccountUuid)(using _: SRLogger))
        .expects(twl_sub_account_sid, twl_sub_account_auth_token, sub_account_uuid, *)
        .returning(Success(subAccountApiKeyAndSecret))

      (callDAO.saveSubAccountEncryptionKeys)
        .expects(subAccountApiKeyAndSecret, sub_account_uuid, org_id)
        .returning(Success(Some(subAccountApiKeyAndSecret.api_key)))

      (twilioDialerService.createTwimlAppForSubAccounts(_: TwlSubAccountSid, _: TwlAuthToken, _: SubAccountUuid)(using _: SRLogger))
        .expects(twl_sub_account_sid, twl_sub_account_auth_token, sub_account_uuid, *)
        .returning(Success(subaccountTwilmlApplication))

      (callDAO.saveSubAccountTwimlApp)
        .expects(subaccountTwilmlApplication, sub_account_uuid, org_id)
        .returning(Success(Some(subaccountTwilmlApplication.app_name)))


      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Success(Some(sub_account_details.copy(trigger_id = Some(trigger_id)))))


      (twilioDialerService.createUsageTrigger(_: OrgId, _: String, _: TwlSubAccountSid, _: TwlAuthToken)(using _: SRLogger))
        .expects(org_id, usage_limit_for_trigger, twl_sub_account_sid, twl_sub_account_auth_token, *)
        .returning(Success(twilio_usage_trigger_details))

      (callDAO.saveTwilioTriggerDetails)
        .expects(twilio_usage_trigger_details)
        .returning(Success(twilio_usage_trigger_details.trigger_id))

      (twilioDialerService.deleteTrigger(_: TwlSubAccountSid, _: TwlAuthToken, _: String)(using _: SRLogger))
        .expects(twl_sub_account_sid, twl_sub_account_auth_token, trigger_id, *)
        .returning(Success(true))

      (callDAO.updateMainCreditForSubAccount)
        .expects(sub_account_details.copy(trigger_id = Some(trigger_id)), add_calling_credit)
        .returning(Success(0))


      val res = callService.addCallingCredits(
        org_id: OrgId,
        credit_details = add_calling_credit
      )


      res.map {

          case Left(err) =>


            assert(false)

          case Right(value) =>

            assert(true)

        }
        .recover { e =>

          assert(false)

        }
    }

    // testing when account is active
    it("should fail while creating usage trigger") {

      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Success(Some(sub_account_details.copy(status = SubAccountStatus.Active)))).atLeastTwice()

      (organizationDAOService.getOrgWithCurrentData(_: OrgId)(_: SRLogger))
        .expects(org_id, *)
        .returning(Success(Some(org)))

      (twilioDialerService.createUsageTrigger(_: OrgId, _: String, _: TwlSubAccountSid, _: TwlAuthToken)(using _: SRLogger))
        .expects(org_id, usage_limit_for_trigger, twl_sub_account_sid, twl_sub_account_auth_token, *)
        .returning(Failure(error))

      val res = callService.addCallingCredits(
        org_id: OrgId,
        credit_details = add_calling_credit
      )

      res.map {

        case Left(AddCallingCreditsError.TriggerCreateError(err)) =>


          err match {

            case CreateUsageTriggerError.ErrorWhileCreatingNewUsageTriggerInTwilio(err) =>

              assert(err.getMessage == error.getMessage)

            case _ =>

              assert(false)

          }


        case _ =>
          assert(false)

      }.recover { e =>

        assert(false)

      }
    }

    it("should fail while saving newly created usage trigger") {

      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Success(Some(sub_account_details.copy(status = SubAccountStatus.Active)))).atLeastTwice()

      (organizationDAOService.getOrgWithCurrentData(_: OrgId)(_: SRLogger))
        .expects(org_id, *)
        .returning(Success(Some(org)))

      (twilioDialerService.createUsageTrigger(_: OrgId, _: String, _: TwlSubAccountSid, _: TwlAuthToken)(using _: SRLogger))
        .expects(org_id, usage_limit_for_trigger, twl_sub_account_sid, twl_sub_account_auth_token, *)
        .returning(Success(twilio_usage_trigger_details))

      (callDAO.saveTwilioTriggerDetails)
        .expects(twilio_usage_trigger_details)
        .returning(Failure(error))

      //      (twilioDialerService.deleteTrigger(_: TwlSubAccountSid, _: TwlAuthToken, _: String)(_: SRLogger))
      //        .expects(twl_sub_account_sid, twl_sub_account_auth_token,trigger_id, *)
      //        .returning(Failure(error))

      val res = callService.addCallingCredits(
        org_id: OrgId,
        credit_details = add_calling_credit
      )

      res.map {

        case Left(AddCallingCreditsError.TriggerCreateError(err)) =>


          err match {

            case CreateUsageTriggerError.ErrorWhileSavingUsageTriggerInDB(err) =>

              assert(err.getMessage == error.getMessage)

            case _ =>

              assert(false)

          }


        case _ =>
          assert(false)

      }.recover { e =>

        assert(false)

      }
    }


    it("should fail while deleting previous usage trigger if no trigger-id is found in db") {

      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Success(Some(sub_account_details.copy(status = SubAccountStatus.Active)))).atLeastTwice()

      (organizationDAOService.getOrgWithCurrentData(_: OrgId)(_: SRLogger))
        .expects(org_id, *)
        .returning(Success(Some(org)))

      (twilioDialerService.createUsageTrigger(_: OrgId, _: String, _: TwlSubAccountSid, _: TwlAuthToken)(using _: SRLogger))
        .expects(org_id, usage_limit_for_trigger, twl_sub_account_sid, twl_sub_account_auth_token, *)
        .returning(Success(twilio_usage_trigger_details))

      (callDAO.saveTwilioTriggerDetails)
        .expects(twilio_usage_trigger_details)
        .returning(Success(twilio_usage_trigger_details.trigger_id))

      //      (twilioDialerService.deleteTrigger(_: TwlSubAccountSid, _: TwlAuthToken, _: String)(_: SRLogger))
      //        .expects(twl_sub_account_sid, twl_sub_account_auth_token,trigger_id, *)
      //        .returning(Failure(error))

      val res = callService.addCallingCredits(
        org_id: OrgId,
        credit_details = add_calling_credit
      )

      res.map {

        case Left(AddCallingCreditsError.TriggerDeleteError(err)) =>


          err match {

            case DeleteUsageTriggerError.TriggerIdNotFoundInDB =>

              println("here")

              assert(true)

            case _ =>

              assert(false)

          }


        case _ =>
          assert(false)

      }.recover { e =>

        assert(false)

      }
    }


    it("should fail while deleting previous usage trigger") {

      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Success(Some(sub_account_details.copy(status = SubAccountStatus.Active, trigger_id = Some(trigger_id))))).atLeastTwice()

      (organizationDAOService.getOrgWithCurrentData(_: OrgId)(_: SRLogger))
        .expects(org_id, *)
        .returning(Success(Some(org)))

      (twilioDialerService.createUsageTrigger(_: OrgId, _: String, _: TwlSubAccountSid, _: TwlAuthToken)(using _: SRLogger))
        .expects(org_id, usage_limit_for_trigger, twl_sub_account_sid, twl_sub_account_auth_token, *)
        .returning(Success(twilio_usage_trigger_details))

      (callDAO.saveTwilioTriggerDetails)
        .expects(twilio_usage_trigger_details)
        .returning(Success(twilio_usage_trigger_details.trigger_id))

      (twilioDialerService.deleteTrigger(_: TwlSubAccountSid, _: TwlAuthToken, _: String)(using _: SRLogger))
        .expects(twl_sub_account_sid, twl_sub_account_auth_token, trigger_id, *)
        .returning(Failure(error))

      val res = callService.addCallingCredits(
        org_id = org_id,
        credit_details = add_calling_credit
      )
      res.map {

        case Left(AddCallingCreditsError.TriggerDeleteError(err)) =>


          err match {

            case DeleteUsageTriggerError.TwilioErrorWhileDeletingTrigger(err) =>

              assert(err.getMessage == error.getMessage)

            case _ =>

              assert(false)

          }


        case _ =>
          assert(false)

      }.recover { e =>

        assert(false)

      }
    }

    it("should fail when updating balance in db") {

      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Success(Some(sub_account_details.copy(trigger_id = Some(trigger_id))))).atLeastTwice()

      (organizationDAOService.getOrgWithCurrentData(_: OrgId)(_: SRLogger))
        .expects(org_id, *)
        .returning(Success(Some(org)))

      (twilioDialerService.createUsageTrigger(_: OrgId, _: String, _: TwlSubAccountSid, _: TwlAuthToken)(using _: SRLogger))
        .expects(org_id, usage_limit_for_trigger, twl_sub_account_sid, twl_sub_account_auth_token, *)
        .returning(Success(twilio_usage_trigger_details))

      (callDAO.saveTwilioTriggerDetails)
        .expects(twilio_usage_trigger_details)
        .returning(Success(twilio_usage_trigger_details.trigger_id))

      (twilioDialerService.deleteTrigger(_: TwlSubAccountSid, _: TwlAuthToken, _: String)(using _: SRLogger))
        .expects(twl_sub_account_sid, twl_sub_account_auth_token, trigger_id, *)
        .returning(Success(true))

      (callDAO.updateMainCreditForSubAccount)
        .expects(sub_account_details.copy(trigger_id = Some(trigger_id)), add_calling_credit)
        .returning(Failure(error))

      val res = callService.addCallingCredits(
        org_id = org_id,
        credit_details = add_calling_credit
      )

      res.map {

        case Left(AddCallingCreditsError.ErrorWhileUpdatingCreditsInDB(err)) =>

          assert(err.getMessage == error.getMessage)

        case _ =>
          assert(false)

      }.recover { e =>

        assert(false)

      }
    }

    it("should success in active cases") {

      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Success(Some(sub_account_details.copy(trigger_id = Some(trigger_id))))).atLeastTwice()

      (organizationDAOService.getOrgWithCurrentData(_: OrgId)(_: SRLogger))
        .expects(org_id, *)
        .returning(Success(Some(org)))

      (twilioDialerService.createUsageTrigger(_: OrgId, _: String, _: TwlSubAccountSid, _: TwlAuthToken)(using _: SRLogger))
        .expects(org_id, usage_limit_for_trigger, twl_sub_account_sid, twl_sub_account_auth_token, *)
        .returning(Success(twilio_usage_trigger_details))

      (callDAO.saveTwilioTriggerDetails)
        .expects(twilio_usage_trigger_details)
        .returning(Success(twilio_usage_trigger_details.trigger_id))

      (twilioDialerService.deleteTrigger(_: TwlSubAccountSid, _: TwlAuthToken, _: String)(using _: SRLogger))
        .expects(twl_sub_account_sid, twl_sub_account_auth_token, trigger_id, *)
        .returning(Success(true))

      (callDAO.updateMainCreditForSubAccount)
        .expects(sub_account_details.copy(trigger_id = Some(trigger_id)), add_calling_credit)
        .returning(Success(0))

      val res = callService.addCallingCredits(
        org_id = org_id,
        credit_details = add_calling_credit
      )

      res.map {

        case Right(0) =>

          assert(true)

        case _ =>
          assert(false)

      }.recover { e =>

        assert(false)

      }
    }

    it("should success in active cases and db returns 0") {

      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Success(Some(sub_account_details.copy(trigger_id = Some(trigger_id))))).atLeastTwice()

      (organizationDAOService.getOrgWithCurrentData(_: OrgId)(_: SRLogger))
        .expects(org_id, *)
        .returning(Success(Some(org)))

      (twilioDialerService.createUsageTrigger(_: OrgId, _: String, _: TwlSubAccountSid, _: TwlAuthToken)(using _: SRLogger))
        .expects(org_id, usage_limit_for_trigger, twl_sub_account_sid, twl_sub_account_auth_token, *)
        .returning(Success(twilio_usage_trigger_details))

      (callDAO.saveTwilioTriggerDetails)
        .expects(twilio_usage_trigger_details)
        .returning(Success(twilio_usage_trigger_details.trigger_id))

      (twilioDialerService.deleteTrigger(_: TwlSubAccountSid, _: TwlAuthToken, _: String)(using _: SRLogger))
        .expects(twl_sub_account_sid, twl_sub_account_auth_token, trigger_id, *)
        .returning(Success(true))

      (callDAO.updateMainCreditForSubAccount)
        .expects(sub_account_details.copy(trigger_id = Some(trigger_id)), add_calling_credit)
        .returning(Success(1))

      val res = callService.addCallingCredits(
        org_id = org_id,
        credit_details = add_calling_credit
      )

      Await.result(res, Duration.create(15, SECONDS))

      res.map {

        case Right(1) =>
          println(s"res should success in active cases and db returns 0 : ${res}")


          assert(true)

        case _ =>
          println(s"res should success in active cases and db returns 0 : ${res}")
          assert(false)

      }.recover { e =>

        assert(false)

      }
    }


    it("should convert inr to usd then proceed ahead") {

      val converted_credit_details = add_calling_credit.copy(
        credit_unit = CurrencyType.USD,
        credit_added_cents = 8000,
        credit_added_dollar = 8000 / 100
      )

      (apiLayerService.convertCurrencyRate(
        _: CurrencyType,
        _: CurrencyType,
        _: Float,
        _: String
      )(_: WSClient,
        _: ExecutionContext,
        _: ISRLogger)
        )
        .expects(CurrencyType.INR, CurrencyType.USD, (8000).toFloat, AppConfig.ApiLayerCurrencyService.apiKey, wsClient, ec, logger)
        .returning(Future.successful(Some(ConvertedCurrency(result = 8000 / 100))))

      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Success(Some(sub_account_details.copy(trigger_id = Some(trigger_id))))).atLeastTwice()

      (organizationDAOService.getOrgWithCurrentData(_: OrgId)(_: SRLogger))
        .expects(org_id, *)
        .returning(Success(Some(org)))

      (twilioDialerService.createUsageTrigger(_: OrgId, _: String, _: TwlSubAccountSid, _: TwlAuthToken)(using _: SRLogger))
        .expects(org_id, ((sub_account_details.call_credits_cents + converted_credit_details.credit_added_cents) / 100).toString, twl_sub_account_sid, twl_sub_account_auth_token, logger)
        .returning(Success(twilio_usage_trigger_details))

      (callDAO.saveTwilioTriggerDetails)
        .expects(twilio_usage_trigger_details)
        .returning(Success(twilio_usage_trigger_details.trigger_id))

      (twilioDialerService.deleteTrigger(_: TwlSubAccountSid, _: TwlAuthToken, _: String)(using _: SRLogger))
        .expects(twl_sub_account_sid, twl_sub_account_auth_token, trigger_id, logger)
        .returning(Success(true))

      (callDAO.updateMainCreditForSubAccount)
        .expects(sub_account_details.copy(trigger_id = Some(trigger_id)), converted_credit_details)
        .returning(Success(1))

      val res = callService.addCallingCredits(

        org_id = org_id,
        credit_details = add_calling_credit.copy(
          credit_unit = CurrencyType.INR,
          credit_added_dollar = 8000,
          credit_added_cents = 800000,
        )
      )

      Await.result(res, Duration.Inf)

      res.map {

        case Right(1) =>
          println(s"res should success in active cases and db returns 0 : ${res}")


          assert(true)

        case _ =>
          println(s"res should success in active cases and db returns 0 : ${res}")
          assert(false)

      }.recover { e =>

        assert(false)

      }
    }


  }

  describe("testing callService.addCallingCredits when account is suspended ") {

    val usage_limit = (sub_account_details.call_credits_cents + add_calling_credit.credit_added_cents).toString

    it("should fail while fetching sub-accounts from db") {

      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Failure(error))

      (organizationDAOService.getOrgWithCurrentData(_: OrgId)(_: SRLogger))
        .expects(org_id, *)
        .returning(Success(Some(org)))

      val res = callService.addCallingCredits(
        org_id: OrgId,
        credit_details = add_calling_credit
      )

      res.map {

          case Left(AddCallingCreditsError.SubAccountError(error)) =>

            assert(true)


          case _ =>

            assert(false)

        }
        .recover(err => assert(false))

      //      assert(res == Future.successful(Left(AddCallingCreditsError.ErrorWhileFetchingSubAccountDetails(GetSubAccountDetailsError.SQLExceptionError(error)))))

    }

    // testing when account is active
    it("should fail as over-usage is not paid") {

      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Success(Some(sub_account_details.copy(status = SubAccountStatus.Suspended,
          twl_trigger_current_value_cents = Some(3300L)
        )))).atLeastTwice()

      (organizationDAOService.getOrgWithCurrentData(_: OrgId)(_: SRLogger))
        .expects(org_id, *)
        .returning(Success(Some(org)))


      val res = callService.addCallingCredits(
        org_id: OrgId,
        credit_details = add_calling_credit.copy(credit_added_cents = 250)
      )

      res.map {

          case Left(AddCallingCreditsError.LessCreditAddedError(300, 250)) =>

            assert(true)

          case _ =>

            assert(false)

        }
        .recover(err => assert(false))

    }

    it("should fail as twl_current_value is not defined") {

      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Success(Some(sub_account_details.copy(status = SubAccountStatus.Suspended)))).atLeastTwice()

      (organizationDAOService.getOrgWithCurrentData(_: OrgId)(_: SRLogger))
        .expects(org_id, *)
        .returning(Success(Some(org)))

      val res = callService.addCallingCredits(
        org_id: OrgId,
        credit_details = add_calling_credit.copy(credit_added_cents = 250)
      )

      res.map {

          case Left(AddCallingCreditsError.TwilioTriggerCurrentValueNotPresetError) =>

            assert(true)

          case _ =>

            assert(false)

        }
        .recover(err => assert(false))

    }

    it("should fail while activating sub-account-for-org ") {

      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Success(Some(sub_account_details.copy(status = SubAccountStatus.Suspended,
          twl_trigger_current_value_cents = Some(3300L)
        ))))

      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Success(Some(sub_account_details.copy(status = SubAccountStatus.Suspended,
          twl_trigger_current_value_cents = Some(3300L)
        )))).atLeastTwice()

      (organizationDAOService.getOrgWithCurrentData(_: OrgId)(_: SRLogger))
        .expects(org_id, *)
        .returning(Success(Some(org)))

      (twilioDialerService.activateSubAccount(_: TwlSubAccountSid)(using _: SRLogger))
        .expects(sub_account_details.sub_account_id, *)
        .returning(Failure(error))

      val res = callService.addCallingCredits(
        org_id: OrgId,
        credit_details = add_calling_credit.copy(credit_added_cents = 4000)
      )

      res.map {

          case Left(AddCallingCreditsError.NotAbleToActivateAccount(ActivatingSubAccountError.ErrorWhileActivatingSubAccount(error))) =>

            assert(true)

          case _ =>

            assert(false)

        }
        .recover(err => assert(false))

    }

    it("should fail while saving updated status after activating account in db ") {

      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Success(Some(sub_account_details.copy(status = SubAccountStatus.Suspended,
          twl_trigger_current_value_cents = Some(3300L)
        ))))

      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Success(Some(sub_account_details.copy(status = SubAccountStatus.Suspended,
          twl_trigger_current_value_cents = Some(3300L)
        )))).atLeastTwice()

      (organizationDAOService.getOrgWithCurrentData(_: OrgId)(_: SRLogger))
        .expects(org_id, *)
        .returning(Success(Some(org)))

      (twilioDialerService.activateSubAccount(_: TwlSubAccountSid)(using _: SRLogger))
        .expects(sub_account_details.sub_account_id, *)
        .returning(Success(twilio_sub_account_details))

      (callDAO.updateSubAccountStatus)
        .expects(twilio_sub_account_details, org_id)
        .returning(Failure(error))

      val res = callService.addCallingCredits(
        org_id,
        credit_details = add_calling_credit.copy(credit_added_cents = 4000)
      )


      res.map {

          case Left(AddCallingCreditsError.NotAbleToActivateAccount(ActivatingSubAccountError.ErrorWhileUpdatingStatusInDB(error))) =>

            assert(true)

          case _ =>

            assert(false)

        }
        .recover(err => assert(false))

    }

    it("should fail while deleting previous trigger  ") {

      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Success(Some(sub_account_details.copy(status = SubAccountStatus.Suspended,
          twl_trigger_current_value_cents = Some(3300L),
          trigger_id = Some(trigger_id)
        )))).repeat(3)

      (organizationDAOService.getOrgWithCurrentData(_: OrgId)(_: SRLogger))
        .expects(org_id, *)
        .returning(Success(Some(org)))

      (twilioDialerService.activateSubAccount(_: TwlSubAccountSid)(using _: SRLogger))
        .expects(sub_account_details.sub_account_id, *)
        .returning(Success(twilio_sub_account_details))

      (callDAO.updateSubAccountStatus)
        .expects(twilio_sub_account_details, org_id)
        .returning(Success(1))

      (twilioDialerService.deleteTrigger(_: TwlSubAccountSid, _: TwlAuthToken, _: String)(using _: SRLogger))
        .expects(twl_sub_account_sid, twl_sub_account_auth_token, trigger_id, *)
        .returning(Failure(error))

      val res = callService.addCallingCredits(
        org_id,
        credit_details = add_calling_credit.copy(credit_added_cents = 4000)
      )


      res.map {

          case Left(AddCallingCreditsError.TriggerDeleteError(DeleteUsageTriggerError.TwilioErrorWhileDeletingTrigger(error))) =>

            assert(true)

          case _ =>

            assert(false)

        }
        .recover(err => assert(false))


    }

    it("should fail while creating new trigger ") {


      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Success(Some(sub_account_details.copy(status = SubAccountStatus.Suspended,
          twl_trigger_current_value_cents = Some(3300L),
          trigger_id = Some(trigger_id)
        )))).repeat(3)

      (organizationDAOService.getOrgWithCurrentData(_: OrgId)(_: SRLogger))
        .expects(org_id, *)
        .returning(Success(Some(org)))

      (twilioDialerService.activateSubAccount(_: TwlSubAccountSid)(using _: SRLogger))
        .expects(sub_account_details.sub_account_id, *)
        .returning(Success(twilio_sub_account_details))

      (callDAO.updateSubAccountStatus)
        .expects(twilio_sub_account_details, org_id)
        .returning(Success(1))

      (twilioDialerService.deleteTrigger(_: TwlSubAccountSid, _: TwlAuthToken, _: String)(using _: SRLogger))
        .expects(twl_sub_account_sid, twl_sub_account_auth_token, trigger_id, *)
        .returning(Success(true))

      (twilioDialerService.createUsageTrigger(_: OrgId, _: String, _: TwlSubAccountSid, _: TwlAuthToken)(using _: SRLogger))
        .expects(org_id, ((add_calling_credit.copy(credit_added_cents = 4000).credit_added_cents + sub_account_details.call_credits_cents) / 100).toString, twl_sub_account_sid, twl_sub_account_auth_token, *)
        .returning(Failure(error))

      val res = callService.addCallingCredits(
        org_id,
        credit_details = add_calling_credit.copy(credit_added_cents = 4000)
      )

      res.map {

          case Left(AddCallingCreditsError.TriggerCreateError(CreateUsageTriggerError.ErrorWhileCreatingNewUsageTriggerInTwilio(error))) =>

            assert(true)

          case _ =>

            assert(false)

        }
        .recover(err => assert(false))

    }

    it("should fail while saving new trigger details") {

      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Success(Some(sub_account_details.copy(status = SubAccountStatus.Suspended,
          twl_trigger_current_value_cents = Some(3300L),
          trigger_id = Some(trigger_id)
        )))).repeat(3)

      (organizationDAOService.getOrgWithCurrentData(_: OrgId)(_: SRLogger))
        .expects(org_id, *)
        .returning(Success(Some(org)))

      (twilioDialerService.activateSubAccount(_: TwlSubAccountSid)(using _: SRLogger))
        .expects(sub_account_details.sub_account_id, *)
        .returning(Success(twilio_sub_account_details))

      (callDAO.updateSubAccountStatus)
        .expects(twilio_sub_account_details, org_id)
        .returning(Success(1))

      (twilioDialerService.deleteTrigger(_: TwlSubAccountSid, _: TwlAuthToken, _: String)(using _: SRLogger))
        .expects(twl_sub_account_sid, twl_sub_account_auth_token, trigger_id, *)
        .returning(Success(true))

      (twilioDialerService.createUsageTrigger(_: OrgId, _: String, _: TwlSubAccountSid, _: TwlAuthToken)(using _: SRLogger))
        .expects(org_id, ((add_calling_credit.copy(credit_added_cents = 4000).credit_added_cents + sub_account_details.call_credits_cents) / 100).toString, twl_sub_account_sid, twl_sub_account_auth_token, *)
        .returning(Success(twilio_usage_trigger_details))

      (callDAO.saveTwilioTriggerDetails)
        .expects(twilio_usage_trigger_details)
        .returning(Failure(error))

      val res = callService.addCallingCredits(
        org_id,
        credit_details = add_calling_credit.copy(credit_added_cents = 4000)
      )

      res.map {

          case Left(AddCallingCreditsError.TriggerCreateError(CreateUsageTriggerError.ErrorWhileSavingUsageTriggerInDB(error))) =>

            assert(true)

          case _ =>

            assert(false)

        }
        .recover(err => assert(false))


    }

    it("should fail while updating main credits in db") {

      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Success(Some(sub_account_details.copy(status = SubAccountStatus.Suspended,
          twl_trigger_current_value_cents = Some(3300L),
          trigger_id = Some(trigger_id)
        )))).repeat(3)

      (organizationDAOService.getOrgWithCurrentData(_: OrgId)(_: SRLogger))
        .expects(org_id, *)
        .returning(Success(Some(org)))

      (twilioDialerService.activateSubAccount(_: TwlSubAccountSid)(using _: SRLogger))
        .expects(sub_account_details.sub_account_id, *)
        .returning(Success(twilio_sub_account_details))

      (callDAO.updateSubAccountStatus)
        .expects(twilio_sub_account_details, org_id)
        .returning(Success(1))

      (twilioDialerService.deleteTrigger(_: TwlSubAccountSid, _: TwlAuthToken, _: String)(using _: SRLogger))
        .expects(twl_sub_account_sid, twl_sub_account_auth_token, trigger_id, *)
        .returning(Success(true))

      (twilioDialerService.createUsageTrigger(_: OrgId, _: String, _: TwlSubAccountSid, _: TwlAuthToken)(using _: SRLogger))
        .expects(org_id, ((add_calling_credit.copy(credit_added_cents = 4000).credit_added_cents + sub_account_details.call_credits_cents) / 100).toString, twl_sub_account_sid, twl_sub_account_auth_token, *)
        .returning(Success(twilio_usage_trigger_details))

      (callDAO.saveTwilioTriggerDetails)
        .expects(twilio_usage_trigger_details)
        .returning(Success(twilio_usage_trigger_details.sub_account_sid))

      (callDAO.updateMainCreditForSubAccount)
        .expects(sub_account_details.copy(status = SubAccountStatus.Suspended,
          twl_trigger_current_value_cents = Some(3300L),
          trigger_id = Some(trigger_id)
        ), add_calling_credit.copy(credit_added_cents = 4000))
        .returning(Failure(error))

      val res = callService.addCallingCredits(
        org_id,
        credit_details = add_calling_credit.copy(credit_added_cents = 4000)
      )

      res.map {

          case Left(AddCallingCreditsError.ErrorWhileUpdatingCreditsInDB(error)) =>

            assert(true)

          case _ =>

            assert(false)

        }
        .recover(err => assert(false))

    }

    it("should succeed and updating main credits in db") {

      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Success(Some(sub_account_details.copy(status = SubAccountStatus.Suspended,
          twl_trigger_current_value_cents = Some(3300L),
          trigger_id = Some(trigger_id)
        )))).repeat(3)

      (organizationDAOService.getOrgWithCurrentData(_: OrgId)(_: SRLogger))
        .expects(org_id, *)
        .returning(Success(Some(org)))

      (twilioDialerService.activateSubAccount(_: TwlSubAccountSid)(using _: SRLogger))
        .expects(sub_account_details.sub_account_id, *)
        .returning(Success(twilio_sub_account_details))

      (callDAO.updateSubAccountStatus)
        .expects(twilio_sub_account_details, org_id)
        .returning(Success(1))

      (twilioDialerService.deleteTrigger(_: TwlSubAccountSid, _: TwlAuthToken, _: String)(using _: SRLogger))
        .expects(twl_sub_account_sid, twl_sub_account_auth_token, trigger_id, *)
        .returning(Success(true))

      (twilioDialerService.createUsageTrigger(_: OrgId, _: String, _: TwlSubAccountSid, _: TwlAuthToken)(using _: SRLogger))
        .expects(org_id, ((add_calling_credit.copy(credit_added_cents = 4000).credit_added_cents + sub_account_details.call_credits_cents) / 100).toString, twl_sub_account_sid, twl_sub_account_auth_token, *)
        .returning(Success(twilio_usage_trigger_details))

      (callDAO.saveTwilioTriggerDetails)
        .expects(twilio_usage_trigger_details)
        .returning(Success(twilio_usage_trigger_details.sub_account_sid))

      (callDAO.updateMainCreditForSubAccount)
        .expects(sub_account_details.copy(status = SubAccountStatus.Suspended,
          twl_trigger_current_value_cents = Some(3300L),
          trigger_id = Some(trigger_id)
        ), add_calling_credit.copy(credit_added_cents = 4000))
        .returning(Success(1))

      val res = callService.addCallingCredits(
        org_id,
        credit_details = add_calling_credit.copy(credit_added_cents = 4000)
      )


      res.map {

          case Right(1) =>

            assert(true)

          case _ =>

            assert(false)

        }
        .recover(err => assert(false))

    }

    it("should succeed and updating main credits in db checking edge case") {

      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Success(Some(sub_account_details.copy(status = SubAccountStatus.Suspended,
          twl_trigger_current_value_cents = Some(3273L),
          trigger_id = Some(trigger_id),
          call_credits_cents = 3000L
        )))).repeat(3)

      (organizationDAOService.getOrgWithCurrentData(_: OrgId)(_: SRLogger))
        .expects(org_id, *)
        .returning(Success(Some(org)))

      (twilioDialerService.activateSubAccount(_: TwlSubAccountSid)(using _: SRLogger))
        .expects(sub_account_details.sub_account_id, *)
        .returning(Success(twilio_sub_account_details))

      (callDAO.updateSubAccountStatus)
        .expects(twilio_sub_account_details, org_id)
        .returning(Success(1))

      (twilioDialerService.deleteTrigger(_: TwlSubAccountSid, _: TwlAuthToken, _: String)(using _: SRLogger))
        .expects(twl_sub_account_sid, twl_sub_account_auth_token, trigger_id, *)
        .returning(Success(true))

      (twilioDialerService.createUsageTrigger(_: OrgId, _: String, _: TwlSubAccountSid, _: TwlAuthToken)(using _: SRLogger))
        .expects(org_id, ((add_calling_credit.copy(credit_added_cents = 4000).credit_added_cents + sub_account_details.call_credits_cents) / 100).toString, twl_sub_account_sid, twl_sub_account_auth_token, *)
        .returning(Success(twilio_usage_trigger_details))

      (callDAO.saveTwilioTriggerDetails)
        .expects(twilio_usage_trigger_details)
        .returning(Success(twilio_usage_trigger_details.sub_account_sid))

      (callDAO.updateMainCreditForSubAccount)
        .expects(sub_account_details.copy(status = SubAccountStatus.Suspended,
          twl_trigger_current_value_cents = Some(3273L),
          trigger_id = Some(trigger_id),
        ), add_calling_credit.copy(credit_added_cents = 4000))
        .returning(Success(1))

      val res = callService.addCallingCredits(
        org_id,
        credit_details = add_calling_credit.copy(credit_added_cents = 4000)
      )

      res.map {

          case Right(1) =>

            assert(true)

          case _ =>

            assert(false)

        }
        .recover(err => assert(false))

    }
  }

  describe("testing CallService.checkIfOverUsagePaid") {

    // trigger current value is set as none in sub_account_details
    it("should fail when twl trigger value is None") {

      val res = CallService.checkIfOverUsagePaid(
        sub_account_details = sub_account_details,
        credit_detail = add_calling_credit
      )

      assert(res == Left(AddCallingCreditsError.TwilioTriggerCurrentValueNotPresetError))

    }

    it("should fail when overusage is greater than credit added") {

      val res = CallService.checkIfOverUsagePaid(
        sub_account_details = sub_account_details.copy(twl_trigger_current_value_cents = Some(3300L)),
        credit_detail = add_calling_credit.copy(credit_added_cents = 300)
      )

      assert(res == Left(AddCallingCreditsError.LessCreditAddedError(
        over_usage = 300,
        credit_added = 300
      )))

    }

    it("should succeed when overusage is less than credit added") {

      val res = CallService.checkIfOverUsagePaid(
        sub_account_details = sub_account_details.copy(twl_trigger_current_value_cents = Some(3300L)),
        credit_detail = add_calling_credit.copy(credit_added_cents = 3000)
      )

      assert(res == Right(300))

    }

  }

  describe("testing CallService.convertCurrencyToUSD") {

    it("should try converting currency and fail") {


      (apiLayerService.convertCurrencyRate(
        _: CurrencyType,
        _: CurrencyType,
        _: Float,
        _: String
      )(_: WSClient,
        _: ExecutionContext,
        _: ISRLogger)
        )
        .expects(CurrencyType.INR, CurrencyType.USD, (800000 / 100).toFloat, AppConfig.ApiLayerCurrencyService.apiKey, *, *, *)
        .returning(Future.failed(error))


      val res = callService.convertCurrencyToUSD(
        credit_details = add_calling_credit.copy(
          credit_unit = CurrencyType.INR,
          credit_added_cents = 800000,
          credit_added_dollar = 8000
        )
      )

      res.map {

          case Left(ErrorWhileCurrencyConversion.ExceptionOccured(error)) =>

            assert(true)

          case _ =>
            assert(false)

        }
        .recover(err => assert(false))
    }

    it("should try converting currency and returns none") {


      (apiLayerService.convertCurrencyRate(
        _: CurrencyType,
        _: CurrencyType,
        _: Float,
        _: String
      )(_: WSClient,
        _: ExecutionContext,
        _: ISRLogger)
        )
        .expects(CurrencyType.INR, CurrencyType.USD, (800000 / 100).toFloat, AppConfig.ApiLayerCurrencyService.apiKey, *, *, *)
        .returning(Future.successful(None))


      val res = callService.convertCurrencyToUSD(
        credit_details = add_calling_credit.copy(
          credit_unit = CurrencyType.INR,
          credit_added_cents = 800000,
          credit_added_dollar = 8000
        )
      )

      res.map {

          case Left(ErrorWhileCurrencyConversion.NoneFoundAfterConversion) =>

            assert(true)

          case _ =>
            assert(false)

        }
        .recover(err => assert(false))
    }

    it("should converting currency and returns usd") {


      (apiLayerService.convertCurrencyRate(
        _: CurrencyType,
        _: CurrencyType,
        _: Float,
        _: String
      )(_: WSClient,
        _: ExecutionContext,
        _: ISRLogger)
        )
        .expects(CurrencyType.INR, CurrencyType.USD, (800000 / 100).toFloat, AppConfig.ApiLayerCurrencyService.apiKey, wsClient, ec, logger)
        .returning(Future.successful(Some(ConvertedCurrency(result = add_calling_credit.credit_added_dollar.toFloat))))


      val res = callService.convertCurrencyToUSD(
        credit_details = add_calling_credit.copy(
          credit_unit = CurrencyType.INR,
          credit_added_cents = 800000,
          credit_added_dollar = 8000
        )
      )

      res.map {

          case Right(add_calling_credit) =>

            assert(true)

          case _ =>
            assert(false)

        }
        .recover(err => assert(false))
    }
  }


  val call_feedback_type_positive = CallFeedBackType.Positive
  val call_feedback_type_negative = CallFeedBackType.Negative
  val call_feedback_reasons = List(CallFeedBackReasons.UnSolicitedCall, CallFeedBackReasons.DroppedCall)
  val call_participant_uuid = CallParticipantUuid(participant_uuid = "uuid")

  val team_id = TeamId(id = 7)

  val call_feedback_response = CallFeedBackResponse(
    call_participant_uuid = call_participant_uuid,
    call_feedback_type = call_feedback_type_positive
  )

  val call_feedback = CallFeedBack(
    call_sid = call_sid,
    feedback_type = call_feedback_type_positive,
    feedback_reason = call_feedback_reasons,
    user_comment = Some("")
  )


  describe(" testing callService.saveCallFeedBack") {

    it("should fail while saving call-feedback to db") {

      (callDAO.saveCallFeedback)
        .expects(
          call_sid,
          team_id,
          call_feedback_type_positive,
          call_feedback_reasons.mkString(","),
          "")
        .returning(Failure(error))


      val res = callService.saveCallFeedBack(
        call_feedback = call_feedback,
        team_id = team_id,
        call_sid = call_sid
      )

      assert(res == Left(CallQualityFeedBackError.SqlExceptionError(error)))

    }

    it("should fail while saving call-feedback when query returns none") {

      (callDAO.saveCallFeedback)
        .expects(
          call_sid,
          team_id,
          call_feedback_type_positive,
          call_feedback_reasons.mkString(","),
          "")
        .returning(Success(None))


      val res = callService.saveCallFeedBack(
        call_feedback = call_feedback,
        team_id = team_id,
        call_sid = call_sid
      )

      assert(res == Left(CallQualityFeedBackError.NoneReceivedWhileSavingFeedback))

    }

    it("should success while saving call-feedback") {

      (callDAO.saveCallFeedback)
        .expects(
          call_sid,
          team_id,
          call_feedback_type_positive,
          call_feedback_reasons.mkString(","),
          "")
        .returning(Success(
          Some(call_feedback_response)))


      val res = callService.saveCallFeedBack(
        call_feedback = call_feedback,
        team_id = team_id,
        call_sid = call_sid
      )

      assert(res == Right(call_feedback_response))

    }

  }


  describe("testing callService.callQualityFeedback") {

    it("should fail while fetching sub-account-org as sql query fails and dao returns failure(error)") {


      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Failure(error))


      val res = callService.callQualityFeedback(
        call_sid = call_sid,
        team_id = team_id,
        call_feedback = call_feedback,
        org_id = org_id
      )

      assert(res == Left(CallQualityFeedBackError.ErrorWhileGettingSubAccount(error)))

    }

    it("should fail while fetching sub-account-org as dao returns None") {


      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Success(None))


      val res = callService.callQualityFeedback(
        call_sid = call_sid,
        team_id = team_id,
        call_feedback = call_feedback,
        org_id = org_id
      )

      res match {
        case Right(value) =>
          assert(false)

        case Left(err) =>

          err match {
            case CallQualityFeedBackError.NoneReceivedWhileSavingFeedback => assert(false)

            case CallQualityFeedBackError.ErrorWhileSendingFeedbackToTwilio(err) => assert(false)

            case CallQualityFeedBackError.SqlExceptionError(err) => assert(false)

            case CallQualityFeedBackError.ErrorWhileGettingSubAccount(err) =>
              assert(err.getMessage == CONSTANTS.API_MSGS.ERR_NO_SUBACCOUNTS_FOUND)


          }

      }

    }

    it("should fail while saving details as saving query fails returns None") {


      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Success(Some(sub_account_details)))

      (callDAO.saveCallFeedback)
        .expects(
          call_sid,
          team_id,
          call_feedback_type_positive,
          call_feedback_reasons.mkString(","),
          ""
        )
        .returning(Failure(error))


      val res = callService.callQualityFeedback(
        call_sid = call_sid,
        team_id = team_id,
        call_feedback = call_feedback,
        org_id = org_id
      )

      assert(res == Left(CallQualityFeedBackError.SqlExceptionError(error)))

    }

    it("should fail while sending details to twilio") {


      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Success(Some(sub_account_details)))

      (callDAO.saveCallFeedback)
        .expects(
          call_sid,
          team_id,
          call_feedback_type_positive,
          call_feedback_reasons.mkString(","),
          ""
        )
        .returning(Success(Some(call_feedback_response)))


      (twilioDialerService.twilioCallQualityFeedback(_: Int, _: List[CallFeedBackReasons], _: CallSID, _: TwlAuthToken, _: TwlSubAccountSid)(using _: SRLogger))
        .expects(
          5,
          call_feedback_reasons,
          call_sid: CallSID,
          twl_sub_account_auth_token,
          twl_sub_account_sid,
          *
        )
        .returning(Failure(error))


      val res = callService.callQualityFeedback(
        call_sid = call_sid,
        team_id = team_id,
        call_feedback = call_feedback,
        org_id = org_id
      )

      assert(res == Left(CallQualityFeedBackError.ErrorWhileSendingFeedbackToTwilio(error)))

    }

    it("should success - positive feedback") {


      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Success(Some(sub_account_details)))

      (callDAO.saveCallFeedback)
        .expects(
          call_sid,
          team_id,
          call_feedback_type_positive,
          call_feedback_reasons.mkString(","),
          ""
        )
        .returning(Success(Some(call_feedback_response)))


      (twilioDialerService.twilioCallQualityFeedback(_: Int, _: List[CallFeedBackReasons], _: CallSID, _: TwlAuthToken, _: TwlSubAccountSid)(using _: SRLogger))
        .expects(
          5,
          call_feedback_reasons,
          call_sid,
          twl_sub_account_auth_token,
          twl_sub_account_sid,
          *
        )
        .returning(Success(twl_sub_account_sid))


      val res = callService.callQualityFeedback(
        call_sid = call_sid,
        team_id = team_id,
        call_feedback = call_feedback,
        org_id = org_id
      )

      assert(res == Right(call_feedback_response))

    }

    it("should success - negative feedback ") {


      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Success(Some(sub_account_details)))

      (callDAO.saveCallFeedback)
        .expects(
          call_sid,
          team_id,
          call_feedback_type_negative,
          call_feedback_reasons.mkString(","),
          ""
        )
        .returning(Success(Some(call_feedback_response)))


      (twilioDialerService.twilioCallQualityFeedback(_: Int, _: List[CallFeedBackReasons], _: CallSID, _: TwlAuthToken, _: TwlSubAccountSid)(using _: SRLogger))
        .expects(
          2,
          call_feedback_reasons,
          call_sid,
          twl_sub_account_auth_token,
          twl_sub_account_sid,
          *
        )
        .returning(Success(twl_sub_account_sid))


      val res = callService.callQualityFeedback(
        call_sid = call_sid,
        team_id = team_id,
        call_feedback = call_feedback.copy(feedback_type = call_feedback_type_negative),
        org_id = org_id
      )

      assert(res == Right(call_feedback_response))

    }
  }


  describe("testing CallService.getCallScoreForFeedback") {

    it("when call feedback is negative should return 2") {

      val res = CallService.getCallScoreForFeedback(
        feedback_type = call_feedback_type_negative
      )

      assert(res == 2)

    }

    it("when call feedback is positive should return 5") {

      val res = CallService.getCallScoreForFeedback(
        feedback_type = call_feedback_type_positive
      )

      assert(res == 5)

    }
  }

  describe("testing CallService.is24HrPassed") {

    it("should return false if 24 hours is not passed") {


      val res = CallService.is24hrPassed(
        time = DateTime.now().minusHours(2)
      )

      assert(res == false)

    }

    it("should return true if 24 hours is passed") {


      val res = CallService.is24hrPassed(
        time = DateTime.now().minusHours(25)
      )

      assert(res == true)

    }


  }

  describe("testing callService.triggerMigartion") {

    it("should success if no org are present") {

      (() => callDAO.fetchOrgIdsFromOrgCallingCredits())
        .expects()
        .returning(Success(List()))

      val res = callService.triggerMigration()

      assert(res == Right(List()))

    }

    it("should success if many orgs are found") {

      (() => callDAO.fetchOrgIdsFromOrgCallingCredits())
        .expects()
        .returning(Success(List(OrgId(id = 20), OrgId(id = 30))))


      (callDAO.getSubAccountDetailsForOrg(_: Option[SubAccountUuid], _: OrgId))
        .expects(None, OrgId(id = 20))
        .returning(Success(Some(sub_account_details.copy(
          org_id = OrgId(id = 20),
          trigger_id = Some(trigger_id)))))

      (callDAO.getSubAccountDetailsForOrg(_: Option[SubAccountUuid], _: OrgId))
        .expects(None, OrgId(id = 30))
        .returning(Success(Some(sub_account_details.copy(
          org_id = OrgId(id = 30),
          trigger_id = Some(trigger_id)))))

      (twilioDialerService.deleteTrigger(_: TwlSubAccountSid, _: TwlAuthToken, _: String)(using _: SRLogger))
        .expects(sub_account_details.sub_account_id, sub_account_details.sub_auth_token, trigger_id, *)
        .returning(Success(true))
        .atLeastTwice()

      (twilioDialerService.createUsageTrigger(_: OrgId, _: String, _: TwlSubAccountSid, _: TwlAuthToken)(using _: SRLogger))
        .expects(OrgId(id = 20), *, sub_account_details.sub_account_id, sub_account_details.sub_auth_token, *)
        .returning(Success(twilio_usage_trigger_details))

      (twilioDialerService.createUsageTrigger(_: OrgId, _: String, _: TwlSubAccountSid, _: TwlAuthToken)(using _: SRLogger))
        .expects(OrgId(id = 30), *, sub_account_details.sub_account_id, sub_account_details.sub_auth_token, *)
        .returning(Success(twilio_usage_trigger_details))

      (callDAO.saveTwilioTriggerDetails(_: TwilioUsageTrigger))
        .expects(twilio_usage_trigger_details)
        .returning(Success(trigger_id))
        .atLeastTwice()

      val res = callService.triggerMigration()

      println(res)
      assert(res == Right(List(OrgId(id = 20), OrgId(id = 30))))


    }

  }


  describe("testing CallService.createHasMapOfCallSettingAndTeamId") {

    val teamIdAndAccount = TeamIdAndAccountDetails(
      team_id = call_account_settings.team_id,
      account_id = call_account_settings.owner_account_id
    )

    it("should convert List[CallAccountSettings] to Map[String, TeamId]") {

      val list_of_call_account_settings = List(call_account_settings)

      val res = CallService.createHashMapOfCallSettingAndTeamId(
        call_account_settings = list_of_call_account_settings
      )


      val expectation = Map(s"client:${call_account_settings.uuid.phone_number_uuid}" -> teamIdAndAccount,
        call_account_settings.phone_number.get.phone_number -> teamIdAndAccount
      )

      Future.successful(assert(res == expectation))

    }

    it("should convert List[CallAccountSettings] to Map[String, TeamId] more than one call_account_settings") {

      val list_of_call_account_settings = List(call_account_settings, call_account_settings.copy(
        uuid = PhoneNumberUuid("phone_number_uuid_number_2")
      ))

      val res = CallService.createHashMapOfCallSettingAndTeamId(
        call_account_settings = list_of_call_account_settings
      )

      val expectation = Map(s"client:${call_account_settings.uuid.phone_number_uuid}" -> teamIdAndAccount,
        call_account_settings.phone_number.get.phone_number -> teamIdAndAccount,
        s"client:phone_number_uuid_number_2" -> teamIdAndAccount
      )

      Future.successful(assert(res == expectation))

    }


    it("should convert when call account settings are empty") {

      val list_of_call_account_settings = List()

      val res = CallService.createHashMapOfCallSettingAndTeamId(
        call_account_settings = list_of_call_account_settings
      )

      val expectation = Map()

      Future.successful(assert(res == expectation))

    }

  }

  describe(" testing getAvailableCountries") {

    it("plan id is trial so should return only us and uk country") {

      val res = callService.getAvailableCountries(
        plan_id = PlanID.TRIAL
      )

      assert(res == callUtils.availableCountryLists.slice(0, 2))


    }

    it("plan id is not trial so should return all available country lists") {

      val res = callService.getAvailableCountries(
        plan_id = PlanID.PRO
      )

      assert(res == callUtils.availableCountryLists)


    }


  }

  describe("testing phone number formatter") {
    val expected_number = "+************"

    it("should give the number in correct format for dot format without prospect timezone and country code") {
      val data: PhoneNumberFormatData = PhoneNumberFormatData(
        prospect_tz = None,
        prospect_country = None,
        phone_numbers = Seq(PhoneNumber("************")),
        phone_number = None,
        user_tz = "Asia/Kolkata"
      )

      val res = callService.getFormattedPhoneNumber(
        data = data,
        tid = team_id
      )

      res match {

        case Failure(exception) => assert(false)

        case Success(value) => assert(value.head.phone_number == expected_number)
      }

    }

    it("should give the number in correct format for dot format without prospect timezone and with country code") {
      val data: PhoneNumberFormatData = PhoneNumberFormatData(
        prospect_tz = None,
        prospect_country = None,
        phone_numbers = Seq(PhoneNumber("+91************")),
        phone_number = None,
        user_tz = "Asia/Kolkata"
      )

      val res = callService.getFormattedPhoneNumber(
        data = data,
        tid = team_id
      )

      res match {

        case Failure(exception) => assert(false)

        case Success(value) => assert(value.head.phone_number == expected_number)
      }

    }

    it("should give the number in correct format for dot format with prospect timezone") {
      val data: PhoneNumberFormatData = PhoneNumberFormatData(
        prospect_tz = Some("Asia/Kolkata"),
        prospect_country = None,
        phone_numbers = Seq(PhoneNumber("************")),
        phone_number = None,
        user_tz = "Asia/Kolkata"
      )

      val res = callService.getFormattedPhoneNumber(
        data = data,
        tid = team_id
      )

      res match {

        case Failure(exception) => assert(false)

        case Success(value) => assert(value.head.phone_number == expected_number)
      }

    }

    it("should give the number in correct format for dot format with prospect timezone and country code") {
      val data: PhoneNumberFormatData = PhoneNumberFormatData(
        prospect_tz = Some("Asia/Kolkata"),
        prospect_country = None,
        phone_numbers = Seq(PhoneNumber("+91************")),
        phone_number = None,
        user_tz = "Asia/Kolkata"
      )

      val res = callService.getFormattedPhoneNumber(
        data = data,
        tid = team_id
      )

      res match {

        case Failure(exception) => assert(false)

        case Success(value) => assert(value.head.phone_number == expected_number)
      }

    }

    it("should give the number in correct format for dot format with prospect country") {
      val data: PhoneNumberFormatData = PhoneNumberFormatData(
        prospect_tz = None,
        prospect_country = Some("India"),
        phone_numbers = Seq(PhoneNumber("************")),
        phone_number = None,
        user_tz = "Asia/Kolkata"
      )

      val res = callService.getFormattedPhoneNumber(
        data = data,
        tid = team_id
      )

      res match {

        case Failure(exception) => assert(false)

        case Success(value) => assert(value.head.phone_number == expected_number)
      }

    }

    it("should give the number in correct format for dot format with prospect country and country code") {
      val data: PhoneNumberFormatData = PhoneNumberFormatData(
        prospect_tz = None,
        prospect_country = Some("India"),
        phone_numbers = Seq(PhoneNumber("+91************")),
        phone_number = None,
        user_tz = "Asia/Kolkata"
      )

      val res = callService.getFormattedPhoneNumber(
        data = data,
        tid = team_id
      )

      res match {

        case Failure(exception) => assert(false)

        case Success(value) => assert(value.head.phone_number == expected_number)
      }

    }

    it("should give the number in correct format for - format with prospect timezone") {
      val data: PhoneNumberFormatData = PhoneNumberFormatData(
        prospect_tz = Some("Asia/Kolkata"),
        prospect_country = None,
        phone_numbers = Seq(PhoneNumber("************")),
        phone_number = None,
        user_tz = "Asia/Kolkata"
      )

      val res = callService.getFormattedPhoneNumber(
        data = data,
        tid = team_id
      )

      res match {

        case Failure(exception) => assert(false)

        case Success(value) => assert(value.head.phone_number == expected_number)
      }

    }

    it("should fail for invalid data") {
      val data: PhoneNumberFormatData = PhoneNumberFormatData(
        prospect_tz = None,
        prospect_country = None,
        phone_numbers = Seq(PhoneNumber("************")),
        phone_number = None,
        user_tz = ""
      )

      val res = callService.getFormattedPhoneNumber(
        data = data,
        tid = team_id
      )

      res match {

        case Failure(exception) => assert(true)

        case Success(value) => assert(false)
      }

    }

    it("should fail for invalid timezone") {
      val data: PhoneNumberFormatData = PhoneNumberFormatData(
        prospect_tz = Some("India"),
        prospect_country = None,
        phone_numbers = Seq(PhoneNumber("************")),
        phone_number = None,
        user_tz = ""
      )

      val res = callService.getFormattedPhoneNumber(
        data = data,
        tid = team_id
      )

      res match {

        case Failure(exception) => assert(true)

        case Success(value) => assert(false)
      }

    }

    it("should fail for invalid country") {
      val data: PhoneNumberFormatData = PhoneNumberFormatData(
        prospect_tz = None,
        prospect_country = Some(""),
        phone_numbers = Seq(PhoneNumber("************")),
        phone_number = None,
        user_tz = ""
      )

      val res = callService.getFormattedPhoneNumber(
        data = data,
        tid = team_id
      )

      res match {

        case Failure(exception) => assert(true)

        case Success(value) => assert(false)
      }

    }

    it("should pass for valid number") {

      val data: PhoneNumberFormatData = PhoneNumberFormatData(
        prospect_tz = None,
        prospect_country = None,
        phone_numbers = Seq(PhoneNumber("+************")),
        phone_number = None,
        user_tz = "Asia/Kolkata"
      )

      val res = callService.getFormattedPhoneNumber(
        data = data,
        tid = team_id
      )

      res match {

        case Failure(exception) => assert(false)

        case Success(value) => assert(value.head.phone_number == expected_number)
      }

    }

    it("should pass if empty data sent for timezone") {
      val data: PhoneNumberFormatData = PhoneNumberFormatData(
        prospect_tz = Some(""),
        prospect_country = None,
        phone_numbers = Seq(PhoneNumber("************")),
        phone_number = None,
        user_tz = "Asia/Kolkata"
      )

      val res = callService.getFormattedPhoneNumber(
        data = data,
        tid = team_id
      )

      res match {

        case Failure(exception) => assert(false)

        case Success(value) => assert(value.head.phone_number == expected_number)
      }

    }

    it("should pass if empty data sent for country") {
      val data: PhoneNumberFormatData = PhoneNumberFormatData(
        prospect_tz = None,
        prospect_country = Some(""),
        phone_numbers = Seq(PhoneNumber("************")),
        phone_number = None,
        user_tz = "Asia/Kolkata"
      )

      val res = callService.getFormattedPhoneNumber(
        data = data,
        tid = team_id
      )

      res match {

        case Failure(exception) => assert(false)

        case Success(value) => assert(value.head.phone_number == expected_number)
      }

    }

    it("should pass for phone format () - ") {
      val data: PhoneNumberFormatData = PhoneNumberFormatData(
        prospect_tz = None,
        prospect_country = Some(""),
        phone_numbers = Seq(PhoneNumber("(*************")),
        phone_number = None,
        user_tz = "Asia/Kolkata"
      )

      val res = callService.getFormattedPhoneNumber(
        data = data,
        tid = team_id
      )

      res match {

        case Failure(exception) => assert(false)

        case Success(value) => assert(value.head.phone_number == expected_number)
      }

    }

    it("should pass for phone format () - -") {
      val data: PhoneNumberFormatData = PhoneNumberFormatData(
        prospect_tz = None,
        prospect_country = Some(""),
        phone_numbers = Seq(PhoneNumber("(*************")),
        phone_number = None,
        user_tz = "Asia/Kolkata"
      )

      val res = callService.getFormattedPhoneNumber(
        data = data,
        tid = team_id
      )

      res match {

        case Failure(exception) => assert(false)

        case Success(value) => assert(value.head.phone_number == "+************")
      }

    }

    it("should pass for 3 phones") {
      val data: PhoneNumberFormatData = PhoneNumberFormatData(
        prospect_tz = None,
        prospect_country = Some(""),
        phone_numbers = Seq(PhoneNumber("8180855508"), PhoneNumber("8180852244"), PhoneNumber("8180856654")),
        phone_number = None,
        user_tz = "Asia/Kolkata"
      )

      val res = callService.getFormattedPhoneNumber(
        data = data,
        tid = team_id
      )

      res match {

        case Failure(exception) => assert(false)

        case Success(value) => assert(value.head.phone_number == "+918180855508" &&
        value(1).phone_number == "+918180852244" &&
        value(2).phone_number == "+918180856654")
      }

    }

    it("should pass for 0 phones") {
      val data: PhoneNumberFormatData = PhoneNumberFormatData(
        prospect_tz = None,
        prospect_country = Some(""),
        phone_numbers = Seq(),
        phone_number = None,
        user_tz = "Asia/Kolkata"
      )

      val res = callService.getFormattedPhoneNumber(
        data = data,
        tid = team_id
      )

      res match {

        case Failure(exception) => assert(false)

        case Success(value) => assert(value.isEmpty)
      }

    }

    it("should pass for 0 phone_numbers and phone_number defined for extension") {
      val data: PhoneNumberFormatData = PhoneNumberFormatData(
        prospect_tz = None,
        prospect_country = None,
        phone_numbers = Seq(),
        phone_number = Some(PhoneNumber("(*************")),
        user_tz = "Asia/Kolkata"
      )

      val res = callService.getFormattedPhoneNumber(
        data = data,
        tid = team_id
      )

      res match {

        case Failure(exception) => assert(false)

        case Success(value) => assert(value.head.phone_number == "+************")
      }

    }


  }

  describe(" testing PhoneNumberValidator") {

    it("should pass isValidPhoneNumber check and return true") {

      val phone_number = "+91**********"

      val res = isValidPhoneNumber(
        phone_number
      )

      assert(res)

    }

    it("should not pass isValidPhoneNumber and return false") {

      val a = isValidPhoneNumber(
        "0"
      )

      val b = isValidPhoneNumber(
        "02"
      )

      val c = isValidPhoneNumber(
        "045"
      )

      val d = isValidPhoneNumber(
        "56"
      )

      val e = isValidPhoneNumber(
        "1234567890"
      )

      val f = isValidPhoneNumber(
        "+23429243"
      )

      val g = isValidPhoneNumber(
        "2342924324sdfsd"
      )

      val h = isValidPhoneNumber(
        "234"
      )

      val i = isValidPhoneNumber(
        "A 90234"
      )

      assert(!a)
      assert(!b)
      assert(!c)
      assert(!d)
      assert(!e)
      assert(!f)
      assert(!g)
      assert(!h)
      assert(!i)

    }

    it("should return PhoneNumber after parsing indian number") {

      val res = parseNumber("+91**********")

      res match {

        case Success(phone_number_details) =>
          println(phone_number_details.getCountryCode)

          assert(phone_number_details.getCountryCode == 91)

        case Failure(err) =>

          assert(false)


      }
    }


    it("should fail as number is incorrect country code is used for parsing ") {

      val res = parseNumber("0007**********")

      res match {

        case Success(phone_number_details) =>

          assert(false)

        case Failure(err) =>

          assert(err.getMessage == "Missing or invalid default region.")

      }
    }

    it("should pass for Egypt +2057547957 ") {

      val res = parseNumber("+2057547957")

      res match {

        case Success(phone_number_details) =>

          assert(phone_number_details.getCountryCode == 20)

        case Failure(err) =>

          assert(false)

      }
    }

  }

  describe("testing validatePhoneNumberBeforeCall") {

    it("should fail when improper structure of phone numbers provided for number +507823") {

      val res = callService.validatePhoneNumberBeforeCall(
        phone_number = PhoneNumber(phone_number = "+507823"),
        org_id = org_id,
        team_id = team_id,
        account_id = account_id
      )

      res match {

        case Left(PhoneNumberValidationError.BasicErrorValidationFailed(err)) =>

          assert(err.getMessage == "Number not in correct format")

        case _ => assert(false)

      }

    }


    it("should fail when improper structure of phone numbers provided for number +123") {

      val res = callService.validatePhoneNumberBeforeCall(
        phone_number = PhoneNumber(phone_number = "+123"),
        org_id = org_id,
        team_id = team_id,
        account_id = account_id
      )

      res match {

        case Left(PhoneNumberValidationError.BasicErrorValidationFailed(err)) =>

          assert(err.getMessage == "Number not in correct format")

        case _ => assert(false)

      }

    }

    it("should fail when improper structure of phone numbers provided for number 0") {

      val res = callService.validatePhoneNumberBeforeCall(
        phone_number = PhoneNumber(phone_number = "0"),
        org_id = org_id,
        team_id = team_id,
        account_id = account_id
      )

      res match {

        case Left(PhoneNumberValidationError.BasicErrorValidationFailed(err)) =>

          assert(err.getMessage == "Number not in correct format")

        case _ => assert(false)

      }

    }

    it("should fail when improper structure of phone numbers provided for number +91563") {

      val res = callService.validatePhoneNumberBeforeCall(
        phone_number = PhoneNumber(phone_number = "+91563"),
        org_id = org_id,
        team_id = team_id,
        account_id = account_id
      )

      res match {

        case Left(PhoneNumberValidationError.BasicErrorValidationFailed(err)) =>

          assert(err.getMessage == "Number not in correct format")

        case _ => assert(false)

      }

    }

    it("should fail when improper structure of phone numbers provided for number +*********") {

      val res = callService.validatePhoneNumberBeforeCall(
        phone_number = PhoneNumber(phone_number = "+************"),
        org_id = org_id,
        team_id = team_id,
        account_id = account_id
      )

      res match {

        case Left(PhoneNumberValidationError.BasicErrorValidationFailed(err)) =>

          assert(err.getMessage == "Number not in correct format")

        case _ => assert(false)

      }

    }

    it("should fail if call balances is not enough for call") {

      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Success(Some(sub_account_details.copy(call_remaining_credit_cents = 0))))

      //      (callDAO.getCallSettingOfUser)
      //        .expects(team_id, account_id)
      //        .returning(Success(Some(call_account_settings)))

      val res = callService.validatePhoneNumberBeforeCall(
        phone_number = PhoneNumber(phone_number = "+91**********"),
        org_id = org_id,
        team_id = team_id,
        account_id = account_id
      )

      assert(res == Left(PhoneNumberValidationError.NotEnoughBalanceToMakeCall))

    }

    it("should fail if account is suspended") {

      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Success(Some(sub_account_details.copy(status = SubAccountStatus.Suspended))))

      //      (callDAO.getCallSettingOfUser)
      //        .expects(team_id, account_id)
      //        .returning(Success(Some(call_account_settings)))

      val res = callService.validatePhoneNumberBeforeCall(
        phone_number = PhoneNumber(phone_number = "+91**********"),
        org_id = org_id,
        team_id = team_id,
        account_id = account_id
      )

      assert(res == Left(PhoneNumberValidationError.AccountSuspended))

    }

    it("should fail if error occurs while fetching number") {

      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Success(Some(sub_account_details)))

      (callDAO.getCallSettingOfUser)
        .expects(team_id, account_id)
        .returning(Failure(error))

      val res = callService.validatePhoneNumberBeforeCall(
        phone_number = PhoneNumber(phone_number = "+91**********"),
        org_id = org_id,
        team_id = team_id,
        account_id = account_id
      )

      assert(res == Left(PhoneNumberValidationError.ErrorWhileFetchingCallSettingForUser(error)))

    }

    it("should fail if call account is not present") {

      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Success(Some(sub_account_details)))

      (callDAO.getCallSettingOfUser)
        .expects(team_id, account_id)
        .returning(Success(None))

      val res = callService.validatePhoneNumberBeforeCall(
        phone_number = PhoneNumber(phone_number = "+91**********"),
        org_id = org_id,
        team_id = team_id,
        account_id = account_id
      )

      assert(res == Left(PhoneNumberValidationError.BuyNumberFirst))

    }

//    it("should fail if calling to blocked country") {
//
//      val res = callService.validatePhoneNumberBeforeCall(
//        phone_number = PhoneNumber(phone_number = "+************"),
//        org_id = org_id,
//        team_id = team_id,
//        account_id = account_id
//      )
//
//      assert(res == Left(PhoneNumberValidationError.CountryNotAllowed("Call not allowed in this country")))
//
//    }

    it("should pass for correct phone_number") {

      (callDAO.getSubAccountDetailsForOrg)
        .expects(None, org_id)
        .returning(Success(Some(sub_account_details)))

      (callDAO.getCallSettingOfUser)
        .expects(team_id, account_id)
        .returning(Success(Some(call_account_settings)))

      val res = callService.validatePhoneNumberBeforeCall(
        phone_number = PhoneNumber(phone_number = "+91**********"),
        org_id = org_id,
        team_id = team_id,
        account_id = account_id
      )

      assert(res == Right(true))

    }

  }

  describe("testing EmailNotificationService.sendNewEmailIfDelayPassed") {

    it("should return success(true) if not log found") {

      val res = EmailNotificationService.sendNewEmailIfDelayPassed(
        send_email_after_hour = 24,
        latest_sent_log = None,
        orgId = org_id,
        teamId = None,
        notification_type = NotificationType.MonthlyProspectLimitHit
      )(logger)

      assert(res == Success(true))

    }

    it("should return success(true) log found and sent_at is before 24 hours") {

      val res = EmailNotificationService.sendNewEmailIfDelayPassed(
        send_email_after_hour = 24,
        latest_sent_log = Some(notificationEmailSendLog.copy(sent_at = DateTime.now().minusHours(25))),
        orgId = org_id,
        teamId = None,
        notification_type = NotificationType.MonthlyProspectLimitHit
      )(logger)

      assert(res == Success(true))

    }

    it("should return failure if log found and sent_at is after 24 hours") {

      val res = EmailNotificationService.sendNewEmailIfDelayPassed(
        send_email_after_hour = 24,
        latest_sent_log = Some(notificationEmailSendLog.copy(sent_at = DateTime.now().minusHours(23))),
        orgId = org_id,
        teamId = None,
        notification_type = NotificationType.MonthlyProspectLimitHit
      )(logger)

      res match {

        case Failure(e) =>

          assert(e.getMessage == s"Wait time is not over yet, notification_type: ${NotificationType.MonthlyProspectLimitHit.toKey} orgId : ${org_id.id}")

        case Success(bool) =>

          assert(false)

      }

    }

  }


}
