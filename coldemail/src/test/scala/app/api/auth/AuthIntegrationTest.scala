package app.api.auth

import db_test_spec.api.accounts.fixtures.NewAccountAndEmailSettingData
import db_test_spec.api.{DbTestingBeforeAllAndAfterAll, InitialData}
import org.scalatest.funspec.AsyncFunSpec
import play.api.libs.json.{JsError, JsSuccess, JsValue, Json}
import play.api.test.FakeRequest
import utils.SRLogger
import utils.helpers.LogHelpers
import play.api.test.Helpers.*

import scala.concurrent.duration.{DAYS, Duration, SECONDS}
import scala.concurrent.{Await, ExecutionContext, Future}

class AuthIntegrationTest extends DbTestingBeforeAllAndAfterAll  {


  lazy val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

  describe("Auth apis case class check ") {
    it("Checking the response of oauth redirection url without any header") {


      val sendingUrl = s"/api/v1/oauth/get_oauth_url"

      val request = FakeRequest(play.api.test.Helpers.GET, sendingUrl) 


      val final_result = play.api.test.Helpers.route(testApi, request).get
      val status: Int = play.api.test.Helpers.status(final_result)
      val json: JsValue = play.api.test.Helpers.contentAsJson(final_result)

        final_result.map(res => {
          if (status == 200) {
            val sentResponseData = (json \ "data" \ "redirect_to")

            sentResponseData.validate[String] match {
              case JsError(errors) =>
                assert(false)

              case JsSuccess(_, _) =>
                assert(true)
            }

          } else {
            assert(false)
          }
        }).recover(e => {
          assert(false)
        })
    }
  }

  it("for org data , has_active_call_campigns should be an boolean") {

    val sendingUrl = s"/api/v2/auth/me?tid=${initialData.head_team_id}"

    val request = FakeRequest(play.api.test.Helpers.GET, sendingUrl)
      .withHeaders("X-API-KEY" -> initialData.teamUserLevelKey)


    val final_result = play.api.test.Helpers.route(testApi, request).get
    val status: Int = play.api.test.Helpers.status(final_result)
    val json: JsValue = play.api.test.Helpers.contentAsJson(final_result)
      final_result.map(res => {
        if (status == 200) {
          val sentResponseData = (json \ "data" \ "account" \ "org" \ "counts" \ "has_active_call_campaigns")

          sentResponseData.validate[Boolean] match {
            case JsError(errors) =>
              println(s"Errors: ${errors}")
              assert(false)

            case JsSuccess(data, _) =>
              assert(true)
          }

        } else {
          assert(false)
        }
      }).recover(e => {
        print(s"Error:: ${LogHelpers.getStackTraceAsString(e)}")

        assert(false)
      })

  }

}
