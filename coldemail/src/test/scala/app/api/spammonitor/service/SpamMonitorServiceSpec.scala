package app.api.spammonitor.service

import org.apache.pekko.actor.ActorSystem
import api.accounts.{AccountDAO, OrgMetadata, Organization, OrganizationForSupportApp, TeamId}
import io.sr.billing_common.models.PlanType
import api.accounts.dao.OrganizationDAO
import api.accounts.models.{AccountId, OrgId}
import api.campaigns.models.{CampaignEmailSettingsId, CampaignStepData, CampaignStepType, CampaignType}
import sr_scheduler.CampaignStatus
import api.campaigns.services.{CallSettingSenderDetails, CampaignId, CampaignService, CampaignStepService, LinkedinSettingSenderDetails, SmsSettingSenderDetails, WhatsappSettingSenderDetails}
import api.campaigns.{Campaign, CampaignDAO, CampaignEmailSettings, CampaignEmailSettingsUuid, CampaignSettings, CampaignStepDAO, CampaignStepVariantDAO, CampaignStepVariantForScheduling, CampaignStepWithChildren, CampaignWithStatsAndEmail, ChannelSettingUuid}
import api.emails.EmailScheduledDAO
import api.free_email_domain.service.FreeEmailDomainListService
import api.gpt.GPTService
import api.prospects.models.StepId
import api.reports.{AllCampaignStats, ReplySentimentStats}
import api.scylla.dao.EmailMessageObjectV2
import api.spammonitor.dao.{DomainInfoWhoisDAO, UpdateEmailSendingStatusForm}
import api.spammonitor.model.{EmailSendStatus, EmailSendingEntityTypeData, SendEmailStatusData, UnderReviewReason}
import api.spammonitor.service.{ApiLayerResponse, CheckIfEmailIsAllowedToSignUpError, DomainDataService, EmailSendingStatusService, SpamMonitorService, UpdateEmailSendingStatusError, WhoIsApiCallError}
import api.sr_ai.service.ContentAnalysisService
import api.tags.models.{CampaignTag, CampaignTagUuid}
import api.tasks.models.TaskPriority
import app.test_fixtures.campaign_settings.{CallSettingSenderDetailsFixtures, LinkedinSettingSenderDetailsFixtures, SmsSettingSenderDetailsFixtures, WhatsappSettingSenderDetailsFixtures}
import app.test_fixtures.organizationa.OrgMetadataFixture
import db_test_spec.api.AppSpecFixture
import io.smartreach.esp.api.emails.{EmailSettingId, IEmailAddress}
import org.joda.time.DateTime
import org.scalamock.scalatest.AsyncMockFactory
import org.scalatest.funspec.AsyncFunSpec
import play.api.libs.json.Json
import play.api.libs.ws.WSClient
import play.api.libs.ws.ahc.AhcWSClient
import sr_scheduler.models.CampaignEmailPriority
import utils.SRLogger
import utils.email_notification.service.EmailNotificationService
import utils.testapp.TestAppExecutionContext

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}

class SpamMonitorServiceSpec extends AsyncFunSpec with AsyncMockFactory {
  given Logger: SRLogger = new SRLogger("SpamMonitorServiceSpec")
  implicit lazy val system: ActorSystem = TestAppExecutionContext.actorSystem
  implicit lazy val actorContext: ExecutionContext = system.dispatcher
  implicit lazy val wSClient: AhcWSClient = TestAppExecutionContext.wsClient

  val freeEmailDomainListService = mock[FreeEmailDomainListService]
  val domainDataService = mock[DomainDataService]
  val domainInfoWhoisDAO = mock[DomainInfoWhoisDAO]
  val organizationDAO = mock[OrganizationDAO]
  val err = new Throwable("ERROR")

  val apiLayerResponse = ApiLayerResponse(
    domain_name = "domain_name",
    creation_date = Some(DateTime.now().minusDays(30)),
    expiration_date = Some(DateTime.now().plusYears(1)),
    jsonData = Json.obj()
  )

  //  val cacheEventScyllaDAO = mock[CacheEventScyllaDAO]
  val campaignDAO = mock[CampaignDAO]
  val emailSendingStatusService = mock[EmailSendingStatusService]
  val emailScheduledDAO = mock[EmailScheduledDAO]
  val campaignService = mock[CampaignService]
  val accountDAO = mock[AccountDAO]
  val emailNotificationService = mock[EmailNotificationService]
  val campaignStepService = mock[CampaignStepService]
  //  val gptService = mock[GPTService]
  val campaignStepVariantDAO = mock[CampaignStepVariantDAO]
  val campaignStepDAO = mock[CampaignStepDAO]
  val contentAnalysisService = mock[ContentAnalysisService]
  
  val spamMonitorService = new SpamMonitorService(
    freeEmailDomainListService = freeEmailDomainListService,
    domainDataService = domainDataService,
    domainInfoWhoisDAO = domainInfoWhoisDAO,
    organizationDAO = organizationDAO,
    //    cacheEventScyllaDAO = cacheEventScyllaDAO,
    campaignDAO = campaignDAO,
    campaignService = campaignService,
    campaignStepService = campaignStepService,
    campaignStepVariantDAO = campaignStepVariantDAO,
    campaignStepDAO = campaignStepDAO,
    //    gptService = gptService,
    contentAnalysisService = contentAnalysisService,
    emailSendingStatusService = emailSendingStatusService,
    emailScheduledDAO = emailScheduledDAO,
    accountDAO = accountDAO,
    emailNotificationService = emailNotificationService
  )

  val campaignId: Long = 3
  val stepId = 5L
  val variantId = 7L

  val settings = CampaignSettings(
    campaign_email_settings = List(),
    campaign_linkedin_settings = List(),
    campaign_call_settings = List(),
    campaign_whatsapp_settings = List(),
    campaign_sms_settings = List(),
    timezone = "IN",
    daily_from_time = 100,
    daily_till_time = 100,
    sending_holiday_calendar_id = None,
    days_preference = List(),
    mark_completed_after_days = 4,
    max_emails_per_day = 7,
    ai_sequence_status = None,
    open_tracking_enabled = false,
    click_tracking_enabled = false,
    enable_email_validation = false,
    ab_testing_enabled = false,
    warmup_started_at = None,
    warmup_length_in_days = None,
    warmup_starting_email_count = None,
    show_soft_start_setting = false,
    schedule_start_at = None,
    schedule_start_at_tz = None,
    email_priority = CampaignEmailPriority.FIRST_EMAIL,
    append_followups = false,
    send_plain_text_email = Some(false),
    campaign_type = CampaignType.MultiChannel,
    opt_out_msg = "opt_out_msg",
    opt_out_is_text = false,
    add_prospect_to_dnc_on_opt_out = false,
    triggers = Seq(),
    sending_mode = None,
    selected_calendar_data = None
  )
  val campaign_uuid = s"cmp_3_cfknacskndjcn"
  val campaign = Campaign(
    id = 2L,
    uuid = Some(campaign_uuid),
    account_id = 4L,
    team_id = 3L,
    shared_with_team = true,
    name = "some name",
    status = CampaignStatus.RUNNING,
    head_step_id = Some(5L),
    settings = settings,
    last_scheduled_at = None,
    created_at = DateTime.now()
  )

  val autoEmailStepData1 = CampaignStepData.AutoEmailStep(
    subject = "Auto Email Step Subject",
    body = "Auto Email Step Body"
  )

  val autoEmailStepData2 = CampaignStepData.AutoEmailStep(
    subject = "Auto Email Step 2 Subject",
    body = "Auto Email Step 2 Body"
  )

  val stepLabel = "Step Label"
  val stepLabel2 = "Step Label 2"

  val campaignStepVariantForSchedulingAutoEmail1 = CampaignStepVariantForScheduling(
    id = variantId,
    step_id = stepId,
    campaign_id = campaignId,
    template_id = None,
    step_data = autoEmailStepData1,
    step_label = Some(stepLabel),
    step_delay = 101,
    notes = Some("Auto Email Step Notes"),
    priority = Some(TaskPriority.High),
    active = true,
    scheduled_count = 11
  )

  val campaignStepVariantForSchedulingAutoEmail2 = campaignStepVariantForSchedulingAutoEmail1.copy(
    step_data = autoEmailStepData2
  )

  val manualStepData = CampaignStepData.ManualEmailStep(
    subject = "Manual Email Step Subject",
    body = "Manual Email Step Body"
  )

  val campaignStepVariantForSchedulingManualEmail = campaignStepVariantForSchedulingAutoEmail1.copy(
    step_data = manualStepData
  )

  val campaignStepWithChildrenAutoEmail = CampaignStepWithChildren(
    id = 3L,
    label = Some("Step Label"),
    campaign_id = campaign.id,
    delay = 101,
    step_type = CampaignStepType.AutoEmailStep,
    created_at = DateTime.now(),
    children = List(3, 5, 7),
    variants = Seq(campaignStepVariantForSchedulingAutoEmail1, campaignStepVariantForSchedulingAutoEmail2)
  )

  val campaignStepWithChildrenManualEmail = campaignStepWithChildrenAutoEmail.copy(
    step_type = CampaignStepType.ManualEmailStep,
    variants = Seq(campaignStepVariantForSchedulingManualEmail)
  )

  val linkedinMessageStepData = CampaignStepData.LinkedinMessageData(
    body = "Linkedin Message Step Body"
  )

  val campaignStepVariantForSchedulingLinkedinMessage = campaignStepVariantForSchedulingAutoEmail1.copy(
    step_data = linkedinMessageStepData
  )

  val campaignStepWithChildrenLinkedinMessage = campaignStepWithChildrenAutoEmail.copy(
    step_type = CampaignStepType.LinkedinMessage,
    variants = Seq(campaignStepVariantForSchedulingLinkedinMessage)
  )
  val organizationForSupportApp = OrganizationForSupportApp(id = 1,
    name = "name",
    owner_account_id = Some(124),
    owner_account_email = "<EMAIL>",
    plan_type = "plan_type",
    plan_name = "plan_name",
    trial_ends_at = DateTime.now(),
    additional_spam_tests = 10,
    metadata = AppSpecFixture.orgMetadata,
    total_sending_email_accounts = 4)

  describe("checkIfEmailIsAllowedToSignUp") {

    it("should fail because freeEmailDomainListService failed") {
      (freeEmailDomainListService.isFreeEmailDomain(_: String)(_: SRLogger))
        .expects("gmail.com", *)
        .returning(Failure(err))

      spamMonitorService.checkIfEmailIsAllowedToSignUp(email = "<EMAIL>").map { result =>

        assert(result == Left(CheckIfEmailIsAllowedToSignUpError.FailedToCheckIfDomainFree(err)))

      }
    }


    it("should fail because freeEmailDomainListService send true") {
      (freeEmailDomainListService.isFreeEmailDomain(_: String)(_: SRLogger))
        .expects("gmail.com", *)
        .returning(Success(true))
      (freeEmailDomainListService.isWhitelistedForSignupAndSendingEmail)
        .expects("<EMAIL>")
        .returning(Success(false))

      spamMonitorService.checkIfEmailIsAllowedToSignUp(email = "<EMAIL>").map { result =>

        assert(result == Left(CheckIfEmailIsAllowedToSignUpError.DomainIsFree))

      }
    }

    it("should show under review because number in the name") {
      (freeEmailDomainListService.isFreeEmailDomain(_: String)(_: SRLogger))
        .expects("smartreach.io", *)
        .returning(Success(false))
//      (domainDataService.getDomainAge(_: String)(_: SRLogger, _: ExecutionContext, _: WSClient))
//        .expects("smartreach.io", *, *, *)
//        .returning(Future.successful(Right(apiLayerResponse)))
//      (domainInfoWhoisDAO.createWhoisCheckLog)
//        .expects(apiLayerResponse)
//        .returning(Success(None))

      spamMonitorService.checkIfEmailIsAllowedToSignUp(email = "<EMAIL>").map { result =>
        println(s"result - $result")
        assert(result.isRight)
        assert(result.toOption.get.emailSendingStatus == EmailSendStatus.WarningForOneDay)
        result.toOption.get match {
          case SendEmailStatusData.WarningData(underReviewReasons, stepLabels, _) =>
            assert(underReviewReasons == Seq(UnderReviewReason.LoginEmailContainsNumber))
            assert(stepLabels == Seq())

          case _ => assert(false)
        }

      }
    }


//    it("should fail because api call failed to ApiLayerWhois") {
//      (freeEmailDomainListService.isFreeEmailDomain(_: String)(_: SRLogger))
//        .expects("smartreach.io", *)
//        .returning(Success(false))
//      (domainDataService.getDomainAge(_: String)(_: SRLogger, _: ExecutionContext, _: WSClient))
//        .expects("smartreach.io", *, *, *)
//        .returning(Future.successful(Left(WhoIsApiCallError.InvalidDataSentBackErrorWhoIs(err))))
//
//      spamMonitorService.checkIfEmailIsAllowedToSignUp(email = "<EMAIL>").map { result =>
//        assert(result.isRight)
//        assert(result.toOption.get.emailSendingStatus == EmailSendStatus.WarningForOneDay)
//        result.toOption.get match {
//          case SendEmailStatusData.WarningData(underReviewReasons, stepLabels, created_at) =>
//            assert(underReviewReasons == Seq(UnderReviewReason.CouldNotCheckLoginEmailDomainAge))
//            assert(stepLabels == Seq())
//
//          case _ => assert(false)
//        }
//
//      }
//    }

//    it("should send to review because less than 15 days") {
//      (freeEmailDomainListService.isFreeEmailDomain(_: String)(_: SRLogger))
//        .expects("smartreach.io", *)
//        .returning(Success(false))
//      (domainInfoWhoisDAO.createWhoisCheckLog)
//        .expects(*)
//        .returning(Success(Some(2)))
//      (domainDataService.getDomainAge(_: String)(_: SRLogger, _: ExecutionContext, _: WSClient))
//        .expects("smartreach.io", *, *, *)
//        .returning(Future.successful(Right(apiLayerResponse.copy(creation_date = Some(DateTime.now().minusDays(5))))))
//
//      spamMonitorService.checkIfEmailIsAllowedToSignUp(email = "<EMAIL>").map { result =>
//        assert(result.isRight)
//        assert(result.toOption.get.emailSendingStatus == EmailSendStatus.WarningForOneDay)
//        result.toOption.get match {
//          case SendEmailStatusData.WarningData(underReviewReasons, stepLabels, created_at) =>
//            assert(underReviewReasons == Seq(UnderReviewReason.LoginEmailDomainAge))
//            assert(stepLabels == Seq())
//
//          case _ => assert(false)
//        }
//
//      }
//    }

    it("should allow") {
      (freeEmailDomainListService.isFreeEmailDomain(_: String)(_: SRLogger))
        .expects("smartreach.io", *)
        .returning(Success(false))
//      (domainDataService.getDomainAge(_: String)(_: SRLogger, _: ExecutionContext, _: WSClient))
//        .expects("smartreach.io", *, *, *)
//        .returning(Future.successful(Right(apiLayerResponse)))
//      (domainInfoWhoisDAO.createWhoisCheckLog)
//        .expects(apiLayerResponse)
//        .returning(Success(Some(2)))

      spamMonitorService.checkIfEmailIsAllowedToSignUp(email = "<EMAIL>").map { result =>

        assert(result == Right(SendEmailStatusData.AllowedData()))

      }
    }

  }

  describe("checkIfCampaignAllowedToSendEmail") {

    val campaignData = EmailSendingEntityTypeData.CampaignData(
      orgId = OrgId(1), campaignId = 2, teamId = 3
    )
    val oldEmailSendingStatus = SendEmailStatusData.AllowedData()

    val objectData = EmailMessageObjectV2(
      from_user = true,
      from = IEmailAddress(email = "<EMAIL>"),
      reply_to = None,
      to = Seq(),
      cc_emails = None,
      bcc_emails = None,
      subject = "Some_Subject",
      sent_at = DateTime.now(),
      bounce_data = None
    )
    //    val eventInDB = EventInDB(
    //      event_id = "some_event_id",
    //      event_type = SrEventType.EmailMessageBounced,
    //      org_id = 1,
    //      campaign_id = Some(2),
    //      event_at = DateTime.now(),
    //      team_id = Some(3),
    //      email_setting_id = None,
    //      event_data = SrEvent.EmailMessageBounced(
    //        event_id = "another_event_id",
    //        event_data = SrResource.EmailMessageResource(
    //          object_data = objectData
    //        )
    //      ),
    //      created_at = DateTime.now()
    //    )

    //    val listOfEvents = List(
    //      eventInDB, eventInDB, eventInDB, eventInDB, eventInDB, eventInDB
    //    )


    val campaignSettings = CampaignSettings(

      // settings
      campaign_email_settings = List(
        CampaignEmailSettings(
          campaign_id = CampaignId(campaign.id),
          sender_email_setting_id = EmailSettingId(2),
          receiver_email_setting_id = EmailSettingId(11),
          team_id = TeamId(campaign.team_id),
          uuid = CampaignEmailSettingsUuid("temp_setting_id"),
          id = CampaignEmailSettingsId(123),
          sender_email = "<EMAIL>",
          receiver_email = "<EMAIL>",
          max_emails_per_day_from_email_account = 100,
          signature = Some("emailsignature"),
          error = None,
          from_name = None
        )
      ),
      campaign_linkedin_settings = List(
        LinkedinSettingSenderDetailsFixtures.linkedin_setting_sender_details
      ),
      campaign_call_settings = List(
        CallSettingSenderDetailsFixtures.call_setting_sender_details
      ),
      campaign_whatsapp_settings = List(
        WhatsappSettingSenderDetailsFixtures.whatsapp_setting_sender_details
      ),
      campaign_sms_settings = List(
        SmsSettingSenderDetailsFixtures.sms_setting_sender_details
      ),
      timezone = "thisistimezone",
      daily_from_time = 2, // time since beginning of day in seconds
      daily_till_time = 3, // time since beginning of day in seconds
      sending_holiday_calendar_id = Some(123L),
      ai_sequence_status = None,

      // Sunday is the first day
      days_preference = List(true, true, true, true, true, true, false),

      mark_completed_after_days = 33,
      max_emails_per_day = 100,
      open_tracking_enabled = true,
      click_tracking_enabled = true,
      enable_email_validation = true,
      ab_testing_enabled = true,

      // warm up
      warmup_started_at = Some(DateTime.now().minusDays(3)),
      warmup_length_in_days = Some(2),
      warmup_starting_email_count = Some(5),
      show_soft_start_setting = false,

      // schedule start
      schedule_start_at = Some(DateTime.now().minusDays(1)),
      schedule_start_at_tz = Some("Sometimezone"),
      send_plain_text_email = Some(false),
      campaign_type = CampaignType.MultiChannel,


      email_priority = CampaignEmailPriority.FIRST_EMAIL,
      append_followups = true,
      opt_out_msg = "opt out msg",
      opt_out_is_text = true,
      add_prospect_to_dnc_on_opt_out = true,
      triggers = Seq(),
      sending_mode = None,
      selected_calendar_data = None
    )

    val allCampaignStats = AllCampaignStats(
      total_sent = 1,
      total_opened = 1,
      total_clicked = 1,
      total_replied = 1,
      total_steps = 1,
      current_prospects = 1,
      current_opted_out = 1,
      current_completed = 1,
      current_bounced = 1,
      current_to_check = 1,
      current_failed_email_validation = 1,
      current_in_progress = 1,
      current_unsent_prospects = 1,
      current_do_not_contact = 1,
      reply_sentiment_stats = ReplySentimentStats(
        positive = 0
      )
    )
    val campaignWithStatsAndEmail = CampaignWithStatsAndEmail(
      id = 2L,
      uuid = Some(campaign_uuid),
      team_id = 3,
      shared_with_team = true,
      name = "campaign_name",
      owner_name = "first_name",
      owner_email = "<EMAIL>",
      owner_id = 9,
      status = CampaignStatus.RUNNING,
      tags = Seq(CampaignTag(11L, "tag1", CampaignTagUuid("tags_abcefgh"))),
      spam_test_exists = false,
      warmup_is_on = false,

      stats = allCampaignStats,

      head_step_id = Some(22L),
      ai_generation_context = None,

      settings = campaignSettings,

      created_at = DateTime.now(),

      error = Some("campaign error"),
      is_archived = false

    )
    val orgMetadata = OrgMetadataFixture.orgMetadataFixture2

    val organization: Organization = Organization(
      id = 1,
      name = "Animesh",
      owner_account_id = Some(1),
      plan_type = PlanType.PAID.toString,
      plan_name = PlanType.PAID.toString,
      trial_ends_at = DateTime.now(),
      additional_spam_tests = 5,
      is_agency = false,
      fp_referrer_account_id = None,
      affiliateTrackerOpt = None
    )


    it("should Fail because No Campaign found") {
      //
      //      (cacheEventScyllaDAO.getBouncedRepliesForCampaign(_: Long, _: Long)(_: ExecutionContext, _: SRLogger))
      //        .expects(campaignData.campaignId, campaignData.orgId, *, *)
      //        .returning(Future.successful(listOfEvents))

      (campaignDAO.findCampaignForCampaignUtilsOnly)
        .expects(2, TeamId(3L))
        .returning(None)
      (organizationDAO.getOrgForSupportByOrgId)
        .expects(1)
        .returning(Success(Some(organizationForSupportApp)))
      spamMonitorService.checkIfCampaignAllowedToSendEmail(
        campaignData = campaignData,
        oldEmailSendingStatus = oldEmailSendingStatus
      ).map { result =>
        Logger.info(s"result -- $result")
        assert(false)
      }.recover { case e =>

        Logger.info(s"error ----- $e")

        assert(e.getMessage == "No campaign found for campaignId - 2")
      }
    }


    it("should fail because getSentEmailsForACampaign failed") {

      //      (cacheEventScyllaDAO.getBouncedRepliesForCampaign(_: Long, _: Long)(_: ExecutionContext, _: SRLogger))
      //        .expects(campaignData.campaignId, campaignData.orgId, *, *)
      //        .returning(Future.successful(listOfEvents))

      (campaignDAO.findCampaignForCampaignUtilsOnly)
        .expects(2, TeamId(3L))
        .returning(Some(campaign))
      (emailScheduledDAO.getSentEmailCountForACampaignForLast24Hours)
        .expects(2, 3)
        .returning(Failure(err))

      //      (campaignStepService.findStepsByCampaign(
      //        _: Campaign,
      //        _: Boolean
      //      )(
      //        _: SRLogger
      //      ))
      //        .expects(campaign, false, *)
      //        .returning(None)
      (organizationDAO.getOrgForSupportByOrgId)
        .expects(1)
        .returning(Success(Some(organizationForSupportApp)))
      spamMonitorService.checkIfCampaignAllowedToSendEmail(
        campaignData = campaignData,
        oldEmailSendingStatus = oldEmailSendingStatus
      ).map { result =>
        Logger.info(s"result -- $result")
        assert(false)
      }.recover { case e =>

        Logger.info(s"error ----- $e")

        assert(e == err)
      }
    }

    //    it("should fail because total number of email sent is less than number of emails bounced") {
    //
    //      (cacheEventScyllaDAO.getBouncedRepliesForCampaign(_: Long, _: Long)(_: ExecutionContext, _: SRLogger))
    //        .expects(campaignData.campaignId, campaignData.orgId, *, *)
    //        .returning(Future.successful(listOfEvents))
    //
    //      (campaignDAO.findCampaignForCampaignUtilsOnly)
    //        .expects(2)
    //        .returning(Some(campaign))
    //      (emailScheduledDAO.getSentEmailCountForACampaignForLast24Hours)
    //        .expects(2, 3)
    //        .returning(Success(List()))
    //
    //      spamMonitorService.checkIfCampaignAllowedToSendEmail(
    //        campaignData = campaignData,
    //        oldEmailSendingStatus = oldEmailSendingStatus
    //      ).map{result =>
    //        Logger.info(s"result -- $result")
    //        assert(false)
    //      }.recover{case e =>
    //
    //        Logger.info(s"error ----- $e")
    //
    //        assert(e.getMessage == "Total number of bounced replies(6) more than total email(0) send in the last 24 hours, for campaignId - 2")
    //      }
    //    }

    it("should fail because update email send status fail for campaign with no emails sent") {

      //      (cacheEventScyllaDAO.getBouncedRepliesForCampaign(_: Long, _: Long)(_: ExecutionContext, _: SRLogger))
      //        .expects(campaignData.campaignId, campaignData.orgId, *, *)
      //        .returning(Future.successful(List()))

      (campaignDAO.findCampaignForCampaignUtilsOnly)
        .expects(2, TeamId(3L))
        .returning(Some(campaign))
      (emailScheduledDAO.getSentEmailCountForACampaignForLast24Hours)
        .expects(2, 3)
        .returning(Success(List()))

      //      (campaignStepService.findStepsByCampaign(
      //        _: Campaign,
      //        _: Boolean
      //      )(
      //      _: SRLogger
      //      ))
      //        .expects(campaign, false, *)
      //        .returning(None)

      (emailSendingStatusService.updateEmailSendingStatus(_: UpdateEmailSendingStatusForm, _: Option[SendEmailStatusData], _: Boolean, _: String)(_: WSClient, _: ExecutionContext, _: SRLogger))
        .expects(UpdateEmailSendingStatusForm(campaignData, OrgId(1), oldEmailSendingStatus), *, *, *, *, *, *)
        .returning(Left(UpdateEmailSendingStatusError.ErrorWhileAddingInDB(err)))
      (organizationDAO.getOrgForSupportByOrgId)
        .expects(1)
        .returning(Success(Some(organizationForSupportApp)))
      spamMonitorService.checkIfCampaignAllowedToSendEmail(
        campaignData = campaignData,
        oldEmailSendingStatus = oldEmailSendingStatus
      ).map { result =>
        Logger.info(s"result -- $result")
        assert(false)
      }.recover { case e =>

        Logger.info(s"error ----- $e")

        assert(e.getMessage == "Error while Adding email send status in DB - ErrorWhileAddingInDB(java.lang.Throwable: ERROR)")
      }
    }


    it("should success for campaign with no emails sent") {

      //      (cacheEventScyllaDAO.getBouncedRepliesForCampaign(_: Long, _: Long)(_: ExecutionContext, _: SRLogger))
      //        .expects(campaignData.campaignId, campaignData.orgId, *, *)
      //        .returning(Future.successful(List()))

      (campaignDAO.findCampaignForCampaignUtilsOnly)
        .expects(2, TeamId(3L))
        .returning(Some(campaign))
      (emailScheduledDAO.getSentEmailCountForACampaignForLast24Hours)
        .expects(2, 3)
        .returning(Success(List()))

      //      (campaignStepService.findStepsByCampaign(
      //        _: Campaign,
      //        _: Boolean
      //      )(
      //        _: SRLogger
      //      ))
      //        .expects(campaign, false, *)
      //        .returning(None)

      (emailSendingStatusService.updateEmailSendingStatus(_: UpdateEmailSendingStatusForm, _: Option[SendEmailStatusData], _: Boolean, _: String)(_: WSClient, _: ExecutionContext, _: SRLogger))
        .expects(UpdateEmailSendingStatusForm(campaignData, OrgId(1), oldEmailSendingStatus), *, *, *, *, *, *)
        .returning(Right(()))
      (organizationDAO.getOrgForSupportByOrgId)
        .expects(1)
        .returning(Success(Some(organizationForSupportApp)))
      spamMonitorService.checkIfCampaignAllowedToSendEmail(
        campaignData = campaignData,
        oldEmailSendingStatus = oldEmailSendingStatus
      ).map { result =>
        Logger.info(s"result -- $result")
        assert(result == campaign.id)
      }.recover { case e =>

        Logger.info(s"error ----- $e")

        assert(false)
      }
    }

    it("should fail to put the campaign under review") {

      //      (cacheEventScyllaDAO.getBouncedRepliesForCampaign(_: Long, _: Long)(_: ExecutionContext, _: SRLogger))
      //        .expects(campaignData.campaignId, campaignData.orgId, *, *)
      //        .returning(Future.successful(listOfEvents))
      val listFromDb: List[Boolean] = (1 to 15).map { i =>
        if (i < 6) {
          true
        } else {
          false
        }
      }.toList

      (campaignDAO.findCampaignForCampaignUtilsOnly)
        .expects(2, TeamId(3L))
        .returning(Some(campaign))
      (emailScheduledDAO.getSentEmailCountForACampaignForLast24Hours)
        .expects(2, 3)
        .returning(Success(listFromDb))

      //      (campaignStepService.findStepsByCampaign(
      //        _: Campaign,
      //        _: Boolean
      //      )(
      //        _: SRLogger
      //      ))
      //        .expects(campaign, false, *)
      //        .returning(None)

      (emailSendingStatusService.updateEmailSendingStatus(_: UpdateEmailSendingStatusForm, _: Option[SendEmailStatusData], _: Boolean, _: String)(_: WSClient, _: ExecutionContext, _: SRLogger))
        .expects(*, *, *, *, *, *, *)
        .returning(Left(UpdateEmailSendingStatusError.ErrorWhileAddingInDB(err)))
      (organizationDAO.getOrgForSupportByOrgId)
        .expects(1)
        .returning(Success(Some(organizationForSupportApp)))
      spamMonitorService.checkIfCampaignAllowedToSendEmail(
        campaignData = campaignData,
        oldEmailSendingStatus = oldEmailSendingStatus
      ).map { result =>
        Logger.info(s"result -- $result")
        assert(false)
      }.recover { case e =>

        Logger.info(s"error ----- $e")

        assert(e.getMessage == "Error while Adding email send status in DB - ErrorWhileAddingInDB(java.lang.Throwable: ERROR)")
      }
    }

    it("should fail to update campaign status") {

      //      (cacheEventScyllaDAO.getBouncedRepliesForCampaign(_: Long, _: Long)(_: ExecutionContext, _: SRLogger))
      //        .expects(campaignData.campaignId, campaignData.orgId, *, *)
      //        .returning(Future.successful(listOfEvents))
      val listFromDb: List[Boolean] = (1 to 15).map { i =>
        if (i < 6) {
          true
        } else {
          false
        }
      }.toList

      (campaignDAO.findCampaignForCampaignUtilsOnly)
        .expects(2, TeamId(3L))
        .returning(Some(campaign))
      (emailScheduledDAO.getSentEmailCountForACampaignForLast24Hours)
        .expects(2, 3)
        .returning(Success(listFromDb))

      //      (campaignStepService.findStepsByCampaign(
      //        _: Campaign,
      //        _: Boolean
      //      )(
      //        _: SRLogger
      //      ))
      //        .expects(campaign, false, *)
      //        .returning(None)

      (emailSendingStatusService.updateEmailSendingStatus(_: UpdateEmailSendingStatusForm, _: Option[SendEmailStatusData], _: Boolean, _: String)(_: WSClient, _: ExecutionContext, _: SRLogger))
        .expects(*, *, *, *, *, *, *)
        .returning(Right(()))

      (campaignService.updateStatus(_: Long, _: CampaignStatus, _: Option[DateTime], _: Option[String], _: Boolean, _: TeamId)(using _: SRLogger))
        .expects(2, CampaignStatus.UNDER_REVIEW, None, None, true, TeamId(3L), *)
        .returning(Failure(err))
      (organizationDAO.getOrgForSupportByOrgId)
        .expects(1)
        .returning(Success(Some(organizationForSupportApp)))
      spamMonitorService.checkIfCampaignAllowedToSendEmail(
        campaignData = campaignData,
        oldEmailSendingStatus = oldEmailSendingStatus
      ).map { result =>
        Logger.info(s"result -- $result")
        assert(false)
      }.recover { case e =>

        Logger.info(s"error ----- $e")

        assert(e == err)
      }
    }

    it("couldnt find campaign after update") {

      //      (cacheEventScyllaDAO.getBouncedRepliesForCampaign(_: Long, _: Long)(_: ExecutionContext, _: SRLogger))
      //        .expects(campaignData.campaignId, campaignData.orgId, *, *)
      //        .returning(Future.successful(listOfEvents))

      val listFromDb: List[Boolean] = (1 to 15).map { i =>
        if (i < 6) {
          true
        } else {
          false
        }
      }.toList

      (campaignDAO.findCampaignForCampaignUtilsOnly)
        .expects(2, TeamId(3L))
        .returning(Some(campaign))
      (emailScheduledDAO.getSentEmailCountForACampaignForLast24Hours)
        .expects(2, 3)
        .returning(Success(listFromDb))

      //      (campaignStepService.findStepsByCampaign(
      //        _: Campaign,
      //        _: Boolean
      //      )(
      //        _: SRLogger
      //      ))
      //        .expects(campaign, false, *)
      //        .returning(None)

      (emailSendingStatusService.updateEmailSendingStatus(_: UpdateEmailSendingStatusForm, _: Option[SendEmailStatusData], _: Boolean, _: String)(_: WSClient, _: ExecutionContext, _: SRLogger))
        .expects(*, *, *, *, *, *, *)
        .returning(Right(()))

      (campaignService.updateStatus(_: Long, _: CampaignStatus, _: Option[DateTime], _: Option[String], _: Boolean, _: TeamId)(using _: SRLogger))
        .expects(2, CampaignStatus.UNDER_REVIEW, None, None, true, TeamId(3L), *)
        .returning(Success(None))
      (organizationDAO.getOrgForSupportByOrgId)
        .expects(1)
        .returning(Success(Some(organizationForSupportApp)))
      spamMonitorService.checkIfCampaignAllowedToSendEmail(
        campaignData = campaignData,
        oldEmailSendingStatus = oldEmailSendingStatus
      ).map { result =>
        Logger.info(s"result -- $result")
        assert(false)
      }.recover { case e =>

        Logger.info(s"error ----- $e")

        assert(e.getMessage == "CAMPAIGN NOT FOUND AFTER UPDATING")
      }
    }

    it("should put the campaign under review") {

      //      (cacheEventScyllaDAO.getBouncedRepliesForCampaign(_: Long, _: Long)(_: ExecutionContext, _: SRLogger))
      //        .expects(campaignData.campaignId, campaignData.orgId, *, *)
      //        .returning(Future.successful(listOfEvents))
      val listFromDb: List[Boolean] = (1 to 15).map { i =>
        if (i < 6) {
          true
        } else {
          false
        }
      }.toList


      (campaignDAO.findCampaignForCampaignUtilsOnly)
        .expects(2, TeamId(3L))
        .returning(Some(campaign))
      (emailScheduledDAO.getSentEmailCountForACampaignForLast24Hours)
        .expects(2, 3)
        .returning(Success(listFromDb))

      //      (campaignStepService.findStepsByCampaign(
      //        _: Campaign,
      //        _: Boolean
      //      )(
      //        _: SRLogger
      //      ))
      //        .expects(campaign, false, *)
      //        .returning(None)

      (emailSendingStatusService.updateEmailSendingStatus(_: UpdateEmailSendingStatusForm, _: Option[SendEmailStatusData], _: Boolean, _: String)(_: WSClient, _: ExecutionContext, _: SRLogger))
        .expects(*, *, *, *, *, *, *)
        .returning(Right(()))
      (organizationDAO.getOrg)
        .expects(1)
        .returning(Success(Some(organization)))

      (accountDAO.getWhenTheLastSpamReviewEmailWasSent)
        .expects(AccountId(id = 1))
        .returning(Success(Some(DateTime.now())))

      (campaignService.updateStatus(_: Long, _: CampaignStatus, _: Option[DateTime], _: Option[String], _: Boolean, _: TeamId)(using _: SRLogger))
        .expects(2, CampaignStatus.UNDER_REVIEW, None, None, true, TeamId(3L), *)
        .returning(Success(Some(campaignWithStatsAndEmail)))
      (organizationDAO.getOrgForSupportByOrgId)
        .expects(1)
        .returning(Success(Some(organizationForSupportApp)))
      spamMonitorService.checkIfCampaignAllowedToSendEmail(
        campaignData = campaignData,
        oldEmailSendingStatus = oldEmailSendingStatus
      ).map { result =>
        Logger.info(s"result -- $result")
        assert(result == campaign.id)
      }.recover { case e =>

        Logger.info(s"error ----- $e")

        assert(false)
      }
    }


    it("should fail to put the campaign in allowed when bounce rate is low") {

      //      (cacheEventScyllaDAO.getBouncedRepliesForCampaign(_: Long, _: Long)(_: ExecutionContext, _: SRLogger))
      //        .expects(campaignData.campaignId, campaignData.orgId, *, *)
      //        .returning(Future.successful(listOfEvents))
      val listFromDb: List[Boolean] = (1 to 100).map { i =>
        if (i < 6) {
          true
        } else {
          false
        }
      }.toList


      (campaignDAO.findCampaignForCampaignUtilsOnly)
        .expects(2, TeamId(3L))
        .returning(Some(campaign))
      (emailScheduledDAO.getSentEmailCountForACampaignForLast24Hours)
        .expects(2, 3)
        .returning(Success(listFromDb))

      //      (campaignStepService.findStepsByCampaign(
      //        _: Campaign,
      //        _: Boolean
      //      )(
      //        _: SRLogger
      //      ))
      //        .expects(campaign, false, *)
      //        .returning(None)

      (emailSendingStatusService.updateEmailSendingStatus(_: UpdateEmailSendingStatusForm, _: Option[SendEmailStatusData], _: Boolean, _: String)(_: WSClient, _: ExecutionContext, _: SRLogger))
        .expects(UpdateEmailSendingStatusForm(campaignData, OrgId(1), oldEmailSendingStatus), *, *, *, *, *, *)
        .returning(Left(UpdateEmailSendingStatusError.ErrorWhileAddingInDB(err)))
      (organizationDAO.getOrgForSupportByOrgId)
        .expects(1)
        .returning(Success(Some(organizationForSupportApp)))
      spamMonitorService.checkIfCampaignAllowedToSendEmail(
        campaignData = campaignData,
        oldEmailSendingStatus = oldEmailSendingStatus
      ).map { result =>
        Logger.info(s"result -- $result")
        assert(false)
      }.recover { case e =>

        Logger.info(s"error ----- $e")

        assert(e.getMessage == "Error while Adding email send status in DB - ErrorWhileAddingInDB(java.lang.Throwable: ERROR)")
      }
    }


    it("should put the campaign in allowed when bounce rate is low") {

      //      (cacheEventScyllaDAO.getBouncedRepliesForCampaign(_: Long, _: Long)(_: ExecutionContext, _: SRLogger))
      //        .expects(campaignData.campaignId, campaignData.orgId, *, *)
      //        .returning(Future.successful(listOfEvents))

      val listFromDb: List[Boolean] = (1 to 100).map { i =>
        if (i < 6) {
          true
        } else {
          false
        }
      }.toList

      (campaignDAO.findCampaignForCampaignUtilsOnly)
        .expects(2, TeamId(3L))
        .returning(Some(campaign))
      (emailScheduledDAO.getSentEmailCountForACampaignForLast24Hours)
        .expects(2, 3)
        .returning(Success(listFromDb))

      //      (campaignStepService.findStepsByCampaign(
      //        _: Campaign,
      //        _: Boolean
      //      )(
      //        _: SRLogger
      //      ))
      //        .expects(campaign, false, *)
      //        .returning(None)

      (organizationDAO.getOrgForSupportByOrgId)
        .expects(1)
        .returning(Success(Some(organizationForSupportApp)))
      (emailSendingStatusService.updateEmailSendingStatus(_: UpdateEmailSendingStatusForm, _: Option[SendEmailStatusData], _: Boolean, _: String)(_: WSClient, _: ExecutionContext, _: SRLogger))
        .expects(UpdateEmailSendingStatusForm(campaignData, OrgId(1), oldEmailSendingStatus), *, *, *, *, *, *)
        .returning(Right(()))

      spamMonitorService.checkIfCampaignAllowedToSendEmail(
        campaignData = campaignData,
        oldEmailSendingStatus = oldEmailSendingStatus
      ).map { result =>
        Logger.info(s"result -- $result")
        assert(result == campaign.id)
      }.recover { case e =>

        Logger.info(s"error ----- $e")

        assert(false)
      }
    }

  }

  describe("Testing SpamMonitorService.checkEmailContentInsideCampaignStep") {
    it("should fail if findStepWithVariants fails") {

      (campaignStepVariantDAO.findStepWithVariants(
        _: StepId,
        _: CampaignId
      ))
        .expects(*, *)
        .returning(Failure(err))

      spamMonitorService.checkEmailContentInsideCampaignStep(
          stepId = StepId(5L),
          campaignId = CampaignId(3L),
          teamId = TeamId(3L),
        )
        .map(reasonsAndStepLabel => {
          assert(false)
        })
        .recover {
          case e => assert(true)
        }
    }

    it("should return empty list if step is not an email step") {

      (campaignStepVariantDAO.findStepWithVariants(
        _: StepId,
        _: CampaignId
      ))
        .expects(*, *)
        .returning(Success(campaignStepWithChildrenLinkedinMessage))

      spamMonitorService.checkEmailContentInsideCampaignStep(
          stepId = StepId(5L),
          campaignId = CampaignId(3L),
          teamId = TeamId(3L),
        )
        .map(reasonsAndStepLabel => {
          assert(reasonsAndStepLabel.underReviewReasons.isEmpty)
        })
        .recover { case _ => assert(false) }
    }

    it("should return empty sequence if content has no red flags") {

      (campaignStepVariantDAO.findStepWithVariants(
        _: StepId,
        _: CampaignId
      ))
        .expects(*, *)
        .returning(Success(campaignStepWithChildrenAutoEmail))

      (contentAnalysisService.categorizeEmail(
        _: TeamId,
        _: String,
        _: String
      )(
        _: WSClient,
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(*, *, *, *, *, *)
        .returning(Future(List()))

      (contentAnalysisService.categorizeEmail(
        _: TeamId,
        _: String,
        _: String
      )(
        _: WSClient,
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(*, *, *, *, *, *)
        .returning(Future(List()))

      spamMonitorService.checkEmailContentInsideCampaignStep(
          stepId = StepId(5L),
          campaignId = CampaignId(3L),
          teamId = TeamId(3L),
        )
        .map(reasonsAndStepLabel => {
          assert(reasonsAndStepLabel.underReviewReasons.isEmpty)
        })
        .recover { case e => assert(false) }
    }

    it("should return flattened sequence of UnderReviewReasons.") {

      (campaignStepVariantDAO.findStepWithVariants(
        _: StepId,
        _: CampaignId
      ))
        .expects(*, *)
        .returning(Success(campaignStepWithChildrenAutoEmail))

      (contentAnalysisService.categorizeEmail(
        _: TeamId,
        _: String,
        _: String
      )(
        _: WSClient,
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(*, *, *, *, *, *)
        .returning(Future(List(UnderReviewReason.Spam, UnderReviewReason.Violence)))

      (contentAnalysisService.categorizeEmail(
        _: TeamId,
        _: String,
        _: String
      )(
        _: WSClient,
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(*, *, *, *, *, *)
        .returning(Future(List(UnderReviewReason.Violence, UnderReviewReason.Hate)))

      spamMonitorService.checkEmailContentInsideCampaignStep(
          stepId = StepId(5L),
          campaignId = CampaignId(3L),
          teamId = TeamId(3L),
        )
        .map(reasonsAndStepLabel => {
          assert(reasonsAndStepLabel.underReviewReasons.size == 3 &&
            reasonsAndStepLabel.underReviewReasons.contains(UnderReviewReason.Spam) &&
            reasonsAndStepLabel.underReviewReasons.contains(UnderReviewReason.Hate) &&
            reasonsAndStepLabel.underReviewReasons.contains(UnderReviewReason.Violence)
          )
        })
        .recover {
          case _ => assert(false)
        }
    }

  }

  describe("sendEmailToOwner") {

    val orgMetadata = OrgMetadataFixture.orgMetadataFixture2
    val organization: Organization = Organization(
      id = 1,
      name = "Animesh",
      owner_account_id = Some(1),
      plan_type = PlanType.PAID.toString,
      plan_name = PlanType.PAID.toString,
      trial_ends_at = DateTime.now(),
      additional_spam_tests = 5,
      is_agency = false,
      fp_referrer_account_id = None,
      affiliateTrackerOpt = None
    )

    it("Error while getting Org") {
      (organizationDAO.getOrg)
        .expects(1)
        .returning(Failure(err))
      val result = spamMonitorService.sendEmailToOwner(
        orgId = 1,
        owner_email = "<EMAIL>",
        owner_name = Some("Animesh"),
        underReviewReason = UnderReviewReason.HighBounceRate
      )

      assert(result == Failure(err))
    }

    it("No Org Found") {
      (organizationDAO.getOrg)
        .expects(1)
        .returning(Success(None))
      val result = spamMonitorService.sendEmailToOwner(
        orgId = 1,
        owner_email = "<EMAIL>",
        owner_name = Some("Animesh"),
        underReviewReason = UnderReviewReason.HighBounceRate
      )

      assert(result.failed.get.getMessage == "Org not found after updating")
    }

    it("Error while getWhenTheLastSpamReviewEmailWasSent ") {
      (organizationDAO.getOrg)
        .expects(1)
        .returning(Success(Some(organization)))
      (accountDAO.getWhenTheLastSpamReviewEmailWasSent)
        .expects(AccountId(id = organization.owner_account_id.get)) // FIXME VALUECLASS
        .returning(Failure(err))
      val result = spamMonitorService.sendEmailToOwner(
        orgId = 1,
        owner_email = "<EMAIL>",
        owner_name = Some("Animesh"),
        underReviewReason = UnderReviewReason.HighBounceRate
      )

      assert(result == Failure(err))
    }

    it("Email sent in the last 24 hours") {
      (organizationDAO.getOrg)
        .expects(1)
        .returning(Success(Some(organization)))

      (accountDAO.getWhenTheLastSpamReviewEmailWasSent)
        .expects(AccountId(id = organization.owner_account_id.get)) // FIXME VALUECLASS
        .returning(Success(Some(DateTime.now().minusHours(12))))

      (emailNotificationService.sendMailFromAdmin(
        _: String,
        _: Option[String],
        _: String,
        _: String,
        _: Option[Boolean],
        _: Option[String],
        _: Option[String],
        _: Seq[String],
        _: Boolean)(
        _: WSClient,
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(
          *,
          *,
          *,
          *,
          *, *, *, *, *, *, *, *)
        //.returning(Failure(err))
        .never()

      val result = spamMonitorService.sendEmailToOwner(
        orgId = 1,
        owner_email = "<EMAIL>",
        owner_name = Some("Animesh"),
        underReviewReason = UnderReviewReason.HighBounceRate
      )

      assert(result == Success({}))
    }


    it("Email sent before the last 24 hours but email sending fail") {
      (organizationDAO.getOrg)
        .expects(1)
        .returning(Success(Some(organization)))

      (accountDAO.getWhenTheLastSpamReviewEmailWasSent)
        .expects(AccountId(id = organization.owner_account_id.get)) // FIXME VALUECLASS
        .returning(Success(Some(DateTime.now().minusHours(30))))

      (emailNotificationService.sendMailFromAdmin(
        _: String,
        _: Option[String],
        _: String,
        _: String,
        _: Option[Boolean],
        _: Option[String],
        _: Option[String],
        _: Seq[String],
        _: Boolean)(
        _: WSClient,
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(
          "<EMAIL>",
          Some("Animesh"),
          "URGENT: Your campaign(s) are under review for Spam Check",
          s"Animesh, We regret to inform you that your campaign was sent under review for ${UnderReviewReason.HighBounceRate}",
          None, None, None, Seq(), true, *, *, *)
        .returning(Failure(err))
      val result = spamMonitorService.sendEmailToOwner(
        orgId = 1,
        owner_email = "<EMAIL>",
        owner_name = Some("Animesh"),
        underReviewReason = UnderReviewReason.HighBounceRate
      )

      assert(result == Failure(err))
    }


    it("Email sent before the last 24 hours") {
      (organizationDAO.getOrg)
        .expects(1)
        .returning(Success(Some(organization)))

      (accountDAO.getWhenTheLastSpamReviewEmailWasSent)
        .expects(AccountId(id = organization.owner_account_id.get)) // FIXME VALUECLASS
        .returning(Success(Some(DateTime.now().minusHours(30))))

      (emailNotificationService.sendMailFromAdmin(
        _: String,
        _: Option[String],
        _: String,
        _: String,
        _: Option[Boolean],
        _: Option[String],
        _: Option[String],
        _: Seq[String],
        _: Boolean)(
        _: WSClient,
        _: ExecutionContext,
        _: SRLogger
      ))
        .expects(
          "<EMAIL>",
          Some("Animesh"),
          "URGENT: Your campaign(s) are under review for Spam Check",
          s"Animesh, We regret to inform you that your campaign was sent under review for ${UnderReviewReason.HighBounceRate}",
          None, None, None, Seq(), true, *, *, *)
        .returning(Success(()))
      val result = spamMonitorService.sendEmailToOwner(
        orgId = 1,
        owner_email = "<EMAIL>",
        owner_name = Some("Animesh"),
        underReviewReason = UnderReviewReason.HighBounceRate
      )

      assert(result == Success({}))
    }

  }
}

