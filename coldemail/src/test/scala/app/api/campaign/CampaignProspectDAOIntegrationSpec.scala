package app.api.campaign

import api.accounts.email.models.EmailServiceProvider
import api.accounts.{Account, TeamId}
import api.accounts.models.{AccountId, OrgId}
import api.campaigns.models.{CampaignEmailSettingsId, CampaignStepType, CurrentStepStatusForScheduler, CurrentStepStatusForSchedulerData}
import api.campaigns.{CampaignProspectUpdateScheduleStatus, CampaignWithStatsAndEmail, UpdateNextScheduleAtData}
import api.campaigns.services.CampaignId
import api.prospects.models.{ProspectDataForChannelScheduling, ProspectId}
import db_test_spec.api.{DbTestingBeforeAllAndAfterAll, InitialData}
import db_test_spec.api.accounts.fixtures.NewAccountAndEmailSettingData
import db_test_spec.api.campaigns.fixtures.{CreateNewCampaignFixture, CreateStepForCampaignFixture}
import db_test_spec.api.campaigns.test_utils.{CampaignUtils, CreateAndStartCampaignData}
import db_test_spec.api.prospects.fixtures.ProspectFixtureForIntegrationTest
import db_test_spec.api.scheduler.dao.SchedulerTestDAO
import db_test_spec.api.scheduler.dao.SchedulerTestDAO.CampaignProspectTestData
import org.joda.time.DateTime
import sr_scheduler.models.CampaignForScheduling.CampaignEmailSettingForScheduler
import sr_scheduler.models.ChannelType
import utils.SRLogger
import utils.helpers.LogHelpers
import utils.mq.channel_scheduler.SchedulerMapStepIdAndDelay

import scala.concurrent.Future

class CampaignProspectDAOIntegrationSpec extends DbTestingBeforeAllAndAfterAll {

  given logger: SRLogger = new SRLogger("[CampaignProspectDAOIntegrationSpec] ::")

  describe("Testing CampaignProspectDAO._updateScheduledStatus") {
    it("should update the status when tasks are scheduled of cp") {
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val result = for {
        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 1
        )

        prospectIds: Seq[Long] <- Future.fromTry {
          campaignProspectDAO._updateScheduledStatus(
            scheduledCampaignProspectData = Seq(CampaignProspectUpdateScheduleStatus(
              current_step_status_for_scheduler_data = CurrentStepStatusForSchedulerData.Due(due_at = DateTime.now()),
              current_step_type = CampaignStepType.AutoEmailStep,
              current_step_task_id = "task_123",
              step_id = createAndStartCampaignData.addStep.step_id,
              campaign_id = createAndStartCampaignData.startCampaign.id,
              prospect_id = createAndStartCampaignData.addProspect.head.id,
              email_message_id = None,
              current_campaign_email_settings_id = None
            )))
        }
      } yield {
        prospectIds
      }

      result
        .map(prospectIds => {
          assert(prospectIds.size == 1)
        })
        .recover {
          case e =>
            println(e.toString)
            assert(false)
        }
    }

    it("should update the status when tasks are done of cp") {
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val result = for {
        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 1
        )

        prospectIds: Seq[Long] <- Future.fromTry {
          campaignProspectDAO._updateScheduledStatus(
            scheduledCampaignProspectData = Seq(CampaignProspectUpdateScheduleStatus(
              current_step_status_for_scheduler_data = CurrentStepStatusForSchedulerData.Done(done_at = DateTime.now()),
              current_step_type = CampaignStepType.AutoEmailStep,
              current_step_task_id = "task_123",
              step_id = createAndStartCampaignData.addStep.step_id,
              campaign_id = createAndStartCampaignData.startCampaign.id,
              prospect_id = createAndStartCampaignData.addProspect.head.id,
              email_message_id = None,
              current_campaign_email_settings_id = None
            )))
        }
      } yield {
        prospectIds
      }

      result
        .map(prospectIds => {
          assert(prospectIds.size == 1)
        })
        .recover {
          case e =>
            println(e.toString)
            assert(false)
        }
    }

    it("should update the status when tasks are skipped of cp") {
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
      val skippedAt = DateTime.now()

      val result: Future[(Seq[Long], Option[CampaignProspectTestData], Option[CampaignProspectTestData])] = for {
        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 1
        )
        originalProspectDataOpt: Option[CampaignProspectTestData] <- Future.fromTry(
          SchedulerTestDAO.getCampaignProspectTestData(CampaignId(id = createAndStartCampaignData.campaign.id),ProspectId(id = createAndStartCampaignData.addProspect.head.id) , TeamId(id = createAndStartCampaignData.startCampaign.team_id))
        )
        updatedProspectIds: Seq[Long] <- Future.fromTry {
          campaignProspectDAO._updateScheduledStatus(
            scheduledCampaignProspectData = Seq(CampaignProspectUpdateScheduleStatus(
              current_step_status_for_scheduler_data = CurrentStepStatusForSchedulerData.Skipped(skipped_at = skippedAt),
              current_step_type = CampaignStepType.AutoEmailStep,
              current_step_task_id = "task_skip_123",
              step_id = createAndStartCampaignData.addStep.step_id,
              campaign_id = createAndStartCampaignData.campaign.id,
              prospect_id =  createAndStartCampaignData.addProspect.head.id,
              email_message_id = None,
              current_campaign_email_settings_id = None
            )))
          }
        updatedProspectDataOpt: Option[CampaignProspectTestData] <- Future.fromTry(
          SchedulerTestDAO.getCampaignProspectTestData(CampaignId(id = createAndStartCampaignData.campaign.id),ProspectId(id = createAndStartCampaignData.addProspect.head.id) , TeamId(id = createAndStartCampaignData.startCampaign.team_id))
        )
      } yield {
        (updatedProspectIds, originalProspectDataOpt, updatedProspectDataOpt)
      }

      result
        .map { case (
          prospectIds: Seq[Long],
          originalProspectDataOpt: Option[CampaignProspectTestData],
          updatedProspectDataOpt: Option[CampaignProspectTestData]
        ) =>
          assert(prospectIds.size == 1)
          assert(originalProspectDataOpt.isDefined, "Original prospect data not found")
          assert(updatedProspectDataOpt.isDefined, "Updated prospect data not found")
          val originalProspectData = originalProspectDataOpt.get
          val updatedProspectData = updatedProspectDataOpt.get
          assert(prospectIds.head == originalProspectData.prospect_id)
          assert(updatedProspectData.current_step_status_for_scheduler.contains(CurrentStepStatusForScheduler.Skipped))
          assert(updatedProspectData.latest_task_done_or_skipped_at.exists(_.getMillis == skippedAt.getMillis))
//          assert(updatedProspectData.current_step_id.contains(createAndStartCampaignData.addStep.step_id))
          assert(updatedProspectData.current_step_task_id.contains("task_skip_123"))
        }
        .recover {
          case e =>
            logger.error(s"Test failed for Skipped status: ${e.getMessage}", e)
            println(e.toString)
            assert(false)
        }
    }

    it("should update the status when tasks are pending approval") {
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
      val pendingApprovalAt = DateTime.now()

      val result: Future[(Seq[Long], Option[CampaignProspectTestData], Option[CampaignProspectTestData])] = for {
        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 1
        )

        originalProspectDataOpt: Option[CampaignProspectTestData] <- Future.fromTry(
          SchedulerTestDAO.getCampaignProspectTestData(CampaignId(id = createAndStartCampaignData.campaign.id),ProspectId(id = createAndStartCampaignData.addProspect.head.id) , TeamId(id = createAndStartCampaignData.startCampaign.team_id))
        )
        updatedProspectIds: Seq[Long] <- Future.fromTry {
          campaignProspectDAO._updateScheduledStatus(
            scheduledCampaignProspectData = Seq(CampaignProspectUpdateScheduleStatus(
              current_step_status_for_scheduler_data = CurrentStepStatusForSchedulerData.PendingApproval(pending_approval_at = pendingApprovalAt),
              current_step_type = CampaignStepType.ManualEmailStep,
              current_step_task_id = "task_pending_123",
              step_id = createAndStartCampaignData.addStep.step_id,
              campaign_id = createAndStartCampaignData.campaign.id,
              prospect_id =  createAndStartCampaignData.addProspect.head.id,
              email_message_id = None,
              current_campaign_email_settings_id = None
            )))
          }
        updatedProspectDataOpt: Option[CampaignProspectTestData] <- Future.fromTry(
          SchedulerTestDAO.getCampaignProspectTestData(CampaignId(id = createAndStartCampaignData.campaign.id),ProspectId(id = createAndStartCampaignData.addProspect.head.id) , TeamId(id = createAndStartCampaignData.startCampaign.team_id))
        )
      } yield {
        (updatedProspectIds, originalProspectDataOpt, updatedProspectDataOpt)
      }

      result
        .map { case (
          prospectIds: Seq[Long],
          originalProspectDataOpt: Option[CampaignProspectTestData],
          updatedProspectDataOpt: Option[CampaignProspectTestData]
        ) =>
          assert(prospectIds.size == 1)
          assert(originalProspectDataOpt.isDefined, "Original prospect data not found")
          assert(updatedProspectDataOpt.isDefined, "Updated prospect data not found")
          val originalProspectData = originalProspectDataOpt.get
          val updatedProspectData = updatedProspectDataOpt.get
          assert(prospectIds.head == originalProspectData.prospect_id)
          assert(updatedProspectData.current_step_status_for_scheduler.contains(CurrentStepStatusForScheduler.PendingApproval))
          assert(updatedProspectData.last_scheduled.exists(_.getMillis == pendingApprovalAt.getMillis))
//          assert(!updatedProspectData.current_step_id.contains(createAndStartCampaignData.addStep.step_id))
          assert(updatedProspectData.current_step_task_id.contains("task_pending_123"))
          assert(updatedProspectData.current_step_type.contains(CampaignStepType.ManualEmailStep.toKey))
        }
        .recover {
          case e =>
            logger.error(s"Test failed for PendingApproval status: ${e.getMessage}", e)
            println(e.toString)
            assert(false)
        }
    }

    it("should update the status when tasks are approved") {
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
      val dummyPendingStepId = 999L
      val dummyPendingTaskId = "task_pending_prev"
      val approvedTaskId = "task_pending_prev"

      val result: Future[(Seq[Long], Option[CampaignProspectTestData], Option[CampaignProspectTestData])] = for {
        // 1. Setup
        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 1
        )
        // 2. Set status to PendingApproval
        _ <- Future.fromTry {
           campaignProspectDAO._updateScheduledStatus(
             scheduledCampaignProspectData = Seq(CampaignProspectUpdateScheduleStatus(
               current_step_status_for_scheduler_data = CurrentStepStatusForSchedulerData.PendingApproval(pending_approval_at = DateTime.now().minusMinutes(5)),
               current_step_type = CampaignStepType.ManualEmailStep,
               current_step_task_id = dummyPendingTaskId,
               step_id = dummyPendingStepId,
               campaign_id = createAndStartCampaignData.campaign.id,
               prospect_id =  createAndStartCampaignData.addProspect.head.id,
               email_message_id = None,
               current_campaign_email_settings_id = None
             )))
         }
        // 3. Get state after PendingApproval
        prospectDataAfterPendingOpt: Option[CampaignProspectTestData] <- Future.fromTry(
          SchedulerTestDAO.getCampaignProspectTestData(
            CampaignId(id = createAndStartCampaignData.campaign.id),
            ProspectId(id = createAndStartCampaignData.addProspect.head.id) ,
            TeamId(id = createAndStartCampaignData.startCampaign.team_id)
          )
        )

        // 4. Set status to Approved
        updatedProspectIds: Seq[Long] <- Future.fromTry {
          campaignProspectDAO._updateScheduledStatus(
            scheduledCampaignProspectData = Seq(CampaignProspectUpdateScheduleStatus(
              current_step_status_for_scheduler_data = CurrentStepStatusForSchedulerData.Approved(),
              current_step_type = CampaignStepType.ManualEmailStep,
              current_step_task_id = approvedTaskId,
              step_id = createAndStartCampaignData.addStep.step_id, // Pass the actual step ID for the approved step
              campaign_id = createAndStartCampaignData.campaign.id,
              prospect_id =  createAndStartCampaignData.addProspect.head.id,
              email_message_id = None,
              current_campaign_email_settings_id = None
            )))
          }
        // 5. Get state after Approved
        prospectDataAfterApprovedOpt: Option[CampaignProspectTestData] <- Future.fromTry(
          SchedulerTestDAO.getCampaignProspectTestData(
            CampaignId(id = createAndStartCampaignData.campaign.id),
            ProspectId(id = createAndStartCampaignData.addProspect.head.id) ,
            TeamId(id = createAndStartCampaignData.startCampaign.team_id)
          )
        )
      } yield {
        (updatedProspectIds, prospectDataAfterPendingOpt, prospectDataAfterApprovedOpt)
      }

      result
        .map { case (
          prospectIds: Seq[Long],
          prospectDataAfterPendingOpt: Option[CampaignProspectTestData],
          prospectDataAfterApprovedOpt: Option[CampaignProspectTestData]
        ) =>
          assert(prospectIds.size == 1, s"Expected 1 updated prospect ID, got ${prospectIds.size}")
          assert(prospectDataAfterPendingOpt.isDefined, "Prospect data after PendingApproval not found")
          assert(prospectDataAfterApprovedOpt.isDefined, "Prospect data after Approved not found")

          val prospectDataAfterPending = prospectDataAfterPendingOpt.get
          val prospectDataAfterApproved = prospectDataAfterApprovedOpt.get

          assert(prospectIds.head == prospectDataAfterPending.prospect_id)

          // Verify state after Approved
          assert(prospectDataAfterApproved.current_step_status_for_scheduler.contains(CurrentStepStatusForScheduler.Approved), "Status should be Approved")
          assert(prospectDataAfterApproved.last_scheduled == prospectDataAfterPending.last_scheduled, "last_scheduled should not change from PendingApproval time")
          assert(prospectDataAfterApproved.latest_task_done_or_skipped_at == prospectDataAfterPending.latest_task_done_or_skipped_at, "latest_task_done_or_skipped_at should not change")

          // Verify step/task IDs remain from the PendingApproval state (as per original assertion logic)
          assert(prospectDataAfterApproved.current_step_id == prospectDataAfterPending.current_step_id, s"current_step_id should remain ${prospectDataAfterPending.current_step_id}, but was ${prospectDataAfterApproved.current_step_id}")
          assert(prospectDataAfterApproved.current_step_task_id == prospectDataAfterPending.current_step_task_id, s"current_step_task_id should remain ${prospectDataAfterPending.current_step_task_id}, but was ${prospectDataAfterApproved.current_step_task_id}")

          // Verify step type remains ManualEmailStep
          assert(prospectDataAfterApproved.current_step_type.contains(CampaignStepType.ManualEmailStep.toKey), "current_step_type should be ManualEmailStep")
        }
        .recover {
          case e =>
            logger.error(s"Test failed for Approved status: ${e.getMessage}", e)
            println(e.toString)
            assert(false)
        }
    }

    it("should update the status when AI content is queued") {
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
      val aiContentQueuedAt = DateTime.now()

      val result: Future[(Seq[Long], Option[CampaignProspectTestData], Option[CampaignProspectTestData])] = for {
        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 1
        )
        originalProspectDataOpt: Option[CampaignProspectTestData] <- Future.fromTry(
          SchedulerTestDAO.getCampaignProspectTestData(
            CampaignId(id = createAndStartCampaignData.campaign.id),
            ProspectId(id = createAndStartCampaignData.addProspect.head.id) ,
            TeamId(id = createAndStartCampaignData.startCampaign.team_id))
        )
        updatedProspectIds: Seq[Long] <- Future.fromTry {
          campaignProspectDAO._updateScheduledStatus(
            scheduledCampaignProspectData = Seq(CampaignProspectUpdateScheduleStatus(
              current_step_status_for_scheduler_data = CurrentStepStatusForSchedulerData.AiContentQueued(ai_content_queued_at = aiContentQueuedAt),
              current_step_type = CampaignStepType.AutoEmailMagicContent,
              current_step_task_id = "task_ai_queued_123",
              step_id = createAndStartCampaignData.addStep.step_id,
              campaign_id = createAndStartCampaignData.campaign.id,
              prospect_id =  createAndStartCampaignData.addProspect.head.id,
              email_message_id = None,
              current_campaign_email_settings_id = None
            )))
        }
        updatedProspectDataOpt: Option[CampaignProspectTestData] <- Future.fromTry(
          SchedulerTestDAO.getCampaignProspectTestData(CampaignId(id = createAndStartCampaignData.campaign.id),
            ProspectId(id = createAndStartCampaignData.addProspect.head.id) ,
            TeamId(id = createAndStartCampaignData.startCampaign.team_id))
        )
      } yield {
        (updatedProspectIds, originalProspectDataOpt, updatedProspectDataOpt)
      }

      result
        .map { case (
          prospectIds: Seq[Long],
          originalProspectDataOpt: Option[CampaignProspectTestData],
          updatedProspectDataOpt: Option[CampaignProspectTestData]
        ) =>
          assert(prospectIds.size == 1)
          assert(originalProspectDataOpt.isDefined, "Original prospect data not found")
          assert(updatedProspectDataOpt.isDefined, "Updated prospect data not found")
          val originalProspectData = originalProspectDataOpt.get
          val updatedProspectData = updatedProspectDataOpt.get
          assert(prospectIds.head == originalProspectData.prospect_id)
          assert(updatedProspectData.current_step_status_for_scheduler.contains(CurrentStepStatusForScheduler.AiContentQueued))
          assert(updatedProspectData.last_scheduled.exists(_.getMillis == aiContentQueuedAt.getMillis))
          assert(updatedProspectData.latest_task_done_or_skipped_at == originalProspectData.latest_task_done_or_skipped_at)
//          assert(!updatedProspectData.current_step_id.contains(createAndStartCampaignData.addStep.step_id))
          assert(updatedProspectData.current_step_task_id.contains("task_ai_queued_123"))
          assert(updatedProspectData.current_step_type.contains(CampaignStepType.AutoEmailMagicContent.toKey))
        }
        .recover {
          case e =>
            logger.error(s"Test failed for AiContentQueued status: ${e.getMessage}", e)
            println(e.toString)
            assert(false)
        }
    }
  }

  describe("Testing fetchProspectsV3Multichannel") {
    it("should return prospects when current step is skipped.") {

      val res = for {
        initialData: InitialData <- Future.fromTry {
          NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData()
        }

        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 1
        )

        completePreviousStep: Seq[Long] <- Future.fromTry {
          campaignProspectDAO._updateScheduledStatus(
            scheduledCampaignProspectData = Seq(
              CampaignProspectUpdateScheduleStatus(
                current_step_status_for_scheduler_data = CurrentStepStatusForSchedulerData.Skipped(skipped_at = DateTime.now().minusDays(2)),
                current_step_type = CampaignStepType.AutoEmailStep,
                current_step_task_id = "3",
                step_id = createAndStartCampaignData.addStep.step_id,
                campaign_id = createAndStartCampaignData.campaign.id.toLong,
                prospect_id = createAndStartCampaignData.addProspect.head.id,
                email_message_id = None,
                current_campaign_email_settings_id = None
              )
            )
          )
        }

        prospects: List[ProspectDataForChannelScheduling] <- Future.fromTry {
          println(completePreviousStep)
          campaignProspectDAO.fetchProspectsV3Multichannel(
            channelType = ChannelType.EmailChannel,
            allowedProspectTimezones = List("Asia/Kolkata"),
            campaignId = createAndStartCampaignData.campaign.id,
            teamId = TeamId(createAndStartCampaignData.startCampaign.team_id),
            limit = 50,
            channelRelevantStepIdAndDelay = Vector(SchedulerMapStepIdAndDelay(
              is_head_step_in_the_campaign = false,
              currentStepType = CampaignStepType.AutoEmailStep,
              nextStepType = CampaignStepType.AutoEmailStep,
              currentStepId = createAndStartCampaignData.addStep.step_id,
              delayTillNextStep = 86400
            )),
            newProspectsInCampaign = false,
            firstStepIsMagicContent = false,
            sendOnlyToProspectsWhoWereSentInCurrentCycle = None,
            campaign_email_setting = Some(CampaignEmailSettingForScheduler(
              sender_email_settings_id = createAndStartCampaignData.campaign.settings.campaign_email_settings.head.sender_email_setting_id.emailSettingId.toInt,
              receiver_email_settings_id = createAndStartCampaignData.campaign.settings.campaign_email_settings.head.receiver_email_setting_id.emailSettingId.toInt,
              campaign_email_settings_id = createAndStartCampaignData.campaign.settings.campaign_email_settings.head.id,
              emailServiceProvider = EmailServiceProvider.OTHER
            )
            ),
            orgId = OrgId(initialData.account.org.id),
//            emailNotCompulsoryEnabled= false,
            enable_magic_column = false,
            useModifiedQueryForDripCampaign = false
          )
        }
      } yield {
        prospects
      }

      res.map(prospects => {
        assert(prospects.size == 1)
      })
        .recover {
          case e =>
            println(e.toString)
            logger.error("Failed fetchProspectsV3Multichannel", e)
            assert(false)
        }

    }

    it("should return prospects when current step is done.") {

      val res = for {
        initialData: InitialData <- Future.fromTry {
          NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData()
        }

        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 1
        )

        completePreviousStep: Seq[Long] <- Future.fromTry {
          campaignProspectDAO._updateScheduledStatus(
            scheduledCampaignProspectData = Seq(
              CampaignProspectUpdateScheduleStatus(
                current_step_status_for_scheduler_data = CurrentStepStatusForSchedulerData.Done(done_at = DateTime.now().minusDays(2)),
                current_step_type = CampaignStepType.AutoEmailStep,
                current_step_task_id = "3",
                step_id = createAndStartCampaignData.addStep.step_id,
                campaign_id = createAndStartCampaignData.campaign.id,
                prospect_id = createAndStartCampaignData.addProspect.head.id,
                email_message_id = None,
                current_campaign_email_settings_id = None
              )
            )
          )
        }

        prospects: List[ProspectDataForChannelScheduling] <- Future.fromTry {
          println(completePreviousStep)
          campaignProspectDAO.fetchProspectsV3Multichannel(
            channelType = ChannelType.EmailChannel,
            allowedProspectTimezones = List("Asia/Kolkata"),
            campaignId = createAndStartCampaignData.campaign.id,
            teamId = TeamId(createAndStartCampaignData.startCampaign.team_id),
            limit = 50,
            channelRelevantStepIdAndDelay = Vector(SchedulerMapStepIdAndDelay(
              is_head_step_in_the_campaign = false,
              currentStepType = CampaignStepType.AutoEmailStep,
              nextStepType = CampaignStepType.AutoEmailStep,
              currentStepId = createAndStartCampaignData.addStep.step_id,
              delayTillNextStep = 86400
            )),
            newProspectsInCampaign = false,
            firstStepIsMagicContent = false,
            sendOnlyToProspectsWhoWereSentInCurrentCycle = None,
            campaign_email_setting = Some(CampaignEmailSettingForScheduler(
              sender_email_settings_id = createAndStartCampaignData.campaign.settings.campaign_email_settings.head.sender_email_setting_id.emailSettingId.toInt,
              receiver_email_settings_id = createAndStartCampaignData.campaign.settings.campaign_email_settings.head.receiver_email_setting_id.emailSettingId.toInt,
              campaign_email_settings_id = createAndStartCampaignData.campaign.settings.campaign_email_settings.head.id,
              emailServiceProvider = EmailServiceProvider.OTHER
            )),
            orgId = OrgId(initialData.account.org.id),
             //emailNotCompulsoryEnabled = false,
            enable_magic_column = false,
            useModifiedQueryForDripCampaign = false
          )
        }
      } yield {
        prospects
      }

      res.map(prospects => {
          assert(prospects.size == 1)
        })
        .recover {
          case e =>
            println(e.toString)
            logger.error("Failed fetchProspectsV3Multichannel", e)
            assert(false)
        }

    }

    it("should not return prospects when current step is skipped and step Delay has not passed.") {

      val res = for {
        initialData: InitialData <- Future.fromTry {
          NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData()
        }

        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 1
        )

        completePreviousStep: Seq[Long] <- Future.fromTry {
          campaignProspectDAO._updateScheduledStatus(
            scheduledCampaignProspectData = Seq(
              CampaignProspectUpdateScheduleStatus(
                current_step_status_for_scheduler_data = CurrentStepStatusForSchedulerData.Skipped(skipped_at = DateTime.now().minusHours(6)),
                current_step_type = CampaignStepType.AutoEmailStep,
                current_step_task_id = "3",
                step_id = createAndStartCampaignData.addStep.step_id,
                campaign_id = createAndStartCampaignData.startCampaign.id,
                prospect_id = createAndStartCampaignData.addProspect.head.id,
                email_message_id = None,
                current_campaign_email_settings_id = None
              )
            )
          )
        }

        prospects: List[ProspectDataForChannelScheduling] <- Future.fromTry {
          println(completePreviousStep)
          campaignProspectDAO.fetchProspectsV3Multichannel(
            channelType = ChannelType.EmailChannel,
            allowedProspectTimezones = List("Asia/Kolkata"),
            campaignId = createAndStartCampaignData.startCampaign.id,
            teamId = TeamId(initialData.head_team_id),
            limit = 50,
            channelRelevantStepIdAndDelay = Vector(SchedulerMapStepIdAndDelay(
              is_head_step_in_the_campaign = false,
              currentStepType = CampaignStepType.AutoEmailStep,
              nextStepType = CampaignStepType.AutoEmailStep,
              currentStepId = createAndStartCampaignData.addStep.step_id,
              delayTillNextStep = 86400
            )),
            newProspectsInCampaign = false,
            firstStepIsMagicContent = false,
            sendOnlyToProspectsWhoWereSentInCurrentCycle = None,
            orgId = OrgId(initialData.account.org.id),
            campaign_email_setting = Some(CampaignEmailSettingForScheduler(
              sender_email_settings_id = createAndStartCampaignData.campaign.settings.campaign_email_settings.head.sender_email_setting_id.emailSettingId.toInt,
              receiver_email_settings_id = createAndStartCampaignData.campaign.settings.campaign_email_settings.head.receiver_email_setting_id.emailSettingId.toInt,
              campaign_email_settings_id = createAndStartCampaignData.campaign.settings.campaign_email_settings.head.id,
              emailServiceProvider = EmailServiceProvider.OTHER
            )),
             //emailNotCompulsoryEnabled = false,
            enable_magic_column = false,
            useModifiedQueryForDripCampaign = false
          )
        }
      } yield {
        prospects
      }

      res.map(prospects => {
          assert(prospects.size == 0)
        })
        .recover {
          case e =>
            println(e.toString)
            logger.error("Failed fetchProspectsV3Multichannel", e)
            assert(false)
        }

    }

    it("should not return any prospects if last task is due") {

      val res = for {
        initialData: InitialData <- Future.fromTry {
          NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData()
        }

        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 1
        )

        completePreviousStep: Seq[Long] <- Future.fromTry {
          campaignProspectDAO._updateScheduledStatus(
            scheduledCampaignProspectData = Seq(
              CampaignProspectUpdateScheduleStatus(
                current_step_status_for_scheduler_data = CurrentStepStatusForSchedulerData.Due(due_at = DateTime.now()),
                current_step_type = CampaignStepType.AutoEmailStep,
                current_step_task_id = "3",
                step_id = createAndStartCampaignData.addStep.step_id,
                campaign_id = createAndStartCampaignData.startCampaign.id,
                prospect_id = createAndStartCampaignData.addProspect.head.id,
                email_message_id = None,
                current_campaign_email_settings_id = None
              )
            )
          )
        }

        prospects: List[ProspectDataForChannelScheduling] <- Future.fromTry {
          println(completePreviousStep)
          campaignProspectDAO.fetchProspectsV3Multichannel(
            channelType = ChannelType.EmailChannel,
            allowedProspectTimezones = List("Asia/Kolkata"),
            campaignId = createAndStartCampaignData.campaign.id,
            teamId = TeamId(createAndStartCampaignData.startCampaign.team_id),
            limit = 50,
            channelRelevantStepIdAndDelay = Vector(SchedulerMapStepIdAndDelay(
              is_head_step_in_the_campaign = false,
              currentStepType = CampaignStepType.AutoEmailStep,
              nextStepType = CampaignStepType.AutoEmailStep,
              currentStepId = createAndStartCampaignData.addStep.step_id,
              delayTillNextStep = 86400
            )),
            newProspectsInCampaign = false,
            firstStepIsMagicContent = false,
            sendOnlyToProspectsWhoWereSentInCurrentCycle = None,
            campaign_email_setting = Some(CampaignEmailSettingForScheduler(
              sender_email_settings_id = createAndStartCampaignData.campaign.settings.campaign_email_settings.head.sender_email_setting_id.emailSettingId.toInt,
              receiver_email_settings_id = createAndStartCampaignData.campaign.settings.campaign_email_settings.head.receiver_email_setting_id.emailSettingId.toInt,
              campaign_email_settings_id = createAndStartCampaignData.campaign.settings.campaign_email_settings.head.id,
              emailServiceProvider = EmailServiceProvider.OTHER
            )),
            orgId = OrgId(initialData.account.org.id),
             //emailNotCompulsoryEnabled = false,
            enable_magic_column = false,
            useModifiedQueryForDripCampaign = false
          )
        }
      } yield {
        prospects
      }

      res.map(prospects => {
          assert(prospects.isEmpty)
        })
        .recover {
          case e =>
            println(e.toString)
            logger.error("Failed fetchProspectsV3Multichannel", e)
            assert(false)
        }

    }

    it("should not return any prospects if last task is done was done 6 hours ago and step delay is 1 day even if last_scheduled_at was 2 days ago.") {

      val res = for {
        initialData: InitialData <- Future.fromTry {
          NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData()
        }

        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 1
        )

        completePreviousStep: Seq[Long] <- Future.fromTry {
          campaignProspectDAO._updateScheduledStatus(
            scheduledCampaignProspectData = Seq(
              CampaignProspectUpdateScheduleStatus(
                current_step_status_for_scheduler_data = CurrentStepStatusForSchedulerData.Done(done_at = DateTime.now().minusHours(6)),
                current_step_type = CampaignStepType.AutoEmailStep,
                current_step_task_id = "3",
                step_id = createAndStartCampaignData.addStep.step_id,
                campaign_id = createAndStartCampaignData.campaign.id,
                prospect_id = createAndStartCampaignData.addProspect.head.id,
                email_message_id = None,
                current_campaign_email_settings_id = None
              )
            )
          )
        }

        prospects: List[ProspectDataForChannelScheduling] <- Future.fromTry {
          println(completePreviousStep)
          campaignProspectDAO.fetchProspectsV3Multichannel(
            channelType = ChannelType.EmailChannel,
            allowedProspectTimezones = List("Asia/Kolkata"),
            campaignId = createAndStartCampaignData.campaign.id,
            teamId = TeamId(initialData.head_team_id),
            limit = 50,
            channelRelevantStepIdAndDelay = Vector(SchedulerMapStepIdAndDelay(
              is_head_step_in_the_campaign = false,
              currentStepType = CampaignStepType.AutoEmailStep,
              nextStepType = CampaignStepType.AutoEmailStep,
              currentStepId = createAndStartCampaignData.addStep.step_id,
              delayTillNextStep = 86400
            )),
            newProspectsInCampaign = false,
            firstStepIsMagicContent = false,
            sendOnlyToProspectsWhoWereSentInCurrentCycle = None,
            campaign_email_setting = Some(CampaignEmailSettingForScheduler(
              sender_email_settings_id = createAndStartCampaignData.campaign.settings.campaign_email_settings.head.sender_email_setting_id.emailSettingId.toInt,
              receiver_email_settings_id = createAndStartCampaignData.campaign.settings.campaign_email_settings.head.receiver_email_setting_id.emailSettingId.toInt,
              campaign_email_settings_id = createAndStartCampaignData.campaign.settings.campaign_email_settings.head.id,
              emailServiceProvider = EmailServiceProvider.OTHER
            )),
            orgId = OrgId(initialData.account.org.id),
             //emailNotCompulsoryEnabled = false,
            enable_magic_column = false,
            useModifiedQueryForDripCampaign = false
          )
        }
      } yield {
        prospects
      }

      res.map(prospects => {
          assert(prospects.isEmpty)
        })
        .recover {
          case e =>
            println(e.toString)
            logger.error("Failed fetchProspectsV3Multichannel", e)
            assert(false)
        }

    }
  }

  describe("updateNextCheckForSchedulingAt") {
    it("should give success") {
      val result = campaignProspectDAO.updateNextCheckForSchedulingAt(
        data = List(
          UpdateNextScheduleAtData(
            campaignId = CampaignId(1),
            prospectId = ProspectId(1),
            teamId = TeamId(1),
            nexToBeCheckedAt = DateTime.now()
          )
        )
      )
//      println(s"${LogHelpers.getStackTraceAsString(result.failed.get)}")
      assert(result.isSuccess)
    }
  }

  describe("getPendingApprovalOrQueuedCountForCampaign") {

    it("should return correct count when prospects are PendingApproval or AIContentQueued for AutoEmailMagicContent step") {
      // Arrange: Start campaign and get prospects
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
      val teamId = TeamId(initialData.head_team_id)
      val result = for {
        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 4 // Create 4 prospects for different scenarios
        )
        campaignId = CampaignId(createAndStartCampaignData.campaign.id)
        stepId = createAndStartCampaignData.addStep.step_id // Use the actual step ID
        prospects = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id))
        prospectId1 = prospects(0)
        prospectId2 = prospects(1)
        prospectId3 = prospects(2)
        prospectId4 = prospects(3)

        // Update Prospect 1: PendingApproval, AutoEmailMagicContent
        _ <- Future.fromTry(campaignProspectDAO._updateScheduledStatus(Seq(CampaignProspectUpdateScheduleStatus(
          CurrentStepStatusForSchedulerData.PendingApproval(pending_approval_at = DateTime.now()),
          CampaignStepType.AutoEmailMagicContent, "task_p1", stepId, campaignId.id, prospectId1.id, None, None))))

        // Update Prospect 2: AIContentQueued, AutoEmailMagicContent
        _ <- Future.fromTry(campaignProspectDAO._updateScheduledStatus(Seq(CampaignProspectUpdateScheduleStatus(
          CurrentStepStatusForSchedulerData.AiContentQueued(ai_content_queued_at = DateTime.now()),
          CampaignStepType.AutoEmailMagicContent, "task_p2", stepId, campaignId.id, prospectId2.id, None, None))))

        // Update Prospect 3: Done status, AutoEmailMagicContent
        _ <- Future.fromTry(campaignProspectDAO._updateScheduledStatus(Seq(CampaignProspectUpdateScheduleStatus(
          CurrentStepStatusForSchedulerData.Done(done_at = DateTime.now()),
          CampaignStepType.AutoEmailMagicContent, "task_p3", stepId, campaignId.id, prospectId3.id, None, None))))

        // Update Prospect 4: PendingApproval, but different step type (AutoEmail)
        _ <- Future.fromTry(campaignProspectDAO._updateScheduledStatus(Seq(CampaignProspectUpdateScheduleStatus(
          CurrentStepStatusForSchedulerData.PendingApproval(pending_approval_at = DateTime.now()),
          CampaignStepType.AutoEmailStep, "task_p4", stepId, campaignId.id, prospectId4.id, None, None))))

        // Act
        count <- Future.fromTry(campaignProspectDAO.getPendingApprovalOrQueuedCountForCampaign(
          campaignId = campaignId,
          teamId = teamId,
          campaignStepType = CampaignStepType.AutoEmailMagicContent))

      } yield count

      // Assert
      result.map { count =>
        assert(count == 2) // Expecting prospectId1 and prospectId2
      }.recover {
        case e =>
          logger.error("Test failed: should return correct count", e)
          assert(false, s"Test failed: ${e.getMessage}")
      }
    }

    it("should return 0 when no prospects match the criteria (status or step type)") {
      // Arrange: Start campaign and update prospects to non-matching states
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
      val teamId = TeamId(initialData.head_team_id)
      val result = for {
        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 2
        )
        campaignId = CampaignId(createAndStartCampaignData.campaign.id)
        stepId = createAndStartCampaignData.addStep.step_id
        prospects = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id))
        prospectId1 = prospects(0)
        prospectId2 = prospects(1)

        // Update Prospect 1: Done status
        _ <- Future.fromTry(campaignProspectDAO._updateScheduledStatus(Seq(CampaignProspectUpdateScheduleStatus(
          CurrentStepStatusForSchedulerData.Done(done_at = DateTime.now()),
          CampaignStepType.AutoEmailMagicContent, "task_p1_done", stepId, campaignId.id, prospectId1.id, None, None))))

        // Update Prospect 2: Different step type (AutoEmail)
        _ <- Future.fromTry(campaignProspectDAO._updateScheduledStatus(Seq(CampaignProspectUpdateScheduleStatus(
          CurrentStepStatusForSchedulerData.PendingApproval(pending_approval_at = DateTime.now()),
          CampaignStepType.AutoEmailStep, "task_p2_auto", stepId, campaignId.id, prospectId2.id, None, None))))

        // Act
        count <- Future.fromTry(campaignProspectDAO.getPendingApprovalOrQueuedCountForCampaign(
          campaignId = campaignId,
          teamId = teamId,
          campaignStepType =  CampaignStepType.AutoEmailMagicContent))
      } yield count

      // Assert
      result.map { count =>
        assert(count == 0)
      }.recover {
        case e =>
          logger.error("Test failed: should return 0 when no prospects match", e)
          assert(false, s"Test failed: ${e.getMessage}")
      }
    }

    it("should return 0 when prospects are inactive or completed") {
       // Arrange: Start campaign, update status, then mark as inactive/completed
      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
      val teamId = TeamId(initialData.head_team_id)
      val result = for {
        createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData,
          generateProspectCountIfNoGivenProspect = 2
        )
        campaignId = CampaignId(createAndStartCampaignData.campaign.id)
        stepId = createAndStartCampaignData.addStep.step_id
        prospects = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id))
        prospectId1 = prospects(0) // To be marked inactive
        prospectId2 = prospects(1) // To be marked completed

        // Set statuses first (matching the criteria initially)
        _ <- Future.fromTry(campaignProspectDAO._updateScheduledStatus(Seq(CampaignProspectUpdateScheduleStatus(
          CurrentStepStatusForSchedulerData.PendingApproval(pending_approval_at = DateTime.now()),
          CampaignStepType.AutoEmailMagicContent, "task_p1_inactive", stepId, campaignId.id, prospectId1.id, None, None))))
        _ <- Future.fromTry(campaignProspectDAO._updateScheduledStatus(Seq(CampaignProspectUpdateScheduleStatus(
          CurrentStepStatusForSchedulerData.AiContentQueued(ai_content_queued_at = DateTime.now()),
          CampaignStepType.AutoEmailMagicContent, "task_p2_completed", stepId, campaignId.id, prospectId2.id, None, None))))

        // Now mark Prospect 1 as inactive
        _ <- Future.fromTry(campaignProspectDAO.unassign(
           permittedOwnerIds = Seq(initialData.account.internal_id),
           doerAccountId = initialData.account.internal_id,
           teamId = teamId.id,
           doerAccountName = "Admin",
           campaignId = campaignId.id,
           prospectIds = Seq(prospectId1.id)
        ))

        // Now mark Prospect 2 as completed
        _ <- Future.fromTry(campaignProspectDAOService.pauseStatusChangedByAdmin(
           permittedAccountIds = Seq(initialData.account.internal_id),
           statusChangedByAccountId = AccountId(initialData.account.internal_id),
           teamId = teamId.id,
           prospectEmails = Seq(),
           prospectIds = Seq(prospectId2.id),
           newPauseStatus = true, // true means completed/paused
           campaignIds = Seq(campaignId.id),
           Logger = logger
        ))


        // Act
        count <- Future.fromTry(campaignProspectDAO.getPendingApprovalOrQueuedCountForCampaign(
          campaignId = campaignId,
          teamId = teamId,
          campaignStepType = CampaignStepType.AutoEmailMagicContent)
        )
      } yield count

      // Assert
      result.map { count =>
        assert(count == 0)
      }.recover {
        case e =>
          logger.error("Test failed: should return 0 when prospects are inactive or completed", e)
          assert(false, s"Test failed: ${e.getMessage}")
      }
    }

  }


}
