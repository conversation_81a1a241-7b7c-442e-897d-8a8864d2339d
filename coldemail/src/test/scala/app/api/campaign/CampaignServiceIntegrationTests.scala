package app.api.campaign

import api.APIErrorResponse.ErrorResponseProspectsAssignApi
import api.CONSTANTS.API_MSGS
import api.accounts.models.{AccountId, OrgId}
import api.accounts.{Account, TeamId, TeamMember}
import api.{AppConfig, ErrorType}
import api.campaigns.{Campaign, CampaignBasicInfo, CampaignCreateForm, CampaignEmailSettings, CampaignEmailSettingsUuid, CampaignEmailSettingsV2, CampaignSettings, CampaignStep, CampaignStepVariantCreateOrUpdate, CampaignWithStatsAndEmail}
import api.campaigns.models.{CampaignEmailSettingsId, CampaignPublicApiErrorV3, CampaignStepData, CampaignStepType, CampaignType, FindCampaignResultPublicApi, FindCampaignResultPublicApiV3, FindCampaignsListingApiV3Response, IgnoreProspectsInOtherCampaigns}
import api.campaigns.services.{CampaignId, ProspectAssignErrorType}
import api.prospects.{CreateOrUpdateProspectsBatchApiError, CreateOrUpdateProspectsResult, ProspectCreateFormData, ProspectCreateFormDataV2, ProspectUuid}
import api.prospects.models.{AllCampaignStatsApiResponse, ProspectObjectForApiIntegrationTest, UpdateProspectType}
import db_test_spec.api.{DbTestingBeforeAllAndAfterAll, InitialData, SRSetupAndDeleteFixtures}
import db_test_spec.api.accounts.fixtures.{AccountFixtureForIntegrationTest, NewAccountAndEmailSettingData, NewAccountAndWhatsappSettingData}
import db_test_spec.api.campaigns.test_utils.{CampaignUtils, CreateAndStartCampaignData}
import io.smartreach.esp.api.emails.EmailSettingId
import io.sr.billing_common.models.PlanID.TRIAL
import org.joda.time.DateTime
import play.api.libs.json.{JsError, JsSuccess, JsValue, Json}
import play.api.test.FakeRequest
import utils.{Helpers, SRLogger}
import sr_scheduler.CampaignStatus
import sr_scheduler.models.CampaignEmailPriority
import utils.helpers.LogHelpers

import java.time.Instant
import scala.concurrent.{Await, ExecutionContext, Future}
import scala.util.{Failure, Success, Try}
import play.api.test.Helpers.*


/**
 * Integrations tests added below are using devapi
 * if these test cases fail then check id devapi.sreml.com is up
 * Also these test cases using specific account from dev environment
 * so any changes(deletion) for prospect(<EMAIL>)/team(80) might affect this test cases
 */



class CampaignServiceIntegrationTests extends DbTestingBeforeAllAndAfterAll {


  lazy val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
  
  given logger: SRLogger = new SRLogger("[CampaignServiceIntegrationTests] ::")

  
  final def checkViewTidViewAidAccess(loggedInAccount: Account, tid: Long
                                     ): Option[TeamMember] = {


    val ta = loggedInAccount.teams
      .find(t => t.team_id == tid)

    val member = ta.toSeq.flatMap(_.access_members)
      .find(mem => mem.team_id == tid && mem.user_id == loggedInAccount.internal_id)

    member

  }


  describe("Campaign api's Integration Tests") {

    describe("testing public api find campaign") {


      it("should give result in expected format with uuid for v1 api") {

        val teamId: Long = initialData.head_team_id

        var campaign_id = 0L

        val apiKey = initialData.teamUserLevelKey

        val final_result = for {
          createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
            initialData = initialData,
          )

          campaign_id <- Future {
            campaign_id = createAndStartCampaignData.campaign.id

            campaign_id
          }

          sendingUrl <- Future {
            s"/api/v1/campaigns/${campaign_id}?tid=${teamId}"
          }

          res <- {

            val request = FakeRequest(play.api.test.Helpers.GET, sendingUrl)
              .withHeaders(
                "X-API-KEY" -> apiKey,
                "Content-Type" -> "application/json")

            play.api.test.Helpers.route(testApi, request).get

          }
        } yield {
          res
        }
        val status: Int = play.api.test.Helpers.status(final_result)
        val json: JsValue = play.api.test.Helpers.contentAsJson(final_result)
        final_result.map(res => {

          if (status == 200) {

            val sentResponseData = (json \ "data" \ "campaign")
            sentResponseData.validate[FindCampaignResultPublicApi] match {
              case JsError(errors) => {
                assert(false)
              }

              case JsSuccess(campaign, _) =>
                assert(campaign.id == campaign_id)
            }

          } else {
            assert(false)
          }
        }).recover(err => {
          println(s"Error: ${LogHelpers.getStackTraceAsString(err)}")
          assert(false)
        })
      }

      it("should give result in expected format with uuid for v3 api") {

        val teamId: Long = initialData.head_team_id

        var campaign_uuid = ""

        val apiKey = initialData.teamUserLevelKey

        val final_result = for {
          createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
            initialData = initialData,
          )

          campaign_uuid <- Future {
            campaign_uuid = createAndStartCampaignData.campaign.uuid.get

            campaign_uuid
          }

          sendingUrl <- Future {
            s"/api/v3/campaigns/${campaign_uuid}"
          }

          res <- {

            val request = FakeRequest(play.api.test.Helpers.GET, sendingUrl)
              .withHeaders(
                "X-API-KEY" -> apiKey,
                "Content-Type" -> "application/json")

            play.api.test.Helpers.route(testApi, request).get
          }

        } yield {
          res
        }
        val status: Int = play.api.test.Helpers.status(final_result)
        val json: JsValue = play.api.test.Helpers.contentAsJson(final_result)
        final_result.map(res => {

          if (status == 200) {

            val sentResponseData = json
            sentResponseData.validate[FindCampaignResultPublicApiV3] match {
              case JsError(errors) => {
                println(s"Errors: $errors")
                assert(false)
              }

              case JsSuccess(campaign, _) =>
               assert(campaign.uuid.get == campaign_uuid)
            }

          } else {
            assert(false)
          }
        }).recover(err => {
          println(s"Error: ${LogHelpers.getStackTraceAsString(err)}")
          assert(false)
        })
      }

    }

  }

  describe("Campaign listing api's Integration Tests") {

    describe("testing public api find campaigns") {

      it("should give result in expected format with uuid for v1 api") {


        val teamId: Long = initialData.head_team_id

        val apiKey = initialData.teamUserLevelKey

        val final_result = for {
          createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
            initialData = initialData,
          )

          sendingUrl <- Future {
            s"/api/v1/campaigns?tid=${teamId}"
          }

          res <- {

            val request = FakeRequest(play.api.test.Helpers.GET, sendingUrl)
              .withHeaders(
                "X-API-KEY" -> apiKey,
                "Content-Type" -> "application/json")

            play.api.test.Helpers.route(testApi, request).get
          }

        } yield {
          res
        }
        val status: Int = play.api.test.Helpers.status(final_result)
        val json: JsValue = play.api.test.Helpers.contentAsJson(final_result)
        final_result.map(res => {

          if (status == 200) {

            val sentResponseData = (json \ "data" \ "campaigns")
            sentResponseData.validate[List[FindCampaignResultPublicApi]] match {
              case JsError(errors) => {
                assert(false)
              }

              case JsSuccess(_, _) =>
                 assert(true)
              }

          } else {
            assert(false)
          }
        }).recover(err => {
          println(s"Error: ${LogHelpers.getStackTraceAsString(err)}")
          assert(false)
        })

      }

      it("should give result in expected format with uuid for v2 api") {

        val teamId: Long = initialData.head_team_id
        var campaign_id: Long = 0L
        val apiKey = initialData.teamUserLevelKey

        val final_result = for {
          createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
            initialData = initialData,
          )

          campaign_id <- Future {
            campaign_id = createAndStartCampaignData.campaign.id

            campaign_id
          }

          sendingUrl <- Future {
            s"/api/v2/campaigns?tid=${teamId}"
          }

          res <- {
            val request = FakeRequest(play.api.test.Helpers.GET, sendingUrl)
              .withHeaders(
                "X-API-KEY" -> apiKey,
                "Content-Type" -> "application/json")

            play.api.test.Helpers.route(testApi, request).get
          }

        } yield {
          res
        }
        val status: Int = play.api.test.Helpers.status(final_result)
        val json: JsValue = play.api.test.Helpers.contentAsJson(final_result)
        final_result.map(res => {

          if (status == 200) {

            val sentResponseData = (json \ "data" \ "campaigns")
            sentResponseData.validate[List[FindCampaignResultPublicApi]] match {
              case JsError(errors) => {
                println(s"Errors: $errors")
                assert(false)
              }

              case JsSuccess(campaign, _) =>
                assert(campaign.map(_.id).contains(campaign_id))
            }

          } else {
            assert(false)
          }
        }).recover(err => {
          println(s"Error: ${LogHelpers.getStackTraceAsString(err)}")
          assert(false)
        })

      }

      it("should give result in expected format with uuid for v3 api") {

        val teamId: Long = initialData.head_team_id

        var campaign_uuid = ""

        val apiKey = initialData.teamUserLevelKey

        val final_result = for {
          createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
            initialData = initialData,
          )

          campaign_uuid <- Future {
            campaign_uuid = createAndStartCampaignData.campaign.uuid.get

            campaign_uuid
          }

          sendingUrl <- Future {
            s"/api/v3/campaigns"
          }

          res <- {
            val request = FakeRequest(play.api.test.Helpers.GET, sendingUrl)
              .withHeaders(
                "X-API-KEY" -> apiKey,
                "Content-Type" -> "application/json")

            play.api.test.Helpers.route(testApi, request).get
          }

        } yield {
          res
        }
        val status: Int = play.api.test.Helpers.status(final_result)
        val json: JsValue = play.api.test.Helpers.contentAsJson(final_result)
        final_result.map(res => {

          if (status == 200) {

            val sentResponseData = json
            sentResponseData.validate[FindCampaignsListingApiV3Response] match {
              case JsError(errors) => {
                println(s"Errors: $errors")
                assert(false)
              }

              case JsSuccess(campaign, _) =>
                assert(campaign.campaigns.map(_.uuid.get).contains(campaign_uuid))
            }

          } else {
            assert(false)
          }
        }).recover(err => {
          println(s"Error: ${LogHelpers.getStackTraceAsString(err)}")
          assert(false)
        })
      }

      it("should give result in expected format with uuid for v3 api and status param") {

        val teamId: Long = initialData.head_team_id

        var campaign_uuid = ""

        val apiKey = initialData.teamUserLevelKey

        val final_result = for {
          createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
            initialData = initialData,
          )

          campaign_uuid <- Future {
            campaign_uuid = createAndStartCampaignData.campaign.uuid.get

            campaign_uuid
          }

          sendingUrl <- Future {
            s"/api/v3/campaigns?status=running"
          }

          res <- {
            val request = FakeRequest(play.api.test.Helpers.GET, sendingUrl)
              .withHeaders(
                "X-API-KEY" -> apiKey,
                "Content-Type" -> "application/json")

            play.api.test.Helpers.route(testApi, request).get
          }

        } yield {
          res
        }
        val status: Int = play.api.test.Helpers.status(final_result)
        val json: JsValue = play.api.test.Helpers.contentAsJson(final_result)
        final_result.map(res => {

          if (status == 200) {

            val sentResponseData = json
            sentResponseData.validate[FindCampaignsListingApiV3Response] match {
              case JsError(errors) => {
                println(s"Errors: $errors")
                assert(false)
              }

              case JsSuccess(campaign, _) =>
                assert(campaign.campaigns.map(_.uuid.get).contains(campaign_uuid))
            }

          } else {
            assert(false)
          }
        }).recover(err => {
          println(s"Error: ${LogHelpers.getStackTraceAsString(err)}")
          assert(false)
        })

      }

    }

  }


  describe("Assign prospects To Campaign api's Integration Tests") {

    val campaign_id: Long = 121L
    val campaign_name = "CampaignName"
    val permittedAccountIds = Seq(2L)
    val teamId: Long = 37L
    val ownerId: Long = 2L

    val first_name = "first_name"
    val last_name = "last_name"
    val company = "CompanyName"
    val email = "<EMAIL>"
    val email_domain = "smartreach.com"

    val aDate = DateTime.parse("2022-3-27")


    val prospectCreateFormdata = ProspectCreateFormDataV2(
      email = Some(email),
      first_name = Some(first_name),
      last_name = Some(last_name),
      custom_fields = Some(Json.obj("followers" -> 500)),

      // should not break in old ui/integrations, currently used only inside createOrUpdateOne controller
      owner_id = Some(ownerId),

      list = Some("list"),
      company = Some(company),
      city = Some("kolkata"),
      country = Some("India"),
      timezone = Some("Asia/Kolkata"),
      created_at = None,

      state = None,
      job_title = None,
      phone_number = None,
      linkedin_url = None,
      tags = None
    )

    describe("testing public api Assign prospects To Campaign") {
      val ignore_prospects_in_other_campaigns = "ignore_prospects_added_in_other_campaigns"

      it("should give result in expected format with uuid for assign api") {

        val teamId: Long = initialData.head_team_id

        var campaign_uuid = ""

        val apiKey = initialData.teamUserLevelKey

        var assignedProspectUuid: String = ""
        val newProspectToBeAssigned: String = initialData.prospectsResult.head.prospect_uuid.get.uuid

        val final_result = for {
          createAndStartCampaignData1: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
            initialData = initialData
          )
          createAndStartCampaignData2: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
            initialData = initialData
          )

          campaign_uuid <- Future {
            campaign_uuid = createAndStartCampaignData2.campaign.uuid.get

            campaign_uuid
          }

          assignedProspectUuid <- Future {
            val puuid = createAndStartCampaignData1.addProspect.head.prospect_uuid.get.uuid
            assignedProspectUuid = assignedProspectUuid
            puuid
          }

          sendingUrl <- Future {
            s"/api/v3/campaigns/${campaign_uuid}/prospects"
          }

          res <- {

            val request = FakeRequest(play.api.test.Helpers.POST, sendingUrl)
              .withHeaders(
                "X-API-KEY" -> apiKey,
                "Content-Type" -> "application/json")
              .withJsonBody(Json.obj(
                "prospect_ids" -> List(newProspectToBeAssigned, assignedProspectUuid),
                "ignore_prospects_in_other_campaigns" -> ignore_prospects_in_other_campaigns
              ))

            play.api.test.Helpers.route(testApi, request).get
          }

        } yield {
          res
        }
        val status: Int = play.api.test.Helpers.status(final_result)
        val json: JsValue = play.api.test.Helpers.contentAsJson(final_result)
        final_result.map(res => {

          if (status == 200) {

            val sentResponseData = json

            val prospect_data = (sentResponseData \ "prospect_data")
            val total_assigned = (sentResponseData \ "total_assigned").as[Int]
            val returned_campaign_uuid = (sentResponseData \ "campaign_id").as[String]

            prospect_data.validate[List[ProspectObjectForApiIntegrationTest]] match {
              case JsError(errors) => {
                println(s"Errors: $errors, ${res.body}")
                assert(false)
              }

              case JsSuccess(prospects, _) =>
                assert(
                  prospects.map(_.id.get).contains(newProspectToBeAssigned) &&
                    total_assigned == 1 &&
                    campaign_uuid == campaign_uuid
                )
            }

          } else {
            assert(false)
          }
        }).recover(err => {
          println(s"Error: ${LogHelpers.getStackTraceAsString(err)}")
          assert(false)
        })

      }

      it("should give result in expected format with uuid for assign api for new prospect") {

        val teamId: Long = initialData.head_team_id

        var campaign_uuid = ""

        val apiKey = initialData.teamUserLevelKey
        var prospect_uuids: Seq[ProspectUuid] = Seq()


        val final_result = for {
          createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
            initialData = initialData,
            generateProspectCountIfNoGivenProspect = 2
          )


          campaign_uuid <- Future {
            campaign_uuid = createAndStartCampaignData.campaign.uuid.get

            campaign_uuid
          }

          assignedProspects <- Future {
            prospect_uuids = createAndStartCampaignData.addProspect.map(_.prospect_uuid.get)
            prospect_uuids
          }


          sendingUrl <- Future {
            s"/api/v3/campaigns/${campaign_uuid}/prospects"
          }

          res <- {

            val request = FakeRequest(play.api.test.Helpers.POST, sendingUrl)
              .withHeaders(
                "X-API-KEY" -> apiKey,
                "Content-Type" -> "application/json")
              .withJsonBody(Json.obj(
                "prospect_ids" -> prospect_uuids,
                "ignore_prospects_in_other_campaigns" -> ignore_prospects_in_other_campaigns
              ))

            play.api.test.Helpers.route(testApi, request).get
          }

        } yield {
          res
        }
        val status: Int = play.api.test.Helpers.status(final_result)
        val json: JsValue = play.api.test.Helpers.contentAsJson(final_result)
        final_result.map(res => {

          if (status == 200) {

            val sentResponseData = json

            val prospect_data = (sentResponseData \ "prospect_data")
            val total_assigned = (sentResponseData \ "total_assigned").as[Int]
            val returned_campaign_uuid = (sentResponseData \ "campaign_id").as[String]

            prospect_data.validate[List[ProspectObjectForApiIntegrationTest]] match {
              case JsError(errors) => {
                println(s"Errors: $errors, ${res.body}")
                assert(false)
              }

              case JsSuccess(prospects, _) =>
                assert(
                  prospects.map(_.id.get) == prospect_uuids.map(_.uuid) &&
                    total_assigned == 2 &&
                    campaign_uuid == campaign_uuid
                )
            }

          } else {
            println(s"res::: ${res.body}")
            assert(false)
          }
        }).recover(err => {
          println(s"Error: ${LogHelpers.getStackTraceAsString(err)}")
          assert(false)
        })

      }


      it("should give error in proper format") {

        val teamId: Long = initialData.head_team_id

        var campaign_uuid = ""

        val apiKey = initialData.teamUserLevelKey
        var prospect_uuids: Seq[ProspectUuid] = Seq()


        val final_result = for {
          createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
            initialData = initialData,
            generateProspectCountIfNoGivenProspect = 2
          )


          campaign_uuid <- Future {
            campaign_uuid = createAndStartCampaignData.campaign.uuid.get

            campaign_uuid
          }

          assignedProspects <- Future {
            prospect_uuids = createAndStartCampaignData.addProspect.map(_.prospect_uuid.get)
            prospect_uuids
          }


          sendingUrl <- Future {
            s"/api/v3/campaigns/${campaign_uuid}/prospects"
          }

          res <- {
            val request = FakeRequest(play.api.test.Helpers.POST, sendingUrl)
              .withHeaders(
                "X-API-KEY" -> apiKey,
                "Content-Type" -> "application/json")
              .withJsonBody(Json.obj(
                "prospect_ids" -> prospect_uuids.appended(ProspectUuid("prs_abcd")),
                "ignore_prospects_in_other_campaigns" -> ignore_prospects_in_other_campaigns
              ))

            play.api.test.Helpers.route(testApi, request).get
          }

        } yield {
          res
        }
        val status: Int = play.api.test.Helpers.status(final_result)
        val json: JsValue = play.api.test.Helpers.contentAsJson(final_result)
        final_result.map(res => {

          if (status == 400) {

          val sentResponseData = (json \ "errors")

          sentResponseData.validate[List[ErrorResponseProspectsAssignApi]] match {
            case JsError(_) =>
              assert(false)

            case JsSuccess(errors_list, _) =>
              assert(errors_list.exists(e => {
                e.error_type.toString == ProspectAssignErrorType.BadRequest.toString
              }))
          }

        } else {
          assert(false)
        }
        }).recover(err => {
          println(s"Error: ${LogHelpers.getStackTraceAsString(err)}")
          assert(false)
        })
      }


    }

  }

  describe("Start/Stop campaign api Integration Tests.") {

    describe("testing public api start/stop campaign response validation") {

      it("should give result in expected format with uuid for v1/v2 api") {

        val teamId: Long = initialData.head_team_id

        var campaign_uuid = ""
        var campaign_id: Long = 0L

        val apiKey = initialData.teamUserLevelKey
        var prospect_uuids: Seq[ProspectUuid] = Seq()


        val final_result = for {
          createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
            initialData = initialData,
            generateProspectCountIfNoGivenProspect = 2
          )


          campaign_uuid <- Future {
            campaign_uuid = createAndStartCampaignData.campaign.uuid.get

            campaign_uuid
          }

          campaign_id <- Future {
            campaign_id = createAndStartCampaignData.campaign.id

            campaign_id
          }

          assignedProspects <- Future {
            prospect_uuids = createAndStartCampaignData.addProspect.map(_.prospect_uuid.get)
            prospect_uuids
          }


          getCampaignUrl <- Future {
            s"/api/v2/campaigns/${campaign_id}"
          }

          res <- {

            val request = FakeRequest(play.api.test.Helpers.GET, getCampaignUrl)
              .withHeaders(
                "X-API-KEY" -> apiKey,
                "Content-Type" -> "application/json")

            play.api.test.Helpers.route(testApi, request).get
          }

        } yield {
          res
        }
        val status: Int = play.api.test.Helpers.status(final_result)
        val json: JsValue = play.api.test.Helpers.contentAsJson(final_result)
        val res = final_result.map(res => {
          val sentResponseData = (json \ "data" \ "campaign")
            sentResponseData.validate[FindCampaignResultPublicApi] match {

              case JsError(errors) =>
                println(s"errr: ${errors}")
                Future.failed(new Throwable("get campaign by id api validation failed"))

              case JsSuccess(currentCampaign, _) =>

                val request = FakeRequest(play.api.test.Helpers.PUT, s"/api/v2/campaigns/${campaign_id}/status")
                  .withHeaders(
                    "X-API-KEY" -> apiKey,
                    "Content-Type" -> "application/json")
                  .withJsonBody(Json.obj(
                    "status" -> (if (currentCampaign.status == CampaignStatus.RUNNING) "stopped" else "running")
                  ))

                val final_result2 = play.api.test.Helpers.route(testApi, request).get
                val status2: Int = play.api.test.Helpers.status(final_result2)
                val json2: JsValue = play.api.test.Helpers.contentAsJson(final_result2)
                final_result2
                  .flatMap(res => {

                    if (status2 == 200) {

                      val sentResponseData = (json2 \ "data" \ "campaign")

                      sentResponseData.validate[FindCampaignResultPublicApi] match {
                        case JsError(errors) =>
                          assert(false)
                          Future.successful("validation failed")

                        case JsSuccess(campaign, _) =>
                          assert(campaign.id == campaign_id)
                          Future.successful("campaign api response validated for v2")
                      }
                    } else {
                      Future.failed(new Throwable(s"res status -> ${status2}"))
                    }
                  })
            }
        }).recover(err => {
          println(s"Error: ${LogHelpers.getStackTraceAsString(err)}")
          Future.failed(new Throwable(s"Error"))
        })

        res.map(rr => {
          assert(true)
        }).recover(err => {
          println(err)
          assert(false)
        })


      }

      it("should give result in expected format with uuid for v3 api") {

        val teamId: Long = initialData.head_team_id

        var campaign_uuid = ""
        var campaign_id: Long = 0L

        val apiKey = initialData.teamUserLevelKey
        var prospect_uuids: Seq[ProspectUuid] = Seq()


        val final_result = for {
          createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
            initialData = initialData,
            generateProspectCountIfNoGivenProspect = 2
          )


          campaign_uuid <- Future {
            campaign_uuid = createAndStartCampaignData.campaign.uuid.get

            campaign_uuid
          }

          campaign_id <- Future {
            campaign_id = createAndStartCampaignData.campaign.id

            campaign_id
          }

          assignedProspects <- Future {
            prospect_uuids = createAndStartCampaignData.addProspect.map(_.prospect_uuid.get)
            prospect_uuids
          }


          getCampaignUrl <- Future {
            s"/api/v2/campaigns/${campaign_id}"
          }

          res <- {

            val request = FakeRequest(play.api.test.Helpers.GET, getCampaignUrl)
              .withHeaders(
                "X-API-KEY" -> apiKey,
                "Content-Type" -> "application/json")


            play.api.test.Helpers.route(testApi, request).get

          }
        } yield {
          res
        }
        val status: Int = play.api.test.Helpers.status(final_result)
        val json: JsValue = play.api.test.Helpers.contentAsJson(final_result)
        val res = final_result.map(res => {
          val sentResponseData = (json \ "data" \ "campaign")
          sentResponseData.validate[FindCampaignResultPublicApi] match {

            case JsError(errors) =>
              println(s"errr: ${errors}")
              Future.failed(new Throwable("get campaign by id api validation failed"))

            case JsSuccess(currentCampaign, _) =>

              val request = FakeRequest(play.api.test.Helpers.PUT, s"/api/v3/campaigns/${campaign_uuid}/status")
                .withHeaders(
                  "X-API-KEY" -> apiKey,
                  "Content-Type" -> "application/json")
                .withJsonBody(Json.obj(
                  "status" -> (if (currentCampaign.status == CampaignStatus.RUNNING) "stopped" else "running")
                ))


              val final_result2 = play.api.test.Helpers.route(testApi, request).get
              val status2: Int = play.api.test.Helpers.status(final_result2)
              val json2: JsValue = play.api.test.Helpers.contentAsJson(final_result2)
                final_result2
                .flatMap(res => {

                  if (status2 == 200) {

                    val sentResponseData = json2

                    sentResponseData.validate[FindCampaignResultPublicApiV3] match {
                      case JsError(errors) =>
                        assert(false)
                        Future.successful("validation failed")

                      case JsSuccess(campaign, _) =>
                        assert(campaign.uuid.get == campaign_uuid)
                        Future.successful("campaign api response validated for v3")
                    }
                  } else {
                    Future.failed(new Throwable(s"res status -> ${status2}"))
                  }
                })
          }
        }).recover(err => {
          println(s"Error: ${LogHelpers.getStackTraceAsString(err)}")
          Future.failed(new Throwable(s"Error"))
        })

        res.map(rr => {
          assert(true)
        }).recover(err => {
          println(err)
          assert(false)
        })

      }
    }

    describe("testing public api start/stop campaign for validation checks") {

      it("should fail if status param is not passed") {

        val teamId: Long = initialData.head_team_id

        var campaign_uuid = ""

        val apiKey = initialData.teamUserLevelKey
        var prospect_uuids: Seq[ProspectUuid] = Seq()


        val final_result = for {
          createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
            initialData = initialData,
            generateProspectCountIfNoGivenProspect = 2
          )


          campaign_uuid <- Future {
            campaign_uuid = createAndStartCampaignData.campaign.uuid.get

            campaign_uuid
          }

          assignedProspects <- Future {
            prospect_uuids = createAndStartCampaignData.addProspect.map(_.prospect_uuid.get)
            prospect_uuids
          }


          sendingUrl <- Future {
            s"/api/v3/campaigns/${campaign_uuid}/status"
          }

          res <- {
            val request = FakeRequest(play.api.test.Helpers.PUT, sendingUrl)
              .withHeaders(
                "X-API-KEY" -> apiKey,
                "Content-Type" -> "application/json")
              .withJsonBody(Json.obj())


            play.api.test.Helpers.route(testApi, request).get

          }

        } yield {
          res
        }
        val status: Int = play.api.test.Helpers.status(final_result)
        final_result.map(res => {

          if (status == 200) {

            println(s"res::: ${res.body}")
            assert(false)

          } else {
            //fixme add error format check
            assert(true)
          }
        }).recover(err => {
          println(s"Error: ${LogHelpers.getStackTraceAsString(err)}")
          assert(false)
        })


      }

      it("should fail if we do not provide schedule_start_at param for status -> scheduled") {

        val teamId: Long = initialData.head_team_id

        var campaign_uuid = ""

        val apiKey = initialData.teamUserLevelKey
        var prospect_uuids: Seq[ProspectUuid] = Seq()


        val final_result = for {
          createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
            initialData = initialData,
            generateProspectCountIfNoGivenProspect = 2
          )


          campaign_uuid <- Future {
            campaign_uuid = createAndStartCampaignData.campaign.uuid.get

            campaign_uuid
          }

          assignedProspects <- Future {
            prospect_uuids = createAndStartCampaignData.addProspect.map(_.prospect_uuid.get)
            prospect_uuids
          }


          sendingUrl <- Future {
            s"/api/v3/campaigns/${campaign_uuid}/status"
          }

          res <- {

            val request = FakeRequest(play.api.test.Helpers.PUT, sendingUrl)
              .withHeaders(
                "X-API-KEY" -> apiKey,
                "Content-Type" -> "application/json")
              .withJsonBody(Json.obj(
                "status" -> "scheduled"
              ))


            play.api.test.Helpers.route(testApi, request).get

          }

        } yield {
          res
        }
        val status: Int = play.api.test.Helpers.status(final_result)
        val json: JsValue = play.api.test.Helpers.contentAsJson(final_result)
        final_result.map(res => {

          if (status == 200) {

            println(s"res::: ${res.body}")
            assert(false)

          } else {
            //fixme add error format check
            assert(true)
          }
        }).recover(err => {
          println(s"Error: ${LogHelpers.getStackTraceAsString(err)}")
          assert(false)
        })

      }

      it("should fail if we try to schedule it in past time") {

        val currentEpochMillis: Long = DateTime.now().minusHours(1).getMillis

        val teamId: Long = initialData.head_team_id

        var campaign_uuid = ""

        val apiKey = initialData.teamUserLevelKey
        var prospect_uuids: Seq[ProspectUuid] = Seq()


        val final_result = for {
          createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
            initialData = initialData,
            generateProspectCountIfNoGivenProspect = 2
          )


          campaign_uuid <- Future {
            campaign_uuid = createAndStartCampaignData.campaign.uuid.get

            campaign_uuid
          }

          assignedProspects <- Future {
            prospect_uuids = createAndStartCampaignData.addProspect.map(_.prospect_uuid.get)
            prospect_uuids
          }


          sendingUrl <- Future {
            s"/api/v3/campaigns/${campaign_uuid}/status"
          }

          res <- {
            val request = FakeRequest(play.api.test.Helpers.PUT, sendingUrl)
              .withHeaders(
                "X-API-KEY" -> apiKey,
                "Content-Type" -> "application/json")
              .withJsonBody(Json.obj(
                "status" -> "scheduled",
                "schedule_start_at" -> currentEpochMillis
              ))


            play.api.test.Helpers.route(testApi, request).get
          }

        } yield {
          res
        }
        val status: Int = play.api.test.Helpers.status(final_result)
        val json: JsValue = play.api.test.Helpers.contentAsJson(final_result)
        final_result.map(res => {

          if (status == 200) {

            println(s"res::: ${res.body}")
            assert(false)

          } else {
            //fixme add error format check
            assert(true)
          }
        }).recover(err => {
          println(s"Error: ${LogHelpers.getStackTraceAsString(err)}")
          assert(false)
        })


      }

      it("should fail if we give incorrect time_zone") {

        val currentEpochMillis: Long = Instant.now().toEpochMilli + (10 * 60 * 1000)

        val teamId: Long = initialData.head_team_id

        val time_zone: String = "abc"
        var campaign_uuid = ""

        val apiKey = initialData.teamUserLevelKey
        var prospect_uuids: Seq[ProspectUuid] = Seq()


        val final_result = for {
          createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
            initialData = initialData,
            generateProspectCountIfNoGivenProspect = 2
          )


          campaign_uuid <- Future {
            campaign_uuid = createAndStartCampaignData.campaign.uuid.get
            campaign_uuid
          }

          assignedProspects <- Future {
            prospect_uuids = createAndStartCampaignData.addProspect.map(_.prospect_uuid.get)
            prospect_uuids
          }


          sendingUrl <- Future {
            s"/api/v3/campaigns/${campaign_uuid}/status"
          }

          res <- {

            val request = FakeRequest(play.api.test.Helpers.PUT, sendingUrl)
              .withHeaders(
                "X-API-KEY" -> apiKey,
                "Content-Type" -> "application/json")
              .withJsonBody(Json.obj(
                "status" -> "scheduled",
                "schedule_start_at" -> currentEpochMillis,
                "time_zone" -> time_zone
              ))


            play.api.test.Helpers.route(testApi, request).get
          }

        } yield {
          res
        }
        val status: Int = play.api.test.Helpers.status(final_result)

        final_result.map(res => {
          if (status == 200) {
            println(s"res::: ${res.body}")
            assert(false)

          } else {
            //fixme add error format check
            assert(true)
          }
        }).recover(err => {
          println(s"Error: ${LogHelpers.getStackTraceAsString(err)}")
          assert(false)
        })


      }

      it("should succeed with correct parameters") {

        val currentEpochMillis: Long = DateTime.now().plusMinutes(10).getMillis
        val time_zone: String = "Asia/Kolkata"

        val teamId: Long = initialData.head_team_id
        var campaign_uuid = ""

        val apiKey = initialData.teamUserLevelKey
        var prospect_uuids: Seq[ProspectUuid] = Seq()


        val final_result = for {
          createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
            initialData = initialData,
            generateProspectCountIfNoGivenProspect = 2
          )


          campaign_uuid <- Future {
            campaign_uuid = createAndStartCampaignData.campaign.uuid.get
            campaign_uuid
          }

          assignedProspects <- Future {
            prospect_uuids = createAndStartCampaignData.addProspect.map(_.prospect_uuid.get)
            prospect_uuids
          }


          sendingUrl <- Future {
            s"/api/v3/campaigns/${campaign_uuid}/status"
          }

          res <- {

            val request = FakeRequest(play.api.test.Helpers.PUT, sendingUrl)
              .withHeaders(
                "X-API-KEY" -> apiKey,
                "Content-Type" -> "application/json")
              .withJsonBody(Json.obj(
                "status" -> "scheduled",
                "schedule_start_at" -> currentEpochMillis,
                "time_zone" -> time_zone
              ))


            play.api.test.Helpers.route(testApi, request).get
          }
        } yield {
          res
        }
        val status: Int = play.api.test.Helpers.status(final_result)

        final_result.map(res => {
          if (status == 200) {
            //fixme do validation on response
            assert(true)

          } else {
            assert(false)
          }
        }).recover(err => {
          println(s"Error: ${LogHelpers.getStackTraceAsString(err)}")
          assert(false)
        })

      }

      it("should fail for status change other than running, scheduled, stopped") {

        val currentEpochMillis: Long = Instant.now().toEpochMilli + (10 * 60 * 1000)
        val time_zone: String = "Asia/Kolkata"

        val teamId: Long = initialData.head_team_id
        var campaign_uuid = ""

        val apiKey = initialData.teamUserLevelKey
        var prospect_uuids: Seq[ProspectUuid] = Seq()


        val final_result = for {
          createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
            initialData = initialData,
            generateProspectCountIfNoGivenProspect = 2
          )


          campaign_uuid <- Future {
            campaign_uuid = createAndStartCampaignData.campaign.uuid.get
            campaign_uuid
          }

          assignedProspects <- Future {
            prospect_uuids = createAndStartCampaignData.addProspect.map(_.prospect_uuid.get)
            prospect_uuids
          }


          sendingUrl <- Future {
            s"/api/v3/campaigns/${campaign_uuid}/status"
          }

          res <- {

            val request = FakeRequest(play.api.test.Helpers.PUT, sendingUrl)
              .withHeaders(
                "X-API-KEY" -> apiKey,
                "Content-Type" -> "application/json")
              .withJsonBody(Json.obj(
                "status" -> "archived",
                "schedule_start_at" -> currentEpochMillis,
                "time_zone" -> time_zone
              ))


            play.api.test.Helpers.route(testApi, request).get
          }

        } yield {
          res
        }
        val status: Int = play.api.test.Helpers.status(final_result)

        final_result.map(res => {
          if (status == 200) {
            assert(false)

          } else {
            //fixme do validation on error
            assert(true)
          }
        }).recover(err => {
          println(s"Error: ${LogHelpers.getStackTraceAsString(err)}")
          assert(false)
        })


      }

    }

  }

  describe("Unassign prospects From Campaign api's Integration Tests") {

    val invalid_prospect_uuid = "prs_aa_2Y9yDepl3I0gAN5CWtJ75bzhVA"

    val BAD_REQUEST = "bad_request"
    val FORBIDDEN = "forbidden"
    describe("Integration test for unassign prospects 204 response"){
      it("test for assigning a prospect to the campaign to test the unassign flow") {

        val teamId: Long = initialData.head_team_id
        var campaign_uuid = ""

        val apiKey = initialData.teamUserLevelKey
        var prospect_uuids: Seq[ProspectUuid] = Seq()


        val final_result = for {
          createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
            initialData = initialData,
            generateProspectCountIfNoGivenProspect = 2
          )


          campaign_uuid <- Future {
            campaign_uuid = createAndStartCampaignData.campaign.uuid.get
            campaign_uuid
          }

          assignedProspects <- Future {
            prospect_uuids = createAndStartCampaignData.addProspect.map(_.prospect_uuid.get)
            prospect_uuids
          }


          sendingUrl <- Future {
            s"/api/v3/campaigns/${campaign_uuid}/prospects"
          }

          res <- {

            val request = FakeRequest(play.api.test.Helpers.POST, sendingUrl)
              .withHeaders(
                "X-API-KEY" -> apiKey,
                "Content-Type" -> "application/json")
              .withJsonBody(Json.obj(
                "prospect_ids" -> assignedProspects
              ))


            play.api.test.Helpers.route(testApi, request).get
          }

        } yield {
          res
        }
        val status: Int = play.api.test.Helpers.status(final_result)
        val json: JsValue = play.api.test.Helpers.contentAsJson(final_result)

        final_result.map(res => {

          if (status == 200) {
              val sentResponseData = json

              val prospect_data = (sentResponseData \ "prospect_data")
              val total_assigned = (sentResponseData \ "total_assigned").as[Int]
              val returned_campaign_uuid = (sentResponseData \ "campaign_id").as[String]

              prospect_data.validate[List[ProspectObjectForApiIntegrationTest]] match {
                case JsError(errors) => assert(false)


                case JsSuccess(prospect_data_list, _) =>
                  assert(
                    prospect_data_list.flatMap(_.id) == prospect_uuids.map(_.uuid) &&
                      total_assigned == 2 &&
                      campaign_uuid == returned_campaign_uuid
                  )
              }

            } else {
              assert(false)
            }
        }).recover(err => {
          println(s"Error: ${LogHelpers.getStackTraceAsString(err)}")
          assert(false)
        })

      }

      it("should succeed for correct input parameters and return 204 status"){

        val teamId: Long = initialData.head_team_id
        var campaign_uuid = ""

        val apiKey = initialData.teamUserLevelKey
        var prospect_uuids: Seq[ProspectUuid] = Seq()


        val final_result = for {
          createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
            initialData = initialData,
            generateProspectCountIfNoGivenProspect = 2
          )


          campaign_uuid <- Future {
            campaign_uuid = createAndStartCampaignData.campaign.uuid.get
            campaign_uuid
          }

          assignedProspects <- Future {
            prospect_uuids = createAndStartCampaignData.addProspect.map(_.prospect_uuid.get)
            prospect_uuids
          }


          sendingUrl <- Future {
            s"/api/v3/campaigns/${campaign_uuid}/prospects"
          }

          res <- {

            val request = FakeRequest(play.api.test.Helpers.PUT, sendingUrl)
              .withHeaders(
                "X-API-KEY" -> apiKey,
                "Content-Type" -> "application/json")
              .withJsonBody(Json.obj(
                "prospect_ids" -> assignedProspects
              ))


            play.api.test.Helpers.route(testApi, request).get
          }

        } yield {
          res
        }
        val status: Int = play.api.test.Helpers.status(final_result)
        final_result.map(res => {

          if (status == 204) {
            assert(true)
          } else {
            assert(false)
          }
        }).recover(err => {
          println(s"Error: ${LogHelpers.getStackTraceAsString(err)}")
          assert(false)
        })
      }
    }

    describe("Integration test for unassign prospects failure incorrect prospect ") {
      val errorResponseForInValidProspects = CampaignPublicApiErrorV3(
        message = "Invalid prospect ids passed",
        error_type = BAD_REQUEST,
        data = Option(invalid_prospect_uuid)
      )

      it("should fail for incorrect input parameters and return error") {

        val teamId: Long = initialData.head_team_id
        var campaign_uuid = ""

        val apiKey = initialData.teamUserLevelKey


        val final_result = for {
          createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
            initialData = initialData,
            generateProspectCountIfNoGivenProspect = 2
          )


          campaign_uuid <- Future {
            campaign_uuid = createAndStartCampaignData.campaign.uuid.get
            campaign_uuid
          }


          sendingUrl <- Future {
            s"/api/v3/campaigns/${campaign_uuid}/prospects"
          }

          res <- {
            val request = FakeRequest(play.api.test.Helpers.PUT, sendingUrl)
              .withHeaders(
                "X-API-KEY" -> apiKey,
                "Content-Type" -> "application/json")
              .withJsonBody(Json.obj(
                "prospect_ids" -> List(invalid_prospect_uuid) //invalid prospect
              ))


            play.api.test.Helpers.route(testApi, request).get
          }

        } yield {
          res
        }
        val status: Int = play.api.test.Helpers.status(final_result)
        val json: JsValue = play.api.test.Helpers.contentAsJson(final_result)
        final_result.map(res => {

          if (status == 400) {
            val sentResponseData = (json \ "errors")
            sentResponseData.validate[List[CampaignPublicApiErrorV3]]match {
              case JsError(errors) => {
                assert(false)
              }

              case JsSuccess(errors, _) =>
                assert(errors.head == errorResponseForInValidProspects)
            }
          } else {
            assert(false)
          }
        }).recover(err => {
          println(s"Error: ${LogHelpers.getStackTraceAsString(err)}")
          assert(false)
        })
      }

      it("should fail for incorrect input parameters and return error prospect not in campaign") {

        val teamId: Long = initialData.head_team_id
        var campaign_uuid = ""

        val apiKey = initialData.teamUserLevelKey
        var prospect_uuid_different_campaign = ""


        val final_result = for {
          createAndStartCampaignData1: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
            initialData = initialData
          )
          createAndStartCampaignData2: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
            initialData = initialData
          )


          campaign_uuid <- Future {
            campaign_uuid = createAndStartCampaignData1.campaign.uuid.get
            campaign_uuid
          }


          sendingUrl <- Future {
            s"/api/v3/campaigns/${campaign_uuid}/prospects"
          }

          res <- {
            prospect_uuid_different_campaign = createAndStartCampaignData2.addProspect.head.prospect_uuid.get.uuid

            val request = FakeRequest(play.api.test.Helpers.PUT, sendingUrl)
              .withHeaders(
                "X-API-KEY" -> apiKey,
                "Content-Type" -> "application/json")
              .withJsonBody(Json.obj(
                "prospect_ids" -> List(prospect_uuid_different_campaign)
              ))


            play.api.test.Helpers.route(testApi, request).get
          }

        } yield {
          res
        }
        val status: Int = play.api.test.Helpers.status(final_result)
        val json: JsValue = play.api.test.Helpers.contentAsJson(final_result)
        final_result.map(res => {

          if (status == 400) {
            val sentResponseData = (json \ "errors")
            sentResponseData.validate[List[CampaignPublicApiErrorV3]]match {
              case JsError(errors) => {
                assert(false)
              }

              case JsSuccess(errors, _) =>
                val errorResponseForProspectNotInCampaign = CampaignPublicApiErrorV3(
                  message = "Given prospects do not exist in the given campaign",
                  error_type = FORBIDDEN,
                  data = Option(prospect_uuid_different_campaign)
                )
                assert(errors.head == errorResponseForProspectNotInCampaign)
            }
          } else {
            assert(false)
          }
        }).recover(err => {
          println(s"Error: ${LogHelpers.getStackTraceAsString(err)}")
          assert(false)
        })
      }
    }

  }

  describe("Campaign Search api Integration test"){

    it("v2 api should return success"){

      val teamId: Long = initialData.head_team_id
      var campaign_uuid = ""

      val apiKey = initialData.teamUserLevelKey

      val final_result = for {
        createAndStartCampaignData1: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
          initialData = initialData
        )

        campaign_uuid <- Future {
          campaign_uuid = createAndStartCampaignData1.campaign.uuid.get
          campaign_uuid
        }


        sendingUrl <- Future {
          s"/api/v2/search/campaigns?tid=${teamId}"
        }

        res <- {
          val request = FakeRequest(play.api.test.Helpers.POST, sendingUrl)
            .withHeaders(
              "X-API-KEY" -> apiKey,
              "Content-Type" -> "application/json")
            .withJsonBody(Json.obj("page" -> 1,
              "query" -> Json.obj(
                "owner_ids" -> Json.arr(0),
                "clause" -> "AND",
                "filters" -> Json.arr())
            ))


          play.api.test.Helpers.route(testApi, request).get
        }

      } yield {
        res
      }
      val status: Int = play.api.test.Helpers.status(final_result)
      val json: JsValue = play.api.test.Helpers.contentAsJson(final_result)
      final_result.map(res => {

          if (status == 200) {

            val sentResponseData = (json \ "data" \ "campaigns")

            sentResponseData.validate[List[CampaignBasicInfo]] match {
              case JsError(errors) => {
                println(s"errors: ${errors}")
                assert(false)
              }

              case JsSuccess(data, _) =>
                assert(data.map(_.uuid.get).contains(campaign_uuid))
            }

          } else {
            assert(false)
          }
      }).recover(err => {
        println(s"Error: ${LogHelpers.getStackTraceAsString(err)}")
        assert(false)
      })

    }

  }

  describe("Get Campaign stats by id api's Integration Tests") {

    describe("Integration test for success Response"){
      it("should return the required response for the campaign id passed"){

        val teamId: Long = initialData.head_team_id
        var campaign_uuid = ""

        val apiKey = initialData.teamUserLevelKey

        val final_result = for {
          createAndStartCampaignData1: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
            initialData = initialData
          )

          campaign_uuid <- Future {
            campaign_uuid = createAndStartCampaignData1.campaign.uuid.get
            campaign_uuid
          }

          sendingUrl <- Future {
            s"/api/v3/campaigns/${campaign_uuid}/stats"
          }

          res <- {

            val request = FakeRequest(play.api.test.Helpers.GET, sendingUrl)
              .withHeaders(
                "X-API-KEY" -> apiKey,
                "Content-Type" -> "application/json")



            play.api.test.Helpers.route(testApi, request).get
          }
        } yield {
          res
        }
        val status: Int = play.api.test.Helpers.status(final_result)
        val json: JsValue = play.api.test.Helpers.contentAsJson(final_result)
        final_result.map(res => {

          if (status == 200) {
              val response = (json)
              response.validate[AllCampaignStatsApiResponse] match {
                case JsError(_) => assert(false)

                case JsSuccess(result, _) =>
                  assert(result.total_steps == 1 && result.current_prospects == 5)

              }
            } else {
              assert(false)
            }
        }).recover(err => {
          println(s"Error: ${LogHelpers.getStackTraceAsString(err)}")
          assert(false)
        })
      }
    }
    describe("Integration test for error Response") {
      it("should return the required response for wrong input passed") {

        val teamId: Long = initialData.head_team_id

        val apiKey = initialData.teamUserLevelKey

        val final_result = for {

          sendingUrl <- Future {
            s"/api/v3/campaigns/cmp_abcd/stats"
          }

          res <- {
            val request = FakeRequest(play.api.test.Helpers.GET, sendingUrl)
              .withHeaders(
                "X-API-KEY" -> apiKey,
                "Content-Type" -> "application/json")


            play.api.test.Helpers.route(testApi, request).get
          }
        } yield {
          res
        }
        val status: Int = play.api.test.Helpers.status(final_result)
        val json: JsValue = play.api.test.Helpers.contentAsJson(final_result)
        final_result.map(res => {

          if (status == 400) {
              val response = (json \ "errors")
              response.validate[List[CampaignPublicApiErrorV3]] match {
                case JsError(errors) => {
                  assert(false)
                }

                case JsSuccess(result, _) =>
                  val headResultOpt: Option[CampaignPublicApiErrorV3] = result.headOption
                  assert(headResultOpt.isDefined && headResultOpt.get.error_type == ErrorType.BAD_REQUEST.toString)
              }
            } else {

              assert(false)
            }
        }).recover(err => {
          println(s"Error: ${LogHelpers.getStackTraceAsString(err)}")
          assert(false)
        })
      }
    }
  }

  describe("Add Prospect flow : permission check"){

    val prospectCreateFormdata = ProspectCreateFormDataV2(
      email = Some(""),
      first_name = Some("first_name"),
      last_name = Some("last_name"),
      custom_fields = Some(Json.obj("followers" -> 500)),

      // should not break in old ui/integrations, currently used only inside createOrUpdateOne controller
      owner_id = None,

      list = Some("list"),
      company = Some("Some company"),
      city = Some("kolkata"),
      country = Some("India"),
      timezone = Some("Asia/Kolkata"),
      created_at = None,

      state = None,
      job_title = None,
      phone_number = None,
      linkedin_url = None,
      tags = None
    )

    it ("should fail when other org member try to create prospects in other's campaign"){

      val prospect_email: String = Helpers.generateRandomString(8) + "@gmail.com"

      val prospects_to_be_added = Json.toJson(Seq(
        prospectCreateFormdata.copy(
          email = Some(prospect_email),
          custom_fields = Some(Json.obj()),
          list = None,
          owner_id = None
        )
      ))

      val anotherUser: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

      val campaignCreateUrl = s"/api/v2/campaigns?tid=${initialData.head_team_id}"

      for {
        // creating a campaign to store prospects
        campaign_uuid <- {

          val request = FakeRequest(play.api.test.Helpers.POST, campaignCreateUrl)
            .withHeaders(
              "X-API-KEY" -> initialData.teamUserLevelKey,
          "Content-Type" -> "application/json")
            .withJsonBody(Json.obj(
              "campaign_type" -> "email"
            ))


          val final_result = play.api.test.Helpers.route(testApi, request).get


          val status: Int = play.api.test.Helpers.status(final_result)
          val json: JsValue = play.api.test.Helpers.contentAsJson(final_result)
          final_result
            .flatMap(res => {
              //                                                                                    println(s"status is ${res.status} with response ${res.body}")
              if (status == 200) {
                val campaignUuid = (json \ "data" \ "campaign" \ "id")
                campaignUuid.validate[Int] match {
                  case JsError(errors) =>
                    assert(false)
                    Future.failed(new Exception("validation failed"))

                  case JsSuccess(value, path) =>
                    assert(true)
                    Future.successful(value)
                }
              }
              else {
                assert(false)
                Future.failed(new Exception("Unable to create the campaign"))
              }
            })
        }

        // adding prospects to the newly created campaign
        _ <- {

          val request = FakeRequest(play.api.test.Helpers.POST, s"/api/v3/prospects?tid=${initialData.account.teams.head.team_id}&campaign_id=$campaign_uuid&source=make.com")
            .withHeaders(
              "X-API-KEY" -> anotherUser.teamUserLevelKey,
              "Content-Type" -> "application/json")
            .withJsonBody(prospects_to_be_added)


          val final_result = play.api.test.Helpers.route(testApi, request).get


          val status: Int = play.api.test.Helpers.status(final_result)
          val json: JsValue = play.api.test.Helpers.contentAsJson(final_result)
          final_result
            .flatMap(res => {
              //                                                                                                println(s"status is ${res.status} with response ${res.body}")
              if (status == 200) {
                assert(false)
              }
              else {
                // response will be look like this : {"error_type":"not_found","message":"Campaign not found"}
                val message = (json \ "message")
                message.validate[String] match {
                  case JsError(errors) =>
                    assert(false)

                  case JsSuccess(value, path) =>
                    assert(value.contains("Campaign not found"))
                    val error_type = (json \ "error_type")
                    error_type.validate[String] match {
                      case JsError(errors) =>
                        assert(false)

                      case JsSuccess(value, path) =>
                        assert(value.contains("not_found"))
                        assert(status == 404)
                        Future.successful(value)
                    }

                    Future.successful(value)
                }


              }
            })
        }
      } yield {
        assert(true)
      }
    }

    it("should fail when non existing campaign was provided to prospect API") {

      val prospect_email: String = Helpers.generateRandomString(8) + "@gmail.com"

      val prospects_to_be_added = Json.toJson(Seq(
        prospectCreateFormdata.copy(
          email = Some(prospect_email),
          custom_fields = Some(Json.obj()),
          list = None,
          owner_id = None
        )
      ))


      for {

        // adding prospects to the newly created campaign
        _ <- {

          val request = FakeRequest(play.api.test.Helpers.POST, s"/api/v3/prospects?tid=${initialData.account.teams.head.team_id}&campaign_id=fefgg_wssjn&source=make.com")
            .withHeaders(
              "X-API-KEY" -> initialData.teamUserLevelKey,
              "Content-Type" -> "application/json")
            .withJsonBody(prospects_to_be_added)


          val final_result = play.api.test.Helpers.route(testApi, request).get


          val status: Int = play.api.test.Helpers.status(final_result)
          val json: JsValue = play.api.test.Helpers.contentAsJson(final_result)
          final_result
            .flatMap(res => {
              //                                                                                                println(s"status is ${res.status} with response ${res.body}")
              if (status == 200) {
                assert(false)
              }
              else {
                val message = (json \ "message")
                message.validate[String] match {
                  case JsError(errors) =>
                    assert(false)
                    val error_type = (json \ "error_type")
                    error_type.validate[String] match {
                      case JsError(errors) =>
                        assert(false)

                      case JsSuccess(value, path) =>
                        assert(value.contains("not_found"))
                        assert(status == 404)
                        Future.successful(value)
                    }

                  case JsSuccess(value, path) =>
                    assert(value.contains("Campaign id not found"))
                    Future.successful(value)
                }
              }
            })
        }

      } yield {
        assert(true)
      }
    }
  }



  describe("Prospect Campaign Adding public flow API test") {

    val prospectCreateFormdata = ProspectCreateFormDataV2(
      email = Some(""),
      first_name = Some("first_name"),
      last_name = Some("last_name"),
      custom_fields = Some(Json.obj("followers" -> 500)),

      // should not break in old ui/integrations, currently used only inside createOrUpdateOne controller
      owner_id = None,

      list = Some("list"),
      company = Some("Some company"),
      city = Some("kolkata"),
      country = Some("India"),
      timezone = Some("Asia/Kolkata"),
      created_at = None,

      state = None,
      job_title = None,
      phone_number = None,
      linkedin_url = None,
      tags = None
    )

    it("Checking the prospects added to the Campaign") {

      val campaignCreateUrl = s"/api/v2/campaigns?tid=${initialData.account.teams.head.team_id}"

      //each time we run this test we are inserting new prospect so it should be unique
      val prospect_email: String = Helpers.generateRandomString(8) + "@gmail.com"

      val prospects_to_be_added = Json.toJson(Seq(
        prospectCreateFormdata.copy(
          email = Some(prospect_email),
          custom_fields = Some(Json.obj()),
          list = None,
          owner_id = None
        )
      ))

      for {
        // creating a campaign to store prospects
        campaign_uuid:String <- {

          val request = FakeRequest(play.api.test.Helpers.POST, campaignCreateUrl)
            .withHeaders(
              "X-API-KEY" -> initialData.teamUserLevelKey,
              "Content-Type" -> "application/json")
            .withJsonBody(Json.obj(
              "campaign_type" -> "email"
            ))
          val final_result = play.api.test.Helpers.route(testApi, request).get

          val status: Int = play.api.test.Helpers.status(final_result)
          val json: JsValue = play.api.test.Helpers.contentAsJson(final_result)
          final_result
            .flatMap(res => {
              //                                                                        println(s"status is ${res.status} with response ${res.body}")
              if (status == 200) {
                val campaignUuid = (json \ "data" \ "campaign" \ "uuid")
                campaignUuid.validate[String] match {
                  case JsError(errors) =>
                    assert(false)
                    Future.failed(new Exception("validation failed"))

                  case JsSuccess(value, path) =>
                    assert(true)
                    Future.successful(value)
                }
              }
              else {
                assert(false)
                Future.failed(new Exception("Unable to create the campaign"))
              }
            })
        }

        // adding prospects to the newly created campaign
        _ <- {

          val request = FakeRequest(play.api.test.Helpers.POST, s"/api/v3/prospects?tid=${initialData.account.teams.head.team_id}&campaign_id=$campaign_uuid&source=make.com")
            .withHeaders(
              "X-API-KEY" -> initialData.teamUserLevelKey,
              "Content-Type" -> "application/json")
            .withJsonBody(prospects_to_be_added)


          val final_result = play.api.test.Helpers.route(testApi, request).get


          val status: Int = play.api.test.Helpers.status(final_result)
          final_result
            .flatMap(res => {
              //                                                                                    println(s"status is ${res.status} with response ${res.body}")
              if (status == 200) {
                assert(true)
                Future.successful(true)
              }
              else {
                assert(false)
                Future.failed(new Exception("Unable to push prospect to campaign"))
              }
            })
        }

        // checking whether the newly created prospect present in campaign.
        _ <- {

          val request = FakeRequest(play.api.test.Helpers.GET, s"/api/v3/campaigns/$campaign_uuid/prospects")
            .withHeaders(
              "X-API-KEY" -> initialData.teamUserLevelKey,
              "Content-Type" -> "application/json")


          val final_result = play.api.test.Helpers.route(testApi, request).get


          val status: Int = play.api.test.Helpers.status(final_result)
          val json: JsValue = play.api.test.Helpers.contentAsJson(final_result)
          final_result
            .flatMap(res => {
              //                                                                                                println(s"status is ${res.status} with response ${res.body}")
              if (status == 200) {
                val email = json \ "campaign_prospects" \ 0 \ "prospect" \ "emails" \ 0 \ "email"

                email.validate[String] match {
                  case JsError(errors) => {
                    assert(false)
                    Future.failed(new Throwable("validation failed"))
                  }

                  case JsSuccess(data, _) =>
                    assert(data == prospect_email)
                    Future.successful("Validation success")
                }
              }
              else {
                assert(false)
                Future.failed(new Exception("Unable to get prospects from campaign"))
              }
            })
        }

      } yield {
        assert(true)
      }

    }
  }

  describe("integration tests for checking deletion of the only step in campaign") {

    it("should not delete the only step in campaign for non-drip type campaigns") {

      val initialData: InitialData = NewAccountAndWhatsappSettingData.createNewAccountAndWhatsappSettingData(emailNotCompulsoryOrgFlag = Some(true)).get
      val account: Account = initialData.account
      val teamId: TeamId = TeamId(account.teams.head.team_id)
      val orgId = initialData.account.org.id
      val taId = account.teams.head.access_members.head.ta_id
      val accountId: Long = account.internal_id

      val final_res: Future[Option[Long]] = for {
        // create a campaign
        campaignWithStatsAndEmail: CampaignWithStatsAndEmail <- campaignService.createCampaign(
            orgId = orgId,
            accountId = accountId,
            teamId = teamId.id,
            taId = taId,
            data = CampaignCreateForm(
              name = None,
              timezone = Some("Asia/Kolkata"),
              campaign_owner_id = Some(accountId),
              campaign_type = CampaignType.MultiChannel,
            ),
            campaignSettings = None,
            permittedAccountIdsForEditCampaigns = Seq(accountId),
            ownerFirstName = "Parth"
          )
          .flatMap {
            case Left(err) =>
              Future.failed(new Exception(s"Campaign Creation Failed :: $err"))

            case Right(value) =>
              Future.successful(value)
          }
        emailVariant0 <- campaignStepService.createVariant(
            orgId = orgId,
            data = CampaignStepVariantCreateOrUpdate(
              parent_id = 0L,
              step_data = CampaignStepData.AutoEmailStep(
                subject = "Subject",
                body = "body"
              ),
              step_delay = 0,
              notes = None,
              priority = None,
            ),
            teamId = teamId.id,
            userId = accountId,
            taId = taId,
            stepId = 0,
            campaignId = campaignWithStatsAndEmail.id,
            campaignHeadStepId = None
          )
          .flatMap {
            case Left(err) =>
              Future.failed(new Exception(s"Error while creating variant :: $err"))

            case Right(value) =>
              Future.successful(value)
          }

        deleteVariant <- Future.fromTry(campaignService.deleteVariant(
          stepId = emailVariant0.step_id,
          campaign = Campaign(
            id = campaignWithStatsAndEmail.id,
            uuid = None,
            account_id = accountId,
            team_id = teamId.id,
            shared_with_team = false,
            name = campaignWithStatsAndEmail.name,
            status = campaignWithStatsAndEmail.status,
            head_step_id = campaignWithStatsAndEmail.head_step_id,
            settings = campaignWithStatsAndEmail.settings,
            last_scheduled_at = None,
            created_at = campaignWithStatsAndEmail.created_at
          ),
          loggedinAccount = account,
          campaignId = campaignWithStatsAndEmail.id,
          step = CampaignStep(
            id = emailVariant0.step_id,
            label = emailVariant0.label,
            campaign_id = emailVariant0.campaign_id,
            delay = 0,
            step_type = CampaignStepType.AutoEmailStep,
            created_at = DateTime.now()
          ),
          variantId = emailVariant0.id.toInt,
          teamId = teamId
        ))

      } yield {

        deleteVariant

      }

      final_res.map(res => {
        assert(false)
      }).recover(e => {
        assert(e.getMessage == API_MSGS.SINGLE_STEP_DELETION_IS_NOT_ALLOWED)
      })
    }

    it("should not delete the only step in a drip campaign if the campaign_status != not_started") {

      val initialData: InitialData = SRSetupAndDeleteFixtures.createInitialData(true).get
      val account: Account = initialData.account
      val teamId: TeamId = TeamId(account.teams.head.team_id)
      val orgId = initialData.account.org.id
      val emailSetting = initialData.emailSetting.get
      val taId: Long = account.teams.head.access_members.head.ta_id
      val emailSettingId: Long = emailSetting.id.get.emailSettingId
      val accountId: Long = account.internal_id

      val campaignSettings = CampaignSettings(
        // settings
        campaign_email_settings = List(
          CampaignEmailSettings(
            campaign_id = CampaignId(26),
            sender_email_setting_id = EmailSettingId(emailSettingId),
            receiver_email_setting_id = EmailSettingId(emailSettingId),
            team_id = teamId,
            uuid = CampaignEmailSettingsUuid("temp_setting_id"),
            id = CampaignEmailSettingsId(emailSettingId),
            sender_email = "<EMAIL>",
            receiver_email = "<EMAIL>",
            max_emails_per_day_from_email_account = 100,
            signature = None,
            error = None,
            from_name = None
          )
        ),
        campaign_linkedin_settings = List(),
        campaign_call_settings = List(),
        campaign_whatsapp_settings = List(),
        campaign_sms_settings = List(),
        timezone = "Asia/Kolkata",
        daily_from_time = 0, // time since beginning of day in seconds
        daily_till_time = 86400, // time since beginning of day in seconds
        sending_holiday_calendar_id = None,

        // Sunday is the first day
        days_preference = List(true, true, true, true, true, true, true),

        mark_completed_after_days = 1,
        max_emails_per_day = 1000,
        open_tracking_enabled = true,
        click_tracking_enabled = true,
        enable_email_validation = false,
        ab_testing_enabled = true,

        ai_sequence_status = None,

        // warm up
        warmup_started_at = None,
        warmup_length_in_days = None,
        warmup_starting_email_count = None,
        show_soft_start_setting = false,

        // schedule start
        schedule_start_at = Option(DateTime.now()),
        schedule_start_at_tz = Option("Asia/Kolkata"),

        send_plain_text_email = Some(false),
        campaign_type = CampaignType.MultiChannel,


        email_priority = CampaignEmailPriority.EQUAL,
        append_followups = true,
        opt_out_msg = "Pivot",
        opt_out_is_text = true,
        add_prospect_to_dnc_on_opt_out = true,
        triggers = Seq(),
        sending_mode = None,
        
        selected_calendar_data = None
      )

      val final_res: Future[(Option[Long], Long)] = for {
        // create a campaign
        campaignWithStatsAndEmail: CampaignWithStatsAndEmail <- campaignService.createCampaign(
            orgId = orgId,
            accountId = accountId,
            teamId = teamId.id,
            taId = taId,
            data = CampaignCreateForm(
              name = None,
              timezone = Some("Asia/Kolkata"),
              campaign_owner_id = Some(accountId),
              campaign_type = CampaignType.Drip,
            ),
            campaignSettings = Some(campaignSettings),
            permittedAccountIdsForEditCampaigns = Seq(accountId),
            ownerFirstName = "Parth"
          )
          .flatMap {
            case Left(err) =>
              Future.failed(new Exception(s"Campaign Creation Failed :: $err"))

            case Right(value) =>
              Future.successful(value)
          }
        emailVariant0 <- campaignStepService.createVariant(
            orgId = orgId,
            data = CampaignStepVariantCreateOrUpdate(
              parent_id = 0L,
              step_data = CampaignStepData.AutoEmailStep(
                subject = "Subject",
                body = "body"
              ),
              step_delay = 0,
              notes = None,
              priority = None,
            ),
            teamId = teamId.id,
            userId = accountId,
            taId = taId,
            stepId = 0,
            campaignId = campaignWithStatsAndEmail.id,
            campaignHeadStepId = None
          )
          .flatMap {
            case Left(err) =>
              Future.failed(new Exception(s"Error while creating variant :: $err"))

            case Right(value) =>
              Future.successful(value)
          }

        prospect = ProspectCreateFormData(
          email = Some("<EMAIL>"),
          first_name = Some("Bruce"),
          last_name = Some("Wayne"),
          custom_fields = Json.obj(),

          list = None,
          company = None,
          city = None,
          country = None,
          timezone = None,

          state = None,
          job_title = None,
          phone = Some("+************"),
          phone_2 = None,
          phone_3 = None,
          linkedin_url = Some("https://linkedin.com/in/aditya-sadana")
        )

        createdProspects: CreateOrUpdateProspectsResult <- Future.fromTry {
          prospectService.createOrUpdateProspects(
            ownerAccountId = accountId,
            teamId = teamId.id,
            listName = None,
            prospects = Seq(prospect),
            updateProspectType = UpdateProspectType.ForceUpdate,
            ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads = true,

            doerAccount = account,
            prospectSource = None,
            prospectAccountId = None,

            campaign_id = Some(campaignWithStatsAndEmail.id),
            prospect_tags = None,
            ignoreProspectInOtherCampaign = IgnoreProspectsInOtherCampaigns.DoNotIgnore,
            deduplicationColumns = None,
            auditRequestLogId = None,
            batchInsertLimit = 1000,

            SRLogger = Logger
          )
        }

        campaignAfterUpdatingEmailSetting: CampaignWithStatsAndEmail <- Future.fromTry {
          campaignService.updateEmailSettingsV2(
              id = CampaignId(campaignWithStatsAndEmail.id),
              teamId = teamId,
              data = List(CampaignEmailSettingsV2(
                sender_email_settings_id = emailSettingId,
                receiver_email_settings_id = emailSettingId
              )),
              campaign_status = CampaignStatus.NOT_STARTED,
                planID = TRIAL,
              orgId = OrgId(orgId)
            )
            .flatMap {
                case Left(err) =>
                    Failure(new Exception(s"Error while updating email settings :: $err"))
              case Right(None) => Failure(new Exception("updateEmailSettingsV2 failed"))
              case Right(Some(value)) => Success(value)
            }
        }

        campaign: Campaign <- campaignService.findCampaignForCampaignUtilsOnly(
          id = campaignAfterUpdatingEmailSetting.id,
          teamId = teamId
        ) match {
          case None => Future.failed(new Exception("Campaign not found"))
          case Some(c) => Future.successful(c)
        }

        startCampaign <- campaignStartService.startStopCampaign(
            status = CampaignStatus.RUNNING,
            schedule_start_at = None,
            time_zone = None,
            campaign = campaign,
            org = initialData.account.org,
            team = Some(initialData.account.teams.head),
            userId = AccountId(accountId),
            teamId = teamId,
            Logger = Logger
          )
          .flatMap {
            case Left(err) =>
              Future.failed(new Exception(s"Error while starting campaign :: $err"))

            case Right(value) =>
              Future.successful(value)
          }

        updatedCampaign: Campaign <- campaignService.findCampaignForCampaignUtilsOnly(
          id = campaignAfterUpdatingEmailSetting.id,
          teamId = teamId
        ) match {
          case None => Future.failed(new Exception("Campaign not found"))
          case Some(c) => Future.successful(c)
        }

        deleteVariant <- Future.fromTry(campaignService.deleteVariant(
          stepId = emailVariant0.step_id,
          campaign = updatedCampaign,
          loggedinAccount = account,
          campaignId = campaignWithStatsAndEmail.id,
          step = CampaignStep(
            id = emailVariant0.step_id,
            label = emailVariant0.label,
            campaign_id = emailVariant0.campaign_id,
            delay = 0,
            step_type = CampaignStepType.AutoEmailStep,
            created_at = DateTime.now()
          ),
          variantId = emailVariant0.id.toInt,
          teamId = teamId
        ))

      } yield {

        (deleteVariant, emailVariant0.id)

      }

      final_res.map(res => {
        assert(false)
      }).recover(e => {
        assert(e.getMessage == API_MSGS.SINGLE_STEP_DELETION_IS_NOT_ALLOWED)
      })
    }

    it("should delete the only step in a drip campaign if the campaign is not started") {

      val initialData: InitialData = NewAccountAndWhatsappSettingData.createNewAccountAndWhatsappSettingData(emailNotCompulsoryOrgFlag = Some(true)).get
      val account: Account = initialData.account
      val teamId: TeamId = TeamId(account.teams.head.team_id)
      val orgId = initialData.account.org.id
      val taId = account.teams.head.access_members.head.ta_id
      val accountId: Long = account.internal_id

      val final_res: Future[(Option[Long], Long)] = for {
        // create a campaign
        campaignWithStatsAndEmail: CampaignWithStatsAndEmail <- campaignService.createCampaign(
            orgId = orgId,
            accountId = accountId,
            teamId = teamId.id,
            taId = taId,
            data = CampaignCreateForm(
              name = None,
              timezone = Some("Asia/Kolkata"),
              campaign_owner_id = Some(accountId),
              campaign_type = CampaignType.Drip,
            ),
            campaignSettings = None,
            permittedAccountIdsForEditCampaigns = Seq(accountId),
            ownerFirstName = "Parth"
          )
          .flatMap {
            case Left(err) =>
              Future.failed(new Exception(s"Campaign Creation Failed :: $err"))

            case Right(value) =>
              Future.successful(value)
          }
        emailVariant0 <- campaignStepService.createVariant(
            orgId = orgId,
            data = CampaignStepVariantCreateOrUpdate(
              parent_id = 0L,
              step_data = CampaignStepData.AutoEmailStep(
                subject = "Subject",
                body = "body"
              ),
              step_delay = 0,
              notes = None,
              priority = None,
            ),
            teamId = teamId.id,
            userId = accountId,
            taId = taId,
            stepId = 0,
            campaignId = campaignWithStatsAndEmail.id,
            campaignHeadStepId = None
          )
          .flatMap {
            case Left(err) =>
              Future.failed(new Exception(s"Error while creating variant :: $err"))

            case Right(value) =>
              Future.successful(value)
          }

        deleteVariant <- Future.fromTry(campaignService.deleteVariant(
          stepId = emailVariant0.step_id,
          campaign = Campaign(
            id = campaignWithStatsAndEmail.id,
            uuid = None,
            account_id = accountId,
            team_id = teamId.id,
            shared_with_team = false,
            name = campaignWithStatsAndEmail.name,
            status = campaignWithStatsAndEmail.status,
            head_step_id = campaignWithStatsAndEmail.head_step_id,
            settings = campaignWithStatsAndEmail.settings,
            last_scheduled_at = None,
            created_at = campaignWithStatsAndEmail.created_at
          ),
          loggedinAccount = account,
          campaignId = campaignWithStatsAndEmail.id,
          step = CampaignStep(
            id = emailVariant0.step_id,
            label = emailVariant0.label,
            campaign_id = emailVariant0.campaign_id,
            delay = 0,
            step_type = CampaignStepType.AutoEmailStep,
            created_at = DateTime.now()
          ),
          variantId = emailVariant0.id.toInt,
          teamId = teamId
        ))

      } yield {

        (deleteVariant, emailVariant0.id)

      }

      final_res.map(res => {
        assert(res._1.get == res._2)
      }).recover(e => {
        assert(false)
      })
    }

  }

}





