package app.api.campaign.dao

import api.accounts.TeamId
import api.campaigns.dao.CampaignSchedulingMetadataDAO
import api.campaigns.services.CampaignId
import org.scalamock.scalatest.MockFactory
import org.scalatest.funspec.AnyFunSpec

class CampaignSchedulingMetadataDAOSpec extends AnyFunSpec with MockFactory {

  describe("getQueryForGetCampaignsToUpdateSchedulingMetadata") {
    it("should give query") {
      val expected =
        """
          |SELECT csm.campaign_id, csm.team_id
          |           FROM campaign_scheduling_metadata csm
          |           INNER JOIN campaigns c ON (
          |           c.id = csm.campaign_id AND c.team_id = csm.team_id
          |           )
          |           INNER JOIN teams t on (
          |           c.team_id = t.id
          |           )
          |           INNER JOIN organizations o ON o.id = t.org_id
          |           where csm.next_to_be_updated_at < now()
          |           AND (
          |                (
          |                NOT in_queue_for_csm_update
          |                 AND (
          |                   pushed_to_csm_update_queue_at IS NULL OR
          |                   pushed_to_csm_update_queue_at < now() - interval '1 hours'
          |                 )
          |                )
          |                OR (
          |                  in_queue_for_csm_update
          |                  AND
          |                   pushed_to_csm_update_queue_at < now() - interval '2 hours'
          |                )
          |           )
          |           AND c.status in (?, ?)
          |           LIMIT 100;
          |""".stripMargin

      val qry_str = CampaignSchedulingMetadataDAO.getQueryForGetCampaignsToUpdateSchedulingMetadata


      val split = expected.split(s"\\s+")
      val rhs = split.reduce((a1, a2) => {
        a1 + " " + a2
      })
      val lhs = qry_str.value.split("\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })

      assert(lhs == rhs)
    }
  }


  describe("getQueryScheduleForUpdateWhenCampaignIsUpdated") {
    it("should give query") {
      val expected =
        """
          |UPDATE campaign_scheduling_metadata
          |         SET
          |         next_to_be_updated_at = now() + interval '5 minutes'
          |         WHERE campaign_id = ?
          |         AND team_id = ?
          |         AND NOT in_queue_for_csm_update
          |         AND next_to_be_updated_at > now() + interval '5 minutes';
          |       """.stripMargin

      val qry_str = CampaignSchedulingMetadataDAO.getQueryScheduleForUpdateWhenCampaignIsUpdated(
        campaignId = CampaignId(123),
        teamId = TeamId(321)
      )


      val split = expected.split(s"\\s+")
      val rhs = split.reduce((a1, a2) => {
        a1 + " " + a2
      })
      val lhs = qry_str.value.split("\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })

      assert(lhs == rhs)
    }
  }


  describe("getQueryUpdateCampaignSchedulingMetadata") {
    it("should give query") {
      val expected =
        """
          |         UPDATE campaign_scheduling_metadata
          |         SET prospects_remaining_to_be_scheduled_exists = ?,
          |         last_updated_at = now(),
          |         next_to_be_updated_at = now() + interval '12 hours',
          |         pushed_to_csm_update_queue_at = null,
          |         in_queue_for_csm_update = false
          |         WHERE campaign_id = ?
          |         AND team_id = ?;
          |       """.stripMargin

      val qry_str = CampaignSchedulingMetadataDAO.getQueryUpdateCampaignSchedulingMetadata(
        campaignId = CampaignId(123),
        teamId = TeamId(321),
        prospects_remaining_to_be_scheduled_exists = true
      )


      val split = expected.split(s"\\s+")
      val rhs = split.reduce((a1, a2) => {
        a1 + " " + a2
      })
      val lhs = qry_str.value.split("\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })

      assert(lhs == rhs)
    }
  }

}
