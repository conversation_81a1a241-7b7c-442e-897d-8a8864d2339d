package app.api.campaign

import api.accounts.TeamId
import api.accounts.models.OrgId
import api.accounts.email.models.{EmailServiceProvider, SrMxCheckESPType}
import api.campaigns.CampaignProspectDAO
import api.campaigns.models.{CampaignEmailSettingsId, CampaignStepType}
import api.prospects.models.ProspectCategoryId
import app.TestUtils
import io.smartreach.esp.api.emails.EmailSettingId
import org.joda.time.DateTime
import org.scalamock.scalatest.AsyncMockFactory
import org.scalatest.funspec.AsyncFunSpec
import scalikejdbc.scalikejdbcSQLInterpolationImplicitDef
import sr_scheduler.models.CampaignForScheduling.CampaignEmailSettingForScheduler
import sr_scheduler.models.ChannelType
import utils.mq.channel_scheduler.SchedulerMapStepIdAndDelay

class CampaignProspectDAOSpec extends AsyncFunSpec with AsyncMockFactory {

  describe("testing CampaignProspectDAO.getSentOrScheduledProspectCountQuery") {
    it("should only give sent prospect count") {

      val expected =
        """
          |select
          |        count(*) as count, es.inbox_email_setting_id as eset_id
          |
          |        from emails_scheduled es
          |        where es.inbox_email_setting_id in (?)
          |        AND es.team_id = ?
          |        AND es.scheduled_from_campaign

          |        AND es.sent

          |        AND es.sent_at >= ?
          |        group by es.inbox_email_setting_id
          |        ;
          |""".stripMargin

      val qry_str = CampaignProspectDAO.getSentOrScheduledProspectCountQuery(
          emailSettingIds = Seq(EmailSettingId(1)),
          accountTimezone = "Asia/Kolkata",
          countOnlySentEmails = true,
          teamId = TeamId(1L)
        )
        .statement

      val split = expected.split(s"\\s+")
      val rhs = split.reduce((a1, a2) => {
        a1 + " " + a2
      })
      val lhs = qry_str.split("\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })

      assert(lhs == rhs)

    }

    it("should only give sent or scheduled prospect count") {

      val expected =
        """

          |
          |          select
          |          count(*) as count, es.inbox_email_setting_id as eset_id
          |
          |          from emails_scheduled es
          |          where es.inbox_email_setting_id in (?)
          |          AND es.team_id = ?
          |          AND es.scheduled_from_campaign
          |            -- AND not es.sent -- we are counting both sent + scheduled-and-not-sent here, so need for this check

          |            AND es.scheduled_at >= ?
          |          group by es.inbox_email_setting_id
          |""".stripMargin

      val qry_str = CampaignProspectDAO.getSentOrScheduledProspectCountQuery(
          emailSettingIds = Seq(EmailSettingId(1)),
          accountTimezone = "Asia/Kolkata",
          countOnlySentEmails = false,
          teamId = TeamId(1L)
        )
        .statement

      val split = expected.split(s"\\s+")
      val rhs = split.reduce((a1, a2) => {
        a1 + " " + a2
      })
      val lhs = qry_str.split("\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })

      assert(lhs == rhs)

    }
  }

  describe("testing CampaignProspectDAO.getScheduledProspectCountForCampaignQuery") {
    it("should return sent prospect count only in a campaign") {
      val expected =
        """
          |
          |          select
          |          es.campaign_id,
          |          count(*) FILTER (WHERE es.is_opening_step ) as newCount,
          |          count(*) FILTER (WHERE NOT es.is_opening_step ) AS followupCount,
          |          0 as newCountNotSent,
          |          0 as followupCountNotSent
          |
          |          from emails_scheduled es
          |
          |
          |          where es.inbox_email_setting_id in (?)
          |
          |          AND es.scheduled_from_campaign
          |
          |          AND (
          |            ( es.campaign_id = ?
          |            AND
          |            es.sent_at >= ?
          |          ) )
          |
          |          -- this clause is only to optimize the search space
          |          AND ( es.sent_at >= now() - interval '24 hours' )
          |          AND team_id = ?
          |          AND es.sent
          |
          |          GROUP BY es.campaign_id
          |          ;
          |""".stripMargin

      val qry_str = CampaignProspectDAO.getScheduledProspectCountForCampaignQuery(
          emailSettingIds = Seq(EmailSettingId(3)),
          teamId = 5,
          campaignIdAndTimezone = Seq((7, "Asia/Kolkata")),
          countOnlySentProspects = true
        )
        .statement

      val split = expected.split(s"\\s+")
      val rhs = split.reduce((a1, a2) => {
        a1 + " " + a2
      })
      val lhs = qry_str.split("\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })

      assert(lhs == rhs)
    }

    it("should return sent or scheduled prospect count in a campaign") {
      val expected =
        """
          |          select part1.campaign_id, sum(newCount) as newCount, sum(followupCount) as followupCount,
          |          sum(newCountNotSent) as newCountNotSent, sum(followupCountNotSent) as followupCountNotSent
          |          from (
          |            select
          |            es.campaign_id,
          |            count(*) FILTER (WHERE es.is_opening_step ) as newCount,
          |            count(*) FILTER (WHERE NOT es.is_opening_step ) AS followupCount,
          |            0 as newCountNotSent,
          |            0 as followupCountNotSent
          |
          |            from emails_scheduled es
          |
          |
          |            where es.inbox_email_setting_id in (?)
          |
          |            AND es.scheduled_from_campaign
          |
          |            AND ( ( es.campaign_id = ? AND es.sent_at >= ? ) )
          |
          |            -- this clause is only to optimize the search space
          |            AND ( es.sent_at >= now() - interval '24 hours' )
          |
          |            AND team_id = ?
          |            AND es.sent
          |
          |            GROUP BY es.campaign_id
          |
          |          UNION ALL
          |
          |
          |            select
          |            es.campaign_id,
          |            0 as newCount,
          |            0 AS followupCount,
          |            count(*) FILTER (WHERE es.is_opening_step ) as newCountNotSent,
          |            count(*) FILTER (WHERE NOT es.is_opening_step ) as followupCountNotSent
          |
          |            from emails_scheduled es
          |
          |
          |            where es.inbox_email_setting_id in (?)
          |
          |            AND es.scheduled_from_campaign
          |
          |            AND ( ( es.campaign_id = ? AND ( es.scheduled_at >= ? ) ) )
          |
          |            -- this clause is only to optimize the search space
          |            AND ( es.scheduled_at >= now() - interval '24 hours' )
          |
          |            AND team_id = ?
          |            AND NOT es.sent
          |
          |            GROUP BY es.campaign_id
          |          ) part1
          |            group by part1.campaign_id
          |            ;
          |""".stripMargin

      val qry_str = CampaignProspectDAO.getScheduledProspectCountForCampaignQuery(
          emailSettingIds = Seq(EmailSettingId(5)),
          teamId = 11,
          campaignIdAndTimezone = Seq((13, "Asia/Kolkata")),
          countOnlySentProspects = false
        )
        .statement

      val split = expected.split(s"\\s+")
      val rhs = split.reduce((a1, a2) => {
        a1 + " " + a2
      })
      val lhs = qry_str.split("\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })

      assert(lhs == rhs)
    }

    it("should return sent or scheduled prospect count in multiple campaigns") {
      val expected =
        """
          |          select part1.campaign_id, sum(newCount) as newCount, sum(followupCount) as followupCount,
          |          sum(newCountNotSent) as newCountNotSent, sum(followupCountNotSent) as followupCountNotSent
          |          from (
          |            select
          |            es.campaign_id,
          |            count(*) FILTER (WHERE es.is_opening_step ) as newCount,
          |            count(*) FILTER (WHERE NOT es.is_opening_step ) AS followupCount,
          |            0 as newCountNotSent,
          |            0 as followupCountNotSent
          |
          |            from emails_scheduled es
          |
          |
          |            where es.inbox_email_setting_id in (?)
          |
          |            AND es.scheduled_from_campaign
          |
          |            AND ( ( es.campaign_id = ? AND es.sent_at >= ? ) OR ( es.campaign_id = ? AND es.sent_at >= ? ) )
          |
          |            -- this clause is only to optimize the search space
          |            AND ( es.sent_at >= now() - interval '24 hours' )
          |
          |            AND team_id = ?
          |            AND es.sent
          |
          |            GROUP BY es.campaign_id
          |
          |          UNION ALL
          |
          |
          |            select
          |            es.campaign_id,
          |            0 as newCount,
          |            0 AS followupCount,
          |            count(*) FILTER (WHERE es.is_opening_step ) as newCountNotSent,
          |            count(*) FILTER (WHERE NOT es.is_opening_step ) as followupCountNotSent
          |
          |            from emails_scheduled es
          |
          |
          |            where es.inbox_email_setting_id in (?)
          |
          |            AND es.scheduled_from_campaign
          |
          |            AND ( ( es.campaign_id = ? AND ( es.scheduled_at >= ? ) ) OR ( es.campaign_id = ? AND ( es.scheduled_at >= ? ) ) )
          |
          |            -- this clause is only to optimize the search space
          |            AND ( es.scheduled_at >= now() - interval '24 hours' )
          |
          |            AND team_id = ?
          |            AND NOT es.sent
          |
          |            GROUP BY es.campaign_id
          |          ) part1
          |            group by part1.campaign_id
          |            ;
          |""".stripMargin

      val qry_str = CampaignProspectDAO.getScheduledProspectCountForCampaignQuery(
          emailSettingIds = Seq(EmailSettingId(5)),
          teamId = 11,
          campaignIdAndTimezone = Seq((13, "Asia/Kolkata"), (17, "America/Los_Angeles")),
          countOnlySentProspects = false
        )
        .statement

      val split = expected.split(s"\\s+")
      val rhs = split.reduce((a1, a2) => {
        a1 + " " + a2
      })
      val lhs = qry_str.split("\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })

      assert(lhs == rhs)
    }
  }

  describe("Testing CampaignProspectDAO.fetchProspectsV3MultichannelQuery") {
    it("should send only to prospects sent in current cycle for magic col") {
      val expected =
        sqls"""
              SELECT
         -- DISTINCT (prospects.id),

         prospects.id,

          a.id AS owner_id,
          a.email AS owner_email,
          a.uuid AS owner_uuid,
          CONCAT(a.first_name, ' ',  a.last_name) AS owner_name,

          prospects.uuid AS prospect_uuid,

          prospects.team_id,

          prospects.first_name,
          prospects.last_name,
          pe.email,
          pe.email_domain,

          (CASE WHEN pe.email_checked THEN pe.invalid_email ELSE NULL END) AS invalid_email,

          prospect_lists.name AS list,
          prospects.list_id,
          prospects.company,
          prospects.city,
          prospects.country,
          prospects.timezone,

          prospects.created_at,
          prospects.updated_at,
          prospects.last_contacted_at,
          prospects.last_contacted_at_phone,
          prospects.last_replied_at,
          prospects.last_opened_at,
          prospects.total_opens,
          prospects.total_clicks,
          prospects.custom_fields,
          prospects.will_delete,
          pe.email_bounced,
          pe.force_send_invalid_email,
          pcat.name as prospect_category_custom,
          pcat.label_color as prospect_category_label_color,
          pcat.id as prospect_category_id_custom,

          prospects.prospect_source,
          prospects.prospect_account_id as prospect_account_id,
          pa.uuid as prospect_account_uuid,
          pa.name as prospect_account_name,
           null  as tags,
           COALESCE(magic_columns.prospect_magic_columns, '[]')  as magic_columns,

           null  as active_campaigns,

          prospects.state,
          prospects.job_title,
          prospects.phone,
          prospects.phone_2,
          prospects.phone_3,
          prospects.linkedin_url,

          prospects.latest_reply_sentiment_uuid,
          prospects.latest_task_done_at,
          ccl.completed_at as last_call_made_at
    ,

          pe.email_checked,
          pe.email_sent_for_validation,
          pe.email_sent_for_validation_at,

          cp.current_step_id,
          cp.last_scheduled,

          cp.latest_task_done_or_skipped_at,
          cp.current_step_status_for_scheduler,

          pa.id as pa_id,
          pa.uuid as pa_uuid,
          pa.owner_id as pa_owner_id,
          pa.team_id as pa_team_id,
          pa.name as pa_name,
          pa.custom_id as pa_custom_id,
          pa.description as pa_description,
          pa.source as pa_source,
          pa.website as pa_website,
          pa.industry as pa_industry,
          pa.linkedin_url as pa_linkedin_url,
          pa.custom_fields as pa_custom_fields,
          pa.created_at as pa_created_at,
          pa.updated_at as pa_updated_at,
          cp.current_step_type,
          CONCAT(a.first_name, ' ', a.last_name) as pa_owner_name


         FROM campaigns_prospects cp
          INNER JOIN prospects ON (prospects.id = cp.prospect_id AND prospects.team_id = cp.team_id)
          INNER JOIN accounts a ON a.id = prospects.account_id
          INNER JOIN prospect_categories_custom pcat on ( pcat.id = prospects.prospect_category_id_custom AND pcat.team_id = prospects.team_id
              AND pcat.id != ? )
          INNER JOIN campaigns ca ON (ca.id = cp.campaign_id AND ca.team_id = cp.team_id)
           LEFT JOIN prospects_emails pe ON ((pe.prospect_id = prospects.id) AND (pe.team_id = prospects.team_id) AND pe.is_primary)
          LEFT JOIN prospect_accounts pa ON (pa.id = prospects.prospect_account_id AND pa.team_id = prospects.team_id)
          LEFT JOIN prospect_lists ON (prospect_lists.id = prospects.list_id AND prospect_lists.team_id = prospects.team_id)
          LEFT JOIN prospects_metadata pm ON pm.prospect_id = prospects.id AND pm.team_id = prospects.team_id


          LEFT JOIN LATERAL (
            SELECT
              json_agg(json_build_object(
               'column_output', cdp.magic_prompt_generated_output,
                'failed_message', cdp.failed_message,
                'column_name', cd.name,
                'status', cdp.status
              )) AS prospect_magic_columns
            FROM
              column_defs_prospects AS cdp
              JOIN column_defs AS cd ON cdp.column_id = cd.id
                AND cd.team_id = cdp.team_id
            WHERE
              cdp.prospect_id = prospects.id
              AND cdp.team_id = prospects.team_id
          ) AS magic_columns ON TRUE

          LEFT JOIN LATERAL (
                SELECT completed_at
                FROM call_conference_logs
                WHERE primary_prospect_id = cp.prospect_id
                  AND team_id = cp.team_id
                ORDER BY completed_at DESC
                LIMIT 1
            ) AS ccl ON TRUE

         WHERE cp.campaign_id = ?
           AND cp.team_id = ?




          AND cp.active = true
          AND cp.to_check = false


          AND (
           cp.unpaused_by_admin = true

           OR (


            -- email-specific checks to stop campaign

            cp.bounced = false AND

            cp.replied = false
            AND cp.opted_out = false
            AND cp.out_of_office = false
            AND cp.auto_reply = false
            AND cp.completed = false
            AND cp.paused = false
            AND cp.will_resume_at is null

            -- TODO: linkedin-specific checks to stop campaign - ideally any such case should result in cp.completed to be true

          )
        )


          AND (

            CASE
             WHEN prospects.timezone is not null and prospects.timezone != '' THEN prospects.timezone IN (?)
             ELSE ca.timezone IN (?)
            END
          )

          AND prospects.prospect_category_id_custom != ?






                    AND (
                      CASE WHEN pm.last_touched_at IS NOT NULL
                      THEN pm.last_touched_at >= ?
                      ELSE (
                        prospects.last_contacted_at IS NOT NULL
                        AND
                        prospects.last_contacted_at >= ?
                      )
                      END
                    )
            AND cp.current_step_id IS NULL


            AND NOT EXISTS (SELECT
              pdp.id
            FROM
              potential_duplicate_prospect_ids pdp
              INNER JOIN potential_duplicate_prospects_log pdpl ON pdp.potential_duplicate_prospects_log_id = pdpl.id
                AND pdp.team_id = pdpl.team_id
            WHERE
              pdpl.status IN (
                ?,
                ?
                )
              AND pdp.prospect_id = prospects.id
              AND pdp.team_id = ?
              )


          ORDER BY  prospects.id ASC
          LIMIT ?
              """

      val qry_str = CampaignProspectDAO.fetchProspectsV3MultichannelQuery(
        channelType = ChannelType.LinkedinChannel,
        allowedProspectTimezones = List("Asia/Kolkata"),
        campaignId = 3L,
        teamId = TeamId(24L),
        limit = 53,
        doNotContactCategoryId = ProspectCategoryId(7L),
        channelRelevantStepIdAndDelay = Vector(),
        newProspectsInCampaign = true,
        firstStepIsMagicContent = false,
        sendOnlyToProspectsWhoWereSentInCurrentCycle = Some(DateTime.now()),
        campaign_email_setting = None,
        orgId = OrgId(2L),
         //emailNotCompulsoryEnabled = false,
        enable_magic_column = false,
        useModifiedQueryForDripCampaign = false
      )

      assert(TestUtils.compareExpectedAndActualQuery(
        expected = expected,
        actual = qry_str
      ))
    }

    it("should send only to prospects sent in current cycle") {
      val expected =
        sqls"""
              SELECT
         -- DISTINCT (prospects.id),

         prospects.id,

          a.id AS owner_id,
          a.email AS owner_email,
          a.uuid AS owner_uuid,
          CONCAT(a.first_name, ' ',  a.last_name) AS owner_name,

          prospects.uuid AS prospect_uuid,

          prospects.team_id,

          prospects.first_name,
          prospects.last_name,
          pe.email,
          pe.email_domain,

          (CASE WHEN pe.email_checked THEN pe.invalid_email ELSE NULL END) AS invalid_email,

          prospect_lists.name AS list,
          prospects.list_id,
          prospects.company,
          prospects.city,
          prospects.country,
          prospects.timezone,

          prospects.created_at,
          prospects.updated_at,
          prospects.last_contacted_at,
          prospects.last_contacted_at_phone,
          prospects.last_replied_at,
          prospects.last_opened_at,
          prospects.total_opens,
          prospects.total_clicks,
          prospects.custom_fields,
          prospects.will_delete,
          pe.email_bounced,
          pe.force_send_invalid_email,
          pcat.name as prospect_category_custom,
          pcat.label_color as prospect_category_label_color,
          pcat.id as prospect_category_id_custom,

          prospects.prospect_source,
          prospects.prospect_account_id as prospect_account_id,
          pa.uuid as prospect_account_uuid,
          pa.name as prospect_account_name,
           null  as tags,
           COALESCE(magic_columns.prospect_magic_columns, '[]')  as magic_columns,

           null  as active_campaigns,

          prospects.state,
          prospects.job_title,
          prospects.phone,
          prospects.phone_2,
          prospects.phone_3,
          prospects.linkedin_url,

          prospects.latest_reply_sentiment_uuid,
          prospects.latest_task_done_at,
          ccl.completed_at as last_call_made_at
    ,

          pe.email_checked,
          pe.email_sent_for_validation,
          pe.email_sent_for_validation_at,

          cp.current_step_id,
          cp.last_scheduled,

          cp.latest_task_done_or_skipped_at,
          cp.current_step_status_for_scheduler,

          pa.id as pa_id,
          pa.uuid as pa_uuid,
          pa.owner_id as pa_owner_id,
          pa.team_id as pa_team_id,
          pa.name as pa_name,
          pa.custom_id as pa_custom_id,
          pa.description as pa_description,
          pa.source as pa_source,
          pa.website as pa_website,
          pa.industry as pa_industry,
          pa.linkedin_url as pa_linkedin_url,
          pa.custom_fields as pa_custom_fields,
          pa.created_at as pa_created_at,
          pa.updated_at as pa_updated_at,
          cp.current_step_type,
          CONCAT(a.first_name, ' ', a.last_name) as pa_owner_name


         FROM campaigns_prospects cp
          INNER JOIN prospects ON (prospects.id = cp.prospect_id AND prospects.team_id = cp.team_id)
          INNER JOIN accounts a ON a.id = prospects.account_id
          INNER JOIN prospect_categories_custom pcat on ( pcat.id = prospects.prospect_category_id_custom AND pcat.team_id = prospects.team_id
              AND pcat.id != ? )
          INNER JOIN campaigns ca ON (ca.id = cp.campaign_id AND ca.team_id = cp.team_id)
           LEFT JOIN prospects_emails pe ON ((pe.prospect_id = prospects.id) AND (pe.team_id = prospects.team_id) AND pe.is_primary)
          LEFT JOIN prospect_accounts pa ON (pa.id = prospects.prospect_account_id AND pa.team_id = prospects.team_id)
          LEFT JOIN prospect_lists ON (prospect_lists.id = prospects.list_id AND prospect_lists.team_id = prospects.team_id)
          LEFT JOIN prospects_metadata pm ON pm.prospect_id = prospects.id AND pm.team_id = prospects.team_id


          LEFT JOIN LATERAL (
            SELECT
              json_agg(json_build_object(
               'column_output', cdp.magic_prompt_generated_output,
                'failed_message', cdp.failed_message,
                'column_name', cd.name,
                'status', cdp.status
              )) AS prospect_magic_columns
            FROM
              column_defs_prospects AS cdp
              JOIN column_defs AS cd ON cdp.column_id = cd.id
                AND cd.team_id = cdp.team_id
            WHERE
              cdp.prospect_id = prospects.id
              AND cdp.team_id = prospects.team_id
          ) AS magic_columns ON TRUE

          LEFT JOIN LATERAL (
                SELECT completed_at
                FROM call_conference_logs
                WHERE primary_prospect_id = cp.prospect_id
                  AND team_id = cp.team_id
                ORDER BY completed_at DESC
                LIMIT 1
            ) AS ccl ON TRUE

         WHERE cp.campaign_id = ?
           AND cp.team_id = ?




          AND cp.active = true
          AND cp.to_check = false


          AND (
           cp.unpaused_by_admin = true

           OR (


            -- email-specific checks to stop campaign

            cp.bounced = false AND

            cp.replied = false
            AND cp.opted_out = false
            AND cp.out_of_office = false
            AND cp.auto_reply = false
            AND cp.completed = false
            AND cp.paused = false
            AND cp.will_resume_at is null

            -- TODO: linkedin-specific checks to stop campaign - ideally any such case should result in cp.completed to be true

          )
        )


          AND (

            CASE
             WHEN prospects.timezone is not null and prospects.timezone != '' THEN prospects.timezone IN (?)
             ELSE ca.timezone IN (?)
            END
          )

          AND prospects.prospect_category_id_custom != ?






                    AND (
                      CASE WHEN pm.last_touched_at IS NOT NULL
                      THEN pm.last_touched_at >= ?
                      ELSE (
                        prospects.last_contacted_at IS NOT NULL
                        AND
                        prospects.last_contacted_at >= ?
                      )
                      END
                    )
            AND (
                cp.current_step_id IS NULL
                AND (
                cp.current_step_status_for_scheduler = ? 
                OR cp.current_step_status_for_scheduler IS NULL
                )
                )


            AND NOT EXISTS (SELECT
              pdp.id
            FROM
              potential_duplicate_prospect_ids pdp
              INNER JOIN potential_duplicate_prospects_log pdpl ON pdp.potential_duplicate_prospects_log_id = pdpl.id
                AND pdp.team_id = pdpl.team_id
            WHERE
              pdpl.status IN (
                ?,
                ?
                )
              AND pdp.prospect_id = prospects.id
              AND pdp.team_id = ?
              )


          ORDER BY  prospects.id ASC
          LIMIT ?
              """

      val qry_str = CampaignProspectDAO.fetchProspectsV3MultichannelQuery(
        channelType = ChannelType.LinkedinChannel,
        allowedProspectTimezones = List("Asia/Kolkata"),
        campaignId = 3L,
        teamId = TeamId(24L),
        limit = 53,
        doNotContactCategoryId = ProspectCategoryId(7L),
        channelRelevantStepIdAndDelay = Vector(),
        newProspectsInCampaign = true,
        firstStepIsMagicContent = true,
        sendOnlyToProspectsWhoWereSentInCurrentCycle = Some(DateTime.now()),
        campaign_email_setting = None,
        orgId = OrgId(2L),
        //emailNotCompulsoryEnabled = false,
        enable_magic_column = false,
        useModifiedQueryForDripCampaign = false
      )

      assert(TestUtils.compareExpectedAndActualQuery(
        expected = expected,
        actual = qry_str
      ))
    }

    it("should schedule old prospects") {
      val expected =
        sqls"""
              SELECT
         -- DISTINCT (prospects.id),

         prospects.id,

          a.id AS owner_id,
          a.email AS owner_email,
          a.uuid AS owner_uuid,
          CONCAT(a.first_name, ' ',  a.last_name) AS owner_name,

          prospects.uuid AS prospect_uuid,

          prospects.team_id,

          prospects.first_name,
          prospects.last_name,
          pe.email,
          pe.email_domain,

          (CASE WHEN pe.email_checked THEN pe.invalid_email ELSE NULL END) AS invalid_email,

          prospect_lists.name AS list,
          prospects.list_id,
          prospects.company,
          prospects.city,
          prospects.country,
          prospects.timezone,

          prospects.created_at,
          prospects.updated_at,
          prospects.last_contacted_at,
          prospects.last_contacted_at_phone,
          prospects.last_replied_at,
          prospects.last_opened_at,
          prospects.total_opens,
          prospects.total_clicks,
          prospects.custom_fields,
          prospects.will_delete,
          pe.email_bounced,
          pe.force_send_invalid_email,
          pcat.name as prospect_category_custom,
          pcat.label_color as prospect_category_label_color,
          pcat.id as prospect_category_id_custom,

          prospects.prospect_source,
          prospects.prospect_account_id as prospect_account_id,
          pa.uuid as prospect_account_uuid,
          pa.name as prospect_account_name,
          null as tags,

          COALESCE(magic_columns.prospect_magic_columns, '[]')  as magic_columns,

          null as active_campaigns,

          prospects.state,
          prospects.job_title,
          prospects.phone,
          prospects.phone_2,
          prospects.phone_3,
          prospects.linkedin_url,
          prospects.latest_reply_sentiment_uuid,
          prospects.latest_task_done_at,
          ccl.completed_at as last_call_made_at ,

          pe.email_checked,
          pe.email_sent_for_validation,
          pe.email_sent_for_validation_at,

          cp.current_step_id,
          cp.last_scheduled,

          cp.latest_task_done_or_skipped_at,
          cp.current_step_status_for_scheduler,

          pa.id as pa_id,
          pa.uuid as pa_uuid,
          pa.owner_id as pa_owner_id,
          pa.team_id as pa_team_id,
          pa.name as pa_name,
          pa.custom_id as pa_custom_id,
          pa.description as pa_description,
          pa.source as pa_source,
          pa.website as pa_website,
          pa.industry as pa_industry,
          pa.linkedin_url as pa_linkedin_url,
          pa.custom_fields as pa_custom_fields,
          pa.created_at as pa_created_at,
          pa.updated_at as pa_updated_at,
          cp.current_step_type,
          CONCAT(a.first_name, ' ', a.last_name) as pa_owner_name


         FROM campaigns_prospects cp
          INNER JOIN prospects ON (prospects.id = cp.prospect_id AND prospects.team_id = cp.team_id)
          INNER JOIN accounts a ON a.id = prospects.account_id
          INNER JOIN prospect_categories_custom pcat on ( pcat.id = prospects.prospect_category_id_custom AND pcat.team_id = prospects.team_id
              AND pcat.id != ? )
          INNER JOIN campaigns ca ON (ca.id = cp.campaign_id AND ca.team_id = cp.team_id)
          LEFT JOIN prospects_emails pe ON ((pe.prospect_id = prospects.id) AND (pe.team_id = prospects.team_id) AND pe.is_primary)
          LEFT JOIN prospect_accounts pa ON (pa.id = prospects.prospect_account_id AND pa.team_id = prospects.team_id)
          LEFT JOIN prospect_lists ON (prospect_lists.id = prospects.list_id AND prospect_lists.team_id = prospects.team_id)

          LEFT JOIN LATERAL (

            SELECT
              json_agg(json_build_object(
                       'column_output', cdp.magic_prompt_generated_output,
                        'failed_message', cdp.failed_message,
                        'column_name', cd.name,
                        'status', cdp.status
                      )) AS prospect_magic_columns
            FROM
              column_defs_prospects AS cdp
              JOIN column_defs AS cd ON cdp.column_id = cd.id
              AND cd.team_id = cdp.team_id
            WHERE
              cdp.prospect_id = prospects.id
              AND cdp.team_id = prospects.team_id
          ) AS magic_columns ON TRUE

          LEFT JOIN LATERAL (
                SELECT completed_at
                FROM call_conference_logs
                WHERE primary_prospect_id = cp.prospect_id
                  AND team_id = cp.team_id
                ORDER BY completed_at DESC
                LIMIT 1
            ) AS ccl ON TRUE

         WHERE cp.campaign_id = ?
           AND cp.team_id = ?

          AND cp.active = true
          AND cp.to_check = false

          AND (
           cp.unpaused_by_admin = true

           OR (

            -- email-specific checks to stop campaign
            cp.bounced = false AND
            cp.replied = false
            AND cp.opted_out = false
            AND cp.out_of_office = false
            AND cp.auto_reply = false
            AND cp.completed = false
            AND cp.paused = false
            AND cp.will_resume_at is null

            -- TODO: linkedin-specific checks to stop campaign - ideally any such case should result in cp.completed to be true

          )
        )


          AND (

            CASE
             WHEN prospects.timezone is not null and prospects.timezone != '' THEN prospects.timezone IN (?)
             ELSE ca.timezone IN (?)
            END
          )

          AND prospects.prospect_category_id_custom != ?

          AND cp.current_step_id IS NOT NULL

          AND (
          (
            cp.current_step_id = ?
            )
            AND (
              (
                cp.current_step_status_for_scheduler = ?
                AND
                date_trunc('day', cp.latest_task_done_or_skipped_at AT TIME ZONE (CASE WHEN (prospects.timezone IS NOT NULL AND prospects.timezone != '') THEN prospects.timezone ELSE ca.timezone END) ) <  ((now() AT TIME ZONE (CASE WHEN (prospects.timezone IS NOT NULL AND prospects.timezone != '') THEN prospects.timezone ELSE ca.timezone END) ) - interval '97' second)
              )
              OR
              (
                cp.current_step_status_for_scheduler = ?
                AND
                date_trunc('day', cp.latest_task_done_or_skipped_at AT TIME ZONE (CASE WHEN (prospects.timezone IS NOT NULL AND prospects.timezone != '') THEN prospects.timezone ELSE ca.timezone END) ) <  ((now() AT TIME ZONE (CASE WHEN (prospects.timezone IS NOT NULL AND prospects.timezone != '') THEN prospects.timezone ELSE ca.timezone END) ) - interval '97' second)
              )
              OR
              (
                cp.current_step_status_for_scheduler = ?
              )
              OR
              cp.current_step_status_for_scheduler IS null
             )
             OR
             (
            cp.current_step_id = ?
            )
            AND (
              (
                cp.current_step_status_for_scheduler = ?
                AND
                date_trunc('day', cp.latest_task_done_or_skipped_at AT TIME ZONE (CASE WHEN (prospects.timezone IS NOT NULL AND prospects.timezone != '') THEN prospects.timezone ELSE ca.timezone END) ) <  ((now() AT TIME ZONE (CASE WHEN (prospects.timezone IS NOT NULL AND prospects.timezone != '') THEN prospects.timezone ELSE ca.timezone END) ) - interval '97' second)
              )
              OR
              (
                cp.current_step_status_for_scheduler = ?
                AND
                date_trunc('day', cp.latest_task_done_or_skipped_at AT TIME ZONE (CASE WHEN (prospects.timezone IS NOT NULL AND prospects.timezone != '') THEN prospects.timezone ELSE ca.timezone END) ) <  ((now() AT TIME ZONE (CASE WHEN (prospects.timezone IS NOT NULL AND prospects.timezone != '') THEN prospects.timezone ELSE ca.timezone END) ) - interval '97' second)
              )
              OR
              (
                cp.current_step_status_for_scheduler = ?
              )
              OR
              cp.current_step_status_for_scheduler IS null
             )
            )

            AND (cp.latest_task_done_or_skipped_at IS NULL OR cp.latest_task_done_or_skipped_at < now() - interval '10 hours')

            AND NOT EXISTS (SELECT
              pdp.id
            FROM
              potential_duplicate_prospect_ids pdp
              INNER JOIN potential_duplicate_prospects_log pdpl ON pdp.potential_duplicate_prospects_log_id = pdpl.id
                AND pdp.team_id = pdpl.team_id
            WHERE
              pdpl.status IN (
                ?,
                ?
                )
              AND pdp.prospect_id = prospects.id
              AND pdp.team_id = ?
              )

          ORDER BY last_contacted_at ASC, prospects.id ASC
          LIMIT ?
              """

      val stepIdAndDelayMap = SchedulerMapStepIdAndDelay(
        is_head_step_in_the_campaign = false,
        currentStepType = CampaignStepType.LinkedinViewProfile,
        nextStepType = CampaignStepType.LinkedinInmail,
        currentStepId = 11L,
        delayTillNextStep = 97
      )

      val qry_str = CampaignProspectDAO.fetchProspectsV3MultichannelQuery(
        channelType = ChannelType.LinkedinChannel,
        allowedProspectTimezones = List("Asia/Kolkata"),
        campaignId = 3L,
        teamId = TeamId(24L),
        limit = 53,
        doNotContactCategoryId = ProspectCategoryId(7L),
        channelRelevantStepIdAndDelay = Vector(stepIdAndDelayMap, stepIdAndDelayMap),
        newProspectsInCampaign = false,
        firstStepIsMagicContent = false,
        sendOnlyToProspectsWhoWereSentInCurrentCycle = None,
        campaign_email_setting = None,
        orgId = OrgId(2L),
        enable_magic_column = false,
         //emailNotCompulsoryEnabled = false,
        useModifiedQueryForDripCampaign = false


      )

      assert(TestUtils.compareExpectedAndActualQuery(
        expected = expected,
        actual = qry_str
      ))
    }

    it("should schedule old prospects with email_setting_id check") {
      val expected =
        sqls"""
        SELECT
         -- DISTINCT (prospects.id),

         prospects.id,

          a.id AS owner_id,
          a.email AS owner_email,
          a.uuid AS owner_uuid,
          CONCAT(a.first_name, ' ',  a.last_name) AS owner_name,

          prospects.uuid AS prospect_uuid,

          prospects.team_id,

          prospects.first_name,
          prospects.last_name,
          pe.email,
          pe.email_domain,

          (CASE WHEN pe.email_checked THEN pe.invalid_email ELSE NULL END) AS invalid_email,

          prospect_lists.name AS list,
          prospects.list_id,
          prospects.company,
          prospects.city,
          prospects.country,
          prospects.timezone,

          prospects.created_at,
          prospects.updated_at,
          prospects.last_contacted_at,
          prospects.last_contacted_at_phone,
          prospects.last_replied_at,
          prospects.last_opened_at,
          prospects.total_opens,
          prospects.total_clicks,
          prospects.custom_fields,
          prospects.will_delete,
          pe.email_bounced,
          pe.force_send_invalid_email,
          pcat.name as prospect_category_custom,
          pcat.label_color as prospect_category_label_color,
          pcat.id as prospect_category_id_custom,

          prospects.prospect_source,
          prospects.prospect_account_id as prospect_account_id,
          pa.uuid as prospect_account_uuid,
          pa.name as prospect_account_name,
           null  as tags,
           COALESCE(magic_columns.prospect_magic_columns, '[]')  as magic_columns,

           null  as active_campaigns,

          prospects.state,
          prospects.job_title,
          prospects.phone,
          prospects.phone_2,
          prospects.phone_3,
          prospects.linkedin_url,

          prospects.latest_reply_sentiment_uuid,
          prospects.latest_task_done_at,
          ccl.completed_at as last_call_made_at
    ,

          pe.email_checked,
          pe.email_sent_for_validation,
          pe.email_sent_for_validation_at,

          cp.current_step_id,
          cp.last_scheduled,

          cp.latest_task_done_or_skipped_at,
          cp.current_step_status_for_scheduler,

          pa.id as pa_id,
          pa.uuid as pa_uuid,
          pa.owner_id as pa_owner_id,
          pa.team_id as pa_team_id,
          pa.name as pa_name,
          pa.custom_id as pa_custom_id,
          pa.description as pa_description,
          pa.source as pa_source,
          pa.website as pa_website,
          pa.industry as pa_industry,
          pa.linkedin_url as pa_linkedin_url,
          pa.custom_fields as pa_custom_fields,
          pa.created_at as pa_created_at,
          pa.updated_at as pa_updated_at,
          cp.current_step_type,
          CONCAT(a.first_name, ' ', a.last_name) as pa_owner_name


         FROM campaigns_prospects cp
          INNER JOIN prospects ON (prospects.id = cp.prospect_id AND prospects.team_id = cp.team_id)
          INNER JOIN accounts a ON a.id = prospects.account_id
          INNER JOIN prospect_categories_custom pcat on ( pcat.id = prospects.prospect_category_id_custom AND pcat.team_id = prospects.team_id
              AND pcat.id != ? )
          INNER JOIN campaigns ca ON (ca.id = cp.campaign_id AND ca.team_id = cp.team_id)

          INNER JOIN prospects_emails pe ON ((pe.prospect_id = prospects.id) AND (pe.team_id = prospects.team_id) AND pe.is_primary)
          LEFT JOIN domain_public_dns dpd ON dpd.domain = pe.email_domain

          LEFT JOIN prospect_accounts pa ON (pa.id = prospects.prospect_account_id AND pa.team_id = prospects.team_id)
          LEFT JOIN prospect_lists ON (prospect_lists.id = prospects.list_id AND prospect_lists.team_id = prospects.team_id)



          LEFT JOIN LATERAL (
            SELECT
              json_agg(json_build_object(
               'column_output', cdp.magic_prompt_generated_output,
                'failed_message', cdp.failed_message,
                'column_name', cd.name,
                'status', cdp.status
              )) AS prospect_magic_columns
            FROM
              column_defs_prospects AS cdp
              JOIN column_defs AS cd ON cdp.column_id = cd.id
                AND cd.team_id = cdp.team_id
            WHERE
              cdp.prospect_id = prospects.id
              AND cdp.team_id = prospects.team_id
          ) AS magic_columns ON TRUE

          LEFT JOIN LATERAL (
                SELECT completed_at
                FROM call_conference_logs
                WHERE primary_prospect_id = cp.prospect_id
                  AND team_id = cp.team_id
                ORDER BY completed_at DESC
                LIMIT 1
            ) AS ccl ON TRUE

         WHERE cp.campaign_id = ?
           AND cp.team_id = ?




          AND cp.active = true
          AND cp.to_check = false
          AND NOT pe.invalid_email

          AND (
           cp.unpaused_by_admin = true

           OR (


            -- email-specific checks to stop campaign

            cp.bounced = false AND

            cp.replied = false
            AND cp.opted_out = false
            AND cp.out_of_office = false
            AND cp.auto_reply = false
            AND cp.completed = false
            AND cp.paused = false
            AND cp.will_resume_at is null

            -- TODO: linkedin-specific checks to stop campaign - ideally any such case should result in cp.completed to be true

          )
        )


          AND (

            CASE
             WHEN prospects.timezone is not null and prospects.timezone != '' THEN prospects.timezone IN (?)
             ELSE ca.timezone IN (?)
            END
          )

          AND prospects.prospect_category_id_custom != ?






            AND cp.current_step_id IS NOT NULL  AND (     (
                      cp.current_step_id = ?
                      )
                      AND (
                        (
                          cp.current_step_status_for_scheduler = ?
                          AND
                          date_trunc('day', cp.latest_task_done_or_skipped_at AT  TIME ZONE (CASE WHEN (prospects.timezone IS NOT NULL AND prospects.timezone != '') THEN prospects.timezone ELSE ca.timezone END) ) <  ((now() AT  TIME ZONE (CASE WHEN (prospects.timezone IS NOT NULL AND prospects.timezone != '') THEN prospects.timezone ELSE ca.timezone END) ) - interval '97' second)
                        )
                        OR
                        (
                          cp.current_step_status_for_scheduler = ?
                          AND
                          date_trunc('day', cp.latest_task_done_or_skipped_at AT  TIME ZONE (CASE WHEN (prospects.timezone IS NOT NULL AND prospects.timezone != '') THEN prospects.timezone ELSE ca.timezone END) ) <  ((now() AT  TIME ZONE (CASE WHEN (prospects.timezone IS NOT NULL AND prospects.timezone != '') THEN prospects.timezone ELSE ca.timezone END) ) - interval '97' second)
                        )
                        OR
                        (
                          cp.current_step_status_for_scheduler = ?
                        )
                        OR
                        cp.current_step_status_for_scheduler IS null
                       )
                        OR    (
                      cp.current_step_id = ?
                      )
                      AND (
                        (
                          cp.current_step_status_for_scheduler = ?
                          AND
                          date_trunc('day', cp.latest_task_done_or_skipped_at AT  TIME ZONE (CASE WHEN (prospects.timezone IS NOT NULL AND prospects.timezone != '') THEN prospects.timezone ELSE ca.timezone END) ) <  ((now() AT  TIME ZONE (CASE WHEN (prospects.timezone IS NOT NULL AND prospects.timezone != '') THEN prospects.timezone ELSE ca.timezone END) ) - interval '97' second)
                        )
                        OR
                        (
                          cp.current_step_status_for_scheduler = ?
                          AND
                          date_trunc('day', cp.latest_task_done_or_skipped_at AT  TIME ZONE (CASE WHEN (prospects.timezone IS NOT NULL AND prospects.timezone != '') THEN prospects.timezone ELSE ca.timezone END) ) <  ((now() AT  TIME ZONE (CASE WHEN (prospects.timezone IS NOT NULL AND prospects.timezone != '') THEN prospects.timezone ELSE ca.timezone END) ) - interval '97' second)
                        )
                        OR
                        (
                          cp.current_step_status_for_scheduler = ?
                        )
                        OR
                        cp.current_step_status_for_scheduler IS null
                       )
                        ) AND  (cp.latest_task_done_or_skipped_at IS NULL OR cp.latest_task_done_or_skipped_at < now() - interval '10 hours')  AND (
                 cp.current_campaign_email_settings_id = ?
                 OR cp.current_campaign_email_settings_id is NULL
                 )




            AND NOT EXISTS (SELECT
              pdp.id
            FROM
              potential_duplicate_prospect_ids pdp
              INNER JOIN potential_duplicate_prospects_log pdpl ON pdp.potential_duplicate_prospects_log_id = pdpl.id
                AND pdp.team_id = pdpl.team_id
            WHERE
              pdpl.status IN (
                ?,
                ?
                )
              AND pdp.prospect_id = prospects.id
              AND pdp.team_id = ?
              )


          ORDER BY  last_contacted_at ASC, CASE
               WHEN dpd.mx_inbox_provider = ? THEN 0
               ELSE 1
               END,
               prospects.id ASC
          LIMIT ?
              """

      val stepIdAndDelayMap = SchedulerMapStepIdAndDelay(
        is_head_step_in_the_campaign = false,
        currentStepType = CampaignStepType.LinkedinViewProfile,
        nextStepType = CampaignStepType.LinkedinInmail,
        currentStepId = 11L,
        delayTillNextStep = 97
      )

      val qry_str = CampaignProspectDAO.fetchProspectsV3MultichannelQuery(
        channelType = ChannelType.EmailChannel,
        allowedProspectTimezones = List("Asia/Kolkata"),
        campaignId = 3L,
        teamId = TeamId(24L),
        limit = 53,
        doNotContactCategoryId = ProspectCategoryId(7L),
        channelRelevantStepIdAndDelay = Vector(stepIdAndDelayMap, stepIdAndDelayMap),
        newProspectsInCampaign = false,
        firstStepIsMagicContent = false,
        sendOnlyToProspectsWhoWereSentInCurrentCycle = None,
        campaign_email_setting = Some(CampaignEmailSettingForScheduler(
          sender_email_settings_id = 11,
          receiver_email_settings_id = 12,
          campaign_email_settings_id = CampaignEmailSettingsId(13L),
          emailServiceProvider = EmailServiceProvider.OTHER
        )),
        orgId = OrgId(2L),
         //emailNotCompulsoryEnabled = false,
        enable_magic_column = false,
        useModifiedQueryForDripCampaign = false

      )


      assert(TestUtils.compareExpectedAndActualQuery(
        expected = expected,
        actual = qry_str
      ))
    }

    it("should get old prospects sent in current cycle with email_setting_id check") {
      val expected =
        sqls"""
                      SELECT
         -- DISTINCT (prospects.id),

         prospects.id,

          a.id AS owner_id,
          a.email AS owner_email,
          a.uuid AS owner_uuid,
          CONCAT(a.first_name, ' ',  a.last_name) AS owner_name,

          prospects.uuid AS prospect_uuid,

          prospects.team_id,

          prospects.first_name,
          prospects.last_name,
          pe.email,
          pe.email_domain,

          (CASE WHEN pe.email_checked THEN pe.invalid_email ELSE NULL END) AS invalid_email,

          prospect_lists.name AS list,
          prospects.list_id,
          prospects.company,
          prospects.city,
          prospects.country,
          prospects.timezone,

          prospects.created_at,
          prospects.updated_at,
          prospects.last_contacted_at,
          prospects.last_contacted_at_phone,
          prospects.last_replied_at,
          prospects.last_opened_at,
          prospects.total_opens,
          prospects.total_clicks,
          prospects.custom_fields,
          prospects.will_delete,
          pe.email_bounced,
          pe.force_send_invalid_email,
          pcat.name as prospect_category_custom,
          pcat.label_color as prospect_category_label_color,
          pcat.id as prospect_category_id_custom,

          prospects.prospect_source,
          prospects.prospect_account_id as prospect_account_id,
          pa.uuid as prospect_account_uuid,
          pa.name as prospect_account_name,
           null  as tags,
           COALESCE(magic_columns.prospect_magic_columns, '[]')  as magic_columns,

           null  as active_campaigns,

          prospects.state,
          prospects.job_title,
          prospects.phone,
          prospects.phone_2,
          prospects.phone_3,
          prospects.linkedin_url,

          prospects.latest_reply_sentiment_uuid,
          prospects.latest_task_done_at,
          ccl.completed_at as last_call_made_at
    ,

          pe.email_checked,
          pe.email_sent_for_validation,
          pe.email_sent_for_validation_at,

          cp.current_step_id,
          cp.last_scheduled,

          cp.latest_task_done_or_skipped_at,
          cp.current_step_status_for_scheduler,

          pa.id as pa_id,
          pa.uuid as pa_uuid,
          pa.owner_id as pa_owner_id,
          pa.team_id as pa_team_id,
          pa.name as pa_name,
          pa.custom_id as pa_custom_id,
          pa.description as pa_description,
          pa.source as pa_source,
          pa.website as pa_website,
          pa.industry as pa_industry,
          pa.linkedin_url as pa_linkedin_url,
          pa.custom_fields as pa_custom_fields,
          pa.created_at as pa_created_at,
          pa.updated_at as pa_updated_at,
          cp.current_step_type,
          CONCAT(a.first_name, ' ', a.last_name) as pa_owner_name


         FROM campaigns_prospects cp
          INNER JOIN prospects ON (prospects.id = cp.prospect_id AND prospects.team_id = cp.team_id)
          INNER JOIN accounts a ON a.id = prospects.account_id
          INNER JOIN prospect_categories_custom pcat on ( pcat.id = prospects.prospect_category_id_custom AND pcat.team_id = prospects.team_id
              AND pcat.id != ? )
          INNER JOIN campaigns ca ON (ca.id = cp.campaign_id AND ca.team_id = cp.team_id)

          INNER JOIN prospects_emails pe ON ((pe.prospect_id = prospects.id) AND (pe.team_id = prospects.team_id) AND pe.is_primary)
          LEFT JOIN domain_public_dns dpd ON dpd.domain = pe.email_domain

          LEFT JOIN prospect_accounts pa ON (pa.id = prospects.prospect_account_id AND pa.team_id = prospects.team_id)
          LEFT JOIN prospect_lists ON (prospect_lists.id = prospects.list_id AND prospect_lists.team_id = prospects.team_id)
          LEFT JOIN prospects_metadata pm ON pm.prospect_id = prospects.id AND pm.team_id = prospects.team_id


          LEFT JOIN LATERAL (
            SELECT
              json_agg(json_build_object(
               'column_output', cdp.magic_prompt_generated_output,
                'failed_message', cdp.failed_message,
                'column_name', cd.name,
                'status', cdp.status
              )) AS prospect_magic_columns
            FROM
              column_defs_prospects AS cdp
              JOIN column_defs AS cd ON cdp.column_id = cd.id
                AND cd.team_id = cdp.team_id
            WHERE
              cdp.prospect_id = prospects.id
              AND cdp.team_id = prospects.team_id
          ) AS magic_columns ON TRUE


          LEFT JOIN LATERAL (
                SELECT completed_at
                FROM call_conference_logs
                WHERE primary_prospect_id = cp.prospect_id
                  AND team_id = cp.team_id
                ORDER BY completed_at DESC
                LIMIT 1
            ) AS ccl ON TRUE

         WHERE cp.campaign_id = ?
           AND cp.team_id = ?




          AND cp.active = true
          AND cp.to_check = false
          AND NOT pe.invalid_email

          AND (
           cp.unpaused_by_admin = true

           OR (


            -- email-specific checks to stop campaign

            cp.bounced = false AND

            cp.replied = false
            AND cp.opted_out = false
            AND cp.out_of_office = false
            AND cp.auto_reply = false
            AND cp.completed = false
            AND cp.paused = false
            AND cp.will_resume_at is null

            -- TODO: linkedin-specific checks to stop campaign - ideally any such case should result in cp.completed to be true

          )
        )


          AND (

            CASE
             WHEN prospects.timezone is not null and prospects.timezone != '' THEN prospects.timezone IN (?)
             ELSE ca.timezone IN (?)
            END
          )

          AND prospects.prospect_category_id_custom != ?




                    AND (
                      CASE WHEN pm.last_touched_at IS NOT NULL
                      THEN pm.last_touched_at >= ?
                      ELSE (
                        prospects.last_contacted_at IS NOT NULL
                        AND
                        prospects.last_contacted_at >= ?
                      )
                      END
                    )




            AND cp.current_step_id IS NOT NULL  AND (     (
                      cp.current_step_id = ?
                      )
                      AND (
                        (
                          cp.current_step_status_for_scheduler = ?
                          AND
                          date_trunc('day', cp.latest_task_done_or_skipped_at AT  TIME ZONE (CASE WHEN (prospects.timezone IS NOT NULL AND prospects.timezone != '') THEN prospects.timezone ELSE ca.timezone END) ) <  ((now() AT  TIME ZONE (CASE WHEN (prospects.timezone IS NOT NULL AND prospects.timezone != '') THEN prospects.timezone ELSE ca.timezone END) ) - interval '97' second)
                        )
                        OR
                        (
                          cp.current_step_status_for_scheduler = ?
                          AND
                          date_trunc('day', cp.latest_task_done_or_skipped_at AT  TIME ZONE (CASE WHEN (prospects.timezone IS NOT NULL AND prospects.timezone != '') THEN prospects.timezone ELSE ca.timezone END) ) <  ((now() AT  TIME ZONE (CASE WHEN (prospects.timezone IS NOT NULL AND prospects.timezone != '') THEN prospects.timezone ELSE ca.timezone END) ) - interval '97' second)
                        )
                        OR
                        (
                          cp.current_step_status_for_scheduler = ?
                        )
                        OR
                        cp.current_step_status_for_scheduler IS null
                       )
                        OR    (
                      cp.current_step_id = ?
                      )
                      AND (
                        (
                          cp.current_step_status_for_scheduler = ?
                          AND
                          date_trunc('day', cp.latest_task_done_or_skipped_at AT  TIME ZONE (CASE WHEN (prospects.timezone IS NOT NULL AND prospects.timezone != '') THEN prospects.timezone ELSE ca.timezone END) ) <  ((now() AT  TIME ZONE (CASE WHEN (prospects.timezone IS NOT NULL AND prospects.timezone != '') THEN prospects.timezone ELSE ca.timezone END) ) - interval '97' second)
                        )
                        OR
                        (
                          cp.current_step_status_for_scheduler = ?
                          AND
                          date_trunc('day', cp.latest_task_done_or_skipped_at AT  TIME ZONE (CASE WHEN (prospects.timezone IS NOT NULL AND prospects.timezone != '') THEN prospects.timezone ELSE ca.timezone END) ) <  ((now() AT  TIME ZONE (CASE WHEN (prospects.timezone IS NOT NULL AND prospects.timezone != '') THEN prospects.timezone ELSE ca.timezone END) ) - interval '97' second)
                        )
                        OR
                        (
                          cp.current_step_status_for_scheduler = ?
                        )
                        OR
                        cp.current_step_status_for_scheduler IS null
                       )
                        ) AND  (cp.latest_task_done_or_skipped_at IS NULL OR cp.latest_task_done_or_skipped_at < now() - interval '10 hours')  AND (
                 cp.current_campaign_email_settings_id = ?
                 OR cp.current_campaign_email_settings_id is NULL
                 )




            AND NOT EXISTS (SELECT
              pdp.id
            FROM
              potential_duplicate_prospect_ids pdp
              INNER JOIN potential_duplicate_prospects_log pdpl ON pdp.potential_duplicate_prospects_log_id = pdpl.id
                AND pdp.team_id = pdpl.team_id
            WHERE
              pdpl.status IN (
                ?,
                ?
                )
              AND pdp.prospect_id = prospects.id
              AND pdp.team_id = ?
              )


          ORDER BY  last_contacted_at ASC, CASE
               WHEN dpd.mx_inbox_provider = ? THEN 0
               ELSE 1
               END,
               prospects.id ASC
          LIMIT ?
              """

      val stepIdAndDelayMap = SchedulerMapStepIdAndDelay(
        is_head_step_in_the_campaign = false,
        currentStepType = CampaignStepType.LinkedinViewProfile,
        nextStepType = CampaignStepType.LinkedinInmail,
        currentStepId = 11L,
        delayTillNextStep = 97
      )

      val qry_str = CampaignProspectDAO.fetchProspectsV3MultichannelQuery(
        channelType = ChannelType.EmailChannel,
        allowedProspectTimezones = List("Asia/Kolkata"),
        campaignId = 3L,
        teamId = TeamId(24L),
        limit = 53,
        doNotContactCategoryId = ProspectCategoryId(7L),
        channelRelevantStepIdAndDelay = Vector(stepIdAndDelayMap, stepIdAndDelayMap),
        newProspectsInCampaign = false,
        firstStepIsMagicContent = false,
        sendOnlyToProspectsWhoWereSentInCurrentCycle = Some(DateTime.now()),
        campaign_email_setting = Some(CampaignEmailSettingForScheduler(
          sender_email_settings_id = 11,
          receiver_email_settings_id = 12,
          campaign_email_settings_id = CampaignEmailSettingsId(13L),
          emailServiceProvider = EmailServiceProvider.OTHER
        )),
        orgId = OrgId(2L),
         //emailNotCompulsoryEnabled = false,
        enable_magic_column = false,
        useModifiedQueryForDripCampaign = false

      )

      assert(TestUtils.compareExpectedAndActualQuery(
        expected = expected,
        actual = qry_str
      ))
    }

    it("should get old prospects sent in current cycle for prospect-email-optional flow") {
      val expected =
        sqls"""
              SELECT
         -- DISTINCT (prospects.id),

         prospects.id,

          a.id AS owner_id,
          a.email AS owner_email,
          a.uuid AS owner_uuid,
          CONCAT(a.first_name, ' ',  a.last_name) AS owner_name,

          prospects.uuid AS prospect_uuid,

          prospects.team_id,

          prospects.first_name,
          prospects.last_name,
          pe.email,
          pe.email_domain,

          (CASE WHEN pe.email_checked THEN pe.invalid_email ELSE NULL END) AS invalid_email,

          prospect_lists.name AS list,
          prospects.list_id,
          prospects.company,
          prospects.city,
          prospects.country,
          prospects.timezone,

          prospects.created_at,
          prospects.updated_at,
          prospects.last_contacted_at,
          prospects.last_contacted_at_phone,
          prospects.last_replied_at,
          prospects.last_opened_at,
          prospects.total_opens,
          prospects.total_clicks,
          prospects.custom_fields,
          prospects.will_delete,
          pe.email_bounced,
          pe.force_send_invalid_email,
          pcat.name as prospect_category_custom,
          pcat.label_color as prospect_category_label_color,
          pcat.id as prospect_category_id_custom,

          prospects.prospect_source,
          prospects.prospect_account_id as prospect_account_id,
          pa.uuid as prospect_account_uuid,
          pa.name as prospect_account_name,
          null as tags,

          COALESCE(magic_columns.prospect_magic_columns, '[]')  as magic_columns,

          null as active_campaigns,

          prospects.state,
          prospects.job_title,
          prospects.phone,
          prospects.phone_2,
          prospects.phone_3,
          prospects.linkedin_url,
          prospects.latest_reply_sentiment_uuid,
          prospects.latest_task_done_at,
          ccl.completed_at as last_call_made_at ,

          pe.email_checked,
          pe.email_sent_for_validation,
          pe.email_sent_for_validation_at,

          cp.current_step_id,
          cp.last_scheduled,
          
          cp.latest_task_done_or_skipped_at,
          cp.current_step_status_for_scheduler,

          pa.id as pa_id,
          pa.uuid as pa_uuid,
          pa.owner_id as pa_owner_id,
          pa.team_id as pa_team_id,
          pa.name as pa_name,
          pa.custom_id as pa_custom_id,
          pa.description as pa_description,
          pa.source as pa_source,
          pa.website as pa_website,
          pa.industry as pa_industry,
          pa.linkedin_url as pa_linkedin_url,
          pa.custom_fields as pa_custom_fields,
          pa.created_at as pa_created_at,
          pa.updated_at as pa_updated_at,
          cp.current_step_type,
          CONCAT(a.first_name, ' ', a.last_name) as pa_owner_name


         FROM campaigns_prospects cp
          INNER JOIN prospects ON (prospects.id = cp.prospect_id AND prospects.team_id = cp.team_id)

          INNER JOIN accounts a ON a.id = prospects.account_id
          INNER JOIN prospect_categories_custom pcat on ( pcat.id = prospects.prospect_category_id_custom AND pcat.team_id = prospects.team_id
              AND pcat.id != ? )
          INNER JOIN campaigns ca ON (ca.id = cp.campaign_id AND ca.team_id = cp.team_id)

          INNER JOIN prospects_emails pe ON ((pe.prospect_id = prospects.id) AND (pe.team_id = prospects.team_id) AND pe.is_primary)
          LEFT JOIN domain_public_dns dpd ON dpd.domain = pe.email_domain
          LEFT JOIN prospect_accounts pa ON (pa.id = prospects.prospect_account_id AND pa.team_id = prospects.team_id)
          LEFT JOIN prospect_lists ON (prospect_lists.id = prospects.list_id AND prospect_lists.team_id = prospects.team_id)
          LEFT JOIN prospects_metadata pm ON pm.prospect_id = prospects.id AND pm.team_id = prospects.team_id

          LEFT JOIN LATERAL (
            SELECT
              json_agg(json_build_object(
                        'column_output', cdp.magic_prompt_generated_output,
                        'failed_message', cdp.failed_message,
                        'column_name', cd.name,
                        'status', cdp.status
                      )) AS prospect_magic_columns
            FROM
              column_defs_prospects AS cdp
              JOIN column_defs AS cd ON cdp.column_id = cd.id
              AND cd.team_id = cdp.team_id
            WHERE
              cdp.prospect_id = prospects.id
              AND cdp.team_id = prospects.team_id
          ) AS magic_columns ON TRUE

          LEFT JOIN LATERAL (
                SELECT completed_at
                FROM call_conference_logs
                WHERE primary_prospect_id = cp.prospect_id
                  AND team_id = cp.team_id
                ORDER BY completed_at DESC
                LIMIT 1
            ) AS ccl ON TRUE

         WHERE cp.campaign_id = ?
           AND cp.team_id = ?

          AND cp.active = true
          AND cp.to_check = false
          AND NOT pe.invalid_email

          AND (
           cp.unpaused_by_admin = true

           OR (

            -- email-specific checks to stop campaign
            cp.bounced = false AND
            cp.replied = false
            AND cp.opted_out = false
            AND cp.out_of_office = false
            AND cp.auto_reply = false
            AND cp.completed = false
            AND cp.paused = false
            AND cp.will_resume_at is null

            -- TODO: linkedin-specific checks to stop campaign - ideally any such case should result in cp.completed to be true

          )
        )


          AND (

            CASE
             WHEN prospects.timezone is not null and prospects.timezone != '' THEN prospects.timezone IN (?)
             ELSE ca.timezone IN (?)
            END
          )

          AND prospects.prospect_category_id_custom != ?

          AND (
                      CASE WHEN pm.last_touched_at IS NOT NULL
                      THEN pm.last_touched_at >= ?
                      ELSE (
                        prospects.last_contacted_at IS NOT NULL
                        AND
                        prospects.last_contacted_at >= ?
                      )
                      END
                    )

          AND cp.current_step_id IS NOT NULL

          AND (
          (
            cp.current_step_id = ?
            )
            AND (
              (
                cp.current_step_status_for_scheduler = ?
                AND
                date_trunc('day', cp.latest_task_done_or_skipped_at AT TIME ZONE (CASE WHEN (prospects.timezone IS NOT NULL AND prospects.timezone != '') THEN prospects.timezone ELSE ca.timezone END) ) <  ((now() AT TIME ZONE (CASE WHEN (prospects.timezone IS NOT NULL AND prospects.timezone != '') THEN prospects.timezone ELSE ca.timezone END) ) - interval '97' second)
              )
              OR
              (
                cp.current_step_status_for_scheduler = ?
                AND
                date_trunc('day', cp.latest_task_done_or_skipped_at AT TIME ZONE (CASE WHEN (prospects.timezone IS NOT NULL AND prospects.timezone != '') THEN prospects.timezone ELSE ca.timezone END) ) <  ((now() AT TIME ZONE (CASE WHEN (prospects.timezone IS NOT NULL AND prospects.timezone != '') THEN prospects.timezone ELSE ca.timezone END) ) - interval '97' second)
              )
              OR
              (
                cp.current_step_status_for_scheduler = ?
              )
              OR
              cp.current_step_status_for_scheduler IS null
             )
             OR
             (
            cp.current_step_id = ?
            )
            AND (
              (
                cp.current_step_status_for_scheduler = ?
                AND
                date_trunc('day', cp.latest_task_done_or_skipped_at AT TIME ZONE (CASE WHEN (prospects.timezone IS NOT NULL AND prospects.timezone != '') THEN prospects.timezone ELSE ca.timezone END) ) <  ((now() AT TIME ZONE (CASE WHEN (prospects.timezone IS NOT NULL AND prospects.timezone != '') THEN prospects.timezone ELSE ca.timezone END) ) - interval '97' second)
              )
              OR
              (
                cp.current_step_status_for_scheduler = ?
                AND
                date_trunc('day', cp.latest_task_done_or_skipped_at AT TIME ZONE (CASE WHEN (prospects.timezone IS NOT NULL AND prospects.timezone != '') THEN prospects.timezone ELSE ca.timezone END) ) <  ((now() AT TIME ZONE (CASE WHEN (prospects.timezone IS NOT NULL AND prospects.timezone != '') THEN prospects.timezone ELSE ca.timezone END) ) - interval '97' second)
              )
              OR
              (
                cp.current_step_status_for_scheduler = ?
              )
              OR
              cp.current_step_status_for_scheduler IS null
             )
            )

            AND (cp.latest_task_done_or_skipped_at IS NULL OR cp.latest_task_done_or_skipped_at < now() - interval '10 hours')

            AND (
                 cp.current_campaign_email_settings_id = ?
                 OR cp.current_campaign_email_settings_id is NULL
                 )

            AND NOT EXISTS (SELECT
              pdp.id
            FROM
              potential_duplicate_prospect_ids pdp
              INNER JOIN potential_duplicate_prospects_log pdpl ON pdp.potential_duplicate_prospects_log_id = pdpl.id
                AND pdp.team_id = pdpl.team_id
            WHERE
              pdpl.status IN (
                ?,
                ?
                )
              AND pdp.prospect_id = prospects.id
              AND pdp.team_id = ?
              )

          ORDER BY last_contacted_at ASC, CASE WHEN dpd.mx_inbox_provider = ? THEN 0 ELSE 1 END, prospects.id ASC
          LIMIT ?
              """

      val stepIdAndDelayMap = SchedulerMapStepIdAndDelay(
        is_head_step_in_the_campaign = false,
        currentStepType = CampaignStepType.LinkedinViewProfile,
        nextStepType = CampaignStepType.LinkedinInmail,
        currentStepId = 11L,
        delayTillNextStep = 97
      )

      val qry_str = CampaignProspectDAO.fetchProspectsV3MultichannelQuery(
        channelType = ChannelType.EmailChannel,
        allowedProspectTimezones = List("Asia/Kolkata"),
        campaignId = 3L,
        teamId = TeamId(5L),
        limit = 53,
        doNotContactCategoryId = ProspectCategoryId(7L),
        channelRelevantStepIdAndDelay = Vector(stepIdAndDelayMap, stepIdAndDelayMap),
        newProspectsInCampaign = false,
        firstStepIsMagicContent = false,
        sendOnlyToProspectsWhoWereSentInCurrentCycle = Some(DateTime.now()),
        orgId = OrgId(70L),
        campaign_email_setting = Some(CampaignEmailSettingForScheduler(
          sender_email_settings_id = 13,
          receiver_email_settings_id = 12,
          campaign_email_settings_id = CampaignEmailSettingsId(13L),
          emailServiceProvider = EmailServiceProvider.OTHER
        )),
         //emailNotCompulsoryEnabled = false,
        enable_magic_column = false,
        useModifiedQueryForDripCampaign = false

      )

      assert(TestUtils.compareExpectedAndActualQuery(
        expected = expected,
        actual = qry_str
      ))
    }

    it("should schedule email not compulsory prospects") {
      val expected =
        sqls"""
              SELECT
         -- DISTINCT (prospects.id),

         prospects.id,

          a.id AS owner_id,
          a.email AS owner_email,
          a.uuid AS owner_uuid,
          CONCAT(a.first_name, ' ',  a.last_name) AS owner_name,

          prospects.uuid AS prospect_uuid,

          prospects.team_id,

          prospects.first_name,
          prospects.last_name,
          pe.email,
          pe.email_domain,

          (CASE WHEN pe.email_checked THEN pe.invalid_email ELSE NULL END) AS invalid_email,

          prospect_lists.name AS list,
          prospects.list_id,
          prospects.company,
          prospects.city,
          prospects.country,
          prospects.timezone,

          prospects.created_at,
          prospects.updated_at,
          prospects.last_contacted_at,
          prospects.last_contacted_at_phone,
          prospects.last_replied_at,
          prospects.last_opened_at,
          prospects.total_opens,
          prospects.total_clicks,
          prospects.custom_fields,
          prospects.will_delete,
          pe.email_bounced,
          pe.force_send_invalid_email,
          pcat.name as prospect_category_custom,
          pcat.label_color as prospect_category_label_color,
          pcat.id as prospect_category_id_custom,

          prospects.prospect_source,
          prospects.prospect_account_id as prospect_account_id,
          pa.uuid as prospect_account_uuid,
          pa.name as prospect_account_name,
          null as tags,

          COALESCE(magic_columns.prospect_magic_columns, '[]')  as magic_columns,

          null as active_campaigns,

          prospects.state,
          prospects.job_title,
          prospects.phone,
          prospects.phone_2,
          prospects.phone_3,
          prospects.linkedin_url,
          prospects.latest_reply_sentiment_uuid,
          prospects.latest_task_done_at,
          ccl.completed_at as last_call_made_at ,

          pe.email_checked,
          pe.email_sent_for_validation,
          pe.email_sent_for_validation_at,

          cp.current_step_id,
          cp.last_scheduled,

          cp.latest_task_done_or_skipped_at,
          cp.current_step_status_for_scheduler,

          pa.id as pa_id,
          pa.uuid as pa_uuid,
          pa.owner_id as pa_owner_id,
          pa.team_id as pa_team_id,
          pa.name as pa_name,
          pa.custom_id as pa_custom_id,
          pa.description as pa_description,
          pa.source as pa_source,
          pa.website as pa_website,
          pa.industry as pa_industry,
          pa.linkedin_url as pa_linkedin_url,
          pa.custom_fields as pa_custom_fields,
          pa.created_at as pa_created_at,
          pa.updated_at as pa_updated_at,
          cp.current_step_type,
          CONCAT(a.first_name, ' ', a.last_name) as pa_owner_name


         FROM campaigns_prospects cp
          INNER JOIN prospects ON (prospects.id = cp.prospect_id AND prospects.team_id = cp.team_id)
          INNER JOIN accounts a ON a.id = prospects.account_id
          INNER JOIN prospect_categories_custom pcat on ( pcat.id = prospects.prospect_category_id_custom AND pcat.team_id = prospects.team_id
              AND pcat.id != ? )
          INNER JOIN campaigns ca ON (ca.id = cp.campaign_id AND ca.team_id = cp.team_id)
          LEFT JOIN prospects_emails pe ON ((pe.prospect_id = prospects.id) AND (pe.team_id = prospects.team_id) AND pe.is_primary)
          LEFT JOIN prospect_accounts pa ON (pa.id = prospects.prospect_account_id AND pa.team_id = prospects.team_id)
          LEFT JOIN prospect_lists ON (prospect_lists.id = prospects.list_id AND prospect_lists.team_id = prospects.team_id)

          LEFT JOIN LATERAL (
            SELECT
              json_agg(json_build_object(
                       'column_output', cdp.magic_prompt_generated_output,
                        'failed_message', cdp.failed_message,
                        'column_name', cd.name,
                        'status', cdp.status
                      )) AS prospect_magic_columns
            FROM
              column_defs_prospects AS cdp
              JOIN column_defs AS cd ON cdp.column_id = cd.id
              AND cd.team_id = cdp.team_id
            WHERE
              cdp.prospect_id = prospects.id
              AND cdp.team_id = prospects.team_id
          ) AS magic_columns ON TRUE


         LEFT JOIN LATERAL (
                SELECT completed_at
                FROM call_conference_logs
                WHERE primary_prospect_id = cp.prospect_id
                  AND team_id = cp.team_id
                ORDER BY completed_at DESC
                LIMIT 1
            ) AS ccl ON TRUE

         WHERE cp.campaign_id = ?
           AND cp.team_id = ?

          AND cp.active = true
          AND cp.to_check = false

          AND (
           cp.unpaused_by_admin = true

           OR (

            -- email-specific checks to stop campaign
            cp.bounced = false AND
            cp.replied = false
            AND cp.opted_out = false
            AND cp.out_of_office = false
            AND cp.auto_reply = false
            AND cp.completed = false
            AND cp.paused = false
            AND cp.will_resume_at is null

            -- TODO: linkedin-specific checks to stop campaign - ideally any such case should result in cp.completed to be true

          )
        )


          AND (

            CASE
             WHEN prospects.timezone is not null and prospects.timezone != '' THEN prospects.timezone IN (?)
             ELSE ca.timezone IN (?)
            END
          )

          AND prospects.prospect_category_id_custom != ?

          AND cp.current_step_id IS NOT NULL

          AND (
          (
            cp.current_step_id = ?
            )
            AND (
              (
                cp.current_step_status_for_scheduler = ?
                AND
                date_trunc('day', cp.latest_task_done_or_skipped_at AT TIME ZONE (CASE WHEN (prospects.timezone IS NOT NULL AND prospects.timezone != '') THEN prospects.timezone ELSE ca.timezone END) ) <  ((now() AT TIME ZONE (CASE WHEN (prospects.timezone IS NOT NULL AND prospects.timezone != '') THEN prospects.timezone ELSE ca.timezone END) ) - interval '97' second)
              )
              OR
              (
                cp.current_step_status_for_scheduler = ?
                AND
                date_trunc('day', cp.latest_task_done_or_skipped_at AT TIME ZONE (CASE WHEN (prospects.timezone IS NOT NULL AND prospects.timezone != '') THEN prospects.timezone ELSE ca.timezone END) ) <  ((now() AT TIME ZONE (CASE WHEN (prospects.timezone IS NOT NULL AND prospects.timezone != '') THEN prospects.timezone ELSE ca.timezone END) ) - interval '97' second)
              )
                OR
                (
                cp.current_step_status_for_scheduler = ?
                )
              OR
              cp.current_step_status_for_scheduler IS null
             )
             OR
             (
            cp.current_step_id = ?
            )
            AND (
              (
                cp.current_step_status_for_scheduler = ?
                AND
                date_trunc('day', cp.latest_task_done_or_skipped_at AT TIME ZONE (CASE WHEN (prospects.timezone IS NOT NULL AND prospects.timezone != '') THEN prospects.timezone ELSE ca.timezone END) ) <  ((now() AT TIME ZONE (CASE WHEN (prospects.timezone IS NOT NULL AND prospects.timezone != '') THEN prospects.timezone ELSE ca.timezone END) ) - interval '97' second)
              )
              OR
              (
                cp.current_step_status_for_scheduler = ?
                AND
                date_trunc('day', cp.latest_task_done_or_skipped_at AT TIME ZONE (CASE WHEN (prospects.timezone IS NOT NULL AND prospects.timezone != '') THEN prospects.timezone ELSE ca.timezone END) ) <  ((now() AT TIME ZONE (CASE WHEN (prospects.timezone IS NOT NULL AND prospects.timezone != '') THEN prospects.timezone ELSE ca.timezone END) ) - interval '97' second)
              )
              OR
              (
                cp.current_step_status_for_scheduler = ?
              )
              OR
              cp.current_step_status_for_scheduler IS null
             )
            )

            AND (cp.latest_task_done_or_skipped_at IS NULL OR cp.latest_task_done_or_skipped_at < now() - interval '10 hours')

          AND NOT EXISTS (SELECT pdp.id FROM potential_duplicate_prospect_ids pdp
            INNER JOIN potential_duplicate_prospects_log pdpl
              ON pdp.potential_duplicate_prospects_log_id = pdpl.id
              AND pdp.team_id = pdpl.team_id WHERE pdpl.status IN ( ?, ? )
              AND pdp.prospect_id = prospects.id AND pdp.team_id = ?
          )

          ORDER BY last_contacted_at ASC, prospects.id ASC
          LIMIT ?
              """

      val stepIdAndDelayMap = SchedulerMapStepIdAndDelay(
        is_head_step_in_the_campaign = false,
        currentStepType = CampaignStepType.LinkedinViewProfile,
        nextStepType = CampaignStepType.LinkedinInmail,
        currentStepId = 11L,
        delayTillNextStep = 97
      )

      val qry_str = CampaignProspectDAO.fetchProspectsV3MultichannelQuery(
        channelType = ChannelType.LinkedinChannel,
        allowedProspectTimezones = List("Asia/Kolkata"),
        campaignId = 3L,
        teamId = TeamId(24L),
        limit = 53,
        doNotContactCategoryId = ProspectCategoryId(7L),
        channelRelevantStepIdAndDelay = Vector(stepIdAndDelayMap, stepIdAndDelayMap),
        newProspectsInCampaign = false,
        firstStepIsMagicContent = false,
        sendOnlyToProspectsWhoWereSentInCurrentCycle = None,
        campaign_email_setting = None,
        orgId = OrgId(2L),
         //emailNotCompulsoryEnabled = true,
        enable_magic_column = false,
        useModifiedQueryForDripCampaign = false

      )

      assert(TestUtils.compareExpectedAndActualQuery(
        expected = expected,
        actual = qry_str
      ))
    }


    it("should give query for drip ") {
      val expected =
        sqls"""
             SELECT
         -- DISTINCT (prospects.id),

         prospects.id,

          a.id AS owner_id,
          a.email AS owner_email,
          a.uuid AS owner_uuid,
          CONCAT(a.first_name, ' ',  a.last_name) AS owner_name,

          prospects.uuid AS prospect_uuid,

          prospects.team_id,

          prospects.first_name,
          prospects.last_name,
          pe.email,
          pe.email_domain,

          (CASE WHEN pe.email_checked THEN pe.invalid_email ELSE NULL END) AS invalid_email,

          prospect_lists.name AS list,
          prospects.list_id,
          prospects.company,
          prospects.city,
          prospects.country,
          prospects.timezone,

          prospects.created_at,
          prospects.updated_at,
          prospects.last_contacted_at,
          prospects.last_contacted_at_phone,
          prospects.last_replied_at,
          prospects.last_opened_at,
          prospects.total_opens,
          prospects.total_clicks,
          prospects.custom_fields,
          prospects.will_delete,
          pe.email_bounced,
          pe.force_send_invalid_email,
          pcat.name as prospect_category_custom,
          pcat.label_color as prospect_category_label_color,
          pcat.id as prospect_category_id_custom,

          prospects.prospect_source,
          prospects.prospect_account_id as prospect_account_id,
          pa.uuid as prospect_account_uuid,
          pa.name as prospect_account_name,
           null  as tags,

           COALESCE(magic_columns.prospect_magic_columns, '[]')  as magic_columns,

           null  as active_campaigns,

          prospects.state,
          prospects.job_title,
          prospects.phone,
          prospects.phone_2,
          prospects.phone_3,
          prospects.linkedin_url,

          prospects.latest_reply_sentiment_uuid,
          prospects.latest_task_done_at,
          ccl.completed_at as last_call_made_at
    ,

          pe.email_checked,
          pe.email_sent_for_validation,
          pe.email_sent_for_validation_at,

          cp.current_step_id,
          cp.last_scheduled,

          cp.latest_task_done_or_skipped_at,
          cp.current_step_status_for_scheduler,

          pa.id as pa_id,
          pa.uuid as pa_uuid,
          pa.owner_id as pa_owner_id,
          pa.team_id as pa_team_id,
          pa.name as pa_name,
          pa.custom_id as pa_custom_id,
          pa.description as pa_description,
          pa.source as pa_source,
          pa.website as pa_website,
          pa.industry as pa_industry,
          pa.linkedin_url as pa_linkedin_url,
          pa.custom_fields as pa_custom_fields,
          pa.created_at as pa_created_at,
          pa.updated_at as pa_updated_at,
          cp.current_step_type,
          CONCAT(a.first_name, ' ', a.last_name) as pa_owner_name


         FROM campaigns_prospects cp
          INNER JOIN prospects ON (prospects.id = cp.prospect_id AND prospects.team_id = cp.team_id)
          INNER JOIN accounts a ON a.id = prospects.account_id
          INNER JOIN prospect_categories_custom pcat on ( pcat.id = prospects.prospect_category_id_custom AND pcat.team_id = prospects.team_id
              AND pcat.id != ? )
          INNER JOIN campaigns ca ON (ca.id = cp.campaign_id AND ca.team_id = cp.team_id)
           LEFT JOIN prospects_emails pe ON ((pe.prospect_id = prospects.id) AND (pe.team_id = prospects.team_id) AND pe.is_primary)
          LEFT JOIN prospect_accounts pa ON (pa.id = prospects.prospect_account_id AND pa.team_id = prospects.team_id)
          LEFT JOIN prospect_lists ON (prospect_lists.id = prospects.list_id AND prospect_lists.team_id = prospects.team_id)

          LEFT JOIN LATERAL (
            SELECT
              json_agg(json_build_object(
                       'column_output', cdp.magic_prompt_generated_output,
                        'failed_message', cdp.failed_message,
                        'column_name', cd.name,
                        'status', cdp.status
                      )) AS prospect_magic_columns
            FROM
              column_defs_prospects AS cdp
              JOIN column_defs AS cd ON cdp.column_id = cd.id
              AND cd.team_id = cdp.team_id
            WHERE
              cdp.prospect_id = prospects.id
              AND cdp.team_id = prospects.team_id
          ) AS magic_columns ON TRUE


         LEFT JOIN LATERAL (
                SELECT completed_at
                FROM call_conference_logs
                WHERE primary_prospect_id = cp.prospect_id
                  AND team_id = cp.team_id
                ORDER BY completed_at DESC
                LIMIT 1
            ) AS ccl ON TRUE

         WHERE cp.campaign_id = ?
           AND cp.team_id = ?




          AND cp.active = true
          AND cp.to_check = false


          AND (
           cp.unpaused_by_admin = true

           OR (

            -- email-specific checks to stop campaign
            cp.replied = false
            AND cp.opted_out = false
            AND cp.out_of_office = false
            AND cp.auto_reply = false
            AND cp.completed = false
            AND cp.paused = false
            AND cp.will_resume_at is null

            -- TODO: linkedin-specific checks to stop campaign - ideally any such case should result in cp.completed to be true

          )
        )


          AND (

            CASE
             WHEN prospects.timezone is not null and prospects.timezone != '' THEN prospects.timezone IN (?)
             ELSE ca.timezone IN (?)
            END
          )

          AND prospects.prospect_category_id_custom != ?

           AND (cp.next_check_for_scheduling_at IS NULL OR cp.next_check_for_scheduling_at < now())

          AND cp.current_step_id IS NOT NULL
            AND (
            cp.current_step_status_for_scheduler IS NULL
            OR
            cp.current_step_status_for_scheduler = ?
            OR
            cp.current_step_status_for_scheduler = ?
            OR
            cp.current_step_status_for_scheduler = ?
            ) AND  (cp.latest_task_done_or_skipped_at IS NULL OR cp.latest_task_done_or_skipped_at < now() - interval '5 minutes')


            AND NOT EXISTS (SELECT
              pdp.id
            FROM
              potential_duplicate_prospect_ids pdp
              INNER JOIN potential_duplicate_prospects_log pdpl ON pdp.potential_duplicate_prospects_log_id = pdpl.id
                AND pdp.team_id = pdpl.team_id
            WHERE
              pdpl.status IN (
                ?,
                ?
                )
              AND pdp.prospect_id = prospects.id
              AND pdp.team_id = ?
              )


          ORDER BY  last_contacted_at ASC,  prospects.id ASC
          LIMIT ?
           """

      val stepIdAndDelayMap = SchedulerMapStepIdAndDelay(
        is_head_step_in_the_campaign = false,
        currentStepType = CampaignStepType.LinkedinViewProfile,
        nextStepType = CampaignStepType.LinkedinInmail,
        currentStepId = 11L,
        delayTillNextStep = 97
      )

      val qry_str = CampaignProspectDAO.fetchProspectsV3MultichannelQuery(
        channelType = ChannelType.LinkedinChannel,
        allowedProspectTimezones = List("Asia/Kolkata"),
        campaignId = 3L,
        teamId = TeamId(24L),
        limit = 53,
        doNotContactCategoryId = ProspectCategoryId(7L),
        channelRelevantStepIdAndDelay = Vector(stepIdAndDelayMap, stepIdAndDelayMap),
        newProspectsInCampaign = false,
        firstStepIsMagicContent = false,
        useModifiedQueryForDripCampaign = true,
        sendOnlyToProspectsWhoWereSentInCurrentCycle = None,
        campaign_email_setting = None,
        orgId = OrgId(2L),
        enable_magic_column = true,
         //emailNotCompulsoryEnabled = true

      )

      assert(TestUtils.compareExpectedAndActualQuery(
        expected = expected,
        actual = qry_str
      ))
    }

  }

}
