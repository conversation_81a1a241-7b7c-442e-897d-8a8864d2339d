package app.api.campaign

import org.apache.pekko.actor.ActorSystem
import org.apache.pekko.stream.Materializer
import api.APIErrorResponse.ErrorResponseProspectsAssignApi
import api.accounts.email.models.EmailServiceProvider
import api.{AppConfig, CacheServiceJedis}
import api.accounts.service.ResetUserCacheUtil
import api.campaigns.{CPAssignResult, Campaign, CampaignBasicInfo, CampaignCreateForm, CampaignDAO, CampaignEmailSettings, CampaignEmailSettingsUuid, CampaignIdAndTeamId, CampaignProspectDAO, CampaignSendReportsDAO, CampaignSettings, CampaignStepDAO, CampaignStepVariantDAO, CampaignStepWithChildren, CampaignWithStatsAndEmail, ChannelSettingUuid, IsUpdate, MaxEmailsPerDay, StuckCampaign, UnsentProspectsCounts}
import api.campaigns.models.{CampaignEmailSettingsId, CampaignSetNextToBeScheduledAtData, CampaignStepType, CampaignType, CampaignTypeData, FindCampaignResultPublicApi, FindCampaignResultPublicApiV3, FindCampaignsListingApiV3Response, IgnoreProspectsInOtherCampaigns, PreviousFollowUpData, SenderDataForSendingLimitNewFlow, SenderRotationStats}
import api.campaigns.{CPAssignResult, Campaign, CampaignBasicInfo, CampaignCreateForm, CampaignDAO, CampaignEmailSettings, CampaignEmailSettingsUuid, CampaignIdAndTeamId, CampaignProspectDAO, CampaignSendReportsDAO, CampaignSettings, CampaignStepDAO, CampaignStepDAOService, CampaignStepVariantDAO, CampaignStepWithChildren, CampaignWithStatsAndEmail, ChannelSettingUuid, IsUpdate, MaxEmailsPerDay, StuckCampaign, UnsentProspectsCounts}
import api.accounts.{Account, AccountAccess, AccountMetadata, AccountService, AccountType, AccountUuid, OrgCountData, OrgMetadata, OrgPlan, OrgSettings, OrganizationRole, OrganizationWithCurrentData, PermType, PermissionLevelForValidation, PermissionOwnershipV2, ProspectCategoriesInDB, ReplyHandling, RolePermV2, RolePermissionDataDAOV2, RolePermissionDataV2, RolePermissionsInDBV2, RolePermissionsV2, TeamAccount, TeamAccountRole, TeamId, TeamMember, TeamMemberLite}
import api.emails.{EmailScheduledDAO, EmailSetting, EmailSettingDAO}
import api.accounts.{Account, AccountAccess, AccountMetadata, AccountService, AccountType, OrgCountData, OrgMetadata, OrgPlan, OrgSettings, OrganizationRole, OrganizationWithCurrentData, PermissionOwnershipV2, ProspectCategoriesInDB, ReplyHandling, RolePermV2, RolePermissionDataV2, RolePermissionsInDBV2, RolePermissionsV2, TeamAccount, TeamAccountRole, TeamMember, TeamMemberLite}
import api.campaigns.services.{AssignProspectsError, AssignProspectsResponse, CallSettingSenderDetails, CampaignCacheService, CampaignCreationError, CampaignDAOService, CampaignId, CampaignPauseReason, CampaignPauseReasonAndCounts, CampaignProspectAssign, CampaignProspectService, CampaignProspectTimezonesJedisService, CampaignSendReportService, CampaignService, ChannelSettingData, ChannelSettingService, ChannelSetupError, DeleteCampaignTasksError, GetCampaignIdFromSrIdentifierError, LinkedinSettingSenderDetails, ProspectAssignErrorType, SearchParams, SmsSettingSenderDetails, UpdateMaxEmailsPerDayError, WhatsappSettingSenderDetails}
import api.prospects.{InferredQueryTimeline, OwnedProspectsForAssigning, ProspectService, ProspectUpdateCategoryTemp, ProspectUuid}
import api.prospects.dao.ProspectAddEventDAO
import api.prospects.dao_service.{ProspectDAOService, ProspectDAOServiceV2}
import api.reports.{AllCampaignStats, ReplySentimentStats}
import api.spammonitor.dao.{EmailSendingStatusDAO, UpdateEmailSendingStatusForm}
import api.spammonitor.model.{EmailSendingEntityTypeData, SendEmailStatusData}
import api.tags.models.{CampaignTag, CampaignTagUuid, ProspectTagUuid}
import api.team_inbox.model.ReplySentimentType
import api.team_inbox.service.ReplySentimentService
import api.triggers.Trigger
import org.joda.time.{DateTime, DateTimeUtils, DateTimeZone}
import org.scalamock.scalatest.AsyncMockFactory
import org.scalatest.funspec.AsyncFunSpec
import sr_scheduler.CampaignStatus
import sr_scheduler.models.{CampaignDataToAddNextToBeScheduledAt, CampaignEmailPriority, CampaignForScheduling, CampaignWarmupSetting, ChannelType, ScheduledProspectsCountForCampaignEmail}
import utils.email.{EmailService, EmailsScheduledDeleteService}
import utils.sr_product_usage_data.services.SrUserFeatureUsageEventService
import utils.{Helpers, SRLogger}
import api.accounts.models.{AccountId, OrgId}
import api.accounts.models.AccountProfileInfo
import api.calendar_app.models.CalendarAccountData
import api.campaigns.DataForCampaignAssignProspects.AssignProspectToCampaignDataV3
import api.campaigns.dao.{CampaignEmailSettingsDAO, CampaignSchedulingMetadataDAO, CampaignSendingVolumeLogsDAO, InternalSchedulerRunLogDAO}
import api.campaigns.models.CampaignTypeData.MultiChannelCampaignData
import api.emails.models.{DeletionReason, EmailSettingUuid, MqDeleteAndRevertDataMsgV2}
import api.emails.services.{EmailScheduledService, EmailSettingJedisService, SelectAndPublishForDeletionService}
import api.prospects.models.{ProspectCategory, ProspectCategoryId, ProspectCategoryRank}
import api.scheduler_report.SchedulerIntegrityService
import api.tasks.services.TaskService
import api.team.TeamUuid
import api.team_inbox.dao_service.ReplySentimentDAOService
import app.test_fixtures.accounts.OrgCountDataFixture
import app.test_fixtures.campaign.CampaignSendReport
import app.test_fixtures.campaign_settings.{CallSettingSenderDetailsFixtures, CampaignEmailSettingsSenderDetailsFixtures, LinkedinSettingSenderDetailsFixtures, SmsSettingSenderDetailsFixtures, WhatsappSettingSenderDetailsFixtures}
import app.test_fixtures.organizationa.{OrgMetadataFixture, OrgPlanFixture}
import eventframework.SrResourceTypes
import io.smartreach.esp.api.emails.EmailSettingId
import play.api.libs.json.{JsError, JsResult, JsSuccess, JsValue, Json, Reads}
import play.api.libs.ws.ahc.AhcWSClient
import scalikejdbc.DBSession
import utils.cronjobs.CampaignSendReportCronService
import utils.dbutils.{DBUtils, DbAndSession}
import utils.timezones.TimezoneUtils
import utils.uuid.{SrId, SrUuid, SrUuidUtils}
import play.api.libs.json.JodaReads.*
import sr_scheduler.models.CampaignForScheduling.{CampaignEmailSettingForScheduler, CampaignForSchedulingEmail}
import sr_scheduler.models.ChannelType.EmailChannel
import utils.GCP.CloudStorage
import utils.cache_utils.model.CampaignUseStatusForEmailSetting
import utils.dependencyinjectionutils.MQCampaignAISequenceGeneratorDI
import utils.email.models.DeleteEmailsScheduledType
import utils.email_notification.service.EmailNotificationService
import utils.jodatimeutils.JodaTimeUtils
import utils.mq.MQCampaignAISequenceGenerator
import utils.mq.channel_scheduler.channels.model.CampaignRejectedForSchedulingReason
import utils.mq.delete_and_revert.MQDeletionAndRevertDataV2
import utils.mq.webhook.mq_activity_trigger.MQActivityTriggerPublisher
import utils.testapp.TestAppExecutionContext
import utils.uuid.services.SrUuidService
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService

import java.util.TimeZone
import scala.concurrent.duration.{Duration, DurationInt, SECONDS}
import scala.concurrent.{Await, ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

class CampaignServiceSpec extends AsyncFunSpec with AsyncMockFactory  {

  val campaignProspectDAO: CampaignProspectDAO = mock[CampaignProspectDAO]
  val prospectDAOService: ProspectDAOService = mock[ProspectDAOService]
  val prospectDAOServiceV2: ProspectDAOServiceV2 = mock[ProspectDAOServiceV2]
  val campaignDAO: CampaignDAO = mock[CampaignDAO]
  val campaignProspectServiceMock : CampaignProspectService = mock[CampaignProspectService]
  val prospectAddEventDAO : ProspectAddEventDAO = mock[ProspectAddEventDAO]
  val emailScheduledDAO: EmailScheduledDAO = mock[EmailScheduledDAO]
  val resetUserCacheUtil: ResetUserCacheUtil = mock[ResetUserCacheUtil]
  val triggerDAO: Trigger = mock[Trigger]
  val campaignStepVariantDAO = mock[CampaignStepVariantDAO]
  val emailSettingDAO = mock[EmailSettingDAO]
  val srUserFeatureUsageEventService: SrUserFeatureUsageEventService = mock[SrUserFeatureUsageEventService]
  val emailSendingStatusDAO = mock[EmailSendingStatusDAO]
  val campaignCacheService = mock[CampaignCacheService]
  val accountService = mock[AccountService]
  val replySentimentDAOService = mock[ReplySentimentDAOService]
  val campaignSendReportsDAO = mock[CampaignSendReportsDAO]
  val taskService = mock[TaskService]
  val cloudStorage = mock[CloudStorage]
  val dbUtils = mock[DBUtils]
  val emailSettingJedisService = mock[EmailSettingJedisService]
  val channelSettingService = mock[ChannelSettingService]
  val campaignStepDAO = mock[CampaignStepDAO]
  val campaignEmailSettingsDAO = mock[CampaignEmailSettingsDAO]
  val campaignSchedulingMetadataDAO = mock[CampaignSchedulingMetadataDAO]
  val schedulerIntegrityService = mock[SchedulerIntegrityService]
  val emailNotificationService = mock[EmailNotificationService]
  val emailScheduledService = mock[EmailScheduledService]
  val mqActivityTriggerPublisher = mock[MQActivityTriggerPublisher]

  val srUuidUtils = mock[SrUuidUtils]

  val campaignDAOService = mock[CampaignDAOService]
  val srUuidService = mock[SrUuidService]
  val campaignSendingVolumeLogsDAO = mock[CampaignSendingVolumeLogsDAO]
  val srRollingUpdateCoreService = mock[SrRollingUpdateCoreService]
  val internalSchedulerRunLogDAO = mock[InternalSchedulerRunLogDAO]
  val selectAndPublishForDeletionService = mock[SelectAndPublishForDeletionService]
  val campaignStepDAOService = mock[CampaignStepDAOService]
  val emailsScheduledDeleteService = mock[EmailsScheduledDeleteService]
  val prospectCategoryTemp = mock[ProspectUpdateCategoryTemp]

  val campaignService = new CampaignService(
    srRollingUpdateCoreService = srRollingUpdateCoreService,
    resetUserCacheUtil = resetUserCacheUtil,
    triggerDAO = triggerDAO,
    emailScheduledDAO = emailScheduledDAO,
    campaignProspectDAO = campaignProspectDAO,
    prospectDAOService = prospectDAOService,
    prospectDAOServiceV2 = prospectDAOServiceV2,
    emailSettingJedisService = emailSettingJedisService,
    campaignDAO = campaignDAO,
    campaignStepVariantDAO = campaignStepVariantDAO,
    emailSettingDAO = emailSettingDAO,
    campaignSendReportsDAO = campaignSendReportsDAO,
    srUserFeatureUsageEventService = srUserFeatureUsageEventService,
    emailSendingStatusDAO = emailSendingStatusDAO,
    campaignCacheService = campaignCacheService,
    accountService = accountService,
    replySentimentDAOService = replySentimentDAOService,
    taskService = taskService,
    dbUtils = dbUtils,
    cloudStorage = cloudStorage,
    channelSettingService = channelSettingService,
    srUuidUtils = srUuidUtils,
    campaignDAOService = campaignDAOService,
    campaignStepDAO = campaignStepDAO,
    campaignSchedulingMetadataDAO = campaignSchedulingMetadataDAO,
    srUuidService = srUuidService,
    campaignSendingVolumeLogsDAO = campaignSendingVolumeLogsDAO,
    internalSchedulerRunLogDAO = internalSchedulerRunLogDAO,
    schedulerIntegrityService = schedulerIntegrityService,
    emailNotificationService = emailNotificationService,
    selectAndPublishForDeletionService = selectAndPublishForDeletionService,
    campaignStepDaoService = campaignStepDAOService,
    mqActivityTriggerPublisher = mqActivityTriggerPublisher,
    emailScheduledService = emailScheduledService,
    prospectUpdateCategoryTemp = prospectCategoryTemp,
    emailsScheduledDeleteService = emailsScheduledDeleteService
  )

  val campaignProspectAssign = new CampaignProspectAssign(
    campaignProspectService = campaignProspectServiceMock,
    campaignCacheService = campaignCacheService
  )

  given Logger: SRLogger = new SRLogger("tests")
  implicit lazy val system: ActorSystem = TestAppExecutionContext.actorSystem
  implicit lazy val actorContext: ExecutionContext = system.dispatcher


  val campaign_id: Long = 121L
  val campaign_name = "CampaignName"
  val permittedAccountIds = Seq(2L,1L)
  val teamId: Long = 37L
  val ownerId: Long = 2L

  val first_name = "Adminfirst"
  val last_name = "Adminlast"
  val company = "CompanyName"
  val email = "<EMAIL>"

  val aDate = DateTime.parse("2022-3-27")



  val profile = AccountProfileInfo(
    first_name = first_name,
    last_name = last_name,
    company = Some(company),
    timezone = None,
    country_code = None,
    mobile_country_code = None,
    mobile_number = None,
      onboarding_phone_number= None,
    twofa_enabled = false,
    has_gauthenticator = false,
    weekly_report_emails = None,
    scheduled_for_deletion_at = None
  )

  val accountMetadata = AccountMetadata(
    // account_ui_version = None,
    is_profile_onboarding_done = None
  )

  val orgMetadata = OrgMetadataFixture.orgMetadataFixture2

  val orgCountData: OrgCountData = OrgCountDataFixture.orgCountData_default

  val orgSettings = OrgSettings(
    enable_ab_testing = false,
    disable_force_send = false,
    bulk_sender = false,
    allow_2fa = false,
    show_2fa_setting = false,
    enforce_2fa = false,
    allow_native_crm_integration = false,
      agency_option_allow_changing = false,
      agency_option_show = false
  )

  val orgPlan = OrgPlanFixture.orgPlanFixture


  val org = OrganizationWithCurrentData(

    id = 1,
    name = company,
    owner_account_id = 49,

    counts = orgCountData,
    settings = orgSettings,
    plan = orgPlan,

    is_agency = true,
    trial_ends_at = DateTime.now().plusDays(100),
    error = None,
    error_code = None,
    paused_till = None,
    errors = Seq(),
    warnings = Seq(),
    via_referral = false,
    org_metadata = orgMetadata
  )

  val teamMember: TeamMember = TeamMember(
    team_id = teamId,
    team_name = "team_name",
    user_id = 1L,
    ta_id = 1L, // dont send ta_id to frontend / api response, only for internal purpose, its dynamically assigned in AuthUtils
    first_name = Some(first_name),
    last_name = Some(last_name),
    email = "<EMAIL>",
    team_role = TeamAccountRole.ADMIN,
    api_key = Some("apiKey"),
    zapier_key = Some("zapier_key")
  )


  val account: Account = Account(
    id = AccountUuid("abcd"),
    internal_id = 5L,
    email = "<EMAIL>",
    email_verification_code = None,
    email_verification_code_created_at = None,
    created_at = DateTime.now().minusDays(1000),
    first_name = Some("first_name"),
    last_name = Some("last_name"),
    company = Some("SR"),
    timezone = None,
    profile = profile,
    org_role = Some(OrganizationRole.OWNER),
    teams = Seq(),
    account_type = AccountType.AGENCY,
    org = org,

    active = true,
    email_notification_summary = "dSFA",
    account_metadata = AccountMetadata(
      is_profile_onboarding_done = Some(true)
    ),
    email_verified = true,
    signupType = None,
    account_access = AccountAccess(
      inbox_access = false
    ),
    calendar_account_data = None
  )

  val campaignStepWithChildren = CampaignStepWithChildren(
    id = 1,
    label = Some("A"),
    campaign_id = 1,
    delay = 1,
    step_type = CampaignStepType.AutoEmailStep,
    created_at = DateTime.now(),
    children = List(1),
    variants = Seq()
  )

  val campaignTimezone = "Asia/Kolkata"

  val teamMemberLite = TeamMemberLite(

    user_id =  2L,
    first_name = Some("first_name"),
    last_name = Some("last_name"),
    email = "<EMAIL>",
    active = true,
    timezone = Some(campaignTimezone),
    twofa_enabled = true,
    created_at = aDate,
    user_uuid = AccountUuid("uuid"),
    team_role = TeamAccountRole.ADMIN

  )

  val prospect_CategoriesInDB = ProspectCategoriesInDB(
    id = 22L,
    name = "Do Not Contact",
    text_id = "do_not_contact",
    label_color = "Blue",
    is_custom = true,
    team_id = teamId,
    rank = ProspectCategoryRank(rank = 2000),
  )

  val adminDefaultPermissions = RolePermissionDataDAOV2.defaultRoles(
    role = TeamAccountRole.ADMIN,
    simpler_perm_flag = false
  )

  val rolePermissionData = RolePermissionDataV2.toRolePermissionApi(
    data = adminDefaultPermissions.copy(id = 10)
  )

  val team_account: TeamAccount = TeamAccount(

    team_id = teamId,
    org_id = 20L,

    role_from_db = Some(adminDefaultPermissions), // MUST come from db (option type only for cacheservice error), should not be sent to frontend, only intermediate

    role = Some(rolePermissionData), // should be sent to frontend

    active = true,
    is_actively_used = true,
    team_name = "team_name",
    total_members = 5,
    access_members = Seq(teamMember),
    all_members = Seq(teamMemberLite),

    prospect_categories_custom = Seq(prospect_CategoriesInDB),
    max_emails_per_prospect_per_day = 100L,
    max_emails_per_prospect_per_week = 500L,
    max_emails_per_prospect_account_per_day = 97,
    max_emails_per_prospect_account_per_week = 497,

    reply_handling = ReplyHandling.PAUSE_SPECIFIC_CAMPAIGN_ON_REPLY,
    created_at = aDate,
    selected_calendar_data = None,
    team_uuid = TeamUuid("uuid")
  )


  val accountAdmin = Account(
    id = AccountUuid("account_uuid"),
    internal_id = 2,
    email = email,
    email_verification_code = None,
    email_verification_code_created_at = None,
    created_at = DateTime.now().minusDays(1000),
    first_name = Some(first_name),
    last_name = Some(last_name),
    company = Some(company),
    timezone = None,
    profile = profile,
    org_role = Some(OrganizationRole.OWNER),
    teams = Seq(team_account),
    account_type = AccountType.AGENCY,
    org = org,
    active = true,
    email_notification_summary = "dSFA",
    account_metadata = accountMetadata,
    email_verified = true,
    signupType = None,
    account_access = AccountAccess(
      inbox_access = false
    ),
    calendar_account_data = None

  )

  val senderEmailSettingId = 3

  val emailAccountSetting = EmailSetting(
    id = Some(EmailSettingId(emailSettingId = senderEmailSettingId)), // FIXME VALUECLASS
    org_id = OrgId(id = org.id), // FIXME VALUECLASS
    owner_id = AccountId(id = ownerId), // FIXME VALUECLASS
    team_id = TeamId(id = teamId), // FIXME VALUECLASS
    message_id_suffix = "Awesome",
    uuid = Some(EmailSettingUuid("test_uuid")),
    owner_uuid = AccountUuid("owner_uuid"),
    team_uuid = TeamUuid("team_uuid"),
    email = email,
    email_address_host = "friends",

    service_provider = EmailServiceProvider.GMAIL_API,
      domain_provider = None,
    via_gmail_smtp = Some(true),

    owner_name = "Rachel",
    sender_name = "Don",
    first_name = first_name,
    last_name = last_name,

    cc_emails = None,
    bcc_emails = None,

    smtp_username = None,
    smtp_password = None,
    smtp_host = None,
    smtp_port = None,

    imap_username = None,
    imap_password = None,
    imap_host = None,
    imap_port = None,

    oauth2_access_token = None,
    oauth2_refresh_token = None,
    oauth2_token_type = None,
    oauth2_token_expires_in = None,
    oauth2_access_token_expires_at = None,

    // for mailgun
    email_domain = None,
    api_key = None,
    mailgun_region = None,

    quota_per_day = 97,

    reply_handling = ReplyHandling.PAUSE_SPECIFIC_CAMPAIGN_ON_REPLY,
    last_read_for_replies = None,
    latest_email_scheduled_at = None,

    error = None,
    error_reported_at = None,
    paused_till = None,

    signature = "Green",

    created_at = None,

    current_prospect_sent_count_email = 7,

    default_tracking_domain = "godaddy",
    default_unsubscribe_domain = "saturday",
    rep_tracking_host_id = 3,
    tracking_domain_host = None,
    custom_tracking_domain = None,
    custom_tracking_cname_value = None,
    custom_tracking_domain_is_verified = None,
    custom_tracking_domain_is_ssl_enabled = None,

    rep_mail_server_id = 3,
    rep_mail_server_public_ip = "***********",
    rep_mail_server_host = "godaddy",
    rep_mail_server_reverse_dns = None,

    min_delay_seconds = 7,
    max_delay_seconds = 59,
      tag = None,
    campaign_use_status_for_email_setting = CampaignUseStatusForEmailSetting.IsNotAssignedToAnyCampaign,
    show_rms_ip_in_frontend = false

  )

  val DONOTCONTACT_CATEGORY_ID = ProspectCategoryId(id = 269L)
  val DONOTCONTACT_CATEGORY_ID_value = 269L

  val scheduledProspectsCountForCampaignEmail = ScheduledProspectsCountForCampaignEmail(
    campaignId = campaign_id,
    newCount = 37,
    followupCount = 41,
    newCountNotSent = 11,
    followupCountNotSent = 7
  )

  val campaignSettings = CampaignSettings(

    // settings
    campaign_email_settings = List(
      CampaignEmailSettings(
        campaign_id = CampaignId(26),
        sender_email_setting_id = EmailSettingId(senderEmailSettingId),
        receiver_email_setting_id = EmailSettingId(11),
        team_id = TeamId(teamId),
        uuid = CampaignEmailSettingsUuid("temp_setting_id"),
        id = CampaignEmailSettingsId(123),
        sender_email = "<EMAIL>",
        receiver_email = "<EMAIL>",
        max_emails_per_day_from_email_account = 100,
        signature = Some("emailsignature"),
        error = None,
        from_name = None
      )
    ),
    campaign_linkedin_settings = List(
      LinkedinSettingSenderDetailsFixtures.linkedin_setting_sender_details
    ),
    campaign_call_settings = List(
      CallSettingSenderDetailsFixtures.call_setting_sender_details
    ),
    campaign_whatsapp_settings = List(
      WhatsappSettingSenderDetailsFixtures.whatsapp_setting_sender_details
    ),
    campaign_sms_settings = List(
      SmsSettingSenderDetailsFixtures.sms_setting_sender_details
    ),
    timezone = campaignTimezone,
    daily_from_time = 2, // time since beginning of day in seconds
    daily_till_time = 3, // time since beginning of day in seconds
    sending_holiday_calendar_id = Some(123L),

    ai_sequence_status = None,

    // Sunday is the first day
    days_preference = List(true, true, true, true, true, true, false),

    mark_completed_after_days = 33,
    max_emails_per_day = 100,
    open_tracking_enabled = true,
    click_tracking_enabled = true,
    enable_email_validation = true,
    ab_testing_enabled = true,

    // warm up
    warmup_started_at = Some(aDate.minusDays(3)),
    warmup_length_in_days = Some(2),
    warmup_starting_email_count = Some(5),
    show_soft_start_setting = false,

    // schedule start
    schedule_start_at = Some(aDate.minusDays(1)),
    schedule_start_at_tz = Some("Sometimezone"),
    send_plain_text_email = Some(false),
    campaign_type = CampaignType.MultiChannel,


    email_priority = CampaignEmailPriority.FIRST_EMAIL,
    append_followups = true,
    opt_out_msg = "opt out msg",
    opt_out_is_text = true,
    add_prospect_to_dnc_on_opt_out = true,
    triggers = Seq(),
    sending_mode = None,

    selected_calendar_data = None
  )

  val allCampaignStats = AllCampaignStats(
    total_sent = 1,
    total_opened = 1,
    total_clicked = 1,
    total_replied = 1,
    total_steps = 1,
    current_prospects = 1,
    current_opted_out = 1,
    current_completed = 1,
    current_bounced = 1,
    current_to_check = 1,
    current_failed_email_validation = 1,
    current_in_progress = 1,
    current_unsent_prospects = 1,
    current_do_not_contact = 1,
    reply_sentiment_stats = ReplySentimentStats(
      positive = 0
    )
  )

  val unsentProspectsCounts = UnsentProspectsCounts(
    total_unsent_prospects = 37,
    total_unsent_prospects_with_invalid_emails = 0,
    total_unsent_prospects_with_missing_merge_tags = 0,
    total_unsent_prospects_in_dnc = 0,
    total_unsent_prospects_with_previous_task_not_done = 0,
    total_unsent_prospects_that_are_likely_valid = 53,
    total_unsent_prospects_in_same_tz = 3
  )

  val campaign_uuid = s"cmp_${teamId}_cfknacskndjcn"

  val campaignWithStatsAndEmail = CampaignWithStatsAndEmail(
    id = 121L,
    uuid = Some(campaign_uuid),
    team_id = teamId,
    shared_with_team = true,
    name = campaign_name,
    owner_name = first_name,
    owner_email = email,
    owner_id = ownerId,
    status = CampaignStatus.RUNNING,
    tags = Seq(CampaignTag(11L, "tag1", CampaignTagUuid("tag_abcdefgh"))),
    spam_test_exists = false,
    warmup_is_on = false,

    stats = allCampaignStats,

    head_step_id = Some(22L),

    ai_generation_context = None,

    settings = campaignSettings,

    created_at = aDate,

    error = Some("campaign error"),
    is_archived = false

  )
  val campaignBasicInfo = CampaignBasicInfo(
    id = 121L,
    uuid = Some(campaign_uuid),
    team_id = teamId,
    shared_with_team = true,
    name = campaign_name,
    owner_name = first_name,
    owner_email = email,
    owner_id = ownerId,
    status = CampaignStatus.RUNNING,
    tags = Seq(CampaignTag(11L, "tag1", CampaignTagUuid("tag_abcdefgh"))),
    spam_test_exists = false,
    warmup_is_on = false,
    ai_generation_context = None,
    head_step_id = Some(22L),
    settings = campaignSettings,
    created_at = aDate,
    error = Some("campaign error"),
    campaign_has_email_step = true,
    is_archived = false
  )


  val cpAssignResult1 = CPAssignResult(

    prospectIdsIgnoredBecauseAlreadyAssignedToThisCampaign= List(),

    prospectIdsIgnoredBecauseInOtherCampaigns= List(),

    newlyAssignedProspectIds= List()

  )

  val cpAssignResult2 = CPAssignResult(

    prospectIdsIgnoredBecauseAlreadyAssignedToThisCampaign= List(),

    prospectIdsIgnoredBecauseInOtherCampaigns= List(),

    newlyAssignedProspectIds= List(123L)

  )

    val emailSetting = CampaignEmailSettingForScheduler(
        sender_email_settings_id = 1,
        receiver_email_settings_id = 1,
        campaign_email_settings_id = CampaignEmailSettingsId(1),
        emailServiceProvider = EmailServiceProvider.GMAIL_API
    )

    val campaignTypeData = MultiChannelCampaignData(head_step_id = 22L)

    val campaign1 = CampaignForSchedulingEmail(
        campaign_id = 101,
        campaign_owner_id = 1,
        team_id = 100,
        org_id = 1,
        campaign_name = "Test Campaign 1",
        status = CampaignStatus.RUNNING,
        campaign_type_data = campaignTypeData,
        ai_generation_context = None,
        sending_holiday_calendar_id = None,
        campaign_email_setting = emailSetting,
        append_followups = true,
        open_tracking_enabled = true,
        click_tracking_enabled = true,
        opt_out_msg = "{{unsubscribe_link}}",
        opt_out_is_text = false,
        timezone = "UTC",
        daily_from_time = 32400, // 9:00 AM
        daily_till_time = 61200, // 5:00 PM
        days_preference = List(false, true, true, true, true, true, false), // Mon-Fri
        email_priority = CampaignEmailPriority.FIRST_EMAIL,
        max_emails_per_prospect_per_day = 5,
        max_emails_per_prospect_per_week = 10,
        max_emails_per_prospect_account_per_day = 100,
        max_emails_per_prospect_account_per_week = 500,
        campaign_max_emails_per_day = 100,
        softstart_setting = None,
        mark_completed_after_days = 30,
        latest_email_scheduled_at = None,
        from_email = "<EMAIL>",
        from_name = "Test Sender",
        reply_to_email = "<EMAIL>",
        reply_to_name = "Test Sender",
        min_delay_seconds = 0,
        max_delay_seconds = 60,
        enable_email_validation = false,
        rep_mail_server_id = 1,
        via_gmail_smtp = None,
        prospects_remaining_to_be_scheduled_exists = Some(true),
        count_of_sender_emails = 1,
        selected_calendar_data = None
    )

    val campaign2 = campaign1.copy(
        campaign_id = 102,
        campaign_name = "Test Campaign 2"
    )

    private def convertForScheduler(data: Set[(CampaignForSchedulingEmail, Set[String])]): Set[(CampaignForScheduling, Set[String])] = {
        data.map { case (campaign, tz) => (campaign: CampaignForScheduling, tz) }
    }

  describe("CampaignService Tests"){
    val ownedProspectsForAssigning = OwnedProspectsForAssigning(
      prospect_id = 1,
      invalid_email = Some(false),
      email_bounced_at = None,
      email_bounced = Some(false),
      prospect_category_id_custom = 16,
      synced_previously_sent_step_for_deleted_prospect_step_id = None
    )
    val ownedProspectsForAssigningList = List(
      ownedProspectsForAssigning,
      ownedProspectsForAssigning.copy(
        prospect_id = 2,
        prospect_category_id_custom = 17
      )
    )
    describe("campaignService.assignProspects") {

      it("should fail when prospect is already assigned") {

        (campaignProspectServiceMock.assignProspectsToCampaign)
          .expects(
            accountAdmin.org.id,
            permittedAccountIds, accountAdmin.internal_id, Helpers.getAccountName(accountAdmin), teamMember.user_id, teamId, campaignWithStatsAndEmail.id.toLong, campaignWithStatsAndEmail.name, campaignWithStatsAndEmail.settings,
            List(1L, 2L), IgnoreProspectsInOtherCampaigns.IgnoreProspectsActiveInOtherCampaigns, Logger)
          .returning(Failure(new Throwable("Error while campaignProspectDAO.assign campaigns_prospects_pkey")))

        val res = campaignProspectAssign.assignProspects(
          doer = accountAdmin,
          accountId = teamMember.user_id,
          teamId = teamMember.team_id,
          ignoreProspectsInOtherCampaigns = IgnoreProspectsInOtherCampaigns.IgnoreProspectsActiveInOtherCampaigns,
          permittedAccountIds = permittedAccountIds,
          campaignId = campaignWithStatsAndEmail.id,
          campaignName = campaignWithStatsAndEmail.name,
          prospectIds = List(1L, 2L),
          campaignSettings = campaignWithStatsAndEmail.settings,
          Logger = Logger,

        )

        res match {
          case Left(e: AssignProspectsError) =>
            e match {
              case AssignProspectsError.ProspectAlreadyAssigned(error) =>
                Logger.info(s"AssignProspectsError!!... $error")
                //AssignProspectsError!!... Prospect is already assigned to the given campaign
                assert(true)
              case _ =>
                Logger.info(s"Error!!!... $e")
                assert(false)
            }

          case Right(v: AssignProspectsResponse) => assert(false)
        }
      }

      it("should fail when prospect not found") {

        (campaignProspectServiceMock.assignProspectsToCampaign)
          .expects(
            accountAdmin.org.id,
            permittedAccountIds, accountAdmin.internal_id, Helpers.getAccountName(accountAdmin), teamMember.user_id, teamId, campaignWithStatsAndEmail.id.toLong, campaignWithStatsAndEmail.name, campaignWithStatsAndEmail.settings,
            List(4L), IgnoreProspectsInOtherCampaigns.IgnoreProspectsActiveInOtherCampaigns, Logger)
          .returning(Failure(new Throwable("Error while campaignProspectDAO.assign campaigns_prospects_prospect_id_fkey")))

        val res = campaignProspectAssign.assignProspects(
          doer = accountAdmin,
          accountId = teamMember.user_id,
          teamId = teamMember.team_id,
          ignoreProspectsInOtherCampaigns = IgnoreProspectsInOtherCampaigns.IgnoreProspectsActiveInOtherCampaigns,
          permittedAccountIds = permittedAccountIds,
          campaignId = campaignWithStatsAndEmail.id,
          campaignName = campaignWithStatsAndEmail.name,
          prospectIds = List(4L),
          campaignSettings = campaignWithStatsAndEmail.settings,
          Logger = Logger
        )

        res match {
          case Left(e: AssignProspectsError) =>
            e match {
              case AssignProspectsError.ProspectNotFound(error) =>
                Logger.info(s"AssignProspectsError!!... $error")
                //AssignProspectsError!!... Prospect with given id not found
                assert(true)
              case _ =>
                Logger.info(s"Error!!!... $e")
                assert(false)
            }

          case Right(v: AssignProspectsResponse) => assert(false)
        }
      }

      it("should fail when campaign not found") {

        (campaignProspectServiceMock.assignProspectsToCampaign)
          .expects(
            accountAdmin.org.id,
            permittedAccountIds, accountAdmin.internal_id, Helpers.getAccountName(accountAdmin), teamMember.user_id, teamId, campaign_id, campaignWithStatsAndEmail.name, campaignWithStatsAndEmail.settings,
            List(1L, 2L), IgnoreProspectsInOtherCampaigns.IgnoreProspectsActiveInOtherCampaigns, Logger)
          .returning(Failure(new Throwable("Error while campaignProspectDAO.assign campaigns_prospects_campaign_id_fkey")))

        val res = campaignProspectAssign.assignProspects(
          doer = accountAdmin,
          accountId = teamMember.user_id,
          teamId = teamMember.team_id,
          ignoreProspectsInOtherCampaigns = IgnoreProspectsInOtherCampaigns.IgnoreProspectsActiveInOtherCampaigns,
          permittedAccountIds = permittedAccountIds,
          campaignId = campaignWithStatsAndEmail.id,
          campaignName = campaignWithStatsAndEmail.name,
          prospectIds = List(1L, 2L),
          campaignSettings = campaignWithStatsAndEmail.settings,
          Logger = Logger
        )

        res match {
          case Left(e: AssignProspectsError) =>
            e match {
              case AssignProspectsError.CampaignNotFound(error) =>
                Logger.info(s"AssignProspectsError!!... $error")
                //AssignProspectsError!!... Campaign with given id not found
                assert(true)
              case _ =>
                Logger.info(s"Error!!!... $e")
                assert(false)
            }

          case Right(v: AssignProspectsResponse) => assert(false)
        }
      }

      it("should fail when campaignProspectDAO.assign throws error") {

        (campaignProspectServiceMock.assignProspectsToCampaign)
          .expects(
            accountAdmin.org.id,
            permittedAccountIds, accountAdmin.internal_id, Helpers.getAccountName(accountAdmin), teamMember.user_id, teamId, campaignWithStatsAndEmail.id.toLong, campaignWithStatsAndEmail.name, campaignWithStatsAndEmail.settings,
            List(1L, 2L), IgnoreProspectsInOtherCampaigns.IgnoreProspectsActiveInOtherCampaigns, Logger)
          .returning(Failure(new Throwable("Error while campaignProspectDAO.assign")))

        val res = campaignProspectAssign.assignProspects(
          doer = accountAdmin,
          accountId = teamMember.user_id,
          teamId = teamMember.team_id,
          ignoreProspectsInOtherCampaigns = IgnoreProspectsInOtherCampaigns.IgnoreProspectsActiveInOtherCampaigns,
          permittedAccountIds = permittedAccountIds,
          campaignId = campaignWithStatsAndEmail.id,
          campaignName = campaignWithStatsAndEmail.name,
          prospectIds = List(1L, 2L),
          campaignSettings = campaignWithStatsAndEmail.settings,
          Logger = Logger
        )

        res match {
          case Left(e: AssignProspectsError) =>
            e match {
              case AssignProspectsError.ErrorWhileAssigningProspect(error) =>
                Logger.info(s"AssignProspectsError!!... $error")
                assert(true)
              case _ =>
                Logger.info(s"Error!!!... $e")
                assert(false)
            }

          case Right(v: AssignProspectsResponse) => assert(false)
        }
      }

      it("should success with warning when empty list of prospect passed") {

        (campaignProspectServiceMock.assignProspectsToCampaign)
          .expects(
            accountAdmin.org.id,
            permittedAccountIds, accountAdmin.internal_id, Helpers.getAccountName(accountAdmin), teamMember.user_id, teamId, campaignWithStatsAndEmail.id.toLong, campaignWithStatsAndEmail.name, campaignWithStatsAndEmail.settings,
            List(), IgnoreProspectsInOtherCampaigns.IgnoreProspectsActiveInOtherCampaigns, Logger)
          .returning(Success(cpAssignResult1))

        val res = campaignProspectAssign.assignProspects(
          doer = accountAdmin,
          accountId = teamMember.user_id,
          teamId = teamMember.team_id,
          ignoreProspectsInOtherCampaigns = IgnoreProspectsInOtherCampaigns.IgnoreProspectsActiveInOtherCampaigns,
          permittedAccountIds = permittedAccountIds,
          campaignId = campaignWithStatsAndEmail.id,
          campaignName = campaignWithStatsAndEmail.name,
          prospectIds = List(),
          campaignSettings = campaignWithStatsAndEmail.settings,
          Logger = Logger
        )

        res match {
          case Left(e: AssignProspectsError) =>
            assert(false)

          case Right(v: AssignProspectsResponse) =>
            Logger.info(s"${v.responseMsg}, assigned ${v.assignedProspectIdsLength} prospects to campaign ${v.campaignId}")
            //Please select a few prospects to assign, assigned 0 prospects to campaign 121
            assert(true)
        }
      }

      it("should success with warning when assignedProspectIds is empty") {

        (campaignProspectServiceMock.assignProspectsToCampaign)
          .expects(
            accountAdmin.org.id,
            permittedAccountIds, accountAdmin.internal_id, Helpers.getAccountName(accountAdmin), teamMember.user_id, teamId, campaignWithStatsAndEmail.id.toLong, campaignWithStatsAndEmail.name,campaignWithStatsAndEmail.settings,
            List(123L), IgnoreProspectsInOtherCampaigns.IgnoreProspectsActiveInOtherCampaigns, Logger)
          .returning(Success(cpAssignResult1))

        val res = campaignProspectAssign.assignProspects(
          doer = accountAdmin,
          accountId = teamMember.user_id,
          teamId = teamMember.team_id,
          ignoreProspectsInOtherCampaigns = IgnoreProspectsInOtherCampaigns.IgnoreProspectsActiveInOtherCampaigns,
          permittedAccountIds = permittedAccountIds,
          campaignId = campaignWithStatsAndEmail.id,
          campaignName = campaignWithStatsAndEmail.name,
          prospectIds = List(123L),
          campaignSettings = campaignWithStatsAndEmail.settings,
          Logger = Logger
        )

        res match {
          case Left(e: AssignProspectsError) =>
            assert(false)

          case Right(v: AssignProspectsResponse) =>
            Logger.info(s"${v.responseMsg}, assigned ${v.assignedProspectIdsLength} prospects to campaign ${v.campaignId}")
            //No prospect assigned. Selected prospects are either already assigned to the campaign, or are currently active in other campaigns,
            //assigned 0 prospects to campaign 121
            assert(true)
        }
      }

      it("should success with warning when assignedProspectIds is empty and ignoreProspectsActiveInOtherCampaigns is false") {

        (campaignProspectServiceMock.assignProspectsToCampaign)
          .expects(
            accountAdmin.org.id,
            permittedAccountIds, accountAdmin.internal_id, Helpers.getAccountName(accountAdmin), teamMember.user_id, teamId, campaignWithStatsAndEmail.id.toLong, campaignWithStatsAndEmail.name, campaignWithStatsAndEmail.settings,
            List(123L), IgnoreProspectsInOtherCampaigns.DoNotIgnore, Logger)
          .returning(Success(cpAssignResult1))

        val res = campaignProspectAssign.assignProspects(
          doer = accountAdmin,
          accountId = teamMember.user_id,
          teamId = teamMember.team_id,
          ignoreProspectsInOtherCampaigns = IgnoreProspectsInOtherCampaigns.DoNotIgnore,
          permittedAccountIds = permittedAccountIds,
          campaignId = campaignWithStatsAndEmail.id,
          campaignName = campaignWithStatsAndEmail.name,
          prospectIds = List(123L),
          campaignSettings = campaignWithStatsAndEmail.settings,
          Logger = Logger
        )

        res match {
          case Left(e: AssignProspectsError) =>
            assert(false)

          case Right(v: AssignProspectsResponse) =>
            Logger.info(s"${v.responseMsg}, assigned ${v.assignedProspectIdsLength} prospects to campaign ${v.campaignId}")
            //No prospect assigned. Selected prospects are already assigned to the campaign, assigned 0 prospects to campaign 121
            assert(true)
        }
      }

      it("should success when prospectIds and assignedProspectIds are nonEmpty") {

        (campaignProspectServiceMock.assignProspectsToCampaign)
          .expects(
            accountAdmin.org.id,
            permittedAccountIds, accountAdmin.internal_id, Helpers.getAccountName(accountAdmin), teamMember.user_id, teamId, campaignWithStatsAndEmail.id.toLong, campaignWithStatsAndEmail.name, campaignWithStatsAndEmail.settings,
            List(123L), IgnoreProspectsInOtherCampaigns.DoNotIgnore, Logger)
          .returning(Success(cpAssignResult2))
        (campaignCacheService.resetCampaignStats(_: Long, _: Long)(using _: SRLogger))
          .expects(121, 37, *)
          .returning(())

        val res = campaignProspectAssign.assignProspects(
          doer = accountAdmin,
          accountId = teamMember.user_id,
          teamId = teamMember.team_id,
          ignoreProspectsInOtherCampaigns = IgnoreProspectsInOtherCampaigns.DoNotIgnore,
          permittedAccountIds = permittedAccountIds,
          campaignId = campaignWithStatsAndEmail.id,
          campaignName = campaignWithStatsAndEmail.name,
          prospectIds = List(123L),
          campaignSettings = campaignWithStatsAndEmail.settings,
          Logger = Logger
        )

        res match {
          case Left(e: AssignProspectsError) =>
            assert(false)

          case Right(v: AssignProspectsResponse) =>
            Logger.info(s"${v.responseMsg}, assigned ${v.assignedProspectIdsLength} prospects to campaign ${v.campaignId}")
            //1 prospects assigned to campaign 'CampaignName', assigned 1 prospects to campaign 121
            assert(v.assignedProspectIds.contains(123L))
        }
      }

    }

    describe("campaignService.createCampaign") {

      val createCampaignFormData = CampaignCreateForm(
        name = Some("Demo Campaign 1"),
        timezone = None,
        campaign_owner_id = None,
        campaign_type = CampaignType.MultiChannel
      )
      val defaultTimeZone = "America/Los_Angeles"

      it("should fail when unique campaign name not provided") {

        (() => srUuidUtils.generateCampaignUuid())
          .expects()
          .returning(s"cmp_aa_abcd")

        (campaignDAOService.getNameForNewCampaign)
          .expects(
            "Demo",
            TeamId(id = teamMember.team_id)
          )
          .returning(
            Success("Demo Campaign 1")
          )

        (campaignDAOService.create)
          .expects(
            teamMember.user_id,
            teamMember.team_id,
            teamMember.ta_id,
            createCampaignFormData.name.get,
            None,
            defaultTimeZone,
            CampaignType.MultiChannel,
            *
          )
          .returning(
            Failure(new Exception("unique_campaign_name"))
          )
        val res = campaignService.createCampaign(
          orgId = 1,
          accountId = teamMember.user_id,
          teamId = teamMember.team_id,
          taId = teamMember.ta_id,
          data = createCampaignFormData,
          permittedAccountIdsForEditCampaigns = permittedAccountIds,
          ownerFirstName = "Demo"
        )

        res.flatMap {
          case Left(CampaignCreationError.CampaignNameNotUniqueError(e)) =>
            assert(true)

          case _ => assert(false)
        }

      }

      it("should fail when SQLExceptionError") {

        (() => srUuidUtils.generateCampaignUuid())
          .expects()
          .returning(s"cmp_aa_abcd")

        val createCampaignFormData = CampaignCreateForm(
          name = Some("Demo Campaign 1"),
          timezone = None,
          campaign_owner_id = None,
          campaign_type = CampaignType.MultiChannel,
        )
        (campaignDAOService.getNameForNewCampaign)
          .expects(
            "Demo",
            TeamId(id = teamMember.team_id)
          )
          .returning(
            Success("Demo Campaign 1")
          )

        (campaignDAOService.create)
          .expects(
            teamMember.user_id,
            teamMember.team_id,
            teamMember.ta_id,
            createCampaignFormData.name.get,
            None,
            defaultTimeZone,
            CampaignType.MultiChannel,
            *
          )
          .returning(
            Failure(new Exception("Sql Exception Error"))
          )
        val res = campaignService.createCampaign(
          orgId = 1,
          accountId = teamMember.user_id,
          teamId = teamMember.team_id,
          taId = teamMember.ta_id,
          data = createCampaignFormData,
          permittedAccountIdsForEditCampaigns = permittedAccountIds,
          ownerFirstName = "Demo"
        )

        res.flatMap {
          case Left(CampaignCreationError.SQLException(err)) =>
            assert(true)

          case _ => assert(false)
        }

      }

      it("should fail when db returns None") {

        (() => srUuidUtils.generateCampaignUuid())
          .expects()
          .returning(s"cmp_aa_abcd")

        (campaignDAOService.getNameForNewCampaign)
          .expects(
            "Demo",
            TeamId(id = teamMember.team_id)
          )
          .returning(
            Success("Demo Campaign 1")
          )

        (campaignDAOService.create)
          .expects(
            teamMember.user_id,
            teamMember.team_id,
            teamMember.ta_id,
            createCampaignFormData.name.get,
            None,
            defaultTimeZone,
            CampaignType.MultiChannel,
            *
          )
          .returning(
            Success(None)
          )
        val res = campaignService.createCampaign(
          orgId = 1,
          accountId = teamMember.user_id,
          teamId = teamMember.team_id,
          taId = teamMember.ta_id,
          data = createCampaignFormData,
          permittedAccountIdsForEditCampaigns = permittedAccountIds,
          ownerFirstName = "Demo"
        )

        res.flatMap {
          case Left(CampaignCreationError.CampaignNotCreated) =>
            assert(true)

          case _ => assert(false)
        }

      }

      it("Should Success when data is returned from db (uses default timezone)") {

        (() => srUuidUtils.generateCampaignUuid())
          .expects()
          .returning(s"cmp_aa_abcd")

        (campaignDAOService.getNameForNewCampaign)
          .expects(
            "Demo",
            TeamId(id = teamMember.team_id)
          )
          .returning(
            Success("Demo Campaign 1")
          )

        (campaignDAOService.create)
          .expects(
            teamMember.user_id,
            teamMember.team_id,
            teamMember.ta_id,
            createCampaignFormData.name.get,
            None,
            defaultTimeZone,
            CampaignType.MultiChannel,
            *
          )
          .returning(
            Success(Some(1))
          )
        (campaignDAO.getCampaignBasicInfo(_: Long, _: Long))
          .expects(37, 1)
          .returning(Success(Some(campaignBasicInfo)))
        (campaignCacheService.getCampaignStatsById(_: Long, _: Long)(using _: SRLogger))
          .expects(1, 37, *)
          .returning(Some(allCampaignStats))
        val updateEmailSendingStatusForm = UpdateEmailSendingStatusForm(
          entityType = EmailSendingEntityTypeData.CampaignData(
            orgId = OrgId(org.id), campaignId = campaignWithStatsAndEmail.id, teamId = teamId
          ),
          orgId = OrgId(org.id),
          sendEmailStatus = SendEmailStatusData.AllowedData()
        )
        (emailSendingStatusDAO.addingEmailSendingStatusTry)
          .expects(updateEmailSendingStatusForm)
          .returning(Success(1L))

        (srUserFeatureUsageEventService.addFeatureUsageEvent)
          .expects(1, *)
          .returning(Success(1))
        val res = campaignService.createCampaign(
          orgId = 1,
          accountId = teamMember.user_id,
          teamId = teamMember.team_id,
          taId = teamMember.ta_id,
          data = createCampaignFormData,
          permittedAccountIdsForEditCampaigns = permittedAccountIds,
          ownerFirstName = "Demo"
        )(actorContext, Logger)

        res.flatMap {
          case Right(data) =>
            assert(true)

          case _ => assert(false)
        }


      }
      it("should success when data is returned from db(campaignBasicInfo) and from cache(campaignStats)(uses campaignSettings timezone)") {
        val testTimeZone = "Europe/Kiev"

        (() => srUuidUtils.generateCampaignUuid())
          .expects()
          .returning(s"cmp_aa_abcd")

        (campaignDAOService.getNameForNewCampaign)
          .expects(
            "Demo",
            TeamId(id = teamMember.team_id)
          )
          .returning(
            Success("Demo Campaign 1")
          )

        (campaignDAOService.create)
          .expects(
            teamMember.user_id,
            teamMember.team_id,
            teamMember.ta_id,
            createCampaignFormData.name.get,
            Some(campaignSettings.copy(timezone = testTimeZone)),
            testTimeZone,
            CampaignType.MultiChannel,
            *
          )
          .returning(
            Success(Some(1))
          )
        (campaignDAO.getCampaignBasicInfo(_: Long, _: Long))
          .expects(37, 1)
          .returning(Success(Some(campaignBasicInfo)))
        (campaignCacheService.getCampaignStatsById(_: Long, _: Long)(using _: SRLogger))
          .expects(1, 37, *)
          .returning(Some(allCampaignStats))
        val updateEmailSendingStatusForm = UpdateEmailSendingStatusForm(
          entityType = EmailSendingEntityTypeData.CampaignData(
            orgId = OrgId(org.id), campaignId = campaignWithStatsAndEmail.id, teamId = teamId
          ),
          orgId = OrgId(org.id),
          sendEmailStatus = SendEmailStatusData.AllowedData()
        )
        (emailSendingStatusDAO.addingEmailSendingStatusTry)
          .expects(updateEmailSendingStatusForm)
          .returning(Success(1L))

        (srUserFeatureUsageEventService.addFeatureUsageEvent)
          .expects(1, *)
          .returning(Success(1))
        val res = campaignService.createCampaign(
          orgId = 1,
          accountId = teamMember.user_id,
          teamId = teamMember.team_id,
          taId = teamMember.ta_id,
          data = createCampaignFormData,
          campaignSettings = Some(campaignSettings.copy(timezone = testTimeZone)),
          permittedAccountIdsForEditCampaigns = permittedAccountIds,
          ownerFirstName = "Demo"
        )

        res.flatMap {
          case Right(data) =>
            assert(true)

          case _ => assert(false)
        }
      }
      it("should success when data is returned from db(campaignBasicInfo and campaignStats)(uses campaignSettings timezone)") {
        val testTimeZone = "Europe/Kiev"

        (() => srUuidUtils.generateCampaignUuid())
          .expects()
          .returning(s"cmp_aa_abcd")

        (campaignDAOService.getNameForNewCampaign)
          .expects(
            "Demo",
            TeamId(id = teamMember.team_id)
          )
          .returning(
            Success("Demo Campaign 1")
          )

        (campaignDAOService.create)
          .expects(
            teamMember.user_id,
            teamMember.team_id,
            teamMember.ta_id,
            createCampaignFormData.name.get,
            Some(campaignSettings.copy(timezone = testTimeZone)),
            testTimeZone,
            CampaignType.MultiChannel,
            *
          )
          .returning(
            Success(Some(1))
          )
        (campaignDAO.getCampaignBasicInfo(_: Long, _: Long))
          .expects(37, 1)
          .returning(Success(Some(campaignBasicInfo)))
        (campaignCacheService.getCampaignStatsById(_: Long, _: Long)(using _: SRLogger))
          .expects(1, 37, *)
          .returning(None)
        (accountService.find(_: Long)(_: SRLogger))
          .expects(2, *)
          .returning(Success(accountAdmin))
        val doNotContactCategoryId = Helpers.getCategoryByTextId(
          team_id = 37,
          account = accountAdmin,
          textId = ProspectCategory.DO_NOT_CONTACT,
          Logger = Logger
        ).get.id
        Logger.info(s"Do Not Contact Category Id : ${doNotContactCategoryId}")
        (replySentimentDAOService.getUuidsForTeamIdAndReplySentimentType(_: Long, _: ReplySentimentType)(_: SRLogger))
          .expects(37, ReplySentimentType.Positive, Logger)
          .returning(Success(List()))
        (campaignDAO.getCampaignStatsById)
          .expects(1, 37, 22, List(), *, *)
          .returning(Success(allCampaignStats))
        (campaignDAOService.findOrderedSteps)
          .expects(1L, TeamId(id = teamId))
          .returning(Seq(campaignStepWithChildren.copy(children = List())))
        (campaignCacheService.setCampaignStats(_: AllCampaignStats, _: Long, _: CampaignStatus, _: DateTime, _: Long)(using _: SRLogger))
          .expects(allCampaignStats, 1, CampaignStatus.RUNNING, aDate, 37, *)
          .returning(())
        //      (campaignDAO.getCampaignStatsById())
        val updateEmailSendingStatusForm = UpdateEmailSendingStatusForm(
          entityType = EmailSendingEntityTypeData.CampaignData(
            orgId = OrgId(org.id), campaignId = campaignWithStatsAndEmail.id, teamId = teamId
          ),
          orgId = OrgId(org.id),
          sendEmailStatus = SendEmailStatusData.AllowedData()
        )
        (emailSendingStatusDAO.addingEmailSendingStatusTry)
          .expects(updateEmailSendingStatusForm)
          .returning(Success(1L))

        (srUserFeatureUsageEventService.addFeatureUsageEvent)
          .expects(1, *)
          .returning(Success(1))

        val res = campaignService.createCampaign(
          orgId = 1,
          accountId = teamMember.user_id,
          teamId = teamMember.team_id,
          taId = teamMember.ta_id,
          data = createCampaignFormData,
          campaignSettings = Some(campaignSettings.copy(timezone = testTimeZone)),
          permittedAccountIdsForEditCampaigns = permittedAccountIds,
          ownerFirstName = "Demo"
        )

        res.flatMap {
          case Right(data) =>
            assert(true)

          case _ => assert(false)
        }
      }

      it("should success when data is returned from db (using  default timezone ,when incorrect value is passed)") {
        val wrongTimeZone = "SomeWrongTimeZone"

        (() => srUuidUtils.generateCampaignUuid())
          .expects()
          .returning(s"cmp_aa_abcd")

        (campaignDAOService.getNameForNewCampaign)
          .expects(
            "Demo",
            TeamId(id = teamMember.team_id)
          )
          .returning(
            Success("Demo Campaign 1")
          )

        (campaignDAOService.create)
          .expects(
            teamMember.user_id,
            teamMember.team_id,
            teamMember.ta_id,
            createCampaignFormData.name.get,
            Some(campaignSettings.copy(timezone = wrongTimeZone)),
            defaultTimeZone,
            CampaignType.MultiChannel,
            *
          )
          .returning(
            Success(Some(1))
          )

        (campaignDAO.getCampaignBasicInfo(_: Long, _: Long))
          .expects(37, 1)
          .returning(Success(Some(campaignBasicInfo)))
        (campaignCacheService.getCampaignStatsById(_: Long, _: Long)(using _: SRLogger))
          .expects(1, 37, *)
          .returning(Some(allCampaignStats))
        val updateEmailSendingStatusForm = UpdateEmailSendingStatusForm(
          entityType = EmailSendingEntityTypeData.CampaignData(
            orgId = OrgId(org.id), campaignId = campaignWithStatsAndEmail.id, teamId = teamId
          ),
          orgId = OrgId(org.id),
          sendEmailStatus = SendEmailStatusData.AllowedData()
        )
        (emailSendingStatusDAO.addingEmailSendingStatusTry)
          .expects(updateEmailSendingStatusForm)
          .returning(Success(1L))

        (srUserFeatureUsageEventService.addFeatureUsageEvent)
          .expects(1, *)
          .returning(Success(1))
        val res = campaignService.createCampaign(
          orgId = 1,
          accountId = teamMember.user_id,
          teamId = teamMember.team_id,
          taId = teamMember.ta_id,
          data = createCampaignFormData,
          campaignSettings = Some(campaignSettings.copy(timezone = wrongTimeZone)),
          permittedAccountIdsForEditCampaigns = permittedAccountIds,
          ownerFirstName = "Demo"
        )

        res.flatMap {
          case Right(data) =>
            assert(true)

          case _ => assert(false)
        }
      }
      it("should success when data is returned from db(using  createCampaignFormData timezone ,when campaignSettings is None") {
        val testTimeZone = "Asia/Kolkata"

        (() => srUuidUtils.generateCampaignUuid())
          .expects()
          .returning(s"cmp_aa_abcd")

        (campaignDAOService.getNameForNewCampaign)
          .expects(
            "Demo",
            TeamId(id = teamMember.team_id)
          )
          .returning(
            Success("Demo Campaign 1")
          )

        (campaignDAOService.create)
          .expects(
            teamMember.user_id,
            teamMember.team_id,
            teamMember.ta_id,
            createCampaignFormData.name.get,
            None,
            testTimeZone,
            CampaignType.MultiChannel,
            *
          )
          .returning(
            Success(Some(1))
          )

        (campaignDAO.getCampaignBasicInfo(_: Long, _: Long))
          .expects(37, 1)
          .returning(Success(Some(campaignBasicInfo)))
        (campaignCacheService.getCampaignStatsById(_: Long, _: Long)(using _: SRLogger))
          .expects(1, 37, *)
          .returning(Some(allCampaignStats))

        val updateEmailSendingStatusForm = UpdateEmailSendingStatusForm(
          entityType = EmailSendingEntityTypeData.CampaignData(
            orgId = OrgId(org.id), campaignId = campaignWithStatsAndEmail.id, teamId = teamId
          ),
          orgId = OrgId(org.id),
          sendEmailStatus = SendEmailStatusData.AllowedData()
        )
        (emailSendingStatusDAO.addingEmailSendingStatusTry)
          .expects(updateEmailSendingStatusForm)
          .returning(Success(1L))

        (srUserFeatureUsageEventService.addFeatureUsageEvent)
          .expects(1, *)
          .returning(Success(1))
        val res = campaignService.createCampaign(
          orgId = 1,
          accountId = teamMember.user_id,
          teamId = teamMember.team_id,
          taId = teamMember.ta_id,
          data = createCampaignFormData.copy(timezone = Some(testTimeZone)),
          campaignSettings = None,
          permittedAccountIdsForEditCampaigns = permittedAccountIds,
          ownerFirstName = "Demo"
        )

        res.flatMap {
          case Right(data) =>
            assert(true)

          case _ => assert(false)
        }
      }

      it("should throw CampaignCreationError.IncorrectOwnerId when accountId is not present in permittedAccountIds") {
        val testTimeZone = "Asia/Kolkata"



        val res = campaignService.createCampaign(
          orgId = 1,
          accountId = teamMember.user_id,
          teamId = teamMember.team_id,
          taId = teamMember.ta_id,
          data = createCampaignFormData.copy(timezone = Some(testTimeZone)),
          campaignSettings = None,
          permittedAccountIdsForEditCampaigns = Seq(2L), // user_id is 1L
          ownerFirstName = "Demo"
        )

        res.flatMap {
          case Left(CampaignCreationError.InCorrectCampaignOwnerId) => assert(true)

          case _ => assert(false)
        }
      }


    }

    val ERROR = new Throwable("ERROR")
    val campaign = Campaign(
      id = campaign_id,
      uuid = Some(campaign_uuid),
      account_id = 1,
      team_id = 2,
      shared_with_team = true,
      name = "some_name",
      status = CampaignStatus.RUNNING,
      head_step_id = None,
      settings = campaignSettings,
      last_scheduled_at = None,
      created_at = DateTime.now()
    )

    describe("updateStatus") {

      it("should fail to find campaign") {

        (campaignDAO.findCampaignForCampaignUtilsOnly)
          .expects(campaign_id, TeamId(teamId))
          .returning(None)

        val result = campaignService.updateStatus(
          id = campaign_id,
          newStatus = CampaignStatus.UNDER_REVIEW,
          isSupport = false,
          teamId = TeamId(teamId)
        )

        assert(result.failed.get.getMessage == "No Campaign Found For the Id")
      }
      it("should fail to update status because no support access") {

        (campaignDAO.findCampaignForCampaignUtilsOnly)
          .expects(campaign_id, TeamId(teamId))
          .returning(Some(campaign.copy(status = CampaignStatus.UNDER_REVIEW)))

        val result = campaignService.updateStatus(
          id = campaign_id,
          newStatus = CampaignStatus.RUNNING,
          isSupport = false,
          teamId = TeamId(teamId)
        )

        assert(
          result.failed.get.getMessage ==
            s"ACTION NOT ALLOWED, tried to ${CampaignStatus.RUNNING.toString} a campaign that is ${CampaignStatus.UNDER_REVIEW.toString} without access to do so."
        )
      }

      it("should be able to change the campaign status with isSupport") {

        (campaignDAO.findCampaignForCampaignUtilsOnly)
          .expects(campaign_id, TeamId(teamId))
          .returning(Some(campaign.copy(status = CampaignStatus.UNDER_REVIEW)))
        (campaignDAO.updateStatus)
          .expects(campaign_id, CampaignStatus.RUNNING, TeamId(campaign.team_id),
            None, None)
          .returning(Success(None))

        val result = campaignService.updateStatus(
          id = campaign_id,
          newStatus = CampaignStatus.RUNNING,
          isSupport = true,
          teamId = TeamId(teamId)
        )

        assert(result == Success(None))
      }

      it("should not be able to change the status because no isSupport and the new status is Under Review") {

        (campaignDAO.findCampaignForCampaignUtilsOnly)
          .expects(campaign_id, TeamId(teamId))
          .returning(Some(campaign))

        val result = campaignService.updateStatus(
          id = campaign_id,
          newStatus = CampaignStatus.UNDER_REVIEW,
          isSupport = false,
          teamId = TeamId(teamId)
        )

        assert(
          result.failed.get.getMessage ==
            s"ACTION NOT ALLOWED, tried to ${CampaignStatus.UNDER_REVIEW.toString} a campaign that is ${campaign.status} without access to do so.")
      }
    }


    it("should update status when the new status is Under review and we are sending is support") {

      (campaignDAO.findCampaignForCampaignUtilsOnly)
        .expects(campaign_id, TeamId(teamId))
        .returning(Some(campaign))
      (campaignDAO.updateStatus)
        .expects(campaign_id, CampaignStatus.UNDER_REVIEW, TeamId(campaign.team_id),
          None,
          None)
        .returning(Success(None))

      val result = campaignService.updateStatus(
        id = campaign_id,
        newStatus = CampaignStatus.UNDER_REVIEW,
        isSupport = true,
        teamId = TeamId(teamId)
      )

      assert(result == Success(None))
    }


    val linkedin_uuid = "linkedin_uuid_passed"
    val whatsapp_uuid = "whatsapp_uuid_passed"
    val error = new Throwable("Error While Testing")
    val teamId_VC = TeamId(
      id = teamId
    )

    val campaignId_VC = CampaignId(
      id = campaign_id
    )

    val isUpdate_VC = IsUpdate(
      update = true
    )

    val channelSettingUuid_linkedin_VC = ChannelSettingUuid(
      uuid = linkedin_uuid
    )

    val channelSettingUuid_whatasapp_VC = ChannelSettingUuid(
      uuid = whatsapp_uuid
    )

    val channelSettingData = ChannelSettingData(
      channel_type = ChannelType.LinkedinChannel,
      channel_setting_uuid = channelSettingUuid_linkedin_VC,
      team_id = teamId_VC,
      campaign_id = campaignId_VC
    )

    describe("testing campaignService.updateAccountSetting") {

      // Error case - common
      it("should fail if no uuid is passed") {

        val re̵s = campaignService.updateAccountSetting(
          channel_setting_data = channelSettingData.copy(
            channel_setting_uuid = ChannelSettingUuid(
              uuid = ""
            )
          ),
          team_id = teamId,
          campaign_id = campaign_id
        )

        re̵s
          .map {
            case Left(ChannelSetupError.NoUuidPresentError) =>

              assert(true)

            case _ =>
              assert(false)

          }
          .recover {
            err => {
              assert(false)
            }
          }

      }

      // Error case - common
      /* Removing this test case as we are no longer passing whts
      it("should fail both linkedin and whatsapp uuid is passed together") {

        val re̵s = campaignService.updateAccountSetting(
          channel_setting_data = channelSettingData.copy(
            ch = Some("linkedin_uuid_passed"),
            whatsapp_uuid = Some("whatsapp_uuid_passed")
          ),
          team_id = teamId,
          campaign_id = campaign_id
        )

        re̵s
          .flatMap {
            case Left(ChannelSetupError.LinkedinWhatsappUuidPassedTogetherError) =>

              assert(true)

            case _ =>
              assert(false)

          }
          .recover {
            err => {
              assert(false)
            }
          }

      }*/

      // Error case - linkedin
      it("should go to linkedin case and fail as updateOrInsertChannelSettingInCampaign fails") {

        (campaignDAO.isCampaignChannelSettingsExists(_: TeamId, _: CampaignId, _: ChannelType)(_: ExecutionContext, _: SRLogger))
          .expects(teamId_VC, campaignId_VC, ChannelType.LinkedinChannel, *, *)
          .returning(Future.successful(isUpdate_VC))

        (campaignDAO.updateOrInsertChannelSettingInCampaign(_: ChannelSettingUuid, _: CampaignId, _: TeamId, _: ChannelType, _: IsUpdate)(_: ExecutionContext, _: SRLogger))
          .expects(channelSettingUuid_linkedin_VC, campaignId_VC, teamId_VC, ChannelType.LinkedinChannel, isUpdate_VC, *, *)
          .returning(Future.failed(error))


        val re̵s = campaignService.updateAccountSetting(
          channel_setting_data = channelSettingData.copy(
            channel_setting_uuid = channelSettingUuid_linkedin_VC
          ),
          team_id = teamId,
          campaign_id = campaign_id
        )

        re̵s
          .map {
            case _ =>
              assert(false)
          }
          .recover {
            err => {
              assert(err.getMessage == error.getMessage)
            }
          }

      }

      // Error case - linkedin
      it("should go to linkedin case and fail as updateOrInsertChannelSettingInCampaign return None") {

        (campaignDAO.isCampaignChannelSettingsExists(_: TeamId, _: CampaignId, _: ChannelType)(_: ExecutionContext, _: SRLogger))
          .expects(teamId_VC, campaignId_VC, ChannelType.LinkedinChannel, *, *)
          .returning(Future.successful(isUpdate_VC))

        (campaignDAO.updateOrInsertChannelSettingInCampaign(_: ChannelSettingUuid, _: CampaignId, _: TeamId, _: ChannelType, _: IsUpdate)(_: ExecutionContext, _: SRLogger))
          .expects(channelSettingUuid_linkedin_VC, campaignId_VC, teamId_VC, ChannelType.LinkedinChannel, isUpdate_VC, *, *)
          .returning(Future.successful(None))


        val re̵s = campaignService.updateAccountSetting(
          channel_setting_data = channelSettingData.copy(
            channel_setting_uuid = channelSettingUuid_linkedin_VC
          ),
          team_id = teamId,
          campaign_id = campaign_id
        )

        re̵s
          .map {
            case Left(ChannelSetupError.ErrorWhileUpdatingInDb) =>
              assert(true)

            case _ =>
              assert(false)
          }
          .recover {
            err => {
              assert(false)
            }
          }

      }

      // Error case - linkedin
      //    it("should go to linkedin case and fail as find campaign function fails") {
      //
      //      (campaignDAO.isCampaignChannelSettingsExists(_: TeamId, _: CampaignId, _: ChannelType)(_: ExecutionContext, _: SRLogger))
      //        .expects(teamId_VC, campaignId_VC, ChannelType.LinkedinChannel, *, *)
      //        .returning(Future.successful(isUpdate_VC))
      //
      //      (campaignDAO.updateOrInsertChannelSettingInCampaign(_: ChannelSettingUuid, _: CampaignId, _: TeamId, _: ChannelType, _: IsUpdate)(_: ExecutionContext, _: SRLogger))
      //        .expects(channelSettingUuid_linkedin_VC, campaignId_VC, teamId_VC, ChannelType.LinkedinChannel, isUpdate_VC, *, *)
      //        .returning(Future.successful(Some(CampaignIdAndTeamId(
      //          campaign_id = campaign_id,
      //          team_id = teamId
      //        ))))
      //
      //      // mocking find start
      ////      (campaignDAO.getCampaignBasicInfo)
      ////        .expects(teamId, campaign_id)
      ////        .returning(Failure(error))  // failing getCampaignBasicInfo
      //
      //      // mocking find end
      //
      //
      //      val re̵s = campaignService.updateAccountSetting(
      //        channel_setting_data = channelSettingData.copy(
      //          channel_setting_uuid = channelSettingUuid_linkedin_VC
      //        ),
      //        team_id = teamId,
      //        campaign_id = campaign_id
      //      )
      //
      //      re̵s
      //        .map {
      //          case _ =>
      //            assert(true)
      //        }
      //        .recover {
      //          err => {
      //            println(s"\n\nerr ${err}\n\n")
      //            assert(false)
      //          }
      //        }
      //
      //    }

      // Error case - whatsapp
      it("should go to whatsapp case and fail as updateOrInsertChannelSettingInCampaign fails") {

        (campaignDAO.isCampaignChannelSettingsExists(_: TeamId, _: CampaignId, _: ChannelType)(_: ExecutionContext, _: SRLogger))
          .expects(teamId_VC, campaignId_VC, ChannelType.WhatsappChannel, *, *)
          .returning(Future.successful(isUpdate_VC))

        (campaignDAO.updateOrInsertChannelSettingInCampaign(_: ChannelSettingUuid, _: CampaignId, _: TeamId, _: ChannelType, _: IsUpdate)(_: ExecutionContext, _: SRLogger))
          .expects(channelSettingUuid_whatasapp_VC, campaignId_VC, teamId_VC, ChannelType.WhatsappChannel, isUpdate_VC, *, *)
          .returning(Future.failed(error))


        val re̵s = campaignService.updateAccountSetting(
          channel_setting_data = channelSettingData.copy(
            channel_setting_uuid = channelSettingUuid_whatasapp_VC,
            channel_type = ChannelType.WhatsappChannel
          ),
          team_id = teamId,
          campaign_id = campaign_id
        )

        re̵s
          .map {
            case _ =>
              assert(false)
          }
          .recover {
            err => {
              assert(err.getMessage == error.getMessage)
            }
          }

      }

      // Error case - whatsapp
      it("should go to whatsapp case and fail as updateWhatsappAccountInCampaign return None") {

        (campaignDAO.isCampaignChannelSettingsExists(_: TeamId, _: CampaignId, _: ChannelType)(_: ExecutionContext, _: SRLogger))
          .expects(teamId_VC, campaignId_VC, ChannelType.WhatsappChannel, *, *)
          .returning(Future.successful(isUpdate_VC))

        (campaignDAO.updateOrInsertChannelSettingInCampaign(_: ChannelSettingUuid, _: CampaignId, _: TeamId, _: ChannelType, _: IsUpdate)(_: ExecutionContext, _: SRLogger))
          .expects(channelSettingUuid_whatasapp_VC, campaignId_VC, teamId_VC, ChannelType.WhatsappChannel, isUpdate_VC, *, *)
          .returning(Future.successful(None))


        val re̵s = campaignService.updateAccountSetting(
          channel_setting_data = channelSettingData.copy(
            channel_setting_uuid = channelSettingUuid_whatasapp_VC,
            channel_type = ChannelType.WhatsappChannel
          ),
          team_id = teamId,
          campaign_id = campaign_id
        )

        re̵s
          .map {
            case Left(ChannelSetupError.ErrorWhileUpdatingInDb) =>
              assert(true)

            case _ =>
              assert(false)
          }
          .recover {
            err => {
              assert(false)
            }
          }

      }

      // Error case - whatsapp
      //    it("should go to whatsapp case and fail as find campaign function fails") {
      //
      //      (campaignDAO.isCampaignChannelSettingsExists(_: TeamId, _: CampaignId, _: ChannelType)(_: ExecutionContext, _: SRLogger))
      //        .expects(teamId_VC, campaignId_VC, ChannelType.WhatsappChannel, *, *)
      //        .returning(Future.successful(isUpdate_VC))
      //
      //      (campaignDAO.updateOrInsertChannelSettingInCampaign(_: ChannelSettingUuid, _: CampaignId, _: TeamId, _: ChannelType, _: IsUpdate)(_: ExecutionContext, _: SRLogger))
      //        .expects(channelSettingUuid_whatasapp_VC, campaignId_VC, teamId_VC, ChannelType.WhatsappChannel, isUpdate_VC, *, *)
      //        .returning(Future.successful(Some(CampaignIdAndTeamId(
      //          campaign_id = campaign_id,
      //          team_id = teamId
      //        ))))
      //
      //      // mocking find start
      ////      (campaignDAO.getCampaignBasicInfo)
      ////        .expects(teamId, campaign_id)
      ////        .returning(Failure(error)) // failing getCampaignBasicInfo
      //
      //      // mocking find end
      //
      //
      //      val re̵s = campaignService.updateAccountSetting(
      //        channel_setting_data = channelSettingData.copy(
      //          channel_setting_uuid = channelSettingUuid_whatasapp_VC,
      //          channel_type = ChannelType.WhatsappChannel
      //        ),
      //        team_id = teamId,
      //        campaign_id = campaign_id
      //      )
      //
      //      re̵s
      //        .map {
      //          case _ =>
      //            assert(false)
      //        }
      //        .recover {
      //          err => {
      //            assert(true)
      //          }
      //        }
      //
      //    }

      // Error case check if exists - whatsapp
      it("should go to whatsapp case and fail as isCampainChannelSettingsExists fails") {

        (campaignDAO.isCampaignChannelSettingsExists(_: TeamId, _: CampaignId, _: ChannelType)(_: ExecutionContext, _: SRLogger))
          .expects(teamId_VC, campaignId_VC, ChannelType.WhatsappChannel, *, *)
          .returning(Future.failed(error))

        //      (campaignDAO.updateWhatsappAccountInCampaign(_: String, _: Long, _: Long, _: ChannelType, _: Boolean)(_: ExecutionContext, _: SRLogger))
        //        .expects(whatsapp_uuid, teamId, campaign_id, ChannelType.WhatsappChannel, true, *, *)
        //        .returning(Future.successful(Some(CampaignIdAndTeamId(
        //          campaign_id = campaign_id,
        //          team_id = teamId
        //        ))))

        // mocking find start
        //      (campaignDAO.getCampaignBasicInfo)
        //        .expects(teamId, campaign_id)
        //        .returning(Failure(error)) // failing getCampaignBasicInfo

        // mocking find end


        val re̵s = campaignService.updateAccountSetting(
          channel_setting_data = channelSettingData.copy(
            channel_setting_uuid = channelSettingUuid_whatasapp_VC,
            channel_type = ChannelType.WhatsappChannel
          ),
          team_id = teamId,
          campaign_id = campaign_id
        )

        re̵s
          .map {
            case _ =>
              assert(false)
          }
          .recover {
            err => {
              assert(err.getMessage == error.getMessage)
            }
          }

      }

      // Success case - whatsapp
      it("should go to whatsapp case and success") {

        (campaignDAO.isCampaignChannelSettingsExists(_: TeamId, _: CampaignId, _: ChannelType)(_: ExecutionContext, _: SRLogger))
          .expects(teamId_VC, campaignId_VC, ChannelType.WhatsappChannel, *, *)
          .returning(Future.successful(isUpdate_VC))

        (campaignDAO.updateOrInsertChannelSettingInCampaign(_: ChannelSettingUuid, _: CampaignId, _: TeamId, _: ChannelType, _: IsUpdate)(_: ExecutionContext, _: SRLogger))
          .expects(channelSettingUuid_whatasapp_VC, campaignId_VC, teamId_VC, ChannelType.WhatsappChannel, isUpdate_VC, *, *)
          .returning(Future.successful(Some(CampaignIdAndTeamId(
            campaign_id = campaign_id,
            team_id = teamId
          ))))
        (campaignSchedulingMetadataDAO.scheduleForUpdateWhenCampaignIsUpdated)
          .expects(CampaignId(campaign_id), teamId_VC)
          .returning(Success(1))
        // mocking find start
        //      (campaignDAO.getCampaignBasicInfo)
        //        .expects(teamId, campaign_id)
        //        .returning(Success(Some(campaignBasicInfo))) // failing getCampaignBasicInfo
        //
        //      (campaignCacheService.getCampaignStatsById(_: Long, _: Long)(_: SRLogger))
        //        .expects(campaign_id, teamId, *)
        //        .returning(Some(allCampaignStats))

        // mocking find end

        (channelSettingService.getChannelAccountSetting(_: Long, _: Long)(_: ExecutionContext, _: SRLogger))
          .expects(teamId_VC.id, campaignId_VC.id, *, *)
          .returning(Future.successful(List(ChannelSettingData(
            channel_type = ChannelType.WhatsappChannel,
            channel_setting_uuid = channelSettingUuid_whatasapp_VC,
            team_id = teamId_VC,
            campaign_id = campaignId_VC
          )

          )))


        val re̵s = campaignService.updateAccountSetting(
          channel_setting_data = channelSettingData.copy(
            channel_setting_uuid = channelSettingUuid_whatasapp_VC,
            channel_type = ChannelType.WhatsappChannel
          ),
          team_id = teamId,
          campaign_id = campaign_id
        )

        re̵s
          .map {
            case Right(data) =>
              assert(data == List(ChannelSettingData(
                channel_type = ChannelType.WhatsappChannel,
                channel_setting_uuid = channelSettingUuid_whatasapp_VC,
                team_id = teamId_VC,
                campaign_id = campaignId_VC
              )))

            case _ =>
              assert(false)
          }
          .recover {
            err => {
              assert(false)
            }
          }

      }

      // Success case - linkedin
      it("should go to linkedin case and success") {

        (campaignDAO.isCampaignChannelSettingsExists(_: TeamId, _: CampaignId, _: ChannelType)(_: ExecutionContext, _: SRLogger))
          .expects(teamId_VC, campaignId_VC, ChannelType.LinkedinChannel, *, *)
          .returning(Future.successful(isUpdate_VC))

        (campaignDAO.updateOrInsertChannelSettingInCampaign(_: ChannelSettingUuid, _: CampaignId, _: TeamId, _: ChannelType, _: IsUpdate)(_: ExecutionContext, _: SRLogger))
          .expects(channelSettingUuid_linkedin_VC, campaignId_VC, teamId_VC, ChannelType.LinkedinChannel, isUpdate_VC, *, *)
          .returning(Future.successful(Some(CampaignIdAndTeamId(
            campaign_id = campaign_id,
            team_id = teamId
          ))))
        (campaignSchedulingMetadataDAO.scheduleForUpdateWhenCampaignIsUpdated)
          .expects(CampaignId(campaign_id), teamId_VC)
          .returning(Success(1))

        // mocking find start
        //      (campaignDAO.getCampaignBasicInfo)
        //        .expects(teamId, campaign_id)
        //        .returning(Success(Some(campaignBasicInfo))) // failing getCampaignBasicInfo
        //
        //      (campaignCacheService.getCampaignStatsById(_: Long, _: Long)(_: SRLogger))
        //        .expects(campaign_id, teamId, *)
        //        .returning(Some(allCampaignStats))

        // mocking find end

        (channelSettingService.getChannelAccountSetting(_: Long, _: Long)(_: ExecutionContext, _: SRLogger))
          .expects(teamId_VC.id, campaignId_VC.id, *, *)
          .returning(Future.successful(List(ChannelSettingData(
            channel_type = ChannelType.LinkedinChannel,
            channel_setting_uuid = channelSettingUuid_linkedin_VC,
            team_id = teamId_VC,
            campaign_id = campaignId_VC
          ))))


        val re̵s = campaignService.updateAccountSetting(
          channel_setting_data = channelSettingData.copy(
            channel_setting_uuid = channelSettingUuid_linkedin_VC
          ),
          team_id = teamId,
          campaign_id = campaign_id
        )

        re̵s
          .map {
            case Right(data) =>
              assert(data == List(ChannelSettingData(
                channel_type = ChannelType.LinkedinChannel,
                channel_setting_uuid = channelSettingUuid_linkedin_VC,
                team_id = teamId_VC,
                campaign_id = campaignId_VC
              )))

            case _ =>
              assert(false)
          }
          .recover {
            err => {
              assert(false)
            }
          }

      }


    }

    describe("testing campaignDAO.getUpdateOrInsertQuery") {

      it("should give update query when isUpdate is true") {

        val expected =
          """
            |UPDATE
            | campaign_channel_settings
            |   SET channel_settings_uuid = ?
            |   WHERE team_id = ?
            |     AND campaign_id = ?
            |     AND channel_type = ?
            | RETURNING campaign_id, team_id;
            |""".stripMargin
        val qry_str = CampaignDAO.getUpdateOrInsertQuery(
          team_id = teamId_VC,
          campaign_id = campaignId_VC,
          channelType = ChannelType.LinkedinChannel,
          channel_settings_uuid = channelSettingUuid_linkedin_VC,
          isUpdate = isUpdate_VC
        )
          .statement

        val split = expected.split(s"\\s+")
        val rhs = split.reduce((a1, a2) => {
          a1 + " " + a2
        })
        val lhs = qry_str.split("\\s+").reduce((s1, s2) => {
          s1 + " " + s2
        })

        assert(lhs == rhs)

      }

      it("should give insert query when isUpdate is false") {

        val expected =
          """
            |INSERT
            | INTO
            | campaign_channel_settings
            |  ( team_id,
            |   campaign_id,
            |   channel_type,
            |   channel_settings_uuid
            |   )
            |   VALUES
            |   ( ?, ?, ?, ? )
            | RETURNING campaign_id, team_id;
            |""".stripMargin
        val qry_str = CampaignDAO.getUpdateOrInsertQuery(
          team_id = teamId_VC,
          campaign_id = campaignId_VC,
          channelType = ChannelType.LinkedinChannel,
          channel_settings_uuid = channelSettingUuid_whatasapp_VC,
          isUpdate = isUpdate_VC.copy(update = false)
        )
          .statement

        val split = expected.split(s"\\s+")
        val rhs = split.reduce((a1, a2) => {
          a1 + " " + a2
        })
        val lhs = qry_str.split("\\s+").reduce((s1, s2) => {
          s1 + " " + s2
        })

        assert(lhs == rhs)

      }
    }

    describe("testing TimezoneUtils.getDayOfWeekInTimezone") {
      it("should fail if 11th Feb, 2023 comes out to be other than Saturday") {
        val result = TimezoneUtils.getDayOfWeekInTimezone(
          currTime = DateTime.parse("2023-02-11"),
          timezone = "Asia/Kolkata"
        )

        assert(result == 6)
      }

      it("should fail if 24th Nov, 2024 comes out to be other than Sunday") {
        val result = TimezoneUtils.getDayOfWeekInTimezone(
          currTime = DateTime.parse("2024-11-24"),
          timezone = "Asia/Kolkata"
        )

        assert(result == 0)
      }

      it("should fail if 10th Nov, 2022 comes out to be other than Thursday") {
        val result = TimezoneUtils.getDayOfWeekInTimezone(
          currTime = DateTime.parse("2022-11-10"),
          timezone = "Asia/Kolkata"
        )

        assert(result == 4)
      }

      it("should fail if 11th July, 2023 comes out to be other than Tuesday") {
        val result = TimezoneUtils.getDayOfWeekInTimezone(
          currTime = DateTime.parse("2023-07-11"),
          timezone = "Asia/Kolkata"
        )

        assert(result == 2)
      }
    }

    describe("testing CampaignSendReportCronService.filterCampaignsOnDaysPreference") {
      it("should filter campaigns that should be running on 11th Feb, 2023 (Saturday)") {

        val stuckCampaigns = List(StuckCampaign(
          campaignId = CampaignId(id = 3),
          teamId = TeamId(id = 5),
          timezone = "Asia/Kolkata",
          days_preference = List(false, false, false, false, false, false, true)
        ), StuckCampaign(
          campaignId = CampaignId(id = 7),
          teamId = TeamId(id = 11),
          timezone = "Asia/Kolkata",
          days_preference = List(true, true, true, true, true, true, false)
        ))

        val result = CampaignSendReportService.filterCampaignsOnDaysPreference(
          currTime = DateTime.parse("2023-02-11"),
          campaigns = stuckCampaigns
        )

        val result2 = CampaignSendReportService.filterCampaignsOnDaysPreference(
          currTime = DateTime.parse("2023-02-12"),
          campaigns = stuckCampaigns
        )

        assert(result == List(stuckCampaigns.head))
        assert(result2 == List(stuckCampaigns(1)))
      }
    }

    describe("testing CampaignService.getCampaignPauseReasonAndCounts") {
      it("should give server_error as pause reason if campaign is not found in DB") {
        (campaignDAO.findCampaignForCampaignUtilsOnly)
          .expects(campaign_id, TeamId(teamId))
          .returning(None)

        val result = campaignService.getCampaignPauseReasonAndCounts(
          campaignId = CampaignId(campaign_id),
          teamId = TeamId(teamId)
        )

        assert(result.campaignPauseReason == CampaignPauseReason.ServerErrorOccurredWhileAnalyzing)
      }

      it("should give server_error as pause reason if email Account is not found in DB") {
        (campaignDAO.findCampaignForCampaignUtilsOnly)
          .expects(campaign_id, TeamId(teamId))
          .returning(Some(campaign))

        (emailSettingDAO.find(_: Seq[EmailSettingId], _: TeamId))
          .expects(Seq(EmailSettingId(senderEmailSettingId)), teamId_VC)
          .returning(Seq())

        val result = campaignService.getCampaignPauseReasonAndCounts(
          campaignId = CampaignId(campaign_id),
          teamId = TeamId(teamId)
        )

        assert(result.campaignPauseReason == CampaignPauseReason.ServerErrorOccurredWhileAnalyzing)
      }

      it("should give service provider error as pause reason if there was error while integrating email account") {
        (campaignDAO.findCampaignForCampaignUtilsOnly)
          .expects(campaign_id, TeamId(teamId))
          .returning(Some(campaign))

        (emailSettingDAO.find(_: Seq[EmailSettingId], _: TeamId))
          .expects(Seq(EmailSettingId(senderEmailSettingId)), teamId_VC)
          .returning(Seq(emailAccountSetting.copy(error = Some("Failed Authorization"))))

        val result = campaignService.getCampaignPauseReasonAndCounts(
          campaignId = CampaignId(campaign_id),
          teamId = TeamId(teamId)
        )

        assert(result.campaignPauseReason == CampaignPauseReason.ServiceProviderError)
      }

      it("should give server error if getSentOrScheduledProspectsCountForEmail call fails.") {
        (campaignDAO.findCampaignForCampaignUtilsOnly)
          .expects(campaign_id, TeamId(teamId))
          .returning(Some(campaign))

        (emailSettingDAO.find(_: Seq[EmailSettingId], _: TeamId))
          .expects(Seq(EmailSettingId(senderEmailSettingId)), teamId_VC)
          .returning(Seq(emailAccountSetting))

        (campaignProspectDAO.getScheduledProspectsCountForCampaign)
          .expects(Logger, Seq(EmailSettingId(senderEmailSettingId)), teamId, Seq((campaign_id, campaignTimezone)), true)
          .returning(Success(Seq()))

        (campaignProspectDAO.getSentOrScheduledProspectsCountForEmail)
          .expects(Seq(EmailSettingId(senderEmailSettingId)),
            campaignTimezone, true, Logger, TeamId(teamId)).returning(Failure(error))

        val result = campaignService.getCampaignPauseReasonAndCounts(
          campaignId = CampaignId(campaign_id),
          teamId = TeamId(teamId)
        )

        assert(result.campaignPauseReason == CampaignPauseReason.ServerErrorOccurredWhileAnalyzing)
      }

      it("should give email account send limit error.") {
        (campaignDAO.findCampaignForCampaignUtilsOnly)
          .expects(campaign_id, TeamId(teamId))
          .returning(Some(campaign))

        (emailSettingDAO.find(_: Seq[EmailSettingId], _: TeamId))
          .expects(Seq(EmailSettingId(senderEmailSettingId)), teamId_VC)
          .returning(Seq(emailAccountSetting))

        (campaignProspectDAO.getScheduledProspectsCountForCampaign)
          .expects(Logger, Seq(EmailSettingId(senderEmailSettingId)), teamId, Seq((campaign_id, campaignTimezone)), true)
          .returning(Success(Seq()))

        (campaignProspectDAO.getSentOrScheduledProspectsCountForEmail)
          .expects(Seq(EmailSettingId(senderEmailSettingId)), campaignTimezone, true, Logger,TeamId(teamId))
          .returning(Success(Map(EmailSettingId(senderEmailSettingId) ->emailAccountSetting.quota_per_day)))

        val result = campaignService.getCampaignPauseReasonAndCounts(
          campaignId = CampaignId(campaign_id),
          teamId = TeamId(teamId)
        )

        assert(result.campaignPauseReason == CampaignPauseReason.EmailAccountSendLimitReached)
      }

      it("should give server error when getScheduledProspectsCountForCampaign call fails.") {
        (campaignDAO.findCampaignForCampaignUtilsOnly)
          .expects(campaign_id, TeamId(teamId))
          .returning(Some(campaign))

        (emailSettingDAO.find(_: Seq[EmailSettingId], _: TeamId))
          .expects(Seq(EmailSettingId(senderEmailSettingId)), teamId_VC)
          .returning(Seq(emailAccountSetting))

        (campaignProspectDAO.getScheduledProspectsCountForCampaign)
          .expects(Logger, Seq(EmailSettingId(senderEmailSettingId)), teamId, Seq((campaign_id, campaignTimezone)), true)
          .returning(Failure(error))

        val result = campaignService.getCampaignPauseReasonAndCounts(
          campaignId = CampaignId(campaign_id),
          teamId = TeamId(teamId)
        )

        assert(result.campaignPauseReason == CampaignPauseReason.ServerErrorOccurredWhileAnalyzing)
      }

      it("should give campaign limit error.") {
        (campaignDAO.findCampaignForCampaignUtilsOnly)
          .expects(campaign_id, TeamId(teamId))
          .returning(Some(campaign))

        (emailSettingDAO.find(_: Seq[EmailSettingId], _: TeamId))
          .expects(Seq(EmailSettingId(senderEmailSettingId)), teamId_VC)
          .returning(Seq(emailAccountSetting))

        (campaignProspectDAO.getScheduledProspectsCountForCampaign)
          .expects(Logger, Seq(EmailSettingId(senderEmailSettingId)), teamId, Seq((campaign_id, campaignTimezone)), true)
          .returning(Success(Seq(scheduledProspectsCountForCampaignEmail.copy(newCount = campaign.settings.max_emails_per_day))))

        val result = campaignService.getCampaignPauseReasonAndCounts(
          campaignId = CampaignId(campaign_id),
          teamId = TeamId(teamId)
        )

        assert(result.campaignPauseReason == CampaignPauseReason.CampaignLimitReached)
      }

      it("should give all prospects completed pause reason.") {
        (campaignDAO.findCampaignForCampaignUtilsOnly)
          .expects(campaign_id, TeamId(teamId))
          .returning(Some(campaign))

        (emailSettingDAO.find(_: Seq[EmailSettingId], _: TeamId))
          .expects(Seq(EmailSettingId(senderEmailSettingId)), teamId_VC)
          .returning(Seq(emailAccountSetting))

        (campaignProspectDAO.getSentOrScheduledProspectsCountForEmail)
          .expects(Seq(EmailSettingId(senderEmailSettingId)),
            campaignTimezone, true, Logger, TeamId(teamId))
          .returning(Success(Map(EmailSettingId(senderEmailSettingId) -> (emailAccountSetting.quota_per_day - 1))))

        (campaignProspectDAO.getScheduledProspectsCountForCampaign)
          .expects(Logger, Seq(EmailSettingId(senderEmailSettingId)), teamId, Seq((campaign_id, campaignTimezone)), true)
          .returning(Success(Seq(scheduledProspectsCountForCampaignEmail)))

        (prospectDAOService.getProspectCategoryId(_:TeamId, _:ProspectCategory.Value, _:Option[Account])(using _:SRLogger))
          .expects(TeamId(teamId), ProspectCategory.DO_NOT_CONTACT, None, *)
          .returning(Success(ProspectCategoryId(DONOTCONTACT_CATEGORY_ID_value)))

        (campaignProspectDAO.getUnsentProspectsCounts)
          .expects(CampaignId(campaign_id), TeamId(teamId), campaignTimezone, DONOTCONTACT_CATEGORY_ID)
          .returning(Some(unsentProspectsCounts.copy(total_unsent_prospects = 0)))

        val result = campaignService.getCampaignPauseReasonAndCounts(
          campaignId = CampaignId(campaign_id),
          teamId = TeamId(teamId)
        )

        assert(result.campaignPauseReason == CampaignPauseReason.AllProspectsCompleted)
      }

      it("should give prospects have invalid emails as pause reason.") {
        (campaignDAO.findCampaignForCampaignUtilsOnly)
          .expects(campaign_id, TeamId(teamId))
          .returning(Some(campaign))

        (emailSettingDAO.find(_: Seq[EmailSettingId], _: TeamId))
          .expects(Seq(EmailSettingId(senderEmailSettingId)), teamId_VC)
          .returning(Seq(emailAccountSetting))

        (campaignProspectDAO.getSentOrScheduledProspectsCountForEmail)
          .expects(Seq(EmailSettingId(senderEmailSettingId)), campaignTimezone, true, Logger, TeamId(teamId))
          .returning(Success(Map(EmailSettingId(senderEmailSettingId) ->(emailAccountSetting.quota_per_day - 1))))

        (campaignProspectDAO.getScheduledProspectsCountForCampaign)
          .expects(Logger, Seq(EmailSettingId(senderEmailSettingId)), teamId, Seq((campaign_id, campaignTimezone)), true)
          .returning(Success(Seq(scheduledProspectsCountForCampaignEmail)))

        (prospectDAOService.getProspectCategoryId(_: TeamId, _: ProspectCategory.Value, _: Option[Account])(using _: SRLogger))
          .expects(TeamId(teamId), ProspectCategory.DO_NOT_CONTACT, None, *)
          .returning(Success(ProspectCategoryId(DONOTCONTACT_CATEGORY_ID_value)))

        (campaignProspectDAO.getUnsentProspectsCounts)
          .expects(CampaignId(campaign_id), TeamId(teamId), campaignTimezone, DONOTCONTACT_CATEGORY_ID)
          .returning(Some(unsentProspectsCounts.copy(total_unsent_prospects_with_invalid_emails = 5)))

        val result = campaignService.getCampaignPauseReasonAndCounts(
          campaignId = CampaignId(campaign_id),
          teamId = TeamId(teamId)
        )

        assert(result.campaignPauseReason == CampaignPauseReason.ProspectsHaveInvalidEmails)
      }

      it("should give prospects have missing merge tags as pause reason.") {
        (campaignDAO.findCampaignForCampaignUtilsOnly)
          .expects(campaign_id, TeamId(teamId))
          .returning(Some(campaign))

        (emailSettingDAO.find(_: Seq[EmailSettingId], _: TeamId))
          .expects(Seq(EmailSettingId(senderEmailSettingId)), teamId_VC)
          .returning(Seq(emailAccountSetting))

        (campaignProspectDAO.getSentOrScheduledProspectsCountForEmail)
          .expects(Seq(EmailSettingId(senderEmailSettingId)), campaignTimezone, true, Logger, TeamId(teamId))
          .returning(Success(Map(EmailSettingId(senderEmailSettingId) ->(emailAccountSetting.quota_per_day - 1))))

        (campaignProspectDAO.getScheduledProspectsCountForCampaign)
          .expects(Logger, Seq(EmailSettingId(senderEmailSettingId)), teamId, Seq((campaign_id, campaignTimezone)), true)
          .returning(Success(Seq(scheduledProspectsCountForCampaignEmail)))

        (prospectDAOService.getProspectCategoryId(_: TeamId, _: ProspectCategory.Value, _: Option[Account])(using _: SRLogger))
          .expects(TeamId(teamId), ProspectCategory.DO_NOT_CONTACT, None, *)
          .returning(Success(ProspectCategoryId(DONOTCONTACT_CATEGORY_ID_value)))

        (campaignProspectDAO.getUnsentProspectsCounts)
          .expects(CampaignId(campaign_id), TeamId(teamId), campaignTimezone, DONOTCONTACT_CATEGORY_ID)
          .returning(Some(unsentProspectsCounts.copy(total_unsent_prospects_with_missing_merge_tags = 5)))

        val result = campaignService.getCampaignPauseReasonAndCounts(
          campaignId = CampaignId(campaign_id),
          teamId = TeamId(teamId)
        )

        assert(result.campaignPauseReason == CampaignPauseReason.ProspectsHaveMissingMergeTags)
      }

      it("should give rest prospects are in DNC as pause reason.") {
        (campaignDAO.findCampaignForCampaignUtilsOnly)
          .expects(campaign_id, TeamId(teamId))
          .returning(Some(campaign))

        (emailSettingDAO.find(_: Seq[EmailSettingId], _: TeamId))
          .expects(Seq(EmailSettingId(senderEmailSettingId)), teamId_VC)
          .returning(Seq(emailAccountSetting))

        (campaignProspectDAO.getSentOrScheduledProspectsCountForEmail)
          .expects(Seq(EmailSettingId(senderEmailSettingId)), campaignTimezone, true, Logger, TeamId(teamId))
          .returning(Success(Map(EmailSettingId(senderEmailSettingId) ->(emailAccountSetting.quota_per_day - 1))))

        (campaignProspectDAO.getScheduledProspectsCountForCampaign)
          .expects(Logger, Seq(EmailSettingId(senderEmailSettingId)), teamId, Seq((campaign_id, campaignTimezone)), true)
          .returning(Success(Seq(scheduledProspectsCountForCampaignEmail)))

        (prospectDAOService.getProspectCategoryId(_: TeamId, _: ProspectCategory.Value, _: Option[Account])(using _: SRLogger))
          .expects(TeamId(teamId), ProspectCategory.DO_NOT_CONTACT, None, *)
          .returning(Success(ProspectCategoryId(DONOTCONTACT_CATEGORY_ID_value)))

        (campaignProspectDAO.getUnsentProspectsCounts)
          .expects(CampaignId(campaign_id), TeamId(teamId), campaignTimezone, DONOTCONTACT_CATEGORY_ID)
          .returning(Some(unsentProspectsCounts.copy(total_unsent_prospects_in_dnc = 37)))

        val result = campaignService.getCampaignPauseReasonAndCounts(
          campaignId = CampaignId(campaign_id),
          teamId = TeamId(teamId)
        )

        assert(result.campaignPauseReason == CampaignPauseReason.RestProspectsAreInDNC)
      }

      it("should give no prospects in current timezone as pause reason.") {
        (campaignDAO.findCampaignForCampaignUtilsOnly)
          .expects(campaign_id, TeamId(teamId))
          .returning(Some(campaign))

        (emailSettingDAO.find(_: Seq[EmailSettingId], _: TeamId))
          .expects(Seq(EmailSettingId(senderEmailSettingId)), teamId_VC)
          .returning(Seq(emailAccountSetting))

        (campaignProspectDAO.getSentOrScheduledProspectsCountForEmail)
          .expects(Seq(EmailSettingId(senderEmailSettingId)), campaignTimezone, true, Logger, TeamId(teamId))
          .returning(Success(Map(EmailSettingId(senderEmailSettingId) -> (emailAccountSetting.quota_per_day - 1))))

        (campaignProspectDAO.getScheduledProspectsCountForCampaign)
          .expects(Logger, Seq(EmailSettingId(senderEmailSettingId)), teamId, Seq((campaign_id, campaignTimezone)), true)
          .returning(Success(Seq(scheduledProspectsCountForCampaignEmail)))

        (prospectDAOService.getProspectCategoryId(_: TeamId, _: ProspectCategory.Value, _: Option[Account])(using _: SRLogger))
          .expects(TeamId(teamId), ProspectCategory.DO_NOT_CONTACT, None, *)
          .returning(Success(ProspectCategoryId(DONOTCONTACT_CATEGORY_ID_value)))

        (campaignProspectDAO.getUnsentProspectsCounts)
          .expects(CampaignId(campaign_id), TeamId(teamId), campaignTimezone, DONOTCONTACT_CATEGORY_ID)
          .returning(Some(unsentProspectsCounts.copy(total_unsent_prospects_in_same_tz = 0)))

        val result = campaignService.getCampaignPauseReasonAndCounts(
          campaignId = CampaignId(campaign_id),
          teamId = TeamId(teamId)
        )

        assert(result.campaignPauseReason == CampaignPauseReason.NoProspectLeftInCurrentTimezone)
      }

      it("should give unknown reason as pause reason.") {
        (campaignDAO.findCampaignForCampaignUtilsOnly)
          .expects(campaign_id, TeamId(teamId))
          .returning(Some(campaign))

        (emailSettingDAO.find(_: Seq[EmailSettingId], _: TeamId))
          .expects(Seq(EmailSettingId(senderEmailSettingId)), teamId_VC)
          .returning(Seq(emailAccountSetting))

        (campaignProspectDAO.getSentOrScheduledProspectsCountForEmail)
          .expects(Seq(EmailSettingId(senderEmailSettingId)), campaignTimezone, true, Logger, TeamId(teamId))
          .returning(Success(Map(EmailSettingId(senderEmailSettingId) ->(emailAccountSetting.quota_per_day - 1))))

        (campaignProspectDAO.getScheduledProspectsCountForCampaign)
          .expects(Logger, Seq(EmailSettingId(senderEmailSettingId)), teamId, Seq((campaign_id, campaignTimezone)), true)
          .returning(Success(Seq(scheduledProspectsCountForCampaignEmail)))

        (prospectDAOService.getProspectCategoryId(_: TeamId, _: ProspectCategory.Value, _: Option[Account])(using _: SRLogger))
          .expects(TeamId(teamId), ProspectCategory.DO_NOT_CONTACT, None, *)
          .returning(Success(ProspectCategoryId(DONOTCONTACT_CATEGORY_ID_value)))

        (campaignProspectDAO.getUnsentProspectsCounts)
          .expects(CampaignId(campaign_id), TeamId(teamId), campaignTimezone, DONOTCONTACT_CATEGORY_ID)
          .returning(Some(unsentProspectsCounts))

        val result = campaignService.getCampaignPauseReasonAndCounts(
          campaignId = CampaignId(campaign_id),
          teamId = TeamId(teamId)
        )

        assert(result.campaignPauseReason == CampaignPauseReason.UnknownReason)
      }
    }

    val max_emails_per_day_50_VC = MaxEmailsPerDay(
      max_emails_per_day = 3
    )

    describe("testing CampaignService.updateAccountSetting") {

      it("should throw error when max_emails_per_day is sent greater than 500 ") {

        val res = campaignService.updateMaxEmailsPerDay(
          campaignId = campaignId_VC,
          max_emails_per_day = max_emails_per_day_50_VC
            .copy(max_emails_per_day = 510),
          team_id = teamId_VC
        )

        res match {
          case Failure(e) =>
            assert(false)

          case Success(data) =>
            data match {

              case Left(UpdateMaxEmailsPerDayError.ValidationFailed(err)) =>
                //              assert(false)
                assert(err.getMessage == "Max emails per day can't be greater than 500")
              case _ =>
                assert(false)
            }
        }

      }

      it("should throw error when max_emails_per_day sent is lower than 1 ") {

        val res = campaignService.updateMaxEmailsPerDay(
          campaignId = campaignId_VC,
          max_emails_per_day = max_emails_per_day_50_VC
            .copy(max_emails_per_day = -10),
          team_id = teamId_VC
        )

        res match {
          case Failure(e) =>
            assert(false)

          case Success(data) =>
            data match {

              case Left(UpdateMaxEmailsPerDayError.ValidationFailed(err)) =>
                //              assert(false)
                assert(err.getMessage == "Max emails per day can't be lower than 1")
              case _ =>
                assert(false)
            }
        }

      }

      it("should throw error when campaignDAO.updateMaxEmailsPerDay fails while updating db") {

        (campaignDAO.updateMaxEmailsPerDay)
          .expects(campaignId_VC, teamId_VC, max_emails_per_day_50_VC)
          .returning(Failure(error))

        val res = campaignService.updateMaxEmailsPerDay(
          campaignId = campaignId_VC,
          max_emails_per_day = max_emails_per_day_50_VC, // 50 sending now
          team_id = teamId_VC
        )

        res match {
          case Failure(e) =>
            assert(false)

          case Success(data) =>
            data match {

              case Left(UpdateMaxEmailsPerDayError.ErrorWhileUpdation(err)) =>
                assert(err.getMessage == error.getMessage)
              case _ =>
                assert(false)
            }
        }

      }

      it("should throw error when campaignDAO.updateMaxEmailsPerDay None instead of campaignId and teamId") {

        (campaignDAO.updateMaxEmailsPerDay)
          .expects(campaignId_VC, teamId_VC, max_emails_per_day_50_VC)
          .returning(Success(None))

        val res = campaignService.updateMaxEmailsPerDay(
          campaignId = campaignId_VC,
          max_emails_per_day = max_emails_per_day_50_VC, // 50 sending now
          team_id = teamId_VC
        )

        res match {
          case Failure(e) =>
            assert(false)

          case Success(data) =>
            data match {

              case Left(UpdateMaxEmailsPerDayError.NoneReturnedWhileUpdate) =>
                assert(true)
              case _ =>
                assert(false)
            }
        }

      }

      /**
       * We are expecting update successful to db, which returned campaignId and teamId
       * now we are going to fetch campaignDetails with the help of above results, and will pass
       * it in right of either. if find fails it will be handled in controller error as server error
       */
      it("should success when campaignDAO.updateMaxEmailsPerDay returns campaignId and teamId") {

        (campaignDAO.getCampaignBasicInfo(_: Long, _: Long))
          .expects(teamId_VC.id, campaignId_VC.id)
          .returning(Success(Some(campaignBasicInfo)))

        (campaignCacheService.getCampaignStatsById(_: Long, _: Long)(using _: SRLogger))
          .expects(campaignId_VC.id, teamId_VC.id, *)
          .returning(Some(allCampaignStats))

        (campaignDAO.updateMaxEmailsPerDay)
          .expects(campaignId_VC, teamId_VC, max_emails_per_day_50_VC)
          .returning(Success(Some(CampaignIdAndTeamId(
            campaign_id = campaign_id,
            team_id = teamId
          ))))

        val res = campaignService.updateMaxEmailsPerDay(
          campaignId = campaignId_VC,
          max_emails_per_day = max_emails_per_day_50_VC, // 50 sending now
          team_id = teamId_VC
        )

        res match {
          case Failure(e) =>
            assert(false)

          case Success(data) =>
            data match {

              case Right(Some(campaignWithStatsAndEmail)) =>
                assert(true)
              case _ =>
                assert(false)
            }
        }

      }

    }

    describe("testing CampaignService.deleteCampaignAndAssociatedTasks") {
      it("should result into Left(DeleteCampaignTasksError.DeleteCampaignsSqlError(error))") {


        (campaignDAO.checkIfCampaignInWebhookFilter (_: CampaignId, _: TeamId))
          .expects(CampaignId(2L), TeamId(id = 3L))
          .returning(Success(false))

        (() => dbUtils.startLocalTx())
          .expects()
          .returning(DbAndSession(null, null))

        (selectAndPublishForDeletionService.selectTaskAndEmailToBeDeletedForDeleteVariantFlow(_: DeletionReason, _: DeleteEmailsScheduledType )(using _: SRLogger))
          .expects(DeletionReason.DeletedCampaign, DeleteEmailsScheduledType.DeleteUnsentByCampaignId(
            campaignId = 2L,
            teamId = TeamId(3L),
            senderEmailSettingIds = Seq()
          ), *)
          .returning(Success(Seq(MQDeletionAndRevertDataV2(
            deletion_reason = DeletionReason.DeletedCampaign,
            deleteAndRevertData = MqDeleteAndRevertDataMsgV2.TaskAndEmailMessage(
              task_id = "task_id",
              email_scheduled_id = 3,
              team_id = TeamId(id = 23),
              sender_email_setting_id = None
            ),
            reverted_by = "" ,
            revert_log_trace_id = ""
          ))))

        (emailsScheduledDeleteService.processMessageForDeletionAndRevert(_: MQDeletionAndRevertDataV2)(using _: SRLogger))
          .expects(MQDeletionAndRevertDataV2(
            deletion_reason = DeletionReason.DeletedCampaign,
            deleteAndRevertData = MqDeleteAndRevertDataMsgV2.TaskAndEmailMessage(
              task_id = "task_id",
              email_scheduled_id = 3,
              team_id = TeamId(id = 23),
              sender_email_setting_id = None
            ),
            reverted_by = "",
            revert_log_trace_id = ""
          ), *)
          .returning(Success(11))


        (campaignDAO.deleteCampaign(_: Long, _: TeamId)(_: DBSession))
          .expects(2L, TeamId(id = 3L), *)
          .returning(Failure(error))

        (dbUtils.commitAndCloseSession)
          .expects(*)
          .returning(())

        val result = campaignService.deleteCampaignAndAssociatedTasks(CampaignId(2), TeamId(3L))
        assert(result == Left(DeleteCampaignTasksError.DeleteCampaignsSqlError(error)))
      }

      it("should result into Left(DeleteCampaignTasksError.NoCampaignFoundToDelete)") {

        (campaignDAO.checkIfCampaignInWebhookFilter (_: CampaignId, _: TeamId))
          .expects(CampaignId(2L), TeamId(id = 3L))
          .returning(Success(false))

        (() => dbUtils.startLocalTx())
          .expects()
          .returning(DbAndSession(null, null))

        (selectAndPublishForDeletionService.selectTaskAndEmailToBeDeletedForDeleteVariantFlow(_: DeletionReason, _: DeleteEmailsScheduledType)(using _: SRLogger))
          .expects(DeletionReason.DeletedCampaign, DeleteEmailsScheduledType.DeleteUnsentByCampaignId(
            campaignId = 2L,
            teamId = TeamId(3L),
            senderEmailSettingIds = Seq()
          ), *)
          .returning(Success(Seq(MQDeletionAndRevertDataV2(
            deletion_reason = DeletionReason.DeletedCampaign,
            deleteAndRevertData = MqDeleteAndRevertDataMsgV2.TaskAndEmailMessage(
              task_id = "task_id",
              email_scheduled_id = 3,
              team_id = TeamId(id = 23),
              sender_email_setting_id = None
            ),
            reverted_by = "",
            revert_log_trace_id = ""
          ))))

        (emailsScheduledDeleteService.processMessageForDeletionAndRevert(_: MQDeletionAndRevertDataV2)(using _: SRLogger))
          .expects(MQDeletionAndRevertDataV2(
            deletion_reason = DeletionReason.DeletedCampaign,
            deleteAndRevertData = MqDeleteAndRevertDataMsgV2.TaskAndEmailMessage(
              task_id = "task_id",
              email_scheduled_id = 3,
              team_id = TeamId(id = 23),
              sender_email_setting_id = None
            ),
            reverted_by = "",
            revert_log_trace_id = ""
          ), *)
          .returning(Success(11))


//        (taskService.deleteNotCompletedTaskForDeletedCampaign(_: CampaignId, _: TeamId)(_: DBSession))
//          .expects(CampaignId(2L), TeamId(3L), *)
//          .returning(Success(List()))

        (campaignDAO.deleteCampaign(_: Long, _: TeamId)(_: DBSession))
          .expects(2L, TeamId(3L), *)
          .returning(Success(0))

        (dbUtils.commitAndCloseSession)
          .expects(*)
          .returning(())

        val result = campaignService.deleteCampaignAndAssociatedTasks(CampaignId(2), TeamId(3L))
        assert(result == Left(DeleteCampaignTasksError.NoCampaignFoundToDelete))
      }

      it("should succeed with  0 selctedAndPublishedForDeletionCount") {

        (campaignDAO.checkIfCampaignInWebhookFilter (_: CampaignId, _: TeamId))
          .expects(CampaignId(2L), TeamId(id = 3L))
          .returning(Success(false))

        (() => dbUtils.startLocalTx())
          .expects()
          .returning(DbAndSession(null, null))

        (selectAndPublishForDeletionService.selectTaskAndEmailToBeDeletedForDeleteVariantFlow(_: DeletionReason, _: DeleteEmailsScheduledType)(using _: SRLogger))
          .expects(DeletionReason.DeletedCampaign, DeleteEmailsScheduledType.DeleteUnsentByCampaignId(
            campaignId = 2L,
            teamId = TeamId(3L),
            senderEmailSettingIds = Seq()
          ), *)
          .returning(Success(Seq(MQDeletionAndRevertDataV2(
            deletion_reason = DeletionReason.DeletedCampaign,
            deleteAndRevertData = MqDeleteAndRevertDataMsgV2.TaskAndEmailMessage(
              task_id = "task_id",
              email_scheduled_id = 3,
              team_id = TeamId(id = 23),
              sender_email_setting_id = None
            ),
            reverted_by = "",
            revert_log_trace_id = ""
          ))))

        (emailsScheduledDeleteService.processMessageForDeletionAndRevert(_: MQDeletionAndRevertDataV2)(using _: SRLogger))
          .expects(MQDeletionAndRevertDataV2(
            deletion_reason = DeletionReason.DeletedCampaign,
            deleteAndRevertData = MqDeleteAndRevertDataMsgV2.TaskAndEmailMessage(
              task_id = "task_id",
              email_scheduled_id = 3,
              team_id = TeamId(id = 23),
              sender_email_setting_id = None
            ),
            reverted_by = "",
            revert_log_trace_id = ""
          ), *)
          .returning(Success(11))


        (campaignDAO.deleteCampaign(_: Long, _: TeamId)(_: DBSession))
          .expects(2L, TeamId(3L), *)
          .returning(Success(1))

        (dbUtils.commitAndCloseSession)
          .expects(*)
          .returning(())

        val result = campaignService.deleteCampaignAndAssociatedTasks(CampaignId(2), TeamId(3L))
        assert(result == Right(1))
      }

      it("should succeed with 20 published count for deletion") {

        (campaignDAO.checkIfCampaignInWebhookFilter (_: CampaignId, _: TeamId))
          .expects(CampaignId(2L), TeamId(id = 3L))
          .returning(Success(false))

        (() => dbUtils.startLocalTx())
          .expects()
          .returning(DbAndSession(null, null))

        (selectAndPublishForDeletionService.selectTaskAndEmailToBeDeletedForDeleteVariantFlow(_: DeletionReason, _: DeleteEmailsScheduledType)(using _: SRLogger))
          .expects(DeletionReason.DeletedCampaign, DeleteEmailsScheduledType.DeleteUnsentByCampaignId(
            campaignId = 2L,
            teamId = TeamId(3L),
            senderEmailSettingIds = Seq()
          ), *)
          .returning(Success(Seq(MQDeletionAndRevertDataV2(
            deletion_reason = DeletionReason.DeletedCampaign,
            deleteAndRevertData = MqDeleteAndRevertDataMsgV2.TaskAndEmailMessage(
              task_id = "task_id",
              email_scheduled_id = 3,
              team_id = TeamId(id = 23),
              sender_email_setting_id = None
            ),
            reverted_by = "",
            revert_log_trace_id = ""
          ))))

        (emailsScheduledDeleteService.processMessageForDeletionAndRevert(_: MQDeletionAndRevertDataV2)(using _: SRLogger))
          .expects(MQDeletionAndRevertDataV2(
            deletion_reason = DeletionReason.DeletedCampaign,
            deleteAndRevertData = MqDeleteAndRevertDataMsgV2.TaskAndEmailMessage(
              task_id = "task_id",
              email_scheduled_id = 3,
              team_id = TeamId(id = 23),
              sender_email_setting_id = None
            ),
            reverted_by = "",
            revert_log_trace_id = ""
          ), *)
          .returning(Success(11))

        (campaignDAO.deleteCampaign(_: Long, _: TeamId)(_: DBSession))
          .expects(2L, TeamId(3L),*)
          .returning(Success(1))

        (dbUtils.commitAndCloseSession)
          .expects(*)
          .returning(())

        val result = campaignService.deleteCampaignAndAssociatedTasks(CampaignId(2), TeamId(3L))
        assert(result == Right(1))
      }
    }

    describe("campaignService.getCampaignIdFromSrIdentifier") {


      it("should return campaign id (Long) if SrIdentifier is SrId without dao call") {

        val srIdentifierId = SrId(12)

        val result = campaignService.getCampaignIdFromSrIdentifier(
          teamId = TeamId(teamId),
          id = srIdentifierId
        )

        assert(result.isRight)
        assert(result == Right(12L))
      }

      it("should return campaign id (Long) if SrIdentifier is SrUuid with dao call to find corresponding id") {

        val srIdentifierUuid = SrUuid(campaign_uuid)

        (campaignDAO.getCampaignIdForUuid)
          .expects(TeamId(teamId), campaign_uuid)
          .returning(Success(Some(12L)))

        val result = campaignService.getCampaignIdFromSrIdentifier(
          teamId = TeamId(teamId),
          id = srIdentifierUuid
        )

        assert(result.isRight)
        assert(result == Right(12L))
      }

      it("should return failure if SrIdentifier is SrUuid with dao call failed") {

        val srIdentifierUuid = SrUuid(campaign_uuid)
        val error = new Throwable("DBFailure")

        (campaignDAO.getCampaignIdForUuid)
          .expects(TeamId(teamId), campaign_uuid)
          .returning(Failure(error))

        val result = campaignService.getCampaignIdFromSrIdentifier(
          teamId = TeamId(teamId),
          id = srIdentifierUuid
        )

        assert(result.isLeft)
        assert(result == Left(GetCampaignIdFromSrIdentifierError.DbFailure(error)))
      }

      it("should return failure if SrIdentifier is SrUuid with dao call returning None") {

        val srIdentifierUuid = SrUuid(campaign_uuid)

        (campaignDAO.getCampaignIdForUuid)
          .expects(TeamId(teamId), campaign_uuid)
          .returning(Success(None))

        val result = campaignService.getCampaignIdFromSrIdentifier(
          teamId = TeamId(teamId),
          id = srIdentifierUuid
        )

        assert(result.isLeft)
        assert(result == Left(GetCampaignIdFromSrIdentifierError.CampaignIdNotFound("Campaign id not found")))
      }
    }

  }

  describe("validate search params"){

    it("should fail when sender_email_address is in invalid format"){
      val query_params: Map[String, Vector[String]] = Map("sender_email_address" -> Vector("abc"))
      val res = CampaignService.validateParams(query_params)

      res match {
        case Success(_) => assert(false)
        case Failure(_) => assert(true)
      }
    }

    it("should fail when receiver_email_address is in invalid format") {
      val query_params: Map[String, Vector[String]] = Map("receiver_email_address" -> Vector("abcd"))
      val res = CampaignService.validateParams(query_params)

      res match {
        case Success(_) => assert(false)
        case Failure(_) => assert(true)
      }
    }

    it("should fail when status is in invalid") {
      val query_params: Map[String, Vector[String]] = Map("status" -> Vector("invalid_status"))
      val res = CampaignService.validateParams(query_params)

      res match {
        case Success(_) => assert(false)
        case Failure(_) => assert(true)
      }
    }

    it("should fail when older_than is invalid") {
      val query_params: Map[String, Vector[String]] = Map("older_than" -> Vector("16964793698"))
      val res = CampaignService.validateParams(query_params)

      res match {
        case Success(_) => assert(false)
        case Failure(_) => assert(true)
      }
    }

    it("should fail when newer_than is invalid") {
      val query_params: Map[String, Vector[String]] = Map("newer_than" -> Vector("16964793698"))
      val res = CampaignService.validateParams(query_params)

      res match {
        case Success(_) => assert(false)
        case Failure(_) => assert(true)
      }
    }

    it("should fail when older_than and newer_than are defined together") {
      val query_params: Map[String, Vector[String]] = Map("older_than" -> Vector("1696479369828"), "newer_than" -> Vector("1696479369899"))
      val res = CampaignService.validateParams(query_params)

      res match {
        case Success(_) => assert(false)
        case Failure(_) => assert(true)
      }
    }

    it("should pass is_first as false if newer_than is defined") {
      val query_params: Map[String, Vector[String]] = Map("newer_than" -> Vector("1696479369828"))
      val res = CampaignService.validateParams(query_params)

      res match {
        case Success(data: SearchParams) => assert(!data.is_first)
        case Failure(_) => assert(false)
      }
    }

    it("should pass is_first as false if older_than is defined") {
      val query_params: Map[String, Vector[String]] = Map("older_than" -> Vector("1696479369828"))
      val res = CampaignService.validateParams(query_params)

      res match {
        case Success(data: SearchParams) => assert(!data.is_first)
        case Failure(_) => assert(false)
      }
    }

    it("should pass is_first as true if timeline not defined") {
      val query_params: Map[String, Vector[String]] = Map()
      val res = CampaignService.validateParams(query_params)

      res match {
        case Success(data: SearchParams) => assert(data.is_first)
        case Failure(_) => assert(false)
      }
    }

    it("should pass"){

      val query_params: Map[String, Vector[String]] = Map("name" -> Vector("hello"),
        "sender_email_address" -> Vector("<EMAIL>"),
        "receiver_email_address" -> Vector("<EMAIL>"),
        "status" -> Vector("running"),
        "newer_than" -> Vector("1696479369828"))
      val res = CampaignService.validateParams(query_params)

      res match {
        case Success(_) => assert(true)
        case Failure(_) => assert(false)
      }

    }
  }


  val campaignBasicInfoSeq: List[CampaignBasicInfo] = List(CampaignBasicInfo(
    id = 121L,
    uuid = Some(campaign_uuid),
    team_id = teamId,
    shared_with_team = true,
    name = campaign_name,
    owner_name = first_name,
    owner_email = email,
    owner_id = ownerId,
    status = CampaignStatus.RUNNING,
    tags = Seq(CampaignTag(11L, "tag1", CampaignTagUuid("tag_abcdefgh"))),
    spam_test_exists = false,
    ai_generation_context = None,
    head_step_id = Some(22L),
    settings = campaignSettings,
    campaign_has_email_step = true,
    created_at = aDate,
    error = None,
    is_archived = false,
    warmup_is_on = false
  ),
    CampaignBasicInfo(
      id = 121L,
      uuid = Some(campaign_uuid + "1"),
      team_id = teamId,
      shared_with_team = true,
      name = campaign_name + "1",
      owner_name = first_name,
      owner_email = email,
      owner_id = ownerId,
      status = CampaignStatus.RUNNING,
      tags = Seq(CampaignTag(11L, "tag1", CampaignTagUuid("tag_abcdefgh"))),
      spam_test_exists = false,
      ai_generation_context = None,
      head_step_id = Some(22L),
      settings = campaignSettings,
      created_at = aDate.plusDays(1),
      error = None,
      is_archived = false,
      campaign_has_email_step = true,
      warmup_is_on = false
    ),
    CampaignBasicInfo(
      id = 121L,
      uuid = Some(campaign_uuid + "2"),
      team_id = teamId,
      shared_with_team = true,
      name = campaign_name + "2",
      owner_name = first_name,
      owner_email = email,
      owner_id = ownerId,
      status = CampaignStatus.RUNNING,
      tags = Seq(CampaignTag(11L, "tag1", CampaignTagUuid("tag_abcdefgh"))),
      spam_test_exists = false,
      ai_generation_context = None,
      head_step_id = Some(22L),
      settings = campaignSettings,
      created_at = aDate.plusDays(2),
      error = None,
      is_archived = false,
      campaign_has_email_step = true,
      warmup_is_on = false
    ),
    CampaignBasicInfo(
      id = 121L,
      uuid = Some(campaign_uuid + "3"),
      team_id = teamId,
      shared_with_team = true,
      name = campaign_name + "3",
      owner_name = first_name,
      owner_email = email,
      owner_id = ownerId,
      status = CampaignStatus.RUNNING,
      tags = Seq(CampaignTag(11L, "tag1", CampaignTagUuid("tag_abcdefgh"))),
      spam_test_exists = false,
      ai_generation_context = None,
      head_step_id = Some(22L),
      settings = campaignSettings,
      created_at = aDate.plusDays(3),
      error = None,
      is_archived = false,
      campaign_has_email_step = true,
      warmup_is_on = false
    ),
    CampaignBasicInfo(
      id = 121L,
      uuid = Some(campaign_uuid + "4"),
      team_id = teamId,
      shared_with_team = true,
      name = campaign_name + "4",
      owner_name = first_name,
      owner_email = email,
      owner_id = ownerId,
      status = CampaignStatus.RUNNING,
      tags = Seq(CampaignTag(11L, "tag1", CampaignTagUuid("tag_abcdefgh"))),
      spam_test_exists = false,
      ai_generation_context = None,
      head_step_id = Some(22L),
      settings = campaignSettings,
      created_at = aDate.plusDays(4),
      error = None,
      is_archived = false,
      campaign_has_email_step = true,
      warmup_is_on = false
    ),
    CampaignBasicInfo(
      id = 121L,
      uuid = Some(campaign_uuid + "5"),
      team_id = teamId,
      shared_with_team = true,
      name = campaign_name + "5",
      owner_name = first_name,
      owner_email = email,
      owner_id = ownerId,
      status = CampaignStatus.RUNNING,
      tags = Seq(CampaignTag(11L, "tag1", CampaignTagUuid("tag_abcdefgh"))),
      spam_test_exists = false,
      ai_generation_context = None,
      head_step_id = Some(22L),
      settings = campaignSettings,
      created_at = aDate.plusDays(5),
      error = None,
      is_archived = false,
      campaign_has_email_step = true,
      warmup_is_on = false
    ),
    CampaignBasicInfo(
      id = 121L,
      uuid = Some(campaign_uuid + "6"),
      team_id = teamId,
      shared_with_team = true,
      name = campaign_name + "6",
      owner_name = first_name,
      owner_email = email,
      owner_id = ownerId,
      status = CampaignStatus.RUNNING,
      tags = Seq(CampaignTag(11L, "tag1", CampaignTagUuid("tag_abcdefgh"))),
      spam_test_exists = false,
      ai_generation_context = None,
      head_step_id = Some(22L),
      settings = campaignSettings,
      created_at = aDate.plusDays(6),
      error = None,
      is_archived = false,
      campaign_has_email_step = true,
      warmup_is_on = false
    ),
    CampaignBasicInfo(
      id = 121L,
      uuid = Some(campaign_uuid + "7"),
      team_id = teamId,
      shared_with_team = true,
      name = campaign_name + "7",
      owner_name = first_name,
      owner_email = email,
      owner_id = ownerId,
      status = CampaignStatus.RUNNING,
      tags = Seq(CampaignTag(11L, "tag1", CampaignTagUuid("tag_abcdefgh"))),
      spam_test_exists = false,
      ai_generation_context = None,
      head_step_id = Some(22L),
      settings = campaignSettings,
      created_at = aDate.plusDays(7),
      error = None,
      is_archived = false,
      campaign_has_email_step = true,
      warmup_is_on = false
    ),
    CampaignBasicInfo(
      id = 121L,
      uuid = Some(campaign_uuid + "8"),
      team_id = teamId,
      shared_with_team = true,
      name = campaign_name + "8",
      owner_name = first_name,
      owner_email = email,
      owner_id = ownerId,
      status = CampaignStatus.RUNNING,
      tags = Seq(CampaignTag(11L, "tag1", CampaignTagUuid("tag_abcdefgh"))),
      spam_test_exists = false,
      ai_generation_context = None,
      head_step_id = Some(22L),
      settings = campaignSettings,
      created_at = aDate.plusDays(8),
      error = None,
      is_archived = false,
      campaign_has_email_step = true,
      warmup_is_on = false
    ),
    CampaignBasicInfo(
      id = 121L,
      uuid = Some(campaign_uuid + "9"),
      team_id = teamId,
      shared_with_team = true,
      name = campaign_name + "9",
      owner_name = first_name,
      owner_email = email,
      owner_id = ownerId,
      status = CampaignStatus.RUNNING,
      tags = Seq(CampaignTag(11L, "tag1", CampaignTagUuid("tag_abcdefgh"))),
      spam_test_exists = false,
      ai_generation_context = None,
      head_step_id = Some(22L),
      settings = campaignSettings,
      created_at = aDate.plusDays(9),
      error = None,
      is_archived = false,
      campaign_has_email_step = true,
      warmup_is_on = false
    ),
    CampaignBasicInfo(
      id = 121L,
      uuid = Some(campaign_uuid + "10"),
      team_id = teamId,
      shared_with_team = true,
      name = campaign_name + "10",
      owner_name = first_name,
      owner_email = email,
      owner_id = ownerId,
      status = CampaignStatus.RUNNING,
      tags = Seq(CampaignTag(11L, "tag1", CampaignTagUuid("tag_abcdefgh"))),
      spam_test_exists = false,
      ai_generation_context = None,
      head_step_id = Some(22L),
      settings = campaignSettings,
      created_at = aDate.plusDays(10),
      error = None,
      is_archived = false,
      campaign_has_email_step = true,
      warmup_is_on = false
    ))


  describe("tests for campaignService.getNextLinkForPagination"){

    val sortedCampaigns = campaignBasicInfoSeq.sortBy(_.created_at)(JodaTimeUtils.dateTimeOrdering)
      .reverse

    it("should return next with value and not contain +10"){

      val not_include = CampaignBasicInfo(
        id = 121L,
        uuid = Some(campaign_uuid +"10"),
        team_id = teamId,
        shared_with_team = true,
        name = campaign_name+"10",
        owner_name = first_name,
        owner_email = email,
        owner_id = ownerId,
        status = CampaignStatus.RUNNING,
        tags = Seq(CampaignTag(11L, "tag1", CampaignTagUuid("tag_abcdefgh"))),
        spam_test_exists = false,
        head_step_id = Some(22L),
        ai_generation_context = None,
        settings = campaignSettings,
        created_at = aDate.plusDays(10),
        error = None,
        is_archived = false,
        campaign_has_email_step = true,
        warmup_is_on = false
      )
      val searchParams = SearchParams(
        name = None,
        sender_email_setting = None,
        receiver_email_setting = None,
        status = None,
        range = InferredQueryTimeline.Range.After(dateTime = aDate.minusDays(1)),
        is_first = true
      )

      val res = campaignService.getNextLinkForPagination(
        campaigns = sortedCampaigns,
        limit = 10,
        search_params = searchParams
      )

      val expected_next = aDate.plusDays(9).toInstant.getMillis+1

      assert(res.next.get == expected_next && !res.response_data.contains(not_include))

    }
    it("should return next with value and not contain +0") {

      val not_include = CampaignBasicInfo(
        id = 121L,
        uuid = Some(campaign_uuid),
        team_id = teamId,
        shared_with_team = true,
        name = campaign_name,
        owner_name = first_name,
        owner_email = email,
        owner_id = ownerId,
        status = CampaignStatus.RUNNING,
        tags = Seq(CampaignTag(11L, "tag1", CampaignTagUuid("tag_abcdefgh"))),
        spam_test_exists = false,
        ai_generation_context = None,
        head_step_id = Some(22L),
        settings = campaignSettings,
        created_at = aDate,
        error = None,
        is_archived = false,
        campaign_has_email_step = true,
        warmup_is_on = false
      )

      val searchParams = SearchParams(
        name = None,
        sender_email_setting = None,
        receiver_email_setting = None,
        status = None,
        range = InferredQueryTimeline.Range.Before(dateTime = aDate.plusDays(11)),
        is_first = true
      )

      val res = campaignService.getNextLinkForPagination(
        campaigns = sortedCampaigns,
        limit = 10,
        search_params = searchParams
      )

      val expected_next = aDate.plusDays(1).toInstant.getMillis

      assert(res.next.get == expected_next && !res.response_data.contains(not_include))

    }

    it("should not return next with value and contain 5 values") {

      val searchParams = SearchParams(
        name = None,
        sender_email_setting = None,
        receiver_email_setting = None,
        status = None,
        range = InferredQueryTimeline.Range.Before(dateTime = aDate.plusDays(5)),
        is_first = true
      )

      val data = campaignBasicInfoSeq.slice(0,5).sortBy(_.created_at)(JodaTimeUtils.dateTimeOrdering)
        .reverse

      val res = campaignService.getNextLinkForPagination(
        campaigns = data,
        limit = 10,
        search_params = searchParams
      )
      assert(!res.next.isDefined && res.response_data.length == 5)
    }

    it("should not return next with value and contain 5 values with after") {

      val searchParams = SearchParams(
        name = None,
        sender_email_setting = None,
        receiver_email_setting = None,
        status = None,
        range = InferredQueryTimeline.Range.After(dateTime = aDate.plusDays(5)),
        is_first = true
      )

      val data = campaignBasicInfoSeq.slice(6, 11).sortBy(_.created_at)(JodaTimeUtils.dateTimeOrdering)
        .reverse

      val res = campaignService.getNextLinkForPagination(
        campaigns = data,
        limit = 10,
        search_params = searchParams
      )
      assert(!res.next.isDefined && res.response_data.length == 5)
    }

    it("should not return next with value and contain 10 values with after") {

      val searchParams = SearchParams(
        name = None,
        sender_email_setting = None,
        receiver_email_setting = None,
        status = None,
        range = InferredQueryTimeline.Range.After(dateTime = aDate.plusDays(1)),
        is_first = true
      )

      val data = campaignBasicInfoSeq.slice(1,11).sortBy(_.created_at)(JodaTimeUtils.dateTimeOrdering)
        .reverse

      val res = campaignService.getNextLinkForPagination(
        campaigns = data,
        limit = 10,
        search_params = searchParams
      )
      assert(!res.next.isDefined && res.response_data.length == 10)
    }

    it("should not return next with value and contain 10 values") {

      val searchParams = SearchParams(
        name = None,
        sender_email_setting = None,
        receiver_email_setting = None,
        status = None,
        range = InferredQueryTimeline.Range.Before(dateTime = aDate.plusDays(1)),
        is_first = true
      )

      val data = campaignBasicInfoSeq.slice(0, 10).sortBy(_.created_at)(JodaTimeUtils.dateTimeOrdering)
        .reverse

      val res = campaignService.getNextLinkForPagination(
        campaigns = data,
        limit = 10,
        search_params = searchParams
      )
      assert(!res.next.isDefined && res.response_data.length == 10)
    }

  }

  describe("tests for CampaignService.validateProspectAssignData"){

    it("should fail if prospect id list is empty") {
      val data: AssignProspectToCampaignDataV3 = AssignProspectToCampaignDataV3(
        prospect_ids = List(),
        ignore_prospects_in_other_campaigns = None)
      val err = List(ErrorResponseProspectsAssignApi(
        message = "Please send a list of prospect ids. Empty request found.",
        error_type = ProspectAssignErrorType.EmptyList,
        data = None
      ))
      val res = CampaignService.validateProspectAssignData(
        data = data,
        isApiCall = true
      )

      assert(res == err)
    }

    it("should fail if prospect id list is greater than 100") {
      val uuid  = ProspectUuid("prospect_uuid ")
      val data: AssignProspectToCampaignDataV3 = AssignProspectToCampaignDataV3(
        prospect_ids = List.fill(101)(uuid),
        ignore_prospects_in_other_campaigns = None)

      val err = List(ErrorResponseProspectsAssignApi(
        message = "Limit exceeded. Cannot assign more than 100 prospects.",
        error_type = ProspectAssignErrorType.LimitExceeded,
        data = None
      ))

      val res = CampaignService.validateProspectAssignData(
        data = data,
        isApiCall = true
      )

      assert(res == err)
    }

    it("should pass"){
      val data: AssignProspectToCampaignDataV3 = AssignProspectToCampaignDataV3(
        prospect_ids = List(
          ProspectUuid(uuid = "prospect uuid 1"),
          ProspectUuid(uuid = "prospect uuid 2")
        ),
        ignore_prospects_in_other_campaigns = None)
      val res = CampaignService.validateProspectAssignData(
        data = data,
        isApiCall = true
      )

      assert(res == List())
    }

  }

  describe("tests for campaignService.findCampaignsV3"){

    it("should fail when dao returns failure") {
      val searchParams = SearchParams(
        name = None,
        sender_email_setting = None,
        receiver_email_setting = None,
        status = None,
        range = InferredQueryTimeline.Range.Before(dateTime = aDate.plusDays(5)),
        is_first = true
      )

      (campaignDAO.getCampaignBasicInfoV3)
        .expects(permittedAccountIds, teamId, 3L, 3L, searchParams, Logger)
        .returning(Failure(new Exception("some exception")))

      val res = campaignService.findCampaignsV3(
        accountIds = permittedAccountIds,
        teamId = teamId,
        orgId = 3L,
        loggedinAccountId = 3L,
        search_params = searchParams,
        account = account,
        Logger = Logger
      )

      res match {

        case Failure(_) => assert(true)

        case Success(_) => assert(false)
      }

    }

    it("should pass and return empty object") {
      val searchParams = SearchParams(
        name = None,
        sender_email_setting = None,
        receiver_email_setting = None,
        status = None,
        range = InferredQueryTimeline.Range.Before(dateTime = aDate.plusDays(5)),
        is_first = true
      )

      (campaignDAO.getCampaignBasicInfoV3)
        .expects(permittedAccountIds, teamId, 3L, 3L, searchParams, Logger)
        .returning(Success(List()))

      val res = campaignService.findCampaignsV3(
        accountIds = permittedAccountIds,
        teamId = teamId,
        orgId = 3L,
        loggedinAccountId = 3L,
        search_params = searchParams,
        account = account,
        Logger = Logger
      )

      res match {

        case Failure(_) => assert(false)

        case Success(result) => assert(result.data.isEmpty && result.links.next.isEmpty)
      }

    }

    it("should pass and return empty object for after") {
      val searchParams = SearchParams(
        name = None,
        sender_email_setting = None,
        receiver_email_setting = None,
        status = None,
        range = InferredQueryTimeline.Range.After(dateTime = aDate.plusDays(5)),
        is_first = true
      )

      (campaignDAO.getCampaignBasicInfoV3)
        .expects(permittedAccountIds, teamId, 3L, 3L, searchParams, Logger)
        .returning(Success(List()))

      val res = campaignService.findCampaignsV3(
        accountIds = permittedAccountIds,
        teamId = teamId,
        orgId = 3L,
        loggedinAccountId = 3L,
        search_params = searchParams,
        account = account,
        Logger = Logger
      )

      res match {

        case Failure(_) => assert(false)

        case Success(result) => assert(result.data.isEmpty && result.links.next.isEmpty)
      }

    }


    it("should pass and return object with length 10 and no next") {
      val searchParams = SearchParams(
        name = None,
        sender_email_setting = None,
        receiver_email_setting = None,
        status = None,
        range = InferredQueryTimeline.Range.Before(dateTime = aDate.plusDays(5)),
        is_first = true
      )
      val req_result = campaignBasicInfoSeq.slice(0,10)

      (campaignDAO.getCampaignBasicInfoV3)
        .expects(permittedAccountIds, teamId, 3L, 3L, searchParams, Logger)
        .returning(Success(req_result))

      val res = campaignService.findCampaignsV3(
        accountIds = permittedAccountIds,
        teamId = teamId,
        orgId = 3L,
        loggedinAccountId = 3L,
        search_params = searchParams,
        account = account,
        Logger = Logger
      )

      res match {

        case Failure(_) => assert(false)

        case Success(result) => assert(result.data.length == 10 && result.links.next.isEmpty)
      }

    }

    it("should pass and return object with length 10 and no next for after") {
      val searchParams = SearchParams(
        name = None,
        sender_email_setting = None,
        receiver_email_setting = None,
        status = None,
        range = InferredQueryTimeline.Range.After(dateTime = aDate.plusDays(5)),
        is_first = true
      )
      val req_result = campaignBasicInfoSeq.slice(0, 10)

      (campaignDAO.getCampaignBasicInfoV3)
        .expects(permittedAccountIds, teamId, 3L, 3L, searchParams, Logger)
        .returning(Success(req_result))

      val res = campaignService.findCampaignsV3(
        accountIds = permittedAccountIds,
        teamId = teamId,
        orgId = 3L,
        loggedinAccountId = 3L,
        search_params = searchParams,
        account = account,
        Logger = Logger
      )

      res match {

        case Failure(_) => assert(false)

        case Success(result) => assert(result.data.length == 10 && result.links.next.isEmpty)
      }

    }


    it("should pass"){
      val searchParams = SearchParams(
        name = None,
        sender_email_setting = None,
        receiver_email_setting = None,
        status = None,
        range = InferredQueryTimeline.Range.Before(dateTime = aDate.plusDays(5)),
        is_first = true
      )

      (campaignDAO.getCampaignBasicInfoV3)
        .expects(permittedAccountIds, teamId, 3L, 3L, searchParams, Logger)
        .returning(Success(campaignBasicInfoSeq))

      val res = campaignService.findCampaignsV3(
        accountIds = permittedAccountIds,
        teamId = teamId,
        orgId = 3L,
        loggedinAccountId = 3L,
        search_params = searchParams,
        account = account,
        Logger = Logger
      )

      res match {

        case Failure(_) => assert(false)

        case Success(data) => assert(data.data.length == 10 && data.links.next.isDefined)
      }

    }

    it("should pass for after") {
      val searchParams = SearchParams(
        name = None,
        sender_email_setting = None,
        receiver_email_setting = None,
        status = None,
        range = InferredQueryTimeline.Range.After(dateTime = aDate.plusDays(5)),
        is_first = true
      )

      (campaignDAO.getCampaignBasicInfoV3)
        .expects(permittedAccountIds, teamId, 3L, 3L, searchParams, Logger)
        .returning(Success(campaignBasicInfoSeq))

      val res = campaignService.findCampaignsV3(
        accountIds = permittedAccountIds,
        teamId = teamId,
        orgId = 3L,
        loggedinAccountId = 3L,
        search_params = searchParams,
        account = account,
        Logger = Logger
      )

      res match {

        case Failure(_) => assert(false)

        case Success(data) => assert(data.data.length == 10 && data.links.next.isDefined)
      }

    }

  }


  describe("Testing PreviousFollowupData.reads") {

    it ("should fail due to insufficient data in json.") {
      Json.obj("step_type" -> "send_email").validate[PreviousFollowUpData]
      match {
        case JsError(errors) =>
          Logger.error(errors.toString())
          assert(true)

        case JsSuccess(value, _) => assert(false)
      }
    }

  }


  describe("CampaignSendReportService.checkIfCampaignsAreStuckNewFlow") {


    it("Has not reached the 60 min mark") {

      val dateNow = DateTime.parse("2024-03-07T17:17:39.051+05:30")


      val daily_from_time = dateNow
        .minusMinutes(10)
        .getSecondOfDay
      val  daily_till_time = dateNow
        .plusMinutes(10)
        .getSecondOfDay
      val result = CampaignService.checkIfCampaignsAreStuckNewFlow(
        campaign_to_check = CampaignSendReport.campaignToCheckForSendingLimitNewFlow.copy(
            daily_from_time = daily_from_time,
          max_emails_per_day = 100,
          total_emails_sent_till_now = 100,
          daily_till_time = daily_till_time,
          timezone = "Asia/Kolkata",
          senders = List(SenderDataForSendingLimitNewFlow(
            quota_per_day = 300,
            min_delay_seconds = 20,
            max_delay_seconds = 30,
            number_of_campaigns = 1
          ))
        ),
        now = dateNow,
        senderRotationStats = SenderRotationStats(prospects_not_sent_any_emails = 1000, prospects_to_get_follow_up = 1000)
      )

      assert(result.isEmpty)
    }


    it("Zero email to send because no sending email") {

      val dateNow = DateTime.parse("2024-07-07T17:17:39.051+05:30")


      val daily_from_time = dateNow
        .minusMinutes(70)
        .getSecondOfDay
      val  daily_till_time = dateNow
        .plusMinutes(10)
        .getSecondOfDay

      val result = CampaignService.checkIfCampaignsAreStuckNewFlow(
        campaign_to_check = CampaignSendReport.campaignToCheckForSendingLimitNewFlow.copy(
            daily_from_time =  daily_from_time,
          max_emails_per_day = 100,
          total_emails_sent_till_now = 0,
          daily_till_time = daily_till_time,
          timezone = "Asia/Kolkata",
          senders = List()
        ),
        now = dateNow,
        senderRotationStats = SenderRotationStats(prospects_not_sent_any_emails = 1000, prospects_to_get_follow_up = 1000)
      )

      assert(result.isDefined)
      assert(result.get.possible_issue_if_any.isEmpty)
    }


    it("Already reached 100% because of the campaign limit") {


      val dateNow = DateTime.parse("2024-07-07T17:17:39.051+05:30")

      val daily_from_time = dateNow
        .minusMinutes(70)
        .getSecondOfDay
      val  daily_till_time = dateNow
        .plusMinutes(10)
        .getSecondOfDay

      val result = CampaignService.checkIfCampaignsAreStuckNewFlow(
        campaign_to_check =  CampaignSendReport.campaignToCheckForSendingLimitNewFlow.copy(
            daily_from_time =  daily_from_time,
          max_emails_per_day = 100,
          total_emails_sent_till_now = 100,
          daily_till_time = daily_till_time,
          timezone = "Asia/Kolkata",
          senders = List(SenderDataForSendingLimitNewFlow(
            quota_per_day = 300,
            min_delay_seconds = 20,
            max_delay_seconds = 30,
            number_of_campaigns = 1
          ))
        ),
        now = dateNow,
        senderRotationStats = SenderRotationStats(prospects_not_sent_any_emails = 1000, prospects_to_get_follow_up = 1000)
      )

      assert(result.isDefined)

      assert(result.get.possible_issue_if_any.isEmpty)
      assert(result.get.expected_sent_count_till_now == 100)
    }


    it("daily till time is before from time so we should not get any reason why the campaign is stopped") {


      val dateNow = DateTime.parse("2024-07-07T17:17:39.051+05:30")

      val daily_from_time = dateNow
        .minusMinutes(70)
        .getSecondOfDay
      val  daily_till_time = dateNow
        .minusMinutes(80)
        .getSecondOfDay
      val result = CampaignService.checkIfCampaignsAreStuckNewFlow(
        campaign_to_check =  CampaignSendReport.campaignToCheckForSendingLimitNewFlow.copy(
          daily_from_time =  daily_from_time,
          max_emails_per_day = 100,
          total_emails_sent_till_now = 0,
          daily_till_time = daily_till_time,
          timezone = "Asia/Kolkata",
          senders = List(SenderDataForSendingLimitNewFlow(
            quota_per_day = 300,
            min_delay_seconds = 20,
            max_delay_seconds = 30,
            number_of_campaigns = 1
          ))
        ),
        now = dateNow,
        senderRotationStats = SenderRotationStats(prospects_not_sent_any_emails = 1000, prospects_to_get_follow_up = 1000)
      )

      assert(result.isDefined)

      assert(result.get.possible_issue_if_any.isEmpty)
      assert(result.get.expected_sent_count_till_now <= 0)
    }


    it("limit for warmup is less than total limit") {

      val dateNow = DateTime.parse("2024-07-07T17:17:39.051+05:30")

      val daily_from_time = dateNow
        .minusMinutes(70)
        .getSecondOfDay
      val  daily_till_time = dateNow
        .plusMinutes(80)
        .getSecondOfDay
      val result = CampaignService.checkIfCampaignsAreStuckNewFlow(
        campaign_to_check =  CampaignSendReport.campaignToCheckForSendingLimitNewFlow.copy(
          daily_from_time =  daily_from_time,
          max_emails_per_day = 100,
          total_emails_sent_till_now = 30,
          daily_till_time = daily_till_time,
          timezone = "Asia/Kolkata",
          senders = List(SenderDataForSendingLimitNewFlow(
            quota_per_day = 300,
            min_delay_seconds = 20,
            max_delay_seconds = 30,
            number_of_campaigns = 1
          )),
          warmupSettingOpt = Some(
            CampaignWarmupSetting(
              warmup_started_at = DateTime.now().minusDays(2),
              warmup_length_in_days = 7,
              warmup_starting_email_count = 10
            )
          )
        ),
        now = dateNow,
        senderRotationStats = SenderRotationStats(prospects_not_sent_any_emails = 1000, prospects_to_get_follow_up = 1000)
      )

      assert(result.isDefined)

      assert(result.get.possible_issue_if_any.isEmpty)
      assert(result.get.expected_sent_count_till_now == 35)
    }



    it("Only Sent 10% and there is an issue") {

      val dateNow = DateTime.parse("2024-07-07T17:17:39.051+05:30")

      val daily_from_time = dateNow
        .minusMinutes(70)
        .getSecondOfDay

      val  daily_till_time = dateNow
        .plusMinutes(10)
        .getSecondOfDay

      val result = CampaignService.checkIfCampaignsAreStuckNewFlow(
        campaign_to_check = CampaignSendReport.campaignToCheckForSendingLimitNewFlow.copy(
            daily_from_time =  daily_from_time,
          total_emails_sent_till_now = 10,
          max_emails_per_day = 100,
          daily_till_time = daily_till_time,
          timezone = "Asia/Kolkata",
          senders = List(SenderDataForSendingLimitNewFlow(
            quota_per_day = 300,
            min_delay_seconds = 20,
            max_delay_seconds = 30,
            number_of_campaigns = 1
          ))
        ),
        now = dateNow,
        senderRotationStats = SenderRotationStats(prospects_not_sent_any_emails = 1000, prospects_to_get_follow_up = 1000)
      )

      assert(result.nonEmpty)
      assert(result.get.expected_sent_count_till_now == 100)

    }

  }

    describe("setNextToBeScheduledAt") {
        val tid = TeamId(teamId)

        // Setup function to handle fixed time testing
        def withFixedTime[T](fixedTime: DateTime)(test: => T): T = {
            try {
                DateTimeUtils.setCurrentMillisFixed(fixedTime.getMillis)
                test
            } finally {
                DateTimeUtils.setCurrentMillisSystem()
            }
        }

        // Helper to capture and verify the expected campaign data
        def expectCampaignData(expectedCampaigns: Seq[Long], expectedTimes: Map[Long, DateTime], returnValue: Try[Int] = Success(1)) = {
            // This is the correct way to use 'where' with ScalaMock
            (campaignDAO._updateNextToScheduledAt)
              .expects(
                  // Use argThat instead of where for cleaner parameter matching
                  argThat[Seq[CampaignDataToAddNextToBeScheduledAt]] { campaignData =>
                      // Verify all expected campaigns are present
                      val allCampaignsPresent = expectedCampaigns.forall(cid =>
                          campaignData.exists(_.campaignId == cid)
                      )

                      // Verify all scheduling times match expectations
                      val allTimesCorrect = expectedTimes.forall { case (cid, expectedTime) =>
                          campaignData.find(_.campaignId == cid).exists { data =>
                              val timeDiff = Math.abs(data.nextToBeScheduledAt.getMillis - expectedTime.getMillis)
                              timeDiff < 1000 // Allow 1 second tolerance
                          }
                      }

                      allCampaignsPresent && allTimesCorrect
                  },
                  Logger,
                  tid,
                  true
              )
              .returning(returnValue)
        }

        describe("SchedulerSuccessFlow") {
            it("should schedule for the next available timezone when multiple timezones exist and no tasks were scheduled") {
                val fixedTime = DateTime.parse("2023-06-15T14:30:00Z") // 2:30 PM UTC

                withFixedTime(fixedTime) {
                    val timezones = Set("America/New_York", "America/Chicago") // 10:30 AM EDT, 9:30 AM CDT
                    val campaignDataWithTimezones = Set((campaign1, timezones))

                    val flowData = CampaignSetNextToBeScheduledAtData.SchedulerSuccessFlow(
                        campaignDataWithAllTheTimezone = convertForScheduler(campaignDataWithTimezones),
                        channelType = EmailChannel,
                        total_scheduled = 0
                    )

                    // Calculate expected time based on the timezone logic in the implementation
                    val nyStart = fixedTime.withZone(DateTimeZone.forID("America/New_York"))
                      .withTimeAtStartOfDay()
                      .plusSeconds(campaign1.daily_from_time) // 9:00 AM EDT

                    val chicagoStart = fixedTime.withZone(DateTimeZone.forID("America/Chicago"))
                      .withTimeAtStartOfDay()
                      .plusSeconds(campaign1.daily_from_time) // 9:00 AM CDT

                    // The current fixed time (2:30 PM UTC) is after both timezone start times
                    // So the earliest start time for the next day should be used
                    val expectedTime = if (nyStart.isBefore(chicagoStart))
                        nyStart.plusDays(1) else chicagoStart.plusDays(1)

                    // Set up expectations with precise time values
                    expectCampaignData(
                        expectedCampaigns = Seq(campaign1.campaign_id),
                        expectedTimes = Map(campaign1.campaign_id -> expectedTime)
                    )

                    val result = campaignService.setNextToBeScheduledAt(flowData, Logger, tid)

                    assert(result.isSuccess)
                    assert(result.get == 1)
                }
            }

            it("should schedule with a short delay when tasks were scheduled") {
                val fixedTime = DateTime.parse("2023-06-15T14:30:00Z")

                withFixedTime(fixedTime) {
                    val timezones = Set("America/New_York", "America/Chicago")
                    val campaignDataWithTimezones = Set((campaign1, timezones))

                    val flowData = CampaignSetNextToBeScheduledAtData.SchedulerSuccessFlow(
                        campaignDataWithAllTheTimezone = convertForScheduler(campaignDataWithTimezones),
                        channelType = EmailChannel,
                        total_scheduled = 5 // Some tasks were scheduled
                    )

                    // With tasks scheduled, it should use a default delay regardless of timezones
                    val expectedTime = fixedTime.plusMinutes(
                        AppConfig.SchedulerConfig.CAMPAIGN_NEXT_SCHEDULED_AT_MIN_DELAY_IN_MINUTES_COULD_NOT_LOCK
                    )

                    expectCampaignData(
                        expectedCampaigns = Seq(campaign1.campaign_id),
                        expectedTimes = Map(campaign1.campaign_id -> expectedTime)
                    )

                    val result = campaignService.setNextToBeScheduledAt(flowData, Logger, tid)

                    assert(result.isSuccess)
                    assert(result.get == 1)
                }
            }

            it("should schedule with a default delay when no timezones are specified") {
                val fixedTime = DateTime.parse("2023-06-15T14:30:00Z")

                withFixedTime(fixedTime) {
                    val emptyTimezones = Set.empty[String]
                    val campaignDataWithTimezones = Set((campaign1, emptyTimezones))

                    val flowData = CampaignSetNextToBeScheduledAtData.SchedulerSuccessFlow(
                        campaignDataWithAllTheTimezone = convertForScheduler(campaignDataWithTimezones),
                        channelType = EmailChannel,
                        total_scheduled = 0
                    )

                    // With no timezones, it should use the default delay
                    val expectedTime = fixedTime.plusMinutes(
                        AppConfig.SchedulerConfig.CAMPAIGN_NEXT_SCHEDULED_AT_MIN_DELAY_IN_MINUTES_COULD_NOT_LOCK
                    )

                    expectCampaignData(
                        expectedCampaigns = Seq(campaign1.campaign_id),
                        expectedTimes = Map(campaign1.campaign_id -> expectedTime)
                    )

                    val result = campaignService.setNextToBeScheduledAt(flowData, Logger, tid)

                    assert(result.isSuccess)
                    assert(result.get == 1)
                }
            }
        }

        describe("SchedulerRejectedFlow") {
            describe("CurrentTimeNotInCampaignTimezone") {
                it("should schedule for the campaign start time when current time is before campaign hours") {
                    val fixedTime = DateTime.parse("2023-06-15T05:30:00Z") // 5:30 AM UTC, before campaign hours

                    withFixedTime(fixedTime) {
                        val timezones = Set("Europe/Paris", "Europe/Berlin")
                        val campaignDataWithTimezones = Set((campaign1, timezones))

                        val flowData = CampaignSetNextToBeScheduledAtData.SchedulerRejectedFlow(
                            campaignDataWithAllTheTimezone = convertForScheduler(campaignDataWithTimezones),
                            channelType = EmailChannel,
                            rejectedForSchedulingReason = CampaignRejectedForSchedulingReason.CurrentTimeNotInCampaignTimezone
                        )

                        // For time before campaign hours, it should schedule at campaign start time
                        val parisStartTime = fixedTime.withZone(DateTimeZone.forID("Europe/Paris"))
                          .withTimeAtStartOfDay()
                          .plusSeconds(campaign1.daily_from_time)

                        val berlinStartTime = fixedTime.withZone(DateTimeZone.forID("Europe/Berlin"))
                          .withTimeAtStartOfDay()
                          .plusSeconds(campaign1.daily_from_time)

                        val expectedTime = if (parisStartTime.isBefore(berlinStartTime))
                            parisStartTime else berlinStartTime

                        expectCampaignData(
                            expectedCampaigns = Seq(campaign1.campaign_id),
                            expectedTimes = Map(campaign1.campaign_id -> expectedTime)
                        )

                        val result = campaignService.setNextToBeScheduledAt(flowData, Logger, tid)

                        assert(result.isSuccess)
                        assert(result.get == 1)
                    }
                }

                it("should schedule for the next day when current time is after campaign hours") {
                    val fixedTime = DateTime.parse("2023-06-15T20:30:00Z") // 8:30 PM UTC, after campaign hours

                    withFixedTime(fixedTime) {
                        val timezones = Set("Europe/Paris", "Europe/Berlin")
                        val campaignDataWithTimezones = Set((campaign1, timezones))

                        val flowData = CampaignSetNextToBeScheduledAtData.SchedulerRejectedFlow(
                            campaignDataWithAllTheTimezone = convertForScheduler(campaignDataWithTimezones),
                            channelType = EmailChannel,
                            rejectedForSchedulingReason = CampaignRejectedForSchedulingReason.CurrentTimeNotInCampaignTimezone
                        )

                        // For time after campaign hours, calculate campaign end time
                        val parisEndTime = fixedTime.withZone(DateTimeZone.forID("Europe/Paris"))
                          .withTimeAtStartOfDay()
                          .plusSeconds(campaign1.daily_till_time)

                        val berlinEndTime = fixedTime.withZone(DateTimeZone.forID("Europe/Berlin"))
                          .withTimeAtStartOfDay()
                          .plusSeconds(campaign1.daily_till_time)

                        val latestEndTime = if (parisEndTime.isAfter(berlinEndTime))
                            parisEndTime else berlinEndTime

                        // After campaign hours, should schedule for next day's start time
                        val parisStartTime = fixedTime.withZone(DateTimeZone.forID("Europe/Paris"))
                          .withTimeAtStartOfDay()
                          .plusSeconds(campaign1.daily_from_time)

                        val berlinStartTime = fixedTime.withZone(DateTimeZone.forID("Europe/Berlin"))
                          .withTimeAtStartOfDay()
                          .plusSeconds(campaign1.daily_from_time)

                        val earliestStartTime = if (parisStartTime.isBefore(berlinStartTime))
                            parisStartTime else berlinStartTime

                        val expectedTime = earliestStartTime.plusDays(1)

                        expectCampaignData(
                            expectedCampaigns = Seq(campaign1.campaign_id),
                            expectedTimes = Map(campaign1.campaign_id -> expectedTime)
                        )

                        val result = campaignService.setNextToBeScheduledAt(flowData, Logger, tid)

                        assert(result.isSuccess)
                        assert(result.get == 1)
                    }
                }

                it("should schedule with a short delay when current time is within campaign hours") {
                    val fixedTime = DateTime.parse("2023-06-15T12:30:00Z") // 12:30 PM UTC, within campaign hours

                    withFixedTime(fixedTime) {
                        val timezones = Set("Europe/Paris", "Europe/Berlin")
                        val campaignDataWithTimezones = Set((campaign1, timezones))

                        val flowData = CampaignSetNextToBeScheduledAtData.SchedulerRejectedFlow(
                            campaignDataWithAllTheTimezone = convertForScheduler(campaignDataWithTimezones),
                            channelType = EmailChannel,
                            rejectedForSchedulingReason = CampaignRejectedForSchedulingReason.CurrentTimeNotInCampaignTimezone
                        )

                        // For time within campaign hours, calculate campaign start/end times
                        val parisStartTime = fixedTime.withZone(DateTimeZone.forID("Europe/Paris"))
                          .withTimeAtStartOfDay()
                          .plusSeconds(campaign1.daily_from_time)

                        val berlinStartTime = fixedTime.withZone(DateTimeZone.forID("Europe/Berlin"))
                          .withTimeAtStartOfDay()
                          .plusSeconds(campaign1.daily_from_time)

                        val earliestStartTime = if (parisStartTime.isBefore(berlinStartTime))
                            parisStartTime else berlinStartTime

                        val parisEndTime = fixedTime.withZone(DateTimeZone.forID("Europe/Paris"))
                          .withTimeAtStartOfDay()
                          .plusSeconds(campaign1.daily_till_time)

                        val berlinEndTime = fixedTime.withZone(DateTimeZone.forID("Europe/Berlin"))
                          .withTimeAtStartOfDay()
                          .plusSeconds(campaign1.daily_till_time)

                        val latestEndTime = if (parisEndTime.isAfter(berlinEndTime))
                            parisEndTime else berlinEndTime

                        // Within campaign hours, it should use the default delay
                        val expectedTime = fixedTime.plusMinutes(
                            AppConfig.SchedulerConfig.CAMPAIGN_NEXT_SCHEDULED_AT_MIN_DELAY_IN_MINUTES
                        )

                        expectCampaignData(
                            expectedCampaigns = Seq(campaign1.campaign_id),
                            expectedTimes = Map(campaign1.campaign_id -> expectedTime)
                        )

                        val result = campaignService.setNextToBeScheduledAt(flowData, Logger, tid)

                        assert(result.isSuccess)
                        assert(result.get == 1)
                    }
                }
            }

            it("should schedule with a lock delay when campaign couldn't acquire lock") {
                val fixedTime = DateTime.parse("2023-06-15T14:30:00Z")

                withFixedTime(fixedTime) {
                    val timezones = Set("Europe/Paris", "Europe/Berlin")
                    val campaignDataWithTimezones = Set((campaign1, timezones))
                    val flowData = CampaignSetNextToBeScheduledAtData.SchedulerRejectedFlow(
                        campaignDataWithAllTheTimezone = convertForScheduler(campaignDataWithTimezones),
                        channelType = EmailChannel,
                        rejectedForSchedulingReason = CampaignRejectedForSchedulingReason.CouldNotAcquireCampaignLock
                    )

                    // Should apply lock delay
                    val expectedTime = fixedTime.plusMinutes(
                        AppConfig.SchedulerConfig.CAMPAIGN_NEXT_SCHEDULED_AT_MIN_DELAY_IN_MINUTES_COULD_NOT_LOCK
                    )

                    expectCampaignData(
                        expectedCampaigns = Seq(campaign1.campaign_id),
                        expectedTimes = Map(campaign1.campaign_id -> expectedTime),
                        returnValue = Success(2) // Returning 2 as in the original test
                    )

                    val result = campaignService.setNextToBeScheduledAt(flowData, Logger, tid)

                    assert(result.isSuccess)
                    assert(result.get == 2)
                }
            }

            it("should schedule for the next day when campaign has hit daily limit") {
                val fixedTime = DateTime.parse("2023-06-15T14:30:00Z")

                withFixedTime(fixedTime) {
                    val timezones = Set("Europe/Paris", "Europe/Berlin")
                    val campaignDataWithTimezones = Set((campaign1, timezones))
                    val flowData = CampaignSetNextToBeScheduledAtData.SchedulerRejectedFlow(
                        campaignDataWithAllTheTimezone = convertForScheduler(campaignDataWithTimezones),

                        channelType = EmailChannel,
                        rejectedForSchedulingReason = CampaignRejectedForSchedulingReason.CampaignHasHitDailyLimit
                    )

                    // Calculate the start of the next day in campaign timezone
                    val startOfNextDay = Helpers.startOfTheDay(campaign1.timezone)
                      .plusDays(1)
                      .minusMinutes(30) // 30 minutes before next day starts

                    expectCampaignData(
                        expectedCampaigns = Seq(campaign1.campaign_id),
                        expectedTimes = Map(campaign1.campaign_id -> startOfNextDay),
                        returnValue = Success(2)
                    )

                    val result = campaignService.setNextToBeScheduledAt(flowData, Logger, tid)

                    assert(result.isSuccess)
                    assert(result.get == 2)
                }
            }
        }
    }


    describe("calculateNextToBeScheduled") {
        it("should return current time plus delay when no timezones are provided") {
            // Given
            val current = new DateTime(2023, 5, 15, 10, 0, 0, DateTimeZone.UTC)
            val timezones = Set.empty[String]
            val daily_from_time = 9 * 3600 // 9 AM in seconds
            val daily_till_time = 17 * 3600 // 5 PM in seconds

            // When
            val result = CampaignService.calculateNextToBeScheduled(
                timezones, current, daily_from_time, daily_till_time
            )

            // Then
            val expected = current.plusMinutes(15)
            assert(result.withZone(DateTimeZone.UTC) === expected.withZone(DateTimeZone.UTC))
        }

        it("should return campaign start time when current time is before campaign start time") {
            // Given
            val current = new DateTime(2023, 5, 15, 6, 0, 0, DateTimeZone.UTC) // 6 AM UTC
            val timezones = Set("America/New_York") // UTC-4
            val daily_from_time = 9 * 3600 // 9 AM in seconds
            val daily_till_time = 17 * 3600 // 5 PM in seconds

            // When
            val result = CampaignService.calculateNextToBeScheduled(
                timezones, current, daily_from_time, daily_till_time
            )

            // Then
            // 9 AM in New York would be 13:00 UTC (9 + 4)
            val expected = current.withZone(DateTimeZone.forID("America/New_York"))
              .withTimeAtStartOfDay()
              .plusSeconds(daily_from_time)
            assert(result.withZone(DateTimeZone.UTC) === expected.withZone(DateTimeZone.UTC))
        }

        it("should return next day's campaign start time when current time is after campaign end time") {
            // Given
            val current = new DateTime(2023, 5, 15, 23, 0, 0, DateTimeZone.UTC) // 11 PM UTC
            val timezones = Set("America/Los_Angeles") // UTC-7
            val daily_from_time = 9 * 3600 // 9 AM in seconds
            val daily_till_time = 14 * 3600 // 5 PM in seconds

            // When
            val result = CampaignService.calculateNextToBeScheduled(
                timezones, current, daily_from_time, daily_till_time
            )

            // Then
            // Campaign end time is 5 PM in Los Angeles, which is 12 AM UTC the next day
            // Next start time should be 9 AM in LA the next day, which is 4 PM UTC
            val expected = current.withZone(DateTimeZone.forID("America/Los_Angeles"))
              .withTimeAtStartOfDay()
              .plusSeconds(daily_from_time)
              .plusDays(1)
            assert(result.withZone(DateTimeZone.UTC) === expected.withZone(DateTimeZone.UTC))
        }

        it("should return current time plus delay when current time is between campaign start and end times") {
            // Given
            val current = new DateTime(2023, 5, 15, 15, 0, 0, DateTimeZone.UTC) // 3 PM UTC
            val timezones = Set("Europe/London") // UTC+1 (during DST)
            val daily_from_time = 9 * 3600 // 9 AM in seconds
            val daily_till_time = 17 * 3600 // 5 PM in seconds

            // When
            val result = CampaignService.calculateNextToBeScheduled(
                timezones, current, daily_from_time, daily_till_time
            )

            // Then
            // London time is 4 PM (3 PM UTC + 1), which is between 9 AM and 5 PM
            val expected = current.plusMinutes(15)
            assert(result.withZone(DateTimeZone.UTC) === expected.withZone(DateTimeZone.UTC))
        }

        it("should handle multiple timezones correctly") {
            // Given
            val current = new DateTime(2023, 5, 15, 12, 0, 0, DateTimeZone.UTC) // 12 PM UTC
            val timezones = Set("Europe/London", "America/New_York", "Asia/Tokyo")
            val daily_from_time = 9 * 3600 // 9 AM in seconds
            val daily_till_time = 17 * 3600 // 5 PM in seconds

            // When
            val result = CampaignService.calculateNextToBeScheduled(
                timezones, current, daily_from_time, daily_till_time
            )

            // Then
            // At 12 PM UTC:
            // - London is 1 PM (within working hours)
            // - New York is 8 AM (before working hours)
            // - Tokyo is 9 PM (after working hours)
            // The function should return the current time plus delay because the current time
            // is between the earliest start time (New York) and the latest end time (Tokyo)
            val expected = current.plusMinutes(15)
            assert(result.withZone(DateTimeZone.UTC) === expected.withZone(DateTimeZone.UTC))
        }

        it("should correctly determine the earliest start time across multiple timezones") {
            // Given
            val current = new DateTime(2023, 5, 15, 2, 0, 0, DateTimeZone.UTC) // 2 AM UTC
            val timezones = Set("Europe/London", "America/New_York", "Asia/Kolkata")
            val daily_from_time = 9 * 3600 // 9 AM in seconds
            val daily_till_time = 16 * 3600 // 4 PM in seconds

            // When
            val result = CampaignService.calculateNextToBeScheduled(
                timezones, current, daily_from_time, daily_till_time
            )



            val expected = current.plusMinutes(15)

            assert(result.withZone(DateTimeZone.UTC) === expected.withZone(DateTimeZone.UTC))
        }
    }

}
