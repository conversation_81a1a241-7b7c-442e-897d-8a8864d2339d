package app.api.campaign

import api.campaigns.models.CampaignStepType
import api.campaigns.services.CampaignId
import api.campaigns.{CampaignStepDAO, CampaignStepWithChildren}
import org.joda.time.DateTime
import org.scalatest.funspec.AnyFunSpec


class CampaignStepDAOSpec extends AnyFunSpec {

  private val campaign_id = CampaignId(id = 43)

  private val created_at = DateTime.now()

  private val step_51 = CampaignStepWithChildren(
    id = 51,
    label = None,
    delay = 86400,
    children = List(),
    campaign_id = campaign_id.id.toInt,
    step_type = CampaignStepType.AutoEmailStep,
    created_at = created_at,
    variants = Seq(),
  )

  private val step_43 = step_51.copy(
    id = 43,
    children = List(step_51.id.toInt)
  )

  private val step_23 = step_51.copy(
    id = 23,
    children = List(step_43.id.toInt)
  )

  private val step_99 = step_51.copy(
    id = 99,
    children = List(step_23.id.toInt)
  )

  describe("Test _getOrderedStepsWithLabels") {

    it("should fail if head step is not present") {

      val steps = List()

      assertThrows[java.util.NoSuchElementException] {

        CampaignStepDAO._getOrderedStepsWithLabels(
          steps = CampaignStepWithChildren.toCampaignStepWithChildrenForUpdatingStepLabels(steps),
          headStepId = step_51.id
        )

      }

    }

    it("should fail if child step not found") {

      val steps = List(step_43)

      assertThrows[java.util.NoSuchElementException] {

        CampaignStepDAO._getOrderedStepsWithLabels(
          steps = CampaignStepWithChildren.toCampaignStepWithChildrenForUpdatingStepLabels(steps),
          headStepId = step_43.id
        )

      }

    }

    it(
      "should ordered correctly and should match the order of getOrderedSteps when only head step is present"
    ) {

      val steps = List(step_51)

      val orderedStepsWithLabels = CampaignStepDAO._getOrderedStepsWithLabels(
        steps = CampaignStepWithChildren.toCampaignStepWithChildrenForUpdatingStepLabels(steps),
        headStepId = step_51.id
      )

      val expectedOrderFromGetOrderedSteps = CampaignStepDAO.getOrderedSteps(
        steps = steps,
        headStepId = step_51.id
      )

      val expectedOrder = CampaignStepWithChildren.toCampaignStepWithChildrenForUpdatingStepLabels(
        List(
          step_51.copy(label = Some("Day 1: Opening")),
        )
      )

      assert(
        orderedStepsWithLabels == expectedOrder &&
          expectedOrder.map(_.id) == expectedOrderFromGetOrderedSteps.map(_.id)
      )

    }

    it(
      "should ordered correctly and should match the order of getOrderedSteps when more than 1 steps are present"
    ) {

      val steps = List(step_23, step_43, step_51, step_99)

      val orderedStepsWithLabels = CampaignStepDAO._getOrderedStepsWithLabels(
        steps = CampaignStepWithChildren.toCampaignStepWithChildrenForUpdatingStepLabels(steps),
        headStepId = step_99.id
      )

      val expectedOrderFromGetOrderedSteps = CampaignStepDAO.getOrderedSteps(
        steps = steps,
        headStepId = step_99.id
      )

      val expectedOrder = CampaignStepWithChildren.toCampaignStepWithChildrenForUpdatingStepLabels(
        List(
          step_99.copy(label = Some("Day 1: Opening")),
          step_23.copy(label = Some("Day 2: Follow up 1")),
          step_43.copy(label = Some("Day 3: Follow up 2")),
          step_51.copy(label = Some("Day 4: Follow up 3"))
        )
      )

      assert(
        orderedStepsWithLabels == expectedOrder &&
          expectedOrder.map(_.id) == expectedOrderFromGetOrderedSteps.map(_.id)
      )

    }

    it(
      "should add the steps labels correctly wrt delay"
    ) {

      val step_43_172800 = step_43.copy(delay = 172800)

      val step_51_259283 = step_51.copy(delay = 259283)

      val steps = List(
        step_23,
        step_43_172800,
        step_51_259283,
        step_99
      )

      val orderedStepsWithLabels = CampaignStepDAO._getOrderedStepsWithLabels(
        steps = CampaignStepWithChildren.toCampaignStepWithChildrenForUpdatingStepLabels(steps),
        headStepId = step_99.id
      )

      val expectedOrderFromGetOrderedSteps = CampaignStepDAO.getOrderedSteps(
        steps = steps,
        headStepId = step_99.id
      )

      val expectedOrder = CampaignStepWithChildren.toCampaignStepWithChildrenForUpdatingStepLabels(
        List(
          step_99.copy(label = Some("Day 1: Opening")),
          step_23.copy(label = Some("Day 2: Follow up 1")),
          step_43_172800.copy(label = Some("Day 4: Follow up 2")),
          step_51_259283.copy(label = Some("Day 8: Follow up 3")) // Day 8, because 259283 (3 day + 83 seconds) will be ceil
        )
      )

      assert(
        orderedStepsWithLabels == expectedOrder &&
          expectedOrder.map(_.id) == expectedOrderFromGetOrderedSteps.map(_.id)
      )

    }


  }

}
