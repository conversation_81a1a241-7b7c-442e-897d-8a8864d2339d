package app.api.campaign.services

import api.campaigns.{CampaignStepVariantForScheduling, CampaignStepWithChildren}
import api.campaigns.models.{CampaignStepData, CampaignStepId, CampaignStepType, ValidateCampaignTemplateError, ValidateStepData}
import api.columns.ProspectColumnDef
import api.campaigns.services.{CampaignDAOService, CampaignId, CampaignTemplateService}
import org.joda.time.DateTime
import utils.templating.TemplateService
import org.scalamock.scalatest.AsyncMockFactory
import org.scalatest.funspec.AsyncFunSpec
import sr_scheduler.models.ChannelType

import scala.util.{Failure, Success}

class CampaignTemplateServiceSpec extends AsyncFunSpec with AsyncMockFactory  {

  val prospectColumnDef: ProspectColumnDef = mock[ProspectColumnDef]
  val templateService: TemplateService = mock[TemplateService]
  val campaignDAOService: CampaignDAOService = mock[CampaignDAOService]

  val campaignTemplateService: CampaignTemplateService = new CampaignTemplateService(
    templateService = templateService,
    prospectColumnDef = prospectColumnDef,
    campaignDAOService = campaignDAOService,
  )

  val stepId=Some(1L)
  val head_step_id = Some(2L)
  val teamId= 3L
  val channelType = ChannelType.LinkedinChannel

  private val campaignId = CampaignId(id = 17)

  val variant: CampaignStepVariantForScheduling = CampaignStepVariantForScheduling(
    id = 1,
    step_id = head_step_id.get,
    campaign_id = campaignId.id.toInt,
    template_id = None,
    step_data = CampaignStepData.AutoEmailStep(subject = "some subject", body = "Some body"),
    step_label = None,
    step_delay = 0,
    notes = None,
    priority = None,
    active = true,
    scheduled_count = 0
  )

  val step: CampaignStepWithChildren = CampaignStepWithChildren(
    id = head_step_id.get,
    label = None,
    campaign_id = campaignId.id.toInt,
    delay = 0,
    step_type = CampaignStepType.AutoEmailStep,
    created_at = DateTime.now(),
    children = List(),
    variants = Seq(variant)
  )

  val availableTagNames: List[String]= List("unsubscribe_link","sender_name", "email","previous_subject","signature","phone")

  describe("validation for body"){
    it("should fail when body has invalid merge tag")
    {
      val body = "{{hello}}"
      val subject = "this is subject"
      val campaignStepData: CampaignStepData = CampaignStepData.LinkedinInmailData(step_type = CampaignStepType.LinkedinInmail,
        subject = Some(subject), body = body)
      val notes = None
      (prospectColumnDef.getAvailableTagNames)
        .expects(teamId, Some(ChannelType.LinkedinChannel))
        .returning(availableTagNames)
      (templateService.isValid)
        .expects(body, availableTagNames, channelType)
        .returning(Failure(new Exception("Invalid merge tag")))


      val validateStepVariantData = ValidateStepData.ValidateStepVariantData(
        campaignStepData = campaignStepData,
        stepId = stepId,
        head_step_id = head_step_id,
        campaignId = campaignId
      )

      val result = campaignTemplateService.validateCampaignTemplate(
        teamId = teamId,
        validateStepData = validateStepVariantData,
        notes = notes
      )
      result match {
        case Left(value) => value match {
          case ValidateCampaignTemplateError.ErrorWhileValidatingBody(_) => assert(true)
          case _ => assert(false)
        }
        case Right(_) => assert(false)
      }
    }

    it("should fail when body has {{previous_subject}}")
    {
      val body = "This body contains {{previous_subject}}"
      val subject = "this is subject"
      val campaignStepData: CampaignStepData = CampaignStepData.LinkedinInmailData(step_type = CampaignStepType.LinkedinInmail,
        subject = Some(subject), body = body)
      val notes = None
      (prospectColumnDef.getAvailableTagNames)
        .expects(teamId, Some(ChannelType.LinkedinChannel))
        .returning(availableTagNames)
      (templateService.isValid)
        .expects(body, availableTagNames, channelType)
        .returning(Success(true))

      val validateStepVariantData = ValidateStepData.ValidateStepVariantData(
        campaignStepData = campaignStepData,
        stepId = stepId,
        head_step_id = head_step_id,
        campaignId = campaignId
      )

      val result = campaignTemplateService.validateCampaignTemplate(
        teamId = teamId,
        validateStepData = validateStepVariantData,
        notes = notes
      )
      result match {
        case Left(value) => value match {
          case ValidateCampaignTemplateError.BodyCantHavePreviousSubject(_) => assert(true)
          case _ => assert(false)
        }
        case Right(_) => assert(false)
      }
    }

    it("should fail when subject has invalid tags")
    {
      val body = "this is body"
      val subject = "this is subject {{hello}}"
      val campaignStepData: CampaignStepData = CampaignStepData.LinkedinInmailData(step_type = CampaignStepType.LinkedinInmail,
        subject = Some(subject), body = body)
      val notes = None
      (prospectColumnDef.getAvailableTagNames)
        .expects(teamId, Some(ChannelType.LinkedinChannel))
        .returning(availableTagNames)
      (templateService.isValid)
        .expects(body, availableTagNames, channelType)
        .returning(Success(true))
      (templateService.isValid)
        .expects(subject, availableTagNames, channelType)
        .returning(Failure(new Exception("Invalid merge tag")))

      val validateStepVariantData = ValidateStepData.ValidateStepVariantData(
        campaignStepData = campaignStepData,
        stepId = stepId,
        head_step_id = head_step_id,
        campaignId = campaignId
      )

      val result = campaignTemplateService.validateCampaignTemplate(
        teamId = teamId,
        validateStepData = validateStepVariantData,
        notes = notes
      )
      result match {
        case Left(value) => value match {
          case ValidateCampaignTemplateError.ErrorWhileValidatingSubject(_) => assert(true)
          case _ => assert(false)
        }
        case Right(_) => assert(false)
      }
    }

    it("should fail when subject of first mail contains {{previous_subject}}")
    {
      val body = "this is body"
      val subject = "this is subject {{previous_subject}}"
      val campaignStepData: CampaignStepData = CampaignStepData.LinkedinInmailData(step_type = CampaignStepType.LinkedinInmail,
        subject = Some(subject), body = body)
      val notes = None

      (prospectColumnDef.getAvailableTagNames)
        .expects(teamId, Some(ChannelType.LinkedinChannel))
        .returning(availableTagNames)

      (templateService.isValid)
        .expects(body, availableTagNames, channelType)
        .returning(Success(true))

      (templateService.isValid)
        .expects(subject, availableTagNames, channelType)
        .returning(Success(true))

      val validateStepVariantData = ValidateStepData.ValidateStepVariantData(
        campaignStepData = campaignStepData,
        stepId = stepId,
        head_step_id = stepId,
        campaignId = campaignId
      )

      val result = campaignTemplateService.validateCampaignTemplate(
        teamId = teamId,
        validateStepData = validateStepVariantData,
        notes = notes
      )
      result match {
        case Left(value) => value match {
          case ValidateCampaignTemplateError.PreviousSubInFirstEmail(_) => assert(true)
          case _ => assert(false)
        }
        case Right(_) => assert(false)
      }
    }

    it("should fail when subject contains {{signature}}")
    {
      val body = "this is body"
      val subject = "this is subject {{signature}}"
      val campaignStepData: CampaignStepData = CampaignStepData.LinkedinInmailData(step_type = CampaignStepType.LinkedinInmail,
        subject = Some(subject), body = body)
      val notes = None
      (prospectColumnDef.getAvailableTagNames)
        .expects(teamId, Some(ChannelType.LinkedinChannel))
        .returning(availableTagNames)
      (templateService.isValid)
        .expects(body, availableTagNames, channelType)
        .returning(Success(true))
      (templateService.isValid)
        .expects(subject, availableTagNames, channelType)
        .returning(Success(true))

      val validateStepVariantData = ValidateStepData.ValidateStepVariantData(
        campaignStepData = campaignStepData,
        stepId = stepId,
        head_step_id = stepId,
        campaignId = campaignId
      )

      val result = campaignTemplateService.validateCampaignTemplate(
        teamId = teamId,
        validateStepData = validateStepVariantData,
        notes = notes
      )
      result match {
        case Left(value) => value match {
          case ValidateCampaignTemplateError.SubjectCantHaveSignature(_) => assert(true)
          case _ => assert(false)
        }
        case Right(_) => assert(false)
      }
    }

    it("should fail when notes have invalid merge tags"){
      val body = "this is body"
      val subject = "this is subject"
      val campaignStepData: CampaignStepData = CampaignStepData.LinkedinInmailData(step_type = CampaignStepType.LinkedinInmail,
        subject = Some(subject), body = body)
      val notes = Some("hello {{world}}")
      (prospectColumnDef.getAvailableTagNames)
        .expects(teamId, Some(ChannelType.LinkedinChannel))
        .returning(availableTagNames)
      (templateService.isValid)
        .expects(body, availableTagNames, channelType)
        .returning(Success(true))
      (templateService.isValid)
        .expects(subject, availableTagNames, channelType)
        .returning(Success(true))
      (templateService.isValid)
        .expects(notes.get, availableTagNames, channelType)
        .returning(Failure(new Exception("Invalid merge tag")))

      val validateStepVariantData = ValidateStepData.ValidateStepVariantData(
        campaignStepData = campaignStepData,
        stepId = stepId,
        head_step_id = head_step_id,
        campaignId = campaignId
      )

      val result = campaignTemplateService.validateCampaignTemplate(
        teamId = teamId,
        validateStepData = validateStepVariantData,
        notes = notes
      )
      result match {
        case Left(value) => value match {
          case ValidateCampaignTemplateError.ErrorWhileValidatingNotes(_) => assert(true)
          case _ => assert(false)
        }
        case Right(_) => assert(false)
      }
    }

    it("should fail when notes contains {{previous_subject}}") {
      val body = "this is body"
      val subject = "this is subject"
      val campaignStepData: CampaignStepData = CampaignStepData.LinkedinInmailData(step_type = CampaignStepType.LinkedinInmail,
        subject = Some(subject), body = body)
      val notes = Some("hello {{previous_subject}}")
      (prospectColumnDef.getAvailableTagNames)
        .expects(teamId, Some(ChannelType.LinkedinChannel))
        .returning(availableTagNames)
      (templateService.isValid)
        .expects(body, availableTagNames, channelType)
        .returning(Success(true))
      (templateService.isValid)
        .expects(subject, availableTagNames, channelType)
        .returning(Success(true))
      (templateService.isValid)
        .expects(notes.get, availableTagNames, channelType)
        .returning(Success(true))

      val validateStepVariantData = ValidateStepData.ValidateStepVariantData(
        campaignStepData = campaignStepData,
        stepId = stepId,
        head_step_id = head_step_id,
        campaignId = campaignId
      )

      val result = campaignTemplateService.validateCampaignTemplate(
        teamId = teamId,
        validateStepData = validateStepVariantData,
        notes = notes
      )
      result match {
        case Left(value) => value match {
          case ValidateCampaignTemplateError.NotesCantHavePreviousSubject(_) => assert(true)
          case _ => assert(false)
        }
        case Right(_) => assert(false)
      }
    }

    it("should pass")
    {
      val body = "this is body"
      val subject = "this is subject"
      val campaignStepData: CampaignStepData = CampaignStepData.LinkedinInmailData(step_type = CampaignStepType.LinkedinInmail,
        subject = Some(subject), body = body)

      val notes = Some("this is notes")
      (prospectColumnDef.getAvailableTagNames)
        .expects(teamId,Some(ChannelType.LinkedinChannel))
        .returning(availableTagNames)
      (templateService.isValid)
        .expects(body, availableTagNames, channelType)
        .returning(Success(true))
      (templateService.isValid)
        .expects(subject, availableTagNames, channelType)
        .returning(Success(true))
      (templateService.isValid)
        .expects(notes.get, availableTagNames, channelType)
        .returning(Success(true))

      val validateStepVariantData = ValidateStepData.ValidateStepVariantData(
        campaignStepData = campaignStepData,
        stepId = stepId,
        head_step_id = head_step_id,
        campaignId = campaignId
      )

      val result = campaignTemplateService.validateCampaignTemplate(
        teamId = teamId,
        validateStepData = validateStepVariantData,
        notes = notes
      )
      assert(result == Right(true))
    }
  }

  describe("Test stepTypeSupportsPrevSubjectMergeTag") {

    it("should return true if step type is AutoEmailStep") {

      val res = CampaignTemplateService.stepTypeSupportsPrevSubjectMergeTag(
        campaignStepType = CampaignStepType.AutoEmailStep
      )

      assert(res)

    }

    it("should return true if step type is ManualEmailStep") {

      val res = CampaignTemplateService.stepTypeSupportsPrevSubjectMergeTag(
        campaignStepType = CampaignStepType.ManualEmailStep
      )

      assert(res)

    }

    it("should return false if step type is LinkedinMessage") {

      val res = CampaignTemplateService.stepTypeSupportsPrevSubjectMergeTag(
        campaignStepType = CampaignStepType.LinkedinMessage
      )

      assert(!res)

    }

    it("should return false if step type is LinkedinInmail") {

      val res = CampaignTemplateService.stepTypeSupportsPrevSubjectMergeTag(
        campaignStepType = CampaignStepType.LinkedinInmail
      )

      assert(!res)

    }

    it("should return false if step type is AutoLinkedinInmail") {

      val res = CampaignTemplateService.stepTypeSupportsPrevSubjectMergeTag(
        campaignStepType = CampaignStepType.AutoLinkedinInmail
      )

      assert(!res)

    }

  }

  describe("Test prevStepsAreValid") {

    it("should fail if adding head step with subject which contains {{previous_subject}} merge tag") {

      val res = CampaignTemplateService.prevStepsSupportPrevSubMergeTag(
        steps = List(),
        stepId = CampaignStepId(id = 8)
      )

      assert(!res)

    }

    it(
      "should fail if updating head step subject with a new subject which contains {{previous_subject}} merge tag"
    ) {

      val stepData = CampaignStepData.AutoEmailStep(
        subject = "Some Sub {{previous_subject}}",
        body = "Some body"
      )

      val res = CampaignTemplateService.prevStepsSupportPrevSubMergeTag(
        steps = List(step.copy(variants = Seq(variant.copy(step_data = stepData)))),
        stepId = CampaignStepId(id = head_step_id.get)
      )

      assert(!res)

    }

    it(
      "should fail if adding a step with {{previous_subject}} merge tag, when no previous step supports {{previous_subject}} merge tag"
    ) {

      val linkedinInmailData = CampaignStepData.LinkedinInmailData(
        subject = Some("Some Sub"),
        body = "Some body"
      )

      val linkedinInmailStep = step.copy(
        step_type = CampaignStepType.LinkedinInmail,
        variants = Seq(
          variant.copy(step_data = linkedinInmailData)
        )
      )

      val linkedinViewProfileStep = step.copy(
        step_type = CampaignStepType.LinkedinViewProfile,
        variants = Seq(
          variant.copy(step_data = CampaignStepData.LinkedinViewProfile())
        )
      )

      val res = CampaignTemplateService.prevStepsSupportPrevSubMergeTag(
        steps = List(linkedinViewProfileStep, linkedinInmailStep),
        stepId = CampaignStepId(id = 17)
      )

      assert(!res)

    }

    it(
      "should succeed when adding a step with {{previous_subject}} merge tag, when some previous step supports {{previous_subject}} merge tag"
    ) {

      val manualEmailStepData = CampaignStepData.ManualEmailStep(
        subject = "Some Sub",
        body = "Some body"
      )

      val manualEmailStep = step.copy(
        step_type = CampaignStepType.ManualEmailStep,
        variants = Seq(
          variant.copy(step_data = manualEmailStepData)
        )
      )

      val linkedinViewProfileStep = step.copy(
        step_type = CampaignStepType.LinkedinViewProfile,
        variants = Seq(
          variant.copy(step_data = CampaignStepData.LinkedinViewProfile())
        )
      )

      val res = CampaignTemplateService.prevStepsSupportPrevSubMergeTag(
        steps = List(manualEmailStep, linkedinViewProfileStep),
        stepId = CampaignStepId(id = 17)
      )

      assert(res)

    }

  }

  describe("Test containsPrevSubjectMergeTag") {

    it("should return false for invalid merge tag - brackets have space in between") {

      val invalidMergeTagSubject = "{ {    previous_subject }} some subj wasd  = "

      val res = CampaignTemplateService.containsPrevSubjectMergeTag(
        s = invalidMergeTagSubject
      )

      assert(!res)

    }

    it("should return false for invalid merge tag - incorrect bracket orientation") {

      val invalidMergeTagSubject = "hello world!    }}   previous_subject}}"

      val res = CampaignTemplateService.containsPrevSubjectMergeTag(
        s = invalidMergeTagSubject
      )

      assert(!res)

    }

    it("should return false for invalid merge tag - prefix extra invalid text in brackets") {

      val invalidMergeTagSubject = "some subj tt yyy  {{ some previous_subject}}"

      val res = CampaignTemplateService.containsPrevSubjectMergeTag(
        s = invalidMergeTagSubject
      )

      assert(!res)

    }

    it("should return false for invalid merge tag - postfix extra invalid text in brackets") {

      val invalidMergeTagSubject = "some subj  {{ previous_subject text}} tt yyy "

      val res = CampaignTemplateService.containsPrevSubjectMergeTag(
        s = invalidMergeTagSubject
      )

      assert(!res)

    }

    it("should return false for invalid merge tag - incorrect order") {

      val invalidMergeTagSubject = "some order  {{  }} previous_subject   "

      val res = CampaignTemplateService.containsPrevSubjectMergeTag(
        s = invalidMergeTagSubject
      )

      assert(!res)

    }

    it("should return true for valid merge tag") {

      val invalidMergeTagSubject = "some xaxb  {{previous_subject}} asd  gg"

      val res = CampaignTemplateService.containsPrevSubjectMergeTag(
        s = invalidMergeTagSubject
      )

      assert(res)

    }

    it("should return true for valid merge tag - space in between brackets and tag text") {

      val invalidMergeTagSubject = "some subj rews  =  7  {{    previous_subject }}"

      val res = CampaignTemplateService.containsPrevSubjectMergeTag(
        s = invalidMergeTagSubject
      )

      assert(res)

    }

    it("should return true for valid merge tag - space in between brackets and before tag text") {

      val invalidMergeTagSubject = "some   {{  previous_subject}}"

      val res = CampaignTemplateService.containsPrevSubjectMergeTag(
        s = invalidMergeTagSubject
      )

      assert(res)

    }

    it("should return true for valid merge tag - space in between brackets and after tag text") {

      val invalidMergeTagSubject = "some    asd  =    {{previous_subject    }}  "

      val res = CampaignTemplateService.containsPrevSubjectMergeTag(
        s = invalidMergeTagSubject
      )

      assert(res)

    }

  }

  describe("Test containsMergeTag") {

    it("should return false for invalid merge tag - brackets have space in between") {

      val invalidMergeTagSubject = "{ {    previous_subject }} some subj wasd  = "

      val res = CampaignTemplateService.containsMergeTag(
        mergeTagName = "previous_subject",
        s = invalidMergeTagSubject
      )

      assert(!res)

    }

    it("should return false for invalid merge tag - incorrect bracket orientation") {

      val invalidMergeTagSubject = "hello world!    }}   first_name}}"

      val res = CampaignTemplateService.containsMergeTag(
        mergeTagName = "first_name",
        s = invalidMergeTagSubject
      )

      assert(!res)

    }

    it("should return false for invalid merge tag - prefix extra invalid text in brackets") {

      val invalidMergeTagSubject = "some subj tt yyy  {{ some email}}"

      val res = CampaignTemplateService.containsMergeTag(
        mergeTagName = "email",
        s = invalidMergeTagSubject
      )

      assert(!res)

    }

    it("should return false for invalid merge tag - postfix extra invalid text in brackets") {

      val invalidMergeTagSubject = "some subj  {{ previous_subject text}} tt yyy "

      val res = CampaignTemplateService.containsMergeTag(
        mergeTagName = "previous_subject",
        s = invalidMergeTagSubject
      )

      assert(!res)

    }

    it("should return false for invalid merge tag - incorrect order") {

      val invalidMergeTagSubject = "some order  {{  }} previous_subject   "

      val res = CampaignTemplateService.containsMergeTag(
        mergeTagName = "previous_subject",
        s = invalidMergeTagSubject
      )

      assert(!res)

    }

    it("should return true for valid merge tag") {

      val invalidMergeTagSubject = "some xaxb  {{last_name}} asd  gg"

      val res = CampaignTemplateService.containsMergeTag(
        mergeTagName = "last_name",
        s = invalidMergeTagSubject
      )

      assert(res)

    }

    it("should return true for valid merge tag - space in between brackets and tag text") {

      val invalidMergeTagSubject = "some subj rews  =  7  {{    previous_subject }}"

      val res = CampaignTemplateService.containsMergeTag(
        mergeTagName = "previous_subject",
        s = invalidMergeTagSubject
      )

      assert(res)

    }

    it("should return true for valid merge tag - space in between brackets and before tag text") {

      val invalidMergeTagSubject = "some   {{  previous_subject}}"

      val res = CampaignTemplateService.containsMergeTag(
        mergeTagName = "previous_subject",
        s = invalidMergeTagSubject
      )

      assert(res)

    }

    it("should return true for valid merge tag - space in between brackets and after tag text") {

      val invalidMergeTagSubject = "some    asd  =    {{email    }}  "

      val res = CampaignTemplateService.containsMergeTag(
        mergeTagName = "email",
        s = invalidMergeTagSubject
      )

      assert(res)

    }

  }


}
