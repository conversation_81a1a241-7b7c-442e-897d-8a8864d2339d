package app.api.campaign.services

import api.accounts.models.{AccountId, OrgId}
import api.accounts.email.models.SrMxCheckESPType
import api.accounts.{AccountService, AccountUuid, TeamId}
import api.campaigns.dao.{CampaignSchedulingMetadataDAO, DripLogJedisDAO}
import api.campaigns.{CPAssignResult, CampaignAssignProspectIdData, CampaignEmailSettings, CampaignEmailSettingsUuid, CampaignIdAndTeamId, CampaignProspectDAO, CampaignSettings}
import api.campaigns.models.{CampaignEmailSettingsId, CampaignSetNextToBeScheduledAtData, CampaignType, IgnoreProspectsInOtherCampaigns}
import api.campaigns.services.{CampaignCacheService, CampaignId, CampaignProspectService, CampaignProspectTimezonesJedisService, CampaignService}
import api.emails.CampaignProspectStepScheduleLogsDAO
import api.prospects.{OwnedProspectsForAssigning, ProspectAccount, ProspectService, ProspectUpdateCategoryTemp, ProspectUuid}
import api.emails.daos.DomainPublicDNSDAO
import api.linkedin.LinkedinConnectionsDAO
import api.prospects.dao.ProspectAddEventDAO
import api.prospects.dao_service.ProspectDAOService
import api.prospects.models.{ProspectCategory, ProspectDataForChannelScheduling, ProspectId, StepId}
import api.prospects.service.ProspectServiceV2
import app.test_fixtures.campaign_settings.{CallSettingSenderDetailsFixtures, LinkedinSettingSenderDetailsFixtures, SmsSettingSenderDetailsFixtures, WhatsappSettingSenderDetailsFixtures}
import app.test_fixtures.prospect.ProspectFixtures
import eventframework.ProspectObject
import io.smartreach.esp.api.emails.EmailSettingId
import io.smartreach.sr_dns_utils.{DNSService, DomainPublicDNSData}
import org.joda.time.DateTime
import org.scalamock.scalatest.MockFactory
import org.scalatest.funspec.AnyFunSpec
import play.api.libs.json.{JsValue, Json}
import sr_scheduler.models.{CampaignEmailPriority, ChannelType}
import utils.SRLogger
import utils.dbutils.DBUtils
import utils.mq.email.MQDomainServiceProviderDNSService
import utils.sr_product_usage_data.models.SrUserFeatureUsageEvent.AddedFirstProspectInCampaign
import utils.sr_product_usage_data.services.SrUserFeatureUsageEventService

import scala.util.{Failure, Success}

class CampaignProspectServiceSpec extends AnyFunSpec with MockFactory{

//  val prospectDAO =  mock[Prospect]
  val campaignService = mock[CampaignService]
  val prospectAddEventDAO = mock[ProspectAddEventDAO]
  val prospectServiceV2 = mock[ProspectServiceV2]
  val campaignProspectDAO = mock[CampaignProspectDAO]
  val srUserFeatureUsageEventService = mock[SrUserFeatureUsageEventService]
  val campaignCacheService = mock[CampaignCacheService]
  val prospectDAOService = mock[ProspectDAOService]
  val dbUtils = mock[DBUtils]
  val campaignProspectJedisService = mock[CampaignProspectTimezonesJedisService]
  val campaignSchedulingMetadataDAO = mock[CampaignSchedulingMetadataDAO]

  val prospectService = mock[ProspectService]
  val domainPublicDNSDAO = mock[DomainPublicDNSDAO]
  val mqDomainServiceProviderDNSService = mock[MQDomainServiceProviderDNSService]

  val prospectUpdateCategoryTemp = mock[ProspectUpdateCategoryTemp]
  val campaignProspectStepScheduleLogsDAO = mock[CampaignProspectStepScheduleLogsDAO]
  val accountService = mock[AccountService]
  val dripLogJedisDAO = mock[DripLogJedisDAO]
  val linkedinConnectionsDAO = mock[LinkedinConnectionsDAO]

  val campaignProspectService = new CampaignProspectService(
//    prospectDAO: Prospect,
    campaignService= campaignService,
    prospectAddEventDAO= prospectAddEventDAO,
    prospectServiceV2 = prospectServiceV2,
    campaignProspectDAO = campaignProspectDAO,
    srUserFeatureUsageEventService= srUserFeatureUsageEventService,
    campaignCacheService = campaignCacheService,
    prospectDAOService = prospectDAOService,
    dbUtils = dbUtils,
    campaignProspectTimzonesJedisService = campaignProspectJedisService,
    campaignSchedulingMetadataDAO = campaignSchedulingMetadataDAO,
    prospectUpdateCategoryTemp = prospectUpdateCategoryTemp,
    domainPublicDNSDAO = domainPublicDNSDAO,
    linkedinConnectionsDAO = linkedinConnectionsDAO,
    mqDomainServiceProviderDNSService = mqDomainServiceProviderDNSService,
    campaignProspectStepScheduleLogsDAO = campaignProspectStepScheduleLogsDAO,
    accountService = accountService,
    dripLogJedisDAO = dripLogJedisDAO
  )

  val permittedAccountIds = Seq(1L, 2L)
  val doerOrgId = 10L
  val doerAccountId = 1L
  val doerAccountName = "Animesh"
  val accountId: Long = 3L
  val teamId: Long = 4L
  val campaignId: Long = 5L
  val campaignName = "campaignName"
  val prospectIds: List[Long] = List(6, 7, 8, 9, 10)
  given Logger: SRLogger = new SRLogger("CampaignProspectServiceSpec")
  val err = new Throwable("Error")
  val ownedProspectsForAssigning = OwnedProspectsForAssigning(
    prospect_id = 6,
    invalid_email = Some(false),
    email_bounced_at = None,
    email_bounced = Some(false),
    prospect_category_id_custom = 16,
    synced_previously_sent_step_for_deleted_prospect_step_id = None
  )

  val ownedProspectsForAssigningList = List(
    ownedProspectsForAssigning,
    ownedProspectsForAssigning.copy(
      prospect_id = 7,
      prospect_category_id_custom = 17
    ),
    ownedProspectsForAssigning.copy(
      prospect_id = 8,
      prospect_category_id_custom = 18
    ),
    ownedProspectsForAssigning.copy(
      prospect_id = 9,
      prospect_category_id_custom = 19
    ),
    ownedProspectsForAssigning.copy(
      prospect_id = 10,
      prospect_category_id_custom = 20
    )
  )
  val campaignSettings = CampaignSettings(
    campaign_email_settings = List(
      CampaignEmailSettings(
        campaign_id = CampaignId(campaignId),
        sender_email_setting_id = EmailSettingId(1),
        receiver_email_setting_id = EmailSettingId(1),
        team_id = TeamId(teamId),
        uuid = CampaignEmailSettingsUuid("temp_setting_id"),
        id = CampaignEmailSettingsId(123),
        sender_email = "<EMAIL>",
        receiver_email = "<EMAIL>",
        max_emails_per_day_from_email_account = 400,
        signature = Some("emailsignature"),
        error = None,
        from_name = None
      )
    ),
    campaign_linkedin_settings = List(
      LinkedinSettingSenderDetailsFixtures.linkedin_setting_sender_details
    ),
    campaign_call_settings = List(
      CallSettingSenderDetailsFixtures.call_setting_sender_details
    ),
    campaign_whatsapp_settings = List(
      WhatsappSettingSenderDetailsFixtures.whatsapp_setting_sender_details
    ),
    campaign_sms_settings = List(
      SmsSettingSenderDetailsFixtures.sms_setting_sender_details
    ),
    ai_sequence_status = None,
    timezone = "UTC",
    daily_from_time = 8,
    daily_till_time = 12,
    sending_holiday_calendar_id = None,
    days_preference = List(false, true, true, true, true, false, false),
    mark_completed_after_days = 4,
    max_emails_per_day = 40,
    open_tracking_enabled = false,
    click_tracking_enabled = false,
    enable_email_validation = false,
    ab_testing_enabled = false,
    warmup_started_at = None,
    warmup_length_in_days = None,
    warmup_starting_email_count = None,
    show_soft_start_setting = false,
    schedule_start_at = None,
    schedule_start_at_tz = None,
    email_priority = CampaignEmailPriority.FIRST_EMAIL,
    append_followups = true,
    opt_out_msg = "{{unsubscribe_link}}",
    opt_out_is_text = false,
    add_prospect_to_dnc_on_opt_out = true,
    triggers = Seq(),
    selected_calendar_data = None,
    campaign_type = CampaignType.MultiChannel,
    sending_mode = None,

    send_plain_text_email = None
  )

  describe("assign") {

    it("Empty prospectIds so will return success with empty lists") {

      val result = campaignProspectService.assignProspectsToCampaign(
        orgId = doerOrgId,
        permittedAccountIds = permittedAccountIds,
        doerAccountId = doerAccountId,
        doerAccountName = doerAccountName,
        accountId = accountId,
        teamId = teamId,
        campaignId = campaignId,
        campaignName = campaignName,
        campaignSettings = campaignSettings ,
        prospectIds = List(),
        ignoreProspectsInOtherCampaigns = IgnoreProspectsInOtherCampaigns.DoNotIgnore,
        Logger = Logger
      )

      assert(result == Success(
        CPAssignResult(
          prospectIdsIgnoredBecauseAlreadyAssignedToThisCampaign = List(),

          prospectIdsIgnoredBecauseInOtherCampaigns = List(),

          newlyAssignedProspectIds = List()
        )
      ))


    }

    it("prospectServiceV2.filterOwnedProspects failed") {

      (prospectServiceV2.filterOwnedProspects)
        .expects(prospectIds, permittedAccountIds, teamId, OrgId(doerOrgId),  *)
        .returning(Failure(err))
      val result = campaignProspectService.assignProspectsToCampaign(
        orgId = doerOrgId,
        permittedAccountIds = permittedAccountIds,
        doerAccountId = doerAccountId,
        doerAccountName = doerAccountName,
        accountId = accountId,
        teamId = teamId,
        campaignId = campaignId,
        campaignName = campaignName,
        campaignSettings = campaignSettings,
        prospectIds = prospectIds,
        ignoreProspectsInOtherCampaigns = IgnoreProspectsInOtherCampaigns.DoNotIgnore,
        Logger: SRLogger
      )

      assert(result == Failure(err))


    }

    it("Prospects sent that are not owned") {

      (prospectServiceV2.filterOwnedProspects)
        .expects(List(6L, 7, 8, 9, 10, 11), permittedAccountIds, teamId, OrgId(doerOrgId), *)
        .returning(Success(ownedProspectsForAssigningList))
      val result = campaignProspectService.assignProspectsToCampaign(
        orgId = doerOrgId,
        permittedAccountIds = permittedAccountIds,
        doerAccountId = doerAccountId,
        doerAccountName = doerAccountName,
        accountId = accountId,
        teamId = teamId,
        campaignId = campaignId,
        campaignName = campaignName,
        campaignSettings = campaignSettings,
        prospectIds = List(6L, 7, 8, 9, 10, 11),
        ignoreProspectsInOtherCampaigns = IgnoreProspectsInOtherCampaigns.DoNotIgnore,
        Logger: SRLogger
      )

      assert(result.isFailure)
      assert(result.failed.get.getMessage == "Given prospects do not exist in your account")


    }

    it("All the prospects are already in campaign") {

      (prospectServiceV2.filterOwnedProspects)
        .expects(prospectIds, permittedAccountIds, teamId, OrgId(doerOrgId), *)
        .returning(Success(ownedProspectsForAssigningList))
      (campaignProspectDAO.getprospectIdsAlreadyAssignedToACampaign)
        .expects(prospectIds, campaignId, TeamId(teamId))
        .returning(prospectIds)
      (campaignProspectDAO.getOldStepIdForCampaign)
        .expects(Seq(), CampaignId(5L), TeamId(teamId))
        .returning(List())

      (prospectUpdateCategoryTemp.autoUpdateProspectCategory(
        _: TeamId, _: Seq[ProspectId], _: AccountId, _: ProspectCategory.ProspectCategory
      )(_: SRLogger)
        )
        .expects(
          TeamId(id = teamId),
          Seq(),
          AccountId(id = doerAccountId),
          ProspectCategory.OPEN,
          Logger,
        )
        .returning(Success(0))

      val result = campaignProspectService.assignProspectsToCampaign(
        orgId = doerOrgId,
        permittedAccountIds = permittedAccountIds,
        doerAccountId = doerAccountId,
        doerAccountName = doerAccountName,
        accountId = accountId,
        teamId = teamId,
        campaignId = campaignId,
        campaignName = campaignName,
        campaignSettings = campaignSettings,
        prospectIds = prospectIds,
        ignoreProspectsInOtherCampaigns = IgnoreProspectsInOtherCampaigns.DoNotIgnore,
        Logger: SRLogger
      )

      assert(result == Success(
        CPAssignResult(
          prospectIdsIgnoredBecauseAlreadyAssignedToThisCampaign = prospectIds,

        prospectIdsIgnoredBecauseInOtherCampaigns = List(),

        newlyAssignedProspectIds = List())
      ))
    }

    it("Pass with do not ignore") {

      (prospectServiceV2.filterOwnedProspects)
        .expects(prospectIds, permittedAccountIds, teamId, OrgId(doerOrgId), *)
        .returning(Success(ownedProspectsForAssigningList))
      (campaignProspectDAO.getprospectIdsAlreadyAssignedToACampaign)
        .expects(prospectIds, campaignId, TeamId(teamId))
        .returning(List())
      (campaignProspectDAO.assignProspectsToCampaign)
        .expects(ownedProspectsForAssigningList, campaignId, TeamId(teamId))
        .returning(prospectIds)

      (prospectAddEventDAO.addEvents)
        .expects(*)
        .returning(Success(List()))

      (campaignService.setNextToBeScheduledAt)
        .expects(
          CampaignSetNextToBeScheduledAtData.AddedProspectToCampaignFlow(
            campaignId = CampaignId(campaignId),
            campaignSettings = campaignSettings,
            teamId = TeamId(teamId)
          ), *, TeamId(teamId))
        .returning(Success(1))

      (campaignSchedulingMetadataDAO.scheduleForUpdateWhenCampaignIsUpdated)
        .expects(CampaignId(campaignId), TeamId(teamId))
        .returning(Success(1))
      (srUserFeatureUsageEventService.addFeatureUsageEvent)
        .expects(10, * )
        .returning(Success(1))

      (campaignCacheService.resetCampaignStats(_: Long, _: Long)(using _: SRLogger))
        .expects(5, 4, *)
        .returning(())
      (campaignProspectDAO.getOldStepIdForCampaign)
      .expects(Seq(ProspectId(6L), ProspectId(7L), ProspectId(8L), ProspectId(9L), ProspectId(10L)), CampaignId(5L), TeamId(teamId))
        .returning(List())
      (campaignCacheService.resetSenderRotationById(_: CampaignId, _: TeamId)(using _: SRLogger))
        .expects(CampaignId(5), TeamId(4), *)
        .returning({})

      (prospectUpdateCategoryTemp.autoUpdateProspectCategory(
        _: TeamId, _: Seq[ProspectId], _: AccountId, _: ProspectCategory.ProspectCategory
      )(_: SRLogger)
        )
        .expects(
          TeamId(id = teamId),
          prospectIds.map(pid => ProspectId(id = pid)),
          AccountId(id = doerAccountId),
          ProspectCategory.OPEN,
          Logger,
        )
        .returning(Success(prospectIds.length))

      val result = campaignProspectService.assignProspectsToCampaign(
        orgId = doerOrgId,
        permittedAccountIds = permittedAccountIds,
        doerAccountId = doerAccountId,
        doerAccountName = doerAccountName,
        accountId = accountId,
        teamId = teamId,
        campaignId = campaignId,
        campaignName = campaignName,
        campaignSettings = campaignSettings,
        prospectIds = prospectIds,
        ignoreProspectsInOtherCampaigns = IgnoreProspectsInOtherCampaigns.DoNotIgnore,
        Logger: SRLogger
      )

      assert(result == Success(
        CPAssignResult(
          prospectIdsIgnoredBecauseAlreadyAssignedToThisCampaign = List(),

          prospectIdsIgnoredBecauseInOtherCampaigns = List(),

          newlyAssignedProspectIds = prospectIds)
      ))


    }

    it("pass with IgnoreProspectsActiveInOtherCampaigns") {

      val newlyAssignedProspectIds = Seq(ProspectId(8L), ProspectId(9L), ProspectId(10L))

      (prospectServiceV2.filterOwnedProspects)
        .expects(prospectIds, permittedAccountIds, teamId, OrgId(doerOrgId), *)
        .returning(Success(ownedProspectsForAssigningList))
      (campaignProspectDAO.getprospectIdsAlreadyAssignedToACampaign)
        .expects(prospectIds, campaignId, TeamId(teamId))
        .returning(List(7))
      (campaignProspectDAO.getProspectsActiveInOtherCampaigns)
      .expects(prospectIds.filterNot(a => a == 7), 5, TeamId(teamId))
        .returning(List(6))
      (campaignProspectDAO.assignProspectsToCampaign)
        .expects(ownedProspectsForAssigningList.filterNot(a => a.prospect_id == 6  || a.prospect_id == 7), campaignId, TeamId(teamId))
        .returning(prospectIds.filterNot(a => a == 6 || a == 7))
      (prospectAddEventDAO.addEvents)
        .expects(*)
        .returning(Success(List()))

      (campaignService.setNextToBeScheduledAt)
        .expects(
          CampaignSetNextToBeScheduledAtData.AddedProspectToCampaignFlow(
            campaignId = CampaignId(campaignId),
            campaignSettings = campaignSettings,
            teamId = TeamId(teamId)
          ),
          *, TeamId(teamId))

        .returning(Success(1))
      (campaignSchedulingMetadataDAO.scheduleForUpdateWhenCampaignIsUpdated)
        .expects(CampaignId(campaignId), TeamId(teamId))
        .returning(Success(1))
      (srUserFeatureUsageEventService.addFeatureUsageEvent)
        .expects(10, * )
        .returning(Success(1))

      (campaignCacheService.resetCampaignStats(_: Long, _: Long)(using _: SRLogger))
        .expects(5, 4, *)
        .returning(())
      (campaignProspectDAO.getOldStepIdForCampaign)
        .expects(newlyAssignedProspectIds, CampaignId(5L), TeamId(teamId))
        .returning(List())
      (campaignCacheService.resetSenderRotationById(_: CampaignId, _: TeamId)(using _: SRLogger))
        .expects(CampaignId(5), TeamId(4), *)
        .returning({})

      (prospectUpdateCategoryTemp.autoUpdateProspectCategory(
        _: TeamId, _: Seq[ProspectId], _: AccountId, _: ProspectCategory.ProspectCategory
      )(_: SRLogger)
        )
        .expects(
          TeamId(id = teamId),
          newlyAssignedProspectIds,
          AccountId(id = doerAccountId),
          ProspectCategory.OPEN,
          Logger,
        )
        .returning(Success(newlyAssignedProspectIds.length))

      val result = campaignProspectService.assignProspectsToCampaign(
        orgId = doerOrgId,
        permittedAccountIds = permittedAccountIds,
        doerAccountId = doerAccountId,
        doerAccountName = doerAccountName,
        accountId = accountId,
        teamId = teamId,
        campaignId = campaignId,
        campaignName = campaignName,
        campaignSettings = campaignSettings,
        prospectIds = prospectIds,
        ignoreProspectsInOtherCampaigns = IgnoreProspectsInOtherCampaigns.IgnoreProspectsActiveInOtherCampaigns,
        Logger: SRLogger
      )

      assert(result == Success(
        CPAssignResult(
          prospectIdsIgnoredBecauseAlreadyAssignedToThisCampaign = List(7),

          prospectIdsIgnoredBecauseInOtherCampaigns = List(6),

          newlyAssignedProspectIds = List(8, 9, 10))
      ))


    }

    it("pass with IgnoreProspectsAddedInOtherCampaigns") {

      val newlyAssignedProspectIds = Seq(ProspectId(8L), ProspectId(9L), ProspectId(10L))

      (prospectServiceV2.filterOwnedProspects)
        .expects(prospectIds, permittedAccountIds, teamId, OrgId(doerOrgId), *)
        .returning(Success(ownedProspectsForAssigningList))
      (campaignProspectDAO.getprospectIdsAlreadyAssignedToACampaign)
        .expects(prospectIds, campaignId, TeamId(teamId))
        .returning(List(7))
      (campaignProspectDAO.getProspectsAddedInOtherCampaigns)
        .expects(prospectIds.filterNot(a => a == 7), 5, TeamId(teamId))
        .returning(List(6))
      (campaignProspectDAO.assignProspectsToCampaign)
        .expects(ownedProspectsForAssigningList.filterNot(a => a.prospect_id == 6 || a.prospect_id == 7), campaignId, TeamId(teamId))
        .returning(prospectIds.filterNot(a => a == 6 || a == 7))
      (prospectAddEventDAO.addEvents)
        .expects(*)
        .returning(Success(List()))

      (campaignService.setNextToBeScheduledAt)
        .expects(

          CampaignSetNextToBeScheduledAtData.AddedProspectToCampaignFlow(
            campaignId = CampaignId(campaignId),
            campaignSettings = campaignSettings,
            teamId = TeamId(teamId)
          ),
          *, TeamId(teamId))
        .returning(Success(1))
      (campaignSchedulingMetadataDAO.scheduleForUpdateWhenCampaignIsUpdated)
        .expects(CampaignId(campaignId), TeamId(teamId))
        .returning(Success(1))
      (srUserFeatureUsageEventService.addFeatureUsageEvent)
        .expects(10, * )
        .returning(Success(1))

      (campaignCacheService.resetCampaignStats(_: Long, _: Long)(using _: SRLogger))
        .expects(5, 4, *)
        .returning(())

      (campaignProspectDAO.getOldStepIdForCampaign)
        .expects(newlyAssignedProspectIds, CampaignId(5L), TeamId(teamId))
        .returning(List())
      (campaignCacheService.resetSenderRotationById(_: CampaignId, _: TeamId)(using _: SRLogger))
        .expects(CampaignId(5), TeamId(4), *)
        .returning({})

      (prospectUpdateCategoryTemp.autoUpdateProspectCategory(
        _: TeamId, _: Seq[ProspectId], _: AccountId, _: ProspectCategory.ProspectCategory
      )(_: SRLogger)
        )
        .expects(
          TeamId(id = teamId),
          newlyAssignedProspectIds,
          AccountId(id = doerAccountId),
          ProspectCategory.OPEN,
          Logger,
        )
        .returning(Success(newlyAssignedProspectIds.length))

      val result = campaignProspectService.assignProspectsToCampaign(
        orgId = doerOrgId,
        permittedAccountIds = permittedAccountIds,
        doerAccountId = doerAccountId,
        doerAccountName = doerAccountName,
        accountId = accountId,
        teamId = teamId,
        campaignId = campaignId,
        campaignName = campaignName,
        campaignSettings = campaignSettings,
        prospectIds = prospectIds,
        ignoreProspectsInOtherCampaigns = IgnoreProspectsInOtherCampaigns.IgnoreProspectsAddedInOtherCampaigns,
        Logger: SRLogger
      )

      assert(result == Success(
        CPAssignResult(
          prospectIdsIgnoredBecauseAlreadyAssignedToThisCampaign = List(7),

          prospectIdsIgnoredBecauseInOtherCampaigns = List(6),

          newlyAssignedProspectIds = List(8, 9, 10))
      ))


    }




  }

  describe("assignV3") {

    it("Empty prospectIds so will return success with empty lists") {

      val result = campaignProspectService.assignV3(
        orgId = doerOrgId,
        permittedAccountIds = permittedAccountIds,
        doerAccountId = doerAccountId,
        doerAccountName = doerAccountName,
        accountId = accountId,
        teamId = teamId,
        campaignId = campaignId,
        campaignName = campaignName,
        campaignSettings = campaignSettings,
        prospectIds = List(),
        ignoreProspectsInOtherCampaigns = IgnoreProspectsInOtherCampaigns.DoNotIgnore,
        accountOwnedProspects = ownedProspectsForAssigningList,

        Logger: SRLogger
      )

      assert(result == Success(
        CPAssignResult(
          prospectIdsIgnoredBecauseAlreadyAssignedToThisCampaign = List(),

          prospectIdsIgnoredBecauseInOtherCampaigns = List(),

          newlyAssignedProspectIds = List()
        )
      ))


    }

    it("All the prospects are already in campaign") {

      (campaignProspectDAO.getprospectIdsAlreadyAssignedToACampaign)
        .expects(prospectIds, campaignId, TeamId(teamId))
        .returning(prospectIds)

      (campaignProspectDAO.getOldStepIdForCampaign)
        .expects(Seq(), CampaignId(5L), TeamId(teamId))
        .returning(List())

      (prospectUpdateCategoryTemp.autoUpdateProspectCategory(
        _: TeamId, _: Seq[ProspectId], _: AccountId, _: ProspectCategory.ProspectCategory
      )(_: SRLogger)
        )
        .expects(
          TeamId(id = teamId),
          Seq(),
          AccountId(id = doerAccountId),
          ProspectCategory.OPEN,
          Logger,
        )
        .returning(Success(0))

      val result = campaignProspectService.assignV3(
        orgId = doerOrgId,
        permittedAccountIds = permittedAccountIds,
        doerAccountId = doerAccountId,
        doerAccountName = doerAccountName,
        accountId = accountId,
        teamId = teamId,
        campaignId = campaignId,
        campaignName = campaignName,
        campaignSettings = campaignSettings,
        prospectIds = prospectIds,
        ignoreProspectsInOtherCampaigns = IgnoreProspectsInOtherCampaigns.DoNotIgnore,
        accountOwnedProspects = ownedProspectsForAssigningList,
        Logger: SRLogger
      )

      assert(
        result == Success(
          CPAssignResult(
            prospectIdsIgnoredBecauseAlreadyAssignedToThisCampaign = prospectIds,

            prospectIdsIgnoredBecauseInOtherCampaigns = List(),

            newlyAssignedProspectIds = List()
          )
        )
      )

    }

    it("should fail when error in prospectAddEventDAO.addEvents") {

      (campaignProspectDAO.getprospectIdsAlreadyAssignedToACampaign)
        .expects(prospectIds, campaignId, TeamId(teamId))
        .returning(List())

      (campaignProspectDAO.assignProspectsToCampaign)
        .expects(ownedProspectsForAssigningList, campaignId, TeamId(teamId))
        .returning(prospectIds)
      (campaignSchedulingMetadataDAO.scheduleForUpdateWhenCampaignIsUpdated)
        .expects(CampaignId(campaignId), TeamId(teamId))
        .returning(Success(1))
      (prospectAddEventDAO.addEvents)
        .expects(*)
        .returning(Failure(new Exception("Some exception")))

      (campaignProspectDAO.getOldStepIdForCampaign)
        .expects(Seq(ProspectId(6L), ProspectId(7L), ProspectId(8L), ProspectId(9L), ProspectId(10L)), CampaignId(5L), TeamId(teamId))
        .returning(List())

      val result = campaignProspectService.assignV3(
        orgId = doerOrgId,
        permittedAccountIds = permittedAccountIds,
        doerAccountId = doerAccountId,
        doerAccountName = doerAccountName,
        accountId = accountId,
        teamId = teamId,
        campaignId = campaignId,
        campaignName = campaignName,
        campaignSettings = campaignSettings,
        prospectIds = prospectIds,
        ignoreProspectsInOtherCampaigns = IgnoreProspectsInOtherCampaigns.DoNotIgnore,
        accountOwnedProspects = ownedProspectsForAssigningList,
        Logger: SRLogger
      )

      result match {
        case Failure(_) => assert(true)

        case Success(_) => assert(false)
      }


    }

    it("should fail when error in prospectAddEventDAO.addFeatureUsageEvent") {

      (campaignProspectDAO.getprospectIdsAlreadyAssignedToACampaign)
        .expects(prospectIds, campaignId, TeamId(teamId))
        .returning(List())

      (campaignProspectDAO.assignProspectsToCampaign)
        .expects(ownedProspectsForAssigningList, campaignId, TeamId(teamId))
        .returning(prospectIds)
      (campaignSchedulingMetadataDAO.scheduleForUpdateWhenCampaignIsUpdated)
        .expects(CampaignId(campaignId), TeamId(teamId))
        .returning(Success(1))
      (prospectAddEventDAO.addEvents)
        .expects(*)
        .returning(Success(List()))

      (srUserFeatureUsageEventService.addFeatureUsageEvent)
        .expects(10, *)
        .returning(Failure(new Exception("Some exception")))

      (campaignProspectDAO.getOldStepIdForCampaign)
        .expects(Seq(ProspectId(6L), ProspectId(7L), ProspectId(8L), ProspectId(9L), ProspectId(10L)), CampaignId(5L), TeamId(teamId))
        .returning(List())

      val result = campaignProspectService.assignV3(
        orgId = doerOrgId,
        permittedAccountIds = permittedAccountIds,
        doerAccountId = doerAccountId,
        doerAccountName = doerAccountName,
        accountId = accountId,
        teamId = teamId,
        campaignId = campaignId,
        campaignName = campaignName,
        campaignSettings = campaignSettings,
        prospectIds = prospectIds,
        ignoreProspectsInOtherCampaigns = IgnoreProspectsInOtherCampaigns.DoNotIgnore,
        accountOwnedProspects = ownedProspectsForAssigningList,
        Logger: SRLogger
      )

      result match {
        case Failure(_) => assert(true)

        case Success(_) => assert(false)
      }


    }

    it("should fail when error in campaignService.setNextToBeScheduledAt") {

      (campaignProspectDAO.getprospectIdsAlreadyAssignedToACampaign)
        .expects(prospectIds, campaignId, TeamId(teamId))
        .returning(List())

      (campaignProspectDAO.assignProspectsToCampaign)
        .expects(ownedProspectsForAssigningList, campaignId, TeamId(teamId))
        .returning(prospectIds)
      (campaignSchedulingMetadataDAO.scheduleForUpdateWhenCampaignIsUpdated)
        .expects(CampaignId(campaignId), TeamId(teamId))
        .returning(Success(1))
      (prospectAddEventDAO.addEvents)
        .expects(*)
        .returning(Success(List()))

      (campaignService.setNextToBeScheduledAt)
        .expects(

          CampaignSetNextToBeScheduledAtData.AddedProspectToCampaignFlow(
            campaignId = CampaignId(campaignId),
            campaignSettings = campaignSettings,
            teamId = TeamId(teamId)
          ),
          *, TeamId(teamId))
        .returning(Failure(new Exception("some exception")))

      (srUserFeatureUsageEventService.addFeatureUsageEvent)
        .expects(10, *)
        .returning(Success(1))

      (campaignProspectDAO.getOldStepIdForCampaign)
        .expects(Seq(ProspectId(6L), ProspectId(7L), ProspectId(8L), ProspectId(9L), ProspectId(10L)), CampaignId(5L), TeamId(teamId))
        .returning(List())

      val result = campaignProspectService.assignV3(
        orgId = doerOrgId,
        permittedAccountIds = permittedAccountIds,
        doerAccountId = doerAccountId,
        doerAccountName = doerAccountName,
        accountId = accountId,
        teamId = teamId,
        campaignId = campaignId,
        campaignName = campaignName,
        campaignSettings = campaignSettings,
        prospectIds = prospectIds,
        ignoreProspectsInOtherCampaigns = IgnoreProspectsInOtherCampaigns.DoNotIgnore,
        accountOwnedProspects = ownedProspectsForAssigningList,
        Logger: SRLogger
      )

      result match {
        case Failure(_) => assert(true)

        case Success(_) => assert(false)
      }


    }


    it("Pass with do not ignore") {

      val newlyAssignedProspectIds = Seq(ProspectId(6L), ProspectId(7L), ProspectId(8L), ProspectId(9L), ProspectId(10L))

      (campaignProspectDAO.getprospectIdsAlreadyAssignedToACampaign)
        .expects(prospectIds, campaignId, TeamId(teamId))
        .returning(List())
      (campaignProspectDAO.assignProspectsToCampaign)
        .expects(ownedProspectsForAssigningList, campaignId, TeamId(teamId))
        .returning(prospectIds)

      (prospectAddEventDAO.addEvents)
        .expects(*)
        .returning(Success(List()))
      (campaignService.setNextToBeScheduledAt)
        .expects(

          CampaignSetNextToBeScheduledAtData.AddedProspectToCampaignFlow(
            campaignId = CampaignId(campaignId),
            campaignSettings = campaignSettings,
            teamId = TeamId(teamId)
          ),
          *, TeamId(teamId))
        .returning(Success(1))
      (campaignSchedulingMetadataDAO.scheduleForUpdateWhenCampaignIsUpdated)
        .expects(CampaignId(campaignId), TeamId(teamId))
        .returning(Success(1))
      (srUserFeatureUsageEventService.addFeatureUsageEvent)
        .expects(10, *)
        .returning(Success(1))

      (campaignCacheService.resetCampaignStats(_: Long, _: Long)(using _: SRLogger))
        .expects(5, 4, *)
        .returning(())
      (campaignProspectDAO.getOldStepIdForCampaign)
        .expects(newlyAssignedProspectIds, CampaignId(5L), TeamId(teamId))
        .returning(List())

      (prospectUpdateCategoryTemp.autoUpdateProspectCategory(
        _: TeamId, _: Seq[ProspectId], _: AccountId, _: ProspectCategory.ProspectCategory
      )(_: SRLogger)
        )
        .expects(
          TeamId(id = teamId),
          newlyAssignedProspectIds,
          AccountId(id = doerAccountId),
          ProspectCategory.OPEN,
          Logger,
        )
        .returning(Success(newlyAssignedProspectIds.length))

      val result = campaignProspectService.assignV3(
        orgId = doerOrgId,
        permittedAccountIds = permittedAccountIds,
        doerAccountId = doerAccountId,
        doerAccountName = doerAccountName,
        accountId = accountId,
        teamId = teamId,
        campaignId = campaignId,
        campaignName = campaignName,
        campaignSettings = campaignSettings,
        prospectIds = prospectIds,
        ignoreProspectsInOtherCampaigns = IgnoreProspectsInOtherCampaigns.DoNotIgnore,
        accountOwnedProspects = ownedProspectsForAssigningList,
        Logger: SRLogger
      )

      assert(result == Success(
        CPAssignResult(
          prospectIdsIgnoredBecauseAlreadyAssignedToThisCampaign = List(),

          prospectIdsIgnoredBecauseInOtherCampaigns = List(),

          newlyAssignedProspectIds = prospectIds)
      ))


    }

    it("pass with IgnoreProspectsActiveInOtherCampaigns") {

      val newlyAssignedProspectIds = Seq(ProspectId(8L), ProspectId(9L), ProspectId(10L))

      (campaignProspectDAO.getprospectIdsAlreadyAssignedToACampaign)
        .expects(prospectIds, campaignId, TeamId(teamId))
        .returning(List(7))
      (campaignProspectDAO.getProspectsActiveInOtherCampaigns)
        .expects(prospectIds.filterNot(a => a == 7), 5, TeamId(teamId))
        .returning(List(6))
      (campaignSchedulingMetadataDAO.scheduleForUpdateWhenCampaignIsUpdated)
        .expects(CampaignId(campaignId), TeamId(teamId))
        .returning(Success(1))
      (campaignProspectDAO.assignProspectsToCampaign)
        .expects(ownedProspectsForAssigningList.filterNot(a => a.prospect_id == 6 || a.prospect_id == 7), campaignId, TeamId(teamId))
        .returning(prospectIds.filterNot(a => a == 6 || a == 7))
      (prospectAddEventDAO.addEvents)
        .expects(*)
        .returning(Success(List()))

      (campaignService.setNextToBeScheduledAt)
        .expects(

          CampaignSetNextToBeScheduledAtData.AddedProspectToCampaignFlow(
            campaignId = CampaignId(campaignId),
            campaignSettings = campaignSettings,
            teamId = TeamId(teamId)
          ),
          *, TeamId(teamId))
        .returning(Success(1))

      (srUserFeatureUsageEventService.addFeatureUsageEvent)
        .expects(10, *)
        .returning(Success(1))

      (campaignCacheService.resetCampaignStats(_: Long, _: Long)(using _: SRLogger))
        .expects(5, 4, *)
        .returning(())
      (campaignProspectDAO.getOldStepIdForCampaign)
        .expects(newlyAssignedProspectIds, CampaignId(5L), TeamId(teamId))
        .returning(List())

      (prospectUpdateCategoryTemp.autoUpdateProspectCategory(
        _: TeamId, _: Seq[ProspectId], _: AccountId, _: ProspectCategory.ProspectCategory
      )(_: SRLogger)
        )
        .expects(
          TeamId(id = teamId),
          newlyAssignedProspectIds,
          AccountId(id = doerAccountId),
          ProspectCategory.OPEN,
          Logger,
        )
        .returning(Success(newlyAssignedProspectIds.length))

      val result = campaignProspectService.assignV3(
        orgId = doerOrgId,
        permittedAccountIds = permittedAccountIds,
        doerAccountId = doerAccountId,
        doerAccountName = doerAccountName,
        accountId = accountId,
        teamId = teamId,
        campaignId = campaignId,
        campaignName = campaignName,
        campaignSettings = campaignSettings,
        prospectIds = prospectIds,
        ignoreProspectsInOtherCampaigns = IgnoreProspectsInOtherCampaigns.IgnoreProspectsActiveInOtherCampaigns,
        accountOwnedProspects = ownedProspectsForAssigningList,
        Logger: SRLogger
      )

      assert(result == Success(
        CPAssignResult(
          prospectIdsIgnoredBecauseAlreadyAssignedToThisCampaign = List(7),

          prospectIdsIgnoredBecauseInOtherCampaigns = List(6),

          newlyAssignedProspectIds = List(8, 9, 10))
      ))


    }

    it("pass with IgnoreProspectsAddedInOtherCampaigns") {

      val newlyAssignedProspectIds = Seq(ProspectId(8L), ProspectId(9L), ProspectId(10L))

      (campaignProspectDAO.getprospectIdsAlreadyAssignedToACampaign)
        .expects(prospectIds, campaignId, TeamId(teamId))
        .returning(List(7))
      (campaignProspectDAO.getProspectsAddedInOtherCampaigns)
        .expects(prospectIds.filterNot(a => a == 7), 5, TeamId(teamId))
        .returning(List(6))
      (campaignProspectDAO.assignProspectsToCampaign)
        .expects(ownedProspectsForAssigningList.filterNot(a => a.prospect_id == 6 || a.prospect_id == 7), campaignId, TeamId(teamId))
        .returning(prospectIds.filterNot(a => a == 6 || a == 7))
      (prospectAddEventDAO.addEvents)
        .expects(*)
        .returning(Success(List()))

      (campaignService.setNextToBeScheduledAt)
        .expects(

          CampaignSetNextToBeScheduledAtData.AddedProspectToCampaignFlow(
            campaignId = CampaignId(campaignId),
            campaignSettings = campaignSettings,
            teamId = TeamId(teamId)
          )
          , Logger, TeamId(teamId))
        .returning(Success(1))
      (campaignSchedulingMetadataDAO.scheduleForUpdateWhenCampaignIsUpdated)
        .expects(CampaignId(campaignId), TeamId(teamId))
        .returning(Success(1))
      (srUserFeatureUsageEventService.addFeatureUsageEvent)
        .expects(10, *)
        .returning(Success(1))

      (campaignCacheService.resetCampaignStats(_: Long, _: Long)(using _: SRLogger))
        .expects(5, 4, *)
        .returning(())

      (campaignProspectDAO.getOldStepIdForCampaign)
        .expects(newlyAssignedProspectIds, CampaignId(5L), TeamId(teamId))
        .returning(List())

      (prospectUpdateCategoryTemp.autoUpdateProspectCategory(
        _: TeamId, _: Seq[ProspectId], _: AccountId, _: ProspectCategory.ProspectCategory
      )(_: SRLogger)
        )
        .expects(
          TeamId(id = teamId),
          newlyAssignedProspectIds,
          AccountId(id = doerAccountId),
          ProspectCategory.OPEN,
          Logger,
        )
        .returning(Success(newlyAssignedProspectIds.length))

      val result = campaignProspectService.assignV3(
        orgId = doerOrgId,
        permittedAccountIds = permittedAccountIds,
        doerAccountId = doerAccountId,
        doerAccountName = doerAccountName,
        accountId = accountId,
        teamId = teamId,
        campaignId = campaignId,
        campaignName = campaignName,
        campaignSettings = campaignSettings,
        prospectIds = prospectIds,
        ignoreProspectsInOtherCampaigns = IgnoreProspectsInOtherCampaigns.IgnoreProspectsAddedInOtherCampaigns,
        accountOwnedProspects = ownedProspectsForAssigningList,
        Logger: SRLogger
      )

      assert(result == Success(
        CPAssignResult(
          prospectIdsIgnoredBecauseAlreadyAssignedToThisCampaign = List(7),

          prospectIdsIgnoredBecauseInOtherCampaigns = List(6),

          newlyAssignedProspectIds = List(8, 9, 10))
      ))


    }


  }

  describe("getProspectIdsToIgnoreWhileAssigning tests"){

    it("should return empty list if ownedProspectsNotAlreadyAssignedToGivenCampaign is empty"){

      val res = campaignProspectService.getProspectIdsToIgnoreWhileAssigning(
        ignoreProspectsInOtherCampaigns = IgnoreProspectsInOtherCampaigns.DoNotIgnore,
        ownedProspectsNotAlreadyAssignedToGivenCampaign = List(),
        campaignId = 5,
        teamId = TeamId(teamId)
      )

      assert(res == Success(List()))

    }

    it("should return empty list for DoNotIgnore"){
      val res = campaignProspectService.getProspectIdsToIgnoreWhileAssigning(
        ignoreProspectsInOtherCampaigns = IgnoreProspectsInOtherCampaigns.DoNotIgnore,
        ownedProspectsNotAlreadyAssignedToGivenCampaign = ownedProspectsForAssigningList.filterNot(a => a.prospect_id == 7),
        campaignId = 5,
        teamId = TeamId(teamId)
      )

      assert(res == Success(List()))
    }

    it("should return for IgnoreProspectsActiveInOtherCampaigns") {

      (campaignProspectDAO.getProspectsActiveInOtherCampaigns)
      .expects(prospectIds.filterNot(a => a == 7), 5, TeamId(teamId))
        .returning(List(6))

      val res = campaignProspectService.getProspectIdsToIgnoreWhileAssigning(
        ignoreProspectsInOtherCampaigns = IgnoreProspectsInOtherCampaigns.IgnoreProspectsActiveInOtherCampaigns,
        ownedProspectsNotAlreadyAssignedToGivenCampaign = ownedProspectsForAssigningList.filterNot(a => a.prospect_id == 7),
        campaignId = 5,
        teamId = TeamId(teamId)
      )

      assert(res == Success(List(6)))
    }

    it("should return for IgnoreProspectsAddedInOtherCampaigns") {

      (campaignProspectDAO.getProspectsAddedInOtherCampaigns)
        .expects(prospectIds.filterNot(a => a == 7), 5, TeamId(teamId))
        .returning(List(6))

      val res = campaignProspectService.getProspectIdsToIgnoreWhileAssigning(
        ignoreProspectsInOtherCampaigns = IgnoreProspectsInOtherCampaigns.IgnoreProspectsAddedInOtherCampaigns,
        ownedProspectsNotAlreadyAssignedToGivenCampaign = ownedProspectsForAssigningList.filterNot(a => a.prospect_id == 7),
        campaignId = 5,
        teamId = TeamId(teamId)
      )

      assert(res == Success(List(6)))
    }


  }

  describe("getProspectIdDataForAssigning tests"){

    val prospectsToBeAssignedToNewCampaign = ownedProspectsForAssigningList.filterNot(a => a.prospect_id == 6 || a.prospect_id == 7)

    val result = CampaignAssignProspectIdData(
      prospectIdsIgnoredBecauseInOtherCampaigns = List(6),
      prospectsToBeAssignedToNewCampaign = prospectsToBeAssignedToNewCampaign,
      prospectIdsAlreadyAssignedToThisCampaign = List(7))


    it("should return correct response for IgnoreProspectsAddedInOtherCampaigns") {

      (campaignProspectDAO.getprospectIdsAlreadyAssignedToACampaign)
        .expects(prospectIds, campaignId, TeamId(teamId))
        .returning(List(7))

      (campaignProspectDAO.getProspectsAddedInOtherCampaigns)
        .expects(prospectIds.filterNot(a => a == 7), 5, TeamId(teamId))
        .returning(List(6))

      (campaignProspectDAO.getOldStepIdForCampaign)
        .expects(prospectsToBeAssignedToNewCampaign.map(p => ProspectId(p.prospect_id)), CampaignId(5L), TeamId(teamId))
        .returning(List())


      val res = campaignProspectService.getProspectIdDataForAssigning(
        orgId = doerOrgId,
        permittedAccountIds = permittedAccountIds,
        doerAccountId = doerAccountId,
        doerAccountName = doerAccountName,
        accountId = accountId,
        teamId = teamId,
        campaignId = campaignId,
        campaignName = campaignName,
        prospectIds = prospectIds,
        ignoreProspectsInOtherCampaigns = IgnoreProspectsInOtherCampaigns.IgnoreProspectsAddedInOtherCampaigns,
        accountOwnedProspects = ownedProspectsForAssigningList
      )

      res match {

        case Failure(_) => assert(false)

        case Success(r) => assert(r == result)
      }
    }

    it("should return correct response for IgnoreProspectsActiveInOtherCampaigns") {

      (campaignProspectDAO.getprospectIdsAlreadyAssignedToACampaign)
        .expects(prospectIds, campaignId, TeamId(teamId))
        .returning(List(7))

      (campaignProspectDAO.getProspectsActiveInOtherCampaigns)
        .expects(prospectIds.filterNot(a => a == 7), 5, TeamId(teamId))
        .returning(List(6))

      (campaignProspectDAO.getOldStepIdForCampaign)
        .expects(prospectsToBeAssignedToNewCampaign.map(p => ProspectId(p.prospect_id)), CampaignId(5L), TeamId(teamId))
        .returning(List())


      val res = campaignProspectService.getProspectIdDataForAssigning(
        orgId = doerOrgId,
        permittedAccountIds = permittedAccountIds,
        doerAccountId = doerAccountId,
        doerAccountName = doerAccountName,
        accountId = accountId,
        teamId = teamId,
        campaignId = campaignId,
        campaignName = campaignName,
        prospectIds = prospectIds,
        ignoreProspectsInOtherCampaigns = IgnoreProspectsInOtherCampaigns.IgnoreProspectsActiveInOtherCampaigns,
        accountOwnedProspects = ownedProspectsForAssigningList
      )

      res match {

        case Failure(_) => assert(false)

        case Success(r) => assert(r == result)
      }
    }

    it("should return correct response for DoNotIgnore") {

      val prospectsToBeAssignedToNewCampaign = ownedProspectsForAssigningList.filterNot(p => p.prospect_id == 7)

      val result2 = CampaignAssignProspectIdData(
        prospectIdsIgnoredBecauseInOtherCampaigns = List(),
        prospectsToBeAssignedToNewCampaign = prospectsToBeAssignedToNewCampaign,
        prospectIdsAlreadyAssignedToThisCampaign = List(7))



      (campaignProspectDAO.getprospectIdsAlreadyAssignedToACampaign)
        .expects(prospectIds, campaignId, TeamId(teamId))
        .returning(List(7))

      (campaignProspectDAO.getOldStepIdForCampaign)
        .expects(prospectsToBeAssignedToNewCampaign.map(p => ProspectId(p.prospect_id)), CampaignId(5L), TeamId(teamId))
        .returning(List())


      val res = campaignProspectService.getProspectIdDataForAssigning(
        orgId = doerOrgId,
        permittedAccountIds = permittedAccountIds,
        doerAccountId = doerAccountId,
        doerAccountName = doerAccountName,
        accountId = accountId,
        teamId = teamId,
        campaignId = campaignId,
        campaignName = campaignName,
        prospectIds = prospectIds,
        ignoreProspectsInOtherCampaigns = IgnoreProspectsInOtherCampaigns.DoNotIgnore,
        accountOwnedProspects = ownedProspectsForAssigningList
      )

      res match {

        case Failure(_) => assert(false)

        case Success(r) => assert(r == result2)
      }
    }

    it("should return correct response if getOldStepIdForCampaign returns list") {

      var prospectsToBeAssignedToNewCampaign = ownedProspectsForAssigningList.filterNot(p => p.prospect_id == 7)

      val result2 = CampaignAssignProspectIdData(
        prospectIdsIgnoredBecauseInOtherCampaigns = List(),
        prospectsToBeAssignedToNewCampaign = prospectsToBeAssignedToNewCampaign.map(p =>
          if(p.prospect_id == 6 ){
            p.copy(synced_previously_sent_step_for_deleted_prospect_step_id = Some(4))
          } else{
            p
          }),
        prospectIdsAlreadyAssignedToThisCampaign = List(7))


      (campaignProspectDAO.getprospectIdsAlreadyAssignedToACampaign)
        .expects(prospectIds, campaignId, TeamId(teamId))
        .returning(List(7))

      (campaignProspectDAO.getOldStepIdForCampaign)
        .expects(prospectsToBeAssignedToNewCampaign.map(p => ProspectId(p.prospect_id)), CampaignId(5L), TeamId(teamId))
        .returning(List((StepId(4), ProspectId(6L), DateTime.now())))


      val res = campaignProspectService.getProspectIdDataForAssigning(
        orgId = doerOrgId,
        permittedAccountIds = permittedAccountIds,
        doerAccountId = doerAccountId,
        doerAccountName = doerAccountName,
        accountId = accountId,
        teamId = teamId,
        campaignId = campaignId,
        campaignName = campaignName,
        prospectIds = prospectIds,
        ignoreProspectsInOtherCampaigns = IgnoreProspectsInOtherCampaigns.DoNotIgnore,
        accountOwnedProspects = ownedProspectsForAssigningList
      )

      res match {

        case Failure(_) => assert(false)

        case Success(r) =>
          assert(r == result2)
      }
    }

    it("should return correct response if all are already assigned") {


      val result2 = CampaignAssignProspectIdData(
        prospectIdsIgnoredBecauseInOtherCampaigns = List(),
        prospectsToBeAssignedToNewCampaign = List(),
        prospectIdsAlreadyAssignedToThisCampaign = ownedProspectsForAssigningList.map(p => p.prospect_id))


      (campaignProspectDAO.getprospectIdsAlreadyAssignedToACampaign)
        .expects(prospectIds, campaignId, TeamId(teamId))
        .returning(ownedProspectsForAssigningList.map(p => p.prospect_id))

      (campaignProspectDAO.getOldStepIdForCampaign)
        .expects(List(), CampaignId(5L), TeamId(teamId))
        .returning(List())


      val res = campaignProspectService.getProspectIdDataForAssigning(
        orgId = doerOrgId,
        permittedAccountIds = permittedAccountIds,
        doerAccountId = doerAccountId,
        doerAccountName = doerAccountName,
        accountId = accountId,
        teamId = teamId,
        campaignId = campaignId,
        campaignName = campaignName,
        prospectIds = prospectIds,
        ignoreProspectsInOtherCampaigns = IgnoreProspectsInOtherCampaigns.DoNotIgnore,
        accountOwnedProspects = ownedProspectsForAssigningList
      )

      res match {

        case Failure(_) => assert(false)

        case Success(r) =>
          assert(r == result2)
      }
    }

    it("should return correct response if all are prospectIdsIgnoredBecauseInOtherCampaigns - IgnoreProspectsAddedInOtherCampaigns") {


      val result2 = CampaignAssignProspectIdData(
        prospectIdsIgnoredBecauseInOtherCampaigns = ownedProspectsForAssigningList.map(p => p.prospect_id),
        prospectsToBeAssignedToNewCampaign = List(),
        prospectIdsAlreadyAssignedToThisCampaign = List())


      (campaignProspectDAO.getprospectIdsAlreadyAssignedToACampaign)
        .expects(prospectIds, campaignId, TeamId(teamId))
        .returning(List())

      (campaignProspectDAO.getProspectsAddedInOtherCampaigns)
        .expects(prospectIds, 5, TeamId(teamId))
        .returning(prospectIds)

      (campaignProspectDAO.getOldStepIdForCampaign)
        .expects(List(), CampaignId(5L), TeamId(teamId))
        .returning(List())


      val res = campaignProspectService.getProspectIdDataForAssigning(
        orgId = doerOrgId,
        permittedAccountIds = permittedAccountIds,
        doerAccountId = doerAccountId,
        doerAccountName = doerAccountName,
        accountId = accountId,
        teamId = teamId,
        campaignId = campaignId,
        campaignName = campaignName,
        prospectIds = prospectIds,
        ignoreProspectsInOtherCampaigns = IgnoreProspectsInOtherCampaigns.IgnoreProspectsAddedInOtherCampaigns,
        accountOwnedProspects = ownedProspectsForAssigningList
      )

      res match {

        case Failure(_) => assert(false)

        case Success(r) =>
          assert(r == result2)
      }
    }


    it("should return correct response if all are prospectIdsIgnoredBecauseInOtherCampaigns - IgnoreProspectsActiveInOtherCampaigns") {


      val result2 = CampaignAssignProspectIdData(
        prospectIdsIgnoredBecauseInOtherCampaigns = ownedProspectsForAssigningList.map(p => p.prospect_id),
        prospectsToBeAssignedToNewCampaign = List(),
        prospectIdsAlreadyAssignedToThisCampaign = List())


      (campaignProspectDAO.getprospectIdsAlreadyAssignedToACampaign)
        .expects(prospectIds, campaignId, TeamId(teamId))
        .returning(List())

      (campaignProspectDAO.getProspectsActiveInOtherCampaigns)
        .expects(prospectIds, 5, TeamId(teamId))
        .returning(prospectIds)

      (campaignProspectDAO.getOldStepIdForCampaign)
        .expects(List(), CampaignId(5L), TeamId(teamId))
        .returning(List())


      val res = campaignProspectService.getProspectIdDataForAssigning(
        orgId = doerOrgId,
        permittedAccountIds = permittedAccountIds,
        doerAccountId = doerAccountId,
        doerAccountName = doerAccountName,
        accountId = accountId,
        teamId = teamId,
        campaignId = campaignId,
        campaignName = campaignName,
        prospectIds = prospectIds,
        ignoreProspectsInOtherCampaigns = IgnoreProspectsInOtherCampaigns.IgnoreProspectsActiveInOtherCampaigns,
        accountOwnedProspects = ownedProspectsForAssigningList
      )

      res match {

        case Failure(_) => assert(false)

        case Success(r) =>
          assert(r == result2)
      }
    }

  }

  describe("postAssignActions Tests"){


    it("should fail when addFeatureUsageEvent fails") {

      (srUserFeatureUsageEventService.addFeatureUsageEvent)
        .expects(10, *)
        .returning(Failure(new Exception("some exception")))


      val res = campaignProspectService.postAssignActions(
        allNewlyAssignedProspectIds = prospectIds,
        orgId = doerOrgId,
        prospectsToBeAssignedToNewCampaign = ownedProspectsForAssigningList,
        campaignId = campaignId,
        teamId = teamId,
        campaignSettings = campaignSettings,
        Logger = Logger
      )

      res match {

        case Failure(_) => assert(true)

        case Success(_) => assert(false)
      }
    }

    it("should fail when setNextToBeScheduledAt fails") {

      (srUserFeatureUsageEventService.addFeatureUsageEvent)
        .expects(10, *)
        .returning(Success(1))

      (campaignCacheService.resetCampaignStats(_: Long, _: Long)(using _: SRLogger))
        .expects(5, 4, *)
        .returning(())

      (campaignService.setNextToBeScheduledAt)
        .expects(

          CampaignSetNextToBeScheduledAtData.AddedProspectToCampaignFlow(
            campaignId = CampaignId(campaignId),
            campaignSettings = campaignSettings,
            teamId = TeamId(teamId)
          )
          , Logger, TeamId(teamId))
        .returning(Failure(new Exception("some exception")))


      val res = campaignProspectService.postAssignActions(
        allNewlyAssignedProspectIds = prospectIds,
        orgId = doerOrgId,
        prospectsToBeAssignedToNewCampaign = ownedProspectsForAssigningList,
        campaignId = campaignId,
        teamId = teamId,
        campaignSettings = campaignSettings,
        Logger = Logger
      )

      res match {

        case Failure(_) => assert(true)

        case Success(_) => assert(false)
      }
    }

    it("should pass"){

      (campaignService.setNextToBeScheduledAt)
        .expects(

          CampaignSetNextToBeScheduledAtData.AddedProspectToCampaignFlow(
            campaignId = CampaignId(campaignId),
            campaignSettings = campaignSettings,
            teamId = TeamId(teamId)
          ), Logger, TeamId(teamId))
        .returning(Success(1))

      (srUserFeatureUsageEventService.addFeatureUsageEvent)
        .expects(10, *)
        .returning(Success(1))

      (campaignCacheService.resetCampaignStats(_: Long, _: Long)(using _: SRLogger))
        .expects(5, 4, *)
        .returning(())

      val res = campaignProspectService.postAssignActions(
        allNewlyAssignedProspectIds = prospectIds,
        orgId = doerOrgId,
        prospectsToBeAssignedToNewCampaign = ownedProspectsForAssigningList,
        campaignId = campaignId,
        teamId = teamId,
        campaignSettings = campaignSettings,
        Logger = Logger
      )

      res match {

        case Failure(_) => assert(false)

        case Success(_) => assert(true)
      }
    }
  }

  describe("checkListOfProspectAndFilterOutProspectsThatDontHaveDomain") {
    val internal = ProspectFixtures.prospectObjectInternal

    val prospectObject = ProspectObject(
      id = 2L,
      owner_id = 4L,
      team_id = 6L,
      first_name = Some("John"),
      last_name = Some("Doe"),
      email = Some("<EMAIL>"),
      custom_fields = Json.obj(
        "field_1" -> "value_1",
        "field_2" -> "value_2"
      ),
      list = None,
      job_title = Some("SDE"),
      company = Some("SmartReach"),
      linkedin_url = None,
      phone = Some("*********"),
      phone_2 = None,
      phone_3 = None,
      city = Some("Pune"),
      state = Some("Maharashtra"),
      country = Some("India"),
      timezone = Some("IN"),
      prospect_category = "Converted",
      last_contacted_at = None,
      last_contacted_at_phone = None,
      created_at = DateTime.now().minusDays(50),
      internal = internal,
      latest_reply_sentiment_uuid = None,
      current_step_type = None,
      latest_task_done_at = None,
      prospect_uuid = Some(ProspectUuid("prs_aa_abcdefghi")),
      owner_uuid = AccountUuid("acc_aa_abcdegfhi"),
      updated_at = DateTime.now()
    )
    val prospectForScheduling: ProspectDataForChannelScheduling.EmailChannelProspectForScheduling = ProspectDataForChannelScheduling.EmailChannelProspectForScheduling(
      prospect = prospectObject,
      current_step_id = None,
      current_step_status_data = None,
      email_checked = true,
      email_sent_for_validation = false,
      email_sent_for_validation_at = None
    )
    val domainPublicDNSData = DomainPublicDNSData(
      domain  = "",
      dns_service = DNSService.GoogleDNS,
      raw_response = Json.obj(),
      mx_inbox_provider = SrMxCheckESPType.Google,
      last_updated_at = DateTime.now(),
      first_checked_at = DateTime.now(),
      is_valid = true
    )
    it("should filter out domain without DNS data") {

      (domainPublicDNSDAO.getBatch (_:Set[String]))
        .expects(Set("domain1.com", "domain2.com", "domain3.com", "domain4.com", "domain5.com", "domain6.com", "domain7.com", "domain8.com", "domain9.com", "domain10.com"))
        .returning(Success(List(
          domainPublicDNSData.copy(domain = "domain2.com"),
          domainPublicDNSData.copy(domain = "domain4.com"),
          domainPublicDNSData.copy(domain = "domain6.com"),
          domainPublicDNSData.copy(domain = "domain8.com"),
          domainPublicDNSData.copy(domain = "domain10.com"),
        )))
      (mqDomainServiceProviderDNSService.publish)
        .expects("domain1.com")
        .returning(Success({}))
      (mqDomainServiceProviderDNSService.publish)
        .expects("domain3.com")
        .returning(Success({}))
      (mqDomainServiceProviderDNSService.publish)
        .expects("domain5.com")
        .returning(Success({}))
      (mqDomainServiceProviderDNSService.publish)
        .expects("domain7.com")
        .returning(Success({}))
      (mqDomainServiceProviderDNSService.publish)
        .expects("domain9.com")
        .returning(Success({}))
      val result = campaignProspectService.checkListOfProspectAndFilterOutProspectsThatDontHaveDomain(
        prospectsForScheduling = List(
          prospectForScheduling.copy(prospect = prospectObject.copy(email = Some("<EMAIL>"))),
          prospectForScheduling.copy(prospect = prospectObject.copy(email = Some("<EMAIL>"))),
          prospectForScheduling.copy(prospect = prospectObject.copy(email = Some("<EMAIL>"))),
          prospectForScheduling.copy(prospect = prospectObject.copy(email = Some("<EMAIL>"))),
          prospectForScheduling.copy(prospect = prospectObject.copy(email = Some("<EMAIL>"))),
          prospectForScheduling.copy(prospect = prospectObject.copy(email = Some("<EMAIL>"))),
          prospectForScheduling.copy(prospect = prospectObject.copy(email = Some("<EMAIL>"))),
          prospectForScheduling.copy(prospect = prospectObject.copy(email = Some("<EMAIL>"))),
          prospectForScheduling.copy(prospect = prospectObject.copy(email = Some("<EMAIL>"))),
          prospectForScheduling.copy(prospect = prospectObject.copy(email = Some("<EMAIL>")))
        )
      )
      println(s"result ==== $result")
      assert(result.isSuccess)
      assert(result.get.nonEmpty)
      assert(result.get.length == 5)
      assert(result.get.contains(prospectForScheduling.copy(prospect = prospectObject.copy(email = Some("<EMAIL>")))))
      assert(result.get.contains(prospectForScheduling.copy(prospect = prospectObject.copy(email = Some("<EMAIL>")))))
      assert(result.get.contains(prospectForScheduling.copy(prospect = prospectObject.copy(email = Some("<EMAIL>")))))
      assert(result.get.contains(prospectForScheduling.copy(prospect = prospectObject.copy(email = Some("<EMAIL>")))))
      assert(result.get.contains(prospectForScheduling.copy(prospect = prospectObject.copy(email = Some("<EMAIL>")))))
    }

  }
}
