package app.api.campaign.services

import org.apache.pekko.actor.ActorSystem
import org.apache.pekko.stream.Materializer
import api.accounts.{TeamAccountRole, TeamId, TeamMember}
import api.campaigns.models.{CampaignEmailSettingsId, CampaignStepData, CampaignStepId, CampaignStepType, CampaignType, CreateCampaignFirstStepError, CreateCampaignStepVariantError, LinkedinSettingSenderDetails, SmsSettingSenderDetails, UpdateVariantError, ValidateCampaignTemplateError, ValidateStepData, WhatsappSettingSenderDetails}
import api.campaigns.{Campaign, CampaignEmailSettings, CampaignEmailSettingsUuid, CampaignSettings, CampaignStep, CampaignStepDAO, CampaignStepDAOService, CampaignStepVariant, CampaignStepVariantCreateOrUpdate, CampaignStepVariantDAO, CampaignStepVariantForScheduling, CampaignStepWithChildren, ChannelSettingUuid}
import api.campaigns.services.{CampaignDAOService, CampaignId, CampaignService, CampaignStepService, CampaignTemplateService, ValidateCampaignStepVariantError}
import api.campaigns.models.CampaignStepData.AutoEmailStep
import api.tasks.models.TaskPriority
import api.templates.{TemplateDuplicateCheck, TemplateUtils}
import app.test_fixtures.campaign_settings.{CallSettingSenderDetailsFixtures, LinkedinSettingSenderDetailsFixtures, SmsSettingSenderDetailsFixtures, WhatsappSettingSenderDetailsFixtures}
import io.smartreach.esp.api.emails.EmailSettingId
import org.joda.time.DateTime
import org.scalamock.scalatest.AsyncMockFactory
import org.scalatest.funspec.AsyncFunSpec
import play.api.libs.ws.WSClient
import play.api.libs.ws.ahc.AhcWSClient
import sr_scheduler.CampaignStatus
import sr_scheduler.models.CampaignEmailPriority
import utils.SRLogger
import utils.sr_product_usage_data.services.SrUserFeatureUsageEventService
import utils.testapp.TestAppExecutionContext

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}

class CampaignStepServiceSpec extends AsyncFunSpec with AsyncMockFactory  {

  val campaignTemplateService: CampaignTemplateService = mock[CampaignTemplateService]
  val campaignStepDAO: CampaignStepDAO = mock[CampaignStepDAO]
  val campaignStepVariantDAO: CampaignStepVariantDAO = mock[CampaignStepVariantDAO]
  val templateUtils: TemplateUtils = mock[TemplateUtils]
  val srUserFeatureUsageEventService: SrUserFeatureUsageEventService = mock[SrUserFeatureUsageEventService]
  val campaignDAOService: CampaignDAOService = mock[CampaignDAOService]
  val campaignStepDAOService: CampaignStepDAOService = mock[CampaignStepDAOService]
  val campaignService: CampaignService = mock[CampaignService]

  val campaignStepService = new CampaignStepService(
    campaignTemplateService = campaignTemplateService,
    campaignStepDAO = campaignStepDAO,
    campaignStepVariantDAO = campaignStepVariantDAO,
    templateUtils = templateUtils,
    srUserFeatureUsageEventService = srUserFeatureUsageEventService,
    campaignDAOService = campaignDAOService,
    campaignStepDAOService = campaignStepDAOService,
    campaignService = campaignService
  )

  val orgId = 1L
  val teamId: Long = 37L
  val first_name = "Adminfirst"
  val last_name = "Adminlast"
  val company = "CompanyName"

  val campaignStepDataEmailStep = CampaignStepData.AutoEmailStep(
    subject = "Subject",
    body = "Body of Email"
  )
  val data = CampaignStepVariantCreateOrUpdate(
    parent_id = 1L,
    step_data = campaignStepDataEmailStep,
    step_delay = 1,
    notes = Some("Test Notes"),
    priority = Some(TaskPriority.Normal),
    template_id = Some(1L),
    template_is_from_library = None
  )
  val teamMember: TeamMember = TeamMember(
    team_id = teamId,
    team_name = "team_name",
    user_id = 1L,
    ta_id = 1L, // dont send ta_id to frontend / api response, only for internal purpose, its dynamically assigned in AuthUtils
    first_name = Some(first_name),
    last_name = Some(last_name),
    email = "<EMAIL>",
    team_role = TeamAccountRole.ADMIN,
    api_key = Some("apiKey"),
    zapier_key = Some("zapier_key")
  )

  val defaultTestStepId: Option[Long] = Some(0L) // todo: check if we are testing all scenarios
  val defaultTestHeadStepId: Option[Long] = None // todo: check if we are testing all scenarios

  given Logger: SRLogger = new SRLogger("Testing")

  implicit lazy val system: ActorSystem = TestAppExecutionContext.actorSystem
  implicit lazy val ec: ExecutionContext = system.dispatcher
  implicit lazy val wSClient: AhcWSClient = TestAppExecutionContext.wsClient

  val error = new Throwable("Failed while Validating the Campaign")
  val errMsg = "Just an error msg"

  val campaignSettings = CampaignSettings(
    campaign_email_settings = List(
      CampaignEmailSettings(
        campaign_id = CampaignId(1L),
        sender_email_setting_id = EmailSettingId(1L),
        receiver_email_setting_id = EmailSettingId(1L),
        team_id = TeamId(teamId),
        uuid = CampaignEmailSettingsUuid("temp_setting_id"),
        id = CampaignEmailSettingsId(123L),
        sender_email = "<EMAIL>",
        receiver_email = "<EMAIL>",
        max_emails_per_day_from_email_account = 100,
        signature = Some("emailsignature"),
        error = None,
        from_name = None
      )
    ),
    campaign_linkedin_settings = List(
      LinkedinSettingSenderDetailsFixtures.linkedin_setting_sender_details
    ),
    campaign_call_settings = List(
      CallSettingSenderDetailsFixtures.call_setting_sender_details
    ),
    campaign_whatsapp_settings = List(
      WhatsappSettingSenderDetailsFixtures.whatsapp_setting_sender_details
    ),
    campaign_sms_settings = List(
      SmsSettingSenderDetailsFixtures.sms_setting_sender_details
    ),
    timezone = "US",
    daily_from_time = 1, // time since beginning of day in seconds
    daily_till_time = 1, // time since beginning of day in seconds
    sending_holiday_calendar_id = Some(1),

    // Sunday is the first day
    days_preference = List(true),


    mark_completed_after_days = 1,
    max_emails_per_day = 1,
    open_tracking_enabled = true,
    click_tracking_enabled = true,
    enable_email_validation = true,
    ab_testing_enabled = true,

    ai_sequence_status = None,

    // warm up
    warmup_started_at = Some(DateTime.now()),
    warmup_length_in_days = Some(1),
    warmup_starting_email_count = Some(1),
    show_soft_start_setting = false,

    // schedule start
    schedule_start_at = Some(DateTime.now()),
    schedule_start_at_tz = Some("A"),
    send_plain_text_email = Some(false),
    campaign_type = CampaignType.MultiChannel,


    email_priority = CampaignEmailPriority.EQUAL,
    append_followups = true,
    opt_out_msg = "A",
    opt_out_is_text = true,
    add_prospect_to_dnc_on_opt_out = true,
    triggers = Seq(),
    sending_mode = None,

    selected_calendar_data = None

  )
  val campaign_uuid = s"cmp_${teamId}_cfknacskndjcn"
  val campaign = Campaign(
    id = 1L,
    uuid = Some(campaign_uuid),
    account_id = 1L,
    team_id = 1L,
    shared_with_team = true,
    name = "A",
    status = CampaignStatus.SCHEDULED,

    head_step_id = Some(1L),

    settings = campaignSettings,

    last_scheduled_at = Some(DateTime.now()),

    created_at = DateTime.now()
  )

  val campaignStepWithChildren = CampaignStepWithChildren(
    id = 1L,
    label = Some("A"),
    campaign_id = 1,
    delay = 1,
    step_type = CampaignStepType.AutoEmailStep,
    created_at = DateTime.now(),
    children = List(1),
    variants = Seq()
  )

  val campaignStep = CampaignStep(
    id = 1L,
    label = Some("testLabel"),
    campaign_id = 1,
    delay = 1,
    step_type = CampaignStepType.AutoEmailStep,
    created_at = DateTime.now()
  )

  val emailStep = AutoEmailStep(
    subject = campaignStepDataEmailStep.subject,
    body = campaignStepDataEmailStep.body
  )

  val campaignStepVariant = CampaignStepVariant(
    id = 1L,
    step_id = 1L,
    campaign_id = 1,
    template_id = Some(1L),
    step_data = emailStep
    ,
    label = None,
    active =  true
  )

  val templateDuplicateCheck = TemplateDuplicateCheck(
    templateCheckError = Some(error),
    templateNotFoundError = Some("false"),
    templateIdToBeSaved = Some(1L)
  )

  private val stepId = CampaignStepId(id = 13L)

  private val campaignId = CampaignId(id = 17L)

  private val campaignStepVariantForScheduling = CampaignStepVariantForScheduling(
    id = 7L,
    step_id = stepId.id,
    campaign_id = campaignId.id.toInt,
    template_id = None,
    step_data = CampaignStepData.AutoEmailStep(
      subject = "some subject",
      body = "some body",
    ),
    step_label = None,
    step_delay = 0,
    notes = None,
    priority = None,
    active = true,
    scheduled_count = 3,
  )

  private val campaignAutoEmailStep = campaignStepWithChildren.copy(
    id = stepId.id,
    campaign_id = campaignId.id.toInt,
    step_type = CampaignStepType.AutoEmailStep,
    variants = Seq(campaignStepVariantForScheduling)
  )

  //val Logger = new SRLogger("logger")
  describe("CampaignStepService.createVariant") {

    it("Should fail when Auto Email Body is empty") {

      val variantData = data.copy(
        step_data = campaignStepDataEmailStep.copy(
          body = ""
        )
      )

      val res = campaignStepService.createVariant(
        orgId = orgId,
        data = variantData,
        teamId = teamMember.team_id,
        userId = teamMember.user_id,
        taId = teamMember.ta_id,
        stepId = 0L,
        campaignId = 1L,
        campaignHeadStepId = None
      )
      res.map { result =>
        assert(result == Left(CreateCampaignStepVariantError.CampaignStepVariantValidationError(
          ValidateCampaignStepVariantError.EmailBodyCantBeEmpty
        )))
      }
    }

    it("Should fail when Auto Email Subject is empty") {

      val variantData = data.copy(
        step_data = campaignStepDataEmailStep.copy(
          subject = ""
        )
      )

      val res = campaignStepService.createVariant(
        orgId = orgId,
        data = variantData,
        teamId = teamMember.team_id,
        userId = teamMember.user_id,
        taId = teamMember.ta_id,
        stepId = 0,
        campaignId = 1,
        campaignHeadStepId = None
      )
      res.map { result =>
        assert(result == Left(CreateCampaignStepVariantError.CampaignStepVariantValidationError(
          ValidateCampaignStepVariantError.EmailSubjectCantBeEmpty
        )))
      }
    }

    it("Should fail when Manual Email Body is empty") {

      val variantData = data.copy(
        step_data = CampaignStepData.ManualEmailStep(
          subject = "Subject",
          body = ""
        )
      )

      val res = campaignStepService.createVariant(
        orgId = orgId,
        data = variantData,
        teamId = teamMember.team_id,
        userId = teamMember.user_id,
        taId = teamMember.ta_id,
        stepId = 0,
        campaignId = 1,
        campaignHeadStepId = None
      )
      res.map { result =>
        assert(result == Left(CreateCampaignStepVariantError.CampaignStepVariantValidationError(
          ValidateCampaignStepVariantError.EmailBodyCantBeEmpty
        )))
      }
    }

    it("Should fail when Manual Email Subject is empty") {

      val variantData = data.copy(
        step_data = CampaignStepData.ManualEmailStep(
          subject = "",
          body = "Body"
        )
      )

      val res = campaignStepService.createVariant(
        orgId = orgId,
        data = variantData,
        teamId = teamMember.team_id,
        userId = teamMember.user_id,
        taId = teamMember.ta_id,
        stepId = 0,
        campaignId = 1,
        campaignHeadStepId = None
      )
      res.map { result =>
        assert(result == Left(CreateCampaignStepVariantError.CampaignStepVariantValidationError(
          ValidateCampaignStepVariantError.EmailSubjectCantBeEmpty
        )))
      }
    }

    it("Should fail when Whatsapp Message Body is empty") {

      val variantData = data.copy(
        step_data = CampaignStepData.WhatsappMessageData(
          body = ""
        )
      )

      val res = campaignStepService.createVariant(
        orgId = orgId,
        data = variantData,
        teamId = teamMember.team_id,
        userId = teamMember.user_id,
        taId = teamMember.ta_id,
        stepId = 0,
        campaignId = 1,
        campaignHeadStepId = None
      )
      res.map { result =>
        assert(result == Left(CreateCampaignStepVariantError.CampaignStepVariantValidationError(
          ValidateCampaignStepVariantError.WhatsappMessageBodyCantBeEmpty
        )))
      }
    }

    it("Should fail when Phone Message Body is empty") {

      val variantData = data.copy(
        step_data = CampaignStepData.SmsMessageData(
          body = ""
        )
      )

      val res = campaignStepService.createVariant(
        orgId = orgId,
        data = variantData,
        teamId = teamMember.team_id,
        userId = teamMember.user_id,
        taId = teamMember.ta_id,
        stepId = 0,
        campaignId = 1,
        campaignHeadStepId = None
      )
      res.map { result =>
        assert(result == Left(CreateCampaignStepVariantError.CampaignStepVariantValidationError(
          ValidateCampaignStepVariantError.PhoneMessageBodyCantBeEmpty
        )))
      }
    }

    it("Should fail when General Task Notes is empty") {

      val variantData = data.copy(
        step_data = CampaignStepData.GeneralTaskData(),
        notes = Some("")
      )

      val res = campaignStepService.createVariant(
        orgId = orgId,
        data = variantData,
        teamId = teamMember.team_id,
        userId = teamMember.user_id,
        taId = teamMember.ta_id,
        stepId = 0,
        campaignId = 1,
        campaignHeadStepId = None
      )
      res.map { result =>
        assert(result == Left(CreateCampaignStepVariantError.CampaignStepVariantValidationError(
          ValidateCampaignStepVariantError.GeneralTaskNotesCantBeEmpty
        )))
      }
    }

    it("Should fail when Linkedin Inmail Body is empty") {

      val variantData = data.copy(
        step_data = CampaignStepData.LinkedinInmailData(
          subject = None,
          body = ""
        )
      )

      val res = campaignStepService.createVariant(
        orgId = orgId,
        data = variantData,
        teamId = teamMember.team_id,
        userId = teamMember.user_id,
        taId = teamMember.ta_id,
        stepId = 0,
        campaignId = 1,
        campaignHeadStepId = None
      )
      res.map { result =>{
        assert(result == Left(CreateCampaignStepVariantError.ErrorWhileCreateCampaignFirst(
          err = CreateCampaignFirstStepError.TemplateIsFromLibraryFieldMissing
        )))
      }
      }
    }

    it("Should fail when Linkedin Message Body is empty") {

      val variantData = data.copy(
        step_data = CampaignStepData.LinkedinMessageData(
          body = ""
        )
      )

      val res = campaignStepService.createVariant(
        orgId = orgId,
        data = variantData,
        teamId = teamMember.team_id,
        userId = teamMember.user_id,
        taId = teamMember.ta_id,
        stepId = 0,
        campaignId = 1,
        campaignHeadStepId = None
      )
      res.map { result =>
        assert(result == Left(CreateCampaignStepVariantError.ErrorWhileCreateCampaignFirst(
          err = CreateCampaignFirstStepError.TemplateIsFromLibraryFieldMissing
        )))
      }
    }

    it("should fail when template_is_from_library is empty") {

      val res = campaignStepService.createVariant(
        orgId = orgId,
        data = data,
        teamId = teamMember.team_id,
        userId = teamMember.user_id,
        taId = teamMember.ta_id,
        stepId = 0,
        campaignId = 1,
        campaignHeadStepId = None
      )
      res.map { result =>
        assert(result == Left(CreateCampaignStepVariantError.ErrorWhileCreateCampaignFirst(
          CreateCampaignFirstStepError.TemplateIsFromLibraryFieldMissing
        )))
      }
    }

    it("Should fail when body contains {{previous_subject}} in step_data") {

      val stepData = campaignStepDataEmailStep.copy(
        body = "{{previous_subject}}"
      )

      val variantData = data.copy(
        template_is_from_library = Some(true),
        step_data = stepData
      )

      val validateStepVariantData = ValidateStepData.ValidateStepVariantData(
        campaignStepData = stepData,
        stepId = defaultTestStepId,
        head_step_id = defaultTestHeadStepId,
        campaignId = CampaignId(id = campaign.id)
      )

      (campaignTemplateService.validateCampaignTemplate)
        .expects(
          teamMember.team_id,
          validateStepVariantData,
          data.notes
        )
        .returning(Left(ValidateCampaignTemplateError.BodyCantHavePreviousSubject(error)))

      val res = campaignStepService.createVariant(
        orgId = orgId,
        data = variantData,
        teamId = teamMember.team_id,
        userId = teamMember.user_id,
        taId = teamMember.ta_id,
        stepId = 0,
        campaignId = 1,
        campaignHeadStepId = None
      )

      res.map { result =>
        assert(result == Left(CreateCampaignStepVariantError.ErrorWhileCreateCampaignFirst(
          CreateCampaignFirstStepError.ValidateCampaignTemplateError(
            error, stepData
          )))
        )
      }

    }

    it("Should fail when subject contains {{signature}} in step_data") {

      val stepData = campaignStepDataEmailStep.copy(
        subject = "{{signature}}"
      )

      val variantData = data.copy(
        template_is_from_library = Some(true),
        step_data = stepData
      )

      val validateStepVariantData = ValidateStepData.ValidateStepVariantData(
        campaignStepData = stepData,
        stepId = defaultTestStepId,
        head_step_id = defaultTestHeadStepId,
        campaignId = CampaignId(id = campaign.id)
      )

      (campaignTemplateService.validateCampaignTemplate)
        .expects(
          teamMember.team_id,
          validateStepVariantData,
          data.notes
        )
        .returning(Left(ValidateCampaignTemplateError.SubjectCantHaveSignature(error)))

      val res = campaignStepService.createVariant(
        orgId = orgId,
        data = variantData,
        teamId = teamMember.team_id,
        userId = teamMember.user_id,
        taId = teamMember.ta_id,
        stepId = 0,
        campaignId = 1,
        campaignHeadStepId = None
      )

      res.map { result =>
        assert(result == Left(CreateCampaignStepVariantError.ErrorWhileCreateCampaignFirst(
          CreateCampaignFirstStepError.ValidateCampaignTemplateError(
            error, stepData
          )))
        )
      }

    }

    it("Should fail when first email contains {{previous_subject}} in step_data") {

      val stepData = campaignStepDataEmailStep.copy(
        subject = "{{previous_subject}}"
      )

      val variantData = data.copy(
        template_is_from_library = Some(true),
        step_data = stepData
      )

      val validateStepVariantData = ValidateStepData.ValidateStepVariantData(
        campaignStepData = stepData,
        stepId = defaultTestStepId,
        head_step_id = defaultTestStepId,
        campaignId = CampaignId(id = campaign.id)
      )

      (campaignTemplateService.validateCampaignTemplate)
        .expects(
          teamMember.team_id,
          validateStepVariantData,
          data.notes
        )
        .returning(Left(ValidateCampaignTemplateError.PreviousSubInFirstEmail(error)))

      val res = campaignStepService.createVariant(
        orgId = orgId,
        data = variantData,
        teamId = teamMember.team_id,
        userId = teamMember.user_id,
        taId = teamMember.ta_id,
        stepId = 0,
        campaignId = 1,
        campaignHeadStepId = defaultTestStepId
      )

      res.map { result =>
        assert(result == Left(CreateCampaignStepVariantError.ErrorWhileCreateCampaignFirst(
          CreateCampaignFirstStepError.ValidateCampaignTemplateError(
            error, stepData
          )))
        )
      }

    }

    it("should fail when campaign Template is validated") {

      val variantData = data.copy(
        template_is_from_library = Some(true)
      )

      val validateStepVariantData = ValidateStepData.ValidateStepVariantData(
        campaignStepData = campaignStepDataEmailStep,
        stepId = defaultTestStepId,
        head_step_id = defaultTestHeadStepId,
        campaignId = CampaignId(id = campaign.id)
      )

      (campaignTemplateService.validateCampaignTemplate)
        .expects(
          teamMember.team_id,
          validateStepVariantData,
          data.notes
        )
        .returning(Left(ValidateCampaignTemplateError.ErrorWhileValidatingBody(error)))

      val res = campaignStepService.createVariant(
        orgId = orgId,
        data = variantData,
        teamId = teamMember.team_id,
        userId = teamMember.user_id,
        taId = teamMember.ta_id,
        stepId = 0,
        campaignId = 1,
        campaignHeadStepId = None
      )
      res.map { result =>
        assert(result == Left(CreateCampaignStepVariantError.ErrorWhileCreateCampaignFirst(
          CreateCampaignFirstStepError.ValidateCampaignTemplateError(error, campaignStepDataEmailStep)
        )))
      }
    }

    it("Should fail when validate subject throws error") {

      val variantData = data.copy(
        template_is_from_library = Some(true),
      )

      val validateStepVariantData = ValidateStepData.ValidateStepVariantData(
        campaignStepData = campaignStepDataEmailStep,
        stepId = defaultTestStepId,
        head_step_id = defaultTestHeadStepId,
        campaignId = CampaignId(id = campaign.id)
      )

      (campaignTemplateService.validateCampaignTemplate)
        .expects(
          teamMember.team_id,
          validateStepVariantData,
          data.notes
        )
        .returning(Left(ValidateCampaignTemplateError.ErrorWhileValidatingSubject(error)))

      val res = campaignStepService.createVariant(
        orgId = orgId,
        data = variantData,
        teamId = teamMember.team_id,
        userId = teamMember.user_id,
        taId = teamMember.ta_id,
        stepId = 0,
        campaignId = 1,
        campaignHeadStepId = None
      )
      res.map { result =>
        assert(result == Left(CreateCampaignStepVariantError.ErrorWhileCreateCampaignFirst(
          CreateCampaignFirstStepError.ValidateCampaignTemplateError(error, campaignStepDataEmailStep)
        )))
      }

    }

    it("should fail when campaign Template Check Error is Defined") {

      val variantData = data.copy(
        template_is_from_library = Some(true)
      )


      val validateStepVariantData = ValidateStepData.ValidateStepVariantData(
        campaignStepData = campaignStepDataEmailStep,
        stepId = defaultTestStepId,
        head_step_id = defaultTestHeadStepId,
        campaignId = CampaignId(id = campaign.id)
      )

      (campaignTemplateService.validateCampaignTemplate)
        .expects(
          teamMember.team_id,
          validateStepVariantData,
          data.notes
        )
        .returning(Right(true))

      (templateUtils.getTemplateIdToBeSavedWhileCreatingOrUpdatingStep)
        .expects(
          data.template_id,
          Some(true),
          teamMember.user_id,
          teamMember.team_id,
          teamMember.ta_id,
          Logger
        )
        .returning(templateDuplicateCheck)

      val res = campaignStepService.createVariant(
        orgId = orgId,
        data = variantData,
        teamId = teamMember.team_id,
        userId = teamMember.user_id,
        taId = teamMember.ta_id,
        stepId = 0,
        campaignId = 1,
        campaignHeadStepId = None
      )
      res.map { result =>
        assert(result == Left(CreateCampaignStepVariantError.ErrorWhileCreateCampaignFirst(
          CreateCampaignFirstStepError.TemplateAccessCheckError(error)
        )))
      }
    }

    it("should fail when campaign Template Not Found Error is Defined") {

      val variantData = data.copy(
        template_is_from_library = Some(true)
      )

      val validateStepVariantData = ValidateStepData.ValidateStepVariantData(
        campaignStepData = campaignStepDataEmailStep,
        stepId = defaultTestStepId,
        head_step_id = defaultTestHeadStepId,
        campaignId = CampaignId(id = campaign.id)
      )

      (campaignTemplateService.validateCampaignTemplate)
        .expects(
          teamMember.team_id,
          validateStepVariantData,
          data.notes
        )
        .returning(Right(true))

      (templateUtils.getTemplateIdToBeSavedWhileCreatingOrUpdatingStep)
        .expects(
          data.template_id,
          Some(true),
          teamMember.user_id,
          teamMember.team_id,
          teamMember.ta_id,
          Logger
        )
        .returning(templateDuplicateCheck.copy(
          templateCheckError = None,
          templateNotFoundError = Some(errMsg)
        ))

      val res = campaignStepService.createVariant(
        orgId = orgId,
        data = variantData,
        teamId = teamMember.team_id,
        userId = teamMember.user_id,
        taId = teamMember.ta_id,
        stepId = 0,
        campaignId = 1,
        campaignHeadStepId = None
      )
      res.map { result =>
        assert(result == Left(CreateCampaignStepVariantError.ErrorWhileCreateCampaignFirst(
          CreateCampaignFirstStepError.TemplateNotFoundError(errMsg)
        )))
      }
    }

    it("should fail when campaign step Dao create Fails") {

      val variantData = data.copy(
        template_is_from_library = Some(true),
        parent_id = 0,
        template_id = Some(1)
      )

      (campaignStepDAO.create)
        .expects(
          1,
          None,
          variantData,
          TeamId(teamId),
        )
        .returning(Failure(error))

      val validateStepVariantData = ValidateStepData.ValidateStepVariantData(
        campaignStepData = campaignStepDataEmailStep,
        stepId = defaultTestStepId,
        head_step_id = defaultTestHeadStepId,
        campaignId = CampaignId(id = campaign.id)
      )

      (campaignTemplateService.validateCampaignTemplate)
        .expects(
          teamMember.team_id,
          validateStepVariantData,
          data.notes
        )
        .returning(Right(true))

      (templateUtils.getTemplateIdToBeSavedWhileCreatingOrUpdatingStep)
        .expects(
          data.template_id,
          Some(true),
          teamMember.user_id,
          teamMember.team_id,
          teamMember.ta_id,
          Logger
        )
        .returning(templateDuplicateCheck.copy(
          templateCheckError = None,
          templateNotFoundError = None,
          templateIdToBeSaved = Some(1)
        ))

      val res = campaignStepService.createVariant(
        orgId = orgId,
        data = variantData,
        teamId = teamMember.team_id,
        userId = teamMember.user_id,
        taId = teamMember.ta_id,
        stepId = 0,
        campaignId = 1,
        campaignHeadStepId = None
      )
      res.map { result =>
        assert(result == Left(CreateCampaignStepVariantError.ErrorWhileCreateCampaignFirst(
          CreateCampaignFirstStepError.CreateStepSQLException(error)
        )))
      }
    }

    it("should fail when campaign step Dao returns None") {

      val variantData = data.copy(
        parent_id = 0,
        template_id = Some(1),
        template_is_from_library = Some(true)
      )

      (campaignStepDAO.create)
        .expects(
          1,
          None,
          variantData,
          TeamId(teamId)
        )
        .returning(Success(None))

      val validateStepVariantData = ValidateStepData.ValidateStepVariantData(
        campaignStepData = campaignStepDataEmailStep,
        stepId = defaultTestStepId,
        head_step_id = defaultTestHeadStepId,
        campaignId = CampaignId(id = campaign.id)
      )

      (campaignTemplateService.validateCampaignTemplate)
        .expects(
          teamMember.team_id,
          validateStepVariantData,
          data.notes
        )
        .returning(Right(true))

      (templateUtils.getTemplateIdToBeSavedWhileCreatingOrUpdatingStep)
        .expects(
          data.template_id,
          Some(true),
          teamMember.user_id,
          teamMember.team_id,
          teamMember.ta_id,
          Logger
        )
        .returning(templateDuplicateCheck.copy(
          templateCheckError = None,
          templateNotFoundError = None,
          templateIdToBeSaved = Some(1)
        ))

      val res = campaignStepService.createVariant(
        orgId = orgId,
        data = variantData,
        teamId = teamMember.team_id,
        userId = teamMember.user_id,
        taId = teamMember.ta_id,
        stepId = 0,
        campaignId = 1,
        campaignHeadStepId = None
      )
      res.map { result =>
        assert(result == Left(CreateCampaignStepVariantError.ErrorWhileCreateCampaignFirst(
          CreateCampaignFirstStepError.CreatedStepNotFound
        )))
      }
    }

    it("should fail when campaign step Dao find returns None") {

      val variantData = data.copy(
        parent_id = 0,
        template_id = Some(1),
        template_is_from_library = Some(true)
      )

      (campaignStepDAO.find)
        .expects(
          campaignStep.id,
          campaignStep.campaign_id.toLong
        )
        .returning(None)

      (campaignStepDAO.create)
        .expects(
          1,
          None,
          variantData,
          TeamId(teamId)
        )
        .returning(Success(Some(campaignStep)))

      val validateStepVariantData = ValidateStepData.ValidateStepVariantData(
        campaignStepData = campaignStepDataEmailStep,
        stepId = defaultTestStepId,
        head_step_id = defaultTestHeadStepId,
        campaignId = CampaignId(id = campaign.id)
      )

      (campaignTemplateService.validateCampaignTemplate)
        .expects(
          teamMember.team_id,
          validateStepVariantData,
          data.notes
        )
        .returning(Right(true))

      (templateUtils.getTemplateIdToBeSavedWhileCreatingOrUpdatingStep)
        .expects(
          data.template_id,
          Some(true),
          teamMember.user_id,
          teamMember.team_id,
          teamMember.ta_id,
          Logger
        )
        .returning(templateDuplicateCheck.copy(
          templateCheckError = None,
          templateNotFoundError = None,
          templateIdToBeSaved = Some(1)
        ))

      val res = campaignStepService.createVariant(
        orgId = orgId,
        data = variantData,
        teamId = teamMember.team_id,
        userId = teamMember.user_id,
        taId = teamMember.ta_id,
        stepId = 0,
        campaignId = 1,
        campaignHeadStepId = None
      )
      res.map { result =>
        assert(result == Left(CreateCampaignStepVariantError.CampaignStepNotFound))
      }
    }

    it("should fail when variants size greater than 5") {

      val variantData = data.copy(
        parent_id = 0,
        template_id = Some(1),
        template_is_from_library = Some(true)
      )

      (campaignStepVariantDAO.findByStepId)
        .expects(
          campaignStep.id
        )
        .returning(
          Seq(campaignStepVariant,campaignStepVariant,
            campaignStepVariant, campaignStepVariant, campaignStepVariant)
        )

      (campaignStepDAO.find)
        .expects(
          campaignStep.id,
          campaignStep.campaign_id.toLong
        )
        .returning(Some(campaignStep))

      (campaignStepDAO.create)
        .expects(
          1,
          None,
          variantData,
          TeamId(teamId)
        )
        .returning(Success(Some(campaignStep)))

      val validateStepVariantData = ValidateStepData.ValidateStepVariantData(
        campaignStepData = campaignStepDataEmailStep,
        stepId = defaultTestStepId,
        head_step_id = defaultTestHeadStepId,
        campaignId = CampaignId(id = campaign.id)
      )

      (campaignTemplateService.validateCampaignTemplate)
        .expects(
          teamMember.team_id,
          validateStepVariantData,
          data.notes
        )
        .returning(Right(true))

      (templateUtils.getTemplateIdToBeSavedWhileCreatingOrUpdatingStep)
        .expects(
          data.template_id,
          Some(true),
          teamMember.user_id,
          teamMember.team_id,
          teamMember.ta_id,
          Logger
        )
        .returning(templateDuplicateCheck.copy(
          templateCheckError = None,
          templateNotFoundError = None,
          templateIdToBeSaved = Some(1)
        ))

      val res = campaignStepService.createVariant(
        orgId = orgId,
        data = variantData,
        teamId = teamMember.team_id,
        userId = teamMember.user_id,
        taId = teamMember.ta_id,
        stepId = 0,
        campaignId = 1,
        campaignHeadStepId = None
      )
      res.map { result =>
        assert(result == Left(CreateCampaignStepVariantError.MaxVariantsExceeded(5)))
      }
    }

    it("should fail when Parent Step not found") {

      val variantData = data.copy(
        template_is_from_library = Some(true),
        parent_id = 1,
        template_id = Some(1)
      )

      (campaignStepDAO.find)
        .expects(
          campaignStep.id,
          campaignStep.campaign_id.toLong
        )
        .returning(None)

      val validateStepVariantData = ValidateStepData.ValidateStepVariantData(
        campaignStepData = campaignStepDataEmailStep,
        stepId = defaultTestStepId,
        head_step_id = defaultTestHeadStepId,
        campaignId = CampaignId(id = campaign.id)
      )

      (campaignTemplateService.validateCampaignTemplate)
        .expects(
          teamMember.team_id,
          validateStepVariantData,
          data.notes
        )
        .returning(Right(true))

      (templateUtils.getTemplateIdToBeSavedWhileCreatingOrUpdatingStep)
        .expects(
          data.template_id,
          Some(true),
          teamMember.user_id,
          teamMember.team_id,
          teamMember.ta_id,
          Logger
        )
        .returning(templateDuplicateCheck.copy(
          templateCheckError = None,
          templateNotFoundError = None,
          templateIdToBeSaved = Some(1)
        ))

      val res = campaignStepService.createVariant(
        orgId = orgId,
        data = variantData,
        teamId = teamMember.team_id,
        userId = teamMember.user_id,
        taId = teamMember.ta_id,
        stepId = 0,
        campaignId = 1,
        campaignHeadStepId = None
      )
      res.map { result =>
        assert(result == Left(CreateCampaignStepVariantError.ErrorWhileCreateCampaignFirst(
          CreateCampaignFirstStepError.ParentStepNotFound(
            1
          )
        )))
      }
    }

    it("should fail when campaignStepDao.create Fails while creating First Step") {

      val variantData = data.copy(
        template_is_from_library = Some(true),
        parent_id = 1,
        template_id = Some(1)
      )

      (campaignStepDAO.create)
        .expects(*, *, *, TeamId(teamId))
        .returning(Failure(error))

      (campaignStepDAO.find)
        .expects(
          campaignStep.id,
          campaignStep.campaign_id.toLong
        )
        .returning(Some(campaignStep))

      val validateStepVariantData = ValidateStepData.ValidateStepVariantData(
        campaignStepData = campaignStepDataEmailStep,
        stepId = defaultTestStepId,
        head_step_id = defaultTestHeadStepId,
        campaignId = CampaignId(id = campaign.id)
      )

      (campaignTemplateService.validateCampaignTemplate)
        .expects(
          teamMember.team_id,
          validateStepVariantData,
          data.notes
        )
        .returning(Right(true))

      (templateUtils.getTemplateIdToBeSavedWhileCreatingOrUpdatingStep)
        .expects(
          data.template_id,
          Some(true),
          teamMember.user_id,
          teamMember.team_id,
          teamMember.ta_id,
          Logger
        )
        .returning(templateDuplicateCheck.copy(
          templateCheckError = None,
          templateNotFoundError = None,
          templateIdToBeSaved = Some(1)
        ))

      val res = campaignStepService.createVariant(
        orgId = orgId,
        data = variantData,
        teamId = teamMember.team_id,
        userId = teamMember.user_id,
        taId = teamMember.ta_id,
        stepId = 0,
        campaignId = 1,
        campaignHeadStepId = None
      )
      res.map { result =>
        assert(result == Left(CreateCampaignStepVariantError.ErrorWhileCreateCampaignFirst(
          CreateCampaignFirstStepError.CreateStepSQLException(
            error
          )
        )))
      }
    }

    it("should fail when campaignStepDao.create returns None while creating first Step") {

      val variantData = data.copy(
        template_is_from_library = Some(true),
        parent_id = 1,
        template_id = Some(1)
      )

      (campaignStepDAO.create)
        .expects(*, *, *, TeamId(teamId))
        .returning(Success(None))

      (campaignStepDAO.find)
        .expects(
          campaignStep.id,
          campaignStep.campaign_id.toLong
        )
        .returning(Some(campaignStep))

      val validateStepVariantData = ValidateStepData.ValidateStepVariantData(
        campaignStepData = campaignStepDataEmailStep,
        stepId = defaultTestStepId,
        head_step_id = defaultTestHeadStepId,
        campaignId = CampaignId(id = campaign.id)
      )

      (campaignTemplateService.validateCampaignTemplate)
        .expects(
          teamMember.team_id,
          validateStepVariantData,
          data.notes
        )
        .returning(Right(true))

      (templateUtils.getTemplateIdToBeSavedWhileCreatingOrUpdatingStep)
        .expects(
          data.template_id,
          Some(true),
          teamMember.user_id,
          teamMember.team_id,
          teamMember.ta_id,
          Logger
        )
        .returning(templateDuplicateCheck.copy(
          templateCheckError = None,
          templateNotFoundError = None,
          templateIdToBeSaved = Some(1)
        ))

      val res = campaignStepService.createVariant(
        orgId = orgId,
        data = variantData,
        teamId = teamMember.team_id,
        userId = teamMember.user_id,
        taId = teamMember.ta_id,
        stepId = 0,
        campaignId = 1,
        campaignHeadStepId = None
      )
      res.map { result =>
        assert(result == Left(CreateCampaignStepVariantError.ErrorWhileCreateCampaignFirst(
          CreateCampaignFirstStepError.CreatedStepNotFound )))
      }
    }

    it("should success when create first step succeeds ") {

      val variantData = data.copy(
        template_is_from_library = Some(true),
        parent_id = 1,
        template_id = Some(1)
      )

      (campaignStepDAO.create)
        .expects(*, *, *, TeamId(teamId))
        .returning(Success(Some(campaignStep)))

      (campaignStepVariantDAO.findByStepId)
        .expects(*)
        .returning(Seq(campaignStepVariant, campaignStepVariant,campaignStepVariant,
          campaignStepVariant))

      (campaignTemplateService.validateCampaignTemplate)
        .expects(*, *, *)
        .returning(Right(true))

      (templateUtils.getTemplateIdToBeSavedWhileCreatingOrUpdatingStep)
        .expects(*, *, *, *, * , * )
        .returning(templateDuplicateCheck.copy(
          templateCheckError = None,
          templateNotFoundError = None,
          templateIdToBeSaved = Some(1)
        ))

      (campaignStepVariantDAO.create  (_: Long, _: Long, _: Long, _: CampaignStepVariantCreateOrUpdate )(_: ExecutionContext,  _: Materializer, _: WSClient, _: SRLogger))
        .expects(*, 1, 1, *, *, *, *, *)
        .returning(
          Future.successful(Some(campaignStepVariant))
        )

      (srUserFeatureUsageEventService.addFeatureUsageEvent)
        .expects(* , *)
        .returning(Success(1))

      (campaignStepDAO.find)
        .expects(
          campaignStep.id,
          campaignStep.campaign_id.toLong
        )
        .returning(Some(campaignStep))
        .anyNumberOfTimes()

      val validateStepVariantData = ValidateStepData.ValidateStepVariantData(
        campaignStepData = campaignStepDataEmailStep,
        stepId = defaultTestStepId,
        head_step_id = defaultTestHeadStepId,
        campaignId = CampaignId(id = campaign.id)
      )

      (campaignTemplateService.validateCampaignTemplate)
        .expects(
          teamMember.team_id,
          validateStepVariantData,
          data.notes
        )
        .returning(Right(true))

      (templateUtils.getTemplateIdToBeSavedWhileCreatingOrUpdatingStep)
        .expects(
          data.template_id,
          Some(true),
          teamMember.user_id,
          teamMember.team_id,
          teamMember.ta_id,
          Logger
        )
        .returning(templateDuplicateCheck.copy(
          templateCheckError = None,
          templateNotFoundError = None,
          templateIdToBeSaved = Some(1)
        ))

      val res = campaignStepService.createVariant(
        orgId = orgId,
        data = variantData,
        teamId = teamMember.team_id,
        userId = teamMember.user_id,
        taId = teamMember.ta_id,
        stepId = 0,
        campaignId = 1,
        campaignHeadStepId = None
      )
      res.map { result =>
        assert(result == Right(campaignStepVariant))
      }
    }

    it("should fail when step_id is not 0 and template_is_from_library is empty") {

      val variantData = data.copy(
        template_is_from_library = None,
        parent_id = 0,
        template_id = Some(1)
      )

      (campaignStepVariantDAO.findByStepId)
        .expects(
          campaignStep.id
        )
        .returning(
          Seq(campaignStepVariant,campaignStepVariant,
            campaignStepVariant, campaignStepVariant)
        )

      (campaignStepDAO.find)
        .expects(
          campaignStep.id,
          campaignStep.campaign_id.toLong
        )
        .returning(Some(campaignStep))

//      (campaignStepDAO.create)
//        .expects(
//          1,
//          None,
//          data.copy(
//            parent_id = 0,
//            template_id = Some(1),
//            template_is_from_library = Some(true)
//          )
//        )
//        .returning(Success(Some(campaignStep)))

//      (campaignTemplateService.validateCampaignTemplate)
//        .expects(
//          teamMember.team_id,
//          campaignStepDataEmailStep.body,
//          campaignStepDataEmailStep.subject
//        )
//        .returning(Success(true))

//      (templateUtils.getTemplateIdToBeSavedWhileCreatingOrUpdatingStep)
//        .expects(
//          data.template_id,
//          Some(true),
//          teamMember.user_id,
//          teamMember.team_id,
//          teamMember.ta_id,
//          Logger
//        )
//        .returning(templateDuplicateCheck.copy(
//          templateCheckError = None,
//          templateNotFoundError = None,
//          templateIdToBeSaved = Some(1)
//        ))

      val res = campaignStepService.createVariant(
        orgId = orgId,
        data = variantData,
        teamId = teamMember.team_id,
        userId = teamMember.user_id,
        taId = teamMember.ta_id,
        stepId = 1,
        campaignId = 1,
        campaignHeadStepId = None
      )
      res.map { result =>
        assert(result == Left(CreateCampaignStepVariantError.TemplateIsFromLibraryFieldMissing))
      }
    }

    it("should fail when step_id is not 0 and template_validation fails") {

      val variantData = data.copy(
        template_is_from_library = Some(true),
        parent_id = 0,
        template_id = Some(1)
      )

      (campaignStepVariantDAO.findByStepId)
        .expects(
          campaignStep.id
        )
        .returning(
          Seq(campaignStepVariant,campaignStepVariant,
            campaignStepVariant, campaignStepVariant)
        )

      (campaignStepDAO.find)
        .expects(
          campaignStep.id,
          campaignStep.campaign_id.toLong
        )
        .returning(Some(campaignStep))

      //      (campaignStepDAO.create)
      //        .expects(
      //          1,
      //          None,
      //          data.copy(
      //            parent_id = 0,
      //            template_id = Some(1),
      //            template_is_from_library = Some(true)
      //          )
      //        )
      //        .returning(Success(Some(campaignStep)))

      val validateStepVariantData = ValidateStepData.ValidateStepVariantData(
        campaignStepData = campaignStepDataEmailStep,
        stepId = Some(1),
        head_step_id = defaultTestHeadStepId,
        campaignId = CampaignId(id = campaign.id)
      )

            (campaignTemplateService.validateCampaignTemplate)
              .expects(
                teamMember.team_id,
                validateStepVariantData,
                data.notes
              )
              .returning(Left(ValidateCampaignTemplateError.ErrorWhileValidatingBody(error)))

      //      (templateUtils.getTemplateIdToBeSavedWhileCreatingOrUpdatingStep)
      //        .expects(
      //          data.template_id,
      //          Some(true),
      //          teamMember.user_id,
      //          teamMember.team_id,
      //          teamMember.ta_id,
      //          Logger
      //        )
      //        .returning(templateDuplicateCheck.copy(
      //          templateCheckError = None,
      //          templateNotFoundError = None,
      //          templateIdToBeSaved = Some(1)
      //        ))

      val res = campaignStepService.createVariant(
        orgId = orgId,
        data = variantData,
        teamId = teamMember.team_id,
        userId = teamMember.user_id,
        taId = teamMember.ta_id,
        stepId = 1,
        campaignId = 1,
        campaignHeadStepId = None
      )
      res.map { result =>
        assert(result == Left(CreateCampaignStepVariantError.ValidateCampaignTemplateError(error)))
      }
    }

    it("should fail when step_id is not 0 and templateCheckError is defined") {

      val variantData = data.copy(
        template_is_from_library = Some(true),
        parent_id = 0,
        template_id = Some(1)
      )

      (campaignStepVariantDAO.findByStepId)
        .expects(
          campaignStep.id
        )
        .returning(
          Seq(campaignStepVariant, campaignStepVariant,
            campaignStepVariant, campaignStepVariant)
        )

      (campaignStepDAO.find)
        .expects(
          campaignStep.id,
          campaignStep.campaign_id.toLong
        )
        .returning(Some(campaignStep))

      //      (campaignStepDAO.create)
      //        .expects(
      //          1,
      //          None,
      //          data.copy(
      //            parent_id = 0,
      //            template_id = Some(1),
      //            template_is_from_library = Some(true)
      //          )
      //        )
      //        .returning(Success(Some(campaignStep)))

      val validateStepVariantData = ValidateStepData.ValidateStepVariantData(
        campaignStepData = campaignStepDataEmailStep,
        stepId = Some(1),
        head_step_id = defaultTestHeadStepId,
        campaignId = CampaignId(id = campaign.id)
      )

      (campaignTemplateService.validateCampaignTemplate)
        .expects(
          teamMember.team_id,
          validateStepVariantData,
          data.notes
        )
        .returning(Right(true))

      (templateUtils.getTemplateIdToBeSavedWhileCreatingOrUpdatingStep)
        .expects(
          data.template_id,
          Some(true),
          teamMember.user_id,
          teamMember.team_id,
          teamMember.ta_id,
          Logger
        )
        .returning(templateDuplicateCheck.copy(
          templateCheckError = Some(error),
          templateNotFoundError = None,
          templateIdToBeSaved = Some(1)
        ))

      val res = campaignStepService.createVariant(
        orgId = orgId,
        data = variantData,
        teamId = teamMember.team_id,
        userId = teamMember.user_id,
        taId = teamMember.ta_id,
        stepId = 1,
        campaignId = 1,
        campaignHeadStepId = None
      )
      res.map { result =>
        assert(result == Left(CreateCampaignStepVariantError.TemplateAccessCheckError(error)))
      }
    }

    it("should fail when step_id is not 0 and template not found") {

      val variantData = data.copy(
        template_is_from_library = Some(true),
        parent_id = 0,
        template_id = Some(1)
      )

      (campaignStepVariantDAO.findByStepId)
        .expects(
          campaignStep.id
        )
        .returning(
          Seq(campaignStepVariant, campaignStepVariant,
            campaignStepVariant, campaignStepVariant)
        )

      (campaignStepDAO.find)
        .expects(
          campaignStep.id,
          campaignStep.campaign_id.toLong
        )
        .returning(Some(campaignStep))

      //      (campaignStepDAO.create)
      //        .expects(
      //          1,
      //          None,
      //          data.copy(
      //            parent_id = 0,
      //            template_id = Some(1),
      //            template_is_from_library = Some(true)
      //          )
      //        )
      //        .returning(Success(Some(campaignStep)))


      val validateStepVariantData = ValidateStepData.ValidateStepVariantData(
        campaignStepData = campaignStepDataEmailStep,
        stepId = Some(1),
        head_step_id = defaultTestHeadStepId,
        campaignId = CampaignId(id = campaign.id)
      )

      (campaignTemplateService.validateCampaignTemplate)
        .expects(
          teamMember.team_id,
          validateStepVariantData,
          data.notes
        )
        .returning(Right(true))

      (templateUtils.getTemplateIdToBeSavedWhileCreatingOrUpdatingStep)
        .expects(
          data.template_id,
          Some(true),
          teamMember.user_id,
          teamMember.team_id,
          teamMember.ta_id,
          Logger
        )
        .returning(templateDuplicateCheck.copy(
          templateCheckError = None,
          templateNotFoundError = Some(errMsg),
          templateIdToBeSaved = Some(1)
        ))

      val res = campaignStepService.createVariant(
        orgId = orgId,
        data = variantData,
        teamId = teamMember.team_id,
        userId = teamMember.user_id,
        taId = teamMember.ta_id,
        stepId = 1,
        campaignId = 1,
        campaignHeadStepId = None
      )
      res.map { result =>
        assert(result == Left(CreateCampaignStepVariantError.TemplateNotFoundError(errMsg)))
      }
    }

    it("should fail when step_id is not 0 and campaignStepVariantDao returns None") {

      val variantData = data.copy(
        template_is_from_library = Some(true),
        parent_id = 0,
        template_id = Some(1)
      )

      (campaignStepVariantDAO.findByStepId)
        .expects(
          campaignStep.id
        )
        .returning(
          Seq(campaignStepVariant, campaignStepVariant,
            campaignStepVariant, campaignStepVariant)
        )

      (campaignStepDAO.find)
        .expects(
          campaignStep.id,
          campaignStep.campaign_id.toLong
        )
        .returning(Some(campaignStep))

      (campaignStepVariantDAO.create  (_: Long, _: Long, _: Long, _: CampaignStepVariantCreateOrUpdate )(_: ExecutionContext,  _: Materializer, _: WSClient, _: SRLogger))
        .expects(*, 1, 1, *, *, *, *, *)
        .returning(
          Future.successful(None)
        )

      val validateStepVariantData = ValidateStepData.ValidateStepVariantData(
        campaignStepData = campaignStepDataEmailStep,
        stepId = Some(1),
        head_step_id = defaultTestHeadStepId,
        campaignId = CampaignId(id = campaign.id)
      )

      (campaignTemplateService.validateCampaignTemplate)
        .expects(
          teamMember.team_id,
          validateStepVariantData,
          data.notes
        )
        .returning(Right(true))

      (templateUtils.getTemplateIdToBeSavedWhileCreatingOrUpdatingStep)
        .expects(
          data.template_id,
          Some(true),
          teamMember.user_id,
          teamMember.team_id,
          teamMember.ta_id,
          Logger
        )
        .returning(templateDuplicateCheck.copy(
          templateCheckError = None,
          templateNotFoundError = None,
          templateIdToBeSaved = Some(1)
        ))

      val res = campaignStepService.createVariant(
        orgId = orgId,
        data = variantData,
        teamId = teamMember.team_id,
        userId = teamMember.user_id,
        taId = teamMember.ta_id,
        stepId = 1,
        campaignId = 1,
        campaignHeadStepId = None
      )
      res.map { result =>
        assert(result == Left(CreateCampaignStepVariantError.CreatedStepVariantNotFound))
      }
    }

    it("should fail when srUserFeatureUsageEventService.addFeatureUsageEvent Fails") {

      val variantData = data.copy(
        template_is_from_library = Some(true),
        parent_id = 0,
        template_id = Some(1)
      )

      (campaignStepVariantDAO.findByStepId)
        .expects(
          campaignStep.id
        )
        .returning(
          Seq(campaignStepVariant, campaignStepVariant,
            campaignStepVariant, campaignStepVariant)
        )

      (campaignStepDAO.find)
        .expects(
          campaignStep.id,
          campaignStep.campaign_id.toLong
        )
        .returning(Some(campaignStep))

      (srUserFeatureUsageEventService.addFeatureUsageEvent)
        .expects(*, *)
        .returning(Failure(error))

      (campaignStepVariantDAO.create  (_: Long, _: Long, _: Long, _: CampaignStepVariantCreateOrUpdate )(_: ExecutionContext,  _: Materializer, _: WSClient, _: SRLogger))
        .expects(*, 1, 1, *, *, *, *, *)
        .returning(
          Future.successful(Some(campaignStepVariant))
        )

      val validateStepVariantData = ValidateStepData.ValidateStepVariantData(
        campaignStepData = campaignStepDataEmailStep,
        stepId = Some(1),
        head_step_id = defaultTestHeadStepId,
        campaignId = CampaignId(id = campaign.id)
      )

      (campaignTemplateService.validateCampaignTemplate)
        .expects(
          teamMember.team_id,
          validateStepVariantData,
          data.notes
        )
        .returning(Right(true))

      (templateUtils.getTemplateIdToBeSavedWhileCreatingOrUpdatingStep)
        .expects(
          data.template_id,
          Some(true),
          teamMember.user_id,
          teamMember.team_id,
          teamMember.ta_id,
          Logger
        )
        .returning(templateDuplicateCheck.copy(
          templateCheckError = None,
          templateNotFoundError = None,
          templateIdToBeSaved = Some(1)
        ))

      val res = campaignStepService.createVariant(
        orgId = orgId,
        data = variantData,
        teamId = teamMember.team_id,
        userId = teamMember.user_id,
        taId = teamMember.ta_id,
        stepId = 1,
        campaignId = 1,
        campaignHeadStepId = None
      )
      res.map { result =>
        assert(result == Left(CreateCampaignStepVariantError.FeatureUsageEventSaveError(error)))
      }
    }

    it("should success when srUserFeatureUsageEventService.addFeatureUsageEvent returns success") {

      val variantData = data.copy(
        template_is_from_library = Some(true),
        parent_id = 0,
        template_id = Some(1)
      )

      (campaignStepVariantDAO.findByStepId)
        .expects(
          campaignStep.id
        )
        .returning(
          Seq(campaignStepVariant, campaignStepVariant,
            campaignStepVariant, campaignStepVariant)
        )

      (campaignStepDAO.find)
        .expects(
          campaignStep.id,
          campaignStep.campaign_id.toLong
        )
        .returning(Some(campaignStep))

      (srUserFeatureUsageEventService.addFeatureUsageEvent)
        .expects(*, *)
        .returning(Success(1))

      (campaignStepVariantDAO.create  (_: Long, _: Long, _: Long, _: CampaignStepVariantCreateOrUpdate )(_: ExecutionContext,  _: Materializer, _: WSClient, _: SRLogger))
        .expects(*, 1, 1, *, *, *, *, *)
        .returning(
          Future.successful(Some(campaignStepVariant))
        )

      val validateStepVariantData = ValidateStepData.ValidateStepVariantData(
        campaignStepData = campaignStepDataEmailStep,
        stepId = Some(1),
        head_step_id = defaultTestHeadStepId,
        campaignId = CampaignId(id = campaign.id)
      )

      (campaignTemplateService.validateCampaignTemplate)
        .expects(
          teamMember.team_id,
          validateStepVariantData,
          data.notes
        )
        .returning(Right(true))

      (templateUtils.getTemplateIdToBeSavedWhileCreatingOrUpdatingStep)
        .expects(
          data.template_id,
          Some(true),
          teamMember.user_id,
          teamMember.team_id,
          teamMember.ta_id,
          Logger
        )
        .returning(templateDuplicateCheck.copy(
          templateCheckError = None,
          templateNotFoundError = None,
          templateIdToBeSaved = Some(1)
        ))

      val res = campaignStepService.createVariant(
        orgId = orgId,
        data = variantData,
        teamId = teamMember.team_id,
        userId = teamMember.user_id,
        taId = teamMember.ta_id,
        stepId = 1,
        campaignId = 1,
        campaignHeadStepId = None
      )
      res.map { result =>
        assert(result == Right(campaignStepVariant))
      }
    }

    it("should fail when campaignStepVariantDao.create fails") {

      val variantData = data.copy(
        template_is_from_library = Some(true),
        parent_id = 0,
        template_id = Some(1)
      )

      (campaignStepVariantDAO.findByStepId)
        .expects(
          campaignStep.id
        )
        .returning(
          Seq(campaignStepVariant, campaignStepVariant,
            campaignStepVariant, campaignStepVariant)
        )

      (campaignStepDAO.find)
        .expects(
          campaignStep.id,
          campaignStep.campaign_id.toLong
        )
        .returning(Some(campaignStep))

      (campaignStepVariantDAO.create  (_: Long, _: Long, _: Long, _: CampaignStepVariantCreateOrUpdate )(_: ExecutionContext,  _: Materializer, _: WSClient, _: SRLogger))
        .expects(*, 1, 1, *, *, *, *, *)
        .returning(
          Future.failed(error)
        )


      val validateStepVariantData = ValidateStepData.ValidateStepVariantData(
        campaignStepData = campaignStepDataEmailStep,
        stepId = Some(1),
        head_step_id = defaultTestHeadStepId,
        campaignId = CampaignId(id = campaign.id)
      )

      (campaignTemplateService.validateCampaignTemplate)
        .expects(
          teamMember.team_id,
          validateStepVariantData,
          data.notes
        )
        .returning(Right(true))

      (templateUtils.getTemplateIdToBeSavedWhileCreatingOrUpdatingStep)
        .expects(
          data.template_id,
          Some(true),
          teamMember.user_id,
          teamMember.team_id,
          teamMember.ta_id,
          Logger
        )
        .returning(templateDuplicateCheck.copy(
          templateCheckError = None,
          templateNotFoundError = None,
          templateIdToBeSaved = Some(1)
        ))

      val res = campaignStepService.createVariant(
        orgId = orgId,
        data = variantData,
        teamId = teamMember.team_id,
        userId = teamMember.user_id,
        taId = teamMember.ta_id,
        stepId = 1,
        campaignId = 1,
        campaignHeadStepId = None
      )
      res.map { result =>
        assert(result == Left(CreateCampaignStepVariantError.CreateStepVariantException(error)))
      }
    }

  }

  describe("CampaignStepService.findStepsByCampaign") {
    it("Should fail when head_step_id is None") {

      (campaignDAOService.findOrderedSteps)
        .expects(1, TeamId(id = campaign.team_id))
        .returning(Seq())

      val res = campaignStepService.findStepsByCampaign(
        campaign = campaign.copy(head_step_id = None)
      )

      assert(res == None)
    }

    it("Should Succeed when head_step_id is not None") {
      (campaignDAOService.findOrderedSteps)
        .expects(3, TeamId(id = campaign.team_id))
        .returning(Seq(campaignStepWithChildren.copy(children = List())))

      val res = campaignStepService.findStepsByCampaign(
        campaign = campaign.copy(id = 3L)
      )

      assert(res == Some(List(campaignStepWithChildren.copy(children = List()))))
    }
  }

  describe("CampaignStepService.updateVariant") {

    it("Should fail when Auto Email Body is empty") {

      val variantData = data.copy(
        step_data = campaignStepDataEmailStep.copy(
          body = ""
        )
      )

      val res = campaignStepService.updateVariant(
        orgId = 1,
        data = variantData,
        t = teamMember,
        variantId = 1,
        campaignHeadStepId = defaultTestHeadStepId,
        stepId = 1,
        campaignId = 1
      )
      res.map { result =>
        assert(result == Left(UpdateVariantError.CampaignStepVariantValidationError(
          ValidateCampaignStepVariantError.EmailBodyCantBeEmpty
        )))
      }
    }

    it("Should fail when Auto Email Subject is empty") {

      val variantData = data.copy(
        step_data = campaignStepDataEmailStep.copy(
          subject = ""
        )
      )

      val res = campaignStepService.updateVariant(
        orgId = 1,
        data = variantData,
        t = teamMember,
        variantId = 1,
        campaignHeadStepId = defaultTestHeadStepId,
        stepId = 1,
        campaignId = 1
      )
      res.map { result =>
        assert(result == Left(UpdateVariantError.CampaignStepVariantValidationError(
          ValidateCampaignStepVariantError.EmailSubjectCantBeEmpty
        )))
      }
    }

    it("Should fail when Manual Email Body is empty") {

      val variantData = data.copy(
        step_data = CampaignStepData.ManualEmailStep(
          subject = "Subject",
          body = ""
        )
      )

      val res = campaignStepService.updateVariant(
        orgId = 1,
        data = variantData,
        t = teamMember,
        variantId = 1,
        campaignHeadStepId = defaultTestHeadStepId,
        stepId = 1,
        campaignId = 1
      )
      res.map { result =>
        assert(result == Left(UpdateVariantError.CampaignStepVariantValidationError(
          ValidateCampaignStepVariantError.EmailBodyCantBeEmpty
        )))
      }
    }

    it("Should fail when Manual Email Subject is empty") {

      val variantData = data.copy(
        step_data = CampaignStepData.ManualEmailStep(
          subject = "",
          body = "Body"
        )
      )

      val res = campaignStepService.updateVariant(
        orgId = 1,
        data = variantData,
        t = teamMember,
        variantId = 1,
        campaignHeadStepId = defaultTestHeadStepId,
        stepId = 1,
        campaignId = 1
      )
      res.map { result =>
        assert(result == Left(UpdateVariantError.CampaignStepVariantValidationError(
          ValidateCampaignStepVariantError.EmailSubjectCantBeEmpty
        )))
      }
    }

    it("Should fail when Whatsapp Message Body is empty") {

      val variantData = data.copy(
        step_data = CampaignStepData.WhatsappMessageData(
          body = ""
        )
      )

      val res = campaignStepService.updateVariant(
        orgId = 1,
        data = variantData,
        t = teamMember,
        variantId = 1,
        campaignHeadStepId = defaultTestHeadStepId,
        stepId = 1,
        campaignId = 1
      )
      res.map { result =>
        assert(result == Left(UpdateVariantError.CampaignStepVariantValidationError(
          ValidateCampaignStepVariantError.WhatsappMessageBodyCantBeEmpty
        )))
      }
    }

    it("Should fail when Phone Message Body is empty") {

      val variantData = data.copy(
        step_data = CampaignStepData.SmsMessageData(
          body = ""
        )
      )

      val res = campaignStepService.updateVariant(
        orgId = 1,
        data = variantData,
        t = teamMember,
        variantId = 1,
        campaignHeadStepId = defaultTestHeadStepId,
        stepId = 1,
        campaignId = 1
      )
      res.map { result =>
        assert(result == Left(UpdateVariantError.CampaignStepVariantValidationError(
          ValidateCampaignStepVariantError.PhoneMessageBodyCantBeEmpty
        )))
      }
    }

    it("Should fail when General Task Notes is empty") {

      val variantData = data.copy(
        step_data = CampaignStepData.GeneralTaskData(),
        notes = Some("")
      )

      val res = campaignStepService.updateVariant(
        orgId = 1,
        data = variantData,
        t = teamMember,
        variantId = 1,
        campaignHeadStepId = defaultTestHeadStepId,
        stepId = 1,
        campaignId = 1
      )
      res.map { result =>
        assert(result == Left(UpdateVariantError.CampaignStepVariantValidationError(
          ValidateCampaignStepVariantError.GeneralTaskNotesCantBeEmpty
        )))
      }
    }

    it("Should fail when Linkedin Inmail Body is empty") {

      val variantData = data.copy(
        step_data = CampaignStepData.LinkedinInmailData(
          subject = None,
          body = ""
        )
      )

      val res = campaignStepService.updateVariant(
        orgId = 1,
        data = variantData,
        t = teamMember,
        variantId = 1,
        campaignHeadStepId = defaultTestHeadStepId,
        stepId = 1,
        campaignId = 1
      )
      res.map { result =>
        assert(result == Left(UpdateVariantError.TemplateIsFromLibraryError))
      }
    }

    it("Should fail when Linkedin Message Body is empty") {

      val variantData = data.copy(
        step_data = CampaignStepData.LinkedinMessageData(
          body = ""
        )
      )

      val res = campaignStepService.updateVariant(
        orgId = 1,
        data = variantData,
        t = teamMember,
        variantId = 1,
        campaignHeadStepId = defaultTestHeadStepId,
        stepId = 1,
        campaignId = 1
      )
      res.map { result =>
        assert(result == Left(UpdateVariantError.TemplateIsFromLibraryError))
      }
    }

    it("Should fail when template_is_from_library not defined") {

      val res = campaignStepService.updateVariant(
        orgId = 1,
        data = data,
        t = teamMember,
        variantId = 1,
        campaignHeadStepId = defaultTestHeadStepId,
        stepId = 1,
        campaignId = 1
      )

      res.map { result =>
        assert(result == Left(UpdateVariantError.TemplateIsFromLibraryError))
      }

    }

    it("Should fail when body contains {{previous_subject}} in step_data") {

      val stepData = campaignStepDataEmailStep.copy(
        body = "{{previous_subject}}"
      )

      val variantData = data.copy(
        template_is_from_library = Some(true),
        step_data = stepData
      )

      val validateStepVariantData = ValidateStepData.ValidateStepVariantData(
        campaignStepData = stepData,
        stepId = defaultTestStepId,
        head_step_id = defaultTestHeadStepId,
        campaignId = CampaignId(id = campaign.id)
      )

      (campaignTemplateService.validateCampaignTemplate)
        .expects(
          teamMember.team_id,
          validateStepVariantData,
          data.notes
        )
        .returning(Left(ValidateCampaignTemplateError.BodyCantHavePreviousSubject(error)))

      val res = campaignStepService.updateVariant(
        orgId = orgId,
        data = variantData,
        t = teamMember,
        variantId = 1,
        stepId = defaultTestStepId.get,
        campaignHeadStepId = None,
        campaignId = 1
      )

      res.map { result =>
        assert(result == Left(UpdateVariantError.ValidateTemplateError(error)))
      }

    }

    it("Should fail when subject contains {{signature}} in step_data") {

      val stepData = campaignStepDataEmailStep.copy(
        subject = "{{signature}}"
      )

      val variantData = data.copy(
        template_is_from_library = Some(true),
        step_data = stepData
      )

      val validateStepVariantData = ValidateStepData.ValidateStepVariantData(
        campaignStepData = stepData,
        stepId = defaultTestStepId,
        head_step_id = defaultTestHeadStepId,
        campaignId = CampaignId(id = campaign.id)
      )

      (campaignTemplateService.validateCampaignTemplate)
        .expects(
          teamMember.team_id,
          validateStepVariantData,
          data.notes
        )
        .returning(Left(ValidateCampaignTemplateError.SubjectCantHaveSignature(error)))

      val res = campaignStepService.updateVariant(
        orgId = orgId,
        data = variantData,
        t = teamMember,
        variantId = 1,
        stepId = defaultTestStepId.get,
        campaignHeadStepId = None,
        campaignId = 1
      )

      res.map { result =>
        assert(result == Left(UpdateVariantError.ValidateTemplateError(error)))
      }

    }

    it("Should fail when first email contains {{previous_subject}} in step_data") {

      val stepData = campaignStepDataEmailStep.copy(
        subject = "{{previous_subject}}"
      )

      val variantData = data.copy(
        template_is_from_library = Some(true),
        step_data = stepData
      )

      val validateStepVariantData = ValidateStepData.ValidateStepVariantData(
        campaignStepData = stepData,
        stepId = defaultTestStepId,
        head_step_id = defaultTestStepId,
        campaignId = CampaignId(id = campaign.id)
      )

      (campaignTemplateService.validateCampaignTemplate)
        .expects(
          teamMember.team_id,
          validateStepVariantData,
          data.notes
        )
        .returning(Left(ValidateCampaignTemplateError.PreviousSubInFirstEmail(error)))

      val res = campaignStepService.updateVariant(
        orgId = orgId,
        data = variantData,
        t = teamMember,
        variantId = 1,
        stepId = defaultTestStepId.get,
        campaignHeadStepId = defaultTestStepId,
        campaignId = 1
      )

      res.map { result =>
        assert(result == Left(UpdateVariantError.ValidateTemplateError(error))
        )
      }

    }

    it("Should fail when Validate template Fails") {

      val variantData = data.copy(
        template_is_from_library = Some(true)
      )

      (campaignTemplateService.validateCampaignTemplate)
        .expects(*, *, *)
        .returning(Left(ValidateCampaignTemplateError.ErrorWhileValidatingBody(error)))

      val res = campaignStepService.updateVariant(
        orgId = 1,
        data = variantData,
        t = teamMember,
        variantId = 1,
        stepId = 1,
        campaignHeadStepId = None,
        campaignId = 1
      )

      res.map { result =>
        assert(result == Left(UpdateVariantError.ValidateTemplateError(error)))
      }

    }

    it("Should fail when validate subject throws error") {

      val variantData = data.copy(
        template_is_from_library = Some(true)
      )

      val validateStepVariantData = ValidateStepData.ValidateStepVariantData(
        campaignStepData = campaignStepDataEmailStep,
        stepId = defaultTestStepId,
        head_step_id = defaultTestHeadStepId,
        campaignId = CampaignId(id = campaign.id)
      )

      (campaignTemplateService.validateCampaignTemplate)
        .expects(
          teamMember.team_id,
          validateStepVariantData,
          data.notes
        )
        .returning(Left(ValidateCampaignTemplateError.ErrorWhileValidatingSubject(error)))

      val res = campaignStepService.updateVariant(
        orgId = orgId,
        data = variantData,
        t = teamMember,
        variantId = 1,
        stepId = defaultTestStepId.get,
        campaignHeadStepId = None,
        campaignId = 1
      )
      res.map { result =>
        assert(result == Left(UpdateVariantError.ValidateTemplateError(error)))
      }

    }

    it("Should fail when template Check error is defined") {

      val variantData = data.copy(
        template_is_from_library = Some(true)
      )

      (campaignTemplateService.validateCampaignTemplate)
        .expects(*, *, *)
        .returning(Right(true))

      (templateUtils.getTemplateIdToBeSavedWhileCreatingOrUpdatingStep)
        .expects(*, *, *, *, *, *)
        .returning(templateDuplicateCheck.copy(
          templateCheckError = Some(error),
          templateNotFoundError = None,
          templateIdToBeSaved = Some(1)
        ))

      val res = campaignStepService.updateVariant(
        orgId = 1,
        data = variantData,
        t = teamMember,
        variantId = 1,
        stepId = 1,
        campaignHeadStepId = None,
        campaignId = 1
      )

      res.map { result =>
        assert(result == Left(UpdateVariantError.ErrorWhileCheckTeamHasAccessError(
          Some(error)
        )))
      }

    }

    it("Should fail when template Not Found") {

      val variantData = data.copy(
        template_is_from_library = Some(true)
      )

      (campaignTemplateService.validateCampaignTemplate)
        .expects(*, *, *)
        .returning(Right(true))

      (templateUtils.getTemplateIdToBeSavedWhileCreatingOrUpdatingStep)
        .expects(*, *, *, *, *, *)
        .returning(templateDuplicateCheck.copy(
          templateCheckError = None,
          templateNotFoundError = Some(errMsg),
          templateIdToBeSaved = Some(1)
        ))

      val res = campaignStepService.updateVariant(
        orgId = 1,
        data = variantData,
        t = teamMember,
        variantId = 1,
        stepId = 1,
        campaignHeadStepId = None,
        campaignId = 1
      )

      res.map { result =>
        assert(result == Left(UpdateVariantError.TemplateNotFoundWhileUpdateError(
          errMsg
        )))
      }

    }

    it("Should fail when campaignStepDao.find function returns None") {

      val variantData = data.copy(
        template_is_from_library = Some(true)
      )

      (campaignStepDAO.find(_: Long, _: Long))
        .expects(*, *)
        .returning(None)

      (campaignTemplateService.validateCampaignTemplate)
        .expects(*, *, *)
        .returning(Right(true))

      (templateUtils.getTemplateIdToBeSavedWhileCreatingOrUpdatingStep)
        .expects(*, *, *, *, *, *)
        .returning(templateDuplicateCheck.copy(
          templateCheckError = None,
          templateNotFoundError = None,
          templateIdToBeSaved = Some(1)
        ))

      val res = campaignStepService.updateVariant(
        orgId = 1,
        data = variantData,
        t = teamMember,
        variantId = 1,
        stepId = 1,
        campaignHeadStepId = None,
        campaignId = 1
      )

      res.map { result =>
        assert(result == Left(UpdateVariantError.CampaignStepNotFound))
      }

    }

    it("Should fail when step_type from data and DB does not match") {

      val variantData = data.copy(
        step_data = CampaignStepData.ManualEmailStep(
          subject = "Subject",
          body = "Body of Email"
        ),
        template_is_from_library = Some(true)
      )

      (campaignStepDAO.find(_: Long, _: Long))
        .expects(*, *)
        .returning(Some(campaignStep))

      (campaignTemplateService.validateCampaignTemplate)
        .expects(*, *, *)
        .returning(Right(true))

      (templateUtils.getTemplateIdToBeSavedWhileCreatingOrUpdatingStep)
        .expects(*, *, *, *, *, *)
        .returning(templateDuplicateCheck.copy(
          templateCheckError = None,
          templateNotFoundError = None,
          templateIdToBeSaved = Some(1)
        ))

      val res = campaignStepService.updateVariant(
        orgId = 1,
        data = variantData,
        t = teamMember,
        variantId = 1,
        stepId = 1,
        campaignHeadStepId = None,
        campaignId = 1
      )

      res.map { result =>
        assert(result == Left(UpdateVariantError.CannotUpdateStepType))
      }

    }

    it("Should fail when campaignStepVariantDao.update function returns None") {

      val variantData = data.copy(
        template_is_from_library = Some(true)
      )

      (campaignStepVariantDAO.update(_: Long, _: Long, _: Long, _: Long, _: CampaignStepVariantCreateOrUpdate, _: TeamId)(_: ExecutionContext,
        _: Materializer, _: WSClient, _: SRLogger))
        .expects(*, *, *, *, *, *, *, *, *,*)
        .returning(Future.successful(None))

      (campaignStepDAO.find(_: Long, _: Long))
        .expects(*, *)
        .returning(Some(campaignStep))

      (campaignTemplateService.validateCampaignTemplate)
        .expects(*, *, *)
        .returning(Right(true))

      (templateUtils.getTemplateIdToBeSavedWhileCreatingOrUpdatingStep)
        .expects(*, *, *, *, *, *)
        .returning(templateDuplicateCheck.copy(
          templateCheckError = None,
          templateNotFoundError = None,
          templateIdToBeSaved = Some(1)
        ))

      val res = campaignStepService.updateVariant(
        orgId = 1,
        data = variantData,
        t = teamMember,
        variantId = 1,
        stepId = 1,
        campaignHeadStepId = None,
        campaignId = 1
      )

      res.map { result =>
        assert(result == Left(UpdateVariantError.StepVariantNotFoundError))
      }

    }

    it("Should success when campaignStepVariantDao.update funtion returns campaign variant") {

      val variantData = data.copy(
        template_is_from_library = Some(true)
      )

      (campaignStepVariantDAO.update(_: Long, _: Long, _: Long, _: Long, _: CampaignStepVariantCreateOrUpdate, _: TeamId)(_: ExecutionContext,
        _: Materializer, _: WSClient, _: SRLogger))
        .expects(*, *, *, *, *, *, *, *, *, *)
        .returning(Future.successful(Some(campaignStepVariant)))

      (campaignStepDAO.find(_: Long, _: Long))
        .expects(*, *)
        .returning(Some(campaignStep))

      (campaignTemplateService.validateCampaignTemplate)
        .expects(*, *, *)
        .returning(Right(true))

      (templateUtils.getTemplateIdToBeSavedWhileCreatingOrUpdatingStep)
        .expects(*, *, *, *, *, *)
        .returning(templateDuplicateCheck.copy(
          templateCheckError = None,
          templateNotFoundError = None,
          templateIdToBeSaved = Some(1)
        ))

      val res = campaignStepService.updateVariant(
        orgId = 1,
        data = variantData,
        t = teamMember,
        variantId = 1,
        stepId = 1,
        campaignHeadStepId = None,
        campaignId = 1
      )

      res.map { result =>
        assert(result == Right(campaignStepVariant))
      }

    }

    it("Should fail when campaignStepVariantDao.update Fails") {

      val variantData = data.copy(
        template_is_from_library = Some(true)
      )

      (campaignStepVariantDAO.update(_: Long, _: Long, _: Long, _: Long, _: CampaignStepVariantCreateOrUpdate, _: TeamId)(_: ExecutionContext,
        _: Materializer, _: WSClient, _: SRLogger))
        .expects(*, *, *, *, *, *, *, *, *, *)
        .returning(Future.failed(error))

      (campaignStepDAO.find(_: Long, _: Long))
        .expects(*, *)
        .returning(Some(campaignStep))

      (campaignTemplateService.validateCampaignTemplate)
        .expects(*, *, *)
        .returning(Right(true))

      (templateUtils.getTemplateIdToBeSavedWhileCreatingOrUpdatingStep)
        .expects(*, *, *, *, *, *)
        .returning(templateDuplicateCheck.copy(
          templateCheckError = None,
          templateNotFoundError = None,
          templateIdToBeSaved = Some(1)
        ))

      val res = campaignStepService.updateVariant(
        orgId = 1,
        data = variantData,
        t = teamMember,
        variantId = 1,
        stepId = 1,
        campaignHeadStepId = None,
        campaignId = 1
      )

      res.map { result =>
        assert(result == Left(UpdateVariantError.UpdateVariantServerError(error)))
      }

    }
  }

  describe("Test reorderCampaignSteps") {

    it("should return list with just stepIdToBeReordered, if the currCampaignStepsOrder list is empty") {

      val stepIdToBeReordered = CampaignStepId(id = 32)

      val newOrder = CampaignStepService.reorderCampaignSteps(
        currCampaignStepsOrder = List(),
        stepIdToBeReordered = stepIdToBeReordered,
        newParentStepId = CampaignStepId(id = 21)
      )

      assert(newOrder == List(stepIdToBeReordered))

    }

    it("should reorder the stepIdToBeReordered at the head position, if newParentStepId is 0") {

      val stepIdToBeReordered = CampaignStepId(id = 99)

      val currCampaignStepsOrder = List(
        CampaignStepId(id = 67),
        CampaignStepId(id = 21),
        CampaignStepId(id = 77),
        stepIdToBeReordered,
        CampaignStepId(id = 90)
      )


      val newOrder = CampaignStepService.reorderCampaignSteps(
        currCampaignStepsOrder = currCampaignStepsOrder,
        stepIdToBeReordered = stepIdToBeReordered,
        newParentStepId = CampaignStepId(id = 0),
      )

      val expectedOrder = List(
        stepIdToBeReordered,
        CampaignStepId(id = 67),
        CampaignStepId(id = 21),
        CampaignStepId(id = 77),
        CampaignStepId(id = 90)
      )

      assert(newOrder == expectedOrder)

    }

    it("should reorder correctly when there are minimum steps in the list") {

      val stepIdToBeReordered = CampaignStepId(id = 67)

      val newParentStepId = CampaignStepId(id = 99)

      val newOrder = CampaignStepService.reorderCampaignSteps(
        currCampaignStepsOrder = List(stepIdToBeReordered, newParentStepId),
        stepIdToBeReordered = stepIdToBeReordered,
        newParentStepId = newParentStepId,
      )

      val expectedOrder = List(newParentStepId, stepIdToBeReordered)

      assert(newOrder == expectedOrder)

    }

    it("should reorder correctly, when newParentStepId is not 0") {

      val stepIdToBeReordered = CampaignStepId(id = 39)

      val newParentStepId = CampaignStepId(id = 99)

      val currCampaignStepsOrder = List(
        CampaignStepId(id = 451),
        stepIdToBeReordered,
        CampaignStepId(id = 21),
        CampaignStepId(id = 77),
        newParentStepId,
        CampaignStepId(id = 90),
      )

      val newOrder = CampaignStepService.reorderCampaignSteps(
        currCampaignStepsOrder = currCampaignStepsOrder,
        stepIdToBeReordered = CampaignStepId(id = 39),
        newParentStepId = CampaignStepId(id = 99),
      )

      val expectedOrder = List(
        CampaignStepId(id = 451),
        CampaignStepId(id = 21),
        CampaignStepId(id = 77),
        newParentStepId,
        stepIdToBeReordered,
        CampaignStepId(id = 90),
      )

      assert(newOrder == expectedOrder)

    }

  }

  describe("Test doesStepVariantsContainPrevSubMergeTag") {

    it("should returns false if the variants list is empty") {

      val res = CampaignStepService.doesStepVariantsContainPrevSubMergeTag(
        stepVariantsData = Seq()
      )

      assert(!res)

    }

    it("should returns false if the variants do not support {{previous_subject}} merge tag") {

      val linkedinViewProfileVariant = campaignStepVariantForScheduling.copy(
        step_data = CampaignStepData.LinkedinViewProfile()
      )

      val linkedinInmailDataVariant = campaignStepVariantForScheduling.copy(
        step_data = CampaignStepData.LinkedinInmailData(
          subject = Some("some linkedin subject"),
          body = "some linkedin body"
        )
      )

      val whatsappMessageDataVariant = campaignStepVariantForScheduling.copy(
        step_data = CampaignStepData.WhatsappMessageData(
          body = "message body"
        )
      )


      val linkedinViewProfileVariantRes = CampaignStepService.doesStepVariantsContainPrevSubMergeTag(
        stepVariantsData = Seq(linkedinViewProfileVariant.step_data)
      )

      val linkedinInmailDataVariantRes = CampaignStepService.doesStepVariantsContainPrevSubMergeTag(
        stepVariantsData = Seq(linkedinInmailDataVariant.step_data)
      )

      val whatsappMessageDataVariantRes = CampaignStepService.doesStepVariantsContainPrevSubMergeTag(
        stepVariantsData = Seq(whatsappMessageDataVariant.step_data)
      )

      assert(!linkedinViewProfileVariantRes)

      assert(!linkedinInmailDataVariantRes)

      assert(!whatsappMessageDataVariantRes)

    }

    it("should returns false if the variants supports {{previous_subject}} merge tag, but does not have it") {

      val autoEmailStepVariant = campaignStepVariantForScheduling.copy(
        step_data = CampaignStepData.AutoEmailStep(
          subject = "some auto email subject",
          body = "some auto email body"
        )
      )

      val manualEmailStepVariant = campaignStepVariantForScheduling.copy(
        step_data = CampaignStepData.ManualEmailStep(
          subject = "some manual email subject",
          body = "some manual email body"
        )
      )

      val autoEmailStepVariantRes = CampaignStepService.doesStepVariantsContainPrevSubMergeTag(
        stepVariantsData = Seq(autoEmailStepVariant.step_data)
      )

      val manualEmailStepVariantRes = CampaignStepService.doesStepVariantsContainPrevSubMergeTag(
        stepVariantsData = Seq(manualEmailStepVariant.step_data)
      )

      assert(!autoEmailStepVariantRes)

      assert(!manualEmailStepVariantRes)

    }

    it("should returns true if the variants supports and have {{previous_subject}} merge tag") {

      val autoEmailStepVariant = campaignStepVariantForScheduling.copy(
        step_data = CampaignStepData.AutoEmailStep(
          subject = "some {{previous_subject}} email subject",
          body = "some auto email body"
        )
      )

      val manualEmailStepVariant = campaignStepVariantForScheduling.copy(
        step_data = CampaignStepData.ManualEmailStep(
          subject = "some {{previous_subject}}  email subject",
          body = "some manual email body"
        )
      )

      val autoEmailStepVariantRes = CampaignStepService.doesStepVariantsContainPrevSubMergeTag(
        stepVariantsData = Seq(autoEmailStepVariant.step_data)
      )

      val manualEmailStepVariantRes = CampaignStepService.doesStepVariantsContainPrevSubMergeTag(
        stepVariantsData = Seq(manualEmailStepVariant.step_data)
      )

      assert(autoEmailStepVariantRes)

      assert(manualEmailStepVariantRes)

    }

  }

  describe("Test validateStepsWithPrevSubMergeTag") {

    it("should return true if the list is empty") {

      val (isValid, index) = CampaignStepService.validateStepsWithPrevSubMergeTag(
        steps = List()
      )

      assert(isValid && index == -1)

    }

    it("should return true if there are no variants with {{previous_subject}} merge tag") {

      val linkedinViewProfileStep = campaignStepWithChildren.copy(
        id = stepId.id,
        campaign_id = campaignId.id.toInt,
        step_type = CampaignStepType.LinkedinViewProfile,
        variants = Seq(
          campaignStepVariantForScheduling.copy(
            step_data = CampaignStepData.LinkedinViewProfile()
          )
        )
      )


      val (isValid, index) = CampaignStepService.validateStepsWithPrevSubMergeTag(
        steps = List(campaignAutoEmailStep, linkedinViewProfileStep)
      )

      assert(isValid && index == -1)

    }

    it(
      "should return true if there are variants with {{previous_subject}} merge tag, with a previous step to rely on"
    ) {

      val linkedinViewProfileStep = campaignStepWithChildren.copy(
        step_type = CampaignStepType.LinkedinViewProfile,
        variants = Seq(
          campaignStepVariantForScheduling.copy(
            step_data = CampaignStepData.LinkedinViewProfile()
          )
        )
      )

      val linkedinInMailStep = campaignStepWithChildren.copy(
        step_type = CampaignStepType.LinkedinInmail,
        variants = Seq(
          campaignStepVariantForScheduling.copy(
            step_data = CampaignStepData.LinkedinInmailData(
              subject = Some("Some linkedin subject"),
              body = "Some linkedin body"
            )
          )
        )
      )

      val manualEmailStepWithPrevSubMergeTag = campaignStepWithChildren.copy(
        step_type = CampaignStepType.ManualEmailStep,
        variants = Seq(
          campaignStepVariantForScheduling.copy(
            step_data = CampaignStepData.ManualEmailStep(
              subject = "{{previous_subject}}", body = "some body"
            )
          )
        )
      )

      val autoEmailStepWithPrevSubMergeTag = campaignStepWithChildren.copy(
        step_type = CampaignStepType.AutoEmailStep,
        variants = Seq(
          campaignStepVariantForScheduling.copy(
            step_data = CampaignStepData.AutoEmailStep(
              subject = "{{previous_subject}}", body = "some body"
            )
          )
        )
      )

      val autoEmailStep = campaignStepWithChildren.copy(
        step_type = CampaignStepType.AutoEmailStep,
        variants = Seq(
          campaignStepVariantForScheduling.copy(
            step_data = CampaignStepData.AutoEmailStep(
              subject = "some subject", body = "some body"
            )
          )
        )
      )

      val (isValid, index) = CampaignStepService.validateStepsWithPrevSubMergeTag(
        steps = List(
          linkedinInMailStep,
          autoEmailStep,
          linkedinViewProfileStep,
          autoEmailStepWithPrevSubMergeTag,
          manualEmailStepWithPrevSubMergeTag,
          autoEmailStepWithPrevSubMergeTag,
        )
      )

      assert(isValid && index == 3)

    }

  }

}
