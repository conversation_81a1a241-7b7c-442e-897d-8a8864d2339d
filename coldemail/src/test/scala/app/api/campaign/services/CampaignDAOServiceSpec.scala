package app.api.campaign.services

import api.accounts.TeamId
import api.accounts.models.OrgId
import api.campaigns.{CampaignDAO, CampaignEmailSettings, CampaignEmailSettingsUuid, CampaignEmailSettingsV2, CampaignIdAndTeamId, CampaignStepVariantDAO}
import api.campaigns.dao.{CampaignEmailSettingsDAO, CampaignSchedulingMetadataDAO}
import api.campaigns.models.CampaignEmailSettingsId
import api.campaigns.models.CampaignAddEmailChannelError.DbFailure
import api.campaigns.services.{CampaignDAOService, CampaignId}
import io.smartreach.esp.api.emails.EmailSettingId
import io.sr.billing_common.models.PlanID.TRIAL
import org.scalamock.scalatest.MockFactory
import org.scalatest.funspec.AnyFunSpec
import scalikejdbc.DBSession
import utils.dbutils.{DBUtils, DbAndSession}

import scala.util.{Failure, Success}

class CampaignDAOServiceSpec extends AnyFunSpec with MockFactory {

  val campaignDAO = mock[CampaignDAO]
  val campaignEmailSettingsDAO = mock[CampaignEmailSettingsDAO]
  val dbUtils = mock[DBUtils]
  val campaignSchedulingMetadataDAO = mock[CampaignSchedulingMetadataDAO]
  val campaignStepVariantDAO = mock[CampaignStepVariantDAO]

  val campaignDAOService = new CampaignDAOService(
    campaignDAO = campaignDAO,
    campaignEmailSettingsDAO = campaignEmailSettingsDAO,
    campaignSchedulingMetadataDAO = campaignSchedulingMetadataDAO,
    dbUtils = dbUtils,
    campaignStepVariantDAO = campaignStepVariantDAO
  )

  val campaignId = CampaignId(12)

  val data = List(CampaignEmailSettingsV2(
    sender_email_settings_id = 3,
    receiver_email_settings_id = 3
  ),
    CampaignEmailSettingsV2(
      sender_email_settings_id = 5,
      receiver_email_settings_id = 3
    ))

  val error = new Throwable("ERROR")

  val teamId = TeamId(34)
  val orgId = OrgId(44)
  
  val campaignEmailSettings = CampaignEmailSettings(
    campaign_id = campaignId,
    sender_email_setting_id = EmailSettingId(3),
    receiver_email_setting_id = EmailSettingId(3),
    team_id = teamId,
    uuid = CampaignEmailSettingsUuid("uuid"),
    id = CampaignEmailSettingsId(1),
    sender_email = "<EMAIL>",
    receiver_email = "<EMAIL>",
    max_emails_per_day_from_email_account = 100,
    signature = Some("emailsignature"),
    error = None,
    from_name = None
  )

  describe("updateEmailSettingsV2") {

    it("should fail when campaignEmailSettingsDAO.getCampaignSenderEmails fails") {

      (campaignEmailSettingsDAO.getCampaignSenderEmailsWithSession(_: CampaignId, _: TeamId))
        .expects(campaignId, teamId)
        .returning(Failure(error))

      val result = campaignDAOService.updateEmailSettingsV2(
        id = campaignId,
        data = data,
        team_id = teamId,
          planID = TRIAL,
        orgId = orgId
      )

      assert(result.get.isLeft)
      assert(result.get == Left(DbFailure(error)))
    }

    it("should fail when campaignEmailSettingsDAO.delete fails") {

      (campaignEmailSettingsDAO.getCampaignSenderEmailsWithSession(_: CampaignId, _: TeamId))
        .expects(campaignId, teamId)
        .returning(Success(List(campaignEmailSettings, campaignEmailSettings.copy(sender_email_setting_id = EmailSettingId(4), receiver_email_setting_id = EmailSettingId(4)))))

      (campaignEmailSettingsDAO.delete(_: CampaignId, _: TeamId, _: List[EmailSettingId]) )
        .expects(campaignId, teamId, List(EmailSettingId(4)))
        .returning(Failure(error))


      val result = campaignDAOService.updateEmailSettingsV2(
        id = campaignId,
        data = data,
        team_id = teamId,
          planID = TRIAL,
        orgId = orgId
      )

      assert(result.get.isLeft)
        assert(result.get == Left(DbFailure(error)))
    }

    it("should fail when campaignEmailSettingsDAO.updateReceiver fails") {

      (campaignEmailSettingsDAO.getCampaignSenderEmailsWithSession(_: CampaignId, _: TeamId))
        .expects(campaignId, teamId)
        .returning(Success(
          List(
          campaignEmailSettings.copy(receiver_email_setting_id = EmailSettingId(4)),
          campaignEmailSettings.copy(sender_email_setting_id = EmailSettingId(4), receiver_email_setting_id = EmailSettingId(4)),
          campaignEmailSettings.copy(sender_email_setting_id =  EmailSettingId(5), receiver_email_setting_id = EmailSettingId(4))
        )
        ))

      (campaignEmailSettingsDAO.delete(_: CampaignId, _: TeamId, _: List[EmailSettingId]))
        .expects(campaignId, teamId, List(EmailSettingId(4)))
        .returning(Success(1))

      (campaignEmailSettingsDAO.updateReceiver(_: CampaignId, _: TeamId, _: List[CampaignEmailSettingsV2]))
        .expects(campaignId, teamId, data)
        .returning(Failure(error))


      val result = campaignDAOService.updateEmailSettingsV2(
        id = campaignId,
        data = data,
        team_id = teamId,
          planID = TRIAL,
        orgId = orgId
      )

      assert(result.get.isLeft)
        assert(result.get == Left(DbFailure(error)))
    }


    it("should fail when campaignEmailSettingsDAO.addingCampaignEmailSetting fails") {

      (campaignEmailSettingsDAO.getCampaignSenderEmailsWithSession(_: CampaignId, _: TeamId))
        .expects(campaignId, teamId)
        .returning(Success(
          List(
            campaignEmailSettings.copy(sender_email_setting_id = EmailSettingId(4), receiver_email_setting_id = EmailSettingId(4)),
            campaignEmailSettings.copy(sender_email_setting_id = EmailSettingId(5), receiver_email_setting_id = EmailSettingId(4))
          )
        ))

      (campaignEmailSettingsDAO.delete(_: CampaignId, _: TeamId, _: List[EmailSettingId]))
        .expects(campaignId, teamId, List(EmailSettingId(4)))
        .returning(Success(1))

      (campaignEmailSettingsDAO.updateReceiver(_: CampaignId, _: TeamId, _: List[CampaignEmailSettingsV2]))
        .expects(campaignId, teamId, List(data(1)))
        .returning(Success(1))

      (campaignEmailSettingsDAO.addingCampaignEmailSettingWithSession(_: CampaignId, _: List[CampaignEmailSettingsV2], _: TeamId))
        .expects(campaignId, List(data.head), teamId )
        .returning(Failure(error))


      val result = campaignDAOService.updateEmailSettingsV2(
        id = campaignId,
        data = data,
        team_id = teamId,
          planID = TRIAL,
        orgId = orgId
      )

      assert(result.get.isLeft)
        assert(result.get == Left(DbFailure(error)))
    }

    it("success") {

      (campaignEmailSettingsDAO.getCampaignSenderEmailsWithSession(_: CampaignId, _: TeamId))
        .expects(campaignId, teamId)
        .returning(Success(
          List(
            campaignEmailSettings.copy(sender_email_setting_id = EmailSettingId(4), receiver_email_setting_id = EmailSettingId(4)),
            campaignEmailSettings.copy(sender_email_setting_id = EmailSettingId(5), receiver_email_setting_id = EmailSettingId(4))
          )
        ))

      (campaignEmailSettingsDAO.delete(_: CampaignId, _: TeamId, _: List[EmailSettingId]))
        .expects(campaignId, teamId, List(EmailSettingId(4)))
        .returning(Success(1))

      (campaignEmailSettingsDAO.updateReceiver(_: CampaignId, _: TeamId, _: List[CampaignEmailSettingsV2]))
        .expects(campaignId, teamId, List(data(1)))
        .returning(Success(1))

      (campaignEmailSettingsDAO.addingCampaignEmailSettingWithSession(_: CampaignId, _: List[CampaignEmailSettingsV2], _: TeamId))
        .expects(campaignId, List(data.head), teamId)
        .returning(Success(1))

      val result = campaignDAOService.updateEmailSettingsV2(
        id = campaignId,
        data = data,
        team_id = teamId,
          planID = TRIAL,
        orgId = orgId
      )
      assert(result.isSuccess)
        assert(result.get.isRight)
      assert(result.get == Right(Some(CampaignIdAndTeamId(campaign_id = campaignId.id, team_id = teamId.id))))
    }
  }

}
