package app.api.campaign

import org.apache.pekko.actor.ActorSystem
import org.apache.pekko.stream.Materializer
import api.AppConfig
import api.accounts.{Account, TeamId}
import api.accounts.models.{AccountId, OrgId}
import api.campaigns.models.CampaignEmailSettingsId
import api.campaigns.services.CampaignId
import api.campaigns.{CampaignEmailSettings, CampaignEmailSettingsUuid, CampaignSettings, CampaignStepVariant, CampaignWithStatsAndEmail}
import api.domain_health.DomainHealthCheckId
import api.emails.{EmailAddressHost, EmailSetting}
import api.spamtest.{CreateSpamTest, SpamTestType}
import app_services.blacklist_monitoring.models.{BlacklistCheckResult, BlacklistCheckStatus}
import db_test_spec.api.accounts.fixtures.NewAccountAndEmailSettingData
import db_test_spec.api.campaigns.fixtures.{CreateNewCampaignFixture, CreateStepForCampaignFixture, DefaultCampaignParametersFixtures}
import db_test_spec.api.emails.fixtures.DefaultEmailSettingParametersFixtures.defaultEmailSettingForm
import db_test_spec.api.emails.fixtures.EmailSettingFixtureForIntegrationTest
import db_test_spec.api.prospects.fixtures.ProspectFixtureForIntegrationTest
import db_test_spec.api.spamtest.SpamTest_TestDAO
import db_test_spec.api.{DbTestingBeforeAllAndAfterAll, InitialData}
import io.smartreach.esp.api.emails.EmailSettingId
import org.joda.time.DateTime
import org.scalatest.ParallelTestExecution
import play.api.libs.json.{JsValue, Json}
import play.api.libs.ws.ahc.AhcWSClient
import play.api.test.FakeRequest
import sr_scheduler.models.CampaignEmailPriority
import utils.emailvalidation.EmailValidationService

import scala.concurrent.duration.{Duration, SECONDS}
import scala.concurrent.{Await, Future}
import scala.util.{Failure, Success, Try}
import play.api.test.Helpers.*
import utils.SRLogger

class CampaignSenderDomainSpamTestIntegrationSpec extends DbTestingBeforeAllAndAfterAll {
    
    given logger: SRLogger = new SRLogger("CampaignSenderDomainSpamTestIntegrationSpec")

describe("Campaign Controller findInitialSpamTests"){
    it("should return domain results for campaignID present"){

        val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

        val emailList:List[String] = List("<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>")


        val emailSettingsList = emailList.map{ email =>

            EmailSettingFixtureForIntegrationTest.createEmailSetting(
                orgId = OrgId(initialData.account.org.id),
                accountId = AccountId(initialData.account.internal_id),
                teamId = TeamId(initialData.head_team_id),
                taId = initialData.account.teams.head.access_members.head.ta_id,
                emailSettingForm = Option(defaultEmailSettingForm.copy(
                    email = email
                ))
            ).get

        }

        val campaignEmailSettingsList: List[CampaignEmailSettings] = DefaultCampaignParametersFixtures.defaultCampaignEmailSettings(
            emailSettings = emailSettingsList,
            teamId = TeamId(initialData.head_team_id)
        )

        val campaignData :Future[CampaignWithStatsAndEmail] = for {
            createCampaign:CampaignWithStatsAndEmail <- CreateNewCampaignFixture.createNewCampaign(
            orgId = OrgId(initialData.account.org.id),
            accountId = AccountId(initialData.account.internal_id),
            teamId = TeamId(initialData.head_team_id),
            taId = initialData.account.teams.head.access_members.head.ta_id,
            campaignEmailSettingsId = CampaignEmailSettingsId(1L),
            senderEmailSettingId = EmailSettingId(1L),
            receiverEmailSettingId = EmailSettingId(1L),
            campaignEmailSettings = Some(campaignEmailSettingsList),
            ownerFirstName = initialData.account.first_name.get
            )


            campaignStep: CampaignStepVariant <- CreateStepForCampaignFixture.createAutoEmailStepForCampaign(
                 orgId = OrgId(initialData.account.org.id),
                 teamId = TeamId(initialData.head_team_id),
                 accountId = AccountId(initialData.account.internal_id),
                 taId = initialData.account.teams.head.access_members.head.ta_id,
                 campaignId = CampaignId(createCampaign.id)
             )


             createSpamTestList: List[CreateSpamTest] = emailSettingsList.map { es =>

                 CreateSpamTest(
                     test_name = s"${es.email}spamTesting",
                     test_type = SpamTestType.AUTH,
                     email_settings_id = es.id.get.emailSettingId,
                     email = es.email,
                     campaign_id = createCampaign.id,
                     step_id = campaignStep.step_id,
                     email_domain = EmailValidationService.getLowercasedNameAndDomainFromEmail(es.email)._2
                 )

             }


            _<-Future.fromTry(SpamTest_TestDAO.insertSpamTestsForTesting(
            createSpamTestList,
                teamId = TeamId(initialData.head_team_id)
            ))
             }yield{
            createCampaign
        }

        campaignData.flatMap { campaignDetails =>
            val campaignId: Long = campaignDetails.id
            println(campaignId)
            val url: String = s"/api/v2/domain_checks/spam_tests?cid=${campaignId}&tid=${initialData.head_team_id}"
            val request = FakeRequest(play.api.test.Helpers.GET, url)
              .withHeaders("X-API-KEY" -> initialData.teamUserLevelKey,
                  "Content-Type" -> "application/json")


            val final_result = play.api.test.Helpers.route(testApi, request).get 
            val status: Int = play.api.test.Helpers.status(final_result)
            val json: JsValue = play.api.test.Helpers.contentAsJson(final_result)
            final_result
              .map { res =>
                  println(json)
                  if (status == 200) {
                      val domainSpamTestData = (json \ "data").as[JsValue]
                      val status = (json \ "status").as[String]
                      val message = (json \ "message").as[String]
                      val domainResults = (json \ "data" \ "campaign_sender_email_spam_test").as[List[JsValue]]
                      val maxSpamTests = (json \ "data" \ "max_spam_tests").as[Int]
                      val currentSpamtestCount = (json \ "data" \ "current_spam_tests_done_count").as[Int]


                      assert(maxSpamTests ==100)

                  } else {
                      println(res)
                      assert(false)
                  }
              }
        }.recover {
            case ex: Exception =>
                println(s"Request failed: ${ex.getMessage}")
                assert(false)
        }







    }



    it("should return domain and results for the matching teamId"){


        val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

        val emailList1:List[String] = List("<EMAIL>","<EMAIL>")
        val emailList2:List[String]=List("<EMAIL>","<EMAIL>","<EMAIL>")


        val emailSettingsList1:List[EmailSetting] = emailList1.map{ email =>

            EmailSettingFixtureForIntegrationTest.createEmailSetting(
                orgId = OrgId(initialData.account.org.id),
                accountId = AccountId(initialData.account.internal_id),
                teamId = TeamId(initialData.head_team_id),
                taId = initialData.account.teams.head.access_members.head.ta_id,
                emailSettingForm = Option(defaultEmailSettingForm.copy(
                    email = email
                ))
            ).get

        }

        val emailSettingsList2:List[EmailSetting] = emailList2.map{ email =>

            EmailSettingFixtureForIntegrationTest.createEmailSetting(
                orgId = OrgId(initialData.account.org.id),
                accountId = AccountId(initialData.account.internal_id),
                teamId = TeamId(initialData.head_team_id),
                taId = initialData.account.teams.head.access_members.head.ta_id,
                emailSettingForm = Option(defaultEmailSettingForm.copy(
                    email = email
                ))
            ).get


        }


        val campaignEmailSettingsList1: List[CampaignEmailSettings] = DefaultCampaignParametersFixtures.defaultCampaignEmailSettings(
            emailSettings = emailSettingsList1,
            teamId = TeamId(initialData.head_team_id)
        )


        val campaignEmailSettingsList2: List[CampaignEmailSettings] = DefaultCampaignParametersFixtures.defaultCampaignEmailSettings(
            emailSettings = emailSettingsList2,
            teamId = TeamId(initialData.head_team_id)
        )

        for {
            createCampaign1:CampaignWithStatsAndEmail <- CreateNewCampaignFixture.createNewCampaign(
                orgId = OrgId(initialData.account.org.id),
                accountId = AccountId(initialData.account.internal_id),
                teamId = TeamId(initialData.head_team_id),
                taId = initialData.account.teams.head.access_members.head.ta_id,
                campaignEmailSettingsId = CampaignEmailSettingsId(1L),
                senderEmailSettingId = EmailSettingId(1L),
                receiverEmailSettingId = EmailSettingId(1L),
                campaignEmailSettings = Some(campaignEmailSettingsList1),
                ownerFirstName = initialData.account.first_name.get

            )

            createCampaign2:CampaignWithStatsAndEmail <- CreateNewCampaignFixture.createNewCampaign(
                orgId = OrgId(initialData.account.org.id),
                accountId = AccountId(initialData.account.internal_id),
                teamId = TeamId(initialData.head_team_id),
                taId = initialData.account.teams.head.access_members.head.ta_id,
                campaignEmailSettingsId = CampaignEmailSettingsId(1L),
                senderEmailSettingId = EmailSettingId(1L),
                receiverEmailSettingId = EmailSettingId(1L),
                campaignEmailSettings = Some(campaignEmailSettingsList2),
                ownerFirstName = initialData.account.first_name.get


            )




            campaignStep1: CampaignStepVariant <- CreateStepForCampaignFixture.createAutoEmailStepForCampaign(
                orgId = OrgId(initialData.account.org.id),
                teamId = TeamId(initialData.head_team_id),
                accountId = AccountId(initialData.account.internal_id),
                taId = initialData.account.teams.head.access_members.head.ta_id,
                campaignId = CampaignId(createCampaign1.id)
            )

            campaignStep2: CampaignStepVariant <- CreateStepForCampaignFixture.createAutoEmailStepForCampaign(
                orgId = OrgId(initialData.account.org.id),
                teamId = TeamId(initialData.head_team_id),
                accountId = AccountId(initialData.account.internal_id),
                taId = initialData.account.teams.head.access_members.head.ta_id,
                campaignId = CampaignId(createCampaign2.id)
            )




            createSpamTestList1: List[CreateSpamTest] = emailSettingsList1.map { es =>

                CreateSpamTest(
                    test_name = s"${es.email}spamTesting",
                    test_type = SpamTestType.AUTH,
                    email_settings_id = es.id.get.emailSettingId,
                    email = es.email,
                    campaign_id = createCampaign1.id,
                    step_id = campaignStep1.step_id,
                    email_domain = EmailValidationService.getLowercasedNameAndDomainFromEmail(es.email)._2
                )

            }


            _<-Future.fromTry(SpamTest_TestDAO.insertSpamTestsForTesting(
                spamTestList = createSpamTestList1,
                teamId = TeamId(initialData.head_team_id)
            ))


            createSpamTestList2: List[CreateSpamTest] = emailSettingsList1.map { es =>

                CreateSpamTest(
                    test_name = s"${es.email}spamTesting",
                    test_type = SpamTestType.AUTH,
                    email_settings_id = es.id.get.emailSettingId,
                    email = es.email,
                    campaign_id = createCampaign2.id,
                    step_id = campaignStep2.step_id,
                    email_domain = EmailValidationService.getLowercasedNameAndDomainFromEmail(es.email)._2
                )

            }


            _<-Future.fromTry(SpamTest_TestDAO.insertSpamTestsForTesting(
                spamTestList = createSpamTestList2,
                teamId = TeamId(initialData.head_team_id)
            ))
        }yield{
            Success(true)
        }

            val url: String = s"/api/v2/domain_checks/spam_tests?tid=${initialData.head_team_id}"

        val request = FakeRequest(play.api.test.Helpers.GET, url)
          .withHeaders("X-API-KEY" -> initialData.teamUserLevelKey,
              "Content-Type" -> "application/json")


        val final_result = play.api.test.Helpers.route(testApi, request).get
        val status: Int = play.api.test.Helpers.status(final_result)
        val json: JsValue = play.api.test.Helpers.contentAsJson(final_result)
        final_result
              .map { res =>
                  println(json)
                  if (status == 200) {
                      val domainSpamTestData = (json \ "data").as[JsValue]
                      val status = (json \ "status").as[String]
                      val message = (json \ "message").as[String]
                      val domainResults = (json \ "data" \ "campaign_sender_email_spam_test").as[List[JsValue]]
                      val maxSpamTests = (json \ "data" \ "max_spam_tests").as[Long]
                      val currentSpamtestCount = (json \ "data" \ "current_spam_tests_done_count").as[Long]


                      assert(maxSpamTests == 100)
                  } else {
                      println(res)
                      assert(false)
                  }
              }
        .recover {
            case ex: Exception =>
                println(s"Request failed: ${ex.getMessage}")
                assert(false)
        }
    }
}

    describe("Campaign Controller startSpamTest"){
        it("should success "){

            val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

            val emailList:List[String] = List("<EMAIL>","<EMAIL>")


            val emailSettingsList = emailList.map{ email =>

                EmailSettingFixtureForIntegrationTest.createEmailSetting(
                    orgId = OrgId(initialData.account.org.id),
                    accountId = AccountId(initialData.account.internal_id),
                    teamId = TeamId(initialData.head_team_id),
                    taId = initialData.account.teams.head.access_members.head.ta_id,
                    emailSettingForm = Option(defaultEmailSettingForm.copy(
                        email = email
                    ))
                ).get

            }



            val campaignEmailSettingsList: List[CampaignEmailSettings] = DefaultCampaignParametersFixtures.defaultCampaignEmailSettings(
                emailSettings = emailSettingsList,
                teamId = TeamId(initialData.account.teams.head.team_id)
            )



            val campaignData:Future[CampaignWithStatsAndEmail] = for {

                createCampaign:CampaignWithStatsAndEmail <- CreateNewCampaignFixture.createNewCampaign(
                    orgId = OrgId(initialData.account.org.id),
                    accountId = AccountId(initialData.account.internal_id),
                    teamId = TeamId(initialData.account.teams.head.team_id),
                    taId = initialData.account.teams.head.access_members.head.ta_id,
                    campaignEmailSettingsId = CampaignEmailSettingsId(1L),
                    senderEmailSettingId = EmailSettingId(1L),
                    receiverEmailSettingId = EmailSettingId(1L),
                    campaignEmailSettings = Some(campaignEmailSettingsList),
                    ownerFirstName = initialData.account.first_name.get


                )

                prospects <- Future.fromTry(ProspectFixtureForIntegrationTest.createUpdateOrAssignProspect(
                    accountId = AccountId(initialData.account.internal_id),
                    teamId = TeamId(initialData.account.teams.head.team_id),
                    account =  initialData.account,
                    campaignId = Some(CampaignId(createCampaign.id)),
                    generateProspectCountIfNoGivenProspect = 5
                ))


                campaignStep: CampaignStepVariant <- CreateStepForCampaignFixture.createAutoEmailStepForCampaign(
                    orgId = OrgId(initialData.account.org.id),
                    teamId = TeamId(initialData.account.teams.head.team_id),
                    accountId = AccountId(initialData.account.internal_id),
                    taId = initialData.account.teams.head.access_members.head.ta_id,
                    campaignId = CampaignId(createCampaign.id)
                )

            }yield{
                createCampaign
            }


            campaignData.flatMap { campaignDetails =>
                val campaignId: Long = campaignDetails.id
                println(campaignId)
                val cesId = campaignDetails.settings.campaign_email_settings.find(_.sender_email=="<EMAIL>").map(_.id.id).get
                println(cesId)
                val requestBody = Json.obj()
                val url: String = s"/api/v2/campaigns/${campaignId}/spam_tests?test_type=auth&cesid=$cesId&tid=${initialData.account.teams.head.team_id}"

                val request = FakeRequest(play.api.test.Helpers.POST, url)
                  .withHeaders("X-API-KEY" -> initialData.teamUserLevelKey,
                      "Content-Type" -> "application/json")
                  .withJsonBody(requestBody)


                val final_result = play.api.test.Helpers.route(testApi, request).get
                val status: Int = play.api.test.Helpers.status(final_result)
                val json: JsValue = play.api.test.Helpers.contentAsJson(final_result)
                final_result
                  .map { res =>
                      println(json)
                      if (status == 200) {
                          val domainSpamTestData = (json \ "data").as[JsValue]
                          val status = (json \ "status").as[String]
                          val message = (json \ "message").as[String]
                          val id = ((json \"data"\ "spam_tests").as[List[JsValue]].head \ "id").as[Long]
                          val testType = ((json \"data"\ "spam_tests").as[List[JsValue]].head \ "test_type").as[String]
                          val email = ((json \"data"\ "spam_tests").as[List[JsValue]].head \ "email").as[String]
                          val createdAt = ((json \"data"\ "spam_tests").as[List[JsValue]].head \ "created_at").as[String]
                          val emailSettingId = ((json \"data"\ "spam_tests").as[List[JsValue]].head \ "email_settings_id").as[Long]
                          val error = ((json \"data"\ "spam_tests").as[List[JsValue]].head \ "error").asOpt[String]

                          println(s"id:$id")

                          assert(testType == "auth")

                      } else {
                          println(res)
                          assert(false)
                      }
                  }
            }.recover {
                case ex: Exception =>
                    println(s"Request failed : ${ex.getMessage}")
                    assert(false)
            }
    }

    }



    describe("findDomainSpamTest API"){
        it("should success"){


            val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

            val emailList:List[String] = List("<EMAIL>")


            val emailSettingsList = emailList.map{ email =>

                EmailSettingFixtureForIntegrationTest.createEmailSetting(
                    orgId = OrgId(initialData.account.org.id),
                    accountId = AccountId(initialData.account.internal_id),
                    teamId = TeamId(initialData.account.teams.head.team_id),
                    taId = initialData.account.teams.head.access_members.head.ta_id,
                    emailSettingForm = Option(defaultEmailSettingForm.copy(
                        email = email
                    ))
                ).get

            }

            val campaignEmailSettingsList: List[CampaignEmailSettings] = DefaultCampaignParametersFixtures.defaultCampaignEmailSettings(
                emailSettings = emailSettingsList,
                teamId = TeamId(initialData.account.teams.head.team_id)
            )





            val campaignData :Future[CampaignWithStatsAndEmail] = for {
                createCampaign:CampaignWithStatsAndEmail <- CreateNewCampaignFixture.createNewCampaign(
                    orgId = OrgId(initialData.account.org.id),
                    accountId = AccountId(initialData.account.internal_id),
                    teamId = TeamId(initialData.account.teams.head.team_id),
                    taId = initialData.account.teams.head.access_members.head.ta_id,
                    campaignEmailSettingsId = CampaignEmailSettingsId(1L),
                    senderEmailSettingId = EmailSettingId(1L),
                    receiverEmailSettingId = EmailSettingId(1L),
                    campaignEmailSettings = Some(campaignEmailSettingsList),
                    ownerFirstName = initialData.account.first_name.get


                )


                campaignStep: CampaignStepVariant <- CreateStepForCampaignFixture.createAutoEmailStepForCampaign(
                    orgId = OrgId(initialData.account.org.id),
                    teamId = TeamId(initialData.account.teams.head.team_id),
                    accountId = AccountId(initialData.account.internal_id),
                    taId = initialData.account.teams.head.access_members.head.ta_id,
                    campaignId = CampaignId(createCampaign.id)
                )


                createSpamTestList: List[CreateSpamTest] = emailSettingsList.map { es =>

                    CreateSpamTest(
                        test_name = s"${es.email}spamTesting",
                        test_type = SpamTestType.AUTH,
                        email_settings_id = es.id.get.emailSettingId,
                        email = es.email,
                        campaign_id = createCampaign.id,
                        step_id = campaignStep.step_id,
                        email_domain = EmailValidationService.getLowercasedNameAndDomainFromEmail(es.email)._2
                    )

                }


                _<-Future.fromTry(SpamTest_TestDAO.insertSpamTestsForTesting(
                    spamTestList = createSpamTestList,
                    teamId = TeamId(initialData.account.teams.head.team_id)
                ))
            }yield{
                createCampaign
            }


            campaignData.flatMap { campaignDetails =>
                val campaignId: Long = campaignDetails.id
                println(campaignId)
                val requestBody = Json.obj()
                val url: String = s"/api/v2/domain_checks/rediffmail.com/spam_tests_report?tid=${initialData.account.teams.head.team_id}"
                val request = FakeRequest(play.api.test.Helpers.GET, url)
                  .withHeaders("X-API-KEY" -> initialData.teamUserLevelKey,
                      "Content-Type" -> "application/json")


                val final_result = play.api.test.Helpers.route(testApi, request).get
                val status: Int = play.api.test.Helpers.status(final_result)
                val json: JsValue = play.api.test.Helpers.contentAsJson(final_result)
                final_result
                  .map { res =>
                      println(json)
                      if (status == 200) {
                          val domainSpamTestData = (json \ "data").as[JsValue]
                          val message = (json \ "message").as[String]
                          val id = (json \"data"\ "spam_test" \ "id").as[Long]
                          val testType = (json \"data"\ "spam_test" \ "test_type").as[String]
                          val email = (json \"data"\ "spam_test" \ "email").as[String]
                          val createdAt = (json \"data"\ "spam_test" \ "created_at").as[String]
                          val emailSettingId = (json \"data"\ "spam_test" \ "email_settings_id").as[Long]
                          val error = (json \"data"\ "spam_test"\ "error").asOpt[String]
                          val mtResult = (json \ "data" \ "spam_test" \ "settings_results").as[JsValue]
                          val serviceProvider = (json \ "data" \ "service_provider").as[String]

                          println(s"id:$id")

                          assert(testType == "auth")
                      } else {
                          println(res)
                          assert(false)
                      }
                  }
            }.recover {
                case ex: Exception =>
                    println(s"Request failed: ${ex.getMessage}")
                    assert(false)
            }




        }
    }

    describe("findDomainBlacklistReport API test"){
        it("should success and return blacklist report"){

            val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

            EmailSettingFixtureForIntegrationTest.createEmailSetting(
                orgId = OrgId(initialData.account.org.id),
                accountId = AccountId(initialData.account.internal_id),
                teamId = TeamId(initialData.account.teams.head.team_id),
                taId = initialData.account.teams.head.access_members.head.ta_id,
                emailSettingForm = Option(defaultEmailSettingForm.copy(
                    email = "<EMAIL>"
                ))
            ).get

            val domainList: List[EmailAddressHost] = List(EmailAddressHost("testing.com"))

            val blacklistResult: BlacklistCheckResult =
                BlacklistCheckResult(
                    status = BlacklistCheckStatus.PASSED,
                    failureDescription = None,
                    fullResult = Json.obj(
                        "Blacklist Result" -> "Not found in any blacklist"
                    )
                )

            for{
                _ <- Future.fromTry(domainHealthCheckDAO.createAndUpdatePushedToQueue(domainList = domainList))

                _:DomainHealthCheckId <- Future.fromTry(domainHealthCheckDAO.updateBlacklistResult(domainBlacklistCheckResult = blacklistResult,domain = "testing.com",isFailedForCriticalBlacklist = false))
            } yield{
                true
            }

            val url: String = s"/api/v2/domain_checks/testing.com/blacklist_report?tid=${initialData.account.teams.head.team_id}"

            val request = FakeRequest(play.api.test.Helpers.GET, url)
              .withHeaders("X-API-KEY" -> initialData.teamUserLevelKey,
                  "Content-Type" -> "application/json")


            val final_result = play.api.test.Helpers.route(testApi, request).get
            val status: Int = play.api.test.Helpers.status(final_result)
            val json: JsValue = play.api.test.Helpers.contentAsJson(final_result)
            final_result
              .map { res =>
                  println(json)
                  if (status == 200) {
                      val domain = (json \ "data" \ "domain").as[String]
                      val blacklistReport = (json \ "data" \ "domain_blacklist_report").as[JsValue]
                      val teamId = (json \ "data" \ "team_id").as[Long]

                      assert(teamId == initialData.account.teams.head.team_id)
                  } else {
                      println(res)
                      assert(false)
                  }
              }
              .recover {
                  case ex: Exception =>
                      println(s"Request failed: ${ex.getMessage}")
                      assert(false)
              }


        }


        it("should fail for domain not present in teams email_settings"){
            val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get


            val domainList: List[EmailAddressHost] = List(EmailAddressHost("testing3.com"))

            val blacklistResult: BlacklistCheckResult =
                BlacklistCheckResult(
                    status = BlacklistCheckStatus.PASSED,
                    failureDescription = None,
                    fullResult = Json.obj(
                        "Blacklist Result" -> "Not found in any blacklist"
                    )
                )

            for{
                _ <- Future.fromTry(domainHealthCheckDAO.createAndUpdatePushedToQueue(domainList = domainList))

                _:DomainHealthCheckId <- Future.fromTry(domainHealthCheckDAO.updateBlacklistResult(domainBlacklistCheckResult = blacklistResult,domain = "testing3.com",isFailedForCriticalBlacklist = false))
            } yield{
                true
            }

            val url: String = s"/api/v2/domain_checks/testing3.com/blacklist_report?tid=${initialData.account.teams.head.team_id}"

            val request = FakeRequest(play.api.test.Helpers.GET, url)
              .withHeaders("X-API-KEY" -> initialData.teamUserLevelKey,
                  "Content-Type" -> "application/json")


            val final_result = play.api.test.Helpers.route(testApi, request).get
            val status: Int = play.api.test.Helpers.status(final_result)
            val json: JsValue = play.api.test.Helpers.contentAsJson(final_result)
            final_result
              .map { res =>
                  println(json)
                  if (status == 404) {


                      assert(true)
                  } else {
                      println(res)
                      assert(false)
                  }
              }



        }
    }

}
