package app.api.smartreach_credits

import api.accounts.TeamId
import api.accounts.models.{AccountId, OrgId}
import api.accounts.service.OrganizationService
import api.lead_finder.models.LeadFinderCreditDescription.MagicColumnCreditDescription
import api.lead_finder.models.{LeadFinderCreditDescription, SmartreachCreditType}
import api.leadfinder_credits.{CreditOperationOutput, SmartReachCreditService, SmartReachCreditServiceError, SmartreachCreditOperationData}
import db_test_spec.api.accounts.fixtures.NewAccountAndEmailSettingData
import db_test_spec.api.{DbTestingBeforeAllAndAfterAll, InitialData}
import org.scalamock.scalatest.AsyncMockFactory
import play.api.libs.ws.WSClient
import utils.SRLogger
import utils.testapp.Test_TaskPgDAO.organizationService

import scala.concurrent.{ExecutionContext, Future}


case class TestOperationOutput(

                              ) extends CreditOperationOutput

class SampleCreditConsumption extends SmartReachCreditService[
  SmartreachCreditOperationData.testOperationProps,
  TestOperationOutput
]{
  
  override def _organizationService: OrganizationService = organizationService

  override def operationForCreditsConsumption(operationData: SmartreachCreditOperationData.testOperationProps)(implicit ws: WSClient, ec: ExecutionContext, Logger: SRLogger): Future[Either[Exception, TestOperationOutput]] = {
    operation()
  }

  def operation(): Future[Either[Exception, TestOperationOutput]] = {
     Future.successful(Right(TestOperationOutput()))
  }

  def executeOperation(orgId: OrgId,
                       accountId: AccountId,
                       teamId: TeamId,
                       leadFinderCreditDescription: LeadFinderCreditDescription,
                       creditUsageType: SmartreachCreditType,
                       creditsToBeConsumed: Int,
                       operationData: SmartreachCreditOperationData.testOperationProps)(
                        implicit ws: WSClient,
                        ec: ExecutionContext,
                        Logger: SRLogger
                      ): Future[Either[SmartReachCreditServiceError, TestOperationOutput]] = {

    executeOperationByConsumingCredits(
      orgId= orgId,
      accountId= accountId,
      teamId= teamId,
      leadFinderCreditDescription= leadFinderCreditDescription,
      creditUsageType= creditUsageType,
      creditsToBeConsumed= creditsToBeConsumed,
      operationData= operationData
    )
  }

//  def doOperation()

}

class SmartReachCreditServiceTest extends DbTestingBeforeAllAndAfterAll with  AsyncMockFactory {

  lazy val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
  
  describe("Testing SampleCreditConsumption Billing and SmartReach Credit logic") {

    val sampleCreditConsumption = new SampleCreditConsumption

    it("should deduct the correct amount of credits on successful operation") {

      val creditsAvailable: Long = organizationService.getRemainingLeadFinderCredits(
        orgId = OrgId(initialData.account.org.id)
      ).get

      val creditsNeededForOperation = 100

      for {
        _ <- sampleCreditConsumption.executeOperation(
          orgId = OrgId(initialData.account.org.id),
          accountId = AccountId(initialData.account.internal_id),
          teamId = TeamId(initialData.account.teams.head.team_id),
          leadFinderCreditDescription = MagicColumnCreditDescription(description = "Test", prospectId = 1, columnId = 1),
          creditUsageType = SmartreachCreditType.MagicColumn,
          creditsToBeConsumed = creditsNeededForOperation,
          operationData = SmartreachCreditOperationData.testOperationProps()
        )
      } yield {
        val newRemainingCredits = organizationService.getRemainingLeadFinderCredits(
          orgId = OrgId(initialData.account.org.id)
        ).get
        assert(newRemainingCredits == creditsAvailable - creditsNeededForOperation)
      }
    }

    it("should not perform the operation if remaining credits are less than credits needed") {

      val creditsAvailable: Long = organizationService.getRemainingLeadFinderCredits(
        orgId = OrgId(initialData.account.org.id)
      ).get

      val creditsNeededForOperation = (creditsAvailable + 1).toInt

      val out =for {
        out: Either[SmartReachCreditServiceError, TestOperationOutput] <- sampleCreditConsumption.executeOperation(
          orgId = OrgId(initialData.account.org.id),
          accountId = AccountId(initialData.account.internal_id),
          teamId = TeamId(initialData.account.teams.head.team_id),
          leadFinderCreditDescription = MagicColumnCreditDescription(description = "Test", prospectId = 1, columnId = 1),
          creditUsageType = SmartreachCreditType.MagicColumn,
          creditsToBeConsumed = creditsNeededForOperation,
          operationData = SmartreachCreditOperationData.testOperationProps()
        )

        newRemainingCredits: Long <- Future.fromTry(organizationService.getRemainingLeadFinderCredits(
          orgId = OrgId(initialData.account.org.id)
        ))
      } yield {
        assert(newRemainingCredits == creditsAvailable)
        out
      }

       out.map {
        case Left(err) =>
          assert(err.getMessage == "Insufficient credits to perform the operation")
        case Right(_) =>
          assert(false)
      } recover {
         err =>
           println("Error:" + err)
           assert(false)
       }
    }


    it("should not deduct the amount of credits on unsuccessful operation") {

      val sampleCreditConsumptionFailure = new SampleCreditConsumption {
        override def operation(): Future[Either[Exception, TestOperationOutput]] = {
          Future.successful(Left(new Exception("Failed to execute operation")))
        }
      }

      val creditsAvailableBeforeOperation: Long = organizationService.getRemainingLeadFinderCredits(
        orgId = OrgId(initialData.account.org.id)
      ).get

      val creditsNeeded = 100

      for {
        operationOutput: Either[SmartReachCreditServiceError, TestOperationOutput] <- sampleCreditConsumptionFailure.executeOperation(
          orgId = OrgId(initialData.account.org.id),
          accountId = AccountId(initialData.account.internal_id),
          teamId = TeamId(initialData.account.teams.head.team_id),
          leadFinderCreditDescription = MagicColumnCreditDescription(description = "Test", prospectId = 1, columnId = 1),
          creditUsageType = SmartreachCreditType.MagicColumn,
          creditsToBeConsumed = creditsNeeded,
          operationData = SmartreachCreditOperationData.testOperationProps()
        )

        creditsAfterOperation <- Future.fromTry(organizationService.getRemainingLeadFinderCredits(
          orgId = OrgId(initialData.account.org.id)
        ))

        result <- operationOutput match {
          case Left(err) =>
            assert(err.getMessage == "Failed to execute operation")
            assert(creditsAfterOperation == creditsAvailableBeforeOperation)

          case Right(value) =>
            assert(false)
        }
      } yield {
        result
      }
    }
  }

}
