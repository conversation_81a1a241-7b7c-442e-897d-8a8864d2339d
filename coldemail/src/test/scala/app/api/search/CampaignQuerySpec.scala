package app.api.search

import api.AppConfig
import api.accounts.models.AccountProfileInfo
import api.accounts.{Account, AccountAccess, AccountMetadata, AccountType, AccountUuid, OrgCountData, OrgMetadata, OrgPlan, OrgSettings, OrganizationRole, OrganizationWithCurrentData}
import api.calendar_app.models.CalendarAccountData
import api.AppConfig
import api.campaigns.CampaignIdAndTeamId
import api.AppConfig
import api.accounts.models.AccountProfileInfo
import api.accounts.{Account, AccountAccess, AccountMetadata, AccountType, AccountUuid, OrgCountData, OrgMetadata, OrgPlan, OrgSettings, OrganizationRole, OrganizationWithCurrentData}
import api.calendar_app.models.CalendarAccountData
import api.campaigns.models.{CampaignStepType, CurrentStepStatusForScheduler, InactiveCampaignCheckType, SearchParams}
import api.campaigns.{CampaignIdAndTeamId, CampaignStepWithChildren}
import api.columns.FieldTypeEnum
import api.prospects.models.ProspectCategory
import api.prospects.{InferredQueryTimeline, SearchQuerySelectType}
import api.search.CampaignQuery.{campaignCallSettingSelect, getForOwnerIds, getModifiedQuerySQL}
import org.scalamock.scalatest.MockFactory
import org.scalatest.funspec.AnyFunSpec
import api.search.{CampaignQuery, SearchQuery, SearchQueryColumnOperator, SearchQueryMainClause, SearchQueryOperators, SearchQuerySubClause, SearchQuerySubFilterData}
import api.triggers.SRTriggerSource
import app.test_fixtures.accounts.OrgCountDataFixture
import app.test_fixtures.organizationa.{OrgMetadataFixture, OrgPlanFixture}
import org.joda.time.DateTime
import scalikejdbc.{SQLSyntax, scalikejdbcSQLInterpolationImplicitDef, sqls}
import sr_scheduler.CampaignStatus
import sr_scheduler.models.ChannelType
import utils.SRAppConfig
import utils.SRAppConfig.StopInactiveCampaign.stop_inactive_campaigns_after_days
import utils.SRLogger
import utils.cronjobs.email_setting_deletion.model.EmailSettingStatus
import utils.srlogging.sr.Logger

import scala.util.Success


class CampaignQuerySpec extends AnyFunSpec with MockFactory{

  val dateTime = DateTime.now()
  val search_params : SearchParams = SearchParams(
    name = None,
    sender_email_setting = None,
    receiver_email_setting = None,
    status = None,
    range = InferredQueryTimeline.Range.Before(dateTime = dateTime),
    is_first = true
  )

  describe("whereClauseFromSearchParams"){
    it("should return name clause"){
      val searchParams = search_params.copy(
        name = Some("name")
      )
      val expected_result =
        sqls"""
               AND c.name ILIKE ${searchParams.name.get}
               AND c.created_at AT TIME ZONE 'UTC' < ${dateTime}

         """.stripMargin
      val qry_str = CampaignQuery.whereClauseFromSearchParams(
        searchParams = searchParams
      )._1

      val split = expected_result.value.split(s"\\s+")
      val rhs = split.reduce((a1, a2) => {
        a1 + " " + a2
      })
      val lhs = qry_str.value.split("\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })

      assert(rhs == lhs)
    }

    it("should return receiver email setting clause") {
      val searchParams = search_params.copy(
        receiver_email_setting = Some("<EMAIL>"),
      )
      val expected_result =
        sqls"""
               AND EXISTS( select
                                 ces.id
                               from campaign_email_settings ces
                               JOIN email_settings receiver ON receiver.id = ces.receiver_email_setting_id AND ces.team_id = receiver.team_id
                               where
                             receiver.email ILIKE ${searchParams.receiver_email_setting.get}
                             and ces.campaign_id = c.id
                             and ces.team_id = c.team_id
                             and receiver.status = ${EmailSettingStatus.Active.toString}
                             LIMIT 1
                         )
               AND c.created_at AT TIME ZONE 'UTC' < ${dateTime}

         """.stripMargin
      val qry_str = CampaignQuery.whereClauseFromSearchParams(
        searchParams = searchParams
      )._1

      val split = expected_result.value.split(s"\\s+")
      val rhs = split.reduce((a1, a2) => {
        a1 + " " + a2
      })
      val lhs = qry_str.value.split("\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })

      assert(rhs == lhs)
    }

    it("should return sender email setting clause") {
      val searchParams = search_params.copy(
        sender_email_setting = Some("<EMAIL>"),
      )
      val expected_result =
        sqls"""
               AND EXISTS( select
                                  ces.id
                                from campaign_email_settings ces
                                JOIN email_settings sender ON sender.id = ces.sender_email_setting_id AND ces.team_id = sender.team_id
                                where
                              sender.email ILIKE ${searchParams.sender_email_setting.get}
                              and ces.campaign_id = c.id
                              and ces.team_id = c.team_id
                              and sender.status = ${EmailSettingStatus.Active.toString}
                              LIMIT 1
                          ) -- check if lower is required
               AND c.created_at AT TIME ZONE 'UTC' < ${dateTime}
         """.stripMargin
      val qry_str = CampaignQuery.whereClauseFromSearchParams(
        searchParams = searchParams
      )._1

      val split = expected_result.value.split(s"\\s+")
      val rhs = split.reduce((a1, a2) => {
        a1 + " " + a2
      })
      val lhs = qry_str.value.split("\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })

      assert(rhs == lhs)
    }

    it("should return status clause") {
      val searchParams = search_params.copy(
        status = Some(CampaignStatus.RUNNING),
      )
      val expected_result =
        sqls"""
               AND c.status ILIKE ${searchParams.status.get.toString}
               AND c.created_at AT TIME ZONE 'UTC' < ${dateTime}
         """.stripMargin
      val qry_str = CampaignQuery.whereClauseFromSearchParams(
        searchParams = searchParams
      )._1

      val split = expected_result.value.split(s"\\s+")
      val rhs = split.reduce((a1, a2) => {
        a1 + " " + a2
      })
      val lhs = qry_str.value.split("\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })

      assert(rhs == lhs)
    }

    it("should return status and name clause") {
      val searchParams = search_params.copy(
        status = Some(CampaignStatus.RUNNING),
        name = Some("name")
      )
      val expected_result =
        sqls"""
               AND c.name ILIKE ${searchParams.name.get}
               AND c.status ILIKE ${searchParams.status.get.toString}
               AND c.created_at AT TIME ZONE 'UTC' < ${dateTime}
         """.stripMargin
      val qry_str = CampaignQuery.whereClauseFromSearchParams(
        searchParams = searchParams
      )._1

      val split = expected_result.value.split(s"\\s+")
      val rhs = split.reduce((a1, a2) => {
        a1 + " " + a2
      })
      val lhs = qry_str.value.split("\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })

      assert(rhs == lhs)

    }

    it("should return status, receiver email address and name clause") {
      val searchParams = search_params.copy(
        status = Some(CampaignStatus.RUNNING),
        name = Some("name"),
        receiver_email_setting = Some("<EMAIL>")
      )
      val expected_result =
        sqls"""
               AND c.name ILIKE ${searchParams.name.get}
               AND EXISTS( select
                                 ces.id
                               from campaign_email_settings ces
                               JOIN email_settings receiver ON receiver.id = ces.receiver_email_setting_id AND ces.team_id = receiver.team_id
                               where
                             receiver.email ILIKE ${searchParams.receiver_email_setting.get}
                             and ces.campaign_id = c.id
                             and ces.team_id = c.team_id
                             and receiver.status = ${EmailSettingStatus.Active.toString}
                             LIMIT 1
                         )
               AND c.status ILIKE ${searchParams.status.get.toString}
               AND c.created_at AT TIME ZONE 'UTC' < ${dateTime}
         """.stripMargin
      val qry_str = CampaignQuery.whereClauseFromSearchParams(
        searchParams = searchParams
      )._1

      val split = expected_result.value.split(s"\\s+")
      val rhs = split.reduce((a1, a2) => {
        a1 + " " + a2
      })
      val lhs = qry_str.value.split("\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })

      assert(rhs == lhs)

    }

    it("should return created at less than and order by desc") {
      val searchParams = search_params.copy()
      val expected_result =
        sqls"""
               AND c.created_at AT TIME ZONE 'UTC' < ${dateTime}
         """.stripMargin

      val expected_order_by_resp =
        sqls" ORDER BY c.created_at desc"

      val response = CampaignQuery.whereClauseFromSearchParams(
        searchParams = searchParams
      )
      val qry_str = response._1
      val order_by = response._2

      val split = expected_result.value.split(s"\\s+")
      val rhs = split.reduce((a1, a2) => {
        a1 + " " + a2
      })
      val lhs = qry_str.value.split("\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })

      assert(rhs == lhs && expected_order_by_resp == order_by)

    }

    it("should return created at greater than and order by asc") {
      val searchParams = search_params.copy(
        range = InferredQueryTimeline.Range.After(dateTime = dateTime)
      )
      val expected_result =
        sqls"""
               AND c.created_at AT TIME ZONE 'UTC' > ${dateTime}
         """.stripMargin

      val expected_order_by_resp =
        sqls" ORDER BY c.created_at asc"

      val response = CampaignQuery.whereClauseFromSearchParams(
        searchParams = searchParams
      )
      val qry_str = response._1
      val order_by = response._2

      val split = expected_result.value.split(s"\\s+")
      val rhs = split.reduce((a1, a2) => {
        a1 + " " + a2
      })
      val lhs = qry_str.value.split("\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })

      assert(rhs == lhs && expected_order_by_resp == order_by)

    }

    it("should return all clauses"){
      val searchParams = search_params.copy(
        name = Some("name"),
        sender_email_setting = Some("<EMAIL>"),
        receiver_email_setting = Some("<EMAIL>"),
        status = Some(CampaignStatus.RUNNING),
      )
      val expected_result =
        sqls"""
               AND c.name ILIKE ${searchParams.name.get}
               AND EXISTS( select
                                 ces.id
                               from campaign_email_settings ces
                               JOIN email_settings receiver ON receiver.id = ces.receiver_email_setting_id AND ces.team_id = receiver.team_id
                               where
                             receiver.email ILIKE ${searchParams.receiver_email_setting.get}
                             and ces.campaign_id = c.id
                             and ces.team_id = c.team_id
                             and receiver.status = ${EmailSettingStatus.Active.toString}
                             LIMIT 1
                         )
               AND EXISTS( select
                                  ces.id
                                from campaign_email_settings ces
                                JOIN email_settings sender ON sender.id = ces.sender_email_setting_id AND ces.team_id = sender.team_id
                                where
                              sender.email ILIKE ${searchParams.sender_email_setting.get}
                              and ces.campaign_id = c.id
                              and ces.team_id = c.team_id
                              and sender.status = ${EmailSettingStatus.Active.toString}
                              LIMIT 1
                          ) -- check if lower is required
               AND c.status ILIKE ${searchParams.status.get.toString}
               AND c.created_at AT TIME ZONE 'UTC' < ${dateTime}
         """.stripMargin
      val qry_str = CampaignQuery.whereClauseFromSearchParams(
        searchParams = searchParams
      )._1

      val split = expected_result.value.split(s"\\s+")
      val rhs = split.reduce((a1, a2) => {
        a1 + " " + a2
      })
      val lhs = qry_str.value.split("\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })
      assert(rhs == lhs)
    }

  }


  describe("campaignEmailSettingsSelect") {
    it("should add a.active in error check") {

      val expected_result =
        sqls"""
              |     (
              |      SELECT
              |        json_agg(
              |          json_build_object(
              |            'id', ces.id,
              |            'campaign_id', ces.campaign_id,
              |            'sender_email_setting_id', ces.sender_email_setting_id,
              |            'receiver_email_setting_id', ces.receiver_email_setting_id,
              |            'team_id', ces.team_id,
              |            'uuid', ces.uuid,
              |            'sender_email', sender.email,
              |            'receiver_email', receiver.email,
              |            'from_name', sender.sender_name,
              |            'signature', sender.signature,
              |            'max_emails_per_day_from_email_account', sender.quota_per_day,
              |            'error', (
              |              CASE WHEN (sender.paused_till IS NOT NULL
              |                AND sender.paused_till > now()) THEN
              |                sender.error
              |              WHEN (receiver.paused_till IS NOT NULL
              |                AND receiver.paused_till > now()) THEN
              |                receiver.error
              |              WHEN (sender.is_under_review IS TRUE
              |                OR receiver.is_under_review IS TRUE) THEN
              |                'Your email account is under manual review. Please contact support.'
              |
              |          WHEN (a.active IS FALSE) THEN
              |            concat('Campaign owner''s (', a.email, ') account has been deactivated by your team''s admin. Please change the owner to restart sending.')
              |
              |              WHEN (sender_owner.active IS FALSE) THEN
              |                concat('You are using an email account (', sender.email, ') whose owner''s SmartReach.io account (', sender_owner.email, ') has been deactivated by your team''s admin. Please change the email account''s owner to restart sending from this campaign.')
              |              WHEN (receiver_owner.active IS FALSE) THEN
              |                concat('You are using an email account (', receiver.email, ') whose owner''s SmartReach.io account (', receiver_owner.email, ') has been deactivated by your team''s admin. Please change the email account''s owner to restart sending from this campaign.')
              |              WHEN (sender_domain_check.is_in_spam_blacklist IS TRUE) THEN
              |                'Your sending email domain is found in a global spam blacklist. Please check the status by going to Settings -> Team Settings -> Domain Health.'
              |              ELSE
              |                NULL
              |              END))) AS campaign_email_settings
              |      FROM
              |        campaign_email_settings ces
              |        JOIN email_settings sender ON sender.id = ces.sender_email_setting_id
              |          AND ces.team_id = sender.team_id
              |        JOIN email_settings receiver ON receiver.id = ces.receiver_email_setting_id
              |          AND ces.team_id = receiver.team_id
              |      LEFT JOIN accounts sender_owner ON sender.account_id = sender_owner.id
              |      LEFT JOIN accounts receiver_owner ON receiver.account_id = receiver_owner.id
              |      LEFT JOIN domain_health_checks sender_domain_check ON sender_domain_check.domain = sender.email_address_host
              |    WHERE
              |      ces.campaign_id = c.id
              |      and sender.status = ${EmailSettingStatus.Active.toString}
              |      and receiver.status = ${EmailSettingStatus.Active.toString}
              |      AND ces.team_id = c.team_id) AS campaign_email_settings
            """.stripMargin.value.split(s"\\s+").reduce((s1, s2) => {
          s1 + " " + s2
        })


      val result = CampaignQuery.campaignEmailSettingsSelect().value.split(s"\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })

      assert(expected_result == result)
    }

  }

  describe("campaignSettings related Select") {

    it("campaignCallSettingSelect"){

      val expected_result =
        """
              |(
              |        SELECT json_agg(
              |          json_build_object(
              |            'channel_setting_uuid', ccs.channel_settings_uuid,
              |            'team_id', c.team_id,
              |            'phone_number', cs.phone_number,
              |            'first_name', cs.first_name,
              |            'last_name',  cs.last_name
              |          )) AS campaign_call_settings
              |        FROM call_settings cs
              |        LEFT JOIN campaign_channel_settings ccs on (
              |         ccs.channel_settings_uuid = cs.uuid AND ccs.channel_type = ?)
              |         WHERE ccs.team_id = c.team_id AND ccs.campaign_id = c.id) AS campaign_call_settings
              |
          """.stripMargin.split(s"\\s+").reduce((s1, s2) => {
          s1 + " " + s2
        })

      val result = CampaignQuery.campaignCallSettingSelect().value.split(s"\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })

      assert(result == expected_result)

    }

    it("campaignWhatsappSettingSelect") {

      val expected_result =
        """
          |(
          |        SELECT json_agg(
          |          json_build_object(
          |            'channel_setting_uuid', ccs.channel_settings_uuid,
          |            'team_id', c.team_id,
          |            'phone_number', ws.whatsapp_number,
          |            'first_name', ws.first_name,
          |            'last_name',  ws.last_name
          |          )) AS campaign_whatsapp_settings
          |        FROM whatsapp_settings ws
          |        LEFT JOIN campaign_channel_settings ccs on ( ccs.channel_settings_uuid = ws.uuid AND ccs.channel_type = ?)
          |        WHERE ccs.team_id = c.team_id AND ccs.campaign_id = c.id) AS campaign_whatsapp_settings
          """.stripMargin.split(s"\\s+").reduce((s1, s2) => {
          s1 + " " + s2
        })

      val result = CampaignQuery.campaignWhatsappSettingSelect().value.split(s"\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })

      assert(result == expected_result)

    }


    it("campaignSmsSettingSelect") {

      val expected_result =
        """
          |(
          |        SELECT json_agg(
          |          json_build_object(
          |            'channel_setting_uuid', ccs.channel_settings_uuid,
          |            'team_id', c.team_id,
          |            'phone_number', ss.phone_number,
          |            'first_name', ss.first_name,
          |            'last_name',  ss.last_name
          |          )) AS campaign_sms_settings
          |        FROM sms_settings ss
          |        LEFT JOIN campaign_channel_settings ccs on ( ccs.channel_settings_uuid = ss.uuid AND ccs.channel_type = ?)
          |        WHERE ccs.team_id = c.team_id AND ccs.campaign_id = c.id) AS campaign_sms_settings
          """.stripMargin.split(s"\\s+").reduce((s1, s2) => {
          s1 + " " + s2
        })

      val result = CampaignQuery.campaignSmsSettingSelect().value.split(s"\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })

      assert(result == expected_result)

    }

    it("campaignLinkedinSettingSelect") {

      val expected_result =
        """
          |(
          |        SELECT json_agg(
          |          json_build_object(
          |            'channel_setting_uuid', ccs.channel_settings_uuid,
          |            'team_id', c.team_id,
          |            'email', ls.email,
          |            'first_name', ls.first_name,
          |            'last_name',  ls.last_name,
          |            'linkedin_profile_url', ls.profile_url,
          |            'automation_enabled', ls.captain_data_account_id IS NOT NULL
          |          )) AS campaign_linkedin_settings
          |        FROM linkedin_settings ls
          |        LEFT JOIN campaign_channel_settings ccs on ( ccs.channel_settings_uuid = ls.uuid AND ccs.channel_type = ?)
          |        WHERE ccs.team_id = c.team_id AND ccs.campaign_id = c.id) AS campaign_linkedin_settings
          """.stripMargin.split(s"\\s+").reduce((s1, s2) => {
          s1 + " " + s2
        })

      val result = CampaignQuery.campaignLinkedinSettingSelect().value.split(s"\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })

      assert(result == expected_result)

    }

  }


  describe("getQueryForInactiveCampaignsForStopping") {
    it("should give query for inactive campaigns after 30 days") {

      val expected_result =
        sqls"""
              |SELECT
              |        o.name as org_name,
              |        o.created_at AS org_created_at,
              |        a.email as campaign_owner_email,
              |        c.id AS cid,
              |        c.created_at AS campaign_created_at,
              |          c.team_id,
              |          c.name AS campaign_name,
              |          t.name AS team_name
              |        FROM
              |          campaigns c
              |          JOIN teams t ON c.team_id = t.id
              |        JOIN accounts a ON a.id = c.account_id
              |        JOIN organizations o ON o.id = a.org_id
              |      WHERE
              |
              |                c.id = ?
              |               AND c.team_id = ?
              |         AND c.status_changed_at < now() - interval '30 days'
              |        AND (
              |        c.last_email_sent_at < now() - interval '30 days'
              |        OR  c.last_email_sent_at IS NULL
              |         )
              |
              |        AND (
              |        last_checked_for_completed_campaign_cron_at IS NULL OR
              |        last_checked_for_completed_campaign_cron_at < now() - interval '24 hours'
              |        )
              |
              |        AND NOT EXISTS (
              |          SELECT
              |            *
              |          FROM
              |            campaigns_prospects cp
              |            JOIN prospect_categories_custom cat ON cat.id = cp.prospect_category_id_custom
              |          WHERE
              |            cp.campaign_id = c.id
              |            AND cp.active
              |            AND NOT cat.text_id = ?
              |            AND (
              |              cp.unpaused_by_admin
              |              OR (
              |                -- email-specific checks to stop campaign
              |                NOT cp.replied
              |                AND NOT cp.opted_out
              |                AND NOT cp.out_of_office
              |                AND NOT cp.auto_reply
              |                AND NOT cp.bounced
              |                AND NOT cp.completed
              |                AND NOT cp.paused
              |                AND (
              |                 cp.will_resume_at is null OR
              |                  cp.will_resume_at > now()
              |                )
              |                -- TODO: linkedin-specific checks to stop campaign - ideally any such case should result in cp.completed to be true
              |              )
              |            )
              |          AND NOT (
              |            cp.last_contacted_at > now() - interval '${SQLSyntax.createUnsafely(SRAppConfig.StopInactiveCampaign.stop_inactive_campaigns_after_days.toString)} days' OR
              |            cp.last_contacted_at is null
              |          )
              |        AND NOT ((
              |            cp.latest_task_done_or_skipped_at > now() - interval '${SQLSyntax.createUnsafely(SRAppConfig.StopInactiveCampaign.stop_inactive_campaigns_after_days.toString)} days'
              |
              |            AND cp.current_step_status_for_scheduler not in  (?)
              |           )
              |        OR cp.current_step_status_for_scheduler in  (?)
              |        )
              |        );
            """.stripMargin.value.split(s"\\s+").reduce((s1, s2) => {
          s1 + " " + s2
        })



      val result = CampaignQuery.getQueryForInactiveCampaignsForStopping(InactiveCampaignCheckType.ThirtyDayCheck,
        campaignIdAndTeamId =  CampaignIdAndTeamId(
          campaign_id = 982,
          team_id = 59),
        stepTypesInCampaign = Set()).value.split(s"\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })
      println(result)

      assert(expected_result == result)
    }



    it("should give query for campaign with no prospects to send to with email step") {
      val expected_result =
        sqls"""
              |SELECT
              |          o.name as org_name,
              |          o.created_at AS org_created_at,
              |          a.email as campaign_owner_email,
              |          c.id AS cid,
              |          c.created_at AS campaign_created_at,
              |          c.team_id,
              |          c.name AS campaign_name,
              |          t.name AS team_name
              |        FROM
              |          campaigns c
              |          JOIN teams t ON c.team_id = t.id
              |          JOIN accounts a ON a.id = c.account_id
              |          JOIN organizations o ON o.id = a.org_id
              |        WHERE
              |
              |                c.id = ?
              |               AND c.team_id = ?

              |
              |        AND NOT EXISTS (
              |          SELECT
              |            *
              |          FROM
              |            campaigns_prospects cp
              |            JOIN prospect_categories_custom cat ON cat.id = cp.prospect_category_id_custom
              |          WHERE
              |            cp.campaign_id = c.id
              |            AND cp.active
              |            AND NOT cat.text_id = ?
              |            AND NOT cp.invalid_email
              |            AND (
              |              cp.unpaused_by_admin
              |              OR (
              |                -- email-specific checks to stop campaign
              |                NOT cp.replied
              |                AND NOT cp.opted_out
              |                AND NOT cp.out_of_office
              |                AND NOT cp.auto_reply
              |                AND NOT cp.bounced
              |                AND NOT cp.completed
              |                AND NOT cp.paused
              |                AND (
              |                cp.will_resume_at is null OR
              |                (
              |                cp.will_resume_at > now()
              |                AND cp.will_resume_at < now() + interval '${SQLSyntax.createUnsafely(AppConfig.CampaignSchedulingMetadataConstants.refresh_after_hours.toString)} hours'
              |                )
              |               )
              |                -- TODO: linkedin-specific checks to stop campaign - ideally any such case should result in cp.completed to be true
              |              )
              |            )
              |            AND NOT cp.to_check
              |        );
            """.stripMargin.value.split(s"\\s+").reduce((s1, s2) => {
          s1 + " " + s2
        })


      val result = CampaignQuery.getQueryForInactiveCampaignsForStopping(
        InactiveCampaignCheckType.OnHoldCheck,
        CampaignIdAndTeamId(
          campaign_id = 982,
          team_id = 59),
        stepTypesInCampaign = Set(CampaignStepType.AutoEmailStep)
      ).value.split(s"\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })
      println(result)

      assert(expected_result == result)
    }
  }

  describe("getQuerySQL") {

    val teamId: Long = 2
    val orgId = 2

    val orgSettings = OrgSettings(
      enable_ab_testing = false,
      disable_force_send = false,
      bulk_sender = false,
      allow_2fa = false,
      show_2fa_setting = false,
      enforce_2fa = false,
      allow_native_crm_integration = false,
        agency_option_allow_changing = false,
        agency_option_show = false
    )

    val accountMetadata = AccountMetadata(
      is_profile_onboarding_done = None
    )

    val orgMetadata = OrgMetadataFixture.orgMetadataFixture2

    val orgCountData: OrgCountData = OrgCountDataFixture.orgCountData_default

    val orgPlan = OrgPlanFixture.orgPlanFixture

    val org = OrganizationWithCurrentData(
      id = 1,
      name = "SR",
      owner_account_id = 2,
      counts = orgCountData,
      settings = orgSettings,
      plan = orgPlan,
      is_agency = true,
      trial_ends_at = DateTime.now().plusDays(100),
      error = None,
      error_code = None,
      paused_till = None,
      errors = Seq(),
      warnings = Seq(),
      via_referral = false,
      org_metadata = orgMetadata
    )

    val profile = AccountProfileInfo(
      first_name = "Test",
      last_name = "Unit",
      company = Some("SmartReach"),
      timezone = None,
      country_code = None,
      mobile_country_code = None,
      mobile_number = None,
      onboarding_phone_number = None,
      twofa_enabled = false,
      has_gauthenticator = false,
      weekly_report_emails = None,
      scheduled_for_deletion_at = None
    )

    val accountAdmin = Account(
      id = AccountUuid("account_uuid"),
      internal_id = 2,
      email = "<EMAIL>",
      email_verification_code = None,
      email_verification_code_created_at = None,
      created_at = DateTime.now().minusDays(1000),
      first_name = Some("Test"),
      last_name = Some("Unit"),
      company = Some("SmartReach"),
      timezone = None,
      profile = profile,
      org_role = Some(OrganizationRole.OWNER),
      teams = Seq(),
      account_type = AccountType.AGENCY,
      org = org,
      active = true,
      email_notification_summary = "dSFA",
      account_metadata = accountMetadata,
      email_verified = true,
      signupType = None,
      account_access = AccountAccess(
        inbox_access = false
      ),
      calendar_account_data = None
    )



    val logger = new SRLogger("query")

    it("Should return the entire query") {
      val data = SearchQuery(
        query = Some(
          SearchQueryMainClause(
            search = Some("test multiple sending"), owner_ids = Seq(90), clause = "", filters = Seq()
          )
        ),
        page = Option(1),
        sort = None,
        is_campaign = None,
        older_than = None,
        newer_than = None
      )

      val res = CampaignQuery.getQuerySQL(
        account = accountAdmin,
        teamId = teamId,
        orgId = orgId,
        data = data,
        Logger = logger,
        permittedAccountIds = Seq(2L),
        fetchType = SearchQuerySelectType.FULL_OBJECT
      )



      val expectedResult =
        s"""
          | SELECT
          |   a.email AS owner_email,
          |   a.id AS owner_account_id,
          |   CONCAT(a.first_name, ' ',  a.last_name) AS owner_name,
          |
          |   COALESCE(json_agg(trig) FILTER (WHERE trig.id IS NOT NULL), '[]') AS triggers,
          |   c.id,
          |   c.account_id,
          |   c.name,
          |   c.status,
          |   c.is_archived,
          |   c.timezone,
          |   c.daily_from_time,
          |   c.daily_till_time,
          |   c.days_preference,
          |   c.ai_sequence_status,
          |   c.ai_generation_context,
          |   c.created_at,
          |   c.head_step_id,
          |   c.last_scheduled_at,
          |   c.email_priority,
          |   c.opt_out_msg,
          |   c.opt_out_is_text,
          |   c.team_id,
          |   c.shared_with_team,
          |   c.ta_id,
          |   c.max_emails_per_day,
          |   c.mark_completed_after_days,
          |   c.open_tracking_enabled,
          |   c.warmup_started_at,
          |   c.warmup_starting_email_count,
          |   c.warmup_length_in_days,
          |   c.click_tracking_enabled,
          |   c.latest_email_scheduled_at,
          |   c.append_followups,
          |   c.status_changed_at,
          |   c.will_delete,
          |   c.will_delete_at,
          |   c.enable_email_validation,
          |   c.ab_testing_enabled,
          |   c.add_prospect_to_dnc_on_opt_out,
          |   c.schedule_start_at,
          |   c.schedule_start_at_tz,
          |   c.last_analysed_at,
          |   c.pushed_to_queue_for_analysis_at,
          |   c.sending_holiday_calendar_id,
          |   -- c.next_to_be_scheduled_at,
          |   c.last_email_sent_at,
          |   c.latest_campaign_send_start_reports_id,
          |   c.pushed_to_queue_for_analysing_start_report_at,
          |   c.in_queue_for_analyzing_start_report,
          |   c.last_pushed_for_preemailvalidation_at,
          |   c.uuid,
          |   c.calendar_event_type_id,
          |   c.calendar_event_type_slug,
          |   c.calendar_is_individual,
          |   c.calendar_selected_user_id,
          |   c.calendar_selected_username_slug,
          |   c.calendar_smartreach_account_id,
          |   c.calendar_team_id,
          |   c.send_plain_text_email,
          |   c.campaign_type,
          |   c.calendar_team_slug,
          |   c.sending_mode,
          |   (
          |      SELECT
          |        json_agg(
          |          json_build_object(
          |            'id', ces.id,
          |            'campaign_id', ces.campaign_id,
          |            'sender_email_setting_id', ces.sender_email_setting_id,
          |            'receiver_email_setting_id', ces.receiver_email_setting_id,
          |            'team_id', ces.team_id,
          |            'uuid', ces.uuid,
          |            'sender_email', sender.email,
          |            'receiver_email', receiver.email,
          |            'from_name', sender.sender_name,
          |            'signature', sender.signature,
          |            'max_emails_per_day_from_email_account', sender.quota_per_day,
          |            'error', (
          |              CASE WHEN (sender.paused_till IS NOT NULL
          |                AND sender.paused_till > now()) THEN
          |                sender.error
          |              WHEN (receiver.paused_till IS NOT NULL
          |                AND receiver.paused_till > now()) THEN
          |                receiver.error
          |              WHEN (sender.is_under_review IS TRUE
          |                OR receiver.is_under_review IS TRUE) THEN
          |                'Your email account is under manual review. Please contact support.'
          |              WHEN (a.active IS FALSE) THEN
          |                concat('Campaign owner''s (', a.email, ') account has been deactivated by your team''s admin. Please change the owner to restart sending.')
          |              WHEN (sender_owner.active IS FALSE) THEN
          |                concat('You are using an email account (', sender.email, ') whose owner''s SmartReach.io account (', sender_owner.email, ') has been deactivated by your team''s admin. Please change the email account''s owner to restart sending from this campaign.')
          |              WHEN (receiver_owner.active IS FALSE) THEN
          |                concat('You are using an email account (', receiver.email, ') whose owner''s SmartReach.io account (', receiver_owner.email, ') has been deactivated by your team''s admin. Please change the email account''s owner to restart sending from this campaign.')
          |              WHEN (sender_domain_check.is_in_spam_blacklist IS TRUE) THEN
          |                'Your sending email domain is found in a global spam blacklist. Please check the status by going to Settings -> Team Settings -> Domain Health.'
          |              ELSE
          |                NULL
          |              END))) AS campaign_email_settings
          |      FROM
          |        campaign_email_settings ces
          |        JOIN email_settings sender ON sender.id = ces.sender_email_setting_id
          |          AND ces.team_id = sender.team_id
          |        JOIN email_settings receiver ON receiver.id = ces.receiver_email_setting_id
          |          AND ces.team_id = receiver.team_id
          |      LEFT JOIN accounts sender_owner ON sender.account_id = sender_owner.id
          |      LEFT JOIN accounts receiver_owner ON receiver.account_id = receiver_owner.id
          |      LEFT JOIN domain_health_checks sender_domain_check ON sender_domain_check.domain = sender.email_address_host
          |    WHERE
          |      ces.campaign_id = c.id
          |      and sender.status = ?
          |      and receiver.status = ?
          |      AND ces.team_id = c.team_id) AS campaign_email_settings
          |       ,
          |   (
          |            SELECT
          |              json_agg(json_build_object('tag_id', tlfc.id, 'tag', tlfc.tag, 'tag_uuid', tlfc.uuid)) AS camtags
          |            FROM
          |              tags_list_for_campaigns tlfc
          |              JOIN tags_campaigns ON (
          |                  tlfc.id = tags_campaigns.tag_id
          |                  AND tlfc.team_id = ?
          |                  AND tags_campaigns.campaign_id = c.id
          |                  AND tags_campaigns.team_id = ?
          |              )
          |					) AS ctags
          |      ,
          |    (
          |        SELECT json_agg(
          |          json_build_object(
          |            'channel_setting_uuid', ccs.channel_settings_uuid,
          |            'team_id', c.team_id,
          |            'email', ls.email,
          |            'first_name', ls.first_name,
          |            'last_name',  ls.last_name,
          |            'linkedin_profile_url', ls.profile_url,
          |            'automation_enabled', ls.captain_data_account_id IS NOT NULL
          |          )) AS campaign_linkedin_settings
          |        FROM linkedin_settings ls
          |        LEFT JOIN campaign_channel_settings ccs on ( ccs.channel_settings_uuid = ls.uuid AND ccs.channel_type = ?)
          |        WHERE ccs.team_id = c.team_id AND ccs.campaign_id = c.id) AS campaign_linkedin_settings
          |      ,
          |     (
          |        SELECT json_agg(
          |          json_build_object(
          |            'channel_setting_uuid', ccs.channel_settings_uuid,
          |            'team_id', c.team_id,
          |            'phone_number', cs.phone_number,
          |            'first_name', cs.first_name,
          |            'last_name',  cs.last_name
          |          )) AS campaign_call_settings
          |        FROM call_settings cs
          |        LEFT JOIN campaign_channel_settings ccs on ( ccs.channel_settings_uuid = cs.uuid AND ccs.channel_type = ?)
          |        WHERE ccs.team_id = c.team_id AND ccs.campaign_id = c.id) AS campaign_call_settings
          |        ,
          |      (
          |        SELECT json_agg(
          |          json_build_object(
          |            'channel_setting_uuid', ccs.channel_settings_uuid,
          |            'team_id', c.team_id,
          |            'phone_number', ws.whatsapp_number,
          |            'first_name', ws.first_name,
          |            'last_name',  ws.last_name
          |          )) AS campaign_whatsapp_settings
          |        FROM whatsapp_settings ws
          |        LEFT JOIN campaign_channel_settings ccs on ( ccs.channel_settings_uuid = ws.uuid AND ccs.channel_type = ?)
          |        WHERE ccs.team_id = c.team_id AND ccs.campaign_id = c.id) AS campaign_whatsapp_settings
          |        ,
          |        (
          |        SELECT json_agg(
          |          json_build_object(
          |            'channel_setting_uuid', ccs.channel_settings_uuid,
          |            'team_id', c.team_id,
          |            'phone_number', ss.phone_number,
          |            'first_name', ss.first_name,
          |            'last_name',  ss.last_name
          |          )) AS campaign_sms_settings
          |        FROM sms_settings ss
          |        LEFT JOIN campaign_channel_settings ccs on ( ccs.channel_settings_uuid = ss.uuid AND ccs.channel_type = ?)
          |        WHERE ccs.team_id = c.team_id AND ccs.campaign_id = c.id) AS campaign_sms_settings
          |        ,
          |
          |
          |      (
          |                SELECT EXISTS(
          |                      SELECT step_type from campaign_steps cs WHERE cs.campaign_id = c.id AND step_type IN
          |                   (
          |                        ?,
          |                        ?
          |                   )
          |                )
          |            ) as show_soft_start_setting ,
          |
          |
          |   has_spam_test.exists AS spam_test_exists,
          |   (SELECT EXISTS(
          |              SELECT cs.id FROM campaign_steps cs
          |              WHERE cs.campaign_id = c.id
          |              AND (
          |                cs.step_type = ?
          |                OR
          |                cs.step_type = ?
          |              )
          |            )) AS campaign_has_email_step,
          |
          |   (case when c.warmup_started_at is null or c.warmup_length_in_days is null THEN false
          |     else c.warmup_started_at + c.warmup_length_in_days * interval '1 day' > now() end) as warmup_is_on
          |
          |
          | FROM campaigns c
          |   INNER JOIN accounts a ON a.id = c.account_id
          |
          |   LEFT JOIN triggers trig ON (trig.campaign_id = c.id AND trig.source = ?)
          |
          |   LEFT JOIN LATERAL (
          |    SELECT EXISTS(SELECT id FROM spam_tests spamtest WHERE spamtest.campaign_id = c.id)
          |   ) AS has_spam_test ON true
          |
          | WHERE
          |   c.team_id = ?
          |
          |   AND c.account_id IN (?)
          |
          |   AND (
          |        c.name ILIKE ?
          |
          |          OR
          |
          |        EXISTS( select
          |           ces.id
          |          from campaign_email_settings ces
          |          JOIN email_settings sender ON sender.id = ces.sender_email_setting_id AND ces.team_id = sender.team_id
          |          where
          |          ces.campaign_id = c.id AND ces.team_id = c.team_id
          |          and sender.status = ?
          |          AND sender.email ILIKE ?
          |          LIMIT 1
          |           )
          |      )
          |
          | GROUP BY c.id, a.id,has_spam_test.exists
          |
          | ORDER BY
          |
          |      CASE
          |        WHEN c.status = ? THEN 1
          |        WHEN c.status = ? THEN 2
          |        WHEN c.status = ? THEN 3
          |        WHEN c.status = ? THEN 4
          |        WHEN c.status = ? THEN 5
          |        WHEN c.status = ? THEN 6
          |        WHEN c.status = ? THEN 7
          |        WHEN c.status = ? THEN 8
          |
          |      END, c.id DESC
          |
          | LIMIT ?
          | OFFSET ?
          |
        """.stripMargin.split(s"\\s+").reduce((s1, s2) => {
          s1 + " " + s2
        })

      val result = res.get._3.statement.split(s"\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })

      assert(expectedResult == result)
    }

    it("Should return the entire query with filter by owner field") {
      val data = SearchQuery(
        query = Some(
          SearchQueryMainClause(
            search = Some("test multiple sending"),
            owner_ids = Seq(90),
            clause = "AND",
            filters = Seq(SearchQuerySubClause(
              clause = "AND",
              filters = Seq(SearchQuerySubFilterData(
                field = "owner_id",
                field_display_name = None,
                operator = SearchQueryOperators.EQUAL,
                value = "1",
                value_display_name = None,
                field_type = FieldTypeEnum.NUMBER,
                is_custom = false,
                allowedFilterOperators = Some(
                  Seq(SearchQueryColumnOperator(
                    key = SearchQueryOperators.EQUAL,
                    display_name = "equals"
                  ))
                )

              ))
            ))
        )),
        page = Option(1),
        sort = None,
        is_campaign = None,
        older_than = None,
        newer_than = None
      )

      val res = CampaignQuery.getQuerySQL(
        account = accountAdmin,
        teamId = teamId,
        orgId = orgId,
        data = data,
        Logger = logger,
        permittedAccountIds = Seq(2L),
        fetchType = SearchQuerySelectType.FULL_OBJECT
      )



      val expectedResult =
        s"""
         SELECT
           a.email AS owner_email,
           a.id AS owner_account_id,
           CONCAT(a.first_name, ' ',  a.last_name) AS owner_name,

           COALESCE(json_agg(trig) FILTER (WHERE trig.id IS NOT NULL), '[]') AS triggers,
           c.id,
           c.account_id,
           c.name,
           c.status,
           c.is_archived,
           c.timezone,
           c.daily_from_time,
           c.daily_till_time,
           c.days_preference,
           c.ai_sequence_status,
           c.ai_generation_context,
           c.created_at,
           c.head_step_id,
           c.last_scheduled_at,
           c.email_priority,
           c.opt_out_msg,
           c.opt_out_is_text,
           c.team_id,
           c.shared_with_team,
           c.ta_id,
           c.max_emails_per_day,
           c.mark_completed_after_days,
           c.open_tracking_enabled,
           c.warmup_started_at,
           c.warmup_starting_email_count,
           c.warmup_length_in_days,
           c.click_tracking_enabled,
           c.latest_email_scheduled_at,
           c.append_followups,
           c.status_changed_at,
           c.will_delete,
           c.will_delete_at,
           c.enable_email_validation,
           c.ab_testing_enabled,
           c.add_prospect_to_dnc_on_opt_out,
           c.schedule_start_at,
           c.schedule_start_at_tz,
           c.last_analysed_at,
           c.pushed_to_queue_for_analysis_at,
           c.sending_holiday_calendar_id,
           -- c.next_to_be_scheduled_at,
           c.last_email_sent_at,
           c.latest_campaign_send_start_reports_id,
           c.pushed_to_queue_for_analysing_start_report_at,
           c.in_queue_for_analyzing_start_report,
           c.last_pushed_for_preemailvalidation_at,
           c.uuid,
           c.calendar_event_type_id,
           c.calendar_event_type_slug,
           c.calendar_is_individual,
           c.calendar_selected_user_id,
           c.calendar_selected_username_slug,
           c.calendar_smartreach_account_id,
           c.calendar_team_id,
           c.send_plain_text_email,
           c.campaign_type,
           c.calendar_team_slug,
           c.sending_mode,
           (
              SELECT
                json_agg(
                  json_build_object(
                    'id', ces.id,
                    'campaign_id', ces.campaign_id,
                    'sender_email_setting_id', ces.sender_email_setting_id,
                    'receiver_email_setting_id', ces.receiver_email_setting_id,
                    'team_id', ces.team_id,
                    'uuid', ces.uuid,
                    'sender_email', sender.email,
                    'receiver_email', receiver.email,
                    'from_name', sender.sender_name,
                    'signature', sender.signature,
                    'max_emails_per_day_from_email_account', sender.quota_per_day,
                    'error', (
                      CASE WHEN (sender.paused_till IS NOT NULL
                        AND sender.paused_till > now()) THEN
                        sender.error
                      WHEN (receiver.paused_till IS NOT NULL
                        AND receiver.paused_till > now()) THEN
                        receiver.error
                      WHEN (sender.is_under_review IS TRUE
                        OR receiver.is_under_review IS TRUE) THEN
                        'Your email account is under manual review. Please contact support.'
                      WHEN (a.active IS FALSE) THEN
                        concat('Campaign owner''s (', a.email, ') account has been deactivated by your team''s admin. Please change the owner to restart sending.')
                      WHEN (sender_owner.active IS FALSE) THEN
                        concat('You are using an email account (', sender.email, ') whose owner''s SmartReach.io account (', sender_owner.email, ') has been deactivated by your team''s admin. Please change the email account''s owner to restart sending from this campaign.')
                      WHEN (receiver_owner.active IS FALSE) THEN
                        concat('You are using an email account (', receiver.email, ') whose owner''s SmartReach.io account (', receiver_owner.email, ') has been deactivated by your team''s admin. Please change the email account''s owner to restart sending from this campaign.')
                      WHEN (sender_domain_check.is_in_spam_blacklist IS TRUE) THEN
                        'Your sending email domain is found in a global spam blacklist. Please check the status by going to Settings -> Team Settings -> Domain Health.'
                      ELSE
                        NULL
                      END))) AS campaign_email_settings
              FROM
                campaign_email_settings ces
                JOIN email_settings sender ON sender.id = ces.sender_email_setting_id
                  AND ces.team_id = sender.team_id
                JOIN email_settings receiver ON receiver.id = ces.receiver_email_setting_id
                  AND ces.team_id = receiver.team_id
              LEFT JOIN accounts sender_owner ON sender.account_id = sender_owner.id
              LEFT JOIN accounts receiver_owner ON receiver.account_id = receiver_owner.id
              LEFT JOIN domain_health_checks sender_domain_check ON sender_domain_check.domain = sender.email_address_host
            WHERE
              ces.campaign_id = c.id
              and sender.status = ?
              and receiver.status = ?
              AND ces.team_id = c.team_id) AS campaign_email_settings
               ,
           (
                    SELECT
                      json_agg(json_build_object('tag_id', tlfc.id, 'tag', tlfc.tag, 'tag_uuid', tlfc.uuid)) AS camtags
                    FROM
                      tags_list_for_campaigns tlfc
                      JOIN tags_campaigns ON (
                          tlfc.id = tags_campaigns.tag_id
                          AND tlfc.team_id = ?
                          AND tags_campaigns.campaign_id = c.id
                          AND tags_campaigns.team_id = ?
                      )
                  ) AS ctags
              ,
              (
                SELECT json_agg(
                  json_build_object(
                    'channel_setting_uuid', ccs.channel_settings_uuid,
                    'team_id', c.team_id,
                    'email', ls.email,
                    'first_name', ls.first_name,
                    'last_name',  ls.last_name,
                    'linkedin_profile_url', ls.profile_url,
                    'automation_enabled', ls.captain_data_account_id IS NOT NULL
                  )) AS campaign_linkedin_settings
                FROM linkedin_settings ls
                LEFT JOIN campaign_channel_settings ccs on ( ccs.channel_settings_uuid = ls.uuid AND ccs.channel_type = ?)
                WHERE ccs.team_id = c.team_id AND ccs.campaign_id = c.id) AS campaign_linkedin_settings
              ,
             (
                SELECT json_agg(
                  json_build_object(
                    'channel_setting_uuid', ccs.channel_settings_uuid,
                    'team_id', c.team_id,
                    'phone_number', cs.phone_number,
                    'first_name', cs.first_name,
                    'last_name',  cs.last_name
                  )) AS campaign_call_settings
                FROM call_settings cs
                LEFT JOIN campaign_channel_settings ccs on ( ccs.channel_settings_uuid = cs.uuid AND ccs.channel_type = ?)
                WHERE ccs.team_id = c.team_id AND ccs.campaign_id = c.id) AS campaign_call_settings
                ,
              (
                SELECT json_agg(
                  json_build_object(
                    'channel_setting_uuid', ccs.channel_settings_uuid,
                    'team_id', c.team_id,
                    'phone_number', ws.whatsapp_number,
                    'first_name', ws.first_name,
                    'last_name',  ws.last_name
                  )) AS campaign_whatsapp_settings
                FROM whatsapp_settings ws
                LEFT JOIN campaign_channel_settings ccs on ( ccs.channel_settings_uuid = ws.uuid AND ccs.channel_type = ?)
                WHERE ccs.team_id = c.team_id AND ccs.campaign_id = c.id) AS campaign_whatsapp_settings
                ,
                (
                SELECT json_agg(
                  json_build_object(
                    'channel_setting_uuid', ccs.channel_settings_uuid,
                    'team_id', c.team_id,
                    'phone_number', ss.phone_number,
                    'first_name', ss.first_name,
                    'last_name',  ss.last_name
                  )) AS campaign_sms_settings
                FROM sms_settings ss
                LEFT JOIN campaign_channel_settings ccs on ( ccs.channel_settings_uuid = ss.uuid AND ccs.channel_type = ?)
                WHERE ccs.team_id = c.team_id AND ccs.campaign_id = c.id) AS campaign_sms_settings
                ,


              (
                        SELECT EXISTS(
                              SELECT step_type from campaign_steps cs WHERE cs.campaign_id = c.id AND step_type IN
                           (
                                ?,
                                ?
                           )
                        )
                    ) as show_soft_start_setting ,


           has_spam_test.exists AS spam_test_exists,
           (SELECT EXISTS(
              SELECT cs.id FROM campaign_steps cs
              WHERE cs.campaign_id = c.id
              AND (
                cs.step_type = ?
                OR
                cs.step_type = ?
              )
            )) AS campaign_has_email_step,

           (case when c.warmup_started_at is null or c.warmup_length_in_days is null THEN false
             else c.warmup_started_at + c.warmup_length_in_days * interval '1 day' > now() end) as warmup_is_on


         FROM campaigns c
           INNER JOIN accounts a ON a.id = c.account_id

           LEFT JOIN triggers trig ON (trig.campaign_id = c.id AND trig.source = ?)

           LEFT JOIN LATERAL (
            SELECT EXISTS(SELECT id FROM spam_tests spamtest WHERE spamtest.campaign_id = c.id)
           ) AS has_spam_test ON true

         WHERE
           c.team_id = ?

           AND c.account_id IN (?)

           AND (
                c.name ILIKE ?

                  OR

                EXISTS( select
                   ces.id
                  from campaign_email_settings ces
                  JOIN email_settings sender ON sender.id = ces.sender_email_setting_id AND ces.team_id = sender.team_id
                  where
                  ces.campaign_id = c.id AND ces.team_id = c.team_id
                  and sender.status = ?
                  AND sender.email ILIKE ?
                  LIMIT 1
                   )
              )
           AND (  (  ( a.id = ? )  )  )

         GROUP BY c.id, a.id,has_spam_test.exists

         ORDER BY

              CASE
                WHEN c.status = ? THEN 1
                WHEN c.status = ? THEN 2
                WHEN c.status = ? THEN 3
                WHEN c.status = ? THEN 4
                WHEN c.status = ? THEN 5
                WHEN c.status = ? THEN 6
                WHEN c.status = ? THEN 7
                WHEN c.status = ? THEN 8

              END, c.id DESC

         LIMIT ?
         OFFSET ?

                """.stripMargin.split(s"\\s+").reduce((s1, s2) => {
          s1 + " " + s2
        })

      val result = res.get._3.statement.split(s"\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })

      assert(expectedResult == result)
    }

    it("Should return the entire query with filter by name field") {
      val data = SearchQuery(
        query = Some(
          SearchQueryMainClause(
            search = Some("test multiple sending"),
            owner_ids = Seq(90),
            clause = "AND",
            filters = Seq(SearchQuerySubClause(
              clause = "AND",
              filters = Seq(
                SearchQuerySubFilterData(
                  field = "name",
                  field_display_name = None,
                  operator = SearchQueryOperators.EQUAL,
                  value = "sample",
                  value_display_name = None,
                  field_type = FieldTypeEnum.TEXT,
                  is_custom = false,
                  allowedFilterOperators = Some(
                    Seq(SearchQueryColumnOperator(
                      key = SearchQueryOperators.EQUAL,
                      display_name = "equals"
                    ))
                  )
               )
              )
            ))
          )),
        page = Option(1),
        sort = None,
        is_campaign = None,
        older_than = None,
        newer_than = None
      )

      val res = CampaignQuery.getQuerySQL(
        account = accountAdmin,
        teamId = teamId,
        orgId = orgId,
        data = data,
        Logger = logger,
        permittedAccountIds = Seq(2L),
        fetchType = SearchQuerySelectType.FULL_OBJECT
      )


      val expectedResult =
        """
             SELECT
               a.email AS owner_email,
               a.id AS owner_account_id,
               CONCAT(a.first_name, ' ',  a.last_name) AS owner_name,

               COALESCE(json_agg(trig) FILTER (WHERE trig.id IS NOT NULL), '[]') AS triggers,
               c.id,
               c.account_id,
               c.name,
               c.status,
               c.is_archived,
               c.timezone,
               c.daily_from_time,
               c.daily_till_time,
               c.days_preference,
               c.ai_sequence_status,
               c.ai_generation_context,
               c.created_at,
               c.head_step_id,
               c.last_scheduled_at,
               c.email_priority,
               c.opt_out_msg,
               c.opt_out_is_text,
               c.team_id,
               c.shared_with_team,
               c.ta_id,
               c.max_emails_per_day,
               c.mark_completed_after_days,
               c.open_tracking_enabled,
               c.warmup_started_at,
               c.warmup_starting_email_count,
               c.warmup_length_in_days,
               c.click_tracking_enabled,
               c.latest_email_scheduled_at,
               c.append_followups,
               c.status_changed_at,
               c.will_delete,
               c.will_delete_at,
               c.enable_email_validation,
               c.ab_testing_enabled,
               c.add_prospect_to_dnc_on_opt_out,
               c.schedule_start_at,
               c.schedule_start_at_tz,
               c.last_analysed_at,
               c.pushed_to_queue_for_analysis_at,
               c.sending_holiday_calendar_id,
               -- c.next_to_be_scheduled_at,
               c.last_email_sent_at,
               c.latest_campaign_send_start_reports_id,
               c.pushed_to_queue_for_analysing_start_report_at,
               c.in_queue_for_analyzing_start_report,
               c.last_pushed_for_preemailvalidation_at,
               c.uuid,
               c.calendar_event_type_id,
               c.calendar_event_type_slug,
               c.calendar_is_individual,
               c.calendar_selected_user_id,
               c.calendar_selected_username_slug,
               c.calendar_smartreach_account_id,
               c.calendar_team_id,
               c.send_plain_text_email,
               c.campaign_type,
               c.calendar_team_slug,
               c.sending_mode,
               (
                  SELECT
                    json_agg(
                      json_build_object(
                        'id', ces.id,
                        'campaign_id', ces.campaign_id,
                        'sender_email_setting_id', ces.sender_email_setting_id,
                        'receiver_email_setting_id', ces.receiver_email_setting_id,
                        'team_id', ces.team_id,
                        'uuid', ces.uuid,
                        'sender_email', sender.email,
                        'receiver_email', receiver.email,
                        'from_name', sender.sender_name,
                        'signature', sender.signature,
                        'max_emails_per_day_from_email_account', sender.quota_per_day,
                        'error', (
                          CASE WHEN (sender.paused_till IS NOT NULL
                            AND sender.paused_till > now()) THEN
                            sender.error
                          WHEN (receiver.paused_till IS NOT NULL
                            AND receiver.paused_till > now()) THEN
                            receiver.error
                          WHEN (sender.is_under_review IS TRUE
                            OR receiver.is_under_review IS TRUE) THEN
                            'Your email account is under manual review. Please contact support.'
                          WHEN (a.active IS FALSE) THEN
                            concat('Campaign owner''s (', a.email, ') account has been deactivated by your team''s admin. Please change the owner to restart sending.')
                          WHEN (sender_owner.active IS FALSE) THEN
                            concat('You are using an email account (', sender.email, ') whose owner''s SmartReach.io account (', sender_owner.email, ') has been deactivated by your team''s admin. Please change the email account''s owner to restart sending from this campaign.')
                          WHEN (receiver_owner.active IS FALSE) THEN
                            concat('You are using an email account (', receiver.email, ') whose owner''s SmartReach.io account (', receiver_owner.email, ') has been deactivated by your team''s admin. Please change the email account''s owner to restart sending from this campaign.')
                          WHEN (sender_domain_check.is_in_spam_blacklist IS TRUE) THEN
                            'Your sending email domain is found in a global spam blacklist. Please check the status by going to Settings -> Team Settings -> Domain Health.'
                          ELSE
                            NULL
                          END))) AS campaign_email_settings
                  FROM
                    campaign_email_settings ces
                    JOIN email_settings sender ON sender.id = ces.sender_email_setting_id
                      AND ces.team_id = sender.team_id
                    JOIN email_settings receiver ON receiver.id = ces.receiver_email_setting_id
                      AND ces.team_id = receiver.team_id
                  LEFT JOIN accounts sender_owner ON sender.account_id = sender_owner.id
                  LEFT JOIN accounts receiver_owner ON receiver.account_id = receiver_owner.id
                  LEFT JOIN domain_health_checks sender_domain_check ON sender_domain_check.domain = sender.email_address_host
                WHERE
                  ces.campaign_id = c.id
                  and sender.status = ?
                  and receiver.status = ?
                  AND ces.team_id = c.team_id) AS campaign_email_settings
                   ,
               (
                        SELECT
                          json_agg(json_build_object('tag_id', tlfc.id, 'tag', tlfc.tag, 'tag_uuid', tlfc.uuid)) AS camtags
                        FROM
                          tags_list_for_campaigns tlfc
                          JOIN tags_campaigns ON (
                              tlfc.id = tags_campaigns.tag_id
                              AND tlfc.team_id = ?
                              AND tags_campaigns.campaign_id = c.id
                              AND tags_campaigns.team_id = ?
                          )
                      ) AS ctags
                  ,
                  (
                SELECT json_agg(
                  json_build_object(
                    'channel_setting_uuid', ccs.channel_settings_uuid,
                    'team_id', c.team_id,
                    'email', ls.email,
                    'first_name', ls.first_name,
                    'last_name',  ls.last_name,
                    'linkedin_profile_url', ls.profile_url,
                    'automation_enabled', ls.captain_data_account_id IS NOT NULL
                  )) AS campaign_linkedin_settings
                FROM linkedin_settings ls
                LEFT JOIN campaign_channel_settings ccs on ( ccs.channel_settings_uuid = ls.uuid AND ccs.channel_type = ?)
                WHERE ccs.team_id = c.team_id AND ccs.campaign_id = c.id) AS campaign_linkedin_settings
              ,
             (
                SELECT json_agg(
                  json_build_object(
                    'channel_setting_uuid', ccs.channel_settings_uuid,
                    'team_id', c.team_id,
                    'phone_number', cs.phone_number,
                    'first_name', cs.first_name,
                    'last_name',  cs.last_name
                  )) AS campaign_call_settings
                FROM call_settings cs
                LEFT JOIN campaign_channel_settings ccs on ( ccs.channel_settings_uuid = cs.uuid AND ccs.channel_type = ?)
                WHERE ccs.team_id = c.team_id AND ccs.campaign_id = c.id) AS campaign_call_settings
                ,
              (
                SELECT json_agg(
                  json_build_object(
                    'channel_setting_uuid', ccs.channel_settings_uuid,
                    'team_id', c.team_id,
                    'phone_number', ws.whatsapp_number,
                    'first_name', ws.first_name,
                    'last_name',  ws.last_name
                  )) AS campaign_whatsapp_settings
                FROM whatsapp_settings ws
                LEFT JOIN campaign_channel_settings ccs on ( ccs.channel_settings_uuid = ws.uuid AND ccs.channel_type = ?)
                WHERE ccs.team_id = c.team_id AND ccs.campaign_id = c.id) AS campaign_whatsapp_settings
                ,
                (
                SELECT json_agg(
                  json_build_object(
                    'channel_setting_uuid', ccs.channel_settings_uuid,
                    'team_id', c.team_id,
                    'phone_number', ss.phone_number,
                    'first_name', ss.first_name,
                    'last_name',  ss.last_name
                  )) AS campaign_sms_settings
                FROM sms_settings ss
                LEFT JOIN campaign_channel_settings ccs on ( ccs.channel_settings_uuid = ss.uuid AND ccs.channel_type = ?)
                WHERE ccs.team_id = c.team_id AND ccs.campaign_id = c.id) AS campaign_sms_settings
                ,


                  (
                        SELECT EXISTS(
                              SELECT step_type from campaign_steps cs WHERE cs.campaign_id = c.id AND step_type IN
                           (
                                ?,
                                ?
                           )
                        )
                    ) as show_soft_start_setting ,


               has_spam_test.exists AS spam_test_exists,
               (SELECT EXISTS(
              SELECT cs.id FROM campaign_steps cs
              WHERE cs.campaign_id = c.id
              AND (
                cs.step_type = ?
                OR
                cs.step_type = ?
              )
            )) AS campaign_has_email_step,

               (case when c.warmup_started_at is null or c.warmup_length_in_days is null THEN false
                 else c.warmup_started_at + c.warmup_length_in_days * interval '1 day' > now() end) as warmup_is_on


             FROM campaigns c
               INNER JOIN accounts a ON a.id = c.account_id

               LEFT JOIN triggers trig ON (trig.campaign_id = c.id AND trig.source = ?)

               LEFT JOIN LATERAL (
                SELECT EXISTS(SELECT id FROM spam_tests spamtest WHERE spamtest.campaign_id = c.id)
               ) AS has_spam_test ON true

             WHERE
               c.team_id = ?

               AND c.account_id IN (?)

               AND (
                    c.name ILIKE ?

                      OR

                    EXISTS( select
                       ces.id
                      from campaign_email_settings ces
                      JOIN email_settings sender ON sender.id = ces.sender_email_setting_id AND ces.team_id = sender.team_id
                      where
                      ces.campaign_id = c.id AND ces.team_id = c.team_id
                      and sender.status = ?
                      AND sender.email ILIKE ?
                      LIMIT 1
                       )
                  )
               AND (  (  ( c.name = ? )  )  )

             GROUP BY c.id, a.id,has_spam_test.exists

             ORDER BY

                  CASE
                    WHEN c.status = ? THEN 1
                    WHEN c.status = ? THEN 2
                    WHEN c.status = ? THEN 3
                    WHEN c.status = ? THEN 4
                    WHEN c.status = ? THEN 5
                    WHEN c.status = ? THEN 6
                    WHEN c.status = ? THEN 7
                    WHEN c.status = ? THEN 8

                  END, c.id DESC

             LIMIT ?
             OFFSET ?

                """.stripMargin.split(s"\\s+").reduce((s1, s2) => {
          s1 + " " + s2
        })

      val result = res.get._3.statement.split(s"\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })

      assert(expectedResult == result)
    }

    it("Should return the entire query with no search parameter") {
      val data = SearchQuery(
        query = None,
        page = Option(1),
        sort = None,
        is_campaign = None,
        older_than = None,
        newer_than = None
      )

      val res = CampaignQuery.getQuerySQL(
        account = accountAdmin,
        teamId = teamId,
        orgId = orgId,
        data = data,
        Logger = logger,
        permittedAccountIds = Seq(2L),
        fetchType = SearchQuerySelectType.FULL_OBJECT
      )

      val expectedResult =
        """
             SELECT
               a.email AS owner_email,
               a.id AS owner_account_id,
               CONCAT(a.first_name, ' ',  a.last_name) AS owner_name,

               COALESCE(json_agg(trig) FILTER (WHERE trig.id IS NOT NULL), '[]') AS triggers,
               c.id,
               c.account_id,
               c.name,
               c.status,
               c.is_archived,
               c.timezone,
               c.daily_from_time,
               c.daily_till_time,
               c.days_preference,
               c.ai_sequence_status,
               c.ai_generation_context,
               c.created_at,
               c.head_step_id,
               c.last_scheduled_at,
               c.email_priority,
               c.opt_out_msg,
               c.opt_out_is_text,
               c.team_id,
               c.shared_with_team,
               c.ta_id,
               c.max_emails_per_day,
               c.mark_completed_after_days,
               c.open_tracking_enabled,
               c.warmup_started_at,
               c.warmup_starting_email_count,
               c.warmup_length_in_days,
               c.click_tracking_enabled,
               c.latest_email_scheduled_at,
               c.append_followups,
               c.status_changed_at,
               c.will_delete,
               c.will_delete_at,
               c.enable_email_validation,
               c.ab_testing_enabled,
               c.add_prospect_to_dnc_on_opt_out,
               c.schedule_start_at,
               c.schedule_start_at_tz,
               c.last_analysed_at,
               c.pushed_to_queue_for_analysis_at,
               c.sending_holiday_calendar_id,
               -- c.next_to_be_scheduled_at,
               c.last_email_sent_at,
               c.latest_campaign_send_start_reports_id,
               c.pushed_to_queue_for_analysing_start_report_at,
               c.in_queue_for_analyzing_start_report,
               c.last_pushed_for_preemailvalidation_at,
               c.uuid,
               c.calendar_event_type_id,
               c.calendar_event_type_slug,
               c.calendar_is_individual,
               c.calendar_selected_user_id,
               c.calendar_selected_username_slug,
               c.calendar_smartreach_account_id,
               c.calendar_team_id,
               c.send_plain_text_email,
               c.campaign_type,
               c.calendar_team_slug,
               c.sending_mode,
               (
                  SELECT
                    json_agg(
                      json_build_object(
                        'id', ces.id,
                        'campaign_id', ces.campaign_id,
                        'sender_email_setting_id', ces.sender_email_setting_id,
                        'receiver_email_setting_id', ces.receiver_email_setting_id,
                        'team_id', ces.team_id,
                        'uuid', ces.uuid,
                        'sender_email', sender.email,
                        'receiver_email', receiver.email,
                        'from_name', sender.sender_name,
                        'signature', sender.signature,
                        'max_emails_per_day_from_email_account', sender.quota_per_day,
                        'error', (
                          CASE WHEN (sender.paused_till IS NOT NULL
                            AND sender.paused_till > now()) THEN
                            sender.error
                          WHEN (receiver.paused_till IS NOT NULL
                            AND receiver.paused_till > now()) THEN
                            receiver.error
                          WHEN (sender.is_under_review IS TRUE
                            OR receiver.is_under_review IS TRUE) THEN
                            'Your email account is under manual review. Please contact support.'
                          WHEN (a.active IS FALSE) THEN
                            concat('Campaign owner''s (', a.email, ') account has been deactivated by your team''s admin. Please change the owner to restart sending.')
                          WHEN (sender_owner.active IS FALSE) THEN
                            concat('You are using an email account (', sender.email, ') whose owner''s SmartReach.io account (', sender_owner.email, ') has been deactivated by your team''s admin. Please change the email account''s owner to restart sending from this campaign.')
                          WHEN (receiver_owner.active IS FALSE) THEN
                            concat('You are using an email account (', receiver.email, ') whose owner''s SmartReach.io account (', receiver_owner.email, ') has been deactivated by your team''s admin. Please change the email account''s owner to restart sending from this campaign.')
                          WHEN (sender_domain_check.is_in_spam_blacklist IS TRUE) THEN
                            'Your sending email domain is found in a global spam blacklist. Please check the status by going to Settings -> Team Settings -> Domain Health.'
                          ELSE
                            NULL
                          END))) AS campaign_email_settings
                  FROM
                    campaign_email_settings ces
                    JOIN email_settings sender ON sender.id = ces.sender_email_setting_id
                      AND ces.team_id = sender.team_id
                    JOIN email_settings receiver ON receiver.id = ces.receiver_email_setting_id
                      AND ces.team_id = receiver.team_id
                  LEFT JOIN accounts sender_owner ON sender.account_id = sender_owner.id
                  LEFT JOIN accounts receiver_owner ON receiver.account_id = receiver_owner.id
                  LEFT JOIN domain_health_checks sender_domain_check ON sender_domain_check.domain = sender.email_address_host
                WHERE
                  ces.campaign_id = c.id
                  and sender.status = ?
                  and receiver.status = ?
                  AND ces.team_id = c.team_id) AS campaign_email_settings
                   ,
               (
                        SELECT
                          json_agg(json_build_object('tag_id', tlfc.id, 'tag', tlfc.tag, 'tag_uuid', tlfc.uuid)) AS camtags
                        FROM
                          tags_list_for_campaigns tlfc
                          JOIN tags_campaigns ON (
                              tlfc.id = tags_campaigns.tag_id
                              AND tlfc.team_id = ?
                              AND tags_campaigns.campaign_id = c.id
                              AND tags_campaigns.team_id = ?
                          )
                      ) AS ctags
                  ,
                  (
                SELECT json_agg(
                  json_build_object(
                    'channel_setting_uuid', ccs.channel_settings_uuid,
                    'team_id', c.team_id,
                    'email', ls.email,
                    'first_name', ls.first_name,
                    'last_name',  ls.last_name,
                    'linkedin_profile_url', ls.profile_url,
                    'automation_enabled', ls.captain_data_account_id IS NOT NULL
                  )) AS campaign_linkedin_settings
                FROM linkedin_settings ls
                LEFT JOIN campaign_channel_settings ccs on ( ccs.channel_settings_uuid = ls.uuid AND ccs.channel_type = ?)
                WHERE ccs.team_id = c.team_id AND ccs.campaign_id = c.id) AS campaign_linkedin_settings
              ,
             (
                SELECT json_agg(
                  json_build_object(
                    'channel_setting_uuid', ccs.channel_settings_uuid,
                    'team_id', c.team_id,
                    'phone_number', cs.phone_number,
                    'first_name', cs.first_name,
                    'last_name',  cs.last_name
                  )) AS campaign_call_settings
                FROM call_settings cs
                LEFT JOIN campaign_channel_settings ccs on ( ccs.channel_settings_uuid = cs.uuid AND ccs.channel_type = ?)
                WHERE ccs.team_id = c.team_id AND ccs.campaign_id = c.id) AS campaign_call_settings
                ,
              (
                SELECT json_agg(
                  json_build_object(
                    'channel_setting_uuid', ccs.channel_settings_uuid,
                    'team_id', c.team_id,
                    'phone_number', ws.whatsapp_number,
                    'first_name', ws.first_name,
                    'last_name',  ws.last_name
                  )) AS campaign_whatsapp_settings
                FROM whatsapp_settings ws
                LEFT JOIN campaign_channel_settings ccs on ( ccs.channel_settings_uuid = ws.uuid AND ccs.channel_type = ?)
                WHERE ccs.team_id = c.team_id AND ccs.campaign_id = c.id) AS campaign_whatsapp_settings
                ,
                (
                SELECT json_agg(
                  json_build_object(
                    'channel_setting_uuid', ccs.channel_settings_uuid,
                    'team_id', c.team_id,
                    'phone_number', ss.phone_number,
                    'first_name', ss.first_name,
                    'last_name',  ss.last_name
                  )) AS campaign_sms_settings
                FROM sms_settings ss
                LEFT JOIN campaign_channel_settings ccs on ( ccs.channel_settings_uuid = ss.uuid AND ccs.channel_type = ?)
                WHERE ccs.team_id = c.team_id AND ccs.campaign_id = c.id) AS campaign_sms_settings
                ,


                  (
                        SELECT EXISTS(
                              SELECT step_type from campaign_steps cs WHERE cs.campaign_id = c.id AND step_type IN
                           (
                                ?,
                                ?
                           )
                        )
                    ) as show_soft_start_setting ,


               has_spam_test.exists AS spam_test_exists,
               (SELECT EXISTS(
              SELECT cs.id FROM campaign_steps cs
              WHERE cs.campaign_id = c.id
              AND (
                cs.step_type = ?
                OR
                cs.step_type = ?
              )
            )) AS campaign_has_email_step,

               (case when c.warmup_started_at is null or c.warmup_length_in_days is null THEN false
                 else c.warmup_started_at + c.warmup_length_in_days * interval '1 day' > now() end) as warmup_is_on


             FROM campaigns c
               INNER JOIN accounts a ON a.id = c.account_id

               LEFT JOIN triggers trig ON (trig.campaign_id = c.id AND trig.source = ?)

               LEFT JOIN LATERAL (
                SELECT EXISTS(SELECT id FROM spam_tests spamtest WHERE spamtest.campaign_id = c.id)
               ) AS has_spam_test ON true

             WHERE
               c.team_id = ?

               AND c.account_id IN (?)



             GROUP BY c.id, a.id,has_spam_test.exists

             ORDER BY

                  CASE
                    WHEN c.status = ? THEN 1
                    WHEN c.status = ? THEN 2
                    WHEN c.status = ? THEN 3
                    WHEN c.status = ? THEN 4
                    WHEN c.status = ? THEN 5
                    WHEN c.status = ? THEN 6
                    WHEN c.status = ? THEN 7
                    WHEN c.status = ? THEN 8

                  END, c.id DESC

             LIMIT ?
             OFFSET ?
             
                """.stripMargin.split(s"\\s+").reduce((s1, s2) => {
          s1 + " " + s2
        })

      val result = res.get._3.statement.split(s"\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })

      assert(expectedResult == result)
    }

  }


  describe("testing getModifiedQuerySQL"){

    it("should return full query"){

      val expectedResult =
        """
          | SELECT
          |   a.email AS owner_email,
          |   a.id AS owner_account_id,
          |   CONCAT(a.first_name, ' ',  a.last_name) AS owner_name,
          |
          |   COALESCE(json_agg(trig) FILTER (WHERE trig.id IS NOT NULL), '[]') AS triggers,
          |   c.id,
          |   c.account_id,
          |   c.name,
          |   c.status,
          |   c.is_archived,
          |   c.timezone,
          |   c.daily_from_time,
          |   c.daily_till_time,
          |   c.days_preference,
          |   c.ai_sequence_status,
          |   c.ai_generation_context,
          |   c.created_at,
          |   c.head_step_id,
          |   c.last_scheduled_at,
          |   c.email_priority,
          |   c.opt_out_msg,
          |   c.opt_out_is_text,
          |   c.team_id,
          |   c.shared_with_team,
          |   c.ta_id,
          |   c.max_emails_per_day,
          |   c.mark_completed_after_days,
          |   c.open_tracking_enabled,
          |   c.warmup_started_at,
          |   c.warmup_starting_email_count,
          |   c.warmup_length_in_days,
          |   c.click_tracking_enabled,
          |   c.latest_email_scheduled_at,
          |   c.append_followups,
          |   c.status_changed_at,
          |   c.will_delete,
          |   c.will_delete_at,
          |   c.enable_email_validation,
          |   c.ab_testing_enabled,
          |   c.add_prospect_to_dnc_on_opt_out,
          |   c.schedule_start_at,
          |   c.schedule_start_at_tz,
          |   c.last_analysed_at,
          |   c.pushed_to_queue_for_analysis_at,
          |   c.sending_holiday_calendar_id,
          |   -- c.next_to_be_scheduled_at,
          |   c.last_email_sent_at,
          |   c.latest_campaign_send_start_reports_id,
          |   c.pushed_to_queue_for_analysing_start_report_at,
          |   c.in_queue_for_analyzing_start_report,
          |   c.last_pushed_for_preemailvalidation_at,
          |   c.uuid,
          |   c.calendar_event_type_id,
          |   c.calendar_event_type_slug,
          |   c.calendar_is_individual,
          |   c.calendar_selected_user_id,
          |   c.calendar_selected_username_slug,
          |   c.calendar_smartreach_account_id,
          |   c.calendar_team_id,
          |   c.send_plain_text_email,
          |   c.campaign_type,
          |   c.calendar_team_slug,
          |   c.sending_mode,
          |   (
          |      SELECT
          |        json_agg(
          |          json_build_object(
          |            'id', ces.id,
          |            'campaign_id', ces.campaign_id,
          |            'sender_email_setting_id', ces.sender_email_setting_id,
          |            'receiver_email_setting_id', ces.receiver_email_setting_id,
          |            'team_id', ces.team_id,
          |            'uuid', ces.uuid,
          |            'sender_email', sender.email,
          |            'receiver_email', receiver.email,
          |            'from_name', sender.sender_name,
          |            'signature', sender.signature,
          |            'max_emails_per_day_from_email_account', sender.quota_per_day,
          |            'error', (
          |              CASE WHEN (sender.paused_till IS NOT NULL
          |                AND sender.paused_till > now()) THEN
          |                sender.error
          |              WHEN (receiver.paused_till IS NOT NULL
          |                AND receiver.paused_till > now()) THEN
          |                receiver.error
          |              WHEN (sender.is_under_review IS TRUE
          |                OR receiver.is_under_review IS TRUE) THEN
          |                'Your email account is under manual review. Please contact support.'
          |              WHEN (a.active IS FALSE) THEN
          |                concat('Campaign owner''s (', a.email, ') account has been deactivated by your team''s admin. Please change the owner to restart sending.')
          |              WHEN (sender_owner.active IS FALSE) THEN
          |                concat('You are using an email account (', sender.email, ') whose owner''s SmartReach.io account (', sender_owner.email, ') has been deactivated by your team''s admin. Please change the email account''s owner to restart sending from this campaign.')
          |              WHEN (receiver_owner.active IS FALSE) THEN
          |                concat('You are using an email account (', receiver.email, ') whose owner''s SmartReach.io account (', receiver_owner.email, ') has been deactivated by your team''s admin. Please change the email account''s owner to restart sending from this campaign.')
          |              WHEN (sender_domain_check.is_in_spam_blacklist IS TRUE) THEN
          |                'Your sending email domain is found in a global spam blacklist. Please check the status by going to Settings -> Team Settings -> Domain Health.'
          |              ELSE
          |                NULL
          |              END))) AS campaign_email_settings
          |      FROM
          |        campaign_email_settings ces
          |        JOIN email_settings sender ON sender.id = ces.sender_email_setting_id
          |          AND ces.team_id = sender.team_id
          |        JOIN email_settings receiver ON receiver.id = ces.receiver_email_setting_id
          |          AND ces.team_id = receiver.team_id
          |      LEFT JOIN accounts sender_owner ON sender.account_id = sender_owner.id
          |      LEFT JOIN accounts receiver_owner ON receiver.account_id = receiver_owner.id
          |      LEFT JOIN domain_health_checks sender_domain_check ON sender_domain_check.domain = sender.email_address_host
          |    WHERE
          |      ces.campaign_id = c.id
          |      and sender.status = ?
          |      and receiver.status = ?
          |      AND ces.team_id = c.team_id) AS campaign_email_settings
          |       ,
          |   (
          |            SELECT
          |              json_agg(json_build_object('tag_id', tlfc.id, 'tag', tlfc.tag, 'tag_uuid', tlfc.uuid)) AS camtags
          |            FROM
          |              tags_list_for_campaigns tlfc
          |              JOIN tags_campaigns ON (
          |                  tlfc.id = tags_campaigns.tag_id
          |                  AND tlfc.team_id = ?
          |                  AND tags_campaigns.campaign_id = c.id
          |                  AND tags_campaigns.team_id = ?
          |              )
          |					) AS ctags
          |      ,
          |      (
          |                SELECT json_agg(
          |                  json_build_object(
          |                    'channel_setting_uuid', ccs.channel_settings_uuid,
          |                    'team_id', c.team_id,
          |                    'email', ls.email,
          |                    'first_name', ls.first_name,
          |                    'last_name',  ls.last_name,
          |                    'linkedin_profile_url', ls.profile_url,
          |                    'automation_enabled', ls.captain_data_account_id IS NOT NULL
          |                  )) AS campaign_linkedin_settings
          |                FROM linkedin_settings ls
          |                LEFT JOIN campaign_channel_settings ccs on ( ccs.channel_settings_uuid = ls.uuid AND ccs.channel_type = ?)
          |                WHERE ccs.team_id = c.team_id AND ccs.campaign_id = c.id) AS campaign_linkedin_settings
          |              ,
          |             (
          |                SELECT json_agg(
          |                  json_build_object(
          |                    'channel_setting_uuid', ccs.channel_settings_uuid,
          |                    'team_id', c.team_id,
          |                    'phone_number', cs.phone_number,
          |                    'first_name', cs.first_name,
          |                    'last_name',  cs.last_name
          |                  )) AS campaign_call_settings
          |                FROM call_settings cs
          |                LEFT JOIN campaign_channel_settings ccs on ( ccs.channel_settings_uuid = cs.uuid AND ccs.channel_type = ?)
          |                WHERE ccs.team_id = c.team_id AND ccs.campaign_id = c.id) AS campaign_call_settings
          |                ,
          |              (
          |                SELECT json_agg(
          |                  json_build_object(
          |                    'channel_setting_uuid', ccs.channel_settings_uuid,
          |                    'team_id', c.team_id,
          |                    'phone_number', ws.whatsapp_number,
          |                    'first_name', ws.first_name,
          |                    'last_name',  ws.last_name
          |                  )) AS campaign_whatsapp_settings
          |                FROM whatsapp_settings ws
          |                LEFT JOIN campaign_channel_settings ccs on ( ccs.channel_settings_uuid = ws.uuid AND ccs.channel_type = ?)
          |                WHERE ccs.team_id = c.team_id AND ccs.campaign_id = c.id) AS campaign_whatsapp_settings
          |                ,
          |                (
          |                SELECT json_agg(
          |                  json_build_object(
          |                    'channel_setting_uuid', ccs.channel_settings_uuid,
          |                    'team_id', c.team_id,
          |                    'phone_number', ss.phone_number,
          |                    'first_name', ss.first_name,
          |                    'last_name',  ss.last_name
          |                  )) AS campaign_sms_settings
          |                FROM sms_settings ss
          |                LEFT JOIN campaign_channel_settings ccs on ( ccs.channel_settings_uuid = ss.uuid AND ccs.channel_type = ?)
          |                WHERE ccs.team_id = c.team_id AND ccs.campaign_id = c.id) AS campaign_sms_settings
          |                ,
          |
          |      (
          |                SELECT EXISTS(
          |                      SELECT step_type from campaign_steps cs WHERE cs.campaign_id = c.id AND step_type IN
          |                   (
          |                        ?,
          |                        ?
          |                   )
          |                )
          |            ) as show_soft_start_setting ,
          |
          |
          |   has_spam_test.exists AS spam_test_exists,
          |   (SELECT EXISTS(
          |              SELECT cs.id FROM campaign_steps cs
          |              WHERE cs.campaign_id = c.id
          |              AND (
          |                cs.step_type = ?
          |                OR
          |                cs.step_type = ?
          |              )
          |            )) AS campaign_has_email_step,
          |
          |   (case when c.warmup_started_at is null or c.warmup_length_in_days is null THEN false
          |     else c.warmup_started_at + c.warmup_length_in_days * interval '1 day' > now() end) as warmup_is_on
          |
          |
          | FROM campaigns c
          |   INNER JOIN accounts a ON a.id = c.account_id
          |
          |   LEFT JOIN triggers trig ON (trig.campaign_id = c.id AND trig.source = ?)
          |
          |   LEFT JOIN LATERAL (
          |    SELECT EXISTS(SELECT id FROM spam_tests spamtest WHERE spamtest.campaign_id = c.id)
          |   ) AS has_spam_test ON true
          |
          | WHERE
          |  c.team_id = ?
          |  AND c.id = ?
          |
          | GROUP BY c.id, a.id,has_spam_test.exists
          |
        """.stripMargin.split(s"\\s+").reduce((s1, s2) => {
          s1 + " " + s2
        })

      val res = CampaignQuery.getModifiedQuerySQL(
        teamId = 3,
        cId = 5
      )

      val result = res.get.statement.split(s"\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })

      assert(expectedResult == result)


    }


  }

  describe("testing getCampaignsV3QuerySQL") {

    val logger = new SRLogger("getCampaignsV3QuerySQL")

    it("should return full query") {

      val expectedResult =
        """
          | SELECT
          |   a.email AS owner_email,
          |   a.id AS owner_account_id,
          |   CONCAT(a.first_name, ' ',  a.last_name) AS owner_name,
          |
          |   COALESCE(json_agg(trig) FILTER (WHERE trig.id IS NOT NULL), '[]') AS triggers,
          |   c.id,
          |   c.account_id,
          |   c.name,
          |   c.status,
          |   c.is_archived,
          |   c.timezone,
          |   c.daily_from_time,
          |   c.daily_till_time,
          |   c.days_preference,
          |   c.ai_sequence_status,
          |   c.ai_generation_context,
          |   c.created_at,
          |   c.head_step_id,
          |   c.last_scheduled_at,
          |   c.email_priority,
          |   c.opt_out_msg,
          |   c.opt_out_is_text,
          |   c.team_id,
          |   c.shared_with_team,
          |   c.ta_id,
          |   c.max_emails_per_day,
          |   c.mark_completed_after_days,
          |   c.open_tracking_enabled,
          |   c.warmup_started_at,
          |   c.warmup_starting_email_count,
          |   c.warmup_length_in_days,
          |   c.click_tracking_enabled,
          |   c.latest_email_scheduled_at,
          |   c.append_followups,
          |   c.status_changed_at,
          |   c.will_delete,
          |   c.will_delete_at,
          |   c.enable_email_validation,
          |   c.ab_testing_enabled,
          |   c.add_prospect_to_dnc_on_opt_out,
          |   c.schedule_start_at,
          |   c.schedule_start_at_tz,
          |   c.last_analysed_at,
          |   c.pushed_to_queue_for_analysis_at,
          |   c.sending_holiday_calendar_id,
          |   -- c.next_to_be_scheduled_at,
          |   c.last_email_sent_at,
          |   c.latest_campaign_send_start_reports_id,
          |   c.pushed_to_queue_for_analysing_start_report_at,
          |   c.in_queue_for_analyzing_start_report,
          |   c.last_pushed_for_preemailvalidation_at,
          |   c.uuid,
          |   c.calendar_event_type_id,
          |   c.calendar_event_type_slug,
          |   c.calendar_is_individual,
          |   c.calendar_selected_user_id,
          |   c.calendar_selected_username_slug,
          |   c.calendar_smartreach_account_id,
          |   c.calendar_team_id,
          |   c.send_plain_text_email,
          |   c.campaign_type,
          |   c.calendar_team_slug,
          |   c.sending_mode,
          |   (
          |      SELECT
          |        json_agg(
          |          json_build_object(
          |            'id', ces.id,
          |            'campaign_id', ces.campaign_id,
          |            'sender_email_setting_id', ces.sender_email_setting_id,
          |            'receiver_email_setting_id', ces.receiver_email_setting_id,
          |            'team_id', ces.team_id,
          |            'uuid', ces.uuid,
          |            'sender_email', sender.email,
          |            'receiver_email', receiver.email,
          |            'from_name', sender.sender_name,
          |            'signature', sender.signature,
          |            'max_emails_per_day_from_email_account', sender.quota_per_day,
          |            'error', (
          |              CASE WHEN (sender.paused_till IS NOT NULL
          |                AND sender.paused_till > now()) THEN
          |                sender.error
          |              WHEN (receiver.paused_till IS NOT NULL
          |                AND receiver.paused_till > now()) THEN
          |                receiver.error
          |              WHEN (sender.is_under_review IS TRUE
          |                OR receiver.is_under_review IS TRUE) THEN
          |                'Your email account is under manual review. Please contact support.'
          |              WHEN (a.active IS FALSE) THEN
          |                concat('Campaign owner''s (', a.email, ') account has been deactivated by your team''s admin. Please change the owner to restart sending.')
          |              WHEN (sender_owner.active IS FALSE) THEN
          |                concat('You are using an email account (', sender.email, ') whose owner''s SmartReach.io account (', sender_owner.email, ') has been deactivated by your team''s admin. Please change the email account''s owner to restart sending from this campaign.')
          |              WHEN (receiver_owner.active IS FALSE) THEN
          |                concat('You are using an email account (', receiver.email, ') whose owner''s SmartReach.io account (', receiver_owner.email, ') has been deactivated by your team''s admin. Please change the email account''s owner to restart sending from this campaign.')
          |              WHEN (sender_domain_check.is_in_spam_blacklist IS TRUE) THEN
          |                'Your sending email domain is found in a global spam blacklist. Please check the status by going to Settings -> Team Settings -> Domain Health.'
          |              ELSE
          |                NULL
          |              END))) AS campaign_email_settings
          |      FROM
          |        campaign_email_settings ces
          |        JOIN email_settings sender ON sender.id = ces.sender_email_setting_id
          |          AND ces.team_id = sender.team_id
          |        JOIN email_settings receiver ON receiver.id = ces.receiver_email_setting_id
          |          AND ces.team_id = receiver.team_id
          |      LEFT JOIN accounts sender_owner ON sender.account_id = sender_owner.id
          |      LEFT JOIN accounts receiver_owner ON receiver.account_id = receiver_owner.id
          |      LEFT JOIN domain_health_checks sender_domain_check ON sender_domain_check.domain = sender.email_address_host
          |    WHERE
          |      ces.campaign_id = c.id
          |      and sender.status = ?
          |      and receiver.status = ?
          |      AND ces.team_id = c.team_id) AS campaign_email_settings
          |       ,
          |   (
          |            SELECT
          |              json_agg(json_build_object('tag_id', tlfc.id, 'tag', tlfc.tag, 'tag_uuid', tlfc.uuid)) AS camtags
          |            FROM
          |              tags_list_for_campaigns tlfc
          |              JOIN tags_campaigns ON (
          |                  tlfc.id = tags_campaigns.tag_id
          |                  AND tlfc.team_id = ?
          |                  AND tags_campaigns.campaign_id = c.id
          |                  AND tags_campaigns.team_id = ?
          |              )
          |					) AS ctags
          |      ,
          |      (
          |                SELECT json_agg(
          |                  json_build_object(
          |                    'channel_setting_uuid', ccs.channel_settings_uuid,
          |                    'team_id', c.team_id,
          |                    'email', ls.email,
          |                    'first_name', ls.first_name,
          |                    'last_name',  ls.last_name,
          |                    'linkedin_profile_url', ls.profile_url,
          |                    'automation_enabled', ls.captain_data_account_id IS NOT NULL
          |                  )) AS campaign_linkedin_settings
          |                FROM linkedin_settings ls
          |                LEFT JOIN campaign_channel_settings ccs on ( ccs.channel_settings_uuid = ls.uuid AND ccs.channel_type = ?)
          |                WHERE ccs.team_id = c.team_id AND ccs.campaign_id = c.id) AS campaign_linkedin_settings
          |              ,
          |             (
          |                SELECT json_agg(
          |                  json_build_object(
          |                    'channel_setting_uuid', ccs.channel_settings_uuid,
          |                    'team_id', c.team_id,
          |                    'phone_number', cs.phone_number,
          |                    'first_name', cs.first_name,
          |                    'last_name',  cs.last_name
          |                  )) AS campaign_call_settings
          |                FROM call_settings cs
          |                LEFT JOIN campaign_channel_settings ccs on ( ccs.channel_settings_uuid = cs.uuid AND ccs.channel_type = ?)
          |                WHERE ccs.team_id = c.team_id AND ccs.campaign_id = c.id) AS campaign_call_settings
          |                ,
          |              (
          |                SELECT json_agg(
          |                  json_build_object(
          |                    'channel_setting_uuid', ccs.channel_settings_uuid,
          |                    'team_id', c.team_id,
          |                    'phone_number', ws.whatsapp_number,
          |                    'first_name', ws.first_name,
          |                    'last_name',  ws.last_name
          |                  )) AS campaign_whatsapp_settings
          |                FROM whatsapp_settings ws
          |                LEFT JOIN campaign_channel_settings ccs on ( ccs.channel_settings_uuid = ws.uuid AND ccs.channel_type = ?)
          |                WHERE ccs.team_id = c.team_id AND ccs.campaign_id = c.id) AS campaign_whatsapp_settings
          |                ,
          |                (
          |                SELECT json_agg(
          |                  json_build_object(
          |                    'channel_setting_uuid', ccs.channel_settings_uuid,
          |                    'team_id', c.team_id,
          |                    'phone_number', ss.phone_number,
          |                    'first_name', ss.first_name,
          |                    'last_name',  ss.last_name
          |                  )) AS campaign_sms_settings
          |                FROM sms_settings ss
          |                LEFT JOIN campaign_channel_settings ccs on ( ccs.channel_settings_uuid = ss.uuid AND ccs.channel_type = ?)
          |                WHERE ccs.team_id = c.team_id AND ccs.campaign_id = c.id) AS campaign_sms_settings
          |                ,
          |
          |      (
          |                SELECT EXISTS(
          |                      SELECT step_type from campaign_steps cs WHERE cs.campaign_id = c.id AND step_type IN
          |                   (
          |                        ?,
          |                        ?
          |                   )
          |                )
          |            ) as show_soft_start_setting ,
          |
          |
          |   has_spam_test.exists AS spam_test_exists,
          |   (SELECT EXISTS(
          |              SELECT cs.id FROM campaign_steps cs
          |              WHERE cs.campaign_id = c.id
          |              AND (
          |                cs.step_type = ?
          |                OR
          |                cs.step_type = ?
          |              )
          |            )) AS campaign_has_email_step,
          |
          |   (case when c.warmup_started_at is null or c.warmup_length_in_days is null THEN false
          |     else c.warmup_started_at + c.warmup_length_in_days * interval '1 day' > now() end) as warmup_is_on
          |
          |
          | FROM campaigns c
          |   INNER JOIN accounts a ON a.id = c.account_id
          |
          |   LEFT JOIN triggers trig ON (trig.campaign_id = c.id AND trig.source = ?)
          |
          |   LEFT JOIN LATERAL (
          |    SELECT EXISTS(SELECT id FROM spam_tests spamtest WHERE spamtest.campaign_id = c.id)
          |   ) AS has_spam_test ON true
          |
          | WHERE
          |        c.team_id = ?
          |        AND c.account_id IN (?)
          |        AND c.created_at AT TIME ZONE 'UTC' < ?
          |
          |      GROUP BY c.id, a.id,has_spam_test.exists
          |
          |       ORDER BY c.created_at desc
          |
          |      LIMIT ?
          |
        """.stripMargin.split(s"\\s+").reduce((s1, s2) => {
          s1 + " " + s2
        })

      val res = CampaignQuery.getCampaignsV3QuerySQL(
        teamId = 15,
        orgId = 2,
        search_params = search_params,
        Logger = logger,
        permittedAccountIds = Seq(17L)
      )

      val result = res.get.statement.split(s"\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })

      assert(expectedResult == result)


    }

    it("should not return c_tags as org_id is not enabled for it.") {

      val expectedResult =
        """
          | SELECT
          |   a.email AS owner_email,
          |   a.id AS owner_account_id,
          |   CONCAT(a.first_name, ' ',  a.last_name) AS owner_name,
          |
          |   COALESCE(json_agg(trig) FILTER (WHERE trig.id IS NOT NULL), '[]') AS triggers,
          |   c.id,
          |   c.account_id,
          |   c.name,
          |   c.status,
          |   c.is_archived,
          |   c.timezone,
          |   c.daily_from_time,
          |   c.daily_till_time,
          |   c.days_preference,
          |   c.ai_sequence_status,
          |   c.ai_generation_context,
          |   c.created_at,
          |   c.head_step_id,
          |   c.last_scheduled_at,
          |   c.email_priority,
          |   c.opt_out_msg,
          |   c.opt_out_is_text,
          |   c.team_id,
          |   c.shared_with_team,
          |   c.ta_id,
          |   c.max_emails_per_day,
          |   c.mark_completed_after_days,
          |   c.open_tracking_enabled,
          |   c.warmup_started_at,
          |   c.warmup_starting_email_count,
          |   c.warmup_length_in_days,
          |   c.click_tracking_enabled,
          |   c.latest_email_scheduled_at,
          |   c.append_followups,
          |   c.status_changed_at,
          |   c.will_delete,
          |   c.will_delete_at,
          |   c.enable_email_validation,
          |   c.ab_testing_enabled,
          |   c.add_prospect_to_dnc_on_opt_out,
          |   c.schedule_start_at,
          |   c.schedule_start_at_tz,
          |   c.last_analysed_at,
          |   c.pushed_to_queue_for_analysis_at,
          |   c.sending_holiday_calendar_id,
          |   -- c.next_to_be_scheduled_at,
          |   c.last_email_sent_at,
          |   c.latest_campaign_send_start_reports_id,
          |   c.pushed_to_queue_for_analysing_start_report_at,
          |   c.in_queue_for_analyzing_start_report,
          |   c.last_pushed_for_preemailvalidation_at,
          |   c.uuid,
          |   c.calendar_event_type_id,
          |   c.calendar_event_type_slug,
          |   c.calendar_is_individual,
          |   c.calendar_selected_user_id,
          |   c.calendar_selected_username_slug,
          |   c.calendar_smartreach_account_id,
          |   c.calendar_team_id,
          |   c.send_plain_text_email,
          |   c.campaign_type,
          |   c.calendar_team_slug,
          |   c.sending_mode,
          |   (
          |      SELECT
          |        json_agg(
          |          json_build_object(
          |            'id', ces.id,
          |            'campaign_id', ces.campaign_id,
          |            'sender_email_setting_id', ces.sender_email_setting_id,
          |            'receiver_email_setting_id', ces.receiver_email_setting_id,
          |            'team_id', ces.team_id,
          |            'uuid', ces.uuid,
          |            'sender_email', sender.email,
          |            'receiver_email', receiver.email,
          |            'from_name', sender.sender_name,
          |            'signature', sender.signature,
          |            'max_emails_per_day_from_email_account', sender.quota_per_day,
          |            'error', (
          |              CASE WHEN (sender.paused_till IS NOT NULL
          |                AND sender.paused_till > now()) THEN
          |                sender.error
          |              WHEN (receiver.paused_till IS NOT NULL
          |                AND receiver.paused_till > now()) THEN
          |                receiver.error
          |              WHEN (sender.is_under_review IS TRUE
          |                OR receiver.is_under_review IS TRUE) THEN
          |                'Your email account is under manual review. Please contact support.'
          |              WHEN (a.active IS FALSE) THEN
          |                concat('Campaign owner''s (', a.email, ') account has been deactivated by your team''s admin. Please change the owner to restart sending.')
          |              WHEN (sender_owner.active IS FALSE) THEN
          |                concat('You are using an email account (', sender.email, ') whose owner''s SmartReach.io account (', sender_owner.email, ') has been deactivated by your team''s admin. Please change the email account''s owner to restart sending from this campaign.')
          |              WHEN (receiver_owner.active IS FALSE) THEN
          |                concat('You are using an email account (', receiver.email, ') whose owner''s SmartReach.io account (', receiver_owner.email, ') has been deactivated by your team''s admin. Please change the email account''s owner to restart sending from this campaign.')
          |              WHEN (sender_domain_check.is_in_spam_blacklist IS TRUE) THEN
          |                'Your sending email domain is found in a global spam blacklist. Please check the status by going to Settings -> Team Settings -> Domain Health.'
          |              ELSE
          |                NULL
          |              END))) AS campaign_email_settings
          |      FROM
          |        campaign_email_settings ces
          |        JOIN email_settings sender ON sender.id = ces.sender_email_setting_id
          |          AND ces.team_id = sender.team_id
          |        JOIN email_settings receiver ON receiver.id = ces.receiver_email_setting_id
          |          AND ces.team_id = receiver.team_id
          |      LEFT JOIN accounts sender_owner ON sender.account_id = sender_owner.id
          |      LEFT JOIN accounts receiver_owner ON receiver.account_id = receiver_owner.id
          |      LEFT JOIN domain_health_checks sender_domain_check ON sender_domain_check.domain = sender.email_address_host
          |    WHERE
          |      ces.campaign_id = c.id
          |      and sender.status = ?
          |      and receiver.status = ?
          |      AND ces.team_id = c.team_id) AS campaign_email_settings
          |       ,
          |        null as ctags
          |      ,
          |      (
          |                SELECT json_agg(
          |                  json_build_object(
          |                    'channel_setting_uuid', ccs.channel_settings_uuid,
          |                    'team_id', c.team_id,
          |                    'email', ls.email,
          |                    'first_name', ls.first_name,
          |                    'last_name',  ls.last_name,
          |                    'linkedin_profile_url', ls.profile_url,
          |                    'automation_enabled', ls.captain_data_account_id IS NOT NULL
          |                  )) AS campaign_linkedin_settings
          |                FROM linkedin_settings ls
          |                LEFT JOIN campaign_channel_settings ccs on ( ccs.channel_settings_uuid = ls.uuid AND ccs.channel_type = ?)
          |                WHERE ccs.team_id = c.team_id AND ccs.campaign_id = c.id) AS campaign_linkedin_settings
          |              ,
          |             (
          |                SELECT json_agg(
          |                  json_build_object(
          |                    'channel_setting_uuid', ccs.channel_settings_uuid,
          |                    'team_id', c.team_id,
          |                    'phone_number', cs.phone_number,
          |                    'first_name', cs.first_name,
          |                    'last_name',  cs.last_name
          |                  )) AS campaign_call_settings
          |                FROM call_settings cs
          |                LEFT JOIN campaign_channel_settings ccs on ( ccs.channel_settings_uuid = cs.uuid AND ccs.channel_type = ?)
          |                WHERE ccs.team_id = c.team_id AND ccs.campaign_id = c.id) AS campaign_call_settings
          |                ,
          |              (
          |                SELECT json_agg(
          |                  json_build_object(
          |                    'channel_setting_uuid', ccs.channel_settings_uuid,
          |                    'team_id', c.team_id,
          |                    'phone_number', ws.whatsapp_number,
          |                    'first_name', ws.first_name,
          |                    'last_name',  ws.last_name
          |                  )) AS campaign_whatsapp_settings
          |                FROM whatsapp_settings ws
          |                LEFT JOIN campaign_channel_settings ccs on ( ccs.channel_settings_uuid = ws.uuid AND ccs.channel_type = ?)
          |                WHERE ccs.team_id = c.team_id AND ccs.campaign_id = c.id) AS campaign_whatsapp_settings
          |                ,
          |                (
          |                SELECT json_agg(
          |                  json_build_object(
          |                    'channel_setting_uuid', ccs.channel_settings_uuid,
          |                    'team_id', c.team_id,
          |                    'phone_number', ss.phone_number,
          |                    'first_name', ss.first_name,
          |                    'last_name',  ss.last_name
          |                  )) AS campaign_sms_settings
          |                FROM sms_settings ss
          |                LEFT JOIN campaign_channel_settings ccs on ( ccs.channel_settings_uuid = ss.uuid AND ccs.channel_type = ?)
          |                WHERE ccs.team_id = c.team_id AND ccs.campaign_id = c.id) AS campaign_sms_settings
          |                ,
          |
          |
          |      (
          |                SELECT EXISTS(
          |                      SELECT step_type from campaign_steps cs WHERE cs.campaign_id = c.id AND step_type IN
          |                   (
          |                        ?,
          |                        ?
          |                   )
          |                )
          |            ) as show_soft_start_setting ,
          |
          |
          |   has_spam_test.exists AS spam_test_exists,
          |   (SELECT EXISTS(
          |              SELECT cs.id FROM campaign_steps cs
          |              WHERE cs.campaign_id = c.id
          |              AND (
          |                cs.step_type = ?
          |                OR
          |                cs.step_type = ?
          |              )
          |            )) AS campaign_has_email_step,
          |
          |   (case when c.warmup_started_at is null or c.warmup_length_in_days is null THEN false
          |     else c.warmup_started_at + c.warmup_length_in_days * interval '1 day' > now() end) as warmup_is_on
          |
          |
          | FROM campaigns c
          |   INNER JOIN accounts a ON a.id = c.account_id
          |
          |   LEFT JOIN triggers trig ON (trig.campaign_id = c.id AND trig.source = ?)
          |
          |   LEFT JOIN LATERAL (
          |    SELECT EXISTS(SELECT id FROM spam_tests spamtest WHERE spamtest.campaign_id = c.id)
          |   ) AS has_spam_test ON true
          |
          | WHERE
          |        c.team_id = ?
          |        AND c.account_id IN (?)
          |        AND c.created_at AT TIME ZONE 'UTC' < ?
          |
          |      GROUP BY c.id, a.id,has_spam_test.exists
          |
          |       ORDER BY c.created_at desc
          |
          |      LIMIT ?
          |
        """.stripMargin.split(s"\\s+").reduce((s1, s2) => {
          s1 + " " + s2
        })

      val res = CampaignQuery.getCampaignsV3QuerySQL(
        teamId = 15,
        orgId = 1,
        search_params = search_params,
        Logger = logger,
        permittedAccountIds = Seq(17L)
      )

      val result = res.get.statement.split(s"\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })

      assert(expectedResult == result)

    }

    it("should return c_tags  and search by name params also.") {

      val expectedResult =
        """
          | SELECT
          |   a.email AS owner_email,
          |   a.id AS owner_account_id,
          |   CONCAT(a.first_name, ' ',  a.last_name) AS owner_name,
          |
          |   COALESCE(json_agg(trig) FILTER (WHERE trig.id IS NOT NULL), '[]') AS triggers,
          |   c.id,
          |   c.account_id,
          |   c.name,
          |   c.status,
          |   c.is_archived,
          |   c.timezone,
          |   c.daily_from_time,
          |   c.daily_till_time,
          |   c.days_preference,
          |   c.ai_sequence_status,
          |   c.ai_generation_context,
          |   c.created_at,
          |   c.head_step_id,
          |   c.last_scheduled_at,
          |   c.email_priority,
          |   c.opt_out_msg,
          |   c.opt_out_is_text,
          |   c.team_id,
          |   c.shared_with_team,
          |   c.ta_id,
          |   c.max_emails_per_day,
          |   c.mark_completed_after_days,
          |   c.open_tracking_enabled,
          |   c.warmup_started_at,
          |   c.warmup_starting_email_count,
          |   c.warmup_length_in_days,
          |   c.click_tracking_enabled,
          |   c.latest_email_scheduled_at,
          |   c.append_followups,
          |   c.status_changed_at,
          |   c.will_delete,
          |   c.will_delete_at,
          |   c.enable_email_validation,
          |   c.ab_testing_enabled,
          |   c.add_prospect_to_dnc_on_opt_out,
          |   c.schedule_start_at,
          |   c.schedule_start_at_tz,
          |   c.last_analysed_at,
          |   c.pushed_to_queue_for_analysis_at,
          |   c.sending_holiday_calendar_id,
          |   -- c.next_to_be_scheduled_at,
          |   c.last_email_sent_at,
          |   c.latest_campaign_send_start_reports_id,
          |   c.pushed_to_queue_for_analysing_start_report_at,
          |   c.in_queue_for_analyzing_start_report,
          |   c.last_pushed_for_preemailvalidation_at,
          |   c.uuid,
          |   c.calendar_event_type_id,
          |   c.calendar_event_type_slug,
          |   c.calendar_is_individual,
          |   c.calendar_selected_user_id,
          |   c.calendar_selected_username_slug,
          |   c.calendar_smartreach_account_id,
          |   c.calendar_team_id,
          |   c.send_plain_text_email,
          |   c.campaign_type,
          |   c.calendar_team_slug,
          |   c.sending_mode,
          |   (
          |      SELECT
          |        json_agg(
          |          json_build_object(
          |            'id', ces.id,
          |            'campaign_id', ces.campaign_id,
          |            'sender_email_setting_id', ces.sender_email_setting_id,
          |            'receiver_email_setting_id', ces.receiver_email_setting_id,
          |            'team_id', ces.team_id,
          |            'uuid', ces.uuid,
          |            'sender_email', sender.email,
          |            'receiver_email', receiver.email,
          |            'from_name', sender.sender_name,
          |            'signature', sender.signature,
          |            'max_emails_per_day_from_email_account', sender.quota_per_day,
          |            'error', (
          |              CASE WHEN (sender.paused_till IS NOT NULL
          |                AND sender.paused_till > now()) THEN
          |                sender.error
          |              WHEN (receiver.paused_till IS NOT NULL
          |                AND receiver.paused_till > now()) THEN
          |                receiver.error
          |              WHEN (sender.is_under_review IS TRUE
          |                OR receiver.is_under_review IS TRUE) THEN
          |                'Your email account is under manual review. Please contact support.'
          |              WHEN (a.active IS FALSE) THEN
          |                concat('Campaign owner''s (', a.email, ') account has been deactivated by your team''s admin. Please change the owner to restart sending.')
          |              WHEN (sender_owner.active IS FALSE) THEN
          |                concat('You are using an email account (', sender.email, ') whose owner''s SmartReach.io account (', sender_owner.email, ') has been deactivated by your team''s admin. Please change the email account''s owner to restart sending from this campaign.')
          |              WHEN (receiver_owner.active IS FALSE) THEN
          |                concat('You are using an email account (', receiver.email, ') whose owner''s SmartReach.io account (', receiver_owner.email, ') has been deactivated by your team''s admin. Please change the email account''s owner to restart sending from this campaign.')
          |              WHEN (sender_domain_check.is_in_spam_blacklist IS TRUE) THEN
          |                'Your sending email domain is found in a global spam blacklist. Please check the status by going to Settings -> Team Settings -> Domain Health.'
          |              ELSE
          |                NULL
          |              END))) AS campaign_email_settings
          |      FROM
          |        campaign_email_settings ces
          |        JOIN email_settings sender ON sender.id = ces.sender_email_setting_id
          |          AND ces.team_id = sender.team_id
          |        JOIN email_settings receiver ON receiver.id = ces.receiver_email_setting_id
          |          AND ces.team_id = receiver.team_id
          |      LEFT JOIN accounts sender_owner ON sender.account_id = sender_owner.id
          |      LEFT JOIN accounts receiver_owner ON receiver.account_id = receiver_owner.id
          |      LEFT JOIN domain_health_checks sender_domain_check ON sender_domain_check.domain = sender.email_address_host
          |    WHERE
          |      ces.campaign_id = c.id
          |      and sender.status = ?
          |      and receiver.status = ?
          |      AND ces.team_id = c.team_id) AS campaign_email_settings
          |       ,
          |        (
          |            SELECT
          |              json_agg(json_build_object('tag_id', tlfc.id, 'tag', tlfc.tag, 'tag_uuid', tlfc.uuid)) AS camtags
          |            FROM
          |              tags_list_for_campaigns tlfc
          |              JOIN tags_campaigns ON (
          |                  tlfc.id = tags_campaigns.tag_id
          |                  AND tlfc.team_id = ?
          |                  AND tags_campaigns.campaign_id = c.id
          |                  AND tags_campaigns.team_id = ?
          |              )
          |					) AS ctags
          |      ,
          |      (
          |                SELECT json_agg(
          |                  json_build_object(
          |                    'channel_setting_uuid', ccs.channel_settings_uuid,
          |                    'team_id', c.team_id,
          |                    'email', ls.email,
          |                    'first_name', ls.first_name,
          |                    'last_name',  ls.last_name,
          |                    'linkedin_profile_url', ls.profile_url,
          |                    'automation_enabled', ls.captain_data_account_id IS NOT NULL
          |                  )) AS campaign_linkedin_settings
          |                FROM linkedin_settings ls
          |                LEFT JOIN campaign_channel_settings ccs on ( ccs.channel_settings_uuid = ls.uuid AND ccs.channel_type = ?)
          |                WHERE ccs.team_id = c.team_id AND ccs.campaign_id = c.id) AS campaign_linkedin_settings
          |              ,
          |             (
          |                SELECT json_agg(
          |                  json_build_object(
          |                    'channel_setting_uuid', ccs.channel_settings_uuid,
          |                    'team_id', c.team_id,
          |                    'phone_number', cs.phone_number,
          |                    'first_name', cs.first_name,
          |                    'last_name',  cs.last_name
          |                  )) AS campaign_call_settings
          |                FROM call_settings cs
          |                LEFT JOIN campaign_channel_settings ccs on ( ccs.channel_settings_uuid = cs.uuid AND ccs.channel_type = ?)
          |                WHERE ccs.team_id = c.team_id AND ccs.campaign_id = c.id) AS campaign_call_settings
          |                ,
          |              (
          |                SELECT json_agg(
          |                  json_build_object(
          |                    'channel_setting_uuid', ccs.channel_settings_uuid,
          |                    'team_id', c.team_id,
          |                    'phone_number', ws.whatsapp_number,
          |                    'first_name', ws.first_name,
          |                    'last_name',  ws.last_name
          |                  )) AS campaign_whatsapp_settings
          |                FROM whatsapp_settings ws
          |                LEFT JOIN campaign_channel_settings ccs on ( ccs.channel_settings_uuid = ws.uuid AND ccs.channel_type = ?)
          |                WHERE ccs.team_id = c.team_id AND ccs.campaign_id = c.id) AS campaign_whatsapp_settings
          |                ,
          |                (
          |                SELECT json_agg(
          |                  json_build_object(
          |                    'channel_setting_uuid', ccs.channel_settings_uuid,
          |                    'team_id', c.team_id,
          |                    'phone_number', ss.phone_number,
          |                    'first_name', ss.first_name,
          |                    'last_name',  ss.last_name
          |                  )) AS campaign_sms_settings
          |                FROM sms_settings ss
          |                LEFT JOIN campaign_channel_settings ccs on ( ccs.channel_settings_uuid = ss.uuid AND ccs.channel_type = ?)
          |                WHERE ccs.team_id = c.team_id AND ccs.campaign_id = c.id) AS campaign_sms_settings
          |                ,
          |
          |      (
          |                SELECT EXISTS(
          |                      SELECT step_type from campaign_steps cs WHERE cs.campaign_id = c.id AND step_type IN
          |                   (
          |                        ?,
          |                        ?
          |                   )
          |                )
          |            ) as show_soft_start_setting ,
          |
          |
          |   has_spam_test.exists AS spam_test_exists,
          |   (SELECT EXISTS(
          |              SELECT cs.id FROM campaign_steps cs
          |              WHERE cs.campaign_id = c.id
          |              AND (
          |                cs.step_type = ?
          |                OR
          |                cs.step_type = ?
          |              )
          |            )) AS campaign_has_email_step,
          |
          |   (case when c.warmup_started_at is null or c.warmup_length_in_days is null THEN false
          |     else c.warmup_started_at + c.warmup_length_in_days * interval '1 day' > now() end) as warmup_is_on
          |
          |
          | FROM campaigns c
          |   INNER JOIN accounts a ON a.id = c.account_id
          |
          |   LEFT JOIN triggers trig ON (trig.campaign_id = c.id AND trig.source = ?)
          |
          |   LEFT JOIN LATERAL (
          |    SELECT EXISTS(SELECT id FROM spam_tests spamtest WHERE spamtest.campaign_id = c.id)
          |   ) AS has_spam_test ON true
          |
          | WHERE
          |        c.team_id = ?
          |        AND c.account_id IN (?)
          |        AND c.name ILIKE ?
          |        AND c.created_at AT TIME ZONE 'UTC' < ?
          |
          |      GROUP BY c.id, a.id,has_spam_test.exists
          |
          |       ORDER BY c.created_at desc
          |
          |      LIMIT ?
          |
        """.stripMargin.split(s"\\s+").reduce((s1, s2) => {
          s1 + " " + s2
        })

      val res = CampaignQuery.getCampaignsV3QuerySQL(
        teamId = 15,
        orgId = 2,
        search_params = search_params.copy(name = Some("Hayabusa")),
        Logger = logger,
        permittedAccountIds = Seq(17L)
      )

      val result = res.get.statement.split(s"\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })

      assert(expectedResult == result)

    }


  }
  // getCampaignsV3QuerySQL

}
