package app.api.search

import api.accounts.models.{AccountProfileInfo, SignupType}
import api.accounts.{Account, AccountAccess, AccountMetadata, AccountType, AccountUuid, OrgCountData, OrgMetadata, OrgPlan, OrgSettings, OrganizationWithCurrentData, PermType, PermissionLevelForValidation, PermissionOwnershipV2, ProspectCategoriesInDB, ReplyHandling, RolePermV2, RolePermissionDataV2, RolePermissionsInDBV2, RolePermissionsV2, TeamAccount, TeamAccountRole, TeamMember, TeamMemberLite, TeamsBelongingToAccount}
import io.sr.billing_common.models.{PlanID, PlanType}
import api.calendar_app.models.CalendarAccountData
import api.columns.{FieldTypeEnum, ProspectColumnDef, ProspectColumnDefDAO}
import api.prospects.SearchQuerySelectType
import api.prospects.models.ProspectCategoryRank
import api.search.{ProspectQuery, SearchQuery, SearchQueryColumnOperator, SearchQueryMainClause, SearchQueryOperators, SearchQuerySubClause, SearchQuerySubFilterData}
import api.team.TeamUuid
import app.test_fixtures.accounts.OrgCountDataFixture
import app.test_fixtures.organizationa.OrgMetadataFixture
import app_services.sr_rate_limiter.dao.SrRateLimiterTenantId.AccountId
import eventframework.SrResourceTypes
import org.joda.time.DateTime
import org.postgresql.util.PGobject
import org.scalamock.scalatest.MockFactory
import org.scalatest.funspec.AnyFunSpec
import play.api.libs.json.Json
import utils.SRLogger
import views.html.emails.invite

class ProspectQuerySpec extends AnyFunSpec with MockFactory {

  val prospectColumnDefDAO = mock[ProspectColumnDefDAO]
  val prospectColumnDef = mock[ProspectColumnDef]
  val prospectQuery = new ProspectQuery(
    prospectColumnDef = prospectColumnDef
  )
  val Logger = new SRLogger("ProspectQuerySpec")

  private val orgMetadata = OrgMetadataFixture.orgMetadata_default

  describe("getQuerySQL") {
    val account = Account(
      id = AccountUuid("acc_2X9VcNvPj9OBXH9B5gIreJEipbn"),
      internal_id = 90,
      email = "<EMAIL>",
      email_verification_code = None,
      email_verification_code_created_at = None,
      created_at = DateTime.now(),
      first_name = Some("Prachi"),
      last_name = Some("Mane"),
      Some("Test Company"),
      Some("Asia/Kolkata"),
      AccountProfileInfo(
        "Prachi",
        "Mane",
        Some("Test Company"),
        Some("Asia/Kolkata"),
        None,
        None,
        None,
        None,
        false,
        false,
        None,
        Some("<EMAIL>")
      ),
      None,
      List(
        TeamAccount(
          80,
          2,
          team_uuid = TeamUuid("uuid"),
          Some(RolePermissionsInDBV2(77, TeamAccountRole.ADMIN, PermissionOwnershipV2.NoAccess, PermissionOwnershipV2.All, PermissionOwnershipV2.All, PermissionOwnershipV2.All, PermissionOwnershipV2.All, PermissionOwnershipV2.All, PermissionOwnershipV2.All, PermissionOwnershipV2.All, PermissionOwnershipV2.All, PermissionOwnershipV2.All, PermissionOwnershipV2.All, PermissionOwnershipV2.All, PermissionOwnershipV2.All, PermissionOwnershipV2.All, PermissionOwnershipV2.All, PermissionOwnershipV2.All, PermissionOwnershipV2.All, PermissionOwnershipV2.All, PermissionOwnershipV2.All, PermissionOwnershipV2.All, PermissionOwnershipV2.All, PermissionOwnershipV2.All, PermissionOwnershipV2.All, PermissionOwnershipV2.NoAccess, PermissionOwnershipV2.NoAccess, PermissionOwnershipV2.All, PermissionOwnershipV2.All, PermissionOwnershipV2.All, PermissionOwnershipV2.All, PermissionOwnershipV2.All, PermissionOwnershipV2.All, PermissionOwnershipV2.All, PermissionOwnershipV2.All, PermissionOwnershipV2.All, PermissionOwnershipV2.All, PermissionOwnershipV2.All)),
          Some(RolePermissionDataV2(
            77,
            TeamAccountRole.ADMIN,
            RolePermissionsV2(
              RolePermV2(PermissionOwnershipV2.All, SrResourceTypes.OTHER, PermissionLevelForValidation.IgnoreValidation, PermType.JUST_LOGGEDIN),
              RolePermV2(PermissionOwnershipV2.All, SrResourceTypes.OTHER, PermissionLevelForValidation.IgnoreValidation, PermType.ZAPIER_ACCESS),
              RolePermV2(PermissionOwnershipV2.NoAccess, SrResourceTypes.OTHER, PermissionLevelForValidation.IgnoreValidation, PermType.MANAGE_BILLING),
              RolePermV2(PermissionOwnershipV2.All, SrResourceTypes.USER, PermissionLevelForValidation.View, PermType.VIEW_USER_MANAGEMENT),
              RolePermV2(PermissionOwnershipV2.All, SrResourceTypes.USER, PermissionLevelForValidation.Edit, PermType.EDIT_USER_MANAGEMENT),
              RolePermV2(PermissionOwnershipV2.All, SrResourceTypes.TEAM, PermissionLevelForValidation.View, PermType.VIEW_TEAM_CONFIG),
              RolePermV2(PermissionOwnershipV2.All, SrResourceTypes.TEAM, PermissionLevelForValidation.Edit, PermType.EDIT_TEAM_CONFIG),
              RolePermV2(PermissionOwnershipV2.All, SrResourceTypes.PROSPECT, PermissionLevelForValidation.View, PermType.VIEW_PROSPECTS),
              RolePermV2(PermissionOwnershipV2.All, SrResourceTypes.PROSPECT, PermissionLevelForValidation.Edit, PermType.EDIT_PROSPECTS),
              RolePermV2(PermissionOwnershipV2.All, SrResourceTypes.PROSPECT, PermissionLevelForValidation.Delete, PermType.DELETE_PROSPECTS),
              RolePermV2(PermissionOwnershipV2.All, SrResourceTypes.CAMPAIGN, PermissionLevelForValidation.View, PermType.VIEW_CAMPAIGNS),
              RolePermV2(PermissionOwnershipV2.All, SrResourceTypes.CAMPAIGN, PermissionLevelForValidation.Edit, PermType.EDIT_CAMPAIGNS),
              RolePermV2(PermissionOwnershipV2.All, SrResourceTypes.CAMPAIGN, PermissionLevelForValidation.Delete, PermType.DELETE_CAMPAIGNS),
              RolePermV2(PermissionOwnershipV2.All, SrResourceTypes.CAMPAIGN, PermissionLevelForValidation.Edit, PermType.CHANGE_CAMPAIGN_STATUS),
              RolePermV2(PermissionOwnershipV2.All, SrResourceTypes.REPORT, PermissionLevelForValidation.View, PermType.VIEW_REPORTS),
              RolePermV2(PermissionOwnershipV2.All, SrResourceTypes.REPORT, PermissionLevelForValidation.Edit, PermType.fromKey("edit_reports").get),
              RolePermV2(PermissionOwnershipV2.All, SrResourceTypes.REPORT, PermissionLevelForValidation.Edit, PermType.fromKey("download_reports").get),
              RolePermV2(PermissionOwnershipV2.All, SrResourceTypes.INBOX, PermissionLevelForValidation.Edit, PermType.fromKey("send_manual_email").get),
              RolePermV2(PermissionOwnershipV2.All, SrResourceTypes.TEMPLATE, PermissionLevelForValidation.View, PermType.fromKey("view_templates").get),
              RolePermV2(PermissionOwnershipV2.All, SrResourceTypes.TEMPLATE, PermissionLevelForValidation.Edit, PermType.fromKey("edit_templates").get),
              RolePermV2(PermissionOwnershipV2.All, SrResourceTypes.TEMPLATE, PermissionLevelForValidation.Delete, PermType.fromKey("delete_templates").get),
              RolePermV2(PermissionOwnershipV2.All, SrResourceTypes.BLACKLIST, PermissionLevelForValidation.View, PermType.fromKey("view_blacklist").get),
              RolePermV2(PermissionOwnershipV2.All, SrResourceTypes.BLACKLIST, PermissionLevelForValidation.Edit, PermType.fromKey("edit_blacklist").get),
              RolePermV2(PermissionOwnershipV2.All, SrResourceTypes.WORKFLOW, PermissionLevelForValidation.View, PermType.fromKey("view_workflows").get),
              RolePermV2(PermissionOwnershipV2.All, SrResourceTypes.WORKFLOW, PermissionLevelForValidation.Edit, PermType.fromKey("edit_workflows").get),
              RolePermV2(PermissionOwnershipV2.NoAccess, SrResourceTypes.WEBHOOK, PermissionLevelForValidation.View, PermType.fromKey("view_webhooks").get),
              RolePermV2(PermissionOwnershipV2.NoAccess, SrResourceTypes.WEBHOOK, PermissionLevelForValidation.Edit, PermType.fromKey("edit_webhooks").get),
//              RolePermV2(PermissionOwnershipV2.All, SrResourceTypes.PROSPECT_ACCOUNT, PermissionLevelForValidation.View, PermType.fromKey("view_prospect_accounts").get),
//              RolePermV2(PermissionOwnershipV2.All, SrResourceTypes.PROSPECT_ACCOUNT, PermissionLevelForValidation.Edit, PermType.fromKey("edit_prospect_accounts").get),
              RolePermV2(PermissionOwnershipV2.All, SrResourceTypes.CHANNEL, PermissionLevelForValidation.View, PermType.fromKey("view_channels").get),
              RolePermV2(PermissionOwnershipV2.All, SrResourceTypes.CHANNEL, PermissionLevelForValidation.Edit, PermType.fromKey("edit_channels").get),
              RolePermV2(PermissionOwnershipV2.All, SrResourceTypes.CHANNEL, PermissionLevelForValidation.Delete, PermType.fromKey("delete_channels").get),
              RolePermV2(PermissionOwnershipV2.All, SrResourceTypes.TASK, PermissionLevelForValidation.View, PermType.fromKey("view_tasks").get),
              RolePermV2(PermissionOwnershipV2.All, SrResourceTypes.TASK, PermissionLevelForValidation.Edit, PermType.fromKey("edit_tasks").get),
              RolePermV2(PermissionOwnershipV2.All, SrResourceTypes.TASK, PermissionLevelForValidation.Delete, PermType.fromKey("delete_tasks").get),
              RolePermV2(PermissionOwnershipV2.All, SrResourceTypes.PIPELINE, PermissionLevelForValidation.Edit, PermType.fromKey("edit_pipeline_details").get),
              RolePermV2(PermissionOwnershipV2.All, SrResourceTypes.PIPELINE, PermissionLevelForValidation.Delete, PermType.fromKey("delete_pipeline_details").get),
              RolePermV2(PermissionOwnershipV2.All, SrResourceTypes.OPPORTUNITY, PermissionLevelForValidation.View, PermType.fromKey("view_opportunities").get),
              RolePermV2(PermissionOwnershipV2.All, SrResourceTypes.OPPORTUNITY, PermissionLevelForValidation.Edit, PermType.fromKey("edit_opportunities").get),
              RolePermV2(PermissionOwnershipV2.All, SrResourceTypes.OPPORTUNITY, PermissionLevelForValidation.Delete, PermType.fromKey("delete_opportunities").get)
            ))),
          true,
          true,
          "Test Company",
          19,
          List(
            TeamMember(2, "Test Company", 2, 2, Some("Prateek"), Some("B"), "<EMAIL>", TeamAccountRole.OWNER, None, Some("zapier_key")),
            TeamMember(2, "Test Company", 7, 6, Some("Michael"), Some("Chandler"), "<EMAIL>", TeamAccountRole.ADMIN, None, Some("zapier_key")),
            TeamMember(2, "Test Company", 8, 7, Some("Jeswanth"), Some("Reddy"), "<EMAIL>", TeamAccountRole.MEMBER, None, Some("zapier_key")),
            TeamMember(2, "Test Company", 9, 11, Some("Abhilash"), Some("Betanamudi"), "<EMAIL>", TeamAccountRole.ADMIN, None, Some("zapier_key")),
            TeamMember(2, "Test Company", 10, 12, Some("sreejit"), Some("s"), "<EMAIL>", TeamAccountRole.MEMBER, None, Some("zapier_key")),
            TeamMember(2, "Test Company", 21, 36, Some("Sathish"), Some("Mukkoju"), "<EMAIL>", TeamAccountRole.ADMIN, None, Some("zapier_key")),
            TeamMember(2, "Test Company", 28, 42, Some("Sathish"), Some("Mukkoju"), "<EMAIL>", TeamAccountRole.ADMIN, None, Some("zapier_key")),
            TeamMember(2, "Test Company", 30, 44, Some("Sathish"), Some("Mukkoju"), "<EMAIL>", TeamAccountRole.ADMIN, None, Some("zapier_key")),
            TeamMember(2, "Test Company", 37, 64, Some("Sathish"), Some("Mukkoju"), "<EMAIL>", TeamAccountRole.MEMBER, None, Some("zapier_key")),
            TeamMember(2, "Test Company", 82, 113, Some("ShubhamSR"), Some("Kudekar"), "<EMAIL>", TeamAccountRole.ADMIN, None, Some("zapier_key")),
            TeamMember(2, "Test Company", 90, 121, Some("AnimeshSR"), Some("Kumar"), "<EMAIL>", TeamAccountRole.ADMIN, None, Some("zapier_key")),
            TeamMember(2, "Test Company", 103, 138, Some("NarutoSR"), Some("Uzumaki"), "<EMAIL>", TeamAccountRole.ADMIN, None, Some("zapier_key")),
            TeamMember(2, "Test Company", 104, 139, Some("HinataSR"), Some("Huga"), "<EMAIL>", TeamAccountRole.MEMBER, None, Some("zapier_key")),
            TeamMember(2, "Test Company", 112, 147, Some("ItachiSR"), Some("Uchiha"), "<EMAIL>", TeamAccountRole.ADMIN, None, Some("zapier_key")),
            TeamMember(2, "Test Company", 113, 148, Some("HinataSR"), Some("Huga"), "<EMAIL>", TeamAccountRole.MEMBER, None, Some("zapier_key")),
            TeamMember(2, "Test Company", 114, 149, Some("ItachiSR"), Some("Uchiha"), "<EMAIL>", TeamAccountRole.MEMBER, None, Some("zapier_key")),
            TeamMember(2, "Test Company", 396, 504, Some(""), Some(""), "<EMAIL>", TeamAccountRole.MEMBER, None, Some("zapier_key")),
            TeamMember(2, "Test Company", 431, 539, Some("Chandler"), Some("Friends"), "<EMAIL>", TeamAccountRole.ADMIN, None, Some("zapier_key")),
            TeamMember(2, "Test Company", 442, 550, Some(""), Some(""), "<EMAIL>", TeamAccountRole.MEMBER, None, Some("zapier_key"))
          ),
          List(
            TeamMemberLite(2, Some("Prateek"), Some("B"), "<EMAIL>", active = true, timezone = Some("campaignTimezone"), twofa_enabled = true, created_at = DateTime.now(), user_uuid = AccountUuid("uuid"), team_role = TeamAccountRole.ADMIN),
            TeamMemberLite(7, Some("Michael"), Some("Chandler"), "<EMAIL>", active = true, timezone = Some("campaignTimezone"), twofa_enabled = true, created_at = DateTime.now(), user_uuid = AccountUuid("uuid"), team_role = TeamAccountRole.ADMIN),
            TeamMemberLite(8, Some("Jeswanth"), Some("Reddy"), "<EMAIL>", false, timezone = Some("campaignTimezone"), twofa_enabled = true, created_at = DateTime.now(), user_uuid = AccountUuid("uuid"), team_role = TeamAccountRole.ADMIN),
            TeamMemberLite(9, Some("Abhilash"), Some("Betanamudi"), "<EMAIL>", false, timezone = Some("campaignTimezone"), twofa_enabled = true, created_at = DateTime.now(), user_uuid = AccountUuid("uuid"), team_role = TeamAccountRole.ADMIN),
            TeamMemberLite(10, Some("sreejit"), Some("s"), "<EMAIL>", false, timezone = Some("campaignTimezone"), twofa_enabled = true, created_at = DateTime.now(), user_uuid = AccountUuid("uuid"), team_role = TeamAccountRole.ADMIN),
            TeamMemberLite(21, Some("Sathish"), Some("Mukkoju"), "<EMAIL>", false, timezone = Some("campaignTimezone"), twofa_enabled = true, created_at = DateTime.now(), user_uuid = AccountUuid("uuid"), team_role = TeamAccountRole.ADMIN),
            TeamMemberLite(28, Some("Sathish"), Some("Mukkoju"), "<EMAIL>", false, timezone = Some("campaignTimezone"), twofa_enabled = true, created_at = DateTime.now(), user_uuid = AccountUuid("uuid"), team_role = TeamAccountRole.ADMIN),
            TeamMemberLite(30, Some("Sathish"), Some("Mukkoju"), "<EMAIL>", false, timezone = Some("campaignTimezone"), twofa_enabled = true, created_at = DateTime.now(), user_uuid = AccountUuid("uuid"), team_role = TeamAccountRole.ADMIN),
            TeamMemberLite(37, Some("Sathish"), Some("Mukkoju"), "<EMAIL>", false, timezone = Some("campaignTimezone"), twofa_enabled = true, created_at = DateTime.now(), user_uuid = AccountUuid("uuid"), team_role = TeamAccountRole.ADMIN),
            TeamMemberLite(82, Some("ShubhamSR"), Some("Kudekar"), "<EMAIL>", true, timezone = Some("campaignTimezone"), twofa_enabled = true, created_at = DateTime.now(), user_uuid = AccountUuid("uuid"), team_role = TeamAccountRole.ADMIN),
            TeamMemberLite(90, Some("AnimeshSR"), Some("Kumar"), "<EMAIL>", true, timezone = Some("campaignTimezone"), twofa_enabled = true, created_at = DateTime.now(), user_uuid = AccountUuid("uuid"), team_role = TeamAccountRole.ADMIN),
            TeamMemberLite(103, Some("NarutoSR"), Some("Uzumaki"), "<EMAIL>", true, timezone = Some("campaignTimezone"), twofa_enabled = true, created_at = DateTime.now(), user_uuid = AccountUuid("uuid"), team_role = TeamAccountRole.ADMIN),
            TeamMemberLite(104, Some("HinataSR"), Some("Huga"), "<EMAIL>", true, timezone = Some("campaignTimezone"), twofa_enabled = true, created_at = DateTime.now(), user_uuid = AccountUuid("uuid"), team_role = TeamAccountRole.ADMIN),
            TeamMemberLite(112, Some("ItachiSR"), Some("Uchiha"), "<EMAIL>", true, timezone = Some("campaignTimezone"), twofa_enabled = true, created_at = DateTime.now(), user_uuid = AccountUuid("uuid"), team_role = TeamAccountRole.ADMIN),
            TeamMemberLite(113, Some("HinataSR"), Some("Huga"), "<EMAIL>", true, timezone = Some("campaignTimezone"), twofa_enabled = true, created_at = DateTime.now(), user_uuid = AccountUuid("uuid"), team_role = TeamAccountRole.ADMIN),
            TeamMemberLite(114, Some("ItachiSR"), Some("Uchiha"), "<EMAIL>", true, timezone = Some("campaignTimezone"), twofa_enabled = true, created_at = DateTime.now(), user_uuid = AccountUuid("uuid"), team_role = TeamAccountRole.ADMIN),
            TeamMemberLite(396, Some(""), Some(""), "<EMAIL>", true, timezone = Some("campaignTimezone"), twofa_enabled = true, created_at = DateTime.now(), user_uuid = AccountUuid("uuid"), team_role = TeamAccountRole.ADMIN),
            TeamMemberLite(431, Some("Chandler"), Some("Friends"), "<EMAIL>", true, timezone = Some("campaignTimezone"), twofa_enabled = true, created_at = DateTime.now(), user_uuid = AccountUuid("uuid"), team_role = TeamAccountRole.ADMIN),
            TeamMemberLite(442, Some(""), Some(""), "<EMAIL>", true, timezone = Some("campaignTimezone"), twofa_enabled = true, created_at = DateTime.now(), user_uuid = AccountUuid("uuid"), team_role = TeamAccountRole.ADMIN)),
          List(
            ProspectCategoriesInDB(26, "Converted", "converted", "#ab149e", true, 2, ProspectCategoryRank(rank = 1000)),
            ProspectCategoriesInDB(28, "Not now", "not_now", "#BB8FCE", true, 2, ProspectCategoryRank(rank = 2000)),
            ProspectCategoriesInDB(29, "Auto reply", "auto_reply", "#DC7633", false, 2, ProspectCategoryRank(rank = 3000)),
            ProspectCategoriesInDB(27, "Delivery failed", "delivery_failed", "#D98880", false, 2, ProspectCategoryRank(rank = 4000)),
            ProspectCategoriesInDB(30, "Do not contact", "do_not_contact", "#C0392B", false, 2, ProspectCategoryRank(rank = 5000)),
            ProspectCategoriesInDB(21, "Forwarded", "forwarded", "#F1C40F", false, 2, ProspectCategoryRank(rank = 6000)),
            ProspectCategoriesInDB(25, "Not categorized", "not_categorized", "#D5D8DC", false, 2, ProspectCategoryRank(rank = 7000)),
            ProspectCategoriesInDB(24, "Out of office", "out_of_office", "#5DADE2", false, 2, ProspectCategoryRank(rank = 8000))
          ),
          10,
          10,
          2,
          5,
          ReplyHandling.PAUSE_SPECIFIC_CAMPAIGN_ON_REPLY,
          None,
          DateTime.now()
        )
      ),
      AccountType.AGENCY,
      OrganizationWithCurrentData(
        2, "Test Company",
        2,
        counts = OrgCountDataFixture.orgCountData_default,
        OrgSettings(true, false, false, true, true, false, true,false,false),
        OrgPlan(None, false, None, None, None, DateTime.now().minusDays(100),None, None, None, PlanType.PAID, "ultimate", PlanID.ULTIMATE),
        true, DateTime.now().minusDays(100),
        None,
        None,
        None,
        List(),
        List(),
        false,
        orgMetadata
      ),
      true,
      "weekly",
      AccountMetadata(Some(true)),
      true,
      Some(SignupType.Password),
      AccountAccess(false),
      None
    )


    it("should build a query for sender_email") {
      val searchString = """{"custom_flags":{"active_campaigns_detailed":true},"page":1,"is_campaign":982,"query":{"owner_ids":[0],"clause":"AND","filters":[{"clause":"AND","filters":[{"field_display_name":"Current sender Email","field":"current_sender_email","allowedFilterOperators":[{"display_name":"Contains","key":"contains"},{"display_name":"Not contains","key":"not_contains"},{"display_name":"Equals","key":"equals"},{"display_name":"Not equals","key":"not_equals"}],"operator":"equals","value":"123","value_display_name":"123","field_type":"number","is_custom":false}]}]}}"""
      val searchJson = Json.parse(searchString).validate[SearchQuery].get
      val expected_result =
        """
          |SELECT
          |
          |          pt1.id,
          |          a.id AS owner_id,
          |          a.email AS owner_email,
          |          a.uuid AS owner_uuid,
          |          CONCAT(a.first_name, ' ',  a.last_name) AS owner_name,
          |
          |          pt1.prospect_uuid,
          |
          |          pt1.team_id,
          |
          |          pt1.first_name,
          |          pt1.last_name,
          |          pt1.email,
          |          pt1.email_domain,
          |
          |          pt1.invalid_email,
          |
          |          prospect_lists.name AS list,
          |          pt1.list_id,
          |          pt1.company,
          |          pt1.city,
          |          pt1.country,
          |          pt1.timezone,
          |
          |          pt1.created_at,
          |          pt1.updated_at,
          |          pt1.last_contacted_at,
          |          pt1.last_contacted_at_phone,
          |          pt1.last_replied_at,
          |          pt1.last_opened_at,
          |          pt1.total_opens,
          |          pt1.total_clicks,
          |          pt1.custom_fields,
          |          pt1.will_delete,
          |          pt1.email_bounced,
          |          pt1.force_send_invalid_email,
          |          pcat.name as prospect_category_custom,
          |          pcat.label_color as prospect_category_label_color,
          |          pcat.id as prospect_category_id_custom,
          |
          |          pt1.prospect_source,
          |          pt1.prospect_account_id as prospect_account_id,
          |          pa.uuid as prospect_account_uuid,
          |          pa.name as prospect_account_name,
          |
          |           COALESCE(ptags.prospecttags, '[]')  as tags,
          |           COALESCE(magic_columns.prospect_magic_columns, '[]')  as magic_columns,
          |
          |           null  as active_campaigns,
          |
          |          pt1.state,
          |          pt1.job_title,
          |          pt1.phone,
          |          pt1.phone_2,
          |          pt1.phone_3,
          |          pt1.linkedin_url,
          |          pt1.current_step_type,
          |          pt1.latest_reply_sentiment_uuid,
          |          pt1.latest_task_done_at,
          |          ccl.completed_at as last_call_made_at
          |
          |
          |          FROM
          |          (
          |
          |        SELECT
          |
          |         prospects.id,
          |          prospects.uuid AS prospect_uuid,
          |          prospects.account_id,
          |
          |          prospects.team_id,
          |
          |          prospects.first_name,
          |          prospects.last_name,
          |          pe.email,
          |          pe.email_domain,
          |
          |          (CASE WHEN pe.email_checked THEN pe.invalid_email ELSE NULL END) AS invalid_email,
          |
          |          prospects.list_id,
          |          prospects.list,
          |          prospects.company,
          |          prospects.city,
          |          prospects.country,
          |          prospects.timezone,
          |          prospects.sending_holiday_calendar_id,
          |
          |          prospects.created_at,
          |          prospects.updated_at,
          |          prospects.last_contacted_at,
          |          prospects.last_contacted_at_phone,
          |          prospects.last_replied_at,
          |          prospects.last_opened_at,
          |          prospects.total_opens,
          |          prospects.total_clicks,
          |          prospects.custom_fields,
          |          prospects.will_delete,
          |          pe.email_bounced,
          |          pe.force_send_invalid_email,
          |
          |          prospects.prospect_source,
          |          prospects.prospect_account_id as prospect_account_id,
          |          prospects.prospect_category_id_custom,
          |
          |          prospects.state,
          |          prospects.job_title,
          |          prospects.phone,
          |          prospects.phone_2,
          |          prospects.phone_3,
          |          prospects.linkedin_url,
          |          cp.current_step_type,
          |          prospects.latest_reply_sentiment_uuid,
          |          prospects.latest_task_done_at
          |
          |
          |          FROM prospects
          |           LEFT JOIN prospects_emails pe ON ((pe.prospect_id = prospects.id) AND (pe.team_id = prospects.team_id) AND pe.is_primary)
          |           INNER JOIN campaigns_prospects cp ON
          |                (
          |                  cp.prospect_id = prospects.id
          |                    AND
          |                    cp.active
          |                    AND
          |                    cp.campaign_id = ?
          |                    AND
          |                    cp.team_id = prospects.team_id
          |                )
          |
          |
          |
          |           WHERE
          |
          |           prospects.team_id = ?
          |
          |            AND prospects.account_id IN (?)
          |
          |           AND NOT prospects.will_delete
          |
          |
          |
          |            AND (  (  ( cp.current_campaign_email_settings_id = ? )  )  )
          |          ) pt1
          |
          |
          |
          |          INNER JOIN accounts a ON a.id = pt1.account_id
          |
          |          INNER JOIN prospect_categories_custom pcat on pcat.id = pt1.prospect_category_id_custom
          |
          |          LEFT JOIN prospect_accounts pa ON pa.id = pt1.prospect_account_id
          |
          |            LEFT JOIN LATERAL (
          |              SELECT
          |
          |              json_agg(
          |                json_build_object(
          |                  'tag_id', tags.id,
          |                  'tag', tags.tag,
          |                  'tag_uuid', tags.uuid
          |                  )
          |                ) as prospecttags
          |
          |              FROM tags
          |              JOIN tags_prospects ON tags_prospects.tag_id = tags.id
          |              WHERE tags_prospects.prospect_id = pt1.id
          |              AND NOT tags.hidden
          |
          |            ) AS ptags ON true
          |
          |
          |          LEFT JOIN LATERAL (
          |            SELECT
          |              json_agg(json_build_object(
          |               'column_output', cdp.magic_prompt_generated_output,
          |                'failed_message', cdp.failed_message,
          |                'column_name', cd.name,
          |                'status', cdp.status
          |              )) AS prospect_magic_columns
          |            FROM
          |              column_defs_prospects AS cdp
          |              JOIN column_defs AS cd ON cdp.column_id = cd.id
          |                AND cd.team_id = cdp.team_id
          |            WHERE
          |              cdp.prospect_id = pt1.id
          |              AND cdp.team_id = pt1.team_id
          |          ) AS magic_columns ON TRUE
          |
          |
          |          LEFT JOIN prospect_lists ON (
          |            prospect_lists.id = pt1.list_id
          |            AND prospect_lists.team_id = pt1.team_id
          |          )
          |
          |           LEFT JOIN LATERAL (
          |    SELECT completed_at
          |    FROM call_conference_logs
          |    WHERE primary_prospect_id = pt1.id
          |      AND team_id = pt1.team_id
          |    ORDER BY completed_at DESC
          |    LIMIT 1
          |  ) AS ccl ON TRUE
          |
          |
          |
          |
          |
          |          WHERE
          |
          |           pt1.team_id = ?
          |
          |
          |
          |          ORDER BY pt1.team_id, pt1.id DESC
          |
          |         LIMIT ? OFFSET ?
          |""".stripMargin

      val qry_str = prospectQuery.getQuerySQL(
        permittedAccountIds = Seq(2),
        teamId = 80,
        orgId = 39,
        data = searchJson,
        account = account,
        isInternalRequest = false,
        fetchType = SearchQuerySelectType.FULL_OBJECT,
        Logger = Logger,
//        emailNotCompulsoryEnabled = Some(false)
      )
      val split = expected_result.split(s"\\s+")
      val rhs = split.reduce((a1, a2) => {
        a1 + " " + a2
      })
      val lhs = qry_str.get._3.statement.split("\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })
      assert(lhs == rhs)
    }

    it("should build a query for email") {
      val searchString = """{"custom_flags":{"active_campaigns_detailed":true},"page":1,"is_campaign":982,"query":{"owner_ids":[0],"clause":"AND","filters":[{"clause":"AND","filters":[{"field_display_name":"Email","field":"email","allowedFilterOperators":[{"display_name":"Contains","key":"contains"},{"display_name":"Not contains","key":"not_contains"},{"display_name":"Equals","key":"equals"},{"display_name":"Not equals","key":"not_equals"}],"operator":"contains","value":"ani","value_display_name":"ani","field_type":"text","is_custom":false}]}]}}"""
      val searchJson = Json.parse(searchString).validate[SearchQuery].get
      val expected_result =
        """
          |
          |SELECT
          |
          |          pt1.id,
          |          a.id AS owner_id,
          |          a.email AS owner_email,
          |          a.uuid AS owner_uuid,
          |          CONCAT(a.first_name, ' ',  a.last_name) AS owner_name,
          |
          |          pt1.prospect_uuid,
          |
          |          pt1.team_id,
          |
          |          pt1.first_name,
          |          pt1.last_name,
          |          pt1.email,
          |          pt1.email_domain,
          |
          |          pt1.invalid_email,
          |
          |          prospect_lists.name AS list,
          |          pt1.list_id,
          |          pt1.company,
          |          pt1.city,
          |          pt1.country,
          |          pt1.timezone,
          |
          |          pt1.created_at,
          |          pt1.updated_at,
          |          pt1.last_contacted_at,
          |          pt1.last_contacted_at_phone,
          |          pt1.last_replied_at,
          |          pt1.last_opened_at,
          |          pt1.total_opens,
          |          pt1.total_clicks,
          |          pt1.custom_fields,
          |          pt1.will_delete,
          |          pt1.email_bounced,
          |          pt1.force_send_invalid_email,
          |          pcat.name as prospect_category_custom,
          |          pcat.label_color as prospect_category_label_color,
          |          pcat.id as prospect_category_id_custom,
          |
          |          pt1.prospect_source,
          |          pt1.prospect_account_id as prospect_account_id,
          |          pa.uuid as prospect_account_uuid,
          |          pa.name as prospect_account_name,
          |
          |           COALESCE(ptags.prospecttags, '[]')  as tags,
          |           COALESCE(magic_columns.prospect_magic_columns, '[]')  as magic_columns,
          |
          |           null  as active_campaigns,
          |
          |          pt1.state,
          |          pt1.job_title,
          |          pt1.phone,
          |          pt1.phone_2,
          |          pt1.phone_3,
          |          pt1.linkedin_url,
          |          pt1.current_step_type,
          |          pt1.latest_reply_sentiment_uuid,
          |          pt1.latest_task_done_at,
          |          ccl.completed_at as last_call_made_at
          |
          |
          |          FROM
          |          (
          |
          |        SELECT
          |
          |         prospects.id,
          |          prospects.uuid AS prospect_uuid,
          |          prospects.account_id,
          |
          |          prospects.team_id,
          |
          |          prospects.first_name,
          |          prospects.last_name,
          |          pe.email,
          |          pe.email_domain,
          |
          |          (CASE WHEN pe.email_checked THEN pe.invalid_email ELSE NULL END) AS invalid_email,
          |
          |          prospects.list_id,
          |          prospects.list,
          |          prospects.company,
          |          prospects.city,
          |          prospects.country,
          |          prospects.timezone,
          |          prospects.sending_holiday_calendar_id,
          |
          |          prospects.created_at,
          |          prospects.updated_at,
          |          prospects.last_contacted_at,
          |          prospects.last_contacted_at_phone,
          |          prospects.last_replied_at,
          |          prospects.last_opened_at,
          |          prospects.total_opens,
          |          prospects.total_clicks,
          |          prospects.custom_fields,
          |          prospects.will_delete,
          |          pe.email_bounced,
          |          pe.force_send_invalid_email,
          |
          |          prospects.prospect_source,
          |          prospects.prospect_account_id as prospect_account_id,
          |          prospects.prospect_category_id_custom,
          |
          |          prospects.state,
          |          prospects.job_title,
          |          prospects.phone,
          |          prospects.phone_2,
          |          prospects.phone_3,
          |          prospects.linkedin_url,
          |          cp.current_step_type,
          |          prospects.latest_reply_sentiment_uuid,
          |          prospects.latest_task_done_at
          |
          |
          |          FROM prospects
          |           LEFT JOIN prospects_emails pe ON ((pe.prospect_id = prospects.id) AND (pe.team_id = prospects.team_id) AND pe.is_primary)
          |           INNER JOIN campaigns_prospects cp ON
          |                (
          |                  cp.prospect_id = prospects.id
          |                    AND
          |                    cp.active
          |                    AND
          |                    cp.campaign_id = ?
          |                    AND
          |                    cp.team_id = prospects.team_id
          |                )
          |
          |
          |
          |           WHERE
          |
          |           prospects.team_id = ?
          |
          |            AND prospects.account_id IN (?)
          |
          |           AND NOT prospects.will_delete
          |
          |
          |
          |            AND (  (  ( lower(pe.email) LIKE ?  AND pe.is_primary  )  )  )
          |          ) pt1
          |
          |
          |
          |          INNER JOIN accounts a ON a.id = pt1.account_id
          |
          |          INNER JOIN prospect_categories_custom pcat on pcat.id = pt1.prospect_category_id_custom
          |
          |          LEFT JOIN prospect_accounts pa ON pa.id = pt1.prospect_account_id
          |
          |            LEFT JOIN LATERAL (
          |              SELECT
          |
          |              json_agg(
          |                json_build_object(
          |                  'tag_id', tags.id,
          |                  'tag', tags.tag,
          |                  'tag_uuid', tags.uuid
          |                  )
          |                ) as prospecttags
          |
          |              FROM tags
          |              JOIN tags_prospects ON tags_prospects.tag_id = tags.id
          |              WHERE tags_prospects.prospect_id = pt1.id
          |              AND NOT tags.hidden
          |
          |            ) AS ptags ON true
          |
          |
          |          LEFT JOIN LATERAL (
          |            SELECT
          |              json_agg(json_build_object(
          |               'column_output', cdp.magic_prompt_generated_output,
          |                'failed_message', cdp.failed_message,
          |                'column_name', cd.name,
          |                'status', cdp.status
          |              )) AS prospect_magic_columns
          |            FROM
          |              column_defs_prospects AS cdp
          |              JOIN column_defs AS cd ON cdp.column_id = cd.id
          |                AND cd.team_id = cdp.team_id
          |            WHERE
          |              cdp.prospect_id = pt1.id
          |              AND cdp.team_id = pt1.team_id
          |          ) AS magic_columns ON TRUE
          |
          |
          |          LEFT JOIN prospect_lists ON (
          |            prospect_lists.id = pt1.list_id
          |            AND prospect_lists.team_id = pt1.team_id
          |          )
          |
          |           LEFT JOIN LATERAL (
          |    SELECT completed_at
          |    FROM call_conference_logs
          |    WHERE primary_prospect_id = pt1.id
          |      AND team_id = pt1.team_id
          |    ORDER BY completed_at DESC
          |    LIMIT 1
          |  ) AS ccl ON TRUE
          |
          |
          |
          |
          |
          |          WHERE
          |
          |           pt1.team_id = ?
          |
          |
          |
          |          ORDER BY pt1.team_id, pt1.id DESC
          |
          |         LIMIT ? OFFSET ?
          |""".stripMargin

      val qry_str = prospectQuery.getQuerySQL(
        permittedAccountIds = Seq(2),
        teamId = 80,
        orgId = 39,
        data = searchJson,
        account = account,
        isInternalRequest = false,
        fetchType = SearchQuerySelectType.FULL_OBJECT,
        Logger = Logger,
//        emailNotCompulsoryEnabled = Some(false)
      )

      val split = expected_result.split(s"\\s+")
      val rhs = split.reduce((a1, a2) => {
        a1 + " " + a2
      })
      val lhs = qry_str.get._3.statement.split("\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })
      assert(lhs == rhs)
    }

    it("should build a query for first name") {
      val searchString = """{"custom_flags":{"active_campaigns_detailed":true},"page":1,"is_campaign":982,"query":{"owner_ids":[0],"clause":"AND","filters":[{"clause":"AND","filters":[{"field_display_name":"First name","field":"first_name","allowedFilterOperators":[{"display_name":"Contains","key":"contains"},{"display_name":"Not contains","key":"not_contains"},{"display_name":"Equals","key":"equals"},{"display_name":"Not equals","key":"not_equals"}],"operator":"contains","value":"ani","value_display_name":"ani","field_type":"text","is_custom":false}]}]}}"""
      val searchJson = Json.parse(searchString).validate[SearchQuery].get
      val expected_result =
        """
          |SELECT
          |
          |          pt1.id,
          |          a.id AS owner_id,
          |          a.email AS owner_email,
          |          a.uuid AS owner_uuid,
          |          CONCAT(a.first_name, ' ',  a.last_name) AS owner_name,
          |
          |          pt1.prospect_uuid,
          |
          |          pt1.team_id,
          |
          |          pt1.first_name,
          |          pt1.last_name,
          |          pt1.email,
          |          pt1.email_domain,
          |
          |          pt1.invalid_email,
          |
          |          prospect_lists.name AS list,
          |          pt1.list_id,
          |          pt1.company,
          |          pt1.city,
          |          pt1.country,
          |          pt1.timezone,
          |
          |          pt1.created_at,
          |          pt1.updated_at,
          |          pt1.last_contacted_at,
          |          pt1.last_contacted_at_phone,
          |          pt1.last_replied_at,
          |          pt1.last_opened_at,
          |          pt1.total_opens,
          |          pt1.total_clicks,
          |          pt1.custom_fields,
          |          pt1.will_delete,
          |          pt1.email_bounced,
          |          pt1.force_send_invalid_email,
          |          pcat.name as prospect_category_custom,
          |          pcat.label_color as prospect_category_label_color,
          |          pcat.id as prospect_category_id_custom,
          |
          |          pt1.prospect_source,
          |          pt1.prospect_account_id as prospect_account_id,
          |          pa.uuid as prospect_account_uuid,
          |          pa.name as prospect_account_name,
          |
          |           COALESCE(ptags.prospecttags, '[]')  as tags,
          |           COALESCE(magic_columns.prospect_magic_columns, '[]')  as magic_columns,
          |
          |           null  as active_campaigns,
          |
          |          pt1.state,
          |          pt1.job_title,
          |          pt1.phone,
          |          pt1.phone_2,
          |          pt1.phone_3,
          |          pt1.linkedin_url,
          |          pt1.current_step_type,
          |          pt1.latest_reply_sentiment_uuid,
          |          pt1.latest_task_done_at,
          |          ccl.completed_at as last_call_made_at
          |
          |
          |          FROM
          |          (
          |
          |        SELECT
          |
          |         prospects.id,
          |          prospects.uuid AS prospect_uuid,
          |          prospects.account_id,
          |
          |          prospects.team_id,
          |
          |          prospects.first_name,
          |          prospects.last_name,
          |          pe.email,
          |          pe.email_domain,
          |
          |          (CASE WHEN pe.email_checked THEN pe.invalid_email ELSE NULL END) AS invalid_email,
          |
          |          prospects.list_id,
          |          prospects.list,
          |          prospects.company,
          |          prospects.city,
          |          prospects.country,
          |          prospects.timezone,
          |          prospects.sending_holiday_calendar_id,
          |
          |          prospects.created_at,
          |          prospects.updated_at,
          |          prospects.last_contacted_at,
          |          prospects.last_contacted_at_phone,
          |          prospects.last_replied_at,
          |          prospects.last_opened_at,
          |          prospects.total_opens,
          |          prospects.total_clicks,
          |          prospects.custom_fields,
          |          prospects.will_delete,
          |          pe.email_bounced,
          |          pe.force_send_invalid_email,
          |
          |          prospects.prospect_source,
          |          prospects.prospect_account_id as prospect_account_id,
          |          prospects.prospect_category_id_custom,
          |
          |          prospects.state,
          |          prospects.job_title,
          |          prospects.phone,
          |          prospects.phone_2,
          |          prospects.phone_3,
          |          prospects.linkedin_url,
          |          cp.current_step_type,
          |          prospects.latest_reply_sentiment_uuid,
          |          prospects.latest_task_done_at
          |
          |
          |          FROM prospects
          |           LEFT JOIN prospects_emails pe ON ((pe.prospect_id = prospects.id) AND (pe.team_id = prospects.team_id) AND pe.is_primary)
          |           INNER JOIN campaigns_prospects cp ON
          |                (
          |                  cp.prospect_id = prospects.id
          |                    AND
          |                    cp.active
          |                    AND
          |                    cp.campaign_id = ?
          |                    AND
          |                    cp.team_id = prospects.team_id
          |                )
          |
          |
          |
          |           WHERE
          |
          |           prospects.team_id = ?
          |
          |            AND prospects.account_id IN (?)
          |
          |           AND NOT prospects.will_delete
          |
          |
          |
          |            AND (  (  ( prospects.first_name ILIKE ? )  )  )
          |          ) pt1
          |
          |
          |
          |          INNER JOIN accounts a ON a.id = pt1.account_id
          |
          |          INNER JOIN prospect_categories_custom pcat on pcat.id = pt1.prospect_category_id_custom
          |
          |          LEFT JOIN prospect_accounts pa ON pa.id = pt1.prospect_account_id
          |
          |            LEFT JOIN LATERAL (
          |              SELECT
          |
          |              json_agg(
          |                json_build_object(
          |                  'tag_id', tags.id,
          |                  'tag', tags.tag,
          |                  'tag_uuid', tags.uuid
          |                  )
          |                ) as prospecttags
          |
          |              FROM tags
          |              JOIN tags_prospects ON tags_prospects.tag_id = tags.id
          |              WHERE tags_prospects.prospect_id = pt1.id
          |              AND NOT tags.hidden
          |
          |            ) AS ptags ON true
          |
          |
          |          LEFT JOIN LATERAL (
          |            SELECT
          |              json_agg(json_build_object(
          |               'column_output', cdp.magic_prompt_generated_output,
          |                'failed_message', cdp.failed_message,
          |                'column_name', cd.name,
          |                'status', cdp.status
          |              )) AS prospect_magic_columns
          |            FROM
          |              column_defs_prospects AS cdp
          |              JOIN column_defs AS cd ON cdp.column_id = cd.id
          |                AND cd.team_id = cdp.team_id
          |            WHERE
          |              cdp.prospect_id = pt1.id
          |              AND cdp.team_id = pt1.team_id
          |          ) AS magic_columns ON TRUE
          |
          |
          |          LEFT JOIN prospect_lists ON (
          |            prospect_lists.id = pt1.list_id
          |            AND prospect_lists.team_id = pt1.team_id
          |          )
          |
          |           LEFT JOIN LATERAL (
          |    SELECT completed_at
          |    FROM call_conference_logs
          |    WHERE primary_prospect_id = pt1.id
          |      AND team_id = pt1.team_id
          |    ORDER BY completed_at DESC
          |    LIMIT 1
          |  ) AS ccl ON TRUE
          |
          |
          |
          |
          |
          |          WHERE
          |
          |           pt1.team_id = ?
          |
          |
          |
          |          ORDER BY pt1.team_id, pt1.id DESC
          |
          |         LIMIT ? OFFSET ?
          |
          |""".stripMargin

      val qry_str = prospectQuery.getQuerySQL(
        permittedAccountIds = Seq(2),
        teamId = 80,
        orgId = 39,
        data = searchJson,
        account = account,
        isInternalRequest = false,
        fetchType = SearchQuerySelectType.FULL_OBJECT,
        Logger = Logger,
//        emailNotCompulsoryEnabled = Some(false)
      )

      val split = expected_result.split(s"\\s+")
      val rhs = split.reduce((a1, a2) => {
        a1 + " " + a2
      })
      val lhs = qry_str.get._3.statement.split("\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })
      assert(lhs == rhs)
    }

    it("should build a query for last name") {
      val searchString = """{"custom_flags":{"active_campaigns_detailed":true},"page":1,"is_campaign":982,"query":{"owner_ids":[0],"clause":"AND","filters":[{"clause":"AND","filters":[{"field_display_name":"Last name","field":"last_name","allowedFilterOperators":[{"display_name":"Contains","key":"contains"},{"display_name":"Not contains","key":"not_contains"},{"display_name":"Equals","key":"equals"},{"display_name":"Not equals","key":"not_equals"}],"operator":"contains","value":"ani","value_display_name":"ani","field_type":"text","is_custom":false}]}]}}"""
      val searchJson = Json.parse(searchString).validate[SearchQuery].get
      val expected_result =
        """
          |SELECT
          |          pt1.id,
          |          a.id AS owner_id,
          |          a.email AS owner_email,
          |          a.uuid AS owner_uuid,
          |          CONCAT(a.first_name, ' ',  a.last_name) AS owner_name,
          |
          |          pt1.prospect_uuid,
          |
          |          pt1.team_id,
          |
          |          pt1.first_name,
          |          pt1.last_name,
          |          pt1.email,
          |          pt1.email_domain,
          |
          |          pt1.invalid_email,
          |
          |          prospect_lists.name AS list,
          |          pt1.list_id,
          |          pt1.company,
          |          pt1.city,
          |          pt1.country,
          |          pt1.timezone,
          |
          |          pt1.created_at,
          |          pt1.updated_at,
          |          pt1.last_contacted_at,
          |          pt1.last_contacted_at_phone,
          |          pt1.last_replied_at,
          |          pt1.last_opened_at,
          |          pt1.total_opens,
          |          pt1.total_clicks,
          |          pt1.custom_fields,
          |          pt1.will_delete,
          |          pt1.email_bounced,
          |          pt1.force_send_invalid_email,
          |          pcat.name as prospect_category_custom,
          |          pcat.label_color as prospect_category_label_color,
          |          pcat.id as prospect_category_id_custom,
          |
          |          pt1.prospect_source,
          |          pt1.prospect_account_id as prospect_account_id,
          |          pa.uuid as prospect_account_uuid,
          |          pa.name as prospect_account_name,
          |
          |           COALESCE(ptags.prospecttags, '[]')  as tags,
          |           COALESCE(magic_columns.prospect_magic_columns, '[]')  as magic_columns,
          |
          |           null  as active_campaigns,
          |
          |          pt1.state,
          |          pt1.job_title,
          |          pt1.phone,
          |          pt1.phone_2,
          |          pt1.phone_3,
          |          pt1.linkedin_url,
          |          pt1.current_step_type,
          |          pt1.latest_reply_sentiment_uuid,
          |          pt1.latest_task_done_at,
          |          ccl.completed_at as last_call_made_at
          |
          |          FROM
          |          (
          |
          |        SELECT
          |
          |         prospects.id,
          |          prospects.uuid AS prospect_uuid,
          |          prospects.account_id,
          |
          |          prospects.team_id,
          |
          |          prospects.first_name,
          |          prospects.last_name,
          |          pe.email,
          |          pe.email_domain,
          |
          |          (CASE WHEN pe.email_checked THEN pe.invalid_email ELSE NULL END) AS invalid_email,
          |
          |          prospects.list_id,
          |          prospects.list,
          |          prospects.company,
          |          prospects.city,
          |          prospects.country,
          |          prospects.timezone,
          |          prospects.sending_holiday_calendar_id,
          |
          |          prospects.created_at,
          |          prospects.updated_at,
          |          prospects.last_contacted_at,
          |          prospects.last_contacted_at_phone,
          |          prospects.last_replied_at,
          |          prospects.last_opened_at,
          |          prospects.total_opens,
          |          prospects.total_clicks,
          |          prospects.custom_fields,
          |          prospects.will_delete,
          |          pe.email_bounced,
          |          pe.force_send_invalid_email,
          |
          |          prospects.prospect_source,
          |          prospects.prospect_account_id as prospect_account_id,
          |          prospects.prospect_category_id_custom,
          |
          |          prospects.state,
          |          prospects.job_title,
          |          prospects.phone,
          |          prospects.phone_2,
          |          prospects.phone_3,
          |          prospects.linkedin_url,
          |          cp.current_step_type,
          |          prospects.latest_reply_sentiment_uuid,
          |          prospects.latest_task_done_at
          |
          |
          |          FROM prospects
          |           LEFT JOIN prospects_emails pe ON ((pe.prospect_id = prospects.id) AND (pe.team_id = prospects.team_id) AND pe.is_primary)
          |           INNER JOIN campaigns_prospects cp ON
          |                (
          |                  cp.prospect_id = prospects.id
          |                    AND
          |                    cp.active
          |                    AND
          |                    cp.campaign_id = ?
          |                    AND
          |                    cp.team_id = prospects.team_id
          |                )
          |
          |
          |
          |           WHERE
          |
          |           prospects.team_id = ?
          |
          |            AND prospects.account_id IN (?)
          |
          |           AND NOT prospects.will_delete
          |
          |
          |
          |            AND (  (  ( prospects.last_name ILIKE ? )  )  )
          |          ) pt1
          |
          |
          |
          |          INNER JOIN accounts a ON a.id = pt1.account_id
          |
          |          INNER JOIN prospect_categories_custom pcat on pcat.id = pt1.prospect_category_id_custom
          |
          |          LEFT JOIN prospect_accounts pa ON pa.id = pt1.prospect_account_id
          |
          |            LEFT JOIN LATERAL (
          |              SELECT
          |
          |              json_agg(
          |                json_build_object(
          |                  'tag_id', tags.id,
          |                  'tag', tags.tag,
          |                  'tag_uuid', tags.uuid
          |                  )
          |                ) as prospecttags
          |
          |              FROM tags
          |              JOIN tags_prospects ON tags_prospects.tag_id = tags.id
          |              WHERE tags_prospects.prospect_id = pt1.id
          |              AND NOT tags.hidden
          |
          |            ) AS ptags ON true
          |
          |
          |          LEFT JOIN LATERAL (
          |            SELECT
          |              json_agg(json_build_object(
          |               'column_output', cdp.magic_prompt_generated_output,
          |                'failed_message', cdp.failed_message,
          |                'column_name', cd.name,
          |                'status', cdp.status
          |              )) AS prospect_magic_columns
          |            FROM
          |              column_defs_prospects AS cdp
          |              JOIN column_defs AS cd ON cdp.column_id = cd.id
          |                AND cd.team_id = cdp.team_id
          |            WHERE
          |              cdp.prospect_id = pt1.id
          |              AND cdp.team_id = pt1.team_id
          |          ) AS magic_columns ON TRUE
          |
          |
          |          LEFT JOIN prospect_lists ON (
          |            prospect_lists.id = pt1.list_id
          |            AND prospect_lists.team_id = pt1.team_id
          |          )

          |           LEFT JOIN LATERAL (
          |    SELECT completed_at
          |    FROM call_conference_logs
          |    WHERE primary_prospect_id = pt1.id
          |      AND team_id = pt1.team_id
          |    ORDER BY completed_at DESC
          |    LIMIT 1
          |  ) AS ccl ON TRUE
          |
          |          WHERE
          |
          |           pt1.team_id = ?
          |
          |
          |
          |          ORDER BY pt1.team_id, pt1.id DESC
          |
          |         LIMIT ? OFFSET ?
          |""".stripMargin

      val qry_str = prospectQuery.getQuerySQL(
        permittedAccountIds = Seq(2),
        teamId = 80,
        orgId = 39,
        data = searchJson,
        account = account,
        isInternalRequest = false,
        fetchType = SearchQuerySelectType.FULL_OBJECT,
        Logger = Logger,
//        emailNotCompulsoryEnabled = Some(false)
      )

      val split = expected_result.split(s"\\s+")
      val rhs = split.reduce((a1, a2) => {
        a1 + " " + a2
      })
      val lhs = qry_str.get._3.statement.split("\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })
      assert(lhs == rhs)
    }

    it("should build a query for prospect category") {
      val searchString = """{"custom_flags":{"active_campaigns_detailed":true},"page":1,"is_campaign":982,"query":{"owner_ids":[0],"clause":"AND","filters":[{"clause":"AND","filters":[{"field_display_name":"Prospect Category","field":"prospect_category","allowedFilterOperators":[{"display_name":"Equals","key":"equals"},{"display_name":"Not equals","key":"not_equals"}],"operator":"equals","value":"26","value_display_name":"Converted","field_type":"text","is_custom":false}]}]}}"""
      val searchJson = Json.parse(searchString).validate[SearchQuery].get
      val expected_result =
        """
          |         SELECT
          |
          |          pt1.id,
          |          a.id AS owner_id,
          |          a.email AS owner_email,
          |          a.uuid AS owner_uuid,
          |          CONCAT(a.first_name, ' ',  a.last_name) AS owner_name,
          |
          |          pt1.prospect_uuid,
          |
          |          pt1.team_id,
          |
          |          pt1.first_name,
          |          pt1.last_name,
          |          pt1.email,
          |          pt1.email_domain,
          |
          |          pt1.invalid_email,
          |
          |          prospect_lists.name AS list,
          |          pt1.list_id,
          |          pt1.company,
          |          pt1.city,
          |          pt1.country,
          |          pt1.timezone,
          |
          |          pt1.created_at,
          |          pt1.updated_at,
          |          pt1.last_contacted_at,
          |          pt1.last_contacted_at_phone,
          |          pt1.last_replied_at,
          |          pt1.last_opened_at,
          |          pt1.total_opens,
          |          pt1.total_clicks,
          |          pt1.custom_fields,
          |          pt1.will_delete,
          |          pt1.email_bounced,
          |          pt1.force_send_invalid_email,
          |          pcat.name as prospect_category_custom,
          |          pcat.label_color as prospect_category_label_color,
          |          pcat.id as prospect_category_id_custom,
          |
          |          pt1.prospect_source,
          |          pt1.prospect_account_id as prospect_account_id,
          |          pa.uuid as prospect_account_uuid,
          |          pa.name as prospect_account_name,
          |
          |           COALESCE(ptags.prospecttags, '[]')  as tags,
          |           COALESCE(magic_columns.prospect_magic_columns, '[]')  as magic_columns,
          |
          |           null  as active_campaigns,
          |
          |          pt1.state,
          |          pt1.job_title,
          |          pt1.phone,
          |          pt1.phone_2,
          |          pt1.phone_3,
          |          pt1.linkedin_url,
          |          pt1.current_step_type,
          |          pt1.latest_reply_sentiment_uuid,
          |          pt1.latest_task_done_at,
          |          ccl.completed_at as last_call_made_at
          |
          |
          |          FROM
          |          (
          |
          |        SELECT
          |
          |         prospects.id,
          |          prospects.uuid AS prospect_uuid,
          |          prospects.account_id,
          |
          |          prospects.team_id,
          |
          |          prospects.first_name,
          |          prospects.last_name,
          |          pe.email,
          |          pe.email_domain,
          |
          |          (CASE WHEN pe.email_checked THEN pe.invalid_email ELSE NULL END) AS invalid_email,
          |
          |          prospects.list_id,
          |          prospects.list,
          |          prospects.company,
          |          prospects.city,
          |          prospects.country,
          |          prospects.timezone,
          |          prospects.sending_holiday_calendar_id,
          |
          |          prospects.created_at,
          |          prospects.updated_at,
          |          prospects.last_contacted_at,
          |          prospects.last_contacted_at_phone,
          |          prospects.last_replied_at,
          |          prospects.last_opened_at,
          |          prospects.total_opens,
          |          prospects.total_clicks,
          |          prospects.custom_fields,
          |          prospects.will_delete,
          |          pe.email_bounced,
          |          pe.force_send_invalid_email,
          |
          |          prospects.prospect_source,
          |          prospects.prospect_account_id as prospect_account_id,
          |          prospects.prospect_category_id_custom,
          |
          |          prospects.state,
          |          prospects.job_title,
          |          prospects.phone,
          |          prospects.phone_2,
          |          prospects.phone_3,
          |          prospects.linkedin_url,
          |          cp.current_step_type,
          |          prospects.latest_reply_sentiment_uuid,
          |          prospects.latest_task_done_at
          |
          |
          |          FROM prospects
          |           LEFT JOIN prospects_emails pe ON ((pe.prospect_id = prospects.id) AND (pe.team_id = prospects.team_id) AND pe.is_primary)
          |           INNER JOIN campaigns_prospects cp ON
          |                (
          |                  cp.prospect_id = prospects.id
          |                    AND
          |                    cp.active
          |                    AND
          |                    cp.campaign_id = ?
          |                    AND
          |                    cp.team_id = prospects.team_id
          |                )
          |
          |
          |
          |           WHERE
          |
          |           prospects.team_id = ?
          |
          |            AND prospects.account_id IN (?)
          |
          |           AND NOT prospects.will_delete
          |
          |
          |
          |            AND (  (  ( (prospects.prospect_category_id_custom = ?) )  )  )
          |          ) pt1
          |
          |
          |
          |          INNER JOIN accounts a ON a.id = pt1.account_id
          |
          |          INNER JOIN prospect_categories_custom pcat on pcat.id = pt1.prospect_category_id_custom
          |
          |          LEFT JOIN prospect_accounts pa ON pa.id = pt1.prospect_account_id
          |
          |            LEFT JOIN LATERAL (
          |              SELECT
          |
          |              json_agg(
          |                json_build_object(
          |                  'tag_id', tags.id,
          |                  'tag', tags.tag,
          |                  'tag_uuid', tags.uuid
          |                  )
          |                ) as prospecttags
          |
          |              FROM tags
          |              JOIN tags_prospects ON tags_prospects.tag_id = tags.id
          |              WHERE tags_prospects.prospect_id = pt1.id
          |              AND NOT tags.hidden
          |
          |            ) AS ptags ON true
          |
          |
          |          LEFT JOIN LATERAL (
          |            SELECT
          |              json_agg(json_build_object(
          |               'column_output', cdp.magic_prompt_generated_output,
          |                'failed_message', cdp.failed_message,
          |                'column_name', cd.name,
          |                'status', cdp.status
          |              )) AS prospect_magic_columns
          |            FROM
          |              column_defs_prospects AS cdp
          |              JOIN column_defs AS cd ON cdp.column_id = cd.id
          |                AND cd.team_id = cdp.team_id
          |            WHERE
          |              cdp.prospect_id = pt1.id
          |              AND cdp.team_id = pt1.team_id
          |          ) AS magic_columns ON TRUE
          |
          |
          |          LEFT JOIN prospect_lists ON (
          |            prospect_lists.id = pt1.list_id
          |            AND prospect_lists.team_id = pt1.team_id
          |          )

          |           LEFT JOIN LATERAL (
          |    SELECT completed_at
          |    FROM call_conference_logs
          |    WHERE primary_prospect_id = pt1.id
          |      AND team_id = pt1.team_id
          |    ORDER BY completed_at DESC
          |    LIMIT 1
          |  ) AS ccl ON TRUE
          |
          |
          |
          |
          |
          |          WHERE
          |
          |           pt1.team_id = ?
          |
          |
          |
          |          ORDER BY pt1.team_id, pt1.id DESC
          |
          |         LIMIT ? OFFSET ?
          |         """
          .stripMargin

      val qry_str = prospectQuery.getQuerySQL(
        permittedAccountIds = Seq(2),
        teamId = 80,
        orgId = 39,
        data = searchJson,
        account = account,
        isInternalRequest = false,
        fetchType = SearchQuerySelectType.FULL_OBJECT,
        Logger = Logger,
//        emailNotCompulsoryEnabled = Some(false)
      )

      val split = expected_result.split(s"\\s+")
      val rhs = split.reduce((a1, a2) => {
        a1 + " " + a2
      })
      val lhs = qry_str.get._3.statement.split("\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })
      assert(lhs == rhs)
    }


    it("should build a query for Current step") {
      val searchString = """{"custom_flags":{"active_campaigns_detailed":true},"page":1,"is_campaign":982,"query":{"owner_ids":[0],"clause":"AND","filters":[{"clause":"AND","filters":[{"field_display_name":"Current step","field":"step","allowedFilterOperators":[{"display_name":"Equals","key":"equals"}],"operator":"equals","value":"861","value_display_name":"Day 1: Opening","field_type":"number","is_custom":false}]}]}}"""
      val searchJson = Json.parse(searchString).validate[SearchQuery].get
      val expected_result =
        """
          |SELECT
          |          pt1.id,
          |          a.id AS owner_id,
          |          a.email AS owner_email,
          |          a.uuid AS owner_uuid,
          |          CONCAT(a.first_name, ' ',  a.last_name) AS owner_name,
          |
          |          pt1.prospect_uuid,
          |
          |          pt1.team_id,
          |
          |          pt1.first_name,
          |          pt1.last_name,
          |          pt1.email,
          |          pt1.email_domain,
          |
          |          pt1.invalid_email,
          |
          |          prospect_lists.name AS list,
          |          pt1.list_id,
          |          pt1.company,
          |          pt1.city,
          |          pt1.country,
          |          pt1.timezone,
          |
          |          pt1.created_at,
          |          pt1.updated_at,
          |          pt1.last_contacted_at,
          |          pt1.last_contacted_at_phone,
          |          pt1.last_replied_at,
          |          pt1.last_opened_at,
          |          pt1.total_opens,
          |          pt1.total_clicks,
          |          pt1.custom_fields,
          |          pt1.will_delete,
          |          pt1.email_bounced,
          |          pt1.force_send_invalid_email,
          |          pcat.name as prospect_category_custom,
          |          pcat.label_color as prospect_category_label_color,
          |          pcat.id as prospect_category_id_custom,
          |
          |          pt1.prospect_source,
          |          pt1.prospect_account_id as prospect_account_id,
          |          pa.uuid as prospect_account_uuid,
          |          pa.name as prospect_account_name,
          |
          |           COALESCE(ptags.prospecttags, '[]')  as tags,
          |           COALESCE(magic_columns.prospect_magic_columns, '[]')  as magic_columns,
          |
          |           null  as active_campaigns,
          |
          |          pt1.state,
          |          pt1.job_title,
          |          pt1.phone,
          |          pt1.phone_2,
          |          pt1.phone_3,
          |          pt1.linkedin_url,
          |          pt1.current_step_type,
          |          pt1.latest_reply_sentiment_uuid,
          |          pt1.latest_task_done_at,
          |          ccl.completed_at as last_call_made_at
          |
          |
          |          FROM
          |          (
          |
          |        SELECT
          |
          |         prospects.id,
          |          prospects.uuid AS prospect_uuid,
          |          prospects.account_id,
          |
          |          prospects.team_id,
          |
          |          prospects.first_name,
          |          prospects.last_name,
          |          pe.email,
          |          pe.email_domain,
          |
          |          (CASE WHEN pe.email_checked THEN pe.invalid_email ELSE NULL END) AS invalid_email,
          |
          |          prospects.list_id,
          |          prospects.list,
          |          prospects.company,
          |          prospects.city,
          |          prospects.country,
          |          prospects.timezone,
          |          prospects.sending_holiday_calendar_id,
          |
          |          prospects.created_at,
          |          prospects.updated_at,
          |          prospects.last_contacted_at,
          |          prospects.last_contacted_at_phone,
          |          prospects.last_replied_at,
          |          prospects.last_opened_at,
          |          prospects.total_opens,
          |          prospects.total_clicks,
          |          prospects.custom_fields,
          |          prospects.will_delete,
          |          pe.email_bounced,
          |          pe.force_send_invalid_email,
          |
          |          prospects.prospect_source,
          |          prospects.prospect_account_id as prospect_account_id,
          |          prospects.prospect_category_id_custom,
          |
          |          prospects.state,
          |          prospects.job_title,
          |          prospects.phone,
          |          prospects.phone_2,
          |          prospects.phone_3,
          |          prospects.linkedin_url,
          |          cp.current_step_type,
          |          prospects.latest_reply_sentiment_uuid,
          |          prospects.latest_task_done_at
          |
          |
          |          FROM prospects
          |           LEFT JOIN prospects_emails pe ON ((pe.prospect_id = prospects.id) AND (pe.team_id = prospects.team_id) AND pe.is_primary)
          |           INNER JOIN campaigns_prospects cp ON
          |                (
          |                  cp.prospect_id = prospects.id
          |                    AND
          |                    cp.active
          |                    AND
          |                    cp.campaign_id = ?
          |                    AND
          |                    cp.team_id = prospects.team_id
          |                )
          |
          |
          |
          |           WHERE
          |
          |           prospects.team_id = ?
          |
          |            AND prospects.account_id IN (?)
          |
          |           AND NOT prospects.will_delete
          |
          |
          |
          |            AND (  (  ( EXISTS(
          |                          SELECT *
          |                          FROM campaigns_prospects cp_steps
          |                          where cp_steps.prospect_id = prospects.id
          |                          AND cp_steps.current_step_id = ?
          |                          AND cp_steps.active
          |                          and cp_steps.team_id = ?
          |                        )
          |                      )  )  )
          |          ) pt1
          |
          |
          |
          |          INNER JOIN accounts a ON a.id = pt1.account_id
          |
          |          INNER JOIN prospect_categories_custom pcat on pcat.id = pt1.prospect_category_id_custom
          |
          |          LEFT JOIN prospect_accounts pa ON pa.id = pt1.prospect_account_id
          |
          |            LEFT JOIN LATERAL (
          |              SELECT
          |
          |              json_agg(
          |                json_build_object(
          |                  'tag_id', tags.id,
          |                  'tag', tags.tag,
          |                  'tag_uuid', tags.uuid
          |                  )
          |                ) as prospecttags
          |
          |              FROM tags
          |              JOIN tags_prospects ON tags_prospects.tag_id = tags.id
          |              WHERE tags_prospects.prospect_id = pt1.id
          |              AND NOT tags.hidden
          |
          |            ) AS ptags ON true
          |
          |
          |          LEFT JOIN LATERAL (
          |            SELECT
          |              json_agg(json_build_object(
          |               'column_output', cdp.magic_prompt_generated_output,
          |                'failed_message', cdp.failed_message,
          |                'column_name', cd.name,
          |                'status', cdp.status
          |              )) AS prospect_magic_columns
          |            FROM
          |              column_defs_prospects AS cdp
          |              JOIN column_defs AS cd ON cdp.column_id = cd.id
          |                AND cd.team_id = cdp.team_id
          |            WHERE
          |              cdp.prospect_id = pt1.id
          |              AND cdp.team_id = pt1.team_id
          |          ) AS magic_columns ON TRUE
          |
          |
          |          LEFT JOIN prospect_lists ON (
          |            prospect_lists.id = pt1.list_id
          |            AND prospect_lists.team_id = pt1.team_id
          |          )

          |           LEFT JOIN LATERAL (
          |    SELECT completed_at
          |    FROM call_conference_logs
          |    WHERE primary_prospect_id = pt1.id
          |      AND team_id = pt1.team_id
          |    ORDER BY completed_at DESC
          |    LIMIT 1
          |  ) AS ccl ON TRUE
          |
          |
          |
          |
          |
          |          WHERE
          |
          |           pt1.team_id = ?
          |
          |
          |
          |          ORDER BY pt1.team_id, pt1.id DESC
          |
          |         LIMIT ? OFFSET ?
          |
          |""".stripMargin
      val qry_str = prospectQuery.getQuerySQL(
        permittedAccountIds = Seq(2),
        teamId = 80,
        orgId = 39,
        data = searchJson,
        account = account,
        isInternalRequest = false,
        fetchType = SearchQuerySelectType.FULL_OBJECT,
        Logger = Logger,
//        emailNotCompulsoryEnabled = Some(false)
      )

      val split = expected_result.split(s"\\s+")
      val rhs = split.reduce((a1, a2) => {
        a1 + " " + a2
      })
      val lhs = qry_str.get._3.statement.split("\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })
      assert(lhs == rhs)
    }

    it("should build a query for has_call_recording") {

      val hcr = prospectColumnDef.defaultColumns.find(_.name == "has_call_recording").get

      val searchJson = SearchQuery(
        query = Some(
          SearchQueryMainClause(
            search = None,
            owner_ids = account.teams.flatMap(_.access_members.map(_.user_id)),
            clause = "AND",
            filters = Seq(
              SearchQuerySubClause(
                clause = "AND",
                filters = Seq(
                  SearchQuerySubFilterData(
                    field = hcr.name,
                    field_display_name = Some(hcr.display_name),
                    operator = SearchQueryOperators.EQUAL,
                    value = "true",
                    value_display_name = None,
                    field_type = hcr.field_type,
                    is_custom = hcr.is_custom,
                    allowedFilterOperators = Some(hcr.allowed_filter_operators),
                  )
                )
              )
            )
          )
        ),
        page = None,
        custom_flags = None,
        sort = None,
        is_campaign = None,
        older_than = None,
        newer_than = None
      )


      val expected_result =
        """
          |SELECT
          |
          |          pt1.id,
          |          a.id AS owner_id,
          |          a.email AS owner_email,
          |          a.uuid AS owner_uuid,
          |          CONCAT(a.first_name, ' ',  a.last_name) AS owner_name,
          |
          |          pt1.prospect_uuid,
          |
          |          pt1.team_id,
          |
          |          pt1.first_name,
          |          pt1.last_name,
          |          pt1.email,
          |          pt1.email_domain,
          |
          |          pt1.invalid_email,
          |
          |          prospect_lists.name AS list,
          |          pt1.list_id,
          |          pt1.company,
          |          pt1.city,
          |          pt1.country,
          |          pt1.timezone,
          |
          |          pt1.created_at,
          |          pt1.updated_at,
          |          pt1.last_contacted_at,
          |          pt1.last_contacted_at_phone,
          |          pt1.last_replied_at,
          |          pt1.last_opened_at,
          |          pt1.total_opens,
          |          pt1.total_clicks,
          |          pt1.custom_fields,
          |          pt1.will_delete,
          |          pt1.email_bounced,
          |          pt1.force_send_invalid_email,
          |          pcat.name as prospect_category_custom,
          |          pcat.label_color as prospect_category_label_color,
          |          pcat.id as prospect_category_id_custom,
          |
          |          pt1.prospect_source,
          |          pt1.prospect_account_id as prospect_account_id,
          |          pa.uuid as prospect_account_uuid,
          |          pa.name as prospect_account_name,
          |
          |           COALESCE(ptags.prospecttags, '[]')  as tags,
          |           COALESCE(magic_columns.prospect_magic_columns, '[]')  as magic_columns,
          |
          |           null  as active_campaigns,
          |
          |          pt1.state,
          |          pt1.job_title,
          |          pt1.phone,
          |          pt1.phone_2,
          |          pt1.phone_3,
          |          pt1.linkedin_url,
          |
          |          pt1.latest_reply_sentiment_uuid,
          |          pt1.latest_task_done_at,
          |          ccl.completed_at as last_call_made_at
          |
          |          FROM
          |          (
          |
          |        SELECT
          |
          |         prospects.id,
          |          prospects.uuid AS prospect_uuid,
          |          prospects.account_id,
          |
          |          prospects.team_id,
          |
          |          prospects.first_name,
          |          prospects.last_name,
          |          pe.email,
          |          pe.email_domain,
          |
          |          (CASE WHEN pe.email_checked THEN pe.invalid_email ELSE NULL END) AS invalid_email,
          |
          |          prospects.list_id,
          |          prospects.list,
          |          prospects.company,
          |          prospects.city,
          |          prospects.country,
          |          prospects.timezone,
          |          prospects.sending_holiday_calendar_id,
          |
          |          prospects.created_at,
          |          prospects.updated_at,
          |          prospects.last_contacted_at,
          |          prospects.last_contacted_at_phone,
          |          prospects.last_replied_at,
          |          prospects.last_opened_at,
          |          prospects.total_opens,
          |          prospects.total_clicks,
          |          prospects.custom_fields,
          |          prospects.will_delete,
          |          pe.email_bounced,
          |          pe.force_send_invalid_email,
          |
          |          prospects.prospect_source,
          |          prospects.prospect_account_id as prospect_account_id,
          |          prospects.prospect_category_id_custom,
          |
          |          prospects.state,
          |          prospects.job_title,
          |          prospects.phone,
          |          prospects.phone_2,
          |          prospects.phone_3,
          |          prospects.linkedin_url,
          |
          |          prospects.latest_reply_sentiment_uuid,
          |          prospects.latest_task_done_at
          |
          |
          |          FROM prospects
          |           LEFT JOIN prospects_emails pe ON ((pe.prospect_id = prospects.id) AND (pe.team_id = prospects.team_id) AND pe.is_primary)
          |
          |
          |
          |           WHERE
          |
          |           prospects.team_id = ?
          |
          |            AND prospects.account_id IN (?)
          |
          |           AND NOT prospects.will_delete
          |
          |
          |
          |
          |          ) pt1
          |
          |
          |
          |          INNER JOIN accounts a ON a.id = pt1.account_id
          |
          |          INNER JOIN prospect_categories_custom pcat on pcat.id = pt1.prospect_category_id_custom
          |
          |          LEFT JOIN prospect_accounts pa ON pa.id = pt1.prospect_account_id
          |
          |            LEFT JOIN LATERAL (
          |              SELECT
          |
          |              json_agg(
          |                json_build_object(
          |                  'tag_id', tags.id,
          |                  'tag', tags.tag,
          |                  'tag_uuid', tags.uuid
          |                  )
          |                ) as prospecttags
          |
          |              FROM tags
          |              JOIN tags_prospects ON tags_prospects.tag_id = tags.id
          |              WHERE tags_prospects.prospect_id = pt1.id
          |              AND NOT tags.hidden
          |
          |            ) AS ptags ON true
          |
          |
          |          LEFT JOIN LATERAL (
          |            SELECT
          |              json_agg(json_build_object(
          |               'column_output', cdp.magic_prompt_generated_output,
          |                'failed_message', cdp.failed_message,
          |                'column_name', cd.name,
          |                'status', cdp.status
          |              )) AS prospect_magic_columns
          |            FROM
          |              column_defs_prospects AS cdp
          |              JOIN column_defs AS cd ON cdp.column_id = cd.id
          |                AND cd.team_id = cdp.team_id
          |            WHERE
          |              cdp.prospect_id = pt1.id
          |              AND cdp.team_id = pt1.team_id
          |          ) AS magic_columns ON TRUE
          |
          |
          |          LEFT JOIN prospect_lists ON (
          |            prospect_lists.id = pt1.list_id
          |            AND prospect_lists.team_id = pt1.team_id
          |          )
          |
          |           LEFT JOIN LATERAL (
          |             SELECT completed_at
          |             FROM call_conference_logs
          |             WHERE primary_prospect_id = pt1.id
          |               AND team_id = pt1.team_id
          |             ORDER BY completed_at DESC
          |             LIMIT 1
          |           ) AS ccl ON TRUE
          |
          |
          |          WHERE
          |
          |           pt1.team_id = ?
          |
          |            AND (  (  ( EXISTS( SELECT * FROM tasks AS t JOIN call_conference_logs AS ccl ON ccl.team_id = t.team_id AND ccl.task_uuid = t.task_id
          |                      WHERE
          |                        t.team_id = pt1.team_id
          |                        AND t.prospect_id = pt1.id
          |                        AND ccl.recording_url IS NOT NULL ) )  )  )
          |
          |          ORDER BY pt1.team_id, pt1.id DESC
          |
          |         LIMIT ? OFFSET ?
          |"""
          .stripMargin

      val qry_str = prospectQuery.getQuerySQL(
        permittedAccountIds = Seq(2),
        teamId = 80,
        orgId = 39,
        data = searchJson,
        account = account,
        isInternalRequest = false,
        fetchType = SearchQuerySelectType.FULL_OBJECT,
        Logger = Logger,
      )

      val split = expected_result.split(s"\\s+")
      val rhs = split.reduce(
        (a1, a2) => {
          a1 + " " + a2
        }
      )
      val lhs = qry_str.get._3.statement.split("\\s+").reduce(
        (s1, s2) => {
          s1 + " " + s2
        }
      )
      assert(lhs == rhs)

    }


    it("should build a query for  company") {
      val searchString = """{"custom_flags":{"active_campaigns_detailed":true},"page":1,"is_campaign":982,"query":{"owner_ids":[0],"clause":"AND","filters":[{"clause":"AND","filters":[{"field_display_name":"Company","field":"company","allowedFilterOperators":[{"display_name":"Contains","key":"contains"},{"display_name":"Not contains","key":"not_contains"},{"display_name":"Equals","key":"equals"},{"display_name":"Not equals","key":"not_equals"}],"operator":"contains","value":"ani","value_display_name":"ani","field_type":"text","is_custom":false}]}]}}"""
      val searchJson = Json.parse(searchString).validate[SearchQuery].get
      val expected_result =
        """
          |         SELECT
          |
          |          pt1.id,
          |          a.id AS owner_id,
          |          a.email AS owner_email,
          |          a.uuid AS owner_uuid,
          |          CONCAT(a.first_name, ' ',  a.last_name) AS owner_name,
          |
          |          pt1.prospect_uuid,
          |
          |          pt1.team_id,
          |
          |          pt1.first_name,
          |          pt1.last_name,
          |          pt1.email,
          |          pt1.email_domain,
          |
          |          pt1.invalid_email,
          |
          |          prospect_lists.name AS list,
          |          pt1.list_id,
          |          pt1.company,
          |          pt1.city,
          |          pt1.country,
          |          pt1.timezone,
          |
          |          pt1.created_at,
          |          pt1.updated_at,
          |          pt1.last_contacted_at,
          |          pt1.last_contacted_at_phone,
          |          pt1.last_replied_at,
          |          pt1.last_opened_at,
          |          pt1.total_opens,
          |          pt1.total_clicks,
          |          pt1.custom_fields,
          |          pt1.will_delete,
          |          pt1.email_bounced,
          |          pt1.force_send_invalid_email,
          |          pcat.name as prospect_category_custom,
          |          pcat.label_color as prospect_category_label_color,
          |          pcat.id as prospect_category_id_custom,
          |
          |          pt1.prospect_source,
          |          pt1.prospect_account_id as prospect_account_id,
          |          pa.uuid as prospect_account_uuid,
          |          pa.name as prospect_account_name,
          |
          |           COALESCE(ptags.prospecttags, '[]')  as tags,
          |           COALESCE(magic_columns.prospect_magic_columns, '[]')  as magic_columns,
          |
          |           null  as active_campaigns,
          |
          |          pt1.state,
          |          pt1.job_title,
          |          pt1.phone,
          |          pt1.phone_2,
          |          pt1.phone_3,
          |          pt1.linkedin_url,
          |          pt1.current_step_type,
          |          pt1.latest_reply_sentiment_uuid,
          |          pt1.latest_task_done_at,
          |          ccl.completed_at as last_call_made_at
          |
          |
          |          FROM
          |          (
          |
          |        SELECT
          |
          |         prospects.id,
          |          prospects.uuid AS prospect_uuid,
          |          prospects.account_id,
          |
          |          prospects.team_id,
          |
          |          prospects.first_name,
          |          prospects.last_name,
          |          pe.email,
          |          pe.email_domain,
          |
          |          (CASE WHEN pe.email_checked THEN pe.invalid_email ELSE NULL END) AS invalid_email,
          |
          |          prospects.list_id,
          |          prospects.list,
          |          prospects.company,
          |          prospects.city,
          |          prospects.country,
          |          prospects.timezone,
          |          prospects.sending_holiday_calendar_id,
          |
          |          prospects.created_at,
          |          prospects.updated_at,
          |          prospects.last_contacted_at,
          |          prospects.last_contacted_at_phone,
          |          prospects.last_replied_at,
          |          prospects.last_opened_at,
          |          prospects.total_opens,
          |          prospects.total_clicks,
          |          prospects.custom_fields,
          |          prospects.will_delete,
          |          pe.email_bounced,
          |          pe.force_send_invalid_email,
          |
          |          prospects.prospect_source,
          |          prospects.prospect_account_id as prospect_account_id,
          |          prospects.prospect_category_id_custom,
          |
          |          prospects.state,
          |          prospects.job_title,
          |          prospects.phone,
          |          prospects.phone_2,
          |          prospects.phone_3,
          |          prospects.linkedin_url,
          |          cp.current_step_type,
          |          prospects.latest_reply_sentiment_uuid,
          |          prospects.latest_task_done_at
          |
          |
          |          FROM prospects
          |           LEFT JOIN prospects_emails pe ON ((pe.prospect_id = prospects.id) AND (pe.team_id = prospects.team_id) AND pe.is_primary)
          |           INNER JOIN campaigns_prospects cp ON
          |                (
          |                  cp.prospect_id = prospects.id
          |                    AND
          |                    cp.active
          |                    AND
          |                    cp.campaign_id = ?
          |                    AND
          |                    cp.team_id = prospects.team_id
          |                )
          |
          |
          |
          |           WHERE
          |
          |           prospects.team_id = ?
          |
          |            AND prospects.account_id IN (?)
          |
          |           AND NOT prospects.will_delete
          |
          |
          |
          |            AND (  (  ( prospects.company ILIKE ? )  )  )
          |          ) pt1
          |
          |
          |
          |          INNER JOIN accounts a ON a.id = pt1.account_id
          |
          |          INNER JOIN prospect_categories_custom pcat on pcat.id = pt1.prospect_category_id_custom
          |
          |          LEFT JOIN prospect_accounts pa ON pa.id = pt1.prospect_account_id
          |
          |            LEFT JOIN LATERAL (
          |              SELECT
          |
          |              json_agg(
          |                json_build_object(
          |                  'tag_id', tags.id,
          |                  'tag', tags.tag,
          |                  'tag_uuid', tags.uuid
          |                  )
          |                ) as prospecttags
          |
          |              FROM tags
          |              JOIN tags_prospects ON tags_prospects.tag_id = tags.id
          |              WHERE tags_prospects.prospect_id = pt1.id
          |              AND NOT tags.hidden
          |
          |            ) AS ptags ON true
          |
          |
          |          LEFT JOIN LATERAL (
          |            SELECT
          |              json_agg(json_build_object(
          |               'column_output', cdp.magic_prompt_generated_output,
          |                'failed_message', cdp.failed_message,
          |                'column_name', cd.name,
          |                'status', cdp.status
          |              )) AS prospect_magic_columns
          |            FROM
          |              column_defs_prospects AS cdp
          |              JOIN column_defs AS cd ON cdp.column_id = cd.id
          |                AND cd.team_id = cdp.team_id
          |            WHERE
          |              cdp.prospect_id = pt1.id
          |              AND cdp.team_id = pt1.team_id
          |          ) AS magic_columns ON TRUE
          |
          |
          |          LEFT JOIN prospect_lists ON (
          |            prospect_lists.id = pt1.list_id
          |            AND prospect_lists.team_id = pt1.team_id
          |          )

          |           LEFT JOIN LATERAL (
          |    SELECT completed_at
          |    FROM call_conference_logs
          |    WHERE primary_prospect_id = pt1.id
          |      AND team_id = pt1.team_id
          |    ORDER BY completed_at DESC
          |    LIMIT 1
          |  ) AS ccl ON TRUE
          |
          |          WHERE
          |
          |           pt1.team_id = ?
          |
          |          ORDER BY pt1.team_id, pt1.id DESC
          |
          |         LIMIT ? OFFSET ?
          |         """
          .stripMargin

      val qry_str = prospectQuery.getQuerySQL(
        permittedAccountIds = Seq(2),
        teamId = 80,
        orgId = 39,
        data = searchJson,
        account = account,
        isInternalRequest = false,
        fetchType = SearchQuerySelectType.FULL_OBJECT,
        Logger = Logger,
//        emailNotCompulsoryEnabled = Some(false)
      )

      val split = expected_result.split(s"\\s+")
      val rhs = split.reduce((a1, a2) => {
        a1 + " " + a2
      })
      val lhs = qry_str.get._3.statement.split("\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })
      assert(lhs == rhs)
    }


      it("should build a query for  notes") {
          val searchString = """{"custom_flags":{"active_campaigns_detailed":false},"page":1,"query":{"owner_ids":[0],"clause":"AND","filters":[{"clause":"AND","filters":[{"field_display_name":"Has notes","field":"has_notes","allowedFilterOperators":[{"display_name":"Equals","key":"equals"}],"operator":"equals","value":"true","value_display_name":"True","field_type":"boolean","is_custom":false}]}]}}"""
          val searchJson = Json.parse(searchString).validate[SearchQuery].get
          val expected_result =
              """
                |SELECT
                |
                |          pt1.id,
                |          a.id AS owner_id,
                |          a.email AS owner_email,
                |          a.uuid AS owner_uuid,
                |          CONCAT(a.first_name, ' ',  a.last_name) AS owner_name,
                |
                |          pt1.prospect_uuid,
                |
                |          pt1.team_id,
                |
                |          pt1.first_name,
                |          pt1.last_name,
                |          pt1.email,
                |          pt1.email_domain,
                |
                |          pt1.invalid_email,
                |
                |          prospect_lists.name AS list,
                |          pt1.list_id,
                |          pt1.company,
                |          pt1.city,
                |          pt1.country,
                |          pt1.timezone,
                |
                |          pt1.created_at,
                |          pt1.updated_at,
                |          pt1.last_contacted_at,
                |          pt1.last_contacted_at_phone,
                |          pt1.last_replied_at,
                |          pt1.last_opened_at,
                |          pt1.total_opens,
                |          pt1.total_clicks,
                |          pt1.custom_fields,
                |          pt1.will_delete,
                |          pt1.email_bounced,
                |          pt1.force_send_invalid_email,
                |          pcat.name as prospect_category_custom,
                |          pcat.label_color as prospect_category_label_color,
                |          pcat.id as prospect_category_id_custom,
                |
                |          pt1.prospect_source,
                |          pt1.prospect_account_id as prospect_account_id,
                |          pa.uuid as prospect_account_uuid,
                |          pa.name as prospect_account_name,
                |
                |           COALESCE(ptags.prospecttags, '[]')  as tags,
                |           COALESCE(magic_columns.prospect_magic_columns, '[]')  as magic_columns,
                |
                |           null  as active_campaigns,
                |
                |          pt1.state,
                |          pt1.job_title,
                |          pt1.phone,
                |          pt1.phone_2,
                |          pt1.phone_3,
                |          pt1.linkedin_url,
                |
                |          pt1.latest_reply_sentiment_uuid,
                |          pt1.latest_task_done_at,
                |          ccl.completed_at as last_call_made_at
                |
                |
                |          FROM
                |          (
                |
                |        SELECT
                |
                |         prospects.id,
                |          prospects.uuid AS prospect_uuid,
                |          prospects.account_id,
                |
                |          prospects.team_id,
                |
                |          prospects.first_name,
                |          prospects.last_name,
                |          pe.email,
                |          pe.email_domain,
                |
                |          (CASE WHEN pe.email_checked THEN pe.invalid_email ELSE NULL END) AS invalid_email,
                |
                |          prospects.list_id,
                |          prospects.list,
                |          prospects.company,
                |          prospects.city,
                |          prospects.country,
                |          prospects.timezone,
                |          prospects.sending_holiday_calendar_id,
                |
                |          prospects.created_at,
                |          prospects.updated_at,
                |          prospects.last_contacted_at,
                |          prospects.last_contacted_at_phone,
                |          prospects.last_replied_at,
                |          prospects.last_opened_at,
                |          prospects.total_opens,
                |          prospects.total_clicks,
                |          prospects.custom_fields,
                |          prospects.will_delete,
                |          pe.email_bounced,
                |          pe.force_send_invalid_email,
                |
                |          prospects.prospect_source,
                |          prospects.prospect_account_id as prospect_account_id,
                |          prospects.prospect_category_id_custom,
                |
                |          prospects.state,
                |          prospects.job_title,
                |          prospects.phone,
                |          prospects.phone_2,
                |          prospects.phone_3,
                |          prospects.linkedin_url,
                |
                |          prospects.latest_reply_sentiment_uuid,
                |          prospects.latest_task_done_at
                |
                |
                |          FROM prospects
                |           LEFT JOIN prospects_emails pe ON ((pe.prospect_id = prospects.id) AND (pe.team_id = prospects.team_id) AND pe.is_primary)
                |
                |
                |
                |           WHERE
                |
                |           prospects.team_id = ?
                |
                |            AND prospects.account_id IN (?)
                |
                |           AND NOT prospects.will_delete
                |
                |
                |
                |
                |          ) pt1
                |
                |
                |
                |          INNER JOIN accounts a ON a.id = pt1.account_id
                |
                |          INNER JOIN prospect_categories_custom pcat on pcat.id = pt1.prospect_category_id_custom
                |
                |          LEFT JOIN prospect_accounts pa ON pa.id = pt1.prospect_account_id
                |
                |            LEFT JOIN LATERAL (
                |              SELECT
                |
                |              json_agg(
                |                json_build_object(
                |                  'tag_id', tags.id,
                |                  'tag', tags.tag,
                |                  'tag_uuid', tags.uuid
                |                  )
                |                ) as prospecttags
                |
                |              FROM tags
                |              JOIN tags_prospects ON tags_prospects.tag_id = tags.id
                |              WHERE tags_prospects.prospect_id = pt1.id
                |              AND NOT tags.hidden
                |
                |            ) AS ptags ON true
                |
                |
                |          LEFT JOIN LATERAL (
                |            SELECT
                |              json_agg(json_build_object(
                |               'column_output', cdp.magic_prompt_generated_output,
                |                'failed_message', cdp.failed_message,
                |                'column_name', cd.name,
                |                'status', cdp.status
                |              )) AS prospect_magic_columns
                |            FROM
                |              column_defs_prospects AS cdp
                |              JOIN column_defs AS cd ON cdp.column_id = cd.id
                |                AND cd.team_id = cdp.team_id
                |            WHERE
                |              cdp.prospect_id = pt1.id
                |              AND cdp.team_id = pt1.team_id
                |          ) AS magic_columns ON TRUE
                |
                |
                |          LEFT JOIN prospect_lists ON (
                |            prospect_lists.id = pt1.list_id
                |            AND prospect_lists.team_id = pt1.team_id
                |          )
                |
                |           LEFT JOIN LATERAL (
                |    SELECT completed_at
                |    FROM call_conference_logs
                |    WHERE primary_prospect_id = pt1.id
                |      AND team_id = pt1.team_id
                |    ORDER BY completed_at DESC
                |    LIMIT 1
                |  ) AS ccl ON TRUE
                |
                |
                |
                |
                |          WHERE
                |
                |           pt1.team_id = ?
                |
                |            AND (  (  ( EXISTS(SELECT * FROM notes WHERE notes.prospect_id = pt1.id AND notes.team_id = pt1.team_id) )  )  )
                |
                |          ORDER BY pt1.team_id, pt1.id DESC
                |
                |         LIMIT ? OFFSET ?
                |"""
                .stripMargin

          val qry_str = prospectQuery.getQuerySQL(
              permittedAccountIds = Seq(2),
              teamId = 80,
              orgId = 39,
              data = searchJson,
              account = account,
              isInternalRequest = false,
              fetchType = SearchQuerySelectType.FULL_OBJECT,
              Logger = Logger,
              //        emailNotCompulsoryEnabled = Some(false)
          )

          val split = expected_result.split(s"\\s+")
          val rhs = split.reduce((a1, a2) => {
              a1 + " " + a2
          })
          val lhs = qry_str.get._3.statement.split("\\s+").reduce((s1, s2) => {
              s1 + " " + s2
          })
          assert(lhs == rhs)
      }

      it("should build a query for last call made at") {
          val searchString = """{"custom_flags":{"active_campaigns_detailed":false},"page":1,"query":{"owner_ids":[0],"clause":"AND","filters":[{"clause":"AND","filters":[{"field_display_name":"Last call made at","field":"last_call_made_at","allowedFilterOperators":[{"display_name":"Contains","key":"contains"},{"display_name":"Not contains","key":"not_contains"},{"display_name":"Equals","key":"equals"},{"display_name":"Not equals","key":"not_equals"}],"operator":"greater_than","value":"1739433600000","value_display_name":"13-Feb-25","field_type":"date","is_custom":false}]}]}}"""
          val searchJson = Json.parse(searchString).validate[SearchQuery].get
          val expected_result =
              """
                |SELECT
                |
                |          pt1.id,
                |          a.id AS owner_id,
                |          a.email AS owner_email,
                |          a.uuid AS owner_uuid,
                |          CONCAT(a.first_name, ' ',  a.last_name) AS owner_name,
                |
                |          pt1.prospect_uuid,
                |
                |          pt1.team_id,
                |
                |          pt1.first_name,
                |          pt1.last_name,
                |          pt1.email,
                |          pt1.email_domain,
                |
                |          pt1.invalid_email,
                |
                |          prospect_lists.name AS list,
                |          pt1.list_id,
                |          pt1.company,
                |          pt1.city,
                |          pt1.country,
                |          pt1.timezone,
                |
                |          pt1.created_at,
                |          pt1.updated_at,
                |          pt1.last_contacted_at,
                |          pt1.last_contacted_at_phone,
                |          pt1.last_replied_at,
                |          pt1.last_opened_at,
                |          pt1.total_opens,
                |          pt1.total_clicks,
                |          pt1.custom_fields,
                |          pt1.will_delete,
                |          pt1.email_bounced,
                |          pt1.force_send_invalid_email,
                |          pcat.name as prospect_category_custom,
                |          pcat.label_color as prospect_category_label_color,
                |          pcat.id as prospect_category_id_custom,
                |
                |          pt1.prospect_source,
                |          pt1.prospect_account_id as prospect_account_id,
                |          pa.uuid as prospect_account_uuid,
                |          pa.name as prospect_account_name,
                |
                |           COALESCE(ptags.prospecttags, '[]')  as tags,
                |           COALESCE(magic_columns.prospect_magic_columns, '[]')  as magic_columns,
                |
                |           null  as active_campaigns,
                |
                |          pt1.state,
                |          pt1.job_title,
                |          pt1.phone,
                |          pt1.phone_2,
                |          pt1.phone_3,
                |          pt1.linkedin_url,
                |
                |          pt1.latest_reply_sentiment_uuid,
                |          pt1.latest_task_done_at,
                |          ccl.completed_at as last_call_made_at
                |
                |
                |          FROM
                |          (
                |
                |        SELECT
                |
                |         prospects.id,
                |          prospects.uuid AS prospect_uuid,
                |          prospects.account_id,
                |
                |          prospects.team_id,
                |
                |          prospects.first_name,
                |          prospects.last_name,
                |          pe.email,
                |          pe.email_domain,
                |
                |          (CASE WHEN pe.email_checked THEN pe.invalid_email ELSE NULL END) AS invalid_email,
                |
                |          prospects.list_id,
                |          prospects.list,
                |          prospects.company,
                |          prospects.city,
                |          prospects.country,
                |          prospects.timezone,
                |          prospects.sending_holiday_calendar_id,
                |
                |          prospects.created_at,
                |          prospects.updated_at,
                |          prospects.last_contacted_at,
                |          prospects.last_contacted_at_phone,
                |          prospects.last_replied_at,
                |          prospects.last_opened_at,
                |          prospects.total_opens,
                |          prospects.total_clicks,
                |          prospects.custom_fields,
                |          prospects.will_delete,
                |          pe.email_bounced,
                |          pe.force_send_invalid_email,
                |
                |          prospects.prospect_source,
                |          prospects.prospect_account_id as prospect_account_id,
                |          prospects.prospect_category_id_custom,
                |
                |          prospects.state,
                |          prospects.job_title,
                |          prospects.phone,
                |          prospects.phone_2,
                |          prospects.phone_3,
                |          prospects.linkedin_url,
                |
                |          prospects.latest_reply_sentiment_uuid,
                |          prospects.latest_task_done_at
                |
                |
                |          FROM prospects
                |           LEFT JOIN prospects_emails pe ON ((pe.prospect_id = prospects.id) AND (pe.team_id = prospects.team_id) AND pe.is_primary)
                |
                |
                |
                |           WHERE
                |
                |           prospects.team_id = ?
                |
                |            AND prospects.account_id IN (?)
                |
                |           AND NOT prospects.will_delete
                |
                |
                |
                |
                |          ) pt1
                |
                |
                |
                |          INNER JOIN accounts a ON a.id = pt1.account_id
                |
                |          INNER JOIN prospect_categories_custom pcat on pcat.id = pt1.prospect_category_id_custom
                |
                |          LEFT JOIN prospect_accounts pa ON pa.id = pt1.prospect_account_id
                |
                |            LEFT JOIN LATERAL (
                |              SELECT
                |
                |              json_agg(
                |                json_build_object(
                |                  'tag_id', tags.id,
                |                  'tag', tags.tag,
                |                  'tag_uuid', tags.uuid
                |                  )
                |                ) as prospecttags
                |
                |              FROM tags
                |              JOIN tags_prospects ON tags_prospects.tag_id = tags.id
                |              WHERE tags_prospects.prospect_id = pt1.id
                |              AND NOT tags.hidden
                |
                |            ) AS ptags ON true
                |
                |
                |          LEFT JOIN LATERAL (
                |            SELECT
                |              json_agg(json_build_object(
                |               'column_output', cdp.magic_prompt_generated_output,
                |                'failed_message', cdp.failed_message,
                |                'column_name', cd.name,
                |                'status', cdp.status
                |              )) AS prospect_magic_columns
                |            FROM
                |              column_defs_prospects AS cdp
                |              JOIN column_defs AS cd ON cdp.column_id = cd.id
                |                AND cd.team_id = cdp.team_id
                |            WHERE
                |              cdp.prospect_id = pt1.id
                |              AND cdp.team_id = pt1.team_id
                |          ) AS magic_columns ON TRUE
                |
                |
                |          LEFT JOIN prospect_lists ON (
                |            prospect_lists.id = pt1.list_id
                |            AND prospect_lists.team_id = pt1.team_id
                |          )
                |
                |           LEFT JOIN LATERAL (
                |    SELECT completed_at
                |    FROM call_conference_logs
                |    WHERE primary_prospect_id = pt1.id
                |      AND team_id = pt1.team_id
                |    ORDER BY completed_at DESC
                |    LIMIT 1
                |  ) AS ccl ON TRUE
                |
                |
                |
                |
                |
                |          WHERE
                |
                |           pt1.team_id = ?
                |
                |            AND (  (  (ccl.completed_at >= TO_TIMESTAMP(? / 1000))  )  )
                |
                |          ORDER BY pt1.team_id, pt1.id DESC
                |
                |         LIMIT ? OFFSET ?
                |"""
                .stripMargin

          val qry_str = prospectQuery.getQuerySQL(
              permittedAccountIds = Seq(2),
              teamId = 80,
              orgId = 39,
              data = searchJson,
              account = account,
              isInternalRequest = false,
              fetchType = SearchQuerySelectType.FULL_OBJECT,
              Logger = Logger,
              //        emailNotCompulsoryEnabled = Some(false)
          )

          val split = expected_result.split(s"\\s+")
          val rhs = split.reduce((a1, a2) => {
              a1 + " " + a2
          })
          val lhs = qry_str.get._3.statement.split("\\s+").reduce((s1, s2) => {
              s1 + " " + s2
          })
          assert(lhs == rhs)
      }

    it("should build a query for has tag") {
      val data = SearchQuery(
        Some(SearchQueryMainClause(
          None,
          List(0),
          "AND",
          List(SearchQuerySubClause(
            "AND",
            List(SearchQuerySubFilterData(
              "has_tag",
              Some("Has tag"),
              SearchQueryOperators.EQUAL,
              "178",
              Some("prospect - tag - 1"),
              FieldTypeEnum.NUMBER,
              false,
              Some(List(SearchQueryColumnOperator("Equals", SearchQueryOperators.EQUAL)))),
              SearchQuerySubFilterData(
                "has_tag", Some("Has tag"),
                SearchQueryOperators.EQUAL,
                "182",
                Some("newtag"),
                FieldTypeEnum.NUMBER,
                false, Some(List(SearchQueryColumnOperator("Equals", SearchQueryOperators.EQUAL))))))))),
        Some(1),Some(Json.obj({"active_campaigns_detailed" -> false})), None, None, None, None)

      val expected_result =
        """
          |          SELECT
          |
          |          pt1.id,
          |          a.id AS owner_id,
          |          a.email AS owner_email,
          |          a.uuid AS owner_uuid,
          |          CONCAT(a.first_name, ' ',  a.last_name) AS owner_name,
          |
          |          pt1.prospect_uuid,
          |
          |          pt1.team_id,
          |
          |          pt1.first_name,
          |          pt1.last_name,
          |          pt1.email,
          |          pt1.email_domain,
          |
          |          pt1.invalid_email,
          |
          |          prospect_lists.name AS list,
          |          pt1.list_id,
          |          pt1.company,
          |          pt1.city,
          |          pt1.country,
          |          pt1.timezone,
          |
          |          pt1.created_at,
          |          pt1.updated_at,
          |          pt1.last_contacted_at,
          |          pt1.last_contacted_at_phone,
          |          pt1.last_replied_at,
          |          pt1.last_opened_at,
          |          pt1.total_opens,
          |          pt1.total_clicks,
          |          pt1.custom_fields,
          |          pt1.will_delete,
          |          pt1.email_bounced,
          |          pt1.force_send_invalid_email,
          |          pcat.name as prospect_category_custom,
          |          pcat.label_color as prospect_category_label_color,
          |          pcat.id as prospect_category_id_custom,
          |
          |          pt1.prospect_source,
          |          pt1.prospect_account_id as prospect_account_id,
          |          pa.uuid as prospect_account_uuid,
          |          pa.name as prospect_account_name,
          |
          |           COALESCE(ptags.prospecttags, '[]')  as tags,
          |           COALESCE(magic_columns.prospect_magic_columns, '[]')  as magic_columns,
          |
          |           null  as active_campaigns,
          |
          |          pt1.state,
          |          pt1.job_title,
          |          pt1.phone,
          |          pt1.phone_2,
          |          pt1.phone_3,
          |          pt1.linkedin_url,
          |
          |          pt1.latest_reply_sentiment_uuid,
          |          pt1.latest_task_done_at,
          |          ccl.completed_at as last_call_made_at
          |
          |
          |          FROM
          |          (
          |
          |        SELECT
          |
          |         prospects.id,
          |          prospects.uuid AS prospect_uuid,
          |          prospects.account_id,
          |
          |          prospects.team_id,
          |
          |          prospects.first_name,
          |          prospects.last_name,
          |          pe.email,
          |          pe.email_domain,
          |
          |          (CASE WHEN pe.email_checked THEN pe.invalid_email ELSE NULL END) AS invalid_email,
          |
          |          prospects.list_id,
          |          prospects.list,
          |          prospects.company,
          |          prospects.city,
          |          prospects.country,
          |          prospects.timezone,
          |          prospects.sending_holiday_calendar_id,
          |
          |          prospects.created_at,
          |          prospects.updated_at,
          |          prospects.last_contacted_at,
          |          prospects.last_contacted_at_phone,
          |          prospects.last_replied_at,
          |          prospects.last_opened_at,
          |          prospects.total_opens,
          |          prospects.total_clicks,
          |          prospects.custom_fields,
          |          prospects.will_delete,
          |          pe.email_bounced,
          |          pe.force_send_invalid_email,
          |
          |          prospects.prospect_source,
          |          prospects.prospect_account_id as prospect_account_id,
          |          prospects.prospect_category_id_custom,
          |
          |          prospects.state,
          |          prospects.job_title,
          |          prospects.phone,
          |          prospects.phone_2,
          |          prospects.phone_3,
          |          prospects.linkedin_url,
          |
          |          prospects.latest_reply_sentiment_uuid,
          |          prospects.latest_task_done_at
          |
          |
          |          FROM prospects
          |           LEFT JOIN prospects_emails pe ON ((pe.prospect_id = prospects.id) AND (pe.team_id = prospects.team_id) AND pe.is_primary)
          |
          |
          |
          |           WHERE
          |
          |           prospects.team_id = ?
          |
          |            AND prospects.account_id IN (?)
          |
          |           AND NOT prospects.will_delete
          |
          |
          |
          |
          |          ) pt1
          |
          |
          |
          |          INNER JOIN accounts a ON a.id = pt1.account_id
          |
          |          INNER JOIN prospect_categories_custom pcat on pcat.id = pt1.prospect_category_id_custom
          |
          |          LEFT JOIN prospect_accounts pa ON pa.id = pt1.prospect_account_id
          |
          |            LEFT JOIN LATERAL (
          |              SELECT
          |
          |              json_agg(
          |                json_build_object(
          |                  'tag_id', tags.id,
          |                  'tag', tags.tag,
          |                  'tag_uuid', tags.uuid
          |                  )
          |                ) as prospecttags
          |
          |              FROM tags
          |              JOIN tags_prospects ON tags_prospects.tag_id = tags.id
          |              WHERE tags_prospects.prospect_id = pt1.id
          |              AND NOT tags.hidden
          |
          |            ) AS ptags ON true
          |
          |
          |          LEFT JOIN LATERAL (
          |            SELECT
          |              json_agg(json_build_object(
          |               'column_output', cdp.magic_prompt_generated_output,
          |                'failed_message', cdp.failed_message,
          |                'column_name', cd.name,
          |                'status', cdp.status
          |              )) AS prospect_magic_columns
          |            FROM
          |              column_defs_prospects AS cdp
          |              JOIN column_defs AS cd ON cdp.column_id = cd.id
          |                AND cd.team_id = cdp.team_id
          |            WHERE
          |              cdp.prospect_id = pt1.id
          |              AND cdp.team_id = pt1.team_id
          |          ) AS magic_columns ON TRUE
          |
          |
          |          LEFT JOIN prospect_lists ON (
          |            prospect_lists.id = pt1.list_id
          |            AND prospect_lists.team_id = pt1.team_id
          |          )
          |
          |           LEFT JOIN LATERAL (
          |    SELECT completed_at
          |    FROM call_conference_logs
          |    WHERE primary_prospect_id = pt1.id
          |      AND team_id = pt1.team_id
          |    ORDER BY completed_at DESC
          |    LIMIT 1
          |  ) AS ccl ON TRUE
          |
          |
          |
          |
          |
          |          WHERE
          |
          |           pt1.team_id = ?
          |
          |            AND (  (   ( EXISTS(
          |                    SELECT * FROM tags_prospects
          |                    inner join tags on tags.id = tags_prospects.tag_id AND tags.team_id = ?
          |                    WHERE tags_prospects.prospect_id = pt1.id  AND tags_prospects.tag_id = ?
          |                   ) )  AND   ( EXISTS(
          |                    SELECT * FROM tags_prospects
          |                    inner join tags on tags.id = tags_prospects.tag_id AND tags.team_id = ?
          |                    WHERE tags_prospects.prospect_id = pt1.id  AND tags_prospects.tag_id = ?
          |                   ) )  )  )
          |
          |          ORDER BY pt1.team_id, pt1.id DESC
          |
          |         LIMIT ? OFFSET ?
          |"""
          .stripMargin

      val qry_str = prospectQuery.getQuerySQL(
        permittedAccountIds = Seq(2),
        teamId = 80,
        orgId = 39,
        data = data,
        account = account,
        isInternalRequest = false,
        fetchType = SearchQuerySelectType.FULL_OBJECT,
        Logger = Logger,
        //        emailNotCompulsoryEnabled = Some(false)
      )

      val split = expected_result.split(s"\\s+")
      val rhs = split.reduce((a1, a2) => {
        a1 + " " + a2
      })
      val lhs = qry_str.get._3.statement.split("\\s+").reduce((s1, s2) => {
        s1 + " " + s2
      })
      assert(lhs == rhs)
    }


  }


}
