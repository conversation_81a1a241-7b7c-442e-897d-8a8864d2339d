package app.api.webhooks

import org.apache.pekko.actor.ActorSystem
import api.accounts.models.OrgId
import api.integrations.{WebhookSourceType, WebhookVersions}
import api.sr_audit_logs.models.EventType
import api.sr_audit_logs.models.EventType.TASK_REPLY_SENTIMENT_UPDATED
import api.webhooks.{UpdateWebhookForm, Webhook, WebhookInDB, WebhookService}
import org.joda.time.DateTime
import org.scalamock.matchers.ArgCapture.CaptureOne
import org.scalamock.scalatest.AsyncMockFactory
import org.scalatest.funspec.AsyncFunSpec
import play.api.libs.ws.ahc.AhcWSClient
import utils.SRLogger
import utils.testapp.TestAppExecutionContext

import scala.concurrent.ExecutionContext
import scala.util.{Failure, Success}

class WebhookServiceSpec
  extends AsyncFunSpec
  with AsyncMockFactory {

  implicit lazy val system: ActorSystem = TestAppExecutionContext.actorSystem
  implicit lazy val wSClient: AhcWSClient = TestAppExecutionContext.wsClient
  implicit lazy val actorContext: ExecutionContext = system.dispatcher


  given Logger: SRLogger = new SRLogger("WebhookServiceSpec Unit test")

  val webhookDAO = mock[Webhook]
  val triggerService = new WebhookService(webhookDAO = webhookDAO)
  val orgId = OrgId(59L)

  describe("updateEventsForReplySentiment") {
    it("should not add task reply sentiment event if email reply sentiment event is absent") {

      val events: Seq[EventType] = Seq(EventType.EMAIL_OPENED, EventType.EMAIL_INVALID)

      val res = triggerService.updateEventsForReplySentiment(
        events = events
      )

      assert(!res.contains(EventType.TASK_REPLY_SENTIMENT_UPDATED))
    }

    it("should add task reply sentiment event if email reply sentiment event is present") {

      val events: Seq[EventType] = Seq(EventType.EMAIL_OPENED, EventType.EMAIL_INVALID, EventType.REPLY_SENTIMENT_UPDATED)

      val res = triggerService.updateEventsForReplySentiment(
        events = events
      )

      assert(res.contains(EventType.TASK_REPLY_SENTIMENT_UPDATED))
      assert(res.contains(EventType.REPLY_SENTIMENT_UPDATED))
    }


    it("should not add task reply sentiment event if email and task reply sentiment event are present") {

      val events: Seq[EventType] = Seq(EventType.EMAIL_OPENED, EventType.EMAIL_INVALID, EventType.REPLY_SENTIMENT_UPDATED, EventType.TASK_REPLY_SENTIMENT_UPDATED)

      val res = triggerService.updateEventsForReplySentiment(
        events = events
      )

      println(s"events ::: ${res}")

      assert(res.count(_ == EventType.TASK_REPLY_SENTIMENT_UPDATED) == 1)
      assert(res.contains(EventType.REPLY_SENTIMENT_UPDATED))
    }

    it("should remove task reply sentiment event if email reply sentiment event is not present") {

      val events: Seq[EventType] = Seq(EventType.EMAIL_OPENED, EventType.EMAIL_INVALID, TASK_REPLY_SENTIMENT_UPDATED)

      val res = triggerService.updateEventsForReplySentiment(
        events = events
      )

      assert(!res.contains(EventType.TASK_REPLY_SENTIMENT_UPDATED))
      assert(!res.contains(EventType.REPLY_SENTIMENT_UPDATED))
    }
  }


  describe("updateWebhook") {



    it("should return InValidWebhookUrlError") {

      val id = 123
      val permittedAccountIds: Seq[Long] = Seq(2221, 3221, 4221)
      val accountId: Long = 2
      val teamId: Long = 2
      val taId = 777
      val data =  UpdateWebhookForm(
        target_url = "invalid_webhook_link",
        events = Some(Seq(EventType.EMAIL_OPENED)),
        version = None,
        campaign_ids = Seq(),
        reply_sentiment_uuids = Seq(),
        prospect_category_ids = Seq()
      )

      val res = triggerService.updateWebhook(id = id,
        permittedAccountIds = permittedAccountIds,
        accountId = accountId,
        teamId = teamId,
        taId = taId,
        data = data,
        Logger = Logger,
        orgId = orgId
      )

      assert(res.isLeft)


    }

    it("should return WebhookEventsEmptyError") {

      val id = 123
      val permittedAccountIds: Seq[Long] = Seq(2221, 3221, 4221)
      val accountId: Long = 2
      val teamId: Long = 2
      val taId = 777
      val data =  UpdateWebhookForm(
        target_url = "https://app.smartreach.io/login?tid=0",
        events = None,
        version = None,
        campaign_ids = Seq(),
        reply_sentiment_uuids = Seq(),
        prospect_category_ids = Seq()
      )

      val res = triggerService.updateWebhook(id = id,
        permittedAccountIds = permittedAccountIds,
        accountId = accountId,
        teamId = teamId,
        taId = taId,
        data = data,
        Logger = Logger,
        orgId = orgId
      )

      assert(res.isLeft)


    }


    it("should return update SQLException findAllEventsByWebhookID non empty") {

      val id = 123L
      val permittedAccountIds: Seq[Long] = Seq(2221, 3221, 4221)

      val accountId: Long = 2
      val teamId: Long = 2
      val taId = 777
      val data =  UpdateWebhookForm(
        target_url = "https://app.smartreach.io/login?tid=0",
        events = Some(Seq(EventType.EMAIL_OPENED)),
        version = None,
        campaign_ids = Seq(),
        reply_sentiment_uuids = Seq(),
        prospect_category_ids = Seq()
      )

      (webhookDAO.update)
        .expects(id, permittedAccountIds, teamId, data.target_url, Seq(), Seq(), Seq(), data.version)
        .returning(Failure(new Exception("SQL Exception")))

      (webhookDAO.findAllEventsByWebhookID)
        .expects(id, teamId, permittedAccountIds)
        .returning(Success(Seq()))

      val res = triggerService.updateWebhook(id = id,
        permittedAccountIds = permittedAccountIds,
        accountId = accountId,
        teamId = teamId,
        taId = taId,
        data = data,
        Logger = Logger,
        orgId = orgId
      )

      assert(res.isLeft)


    }

    it("should return update WebhookNotFoundError findAllEventsByWebhookID non empty") {

      val id = 123L
      val permittedAccountIds: Seq[Long] = Seq(2221, 3221, 4221)

      val accountId: Long = 2
      val teamId: Long = 2
      val taId = 777L
      val data =  UpdateWebhookForm(
        target_url = "https://app.smartreach.io/login?tid=0",
        events = Some(Seq(EventType.EMAIL_OPENED)),
        version = None,
        campaign_ids = Seq(),
        reply_sentiment_uuids = Seq(),
        prospect_category_ids = Seq()
      )


      (webhookDAO.update)
        .expects(id, permittedAccountIds, teamId, data.target_url, Seq(), Seq(), Seq(), data.version)
        .returning(Success(None))

      (webhookDAO.findAllEventsByWebhookID)
        .expects(id, teamId, permittedAccountIds)
        .returning(Success(Seq()))

      (webhookDAO.deleteEvents)
        .expects(id, permittedAccountIds, teamId, List())
        .returning(Success(0))

      (webhookDAO.createOrUpdateEvents)
        .expects(id, teamId, accountId, List(EventType.EMAIL_OPENED))
        .returning(Success(Seq()))

      val res = triggerService.updateWebhook(id = id,
        permittedAccountIds = permittedAccountIds,
        accountId = accountId,
        teamId = teamId,
        taId = taId,
        data = data,
        Logger = Logger,
        orgId = orgId
      )

      assert(res.isLeft)


    }

    it("should return update success createOrUpdateEvents SQLException findAllEventsByWebhookID non empty") {

      val id = 123L
      val permittedAccountIds: Seq[Long] = Seq(2221, 3221, 4221)

      val accountId: Long = 2
      val teamId: Long = 2
      val taId = 777L
      val data =  UpdateWebhookForm(
        target_url = "https://app.smartreach.io/login?tid=0",
        events = Some(Seq(EventType.EMAIL_OPENED)),
        version = None,
        campaign_ids = Seq(),
        reply_sentiment_uuids = Seq(),
        prospect_category_ids = Seq()
      )


      val webhookInDB = WebhookInDB(
        id = id.toInt,
        account_id = accountId,
        team_id = teamId,
        ta_id = taId,
        target_url = data.target_url,
        events =  Some(Seq(EventType.EMAIL_OPENED.toString)),
        source = WebhookSourceType.SMARTREACH,
        version = WebhookVersions.V2,
        active = true,
        created_at = DateTime.now(),
        campaign_ids = Seq(),
        reply_sentiment_uuids = Seq(),
        prospect_category_ids = Seq()
      )


      val eventsTobeDeleted = CaptureOne[Seq[String]]()
      (webhookDAO.deleteEvents)
        .expects(id, permittedAccountIds, teamId, capture(eventsTobeDeleted))
        .returning(Failure(new Exception("SQL exception")))

      (webhookDAO.update)
        .expects(id, permittedAccountIds, teamId, data.target_url, Seq(), Seq(), Seq(), data.version)
        .returning(Success(Some(webhookInDB)))

      (webhookDAO.findAllEventsByWebhookID)
        .expects(id, teamId, permittedAccountIds)
        .returning(Success(Seq(EventType.EMAIL_OPENED.toString)))

      val res = triggerService.updateWebhook(id = id,
        permittedAccountIds = permittedAccountIds,
        accountId = accountId,
        teamId = teamId,
        taId = taId,
        data = data,
        Logger = Logger,
        orgId = orgId
      )

      assert(res.isLeft)


    }

    it("should return update success deleteEvents  Success createOrUpdateEvents SQLException:: findAllEventsByWebhookID non empty") {

      val id = 123L
      val permittedAccountIds: Seq[Long] = Seq(2221, 3221, 4221)

      val accountId: Long = 2
      val teamId: Long = 2
      val taId = 777L
      val data =  UpdateWebhookForm(
        target_url = "https://app.smartreach.io/login?tid=0",
        events = Some(Seq(EventType.EMAIL_OPENED)),
        version = None,
        campaign_ids = Seq(),
        reply_sentiment_uuids = Seq(),
        prospect_category_ids = Seq()
      )


      val webhookInDB = WebhookInDB(
        id = id.toInt,
        account_id = accountId,
        team_id = teamId,
        ta_id = taId,
        target_url = data.target_url,
        events =  Some(Seq(EventType.EMAIL_OPENED.toString)),
        source = WebhookSourceType.SMARTREACH,
        version = WebhookVersions.V2,
        active = true,
        created_at = DateTime.now(),
        campaign_ids = Seq(),
        reply_sentiment_uuids = Seq(),
        prospect_category_ids = Seq()
      )

      (webhookDAO.createOrUpdateEvents)
        .expects(id, teamId, accountId, data.events.get)
        .returning(Failure(new Exception("SQL exception")))

      val eventsTobeDeleted = CaptureOne[Seq[String]]()
      (webhookDAO.deleteEvents)
        .expects(id, permittedAccountIds, teamId, capture(eventsTobeDeleted))
        .returning(Success(1))

      (webhookDAO.update)
        .expects(id, permittedAccountIds, teamId, data.target_url, Seq(), Seq(), Seq(), data.version)
        .returning(Success(Some(webhookInDB)))

      (webhookDAO.findAllEventsByWebhookID)
        .expects(id, teamId, permittedAccountIds)
        .returning(Success(Seq(EventType.EMAIL_OPENED.toString)))

      val res = triggerService.updateWebhook(id = id,
        permittedAccountIds = permittedAccountIds,
        accountId = accountId,
        teamId = teamId,
        taId = taId,
        data = data,
        Logger = Logger,
        orgId = orgId
      )

      assert(res.isLeft)


    }


    it("should return update success deleteEvents Success createOrUpdateEvents Success:: findAllEventsByWebhookID non empty") {

      val id = 123L
      val permittedAccountIds: Seq[Long] = Seq(2221, 3221, 4221)

      val accountId: Long = 2
      val teamId: Long = 2
      val taId = 777L
      val data =  UpdateWebhookForm(
        target_url = "https://app.smartreach.io/login?tid=0",
        events = Some(Seq(EventType.EMAIL_OPENED)),
        version = None,
        campaign_ids = Seq(),
        reply_sentiment_uuids = Seq(),
        prospect_category_ids = Seq()
      )


      val webhookInDB = WebhookInDB(
        id = id.toInt,
        account_id = accountId,
        team_id = teamId,
        ta_id = taId,
        target_url = data.target_url,
        events =  Some(Seq(EventType.EMAIL_OPENED.toString)),
        source = WebhookSourceType.SMARTREACH,
        version = WebhookVersions.V2,
        active = true,
        created_at = DateTime.now(),
        campaign_ids = Seq(),
        reply_sentiment_uuids = Seq(),
        prospect_category_ids = Seq()
      )

      (webhookDAO.createOrUpdateEvents)
        .expects(id, teamId, accountId, data.events.get)
        .returning(Success(Seq(id)))

      val eventsTobeDeleted = CaptureOne[Seq[String]]()
      (webhookDAO.deleteEvents)
        .expects(id, permittedAccountIds, teamId, capture(eventsTobeDeleted))
        .returning(Success(1))

      (webhookDAO.update)
        .expects(id, permittedAccountIds, teamId, data.target_url, Seq(), Seq(), Seq(), data.version)
        .returning(Success(Some(webhookInDB)))

      (webhookDAO.findAllEventsByWebhookID)
        .expects(id, teamId, permittedAccountIds)
        .returning(Success(Seq(EventType.EMAIL_OPENED.toString)))

      val res = triggerService.updateWebhook(id = id,
        permittedAccountIds = permittedAccountIds,
        accountId = accountId,
        teamId = teamId,
        taId = taId,
        data = data,
        Logger = Logger,
        orgId = orgId
      )

      assert(res.isRight)


    }

  }

}
