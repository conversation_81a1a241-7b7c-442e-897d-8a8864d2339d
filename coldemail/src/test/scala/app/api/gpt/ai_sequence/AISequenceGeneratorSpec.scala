/*
package app.api.gpt.ai_sequence

import api.AppConfig
import api.accounts.TeamId
import api.accounts.models.{AccountId, OrgId}
import api.campaigns.models.CampaignStepData
import api.campaigns.services.{CampaignId, CampaignStepService, CreateCampaignStepVariantError}
import api.campaigns.{CampaignDAO, CampaignStepVariant, CampaignStepVariantCreateOrUpdate}
import api.columns.ColumnDefsProspectsDetails
import api.gpt.ai_sequence.{AISequenceGenerator, CampaignStepCreationRequestData}
import api.gpt.services.GptApiService
import api.sr_ai.service.CampaignGenerationService
import org.apache.pekko.actor.ActorSystem
import org.apache.pekko.stream.Materializer
import org.scalamock.scalatest.AsyncMockFactory
import org.scalatest.ParallelTestExecution
import org.scalatest.funspec.AsyncFunSpec
import play.api.libs.ws.WSClient
import play.api.libs.ws.ahc.AhcWSClient
import utils.helpers.LogHelpers
import utils.{PusherService, SRLogger}

import scala.concurrent.{ExecutionContext, Future}


class AISequenceGeneratorSpec extends AsyncFunSpec with AsyncMockFactory with ParallelTestExecution {

  implicit lazy val system: ActorSystem = ActorSystem()
  implicit lazy val ec: ExecutionContext = system.dispatcher
  implicit lazy val wSClient: AhcWSClient = AhcWSClient()
  given Logger: SRLogger = new SRLogger("AISequenceGeneratorSpec")
  implicit val materializer: Materializer = Materializer(system)

  val gptApiService: GptApiService = mock[GptApiService]
  val campaignStepService: CampaignStepService = mock[CampaignStepService]
  val campaignDAO: CampaignDAO = mock[CampaignDAO]
  val pusherService: PusherService = mock[PusherService]
  
  val campaignGenerationService: CampaignGenerationService = mock[CampaignGenerationService]

  val aiSequenceGenerator: AISequenceGenerator = new AISequenceGenerator(
    gptApiService = gptApiService,
    campaignStepService = campaignStepService,
    campaignDAO = campaignDAO,
    campaignGenerationService = campaignGenerationService,
    pusherService = pusherService
  )

  private val orgId_7 = OrgId(id = 7)
  private val accountId_61 = AccountId(id = 61)
  private val teamId_23 = TeamId(id = 23)
  private val taId_13: Long = 13
  private val campaignId_2 = CampaignId(2)

  private val campaignStepCreationRequestData = CampaignStepCreationRequestData(
    parentId = 0,
    subject = "test",
    stepNumber = 1,
    previousChat = List(),
    isFirstStep = true,
    headStepId = None,
    isVariant = false
  )

  describe("Test createEmailVariantWithRetry") {

    it("should generate an email variant with valid body content") {

      val body = "test completion"

      (gptApiService.generateCompletionsForChat(
        _: List[Map[String, String]],
        _: OrgId,
        _: Option[ColumnDefsProspectsDetails],
      )(
        _: WSClient,
        _: ExecutionContext,
        _: SRLogger,
      )).expects(
        *,
        orgId_7,
        None,
        wSClient,
        ec,
        Logger
      ).returning(Future.successful(body))

      val request = campaignStepCreationRequestData

      val stepData: CampaignStepData.AutoEmailStep = CampaignStepData.AutoEmailStep(
        subject = if (request.isFirstStep)
          request.subject
        else
          "{{previous_subject}}",
        body = body
      )

      val variantData: CampaignStepVariantCreateOrUpdate = CampaignStepVariantCreateOrUpdate(
        parent_id = if (request.isVariant) 0 else request.parentId, // for variant, parentId is 0 as we use already created step id
        step_data = stepData,
        step_delay = if (request.isFirstStep) 0 else AppConfig.stepDelayForAISequenceGenerator, // for initial step, delay is 0
        notes = None,
        priority = None
      )

      val stepId = if (request.isVariant) {
        request.parentId

      } else {
        0
      }

      (campaignStepService.createVariant(
        _: Long,
        _: CampaignStepVariantCreateOrUpdate,
        _: Long,
        _: Long,
        _: Long,
        _: Long,
        _: Long,
        _: Option[Long],
      )(
        _: SRLogger,
        _: ExecutionContext,
        _: Materializer,
        _: WSClient,
      )).expects(
        orgId_7.id,
        variantData,
        teamId_23.id,
        accountId_61.id,
        taId_13,
        stepId,
        campaignId_2.id,
        None,
        Logger,
        ec,
        materializer,
        wSClient,
      ).returning(
        Future.successful(
          Right(
            CampaignStepVariant(
              id = 1,
              step_id = stepId,
              campaign_id = campaignId_2.id,
              template_id = None,
              step_data = stepData,
              active = true,
            )
          )
        )
      )

      val result = aiSequenceGenerator.createEmailVariantWithRetry(
        accountId = accountId_61,
        teamId = teamId_23,
        taId = taId_13,
        orgId = orgId_7,
        campaignId = campaignId_2,
        request = request,
      )

      result.map { _ =>

        assert(true)

      }.recover { e =>

        println(LogHelpers.getStackTraceAsString(e))

        assert(false)

      }

    }


    it("should return email variant if first attempt fails but second attempt succeeds") {

      val invalidBody = "test completion {{% spin %}}"

      // first attempt fails

      (gptApiService.generateCompletionsForChat(
        _: List[Map[String, String]],
        _: OrgId,
        _: Option[ColumnDefsProspectsDetails],
      )(
        _: WSClient,
        _: ExecutionContext,
        _: SRLogger,
      )).expects(
        *,
        orgId_7,
        None,
        wSClient,
        ec,
        Logger
      ).returning(Future.successful(invalidBody))

      val request = campaignStepCreationRequestData

      val stepData: CampaignStepData.AutoEmailStep = CampaignStepData.AutoEmailStep(
        subject = if (request.isFirstStep)
          request.subject
        else
          "{{previous_subject}}",
        body = invalidBody
      )

      val variantData: CampaignStepVariantCreateOrUpdate = CampaignStepVariantCreateOrUpdate(
        parent_id = if (request.isVariant) 0 else request.parentId, // for variant, parentId is 0 as we use already created step id
        step_data = stepData,
        step_delay = if (request.isFirstStep) 0 else AppConfig.stepDelayForAISequenceGenerator, // for initial step, delay is 0
        notes = None,
        priority = None
      )

      val stepId = if (request.isVariant) {
        request.parentId
      } else {
        0
      }

      (campaignStepService.createVariant(
        _: Long,
        _: CampaignStepVariantCreateOrUpdate,
        _: Long,
        _: Long,
        _: Long,
        _: Long,
        _: Long,
        _: Option[Long],
      )(
        _: SRLogger,
        _: ExecutionContext,
        _: Materializer,
        _: WSClient,
      )).expects(
        orgId_7.id,
        variantData,
        teamId_23.id,
        accountId_61.id,
        taId_13,
        stepId,
        campaignId_2.id,
        None,
        Logger,
        ec,
        materializer,
        wSClient,
      ).returning(
        Future.successful(
          Left(
            CreateCampaignStepVariantError.ValidateCampaignTemplateError(
              err = new Exception("Error while validating campaign template")
            )
          )
        )
      )

      // second attempt - success

      val validBody = "test completion {{% spin %}}"

      (gptApiService.generateCompletionsForChat(
        _: List[Map[String, String]],
        _: OrgId,
        _: Option[ColumnDefsProspectsDetails],
      )(
        _: WSClient,
        _: ExecutionContext,
        _: SRLogger,
      )).expects(
        *,
        orgId_7,
        None,
        wSClient,
        ec,
        Logger
      ).returning(Future.successful(validBody))

      val stepData2: CampaignStepData.AutoEmailStep = CampaignStepData.AutoEmailStep(
        subject = if (request.isFirstStep)
          request.subject
        else
          "{{previous_subject}}",
        body = validBody
      )

      val variantData2: CampaignStepVariantCreateOrUpdate = CampaignStepVariantCreateOrUpdate(
        parent_id = if (request.isVariant) 0 else request.parentId, // for variant, parentId is 0 as we use already created step id
        step_data = stepData2,
        step_delay = if (request.isFirstStep) 0 else AppConfig.stepDelayForAISequenceGenerator, // for initial step, delay is 0
        notes = None,
        priority = None
      )

      (campaignStepService.createVariant(
        _: Long,
        _: CampaignStepVariantCreateOrUpdate,
        _: Long,
        _: Long,
        _: Long,
        _: Long,
        _: Long,
        _: Option[Long],
      )(
        _: SRLogger,
        _: ExecutionContext,
        _: Materializer,
        _: WSClient,
      )).expects(
        orgId_7.id,
        variantData2,
        teamId_23.id,
        accountId_61.id,
        taId_13,
        stepId,
        campaignId_2.id,
        None,
        Logger,
        ec,
        materializer,
        wSClient,
      ).returning(
        Future.successful(
          Right(
            CampaignStepVariant(
              id = 1,
              step_id = stepId,
              campaign_id = campaignId_2.id,
              template_id = None,
              step_data = stepData2,
              active = true,
            )
          )
        )
      )

      val result = aiSequenceGenerator.createEmailVariantWithRetry(
        accountId = accountId_61,
        teamId = teamId_23,
        taId = taId_13,
        orgId = orgId_7,
        campaignId = campaignId_2,
        request = request,
      )

      result.map { _ =>

        assert(true)

      }.recover { e =>

        println(LogHelpers.getStackTraceAsString(e))

        assert(false)

      }

    }


    it("should return failure if all attempts fail") {

      // both the attempts fail

      val invalidBody = "test completion {{% spin %}}"

      (gptApiService.generateCompletionsForChat(
        _: List[Map[String, String]],
        _: OrgId,
        _: Option[ColumnDefsProspectsDetails],
      )(
        _: WSClient,
        _: ExecutionContext,
        _: SRLogger,
      )).expects(
          *,
          orgId_7,
          None,
          wSClient,
          ec,
          Logger
        ).returning(
          Future.successful(invalidBody)
        )
        .repeat(AppConfig.maxRetryAIGenerationAttempts)

      val request = campaignStepCreationRequestData

      val stepData: CampaignStepData.AutoEmailStep = CampaignStepData.AutoEmailStep(
        subject = request.subject,
        body = invalidBody
      )

      val variantData: CampaignStepVariantCreateOrUpdate = CampaignStepVariantCreateOrUpdate(
        parent_id = if (request.isVariant) 0 else request.parentId,
        step_data = stepData,
        step_delay = if (request.isFirstStep) 0 else AppConfig.stepDelayForAISequenceGenerator,
        notes = None,
        priority = None
      )

      val stepId = if (request.isVariant) {
        request.parentId
      } else {
        0
      }

      (campaignStepService.createVariant(
        _: Long,
        _: CampaignStepVariantCreateOrUpdate,
        _: Long,
        _: Long,
        _: Long,
        _: Long,
        _: Long,
        _: Option[Long],
      )(
        _: SRLogger,
        _: ExecutionContext,
        _: Materializer,
        _: WSClient,
      )).expects(
          orgId_7.id,
          variantData,
          teamId_23.id,
          accountId_61.id,
          taId_13,
          stepId,
          campaignId_2.id,
          None,
          Logger,
          ec,
          materializer,
          wSClient,
        ).returning(
          Future.successful(
            Left(
              CreateCampaignStepVariantError.ValidateCampaignTemplateError(
                err = new Exception("Error while validating campaign template")
              )
            )
          )
        )
        .repeat(AppConfig.maxRetryAIGenerationAttempts)

      val result = aiSequenceGenerator.createEmailVariantWithRetry(
        accountId = accountId_61,
        teamId = teamId_23,
        taId = taId_13,
        orgId = orgId_7,
        campaignId = campaignId_2,
        request = request,
      )

      result.map { _ =>

        assert(false)

      }.recover { e =>

        println(LogHelpers.getStackTraceAsString(e))

        assert(true)

      }

    }

  }


  describe("Test createCallVariantWithRetry") {

    it("should return call variant") {

      val callBody = "test call script"

      (gptApiService.generateCompletionsForChat(
        _: List[Map[String, String]],
        _: OrgId,
        _: Option[ColumnDefsProspectsDetails],
      )(
        _: WSClient,
        _: ExecutionContext,
        _: SRLogger,
      )).expects(
        *,
        orgId_7,
        None,
        wSClient,
        ec,
        Logger
      ).returning(
        Future.successful(callBody)
      )

      val request = campaignStepCreationRequestData

      val stepData: CampaignStepData.CallTaskData = CampaignStepData.CallTaskData(
        body = callBody
      )

      val variantData: CampaignStepVariantCreateOrUpdate = CampaignStepVariantCreateOrUpdate(
        parent_id = if (request.isVariant) request.parentId else 0,
        step_data = stepData,
        step_delay = if (request.isFirstStep) 0 else AppConfig.stepDelayForAISequenceGenerator,
        notes = None,
        priority = None
      )

      val stepId = if (request.isVariant) {
        request.parentId
      } else {
        0
      }

      (campaignStepService.createVariant(
        _: Long,
        _: CampaignStepVariantCreateOrUpdate,
        _: Long,
        _: Long,
        _: Long,
        _: Long,
        _: Long,
        _: Option[Long],
      )(
        _: SRLogger,
        _: ExecutionContext,
        _: Materializer,
        _: WSClient,
      )).expects(
        orgId_7.id,
        variantData,
        teamId_23.id,
        accountId_61.id,
        taId_13,
        stepId,
        campaignId_2.id,
        None,
        Logger,
        ec,
        materializer,
        wSClient,
      ).returning(
        Future.successful(
          Right(
            CampaignStepVariant(
              id = 1,
              step_id = stepId,
              campaign_id = campaignId_2.id,
              template_id = None,
              step_data = stepData,
              active = true
            )
          )
        )
      )

      val result = aiSequenceGenerator.createCallVariantWithRetry(
        accountId = accountId_61,
        teamId = teamId_23,
        taId = taId_13,
        orgId = orgId_7,
        campaignId = campaignId_2,
        request = request,
      )

      result.map { _ =>

        assert(true)

      }.recover { e =>

        println(LogHelpers.getStackTraceAsString(e))

        assert(false)

      }

    }

    it("should return call variant if first attempt fails but second attempt succeeds") {

      // first attempt fails

      val invalidCallBody = "test call script with error - {{% spin %}}"

      (gptApiService.generateCompletionsForChat(
        _: List[Map[String, String]],
        _: OrgId,
        _: Option[ColumnDefsProspectsDetails],
      )(
        _: WSClient,
        _: ExecutionContext,
        _: SRLogger,
      )).expects(
        *,
        orgId_7,
        None,
        wSClient,
        ec,
        Logger
      ).returning(
        Future.successful(invalidCallBody)
      )

      val request = campaignStepCreationRequestData

      val stepData: CampaignStepData.CallTaskData = CampaignStepData.CallTaskData(
        body = invalidCallBody
      )

      val variantData: CampaignStepVariantCreateOrUpdate = CampaignStepVariantCreateOrUpdate(
        parent_id = if (request.isVariant) request.parentId else 0,
        step_data = stepData,
        step_delay = if (request.isFirstStep) 0 else AppConfig.stepDelayForAISequenceGenerator,
        notes = None,
        priority = None
      )

      val stepId = if (request.isVariant) {
        request.parentId
      } else {
        0
      }

      (campaignStepService.createVariant(
        _: Long,
        _: CampaignStepVariantCreateOrUpdate,
        _: Long,
        _: Long,
        _: Long,
        _: Long,
        _: Long,
        _: Option[Long],
      )(
        _: SRLogger,
        _: ExecutionContext,
        _: Materializer,
        _: WSClient,
      )).expects(
        orgId_7.id,
        variantData,
        teamId_23.id,
        accountId_61.id,
        taId_13,
        stepId,
        campaignId_2.id,
        None,
        Logger,
        ec,
        materializer,
        wSClient,
      ).returning(
        Future.successful(
          Left(
            CreateCampaignStepVariantError.ValidateCampaignTemplateError(
              err = new Exception("Error while validating call script")
            )
          )
        )
      )


      // second attempt succeeds

      val validCallBody = "test call script with error - {{% spin %}}"

      (gptApiService.generateCompletionsForChat(
        _: List[Map[String, String]],
        _: OrgId,
        _: Option[ColumnDefsProspectsDetails],
      )(
        _: WSClient,
        _: ExecutionContext,
        _: SRLogger,
      )).expects(
        *,
        orgId_7,
        None,
        wSClient,
        ec,
        Logger
      ).returning(
        Future.successful(validCallBody)
      )

      val stepData2: CampaignStepData.CallTaskData = CampaignStepData.CallTaskData(
        body = validCallBody
      )

      val variantData2: CampaignStepVariantCreateOrUpdate = CampaignStepVariantCreateOrUpdate(
        parent_id = if (request.isVariant) request.parentId else 0,
        step_data = stepData2,
        step_delay = if (request.isFirstStep) 0 else AppConfig.stepDelayForAISequenceGenerator,
        notes = None,
        priority = None
      )

      (campaignStepService.createVariant(
        _: Long,
        _: CampaignStepVariantCreateOrUpdate,
        _: Long,
        _: Long,
        _: Long,
        _: Long,
        _: Long,
        _: Option[Long],
      )(
        _: SRLogger,
        _: ExecutionContext,
        _: Materializer,
        _: WSClient,
      )).expects(
        orgId_7.id,
        variantData2,
        teamId_23.id,
        accountId_61.id,
        taId_13,
        stepId,
        campaignId_2.id,
        None,
        Logger,
        ec,
        materializer,
        wSClient,
      ).returning(
        Future.successful(
          Right(
            CampaignStepVariant(
              id = 1,
              step_id = stepId,
              campaign_id = campaignId_2.id,
              template_id = None,
              step_data = stepData2,
              active = true
            )
          )
        )
      )

      val result = aiSequenceGenerator.createCallVariantWithRetry(
        accountId = accountId_61,
        teamId = teamId_23,
        taId = taId_13,
        orgId = orgId_7,
        campaignId = campaignId_2,
        request = request,
      )

      result.map { _ =>

        assert(true)

      }.recover { e =>

        println(LogHelpers.getStackTraceAsString(e))

        assert(false)

      }

    }

    it("should return failure if all attempts fail") {

      // both attempts fail

      val invalidCallBody = "test call script with error - {{% spin %}}"

      (gptApiService.generateCompletionsForChat(
        _: List[Map[String, String]],
        _: OrgId,
        _: Option[ColumnDefsProspectsDetails],
      )(
        _: WSClient,
        _: ExecutionContext,
        _: SRLogger,
      )).expects(
          *,
          orgId_7,
          None,
          wSClient,
          ec,
          Logger
        ).returning(
          Future.successful(invalidCallBody)
        )
        .repeat(AppConfig.maxRetryAIGenerationAttempts)

      val request = campaignStepCreationRequestData

      val stepData: CampaignStepData.CallTaskData = CampaignStepData.CallTaskData(
        body = invalidCallBody
      )

      val variantData: CampaignStepVariantCreateOrUpdate = CampaignStepVariantCreateOrUpdate(
        parent_id = if (request.isVariant) request.parentId else 0,
        step_data = stepData,
        step_delay = if (request.isFirstStep) 0 else AppConfig.stepDelayForAISequenceGenerator,
        notes = None,
        priority = None
      )

      val stepId = if (request.isVariant) {
        request.parentId
      } else {
        0
      }

      (campaignStepService.createVariant(
        _: Long,
        _: CampaignStepVariantCreateOrUpdate,
        _: Long,
        _: Long,
        _: Long,
        _: Long,
        _: Long,
        _: Option[Long],
      )(
        _: SRLogger,
        _: ExecutionContext,
        _: Materializer,
        _: WSClient,
      )).expects(
          orgId_7.id,
          variantData,
          teamId_23.id,
          accountId_61.id,
          taId_13,
          stepId,
          campaignId_2.id,
          None,
          Logger,
          ec,
          materializer,
          wSClient,
        ).returning(
          Future.successful(
            Left(
              CreateCampaignStepVariantError.ValidateCampaignTemplateError(
                err = new Exception("Error while validating call script")
              )
            )
          )
        )
        .repeat(AppConfig.maxRetryAIGenerationAttempts)

      val result = aiSequenceGenerator.createCallVariantWithRetry(
        accountId = accountId_61,
        teamId = teamId_23,
        taId = taId_13,
        orgId = orgId_7,
        campaignId = campaignId_2,
        request = request,
      )

      result.map { _ =>

        assert(false)

      }.recover { e =>

        println(LogHelpers.getStackTraceAsString(e))

        assert(true)

      }

    }

  }

}
*/
