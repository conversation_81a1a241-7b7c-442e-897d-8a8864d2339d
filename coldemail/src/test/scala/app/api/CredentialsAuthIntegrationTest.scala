package app.api

import api.APIErrorResponse.ErrorResponse
import api.accounts.AccountUuid
import api.accounts.models.UserFromAccountApiResponseTest
import db_test_spec.api.accounts.fixtures.NewAccountAndEmailSettingData
import db_test_spec.api.{DbTestingBeforeAllAndAfterAll, InitialData}
import org.scalatest.funspec.AsyncFunSpec
import play.api.libs.json.{JsDefined, JsError, JsLookupResult, JsSuccess, JsUndefined, JsValue}
import play.api.test.{FakeRequest, Helpers}
import utils.helpers.LogHelpers
import play.api.test.Helpers.*
import utils.SRLogger

class CredentialsAuthIntegrationTest extends DbTestingBeforeAllAndAfterAll {

  lazy val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get


  
  describe("CredentialAuth Integration Test"){

    describe("CredentialAuthController.getUsersFromAccount"){

      it("should return access users in the account"){

        val apiKey = initialData.teamUserLevelKey
        val usersIds = Set(
          AccountUuid(initialData.account.uuid)
        )

        val sendingUrl: String = s"/api/v3/users"

        val request = FakeRequest(GET, sendingUrl)
          .withHeaders(
            "X-API-KEY" -> apiKey,
            "Content-Type" -> "application/json"
          )

        val final_result = Helpers.route(testApi, request).get

        val status: Int = Helpers.status(final_result)
        val json: JsValue = Helpers.contentAsJson(final_result)

        final_result
          .map( res => {

            if(status == 200){
              val response :  JsLookupResult = (json \ "users" )
              response match {
                case undefined: JsUndefined =>
                  assert(false)
                case JsDefined(result) =>
                  result.validate[List[UserFromAccountApiResponseTest]] match {
                    case JsError(err) =>
                      assert(false)

                    case JsSuccess(users_data_list, _) =>
                      val responseUserIds = users_data_list.map(p => p.id).toSet
                      assert(usersIds.subsetOf(responseUserIds))
                  }
              }
              assert(true)
            } else {
              assert(false)
            }
          })
          .recover { case e => assert(false)
          }
      }
    }

    describe("CredentialAuthController.getUserById"){

      it("should return required output for user passed"){

        val apiKey = initialData.teamUserLevelKey
        val userAccId = initialData.account.uuid
        val sendingUrl: String = s"/api/v3/user/${userAccId}"

        val request = FakeRequest(GET, sendingUrl)
          .withHeaders(
            "X-API-KEY" -> apiKey,
            "Content-Type" -> "application/json"
          )

        val final_result = Helpers.route(testApi, request).get

        val status: Int = Helpers.status(final_result)
        val json: JsValue = Helpers.contentAsJson(final_result)

        final_result
          .map(res => {
            if (status == 200) {
              val response: JsValue = json
              response.validate[UserFromAccountApiResponseTest] match {
                case JsError(err) =>
                  assert(false)

                case JsSuccess(users_data, _) =>
                  val responseUserId = users_data.id
                  assert(responseUserId.toString == userAccId)
              }
            } else {
              assert(false)
            }
          })
          .recover { case e => {
            println(s"Error! :: ${LogHelpers.getStackTraceAsString(e)}")
            assert(false)
          }
          }
      }
      it("should return required output for wrong userId passsed") {
        val apiKey = initialData.teamUserLevelKey

        val errorMessage = s"User Id not found abcd"
        val sendingUrl: String = s"/api/v3/user/abcd"

        val request = FakeRequest(GET, sendingUrl)
          .withHeaders(
            "X-API-KEY" -> apiKey,
            "Content-Type" -> "application/json"
          )

        val final_result = Helpers.route(testApi, request).get

        val status: Int = Helpers.status(final_result)
        val json: JsValue = Helpers.contentAsJson(final_result)

        final_result
          .map(res => {
            if (status == 400) {
              val sentResponseData = (json \ "errors")
              sentResponseData.validate[List[ErrorResponse]] match {
                case JsError(_) =>
                  assert(false)

                case JsSuccess(errors_list, _) =>
                  val error_list_head = errors_list.headOption
                  error_list_head match {
                    case None =>
                      assert(false)
                    case Some(error) =>
                      assert(error.message == errorMessage)
                  }
              }
            } else {
              assert(false)
            }
          })
          .recover { case e => assert(false)
          }
      }
    }
  }
}
