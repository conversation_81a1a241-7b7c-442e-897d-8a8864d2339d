/* 17-jun-2025: [DO_NOT_REMOVE] [LEADFINDERUPLOAD] was slowing down compilation. Only needed for the Lead database upload.


package app.api.lead_finder

import org.apache.pekko.actor.ActorSystem
import api.AppConfig
import api.lead_finder.models.LeadHiddenInfo
import db_test_spec.api.accounts.fixtures.NewAccountAndEmailSettingData
import db_test_spec.api.{DbTestingBeforeAllAndAfterAll, InitialData}
import db_test_spec.api.leadFinder.dao.LeadFinderTestDAO
import play.api.libs.json.{JsError, JsSuccess, JsValue, Json}

import scala.util.{Failure, Success}
import utils.testapp.csv_upload.CsvUploadCol

import scala.concurrent.{ExecutionContext, Future}
import play.api.test.FakeRequest
import play.api.test.Helpers.*
import utils.SRLogger

class LeadFinderIntegrationTest extends DbTestingBeforeAllAndAfterAll {
  
  given logger: SRLogger = new SRLogger("LeadFinderIntegrationTest")

  describe("Lead Finder Integration tests"){

    lazy val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
    
    it ("checking the basic filter call"){

      val data= Seq(
        CsvUploadCol(prospect_data_hash = "some_hash_1", person_first_name = Some("first_name_1"), person_last_name = Some("last_name"),company_type = Some("public"),company_industry = Some("Other"),person_business_email = Some("<EMAIL>")),
        CsvUploadCol(prospect_data_hash = "some_hash_2", person_first_name = Some("first_name_1"), person_last_name = Some("last_name"),company_type = Some("public"),company_industry = Some("Other"),person_business_email = Some("<EMAIL>")),
        CsvUploadCol(prospect_data_hash = "some_hash_3", person_first_name = Some("first_name_1"), person_last_name = Some("last_name"),company_type = Some("public"),company_industry = Some("Other"),person_business_email = Some("<EMAIL>")),
      )

      LeadFinderTestDAO.insertIntoLeadFinderVerifiedBusinessDomain(domains =  Seq("example.com"))

      leadFinderUploadDao.insertCsvParsedCsvData(
        accountId = 2,
        teamId = 2,
        ta_id=2,
        fileName = "some_file",
        dataSource = Some("source"),
        data=data
      ) match {
        case Success(arr)=>
          val sendingUrl = s"/api/v2/leads/filter?tid=${initialData.head_team_id}"

          val companyFilter:List[Int]=List()
          val industryFilter:List[Int]=List()
          val companySize:List[Int]=List()
          val jobTitle:List[Int]=List()
          val city:List[String]=List()

          val payload = Json.obj(
            "company_type" -> companyFilter,
            "industry" ->  industryFilter,
            "company_size"-> companySize,
            "job_title"->jobTitle,
            "city"-> city
          )


          val request = FakeRequest(play.api.test.Helpers.POST, sendingUrl)
            .withHeaders("X-API-KEY" -> initialData.teamUserLevelKey,
              "Content-Type" -> "application/json")
            .withJsonBody(payload)

          val final_result = play.api.test.Helpers.route(testApi, request).get

          val status: Int = play.api.test.Helpers.status(final_result)
          val json: JsValue = play.api.test.Helpers.contentAsJson(final_result)

          final_result.flatMap(res => {
            println(s"status is ${status} with response ${res.body}")

              if (status==200){
                val results = (json \ "data" \"leads")
                results.validate[List[LeadHiddenInfo]] match {

                  case JsError(errors) =>
                    assert(false)
                    Future.failed(new Exception(s"[Lead Finder Integration tests] validation failed ${errors}"))

                  case JsSuccess(leads, path) =>
                    Future.successful("Able to fetch the leads data")
                    assert(leads.length==3)
                }
              }
              else{
                assert(false)
                Future.failed(new Exception("[Lead Finder Integration tests] Error while getting lead filters"))
              }
            })

        case Failure(err)=>
          assert(false)
      }
    }
  }


}*/