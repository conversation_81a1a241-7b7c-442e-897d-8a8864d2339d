package app.api.accounts

import api.ErrorType
import api.accounts.models.{AccountId, OrgId}
import api.prospects.ProspectAccountCreateFormData
import app.test_fixtures.prospect.ProspectAccountFixture
import db_test_spec.api.{DbTestingBeforeAllAndAfterAll, InitialData}
import db_test_spec.api.accounts.fixtures.NewAccountAndEmailSettingData
import org.scalatest.funspec.AsyncFunSpec
import play.api.libs.json.{JsError, JsSuccess, JsVal<PERSON>, <PERSON><PERSON>, Reads, Writes}
import play.api.test.FakeRequest
import utils.helpers.LogHelpers
import utils.SRLogger
import play.api.test.Helpers.*

import scala.concurrent.Future

case class ErrorResponseV3ForTest(
                                   error_type: String,
                                   message: String,
                                 )

object ErrorResponseV3ForTest {
  implicit val writes: Writes[ErrorResponseV3ForTest] = Json.writes[ErrorResponseV3ForTest]
  implicit val reads: Reads[ErrorResponseV3ForTest] = Json.reads[ErrorResponseV3ForTest]
}


class AccountServiceIntegrationTests extends DbTestingBeforeAllAndAfterAll {


  describe("Accounts api's Integration Test") {
    describe("ProspectAccounts Integration Test") {
      describe("testing public api to create Account") {
        it("should give result in expected format with uuid for Create Account") {
          val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
          val teamId = initialData.emailSetting.get.team_id
          val orgId = initialData.emailSetting.get.org_id
          val accountId = initialData.account.id
          val sendingUrl = s"/api/v3/accounts?tid=80"
          val startTime = System.currentTimeMillis()
          val prospectAccountCreateFormData = ProspectAccountFixture.prospectAccountCreateFormData
          val teamUuid = ProspectAccountFixture.getTeamUuidFromId(
            teamId = teamId,
            orgId = orgId
          ).get

          val intermed1_time = System.currentTimeMillis() - startTime
          println(s"Test: should give result in expected format with uuid for Create Account, fixture setup time ${ intermed1_time }")

          val request = FakeRequest(play.api.test.Helpers.POST, sendingUrl)
            .withHeaders("X-API-KEY" -> initialData.teamUserLevelKey)
            .withJsonBody(Json.toJson(prospectAccountCreateFormData))

          val final_result = play.api.test.Helpers.route(testApi, request).get

          val status: Int = play.api.test.Helpers.status(final_result)
          val json: JsValue = play.api.test.Helpers.contentAsJson(final_result)

          final_result.map { res =>
            if (status == 200) {
              // Validate the full response JSON
              val responseData = json
              println(s"responseData $responseData")
              val objectField = (responseData \ "object").asOpt[String]
              val id = (responseData \ "id").asOpt[String]
              val name = (responseData \ "name").asOpt[String]
              val createdAt = (responseData \ "created_at").asOpt[Long]
              val teamId = (responseData \ "team_id").asOpt[String]
              val ownerAccountId = (responseData \ "owner_account_id").asOpt[String]
              val description = (responseData \ "description").asOpt[String]
              val source = (responseData \ "source").asOpt[String]
              val website = (responseData \ "website").asOpt[String]
              val industry = (responseData \ "industry").asOpt[String]
              val linkedinUrl = (responseData \ "linkedin_url").asOpt[String]
              val customId = (responseData \ "custom_id").asOpt[String]
              val customFieldsResponse = (responseData \ "custom_fields").asOpt[JsValue]

              val elapsed_time = System.currentTimeMillis() - startTime
              println(s"Test: should give result in expected format with uuid for Create Account, fixture setup time elapsed_time: ${elapsed_time}")

              // Assertions for required fields
              assert(objectField.contains("prospect_account"))
              assert(id.isDefined)

              assert(name.contains(prospectAccountCreateFormData.name)) // Match the input name

              assert(createdAt.isDefined)

              assert(teamId.isDefined) // Assuming team ID 80 was passed in the query
              assert(teamId.contains(teamUuid.uuid))

              assert(ownerAccountId.isDefined)
              assert(ownerAccountId.contains(accountId.toString))

              // Assertions for optional fields
              assert(description.isDefined) // Example: if no description was passed, expect none
              assert(description == prospectAccountCreateFormData.description)

              assert(source.isDefined) // Example: if no source was passed, expect none
              assert(source == prospectAccountCreateFormData.source)

              assert(website.isDefined)
              assert(website == prospectAccountCreateFormData.website)

              assert(industry.isDefined)
              assert(industry == prospectAccountCreateFormData.industry)

              assert(linkedinUrl.isDefined)
              assert(linkedinUrl == prospectAccountCreateFormData.linkedin_url)

              assert(customId.isDefined)
              assert(customId == prospectAccountCreateFormData.custom_id)

              assert(customFieldsResponse.isDefined)
              assert(customFieldsResponse == prospectAccountCreateFormData.custom_fields)


            } else {
              println(s"Unexpected response status: $status")
              println(s"Response body: ${play.api.test.Helpers.contentAsString(final_result)}")
              assert(false)
            }
          }.recover { e =>
            println(s"Test failed with exception: ${e.getMessage}")
            assert(false)
          }
        }

      }
      describe("testing public api to create Account") {
        val accountName1 = "accountName1"
        val customFields = Json.obj(
          "nn" -> null
        )
        it("should return error for account with same name exist") {
          val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

          val final_result = for {

            prospect_account <- Future {
              prospectAccountDAO.createProspectAccountsFromUploadMultiRowInsert(
                ownerAccountId = AccountId(initialData.account.internal_id),
                teamId = initialData.emailSetting.get.team_id,
                accounts = Seq(ProspectAccountCreateFormData(
                  name = accountName1,
                  custom_id = None,
                  description = None,
                  source = None,
                  website = None,
                  industry = None,
                  linkedin_url = None,
                  created_at = None,
                  custom_fields = None,
                  update_account = None,
                )),
                forceUpdateAccounts = false,
                ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads = false,
                permittedAccountIdsForEditingProspectAccounts = Seq(AccountId(initialData.account.internal_id)),
                Logger = Logger
              ).get
            }

            res <- {
              val sendingUrl = s"/api/v3/accounts?tid=80"

              val request = FakeRequest(play.api.test.Helpers.POST, sendingUrl)
                .withHeaders("X-API-KEY" -> initialData.teamUserLevelKey)
                .withJsonBody(Json.obj(
                  "name" -> accountName1,
                  "custom_fields" -> customFields
                ))

              val final_result = play.api.test.Helpers.route(testApi, request).get


              final_result
            }
          } yield {
              println(s"prospect_account:: ${prospect_account} :: res:: ${res}")
            res
          }
          val status: Int = play.api.test.Helpers.status(final_result)
          val json: JsValue = play.api.test.Helpers.contentAsJson(final_result)
          final_result.map(res => {
            println(s"res:: ${res.body} :: ${status}")
            if (status == 400) {

              val responseData = (json \ "errors")
              responseData.validate[List[ErrorResponseV3ForTest]] match {
                case JsError(_) => {
                  assert(false)
                }

                case JsSuccess(response, _) =>
                  val headOpt = response.headOption
                  headOpt match {
                    case None =>
                      assert(false)
                    case Some(errorV3) =>
                      assert(errorV3.error_type == ErrorType.BAD_REQUEST.toString)
                  }
              }

            } else {
              assert(false)
            }
          }).recover(e => {
            println(s"Error:: ${LogHelpers.getStackTraceAsString(e)}")
            assert(false)
          })
        }
      }
      describe("Prospect Listing API Integration Test") {
        it("Should return all prospect account for team with pagination") {
          val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
          val teamId = initialData.emailSetting.get.team_id
          val orgId = initialData.emailSetting.get.org_id
          val accountUuid = initialData.account.id
          val accountId = AccountId(initialData.account.internal_id)

          // creating prospect account
          val prospectAccountCreateFormData = ProspectAccountFixture.prospectAccountCreateFormData

          val teamUuid = ProspectAccountFixture.getTeamUuidFromId(
            teamId = teamId,
            orgId = orgId
          ).get

          ProspectAccountFixture.createOrUpdateProspectAccount(
            data = prospectAccountCreateFormData,
            userId = accountId,
            teamId = teamId,
            Logger = Logger,
            permittedAccountIds = Seq()
          )

          val sendingUrl = s"/api/v3/accounts?tid=80"

          val request = FakeRequest(play.api.test.Helpers.GET, sendingUrl)
            .withHeaders("X-API-KEY" -> initialData.teamUserLevelKey)

          val final_result = play.api.test.Helpers.route(testApi, request).get

          val status: Int = play.api.test.Helpers.status(final_result)
          val json: JsValue = play.api.test.Helpers.contentAsJson(final_result)

          final_result.map { res =>
            if (status == 200) {
              // Extract the list of prospect accounts
              val prospectAccountList = (json \ "accounts").validate[List[JsValue]]
              prospectAccountList match {
                case JsError(errors) =>
                  println(s"Validation errors: $errors")
                  assert(false)

                case JsSuccess(accounts, _) =>
                  // Iterate through each account and validate fields
                  accounts.headOption.map { account =>

                    println(s"account: $account")
                    val obj = (account \ "object").asOpt[String]
                    val id = (account \ "id").asOpt[String]
                    val name = (account \ "name").asOpt[String]
                    val createdAt = (account \ "created_at").asOpt[Long]
                    val teamId = (account \ "team_id").asOpt[String]
                    val ownerAccountId = (account \ "owner_account_id").asOpt[String]
                    val description = (account \ "description").asOpt[String]
                    val source = (account \ "source").asOpt[String]
                    val website = (account \ "website").asOpt[String]
                    val industry = (account \ "industry").asOpt[String]
                    val linkedinUrl = (account \ "linkedin_url").asOpt[String]
                    val customId = (account \ "custom_id").asOpt[String]
                    val customFields = (account \ "custom_fields").asOpt[JsValue]

                    // Assertions for presence
                    assert(obj.contains("prospect_account"))
                    assert(id.isDefined)

                    assert(name.contains(prospectAccountCreateFormData.name)) // Match the input name

                    assert(createdAt.isDefined)

                    assert(teamId.isDefined) // Assuming team ID 80 was passed in the query
                    assert(teamId.contains(teamUuid.uuid))

                    assert(ownerAccountId.isDefined)
                    assert(ownerAccountId.contains(accountUuid.toString))

                    // Assertions for optional fields
                    assert(description.isDefined) // Example: if no description was passed, expect none
                    assert(description == prospectAccountCreateFormData.description)

                    assert(source.isDefined) // Example: if no source was passed, expect none
                    assert(source == prospectAccountCreateFormData.source)

                    assert(website.isDefined)
                    assert(website == prospectAccountCreateFormData.website)

                    assert(industry.isDefined)
                    assert(industry == prospectAccountCreateFormData.industry)

                    assert(linkedinUrl.isDefined)
                    assert(linkedinUrl == prospectAccountCreateFormData.linkedin_url)

                    assert(customId.isDefined)
                    assert(customId == prospectAccountCreateFormData.custom_id)

                    assert(customFields.isDefined)
                    assert(customFields == prospectAccountCreateFormData.custom_fields)

                  }
                  assert(true)
              }
            } else {
              println(s"Unexpected response status: $status")
              println(s"Response body: ${play.api.test.Helpers.contentAsString(final_result)}")
              assert(false)
            }
          }.recover { e =>
            println(s"Test failed with exception: ${e.getMessage}")
            assert(false)
          }
        }


      }
      describe("ProspectAccount by id Integration test") {
        it("Should return the prospect Account in required format") {
          val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

          val prospectAccountId: String = initialData.prospectsResult.head.internal.prospect_account_uuid.get

          val sendingUrl = s"/api/v3/accounts/${prospectAccountId}?tid=80"

          val request = FakeRequest(play.api.test.Helpers.GET, sendingUrl)
            .withHeaders("X-API-KEY" -> initialData.teamUserLevelKey)


          val final_result = play.api.test.Helpers.route(testApi, request).get

          val status: Int = play.api.test.Helpers.status(final_result)
          val json: JsValue = play.api.test.Helpers.contentAsJson(final_result)

          final_result.map(res => {

            if (status == 200) {
              val resJson = json
              val id = (resJson \ "id")
              val name = (resJson \ "name")
              val custom_id = (resJson \ "custom_id")
              val description = (resJson \ "description")
              val source = (resJson \ "source")
              val website = (resJson \ "website")
              val industry = (resJson \ "industry")
              val linkedin_url = (resJson \ "linkedin_url")
              val created_at = (resJson \ "created_at")
              val updated_at = (resJson \ "updated_at")
              val custom_fields = (resJson \ "custom_fields")

              assert(id.isDefined)
              assert(name.isDefined)
              assert(custom_id.isDefined)
              assert(description.isDefined)
              assert(source.isDefined)
              assert(website.isDefined)
              assert(industry.isDefined)
              assert(created_at.isDefined)
              assert(updated_at.isDefined)
              assert(linkedin_url.isDefined)
              assert(custom_fields.isDefined)

            } else {
              assert(false)
            }
          }).recover(e => {
            assert(false)
          })
        }

      }
    }
    describe("Enable Agency plan api tests") {
      it("should change the non agency to agency") {
        val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
        val teamId = initialData.head_team_id

        val sendingUrl = s"/api/v2/auth/onboarding/enable_agency_view?tid=${teamId}"

        val request = FakeRequest(play.api.test.Helpers.POST, sendingUrl)
          .withHeaders("X-API-KEY" -> initialData.teamUserLevelKey)
          .withJsonBody(Json.obj(
            "enable" -> true,
          ))

        val final_result = play.api.test.Helpers.route(testApi, request).get
        val status: Int = play.api.test.Helpers.status(final_result)
        val json: JsValue = play.api.test.Helpers.contentAsJson(final_result)
        final_result
          .map(res => {
              println(s"status : ${status} :: response body : ${json} ")
            if (status == 200) {
              val isAgency = (json \ "data" \ "account" \ "org" \ "is_agency").as[Boolean]

              assert(isAgency)
            } else {
              assert(false)
            }
          }
          ).recover(e => {
            println(s"Test failed with exception: ${LogHelpers.getStackTraceAsString(e)}")
            assert(false)
          })
      }

      it("should change the agency to non agency") {
        val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
        val teamId = initialData.head_team_id
        val orgId = OrgId(initialData.account.org.id)

        accountService.enableAgencyViewDuringOnboarding(
          orgId = orgId,
          enableAgency = true,
          account = initialData.account
        ) match {

          case Left(error) => assert(false)

          case Right(value) =>

            val sendingUrl = s"/api/v2/auth/onboarding/enable_agency_view?tid=${teamId}"

            val request = FakeRequest(play.api.test.Helpers.POST, sendingUrl)
              .withHeaders("X-API-KEY" -> initialData.teamUserLevelKey)
              .withJsonBody(Json.obj(
                "enable" -> false,
              ))

            val final_result = play.api.test.Helpers.route(testApi, request).get
            val status: Int = play.api.test.Helpers.status(final_result)
            val json: JsValue = play.api.test.Helpers.contentAsJson(final_result)


            final_result
              .map(res => {
                if (status == 200) {

                  val isAgency = (json \ "data" \ "account" \ "org" \ "is_agency").as[Boolean]

                  assert(!isAgency)
                } else {
                  assert(false)
                }
              }
              ).recover(e => {
                assert(false)
              })
        }
      }

    }
  }
}
