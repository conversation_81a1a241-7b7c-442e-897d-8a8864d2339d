package app.api.accounts

import api.accounts.{CountOfMigration, IdsToUpdate, MigrationRecursiveService}
import org.scalamock.scalatest.MockFactory
import org.scalatest.funspec.AnyFunSpec
import utils.SRLogger
import utils.uuid.TableNames

import scala.util.{Failure, Success, Try}

class MigrationRecursiveServiceSpec extends AnyFunSpec with MockFactory {

  class TestClass {
    def getIdsToMigrateMethod(ids_to_update_from: IdsToUpdate, count_per_cycle: CountOfMigration, tableNames: TableNames): Try[List[IdsToUpdate]] = {
    Success(List())
    }
    def updateTheIds (ids_to_update: List[IdsToUpdate], tableNames: TableNames): Try[CountOfMigration] = {
      Success(CountOfMigration(1))
    }
  }


  val testClass = mock[TestClass]
  given Logger: SRLogger = new SRLogger("MigrationRecursiveServiceSpec")
  val ERROR = new Throwable("ERROR")


  describe("MigrationRecursiveService.recursiveMigrationMethod") {

    (testClass.getIdsToMigrateMethod)
      .expects(IdsToUpdate(0), CountOfMigration(5), TableNames.EmailsScheduled)
      .returning(Failure(ERROR))
    it("fail when getIdsToMigrateMethod fails") {
      val result = MigrationRecursiveService.recursiveMigrationMethod(
        getIdsToMigrateMethod = testClass.getIdsToMigrateMethod,
        updateTheIds = testClass.updateTheIds,
        total_updated = CountOfMigration(0),
        ids_to_update_from = IdsToUpdate(0),
        count_per_cycle = CountOfMigration(5),
        table_name = TableNames.EmailsScheduled
      )

      assert(result.isLeft)
    }
  }

}
