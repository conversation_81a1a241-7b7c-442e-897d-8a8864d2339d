package app.api.accounts.service

import org.apache.pekko.actor.ActorSystem
import org.apache.pekko.stream.Materializer
import api.CONSTANTS
import api.accounts.email.models.EmailServiceProvider
import api.accounts.{Account, AccountAccess, AccountMetadata, AccountType, AccountUuid, GetOAuthUrlError, GoogleOAuthSettings, OAuthFetchTokensError, OAuthFetchTokensResponse, OAuthState, OrgCountData, OrgMetadata, OrgPlan, OrgSettings, OrganizationRole, OrganizationWithCurrentData, ProspectCategoriesInDB, RepGoogleApiKey, ReplyHandling, RolePermissionDataDAOV2, RolePermissionDataV2, RolePermissionsInDBV2, TeamAccount, TeamAccountRole, TeamId, TeamMember, TeamMemberLite}
import api.accounts.models.{AccountId, AccountProfileInfo, OrgId}
import api.emails.{EmailSetting, EmailSettingCreateViaOAuth}
import api.emails.models.{EmailSettingIntegrationLogsStage, EmailSettingUuid}
import api.emails.services.CreateEmailViaOauthError
import api.integrations.CommonCRMAPIErrors.ConvertedLead
import api.integrations.{GetCRMUsersError, IntegrationTPAccessTokenResponse, IntegrationTPUser, IntegrationTPUsersList}
import api.prospects.models.ProspectCategoryRank
import api.team.TeamUuid
import api.triggers.{CheckLimitAndAddOrUpdateCRMError, IntegrationType}
import app.api.TestMockTrait.TestMockTrait
import app.test_fixtures.accounts.OrgCountDataFixture
import app.test_fixtures.organizationa.{OrgMetadataFixture, OrgPlanFixture}
import io.smartreach.esp.api.emails.EmailSettingId
import io.smartreach.esp.api.microsoftOAuth.{MSAccessTokenResponse, MSUserProfileResponse, MicrosoftOAuthSettings}
import org.joda.time.DateTime
import org.scalamock.scalatest.AsyncMockFactory
import org.scalatest.funspec.AsyncFunSpec
import play.api.libs.json.Json
import play.api.libs.ws.WSClient
import play.api.libs.ws.ahc.AhcWSClient
import utils.{SRAppConfig, SRLogger}
import utils.cache_utils.model.CampaignUseStatusForEmailSetting
import utils.testapp.TestAppExecutionContext

import java.util.concurrent.Executors
import scala.concurrent.{ExecutionContext, ExecutionContextExecutor, Future}
import scala.util.{Failure, Success}

class SocialAuthServiceSpec extends AsyncFunSpec with AsyncMockFactory with TestMockTrait {
  val dateTime: DateTime = DateTime.now()

  val emailSetting: EmailSetting = EmailSetting(
    id = Some(EmailSettingId(emailSettingId = 1)),
    org_id = OrgId(id = 1),
    owner_id = AccountId(id = 1),
    team_id = TeamId(id = 1),
    uuid = Some(EmailSettingUuid("test_uuid")),
    owner_uuid = AccountUuid("owner_uuid"),
    team_uuid = TeamUuid("team_uuid"),
    message_id_suffix = "Hello",
    email = "<EMAIL>",
    email_address_host = "<EMAIL>",
    service_provider = EmailServiceProvider.GMAIL_API,
    domain_provider = None,  
    via_gmail_smtp = None,
    owner_name = "Shubham",
    sender_name = "Shubham Kudekar",
    first_name = "Shubham",
    last_name = "Kudekar",
    cc_emails = None,
    bcc_emails = None,
    smtp_username = None,
    smtp_password = None,
    smtp_host = None,
    smtp_port = None,
    imap_username = None,
    imap_password = None,
    imap_host = None,
    imap_port = None,
    oauth2_access_token = None,
    oauth2_refresh_token = None,
    oauth2_token_type = None,
    oauth2_token_expires_in = None,
    oauth2_access_token_expires_at = None,
    email_domain = None,
    api_key = None,
    mailgun_region = None,
    quota_per_day = 400,
    reply_handling = ReplyHandling.PAUSE_SPECIFIC_CAMPAIGN_ON_REPLY,
    last_read_for_replies = None,
    latest_email_scheduled_at = None,
    error = None,
    error_reported_at = None,
    paused_till = None,
    signature = "Shubham Kudekar",
    created_at = None,
    current_prospect_sent_count_email = 10,
    default_tracking_domain = "default_tracking_domain",
    default_unsubscribe_domain = "default_unsubscribe_domain",
    rep_tracking_host_id = 1,
    tracking_domain_host = None,
    custom_tracking_domain = None,
    custom_tracking_cname_value = None,
    custom_tracking_domain_is_verified = None,
    custom_tracking_domain_is_ssl_enabled = None,
    rep_mail_server_id = 1,
    rep_mail_server_public_ip = "rep_mail_server_public_ip",
    rep_mail_server_host = "rep_mail_server_host",
    rep_mail_server_reverse_dns = None,
    min_delay_seconds = 0,
    max_delay_seconds = 0,
      tag = None,
    campaign_use_status_for_email_setting = CampaignUseStatusForEmailSetting.IsNotAssignedToAnyCampaign,
    show_rms_ip_in_frontend = false
  )

  val teamMember: TeamMember = TeamMember(
    team_id = 2,
    team_name = "Test Company",
    user_id = 2,
    ta_id = 2,
    first_name = Some("Prateek"),
    last_name = Some("B"),
    email = "<EMAIL>",
    team_role = TeamAccountRole.OWNER,
    api_key = None,
    zapier_key = Some("zapier_key")
  )

  val orgMetadata: OrgMetadata = OrgMetadataFixture.orgMetadataFixture2.copy(
    allowed_for_new_google_api_key = Some(true)
  )

  val orgCountData: OrgCountData = OrgCountDataFixture.orgCountData_default

  val orgPlan: OrgPlan = OrgPlanFixture.orgPlanFixture

  val orgSettings: OrgSettings = OrgSettings(
    enable_ab_testing = false,
    disable_force_send = false,
    bulk_sender = false,
    allow_2fa = false,
    show_2fa_setting = false,
    enforce_2fa = false,
    allow_native_crm_integration = false,
      agency_option_allow_changing = false,
      agency_option_show = false
  )

  val org: OrganizationWithCurrentData = OrganizationWithCurrentData(

    id = 1,
    name = "AK",
    owner_account_id = 2,

    counts = orgCountData,
    settings = orgSettings,
    plan = orgPlan,

    is_agency = true,
    trial_ends_at = dateTime.plusDays(100),
    error = None,
    error_code = None,
    paused_till = None,
    errors = Seq(),
    warnings = Seq(),
    via_referral = false,
    org_metadata = orgMetadata
  )

  val fullTokenData: IntegrationTPAccessTokenResponse.FullTokenData = IntegrationTPAccessTokenResponse.FullTokenData(
    access_token = "access_token",
    refresh_token = Some("refresh_token"),
    expires_in = Some(1),
    expires_at = Some(dateTime),
    token_type = Some("token_type"),
    api_domain = Some("api_domain"),
    is_sandbox = Some(false)
  )

  val integrationTPUser: IntegrationTPUser = IntegrationTPUser(
    id = "id",
    email = "test@gmail",
    company_id = Some("company_id"),
    owner_id = Some("owner_id")
  )

  val msAccessTokenResponse: MSAccessTokenResponse = MSAccessTokenResponse(
    access_token = "access_token",
    refresh_token = Some("refresh_token"),
    expires_in = Some(1),
    token_type = Option("token_type")
  )

  val msUserProfileResponse: MSUserProfileResponse = MSUserProfileResponse(
    displayName = "displayName",
    givenName = Some("givenName"),
    surname = Some("surname"),
    userPrincipalName = "userPrincipalName",
    mail = Some("mail")
  )

  val organizationWithCurrentData: OrganizationWithCurrentData = OrganizationWithCurrentData(
    id = 1,
    name = "smartreach",
    owner_account_id = 1,
    counts = orgCountData,
    settings = orgSettings,
    plan = orgPlan,

    is_agency = false,
    trial_ends_at = dateTime.plusDays(9),
    error = None,
    error_code = None,
    paused_till = None,

    errors = Seq(),
    warnings = Seq(),

    via_referral = false,

    org_metadata = orgMetadata

  )

  val accountProfileInfo: AccountProfileInfo = AccountProfileInfo(
    first_name = "Aditya",
    last_name = "Sadana",
    company = None,
    timezone = Some("timezone"),
    country_code = None,
    mobile_country_code = None,
    mobile_number = None,
    onboarding_phone_number = None,
    twofa_enabled = true,
    has_gauthenticator = true,
    weekly_report_emails = None,
    scheduled_for_deletion_at = None
  )
  val adminDefaultPermissions: RolePermissionsInDBV2 = RolePermissionDataDAOV2.defaultRoles(
    role = TeamAccountRole.ADMIN,
    simpler_perm_flag = false
  )

  val rolePermissionData: RolePermissionDataV2 = RolePermissionDataV2.toRolePermissionApi(
    data = adminDefaultPermissions.copy(id = 2)
  )

  val teamMemberLite: TeamMemberLite = TeamMemberLite(
    user_id = 1,
    first_name = None,
    last_name = None,
    email = "<EMAIL>",
    active = true,
    timezone = Some("timezone"),
    twofa_enabled = true,
    created_at = dateTime,
    user_uuid = AccountUuid(uuid = "uuid"),
    team_role = TeamAccountRole.ADMIN
  )

  val prospectCategoriesInDB: ProspectCategoriesInDB = ProspectCategoriesInDB(
    id = 1,
    name = "Do not contact",
    text_id = "do_not_contact",
    label_color = "#d52728",
    is_custom = false,
    team_id = 1,
    rank = ProspectCategoryRank(rank = 2000)
  )

  val prospectCategoriesInDB2: ProspectCategoriesInDB = ProspectCategoriesInDB(
    id = 1,
    name = "Not Categorized",
    text_id = "not_categorized",
    label_color = "#d52728",
    is_custom = false,
    team_id = 1,
    rank = ProspectCategoryRank(rank = 2000)
  )

  val teamAccount: TeamAccount = TeamAccount(

    team_id = 1,
    org_id = 1,

    role_from_db = None, // MUST come from db (option type only for cacheservice error), should not be sent to frontend, only intermediate
    role = Some(rolePermissionData), // should be sent to frontend
    active = true,
    is_actively_used = true,
    team_name = "smartreach",
    total_members = 1,
    access_members = Seq(teamMember),
    all_members = Seq(teamMemberLite),

    prospect_categories_custom = Seq(prospectCategoriesInDB, prospectCategoriesInDB2),
    max_emails_per_prospect_per_day = 100L,
    max_emails_per_prospect_per_week = 1000L,
    max_emails_per_prospect_account_per_day = 97,
    max_emails_per_prospect_account_per_week = 497,

    // ADMIN SETTINGS FOR MULTICAMPAIGN
    // allow_assigning_prospects_to_multiple_campaigns: Boolean, FORCEASSIGNISSUE
    reply_handling = ReplyHandling.PAUSE_SPECIFIC_CAMPAIGN_ON_REPLY,
    created_at = dateTime,
    selected_calendar_data = None,
    team_uuid = TeamUuid("team_bvfbvfroebbkbkvcs")
  )

  val account: Account = Account(
    id = AccountUuid("account_uuid"),
    internal_id = 1,
    email = "<EMAIL>",
    email_verification_code = None,
    email_verification_code_created_at = None,
    created_at = dateTime.minusDays(7),

    first_name = None,
    last_name = None,
    company = None,
    timezone = None,

    profile = accountProfileInfo,

    org_role = Some(OrganizationRole.OWNER),

    teams = Seq(teamAccount),
    account_type = AccountType.TEAM,
    org = organizationWithCurrentData,
    active = true,
    email_notification_summary = "",
    account_metadata = AccountMetadata(is_profile_onboarding_done = Some(true)),
    email_verified = true,
    signupType = None,
    account_access = AccountAccess(inbox_access = true),
    calendar_account_data = None
  )

  val dataEmailSettingCreateViaOAuth: EmailSettingCreateViaOAuth = EmailSettingCreateViaOAuth(
    email = "mail",

    service_provider = EmailServiceProvider.OUTLOOK_API,

    sender_name = "displayName",
    first_name = "givenName",
    last_name = "surname",

    oauth2_access_token = "access_token",
    oauth2_refresh_token = "refresh_token",
    oauth2_token_type = "token_type",
    oauth2_token_expires_in = 1,
    oauth2_access_token_expires_at = dateTime,

    rep_google_api_key_id = None,
    rep_mail_server_id = None,
    rep_tracking_host_id = None,
    gsuite_domain_install = None,
    domain_provider = None,
    domain = "domain",
    email_purchase_status = None,

    quota_per_day = 100,

    alias_parent_id = None,

  )

  describe("Test SocialAuthService.getOAuthUrl") {
    given Logger: SRLogger = new SRLogger("SocialAuthService")
    implicit lazy val actorSystem: ActorSystem = TestAppExecutionContext.actorSystem
    implicit lazy val materializer: Materializer = TestAppExecutionContext.actorMaterializer
    implicit lazy val wsClient: AhcWSClient = TestAppExecutionContext.wsClient
    it("test for getOAuthUrl when email_type is defined but the value is not send or receive"){
      (emailAccountTestServiceMock.insertEmailSettingIntegrationLogs(_: AccountId, _: OrgId, _: TeamId, _: Option[String], _: Option[EmailServiceProvider], _: Boolean, _: EmailSettingIntegrationLogsStage, _: Option[String])(using _: SRLogger))
        .expects(AccountId(1), OrgId(1), TeamId(2), Some("<EMAIL>"), Some(EmailServiceProvider.GMAIL_API), false, EmailSettingIntegrationLogsStage.OAuthUrlGenerationError, Some("email_type must be either 'send' or 'receive'"), *)
        .returning(Success(Some(1)))

      val result = socialAuthService.getOAuthUrl (
        email_type = Some("test"),
        email_setting_id = Some(1),
        emailSettingOpt = Some(emailSetting),
        service_provider = "google",
        emailAddress = Some("<EMAIL>"),
        campaign_id = Some(1),
        teamMember = teamMember,
        email_address = Some("<EMAIL>"),
        org = org,
        teamId = TeamId(1),
        accountId = AccountId(1),
        allowCRMIntegration = false,
        isZapmailFlow = false
      )
      assert(result.isLeft)
      assert(result == Left(GetOAuthUrlError.EmailEitherSendOrReceiveError))
    }
    it("test for getOAuthUrl when email_setting_id is defined and emailSetting is not defined") {
      (emailAccountTestServiceMock.insertEmailSettingIntegrationLogs(_: AccountId, _: OrgId, _: TeamId, _: Option[String], _: Option[EmailServiceProvider], _: Boolean, _: EmailSettingIntegrationLogsStage, _: Option[String])(using _: SRLogger))
        .expects(AccountId(1), OrgId(1), TeamId(2), Some("<EMAIL>"), Some(EmailServiceProvider.GMAIL_API), false, EmailSettingIntegrationLogsStage.OAuthUrlGenerationError, Some("email setting not found, please contact support"), *)
        .returning(Success(Some(1)))
      val result = socialAuthService.getOAuthUrl(
        email_type = Some("send"),
        email_setting_id = Some(1),
        emailSettingOpt = None,
        service_provider = "google",
        emailAddress = Some("<EMAIL>"),
        campaign_id = Some(1),
        teamMember = teamMember,
        email_address = Some("<EMAIL>"),
        org = org,
        teamId = TeamId(1),
        accountId = AccountId(1),
        allowCRMIntegration = false,
        isZapmailFlow = false
      )
      assert(result.isLeft)
      assert(result == Left(GetOAuthUrlError.EmailSettingNotFound))
    }
    it("test for getOAuthUrl when service_provider is google emailAddress.isDefined and emailAddress is a free domain email") {
      (emailAccountTestServiceMock.insertEmailSettingIntegrationLogs(_: AccountId, _: OrgId, _: TeamId, _: Option[String], _: Option[EmailServiceProvider], _: Boolean, _: EmailSettingIntegrationLogsStage, _: Option[String])(using _: SRLogger))
        .expects(AccountId(1), OrgId(1), TeamId(2), Some("<EMAIL>"), Some(EmailServiceProvider.GMAIL_API), false, EmailSettingIntegrationLogsStage.OAuthUrlGenerationError, Some("Please provide a work email . Personal email addresses are not allowed"), *)
        .returning(Success(Some(1)))
      (freeEmailDomainListServiceMock.checkIfFreeEmailService(_: String)(_: SRLogger))
        .expects("<EMAIL>", *)
        .returning(Success(true))
      val result = socialAuthService.getOAuthUrl(
        email_type = Some("send"),
        email_setting_id = Some(1),
        emailSettingOpt = Some(emailSetting),
        service_provider = "google",
        emailAddress = Some("<EMAIL>"),
        campaign_id = Some(1),
        teamMember = teamMember,
        email_address = Some("<EMAIL>"),
        org = org,
        teamId = TeamId(1),
        accountId = AccountId(1),
        allowCRMIntegration = false,
        isZapmailFlow = false
      )
      assert(result.isLeft)
      assert(result == Left(GetOAuthUrlError.FreeDomainUseError))
    }
    it("test for getOAuthUrl when service_provider is google emailAddress.isDefined and emailAddress is confirm_install.isEmpty and GSuiteDomainNotInstalled") {
      (freeEmailDomainListServiceMock.checkIfFreeEmailService(_: String)(_: SRLogger))
        .expects("<EMAIL>", *)
        .returning(Success(false))
      (emailSettingDAOMock.isGsuiteDomainInstalled(_: String, _:Long)(using _: SRLogger))
        .expects("<EMAIL>",1,*)
        .returning(false)
      (emailAccountTestServiceMock.insertEmailSettingIntegrationLogs(_: AccountId, _: OrgId, _: TeamId, _: Option[String], _: Option[EmailServiceProvider], _: Boolean, _: EmailSettingIntegrationLogsStage, _: Option[String])(using _: SRLogger))
        .expects(AccountId(1), OrgId(1), TeamId(2), Some("<EMAIL>"), Some(EmailServiceProvider.GMAIL_API), false, EmailSettingIntegrationLogsStage.OAuthUrlGenerationError, Some("Your G Suite domain is not installed. Please follow the instructions to install"), *)
        .returning(Success(Some(1)))
      val result = socialAuthService.getOAuthUrl(
        email_type = Some("send"),
        email_setting_id = Some(1),
        emailSettingOpt = Some(emailSetting),
        service_provider = "google",
        emailAddress = Some("<EMAIL>"),
        campaign_id = Some(1),
        teamMember = teamMember,
        email_address = Some("<EMAIL>"),
        org = org,
        teamId = TeamId(1),
        accountId = AccountId(1),
        allowCRMIntegration = false,
        isZapmailFlow = false
      )
      assert(result.isLeft)
      assert(result == Left(GetOAuthUrlError.GSuiteDomainNotInstalled))
    }
    it("test for getOAuthUrl when service_provider is google and googleApiKeyAndSettingsTry returns false") {
      val err = new Exception("Error occured")
      (freeEmailDomainListServiceMock.checkIfFreeEmailService(_: String)(_: SRLogger))
        .expects("<EMAIL>", *)
        .returning(Success(false))
      (emailSettingDAOMock.isGsuiteDomainInstalled(_: String, _: Long)(using _: SRLogger))
        .expects("<EMAIL>", 1, *)
        .returning(true)
      (emailSettingDAOMock.getGoogleAuthKeysForOrg(_: Long))
        .expects(1)
        .returning(Failure(err))
      (emailAccountTestServiceMock.insertEmailSettingIntegrationLogs(_: AccountId, _: OrgId, _: TeamId, _: Option[String], _: Option[EmailServiceProvider], _: Boolean, _: EmailSettingIntegrationLogsStage, _: Option[String])(using _: SRLogger))
        .expects(AccountId(1), OrgId(1), TeamId(2), Some("<EMAIL>"), Some(EmailServiceProvider.GMAIL_API), false, EmailSettingIntegrationLogsStage.OAuthUrlGenerationError, Some("There was an error, please try again or contact supportError occured"), *)
        .returning(Success(Some(1)))

      val result = socialAuthService.getOAuthUrl(
        email_type = Some("send"),
        email_setting_id = Some(1),
        emailSettingOpt = Some(emailSetting),
        service_provider = "google",
        emailAddress = Some("<EMAIL>"),
        campaign_id = Some(1),
        teamMember = teamMember,
        email_address = Some("<EMAIL>"),
        org = org,
        teamId = TeamId(1),
        accountId = AccountId(1),
        allowCRMIntegration = false,
        isZapmailFlow = false,
      )
      assert(result.isLeft)
      assert(result == Left(GetOAuthUrlError.ServerError(err)))
    }

    it("test for getOAuthUrl when service_provider is google and googleApiKeyAndSettingsTry returns success with oAuthToken") {
      (freeEmailDomainListServiceMock.checkIfFreeEmailService(_: String)(using _: SRLogger))
        .expects("<EMAIL>", *)
        .returning(Success(false))
      (emailSettingDAOMock.isGsuiteDomainInstalled(_: String, _: Long)(using _: SRLogger))
        .expects("<EMAIL>", 1, *)
        .returning(true)
      (emailSettingDAOMock.getGoogleAuthKeysForOrg(_: Long))
        .expects(1)
        .returning(Success(RepGoogleApiKey(id = 1, cl_id = "1", cl_sec = "5")))
      (googleOAuthMock.authorizationUrl(_: String,_: String, _:GoogleOAuthSettings, _: Option[String], _: Boolean)(_:WSClient, _:ExecutionContext, _: SRLogger))
        .expects("1___1___google___1___send___1___false___false___false___false","1",GoogleOAuthSettings(authorizationURL = "https://accounts.google.com/o/oauth2/auth",accessTokenURL="https://www.googleapis.com/oauth2/v4/token",redirectRoute="/dashboard/account_settings/email_accounts",scope = "email profile https://www.googleapis.com/auth/gmail.modify",redirectURL = "http://localhost:3001/dashboard/account_settings/email_accounts"),Option("<EMAIL>"),true,*,*,*)
        .returning("authorizationUrl")
      (emailAccountTestServiceMock.insertEmailSettingIntegrationLogs(_: AccountId, _: OrgId, _: TeamId, _: Option[String], _: Option[EmailServiceProvider], _: Boolean, _: EmailSettingIntegrationLogsStage, _: Option[String])(using _: SRLogger))
        .expects(AccountId(1), OrgId(1), TeamId(2), Some("<EMAIL>"), Some(EmailServiceProvider.GMAIL_API), false, EmailSettingIntegrationLogsStage.OAuthUrlGenerationSuccess, None, *)
        .returning(Success(Some(1)))

      val result = socialAuthService.getOAuthUrl(
        email_type = Some("send"),
        email_setting_id = Some(1),
        emailSettingOpt = Some(emailSetting),
        service_provider = "google",
        emailAddress = Some("<EMAIL>"),
        campaign_id = Some(1),
        teamMember = teamMember,
        email_address = Some("<EMAIL>"),
        org = org,
        teamId = TeamId(1),
        accountId = AccountId(1),
        allowCRMIntegration = false,
        isZapmailFlow = false,
      )
      assert(result.isRight)
    }

    it("test for getOAuthUrl when service_provider is outlook  returns success with oAuthToken") {
      (microsoftOAuthMock.authorizationUrl(_: String, _: Option[String], _: MicrosoftOAuthSettings, _: Boolean,_:  Option[String])(_: WSClient, _: ExecutionContext))
        .expects("1___1___outlook___1___send______false___false___false___false", Option("<EMAIL>"), MicrosoftOAuthSettings(authorizationURL = "https://login.microsoftonline.com/common/oauth2/v2.0/authorize", accessTokenURL = "https://login.microsoftonline.com/common/oauth2/v2.0/token", redirectRoute = "/dashboard/account_settings/email_accounts", clientID = "x", clientSecret = "x", scope = "offline_access user.read mail.send mail.readwrite", redirectURL = "http://localhost:3001/dashboard/account_settings/email_accounts"), true, None, *, *)
        .returning("authorizationUrl")
      (emailAccountTestServiceMock.insertEmailSettingIntegrationLogs(_: AccountId, _: OrgId, _: TeamId, _: Option[String], _: Option[EmailServiceProvider], _: Boolean, _: EmailSettingIntegrationLogsStage, _: Option[String])(using _: SRLogger))
        .expects(AccountId(1), OrgId(1), TeamId(2), Some("<EMAIL>"), Some(EmailServiceProvider.OUTLOOK_API), false, EmailSettingIntegrationLogsStage.OAuthUrlGenerationSuccess, None, *)
        .returning(Success(Some(1)))
      val result = socialAuthService.getOAuthUrl(
        email_type = Some("send"),
        email_setting_id = Some(1),
        emailSettingOpt = Some(emailSetting),
        service_provider = "outlook",
        emailAddress = Some("<EMAIL>"),
        campaign_id = Some(1),
        teamMember = teamMember,
        email_address = Some("<EMAIL>"),
        org = org,
        teamId = TeamId(1),
        accountId = AccountId(1),
        allowCRMIntegration = false,
        isZapmailFlow = false,
      )
      assert(result.isRight)
    }
    it("test for getOAuthUrl when service_provider is other and workflowCrmSettingsDAO.getAllIntegrationsForTeam return failure") {
      val err = new Exception("Error occured")
      (workflowCrmSettingsDAOMock.getAllIntegrationsForTeam(_: Long))
        .expects(1)
        .returning(Failure(err))
      val result = socialAuthService.getOAuthUrl(
        email_type = Some("send"),
        email_setting_id = Some(1),
        emailSettingOpt = Some(emailSetting),
        service_provider = "other",
        emailAddress = Some("<EMAIL>"),
        campaign_id = Some(1),
        teamMember = teamMember,
        email_address = Some("<EMAIL>"),
        org = org,
        teamId = TeamId(1),
        accountId = AccountId(1),
        allowCRMIntegration = false,
        isZapmailFlow = false,
      )
      assert(result.isLeft)
      assert(result == Left(GetOAuthUrlError.ServerError(err)))
    }

    it("test for getOAuthUrl when service_provider is zoho and workflowCrmSettingsDAO.getAllIntegrationsForTeam return Success with hubspot as integration type") {
      (workflowCrmSettingsDAOMock.getAllIntegrationsForTeam(_: Long))
        .expects(1)
        .returning(Success(List(IntegrationType.HUBSPOT)))
      val result = socialAuthService.getOAuthUrl(
        email_type = Some("send"),
        email_setting_id = Some(1),
        emailSettingOpt = Some(emailSetting),
        service_provider = "zoho",
        emailAddress = Some("<EMAIL>"),
        campaign_id = Some(1),
        teamMember = teamMember,
        email_address = Some("<EMAIL>"),
        org = org,
        teamId = TeamId(1),
        accountId = AccountId(1),
        allowCRMIntegration = false,
        isZapmailFlow = false,
      )
      assert(result.isLeft)
      assert(result == Left(GetOAuthUrlError.MultipleCrmIntegrationError))
    }

    it("test for getOAuthUrl when service_provider is hubspot and workflowCrmSettingsDAO.getAllIntegrationsForTeam return Success with hubspot as integration type and returns upgrade subscription") {
      (workflowCrmSettingsDAOMock.getAllIntegrationsForTeam(_: Long))
        .expects(1)
        .returning(Success(List(IntegrationType.HUBSPOT)))
      val result = socialAuthService.getOAuthUrl(
        email_type = Some("send"),
        email_setting_id = Some(1),
        emailSettingOpt = Some(emailSetting),
        service_provider = "hubspot",
        emailAddress = Some("<EMAIL>"),
        campaign_id = Some(1),
        teamMember = teamMember,
        email_address = Some("<EMAIL>"),
        org = org,
        teamId = TeamId(1),
        accountId = AccountId(1),
        allowCRMIntegration = false,
        isZapmailFlow = false,
      )
      assert(result.isLeft)
      assert(result == Left(GetOAuthUrlError.UpgradeSubscription))
    }

    it("test for getOAuthUrl when service_provider is hubspot and workflowCrmSettingsDAO.getAllIntegrationsForTeam return Success with hubspot as integration type and return oAuthToken") {
      (workflowCrmSettingsDAOMock.getAllIntegrationsForTeam(_: Long))
        .expects(1)
        .returning(Success(List(IntegrationType.HUBSPOT)))
      (hubspotOAuthMock.authorizationUrl(_: String,_: Boolean)(_: WSClient, _: ExecutionContext))
        .expects("1___1___hubspot___1___send_________false___false___false", false, *, *)
        .returning("authorizationUrl")
      (emailAccountTestServiceMock.insertEmailSettingIntegrationLogs(_: AccountId, _: OrgId, _: TeamId, _: Option[String], _: Option[EmailServiceProvider], _: Boolean, _: EmailSettingIntegrationLogsStage, _: Option[String])(using _: SRLogger))
        .expects(AccountId(1), OrgId(1), TeamId(2), Some("<EMAIL>"), Some(EmailServiceProvider.OTHER), false, EmailSettingIntegrationLogsStage.OAuthUrlGenerationSuccess, None, *)
        .returning(Success(Some(1)))

      val result = socialAuthService.getOAuthUrl(
        email_type = Some("send"),
        email_setting_id = Some(1),
        emailSettingOpt = Some(emailSetting),
        service_provider = "hubspot",
        emailAddress = Some("<EMAIL>"),
        campaign_id = Some(1),
        teamMember = teamMember,
        email_address = Some("<EMAIL>"),
        org = org,
        teamId = TeamId(1),
        accountId = AccountId(1),
        allowCRMIntegration = true,
        isZapmailFlow = false,
      )
      assert(result.isRight)
    }

    it("test for getOAuthUrl when service_provider is zoho and workflowCrmSettingsDAO.getAllIntegrationsForTeam return Success with zoho as integration type and return oAuthToken") {
      (workflowCrmSettingsDAOMock.getAllIntegrationsForTeam(_: Long))
        .expects(1)
        .returning(Success(List(IntegrationType.ZOHO)))
      (zohoOAuthMock.authorizationUrl(_: String, _: Boolean)(_: WSClient, _: ExecutionContext))
        .expects("1___1___zoho___1___send_________false___false___false", false, *, *)
        .returning("authorizationUrl")
      (emailAccountTestServiceMock.insertEmailSettingIntegrationLogs(_: AccountId, _: OrgId, _: TeamId, _: Option[String], _: Option[EmailServiceProvider], _: Boolean, _: EmailSettingIntegrationLogsStage, _: Option[String])(using _: SRLogger))
        .expects(AccountId(1), OrgId(1), TeamId(2), Some("<EMAIL>"), Some(EmailServiceProvider.OTHER), false, EmailSettingIntegrationLogsStage.OAuthUrlGenerationSuccess, None, *)
        .returning(Success(Some(1)))
      val result = socialAuthService.getOAuthUrl(
        email_type = Some("send"),
        email_setting_id = Some(1),
        emailSettingOpt = Some(emailSetting),
        service_provider = "zoho",
        emailAddress = Some("<EMAIL>"),
        campaign_id = Some(1),
        teamMember = teamMember,
        email_address = Some("<EMAIL>"),
        org = org,
        teamId = TeamId(1),
        accountId = AccountId(1),
        allowCRMIntegration = true,
        isZapmailFlow = false,
      )
      assert(result.isRight)
    }

    it("test for getOAuthUrl when service_provider is zoho_recruit and workflowCrmSettingsDAO.getAllIntegrationsForTeam return Success with zoho_recruit as integration type and return oAuthToken") {
      (workflowCrmSettingsDAOMock.getAllIntegrationsForTeam(_: Long))
        .expects(1)
        .returning(Success(List(IntegrationType.ZOHO_RECRUIT)))
      (zohoRecruitOAuthMock.authorizationUrl(_: String, _: Boolean)(_: WSClient, _: ExecutionContext))
        .expects("1___1___zoho_recruit___1___send_________false___false___false", false, *, *)
        .returning("authorizationUrl")
      (emailAccountTestServiceMock.insertEmailSettingIntegrationLogs(_: AccountId, _: OrgId, _: TeamId, _: Option[String], _: Option[EmailServiceProvider], _: Boolean, _: EmailSettingIntegrationLogsStage, _: Option[String])(using _: SRLogger))
        .expects(AccountId(1), OrgId(1), TeamId(2), Some("<EMAIL>"), Some(EmailServiceProvider.OTHER), false, EmailSettingIntegrationLogsStage.OAuthUrlGenerationSuccess, None, *)
        .returning(Success(Some(1)))
      val result = socialAuthService.getOAuthUrl(
        email_type = Some("send"),
        email_setting_id = Some(1),
        emailSettingOpt = Some(emailSetting),
        service_provider = "zoho_recruit",
        emailAddress = Some("<EMAIL>"),
        campaign_id = Some(1),
        teamMember = teamMember,
        email_address = Some("<EMAIL>"),
        org = org,
        teamId = TeamId(1),
        accountId = AccountId(1),
        allowCRMIntegration = true,
        isZapmailFlow = false,
      )
      assert(result.isRight)
    }

    it("test for getOAuthUrl when service_provider is pipedrive and workflowCrmSettingsDAO.getAllIntegrationsForTeam return Success with pipedrive as integration type and return oAuthToken") {
      (workflowCrmSettingsDAOMock.getAllIntegrationsForTeam(_: Long))
        .expects(1)
        .returning(Success(List(IntegrationType.PIPEDRIVE)))
      (pipedriveOAuthMock.authorizationUrl(_: String, _: Boolean)(_: WSClient, _: ExecutionContext))
        .expects("1___1___pipedrive___1___send_________false___false___false", false, *, *)
        .returning("authorizationUrl")
      (emailAccountTestServiceMock.insertEmailSettingIntegrationLogs(_: AccountId, _: OrgId, _: TeamId, _: Option[String], _: Option[EmailServiceProvider], _: Boolean, _: EmailSettingIntegrationLogsStage, _: Option[String])(using _: SRLogger))
        .expects(AccountId(1), OrgId(1), TeamId(2), Some("<EMAIL>"), Some(EmailServiceProvider.OTHER), false, EmailSettingIntegrationLogsStage.OAuthUrlGenerationSuccess, None, *)
        .returning(Success(Some(1)))
      val result = socialAuthService.getOAuthUrl(
        email_type = Some("send"),
        email_setting_id = Some(1),
        emailSettingOpt = Some(emailSetting),
        service_provider = "pipedrive",
        emailAddress = Some("<EMAIL>"),
        campaign_id = Some(1),
        teamMember = teamMember,
        email_address = Some("<EMAIL>"),
        org = org,
        teamId = TeamId(1),
        accountId = AccountId(1),
        allowCRMIntegration = true,
        isZapmailFlow = false,
      )
      assert(result.isRight)
    }

    it("test for getOAuthUrl when service_provider is salesforce and workflowCrmSettingsDAO.getAllIntegrationsForTeam return Success with salesforce as integration type and return oAuthToken") {
      (workflowCrmSettingsDAOMock.getAllIntegrationsForTeam(_: Long))
        .expects(1)
        .returning(Success(List(IntegrationType.SALESFORCE)))
      (salesforceOAuthMock.authorizationUrl(_: String, _: Boolean)(_: WSClient, _: ExecutionContext))
        .expects("1___1___salesforce___1___send_________false___false___false", false, *, *)
        .returning("authorizationUrl")
      (emailAccountTestServiceMock.insertEmailSettingIntegrationLogs(_: AccountId, _: OrgId, _: TeamId, _: Option[String], _: Option[EmailServiceProvider], _: Boolean, _: EmailSettingIntegrationLogsStage, _: Option[String])(using _: SRLogger))
        .expects(AccountId(1), OrgId(1), TeamId(2), Some("<EMAIL>"), Some(EmailServiceProvider.OTHER), false, EmailSettingIntegrationLogsStage.OAuthUrlGenerationSuccess, None, *)
        .returning(Success(Some(1)))
      val result = socialAuthService.getOAuthUrl(
        email_type = Some("send"),
        email_setting_id = Some(1),
        emailSettingOpt = Some(emailSetting),
        service_provider = "salesforce",
        emailAddress = Some("<EMAIL>"),
        campaign_id = Some(1),
        teamMember = teamMember,
        email_address = Some("<EMAIL>"),
        org = org,
        teamId = TeamId(1),
        accountId = AccountId(1),
        allowCRMIntegration = true,
        isZapmailFlow = false,
      )
      assert(result.isRight)
    }

    it("test for getOAuthUrl when service_provider is smartreach and workflowCrmSettingsDAO.getAllIntegrationsForTeam return Success with smartreach as integration type and return oAuthToken") {
      (emailAccountTestServiceMock.insertEmailSettingIntegrationLogs(_: AccountId, _: OrgId, _: TeamId, _: Option[String], _: Option[EmailServiceProvider], _: Boolean, _: EmailSettingIntegrationLogsStage, _: Option[String])(using _: SRLogger))
        .expects(AccountId(1), OrgId(1), TeamId(2), Some("<EMAIL>"), Some(EmailServiceProvider.OTHER), false, EmailSettingIntegrationLogsStage.OAuthUrlGenerationError, Some("Invalid Service Provider"), *)
        .returning(Success(Some(1)))
      (workflowCrmSettingsDAOMock.getAllIntegrationsForTeam(_: Long))
        .expects(1)
        .returning(Success(List(IntegrationType.SMARTREACH)))
      val result = socialAuthService.getOAuthUrl(
        email_type = Some("send"),
        email_setting_id = Some(1),
        emailSettingOpt = Some(emailSetting),
        service_provider = "smartreach",
        emailAddress = Some("<EMAIL>"),
        campaign_id = Some(1),
        teamMember = teamMember,
        email_address = Some("<EMAIL>"),
        org = org,
        teamId = TeamId(1),
        accountId = AccountId(1),
        allowCRMIntegration = true,
        isZapmailFlow = false,
      )
      assert(result.isLeft)
      assert(result == Left(GetOAuthUrlError.InvalidServiceProvider))
    }

    it("test for oauthFetchTokens when codeOpt is None"){
      val result = socialAuthService.oauthFetchTokens(
        codeOpt = None,
        serviceProvider = "google",
        accountId = AccountId(1),
        teamId = TeamId(1),
        state = Some("abc"),
        location = Some("abc"),
        teamMember = teamMember,
        is_sandbox = false,
        googleApiKeyId = None,
        permittedAccountIds = Seq(),
        orgId = OrgId(1),
        campaignId = None,
        emailType = None,
        isZapmailOauthFlow = false
      )

      result.map(rs=>
        assert(rs.isLeft)
      )
    }

    it("test for oauthFetchTokens when tIntegrationCRMService.getTPUsers return GetCRMUsersError.MalformedUsersResponseError") {
      (tIntegrationCRMServiceMock.getAccessToken(_: IntegrationType,_: String,_: Option[String],_: Boolean)(_: WSClient,_: ExecutionContext,_: SRLogger))
        .expects(IntegrationType.HUBSPOT,"code",Some("abc"),false,*,*,*)
        .returning(Future(Right(fullTokenData)))
      (tIntegrationCRMServiceMock.getTPColumns(_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData,_: Option[String] )(_: WSClient, _: ExecutionContext, _: SRLogger))
        .expects(IntegrationType.HUBSPOT, fullTokenData,None, *, *, *)
        .returning(Future(Seq()))
      (tIntegrationCRMServiceMock.getTPUsers(_: IntegrationType,_: IntegrationTPAccessTokenResponse.FullTokenData)(_: WSClient,_: ExecutionContext,_: SRLogger))
        .expects(IntegrationType.HUBSPOT,fullTokenData,*,*,*)
        .returning(Future(Left(GetCRMUsersError.MalformedUsersResponseError("Error occurred"))))
      val result = socialAuthService.oauthFetchTokens(
        codeOpt = Some("code"),
        serviceProvider = "hubspot",
        accountId = AccountId(1),
        teamId = TeamId(1),
        state = Some("abc"),
        location = Some("abc"),
        teamMember = teamMember,
        is_sandbox = false,
        googleApiKeyId = None,
        permittedAccountIds = Seq(),
        orgId = OrgId(1),
        campaignId = None,
        emailType = None,
        isZapmailOauthFlow = false
      )

      result.map(rs => {
        assert(rs.isLeft)
        assert(rs == Left(OAuthFetchTokensError.ServerError("Error occurred",error = None)))
      }
      )
    }

    it("test for oauthFetchTokens when tIntegrationCRMService.getTPUsers return GetCRMUsersError.CommonCRMAPIError ") {
      (tIntegrationCRMServiceMock.getAccessToken(_: IntegrationType, _: String, _: Option[String], _: Boolean)(_: WSClient, _: ExecutionContext, _: SRLogger))
        .expects(IntegrationType.HUBSPOT, "code", Some("abc"), false, *, *, *)
        .returning(Future(Right(fullTokenData)))
      (tIntegrationCRMServiceMock.getTPColumns(_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: Option[String])(_: WSClient, _: ExecutionContext, _: SRLogger))
        .expects(IntegrationType.HUBSPOT, fullTokenData, None, *, *, *)
        .returning(Future(Seq()))
      (tIntegrationCRMServiceMock.getTPUsers(_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData)(_: WSClient, _: ExecutionContext, _: SRLogger))
        .expects(IntegrationType.HUBSPOT, fullTokenData, *, *, *)
        .returning(Future(Left(GetCRMUsersError.CommonCRMAPIError(err = ConvertedLead("Error occured")))))
      val result = socialAuthService.oauthFetchTokens(
        codeOpt = Some("code"),
        serviceProvider = "hubspot",
        accountId = AccountId(1),
        teamId = TeamId(1),
        state = Some("abc"),
        location = Some("abc"),
        teamMember = teamMember,
        is_sandbox = false,
        googleApiKeyId = None,
        permittedAccountIds = Seq(),
        orgId = OrgId(1),
        campaignId = None,
        emailType = None,
        isZapmailOauthFlow = false
      )

      result.map(rs => {
        assert(rs.isLeft)
        assert(rs == Left(OAuthFetchTokensError.BadRequestError("ConvertedLead.Error occured")))
      }
      )
    }

    it("test for oauthFetchTokens when tIntegrationCRMService.getTPUsers return CheckLimitAndAddOrUpdateCRMError.CRMIntegrationLimitReached") {
      (tIntegrationCRMServiceMock.getAccessToken(_: IntegrationType, _: String, _: Option[String], _: Boolean)(_: WSClient, _: ExecutionContext, _: SRLogger))
        .expects(IntegrationType.HUBSPOT, "code", Some("abc"), false, *, *, *)
        .returning(Future(Right(fullTokenData)))
      (tIntegrationCRMServiceMock.getTPColumns(_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: Option[String])(_: WSClient, _: ExecutionContext, _: SRLogger))
        .expects(IntegrationType.HUBSPOT, fullTokenData, None, *, *, *)
        .returning(Future(Seq()))
      (tIntegrationCRMServiceMock.getTPUsers(_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData)(_: WSClient, _: ExecutionContext, _: SRLogger))
        .expects(IntegrationType.HUBSPOT, fullTokenData, *, *, *)
        .returning(Future(Right(Seq())))
      (tIntegrationCRMServiceMock.me(_: IntegrationType,_: IntegrationTPAccessTokenResponse.FullTokenData)(_: WSClient, _: ExecutionContext, _: SRLogger))
        .expects(IntegrationType.HUBSPOT,fullTokenData,*,*,*)
        .returning(Future(integrationTPUser))
      (workflowCrmSettingsServiceMock.checkLimitAndAddOrUpdateCRM(_: TeamId,_: AccountId,_: IntegrationTPAccessTokenResponse.FullTokenData,_: IntegrationType,_: Option[String],_: Option[String],_: Option[String],_: Seq[IntegrationTPUsersList],_: Boolean)(using _: SRLogger))
        .expects(TeamId(2),AccountId(2),fullTokenData,IntegrationType.HUBSPOT,Some("id"),Some("company_id"),Some("owner_id"),Seq(),false,*)
        .returning(Left(CheckLimitAndAddOrUpdateCRMError.CRMIntegrationLimitReached))

      val result = socialAuthService.oauthFetchTokens(
        codeOpt = Some("code"),
        serviceProvider = "hubspot",
        accountId = AccountId(1),
        teamId = TeamId(1),
        state = Some("abc"),
        location = Some("abc"),
        teamMember = teamMember,
        is_sandbox = false,
        googleApiKeyId = None,
        permittedAccountIds = Seq(),
        orgId = OrgId(1),
        campaignId = None,
        emailType = None,
        isZapmailOauthFlow = false
      )

      result.map(rs => {
        assert(rs.isLeft)
        assert(rs == Left(OAuthFetchTokensError.BadRequestError("You don't have enough CRM Integration credits left")))
      }
      )
    }

    it("test for oauthFetchTokens when tIntegrationCRMService.getTPUsers return CheckLimitAndAddOrUpdateCRMError.SQLException") {
      val error = new Throwable("Error occured")
      (tIntegrationCRMServiceMock.getAccessToken(_: IntegrationType, _: String, _: Option[String], _: Boolean)(_: WSClient, _: ExecutionContext, _: SRLogger))
        .expects(IntegrationType.HUBSPOT, "code", Some("abc"), false, *, *, *)
        .returning(Future(Right(fullTokenData)))
      (tIntegrationCRMServiceMock.getTPColumns(_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: Option[String])(_: WSClient, _: ExecutionContext, _: SRLogger))
        .expects(IntegrationType.HUBSPOT, fullTokenData, None, *, *, *)
        .returning(Future(Seq()))
      (tIntegrationCRMServiceMock.getTPUsers(_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData)(_: WSClient, _: ExecutionContext, _: SRLogger))
        .expects(IntegrationType.HUBSPOT, fullTokenData, *, *, *)
        .returning(Future(Right(Seq())))
      (tIntegrationCRMServiceMock.me(_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData)(_: WSClient, _: ExecutionContext, _: SRLogger))
        .expects(IntegrationType.HUBSPOT, fullTokenData, *, *, *)
        .returning(Future(integrationTPUser))
      (workflowCrmSettingsServiceMock.checkLimitAndAddOrUpdateCRM(_: TeamId, _: AccountId, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationType, _: Option[String], _: Option[String], _: Option[String], _: Seq[IntegrationTPUsersList], _: Boolean)(using _: SRLogger))
        .expects(TeamId(2), AccountId(2), fullTokenData, IntegrationType.HUBSPOT, Some("id"), Some("company_id"), Some("owner_id"), Seq(), false, *)
        .returning(Left(CheckLimitAndAddOrUpdateCRMError.SQLException(error)))

      val result = socialAuthService.oauthFetchTokens(
        codeOpt = Some("code"),
        serviceProvider = "hubspot",
        accountId = AccountId(1),
        teamId = TeamId(1),
        state = Some("abc"),
        location = Some("abc"),
        teamMember = teamMember,
        is_sandbox = false,
        googleApiKeyId = None,
        permittedAccountIds = Seq(),
        orgId = OrgId(1),
        campaignId = None,
        emailType = None,
        isZapmailOauthFlow = false
      )

      result.map(rs => {
        assert(rs.isLeft)
        assert(rs == Left(OAuthFetchTokensError.ServerError("Failed hubspot Authorization. Please retry or contact support.",error = Some(error))))
      }
      )
    }

    it("test for oauthFetchTokens when tIntegrationCRMService.getTPUsers return OAuthFetchTokensError.ServerError") {
      (tIntegrationCRMServiceMock.getAccessToken(_: IntegrationType, _: String, _: Option[String], _: Boolean)(_: WSClient, _: ExecutionContext, _: SRLogger))
        .expects(IntegrationType.HUBSPOT, "code", Some("abc"), false, *, *, *)
        .returning(Future(Right(fullTokenData)))
      (tIntegrationCRMServiceMock.getTPColumns(_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: Option[String])(_: WSClient, _: ExecutionContext, _: SRLogger))
        .expects(IntegrationType.HUBSPOT, fullTokenData, None, *, *, *)
        .returning(Future(Seq()))
      (tIntegrationCRMServiceMock.getTPUsers(_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData)(_: WSClient, _: ExecutionContext, _: SRLogger))
        .expects(IntegrationType.HUBSPOT, fullTokenData, *, *, *)
        .returning(Future(Right(Seq())))
      (tIntegrationCRMServiceMock.me(_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData)(_: WSClient, _: ExecutionContext, _: SRLogger))
        .expects(IntegrationType.HUBSPOT, fullTokenData, *, *, *)
        .returning(Future(integrationTPUser))
      (workflowCrmSettingsServiceMock.checkLimitAndAddOrUpdateCRM(_: TeamId, _: AccountId, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationType, _: Option[String], _: Option[String], _: Option[String], _: Seq[IntegrationTPUsersList], _: Boolean)(using _: SRLogger))
        .expects(TeamId(2), AccountId(2), fullTokenData, IntegrationType.HUBSPOT, Some("id"), Some("company_id"), Some("owner_id"), Seq(), false, *)
        .returning(Right(Some(1)))

      val result = socialAuthService.oauthFetchTokens(
        codeOpt = Some("code"),
        serviceProvider = "hubspot",
        accountId = AccountId(1),
        teamId = TeamId(1),
        state = Some("abc"),
        location = Some("abc"),
        teamMember = teamMember,
        is_sandbox = false,
        googleApiKeyId = None,
        permittedAccountIds = Seq(),
        orgId = OrgId(1),
        campaignId = None,
        emailType = None,
        isZapmailOauthFlow = false
      )

      result.map(rs => {
        assert(rs.isRight)
        assert(rs == Right(OAuthFetchTokensResponse(message = s"Your ${IntegrationType.HUBSPOT} account successfully integrated", data = Json.obj())))
      }
      )
    }

    it("test for oauthFetchTokens when Integration type is smartreach ") {
      val result = socialAuthService.oauthFetchTokens(
        codeOpt = Some("code"),
        serviceProvider = "smartreach",
        accountId = AccountId(1),
        teamId = TeamId(1),
        state = Some("abc"),
        location = Some("abc"),
        teamMember = teamMember,
        is_sandbox = false,
        googleApiKeyId = None,
        permittedAccountIds = Seq(),
        orgId = OrgId(1),
        campaignId = None,
        emailType = None,
        isZapmailOauthFlow = false
      )

      result.map(rs => {
        assert(rs.isLeft)
        assert(rs == Left(OAuthFetchTokensError.BadRequestError(s"Invalid service type. Please contact support.")))
      })
    }

    it("test for oauthFetchTokens when Integration type is outlook and acces token is empty ") {
      (microsoftOAuthMock.getAccessToken(_: String,_: SRLogger,_: MicrosoftOAuthSettings)(_: WSClient,_: ExecutionContext))
        .expects("code",Logger,SRAppConfig.microsoftOAuthSettings,*,*)
        .returning(Future(None))
      (emailAccountTestServiceMock.insertEmailSettingIntegrationLogs(_: AccountId, _: OrgId, _: TeamId, _: Option[String], _: Option[EmailServiceProvider], _: Boolean, _: EmailSettingIntegrationLogsStage, _: Option[String])(using _: SRLogger))
        .expects(AccountId(1), OrgId(1), TeamId(2), None, Some(EmailServiceProvider.OUTLOOK_API), false, EmailSettingIntegrationLogsStage.OAuthUrlGenerationError, Some("outlookAuthFetchTokens error: emailSettingsForSaving, TeamMember(2,Test Company,2,2,Some(Prateek),Some(B),<EMAIL>,owner,None,Some(zapier_key)) Failed outlook Authorization. Please retry or contact support."), *)
        .returning(Success(Some(1)))
      val result = socialAuthService.oauthFetchTokens(
        codeOpt = Some("code"),
        serviceProvider = "outlook",
        accountId = AccountId(1),
        teamId = TeamId(1),
        state = Some("abc"),
        location = Some("abc"),
        teamMember = teamMember,
        is_sandbox = false,
        googleApiKeyId = None,
        permittedAccountIds = Seq(),
        orgId = OrgId(1),
        campaignId = None,
        emailType = None,
        isZapmailOauthFlow = false
      )

      result.map(rs => {
        assert(rs.isLeft)
        assert(rs == Left(OAuthFetchTokensError.BadRequestError("Failed outlook Authorization. Please retry or contact support.")))
      })
    }

    it("test for oauthFetchTokens when Integration type is google and googleApiKeyId.isEmpty ") {
      (microsoftOAuthMock.getAccessToken(_: String, _: SRLogger, _: MicrosoftOAuthSettings)(_: WSClient, _: ExecutionContext))
        .expects("code", Logger, SRAppConfig.microsoftOAuthSettings, *, *)
        .returning(Future(None))
      (emailAccountTestServiceMock.insertEmailSettingIntegrationLogs(_: AccountId, _: OrgId, _: TeamId, _: Option[String], _: Option[EmailServiceProvider], _: Boolean, _: EmailSettingIntegrationLogsStage, _: Option[String])(using _: SRLogger))
        .expects(AccountId(1), OrgId(1), TeamId(2), None, Some(EmailServiceProvider.OUTLOOK_API), false, EmailSettingIntegrationLogsStage.OAuthUrlGenerationError, Some("outlookAuthFetchTokens error: emailSettingsForSaving, TeamMember(2,Test Company,2,2,Some(Prateek),Some(B),<EMAIL>,owner,None,Some(zapier_key)) Failed outlook Authorization. Please retry or contact support."), *)
        .returning(Success(Some(1)))
      val result = socialAuthService.oauthFetchTokens(
        codeOpt = Some("code"),
        serviceProvider = "outlook",
        accountId = AccountId(1),
        teamId = TeamId(1),
        state = Some("abc"),
        location = Some("abc"),
        teamMember = teamMember,
        is_sandbox = false,
        googleApiKeyId = None,
        permittedAccountIds = Seq(),
        orgId = OrgId(1),
        campaignId = None,
        emailType = None,
        isZapmailOauthFlow = false
      )

      result.map(rs => {
        assert(rs.isLeft)
        assert(rs == Left(OAuthFetchTokensError.BadRequestError("Failed outlook Authorization. Please retry or contact support.")))
      })
    }

    it("test for oauthFetchTokens when Integration type is acces token is non empty ") {
      (microsoftOAuthMock.getAccessToken(_: String, _: SRLogger, _: MicrosoftOAuthSettings)(_: WSClient, _: ExecutionContext))
        .expects("code", Logger, SRAppConfig.microsoftOAuthSettings, *, *)
        .returning(Future(Some(msAccessTokenResponse)))
      (microsoftOAuthMock.getProfile(_: String,_: SRLogger)(_: WSClient, _: ExecutionContext))
        .expects("access_token",Logger,*,*)
        .returning(Future(msUserProfileResponse))
      (emailSettingDAOMock.findByEmailAndTeamId(_:String,_: Long))
        .expects("mail",2)
        .returning(Option(emailSetting))
      (accountServiceMock.find(_: Long)(_: SRLogger))
        .expects(1,*)
        .returning(Success(account))
      (emailAccountTestServiceMock.insertEmailSettingIntegrationLogs(_: AccountId, _: OrgId, _: TeamId, _: Option[String], _: Option[EmailServiceProvider], _: Boolean, _: EmailSettingIntegrationLogsStage, _: Option[String])(using _: SRLogger))
        .expects(AccountId(1), OrgId(1), TeamId(2), None, Some(EmailServiceProvider.OUTLOOK_API), false, EmailSettingIntegrationLogsStage.OAuthUrlGenerationError, Some("mail is already integrated by . You do not have the permission to edit/update it. Please contact your admin or our support."), *)
        .returning(Success(Some(1)))
      val result = socialAuthService.oauthFetchTokens(
        codeOpt = Some("code"),
        serviceProvider = "outlook",
        accountId = AccountId(1),
        teamId = TeamId(1),
        state = Some("abc"),
        location = Some("abc"),
        teamMember = teamMember,
        is_sandbox = false,
        googleApiKeyId = None,
        permittedAccountIds = Seq(),
        orgId = OrgId(1),
        campaignId = None,
        emailType = None,
        isZapmailOauthFlow = false
      )

      result.map(rs => {
        assert(rs.isLeft)
        assert(rs == Left(OAuthFetchTokensError.BadRequestError(s"mail is already integrated by . You do not have the permission to edit/update it. Please contact your admin or our support.")))
      })
    }

    it("test for oauthFetchTokens when Integration type outlook and retuen free email error ") {
      (microsoftOAuthMock.getAccessToken(_: String, _: SRLogger, _: MicrosoftOAuthSettings)(_: WSClient, _: ExecutionContext))
        .expects("code", Logger, SRAppConfig.microsoftOAuthSettings, *, *)
        .returning(Future(Some(msAccessTokenResponse)))
      (microsoftOAuthMock.getProfile(_: String, _: SRLogger)(_: WSClient, _: ExecutionContext))
        .expects("access_token", Logger, *, *)
        .returning(Future(msUserProfileResponse))
      (emailSettingDAOMock.findByEmailAndTeamId(_: String, _: Long))
        .expects("mail", 2)
        .returning(None)
      (emailAccountServiceMock.createEmailViaOauth(_: Long,_: Long,_: Long,_: Long,_: EmailSettingCreateViaOAuth, _: String)(using _: SRLogger, _: ExecutionContext, _: WSClient))
        .expects(1,2,2,2,*,*,*,*, *)
        .returning(Left(CreateEmailViaOauthError.FreeDomainUseError))
      (emailAccountTestServiceMock.insertEmailSettingIntegrationLogs(_: AccountId, _: OrgId, _: TeamId, _: Option[String], _: Option[EmailServiceProvider], _: Boolean, _: EmailSettingIntegrationLogsStage, _: Option[String])(using _: SRLogger))
        .expects(AccountId(1), OrgId(1), TeamId(2), None, Some(EmailServiceProvider.OUTLOOK_API), false, EmailSettingIntegrationLogsStage.OAuthUrlGenerationError, Some("This email service is not allowed. It could be for various reasons including if the email service is a free or temporary service, or is blocked by our anti-spam filters."), *)
        .returning(Success(Some(1)))
      val result = socialAuthService.oauthFetchTokens(
        codeOpt = Some("code"),
        serviceProvider = "outlook",
        accountId = AccountId(1),
        teamId = TeamId(1),
        state = Some("abc"),
        location = Some("abc"),
        teamMember = teamMember,
        is_sandbox = false,
        googleApiKeyId = None,
        permittedAccountIds = Seq(),
        orgId = OrgId(1),
        campaignId = None,
        emailType = None,
        isZapmailOauthFlow = false
      )

      result.map(rs => {
        assert(rs.isLeft)
        assert(rs == Left(OAuthFetchTokensError.BadRequestError(CONSTANTS.API_MSGS.FREE_EMAIL_DOMAIN_ERROR)))
      })
    }

    it("test for oauthFetchTokens when Integration type outlook and retuen CreateEmailViaOauthError.DuplicateEmailError ") {
      (microsoftOAuthMock.getAccessToken(_: String, _: SRLogger, _: MicrosoftOAuthSettings)(_: WSClient, _: ExecutionContext))
        .expects("code", Logger, SRAppConfig.microsoftOAuthSettings, *, *)
        .returning(Future(Some(msAccessTokenResponse)))
      (microsoftOAuthMock.getProfile(_: String, _: SRLogger)(_: WSClient, _: ExecutionContext))
        .expects("access_token", Logger, *, *)
        .returning(Future(msUserProfileResponse))
      (emailSettingDAOMock.findByEmailAndTeamId(_: String, _: Long))
        .expects("mail", 2)
        .returning(None)
      (emailAccountServiceMock.createEmailViaOauth(_: Long,_: Long,_: Long,_: Long,_: EmailSettingCreateViaOAuth, _: String)(using _: SRLogger, _: ExecutionContext, _: WSClient))
        .expects(1, 2, 2, 2, *, *, *, *, *)
        .returning(Left(CreateEmailViaOauthError.DuplicateEmailError))
      (emailAccountTestServiceMock.insertEmailSettingIntegrationLogs(_: AccountId, _: OrgId, _: TeamId, _: Option[String], _: Option[EmailServiceProvider], _: Boolean, _: EmailSettingIntegrationLogsStage, _: Option[String])(using _: SRLogger))
        .expects(AccountId(1), OrgId(1), TeamId(2), None, Some(EmailServiceProvider.OUTLOOK_API), false, EmailSettingIntegrationLogsStage.OAuthUrlGenerationError, Some("Given email is already added to your account"), *)
        .returning(Success(Some(1)))
      val result = socialAuthService.oauthFetchTokens(
        codeOpt = Some("code"),
        serviceProvider = "outlook",
        accountId = AccountId(1),
        teamId = TeamId(1),
        state = Some("abc"),
        location = Some("abc"),
        teamMember = teamMember,
        is_sandbox = false,
        googleApiKeyId = None,
        permittedAccountIds = Seq(),
        orgId = OrgId(1),
        campaignId = None,
        emailType = None,
        isZapmailOauthFlow = false
      )

      result.map(rs => {
        assert(rs.isLeft)
        assert(rs == Left(OAuthFetchTokensError.BadRequestError("Given email is already added to your account")))
      })
    }

    it("test for oauthFetchTokens when Integration type outlook and retuen CreateEmailViaOauthError.SQLException ") {
      val error = new Throwable("Error occured")
      (microsoftOAuthMock.getAccessToken(_: String, _: SRLogger, _: MicrosoftOAuthSettings)(_: WSClient, _: ExecutionContext))
        .expects("code", Logger, SRAppConfig.microsoftOAuthSettings, *, *)
        .returning(Future(Some(msAccessTokenResponse)))
      (microsoftOAuthMock.getProfile(_: String, _: SRLogger)(_: WSClient, _: ExecutionContext))
        .expects("access_token", Logger, *, *)
        .returning(Future(msUserProfileResponse))
      (emailSettingDAOMock.findByEmailAndTeamId(_: String, _: Long))
        .expects("mail", 2)
        .returning(None)
      (emailAccountServiceMock.createEmailViaOauth(_: Long,_: Long,_: Long,_: Long,_: EmailSettingCreateViaOAuth, _: String)(using _: SRLogger, _: ExecutionContext, _: WSClient))
        .expects(1, 2, 2, 2, *, *, *, *, *)
        .returning(Left(CreateEmailViaOauthError.SQLException(msg = error.getMessage,err = error)))
      (emailAccountTestServiceMock.insertEmailSettingIntegrationLogs(_: AccountId, _: OrgId, _: TeamId, _: Option[String], _: Option[EmailServiceProvider], _: Boolean, _: EmailSettingIntegrationLogsStage, _: Option[String])(using _: SRLogger))
        .expects(AccountId(1), OrgId(1), TeamId(2), None, Some(EmailServiceProvider.OUTLOOK_API), false, EmailSettingIntegrationLogsStage.OAuthUrlGenerationError, Some("5 createViaOauth exception mail: java.lang.Throwable: Error occured: outlookAuthFetchTokens [FATAL]: Failed: 1 :: 1 :: Some(abc) :: code"), *)
        .returning(Success(Some(1)))
      val result = socialAuthService.oauthFetchTokens(
        codeOpt = Some("code"),
        serviceProvider = "outlook",
        accountId = AccountId(1),
        teamId = TeamId(1),
        state = Some("abc"),
        location = Some("abc"),
        teamMember = teamMember,
        is_sandbox = false,
        googleApiKeyId = None,
        permittedAccountIds = Seq(),
        orgId = OrgId(1),
        campaignId = None,
        emailType = None,
        isZapmailOauthFlow = false
      )

      result.map(rs => {
        assert(rs.isLeft)
        assert(rs == Left(OAuthFetchTokensError.ServerError("Failed outlook Authorization. Please retry or contact support.",error = Some(error))))
      })
    }

    it("test for oauthFetchTokens when Integration type outlook and retuen CreateEmailViaOauthError.EmailNotAddedError ") {
      (microsoftOAuthMock.getAccessToken(_: String, _: SRLogger, _: MicrosoftOAuthSettings)(_: WSClient, _: ExecutionContext))
        .expects("code", Logger, SRAppConfig.microsoftOAuthSettings, *, *)
        .returning(Future(Some(msAccessTokenResponse)))
      (microsoftOAuthMock.getProfile(_: String, _: SRLogger)(_: WSClient, _: ExecutionContext))
        .expects("access_token", Logger, *, *)
        .returning(Future(msUserProfileResponse))
      (emailSettingDAOMock.findByEmailAndTeamId(_: String, _: Long))
        .expects("mail", 2)
        .returning(None)
      (emailAccountServiceMock.createEmailViaOauth(_: Long,_: Long,_: Long,_: Long,_: EmailSettingCreateViaOAuth, _: String)(using _: SRLogger, _: ExecutionContext, _: WSClient))
        .expects(1, 2, 2, 2, *, *, *, *, *)
        .returning(Left(CreateEmailViaOauthError.EmailNotAddedError))
      (emailAccountTestServiceMock.insertEmailSettingIntegrationLogs(_: AccountId, _: OrgId, _: TeamId, _: Option[String], _: Option[EmailServiceProvider], _: Boolean, _: EmailSettingIntegrationLogsStage, _: Option[String])(using _: SRLogger))
        .expects(AccountId(1), OrgId(1), TeamId(2), None, Some(EmailServiceProvider.OUTLOOK_API), false, EmailSettingIntegrationLogsStage.OAuthUrlGenerationError, Some("6 empty savedParentEmailSetting mail: outlookAuthFetchTokens [FATAL]: Failed: 1 :: 1 :: Some(abc) :: code"), *)
        .returning(Success(Some(1)))
      val result = socialAuthService.oauthFetchTokens(
        codeOpt = Some("code"),
        serviceProvider = "outlook",
        accountId = AccountId(1),
        teamId = TeamId(1),
        state = Some("abc"),
        location = Some("abc"),
        teamMember = teamMember,
        is_sandbox = false,
        googleApiKeyId = None,
        permittedAccountIds = Seq(),
        orgId = OrgId(1),
        campaignId = None,
        emailType = None,
        isZapmailOauthFlow = false
      )

      result.map(rs => {
        assert(rs.isLeft)
        assert(rs == Left(OAuthFetchTokensError.ServerError("Failed outlook Authorization. Please retry or contact support.", error = None)))
      })
    }

    it("test for oauthFetchTokens when Integration type outlook and retuen CreateEmailViaOauthError.FeatureUsageServiceError(error) ") {
      val error = new Throwable("Error occured")
      (microsoftOAuthMock.getAccessToken(_: String, _: SRLogger, _: MicrosoftOAuthSettings)(_: WSClient, _: ExecutionContext))
        .expects("code", Logger, SRAppConfig.microsoftOAuthSettings, *, *)
        .returning(Future(Some(msAccessTokenResponse)))
      (microsoftOAuthMock.getProfile(_: String, _: SRLogger)(_: WSClient, _: ExecutionContext))
        .expects("access_token", Logger, *, *)
        .returning(Future(msUserProfileResponse))
      (emailSettingDAOMock.findByEmailAndTeamId(_: String, _: Long))
        .expects("mail", 2)
        .returning(None)
      (emailAccountServiceMock.createEmailViaOauth(_: Long,_: Long,_: Long,_: Long,_: EmailSettingCreateViaOAuth, _: String)(using _: SRLogger, _: ExecutionContext, _: WSClient))
        .expects(1, 2, 2, 2, *, *, *, *, *)
        .returning(Left(CreateEmailViaOauthError.FeatureUsageServiceError(error)))
      (emailAccountTestServiceMock.insertEmailSettingIntegrationLogs(_: AccountId, _: OrgId, _: TeamId, _: Option[String], _: Option[EmailServiceProvider], _: Boolean, _: EmailSettingIntegrationLogsStage, _: Option[String])(using _: SRLogger))
        .expects(AccountId(1), OrgId(1), TeamId(2), None, Some(EmailServiceProvider.OUTLOOK_API), false, EmailSettingIntegrationLogsStage.OAuthUrlGenerationError, Some("Error while adding email: Error occured"), *)
        .returning(Success(Some(1)))
      val result = socialAuthService.oauthFetchTokens(
        codeOpt = Some("code"),
        serviceProvider = "outlook",
        accountId = AccountId(1),
        teamId = TeamId(1),
        state = Some("abc"),
        location = Some("abc"),
        teamMember = teamMember,
        is_sandbox = false,
        googleApiKeyId = None,
        permittedAccountIds = Seq(),
        orgId = OrgId(1),
        campaignId = None,
        emailType = None,
        isZapmailOauthFlow = false
      )

      result.map(rs => {
        assert(rs.isLeft)
        assert(rs == Left(OAuthFetchTokensError.ServerError("Error while adding email: ", error = Some(error))))
      })
    }

    it("test for oauthFetchTokens when Integration type outlook and retuen Success ") {
      (microsoftOAuthMock.getAccessToken(_: String, _: SRLogger, _: MicrosoftOAuthSettings)(_: WSClient, _: ExecutionContext))
        .expects("code", Logger, SRAppConfig.microsoftOAuthSettings, *, *)
        .returning(Future(Some(msAccessTokenResponse)))
      (microsoftOAuthMock.getProfile(_: String, _: SRLogger)(_: WSClient, _: ExecutionContext))
        .expects("access_token", Logger, *, *)
        .returning(Future(msUserProfileResponse))
      (emailSettingDAOMock.findByEmailAndTeamId(_: String, _: Long))
        .expects("mail", 2)
        .returning(None)
      (emailAccountServiceMock.createEmailViaOauth(_: Long,_: Long,_: Long,_: Long,_: EmailSettingCreateViaOAuth, _: String)(using _: SRLogger, _: ExecutionContext, _: WSClient))
        .expects(1, 2, 2, 2, *, *, *, *, *)
        .returning(Right(emailSetting))
      val result = socialAuthService.oauthFetchTokens(
        codeOpt = Some("code"),
        serviceProvider = "outlook",
        accountId = AccountId(1),
        teamId = TeamId(1),
        state = Some("abc"),
        location = Some("abc"),
        teamMember = teamMember,
        is_sandbox = false,
        googleApiKeyId = None,
        permittedAccountIds = Seq(),
        orgId = OrgId(1),
        campaignId = None,
        emailType = None,
        isZapmailOauthFlow = false
      )

      result.map(rs => {
        assert(rs.isRight)
        assert(rs == Right(OAuthFetchTokensResponse("Email account(s) have been saved",Json.obj("emails" -> List(emailSetting), "campaign_id" -> null, "email_type" -> null))))
      })
    }

    it("test for _createAuthState when isZapmailFlow is false") {
      val stateData = OAuthState(
        campaignId = Some(1),
        emailType = Some("send"),
        aid = 1,
        tid = 2,
        serviceProvider = "google",
        googleApiKeyId = Some(3),
        campaign_basic_setup = Some(true),
        is_sandbox = false,
        is_inbox = true,
        goto_quickstart = false
      )

      val result = socialAuthService._createAuthState(stateData, isZapmailFlow = false)

      val expected = "1___2___google___1___send___3___true___false___true___false"
      assert(result == expected)
    }

    it("test for _deconstructGAuthState when isZapmailFlow is false") {
      val state = "1___2___google___1___send___3___true___false___true___false"

      val result = socialAuthService._deconstructGAuthState(state, isZapmailFlow = false)

      val expected = OAuthState(
        campaignId = Some(1),
        emailType = Some("send"),
        aid = 1,
        tid = 2,
        serviceProvider = "google",
        googleApiKeyId = Some(3),
        campaign_basic_setup = Some(true),
        is_sandbox = false,
        is_inbox = true,
        goto_quickstart = false
      )

      assert(result == expected)
    }

    it("test for _createAuthState and _deconstructGAuthState without encryption") {
      val stateData = OAuthState(
        campaignId = Some(1),
        emailType = Some("send"),
        aid = 1,
        tid = 2,
        serviceProvider = "google",
        googleApiKeyId = Some(3),
        campaign_basic_setup = Some(true),
        is_sandbox = false,
        is_inbox = true,
        goto_quickstart = false
      )

      // Test without zapmail flow (no encryption)
      val stateStr = socialAuthService._createAuthState(stateData, isZapmailFlow = false)
      val reconstructed = socialAuthService._deconstructGAuthState(stateStr, isZapmailFlow = false)

      assert(reconstructed == stateData)
    }

    it("test for _deconstructGAuthState with empty optional fields") {
      val state = "1___2___google_____________________"

      val result = socialAuthService._deconstructGAuthState(state, isZapmailFlow = false)

      val expected = OAuthState(
        campaignId = None,
        emailType = None,
        aid = 1,
        tid = 2,
        serviceProvider = "google",
        googleApiKeyId = None,
        campaign_basic_setup = None,
        is_sandbox = false,
        is_inbox = false,
        goto_quickstart = false
      )

      assert(result == expected)
    }

  }


}
