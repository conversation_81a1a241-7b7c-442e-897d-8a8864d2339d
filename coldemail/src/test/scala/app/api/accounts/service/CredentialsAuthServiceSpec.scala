package app.api.accounts.service

import org.apache.pekko.actor.ActorSystem
import org.apache.pekko.stream.ActorMaterializer
import api.{AppConfig, CONSTANTS, CacheService}
import api.accounts.models.{AccountId, AccountProfileInfo, OTPType, SignupType}
import api.accounts.service.{AccountOTPService, AccountWithRedirectUri, CredentialsAuthApiService, CredentialsAuthService, FailedAtSignup, ForgotPasswordSendOTPError, GetOTPError, LoginChallenge, OTPAndCount, OTPPassAndCode, OTPService, ResendVerificationEmailError, ResetUserCacheUtil, UpdatePasswordWithOTPError, UserRedisKeyService, VerifyEmailError, VerifyOTPError}
import api.accounts.{Account, AccountAccess, AccountCreateForm, AccountDAO, AccountMetadata, AccountService, AccountType, AccountUuid, AuthUtils, InviteMember, InviteMemberBasic, OrgCountData, OrgMetadata, OrgPlan, OrgSettings, OrganizationRole, OrganizationWithCurrentData, RolePermissionDataDAOV2, SignInForm, SignupViaPasswordForm, UpdateAccountProfileDuringOnboarding}
import api.calendar_app.models.CalendarAccountData
import api.common_auth.CommonAuthService
import api.config.AppConfigCommonAuth
import api.free_email_domain.service.FreeEmailDomainListService
import api.google_recaptcha.service.{ReCaptchaError, RecaptchaResponse, SRGoogleRecaptchaServices}
import api.reports.models.UtmData
import api.spammonitor.model.SendEmailStatusData
import api.spammonitor.service.{CheckIfEmailIsAllowedToSignUpError, SpamMonitorService}
import app.test_fixtures.accounts.OrgCountDataFixture
import app.test_fixtures.organizationa.{OrgMetadataFixture, OrgPlanFixture}
import org.joda.time.DateTime
import org.scalamock.scalatest.AsyncMockFactory
import org.scalatest.flatspec.AsyncFlatSpec
import play.api.libs.json.{JsValue, Json}
import play.api.libs.ws.WSClient
import play.api.libs.ws.ahc.AhcWSClient
import play.api.mvc
import play.api.mvc.{Request, RequestHeader}
import play.mvc.Http.Request
import utils.email_notification.service.EmailNotificationService
import utils.testapp.TestAppExecutionContext
import utils.uuid.services.SrUuidService
import utils.{Helpers, SRLogger}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}

class CredentialsAuthServiceSpec extends AsyncFlatSpec with AsyncMockFactory {
  implicit lazy val system: ActorSystem = TestAppExecutionContext.actorSystem
  implicit lazy val ec: ExecutionContext = system.dispatcher
  implicit lazy val wSClient: AhcWSClient = TestAppExecutionContext.wsClient
  given Logger: SRLogger = new SRLogger("CredentialsAuthServiceSpec")
  val mockSRGoogleRecaptchaServices = mock[SRGoogleRecaptchaServices]
  val mockAccountDAO = mock[AccountDAO]
  val mockEmailNotificationService = mock[EmailNotificationService]
  val mockOTPService = mock[AccountOTPService]
  val authUtils = mock[AuthUtils]
  val cacheService = mock[CacheService]
  val userRedisKeyService = mock[UserRedisKeyService]
  val spamMonitorService = mock[SpamMonitorService]
  val accountService = mock[AccountService]
  val freeEmailDomainListService = mock[FreeEmailDomainListService]
  val rolePermissionDataDAOV2 = mock[RolePermissionDataDAOV2]
  val commonAuthService = mock[CommonAuthService]
  val accountDAO = mock[AccountDAO]
  val srUuidService = mock[SrUuidService]
  val resetUserCacheUtil = mock[ResetUserCacheUtil]

  val credentialsAuthService = new CredentialsAuthService(
    sRGoogleRecaptchaServices = mockSRGoogleRecaptchaServices,
    //accountDAO = mockAccountDAO,
    emailNotificationService = mockEmailNotificationService,
    accountOTPService = mockOTPService,
    authUtils =authUtils,
    cacheService = cacheService,
    ec = ec,
    wsClient = wSClient,
//    userRedisKeyService = userRedisKeyService,
    spamMonitorService = spamMonitorService,
    accountService = accountService,
//    freeEmailDomainListService = freeEmailDomainListService,
    commonAuthService = commonAuthService,
    accountDAO = accountDAO,
    srUuidService = srUuidService,
    resetUserCacheUtil = resetUserCacheUtil
  )

  val data = UpdateAccountProfileDuringOnboarding(
    first_name = "",
    last_name = "",
    org_name = None,
    onboarding_phone_number = Some("+************")
  )

  val profile = AccountProfileInfo(
    first_name = "Animesh",
    last_name = "Kumar",
    company = Some("AK"),
    timezone = None,
    country_code = None,
    mobile_country_code = None,
    mobile_number = None,
    onboarding_phone_number= None,
    twofa_enabled = false,
    has_gauthenticator = false,
    weekly_report_emails = None,
    scheduled_for_deletion_at = None
  )

  val orgCountData: OrgCountData = OrgCountDataFixture.orgCountData_default

  val orgPlan = OrgPlanFixture.orgPlanFixture

  val orgSettings = OrgSettings(
    enable_ab_testing = false,
    disable_force_send = false,
    bulk_sender = false,
    allow_2fa = false,
    show_2fa_setting = false,
    enforce_2fa = false,
    allow_native_crm_integration = false,
      agency_option_allow_changing = false,
      agency_option_show = false
  )

  val accountMetadata = AccountMetadata(
    // account_ui_version = None,
    is_profile_onboarding_done = None
  )

  val orgMetadata = OrgMetadataFixture.orgMetadataFixture2



  val org = OrganizationWithCurrentData(

    id = 1,
    name = "AK",
    owner_account_id = 2,

    counts = orgCountData,
    settings = orgSettings,
    plan = orgPlan,

    is_agency = true,
    trial_ends_at = DateTime.now().plusDays(100),
    error = None,
    error_code = None,
    paused_till = None,
    errors = Seq(),
    warnings = Seq(),
    via_referral = false,
    org_metadata = orgMetadata
  )


  val accountAdmin = Account(
    id = AccountUuid("account_uuid"),
    internal_id = 2,
    email = "<EMAIL>",
    email_verification_code = None,
    email_verification_code_created_at = None,
    created_at = DateTime.now().minusDays(1000),
    first_name = Some("Animesh"),
    last_name = Some("Kumar"),
    company = Some("AK"),
    timezone = None,
    profile = profile,
    org_role = Some(OrganizationRole.OWNER),
    teams = Seq(),
    account_type =  AccountType.AGENCY,
    org = org,
    active = true,
    email_notification_summary = "dSFA",
    account_metadata = accountMetadata,
    email_verified = false,
    signupType = None,
    account_access = AccountAccess(
      inbox_access = false
    ),
    calendar_account_data = None
  )

  val SQLError = new Throwable("SQLERROR")


  val signInForm =  SignInForm(
    email = "<EMAIL>",
    password = "SomePassword",
    rememberMe = false,
    g_response = None,
    login_challenge = Some("RANDOM_STRING")
  )

  val utmData = UtmData(
    utm_source = None,
    utm_medium = None,
    utm_campaign = None,
    utm_term = None,
    utm_content = None,
    utm_device = None,
    utm_agid = None,
    utm_match_type = None,
    utm_placement = None,
    utm_network = None
  )


//  "authenticate" should "" in {
//
//    (accountService.findByEmail (_: String) (_: SRLogger))
//      .expects("<EMAIL>")
//      .returning(Failure(SQLError))
//    val result = credentialsAuthService.authenticate(
//      data = signInForm,
//      request = mvc.Request[JsValue](body = Json.obj(), rh = RequestHeader)
//    )
//
//    assert(result == Left(ErrorWhileAuthenticating.ErrorWhileFindingAccount(SQLError)))
//
//  }

  "forgotPasswordSendOTP" should "No response sent error" in {

    (mockSRGoogleRecaptchaServices.verifyCaptcha)
      .expects("0.0.0.0", None, None)
      .returning(Future.successful(Left(ReCaptchaError.NoGResponseSentError)))

    credentialsAuthService.forgotPasswordSendOTP(
      userIp = "0.0.0.0",
      g_response = None,
      email = "<EMAIL>"
    ).map{result =>

      assert(result == Left(ForgotPasswordSendOTPError.ReCaptchaValidationError(ReCaptchaError.NoGResponseSentError)))

    }
  }

  "forgotPasswordSendOTP" should "failed recaptcha" in {

    (mockSRGoogleRecaptchaServices.verifyCaptcha)
      .expects("0.0.0.0", Some("ABCD"), None)
      .returning(Future.successful(Right(RecaptchaResponse(showCaptcha = true, passedCaptcha = false))))

    credentialsAuthService.forgotPasswordSendOTP(
      userIp = "0.0.0.0",
      g_response = Some("ABCD"),
      email = "<EMAIL>"
    ).map{result =>

      assert(result == Left(ForgotPasswordSendOTPError.ReCaptchaFailed))

    }
  }

  "forgotPasswordSendOTP" should "No account found" in {

    (mockSRGoogleRecaptchaServices.verifyCaptcha)
      .expects("0.0.0.0", Some("ABCD"), None)
      .returning(Future.successful(Right(RecaptchaResponse(showCaptcha = false, passedCaptcha = true))))

    (accountService.findByEmailIgnoreCache (_: String) (_: SRLogger))
      .expects("<EMAIL>", *)
      .returning(Success(None))

    credentialsAuthService.forgotPasswordSendOTP(
      userIp = "0.0.0.0",
      g_response = Some("ABCD"),
      email = "<EMAIL>"
    ).map{result =>

      assert(result == Left(ForgotPasswordSendOTPError.NoAccountFound))

    }
  }

  "forgotPasswordSendOTP" should "Wrong signup type" in {

    (mockSRGoogleRecaptchaServices.verifyCaptcha)
      .expects("0.0.0.0", Some("ABCD"), None)
      .returning(Future.successful(Right(RecaptchaResponse(showCaptcha = false, passedCaptcha = true))))

    (accountService.findByEmailIgnoreCache (_: String) (_: SRLogger))
      .expects("<EMAIL>", *)
      .returning(Success(Some(accountAdmin)))

    credentialsAuthService.forgotPasswordSendOTP(
      userIp = "0.0.0.0",
      g_response = Some("ABCD"),
      email = "<EMAIL>"
    ).map{result =>

      assert(result == Left(ForgotPasswordSendOTPError.WrongSignupType))

    }
  }

  "forgotPasswordSendOTP" should "Didnt genrate the otp" in {

    val account = accountAdmin.copy(
      signupType = Some(SignupType.Password)
    )
    (mockSRGoogleRecaptchaServices.verifyCaptcha)
      .expects("0.0.0.0", Some("ABCD"), None)
      .returning(Future.successful(Right(RecaptchaResponse(showCaptcha = false, passedCaptcha = true))))

    (accountService.findByEmailIgnoreCache (_: String) (_: SRLogger))
      .expects("<EMAIL>", *)
      .returning(Success(Some(account)))

    (mockOTPService.getOrCreateOTPAndCount (_:Option[String], _: OTPType) (using _:SRLogger))
      .expects(account.email_verification_code, OTPType.ForgotPassword(account.internal_id), *)
      .returning(Left(GetOTPError.OTPNotFoundInDB))

    credentialsAuthService.forgotPasswordSendOTP(
      userIp = "0.0.0.0",
      g_response = Some("ABCD"),
      email = "<EMAIL>"
    ).map{result =>

      assert(result == Left(ForgotPasswordSendOTPError.ErrorWhileGettingOtp(GetOTPError.OTPNotFoundInDB)))

    }
  }

  "forgotPasswordSendOTP" should "failed to send email" in { //passes when run alone, does not always pass when run all together

    val account = accountAdmin.copy(
      signupType = Some(SignupType.Password)
    )
    val body = views.html.emails.forgotPassword(
      otp = "111111",
      first_name = account.first_name.getOrElse("there")
    ).toString()

    (mockSRGoogleRecaptchaServices.verifyCaptcha)
      .expects("0.0.0.0", Some("ABCD"), None)
      .returning(Future.successful(Right(RecaptchaResponse(showCaptcha = false, passedCaptcha = true))))

    (accountService.findByEmailIgnoreCache (_: String) (_: SRLogger))
      .expects("<EMAIL>", *)
      .returning(Success(Some(account)))

    (mockOTPService.getOrCreateOTPAndCount (_:Option[String], _: OTPType) (using _:SRLogger))
      .expects(account.email_verification_code, OTPType.ForgotPassword(account.internal_id), *)
      .returning(Right(
        OTPAndCount(
          "111111",
          1
        )
      ))



    (mockEmailNotificationService.sendMailFromAdmin (
      _: String, _: Option[String], _: String, _:String,
      _:Option[Boolean], _: Option[String], _:Option[String], _: Seq[String], _:Boolean
    )(_:WSClient, _: ExecutionContext, _:SRLogger))
      .expects(
        account.email,
        Some(Helpers.getAccountName(account)),
        s"Reset your ${CONSTANTS.APP_NAME} password",
        *, None, None, None, Seq(), true, *, *, *)
      .returning(Failure(SQLError))


    credentialsAuthService.forgotPasswordSendOTP(
      userIp = "0.0.0.0",
      g_response = Some("ABCD"),
      email = "<EMAIL>"
    ).map{result =>

      assert(result == Left(ForgotPasswordSendOTPError.EmailNotificationServiceError(SQLError)))

    }
  }

  "forgotPasswordSendOTP" should "success" in { //passes when run alone, does not always pass when run all together

    val account = accountAdmin.copy(
      signupType = Some(SignupType.Password)
    )
    (mockSRGoogleRecaptchaServices.verifyCaptcha)
      .expects("0.0.0.0", Some("ABCD"), None)
      .returning(Future.successful(Right(RecaptchaResponse(showCaptcha = false, passedCaptcha = true))))

    (accountService.findByEmailIgnoreCache (_: String) (_: SRLogger))
      .expects("<EMAIL>", *)
      .returning(Success(Some(account)))

    (mockOTPService.getOrCreateOTPAndCount (_:Option[String], _: OTPType) (using _:SRLogger))
      .expects(account.email_verification_code, OTPType.ForgotPassword(account.internal_id), *)
      .returning(Right(
        OTPAndCount(
          "111111",
          1
        )
      ))

    val body = views.html.emails.forgotPassword(
      otp = "111111",
      first_name = account.first_name.getOrElse("there")
    ).toString()

    (mockEmailNotificationService.sendMailFromAdmin (
      _: String, _: Option[String], _: String, _:String,
      _:Option[Boolean], _: Option[String], _:Option[String], _: Seq[String], _:Boolean
    )(_:WSClient, _: ExecutionContext, _:SRLogger))
      .expects(
        account.email,
        Some(Helpers.getAccountName(account)),
        s"Reset your ${CONSTANTS.APP_NAME} password",
        *, None, None, None, Seq(), true, *, *, *)
      .returning(Success({}))


    credentialsAuthService.forgotPasswordSendOTP(
      userIp = "0.0.0.0",
      g_response = Some("ABCD"),
      email = "<EMAIL>"
    ).map{result =>

      assert(result == Right(1))

    }
  }


  "updatePasswordWithOTP" should "No response sent error" in {

    (mockSRGoogleRecaptchaServices.verifyCaptcha)
      .expects("0.0.0.0", None, None)
      .returning(Future.successful(Left(ReCaptchaError.NoGResponseSentError)))

    credentialsAuthService.updatePasswordWithOTP(
      userIp = "0.0.0.0",
      g_response = None,
      email = "<EMAIL>",
      otp = "111111",
      password = "password"
    )(Logger).map{result =>

      assert(result == Left(UpdatePasswordWithOTPError.ReCaptchaValidationError(ReCaptchaError.NoGResponseSentError)))

    }
  }

  "updatePasswordWithOTP" should "failed recaptcha" in {

    (mockSRGoogleRecaptchaServices.verifyCaptcha)
      .expects("0.0.0.0", Some("ABCD"), None)
      .returning(Future.successful(Right(RecaptchaResponse(showCaptcha = true, passedCaptcha = false))))

    credentialsAuthService.updatePasswordWithOTP(
      userIp = "0.0.0.0",
      g_response = Some("ABCD"),
      email = "<EMAIL>",
      otp = "111111",
      password = "password"
    )(Logger).map{result =>

      assert(result == Left(UpdatePasswordWithOTPError.ReCaptchaFailed))

    }
  }

  "updatePasswordWithOTP" should "No account found" in {

    (mockSRGoogleRecaptchaServices.verifyCaptcha)
      .expects("0.0.0.0", Some("ABCD"), None)
      .returning(Future.successful(Right(RecaptchaResponse(showCaptcha = false, passedCaptcha = true))))

    (accountService.findByEmailIgnoreCache (_: String) (_: SRLogger))
      .expects("<EMAIL>", *)
      .returning(Success(None))

    credentialsAuthService.updatePasswordWithOTP(
      userIp = "0.0.0.0",
      g_response = Some("ABCD"),
      email = "<EMAIL>",
      otp = "111111",
      password = "password"
    )(Logger).map{result =>

      assert(result == Left(UpdatePasswordWithOTPError.NoAccountFound))

    }
  }

  "updatePasswordWithOTP" should "Wrong signup type" in {

    (mockSRGoogleRecaptchaServices.verifyCaptcha)
      .expects("0.0.0.0", Some("ABCD"), None)
      .returning(Future.successful(Right(RecaptchaResponse(showCaptcha = false, passedCaptcha = true))))

    (accountService.findByEmailIgnoreCache (_: String) (_: SRLogger))
      .expects("<EMAIL>", *)
      .returning(Success(Some(accountAdmin)))

    credentialsAuthService.updatePasswordWithOTP(
      userIp = "0.0.0.0",
      g_response = Some("ABCD"),
      email = "<EMAIL>",
      otp = "111111",
      password = "password"
    )(Logger).map{result =>

      assert(result == Left(UpdatePasswordWithOTPError.WrongSignupType))

    }
  }


  "updatePasswordWithOTP" should "Didnt verify the otp" in {

    val account = accountAdmin.copy(
      signupType = Some(SignupType.Password)
    )
    (mockSRGoogleRecaptchaServices.verifyCaptcha)
      .expects("0.0.0.0", Some("ABCD"), None)
      .returning(Future.successful(Right(RecaptchaResponse(showCaptcha = false, passedCaptcha = true))))

    (accountService.findByEmailIgnoreCache (_: String) (_: SRLogger))
      .expects("<EMAIL>", *)
      .returning(Success(Some(account)))

    (mockOTPService.getOTPFromDBAndVerifyOTP (_:Option[String], _: OTPType, _: String) (_:SRLogger))
      .expects(account.email_verification_code, OTPType.ForgotPassword(account.internal_id), "111111", *)
      .returning(Left(VerifyOTPError.OTPNotInDB))

    credentialsAuthService.updatePasswordWithOTP(
      userIp = "0.0.0.0",
      g_response = Some("ABCD"),
      email = "<EMAIL>",
      otp = "111111",
      password = "password"
    )(Logger).map{result =>

      assert(result == Left(UpdatePasswordWithOTPError.VerifyOTPFail(VerifyOTPError.OTPNotInDB)))

    }
  }

  "updatePasswordWithOTP" should "otp didnt pass" in {

    val account = accountAdmin.copy(
      signupType = Some(SignupType.Password)
    )
    (mockSRGoogleRecaptchaServices.verifyCaptcha)
      .expects("0.0.0.0", Some("ABCD"), None)
      .returning(Future.successful(Right(RecaptchaResponse(showCaptcha = false, passedCaptcha = true))))

    (accountService.findByEmailIgnoreCache (_: String) (_: SRLogger))
      .expects("<EMAIL>", *)
      .returning(Success(Some(account)))

    (mockOTPService.getOTPFromDBAndVerifyOTP  (_:Option[String], _: OTPType, _: String) (_:SRLogger))
      .expects(account.email_verification_code, OTPType.ForgotPassword(account.internal_id), "111111", *)
      .returning(Right(OTPPassAndCode(
        pass = false,
        code = "forgot_password___111111"
      )))

    credentialsAuthService.updatePasswordWithOTP(
      userIp = "0.0.0.0",
      g_response = Some("ABCD"),
      email = "<EMAIL>",
      otp = "111111",
      password = "password"
    )(Logger).map{result =>

      assert(result == Left(UpdatePasswordWithOTPError.OTPDidNotMatch))

    }
  }

  "updatePasswordWithOTP" should "Error while updating account" in {

    val account = accountAdmin.copy(
      signupType = Some(SignupType.Password)
    )
    (mockSRGoogleRecaptchaServices.verifyCaptcha)
      .expects("0.0.0.0", Some("ABCD"), None)
      .returning(Future.successful(Right(RecaptchaResponse(showCaptcha = false, passedCaptcha = true))))

    (accountService.findByEmailIgnoreCache (_: String) (_: SRLogger))
      .expects("<EMAIL>", *)
      .returning(Success(Some(account)))

    (mockOTPService.getOTPFromDBAndVerifyOTP  (_: Option[String], _: OTPType, _: String) (_:SRLogger))
      .expects(account.email_verification_code, OTPType.ForgotPassword(account.internal_id), "111111", *)
      .returning(Right(OTPPassAndCode(
        pass = true,
        code = "forgot_password___111111"
      )))

    (accountService.updatePasswordAndResetVerificationCode (_:AccountId,_:String,_:String)(_:SRLogger))
      .expects(AccountId(id = 2), "password", "forgot_password___111111",Logger) // FIXME VALUECLASS
      .returning(Failure(SQLError))

    credentialsAuthService.updatePasswordWithOTP(
      userIp = "0.0.0.0",
      g_response = Some("ABCD"),
      email = "<EMAIL>",
      otp = "111111",
      password = "password"
    )(Logger).map{result =>

      assert(result == Left(UpdatePasswordWithOTPError.SQLErrorWhileUpdatingPassword(SQLError)))

    }
  }

  "updatePasswordWithOTP" should "account not found after update" in {

    val account = accountAdmin.copy(
      signupType = Some(SignupType.Password)
    )
    (mockSRGoogleRecaptchaServices.verifyCaptcha)
      .expects("0.0.0.0", Some("ABCD"), None)
      .returning(Future.successful(Right(RecaptchaResponse(showCaptcha = false, passedCaptcha = true))))

    (accountService.findByEmailIgnoreCache (_: String) (_: SRLogger))
      .expects("<EMAIL>", *)
      .returning(Success(Some(account)))

    (mockOTPService.getOTPFromDBAndVerifyOTP  (_:Option[String], _: OTPType, _: String) (_:SRLogger))
      .expects(account.email_verification_code, OTPType.ForgotPassword(account.internal_id), "111111", *)
      .returning(Right(OTPPassAndCode(
        pass = true,
        code = "forgot_password___111111"
      )))

    (accountService.updatePasswordAndResetVerificationCode (_:AccountId,_:String,_:String)(_:SRLogger))
      .expects(AccountId(id = 2), "password", "forgot_password___111111",Logger) // FIXME VALUECLASS
      .returning(Success(None))

    credentialsAuthService.updatePasswordWithOTP(
      userIp = "0.0.0.0",
      g_response = Some("ABCD"),
      email = "<EMAIL>",
      otp = "111111",
      password = "password"
    )(Logger).map{result =>

      assert(result == Left(UpdatePasswordWithOTPError.NoAccountFound))

    }
  }

  "updatePasswordWithOTP" should "Error while deleting key" in {

    val account = accountAdmin.copy(
      signupType = Some(SignupType.Password)
    )
    (mockSRGoogleRecaptchaServices.verifyCaptcha)
      .expects("0.0.0.0", Some("ABCD"), None)
      .returning(Future.successful(Right(RecaptchaResponse(showCaptcha = false, passedCaptcha = true))))

    (accountService.findByEmailIgnoreCache (_: String) (_: SRLogger))
      .expects("<EMAIL>", *)
      .returning(Success(Some(account)))

    (mockOTPService.getOTPFromDBAndVerifyOTP  (_:Option[String], _: OTPType, _: String) (_:SRLogger))
      .expects(account.email_verification_code, OTPType.ForgotPassword(account.internal_id), "111111", *)
      .returning(Right(OTPPassAndCode(
        pass = true,
        code = "forgot_password___111111"
      )))

    (accountService.updatePasswordAndResetVerificationCode (_:AccountId,_:String,_:String)(_:SRLogger))
      .expects(AccountId(id = 2), "password", "forgot_password___111111",*) // FIXME VALUECLASS
      .returning(Success(Some(AccountId(id = 2)))) // FIXME VALUECLASS

    (mockOTPService.deleteKeyInRedis)
      .expects(OTPType.ForgotPassword(2))
      .returning(Failure(SQLError))

    credentialsAuthService.updatePasswordWithOTP(
      userIp = "0.0.0.0",
      g_response = Some("ABCD"),
      email = "<EMAIL>",
      otp = "111111",
      password = "password"
    )(Logger).map{result =>

      assert(result == Left(UpdatePasswordWithOTPError.ErrorWhileDeletingTheKey(SQLError)))

    }
  }

  "updatePasswordWithOTP" should "success" in {

    val account = accountAdmin.copy(
      signupType = Some(SignupType.Password)
    )
    (mockSRGoogleRecaptchaServices.verifyCaptcha)
      .expects("0.0.0.0", Some("ABCD"), None)
      .returning(Future.successful(Right(RecaptchaResponse(showCaptcha = false, passedCaptcha = true))))

    (accountService.findByEmailIgnoreCache (_: String) (_: SRLogger))
      .expects("<EMAIL>", *)
      .returning(Success(Some(account)))

    (mockOTPService.getOTPFromDBAndVerifyOTP  (_:Option[String], _: OTPType, _: String) (_:SRLogger))
      .expects(account.email_verification_code, OTPType.ForgotPassword(account.internal_id), "111111", *)
      .returning(Right(OTPPassAndCode(
        pass = true,
        code = "forgot_password___111111"
      )))

    (accountService.updatePasswordAndResetVerificationCode (_:AccountId,_:String,_:String)(_:SRLogger))
      .expects(AccountId(id = 2), "password", "forgot_password___111111",*) // FIXME VALUECLASS
      .returning(Success(Some(AccountId(id = 2)))) // FIXME VALUECLASS

    (mockOTPService.deleteKeyInRedis)
      .expects(OTPType.ForgotPassword(2))
      .returning(Success(1))

    credentialsAuthService.updatePasswordWithOTP(
      userIp = "0.0.0.0",
      g_response = Some("ABCD"),
      email = "<EMAIL>",
      otp = "111111",
      password = "password"
    )(Logger).map{result =>

      assert(result == Right(2))

    }
  }





  "resendVerificationEmail" should "No account found" in {

    (accountService.findByEmailIgnoreCache (_: String) (_: SRLogger))
      .expects("<EMAIL>", *)
      .returning(Success(None))

    val result = credentialsAuthService.resendVerificationEmail(
      email = "<EMAIL>"
    )

    assert(result == Left(ResendVerificationEmailError.NoAccountFound))
  }

  "resendVerificationEmail" should "Already verified" in {
    (accountService.findByEmailIgnoreCache (_: String) (_: SRLogger))
      .expects("<EMAIL>", *)
      .returning(Success(Some(accountAdmin.copy(email_verified = true))))

    val result = credentialsAuthService.resendVerificationEmail(
      email = "<EMAIL>"
    )

    assert(result == Right(0))


  }

  "resendVerificationEmail" should "GetOTPFail" in {

    val account = accountAdmin.copy(
      email_verified = false
    )
    (accountService.findByEmailIgnoreCache (_: String) (_: SRLogger))
      .expects("<EMAIL>", *)
      .returning(Success(Some(account)))

    (mockOTPService.getOrCreateOTPAndCount (_:Option[String], _: OTPType) (using _:SRLogger))
      .expects(account.email_verification_code, OTPType.VerifyEmail(account.internal_id), *)
      .returning(Left(GetOTPError.OTPNotFoundInDB))

    val result = credentialsAuthService.resendVerificationEmail(
      email = "<EMAIL>"
    )

    assert(result == Left(ResendVerificationEmailError.ErrorWhileGettingOtp(GetOTPError.OTPNotFoundInDB)))


  }

  "resendVerificationEmail" should "faild to send email" in {

    val account = accountAdmin.copy(
      email_verified = false
    )

    (accountService.findByEmailIgnoreCache (_: String) (_: SRLogger))
      .expects("<EMAIL>", *)
      .returning(Success(Some(account)))

    (mockOTPService.getOrCreateOTPAndCount (_:Option[String], _: OTPType) (using _:SRLogger))
      .expects(account.email_verification_code, OTPType.VerifyEmail(account.internal_id), *)
      .returning(Right(
        OTPAndCount(
          otp = "111111",
          count = 2
        )
      ))
    (mockEmailNotificationService.sendMailFromAdmin (
      _: String, _: Option[String], _: String, _:String,
      _:Option[Boolean], _: Option[String], _:Option[String], _: Seq[String], _:Boolean
    )(_:WSClient, _: ExecutionContext, _:SRLogger))
      .expects(
        account.email,
        account.first_name,
        s"Verify your SmartReach.io email",
        *, None, None, None, Seq(), true, *, *, *)
      .returning(Failure(SQLError))

    val result = credentialsAuthService.resendVerificationEmail(

      email = "<EMAIL>"
    )

    assert(result == Left(ResendVerificationEmailError.EmailNotificationServiceError(SQLError)))
  }

  "resendVerificationEmail" should "success to send email" in {

    val account = accountAdmin.copy(
      email_verified = false
    )

    (accountService.findByEmailIgnoreCache (_: String) (_: SRLogger))
      .expects("<EMAIL>", *)
      .returning(Success(Some(account)))

    (mockOTPService.getOrCreateOTPAndCount (_:Option[String], _: OTPType) (using _:SRLogger))
      .expects(account.email_verification_code, OTPType.VerifyEmail(account.internal_id), *)
      .returning(Right(
        OTPAndCount(
          otp = "111111",
          count = 2
        )
      ))
    (mockEmailNotificationService.sendMailFromAdmin (
      _: String, _: Option[String], _: String, _:String,
      _:Option[Boolean], _: Option[String], _:Option[String], _: Seq[String], _:Boolean
    )(_:WSClient, _: ExecutionContext, _: SRLogger))
      .expects(
        account.email,
        account.first_name,
        s"Verify your SmartReach.io email",
        *, None, None, None, Seq(), true, *, *, *)
      .returning(Success(()))

    val result = credentialsAuthService.resendVerificationEmail(
      email = "<EMAIL>"
    )

    assert(result == Right(2))

  }

  "verifyEmail" should "fail recaptcha" in {

    (mockSRGoogleRecaptchaServices.verifyCaptcha)
      .expects("0.0.0.0", None, None)
      .returning(Future.successful(Left(ReCaptchaError.NoGResponseSentError)))

    credentialsAuthService.verifyEmail(
      userIp = "0.0.0.0",
      g_response = None,
      email = "<EMAIL>",
      otp = "111111",
      loginChallenge = Some(LoginChallenge(challenge = "login_challenge"))
    ).map{result =>

      assert(result == Left(VerifyEmailError.ReCaptchaValidationError(ReCaptchaError.NoGResponseSentError)))

    }
  }
  "verifyEmail" should " recaptcha failed" in {

    (mockSRGoogleRecaptchaServices.verifyCaptcha)
      .expects("0.0.0.0", Some("ABCD"), None)
      .returning(Future.successful(Right(
        RecaptchaResponse(
          passedCaptcha = false,
          showCaptcha = true
        )
      )))

    credentialsAuthService.verifyEmail(
      userIp = "0.0.0.0",
      g_response = Some("ABCD"),
      email = "<EMAIL>",
      otp = "111111",
      loginChallenge = Some(LoginChallenge(challenge = "login_challenge"))
    ).map{result =>

      assert(result == Left(VerifyEmailError.ReCaptchaFailed))

    }
  }
  "verifyEmail" should "No Account found" in {

    (mockSRGoogleRecaptchaServices.verifyCaptcha)
      .expects("0.0.0.0", Some("ABCD"), None)
      .returning(Future.successful(Right(
        RecaptchaResponse(
          passedCaptcha = true,
          showCaptcha = false
        )
      )))

    (accountService.findByEmailIgnoreCache (_: String) (_: SRLogger))
      .expects("<EMAIL>", *)
      .returning(Success(None))

    credentialsAuthService.verifyEmail(
      userIp = "0.0.0.0",
      g_response = Some("ABCD"),
      email = "<EMAIL>",
      otp = "111111",
      loginChallenge = Some(LoginChallenge(challenge = "login_challenge"))

    ).map{result =>

      assert(result == Left(VerifyEmailError.NoAccountFound))

    }
  }

  "verifyEmail" should "verifying otp fail" in {

    (mockSRGoogleRecaptchaServices.verifyCaptcha)
      .expects("0.0.0.0", Some("ABCD"), None)
      .returning(Future.successful(Right(
        RecaptchaResponse(
          passedCaptcha = true,
          showCaptcha = false
        )
      )))

    (accountService.findByEmailIgnoreCache (_: String) (_: SRLogger))
      .expects("<EMAIL>", *)
      .returning(Success(Some(accountAdmin)))

    (mockOTPService.getOTPFromDBAndVerifyOTP  (_:Option[String], _: OTPType, _: String) (_:SRLogger))
      .expects(accountAdmin.email_verification_code, OTPType.VerifyEmail(accountAdmin.internal_id), "111112", *)
      .returning(Left(VerifyOTPError.OTPNotInDB))

    credentialsAuthService.verifyEmail(
      userIp = "0.0.0.0",
      g_response = Some("ABCD"),
      email = "<EMAIL>",
      otp = "111112",
      loginChallenge = Some(LoginChallenge(challenge = "login_challenge"))
    ).map{result =>

      assert(result == Left(VerifyEmailError.VerifyOTPFail(VerifyOTPError.OTPNotInDB)))

    }
  }

  "verifyEmail" should "wrong otp" in {

    (mockSRGoogleRecaptchaServices.verifyCaptcha)
      .expects("0.0.0.0", Some("ABCD"), None)
      .returning(Future.successful(Right(
        RecaptchaResponse(
          passedCaptcha = true,
          showCaptcha = false
        )
      )))

    (accountService.findByEmailIgnoreCache (_: String) (_: SRLogger))
      .expects("<EMAIL>", *)
      .returning(Success(Some(accountAdmin)))

    (mockOTPService.getOTPFromDBAndVerifyOTP  (_:Option[String], _: OTPType, _: String) (_:SRLogger))
      .expects(accountAdmin.email_verification_code, OTPType.VerifyEmail(accountAdmin.internal_id), "111112", *)
      .returning(Right(
        OTPPassAndCode(
          pass = false,
          code = "verify_email___111111"
        )
      ))

    credentialsAuthService.verifyEmail(
      userIp = "0.0.0.0",
      g_response = Some("ABCD"),
      email = "<EMAIL>",
      otp = "111112",
      loginChallenge = Some(LoginChallenge(challenge = "login_challenge"))
    ).map{result =>

      assert(result == Left(VerifyEmailError.OTPDidNotMatch))

    }
  }


  "verifyEmail" should "Failed while verifying email" in {

    (mockSRGoogleRecaptchaServices.verifyCaptcha)
      .expects("0.0.0.0", Some("ABCD"), None)
      .returning(Future.successful(Right(
        RecaptchaResponse(
          passedCaptcha = true,
          showCaptcha = false
        )
      )))

    (accountService.findByEmailIgnoreCache (_: String) (_: SRLogger))
      .expects("<EMAIL>", *)
      .returning(Success(Some(accountAdmin)))

    (mockOTPService.getOTPFromDBAndVerifyOTP  (_:Option[String], _: OTPType, _: String) (_:SRLogger))
      .expects(accountAdmin.email_verification_code, OTPType.VerifyEmail(accountAdmin.internal_id), "111111", *)
      .returning(Right(
        OTPPassAndCode(
          pass = true,
          code = "verify_email___111111"
        )
      ))
    (accountService.updateAccountEmailStatusIsVerified (_:AccountId,_:String)(_:SRLogger))
      .expects(AccountId(id = 2), "verify_email___111111",Logger) // FIXME VALUECLASS
      .returning(Failure(SQLError))

    credentialsAuthService.verifyEmail(
      userIp = "0.0.0.0",
      g_response = Some("ABCD"),
      email = "<EMAIL>",
      otp = "111111",
      loginChallenge = Some(LoginChallenge(challenge = "login_challenge"))
    ).map{result =>

      assert(result == Left(VerifyEmailError.SQLErrorWhileVerifyingAccount(SQLError)))

    }
  }

  "verifyEmail" should "didnt find after updating" in {

    (mockSRGoogleRecaptchaServices.verifyCaptcha)
      .expects("0.0.0.0", Some("ABCD"), None)
      .returning(Future.successful(Right(
        RecaptchaResponse(
          passedCaptcha = true,
          showCaptcha = false
        )
      )))

    (accountService.findByEmailIgnoreCache (_: String) (_: SRLogger))
      .expects("<EMAIL>", *)
      .returning(Success(Some(accountAdmin)))

    (mockOTPService.getOTPFromDBAndVerifyOTP  (_:Option[String], _: OTPType, _: String) (_:SRLogger))
      .expects(accountAdmin.email_verification_code, OTPType.VerifyEmail(accountAdmin.internal_id), "111111", *)
      .returning(Right(
        OTPPassAndCode(
          pass = true,
          code = "verify_email___111111"
        )
      ))
    (accountService.updateAccountEmailStatusIsVerified (_:AccountId,_:String)(_:SRLogger))
      .expects(AccountId(id = 2), "verify_email___111111",*) // FIXME VALUECLASS
      .returning(Success(None))

    credentialsAuthService.verifyEmail(
      userIp = "0.0.0.0",
      g_response = Some("ABCD"),
      email = "<EMAIL>",
      otp = "111111",
      loginChallenge = Some(LoginChallenge(challenge = "login_challenge"))
    ).map{result =>

      assert(result == Left(VerifyEmailError.NoneResponseAfterVerifyingEmail))

    }
  }

  "verifyEmail" should "Failed to delete key" in {

    (mockSRGoogleRecaptchaServices.verifyCaptcha)
      .expects("0.0.0.0", Some("ABCD"), None)
      .returning(Future.successful(Right(
        RecaptchaResponse(
          passedCaptcha = true,
          showCaptcha = false
        )
      )))

    (accountService.findByEmailIgnoreCache (_: String) (_: SRLogger))
      .expects("<EMAIL>", *)
      .returning(Success(Some(accountAdmin)))

    (mockOTPService.getOTPFromDBAndVerifyOTP  (_:Option[String], _: OTPType, _: String) (_:SRLogger))
      .expects(accountAdmin.email_verification_code, OTPType.VerifyEmail(accountAdmin.internal_id), "111111", *)
      .returning(Right(
        OTPPassAndCode(
          pass = true,
          code = "verify_email___111111"
        )
      ))
    (accountService.updateAccountEmailStatusIsVerified (_:AccountId,_:String)(_:SRLogger))
      .expects(AccountId(id = 2), "verify_email___111111",*) // FIXME VALUECLASS
      .returning(Success(Some(AccountId(id = 2))))

    (mockOTPService.deleteKeyInRedis)
      .expects(OTPType.VerifyEmail(2))
      .returning(Failure(SQLError))
    credentialsAuthService.verifyEmail(
      userIp = "0.0.0.0",
      g_response = Some("ABCD"),
      email = "<EMAIL>",
      otp = "111111",
      loginChallenge = Some(LoginChallenge(challenge = "login_challenge"))
    ).map{result =>

      assert(result == Left(VerifyEmailError.ErrorWhileDeletingTheKey(SQLError)))

    }
  }

  "verifyEmail" should "success" in {

    (mockSRGoogleRecaptchaServices.verifyCaptcha)
      .expects("0.0.0.0", Some("ABCD"), None)
      .returning(Future.successful(Right(
        RecaptchaResponse(
          passedCaptcha = true,
          showCaptcha = false
        )
      )))


    (accountService.findByEmailIgnoreCache (_: String) (_: SRLogger))
      .expects("<EMAIL>", *)
      .returning(Success(Some(accountAdmin)))

    (mockOTPService.getOTPFromDBAndVerifyOTP  (_:Option[String], _: OTPType, _: String) (_:SRLogger))
      .expects(accountAdmin.email_verification_code, OTPType.VerifyEmail(accountAdmin.internal_id), "111111", *)
      .returning(Right(
        OTPPassAndCode(
          pass = true,
          code = "verify_email___111111"
        )
      ))
    (accountService.updateAccountEmailStatusIsVerified (_:AccountId,_:String)(_:SRLogger))
      .expects(AccountId(id = 2), "verify_email___111111",*) // FIXME VALUECLASS
      .returning(Success(Some(AccountId(id = 2))))

    (mockOTPService.deleteKeyInRedis)
      .expects(OTPType.VerifyEmail(2))
      .returning(Success(1))

    (commonAuthService.acceptLogin (_:String,_:String,_:Boolean,_:Long)(_:WSClient,_: ExecutionContext,_:SRLogger))
      .expects("login_challenge","2", true, AppConfigCommonAuth.remember_for_duration_in_seconds.toLong,*,*,*)
      .returning(Future.successful("redirect_to"))

    credentialsAuthService.verifyEmail(
      userIp = "0.0.0.0",
      g_response = Some("ABCD"),
      email = "<EMAIL>",
      otp = "111111",
      loginChallenge = Some(LoginChallenge(challenge = "login_challenge"))
    ).map{result =>


      assert(result == Right(AccountWithRedirectUri(accountAdmin,s"redirect_to")))

    }
  }


  val newAccount = SignupViaPasswordForm(
    email = "<EMAIL>",
    password = "SomePassword1234@",
    invite_code = Some("SomeInviteCode"),
    timezone = Some("SomeTimezone"),
    country_code = "IN",
    g_response = Some("SomeResponse"),
    login_challenge = Some("login_challenge")

  )

  "signup" should "Recaptcha verification failed" in {

    (mockSRGoogleRecaptchaServices.verifyCaptcha)
      .expects("0.0.0.0", Some("SomeResponse"), None)
      .returning(Future.successful(Left(ReCaptchaError.NoGResponseSentError)))
    (spamMonitorService.checkIfEmailIsAllowedToSignUp (_: String)(using _: SRLogger, _: ExecutionContext, _: WSClient))
      .expects("<EMAIL>", *, *, *)
      .returning(Future(Right(SendEmailStatusData.AllowedData())))

    credentialsAuthService.signup(
      userIp = "0.0.0.0",
      newAccount: SignupViaPasswordForm,
      utmData = utmData,
      rolePermissionDataDAOV2 = rolePermissionDataDAOV2,
      cookieString = "SomeCookieString",
      queryStr = "SomeQueryString",
      admitadCreateAt = None,
      firstPromoterCreateAt = None
    ).map{result =>

      assert(result == Left(FailedAtSignup.ReCaptchaValidationError(ReCaptchaError.NoGResponseSentError)))
    }
  }

  "signup" should "Recaptcha verification success but didn't pass" in {

    (mockSRGoogleRecaptchaServices.verifyCaptcha)
      .expects("0.0.0.0", Some("SomeResponse"), None)
      .returning(Future.successful(Right(RecaptchaResponse(showCaptcha = true, passedCaptcha = false))))

    (spamMonitorService.checkIfEmailIsAllowedToSignUp (_: String)(using _: SRLogger, _: ExecutionContext, _: WSClient))
      .expects("<EMAIL>", *, *, *)
      .returning(Future(Right(SendEmailStatusData.AllowedData())))

    credentialsAuthService.signup(
      userIp = "0.0.0.0",
      newAccount: SignupViaPasswordForm,
      utmData = utmData,
      rolePermissionDataDAOV2 = rolePermissionDataDAOV2,
      cookieString = "SomeCookieString",
      queryStr = "SomeQueryString",
      admitadCreateAt = None,
      firstPromoterCreateAt = None
    ).map{result =>

      assert(result == Left(FailedAtSignup.ReCaptchaFailed))
    }
  }

  "signup" should "Failed to check if domain free" in {

    (mockSRGoogleRecaptchaServices.verifyCaptcha)
      .expects("0.0.0.0", Some("SomeResponse"), None)
      .returning(Future.successful(Right(RecaptchaResponse(showCaptcha = false, passedCaptcha = true))))

    (spamMonitorService.checkIfEmailIsAllowedToSignUp (_: String)(using _: SRLogger, _: ExecutionContext, _: WSClient))
      .expects("<EMAIL>", *, *, *)
      .returning(Future(Left(CheckIfEmailIsAllowedToSignUpError.FailedToCheckIfDomainFree(SQLError))))



    credentialsAuthService.signup(
      userIp = "0.0.0.0",
      newAccount: SignupViaPasswordForm,
      utmData = utmData,
      rolePermissionDataDAOV2 = rolePermissionDataDAOV2,
      cookieString = "SomeCookieString",
      queryStr = "SomeQueryString",
      admitadCreateAt = None,
      firstPromoterCreateAt = None
    ).map{result =>

      assert(result == Left(FailedAtSignup.SpamCheckFailed(CheckIfEmailIsAllowedToSignUpError.FailedToCheckIfDomainFree(SQLError))))
    }
  }


  "signup" should "Domain Empty" in {

    (mockSRGoogleRecaptchaServices.verifyCaptcha)
      .expects("0.0.0.0", Some("SomeResponse"), None)
      .returning(Future.successful(Right(RecaptchaResponse(showCaptcha = false, passedCaptcha = true))))

    (accountService.getInviteByEmail)
      .expects("animesh")
      .returning(None)


    (spamMonitorService.checkIfEmailIsAllowedToSignUp (_: String)(using _: SRLogger, _: ExecutionContext, _: WSClient))
      .expects("animesh", *, *, *)
      .returning(Future(Right(SendEmailStatusData.AllowedData())))

    credentialsAuthService.signup(
      userIp = "0.0.0.0",
      newAccount = newAccount.copy(email = "animesh"),
      utmData = utmData,
      rolePermissionDataDAOV2 = rolePermissionDataDAOV2,
      cookieString = "SomeCookieString",
      queryStr = "SomeQueryString",
      admitadCreateAt = None,
      firstPromoterCreateAt = None
    ).map{result =>

      assert(result == Left(FailedAtSignup.EmptyDomain))
    }
  }

  val inviteMember = InviteMemberBasic(
    team_id = 3,
    team_name = "team_name",
    email = "<EMAIL>",
    inviter_id = 4,
    inviter_name = "inviter_name"
  )

  "signup" should "Empty invite code" in {

    (mockSRGoogleRecaptchaServices.verifyCaptcha)
      .expects("0.0.0.0", Some("SomeResponse"), None)
      .returning(Future.successful(Right(RecaptchaResponse(showCaptcha = false, passedCaptcha = true))))

    (accountService.getInviteByEmail)
      .expects("<EMAIL>")
      .returning(Some(inviteMember))

    (spamMonitorService.checkIfEmailIsAllowedToSignUp (_: String)(using _: SRLogger, _: ExecutionContext, _: WSClient))
      .expects("<EMAIL>", *, *, *)
      .returning(Future(Right(SendEmailStatusData.AllowedData())))

    credentialsAuthService.signup(
      userIp = "0.0.0.0",
      newAccount = newAccount.copy(invite_code = None),
      utmData = utmData,
      rolePermissionDataDAOV2 = rolePermissionDataDAOV2,
      cookieString = "SomeCookieString",
      queryStr = "SomeQueryString",
      admitadCreateAt = None,
      firstPromoterCreateAt = None
    ).map{result =>

      assert(result == Left(FailedAtSignup.NoInviteCode(Some(inviteMember))))
    }
  }


  "signup" should "weak password" in {

    (mockSRGoogleRecaptchaServices.verifyCaptcha)
      .expects("0.0.0.0", Some("SomeResponse"), None)
      .returning(Future.successful(Right(RecaptchaResponse(showCaptcha = false, passedCaptcha = true))))

    (accountService.getInviteByEmail)
      .expects("<EMAIL>")
      .returning(None)
    (spamMonitorService.checkIfEmailIsAllowedToSignUp (_: String)(using _: SRLogger, _: ExecutionContext, _: WSClient))
      .expects("<EMAIL>", *, *, *)
      .returning(Future(Right(SendEmailStatusData.AllowedData())))

    credentialsAuthService.signup(
      userIp = "0.0.0.0",
      newAccount = newAccount.copy(password = "weakpassword"),
      utmData = utmData,
      rolePermissionDataDAOV2 = rolePermissionDataDAOV2,
      cookieString = "SomeCookieString",
      queryStr = "SomeQueryString",
      admitadCreateAt = None,
      firstPromoterCreateAt = None
    ).map{result =>

      assert(result == Left(FailedAtSignup.PasswordFailed))
    }
  }

  "signup" should "Account Already exists" in {

    (mockSRGoogleRecaptchaServices.verifyCaptcha)
      .expects("0.0.0.0", Some("SomeResponse"), None)
      .returning(Future.successful(Right(RecaptchaResponse(showCaptcha = false, passedCaptcha = true))))

    (accountService.getInviteByEmail)
      .expects("<EMAIL>")
      .returning(None)
    (spamMonitorService.checkIfEmailIsAllowedToSignUp (_: String)(using _: SRLogger, _: ExecutionContext, _: WSClient))
      .expects("<EMAIL>", *, *, *)
      .returning(Future(Right(SendEmailStatusData.AllowedData())))

    (accountService.findByEmailIgnoreCache (_: String) (_: SRLogger))
      .expects("<EMAIL>", *)
      .returning(Success(Some(accountAdmin)))

    credentialsAuthService.signup(
      userIp = "0.0.0.0",
      newAccount = newAccount,
      utmData = utmData,
      rolePermissionDataDAOV2 = rolePermissionDataDAOV2,
      cookieString = "SomeCookieString",
      queryStr = "SomeQueryString",
      admitadCreateAt = None,
      firstPromoterCreateAt = None
    ).map{result =>

      assert(result == Left(FailedAtSignup.AccountAlreadyThere))
    }
  }

  "signup" should "Failed while creating account" in {

    (mockSRGoogleRecaptchaServices.verifyCaptcha)
      .expects("0.0.0.0", Some("SomeResponse"), None)
      .returning(Future.successful(Right(RecaptchaResponse(showCaptcha = false, passedCaptcha = true))))

    (accountService.getInviteByEmail)
      .expects("<EMAIL>")
      .returning(None)
    (spamMonitorService.checkIfEmailIsAllowedToSignUp (_: String)(using _: SRLogger, _: ExecutionContext, _: WSClient))
      .expects("<EMAIL>", *, *, *)
      .returning(Future(Right(SendEmailStatusData.AllowedData())))

    (accountService.findByEmailIgnoreCache (_: String) (_: SRLogger))
      .expects("<EMAIL>", *)
      .returning(Success(None))

    (accountService.checkOrgTrackingSubdomainKey)
      .expects("smartreach")
      .returning(0)

    (accountService.create (_: SRLogger, _: AccountCreateForm, _: String, _: UtmData, _: SignupType, _: SendEmailStatusData, _: Option[DateTime], _: Option[DateTime])(using _:RolePermissionDataDAOV2))
      .expects(
        *, AccountCreateForm(
          email = newAccount.email,
          password = Some(newAccount.password),
          first_name = "",
          last_name = "",
          company = "smartreach",
          invite_code = newAccount.invite_code,
          timezone = newAccount.timezone,
          country_code = newAccount.country_code,
          g_response = newAccount.g_response,
          rs_code_used = None
        ), "smartreach", utmData, SignupType.Password, SendEmailStatusData.AllowedData(), None, None, *)
      .returning(Failure(SQLError))

    credentialsAuthService.signup(
      userIp = "0.0.0.0",
      newAccount = newAccount,
      utmData = utmData,
      rolePermissionDataDAOV2 = rolePermissionDataDAOV2,
      cookieString = "SomeCookieString",
      queryStr = "SomeQueryString",
      admitadCreateAt = None,
      firstPromoterCreateAt = None
    ).map{result =>

      assert(result == Left(FailedAtSignup.FailedToCreateAccount(SQLError)))
    }
  }

  "signup" should "account created but not found" in {

    (mockSRGoogleRecaptchaServices.verifyCaptcha)
      .expects("0.0.0.0", Some("SomeResponse"), None)
      .returning(Future.successful(Right(RecaptchaResponse(showCaptcha = false, passedCaptcha = true))))

    (accountService.getInviteByEmail)
      .expects("<EMAIL>")
      .returning(None)
    (spamMonitorService.checkIfEmailIsAllowedToSignUp (_: String)(using _: SRLogger, _: ExecutionContext, _: WSClient))
      .expects("<EMAIL>", *, *, *)
      .returning(Future(Right(SendEmailStatusData.AllowedData())))

    (accountService.findByEmailIgnoreCache (_: String) (_: SRLogger))
      .expects("<EMAIL>", *)
      .returning(Success(None))

    (accountService.checkOrgTrackingSubdomainKey)
      .expects("smartreach")
      .returning(0)

    (accountService.create (_: SRLogger, _: AccountCreateForm, _: String, _: UtmData, _: SignupType, _: SendEmailStatusData, _: Option[DateTime], _: Option[DateTime])(using _:RolePermissionDataDAOV2))
      .expects(
        *, AccountCreateForm(
          email = newAccount.email,
          password = Some(newAccount.password),
          first_name = "",
          last_name = "",
          company = "smartreach",
          invite_code = newAccount.invite_code,
          timezone = newAccount.timezone,
          country_code = newAccount.country_code,
          g_response = newAccount.g_response,
          rs_code_used = None
        ), "smartreach", utmData, SignupType.Password, SendEmailStatusData.AllowedData(), None, None, *)
      .returning(Success(None))

    credentialsAuthService.signup(
      userIp = "0.0.0.0",
      newAccount = newAccount,
      utmData = utmData,
      rolePermissionDataDAOV2 = rolePermissionDataDAOV2,
      cookieString = "SomeCookieString",
      queryStr = "SomeQueryString",
      admitadCreateAt = None,
      firstPromoterCreateAt = None
    ).map{result =>

      assert(result == Left(FailedAtSignup.NoAccountFoundAfterCreating))
    }
  }

  "signup" should "account created" in {

    (mockSRGoogleRecaptchaServices.verifyCaptcha)
      .expects("0.0.0.0", Some("SomeResponse"), None)
      .returning(Future.successful(Right(RecaptchaResponse(showCaptcha = false, passedCaptcha = true))))

    (accountService.getInviteByEmail)
      .expects("<EMAIL>")
      .returning(None)
    (spamMonitorService.checkIfEmailIsAllowedToSignUp (_: String)(using _: SRLogger, _: ExecutionContext, _: WSClient))
      .expects("<EMAIL>", *, *, *)
      .returning(Future(Right(SendEmailStatusData.AllowedData())))

    (accountService.findByEmailIgnoreCache (_: String) (_: SRLogger))
      .expects("<EMAIL>", *)
      .returning(Success(None))

    (accountService.checkOrgTrackingSubdomainKey)
      .expects("smartreach")
      .returning(1)

    (accountService.create (_: SRLogger, _: AccountCreateForm, _: String, _: UtmData, _: SignupType, _: SendEmailStatusData, _: Option[DateTime], _: Option[DateTime])(using _:RolePermissionDataDAOV2))
      .expects(
        *, AccountCreateForm(
          email = newAccount.email,
          password = Some(newAccount.password),
          first_name = "",
          last_name = "",
          company = "smartreach",
          invite_code = newAccount.invite_code,
          timezone = newAccount.timezone,
          country_code = newAccount.country_code,
          g_response = newAccount.g_response,
          rs_code_used = None
        ), *, utmData, SignupType.Password, SendEmailStatusData.AllowedData(), None, None, *)
      .returning(Success(Some(accountAdmin)))

    //On staging this test was failing as isDevSetup was true there .
    val isDevSetup = AppConfig.isDevDomain
    val isInvitedUser = newAccount.invite_code.isDefined

    if (!isDevSetup && isInvitedUser) {
      (mockEmailNotificationService.sendMailFromAdmin(_: String, _: Option[String], _: String, _: String, _: Option[Boolean], _: Option[String], _: Option[String], _: Seq[String], _: Boolean)(_: WSClient, _: ExecutionContext, _: SRLogger))
        .expects("<EMAIL>", Some("Heaplabs Team"), "SR Invited User: <EMAIL> : AK", *, None, None, None, List(), true, *, *, *)
        .returning(Success(()))
    }

    credentialsAuthService.signup(
      userIp = "0.0.0.0",
      newAccount = newAccount,
      utmData = utmData,
      rolePermissionDataDAOV2 = rolePermissionDataDAOV2,
      cookieString = "SomeCookieString",
      queryStr = "SomeQueryString",
      admitadCreateAt = None,
      firstPromoterCreateAt = None
    ).map{result =>

      assert(result == Right(accountAdmin))
    }
  }


}