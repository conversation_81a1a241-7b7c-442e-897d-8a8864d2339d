package app.api.accounts

import api.accounts.{AccountAndTeamsAccountReferenceUpdateData, AccountReferenceUpdateData, MigrationDAO, TeamId}
import api.accounts.models.{AccountId, OrgId}
import org.scalamock.scalatest.MockFactory
import org.scalatest.funspec.AnyFunSpec
import scalikejdbc.interpolation.SQLSyntax
import scalikejdbc.{scalikejdbcSQLInterpolationImplicitDef, sqls}
import utils.SRLogger

class MigrationDAOSpec extends AnyFunSpec with MockFactory {

  implicit lazy val logger: SRLogger = new SRLogger("MigrationDAOSpec")

  val org_id: OrgId = OrgId(id = 5)

  val account_id: AccountId = AccountId(id = 9)

  val owner_account_id: AccountId = AccountId(id = 8)

  val team_id: TeamId = TeamId(id = 23)

  val tableName_prospects = "prospects"

  val tableName_webhook_events = "webhook_events"

  val tableName_webhooks = "webhooks"

  val colName_account_id = "account_id"

  val colName_added_by = "added_by"

  val delSqlWhereClause_account = sqls" tabl.${SQLSyntax.createUnsafely(colName_account_id)} = $account_id "

  val delSqlWhereClause_team = sqls" tabl.team_id = $team_id "

  val idsForDeleting: Seq[Long] = Seq(1, 4, 5)

  val ta_id = 8

  val owner_ta_id = 3

  val delSqlWhereClause_teams_account = sqls" tabl.team_id = ${team_id.id} AND tabl.account_id = ${account_id.id} "

  val maxRowsToDeleteAtOnce = 1000

  val idColName_id = "id"

  val idColName_webhook_id = "webhook_id"

  val accountReferenceUpdateData = AccountReferenceUpdateData(
    delete_account_id = account_id,
    org_owner_id = owner_account_id,
    tableName = tableName_prospects,
    idColName = idColName_id,
    orgId = org_id
  )

  val accountAndTeamsAccountReferenceUpdateData = AccountAndTeamsAccountReferenceUpdateData(
    account_id_col_name = colName_account_id,
    delete_account_id = account_id,
    org_owner_id = owner_account_id,
    teamId = team_id,
    owner_ta_id = owner_ta_id,
    tableName = tableName_webhooks,
    idColName = idColName_id,
    orgId = org_id,
    delete_account_name = "Animesh Kumar"
  )

  describe("Test getTaIdAndTeamIdByAccountIdQuery") {

    it("should return a valid query to fetch TaIdAndTeamId by AccountId") {

      val expected =
        s"""|
            |        SELECT
            |          ta.team_id,
            |          org.owner_account_id,
            |          ta_owner.id
            |        FROM
            |          teams_accounts AS ta
            |          JOIN accounts AS acc ON ta.account_id = acc.id
            |          JOIN organizations AS org ON org.id = acc.org_id
            |	         JOIN teams_accounts AS ta_owner ON ta_owner.account_id = org.owner_account_id AND ta_owner.team_id = ta.team_id
            |        WHERE
            |          ta.account_id = ?
            |          AND acc.org_id = ?;
            |      """.stripMargin

      val qry_str = MigrationDAO.getOwnerInfoListQuery(
        accountId = account_id,
        orgId = org_id
      ).statement

      val rhs = expected.split(s"\\s+").reduce(
        (a1, a2) => {
          a1 + " " + a2
        }
      )

      val lhs = qry_str.split("\\s+").reduce(
        (s1, s2) => {
          s1 + " " + s2
        }
      )

      assert(lhs == rhs)
    }
  }

  describe("Test deleteFromTableQuery") {

    it("should return a valid query to delete entry from table") {

      val expected =
        s"""
           |        DELETE FROM prospects tabl
           |        WHERE
           |           tabl.team_id = ?
           |          AND tabl.id IN (?, ?, ?)
          """.stripMargin

      val qry_str = MigrationDAO.deleteFromTableQueryForId(
        idColName = idColName_id,
        tableName = tableName_prospects,
        delSqlWhereClause = delSqlWhereClause_team,
        idsForDeleting = idsForDeleting
      ).statement

      val rhs = expected.split(s"\\s+").reduce(
        (a1, a2) => {
          a1 + " " + a2
        }
      )

      val lhs = qry_str.split("\\s+").reduce(
        (s1, s2) => {
          s1 + " " + s2
        }
      )

      assert(lhs == rhs)
    }

    it("should return a valid query to delete entry from table with id col name") {

      val expected =
        s"""
           |        DELETE FROM webhook_events tabl
           |        WHERE
           |           tabl.team_id = ?
           |          AND tabl.webhook_id IN (?, ?, ?)
          """.stripMargin

      val qry_str = MigrationDAO.deleteFromTableQueryForId(
        idColName = idColName_webhook_id,
        tableName = tableName_webhook_events,
        delSqlWhereClause = delSqlWhereClause_team,
        idsForDeleting = idsForDeleting
      ).statement

      val rhs = expected.split(s"\\s+").reduce(
        (a1, a2) => {
          a1 + " " + a2
        }
      )

      val lhs = qry_str.split("\\s+").reduce(
        (s1, s2) => {
          s1 + " " + s2
        }
      )

      assert(lhs == rhs)
    }
  }

  describe("Test updateDataForTableWithOnlyAccountIdQuery") {

    it("should return a valid query to update account_id") {

      val expected =
        s"""
           |        UPDATE
           |          prospects AS tabl
           |        SET
           |          added_by = ?
           |        FROM
           |          accounts AS acc
           |        WHERE
           |          tabl.added_by = acc.id
           |          AND acc.org_id = ?
           |          AND tabl.added_by = ?
           |          AND tabl.id IN (?, ?, ?);
          """.stripMargin

      val qry_str = MigrationDAO.updateDataForTableWithOnlyAccountIdQueryForIds(
        updateAccountData = accountReferenceUpdateData,
        accountIdColName = colName_added_by,
        idsForUpdating = idsForDeleting,
      ).statement

      val rhs = expected.split(s"\\s+").reduce(
        (a1, a2) => {
          a1 + " " + a2
        }
      )

      val lhs = qry_str.split("\\s+").reduce(
        (s1, s2) => {
          s1 + " " + s2
        }
      )

      assert(lhs == rhs)
    }
  }

  describe("Test updateDataForTableWithAccountIdAndTaIdQuery") {

    it("should return a valid query to update account_id and ta_id") {

      val expected =
        s"""
           |         UPDATE
           |           webhooks
           |         SET
           |           ta_id = ?,
           |           account_id = ?
           |         WHERE
           |           team_id = ?
           |           AND (
           |           account_id = ? OR
           |           ta_id IN (select id from teams_accounts where account_id = ? and team_id = ?)
           |           )
           |           AND webhooks.id IN (?, ?, ?);
          """.stripMargin

      val qry_str = MigrationDAO.updateDataForTableWithAccountIdAndTaIdQueryForId(
        updateAccountAndTeamsAccountData = accountAndTeamsAccountReferenceUpdateData,
        idsForUpdating = idsForDeleting,
      ).statement

      val rhs = expected.split(s"\\s+").reduce(
        (a1, a2) => {
          a1 + " " + a2
        }
      )

      val lhs = qry_str.split("\\s+").reduce(
        (s1, s2) => {
          s1 + " " + s2
        }
      )

      assert(lhs == rhs)
    }
  }


  describe("Test getIdsForDeletingQuery") {

    it("should return a valid query to fetch ids") {

      val expected =
        s"""
           |         SELECT
           |            tabl.id
           |         FROM
           |           prospects tabl
           |         WHERE
           |            tabl.team_id = ?
           |            AND tabl.account_id = ?
           |            AND tabl.id > ?
           |            order by tabl.id asc
           |         LIMIT ?
         """.stripMargin

      val qry_str = MigrationDAO.getIdsForManipulationQuery(
        idColName = idColName_id,
        tableName = tableName_prospects,
        maxRowsToManipulateAtOnce = maxRowsToDeleteAtOnce,
        sqlWhereClause = delSqlWhereClause_teams_account,
        accountIdColName = Some(colName_account_id),
        orgId = org_id,
        idColIsUuid = false,
        idToDeleteFrom = 0
      ).statement

      val rhs = expected.split(s"\\s+").reduce(
        (a1, a2) => {
          a1 + " " + a2
        }
      )

      val lhs = qry_str.split("\\s+").reduce(
        (s1, s2) => {
          s1 + " " + s2
        }
      )

      assert(lhs == rhs)
    }

    it("should return a valid query to fetch ids with different id col name") {

      val expected =
        s"""
           |         SELECT
           |            tabl.webhook_id
           |         FROM
           |           webhook_events tabl
           |         WHERE
           |            tabl.account_id = ?
           |         LIMIT ?
         """.stripMargin

      val qry_str = MigrationDAO.getIdsForManipulationQuery(
        idColName = idColName_webhook_id,
        tableName = tableName_webhook_events,
        maxRowsToManipulateAtOnce = maxRowsToDeleteAtOnce,
        sqlWhereClause = delSqlWhereClause_account,
        accountIdColName = Some(colName_account_id),
        orgId = org_id,
        idColIsUuid = true,
        idToDeleteFrom = 0
      ).statement

      val rhs = expected.split(s"\\s+").reduce(
        (a1, a2) => {
          a1 + " " + a2
        }
      )

      val lhs = qry_str.split("\\s+").reduce(
        (s1, s2) => {
          s1 + " " + s2
        }
      )

      assert(lhs == rhs)
    }

  }

  describe("Test getAccountEmailQuery") {

    it("should return a valid query to fetch account email") {

      val expected =
        s"""|
            |      SELECT
            |        email
            |      FROM
            |        accounts
            |      WHERE
            |        id = ?
            |        AND org_id = ?
          """.stripMargin

      val qry_str = MigrationDAO.getAccountEmailQuery(
        accountId = account_id,
        orgId = org_id
      ).statement

      val rhs = expected.split(s"\\s+").reduce(
        (a1, a2) => {
          a1 + " " + a2
        }
      )

      val lhs = qry_str.split("\\s+").reduce(
        (s1, s2) => {
          s1 + " " + s2
        }
      )

      assert(lhs == rhs)
    }
  }

  describe("Test getTeamNameQuery") {

    it("should return a valid query to fetch team name") {

      val expected =
        s"""|
            |       SELECT
            |         name
            |       FROM
            |         teams
            |       WHERE
            |         id = ?
            |         AND org_id = ?
          """.stripMargin

      val qry_str = MigrationDAO.getTeamNameQuery(
        teamId = team_id,
        orgId = org_id
      ).statement

      val rhs = expected.split(s"\\s+").reduce(
        (a1, a2) => {
          a1 + " " + a2
        }
      )

      val lhs = qry_str.split("\\s+").reduce(
        (s1, s2) => {
          s1 + " " + s2
        }
      )

      assert(lhs == rhs)
    }
  }


}
