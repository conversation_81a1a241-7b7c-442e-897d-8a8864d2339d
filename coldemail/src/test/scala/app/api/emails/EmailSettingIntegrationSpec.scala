package app.api.emails

import org.apache.pekko.actor.ActorSystem
import org.apache.pekko.stream.Materializer
import api.emails.models.{EmailSettingApiError, EmailSettingUuid}
import api.{AppConfig, ErrorType}
import api.emails.EmailSettingApiResult
import db_test_spec.api.accounts.fixtures.NewAccountAndEmailSettingData
import db_test_spec.api.{DbTestingBeforeAllAndAfterAll, InitialData}
import org.scalamock.scalatest.AsyncMockFactory
import org.scalatest.ParallelTestExecution
import org.scalatest.funspec.AsyncFunSpec
import play.api.libs.json.{JsError, JsSuccess, JsValue}
import play.api.libs.ws.ahc.AhcWSClient
import utils.SRLogger

import scala.concurrent.Future
import play.api.test.FakeRequest
import play.api.test.Helpers._

class EmailSettingIntegrationSpec extends DbTestingBeforeAllAndAfterAll {

  given logger: SRLogger = new SRLogger("EmailSettingIntegrationSpec")
  
  describe("Integration tests for EmailSetting apis"){

    describe("EmailSettingController.findAll") {

      it("should return response in expected format for success for campaignId not defined") {
        val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
        val sendingUrl: String = s"/api/v3/email_settings?tid=${initialData.head_team_id}"

        val request = FakeRequest(play.api.test.Helpers.GET, sendingUrl)
          .withHeaders("X-API-KEY" -> initialData.teamUserLevelKey,
            "Content-Type" -> "application/json")

        val final_result = play.api.test.Helpers.route(testApi, request).get

        val status: Int = play.api.test.Helpers.status(final_result)
        val json: JsValue = play.api.test.Helpers.contentAsJson(final_result)

        final_result.map(res => {
            if (status == 200) {
              val sentResponseEmailSetting = (json \ "email_settings").as[List[JsValue]]
              val headEmailSetting = sentResponseEmailSetting.head
              val id = headEmailSetting \ "id"
              val owner_id = headEmailSetting \ "owner_id"
              val service_provider = headEmailSetting \ "service_provider"
              val first_name = headEmailSetting \ "first_name"
              val last_name = headEmailSetting \ "last_name"
              val email = headEmailSetting \ "email"
              val cc_emails = headEmailSetting \ "cc_emails"
              val bcc_emails = headEmailSetting \ "bcc_emails"
              val created_at = headEmailSetting \ "created_at"
              val min_delay_seconds = headEmailSetting \ "min_delay_seconds"
              val max_delay_seconds = headEmailSetting \ "max_delay_seconds"

              assert(id.isDefined)
              assert(owner_id.isDefined)
              assert(service_provider.isDefined)
              assert(first_name.isDefined)
              assert(last_name.isDefined)
              assert(email.isDefined)
              assert(cc_emails.isDefined)
              assert(bcc_emails.isDefined)
              assert(created_at.isDefined)
              assert(min_delay_seconds.isDefined)
              assert(max_delay_seconds.isDefined)

            } else {
              assert(false)
            }
          })
      }

      it("should return response in expected format for user error for campaignId defined") {
        val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
        val sendingUrl: String = s"/api/v3/email_settings?campaign_id=46&tid=80"

        val request = FakeRequest(play.api.test.Helpers.GET, sendingUrl)
          .withHeaders("X-API-KEY" -> initialData.teamUserLevelKey,
            "Content-Type" -> "application/json")

        val final_result = play.api.test.Helpers.route(testApi, request).get

        val status: Int = play.api.test.Helpers.status(final_result)
        val json: JsValue = play.api.test.Helpers.contentAsJson(final_result)

        final_result.map(res => {
            if (status == 403) {
              val sentResponseData = json
              val errorType = sentResponseData \ "error_type"
              val message = sentResponseData \ "message"

              assert(errorType.isDefined)
              assert(message.isDefined)

            } else {
              assert(false)
            }
          })
      }
    }
  }

}
