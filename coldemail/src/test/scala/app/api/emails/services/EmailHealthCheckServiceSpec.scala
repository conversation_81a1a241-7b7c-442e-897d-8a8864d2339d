package app.api.emails.services

import api.emails.EmailMessageTracked
import api.emails.models.EmailReplyType
import api.emails.services.EmailHealthCheckService
import api.prospects.models.ProspectCategory
import io.smartreach.esp.api.emails.IEmailAddress
import org.joda.time.DateTime
import org.scalatest.funspec.AsyncFunSpec
import play.api.libs.json.Json
import utils.email.EmailReplyStatus
import utils.email.services.InternalTrackingNote

class EmailHealthCheckServiceSpec extends AsyncFunSpec {

  private val emailMessage = EmailMessageTracked(
    inbox_email_setting_id = 23,
    from = IEmailAddress(name = None, email = "<EMAIL>"),
    to_emails = Seq(IEmailAddress(name = None, email = "<EMAIL>")),
    subject = "Some subject",
    body = "Some body",
    base_body = "Some base body",
    text_body = "Some text body",
    references_header = None,
    campaign_id = None,
    step_id = None,
    prospect_id_in_campaign = None,
    prospect_account_id_in_campaign = None,
    campaign_name = None,
    step_name = None,
    received_at = DateTime.now(),
    recorded_at = DateTime.now(),
    sr_inbox_read = false,
    original_inbox_folder = None,
    email_status = EmailReplyStatus(
      replyType = EmailReplyType.NOT_CATEGORIZED,
      isReplied = false,
      bouncedData = None,
      isUnsubscribeRequest = false,
      isAutoReply = false,
      isOutOfOfficeReply = false,
      isInvalidEmail = false,
      isForwarded = false,
    ),
    message_id = "some-message-id",
    full_headers = Json.obj(),
    scheduled_manually = true,
    reply_to = None,
    email_thread_id = None,
    gmail_msg_id = None,
    gmail_thread_id = None,
    outlook_msg_id = None,
    outlook_conversation_id = None,
    outlook_response_json = None,
    cc_emails = Seq(),
    in_reply_to_header = None,
    team_id = 23,
    account_id = 435,
    internal_tracking_note = InternalTrackingNote.EXISTING_INREPLYTO,
    tempThreadId = None,
  )

  describe("Test extractDkimSelectorFromSignature") {

    it("should return None if dkim selector not present") {

      val emailMessageWithNoDkimSelector = emailMessage.copy(
        full_headers = Json.obj(
          "DKIM-Signature" -> "v=1; a=rsa-sha256;  c=relaxed/simple; h= content-type:mime-version:subject:message-id:to:from:date; "
        )
      )

      val dkimSelectorValue = EmailHealthCheckService.extractDkimSelectorFromSignature(
        emailMessage = emailMessageWithNoDkimSelector
      )

      assert(dkimSelectorValue.isEmpty)

    }

    it("should extract dkim selected even if there is no space after s=") {

      val emailMessageWithDkimSelectorNoSpace = emailMessage.copy(
        full_headers = Json.obj(
          "DKIM-Signature" -> "v=1; a=rsa-sha256; s=selector1; "
        )
      )

      val dkimSelectorValue = EmailHealthCheckService.extractDkimSelectorFromSignature(
        emailMessage = emailMessageWithDkimSelectorNoSpace
      )

      assert(dkimSelectorValue.contains("selector1"))

    }

    it("should extract dkim selected even if there is space after s=") {

      val emailMessageWithDkimSelectorSingleSpace = emailMessage.copy(
        full_headers = Json.obj(
          "DKIM-Signature" -> "v=1; a=rsa-sha256; c=relaxed/simple; d=co-plan.com; h= content-type:mime-version:subject:message-id:to:from:date; s= dkim; t=**********; bh=W7ZodVeWV2zkdP/5QsrXUZ5W+LvSQZVOkELuPtHhR"
        )
      )

      val dkimSelectorValue = EmailHealthCheckService.extractDkimSelectorFromSignature(
        emailMessage = emailMessageWithDkimSelectorSingleSpace
      )

      assert(dkimSelectorValue.contains("dkim"))

    }

    it("should extract dkim selected even if there are multiple spaces after s=") {

      val emailMessageWithDkimSelectorMultipleSpaces = emailMessage.copy(
        full_headers = Json.obj(
          "DKIM-Signature" -> "v=1; a=rsa-sha256; s=    default; t=**********; bh=W7ZodVeWV2zkdP/5QsrXUZ5W+LvSQZVOkELuPtHhR"
        )
      )

      val dkimSelectorValue = EmailHealthCheckService.extractDkimSelectorFromSignature(
        emailMessage = emailMessageWithDkimSelectorMultipleSpaces
      )

      assert(dkimSelectorValue.contains("default"))

    }

  }

}
