package app.api.emails.services

import org.apache.pekko.actor.ActorSystem
import api.accounts.email.models.EmailServiceProvider
import api.accounts.models.{AccountId, OrgId}
import api.accounts.{AccountDAO, AccountService, AccountUuid, ReplyHandling, TeamId}
import io.sr.billing_common.models.PlanType
import api.emails.{EmailSetting, EmailSettingDAO}
import api.emails.models.EmailSettingUuid
import api.emails.services.OldEmailSyncService
import api.team.TeamUuid
import api.team_inbox.dao.EmailTeamInboxDAO
import api.team_inbox.model.{InitialEmailSyncData, TeamInbox}
import io.smartreach.esp.api.emails.EmailSettingId
import org.joda.time.DateTime
import org.scalamock.scalatest.AsyncMockFactory
import org.scalatest.funspec.AsyncFunSpec
import play.api.libs.ws.WSClient
import play.api.libs.ws.ahc.AhcWSClient
import utils.SRLogger
import utils.cache_utils.model.CampaignUseStatusForEmailSetting
import utils.cronjobs.email_setting_deletion.model.EmailSettingStatus
import utils.email.EmailService
import utils.email_notification.service.EmailNotificationService
import utils.mq.email.TeamInboxForSync
import utils.testapp.TestAppExecutionContext
import utils.uuid.SrUuidUtils

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}

class OldEmailSyncServiceSpec extends AsyncFunSpec with AsyncMockFactory {
  implicit lazy val system: ActorSystem = TestAppExecutionContext.actorSystem
  implicit lazy val wSClient: AhcWSClient = TestAppExecutionContext.wsClient
  implicit lazy val actorContext: ExecutionContext = system.dispatcher

  given Logger: SRLogger = new SRLogger("Unit test")

  val emailSettingDAO = mock[EmailSettingDAO]
  val emailService = mock[EmailService]
  val teamInboxDAO = mock[EmailTeamInboxDAO]
  val emailNotificationService = mock[EmailNotificationService]
  val accountService = mock[AccountService]
  val srUuidUtils = mock[SrUuidUtils]


  val ERROR = new Throwable("ERROR")

  val oldEmailSyncService = new OldEmailSyncService(
    srUuidUtils = srUuidUtils,
    emailSettingDAO = emailSettingDAO,
    emailService = emailService,
    teamInboxDAO = teamInboxDAO,
    emailNotificationService = emailNotificationService,
    accountService = accountService
  )

  val today = DateTime.now()

  val initial_sync_data = InitialEmailSyncData(
    initial_sync_completed = false,
    initial_sync_completed_at = None,
    initial_sync_requested_till = Some(today),
    total_emails_in_inbox_while_starting_sync = 5000,
    total_emails_synced_till_now = 0,
    initial_sync_synced_completed_from = today,
    error_while_initial_sync = None,
    error_while_initial_sync_at = None,
    initial_sync_next_attempt_at = None,
    in_queue_for_initial_syncing = false,
    pushed_to_queue_for_initial_syncing_at = None,
    total_consecutive_error_attempts = 0,
    initial_sync_total_consecutive_empty_cycles = 0,
    initial_sync_completed_trial = false,
    initial_sync_completed_trial_at = None
  )
  val teamInbox = TeamInbox(
    id = 1,
    email_setting_id = Some(2),
    linkedin_setting_id = None,
    name = "animesh",
    email = "<EMAIL>",
    created_at = Some(today),
    initial_sync_required = true,
    initial_sync_data = Some(initial_sync_data)
  )

  val msg = TeamInboxForSync(
    team_inbox_id = teamInbox.id,
    email_setting_id = teamInbox.email_setting_id.get,
    initial_sync_data = initial_sync_data,
    fromDate = today.minusDays(7),
    tillDate = today,
    inbox_name = "animesh",
    org_plan_type = PlanType.PAID,
    created_at = today
  )
  val emailSetting = EmailSetting(
    id = Some(EmailSettingId(emailSettingId = 123)),
    org_id = OrgId(id = 123567),
    owner_id = AccountId(id = 47),
    team_id = TeamId(id = 789),
    message_id_suffix = "emailMessageIdSuffix",
    uuid = Some(EmailSettingUuid("test_uuid")),
    owner_uuid = AccountUuid("owner_uuid"),
    team_uuid = TeamUuid("team_uuid"),
    email = "<EMAIL>",
    email_address_host = "email_address_host",

    service_provider = EmailServiceProvider.OTHER,
      domain_provider = None,
    via_gmail_smtp = None,

    owner_name = "Ownername dummy",
    sender_name = "Test Sender Name",
    first_name = "John",
    last_name = "Doe",

    cc_emails = None,
    bcc_emails = None,

    smtp_username = Some("<EMAIL>"),
    smtp_password = Some("thisispassword"),
    smtp_host = Some("this is the smtp host"),
    smtp_port = Some(12345),

    imap_username = Some("<EMAIL>"),
    imap_password = Some("thisisimappassword"),
    imap_host = Some("imap.host.com"),
    imap_port = Some(993),

    oauth2_access_token = None,
    oauth2_refresh_token = None,
    oauth2_token_type = None,
    oauth2_token_expires_in = None,
    oauth2_access_token_expires_at = None,

    // for mailgun
    email_domain = None,
    api_key = None,
    mailgun_region = None,

    quota_per_day = 3,

    reply_handling = ReplyHandling.PAUSE_SPECIFIC_CAMPAIGN_ON_REPLY,
    last_read_for_replies = None,
    latest_email_scheduled_at = None,

    error = None,
    error_reported_at = None,
    paused_till = None,

    signature = "MySignature",

    created_at = Some(DateTime.now()),

    current_prospect_sent_count_email = 3,

    default_tracking_domain = "company.com",
    default_unsubscribe_domain = "company.com",
    rep_tracking_host_id = 123,
    tracking_domain_host = None,
    custom_tracking_domain = None,
    custom_tracking_cname_value = None,
    custom_tracking_domain_is_verified = None,
    custom_tracking_domain_is_ssl_enabled = None,

    rep_mail_server_id = 123,
    rep_mail_server_public_ip = "0.0.0.0",
    rep_mail_server_host = "randomserverhost.com",
    rep_mail_server_reverse_dns = None,

    min_delay_seconds = 30,
    max_delay_seconds = 120,
      tag = None,
    campaign_use_status_for_email_setting = CampaignUseStatusForEmailSetting.IsNotAssignedToAnyCampaign,
    show_rms_ip_in_frontend = false

  )


  val team_id: TeamId = emailSetting.team_id
  val org_id: OrgId = emailSetting.org_id
  val auditlogReqId = "auditlogrequestid"

  describe("syncEmailForAGivenTimePeriod") {

    it("should fail because no email setting found") {

     (emailSettingDAO.find (_: Long, _: EmailSettingStatus))
      .expects(2, EmailSettingStatus.Active)
       .returning(None)
      oldEmailSyncService.syncEmailForAGivenTimePeriod(
        msg = msg
      ).map { result =>
        assert(false)
      }.recover{case e =>
        assert(e.getMessage.contains("email setting not found"))
      }
    }


    it("should fail because readEmail and updateTeamInboxAfterFailedSyncCycle failed") {

      (emailSettingDAO.find(_: Long, _: EmailSettingStatus))
        .expects(2, EmailSettingStatus.Active)
        .returning(Some(emailSetting))

      (srUuidUtils.generateBackgroundRequestLogId)
        .expects(team_id, org_id)
        .returning(auditlogReqId)

      (emailService.readEmail(_: EmailSetting, _: String, _: DateTime, _: Option[DateTime], _: Boolean)( _: ExecutionContext, _: SRLogger, _: WSClient))
        .expects(emailSetting, auditlogReqId, msg.fromDate, Some(msg.tillDate), false, *, *, *)
        .returning(Future.failed(ERROR))

      (teamInboxDAO.updateTeamInboxAfterFailedSyncCycle)
        .expects(msg.team_inbox_id, 1, *)
        .returning(Failure(ERROR))

      oldEmailSyncService.syncEmailForAGivenTimePeriod(
        msg = msg
      ).map { result =>
        assert(false)
      }.recover { case e =>
        assert(e == ERROR)
      }
    }

    it("should success because readEmail failed but updateTeamInboxAfterFailedSyncCycle passed") {

      (emailSettingDAO.find(_: Long, _: EmailSettingStatus))
        .expects(2, EmailSettingStatus.Active)
        .returning(Some(emailSetting))

      (srUuidUtils.generateBackgroundRequestLogId)
        .expects(team_id, org_id)
        .returning(auditlogReqId)

      (emailService.readEmail(_: EmailSetting, _: String, _: DateTime, _: Option[DateTime], _: Boolean)(_: ExecutionContext, _: SRLogger, _: WSClient))
        .expects(emailSetting, auditlogReqId, msg.fromDate, Some(msg.tillDate), false, *, *, *)
        .returning(Future.failed(ERROR))

      (teamInboxDAO.updateTeamInboxAfterFailedSyncCycle)
        .expects(msg.team_inbox_id, 1, *)
        .returning(Success(1))

      oldEmailSyncService.syncEmailForAGivenTimePeriod(
        msg = msg
      ).map { result =>
        assert(result == 1)
      }.recover { case e =>
        assert(false)
      }
    }


    it("should success because readEmail is a success but updateTeamInboxAfterSuccessSyncCyclePaidOrg failed and updateTeamInboxAfterFailedSyncCycle success") {

      (emailSettingDAO.find(_: Long, _:EmailSettingStatus))
        .expects(2, EmailSettingStatus.Active)
        .returning(Some(emailSetting))

      (srUuidUtils.generateBackgroundRequestLogId)
        .expects(team_id, org_id)
        .returning(auditlogReqId)

      (emailService.readEmail(_: EmailSetting, _: String, _: DateTime, _: Option[DateTime], _: Boolean)(_: ExecutionContext, _: SRLogger, _: WSClient))
        .expects(emailSetting, auditlogReqId, msg.fromDate, Some(msg.tillDate), false, *, *, *)
        .returning(Future.successful((emailSetting, 10, 100, Seq())))

      (teamInboxDAO.updateTeamInboxAfterSuccessSyncCyclePaidOrg)
        .expects(10, teamInbox.id, msg.fromDate, false, None, 0)
        .returning(Failure(ERROR))

      (teamInboxDAO.updateTeamInboxAfterFailedSyncCycle)
        .expects(msg.team_inbox_id, 1, *)
        .returning(Success(1))

      oldEmailSyncService.syncEmailForAGivenTimePeriod(
        msg = msg
      ).map { result =>
        assert(result == 1)
      }.recover { case e =>
        assert(false)
      }
    }

    it("should success because readEmail is a success and updateTeamInboxAfterSuccessSyncCyclePaidOrg Success") {

      (emailSettingDAO.find(_: Long, _: EmailSettingStatus))
        .expects(2, EmailSettingStatus.Active)
        .returning(Some(emailSetting))

      (srUuidUtils.generateBackgroundRequestLogId)
        .expects(team_id, org_id)
        .returning(auditlogReqId)

      (emailService.readEmail(_: EmailSetting, _: String, _: DateTime, _: Option[DateTime], _: Boolean)(_: ExecutionContext, _: SRLogger, _: WSClient))
        .expects(emailSetting, auditlogReqId, msg.fromDate, Some(msg.tillDate), false, *, *, *)
        .returning(Future.successful((emailSetting, 10, 100, Seq())))

      (teamInboxDAO.updateTeamInboxAfterSuccessSyncCyclePaidOrg)
        .expects(10, teamInbox.id, msg.fromDate, false, None, 0)
        .returning(Success(1))


      oldEmailSyncService.syncEmailForAGivenTimePeriod(
        msg = msg
      ).map { result =>
        assert(result == 1)
      }.recover { case e =>
        assert(false)
      }
    }

    it("should success because readEmail is a success and updateTeamInboxAfterSuccessSyncCycleTrialOrg Success") {

      (emailSettingDAO.find(_: Long, _: EmailSettingStatus))
        .expects(2, EmailSettingStatus.Active)
        .returning(Some(emailSetting))

      (srUuidUtils.generateBackgroundRequestLogId)
        .expects(team_id, org_id)
        .returning(auditlogReqId)

      (emailService.readEmail(_: EmailSetting, _: String, _: DateTime, _: Option[DateTime], _: Boolean)(_: ExecutionContext, _: SRLogger, _: WSClient))
        .expects(emailSetting, auditlogReqId, msg.fromDate, Some(msg.tillDate), false, *, *, *)
        .returning(Future.successful((emailSetting, 10, 100, Seq())))

      (teamInboxDAO.updateTeamInboxAfterSuccessSyncCycleTrialOrg)
        .expects(10, teamInbox.id, msg.fromDate, false, None, 0)
        .returning(Success(1))


      oldEmailSyncService.syncEmailForAGivenTimePeriod(
        msg = msg.copy(org_plan_type = PlanType.TRIAL)
      ).map { result =>
        assert(result == 1)
      }.recover { case e =>
        assert(false)
      }
    }

    it("should pass finish sync true to updateTeamInboxAfterSuccessSyncCycleTrialOrg only if from date is now - 15 days") {

      val msg2 =  msg.copy(org_plan_type = PlanType.TRIAL, fromDate = today.minusDays(15))

      (emailSettingDAO.find(_: Long, _: EmailSettingStatus))
        .expects(2, EmailSettingStatus.Active)
        .returning(Some(emailSetting))

      (srUuidUtils.generateBackgroundRequestLogId(_: TeamId, _: OrgId))
        .expects(team_id, org_id)
        .returning(auditlogReqId)

      (emailService.readEmail(_: EmailSetting, _: String, _: DateTime, _: Option[DateTime], _: Boolean)(_: ExecutionContext, _: SRLogger, _: WSClient))
        .expects(emailSetting, *, msg2.fromDate, Some(msg2.tillDate), false, *, *, *)
        .returning(Future.successful((emailSetting, 10, 100, Seq())))

      (teamInboxDAO.updateTeamInboxAfterSuccessSyncCycleTrialOrg)
        .expects(10, teamInbox.id, msg2.fromDate, true, *, 0)
        .returning(Success(1))


      oldEmailSyncService.syncEmailForAGivenTimePeriod(
        msg = msg2
      ).map { result =>
        assert(result == 1)
      }.recover { case e =>
        assert(false)
      }
    }

  }

}
