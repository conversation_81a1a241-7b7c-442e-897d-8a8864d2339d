package app.api.emails.services

import org.apache.pekko.actor.ActorSystem
import api.accounts.email.models.EmailServiceProvider
import api.accounts.{AccountDAO, AccountUuid, ReplyHandling, TeamId}
import api.accounts.models.{AccountId, OrgId}
import api.emails.models.{EmailSettingIntegrationLogsStage, EmailSettingUuid}
import api.emails.services.CreateEmailViaFormError.SQLException
import api.emails.{EmailSetting, EmailSettingCreateViaOAuth, EmailSettingDAO, EmailSettingForm}
import api.emails.services.{CreateEmailViaFormError, CreateEmailViaOauthError, EmailAccountService, EmailAccountTestService}
import api.free_email_domain.service.FreeEmailDomainListService
import api.rep_mail_servers.models.RepMailServer
import api.rep_mail_servers.services.SrMailServerService
import api.spammonitor.model.{EmailSendStatus, SendEmailStatusData}
import api.spammonitor.service.{EmailSendingStatusService, SpamMonitorService}
import api.team.TeamUuid
import io.smartreach.esp.api.emailApi.models.ESPMailgunRegion
import io.smartreach.esp.api.emails.EmailSettingId
import org.scalamock.scalatest.AsyncMockFactory
import org.scalatest.funspec.AsyncFunSpec
import org.scalatest.matchers.must.Matchers
import utils.SRLogger
import utils.emailvalidation.EmailValidationService
import org.joda.time.DateTime
import utils.sr_product_usage_data.services.SrUserFeatureUsageEventService
import play.api.libs.ws.WSClient
import play.api.libs.ws.ahc.AhcWSClient
import utils.cache_utils.model.CampaignUseStatusForEmailSetting
import utils.cache_utils.service.SrRedisSimpleLockServiceV2
import utils.email_notification.models.EmailNotificationLog
import utils.email_notification.service.EmailNotificationService
import utils.sr_product_usage_data.models.SrUserFeatureUsageEvent
import utils.testapp.TestAppExecutionContext
import utils.uuid.SrUuidUtils

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}

class EmailAccountServiceSpec extends AsyncFunSpec with AsyncMockFactory{

  implicit lazy val system: ActorSystem = TestAppExecutionContext.actorSystem
  implicit lazy val actorContext: ExecutionContext = system.dispatcher
  implicit lazy val wSClient: AhcWSClient = TestAppExecutionContext.wsClient
  given logger: SRLogger = new SRLogger(logRequestId = "EmailAccountServiceSpec:")

  val e= new Throwable("error occurred")
  val msgThrowable  = new Throwable("(account_id, email) = (1,<EMAIL>")
  val msgThrowableForForm = new Throwable("email_settings_team_id_email_key")

  val freeEmailDomainListService = mock[FreeEmailDomainListService]
  val emailSendingStatusService = mock[EmailSendingStatusService]
  val spamMonitorService = mock[SpamMonitorService]
  val emailSettingDAO = mock[EmailSettingDAO]
  val srUserFeatureUsageEventService =mock[SrUserFeatureUsageEventService]
  val emailAccountTestService = mock[EmailAccountTestService]
  val srRedisSimpleLockServiceV2 = mock[SrRedisSimpleLockServiceV2]
  val srUuidUtils = mock[SrUuidUtils]
  val emailAccountTestServiceMock = mock[EmailAccountTestService]
  val srMailServerService = mock[SrMailServerService]
  val accountDAO = mock[AccountDAO]

  val emailAccountService = new EmailAccountService(
    emailAccountTestService = emailAccountTestService,
    srUserFeatureUsageEventService = srUserFeatureUsageEventService,
    emailSettingDAO = emailSettingDAO,
    spamMonitorService = spamMonitorService,
    emailSendingStatusService = emailSendingStatusService,
    srRedisSimpleLockServiceV2 = srRedisSimpleLockServiceV2,
    freeEmailDomainListService = freeEmailDomainListService,
    srUuidUtils = srUuidUtils,
    srMailServerService = srMailServerService,
    accountDAO = accountDAO
  )


  val dummyEmail="<EMAIL>"
  val dummyFreeEmail="<EMAIL>"


  val dataEmailSettingCreateViaOAuth = EmailSettingCreateViaOAuth(
    email = "<EMAIL>",

    service_provider = EmailServiceProvider.GMAIL_ALIAS,

    sender_name = "Shubham Kudekar",
    first_name = "Shubham" ,
    last_name = "Kudekar",

    oauth2_access_token = "1234",
    oauth2_refresh_token = "5678",
    oauth2_token_type = "someType",
    oauth2_token_expires_in = 10,
    oauth2_access_token_expires_at = DateTime.now(),

    rep_google_api_key_id = Some(1),
    rep_mail_server_id = Some(1) ,
    rep_tracking_host_id = Some(1),
    gsuite_domain_install =  Some(true),
    domain_provider = None,
    domain = "domain",
    email_purchase_status = None,
    quota_per_day = 10,

    alias_parent_id = Some(1)

  )
  val emailSetting = EmailSetting(
    id = Some(EmailSettingId(emailSettingId = 1)),
    org_id = OrgId(id = 1),
    owner_id = AccountId(id = 1),
    team_id = TeamId(id = 1),
    uuid = Some(EmailSettingUuid("test_uuid")),
    owner_uuid = AccountUuid("owner_uuid"),
    team_uuid = TeamUuid("team_uuid"),
    message_id_suffix = "Hello",
    email = "<EMAIL>",
    email_address_host = "<EMAIL>",
    service_provider = EmailServiceProvider.GMAIL_API,
      domain_provider = None,  
    via_gmail_smtp = None,
    owner_name = "Shubham",
    sender_name = "Shubham Kudekar",
    first_name = "Shubham",
    last_name = "Kudekar",
    cc_emails = None,
    bcc_emails = None,
    smtp_username = None,
    smtp_password = None,
    smtp_host = None,
    smtp_port = None,
    imap_username = None,
    imap_password = None,
    imap_host = None,
    imap_port = None,
    oauth2_access_token = None,
    oauth2_refresh_token = None,
    oauth2_token_type = None,
    oauth2_token_expires_in = None,
    oauth2_access_token_expires_at = None,
    email_domain = None,
    api_key = None,
    mailgun_region = None,
    quota_per_day = 400,
    reply_handling = ReplyHandling.PAUSE_SPECIFIC_CAMPAIGN_ON_REPLY,
    last_read_for_replies = None,
    latest_email_scheduled_at = None,
    error = None,
    error_reported_at = None,
    paused_till = None,
    signature = "Shubham Kudekar",
    created_at = None,
    current_prospect_sent_count_email = 10,
    default_tracking_domain = "default_tracking_domain",
    default_unsubscribe_domain = "default_unsubscribe_domain",
    rep_tracking_host_id = 1,
    tracking_domain_host = None,
    custom_tracking_domain = None,
    custom_tracking_cname_value = None,
    custom_tracking_domain_is_verified = None,
    custom_tracking_domain_is_ssl_enabled = None,
    rep_mail_server_id = 1,
    rep_mail_server_public_ip = "rep_mail_server_public_ip",
    rep_mail_server_host = "rep_mail_server_host",
    rep_mail_server_reverse_dns = None,
    min_delay_seconds = 0,
    max_delay_seconds = 0,
      tag = None,
    campaign_use_status_for_email_setting = CampaignUseStatusForEmailSetting.IsNotAssignedToAnyCampaign,
    show_rms_ip_in_frontend = false

  )

  val sendEmailStatusData =SendEmailStatusData.AllowedData()

  val dataEmailSettingForm = EmailSettingForm(
    email= "<EMAIL>",

    service_provider = EmailServiceProvider.GMAIL_ALIAS,

    smtp_username = Some("Shubham"),
    smtp_password = Some("password"),
    smtp_host = Some("host"),
    smtp_port = Some(1),

    imap_username = Some("shubham"),
    imap_password = Some("password"),
    imap_host = Some("host"),
    imap_port = Some(1),

    // for mailgun
    email_domain = Some("gmail"),
    api_key = Some("apiKey"),
    mailgun_region = Some(ESPMailgunRegion.EU),

    quota_per_day = 10,
    min_delay_seconds = 1,
    max_delay_seconds = 2,


    can_send = true,
    can_receive = true,

    cc_emails = None,
    bcc_emails = None,

    first_name = "Shubham",
    last_name = "Kudekar",

    platform_email_id = None,
    email_tag = None
  )

  val emailSettingUuid = EmailSettingUuid(uuid = "email_setting_uuid")


  describe("createEmailViaOAuth"){

    it("should fail because domain is a free domain"){
      (freeEmailDomainListService.checkIfFreeEmailService(_:String)(_:SRLogger)).expects(dummyFreeEmail,*).returning(Success(true))
      (emailAccountTestService.insertEmailSettingIntegrationLogs(_:AccountId,_:OrgId,_:TeamId,_:Option[String],_:Option[EmailServiceProvider],_:Boolean,_:EmailSettingIntegrationLogsStage,_:Option[String])(using _:SRLogger))
        .expects(AccountId(1),OrgId(1),TeamId(1),Some("<EMAIL>"), Some(EmailServiceProvider.GMAIL_ALIAS), false,EmailSettingIntegrationLogsStage.OAuthPostRedirectError, Some("This email service is not allowed. It could be for various reasons including if the email service is a free or temporary service, or is blocked by our anti-spam filters."),*)
        .returning(Success(Some(1)))
      val result = emailAccountService.createEmailViaOauth(
        orgId = 1,
        accountId = 1,
        teamId = 1,
        taId = 1,
        data = dataEmailSettingCreateViaOAuth,
        owner_email = "<EMAIL>"
      )
      assert(result == Left(CreateEmailViaOauthError.FreeDomainUseError))

    }
    it("should fail with Sql exception"){
      (freeEmailDomainListService.checkIfFreeEmailService(_: String)(_: SRLogger)).expects(dummyFreeEmail, *).returning(Success(false))
      (() => srUuidUtils.generateEmailSettingUuid ()).expects().returning(emailSettingUuid.uuid)
      (emailSettingDAO.createViaOauth(_: Long,_:Long,_:Long,_:EmailSettingCreateViaOAuth,_:EmailSettingUuid)(using _:SRLogger))
        .expects(1,1,1,dataEmailSettingCreateViaOAuth, emailSettingUuid,*).returning(Failure(e))
      (emailAccountTestService.insertEmailSettingIntegrationLogs(_: AccountId, _: OrgId, _: TeamId, _: Option[String], _: Option[EmailServiceProvider], _: Boolean,_:EmailSettingIntegrationLogsStage, _: Option[String])(using _: SRLogger))
        .expects(AccountId(1), OrgId(1), TeamId(1), Some("<EMAIL>"), Some(EmailServiceProvider.GMAIL_ALIAS), false,EmailSettingIntegrationLogsStage.OAuthPostRedirectError, Some("error occurred"), *)
        .returning(Success(Some(1)))
      val result = emailAccountService.createEmailViaOauth(
        orgId = 1,
        accountId = 1,
        teamId = 1,
        taId = 1,
        data = dataEmailSettingCreateViaOAuth,
        owner_email = "<EMAIL>"
      )
      assert(result.isLeft)
    }

    it("should fail with Duplicate Email Error") {
      (freeEmailDomainListService.checkIfFreeEmailService(_: String)(_: SRLogger)).expects(dummyFreeEmail, *).returning(Success(false))
      (() => srUuidUtils.generateEmailSettingUuid ()).expects().returning(emailSettingUuid.uuid)
      (emailSettingDAO.createViaOauth(_: Long, _: Long, _: Long, _: EmailSettingCreateViaOAuth,_:EmailSettingUuid)(using _: SRLogger))
        .expects(1, 1, 1, dataEmailSettingCreateViaOAuth, emailSettingUuid, *).returning(Failure(msgThrowable))
      (emailAccountTestService.insertEmailSettingIntegrationLogs(_: AccountId, _: OrgId, _: TeamId, _: Option[String], _: Option[EmailServiceProvider], _: Boolean,_:EmailSettingIntegrationLogsStage, _: Option[String])(using _: SRLogger))
        .expects(AccountId(1), OrgId(1), TeamId(1), Some("<EMAIL>"), Some(EmailServiceProvider.GMAIL_ALIAS), false,EmailSettingIntegrationLogsStage.OAuthPostRedirectError, Some("Given email is already added to your account"), *)
        .returning(Success(Some(1)))
      val result = emailAccountService.createEmailViaOauth(
        orgId = 1,
        accountId = 1,
        teamId = 1,
        taId = 1,
        data = dataEmailSettingCreateViaOAuth,
        owner_email = "<EMAIL>"
      )
      assert(result==Left(CreateEmailViaOauthError.DuplicateEmailError))
    }
    it("should fail with email not added error ") {
      (freeEmailDomainListService.checkIfFreeEmailService(_: String)(_: SRLogger)).expects(dummyFreeEmail, *).returning(Success(false))
      (() => srUuidUtils.generateEmailSettingUuid ()).expects().returning(emailSettingUuid.uuid)
      (emailSettingDAO.createViaOauth(_: Long, _: Long, _: Long, _: EmailSettingCreateViaOAuth,_:EmailSettingUuid)(using _: SRLogger))
        .expects(1, 1, 1, dataEmailSettingCreateViaOAuth, emailSettingUuid, *).returning(Success(None))
      (emailAccountTestService.insertEmailSettingIntegrationLogs(_: AccountId, _: OrgId, _: TeamId, _: Option[String], _: Option[EmailServiceProvider], _: Boolean,_:EmailSettingIntegrationLogsStage, _: Option[String])(using _: SRLogger))
        .expects(AccountId(1), OrgId(1), TeamId(1), Some("<EMAIL>"), Some(EmailServiceProvider.GMAIL_ALIAS), false,EmailSettingIntegrationLogsStage.OAuthPostRedirectError, Some("Email not added error occurred"), *)
        .returning(Success(Some(1)))
      val result = emailAccountService.createEmailViaOauth(
        orgId = 1,
        accountId = 1,
        teamId = 1,
        taId = 1,
        data = dataEmailSettingCreateViaOAuth,
        owner_email = "<EMAIL>"
      )
      assert(result == Left(CreateEmailViaOauthError.EmailNotAddedError))
    }
    it("should fail with feature usage service error ") {
      (freeEmailDomainListService.checkIfFreeEmailService(_: String)(_: SRLogger)).expects(dummyFreeEmail, *).returning(Success(false))
      (() => srUuidUtils.generateEmailSettingUuid ()).expects().returning(emailSettingUuid.uuid)
      (emailSettingDAO.createViaOauth(_: Long, _: Long, _: Long, _: EmailSettingCreateViaOAuth,_:EmailSettingUuid)(using _: SRLogger))
        .expects(1, 1, 1, dataEmailSettingCreateViaOAuth, emailSettingUuid, *).returning(Success(Some(emailSetting)))
      (spamMonitorService.checkIfEmailIsAllowedToSend(_:String,_:Long)(using _:SRLogger,_:ExecutionContext,_:WSClient))
        .expects(*,*,logger,actorContext,wSClient).returning(Future{sendEmailStatusData})
      (srUserFeatureUsageEventService.addFeatureUsageEvent(_:Long,_:SrUserFeatureUsageEvent)).expects(1,*).returning(Failure(e))
      (emailAccountTestService.insertEmailSettingIntegrationLogs(_: AccountId, _: OrgId, _: TeamId, _: Option[String], _: Option[EmailServiceProvider], _: Boolean,_:EmailSettingIntegrationLogsStage, _: Option[String])(using _: SRLogger))
        .expects(AccountId(1), OrgId(1), TeamId(1), Some("<EMAIL>"), Some(EmailServiceProvider.GMAIL_ALIAS), false,EmailSettingIntegrationLogsStage.OAuthPostRedirectError, Some("Feature UsageServiceError error occurred"), *)
        .returning(Success(Some(1)))
      val result = emailAccountService.createEmailViaOauth(
        orgId = 1,
        accountId = 1,
        teamId = 1,
        taId = 1,
        data = dataEmailSettingCreateViaOAuth,
        owner_email = "<EMAIL>"
      )
      assert(result == Left(CreateEmailViaOauthError.FeatureUsageServiceError(e)))
    }

    it("should success") {
      (freeEmailDomainListService.checkIfFreeEmailService(_: String)(_: SRLogger)).expects(dummyFreeEmail, *).returning(Success(false))
      (() => srUuidUtils.generateEmailSettingUuid ()).expects().returning(emailSettingUuid.uuid)
      (emailSettingDAO.createViaOauth(_: Long, _: Long, _: Long, _: EmailSettingCreateViaOAuth,_:EmailSettingUuid)(using _: SRLogger))
        .expects(1, 1, 1, dataEmailSettingCreateViaOAuth, emailSettingUuid, *).returning(Success(Some(emailSetting)))
      (spamMonitorService.checkIfEmailIsAllowedToSend(_: String, _: Long)(using _: SRLogger, _: ExecutionContext, _: WSClient))
        .expects(*, *, logger, actorContext, wSClient).returning(Future {
        sendEmailStatusData
      })
      (srUserFeatureUsageEventService.addFeatureUsageEvent(_: Long, _: SrUserFeatureUsageEvent)).expects(1, *).returning(Success(1))
      (emailAccountTestService.insertEmailSettingIntegrationLogs(_: AccountId, _: OrgId, _: TeamId, _: Option[String], _: Option[EmailServiceProvider], _: Boolean,_:EmailSettingIntegrationLogsStage, _: Option[String])(using _: SRLogger))
        .expects(AccountId(1), OrgId(1), TeamId(1), Some("<EMAIL>"), Some(EmailServiceProvider.GMAIL_ALIAS), true,EmailSettingIntegrationLogsStage.OAuthPostRedirectSuccess, None, *)
        .returning(Success(Some(1)))
      val result = emailAccountService.createEmailViaOauth(
        orgId = 1,
        accountId = 1,
        teamId = 1,
        taId = 1,
        data = dataEmailSettingCreateViaOAuth,
        owner_email = "<EMAIL>"
      )
      assert(result == Right(emailSetting))
    }
    it("should fail with Sql Exception while performing check "){
      (freeEmailDomainListService.checkIfFreeEmailService(_: String)(_: SRLogger)).expects(dummyFreeEmail, *).returning(Failure(e))
      (emailAccountTestService.insertEmailSettingIntegrationLogs(_: AccountId, _: OrgId, _: TeamId, _: Option[String], _: Option[EmailServiceProvider], _: Boolean,_:EmailSettingIntegrationLogsStage, _: Option[String])(using _: SRLogger))
        .expects(AccountId(1), OrgId(1), TeamId(1), Some("<EMAIL>"), Some(EmailServiceProvider.GMAIL_ALIAS), false,EmailSettingIntegrationLogsStage.OAuthPostRedirectError, Some("error occurred"), *)
        .returning(Success(Some(1)))
      val result = emailAccountService.createEmailViaOauth(
        orgId = 1,
        accountId = 1,
        teamId = 1,
        taId = 1,
        data = dataEmailSettingCreateViaOAuth,
        owner_email = "<EMAIL>"
      )
      assert(result.isLeft)
    }


  }

  describe("createEmailViaForm"){
    it("should fail because domain is a free domain") {
      (freeEmailDomainListService.checkIfFreeEmailService(_: String)(_: SRLogger)).expects(dummyFreeEmail, *).returning(Success(true))
      val result = emailAccountService.createEmailViaForm(
        accountId = 1,
        teamId = 1,
        taId = 1,
        data = dataEmailSettingForm,
        orgId = OrgId(1)
      )
      assert(result == Left(CreateEmailViaFormError.FreeDomainUseError))
    }
    it("should fail with Sql Exception ") {
      (freeEmailDomainListService.checkIfFreeEmailService(_: String)(_: SRLogger)).expects(dummyFreeEmail, *).returning(Success(false))
      (() => srUuidUtils.generateEmailSettingUuid ()).expects().returning(emailSettingUuid.uuid)
      (srMailServerService.getForOrgIfDedicated)
        .expects(*)
        .returning(Success(None))
      (srMailServerService.getRandomRepMailServer)
        .expects(*)
        .returning(Success(RepMailServer(
          id = 1,
          public_ip = "String",
          reverse_dns = "String",
          sending_score_threshold = 100,
          sending_score = 10
        )))
      (emailSettingDAO.createViaForm(_: Long, _: Long, _: Long, _: EmailSettingForm,_:EmailSettingUuid, _: Int)(using _: SRLogger))
        .expects(1, 1, 1, dataEmailSettingForm, emailSettingUuid, 1, *).returning(Failure(e))

      val result = emailAccountService.createEmailViaForm(
        accountId = 1,
        teamId = 1,
        taId = 1,
        data = dataEmailSettingForm,
        orgId = OrgId(1)

      )
      assert(result.isLeft)
    }

    it("should fail with duplicate email error "){
      (freeEmailDomainListService.checkIfFreeEmailService(_: String)(_: SRLogger)).expects(dummyFreeEmail, *).returning(Success(false))
      (() => srUuidUtils.generateEmailSettingUuid ()).expects().returning(emailSettingUuid.uuid)
      (srMailServerService.getForOrgIfDedicated)
        .expects(*)
        .returning(Success(None))
      (srMailServerService.getRandomRepMailServer)
        .expects(*)
        .returning(Success(RepMailServer(
          id = 1,
          public_ip = "String",
          reverse_dns = "String",
          sending_score_threshold = 100,
          sending_score = 10
        )))
      (emailSettingDAO.createViaForm(_: Long, _: Long, _: Long, _: EmailSettingForm,_:EmailSettingUuid, _: Int)(using _: SRLogger))
        .expects(1, 1, 1, dataEmailSettingForm, emailSettingUuid, 1, *).returning(Failure(msgThrowableForForm))

      val result = emailAccountService.createEmailViaForm(
        accountId = 1,
        teamId = 1,
        taId = 1,
        data = dataEmailSettingForm,
        orgId = OrgId(1)
      )
      assert(result==Left(CreateEmailViaFormError.DuplicateEmailError))
    }

    it("should fail with email not added error ") {
      (freeEmailDomainListService.checkIfFreeEmailService(_: String)(_: SRLogger)).expects(dummyFreeEmail, *).returning(Success(false))
      (() => srUuidUtils.generateEmailSettingUuid ()).expects().returning(emailSettingUuid.uuid)
      (srMailServerService.getForOrgIfDedicated)
        .expects(*)
        .returning(Success(None))
      (srMailServerService.getRandomRepMailServer)
        .expects(*)
        .returning(Success(RepMailServer(
          id = 1,
          public_ip = "String",
          reverse_dns = "String",
          sending_score_threshold = 100,
          sending_score = 10
        )))
      (emailSettingDAO.createViaForm(_: Long, _: Long, _: Long, _: EmailSettingForm,_:EmailSettingUuid, _: Int)(using _: SRLogger))
        .expects(1, 1, 1, dataEmailSettingForm, emailSettingUuid, 1, *).returning(Success(None))

      val result = emailAccountService.createEmailViaForm(
        accountId = 1,
        teamId = 1,
        taId = 1,
        data = dataEmailSettingForm,
        orgId = OrgId(1)
      )
      assert(result == Left(CreateEmailViaFormError.EmailNotAdded))
    }

    it("should success") {
      (freeEmailDomainListService.checkIfFreeEmailService(_: String)(_: SRLogger)).expects(dummyFreeEmail, *).returning(Success(false))
      (() => srUuidUtils.generateEmailSettingUuid ()).expects().returning(emailSettingUuid.uuid)
      (srMailServerService.getForOrgIfDedicated)
        .expects(*)
        .returning(Success(None))
      (srMailServerService.getRandomRepMailServer)
        .expects(*)
        .returning(Success(RepMailServer(
          id = 1,
          public_ip = "String",
          reverse_dns = "String",
          sending_score_threshold = 100,
          sending_score = 10
        )))
      (emailSettingDAO.createViaForm(_: Long, _: Long, _: Long, _: EmailSettingForm,_:EmailSettingUuid, _: Int)(using _: SRLogger))
        .expects(1, 1, 1, dataEmailSettingForm, emailSettingUuid, 1, *).returning(Success(Some(emailSetting)))

      val result = emailAccountService.createEmailViaForm(
        accountId = 1,
        teamId = 1,
        taId = 1,
        data = dataEmailSettingForm,
        orgId = OrgId(1)
      )
      assert(result.isRight)
    }
    it("should fail with Sql Exception while performing check ") {
      (freeEmailDomainListService.checkIfFreeEmailService(_: String)(_: SRLogger)).expects(dummyFreeEmail, *).returning(Failure(e))

      val result = emailAccountService.createEmailViaForm(
        accountId = 1,
        teamId = 1,
        taId = 1,
        data = dataEmailSettingForm,
        orgId = OrgId(1)
      )
      assert(result.isLeft)
    }

  }




}
