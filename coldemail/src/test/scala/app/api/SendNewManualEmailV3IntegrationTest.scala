package app.api

import api.AppConfig
import api.accounts.{Account, AccountUuid}
import api.emails.SendNewManualEmailV3
import api.prospects.SendNewEmailManuallyError
import api.prospects.models.ValidateSendNewEmailManuallyError
import api.scheduler.model.SchedulerIntegrationTestUtils
import api.scheduler.model.SendNewEmailManuallyIntegrationTestData.InputData
import app.test_fixtures.inbox_service.InboxV3ServiceFixtures
import app.test_fixtures.inbox_service.InboxV3ServiceFixtures.{accountMetadata, profile}
import io.smartreach.esp.api.emails.IEmailAddress
import org.joda.time.DateTime
import org.scalamock.scalatest.AsyncMockFactory
import org.scalatest.funspec.AsyncFunSpec
import scalikejdbc.config.DBs
import utils.SRLogger
import utils.testapp.TestAppTrait

import scala.concurrent.ExecutionContext




class SendNewManualEmailV3IntegrationTest extends AsyncFunSpec with AsyncMockFactory with TestAppTrait{
  override implicit lazy val executionContext: ExecutionContext = workerActorSystem.dispatcher
  DBs.setupAll()

  describe("testing sendNewManualEmailV3 for sending manual email without campaign"){

    given logger: SRLogger = new SRLogger("SendNewManualEmailV3IntegrationTest ")

    // have created the fix so its not failing. Not failing is actually proving the fix works :-)
    it("should fail as we aren't allowing campaign-step-id or email-thread-id both none."){

      if(AppConfig.isDev2Domain){

        val result = multichannelTestUtil.sendManualEmailWithoutCampaignIntegrationTest(
          data = InputData.inputData.data,
          org_id = InputData.inputData.org_id,
          accountId = InputData.inputData.account_id,
          teamId = InputData.inputData.teamId,
          loggedinAccount = InputData.inputData.loggedinInAccount,
          auditRequestLogId = InputData.inputData.auditRequestLog,
          permittedAccountIds = Seq(InputData.inputData.loggedinInAccount.internal_id),
          version = InputData.inputData.version,
          tiid = InputData.inputData.tiid
        )

        result.map {

          case Left(_) =>

            assert(false)

          case Right(data) =>

            println(s"success ${data}")

            assert(true)

        }
          .recover(err => {

            println(err)

            assert(false)


          })


      } else {

        println("test didn't ran on this environment, It's for dev2 currently test name :" +
          " `should fail as we aren't allowing campaign-step-id or email-thread-id both none`")
        assert(true)

      }

    }

    // Sending Manual Email from extension integration test
    it("should succeed in fetch email-scheduled-id and succeeding as we are sending manual email from extension.") {

      if (AppConfig.isDev2Domain) {

        val result = multichannelTestUtil.sendManualEmailWithCampaignIntegrationTest(
          data = InputData.inputData.data.copy(to = Seq(IEmailAddress(
            name = None,
            email = "<EMAIL>")
          ), campaign_step_id = Some(642)),
          org_id = InputData.inputData.org_id,
          accountId = InputData.inputData.account_id,
          teamId = InputData.inputData.teamId,
          loggedinAccount = InputData.inputData.loggedinInAccount,
          auditRequestLogId = InputData.inputData.auditRequestLog,
          permittedAccountIds = Seq(InputData.inputData.loggedinInAccount.internal_id),
          version = InputData.inputData.version,
          tiid = InputData.inputData.tiid
        )

        result.map {

          case Left(_) =>

            assert(false)

          case Right(data) =>

            println(s"success ${data}")

            assert(true)

        }
          .recover(err => {

            println(err)

            assert(false)


          })


      } else {

        println("test didn't ran on this environment, It's for dev2 currently test name :" +
          " `should fail as we aren't allowing campaign-step-id or email-thread-id both none`")
        assert(true)

      }

    }

  }

}
