package app.api

import api.AppConfig
import api.accounts.{Account, AccountUuid}
import api.emails.SendNewManualEmailV3
import api.prospects.SendNewEmailManuallyError
import api.prospects.models.ValidateSendNewEmailManuallyError
import api.scheduler.model.SchedulerIntegrationTestUtils
import api.scheduler.model.SendNewEmailManuallyIntegrationTestData.InputData
import app.test_fixtures.inbox_service.InboxV3ServiceFixtures
import app.test_fixtures.inbox_service.InboxV3ServiceFixtures.{accountMetadata, profile}
import db_test_spec.api.DbTestingBeforeAllAndAfterAll
import io.smartreach.esp.api.emails.IEmailAddress
import org.joda.time.DateTime
import org.scalamock.scalatest.AsyncMockFactory
import org.scalatest.funspec.AsyncFunSpec
import scalikejdbc.config.DBs
import utils.SRLogger
import utils.testapp.TestAppTrait

import scala.concurrent.{ExecutionContext, Future}




class SendNewManualEmailV3IntegrationTest extends AsyncMockFactory with DbTestingBeforeAllAndAfterAll {




  def sendManualEmailWithoutCampaignIntegrationTest(

    data: SendNewManualEmailV3,
    org_id: Long,
    accountId: Long,
    teamId: Long,
    loggedinAccount: Account,
    auditRequestLogId: String,
    permittedAccountIds: Seq[Long],
    version: String,
    tiid: Option[String]

  ):  Future[Either[SendNewEmailManuallyError, String]] = {


    for {

      sendNewEmailManually:  Either[SendNewEmailManuallyError, String] <- inboxV3Service.sendNewEmailManually(
        data: SendNewManualEmailV3,
        org_id: Long,

        accountId: Long,
        teamId: Long,
        loggedinAccount: Account,
        auditRequestLogId: String,
        permittedAccountIds: Seq[Long],
        version: String,
        tiid: Option[String]
      )


    }yield{

      sendNewEmailManually match {

        case Left(e) =>

          println(s"error ${e}")

        case Right(data) =>

          println(s"result = >  ${data}")


      }

      sendNewEmailManually

    }

  }

  def sendManualEmailWithCampaignIntegrationTest(

    data: SendNewManualEmailV3,
    org_id: Long,
    accountId: Long,
    teamId: Long,
    loggedinAccount: Account,
    auditRequestLogId: String,
    permittedAccountIds: Seq[Long],
    version: String,
    tiid: Option[String]

  ): Future[Either[SendNewEmailManuallyError, String]] = {


    for {



      sendNewEmailManually: Either[SendNewEmailManuallyError, String] <- inboxV3Service.sendNewEmailManually(
        data = data,
        org_id = org_id,

        accountId = accountId,
        teamId = teamId,
        loggedinAccount = loggedinAccount,
        auditRequestLogId = auditRequestLogId,
        permittedAccountIds = permittedAccountIds,
        version = version,
        tiid = tiid
      )


    } yield {

      sendNewEmailManually match {

        case Left(e) =>

          println(s"error ${e}")

        case Right(data) =>

          println(s"result = >  ${data}")


      }

      sendNewEmailManually

    }

  }


  describe("testing sendNewManualEmailV3 for sending manual email without campaign"){

    given logger: SRLogger = new SRLogger("SendNewManualEmailV3IntegrationTest ")

    // have created the fix so its not failing. Not failing is actually proving the fix works :-)
    it("should fail as we aren't allowing campaign-step-id or email-thread-id both none."){

      if(AppConfig.isDev2Domain){

        val result = sendManualEmailWithoutCampaignIntegrationTest(
          data = InputData.inputData.data,
          org_id = InputData.inputData.org_id,
          accountId = InputData.inputData.account_id,
          teamId = InputData.inputData.teamId,
          loggedinAccount = InputData.inputData.loggedinInAccount,
          auditRequestLogId = InputData.inputData.auditRequestLog,
          permittedAccountIds = Seq(InputData.inputData.loggedinInAccount.internal_id),
          version = InputData.inputData.version,
          tiid = InputData.inputData.tiid
        )

        result.map {

          case Left(_) =>

            assert(false)

          case Right(data) =>

            println(s"success ${data}")

            assert(true)

        }
          .recover(err => {

            println(err)

            assert(false)


          })


      } else {

        println("test didn't ran on this environment, It's for dev2 currently test name :" +
          " `should fail as we aren't allowing campaign-step-id or email-thread-id both none`")
        assert(true)

      }

    }

    // Sending Manual Email from extension integration test
    it("should succeed in fetch email-scheduled-id and succeeding as we are sending manual email from extension.") {

      if (AppConfig.isDev2Domain) {

        val result = sendManualEmailWithCampaignIntegrationTest(
          data = InputData.inputData.data.copy(to = Seq(IEmailAddress(
            name = None,
            email = "<EMAIL>")
          ), campaign_step_id = Some(642)),
          org_id = InputData.inputData.org_id,
          accountId = InputData.inputData.account_id,
          teamId = InputData.inputData.teamId,
          loggedinAccount = InputData.inputData.loggedinInAccount,
          auditRequestLogId = InputData.inputData.auditRequestLog,
          permittedAccountIds = Seq(InputData.inputData.loggedinInAccount.internal_id),
          version = InputData.inputData.version,
          tiid = InputData.inputData.tiid
        )

        result.map {

          case Left(_) =>

            assert(false)

          case Right(data) =>

            println(s"success ${data}")

            assert(true)

        }
          .recover(err => {

            println(err)

            assert(false)


          })


      } else {

        println("test didn't ran on this environment, It's for dev2 currently test name :" +
          " `should fail as we aren't allowing campaign-step-id or email-thread-id both none`")
        assert(true)

      }

    }

  }

}
