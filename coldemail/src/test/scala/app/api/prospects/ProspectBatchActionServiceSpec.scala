package app.api.prospects

import api.APIErrorResponse.ErrorResponseUpdateProspectStatusApi
import api.accounts.TeamId
import api.accounts.models.OrgId
import api.campaigns.CampaignJustIdNameV2
import api.campaigns.models.ValidAndInvalidCampaignUuidIdList
import api.campaigns.services.GetCampaignIdFromUuidError.GetCampaignIdError
import api.campaigns.services.UpdateProspectStatusErrorV3.{FilterCampaignOwnedProspectError, FilterOwnedProspectError, GetProspectUuidFromId, UpdateProspectsStatusError, UpdateProspectsStatusValidationError}
import api.campaigns.services.{CampaignId, CampaignUuid, UpdateProspectStatusErrorV3}
import api.prospects.{OwnedProspectsForAssigning, ProspectBatchActionStatusChange, ProspectUuid}
import api.prospects.models.ProspectId
import api.prospects.service.{UpdateProspectStatusErrorType, UpdateProspectStatusValidationResult}
import app.api.TestMockTrait.TestMockTrait
import app.test_fixtures.prospect.ProspectFixtures.prospectObject_1
import org.joda.time.DateTime
import utils.SRLogger

import scala.util.{Failure, Success}

class ProspectBatchActionServiceSpec extends TestMockTrait {

  given Logger: SRLogger = new SRLogger("ProspectBatchActionSpec")
  val dateTime = Option(DateTime.now())
  val teamId = TeamId(1L)
  val orgId = OrgId(10L)
  val permittedAccountIds = Seq(1L)
  val prospectId = 1L
  val prospectIds = Seq(1L)
  val prospectsUuids = List(ProspectUuid("prospectUuids"))
  val campaignId: Long = 1L
  val campaignUuid = "campaignUuid1"
  val campaignName = "campaignName"
  val campaignUuids = List(CampaignUuid(campaignUuid))
  val campaignIds = List(CampaignId(campaignId))
  val campaignIdUuidMap = Map(CampaignUuid(campaignUuid) -> Option(CampaignId(campaignId)))
  describe("Testing ProspectBatchActionService.validateDataForUpdatingProspectStatus") {
    val wrongStatusData = ProspectBatchActionStatusChange(
      campaign_ids = Seq(campaignUuid),
      prospect_status = "wrongStatus",
      will_resume_at = dateTime,
      will_resume_at_tz = Option("timezone")
    )
    val resumeLaterNotDefinedData = ProspectBatchActionStatusChange(
      campaign_ids = Seq(campaignUuid),
      prospect_status = "resume_later",
      will_resume_at = dateTime,
      will_resume_at_tz = None
    )
    val validStatusData = ProspectBatchActionStatusChange(
      campaign_ids = Seq(campaignUuid),
      will_resume_at = dateTime,
      will_resume_at_tz = Option("timezone"),
      prospect_status = "pause"
    )
    val campaignsDb = List(CampaignJustIdNameV2(
      campaign_id = campaignId,
      campaign_name = campaignName
    ))
    val prospectObject = prospectObject_1
    val errorListProspectEmptyInvalidStatus = Left(UpdateProspectsStatusValidationError(
      List(
        ErrorResponseUpdateProspectStatusApi(
          error_type = UpdateProspectStatusErrorType.BAD_REQUEST,
          message = "prospect_ids cannot be empty.",
          data = None
        ),
        ErrorResponseUpdateProspectStatusApi(
          message = "Please send a valid prospect status.",
          error_type = UpdateProspectStatusErrorType.INVALID_PROSPECT_STATUS,
          data = None
        )
      )
    ))
    val errorListResumeLaterNotDefined = Left(UpdateProspectsStatusValidationError(
      List(
        ErrorResponseUpdateProspectStatusApi(
          error_type = UpdateProspectStatusErrorType.RESUME_LATER_DATA_ERROR,
          message = "Status change passed resume_later but  \"will_resume_at\"  or \"will_resume_at_tz\" fields not specified.",
          data = None
        )
      )
    ))
    val getCampaignIdFromUuidError = GetCampaignIdError(new Throwable("Error Occurred"))
    val validAndInvalidCampaignUuidIdListWithInvalidUuids =  ValidAndInvalidCampaignUuidIdList(
      invalid_uuids = campaignUuids,
      valid_campaigns_ids = campaignIdUuidMap
    )
    val validAndInvalidCampaignUuidIdListWithValidIds = ValidAndInvalidCampaignUuidIdList(
      invalid_uuids = List(),
      valid_campaigns_ids = campaignIdUuidMap
    )
    val invalidCampaignIdError = Left(UpdateProspectsStatusError("Please provide valid campaign_id"))
    val editPermissionError = Left(UpdateProspectsStatusError("You do not have enough permission to edit the campaign"))
    val failure = Failure(new Throwable())
    val filterOwnedProspectError = Left(FilterOwnedProspectError(failure.exception))
    val getProspectUuidFromId = Left(GetProspectUuidFromId(failure.exception))

    val failureFilterProspect = new Throwable("FilterCampaignOwnedProspectError")
    val filterCampaignOwnedProspectError = Left(FilterCampaignOwnedProspectError(failureFilterProspect))
    val errorResponseUpdateProspectStatusApi = Left(UpdateProspectsStatusValidationError(List(ErrorResponseUpdateProspectStatusApi(
      message = "Given prospects do not exist in your account",
      data = Some(prospectsUuids.mkString(",")),
      error_type = UpdateProspectStatusErrorType.FORBIDDEN
    ))))
    val prospectNotInCampaignError = Left(
      UpdateProspectsStatusValidationError(
      List(
        ErrorResponseUpdateProspectStatusApi(
          message = s"Given prospects do not exist in your campaign : ${validAndInvalidCampaignUuidIdListWithValidIds.valid_campaigns_ids.find(_._2.get.id == campaignId).map(_._1)}",
          data = Some(prospectsUuids.mkString(",")),
          error_type = UpdateProspectStatusErrorType.FORBIDDEN
        )
      )
      )
    )
    val accountOwnedProspects = List(
      OwnedProspectsForAssigning(
        prospect_id = prospectId,
        invalid_email = Some(false),
        email_bounced_at = dateTime,
        email_bounced = Some(false),
        prospect_category_id_custom = 1,
        synced_previously_sent_step_for_deleted_prospect_step_id = Option(1L)
      )
    )
    val updateProspectStatusValidationResult = Right(UpdateProspectStatusValidationResult(
      campaign_ids = List(1),
      prospect_status = "pause",
      will_resume_at = dateTime,
      will_resume_at_tz = Some("timezone"))
    )
    val campaignOwnedProspectsEmpty = Seq()
    val prospectNotInCampaign = prospectIds.filter(p => !(campaignOwnedProspectsEmpty.toList).contains(p))
    it("should return error list of wrong prospect status and empty prospect list") {
      val result = prospectBatchActionService.validateDataForUpdatingProspectStatus(
        data = wrongStatusData,
        teamId = teamId,
        orgId = orgId,
        permittedAccountIds = permittedAccountIds,
        prospectIds = Seq(),
        isApiCall = true
      )
        assert(result === errorListProspectEmptyInvalidStatus)
    }
    it("should return error list of resume_later not defined") {
      val result = prospectBatchActionService.validateDataForUpdatingProspectStatus(
        data = resumeLaterNotDefinedData,
        teamId = teamId,
        orgId = orgId,
        permittedAccountIds = permittedAccountIds,
        prospectIds = prospectIds,
        isApiCall = true
      )
      assert(result === errorListResumeLaterNotDefined)
    }
    it("should return GetCampaignIdFromUuidErrors error") {
      (campaignServiceMock.getCampaignIdsFromUuid( _:List[CampaignUuid], _:TeamId)(using _:SRLogger))
        .expects(campaignUuids,teamId,Logger)
        .returning(Left(getCampaignIdFromUuidError))

      val result = prospectBatchActionService.validateDataForUpdatingProspectStatus(
        data = validStatusData,
        teamId = teamId,
        orgId = orgId,
        permittedAccountIds = permittedAccountIds,
        prospectIds = prospectIds,
        isApiCall = true
      )
      result match {
        case Left(error) => error match {
          case UpdateProspectStatusErrorV3.GetProspectIdFromUuidErrors(_) => assert(false)
          case UpdateProspectStatusErrorV3.UpdateProspectsStatusValidationError(_) => assert(false)
          case UpdateProspectStatusErrorV3.UpdateProspectsStatusError(_) => assert(false)
          case UpdateProspectStatusErrorV3.ServerError(_) => assert(false)
          case UpdateProspectStatusErrorV3.FilterOwnedProspectError(_) => assert(false)
          case UpdateProspectStatusErrorV3.GetCampaignIdFromUuidErrors(err) => assert(err === getCampaignIdFromUuidError)
          case UpdateProspectStatusErrorV3.FilterCampaignOwnedProspectError(_) => assert(false)
          case UpdateProspectStatusErrorV3.GetProspectUuidFromId(_) => assert(false)
        }

        case Right(_) => assert(false)
      }
    }
    it("should return UpdateProspectsStatusError for wrong uuids passed") {
      (campaignServiceMock.getCampaignIdsFromUuid(_: List[CampaignUuid], _: TeamId)(using _: SRLogger))
        .expects(campaignUuids, teamId, Logger)
        .returning(Right(validAndInvalidCampaignUuidIdListWithInvalidUuids))

      val result = prospectBatchActionService.validateDataForUpdatingProspectStatus(
        data = validStatusData,
        teamId = teamId,
        orgId = orgId,
        permittedAccountIds = permittedAccountIds,
        prospectIds = prospectIds,
        isApiCall = true
      )
      assert(result === invalidCampaignIdError)
    }
    it("should return UpdateProspectsStatusError for edit campaign") {
      (campaignServiceMock.getCampaignIdsFromUuid(_: List[CampaignUuid], _: TeamId)(using _: SRLogger))
        .expects(campaignUuids, teamId, Logger)
        .returning(Right(validAndInvalidCampaignUuidIdListWithValidIds))
      (campaignServiceMock.findBasicDetailsBatch(_: List[Long], _: Long))
        .expects(List(campaignId), teamId.id)
        .returning(List())

      val result = prospectBatchActionService.validateDataForUpdatingProspectStatus(
        data = validStatusData,
        teamId = teamId,
        orgId = orgId,
        permittedAccountIds = permittedAccountIds,
        prospectIds = prospectIds,
        isApiCall = true
      )
      assert(result === editPermissionError)
    }
    it("should return FilterOwnedProspectError") {
      (campaignServiceMock.getCampaignIdsFromUuid(_: List[CampaignUuid], _: TeamId)(using _: SRLogger))
        .expects(campaignUuids, teamId, Logger)
        .returning(Right(validAndInvalidCampaignUuidIdListWithValidIds))
      (campaignServiceMock.findBasicDetailsBatch(_: List[Long], _: Long))
        .expects(List(campaignId), teamId.id)
        .returning(campaignsDb)
      (prospectServiceV2Mock.filterOwnedProspects(_: Seq[Long], _: Seq[Long], _: Long, _:OrgId, _: SRLogger))
        .expects(prospectIds,permittedAccountIds,teamId.id, orgId, Logger)
        .returning(failure)

      val result = prospectBatchActionService.validateDataForUpdatingProspectStatus(
        data = validStatusData,
        teamId = teamId,
        orgId = orgId,
        permittedAccountIds = permittedAccountIds,
        prospectIds = prospectIds,
        isApiCall = true
      )
      assert(result === filterOwnedProspectError)
    }
    it("should return getProspectUuidFromId") {
      (campaignServiceMock.getCampaignIdsFromUuid(_: List[CampaignUuid], _: TeamId)(using _: SRLogger))
        .expects(campaignUuids, teamId, Logger)
        .returning(Right(validAndInvalidCampaignUuidIdListWithValidIds))
      (campaignServiceMock.findBasicDetailsBatch(_: List[Long], _: Long))
        .expects(List(campaignId), teamId.id)
        .returning(campaignsDb)
      (prospectServiceV2Mock.filterOwnedProspects(_: Seq[Long], _: Seq[Long], _: Long, _:OrgId, _: SRLogger))
        .expects(prospectIds, permittedAccountIds, teamId.id, orgId, Logger)
        .returning(Success(List()))
      (prospectServiceMock.getProspectUuidFromId(_: List[ProspectId], _:TeamId)(using _:SRLogger))
        .expects(prospectIds.map(p=>ProspectId(p)).toList,teamId,Logger)
        .returning(failure)

      val result = prospectBatchActionService.validateDataForUpdatingProspectStatus(
        data = validStatusData,
        teamId = teamId,
        orgId = orgId,
        permittedAccountIds = permittedAccountIds,
        prospectIds = prospectIds,
        isApiCall = true
      )
      assert(result === getProspectUuidFromId)
    }

    it("should return ErrorResponseUpdateProspectStatusApi") {
      (campaignServiceMock.getCampaignIdsFromUuid(_: List[CampaignUuid], _: TeamId)(using _: SRLogger))
        .expects(campaignUuids, teamId, Logger)
        .returning(Right(validAndInvalidCampaignUuidIdListWithValidIds))
      (campaignServiceMock.findBasicDetailsBatch(_: List[Long], _: Long))
        .expects(List(campaignId), teamId.id)
        .returning(campaignsDb)
      (prospectServiceV2Mock.filterOwnedProspects(_: Seq[Long], _: Seq[Long], _: Long, _:OrgId, _: SRLogger))
        .expects(prospectIds, permittedAccountIds, teamId.id, orgId, Logger)
        .returning(Success(List()))
      (prospectServiceMock.getProspectUuidFromId(_: List[ProspectId], _: TeamId)(using _: SRLogger))
        .expects(prospectIds.map(p => ProspectId(p)).toList, teamId, Logger)
        .returning(Success(prospectsUuids))

      val result = prospectBatchActionService.validateDataForUpdatingProspectStatus(
        data = validStatusData,
        teamId = teamId,
        orgId = orgId,
        permittedAccountIds = permittedAccountIds,
        prospectIds = prospectIds,
        isApiCall = true
      )
      assert(result === errorResponseUpdateProspectStatusApi)
    }
    it("should return filterCampaignOwnedProspectError") {
      (campaignServiceMock.getCampaignIdsFromUuid(_: List[CampaignUuid], _: TeamId)(using _: SRLogger))
        .expects(campaignUuids, teamId, Logger)
        .returning(Right(validAndInvalidCampaignUuidIdListWithValidIds))
      (campaignServiceMock.findBasicDetailsBatch(_: List[Long], _: Long))
        .expects(List(campaignId), teamId.id)
        .returning(campaignsDb)
      (prospectServiceV2Mock.filterOwnedProspects(_: Seq[Long], _: Seq[Long], _: Long, _:OrgId, _: SRLogger))
        .expects(prospectIds, permittedAccountIds, teamId.id, orgId, Logger)
        .returning(Success(accountOwnedProspects))

      (campaignProspectDAOMock.getProspectsInCampaignFromGivenProspects(_: Seq[Long], _: CampaignId, _:TeamId)(using _: SRLogger))
        .expects(prospectIds, CampaignId(campaignId), teamId, *)
        .returning(Failure(failureFilterProspect))

      val result = prospectBatchActionService.validateDataForUpdatingProspectStatus(
        data = validStatusData,
        teamId = teamId,
        orgId = orgId,
        permittedAccountIds = permittedAccountIds,
        prospectIds = prospectIds,
        isApiCall = true
      )

      result match {
        case Left(FilterCampaignOwnedProspectError(_)) => assert(true)
        case Right(_) => assert(false)
        case _ => assert(false)
      }

    }

    it("should return GetProspectUuidFromId error") {
      (campaignServiceMock.getCampaignIdsFromUuid(_: List[CampaignUuid], _: TeamId)(using _: SRLogger))
        .expects(campaignUuids, teamId, Logger)
        .returning(Right(validAndInvalidCampaignUuidIdListWithValidIds))
      (campaignServiceMock.findBasicDetailsBatch(_: List[Long], _: Long))
        .expects(List(campaignId), teamId.id)
        .returning(campaignsDb)
      (prospectServiceV2Mock.filterOwnedProspects(_: Seq[Long], _: Seq[Long], _: Long, _:OrgId, _: SRLogger))
        .expects(prospectIds, permittedAccountIds, teamId.id, orgId, Logger)
        .returning(Success(accountOwnedProspects))
      (campaignProspectDAOMock.getProspectsInCampaignFromGivenProspects(_: Seq[Long], _: CampaignId, _:TeamId)(using _: SRLogger))
        .expects(prospectIds, CampaignId(campaignId), teamId, *)
        .returning(Success(campaignOwnedProspectsEmpty))

      (prospectServiceMock.getProspectUuidFromId(_: List[ProspectId], _: TeamId)(using _: SRLogger))
        .expects(prospectNotInCampaign.map(p => ProspectId(p)).toList, teamId, Logger)
        .returning(failure)

      val result = prospectBatchActionService.validateDataForUpdatingProspectStatus(
        data = validStatusData,
        teamId = teamId,
        orgId = orgId,
        permittedAccountIds = permittedAccountIds,
        prospectIds = prospectIds,
        isApiCall = true
      )

      result match {
        case Left(GetProspectUuidFromId(_)) => assert(true)
        case Right(_) => assert(false)
        case _ => assert(false)
      }
    }

    it("should return prospect not in campaign error") {
      (campaignServiceMock.getCampaignIdsFromUuid(_: List[CampaignUuid], _: TeamId)(using _: SRLogger))
        .expects(campaignUuids, teamId, Logger)
        .returning(Right(validAndInvalidCampaignUuidIdListWithValidIds))
      (campaignServiceMock.findBasicDetailsBatch(_: List[Long], _: Long))
        .expects(List(campaignId), teamId.id)
        .returning(campaignsDb)
      (prospectServiceV2Mock.filterOwnedProspects(_: Seq[Long], _: Seq[Long], _: Long, _:OrgId, _: SRLogger))
        .expects(prospectIds, permittedAccountIds, teamId.id, orgId, Logger)
        .returning(Success(accountOwnedProspects))
      (campaignProspectDAOMock.getProspectsInCampaignFromGivenProspects(_: Seq[Long], _: CampaignId, _:TeamId)(using _: SRLogger))
        .expects(List(1L), CampaignId(campaignId), teamId, *)
        .returning(Success(List()))
      (prospectServiceMock.getProspectUuidFromId(_: List[ProspectId], _:TeamId)(using _:SRLogger))
        .expects(prospectIds.map(p => ProspectId(p)).toList,teamId,Logger)
        .returning(Success(prospectsUuids))

      val result = prospectBatchActionService.validateDataForUpdatingProspectStatus(
        data = validStatusData,
        teamId = teamId,
        orgId = orgId,
        permittedAccountIds = permittedAccountIds,
        prospectIds = prospectIds,
        isApiCall = true
      )

      result match {
        case Left(UpdateProspectsStatusValidationError(_)) => assert(true)
        case Right(_) => assert(false)
        case _ => assert(false)
      }
    }

    it("should return UpdateProspectStatusValidationResult") {
      (campaignServiceMock.getCampaignIdsFromUuid(_: List[CampaignUuid], _: TeamId)(using _: SRLogger))
        .expects(campaignUuids, teamId, Logger)
        .returning(Right(validAndInvalidCampaignUuidIdListWithValidIds))
      (campaignServiceMock.findBasicDetailsBatch(_: List[Long], _: Long))
        .expects(List(campaignId), teamId.id)
        .returning(campaignsDb)
      (prospectServiceV2Mock.filterOwnedProspects(_: Seq[Long], _: Seq[Long], _: Long, _:OrgId, _: SRLogger))
        .expects(prospectIds, permittedAccountIds, teamId.id, orgId, Logger)
        .returning(Success(accountOwnedProspects))
      (campaignProspectDAOMock.getProspectsInCampaignFromGivenProspects(_: Seq[Long], _: CampaignId, _:TeamId)(using _: SRLogger))
        .expects(List(1L), CampaignId(campaignId), teamId, *)
        .returning(Success(Seq(1L)))

      val result = prospectBatchActionService.validateDataForUpdatingProspectStatus(
        data = validStatusData,
        teamId = teamId,
        orgId = orgId,
        permittedAccountIds = permittedAccountIds,
        prospectIds = prospectIds,
        isApiCall = true
      )

      assert(result === updateProspectStatusValidationResult)
    }

  }
}
