package app.api.prospects

import org.apache.pekko.actor.ActorSystem
import api.accounts.models.{AccountId, AccountProfileInfo, OrgId, OrgId as orgId}
import api.campaigns.models.{CurrentStepId, SendEmailFromCampaignDetails, StepDetails}
import api.accounts.{Account, AccountAccess, AccountBillingData, AccountErrorObjectApi, AccountMetadata, AccountService, AccountType, AccountUuid, AccountWarningObjectApi, OrgCountData, OrgMetadata, OrgPlan, OrgSettings, OrganizationRole, OrganizationWithCurrentData, PermType, PermissionLevelForValidation, PermissionMethods, PermissionOwnershipV2, ProspectCategoriesInDB, RepTrackingHosts, ReplyHandling, RolePermV2, RolePermissionDataDAOV2, RolePermissionDataV2, RolePermissionsInDBV2, RolePermissionsV2, TeamA<PERSON>unt, TeamAccount<PERSON>ole, TeamId, TeamMember, TeamMemberLite}
import api.AppConfig
import api.accounts.email.models.EmailServiceProvider
import api.accounts.service.AccountOrgBillingRelatedService
import api.calendar_app.models.CalendarAccountData
import api.campaigns.services.{CampaignId, CampaignService}
import api.campaigns.{AssignedCampaignForProspect, CPCompleted, CampaignDAO, CampaignProspectDAO}
import api.emails.dao_service.EmailScheduledDAOService
import api.emails.dao_service.EmailThreadDAOService
import api.emails.models.{EmailMessageContactModel, EmailReplyType, EmailSettingUuid, InboxType, InboxTypeData, ProspectEmailMessage}
import api.emails.{AssociateEmailThreadProspect, ConversationsSearchResponse, DBEmailMessagesSavedResponse, EmailCommonService, EmailMessageTracked, EmailReplySavedV3, EmailReplyTrackingModelV2, EmailScheduled, EmailScheduledDAO, EmailSetting, EmailSettingDAO, EmailThreadDAO, EmailToBeSent, EmailsScheduledUuid, QueryTimeLine, SearchedEmailThreads, SendNewManualEmailV2, SendNewManualEmailV3, ThreadAndTeamId, ThreadForProspectInternal}
import api.linkedin_message_threads.LinkedinMessageThreadsDAO
import api.linkedin_messages.LinkedinMessagesDAO
import api.linkedin_messages.dao_service.LinkedinMessagesDAOService
import api.prospects.MailboxFolder.TeamInboxFolder
import api.prospects.{ExactIdToCompareTime, FindConversationCampaignError, GetConversationIdsForSearchFailure, InboxController, InboxV3Service, InboxV3ServiceGetConversationsError, InboxV3ServiceGetNextConversationError, IncomingQueryTimeLine, InferredQueryTimeline, MailboxFolder, ProspectAccountDAO1, ProspectCheckForIsSentEmail, ProspectIdEmail, ThreadId, ValidatedConvReq}
import api.prospects.ValidatedConvReq.ValidatedMailboxReqRange
import api.prospects.dao.NewlyCreatedProspect
import api.prospects.dao_service.{InboxV3DAOService, ProspectDAOService}
import api.prospects.models.{ProspectAccountsId, ProspectCategory, ProspectCategoryRank, ProspectId, SendNewEmailManualData, StepId, ValidateSendNewEmailManuallyError}
import api.prospects.service.ProspectServiceV2
import api.rep_tracking_hosts.service.RepTrackingHostService
import api.tasks.services.TaskService
import api.team.TeamUuid
import api.team_inbox.dao.{EmailTeamInboxDAO, SettingIdsAndChannelType}
import eventframework.ConversationObject.ConversationCampaignAndProspect
import eventframework.{ConversationObjectInboxV3, MessageObject, SrResourceTypes}
import org.scalamock.scalatest.AsyncMockFactory
import org.scalatest.flatspec.AsyncFlatSpec
import play.api.libs.ws.WSClient
import play.api.libs.ws.ahc.AhcWSClient
import scalikejdbc.{DBSession, NoExtractor, SQL, scalikejdbcSQLInterpolationImplicitDef}
import utils.email.{EmailReplyStatus, EmailSendMailRequest, EmailSenderService, EmailService, EmailServiceBody}
import utils.templating.TemplateService
import scalikejdbc.interpolation.SQLSyntax

import scala.concurrent.{ExecutionContext, Future}
import api.team.service.TeamService
import api.team_inbox.dao_service.ReplySentimentDAOService
import api.team_inbox.model.{FolderType, ValidatedInboxAndSettingData}
import api.team_inbox.service.{ReplySentimentService, ReplySentimentUuid, TeamInboxService, ValidateInboxTypeAndGetEsetsError}
import app.test_fixtures.accounts.OrgCountDataFixture
import app.test_fixtures.inbox_service.InboxV3ServiceFixtures
import app.test_fixtures.inbox_service.InboxV3ServiceFixtures.campaign_step_related_data
import app.test_fixtures.organizationa.{OrgMetadataFixture, OrgPlanFixture}
import io.smartreach.esp.api.emails.{EmailSettingId, IEmailAddress}
import org.joda.time.DateTime
import org.scalamock.scalatest.MockFactory
import org.scalatest.flatspec.AnyFlatSpec
import utils.SRLogger
import org.joda.time.DateTime
import play.api.libs.json.{JsValue, Json}
import sr_scheduler.CampaignStatus
import sr_scheduler.models.ChannelType
import utils.cache_utils.model.CampaignUseStatusForEmailSetting
import utils.dbutils.{DBUtils, DbAndSession}
import utils.jodatimeutils.JodaTimeUtils
import utils.mq.webhook.mq_activity_trigger.MQActivityTriggerPublisher
import utils.uuid.services.SrUuidService
import api.emails.EmailCommonService
import utils.testapp.TestAppExecutionContext

import scala.util.{Failure, Success}

class InboxV3ServiceSpec extends AsyncFlatSpec with AsyncMockFactory {

  val emailThreadDAOService: EmailThreadDAOService = mock[EmailThreadDAOService]
  val campaignService: CampaignService = mock[CampaignService]
  val emailSettingDAO: EmailSettingDAO = mock[EmailSettingDAO]
  val prospectServiceV2: ProspectServiceV2 = mock[ProspectServiceV2]
//  val accountService: AccountService = mock[AccountService]
  val emailScheduledDAO: EmailScheduledDAO = mock[EmailScheduledDAO]
  val emailReplyTrackingModelV2: EmailReplyTrackingModelV2 = mock[EmailReplyTrackingModelV2]
  val templateService: TemplateService = mock[TemplateService]
  val emailService: EmailService = mock[EmailService]
  val prospectDAOService: ProspectDAOService = mock[ProspectDAOService]
  val prospectAccountDAO: ProspectAccountDAO1 = mock[ProspectAccountDAO1]
  //  val teamInboxDAO: TeamInboxDAO = mock[TeamInboxDAO]
  val replySentimentDAOService: ReplySentimentDAOService = mock[ReplySentimentDAOService]
  val dbUtils: DBUtils = mock[DBUtils]
  val emailMessageContactModel: EmailMessageContactModel = mock[EmailMessageContactModel]
  val campaignProspectDAO: CampaignProspectDAO = mock[CampaignProspectDAO]
  val teamService: TeamService = mock[TeamService]
  val rolePermissionDataDAOV2 = mock[RolePermissionDataDAOV2]
  val srUuidService = mock[SrUuidService]
  val linkedinMessageThreadsDAO = mock[LinkedinMessageThreadsDAO]
  val inboxV3DAOService: InboxV3DAOService = mock[InboxV3DAOService]
  val repTrackingHostService: RepTrackingHostService = mock[RepTrackingHostService]
  val linkedinMessagesDAOService = mock[LinkedinMessagesDAOService]
  val teamInboxService: TeamInboxService = mock[TeamInboxService]
  val emailSenderService: EmailSenderService = mock[EmailSenderService]
  val taskService: TaskService = mock[TaskService]
  val emailScheduledDAOService: EmailScheduledDAOService = mock[EmailScheduledDAOService]
  val mqActivityTriggerPublisher: MQActivityTriggerPublisher = mock[MQActivityTriggerPublisher]
  val accountOrgBillingRelatedService: AccountOrgBillingRelatedService = mock[AccountOrgBillingRelatedService]
  val emailCommonService: EmailCommonService = mock[EmailCommonService]


  val inboxV3Service = new InboxV3Service(
    emailSettingDAO = emailSettingDAO,
    emailThreadDAOService = emailThreadDAOService,
    campaignService = campaignService,
    linkedinMessageThreadsDAO = linkedinMessageThreadsDAO,
    prospectServiceV2 = prospectServiceV2,
//    accountService = accountService,
    emailScheduledDAO = emailScheduledDAO,
    emailScheduledDAOService = emailScheduledDAOService,
    linkedinMessagesDAOService = linkedinMessagesDAOService,
    srUuidService = srUuidService,
    emailReplyTrackingModelV2 = emailReplyTrackingModelV2,
    templateService = templateService,
    emailService = emailService,
    prospectDAOService = prospectDAOService,
    prospectAccountDAO = prospectAccountDAO,
    //    teamInboxDAO = teamInboxDAO,
    dbUtils = dbUtils,
    emailMessageContactModel = emailMessageContactModel,
    replySentimentDAOService = replySentimentDAOService,
    campaignProspectDAO = campaignProspectDAO,
    teamService = teamService,
    inboxV3DAOService = inboxV3DAOService,
    repTrackingHostService = repTrackingHostService,
    teamInboxService = teamInboxService,
    mqActivityTriggerPublisher = mqActivityTriggerPublisher,
    accountOrgBillingRelatedService = accountOrgBillingRelatedService,
    emailCommonService = emailCommonService
  )

  val aDate = DateTime.now()
  //val pageSize = 20
  val SQLError = new Throwable("SQLERROR")

  val teamId: Long = 37L
  val permittedOwnerIds = Seq(47L)
  val page_size = 5
  given Logger: SRLogger = new SRLogger("tests")

  val orgMetadata = OrgMetadataFixture.orgMetadataFixture2

  val orgCountData: OrgCountData = OrgCountDataFixture.orgCountData_default

  val orgSettings = OrgSettings(
    enable_ab_testing = false,
    disable_force_send = false,
    bulk_sender = false,
    allow_2fa = false,
    show_2fa_setting = false,
    enforce_2fa = false,
    allow_native_crm_integration = false,
    agency_option_allow_changing = false,
    agency_option_show = false
  )

  val orgPlan = OrgPlanFixture.orgPlanFixture

  val org = OrganizationWithCurrentData(

    id = 1,
    name = "AK",
    owner_account_id = 2,

    counts = orgCountData,
    settings = orgSettings,
    plan = orgPlan,

    is_agency = true,
    trial_ends_at = DateTime.now().plusDays(100),
    error = None,
    error_code = None,
    paused_till = None,
    errors = Seq(),
    warnings = Seq(),
    via_referral = false,
    org_metadata = orgMetadata
  )

  val teamMemberLite = TeamMemberLite(

    user_id = 10L,
    first_name = Some("first_name"),
    last_name = Some("last_name"),
    email = "<EMAIL>",
    active = true,
    timezone = Some("campaignTimezone"),
    twofa_enabled = true,
    created_at = aDate,
    user_uuid = AccountUuid("uuid"),
    team_role = TeamAccountRole.ADMIN

  )

  val prospect_CategoriesInDB = ProspectCategoriesInDB(
    id = 22L,
    name = "Completed",
    text_id = "Done",
    label_color = "Blue",
    is_custom = true,
    team_id = teamId,
    rank = ProspectCategoryRank(rank = 2000),
  )


  val profile = AccountProfileInfo(
    first_name = "Animesh",
    last_name = "Kumar",
    company = Some("AK"),
    timezone = None,
    country_code = None,
    mobile_country_code = None,
    mobile_number = None,
    onboarding_phone_number = None,
    twofa_enabled = false,
    has_gauthenticator = false,
    weekly_report_emails = None,
    scheduled_for_deletion_at = None
  )

  val accountMetadata = AccountMetadata(
    // account_ui_version = None,
    is_profile_onboarding_done = None
  )

  val accountAdmin = Account(
    id = AccountUuid("account_uuid"),
    internal_id = 2,
    email = "<EMAIL>",
    email_verification_code = None,
    email_verification_code_created_at = None,
    created_at = DateTime.now().minusDays(1000),
    first_name = Some("Animesh"),
    last_name = Some("Kumar"),
    company = Some("AK"),
    timezone = None,
    profile = profile,
    org_role = Some(OrganizationRole.OWNER),
    teams = Seq(),
    account_type = AccountType.AGENCY,
    org = org,
    active = true,
    email_notification_summary = "dSFA",
    account_metadata = accountMetadata,
    email_verified = true,
    signupType = None,
    account_access = AccountAccess(
      inbox_access = false
    ),
    calendar_account_data = None

  )

  val teamMember: TeamMember = TeamMember(
    team_id = teamId,
    team_name = "team_name",
    user_id = 47L,
    ta_id = 49L, // dont send ta_id to frontend / api response, only for internal purpose, its dynamically assigned in AuthUtils
    first_name = Some("first_name"),
    last_name = Some("Last_name"),
    email = "<EMAIL>",
    team_role = TeamAccountRole.ADMIN,
    api_key = Some("apiKey"),
    zapier_key = Some("zapier_key")
  )


  //val team_id: Long = 40L

  val adminDefaultPermissions = RolePermissionDataDAOV2.defaultRoles(
    role = TeamAccountRole.ADMIN,
    simpler_perm_flag = false
  )

  val rolePermissionData = RolePermissionDataV2.toRolePermissionApi(
    data = adminDefaultPermissions.copy(id = 10)
  )

  val PermLogger = new SRLogger(logRequestId = "inboxV3ServiceSpec")
  val team_account: TeamAccount = TeamAccount(

    team_id = teamId,
    org_id = 20L,

    role_from_db = Some(adminDefaultPermissions), // MUST come from db (option type only for cacheservice error), should not be sent to frontend, only intermediate

    role = Some(rolePermissionData), // should be sent to frontend

    active = true,
    is_actively_used = true,
    team_name = "team_name",
    total_members = 5,
    access_members = Seq(teamMember),
    all_members = Seq(teamMemberLite),

    prospect_categories_custom = Seq(prospect_CategoriesInDB),
    max_emails_per_prospect_per_day = 100L,
    max_emails_per_prospect_per_week = 500L,
    max_emails_per_prospect_account_per_day = 97,
    max_emails_per_prospect_account_per_week = 497,

    reply_handling = ReplyHandling.PAUSE_SPECIFIC_CAMPAIGN_ON_REPLY,
    created_at = DateTime.now(),
    selected_calendar_data = None,
    team_uuid = TeamUuid("uuid")
  )




  /*
  "inboxV3Service.getNextConversationsForProspects" should(
          " throw error") in {

    (emailThreadDAO.getConversationsForProspects)
            .expects(teamId,
              permittedOwnerIds,
              MailboxFolder.Prospects,
              QueryTimeLine.Before( aDate),
              page_size + 1, Logger)
            .returning(Failure(SQLError))


    val res = inboxV3Service.getNextConversationsForProspects(
      teamId = teamId,
      folder = MailboxFolder.Prospects,
      permittedOwnerIds = permittedOwnerIds,
      replied_at_beforeTime = aDate, page_size = page_size, Logger)
    res match {
      case Left(InboxV3ServiceGetNextConversationError
        .DBFailure( exception)) => assert(true)
      case _ => assert(false)
    }
  }

   */


  val owner_id: Long = 300101L
  val owner_name = "the owner"
  val done = false
  val snoozed = false

  val email_msg_id_start = 5001000
  val from_email: IEmailAddress = IEmailAddress(name = Some("a person"),
    email = "<EMAIL>")
  val to_emails: Seq[IEmailAddress] = Seq(IEmailAddress(name = None, email = "<EMAIL>"))
  val cc_emails: Seq[IEmailAddress] = Seq(IEmailAddress(name = None, email = "<EMAIL>"))
  val bcc_emails: Seq[IEmailAddress] = Seq(IEmailAddress(name = None, email = "<EMAIL>"))

  val subj = "Subj : Test Email"
  val body = "Body of the email"
  val body_preview = "Preview Body of the email"
  val sent_at = DateTime.parse("2022-01-13")

  val emlMsg = MessageObject.EmailMessageObject(
    uuid = Some("em_2vJVmvifsnC"),
    from_user = true,
    from = from_email,
    reply_to = None,
    to = to_emails,
    cc_emails = None,
    bcc_emails = None,
    subject = subj,
    body = body,
    body_preview = body_preview,
    sent_at = sent_at
  )

  val convObjV3 = ConversationObjectInboxV3.EmailConversationObjectInboxV3(
    uuid = "100101",
    team_id = TeamId(teamId),
    owner_id = AccountId(owner_id),
    owner_name = owner_name,
    snoozed_till = None,
    // latest_message.sent_at is used to paginate
    latest_message = emlMsg,
    latest_reply_at = Some(aDate),
    latest_sent_by_admin_at = Some(aDate),
    is_read = false,
    team_inbox_id = Some(101L),
    team_inbox_name = Some("Sales"),
    reply_sentiment_uuid = None,
    updated_at = aDate
  )

  val dummy_prospect_category = Some(1037101L)


  "inboxV3Service.validateGetConversationsRequest" should (
    " reject if before and after both present") in {

    val res = inboxV3Service.validateGetConversationsRequest(
      esets = List(1L),
      folder_id = Some("prospect"),
      prospect_category = None,
      older_than = Some(12334567L),
      newer_than = Some(34343344L),
      page_size = Some(40),
      rs_id = None,
      inbox_type_data = InboxTypeData.SINGLE_DATA(
        team_inbox_id = 123
      )
    )
    res match {
      case Left(str: String) => assert(true)
      case _ => assert(false)
    }
  }

  "inboxV3Service.validateGetConversationsRequest" should (
    s" reject if limit > 100 ") in {

    val res = inboxV3Service.validateGetConversationsRequest(
      esets = List(1L),
      folder_id = Some("prospect"),
      prospect_category = None,
      older_than = Some(12334567L),
      newer_than = None,
      page_size = Some(101),
      rs_id = None,
      inbox_type_data = InboxTypeData.SINGLE_DATA(
        team_inbox_id = 123
      )
    )
    res match {
      case Left(str: String) => assert(true)
      case _ => assert(false)
    }
  }

  "inboxV3Service.validateGetConversationsRequest" should (
    s" reject if limit < 3 ") in {

    val res = inboxV3Service.validateGetConversationsRequest(
      esets = List(1L),
      folder_id = Some("prospect"),
      prospect_category = None,
      older_than = Some(12334567L),
      newer_than = None,
      page_size = Some(2),
      rs_id = None,
      inbox_type_data = InboxTypeData.SINGLE_DATA(
        team_inbox_id = 123
      )
    )
    res match {
      case Left(str: String) => assert(true)
      case _ => assert(false)
    }
  }

  "inboxV3Service.validateGetConversationsRequest" should (
    s" reject if folder_id none for inbox inbox_id  ") in {

    val res = inboxV3Service.validateGetConversationsRequest(
      esets = List(1L),
      folder_id = None,
      prospect_category = None,
      older_than = Some(12334567L),
      newer_than = None,
      page_size = Some(22),
      rs_id = None,
      inbox_type_data = InboxTypeData.SINGLE_DATA(
        team_inbox_id = 123
      )
    )
    res match {
      case Left(str: String) =>
        Logger.info(str)
        assert(true)
      case _ => assert(false)
    }
  }

  "inboxV3Service.validateGetConversationsRequest" should (
    s" return mailBoxFolderReq as TeamInboxFolder.Prospects(esetid, prospect_category) only if eset is valid") in {

    val res = inboxV3Service.validateGetConversationsRequest(
      esets = List(1L),
      folder_id = Some("prospect"),
      prospect_category = Some(120),
      older_than = Some(12334567L),
      newer_than = None,
      page_size = Some(22),
      rs_id = None,
      inbox_type_data = InboxTypeData.SINGLE_DATA(
        team_inbox_id = 123
      )
    )
    res match {
      case Left(str: String) =>
        Logger.info(str)
        assert(true)
      case _ =>
        assert(false)
    }
  }

  "inboxV3Service.validateGetConversationsRequest" should (
    s" should throw when folder_id is non_prospects and inbox_type_data is All_campaigns") in {

    val res = inboxV3Service.validateGetConversationsRequest(
      esets = List(1L),
      folder_id = Some("non-prospects"),
      prospect_category = Some(120),
      older_than = Some(12334567L),
      newer_than = None,
      page_size = Some(22),
      rs_id = None,
      inbox_type_data = InboxTypeData.ALLCAMPAIGNS_DATA(
        prospect_id = None,
        prospect_account_id = None,
        campaign_id = None
      )
    )
    res match {
      case Left(str: String) =>
        Logger.info(str)
        assert(true)
      case _ =>
        assert(false)
    }
  }
  val inboxEmailSettingId = EmailSettingId(emailSettingId = 123)
  val ownerId = AccountId(id = 47)
  //val team_id: Long = 789
  val email = "<EMAIL>"
  val email_address_host = "company.com"

  val emailMessageIdSuffix = "local@smartreachio"


  val emailSetting1 = EmailSetting(
    id = Some(inboxEmailSettingId),
    org_id = OrgId(id = 123567),
    owner_id = ownerId,
    team_id = TeamId(id = teamId), // FIXME VALUECLASS
    message_id_suffix = emailMessageIdSuffix,
    uuid = Some(EmailSettingUuid("test_uuid")),
    owner_uuid = AccountUuid("owner_uuid"),
    team_uuid = TeamUuid("team_uuid"),
    email = email,
    email_address_host = email_address_host,

    service_provider = EmailServiceProvider.OTHER,
      domain_provider = None,
    via_gmail_smtp = None,

    owner_name = "Ownername dummy",
    sender_name = "Test Sender Name",
    first_name = "John",
    last_name = "Doe",

    cc_emails = None,
    bcc_emails = None,

    smtp_username = Some(email),
    smtp_password = Some("thisispassword"),
    smtp_host = Some("this is the smtp host"),
    smtp_port = Some(12345),

    imap_username = Some(email),
    imap_password = Some("thisisimappassword"),
    imap_host = Some("imap.host.com"),
    imap_port = Some(993),

    oauth2_access_token = None,
    oauth2_refresh_token = None,
    oauth2_token_type = None,
    oauth2_token_expires_in = None,
    oauth2_access_token_expires_at = None,

    // for mailgun
    email_domain = None,
    api_key = None,
    mailgun_region = None,

    quota_per_day = 3,

    reply_handling = ReplyHandling.PAUSE_SPECIFIC_CAMPAIGN_ON_REPLY,
    last_read_for_replies = None,
    latest_email_scheduled_at = None,

    error = None,
    error_reported_at = None,
    paused_till = None,

    signature = "MySignature",

    created_at = Some(DateTime.now()),

    current_prospect_sent_count_email = 3,

    default_tracking_domain = "company.com",
    default_unsubscribe_domain = "company.com",
    rep_tracking_host_id = 123,
    tracking_domain_host = None,
    custom_tracking_domain = None,
    custom_tracking_cname_value = None,
    custom_tracking_domain_is_verified = None,
    custom_tracking_domain_is_ssl_enabled = None,

    rep_mail_server_id = 123,
    rep_mail_server_public_ip = "127.0.0.1",
    rep_mail_server_host = "http://localhost:9000",
    rep_mail_server_reverse_dns = None,

    min_delay_seconds = 30,
    max_delay_seconds = 120,
      tag = None,
    campaign_use_status_for_email_setting = CampaignUseStatusForEmailSetting.IsNotAssignedToAnyCampaign,
    show_rms_ip_in_frontend = false

  )

  val emailSetting2 = EmailSetting(
    id = Some(inboxEmailSettingId),
    org_id = OrgId(id = 123567),
    owner_id = AccountId(id = 1),
    team_id = TeamId(id = 80),
    message_id_suffix = emailMessageIdSuffix,
    uuid = Some(EmailSettingUuid("test_uuid")),
    owner_uuid = AccountUuid("owner_uuid"),
    team_uuid = TeamUuid("team_uuid"),
    email = email,
    email_address_host = email_address_host,

    service_provider = EmailServiceProvider.OTHER,
      domain_provider = None,
    via_gmail_smtp = None,

    owner_name = "Ownername dummy",
    sender_name = "Test Sender Name",
    first_name = "John",
    last_name = "Doe",

    cc_emails = None,
    bcc_emails = None,

    smtp_username = Some(email),
    smtp_password = Some("thisispassword"),
    smtp_host = Some("this is the smtp host"),
    smtp_port = Some(12345),

    imap_username = Some(email),
    imap_password = Some("thisisimappassword"),
    imap_host = Some("imap.host.com"),
    imap_port = Some(993),

    oauth2_access_token = None,
    oauth2_refresh_token = None,
    oauth2_token_type = None,
    oauth2_token_expires_in = None,
    oauth2_access_token_expires_at = None,

    // for mailgun
    email_domain = None,
    api_key = None,
    mailgun_region = None,

    quota_per_day = 3,

    reply_handling = ReplyHandling.PAUSE_SPECIFIC_CAMPAIGN_ON_REPLY,
    last_read_for_replies = None,
    latest_email_scheduled_at = None,

    error = None,
    error_reported_at = None,
    paused_till = None,

    signature = "MySignature",

    created_at = Some(DateTime.now()),

    current_prospect_sent_count_email = 3,

    default_tracking_domain = "company.com",
    default_unsubscribe_domain = "company.com",
    rep_tracking_host_id = 123,
    tracking_domain_host = None,
    custom_tracking_domain = None,
    custom_tracking_cname_value = None,
    custom_tracking_domain_is_verified = None,
    custom_tracking_domain_is_ssl_enabled = None,

    rep_mail_server_id = 123,
    rep_mail_server_public_ip = "0.0.0.0",
    rep_mail_server_host = "randomserverhost.com",
    rep_mail_server_reverse_dns = None,

    min_delay_seconds = 30,
    max_delay_seconds = 120,
      tag = None,
    campaign_use_status_for_email_setting = CampaignUseStatusForEmailSetting.IsNotAssignedToAnyCampaign,
    show_rms_ip_in_frontend = false

  )


  "sorted order is descending " should ("return convs in sorted order descending") in {
    val sortedConvs1 = List(
      convObjV3
      , convObjV3.copy(updated_at = aDate.minusDays(1)))
      .sortBy(_.updated_at)(JodaTimeUtils.dateTimeOrdering)
      .reverse

    assert(sortedConvs1.head.updated_at == aDate)
    assert(sortedConvs1.last.updated_at == aDate.minusDays(1))

  }
  // see the test above - the order of the convs is
  // [0: ("2022-1-12")][1: ("2022-1-11")]


  val team_id: Long = 1011001
  val conversation_id: Long = 1021001
  val cmp_id = 1031001L
  val prospect_is_active = true

  "inboxV3Service.handleGetCampaignAndProspectForConversationInboxV3" should (
    "return left database error if the DB call throws an exception"
    ) in {

    (campaignService.getCampaignAndProspectByConversationId)
      .expects(
        team_id,
        cmp_id,
        conversation_id,
        Logger
      ).returning(Failure(new Exception("database error")))

    val res = inboxV3Service.handleGetCampaignAndProspectForConversationInboxV3(
      conversation_id = conversation_id,
      campaign_id = Some(cmp_id),
      team_id = team_id,
      Logger = Logger
    )

    res match {
      case Left(FindConversationCampaignError.DatabaseError(ex)) =>
        assert(true)
      case _ =>
        assert(false)
    }

  }

  "inboxV3Service.handleGetCampaignAndProspectForConversationInboxV3" should (
    "return left Email thread not part of campaign if the campaign_id is a none"
    ) in {

    val res = inboxV3Service.handleGetCampaignAndProspectForConversationInboxV3(
      conversation_id = conversation_id,
      campaign_id = None,
      team_id = team_id,
      Logger = Logger
    )

    res match {
      case Left(FindConversationCampaignError.EmailThreadNotPartOfACampaign(err)) =>
        assert(true)
      case _ =>
        assert(false)
    }

  }

  "inboxV3Service.handleGetCampaignAndProspectForConversationInboxV3" should (
    "return left Campaign not found if db returns empty"
    ) in {

    (campaignService.getCampaignAndProspectByConversationId)
      .expects(
        team_id,
        cmp_id,
        conversation_id,
        Logger
      ).returning(Success(None))


    val res = inboxV3Service.handleGetCampaignAndProspectForConversationInboxV3(
      conversation_id = conversation_id,
      campaign_id = Some(cmp_id),
      team_id = team_id,
      Logger = Logger
    )

    res match {
      case Left(FindConversationCampaignError.CampaignNotFoundError(err)) =>
        assert(true)
      case _ =>
        assert(false)
    }

  }

  "inboxV3Service.handleGetCampaignAndProspectForConversationInboxV3" should (
    "return Right ConversationCampaignAndPropsect Object when db returns success"
    ) in {

    val conv_campaign = ConversationCampaignAndProspect(
      prospect_id = 44444L,
      campaign_id = cmp_id,
      prospect_uuid = "uuid",
      campaign_uuid = "uuid",
      campaign_name = "campaign 101",
      campaign_tz = "timezone",
      show_rescheduling_option = true,
      prospect_is_active = true,
      will_resume_at = Some(aDate),
      will_resume_at_tz = Some("resume date")
    )

    (campaignService.getCampaignAndProspectByConversationId)
      .expects(
        team_id,
        cmp_id,
        conversation_id,
        Logger
      ).returning(Success(Some(conv_campaign)))


    val res = inboxV3Service.handleGetCampaignAndProspectForConversationInboxV3(
      conversation_id = conversation_id,
      campaign_id = Some(cmp_id),
      team_id = team_id,
      Logger = Logger
    )

    res match {
      case Right(conv_campaign) =>
        assert(true)
      case _ =>
        assert(false)
    }

  }

  val email_thread_id = 111

  val sendNewManualEmailV2 = SendNewManualEmailV3(
    email_thread_id = Some(email_thread_id.toString),
    sender_email_setting_id = 1L,
    receiver_email_setting_id = Some(1L),

    to = to_emails,

    cc_emails = None,
    bcc_emails = None,

    body = "email body",
    subject = "email subject",

    enable_open_tracking = None,
    enable_click_tracking = None,
    mark_as_done = None
  )

  val sendEmailFromCampaignDetails = SendEmailFromCampaignDetails(
    campaign_id = 1L,
    campaign_name = "campaignName",
    stepDetails = None
  )

  val emailScheduled = EmailScheduled(
    id = Some(1L),

    subject = Some("email subject"),

    message_id = Some("message_id"),

    references_header = None,

    sendEmailFromCampaignDetails = Some(sendEmailFromCampaignDetails),
    prospect_id = Some(1L),
    sender_email_settings_id = Some(1L),

    email_thread_id = Some(1L),
    outlook_msg_id = None,

    scheduled_at = aDate,

    sent_at = Some(aDate),

    account_id = Some(accountAdmin.internal_id),
    team_id = Some(team_id)
  )

  val emailToBeSent = EmailToBeSent(
    to_emails = to_emails,
    from_email = "<EMAIL>",
    cc_emails = Seq(),
    bcc_emails = Seq(),
    from_name = "from_name",
    reply_to_email = Some("<EMAIL>"),
    reply_to_name = Some("from_name"),

    subject = "email subject",
    textBody = "textBody",
    htmlBody = "htmlBody",
    message_id = Some("message_id"),
    references_header = None,

    in_reply_to_id = None,
    in_reply_to_references_header = None,
    in_reply_to_sent_at = None,

    sender_email_settings_id = 123L,

    email_thread_id = Some(1L),
    gmail_msg_id = None,
    gmail_thread_id = None,

    outlook_msg_id = None,
    outlook_conversation_id = None,
    outlook_response_json = None,

    gmail_fbl = None,
    list_unsubscribe_header = None,
    hasCustomTrackingDomain = false,
    rep_smtp_reverse_dns_host = None

  )

  val emailReplyStatus = EmailReplyStatus(
    replyType = EmailReplyType.NOT_CATEGORIZED,
    isReplied = false,
    isUnsubscribeRequest = false,
    isAutoReply = false,
    isOutOfOfficeReply = false,
    isInvalidEmail = false,
    isForwarded = false,
    bouncedData = None
  )

  val emailReplySavedV3 = EmailReplySavedV3(
    message_id = "some_message_id",
    email_body = "test body",
    email_scheduled_id = 345,
    email_thread_id = 456,
    by_account = true,
    sent_at = DateTime.now().minusDays(1),
    campaign_id = None,
    step_id = None,
    prospect_id_in_campaign = None,
    prospect_account_id_in_campaign = None,
    reply_type = EmailReplyType.NOT_CATEGORIZED,
    from_email = IEmailAddress(email = "<EMAIL>"),
    to_emails = Seq(IEmailAddress(email = "<EMAIL>"), IEmailAddress(email = "<EMAIL>")),
    cc_emails = Seq(),
    reply_to = Some(IEmailAddress(email = "<EMAIL>")),
    inbox_email_setting_id = 1,
    from_prospect = None,
    to_prospects = Seq(),
    campaign_associated_prospect = None,
    all_prospects_involved = Seq(),
    email_status = emailReplyStatus.copy(isReplied = true),
    subject = "email subject",
    text_body = "textBody",
    base_body = "htmlBody",
    full_headers = Json.parse("{}"),

    gmail_msg_id = None,

    gmail_thread_id = None,
    outlook_msg_id = None,
    outlook_conversation_id = None,

    outlook_response_json = None,
    team_id = TeamId(team_id),
    references_header = None,
    in_reply_to_header = None
  )

  val cpCompleted = CPCompleted(
    campaignId = 1,
    prospectId = 2,
    completed = true)
  val dBSavedRepliesRes = DBEmailMessagesSavedResponse(
    savedMessages = Seq(emailReplySavedV3),
    emailMessagesFromProspects = Seq(emailReplySavedV3),
    prospectIdsWithHardBouncedEmail = Seq(3),
    prospectIdsWithSoftBouncedEmail = Seq(),
    emailScheduledIdsWithHardBouncedEmail = Seq(4),
    emailScheduledIdsWithSoftBouncedEmail = Seq(),
    emailScheduledIdsWithAutoReplyEmail = Seq(5),
    emailScheduledIdsWithOutOfOfficeReplyEmail = Seq(6),
    completedWebhookData = Seq(cpCompleted),
    gmailSendingLimitErrorEmailSettingId = Some(12),
    hardBouncedReplies = Seq(),
    softBouncedReplies = Seq()
  )

  val tiid: String = InboxV3ServiceFixtures.tiid
  val settingsIdsAndChannel: SettingIdsAndChannelType = InboxV3ServiceFixtures.settingsIdsAndChannel

  val validatedInboxAndSettings: ValidatedInboxAndSettingData = ValidatedInboxAndSettingData(
    inbox_type_data = InboxTypeData.SINGLE_DATA(team_inbox_id = 145),
    settingsAndChannelType = settingsIdsAndChannel
  )


  given system: ActorSystem = TestAppExecutionContext.actorSystem
  given wSClient: AhcWSClient = TestAppExecutionContext.wsClient
  given actorContext: ExecutionContext = system.dispatcher

  "inboxV3Service.sendNewEmailManually" should (
    "return Left if to emails is empty"
    ) in {

    //    (teamInboxService.validateInboxTypeAndGetSettingIds)
    //      .expects(tiid, accountAdmin.internal_id, teamId, false, org.id)
    //      .returning(Right(settingsIdsAndChannel))

    inboxV3Service.sendNewEmailManually(
      data = sendNewManualEmailV2.copy(to = Seq()),
      org_id = org.id,
      accountId = accountAdmin.internal_id,
      teamId = teamId,
      loggedinAccount = accountAdmin,
      auditRequestLogId = "auditRequestLogId",
      permittedAccountIds = Seq(accountAdmin.internal_id),
      version = "v2",
      tiid = Some(tiid)
    ).map(result => assert(result.isLeft))

  }

  "inboxV3Service.sendNewEmailManually" should (
    "return Left if prospectServiceV2.findByEmail failed"
    ) in {

    (teamInboxService.validateInboxTypeAndGetSettingIds)
      .expects("v2",
        accountAdmin,
        validatedInboxAndSettings.inbox_type_data,
        Seq(accountAdmin.internal_id),
        teamId,
        Logger)
      .returning(Right(validatedInboxAndSettings))

    (prospectServiceV2.findByEmail)
      .expects(*, *, *)
      .returning(Failure(new Throwable("prospectServiceV2.findByEmail failed")))

    //    (() => repTrackingHostService.getRepTrackingHosts())
    //      .expects()
    //      .returning(Success(Seq()))

    inboxV3Service.sendNewEmailManually(
      data = sendNewManualEmailV2,
      org_id = org.id,
      accountId = accountAdmin.internal_id,
      teamId = teamId,
      loggedinAccount = accountAdmin,
      auditRequestLogId = "auditRequestLogId",
      permittedAccountIds = Seq(accountAdmin.internal_id),
      version = "v2",
      tiid = Some(tiid)
    ).map(result => assert(result.isLeft))

  }

  "inboxV3Service.sendNewEmailManually" should (
    "return Left if emailService.getSenderEmailSettingForManualEmailTask returns failure"
    ) in {

    (teamInboxService.validateInboxTypeAndGetSettingIds)
      .expects("v2",
        accountAdmin,
        validatedInboxAndSettings.inbox_type_data,
        Seq(accountAdmin.internal_id),
        teamId,
        Logger)
      .returning(Right(validatedInboxAndSettings))

    (prospectServiceV2.findByEmail)
      .expects(*, *, *)
      .returning(Success(Seq()))

    (emailService.getSenderEmailSettingForManualEmailTask)
      .expects(EmailSettingId(sendNewManualEmailV2.sender_email_setting_id), Seq(accountAdmin.internal_id), *)
      .returning(Failure(new Throwable("Sender email account not found")))

    (() => repTrackingHostService.getRepTrackingHosts())
      .expects()
      .returning(Success(Seq()))

    inboxV3Service.sendNewEmailManually(
      data = sendNewManualEmailV2,
      org_id = org.id,
      accountId = accountAdmin.internal_id,
      teamId = teamId,
      loggedinAccount = accountAdmin,
      auditRequestLogId = "auditRequestLogId",
      permittedAccountIds = Seq(accountAdmin.internal_id),
      version = "v2",
      tiid = Some(tiid)
    ).map(result => {
      assert(result.isLeft)
    })
  }

  "inboxV3Service.sendNewEmailManually" should (
    "return Left if owner of email_setting is not in permittedAccountIdsForEmailSetting"
    ) in {

    (teamInboxService.validateInboxTypeAndGetSettingIds)
      .expects("v2",
        accountAdmin,
        validatedInboxAndSettings.inbox_type_data,
        Seq(accountAdmin.internal_id),
        teamId,
        Logger)
      .returning(Right(validatedInboxAndSettings))

    (prospectServiceV2.findByEmail)
      .expects(*, *, *)
      .returning(Success(Seq()))

    (emailService.getSenderEmailSettingForManualEmailTask)
      .expects(EmailSettingId(sendNewManualEmailV2.sender_email_setting_id), Seq(accountAdmin.internal_id), *)
      .returning(Failure(new Throwable("You do not have the permission for the selected from-email")))

    (() => repTrackingHostService.getRepTrackingHosts())
      .expects()
      .returning(Success(Seq()))

    inboxV3Service.sendNewEmailManually(
      data = sendNewManualEmailV2,
      org_id = org.id,
      accountId = accountAdmin.internal_id,
      teamId = teamId,
      loggedinAccount = accountAdmin,
      auditRequestLogId = "auditRequestLogId",
      permittedAccountIds = Seq(accountAdmin.internal_id),
      version = "v2",
      tiid = Some(tiid)
    ).map(result => {
      assert(result.isLeft)
    })
  }

  "inboxV3Service.sendNewEmailManually" should (
    "return Left if getBodiesForInboxMessage fails"
    ) in {

    (teamInboxService.validateInboxTypeAndGetSettingIds)
      .expects("v2",
        accountAdmin,
        validatedInboxAndSettings.inbox_type_data,
        Seq(accountAdmin.internal_id),
        teamId,
        Logger)
      .returning(Right(validatedInboxAndSettings))

    (prospectServiceV2.findByEmail)
      .expects(*, *, *)
      .returning(Success(Seq(ProspectIdEmail(1L, "<EMAIL>", accountAdmin.internal_id))))

    (emailService.getSenderEmailSettingForManualEmailTask)
      .expects(EmailSettingId(sendNewManualEmailV2.sender_email_setting_id), Seq(accountAdmin.internal_id), *)
      .returning(Success(emailSetting1.copy(
        org_id = OrgId(id = org.id), // FIXME VALUECLASS
        owner_id = AccountId(id = accountAdmin.internal_id) // FIXME VALUECLASS
      )))

    (srUuidService.getThreadIdFromUuid(
      _: String,
      _: TeamId
    )(
      _: SRLogger
    ))
      .expects(*, *, *)
      .returning(Success(ThreadId.EmailThreadId(email_thread_id)))

    (emailThreadDAOService.findByIdInternalV3)
      .expects(*, *, *)
      .returning(Some(ThreadForProspectInternal.EmailThreadForProspectInternal(
        id = email_thread_id,
        team_id = teamId,
        owner_id = accountAdmin.internal_id,
        campaign_id = Some(1L),
        campaign_name = Some("Campaign1"),
        inbox_owner_id = AccountId(accountAdmin.internal_id),
        gmail_thread_id = Some("gmail_threadId")
      )))

    (emailScheduledDAO.findEmailToReplyToV3)
      .expects(*, *)
      .returning(Success(Some(emailScheduled)))

    (emailService.getBodiesForInboxMessage(_: EmailsScheduledUuid, _: Option[String], _: String, _: String, _: String, _: Boolean, _: Boolean, _: Seq[String], _:TeamId, _: OrgId)(using _: SRLogger))
      .expects(*, *, *, *, *, *, *, *, *, *, *)
      .returning(Failure(new Throwable("getBodiesForInboxMessage failed")))

    (() => repTrackingHostService.getRepTrackingHosts())
      .expects()
      .returning(Success(Seq(RepTrackingHosts(
        id = 1,
        host_url = "host_url",
        subdomain_based = false,
        active = true
      ))))

    inboxV3Service.sendNewEmailManually(
      data = sendNewManualEmailV2,
      org_id = org.id,
      accountId = accountAdmin.internal_id,
      teamId = teamId,
      loggedinAccount = accountAdmin,
      auditRequestLogId = "auditRequestLogId",
      permittedAccountIds = Seq(accountAdmin.internal_id),
      version = "v2",
      tiid = Some(tiid)
    ).map(result => {
      Logger.info(s"Result: $result")
      assert(result.isLeft)
    })
  }

  "inboxV3Service.sendNewEmailManually" should (
    "return Left if sendEmailViaSpecificHost fails"
    ) in {

    (teamInboxService.validateInboxTypeAndGetSettingIds)
      .expects("v2",
        accountAdmin,
        validatedInboxAndSettings.inbox_type_data,
        Seq(accountAdmin.internal_id),
        teamId,
        Logger)
      .returning(Right(validatedInboxAndSettings))

    (prospectServiceV2.findByEmail)
      .expects(*, *, *)
      .returning(Success(Seq(ProspectIdEmail(1L, "<EMAIL>", accountAdmin.internal_id))))

    (emailService.getSenderEmailSettingForManualEmailTask)
      .expects(EmailSettingId(sendNewManualEmailV2.sender_email_setting_id), Seq(accountAdmin.internal_id), *)
      .returning(Success(emailSetting1.copy(
        org_id = OrgId(id = org.id), // FIXME VALUECLASS 
        owner_id = AccountId(id = accountAdmin.internal_id) // FIXME VALUECLASS
      )))

    (srUuidService.getThreadIdFromUuid(
      _: String,
      _: TeamId
    )(
      _: SRLogger
    ))
      .expects(*, *, *)
      .returning(Success(ThreadId.EmailThreadId(email_thread_id)))

    (emailThreadDAOService.findByIdInternalV3)
      .expects(*, *, *)
      .returning(Some(ThreadForProspectInternal.EmailThreadForProspectInternal(
        id = email_thread_id,
        team_id = teamId,
        owner_id = accountAdmin.internal_id,
        campaign_id = Some(1L),
        campaign_name = Some("Campaign1"),
        inbox_owner_id = AccountId(accountAdmin.internal_id),
        gmail_thread_id = Some("gmail_threadId")
      )))

    (emailScheduledDAO.findEmailToReplyToV3)
      .expects(*, *)
      .returning(Success(Some(emailScheduled)))

    (emailService.getBodiesForInboxMessage(_: EmailsScheduledUuid, _: Option[String], _: String, _: String, _: String, _: Boolean, _: Boolean, _: Seq[String], _:TeamId, _: OrgId)(using _: SRLogger))
      .expects(*, *, *, *, *, *, *, *, *, *, *)
      .returning(Success(EmailServiceBody(
        subject = "email subject",
        textBody = "textBody",
        htmlBody = "htmlBody",
        baseBody = "baseBody",
        isEditedPreviewEmail = false,
        has_unsubscribe_link = false
      )))

    (() => repTrackingHostService.getRepTrackingHosts())
      .expects()
      .returning(Success(Seq(RepTrackingHosts(
        id = 1,
        host_url = "host_url",
        subdomain_based = false,
        active = true
      ))))

    (emailService.sendEmailViaSpecificHost(_: EmailSendMailRequest, _: String)(_: WSClient, _: SRLogger, _: ExecutionContext))
      .expects(*, *, *, *, *)
      .returning(Future.failed(new Throwable("sendEmailViaSpecificHost failed")))


    inboxV3Service.sendNewEmailManually(
      data = sendNewManualEmailV2,
      org_id = org.id,
      accountId = accountAdmin.internal_id,
      teamId = teamId,
      loggedinAccount = accountAdmin,
      auditRequestLogId = "auditRequestLogId",
      permittedAccountIds = Seq(accountAdmin.internal_id),
      version = "v2",
      tiid = Some(tiid)
    ).map(result => {
      Logger.info(s"Result: $result")
      assert(result.isLeft)
    })
  }

  "inboxV3Service.sendNewEmailManually" should (
    "return Email successfully sent but Left APIManualEmailSentButSavingFailed if saveEmailsAndRepliesFromInboxV3 fails"
    ) in {

    (teamInboxService.validateInboxTypeAndGetSettingIds)
      .expects("v2",
        accountAdmin,
        validatedInboxAndSettings.inbox_type_data,
        Seq(accountAdmin.internal_id),
        teamId,
        Logger)
      .returning(Right(validatedInboxAndSettings))

    (prospectServiceV2.findByEmail)
      .expects(*, *, *)
      .returning(Success(Seq(ProspectIdEmail(1L, "<EMAIL>", accountAdmin.internal_id))))

    (emailService.getSenderEmailSettingForManualEmailTask)
      .expects(EmailSettingId(sendNewManualEmailV2.sender_email_setting_id), Seq(accountAdmin.internal_id), *)
      .returning(Success(emailSetting1.copy(
        org_id = OrgId(id = org.id), // FIXME VALUECLASS
        owner_id = AccountId(id = accountAdmin.internal_id) // FIXME VALUECLASS
      )))

    (srUuidService.getThreadIdFromUuid(
      _: String,
      _: TeamId
    )(
      _: SRLogger
    ))
      .expects(*, *, *)
      .returning(Success(ThreadId.EmailThreadId(email_thread_id)))

    (emailThreadDAOService.findByIdInternalV3)
      .expects(*, *, *)
      .returning(Some(ThreadForProspectInternal.EmailThreadForProspectInternal(
        id = email_thread_id,
        team_id = teamId,
        owner_id = accountAdmin.internal_id,
        campaign_id = Some(1L),
        campaign_name = Some("Campaign1"),
        inbox_owner_id = AccountId(accountAdmin.internal_id),
        gmail_thread_id = Some("gmail_threadId")
      )))

    (emailScheduledDAO.findEmailToReplyToV3)
      .expects(*, *)
      .returning(Success(Some(emailScheduled)))

    (emailService.getBodiesForInboxMessage(_: EmailsScheduledUuid, _: Option[String], _: String, _: String, _: String, _: Boolean, _: Boolean, _: Seq[String], _:TeamId, _: OrgId)(using _: SRLogger))
      .expects(*, *, *, *, *, *, *, *, *, *, *)
      .returning(Success(EmailServiceBody(
        subject = "email subject",
        textBody = "textBody",
        htmlBody = "htmlBody",
        baseBody = "baseBody",
        isEditedPreviewEmail = false,
        has_unsubscribe_link = false
      )))

    (() => repTrackingHostService.getRepTrackingHosts())
      .expects()
      .returning(Success(Seq(RepTrackingHosts(
        id = 1,
        host_url = "host_url",
        subdomain_based = false,
        active = true
      ))))

    (emailService.sendEmailViaSpecificHost(_: EmailSendMailRequest, _: String)(_: WSClient, _: SRLogger, _: ExecutionContext))
      .expects(*, *, *, *, *)
      .returning(Future.successful(emailToBeSent))

    (prospectServiceV2.findByIdOrEmail)
      .expects(*, *, *,*)
      .returning(Success(Seq(ProspectCheckForIsSentEmail(1L, "<EMAIL>",accountAdmin.internal_id,None,None))))

    (emailReplyTrackingModelV2.saveEmailsAndRepliesFromInboxV3(_: Long, _: Long, _: Seq[EmailMessageTracked], _: EmailSetting, _: ReplyHandling.ReplyHandling, _: Account,
      _: Seq[String], _: Boolean, _: String, _: Boolean)(using _: SRLogger))
      .expects(*, *, *, *, *, *, *, *, *, *, *)
      .returning(Failure(new Throwable("saveEmailsAndRepliesFromInboxV3 failed")))

    inboxV3Service.sendNewEmailManually(
      data = sendNewManualEmailV2,
      org_id = org.id,
      accountId = accountAdmin.internal_id,
      teamId = teamId,
      loggedinAccount = accountAdmin,
      auditRequestLogId = "auditRequestLogId",
      permittedAccountIds = Seq(accountAdmin.internal_id),
      version = "v2",
      tiid = Some(tiid)
    ).map(result => {
      Logger.info(s"Result: $result")
      assert(result.isLeft)
    })
  }

  "inboxV3Service.sendNewEmailManually" should (
    "return Email sent if sent and saved successfully"
    ) in {

    (teamInboxService.validateInboxTypeAndGetSettingIds)
      .expects("v2",
        accountAdmin,
        validatedInboxAndSettings.inbox_type_data,
        Seq(accountAdmin.internal_id),
        teamId,
        Logger)
      .returning(Right(validatedInboxAndSettings))

    (prospectServiceV2.findByEmail)
      .expects(*, *, *)
      .returning(Success(Seq(ProspectIdEmail(1L, "<EMAIL>", accountAdmin.internal_id))))

    (emailService.getSenderEmailSettingForManualEmailTask)
      .expects(EmailSettingId(sendNewManualEmailV2.sender_email_setting_id), Seq(accountAdmin.internal_id), *)
      .returning(Success(emailSetting1.copy(
        org_id = OrgId(id = org.id), // FIXME VALUECLASS
        owner_id = AccountId(id = accountAdmin.internal_id) // FIXME VALUECLASS
      )))

    (srUuidService.getThreadIdFromUuid(
      _: String,
      _: TeamId
    )(
      _: SRLogger
    ))
      .expects(*, *, *)
      .returning(Success(ThreadId.EmailThreadId(email_thread_id)))

    (emailThreadDAOService.findByIdInternalV3)
      .expects(*, *, *)
      .returning(Some(ThreadForProspectInternal.EmailThreadForProspectInternal(
        id = email_thread_id,
        team_id = teamId,
        owner_id = accountAdmin.internal_id,
        campaign_id = Some(1L),
        campaign_name = Some("Campaign1"),
        inbox_owner_id = AccountId(accountAdmin.internal_id),
        gmail_thread_id = Some("gmail_threadId")
      )))

    (emailScheduledDAO.findEmailToReplyToV3)
      .expects(*, *)
      .returning(Success(Some(emailScheduled)))

    (emailService.getBodiesForInboxMessage(_: EmailsScheduledUuid, _: Option[String], _: String, _: String, _: String, _: Boolean, _: Boolean, _: Seq[String], _:TeamId, _: OrgId)(using _: SRLogger))
      .expects(*, *, *, *, *, *, *, *, *, *, *)
      .returning(Success(EmailServiceBody(
        subject = "email subject",
        textBody = "textBody",
        htmlBody = "htmlBody",
        baseBody = "baseBody",
        isEditedPreviewEmail = false,
        has_unsubscribe_link = false
      )))

    (() => repTrackingHostService.getRepTrackingHosts())
      .expects()
      .returning(Success(Seq(RepTrackingHosts(
        id = 1,
        host_url = "host_url",
        subdomain_based = false,
        active = true
      ))))

    (emailService.sendEmailViaSpecificHost(_: EmailSendMailRequest, _: String)(_: WSClient, _: SRLogger, _: ExecutionContext))
      .expects(*, *, *, *, *)
      .returning(Future.successful(emailToBeSent))

    (emailReplyTrackingModelV2.saveEmailsAndRepliesFromInboxV3(_: Long, _: Long, _: Seq[EmailMessageTracked], _: EmailSetting, _: ReplyHandling.ReplyHandling, _: Account,
      _: Seq[String], _: Boolean, _: String, _: Boolean)(using _: SRLogger))
      .expects(*, *, *, *, *, *, *, *, *, *, *)
      .returning(Success(dBSavedRepliesRes))

    (prospectServiceV2.findByIdOrEmail)
      .expects(*, *, *,*)
      .returning(Success(Seq(ProspectCheckForIsSentEmail(1L, "<EMAIL>",accountAdmin.internal_id,None,None))))

    (accountOrgBillingRelatedService.accountBillingDateAndCurrentSentAccount)
      .expects(*)
      .returning(Success(AccountBillingData(
        orgId = OrgId(2),
        currentBillingCycleStartedAt = DateTime.now().minusDays(2)
      )))

    (accountOrgBillingRelatedService.checkAndUpdateProspectsContacted)
      .expects(*,*,*,*,*,*)
      .returning(Success(1))

    (emailCommonService.incrementSentProspectCountIfNeeded(_:Option[ProspectCheckForIsSentEmail],_:DateTime, _: Long)(using _: SRLogger))
      .expects(*,*,*,*)
      .returning(())

    (mqActivityTriggerPublisher.publishEvents)
      .expects(*)
      .returning(())

    inboxV3Service.sendNewEmailManually(
      data = sendNewManualEmailV2,
      org_id = org.id,
      accountId = accountAdmin.internal_id,
      teamId = teamId,
      loggedinAccount = accountAdmin,
      auditRequestLogId = "auditRequestLogId",
      permittedAccountIds = Seq(accountAdmin.internal_id),
      version = "v2",
      tiid = Some(tiid)
    ).map(result => {
      assert(result.toOption.get.toString.contains("Email sent"))
    })
  }

  "inboxV3Service.sendNewEmailManually" should (
    "return Email sent if sent and received with different email_setting_id"
    ) in {

    (teamInboxService.validateInboxTypeAndGetSettingIds)
      .expects("v2",
        accountAdmin,
        validatedInboxAndSettings.inbox_type_data,
        Seq(accountAdmin.internal_id),
        teamId,
        Logger)
      .returning(Right(validatedInboxAndSettings))

    (prospectServiceV2.findByEmail)
      .expects(*, *, *)
      .returning(Success(Seq(ProspectIdEmail(1L, "<EMAIL>", accountAdmin.internal_id))))

    (emailService.getSenderEmailSettingForManualEmailTask)
      .expects(EmailSettingId(sendNewManualEmailV2.sender_email_setting_id), Seq(accountAdmin.internal_id), *)
      .returning(Success(emailSetting1.copy(
        org_id = OrgId(id = org.id), // FIXME VALUECLASS
        owner_id = AccountId(id = accountAdmin.internal_id) // FIXME VALUECLASS
      )))

    (srUuidService.getThreadIdFromUuid(
      _: String,
      _: TeamId
    )(
      _: SRLogger
    ))
      .expects(*, *, *)
      .returning(Success(ThreadId.EmailThreadId(email_thread_id)))

    (emailThreadDAOService.findByIdInternalV3)
      .expects(*, *, *)
      .returning(Some(ThreadForProspectInternal.EmailThreadForProspectInternal(
        id = email_thread_id,
        team_id = teamId,
        owner_id = accountAdmin.internal_id,
        campaign_id = Some(1L),
        campaign_name = Some("Campaign1"),
        inbox_owner_id = AccountId(accountAdmin.internal_id),
        gmail_thread_id = Some("gmail_threadId")
      )))

    (emailSettingDAO.find(_: EmailSettingId, _: TeamId))
      .expects(*, *)
      .returning(Some(emailSetting1.copy(
        org_id = OrgId(id = org.id), // FIXME VALUECLASS
        owner_id = AccountId(id = accountAdmin.internal_id) // FIXME VALUECLASS
      )))

    (emailScheduledDAO.findEmailToReplyToV3)
      .expects(*, *)
      .returning(Success(Some(emailScheduled)))

    (emailService.getBodiesForInboxMessage(_: EmailsScheduledUuid, _: Option[String], _: String, _: String, _: String, _: Boolean, _: Boolean, _: Seq[String], _:TeamId, _: OrgId)(using _: SRLogger))
      .expects(*, *, *, *, *, *, *, *, *, *, *)
      .returning(Success(EmailServiceBody(
        subject = "email subject",
        textBody = "textBody",
        htmlBody = "htmlBody",
        baseBody = "baseBody",
        isEditedPreviewEmail = false,
        has_unsubscribe_link = false
      )))

    (() => repTrackingHostService.getRepTrackingHosts())
      .expects()
      .returning(Success(Seq(RepTrackingHosts(
        id = 1,
        host_url = "host_url",
        subdomain_based = false,
        active = true
      ))))

    (emailService.sendEmailViaSpecificHost(_: EmailSendMailRequest, _: String)(_: WSClient, _: SRLogger, _: ExecutionContext))
      .expects(*, *, *, *, *)
      .returning(Future.successful(emailToBeSent))

    (emailReplyTrackingModelV2.saveEmailsAndRepliesFromInboxV3(_: Long, _: Long, _: Seq[EmailMessageTracked], _: EmailSetting, _: ReplyHandling.ReplyHandling, _: Account,
      _: Seq[String], _: Boolean, _: String, _: Boolean)(using _: SRLogger))
      .expects(*, *, *, *, *, *, *, *, *, *, *)
      .returning(Success(dBSavedRepliesRes))

    (prospectServiceV2.findByIdOrEmail)
      .expects(*, *, *,*)
      .returning(Success(Seq(ProspectCheckForIsSentEmail(1L, "<EMAIL>",accountAdmin.internal_id,None,None))))


    (accountOrgBillingRelatedService.accountBillingDateAndCurrentSentAccount)
      .expects(*)
      .returning(Success(AccountBillingData(
        orgId = OrgId(2),
        currentBillingCycleStartedAt = DateTime.now().minusDays(2)
      )))

    (accountOrgBillingRelatedService.checkAndUpdateProspectsContacted)
      .expects(*,*,*,*,*,*)
      .returning(Success(1))

    (emailCommonService.incrementSentProspectCountIfNeeded(_:Option[ProspectCheckForIsSentEmail],_:DateTime, _: Long)(using _: SRLogger))
      .expects(*,*,*,*)
      .returning(())

    (mqActivityTriggerPublisher.publishEvents)
      .expects(*)
      .returning(())

    inboxV3Service.sendNewEmailManually(
      data = sendNewManualEmailV2.copy(receiver_email_setting_id = Some(2L)),
      org_id = org.id,
      accountId = accountAdmin.internal_id,
      teamId = teamId,
      loggedinAccount = accountAdmin,
      auditRequestLogId = "auditRequestLogId",
      permittedAccountIds = Seq(accountAdmin.internal_id),
      version = "v2",
      tiid = Some(tiid)
    ).map(result => {
      assert(result.toOption.get.toString.contains("Email sent"))
    })
  }

  "inboxV3Service.sendNewEmailManually" should (
    "return Email sent if sent with cc and bcc emails"
    ) in {

    (teamInboxService.validateInboxTypeAndGetSettingIds)
      .expects("v2",
        accountAdmin,
        validatedInboxAndSettings.inbox_type_data,
        Seq(accountAdmin.internal_id),
        teamId,
        Logger)
      .returning(Right(validatedInboxAndSettings))

    (prospectServiceV2.findByEmail)
      .expects(*, *, *)
      .returning(Success(Seq(ProspectIdEmail(1L, "<EMAIL>", accountAdmin.internal_id))))

    (emailService.getSenderEmailSettingForManualEmailTask)
      .expects(EmailSettingId(sendNewManualEmailV2.sender_email_setting_id), Seq(accountAdmin.internal_id), *)
      .returning(Success(emailSetting1.copy(
        org_id = OrgId(id = org.id), // FIXME VALUECLASS
        owner_id = AccountId(id = accountAdmin.internal_id) // FIXME VALUECLASS
      )))

    (srUuidService.getThreadIdFromUuid(
      _: String,
      _: TeamId
    )(
      _: SRLogger
    ))
      .expects(*, *, *)
      .returning(Success(ThreadId.EmailThreadId(email_thread_id)))

    (emailThreadDAOService.findByIdInternalV3)
      .expects(*, *, *)
      .returning(Some(ThreadForProspectInternal.EmailThreadForProspectInternal(
        id = email_thread_id,
        team_id = teamId,
        owner_id = accountAdmin.internal_id,
        campaign_id = Some(1L),
        campaign_name = Some("Campaign1"),
        inbox_owner_id = AccountId(accountAdmin.internal_id),
        gmail_thread_id = Some("gmail_threadId")
      )))

    (emailScheduledDAO.findEmailToReplyToV3)
      .expects(*, *)
      .returning(Success(Some(emailScheduled)))

    (emailService.getBodiesForInboxMessage(_: EmailsScheduledUuid, _: Option[String], _: String, _: String, _: String, _: Boolean, _: Boolean, _: Seq[String], _:TeamId, _: OrgId)(using _: SRLogger))
      .expects(*, *, *, *, *, *, *, *, *, *, *)
      .returning(Success(EmailServiceBody(
        subject = "email subject",
        textBody = "textBody",
        htmlBody = "htmlBody",
        baseBody = "baseBody",
        isEditedPreviewEmail = false,
        has_unsubscribe_link = false
      )))

    (() => repTrackingHostService.getRepTrackingHosts())
      .expects()
      .returning(Success(Seq(RepTrackingHosts(
        id = 1,
        host_url = "host_url",
        subdomain_based = false,
        active = true
      ))))

    (emailService.sendEmailViaSpecificHost(_: EmailSendMailRequest, _: String)(_: WSClient, _: SRLogger, _: ExecutionContext))
      .expects(*, *, *, *, *)
      .returning(Future.successful(emailToBeSent.copy(cc_emails = cc_emails, bcc_emails = bcc_emails)))

    (emailReplyTrackingModelV2.saveEmailsAndRepliesFromInboxV3(_: Long, _: Long, _: Seq[EmailMessageTracked], _: EmailSetting, _: ReplyHandling.ReplyHandling, _: Account,
      _: Seq[String], _: Boolean, _: String, _: Boolean)(using _: SRLogger))
      .expects(*, *, *, *, *, *, *, *, *, *, *)
      .returning(Success(dBSavedRepliesRes))

    (prospectServiceV2.findByIdOrEmail)
      .expects(*, *, *,*)
      .returning(Success(Seq(ProspectCheckForIsSentEmail(1L, "<EMAIL>",accountAdmin.internal_id,None,None))))


    (accountOrgBillingRelatedService.accountBillingDateAndCurrentSentAccount)
      .expects(*)
      .returning(Success(AccountBillingData(
        orgId = OrgId(2),
        currentBillingCycleStartedAt = DateTime.now().minusDays(2)
      )))

    (accountOrgBillingRelatedService.checkAndUpdateProspectsContacted)
      .expects(*,*,*,*,*,*)
      .returning(Success(1))

    (emailCommonService.incrementSentProspectCountIfNeeded(_:Option[ProspectCheckForIsSentEmail],_:DateTime, _: Long)(using _: SRLogger))
      .expects(*,*,*,*)
      .returning(())

    
    (mqActivityTriggerPublisher.publishEvents)
      .expects(*)
      .returning(())

    inboxV3Service.sendNewEmailManually(
      data = sendNewManualEmailV2.copy(cc_emails = Some(Seq(IEmailAddress(email = "<EMAIL>"))),
        bcc_emails = Some(Seq(IEmailAddress(email = "<EMAIL>")))),
      org_id = org.id,
      accountId = accountAdmin.internal_id,
      teamId = teamId,
      loggedinAccount = accountAdmin,
      auditRequestLogId = "auditRequestLogId",
      permittedAccountIds = Seq(accountAdmin.internal_id),
      version = "v2",
      tiid = Some(tiid)
    ).map(result => {
      assert(result.toOption.get.toString.contains("Email sent"))
    })
  }

  val thread_id_seq = Seq(10L, 11L, 12L)

  val team_id2 = TeamId(id = if (AppConfig.isProd) 8835L else 80L)

  "inboxV3Service.changeArchiveStatusOfConvInboxV3" should (
    "update folder to archive if team_id in AppConfig for folder enum and archive is true"
    ) in {

    (emailThreadDAOService.updateInboxFolderTypeTry)
      .expects(thread_id_seq, team_id2, FolderType.DONE)
      .returning(Success(1))

    val result = inboxV3Service.changeArchiveStatusOfConvInboxV3(
      threadIds = thread_id_seq,
      channelType = ChannelType.EmailChannel,
      teamId = team_id2,
      archive = true
    )

    assert(result.isRight)
  }

  "inboxV3Service.changeArchiveStatusOfConvInboxV3" should (
    "update folder to prospects if archive is false and thread_id present in email_threads_prospects"
    ) in {

    (() => dbUtils.startLocalTx())
      .expects()
      .returning(DbAndSession(null, null))

    (emailThreadDAOService.getThreadIdsExistingInEmailThreadsProspects(_: Seq[Long], _: TeamId)(_: DBSession))
      .expects(*, team_id2, *)
      .returning(Success(thread_id_seq.toList))

    (emailThreadDAOService.updateInboxFolderType(_: Seq[Long], _: TeamId, _: FolderType, _: Boolean, _: Option[DateTime])(_: DBSession))
      .expects(*, team_id2, FolderType.PROSPECTS, *, *, *)
      .returning(Success(3))

    (dbUtils.commitAndCloseSession)
      .expects(*)
      .returning(())

    val result = inboxV3Service.changeArchiveStatusOfConvInboxV3(
      threadIds = thread_id_seq,
      channelType = ChannelType.EmailChannel,
      teamId = team_id2,
      archive = false
    )

    assert(result.isRight)
  }

  "inboxV3Service.changeArchiveStatusOfConvInboxV3" should (
    "update folder to non-prospects if archive is false and thread_id not present in email_threads_prospects"
    ) in {

    (() => dbUtils.startLocalTx())
      .expects()
      .returning(DbAndSession(null, null))

    (emailThreadDAOService.getThreadIdsExistingInEmailThreadsProspects(_: Seq[Long], _: TeamId)(_: DBSession))
      .expects(*, team_id2, *)
      .returning(Success(List()))

    (emailThreadDAOService.updateInboxFolderType(_: Seq[Long], _: TeamId, _: FolderType, _: Boolean, _: Option[DateTime])(_: DBSession))
      .expects(*, team_id2, FolderType.NON_PROSPECTS, *, *, *)
      .returning(Success(3))

    (dbUtils.commitAndCloseSession)
      .expects(*)
      .returning(())

    val result = inboxV3Service.changeArchiveStatusOfConvInboxV3(
      threadIds = thread_id_seq,
      channelType = ChannelType.EmailChannel,
      teamId = team_id2,
      archive = false
    )

    assert(result.isRight)
  }

  "inboxV3Service.changeArchiveStatusOfConvInboxV3" should (
    "return left if archive is true but there is db failure while changing folder"
    ) in {

    (emailThreadDAOService.updateInboxFolderTypeTry)
      .expects(thread_id_seq, team_id2, FolderType.DONE)
      .returning(Failure(new Exception("DB failure")))

    val result = inboxV3Service.changeArchiveStatusOfConvInboxV3(
      threadIds = thread_id_seq,
      channelType = ChannelType.EmailChannel,
      teamId = team_id2,
      archive = true
    )

    assert(result.isLeft)
  }

  "inboxV3Service.changeArchiveStatusOfConvInboxV3" should (
    "update folder to non-prospects/prospects correctly based on getThreadIdsExistingInEmailThreadsProspects returned list"
    ) in {

    (() => dbUtils.startLocalTx())
      .expects()
      .returning(DbAndSession(null, null))

    (emailThreadDAOService.getThreadIdsExistingInEmailThreadsProspects(_: Seq[Long], _: TeamId)(_: DBSession))
      .expects(*, team_id2, *)
      .returning(Success(List(10L)))

    (emailThreadDAOService.updateInboxFolderType(_: Seq[Long], _: TeamId, _: FolderType, _: Boolean, _: Option[DateTime])(_: DBSession))
      .expects(*, team_id2, FolderType.NON_PROSPECTS, *, *, *)
      .returning(Success(2))

    (emailThreadDAOService.updateInboxFolderType(_: Seq[Long], _: TeamId, _: FolderType, _: Boolean, _: Option[DateTime])(_: DBSession))
      .expects(*, team_id2, FolderType.PROSPECTS, *, *, *)
      .returning(Success(1))

    (dbUtils.commitAndCloseSession)
      .expects(*)
      .returning(())

    val result = inboxV3Service.changeArchiveStatusOfConvInboxV3(
      threadIds = thread_id_seq,
      channelType = ChannelType.EmailChannel,
      teamId = team_id2,
      archive = false
    )

    assert(result.isRight)
  }

  val threadIds = List(11L, 22L)
  val snooze_till = DateTime.now().plusDays(2)

  "inboxV3Service.changeArchiveStatusOfConvInboxV3" should (
    "update folder to snoozed if snooze is true"
    ) in {

    (emailThreadDAOService.changeFolderToSnooze)
      .expects(threadIds, Some(snooze_till), team_id2)
      .returning(Success(1))

    val result = inboxV3Service.changeSnoozeStatusOfConvInboxV3(
      threadIds = threadIds,
      channelType = ChannelType.EmailChannel,
      snooze = true,
      snoozed_till = Some(snooze_till),
      teamId = team_id2
    )

    assert(result.isRight)
  }


  "inboxV3Service.changeArchiveStatusOfConvInboxV3" should (
    "update folder to prospects if snooze is false and thread_id present in email_threads_prospects"
    ) in {

    (() => dbUtils.startLocalTx())
      .expects()
      .returning(DbAndSession(null, null))

    (emailThreadDAOService.getThreadIdsExistingInEmailThreadsProspects(_: Seq[Long], _: TeamId)(_: DBSession))
      .expects(*, team_id2, *)
      .returning(Success(threadIds.map(_.toLong)))

    (emailThreadDAOService.updateInboxFolderType(_: Seq[Long], _: TeamId, _: FolderType, _: Boolean, _: Option[DateTime])(_: DBSession))
      .expects(*, team_id2, FolderType.PROSPECTS, *, *, *)
      .returning(Success(2))

    (dbUtils.commitAndCloseSession)
      .expects(*)
      .returning(())

    val result = inboxV3Service.changeSnoozeStatusOfConvInboxV3(
      threadIds = threadIds,
      channelType = ChannelType.EmailChannel,
      snooze = false,
      snoozed_till = Some(snooze_till),
      teamId = team_id2
    )

    assert(result.isRight)
  }

  "inboxV3Service.changeArchiveStatusOfConvInboxV3" should (
    "update folder to non-prospects if snooze is false and thread_id is not present in email_threads_prospects"
    ) in {

    (() => dbUtils.startLocalTx())
      .expects()
      .returning(DbAndSession(null, null))

    (emailThreadDAOService.getThreadIdsExistingInEmailThreadsProspects(_: Seq[Long], _: TeamId)(_: DBSession))
      .expects(*, team_id2, *)
      .returning(Success(List()))

    (emailThreadDAOService.updateInboxFolderType(_: Seq[Long], _: TeamId, _: FolderType, _: Boolean, _: Option[DateTime])(_: DBSession))
      .expects(*, team_id2, FolderType.NON_PROSPECTS, *, *, *)
      .returning(Success(2))

    (dbUtils.commitAndCloseSession)
      .expects(*)
      .returning(())

    val result = inboxV3Service.changeSnoozeStatusOfConvInboxV3(
      threadIds = threadIds,
      channelType = ChannelType.EmailChannel,
      snooze = false,
      snoozed_till = Some(snooze_till),
      teamId = team_id2
    )

    assert(result.isRight)
  }

  val threadAndTeamId_1 = ThreadAndTeamId(
    thread_id = 122,
    teamId = 2L
  )

  val threadAndTeamId_2 = ThreadAndTeamId(
    thread_id = 124,
    teamId = 2L
  )

  "inboxV3Service.unsnoozeThreadAutomatically" should (
    "unsnoozeAutomatically  with 2 records"
    ) in {

    (() => dbUtils.startLocalTx())
      .expects()
      .returning(DbAndSession(null, null))

    (emailThreadDAOService.getThreadIdsExistingInEmailThreadsProspects(_: Seq[Long], _: TeamId)(_: DBSession))
      .expects(List(122L, 124L), TeamId(2L), *)
      .returning(Success(List(122L, 124L)))

    (emailThreadDAOService.updateInboxFolderType(_: Seq[Long], _: TeamId, _: FolderType, _: Boolean, _: Option[DateTime])(_: DBSession))
      .expects(Seq(122L), TeamId(2L), FolderType.PROSPECTS, *, *, *)
      .returning(Success(2))

    (emailThreadDAOService.updateInboxFolderType(_: Seq[Long], _: TeamId, _: FolderType, _: Boolean, _: Option[DateTime])(_: DBSession))
      .expects(Seq(124L), TeamId(2L), FolderType.PROSPECTS, *, *, *)
      .returning(Success(2))

    (dbUtils.commitAndCloseSession)
      .expects(*)
      .returning(())

    inboxV3Service.unsnoozeThreadAutomatically(
      threadsAndTeamIds = List(threadAndTeamId_1, threadAndTeamId_2)
    ).map(res => {
      assert(res.isRight)
    }
    )


  }

  val conversationsSearchResponse = ConversationsSearchResponse(
    conv_id = "10", team_inbox_id = Some(11L), title = "product demo", description = "<EMAIL>"
  )

  "inboxV3Service.getConversationIdsForSearch" should (
    "return list of threads successfully when no filters applied"
    ) in {

    (emailThreadDAOService.getQueryForSearchInboxEmailThreads(_: Long, _: String, _: Seq[Long], _: InboxType, _: Option[DateTime], _:Boolean, _:Option[Long], _:Option[String], _: Option[FolderType])(using _: SRLogger))
      .expects(*, *, *, InboxType.CONSOLIDATED, *, *, *, *, *, *)
      .returning(sqls"")

    (linkedinMessageThreadsDAO.getQueryForSearchInboxEmailThreads(_: Long, _: String, _: Seq[Long], _: InboxType, _: Option[DateTime],  _:Boolean, _:Option[Long], _:Option[String], _: Option[FolderType])(using _: SRLogger))
      .expects(*, *, *, InboxType.CONSOLIDATED, *, *, *, *, *, *)
      .returning(sqls"")

    (emailThreadDAOService.getConversationsForSearch(_: SQLSyntax, _: InboxType)(using _: SRLogger))
      .expects(*, InboxType.CONSOLIDATED, *)
      .returning(Success(List(conversationsSearchResponse)))

    (linkedinMessageThreadsDAO.getConversationsForSearch(_: SQLSyntax, _: InboxType)(using _: SRLogger))
      .expects(*, InboxType.CONSOLIDATED, *)
      .returning(Success(List()))

    val res: Either[GetConversationIdsForSearchFailure, List[ConversationsSearchResponse]] = inboxV3Service.getConversationIdsForSearch(
      team_id = teamId,
      search_key = Some("product"),
      esets = Seq(2L),
      lsets = Seq(5L),
      InboxType.CONSOLIDATED,
      duration = None,
      prospect_id = None,
      prospect_account_id = None,
      are_all_esets_accessible = false,
      sent_at = None,
      last_sent_id = None,
      folder_type = None
    )

    assert(res.isRight)
  }

  "inboxV3Service.getConversationIdsForSearch" should (
    "return list of threads successfully when duration filter is applied"
    ) in {

    (emailThreadDAOService.getQueryForSearchInboxEmailThreads(_: Long, _: String, _: Seq[Long], _: InboxType, _: Option[DateTime],  _:Boolean, _:Option[Long], _:Option[String], _: Option[FolderType])(using _: SRLogger))
      .expects(*, *, *, InboxType.CONSOLIDATED, *, *, *, *, *, *)
      .returning(sqls"")

    (linkedinMessageThreadsDAO.getQueryForSearchInboxEmailThreads(_: Long, _: String, _: Seq[Long], _: InboxType, _: Option[DateTime],  _:Boolean, _:Option[Long], _:Option[String], _: Option[FolderType])(using _: SRLogger))
      .expects(*, *, *, InboxType.CONSOLIDATED, *, *, *, *, *, *)
      .returning(sqls"")

    (emailThreadDAOService.getConversationsForSearch(_: SQLSyntax, _: InboxType)(using _: SRLogger))
      .expects(*, InboxType.CONSOLIDATED, *)
      .returning(Success(List(conversationsSearchResponse)))

    (linkedinMessageThreadsDAO.getConversationsForSearch(_: SQLSyntax, _: InboxType)(using _: SRLogger))
      .expects(*, InboxType.CONSOLIDATED, *)
      .returning(Success(List()))

    val res: Either[GetConversationIdsForSearchFailure, List[ConversationsSearchResponse]] = inboxV3Service.getConversationIdsForSearch(
      team_id = teamId,
      search_key = Some("product"),
      esets = Seq(2L),
      lsets = Seq(7L),
      InboxType.CONSOLIDATED,
      duration = Some("one_week"),
      prospect_id = None,
      prospect_account_id = None,
      are_all_esets_accessible = false,
      sent_at = None,
      last_sent_id = None,
      folder_type = None
    )

    assert(res.isRight)
  }

  "inboxV3Service.getConversationIdsForSearch" should (
    "return list of threads successfully when folder_type filter applied"
    ) in {

    (emailThreadDAOService.getQueryForSearchInboxEmailThreads(_: Long, _: String, _: Seq[Long], _: InboxType, _: Option[DateTime],  _:Boolean, _:Option[Long], _:Option[String], _: Option[FolderType])(using _: SRLogger))
      .expects(*, *, *, InboxType.CONSOLIDATED, *, *, *, *, *, *)
      .returning(sqls"")

    (linkedinMessageThreadsDAO.getQueryForSearchInboxEmailThreads(_: Long, _: String, _: Seq[Long], _: InboxType, _: Option[DateTime],  _:Boolean, _:Option[Long], _:Option[String], _: Option[FolderType])(using _: SRLogger))
      .expects(*, *, *, InboxType.CONSOLIDATED, *, *, *, *, *, *)
      .returning(sqls"")

    (emailThreadDAOService.getConversationsForSearch(_: SQLSyntax, _: InboxType)(using _: SRLogger))
      .expects(*, InboxType.CONSOLIDATED, *)
      .returning(Success(List(conversationsSearchResponse)))

    (linkedinMessageThreadsDAO.getConversationsForSearch(_: SQLSyntax, _: InboxType)(using _: SRLogger))
      .expects(*, InboxType.CONSOLIDATED, *)
      .returning(Success(List()))

    val res: Either[GetConversationIdsForSearchFailure, List[ConversationsSearchResponse]] = inboxV3Service.getConversationIdsForSearch(
      team_id = teamId,
      search_key = Some("product"),
      esets = Seq(2L),
      lsets = Seq(5L),
      InboxType.CONSOLIDATED,
      duration = None,
      prospect_id = None,
      prospect_account_id = None,
      are_all_esets_accessible = false,
      sent_at = None,
      last_sent_id = None,
      folder_type = Some("prospects")
    )

    assert(res.isRight)
  }

  "inboxV3Service.getConversationIdsForSearch" should (
    "return list of threads successfully when prospect_id filter applied"
    ) in {

    (emailThreadDAOService.getQueryForSearchInboxEmailThreadsForProspect(_: Long, _: Seq[Long], _: Option[ProspectId], _: Option[ProspectAccountsId],  _:Boolean, _:Option[Long], _:Option[String], _: InboxType))
      .expects(*, *, Some(ProspectId(1L)), None, *, *, *, InboxType.AllCampaigns)
      .returning(sqls"")

    (linkedinMessageThreadsDAO.getQueryForSearchInboxEmailThreads(_: Long, _: String, _: Seq[Long], _: InboxType, _: Option[DateTime], _: Boolean, _: Option[Long], _: Option[String], _: Option[FolderType])(using _: SRLogger))
      .expects(*, *, *, InboxType.AllCampaigns, *, *, *, *, *, *)

    (emailThreadDAOService.getConversationsForSearch(_: SQLSyntax, _: InboxType)(using _: SRLogger))
      .expects(*, InboxType.AllCampaigns, *)
      .returning(Success(List(conversationsSearchResponse)))

    (linkedinMessageThreadsDAO.getConversationsForSearch(_: SQLSyntax, _: InboxType)(using _: SRLogger))
      .expects(*, InboxType.AllCampaigns, *)
      .returning(Success(List()))


    val res: Either[GetConversationIdsForSearchFailure, List[ConversationsSearchResponse]] = inboxV3Service.getConversationIdsForSearch(
      team_id = teamId,
      search_key = None,
      esets = Seq(2L),
      lsets = Seq(5L),
      InboxType.AllCampaigns,
      duration = None,
      prospect_id = Some(ProspectId(1L)),
      prospect_account_id = None,
      are_all_esets_accessible = false,
      sent_at = None,
      last_sent_id = None,
      folder_type = Some("prospects")
    )

    assert(res.isRight)
  }

  "inboxV3Service.getConversationIdsForSearch" should (
    "return exception if wrong folder name is sent"
    ) in {

    val res: Either[GetConversationIdsForSearchFailure, List[ConversationsSearchResponse]] = inboxV3Service.getConversationIdsForSearch(
      team_id = teamId,
      search_key = Some("product"),
      esets = Seq(2L),
      lsets = Seq(),
      InboxType.CONSOLIDATED,
      duration = None,
      prospect_id = None,
      prospect_account_id = None,
      are_all_esets_accessible = false,
      sent_at = None,
      last_sent_id = None,
      folder_type = Some("prospect")
    )

    assert(res.isLeft)
  }

  "inboxV3Service.getConversationIdsForSearch" should (
    "return exception if db failure"
    ) in {

    (emailThreadDAOService.getQueryForSearchInboxEmailThreads(_: Long, _: String, _: Seq[Long], _: InboxType, _: Option[DateTime],  _:Boolean, _:Option[Long], _:Option[String], _: Option[FolderType])(using _: SRLogger))
      .expects(*, *, *, InboxType.CONSOLIDATED, *, *, *, *, *, *)
      .returning(sqls"")

    (linkedinMessageThreadsDAO.getQueryForSearchInboxEmailThreads(_: Long, _: String, _: Seq[Long], _: InboxType, _: Option[DateTime],  _:Boolean, _:Option[Long], _:Option[String], _: Option[FolderType])(using _: SRLogger))
      .expects(*, *, *, InboxType.CONSOLIDATED, *, *, *, *, *, *)
      .returning(sqls"")

    (emailThreadDAOService.getConversationsForSearch(_: SQLSyntax, _: InboxType)(using _: SRLogger))
      .expects(*, InboxType.CONSOLIDATED, *)
      .returning(Failure(new Throwable("DB failure")))

    val res: Either[GetConversationIdsForSearchFailure, List[ConversationsSearchResponse]] = inboxV3Service.getConversationIdsForSearch(
      team_id = teamId,
      search_key = Some("product"),
      esets = Seq(2L),
      lsets = Seq()
      , InboxType.CONSOLIDATED,
      duration = None,
      prospect_id = None,
      prospect_account_id = None,
      are_all_esets_accessible = false,
      sent_at = None,
      last_sent_id = None,
      folder_type = Some("prospects")
    )

    assert(res.isLeft)
  }

  val search_key = "product"
  val esets = Seq(12L, 33L)

  val send_new_manual_email = InboxV3ServiceFixtures.send_new_manual_email
  val account_fix: Account = InboxV3ServiceFixtures.accountAdmin
  val em = InboxV3ServiceFixtures.email
  val tiid_2 = InboxV3ServiceFixtures.tiid

  "Testing validateSendNewEmailManual" should (
    "return exception if toEmail is not found"
    ) in {

    val result = inboxV3Service.validateSendNewEmailManual(
      version = "v2",
      loggedInAccountId = accountAdmin,
      to_emails = send_new_manual_email.copy(to = Seq()).to,
      tiid = None,
      accountId = 17,
      teamId = 15,
      orgId = 20,
      permittedAccountIds = Seq(17),
      campaign_step_id = None
    )

    result
      .map(rs =>
        assert(rs == Left(ValidateSendNewEmailManuallyError.ToEmailEmpty(str = "Please provide valid To email address")))
      )

  }

  "Testing validateSendNewEmailManual" should (
    "fail when campaign step-id and tiid both none"
    ) in {

    (prospectServiceV2.findByEmail)
      .expects(send_new_manual_email.to.map(_.email), 15, *)
      .returning(Success(Seq(ProspectIdEmail(id = 3, email, owner_id))))


    val result = inboxV3Service.validateSendNewEmailManual(
      version = "v2",
      loggedInAccountId = accountAdmin,
      to_emails = send_new_manual_email.to,
      tiid = None,
      accountId = 17,
      teamId = 15,
      orgId = 20,
      permittedAccountIds = Seq(17),
      campaign_step_id = None
    )

    print(result)

    result
      .map(rs =>
        assert(rs == Right(SendNewEmailManualData.ExtensionFlowWithoutCampaign(send_new_manual_email.to)))
      )
      .recover(er => assert(false))

  }

  "Testing validateSendNewEmailManual" should (
    "fail when campaign step-id and tiid both found"
    ) in {

    (prospectServiceV2.findByEmail)
      .expects(send_new_manual_email.to.map(_.email), 15, *)
      .returning(Success(Seq(ProspectIdEmail(id = 3, email, owner_id))))

    val result = inboxV3Service.validateSendNewEmailManual(
      version = "v2",
      loggedInAccountId = accountAdmin,
      to_emails = send_new_manual_email.to,
      tiid = Some(tiid_2),
      accountId = 17,
      teamId = 15,
      orgId = 20,
      permittedAccountIds = Seq(17),
      campaign_step_id = Some(659L)
    )

    print(result)

    result
      .map(rs =>
        assert(rs == Left(ValidateSendNewEmailManuallyError.CampaignStepIdAndTIIDBothDefinedError))
      )
      .recover(er => assert(false))

  }

  val prospect_id_email = InboxV3ServiceFixtures.prospect_id_email

  "Testing validateSendNewEmailManual" should (
    "return when campaign details were not found for particular step_id"
    ) in {

    (prospectServiceV2.findByEmail)
      .expects(send_new_manual_email.to.map(_.email), 15, *)
      .returning(Success(Seq(ProspectIdEmail(id = 3, email, owner_id))))


    (campaignProspectDAO.findCampaignStepDataForProspect)
      .expects(CurrentStepId(step_id = 24), TeamId(id = 15))
      .returning(Success(None))

    val result = inboxV3Service.validateSendNewEmailManual(
      version = "v2",
      loggedInAccountId = accountAdmin,
      to_emails = send_new_manual_email.to,
      tiid = None,
      accountId = 17,
      teamId = 15,
      orgId = 20,
      permittedAccountIds = Seq(17),
      campaign_step_id = Some(24)
    )

    print(result)

    result
      .map(rs =>
        assert(false)
      )
      .recover(err => assert(err.getMessage.contains("Campaign details not found while sending email")))

  }

  val campaign_step_related_data_prospect = InboxV3ServiceFixtures.campaign_step_related_data

  "Testing validateSendNewEmailManual" should (
    "success when campaign details were found particular step_id"
    ) in {

    (prospectServiceV2.findByEmail)
      .expects(send_new_manual_email.to.map(_.email), 15, *)
      .returning(Success(Seq(ProspectIdEmail(id = 3, email, owner_id))))

    (campaignProspectDAO.findCampaignStepDataForProspect)
      .expects(CurrentStepId(step_id = 24), TeamId(id = 15))
      .returning(Success(
        Some(campaign_step_related_data_prospect)
      ))

    (emailScheduledDAO.getEmailScheduledIdForManualEmailSending)
      .expects(Seq(ProspectId(id = 3)), TeamId(id = 15), CampaignId(id = 891), StepId(id = 24))
      .returning(Success(Some(3)))

    val result = inboxV3Service.validateSendNewEmailManual(
      version = "v2",
      loggedInAccountId = accountAdmin,
      to_emails = send_new_manual_email.to,
      tiid = None,
      accountId = 17,
      teamId = 15,
      orgId = 20,
      permittedAccountIds = Seq(17),
      campaign_step_id = Some(24)
    )

    val expected_result = SendNewEmailManualData.ExtensionFlowData(
      campaign_step_id = CurrentStepId(24),
      campaign_step_related_data = campaign_step_related_data,
      to_emails = send_new_manual_email.to,
      email_scheduled_id = 3,
      to_prospects = Seq(ProspectId(3L))
    )

    result
      .map(rs =>
        assert(rs == Right(expected_result))
      )
      .recover(err => assert(false))

  }

  val error = new Exception("Error while getting campaign-step-related_dat")

  "Testing validateSendNewEmailManual" should (
    "should fail get campaign step related data fails"
    ) in {

    (prospectServiceV2.findByEmail)
      .expects(send_new_manual_email.to.map(_.email), 15, *)
      .returning(Success(Seq(ProspectIdEmail(id = 3, email, owner_id))))

    (campaignProspectDAO.findCampaignStepDataForProspect)
      .expects(CurrentStepId(step_id = 24), TeamId(id = 15))
      .returning(Failure(
        error
      ))

    val result = inboxV3Service.validateSendNewEmailManual(
      version = "v2",
      loggedInAccountId = accountAdmin,
      to_emails = send_new_manual_email.to,
      tiid = None,
      accountId = 17,
      teamId = 15,
      orgId = 20,
      permittedAccountIds = Seq(17),
      campaign_step_id = Some(24)
    )


    result
      .map(rs =>
        assert(rs == Left(ValidateSendNewEmailManuallyError.CampaignStepIdAndTIIDBothDefinedError))
      )
      .recover(err => assert(err.getMessage == error.getMessage))

  }

  "Testing validateSendNewEmailManual" should (
    "fail when validateInboxTypeAndGetSettingIds fails"
    ) in {

    val inbox_type_data: InboxTypeData = InboxTypeData.SINGLE_DATA(
      team_inbox_id = 145
    )

    (prospectServiceV2.findByEmail)
      .expects(send_new_manual_email.to.map(_.email), 15, *)
      .returning(Success(Seq(ProspectIdEmail(id = 3, email, owner_id))))

    (teamInboxService.validateInboxTypeAndGetSettingIds)
      .expects(
        "v2",
        accountAdmin,
        inbox_type_data,
        Seq(17L),
        15,
        Logger
      )
      .returning(Left(
        ValidateInboxTypeAndGetEsetsError.DBFailure(error)
      ))

    val result = inboxV3Service.validateSendNewEmailManual(
      version = "v2",
      loggedInAccountId = accountAdmin,
      to_emails = send_new_manual_email.to,
      tiid = Some(tiid_2),
      accountId = 17,
      teamId = 15,
      orgId = 20,
      permittedAccountIds = Seq(17),
      campaign_step_id = None
    )


    result
      .map(rs =>
        assert(rs == Left(ValidateSendNewEmailManuallyError.ValidateInboxTypeError(
          ValidateInboxTypeAndGetEsetsError.DBFailure(error)
        )))
      )
      .recover(err => assert(false))

  }


  //  "EmailThreadDAO.getQueryForSearchInboxEmailThreads" should
  //    ("generate a syntactically valid sql: when date_within and folder_type are none") in {
  //
  //    val qry = emailThreadDAO.getQueryForSearchInboxEmailThreads(
  //      teamId = teamId,
  //      searchKey = search_key,
  //      settingsIds = esets,
  //      date_within = None,
  //      folder_type = None
  //    )
  //    val qry_str = qry
  //
  //    val expected =
  //      """
  //        |select
  //        |distinct on (t.email_thread_id) t.uuid,
  //        |t.subject,
  //        |t.to_email,
  //        |t.sender_email_settings_id,
  //        |t.sent_at, t.team_inbox_id
  //        |
  //        |from (
  //        |   (select email_thread_id, et.uuid, emails_scheduled.subject, to_email, sender_email_settings_id, sent_at, team_inbox.id as team_inbox_id
  //        |   from emails_scheduled
  //        |   inner join email_threads et on et.id = emails_scheduled.email_thread_id and et.team_id = emails_scheduled.team_id
  //        |   inner join team_inbox on team_inbox.email_setting_id = emails_scheduled.sender_email_settings_id
  //        |   where emails_scheduled.team_id = ? AND sender_email_settings_id in (?, ?) AND email_thread_id is not null AND lower(to_email) like ?
  //        |   limit 5)
  //        |   union all
  //        |   (select email_thread_id, et.uuid, emails_scheduled.subject, to_email, sender_email_settings_id, sent_at, team_inbox.id as team_inbox_id
  //        |   from emails_scheduled
  //        |   inner join email_threads et on et.id = emails_scheduled.email_thread_id and et.team_id = emails_scheduled.team_id
  //        |   inner join team_inbox on team_inbox.email_setting_id = emails_scheduled.sender_email_settings_id
  //        |   where emails_scheduled.team_id = ? AND sender_email_settings_id in (?, ?) AND email_thread_id is not null AND lower(from_email) like ?
  //        |   limit 5)
  //        |   union all
  //        |   (select email_thread_id, et.uuid, emails_scheduled.subject, to_email, sender_email_settings_id, sent_at, team_inbox.id as team_inbox_id
  //        |   from emails_scheduled
  //        |   inner join email_threads et on et.id = emails_scheduled.email_thread_id and et.team_id = emails_scheduled.team_id
  //        |   inner join team_inbox on team_inbox.email_setting_id = emails_scheduled.sender_email_settings_id
  //        |   where emails_scheduled.team_id = ? AND sender_email_settings_id in (?, ?) AND email_thread_id is not null AND lower(emails_scheduled.subject) like ?
  //        |   limit 5)
  //        |   union all
  //        |   (select email_thread_id, et.uuid, emails_scheduled.subject, to_email, sender_email_settings_id, sent_at, team_inbox.id as team_inbox_id
  //        |   from emails_scheduled
  //        |   inner join email_threads et on et.id = emails_scheduled.email_thread_id and et.team_id = emails_scheduled.team_id
  //        |   inner join team_inbox on team_inbox.email_setting_id = emails_scheduled.sender_email_settings_id
  //        |   where emails_scheduled.team_id = ? AND sender_email_settings_id in (?, ?) AND email_thread_id is not null AND lower(cc_emails) like ?
  //        |   limit 5)
  //        |   ) as t order by t.email_thread_id desc;
  //        """.stripMargin
  //
  //    val split = expected.split(s"\\s+")
  //    val rhs = split.reduce((a1, a2) => {
  //      a1 + " " + a2
  //    })
  //    val lhs = qry_str.split("\\s+").reduce((s1, s2) => {
  //      s1 + " " + s2
  //    })
  //    assert(rhs == lhs)
  //  }
  //
  //  "EmailThreadDAO.getQueryForSearchInboxEmailThreads" should
  //    ("generate a syntactically valid sql: when date_within given and folder_type is none") in {
  //
  //    val qry = emailThreadDAO.getQueryForSearchInboxEmailThreads(
  //      teamId = teamId,
  //      searchKey = search_key,
  //      settingsIds = esets,
  //      date_within = Some(DateTime.now()),
  //      folder_type = None
  //    )
  //    val qry_str = qry
  //
  //    val expected =
  //      """
  //        |select
  //        |distinct on (t.email_thread_id) t.uuid,
  //        |t.subject,
  //        |t.to_email,
  //        |t.sender_email_settings_id,
  //        |t.sent_at, t.team_inbox_id
  //        |
  //        |from (
  //        |   (select email_thread_id, et.uuid, emails_scheduled.subject, to_email, sender_email_settings_id, sent_at, team_inbox.id as team_inbox_id
  //        |   from emails_scheduled
  //        |   inner join email_threads et on et.id = emails_scheduled.email_thread_id and et.team_id = emails_scheduled.team_id
  //        |   inner join team_inbox on team_inbox.email_setting_id = emails_scheduled.sender_email_settings_id
  //        |   where emails_scheduled.team_id = ? AND sender_email_settings_id in (?, ?) AND email_thread_id is not null AND lower(to_email) like ?
  //        |   AND sent_at > ?
  //        |   limit 5)
  //        |   union all
  //        |   (select email_thread_id, et.uuid, emails_scheduled.subject, to_email, sender_email_settings_id, sent_at, team_inbox.id as team_inbox_id
  //        |   from emails_scheduled
  //        |   inner join email_threads et on et.id = emails_scheduled.email_thread_id and et.team_id = emails_scheduled.team_id
  //        |   inner join team_inbox on team_inbox.email_setting_id = emails_scheduled.sender_email_settings_id
  //        |   where emails_scheduled.team_id = ? AND sender_email_settings_id in (?, ?) AND email_thread_id is not null AND lower(from_email) like ?
  //        |   AND sent_at > ?
  //        |   limit 5)
  //        |   union all
  //        |   (select email_thread_id, et.uuid, emails_scheduled.subject, to_email, sender_email_settings_id, sent_at, team_inbox.id as team_inbox_id
  //        |   from emails_scheduled
  //        |   inner join email_threads et on et.id = emails_scheduled.email_thread_id and et.team_id = emails_scheduled.team_id
  //        |   inner join team_inbox on team_inbox.email_setting_id = emails_scheduled.sender_email_settings_id
  //        |   where emails_scheduled.team_id = ? AND sender_email_settings_id in (?, ?) AND email_thread_id is not null AND lower(emails_scheduled.subject) like ?
  //        |   AND sent_at > ?
  //        |   limit 5)
  //        |   union all
  //        |   (select email_thread_id, et.uuid, emails_scheduled.subject, to_email, sender_email_settings_id, sent_at, team_inbox.id as team_inbox_id
  //        |   from emails_scheduled
  //        |   inner join email_threads et on et.id = emails_scheduled.email_thread_id and et.team_id = emails_scheduled.team_id
  //        |   inner join team_inbox on team_inbox.email_setting_id = emails_scheduled.sender_email_settings_id
  //        |   where emails_scheduled.team_id = ? AND sender_email_settings_id in (?, ?) AND email_thread_id is not null AND lower(cc_emails) like ?
  //        |   AND sent_at > ?
  //        |   limit 5)
  //        |   ) as t order by t.email_thread_id desc;
  //        """.stripMargin
  //
  //    val split = expected.split(s"\\s+")
  //    val rhs = split.reduce((a1, a2) => {
  //      a1 + " " + a2
  //    })
  //    val lhs = qry_str.split("\\s+").reduce((s1, s2) => {
  //      s1 + " " + s2
  //    })
  //    assert(rhs == lhs)
  //  }
  //
  //  "EmailThreadDAO.getQueryForSearchInboxEmailThreads" should
  //    ("generate a syntactically valid sql: when date_within none and folder_type given") in {
  //
  //    val qry = emailThreadDAO.getQueryForSearchInboxEmailThreads(
  //      teamId = teamId,
  //      searchKey = search_key,
  //      settingsIds = esets,
  //      date_within = None,
  //      folder_type = Some(FolderType.PROSPECTS)
  //    )
  //    val qry_str = qry
  //
  //    val expected =
  //      """
  //        |select
  //        |distinct on (t.email_thread_id) t.uuid,
  //        |t.subject,
  //        |t.to_email,
  //        |t.sender_email_settings_id,
  //        |t.sent_at, t.team_inbox_id
  //        |
  //        |from (
  //        |   (select email_thread_id, et.uuid, emails_scheduled.subject, to_email, sender_email_settings_id, sent_at, team_inbox.id as team_inbox_id
  //        |   from emails_scheduled
  //        |   inner join email_threads et on et.id = emails_scheduled.email_thread_id and et.team_id = emails_scheduled.team_id
  //        |   inner join team_inbox on team_inbox.email_setting_id = emails_scheduled.sender_email_settings_id
  //        |   where emails_scheduled.team_id = ? AND sender_email_settings_id in (?, ?) AND email_thread_id is not null AND et.folder_type = ? AND lower(to_email) like ?
  //        |   limit 5)
  //        |   union all
  //        |   (select email_thread_id, et.uuid, emails_scheduled.subject, to_email, sender_email_settings_id, sent_at, team_inbox.id as team_inbox_id
  //        |   from emails_scheduled
  //        |   inner join email_threads et on et.id = emails_scheduled.email_thread_id and et.team_id = emails_scheduled.team_id
  //        |   inner join team_inbox on team_inbox.email_setting_id = emails_scheduled.sender_email_settings_id
  //        |   where emails_scheduled.team_id = ? AND sender_email_settings_id in (?, ?) AND email_thread_id is not null AND et.folder_type = ? AND lower(from_email) like ?
  //        |   limit 5)
  //        |   union all
  //        |   (select email_thread_id, et.uuid, emails_scheduled.subject, to_email, sender_email_settings_id, sent_at, team_inbox.id as team_inbox_id
  //        |   from emails_scheduled
  //        |   inner join email_threads et on et.id = emails_scheduled.email_thread_id and et.team_id = emails_scheduled.team_id
  //        |   inner join team_inbox on team_inbox.email_setting_id = emails_scheduled.sender_email_settings_id
  //        |   where emails_scheduled.team_id = ? AND sender_email_settings_id in (?, ?) AND email_thread_id is not null AND et.folder_type = ? AND lower(emails_scheduled.subject) like ?
  //        |   limit 5)
  //        |   union all
  //        |   (select email_thread_id, et.uuid, emails_scheduled.subject, to_email, sender_email_settings_id, sent_at, team_inbox.id as team_inbox_id
  //        |   from emails_scheduled
  //        |   inner join email_threads et on et.id = emails_scheduled.email_thread_id and et.team_id = emails_scheduled.team_id
  //        |   inner join team_inbox on team_inbox.email_setting_id = emails_scheduled.sender_email_settings_id
  //        |   where emails_scheduled.team_id = ? AND sender_email_settings_id in (?, ?) AND email_thread_id is not null AND et.folder_type = ? AND lower(cc_emails) like ?
  //        |   limit 5)
  //        |   ) as t order by t.email_thread_id desc;
  //      """.stripMargin
  //
  //    val split = expected.split(s"\\s+")
  //    val rhs = split.reduce((a1, a2) => {
  //      a1 + " " + a2
  //    })
  //    val lhs = qry_str.split("\\s+").reduce((s1, s2) => {
  //      s1 + " " + s2
  //    })
  //    assert(rhs == lhs)
  //  }
  //
  //  "EmailThreadDAO.getQueryForSearchInboxEmailThreads" should
  //    ("generate a syntactically valid sql: when date_within and folder_type both given") in {
  //
  //    val qry = emailThreadDAO.getQueryForSearchInboxEmailThreads(
  //      teamId = teamId,
  //      searchKey = search_key,
  //      settingsIds = esets,
  //      date_within = Some(DateTime.now()),
  //      folder_type = Some(FolderType.PROSPECTS)
  //    )
  //    val qry_str = qry
  //
  //    val expected =
  //      """
  //        |select
  //        |distinct on (t.email_thread_id) t.uuid,
  //        |t.subject,
  //        |t.to_email,
  //        |t.sender_email_settings_id,
  //        |t.sent_at, t.team_inbox_id
  //        |
  //        |from (
  //        |   (select email_thread_id, et.uuid, emails_scheduled.subject, to_email, sender_email_settings_id, sent_at, team_inbox.id as team_inbox_id
  //        |   from emails_scheduled
  //        |   inner join email_threads et on et.id = emails_scheduled.email_thread_id and et.team_id = emails_scheduled.team_id
  //        |   inner join team_inbox on team_inbox.email_setting_id = emails_scheduled.sender_email_settings_id
  //        |   where emails_scheduled.team_id = ? AND sender_email_settings_id in (?, ?) AND email_thread_id is not null AND et.folder_type = ? AND lower(to_email) like ?
  //        |   AND sent_at > ?
  //        |   limit 5)
  //        |   union all
  //        |   (select email_thread_id, et.uuid, emails_scheduled.subject, to_email, sender_email_settings_id, sent_at, team_inbox.id as team_inbox_id
  //        |   from emails_scheduled
  //        |   inner join email_threads et on et.id = emails_scheduled.email_thread_id and et.team_id = emails_scheduled.team_id
  //        |   inner join team_inbox on team_inbox.email_setting_id = emails_scheduled.sender_email_settings_id
  //        |   where emails_scheduled.team_id = ? AND sender_email_settings_id in (?, ?) AND email_thread_id is not null AND et.folder_type = ? AND lower(from_email) like ?
  //        |   AND sent_at > ?
  //        |   limit 5)
  //        |   union all
  //        |   (select email_thread_id, et.uuid, emails_scheduled.subject, to_email, sender_email_settings_id, sent_at, team_inbox.id as team_inbox_id
  //        |   from emails_scheduled
  //        |   inner join email_threads et on et.id = emails_scheduled.email_thread_id and et.team_id = emails_scheduled.team_id
  //        |   inner join team_inbox on team_inbox.email_setting_id = emails_scheduled.sender_email_settings_id
  //        |   where emails_scheduled.team_id = ? AND sender_email_settings_id in (?, ?) AND email_thread_id is not null AND et.folder_type = ? AND lower(emails_scheduled.subject) like ?
  //        |   AND sent_at > ?
  //        |   limit 5)
  //        |   union all
  //        |   (select email_thread_id, et.uuid, emails_scheduled.subject, to_email, sender_email_settings_id, sent_at, team_inbox.id as team_inbox_id
  //        |   from emails_scheduled
  //        |   inner join email_threads et on et.id = emails_scheduled.email_thread_id and et.team_id = emails_scheduled.team_id
  //        |   inner join team_inbox on team_inbox.email_setting_id = emails_scheduled.sender_email_settings_id
  //        |   where emails_scheduled.team_id = ? AND sender_email_settings_id in (?, ?) AND email_thread_id is not null AND et.folder_type = ? AND lower(cc_emails) like ?
  //        |   AND sent_at > ?
  //        |   limit 5)
  //        |   ) as t order by t.email_thread_id desc;
  //      """.stripMargin
  //
  //    val split = expected.split(s"\\s+")
  //    val rhs = split.reduce((a1, a2) => {
  //      a1 + " " + a2
  //    })
  //    val lhs = qry_str.split("\\s+").reduce((s1, s2) => {
  //      s1 + " " + s2
  //    })
  //    assert(rhs == lhs)
  //  }


}
