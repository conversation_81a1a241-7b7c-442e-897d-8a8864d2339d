package app.api.prospects

import api.accounts.models.{AccountId, OrgId}
import api.accounts.{Account, TeamId}
import api.call.controller.CreateOrUpdateCallAccountData
import api.call.models.{CallFeedBackResponse, CallParticipantUuid, CallingServiceProvider, ConferenceUuid, NewSubAccountDetails, PhoneNumber, PhoneNumberUuid, PhoneSID, PhoneType, SubAccountStatus, SubAccountUuid, TwlAuthToken, TwlSubAccountSid}
import api.campaigns.{CampaignStep, CampaignStepVariant}
import api.campaigns.models.SendEmailFromCampaignDetails
import api.campaigns.services.CampaignId
import api.emails.{EmailScheduled, EmailScheduledNewAfterSaving}
import api.prospects.{CreateProspectEventDB, ProspectUuid}
import api.prospects.models.{ProspectCategory, ProspectCategoryId, ProspectCategoryUpdateFlow, ProspectId}
import api.sr_audit_logs.models.EventType
import api.tasks.models.TaskType
import api.tasks.services.TaskUuid
import api_layer_models.CurrencyType
import app.api.prospects.dao.TaskDAOTest
import db_test_spec.api.{DbTestingBeforeAllAndAfterAll, InitialData, SRSetupAndDeleteFixtures}
import db_test_spec.api.accounts.fixtures.{EmailScheduledNewFixture, NewAccountAndEmailSettingData, NewAccountAndWhatsappSettingData}
import db_test_spec.api.campaigns.dao.CampaignProspectTestDAO
import db_test_spec.api.campaigns.test_utils.{CampaignUtils, CreateAndStartCampaignData}
import db_test_spec.api.scheduler.fixtures.{DefaultParametersFixtureForInitializingDataForReScheduling, ScheduleTaskFixture}
import eventframework.ProspectObject
import io.smartreach.esp.api.emails.IEmailAddress
import org.joda.time.DateTime
import play.api.libs.json.JodaReads._
import org.scalatest.funspec.AsyncFunSpec
import play.api.libs.json.{JsValue, Json}
import sr_scheduler.models.ChannelData.WhatsAppChannelData
import sr_scheduler.models.ChannelType
import utils.SRLogger
import utils.helpers.LogHelpers
import utils.mq.channel_scheduler.channels.ScheduleTasksData

import scala.concurrent.{Await, ExecutionContext, Future}
import play.api.test.FakeRequest
import play.api.test.Helpers._

/*
  Date: 18-Apr-2024
  We are covering all the events which are being pushed to the DB
  if we add new event in webhook or DB, there should be an Integration test for that in this file.
  Events tested:
    - activity.prospect.created
    - activity.prospect.category_updated
    - activity.campaign.completed_campaign
    - activity.campaign.added_to_campaign
    - activity.campaign.removed_from_campaign
    - activity.email.sent
    - activity.email.opened
    - activity.task.created
    - activity.call.placed
 */

class ProspectEventsListingIntegrationTests extends DbTestingBeforeAllAndAfterAll {


  lazy val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

  describe("Integration tests for Prospect Events") {

    describe("Testing response for Prospect type events") {

      it("should give result in expected format for prospect_category_updated event") {

        val olderThan: Long = DateTime.now().plusDays(1).getMillis

        val apiKey = initialData.teamUserLevelKey

        val teamId: Long = initialData.head_team_id

        val prospect: ProspectObject = initialData.prospectsResult.head
        val prospect_uuid = prospect.prospect_uuid.get.uuid

        val final_result = for {

          newProspectCategoryId <- Future.fromTry {
            prospectDAOService.getProspectCategoryId(
              teamId = TeamId(teamId),
              text_id = ProspectCategory.INTERESTED,
              account = None
            )
          }

          updatedCategory <- Future.fromTry {
            prospectUpdateCategoryTemp.updateCategoryAndCreateEvent(
              prospectIds = Seq(prospect.id),
              doerAccountId = prospect.owner_id,
              teamId = teamId,
              accountName = initialData.account.first_name.get,
              prospectCategoryUpdateFlow = ProspectCategoryUpdateFlow.AdminUpdate(
                old_prospect_category_id = None,
                new_prospect_category_id = newProspectCategoryId
              ),
              account = Some(initialData.account),
              logger = Logger,
              event_created_at = DateTime.now(),
              auditRequestLogId = Some("req_log_id")
            )
          }

          sendingUrl <- Future {
            s"/api/v3/prospects/${prospect_uuid}/events?older_than=${olderThan}"
          }

          request <- Future {
            FakeRequest(play.api.test.Helpers.GET, sendingUrl)
              .withHeaders("X-API-KEY" -> apiKey,
                "Content-Type" -> "application/json")
          }

          res <- play.api.test.Helpers.route(testApi, request).get

        } yield {
          res
        }
        val status: Int = play.api.test.Helpers.status(final_result)
        val json: JsValue = play.api.test.Helpers.contentAsJson(final_result)

        final_result.map(res => {
          if (status == 200) {

            val event = (json \ "events").as[List[JsValue]].find(js => (js \ "event_type").as[String] == "activity.prospect.category_updated").get
            val objectOfData = (event \ "object").as[String]
            val created_at = (event \ "created_at").as[Long]
            val data = (event \ "data")

            val dataObject = (data \ "object").as[String]
            val id = (data \ "id").as[String]
            val email = ((data \ "emails").as[List[JsValue]].head \ "email").as[String]
            val account_id = (data \ "account_id").as[String]
            val first_name = (data \ "first_name").as[String]
            val last_name = (data \ "last_name").as[String]
            val phone_number = (data \ "phone_numbers").as[List[String]].head

            (data \ "updated_at").as[DateTime]
            (data \ "created_at").as[DateTime]

            assert(objectOfData == "event")
            assert(dataObject == "prospect")
            assert(id == prospect.prospect_uuid.get.uuid)
            assert(email == prospect.email.get)
            assert(account_id == prospect.owner_uuid.toString)
            assert(first_name == prospect.first_name.get)
            assert(last_name == prospect.last_name.get)
            assert(phone_number == prospect.phone.get)

          } else {
            println(s"res:::: ${res.body}")
            assert(false)
          }
        }).recover(err => {
          println(s"Error: ${LogHelpers.getStackTraceAsString(err)}")
          assert(false)
        })



      }

    }

    describe("Testing response for Campaign type event") {

      it("should give result in expected format for prospect_completed event") {

        val olderThan: Long = DateTime.now().plusDays(1).getMillis
        

        val apiKey = initialData.teamUserLevelKey

        val teamId: Long = initialData.head_team_id

        var prospect: Option[ProspectObject] = None
        var prospect_uuid = ""

        var campaign_uuid = ""
        var campaign_id = 0L
        var campaign_name = ""
        val final_result = for {

          createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
            initialData = initialData,
            generateProspectCountIfNoGivenProspect = 2
          )

          markProspectAsCompleted: List[Int] <- Future.fromTry {
            campaign_id = createAndStartCampaignData.campaign.id
            campaign_name = createAndStartCampaignData.campaign.name
            CampaignProspectTestDAO.markAsCompleted(
              campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
              teamId = TeamId(teamId),
              prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id))
            )
          }

          event <- Future {
            prospect = Some(createAndStartCampaignData.addProspect.head)
            prospect_uuid = prospect.get.prospect_uuid.get.uuid
            CreateProspectEventDB(

              event_type = EventType.EVENT_V3_PROSPECT_COMPLETED,
              doer_account_id = Some(initialData.account.internal_id),
              doer_account_name = Some("name"),

              assigned_to_account_id = None,
              assigned_to_account_name = None,

              old_category = None,
              new_category = None,

              prospect_id = prospect.get.id,
              email_thread_id = None,

              campaign_id = Some(createAndStartCampaignData.campaign.id),
              campaign_name = Some(createAndStartCampaignData.campaign.name),

              step_id = None,
              step_name = None,

              clicked_url = None,

              account_id = initialData.account.internal_id,
              team_id = teamId,
              email_scheduled_id = None,

              created_at = DateTime.now(),

              task_type = None,
              channel_type = None,
              task_uuid = None,
              call_conference_uuid = None,

              duplicates_merged_at = None,
              total_merged_prospects = None,
              potential_duplicate_prospect_id = None

            )
          }

          addEvent <- Future.fromTry {
            campaign_uuid = createAndStartCampaignData.campaign.uuid.get
            prospectAddEventDAO.addEvents(events = Seq(event))
          }


          sendingUrl <- Future {
            s"/api/v3/prospects/${prospect_uuid}/events?older_than=${olderThan}"
          }

          request <- Future {
            FakeRequest(play.api.test.Helpers.GET, sendingUrl)
              .withHeaders("X-API-KEY" -> apiKey,
                "Content-Type" -> "application/json")
          }

          res <- play.api.test.Helpers.route(testApi, request).get

        } yield {
          res
        }

        val status: Int = play.api.test.Helpers.status(final_result)
        val json: JsValue = play.api.test.Helpers.contentAsJson(final_result)

        final_result.map(res => {
          if (status == 200) {

              val event = (json \ "events").as[List[JsValue]].find(js => (js \ "event_type").as[String] == "activity.campaign.completed_campaign").get
              val objectOfData = (event \ "object").as[String]
              val created_at = (event \ "created_at").as[Long]
              val data = (event \ "data")

              val dataObject = (data \ "object").as[String]

              val prospectData = (data \ "prospect")

              val id = (prospectData \ "id").as[String]
              val email = ((prospectData \ "emails").as[List[JsValue]].head \ "email").as[String]
              val account_id = (prospectData \ "account_id").as[String]
              val first_name = (prospectData \ "first_name").as[String]
              val last_name = (prospectData \ "last_name").as[String]
              val phone_number = (prospectData \ "phone_numbers").as[List[String]].head


              assert(objectOfData == "event")
              assert(dataObject == "campaign_prospect")
              assert(id == prospect.get.prospect_uuid.get.uuid)
              assert(email == prospect.get.email.get)
              assert(account_id == prospect.get.owner_uuid.toString)
              assert(first_name == prospect.get.first_name.get)
              assert(last_name == prospect.get.last_name.get)
              assert(phone_number == prospect.get.phone.get)

              val prospect_status_in_campaign = (data \ "prospect_status_in_campaign")
              val total_opens = (prospect_status_in_campaign \ "total_opens_in_campaign").as[Int]
              val total_clicks_in_campaign = (prospect_status_in_campaign \ "total_clicks_in_campaign").as[Int]
              val sent = (prospect_status_in_campaign \ "sent").as[Boolean]
              val opened = (prospect_status_in_campaign \ "opened").as[Boolean]
              val replied = (prospect_status_in_campaign \ "replied").as[Boolean]
              val opted_out = (prospect_status_in_campaign \ "opted_out").as[Boolean]
              val bounced = (prospect_status_in_campaign \ "bounced").as[Boolean]
              val completed = (prospect_status_in_campaign \ "completed").as[Boolean]
              val clicked = (prospect_status_in_campaign \ "clicked").as[Boolean]
              val invalid_email = (prospect_status_in_campaign \ "invalid_email").as[Boolean]
              val auto_reply = (prospect_status_in_campaign \ "auto_reply").as[Boolean]
              val campaign_name = (prospect_status_in_campaign \ "campaign_name").as[String]
              val campaign_id = (prospect_status_in_campaign \ "campaign_id").as[String]

              assert(total_opens == 0)
              assert(total_clicks_in_campaign == 0)
              assert(!sent)
              assert(!opened)
              assert(!replied)
              assert(!opted_out)
              assert(!bounced)
              assert(completed)
              assert(!clicked)
              assert(!invalid_email)
              assert(!auto_reply)
              assert(campaign_name == campaign_name)
              assert(campaign_id == campaign_id)

          } else {
            println(s"res:::: ${res.body}")
            assert(false)
          }
        }).recover(err => {
          println(s"Error: ${LogHelpers.getStackTraceAsString(err)}")
          assert(false)
        })
      }

      it("should give result in expected format for prospect_added_to_campaign event") {

        val olderThan: Long = DateTime.now().plusDays(1).getMillis
        

        val apiKey = initialData.teamUserLevelKey

        val teamId: Long = initialData.head_team_id

        var prospect: Option[ProspectObject] = None
        var prospect_uuid = ""

        var campaign_uuid = ""
        var campaign_id: Long = 0L
        var campaign_name = ""
        val final_result = for {

          createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
            initialData = initialData,
            generateProspectCountIfNoGivenProspect = 2
          )

          markProspectAsCompleted: List[Int] <- Future.fromTry {
            campaign_id = createAndStartCampaignData.campaign.id
            campaign_name = createAndStartCampaignData.campaign.name
            CampaignProspectTestDAO.markAsCompleted(
              campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
              teamId = TeamId(teamId),
              prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id))
            )
          }

          event <- Future {
            prospect = Some(createAndStartCampaignData.addProspect.head)
            prospect_uuid = prospect.get.prospect_uuid.get.uuid
            CreateProspectEventDB(

              event_type = EventType.EVENT_V3_PROSPECT_COMPLETED,
              doer_account_id = Some(initialData.account.internal_id),
              doer_account_name = Some("name"),

              assigned_to_account_id = None,
              assigned_to_account_name = None,

              old_category = None,
              new_category = None,

              prospect_id = prospect.get.id,
              email_thread_id = None,

              campaign_id = Some(createAndStartCampaignData.campaign.id),
              campaign_name = Some(createAndStartCampaignData.campaign.name),

              step_id = None,
              step_name = None,

              clicked_url = None,

              account_id = initialData.account.internal_id,
              team_id = teamId,
              email_scheduled_id = None,

              created_at = DateTime.now(),

              task_type = None,
              channel_type = None,
              task_uuid = None,
              call_conference_uuid = None,

              duplicates_merged_at = None,
              total_merged_prospects = None,
              potential_duplicate_prospect_id = None

            )
          }

          addEvent <- Future.fromTry {
            campaign_uuid = createAndStartCampaignData.campaign.uuid.get
            prospectAddEventDAO.addEvents(events = Seq(event))
          }


          sendingUrl <- Future {
            s"/api/v3/prospects/${prospect_uuid}/events?older_than=${olderThan}"
          }

          request <- Future {
            FakeRequest(play.api.test.Helpers.GET, sendingUrl)
              .withHeaders("X-API-KEY" -> apiKey,
                "Content-Type" -> "application/json")
          }

          res <- play.api.test.Helpers.route(testApi, request).get
        } yield {
          res
        }

        val status: Int = play.api.test.Helpers.status(final_result)
        val json: JsValue = play.api.test.Helpers.contentAsJson(final_result)

        final_result.map(res => {
          if (status == 200) {

              val event = (json \ "events").as[List[JsValue]].find(js => (js \ "event_type").as[String] == "activity.campaign.added_to_campaign").get
              val objectOfData = (event \ "object").as[String]
              val created_at = (event \ "created_at").as[Long]
              val data = (event \ "data")

              val dataObject = (data \ "object").as[String]

              val prospectData = (data \ "prospect")

              val id = (prospectData \ "id").as[String]
              val email = ((prospectData \ "emails").as[List[JsValue]].head \ "email").as[String]
              val account_id = (prospectData \ "account_id").as[String]
              val first_name = (prospectData \ "first_name").as[String]
              val last_name = (prospectData \ "last_name").as[String]
              val phone_number = (prospectData \ "phone_numbers").as[List[String]].head


              assert(objectOfData == "event")
              assert(dataObject == "campaign_prospect")
              assert(id == prospect.get.prospect_uuid.get.uuid)
              assert(email == prospect.get.email.get)
              assert(account_id == prospect.get.owner_uuid.toString)
              assert(first_name == prospect.get.first_name.get)
              assert(last_name == prospect.get.last_name.get)
              assert(phone_number == prospect.get.phone.get)

              val prospect_status_in_campaign = (data \ "prospect_status_in_campaign")
              val total_opens = (prospect_status_in_campaign \ "total_opens_in_campaign").as[Int]
              val total_clicks_in_campaign = (prospect_status_in_campaign \ "total_clicks_in_campaign").as[Int]
              val sent = (prospect_status_in_campaign \ "sent").as[Boolean]
              val opened = (prospect_status_in_campaign \ "opened").as[Boolean]
              val replied = (prospect_status_in_campaign \ "replied").as[Boolean]
              val opted_out = (prospect_status_in_campaign \ "opted_out").as[Boolean]
              val bounced = (prospect_status_in_campaign \ "bounced").as[Boolean]
              val completed = (prospect_status_in_campaign \ "completed").as[Boolean]
              val clicked = (prospect_status_in_campaign \ "clicked").as[Boolean]
              val invalid_email = (prospect_status_in_campaign \ "invalid_email").as[Boolean]
              val auto_reply = (prospect_status_in_campaign \ "auto_reply").as[Boolean]
              val campaign_name = (prospect_status_in_campaign \ "campaign_name").as[String]
              val campaign_id = (prospect_status_in_campaign \ "campaign_id").as[String]

              assert(total_opens == 0)
              assert(total_clicks_in_campaign == 0)
              assert(!sent)
              assert(!opened)
              assert(!replied)
              assert(!opted_out)
              assert(!bounced)
              assert(completed)
              assert(!clicked)
              assert(!invalid_email)
              assert(!auto_reply)
              assert(campaign_name == campaign_name)
              assert(campaign_id == campaign_uuid)

          } else {
            // println(s"res:::: ${res.body}")
            assert(false)
          }
        }).recover(err => {
          println(s"Error: ${LogHelpers.getStackTraceAsString(err)}")
          assert(false)
        })

      }

      it("should give result in expected format for prospect_removed_from_campaign event") {

        val olderThan: Long = DateTime.now().plusDays(1).getMillis
        

        val apiKey = initialData.teamUserLevelKey

        val teamId: Long = initialData.head_team_id

        var prospect: Option[ProspectObject] = None
        var prospect_uuid = ""

        var campaign_uuid = ""
        var campaign_id: Long = 0L
        var campaign_name = ""
        val final_result = for {

          createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
            initialData = initialData,
            generateProspectCountIfNoGivenProspect = 2
          )

          markProspectAsCompleted: List[Int] <- Future.fromTry {
            campaign_id = createAndStartCampaignData.campaign.id
            campaign_name = createAndStartCampaignData.campaign.name
            CampaignProspectTestDAO.markAsCompleted(
              campaign_id = CampaignId(createAndStartCampaignData.createCampaign.id),
              teamId = TeamId(teamId),
              prospect_ids = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id))
            )
          }

          event <- Future {
            prospect = Some(createAndStartCampaignData.addProspect.head)
            prospect_uuid = prospect.get.prospect_uuid.get.uuid
            CreateProspectEventDB(

              event_type = EventType.EVENT_V3_PROSPECT_REMOVED,
              doer_account_id = Some(initialData.account.internal_id),
              doer_account_name = Some("name"),

              assigned_to_account_id = None,
              assigned_to_account_name = None,

              old_category = None,
              new_category = None,

              prospect_id = prospect.get.id,
              email_thread_id = None,

              campaign_id = Some(createAndStartCampaignData.campaign.id),
              campaign_name = Some(createAndStartCampaignData.campaign.name),

              step_id = None,
              step_name = None,

              clicked_url = None,

              account_id = initialData.account.internal_id,
              team_id = teamId,
              email_scheduled_id = None,

              created_at = DateTime.now(),

              task_type = None,
              channel_type = None,
              task_uuid = None,
              call_conference_uuid = None,

              duplicates_merged_at = None,
              total_merged_prospects = None,
              potential_duplicate_prospect_id = None

            )
          }

          addEvent <- Future.fromTry {
            campaign_uuid = createAndStartCampaignData.campaign.uuid.get
            prospectAddEventDAO.addEvents(events = Seq(event))
          }


          sendingUrl <- Future {
            s"/api/v3/prospects/${prospect_uuid}/events?older_than=${olderThan}"
          }


          request <- Future {
            FakeRequest(play.api.test.Helpers.GET, sendingUrl)
              .withHeaders("X-API-KEY" -> apiKey,
                "Content-Type" -> "application/json")
          }

          res <- play.api.test.Helpers.route(testApi, request).get
        } yield {
          res
        }

        val status: Int = play.api.test.Helpers.status(final_result)
        val json: JsValue = play.api.test.Helpers.contentAsJson(final_result)

        final_result.map(res => {
          if (status == 200) {

            val event = (json \ "events").as[List[JsValue]].find(js => (js \ "event_type").as[String] == "activity.campaign.removed_from_campaign").get
            val objectOfData = (event \ "object").as[String]
            val created_at = (event \ "created_at").as[Long]
            val data = (event \ "data")

            val dataObject = (data \ "object").as[String]

            val prospectData = (data \ "prospect")

            val id = (prospectData \ "id").as[String]
            val email = ((prospectData \ "emails").as[List[JsValue]].head \ "email").as[String]
            val account_id = (prospectData \ "account_id").as[String]
            val first_name = (prospectData \ "first_name").as[String]
            val last_name = (prospectData \ "last_name").as[String]
            val phone_number = (prospectData \ "phone_numbers").as[List[String]].head


            assert(objectOfData == "event")
            assert(dataObject == "campaign_prospect")
            assert(id == prospect.get.prospect_uuid.get.uuid)
            assert(email == prospect.get.email.get)
            assert(account_id == prospect.get.owner_uuid.toString)
            assert(first_name == prospect.get.first_name.get)
            assert(last_name == prospect.get.last_name.get)
            assert(phone_number == prospect.get.phone.get)

            val prospect_status_in_campaign = (data \ "prospect_status_in_campaign")
            val total_opens = (prospect_status_in_campaign \ "total_opens_in_campaign").as[Int]
            val total_clicks_in_campaign = (prospect_status_in_campaign \ "total_clicks_in_campaign").as[Int]
            val sent = (prospect_status_in_campaign \ "sent").as[Boolean]
            val opened = (prospect_status_in_campaign \ "opened").as[Boolean]
            val replied = (prospect_status_in_campaign \ "replied").as[Boolean]
            val opted_out = (prospect_status_in_campaign \ "opted_out").as[Boolean]
            val bounced = (prospect_status_in_campaign \ "bounced").as[Boolean]
            val completed = (prospect_status_in_campaign \ "completed").as[Boolean]
            val clicked = (prospect_status_in_campaign \ "clicked").as[Boolean]
            val invalid_email = (prospect_status_in_campaign \ "invalid_email").as[Boolean]
            val auto_reply = (prospect_status_in_campaign \ "auto_reply").as[Boolean]
            val campaign_name = (prospect_status_in_campaign \ "campaign_name").as[String]
            val campaign_id = (prospect_status_in_campaign \ "campaign_id").as[String]

            assert(total_opens == 0)
            assert(total_clicks_in_campaign == 0)
            assert(!sent)
            assert(!opened)
            assert(!replied)
            assert(!opted_out)
            assert(!bounced)
            assert(completed)
            assert(!clicked)
            assert(!invalid_email)
            assert(!auto_reply)
            assert(campaign_name == campaign_name)
            assert(campaign_id == campaign_uuid)

          } else {
            //println(s"res:::: ${res.body}")
            assert(false)
          }
        }).recover(err => {
          println(s"Error: ${LogHelpers.getStackTraceAsString(err)}")
          assert(false)
        })

      }

    }

    describe("Testing response for Email type event") {

      it("should give result in expected format for email_sent event") {

        val olderThan: Long = DateTime.now().plusDays(1).getMillis
        

        val apiKey = initialData.teamUserLevelKey

        val teamId: Long = initialData.head_team_id

        var prospect: Option[ProspectObject] = None
        var prospect_uuid = ""

        var campaign_uuid = ""
        var campaign_id = 0
        var campaign_name = ""
        var emailSentObject: Option[EmailScheduled] = None
        var emailsScheduled: Option[Seq[EmailScheduledNewAfterSaving]] = None
        var campaignStep: Option[CampaignStepVariant] = None
        val final_result = for {

          createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
            initialData = initialData,
            generateProspectCountIfNoGivenProspect = 2
          )

          addingEmailScheduled <- Future.fromTry {
            prospect = Some(createAndStartCampaignData.addProspect.head)
            prospect_uuid = prospect.get.prospect_uuid.get.uuid
            campaignStep = Some(createAndStartCampaignData.addStep)
            emailScheduledDAOService.saveEmailsToBeScheduledAndUpdateCampaignDataV2(
              emailsToBeScheduled = Vector(EmailScheduledNewFixture.generateEmailScheduledNew.copy(
                campaign_id = Some(createAndStartCampaignData.createCampaign.id),
                step_id = createAndStartCampaignData.createCampaign.head_step_id,
                from_email = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.sender_email,
                to_email = prospect.get.email.get,
                to_name = prospect.get.first_name,
                scheduled_from_campaign = true,
                is_opening_step = true,
                sender_email_settings_id = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.sender_email_setting_id.emailSettingId,
                team_id = createAndStartCampaignData.createCampaign.team_id,
                account_id = createAndStartCampaignData.createCampaign.owner_id,
                receiver_email_settings_id = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.receiver_email_setting_id.emailSettingId,
                campaign_email_settings_id = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.id,
                prospect_id = Some(prospect.get.id),
                base_body = Some("email content")

              )
              ),
              campaign_email_setting_id = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.id,
              emailSendingFlow = None,
              Logger = Logger
            )
          }
          emailSent <- Future {
            emailsScheduled = Some(addingEmailScheduled)
            emailSenderService.onEmailSent(
              emailSentId = addingEmailScheduled.head.email_scheduled_id,
              data = DefaultParametersFixtureForInitializingDataForReScheduling.defaultEmailToBeSent(
                emailSetting = initialData.emailSetting.get,
                campaign = createAndStartCampaignData.campaign
              ).copy(
                outlook_conversation_id = Some("outlook_conversation_id1"),
                to_emails = Seq(IEmailAddress(email = prospect.get.email.get)),
                subject = "subject-abcd",
                textBody = "email text"
              ),
              accountId = createAndStartCampaignData.createCampaign.owner_id,
              sendEmailFromCampaignDetails = Some(SendEmailFromCampaignDetails(
                campaign_id = createAndStartCampaignData.createCampaign.id,
                campaign_name = createAndStartCampaignData.createCampaign.name,
                // stepDetails can be none when email is being sent manually from Inbox.
                // Example: sendNewEmailManually in InboxV3Service
                stepDetails = None
              )),
              prospectIdInCampaign = Some(prospect.get.id),
              currentBillingCycleStartedAt = initialData.account.created_at,
              orgId = initialData.account.org.id,
              repTrackingHostId = 1,
              teamId = initialData.head_team_id
            ).get
          }

          event <- Future {
            emailSentObject = Some(emailSent)

            CreateProspectEventDB(

              event_type = EventType.EMAIL_SENT,
              doer_account_id = Some(initialData.account.internal_id),
              doer_account_name = Some("name"),

              assigned_to_account_id = None,
              assigned_to_account_name = None,

              old_category = None,
              new_category = None,

              prospect_id = prospect.get.id,
              email_thread_id = emailSent.email_thread_id,

              campaign_id = Some(createAndStartCampaignData.campaign.id),
              campaign_name = Some(createAndStartCampaignData.campaign.name),

              step_id = Some(createAndStartCampaignData.addStep.step_id),
              step_name = None,

              clicked_url = None,

              account_id = initialData.account.internal_id,
              team_id = teamId,
              email_scheduled_id = None,

              created_at = DateTime.now(),

              task_type = None,
              channel_type = None,
              task_uuid = None,
              call_conference_uuid = None,

              duplicates_merged_at = None,
              total_merged_prospects = None,
              potential_duplicate_prospect_id = None

            )
          }

          addEvent <- Future.fromTry {
            campaign_uuid = createAndStartCampaignData.campaign.uuid.get
            prospectAddEventDAO.addEvents(events = Seq(event))
          }


          sendingUrl <- Future {
            s"/api/v3/prospects/${prospect_uuid}/events?older_than=${olderThan}"
          }


          request <- Future {
            FakeRequest(play.api.test.Helpers.GET, sendingUrl)
              .withHeaders("X-API-KEY" -> apiKey,
                "Content-Type" -> "application/json")
          }

          res <- play.api.test.Helpers.route(testApi, request).get
        } yield {
          res
        }

        val status: Int = play.api.test.Helpers.status(final_result)
        val json: JsValue = play.api.test.Helpers.contentAsJson(final_result)

        final_result.map(res => {
          if (status == 200) {

            val event = (json \ "events").as[List[JsValue]].find(js => (js \ "event_type").as[String] == "activity.email.sent").get
            val objectOfData = (event \ "object").as[String]
            val created_at = (event \ "created_at").as[Long]
            val data = (event \ "data")

            val dataObject = (data \ "object").as[String]

            val prospectData = (data \ "prospect")

            val id = (prospectData \ "id").as[String]
            val email = ((prospectData \ "emails").as[List[JsValue]].head \ "email").as[String]
            val account_id = (prospectData \ "account_id").as[String]
            val first_name = (prospectData \ "first_name").as[String]
            val last_name = (prospectData \ "last_name").as[String]
            val phone_number = (prospectData \ "phone_numbers").as[List[String]].head


              assert(objectOfData == "event")
              assert(dataObject == "email")
              assert(id == prospect.get.prospect_uuid.get.uuid)
              assert(email == prospect.get.email.get)
              assert(account_id == prospect.get.owner_uuid.toString)
              assert(first_name == prospect.get.first_name.get)
              assert(last_name == prospect.get.last_name.get)
              assert(phone_number == prospect.get.phone.get)

              val email_message = (data \ "email_message").as[JsValue]
              val em_object = (email_message \ "object").as[String]
              val email_scheduled_id = (email_message \ "email_scheduled_id").as[String]
              val from_user = (email_message \ "from_user").as[Boolean]
              val from = (email_message \ "from" \ "email").as[String]
              val to = ((email_message \ "to").as[List[JsValue]].head \ "email").as[String]
              val cc_emails = (email_message \ "cc_emails").asOpt[List[String]]
              val bcc_emails = (email_message \ "bcc_emails").asOpt[List[String]]
              val subject = (email_message \ "subject").as[String]
              val body = (email_message \ "body").as[String]
              val body_preview = (email_message \ "body_preview").as[String]

              val campaign_step = (data \ "campaign_step")
              val cs_object = (campaign_step \ "object").as[String]
              val cs_label = (campaign_step \ "label").as[String]
              val cs_created_at = (campaign_step \ "created_at").as[Long]
              val step_type = (campaign_step \ "step_type").as[String]
              val campaign_id = (campaign_step \ "campaign_id").as[String]

              assert(em_object == "email_message")
              assert(from_user)
              assert(from.toLowerCase == emailsScheduled.get.head.from_email.toLowerCase)
              assert(to.toLowerCase == emailsScheduled.get.head.to_email.toLowerCase)
              assert(cc_emails.isEmpty)
              assert(bcc_emails.isEmpty)
              assert(subject == emailSentObject.get.subject.get)

              assert(step_type == "send_email")
              assert(campaign_id == campaign_uuid)

          } else {
            println(s"res:::: ${res.body}")
            assert(false)
          }
        }).recover(err => {
          println(s"Error: ${LogHelpers.getStackTraceAsString(err)}")
          assert(false)
        })


      }

      it("should give result in expected format for email_opened event") {

        val olderThan: Long = DateTime.now().plusDays(1).getMillis
        

        val apiKey = initialData.teamUserLevelKey

        val teamId: Long = initialData.head_team_id

        var prospect: Option[ProspectObject] = None
        var prospect_uuid = ""

        var campaign_uuid = ""
        var campaign_id = 0
        var campaign_name = ""
        var emailSentObject: Option[EmailScheduled] = None
        var emailsScheduled: Option[Seq[EmailScheduledNewAfterSaving]] = None
        var campaignStep: Option[CampaignStepVariant] = None
        val final_result = for {

          createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
            initialData = initialData,
            generateProspectCountIfNoGivenProspect = 2
          )

          addingEmailScheduled <- Future.fromTry {
            prospect = Some(createAndStartCampaignData.addProspect.head)
            prospect_uuid = prospect.get.prospect_uuid.get.uuid
            campaignStep = Some(createAndStartCampaignData.addStep)
            emailScheduledDAOService.saveEmailsToBeScheduledAndUpdateCampaignDataV2(
              emailsToBeScheduled = Vector(EmailScheduledNewFixture.generateEmailScheduledNew.copy(
                campaign_id = Some(createAndStartCampaignData.createCampaign.id),
                step_id = createAndStartCampaignData.createCampaign.head_step_id,
                from_email = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.sender_email,
                to_email = prospect.get.email.get,
                to_name = prospect.get.first_name,
                scheduled_from_campaign = true,
                is_opening_step = true,
                sender_email_settings_id = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.sender_email_setting_id.emailSettingId,
                team_id = createAndStartCampaignData.createCampaign.team_id,
                account_id = createAndStartCampaignData.createCampaign.owner_id,
                receiver_email_settings_id = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.receiver_email_setting_id.emailSettingId,
                campaign_email_settings_id = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.id,
                prospect_id = Some(prospect.get.id),
                base_body = Some("email content")

              )
              ),
              campaign_email_setting_id = createAndStartCampaignData.createCampaign.settings.campaign_email_settings.head.id,
              emailSendingFlow = None,
              Logger = Logger
            )
          }
          emailSent <- Future {
            emailsScheduled = Some(addingEmailScheduled)
            emailSenderService.onEmailSent(
              emailSentId = addingEmailScheduled.head.email_scheduled_id,
              data = DefaultParametersFixtureForInitializingDataForReScheduling.defaultEmailToBeSent(
                emailSetting = initialData.emailSetting.get,
                campaign = createAndStartCampaignData.campaign
              ).copy(
                outlook_conversation_id = Some("outlook_conversation_id1"),
                to_emails = Seq(IEmailAddress(email = prospect.get.email.get)),
                subject = "subject-abcd",
                textBody = "email text"
              ),
              accountId = createAndStartCampaignData.createCampaign.owner_id,
              sendEmailFromCampaignDetails = Some(SendEmailFromCampaignDetails(
                campaign_id = createAndStartCampaignData.createCampaign.id,
                campaign_name = createAndStartCampaignData.createCampaign.name,
                // stepDetails can be none when email is being sent manually from Inbox.
                // Example: sendNewEmailManually in InboxV3Service
                stepDetails = None
              )),
              prospectIdInCampaign = Some(prospect.get.id),
              currentBillingCycleStartedAt = initialData.account.created_at,
              orgId = initialData.account.org.id,
              repTrackingHostId = 1,
              teamId = initialData.head_team_id
            ).get
          }

          event <- Future {
            emailSentObject = Some(emailSent)

            CreateProspectEventDB(

              event_type = EventType.EMAIL_OPENED,
              doer_account_id = Some(initialData.account.internal_id),
              doer_account_name = Some("name"),

              assigned_to_account_id = None,
              assigned_to_account_name = None,

              old_category = None,
              new_category = None,

              prospect_id = prospect.get.id,
              email_thread_id = emailSent.email_thread_id,

              campaign_id = Some(createAndStartCampaignData.campaign.id),
              campaign_name = Some(createAndStartCampaignData.campaign.name),

              step_id = Some(createAndStartCampaignData.addStep.step_id),
              step_name = None,

              clicked_url = None,

              account_id = initialData.account.internal_id,
              team_id = teamId,
              email_scheduled_id = None,

              created_at = DateTime.now(),

              task_type = None,
              channel_type = None,
              task_uuid = None,
              call_conference_uuid = None,

              duplicates_merged_at = None,
              total_merged_prospects = None,
              potential_duplicate_prospect_id = None

            )
          }

          addEvent <- Future.fromTry {
            campaign_uuid = createAndStartCampaignData.campaign.uuid.get
            prospectAddEventDAO.addEvents(events = Seq(event))
          }


          sendingUrl <- Future {
            s"/api/v3/prospects/${prospect_uuid}/events?older_than=${olderThan}"
          }

          request <- Future {
            FakeRequest(play.api.test.Helpers.GET, sendingUrl)
              .withHeaders("X-API-KEY" -> apiKey,
                "Content-Type" -> "application/json")
          }

          res <- play.api.test.Helpers.route(testApi, request).get
        } yield {
          res
        }

        val status: Int = play.api.test.Helpers.status(final_result)
        val json: JsValue = play.api.test.Helpers.contentAsJson(final_result)

        final_result.map(res => {
          if (status == 200) {

            val event = (json \ "events").as[List[JsValue]].find(js => (js \ "event_type").as[String] == "activity.email.opened").get
            val objectOfData = (event \ "object").as[String]
            val created_at = (event \ "created_at").as[Long]
            val data = (event \ "data")

            val dataObject = (data \ "object").as[String]

            val prospectData = (data \ "prospect")

            val id = (prospectData \ "id").as[String]
            val email = ((prospectData \ "emails").as[List[JsValue]].head \ "email").as[String]
            val account_id = (prospectData \ "account_id").as[String]
            val first_name = (prospectData \ "first_name").as[String]
            val last_name = (prospectData \ "last_name").as[String]
            val phone_number = (prospectData \ "phone_numbers").as[List[String]].head


            assert(objectOfData == "event")
            assert(dataObject == "email")
            assert(id == prospect.get.prospect_uuid.get.uuid)
            assert(email == prospect.get.email.get)
            assert(account_id == prospect.get.owner_uuid.toString)
            assert(first_name == prospect.get.first_name.get)
            assert(last_name == prospect.get.last_name.get)
            assert(phone_number == prospect.get.phone.get)

            val email_message = (data \ "email_message").as[JsValue]
            val em_object = (email_message \ "object").as[String]
            val email_scheduled_id = (email_message \ "email_scheduled_id").as[String]
            val from_user = (email_message \ "from_user").as[Boolean]
            val from = (email_message \ "from" \ "email").as[String]
            val to = ((email_message \ "to").as[List[JsValue]].head \ "email").as[String]
            val cc_emails = (email_message \ "cc_emails").asOpt[List[String]]
            val bcc_emails = (email_message \ "bcc_emails").asOpt[List[String]]
            val subject = (email_message \ "subject").as[String]
            val body = (email_message \ "body").as[String]
            val body_preview = (email_message \ "body_preview").as[String]

            val campaign_step = (data \ "campaign_step")
            val cs_object = (campaign_step \ "object").as[String]
            val cs_label = (campaign_step \ "label").as[String]
            val cs_created_at = (campaign_step \ "created_at").as[Long]
            val step_type = (campaign_step \ "step_type").as[String]
            val campaign_id = (campaign_step \ "campaign_id").as[String]

            assert(em_object == "email_message")
            assert(from_user)
            assert(from.toLowerCase == emailsScheduled.get.head.from_email.toLowerCase)
            assert(to.toLowerCase == emailsScheduled.get.head.to_email.toLowerCase)
            assert(cc_emails.isEmpty)
            assert(bcc_emails.isEmpty)
            assert(subject == emailSentObject.get.subject.get)

            assert(step_type == "send_email")
            assert(campaign_id == campaign_uuid)

          } else {
            println(s"res:::: ${res.body}")
            assert(false)
          }
        }).recover(err => {
          println(s"Error: ${LogHelpers.getStackTraceAsString(err)}")
          assert(false)
        })

      }

    }

    describe("Testing response for Task type event") {

      it("should give result in expected format for task_created event") {

        val olderThan: Long = DateTime.now().plusDays(1).getMillis
        val initialData: InitialData = NewAccountAndWhatsappSettingData.createNewAccountAndWhatsappSettingData(emailNotCompulsoryOrgFlag = Some(false)).get
        val whatsappSettingData = initialData.whatsappAccount.get

        val apiKey = initialData.teamUserLevelKey

        val teamId: Long = whatsappSettingData.settings.team_id

        var prospect: Option[ProspectObject] = None
        var prospect_uuid = ""

        var campaign_uuid = ""
        var campaign_id = 0
        var campaign_name = ""
        var task_uuid = ""

        val final_result = for {

          createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartWhatsappCampaign(
            initialData = initialData,
            whatsAppSetting =  whatsappSettingData.settings
          )

          //Test result after scheduling
          taskData: ScheduleTasksData <- {
            prospect = Some(createAndStartCampaignData.addProspect.head)
            prospect_uuid = prospect.get.prospect_uuid.get.uuid
            ScheduleTaskFixture.scheduleTaskForWhatsappChannel(
              whatsAppChannelData = WhatsAppChannelData(
                whatsAppSettingUuid = whatsappSettingData.id.uuid
              ),
              teamId = TeamId(teamId)
            )
          }
          taskUuid <- Future {
            TaskDAOTest.getTaskUuidForProspect(
              teamId = TeamId(teamId),
              prospectId = ProspectId(prospect.get.id)
            ).get
          }
          event <- Future {
            task_uuid = taskUuid
            CreateProspectEventDB(

              event_type = EventType.TASK_CREATED,
              doer_account_id = Some(initialData.account.internal_id),
              doer_account_name = Some("name"),

              assigned_to_account_id = None,
              assigned_to_account_name = None,

              old_category = None,
              new_category = None,

              prospect_id = prospect.get.id,
              email_thread_id = None,

              campaign_id = Some(createAndStartCampaignData.campaign.id),
              campaign_name = Some(createAndStartCampaignData.campaign.name),

              step_id = Some(createAndStartCampaignData.addStep.step_id),
              step_name = None,

              clicked_url = None,

              account_id = initialData.account.internal_id,
              team_id = teamId,
              email_scheduled_id = None,

              created_at = DateTime.now(),

              task_type = Some(TaskType.SendWhatsAppMessage),
              channel_type = Some(ChannelType.WhatsappChannel),
              task_uuid = Some(taskUuid),
              call_conference_uuid = None,

              duplicates_merged_at = None,
              total_merged_prospects = None,
              potential_duplicate_prospect_id = None

            )
          }

          addEvent <- Future.fromTry {
            campaign_uuid = createAndStartCampaignData.campaign.uuid.get
            prospectAddEventDAO.addEvents(events = Seq(event))
          }


          sendingUrl <- Future {
            s"/api/v3/prospects/${prospect_uuid}/events?older_than=${olderThan}"
          }


          request <- Future {
            FakeRequest(play.api.test.Helpers.GET, sendingUrl)
              .withHeaders("X-API-KEY" -> apiKey,
                "Content-Type" -> "application/json")
          }

          res <- play.api.test.Helpers.route(testApi, request).get
        } yield {
          res
        }

        val status: Int = play.api.test.Helpers.status(final_result)
        val json: JsValue = play.api.test.Helpers.contentAsJson(final_result)

        final_result.map(res => {
          if (status == 200) {

              val event = (json \ "events").as[List[JsValue]].find(js => (js \ "event_type").as[String] == "activity.task.created").get
              val objectOfData = (event \ "object").as[String]
              val created_at = (event \ "created_at").as[Long]
              val data = (event \ "data")

              val dataObject = (data \ "object").as[String]
              val task_id = (data \ "task_id").as[String]
              val task_data = (data \ "task_data")
              val task_body = (task_data \ "body").as[String]
              val task_type = (task_data \ "task_type").as[String]
              val status = (data \ "status").as[String]
              val team_id: String = (data \ "team_id").as[String]
              val priority = (data \ "priority").as[String]
              val task_created_at = (data \ "created_at").as[Long]
              val task_due_at = (data \ "due_at").as[Long]

              assert(objectOfData == "event")
              assert(dataObject == "task")
              assert(task_id == task_uuid)
              assert(task_type == "send_whatsapp_message")
              assert(priority == "high")
          } else {
            println(s"res:::: ${res.body}")
            assert(false)
          }
        }).recover(err => {
          println(s"Error: ${LogHelpers.getStackTraceAsString(err)}")
          assert(false)
        })

      }

    }

    describe("Testing response for Call type event") {

      it("should give result in expected format for call_placed event") {

        val olderThan: Long = DateTime.now().plusDays(1).getMillis

        val initialData = SRSetupAndDeleteFixtures.createInitialData().get
        val org_id = OrgId(id = initialData.account.org.id)
        val team_id = TeamId(id = initialData.head_team_id)
        val account_id = AccountId(id = initialData.account.internal_id)
        val sub_acount_uuid = SubAccountUuid(uuid = srUuidUtils.generateSubAccountUuid())
        val conferenceUuid = ConferenceUuid("conference_uuid_dec")
        val participantUuid = CallParticipantUuid(participant_uuid = "participant_uuid_dec")
        val addedBy = AccountId(initialData.account.internal_id)
        val taskUuid = TaskUuid("task_uuid_abcd_dec")

        var prospect: Option[ProspectObject] = None
        var prospectId: Long = 0
        var prospect_uuid = ""

        val sub_account_detail = NewSubAccountDetails(
          uuid = sub_acount_uuid,
          sub_account_sid = TwlSubAccountSid("twl_sub_accountsid_dec"),
          auth_token = TwlAuthToken("twl_auth_token"),
          sub_account_name = "Prachi",
          status = SubAccountStatus.Active.toString,
          owner_account_sid = account_id.id.toString,
          call_remaining_credit = 10000,
          call_credit_updated_at = DateTime.now(),
          credit_unit = CurrencyType.USD,
          org_id = org_id ,
          call_credit = 10000,
          previous_usage_deducted = None,
          check_usage_from = DateTime.now()
        )

        val final_result = for {

          createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
            initialData = initialData,
            generateProspectCountIfNoGivenProspect = 2
          )

          create_sub_account <- Future {
            prospect = Some(createAndStartCampaignData.addProspect.head)
            prospectId = prospect.get.id
            prospect_uuid = prospect.get.prospect_uuid.get.uuid
            callDAO.saveSubAccountDetails(
              subAccountDetails = sub_account_detail
            ).get
          }

          insert_call_settings <- Future {
            callDAO.addNumberToDB(
              teamId = team_id,
              accountId = account_id,
              data = CreateOrUpdateCallAccountData(
                first_name = "Prachi",
                last_name = "Mane",
                country_code = "AU",
                phone_type = PhoneType.Local,
                enable_forward = false,
                forward_number = None,
                call_limit = 50,
                phone_uuid = None,
                caller_id = None
              ),
              phone_uuid = PhoneNumberUuid(phone_number_uuid = srUuidUtils.generatePhoneNumberUuid),
              phone_number = PhoneNumber(phone_number = "+************"),
              phone_number_sid = PhoneSID(phone_sid = "phone_sid_dec"),
              callingServiceProvider = CallingServiceProvider.TWILIO,
              org_sub_account_uuid = sub_acount_uuid,
              price_unit = "USD",
              phone_number_cost_cents = 115
            ).get
          }

          event <- Future {
            CreateProspectEventDB(

              event_type = EventType.CALL_PLACED,
              doer_account_id = Some(initialData.account.internal_id),
              doer_account_name = Some("name"),

              assigned_to_account_id = None,
              assigned_to_account_name = None,

              old_category = None,
              new_category = None,

              prospect_id = prospect.get.id,
              email_thread_id = None,

              campaign_id = Some(createAndStartCampaignData.campaign.id),
              campaign_name = Some(createAndStartCampaignData.campaign.name),

              step_id = Some(createAndStartCampaignData.addStep.step_id),
              step_name = None,

              clicked_url = None,

              account_id = initialData.account.internal_id,
              team_id = team_id.id,
              email_scheduled_id = None,

              created_at = DateTime.now(),

              task_type = None,
              channel_type = None,
              task_uuid = None,
              call_conference_uuid = None,

              duplicates_merged_at = None,
              total_merged_prospects = None,
              potential_duplicate_prospect_id = None

            )
          }

          addEvent <- Future.fromTry {
            prospectAddEventDAO.addEvents(events = Seq(event))
          }

          sendingUrl <- Future {
            s"/api/v3/prospects/${prospect_uuid}/events?older_than=${olderThan}"
          }


          request <- Future {
            FakeRequest(play.api.test.Helpers.GET, sendingUrl)
              .withHeaders("X-API-KEY" -> initialData.teamUserLevelKey,
                "Content-Type" -> "application/json")
          }

          res <- play.api.test.Helpers.route(testApi, request).get
        } yield {
          res
        }

        val status: Int = play.api.test.Helpers.status(final_result)
        val json: JsValue = play.api.test.Helpers.contentAsJson(final_result)

        final_result.map(res => {
          if (status == 200) {

              val event = (json \ "events").as[List[JsValue]].find(js => (js \ "event_type").as[String] == "activity.call.placed").get
              val objectOfData = (event \ "object").as[String]
              val created_at = (event \ "created_at").as[Long]
              val data = (event \ "data")

              val dataObject = (data \ "object").as[String]
              val call_setting_id = (data \ "call_setting_id").as[String]
              val team_id_res = (data \ "team_id").as[String]
              val owner_account_id = (data \ "owner_account_id").as[String]
              val from_number = (data \ "from_number").as[String]
              val to_number = (data \ "to_number").as[String]

              assert(objectOfData == "event")
              assert(dataObject == "call")
              assert(team_id_res == initialData.account.teams.filter(_.team_id == team_id.id).head.team_uuid.uuid)
              assert(from_number == "+************")
              assert(to_number == "+************")

          } else {
            println(s"res:::: ${res.body}")
            assert(false)
          }
        }).recover(err => {
          println(s"Error: ${LogHelpers.getStackTraceAsString(err)}")
          assert(false)
        })

      }

    }

  }

}


