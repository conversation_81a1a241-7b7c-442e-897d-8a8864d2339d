package app.api.prospects

import api.accounts.dao_service.OrganizationDAOService
import api.accounts.models.{AccountId, OrgId}
import api.accounts.{Account, ProspectCategoriesInDB, TeamId}
import api.accounts.service.OrganizationService
import api.blacklist.models.{BlacklistEmailAndUuid, BlacklistTypeData, BlacklistUuid, DoNotContactType}
import api.blacklist.{BlacklistCreateUpdateFormWithUuid, BlacklistDAO, BlacklistPhonesWithUuid, BlacklistTeamLevel, DomainsWithExcludedEmailsWithUuid}
import api.campaigns.CampaignProspectDAO
import api.prospects.dao.ProspectAddEventDAO
import api.prospects.{ProspectBasicForBatchUpdate, ProspectUpdateCategoryTemp, ProspectUpdateCategoryTemp2}
import api.prospects.dao_service.ProspectDAOService
import api.prospects.models.{ProspectCategory, ProspectCategoryId, ProspectCategoryRank, ProspectCategoryUpdateFlow}
import api.sr_audit_logs.models.EventDataType
import api.sr_audit_logs.services.EventLogService
import api.team_inbox.dao.ReplySentimentDAO
import app.test_fixtures.accounts.AccountAdminFixture
import org.joda.time.DateTime
import org.scalamock.scalatest.AsyncMockFactory
import org.scalatest.funspec.AsyncFunSpec
import scalikejdbc.DBSession
import utils.SRLogger
import utils.dbutils.{DBUtils, DbAndSession}
import utils.helpers.LogHelpers
import utils.testapp.Test_TaskPgDAO.dbUtils
import utils.uuid.SrUuidUtils
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService

import scala.util.{Failure, Success, Try}

class ProspectsUpdateCategoryTempSpec extends AsyncFunSpec with AsyncMockFactory {

  val prospectDAOService: ProspectDAOService = mock[ProspectDAOService]
  val prospectUpdateCategoryTemp2: ProspectUpdateCategoryTemp2 = mock[ProspectUpdateCategoryTemp2]
  val prospectAddEventDAO: ProspectAddEventDAO = mock[ProspectAddEventDAO]
  val eventLogService: EventLogService = mock[EventLogService]
  val campaignProspectDAO: CampaignProspectDAO = mock[CampaignProspectDAO]
  val organizationDAOService: OrganizationDAOService = mock[OrganizationDAOService]
  val dbUtils: DBUtils = mock[DBUtils]
  val srRollingUpdateCoreService: SrRollingUpdateCoreService = mock[SrRollingUpdateCoreService]
  val replySentimentDAO: ReplySentimentDAO = mock[ReplySentimentDAO]

  val prospectsUpdateCategoryTemp: ProspectUpdateCategoryTemp = new ProspectUpdateCategoryTemp(
    prospectDAOService = prospectDAOService,
    prospectUpdateCategoryTemp2 = prospectUpdateCategoryTemp2,
    prospectAddEventDAO = prospectAddEventDAO,
    eventLogService = eventLogService,
    campaignProspectDAO = campaignProspectDAO,
    organizationDAOService = organizationDAOService,
    srRollingUpdateCoreService = srRollingUpdateCoreService,
    replySentimentDAO = replySentimentDAO,
    dbUtils = dbUtils
  )

  val logger: SRLogger = new SRLogger("ProspectsUpdateCategoryTempSpec")

  val prospectId = 22L
  val prospectEmail = "<EMAIL>"
  val prospectEmailDomain = "sales.in"
  val doerAccountId: Long = AccountAdminFixture.accountAdmin.internal_id

  describe("updateCategory") {

    it("should return Success(0) if both old and new categories are same") {
      
      val res: Try[Int] = prospectsUpdateCategoryTemp.updateCategory(
        prospect = ProspectBasicForBatchUpdate(
          id = prospectId,
          prospect_category_id_custom = 1,
          prospect_account_id = None
        ),
        doerAccountId = doerAccountId,
        accountName = AccountAdminFixture.accountAdmin.first_name.get,
        teamId = AccountAdminFixture.teamId,
        prospectCategoryUpdateFlow = ProspectCategoryUpdateFlow.AdminUpdate(
          old_prospect_category_id = Some(ProspectCategoryId(1)),
          new_prospect_category_id = ProspectCategoryId(1)
        ),
        account = AccountAdminFixture.accountAdmin,
        logger = logger,
         //emailNotCompulsoryEnabled = false,
        auditRequestLogId = Some("ProspectsUpdateCategoryTempSpec")
      )

      assert(res.isSuccess && res.get == 0)
    }

    it("should return Success if category updated") {

      (() => dbUtils.startLocalTx())
        .expects()
        .returning(DbAndSession(null, null))

      (prospectUpdateCategoryTemp2._updateCategoryDB(_: Seq[Long], _: Long, _: Boolean, _: SRLogger, _: ProspectCategoryId)(_: DBSession))
        .expects(
          Seq(22L),
          AccountAdminFixture.teamId,
          true,
          *,
          ProspectCategoryId(2L),
          *
        )
        .returning(List(22L))

      (campaignProspectDAO._updateProspectCategory(_: Seq[Long], _: ProspectCategoryId, _: TeamId)(_: DBSession))
        .expects(
          Seq(22L),
          ProspectCategoryId(2L),
          TeamId(AccountAdminFixture.teamId),
          *
        )
        .returning(1)

      (dbUtils.commitAndCloseSession)
        .expects(*)
        .returning(())

      (prospectDAOService.getProspectCategoryById)
        .expects(AccountAdminFixture.teamId, 2L, Some(AccountAdminFixture.accountAdmin))
        .returning(Some(ProspectCategoriesInDB(
          id = 22L,
          name = "Completed",
          text_id = "Done",
          label_color = "Blue",
          is_custom = true,
          team_id = AccountAdminFixture.teamId,
          rank = ProspectCategoryRank(rank = 2000)
        )))

      (prospectDAOService.getProspectCategoryById)
        .expects(AccountAdminFixture.teamId, 1L, Some(AccountAdminFixture.accountAdmin))
        .returning(Some(ProspectCategoriesInDB(
          id = 23L,
          name = "Interested",
          text_id = "Done",
          label_color = "Blue",
          is_custom = true,
          team_id = AccountAdminFixture.teamId,
          rank = ProspectCategoryRank(rank = 2000)
        )))

      (prospectAddEventDAO.addEvents)
        .expects(*)
        .returning(Success(List(1L)))

      (eventLogService.createEventLogBatch(_: Seq[EventDataType], _: Long, _: Long, _: DateTime)(using _: SRLogger))
        .expects(*, AccountAdminFixture.teamId, AccountAdminFixture.accountAdmin.internal_id, *, *)
        .returning(Right(List("1L"))).twice()

      val res: Try[Int] = prospectsUpdateCategoryTemp.updateCategory(
        prospect = ProspectBasicForBatchUpdate(
          id = prospectId,
          prospect_category_id_custom = 1,
          prospect_account_id = None
        ),
        doerAccountId = doerAccountId,
        accountName = AccountAdminFixture.accountAdmin.first_name.get,
        teamId = AccountAdminFixture.teamId,
        prospectCategoryUpdateFlow = ProspectCategoryUpdateFlow.AdminUpdate(
          old_prospect_category_id = Some(ProspectCategoryId(1)),
          new_prospect_category_id = ProspectCategoryId(2)
        ),
        account = AccountAdminFixture.accountAdmin,
        logger = logger,
        auditRequestLogId = Some("ProspectsUpdateCategoryTempSpec")
      )

      res match {
        case Failure(exception) =>
          println(LogHelpers.getStackTraceAsString(exception))
          assert(false)
        case Success(_) => assert(res.get == 1)
      }


    }

  }
}
