package app.api.prospects

import api.accounts.{Account, AccountAccess, AccountMetadata, AccountType, OrgCountData, OrgMetadata, OrgPlan, OrgSettings, OrganizationRole, OrganizationWithCurrentData, PermType, PermissionLevelForValidation, PermissionOwnershipV2, ProspectCategoriesInDB, ReplyHandling, RolePermV2, RolePermissionDataDAOV2, RolePermissionDataV2, RolePermissionsInDBV2, RolePermissionsV2, TeamAccount, TeamAccountRole, TeamId, TeamMember, TeamMemberBasic, TeamMemberLite}
import api.accounts.{Account, AccountAccess, AccountMetadata, AccountType, AccountUuid, OrgCountData, OrgMetadata, OrgPlan, OrgSettings, OrganizationRole, OrganizationWithCurrentData, PermType, PermissionLevelForValidation, PermissionOwnershipV2, ProspectCategoriesInDB, ReplyHandling, RolePermV2, RolePermissionDataDAOV2, RolePermissionDataV2, RolePermissionsInDBV2, RolePermissionsV2, TeamAccount, TeamAccountRole, TeamMember, TeamMemberBasic, TeamMemberLite}
import api.accounts.models.{AccountProfileInfo, OrgId}
import api.calendar_app.models.CalendarAccountData
import api.campaigns.models.{CallSettingSenderDetails, CampaignEmailSettingsId, CampaignType, IgnoreProspectsInOtherCampaigns, LinkedinSettingSenderDetails, SmsSettingSenderDetails, WhatsappSettingSenderDetails}
import api.campaigns.services.{CampaignId, CampaignProspectService, CampaignService}
import api.campaigns.{CPAssignResult, CampaignBasicDetails, CampaignDAO, CampaignEmailSettings, CampaignEmailSettingsUuid, CampaignProspectDAO, CampaignSettings, CampaignWithStatsAndEmail, ChannelSettingUuid}
import api.columns.{ColumnDef, FieldTypeEnum, ProspectColumnDef}
import api.prospects.dao.{DuplicateProspectResult, NewlyCreatedProspect}
import api.prospects.models.{ProspectCategoryRank, ProspectId, SrProspectColumns, UpdateProspectType}
import api.prospects.service.ProspectServiceV2
import api.prospects.{CreateOrUpdateProspectsResult, InsertOrUpdateProspectResult, ListOfProspectsWithDataFromDifferentRowsInDb, ProspectCreateFormData, ProspectService, ProspectUploadService, ProspectUuid, ProspectsKeyMatchingInDB, UploadCSVResult, UpsertSQLProspectData, UpsertSQLResult, ValidInvalidProspectsFromCSV}
import api.reports.{AllCampaignStats, ReplySentimentStats}
import api.search.{SearchQueryColumnOperator, SearchQueryOperators}
import api.tags.models.{CampaignTag, CampaignTagUuid}
import api.team.TeamUuid
import app.test_fixtures.accounts.OrgCountDataFixture
import app.test_fixtures.campaign_settings.{CallSettingSenderDetailsFixtures, LinkedinSettingSenderDetailsFixtures, SmsSettingSenderDetailsFixtures, WhatsappSettingSenderDetailsFixtures}
import app.test_fixtures.organizationa.{OrgMetadataFixture, OrgPlanFixture}
import app.test_fixtures.prospect.ProspectFixtures
import eventframework.{ProspectObject, ProspectObjectInternal, SrResourceTypes}
import io.smartreach.esp.api.emails.EmailSettingId
import org.joda.time.DateTime
import org.scalamock.scalatest.AsyncMockFactory
import org.scalatest.funspec.AsyncFunSpec
import play.api.libs.json.{JsError, JsSuccess, Json}
import sr_scheduler.CampaignStatus
import sr_scheduler.models.{CampaignEmailPriority, ChannelType}
import utils.SRLogger
import utils.emailvalidation.EmailValidationService
import utils.emailvalidation.models.{EmailValidationInitiator, EmailValidationPriority, IdsOrEmailsForValidation}
import utils.helpers.LogHelpers
import utils.mq.webhook.model.TriggerSource
import utils_deploy.rolling_updates.models.SrRollingUpdateFeature
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService

import scala.util.{Failure, Success, Try}

class ProspectUploadServiceSpec extends AsyncFunSpec with AsyncMockFactory {

  val campaignService = mock[CampaignService]
  val prospectService = mock[ProspectService]
  val emailValidationService = mock[EmailValidationService]
  val prospectColumnDef = mock[ProspectColumnDef]
  val prospectServiceV2 = mock[ProspectServiceV2]
  val campaignProspectService = mock[CampaignProspectService]
  val rolePermissionDataDAOV2 = mock[RolePermissionDataDAOV2]
  val srRollingUpdateCoreService = mock[SrRollingUpdateCoreService]

  val prospectUploadService = new ProspectUploadService(
    campaignService: CampaignService,
    prospectService: ProspectService,
    emailValidationService: EmailValidationService,
    prospectColumnDef = prospectColumnDef,
    prospectServiceV2 = prospectServiceV2,
    campaignProspectService: CampaignProspectService,
    srRollingUpdateCoreService = srRollingUpdateCoreService

  )

  given Logger: SRLogger = new SRLogger("tests")

  val Error = new Throwable("Error")
  val campaign_id: Long = 121L
  val campaign_name = "CampaignName"
  val permittedAccountIds = Seq(2L)
  val teamId: Long = 37L
  val ownerId: Long = 2L

  val first_name = "Adminfirst"
  val last_name = "Adminlast"
  val company = "CompanyName"
  val email = "<EMAIL>"

  val aDate = DateTime.parse("2022-3-27")

  val list_name: Option[String] = Some("List Name")


  val profile = AccountProfileInfo(
    first_name = first_name,
    last_name = last_name,
    company = Some(company),
    timezone = None,
    country_code = None,
    mobile_country_code = None,
    mobile_number = None,
    onboarding_phone_number = None,
    twofa_enabled = false,
    has_gauthenticator = false,
    weekly_report_emails = None,
    scheduled_for_deletion_at = None
  )

  val accountMetadata = AccountMetadata(
    // account_ui_version = None,
    is_profile_onboarding_done = None
  )

  val orgMetadata = OrgMetadataFixture.orgMetadataFixture2

  val orgCountData: OrgCountData = OrgCountDataFixture.orgCountData_default

  val orgSettings = OrgSettings(
    enable_ab_testing = false,
    disable_force_send = false,
    bulk_sender = false,
    allow_2fa = false,
    show_2fa_setting = false,
    enforce_2fa = false,
    allow_native_crm_integration = false,
    agency_option_allow_changing = false,
    agency_option_show = false
  )

  val orgPlan = OrgPlanFixture.orgPlanFixture


  val org = OrganizationWithCurrentData(

    id = 10,
    name = company,
    owner_account_id = 49,

    counts = orgCountData,
    settings = orgSettings,
    plan = orgPlan,

    is_agency = true,
    trial_ends_at = DateTime.now().plusDays(100),
    error = None,
    error_code = None,
    paused_till = None,
    errors = Seq(),
    warnings = Seq(),
    via_referral = false,
    org_metadata = orgMetadata
  )

  val teamMemberLite = TeamMemberLite(

    user_id = 2L,
    first_name = Some("first_name"),
    last_name = Some("last_name"),
    email = "<EMAIL>",
    active = true,
    timezone = Some("campaignTimezone"),
    twofa_enabled = true,
    created_at = aDate,
    user_uuid = AccountUuid("uuid"),
    team_role = TeamAccountRole.ADMIN

  )


  val teamMember: TeamMember = TeamMember(
    team_id = teamId,
    team_name = "team_name",
    user_id = 2L,
    ta_id = 49L, // dont send ta_id to frontend / api response, only for internal purpose, its dynamically assigned in AuthUtils
    first_name = Some(first_name),
    last_name = Some(last_name),
    email = "<EMAIL>",
    team_role = TeamAccountRole.ADMIN,
    api_key = Some("apiKey"),
    zapier_key = Some("zapier_key")
  )


  val prospect_CategoriesInDB = ProspectCategoriesInDB(
    id = 22L,
    name = "Completed",
    text_id = "Done",
    label_color = "Blue",
    is_custom = true,
    team_id = teamId,
    rank = ProspectCategoryRank(rank = 2000),
  )

  val adminDefaultPermissions = RolePermissionDataDAOV2.defaultRoles(
    role = TeamAccountRole.ADMIN,
    simpler_perm_flag = false
  )

  val rolePermissionData = RolePermissionDataV2.toRolePermissionApi(
    data = adminDefaultPermissions.copy(id = 10)
  )

  val team_account: TeamAccount = TeamAccount(

    team_id = teamId,
    org_id = 20L,

    role_from_db = Some(adminDefaultPermissions), // MUST come from db (option type only for cacheservice error), should not be sent to frontend, only intermediate

    role = Some(rolePermissionData), // should be sent to frontend

    active = true,
    is_actively_used = true,
    team_name = "team_name",
    total_members = 5,
    access_members = Seq(teamMember),
    all_members = Seq(teamMemberLite),

    prospect_categories_custom = Seq(prospect_CategoriesInDB),
    max_emails_per_prospect_per_day = 100L,
    max_emails_per_prospect_per_week = 500L,
    max_emails_per_prospect_account_per_day = 97,
    max_emails_per_prospect_account_per_week = 497,

    reply_handling = ReplyHandling.PAUSE_SPECIFIC_CAMPAIGN_ON_REPLY,
    created_at = aDate,
    selected_calendar_data = None,
    team_uuid = TeamUuid("uuid")
  )

  val accountAdmin = Account(
    id = AccountUuid("account_uuid"),
    internal_id = 2,
    email = email,
    email_verification_code = None,
    email_verification_code_created_at = None,
    created_at = DateTime.now().minusDays(1000),
    first_name = Some(first_name),
    last_name = Some(last_name),
    company = Some(company),
    timezone = None,
    profile = profile,
    org_role = Some(OrganizationRole.OWNER),
    teams = Seq(team_account),
    account_type = AccountType.AGENCY,
    org = org,
    active = true,
    email_notification_summary = "dSFA",
    account_metadata = accountMetadata,
    email_verified = true,
    signupType = None,
    account_access = AccountAccess(
      inbox_access = false
    ),
    calendar_account_data = None

  )
  val campaignSettings = CampaignSettings(

    // settings
    campaign_email_settings = List(
      CampaignEmailSettings(
        campaign_id = CampaignId(campaign_id),
        sender_email_setting_id = EmailSettingId(2),
        receiver_email_setting_id = EmailSettingId(11),
        team_id = TeamId(teamId),
        uuid = CampaignEmailSettingsUuid("temp_setting_id"),
        id = CampaignEmailSettingsId(123),
        sender_email = "<EMAIL>",
        receiver_email = "<EMAIL>",
        max_emails_per_day_from_email_account = 100,
        signature = Some("emailsignature"),
        error = None,
        from_name = None
      )
    ),
    campaign_linkedin_settings = List(
      LinkedinSettingSenderDetailsFixtures.linkedin_setting_sender_details
    ),
    campaign_call_settings = List(
      CallSettingSenderDetailsFixtures.call_setting_sender_details
    ),
    campaign_whatsapp_settings = List(
      WhatsappSettingSenderDetailsFixtures.whatsapp_setting_sender_details
    ),
    campaign_sms_settings = List(
      SmsSettingSenderDetailsFixtures.sms_setting_sender_details
    ),
    timezone = "thisistimezone",
    daily_from_time = 2, // time since beginning of day in seconds
    daily_till_time = 3, // time since beginning of day in seconds
    sending_holiday_calendar_id = Some(123L),

    ai_sequence_status = None,

    // Sunday is the first day
    days_preference = List(true, true, true, true, true, true, false),

    mark_completed_after_days = 33,
    max_emails_per_day = 100,
    open_tracking_enabled = true,
    click_tracking_enabled = true,
    enable_email_validation = true,
    ab_testing_enabled = true,
    send_plain_text_email = Some(false),
    campaign_type = CampaignType.MultiChannel,

    // warm up
    warmup_started_at = Some(aDate.minusDays(3)),
    warmup_length_in_days = Some(2),
    warmup_starting_email_count = Some(5),
    show_soft_start_setting = false,

    // schedule start
    schedule_start_at = Some(aDate.minusDays(1)),
    schedule_start_at_tz = Some("Sometimezone"),


    email_priority = CampaignEmailPriority.FIRST_EMAIL,
    append_followups = true,
    opt_out_msg = "opt out msg",
    opt_out_is_text = true,
    add_prospect_to_dnc_on_opt_out = true,
    triggers = Seq(),
    sending_mode = None,
    selected_calendar_data = None
  )

  val allCampaignStats = AllCampaignStats(
    total_sent = 1,
    total_opened = 1,
    total_clicked = 1,
    total_replied = 1,
    total_steps = 1,
    current_prospects = 1,
    current_opted_out = 1,
    current_completed = 1,
    current_bounced = 1,
    current_to_check = 1,
    current_failed_email_validation = 1,
    current_in_progress = 1,
    current_unsent_prospects = 1,
    current_do_not_contact = 1,
    reply_sentiment_stats = ReplySentimentStats(
      positive = 0
    )
  )

  val campaign_uuid = s"cmp_${teamId}_cfknacskndjcn"

  val campaign = CampaignWithStatsAndEmail(
    id = 101L,
    uuid = Some(campaign_uuid),
    team_id = teamId,
    shared_with_team = true,
    name = campaign_name,
    owner_name = first_name,
    owner_email = email,
    owner_id = ownerId,
    status = CampaignStatus.RUNNING,
    tags = Seq(CampaignTag(11L, "tag1", CampaignTagUuid("tags_abcefgh"))),
    spam_test_exists = false,
    warmup_is_on = false,

    stats = allCampaignStats,

    head_step_id = Some(22L),

    ai_generation_context = None,

    settings = campaignSettings,

    created_at = aDate,

    error = Some("campaign error"),

    is_archived = false
  )

  val prospectCreateFormdata = ProspectCreateFormData(
    email = Some(email),
    first_name = Some(first_name),
    last_name = Some(last_name),
    custom_fields = Json.obj("followers" -> 500),

    // should not break in old ui/integrations, currently used only inside createOrUpdateOne controller
    owner_id = Some(ownerId),

    list = Some("list"),
    company = Some(company),
    city = Some("kolkata"),
    country = Some("India"),
    timezone = Some("Asia/Kolkata"),
    created_at = None,

    state = None,
    job_title = None,
    phone = None,
    phone_2 = None,
    phone_3 = None,
    linkedin_url = None
  )

  val validateData = Option(prospectCreateFormdata) match {
    case None => JsError(s"Fatal JSError")
    case Some(value) => JsSuccess(value)
  }


  val teamMemberBasic = TeamMemberBasic(
    team_id = teamId,
    user_id = teamMember.user_id,
    ta_id = team_account.team_id,
    first_name = first_name,
    last_name = last_name
  )

  val upsertSQLResult = UpsertSQLResult(
    createdProspectEmails = Seq(),
    createdProspectIds = Seq(),
    updatedIds = Seq(),

    duplicateProspectsData = Seq(),
    duplicatesIgnoredEmailsBecauseOfNoEditPermission = Seq("duplicatewithotherteam"),

    // will be used for assigning prospects to campaign when force-update is false but campaign is selected
    // earlier if force-update was false, duplicate prospects were not getting assigned to campaign also
    duplicateProspectIdsWithEditPermission = Seq(),
    totalDNCProspects = 0,
    prospectDataForApi = List()
  )

  val upsertSQLResult2 = UpsertSQLResult(
    createdProspectEmails = Seq(),
    createdProspectIds = Seq(),
    updatedIds = Seq(),

    duplicateProspectsData = Seq(),
    duplicatesIgnoredEmailsBecauseOfNoEditPermission = Seq(),

    // will be used for assigning prospects to campaign when force-update is false but campaign is selected
    // earlier if force-update was false, duplicate prospects were not getting assigned to campaign also
    duplicateProspectIdsWithEditPermission = Seq(),
    totalDNCProspects = 0,
    prospectDataForApi = List()
  )
  val upsertSQLResult3 = UpsertSQLResult(
    createdProspectEmails = Seq(NewlyCreatedProspect(1L, "<EMAIL>")),
    createdProspectIds = Seq(),
    updatedIds = Seq(),

    duplicateProspectsData = Seq(),
    duplicatesIgnoredEmailsBecauseOfNoEditPermission = Seq(),

    // will be used for assigning prospects to campaign when force-update is false but campaign is selected
    // earlier if force-update was false, duplicate prospects were not getting assigned to campaign also
    duplicateProspectIdsWithEditPermission = Seq(),
    totalDNCProspects = 0,
    prospectDataForApi = List()
  )

  val cpAssignResult = CPAssignResult(

    prospectIdsIgnoredBecauseAlreadyAssignedToThisCampaign = List(),

    prospectIdsIgnoredBecauseInOtherCampaigns = List(),

    newlyAssignedProspectIds = List(1L)

  )

  val dummyProspectObj = ProspectObject(
    id = 1,

    owner_id = 0,

    team_id = 0,

    first_name = Some(""),
    last_name = None,

    email = Some(email),

    last_contacted_at = None,
    last_contacted_at_phone = None,

    created_at = DateTime.now,

    custom_fields = Json.obj(),

    list = None,

    company = None,
    job_title = None,
    phone = None,
    phone_2 = None,
    phone_3 = None,

    linkedin_url = None,

    city = None,
    state = None,
    country = None,
    timezone = None,
    latest_reply_sentiment_uuid = None,

    prospect_category = "",

    internal = {

      ProspectFixtures.prospectObjectInternal
    },
    current_step_type = None,
    latest_task_done_at = None,
    prospect_uuid = Some(ProspectUuid("prs_aa_abcdefghi")),
    owner_uuid = AccountUuid("acc_aa_abcdegfhi"),
    updated_at = DateTime.now()
  )


  val campaignWithStatsAndEmail = CampaignWithStatsAndEmail(
    id = 1L,
    uuid = Some(campaign_uuid),
    team_id = teamId,
    shared_with_team = false,
    name = "Animesh",
    owner_name = first_name,
    owner_email = email,
    owner_id = ownerId,
    status = CampaignStatus.RUNNING,
    tags = Seq[CampaignTag](),
    spam_test_exists = true,
    warmup_is_on = false,
    stats = allCampaignStats,
    head_step_id = None,
    ai_generation_context = None,
    settings = campaignSettings,
    created_at = aDate,
    error = None,
    is_archived = false
  )
  val campaignBasicDetails = CampaignBasicDetails(
    id = 1,
    account_id = 2,
    team_id = teamId,
    name = "Shubham",
    head_step_id = None
  )

  val equalColumnOperator = SearchQueryColumnOperator(
    display_name = SearchQueryOperators.displayNames(SearchQueryOperators.EQUAL),
    key = SearchQueryOperators.EQUAL
  )

  val textFieldOperators = Seq(
    SearchQueryColumnOperator(
      display_name = SearchQueryOperators.displayNames(SearchQueryOperators.CONTAINS),
      key = SearchQueryOperators.CONTAINS
    ),

    SearchQueryColumnOperator(
      display_name = SearchQueryOperators.displayNames(SearchQueryOperators.NOT_CONTAINS),
      key = SearchQueryOperators.NOT_CONTAINS
    ),

    equalColumnOperator,

    SearchQueryColumnOperator(
      display_name = SearchQueryOperators.displayNames(SearchQueryOperators.NOT_EQUAL),
      key = SearchQueryOperators.NOT_EQUAL
    )
  )

  val columnDef = ColumnDef(
    display_name = "Prospect email",
    name = "email",
    field_type = FieldTypeEnum.TEXT,
    show_in_datagrid = true,
    sortable = true,
    allowed_filter_operators = textFieldOperators,
    filterable = true
  )


  describe("ProspectUploadService.uploadCSVViaCronV2") {

    it("campaignDAO.find sends none") {

      (campaignService.findBasicDetailsWithPermission(_: Long, _: Seq[Long], _: Long))
        .expects(121, List(2L), 37)
        .returning(Success(None))
      prospectUploadService.uploadCSVViaCronV2(
        doerAccount = accountAdmin,
        campaignId = Some(campaign_id),
        ownerAccountId = ownerId,
        teamId = teamId,
        taId = teamId,
        prospectTags = None,
        ignoreProspectsInOtherCampaigns = IgnoreProspectsInOtherCampaigns.DoNotIgnore,
        forceChangeOwnershipToMe = false,
        forceUpdateProspects = false,
        ignore_email_empty_rows = false,
        fileName = "fileName",
        list_name = list_name,
        mappingFromClient = Map[String, String](),
        rowMapFromCSV = Seq[Map[String, String]](),
        deduplicationColumns = None,
        SRLogger = Logger
      ) match {
        case Success(value) =>
          Logger.info(s"result_____________$value")

          assert(false)

        case Failure(err) =>
          Logger.info(s"result_____________$err")

          assert(err.getMessage == "Selected owner (or you) do not have permission fot the given campign. Could you check and try again ?")
      }
    }


//    it("fails csvRowsTry because no data sent") {
//
//      (campaignService.findBasicDetailsWithPermission(_: Long, _: Seq[Long], _: Long))
//        .expects(121, List(2L), 37)
//        .returning(Success(Some(campaignBasicDetails)))
//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(_: TeamId, _: SrRollingUpdateFeature)(_: SRLogger))
//        .expects(TeamId(teamId), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .returning(false)
//
//      (prospectColumnDef.allColumns(_: Long, _: Option[ChannelType]))
//        .expects(37, None)
//        .returning(List())
//      prospectUploadService.uploadCSVViaCronV2(
//        doerAccount = accountAdmin,
//        campaignId = Some(campaign_id),
//        ownerAccountId = ownerId,
//        teamId = teamId,
//        taId = teamId,
//        prospectTags = None,
//        ignoreProspectsInOtherCampaigns = IgnoreProspectsInOtherCampaigns.DoNotIgnore,
//        forceChangeOwnershipToMe = false,
//        forceUpdateProspects = false,
//        ignore_email_empty_rows = false,
//        fileName = "fileName",
//        list_name = list_name,
//        mappingFromClient = Map[String, String](),
//        rowMapFromCSV = Seq[Map[String, String]](),
//        deduplicationColumns = None,
//        SRLogger = Logger
//      ) match {
//        case Success(value) =>
//          Logger.info(s"result_____________$value")
//
//          assert(false)
//
//        case Failure(err) =>
//          Logger.info(s"result_____________$err")
//
//          assert(err.getMessage == "Malformed CSV file. The csv file does not have valid content. Additional info: email must be mapped to a CSV header")
//      }
//    }
//
//    it("Error while uploading prospects") {
//
//      (campaignService.findBasicDetailsWithPermission(_: Long, _: Seq[Long], _: Long))
//        .expects(121, List(2L), 37)
//        .returning(Success(Some(campaignBasicDetails)))
//
//
//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(_: TeamId, _: SrRollingUpdateFeature)(_: SRLogger))
//        .expects(TeamId(teamId), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .repeat(2)
//        .returning(false)
//
//      (prospectColumnDef.allColumns(_: Long, _: Option[ChannelType]))
//        .expects(37, None)
//        .returning(List(columnDef))
//
//      (prospectServiceV2.findByEmailOwnedByOthers(_: Seq[String], _: Long, _: Long)(_: SRLogger))
//        .expects(List("<EMAIL>"), 37, 2, *)
//        .returning(Success(Seq()))
//
//      (campaignProspectService.unassignProspectsFromMainPage)
//        .expects(List(2L), 2L, 37L, "Adminfirst Adminlast", List[Long](), List[Long](), *)
//        .returning(Success(1))
//      (prospectServiceV2.forceChangeOwnershipFromUpload)
//        .expects(2, "Adminfirst Adminlast", 37, 37, List(), *)
//        .returning(Success(1))
//
//      (prospectService.createOrUpdateProspects)
//        .expects(ownerId, teamId, list_name, *, UpdateProspectType.None, true, accountAdmin, None, None, Some(121L), None, IgnoreProspectsInOtherCampaigns.DoNotIgnore, None, None, None, *, TriggerSource.OTHER)
//        .returning(Failure(Error))
//
//
//      prospectUploadService.uploadCSVViaCronV2(
//        doerAccount = accountAdmin,
//        campaignId = Some(campaign_id),
//        ownerAccountId = ownerId,
//        teamId = teamId,
//        taId = teamId,
//        prospectTags = None,
//        ignoreProspectsInOtherCampaigns = IgnoreProspectsInOtherCampaigns.DoNotIgnore,
//        forceChangeOwnershipToMe = true,
//        forceUpdateProspects = false,
//        ignore_email_empty_rows = false,
//        fileName = "fileName",
//        list_name = list_name,
//        mappingFromClient = Map[String, String]("email" -> "email"),
//        rowMapFromCSV = Seq[Map[String, String]](Map[String, String]("email" -> "<EMAIL>")),
//        deduplicationColumns = None,
//        SRLogger = Logger
//      ) match {
//        case Success(value) =>
//          Logger.info(s"result_____________$value")
//
//          assert(false)
//
//        case Failure(err) =>
//          Logger.info(s"result_____________$err")
//
//          assert(err.getMessage == "Error while uploading prospects: java.lang.Throwable: Error")
//      }
//    }

    val upsertSqlProspectData = UpsertSQLProspectData(
      prospect_email = "prospect@gmailcom", prospect_data = InsertOrUpdateProspectResult(
        prospect_id = 1L,
        prospect_uuid = Some("prs_aa_qwerty"),
        is_updated_internal = true,
        account_id = 2L
      )
    )

    val createOrUpdateProspectsResult = CreateOrUpdateProspectsResult(
      invalid_emails = Seq[String](),
      duplicate_prospects_data = Seq[DuplicateProspectResult](),
      duplicate_ids_with_edit_permission = Seq[Long](),
      created_ids = Seq[Long](1, 2, 3, 4, 5),
      updated_ids = Seq[Long](6, 7, 8, 9),
      assigned_ids = Seq[Long](1, 2, 3, 4, 5, 6, 7),
      duplicates_ignored_for_no_edit_permission = Seq[String](),
      ignored_internal_emails = Seq[String](),
      total_duplicate_emails = 0,
      total_dnc_prospects = 0,
      inserted_or_updated_prospects = List(upsertSqlProspectData)
    )


//    it("Success with emailValidationService.sendProspectsForValidation failing") {
//
//      (campaignService.findBasicDetailsWithPermission(_: Long, _: Seq[Long], _: Long))
//        .expects(121, List(2L), 37)
//        .returning(Success(Some(campaignBasicDetails)))
//
//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(_: TeamId, _: SrRollingUpdateFeature)(_: SRLogger))
//        .expects(TeamId(teamId), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .repeat(2)
//        .returning(false)
//
//      (prospectColumnDef.allColumns(_: Long, _: Option[ChannelType]))
//        .expects(37, None)
//        .returning(List(columnDef))
//
//      (prospectService.createOrUpdateProspects)
//        .expects(ownerId, teamId, list_name, *, UpdateProspectType.None, true, accountAdmin, None, None, Some(121L), None, IgnoreProspectsInOtherCampaigns.DoNotIgnore, None, None, None, *, TriggerSource.OTHER)
//        .returning(Success(createOrUpdateProspectsResult))
//
//      val prospectIdsForValidation = IdsOrEmailsForValidation.ProspectIdsForValidation(
//        prospectIds = Seq(1, 2, 3, 4, 5).map(id => ProspectId(id = id)),
//        initiatorCampaign = CampaignId(id = 121L),
//      )
//
//      (emailValidationService.sendProspectsForValidation)
//        .expects(EmailValidationPriority.High, *, 2, TeamId(37), 10, true, prospectIdsForValidation)
//        .returning(Failure(Error))
//
//      prospectUploadService.uploadCSVViaCronV2(
//        doerAccount = accountAdmin,
//        campaignId = Some(campaign_id),
//        ownerAccountId = ownerId,
//        teamId = teamId,
//        taId = teamId,
//        prospectTags = None,
//        ignoreProspectsInOtherCampaigns = IgnoreProspectsInOtherCampaigns.DoNotIgnore,
//        forceChangeOwnershipToMe = false,
//        forceUpdateProspects = false,
//        ignore_email_empty_rows = false,
//        fileName = "fileName",
//        list_name = list_name,
//        mappingFromClient = Map[String, String]("email" -> "email"),
//        rowMapFromCSV = Seq[Map[String, String]](Map[String, String]("email" -> "<EMAIL>")),
//        deduplicationColumns = None,
//        SRLogger = Logger
//      ) match {
//        case Success(value) =>
//          Logger.info(s"result_____________$value")
//
//          assert(value == UploadCSVResult.UploadProspectCSVResult(1, 5, 4, 0, 0, 7, 0, Some(0), 0, Some(0)))
//
//        case Failure(err) =>
//          Logger.info(s"result_____________$err")
//
//          assert(false)
//      }
//    }
//
//    it("Success with emailValidationService.sendProspectsForValidation passing") {
//
//      (campaignService.findBasicDetailsWithPermission(_: Long, _: Seq[Long], _: Long))
//        .expects(121, List(2L), 37)
//        .returning(Success(Some(campaignBasicDetails)))
//
//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(_: TeamId, _: SrRollingUpdateFeature)(_: SRLogger))
//        .expects(TeamId(teamId), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .repeat(2)
//        .returning(false)
//
//      (prospectColumnDef.allColumns(_: Long, _: Option[ChannelType]))
//        .expects(37, None)
//        .returning(List(columnDef))
//
//      (prospectService.createOrUpdateProspects)
//        .expects(ownerId, teamId, list_name, *, UpdateProspectType.None, true, accountAdmin, None, None, Some(121L), None, IgnoreProspectsInOtherCampaigns.DoNotIgnore, None, None, None, *, TriggerSource.OTHER)
//        .returning(Success(createOrUpdateProspectsResult))
//
//      val prospectIdsForValidation = IdsOrEmailsForValidation.ProspectIdsForValidation(
//        prospectIds = Seq(1, 2, 3, 4, 5).map(id => ProspectId(id = id)),
//        initiatorCampaign = CampaignId(id = 121L),
//      )
//
//      (emailValidationService.sendProspectsForValidation)
//        .expects(EmailValidationPriority.High, *, 2, TeamId(37), 10, true, prospectIdsForValidation)
//        .returning(Success(1))
//
//      prospectUploadService.uploadCSVViaCronV2(
//        doerAccount = accountAdmin,
//        campaignId = Some(campaign_id),
//        ownerAccountId = ownerId,
//        teamId = teamId,
//        taId = teamId,
//        prospectTags = None,
//        ignoreProspectsInOtherCampaigns = IgnoreProspectsInOtherCampaigns.DoNotIgnore,
//        forceChangeOwnershipToMe = false,
//        ignore_email_empty_rows = false,
//        forceUpdateProspects = false,
//        fileName = "fileName",
//        list_name = list_name,
//        mappingFromClient = Map[String, String]("email" -> "email", "list" -> list_name.get),
//        rowMapFromCSV = Seq[Map[String, String]](Map[String, String]("email" -> "<EMAIL>")),
//        deduplicationColumns = None,
//        SRLogger = Logger
//      ) match {
//        case Success(value) =>
//          Logger.info(s"result_____________$value")
//
//          assert(value == UploadCSVResult.UploadProspectCSVResult(1, 5, 4, 0, 0, 7, 0, Some(0), 0, Some(0)))
//
//        case Failure(err) =>
//          Logger.info(s"result_____________$err")
//
//          assert(false)
//      }
//    }

    it("Should go to new path for email optional if getCSVValueV2 return none for email") {

      val emailOptionPathAdmin = accountAdmin.copy(org = accountAdmin.org.copy(id = 70L))

      (campaignService.findBasicDetailsWithPermission(_: Long, _: Seq[Long], _: Long))
        .expects(121, List(2L), 37)
        .returning(Success(Some(campaignBasicDetails)))


//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(_: TeamId, _: SrRollingUpdateFeature)(_: SRLogger))
//        .expects(TeamId(37), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .repeat(2)
//        .returning(true)


      (prospectColumnDef.allColumns(_: Long, _: Option[ChannelType]))
        .expects(37, None)
        .returning(List(columnDef))

      val prospectList = List(prospectCreateFormdata.copy(email = Some("<EMAIL>")), prospectCreateFormdata.copy(email = None))

      (prospectService.getProspectsDbValidationData)
        .expects(*, TeamId(teamId))
        .returning(Success(prospectList.map(Right(_))))

      (prospectService.createOrUpdateProspects)
        .expects(ownerId, teamId, list_name, *, UpdateProspectType.None, true, emailOptionPathAdmin, None, None, Some(121L), None, IgnoreProspectsInOtherCampaigns.DoNotIgnore, None, None, None, *, TriggerSource.OTHER, 100)
        .returning(Success(createOrUpdateProspectsResult))

      val prospectIdsForValidation = IdsOrEmailsForValidation.ProspectIdsForValidation(
        prospectIds = Seq(1, 2, 3, 4, 5).map(id => ProspectId(id = id)),
        initiatorCampaign = CampaignId(id = 121L),
      )

      (emailValidationService.sendProspectsForValidation)
        .expects(EmailValidationPriority.High, *, 2, TeamId(37), 70L, true, prospectIdsForValidation)
        .returning(Success(1))

      prospectUploadService.uploadCSVViaCronV2(
        doerAccount = emailOptionPathAdmin,
        campaignId = Some(campaign_id),
        ownerAccountId = ownerId,
        teamId = teamId,
        taId = teamId,
        prospectTags = None,
        ignoreProspectsInOtherCampaigns = IgnoreProspectsInOtherCampaigns.DoNotIgnore,
        forceChangeOwnershipToMe = false,
        forceUpdateProspects = false,
        ignore_email_empty_rows = false,
        fileName = "fileName",
        list_name = list_name,
        mappingFromClient = Map[String, String]("email" -> "email", "list" -> list_name.get),
        rowMapFromCSV = Seq[Map[String, String]](Map[String, String]("email" -> "<EMAIL>"), Map[String, String]("email" -> null)),
        deduplicationColumns = None,
        SRLogger = Logger
      ) match {
        case Success(value) =>
          assert(value == UploadCSVResult.UploadProspectCSVResult(2, 5, 4, 0, 0, 7, 0, Some(0), 0, Some(0)))

        case Failure(err) =>
          println(s"Error Occurred ${err.printStackTrace()}")
          assert(false)
      }
    }

//    it("Success with ignore_email_empty_rows true") {
//
//      (campaignService.findBasicDetailsWithPermission(_: Long, _: Seq[Long], _: Long))
//        .expects(121, List(2L), 37)
//        .returning(Success(Some(campaignBasicDetails)))
//
//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(_: TeamId, _: SrRollingUpdateFeature)(_: SRLogger))
//        .expects(TeamId(teamId), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .repeat(2)
//        .returning(false)
//
//      (prospectColumnDef.allColumns(_: Long, _: Option[ChannelType]))
//        .expects(37, None)
//        .returning(List(columnDef))
//
//      (prospectService.createOrUpdateProspects)
//        .expects(ownerId, teamId, list_name, *, UpdateProspectType.None, true, accountAdmin, None, None, Some(121L), None, IgnoreProspectsInOtherCampaigns.DoNotIgnore, None, None, None, *, TriggerSource.OTHER)
//        .returning(Success(createOrUpdateProspectsResult.copy(
//          created_ids = Seq(1),
//          updated_ids = Seq(),
//          assigned_ids = Seq(),
//          duplicate_prospects_data = Seq()
//        )))
//
//      val prospectIdsForValidation = IdsOrEmailsForValidation.ProspectIdsForValidation(
//        prospectIds = Seq(1).map(id => ProspectId(id = id)),
//        initiatorCampaign = CampaignId(id = 121L),
//      )
//
//      (emailValidationService.sendProspectsForValidation)
//        .expects(EmailValidationPriority.High, *, 2, TeamId(37), 10, true, prospectIdsForValidation)
//        .returning(Success(1))
//
//      prospectUploadService.uploadCSVViaCronV2(
//        doerAccount = accountAdmin,
//        campaignId = Some(campaign_id),
//        ownerAccountId = ownerId,
//        teamId = teamId,
//        taId = teamId,
//        prospectTags = None,
//        ignoreProspectsInOtherCampaigns = IgnoreProspectsInOtherCampaigns.DoNotIgnore,
//        forceChangeOwnershipToMe = false,
//        ignore_email_empty_rows = true,
//        forceUpdateProspects = false,
//        fileName = "fileName",
//        list_name = list_name,
//        mappingFromClient = Map[String, String]("email" -> "email", "list" -> list_name.get, "phone" -> "phone"),
//        rowMapFromCSV = Seq[Map[String, String]](Map[String, String]("email" -> "<EMAIL>"), Map[String, String]("phone" -> "+************")),
//        deduplicationColumns = None,
//        SRLogger = Logger
//      ) match {
//        case Success(value) =>
//          Logger.info(s"result_____________$value")
//
//          assert(value == UploadCSVResult.UploadProspectCSVResult(2, 1, 0, 1, 0, 0, 0, Some(0), 0, Some(0)))
//
//        case Failure(err) =>
//          Logger.info(s"result_____________$err")
//
//          assert(false)
//      }
//    }


  }

  describe("ProspectUploadService.getValidAndInvalidProspects") {

    val old_path_org = OrgId(10L)
    val new_path_org = OrgId(70L)
    val old_path_teamId = TeamId(22L)
    val new_path_teamId = TeamId(33L)

    it("should return failure if getProspectsDbValidationData throws DBFailure in new path") {
      val prospectList = List(prospectCreateFormdata, prospectCreateFormdata.copy(email = Some("<EMAIL>")))

      (prospectService.getProspectsDbValidationData)
        .expects(prospectList, new_path_teamId)
        .returning(Failure(new Throwable("DB failure")))

      val res: Try[ValidInvalidProspectsFromCSV] = prospectUploadService.getValidAndInvalidProspects(
        team_id = new_path_teamId,
        filteredRowsFromCSV = List(prospectCreateFormdata, prospectCreateFormdata.copy(email = Some("<EMAIL>")))
      )

      res match {
        case Failure(exception) => {
          assert(true)
        }
        case Success(value) => assert(false)
      }
    }

    it("should return valid rows if we get Right() response from getProspectsDbValidationData") {

      val prospectList = List(prospectCreateFormdata, prospectCreateFormdata.copy(email = Some("<EMAIL>")))

      (prospectService.getProspectsDbValidationData)
        .expects(prospectList, new_path_teamId)
        .returning(Success(prospectList.map(Right(_))))

      prospectUploadService.getValidAndInvalidProspects(
        team_id = new_path_teamId,
        filteredRowsFromCSV = prospectList
      ) match {
        case Failure(exception) => {

          assert(false)
        }
        case Success(res) => assert(res.valid.length == 2 && res.invalid.isEmpty)
      }
    }

    it("should return invalid rows if we get Left() response from getProspectsDbValidationData") {

      val prospectList = List(prospectCreateFormdata, prospectCreateFormdata.copy(email = Some("<EMAIL>")))

      val listOfProspectsWithDataFromDifferentRowsInDb = ListOfProspectsWithDataFromDifferentRowsInDb(
        prospectsDuplicateFound = List(ProspectsKeyMatchingInDB(
          prospect_id = 55L,
          column_name = SrProspectColumns.Email, column_value = email
        )),
        originalProspectRow = prospectCreateFormdata
      )

//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(_: TeamId, _: SrRollingUpdateFeature)(_: SRLogger))
//        .expects(new_path_teamId, SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .returning(true)

      (prospectService.getProspectsDbValidationData)
        .expects(prospectList, new_path_teamId)
        .returning(Success(List(
          Left(listOfProspectsWithDataFromDifferentRowsInDb),
          Left(listOfProspectsWithDataFromDifferentRowsInDb.copy(
            originalProspectRow = prospectCreateFormdata.copy(
              email = Some("<EMAIL>")
            )))
        )))

      prospectUploadService.getValidAndInvalidProspects(
        team_id = new_path_teamId,
        filteredRowsFromCSV = prospectList
      ) match {
        case Failure(exception) => {

          assert(false)
        }
        case Success(res) => assert(res.invalid.length == 2 && res.valid.isEmpty)
      }
    }

    it("should update phone/linkedin to None if it is invalid and already one other deduplication column present") {

      val prospectData1 = prospectCreateFormdata.copy(email = Some("<EMAIL>"), phone = Some("NA"), linkedin_url = Some("NA"))

      val prospectList: List[ProspectCreateFormData] = List(
        prospectData1
      )

      (prospectService.getProspectsDbValidationData)
        .expects(prospectList, new_path_teamId)
        .returning(Success(List(Right(prospectData1))))

      prospectUploadService.getValidAndInvalidProspects(
        team_id = new_path_teamId,
        filteredRowsFromCSV = prospectList
      ) match {
        case Failure(exception) => assert(false)
        case Success(res) =>
          assert(res.valid.length == 1 && res.invalid.isEmpty)
          val validProspectsPhone: Option[String] = res.valid.head.phone
          val validProspectsLinkedin: Option[String] = res.valid.head.linkedin_url

          assert(validProspectsPhone.isEmpty && validProspectsLinkedin.isEmpty)

      }
    }


    it("should return invalid rows if it phone/linkedin invalid and email not present") {

      val prospectData1 = prospectCreateFormdata.copy(email = None, phone = None, linkedin_url = Some("NA"))
      val prospectData2 = prospectCreateFormdata.copy(email = None, phone = Some("NA"), linkedin_url = None)
      val prospectData3 = prospectCreateFormdata.copy(email = None, phone = None, linkedin_url = Some("NA"), first_name = None, last_name = None, company = None)
      val prospectData4 = prospectCreateFormdata.copy(email = None, phone = Some("NA"), linkedin_url = None, first_name = None, last_name = None, company = None)

      val prospectList: List[ProspectCreateFormData] = List(
        prospectData1,
        prospectData2,
        prospectData3,
        prospectData4
      )

      (prospectService.getProspectsDbValidationData)
        .expects(List(prospectData1, prospectData2), new_path_teamId)
        .returning(Success(List(Right(prospectData1), Right(prospectData2))))

      prospectUploadService.getValidAndInvalidProspects(
        team_id = new_path_teamId,
        filteredRowsFromCSV = prospectList
      ) match {
        case Failure(exception) =>
          println(s"${LogHelpers.getStackTraceAsString(exception)}")
          assert(false)
        case Success(res) =>
          assert(res.valid.length == 2 && res.invalid.isEmpty)
          val validProspectsPhones: List[Option[String]] = res.valid.map(_.phone)
          val validProspectsLinkedins: List[Option[String]] = res.valid.map(_.linkedin_url)

          assert(validProspectsPhones.forall(_.isEmpty) && validProspectsLinkedins.forall(_.isEmpty))

      }
    }

  }

  describe("assignValuesToPhones") {
    it("should return phones as it is if all 3 defined") {
      val res: (Option[String], Option[String], Option[String]) = ProspectUploadService.assignValuesToPhones(
        phone1 = Some("+93829382938"),
        phone2 = Some("+93829382922"),
        phone3 = Some("+93829382955")
      )

      assert(res._1 == Some("+93829382938") && res._2 == Some("+93829382922") && res._3 == Some("+93829382955"))
    }

    it("if phone is undefined then assign phone2 and assign phone3 to phone2") {
      val res: (Option[String], Option[String], Option[String]) = ProspectUploadService.assignValuesToPhones(
        phone1 = None,
        phone2 = Some("+93829382922"),
        phone3 = Some("+93829382955")
      )

      assert(res._1 == Some("+93829382922") && res._2 == Some("+93829382955") && res._3 == None)
    }

    it("if phone is undefined then assign phone2 and assign phone3 to phone2 - phone3 none") {
      val res: (Option[String], Option[String], Option[String]) = ProspectUploadService.assignValuesToPhones(
        phone1 = None,
        phone2 = Some("+93829382922"),
        phone3 = None
      )

      assert(res._1 == Some("+93829382922") && res._2 == None && res._3 == None)
    }

    it("if phone is undefined then assign phone3 if phone2 also undefined") {
      val res: (Option[String], Option[String], Option[String]) = ProspectUploadService.assignValuesToPhones(
        phone1 = None,
        phone2 = None,
        phone3 = Some("+93829382955")
      )

      assert(res._1 == Some("+93829382955") && res._2 == None && res._3 == None)
    }

    it("if phone, phone3 is defined and phone2 undefined then assign phone3 to phone2") {
      val res: (Option[String], Option[String], Option[String]) = ProspectUploadService.assignValuesToPhones(
        phone1 = Some("+93829382938"),
        phone2 = None,
        phone3 = Some("+93829382955")
      )

      assert(res._1 == Some("+93829382938") && res._2 == Some("+93829382955") && res._3 == None)
    }

    it("if phone is defined and phone2, phone3 undefined then return as it is") {
      val res: (Option[String], Option[String], Option[String]) = ProspectUploadService.assignValuesToPhones(
        phone1 = Some("+93829382938"),
        phone2 = None,
        phone3 = None
      )

      assert(res._1 == Some("+93829382938") && res._2 == None && res._3 == None)
    }

    it("if phone, phone2 are defined and phone3 undefined then return as it is") {
      val res: (Option[String], Option[String], Option[String]) = ProspectUploadService.assignValuesToPhones(
        phone1 = Some("+93829382938"),
        phone2 = Some("+93829382922"),
        phone3 = None
      )

      assert(res._1 == Some("+93829382938") && res._2 == Some("+93829382922") && res._3 == None)
    }

    it("if phone, phone2 and phone3 none then return as it is") {
      val res: (Option[String], Option[String], Option[String]) = ProspectUploadService.assignValuesToPhones(
        phone1 = None,
        phone2 = None,
        phone3 = None
      )

      assert(res._1 == None && res._2 == None && res._3 == None)
    }
  }

  describe("getFilteredProspectPhones") {
    it("should return correct response if all phones are none") {
      val res = ProspectUploadService.getFilteredProspectPhones(
        data = prospectCreateFormdata.copy(
          phone = None,
          phone_2 = None,
          phone_3 = None
        )
      )

      assert(res._1.isEmpty && res._2.isEmpty && res._3.isEmpty)
    }

    it("should return correct response if all phones are valid") {
      val res = ProspectUploadService.getFilteredProspectPhones(
        data = prospectCreateFormdata.copy(
          phone = Some("9826785467"),
          phone_2 = Some("9826785222"),
          phone_3 = Some("9826785111")
        )
      )

      assert(res._1.contains("9826785467") &&
        res._2.contains("9826785222") &&
        res._3.contains("9826785111")
      )

    }

    it("should return correct response if phone is valid") {
      val res = ProspectUploadService.getFilteredProspectPhones(
        data = prospectCreateFormdata.copy(
          phone = Some("9826785467"),
          phone_2 = Some("a.2678.5222"),
          phone_3 = Some("a.2678.5111")
        )
      )

      assert(res._1.contains("9826785467") &&
        res._2.isEmpty &&
        res._3.isEmpty
      )
    }

    it("should return correct response if phone_2 is valid") {
      val res = ProspectUploadService.getFilteredProspectPhones(
        data = prospectCreateFormdata.copy(
          phone = Some("982.6785.a"),
          phone_2 = Some("9826785222"),
          phone_3 = Some("982.678.a")
        )
      )

      assert(res._1.contains("9826785222") &&
        res._2.isEmpty &&
        res._3.isEmpty
      )
    }

    it("should return correct response if phone_2, phone_2 are valid") {
      val res = ProspectUploadService.getFilteredProspectPhones(
        data = prospectCreateFormdata.copy(
          phone = Some("982.6785.a"),
          phone_2 = Some("9826785222"),
          phone_3 = Some("9826785111")
        )
      )

      assert(res._1.contains("9826785222") &&
        res._2.contains("9826785111") &&
        res._3.isEmpty
      )
    }
  }

}
