package app.api.prospects

import api.accounts.TeamId
import api.accounts.models.AccountId
import api.notes.models.CreateNotesForm
import api.prospects.models.ProspectId
import api.prospects.{CreateProspectEventDB, ExactIdToCompareTime, InferredQueryTimeline, ProspectEventV2}
import api.prospects.service.ProspectEventsListingPaginationService
import api.sr_audit_logs.models.EventType
import api.tasks.services.TaskUuid
import app.api.notes.NotesFixture
import db_test_spec.api.{DbTestingBeforeAllAndAfterAll, InitialData}
import db_test_spec.api.accounts.fixtures.NewAccountAndEmailSettingData
import db_test_spec.api.prospects.fixtures.ProspectFixtureForIntegrationTest
import org.joda.time.DateTime
import utils.SRLogger

import scala.util.Try

class ProspectEventDAOServiceIntegrationTests extends DbTestingBeforeAllAndAfterAll{

  given logger: SRLogger = new SRLogger("ProspectEventDAOServiceIntegrationTests ")

  describe("integration test for ProspectEventDAOService.getNotesEvents") {

    it("should create a note and return the note_added event successfully for InferredQueryTimeline.Range.Before") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
      val sampleNote = "have to watch euro semifinal today."
      val teamId = TeamId(initialData.head_team_id)
      val accountId = AccountId(initialData.account.internal_id)

      val prospect = ProspectFixtureForIntegrationTest.createUpdateOrAssignProspect(
        accountId = accountId,
        teamId = teamId,
        account = initialData.account,
        generateProspectCountIfNoGivenProspect = 1
      ).get.head

      val prospectId = ProspectId(prospect.id)
      val task_uuid = TaskUuid("task_uuid_2903")
      val addedBy = AccountId(initialData.account.internal_id)

      val createNoteForm: CreateNotesForm = NotesFixture.getCreateNotesForm(
        note = sampleNote,
        addedBy = addedBy,
        teamId = teamId,
        taskUuid = Some(task_uuid),
        prospectId = Some(prospectId)
      )

      val note_created = notesDAO.create(createNoteForm).get

      val res: Try[List[ProspectEventV2]] = prospectEventDAOService.getNotesEvents(
        queryTimeline = InferredQueryTimeline.Range.Before(DateTime.now().plusMinutes(1)),
        limit = 2,
        prospectId = Some(prospectId),
        baseEvent = ProspectEventsListingPaginationService.baseEvent,
        prospectAccountId = None,
        teamId = teamId
      )

      assert(res.isSuccess)
      assert(res.get.nonEmpty)
      assert(res.get.length == 1)

      val note_added_event = res.get
      assert(note_added_event.exists(na => na.note_id.get == note_created.noteId))
      assert(note_added_event.exists(na => na.note.get == sampleNote))
      assert(note_added_event.exists(na => na.event_type == EventType.NOTE_ADDED))

    }

    it("should create a note and return the note_added event successfully for InferredQueryTimeline.Range.After") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
      val sampleNote = "have to watch euro semifinal today."
      val teamId = TeamId(initialData.head_team_id)
      val accountId = AccountId(initialData.account.internal_id)

      val prospect = ProspectFixtureForIntegrationTest.createUpdateOrAssignProspect(
        accountId = accountId,
        teamId = teamId,
        account = initialData.account,
        generateProspectCountIfNoGivenProspect = 1
      ).get.head

      val prospectId = ProspectId(prospect.id)
      val task_uuid = TaskUuid("task_uuid_2903")
      val addedBy = AccountId(initialData.account.internal_id)

      val createNoteForm: CreateNotesForm = NotesFixture.getCreateNotesForm(
        note = sampleNote,
        addedBy = addedBy,
        teamId = teamId,
        taskUuid = Some(task_uuid),
        prospectId = Some(prospectId)
      )

      val note_created = notesDAO.create(createNoteForm).get

      val res: Try[List[ProspectEventV2]] = prospectEventDAOService.getNotesEvents(
        queryTimeline = InferredQueryTimeline.Range.After(DateTime.now().minusMinutes(1)),
        limit = 2,
        prospectId = Some(prospectId),
        baseEvent = ProspectEventsListingPaginationService.baseEvent,
        prospectAccountId = None,
        teamId = teamId
      )

      assert(res.isSuccess)
      assert(res.get.nonEmpty)
      assert(res.get.length == 1)

      val note_added_event = res.get.head
      assert(note_added_event.note_id.get == note_created.noteId)
      assert(note_added_event.note.get == sampleNote)
      assert(note_added_event.event_type == EventType.NOTE_ADDED)

    }

    it("should create a note and return the note_added event successfully for InferredQueryTimeline.Exact") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
      val sampleNote = "have to watch euro semifinal today."
      val teamId = TeamId(initialData.head_team_id)
      val accountId = AccountId(initialData.account.internal_id)

      val prospect = ProspectFixtureForIntegrationTest.createUpdateOrAssignProspect(
        accountId = accountId,
        teamId = teamId,
        account = initialData.account,
        generateProspectCountIfNoGivenProspect = 1
      ).get.head

      val prospectId = ProspectId(prospect.id)
      val task_uuid = TaskUuid("task_uuid_2903")
      val addedBy = AccountId(initialData.account.internal_id)

      val createNoteForm: CreateNotesForm = NotesFixture.getCreateNotesForm(
        note = sampleNote,
        addedBy = addedBy,
        teamId = teamId,
        taskUuid = Some(task_uuid),
        prospectId = Some(prospectId)
      )

      val note_created = notesDAO.create(createNoteForm).get

      val res: Try[List[ProspectEventV2]] = prospectEventDAOService.getNotesEvents(
        queryTimeline = InferredQueryTimeline.Exact(ExactIdToCompareTime(id = note_created.created_at.toString)),
        limit = 2,
        prospectId = Some(prospectId),
        baseEvent = ProspectEventsListingPaginationService.baseEvent,
        prospectAccountId = None,
        teamId = teamId
      )

      assert(res.isSuccess)
      assert(res.get.nonEmpty)
      assert(res.get.length == 1)

      val note_added_event = res.get.head
      assert(note_added_event.note_id.get == note_created.noteId)
      assert(note_added_event.note.get == sampleNote)
      assert(note_added_event.event_type == EventType.NOTE_ADDED)

    }

  }

  describe("tests for storing and retrieving merge_duplicates event") {

    it("should create and retrieve the merge_duplicates event") {

      val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
      val teamId = TeamId(initialData.head_team_id)
      val accountId = AccountId(initialData.account.internal_id)
      val accountName = initialData.account.first_name.getOrElse("") + " " + initialData.account.last_name.getOrElse("")

      val prospect = ProspectFixtureForIntegrationTest.createUpdateOrAssignProspect(
        accountId = accountId,
        teamId = teamId,
        account = initialData.account,
        generateProspectCountIfNoGivenProspect = 1
      ).get.head

      val prospectId = ProspectId(prospect.id)

      prospectAddEventDAO.addEvents(List(CreateProspectEventDB(
        event_type = EventType.MERGED_DUPLICATES,
        doer_account_id = Some(accountId.id),
        doer_account_name = Some(accountName),
        assigned_to_account_id = None,
        assigned_to_account_name = None,
        old_category = None,
        new_category = None,
        prospect_id = prospectId.id,
        email_thread_id = None,
        campaign_id = None,
        campaign_name = None,
        step_id = None,
        step_name = None,
        clicked_url = None,
        account_id = accountId.id,
        team_id = teamId.id,
        email_scheduled_id = None,
        created_at = DateTime.now(),
        task_type = None,
        channel_type = None,
        task_uuid = None,
        call_conference_uuid = None,

        duplicates_merged_at = Some(DateTime.now()),
        total_merged_prospects = Some(2),
        potential_duplicate_prospect_id = Some(2)
      )))

      val res = prospectEventDAOService.getOtherEvents(
        queryTimeline = InferredQueryTimeline.Range.Before(DateTime.now().plusMinutes(1)),
        limit = 10,
        prospectId = Some(prospectId.id),
        baseEvent = ProspectEventsListingPaginationService.baseEvent,
        teamId = teamId.id,
        prospectAccountId = None,
        account = Some(initialData.account),
         //emailNotCompulsoryEnabled = false
      )

      assert(res.isSuccess)
      assert(res.get.nonEmpty)

      val event = res.get.head

      assert(event.event_type == EventType.MERGED_DUPLICATES)
      assert(event.total_merged_prospects.get == 2)
      assert(event.potential_duplicate_prospect_id.get == 2)

    }

  }

}
