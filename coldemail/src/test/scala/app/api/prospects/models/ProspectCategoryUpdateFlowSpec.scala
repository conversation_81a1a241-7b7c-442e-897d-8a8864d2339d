package app.api.prospects.models

import api.accounts.models.OrgId
import api.prospects.models.{ProspectCategory, ProspectCategoryNew, ProspectCategoryUpdateFlow}
import org.scalatest.funspec.AsyncFunSpec
import utils.SRLogger

class ProspectCategoryUpdateFlowSpec extends AsyncFunSpec {

  given Logger: SRLogger = new SRLogger("ProspectCategoryUpdateFlowSpec")

  describe("Test if all transitions are defined") {

    it("should have all transitions defined for old prospect categories") {

      val areAllTransitionsDefined = ProspectCategory.values.forall { pc =>

        ProspectCategory.prospectCategoryTransitions.contains(pc)

      }

      assert(areAllTransitionsDefined)

    }

    it("should have all transitions defined for new prospect categories") {

      val areAllTransitionsDefined = ProspectCategoryNew.values.forall { pc =>

        ProspectCategoryNew.prospectCategoryNewTransitions.contains(pc)

      }

      assert(areAllTransitionsDefined)

    }

  }

  describe("Test isProspectCategoryAutoUpdateValid") {

    it("should return false, if current prospect category is DO_NOT_CONTACT") {

      val res = ProspectCategory.values.forall { newProspectCategory =>

        val isValid = ProspectCategoryUpdateFlow.isProspectCategoryAutoUpdateValid(
          currProspectCategoryTextId = ProspectCategory.DO_NOT_CONTACT.toString,
          newProspectCategoryTextId = newProspectCategory.toString,
          orgId = OrgId(1),
        )

        !isValid

      }

      assert(res)

    }

    it("should return false if current and new prospect category are the same") {

      val res = ProspectCategory.values.forall { cat =>

        !ProspectCategoryUpdateFlow.isProspectCategoryAutoUpdateValid(
          currProspectCategoryTextId = cat.toString,
          newProspectCategoryTextId = cat.toString,
          orgId = OrgId(1),
        )

      }

      assert(res)

    }

    it("should validate CONVERTED transitions") {

      val res = ProspectCategory.values.forall { newProspectCategory =>

        val isValid = ProspectCategoryUpdateFlow.isProspectCategoryAutoUpdateValid(
          currProspectCategoryTextId = ProspectCategory.CONVERTED.toString,
          newProspectCategoryTextId = newProspectCategory.toString,
          orgId = OrgId(1),
        )

        if (
          newProspectCategory == ProspectCategory.DO_NOT_CONTACT
        ) {

          isValid

        } else {

          !isValid

        }

      }

      assert(res)

    }

    it("should validate OPEN transitions") {

      val res = ProspectCategory.values.forall { newProspectCategory =>

        val isValid = ProspectCategoryUpdateFlow.isProspectCategoryAutoUpdateValid(
          currProspectCategoryTextId = ProspectCategory.OPEN.toString,
          newProspectCategoryTextId = newProspectCategory.toString,
          orgId = OrgId(1),
        )

        if (
          newProspectCategory == ProspectCategory.NOT_CATEGORIZED ||
          newProspectCategory == ProspectCategory.OPEN
        ) {

          !isValid

        } else {

          isValid

        }

      }

      assert(res)

    }

    it("should validate transitions to OPEN") {

      val res = ProspectCategory.values.forall { currProspectCategory =>

        val isValid = ProspectCategoryUpdateFlow.isProspectCategoryAutoUpdateValid(
          currProspectCategoryTextId = currProspectCategory.toString,
          newProspectCategoryTextId = ProspectCategory.OPEN.toString,
          orgId = OrgId(1),
        )

        if (currProspectCategory == ProspectCategory.NOT_CATEGORIZED ||
          currProspectCategory == ProspectCategory.DELIVERY_FAILED
        ) {

          isValid

        } else {

          !isValid

        }

      }

      assert(res)

    }

    it("should validate CONTACTED transitions") {

      val res = ProspectCategory.values.forall { newProspectCategory =>

        val isValid = ProspectCategoryUpdateFlow.isProspectCategoryAutoUpdateValid(
          currProspectCategoryTextId = ProspectCategory.CONTACTED.toString,
          newProspectCategoryTextId = newProspectCategory.toString,
          orgId = OrgId(1),
        )

        if (
          newProspectCategory == ProspectCategory.CONTACTED ||
            newProspectCategory == ProspectCategory.NOT_CATEGORIZED ||
            newProspectCategory == ProspectCategory.OPEN
        ) {

          !isValid

        } else {

          isValid

        }

      }

      assert(res)

    }

    it("should validate transitions to CONTACTED") {

      val res = ProspectCategory.values.forall { currProspectCategory =>

        val isValid = ProspectCategoryUpdateFlow.isProspectCategoryAutoUpdateValid(
          currProspectCategoryTextId = currProspectCategory.toString,
          newProspectCategoryTextId = ProspectCategory.CONTACTED.toString,
          orgId = OrgId(1),
        )

        if (
          currProspectCategory == ProspectCategory.NOT_CATEGORIZED ||
            currProspectCategory == ProspectCategory.OPEN ||
            currProspectCategory == ProspectCategory.DELIVERY_FAILED
        ) {

          isValid

        } else {

          !isValid

        }

      }

      assert(res)

    }

    it("should return false if we transition from CONTACTED to OPEN") {
      val isValid = ProspectCategoryUpdateFlow.isProspectCategoryAutoUpdateValid(
        currProspectCategoryTextId = ProspectCategory.CONTACTED.toString,
        newProspectCategoryTextId = ProspectCategory.OPEN.toString,
        orgId = OrgId(1),
      )

      assert(!isValid)
    }

    it("should validate if current prospect category is OUT_OF_OFFICE") {

      val res = ProspectCategory.values.forall { newProspectCategory =>

        val isValid = ProspectCategoryUpdateFlow.isProspectCategoryAutoUpdateValid(
          currProspectCategoryTextId = ProspectCategory.OUT_OF_OFFICE.toString,
          newProspectCategoryTextId = newProspectCategory.toString,
          orgId = OrgId(1),
        )

        val invalidNewCategories = List(
          ProspectCategory.NOT_CATEGORIZED,
          ProspectCategory.OPEN,
          ProspectCategory.CONTACTED,
          ProspectCategory.AUTO_REPLY,
        )

        if (
          newProspectCategory == ProspectCategory.OUT_OF_OFFICE ||
            invalidNewCategories.contains(newProspectCategory)
        ) {

          !isValid

        } else {

          isValid

        }

      }

      assert(res)

    }

    it("should validate if current prospect category is AUTO_REPLY") {

      val res = ProspectCategory.values.forall { newProspectCategory =>

        val isValid = ProspectCategoryUpdateFlow.isProspectCategoryAutoUpdateValid(
          currProspectCategoryTextId = ProspectCategory.AUTO_REPLY.toString,
          newProspectCategoryTextId = newProspectCategory.toString,
          orgId = OrgId(1),
        )

        val invalidNewCategories = List(
          ProspectCategory.NOT_CATEGORIZED,
          ProspectCategory.OPEN,
          ProspectCategory.CONTACTED,
        )

        if (
          newProspectCategory == ProspectCategory.AUTO_REPLY ||
            invalidNewCategories.contains(newProspectCategory)
        ) {

          !isValid

        } else {

          isValid

        }

      }

      assert(res)

    }

    it("should validate if current prospect category is DELIVERY_FAILED") {

      val res = ProspectCategory.values.forall { newProspectCategory =>

        val isValid = ProspectCategoryUpdateFlow.isProspectCategoryAutoUpdateValid(
          currProspectCategoryTextId = ProspectCategory.DELIVERY_FAILED.toString,
          newProspectCategoryTextId = newProspectCategory.toString,
          orgId = OrgId(1),
        )

        val invalidNewCategories = List(
          ProspectCategory.NOT_CATEGORIZED,
          ProspectCategory.OUT_OF_OFFICE,
          ProspectCategory.AUTO_REPLY,
          ProspectCategory.INTERESTED,
          ProspectCategory.NOT_INTERESTED,
          ProspectCategory.NOT_NOW,
          ProspectCategory.CONVERTED,
          ProspectCategory.MEETING_BOOKED,
          ProspectCategory.UNRESPONSIVE
        )

        if (
          newProspectCategory == ProspectCategory.DELIVERY_FAILED ||
            invalidNewCategories.contains(newProspectCategory)
        ) {

          !isValid

        } else {

          isValid

        }

      }

      assert(res)

    }

    it("should return false if new category is OUT_OF_OFFICE and current is in invalid list") {

      val invalidCurrentCategories = List(
        ProspectCategory.INTERESTED,
        ProspectCategory.NOT_INTERESTED,
        ProspectCategory.NOT_NOW,
        ProspectCategory.DO_NOT_CONTACT,
        ProspectCategory.CONVERTED,
        ProspectCategory.MEETING_BOOKED,
        ProspectCategory.DELIVERY_FAILED,
      )

      val res = invalidCurrentCategories.forall { current =>

        val isValid = ProspectCategoryUpdateFlow.isProspectCategoryAutoUpdateValid(
          currProspectCategoryTextId = current.toString,
          newProspectCategoryTextId = ProspectCategory.OUT_OF_OFFICE.toString,
          orgId = OrgId(1),
        )

        !isValid

      }

      assert(res)
    }

    it("should return false if new category is AUTO_REPLY and current is in invalid list") {

      val invalidCurrentCategories = List(
        ProspectCategory.INTERESTED,
        ProspectCategory.NOT_INTERESTED,
        ProspectCategory.NOT_NOW,
        ProspectCategory.DO_NOT_CONTACT,
        ProspectCategory.CONVERTED,
        ProspectCategory.MEETING_BOOKED,
        ProspectCategory.DELIVERY_FAILED,
        ProspectCategory.OUT_OF_OFFICE,
      )

      val res = invalidCurrentCategories.forall { current =>

        val isValid = ProspectCategoryUpdateFlow.isProspectCategoryAutoUpdateValid(
          currProspectCategoryTextId = current.toString,
          newProspectCategoryTextId = ProspectCategory.AUTO_REPLY.toString,
          orgId = OrgId(1),
        )

        !isValid
      }

      assert(res)
    }

    it("should return false if new category is DELIVERY_FAILED and current is in invalid list") {

      val invalidCurrentCategories = List(
        ProspectCategory.CONVERTED,
        ProspectCategory.MEETING_BOOKED,
        ProspectCategory.DO_NOT_CONTACT,
      )

      val res = invalidCurrentCategories.forall { current =>

        val isValid = ProspectCategoryUpdateFlow.isProspectCategoryAutoUpdateValid(
          currProspectCategoryTextId = current.toString,
          newProspectCategoryTextId = ProspectCategory.DELIVERY_FAILED.toString,
          orgId = OrgId(1),
        )

        !isValid
      }

      assert(res)
    }

    it("should return true for valid mapped transitions in new category system") {

      val validTransitions = List(
        (ProspectCategory.OPEN, ProspectCategory.REPLIED),
        (ProspectCategory.CONTACTED, ProspectCategory.INTERESTED),
        (ProspectCategory.NOT_CATEGORIZED, ProspectCategory.CONTACTED),
      )

      val res = validTransitions.forall { case (curr, next) =>

        val isValid = ProspectCategoryUpdateFlow.isProspectCategoryAutoUpdateValid(
          currProspectCategoryTextId = curr.toString,
          newProspectCategoryTextId = next.toString,
          orgId = OrgId(1),
        )

        isValid
      }

      assert(res)
    }

  }

  describe("Test isProspectCategoryAutoUpdateValidNew") {

    it("should return false if current and new category are the same") {

      val res = ProspectCategoryNew.values.forall { cat =>

        !ProspectCategoryUpdateFlow.isProspectCategoryAutoUpdateValidNew(
          currProspectCategoryTextId = cat.toString,
          newProspectCategoryTextId = cat.toString
        )

      }

      assert(res)

    }

    it("should return false if current category is DO_NOT_CONTACT") {

      val res = ProspectCategoryNew.values.forall { newCat =>

        !ProspectCategoryUpdateFlow.isProspectCategoryAutoUpdateValidNew(
          currProspectCategoryTextId = ProspectCategoryNew.DO_NOT_CONTACT.toString,
          newProspectCategoryTextId = newCat.toString
        )

      }

      assert(res)

    }

    it("should return true only if CONVERTED -> DO_NOT_CONTACT") {

      val res = ProspectCategoryNew.values.forall { newCat =>

        val isValid =ProspectCategoryUpdateFlow.isProspectCategoryAutoUpdateValidNew(
          currProspectCategoryTextId = ProspectCategoryNew.CONVERTED.toString,
          newProspectCategoryTextId = newCat.toString
        )

        if (newCat == ProspectCategoryNew.DO_NOT_CONTACT
        ) {

          isValid

        } else {

          !isValid

        }

      }

      assert(res)

    }

    it("should return true only if NOT_INTERESTED -> DO_NOT_CONTACT / INTERESTED ? MEETING_BOOKED") {

      val res = ProspectCategoryNew.values.forall { newCat =>

        val isValid = ProspectCategoryUpdateFlow.isProspectCategoryAutoUpdateValidNew(
          currProspectCategoryTextId = ProspectCategoryNew.NOT_INTERESTED.toString,
          newProspectCategoryTextId = newCat.toString
        )

        if (newCat == ProspectCategoryNew.DO_NOT_CONTACT ||
          newCat == ProspectCategoryNew.INTERESTED ||
          newCat == ProspectCategoryNew.MEETING_BOOKED
        ) {

          isValid

        } else {

          !isValid

        }

      }

      assert(res)

    }

    it("should validate MEETING_BOOKED transitions") {

      val invalidNew = List(
        ProspectCategoryNew.NOT_CONTACTED,
        ProspectCategoryNew.APPROACHING,
        ProspectCategoryNew.ENGAGING,
        ProspectCategoryNew.INTERESTED,
        ProspectCategoryNew.UNRESPONSIVE
      )

      val res = ProspectCategoryNew.values.forall { newCat =>

        val isValid = ProspectCategoryUpdateFlow.isProspectCategoryAutoUpdateValidNew(
          currProspectCategoryTextId = ProspectCategoryNew.MEETING_BOOKED.toString,
          newProspectCategoryTextId = newCat.toString
        )

        if (newCat == ProspectCategoryNew.MEETING_BOOKED || invalidNew.contains(newCat)) {

          !isValid

        } else {

          isValid

        }

      }

      assert(res)

    }

    it("should validate INTERESTED transitions") {

      val invalidNew = List(
        ProspectCategoryNew.NOT_CONTACTED,
        ProspectCategoryNew.APPROACHING,
        ProspectCategoryNew.ENGAGING,
        ProspectCategoryNew.UNRESPONSIVE
      )

      val res = ProspectCategoryNew.values.forall { newCat =>

        val isValid = ProspectCategoryUpdateFlow.isProspectCategoryAutoUpdateValidNew(
          currProspectCategoryTextId = ProspectCategoryNew.INTERESTED.toString,
          newProspectCategoryTextId = newCat.toString
        )

        if (newCat == ProspectCategoryNew.INTERESTED || invalidNew.contains(newCat)) {

          !isValid

        } else {

          isValid

        }

      }

      assert(res)

    }

    it("should validate NOT_NOW transitions") {

      val invalidNew = List(
        ProspectCategoryNew.NOT_CONTACTED,
        ProspectCategoryNew.APPROACHING,
        ProspectCategoryNew.ENGAGING,
        ProspectCategoryNew.UNRESPONSIVE
      )

      val res = ProspectCategoryNew.values.forall { newCat =>

        val isValid = ProspectCategoryUpdateFlow.isProspectCategoryAutoUpdateValidNew(
          currProspectCategoryTextId = ProspectCategoryNew.NOT_NOW.toString,
          newProspectCategoryTextId = newCat.toString

        )

        if (newCat == ProspectCategoryNew.NOT_NOW || invalidNew.contains(newCat)) {

          !isValid

        } else {

          isValid

        }

      }

      assert(res)

    }

    it("should allow all transitions from NOT_CONTACTED") {

      val res = ProspectCategoryNew.values.forall { newCat =>

        val isValid = ProspectCategoryUpdateFlow.isProspectCategoryAutoUpdateValidNew(
          currProspectCategoryTextId = ProspectCategoryNew.NOT_CONTACTED.toString,
          newProspectCategoryTextId = newCat.toString
        )

        if (newCat == ProspectCategoryNew.NOT_CONTACTED) !isValid else isValid

      }

      assert(res)

    }

    it("should validate APPROACHING transitions") {

      val res = ProspectCategoryNew.values.forall { newCat =>

        val isValid = ProspectCategoryUpdateFlow.isProspectCategoryAutoUpdateValidNew(
          currProspectCategoryTextId = ProspectCategoryNew.APPROACHING.toString,
          newProspectCategoryTextId = newCat.toString
        )

        if (
          newCat == ProspectCategoryNew.NOT_CONTACTED ||
            newCat == ProspectCategoryNew.APPROACHING
        ) {

          !isValid

        } else {

          isValid

        }

      }

      assert(res)

    }

    it("should validate ENGAGING transitions") {

      val invalidNew = List(
        ProspectCategoryNew.NOT_CONTACTED,
        ProspectCategoryNew.APPROACHING,
        ProspectCategoryNew.UNRESPONSIVE
      )

      val res = ProspectCategoryNew.values.forall { newCat =>

        val isValid = ProspectCategoryUpdateFlow.isProspectCategoryAutoUpdateValidNew(
          currProspectCategoryTextId = ProspectCategoryNew.ENGAGING.toString,
          newProspectCategoryTextId = newCat.toString,
        )

        if (newCat == ProspectCategoryNew.ENGAGING || invalidNew.contains(newCat)) {

          !isValid

        } else {

          isValid

        }

      }

      assert(res)

    }

    it("should validate UNRESPONSIVE transitions") {

      val invalidNew = List(
        ProspectCategoryNew.NOT_CONTACTED,
        ProspectCategoryNew.APPROACHING
      )

      val res = ProspectCategoryNew.values.forall { newCat =>

        val isValid = ProspectCategoryUpdateFlow.isProspectCategoryAutoUpdateValidNew(
          currProspectCategoryTextId = ProspectCategoryNew.UNRESPONSIVE.toString,
          newProspectCategoryTextId = newCat.toString,
        )

        if (newCat == ProspectCategoryNew.UNRESPONSIVE || invalidNew.contains(newCat)) {

          !isValid

        } else {

          isValid

        }

      }

      assert(res)

    }

  }

}
