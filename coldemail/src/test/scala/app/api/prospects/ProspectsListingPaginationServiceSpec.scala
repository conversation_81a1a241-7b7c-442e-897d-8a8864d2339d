package app.api.prospects

import api.accounts.TeamId
import api.prospects.{InferredQueryTimeline, ProspectsApiSearchParams}
import api.prospects.dao_service.ProspectDAOService
import api.prospects.service.ProspectsListingPaginationService
import org.joda.time.DateTime
import org.scalamock.scalatest.AsyncMockFactory
import org.scalatest.funspec.AsyncFunSpec
import utils.SRLogger

import scala.util.{Failure, Success}

class ProspectsListingPaginationServiceSpec extends AsyncFunSpec with AsyncMockFactory{

  val prospectDAOService: ProspectDAOService = mock[ProspectDAOService]

  val prospectsListingPaginationService = new ProspectsListingPaginationService(
    prospectDAOService = prospectDAOService
  )

  val currentDate: DateTime = DateTime.now()

  val teamId: TeamId = TeamId(80L)

  given Logger: SRLogger = new SRLogger("blacklist pagination unit test")

  val filters: ProspectsApiSearchParams = ProspectsApiSearchParams(
    range = InferredQueryTimeline.Range.Before(currentDate), is_first = true
  )

  describe("ProspectsListingPaginationService.findProspectsListingPageForApi") {
    it("should return empty response if find prospects returns empty list") {

      (prospectDAOService.find)
        .expects(*, *, *, *, teamId.id, *, 101, *, *, false, Some(filters.range), *)
        .returning(Success(Seq()))

      prospectsListingPaginationService.findProspectsListingPageForApi(
        filters = filters,
        teamId = teamId
      ) match {
        case Failure(_) => assert(false)
        case Success(res) => assert(res.data.isEmpty)
      }
    }
  }
}
