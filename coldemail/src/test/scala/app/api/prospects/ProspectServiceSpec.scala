package app.api.prospects

import org.apache.pekko.actor.ActorSystem
import org.apache.pekko.stream.Materializer
import api.{AppConfig, ServerErrorException}
import api.accounts.{Account, AccountAccess, AccountDAO, AccountMetadata, AccountService, AccountType, AccountUuid, OrgCountData, OrgMetadata, OrgPlan, OrgSettings, OrganizationRole, OrganizationWithCurrentData, PermType, PermissionLevelForValidation, PermissionOwnershipV2, PermissionRequest, ProspectCategoriesInDB, ReplyHandling, RolePermV2, RolePermissionDataDAOV2, RolePermissionDataV2, RolePermissionsInDBV2, RolePermissionsV2, TeamAccount, TeamAccountRole, TeamId, TeamMember, TeamMemberBasic, TeamMemberLite}
import api.accounts.models.{AccountId, AccountProfileInfo, OrgId, ProspectAccountUuid}
import api.blacklist.BlacklistTeamLevel
import api.blacklist.dao.BlacklistProspectCheckDAO
import api.blacklist.models.DoNotContactType
import api.calendar_app.models.CalendarAccountData
import api.campaigns.dao.CampaignProspectDAO_2
import api.campaigns.models.{CallSettingSenderDetails, CampaignEmailSettingsId, CampaignType, IgnoreProspectsInOtherCampaigns, LinkedinSettingSenderDetails, SmsSettingSenderDetails, WhatsappSettingSenderDetails}
import api.campaigns.services.{CampaignId, CampaignProspectService, CampaignProspectTimezonesJedisService, CampaignService, MergeTagService}
import api.campaigns.{AssignedCampaignForProspect, CPAssignResult, CPCompleted, CampaignBasicDetails, CampaignDAO, CampaignEmailSettings, CampaignEmailSettingsUuid, CampaignProspectDAO, CampaignSettings, CampaignWithStatsAndEmail, ChannelSettingUuid}
import api.columns.{ColumnDef, ProspectColumnDef}
import api.emails.EmailSettingDAO
import api.prospects.dao.{DeDuplicateColumnTypeAndValue, DuplicateProspectResult, NewlyCreatedProspect, ProspectsEmailsDAO}
import api.prospects.dao_service.{DuplicationFindProspectData, ProspectDAOService}
import api.prospects.dao_service.{DuplicationFindProspectDataV2, ProspectDAOService}
import api.prospects.service.{PotentialDuplicateProspectService, ProspectEmail, ProspectEmailData, ProspectErrorType, ProspectObjectForApi, ProspectServiceV2}
import api.prospects.models.{ProspectCategory, ProspectCategoryId, ProspectCategoryRank, ProspectId, SrProspectColumns, UpdateProspectType}
import api.prospects.{AssignProspectsToCampaignError, AssignProspectsToCampaignResponse, BlacklistStatusForProspectUuid, CreateOrUpdateOneError, CreateOrUpdateOneResponse, CreateOrUpdateProspectBatchError, GenerateTempId, InsertOrUpdateProspectResult, InsertSecondaryProspectEmailError, InternalEmailsOrDomains, ListOfProspectsWithDataFromDifferentRowsInDb, NewProspectsToBeAdded, ProspectAccount, ProspectAccountCreateFormData, ProspectAccountCreateOrUpdateResult, ProspectAccountDAO1, ProspectAccountIdAndCustomId, ProspectBatchCreateFormData, ProspectCreateFormData, ProspectCreateFormDataV2, ProspectCreateManuallyFormData, ProspectDeduplicationColumnsData, ProspectEmailsTobeAddedInForceUpdateProspect, ProspectIdEmail, ProspectInternalEmails, ProspectService, ProspectSource, ProspectUpdateCategoryTemp, ProspectUploadService, ProspectUuid, ProspectsKeyMatchingInDB, ProspectsToBeForceUpdated, UploadCSVError, UploadCSVResult, UpsertSQLProspectData, UpsertSQLResult}
import api.campaigns.services.MergeTagService
import api.prospects.UploadCSVResult.UploadProspectCSVResult
import api.reports.{AllCampaignStats, ReplySentimentStats}
import api.sr_audit_logs.models.{CreateEventLogError, EventDataType, EventLog, EventType, OldProspectDeduplicationColumn}
import api.reports.AllCampaignStats
import api.reports.AllCampaignStats
import api.sr_audit_logs.services.EventLogService
import api.tags.{ProspectsAndTags, TagService}
import api.tags.models.{CampaignTag, CampaignTagUuid, ProspectTagUuid, TagAndUuid}
import api.team.TeamUuid
import api.triggers.AddOrRemoveTagAction
import app.test_fixtures.accounts.OrgCountDataFixture
import app.test_fixtures.prospect.ProspectCreateFormDataFixture.prospectCreateFormData
import app.test_fixtures.campaign_settings.{CallSettingSenderDetailsFixtures, LinkedinSettingSenderDetailsFixtures, SmsSettingSenderDetailsFixtures, WhatsappSettingSenderDetailsFixtures}
import app.test_fixtures.organizationa.{OrgMetadataFixture, OrgPlanFixture}
import app.test_fixtures.prospect.{ProspectAccountFixture, ProspectFixtures}
import eventframework.{ProspectObject, ProspectObjectInternal, SrResourceTypes}
import io.smartreach.esp.api.emails.EmailSettingId
import org.joda.time.DateTime
import org.scalamock.matchers.ArgCapture.CaptureOne
import org.scalamock.scalatest.AsyncMockFactory
import org.scalatest.funspec.AsyncFunSpec
import play.api.libs.Files
import play.api.libs.Files.{SingletonTemporaryFileCreator, TemporaryFile, logger}
import play.api.libs.json.{JsError, JsResult, JsSuccess, JsValue, Json, __}
import play.api.libs.ws.ahc.AhcWSClient
import play.api.mvc.MultipartFormData
import sr_scheduler.CampaignStatus
import sr_scheduler.models.CampaignEmailPriority
import utils.{Helpers, ISRLogger, SRLogger}
import utils.emailvalidation.EmailValidationService
import utils.emailvalidation.models.EmailValidationPriority
import utils.helpers.LogHelpers
import utils.linkedin.LinkedinHelperFunctions
import utils.mq.email.{MQAssociateProspectToOldEmails, MQDomainServiceProviderDNSService}
import utils.mq.webhook.model.TriggerSource
import utils.mq.webhook.{MQTrigger, MQTriggerMsg}
import utils.sr_csv_parser.{CSVParseFileType, SRCsvParserRawResult, SRCsvParserUtils}
import utils.uuid.SrUuidUtils
import utils.uuid.services.SrUuidService
import utils_deploy.rolling_updates.models.SrRollingUpdateFeature
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService

import java.io.File
import java.net.URLEncoder
import java.nio.file.Path
import scala.concurrent.duration.{Duration, SECONDS}
import scala.concurrent.{Await, ExecutionContext, Future}
import scala.util.{Failure, Random, Success, Try}

class ProspectServiceSpec extends AsyncFunSpec with AsyncMockFactory {

  val campaignService: CampaignService = mock[CampaignService]
  val prospectDAOService: ProspectDAOService = mock[ProspectDAOService]
  val accountService: AccountService = mock[AccountService]
  val accountDAO: AccountDAO = mock[AccountDAO]
  //val campaignProspectDAO: CampaignProspectDAO = mock[CampaignProspectDAO]
  val campaignProspectService: CampaignProspectService = mock[CampaignProspectService]
  val mqTrigger: MQTrigger = mock[MQTrigger]
  val tagService: TagService = mock[TagService]
  val prospectServiceV2 = mock[ProspectServiceV2]
  //  val prospectUploadService = mock[ProspectUploadService]
  //  val srCsvParserUtils = mock[SRCsvParserUtils]
  val prospectAccountDAO1 = mock[ProspectAccountDAO1]
  val prospectColumnDef = mock[ProspectColumnDef]
  val prospectsEmailsDAO = mock[ProspectsEmailsDAO]
  val blacklistProspectCheckDAO = mock[BlacklistProspectCheckDAO]
  val generateTempId = mock[GenerateTempId]
  val emailSettingDAO = mock[EmailSettingDAO]
  val prospectUpdateCategoryTemp = mock[ProspectUpdateCategoryTemp]
  val mergeTagService = mock[MergeTagService]
  val mqAssociateProspectToOldEmails: MQAssociateProspectToOldEmails = mock[MQAssociateProspectToOldEmails]
  val campaignProspectTimezonesJedisService = mock[CampaignProspectTimezonesJedisService]
  val rolePermissionDataDAOV2 = mock[RolePermissionDataDAOV2]
  val srUuidUtils: SrUuidUtils = mock[SrUuidUtils]
  val srUuidService: SrUuidService = mock[SrUuidService]
  val potentialDuplicateProspectService: PotentialDuplicateProspectService = mock[PotentialDuplicateProspectService]
  val mqDomainServiceProviderDNSService = mock[MQDomainServiceProviderDNSService]
  val srRollingUpdateCoreService = mock[SrRollingUpdateCoreService]

  val prospectService = new ProspectService(
    prospectDAOService = prospectDAOService,
    accountService = accountService,
    accountDAO = accountDAO,
    campaignService = campaignService,
    campaignProspectService = campaignProspectService,
    mqTrigger = mqTrigger,
    tagService = tagService,
    prospectServiceV2 = prospectServiceV2,
    prospectAccountDAO1 = prospectAccountDAO1,
    prospectColumnDef = prospectColumnDef,
    prospectsEmailsDAO = prospectsEmailsDAO,
    campaignProspectTimzonesJedisService = campaignProspectTimezonesJedisService,
    blacklistProspectCheckDAO = blacklistProspectCheckDAO,
    generateTempId = generateTempId,
    emailSettingDAO = emailSettingDAO,
    prospectUpdateCategoryTemp = prospectUpdateCategoryTemp,
    mergeTagService = mergeTagService,
    mqAssociateProspectToOldEmails = mqAssociateProspectToOldEmails,
    potentialDuplicateProspectService = potentialDuplicateProspectService,
    srUuidUtils = srUuidUtils,
    srUuidService = srUuidService,
    mqDomainServiceProviderDNSService = mqDomainServiceProviderDNSService,
    srRollingUpdateCoreService = srRollingUpdateCoreService
  )

  given Logger: SRLogger = new SRLogger("tests")

  val campaign_id: Long = 121L
  val campaign_name = "CampaignName"
  val permittedAccountIds = Seq(2L)
  val teamId: Long = 37L
  val ownerId: Long = 2L

  val first_name = "first_name"
  val last_name = "last_name"
  val company = "CompanyName"
  val email = "<EMAIL>"
  val email_domain = "smartreach.com"

  val aDate = DateTime.parse("2022-3-27")


  val profile = AccountProfileInfo(
    first_name = first_name,
    last_name = last_name,
    company = Some(company),
    timezone = None,
    country_code = None,
    mobile_country_code = None,
    mobile_number = None,
    onboarding_phone_number = None,
    twofa_enabled = false,
    has_gauthenticator = false,
    weekly_report_emails = None,
    scheduled_for_deletion_at = None
  )

  val accountMetadata = AccountMetadata(
    // account_ui_version = None,
    is_profile_onboarding_done = None
  )

  val orgMetadata = OrgMetadataFixture.orgMetadataFixture2

  val orgCountData: OrgCountData = OrgCountDataFixture.orgCountData_default

  val orgSettings = OrgSettings(
    enable_ab_testing = false,
    disable_force_send = false,
    bulk_sender = false,
    allow_2fa = false,
    show_2fa_setting = false,
    enforce_2fa = false,
    allow_native_crm_integration = false,
    agency_option_allow_changing = false,
    agency_option_show = false
  )

  val orgPlan = OrgPlanFixture.orgPlanFixture


  val org = OrganizationWithCurrentData(

    id = 10,
    name = company,
    owner_account_id = 49,
    /*


    fs_account_id = None,
    stripe_customer_id = None,
    payment_gateway = None,
    plan_type = PlanType.PAID,
    plan_name = "ultimate",
    plan_id = PlanID.ULTIMATE,
    */

    counts = orgCountData,
    settings = orgSettings,
    plan = orgPlan,

    is_agency = true,
    trial_ends_at = DateTime.now().plusDays(100),
    error = None,
    error_code = None,
    paused_till = None,
    errors = Seq(),
    warnings = Seq(),
    via_referral = false,
    org_metadata = orgMetadata
  )

  val rolePermissionInDb = RolePermissionsInDBV2(

    id = 11L,
    role_name = TeamAccountRole.ADMIN,

    manage_billing_v2 = PermissionOwnershipV2.All,
    view_user_management_v2 = PermissionOwnershipV2.All,
    edit_user_management_v2 = PermissionOwnershipV2.All,

    view_team_config_v2 = PermissionOwnershipV2.All,
    edit_team_config_v2 = PermissionOwnershipV2.All,

    view_prospects_v2 = PermissionOwnershipV2.All,

    edit_prospects_v2 = PermissionOwnershipV2.All,

    delete_prospects_v2 = PermissionOwnershipV2.All,


    view_campaigns_v2 = PermissionOwnershipV2.All,

    edit_campaigns_v2 = PermissionOwnershipV2.All,

    delete_campaigns_v2 = PermissionOwnershipV2.All,

    change_campaign_status_v2 = PermissionOwnershipV2.All,


    view_reports_v2 = PermissionOwnershipV2.All,

    edit_reports_v2 = PermissionOwnershipV2.All,

    download_reports_v2 = PermissionOwnershipV2.All,

    send_manual_email_v2 = PermissionOwnershipV2.All,


    view_templates_v2 = PermissionOwnershipV2.All,

    edit_templates_v2 = PermissionOwnershipV2.All,

    delete_templates_v2 = PermissionOwnershipV2.All,


    view_blacklist_v2 = PermissionOwnershipV2.All,

    edit_blacklist_v2 = PermissionOwnershipV2.All,


    view_workflows_v2 = PermissionOwnershipV2.All,

    edit_workflows_v2 = PermissionOwnershipV2.All,

    view_webhooks_v2 = PermissionOwnershipV2.All,

    edit_webhooks_v2 = PermissionOwnershipV2.All,

//    view_prospect_accounts_v2 = PermissionOwnershipV2.All,
//
//    edit_prospect_accounts_v2 = PermissionOwnershipV2.All,

    view_channels_v2 = PermissionOwnershipV2.All,
    edit_channels_v2 = PermissionOwnershipV2.All,
    delete_channels_v2 = PermissionOwnershipV2.All,

    view_tasks_v2 = PermissionOwnershipV2.All,
    edit_tasks_v2 = PermissionOwnershipV2.All,
    delete_tasks_v2 = PermissionOwnershipV2.All,

    edit_pipeline_details_v2 = PermissionOwnershipV2.All,
    delete_pipeline_details_v2 = PermissionOwnershipV2.All,

    view_opportunities_v2 = PermissionOwnershipV2.All,
    edit_opportunities_v2 = PermissionOwnershipV2.All,
    delete_opportunities_v2 = PermissionOwnershipV2.All,

  )

  val teamMember: TeamMember = TeamMember(
    team_id = teamId,
    team_name = "team_name",
    user_id = 2L,
    ta_id = 49L, // dont send ta_id to frontend / api response, only for internal purpose, its dynamically assigned in AuthUtils
    first_name = Some(first_name),
    last_name = Some(last_name),
    email = "<EMAIL>",
    team_role = TeamAccountRole.ADMIN,
    api_key = Some("apiKey"),
    zapier_key = Some("zapier_key")
  )

  val teamMemberLite = TeamMemberLite(

    user_id = 2L,
    first_name = Some("first_name"),
    last_name = Some("last_name"),
    email = "<EMAIL>",
    active = true,
    timezone = Some("campaignTimezone"),
    twofa_enabled = true,
    created_at = aDate,
    user_uuid = AccountUuid("uuid"),
    team_role = TeamAccountRole.ADMIN

  )

  val prospect_CategoriesInDB = ProspectCategoriesInDB(
    id = 22L,
    name = "Completed",
    text_id = "Done",
    label_color = "Blue",
    is_custom = true,
    team_id = teamId,
    rank = ProspectCategoryRank(rank = 2000),
  )

  val adminDefaultPermissions = RolePermissionDataDAOV2.defaultRoles(
    role = TeamAccountRole.ADMIN,
    simpler_perm_flag = false
  )

  val rolePermissionData = RolePermissionDataV2.toRolePermissionApi(
    data = adminDefaultPermissions.copy(id = 10)
  )

  val team_account: TeamAccount = TeamAccount(

    team_id = teamId,
    org_id = 20L,

    role_from_db = Some(adminDefaultPermissions), // MUST come from db (option type only for cacheservice error), should not be sent to frontend, only intermediate

    role = Some(rolePermissionData), // should be sent to frontend

    active = true,
    is_actively_used = true,
    team_name = "team_name",
    total_members = 5,
    access_members = Seq(teamMember),
    all_members = Seq(teamMemberLite),

    prospect_categories_custom = Seq(prospect_CategoriesInDB),
    max_emails_per_prospect_per_day = 100L,
    max_emails_per_prospect_per_week = 500L,
    max_emails_per_prospect_account_per_day = 97,
    max_emails_per_prospect_account_per_week = 497,

    reply_handling = ReplyHandling.PAUSE_SPECIFIC_CAMPAIGN_ON_REPLY,
    created_at = aDate,
    selected_calendar_data = None,
    team_uuid = TeamUuid("uuid")

  )

  val accountAdmin = Account(
    id = AccountUuid("account_uuid"),
    internal_id = 2,
    email = email,
    email_verification_code = None,
    email_verification_code_created_at = None,
    created_at = DateTime.now().minusDays(1000),
    first_name = Some(first_name),
    last_name = Some(last_name),
    company = Some(company),
    timezone = None,
    profile = profile,
    org_role = Some(OrganizationRole.OWNER),
    teams = Seq(team_account),
    account_type = AccountType.AGENCY,
    org = org,
    active = true,
    email_notification_summary = "dSFA",
    account_metadata = accountMetadata,
    email_verified = true,
    signupType = None,
    account_access = AccountAccess(
      inbox_access = false
    ),
    calendar_account_data = None

  )

  val campaignSettings = CampaignSettings(

    // settings
    campaign_email_settings = List(
      CampaignEmailSettings(
        campaign_id = CampaignId(campaign_id),
        sender_email_setting_id = EmailSettingId(2),
        receiver_email_setting_id = EmailSettingId(11),
        team_id = TeamId(teamId),
        uuid = CampaignEmailSettingsUuid("temp_setting_id"),
        id = CampaignEmailSettingsId(123),
        sender_email = "<EMAIL>",
        receiver_email = "<EMAIL>",
        max_emails_per_day_from_email_account = 100,
        signature = Some("emailsignature"),
        error = None,
        from_name = None
      )
    ),
    campaign_linkedin_settings = List(
      LinkedinSettingSenderDetailsFixtures.linkedin_setting_sender_details
    ),
    campaign_call_settings = List(
      CallSettingSenderDetailsFixtures.call_setting_sender_details
    ),
    campaign_whatsapp_settings = List(
      WhatsappSettingSenderDetailsFixtures.whatsapp_setting_sender_details
    ),
    campaign_sms_settings = List(
      SmsSettingSenderDetailsFixtures.sms_setting_sender_details
    ),
    timezone = "thisistimezone",
    daily_from_time = 2, // time since beginning of day in seconds
    daily_till_time = 3, // time since beginning of day in seconds
    sending_holiday_calendar_id = Some(123L),

    // Sunday is the first day
    days_preference = List(true, true, true, true, true, true, false),


    ai_sequence_status = None,

    mark_completed_after_days = 33,
    max_emails_per_day = 100,
    open_tracking_enabled = true,
    click_tracking_enabled = true,
    enable_email_validation = true,
    ab_testing_enabled = true,

    // warm up
    warmup_started_at = Some(aDate.minusDays(3)),
    warmup_length_in_days = Some(2),
    warmup_starting_email_count = Some(5),
    show_soft_start_setting = false,

    // schedule start
    schedule_start_at = Some(aDate.minusDays(1)),
    schedule_start_at_tz = Some("Sometimezone"),
    send_plain_text_email = Some(false),
    campaign_type = CampaignType.MultiChannel,


    email_priority = CampaignEmailPriority.FIRST_EMAIL,
    append_followups = true,
    opt_out_msg = "opt out msg",
    opt_out_is_text = true,
    add_prospect_to_dnc_on_opt_out = true,
    triggers = Seq(),
    sending_mode = None,
    selected_calendar_data = None
  )

  val allCampaignStats = AllCampaignStats(
    total_sent = 1,
    total_opened = 1,
    total_clicked = 1,
    total_replied = 1,
    total_steps = 1,
    current_prospects = 1,
    current_opted_out = 1,
    current_completed = 1,
    current_bounced = 1,
    current_to_check = 1,
    current_failed_email_validation = 1,
    current_in_progress = 1,
    current_unsent_prospects = 1,
    current_do_not_contact = 1,
    reply_sentiment_stats = ReplySentimentStats(
      positive = 0
    )
  )

  val campaign_uuid = s"cmp_${teamId}_cfknacskndjcn"

  val campaign = CampaignWithStatsAndEmail(
    id = 121L,
    uuid = Some(campaign_uuid),
    team_id = teamId,
    shared_with_team = true,
    name = campaign_name,
    owner_name = first_name,
    owner_email = email,
    owner_id = ownerId,
    status = CampaignStatus.RUNNING,
    tags = Seq(CampaignTag(11L, "tag1", CampaignTagUuid("tags_abcefgh"))),
    spam_test_exists = false,
    warmup_is_on = false,

    stats = allCampaignStats,

    head_step_id = Some(22L),

    ai_generation_context = None,

    settings = campaignSettings,

    created_at = aDate,

    error = Some("campaign error"),

    is_archived = false

  )
  val campaignWithBasicDetails = CampaignBasicDetails(
    id = 121L,
    account_id= 2L ,
    team_id= teamId,
    name= campaign_name,
    head_step_id= Some(22L)
  )


  val prospectCreateFormdata = prospectCreateFormData.copy(
    email = Some(email),
    first_name = Some(first_name),
    last_name = Some(last_name),
    owner_id = Some(ownerId),
    company = Some(company)
  )

  val deDuplicateColumnTypeAndValues = Seq(
    DeDuplicateColumnTypeAndValue(
      email,
      SrProspectColumns.Email
    )
  )

  val prospectAccountCreateFormData = ProspectAccountCreateFormData(
    name = "smartreach.com",
    custom_id = None,
    description = None,
    source = None,
    website = None,
    industry = None,
    linkedin_url = None,
    created_at = None,
    custom_fields = Option(Json.obj()),
    update_account = None
  )

  val prospectAccountIdAndCustomId = ProspectAccountIdAndCustomId(
    id = 1L,
    custom_id = "custom_id",
    is_updated_internal = true
  )

  val prospectAccountCreateOrUpdateResult = ProspectAccountCreateOrUpdateResult(
    prospectAccountsCreated = Seq(prospectAccountIdAndCustomId),
    prospectAccountsUpdated = Seq(prospectAccountIdAndCustomId.copy(is_updated_internal = true, id = 2L)),
    duplicate_ids = Seq(3L),
    duplicateProspectAccountCustomIDsWithNoEditPermission = Seq(),
    duplicateProspectAccountIdsWithEditPermission = Seq()
  )

  val validateData = ProspectCreateManuallyFormData(prospect = prospectCreateFormdata, ignoreProspectsInOtherCampaigns = None)

  val prospectCreateFormdata2 = ProspectCreateFormData( //owner_id is None
    email = Some(email),
    first_name = Some(first_name),
    last_name = Some(last_name),
    custom_fields = Json.obj("followers" -> 500),

    // should not break in old ui/integrations, currently used only inside createOrUpdateOne controller
    owner_id = None,

    list = Some("list"),
    company = Some(company),
    city = Some("kolkata"),
    country = Some("India"),
    timezone = Some("Asia/Kolkata"),
    created_at = None,

    state = None,
    job_title = None,
    phone = None,
    phone_2 = None,
    phone_3 = None,
    linkedin_url = None
  )

  val validateData2 = ProspectCreateManuallyFormData(prospect = prospectCreateFormdata2, ignoreProspectsInOtherCampaigns = None)


  val teamMemberBasic = TeamMemberBasic(
    team_id = teamId,
    user_id = teamMember.user_id,
    ta_id = team_account.team_id,
    first_name = first_name,
    last_name = last_name
  )

  val upsertSQLResult = UpsertSQLResult(
    createdProspectEmails = Seq(),
    createdProspectIds = Seq(),
    updatedIds = Seq(),

    duplicateProspectsData = Seq(),
    duplicatesIgnoredEmailsBecauseOfNoEditPermission = Seq("duplicatewithotherteam"),

    // will be used for assigning prospects to campaign when force-update is false but campaign is selected
    // earlier if force-update was false, duplicate prospects were not getting assigned to campaign also
    duplicateProspectIdsWithEditPermission= Seq(),
    totalDNCProspects = 0,
    prospectDataForApi = List()
  )

  val upsertSQLResult2 = UpsertSQLResult(
    createdProspectEmails = Seq(),
    createdProspectIds = Seq(),
    updatedIds = Seq(),

    duplicateProspectsData = Seq(),
    duplicatesIgnoredEmailsBecauseOfNoEditPermission = Seq(),

    duplicateProspectIdsWithEditPermission= Seq(),
    totalDNCProspects = 4,
    prospectDataForApi = List()
  )

  val newlyCreatedProspect = NewlyCreatedProspect(1L, "<EMAIL>")

  val upsertSQLResult3 = UpsertSQLResult(
    createdProspectEmails = Seq(newlyCreatedProspect),
    createdProspectIds = Seq(newlyCreatedProspect.prospect_id),
    updatedIds = Seq(),

    duplicateProspectsData = Seq(),
    duplicatesIgnoredEmailsBecauseOfNoEditPermission = Seq(),

    duplicateProspectIdsWithEditPermission= Seq(),
    totalDNCProspects = 2,
    prospectDataForApi = List()

  )

  val upsertSQLResult4 = UpsertSQLResult(
    createdProspectEmails = Seq(),
    createdProspectIds = Seq(),
    updatedIds = Seq(1L),

    duplicateProspectsData = Seq(),
    duplicatesIgnoredEmailsBecauseOfNoEditPermission = Seq(),

    duplicateProspectIdsWithEditPermission= Seq(),
    totalDNCProspects = 3,
    prospectDataForApi = List()
  )

  val upsertSQLResult5 = UpsertSQLResult(
    createdProspectEmails = Seq(),
    createdProspectIds = Seq(),

    updatedIds = Seq(),

    duplicateProspectsData = Seq(),
    duplicatesIgnoredEmailsBecauseOfNoEditPermission = Seq(),

    duplicateProspectIdsWithEditPermission= Seq(1L),
    totalDNCProspects = 4,
    prospectDataForApi = List()
  )

  val cpAssignResult = CPAssignResult(

    prospectIdsIgnoredBecauseAlreadyAssignedToThisCampaign = List(),

    prospectIdsIgnoredBecauseInOtherCampaigns = List(),

    newlyAssignedProspectIds = List(1L)
  )

  val dummyProspectObj = ProspectObject(
    id = 1,

    owner_id = 0,

    team_id = 0,

    first_name = Some(""),
    last_name = None,

    email = Some(email),

    last_contacted_at = None,
    last_contacted_at_phone = None,

    created_at = DateTime.now,

    custom_fields = Json.obj(),

    list = None,

    company = None,
    job_title = None,
    phone = None,
    phone_2 = None,
    phone_3 = None,

    linkedin_url = None,

    city = None,
    state = None,
    country = None,
    timezone = None,
    latest_reply_sentiment_uuid = None,

    prospect_category = "",

    internal = {

      ProspectFixtures.prospectObjectInternal
    },
    current_step_type = None,
    latest_task_done_at = None,
    prospect_uuid = Some(ProspectUuid("prs_aa_abcdefghi")),
    owner_uuid = AccountUuid("acc_aa_abcdegfhi"),
    updated_at = DateTime.now()
  )


  val insertedProspectResult = InsertOrUpdateProspectResult(
    prospect_id = 11L,
    prospect_uuid = Some("prs_aa_abcdefgh"),
    is_updated_internal = true,
    account_id = 101L
  )

  val updateProspectResult = InsertOrUpdateProspectResult(
    prospect_id = 11L,
    prospect_uuid = Some("prs_aa_abcdefgh"),
    is_updated_internal = true,
    account_id = 101L
  )


  val filename = URLEncoder.encode(s"prospects__${DateTime.now().getMillis}", "UTF-8") + "_export.csv"
  val f = SingletonTemporaryFileCreator.create(new File("/tmp/" + filename).toPath)
  val file = new MultipartFormData.FilePart[TemporaryFile]("key", "filename", Some("contentType"), ref = f)

  val srCsvParserRawResult = SRCsvParserRawResult(
    parserName = "parserName",
    rowMapFromCSV = Seq[Map[String, String]]()
  )

  val uploadCSVResult = UploadProspectCSVResult(
    total_rows = 1,
    total_created = 2,
    total_duplicates_updated = 3,
    total_empty_or_invalid_rows = 4,
    total_duplicates_found = 5,
    total_assigned = 6,
    totaL_duplicates_ignored_for_no_edit_permission = 7,
    total_ignored_internal_emails = Some(0),
    total_dnc_prospects_created = 0,
    total_duplicate_emails_in_csv = Some(0)
  )

  val Error = new Throwable("Error")

  describe("prospectService.createOrUpdateProspects") {

    it("should fail beacause campaignservice.findBasicDetailsWithPermission returned Failure") {
      (accountService.findNewOwnerTeamMember(_: TeamId, _: AccountId))
        .expects(TeamId(id = teamId), AccountId(id = ownerId)) // FIXME VALUECLASS
        .returning(Success(Some(teamMemberBasic)))

//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//        _: TeamId,
//        _: SrRollingUpdateFeature
//      )(_: ISRLogger))
//        .expects(TeamId(37L), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .returning(false)

      (campaignService.findWithPermission(_: Long, _: Seq[Long], _: Long, _: Option[Account])(using _: SRLogger))
        .expects(campaign_id, *, teamId, Some(accountAdmin), *)
        .returning(None)

      val res = prospectService.createOrUpdateProspects(
        ownerAccountId = ownerId,
        teamId = teamId,
        listName = prospectCreateFormdata.list,
        prospects = Seq(prospectCreateFormdata, prospectCreateFormdata.copy(email = Some("<EMAIL>")), prospectCreateFormdata.copy(email = Some("<EMAIL>")), prospectCreateFormdata.copy(email = Some("<EMAIL>"))),
        updateProspectType = UpdateProspectType.ForceUpdate,
        ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads = true,
        doerAccount = accountAdmin,
        prospectSource = None,
        prospectAccountId = Some(120L),
        prospect_tags = Some("tag1,tag2,tag3"),

        campaign_id = Some(campaign_id),
        ignoreProspectInOtherCampaign = IgnoreProspectsInOtherCampaigns.IgnoreProspectsActiveInOtherCampaigns,
        deduplicationColumns = None,
        auditRequestLogId = Some("auditRequestLogId"),
        SRLogger = Logger
      )

      res match {
        case Failure(exception) =>
          Logger.info(s"tag error $exception")
          assert(true)
        case Success(value) =>
          Logger.info(s"createOrUpdateProspectResult $value")
          assert(false)
      }
    }

    it("should Log- error while adding tags to saved/updated prospects when TagService.updateTagsForProspects fails and return CreateOrUpdateProspectsResult") {

      (accountService.findNewOwnerTeamMember(_: TeamId, _: AccountId))
        .expects(TeamId(id = teamId), AccountId(id = ownerId)) // FIXME VALUECLASS
        .returning(Success(Some(teamMemberBasic)))

      (campaignService.findWithPermission(_: Long, _: Seq[Long], _: Long, _: Option[Account])(using _: SRLogger))
        .expects(campaign_id, *, teamId, Some(accountAdmin), *)
        .returning(Some(campaign))

      (emailSettingDAO.getAllEmailSettingsInTeam)
        .expects(teamId)
        .returning(Success(List()))

      (accountService.getEmailsOfTeamMembers)
        .expects(TeamId(id = teamId)) // FIXME VALUECLASS
        .returning(Success(List()))

      (accountService.getEmailOfOrgOwner)
        .expects(OrgId(id = org.id)) // FIXME VALUECLASS
        .returning(Success("<EMAIL>"))

      (prospectColumnDef.findCustomColumns)
        .expects(teamId)
        .returning(Seq())

      (prospectDAOService.getProspectCategoryId(_: TeamId, _: ProspectCategory.Value, _: Option[Account])(using _:SRLogger))
        .expects(TeamId(id = teamId), ProspectCategory.DO_NOT_CONTACT, Some(accountAdmin), *)
        .returning(Success(ProspectCategoryId(1L)))

      (prospectDAOService.getProspectCategoryId(_: TeamId, _: ProspectCategory.Value, _: Option[Account])(using _:SRLogger))
        .expects(TeamId(id = teamId), ProspectCategory.NOT_CATEGORIZED, Some(accountAdmin), *)
        .returning(Success(ProspectCategoryId(1L)))

      (prospectDAOService.findOrCreateList)
        .expects(Some("list"), 2, permittedAccountIds, teamId)
        .returning(Some(1L))

      (prospectAccountDAO1.find)
        .expects(120, teamId)
        .returning(Some(ProspectAccountFixture.prospectAccount))

      (blacklistProspectCheckDAO.findByEmailsAndDomains)
        .expects(*, teamId, OrgId(id = org.id), Seq(email, "<EMAIL>", "<EMAIL>", "<EMAIL>"), *)
        .returning(Success(Seq()))

      (prospectDAOService.findDuplicateProspectsForForceUpdateV2(_: Seq[Seq[DuplicationFindProspectDataV2]], _: TeamId)(using _:SRLogger))
        .expects(*, TeamId(teamId), *)
        .returning(Success(List(DuplicateProspectResult(
          prospectCreateFormData = prospectCreateFormdata,
          deDuplicateColumnTypeAndValues, 1L, Some(ownerId)))))

      (prospectDAOService.updateProspectsForEmailOptional(_: Boolean, _: Seq[ColumnDef], _: Option[Long], _: Iterable[ProspectsToBeForceUpdated], _: Seq[SrProspectColumns], _: List[ProspectEmailsTobeAddedInForceUpdateProspect], _: Long)(using _:SRLogger))
        .expects(true, List(), Some(1L), *, *, *, teamId, *)
        .returning(Success(List(updateProspectResult.copy(1L, Some("prs_aa_qwerty"), true, ownerId))))

      for (a <- 1 until 4) {
        (() => srUuidUtils.generateProspectUuid())
          .expects()
          .returning(temp_prospect_id + s"$a")
      }

      val insertedProspectResult_new = (1 to 3).zipWithIndex.map {
        case (_, index) =>
          insertedProspectResult.copy(index + 2, Some(temp_prospect_id + s"${index + 1}"), false, ownerId)
      }.toList

      (prospectDAOService.insertNewProspects)
        .expects(*, * , *, *, None, *, *, true, *, ownerId, ownerId, teamId, TriggerSource.OTHER, *, *, *)
        .returning(Success(insertedProspectResult_new))

      (prospectsEmailsDAO.__createFromUploadMultiRowInsertV2_New)
        .expects(*, *, *, *)
        .returning(Success(List(
          NewlyCreatedProspect(2L, "<EMAIL>"),
          NewlyCreatedProspect(3L, "<EMAIL>"),
          NewlyCreatedProspect(4L, "<EMAIL>")
        )))

      (mqDomainServiceProviderDNSService.publish)
        .expects("gmail.com")
        .returning(Success({}))

      (campaignProspectTimezonesJedisService.delete(_: CampaignId)(_: SRLogger))
        .expects(*, *)

      (mergeTagService._removeMissingErrorMessage)
        .expects(Seq(1L))
        .returning(1)


      //publishing trigger events
      val createdTriggerMsg1 = MQTriggerMsg(
        event = EventType.CREATED_PROSPECT_IN_SMARTREACH.toString,
        prospectIds = Seq(2L, 3L, 4L),
        accountId = ownerId,
        teamId = teamId,
        updatedProspectCategoryId = None,
        triggerPath = Some(TriggerSource.OTHER),
        oldProspectDeduplicationColumn = None,

      )

      (mqTrigger.publishEvents)
        .expects(createdTriggerMsg1)
        .returning(())

      (mqAssociateProspectToOldEmails.publish)
        .expects(*)
        .returning(Success(()))
      //publishing trigger events
      val createdTriggerMsg2 = MQTriggerMsg(
        event = EventType.UPDATED_PROSPECT_IN_SMARTREACH.toString,
        prospectIds = Seq(1L),
        accountId = ownerId,
        teamId = teamId,
        updatedProspectCategoryId = None,
        triggerPath = Some(TriggerSource.OTHER),
        oldProspectDeduplicationColumn = None,

      )

      (mqTrigger.publishEvents)
        .expects(createdTriggerMsg2)
        .returning(())

      for (a <- 1 until 4) {
        (() => srUuidUtils.generateTagsUuid())
          .expects()
          .returning("tag_abcd" + s"$a")
      }

      val prospectCreatedAndUpdatedIds = Seq(2L, 3L, 4L, 1L)

      (tagService.updateTagsForProspects(_: AddOrRemoveTagAction.Value, _: Seq[Long], _: Seq[TagAndUuid], _: Long, _: Long)(using _: SRLogger))
        .expects(AddOrRemoveTagAction.ADD, prospectCreatedAndUpdatedIds, *, teamId, ownerId, *)
        .returning(Failure(new Throwable("Error while updating tags")))


      (campaignProspectService.assignProspectsToCampaign)
        .expects(
          accountAdmin.org.id,
          permittedAccountIds, accountAdmin.internal_id, Helpers.getAccountName(accountAdmin), ownerId, teamId, campaign.id.toLong, campaign.name, campaign.settings,
          List(2L, 3L, 4L, 1L), IgnoreProspectsInOtherCampaigns.IgnoreProspectsActiveInOtherCampaigns, Logger)
        .returning(Success(cpAssignResult))

//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//        _: TeamId,
//        _: SrRollingUpdateFeature
//      )(_: ISRLogger))
//        .expects(TeamId(37L), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .twice()
//        .returning(false)

      val res = prospectService.createOrUpdateProspects(
        ownerAccountId = ownerId,
        teamId = teamId,
        listName = prospectCreateFormdata.list,
        prospects = Seq(prospectCreateFormdata, prospectCreateFormdata.copy(email = Some("<EMAIL>")), prospectCreateFormdata.copy(email = Some("<EMAIL>")), prospectCreateFormdata.copy(email = Some("<EMAIL>"))),
        updateProspectType = UpdateProspectType.ForceUpdate,
        ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads = true,
        doerAccount = accountAdmin,
        prospectSource = None,
        prospectAccountId = Some(120L),
        prospect_tags = Some("tag1,tag2,tag3"),

        campaign_id = Some(campaign_id),
        ignoreProspectInOtherCampaign = IgnoreProspectsInOtherCampaigns.IgnoreProspectsActiveInOtherCampaigns,
        deduplicationColumns = None,
        auditRequestLogId = Some("auditRequestLogId"),
        SRLogger = Logger
      )

      res match {
        case Failure(exception) =>
          println(s"tag error ${exception.printStackTrace()}")
          assert(false)
        case Success(value) =>
          Logger.info(s"createOrUpdateProspectResult $value")
          assert(true)
      }
    }

    it("should throw error for the invalid tags") {

      val res = prospectService.createOrUpdateProspects(
        ownerAccountId = ownerId,
        teamId = teamId,
        listName = prospectCreateFormdata.list,
        prospects = Seq(prospectCreateFormdata),
        updateProspectType = UpdateProspectType.None,
        ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads = true,
        doerAccount = accountAdmin,
        prospectSource = None,
        prospectAccountId = Some(120L),
        prospect_tags = Some("tag1##,tag2%%,tag3&&"),

        campaign_id = Some(campaign_id),
        ignoreProspectInOtherCampaign = IgnoreProspectsInOtherCampaigns.IgnoreProspectsActiveInOtherCampaigns,
        deduplicationColumns = None,
        auditRequestLogId = Some("auditRequestLogId"),
        SRLogger = Logger
      )

      res match {
        case Failure(exception) =>
          Logger.info(s"Tag error $exception")
          //tag error api.BadRequestErrorException: Invalid tag: tag1## :: only alphanumeric names with space/hyphens allowed
          assert(true)
        case Success(value) =>
          Logger.info(s"createOrUpdateProspectResult $value")
          assert(false)
      }
    }

    it("should successfully add tags to saved/updated prospects when TagService.updateTagsForProspects and return CreateOrUpdateProspectsResult") {

      (accountService.findNewOwnerTeamMember(_: TeamId, _: AccountId))
        .expects(TeamId(id = teamId), AccountId(id = ownerId)) // FIXME VALUECLASS
        .returning(Success(Some(teamMemberBasic)))

      (campaignService.findWithPermission(_: Long, _: Seq[Long], _: Long, _: Option[Account])(using _: SRLogger))
        .expects(campaign_id, *, teamId, Some(accountAdmin), *)
        .returning(Some(campaign))

      (emailSettingDAO.getAllEmailSettingsInTeam)
        .expects(teamId)
        .returning(Success(List()))

      (accountService.getEmailsOfTeamMembers)
        .expects(TeamId(id = teamId)) // FIXME VALUECLASS
        .returning(Success(List()))

      (accountService.getEmailOfOrgOwner)
        .expects(OrgId(id = org.id)) // FIXME VALUECLASS
        .returning(Success("<EMAIL>"))

      (prospectColumnDef.findCustomColumns)
        .expects(teamId)
        .returning(Seq())

      (prospectDAOService.getProspectCategoryId(_: TeamId, _: ProspectCategory.Value, _: Option[Account])(using _:SRLogger))
        .expects(TeamId(id = teamId), ProspectCategory.DO_NOT_CONTACT, Some(accountAdmin), *)
        .returning(Success(ProspectCategoryId(1L)))

      (prospectDAOService.getProspectCategoryId(_: TeamId, _: ProspectCategory.Value, _: Option[Account])(using _:SRLogger))
        .expects(TeamId(id = teamId), ProspectCategory.NOT_CATEGORIZED, Some(accountAdmin), *)
        .returning(Success(ProspectCategoryId(1L)))

      (prospectDAOService.findOrCreateList)
        .expects(Some("list"), 2, permittedAccountIds, teamId)
        .returning(Some(1L))

      (prospectAccountDAO1.find)
        .expects(120, teamId)
        .returning(Some(ProspectAccountFixture.prospectAccount))

      (blacklistProspectCheckDAO.findByEmailsAndDomains)
        .expects(*, teamId, OrgId(id = org.id), Seq(email, "<EMAIL>", "<EMAIL>", "<EMAIL>"), *)
        .returning(Success(Seq()))

      (prospectDAOService.findDuplicateProspectsForForceUpdateV2(_: Seq[Seq[DuplicationFindProspectDataV2]], _: TeamId)(using _:SRLogger))
        .expects(*, TeamId(teamId), *)
        .returning(Success(List(DuplicateProspectResult(
          prospectCreateFormData = prospectCreateFormdata,
          deDuplicateColumnTypeAndValues, 1L, Some(ownerId)))))

      (prospectDAOService.updateProspectsForEmailOptional(_: Boolean, _: Seq[ColumnDef], _: Option[Long], _: Iterable[ProspectsToBeForceUpdated], _: Seq[SrProspectColumns], _: List[ProspectEmailsTobeAddedInForceUpdateProspect], _: Long)(using _:SRLogger))
        .expects(true, List(), Some(1L), *, *, *, teamId, *)
        .returning(Success(List(updateProspectResult.copy(1L, Some("prs_aa_qwerty"), true, ownerId))))

      for (a <- 1 until 4) {
        (() => srUuidUtils.generateProspectUuid())
          .expects()
          .returning(temp_prospect_id + s"$a")
      }

      val insertedProspectResult_new = (1 to 3).zipWithIndex.map {
        case (_, index) =>
          insertedProspectResult.copy(index + 2, Some(temp_prospect_id + s"${index + 1}"), false, ownerId)
      }.toList

      (prospectDAOService.insertNewProspects)
        .expects(*, List(
          BlacklistStatusForProspectUuid(ProspectUuid("prs_aa_qwerty1"),false),
          BlacklistStatusForProspectUuid(ProspectUuid("prs_aa_qwerty2"),false),
          BlacklistStatusForProspectUuid(ProspectUuid("prs_aa_qwerty3"),false)),
          *, *, None, *, *, true, *, ownerId, ownerId, teamId, TriggerSource.OTHER, *, *, *)
        .returning(Success(insertedProspectResult_new))

      (prospectsEmailsDAO.__createFromUploadMultiRowInsertV2_New)
        .expects(*, *, *, *)
        .returning(Success(List(
          NewlyCreatedProspect(2L, "<EMAIL>"),
          NewlyCreatedProspect(3L, "<EMAIL>"),
          NewlyCreatedProspect(4L, "<EMAIL>")
        )))
      (mqDomainServiceProviderDNSService.publish)
        .expects("gmail.com")
        .returning(Success({}))

      (campaignProspectTimezonesJedisService.delete(_: CampaignId)(_: SRLogger))
        .expects(*, *)

      (mergeTagService._removeMissingErrorMessage)
        .expects(Seq(1L))
        .returning(1)

      //publishing trigger events
      val createdTriggerMsg1 = MQTriggerMsg(
        event = EventType.CREATED_PROSPECT_IN_SMARTREACH.toString,
        prospectIds = Seq(2L, 3L, 4L),
        accountId = ownerId,
        teamId = teamId,
        updatedProspectCategoryId = None,
        triggerPath = Some(TriggerSource.OTHER),
        oldProspectDeduplicationColumn = None
      )

      (mqTrigger.publishEvents)
        .expects(createdTriggerMsg1)
        .returning(())

      (mqAssociateProspectToOldEmails.publish)
        .expects(*)
        .returning(Success(()))

      //publishing trigger events
      val createdTriggerMsg2 = MQTriggerMsg(
        event = EventType.UPDATED_PROSPECT_IN_SMARTREACH.toString,
        prospectIds = Seq(1L),
        accountId = ownerId,
        teamId = teamId,
        updatedProspectCategoryId = None,
        triggerPath = Some(TriggerSource.OTHER),
        oldProspectDeduplicationColumn = None
      )

      (mqTrigger.publishEvents)
        .expects(createdTriggerMsg2)
        .returning(())

      val prospectCreatedAndUpdatedIds = Seq(2L, 3L, 4L, 1L)

      for (a <- 1 until 4) {
        (() => srUuidUtils.generateTagsUuid())
          .expects()
          .returning("tag_abcd" + s"$a")
      }

      (tagService.updateTagsForProspects(_: AddOrRemoveTagAction.Value, _: Seq[Long], _: Seq[TagAndUuid], _: Long, _: Long)(using _: SRLogger))
        .expects(AddOrRemoveTagAction.ADD, prospectCreatedAndUpdatedIds, *, teamId, ownerId, *)
        .returning(Success(1))


      (campaignProspectService.assignProspectsToCampaign)
        .expects(
          accountAdmin.org.id,
          permittedAccountIds, accountAdmin.internal_id, Helpers.getAccountName(accountAdmin), ownerId, teamId, campaign.id.toLong, campaign.name, campaign.settings,
          *, IgnoreProspectsInOtherCampaigns.IgnoreProspectsActiveInOtherCampaigns, Logger)
        .returning(Success(cpAssignResult))


//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//        _: TeamId,
//        _: SrRollingUpdateFeature
//      )(_: ISRLogger))
//        .expects(TeamId(37L), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .twice()
//        .returning(false)


      val res = prospectService.createOrUpdateProspects(
        ownerAccountId = ownerId,
        teamId = teamId,
        listName = prospectCreateFormdata.list,
        prospects = Seq(prospectCreateFormdata, prospectCreateFormdata.copy(email = Some("<EMAIL>")), prospectCreateFormdata.copy(email = Some("<EMAIL>")), prospectCreateFormdata.copy(email = Some("<EMAIL>"))),
        updateProspectType = UpdateProspectType.ForceUpdate,
        ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads = true,
        doerAccount = accountAdmin,
        prospectSource = None,
        prospectAccountId = Some(120L),
        prospect_tags = Some("tag1,tag2,tag3"),

        campaign_id = Some(campaign_id),
        ignoreProspectInOtherCampaign = IgnoreProspectsInOtherCampaigns.IgnoreProspectsActiveInOtherCampaigns,
        deduplicationColumns = None,
        auditRequestLogId = Some("auditRequestLogId"),
        SRLogger = Logger
      )

      res match {
        case Failure(exception) =>
          Logger.info(s"tag error $exception")
          assert(false)
        case Success(value) =>
          Logger.info(s"createOrUpdateProspectResult $value")
          //createOrUpdateProspectResult CreateOrUpdateProspectsResult(List(),List(4),List(4),List(1, 2),List(3),List(1),List())
          assert(true)
      }
    }


    it("should successfully add tags to prospects even if no any prospect created/updated") {

      (accountService.findNewOwnerTeamMember(_: TeamId, _: AccountId))
        .expects(TeamId(id = teamId), AccountId(id = ownerId)) // FIXME VALUECLASS
        .returning(Success(Some(teamMemberBasic)))

      (campaignService.findWithPermission(_: Long, _: Seq[Long], _: Long, _: Option[Account])(using _: SRLogger))
        .expects(campaign_id, *, teamId, Some(accountAdmin), *)
        .returning(Some(campaign))

      (emailSettingDAO.getAllEmailSettingsInTeam)
        .expects(teamId)
        .returning(Success(List()))

      (accountService.getEmailsOfTeamMembers)
        .expects(TeamId(id = teamId)) // FIXME VALUECLASS
        .returning(Success(List()))

      (accountService.getEmailOfOrgOwner)
        .expects(OrgId(id = org.id)) // FIXME VALUECLASS
        .returning(Success("<EMAIL>"))

      (prospectColumnDef.findCustomColumns)
        .expects(teamId)
        .returning(Seq())

      (prospectDAOService.getProspectCategoryId(_: TeamId, _: ProspectCategory.Value, _: Option[Account])(using _:SRLogger))
        .expects(TeamId(id = teamId), ProspectCategory.DO_NOT_CONTACT, Some(accountAdmin), *)
        .returning(Success(ProspectCategoryId(1L)))

      (prospectDAOService.getProspectCategoryId(_: TeamId, _: ProspectCategory.Value, _: Option[Account])(using _:SRLogger))
        .expects(TeamId(id = teamId), ProspectCategory.NOT_CATEGORIZED, Some(accountAdmin), *)
        .returning(Success(ProspectCategoryId(1L)))

      (prospectDAOService.findOrCreateList)
        .expects(Some("list"), 2, permittedAccountIds, teamId)
        .returning(Some(1L))

      (prospectAccountDAO1.find)
        .expects(120, teamId)
        .returning(Some(ProspectAccountFixture.prospectAccount))

      (blacklistProspectCheckDAO.findByEmailsAndDomains)
        .expects(*, teamId, OrgId(id = org.id), Seq(email), *)
        .returning(Success(Seq()))

      (prospectDAOService.findDuplicateProspectsForForceUpdateV2(_: Seq[Seq[DuplicationFindProspectDataV2]], _: TeamId)(using _:SRLogger))
        .expects(*, TeamId(teamId), *)
        .returning(Success(List(DuplicateProspectResult(
          prospectCreateFormData = prospectCreateFormdata,
          deDuplicateColumnTypeAndValues, 1L, Some(ownerId)))))

      (prospectDAOService.updateProspectsForEmailOptional(_: Boolean, _: Seq[ColumnDef], _: Option[Long], _: Iterable[ProspectsToBeForceUpdated], _: Seq[SrProspectColumns], _: List[ProspectEmailsTobeAddedInForceUpdateProspect], _: Long)(using _: SRLogger))
        .expects(true, List(), Some(1L), *, *, *, teamId, *)
        .returning(Success(List()))


      (prospectDAOService.insertNewProspects)
        .expects(*, List(), *, *, None, *, *, true, *, ownerId, ownerId, teamId, TriggerSource.OTHER, *, *, *)
        .returning(Success(List()))

      (mergeTagService._removeMissingErrorMessage)
        .expects(Seq())
        .returning(0)


      val prospectIds = Seq(1L)

      for (a <- 1 until 4) {
        (() => srUuidUtils.generateTagsUuid())
          .expects()
          .returning("tag_abcd" + s"$a")
      }

      (tagService.updateTagsForProspects(_: AddOrRemoveTagAction.Value, _: Seq[Long], _: Seq[TagAndUuid], _: Long, _: Long)(using _: SRLogger))
        .expects(AddOrRemoveTagAction.ADD, prospectIds, *, teamId, ownerId, *)
        .returning(Success(1))


      (campaignProspectService.assignProspectsToCampaign)
        .expects(
          accountAdmin.org.id,
          permittedAccountIds, accountAdmin.internal_id, Helpers.getAccountName(accountAdmin), ownerId, teamId, campaign.id.toLong, campaign.name, campaign.settings,
          *, IgnoreProspectsInOtherCampaigns.IgnoreProspectsActiveInOtherCampaigns, Logger)
        .returning(Success(cpAssignResult))


//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//        _: TeamId,
//        _: SrRollingUpdateFeature
//      )(_: ISRLogger))
//        .expects(TeamId(37L), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .twice()
//        .returning(false)


      val res = prospectService.createOrUpdateProspects(
        ownerAccountId = ownerId,
        teamId = teamId,
        listName = prospectCreateFormdata.list,
        prospects = Seq(prospectCreateFormdata),
        updateProspectType = UpdateProspectType.ForceUpdate,
        ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads = true,
        doerAccount = accountAdmin,
        prospectSource = None,
        prospectAccountId = Some(120L),
        prospect_tags = Some("tag1,tag2,tag3"),

        campaign_id = Some(campaign_id),
        ignoreProspectInOtherCampaign = IgnoreProspectsInOtherCampaigns.IgnoreProspectsActiveInOtherCampaigns,
        deduplicationColumns = None,
        auditRequestLogId = Some("auditRequestLogId"),
        SRLogger = Logger
      )

      res match {
        case Failure(exception) =>
          assert(false)
        case Success(value) =>
          Logger.info(s"createOrUpdateProspectResult $value")
          //createOrUpdateProspectResult CreateOrUpdateProspectsResult(List(),List(4),List(4),List(1, 2),List(3),List(1),List())
          assert(true)
      }
    }

    it("should successfully add tags to saved/updated/duplicate(not updated) prospects when TagService.updateTagsForProspects and return CreateOrUpdateProspectsResult") {

      (accountService.findNewOwnerTeamMember(_: TeamId, _: AccountId))
        .expects(TeamId(id = teamId), AccountId(id = ownerId)) // FIXME VALUECLASS
        .returning(Success(Some(teamMemberBasic)))

      (campaignService.findWithPermission(_: Long, _: Seq[Long], _: Long, _: Option[Account])(using _: SRLogger))
        .expects(campaign_id, *, teamId, Some(accountAdmin), *)
        .returning(Some(campaign))

      (emailSettingDAO.getAllEmailSettingsInTeam)
        .expects(teamId)
        .returning(Success(List()))

      (accountService.getEmailsOfTeamMembers)
        .expects(TeamId(id = teamId)) // FIXME VALUECLASS
        .returning(Success(List()))

      (accountService.getEmailOfOrgOwner)
        .expects(OrgId(id = org.id)) // FIXME VALUECLASS
        .returning(Success("<EMAIL>"))

      (prospectColumnDef.findCustomColumns)
        .expects(teamId)
        .returning(Seq())

      (prospectDAOService.getProspectCategoryId(_: TeamId, _: ProspectCategory.Value, _: Option[Account])(using _:SRLogger))
        .expects(TeamId(id = teamId), ProspectCategory.DO_NOT_CONTACT, Some(accountAdmin), *)
        .returning(Success(ProspectCategoryId(1L)))

      (prospectDAOService.getProspectCategoryId(_: TeamId, _: ProspectCategory.Value, _: Option[Account])(using _:SRLogger))
        .expects(TeamId(id = teamId), ProspectCategory.NOT_CATEGORIZED, Some(accountAdmin), *)
        .returning(Success(ProspectCategoryId(1L)))

      (prospectDAOService.findOrCreateList)
        .expects(Some("list"), 2, permittedAccountIds, teamId)
        .returning(Some(1L))

      (prospectAccountDAO1.find)
        .expects(120, teamId)
        .returning(Some(ProspectAccountFixture.prospectAccount))

      (blacklistProspectCheckDAO.findByEmailsAndDomains)
        .expects(*, teamId, OrgId(id = org.id), Seq(email, "<EMAIL>", "<EMAIL>", "<EMAIL>"), *)
        .returning(Success(Seq()))

      (prospectDAOService.findDuplicateProspectsForForceUpdateV2(_: Seq[Seq[DuplicationFindProspectDataV2]], _: TeamId)(using _:SRLogger))
        .expects(*, TeamId(teamId), *)
        .returning(Success(List(DuplicateProspectResult(
          prospectCreateFormData = prospectCreateFormdata.copy(city = Some("NewYork")),
          deDuplicateColumnTypeAndValues, 1L, Some(ownerId)),
          DuplicateProspectResult(
            prospectCreateFormData = prospectCreateFormdata.copy(email = Some("<EMAIL>")),
            deDuplicateColumnTypeAndValues.map(d => d.copy(deDuplicationColumnValue = "<EMAIL>")), 2L, Some(ownerId))
        )))

      (prospectDAOService.updateProspectsForEmailOptional(_: Boolean, _: Seq[ColumnDef], _: Option[Long], _: Iterable[ProspectsToBeForceUpdated], _: Seq[SrProspectColumns], _: List[ProspectEmailsTobeAddedInForceUpdateProspect], _: Long)(using _: SRLogger))
        .expects(true, List(), *, *, *, *, teamId, *)
        .returning(Success(List(updateProspectResult.copy(1L, Some("prs_aa_qwerty"), true, ownerId))))

      for (a <- 2 until 4) {
        (() => srUuidUtils.generateProspectUuid())
          .expects()
          .returning(temp_prospect_id + s"$a")
      }

      val insertedProspectResult_new = (2 to 4).zipWithIndex.map {
        case (_, index) =>
          insertedProspectResult.copy(index + 3, Some(temp_prospect_id + s"${index + 2}"), false, ownerId)
      }.toList

      (prospectDAOService.insertNewProspects)
        .expects(*, List(BlacklistStatusForProspectUuid(ProspectUuid("prs_aa_qwerty2"),false),
          BlacklistStatusForProspectUuid(ProspectUuid("prs_aa_qwerty3"),false)),
          *, *, None, *, *, true, *, ownerId, ownerId, teamId, TriggerSource.OTHER, *, *, *)
        .returning(Success(insertedProspectResult_new))

      (prospectsEmailsDAO.__createFromUploadMultiRowInsertV2_New)
        .expects(*, *, *, *)
        .returning(Success(List(
          NewlyCreatedProspect(3L, "<EMAIL>"),
          NewlyCreatedProspect(4L, "<EMAIL>")
        )))
      (mqDomainServiceProviderDNSService.publish)
        .expects("gmail.com")
        .returning(Success({}))

      (campaignProspectTimezonesJedisService.delete(_: CampaignId)(_: SRLogger))
        .expects(*, *)

      (mergeTagService._removeMissingErrorMessage)
        .expects(Seq(1L))
        .returning(1)

      //publishing trigger events
      val createdTriggerMsg1 = MQTriggerMsg(
        event = EventType.CREATED_PROSPECT_IN_SMARTREACH.toString,
        prospectIds = Seq(3L, 4L, 5L),
        accountId = ownerId,
        teamId = teamId,
        updatedProspectCategoryId = None,
        triggerPath = Some(TriggerSource.OTHER),
        oldProspectDeduplicationColumn = None
      )

      (mqTrigger.publishEvents)
        .expects(createdTriggerMsg1)
        .returning(())

      (mqAssociateProspectToOldEmails.publish)
        .expects(*)
        .returning(Success(()))

      //publishing trigger events
      val createdTriggerMsg2 = MQTriggerMsg(
        event = EventType.UPDATED_PROSPECT_IN_SMARTREACH.toString,
        prospectIds = Seq(1L),
        accountId = ownerId,
        teamId = teamId,
        updatedProspectCategoryId = None,
        triggerPath = Some(TriggerSource.OTHER),
        oldProspectDeduplicationColumn = None
      )

      (mqTrigger.publishEvents)
        .expects(createdTriggerMsg2)
        .returning(())

      val prospectIdsToAddTags = Seq(3L, 4L, 5L, 1L, 2L)

      for (a <- 1 until 4) {
        (() => srUuidUtils.generateTagsUuid())
          .expects()
          .returning("tag_abcd" + s"$a")
      }

      (tagService.updateTagsForProspects(_: AddOrRemoveTagAction.Value, _: Seq[Long], _: Seq[TagAndUuid], _: Long, _: Long)(using _: SRLogger))
        .expects(AddOrRemoveTagAction.ADD, prospectIdsToAddTags, *, teamId, ownerId, *)
        .returning(Success(1))


      (campaignProspectService.assignProspectsToCampaign)
        .expects(
          accountAdmin.org.id,
          permittedAccountIds, accountAdmin.internal_id, Helpers.getAccountName(accountAdmin), ownerId, teamId, campaign.id.toLong, campaign.name, campaign.settings,
          *, IgnoreProspectsInOtherCampaigns.IgnoreProspectsActiveInOtherCampaigns, Logger)
        .returning(Success(cpAssignResult))


//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//        _: TeamId,
//        _: SrRollingUpdateFeature
//      )(_: ISRLogger))
//        .expects(TeamId(37L), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .twice()
//        .returning(false)


      val res = prospectService.createOrUpdateProspects(
        ownerAccountId = ownerId,
        teamId = teamId,
        listName = prospectCreateFormdata.list,
        prospects = Seq(prospectCreateFormdata, prospectCreateFormdata.copy(email = Some("<EMAIL>")), prospectCreateFormdata.copy(email = Some("<EMAIL>")), prospectCreateFormdata.copy(email = Some("<EMAIL>"))),
        updateProspectType = UpdateProspectType.ForceUpdate,
        ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads = true,
        doerAccount = accountAdmin,
        prospectSource = None,
        prospectAccountId = Some(120L),
        prospect_tags = Some("tag1,tag2,tag3"),

        campaign_id = Some(campaign_id),
        ignoreProspectInOtherCampaign = IgnoreProspectsInOtherCampaigns.IgnoreProspectsActiveInOtherCampaigns,
        deduplicationColumns = None,
        auditRequestLogId = Some("auditRequestLogId"),
        SRLogger = Logger
      )

      res match {
        case Failure(exception) =>
          println(s"tag error ${LogHelpers.getStackTraceAsString(exception)}")
          assert(false)
        case Success(value) =>
          Logger.info(s"createOrUpdateProspectResult $value")
          //createOrUpdateProspectResult CreateOrUpdateProspectsResult(List(),List(4),List(4),List(1, 2),List(3),List(1),List())
          assert(true)
      }
    }


    it("should reject if ownerId not in PermittedAccountIds") {
      val res = prospectService.createOrUpdateProspects(
        ownerAccountId = 11L,
        teamId = teamId,
        listName = prospectCreateFormdata.list,
        prospects = Seq(prospectCreateFormdata),
        updateProspectType = UpdateProspectType.None,
        ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads = true,
        doerAccount = accountAdmin,
        prospectSource = None,
        prospectAccountId = Some(120L),
        prospect_tags = Some("tag1,tag2,tag3"),

        campaign_id = Some(campaign_id),
        ignoreProspectInOtherCampaign = IgnoreProspectsInOtherCampaigns.IgnoreProspectsActiveInOtherCampaigns,
        deduplicationColumns = None,
        auditRequestLogId = Some("auditRequestLogId"),
        SRLogger = Logger
      )

      res match {
        case Failure(exception) =>
          Logger.info(s"Error... ${exception.getMessage}")
          //Error... You do not have permission to edit prospects for the selected owner
          assert(true)
        case Success(value) =>
          Logger.info(s"createOrUpdateProspectResult $value")
          assert(false)
      }
    }

    it("should reject if findNewOwnerTeamMember throws error") {

      (accountService.findNewOwnerTeamMember(_: TeamId, _: AccountId))
        .expects(TeamId(id = teamId), AccountId(id = ownerId)) // FIXME VALUECLASS
        .returning(Failure(new Throwable("findNewOwnerTeamMember exception")))

//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//        _: TeamId,
//        _: SrRollingUpdateFeature
//      )(_: ISRLogger))
//        .expects(TeamId(37L), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .once()
//        .returning(false)

      val res = prospectService.createOrUpdateProspects(
        ownerAccountId = ownerId,
        teamId = teamId,
        listName = prospectCreateFormdata.list,
        prospects = Seq(prospectCreateFormdata),
        updateProspectType = UpdateProspectType.None,
        ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads = true,
        doerAccount = accountAdmin,
        prospectSource = None,
        prospectAccountId = Some(120L),
        prospect_tags = Some("tag1,tag2,tag3"),

        campaign_id = Some(campaign_id),
        ignoreProspectInOtherCampaign = IgnoreProspectsInOtherCampaigns.IgnoreProspectsActiveInOtherCampaigns,
        deduplicationColumns = None,
        auditRequestLogId = Some("auditRequestLogId"),
        SRLogger = Logger
      )

      res match {
        case Failure(exception) =>
          Logger.info(s"Error... ${exception.getMessage}")
          //Error... Invalid owner selected. Please try again or contact support [2]
          assert(true)
        case Success(value) =>
          Logger.info(s"createOrUpdateProspectResult $value")
          assert(false)
      }

    }

    it("should Log failure while createEventLog") {

      (accountService.findNewOwnerTeamMember(_: TeamId, _: AccountId))
        .expects(TeamId(id = teamId), AccountId(id = ownerId)) // FIXME VALUECLASS
        .returning(Success(Some(teamMemberBasic)))

      (campaignService.findWithPermission(_: Long, _: Seq[Long], _: Long, _: Option[Account])(using _: SRLogger))
        .expects(campaign_id, *, teamId, Some(accountAdmin), *)
        .returning(Some(campaign))

      (emailSettingDAO.getAllEmailSettingsInTeam)
        .expects(teamId)
        .returning(Success(List()))

      (accountService.getEmailsOfTeamMembers)
        .expects(TeamId(id = teamId)) // FIXME VALUECLASS
        .returning(Success(List()))

      (accountService.getEmailOfOrgOwner)
        .expects(OrgId(id = org.id)) // FIXME VALUECLASS
        .returning(Success("<EMAIL>"))

      (prospectColumnDef.findCustomColumns)
        .expects(teamId)
        .returning(Seq())

      (prospectDAOService.getProspectCategoryId(_: TeamId, _: ProspectCategory.Value, _: Option[Account])(using _:SRLogger))
        .expects(TeamId(id = teamId), ProspectCategory.DO_NOT_CONTACT, Some(accountAdmin), *)
        .returning(Success(ProspectCategoryId(1L)))

      (prospectDAOService.getProspectCategoryId(_: TeamId, _: ProspectCategory.Value, _: Option[Account])(using _:SRLogger))
        .expects(TeamId(id = teamId), ProspectCategory.NOT_CATEGORIZED, Some(accountAdmin), *)
        .returning(Success(ProspectCategoryId(1L)))

      (prospectDAOService.findOrCreateList)
        .expects(Some("list"), 2, permittedAccountIds, teamId)
        .returning(Some(1L))

      (prospectAccountDAO1.find)
        .expects(120, teamId)
        .returning(Some(ProspectAccountFixture.prospectAccount))

      (blacklistProspectCheckDAO.findByEmailsAndDomains)
        .expects(*, teamId, OrgId(id = org.id), Seq(email, "<EMAIL>", "<EMAIL>", "<EMAIL>"), *)
        .returning(Success(Seq()))

      (prospectDAOService.findDuplicateProspectsForForceUpdateV2(_: Seq[Seq[DuplicationFindProspectDataV2]], _: TeamId)(using _:SRLogger))
        .expects(*, TeamId(teamId), *)
        .returning(Success(List(DuplicateProspectResult(
          prospectCreateFormData = prospectCreateFormdata,
          deDuplicateColumnTypeAndValues, 1L, Some(ownerId)))))

      (prospectDAOService.updateProspectsForEmailOptional(_: Boolean, _: Seq[ColumnDef], _: Option[Long], _: Iterable[ProspectsToBeForceUpdated], _: Seq[SrProspectColumns], _: List[ProspectEmailsTobeAddedInForceUpdateProspect], _: Long)(using _: SRLogger))
        .expects(true, List(), *, *, *, *, teamId, *)
        .returning(Success(List(updateProspectResult.copy(1L, Some("prs_aa_qwerty"), true, ownerId))))

      for (a <- 1 until 4) {
        (() => srUuidUtils.generateProspectUuid())
          .expects()
          .returning(temp_prospect_id + s"$a")
      }

      val insertedProspectResult_new = (1 to 3).zipWithIndex.map {
        case (_, index) =>
          insertedProspectResult.copy(index + 2, Some(temp_prospect_id + s"${index + 1}"), false, ownerId)
      }.toList

      (prospectDAOService.insertNewProspects)
        .expects(*,
          List(BlacklistStatusForProspectUuid(ProspectUuid("prs_aa_qwerty1"),false),
            BlacklistStatusForProspectUuid(ProspectUuid("prs_aa_qwerty2"),false),
          BlacklistStatusForProspectUuid(ProspectUuid("prs_aa_qwerty3"),false)),
          *, *, None, *, *, true, *, ownerId, ownerId, teamId, TriggerSource.OTHER, *, *, *)
        .returning(Success(insertedProspectResult_new))

      (prospectsEmailsDAO.__createFromUploadMultiRowInsertV2_New)
        .expects(*, *, *, *)
        .returning(Success(List(
          NewlyCreatedProspect(2L, "<EMAIL>"),
          NewlyCreatedProspect(3L, "<EMAIL>"),
          NewlyCreatedProspect(4L, "<EMAIL>")
        )))
      (mqDomainServiceProviderDNSService.publish)
        .expects("gmail.com")
        .returning(Success({}))

      (campaignProspectTimezonesJedisService.delete(_: CampaignId)(_: SRLogger))
        .expects(*, *)

      (mergeTagService._removeMissingErrorMessage)
        .expects(Seq(1L))
        .returning(1)


      //publishing trigger events
      val createdTriggerMsg1 = MQTriggerMsg(
        event = EventType.CREATED_PROSPECT_IN_SMARTREACH.toString,
        prospectIds = Seq(2L, 3L, 4L),
        accountId = ownerId,
        teamId = teamId,
        updatedProspectCategoryId = None,
        triggerPath = Some(TriggerSource.OTHER),
        oldProspectDeduplicationColumn = None
      )

      (mqTrigger.publishEvents)
        .expects(createdTriggerMsg1)
        .returning(())

      //publishing trigger events
      val createdTriggerMsg2 = MQTriggerMsg(
        event = EventType.UPDATED_PROSPECT_IN_SMARTREACH.toString,
        prospectIds = Seq(1L),
        accountId = ownerId,
        teamId = teamId,
        updatedProspectCategoryId = None,
        triggerPath = Some(TriggerSource.OTHER),
        oldProspectDeduplicationColumn = None
      )

      (mqTrigger.publishEvents)
        .expects(createdTriggerMsg2)
        .returning(())

      (mqAssociateProspectToOldEmails.publish)
        .expects(*)
        .returning(Success(()))

      for (a <- 1 until 4) {
        (() => srUuidUtils.generateTagsUuid())
          .expects()
          .returning("tag_abcd" + s"$a")
      }

      val prospectCreatedAndUpdatedIds = Seq(2L, 3L, 4L, 1L)

      (tagService.updateTagsForProspects(_: AddOrRemoveTagAction.Value, _: Seq[Long], _: Seq[TagAndUuid], _: Long, _: Long)(using _: SRLogger))
        .expects(AddOrRemoveTagAction.ADD, prospectCreatedAndUpdatedIds, *, teamId, ownerId, *)
        .returning(Success(1))


      (campaignProspectService.assignProspectsToCampaign)
        .expects(
          accountAdmin.org.id,
          permittedAccountIds, accountAdmin.internal_id, Helpers.getAccountName(accountAdmin), ownerId, teamId, campaign.id.toLong, campaign.name, campaign.settings,
          List(2L, 3L, 4L, 1L), IgnoreProspectsInOtherCampaigns.IgnoreProspectsActiveInOtherCampaigns, Logger)
        .returning(Success(cpAssignResult))

//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//        _: TeamId,
//        _: SrRollingUpdateFeature
//      )(_: ISRLogger))
//        .expects(TeamId(37L), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .twice()
//        .returning(false)


      val res = prospectService.createOrUpdateProspects(
        ownerAccountId = ownerId,
        teamId = teamId,
        listName = prospectCreateFormdata.list,
        prospects = Seq(prospectCreateFormdata, prospectCreateFormdata.copy(email = Some("<EMAIL>")), prospectCreateFormdata.copy(email = Some("<EMAIL>")), prospectCreateFormdata.copy(email = Some("<EMAIL>"))),
        updateProspectType = UpdateProspectType.ForceUpdate,
        ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads = true,
        doerAccount = accountAdmin,
        prospectSource = None,
        prospectAccountId = Some(120L),
        prospect_tags = Some("tag1,tag2,tag3"),

        campaign_id = Some(campaign_id),
        ignoreProspectInOtherCampaign = IgnoreProspectsInOtherCampaigns.IgnoreProspectsActiveInOtherCampaigns,
        deduplicationColumns = None,
        auditRequestLogId = Some("auditRequestLogId"),
        SRLogger = Logger
      )

      res match {
        case Failure(exception) =>
          println(s"tag error ${LogHelpers.getStackTraceAsString(exception)}")
          assert(false)
        case Success(value) =>
          Logger.info(s"createOrUpdateProspectResult $value")
          //createOrUpdateProspectResult CreateOrUpdateProspectsResult(List(),List(1),List(1),List(2, 3, 4),List(1),List(1),List())
          assert(true)
      }
    }


  }




  describe("prospectService.assignProspectsToCampaign") {

    it("should fail when campaign_id is empty") {

      val res = prospectService.assignProspectsToCampaign(
        accountId = teamMember.user_id,
        teamId = teamMember.team_id,
        campaignId = None,
        prospectIds = List(1L, 2L),
        account = accountAdmin,
        permittedAccountIdsForEditingCampaigns = permittedAccountIds,
        permittedAccountIdsForEditingProspects = permittedAccountIds,
        ignore_prospects_in_other_campaigns = (IgnoreProspectsInOtherCampaigns.IgnoreProspectsActiveInOtherCampaigns),
        Logger = Logger
      )

      res match {
        case Left(e: AssignProspectsToCampaignError) =>
          e match {
            case AssignProspectsToCampaignError.CampaignNotFoundError(error) =>
              Logger.info(s"AssignProspectsToCampaignError!!... $error")
              //AssignProspectsToCampaignError!!... Please provide campaign_id
              assert(true)
            case _ =>
              Logger.info(s"Error!!!... $e")
              assert(false)
          }

        case Right(v: AssignProspectsToCampaignResponse) => assert(false)
      }
    }

    //    it("should fail when campaignService.findBasicDetailsWithPermission returns error") {
    //      (campaignService.find(_: Long, _: Long, _: Option[Account])(_: SRLogger))
    //        .expects(campaign_id, teamId, Some(accountAdmin), *)
    //        .returning(None)
    //
    //      val res = prospectService.assignProspectsToCampaign(
    //        accountId = teamMember.user_id,
    //        teamId = teamMember.team_id,
    //        campaignId = Some(campaign_id),
    //        prospectIds = List(1L, 2L),
    //        account = accountAdmin,
    //        permittedAccountIdsForEditingCampaigns = permittedAccountIds,
    //        permittedAccountIdsForEditingProspects = permittedAccountIds,
    //        ignore_prospects_in_other_campaigns = (IgnoreProspectsInOtherCampaigns.IgnoreProspectsActiveInOtherCampaigns),
    //        Logger = Logger
    //      )
    //
    //      res match {
    //        case Left(e: AssignProspectsToCampaignError) =>
    //          e match {
    //            case AssignProspectsToCampaignError.CampaignFetchError(error) =>
    //              Logger.info(s"AssignProspectsToCampaignError!!... $error")
    //              //AssignProspectsToCampaignError!!... Prospect is already assigned to the given campaign
    //              assert(true)
    //            case _ =>
    //              Logger.info(s"Error!!!... $e")
    //              assert(false)
    //          }
    //
    //        case Right(v: AssignProspectsToCampaignResponse) => assert(false)
    //      }
    //    }

    it("should fail when prospect is already assigned") {

      (campaignService.findWithPermission(_: Long, _: Seq[Long], _: Long, _: Option[Account])(using _: SRLogger))
        .expects(campaign_id, permittedAccountIds, teamId, Some(accountAdmin), *)
        .returning(Some(campaign))

      (campaignProspectService.assignProspectsToCampaign)
        .expects(
          accountAdmin.org.id,
          permittedAccountIds, accountAdmin.internal_id, Helpers.getAccountName(accountAdmin), teamMember.user_id, teamId, campaign.id.toLong, campaign.name, campaign.settings,
          List(1L, 2L), IgnoreProspectsInOtherCampaigns.IgnoreProspectsActiveInOtherCampaigns, Logger)
        .returning(Failure(new Throwable("Error while campaignProspectDAO.assign campaigns_prospects_pkey")))

      val res = prospectService.assignProspectsToCampaign(
        accountId = teamMember.user_id,
        teamId = teamMember.team_id,
        campaignId = Some(campaign_id),
        prospectIds = List(1L, 2L),
        account = accountAdmin,
        permittedAccountIdsForEditingCampaigns = permittedAccountIds,
        permittedAccountIdsForEditingProspects = permittedAccountIds,
        ignore_prospects_in_other_campaigns = (IgnoreProspectsInOtherCampaigns.IgnoreProspectsActiveInOtherCampaigns),
        Logger = Logger
      )

      res match {
        case Left(e: AssignProspectsToCampaignError) =>
          e match {
            case AssignProspectsToCampaignError.ProspectIsAlreadyAssigned(error) =>
              Logger.info(s"AssignProspectsToCampaignError!!... $error")
              //AssignProspectsToCampaignError!!... Prospect is already assigned to the given campaign
              assert(true)
            case _ =>
              Logger.info(s"Error!!!... $e")
              assert(false)
          }

        case Right(v: AssignProspectsToCampaignResponse) => assert(false)
      }
    }

    it("should fail when prospects are not selected") {

      val res = prospectService.assignProspectsToCampaign(
        accountId = teamMember.user_id,
        teamId = teamMember.team_id,
        campaignId = Some(campaign_id),
        prospectIds = List(),
        account = accountAdmin,
        permittedAccountIdsForEditingCampaigns = permittedAccountIds,
        permittedAccountIdsForEditingProspects = permittedAccountIds,
        ignore_prospects_in_other_campaigns = (IgnoreProspectsInOtherCampaigns.IgnoreProspectsActiveInOtherCampaigns),
        Logger = Logger
      )

      res match {
        case Left(e: AssignProspectsToCampaignError) =>
          e match {
            case AssignProspectsToCampaignError.ProspectNotFoundError(error) =>
              Logger.info(s"AssignProspectsToCampaignError!!... $error")
              //AssignProspectsToCampaignError!!... Please select a few prospects to assign
              assert(true)
            case _ =>
              Logger.info(s"Error!!!... $e")
              assert(false)
          }

        case Right(v: AssignProspectsToCampaignResponse) => assert(false)
      }
    }

    it("should fail when campaignDAO return None") {

      (campaignService.findWithPermission(_: Long, _: Seq[Long], _: Long, _: Option[Account])(using _: SRLogger))
        .expects(campaign_id, permittedAccountIds, teamId, Some(accountAdmin), *)
        .returning(None)

      val res = prospectService.assignProspectsToCampaign(
        accountId = teamMember.user_id,
        teamId = teamMember.team_id,
        campaignId = Some(campaign_id),
        prospectIds = List(1L),
        account = accountAdmin,
        permittedAccountIdsForEditingCampaigns = permittedAccountIds,
        permittedAccountIdsForEditingProspects = permittedAccountIds,
        ignore_prospects_in_other_campaigns = (IgnoreProspectsInOtherCampaigns.IgnoreProspectsActiveInOtherCampaigns),
        Logger = Logger
      )

      res match {
        case Left(e: AssignProspectsToCampaignError) =>
          e match {
            case AssignProspectsToCampaignError.CampaignNotFoundError(error) =>
              Logger.info(s"AssignProspectsToCampaignError!!... $error")
              //AssignProspectsToCampaignError!!... Campaign with given id not found
              assert(true)
            case _ =>
              Logger.info(s"Error!!!... $e")
              assert(false)
          }

        case Right(v: AssignProspectsToCampaignResponse) => assert(false)
      }
    }

    it("should fail when prospect not found") {

      (campaignService.findWithPermission(_: Long, _: Seq[Long], _: Long, _: Option[Account])(using _: SRLogger))
        .expects(campaign_id, permittedAccountIds, teamId, Some(accountAdmin), *)
        .returning(Some(campaign))

      (campaignProspectService.assignProspectsToCampaign)
        .expects(
          accountAdmin.org.id,
          permittedAccountIds, accountAdmin.internal_id, Helpers.getAccountName(accountAdmin), teamMember.user_id, teamId, campaign.id.toLong, campaign.name, campaign.settings,
          List(111L), IgnoreProspectsInOtherCampaigns.IgnoreProspectsActiveInOtherCampaigns, Logger)
        .returning(Failure(new Throwable("Error while campaignProspectDAO.assign campaigns_prospects_prospect_id_fkey")))

      val res = prospectService.assignProspectsToCampaign(
        accountId = teamMember.user_id,
        teamId = teamMember.team_id,
        campaignId = Some(campaign_id),
        prospectIds = List(111L),
        account = accountAdmin,
        permittedAccountIdsForEditingCampaigns = permittedAccountIds,
        permittedAccountIdsForEditingProspects = permittedAccountIds,
        ignore_prospects_in_other_campaigns = (IgnoreProspectsInOtherCampaigns.IgnoreProspectsActiveInOtherCampaigns),
        Logger = Logger
      )

      res match {
        case Left(e: AssignProspectsToCampaignError) =>
          e match {
            case AssignProspectsToCampaignError.ProspectNotFoundError(error) =>
              Logger.info(s"AssignProspectsToCampaignError!!... $error")
              //AssignProspectsToCampaignError!!... Prospect with given id not found
              assert(true)
            case _ =>
              Logger.info(s"Error!!!... $e")
              assert(false)
          }

        case Right(v: AssignProspectsToCampaignResponse) => assert(false)
      }
    }

    it("should fail when campaign not found") {

      (campaignService.findWithPermission(_: Long, _: Seq[Long], _: Long, _: Option[Account])(using _: SRLogger))
        .expects(campaign_id, permittedAccountIds, teamId, Some(accountAdmin), *)
        .returning(Some(campaign))

      (campaignProspectService.assignProspectsToCampaign)
        .expects(
          accountAdmin.org.id,
          permittedAccountIds, accountAdmin.internal_id, Helpers.getAccountName(accountAdmin), teamMember.user_id, teamId, campaign_id, campaign.name, campaign.settings,
          List(1L, 2L), IgnoreProspectsInOtherCampaigns.IgnoreProspectsActiveInOtherCampaigns, Logger)
        .returning(Failure(new Throwable("Error while campaignProspectDAO.assign campaigns_prospects_campaign_id_fkey")))

      val res = prospectService.assignProspectsToCampaign(
        accountId = teamMember.user_id,
        teamId = teamMember.team_id,
        campaignId = Some(campaign.id),
        prospectIds = List(1L, 2L),
        account = accountAdmin,
        permittedAccountIdsForEditingCampaigns = permittedAccountIds,
        permittedAccountIdsForEditingProspects = permittedAccountIds,
        ignore_prospects_in_other_campaigns = (IgnoreProspectsInOtherCampaigns.IgnoreProspectsActiveInOtherCampaigns),
        Logger = Logger
      )

      res match {
        case Left(e: AssignProspectsToCampaignError) =>
          e match {
            case AssignProspectsToCampaignError.CampaignNotFoundError(error) =>
              Logger.info(s"AssignProspectsToCampaignError!!... $error")
              //AssignProspectsToCampaignError!!... Campaign with given id not found
              assert(true)
            case _ =>
              Logger.info(s"Error!!!... $e")
              assert(false)
          }

        case Right(v: AssignProspectsToCampaignResponse) => assert(false)
      }
    }

    it("should fail when campaignProspectDAO.assign throws error") {

      (campaignService.findWithPermission(_: Long, _: Seq[Long], _: Long, _: Option[Account])(using _: SRLogger))
        .expects(campaign_id, permittedAccountIds, teamId, Some(accountAdmin), *)
        .returning(Some(campaign))

      (campaignProspectService.assignProspectsToCampaign)
        .expects(
          accountAdmin.org.id,
          permittedAccountIds, accountAdmin.internal_id, Helpers.getAccountName(accountAdmin), teamMember.user_id, teamId, campaign.id.toLong, campaign.name, campaign.settings,
          List(1L, 2L), IgnoreProspectsInOtherCampaigns.IgnoreProspectsActiveInOtherCampaigns, Logger)
        .returning(Failure(new Throwable("Error while campaignProspectDAO.assign")))

      val res = prospectService.assignProspectsToCampaign(
        accountId = teamMember.user_id,
        teamId = teamMember.team_id,
        campaignId = Some(campaign_id),
        prospectIds = List(1L, 2L),
        account = accountAdmin,
        permittedAccountIdsForEditingCampaigns = permittedAccountIds,
        permittedAccountIdsForEditingProspects = permittedAccountIds,
        ignore_prospects_in_other_campaigns = (IgnoreProspectsInOtherCampaigns.IgnoreProspectsActiveInOtherCampaigns),
        Logger = Logger
      )

      res match {
        case Left(e: AssignProspectsToCampaignError) =>
          e match {
            case AssignProspectsToCampaignError.ErrorWhileAssigningProspect(error) =>
              Logger.info(s"AssignProspectsToCampaignError!!... $error")
              assert(true)
            case _ =>
              Logger.info(s"Error!!!... $e")
              assert(false)
          }

        case Right(v: AssignProspectsToCampaignResponse) => assert(false)
      }
    }

    it("should success with warning when assignedProspectIds is empty") {

      (campaignService.findWithPermission(_: Long, _: Seq[Long], _: Long, _: Option[Account])(using _: SRLogger))
        .expects(campaign_id, permittedAccountIds, teamId, Some(accountAdmin), *)
        .returning(Some(campaign))

      (campaignProspectService.assignProspectsToCampaign)
        .expects(
          accountAdmin.org.id,
          permittedAccountIds, accountAdmin.internal_id, Helpers.getAccountName(accountAdmin), teamMember.user_id, teamId, campaign.id.toLong, campaign.name, campaign.settings,
          List(123L), IgnoreProspectsInOtherCampaigns.IgnoreProspectsActiveInOtherCampaigns, Logger)
        .returning(Success(CPAssignResult(List(), List(), List())))

      val res = prospectService.assignProspectsToCampaign(
        accountId = teamMember.user_id,
        teamId = teamMember.team_id,
        campaignId = Some(campaign_id),
        prospectIds = List(123L),
        account = accountAdmin,
        permittedAccountIdsForEditingCampaigns = permittedAccountIds,
        permittedAccountIdsForEditingProspects = permittedAccountIds,
        ignore_prospects_in_other_campaigns = (IgnoreProspectsInOtherCampaigns.IgnoreProspectsActiveInOtherCampaigns),
        Logger = Logger
      )

      res match {
        case Left(e: AssignProspectsToCampaignError) =>
          assert(false)

        case Right(v: AssignProspectsToCampaignResponse) =>
          Logger.info(s"${v.responseMsg}, assigned ${v.assignedProspectIdsLength} prospects to campaign ${v.campaignId}")
          //No prospect assigned. Selected prospects are either already assigned to the campaign, or are currently active in other campaigns,
          //assigned 0 prospects to campaign 121
          assert(true)
      }
    }

    it("should success with warning when assignedProspectIds is empty, force_assign true and ignoreProspectsActiveInOtherCampaigns is false") {

      (campaignService.findWithPermission(_: Long, _: Seq[Long], _: Long, _: Option[Account])(using _: SRLogger))
        .expects(campaign_id, permittedAccountIds, teamId, Some(accountAdmin), *)
        .returning(Some(campaign))

      (campaignProspectService.assignProspectsToCampaign)
        .expects(
          accountAdmin.org.id,
          permittedAccountIds, accountAdmin.internal_id, Helpers.getAccountName(accountAdmin), teamMember.user_id, teamId, campaign.id.toLong, campaign.name, campaign.settings,
          List(123L), IgnoreProspectsInOtherCampaigns.DoNotIgnore, Logger)
        .returning(Success(CPAssignResult(List(), List(), List())))

      val res = prospectService.assignProspectsToCampaign(
        accountId = teamMember.user_id,
        teamId = teamMember.team_id,
        campaignId = Some(campaign_id),
        prospectIds = List(123L),
        account = accountAdmin,
        permittedAccountIdsForEditingCampaigns = permittedAccountIds,
        permittedAccountIdsForEditingProspects = permittedAccountIds,
        ignore_prospects_in_other_campaigns = (IgnoreProspectsInOtherCampaigns.DoNotIgnore),
        Logger = Logger
      )

      res match {
        case Left(e: AssignProspectsToCampaignError) =>
          assert(false)

        case Right(v: AssignProspectsToCampaignResponse) =>
          Logger.info(s"${v.responseMsg}, assigned ${v.assignedProspectIdsLength} prospects to campaign ${v.campaignId}")
          //No prospect assigned. Selected prospects are already assigned to the campaign, assigned 0 prospects to campaign 121
          assert(true)
      }
    }

    it("should success with warning when assignedProspectIds is empty, force_assign empty and ignoreProspectsActiveInOtherCampaigns is false") {

      (campaignService.findWithPermission(_: Long, _: Seq[Long], _: Long, _: Option[Account])(using _: SRLogger))
        .expects(campaign_id, permittedAccountIds, teamId, Some(accountAdmin), *)
        .returning(Some(campaign))

      (campaignProspectService.assignProspectsToCampaign)
        .expects(
          accountAdmin.org.id,
          permittedAccountIds, accountAdmin.internal_id, Helpers.getAccountName(accountAdmin), teamMember.user_id, teamId, campaign.id.toLong, campaign.name, campaign.settings,
          List(123L), IgnoreProspectsInOtherCampaigns.DoNotIgnore, Logger)
        .returning(Success(CPAssignResult(List(), List(), List())))

      val res = prospectService.assignProspectsToCampaign(
        accountId = teamMember.user_id,
        teamId = teamMember.team_id,
        campaignId = Some(campaign_id),
        prospectIds = List(123L),
        account = accountAdmin,
        permittedAccountIdsForEditingCampaigns = permittedAccountIds,
        permittedAccountIdsForEditingProspects = permittedAccountIds,
        ignore_prospects_in_other_campaigns = (IgnoreProspectsInOtherCampaigns.DoNotIgnore),
        Logger = Logger
      )

      res match {
        case Left(e: AssignProspectsToCampaignError) =>
          assert(false)

        case Right(v: AssignProspectsToCampaignResponse) =>
          Logger.info(s"${v.responseMsg}, assigned ${v.assignedProspectIdsLength} prospects to campaign ${v.campaignId}")
          //No prospect assigned. Selected prospects are already assigned to the campaign, assigned 0 prospects to campaign 121
          assert(true)
      }
    }

    it("should success when prospectIds and assignedProspectIds are nonEmpty") {

      (campaignService.findWithPermission(_: Long, _: Seq[Long], _: Long, _: Option[Account])(using _: SRLogger))
        .expects(campaign_id, permittedAccountIds, teamId, Some(accountAdmin), *)
        .returning(Some(campaign))

      (campaignProspectService.assignProspectsToCampaign)
        .expects(
          accountAdmin.org.id,
          permittedAccountIds, accountAdmin.internal_id, Helpers.getAccountName(accountAdmin), teamMember.user_id, teamId, campaign.id.toLong, campaign.name, campaign.settings,
          List(123L), IgnoreProspectsInOtherCampaigns.DoNotIgnore, Logger)
        .returning(Success(CPAssignResult(List(), List(), List(123L))))
      (campaignProspectTimezonesJedisService.delete(
        _: CampaignId
      )(
        _: SRLogger
      ))
        .expects(*, *)

      val res = prospectService.assignProspectsToCampaign(
        accountId = teamMember.user_id,
        teamId = teamMember.team_id,
        campaignId = Some(campaign_id),
        prospectIds = List(123L),
        account = accountAdmin,
        permittedAccountIdsForEditingCampaigns = permittedAccountIds,
        permittedAccountIdsForEditingProspects = permittedAccountIds,
        ignore_prospects_in_other_campaigns = (IgnoreProspectsInOtherCampaigns.DoNotIgnore),
        Logger = Logger
      )

      res match {
        case Left(e: AssignProspectsToCampaignError) =>
          assert(false)

        case Right(v: AssignProspectsToCampaignResponse) =>
          Logger.info(s"${v.responseMsg}, assigned ${v.assignedProspectIdsLength} prospects to campaign ${v.campaignId}")
          //1 prospects assigned to campaign 'CampaignName', assigned 1 prospects to campaign 121
          assert(v.assignedProspectIds.contains(123L))
      }
    }

  }



  //  sbt "coldemail/testOnly *IEmailAddressSpec -- -z "should parse emails with random utf characters in the email""

  val temp_prospect_id = s"prs_aa_qwerty"
  val upsertSQLProspectData = UpsertSQLProspectData(
    prospect_email = "<EMAIL>",
    prospect_data = InsertOrUpdateProspectResult(
      prospect_id = 1L,
      prospect_uuid = Some("prs_aa_qwerty"),
      is_updated_internal = true,
      account_id = 2L
    )
  )
  val updated_prospects_api = List(upsertSQLProspectData)


  val prospect_object_for_api = ProspectObjectForApi(
    id = Some("prs_aa_qwerty"),
    emails = List(ProspectEmail(
      email = "<EMAIL>",
      is_valid = true,
      is_primary = true
    )),
    account_id = "acc_abcdefghi",
    first_name = None,
    last_name = None,
    phone_numbers = List(),
    linkedin_url = None,
    city = None,
    company = None,
    state = None,
    country = None,
    timezone = None,
    list = None,
    tags = None,
    custom_fields = Json.obj(),
    updated_at = aDate,
    created_at = aDate
  )

  val duplicateProspectResult = DuplicateProspectResult(
    prospectCreateFormData = prospectCreateFormdata,
    deDuplicateColumnAndValue = deDuplicateColumnTypeAndValues.map(p => p.copy(deDuplicationColumnValue = "<EMAIL>")),
    prospect_id = 1L,
    account_id = Some(2L)
  )


  describe("prospectService.__createFromUploadMultiRowInsertNew") {

    it("should try to update the prospects if forceUpdateProspects is true - but the duplicate finder has removed the exact dupe so the update list is empty") {
      (prospectColumnDef.findCustomColumns)
        .expects(teamId)
        .returning(Seq())

      (prospectDAOService.getProspectCategoryId(_: TeamId, _: ProspectCategory.Value, _: Option[Account])(using _:SRLogger))
        .expects(TeamId(id = teamId), ProspectCategory.DO_NOT_CONTACT, Some(accountAdmin), *)
        .returning(Success(ProspectCategoryId(1L)))

      (prospectDAOService.getProspectCategoryId(_: TeamId, _: ProspectCategory.Value, _: Option[Account])(using _:SRLogger))
        .expects(TeamId(id = teamId), ProspectCategory.NOT_CATEGORIZED, Some(accountAdmin), *)
        .returning(Success(ProspectCategoryId(1L)))

      (prospectDAOService.findOrCreateList)
        .expects(None, 2, permittedAccountIds, teamId)
        .returning(None)

      (prospectAccountDAO1.find)
        .expects(120, teamId)
        .returning(Some(ProspectAccountFixture.prospectAccount))

      (blacklistProspectCheckDAO.findByEmailsAndDomains)
        .expects(*, teamId, OrgId(id = org.id), Seq(email), Seq(email_domain))
        .returning(Success(Seq()))

      (prospectDAOService.findDuplicateProspectsForForceUpdateV2(_: Seq[Seq[DuplicationFindProspectDataV2]], _: TeamId)(using _:SRLogger))
        .expects(*, TeamId(teamId), *)
        .returning(Success(List(DuplicateProspectResult(
          prospectCreateFormData = prospectCreateFormdata,
          deDuplicateColumnTypeAndValues, 1L, Some(ownerId)))))

      (prospectDAOService.updateProspectsForEmailOptional(_: Boolean, _: Seq[ColumnDef], _: Option[Long], _: Iterable[ProspectsToBeForceUpdated], _: Seq[SrProspectColumns], _: List[ProspectEmailsTobeAddedInForceUpdateProspect], _: Long)(using _: SRLogger))
        .expects(true, List(), *, *, *, *, teamId, *)
        .returning(Success(List(updateProspectResult.copy(1L, Some("prs_aa_qwerty"), true, ownerId))))

      (prospectDAOService.insertNewProspects)
        .expects(*, List(), 1L, 1L, None, Some(120L), *, true, List(), ownerId, ownerId, teamId, TriggerSource.OTHER, *, *, *)
        .returning(Success(List()))

      (mergeTagService._removeMissingErrorMessage)
        .expects(Seq(1L))
        .returning(1)

//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//        _: TeamId,
//        _: SrRollingUpdateFeature
//      )(_: ISRLogger))
//        .expects(TeamId(37L), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .returning(false)

      prospectService.__createFromUploadMultiRowInsertNew(
        ownerAccountId = ownerId,
        teamId = teamId,
        taId = teamMember.ta_id,
        listName = None,
        prospects = Seq(prospectCreateFormdata),
        deduplicationColumns = Seq(SrProspectColumns.Email),
        updateProspect = UpdateProspectType.ForceUpdate,
        ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads = true,
        doerAccount = accountAdmin,
        prospectSource = None,
        prospectAccountId = Some(120L),
        triggerSource = TriggerSource.OTHER,
        permittedAccountIdsForEditingProspects = permittedAccountIds,
        permittedAccountIdsForEditingProspectAccounts = permittedAccountIds,
        is_public_api = false,
        Logger = Logger
      ) match {
        case Failure(exception) =>
          println(s"Error!... ${exception.printStackTrace()}")
          assert(false)
        case Success(result) =>
          assert(result == UpsertSQLResult(Seq(), Seq(), Seq(1L), Seq(duplicateProspectResult), Seq(), Seq(1L), 0, List(UpsertSQLProspectData("<EMAIL>", InsertOrUpdateProspectResult(1, Some("prs_aa_qwerty"), true, 2)))))
      }
    }


    // failing test
    it("shubham") {
      (prospectColumnDef.findCustomColumns)
        .expects(teamId)
        .returning(Seq())

      (prospectDAOService.getProspectCategoryId(_: TeamId, _: ProspectCategory.Value, _: Option[Account])(using _:SRLogger))
        .expects(TeamId(id = teamId), ProspectCategory.DO_NOT_CONTACT, Some(accountAdmin), *)
        .returning(Success(ProspectCategoryId(1L)))

      (prospectDAOService.getProspectCategoryId(_: TeamId, _: ProspectCategory.Value, _: Option[Account])(using _:SRLogger))
        .expects(TeamId(id = teamId), ProspectCategory.NOT_CATEGORIZED, Some(accountAdmin), *)
        .returning(Success(ProspectCategoryId(1L)))

      (prospectDAOService.findOrCreateList)
        .expects(None, 2, permittedAccountIds, teamId)
        .returning(None)

      (prospectAccountDAO1.find)
        .expects(120, teamId)
        .returning(Some(ProspectAccountFixture.prospectAccount))

      (blacklistProspectCheckDAO.findByEmailsAndDomains)
        .expects(*, teamId, OrgId(id = org.id), Seq(email), Seq(email_domain))
        .returning(Success(Seq()))

      (prospectDAOService.findDuplicateProspectsForForceUpdateV2(_: Seq[Seq[DuplicationFindProspectDataV2]], _: TeamId)(using _:SRLogger))
        .expects(*, TeamId(teamId), *)
        .returning(Success(List()))

      (() => srUuidUtils.generateProspectUuid())
        .expects()
        .returning(temp_prospect_id)

      (prospectDAOService.insertNewProspects)
        .expects(*, List(BlacklistStatusForProspectUuid(ProspectUuid("prs_aa_qwerty"),false)),
          1L, 1L, None, Some(120L), *, true, List(), ownerId, ownerId, teamId, TriggerSource.OTHER, *, *, *)
        .returning(Success(List(insertedProspectResult.copy(1L, Some(temp_prospect_id), false, ownerId))))

      (prospectsEmailsDAO.__createFromUploadMultiRowInsertV2_New)
        .expects(*, *, *, teamId)
        .returning(Success(List(newlyCreatedProspect)))
      (mqDomainServiceProviderDNSService.publish)
        .expects("email.com")
        .returning(Success({}))

      (mergeTagService._removeMissingErrorMessage)
        .expects(Seq())
        .returning(1)

//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//        _: TeamId,
//        _: SrRollingUpdateFeature
//      )(_: ISRLogger))
//        .expects(TeamId(37L), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .returning(false)


      prospectService.__createFromUploadMultiRowInsertNew(
        ownerAccountId = ownerId,
        teamId = teamId,
        taId = teamMember.ta_id,
        listName = None,
        prospects = Seq(prospectCreateFormdata),
        deduplicationColumns = Seq(SrProspectColumns.Email),
        updateProspect = UpdateProspectType.None,
        ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads = true,
        doerAccount = accountAdmin,
        prospectSource = None,
        prospectAccountId = Some(120L),
        triggerSource = TriggerSource.OTHER,
        permittedAccountIdsForEditingProspects = permittedAccountIds,
        permittedAccountIdsForEditingProspectAccounts = permittedAccountIds,
        is_public_api = false,
        Logger = Logger
      ) match {
        case Failure(exception) =>
          println(s"Error! ${LogHelpers.getStackTraceAsString(exception)}")
          assert(false)
        case Success(result) => assert(result == UpsertSQLResult(Seq(newlyCreatedProspect), Seq(newlyCreatedProspect.prospect_id), Seq(), Seq(), Seq(), Seq(), 0, updated_prospects_api.map(p => p.copy(prospect_data = p.prospect_data.copy(is_updated_internal = false)))))
      }
    }

    // failing test
    it("should return empty UpsertSQLResult if prospectCreateFormdata is not provided") {
      prospectService.__createFromUploadMultiRowInsertNew(
        ownerAccountId = ownerId,
        teamId = teamId,
        taId = teamMember.ta_id,
        listName = None,
        prospects = Seq(),
        deduplicationColumns = Seq(SrProspectColumns.Email),
        updateProspect = UpdateProspectType.ForceUpdate,
        ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads = true,
        doerAccount = accountAdmin,
        prospectSource = None,
        prospectAccountId = None,
        triggerSource = TriggerSource.CRM,
        permittedAccountIdsForEditingProspects = permittedAccountIds,
        permittedAccountIdsForEditingProspectAccounts = permittedAccountIds,
        is_public_api = false,
        Logger = Logger
      ) match {
        case Failure(exception) =>
          assert(false)

        case Success(result) =>

          assert(result == UpsertSQLResult(List(),List(),List(),List(),List(),List(), 0, List()))
      }
    }

    it("should create account_id for prospect if prospectAccountId is None and return UpsertSQLResult") {
      (prospectColumnDef.findCustomColumns)
        .expects(teamId)
        .returning(Seq())

      (prospectDAOService.getProspectCategoryId(_: TeamId, _: ProspectCategory.Value, _: Option[Account])(using _:SRLogger))
        .expects(TeamId(id = teamId), ProspectCategory.DO_NOT_CONTACT, Some(accountAdmin), *)
        .returning(Success(ProspectCategoryId(1L)))

      (prospectDAOService.getProspectCategoryId(_: TeamId, _: ProspectCategory.Value, _: Option[Account])(using _:SRLogger))
        .expects(TeamId(id = teamId), ProspectCategory.NOT_CATEGORIZED, Some(accountAdmin), *)
        .returning(Success(ProspectCategoryId(1L)))

      (prospectDAOService.findOrCreateList)
        .expects(None, 2, permittedAccountIds, teamId)
        .returning(None)

      (prospectAccountDAO1.findOrCreateProspectAccount)
        .expects(Seq(prospectAccountCreateFormData), ownerId, permittedAccountIds, teamId, *)
        .returning(prospectAccountCreateOrUpdateResult)

      (blacklistProspectCheckDAO.findByEmailsAndDomains)
        .expects(*, teamId, OrgId(id = org.id), Seq(email), Seq(email_domain))
        .returning(Success(Seq()))

      (prospectDAOService.findDuplicateProspectsForForceUpdateV2(_: Seq[Seq[DuplicationFindProspectDataV2]], _: TeamId)(using _:SRLogger))
        .expects(*, TeamId(teamId), *)
        .returning(Success(List()))

      (() => srUuidUtils.generateProspectUuid())
        .expects()
        .returning(temp_prospect_id)

      (prospectDAOService.insertNewProspects)
        .expects(*, List(BlacklistStatusForProspectUuid(ProspectUuid("prs_aa_qwerty"),false)), 1L, 1L, None, None, *, true, List(), ownerId, ownerId, teamId, TriggerSource.OTHER, *, *, *)
        .returning(Success(List(insertedProspectResult.copy(1L, Some(temp_prospect_id), false, ownerId))))

      (prospectsEmailsDAO.__createFromUploadMultiRowInsertV2_New)
        .expects(*, *, *, teamId)
        .returning(Success(List(newlyCreatedProspect)))
      (mqDomainServiceProviderDNSService.publish)
        .expects("email.com")
        .returning(Success({}))
      (mergeTagService._removeMissingErrorMessage)
        .expects(Seq())
        .returning(1)

//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//        _: TeamId,
//        _: SrRollingUpdateFeature
//      )(_: ISRLogger))
//        .expects(TeamId(37L), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .returning(false)


      prospectService.__createFromUploadMultiRowInsertNew(
        ownerAccountId = ownerId,
        teamId = teamId,
        taId = teamMember.ta_id,
        listName = None,
        prospects = Seq(prospectCreateFormdata.copy(linkedin_url = Some("linkedin.com/in/shanky99"))),
        deduplicationColumns = Seq(SrProspectColumns.Email),
        updateProspect = UpdateProspectType.None,
        ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads = true,
        doerAccount = accountAdmin,
        prospectSource = None,
        prospectAccountId = None,
        triggerSource = TriggerSource.OTHER,
        permittedAccountIdsForEditingProspects = permittedAccountIds,
        permittedAccountIdsForEditingProspectAccounts = permittedAccountIds,
        is_public_api = false,
        Logger = Logger
      ) match {
        case Failure(exception) =>
          println(s"Error: ${LogHelpers.getStackTraceAsString(exception)}")
          assert(false)

        case Success(result) =>

          assert(result == UpsertSQLResult(List(newlyCreatedProspect),List(newlyCreatedProspect.prospect_id),List(),List(),List(),List(), 0, updated_prospects_api.map(p => p.copy(prospect_data = p.prospect_data.copy(is_updated_internal = false)))))
      }
    }



    // failing test
    it("will fail when update return DB error - part 1 duplicate") {
      (prospectColumnDef.findCustomColumns)
        .expects(teamId)
        .returning(Seq())

      (prospectDAOService.getProspectCategoryId(_: TeamId, _: ProspectCategory.Value, _: Option[Account])(using _:SRLogger))
        .expects(TeamId(id = teamId), ProspectCategory.DO_NOT_CONTACT, Some(accountAdmin), *)
        .returning(Success(ProspectCategoryId(1L)))

      (prospectDAOService.getProspectCategoryId(_: TeamId, _: ProspectCategory.Value, _: Option[Account])(using _:SRLogger))
        .expects(TeamId(id = teamId), ProspectCategory.NOT_CATEGORIZED, Some(accountAdmin), *)
        .returning(Success(ProspectCategoryId(1L)))

      (prospectDAOService.findOrCreateList)
        .expects(None, 2, permittedAccountIds, teamId)
        .returning(None)

      (prospectAccountDAO1.find)
        .expects(120, teamId)
        .returning(Some(ProspectAccountFixture.prospectAccount))

      (blacklistProspectCheckDAO.findByEmailsAndDomains)
        .expects(*, teamId, OrgId(id = org.id), Seq(email), Seq(email_domain))
        .returning(Success(Seq()))

      (prospectDAOService.findDuplicateProspectsForForceUpdateV2(_: Seq[Seq[DuplicationFindProspectDataV2]], _: TeamId)(using _:SRLogger))
        .expects(*, TeamId(teamId), *)
        .returning(Success(List(DuplicateProspectResult(
          prospectCreateFormData = prospectCreateFormdata,
          deDuplicateColumnTypeAndValues, 1L, Some(ownerId)))))

      (prospectDAOService.updateProspectsForEmailOptional(_: Boolean, _: Seq[ColumnDef], _: Option[Long], _: Iterable[ProspectsToBeForceUpdated], _: Seq[SrProspectColumns], _: List[ProspectEmailsTobeAddedInForceUpdateProspect], _: Long)(using _: SRLogger))
        .expects(true, List(), *, *, *, *, teamId, *)
        .returning(Failure(new Throwable(s"prospectDAO.updateProspects DB Error")))
//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//        _: TeamId,
//        _: SrRollingUpdateFeature
//      )(_: ISRLogger))
//        .expects(TeamId(37L), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .returning(false)


      prospectService.__createFromUploadMultiRowInsertNew(
        ownerAccountId = ownerId,
        teamId = teamId,
        taId = teamMember.ta_id,
        listName = None,
        prospects = Seq(prospectCreateFormdata),
        deduplicationColumns = Seq(SrProspectColumns.Email),
        updateProspect = UpdateProspectType.ForceUpdate,
        ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads = true,
        doerAccount = accountAdmin,
        prospectSource = None,
        prospectAccountId = Some(120L),
        triggerSource = TriggerSource.CRM,
        permittedAccountIdsForEditingProspects = permittedAccountIds,
        permittedAccountIdsForEditingProspectAccounts = permittedAccountIds,
        is_public_api = false,
        Logger = Logger
      ) match {
        case Failure(exception) => assert(exception.getMessage == s"prospectDAO.updateProspects DB Error")
        case Success(result) => assert(false)
      }
    }
    /*
                */

    /*
     * In this test we will pass a prospect which will be present in the DB
     * - but the values in the DB will not match the input values
     * - so the DB will actually need to fire the update
    */


    it("will fail when update return DB error - part 2 no duplicate") {
      (prospectColumnDef.findCustomColumns)
        .expects(teamId)
        .returning(Seq())

      (prospectDAOService.getProspectCategoryId(_: TeamId, _: ProspectCategory.Value, _: Option[Account])(using _:SRLogger))
        .expects(TeamId(id = teamId), ProspectCategory.DO_NOT_CONTACT, Some(accountAdmin), *)
        .returning(Success(ProspectCategoryId(1L)))

      (prospectDAOService.getProspectCategoryId(_: TeamId, _: ProspectCategory.Value, _: Option[Account])(using _:SRLogger))
        .expects(TeamId(id = teamId), ProspectCategory.NOT_CATEGORIZED, Some(accountAdmin), *)
        .returning(Success(ProspectCategoryId(1L)))

      (prospectDAOService.findOrCreateList)
        .expects(None, 2, permittedAccountIds, teamId)
        .returning(None)

      (prospectAccountDAO1.find)
        .expects(120, teamId)
        .returning(Some(ProspectAccountFixture.prospectAccount))

      val sillybillys_email = "<EMAIL>"
      val sillybillys_email_domain = "smartreach.com"
      val sillybillys_prospect_id_from_db = 123L
      val sillybilly_prospect_from_user: ProspectCreateFormData = prospectCreateFormdata.copy(
        owner_id = Some(ownerId),
        first_name = Some("billy"),
        email = Some(sillybillys_email)
      )

      (blacklistProspectCheckDAO.findByEmailsAndDomains)
        .expects(*, teamId, OrgId(id = org.id), Seq(email, sillybillys_email), Seq(email_domain, sillybillys_email_domain))
        .returning(Success(Seq()))

      (prospectDAOService.findDuplicateProspectsForForceUpdateV2(_: Seq[Seq[DuplicationFindProspectDataV2]], _: TeamId)(using _:SRLogger))
        .expects(*, TeamId(teamId), *)
        .returning(Success(
          List(
            DuplicateProspectResult(
              prospectCreateFormData = sillybilly_prospect_from_user.copy(first_name = Some("sillybilly")), deDuplicateColumnTypeAndValues.map(p => p.copy(deDuplicationColumnValue = sillybillys_email)), sillybillys_prospect_id_from_db, Some(ownerId)),
            DuplicateProspectResult(
              prospectCreateFormData = prospectCreateFormdata, deDuplicateColumnTypeAndValues, 1L, Some(ownerId)))
        ))

      // note that the firstname from the user is silly , whereas
      // - when we fetch from the db the name is sillybilly
      // - this will cause the update to be fired
      (prospectDAOService.updateProspectsForEmailOptional(_: Boolean, _: Seq[ColumnDef], _: Option[Long], _: Iterable[ProspectsToBeForceUpdated], _: Seq[SrProspectColumns], _: List[ProspectEmailsTobeAddedInForceUpdateProspect], _: Long)(using _: SRLogger))
        .expects(true, List(), *, *, *, *, teamId, *)
        .returning(Failure(new Throwable(s"prospectDAO.updateProspects DB Error")))


//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//        _: TeamId,
//        _: SrRollingUpdateFeature
//      )(_: ISRLogger))
//        .expects(TeamId(37L), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .returning(false)

      prospectService.__createFromUploadMultiRowInsertNew(
        ownerAccountId = ownerId,
        teamId = teamId,
        taId = teamMember.ta_id,
        listName = None,
        prospects = Seq(prospectCreateFormdata, sillybilly_prospect_from_user),
        deduplicationColumns = Seq(SrProspectColumns.Email),
        updateProspect = UpdateProspectType.ForceUpdate,
        ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads = true,
        doerAccount = accountAdmin,
        prospectSource = None,
        prospectAccountId = Some(120L),
        triggerSource = TriggerSource.CRM,
        permittedAccountIdsForEditingProspects = permittedAccountIds,
        permittedAccountIdsForEditingProspectAccounts = permittedAccountIds,
        is_public_api = false,
        Logger = Logger
      ) match {
        case Failure(exception) => assert(exception.getMessage == s"prospectDAO.updateProspects DB Error")
        case Success(result) => assert(false)
      }
    }
    /*
        */


    // failing test
    it("will fail when insert the new prospects in prospects table fails") {
      (prospectColumnDef.findCustomColumns)
        .expects(teamId)
        .returning(Seq())

      (prospectDAOService.getProspectCategoryId(_: TeamId, _: ProspectCategory.Value, _: Option[Account])(using _:SRLogger))
        .expects(TeamId(id = teamId), ProspectCategory.DO_NOT_CONTACT, Some(accountAdmin), *)
        .returning(Success(ProspectCategoryId(1L)))

      (prospectDAOService.findOrCreateList)
        .expects(None, 2, permittedAccountIds, teamId)
        .returning(None)

      (prospectAccountDAO1.find)
        .expects(120, teamId)
        .returning(Some(ProspectAccountFixture.prospectAccount))


      (blacklistProspectCheckDAO.findByEmailsAndDomains)
        .expects(*, teamId, OrgId(id = org.id), Seq(email), Seq(email_domain))
        .returning(Success(Seq()))


      (prospectDAOService.findDuplicateProspectsForForceUpdateV2(_: Seq[Seq[DuplicationFindProspectDataV2]], _: TeamId)(using _:SRLogger))
        .expects(*, TeamId(teamId), *)
        .returning(Success(
          List()))
      (prospectDAOService.getProspectCategoryId(_: TeamId, _: ProspectCategory.Value, _: Option[Account])(using _:SRLogger))
        .expects(TeamId(id = teamId), ProspectCategory.NOT_CATEGORIZED, Some(accountAdmin), *)
        .returning(Success(ProspectCategoryId(1L)))

      (() => srUuidUtils.generateProspectUuid())
        .expects()
        .returning(temp_prospect_id)

      (prospectDAOService.insertNewProspects)
        .expects(*, List(BlacklistStatusForProspectUuid(ProspectUuid("prs_aa_qwerty"),false)),
          1L, 1L, None, Some(120L), *, true, List(), ownerId, ownerId, teamId, TriggerSource.OTHER, *, *, *)
        .returning(Failure(new Throwable(s"prospectDAO.insertNewProspects DB Error")))

//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//        _: TeamId,
//        _: SrRollingUpdateFeature
//      )(_: ISRLogger))
//        .expects(TeamId(37L), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .returning(false)


      prospectService.__createFromUploadMultiRowInsertNew(
        ownerAccountId = ownerId,
        teamId = teamId,
        taId = teamMember.ta_id,
        listName = None,
        prospects = Seq(prospectCreateFormdata),
        deduplicationColumns = Seq(SrProspectColumns.Email),
        updateProspect = UpdateProspectType.None,
        ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads = true,
        doerAccount = accountAdmin,
        prospectSource = None,
        prospectAccountId = Some(120L),
        triggerSource = TriggerSource.OTHER,
        permittedAccountIdsForEditingProspects = permittedAccountIds,
        permittedAccountIdsForEditingProspectAccounts = permittedAccountIds,
        is_public_api = false,
        Logger = Logger
      ) match{
        case Failure(exception) =>
          assert(exception.getMessage == s"prospectDAO.insertNewProspects DB Error")
        case Success(result) => assert(false)
      }
    }
    //    /*
    //    */


    it("should update/insert more than 100 prospects") {

      val prospectList = (101 to 444)
        .map(i => prospectCreateFormdata.copy(email = Some(s"prospectemail${i}@email.com")))
        .toList

      (prospectColumnDef.findCustomColumns)
        .expects(teamId)
        .returning(Seq())

      (prospectDAOService.getProspectCategoryId(_: TeamId, _: ProspectCategory.Value, _: Option[Account])(using _:SRLogger))
        .expects(TeamId(id = teamId), ProspectCategory.DO_NOT_CONTACT, Some(accountAdmin), *)
        .returning(Success(ProspectCategoryId(1L)))

      (prospectDAOService.getProspectCategoryId(_: TeamId, _: ProspectCategory.Value, _: Option[Account])(using _:SRLogger))
        .expects(TeamId(id = teamId), ProspectCategory.NOT_CATEGORIZED, Some(accountAdmin), *)
        .returning(Success(ProspectCategoryId(2L)))

      (prospectDAOService.findOrCreateList)
        .expects(None, 2, permittedAccountIds, teamId)
        .returning(None)

      (prospectAccountDAO1.find)
        .expects(120, teamId)
        .returning(Some(ProspectAccountFixture.prospectAccount))

      (blacklistProspectCheckDAO.findByEmailsAndDomains)
        .expects(*, teamId, OrgId(id = org.id), *, *)
        .returning(Success(Seq()))

      val prospectResult = prospectList.zipWithIndex.map {
        case (p, index) =>
          DuplicateProspectResult(
            prospectCreateFormData = prospectCreateFormdata,
            deDuplicateColumnTypeAndValues.map(p1 => p1.copy(deDuplicationColumnValue = p.email.get)), index + 101, Some(ownerId))
      }

      val updateProspectResult_new = (101 to 150).zipWithIndex.map {
        case (_, index) =>
          updateProspectResult.copy(index + 101, prospect_uuid = Some(s"prs_aa_qwerty${index + 101}"), is_updated_internal = true, account_id = ownerId)
      }.toList

      (prospectDAOService.findDuplicateProspectsForForceUpdateV2(_: Seq[Seq[DuplicationFindProspectDataV2]], _: TeamId)(using _:SRLogger))
        .expects(*, TeamId(teamId), *)
        .returning(Success(prospectResult.take(50)))

      (mqDomainServiceProviderDNSService.publish)
        .expects("email.com")
        .returning(Success({}))
      (prospectDAOService.updateProspectsForEmailOptional(_: Boolean, _: Seq[ColumnDef], _: Option[Long], _: Iterable[ProspectsToBeForceUpdated], _: Seq[SrProspectColumns], _: List[ProspectEmailsTobeAddedInForceUpdateProspect], _: Long)(using _: SRLogger))
        .expects(true, List(), *, *, *, *, teamId, *)
        .returning(Success(updateProspectResult_new))

      for (a <- 151 until 201) {
        (() => srUuidUtils.generateProspectUuid())
          .expects()
          .returning(temp_prospect_id + s"$a")
      }

      val insertedProspectResult_new = (151 to 200).zipWithIndex.map {
        case (_, index) =>
          insertedProspectResult.copy(index + 151, Some(temp_prospect_id + s"${index + 151}"), true, ownerId)
      }.toList

      (prospectDAOService.insertNewProspects)
        .expects(*, *, *, *, None, *, *, true, *, ownerId, ownerId, teamId, TriggerSource.OTHER, *, *, *)
        .returning(Success(insertedProspectResult_new))

      val newly_created_prospect_1 = (151 to 200).zipWithIndex.map {
        case (_, index) =>
          newlyCreatedProspect.copy(
            prospect_id = index + 151,
            email = s"prospectemail${index + 151}@email.com"
          )
      }.toList

      (prospectsEmailsDAO.__createFromUploadMultiRowInsertV2_New)
        .expects(*, *, *, teamId)
        .returning(Success(newly_created_prospect_1))
      (mqDomainServiceProviderDNSService.publish)
        .expects("email.com")
        .returning(Success({}))
      //Second loop
      (blacklistProspectCheckDAO.findByEmailsAndDomains)
        .expects(*, teamId, OrgId(id = org.id), *, *)
        .returning(Success(Seq()))

      (prospectDAOService.findDuplicateProspectsForForceUpdateV2(_: Seq[Seq[DuplicationFindProspectDataV2]], _: TeamId)(using _:SRLogger))
        .expects(*, TeamId(teamId), *)
        .returning(Success(List()))

      (prospectDAOService.updateProspectsForEmailOptional(_: Boolean, _: Seq[ColumnDef], _: Option[Long], _: Iterable[ProspectsToBeForceUpdated], _: Seq[SrProspectColumns], _: List[ProspectEmailsTobeAddedInForceUpdateProspect], _: Long)(using _: SRLogger))
        .expects(true, List(), *, *, *, *, teamId, *)
        .returning(Success(List()))

      for (a <- 201 until 301) {
        (() => srUuidUtils.generateProspectUuid())
          .expects()
          .returning(temp_prospect_id + s"$a")
      }

      val insertedProspectResult2 = (201 to 300).zipWithIndex.map {
        case (_, index) =>
          insertedProspectResult.copy(index + 201, Some(temp_prospect_id + s"${index + 201}"), true, ownerId)
      }.toList

      (prospectDAOService.insertNewProspects)
        .expects(*, *, *, *, None, *, *, true, *, ownerId, ownerId, teamId, TriggerSource.OTHER, *, *, *)
        .returning(Success(insertedProspectResult2))

      val newly_created_prospect_2 = (201 to 300).zipWithIndex.map {
        case (_, index) =>
          newlyCreatedProspect.copy(
            prospect_id = index + 201,
            email = s"prospectemail${index + 201}@email.com"
          )
      }.toList

      (prospectsEmailsDAO.__createFromUploadMultiRowInsertV2_New)
        .expects(*, *, *, teamId)
        .returning(Success(newly_created_prospect_2))
      (mqDomainServiceProviderDNSService.publish)
        .expects("email.com")
        .returning(Success({}))
      //Third loop
      (blacklistProspectCheckDAO.findByEmailsAndDomains)
        .expects(*, teamId, OrgId(id = org.id), *, *)
        .returning(Success(Seq()))

      (prospectDAOService.findDuplicateProspectsForForceUpdateV2(_: Seq[Seq[DuplicationFindProspectDataV2]], _: TeamId)(using _:SRLogger))
        .expects(*, TeamId(teamId), *)
        .returning(Success(List()))

      (prospectDAOService.updateProspectsForEmailOptional(_: Boolean, _: Seq[ColumnDef], _: Option[Long], _: Iterable[ProspectsToBeForceUpdated], _: Seq[SrProspectColumns], _: List[ProspectEmailsTobeAddedInForceUpdateProspect], _: Long)(using _: SRLogger))
        .expects(true, List(), *, *, *, *, teamId, *)
        .returning(Success(List()))

      for (a <- 301 until 401) {
        (() => srUuidUtils.generateProspectUuid())
          .expects()
          .returning(temp_prospect_id + s"$a")
      }

      val insertedProspectResult3 = (301 to 400).zipWithIndex.map {
        case (_, index) =>
          insertedProspectResult.copy(index + 301, Some(temp_prospect_id + s"${index + 301}"), true, ownerId)
      }.toList

      (prospectDAOService.insertNewProspects)
        .expects(*, *, *, *, None, *, *, true, *, ownerId, ownerId, teamId, TriggerSource.OTHER, *, *, *)
        .returning(Success(insertedProspectResult3))

      val newly_created_prospect_3 = (301 to 400).zipWithIndex.map {
        case (_, index) =>
          newlyCreatedProspect.copy(
            prospect_id = index + 301,
            email = s"prospectemail${index + 301}@email.com"
          )
      }.toList

      (prospectsEmailsDAO.__createFromUploadMultiRowInsertV2_New)
        .expects(*, *, *, teamId)
        .returning(Success(newly_created_prospect_3))
      (mqDomainServiceProviderDNSService.publish)
        .expects("email.com")
        .returning(Success({}))
      //Fourth loop
      (blacklistProspectCheckDAO.findByEmailsAndDomains)
        .expects(*, teamId, OrgId(id = org.id), *, *)
        .returning(Success(Seq()))

      (prospectDAOService.findDuplicateProspectsForForceUpdateV2(_: Seq[Seq[DuplicationFindProspectDataV2]], _: TeamId)(using _:SRLogger))
        .expects(*, TeamId(teamId), *)
        .returning(Success(List()))

      (prospectDAOService.updateProspectsForEmailOptional(_: Boolean, _: Seq[ColumnDef], _: Option[Long], _: Iterable[ProspectsToBeForceUpdated], _: Seq[SrProspectColumns], _: List[ProspectEmailsTobeAddedInForceUpdateProspect], _: Long)(using _: SRLogger))
        .expects(true, List(), *, *, *, *, teamId, *)
        .returning(Success(List()))

      for (a <- 401 until 445) {
        (() => srUuidUtils.generateProspectUuid())
          .expects()
          .returning(temp_prospect_id + s"$a")
      }

      val insertedProspectResult4 = (401 to 444).zipWithIndex.map {
        case (_, index) =>
          insertedProspectResult.copy(index + 401, Some(temp_prospect_id + s"${index + 401}"), true, ownerId)
      }.toList

      (prospectDAOService.insertNewProspects)
        .expects(*, *, *, *, None, *, *, true, *, ownerId, ownerId, teamId, TriggerSource.OTHER, *, *, *)
        .returning(Success(insertedProspectResult4))

      val newly_created_prospect_4 = (401 to 444).zipWithIndex.map {
        case (_, index) =>
          newlyCreatedProspect.copy(
            prospect_id = index + 401,
            email = s"prospectemail${index + 401}@email.com"
          )
      }.toList


      (prospectsEmailsDAO.__createFromUploadMultiRowInsertV2_New)
        .expects(*, *, *, teamId)
        .returning(Success(newly_created_prospect_4))

      (mergeTagService._removeMissingErrorMessage)
        .expects(*)
        .returning(1)


//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//        _: TeamId,
//        _: SrRollingUpdateFeature
//      )(_: ISRLogger))
//        .expects(TeamId(37L), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .returning(false)


      prospectService.__createFromUploadMultiRowInsertNew(
        ownerAccountId = ownerId,
        teamId = teamId,
        taId = teamMember.ta_id,
        listName = None,
        prospects = prospectList,
        deduplicationColumns = Seq(SrProspectColumns.Email),
        updateProspect = UpdateProspectType.ForceUpdate,
        ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads = true,
        doerAccount = accountAdmin,
        prospectSource = None,
        prospectAccountId = Some(120L),
        triggerSource = TriggerSource.OTHER,
        permittedAccountIdsForEditingProspects = permittedAccountIds,
        permittedAccountIdsForEditingProspectAccounts = permittedAccountIds,
        is_public_api = false,
        Logger = Logger
      ) match {
        case Failure(exception) =>
          assert(false)
        case Success(result) =>
          //Out of 344 prospects 294 inserted and 50 updated which were duplicates

          val upsertSqlRes = (101 to 444).zipWithIndex.map {
            case (_, index) =>
              upsertSQLProspectData.copy(
                prospect_email = s"prospectemail${index + 101}@email.com",
                prospect_data = upsertSQLProspectData.prospect_data.copy(
                  prospect_id = index + 101,
                  prospect_uuid = Some(s"prs_aa_qwerty${index + 101}")
                )
              )
          }.toList

          val prospect_ids: List[Long] = List(101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115,
            116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135,
            136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150)

          val duplicate_list_result: List[DuplicateProspectResult] = List(101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115,
            116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135,
            136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150).map(p =>
            DuplicateProspectResult(
              prospectCreateFormData = prospectCreateFormdata.copy(email = Some("<EMAIL>")),
              deDuplicateColumnAndValue = deDuplicateColumnTypeAndValues.map(p1 => p1.copy(deDuplicationColumnValue = s"prospectemail${p}@email.com")),
              prospect_id = p,
              account_id = Some(ownerId)
            )
          )
          assert(result == UpsertSQLResult(
            newly_created_prospect_1 ++ newly_created_prospect_2 ++ newly_created_prospect_3 ++ newly_created_prospect_4,
            newly_created_prospect_1.map(_.prospect_id) ++ newly_created_prospect_2.map(_.prospect_id) ++ newly_created_prospect_3.map(_.prospect_id) ++ newly_created_prospect_4.map(_.prospect_id),
            prospect_ids,
            duplicate_list_result,
            List(),
            prospect_ids,
            0,
            upsertSqlRes)
          )
      }
    }

    it("will throw 'invalid account' when prospectAccountDAO1.find returns None") {

      (prospectColumnDef.findCustomColumns)
        .expects(teamId)
        .returning(Seq())

      (prospectDAOService.getProspectCategoryId(_: TeamId, _: ProspectCategory.Value, _: Option[Account])(using _:SRLogger))
        .expects(TeamId(id = teamId), ProspectCategory.DO_NOT_CONTACT, Some(accountAdmin), *)
        .returning(Success(ProspectCategoryId(1L)))

      (prospectDAOService.getProspectCategoryId(_: TeamId, _: ProspectCategory.Value, _: Option[Account])(using _:SRLogger))
        .expects(TeamId(id = teamId), ProspectCategory.NOT_CATEGORIZED, Some(accountAdmin), *)
        .returning(Success(ProspectCategoryId(1L)))

      (prospectDAOService.findOrCreateList)
        .expects(None, 2, permittedAccountIds, teamId)
        .returning(None)

      (prospectAccountDAO1.find)
        .expects(120, teamId)
        .returning(None)

//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//        _: TeamId,
//        _: SrRollingUpdateFeature
//      )(_: ISRLogger))
//        .expects(TeamId(teamId), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .returning(true)

      prospectService.__createFromUploadMultiRowInsertNew(
        ownerAccountId = ownerId,
        teamId = teamId,
        taId = teamMember.ta_id,
        listName = None,
        prospects = Seq(prospectCreateFormdata),
        deduplicationColumns = Seq(SrProspectColumns.Email),
        updateProspect = UpdateProspectType.ForceUpdate,
        ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads = true,
        doerAccount = accountAdmin,
        prospectSource = None,
        prospectAccountId = Some(120L),
        triggerSource = TriggerSource.CRM,
        permittedAccountIdsForEditingProspects = permittedAccountIds,
        permittedAccountIdsForEditingProspectAccounts = permittedAccountIds,
        is_public_api = false,
        Logger = Logger
      ) match {
        case Failure(exception) =>
          Logger.error(s"__createFromUploadMultiRowInsertError!!..... ${exception.getMessage}")
          //__createFromUploadMultiRowInsertError!!..... Invalid account.
          assert(true)

        case Success(result) =>
          assert(false)
      }
    }
  }

  val emails_in_team = List("<EMAIL>, <EMAIL>, <EMAIL>")
  val email_settings = List("<EMAIL>, <EMAIL>")
  val owner_email = "<EMAIL>"

  describe("prospectService.getAllInternalEmailsInTeam") {

    it("should throw exception if emailSettingDAO.getAllEmailSettingsInTeam fails") {

      (emailSettingDAO.getAllEmailSettingsInTeam)
        .expects(teamId)
        .returning(Failure(new Throwable("DBError: emailSettingDAO.getAllEmailSettingsInTeam failed")))

      prospectService.getAllInternalEmailsInTeam(
        team_id = teamId,
        org_id = org.id,
        Logger = Logger
      ) match {
        case Failure(_) => assert(true)
        case Success(_) => assert(false)
      }
    }

    it("should throw exception if accountService.getEmailsOfTeamMembers fails") {

      (emailSettingDAO.getAllEmailSettingsInTeam)
        .expects(teamId)
        .returning(Success(email_settings))

      (accountService.getEmailsOfTeamMembers)
        .expects(TeamId(id = teamId)) // FIXME VALUECLASS
        .returning(Failure(new Throwable("DBError: accountService.getEmailsOfTeamMembers failed")))

      prospectService.getAllInternalEmailsInTeam(
        team_id = teamId,
        org_id = org.id,
        Logger = Logger
      ) match {
        case Failure(_) => assert(true)
        case Success(_) => assert(false)
      }
    }

    it("should throw exception if accountService.getEmailOfOrgOwner fails") {

      (emailSettingDAO.getAllEmailSettingsInTeam)
        .expects(teamId)
        .returning(Success(email_settings))

      (accountService.getEmailsOfTeamMembers)
        .expects(TeamId(id = teamId)) // FIXME VALUECLASS
        .returning(Success(emails_in_team))

      (accountService.getEmailOfOrgOwner)
        .expects(OrgId(id = org.id)) // FIXME VALUECLASS
        .returning(Failure(new Throwable("DBError: accountService.getEmailOfOrgOwner failed")))

      prospectService.getAllInternalEmailsInTeam(
        team_id = teamId,
        org_id = org.id,
        Logger = Logger
      ) match {
        case Failure(_) => assert(true)
        case Success(_) => assert(false)
      }
    }

    it("should return InternalEmailsOrDomains with unique internal emails") {

      (emailSettingDAO.getAllEmailSettingsInTeam)
        .expects(teamId)
        .returning(Success(email_settings))

      (accountService.getEmailsOfTeamMembers)
        .expects(TeamId(id = teamId)) // FIXME VALUECLASS
        .returning(Success(emails_in_team))

      (accountService.getEmailOfOrgOwner)
        .expects(OrgId(id = org.id)) // FIXME VALUECLASS
        .returning(Success(owner_email))

      prospectService.getAllInternalEmailsInTeam(
        team_id = teamId,
        org_id = org.id,
        Logger = Logger
      ) match {
        case Failure(_) => assert(false)
        case Success(result) =>
          assert(result == InternalEmailsOrDomains((email_settings ++ emails_in_team).toSet.toList, List("team.com")))
      }
    }

  }

  val prospects: Seq[ProspectCreateFormData] = Seq(
    ProspectCreateFormData(
      email = Some("<EMAIL>"),
      first_name = Some("Shubham"),
      last_name = Some("Kudekar"),
      custom_fields = Json.obj(),
      owner_id = None,
      list = None,
      company = None,
      city = None,
      country = None,
      timezone = None,
      created_at = None,
      state = None,
      job_title = None,
      phone = None,
      phone_2 = None,
      phone_3 = None,
      linkedin_url = None
    ), ProspectCreateFormData(
      email = Some("<EMAIL>"),
      first_name = Some("Shubham"),
      last_name = Some("Kudekar"),
      custom_fields = Json.obj(),
      owner_id = None,
      list = None,
      company = None,
      city = None,
      country = None,
      timezone = None,
      created_at = None,
      state = None,
      job_title = None,
      phone = None,
      phone_2 = None,
      phone_3 = None,
      linkedin_url = None),
    ProspectCreateFormData(
      email = Some("<EMAIL>"),
      first_name = Some("Shubham"),
      last_name = Some("Kudekar"),
      custom_fields = Json.obj(),
      owner_id = None,
      list = None,
      company = None,
      city = None,
      country = None,
      timezone = None,
      created_at = None,
      state = None,
      job_title = None,
      phone = None,
      phone_2 = None,
      phone_3 = None,
      linkedin_url = None),
    ProspectCreateFormData(
      email = Some("<EMAIL>"),
      first_name = Some("Shubham"),
      last_name = Some("Kudekar"),
      custom_fields = Json.obj(),
      owner_id = None,
      list = None,
      company = None,
      city = None,
      country = None,
      timezone = None,
      created_at = None,
      state = None,
      job_title = None,
      phone = None,
      phone_2 = None,
      phone_3 = None,
      linkedin_url = None)
  )


  describe("ProspectService.findNumberOfDuplicateEmailsOfProspects") {

    it("should give result as 2 for 2 unique emails which were duplicated twice") {
      val res = ProspectService.findNumberOfDuplicateEmailsOfProspects(prospects = prospects)
      assert(res == 2)
    }

    it("should give result as 1 for 3 unique and 1 duplicate emails") {
      val res = ProspectService.findNumberOfDuplicateEmailsOfProspects(prospects = prospects.updated(1, prospects(1).copy(email = Some("<EMAIL>"))))
      assert(res == 1)
    }

    it("should obtain result which is independent of case") {
      val res = ProspectService.findNumberOfDuplicateEmailsOfProspects(prospects =
        prospects.updated(1, prospects(1).copy(email = Some("<EMAIL>"))))
      assert(res == 2)

    }
    it("should give 0 for no duplicate emails") {
      val res = ProspectService.findNumberOfDuplicateEmailsOfProspects(prospects = prospects.updated(1, prospects(1).copy(email = Some("<EMAIL>")))
        .updated(2, prospects(2).copy(Some("<EMAIL>"))))
      assert(res == 0)

    }

    it("should give 1 for 1 duplicate email and one non-email prospect") {
      val prospect = ProspectCreateFormData(
        email = Some("<EMAIL>"),
        first_name = Some("Prachi"),
        last_name = Some("Mane"),
        custom_fields = Json.obj(),
        owner_id = None,
        list = None,
        company = None,
        city = None,
        country = None,
        timezone = None,
        created_at = None,
        state = None,
        job_title = None,
        phone = None,
        phone_2 = None,
        phone_3 = None,
        linkedin_url = None
      )

      val prospectsList: Seq[ProspectCreateFormData] = Seq(prospect, prospect, prospect.copy(email = None, phone = Some("+************")))

      val res = ProspectService.findNumberOfDuplicateEmailsOfProspects(
        prospects = prospectsList
      )
      assert(res == 1 && prospectsList.length == 3)

    }
  }

  describe("ProspectService.insertSecondaryProspectEmail") {
    it("should return left for invalid email") {

      prospectService.insertSecondaryProspectEmail(
        team_id = teamId,
        added_by_account_id = accountAdmin.internal_id,
        prospect_id = 10L,
        email = "secondary_email@company",
        orgId = OrgId(org.id),
        active_campaign_ids = Seq()
      ) match {
        case Left(error) => assert(error == InsertSecondaryProspectEmailError.InvalidEmailFormat("Please send valid email format"))
        case Right(_) => assert(false)
      }
    }

    it("should return left if prospect email already present") {

      (emailSettingDAO.getAllEmailSettingsInTeam)
        .expects(teamId)
        .returning(Success(List("<EMAIL>")))

      (accountService.getEmailsOfTeamMembers)
        .expects(TeamId(teamId))
        .returning(Success(List("<EMAIL>")))

      (accountService.getEmailOfOrgOwner)
        .expects(OrgId(org.id))
        .returning(Success("<EMAIL>"))

      (prospectsEmailsDAO.findByEmailV2)
        .expects(Seq("<EMAIL>"), teamId, *)
        .returning(Success(Seq(ProspectIdEmail(7L, "<EMAIL>", 22L))))

      prospectService.insertSecondaryProspectEmail(
        team_id = teamId,
        added_by_account_id = accountAdmin.internal_id,
        prospect_id = 10L,
        email = "<EMAIL>",
        orgId = OrgId(org.id),
        active_campaign_ids = Seq()
      ) match {
        case Left(error) =>
          assert(error == InsertSecondaryProspectEmailError.DuplicateProspect("Email is already present as prospect"))
        case Right(_) => assert(false)
      }
    }

    it("should return left if email is internal email or domain") {

      (emailSettingDAO.getAllEmailSettingsInTeam)
        .expects(teamId)
        .returning(Success(List("<EMAIL>")))

      (accountService.getEmailsOfTeamMembers)
        .expects(TeamId(teamId))
        .returning(Success(List("<EMAIL>")))

      (accountService.getEmailOfOrgOwner)
        .expects(OrgId(org.id))
        .returning(Success("<EMAIL>"))

      prospectService.insertSecondaryProspectEmail(
        team_id = teamId,
        added_by_account_id = accountAdmin.internal_id,
        prospect_id = 10L,
        email = "<EMAIL>",
        orgId = OrgId(org.id),
        active_campaign_ids = Seq()
      ) match {
        case Left(error) =>
          assert(error == InsertSecondaryProspectEmailError.InternalEmailOrDomainFound("Internal email cannot be added as secondary email"))
        case Right(_) => assert(false)
      }
    }

    it("should return success if secondary email added and calls MQ to associate prospect") {

      val prospect_id = 10L
      val email = "<EMAIL>"

      (emailSettingDAO.getAllEmailSettingsInTeam)
        .expects(teamId)
        .returning(Success(List("<EMAIL>")))

      (accountService.getEmailsOfTeamMembers)
        .expects(TeamId(teamId))
        .returning(Success(List("<EMAIL>")))

      (accountService.getEmailOfOrgOwner)
        .expects(OrgId(org.id))
        .returning(Success("<EMAIL>"))

      (prospectsEmailsDAO.findByEmailV2)
        .expects(Seq("<EMAIL>"), teamId, *)
        .returning(Success(Seq()))

      (prospectsEmailsDAO.addSecondaryProspectEmail)
        .expects(*)
        .returning(Success(NewlyCreatedProspect(prospect_id, email)))
      (mqDomainServiceProviderDNSService.publish)
        .expects("company.in")
        .returning(Success({}))
      (mqAssociateProspectToOldEmails.publish)
        .expects(*)
        .returning(Success(()))

      prospectService.insertSecondaryProspectEmail(
        team_id = teamId,
        added_by_account_id = accountAdmin.internal_id,
        prospect_id = prospect_id,
        email = email,
        orgId = OrgId(org.id),
        active_campaign_ids = Seq()
      ) match {
        case Left(_) => assert(false)
        case Right(_) => assert(true)
      }
    }


  }

  val prospectBatchCreateFormData: ProspectBatchCreateFormData = ProspectBatchCreateFormData(
    prospects = prospects,
    tags = Some(Seq("tag1", "tag2"))
  )


  describe("ProspectService.createOrUpdateProspectsBatch") {
    it("should add/update prospects successfully") {

      (accountService.findNewOwnerTeamMember(_: TeamId, _: AccountId))
        .expects(TeamId(id = teamId), AccountId(id = ownerId))
        .returning(Success(Some(teamMemberBasic)))

      (emailSettingDAO.getAllEmailSettingsInTeam)
        .expects(teamId)
        .returning(Success(email_settings))

      (accountService.getEmailsOfTeamMembers)
        .expects(TeamId(id = teamId))
        .returning(Success(emails_in_team))

      (accountService.getEmailOfOrgOwner)
        .expects(OrgId(id = org.id))
        .returning(Success("<EMAIL>"))

      (prospectColumnDef.findCustomColumns)
        .expects(teamId)
        .returning(Seq())

      (prospectDAOService.getProspectCategoryId(_: TeamId, _: ProspectCategory.Value, _: Option[Account])(using _:SRLogger))
        .expects(TeamId(id = teamId), ProspectCategory.DO_NOT_CONTACT, *, *)
        .returning(Success(ProspectCategoryId(1L)))

      (prospectDAOService.getProspectCategoryId(_: TeamId, _: ProspectCategory.Value, _: Option[Account])(using _:SRLogger))
        .expects(TeamId(id = teamId), ProspectCategory.NOT_CATEGORIZED, *, *)
        .returning(Success(ProspectCategoryId(2L)))

      (prospectDAOService.findOrCreateList)
        .expects(None, 2, permittedAccountIds, teamId)
        .returning(Some(1L))

      (prospectAccountDAO1.findOrCreateProspectAccount)
        .expects(Seq(prospectAccountCreateFormData.copy(name = "smartreach.io")), ownerId, permittedAccountIds, teamId, *)
        .returning(prospectAccountCreateOrUpdateResult)

      (blacklistProspectCheckDAO.findByEmailsAndDomains)
        .expects(*, teamId, OrgId(id = org.id), *, *)
        .returning(Success(Seq()))


      (prospectDAOService.findDuplicateProspectsForForceUpdateV2(_: Seq[Seq[DuplicationFindProspectDataV2]], _: TeamId)(using _:SRLogger))
        .expects(*, TeamId(teamId), *)
        .returning(Success(List()))


      (prospectDAOService.updateProspectsForEmailOptional(_: Boolean, _: Seq[ColumnDef], _: Option[Long], _: Iterable[ProspectsToBeForceUpdated], _: Seq[SrProspectColumns], _: List[ProspectEmailsTobeAddedInForceUpdateProspect], _: Long)(using _: SRLogger))
        .expects(true, List(), *, *, *, *, teamId, *)
        .returning(Success(List(updateProspectResult.copy(1L, Some("prs_aa_abcdef"), true, ownerId))))

      (() => srUuidUtils.generateProspectUuid())
        .expects()
        .returning("prs_aa_qwerty")

      (() => srUuidUtils.generateProspectUuid())
        .expects()
        .returning("prs_aa_asdfgh")

      (prospectDAOService.insertNewProspects)
        .expects(*, *, *, *, *, *, *, true, List(), ownerId, ownerId, teamId, TriggerSource.OTHER, *, *, *)
        .returning(Success(List(insertedProspectResult.copy(1L, Some("prs_aa_qwerty"), false, ownerId), insertedProspectResult.copy(2L, Some("prs_aa_asdfgh"), false, ownerId))))

      (prospectsEmailsDAO.__createFromUploadMultiRowInsertV2_New)
        .expects(*, *, *, teamId)
        .returning(Success(List(newlyCreatedProspect)))
      (mqDomainServiceProviderDNSService.publish)
        .expects("email.com")
        .returning(Success({}))
      (mergeTagService._removeMissingErrorMessage)
        .expects(List(1L))
        .returning(1)

      (mqTrigger.publishEvents)
        .expects(*)
        .returning(())

      (mqAssociateProspectToOldEmails.publish)
        .expects(*)
        .returning(Success(()))

      (mqTrigger.publishEvents)
        .expects(*)
        .returning(())

      for (a <- 1 until 3) {
        (() => srUuidUtils.generateTagsUuid())
          .expects()
          .returning("tag_abcd" + s"$a")
      }

      (tagService.updateTagsForProspects(_: AddOrRemoveTagAction.Value, _: Seq[Long], _: Seq[TagAndUuid], _: Long, _: Long)(using _: SRLogger))
        .expects(AddOrRemoveTagAction.ADD, *, *, teamId, ownerId, *)
        .returning(Success(1))

//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//        _: TeamId,
//        _: SrRollingUpdateFeature
//      )(_: ISRLogger))
//        .expects(TeamId(teamId), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .twice()
//        .returning(false)

      val res = prospectService.createOrUpdateProspectsBatch(
        ignore_prospects = None,
        data = prospectBatchCreateFormData,
        teamAccount = teamMember,
        source = None,
        campaign_id = None,
        doer = accountAdmin,
        auditRequestLogId = Some("auditRequestLogId")
      )
      assert(res.isRight)
    }

    it("should fail if the prospect source is invalid") {

      val res = prospectService.createOrUpdateProspectsBatch(
        ignore_prospects = None,
        data = prospectBatchCreateFormData,
        teamAccount = teamMember,
        source = Some("TEST"),
        campaign_id = None,
        doer = accountAdmin,
        auditRequestLogId = Some("auditRequestLogId")
      )
      assert(res == Left(CreateOrUpdateProspectBatchError.InvalidProspectSource(s"Invalid Source: ${Some("TEST")}")))
    }

    it("should fail if the prospects list is empty") {

      val res = prospectService.createOrUpdateProspectsBatch(
        ignore_prospects = None,
        data = prospectBatchCreateFormData.copy(prospects = Seq()),
        teamAccount = teamMember,
        source = Some(ProspectSource.ZOHO.toString),
        campaign_id = None,
        doer = accountAdmin,
        auditRequestLogId = Some("auditRequestLogId")
      )
      assert(res == Left(CreateOrUpdateProspectBatchError.ProspectListEmpty("Please send a list of prospects. Empty request found.")))
    }

    it("should fail if the prospect email is invalid") {

      val res = prospectService.createOrUpdateProspectsBatch(
        ignore_prospects = None,
        data = prospectBatchCreateFormData.copy(prospects = Seq(prospectBatchCreateFormData.prospects.head.copy(email = Some("prospect_invalid_email")))),
        teamAccount = teamMember,
        source = None,
        campaign_id = None,
        doer = accountAdmin,
        auditRequestLogId = Some("auditRequestLogId")
      )
      assert(res.isLeft)
    }

    it("should fail if there are more than one owner") {

      val res = prospectService.createOrUpdateProspectsBatch(
        ignore_prospects = None,
        data = prospectBatchCreateFormData.copy(prospects = Seq(prospectBatchCreateFormData.prospects.head.copy(owner_id = Some(222L)), prospectBatchCreateFormData.prospects.head.copy(owner_id = Some(211L)))),
        teamAccount = teamMember,
        source = None,
        campaign_id = None,
        doer = accountAdmin,
        auditRequestLogId = Some("auditRequestLogId")
      )
      assert(res == Left(CreateOrUpdateProspectBatchError.MoreThanOneOwnerInvolved("In a single request, Prospects can be added to one specific Owner. Send multiple requests to add prospects to different owners.")))
    }

    it("should fail if there are more than one lists") {

      val res = prospectService.createOrUpdateProspectsBatch(
        ignore_prospects = None,
        data = prospectBatchCreateFormData.copy(prospects = Seq(prospectBatchCreateFormData.prospects.head.copy(list = Some("list1")), prospectBatchCreateFormData.prospects.head.copy(list = Some("list2")))),
        teamAccount = teamMember,
        source = None,
        campaign_id = None,
        doer = accountAdmin,
        auditRequestLogId = Some("auditRequestLogId")
      )
      assert(res == Left(CreateOrUpdateProspectBatchError.FoundMultipleProspectsList("In a single request, Prospects can be added to one specific List. Send multiple requests to add prospects to different Lists.")))

    }

    it("should fail if there is BadRequestErrorException") {

//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//        _: TeamId,
//        _: SrRollingUpdateFeature
//      )(_: ISRLogger))
//        .expects(TeamId(teamId), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .returning(false)

      val res = prospectService.createOrUpdateProspectsBatch(
        ignore_prospects = None,
        data = prospectBatchCreateFormData.copy(prospects = Seq(prospectBatchCreateFormData.prospects.head.copy(owner_id = Some(111L)))),
        teamAccount = teamMember,
        source = None,
        campaign_id = None,
        doer = accountAdmin,
        auditRequestLogId = Some("auditRequestLogId")
      )

      assert(res == Left(CreateOrUpdateProspectBatchError.BadRequestError("You do not have permission to edit prospects for the selected owner")))

    }

    it("should fail if there is DB error") {

      (accountService.findNewOwnerTeamMember(_: TeamId, _: AccountId))
        .expects(TeamId(id = teamId), AccountId(id = ownerId))
        .returning(Success(Some(teamMemberBasic)))

      (emailSettingDAO.getAllEmailSettingsInTeam)
        .expects(teamId)
        .returning(Success(email_settings))

      (accountService.getEmailsOfTeamMembers)
        .expects(TeamId(id = teamId))
        .returning(Success(emails_in_team))

      (accountService.getEmailOfOrgOwner)
        .expects(OrgId(id = org.id))
        .returning(Success("<EMAIL>"))

      (prospectColumnDef.findCustomColumns)
        .expects(teamId)
        .returning(Seq())

      (prospectDAOService.getProspectCategoryId(_: TeamId, _: ProspectCategory.Value, _: Option[Account])(using _:SRLogger))
        .expects(TeamId(id = teamId), ProspectCategory.DO_NOT_CONTACT, *, *)
        .returning(Success(ProspectCategoryId(1L)))

      (prospectDAOService.getProspectCategoryId(_: TeamId, _: ProspectCategory.Value, _: Option[Account])(using _:SRLogger))
        .expects(TeamId(id = teamId), ProspectCategory.NOT_CATEGORIZED, *, *)
        .returning(Success(ProspectCategoryId(2L)))

      (prospectDAOService.findOrCreateList)
        .expects(None, 2, permittedAccountIds, teamId)
        .returning(Some(1L))

      (prospectAccountDAO1.findOrCreateProspectAccount)
        .expects(Seq(prospectAccountCreateFormData.copy(name = "smartreach.io")), ownerId, permittedAccountIds, teamId, *)
        .returning(prospectAccountCreateOrUpdateResult)

      (blacklistProspectCheckDAO.findByEmailsAndDomains)
        .expects(*, teamId, OrgId(id = org.id), *, *)
        .returning(Success(Seq()))

      (prospectDAOService.findDuplicateProspectsForForceUpdateV2(_: Seq[Seq[DuplicationFindProspectDataV2]], _: TeamId)(using _:SRLogger))
        .expects(*, TeamId(teamId), *)
        .returning(Success(List()))


      (prospectDAOService.updateProspectsForEmailOptional(_: Boolean, _: Seq[ColumnDef], _: Option[Long], _: Iterable[ProspectsToBeForceUpdated], _: Seq[SrProspectColumns], _: List[ProspectEmailsTobeAddedInForceUpdateProspect], _: Long)(using _: SRLogger))
        .expects(true, List(), *, *, *, *, teamId, *)
        .returning(Success(List(updateProspectResult.copy(1L, Some("prs_aa_abcdef"), true, ownerId))))

      (() => srUuidUtils.generateProspectUuid())
        .expects()
        .returning("prs_aa_qwerty")

      (() => srUuidUtils.generateProspectUuid())
        .expects()
        .returning("prs_aa_asdfgh")

//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//        _: TeamId,
//        _: SrRollingUpdateFeature
//      )(_: ISRLogger))
//        .expects(TeamId(teamId), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .twice()
//        .returning(false)

      (prospectDAOService.insertNewProspects)
        .expects(*, *, *, *, *, *, *, true, List(), ownerId, ownerId, teamId, TriggerSource.OTHER, *, *, *)
        .returning(Success(List(insertedProspectResult.copy(1L, Some("prs_aa_qwerty"), false, ownerId), insertedProspectResult.copy(2L, Some("prs_aa_asdfgh"), false, ownerId))))

      (prospectsEmailsDAO.__createFromUploadMultiRowInsertV2_New)
        .expects(*, *, *, teamId)
        .returning(Failure(new Throwable("Error while adding prospect")))

      val res = prospectService.createOrUpdateProspectsBatch(
        ignore_prospects = None,
        data = prospectBatchCreateFormData,
        teamAccount = teamMember,
        source = None,
        campaign_id = None,
        doer = accountAdmin,
        auditRequestLogId = Some("auditRequestLogId")
      )

      assert(res.isLeft)

    }


  }

  describe("ProspectService.constructProspectDataForApi") {
    it("should return list of UpsertSQLProspectData successFully if only inserted data is passed") {

      val newly_created_prospect: List[NewlyCreatedProspect] = (101 to 250).zipWithIndex.map {
        case (_, index) =>
          newlyCreatedProspect.copy(
            prospect_id = index + 101,
            email = s"prospectemail${index + 101}"
          )
      }.toList

      val new_prospect_data: List[InsertOrUpdateProspectResult] = (101 to 250).zipWithIndex.map {
        case (_, index) =>
          insertedProspectResult.copy(
            prospect_id = index + 101
          )
      }.toList


      val res: Try[List[UpsertSQLProspectData]] = ProspectService.constructProspectDataForApi(
        prospect_inserted_emails = newly_created_prospect,
        prospect_inserted_data = new_prospect_data,
        prospect_updated_emails = List(),
        prospect_updated_data = List()
      )

      val expectedResult = (101 to 250).zipWithIndex.map {
        case (_, index) =>
          upsertSQLProspectData.copy(
            prospect_email = s"prospectemail${index + 101}",
            prospect_data = insertedProspectResult.copy(
              prospect_id = index + 101
            )
          )
      }.toList

      res match {
        case Failure(_) => assert(false)
        case Success(res) => assert(res == expectedResult)
      }
    }

    it("should return list of UpsertSQLProspectData successFully if only updated data is passed") {

      val updated_emails: List[ProspectsToBeForceUpdated] = (101 to 250).zipWithIndex.map {
        case (_, index) =>
          ProspectsToBeForceUpdated(prospectCreateFormdata.copy(
            email = Some(s"prospectemail${index + 101}")
          ), index + 101L, Seq(SrProspectColumns.Email))
      }.toList

      val updated_data: List[InsertOrUpdateProspectResult] = (101 to 250).zipWithIndex.map {
        case (_, index) =>
          updateProspectResult.copy(
            prospect_id = index + 101,
          )
      }.toList


      val res = ProspectService.constructProspectDataForApi(
        prospect_inserted_emails = List(),
        prospect_inserted_data = List(),
        prospect_updated_emails = updated_emails,
        prospect_updated_data = updated_data
      )

      val expectedResult = (101 to 250).zipWithIndex.map {
        case (_, index) =>
          upsertSQLProspectData.copy(
            prospect_email = s"prospectemail${index + 101}",
            prospect_data = insertedProspectResult.copy(
              prospect_id = index + 101
            )
          )
      }.toList

      res match {
        case Failure(_) => assert(false)
        case Success(res) => assert(res == expectedResult)
      }
    }

    it("should return list of UpsertSQLProspectData successFully if inserted and updated data is passed") {

      val newly_created_prospect: List[NewlyCreatedProspect] = (101 to 150).zipWithIndex.map {
        case (_, index) =>
          newlyCreatedProspect.copy(
            prospect_id = index + 101,
            email = s"prospectemail${index + 101}"
          )
      }.toList

      val new_prospect_data: List[InsertOrUpdateProspectResult] = (101 to 150).zipWithIndex.map {
        case (_, index) =>
          insertedProspectResult.copy(
            prospect_id = index + 101
          )
      }.toList

      val updated_emails: List[ProspectsToBeForceUpdated] = (151 to 200).zipWithIndex.map {
        case (_, index) =>
          ProspectsToBeForceUpdated(prospectCreateFormdata.copy(
            email = Some(s"prospectemail${index + 151}")
          ),
            index + 151L, Seq(SrProspectColumns.Email))
      }.toList

      val updated_data: List[InsertOrUpdateProspectResult] = (151 to 200).zipWithIndex.map {
        case (_, index) =>
          updateProspectResult.copy(
            prospect_id = index + 151,
          )
      }.toList


      val res = ProspectService.constructProspectDataForApi(
        prospect_inserted_emails = newly_created_prospect,
        prospect_inserted_data = new_prospect_data,
        prospect_updated_emails = updated_emails,
        prospect_updated_data = updated_data
      )

      val expectedResult_1 = (101 to 150).zipWithIndex.map {
        case (_, index) =>
          upsertSQLProspectData.copy(
            prospect_email = s"prospectemail${index + 101}",
            prospect_data = insertedProspectResult.copy(
              prospect_id = index + 101
            )
          )
      }.toList

      val expectedResult_2 = (151 to 200).zipWithIndex.map {
        case (_, index) =>
          upsertSQLProspectData.copy(
            prospect_email = s"prospectemail${index + 151}",
            prospect_data = updateProspectResult.copy(
              prospect_id = index + 151
            )
          )
      }.toList

      res match {
        case Failure(_) => assert(false)
        case Success(res) => assert(res == expectedResult_2 ++ expectedResult_1)
      }
    }

    //    it("should fail if newly_created_prospect is empty but new_prospect_data is non empty") {
    //
    //      val newly_created_prospect: List[NewlyCreatedProspect] = (101 to 150).zipWithIndex.map {
    //        case (_, index) =>
    //          newlyCreatedProspect.copy(
    //            prospect_id = index + 101,
    //            email = s"prospectemail${index + 101}"
    //          )
    //      }.toList
    //
    //      val new_prospect_data: List[InsertOrUpdateProspectResult] = (101 to 150).zipWithIndex.map {
    //        case (_, index) =>
    //          insertedProspectResult.copy(
    //            prospect_id = index + 101
    //          )
    //      }.toList
    //
    //      val res = ProspectService.constructProspectDataForApi(
    //        prospect_inserted_emails = List(),
    //        prospect_inserted_data = new_prospect_data,
    //        prospect_updated_emails = List(),
    //        prospect_updated_data = List()
    //      )
    //
    //      assert(res.isFailure)
    //    }
  }

  describe("ProspectService.getDuplicateProspectEmails") {
    it("should return duplicate prospect emails if any") {
      val result: List[String] = ProspectService.getDuplicateProspectEmails(
        prospect_emails = Seq("<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>"
        )
      )

      assert(result == Seq("<EMAIL>", "<EMAIL>"))
    }

    it("should return empty list if no duplicate prospect emails found") {
      val result: List[String] = ProspectService.getDuplicateProspectEmails(
        prospect_emails = Seq("<EMAIL>",
          "<EMAIL>",
          "<EMAIL>"
        )
      )

      assert(result == Seq())
    }

    it("should return empty list if no any prospect emails passed") {
      val result: List[String] = ProspectService.getDuplicateProspectEmails(
        prospect_emails = Seq()
      )

      assert(result == Seq())
    }
  }

  describe("ProspectService.markProspectsAsInvalidForApi") {
    it("should return successfully return prospect_ids of invalid prospect emails") {

      (prospectServiceV2.markAsInvalid(_: Long, _: Long)(using _: SRLogger))
        .expects(901L, teamId, *)
        .returning(Success(1))

      (prospectServiceV2.markAsInvalid(_: Long, _: Long)(using _: SRLogger))
        .expects(902L, teamId, *)
        .returning(Success(1))

      val result: Try[Seq[Option[ProspectUuid]]] = prospectService.markProspectsAsInvalidForApi(
        data = Seq(
          dummyProspectObj.copy(email = Some("<EMAIL>")),
          dummyProspectObj.copy(email = Some("ppppp"), id = 901L, prospect_uuid = Some(ProspectUuid("prs_aa_aaaa"))),
          dummyProspectObj.copy(email = Some("prospect.io"), id = 902L, prospect_uuid = Some(ProspectUuid("prs_aa_bbbbb")))
        ),
        team_id = teamId
      )
      result match {
        case Failure(_) => assert(false)
        case Success(value) => assert(value == Seq(Some(ProspectUuid("prs_aa_aaaa")), Some(ProspectUuid("prs_aa_bbbbb"))))
      }
    }

    it("should return successfully return empty list of prospect_ids if no any invalid found ") {

      val result: Try[Seq[Option[ProspectUuid]]] = prospectService.markProspectsAsInvalidForApi(
        data = List(dummyProspectObj.copy(email = Some("<EMAIL>")),
          dummyProspectObj.copy(email = Some("<EMAIL>"), id = 901L, prospect_uuid = Some(ProspectUuid("prs_aa_aaaa"))),
          dummyProspectObj.copy(email = Some("<EMAIL>"), id = 902L, prospect_uuid = Some(ProspectUuid("prs_aa_bbbbb")))
        ),
        team_id = teamId
      )

      result match {
        case Failure(_) => assert(false)
        case Success(value) => assert(value == Seq())
      }
    }

    it("should return failure if dao call fails") {

      (prospectServiceV2.markAsInvalid(_: Long, _: Long)(using _: SRLogger))
        .expects(901L, teamId, *)
        .returning(Failure(new Exception("Error while marking invalid")))

      val result: Try[Seq[Option[ProspectUuid]]] = prospectService.markProspectsAsInvalidForApi(
        data = List(dummyProspectObj.copy(email = Some("<EMAIL>")),
          dummyProspectObj.copy(email = Some("sale.com"), id = 901L, prospect_uuid = Some(ProspectUuid("prs_aa_aaaa"))),
          dummyProspectObj.copy(email = Some("<EMAIL>"), id = 902L, prospect_uuid = Some(ProspectUuid("prs_aa_bbbbb")))
        ),
        team_id = teamId
      )

      assert(result.isFailure)
    }
  }

  describe("ProspectService.getProspectListWithoutInternalEmails") {
    it("should return only prospects_without_internal_emails if no email match") {

      (emailSettingDAO.getAllEmailSettingsInTeam)
        .expects(teamId)
        .returning(Success(List("<EMAIL>")))

      (accountService.getEmailsOfTeamMembers)
        .expects(TeamId(teamId))
        .returning(Success(List("<EMAIL>")))

      (accountService.getEmailOfOrgOwner)
        .expects(OrgId(accountAdmin.org.id))
        .returning(Success("<EMAIL>"))

      val result = prospectService.getProspectListWithoutInternalEmails(
        teamId = teamId,
        org_id = accountAdmin.org.id,
        Logger = Logger,
        prospectsWithValidEmailFormat = Seq(
          prospectCreateFormdata.copy(email = Some("<EMAIL>")),
          prospectCreateFormdata2.copy(email = Some("<EMAIL>")))
      )

      result match {
        case Failure(_) => assert(false)
        case Success(res) => assert(res == ProspectInternalEmails(List(), List(
          prospectCreateFormdata.copy(email = Some("<EMAIL>")),
          prospectCreateFormdata2.copy(email = Some("<EMAIL>"))
        )))
      }

    }

    it("should return only prospects_with_internal_emails if all emails match") {

      (emailSettingDAO.getAllEmailSettingsInTeam)
        .expects(teamId)
        .returning(Success(List("<EMAIL>")))

      (accountService.getEmailsOfTeamMembers)
        .expects(TeamId(teamId))
        .returning(Success(List("<EMAIL>")))

      (accountService.getEmailOfOrgOwner)
        .expects(OrgId(accountAdmin.org.id))
        .returning(Success("<EMAIL>"))

      val result = prospectService.getProspectListWithoutInternalEmails(
        teamId = teamId,
        org_id = accountAdmin.org.id,
        Logger = Logger,
        prospectsWithValidEmailFormat = Seq(
          prospectCreateFormdata.copy(email = Some("<EMAIL>")),
          prospectCreateFormdata2.copy(email = Some("<EMAIL>")))
      )

      result match {
        case Failure(_) => assert(false)
        case Success(res) => assert(res == ProspectInternalEmails(List(
          prospectCreateFormdata.copy(email = Some("<EMAIL>")),
          prospectCreateFormdata2.copy(email = Some("<EMAIL>"))
        ), List()))
      }

    }

    it("should return only prospects_with_internal_emails if all domains match") {

      (emailSettingDAO.getAllEmailSettingsInTeam)
        .expects(teamId)
        .returning(Success(List("<EMAIL>")))

      (accountService.getEmailsOfTeamMembers)
        .expects(TeamId(teamId))
        .returning(Success(List("<EMAIL>")))

      (accountService.getEmailOfOrgOwner)
        .expects(OrgId(accountAdmin.org.id))
        .returning(Success("<EMAIL>"))

      val result = prospectService.getProspectListWithoutInternalEmails(
        teamId = teamId,
        org_id = accountAdmin.org.id,
        Logger = Logger,
        prospectsWithValidEmailFormat = Seq(
          prospectCreateFormdata.copy(email = Some("<EMAIL>")),
          prospectCreateFormdata2.copy(email = Some("<EMAIL>")))
      )

      result match {
        case Failure(_) => assert(false)
        case Success(res) => assert(res == ProspectInternalEmails(List(
          prospectCreateFormdata.copy(email = Some("<EMAIL>")),
          prospectCreateFormdata2.copy(email = Some("<EMAIL>"))
        ), List()))
      }

    }

    it("should return both prospects_without_internal_emails and prospects_with_internal_emails if some of the emails match") {

      (emailSettingDAO.getAllEmailSettingsInTeam)
        .expects(teamId)
        .returning(Success(List("<EMAIL>")))

      (accountService.getEmailsOfTeamMembers)
        .expects(TeamId(teamId))
        .returning(Success(List("<EMAIL>")))

      (accountService.getEmailOfOrgOwner)
        .expects(OrgId(accountAdmin.org.id))
        .returning(Success("<EMAIL>"))

      val result = prospectService.getProspectListWithoutInternalEmails(
        teamId = teamId,
        org_id = accountAdmin.org.id,
        Logger = Logger,
        prospectsWithValidEmailFormat = Seq(
          prospectCreateFormdata.copy(email = Some("<EMAIL>")),
          prospectCreateFormdata2.copy(email = Some("<EMAIL>")))
      )

      result match {
        case Failure(_) => assert(false)
        case Success(res) => assert(res == ProspectInternalEmails(List(
          prospectCreateFormdata2.copy(email = Some("<EMAIL>"))
        ), List(
          prospectCreateFormdata.copy(email = Some("<EMAIL>"))
        )))
      }

    }

    it("should return failure if exception is thrown") {

      (emailSettingDAO.getAllEmailSettingsInTeam)
        .expects(teamId)
        .returning(Failure(new Exception("DB Failure")))

      val result = prospectService.getProspectListWithoutInternalEmails(
        teamId = teamId,
        org_id = accountAdmin.org.id,
        Logger = Logger,
        prospectsWithValidEmailFormat = Seq(
          prospectCreateFormdata.copy(email = Some("<EMAIL>")),
          prospectCreateFormdata2.copy(email = Some("<EMAIL>")))
      )

      assert(result.isFailure)
    }

  }

  val prospectCreateFormDataV2 = ProspectCreateFormDataV2(
    email = Some(email),
    first_name = Some(first_name),
    last_name = Some(last_name),
    custom_fields = Some(Json.obj("followers" -> 500)),

    // should not break in old ui/integrations, currently used only inside createOrUpdateOne controller
    owner_id = None,

    list = Some("list"),
    company = Some(company),
    city = Some("kolkata"),
    country = Some("India"),
    timezone = Some("Asia/Kolkata"),
    created_at = None,

    state = None,
    job_title = None,
    phone_number = None,
    linkedin_url = None,

    tags = Some(Seq("tag1"))
  )
  describe("ProspectService.createProspectsAndTagsSeq") {
    it("should return empty prospect and tags if createdOrUpdatedProspectsData is empty") {
      val res = prospectService.createProspectsAndTagsSeq(
        prospectCreateFormDataV2 = Seq(prospectCreateFormDataV2),
        createdOrUpdatedProspectsData = List()
      )

      assert(res.isEmpty)
    }

    it("should return empty prospect and tags if prospectCreateFormDataV2 is not having tags") {
      val res = prospectService.createProspectsAndTagsSeq(
        prospectCreateFormDataV2 = Seq(prospectCreateFormDataV2.copy(tags = None)),
        createdOrUpdatedProspectsData = List(upsertSQLProspectData.copy(prospect_email = email))
      )

      assert(res.isEmpty)
    }

    it("should return prospect and tags successfully if tags defined") {

      (() => srUuidUtils.generateTagsUuid())
        .expects()
        .returning("tags_abcdefgh")

      (() => srUuidUtils.generateTagsUuid())
        .expects()
        .returning("tags_piiojkhhbg")

      val res: List[ProspectsAndTags] = prospectService.createProspectsAndTagsSeq(
        prospectCreateFormDataV2 = Seq(
          prospectCreateFormDataV2,
          prospectCreateFormDataV2.copy(email = Some("<EMAIL>"), tags = Some(Seq("tag4")))
        ),
        createdOrUpdatedProspectsData = List(
          upsertSQLProspectData.copy(prospect_email = email),
          upsertSQLProspectData.copy(
            prospect_email = "<EMAIL>",
            prospect_data = upsertSQLProspectData.prospect_data.copy(prospect_id = 190L, prospect_uuid = Some("prs_ksjdsdjskds"))
          )
        )
      )

      val expected_res = List(ProspectsAndTags(
        prospect_id = ProspectId(upsertSQLProspectData.prospect_data.prospect_id),
        tags = Seq(TagAndUuid(
          tag = "tag1", uuid = ProspectTagUuid("tags_abcdefgh")
        ))
      ), ProspectsAndTags(
        prospect_id = ProspectId(190L),
        tags = Seq(TagAndUuid(
          tag = "tag4", uuid = ProspectTagUuid("tags_piiojkhhbg")
        ))
      ))
      assert(res == expected_res)
    }
  }


  val prospectCreateFormDataNewPath: ProspectDeduplicationColumnsData = ProspectDeduplicationColumnsData(
    email = None,
    first_name = None,
    last_name = None,
    company = None,
    phone = None,
    linkedin_url = None
  )

  describe("ProspectService.validateAllCriteriaForProspect") {
    it("should return false if all of them email/phone/linkedin/(company, firstname, lastname) are empty") {
      val res: Boolean = ProspectService.areAnyOfTheDeduplicationColumnsPresentForProspect(
        prospect = prospectCreateFormDataNewPath
      )

      assert(!res)
    }

    it("should return false if all of them email/phone/linkedin are none and among (company, firstname, lastname) only company is present") {
      val res: Boolean = ProspectService.areAnyOfTheDeduplicationColumnsPresentForProspect(
        prospect = prospectCreateFormDataNewPath.copy(company = Some("NewCompany"))
      )

      assert(!res)
    }

    it("should return false if all of them email/phone/linkedin are none and among (company, firstname, lastname) only last_name is present") {
      val res: Boolean = ProspectService.areAnyOfTheDeduplicationColumnsPresentForProspect(
        prospect = prospectCreateFormDataNewPath.copy(last_name = Some("Doe"))
      )

      assert(!res)
    }

    it("should return true if all of them email/phone/linkedin are none and all (company, firstname, lastname) are present") {
      val res: Boolean = ProspectService.areAnyOfTheDeduplicationColumnsPresentForProspect(
        prospect = prospectCreateFormDataNewPath.copy(
          company = Some("SmartReach"),
          first_name = Some("Prachi"),
          last_name = Some("Mane")
        )
      )

      assert(res)
    }

    it("should return true if any of them email/phone/linkedin/(company, firstname, lastname) are present") {
      val res: Boolean = ProspectService.areAnyOfTheDeduplicationColumnsPresentForProspect(
        prospect = prospectCreateFormDataNewPath.copy(
          email = Some("<EMAIL>")
        ))

      assert(res)
    }

    it("should return false if only email present but format is invalid") {
      val res: Boolean = ProspectService.areAnyOfTheDeduplicationColumnsPresentForProspect(
        prospect = prospectCreateFormDataNewPath.copy(
          email = Some("prachismartreach.io")
        ))

      assert(!res)
    }

    it("should return false if only phone from email/phone/linkedin/(company, firstname, lastname) is present but empty string") {
      val res: Boolean = ProspectService.areAnyOfTheDeduplicationColumnsPresentForProspect(
        prospect = prospectCreateFormDataNewPath.copy(
          phone = Some("")
        )
      )

      assert(!res)
    }

    it("should return false if only (company, firstname, lastname) from email/phone/linkedin/(company, firstname, lastname) is present but empty string") {
      val res: Boolean = ProspectService.areAnyOfTheDeduplicationColumnsPresentForProspect(
        prospect = prospectCreateFormDataNewPath.copy(
          company = Some(""),
          first_name = Some(""),
          last_name = Some("")
        )
      )

      assert(!res)
    }
  }

  val prospect_emails: Map[String, Long] = Map(
    "<EMAIL>" -> 101L,
    "<EMAIL>" -> 102L,
    "<EMAIL>" -> 103L
  )

  val prospect_phones: Map[String, Long] = Map(
    "+919999999901" -> 101L,
    "+919999999902" -> 102L,
    "+919999999903" -> 103L,
    "+919999999904" -> 104L,
    "+919999999905" -> 105L,
    "+919999999906" -> 106L
  )

  val prospect_linkedin: Map[String, Long] = Map(
    "linkedin.com/prospect1" -> 101L,
    "linkedin.com/prospect2" -> 102L,
    "linkedin.com/prospect3" -> 103L,
    "linkedin.com/prospect4" -> 104L,
    "linkedin.com/prospect5" -> 105L,
    "linkedin.com/prospect6" -> 106L
  )

  val prospect_cfl: Map[String, Long] = Map(
    "one.ioprospect1last1" -> 101L,
    "two.ioprospect2last2" -> 102L,
    "three.ioprospect3last3" -> 103L,
    "four.ioprospect4last4" -> 104L,
    "five.ioprospect5last5" -> 105L
  )


  describe("ProspectService.checkIfFormDataMatchingDb") {

    it("Should return list of right if no any rows from ProspectCreateFormData have data from two different prospects in db") {

      val prospects_input: List[ProspectCreateFormData] = List(
        prospectCreateFormdata.copy(
          email = Some("<EMAIL>"),
          linkedin_url = Some("linkedin.com/prospect2"),
          phone = Some("+919999999902"),
          company = Some("two.io"),
          first_name = Some("prospect2"),
          last_name = Some("last2")
        ),
        prospectCreateFormdata.copy(
          email = Some("<EMAIL>"),
          linkedin_url = Some("linkedin.com/prospect3"),
          phone = Some("+919999999903")
        )
      )

      val res: List[Either[ListOfProspectsWithDataFromDifferentRowsInDb, ProspectCreateFormData]] = ProspectService.checkIfFormDataMatchingDb(
        prospect_emails = prospect_emails,
        prospect_phones = prospect_phones,
        prospect_linkedin = prospect_linkedin,
        prospect_cfl = prospect_cfl,
        prospects_input = prospects_input
      )

      val resLength = res.count(_.isRight)

      assert(resLength == 2)
    }

    it("Should return list of right if there are any rows with email and company are having same company in input but different firstName and lastName") {

      val prospects_input: List[ProspectCreateFormData] = List(
        prospectCreateFormdata.copy(email = Some("<EMAIL>")),
        prospectCreateFormdata.copy(
          email = Some("<EMAIL>"),
          company = Some("two.io"),
          first_name = Some("Prospect2"),
          last_name = Some("Last2")
        ),
        prospectCreateFormdata.copy(
          email = Some("<EMAIL>"),
          company = Some("two.io"),
          first_name = Some("Prospect3"),
          last_name = Some("Last3")
        )
      )

      val res: List[Either[ListOfProspectsWithDataFromDifferentRowsInDb, ProspectCreateFormData]] = ProspectService.checkIfFormDataMatchingDb(
        prospect_emails = prospect_emails,
        prospect_phones = prospect_phones,
        prospect_linkedin = prospect_linkedin,
        prospect_cfl = prospect_cfl,
        prospects_input = prospects_input
      )

      val resLength = res.count(_.isRight)

      assert(resLength == 3)
    }


    it("should return specific left and right if there are any rows with email and phone are from two different prospects in db") {

      val prospects_input: List[ProspectCreateFormData] = List(
        prospectCreateFormdata.copy(email = Some("<EMAIL>")),
        prospectCreateFormdata.copy(
          email = Some("<EMAIL>"),
          phone = Some("+919999999905")
        ),
        prospectCreateFormdata.copy(
          email = Some("<EMAIL>"),
          phone = Some("+919999999906")
        )
      )

      val res: List[Either[ListOfProspectsWithDataFromDifferentRowsInDb, ProspectCreateFormData]] = ProspectService.checkIfFormDataMatchingDb(
        prospect_emails = prospect_emails,
        prospect_phones = prospect_phones,
        prospect_linkedin = prospect_linkedin,
        prospect_cfl = prospect_cfl,
        prospects_input = prospects_input
      )

      val resRightLength = res.count(_.isRight)
      val resLeftLength = res.count(_.isLeft)

      assert(resLeftLength == 2 && resRightLength == 1)

    }

    it("should return specific left and right if there are any rows with email and linkedin are from two different prospects in db") {

      val prospects_input: List[ProspectCreateFormData] = List(
        prospectCreateFormdata.copy(email = Some("<EMAIL>")),
        prospectCreateFormdata.copy(
          email = Some("<EMAIL>"),
          linkedin_url = Some("linkedin.com/prospect6")
        ),
        prospectCreateFormdata.copy(
          email = Some("<EMAIL>"),
          linkedin_url = Some("linkedin.com/prospect1")
        )
      )

      val res: List[Either[ListOfProspectsWithDataFromDifferentRowsInDb, ProspectCreateFormData]] = ProspectService.checkIfFormDataMatchingDb(
        prospect_emails = prospect_emails,
        prospect_phones = prospect_phones,
        prospect_linkedin = prospect_linkedin,
        prospect_cfl = prospect_cfl,
        prospects_input = prospects_input
      )

      val resRightLength = res.count(_.isRight)
      val resLeftLength = res.count(_.isLeft)

      assert(resLeftLength == 2 && resRightLength == 1)
    }


    it("should return specific left and right if there are any rows with email and company,firstname,lastname are from two different prospects in db") {

      val prospects_input: List[ProspectCreateFormData] = List(
        prospectCreateFormdata.copy(email = Some("<EMAIL>")),
        prospectCreateFormdata.copy(email = Some("<EMAIL>")),
        prospectCreateFormdata.copy(
          email = Some("<EMAIL>"),
          company = Some("one.io"),
          first_name = Some("Prospect1"),
          last_name = Some("Last1")
        )
      )

      val res: List[Either[ListOfProspectsWithDataFromDifferentRowsInDb, ProspectCreateFormData]] = ProspectService.checkIfFormDataMatchingDb(
        prospect_emails = prospect_emails,
        prospect_phones = prospect_phones,
        prospect_linkedin = prospect_linkedin,
        prospect_cfl = prospect_cfl,
        prospects_input = prospects_input
      )

      val resRightLength = res.count(_.isRight)
      val resLeftLength = res.count(_.isLeft)

      assert(resLeftLength == 1 && resRightLength == 2)
    }

    it("should return specific left and right if there are any rows with (linkedin and phone) or (linkedin and cfl) are from two different prospects in db") {

      val prospects_input: List[ProspectCreateFormData] = List(
        prospectCreateFormdata.copy(email = Some("<EMAIL>")),
        prospectCreateFormdata.copy(
          linkedin_url = Some("linkedin.com/prospect1"),
          phone = Some("+919999999902")
        ),
        prospectCreateFormdata.copy(
          linkedin_url = Some("linkedin.com/prospect4"),
          company = Some("three.io"),
          first_name = Some("Prospect3"),
          last_name = Some("Last3"))
      )

      val res: List[Either[ListOfProspectsWithDataFromDifferentRowsInDb, ProspectCreateFormData]] = ProspectService.checkIfFormDataMatchingDb(
        prospect_emails = prospect_emails,
        prospect_phones = prospect_phones,
        prospect_linkedin = prospect_linkedin,
        prospect_cfl = prospect_cfl,
        prospects_input = prospects_input
      )

      val resRightLength = res.count(_.isRight)
      val resLeftLength = res.count(_.isLeft)

      assert(resLeftLength == 2 && resRightLength == 1)
    }

    it("should return specific left and right if there are any rows with phone and company_firstname_lastname are from two different prospects in db") {

      val prospects_input: List[ProspectCreateFormData] = List(
        prospectCreateFormdata.copy(email = Some("<EMAIL>")),
        prospectCreateFormdata.copy(
          phone = Some("+919999999902"),
          company = Some("three.io"),
          first_name = Some("Prospect3"),
          last_name = Some("Last3"))
      )

      val res: List[Either[ListOfProspectsWithDataFromDifferentRowsInDb, ProspectCreateFormData]] = ProspectService.checkIfFormDataMatchingDb(
        prospect_emails = prospect_emails,
        prospect_phones = prospect_phones,
        prospect_linkedin = prospect_linkedin,
        prospect_cfl = prospect_cfl,
        prospects_input = prospects_input
      )

      val resRightLength = res.count(_.isRight)
      val resLeftLength = res.count(_.isLeft)

      assert(resLeftLength == 1 && resRightLength == 1)
    }

    it("should return right if there are no any rows with duplicate data in db") {

      val prospects_input: List[ProspectCreateFormData] = List(
        prospectCreateFormdata.copy(email = Some("<EMAIL>")),
        prospectCreateFormdata.copy(
          phone = Some("+919999999911"),
          company = Some("eleven.io"),
          first_name = Some("Prospect11"),
          last_name = Some("Last11"))
      )

      val res: List[Either[ListOfProspectsWithDataFromDifferentRowsInDb, ProspectCreateFormData]] = ProspectService.checkIfFormDataMatchingDb(
        prospect_emails = prospect_emails,
        prospect_phones = prospect_phones,
        prospect_linkedin = prospect_linkedin,
        prospect_cfl = prospect_cfl,
        prospects_input = prospects_input
      )

      val resRightLength = res.count(_.isRight)
      val resLeftLength = res.count(_.isLeft)

      assert(resLeftLength == 0 && resRightLength == 2)
    }

  }

  describe("ProspectService.extractUniqueProspectsForMultiRowInsert") {
    it("should return unique email prospects only for old path") {

      val res: List[ProspectCreateFormData] = ProspectService.extractUniqueProspectsForMultiRowInsert(
        prospects = Seq(prospectCreateFormdata, prospectCreateFormdata, prospectCreateFormdata.copy(email = Some("<EMAIL>"))),
        orgId = org.id,
//        emailNotCompulsoryEnabled = false
      ).toList

      assert(res.length == 2)
    }


    //Email is defined for all prospects so finding unique based on email
    it("should return unique email prospects for new path if email is defined for all prospects") {

      val res: List[ProspectCreateFormData] = ProspectService.extractUniqueProspectsForMultiRowInsert(
        prospects = Seq(
          prospectCreateFormdata,
          prospectCreateFormdata,
          prospectCreateFormdata.copy(email = Some("<EMAIL>")),
          prospectCreateFormdata.copy(email = Some("<EMAIL>")),
          prospectCreateFormdata.copy(email = Some("<EMAIL>"))
        ),
        orgId = 70L,
//        emailNotCompulsoryEnabled = false
      ).toList

      assert(res.length == 3)
    }


    //Finding unique based on email where email is defined
    //If email is not defined then unique based on linkedin_url
    it("should return unique email and linkedin prospects for new path if email is undefined for some prospects where linkedin is defined") {

      val res: List[ProspectCreateFormData] = ProspectService.extractUniqueProspectsForMultiRowInsert(
        prospects = Seq(
          prospectCreateFormdata,
          prospectCreateFormdata,
          prospectCreateFormdata.copy(email = Some("<EMAIL>")),
          prospectCreateFormdata.copy(email = Some("<EMAIL>")),
          prospectCreateFormdata.copy(
            email = None,
            linkedin_url = Some("linkedin.com/sales")
          ),
          prospectCreateFormdata.copy(
            email = None,
            linkedin_url = Some("linkedin.com/sales")
          ),
          prospectCreateFormdata.copy(
            email = None,
            linkedin_url = Some("linkedin.com/marketing")
          )
        ),
        orgId = 70L,
//         //emailNotCompulsoryEnabled = true
      ).toList

      assert(res.length == 4)
      assert(res.count(_.linkedin_url.isDefined) == 2)
    }

    //Finding unique based on email where email is defined
    //If email is not defined then unique based on linkedin_url
    //If email and linkedin_url both are undefined then unique based on phone
    it("should return unique email, linkedin and phone prospects for new path if email and linkedin are undefined for some prospects where phone is defined") {

      val res: List[ProspectCreateFormData] = ProspectService.extractUniqueProspectsForMultiRowInsert(
        prospects = Seq(
          prospectCreateFormdata,
          prospectCreateFormdata,
          prospectCreateFormdata.copy(email = Some("<EMAIL>")),
          prospectCreateFormdata.copy(email = Some("<EMAIL>")),
          prospectCreateFormdata.copy(
            email = None,
            linkedin_url = Some("linkedin.com/sales")
          ),
          prospectCreateFormdata.copy(
            email = None,
            linkedin_url = Some("linkedin.com/sales")
          ),
          prospectCreateFormdata.copy(
            email = None,
            linkedin_url = Some("linkedin.com/marketing")
          ),
          prospectCreateFormdata.copy(
            email = None,
            linkedin_url = None,
            phone = Some("+912838485868")
          ),
          prospectCreateFormdata.copy(
            email = None,
            linkedin_url = None,
            phone = Some("+912838485868")
          )
        ),
        orgId = 70L,
//         //emailNotCompulsoryEnabled = true
      ).toList

      assert(res.length == 5)
      assert(res.count(_.phone.isDefined) == 1)
    }

    //Finding unique based on email where email is defined
    //If email is not defined then unique based on linkedin_url
    //If email and linkedin_url both are undefined then unique based on phone
    //If email, linkedin_url and phone are undefined then unique based on company-firstname-lastname
    it("should return unique email, linkedin, phone and company-firstname-lastname prospects for new path if email, linkedin and phone are undefined for some prospects where company-firstname-lastname is defined") {

      val res: List[ProspectCreateFormData] = ProspectService.extractUniqueProspectsForMultiRowInsert(
        prospects = Seq(
          prospectCreateFormdata,
          prospectCreateFormdata.copy(email = Some("<EMAIL>")),
          prospectCreateFormdata.copy(email = Some("<EMAIL>")),
          prospectCreateFormdata.copy(
            email = None,
            linkedin_url = Some("linkedin.com/sales")
          ),
          prospectCreateFormdata.copy(
            email = None,
            linkedin_url = None,
            phone = Some("+912838485868")
          ),
          prospectCreateFormdata.copy(
            email = None,
            linkedin_url = None,
            phone = None,
            company = Some("marketing"),
            first_name = Some("John"),
            last_name = Some("Doe")
          ),
          prospectCreateFormdata.copy(
            email = None,
            linkedin_url = None,
            phone = None,
            company = Some("marketing"),
            first_name = Some("John"),
            last_name = Some("Doe")
          )
        ),
        orgId = 70L,
//         //emailNotCompulsoryEnabled = true
      ).toList

      assert(res.length == 5)
    }

    //Finding unique based on email where email is defined
    //If email is not defined then unique based on linkedin_url
    //If email and linkedin_url both are undefined then unique based on phone
    //If email, linkedin_url and phone are undefined then unique based on company-firstname-lastname
    //But if firstname/lastname are none then it will not consider that row
    //Note: we are already having the criteria check for deduplication so this case don't arise
    it("should return unique email, linkedin and phone prospects for new path if email, linkedin and phone are undefined for some prospects where company-firstname-lastname is only having company defined") {

      val res: List[ProspectCreateFormData] = ProspectService.extractUniqueProspectsForMultiRowInsert(
        prospects = Seq(
          prospectCreateFormdata,
          prospectCreateFormdata.copy(email = Some("<EMAIL>")),
          prospectCreateFormdata.copy(email = Some("<EMAIL>")),
          prospectCreateFormdata.copy(
            email = None,
            linkedin_url = Some("linkedin.com/sales")
          ),
          prospectCreateFormdata.copy(
            email = None,
            linkedin_url = None,
            phone = Some("+912838485868")
          ),
          prospectCreateFormdata.copy(
            email = None,
            linkedin_url = None,
            phone = None,
            company = Some("marketing"),
            first_name = None,
            last_name = None
          ),
          prospectCreateFormdata.copy(
            email = None,
            linkedin_url = None,
            phone = None,
            company = Some("marketing"),
            first_name = None,
            last_name = None
          )
        ),
        orgId = 70L,
         //emailNotCompulsoryEnabled = true
      ).toList

      assert(res.length == 4)
    }

  }

  describe("ProspectService.constructProspectAccountData") {
//    it("should return prospectAccountData with name as email always if it's old path") {
//      val res = ProspectService.constructProspectAccountData(
//        prospects = Iterable(prospectCreateFormdata,
//          prospectCreateFormdata.copy(email = Some("<EMAIL>")),
//          prospectCreateFormdata.copy(email = None)),
//        org_id = org.id,
////        emailNotCompulsaryEnabled = false
//      )
//
//      val accountNames: List[String] = res.filter(_.prospectAccountCreateFormData.isDefined).map(_.prospectAccountCreateFormData.get).map(_.name)
//      assert(res.length == 2 && accountNames.contains("gmail.com"))
//    }

    it("should return not return prospectAccountData if email is not defined for new path") {
      val res = ProspectService.constructProspectAccountData(
        prospects = Iterable(prospectCreateFormdata,
          prospectCreateFormdata.copy(email = Some("<EMAIL>")),
          prospectCreateFormdata.copy(email = None, linkedin_url = Some("linkedin.com/sales")),
          prospectCreateFormdata.copy(email = None,
            linkedin_url = None,
            phone = Some("*********")
          ),
          prospectCreateFormdata.copy(email = None,
            linkedin_url = None,
            phone = None,
            company = Some("sales_company"),
            first_name = Some("Sam"),
            last_name = Some("Dew")
          )
        ),
        org_id = 70L,
//        emailNotCompulsaryEnabled = true
      )

      val accountNames: List[String] = res.filter(_.prospectAccountCreateFormData.isDefined).map(_.prospectAccountCreateFormData.get).map(_.name)
      assert(res.length == 5 && res.filter(_.prospectAccountCreateFormData.isDefined).length == 2)
    }

    it("should return empty list for enc flow if no any deduplication column present - should never occur") {
      val res = ProspectService.constructProspectAccountData(
        prospects = Iterable(
          prospectCreateFormdata.copy(email = None, phone = None, linkedin_url = None, company = None)),
        org_id = 70L,
//        emailNotCompulsaryEnabled = true
      )

      val accountNames: List[String] = res.filter(_.prospectAccountCreateFormData.isDefined).map(_.prospectAccountCreateFormData.get).map(_.name)
      assert(res.length == 1 && accountNames.isEmpty)
    }
  }

  describe("unit testing linkedin normalizer") {

    it("should leave any wrong url 2 ") {

      val result: Option[String] = LinkedinHelperFunctions.normalizeLinkedInURL(
        url = Some("/in/shanky99")
      )

      assert(result.get == "/in/shanky99")

    }

    it("should add https:// before *.linkedin.com/ urls   ") {

      val result: Option[String] = LinkedinHelperFunctions.normalizeLinkedInURL(
        url = Some("www.linkedin.com/in/shanky99")
      )

      assert(result.get == ("https://linkedin.com/in/shanky99"))

    }

    it("should leave url  if https:// is already present ") {

      val result: Option[String] = LinkedinHelperFunctions.normalizeLinkedInURL(
        url = Some("https://www.linkedin.com/in/shanky99")
      )

      assert(result.get == ("https://linkedin.com/in/shanky99"))

    }

    it("should return  https://in.linkedin.com/ linkedin_url when provided in.linkedin.com") {

      val result: Option[String] = LinkedinHelperFunctions.normalizeLinkedInURL(
        url = Some("in.linkedin.com/in/shanky99")
      )

      assert(result.get == ("https://linkedin.com/in/shanky99"))

    }

    it("should return  https://us.linkedin.com/ linkedin_url when provided us.linkedin.com") {

      val result: Option[String] = LinkedinHelperFunctions.normalizeLinkedInURL(
        url = Some("us.linkedin.com/in/shanky99")
      )

      assert(result.get == ("https://linkedin.com/in/shanky99"))

    }

    it("should return  https://uk.linkedin.com/ linkedin_url when provided uk.linkedin.com") {

      val result: Option[String] = LinkedinHelperFunctions.normalizeLinkedInURL(
        url = Some("uk.linkedin.com/in/shanky99")
      )

      assert(result.get == ("https://linkedin.com/in/shanky99"))

    }

    it("should return  https://uurs.linkedin.com/ linkedin_url when provided uurs.linkedin.com") {

      val result: Option[String] = LinkedinHelperFunctions.normalizeLinkedInURL(
        url = Some("uurs.linkedin.com/in/shanky99")
      )

      assert(result.get == ("https://linkedin.com/in/shanky99"))

    }

    it("should return  https://uurs.linkedin.com/in/shanky99/fastforward linkedin_url when provided uurs.linkedin.com") {

      val result: Option[String] = LinkedinHelperFunctions.normalizeLinkedInURL(
        url = Some("uurs.linkedin.com/in/shanky99/fastforward")
      )

      assert(result.get == ("https://linkedin.com/in/shanky99/fastforward"))

    }

    it("should return  https://uurs.linkedin.com/in/shanky99/fastforward linkedin_url when provided uurs.linkedin.com has some white spaces ") {

      val result: Option[String] = LinkedinHelperFunctions.normalizeLinkedInURL(
        url = Some(" uurs.linkedin.com/in/shanky99/fastforward")
      )

      assert(result.get == ("https://linkedin.com/in/shanky99/fastforward"))

    }

    it("should return  https://uurs.linkedin.com/in/shanky99/fastforward linkedin_url when provided uurs.linkedin.com has some white spaces  2 before and after") {

      val result: Option[String] = LinkedinHelperFunctions.normalizeLinkedInURL(
        url = Some(" uurs.linkedin.com/in/shanky99/fastforward ")
      )

      assert(result.get == ("https://linkedin.com/in/shanky99/fastforward"))

    }

    it("should return abcsdfsadf without white spaces before and after ( remove white spaces ) ") {

      val result: Option[String] = LinkedinHelperFunctions.normalizeLinkedInURL(
        url = Some(" " +
          "abcdefgh" +
          " ")
      )

      assert(result.get == ("abcdefgh"))

    }


    it("should return empty when none is provided as linkedin_url") {

      val result = LinkedinHelperFunctions.normalizeLinkedInURL(
        url = None
      )

      assert(result.isEmpty)
    }

    it("should return empty string if empty string is provided as linkedin_url") {

      val result = LinkedinHelperFunctions.normalizeLinkedInURL(
        url = Some("")
      )

      assert(result.isEmpty)
    }

    it("should trim and return linkedin_url with not query parameters") {
      val result = LinkedinHelperFunctions.normalizeLinkedInURL(
        url = Some("https://www.linkedin.com/in/aditya-sadana?miniProfileUrn=urn%3Ali%3Afs_miniProfile%3AACoAADPrceIBCqfk7PtLWi8nbELhFOzWXT7bdQk&lipi=urn%3Ali%3Apage%3Ad_flagship3_feed%3BLTDzpCq5RMC%2FHjxaqJssAg%3D%3D    ")
      )

      assert(result.contains("https://linkedin.com/in/aditya-sadana"))
    }

    it("should trim and return the url without lowercasing the unique id characters") {
      val result = LinkedinHelperFunctions.normalizeLinkedInURL(
        url = Some("https://www.linkedin.com/in/ACwAAACr3VQBNh-zr92z-L6MuZC60pUF-TIidCw")
      )

      assert(result.contains("https://linkedin.com/in/ACwAAACr3VQBNh-zr92z-L6MuZC60pUF-TIidCw"))
    }

  }

  describe("findProspectsInBlacklistForMultiRowInsert") {
    val blacklist: BlacklistTeamLevel = BlacklistTeamLevel(
      id = 2L,
      team_id = teamId,
      org_id = accountAdmin.org.id,
      name = "<EMAIL>",
      excluded_emails = Seq(),
      created_at = DateTime.now(),
      uuid = "some_uuid",
      do_not_contact_type = DoNotContactType.EMAIL
    )
    val newProspectsToBeAdded: NewProspectsToBeAdded = NewProspectsToBeAdded(
      prospectAccountCreateFormData = None,
      prospectCreateFormData = prospectCreateFormdata,
      prospect_uuid = "prs_uuid1"
    )

    it("should match expected result if some emails in prospectCreateFormData are matching with blacklist emails") {
      val res: Seq[BlacklistStatusForProspectUuid] = ProspectService.findProspectsInBlacklistForMultiRowInsert(
        blacklist = Seq(blacklist, blacklist.copy(id = 3L, name = "<EMAIL>", uuid = "uuidp2")),
        newProspects = Seq(
          newProspectsToBeAdded.copy(prospectCreateFormData = newProspectsToBeAdded.prospectCreateFormData.copy(email = Some("<EMAIL>")), prospect_uuid = "prs_uuid2"),
          newProspectsToBeAdded.copy(prospectCreateFormData = newProspectsToBeAdded.prospectCreateFormData.copy(email = Some("<EMAIL>")), prospect_uuid = "prs_uuid3"),
          newProspectsToBeAdded.copy(prospectCreateFormData = newProspectsToBeAdded.prospectCreateFormData.copy(email = None), prospect_uuid = "prs_uuid4")
        )
      )

      val expectedRes: Seq[BlacklistStatusForProspectUuid] = Seq(BlacklistStatusForProspectUuid(
        prospectUuid = ProspectUuid("prs_uuid2"),
        isInBlacklist = true,
      ), BlacklistStatusForProspectUuid(
        prospectUuid = ProspectUuid("prs_uuid3"),
        isInBlacklist = true,
      ),
        BlacklistStatusForProspectUuid(
          prospectUuid = ProspectUuid("prs_uuid4"),
          isInBlacklist = false,
        ))

      assert(res.nonEmpty && res == expectedRes)
    }

    it("should match expected result if some emails in prospectCreateFormData are matching with blacklist excluded emails") {
      val res: Seq[BlacklistStatusForProspectUuid] = ProspectService.findProspectsInBlacklistForMultiRowInsert(
        blacklist = Seq(blacklist.copy(id = 3L, name = "sales.in", uuid = "uuidp2", excluded_emails = Seq("<EMAIL>"), do_not_contact_type = DoNotContactType.DOMAIN)),
        newProspects = Seq(
          newProspectsToBeAdded.copy(prospectCreateFormData = newProspectsToBeAdded.prospectCreateFormData.copy(email = Some("<EMAIL>")), prospect_uuid = "prs_uuid2"),
          newProspectsToBeAdded.copy(prospectCreateFormData = newProspectsToBeAdded.prospectCreateFormData.copy(email = Some("<EMAIL>")), prospect_uuid = "prs_uuid3")
        )
      )

      val expectedRes: Seq[BlacklistStatusForProspectUuid] = Seq(BlacklistStatusForProspectUuid(
        prospectUuid = ProspectUuid("prs_uuid2"),
        isInBlacklist = true,
      ), BlacklistStatusForProspectUuid(
        prospectUuid = ProspectUuid("prs_uuid3"),
        isInBlacklist = false,
      ))

      assert(res.nonEmpty && res == expectedRes)
    }


    it("should match expected result if some domains in prospectCreateFormData are matching with blacklist domains") {

      val res: Seq[BlacklistStatusForProspectUuid] = ProspectService.findProspectsInBlacklistForMultiRowInsert(
        blacklist = Seq(
          blacklist.copy(id = 3L, name = "sales.com", uuid = "uuidp2", do_not_contact_type = DoNotContactType.DOMAIN),
        ),
        newProspects = Seq(
          newProspectsToBeAdded.copy(prospectCreateFormData = newProspectsToBeAdded.prospectCreateFormData.copy(email = Some("<EMAIL>")), prospect_uuid = "prs_uuid2"),
          newProspectsToBeAdded.copy(prospectCreateFormData = newProspectsToBeAdded.prospectCreateFormData.copy(email = Some("<EMAIL>")), prospect_uuid = "prs_uuid3")
        )
      )

      val expectedRes: Seq[BlacklistStatusForProspectUuid] = Seq(BlacklistStatusForProspectUuid(
        prospectUuid = ProspectUuid("prs_uuid2"),
        isInBlacklist = true,
      ), BlacklistStatusForProspectUuid(
        prospectUuid = ProspectUuid("prs_uuid3"),
        isInBlacklist = false,
      )
      )

      assert(res.nonEmpty && res == expectedRes)
    }
    it("should match expected result if some phones in prospectCreateFormData are matching with blacklist phones") {

      val res: Seq[BlacklistStatusForProspectUuid] = ProspectService.findProspectsInBlacklistForMultiRowInsert(
        blacklist = Seq(
          blacklist.copy(id = 3L, name = "+************", uuid = "uuidp2", do_not_contact_type = DoNotContactType.PHONE)
        ),
        newProspects = Seq(
          newProspectsToBeAdded.copy(prospectCreateFormData = newProspectsToBeAdded.prospectCreateFormData.copy(phone = Some("+************")), prospect_uuid = "prs_uuid2"),
          newProspectsToBeAdded.copy(prospectCreateFormData = newProspectsToBeAdded.prospectCreateFormData.copy(email = Some("<EMAIL>")), prospect_uuid = "prs_uuid3")
        )
      )

      val expectedRes: Seq[BlacklistStatusForProspectUuid] = Seq(BlacklistStatusForProspectUuid(
        prospectUuid = ProspectUuid("prs_uuid2"),
        isInBlacklist = true,
      ), BlacklistStatusForProspectUuid(
        prospectUuid = ProspectUuid("prs_uuid3"),
        isInBlacklist = false,
      )
      )

      assert(res.nonEmpty && res == expectedRes)
    }
    it("should match expected result if all email,domain and phone in prospectCreateFormData are  matching with blacklist") {
      val res: Seq[BlacklistStatusForProspectUuid] = ProspectService.findProspectsInBlacklistForMultiRowInsert(
        blacklist = Seq(
          blacklist,
          blacklist.copy(id = 4L, name = "marketing.com", uuid = "uuidp2", do_not_contact_type = DoNotContactType.DOMAIN),
          blacklist.copy(id = 3L, name = "+************", uuid = "uuidp2", do_not_contact_type = DoNotContactType.PHONE)
        ),

        newProspects = Seq(
          newProspectsToBeAdded.copy(prospectCreateFormData = newProspectsToBeAdded.prospectCreateFormData.copy(email = Some(blacklist.name)), prospect_uuid = "prs_uuid2"),
          newProspectsToBeAdded.copy(prospectCreateFormData = newProspectsToBeAdded.prospectCreateFormData.copy(phone = Some("+************")), prospect_uuid = "prs_uuid3"),
          newProspectsToBeAdded.copy(prospectCreateFormData = newProspectsToBeAdded.prospectCreateFormData.copy(email = Some("<EMAIL>")), prospect_uuid = "prs_uuid4")
        )
      )

      val expectedRes: Seq[BlacklistStatusForProspectUuid] = Seq(BlacklistStatusForProspectUuid(
        prospectUuid = ProspectUuid("prs_uuid2"),
        isInBlacklist = true,
      ), BlacklistStatusForProspectUuid(
        prospectUuid = ProspectUuid("prs_uuid3"),
        isInBlacklist = true,
      ),
        BlacklistStatusForProspectUuid(
          prospectUuid = ProspectUuid("prs_uuid4"),
          isInBlacklist = true,
        )
      )

      assert(res.nonEmpty && res == expectedRes)
    }
    it("should match expected result if email/domain/phone in prospectCreateFormData are not matching with any blacklist") {

      val res: Seq[BlacklistStatusForProspectUuid] = ProspectService.findProspectsInBlacklistForMultiRowInsert(
        blacklist = Seq(
          blacklist,
          blacklist.copy(id = 4L, name = "marketing.com", uuid = "uuidp2", do_not_contact_type = DoNotContactType.DOMAIN),
          blacklist.copy(id = 3L, name = "+************", uuid = "uuidp2", do_not_contact_type = DoNotContactType.PHONE)
        ),
        newProspects = Seq(
          newProspectsToBeAdded.copy(prospectCreateFormData = newProspectsToBeAdded.prospectCreateFormData.copy(phone = Some("+************")), prospect_uuid = "prs_uuid3"),
          newProspectsToBeAdded.copy(prospectCreateFormData = newProspectsToBeAdded.prospectCreateFormData.copy(email = Some("<EMAIL>")), prospect_uuid = "prs_uuid4")
        )
      )

      val expectedRes: Seq[BlacklistStatusForProspectUuid] = Seq(BlacklistStatusForProspectUuid(
        prospectUuid = ProspectUuid("prs_uuid3"),
        isInBlacklist = false,
      ),
        BlacklistStatusForProspectUuid(
          prospectUuid = ProspectUuid("prs_uuid4"),
          isInBlacklist = false,
        )
      )

      assert(res.nonEmpty && res == expectedRes)
    }
  }

  describe("testing total_dnc_prospects in CreateOrUpdateProspectsResult"){
    it("should successfully return CreateOrUpdateProspectsResult with total_dnc_prospects"){

      (accountService.findNewOwnerTeamMember(_: TeamId, _: AccountId))
        .expects(TeamId(id = teamId), AccountId(id = ownerId)) // FIXME VALUECLASS
        .returning(Success(Some(teamMemberBasic)))

      (campaignService.findWithPermission(_: Long, _: Seq[Long], _: Long, _: Option[Account])(using _: SRLogger))
        .expects(campaign_id, *, teamId, Some(accountAdmin), *)
        .returning(Some(campaign))

      (emailSettingDAO.getAllEmailSettingsInTeam)
        .expects(teamId)
        .returning(Success(List()))

      (accountService.getEmailsOfTeamMembers)
        .expects(TeamId(id = teamId)) // FIXME VALUECLASS
        .returning(Success(List()))

      (accountService.getEmailOfOrgOwner)
        .expects(OrgId(id = org.id)) // FIXME VALUECLASS
        .returning(Success("<EMAIL>"))

      (prospectColumnDef.findCustomColumns)
        .expects(teamId)
        .returning(Seq())

      (prospectDAOService.getProspectCategoryId(_: TeamId, _: ProspectCategory.Value, _: Option[Account])(using _:SRLogger))
        .expects(TeamId(id = teamId), ProspectCategory.DO_NOT_CONTACT, Some(accountAdmin), *)
        .returning(Success(ProspectCategoryId(1L)))

      (prospectDAOService.getProspectCategoryId(_: TeamId, _: ProspectCategory.Value, _: Option[Account])(using _:SRLogger))
        .expects(TeamId(id = teamId), ProspectCategory.NOT_CATEGORIZED, Some(accountAdmin), *)
        .returning(Success(ProspectCategoryId(1L)))

      (prospectDAOService.findOrCreateList)
        .expects(Some("list"), 2, permittedAccountIds, teamId)
        .returning(Some(1L))

      (prospectAccountDAO1.find)
        .expects(120, teamId)
        .returning(Some(ProspectAccountFixture.prospectAccount))

      val blacklist: BlacklistTeamLevel = BlacklistTeamLevel(
        id = 2L,
        team_id = teamId,
        org_id = accountAdmin.org.id,
        name = email,
        excluded_emails = Seq(),
        created_at = DateTime.now(),
        uuid = "some_uuid",
        do_not_contact_type = DoNotContactType.EMAIL
      )

      (blacklistProspectCheckDAO.findByEmailsAndDomains)
        .expects(*, teamId,OrgId(id = org.id), Seq(email, "<EMAIL>", "<EMAIL>", "<EMAIL>"), *)
        .returning(Success(Seq(
          blacklist,
          blacklist.copy(id = 3L, name = "sales.in", do_not_contact_type = DoNotContactType.DOMAIN),
          blacklist.copy(id = 4L, name = "market.in", do_not_contact_type = DoNotContactType.DOMAIN, excluded_emails = Seq("<EMAIL>"))
        )))

      (prospectDAOService.findDuplicateProspectsForForceUpdateV2(_: Seq[Seq[DuplicationFindProspectDataV2]], _: TeamId)(using _:SRLogger))
        .expects(*, TeamId(teamId), *)
        .returning(Success(List()))


      (prospectDAOService.updateProspectsForEmailOptional(_: Boolean, _: Seq[ColumnDef], _: Option[Long], _: Iterable[ProspectsToBeForceUpdated], _: Seq[SrProspectColumns], _: List[ProspectEmailsTobeAddedInForceUpdateProspect], _: Long)(using _: SRLogger))
        .expects(true, List(), *, *, *, *, teamId, *)
        .returning(Success(List()))

      for(a <- 0 until 4){(() => srUuidUtils.generateProspectUuid())
        .expects()
        .returning(temp_prospect_id + s"$a")}

      val insertedProspectResult_new = (0 to 4).zipWithIndex.map {
        case (_, index) =>
          insertedProspectResult.copy(index+3, Some(temp_prospect_id + s"${index}"), false, ownerId)
      }.toList
      
      (prospectDAOService.insertNewProspects)
        .expects(*, List(BlacklistStatusForProspectUuid(ProspectUuid("prs_aa_qwerty0"),true),
          BlacklistStatusForProspectUuid(ProspectUuid("prs_aa_qwerty1"),true),
          BlacklistStatusForProspectUuid(ProspectUuid("prs_aa_qwerty2"),false),
          BlacklistStatusForProspectUuid(ProspectUuid("prs_aa_qwerty3"),false)),
          *, *, None, *, *, true, *, ownerId, ownerId, teamId, TriggerSource.OTHER, *, *, *)
        .returning(Success(insertedProspectResult_new))

      (prospectsEmailsDAO.__createFromUploadMultiRowInsertV2_New)
        .expects(*, *, *, *)
        .returning(Success(List(
          NewlyCreatedProspect(3L, "<EMAIL>"),
          NewlyCreatedProspect(4L, "<EMAIL>"),
          NewlyCreatedProspect(5L, "<EMAIL>"),
          NewlyCreatedProspect(6L, "<EMAIL>")
        )))
      (mqDomainServiceProviderDNSService.publish)
        .expects("gmail.com")
        .returning(Success({}))

      (campaignProspectTimezonesJedisService.delete(_: CampaignId)(_: SRLogger))
        .expects(*, *)

      (mergeTagService._removeMissingErrorMessage)
        .expects(Seq())
        .returning(1)

      //publishing trigger events
      val createdTriggerMsg1 = MQTriggerMsg(
        event = EventType.CREATED_PROSPECT_IN_SMARTREACH.toString,
        prospectIds = Seq(3L, 4L, 5L, 6L, 7L),
        accountId = ownerId,
        teamId = teamId,
        updatedProspectCategoryId = None,
        triggerPath = Some(TriggerSource.OTHER),
        oldProspectDeduplicationColumn = None
      )

      (mqTrigger.publishEvents)
        .expects(createdTriggerMsg1)
        .returning(())

      (mqAssociateProspectToOldEmails.publish)
        .expects(*)
        .returning(Success(()))

      val prospectIdsToAddTags = Seq(3L, 4L, 5L, 6L, 7L)

      for (a <- 1 until 4) {
        (() => srUuidUtils.generateTagsUuid())
          .expects()
          .returning("tag_abcd" + s"$a")
      }

      (tagService.updateTagsForProspects (_: AddOrRemoveTagAction.Value, _: Seq[Long], _: Seq[TagAndUuid], _: Long, _: Long)(using _: SRLogger))
        .expects(AddOrRemoveTagAction.ADD, prospectIdsToAddTags, *, teamId, ownerId, *)
        .returning(Success(1))


      (campaignProspectService.assignProspectsToCampaign)
        .expects(
          accountAdmin.org.id,
          permittedAccountIds, accountAdmin.internal_id, Helpers.getAccountName(accountAdmin), ownerId, teamId, campaign.id, campaign.name, campaign.settings,
          *, IgnoreProspectsInOtherCampaigns.IgnoreProspectsActiveInOtherCampaigns, Logger)
        .returning(Success(cpAssignResult))


//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//        _: TeamId,
//        _: SrRollingUpdateFeature
//      )(_: ISRLogger))
//        .expects(TeamId(37L), SrRollingUpdateFeature.EmailNotCompulsory, *)
//        .twice()
//        .returning(false)


      val res = prospectService.createOrUpdateProspects(
        ownerAccountId = ownerId,
        teamId = teamId,
        listName = prospectCreateFormdata.list,
        prospects = Seq(prospectCreateFormdata, prospectCreateFormdata.copy(email = Some("<EMAIL>")), prospectCreateFormdata.copy(email = Some("<EMAIL>")), prospectCreateFormdata.copy(email = Some("<EMAIL>"))),
        updateProspectType = UpdateProspectType.ForceUpdate,
        ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads = true,
        doerAccount = accountAdmin,
        prospectSource = None,
        prospectAccountId = Some(120L),
        prospect_tags = Some("tag1,tag2,tag3"),

        campaign_id = Some(campaign_id),
        ignoreProspectInOtherCampaign = IgnoreProspectsInOtherCampaigns.IgnoreProspectsActiveInOtherCampaigns,
        deduplicationColumns = None,
        auditRequestLogId = Some("auditRequestLogId"),
        SRLogger = Logger
      )

      res match{
        case Failure(exception) =>
          println(s"tag error ${LogHelpers.getStackTraceAsString(exception)}")
          assert(false)
        case Success(value) =>
          Logger.info(s"createOrUpdateProspectResult $value")
          assert(value.total_dnc_prospects == 2)
      }
    }

  }

  describe("findNewProspectEmailsDataForForceUpdate") {
    it("should return new prospect email with is_primary false if there is existing email for duplicate prospect") {
      val res = ProspectService.findNewProspectEmailsDataForForceUpdate(
        prospectsToBeUpdated = Iterable(ProspectsToBeForceUpdated(
          prospectCreateFormdata.copy(email = Some("<EMAIL>")),
          20L,
          Seq(SrProspectColumns.Phone))
        ),
        prospectEmailData = List(ProspectEmailData(prospect_id = 20L,
          email_data = ProspectEmail(email = "<EMAIL>", is_valid = true, is_primary = true)
        ))
      )

      assert(res.nonEmpty &&
        res.head.prospectEmailData.email_data.email == "<EMAIL>" &&
        !res.head.prospectEmailData.email_data.is_primary
      )
    }

    it("should return new prospect email with is_primary true if there is no any existing email for duplicate prospect") {
      val res = ProspectService.findNewProspectEmailsDataForForceUpdate(
        prospectsToBeUpdated = Iterable(ProspectsToBeForceUpdated(
          prospectCreateFormdata.copy(email = Some("<EMAIL>")),
          20L,
          Seq(SrProspectColumns.Phone))
        ),
        prospectEmailData = List()
      )

      assert(res.nonEmpty &&
        res.head.prospectEmailData.email_data.email == "<EMAIL>" &&
        res.head.prospectEmailData.email_data.is_primary
      )
    }

    it("should return empty list if email already exist") {
      val res = ProspectService.findNewProspectEmailsDataForForceUpdate(
        prospectsToBeUpdated = Iterable(ProspectsToBeForceUpdated(
          prospectCreateFormdata.copy(email = Some("<EMAIL>")),
          20L,
          Seq(SrProspectColumns.Phone))
        ),
        prospectEmailData = List(ProspectEmailData(prospect_id = 20L,
          email_data = ProspectEmail(email = "<EMAIL>", is_valid = true, is_primary = true)
        ))
      )
      assert(res.isEmpty)
    }

    it("should return list will 1 new primary email, 1 secondary and nothing for exact same email") {
      val res = ProspectService.findNewProspectEmailsDataForForceUpdate(
        prospectsToBeUpdated = Iterable(
          ProspectsToBeForceUpdated(
          prospectCreateFormdata.copy(email = Some("<EMAIL>")),
          20L,
          Seq(SrProspectColumns.Phone)
          ),
          ProspectsToBeForceUpdated(
            prospectCreateFormdata.copy(email = Some("<EMAIL>")),
            21L,
            Seq(SrProspectColumns.Phone)
          ),
          ProspectsToBeForceUpdated(
            prospectCreateFormdata.copy(email = Some("<EMAIL>")),
            22L,
            Seq(SrProspectColumns.Phone)
          ),
          ProspectsToBeForceUpdated(
            prospectCreateFormdata.copy(email = None),
            23L,
            Seq(SrProspectColumns.Phone)
          )
        ),
        prospectEmailData = List(
          ProspectEmailData(prospect_id = 20L,
          email_data = ProspectEmail(email = "<EMAIL>", is_valid = true, is_primary = true)
          ),
          ProspectEmailData(prospect_id = 22L,
            email_data = ProspectEmail(email = "<EMAIL>", is_valid = true, is_primary = true)
          )
        )
      )
      assert(res.nonEmpty && res.length == 2)
    }
  }

  describe("findDuplicateProspectsFromDbForSelectedColumnsV2") {
    it("should return sucess if no duplicates found") {

      (prospectDAOService.findDuplicateProspectsForForceUpdateV2(_: Seq[Seq[DuplicationFindProspectDataV2]], _: TeamId)(using _:SRLogger))
        .expects(Seq(Seq(DuplicationFindProspectDataV2(
          data = "<EMAIL>", columnType = SrProspectColumns.Email
        )),Seq(DuplicationFindProspectDataV2(
          data = "<EMAIL>", columnType = SrProspectColumns.Email
        ))), *, *)
        .returning(Success(List()))

      val res = prospectService.findDuplicateProspectsFromDbForSelectedColumnsV2(
        prospects = Seq(
          prospectCreateFormdata.copy(email = Some("<EMAIL>")),
          prospectCreateFormdata.copy(email = Some("<EMAIL>"))),
        teamId = teamId,
        orgId = org.id,
        deduplicationColumns = Seq(SrProspectColumns.Email),
//        emailNotCompulsoryEnabled = true
      )
      assert(res.isSuccess && res.get == List())
    }

    it("should return Email duplicates if deduplicationColumns only have email") {

      val expectedDuplicateList = List(DuplicateProspectResult(
        prospectCreateFormData = prospectCreateFormData.copy(email = Some("<EMAIL>")),
        deDuplicateColumnAndValue = Seq(DeDuplicateColumnTypeAndValue(
          deDuplicationColumnValue = "<EMAIL>",
          deDuplicationColumnType = SrProspectColumns.Email)
        ),
        prospect_id = 10L,
        account_id = Some(20L),
      ))

      (prospectDAOService.findDuplicateProspectsForForceUpdateV2(_: Seq[Seq[DuplicationFindProspectDataV2]], _: TeamId)(using _: SRLogger))
        .expects(Seq(Seq(DuplicationFindProspectDataV2(
          data = "<EMAIL>", columnType = SrProspectColumns.Email
        )), Seq(DuplicationFindProspectDataV2(
          data = "<EMAIL>", columnType = SrProspectColumns.Email
        ))), *, *)
        .returning(Success(expectedDuplicateList))

      val res = prospectService.findDuplicateProspectsFromDbForSelectedColumnsV2(
        prospects = Seq(
          prospectCreateFormdata.copy(email = Some("<EMAIL>")),
          prospectCreateFormdata.copy(email = Some("<EMAIL>"))),
        teamId = teamId,
        orgId = org.id,
        deduplicationColumns = Seq(SrProspectColumns.Email),
//        emailNotCompulsoryEnabled = true
      )
      assert(res.isSuccess && res.get == expectedDuplicateList)
    }

    it("should return Email, Phone, linkedin duplicates if deduplicationColumns have email, Phone, linkedin") {

      val expectedDuplicateList = List(DuplicateProspectResult(
        prospectCreateFormData = prospectCreateFormData.copy(email = Some("<EMAIL>")),
        deDuplicateColumnAndValue = Seq(DeDuplicateColumnTypeAndValue(
          deDuplicationColumnValue = "<EMAIL>",
          deDuplicationColumnType = SrProspectColumns.Email),
          DeDuplicateColumnTypeAndValue(
            deDuplicationColumnValue = "**********",
            deDuplicationColumnType = SrProspectColumns.Phone)
        ),
        prospect_id = 10L,
        account_id = Some(20L),
      ))

      val dataForDao = Seq(Seq(DuplicationFindProspectDataV2(
        data = "<EMAIL>", columnType = SrProspectColumns.Email
      ), DuplicationFindProspectDataV2(
        data = "**********", columnType = SrProspectColumns.Phone
      )), Seq(DuplicationFindProspectDataV2(
        data = "<EMAIL>", columnType = SrProspectColumns.Email
      ), DuplicationFindProspectDataV2(
        data = "https://www.linkedin.com/in/user2", columnType = SrProspectColumns.LinkedinUrl
      )))

      (prospectDAOService.findDuplicateProspectsForForceUpdateV2(_: Seq[Seq[DuplicationFindProspectDataV2]], _: TeamId)(using _: SRLogger))
        .expects(dataForDao, *, *)
        .returning(Success(expectedDuplicateList))

      val res = prospectService.findDuplicateProspectsFromDbForSelectedColumnsV2(
        prospects = Seq(
          prospectCreateFormdata.copy(email = Some("<EMAIL>"), phone = Some("**********"), linkedin_url = None),
          prospectCreateFormdata.copy(email = Some("<EMAIL>"), phone = None, linkedin_url = Some("https://www.linkedin.com/in/user2"))),
        teamId = teamId,
        orgId = org.id,
        deduplicationColumns = Seq(SrProspectColumns.Email, SrProspectColumns.Phone, SrProspectColumns.LinkedinUrl),
//        emailNotCompulsoryEnabled = true
      )
      assert(res.isSuccess && res.get == expectedDuplicateList)
    }

    it("should return company-firstname-lastname duplicates if deduplicationColumns have company-firstname-lastname") {

      val expectedDuplicateList = List(DuplicateProspectResult(
        prospectCreateFormData = prospectCreateFormdata.copy(email = Some("<EMAIL>"),
          first_name = Some("user1first"),
          last_name = Some("user1last"),
          company = Some("user1comp")),
        deDuplicateColumnAndValue = Seq(DeDuplicateColumnTypeAndValue(
          deDuplicationColumnValue = "user1compuser1firstuser1last",
          deDuplicationColumnType = SrProspectColumns.CompanyFirstnameLastname)
        ),
        prospect_id = 10L,
        account_id = Some(20L),
      ))

      (prospectDAOService.findDuplicateProspectsForForceUpdateV2(_: Seq[Seq[DuplicationFindProspectDataV2]], _: TeamId)(using _: SRLogger))
        .expects(Seq(Seq(DuplicationFindProspectDataV2(
          data = "user1compuser1firstuser1last", columnType = SrProspectColumns.CompanyFirstnameLastname
        )), Seq(DuplicationFindProspectDataV2(
          data = "user2compuser2firstuser2last", columnType = SrProspectColumns.CompanyFirstnameLastname
        ))), *, *)
        .returning(Success(expectedDuplicateList))

      val res = prospectService.findDuplicateProspectsFromDbForSelectedColumnsV2(
        prospects = Seq(
          prospectCreateFormdata.copy(email = Some("<EMAIL>"),
            first_name = Some("user1first"),
            last_name = Some("user1last"),
            company = Some("user1comp")),
          prospectCreateFormdata.copy(
            email = Some("<EMAIL>"),
            first_name = Some("user2first"),
            last_name = Some("user2last"),
            company = Some("user2comp")
          )),
        teamId = teamId,
        orgId = org.id,
        deduplicationColumns = Seq(SrProspectColumns.CompanyFirstnameLastname),
//        emailNotCompulsoryEnabled = true
      )
      assert(res.isSuccess && res.get == expectedDuplicateList)
    }

    it("should not pass data to dao if email is defined but email is not in selected column") {

      (prospectDAOService.findDuplicateProspectsForForceUpdateV2(_: Seq[Seq[DuplicationFindProspectDataV2]], _: TeamId)(using _: SRLogger))
        .expects(Seq(), *, *)
        .returning(Success(List()))

      val res = prospectService.findDuplicateProspectsFromDbForSelectedColumnsV2(
        prospects = Seq(
          prospectCreateFormdata.copy(email = Some("<EMAIL>")),
          prospectCreateFormdata.copy(email = Some("<EMAIL>"))),
        teamId = teamId,
        orgId = org.id,
        deduplicationColumns = Seq(SrProspectColumns.Phone),
//        emailNotCompulsoryEnabled = true
      )
      assert(res.isSuccess && res.get == List())
    }

  }
}
