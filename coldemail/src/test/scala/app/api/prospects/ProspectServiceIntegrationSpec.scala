package app.api.prospects

import org.apache.pekko.actor.ActorSystem
import org.apache.pekko.stream.Materializer
import api.APIErrorResponse.{ErrorResponseProspectsPostApi, ErrorResponseUpdateProspectStatusApi}
import api.AppConfig
import api.accounts.{AccountDAO, AccountService, TeamId}
import api.accounts.models.OrgId 
import api.blacklist.dao.BlacklistProspectCheckDAO
import api.campaigns.services.{CampaignProspectService, CampaignProspectTimezonesJedisService, CampaignService, MergeTagService}
import api.columns.{CustomColumnDefCreateForm, FieldTypeEnum, ProspectColumnDef}
import api.csv_uploads.models.CsvUploadType
import api.emails.EmailSettingDAO
import api.prospects.dao.ProspectsEmailsDAO
import api.prospects.dao_service.ProspectDAOService
import api.prospects.{CsvQueue, CsvQueueCreateFormDataV2, GenerateTempId, ProspectAccountDAO1, ProspectCreateFormDataV2, ProspectService, ProspectUpdateCategoryTemp, ProspectUuid}
import api.prospects.models.ProspectObjectForApiIntegrationTest.{ProspectStatusChangeObjectForApiIntegrationTest, ProspectStatusChangeRequestBodyTest}
import api.prospects.models.{ProspectId, ProspectObjectForApiIntegrationTest}
import api.prospects.service.{PotentialDuplicateProspectService, ProspectEmail, ProspectErrorType, ProspectServiceV2, ProspectTagForApi, UpdateProspectStatusErrorType}
import api.tags.TagService
import app.test_models.ProspectObjectV2ForApiIntegrationTest
import db_test_spec.api.{DbTestingBeforeAllAndAfterAll, InitialData}
import db_test_spec.api.accounts.fixtures.NewAccountAndEmailSettingData
import db_test_spec.api.campaigns.test_utils.{CampaignUtils, CreateAndStartCampaignData}
import eventframework.ProspectObject
import org.joda.time.DateTime
import org.scalamock.scalatest.AsyncMockFactory
import org.scalatest.funspec.AsyncFunSpec
import play.api.libs.json.{JsError, JsResult, JsSuccess, JsValue, Json, Reads}
import play.api.libs.json.JodaReads._
import play.api.libs.ws.ahc.AhcWSClient
import utils.helpers.LogHelpers
import utils.mq.email.{MQAssociateProspectToOldEmails, MQDomainServiceProviderDNSService}
import utils.mq.webhook.MQTrigger
import utils.uuid.SrUuidUtils
import utils.uuid.services.SrUuidService
import utils.{Helpers, SRLogger}
import utils_deploy.rolling_updates.models.SrRollingUpdateFeature
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService

import scala.concurrent.duration.{Duration, SECONDS}
import scala.concurrent.{Await, Future}
import scala.util.{Failure, Success, Try}
import play.api.test.FakeRequest
import play.api.test.Helpers._

class ProspectServiceIntegrationSpec extends AsyncFunSpec with AsyncMockFactory with DbTestingBeforeAllAndAfterAll {

  override lazy val srRollingUpdateCoreService = mock[SrRollingUpdateCoreService]

  val campaign_id: Long = 121L
  val campaign_name = "CampaignName"
  val permittedAccountIds = Seq(2L)
  val teamId: Long = 37L
  val ownerId: Long = 2L

  val first_name = "first_name"
  val last_name = "last_name"
  val company = "CompanyName"
  val email = "<EMAIL>"
  val email_domain = "smartreach.com"
  val campaignIds = Seq("cmp_aa_2X9VhzmaReFKIQ7VUf5T8PP7Kny")
  val prospectIds = Seq("prs_aa_2ZqQV8Vc3RFA1lLfxFxFyOaoILm")

  val aDate = DateTime.parse("2022-3-27")
  val willResumeAt = Option(DateTime.now())
  val willResumeAtTz = Option("Asia/kolkata")

  val prospectCreateFormdata = ProspectCreateFormDataV2(
    email = Some(email),
    first_name = Some(first_name),
    last_name = Some(last_name),
    custom_fields = Some(Json.obj("followers" -> 500)),

    // should not break in old ui/integrations, currently used only inside createOrUpdateOne controller
    owner_id = Some(ownerId),

    list = Some("list"),
    company = Some(company),
    city = Some("kolkata"),
    country = Some("India"),
    timezone = Some("Asia/Kolkata"),
    created_at = None,

    state = None,
    job_title = None,
    phone_number = None,
    linkedin_url = None,
    tags = None
  )

  val prospectStatusChangeRequestBodyForPauseStatus = ProspectStatusChangeRequestBodyTest(
    prospect_ids = prospectIds,
    campaign_ids = campaignIds,
    prospect_status = "unpause",
    will_resume_at = None,
    will_resume_at_tz = None
  )

  val prospectStatusChangeRequestBodyForCampaignIdError = ProspectStatusChangeRequestBodyTest(
    prospect_ids = prospectIds,
    campaign_ids = Seq(),
    prospect_status = "unpause",
    will_resume_at = None,
    will_resume_at_tz = None
  )

  val prospectStatusChangeRequestBodyForResumeLaterStatus = ProspectStatusChangeRequestBodyTest(
    prospect_ids = prospectIds,
    campaign_ids = campaignIds,
    prospect_status = "resume_later",
    will_resume_at = willResumeAt,
    will_resume_at_tz = Option("Asia/kolkata")
  )

  /**
   * Integrations tests added below are using devapi
   * if these test cases fail then check id devapi.sreml.com is up
   * Also these test cases using specific account from dev environment
   * so any changes(deletion) for prospect(<EMAIL>)/team(80) might affect this test cases
   */

  describe("Integration tests for prospects apis") {


    describe("ProspectController.createOrUpdateProspectsBatch") {
      it("should return response in expected format for success") {

        val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
        val apiKey = initialData.teamUserLevelKey

        val sendingUrl: String = s"/api/v3/prospects"

        //each time we run this test we are inserting new prospect so it should be unique
        val prospect_email: String = Helpers.generateRandomString(8) + "@gmail.com"

        val request = FakeRequest(play.api.test.Helpers.POST, sendingUrl)
          .withHeaders("X-API-KEY" -> apiKey,
            "Content-Type" -> "application/json")
          .withJsonBody(Json.toJson(Seq(
            prospectCreateFormdata.copy(
              email = Some(prospect_email),
              custom_fields = Some(Json.obj()),
              list = None,
              owner_id = None,
              phone_number = Some("+************")
            )
          )
          ))

        val final_result = play.api.test.Helpers.route(testApi, request).get

        val status: Int = play.api.test.Helpers.status(final_result)
        val json: JsValue = play.api.test.Helpers.contentAsJson(final_result)


        final_result.map(res => {

            if (status == 200) {
              val sentResponseData: JsValue = json

              sentResponseData.validate[List[ProspectObjectForApiIntegrationTest]] match {
                case JsError(_) => assert(false)

                case JsSuccess(prospect_data_list, _) =>
                  assert(prospect_data_list.flatMap(_.emails.map(_.email)).contains(prospect_email))
                  assert(prospect_data_list.exists(p => p.phone_numbers.exists(pn => pn.equals("+************"))))
              }

            } else {
              assert(false)
            }

          })
          .recover { case e => assert(false)
          }

      }

      it("should return response in expected format for success when inserted and updated data is returned") {
        val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
        val apiKey = initialData.teamUserLevelKey

        val sendingUrl: String = s"/api/v3/prospects?tid=80"

        //each time we run this test we are inserting new prospect so it should be unique
        val prospect_email: String = Helpers.generateRandomString(8) + "@gmail.com"
        val existing_prospect: String = "<EMAIL>"

        //to update company name of existing prospect
        val new_company_name: String = Helpers.generateRandomString(8)

        val request = FakeRequest(play.api.test.Helpers.POST, sendingUrl)
          .withHeaders("X-API-KEY" -> apiKey,
            "Content-Type" -> "application/json")
          .withJsonBody(Json.toJson(Seq(
            prospectCreateFormdata.copy(
              email = Some(prospect_email),
              custom_fields = Some(Json.obj()),
              list = None,
              owner_id = None
            ),
            prospectCreateFormdata.copy(
              email = Some(existing_prospect),
              company = Some(new_company_name),
              custom_fields = Some(Json.obj()),
              list = None,
              owner_id = None
            )
          )
          ))

        val final_result = play.api.test.Helpers.route(testApi, request).get

        val status: Int = play.api.test.Helpers.status(final_result)
        val json: JsValue = play.api.test.Helpers.contentAsJson(final_result)

        final_result.map(res => {

            if (status == 200) {
              val sentResponseData = json

              sentResponseData.validate[List[ProspectObjectForApiIntegrationTest]] match {
                case JsError(_) => assert(false)

                case JsSuccess(prospect_data_list, _) =>
                  assert(prospect_data_list.exists(_.emails.map(_.email).contains("<EMAIL>"))
                    && prospect_data_list.find(_.emails.map(_.email).contains("<EMAIL>")).get.company.isDefined
                    && prospect_data_list.find(_.emails.map(_.email).contains("<EMAIL>")).get.company.get == new_company_name
                  )
              }

            } else {
              assert(false)
            }

          })
          .recover { case e => assert(false)
          }

      }

      it("should return response in expected format for success when tags existing/new") {
        val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
        val apiKey = initialData.teamUserLevelKey

        val sendingUrl: String = s"/api/v3/prospects?tid=80"

        //each time we run this test we are inserting new prospect so it should be unique
        val prospect_email_1: String = Helpers.generateRandomString(8) + "@gmail.com"
        val prospect_email_2: String = Helpers.generateRandomString(8) + "@gmail.com"

        val prospect_tag_existing = "existing"
        val prospect_tag_new = Helpers.generateRandomString(5)

        val prospect_1 = prospectCreateFormdata.copy(
          email = Some(prospect_email_1),
          custom_fields = Some(Json.obj()),
          list = None,
          owner_id = None,
          tags = Some(Seq(prospect_tag_existing))
        )

        val prospect_2 = prospectCreateFormdata.copy(
          email = Some(prospect_email_2),
          custom_fields = Some(Json.obj()),
          list = None,
          owner_id = None,
          tags = Some(Seq(prospect_tag_new))
        )

        val request = FakeRequest(play.api.test.Helpers.POST, sendingUrl)
          .withHeaders("X-API-KEY" -> apiKey,
            "Content-Type" -> "application/json")
          .withJsonBody(Json.toJson(Seq(prospect_1, prospect_2)))

        val final_result = play.api.test.Helpers.route(testApi, request).get

        val status: Int = play.api.test.Helpers.status(final_result)
        val json: JsValue = play.api.test.Helpers.contentAsJson(final_result)

        final_result.map(res => {

            if (status == 200) {
              val sentResponseData = json

              sentResponseData.validate[List[ProspectObjectForApiIntegrationTest]] match {
                case JsError(_) => assert(false)

                case JsSuccess(prospect_data_list, _) =>
                  val prospect1 = prospect_data_list.filter(_.emails.head.email == prospect_email_1)
                  val prospect2 = prospect_data_list.filter(_.emails.head.email == prospect_email_2)
                  assert(prospect1.exists(p => p.tags.isDefined && p.tags.get.exists(_.tag == prospect_tag_existing)))
                  assert(prospect2.exists(p => p.tags.isDefined && p.tags.get.exists(_.tag == prospect_tag_new)))
              }

            } else {
              assert(false)
            }

          })
          .recover { case e => assert(false)
          }

      }

      it("should return error response in expected format for failure (1.duplicate_emails_found 2.custom_field_not_found)") {
        val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
        val apiKey = initialData.teamUserLevelKey
        val sendingUrl: String = s"/api/v3/prospects?tid=80"

        //each time we run this test we are inserting new prospect so it should be unique
        val prospect_email: String = Helpers.generateRandomString(8) + "@gmail.com"

        val request = FakeRequest(play.api.test.Helpers.POST, sendingUrl)
          .withHeaders("X-API-KEY" -> apiKey,
            "Content-Type" -> "application/json")
          .withJsonBody(Json.toJson(Seq(
            prospectCreateFormdata.copy(
              email = Some(prospect_email),
              custom_fields = Some(Json.obj()),
              list = None,
              owner_id = None
            ),
            prospectCreateFormdata.copy(
              email = Some(prospect_email),
              custom_fields = Some(Json.obj("webb" -> "sales.com")),
              list = None,
              owner_id = None
            )
          )
          ))

        val final_result = play.api.test.Helpers.route(testApi, request).get

        val status: Int = play.api.test.Helpers.status(final_result)
        val json: JsValue = play.api.test.Helpers.contentAsJson(final_result)

        final_result.map(res => {

            if (status == 400) {
              val sentResponseData = (json \ "errors")

              sentResponseData.validate[List[ErrorResponseProspectsPostApi]] match {
                case JsError(_) => assert(false)

                case JsSuccess(errors_list, _) =>
                  assert(errors_list.exists(e => {
                    e.error_type.toString == ProspectErrorType.CustomFieldNotFound.toString || e.error_type.toString == ProspectErrorType.DuplicateEmailsFound.toString
                  }))
              }

            } else {
              assert(false)
            }

          })
          .recover { case e => assert(false)
          }

      }


      it("should return success if only email is there in requestBody") {
        val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
        val apiKey = initialData.teamUserLevelKey
        val sendingUrl: String = s"/api/v3/prospects?tid=80"

        //each time we run this test we are inserting new prospect so it should be unique
        val prospect_email: String = Helpers.generateRandomString(8) + "@gmail.com"

        val prospectCreateFormdataOnlyEmail = ProspectCreateFormDataV2(
          email = Some(prospect_email),
          first_name = None,
          last_name = None,
          custom_fields = None,

          owner_id = None,

          list = None,
          company = None,
          city = None,
          country = None,
          timezone = None,
          created_at = None,

          state = None,
          job_title = None,
          phone_number = None,
          linkedin_url = None,
          tags = None
        )

        val request = FakeRequest(play.api.test.Helpers.POST, sendingUrl)
          .withHeaders("X-API-KEY" -> apiKey,
            "Content-Type" -> "application/json")
          .withJsonBody(Json.toJson(Seq(
            prospectCreateFormdataOnlyEmail
          )
          ))

        val final_result = play.api.test.Helpers.route(testApi, request).get

        val status: Int = play.api.test.Helpers.status(final_result)
        val json: JsValue = play.api.test.Helpers.contentAsJson(final_result)

        final_result.map(res => {

            if (status == 200) {
              val sentResponseData: JsValue = json

              sentResponseData.validate[List[ProspectObjectForApiIntegrationTest]] match {
                case JsError(_) => assert(false)

                case JsSuccess(prospect_data_list, _) =>
                  assert(prospect_data_list.flatMap(_.emails.map(_.email)).contains(prospect_email))
              }

            } else {
              assert(false)
            }

          })
          .recover { case e => assert(false)
          }

      }

      it("should return custom fields if it is inserted") {
        val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
        val accountId: Long = initialData.account.internal_id
        val teamId: TeamId = TeamId(initialData.head_team_id)
        val taId = initialData.account.teams.head.access_members.head.ta_id
        val apiKey = initialData.teamUserLevelKey
        val sendingUrl: String = s"/api/v3/prospects"

        //each time we run this test we are inserting new prospect so it should be unique
        val prospect_email: String = Helpers.generateRandomString(8) + "@gmail.com"

        val prospectCreateFormdataCF = ProspectCreateFormDataV2(
          email = Some(prospect_email),
          first_name = None,
          last_name = None,
          custom_fields = Some(Json.obj("webb" -> "sales.com")),

          owner_id = None,

          list = None,
          company = None,
          city = None,
          country = None,
          timezone = None,
          created_at = None,

          state = None,
          job_title = None,
          phone_number = None,
          linkedin_url = None,
          tags = None
        )
        prospectColumnDefDAO.create(
          accountId = accountId,
          teamId = teamId.id,
          taId = taId,
          data = CustomColumnDefCreateForm(
            name = "webb", field_type = FieldTypeEnum.TEXT,
            magic_prompt = None,
          ),
          parsedColumnName = "webb", columnType = "prospect") match {
          case None => assert(false)
          case Some(columnCreated) =>

            val request2 = FakeRequest(play.api.test.Helpers.POST, sendingUrl)
              .withHeaders("X-API-KEY" -> apiKey,
                "Content-Type" -> "application/json")
              .withJsonBody(Json.toJson(Seq(
                prospectCreateFormdataCF
              )
              ))

            val final_result2 = play.api.test.Helpers.route(testApi, request2).get

            val status2: Int = play.api.test.Helpers.status(final_result2)
            val json2: JsValue = play.api.test.Helpers.contentAsJson(final_result2)

            final_result2.map(res => {

                if (status2 == 200) {
                  val sentResponseData: JsValue = json2

                  sentResponseData.validate[List[ProspectObjectForApiIntegrationTest]] match {
                    case JsError(_) => assert(false)

                    case JsSuccess(prospect_data_list, _) =>
                      assert(prospect_data_list.exists(_.custom_fields.toString() == Json.obj("webb" -> "sales.com").toString()))
                  }

                } else {
                  assert(false)
                }

              })
              .recover { case e =>
                println(s"Error:: ${LogHelpers.getStackTraceAsString(e)}")
                assert(false)
              }

        }
      }

      it("should return each and every field which is inserted") {
        val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
        val accountId: Long = initialData.account.internal_id
        val teamId: TeamId = TeamId(initialData.account.teams.head.team_id)
        val taId = initialData.account.teams.head.access_members.head.ta_id
        val apiKey = initialData.teamUserLevelKey
        val sendingUrl: String = s"/api/v3/prospects"

        //each time we run this test we are inserting new prospect so it should be unique
        val prospect_email: String = Helpers.generateRandomString(8) + "@gmail.com"

        val pcfd = ProspectCreateFormDataV2(
          email = Some(prospect_email),
          first_name = Some("John"),
          last_name = Some("Doe"),
          custom_fields = Some(Json.obj("webb" -> "sales.com")),

          owner_id = None,

          list = Some("new"),
          company = Some("Sales"),
          city = Some("Pune"),
          country = Some("India"),
          timezone = Some("Asia/Kolkata"),
          created_at = None,

          state = Some("Maharashtra"),
          job_title = Some("CEO"),
          phone_number = Some("+************"),
          linkedin_url = Some("linkedin.com/johndoe"),
          tags = Some(Seq("prstag"))
        )

        prospectColumnDefDAO.create(
          accountId = accountId,
          teamId = teamId.id,
          taId = taId,
          data = CustomColumnDefCreateForm(
            name = "webb", field_type = FieldTypeEnum.TEXT,
            magic_prompt = None,
          ),
          parsedColumnName = "webb", columnType = "prospect") match {
          case None => assert(false)
          case Some(_) =>

            val request = FakeRequest(play.api.test.Helpers.POST, sendingUrl)
              .withHeaders("X-API-KEY" -> apiKey,
                "Content-Type" -> "application/json")
              .withJsonBody(Json.toJson(Seq(
                pcfd
              )
              ))

            val final_result = play.api.test.Helpers.route(testApi, request).get

            val status: Int = play.api.test.Helpers.status(final_result)
            val json: JsValue = play.api.test.Helpers.contentAsJson(final_result)

            final_result.map(res => {

                if (status == 200) {
                  val sentResponseData: JsValue = json

                  sentResponseData.validate[List[ProspectObjectForApiIntegrationTest]] match {
                    case JsError(_) => assert(false)

                    case JsSuccess(prospect_data_list, _) =>
                      assert(prospect_data_list.flatMap(_.emails.map(_.email)).contains(prospect_email)
                        && prospect_data_list.flatMap(_.emails.map(_.is_valid)).contains(true)
                        && prospect_data_list.flatMap(_.emails.map(_.is_primary)).contains(true)
                      )
                      assert(prospect_data_list.exists(_.first_name == pcfd.first_name))
                      assert(prospect_data_list.exists(_.last_name == pcfd.last_name))
                      assert(prospect_data_list.exists(_.list == pcfd.list))
                      assert(prospect_data_list.exists(_.company == pcfd.company))
                      assert(prospect_data_list.exists(_.city == pcfd.city))
                      assert(prospect_data_list.exists(_.country == pcfd.country))
                      assert(prospect_data_list.exists(_.timezone == pcfd.timezone))
                      assert(prospect_data_list.exists(_.state == pcfd.state))
                      assert(prospect_data_list.exists(_.phone_numbers.contains(pcfd.phone_number.get)))
                      assert(prospect_data_list.exists(_.linkedin_url.get.contains(pcfd.linkedin_url.get)))
                      assert(prospect_data_list.exists(_.tags.map(_.map(_.tag)) == pcfd.tags))
                      assert(prospect_data_list.exists(_.custom_fields.toString() == pcfd.custom_fields.get.toString()))
                  }

                } else {
                  assert(false)
                }

              })
              .recover { case e =>
                println(s"Error:: ${LogHelpers.getStackTraceAsString(e)}")
                assert(false)
              }
        }
      }


    }
    describe("ProspectController.prospectStatusChangeV3") {
      describe("Integration Test for prospect_status_change") {
        it("should return response in expected format for success for prospect status unpause") {
          val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
          val apiKey = initialData.teamUserLevelKey

          val sendingUrl: String = s"/api/v3/prospects/prospect_status_change?tid=80"

          val res = for {

            createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
              initialData = initialData,
            )

            getProspectUuids: List[ProspectUuid] <- Future.fromTry(srUuidService.getProspectUuidFromId(
              prospectIds = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)).toList,
              team_id = initialData.emailSetting.get.team_id
            ))

            request <- Future {
              FakeRequest(play.api.test.Helpers.PUT, sendingUrl)
                .withHeaders("X-API-KEY" -> apiKey,
                  "Content-Type" -> "application/json")
                .withJsonBody(Json.toJson(
                  ProspectStatusChangeRequestBodyTest(
                    prospect_ids = getProspectUuids.map(_.uuid),
                    campaign_ids = Seq(createAndStartCampaignData.campaign.uuid.get),
                    prospect_status = "unpause",
                    will_resume_at = None,
                    will_resume_at_tz = None
                  )
                ))
            }

            response <- play.api.test.Helpers.route(testApi, request).get

          } yield {
            response
          }

          val status: Int = play.api.test.Helpers.status(res)
          val json: JsValue = play.api.test.Helpers.contentAsJson(res)

          res.map(res => {
              if (status == 200) {
                val sentResponseData: JsValue = json

                sentResponseData.validate[ProspectStatusChangeObjectForApiIntegrationTest] match {
                  case JsError(_) => assert(false)

                  case JsSuccess(prospect_status_change_data, _) =>
                    assert(!prospect_status_change_data.prospect_status_updated.head.prospect_status_in_campaign.completed)
                }

              } else {
                assert(false)
              }
            }
            )
            .recover { case e =>
              assert(false)
            }
        }

        it("should return failed response") {
          val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
          val apiKey = initialData.teamUserLevelKey

          val sendingUrl: String = s"/api/v3/prospects/prospect_status_change?tid=80"
          val res = for {

            createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
              initialData = initialData,
            )

            getProspectUuids: List[ProspectUuid] <- Future.fromTry(srUuidService.getProspectUuidFromId(
              prospectIds = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)).toList,
              team_id = initialData.emailSetting.get.team_id
            ))

            request <- Future {
              FakeRequest(play.api.test.Helpers.PUT, sendingUrl)
                .withHeaders("X-API-KEY" -> apiKey,
                  "Content-Type" -> "application/json")
                .withJsonBody(Json.toJson(
                  ProspectStatusChangeRequestBodyTest(
                    prospect_ids = getProspectUuids.map(_.uuid),
                    campaign_ids = Seq(),
                    prospect_status = "unpause",
                    will_resume_at = None,
                    will_resume_at_tz = None
                  )
                ))
            }

            response <- play.api.test.Helpers.route(testApi, request).get

          } yield {
            response
          }

          val status: Int = play.api.test.Helpers.status(res)
          val json: JsValue = play.api.test.Helpers.contentAsJson(res)

          res.map(res => {
              if (status == 400) {
                val sentResponseData = (json \ "errors")
                sentResponseData.validate[List[ErrorResponseUpdateProspectStatusApi]] match {
                  case JsError(_) => assert(false)

                  case JsSuccess(errors_list, _) =>
                    assert(errors_list.exists(e => {
                      e.error_type.toString == UpdateProspectStatusErrorType.BAD_REQUEST.toString
                    }))
                }

              } else {
                assert(false)
              }
            }
            )
            .recover { case e => {
              assert(false)
            }
            }
        }
        it("should return response in expected format for success for prospect status resume_later") {
          val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
          val apiKey = initialData.teamUserLevelKey

          val sendingUrl: String = s"/api/v3/prospects/prospect_status_change?tid=80"
          val res = for {

            createAndStartCampaignData: CreateAndStartCampaignData <- CampaignUtils.createAndStartAutoEmailCampaign(
              initialData = initialData,
            )

            getProspectUuids: List[ProspectUuid] <- Future.fromTry(srUuidService.getProspectUuidFromId(
              prospectIds = createAndStartCampaignData.addProspect.map(p => ProspectId(p.id)).toList,
              team_id = initialData.emailSetting.get.team_id
            ))

            request <- Future {
              FakeRequest(play.api.test.Helpers.PUT, sendingUrl)
                .withHeaders("X-API-KEY" -> apiKey,
                  "Content-Type" -> "application/json")
                .withJsonBody(Json.toJson(
                  ProspectStatusChangeRequestBodyTest(
                    prospect_ids = getProspectUuids.map(_.uuid),
                    campaign_ids = Seq(createAndStartCampaignData.campaign.uuid.get),
                    prospect_status = "resume_later",
                    will_resume_at = willResumeAt,
                    will_resume_at_tz = Option("Asia/kolkata")
                  )
                ))
            }

            response <- play.api.test.Helpers.route(testApi, request).get

          } yield {
            response
          }

          val status: Int = play.api.test.Helpers.status(res)
          val json: JsValue = play.api.test.Helpers.contentAsJson(res)

          res.map(res => {
              if (status == 200) {
                val sentResponseData: JsValue = json

                sentResponseData.validate[ProspectStatusChangeObjectForApiIntegrationTest] match {
                  case JsError(_) => assert(false)

                  case JsSuccess(prospect_status_change_data, _) =>
                    assert(
                      prospect_status_change_data.prospect_status_updated.head.prospect_status_in_campaign.will_resume_at == willResumeAt &&
                        prospect_status_change_data.prospect_status_updated.head.prospect_status_in_campaign.will_resume_at_tz == willResumeAtTz
                    )
                }

              } else {
                assert(false)
              }
            }
            )
            .recover { case e =>
              assert(false)
            }
        }
      }
    }

    describe("testing prospect apis for mobile app") {


      it("get prospect from id api for v2 test - used in mobile app") {

        val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
        val apiKey = initialData.teamUserLevelKey

        val prospectToFetch: ProspectObject = initialData.prospectsResult.head

        val sendingUrl = s"/api/v2/prospects/${prospectToFetch.id}?tid=80"

        val request = FakeRequest(play.api.test.Helpers.GET, sendingUrl)
          .withHeaders("X-API-KEY" -> apiKey,
            "Content-Type" -> "application/json")

        val final_result = play.api.test.Helpers.route(testApi, request).get

        val status: Int = play.api.test.Helpers.status(final_result)
        val json: JsValue = play.api.test.Helpers.contentAsJson(final_result)

        final_result.map(res => {

            if (status == 200) {

              val sentResponseData = (json \ "data" \ "prospect")

              sentResponseData.validate[ProspectObjectV2ForApiIntegrationTest] match {
                case JsError(errors) => {
                  assert(false)
                }

                case JsSuccess(prospect, _) =>
                  assert(prospect.email.equals(prospectToFetch.email.get))
                  assert(prospect.first_name.get.equals(prospectToFetch.first_name.get))
                  assert(prospect.last_name.get.equals(prospectToFetch.last_name.get))
              }

            } else {
              assert(false)
            }

          })
          .recover { case e =>
            assert(false)
          }

      }
    }

    describe("tests to check fields passed in CSV upload flow for email-not-compulsory") {

      it("should give error message if none of the deduplication columns are passed") {

        val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
        val orgId = initialData.account.org.id
        AppConfig.lead_finder_local_test_email_optional_org = AppConfig.lead_finder_local_test_email_optional_org ++ scala.collection.mutable.Set(orgId)

        val column_map = Json.obj(
          "field1" -> "some_field",
          "field2" -> "some_field"
        )

//        (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//          _: TeamId,
//          _: SrRollingUpdateFeature
//        )(_: SRLogger))
//          .expects(*, SrRollingUpdateFeature.EmailNotCompulsory, *)
//          .repeat(1)
//          .returning(true)

        prospectService.checkIfUploadCsvDataIsValid(
          csvData = column_map,
          teamId = TeamId(teamId)
        )(Logger) match {

          case Left(errMsg: String) => assert(errMsg == "Please provide valid data for at least one of these deduplication checks: email, phone, linkedin_url, (first_name, last_name, company)")
          case Right(_) => assert(false)

        }

      }

      it("should give error msg if only first_name is passed") {
        val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
        val orgId = initialData.account.org.id
        AppConfig.lead_finder_local_test_email_optional_org = AppConfig.lead_finder_local_test_email_optional_org ++ scala.collection.mutable.Set(orgId)


//        (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//          _: TeamId,
//          _: SrRollingUpdateFeature
//        )(_: SRLogger))
//          .expects(*, SrRollingUpdateFeature.EmailNotCompulsory, *)
//          .repeat(1)
//          .returning(true)

        val column_map = Json.obj(
          "first_name" -> "Parth",
          "field2" -> "some_field"
        )

        prospectService.checkIfUploadCsvDataIsValid(
          csvData = column_map,
          teamId = TeamId(teamId)
        )(Logger) match {

          case Left(errMsg: String) => assert(errMsg == "Please provide valid data for at least one of these deduplication checks: email, phone, linkedin_url, (first_name, last_name, company)")
          case Right(_) => assert(false)

        }
      }

      it("should pass if only (first_name, last_name, company) is passed") {
        val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
        val orgId = initialData.account.org.id
        AppConfig.lead_finder_local_test_email_optional_org = AppConfig.lead_finder_local_test_email_optional_org ++ scala.collection.mutable.Set(orgId)


//        (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//          _: TeamId,
//          _: SrRollingUpdateFeature
//        )(_: SRLogger))
//          .expects(*, SrRollingUpdateFeature.EmailNotCompulsory, *)
//          .repeat(1)
//          .returning(true)

        val column_map = Json.obj(
          "first_name" -> "Parth",
          "last_name" -> "Gupta",
          "company" -> "Smartreach",
          "field2" -> "some_field"
        )

        prospectService.checkIfUploadCsvDataIsValid(
          csvData = column_map,
          teamId = TeamId(teamId)
        )(Logger) match {

          case Left(_: String) => assert(false)
          case Right(value) => assert(value)

        }
      }

      it("should pass if email, first_name are passed") {

        val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
        val orgId = initialData.account.org.id
        AppConfig.lead_finder_local_test_email_optional_org = AppConfig.lead_finder_local_test_email_optional_org ++ scala.collection.mutable.Set(orgId)


//        (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//          _: TeamId,
//          _: SrRollingUpdateFeature
//        )(_: SRLogger))
//          .expects(*, SrRollingUpdateFeature.EmailNotCompulsory, *)
//          .repeat(1)
//          .returning(true)

        val column_map = Json.obj(
          "email" -> "+************",
          "first_name" -> "Parth",
          "field2" -> "some_field"
        )

        prospectService.checkIfUploadCsvDataIsValid(
          csvData = column_map,
          teamId = TeamId(teamId)
        )(Logger) match {

          case Left(_: String) => assert(false)
          case Right(value) => assert(value)

        }

      }

      it("should pass if one of the deduplication columns are passed") {

        val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get
        val orgId = initialData.account.org.id
        AppConfig.lead_finder_local_test_email_optional_org = AppConfig.lead_finder_local_test_email_optional_org ++ scala.collection.mutable.Set(orgId)

        val column_map = Json.obj(
          "phone" -> "+************",
          "field2" -> "some_field"
        )


//        (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(
//          _: TeamId,
//          _: SrRollingUpdateFeature
//        )(_: SRLogger))
//          .expects(*, SrRollingUpdateFeature.EmailNotCompulsory, *)
//          .repeat(1)
//          .returning(true)

        prospectService.checkIfUploadCsvDataIsValid(
          csvData = column_map,
          teamId = TeamId(teamId)
        )(Logger) match {

          case Left(data: String) => {
            println(s"Error is ${data}")
            assert(false)
          }
          case Right(value) => {
            assert(value)
          }

        }

      }

    }

    describe("Prospect Controller getCsvUploadLogs check") {
      it("should success and return the latest csv uploads") {

        val initialData: InitialData = NewAccountAndEmailSettingData.createNewAccountAndEmailSettingData().get

        val columnMap: JsValue = Json.obj(
          "emails" -> "email"
        )


        val csvCreateForm: List[CsvQueueCreateFormDataV2] = List.fill(10)(
          CsvQueueCreateFormDataV2(
            file_url = "https://testing.com/upload.csv",
            column_map = columnMap,
            force_update_prospects = Some(false),
            csv_upload_type = CsvUploadType.PROSPECTUPLOAD,
            list_name = None,
            campaign_id = None,
            tags = None,
            force_change_ownership = Some(false),
            ignore_email_empty_rows = Some(false),
            ignore_prospects_active_in_other_campaigns = Some(false),
            deduplication_columns = None,
            ignore_prospects_in_other_campaigns = None
          )
        )

        csvCreateForm.map { csvFile =>

          CsvQueue.createV2(
            accountId = initialData.account.internal_id,
            teamId = Some(initialData.account.teams.head.team_id),
            loggedin_id = initialData.account.internal_id,
            ta_id = Some(initialData.account.teams.head.team_id),
            data = csvFile
          )
        }

        val sendingUrl: String = s"/api/v2/settings/csv_upload_logs?tid=${initialData.account.teams.head.team_id}"


        val request = FakeRequest(play.api.test.Helpers.GET, sendingUrl)
          .withHeaders("X-API-KEY" -> initialData.teamUserLevelKey,
            "Content-Type" -> "application/json")

        val final_result = play.api.test.Helpers.route(testApi, request).get

        val status: Int = play.api.test.Helpers.status(final_result)
        val json: JsValue = play.api.test.Helpers.contentAsJson(final_result)

        final_result.map(res =>
            if (status == 200) {
              assert(true)

            } else {
              println(json)
              assert(false)
            }
          )
          .recover { case e =>
            println(e)
            assert(false)
          }

      }
    }

  }
}
