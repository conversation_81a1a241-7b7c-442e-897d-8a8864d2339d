package app.api.prospects

import api.{ApiService, AppConfig}
import api.accounts.{Account, AccountDAO, AccountUuid, RepTrackingHosts, TeamId}
import api.call.models.CallType
import api.prospects.{NewProspectEventV2, ProspectAccountDAO1, ProspectEventDAO, ProspectEventDetails, ProspectEventService, ProspectEventV2, ProspectUuid}
import api.prospects.{ExactIdToCompareTime, InferredQueryTimeline}
import org.joda.time.DateTime
import org.scalamock.scalatest.AsyncMockFactory
import org.scalatest.funspec.AsyncFunSpec
import api.prospects.dao_service.{ProspectDAOService, ProspectEventDAOService}
import api.prospects.models.ProspectId
import api.sr_audit_logs.models.EventType
import app.test_fixtures.inbox_service.InboxV3ServiceFixtures
import app.test_fixtures.prospect.ProspectFixtures
import eventframework.{ProspectObject, ProspectObjectInternal}
import play.api.libs.json.Json
import scalikejdbc.{NoExtractor, S<PERSON>, scalikejdbcSQLInterpolationImplicitDef}
import utils.{<PERSON><PERSON>, <PERSON>RLogger, SRLogger}
import org.joda.time.DateTimeUtils
import org.scalamock.matchers.ArgCapture.CaptureOne
import utils_deploy.rolling_updates.models.SrRollingUpdateFeature
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService

import scala.util.{Failure, Success}

class ProspectEventServiceSpec extends AsyncFunSpec with AsyncMockFactory {


  val prospectEventDAOMock = mock[ProspectEventDAO]
  val prospectDAOServiceMock = mock[ProspectDAOService]
  val prospectAccountDAOMock = mock[ProspectAccountDAO1]
  val srRollingUpdateCoreService = mock[SrRollingUpdateCoreService]


  val prospectEventDAOService = new ProspectEventDAOService(
    prospectEventDAO = prospectEventDAOMock,
    srRollingUpdateCoreService = srRollingUpdateCoreService )

  val prospectEventService = new ProspectEventService(
    prospectEventDAO = prospectEventDAOMock,
    prospectDAOService = prospectDAOServiceMock,
    prospectAccountDAO = prospectAccountDAOMock,
    prospectEventDAOService = prospectEventDAOService

  )
  val account = InboxV3ServiceFixtures.accountAdmin

  val dateTime = DateTime.now()


  DateTimeUtils.setCurrentMillisFixed(dateTime.getMillis)

  given logger: SRLogger = new SRLogger("ProspectEventServiceSpec")

  val prospectObject = ProspectObject(
    id = 3L,
    owner_id = 5L,
    team_id = 3L,
    first_name = Some("Shubham"),
    last_name = Some("Kudekar"),
    email = Some("<EMAIL>"),
    custom_fields = Json.obj(),

    list = None,

    company = None,
    job_title = None,
    phone = None,
    phone_2 = None,
    phone_3 = None,

    linkedin_url = None,

    city = None,
    state = None,
    country = None,
    timezone = None,
    latest_reply_sentiment_uuid = None,

    prospect_category = "",

    internal = {

      ProspectFixtures.prospectObjectInternal
    },
    current_step_type = None,
    latest_task_done_at = None,
    last_contacted_at = None,
    last_contacted_at_phone = None,
    created_at = dateTime.minusHours(4),
    prospect_uuid = Some(ProspectUuid("prs_aa_abcdefghi")),
    owner_uuid = AccountUuid("acc_aa_abcdegfhi"),
    updated_at = DateTime.now()
  )

  val repTrackingHosts = Seq(RepTrackingHosts(id = 3, host_url = "smartreach.io", subdomain_based = false, active = true))

  val baseProspectEventV2 = ProspectEventV2(
    event_type = EventType.PROSPECT_CREATED,
    doer_account_id = Some(1L),
    doer_account_name = Some("Shubham Kudekar"),
    assigned_to_account_id = Some(1L),
    assigned_to_account_name = Some("Shubham Kudekar"),
    old_category = None,
    new_category = None,
    new_reply_sentiment_uuid = None,
    prospect_id = 1L,
    prospect_account_id = Some(1L),
    prospect_first_name = Some("Naruto"),
    prospect_last_name = Some("Uzumaki"),
    prospect_email = Some("<EMAIL>"),
    prospect_source = None,
    campaign_id = Some(1L),
    campaign_name = Some("Shippudden Campaign"),
    step_id = Some(1L),
    step_name = Some("Day1:Opening"),
    clicked_url = None,
    created_at = dateTime,
    email_db_id = None,
    email_thread_id = Some(1234L),
    email_from_email = Some("<EMAIL>"),
    email_from_name = Some("Shubham Kudekar"),
    email_to_email = Some("<EMAIL>"),
    email_to_name = Some("Naruto Uzumaki"),
    email_reply_to_email = Some("<EMAIL>"),
    email_reply_to_name = Some("Shubham Kudekar"),
    email_cc_emails = None,
    email_bcc_emails = None,
    email_subject = Some("subject"),
    email_body = Some("Hi Brother"),
    email_body_preview = Some("Hi Brother"),
    email_opened = None,
    email_opened_at = None,
    task_type = None,
    channel_type = None,
    recording_url = None,
    task_uuid = None,
    call_note = List(),

    note = None,
    note_id = None,
    note_updated_at = None,

    call_completed_at = None,

    reply_sentiment = None,
    reply_sentiment_uuid = None,
    reply_sentiment_type = None,
    duplicates_merged_at = None,
    total_merged_prospects = None,
    potential_duplicate_prospect_id = None,
    conference_uuid = None,
    conference_reply_sentiment_uuid = None
  )

  val anotherBaseProspectEventV2 = ProspectEventV2(


    event_type = EventType.PROSPECT_CREATED, // dummy

    doer_account_id = None,
    doer_account_name = None,

    // for assigned team member
    assigned_to_account_id = None,
    assigned_to_account_name = None,

    // changed category
    old_category = None,
    new_category = None,
    new_reply_sentiment_uuid = None,

    prospect_id = 0,
    prospect_account_id = None,

    prospect_first_name = None,
    prospect_last_name = None,
    prospect_email = None,
    prospect_source = None,

    campaign_id = None,
    campaign_name = None,

    step_id = None,
    step_name = None,

    clicked_url = None,

    created_at = dateTime, // dummy


    // email message specific //
    email_db_id = None,
    email_thread_id = None,

    email_from_email = None,
    email_from_name = None,

    email_to_email = None,
    email_to_name = None,

    email_reply_to_email = None,
    email_reply_to_name = None,

    email_cc_emails = None,
    email_bcc_emails = None,

    email_subject = None,
    email_body = None,
    email_body_preview = None,

    email_opened = None,
    email_opened_at = None,

    task_type = None,
    channel_type = None,
    recording_url = None,
    task_uuid = None,
    call_note = List(),

    note = None,
    note_id = None,
    note_updated_at = None,

    call_completed_at = None,

    reply_sentiment = None,
    reply_sentiment_uuid = None,
    reply_sentiment_type = None,

    duplicates_merged_at = None,
    total_merged_prospects = None,
    potential_duplicate_prospect_id = None,
    conference_uuid = None,
    conference_reply_sentiment_uuid = None

  )

  val baseProspectEventDetails = ProspectEventDetails(
    prospectEventV2 = baseProspectEventV2,
    openCount = 0,
    clickCount = 0
  )

  val baseNewProspectEvent = NewProspectEventV2(
    uniqId = 1234,
    created_at = dateTime,
    prospectEventDetails = Seq(baseProspectEventDetails)
  )
  val prospectEvents = Seq(baseNewProspectEvent.copy(prospectEventDetails = Seq(
    baseProspectEventDetails.
      copy(
        prospectEventV2 = baseProspectEventV2.copy(
          created_at = dateTime,
          event_type = EventType.EMAIL_OPENED
        ),
        openCount = 10,
        clickCount = 0
      ),

    baseProspectEventDetails
      .copy(
        prospectEventV2 = baseProspectEventV2.copy(
          created_at = dateTime.minusDays(1),
          event_type = EventType.NEW_REPLY
        ),
        openCount = 0,
        clickCount = 0
      ),

    baseProspectEventDetails
      .copy(
        prospectEventV2 = baseProspectEventV2.copy(
          created_at = dateTime.minusDays(2),
          event_type = EventType.EMAIL_OPENED
        ),
        openCount = 5,
        clickCount = 0
      ),

    baseProspectEventDetails
      .copy(
        prospectEventV2 = baseProspectEventV2.copy(
          created_at = dateTime.minusDays(3),
          event_type = EventType.EMAIL_SENT
        ),
        openCount = 0,
        clickCount = 0
      )
  )))


  val emailEvents: Seq[ProspectEventV2] = Seq(
    baseProspectEventV2.copy(event_type = EventType.NEW_REPLY, created_at = dateTime.minusDays(1)),
    baseProspectEventV2.copy(event_type = EventType.EMAIL_SENT, created_at = dateTime.minusDays(3))
  )


  val otherEvents: Seq[ProspectEventV2] =

    Seq(
      baseProspectEventV2.copy(event_type = EventType.EMAIL_OPENED, created_at = dateTime),
      baseProspectEventV2.copy(event_type = EventType.EMAIL_OPENED, created_at = dateTime),
      baseProspectEventV2.copy(event_type = EventType.EMAIL_OPENED, created_at = dateTime),
      baseProspectEventV2.copy(event_type = EventType.EMAIL_OPENED, created_at = dateTime),
      baseProspectEventV2.copy(event_type = EventType.EMAIL_OPENED, created_at = dateTime),
      baseProspectEventV2.copy(event_type = EventType.EMAIL_OPENED, created_at = dateTime),
      baseProspectEventV2.copy(event_type = EventType.EMAIL_OPENED, created_at = dateTime),
      baseProspectEventV2.copy(event_type = EventType.EMAIL_OPENED, created_at = dateTime),
      baseProspectEventV2.copy(event_type = EventType.EMAIL_OPENED, created_at = dateTime),
      baseProspectEventV2.copy(event_type = EventType.EMAIL_OPENED, created_at = dateTime),

      baseProspectEventV2.copy(event_type = EventType.EMAIL_OPENED, created_at = dateTime.minusDays(2)),
      baseProspectEventV2.copy(event_type = EventType.EMAIL_OPENED, created_at = dateTime.minusDays(2)),
      baseProspectEventV2.copy(event_type = EventType.EMAIL_OPENED, created_at = dateTime.minusDays(2)),
      baseProspectEventV2.copy(event_type = EventType.EMAIL_OPENED, created_at = dateTime.minusDays(2)),
      baseProspectEventV2.copy(event_type = EventType.EMAIL_OPENED, created_at = dateTime.minusDays(2)),
    )

  val allEmailMessageEventsMap = Map(
    1L -> Seq(baseProspectEventV2.copy(event_type = EventType.NEW_REPLY), baseProspectEventV2.copy(event_type = EventType.EMAIL_OPENED)),
    2L -> Seq(baseProspectEventV2.copy(event_type = EventType.NEW_REPLY))
  )

  val openAndClickedEventsMap = Map(
    1L -> Seq(baseProspectEventV2.copy(event_type = EventType.EMAIL_SENT)),
    3L -> Seq(baseProspectEventV2.copy(event_type = EventType.EMAIL_LINK_CLICKED))
  )

  val finalEmailMessageEventsMap = Map(
    1L -> Seq(
      baseProspectEventV2.copy(event_type = EventType.EMAIL_OPENED, created_at = dateTime, email_thread_id = Some(1L)),
      baseProspectEventV2.copy(event_type = EventType.EMAIL_OPENED, created_at = dateTime, email_thread_id = Some(1L)),
      baseProspectEventV2.copy(event_type = EventType.EMAIL_OPENED, created_at = dateTime, email_thread_id = Some(1L)),
      baseProspectEventV2.copy(event_type = EventType.NEW_REPLY, created_at = dateTime.minusDays(1), email_thread_id = Some(1L)),
      baseProspectEventV2.copy(event_type = EventType.EMAIL_OPENED, created_at = dateTime.minusDays(2), email_thread_id = Some(1L)),
      baseProspectEventV2.copy(event_type = EventType.EMAIL_SENT, created_at = dateTime.minusDays(3), email_thread_id = Some(1L))
    ),
    2L -> Seq(
      baseProspectEventV2.copy(event_type = EventType.EMAIL_OPENED, created_at = dateTime, email_thread_id = Some(2L)),
      baseProspectEventV2.copy(event_type = EventType.EMAIL_LINK_CLICKED, created_at = dateTime, email_thread_id = Some(2L)),
    ),
    3L -> Seq(
      baseProspectEventV2.copy(event_type = EventType.NEW_REPLY, created_at = dateTime, email_thread_id = Some(3L)),
      baseProspectEventV2.copy(event_type = EventType.EMAIL_OPENED, created_at = dateTime.minusDays(1), email_thread_id = Some(3L)),
      baseProspectEventV2.copy(event_type = EventType.EMAIL_OPENED, created_at = dateTime.minusDays(1), email_thread_id = Some(3L)),
      baseProspectEventV2.copy(event_type = EventType.EMAIL_OPENED, created_at = dateTime.minusDays(1), email_thread_id = Some(3L)),
      baseProspectEventV2.copy(event_type = EventType.EMAIL_LINK_CLICKED, created_at = dateTime.minusDays(3), email_thread_id = Some(3L)),
    )
  )

  val expectedResult: Seq[NewProspectEventV2] = Seq(
    NewProspectEventV2(1L, dateTime, Seq(
      ProspectEventDetails(baseProspectEventV2.copy(event_type = EventType.EMAIL_OPENED, created_at = dateTime, email_thread_id = Some(1L)), 3, 0),
      ProspectEventDetails(baseProspectEventV2.copy(event_type = EventType.NEW_REPLY, created_at = dateTime.minusDays(1), email_thread_id = Some(1L)), 0, 0),
      ProspectEventDetails(baseProspectEventV2.copy(event_type = EventType.EMAIL_OPENED, created_at = dateTime.minusDays(2), email_thread_id = Some(1L)), 1, 0),
      ProspectEventDetails(baseProspectEventV2.copy(event_type = EventType.EMAIL_SENT, created_at = dateTime.minusDays(3), email_thread_id = Some(1L)), 0, 0)
    )),
    NewProspectEventV2(2, dateTime,
      Seq(
        ProspectEventDetails(baseProspectEventV2.copy(event_type = EventType.EMAIL_OPENED, created_at = dateTime, email_thread_id = Some(2L)), 1, 0),
        ProspectEventDetails(baseProspectEventV2.copy(event_type = EventType.EMAIL_LINK_CLICKED, created_at = dateTime, email_thread_id = Some(2L)), 0, 1)
      )),
    NewProspectEventV2(3, dateTime,
      Seq(
        ProspectEventDetails(baseProspectEventV2.copy(event_type = EventType.NEW_REPLY, created_at = dateTime, email_thread_id = Some(3L)), 0, 0),
        ProspectEventDetails(baseProspectEventV2.copy(event_type = EventType.EMAIL_OPENED, created_at = dateTime.minusDays(1), email_thread_id = Some(3L)), 3, 0),
        ProspectEventDetails(baseProspectEventV2.copy(event_type = EventType.EMAIL_LINK_CLICKED, created_at = dateTime.minusDays(3), email_thread_id = Some(3L)), 0, 1)
      )))

  describe("Testing ProspectEventsDAO.getNewProspectEventV2List") {

    it("should match the expectedResult") {

      val result = ProspectEventDAO.getNewProspectEventV2List(
        allEvents = emailEvents ++ otherEvents
      )
      logger.warn(result.toString())

      assert(result == prospectEvents)
    }

    it("should inc the count if increase the count of open event") {
      val result = ProspectEventDAO.getNewProspectEventV2List(
        allEvents = emailEvents ++ otherEvents ++ Seq(baseProspectEventV2.copy(event_type = EventType.EMAIL_OPENED, created_at = dateTime.minusDays(2)))
      )
      assert(result.head.prospectEventDetails(2).openCount == prospectEvents.head.prospectEventDetails(2).openCount + 1)
    }

    it("should create a separate open event since the created at is different") {
      val result = ProspectEventDAO.getNewProspectEventV2List(
        allEvents = emailEvents ++ otherEvents ++ Seq(baseProspectEventV2.copy(event_type = EventType.EMAIL_OPENED, created_at = dateTime.minusDays(3)))
      )

      assert(result.head.prospectEventDetails.length == 5 &&
        result.head.prospectEventDetails(3).prospectEventV2.event_type == EventType.EMAIL_OPENED &&
        result.head.prospectEventDetails(3).openCount == 1
      )
    }

  }


  describe("Testing ProspectEventsDAO.mergeProspectEventMaps ") {
    it("should concatenate the values if the two maps have common keys") {
      val result = ProspectEventDAO.mergeProspectEventMaps(
        allEmailMessageEventsForProspectMap = allEmailMessageEventsMap,
        openedAndClickedEventsMap = openAndClickedEventsMap
      )
      assert(result == Map(
        1L -> Seq(
          baseProspectEventV2.copy(event_type = EventType.NEW_REPLY),
          baseProspectEventV2.copy(event_type = EventType.EMAIL_OPENED),
          baseProspectEventV2.copy(event_type = EventType.EMAIL_SENT)),

        2L -> Seq(baseProspectEventV2.copy(event_type = EventType.NEW_REPLY)),

        3L -> Seq(baseProspectEventV2.copy(event_type = EventType.EMAIL_LINK_CLICKED))
      ))
    }

    it("should merge a empty and non empty map") {
      val result = ProspectEventDAO.mergeProspectEventMaps(
        allEmailMessageEventsForProspectMap = Map(),
        openedAndClickedEventsMap = openAndClickedEventsMap
      )
      assert(result == openAndClickedEventsMap)
    }

    it("should work correctly if a common key is removed from one map") {
      val result = ProspectEventDAO.mergeProspectEventMaps(
        allEmailMessageEventsForProspectMap = allEmailMessageEventsMap - 1L,
        openedAndClickedEventsMap = openAndClickedEventsMap
      )
      assert(result == Map(
        1L -> Seq(baseProspectEventV2.copy(event_type = EventType.EMAIL_SENT)),

        2L -> Seq(baseProspectEventV2.copy(event_type = EventType.NEW_REPLY)),

        3L -> Seq(baseProspectEventV2.copy(event_type = EventType.EMAIL_LINK_CLICKED))
      ))
    }

  }

  describe("ProspectEventDAO.generateFromMap") {
    it("should match the expected result") {
      val result = ProspectEventDAO.getEmailEventsFromMap(finalEmailMessageEventsMap = finalEmailMessageEventsMap)
      assert(result == expectedResult)
    }

    it("should create one more NewProspectEventV2 if a new key value pair is added to map") {
      val result = ProspectEventDAO.getEmailEventsFromMap(finalEmailMessageEventsMap = finalEmailMessageEventsMap + (4L -> Seq(
        baseProspectEventV2.copy(event_type = EventType.NEW_REPLY, created_at = dateTime, email_thread_id = Some(3L))),
        ))
      assert(result.length == 4 && result(3).prospectEventDetails.head.prospectEventV2.event_type == EventType.NEW_REPLY)
    }

    it("should add it to the open event and also increase the count") {
      val updatedSeq = finalEmailMessageEventsMap(1) :+ baseProspectEventV2.copy(event_type = EventType.EMAIL_OPENED, created_at = dateTime, email_thread_id = Some(1L))
      val result = ProspectEventDAO.getEmailEventsFromMap(finalEmailMessageEventsMap = finalEmailMessageEventsMap.updated(1L, updatedSeq))
      assert(result.head.prospectEventDetails.head.openCount == 4)
    }

  }


  describe("Testing the milliseconds mismatch case in prospectEventsService.getEventsV2") {

    val expectedQuery1 =
      sql"""
           |select
           |        e.event_type,
           |        e.doer_account_id,
           |        e.doer_account_name,
           |        e.assigned_to_account_id,
           |        e.assigned_to_account_name,
           |        e.old_category,
           |        e.new_category,
           |        e.campaign_id,
           |        e.campaign_name,
           |        e.step_id,
           |        e.step_name,
           |        e.clicked_url,
           |        e.created_at,
           |        e.task_type,
           |        e.channel_type,
           |        e.email_thread_id,
           |        e.email_scheduled_id,
           |        e.reply_sentiment_uuid as new_reply_sentiment_uuid,
           |        p.prospect_account_id,
           |        p.first_name AS prospect_first_name,
           |        p.last_name AS prospect_last_name,
           |        pe.email AS prospect_email,
           |        ccl.recording_url as recording_url,
           |        ccl.conference_uuid,
           |        ccl.reply_sentiment_uuid as conference_reply_sentiment_uuid,
           |        notes.id as note_id,
           |        notes.note as call_note,
           |        notes.created_at as call_note_updated_at,
           |        cs.owner_account_id as call_note_added_by_id,
           |        rs.uuid as reply_sentiment_uuid,
           |        rs.reply_sentiment_type,
           |        rs.reply_sentiment_name AS reply_sentiment,
           |        ccl.completed_at AS call_completed_at,
           |        CONCAT( cs.first_name, ' ', cs.last_name)  as call_note_added_by_full_name,
           |        e.task_uuid,
           |        e.duplicates_merged_at,
           |        e.total_merged_prospects,
           |        e.potential_duplicate_prospect_id
           |
           |        from prospect_events e
           |        inner join prospects p on p.id = e.prospect_id and p.team_id =?
           |        left join prospects_emails pe on pe.prospect_id = e.prospect_id and pe.is_primary and pe.team_id =?
           |        left join call_conference_logs ccl on (ccl.task_uuid = e.task_uuid and ccl.team_id = ? and ccl.conference_uuid = e.call_conference_uuid)
           |        left join call_participants_logs cpl on (e.call_conference_uuid = cpl.call_conference_uuid and cpl.call_conference_uuid = ccl.conference_uuid and cpl.team_id = ?)
           |        left join call_settings cs on ( cpl.call_setting_uuid = cs.uuid AND cs.team_id = ? )
           |        left join notes on ( notes.id = cpl.note_id and notes.team_id = ? )
           |        left join tasks t on (t.task_id = e.task_uuid and t.team_id = e.team_id and t.prospect_id = e.prospect_id)
           |        left join reply_sentiments_for_teams rs on (t.reply_sentiment_uuid = rs.uuid and t.team_id = ? )
           |        where
           |        e.prospect_id = ?  and e.team_id = ?
           |        AND e.created_at AT TIME ZONE 'UTC' < ? AT TIME ZONE 'UTC' ORDER BY e.created_at desc LIMIT ?
           |        ;
           |""".stripMargin


    val expectedQuery2 =
      sql"""
           |select
           |            cp.campaign_id,
           |            cp.prospect_id,
           |            cp.opted_out,
           |            cp.opted_out_at,
           |            cp.completed,
           |            cp.completed_at,
           |            cp.paused,
           |            cp.paused_at,
           |            p.first_name AS prospect_first_name,
           |            p.last_name AS prospect_last_name,
           |            pe.email AS prospect_email,
           |            c.name as campaign_name
           |            from campaigns_prospects cp
           |            inner join campaigns c on c.id = cp.campaign_id and c.team_id = cp.team_id
           |            inner join prospects p on p.id = cp.prospect_id and p.team_id = cp.team_id
           |            left join prospects_emails pe on pe.prospect_id = cp.prospect_id and pe.is_primary and pe.team_id = cp.team_id
           |            where p.id = ? and p.team_id = ?
           |
           |            and
           |
           |            ((cp.opted_out IS TRUE AND cp.opted_out_at AT TIME ZONE 'UTC' < ? AT TIME ZONE 'UTC')
           |            OR (cp.completed IS TRUE AND cp.completed_at AT TIME ZONE 'UTC' < ? AT TIME ZONE 'UTC')
           |            OR (cp.paused IS TRUE AND cp.completed IS NOT TRUE AND cp.paused_at AT TIME ZONE 'UTC' < ? AT TIME ZONE 'UTC'))
           |
           |             ORDER BY
           |            CASE
           |              WHEN cp.opted_out IS TRUE THEN cp.opted_out_at
           |              WHEN cp.completed IS TRUE THEN cp.completed_at
           |              WHEN cp.paused IS TRUE AND cp.completed IS NOT TRUE THEN cp.paused_at
           |            END
           |            DESC
           |            LIMIT ?
           |""".stripMargin


    val expectedQuery3 =
      sql"""
           |
           |  select
           |  e.campaign_id,
           |        e.prospect_id AS prospect_id,
           |        e.step_id,
           |        e.id,
           |        ems.email_thread_id,
           |
           |        ems.from_email,
           |        ems.from_name,
           |
           |        ems.to_email,
           |        ems.to_name,
           |
           |        ems.reply_to_email,
           |        ems.reply_to_name,
           |
           |        ems.cc_emails,
           |        ems.bcc_emails,
           |
           |        ems.subject,
           |        ems.message_id,
           |        ems.body,
           |        e.sent_at,
           |        e.opened,
           |        e.opened_at,
           |
           |        e.bounced,
           |        e.bounced_at,
           |
           |        e.campaign_name,
           |        e.step_name,
           |
           |        p.first_name AS prospect_first_name,
           |        p.last_name AS prospect_last_name,
           |        pe.email AS prospect_email,
           |
           |        -- c.name as campaign_name,
           |        -- cs.label as step_name,
           |        (case when e.scheduled_from_campaign or e.scheduled_manually then true else false end) as by_account
           |        from emails_scheduled e
           |        inner join email_message_data ems on ems.team_id = e.team_id and e.id = ems.es_id
           |        left join prospects p on p.id = e.prospect_id
           |        left join prospects_emails pe on pe.prospect_id = e.prospect_id and pe.is_primary
           |        left join email_threads et on et.id = e.email_thread_id
           |        -- left join campaigns c on c.id = e.campaign_id
           |        -- left join campaign_steps cs on cs.id = e.step_id
           |        where e.team_id = ?
           |  AND e.prospect_id IS NOT NULL
           |  AND e.prospect_id = ?
           |  and e.sent
           |  AND
           |  ((
           |  (e.scheduled_from_campaign or e.scheduled_manually is true)
           |  AND e.sent_at AT TIME ZONE 'UTC' < ? AT TIME ZONE 'UTC')
           |  OR (e.bounced is not null and e.bounced and e.bounced_at AT TIME ZONE 'UTC' < ? AT TIME ZONE 'UTC')
           |  OR (e.replied and e.replied_at AT TIME ZONE 'UTC' < ? AT TIME ZONE 'UTC'))
           |   ORDER BY
           | CASE
           |   WHEN (e.scheduled_from_campaign or e.scheduled_manually) IS TRUE THEN e.sent_at
           |   WHEN e.bounced is not null and e.bounced IS TRUE THEN e.bounced_at
           |   WHEN e.replied IS TRUE THEN e.replied_at
           | END
           | DESC
           | LIMIT ?
           |""".stripMargin

    val getNotesEventsExpectedQuery =
      sql"""
        SELECT
          notes.id,
          notes.prospect_id,
          notes.added_by,
          notes.task_uuid,
          notes.created_at,
          notes.updated_at,
          notes.note

        FROM notes
        LEFT JOIN call_participants_logs cpl ON ( cpl.note_id = notes.id AND cpl.team_id = notes.team_id )
        WHERE notes.prospect_id = ?
        AND cpl.note_id IS NULL
        AND notes.team_id = ?
        AND notes.created_at AT TIME ZONE 'UTC' < ? AT TIME ZONE 'UTC' ORDER BY notes.created_at desc LIMIT ?;
      """.stripMargin

    val queryTimeline_Before = InferredQueryTimeline.Range.Before(DateTime.now())
    val queryTimeline_After = InferredQueryTimeline.Range.After(DateTime.now())
    val queryTimeline_Exact = InferredQueryTimeline.Exact(exactIdToCompareTime = ExactIdToCompareTime(id = ""))

    val limit = AppConfig.prospectEventsListingRowsPerPage

    val teamId = TeamId(3L)

    it("should result in non empty event as everything is in seconds, and the created at is between from time and till time") {

      val query1 = CaptureOne[SQL[Nothing, NoExtractor]]()

      (prospectEventDAOMock.getOtherEvents(_: SQL[Nothing, NoExtractor], _: ProspectEventV2, _: Option[Long], _: Long, _: Option[Account]))
        .expects(
          capture(query1),
          baseProspectEventV2,
          Some(prospectObject.id),
          prospectObject.team_id,
          Some(account)
        ).returning(Success(List()))

      val query2 = CaptureOne[SQL[Nothing, NoExtractor]]()
      (prospectEventDAOMock.getCampaignEvents(_: SQL[Nothing, NoExtractor], _: ProspectEventV2, _: Option[Long], _: Option[Long]))
        .expects(
          capture(query2),
          baseProspectEventV2,
          Some(prospectObject.id),
          None
        ).returning(Success(List()))


      val query3 = CaptureOne[SQL[Nothing, NoExtractor]]()
      (prospectEventDAOMock.getEmailMessageEvents(_: SQL[Nothing, NoExtractor], _: Seq[RepTrackingHosts]))
        .expects(
          capture(query3),
          repTrackingHosts
        ).returning(Success(List()))

      val query4 = CaptureOne[SQL[Nothing, NoExtractor]]()
      (prospectEventDAOMock.getNotesEvents(_: SQL[Nothing, NoExtractor], _: ProspectEventV2))
        .expects(
          capture(query4),
          baseProspectEventV2
        ).returning(Success(List()))

//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates (_: TeamId, _: SrRollingUpdateFeature)(_: ISRLogger))
//      .expects(teamId, SrRollingUpdateFeature.UseEmailMessageDataTable, *)
//        .returning(false)

      val result = prospectEventService.getAllEvents(
        queryTimeline = queryTimeline_Before,
        limit = limit,
        prospectId = Some(ProspectId(3L)),
        prospectAccountId = None,
        repTrackingHosts = repTrackingHosts,
        prospectAccountObjectIfAlreadyThere = None,
        prospectObjectIfAlreadyThere = Some(prospectObject),
        includeMessageEventForEmailThreadId = None,
        teamId = teamId,
        account = Some(account),
        baseEvent = baseProspectEventV2
      )

      result match {
        case Failure(e) => println(s"${e.getMessage}")
          assert(false)
        case Success(events) => {
          logger.warn("entered here")
          assert(events.nonEmpty && events.head.event_type == EventType.PROSPECT_CREATED
          )
          assert(Helpers.compareSQLQueriesInTest(expectedQuery = expectedQuery1.statement, actualQuery = query1.value.statement))
          assert(Helpers.compareSQLQueriesInTest(expectedQuery = expectedQuery2.statement, actualQuery = query2.value.statement))
          assert(Helpers.compareSQLQueriesInTest(expectedQuery = expectedQuery3.statement, actualQuery = query3.value.statement))
          assert(Helpers.compareSQLQueriesInTest(expectedQuery = getNotesEventsExpectedQuery.statement, actualQuery = query4.value.statement))
        }
      }
    }
    it("should not give ADDED_PROSPECT as next timestamp is milliseconds and tillTimeSeconds will always ne greater, and the created at is between from time and till time") {
      val next_page_timestamp_starts = 1708950497L

      val query1 = CaptureOne[SQL[Nothing, NoExtractor]]()

      (prospectEventDAOMock.getOtherEvents(_: SQL[Nothing, NoExtractor], _: ProspectEventV2, _: Option[Long], _: Long, _: Option[Account]))
        .expects(
          capture(query1),
          baseProspectEventV2,
          Some(prospectObject.id),
          prospectObject.team_id,
          Some(account)
        ).returning(Success(List()))

      val query2 = CaptureOne[SQL[Nothing, NoExtractor]]()
      (prospectEventDAOMock.getCampaignEvents(_: SQL[Nothing, NoExtractor], _: ProspectEventV2, _: Option[Long], _: Option[Long]))
        .expects(
          capture(query2),
          baseProspectEventV2,
          Some(prospectObject.id),
          None
        ).returning(Success(List()))


      val query3 = CaptureOne[SQL[Nothing, NoExtractor]]()
      (prospectEventDAOMock.getEmailMessageEvents(_: SQL[Nothing, NoExtractor], _: Seq[RepTrackingHosts]))
        .expects(
          capture(query3),
          repTrackingHosts
        ).returning(Success(List(baseProspectEventV2.copy(event_type = EventType.TASK_SNOOZED))))

      val query4 = CaptureOne[SQL[Nothing, NoExtractor]]()
      (prospectEventDAOMock.getNotesEvents(_: SQL[Nothing, NoExtractor], _: ProspectEventV2))
        .expects(
          capture(query4),
          baseProspectEventV2
        ).returning(Success(List()))

      // Returning a dummy event above to break the loop
//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates (_: TeamId, _: SrRollingUpdateFeature)(_: ISRLogger))
//        .expects(teamId, SrRollingUpdateFeature.UseEmailMessageDataTable, *)
//        .returning(false)
      val result = prospectEventService.getAllEvents(
        queryTimeline = InferredQueryTimeline.Range.Before(new DateTime(1686124505260L)),
        limit = limit,
        prospectId = Some(ProspectId(3L)),
        prospectAccountId = None,
        repTrackingHosts = repTrackingHosts,
        prospectAccountObjectIfAlreadyThere = None,
        prospectObjectIfAlreadyThere = Some(prospectObject),
        includeMessageEventForEmailThreadId = None,
        teamId = teamId,
        account = Some(account),
        baseEvent = baseProspectEventV2
      )

      result match {
        case Failure(e) =>
          println(s"${e.getMessage}")
          assert(false)
        case Success(events) => {
          logger.warn("entered here")
          assert(events.nonEmpty &&
            events.length == 1 &&
            events.head.event_type != EventType.PROSPECT_CREATED
          )
          assert(Helpers.compareSQLQueriesInTest(expectedQuery = expectedQuery1.statement, actualQuery = query1.value.statement))
          assert(Helpers.compareSQLQueriesInTest(expectedQuery = expectedQuery2.statement, actualQuery = query2.value.statement))
          assert(Helpers.compareSQLQueriesInTest(expectedQuery = expectedQuery3.statement, actualQuery = query3.value.statement))
          assert(Helpers.compareSQLQueriesInTest(expectedQuery = getNotesEventsExpectedQuery.statement, actualQuery = query4.value.statement))
        }
      }
    }


  }

  describe("test for getAllEvents") {

    val queryTimeline = InferredQueryTimeline.Range.Before(DateTime.now())
    val limit: Int = AppConfig.prospectEventsListingRowsPerPage
    val prospectId = Some(ProspectId(3L))
    val teamId = TeamId(1L)

    it("should give the list of all the events received from different calls") {

      (prospectEventDAOMock.getOtherEvents)
        .expects(*, baseProspectEventV2, prospectId.map(_.id), teamId.id, Some(account))
        .returning(Success(List(
          baseProspectEventV2.copy(
            event_type = EventType.EVENT_V3_PROSPECT_ADDED
          ),
          baseProspectEventV2.copy(
            event_type = EventType.EVENT_V3_PROSPECT_REMOVED
          )
        )))

      (prospectEventDAOMock.getCampaignEvents)
        .expects(*, baseProspectEventV2, prospectId.map(_.id), None)
        .returning(Success(List(
          baseProspectEventV2.copy(
            event_type = EventType.EVENT_V3_PROSPECT_OPTED_OUT
          ),
          baseProspectEventV2.copy(
            event_type = EventType.EVENT_V3_PROSPECT_COMPLETED
          )
        )))

      (prospectEventDAOMock.getEmailMessageEvents)
        .expects(*, repTrackingHosts)
        .returning(Success(List(
          baseProspectEventV2.copy(
            event_type = EventType.EMAIL_SENT
          ),
          baseProspectEventV2.copy(
            event_type = EventType.EMAIL_BOUNCED
          )
        )))

      val query4 = CaptureOne[SQL[Nothing, NoExtractor]]()
      (prospectEventDAOMock.getNotesEvents(_: SQL[Nothing, NoExtractor], _: ProspectEventV2))
        .expects(
          capture(query4),
          baseProspectEventV2
        ).returning(Success(List()))
//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates (_: TeamId, _: SrRollingUpdateFeature)(_: ISRLogger))
//        .expects(TeamId(1), SrRollingUpdateFeature.UseEmailMessageDataTable, *)
//        .returning(false)
      val res = prospectEventService.getAllEvents(
        queryTimeline = queryTimeline,
        limit = limit,
        prospectId = prospectId,
        prospectAccountId = None,
        repTrackingHosts = repTrackingHosts,
        prospectObjectIfAlreadyThere = Some(prospectObject),
        prospectAccountObjectIfAlreadyThere = None,
        includeMessageEventForEmailThreadId = None,
        teamId = teamId,
        account = Some(account),
        baseEvent = baseProspectEventV2
      ) match {
        case Failure(exception) =>
          println(s"Error Occurred :: ${exception.printStackTrace()}")
          Failure(exception)
        case Success(value) => Success(value)
      }

      val expectedRes = List(
        baseProspectEventV2.copy(
          event_type = EventType.PROSPECT_CREATED,
          created_at = prospectObject.created_at,
          prospect_first_name = prospectObject.first_name,
          prospect_last_name = prospectObject.last_name,
          prospect_email = prospectObject.email,
          prospect_source = prospectObject.internal.prospect_source,
          prospect_id = prospectId.get.id
        ),
        baseProspectEventV2.copy(
          event_type = EventType.EVENT_V3_PROSPECT_ADDED
        ),
        baseProspectEventV2.copy(
          event_type = EventType.EVENT_V3_PROSPECT_REMOVED
        ),
        baseProspectEventV2.copy(
          event_type = EventType.EVENT_V3_PROSPECT_OPTED_OUT
        ),
        baseProspectEventV2.copy(
          event_type = EventType.EVENT_V3_PROSPECT_COMPLETED
        ),
        baseProspectEventV2.copy(
          event_type = EventType.EMAIL_SENT
        ),
        baseProspectEventV2.copy(
          event_type = EventType.EMAIL_BOUNCED
        )
      )

      assert(res.isSuccess)
      assert(res.get.length == 7) // 2 from each mentioned above and 1 prospect_added
      assert(res.get == expectedRes)

    }

    it("should give the list of only prospect events while other calls returns empty list") {

      (prospectEventDAOMock.getOtherEvents)
        .expects(*, baseProspectEventV2, prospectId.map(_.id), teamId.id, Some(account))
        .returning(Success(List()))

      (prospectEventDAOMock.getCampaignEvents)
        .expects(*, baseProspectEventV2, prospectId.map(_.id), None)
        .returning(Success(List()))

      (prospectEventDAOMock.getEmailMessageEvents)
        .expects(*, repTrackingHosts)
        .returning(Success(List()))

      val query4 = CaptureOne[SQL[Nothing, NoExtractor]]()
      (prospectEventDAOMock.getNotesEvents(_: SQL[Nothing, NoExtractor], _: ProspectEventV2))
        .expects(
          capture(query4),
          baseProspectEventV2
        ).returning(Success(List()))
//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates (_: TeamId, _: SrRollingUpdateFeature)(_: ISRLogger))
//        .expects(teamId, SrRollingUpdateFeature.UseEmailMessageDataTable, *)
//        .returning(false)
      val res = prospectEventService.getAllEvents(
        queryTimeline = queryTimeline,
        limit = limit,
        prospectId = prospectId,
        prospectAccountId = None,
        repTrackingHosts = repTrackingHosts,
        prospectObjectIfAlreadyThere = Some(prospectObject),
        prospectAccountObjectIfAlreadyThere = None,
        includeMessageEventForEmailThreadId = None,
        teamId = teamId,
        account = Some(account),
        baseEvent = baseProspectEventV2
      )

      val expectedRes = List(
        baseProspectEventV2.copy(
          event_type = EventType.PROSPECT_CREATED,
          created_at = prospectObject.created_at,
          prospect_first_name = prospectObject.first_name,
          prospect_last_name = prospectObject.last_name,
          prospect_email = prospectObject.email,
          prospect_source = prospectObject.internal.prospect_source,
          prospect_id = prospectId.get.id
        )
      )

      assert(res.isSuccess)
      assert(res.get.length == 1) // 1 prospect_added
      assert(res.get == expectedRes)

    }

    it("should give the list of campaign events with added_prospect while other calls returns empty list") {

      (prospectEventDAOMock.getOtherEvents)
        .expects(*, baseProspectEventV2, prospectId.map(_.id), teamId.id, Some(account))
        .returning(Success(List()))

      (prospectEventDAOMock.getCampaignEvents)
        .expects(*, baseProspectEventV2, prospectId.map(_.id), None)
        .returning(Success(List(
          baseProspectEventV2.copy(
            event_type = EventType.EVENT_V3_PROSPECT_OPTED_OUT
          ),
          baseProspectEventV2.copy(
            event_type = EventType.EVENT_V3_PROSPECT_COMPLETED
          )
        )))

      (prospectEventDAOMock.getEmailMessageEvents)
        .expects(*, repTrackingHosts)
        .returning(Success(List()))

      val query4 = CaptureOne[SQL[Nothing, NoExtractor]]()
      (prospectEventDAOMock.getNotesEvents(_: SQL[Nothing, NoExtractor], _: ProspectEventV2))
        .expects(
          capture(query4),
          baseProspectEventV2
        ).returning(Success(List()))
//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates (_: TeamId, _: SrRollingUpdateFeature)(_: ISRLogger))
//        .expects(teamId, SrRollingUpdateFeature.UseEmailMessageDataTable, *)
//        .returning(false)
      val res = prospectEventService.getAllEvents(
        queryTimeline = queryTimeline,
        limit = limit,
        prospectId = prospectId,
        prospectAccountId = None,
        repTrackingHosts = repTrackingHosts,
        prospectObjectIfAlreadyThere = Some(prospectObject),
        prospectAccountObjectIfAlreadyThere = None,
        includeMessageEventForEmailThreadId = None,
        teamId = teamId,
        account = Some(account),
        baseEvent = baseProspectEventV2
      )

      val expectedRes = List(
        baseProspectEventV2.copy(
          event_type = EventType.PROSPECT_CREATED,
          created_at = prospectObject.created_at,
          prospect_first_name = prospectObject.first_name,
          prospect_last_name = prospectObject.last_name,
          prospect_email = prospectObject.email,
          prospect_source = prospectObject.internal.prospect_source,
          prospect_id = prospectId.get.id
        ),
        baseProspectEventV2.copy(
          event_type = EventType.EVENT_V3_PROSPECT_OPTED_OUT
        ),
        baseProspectEventV2.copy(
          event_type = EventType.EVENT_V3_PROSPECT_COMPLETED
        )
      )

      assert(res.isSuccess)
      assert(res.get.length == 3) // 2 from getCampaignEvents mentioned above and 1 prospect_added
      assert(res.get == expectedRes)

    }

    it("should give the list of other events with added_prospect while other calls returns empty list") {

      (prospectEventDAOMock.getOtherEvents)
        .expects(*, baseProspectEventV2, prospectId.map(_.id), teamId.id, Some(account))
        .returning(Success(List(
          baseProspectEventV2.copy(
            event_type = EventType.EVENT_V3_PROSPECT_ADDED
          ),
          baseProspectEventV2.copy(
            event_type = EventType.EVENT_V3_PROSPECT_REMOVED
          )
        )))

      (prospectEventDAOMock.getCampaignEvents)
        .expects(*, baseProspectEventV2, prospectId.map(_.id), None)
        .returning(Success(List()))

      (prospectEventDAOMock.getEmailMessageEvents)
        .expects(*, repTrackingHosts)
        .returning(Success(List()))
//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates (_: TeamId, _: SrRollingUpdateFeature)(_: ISRLogger))
//        .expects(teamId, SrRollingUpdateFeature.UseEmailMessageDataTable, *)
//        .returning(false)
      
      val query4 = CaptureOne[SQL[Nothing, NoExtractor]]()
      (prospectEventDAOMock.getNotesEvents(_: SQL[Nothing, NoExtractor], _: ProspectEventV2))
        .expects(
          capture(query4),
          baseProspectEventV2
        ).returning(Success(List()))

      val res = prospectEventService.getAllEvents(
        queryTimeline = queryTimeline,
        limit = limit,
        prospectId = prospectId,
        prospectAccountId = None,
        repTrackingHosts = repTrackingHosts,
        prospectObjectIfAlreadyThere = Some(prospectObject),
        prospectAccountObjectIfAlreadyThere = None,
        includeMessageEventForEmailThreadId = None,
        teamId = teamId,
        account = Some(account),
        baseEvent = baseProspectEventV2
      )

      val expectedRes = List(
        baseProspectEventV2.copy(
          event_type = EventType.PROSPECT_CREATED,
          created_at = prospectObject.created_at,
          prospect_first_name = prospectObject.first_name,
          prospect_last_name = prospectObject.last_name,
          prospect_email = prospectObject.email,
          prospect_source = prospectObject.internal.prospect_source,
          prospect_id = prospectId.get.id
        ),
        baseProspectEventV2.copy(
          event_type = EventType.EVENT_V3_PROSPECT_ADDED
        ),
        baseProspectEventV2.copy(
          event_type = EventType.EVENT_V3_PROSPECT_REMOVED
        )
      )

      assert(res.isSuccess)
      assert(res.get.length == 3) // 2 from other events mentioned above and 1 prospect_added
      assert(res.get == expectedRes)

    }

    it("should give the list of email events with added_prospect while other calls returns empty list") {

      (prospectEventDAOMock.getOtherEvents)
        .expects(*, baseProspectEventV2, prospectId.map(_.id), teamId.id, Some(account))
        .returning(Success(List()))

      (prospectEventDAOMock.getCampaignEvents)
        .expects(*, baseProspectEventV2, prospectId.map(_.id), None)
        .returning(Success(List()))

      (prospectEventDAOMock.getEmailMessageEvents)
        .expects(*, repTrackingHosts)
        .returning(Success(List(
          baseProspectEventV2.copy(
            event_type = EventType.EMAIL_SENT
          ),
          baseProspectEventV2.copy(
            event_type = EventType.EMAIL_BOUNCED
          )
        )))
//      (srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates (_: TeamId, _: SrRollingUpdateFeature)(_: ISRLogger))
//        .expects(teamId, SrRollingUpdateFeature.UseEmailMessageDataTable, *)
//        .returning(false)
      val query4 = CaptureOne[SQL[Nothing, NoExtractor]]()
      (prospectEventDAOMock.getNotesEvents(_: SQL[Nothing, NoExtractor], _: ProspectEventV2))
        .expects(
          capture(query4),
          baseProspectEventV2
        ).returning(Success(List()))

      val res = prospectEventService.getAllEvents(
        queryTimeline = queryTimeline,
        limit = limit,
        prospectId = prospectId,
        prospectAccountId = None,
        repTrackingHosts = repTrackingHosts,
        prospectObjectIfAlreadyThere = Some(prospectObject),
        prospectAccountObjectIfAlreadyThere = None,
        includeMessageEventForEmailThreadId = None,
        teamId = teamId,
        account = Some(account),
        baseEvent = baseProspectEventV2
      )

      val expectedRes = List(
        baseProspectEventV2.copy(
          event_type = EventType.PROSPECT_CREATED,
          created_at = prospectObject.created_at,
          prospect_first_name = prospectObject.first_name,
          prospect_last_name = prospectObject.last_name,
          prospect_email = prospectObject.email,
          prospect_source = prospectObject.internal.prospect_source,
          prospect_id = prospectId.get.id
        ),
        baseProspectEventV2.copy(
          event_type = EventType.EMAIL_SENT
        ),
        baseProspectEventV2.copy(
          event_type = EventType.EMAIL_BOUNCED
        )
      )

      assert(res.isSuccess)
      assert(res.get.length == 3) // 2 from email events mentioned above and 1 prospect_added
      assert(res.get == expectedRes)

    }

  }
}
