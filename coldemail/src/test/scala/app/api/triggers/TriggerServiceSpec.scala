package app.api.triggers

import org.apache.pekko.actor.ActorSystem
import org.apache.pekko.stream.ActorMaterializer
import api.accounts.AccountDAO
import api.accounts.dao.TeamsDAO
import api.accounts.service.OrganizationService
import api.integrations.{CommonCRMAPIErrors, GetLeadStatusColumnsError, IntegrationTPAccessTokenResponse, IntegrationTPColumns, TIntegrationCRMTrait}
import api.triggers.*
import org.joda.time.DateTime
import org.scalamock.scalatest.AsyncMockFactory
import org.scalatest.funspec.AsyncFunSpec
import org.scalatest.matchers.must.Matchers.be
import play.api.libs.ws.WSClient
import play.api.libs.ws.ahc.AhcWSClient
import utils.SRLogger
import api.integrations.CRMIntegrations
import api.integrations.services.TIntegrationCRMService
import api.sr_audit_logs.models.EventType
import utils.testapp.TestAppExecutionContext

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}

class TriggerServiceSpec
  extends AsyncFunSpec
  with AsyncMockFactory {

  implicit lazy val system: ActorSystem = TestAppExecutionContext.actorSystem
  implicit lazy val wSClient: AhcWSClient = TestAppExecutionContext.wsClient
  implicit lazy val actorContext: ExecutionContext = system.dispatcher

  given Logger: SRLogger = new SRLogger("TriggerServiceSpec Unit test")


  val status_col_name = "lead_status"
  val status_col_value = "Closed - Converted"
  val status_col_with_options = CRMStatusColumnNameAndOptions(
    column_name = status_col_name,
    column_options = Seq(
      CRMStatusColumnOptions(
        label = "Closed - Converted",
        text_id = "Closed - Converted"
      )
    )
  )

  val triggerDAO = mock[Trigger]
  val tIntegrationCRMService = mock[TIntegrationCRMService]
  val organizationService = mock[OrganizationService]
  val teamDAO = mock[TeamsDAO]
  
  val triggerService = new TriggerService(
    triggerDAO = triggerDAO,
    organizationService = organizationService,
    tIntegrationCRMService  =tIntegrationCRMService,
      teamDAO = teamDAO
  )

  describe("getLeadStatusColumns") {


    it("should return LeadStatusColumnsFetchError") {

      val crm_type = IntegrationType.SALESFORCE
      val module_type = IntegrationModuleType.CONTACTS
      val team_id: Long = 2
      val empty_email = ""

      val access_token = "abcd-1234"
      val refresh_token = Some("wxyz-10111213")
      val expires_in = Some(98761234);
      val expires_at = Some(DateTime.now())
      val token_type = Some("acc-tok")
      val api_domain = Some("api.domain.com")
      val intg_acc_tok = IntegrationTPAccessTokenResponse.FullTokenData(
        access_token = access_token,
        refresh_token = refresh_token,
        expires_in = expires_in,
        expires_at = expires_at,
        token_type = token_type,
        api_domain = api_domain,
        is_sandbox = Some(false)
      )


      val columns = IntegrationTPColumns(
        col_id = "first_name",
        label = "First name",
        field_type = "text",
        description = None,
        field_options = None
      )


      trait IntegrationsCRMTestTrait extends TIntegrationCRMTrait {
        val name = IntegrationType.SALESFORCE
      }
      val crmIntegrationService = mock[IntegrationsCRMTestTrait]


      (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
      (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
        .expects(team_id, crm_type, *, *, *)
        .returning(Future.successful(
          Right(intg_acc_tok)
        ))


      //    val person_id_capt = CaptureOne[String]()

      (tIntegrationCRMService.getLeadStatusColumns
      (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType)
      (_: WSClient, _: ExecutionContext, _: SRLogger)
        )
        .expects(crmIntegrationService.name,
          intg_acc_tok, module_type,
          *, *, *
        ).returning(Future.successful(
        Left(GetLeadStatusColumnsError.LeadStatusColumnsFetchError(message = "LeadStatusColumnsFetchError"))
      ))

      triggerService.getLeadStatusColumns(
        integration_type = crmIntegrationService.name,
        module_type = module_type,
        team_id = team_id
      ).flatMap(result => {
        assert(result.isLeft)
      })


    }

    it("should return InvalidModuleError") {

      val crm_type = IntegrationType.SALESFORCE
      val module_type = IntegrationModuleType.CONTACTS
      val team_id: Long = 2
      val empty_email = ""

      val access_token = "abcd-1234"
      val refresh_token = Some("wxyz-10111213")
      val expires_in = Some(98761234);
      val expires_at = Some(DateTime.now())
      val token_type = Some("acc-tok")
      val api_domain = Some("api.domain.com")
      val intg_acc_tok = IntegrationTPAccessTokenResponse.FullTokenData(
        access_token = access_token,
        refresh_token = refresh_token,
        expires_in = expires_in,
        expires_at = expires_at,
        token_type = token_type,
        api_domain = api_domain,
        is_sandbox = Some(false)
      )


      val columns = IntegrationTPColumns(
        col_id = "first_name",
        label = "First name",
        field_type = "text",
        description = None,
        field_options = None
      )


      trait IntegrationsCRMTestTrait extends TIntegrationCRMTrait {
        val name = IntegrationType.SALESFORCE
      }
      val crmIntegrationService = mock[IntegrationsCRMTestTrait]


      (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
      (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
        .expects(team_id, crm_type, *, *, *)
        .returning(Future.successful(
          Right(intg_acc_tok)
        ))


      //    val person_id_capt = CaptureOne[String]()

      (tIntegrationCRMService.getLeadStatusColumns
      (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType)
      (_: WSClient, _: ExecutionContext, _: SRLogger)
        )
        .expects(crmIntegrationService.name,
          intg_acc_tok, module_type,
          *, *, *
        ).returning(Future.successful(
        Left(GetLeadStatusColumnsError.InvalidModuleError(message = "InvalidModuleError"))
      ))

      triggerService.getLeadStatusColumns(
        integration_type = crmIntegrationService.name,
        module_type = module_type,
        team_id = team_id
      ).flatMap(result => {
        assert(result.isLeft)
      })


    }

    it("should return InvalidGrantException") {

      val crm_type = IntegrationType.SALESFORCE
      val module_type = IntegrationModuleType.CONTACTS
      val team_id: Long = 2
      val empty_email = ""

      val access_token = "abcd-1234"
      val refresh_token = Some("wxyz-10111213")
      val expires_in = Some(98761234);
      val expires_at = Some(DateTime.now())
      val token_type = Some("acc-tok")
      val api_domain = Some("api.domain.com")
      val intg_acc_tok = IntegrationTPAccessTokenResponse.FullTokenData(
        access_token = access_token,
        refresh_token = refresh_token,
        expires_in = expires_in,
        expires_at = expires_at,
        token_type = token_type,
        api_domain = api_domain,
        is_sandbox = Some(false)
      )


      val columns = IntegrationTPColumns(
        col_id = "first_name",
        label = "First name",
        field_type = "text",
        description = None,
        field_options = None
      )


      trait IntegrationsCRMTestTrait extends TIntegrationCRMTrait {
        val name = IntegrationType.SALESFORCE
      }
      val crmIntegrationService = mock[IntegrationsCRMTestTrait]


      (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
      (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
        .expects(team_id, crm_type, *, *, *)
        .returning(Future.successful(
          Right(intg_acc_tok)
        ))


      //    val person_id_capt = CaptureOne[String]()

      (tIntegrationCRMService.getLeadStatusColumns
      (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType)
      (_: WSClient, _: ExecutionContext, _: SRLogger)
        )
        .expects(crmIntegrationService.name,
          intg_acc_tok, module_type,
          *, *, *
        ).returning(Future.successful(
        Left(GetLeadStatusColumnsError.InvalidGrantException(message = "InvalidGrantException"))
      ))

      triggerService.getLeadStatusColumns(
        integration_type = crmIntegrationService.name,
        module_type = module_type,
        team_id = team_id
      ).flatMap(result => {
        assert(result.isLeft)
      })


    }

    it("should return MalformedResponseStructureError") {

      val crm_type = IntegrationType.SALESFORCE
      val module_type = IntegrationModuleType.CONTACTS
      val team_id: Long = 2
      val empty_email = ""

      val access_token = "abcd-1234"
      val refresh_token = Some("wxyz-10111213")
      val expires_in = Some(98761234);
      val expires_at = Some(DateTime.now())
      val token_type = Some("acc-tok")
      val api_domain = Some("api.domain.com")
      val intg_acc_tok = IntegrationTPAccessTokenResponse.FullTokenData(
        access_token = access_token,
        refresh_token = refresh_token,
        expires_in = expires_in,
        expires_at = expires_at,
        token_type = token_type,
        api_domain = api_domain,
        is_sandbox = Some(false)
      )


      val columns = IntegrationTPColumns(
        col_id = "first_name",
        label = "First name",
        field_type = "text",
        description = None,
        field_options = None
      )


      trait IntegrationsCRMTestTrait extends TIntegrationCRMTrait {
        val name = IntegrationType.SALESFORCE
      }
      val crmIntegrationService = mock[IntegrationsCRMTestTrait]


      (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
      (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
        .expects(team_id, crm_type, *, *, *)
        .returning(Future.successful(
          Right(intg_acc_tok)
        ))


      //    val person_id_capt = CaptureOne[String]()

      (tIntegrationCRMService.getLeadStatusColumns
      (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType)
      (_: WSClient, _: ExecutionContext, _: SRLogger)
        )
        .expects(crmIntegrationService.name,
          intg_acc_tok, module_type,
          *, *, *
        ).returning(Future.successful(
        Left(GetLeadStatusColumnsError.MalformedResponseStructureError(message = "MalformedResponseStructureError"))
      ))

      triggerService.getLeadStatusColumns(
        integration_type = crmIntegrationService.name,
        module_type = module_type,
        team_id = team_id
      ).flatMap(result => {
        assert(result.isLeft)
      })


    }

    it("should return InternalServerError") {

      val crm_type = IntegrationType.SALESFORCE
      val module_type = IntegrationModuleType.CONTACTS
      val team_id: Long = 2
      val empty_email = ""

      val access_token = "abcd-1234"
      val refresh_token = Some("wxyz-10111213")
      val expires_in = Some(98761234);
      val expires_at = Some(DateTime.now())
      val token_type = Some("acc-tok")
      val api_domain = Some("api.domain.com")
      val intg_acc_tok = IntegrationTPAccessTokenResponse.FullTokenData(
        access_token = access_token,
        refresh_token = refresh_token,
        expires_in = expires_in,
        expires_at = expires_at,
        token_type = token_type,
        api_domain = api_domain,
        is_sandbox = Some(false)
      )


      val columns = IntegrationTPColumns(
        col_id = "first_name",
        label = "First name",
        field_type = "text",
        description = None,
        field_options = None
      )


      trait IntegrationsCRMTestTrait extends TIntegrationCRMTrait {
        val name = IntegrationType.SALESFORCE
      }
      val crmIntegrationService = mock[IntegrationsCRMTestTrait]


      (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
      (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
        .expects(team_id, crm_type, *, *, *)
        .returning(Future.successful(
          Right(intg_acc_tok)
        ))


      //    val person_id_capt = CaptureOne[String]()

      (tIntegrationCRMService.getLeadStatusColumns
      (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType)
      (_: WSClient, _: ExecutionContext, _: SRLogger)
        )
        .expects(crmIntegrationService.name,
          intg_acc_tok, module_type,
          *, *, *
        ).returning(Future.successful(
        Left(GetLeadStatusColumnsError.CommonCRMAPIError(err = CommonCRMAPIErrors.InternalServerError(msg = "InternalServerError")))
      ))

      triggerService.getLeadStatusColumns(
        integration_type = crmIntegrationService.name,
        module_type = module_type,
        team_id = team_id
      ).flatMap(result => {
        assert(result.isLeft)
      })


    }

    it("should return NotFoundError") {

      val crm_type = IntegrationType.SALESFORCE
      val module_type = IntegrationModuleType.CONTACTS
      val team_id: Long = 2
      val empty_email = ""

      val access_token = "abcd-1234"
      val refresh_token = Some("wxyz-10111213")
      val expires_in = Some(98761234);
      val expires_at = Some(DateTime.now())
      val token_type = Some("acc-tok")
      val api_domain = Some("api.domain.com")
      val intg_acc_tok = IntegrationTPAccessTokenResponse.FullTokenData(
        access_token = access_token,
        refresh_token = refresh_token,
        expires_in = expires_in,
        expires_at = expires_at,
        token_type = token_type,
        api_domain = api_domain,
        is_sandbox = Some(false)
      )


      val columns = IntegrationTPColumns(
        col_id = "first_name",
        label = "First name",
        field_type = "text",
        description = None,
        field_options = None
      )


      trait IntegrationsCRMTestTrait extends TIntegrationCRMTrait {
        val name = IntegrationType.SALESFORCE
      }
      val crmIntegrationService = mock[IntegrationsCRMTestTrait]


      (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
      (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
        .expects(team_id, crm_type, *, *, *)
        .returning(Future.successful(
          Right(intg_acc_tok)
        ))


      //    val person_id_capt = CaptureOne[String]()

      (tIntegrationCRMService.getLeadStatusColumns
      (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType)
      (_: WSClient, _: ExecutionContext, _: SRLogger)
        )
        .expects(crmIntegrationService.name,
          intg_acc_tok, module_type,
          *, *, *
        ).returning(Future.successful(
        Left(GetLeadStatusColumnsError.CommonCRMAPIError(err = CommonCRMAPIErrors.NotFoundError(msg = "NotFoundError")))
      ))

      triggerService.getLeadStatusColumns(
        integration_type = crmIntegrationService.name,
        module_type = module_type,
        team_id = team_id
      ).flatMap(result => {
        assert(result.isLeft)
      })


    }

    it("should return UnknownError") {

      val crm_type = IntegrationType.SALESFORCE
      val module_type = IntegrationModuleType.CONTACTS
      val team_id: Long = 2
      val empty_email = ""

      val access_token = "abcd-1234"
      val refresh_token = Some("wxyz-10111213")
      val expires_in = Some(98761234);
      val expires_at = Some(DateTime.now())
      val token_type = Some("acc-tok")
      val api_domain = Some("api.domain.com")
      val intg_acc_tok = IntegrationTPAccessTokenResponse.FullTokenData(
        access_token = access_token,
        refresh_token = refresh_token,
        expires_in = expires_in,
        expires_at = expires_at,
        token_type = token_type,
        api_domain = api_domain,
        is_sandbox = Some(false)
      )


      val columns = IntegrationTPColumns(
        col_id = "first_name",
        label = "First name",
        field_type = "text",
        description = None,
        field_options = None
      )


      trait IntegrationsCRMTestTrait extends TIntegrationCRMTrait {
        val name = IntegrationType.SALESFORCE
      }
      val crmIntegrationService = mock[IntegrationsCRMTestTrait]


      (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
      (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
        .expects(team_id, crm_type, *, *, *)
        .returning(Future.successful(
          Right(intg_acc_tok)
        ))


      //    val person_id_capt = CaptureOne[String]()

      (tIntegrationCRMService.getLeadStatusColumns
      (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType)
      (_: WSClient, _: ExecutionContext, _: SRLogger)
        )
        .expects(crmIntegrationService.name,
          intg_acc_tok, module_type,
          *, *, *
        ).returning(Future.successful(
        Left(GetLeadStatusColumnsError.CommonCRMAPIError(err = CommonCRMAPIErrors.UnknownError(msg = "UnknownError")))
      ))

      triggerService.getLeadStatusColumns(
        integration_type = crmIntegrationService.name,
        module_type = module_type,
        team_id = team_id
      ).flatMap(result => {
        assert(result.isLeft)
      })


    }

    it("should return UnAuthorizedError") {

      val crm_type = IntegrationType.SALESFORCE
      val module_type = IntegrationModuleType.CONTACTS
      val team_id: Long = 2
      val empty_email = ""

      val access_token = "abcd-1234"
      val refresh_token = Some("wxyz-10111213")
      val expires_in = Some(98761234);
      val expires_at = Some(DateTime.now())
      val token_type = Some("acc-tok")
      val api_domain = Some("api.domain.com")
      val intg_acc_tok = IntegrationTPAccessTokenResponse.FullTokenData(
        access_token = access_token,
        refresh_token = refresh_token,
        expires_in = expires_in,
        expires_at = expires_at,
        token_type = token_type,
        api_domain = api_domain,
        is_sandbox = Some(false)
      )


      val columns = IntegrationTPColumns(
        col_id = "first_name",
        label = "First name",
        field_type = "text",
        description = None,
        field_options = None
      )


      trait IntegrationsCRMTestTrait extends TIntegrationCRMTrait {
        val name = IntegrationType.SALESFORCE
      }
      val crmIntegrationService = mock[IntegrationsCRMTestTrait]


      (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
      (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
        .expects(team_id, crm_type, *, *, *)
        .returning(Future.successful(
          Right(intg_acc_tok)
        ))


      //    val person_id_capt = CaptureOne[String]()

      (tIntegrationCRMService.getLeadStatusColumns
      (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType)
      (_: WSClient, _: ExecutionContext, _: SRLogger)
        )
        .expects(crmIntegrationService.name,
          intg_acc_tok, module_type,
          *, *, *
        ).returning(Future.successful(
        Left(GetLeadStatusColumnsError.CommonCRMAPIError(err = CommonCRMAPIErrors.UnAuthorizedError(msg = "UnAuthorizedError")))
      ))

      triggerService.getLeadStatusColumns(
        integration_type = crmIntegrationService.name,
        module_type = module_type,
        team_id = team_id
      ).flatMap(result => {
        assert(result.isLeft)
      })


    }

    it("should return TooManyRequestsError") {

      val crm_type = IntegrationType.SALESFORCE
      val module_type = IntegrationModuleType.CONTACTS
      val team_id: Long = 2L
      val empty_email = ""

      val access_token = "abcd-1234"
      val refresh_token = Some("wxyz-10111213")
      val expires_in = Some(98761234);
      val expires_at = Some(DateTime.now())
      val token_type = Some("acc-tok")
      val api_domain = Some("api.domain.com")
      val intg_acc_tok = IntegrationTPAccessTokenResponse.FullTokenData(
        access_token = access_token,
        refresh_token = refresh_token,
        expires_in = expires_in,
        expires_at = expires_at,
        token_type = token_type,
        api_domain = api_domain,
        is_sandbox = Some(false)
      )


      val columns = IntegrationTPColumns(
        col_id = "first_name",
        label = "First name",
        field_type = "text",
        description = None,
        field_options = None
      )


      trait IntegrationsCRMTestTrait extends TIntegrationCRMTrait {
        val name = IntegrationType.SALESFORCE
      }
      val crmIntegrationService = mock[IntegrationsCRMTestTrait]


      (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
      (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
        .expects(team_id, crm_type, *, *, *)
        .returning(Future.successful(
          Right(intg_acc_tok)
        ))


      //    val person_id_capt = CaptureOne[String]()

      (tIntegrationCRMService.getLeadStatusColumns
      (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType)
      (_: WSClient, _: ExecutionContext, _: SRLogger)
        )
        .expects(crmIntegrationService.name,
          intg_acc_tok, module_type,
          *, *, *
        ).returning(Future.successful(
        Left(GetLeadStatusColumnsError.CommonCRMAPIError(err = CommonCRMAPIErrors.TooManyRequestsError(msg = "TooManyRequestsError")))
      ))

      triggerService.getLeadStatusColumns(
        integration_type = crmIntegrationService.name,
        module_type = module_type,
        team_id = team_id
      ).flatMap(result => {
        assert(result.isLeft)
      })


    }

    it("should return lead status columns") {

      val crm_type = IntegrationType.SALESFORCE
      val module_type = IntegrationModuleType.CONTACTS
      val team_id: Long = 2
      val empty_email = ""

      val access_token = "abcd-1234"
      val refresh_token = Some("wxyz-10111213")
      val expires_in = Some(98761234);
      val expires_at = Some(DateTime.now())
      val token_type = Some("acc-tok")
      val api_domain = Some("api.domain.com")
      val intg_acc_tok = IntegrationTPAccessTokenResponse.FullTokenData(
        access_token = access_token,
        refresh_token = refresh_token,
        expires_in = expires_in,
        expires_at = expires_at,
        token_type = token_type,
        api_domain = api_domain,
        is_sandbox = Some(false)
      )


      val columns = IntegrationTPColumns(
        col_id = "first_name",
        label = "First name",
        field_type = "text",
        description = None,
        field_options = None
      )


      trait IntegrationsCRMTestTrait extends TIntegrationCRMTrait {
        val name = IntegrationType.SALESFORCE
      }
      val crmIntegrationService = mock[IntegrationsCRMTestTrait]


      (tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken
      (_: Long, _: IntegrationType)(_: SRLogger, _: ExecutionContext, _: WSClient))
        .expects(team_id, crm_type, *, *, *)
        .returning(Future.successful(
          Right(intg_acc_tok)
        ))


      //    val person_id_capt = CaptureOne[String]()

      (tIntegrationCRMService.getLeadStatusColumns
      (_: IntegrationType, _: IntegrationTPAccessTokenResponse.FullTokenData, _: IntegrationModuleType)
      (_: WSClient, _: ExecutionContext, _: SRLogger)
        )
        .expects(crmIntegrationService.name,
          intg_acc_tok, module_type,
          *, *, *
        ).returning(Future.successful(
        Right(Seq(columns))
      ))

      triggerService.getLeadStatusColumns(
        integration_type = crmIntegrationService.name,
        module_type = module_type,
        team_id = team_id
      ).flatMap(result => {
        assert(result.toOption.get.nonEmpty)
      })


    }
  }


  describe("updateLeadStatusMapping") {
    
    it("should update lead status mapping") {


      val crm_type = IntegrationType.SALESFORCE
      val module_type = IntegrationModuleType.CONTACTS
      val team_id: Long = 2
      val data = UpdateStatusMappingForm(
        activity_to_status_mapping = Some(
          Seq(
            LeadStatusMapping(
              sr_activity = EventType.EMAIL_OPENED,
              sr_activity_label = "Email open",
              crm_status = "Open"
            ))
        ),
        category_to_status_mapping = Some(
          Seq(
            CategoryToStatusMapping(
              sr_category_id = 123,
              sr_category_label = "Interested",
              crm_status = "Closed"
            ))
        ),
        crm_column = Some(status_col_with_options),
        sentiment_to_status_mapping = None,
        update_reply_sentiment_for_all_associated_prospects = false
      )



      (triggerDAO.updateLeadStatusMapping)
        .expects(team_id, data, crm_type, module_type, Logger)
        .returning(Success(Some(777)))

      Future.fromTry(
        triggerService.updateLeadStatusMapping(
          crm_type = crm_type,
          module_type = module_type,
          team_id = team_id,
          data = data,
          Logger = Logger
        )).flatMap(result => {
        assert(result.nonEmpty)
      })

    }
  }


  describe("getLeadStatusMapping") {
    
    it("should get lead status mapping") {


      val crm_type = IntegrationType.SALESFORCE
      val module_type = IntegrationModuleType.CONTACTS
      val team_id: Long = 2

      val res = UpdateStatusMappingForm(
        activity_to_status_mapping = Some(
          Seq(
            LeadStatusMapping(
              sr_activity = EventType.EMAIL_OPENED,
              sr_activity_label = "Email open",
              crm_status = "Open"
            ))
        ),
        category_to_status_mapping = Some(
          Seq(
            CategoryToStatusMapping(
              sr_category_id = 123,
              sr_category_label = "Interested",
              crm_status = "Closed"
            ))
        ),
        crm_column = Some(status_col_with_options),
        sentiment_to_status_mapping = None,
        update_reply_sentiment_for_all_associated_prospects = false
      )


      (triggerDAO.findLeadStatusMapping)
        .expects(team_id, crm_type, module_type, Logger)
        .returning(Success(Some(res)))

      Future.fromTry(
        triggerService.getLeadStatusMapping(
          crm_type = crm_type,
          module_type = module_type,
          team_id = team_id,
          Logger = Logger
        )).flatMap(result => {
        assert(result.nonEmpty)
      })

    }
  }


  describe("getConfiguredModulesByCRM") {

    it("should get configured modules by CRM") {


      val crm_type = IntegrationType.SALESFORCE
      val team_id: Long = 2
      val res = CRMConfiguredModules(
        module_type = "leads",
        is_configured = true
      )


      (triggerDAO.findConfiguredModulesByCRM)
        .expects(team_id, crm_type, Logger)
        .returning(Success(Seq(res)))

      Future.fromTry(
        triggerService.getConfiguredModulesByCRM(
          crm_type = crm_type,
          team_id = team_id,
          Logger = Logger
        )).flatMap(result => {
        assert(result.nonEmpty)
      })

    }
  }


  describe("getCRMModuleLevelSettings") {
    
    it("should get CRM module level settings") {


      val crm_type = IntegrationType.SALESFORCE
      val module_type = IntegrationModuleType.CONTACTS
      val team_id: Long = 2
      val dummyFilters = Seq(
        CRMFiltersForAddToDNCInSR(
          filter_id = "00B2w000002gLULEA2",
          last_sync_at = Some(DateTime.now())
        ))
      val res = UpdateCRMModuleLevelSettingsForm(
        active = true,
        track_activities = true,
        create_record_if_not_exists = true,
        create_or_update_record_in_crm = true

      )


      (triggerDAO.findCRMModuleLevelSettings)
        .expects(team_id, crm_type, module_type, Logger)
        .returning(Success(Some(res)))

      Future.fromTry(
        triggerService.getCRMModuleLevelSettings(
          crm_type = crm_type,
          module_type = module_type,
          team_id = team_id,
          Logger = Logger
        )).flatMap(res => {
        assert(res.isDefined)
      })

    }
  }


  describe("updateCRMModuleLevelSettings") {

    it("should update CRM module level settings") {


      val crm_type = IntegrationType.SALESFORCE
      val module_type = IntegrationModuleType.CONTACTS
      val team_id: Long = 2
      val dummyFilters = Seq(
        CRMFiltersForAddToDNCInSR(
          filter_id = "00B2w000002gLULEA2",
          last_sync_at = Some(DateTime.now())
        ))
      val data = UpdateCRMModuleLevelSettingsForm(
        active = true,
        track_activities = true,
        create_record_if_not_exists = true,
        create_or_update_record_in_crm = true
      )

      
      (triggerDAO.updateCRMModuleLevelSettings)
        .expects(team_id, data, crm_type, module_type, Logger)
        .returning(Success(Some(team_id)))

      Future.fromTry(
        triggerService.updateCRMModuleLevelSettings(
          crm_type = crm_type,
          module_type = module_type,
          team_id = team_id,
          data = data,
          Logger = Logger
        )).flatMap(res => {
        assert(res.isDefined && res.get == team_id)
      })

    }
  }


  describe("validateAndGetParsedCRMTypeModuleTypeAndWorkflowEventName") {

    val triggerDAO = mock[Trigger]
    val tIntegrationCRMService = mock[TIntegrationCRMService]

    it("should return invalid crm error") {

      val crm_type = "salesforce122"
      val module_type = "contacts"

      val res = triggerService.validateAndGetParsedCRMTypeModuleTypeAndWorkflowEventName(
        crm_type = crm_type,
        module_type = module_type
      )

      assert(res.isLeft)
      assert(res.swap.getOrElse("").toString.contains("InvalidCRMError"))
    }

    it("should return invalid module error") {

      val crm_type = "salesforce"
      val module_type = "contacts123"

      val res = triggerService.validateAndGetParsedCRMTypeModuleTypeAndWorkflowEventName(
        crm_type = crm_type,
        module_type = module_type
      )

      assert(res.isLeft)
      assert(res.swap.getOrElse("").toString.contains("InvalidModuleError"))
    }

    it("should return unsupported event error") {

      val crm_type = "salesforce"
      val module_type = "candidates"

      val res = triggerService.validateAndGetParsedCRMTypeModuleTypeAndWorkflowEventName(
        crm_type = crm_type,
        module_type = module_type
      )

      assert(res.isLeft)
      assert(res.swap.getOrElse("").toString.contains("InvalidCRMOrModuleForEventTypeError"))
    }

    it("should return WorkflowEventName") {

      val crm_type = "salesforce"
      val module_type = "contacts"

      val res = triggerService.validateAndGetParsedCRMTypeModuleTypeAndWorkflowEventName(crm_type = crm_type,
        module_type = module_type
      )

      assert(res.isRight)
      assert(res.toOption.get == EventType.SALESFORCE_PROSPECT_SYNC)

    }
  }

  describe("findAllCampaignTriggersByEventType") {
    
    it("should return sql exception") {

      given Logger: SRLogger = new SRLogger("findAllCampaignTriggersByEventType Unit test")
      val team_id: Long = 2
      val permittedAccountIds: Seq[Long] = Seq(7)
      val eventType = EventType.SALESFORCE_PROSPECT_SYNC

      (triggerDAO.findAllCampaignTriggersByEventType)
        .expects(team_id, permittedAccountIds, eventType, Logger)
        .returning(Failure(new Exception("Sql Exception")))

      val res = triggerService.findAllCampaignTriggersByEventType(
        teamId = team_id,
        permittedAccountIds = permittedAccountIds,
        eventType = eventType,
        logger = Logger
      )

      assert(res.isLeft)
      assert(res.swap.getOrElse("").toString.contains("SQLException"))

    }

    it("should return empty syncs") {

      given Logger: SRLogger = new SRLogger("findAllCampaignTriggersByEventType Unit test")
      val team_id: Long = 2
      val permittedAccountIds: Seq[Long] = Seq(7)
      val eventType = EventType.SALESFORCE_PROSPECT_SYNC

      (triggerDAO.findAllCampaignTriggersByEventType)
        .expects(team_id, permittedAccountIds, eventType, Logger)
        .returning(Success(Seq()))

      val res = triggerService.findAllCampaignTriggersByEventType(
        teamId = team_id,
        permittedAccountIds = permittedAccountIds,
        eventType = eventType,
        logger = Logger
      )

      assert(res.isLeft)
      assert(res.swap.getOrElse("").toString.contains("NOTFoundSyncsError"))

    }

    it("should return campaign syncs") {

      given Logger: SRLogger = new SRLogger("findAllCampaignTriggersByEventType Unit test")
      val team_id: Long = 2
      val permittedAccountIds: Seq[Long] = Seq(7)
      val eventType = EventType.SALESFORCE_PROSPECT_SYNC

      val dummyRes = CampaignSyncInDB(
        integration_type = IntegrationType.SALESFORCE,
        module_type = IntegrationModuleType.CONTACTS,
        campaign_id = Some(1244),
        campaign_name = Some("New campaign 123455"),
        filter_id = Some("756FFGR3"),
        ignore_prospects_active_in_other_campaigns = Some("yes"),
        last_ran_at = None,
        error = None,
        error_at = None
      )

      (triggerDAO.findAllCampaignTriggersByEventType)
        .expects(team_id, permittedAccountIds, eventType, Logger)
        .returning(Success(Seq(dummyRes)))

      val res = triggerService.findAllCampaignTriggersByEventType(
        teamId = team_id,
        permittedAccountIds = permittedAccountIds,
        eventType = eventType,
        logger = Logger
      )

      assert(res.isRight)
      assert(res.toOption.get.nonEmpty)

    }
  }

  describe("findAllCrmIntegrationByOrgId"){
    
    val error = new Throwable("failed to fetch data")
    it("should fails because it fails to fetch from db"){

      (triggerDAO.findAllCRMIntegrationsByOrgID (_:Long)).expects(10).returning(Failure(error))
      val res = triggerService.findAllCrmIntegrationByOrgId(orgId = 10)
      assert(res == Failure(error))

    }
    it("should success with some crm itegration type(hubspot)"){
      (triggerDAO.findAllCRMIntegrationsByOrgID(_: Long)).expects(10).returning(Success(List("hubspot")))
      val res = triggerService.findAllCrmIntegrationByOrgId(orgId = 10)
      assert(res == Success( CRMIntegrations(true,false,false,false,false,false)))
    }
    it("should success with some crm itegration type(hubspot,pipedrive)") {
      (triggerDAO.findAllCRMIntegrationsByOrgID(_: Long)).expects(10).returning(Success(List("hubspot","pipedrive")))
      val res = triggerService.findAllCrmIntegrationByOrgId(orgId = 10)
      assert(res == Success(CRMIntegrations(true, true, false, false, false,false)))
    }
    it("should success with some crm itegration type(hubspot,pipedrive,zoho)") {
      (triggerDAO.findAllCRMIntegrationsByOrgID(_: Long)).expects(10).returning(Success(List("hubspot", "pipedrive","zoho")))
      val res = triggerService.findAllCrmIntegrationByOrgId(orgId = 10)
      assert(res == Success(CRMIntegrations(true, true, false, true, false,false)))
    }

  }
}
