package app.api.triggers

import api.emails.EmailReceivedForWebhook
import api.sr_audit_logs.models.EventType
import org.scalamock.scalatest.MockFactory
import org.scalatest.funspec.AnyFunSpec
import play.api.Logging
import api.triggers.{TPActivityType, TriggerCondition, TriggerConditionOperatorType, TriggerConditionType, TriggerUtils}
import org.joda.time.DateTime
import play.api.libs.json.Json
import utils.SRLogger
import utils.mq.webhook.MQTriggerConditionProspectEvent

class TriggerUtilsSpec  extends AnyFunSpec with Logging{

  given Logger: SRLogger = new SRLogger("TriggerUtilsSpec")
  val prospect_id: Long = 2
  val campaign_id: Option[Long] = Some(4)
  val email_scheduled_id: Option[Long] = Some(6)
  val updated_prospect_category_id: Option[Long] = Some(8)

  val prospectEventForConditionCheck = MQTriggerConditionProspectEvent(
    prospect_id = prospect_id,
    campaign_id = campaign_id,
    email_scheduled_id = email_scheduled_id,
    updated_prospect_category_id = updated_prospect_category_id
  )

  describe("_checkConditionsV2 NO condition") {

    it("should send true when no conditions") {
      val result = TriggerUtils._checkConditionsV2(
        conditions = None,
        prospectEventForConditionCheck = prospectEventForConditionCheck,
        logName = "Test"
      )
      assert(result.get)
    }
  }

  describe("_checkConditionsV2 CAMPAIGN condition") {

    it("should send true when Some conditions and campaign id is in allowed campaign id") {
      val campaignCondition = TriggerCondition(
        condition_type = TriggerConditionType.CAMPAIGN,
        field = "field",
        operator = TriggerConditionOperatorType.EQUAL,
        value = "4,6"
      )
      val result = TriggerUtils._checkConditionsV2(
        conditions = Some(Seq(campaignCondition)),
        prospectEventForConditionCheck = prospectEventForConditionCheck,
        logName = "Test"
      )
      assert(result.get)
    }


    it("should send false when Some conditions and  campaign id is not in allowed campaign id") {
      val campaignCondition = TriggerCondition(
        condition_type = TriggerConditionType.CAMPAIGN,
        field = "field",
        operator = TriggerConditionOperatorType.EQUAL,
        value = "6"
      )
      val result = TriggerUtils._checkConditionsV2(
        conditions = Some(Seq(campaignCondition)),
        prospectEventForConditionCheck = prospectEventForConditionCheck,
        logName = "Test"
      )
      assert(!result.get)
    }


    it("should send failure when Some conditions and No campaign_id is given") { //FIXME
      val campaignCondition = TriggerCondition(
        condition_type = TriggerConditionType.CAMPAIGN,
        field = "field",
        operator = TriggerConditionOperatorType.EQUAL,
        value = "6"
      )
      val result = TriggerUtils._checkConditionsV2(
        conditions = Some(Seq(campaignCondition)),
        prospectEventForConditionCheck = prospectEventForConditionCheck.copy(campaign_id = None),
        logName = "Test"
      )
      assert(result.isFailure) //I think it should be Failure
    }

    it("should send failure when Some conditions and No allowed campaign_id") { //FIXME
      val campaignCondition = TriggerCondition(
        condition_type = TriggerConditionType.CAMPAIGN,
        field = "field",
        operator = TriggerConditionOperatorType.EQUAL,
        value = ""
      )
      val result = TriggerUtils._checkConditionsV2(
        conditions = Some(Seq(campaignCondition)),
        prospectEventForConditionCheck = prospectEventForConditionCheck,
        logName = "Test"
      )
      assert(result.isFailure) //I think it should be Failure
    }

  }

  describe("_checkConditionsV2 CATEGORY condition") {

    it("should send true when Some conditions and some category_id is given and matches the allowed category_id") {
      val campaignCondition = TriggerCondition(
        condition_type = TriggerConditionType.CATEGORY,
        field = "field",
        operator = TriggerConditionOperatorType.EQUAL,
        value = "8"
      )
      val result = TriggerUtils._checkConditionsV2(
        conditions = Some(Seq(campaignCondition)),
        prospectEventForConditionCheck = prospectEventForConditionCheck,
        logName = "Test"
      )
      assert(result.get)
    }

    it("should send false when Some conditions and some category_id but not in the allowed") {
      val campaignCondition = TriggerCondition(
        condition_type = TriggerConditionType.CATEGORY,
        field = "field",
        operator = TriggerConditionOperatorType.EQUAL,
        value = "9"
      )
      val result = TriggerUtils._checkConditionsV2(
        conditions = Some(Seq(campaignCondition)),
        prospectEventForConditionCheck = prospectEventForConditionCheck,
        logName = "Test"
      )
      assert(!result.get)
    }


    it("should send failure when Some conditions and updated_prospect_category_id = None") { //FIXME
      val campaignCondition = TriggerCondition(
        condition_type = TriggerConditionType.CATEGORY,
        field = "field",
        operator = TriggerConditionOperatorType.EQUAL,
        value = "9"
      )
      val result = TriggerUtils._checkConditionsV2(
        conditions = Some(Seq(campaignCondition)),
        prospectEventForConditionCheck = prospectEventForConditionCheck.copy(updated_prospect_category_id = None),
        logName = "Test"
      )
      assert(result.isFailure) //I think it should be Failure
    }

    it("should send failure when Some conditions and Some updated_prospect_category_id but  but allowed category_id list is none") { //FIXME
      val campaignCondition = TriggerCondition(
        condition_type = TriggerConditionType.CATEGORY,
        field = "field",
        operator = TriggerConditionOperatorType.EQUAL,
        value = ""
      )
      val result = TriggerUtils._checkConditionsV2(
        conditions = Some(Seq(campaignCondition)),
        prospectEventForConditionCheck = prospectEventForConditionCheck,
        logName = "Test"
      )
      assert(result.isFailure) //I think it should be Failure
    }
  }

  describe("_checkConditionsV2 Other conditions and multiple conditions") {
    it("should send failure if wrong condition type") { //FIXME
      val campaignCondition = TriggerCondition(
        condition_type = TriggerConditionType.ACCOUNT,
        field = "field",
        operator = TriggerConditionOperatorType.EQUAL,
        value = ""
      )
      val result = TriggerUtils._checkConditionsV2(
        conditions = Some(Seq(campaignCondition)),
        prospectEventForConditionCheck = prospectEventForConditionCheck,
        logName = "Test"
      )
      assert(result.isFailure) //I think it should be Failure
    }

    it("should send failure if one wrong fail condition one success condition") { //FIXME
      val campaignCondition1 = TriggerCondition(
        condition_type = TriggerConditionType.CAMPAIGN,
        field = "field",
        operator = TriggerConditionOperatorType.EQUAL,
        value = "4,6"
      )
      val campaignCondition2 = TriggerCondition(
        condition_type = TriggerConditionType.CATEGORY,
        field = "field",
        operator = TriggerConditionOperatorType.EQUAL,
        value = ""
      )
      val result = TriggerUtils._checkConditionsV2(
        conditions = Some(Seq(campaignCondition1, campaignCondition2)),
        prospectEventForConditionCheck = prospectEventForConditionCheck,
        logName = "Test"
      )
      assert(result.isFailure) //I think it should be Failure
    }
  }

  describe("buildActivityDetailedSubject") {
    val received_at = DateTime.now()
    val emailObj = EmailReceivedForWebhook(
      id = 123,

      campaign_name = Some("campaignName"),

      prospect_id = prospect_id,
      email = "<EMAIL>",
      first_name = Some(""),
      last_name = None,
      prospect_list = None,
      custom_fields = Json.obj(),
      company = None,
      city = None,
      country = None,

      subject = "",
      body = "",
      body_text = "",
      email_thread_id = None,
      from_email = "",
      received_at = received_at,
      step_name = None,

      sent_at = None,
      opened_at = None,
      clicked_at = None,
      bounced_at = None,
      replied_at = None,
      auto_reply_at = None,
      out_of_office_reply_at = None
    )

    it("should give response for EventType.EMAIL_SENT"){
      val result = TriggerUtils.buildActivityDetailedSubject(
        event = EventType.EMAIL_SENT,
        stepName = Some("stepName"),
        campaignName = Some("campaignName"),
        emailObj = Some(emailObj)
      )

      assert(result.activitySubject == "Email sent")
      assert(result.activityType == TPActivityType.EMAIL)
      assert(result.detailedActivitySubject == s"Sent stepName email from campaignName campaign")

    }

    it("should give response for EventType.EMAIL_OPENED") {
      val result = TriggerUtils.buildActivityDetailedSubject(
        event = EventType.EMAIL_OPENED,
        stepName = Some("stepName"),
        campaignName = Some("campaignName"),
        emailObj = Some(emailObj)
      )

      assert(result.activitySubject == "Email opened")
      assert(result.activityType == TPActivityType.EMAIL)
      assert(result.detailedActivitySubject == s"Opened stepName email from campaignName campaign")

    }

    it("should give response for EventType.EMAIL_LINK_CLICKED") {
      val result = TriggerUtils.buildActivityDetailedSubject(
        event = EventType.EMAIL_LINK_CLICKED,
        stepName = Some("stepName"),
        campaignName = Some("campaignName"),
        emailObj = Some(emailObj)
      )

      assert(result.activitySubject == "Email link clicked")
      assert(result.activityType == TPActivityType.EMAIL)
      assert(result.detailedActivitySubject == s"Clicked stepName email from campaignName campaign")

    }

    it("should give response for EventType.EMAIL_BOUNCED") {
      val result = TriggerUtils.buildActivityDetailedSubject(
        event = EventType.EMAIL_BOUNCED,
        stepName = Some("stepName"),
        campaignName = Some("campaignName"),
        emailObj = Some(emailObj)
      )

      assert(result.activitySubject == "Email bounced")
      assert(result.activityType == TPActivityType.EMAIL)
      assert(result.detailedActivitySubject == s"Bounced on stepName email from campaignName campaign")

    }

    it("should give response for EventType.NEW_REPLY") {
      val result = TriggerUtils.buildActivityDetailedSubject(
        event = EventType.NEW_REPLY,
        stepName = Some("stepName"),
        campaignName = Some("campaignName"),
        emailObj = Some(emailObj)
      )

      assert(result.activitySubject == "New reply")
      assert(result.activityType == TPActivityType.EMAIL)
      assert(result.detailedActivitySubject == s"Replied to stepName email from campaignName campaign")

    }

    it("should give response for EventType.AUTO_REPLY") {
      val result = TriggerUtils.buildActivityDetailedSubject(
        event = EventType.AUTO_REPLY,
        stepName = Some("stepName"),
        campaignName = Some("campaignName"),
        emailObj = Some(emailObj)
      )

      assert(result.activitySubject == "Auto reply")
      assert(result.activityType == TPActivityType.EMAIL)
      assert(result.detailedActivitySubject == s"Auto reply to stepName email from campaignName campaign")

    }

    it("should give response for EventType.OUT_OF_OFFICE_REPLY") {
      val result = TriggerUtils.buildActivityDetailedSubject(
        event = EventType.OUT_OF_OFFICE_REPLY,
        stepName = Some("stepName"),
        campaignName = Some("campaignName"),
        emailObj = Some(emailObj)
      )

      assert(result.activitySubject == "Out of office reply")
      assert(result.activityType == TPActivityType.EMAIL)
      assert(result.detailedActivitySubject == s"Out of office reply to stepName email from campaignName campaign")

    }

    it("should give response for EventType.EMAIL_INVALID") {
      val result = TriggerUtils.buildActivityDetailedSubject(
        event = EventType.EMAIL_INVALID,
        stepName = Some("stepName"),
        campaignName = Some("campaignName"),
        emailObj = Some(emailObj)
      )

      assert(result.activitySubject == "Email is invalid")
      assert(result.activityType == TPActivityType.NOTE)
      assert(result.detailedActivitySubject == s"Invalid email on stepName email from campaignName campaign")

    }

    it("should give response for EventType.PROSPECT_OPTED_OUT") {
      val result = TriggerUtils.buildActivityDetailedSubject(
        event = EventType.PROSPECT_OPTED_OUT,
        stepName = Some("stepName"),
        campaignName = Some("campaignName"),
        emailObj = Some(emailObj)
      )

      assert(result.activitySubject == "Opted out from campaign:  campaignName")
      assert(result.activityType == TPActivityType.NOTE)
      assert(result.detailedActivitySubject == s"Opted out from stepName email from campaignName campaign")

    }

    it("should give response for EventType.PROSPECT_COMPLETED") {
      val result = TriggerUtils.buildActivityDetailedSubject(
        event = EventType.PROSPECT_COMPLETED,
        stepName = Some("stepName"),
        campaignName = Some("campaignName"),
        emailObj = Some(emailObj)
      )

      assert(result.activitySubject == "Campaign completed for prospect :  campaignName")
      assert(result.activityType == TPActivityType.NOTE)
      assert(result.detailedActivitySubject == s"Campaign completed stepName email from campaignName campaign")

    }

    it("should give response for EventType.ADD_TO_DO_NOT_CONTACT") {
      val result = TriggerUtils.buildActivityDetailedSubject(
        event = EventType.ADD_TO_DO_NOT_CONTACT,
        stepName = Some("stepName"),
        campaignName = Some("campaignName"),
        emailObj = Some(emailObj)
      )

      assert(result.activitySubject == "")
      assert(result.activityType == TPActivityType.EMAIL)
      assert(result.detailedActivitySubject == s"")

    }
  }

}
