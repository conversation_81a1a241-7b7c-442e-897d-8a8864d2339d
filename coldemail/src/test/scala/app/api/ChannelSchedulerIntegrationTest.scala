package app.api

import db_test_spec.api.DbTestingBeforeAllAndAfterAll
import org.scalamock.scalatest.AsyncMockFactory
import org.scalatest.funspec.AsyncFunSpec

class ChannelSchedulerIntegrationTest extends AsyncMockFactory with DbTestingBeforeAllAndAfterAll {

  describe("Scheduler Integration Test"){

    /* Commenting it out as test was setup on dev2 I will later bring it in on dev5
    it("expected single task to be created but 2 will be created hence test fails currently") {

      val logger: SRLogger = new SRLogger("ChannelSchedulerIntegrationTest")
      if (true) {
        val input_staging_5 = SchedulerIntegrationTestUtils.schedulerIntegrationTestFor_staging_2
        val result = multichannelTestUtil.createAndScheduleCampaignManualEmail(
          orgId = input_staging_5.orgId,
          accountId = input_staging_5.accountId,
          teamId = input_staging_5.teamId,
          taId = input_staging_5.taId,
          timezone = input_staging_5.timezone,
          linkedinAccountSettingId = input_staging_5.linkedinAccountSettingId,
          receiver_email_setting_id = input_staging_5.receiver_email_setting_id,
          sender_email_setting_id = input_staging_5.sender_email_setting_id,
          prospect_categories_custom_not_categorized = input_staging_5.prospect_categories_custom_not_categorized,
          prospect_categories_custom_do_not_contact = input_staging_5.prospect_categories_custom_do_not_contact,
          current_sending_email_accounts = input_staging_5.current_sending_email_accounts,
          emailSettingId = input_staging_5.emailSettingId,
          schedule_from_time_sec = input_staging_5.schedule_from_time_sec,
          schedule_till_time_sec = input_staging_5.schedule_till_time_sec,
          enableEmailScheduler = input_staging_5.enableEmailScheduler,
          campaign_name = input_staging_5.campaign_name,
          prospectEmail = input_staging_5.prospectEmail
        )

        result.map(p => {
          //              println(s"list of task : ${p}")
          assert(p.length == 1)
        }).recover({ case e => {
          assert(false)
        }
        })
      } else {
        assert(true)
      }
    }
     */


  }
}
