import org.scalamock.scalatest.MockFactory
import org.scalatest.funspec.AnyFunSpec
import org.scalatest.matchers.should.Matchers
import utils.StringUtils

class StringUtilsSpec extends AnyFunSpec with MockFactory with Matchers {

  describe("base32EncodeURIString and base32DecodeURIString") {
    it("should encode and decode simple lowercase letters") {
      val input = "hello"
      val encoded = StringUtils.base32EncodeURIString(input)
      val decoded = StringUtils.base32DecodeURIString(encoded)
      decoded shouldBe input
    }

    it("should encode and decode simple uppercase letters") {
      val input = "HELLO"
      val encoded = StringUtils.base32EncodeURIString(input)
      val decoded = StringUtils.base32DecodeURIString(encoded)
      decoded shouldBe input
    }

    it("should encode and decode mixed case letters") {
      val input = "HeLLo"
      val encoded = StringUtils.base32EncodeURIString(input)
      val decoded = StringUtils.base32DecodeURIString(encoded)
      decoded shouldBe input
    }

    it("should encode and decode numbers") {
      val input = "12345"
      val encoded = StringUtils.base32EncodeURIString(input)
      val decoded = StringUtils.base32DecodeURIString(encoded)
      decoded shouldBe input
    }

    it("should encode and decode alphanumeric strings") {
      val input = "Hello123"
      val encoded = StringUtils.base32EncodeURIString(input)
      val decoded = StringUtils.base32DecodeURIString(encoded)
      decoded shouldBe input
    }

    it("should encode and decode strings with special characters") {
      val input = "Hello@123#World"
      val encoded = StringUtils.base32EncodeURIString(input)
      val decoded = StringUtils.base32DecodeURIString(encoded)
      decoded shouldBe input
    }

    it("should encode and decode empty string") {
      val input = ""
      val encoded = StringUtils.base32EncodeURIString(input)
      val decoded = StringUtils.base32DecodeURIString(encoded)
      decoded shouldBe input
    }

    it("should encode and decode strings with spaces") {
      val input = "Hello World 123"
      val encoded = StringUtils.base32EncodeURIString(input)
      val decoded = StringUtils.base32DecodeURIString(encoded)
      decoded shouldBe input
    }

    it("should encode and decode strings with Unicode characters") {
      val input = "Hello 世界 123"
      val encoded = StringUtils.base32EncodeURIString(input)
      val decoded = StringUtils.base32DecodeURIString(encoded)
      decoded shouldBe input
    }

    it("should handle case-insensitive decoding") {
      val input = "Hello123"
      val encoded = StringUtils.base32EncodeURIString(input)
      // Convert encoded string to lowercase and try to decode
      val decoded = StringUtils.base32DecodeURIString(encoded.toLowerCase)
      decoded shouldBe input
    }

    it("should encode and decode long strings") {
      val input = "This is a very long string with multiple words and numbers 1234567890 and special characters @#$%^&*()"
      val encoded = StringUtils.base32EncodeURIString(input)
      val decoded = StringUtils.base32DecodeURIString(encoded)
      decoded shouldBe input
    }
  }
}

