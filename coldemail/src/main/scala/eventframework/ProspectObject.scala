package eventframework

import api.HelpfulErrorMessageTrait
import api.accounts.AccountUuid
import api.campaigns.models.CampaignStepType
import api.columns.{ColumnDef, ColumnDefsProspectsDetails, MagicColumnResponse}
import api.prospects.models.PotentialDuplicateProspectLogId
import api.prospects.service.{ProspectEmail, ProspectEmailData}
import api.prospects.{ProspectSource, ProspectUuid}
import api.tags.models.ProspectTag
import api.team_inbox.service.{ReplySentimentForTeam, ReplySentimentUuid}
import org.joda.time.DateTime
import org.postgresql.util.PGobject
import play.api.libs.json.{Format, JsObject, JsValue, Json, OFormat, OWrites, Reads, Writes}
import play.api.libs.json.JodaWrites.*
import play.api.libs.json.JodaReads.*
import scalikejdbc.WrappedResultSet
import scalikejdbc.jodatime.JodaWrappedResultSet.*
import utils.Helpers.sanitizeOptionString
import api.leadfinder_credits.CreditOperationOutput

import scala.util.{Failure, Success}
import utils.sr_json_utils.CampaignStepTypeJsonUtil.jsonFormatCampaignStepType

case class ProspectObjectInternal(


  owner_name: String,
  owner_email: String,

  email_domain: Option[String],
  invalid_email: Option[Boolean],

  last_contacted_at: Option[DateTime], // FIXME: remove this from internal in backend and v1/v2 frontend
  last_replied_at: Option[DateTime],
  last_opened_at: Option[DateTime],
  last_call_made_at: Option[DateTime],

  list_id: Option[Long],

  prospect_category_id_custom: Long,
  prospect_category_label_color: String,

  prospect_source: Option[String],


  prospect_account_id: Option[Long], //table -> prospect_accounts.id
  prospect_account_uuid: Option[String],
  prospect_account: Option[String], //table -> prospect_accounts.name


  total_opens: Int,
  total_clicks: Int,

  active_campaigns: Option[JsValue],

  /* only in case of specific campaign prospect table in frontend */
  current_campaign_id: Option[Long],

  magic_columns: List[MagicColumnResponse],

  tags: Option[List[ProspectTag]],
  flags: JsValue,
  latest_reply_sentiment: Option[ReplySentimentForTeam]
)

object ProspectObjectInternal {
  // implicit val writes = Json.writes[ProspectObjectInternal]
  implicit val writes: Writes[ProspectObjectInternal] = new Writes[ProspectObjectInternal] {

    def writes(pi: ProspectObjectInternal) = {

      /**
        * 16 Nov 2024
        *
        * To match the structure of custom columns
        *
        * The column name is unique,
        * so there should be only 1 key-value pair for a particular column name.
        */
      val colNameToColDefMap: Map[String, JsObject] =
        pi.magic_columns.map(mc =>
          mc.column_name -> MagicColumnResponse.formatMagicColumnByRemovingColumnName(mc)
        ).toMap

      Json.obj(

        "owner_name" -> pi.owner_name,
        "owner_email" -> pi.owner_email,

        "invalid_email" -> pi.invalid_email,

        "list_id" -> pi.list_id,


        "last_contacted_at" -> pi.last_contacted_at,
        "last_replied_at" -> pi.last_replied_at,
        "last_opened_at" -> pi.last_opened_at,
        "last_call_made_at" -> pi.last_call_made_at,

        "total_opens" -> pi.total_opens,
        "total_clicks" -> pi.total_clicks,

        "prospect_category_id_custom" -> pi.prospect_category_id_custom,
        "prospect_category_label_color" -> pi.prospect_category_label_color,

        "prospect_source" -> pi.prospect_source,

        "prospect_account_id" -> pi.prospect_account_id,
        "prospect_account_uuid" -> pi.prospect_account_uuid,
        "prospect_account" -> pi.prospect_account,

        "active_campaigns" -> pi.active_campaigns,

        "magic_columns" -> colNameToColDefMap,

        "tags" -> pi.tags,
        "current_campaign_id" -> pi.current_campaign_id,

        "flags" -> pi.flags,
        "latest_reply_sentiment" -> pi.latest_reply_sentiment

      )

    }
  }
}

case class StandardProspectFields(
           firstName: Option[String] = None,
           lastName: Option[String] = None,
           email: Option[String] = None,
           jobTitle: Option[String] = None,
           company: Option[String] = None,
           linkedinUrl: Option[String] = None,
           phone: Option[String] = None,
           city: Option[String] = None,
           state: Option[String] = None,
           country: Option[String] = None,
           timezone: Option[String] = None
         )

object StandardProspectFields {
  implicit val format: OFormat[StandardProspectFields] = Json.format[StandardProspectFields]
}
case class ProspectFieldsResult(
                                 standardFields: StandardProspectFields,
                                 customFields: Map[String, Option[String]]
                               )

object ProspectFieldsResult {

  implicit val writes: Writes[ProspectFieldsResult] = new Writes[ProspectFieldsResult] {
    def writes(data: ProspectFieldsResult): JsObject = {

      val customFieldsJson: Map[String, String] = data.customFields.flatMap { field =>
        if(field._2.isDefined && field._2.get.trim.nonEmpty){
          Map(field._1 -> field._2.get)
        } else Map()
      }

      Json.obj(
       "standard_fields" -> Json.toJson(data.standardFields),
        "custom_fields" -> Json.toJson(customFieldsJson)
      )
    }
  }}

// PROSPECTCOLUMNCHANGETAG
case class ProspectObject(
  id: Long,
  owner_id: Long,
  team_id: Long,

  first_name: Option[String],
  last_name: Option[String],

  email: Option[String],

  custom_fields: JsValue,

  list: Option[String],

  job_title: Option[String],
  company: Option[String],
  linkedin_url: Option[String],
  phone: Option[String],
  phone_2: Option[String],
  phone_3: Option[String],

  city: Option[String],
  state: Option[String],
  country: Option[String],
  timezone: Option[String],

  prospect_category: String, // display name

  last_contacted_at: Option[DateTime],
  last_contacted_at_phone: Option[String],

  created_at: DateTime,


  /* internal columns only for smartreach website, not for public api */
  internal: ProspectObjectInternal,
  latest_reply_sentiment_uuid: Option[ReplySentimentUuid],
  current_step_type:Option[CampaignStepType],
  latest_task_done_at:Option[DateTime],

  prospect_uuid: Option[ProspectUuid],
  owner_uuid: AccountUuid,
  updated_at: DateTime

) extends CreditOperationOutput with PaginationSortedData {
  override def getSortBy: DateTime = created_at
  override def getExactlyAtIdOrUuid: String = id.toString  //needs to be id as it is getting matched on id in the DAO.
}

object ProspectObject {

  implicit val writes: Writes[ProspectObject] = Json.writes[ProspectObject]
  def fromDb(
    rs: WrappedResultSet,

    /* only in case of specific campaign prospect table in frontend */
    is_campaign: Option[Long] = None

  ): ProspectObject = {


    val prospectCategory = rs.string("prospect_category_custom")
    val prospectCategoryLabelColor = rs.string("prospect_category_label_color")
    val prospectCategoryIdCustom = rs.long("prospect_category_id_custom")

    val forceSendEnabled = rs.booleanOpt("force_send_invalid_email").getOrElse(false)

    val source = rs.stringOpt("prospect_source")
      .map(s => ProspectSource.getFullNameFromSource(s))

    val ownerAccountId = rs.long("owner_id")

    ProspectObject(
      id = rs.long("id"),

      owner_id = ownerAccountId,

      team_id = rs.long("team_id"),

      first_name = rs.stringOpt("first_name"),
      last_name = rs.stringOpt("last_name"),

      email = rs.stringOpt("email"),

      last_contacted_at = rs.jodaDateTimeOpt("last_contacted_at"),
      last_contacted_at_phone = rs.stringOpt("last_contacted_at_phone"),

      created_at = rs.jodaDateTime("created_at"),

      custom_fields = Json.parse(rs.any("custom_fields").asInstanceOf[PGobject].getValue),

      list = rs.stringOpt("list"),

      company = rs.stringOpt("company"),
      job_title = rs.stringOpt("job_title"),
      phone = sanitizeOptionString(rs.stringOpt("phone")),
      phone_2 = sanitizeOptionString(rs.stringOpt("phone_2")),
      phone_3 = sanitizeOptionString(rs.stringOpt("phone_3")),

      linkedin_url = rs.stringOpt("linkedin_url"),

      city = rs.stringOpt("city"),
      state = rs.stringOpt("state"),
      country = rs.stringOpt("country"),
      timezone = rs.stringOpt("timezone"),


      prospect_category = prospectCategory,
      latest_reply_sentiment_uuid = rs.stringOpt("latest_reply_sentiment_uuid").map(ReplySentimentUuid(_)),

      internal = {

        val activeCampaigns = rs.anyOpt("active_campaigns")
        val tags = {
          val tags = rs.anyOpt("tags")
          if (tags.isEmpty) {
            None
          } else {
            Some(Json.parse(tags.get.asInstanceOf[PGobject].getValue).as[List[ProspectTag]])
          }
        }

        val magic_columns = {
          val magicColsAsAny = rs.anyOpt("magic_columns")
          if (magicColsAsAny.isEmpty) {
            List()
          } else {
            Json.parse(magicColsAsAny.get.asInstanceOf[PGobject].getValue).as[List[MagicColumnResponse]]
          }
        }

        ProspectObjectInternal(

          owner_name = rs.string("owner_name"),
          owner_email = rs.string("owner_email"),

          list_id = rs.longOpt("list_id"),

          email_domain = rs.stringOpt("email_domain"),
          invalid_email = rs.booleanOpt("invalid_email"),

          prospect_category_label_color = prospectCategoryLabelColor,
          prospect_category_id_custom = prospectCategoryIdCustom,

          prospect_source = source,

          prospect_account = rs.stringOpt("prospect_account_name"),
          prospect_account_id = rs.longOpt("prospect_account_id"),
          prospect_account_uuid = rs.stringOpt("prospect_account_uuid"),

          last_contacted_at = rs.jodaDateTimeOpt("last_contacted_at"),
          last_replied_at = rs.jodaDateTimeOpt("last_replied_at"),
          last_opened_at = rs.jodaDateTimeOpt("last_opened_at"),
            last_call_made_at=rs.jodaDateTimeOpt("last_call_made_at"),

          total_opens = rs.int("total_opens"),
          total_clicks = rs.int("total_clicks"),

          magic_columns = magic_columns,

          tags = tags,


          active_campaigns = if (activeCampaigns.isEmpty) None else Some(
            Json.parse(activeCampaigns.get.asInstanceOf[PGobject].getValue)
          ),

          /* only in case of specific campaign prospect table in frontend */
          current_campaign_id = is_campaign,


          flags = Json.obj(
            "will_delete" -> rs.boolean("will_delete"),
            "email_bounced" -> rs.booleanOpt("email_bounced"),
            "force_send_invalid_email" -> forceSendEnabled
          ),
          latest_reply_sentiment = None
        )
      },
      current_step_type = if(is_campaign.isDefined) {
        CampaignStepType.fromKey(rs.stringOpt("current_step_type").getOrElse("")) match {
          case Failure(exception) => None
          case Success(value) => Some(value)
        }
      }else {
        None
      },
      latest_task_done_at = rs.jodaDateTimeOpt("latest_task_done_at"),
      prospect_uuid = rs.stringOpt("prospect_uuid").map(ProspectUuid(_)),
      owner_uuid = AccountUuid(rs.string("owner_uuid")),
      updated_at = rs.jodaDateTime("updated_at")
    )

  }

  // PROSPECTCOLUMNCHANGETAG
  def apiStructure(
    prospect: ProspectObject,
    isInternalRequest: Boolean = false,
    isWebhook: Boolean = false  // added this flag to check if the call is from webhook, if so, we add tags in the Json.obj as a CSV string
  ): JsObject = {

    var publicApiData = Json.obj(
      "object" -> SrResourceTypes.PROSPECT.toKey,

      "id" -> prospect.id,
      "owner_id" -> prospect.owner_id,
      "team_id" -> prospect.team_id,

      "first_name" -> prospect.first_name,
      "last_name" -> prospect.last_name,
      "email" -> prospect.email,

      "custom_fields" -> prospect.custom_fields,

      "list" -> prospect.list,

      "company" -> prospect.company,
      "job_title" -> prospect.job_title,
      "linkedin_url" -> prospect.linkedin_url,
      "phone" -> prospect.phone,

      "city" -> prospect.city,
      "state" -> prospect.state,
      "country" -> prospect.country,
      "timezone" -> prospect.timezone,

      "prospect_category" -> prospect.prospect_category,

      "last_contacted_at" -> prospect.last_contacted_at,
      "created_at" -> prospect.created_at,
      "latest_reply_sentiment_uuid" -> prospect.latest_reply_sentiment_uuid,
      "current_step_type" -> prospect.current_step_type,
      "latest_task_done_at"->prospect.latest_task_done_at,

      "prospect_uuid" -> prospect.prospect_uuid,
      "owner_uuid" -> prospect.owner_uuid,
      "updated_at" -> prospect.updated_at
    )

    if(isWebhook && prospect.internal.tags.isDefined) {
      publicApiData = publicApiData ++ Json.obj(
        "tags" -> {
          if (prospect.internal.tags.get.nonEmpty) {
            prospect.internal.tags.get.map(_.tag).mkString(", ")
          } else ""
        }
      )
    }

    if (isInternalRequest) {
      publicApiData = publicApiData ++ Json.obj(
        "phone_2" -> prospect.phone_2,
        "phone_3" -> prospect.phone_3,
        "last_contacted_at_phone" -> prospect.last_contacted_at_phone,
        "internal" -> prospect.internal
      )
    }

    publicApiData

  }

  def apiStructureWithAllCustomColumns(
    prospect: ProspectObject,
    prospectCustomColumns: Seq[ColumnDef],
    isWebhook: Boolean
  ): JsObject = {

    val customFieldsWithNullValuesAsWell = ColumnDef.getCustomFieldsJsonWithAllCustomColumns(
      customFieldObj = prospect.custom_fields,
      customFieldNames = prospectCustomColumns
        .filter(_.is_custom)
        .map(_.name)
    )

    val apiStruct = apiStructure(
      prospect = prospect,
      isWebhook = isWebhook
    )

    apiStruct ++ Json.obj(
      "custom_fields" -> customFieldsWithNullValuesAsWell
    )

  }

  def extractStandardFields(prospect: ProspectObject): StandardProspectFields = {
    StandardProspectFields(
      firstName = prospect.first_name,
      lastName = prospect.last_name,
      email = prospect.email,
      jobTitle = prospect.job_title,
      company = prospect.company,
      linkedinUrl = prospect.linkedin_url,
      phone = prospect.phone,
      city = prospect.city,
      state = prospect.state,
      country = prospect.country,
      timezone = prospect.timezone
    )
  }

  def extractAllFields(
                        prospect: ProspectObject,
                        selectedCustomColumns: List[String] = List.empty
                      ): ProspectFieldsResult = {
    // Always extract standard fields
    val standardFields = extractStandardFields(prospect)

    // Extract custom fields if any are selected
    val customFields = selectedCustomColumns.map { column =>
      column -> extractCustomField(prospect, column)
    }.toMap

    ProspectFieldsResult(
      standardFields = standardFields,
      customFields = customFields
    )
  }

  private def extractCustomField(
                                  prospect: ProspectObject,
                                  columnName: String
                                ): Option[String] = {
    prospect.custom_fields match {
      case jsValue if jsValue != null =>
        jsValue.asOpt[JsObject].flatMap { obj =>
          (obj \ columnName).asOpt[String]
        }
      case _ => None
    }
  }


  //////// DUMMY PROSPECT OBJECT //////////
  // used while creating inbox draft for non-existing/new prospects
  val   dummyProspectObj =     ProspectObject(
    id = 0,

    owner_id = 0,

    team_id = 0,

    first_name = Some(""),
    last_name = None,

    email = Some(""), //TODO: EMAIL_OPTIONAL re-evaluate whether this should be none/empty string

    last_contacted_at = None,
    last_contacted_at_phone = None,

    created_at = DateTime.now,

    custom_fields = Json.obj(),

    list = None,

    company = None,
    job_title = None,
    phone = None,
    phone_2 = None,
    phone_3 = None,

    linkedin_url = None,

    city = None,
    state = None,
    country = None,
    timezone = None,


    prospect_category = "",
    latest_reply_sentiment_uuid = None,

    internal = {

      ProspectObjectInternal(

        owner_name = "",
        owner_email = "",

        list_id = None,

        email_domain = Some(""), //TODO: EMAIL_OPTIONAL re-evaluate whether this should be none/empty string
        invalid_email = None,

        prospect_category_label_color = "",
        prospect_category_id_custom = 0,

        prospect_source = None,

        prospect_account = None,
        prospect_account_id = None,
        prospect_account_uuid = None,

        last_contacted_at = None,
        last_replied_at = None,
        last_opened_at = None,
          last_call_made_at = None,

        total_opens = 0,
        total_clicks = 0,

        magic_columns = List(),

        tags = None,

        active_campaigns = None,

        /* only in case of specific campaign prospect table in frontend */
        current_campaign_id = None,


        flags = Json.obj(),
        latest_reply_sentiment = None
      )
    },
    current_step_type =Some(CampaignStepType.SmsMessage),
    latest_task_done_at = None,

    prospect_uuid = Some(ProspectUuid("prs_aa_abcdefghi")),
    owner_uuid = AccountUuid("acc_aa_abcdegfhi"),
    updated_at = DateTime.now()

  )


}

//25 June 2024: This is only for internal api
case class ProspectBasicInfo(
                              id: Long,
                              owner_name: String,

                              email: Seq[ProspectEmail],
                              linkedin_url: Option[String],
                              phone: Option[String],

                              company: Option[String],
                              first_name: Option[String],
                              last_name: Option[String],

                              job_title: Option[String],
                              city: Option[String],
                              state: Option[String],
                              country: Option[String]
                            )

object ProspectBasicInfo {
  implicit val writes: Writes[ProspectBasicInfo] = new Writes[ProspectBasicInfo] {

    def writes(p: ProspectBasicInfo): JsObject = {

      Json.obj(
        "object" -> SrResourceTypes.PROSPECT_BASIC.toKey,
        "id" -> p.id,
        "owner_name" -> p.owner_name,

        "email" -> p.email,
        "linkedin_url" -> p.linkedin_url,
        "phone" -> p.phone,

        "company" -> p.company,
        "first_name" -> p.first_name,
        "last_name" -> p.last_name,

        "job_title" -> p.job_title,
        "city" -> p.city,
        "state" -> p.state,
        "country" -> p.country
      )

    }
  }

  implicit val reads: Reads[ProspectBasicInfo] = Json.reads[ProspectBasicInfo]

  def constructBasicInfoFromProspectObject(
                                            prospectObject: ProspectObject,
                                            prospectEmailsData: Seq[ProspectEmail]
                                          ): ProspectBasicInfo = {
    ProspectBasicInfo(
      id = prospectObject.id,
      owner_name = prospectObject.internal.owner_name,

      email = prospectEmailsData,
      linkedin_url = prospectObject.linkedin_url,
      phone = prospectObject.phone,

      company = prospectObject.company,
      first_name = prospectObject.first_name,
      last_name = prospectObject.last_name,

      job_title = prospectObject.job_title,
      city = prospectObject.city,
      state = prospectObject.state,
      country = prospectObject.country
    )
  }

}

case class PotentialDuplicateProspectsGroup(
                                             log_id: PotentialDuplicateProspectLogId,
                                             prospects: List[ProspectBasicInfo]
                                           )

object PotentialDuplicateProspectsGroup {
  implicit val writes: Writes[PotentialDuplicateProspectsGroup] = new Writes[PotentialDuplicateProspectsGroup] {

    def writes(p: PotentialDuplicateProspectsGroup): JsObject = {

      Json.obj(
        "log_id" -> p.log_id,
        "prospects" -> p.prospects
      )

    }
  }
}
