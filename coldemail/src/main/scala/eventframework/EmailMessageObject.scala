package eventframework

import io.smartreach.esp.api.emails.IEmailAddress
import org.joda.time.DateTime
import play.api.libs.json.{JsValue, Json, OWrites, Writes}
import play.api.libs.json.JodaWrites._
import sr_scheduler.models.ChannelType

case class ILinkedinProfile(
                             name: Option[String] = None,
                             profile_url: String
                           )

object ILinkedinProfile {
  implicit val writes: OWrites[ILinkedinProfile] = Json.writes[ILinkedinProfile]
}

sealed trait MessageObject {
  def uuid: Option[String]
  def from_user: Boolean
  def subject: String
  def body: String
  def body_preview: String
  def sent_at: DateTime
}

object MessageObject {

  case class EmailMessageObject(
                                 uuid: Option[String],

                                 from_user: <PERSON><PERSON><PERSON>,

                                 from: IEmailAddress,
                                 reply_to: Option[IEmailAddress],
                                 to: Seq[IEmailAddress],

                                 cc_emails: Option[Seq[IEmailAddress]],
                                 bcc_emails: Option[Seq[IEmailAddress]],

                                 subject: String,
                                 body: String,
                                 body_preview: String,

                                 sent_at: DateTime
                               ) extends MessageObject

  object EmailMessageObject {

    implicit val writes: Writes[EmailMessageObject] = new Writes[EmailMessageObject] {

      def writes(rs: EmailMessageObject): JsValue = {

        Json.obj(
          "object" -> SrResourceTypes.EMAIL_MESSAGE.toKey,

          "uuid" -> rs.uuid,

          "from_user" -> rs.from_user,

          "from" -> rs.from,
          "reply_to" -> rs.reply_to,
          "to" -> rs.to,

          "cc_emails" -> rs.cc_emails,
          "bcc_emails" -> rs.bcc_emails,

          "subject" -> rs.subject,
          "body" -> rs.body,
          "body_preview" -> rs.body_preview,

          "sent_at" -> rs.sent_at

        )
      }
    }

    val writesForApi: Writes[EmailMessageObject] = new Writes[EmailMessageObject] {

      def writes(rs: EmailMessageObject): JsValue = {

        Json.obj(
          "object" -> SrResourceTypes.EMAIL_MESSAGE.toKey,

          "email_scheduled_id" -> rs.uuid,

          "from_user" -> rs.from_user,

          "from" -> rs.from,
          "reply_to" -> rs.reply_to,
          "to" -> rs.to,

          "cc_emails" -> rs.cc_emails,
          "bcc_emails" -> rs.bcc_emails,

          "subject" -> rs.subject,
          "body" -> rs.body,
          "body_preview" -> rs.body_preview,

        )
      }
    }

  }

  case class LinkedinMessageObject(
                                    uuid: Option[String],
                                    from_user: Boolean,

                                    from_profile_url: ILinkedinProfile,
                                    to_profile_url: ILinkedinProfile,

                                    subject: String,

                                    body: String,
                                    body_preview: String,

                                    sent_at: DateTime
                                  ) extends MessageObject

  object LinkedinMessageObject {

    implicit val writes: Writes[LinkedinMessageObject] = new Writes[LinkedinMessageObject] {
      override def writes(o: LinkedinMessageObject): JsValue = {

        Json.obj(
          "object" -> SrResourceTypes.LINKEDIN_MESSAGE.toKey,
          "uuid" -> o.uuid,
          "from_user" -> o.from_user,

          "from_profile_url" -> o.from_profile_url,
          "to_profile_url" -> o.to_profile_url,

          "subject" -> o.subject,

          "body" -> o.body,
          "body_preview" -> o.body_preview,

          "sent_at" -> o.sent_at
        )

      }
    }

  }

  implicit def writes: Writes[MessageObject] = new Writes[MessageObject] {
    override def writes(o: MessageObject): JsValue = {
      o match {
        case data: MessageObject.EmailMessageObject => Json.toJson(data)
        case data: MessageObject.LinkedinMessageObject => Json.toJson(data)
      }
    }
  }

}

