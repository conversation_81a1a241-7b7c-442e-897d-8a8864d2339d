/*
package eventframework.services

import eventframework.models.{SrEventData, SrEventObject, SrEventType}
import org.apache.kafka.clients.producer.ProducerRecord
import org.joda.time.DateTime
import play.api.libs.json.Json
import utils.{SRLogger, StringUtils}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.parsing.json.JSON

class SrEventService(
                    kafkaService: KafkaProducerService
                    ) {

  def createEvent(

    eventData: SrEventData,
    eventType: SrEventType, // FIXME EVENTFRAMEWORK

  )(
    implicit ec: ExecutionContext,
    logger: SRLogger
  ): Future[SrEventObject] = {

    val event_id = s"evt_${StringUtils.genLogTraceId}" // FIXME: this will change
    val producer = kafkaService.kafkaProducer
    val topic = eventType.toString
    try {
      val key = event_id
      // This would convert the SrEventData case class to json obj and pass it as string. We can convert it to case class again from json object in the consumer end.
      val value = Json.toJson(eventData).toString
      // Ref Link - https://kafka.apache.org/23/javadoc/org/apache/kafka/clients/producer/ProducerRecord.html
      val record = new ProducerRecord[String, String](topic, key, value)
      val metadata = producer.send(record)

      // FIXME EVENTFRAMEWORK DUMMY IMPLEMENTATION
      Future.successful(

        SrEventObject(
          event_id = event_id,
          event_type = eventType,
          event_data = eventData,
          created_at = DateTime.now()
        )

      )
    } catch {
      case e:Exception =>
        logger.fatal("kafka.createEvent error", err = e)
        Future.failed(new Exception("Event is not sent."))
    } finally {
      // Closing the producer. Producer would restart when a new event occurs.
      producer.close()
    }


  }

}
*/
