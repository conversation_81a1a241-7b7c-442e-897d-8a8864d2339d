package playfilters

import org.apache.pekko.stream.Materializer
import play.api.Environment
import play.api.mvc.{Filter, RequestHeader, Result, Results}

import scala.concurrent.{ExecutionContext, Future}

// On production (NOTE: domain contains "smartreach"), if request comes from
// REF: https://www.clever-cloud.com/blog/engineering/2015/12/01/redirect-to-https-in-play/
// REF (proto header): https://aws.amazon.com/premiumsupport/knowledge-center/redirect-http-https-elb/
class TLSFilter(env: Environment, implicit val mat: Materializer, implicit val ec: ExecutionContext) extends Filter {

  def apply(nextFilter: RequestHeader => Future[Result])(requestHeader: RequestHeader): Future[Result] = {

    //    Logger.info(s"inside TLSFilter::  ${requestHeader.host} :: ${requestHeader.domain}")

    val hostStartsWithWWW = requestHeader.host.toLowerCase.startsWith("www.")
    val host = if (hostStartsWithWWW) requestHeader.host.substring(4) else requestHeader.host

    if (requestHeader.headers.get("X-Forwarded-Proto").getOrElse("http") != "https" && env.mode == play.api.Mode.Prod && requestHeader.domain.toLowerCase.contains("smartreach"))

      if (requestHeader.path.toLowerCase.contains("/api")) {
        Future.successful(Results.BadRequest("Invalid request: use https"))
      } else {
        Future.successful(Results.MovedPermanently("https://" + host + requestHeader.uri))
      }

    else if (hostStartsWithWWW)
      Future.successful(Results.MovedPermanently("https://" + host + requestHeader.uri))
    else
      nextFilter(requestHeader).map(_.withHeaders("Strict-Transport-Security" -> "max-age=31536000; includeSubDomains"))
  }
}
