package app_services.sr_rate_limiter

import api.accounts.models.{AccountId, OrgId}
import app_services.sr_rate_limiter.dao.{SrRateLimitCrossedReportData, SrRateLimitUrlData, SrRateLimiterDAO}
import play.api.libs.json.{Json, OWrites}
import utils.{ParseUtils, SRLogger}

import scala.util.{Failure, Success, Try}


case class TopUrlResponse(
  date: String,
  topUrlHits: Seq[SrRateLimitUrlData]
)

object TopUrlResponse {
  implicit val writes: OWrites[TopUrlResponse] = Json.writes[TopUrlResponse]
}


case class SrRateLimitAccountDataWithHitsByUrl(
  accountId: Long,
  hits: Double,
  hitsByUrl: Seq[SrRateLimitUrlData]
)

object SrRateLimitAccountDataWithHitsByUrl {
  implicit val writes: OWrites[SrRateLimitAccountDataWithHitsByUrl] = J<PERSON>.writes[SrRateLimitAccountDataWithHitsByUrl]
}

case class SrRateLimitAccountDataWithUrl(
  accountId: Long,
  url: String,
  hits: Double
)

case class CostliestAccountsResponse(
  date: String,
  topAccountHits: Seq[SrRateLimitAccountDataWithHitsByUrl]
)

object CostliestAccountsResponse {
  implicit val writes: OWrites[CostliestAccountsResponse] = Json.writes[CostliestAccountsResponse]
}

class SrRateLimiter(
  srRateLimiterDAO: SrRateLimiterDAO
) {


  /*
 this data is used for enforcing rate limits by accountId

 we are enforcing rate limits on a 10-second interval
  */
  private def logRateLimitingCount(

    loggedinAccountId: Long,
    orgId: Long,
    logger: SRLogger

  ): Boolean = {

    srRateLimiterDAO.logRateLimitingCount(
      loggedinAccountId = loggedinAccountId,
      orgId = orgId
    ) match {

      case Failure(exception) =>

        logger.fatal(s"logRateLimitingCount failure", err = exception)

        true

      case Success(latestCount) =>

        val threshold = 10
        // TODO: rate limiting threshold logic will be here

        if (latestCount > threshold) {
          logger.info(s"logRateLimitingCount threshold crossed > $threshold:: latestCount: $latestCount")
        }

        true

    }


  }


  /*
  this data is used for getting costliest accounts report
   */
  private def logUrlHitByAccount(

    apiPath: String,
    loggedinAccountId: Long, // tenantId for now
    logger: SRLogger

  ): Boolean = {

    srRateLimiterDAO.logUrlHitByAccount(
      apiPath = apiPath,
      loggedinAccountId = loggedinAccountId
    ) match {

      case Failure(exception) =>

        logger.fatal(s"logUrlHitByAccount failure", err = exception)

        true

      case Success(latestCount) =>

        val threshold = 5
        // TODO: rate limiting threshold logic will be here

        if (latestCount > threshold) {
          logger.info(s"logUrlHitByAccount threshold crossed > $threshold:: latestCount: $latestCount")
        }

        true

    }


  }

  /*
  this data is used to get the top-urls report
   */
  private def logApiCallFrequency(

    apiPath: String,
    logger: SRLogger

  ): Boolean = {

    srRateLimiterDAO.logApiCallFrequency(
      apiPath = apiPath
    ) match {

      case Failure(exception) =>

        logger.fatal(s"logApiCallFrequency failure", err = exception)

        true

      case Success(latestCount) =>

        // TODO: rate limiting threshold logic will be here

        val threshold = 15

        if (latestCount > threshold) {
          logger.info(s"logApiCallFrequency threshold crossed > $threshold:: latestCount: $latestCount")
        }

        true

    }


  }


  def rateLimitAPIAndLogOverallFrequency(

    apiPath: String,
    loggedinAccountId: Long,
    orgId: Long,
    logger: SRLogger

  ): Boolean = {

    val currentRateLimitCount = logRateLimitingCount(
      loggedinAccountId = loggedinAccountId,
      orgId = orgId,
      logger = logger
    )

    logUrlHitByAccount(
      apiPath = apiPath,
      loggedinAccountId = loggedinAccountId,
      logger = logger
    )

    logApiCallFrequency(
      apiPath = apiPath,
      logger = logger
    )

    currentRateLimitCount

  }

  def rateLimitApiCall(
                        key: String,
                        threshold: Int,
                        expireInSeconds: Long
                      ): Try[Long] = {

    srRateLimiterDAO.getApiHitCount(key)
      .flatMap(currentHitCountOpt => {

        if (currentHitCountOpt.isEmpty) {
          srRateLimiterDAO.incrementApiHitCount(
            key = key,
            expireInSecondsOpt = Some(expireInSeconds)
          )
        }
        else if (currentHitCountOpt.get >= threshold) {
          Failure(new Exception(s"API Hit Limit Reached."))
        }
        else {
          srRateLimiterDAO.incrementApiHitCount(
            key = key,
            expireInSecondsOpt = None
          )
        }
      })
  }

  def getTopUrlsReport(

    thresholdForNumberOfHits: Int = 50,
    month_MM: Int,
    day_DD: Int

  ) = {

    srRateLimiterDAO.getTopUrlsReportRawData(

      thresholdForNumberOfHits = thresholdForNumberOfHits,
      month_MM = month_MM,
      day_DD = day_DD

    ).map(keyRows => {

      keyRows
        .map(keyRow => {

          val monthDate = s"${keyRow.month}${keyRow.dayOfMonth}"

          (monthDate, keyRow.members)

        })
        .groupBy(_._1)
        .view.mapValues(_.flatMap(_._2))
        .mapValues(urlsForDate => {

          urlsForDate
            .groupBy(_.url)
            .map { case (url, hourlyUrlHits) =>

              SrRateLimitUrlData(
                url = url,
                hits = hourlyUrlHits.map(_.hits).sum
              )

            }
            .toSeq
            .sortBy(_.hits)
            .reverse


        })
        .map { case (datetimeString, urlHits) =>

          TopUrlResponse(
            date = datetimeString,
            topUrlHits = urlHits
          )

        }
    })


  }


  def getCostliestAccountsReport(

    month_MM: Int,
    day_DD: Int

  ) = {


    srRateLimiterDAO.getCostliestAccountsReportRawData(
      month_MM = month_MM,
      day_DD = day_DD
    ).map(keyRows => {

      keyRows
        .map(keyRow => {

          val monthDate = s"${keyRow.month}${keyRow.dayOfMonth}"

          val accDataWithUrl = keyRow.accountData
            .map(acc => {
              SrRateLimitAccountDataWithUrl(
                accountId = acc.accountId,
                url = keyRow.url,
                hits = acc.hits
              )
            })

          (monthDate, accDataWithUrl)

        })
        .groupBy(_._1)
        .view.mapValues(_.flatMap(_._2))
        .mapValues { accountsForDate =>

          accountsForDate
            .groupBy(_.accountId)
            .map { case (accountId, accountHits) =>

              val accUrlHits = accountHits
                .groupBy(_.url)
                .map { case (url, urlData) =>

                  val totalHitsForUrl = urlData.map(_.hits).sum

                  SrRateLimitUrlData(
                    url = url,
                    hits = totalHitsForUrl
                  )

                }
                .toSeq
                .sortBy(_.hits)
                .reverse

              SrRateLimitAccountDataWithHitsByUrl(
                accountId = accountId,
                hitsByUrl = accUrlHits,
                hits = accUrlHits.map(_.hits).sum

              )


            }
            .toSeq


        }
        .map { case (datetimeString, accWithHitsByUrl) =>

          CostliestAccountsResponse(

            date = datetimeString,

            topAccountHits = accWithHitsByUrl
              .sortBy(_.hits)
              .reverse
          )

        }



    })

  }


  def getAccountsCrossingRateLimit(
    thresholdFor10SecondlyRateLimit: Int = 10,
    month_MM: Int,
    day_DD: Int,
    limitResults: Option[Int] = Some(50)
  ): Try[Seq[SrRateLimitCrossedReportData]] = {

    srRateLimiterDAO.getAccountsCrossingRateLimitThreshold(
      thresholdFor10SecondlyRateLimit = thresholdFor10SecondlyRateLimit,
      month_MM = month_MM,
      day_DD = day_DD
    )
      .map(result => {

        val sorted = result
          .sortBy(_.apiHits)
          .reverse

        if (limitResults.isDefined) sorted.take(limitResults.get)
        else sorted

      })

  }
}

object SrRateLimiter {

  def parse_MMDD_input(

          date_MMDD_input: Option[String]

  ): Option[(Int, Int)]  = {

    // (month, date)
    for {
      date_MMDD <- date_MMDD_input

      _ <- ParseUtils.parseInt(date_MMDD)

      value_MM <- ParseUtils.parseInt(
        date_MMDD.substring(0, 2)
      )

      value_DD <- ParseUtils.parseInt(
        date_MMDD.substring(2, 4)
      )

    } yield {
      (value_MM, value_DD)
    }

  }


}
