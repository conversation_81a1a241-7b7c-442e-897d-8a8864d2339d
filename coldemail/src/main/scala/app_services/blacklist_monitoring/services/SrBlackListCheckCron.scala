package app_services.blacklist_monitoring.services

import org.apache.pekko.actor.ActorSystem
import utils.cronjobs.{SRCronFutureTrait, SRCronTrait}
import api.emails.{BlackListedCustomerDomainDetails, EmailAddressHost, EmailSettingDAO}
import play.api.libs.ws.WSClient
import utils.SRLogger

import scala.util.{Failure, Success, Try}
import scala.concurrent.{Await, ExecutionContext, Future}
import api.AppConfig
import api.emails.services.EmailSettingService
import app_services.blacklist_monitoring.models.{BlacklistCheckResult, BlacklistCheckStatus, BlacklistCheckType}
import io.smartreach.esp.api.emails.EmailSettingId
import org.joda.time.{DateTime, Seconds}
import org.joda.time.format.{DateTimeFormat, DateTimeFormatter}
import utils.email_notification.service.EmailNotificationService

import scala.concurrent.duration.{Duration, SECONDS}

class SrBlackListCheckCron(
                            emailSettingDAO: EmailSettingDAO,
                            emailNotificationService: EmailNotificationService
                          ) extends  SRCronFutureTrait[Boolean] {
  override val cronName: String = "SrBlackListCheckCron"
  override val cronIntervalInSeconds: Int = AppConfig.cronCustomerBlackListEmailDomainIntervalInSeconds

  def executeCron(
                   logger: SRLogger
                 )(
                   implicit system: ActorSystem,
                   ws: WSClient,
                   ec: ExecutionContext
                 ): Future[Boolean] = {

    given Logger: SRLogger= logger
    logger.debug("Inside SrBlackListCheckCron executeCron" )
    val startTime = DateTime.now()

    var lastCheckPoint = DateTime.now()

    val resultF:Future[Boolean] = emailSettingDAO.getEmailDomainNotBlackListChecked match {

       case Failure(err) =>
         logger.fatal(s"An error occurred while fetching domain which is not blacklist checked in last 24 hours :: time taken:  ${Seconds.secondsBetween(lastCheckPoint, DateTime.now()).getSeconds}",err = err)
         lastCheckPoint = DateTime.now()
         Future.successful{false}

       case Success(domainOpt) =>

          domainOpt match {
            case None => {
              logger.info(s"No email domain found for which is not blacklist checked in last 24 hours :: time taken:  ${Seconds.secondsBetween(lastCheckPoint, DateTime.now()).getSeconds}")
              lastCheckPoint = DateTime.now()
              Future.successful(false)
            }


            case Some(domain) =>
              logger.info(s"found some domain using getEmailDomainNotBlackListChecked::${domain.emailAddressHost} :: time taken:  ${Seconds.secondsBetween(lastCheckPoint, DateTime.now()).getSeconds}")
              lastCheckPoint = DateTime.now()

              val res: Future[true] = for {

                blacklistCheckResult: BlacklistCheckResult <- MxToolBoxService.checkBlacklistStatus(domainOrIP = BlacklistCheckType.Domain(domain = domain.emailAddressHost), logger = logger)
                  .map{result =>
                    logger.info(s"checkBlacklistStatus :: time taken:  ${Seconds.secondsBetween(lastCheckPoint, DateTime.now()).getSeconds}")
                    lastCheckPoint = DateTime.now()
                    result
                  }
                  .recover(e => {
                  logger.error(s"Some error occurred in checkBlacklistStatus for domain ${domain.emailAddressHost} :: ${e.getMessage()} :: time taken:  ${Seconds.secondsBetween(lastCheckPoint, DateTime.now()).getSeconds}")
                    lastCheckPoint = DateTime.now()
                  throw e
                })

                _: List[EmailSettingId] <- Future.fromTry(
                  emailSettingDAO.updateBlacklistChecked(
                    emailDomain = domain,
                    blacklistCheckStatus = blacklistCheckResult.status) match {
                    case Success(updatedRows) =>
                      logger.info(s"Success emailSettingDAO.updateBlacklistChecked time taken:  ${Seconds.secondsBetween(lastCheckPoint, DateTime.now()).getSeconds} :: following rows are updated : ${updatedRows.mkString(",")}")
                      lastCheckPoint = DateTime.now()
                      Success(updatedRows)
                    case Failure(e) =>
                      logger.fatal(s"Failed emailSettingDAO.updateBlacklistChecked time taken:  ${Seconds.secondsBetween(lastCheckPoint, DateTime.now()).getSeconds}", e)
                      lastCheckPoint = DateTime.now()
                      Failure(e)
                  })


                blackListedEmailDomain: Option[BlackListedCustomerDomainDetails] <- Future.fromTry(
                  emailSettingDAO.getBlackListedDomainDetailsInLast24Hours(domain = domain) match {
                    case Success(detailsOpt) =>
                      logger.info(s" getBlackListedDomainDetailsInLast24Hours :: time taken:  ${Seconds.secondsBetween(lastCheckPoint, DateTime.now()).getSeconds} :: blacklisted  email domain details is ${detailsOpt.toString}")
                      lastCheckPoint = DateTime.now()
                      Success(detailsOpt)
                    case Failure(e) =>
                      logger.fatal(s"Some error occurred in getBlackListedDomainDetailsInLast24Hours for domain ${domain.emailAddressHost} :: time taken:  ${Seconds.secondsBetween(lastCheckPoint, DateTime.now()).getSeconds}", e)
                      lastCheckPoint = DateTime.now()
                      Failure(e)
                  })

                _: Boolean <- if(blackListedEmailDomain.isDefined) {
                  sendEmailNotification(blackListedEmailDomain = blackListedEmailDomain.get,blacklistCheckResult=blacklistCheckResult)(logger = logger, wsClient = ws, ec = ec).map(sent =>{
                    logger.debug(s"Email delivered successfully for blacklisted  domain with details ${blackListedEmailDomain.get.toString} :: time taken:  ${Seconds.secondsBetween(lastCheckPoint, DateTime.now()).getSeconds}")
                    lastCheckPoint = DateTime.now()
                    sent
                  }).recover(e => {
                    logger.debug(s"Failed to deliver email for blacklisted domain with details ${blackListedEmailDomain.get.toString} ${e.getMessage} :: time taken:  ${Seconds.secondsBetween(lastCheckPoint, DateTime.now()).getSeconds}")
                    lastCheckPoint = DateTime.now()
                    throw e
                  })
                } else {
                  Future.successful(false)
                }

              } yield {
                true
              }

              res.map{ ans =>
                logger.debug(s"SrBlackListCheckCron Finished successfully :: total time taken:  ${Seconds.secondsBetween(startTime, DateTime.now()).getSeconds}")
                ans
              }.recover{ e => {
                logger.fatal(s"rBlackListCheckCron Finished failed :: total time taken:  ${Seconds.secondsBetween(startTime, DateTime.now()).getSeconds}")
                false
              }}
          }}

//    Try{
//      Await.result(resultF,Duration(AppConfig.cronCustomerBlackListEmailDomainIntervalInSeconds,SECONDS))
//    } match {
//      case Success(result) => logger.info(s"Future completed  with result ${result.toString}")
//      case Failure(ex) => logger.fatal("Future didn't complete within the given duration",err = ex)
//    }
    resultF
  }


  private def sendEmailNotification(
                                     blackListedEmailDomain: BlackListedCustomerDomainDetails,
                                     blacklistCheckResult: BlacklistCheckResult
                                   )(implicit wsClient: WSClient,ec:ExecutionContext, logger: SRLogger):Future[Boolean] = {
    val date = {

      val dtf: DateTimeFormatter = DateTimeFormat.forPattern("DD MMM YYYY")

      dtf.print(DateTime.now())

    }

    val subject: String = s"SrBlacklistCheckCron :: $date :: [ACTION REQUIRED] :Customer Email Domains found in Blacklist"

    val ip_blacklist_full_result:String = blackListedEmailDomain.ip_blacklist_status match {
      case BlacklistCheckStatus.PASSED => ""
      case BlacklistCheckStatus.WARNING => ""
      case BlacklistCheckStatus.NOT_FOUND => ""
      case BlacklistCheckStatus.FAILED => s"<br/> ip_blacklist_full_result : ${blackListedEmailDomain.ip_blacklist_full_result}"
    }

    val domain_blacklist_full_result: String = blackListedEmailDomain.domain_blacklist_status match {
      case BlacklistCheckStatus.PASSED => ""
      case BlacklistCheckStatus.WARNING => ""
      case BlacklistCheckStatus.NOT_FOUND => ""
      case BlacklistCheckStatus.FAILED => s"<br/> domain_blacklist_full_result : ${blackListedEmailDomain.domain_blacklist_full_result}"
    }

    val body = {

      val overallResultHtml = s"""<b> Customer Domain Details </b>
                                 |<br/>
                                 |<br/> blackListedEmailDomain : ${blackListedEmailDomain.emailDomain.domain}
                                 |<br/>
                                 |<br/> logInEmail : ${blackListedEmailDomain.logInEmail}
                                 |<br/>
                                 |<br/> org_id : ${blackListedEmailDomain.orgId.id}
                                 |<br/>
                                 |<br/> Customer_domain_blacklist_failure_desc : ${blacklistCheckResult.failureDescription.getOrElse("null")}
                                 |<br/>
                                 |<br/> Customer_domain_blacklist_full_result : ${blacklistCheckResult.fullResult.toString}
                                 |<br/>
                                 |<br/>
                                 |<b> rep mail server Details </b>
                                 |<br/>
                                 |<br/> host : ${blackListedEmailDomain.host}
                                 |<br/>
                                 |<br/> public_ip : ${blackListedEmailDomain.public_ip}
                                 |<br/>
                                 |<br/> tracking_host_url : ${blackListedEmailDomain.host_url}
                                 |<br/>
                                 |<br/> domain_blacklist_failure_description : ${blackListedEmailDomain.domain_blacklist_failure_description}
                                 |<br/>
                                 |<br/> domain_blacklist_status : ${blackListedEmailDomain.domain_blacklist_status.toString}
                                 |<br/>
                                 |<br/> ip_blacklist_failure_description : ${blackListedEmailDomain.ip_blacklist_failure_description}
                                 |<br/>
                                 |<br/> ip_blacklist_status : ${blackListedEmailDomain.ip_blacklist_status.toString}
                                 |<br/>
                                 |<br/> overall_blacklist_status : ${blackListedEmailDomain.overall_blacklist_status.toString}
                                 |<br/>
                                 $ip_blacklist_full_result
                                 |<br/>
                                 $domain_blacklist_full_result""".stripMargin

      s"""
         |Blacklist check results as on $date The following Customer Email Domains are found in blacklist:
         |<br/>
         |<br/>
         |$overallResultHtml
         |""".stripMargin
    }


    emailNotificationService
      .sendMailFromAdminFut(
        toEmail = "<EMAIL>",
        ccEmail = Some("<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>"),
        subject = subject,
        body = body,
        toName = None
      )
      .map(_ => true)

  }


}
