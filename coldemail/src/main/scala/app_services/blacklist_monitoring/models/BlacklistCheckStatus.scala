package app_services.blacklist_monitoring.models

import utils.enum_sr_utils.SREnumJsonUtils

import scala.util.Try


sealed trait BlacklistCheckStatus

object BlacklistCheckStatus
  extends SREnumJsonUtils[BlacklistCheckStatus] {

  override protected val enumName: String = "APIRequestMethod"

  private val KEY_PASSED = "passed"
  private val KEY_WARNING = "warning"
  private val KEY_FAILED = "failed"
  private val KEY_NOT_FOUND = "not found"

  case object PASSED extends BlacklistCheckStatus {

    override def toString: String = KEY_PASSED

  }

  case object WARNING extends BlacklistCheckStatus {

    override def toString: String = KEY_WARNING

  }

  case object FAILED extends BlacklistCheckStatus {

    override def toString: String = KEY_FAILED

  }

  case object NOT_FOUND extends BlacklistCheckStatus {
      override def toString: String = KEY_NOT_FOUND
  }


  override def toKey(value: BlacklistCheckStatus): String = value.toString

  override def fromKey(key: String): Try[BlacklistCheckStatus] = Try {

    key match {

      case KEY_FAILED => FAILED

      case KEY_WARNING => WARNING

      case KEY_PASSED => PASSED

      case KEY_NOT_FOUND => NOT_FOUND

    }

  }
}