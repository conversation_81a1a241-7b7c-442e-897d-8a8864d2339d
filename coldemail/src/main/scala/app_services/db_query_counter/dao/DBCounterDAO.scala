package app_services.db_query_counter.dao

import api.{AppConfig, CacheServiceJedis}
import app_services.sr_rate_limiter.dao.SrRateLimiterDAO.{dayPrefix, extractDateComponentFromKey, get2DigitDateRelatedStringForCacheKey, hourPrefix, minPrefix, monthPrefix}
import app_services.sr_rate_limiter.dao.SrRateLimiterDAO
import org.joda.time.{DateTime, DateTimeZone}
import play.api.libs.json.{Json, OWrites}

import scala.util.Try


case class SrDBQueryRateData(
        queryName: String,
        hits: Double
)

object SrDBQueryRateData {
  implicit val writes: OWrites[SrDBQueryRateData] = Json.writes[SrDBQueryRateData]
}

case class SrDBQueryRateReportData(
        month: String,
        dayOfMonth: String,
        hour: String,
        min: String,
        members: Seq[SrDBQueryRateData]
)


class DBCounterDAO(
  cacheServiceJedis: CacheServiceJedis
) {

  private val envKeyPrefix = AppConfig.redisKeyPrefix
  private val dbRateCachePrefix = "db_qry"


  // We want to count the no of DB calls for different queries
  // 1. we will make the call from the DB DAO to redis
  // 2. we will have a sorted set key for that time interval - 10 minutes
  // 3. the DB fn name - will be passed in as a member key into the sorted set


  //
  private def getCacheMemberForDBQuery(queryName: String): String = {
    //s"$accountIdPrefix$accountId:$orgIdPrefix$orgId"
    s"dbq_$queryName"
  }

  private def getCacheKeyForDBQuery(time: DateTime): String = {
    val tenMinutelyString = SrRateLimiterDAO.genTimeString10MinutelyForCacheKey(time = time)

    s"$envKeyPrefix:$dbRateCachePrefix:$tenMinutelyString"
  }

  def logDBQueryFrequency(

          queryName: String

  ): Try[Double] = {

    val now = DateTime.now().withZone(DateTimeZone.UTC)
    val key = getCacheKeyForDBQuery(now)

    cacheServiceJedis
            .incrementSortedSet(
              key = key,
              score = 1,
              member = getCacheMemberForDBQuery(queryName = queryName)
            )


  }


  def getTopDBQueriesReportRawData(

          thresholdForNumberOfHits: Int = 50,
          month_MM: Int,
          day_DD: Int

  ): Try[Seq[SrDBQueryRateReportData]] ={


    val monthKey = s"$monthPrefix${get2DigitDateRelatedStringForCacheKey(month_MM.toString)}"

    val dateKey = s"$dayPrefix${get2DigitDateRelatedStringForCacheKey(day_DD.toString)}"

    for {
      keys <- cacheServiceJedis.scan(
        keyPattern = s"$envKeyPrefix:$dbRateCachePrefix:$monthKey:$dateKey*"
      )

      rawRows <- cacheServiceJedis
              .getSortedSetMembersAboveScore(
                keys = keys,
                minScore = thresholdForNumberOfHits
              )

    } yield {

      rawRows
              .map(k => {

                val cacheKey = k._1

                val getDateComponent = extractDateComponentFromKey(
                  redisKey = cacheKey
                )

                for {

                  mon <- getDateComponent(monthPrefix)
                  dd <- getDateComponent(dayPrefix)
                  hh <- getDateComponent(hourPrefix)
                  min <- getDateComponent(minPrefix)

                } yield {

                  SrDBQueryRateReportData(

                    month = mon,
                    dayOfMonth = dd,
                    hour = hh,
                    min = min,

                    members = k._2.map(m => {

                      SrDBQueryRateData(
                        queryName = m._1,
                        hits = m._2
                      )

                    })
                  )

                }

              })
              .filter(_.isDefined)
              .map(_.get)

    }


  }






}
