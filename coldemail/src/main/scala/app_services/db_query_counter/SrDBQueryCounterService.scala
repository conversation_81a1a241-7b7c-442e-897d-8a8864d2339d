package app_services.db_query_counter

import app_services.db_query_counter.dao.{DBCounterDAO, SrDBQueryRateData}
import play.api.libs.json.{<PERSON>son, OWrites}
import utils.SrLoggerTrait

import scala.util.{Failure, Success}

case class TopQueryResponse(
        date: String,
        topQueryHits: Seq[SrDBQueryRateData]
)

object TopQueryResponse {
  implicit val writes: OWrites[TopQueryResponse] = Json.writes[TopQueryResponse]
}


class SrDBQueryCounterService(
        dbCounterDAO: DBCounterDAO
) {

  def logDBQueryCallFrequency(

          dbQueryName: String,
          logger: SrLoggerTrait

  ): Boolean = {

    // TODO: remove this logger
    // logger.info(s"logDBQueryCallFrequency init: dbQueryName: $dbQueryName")

    dbCounterDAO.logDBQueryFrequency(
      queryName = dbQueryName
    ) match {

      case Failure(exception) =>

        logger.fatal(s"logDBQueryFrequency $dbQueryName failure", err = exception)

        true

      case Success(latestCount) =>

        // TODO: remove this logger
//        logger.info(s"logDBQueryCallFrequency successfully logger to redis: dbQueryName: $dbQueryName")


        // TODO: rate limiting threshold logic will be here

        val threshold = 25

        if (latestCount > threshold) {
          logger.info(s"logDBQueryFrequency crossed threshold $dbQueryName  > $threshold:: latestCount: $latestCount")
        }

        true

    }


  }

  def getTopDBQueriesReport(

          thresholdForNumberOfHits: Int = 50,
          month_MM: Int,
          day_DD: Int

  ) = {

    dbCounterDAO.getTopDBQueriesReportRawData(
      thresholdForNumberOfHits = thresholdForNumberOfHits,
      month_MM = month_MM,
      day_DD = day_DD
    ).map(keyRows => {

      keyRows
              .map(keyRow => {

                val monthDate = s"${keyRow.month}${keyRow.dayOfMonth}"

                (monthDate, keyRow.members)

              })
              .groupBy(_._1)
              .view.mapValues(_.flatMap(_._2))
              .mapValues(queriesForDate => {

                queriesForDate
                        .groupBy(_.queryName)
                        .map { case (queryName, dailyQueryHits) =>

                          SrDBQueryRateData(
                            queryName = queryName,
                            hits = dailyQueryHits.map(_.hits).sum
                          )

                        }
                        .toSeq
                        .sortBy(_.hits)
                        .reverse


              })
              .map { case (datetimeString, queryHits) =>

                TopQueryResponse(
                  date = datetimeString,
                  topQueryHits = queryHits
                )

              }
    })

  }

}
