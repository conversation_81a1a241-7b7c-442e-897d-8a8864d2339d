import api.sr_audit_logs.models.EventDataType.PushEventDataType.UpdatedProspectsEventData
import io.smartreach.esp.api.emails.IEmailAddress
import org.joda.time.{DateTime, DateTimeZone, Duration}
import play.api.libs.json.Json
import utils.SRLogger
import utils.mq.webhook.model.TriggerSource

import java.util

DateTime
  .now()
  .withZone(DateTimeZone.forID("Asia/Kolkata"))
//  .withTimeAtStartOfDay()
  .getSecondOfDay()

DateTime
  .now()
  .withZone(DateTimeZone.forID("America/New_York"))
  //  .withTimeAtStartOfDay()
  .getSecondOfDay()


DateTime
  .now()
  .withZone(DateTimeZone.forID("Asia/Kolkata"))
  .minusDays(3)
  .getDayOfWeek()



DateTime.parse("2022-08-14")
  .withZone(DateTimeZone.forID("Asia/Kolkata"))
  .getDayOfWeek() % 7

DateTime.parse("2022-08-13")
  .withZone(DateTimeZone.forID("Asia/Kolkata"))
  .getDayOfWeek()


DateTime.parse("2022-08-20")
  .withZone(DateTimeZone.forID("Asia/Kolkata"))
  .getDayOfWeek()


7 % 7

val from: Long = 1683604800000L

val till: Long = 1686369599999L
val fromDateTime = if (from < 9999999999L) {
  new DateTime(from * 1000)
} else {
  new DateTime(from)
}
val tillDateTime = if (from < 9999999999L) {
  new DateTime(till * 1000)
} else {
  new DateTime(till)
}
val durationInDays = new Duration(fromDateTime, tillDateTime).getStandardDays


val string = "this is a string with no underscore"


val testWithNone = UpdatedProspectsEventData(
  updated_id = 123,
  ownerAccountId = 123,
  teamId = 123,
  triggerPath = None
)


val testWithSome = UpdatedProspectsEventData(
  updated_id = 123,
  ownerAccountId = 123,
  teamId = 123,
  triggerPath = Some(TriggerSource.CRM)
)

val testWithNoneJSString = Json.toJson(testWithNone)

val testWithSomeJSString = Json.toJson(testWithSome)

val gettingFromJsNone = testWithNoneJSString.validate[UpdatedProspectsEventData]

val gettingFromJsSome = testWithSomeJSString.validate[UpdatedProspectsEventData]

val Logger = new SRLogger("Test")

val list = new util.ArrayList[String]

list.add("string 122")
list.add("string 2")



println(s"list - $list")