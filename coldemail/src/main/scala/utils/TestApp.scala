package utils

import play.api.Logging
import api.AppConfig
import api.accounts.dao.TeamsDAO
import api.accounts.{AccountUuid, RecursiveMigrationMethodError, TeamId}
import api.accounts.models.{AccountId, AccountStatus, AccountV2, OrgId}
import api.billing.PaymentGateway
import api.campaigns.services.CampaignId
import api.gpt.services.GptApiService
import api.gpt.{GPTApi, GPTService}
import api.internal_support.service.DeleteAccountError
import api.llm.dao.LlmAuditLogDAO
import api.llm.models.LlmFlow.MagicColumnFlow
import api.pipelines.dao.{OpportunityData, OpportunityStatusCreateData, OpportunityStatusUpdateData, PipelineData, ReorderOpportunityData, ReorderOpportunityStatusData}
import api.pipelines.models.{OpportunityPosRank, OpportunityStatusInternalId, OpportunityStatusPosRank, OpportunityStatusUUID, OpportunityType, PipelineInternalId, PipelineUUID, StatusType}
import api.pipelines.services.OpportunitiesDefaultSetupData
import api.prospects.{InferredQueryTimeline, ProspectBounceResponseInfo, ProspectUuid}
import api.prospects.dao.ProspectDAO
import api.prospects.models.{ProspectEmailId, ProspectId}
import api.pipelines.models.OpportunityUUID
import api.reports.models.InternalAudienceReportType
import api.spammonitor.service.{ApiLayerAPIService, RapidAPIService}
import api.sr_ai.models.OpenAiModel
import api.tasks.models.{ChangeStatusPermissionCheck, NewTask, SearchTask, TaskCreatedVia, TaskData, TaskPriority, TaskStatus, TaskType, TimeBasedTaskType, UpdateTask}
import api.tasks.pgDao.ValidatedTaskReq
import api.tasks.services.GetAllTaskForUserError
import api.team_inbox.service.TeamInboxService
import api.triggers.{Trigger, TriggerFields}
import api_layer_models.CurrencyType
import eventframework.ProspectObject
import io.smartreach.companies_api.api.enrich_company_info.{CompaniesServiceDI, TheCompaniesApiDI}
import io.smartreach.esp.api.emails.{AccessToken, EmailSettingId, InternetMessageId}
import io.smartreach.esp.api.microsoftOAuth.EmailSettingUpdateAccessToken
import io.smartreach.esp.utils.email.{EmailReplyBounceType, OutlookReceiveEmailService, OutlookUtilsApi}
import io.smartreach.sr_dns_utils.SrDNSUtil
import org.apache.pekko.actor.ActorSystem
import org.apache.pekko.stream.Materializer
import org.joda.time.{DateTime, DateTimeZone}
import play.api.libs.json.Json
import play.api.libs.ws.ahc.AhcWSClient
import scalikejdbc.*
import scalikejdbc.config.DBs
import scalikejdbc.scalikejdbcSQLInterpolationImplicitDef
import sr_scheduler.models.ChannelType
import utils.affiliate.{FirstPromoterReferrerAccountDetails, OrgOwnerDetails, ReferralCouponDetails, ReferralCouponStatus}
import utils.aws.S3
import utils.customersupport.InternalGSTUtil
import utils.customersupport.dao.CustomerAcquisitionSRData
import utils.customersupport.services.{AcquisitionFieldData, CustomerAcquisitionDataCombined, InternalAdoptionReport}
import utils.dbutils.DBUtils
import utils.dependencyinjectionutils.{AccountDAOV2_DI, AccountDAO_DI, AffiliateDAO_DI, CacheServiceJedisDI, CampaignDAO_DI, DBCounterDAO_DI, DBUtils_DI, EmailSettingDAO_DI, InternalCSDReportDAODI, MqOpportunitiesDefaultSetupMigration_DI, OpportunitiesDefaultSetupService_DI, OpportunityDAO_DI, OpportunityStatusDAO_DI, OrganizationBillingDAO_DI, OrganizationDAO_DI, PipelineDAO_DI, SrDBQueryCounterService_DI, SrInternalFeatureUsageDaoDI, SrUuidUtilsDI, TeamsPipelineConfigDAO_DI, TeamsPipelineConfigService_DI}
import utils.dependencyinjectionutils.Tasks.TaskDAO_DI
import utils.emailvalidation.EmailValidationModel
import utils.emailvalidation.models.{EmailValidationPriority, IdsOrEmailsForValidation}
import utils.helpers.LogHelpers
import utils.intercom.dao.InternalSRUsageData
import utils.mq.webhook.mq_activity_trigger.MQActivityTriggerService
import utils.proxy.brightdata.BrightDataApi
import utils.security.EncryptionHelpers
import utils.testapp.{TestAppTrait, Test_TaskPgDAO}
import utils.timezones.TimezoneData

import scala.concurrent.{Await, ExecutionContext, Future}
import scala.concurrent.duration.*
import scala.util.{Failure, Success, Try}


/*
This object holds any sort internal test function used to test features/changes manually during development.
 */
object TestApp extends Logging {

  def main(args: Array[String]): Unit = {

    if (args.length > 0) {
      DBs.setupAll()


      //      implicit lazy val system = ActorSystem()
      //      implicit lazy val materializer = ActorMaterializer()
      //      implicit lazy val wSClient: AhcWSClient = AhcWSClient()
      //      implicit lazy val actorContext: ExecutionContext = system.dispatcher


      val applicationName = args(0)

      applicationName match {

        case "jodaparse" =>

          println(s"date: ${AppConfig.RollingUpdates.prospectsTouchedCountFromMetadataOnlyIfBillingCycleStartedAfter}")


        case "startDate" =>
          val dateTime = new DateTime(1711440000)
          println(dateTime)
          println(dateTime.toDate)

        case "getListOfBlacklistedIPs" =>

          implicit val system: ActorSystem = ActorSystem()
          implicit val wSClient: AhcWSClient = AhcWSClient()
          implicit val actorContext: ExecutionContext = system.dispatcher
          given logger: SRLogger = new SRLogger("TestApp : removeIPFromBlacklist ")

          val zone = "isp"

          val result = new BrightDataApi().getBlacklistedIPsForZone(zone = zone)
            .map(res => {
              println(s"IPs -> $res")
            })
            .recover {
              case e =>
                logger.error("Error occured", e)
            }

          Await.result(result, 30000.millis)


        case "mxcheck" =>
          val srDnsUtil = new SrDNSUtil()

          implicit val system = ActorSystem()
          implicit val materializer = Materializer(system)
          implicit val wSClient: AhcWSClient = AhcWSClient()
          implicit val actorContext: ExecutionContext = system.dispatcher
          given logger: SRLogger = new SRLogger("mxcheck")

          val result = srDnsUtil
            .checkMxType(
              domain = "heaplabs.com"
            )
            .map(res => logger.error(s"dns found: $res"))
            .recover { case e => logger.error(s"dns fail: $e") }

          Await.result(result, Duration.Inf)


        case "getPath" =>
          implicit val system = ActorSystem()
          implicit val materializer = Materializer(system)
          implicit val wSClient: AhcWSClient = AhcWSClient()
          implicit val actorContext: ExecutionContext = system.dispatcher

          val res = CommandExecutor.runCommandInBash("pwd").map(_.output)
          println(Await.result(res, 60000.millis).trim())

    






   
    

   


        case "dateTimeParseTest" =>
          //2022-03-21T11:58:03.294Z"
          val newDate: DateTime = DateTime.parse("2023-02-27T14:27:00.94Z")

          println(newDate)

   
        case "future_demo" =>

          implicit val ec = Helpers.genFixedThreadPoolEC(threads = 10)

          def blockingDBQuery(teamId: Int) = {
            Thread.sleep(5000)
            println(s"blockingDBQuery called teamId: $teamId")
          }

          def blockingDbQueryRunningInFuture(
                                              teamId: Int
                                            )(
                                              implicit ec: ExecutionContext
                                            ) = Future {
            blockingDBQuery(teamId = teamId)
          }(
            executor = ec
          )

          Future
            .sequence(
              (1 to 20).map { teamId =>
                blockingDbQueryRunningInFuture(
                  teamId = teamId
                )(ec)
              }
            )
            .map(_ => {
              println("\n\nall done")
            })
            .recover { case e =>

              println(s"ERROR: $e")
            }





        /*case "test_ApiLayerWhois" =>
         // val string1 = "{\"domain_name\":\"smartreach.io\",\"domain__id\":\"c0ca066236f6412f97f4b026293e9230-DONUTS\",\"registrar\":\"IONOS SE\",\"registrar_id\":\"83\",\"registrar_url\":\"https://www.ionos.com\",\"status\":\"ok https://icann.org/epp#ok\",\"registrant_name\":\"HVIF Solutions Pvt. Ltd.\",\"registrant_state_province\":\"AP\",\"registrant_country\":\"IN\",\"name_servers\":[\"ns-541.awsdns-03.net\",\"ns-1160.awsdns-17.org\",\"ns-15.awsdns-01.com\",\"ns-1637.awsdns-12.co.uk\"],\"creation_date\":\"2017-04-10 07:39:05\",\"expiration_date\":\"2023-04-10 07:39:05\",\"updated_date\":\"2022-05-25 07:39:06\"}"

          val string1 = "{\"domain_name\":\"smartreach.io\",\"registrar\":\"IONOS SE\",\"creation_date\":\"2017-04-10 07:39:05\",\"expiration_date\":\"2023-04-10 07:39:05\",\"updated_date\":\"2022-05-25 07:39:06\"}"
          val string2 = "{\"domain_name\":\"APILAYER.COM\",\"registrar\":\"GoDaddy.com, LLC\",\"whois_server\":\"whois.godaddy.com\",\"referral_url\":null,\"updated_date\":\"2021-12-12 10:45:14\",\"creation_date\":\"2012-12-11 19:43:57\",\"expiration_date\":\"2023-12-11 19:43:57\",\"name_servers\":[\"ABBY.NS.CLOUDFLARE.COM\",\"RAM.NS.CLOUDFLARE.COM\"],\"status\":[\"clientDeleteProhibited https://icann.org/epp#clientDeleteProhibited\",\"clientRenewProhibited https://icann.org/epp#clientRenewProhibited\",\"clientTransferProhibited https://icann.org/epp#clientTransferProhibited\",\"clientUpdateProhibited https://icann.org/epp#clientUpdateProhibited\"],\"emails\":\"<EMAIL>\",\"dnssec\":\"unsigned\",\"name\":null,\"org\":null,\"address\":null,\"city\":null,\"state\":null,\"zipcode\":null,\"country\":null}"

//          val json1 = Json.parse(string1)
//
//          val apiResult1 = json1.as[ApiLayerResponse]
//
//          val a1: DateTime = apiResult1.creation_date
//
//          logger.info(s"apiResult ------- $apiResult1")
//          logger.info(s"creation_date as millies------- ${a1.getMillis}")

          val json2 = Json.parse(string2)

          val apiResult2 = json2.as[ApiLayerResponse]

          val a2: DateTime = apiResult2.creation_date

          logger.info(s"apiResult ------- $apiResult2")
          logger.info(s"creation_date as millies------- ${a2.getMillis}")*/

        //          val domainDataService = new DomainDataService()
        //          given Logger = new SRLogger("test_ApiLayerWhois")
        //
        //          val result = domainDataService.getDomainAge("apilayer.com")
        //
        //          result.map{result =>
        //            logger.info(s"result ------------ $result")
        //          }

        case "test_OpenTrackingOnClickTracking" =>
          given logger: SRLogger = new SRLogger("testing open")
          val out = Test_TaskPgDAO.emailScheduledDAO.isClicked(28130, "https:///smartreach.io", DateTime.now(), None, logger)
          println(s"hello $out")
/*

        case "gu/adding_lead_industry_metadata" =>
          val out = leadFinderMetaDataDAO.addIndustryMetadata()
          println(s"$out")

        case "gu/adding_lead_company_type_metadata" =>
          val out = leadFinderMetaDataDAO.addCompanyTypeMetadata()
          println(s"$out")
*/


 

        case "guru/test_ApiLayerWhois_v2" =>

          given logger: SRLogger = new SRLogger("testing connection")

          implicit val  system: ActorSystem = ActorSystem()
          implicit val  materializer = Materializer(system)
          implicit val  wSClient: AhcWSClient = AhcWSClient()
          implicit val  actorContext: ExecutionContext = system.dispatcher

          val data = new ApiLayerAPIService().getDomainDetails("jwsuns.com")
          //logger.warn(Await.result(data, scala.concurrent.duration.Duration.Inf).statusText)

          logger.warn(Await.result(data, scala.concurrent.duration.Duration.Inf).toString)

        case "guru/test_RapidAPIWhois_v2" =>

          given logger: SRLogger = new SRLogger("testing connection")

          implicit val  system: ActorSystem = ActorSystem()
          implicit val  materializer = Materializer(system)
          implicit val  wSClient: AhcWSClient = AhcWSClient()
          implicit val  actorContext: ExecutionContext = system.dispatcher

          val data = new RapidAPIService().getDomainDetails("smartreach.io")
          //logger.warn(Await.result(data, scala.concurrent.duration.Duration.Inf).statusText)

          logger.warn(Await.result(data, scala.concurrent.duration.Duration.Inf).toString)


        case "find" =>

        //          val prospectAddEventDAO = new  ProspectAddEventDAO


        //          val logger = new SRLogger("find()")

        //          val res = prospectDAO.find(byProspectIds = Seq(4418245, 4418244, 4418243),
        //            byProspectEmails = Seq("<EMAIL>", "<EMAIL>", "<EMAIL>"), teamId = 47L, Logger = logger)


        case "json_key_value" =>

          val keyValueList = Json.obj(
            "first" -> Json.obj(
              "vid" -> 503,
              "canonical-vid" -> 501,
              "portal-id" -> 23971549,
              "is-contact" -> true,
            ),
            "second" -> Json.obj(
              "vid" -> 501,
              "canonical-vid" -> 501,
              "portal-id" -> 23971549,
              "is-contact" -> true,
            )
          )

          val values = keyValueList.values

          logger.info(s"$values")


     



      
        case "st_gst" =>

          implicit val  system: ActorSystem = ActorSystem()
          implicit val  materializer = Materializer(system)
          implicit val  wSClient: AhcWSClient = AhcWSClient()
          implicit val  actorContext: ExecutionContext = system.dispatcher

          val appendMonthYear = "sep21"

          InternalGSTUtil
            .generateStripeGST(
              inr = true,
              appendMonthYear = appendMonthYear
            )
            .map { _ =>
              logger.info("\n\n\n\n----------\ndone\n---------\n\n\n\n\n\n")
            }
            .recover { case e =>
              logger.info(s"\n\n\n\n----------\nERROR: $e\n---------\n\n\n\n\n\n")

            }


        case "test_s3" =>
          logger.info("HELLO WTF")

          S3.genPresignedUrl(filename = "Hello") match {
            case Failure(e) =>
              logger.error(s"$e")

            case Success(data) =>
              logger.info(s"wow: $data")

          }






    

        case "nxdDBWrite" => {
          logger.info("nxdDBWriteTest")

        }

        /*case "handlePhantomBusterWebhook" =>

          implicit lazy val system = ActorSystem()
          implicit lazy val materializer = Materializer(system)
          implicit lazy val wSClient: AhcWSClient = AhcWSClient()
          implicit lazy val actorContext: ExecutionContext = system.dispatcher
          implicit lazy val logger: SRLogger = new SRLogger("TestApp.handlePhantomBusterWebhook :: ")

          object HandlePhantomBusterWebhook extends PhantomBusterServiceDI
          with Team.TeamServiceDI
          with TaskDaoServiceDI
          with PhantomBusterAgentServiceDI
          with CloudStorageDI
          with ProspectDAOService_DI
          with LinkedinSettingServiceDI
          with LinkedinMessagesServiceDI
          with LinkedinMessageThreadsServiceDI
          with CampaignServiceDI
          with SrUuidUtilsDI
          with AccountServiceDI
          with CampaignCacheServiceDI
          with CampaignDAO_DI
          with CampaignProspectDAO_DI
          with CampaignSendReportsDAO_DI
          with CampaignStepVariantDAO_DI
          with ChannelSettingServiceDI
          with DBUtils_DI
          with EmailScheduledDAO_DI
          with EmailSendingStatusDAO_DI
          with EmailSettingDAO_DI
          with EmailSettingJedisServiceDI
          with ReplySentimentDAOService_DI
          with ResetUserCacheUtilDI
          with SrUserFeatureUsageEventServiceDI
          with TaskServiceDI
          with TriggerDAO_DI
          with LinkedinMessageThreadsDAODI
          with LinkedinMessagesDAODI
          with LinkedinSettingDAO_DI
          with PhantomBusterAgentsDAODI
          with EmailThreadDAO_DI
          with MergeTagService_DI
          with ProspectColumnDef_DI
          with ProspectDAO_DI
          with ProspectQueryDI
          with AccountDAO_DI
          with ProspectAddEventDAO_DI
          with SrEventServiceDI
          with TaskCacheDAO_DI
          with Tasks.TaskDAO_DI
          with SRFeatureFlagsServiceDI
          with TeamsDAO_DI
          with AccessTokenService_DI
          with OrganizationDAO_DI
          with SupportAccessToUserAccountDAO_DI
          with UserRedisKeyService_DI
          with CampaignStepDAO_DI
          with PhishingCheckServiceDI
          with ProspectServiceV2_DI
          with CacheServiceJedisDI
          with CampaignProspectDAO_2DI
          with ProspectColumnDefDAO_DI
          with ReplySentimentDAO_DI
          with ReplySentimentJedisDAO_DI
          with CacheServiceDI
          with OrgMetadataDAO_DI
          with KafkaServiceDI
          with SrInternalFeatureUsageDaoDI
          with TaskCacheService_DI
          with SrDBQueryCounterService_DI
          with CallDAO_DI
          with ReplySentimentService_DI
          with ClientAccountAccessLogDAO_DI
          with EmailValidationModelDI
          with ProspectsEmailsDAO_DI
          with ActivityTriggerMQServiceDI
          with DBCounterDAO_DI
          with ScyllaRunSafely_DI
          with EventLogService_DI
          with ScyllaCluster_DI
          with EventLogDAO_DI
          with MqHandleEventLogDI
          with ScyllaDbConnection_DI
          with EventLogFindServiceDI
          with HandleActivityTriggerEventServiceDI
          with HandlePushTriggerEventServiceDI
          with WorkFlowAttemptService_DI
          with CampaignProspectServiceDI
          with TagServiceDI
          with EmailNotificationServiceDI
          with LeadStatusServiceDI
          with SRTriggerAllowedCombos_DI
          with TIntegrationCRMService_DI
          with WorkflowAttemptDAO_DI
          with CampaignProspectTimezonesJedisServiceDI
          with MailgunServiceDI
          with IntegrationTypeService_DI
          with HubSpotOAuth_DI
          with PipedriveOAuth_DI
          with SalesforceOAuth_DI
          with ZohoOAuth_DI
          with ZohoRecruitOAuth_DI
          with TriggerDAOService_DI
          with TriggerServiceV2_DI
          with TriggerJedisDAO_DI
          with PhantomBusterApiDI {

            val phantomBusterWebhookResult = PhantomBusterWebhookResult(
              agentId = PhantomBusterAgentId("2606942874040720"),
              agentName = "SmartReach Linkedin Profile Scraper.js",
              containerId = PhantomBusterContainerId("7063959221403815"),
              script = PhantomBusterScript.LinkedinViewProfile,
              scriptOrg = "PhantomBuster",
              branch = "master",
              launchDuration = 101,
              runDuration = 201,
              resultObject = Some("{\"agentId\": \"124344543\"}"),
              exitMessage = PhantombusterWebhookExitMessage.Finished,
              exitCode = 0
            )

            def init() = {
              phantomBusterService.updateStatusOfRunningLinkedinTasks(
                phantomBusterWebhookResult = phantomBusterWebhookResult,
                teamId = TeamId(12L)
              )
            }

          }

          Await.result(HandlePhantomBusterWebhook.init()
            .map(count => logger.info(s"Updated $count task statuses"))
            .recover{
              case e => logger.error(e.getMessage, e)
            }, scala.concurrent.duration.Duration.Inf)*/


        case "parseStringToJson" =>
          given logger: SRLogger = new SRLogger("TestApp.parseStringToJson ::")

          logger.info(s"${Json.parse("{\"agentId\": \"124344543\"}")}")

    
    



        case "UpdateTaskForUserInDB" =>

          implicit val ec = Helpers.genFixedThreadPoolEC(threads = 10)
          val logger = new SRLogger("TestApp CreateNewTask")

          val newTask = NewTask(
            campaign_id = Some(1),
            campaign_name = Some("Campaign"),
            step_id = Some(1),
            step_label = Some("Day 1: Opening"),
            created_via = TaskCreatedVia.Manual,
            is_opening_step = Some(true),
            task_type = TaskType.SendLinkedinInMail,
            is_auto_task = false,
            task_data = TaskData.SendLinkedinInMailData(
              subject = None,
              body = "hi there "
            ),
            status = TaskStatus.Due(
              due_at = DateTime.now()
            ),
            assignee_id = Some(11),
            prospect_id = Some(4),
            priority = TaskPriority.Critical,
            note = None,
          )

          val updateTask = UpdateTask(
            task_data = newTask.task_data,
            status = newTask.status,
            assignee_id = newTask.assignee_id,
            prospect_id = newTask.prospect_id,
            priority = newTask.priority,
            note = newTask.note
          )

          object main extends TaskDAO_DI
            with SrDBQueryCounterService_DI
            with DBCounterDAO_DI
            with CacheServiceJedisDI {
            def init() = {
              taskDAO.updateTask(
                update_task_data = updateTask,
                task_id = "task_NJJQaBB0HoJwaaV2ylXdkPfYjxHV50NHSacbzXqi",
                team_id = 9,
                permittedAccountIds = Seq(11)
              )
            }
          }

          Await.result(
            main.init().map(res => println(s"\n\n\ntask updated successfull successfully $res"))
              .recover {
                case e =>
                  logger.error("\n\n\n [worker] error while updating Task: " + LogHelpers.getStackTraceAsString(e))
              }
            ,
            20.seconds
          )

        /*
        // sbt "coldemail/runMain utils.TestApp generateResponseThroughGPT"
        case "generateResponseThroughGPT" =>
          implicit lazy val system: ActorSystem = ActorSystem()
          implicit lazy val materializer: Materializer = Materializer(system)
          implicit lazy val ws: WSClient = AhcWSClient()
          implicit lazy val ec: ExecutionContext = system.dispatcher
          implicit lazy val logger: SRLogger = new SRLogger(logRequestId = "TestApp generateResponseThroughGPT ::")

          val gptApi = new GPTApi()

          val userPrompt: String = scala.io.StdIn.readLine("Enter prompt: ")

          val responseText = gptApi.generateCompletionsForPrompt(
            prompt = userPrompt,
            userId = OrgId(3L)
          )

          Await.result(responseText, scala.concurrent.duration.Duration.Inf)

          println(s"responseText: $responseText")
          */


        /*
        // sbt "coldemail/runMain utils.TestApp generateCompletionsForChat"
        case "generateCompletionsForChat" =>

          implicit val  system: ActorSystem = ActorSystem()
          implicit val  materializer: Materializer = Materializer(system)
          implicit val ws: AhcWSClient = AhcWSClient()
          implicit val  ec: ExecutionContext = system.dispatcher
          implicit val  logger: SRLogger = new SRLogger(logRequestId = "TestApp generateResponseThroughGPT ::")

          val gptApi = new GPTApi()
          
          val llmAuditLogDAO = new LlmAuditLogDAO()
          
          val gptApiService = new GptApiService(
            gptApi = gptApi,
            llmAuditLogDAO = llmAuditLogDAO,
          )

          val chat = List(
            Map("role" -> "user", "content" -> "Who won the Cricket World Cup in 1983?"),
            Map("role" -> "assistant", "content" -> "India won the Cricket World Cup in 1983"),
            Map("role" -> "user", "content" -> "Where was it played?"),
          )

          val responseText = gptApiService.generateCompletionsForChat(
            chat = chat,
            userId = OrgId(3L),
            flow = MagicColumnFlow
          )

          Await.result(responseText, scala.concurrent.duration.Duration.Inf)

          println(s"responseText: $responseText")

         */

        /*

                // sbt "coldemail/runMain utils.TestApp textCategorizationUsingGPT"
                case "textCategorizationUsingGPT" =>

                  implicit lazy val system: ActorSystem = ActorSystem()
                  implicit lazy val materializer: Materializer = Materializer(system)
                  implicit lazy val ws: WSClient = AhcWSClient()
                  implicit lazy val ec: ExecutionContext = system.dispatcher
                  implicit lazy val logger: SRLogger = new SRLogger(logRequestId = "TestApp generateResponseThroughGPT ::")

                  object TextCategorization extends GPTServiceDI
                    with GPTApiDI
                    with SrRateLimiterDI
                    with SrRateLimiterDAO_DI
                    with CacheServiceJedisDI {
                      def init(): Future[List[UnderReviewReason]] = {
                        val text = scala.io.StdIn.readLine("Enter email body: ")

                        gptService.categorizeEmail(
                          email_body = text,
                          step_label = "Day 1: Opening"
                        )
                          .recover {
                            case e =>
                              println(s"\n\n\n Error in Future e ->${LogHelpers.getStackTraceAsString(e)}  \n\n\n\n")
                              List()
                          }
                      }


                    }

                    val categories = TextCategorization.init()
                    Await.result(categories, scala.concurrent.duration.Duration.Inf)
                    println(s"categories: $categories")

        */


        case "GetTaskFromPg" =>

          implicit val ec = Helpers.genFixedThreadPoolEC(threads = 10)
          val logger = new SRLogger("TestApp GetTaskFromPg")

          val newTask = NewTask(
            campaign_id = Some(1),
            campaign_name = Some("Campaign"),
            step_id = Some(1),
            step_label = Some("Day 1: Opening"),
            created_via = TaskCreatedVia.Manual,
            is_opening_step = Some(true),
            task_type = TaskType.SendLinkedinInMail,
            is_auto_task = false,
            task_data = TaskData.SendLinkedinInMailData(
              subject = None,
              body = "hi there "
            ),
            status = TaskStatus.Due(
              due_at = DateTime.now()
            ),
            assignee_id = Some(11),
            prospect_id = Some(4),
            priority = TaskPriority.Critical,
            note = None,
          )

          //          val updateTask = UpdateTask(
          //            task_data = newTask.task_data,
          //            status = newTask.status,
          //            assignee_id = newTask.assignee_id,
          //            prospect_id = newTask.prospect_id,
          //            priority = newTask.priority,
          //            note = newTask.note
          //          )

          object main extends TaskDAO_DI
            with SrDBQueryCounterService_DI
            with DBCounterDAO_DI
            with CacheServiceJedisDI {
            def init() = {
              taskDAO.findTaskbyTaskID(
                taskId = "task_NJJQaBB0HoJwaaV2ylXdkPfYjxHV50NHSacbzXqi",
                teamId = 9,
                changeStatusPermissionCheck = ChangeStatusPermissionCheck.ManualTaskCheck(
                  doer = None,
                  permittedAccountIds = Seq(11L)
                ),
                orgId = OrgId(9L),
//                emailNotCompulsoryEnabled = false

              )
            }
          }

          Await.result(
            main.init().map(res => println(s"\n\n\ntask updated successfull successfully $res"))
              .recover {
                case e =>
                  logger.error("\n\n\n [worker] error while updating Task: " + LogHelpers.getStackTraceAsString(e))
              }
            ,
            20.seconds
          )
        //          object main extends TaskCacheDAO_DI
        //          with TaskCacheService_DI
        //          with TaskCacheDatabase_DI
        //          with ScyllaDbConnection_DI
        //          with TaskDAO_DI
        //          {
        //            def init() = {
        //
        //              given logger: SRLogger = new SRLogger("testing connection")
        //              logger.info("inside init")
        //              taskCacheDAO.createNewTaskInCache(
        //                created_task = taskDAO.tasks.head
        //              )
        //                .map(task => {
        //
        //                  println(s"\n\n\n\n task created ${task} \n\n\n\n")
        //                  task
        //                }
        //                ).recover{
        //                case e => println("\n\n\n error while creating task \n\n\n\n")
        //              }
        //            }
        //
        //            override val cluster: Cluster = scyllaDbConnection.initialize()
        //          }
        //
        //          main.init()

        case "GetTaskForUserPagination" =>


          object main extends TestAppTrait {
            def init() = {

              //              val scyllaRunSafely: ScyllaRunSafely =
              //              override val cluster: Cluster = scyllaDbConnection.initialize()
              given logger: SRLogger = new SRLogger("testing connection")
              logger.info("inside init")
              taskService.getAllTasksForUser(
                  isFirst = false,
                  team_id = 9,
                  orgId = OrgId(9L), // Dummy Org
                  doNotFetchAutomatedDueTasks = false,
                  validatedTaskReq = ValidatedTaskReq.ValidatedTaskReqRange(
                    timeline = InferredQueryTimeline.Range.Before(DateTime.now()),
                    pageSize = 500
                  ),
                  searchTask = SearchTask(
                    assignee_ids = None,

                    reply_sentiment_uuids = None,

                    task_status = None,

                    task_priority = None,
                    campaign_ids = None,

                    task_types = None,

                    time_based_task_type = Some(TimeBasedTaskType.Today)

                  ),
                  timeZone = "UTC",
                  permittedAccountIds = Seq(11L)
                )
                .map(task => task match {

                  case Left(GetAllTaskForUserError.ServerError(e)) =>
                    logger.debug(s"\n\nServer Error : e: $e")


                  case Left(GetAllTaskForUserError.ErrorWhileGettingTasks) =>
                    logger.debug("\n\nError While fetching task\n\n")

                  case Right(taskPagination) =>
                    logger.info(s"\n\ntaskPagination -> $taskPagination\n\n")

                }
                ).recover {
                  case e => println(s"\n\n\n Error in Future e ->${LogHelpers.getStackTraceAsString(e)}  \n\n\n\n")
                }
            }

          }

          Await.result(main.init(), 300.seconds)


        //println(s"\n\n\n task found ${task} \n\n\n")

        //        case "UpdateTTLTimeForTaskCache" =>
        //
        //
        //          object main extends TestAppTrait {
        //            def init() = {
        //
        //              given logger: SRLogger = new SRLogger("testing connection")
        //              logger.info("inside init")
        //              val result = for {
        //                truncateTable <- taskCacheDAO.truncateTaskCacheTable()
        //
        //                updateTTLValue <- taskCacheDAO.updateTtlValueOfTasks(AppConfig.ttl_time_in_seconds_for_cache)
        //              } yield {
        //                updateTTLValue
        //              }
        //              result
        //            }
        //
        //          }
        //
        //          val magicNumber = Await.result(main.init(), 300.seconds)
        //
        //          logger.info(s"result -> $magicNumber")

        //        case "getTaskByTaskId" =>
        //          implicit val ec = Helpers.genFixedThreadPoolEC(threads = 10)
        //
        //          given logger: SRLogger = new SRLogger("testing connection")
        //          object main extends Tasks.TaskCacheService_DI
        //            with ScyllaDbConnection_DI
        //            with ScyllaRunSafely_DI
        //            with Tasks.TaskDAO_DI
        //            with SrDBQueryCounterService_DI
        //            with DBCounterDAO_DI
        //            with CacheServiceJedisDI {
        //            def init() = {
        //              logger.info("inside init")
        //              taskCacheService.getByTaskId(
        //                task_id = "randomString2"
        //              )
        //              //              .map(
        //              //                result => logger.info("\n\n inserted data successfully")
        //              //              )
        //              //                .recover {
        //              //                  case e => logger.debug(s"\n\n failed while inserting data ${e.getMessage}")
        //              //                }
        //            }
        //            override val scyllaCluster: Cluster = scyllaDbConnection.initialize()
        //          }
        //          Await.result(
        //            main.init().map{
        //              case Some(data) => println(s"found data -> ${data}")
        //              case None => println("no Task found")
        //            },
        //            20.seconds
        //          )

        // sbt "coldemail/runMain utils.TestApp fetchTasksFromScylla"
        //        case "fetchBatchTasksFromScyllaAndPg" =>
        //          implicit val ec = Helpers.genFixedThreadPoolEC(threads = 10)
        //
        //          val task_ids = List("task_ZNGIrY1vncuIp3Ku4bTVecmNgi744O8End5mlQUi","task_yQ0FmmFym5Y6evTMwTRQxVYqqEuPXBCJMoL86axl",
        //          "task_UqYUmsEMU929TUDdC70sS8jd7YlyfdcUClSji4QQ")
        //
        //          given logger: SRLogger = new SRLogger("testing connection")
        //          // taskCacheDAO: TaskCacheDAO in utils.dependencyinjectionutils.Tasks.TaskDaoServiceDI is not defined; member srEventService: SrEventService in utils.dependencyinjectionutils.Tasks.TaskDaoServiceDI is not defined; member taskDAO: TaskPgDAO in utils.dependencyinjectionutils.Tasks.TaskDaoServiceDI is not defined
        //          object main extends ProspectDAO_DI
        //            with ProspectAddEventDAO_DI
        //            with ProspectDAOService_DI
        //            with LinkedinTaskServiceDI
        //            with ProspectQueryDI
        //            with ProspectServiceV2_DI
        //            with CampaignProspectDAO_DI
        //            with CampaignProspectDAO_2DI
        //            with CampaignSchedulingMetadataDAO_DI
        //            with SrRandomUtilsDI
        //            with ProspectColumnDef_DI
        //            with MergeTagService_DI
        //            with ProspectsEmailsDAO_DI
        //            with ReplySentimentDAOService_DI
        //            with ProspectColumnDefDAO_DI
        //            with ReplySentimentDAO_DI
        //            with ReplySentimentJedisDAO_DI
        //            with EmailValidationModelDI
        //            with Tasks.TaskDaoServiceDI
        ////            with Tasks.TaskCacheDAO_DI
        //            with SrEventServiceDI
        ////            with Tasks.TaskCacheService_DI
        //            with KafkaServiceDI
        //            with Tasks.TaskDAO_DI
        //            with ScyllaRunSafely_DI
        //            with ScyllaDbConnection_DI
        //            with SrDBQueryCounterService_DI
        //            with DBCounterDAO_DI
        //            with CacheServiceJedisDI
        //            with EmailThreadDAO_DI
        //            with DBUtils_DI
        //            with AccountDAO_DI
        //            with SrUuidUtilsDI
        //          {
        //            def init() = {
        //              logger.info("inside init")
        //              taskDaoService.findBatchTasksAndAddtoScylla(
        //                taskIds = task_ids,
        //                teamId = 9,
        //                permittedAccountIds = Seq(11),
        //              )
        ////              taskDaoService.scyllaConnectionCheck()
        ////                            .map(
        ////                              result => logger.info("\n\n inserted data successfully")
        ////                            )
        ////                              .recover {
        ////                                case e => logger.debug(s"\n\n failed while inserting data ${e.getMessage}")
        ////                              }
        //            }
        //
        //            override val scyllaCluster: Cluster = scyllaDbConnection.initialize()
        //
        //          }
        //
        //          Await.result(
        //            main.init().map{ tasks => println(s"\n\n\nfound Tasks -> $tasks\n\n\n")
        //            },
        //            100.seconds
        //          )

        //        case "updateTaskForuser" =>
        //
        //          implicit val ec = Helpers.genFixedThreadPoolEC(threads = 10)
        //
        //          given logger: SRLogger = new SRLogger("testing connection")
        //          object main extends Tasks.TaskCacheService_DI
        //            with ScyllaDbConnection_DI
        //            with ScyllaRunSafely_DI
        //            with Tasks.TaskDAO_DI
        //            with SrDBQueryCounterService_DI
        //            with DBCounterDAO_DI
        //            with CacheServiceJedisDI {
        //            def init() = {
        //              logger.info("inside init")
        //              taskCacheService.updateTask(
        //                taskData = TaskCache(
        //                  task_id = "randomString2",
        //                  task_json = Json.toJson("hello2 json").toString(),
        //                  created_at = DateTime.now()
        //                ))
        ////              .map(
        ////                result => logger.info("\n\n inserted data successfully")
        ////              )
        ////                .recover {
        ////                  case e => logger.debug(s"\n\n failed while inserting data ${e.getMessage}")
        ////                }
        //            }
        //            override val scyllaCluster: Cluster = scyllaDbConnection.initialize()
        //          }
        //          Await.result(
        //            main.init()
        //          ,
        //            20.seconds
        //          )

        //          Thread.sleep(4000)

        case "createNewTaskInDB" =>

        //          val newTask = NewTask(
        //            task_type = TaskType.SendEmail,
        //            is_auto_task =  false,
        //            task_data = TaskData.SendEmailData(
        //              subject = "this is subject ",
        //              body = "this is body"
        //            ),
        //            status = TaskStatus.Due,
        //            assignee_id = 1,
        //            prospect_id = 1,
        //            priority = TaskPriority.Urgent,
        //            note = "hello",
        //          )
        //          implicit val ec = Helpers.genFixedThreadPoolEC(threads = 10)
        //          val accountDAO = new AccountDAO
        //          val taskDao = new TaskPgDAO(accountDao = accountDAO)
        //          Await.result(
        //          taskDao.createNewTask(
        //            task_data = newTask,
        //            account_id = 2,
        //            team_id = 2
        //          ).map{
        //            created_task =>
        //              logger.info(s"\n\n\n Created new task in db-> ${created_task}\n\n\n")
        //          }.recover{
        //            case e =>
        //              logger.error(s"\n\n\n Error While created new task in db ${e.getMessage}\n\n\n")
        //          }
        //      ,
        //      20.seconds
        //      )

        case "getTaskForUser" =>

        //          implicit val ec = Helpers.genFixedThreadPoolEC(threads = 10)
        //          val accountDAO = new AccountDAO
        //          val taskDao = new TaskPgDAO(accountDao = accountDAO)
        //          Await.result(
        //            taskDao.getTaskForUser(
        //              team_id = 9, assignee_id = 1
        //            ).map{
        //              tasks =>
        //                logger.info(s"\n\n\n found tasks -> ${tasks}\n\n\n")
        //            }.recover{
        //              case e =>
        //                logger.error(s"\n\n\n Error While fetching tasks ${e.getMessage}\n\n\n")
        //            }
        //            ,
        //            20.seconds
        //          )

        //        case "GetAllTasks" =>
        //
        //          implicit val ec = Helpers.genFixedThreadPoolEC(threads = 10)
        //          given logger: SRLogger = new SRLogger("testing connection")
        //
        //
        //          object main extends Tasks.TaskCacheService_DI
        //         with ScyllaDbConnection_DI
        //            with ScyllaRunSafely_DI {
        //           def init() = {
        //             logger.info("inside init")
        //             taskCacheService.getAllTaskFromCache()
        //           }
        //           override val scyllaCluster: Cluster = scyllaDbConnection.initialize()
        //         }
        //
        //          main.init()

       

        case "sd_move_last_updated_to_last_emailed_at" =>

          given logger: SRLogger = new SRLogger("move_last_emailed_at")

          logger.info("move_last_emailed_at")

          object main extends TestAppTrait {

            def init(): Unit = {

              migrationUtils.moveLastContactedAtToLastEmailedAt(
                team_id = TeamId(id = 15)
              ) match {
                case Left(RecursiveMigrationMethodError.GetIdsForMigrationError(err)) =>
                  logger.fatal(s"Error while getting prospect team_ids", err)
                  throw err
                case Left(RecursiveMigrationMethodError.UpdateTheIdsError(err)) =>
                  logger.fatal("Error while moving last_contacted_at", err)
                case Right(count_of_migration) =>
                  logger.info(s"Success count of migration : ${count_of_migration}")

              }
            }

          }

          main.init()

        case "vg/finalDeleteAccount" =>

          val logger: SRLogger = new SRLogger("testing connection")

          logger.info("finalDeleteAccount")

          object main extends AccountDAOV2_DI
            with OrganizationDAO_DI {

            def init(): Unit = {

              accountDAOV2.deleteAccount(accountId = 16, orgId = 17) match {
                case Success(value) =>
                  logger.info(s"finalDeleteAccount --------- $value")
                case Failure(exception) =>
                  logger.error("finalDeleteAccount FAILED", err = exception)
                  throw exception
              }
            }

          }

          main.init()

        case "vg/populateDefaultOpportunitiesData" =>

          val logger: SRLogger = new SRLogger("populateDefaultOpportunitiesData")

          object main extends OrganizationDAO_DI
            with MqOpportunitiesDefaultSetupMigration_DI
            with OpportunitiesDefaultSetupService_DI
            with DBUtils_DI
            with OpportunityStatusDAO_DI
            with PipelineDAO_DI
            with SrUuidUtilsDI
            with TeamsPipelineConfigDAO_DI
            with TeamsPipelineConfigService_DI {

            def init(): Unit = {

              organizationDAO.getAllOrgOwnerAndTeamIdsForDefaultOpportunityMigration().map { orgOwnerAndTeamIds =>

                orgOwnerAndTeamIds.foreach { (o: OpportunitiesDefaultSetupData) =>

                  mqOpportunitiesDefaultSetupMigration.publish(
                    msg = o
                  )

                }
              }
            }
          }

          main.init()

        case "vg/getAccountDetailsByRefId" =>

          val logger: SRLogger = new SRLogger("testing connection")

          logger.info("getAccountDetailsByRefId")

          object main extends AffiliateDAO_DI {

            def init(): Unit = {

              affiliateDAO.getAccountDetailsByRefId(ref_id = "micheal30") match {
                case Failure(exception) =>
                  logger.error("getAccountDetailsByRefId FAILED", err = exception)
                  throw exception

                case Success(None) =>
                  logger.info(s"getAccountDetailsByRefId: None")

                case Success(Some(accountDetails: FirstPromoterReferrerAccountDetails)) =>
                  logger.info(s"getAccountDetailsByRefId: $accountDetails")

              }
            }

          }

          main.init()

        case "vg/getReferredOrgDetails" =>

          val logger: SRLogger = new SRLogger("testing connection")

          logger.info("getReferredOrgDetails")

          object main extends AffiliateDAO_DI {

            def init(): Unit = {

              affiliateDAO.getReferredOrgDetails(orgId = OrgId(id = 10)) match {
                case Failure(exception) =>
                  logger.error("getReferredOrgDetails FAILED", err = exception)
                  throw exception

                case Success(None) =>
                  logger.info(s"getReferredOrgDetails: None")

                case Success(Some(ownerDetails: OrgOwnerDetails)) =>
                  logger.info(s"getReferredOrgDetails: $ownerDetails")
              }
            }
          }

          main.init()

        case "vg/getPendingCouponApprovals" =>

          val logger: SRLogger = new SRLogger("testing connection")

          logger.info("getPendingCouponApprovals")

          object main extends AffiliateDAO_DI {

            def init(): Unit = {

              affiliateDAO.getPendingCouponApprovals(
                fromDate = DateTime.now().minusDays(50)
              ) match {
                case Failure(exception) =>
                  logger.error("getPendingCouponApprovals FAILED", err = exception)
                  throw exception

                case Success(pendingApprovals: List[ReferralCouponDetails]) =>
                  logger.info(s"getPendingCouponApprovals: $pendingApprovals")

              }
            }

          }

          main.init()

        case "vg/updateReferralCouponStatusOnPayment" =>

          val logger: SRLogger = new SRLogger("testing connection")

          logger.info("updateReferralCouponStatusOnPayment")

          object main extends AffiliateDAO_DI {

            def init(): Unit = {

              affiliateDAO.updateReferralCouponStatusOnPayment(orgId = OrgId(id = 16)) match {
                case Failure(exception) =>
                  logger.error("updateReferralCouponStatusOnPayment FAILED", err = exception)
                  throw exception

                case Success(value) =>
                  logger.info(s"updateReferralCouponStatusOnPayment: $value")

              }
            }

          }

          main.init()

        case "vg/updateReferralCouponStatus" =>

          val logger: SRLogger = new SRLogger("testing connection")

          logger.info("updateReferralCouponStatus")

          object main extends AffiliateDAO_DI {

            def init(): Unit = {

              affiliateDAO.updateReferralCouponStatus(
                orgId = OrgId(id = 16),
                couponStatus = ReferralCouponStatus.Approved
              ) match {
                case Failure(exception) =>
                  logger.error("updateReferralCouponStatus FAILED", err = exception)
                  throw exception

                case Success(value) =>
                  logger.info(s"updateReferralCouponStatus: $value")

              }
            }

          }

          main.init()

        case "vg/getOrgIdsToUpdateInternalSRUsageData" =>

          val logger: SRLogger = new SRLogger("testing connection")

          logger.info("getOrgIdsToUpdateInternalSRUsageData")

          object main extends OrganizationDAO_DI {

            def init(): Unit = {

              organizationDAO.getOrgIdsToUpdateInternalSRUsageData() match {
                case Success(orgIds) =>
                  logger.info(s"getOrgIdsToUpdateInternalSRUsageData --------- $orgIds")
                case Failure(exception) =>
                  logger.error("getOrgIdsToUpdateInternalSRUsageData FAILED", err = exception)
                  throw exception
              }
            }

          }

          main.init()


        case "vg/getAffiliateCreatedAtByOrgId" =>

          val logger: SRLogger = new SRLogger("testing connection")

          logger.info("getAffiliateCreatedAtByOrgId")

          object main extends OrganizationDAO_DI {

            def init(): Unit = {

              organizationDAO.getAffiliateCreatedAtByOrgId(orgId = OrgId(id = 5)) match {
                case Failure(exception) =>
                  logger.error("getAffiliateCreatedAtByOrgId FAILED", err = exception)
                  throw exception

                case Success(None) =>
                  logger.info("affiliateCreatedAt found NONE")

                case Success(Some(affiliateCreatedAt)) =>
                  logger.info(s"affiliateCreatedAt found - $affiliateCreatedAt")
              }
            }

          }

          main.init()

        case "vg/updateAdmitadTrackSignup" =>

          val logger: SRLogger = new SRLogger("testing connection")

          logger.info("updateAdmitadTrackSignup")

          object main extends AccountDAO_DI
            with SrUuidUtilsDI {

            def init(): Unit = {

              accountDAO.updateAdmitadTrackSignup(
                orgId = OrgId(id = 5),
                admitad_uid = "Something",
                admitadGclidCookieValue = Some("value")
              ) match {
                case Failure(exception) =>
                  logger.error("updateAdmitadTrackSignup FAILED", err = exception)
                  throw exception

                case Success(value) =>
                  logger.info(s"updateAdmitadTrackSignup SUCCESS - $value")
              }
            }

          }

          main.init()

        case "vg/updateFirstPromoterTrackSignup" =>

          val logger: SRLogger = new SRLogger("testing connection")

          logger.info("updateFirstPromoterTrackSignup")

          object main extends AccountDAO_DI
            with SrUuidUtilsDI {

            def init(): Unit = {

              accountDAO.updateFirstPromoterAffiliateTrackSignup(
                orgId = OrgId(id = 5),
                fpLead = Json.obj("name" -> "fplead")
              ) match {
                case Failure(exception) =>
                  logger.error("updateFirstPromoterTrackSignup FAILED", err = exception)
                  throw exception

                case Success(value) =>
                  logger.info(s"updateFirstPromoterTrackSignup SUCCESS - $value")
              }
            }

          }

          main.init()

        case "vg/updateFirstPromoterReferralTrackSignup" =>

          val logger: SRLogger = new SRLogger("testing connection")

          logger.info("updateFirstPromoterReferralTrackSignup")

          object main extends AccountDAO_DI
            with SrUuidUtilsDI {

            def init(): Unit = {

              val firstPromoterReferrerAccountDetails = FirstPromoterReferrerAccountDetails(
                accountId = AccountId(id = 9), email = "Something"
              )

              accountDAO.updateFirstPromoterReferralTrackSignup(
                orgId = OrgId(id = 15),
                affiliateCookieValue = "CookieValue",
                firstPromoterReferrerAccountDetails = firstPromoterReferrerAccountDetails
              ) match {
                case Failure(exception) =>
                  logger.error("updateFirstPromoterReferralTrackSignup FAILED", err = exception)
                  throw exception

                case Success(value) =>
                  logger.info(s"updateFirstPromoterReferralTrackSignup SUCCESS - $value")
              }
            }

          }

          main.init()


        case "vg/getOrgDetailsForNewBillingFlow" =>

          val logger: SRLogger = new SRLogger("testing connection")

          logger.info("getOrgDetailsForNewBillingFlow")

          object main extends OrganizationBillingDAO_DI
            with EmailSettingDAO_DI
            with SrUuidUtilsDI
            with SrDBQueryCounterService_DI
            with DBCounterDAO_DI
            with CacheServiceJedisDI {

            def init(): Unit = {

              organizationBillingDAO.getOrgDetailsForNewBillingFlow(orgId = 15) match {
                case Failure(exception) =>
                  logger.error("getOrgDetailsForNewBillingFlow FAILED", err = exception)
                  throw exception

                case Success(None) =>
                  logger.info("No OrgPlanDetail found NONE")

                case Success(Some(orgPlanDetail)) =>
                  logger.info(s"OrgPlanDetail found - $orgPlanDetail")
              }

            }

          }

          main.init()

        case "vg/getOpportunities" =>

          val logger: SRLogger = new SRLogger(logRequestId = "Opportunities")

          object main extends OpportunityDAO_DI {

            def init(): Unit = {

              opportunityDAO.getOpportunities(
                teamId = TeamId(id = 1),
                pipelineIdOpt = None,
                opportunityStatusIdOpt = None,
                prospectIdOpt = None,
                olderThanOpt = None,
                maxCurrOpportunityRankOpt = None,
                permittedAccountIds = Seq(AccountId(id = 23)),
                maxOpportunitiesFetchFromDBLimit = 10
              ) match {
                case Failure(exception) =>
                  logger.error("getOpportunities failed", err = exception)
                  throw exception

                case Success(opportunities) =>
                  logger.warn(s"getOpportunities success - $opportunities")
              }
            }
          }

          main.init()

        case "vg/getStatuses" =>

          val logger: SRLogger = new SRLogger(logRequestId = "OpportunityStatus")

          object main extends OpportunityStatusDAO_DI {

            def init(): Unit = {

              opportunityStatusDAO.getStatuses(
                teamId = TeamId(id = 1),
                pipelineUUID = PipelineUUID(uuid = "some-uuid")
              ) match {
                case Failure(exception) =>
                  logger.error("getStatuses failed", err = exception)
                  throw exception

                case Success(opportunityStatuses) =>
                  logger.warn(s"getStatuses success - $opportunityStatuses")
              }
            }
          }

          main.init()

        case "vg/createOpportunity" =>

          val logger: SRLogger = new SRLogger(logRequestId = "Opportunities")

          object main extends OpportunityDAO_DI with DBUtils_DI {

            def init(): Unit = {

              val opportunityData: OpportunityData = OpportunityData(
                prospect_id = ProspectId(id = 37),
                value = 234,
                currency = CurrencyType.USD,
                opportunity_type = OpportunityType.OneTime,
                opportunity_status_id = OpportunityStatusUUID(uuid = "some-status-uuid-1"),
                pipeline_id = PipelineUUID(uuid = "some-pipeline-uuid-1"),
                confidence = 34,
                campaign_id = None,
                notes = None,
                owner_id = AccountUuid(uuid = "some-uuid"),
                estimate_closing_date = None
              )

              val dbAndSession = dbUtils.startLocalTx()

              implicit val session = dbAndSession.session

              opportunityDAO.createOpportunity(
                teamId = TeamId(id = 1),
                opportunityUUID = OpportunityUUID(uuid = "some-opp-uuid-new-1"),
                opportunityData = opportunityData,
                opportunityStatusInternalId = OpportunityStatusInternalId(id = 1),
                pipelineInternalId = PipelineInternalId(id = 1),
                ownerAccountInternalId = AccountId(id = 4),
                newHighestOpportunityRankInStatus = OpportunityPosRank(rank = 23423)
              ) match {
                case Failure(exception) =>
                  logger.error("createOpportunity failed", err = exception)
                  throw exception

                case Success(opportunityOpt) =>
                  logger.warn(s"createOpportunity success - $opportunityOpt")
              }

              dbUtils.commitAndCloseSession(dbAndSession.db)

            }
          }

          main.init()

        case "vg/getPipelines" =>

          val logger: SRLogger = new SRLogger(logRequestId = "Pipelines")

          object main extends PipelineDAO_DI {

            def init(): Unit = {

              pipelineDAO.getPipelines(
                teamId = TeamId(id = 1)
              ) match {
                case Failure(exception) =>
                  logger.error("getPipelines failed", err = exception)
                  throw exception

                case Success(pipelines) =>
                  logger.warn(s"getPipelines success - $pipelines")
              }
            }
          }

          main.init()

        case "vg/createStatus" =>

          val logger: SRLogger = new SRLogger(logRequestId = "OpportunityStatus")

          object main extends OpportunityStatusDAO_DI {

            def init(): Unit = {

              val statusData = OpportunityStatusCreateData(
                opportunity_status_name = "some-status-name",
                opportunity_status_pos_rank = OpportunityStatusPosRank(rank = 140),
                opportunity_status_type = StatusType.Won,
                opportunity_status_color = "#ffffff",
              )

              opportunityStatusDAO.createStatus(
                teamId = TeamId(id = 1),
                pipelineInternalId = PipelineInternalId(id = 1),
                statusUUID = OpportunityStatusUUID(uuid = "new-status-uuid"),
                statusData = statusData
              ) match {
                case Failure(exception) =>
                  logger.error("createStatus failed", err = exception)
                  throw exception

                case Success(opportunityStatusOpt) =>
                  logger.warn(s"createStatus success - $opportunityStatusOpt")
              }
            }
          }

          main.init()

        case "vg/updateOpportunity" =>

          val logger: SRLogger = new SRLogger(logRequestId = "Opportunities")

          object main extends OpportunityDAO_DI with DBUtils_DI {

            def init(): Unit = {

              val opportunityData: OpportunityData = OpportunityData(
                prospect_id = ProspectId(id = 37),
                value = 2345,
                currency = CurrencyType.USD,
                opportunity_type = OpportunityType.OneTime,
                opportunity_status_id = OpportunityStatusUUID(uuid = "some-status-uuid-1"),
                pipeline_id = PipelineUUID(uuid = "some-pipeline-uuid-1"),
                confidence = 34,
                campaign_id = None,
                notes = None,
                owner_id = AccountUuid(uuid = "some-uuid"),
                estimate_closing_date = Some(DateTime.now())
              )

              val dbAndSession = dbUtils.startLocalTx()

              implicit val session = dbAndSession.session

              opportunityDAO.updateOpportunity(
                teamId = TeamId(id = 1),
                opportunityUUID = OpportunityUUID(uuid = "some-opp-uuid-new-1"),
                opportunityData = opportunityData,
                opportunityStatusInternalId = OpportunityStatusInternalId(id = 1),
                pipelineInternalId = PipelineInternalId(id = 1),
                ownerAccountInternalId = AccountId(id = 4),
                opportunityPosRank = OpportunityPosRank(rank = 23451)
              ) match {
                case Failure(exception) =>
                  logger.error("updateOpportunity failed", err = exception)
                  throw exception

                case Success(opportunityOpt) =>
                  logger.warn(s"updateOpportunity success - $opportunityOpt")

              }

              dbUtils.commitAndCloseSession(db = dbAndSession.db)
            }
          }

          main.init()

        case "vg/createPipeline" =>

          val logger: SRLogger = new SRLogger(logRequestId = "Pipelines")

          object main extends PipelineDAO_DI {

            def init(): Unit = {

              val pipelineData = PipelineData(
                pipeline_name = "some-pipeline-name",
                currency_type = CurrencyType.USD
              )

              pipelineDAO.createPipelineWithAutoTx(
                teamId = TeamId(id = 1),
                ownerId = AccountId(id = 4),
                pipelineUUID = PipelineUUID(uuid = "some-pipeline-uuid"),
                pipelineData = pipelineData
              ) match {
                case Failure(exception) =>
                  logger.error("createPipeline failed", err = exception)
                  throw exception

                case Success(pipelineOpt) =>
                  logger.warn(s"createPipeline success - $pipelineOpt")
              }
            }
          }

          main.init()


        case "vg/reorderOpportunity" =>

          val logger: SRLogger = new SRLogger(logRequestId = "Opportunities")

          object main extends OpportunityDAO_DI with DBUtils_DI {

            def init(): Unit = {

              val reorderOpportunityData = ReorderOpportunityData(
                dest_status_uuid = OpportunityStatusUUID(uuid = "some-status-uuid-1"),
                to_be_next_opportunity_rank = OpportunityPosRank(rank = 200.23456),
                to_be_prev_opportunity_rank = OpportunityPosRank(rank = 100)
              )

              val dbAndSession = dbUtils.startLocalTx()

              implicit val session = dbAndSession.session

              opportunityDAO.reorderOpportunity(
                teamId = TeamId(id = 1),
                opportunityUUID = OpportunityUUID(uuid = "some-opp-uuid-new-1"),
                reorderOpportunityData = reorderOpportunityData,
                destOpportunityStatusInternalId = OpportunityStatusInternalId(id = 1)
              ) match {
                case Failure(exception) =>
                  logger.error("reorderOpportunity failed", err = exception)
                  throw exception

                case Success(opportunityOpt) =>
                  logger.warn(s"reorderOpportunity success - $opportunityOpt")
              }

              dbUtils.commitAndCloseSession(db = dbAndSession.db)

            }
          }

          main.init()

        case "vg/updateStatus" =>

          val logger: SRLogger = new SRLogger(logRequestId = "OpportunityStatus")

          object main extends OpportunityStatusDAO_DI {

            def init(): Unit = {

              val statusData = OpportunityStatusUpdateData(
                opportunity_status_name = "some-status-name-update",
                opportunity_status_color = "#ffffff",
              )

              opportunityStatusDAO.updateStatus(
                teamId = TeamId(id = 1),
                pipelineInternalId = PipelineInternalId(id = 1),
                statusUUID = OpportunityStatusUUID(uuid = "new-status-uuid"),
                statusData = statusData
              ) match {
                case Failure(exception) =>
                  logger.error("updateStatus failed", err = exception)
                  throw exception

                case Success(opportunityStatusOpt) =>
                  logger.warn(s"updateStatus success - $opportunityStatusOpt")
              }
            }
          }

          main.init()

        case "vg/deleteOpportunity" =>

          val logger: SRLogger = new SRLogger(logRequestId = "Opportunities")

          object main extends OpportunityDAO_DI with DBUtils_DI {

            def init(): Unit = {

              val dbAndSession = dbUtils.startLocalTx()

              implicit val session = dbAndSession.session

              opportunityDAO.deleteOpportunity(
                teamId = TeamId(id = 1),
                opportunityUUID = OpportunityUUID(uuid = "some-opp-uuid-new-1"),
              ) match {
                case Failure(exception) =>
                  logger.error("deleteOpportunity failed", err = exception)
                  throw exception

                case Success(deleteCount) =>
                  logger.warn(s"deleteOpportunity success - $deleteCount")
              }

              dbUtils.commitAndCloseSession(db = dbAndSession.db)

            }
          }

          main.init()

        case "vg/migrateOpportunities" =>

          val logger: SRLogger = new SRLogger(logRequestId = "Opportunities")

          object main extends OpportunityDAO_DI with DBUtils_DI {

            def init(): Unit = {

              val dbAndSession = dbUtils.startLocalTx()

              implicit val session = dbAndSession.session

              opportunityDAO.transferOpportunities(
                teamId = TeamId(id = 63),
                fromOpportunityStatusInternalId = OpportunityStatusInternalId(id = 9),
                toOpportunityStatusInternalId = OpportunityStatusInternalId(id = 3),
                highestRankInToBeTransferredStatus = OpportunityPosRank(rank = 1000)
              ) match {
                case Failure(exception) =>
                  logger.error("migrateOpportunities failed", err = exception)
                  throw exception

                case Success(deleteCount) =>
                  logger.warn(s"migrateOpportunities success - $deleteCount")
              }

              dbUtils.commitAndCloseSession(db = dbAndSession.db)
            }
          }

          main.init()

        case "vg/updatePipeline" =>

          val logger: SRLogger = new SRLogger(logRequestId = "Pipelines")

          object main extends PipelineDAO_DI {

            def init(): Unit = {

              val pipelineData = PipelineData(
                pipeline_name = "some-pipeline-name-new",
                currency_type = CurrencyType.USD
              )

              pipelineDAO.updatePipeline(
                teamId = TeamId(id = 1),
                pipelineUUID = PipelineUUID(uuid = "some-pipeline-uuid"),
                pipelineData = pipelineData
              ) match {
                case Failure(exception) =>
                  logger.error("updatePipeline failed", err = exception)
                  throw exception

                case Success(pipelineOpt) =>
                  logger.warn(s"updatePipeline success - $pipelineOpt")
              }
            }
          }

          main.init()

        case "vg/reorderStatus" =>

          val logger: SRLogger = new SRLogger(logRequestId = "OpportunityStatus")

          object main extends OpportunityStatusDAO_DI {

            def init(): Unit = {

              val reorderStatusData = ReorderOpportunityStatusData(
                to_be_next_status_rank = OpportunityStatusPosRank(rank = 234.2340),
                to_be_prev_status_rank = OpportunityStatusPosRank(rank = 123.235667)
              )

              opportunityStatusDAO.reorderStatus(
                teamId = TeamId(id = 1),
                pipelineUUID = PipelineUUID(uuid = "some-uuid"),
                statusUUID = OpportunityStatusUUID(uuid = "new-status-uuid"),
                reorderStatusData = reorderStatusData
              ) match {
                case Failure(exception) =>
                  logger.error("reorderStatus failed", err = exception)
                  throw exception

                case Success(opportunityStatusOpt) =>
                  logger.warn(s"reorderStatus success - $opportunityStatusOpt")

              }
            }
          }

          main.init()

        case "vg/deletePipeline" =>

          val logger: SRLogger = new SRLogger(logRequestId = "Pipelines")

          object main extends PipelineDAO_DI {

            def init(): Unit = {

              pipelineDAO.deletePipeline(
                teamId = TeamId(id = 1),
                pipelineUUID = PipelineUUID(uuid = "some-pipeline-uuid"),
              ) match {
                case Failure(exception) =>
                  logger.error("deletePipeline failed", err = exception)
                  throw exception

                case Success(deleteCount) =>
                  logger.warn(s"deletePipeline success - $deleteCount")
              }
            }
          }

          main.init()

        case "vg/deleteStatus" =>

          val logger: SRLogger = new SRLogger(logRequestId = "OpportunityStatus")

          object main extends OpportunityStatusDAO_DI with DBUtils_DI {

            def init(): Unit = {

              val dbAndSession = dbUtils.startLocalTx()

              implicit val session = dbAndSession.session

              opportunityStatusDAO.deleteStatus(
                teamId = TeamId(id = 1),
                pipelineUUID = PipelineUUID(uuid = "some-uuid"),
                statusUUID = OpportunityStatusUUID(uuid = "new-status-uuid"),
              ) match {
                case Failure(exception) =>
                  logger.error("deleteStatus failed", err = exception)
                  throw exception

                case Success(opportunityStatusOpt) =>
                  logger.warn(s"deleteStatus success - $opportunityStatusOpt")
              }

              dbUtils.commitAndCloseSession(db = dbAndSession.db)
            }
          }

          main.init()

    

        case "vg/searchMessagesByBodyTermViaAPIs" =>

          val Logger: SRLogger = new SRLogger("testing connection")

          implicit val  system: ActorSystem = ActorSystem()
          implicit val  materializer: Materializer = Materializer(system)
          implicit val  ws: AhcWSClient = AhcWSClient()
          implicit val  ec: ExecutionContext = system.dispatcher

          Logger.info("moveToInbox")
          object receive extends OutlookReceiveEmailService {
            override def updateAccessTokenAndRefreshToken(emailSettingId: EmailSettingId, data: EmailSettingUpdateAccessToken): Try[Option[EmailSettingId]] = {
              Success(Some(EmailSettingId(2)));
            }
          }

          def init(): Unit = {

            val resFut = receive.searchMessagesByBodyTermViaAPI(
              bodySearchTerm = "some-body-search-term",
              accessToken = AccessToken(id = "<Access-Token>"),
              Logger = Logger
            )

            val res = Await.result(resFut, scala.concurrent.duration.Duration.Inf)

            res
              .filter(msg => msg.commonPropsEmailMessage.received_at.isAfterNow)
              .foreach { msg =>
                Logger.warn(s"moveToInbox: outlook_msg_id: ${msg.outlook_msg_id} :: subject: ${msg.commonPropsEmailMessage.subject}")
              }
          }

          init()

        case "vg/getEnrichedCompanyData" =>

          given logger: SRLogger = new SRLogger("GetEnrichedCompanyData")

          implicit val  system: ActorSystem = ActorSystem()
          implicit val  materializer: Materializer = Materializer(system)
          implicit val  ws: AhcWSClient = AhcWSClient()
          implicit val  ec: ExecutionContext = system.dispatcher

          object main extends AccountDAO_DI
            with CompaniesServiceDI
            with SrUuidUtilsDI
            with TheCompaniesApiDI {

            def init(): Unit = {

              companiesService.getEnrichedCompanyData(
                companyDomain = "mckesson.com",
                apiKey = "API_KEY"
              ).map { e =>

                accountDAO.updateOrgWithEnrichedCompanyData(
                  orgId = OrgId(id = 15),
                  enrichedCompanyData = e
                ) match {

                  case Failure(exception) =>
                    logger.error("updateOrgWithEnrichedCompanyData Failed", err = exception)

                  case Success(None) =>
                    logger.error("updateOrgWithEnrichedCompanyData None")

                  case Success(Some(orgId)) =>
                    logger.warn(s"orgId: $orgId")

                }
              }
            }
          }

          main.init()

        case "vg/getOrgDetailByPgCustomerId" =>

          val logger: SRLogger = new SRLogger("testing connection")

          logger.info("getOrgDetailByPgCustomerId")

          object main extends OrganizationBillingDAO_DI
            with EmailSettingDAO_DI
            with SrDBQueryCounterService_DI
            with SrUuidUtilsDI
            with DBCounterDAO_DI
            with CacheServiceJedisDI {

            def init(): Unit = {

              organizationBillingDAO.getOrgDetailByPgCustomerId(pgCustomerId = "xyz", pg = PaymentGateway.FASTSPRING) match {
                case Failure(exception) =>
                  logger.error("getOrgDetailByPgCustomerId FAILED", err = exception)
                  throw exception

                case Success(None) =>
                  logger.info("No OrgPlanDetail found NONE")

                case Success(Some(orgPlanDetail)) =>
                  logger.info(s"OrgPlanDetail found - $orgPlanDetail")
              }
            }

          }

          main.init()

        case "vg/getFunnelStageByOrgId" =>

          val logger: SRLogger = new SRLogger("getFunnelStageByOrgId")

          object main extends SrInternalFeatureUsageDaoDI {

            def init(): Unit = {

              srInternalFeatureUsageDao.getFunnelStageByOrgId(orgId = 58) match {
                case Failure(exception) =>
                  logger.error("getOrgDetailByPgCustomerId FAILED", err = exception)
                  throw exception

                case Success(value) =>
                  logger.warn(s"OrgPlanDetail found - $value")
              }
            }
          }

          main.init()


        case "vg/updateInternalSRUsageData" =>

          val logger: SRLogger = new SRLogger("testing connection")

          logger.info("updateInternalSRUsageData")

          object main extends CampaignDAO_DI
            with SrDBQueryCounterService_DI
            with DBCounterDAO_DI
            with CacheServiceJedisDI
            with SrUuidUtilsDI {

            def init(): Unit = {

              val internalSRUsageDataWithScore = Map(
                "using_ab_testing" -> 5,
                "using_sending_holiday_calendar" -> 0,
                "using_spam_test" -> 4,
                "using_integration_via_zapier" -> 7,
                "using_custom_column" -> 9,
                "using_workflow_automation" -> 0,
                "using_merge_tags" -> 3,
                "using_campaign_warmup" -> 8,
                "using_webhooks" -> 0,
                "using_email_finder" -> 7,
                "using_prospect_daddy" -> 5,
                "soft_start_greater_than_50_percent" -> 9,
                "invited_team_members" -> 0,
                "using_multichannel" -> 8,
                "using_shared_inbox" -> 2,
                "tagging_reply_sentiments" -> 3,
                "using_prospect_categorization" -> 6
              )

              val internalAdoptionReport = InternalAdoptionReport(
                internalSRUsageDataWithScore = internalSRUsageDataWithScore,
                internalSRAdoptionScore = internalSRUsageDataWithScore.foldLeft(0)(_ + _._2)
              )

              campaignDAO.updateInternalSRUsageData(
                internalAdoptionReport = internalAdoptionReport,
                orgId = OrgId(id = 9)
              ) match {
                case Success(value) => {
                  logger.info(s"udpateInternalSRUSageData --------- $value")
                }
                case Failure(err) => {
                  logger.error("updateInternalS fialed", err = err)
                  throw err
                }
              }
            }

          }

          main.init()

        case "vg/getAffiliateDetailsByOrgId" =>

          val logger: SRLogger = new SRLogger("testing connection")

          logger.info("getAffiliateDetailsByOrgId")

          object main extends OrganizationDAO_DI {

            def init(): Unit = {

              organizationDAO.getAffiliateDetailsByOrgId(
                orgId = OrgId(id = 5),
              ) match {
                case Failure(exception) =>
                  logger.error("getAffiliateDetailsByOrgId FAILED", err = exception)
                  throw exception

                case Success(value) =>
                  logger.info(s"getAffiliateDetailsByOrgId SUCCESS - $value")
              }
            }

          }

          main.init()


        case "vg/getInternalSRUsageDataByOrgID" =>

          val logger: SRLogger = new SRLogger("testing connection")

          logger.info("getInternalSRUsageDataByOrgID")

          object main extends CampaignDAO_DI
            with SrDBQueryCounterService_DI
            with DBCounterDAO_DI
            with CacheServiceJedisDI
            with SrUuidUtilsDI {

            def init(): Unit = {

              campaignDAO.getInternalSRUsageDataByOrgID(
                orgId = 3
              ) match {
                case Success(internalSRUsageData: Option[InternalSRUsageData]) => {
                  internalSRUsageData match {
                    case Some(value) =>
                      logger.info(s"getInternalSRUsageDataByOrgID --------- $value")

                    case None =>
                      logger.info(s"getInternalSRUsageDataByOrgID --------- NONE")
                  }
                }
                case Failure(err) => {
                  logger.error("getInternalSRUsageDataByOrgID FAILED", err = err)
                  throw err
                }
              }
            }

          }

          main.init()

        case "vg/getCustomerAcquisitionData" =>

          val logger: SRLogger = new SRLogger("testing connection")

          logger.info("getCustomerAcquisitionData")

          object main extends InternalCSDReportDAODI {

            def init(): Unit = {

              val startDate = DateTime.now().minusDays(1)
              val endDate = DateTime.now()

              internalCSDReportDAO.getCustomerAcquisitionData(startDate = startDate, endDate = endDate) match {
                case Failure(exception) =>
                  logger.error("getCustomerAcquisitionData FAILED", err = exception)
                  throw exception
                case Success(customerAcquisitionDataOpt: Option[CustomerAcquisitionSRData]) =>
                  customerAcquisitionDataOpt match {
                    case Some(value) =>
                      logger.info(s"getCustomerAcquisitionData ------ $value")
                    case None =>
                      logger.info("getCustomerAcquisitionData NONE")
                  }
              }
            }

          }

          main.init()

        case "vg/createAcquisitionReportEntry" =>

          val logger: SRLogger = new SRLogger("testing connection")

          logger.info("createAcquisitionReportEntry")

          object main extends InternalCSDReportDAODI {

            def init(): Unit = {

              val customerAcquisitionDataCombined = CustomerAcquisitionDataCombined(
                trial_started_count = 3,
                num_of_agencies = 5,
                num_of_team_size_greater_than_21 = 5,
                num_of_team_size_greater_than_50 = 5,
                num_of_new_subs = 34,
                num_of_churn_subs = 45,
                num_of_active_subs = 453,
                new_mrr = 234,
                reactivation_mrr = 452,
                expansion_mrr = 453,
                contraction_mrr = 345,
                churn_mrr = 675,
                start_date = DateTime.now().minusDays(1).withZone(DateTimeZone.UTC).withTimeAtStartOfDay(),
                end_date = DateTime.now().plus(1)
              )

              internalCSDReportDAO.createAcquisitionReportEntry(
                customerAcquisitionDataCombined = customerAcquisitionDataCombined
              ) match {
                case Failure(exception) =>
                  logger.error("createAcquisitionReportEntry FAILED", err = exception)
                  throw exception

                case Success(None) =>
                  logger.fatal("createAcquisitionReportEntry - NONE")

                case Success(Some(id: Long)) =>
                  logger.success(s"createAcquisitionReportEntry - id: $id")
              }
            }
          }

          main.init()

        case "vg/updateAcquisitionReport" =>

          val logger: SRLogger = new SRLogger("testing connection")

          logger.info("updateAcquisitionReport")

          object main extends InternalCSDReportDAODI {

            def init(): Unit = {
              val acquisitionFieldData = AcquisitionFieldData(
                id = 1,
                users_count = None,
                new_users_count = Some(21),
                demo_delivered = None,
                good_fit = Some(7)
              )

              internalCSDReportDAO.updateAcquisitionReport(
                acquisitionFieldData = acquisitionFieldData
              ) match {
                case Failure(exception) =>
                  logger.error("updateAcquisitionReport FAILED", err = exception)
                  throw exception

                case Success(updateCount: Int) =>
                  logger.success(s"updateAcquisitionReport - id: $updateCount")
              }
            }
          }
          main.init()

        case "vg/checkIfAcquisitionReportExists" =>

          val logger: SRLogger = new SRLogger("testing connection")

          logger.info("checkIfAcquisitionReportExists")

          object main extends InternalCSDReportDAODI {

            def init(): Unit = {

              internalCSDReportDAO.checkIfAcquisitionReportExists(
                startDate = DateTime.now().minusDays(1).withZone(DateTimeZone.UTC).withTimeAtStartOfDay(),
                endDate = DateTime.now().plus(1)
              ) match {
                case Failure(exception) =>
                  logger.error("createAcquisitionReportEntry FAILED", err = exception)
                  throw exception

                case Success(None) =>
                  logger.fatal("checkIfAcquisitionReportExists - NONE")

                case Success(Some(acquisitionReportExits: Boolean)) =>
                  logger.success(s"checkIfAcquisitionReportExists - acquisitionReportExits: $acquisitionReportExits")
              }
            }
          }

          main.init()

        case "vg/getInternalSRUsageData" =>

          val logger: SRLogger = new SRLogger("testing connection")

          logger.info("getInternalSRUsageDataV2")

          object main extends SrInternalFeatureUsageDaoDI {

            def init(): Unit = {

              srInternalFeatureUsageDao.getInternalSRUsageData() match {
                case Success(value) => {
                  logger.info(s"getInternalSRUsageData --------- $value")
                }
                case Failure(err) => {
                  logger.error("getInternalSRUsageData FAILED --", err = err)
                  throw err
                }
              }
            }

          }

          main.init()

        case "vg/getAudienceReportData" =>

          val logger: SRLogger = new SRLogger("testing connection")

          logger.info("getAudienceReportData")

          object main extends SrInternalFeatureUsageDaoDI {

            def init(): Unit = {

              srInternalFeatureUsageDao.getAudienceReportData(
                report_type = InternalAudienceReportType.SubscribedActive
              ) match {
                case Success(value) => {
                  logger.info(s"getAudienceReportData --------- $value")
                }
                case Failure(err) => {
                  logger.error("getAudienceReportData FAILED --", err = err)
                  throw err
                }
              }
            }

          }

          main.init()


        case "vg/getResultsForPreviouslyValidatedEmails" =>

          val emails = Seq("<EMAIL>", "<EMAIL>")


          val emailValidationModel = new EmailValidationModel()

          val result = emailValidationModel.getResultsForPreviouslyValidatedEmails(emails) match {
            case Failure(exception) =>

              logger.error("emailValidationModel.getResultsForPreviouslyValidatedEmails failed: ", exception)
              throw exception

            case Success(prospectValidationResult) =>
              prospectValidationResult
          }

          logger.info(s"vg/getResultsForPreviouslyValidatedEmails: $result")


        case "vg/createAccount" =>


          val logger: SRLogger = new SRLogger("testing connection")

          logger.info("createAccount")

          object main extends TestAppTrait {

            def init(): Unit = {

              firstPromoter.createAccount(
                email = "<EMAIL>",
                firstName = Some("testv"),
                lastName = Some("gvte"),
                customerId = AccountId(id = 34),
                logger = logger
              ).map(
                value =>
                  logger.info(s"createAccount - Success $value")
              ).recover(
                e =>
                  logger.error("createAccount - error", err = e)
              )
            }

          }

          main.init()


        case "vg/sendProspectsForValidation" =>
          val logger: SRLogger = new SRLogger("testing connection")

          logger.info("sendProspectsForValidation")

          object main extends TestAppTrait {

            def init(): Unit = {

              //              val prospect1 = ProspectForValidation(
              //                id = 1,
              //                email = "<EMAIL>",
              //                email_checked = false,
              //                email_sent_for_validation = false,
              //                email_sent_for_validation_at = Option(DateTime.now())
              //              )

              //              val prospectsForValidation = Seq(prospect1)

              val prospectIdsForValidation: Seq[Long] = Seq(1, 5, 6)

              val result: Try[Int] = emailValidationService.sendProspectsForValidation(
                priority = EmailValidationPriority.Low,
                logger = logger,
                accountId = 1,
                teamId = TeamId(1),
                orgId = 1,
                isAgency = true,
                idsOrEmailsForValidation = IdsOrEmailsForValidation.ProspectIdsForValidation(
                  prospectIds = prospectIdsForValidation.map(id => ProspectId(id = id)),
                  initiatorCampaign = CampaignId(id = 8),
                ),
              )

              logger.info(s"ProspectForValidation ${result}")
            }

            override lazy val mqActivityTriggerService: MQActivityTriggerService = new MQActivityTriggerService(
              emailScheduledDAO = emailScheduledDAO,
              prospectDAOService = prospectDAOService,
              eventLogService = eventLogService,
              mqActivityTriggerPublisher = mqActivityTriggerPublisher
            )

            override lazy val trigger: Trigger = new Trigger
          }

          main.init()

        case "vg/updatePreviouslyValidatedEmailsWithBounceResponse" =>

          val bouncedReply1 = ProspectBounceResponseInfo(
            prospectId = 5,
            prospectEmail = "<EMAIL>",
            bouncedAt = DateTime.now(),
            campaignId = None,
            bounceType = Some(EmailReplyBounceType.EmailAddressNotFound),
            bounceReason = Some("possible_mail_loop")
          )

          val bouncedReply2 = ProspectBounceResponseInfo(
            prospectId = 1,
            prospectEmail = "<EMAIL>",
            bouncedAt = DateTime.now(),
            campaignId = None,
            bounceType = Some(EmailReplyBounceType.EmailAddressNotFound),
            bounceReason = Some("possible_mail_loop")
          )

          val prospectIdToProspectEmailId: Map[ProspectId, ProspectEmailId] =
            Map(
              ProspectId(id = 5) -> ProspectEmailId(id = 5),
              ProspectId(id = 1) -> ProspectEmailId(id = 1)
            )

          val hardBouncedReplies: Seq[ProspectBounceResponseInfo] = Seq(bouncedReply1, bouncedReply2)

          val emailValidationModel = new EmailValidationModel()

          val result = emailValidationModel.updatePreviouslyValidatedEmailsWithBounceResponse(
            hardBouncedReplies = hardBouncedReplies,
            prospectIdToProspectEmailId = prospectIdToProspectEmailId
          ) match {
            case Failure(exception) =>

              logger.error(s"emailValidationModel.updateEmailValidationResultsWithBouncedReplies failed: $exception")
              throw exception

            case Success(result) =>
              result
          }

          logger.info(s"vg/updateEmailValidationResultsWithBouncedReplies: $result")


        case "guru/_assignOrgsToRelevantDripCampaign" =>

          object drip extends TestAppTrait {

            def init(): Unit = {
              val logger: SRLogger = new SRLogger("testing connection")
              logger.error("Starting the program")
              val result = newUserDripCampaignService.assignOrgsToRelevantDripCampaign(logger)
              Await.result(result, scala.concurrent.duration.Duration.Inf)
              println(s"responseText: $result")
              logger.warn("Finished the program")
            }

          }

          drip.init()

        case "vg/_updateEmailValidationDataAfterBouncingV3" =>

          val logger: SRLogger = new SRLogger("testing connection")

          logger.info("_updateEmailValidationDataAfterBouncingV3")


          object main extends TestAppTrait {

            def init(): Unit = {

              val bouncedReply1 = ProspectBounceResponseInfo(
                prospectId = 5,
                prospectEmail = "<EMAIL>",
                bouncedAt = DateTime.now(),
                campaignId = None,
                bounceType = Some(EmailReplyBounceType.EmailAddressNotFound),
                bounceReason = Some("possible_mail_loop")
              )

              val bouncedReplies: Seq[ProspectBounceResponseInfo] = Seq(bouncedReply1)

              val result = prospectServiceV2._updateEmailValidationDataAfterHardBouncingV3(
                hardBouncedReplies = bouncedReplies,
                teamId = TeamId(id = 1),
                logger = logger
              )


              result match {
                case Failure(exception) =>
                  logger.error(s"Error _updateEmailValidationDataAfterBouncingV3:", exception)
                  throw exception
                case Success(numOfProspectEmailsUpdated: Int) =>
                  logger.info(s"_updateEmailValidationDataAfterBouncingV3 - ${numOfProspectEmailsUpdated}")
              }
            }

          }


          main.init()


        //        case  "CalendarAppService" =>
        //          implicit lazy val system = ActorSystem()
        //          implicit lazy val wSClient: AhcWSClient = AhcWSClient()
        //          implicit lazy val actorContext: ExecutionContext = system.dispatcher
        //            object CalendarApiTest extends Calendar.CalendarAppServiceDI {
        //              def init(): Unit = {
        //                val logRequestId = "CalendarAppService"
        //                given Logger = new SRLogger(logRequestId)
        //                  calendarAppService.getSession(
        //                    orgId = OrgId(id = 1),
        //                    userId = AccountId(id = 12),
        //                    userEmail = "<EMAIL>"
        //                  ).map( res => {
        //                    Logger.info(s"Success got res  ${res}",logInStaging = true)
        //                    res
        //                  }
        //                  ).recover( e => {
        //                    Logger.error(s"Failed future ${e.printStackTrace()}")
        //                    "Failed beautifully"
        //                  }
        //                )
        //
        //              }
        //            }
        //            CalendarApiTest.init()


        /*
                case "migrateInboxFolders" =>

                  val prospectColumnDef = new ProspectColumnDef(new ProspectColumnDefDAO)
                  val prospectQuery = new ProspectQuery(
                    prospectColumnDef = prospectColumnDef
                  )
                  val prospectAddEventDAO = new ProspectAddEventDAO
                  val freeEmailDomainListDAO = new FreeEmailDomainListDAO
                  val cacheServiceJedis = new CacheServiceJedis
                  val campaignProspectDAO_2 = new CampaignProspectDAO_2
                  val prospectsEmailsDAO = new ProspectsEmailsDAO
                  val campaignSchedulingMetadataDAO = new CampaignSchedulingMetadataDAO

                  val mergeTagService = new MergeTagService(
                    campaignProspectDAO_2 = campaignProspectDAO_2,
                    campaignSchedulingMetadataDAO = campaignSchedulingMetadataDAO
                  )
                  val replySentimentDAO = new ReplySentimentDAO
                  val replySentimentJedisDAO = new ReplySentimentJedisDAO(
                    cacheServiceJedis = cacheServiceJedis
                  )
                  val replySentimentDAOService = new ReplySentimentDAOService(
                    replySentimentDAO = replySentimentDAO,
                    replySentimentJedisDAO = replySentimentJedisDAO
                  )

                  val prospectDAO = new ProspectDAO
                  val dbUtils = new DBUtils
                  val emailThreadDAO = new EmailThreadDAO

                  val dbCounterDAO = new DBCounterDAO(cacheServiceJedis)
                  val srDBQueryCounterService = new SrDBQueryCounterService(dbCounterDAO = dbCounterDAO)

                  val taskPgDAO = new TaskPgDAO(
                    srDBQueryCounterService = srDBQueryCounterService
                  )

                  val linkedinTaskService = new LinkedinTaskService(
                    taskPgDAO = taskPgDAO
                  )

                  val prospectDAOService = new ProspectDAOService(
                    prospectDAO = prospectDAO,
                    prospectQuery = prospectQuery,
                    linkedinTaskService = linkedinTaskService,
                    prospectColumnDef = prospectColumnDef,
                    mergeTagService = mergeTagService,
                    replySentimentDAOService = replySentimentDAOService,
                    emailThreadDAO = emailThreadDAO,
                    dbUtils = dbUtils
                  )

                  val emailValidationModel = new EmailValidationModel

                  val accountOrgBillingRelatedInfoDAO = new AccountOrgBillingRelatedInfoDAO()

                  val accountOrgBillingRelatedService = new AccountOrgBillingRelatedService(accountOrgBillingRelatedInfoDAO)

                  val prospectServiceV2 = new ProspectServiceV2(
                    prospectDAOService = prospectDAOService,
                    prospectsEmailsDAO = prospectsEmailsDAO,
                    prospectAddEventDAO = prospectAddEventDAO,
                    prospectColumnDef = prospectColumnDef,
                    campaignProspectDAO_2 = campaignProspectDAO_2,
                    accountOrgBillingRelatedService = accountOrgBillingRelatedService,
                    mergeTagService = mergeTagService,
                    emailValidationModel = emailValidationModel
                  )
                  val rolePermissionDataDAOV2 = new RolePermissionDataDAOV2()
                  val organizationDAO = new OrganizationDAO()
                  val accountDAOV2 = new AccountDAOV2(organizationDAO)

                  val srUuidUtils = new SrUuidUtils
                  val accountDAO = new AccountDAO(
                    srUuidUtils = srUuidUtils
                  )

                  val migrationDAO = new MigrationDAO

                  val migrationService = new MigrationService(
                    migrationDAO = migrationDAO,
                    accountDAO = accountDAO
                  )

                  val emailMessageContactModel = new EmailMessageContactModel
                  val campaignProspectDAO = new CampaignProspectDAO(
                    prospectDAOService = prospectDAOService, prospectQuery = prospectQuery, srDBQueryCounterService = srDBQueryCounterService, prospectAddEventDAO = prospectAddEventDAO, prospectServiceV2 = prospectServiceV2
                  )
                  val migrationUtils = new MigrationUtils(
                    rolePermissionDataDAOV2 = rolePermissionDataDAOV2,
                    accountDAOV2 = accountDAOV2,
                    prospectServiceV2 = prospectServiceV2,
                    emailMessageContactModel = emailMessageContactModel,
                    migrationService = migrationService,
                    campaignProspectDAO = campaignProspectDAO,
                    replySentimentDAOService = replySentimentDAOService,
                    srUuidUtils = srUuidUtils,
                    dbUtils = dbUtils
                  )

                  given logger: SRLogger = new SRLogger("TestApp: migrateInboxFolders")

                  val team_ids_to_be_migrated: List[Long] = List(
                    11672, 11667, 11665, 11660, 11658, 11316, 9545, 9800, 12283, 5290,
                    11394, 11981, 12198, 12453, 12273, 12268, 11743, 12292, 5503, 10936,
                    10977, 10187, 12404, 11666, 12282, 11829, 11510, 12140, 11637, 11017,
                    10935, 11670, 12390, 10507, 11253, 11270, 12387, 11272, 11508, 11863,
                    10854, 12310, 9283, 11197, 11268, 11507, 11864, 9983, 11826, 11351,
                    11016, 9967, 11638, 12114, 11477, 10362, 11369, 11361, 10852, 11292,
                    11215, 11254, 12202, 11419, 10241, 11862, 12179, 11635, 12129, 12361,
                    11367, 10851, 10691, 11305, 11187, 12136, 11018, 11557, 11235, 11582,
                    11054, 9908, 12384, 11284, 12450, 11406, 11777, 9799, 12285, 11293,
                    10355, 11758, 10651, 11269, 11003, 11191, 11787, 11282, 9979, 11578,
                    11789, 11779, 11756, 11734, 11716, 11715, 11705, 12226, 11689, 11965,
                    11675, 11918, 11913, 11912, 11891, 11887, 11884, 11805, 11790, 12433,
                    12410, 12405, 12399, 12389, 12383, 12369, 12363, 12359, 12279, 12271,
                    12269, 12263, 12258, 12248, 12220, 12192, 12180, 12144, 12135, 12126,
                    12124, 12121, 12103, 12094, 12079, 12077, 12076, 12067, 12062, 12046,
                    12043, 12023, 12016, 12012, 12006, 11994, 11993, 11983, 11975, 11970,
                    11963, 11941, 11927
                  )

                  migrationUtils.migrateInboxFolders(team_ids_to_be_migrated)
        */

        case "MigrationCampaignEmailSetting" =>

        //          given logger: SRLogger = new SRLogger("TestApp: MigrationCampaignEmailSetting")
        //          val srUuidUtils = new SrUuidUtils()
        //
        //          def getCampaignIdsForUpdate(
        //                                       ids_to_update_from: IdsToUpdate,
        //                                       count_per_cycle: CountOfMigration
        //                                     ): Try[List[IdsToUpdate]] = Try {
        //            DB readOnly {implicit session =>
        //              sql"""
        //                    select id
        //                    from campaigns
        //                    where id > ${ids_to_update_from.id}
        //                    and sender_email_settings_id is not null
        //                    and receiver_email_settings_id is not null
        //                    order by id
        //                    Limit ${count_per_cycle.count}
        //                  """
        //                .map(rs => IdsToUpdate(rs.long("id")))
        //                .list.apply()
        //
        //            }
        //          }
        //        case class DataFromCampaigns(
        //                                      campaign_id: CampaignId,
        //                                      sender_email_setting_id: EmailSettingId,
        //                                      receiver_email_setting_id: EmailSettingId,
        //                                      team_id: TeamId
        //                                    )
        //
        //          def insertInCampaignEmailSetting (
        //                                           ids_to_migrate: List[IdsToUpdate]
        //                                           ): Try[CountOfMigration] = Try{
        //            val count = DB localTx  { implicit session =>
        //              val data = sql"""
        //
        //                    SELECT
        //                    id,
        //                    sender_email_settings_id,
        //                    receiver_email_settings_id,
        //                    team_id
        //                    FROM campaigns
        //                    where id in (${ids_to_migrate.map(_.id)})
        //                 """
        //                .map(rs => DataFromCampaigns(
        //                  campaign_id = CampaignId(rs.long("id")),
        //                  sender_email_setting_id = EmailSettingId(rs.long("sender_email_settings_id")),
        //                  receiver_email_setting_id = EmailSettingId(rs.long("receiver_email_settings_id")),
        //                  team_id = TeamId(rs.long("team_id"))
        //                ))
        //                .list.apply()
        //
        //
        //              var valueParameters = List[Any]()
        //
        //              val valuePlaceholder: SQLSyntax = data
        //                .map(t => {
        //
        //                  valueParameters = valueParameters ::: List(
        //                    t.campaign_id.id,
        //                    t.sender_email_setting_id.emailSettingId,
        //                    t.receiver_email_setting_id.emailSettingId,
        //                    t.team_id.id,
        //                    srUuidUtils.generateCampaignEmailSettingsUuid()
        //                  )
        //
        //                  sqls"""
        //                     (
        //                        ?,
        //                        ?,
        //                        ?,
        //
        //                        ?,
        //                        ?
        //                      )
        //                    """
        //
        //                })
        //                .reduce((vp1, vp2) => sqls"$vp1, $vp2")
        //
        //              sql"""
        //                    INSERT INTO campaign_email_settings (
        //                    campaign_id,
        //                    sender_email_setting_id,
        //                    receiver_email_setting_id,
        //                    team_id,
        //                    uuid
        //                    )
        //
        //                    VALUES $valuePlaceholder
        //                    ON CONFLICT DO NOTHING
        //
        //                    returning campaign_id
        //;
        //                 """
        //                .bind(valueParameters*)
        //                .map(_.long("campaign_id"))
        //                .list
        //                .apply().length
        //            }
        //
        //            CountOfMigration(count)
        //          }
        //
        //          MigrationRecursiveService.recursiveMigrationMethod(
        //            getIdsToMigrateMethod = getCampaignIdsForUpdate,
        //            updateTheIds = insertInCampaignEmailSetting,
        //            total_updated = CountOfMigration(0),
        //            ids_to_update_from = IdsToUpdate(0),
        //            count_per_cycle = CountOfMigration(5000)
        //          ) match {
        //            case Right(value) => logger.info(s"Migration DONE!!!!")
        //            case Left(value) =>
        //              value match {
        //                case RecursiveMigrationMethodError.GetIdsForMigrationError(error) => logger.fatal(s"Failed to get the ids", error)
        //                case RecursiveMigrationMethodError.UpdateTheIdsError(error) => logger.fatal(s"Failed updated the ids", error)
        //              }
        //          }


        case "TestBlacklistCheckCron" =>

          object BlacklistCheckCronTest
            extends TestAppTrait {
            def init(): Unit = {
              val logRequestId = "BlacklistCheckCronTest"
              given Logger: SRLogger = new SRLogger(logRequestId)
              srBlackListCheckCron.executeCron(Logger)
            }
          }
          BlacklistCheckCronTest.init()
        //
        //        case "testingProspectEventsV2" =>
        //          implicit lazy val system = ActorSystem()
        //          implicit lazy val actorContext: ExecutionContext = system.dispatcher
        //          object TestProspectEventsV2
        //          extends ProspectEventDAO_DI
        //          with ProspectAccountDAO1DI
        //          with FreeEmailDomainListService_DI
        //          with FreeEmailDomainListDAO_DI
        //          with ProspectDAOService_DI
        //          with ProspectQueryDI
        //          with ProspectColumnDef_DI
        //          with ProspectDAO_DI
        //          with MergeTagService_DI
        //          with EmailThreadDAO_DI
        //          with DBUtils_DI
        //          with CacheServiceJedisDI
        //          with CampaignProspectDAO_2DI
        //          with ProspectColumnDefDAO_DI
        //          with ReplySentimentService_DI
        //          with CampaignProspectDAO_DI
        //          with ReplySentimentDAOService_DI
        //          with TaskDaoServiceDI
        //          with ProspectServiceV2_DI
        //          with SrDBQueryCounterService_DI
        //          with ReplySentimentDAO_DI
        //          with ReplySentimentJedisDAO_DI
        //          with AccountDAO_DI
        //          with ProspectAddEventDAO_DI
        //          with SrEventServiceDI
        //          with TaskCacheDAO_DI
        //          with TaskDAO_DI
        //          with ProspectsEmailsDAO_DI
        //          with EmailValidationModelDI
        //          with DBCounterDAO_DI
        //          with KafkaServiceDI
        //          with TaskCacheService_DI
        //          with ScyllaRunSafely_DI
        //          with ScyllaCluster_DI
        //          with ScyllaDbConnection_DI
        //          {
        //            def init():Unit = {
        //              val logRequestId = "ProspectEventsV2Test"
        //              given Logger = new SRLogger(logRequestId)
        //              prospectEventDAO.getEventsV2(
        //                prospectId = None,
        //                prospectAccountId = Some(53027L),
        //                repTrackingHosts = Seq(),
        //                prospectObjectIfAlreadyThere = None,
        //                prospectAccountObjectIfAlreadyThere =None ,
        //                includeMessageEventForEmailThreadId =None ,
        //                teamId = 2L,
        //                account =None,
        //                Logger = Logger
        //              )
        //            }
        //
        //
        //          }
        //          TestProspectEventsV2.init()

        case "createGeneralChannelSettings" =>
          object main extends TestAppTrait {
            def init() = {

              val dbAndSession = dbUtils.startLocalTx()
              val db = dbAndSession.db
              val session = dbAndSession.session

              val res = accountDAO.findAccountsThatDoNotHaveGeneralChannelSettings()
                .flatMap(accountAndTeamIdList => {
                  println(s"\n\naccountAndTeamIdList => $accountAndTeamIdList\n\n")
                  Try {
                    accountAndTeamIdList.map(
                      accountIdAndTeamId => {

                        val uuid = srUuidUtils.generateGeneralChannelSettingUuid()

                        generalSettingDAO.addGeneralSetting(
                          teamId = accountIdAndTeamId.teamId.id,
                          uuid = uuid,
                          ownerAccountId = accountIdAndTeamId.accountId.id,
                          quota_per_day = AppConfig.generalTaskQuotaPerDay,
                          min_delay_seconds = AppConfig.generalTaskMinDelayInSeconds
                        )(session)
                      }
                        .get
                    )
                  }
                })

              dbUtils.commitAndCloseSession(db)

              res

            }
          }

          main.init() match {
            case Failure(e) =>
              println(e.toString)
            case Success(value) =>
              value.sum
          }

        case "refactorLinkedinUrls" =>
          object main extends TestAppTrait {
            def init() = {
              prospectDAO.fetchTeamsToUpdateProspectsLinkedinUrlForMigration()
                .map(teamIdList => {
                  teamIdList.map(teamId => {
                    mqRefactorLinkedinUrl.publish(msg = teamId) match {
                      case Failure(e) =>
                        logger.error(s"Could not publish $teamId to MqRefactorLinkedinUrl", e)

                      case Success(_) =>
                        logger.info(s"Published $teamId to MqRefactorLinkedinUrl")
                    }
                  })
                })
            }
          }

          main.init() match {
            case Failure(e) =>
              println(s"Error while fetching Teams :: ${e.toString}")

            case Success(_) =>
              println(s"Publish Complete")
          }



   
        case "ChangeStatus" =>
        //          val newTask = NewTask(
        //            task_type = TaskType.SendEmail,
        //            is_auto_task =  false,
        //            task_data = TaskData.SendEmailData(
        //              subject = "this is subject ",
        //              body = "this is body"
        //            ),
        //            status = TaskStatus.Due(
        //              due_at = DateTime.now()
        //            ),
        //            assignee_id = Some(1),
        //            prospect_id = Some(1),
        //            priority = TaskPriority.Urgent,
        //            note = "hello",
        //          )
        //          implicit val ec = Helpers.genFixedThreadPoolEC(threads = 10)
        //          val accountDAO = new AccountDAO
        //          val taskDao = new TaskPgDAO(accountDao = accountDAO)
        //          Await.result(
        //            taskDao.changeStatus(
        //              task_id = "",
        //              task_status = TaskStatus.Skipped(
        //
        //              )
        //            ).map{
        //              created_task =>
        //                logger.info(s"\n\n\n Created new task in db-> ${created_task}\n\n\n")
        //            }.recover{
        //              case e =>
        //                logger.error(s"\n\n\n Error While created new task in db ${e.getMessage}\n\n\n")
        //            }
        //            ,
        //            20.seconds
        //          )
        /*
                case "new_insertTrackedRepliesV3" =>
                  val emailReplyStatus = EmailReplyStatus(
                    replyType = EmailReplyType.ALL,
                    isReplied = false,
                    isBounced = false,
                    isUnsubscribeRequest = false,
                    isAutoReply = false,
                    isOutOfOfficeReply = false,
                    isInvalidEmail = false,
                    isForwarded = false,
                    bounceType = None,
                    bounceReason = None
                  )

                  /*
                  val erivp1 =  ERIntermediateValidProspect(
                                                          prospect_id: Long,
                                                          email: String,
                                                          email_domain: String,
                                                          account_id: Long, //prospect owner id
                                                          team_id: Long,
                                                          ta_id: Long,
                                                          prospect_account_id: Option[Long], //prospect_acccount_id => table -> prospect_accounts.id
                                                          prospect_category_id_custom: Long
                                                        )

                   */
                  val team_id = 1L
                  val account_id = 1L
                  val to_email1 = "<EMAIL>"
                  val prospect_id_in_campaign = 3L
                  val prospect_category_id_custom = 358L
                  val ta_id = 1L
                  val erIVP1 =  ERIntermediateValidProspect(
                    prospect_id = prospect_id_in_campaign,
                    email = to_email1,
                    email_domain= "example.com",
                    account_id = account_id, //prospect owner id
                    team_id= team_id,
                    ta_id = ta_id,
                    prospect_account_id= None, //prospect_acccount_id => table -> prospect_accounts.id
                    prospect_category_id_custom = prospect_category_id_custom
                  )
                  val emt1 =  EmailMessageTracked(
                    inbox_email_setting_id = 1,
                    from = IEmailAddress(email = "<EMAIL>"),
                    to_emails = Seq(IEmailAddress(email = to_email1)),
                    subject = "SomeSubject",
                    body = "SomeBody",
                    base_body = "SomeBaseBody",
                    text_body = "SomeTextBody",
                    references_header = None,
                    campaign_id = Some(1),
                    step_id = Some(1),
                    prospect_id_in_campaign = Some(prospect_id_in_campaign),
                    prospect_account_id_in_campaign = None,
                    campaign_name = Some("New Campaign 0183"),
                    step_name = Some("Day 1: Opening"),
                    received_at = DateTime.now().minusDays(2),
                    recorded_at = DateTime.now().minusDays(2),
                    sr_inbox_read = true,
                    original_inbox_folder = None,
                    email_status = emailReplyStatus,
                    message_id = "some_message_id1",
                    full_headers = Json.obj(),
                    scheduled_manually = false,
                    reply_to = None,
                    email_thread_id = Some(13),
                    gmail_msg_id = None,
                    gmail_thread_id = None,
                    outlook_msg_id = None,
                    outlook_conversation_id = None,
                    outlook_response_json = None,
                    cc_emails = Seq(),
                    in_reply_to_header = None,
                    team_id = team_id,
                    account_id = account_id,
                    internal_tracking_note = InternalTrackingNote.EXISTING_INREPLYTO,
                    tempThreadId = Some(7)
                  )

                  val newEmailThreadV3_1 = NewEmailThreadV3(
                                               temp_thread_id = 1,
                                               owner_id = 1,
                                               team_id = team_id,
                                               campaign_id = Some(1),
                                               campaign_name=  Some("New Campaign 0183"),
                                               prospect_ids =  Seq[Long](1,2),
                                               inbox_email = "<EMAIL>",
                                               inbox_email_settings_id = 1,
                                               subject = "Hello, open for a posting",
                                               latest_email_id =  Some(1),
                                               gmail_msg_id = None,
                                               gmail_thread_id = None,
                                               outlook_msg_id = None,
                                               outlook_conversation_id = None,
                                               internal_tracking_note =  InternalTrackingNote.NONE,
                                               // - computed from the no of prospect ids > 0
                                               has_prospect = false
                                             )

                  val logTraceId = s"${StringUtils.genLogTraceId}_th_${Thread.currentThread().getName}"
                  given logger1 = new SRLogger(
                    logRequestId = s"TestApp. logRequestId: ",
                    logTraceId = Some(logTraceId)
                  )

                  object Test extends EmailReplyTrackingModelDI
                    with ProspectServiceV2_DI
                    with ProspectDAO_DI
                    with ProspectColumnDef_DI
                    with ProspectAddEventDAO_DI
                    with ProspectQueryDI
                    with SrDBQueryCounterService_DI
                    with DBCounterDAO_DI
                    with ProspectAccountDAO1DI
                    with FreeEmailDomainListService_DI
                    with ProspectsEmailsDAO_DI
                    with CacheServiceJedisDI
                    with FreeEmailDomainListDAO_DI
                    with EmailScheduledDAO_DI
                    with CampaignProspectDAO_DI
                    with EmailThreadDAO_DI
                    with AccountDAO_DI
                    with EmailSettingDAO_DI
                    with EmailMessageContactModel_DI {
                      def init() {
                        logger.info("new_insertTrackedRepliesV3")
                        val srLogger = new SRLogger(logRequestId ="new_insertTrackedRepliesV3",
                          logTraceId = logger1.logTraceId)

                        //Todo: this should changed to emailReplyTrackingModelV2.insertTrackedRepliesV3
                        emailReplyTrackingModel._insertTrackedRepliesV3(
                          // Seq[(EmailMessageTracked, List[ERIntermediateValidProspect])]
                          newMsgsWithEmailThreadId = List( (emt1, List(erIVP1)) ),
                          sentManuallyFromSRInbox = false,
                          Logger = logger1
                        )
                        val aetp1 = AssociateEmailThreadProspect(
                          emailThreadId = 2 ,
                          prospectId = 3,
                          temp_thread_id = Some(1)
                        )
                        val aetp2 = AssociateEmailThreadProspect(
                          emailThreadId = 2 ,
                          prospectId = 1,
                          temp_thread_id = None
                        )
                        val aetp3 = AssociateEmailThreadProspect(
                          emailThreadId = 2 ,
                          prospectId = 2,
                          temp_thread_id = Some(-2)
                        )
                        val threadProspects: Seq[AssociateEmailThreadProspect] = List(
                          aetp1, aetp2, aetp3
                        )
                        emailThreadDAO._associateEmailThreadsAndProspectsV3(
                          logger = logger1,
                          threadProspects = threadProspects
                        )
                        emailThreadDAO._insertNewThreadsV3(
                          threads = Seq(newEmailThreadV3_1),
                          adminReplyFromSRInbox = false,
                          SRLogger = logger1
                        )
                      }
                  }
                  Test.init()
        */
        //          val srDBQueryCounterService: SrDBQueryCounterService = new SrDBQueryCounterService()
        //          val emailThreadDAO: EmailThreadDAO = new EmailThreadDAO
        //          val emailSettingDAO: EmailSettingDAO = new EmailSettingDAO()
        //              emailThreadDAO: EmailThreadDAO = new E
        //      accountDAO: AccountDAO,
        //      prospectAddEventDAO: ProspectAddEventDAO,
        //      campaignProspectDAO: CampaignProspectDAO,
        //      prospectDAO: Prospect
        //
        //          val emailScheduledDAO: EmailScheduledDAO
        //          val prospectServiceV2: ProspectServiceV2
        //          val emailReplyTrackingModel = new EmailReplyTrackingModel(
        //            emailThreadDAO = emailThreadDAO, emailScheduledDAO = emailScheduledDAO,
        //            prospectServiceV2 = prospectServiceV2
        //          )


        // sbt "coldemail/runMain utils.TestApp addPhantomBusterApiKey"
        case "addPhantomBusterApiKey" =>

          print("Enter the new API Key: ")
          val apiKey = scala.io.StdIn.readLine()

          print("\nEnter PhantomBuster Account Login Email: ")
          val email = scala.io.StdIn.readLine()

          print("\nEnter the execution time per month in hours: ")
          val executionTime = scala.io.StdIn.readInt()

          print("\nEnter the Month reset date: ")
          val monthResetDate = scala.io.StdIn.readLine()

          Try {
            DB autoCommit { implicit session =>
              sql"""
                 INSERT INTO phantombuster_api_keys(
                    api_key_enc,
                    phantombuster_login_email,
                    execution_time_per_month_in_hours,
                    execution_time_left_for_this_month_in_millis,
                    reset_execution_time_at
                 ) VALUES (
                    ${EncryptionHelpers.encryptPhantomBusterApiKey(apiKey)},
                    ${email},
                    ${executionTime},
                    ${executionTime * 60 * 60000},
                    $monthResetDate::date
                 )

                 RETURNING id;
                 """
                .map(rs => rs.int("id"))
                .single
                .apply()
            }
          } match {
            case Failure(e) =>
              println(s"Error while adding api key: ${e.toString}")

            case Success(id) =>
              println(s"Added api key to table with id: $id")
          }

        // sbt "coldemail/runMain utils.TestApp addPhantomBusterProxy"
        case "addPhantomBusterProxy" =>
          val logger: SRLogger = new SRLogger("TestApp :: addPhantomBusterProxy :: ")
          //          implicit val ec: ExecutionContext = Helpers.genFixedThreadPoolEC(threads = 1)

          println("\nEnter zone name: ")
          val zone = scala.io.StdIn.readLine()

          println("\nEnter proxy address: ")
          val ipAddress = scala.io.StdIn.readLine()

          println("\nEnter Username: ")
          val username = scala.io.StdIn.readLine()

          println("\nEnter Password: ")
          val password = scala.io.StdIn.readLine()

          val allCountryNames = TimezoneData.countryCodeToName.values.toList.sorted
          var i = 1
          allCountryNames.foreach(country => {
            println(s"$i. $country")
            i += 1
          })
          println("Select the proxy country from above list")

          val countryChoice = scala.io.StdIn.readInt()

          object main extends TestAppTrait {
            def init() = {
              phantomBusterProxyService.addPhantomBusterProxyAndWhitelistDomains(
                zone = zone,
                ipAddress = ipAddress,
                username = username,
                password = password,
                country = allCountryNames(countryChoice - 1)
              )
            }
          }

          Await.result(
            main.init(),
            30000.millis
          )



        /*
        // sbt "coldemail/runMain utils.TestApp createLinkedinSession"
      case "createLinkedinSession" =>
        implicit val ec: ExecutionContextExecutor = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(20))
        given Logger: SRLogger = new SRLogger("TestApp: createLinkedinSession")

        object CreateLinkedinSession extends TestAppTrait {
          def init() = {
            val res = linkedinSessionCreator.createSessionAndGetCookie(
              username = "<LINKEDIN_USERNAME>",
              password = "<LINKEDIN_PASSWORD>",
              proxyIPAndCredentials = ProxyIPAndCredentials(
                ip_address = "<PROXY_HOST>",
                username = Some("<PROXY_USERNAME>"),
                password = Some("<PROXY_PASSWORD>")
              )
            )

            val cookie = Await.result(res, 300000.millis)

            Logger.info(s"Linkedin Session Cookie -> $cookie")
          }
        }

        CreateLinkedinSession.init()
      */

 



        case "getTeamsForNotAllSavedMQ" =>
          val teamsDAO = new TeamsDAO
          teamsDAO.getTeamsOrderByOrgPlanType match {
            case Success(value) => logger.info(s"SUCCESS $value")
            case Failure(exception) => logger.error(s"Failed $exception")
          }
        case "checkIfTeamIsActive" =>
          val teamsDAO = new TeamsDAO
          teamsDAO.checkIfTeamIsDeleted(TeamId(2)) match {
            case Success(value) => logger.info(s"SUCCESS $value")
            case Failure(exception) => logger.error(s"Failed $exception")
          }


        //          sbt "coldemail/runMain utils.TestApp add_uuid_for_tables"

        //    java -Djavax.net.ssl.trustStore=/home/<USER>/scylla_client_certs/java-cacerts -Djavax.net.ssl.trustStorePassword=changeit -Dconfig.file=/home/<USER>/srbuild/resources/production.conf -Dlogger.file=/home/<USER>/srbuild/resources/production-logback.xml -cp /home/<USER>/srbuild/coldemail.jar utils.TestApp add_uuid_for_tables

 

        case "get_inconsistent_data" =>

          object main extends TestAppTrait {
            def init(): Try[Unit] = Try {

              implicit val sr_logger: SRLogger = new SRLogger("get_inconsistent_data")

              val res = campaignDAO.getCampaignIdWhichHaveInConsistentData

              res match {

                case Failure(e) =>

                  println(s" error while getting inconsistent campaign ${e.printStackTrace()}")

                case Success(value) =>

                  println(s"Succsss :: ${value.toString}")

              }

            }

          }

          main.init()

        case "delete_duplicate_tasks_and_perform_actions" =>

          object main extends TestAppTrait {
            def init(): Try[Unit] = Try {

              given sr_logger: SRLogger = new SRLogger("DuplicateTasks")

              val res = taskService.deleteDuplicateTasks()

              res match {

                case Failure(e) =>

                  sr_logger.error(s" error while deletingDuplicateTasks", e)

                case Success(d) =>

                  d.foreach(pair => {

                    sr_logger.info(s"group: ${pair._1}  : list of tasks_id that got deleted : ${pair._2}")

                  })

              }

            }

          }

          main.init() match {

            case Failure(e) =>

              logger.error(s" error while doing this", e)

            case Success(d) =>

              logger.info(s"success while deleting duplicates : ${d}")


          }
        case "AddCallReplySentimentsInDB" =>

          object main extends TestAppTrait {

            def init() = {

              val dbAndSession = dbUtils.startLocalTx()
              val db = dbAndSession.db
              val session = dbAndSession.session

             given Logger : SRLogger = new SRLogger("defaultReplySentimentTypes additin from testapp")

              val teamId = 15

              TeamInboxService.defaultReplySentimentTypes(
                prospect_categories = Seq(),
                newReplySentimentsEnabled = true
                // Use correct OrgId here
              ).map { replySentimentType =>
                replySentimentDAOService.addReplySentimentsForATeam(
                  team_id = teamId,
                  replySentimentType = replySentimentType,
                  uuid = srUuidUtils.generateReplySentimentUuid()
                )(session)

              }
              dbUtils.commitAndCloseSession(db = db)


            }

          }

          val result = main.init()
          println(s"result $result")



        case "jedisTest" =>
          given logger: SRLogger = new SRLogger("jedisTest :: ")

          object main extends TestAppTrait {
            def init() = {
              cacheServiceJedis.pipelinedSadd(
                key = "key1",
                values = Set("value1", "value2", "value3", "value4", "value5", "value6", "value7", "value8",
                  "value9", "value10", "value11", "value12", "value13", "value14", "value15", "value16"
                )
              )
            }
          }

          val res = main.init()
          res match {

            case Failure(err) =>
              println(s"Testapp error jedisTest : ${err}")

            case Success(_) =>

              println(s"Testapp jedisTest success")

          }



 

      }
    } else {
      logger.error("Application name not provided")
    }
  }
}
