package utils.testapp

import api.campaigns.services.CampaignId
import play.api.Logging
import play.api.libs.json.Json
import scalikejdbc.config.DBs
import utils.SRLogger
import utils.emailvalidation.models.EmailValidationToolV2.{BOUNCER, DEBOUNCE}
import utils.emailvalidation.models.{EmailValidationInitiator, EmailValidationPriority, InternalInvalidIEmailAddressReasons}
import utils.emailvalidation.{EmailValidationResultWithAnalysisId, SREmailValidityStatus}
import utils.testapp.AllBlacklistDAO.{emailValidationDAOService, emailValidationModel}

import scala.util.{Failure, Success}


object Test_EmailValidationModel extends Logging{

  def main(args:Array[String]):Unit={
    if(args.length>0){
      DBs.setupAll()

      val doerAccountId=30
      val teamId=12
      val initiatorCampaignId=50
      val validationResult=EmailValidationResultWithAnalysisId(
        emailDeliveryAnalysisId=None,
        email="<EMAIL>",
        fullJson = Json.obj(),
        isValid = true,
        srEmailValidityStatus = SREmailValidityStatus.SR_INFERRED_UNDELIVERABLE,
        internal_sr_validation_fail_reason = Some(InternalInvalidIEmailAddressReasons.FailedIEmailAddressParse)
      )

      val applicationName=args(0);

      given logger: SRLogger = new SRLogger("test_EmailValidationModel")

      applicationName match{
        case "test_addEmailValidationResponse"=>

          logger.info("test_addEmailValidationResponse started")
          val result=emailValidationModel.addEmailValidationResponse(validationResult,BOUNCER,doerAccountId,teamId,initiatorCampaignId)

          result match{
            case Failure(exception)=>
              println(exception.getMessage)
            case Success(value)=>
              println(value)
          }

          logger.info("test_addEmailValidationResponse ended")


        case "test_addForBatchRequest"=>

          logger.info("test_addForBatchRequest started")

          val emails=Seq("<EMAIL>","<EMAIL>")
          val low=EmailValidationPriority.Low

          val result = emailValidationModel.addForBatchRequest(
            emails = emails,
            doerAccountId = doerAccountId,
            teamId = teamId,
            priority = low,
            initiatorWithValue = EmailValidationInitiator.InitiatedByCampaign(
              initiatorCampaignId = CampaignId(id = initiatorCampaignId)
            )
          )

          result match{
            case Failure(exception)=>
              println(exception.getMessage)
            case Success(value)=>
              println(value)
          }

          logger.info("test_addForBatchRequest ended")

        case "test_updateEmailValidationResultsFromBatchRequest"=>



          logger.info("test_updateEmailValidationResultsFromBatchRequest started")

          val validationResult2=EmailValidationResultWithAnalysisId(
            emailDeliveryAnalysisId=None,
            email="<EMAIL>",
            fullJson = Json.obj(),
            isValid = true,
            srEmailValidityStatus = SREmailValidityStatus.DELIVERABLE,
            internal_sr_validation_fail_reason = None
          )


          val result=emailValidationModel.updateEmailValidationResultsFromBatchRequest(Seq(validationResult,validationResult2),DEBOUNCE)

          result match {
            case Failure(exception)=>
              println(s"${exception.getMessage}")

            case Success(value)=>
              println(value)
          }
          logger.info("test_updateEmailValidationResultsFromBatchRequest ended")

        case "test_findEmailForMakingBatchRequest"=>



        logger.info("test_findEmailForMakingBatchRequest started")


        val result =emailValidationModel.findEmailsForMakingBatchRequest(true)

        result match{
         case Failure(exception)=>
          println(s"${exception.getMessage}")
         case Success(value)=>
          println(value)

          logger.info("test_findEmailForMakingBatchRequest ended")
        }

        case "test_getEmailsToChangeStatusToToBeQueued"=>
            logger.info("test_getEmailsToChangeStatusToToBeQueued started")

            val result=emailValidationModel.getEmailsToChangeStatusToToBeQueued(DEBOUNCE)

            result match {
                case Failure(exception)=>
                    println(s"${exception.getMessage}")

                case Success(value)=>
                    println(value)
            }
            logger.info("test_getEmailsToChangeStatusToToBeQueued ended")


        case "test_updateProcessStatus"=>

          logger.info("test_updateProcessStatus started")

          val emailList=List()

          val result=emailValidationModel.updateProcessStatusTo_ToBeQueued(emailList)

          result match{
            case Failure(exception)=>
              println(s"${exception.getMessage}")

            case Success(value)=>
              println(s"${value}")
          }
          logger.info("test_updateProcessStatus ended")

        case "test_switchStuckEmailsToBackupVerificationTool"=>


          logger.info("test_switchStuckEmailsToBackupVerificationTool started")


            val emailList=List("<EMAIL>","<EMAIL>")


            val result=emailValidationModel.switchStuckEmailsToBackupVerificationTool(emailList,DEBOUNCE)

          result match{
            case Failure(exception)=>
              println(s"${exception.getMessage}")

            case Success(value)=>
              println(value)
          }
          logger.info("test_updateToolifDown ended")

        case "test_getLowPriorityEmailsForToolAssignment"=>


          logger.info("test_getLowPriorityEmailsForToolAssignment started")
          val result=emailValidationModel.getLowPriorityEmailsForToolAssignment()

          result match{
            case Failure(exception)=>
              println(s"${exception.getMessage}")

            case Success(value)=>
              println(value)
          }
          logger.info("test_getLowPriorityEmailsForToolAssignment ended")

        case "test_getHighandMediumPriorityEmailsForToolAssignment"=>

          logger.info("test_getHighandMediumPriorityEmailsForToolAssignment started")

          val result=emailValidationModel
          .getHighAndMediumPriorityEmailsForToolAssignment()

          result match{
            case Failure(exception)=>
              println(s"${exception.getMessage}")

            case Success(value)=>
              println(value)
          }

          logger.info("test_getHighandMediumPriorityEmailsForToolAssignment ended")

        case "test_updateTool"=>

          logger.info("test_updateTool started")

          val emailList=List("<EMAIL>","<EMAIL>")
          val result=emailValidationModel.assignToolForValidation(emailList,BOUNCER)

          result match{
            case Failure(exception)=>
              println(s"${exception.getMessage}")

            case Success(value)=>
              println(value)
          }

          logger.info("test_updateTool ended")

        case "test_getEmailsToSwitchToBackupVerificationTool"=>

            logger.info("test_getEmailsToSwitchToBackupVerificationTool started")

            val result=emailValidationModel.getEmailsToSwitchToBackupVerificationTool(BOUNCER)

            result match{
                case Failure(exception)=>
                    println(s"${exception.getMessage}")
                case Success(value)=>
                    println(value)
            }

              logger.info("test_getEmailsToSwitchToBackupVerificationTool ended")


        case "test_changingValidationStatusTo_ToBeQueued"=>
            logger.info("test_changingValidationStatusTo_ToBeQueued started")

            val result=emailValidationDAOService.changingValidationStatusTo_ToBeQueued(DEBOUNCE)

            result match {
                case Failure(exception)=>
                    println(s"${exception.getMessage}")
                case Success(value)=>
                    println(value)
            }
              logger.info("test_changingValidationStatusTo_ToBeQueued ended")

        case "test_switchingValidationTooltoBackupTool" =>
            logger.info("test_switchingValidationTooltoBackupTool started")


            val result=emailValidationDAOService.switchingValidationTooltoBackupTool(DEBOUNCE,BOUNCER)


            result match {
                case Failure(exception)=>
                    println(s"${exception.getMessage}")

                case Success(value)=>
                    println(value)
            }
              logger.info("test_switchingValidationTooltoBackupTool ended")

        case "test_assigningToolForValidation"=>

              logger.info("test_assigningToolForValidation started")

                val emailList=emailValidationModel.getHighAndMediumPriorityEmailsForToolAssignment() match {
                    case Success(value)=>
                        value
                    case Failure(exception)=>
                            throw exception
                }


              val result=emailValidationDAOService.assigningToolForValidation(emailList,BOUNCER)

              result match {
                  case Failure(exception)=>
                      println(s"${exception.getMessage}")
                  case Success(value)=>
                        println(value)
              }
              logger.info("test_assigningToolForValidation ended")


        case "test_findEmailsForMakingBatchRequest"=>

              logger.info("test_findEmailsForMakingBatchRequest started")

              val result=emailValidationModel.findEmailsForMakingBatchRequest(true)

              result match {
                  case Failure(exception) =>
                      println(s"${exception.getMessage}")
                  case Success(value) =>
                      println(value)
              }
              logger.info("test_findEmailsForMakingBatchRequest ended")









      }

    }
  }

}
