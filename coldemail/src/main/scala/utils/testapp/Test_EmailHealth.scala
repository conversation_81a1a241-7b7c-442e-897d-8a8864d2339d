package utils.testapp

import api.emails.EmailSetting
import io.smartreach.esp.api.emails.IEmailAddress
import org.joda.time.DateTime
import scalikejdbc.config.DBs
import utils.email.EmailSendDetail
import utils.email.services.EmailReplyTrackingService
import utils.{CommandExecutor, Helpers, SRLogger}

import scala.concurrent.duration.Duration
import scala.concurrent.{Await, Future}
import scala.util.{Failure, Success}

object Test_EmailHealth extends TestAppTrait {

  def getDkimRecord(domain: String, selector: String): Future[String] = {

    val command =
      s"""
         |dig +short $selector._domainkey.$domain TXT
         |""".stripMargin

    CommandExecutor.runCommandInBash(command).map { res =>

      if (res.error == "" && res.exitValue == 0) {

        res.output

      } else {

        println(s"ERROR ${res.error}, ${res.exitValue}")

        ""

      }

    }

  }


  def main(args: Array[String]): Unit = {

    if (args.length > 0) {

      DBs.setupAll()

      given Logger: SRLogger = new SRLogger(logRequestId = "Test_AWSBedrock")

      val applicationName = args(0)

      applicationName match {

        case "validate_dkim_record" =>

          val x =
            """
              |\"v=DKIM1; k=rsa; p=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCQsiTqfeaG+qZamBpZCUoKhbRuLdvR9l2qFdLX79C4UODkpOKhjqlAvSaup48ddD5A1SkDKegvokK5lLZ8BK8FDTDt+RYy7gXBzrw2yu3eHWUalzd5av0eB/AcUK6x1AVea0zRHWcwcciHNhEUMD2gLGhQvc+ETG1nXSfC4xKMsQIDAQAB\"\n
              |"""".stripMargin


          val dkimPubKey = "\\bp=([a-zA-Z0-9+]+/)".r

          println(dkimPubKey.findFirstMatchIn(x))


        case "test_health_check" =>

          val subject = "SmartReach.io SPF DKIM DMARC Check"

          val emailTextBody = "This is to check SPF DKIM DMARC"


          val emailsSetting: EmailSetting = emailSettingDAO.find(id = 60).get

          val name = s"${emailsSetting.first_name} ${emailsSetting.last_name}"

          val emailSendDetail = EmailSendDetail(
            id = 0,
            org_id = 0,
            account_id = 0,
            team_id = 0,

            sendEmailFromCampaignDetails = None,
            prospect_id = None,

            subject = subject,
            body = "",
            text_body = emailTextBody,

            scheduled_from_campaign = false,
            sender_email_settings_id = emailsSetting.id.get.emailSettingId,
            sender_message_id_suffix = emailsSetting.message_id_suffix,

            receiving_email_settings_id = 0,

            service_provider = emailsSetting.service_provider,
            via_gmail_smtp = emailsSetting.via_gmail_smtp, // check org also

            smtp_username = emailsSetting.smtp_username,
            smtp_host = emailsSetting.smtp_host,
            smtp_port = emailsSetting.smtp_port,
            smtp_password = emailsSetting.smtp_password,

            oauth2_refresh_token = emailsSetting.oauth2_refresh_token,
            oauth2_access_token = emailsSetting.oauth2_access_token,
            oauth2_access_token_expires_at = emailsSetting.oauth2_access_token_expires_at,

            email_domain = emailsSetting.email_domain,

            api_key = emailsSetting.api_key,

            mailgun_region = emailsSetting.mailgun_region,

            from_email = emailsSetting.email,
            from_name = name,
            to_emails = Seq(
              IEmailAddress(
                name = Some("Neil Prakash"),
                email = "<EMAIL>"
              )
            ),

            reply_to_email = None,
            reply_to_name = None,

            cc_emails = Seq(),
            bcc_emails = Seq(),

            in_reply_to_id = None,
            in_reply_to_references_header = None,
            in_reply_to_sent_at = None,
            in_reply_to_subject = None,
            in_reply_to_outlook_msg_id = None,
            sender_email_setting_paused_till = None,
            custom_tracking_domain = None,
            rep_tracking_host_id = 0,

            gmail_fbl = None,
            list_unsubscribe_header = None,
            email_thread_id = None,
            send_plain_text_email = Some(true), // TODO check
            gmail_thread_id = None,

          )

          emailSenderService.sendEmailToProspect(
            rep_smtp_reverse_dns_host = None,
            data = emailSendDetail,
          )

        case "test_receive" =>

          val emailsSetting: EmailSetting = emailSettingDAO.find(id = 60).get

          val emailServiceV: EmailReplyTrackingService = emailService.getEmailReplyTrackingService(
            emailSetting = emailsSetting
          )

          val f = emailServiceV.receiveEmail(
            receiverEmailSetting = emailsSetting,
            fromDate = DateTime.now().minusHours(20),
            tillDate = None,
            Logger = Logger,
            senderEmailSettings = Seq(),
          )

          Await.ready(f, Duration.Inf).value match {

            case None =>

              println("Receive Email None found.")

            case Some(Failure(exception)) =>

              println(s"Failed to receive email - $exception")

            case Some(Success(messages)) =>

              val pattern = "\\bs=([a-zA-Z0-9_-]+)".r

              println(
                messages
                  .filter(_.subject == "SmartReach.io SPF DKIM DMARC Check Test")
                  .map(_.original_inbox_folder)
              )

              val i = messages
                .filter(_.subject == "SmartReach.io SPF DKIM DMARC Check Test")
                // .distinctBy(_.message_id)
                .map(x => (x.full_headers \ "DKIM-Signature").as[String])
                .map(s => pattern.findFirstMatchIn(s))

              println(s"TOTAL LEN - ${i.length}")

              i.foreach {

                case None =>

                  println("Selector not found in the email headers.")

                case Some(regexMatch) =>

                  // ex. "s=google".drop(2) => "google"
                  val selector = regexMatch.toString.drop(2)

                  val domain = "coppermail.in" // TODO: this will come from the email setting.

                  val fx = getDkimRecord(domain = domain, selector = selector)

                  Await.ready(fx, Duration.Inf)

                  println(fx)

              }

          }

        case "exe_cmd" =>

          val command =
            """
              |di +short _dmarc.outlook.com TXT
              |""".stripMargin

          CommandExecutor.runCommandInBash(command).map { res =>

            if (res.error == "" && res.exitValue == 0) {

              println(res.output)

            } else {

              println(s"ERROR ${res.error}, ${res.exitValue}")


            }


          }

        case "test_digDKIM" =>

          val d1 = getDkimRecord(
            domain = "coppermail.in",
            selector = "google",
          )

          Await.ready(d1, Duration.Inf)

          val d2 = Helpers.digDKIM(
            domain = "coppermail.in",
            selector = "google",
          )

          println(d1)
          println(d2)

      }

    }

  }

}
