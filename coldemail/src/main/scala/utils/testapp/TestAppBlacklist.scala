package utils.testapp

import org.apache.pekko.actor.ActorSystem
import org.apache.pekko.stream.Materializer
import play.api.Logging
import play.api.libs.ws.ahc.AhcWSClient
import scalikejdbc.config.DBs
import utils.SRLogger
import utils.proxy.brightdata.BrightDataApi

import scala.concurrent.duration.*
import scala.concurrent.{Await, ExecutionContext}

/*
This object holds any sort internal test function used to test features/changes manually during development.
 */
object TestAppBlacklist extends Logging {

  def main(args: Array[String]): Unit = {

    if (args.length > 0) {
      DBs.setupAll()


      val applicationName = args(0)

      applicationName match {


        case "removeIPFromBlacklist" =>

          implicit val system = ActorSystem()
          implicit val materializer = Materializer(system)
          implicit val wSClient: AhcWSClient = AhcWSClient()
          implicit val actorContext: ExecutionContext = system.dispatcher
          given logger: SRLogger = new SRLogger("TestApp : removeIPFromBlacklist ")

          val zone = "isp"
          val ip = "************"

          val result = new BrightDataApi().removeIPFromBlacklist(zone = zone, ip = ip)
            .map(res => {
              println(s"Removed -> $res")
            })
            .recover {
              case e =>
                logger.error("Error occured", e)
            }

          Await.result(result, 30000.millis)



      }
    }

  }
}
