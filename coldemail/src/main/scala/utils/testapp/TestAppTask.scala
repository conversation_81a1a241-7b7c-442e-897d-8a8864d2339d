package utils.testapp

import api.scheduler.model.SchedulerIntegrationTestUtils
import play.api.Logging
import play.api.libs.json.Json
import scalikejdbc.config.DBs
import utils.SRLogger
import utils.TestApp.logger
import utils.testapp.TestAppTrait
import utils.uuid.TableNames

import scala.concurrent.Await
import scala.util.{Failure, Success, Try}

/*
This object holds any sort internal test function used to test features/changes manually during development.
 */
object TestAppTask extends Logging {

  def main(args: Array[String]): Unit = {

    if (args.length > 0) {
      DBs.setupAll()


      val applicationName = args(0)

      applicationName match {


        case "updateLatestTaskDoneOrSkippedAt" =>
          given Logger: SRLogger = new SRLogger("TestApp updateLatestTaskDoneOrSkippedAt :: ")

          object main extends TestAppTrait {
            def init() = {
              migrationFunc.getTeamIdsInTable(
                  tableNames = TableNames.CampaignsProspects,
                  fetchAllTeams = true,
                  active_teams = true
                )
                .map(teamList => {
                  teamList.map(tid => {
                    mqUpdateLatestTaskDoneOrSkippedAt.publish(msg = tid) match {
                      case Failure(exception) => Logger.error(s"Error while publishing team: ${tid.id} to MqUpdateLatestTaskDoneOrSkippedAt", exception)
                      case Success(value) => Logger.info(s"Published team: ${tid.id} to MqUpdateLatestTaskDoneOrSkippedAt")
                    }
                  })
                })
            }
          }

          main.init()

/*
        case "duplicateTask_test" =>
          object Schedule extends TestAppTrait {
            val input_staging_2 = SchedulerIntegrationTestUtils.schedulerIntegrationTestFor_staging_2

            def init = multichannelTestUtil.createAndScheduleCampaignManualEmail(
              schedulerInputForIntegrationTest = input_staging_2
            )
          }
          val result = Schedule.init
          Await.result(result, scala.concurrent.duration.Duration.Inf)
*/



        /*case "taskParseIssue" =>
          val jsonString =
            """
              {"object_type":"task","task_id":"randomString","task_type":"send_email","task_data":{"subject":"Hi this is the subject","body":"hello, this is the body","task_type":"send_email"},"status":{"scheduled_at":"2022-07-20T14:43:48.501+05:30","status_type":"scheduled"},"assignee":{"id":1,"first_name":"Shashank","last_name":"Dwivedi"},"team_id":9,"prospect_id":1,"priority":"urgent","note":"This is Note","created_at":"2022-07-20T14:43:48.521+05:30","updated_at":"2022-07-20T14:43:48.521+05:30"}
              """

          val a = Try[]() {
            Json.parse(jsonString).validate[Task]
          }
          logger.info(
            s"a -> ${a}"
          )*/

      }
    }

  }
}
