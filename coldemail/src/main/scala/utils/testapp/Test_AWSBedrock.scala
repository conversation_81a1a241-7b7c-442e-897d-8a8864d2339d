package utils.testapp

import io.sr.llm.bedrock.services.GeneratedEmail
import scalikejdbc.config.DBs
import utils.SRLogger

import scala.concurrent.duration.Duration
import scala.concurrent.{Await, Future}
import scala.util.{Failure, Success}


object Test_AWSBedrock extends TestAppTrait {

  def main(args: Array[String]): Unit = {

    if (args.length > 0) {

      DBs.setupAll()

      given Logger: SRLogger = new SRLogger(logRequestId = "Test_AWSBedrock")

      val applicationName = args(0)

      applicationName match {

        case "test_bedrock" =>

          val generatedEmailFut: Future[GeneratedEmail] = srLLMService.generateEmailSubjectAndBody(
            emailPurpose = "Sales Outreach",
            senderPosition = "Sales Leader",
            typeOfEmail = "sales email",
            companyName = "Disney",
            year = 2012
          )

          Await.ready(generatedEmailFut, Duration.Inf).value match {

            case None =>

              println("generatedEmailFut - None Found.")

            case Some(Failure(exception)) =>

              println(exception)

            case Some(Success(generatedEmail)) =>

              println(s"Email Subject: ${generatedEmail.subject}")

              println()

              println("Email Body:")

              println(s"${generatedEmail.body}")

              val generatedReplyEmailFut: Future[GeneratedEmail] = srLLMService.generatePositiveReplyEmailBody(
                emailSubject = generatedEmail.subject,
              )

              Await.ready(generatedReplyEmailFut, Duration.Inf).value match {

                case None =>

                  println("generatedReplyEmailFut - None Found.")

                case Some(Failure(exception)) =>

                  println(exception)

                case Some(Success(generatedReplyEmail)) =>

                  println(s"Reply Email Subject: ${generatedReplyEmail.subject}")

                  println()

                  println("Reply Email Body:")

                  println(s"${generatedReplyEmail.body}")

              }

          }

      }

    }

  }

}
