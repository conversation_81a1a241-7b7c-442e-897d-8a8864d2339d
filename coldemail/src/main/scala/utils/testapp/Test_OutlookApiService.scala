package utils.testapp

import api.accounts.TeamId
import api.accounts.email.models.EmailServiceProvider
import api.accounts.models.OrgId
import api.emails.EmailToBeSent
import io.smartreach.esp.api.emails.{IEmailAddress, SREmailToBeSent}
import play.api.Logging
import scalikejdbc.config.DBs
import utils.{ISRLogger, SRLogger}
import utils.email.EmailSendDetail
import utils.mq.services.MQDoNotNackException

import scala.Console.err
import scala.concurrent.duration.Duration
import scala.concurrent.{Await, Future}
import scala.util.{Failure, Success}

object Test_OutlookApiService extends Logging {

  def main(args: Array[String]): Unit = {

    if (args.length > 0) {
      DBs.setupAll()

      val applicationName = args(0)

      applicationName match {

        case "test_outlook_email_sending" =>


          object Test extends TestAppTrait {

            logger.info(" Starting test_outlook_email_sending")


            def init() = {

              given Logger: SRLogger = new SRLogger(logRequestId = "Test_OutlookApiService")

              //              val id: Long = 234L
              //              val data = CampaignFollowUpSetting(append_followups = false)
              /*
              val team_id: TeamId = TeamId(id = 132)

              val em = EmailSendDetail(
                id = 123,
                org_id = 20,
                account_id = 123,
                team_id = 23,

                // primary_prospect_id: useful mostly while sending campaign emails
                // campaign can be None when mail is sent from admin.
                // Example: sendMailFromAdminFut in EmailNotificationService
                sendEmailFromCampaignDetails = None,
                prospect_id = None, // can be None in case of test emails.
                subject = "sfdsf",
                body = "sfadsf",
                text_body = "sfadsf",
                scheduled_from_campaign = true,

                sender_email_settings_id = 123,
                sender_message_id_suffix = " safdf",

                receiving_email_settings_id = 123,

                service_provider =  EmailServiceProvider.OUTLOOK_API,
                via_gmail_smtp = None,

                smtp_username = None,
                smtp_host  = None,
                smtp_port = None,
                smtp_password = None,

                oauth2_refresh_token   = None,
                oauth2_access_token = None,
                oauth2_access_token_expires_at = None,

                // for mailgun
                email_domain = None,
                api_key = None,
                mailgun_region = None,

                from_email = "<EMAIL>",
                from_name = "shashank",
                to_emails = Seq(IEmailAddress(
                  name  = Some("Shashank"),
                  email = "<EMAIL>"
                )),
                reply_to_email = None,
                reply_to_name  = None,

                cc_emails = Seq(),
                bcc_emails= Seq(),

                in_reply_to_id = None,
                in_reply_to_references_header= None,
                in_reply_to_sent_at= None,
                in_reply_to_subject= None,
                in_reply_to_outlook_msg_id= None,

                // append_followups: Boolean,
                // open_tracking_enabled: Boolean,
                // click_tracking_enabled: Boolean,

                // opt_out_msg: String,
                // opt_out_is_text: Boolean,
                // signature: Option[String],


                sender_email_setting_paused_till= None,

                custom_tracking_domain= None,
                // sr_tracking_host: String,
                rep_tracking_host_id = 1,
                // bulk_sender: Boolean,
                gmail_fbl = None,
                list_unsubscribe_header= None,


                email_thread_id= None,
                send_plain_text_email= None, //  = Some(false)
                gmail_thread_id= None
              )

              val ems =
                EmailToBeSent(to_emails = em.to_emails,
                from_email = em.from_email,
                cc_emails = em.cc_emails,
                bcc_emails = em.bcc_emails,
                from_name= em.from_name,
                reply_to_email = em.reply_to_email,
                reply_to_name= em.reply_to_name,
                subject =  em.subject,
                textBody =  em.text_body,
                htmlBody = em.body,
                // baseBody: String,
                message_id= None,
                references_header = None,

                in_reply_to_id = em.in_reply_to_id,
                in_reply_to_references_header = em.in_reply_to_references_header,
                in_reply_to_sent_at = em.in_reply_to_sent_at,

                sender_email_settings_id = em.sender_email_settings_id,

                email_thread_id = em.email_thread_id,
                gmail_msg_id = None,
                gmail_thread_id =  em.gmail_thread_id,

                outlook_msg_id = None,
                outlook_conversation_id = None,
                outlook_response_json = None,

                gmail_fbl = None,
                list_unsubscribe_header = None,
                hasCustomTrackingDomain = false,
                rep_smtp_reverse_dns_host = None
              )

              */
              val email_scheduled_id = 1715706
              val teamId = TeamId(id = 610)
//              val email_scheduled_id = 1712875
//              val teamId = TeamId(id = 2)

              emailSenderService.getScheduleDetailsForSending(email_scheduled_id, teamId = teamId) match {
                case Failure(err1) =>
                  Logger.fatal(s"$logger", err = err1)

                  Future.failed(MQDoNotNackException(s"${Logger.logRequestId} [EmailService] processSendEmailRequest: emailScheduledId: ${email_scheduled_id} Server Error"))

                case Success(None) =>

                  Logger.fatal(s"$logger email_scheduled_id NOT_FOUND")

                  Future.failed(MQDoNotNackException(s"${Logger.logRequestId} [EmailService] processSendEmailRequest: emailScheduledId: ${email_scheduled_id} error: Not Found"))

                case Success(Some(data)) =>

                  emailSenderService.sendEmailToProspect(
                    data = data,
                    rep_smtp_reverse_dns_host = None
                  )(
                    playDefaultExecutionContext,
                    wSClient,
                    Logger,
                    workerActorSystem
                  ).map { data => {

                    print(s"data ${data}")

                  }}.recover(err => {

                    print(s"err ${err}")


                  })
              }
            }
          }
          logger.info("test_outlook_email_sending :: doing init")
          Await.result(Test.init(), Duration.Inf)
          logger.info("test_outlook_email_sending :: after init")

      }
    }

  }

}