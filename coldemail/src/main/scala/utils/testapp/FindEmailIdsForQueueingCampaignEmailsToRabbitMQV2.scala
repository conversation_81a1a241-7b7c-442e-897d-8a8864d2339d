package utils.testapp


import api.AppConfig
import org.joda.time.{DateTime, DateTimeZone}
import play.api.Logging
import scalikejdbc.config.DBs
import utils.SRLogger

object FindEmailIdsForQueueingCampaignEmailsToRabbitMQV2 extends Logging with TestAppTrait {

  def main(args: Array[String]): Unit = {

    if (args.length > 0) {
      DBs.setupAll()

      val applicationName = args(0)

      applicationName match {

        case "test_findEmailIdsForQueueingCampaignEmailsToRabbitMQV2" =>
          
          def init() = {
            val scheduleForNextSeconds = AppConfig.cronPushToRabbitMqIntervalInSeconds
            val dateTime: DateTime = DateTime.now(DateTimeZone.UTC).plusSeconds(scheduleForNextSeconds)
            given Logger: SRLogger= new SRLogger("nck")

//            val result1 = emailScheduledDAO.findEmailSettingsForQueueingCampaignEmailsToRabbitMQV2(toBeSentTill = dateTime, logger = Logger)
//            val result2 = emailScheduledDAO.findEmailIdsForQueueingCampaignEmailsToRabbitMQV2(emailSettingData = result1.get, toBeSentTill = dateTime, logger = Logger)
//            println(result2)
          }
          
          init()
      }
    }

  }

}