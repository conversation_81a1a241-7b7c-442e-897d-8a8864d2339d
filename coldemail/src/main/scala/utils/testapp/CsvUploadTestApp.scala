/* 17-jun-2025: [DO_NOT_REMOVE] [LEADFINDERUPLOAD] was slowing down compilation. Only needed for the Lead database upload.

package utils.testapp

import play.api.Logging
import play.api.libs.json.{JsObject, JsValue, Json}
import scalikejdbc.config.DBs
import utils.SRLogger
import utils.testapp.csv_upload.{ColumnMap4_1, ColumnMap4_2, ColumnMap4_3, CsvQueueCreateFormData}

import scala.util.{Failure, Random, Success, Try}

case class CsvDetailsForUploadIntoCsvQueue(
                                            file_url: String,
                                            column_map: JsValue
                                          )

object CsvUploadTestApp extends Logging with TestAppTrait {

  def main(args: Array[String]): Unit = {

    if (args.length > 0) {
      DBs.setupAll()

      val applicationName = args(0)

      applicationName match {
       case "upload_csv_details_lead_finder_queue" =>

         given logger: SRLogger = new SRLogger("test_upload_csv_details")

         object main {

           def init(
                     csvDetailsForUploadIntoCsvQueue: Seq[CsvDetailsForUploadIntoCsvQueue]
                   )(using logger: SRLogger)= Try{

             val csvQueueCreateFormData = csvDetailsForUploadIntoCsvQueue.map(data =>
               CsvQueueCreateFormData(
                 list_name = Some(Random.alphanumeric.take(5).mkString("")),
                 file_url = data.file_url,
                 column_map = data.column_map,
                 dt_src = "411"
               )
             )
             val res1 = leadFinderUploadService.uploadCsvDetails(
               accountId = 6939,
               teamId = 6412,
               ta_id = 20165,
               loggedIn_id = 6939,
               data = csvQueueCreateFormData
             )

             assert(res1.isSuccess);
           }

         }

         val column_map_1: JsObject = Json.obj(
           "City"-> "city",
           "Company Name"-> "company_name",
           "Contact Name"-> "contact_name",
           "Country"-> "country",
           "Email"-> "email",
           "Employee Size"-> "employee_size",
           "Employees"-> "employees",
           "First Name"-> "first_name",
           "Industry"-> "industry",
           "Last Name"-> "last_name",
           "Middle Name"-> "middle_name",
           "Phone"-> "phone",
           "Phone Number"-> "phone_number",
           "Revenue"-> "revenue",
           "Revenue (in $)"-> "revenue__in_u_",
           "State"-> "state",
           "Title"-> "title",
           "Website"-> "website",
         )

         val column_map_2: JsObject = Json.obj(
           "Birth Date" -> "birth_date",
           "Birth Year" -> "birth_year",
           "Countries" -> "countries",
           "Emails" -> "emails",
           "Full name" -> "full_name",
           "Gender" -> "gender",
           "LinkedIn Url" -> "linkedin_url",
           "Locality" -> "locality",
           "Location" -> "location",
           "company_address" -> "company_address",
           "company_country" -> "company_country",
           "company_founded" -> "company_founded",
           "company_industry" -> "company_industry",
           "company_linkedin_id" -> "company_linkedin_id",
           "company_linkedin_url" -> "company_linkedin_url",
           "company_meta_description" -> "company_meta_description",
           "company_meta_emails" -> "company_meta_emails",
           "company_meta_keywords" -> "company_meta_keywords",
           "company_meta_phones" -> "company_meta_phones",
           "company_meta_title" -> "company_meta_title",
           "company_name" -> "company_name",
           "company_size" -> "company_size",
           "company_type" -> "company_type",
           "email_format" -> "email_format",
           "list_name" -> "list_name",
           "person_business_email" -> "person_business_email",
           "person_city" -> "person_city",
           "person_company_name" -> "person_company_name",
           "person_first_name" -> "person_first_name",
           "person_headline" -> "person_headline",
           "person_job_title" -> "person_job_title",
           "person_last_name" -> "person_last_name",
           "person_linkedin_id" -> "person_linkedin_id",
           "person_linkedin_url" -> "person_linkedin_url",
           "person_location" -> "person_location",
           "person_personal_email" -> "person_personal_email",
           "person_phone" -> "person_phone",
           "query" -> "query",
           "url" -> "url",
         )
         
         val column_map_3: JsObject = Json.obj(
           ""-> "",
           "ANZSIC 2006 Code"-> "anzsic_2006_code",
           "ANZSIC 2006 Description"-> "anzsic_2006_description",
           "Address"-> "address",
           "Address 1"-> "address_1",
           "Address 2"-> "address_2",
           "Address Line 1"-> "address_line_1",
           "Address Line 2"-> "address_line_2",
           "Address Line 3"-> "address_line_3",
           "App Using"-> "app_using",
           "Assets (USD)"-> "assets__usd_",
           "Business Description"-> "business_description",
           "City"-> "city",
           "Company"-> "company",
           "Company City"-> "company_city",
           "Company Country"-> "company_country",
           "Company Name"-> "company_name",
           "Company State"-> "company_state",
           "Company Street address"-> "company_street_address",
           "Company ZIP/Postal code"-> "company_zip_postal_code",
           "Company division name"-> "company_division_name",
           "Company domain name"-> "company_domain_name",
           "Company phone number"-> "company_phone_number",
           "Contact"-> "contact",
           "Contact Level"-> "contact_level",
           "Contact Name"-> "contact_name",
           "Contact Title"-> "contact_title",
           "Country"-> "country",
           "Country/Region"-> "country_region",
           "County"-> "county",
           "D&B Hoovers Industry"-> "dnb_hoovers_industry",
           "Direct Marketing Status"-> "direct_marketing_status",
           "Direct Phone"-> "direct_phone",
           "Direct Phone Number"-> "direct_phone_number",
           "Email"-> "email",
           "Email Domain"-> "email_domain",
           "Email address"-> "email_address",
           "Employees"-> "employees",
           "Employees (All Sites)"-> "employees__all_sites_",
           "Employees (Single Site)"-> "employees__single_site_",
           "Employees Range"-> "employees_range",
           "Encrypted Email Address"-> "encrypted_email_address",
           "Entity Type"-> "entity_type",
           "Fax"-> "fax",
           "First Name"-> "first_name",
           "Highest Level Job Function"-> "highest_level_job_function",
           "ISIC Rev 4 Code"-> "isic_rev_4_code",
           "ISIC Rev 4 Description"-> "isic_rev_4_description",
           "Industry"-> "industry",
           "Industry Type"-> "industry_type",
           "Industry hierarchical category"-> "industry_hierarchical_category",
           "Industry label"-> "industry_label",
           "Job Function"-> "job_function",
           "Job title"-> "job_title",
           "Job title hierarchy level"-> "job_title_hierarchy_level",
           "Last Name"-> "last_name",
           "Liabilities (USD)"-> "liabilities__usd_",
           "Linkedin Links"-> "linkedin_links",
           "Management Level"-> "management_level",
           "Middle Name"-> "middle_name",
           "NACE Rev 2 Code"-> "nace_rev_2_code",
           "NACE Rev 2 Description"-> "nace_rev_2_description",
           "NAICS"-> "naics",
           "NAICS 2012 Code"-> "naics_2012_code",
           "NAICS 2012 Description"-> "naics_2012_description",
           "NAICS1"-> "naics1",
           "NAICS2"-> "naics2",
           "Ownership Type"-> "ownership_type",
           "Parent Company"-> "parent_company",
           "Parent Country/Region"-> "parent_country_region",
           "Person City"-> "person_city",
           "Person Pro URL"-> "person_pro_url",
           "Person State"-> "person_state",
           "Person Zip"-> "person_zip",
           "Phone"-> "phone",
           "Postal Code"-> "postal_code",
           "Pre Tax Profit (USD)"-> "pre_tax_profit__usd_",
           "Query Name"-> "query_name",
           "Revenue"-> "revenue",
           "Revenue (USD)"-> "revenue__usd_",
           "Revenue (in 000s)"-> "revenue__in_000s_",
           "Revenue Range"-> "revenue_range",
           "SIC"-> "sic",
           "SIC Code"-> "sic_code",
           "SIC1"-> "sic1",
           "SIC2"-> "sic2",
           "Sales"-> "sales",
           "Salutation"-> "salutation",
           "Secondary industry hierarchical category"-> "secondary_industry_hierarchical_category",
           "Secondary industry label"-> "secondary_industry_label",
           "Source count"-> "source_count",
           "State"-> "state",
           "State Or Province"-> "state_or_province",
           "Suffix"-> "suffix",
           "Ticker"-> "ticker",
           "Title"-> "title",
           "TitleCode"-> "titlecode",
           "Total Employees"-> "total_employees",
           "Tradestyle"-> "tradestyle",
           "UK SIC 2007 Code"-> "uk_sic_2007_code",
           "UK SIC 2007 Description"-> "uk_sic_2007_description",
           "URL"-> "url",
           "US SIC 1987 Code"-> "us_sic_1987_code",
           "US SIC 1987 Description"-> "us_sic_1987_description",
           "Ultimate Parent Company"-> "ultimate_parent_company",
           "Ultimate Parent Country/Region"-> "ultimate_parent_country_region",
           "Website"-> "website",
           "Zip"-> "zip",
           "Zoom Individual ID"-> "zoom_individual_id",
           "Zoom company ID"-> "zoom_company_id",
           "Zoominfo Industry"-> "zoominfo_industry",
         )

         val csvDetailsForUploadIntoCsvQueue = Seq(
           CsvDetailsForUploadIntoCsvQueue(
             file_url = "https://storage.googleapis.com/ld-zip/nxd/flattened/ap_part1-01-1.csv",
             column_map = column_map_2
           ),
           CsvDetailsForUploadIntoCsvQueue(
             file_url = "https://storage.googleapis.com/ld-zip/nxd/flattened/ap_part1-01-2.csv",
             column_map = column_map_2
           ),
           CsvDetailsForUploadIntoCsvQueue(
             file_url = "https://storage.googleapis.com/ld-zip/nxd/flattened/ap_part1-01-3.csv",
             column_map = column_map_2
           ),
           CsvDetailsForUploadIntoCsvQueue(
             file_url = "https://storage.googleapis.com/ld-zip/nxd/flattened/ap_part1-01-4.csv",
             column_map = column_map_2
           ),
           CsvDetailsForUploadIntoCsvQueue(
             file_url = "https://storage.googleapis.com/ld-zip/nxd/flattened/ap_part1-01-5.csv",
             column_map = column_map_2
           ),
           CsvDetailsForUploadIntoCsvQueue(
             file_url = "https://storage.googleapis.com/ld-zip/nxd/flattened/ap_part1-01-6.csv",
             column_map = column_map_2
           ),
           CsvDetailsForUploadIntoCsvQueue(
             file_url = "https://storage.googleapis.com/ld-zip/nxd/flattened/ap_part1-01-7.csv",
             column_map = column_map_2
           ),
           CsvDetailsForUploadIntoCsvQueue(
             file_url = "https://storage.googleapis.com/ld-zip/nxd/flattened/ap_part1-01-8.csv",
             column_map = column_map_2
           ),
           CsvDetailsForUploadIntoCsvQueue(
             file_url = "https://storage.googleapis.com/ld-zip/nxd/flattened/ap_part1-01-9.csv",
             column_map = column_map_2
           ),
           CsvDetailsForUploadIntoCsvQueue(
             file_url = "https://storage.googleapis.com/ld-zip/nxd/flattened/ap_part1-01-10.csv",
             column_map = column_map_2
           ),
           CsvDetailsForUploadIntoCsvQueue(
             file_url = "https://storage.googleapis.com/ld-zip/nxd/flattened/ap_part1-01-11.csv",
             column_map = column_map_2
           ),
           CsvDetailsForUploadIntoCsvQueue(
             file_url = "https://storage.googleapis.com/ld-zip/nxd/flattened/ap_part1-01-12.csv",
             column_map = column_map_2
           ),
           CsvDetailsForUploadIntoCsvQueue(
             file_url = "https://storage.googleapis.com/ld-zip/nxd/flattened/ap_part1-01-13.csv",
             column_map = column_map_2
           ),
           CsvDetailsForUploadIntoCsvQueue(
             file_url = "file_name = https://storage.googleapis.com/ld-zip/nxd/flattened/ap_part1-01-14.csv",
             column_map = column_map_2
           ),
           CsvDetailsForUploadIntoCsvQueue(
             file_url = "https://storage.googleapis.com/ld-zip/nxd/flattened/ap_part1-01-15.csv",
             column_map = column_map_2
           ),
           CsvDetailsForUploadIntoCsvQueue(
             file_url = "https://storage.googleapis.com/ld-zip/nxd/flattened/ap_part1-01-16.csv",
             column_map = column_map_2
           ),
           CsvDetailsForUploadIntoCsvQueue(
             file_url = "https://storage.googleapis.com/ld-zip/nxd/flattened/ap_part1-01-17.csv",
             column_map = column_map_2
           ),
           CsvDetailsForUploadIntoCsvQueue(
             file_url = "https://storage.googleapis.com/ld-zip/nxd/flattened/ap_part1-01-18.csv",
             column_map = column_map_2
           ),
           CsvDetailsForUploadIntoCsvQueue(
             file_url = "https://storage.googleapis.com/ld-zip/nxd/flattened/ap_part1-01-19.csv",
             column_map = column_map_2
           ),
           CsvDetailsForUploadIntoCsvQueue(
             file_url = "https://storage.googleapis.com/ld-zip/nxd/flattened/ap_part1-01-20.csv",
             column_map = column_map_2
           ),
           CsvDetailsForUploadIntoCsvQueue(
             file_url = "https://storage.googleapis.com/ld-zip/nxd/flattened/ap_part1-01-21.csv",
             column_map = column_map_2
           ),
           CsvDetailsForUploadIntoCsvQueue(
             file_url = "https://storage.googleapis.com/ld-zip/nxd/flattened/ap_part1-01-22.csv",
             column_map = column_map_2
           ),
           CsvDetailsForUploadIntoCsvQueue(
             file_url = "https://storage.googleapis.com/ld-zip/nxd/flattened/ap_part1-01-23.csv",
             column_map = column_map_2
           ),
           CsvDetailsForUploadIntoCsvQueue(
             file_url = "https://storage.googleapis.com/ld-zip/nxd/flattened/ap_part1-01-24.csv",
             column_map = column_map_2
           ),
           CsvDetailsForUploadIntoCsvQueue(
             file_url = "https://storage.googleapis.com/ld-zip/nxd/flattened/ap_part1-01-25.csv",
             column_map = column_map_2
           ),
           CsvDetailsForUploadIntoCsvQueue(
             file_url = "https://storage.googleapis.com/ld-zip/nxd/flattened/ap_part1-01-26.csv",
             column_map = column_map_2
           ),
           CsvDetailsForUploadIntoCsvQueue(
             file_url = "https://storage.googleapis.com/ld-zip/nxd/flattened/ap_part1-01-28.csv",
             column_map = column_map_2
           ),
           CsvDetailsForUploadIntoCsvQueue(
             file_url = "https://storage.googleapis.com/ld-zip/nxd/flattened/ap_part1-01-29.csv",
             column_map = column_map_2
           )
         )

         leadFinderUploadDao.findAll() match {
           case Failure(err)=>
             logger.error("Error occurred while finding records from csv ")
           case Success(data)=>
             val filtered_list = csvDetailsForUploadIntoCsvQueue.filter(p => !data.contains(p.file_url))
             println(filtered_list)
             main.init(csvDetailsForUploadIntoCsvQueue = filtered_list) match {
               case Success(_) => logger.info("success")
               case Failure(exception) => logger.error("error", err = exception)
             }
         }

       case "leadFinderCsvUploadExecuteCron" =>
         given srLogger: SRLogger = new SRLogger("::::testApp lea finder upload csv executeCron::::")

         object CSU
           extends TestAppTrait {

           def mainFn() = {

             uploadLeadFinderCsvCronService.executeCron()

           }

         }

         val result = CSU
           .mainFn()

       case "upload_csv_details_li_lead_finder_queue" =>

         given logger: SRLogger = new SRLogger("test_upload_csv_details")

         object main {

           def init(
                     csvDetailsForUploadIntoCsvQueue: Seq[CsvDetailsForUploadIntoCsvQueue]
                   )(using logger: SRLogger) = Try {

             val csvQueueCreateFormData = csvDetailsForUploadIntoCsvQueue.map(data =>
               CsvQueueCreateFormData(
                 list_name = Some(Random.alphanumeric.take(5).mkString("")),
                 file_url = data.file_url,
                 column_map = data.column_map,
                 dt_src = "li"
               )
             )
             val res1 = leadFinderUploadService.uploadLinkedinCsvDetails(
               accountId = 6939,
               teamId = 6412,
               ta_id = 20165,
               loggedIn_id = 6939,
               data = csvQueueCreateFormData
             )

             assert(res1.isSuccess);
           }

         }

         val column_map_4_1: JsObject = ColumnMap4_1.column_map_4_1
         val column_map_4_2: JsObject = ColumnMap4_2.column_map_4_2
         val column_map_4_3: JsObject = ColumnMap4_3.column_map_4_3

         val combinedJson = column_map_4_1 ++ column_map_4_2 ++ column_map_4_3

         val csvDetailsForUploadIntoCsvQueue = Seq(
           CsvDetailsForUploadIntoCsvQueue(
             file_url = "https->//storage.googleapis.com/ld-zip/nxd/flattened/part151_to_part160-0005_prsw_sample_data_513_records.csv",
             column_map = combinedJson
           )
         )

         leadFinderUploadDao.findAllLinkedin() match {
           case Failure(err) =>
             logger.error("Error occurred while finding records from csv ")
           case Success(data) =>
             val filtered_list = csvDetailsForUploadIntoCsvQueue.filter(p => !data.contains(p.file_url))
             main.init(csvDetailsForUploadIntoCsvQueue = filtered_list) match {
               case Success(_) => logger.info("success")
               case Failure(exception) => logger.error("error", err = exception)
             }
         }

       case "leadFinderLinkedinCsvUploadExecuteCron" =>
         given srLogger: SRLogger = new SRLogger("->->->->testApp lead finder upload csv executeCron->->->->")

         object CSU
           extends TestAppTrait {

           def mainFn() = {

             uploadLeadFinderLinkedinCsvCronService.executeCron()

           }

         }

         val result = CSU
           .mainFn()

      }

    }
  }

}
*/
