package utils.testapp

import api.accounts.TeamId
import api.emails.EmailSetting
import api.emails.daos.EmailHealthCheckRecord
import io.smartreach.esp.api.emails.EmailSettingId
import scalikejdbc.config.DBs
import utils.SRLogger

import scala.concurrent.duration.Duration
import scala.concurrent.{Await, Future}
import scala.util.{Failure, Success, Try}

object TriggerEmailHealthCheck extends TestAppTrait {

  def main(args: Array[String]): Unit = {

    if (args.length > 0) {

      DBs.setupAll()

      given Logger: SRLogger = new SRLogger(logRequestId = "TriggerEmailHealthCheck")

      val applicationName = args(0)

      applicationName match {

        case "trigger_health_check" =>

          val emailHealthCheckRecordFut = for {

            teamId: TeamId <- Try(args(1).toLong).map(tid => TeamId(id = tid)) match {

              case Failure(exception) =>

                Logger.error(
                  msg = s"Arg 1 - Invalid teamId: ${args.lift(1)}",
                  err = exception,
                )

                Future.failed(exception)

              case Success(value) =>

                Future.successful(value)

            }

            emailSettingId: EmailSettingId <- Try(args(2).toLong).map(eid => EmailSettingId(eid)) match {

              case Failure(exception) =>

                Logger.error(
                  msg = s"Arg 2 - Invalid emailSettingId: ${args.lift(2)}",
                  err = exception,
                )

                Future.failed(exception)

              case Success(value) =>

                Future.successful(value)

            }

            emailSetting: EmailSetting <- emailSettingDAO.find(
              teamId = teamId,
              id = emailSettingId,
            ) match {

              case None =>

                Logger.error(
                  msg = s"Email setting not found for teamId: $teamId and emailSettingId: $emailSettingId",
                )

                Future.failed(
                  new Exception(s"Email setting not found for teamId: $teamId and emailSettingId: $emailSettingId")
                )

              case Some(emailSetting) =>

                Future.successful(emailSetting)

            }

            emailHealthCheckRecord: EmailHealthCheckRecord <- emailHealthCheckService.createEmailSettingHealthCheck(
              emailSettingForHealthCheck = emailSetting,
            )

          } yield {

            emailHealthCheckRecord

          }

          Await.ready(emailHealthCheckRecordFut, Duration.Inf).value match {

            case None =>

              Logger.error(
                msg = "Email health check record - None.",
              )

            case Some(Failure(exception)) =>

              Logger.error(
                msg = "Failed to create email health check record",
                err = exception,
              )

            case Some(Success(emailHealthCheckRecord)) =>

              Logger.info(
                msg = s"Email health check record created with id: ${emailHealthCheckRecord.id}",
              )

          }

      }

    }

  }

}
