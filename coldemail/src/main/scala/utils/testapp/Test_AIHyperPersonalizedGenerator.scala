package utils.testapp

import api.accounts.AccountUuid
import api.accounts.email.models.EmailServiceProvider
import api.accounts.models.OrgId
import api.campaigns.{CampaignStepVariantForScheduling, CampaignStepWithChildren}
import api.campaigns.models.CampaignStepData.AutoEmailStep
import api.campaigns.models.{CampaignEmailSettingsId, CampaignStepData, CampaignStepType, CampaignStepsStructure, CampaignTypeData}
import api.campaigns.services.CampaignId
import api.columns.{InternalMergeTagValuesForProspect, MagicColumnResponse}
import api.gpt.CreateStepsRequest
import api.gpt.ai_hyperpersonalized.{StepContext}
import api.llm.dao.LlmResponseData
import api.prospects.{ProspectAccount, ProspectUuid}
import api.prospects.models.{ProspectDataForChannelScheduling, StepId}
import utils.mq.channel_scheduler.channels.StepType
import api.tags.models.ProspectTag
import api.tasks.models.TaskPriority.Normal
import api.team_inbox.service.ReplySentimentForTeam
import eventframework.{ProspectFieldsResult, ProspectObject, ProspectObjectInternal, StandardProspectFields}
import io.smartreach.esp.api.emails.EmailSettingId
import io.sr.llm.bedrock.services.GeneratedEmail
import org.joda.time.DateTime
import play.api.libs.json.JsValue
import play.api.libs.json.Json
import scalikejdbc.config.DBs
import sr_scheduler.models.CampaignForScheduling.CampaignEmailSettingForScheduler
import sr_scheduler.models.ChannelType.EmailChannel
import sr_scheduler.models.{CampaignAIGenerationContext, CampaignEmailPriority, CampaignForScheduling}
import sr_scheduler.{CampaignStatus, models}
import utils.SRLogger
import utils.helpers.LogHelpers
import utils.testapp.Test_SmartReachAiApi.srAiApi

import scala.concurrent.duration.Duration
import scala.concurrent.{Await, Future}
import scala.util.{Failure, Success}


object Test_AIHyperPersonalizedGenerator extends TestAppTrait {

  def main(args: Array[String]): Unit = {

    if (args.length > 0) {

      DBs.setupAll()

      given Logger: SRLogger = new SRLogger(logRequestId = "Test_AIHyperPersonalizedGenerator")

      val applicationName = args(0)

      applicationName match {

        case "test_hyperpersonalized_output" =>
          
          /*
             prospects level data
             campaign level context  
              - we are telling how many steps would be there.
              - What we are offering.
                  - The offer could be 2 months free access to premium agency features. if I'm a saas product and offering to agencies.
                  - Marketing manager working for a solar panel company. You are contacting head of purchase/ commercials at Hospitals and you provide a solution
                    the offer here would be free installation and service for first year. 
                    - What we are helping -> You are helping the purchase manager to reduce electricity overheads and medical exturgencies due to failure in 
                    electricity supply.
                     - possibly I want 4 emails, 
                     - if I put the designation of your prospects as CEO ( purhcase head ) -> check if email content is coming accordingly.
                     - In the command 
                     
             step level context
             previous sent communications
             which step its currently writing for  out of number steps in the campaign.
             which channel its currently writing for
             
          
           */
          // Sample test data
          // val testProspectData = ProspectEnrichmentData(
          //   firstName = "John",
          //   lastName = "Smith",
          //   company = "TechCorp Solutions",
          //   designation = "VP of Engineering",
          //   companySize = Some(500),
          //   industry = Some("Software Development"),
          //   companyLocation = Some("San Francisco, CA"),
          //   companyFunding = Some("Series B - $50M"),
          //   companyTechnology = List("React", "Node.js", "AWS", "MongoDB"),
          //   companyRevenue = Some("$50M-$100M"),
          //   companyGrowthRate = Some("40% YoY"),
          //   recentNewsAndAnnouncements = List(
          //     "Launched new AI-powered platform",
          //     "Expanded to European market"
          //   ),
          //   companyLinkedInFollowers = Some(15000),
          //   companyFoundedYear = Some(2015),
          //   recentProfessionalAchievements = Some("Speaker at AWS re:Invent 2023"),
          //   educationalBackground = Some("MS Computer Science, Stanford"),
          //   sharedConnections = List("Jane Doe", "Mike Johnson"),
          //   publishedArticles = List(
          //     "The Future of Cloud Computing",
          //     "Building Scalable Systems"
          //   ),
          //   conferenceAppearances = List("TechCrunch Disrupt 2023"),
          //   socialCauses = List("Tech Education for Underprivileged"),
          //   awards = List("Top 40 Under 40 Tech Leaders"),
          //   patentsField = List("Distributed Systems Architecture"),
          //   previousCompanies = List("Google", "Microsoft"),
          //   commonInterests = List("AI/ML", "Cloud Architecture")
          // )

          // val testCreateEmailRequest = CreateStepsRequest(
          //   team_type = "software companies",
          //   industry = "technology",
          //   motive = "improve development efficiency",
          //   solution = "providing AI-powered code review tools",
          //   reason_of_reaching_out = "their recent expansion and tech stack aligns with our solution",
          //   prospects_designation = "VP of Engineering, CTO, Tech Leaders",
          //   language = "English",
          //   tone = "professional",
          //   number_of_steps = 1 // Not relevant for single email generation
          // )

          // val generatedEmailFut: Future[String] = aiHyperPersonalizedGenerator.generateHyperPersonalizedEmail(
          //   prospectData = testProspectData,
          //   createEmailRequest = testCreateEmailRequest,
          //   orgId = OrgId(23)
          // )

          // Await.ready(generatedEmailFut, Duration.Inf).value match {
          //   case None =>
          //     Logger.error("generatedEmailFut - None Found.")
          //     println("generatedEmailFut - None Found.")

          //   case Some(Failure(exception)) =>
          //     Logger.error("Error generating hyper-personalized email", exception)
          //     println(s"Error: ${exception.getMessage}")

          //   case Some(Success(generatedEmail)) =>
          //     Logger.info("Successfully generated hyper-personalized email")
          //     println("Generated Hyper-Personalized Email:")
          //     println("----------------------------------------")
          //     println(generatedEmail)
          //     println("----------------------------------------")

          //     // You can split the response into subject and body if needed
          //     val Array(subject, body) = generatedEmail.split("\n\n", 2)
          //     println(s"Subject: ${subject.replace("Subject: ", "")}")
          //     println("\nBody:")
          //     println(body)
          // }

        case "test_multiple_hyperpersonalized_output" =>
          // List of test prospect data with different contexts
          /*
          val testProspects2 = List(
            // 1. Tech Company - Standard Case
            ProspectEnrichmentData(
              firstName = "John",
              lastName = "Smith",
              company = "TechCorp Solutions",
              designation = "VP of Engineering",
              companySize = Some(500),
              industry = Some("Software Development"),
              companyLocation = Some("San Francisco, CA"),
              companyFunding = Some("Series B - $50M"),
              companyTechnology = List("React", "Node.js", "AWS", "MongoDB"),
              companyRevenue = Some("$50M-$100M"),
              companyGrowthRate = Some("40% YoY"),
              recentNewsAndAnnouncements = List(
                "Launched new AI-powered platform",
                "Expanded to European market"
              ),
              companyLinkedInFollowers = Some(15000),
              companyFoundedYear = Some(2015),
              recentProfessionalAchievements = Some("Speaker at AWS re:Invent 2023"),
              educationalBackground = Some("MS Computer Science, Stanford"),
              sharedConnections = List("Jane Doe", "Mike Johnson"),
              publishedArticles = List(
                "The Future of Cloud Computing",
                "Building Scalable Systems"
              ),
              conferenceAppearances = List("TechCrunch Disrupt 2023"),
              socialCauses = List("Tech Education for Underprivileged"),
              awards = List("Top 40 Under 40 Tech Leaders"),
              patentsField = List("Distributed Systems Architecture"),
              previousCompanies = List("Google", "Microsoft"),
              commonInterests = List("AI/ML", "Cloud Architecture")
            ),

            // 2. Healthcare - Different Target Persona Case
            ProspectEnrichmentData(
              firstName = "Sarah",
              lastName = "Johnson",
              company = "Memorial Healthcare",
              designation = "CEO", // Different from target persona (Head of Purchase)
              companySize = 2000,
              industry = "Healthcare",
              companyLocation = "Chicago, IL",
              companyFunding = None,
              companyTechnology = List("Epic Systems", "Cerner"),
              companyRevenue = Some("$200M-$500M"),
              companyGrowthRate = Some("15% YoY"),
              recentNewsAndAnnouncements = List(
                "New Emergency Wing Opening",
                "Green Energy Initiative Announcement"
              ),
              companyLinkedInFollowers = 25000,
              companyFoundedYear = 1985,
              recentProfessionalAchievements = Some("Healthcare Leadership Award 2023"),
              educationalBackground = Some("MD, MBA"),
              sharedConnections = List("Hospital Administrators Network"),
              publishedArticles = List("Future of Healthcare Management"),
              conferenceAppearances = List("Healthcare Innovation Summit 2023"),
              socialCauses = List("Healthcare Accessibility"),
              awards = List("Excellence in Healthcare Management"),
              patentsField = List(),
              previousCompanies = List("Cleveland Clinic", "Kaiser Permanente"),
              commonInterests = List("Healthcare Innovation", "Sustainability")
            ),

            // 3. Startup - Minimal Information Case
            ProspectEnrichmentData(
              firstName = "Mike",
              lastName = "Chen",
              company = "InnovateTech",
              designation = "Founder",
              companySize = 10,
              industry = "Technology",
              companyLocation = "Austin, TX",
              companyFunding = None,
              companyTechnology = List(),
              companyRevenue = None,
              companyGrowthRate = None,
              recentNewsAndAnnouncements = List(),
              companyLinkedInFollowers = 500,
              companyFoundedYear = 2023,
              recentProfessionalAchievements = None,
              educationalBackground = None,
              sharedConnections = List(),
              publishedArticles = List(),
              conferenceAppearances = List(),
              socialCauses = List(),
              awards = List(),
              patentsField = List(),
              previousCompanies = List(),
              commonInterests = List()
            ),

            // 4. Enterprise - Rich Information Case
            ProspectEnrichmentData(
              firstName = "Elizabeth",
              lastName = "Taylor",
              company = "Global Enterprises Inc",
              designation = "Chief Innovation Officer",
              companySize = 50000,
              industry = "Conglomerate",
              companyLocation = "New York, NY",
              companyFunding = Some("Public - NYSE: GEI"),
              companyTechnology = List("SAP", "Oracle", "Salesforce", "Microsoft Stack"),
              companyRevenue = Some("$5B+"),
              companyGrowthRate = Some("8% YoY"),
              recentNewsAndAnnouncements = List(
                "Digital Transformation Initiative",
                "Sustainability Goals 2030",
                "Innovation Hub Launch",
                "Strategic Partnership with Tech Giants"
              ),
              companyLinkedInFollowers = 1000000,
              companyFoundedYear = 1950,
              recentProfessionalAchievements = Some("Fortune 500 Innovation Award, Digital Transformation Leader of the Year"),
              educationalBackground = Some("PhD in Computer Science, MBA from Harvard"),
              sharedConnections = List("Multiple C-level executives", "Industry leaders"),
              publishedArticles = List(
                "Digital Transformation at Scale",
                "Enterprise Innovation Framework",
                "Future of Work"
              ),
              conferenceAppearances = List("World Economic Forum", "Forbes Innovation Summit"),
              socialCauses = List("Climate Action", "Gender Equality in Tech"),
              awards = List("Innovation Leader 2023", "Digital Pioneer Award"),
              patentsField = List("Enterprise Architecture", "Digital Transformation"),
              previousCompanies = List("McKinsey", "IBM", "Accenture"),
              commonInterests = List("Digital Transformation", "Innovation", "Sustainability")
            ),

            // 5. Non-Profit - Different Industry Case
            ProspectEnrichmentData(
              firstName = "David",
              lastName = "Wilson",
              company = "Global Education Initiative",
              designation = "Operations Director",
              companySize = 150,
              industry = "Non-Profit",
              companyLocation = "Washington, DC",
              companyFunding = Some("Grant-funded"),
              companyTechnology = List("Basic CRM", "Google Workspace"),
              companyRevenue = None,
              companyGrowthRate = None,
              recentNewsAndAnnouncements = List("Educational Impact Report 2023"),
              companyLinkedInFollowers = 5000,
              companyFoundedYear = 2010,
              recentProfessionalAchievements = Some("Expanded to 10 new countries"),
              educationalBackground = Some("MA in Non-Profit Management"),
              sharedConnections = List("NGO Network"),
              publishedArticles = List("Sustainable Non-Profit Operations"),
              conferenceAppearances = List("Social Impact Summit"),
              socialCauses = List("Education Access", "Poverty Alleviation"),
              awards = List("Social Impact Award"),
              patentsField = List(),
              previousCompanies = List("UNICEF", "Save the Children"),
              commonInterests = List("Social Impact", "Education")
            ),

            // 6. International - Language/Culture Case
            ProspectEnrichmentData(
              firstName = "Hans",
              lastName = "Mueller",
              company = "Deutsche Innovation GmbH",
              designation = "Technischer Direktor",
              companySize = 300,
              industry = "Manufacturing",
              companyLocation = "Berlin, Germany",
              companyFunding = Some("Private"),
              companyTechnology = List("Siemens", "SAP"),
              companyRevenue = Some("€40M"),
              companyGrowthRate = Some("20% YoY"),
              recentNewsAndAnnouncements = List(
                "Industry 4.0 Implementation",
                "Expansion to Asian Markets"
              ),
              companyLinkedInFollowers = 8000,
              companyFoundedYear = 2005,
              recentProfessionalAchievements = Some("German Innovation Award 2023"),
              educationalBackground = Some("Diplom-Ingenieur, TU Munich"),
              sharedConnections = List(),
              publishedArticles = List("Industrie 4.0 Transformation"),
              conferenceAppearances = List("Hannover Messe 2023"),
              socialCauses = List("Sustainable Manufacturing"),
              awards = List("German Engineering Excellence"),
              patentsField = List("Industrial Automation"),
              previousCompanies = List("Siemens", "Bosch"),
              commonInterests = List("Industry 4.0", "Automation")
            ),

            // 7. Regulated Industry - Compliance Case
            ProspectEnrichmentData(
              firstName = "Patricia",
              lastName = "Martinez",
              company = "SecureBank Financial",
              designation = "Chief Compliance Officer",
              companySize = 5000,
              industry = "Banking",
              companyLocation = "Miami, FL",
              companyFunding = Some("Public - NYSE: SBF"),
              companyTechnology = List("Oracle Financial", "Bloomberg Terminal"),
              companyRevenue = Some("$1B+"),
              companyGrowthRate = Some("5% YoY"),
              recentNewsAndAnnouncements = List(
                "Digital Banking Initiative",
                "Regulatory Compliance Achievement"
              ),
              companyLinkedInFollowers = 50000,
              companyFoundedYear = 1980,
              recentProfessionalAchievements = Some("Banking Compliance Excellence Award"),
              educationalBackground = Some("JD, MBA"),
              sharedConnections = List("Banking Network"),
              publishedArticles = List("Future of Banking Compliance"),
              conferenceAppearances = List("Financial Regulation Summit"),
              socialCauses = List("Financial Literacy"),
              awards = List("Risk Management Excellence"),
              patentsField = List(),
              previousCompanies = List("JPMorgan", "Wells Fargo"),
              commonInterests = List("RegTech", "Financial Innovation")
            ),

            // 8. Small Business - Limited Budget Case
            ProspectEnrichmentData(
              firstName = "Robert",
              lastName = "Brown",
              company = "Local Retail Solutions",
              designation = "Owner",
              companySize = 25,
              industry = "Retail",
              companyLocation = "Portland, OR",
              companyFunding = None,
              companyTechnology = List("Shopify", "QuickBooks"),
              companyRevenue = Some("$2M"),
              companyGrowthRate = None,
              recentNewsAndAnnouncements = List("Local Business Award Winner"),
              companyLinkedInFollowers = 200,
              companyFoundedYear = 2018,
              recentProfessionalAchievements = Some("Opened second location"),
              educationalBackground = Some("BS Business"),
              sharedConnections = List(),
              publishedArticles = List(),
              conferenceAppearances = List(),
              socialCauses = List("Local Community Support"),
              awards = List("Local Business Excellence"),
              patentsField = List(),
              previousCompanies = List("Retail Chain Store"),
              commonInterests = List("Small Business Growth")
            ),

            // 9. Competitor Adjacent - Sensitive Case
            ProspectEnrichmentData(
              firstName = "Amanda",
              lastName = "Lee",
              company = "CompeteTech Solutions",
              designation = "Product Director",
              companySize = 400,
              industry = "Software",
              companyLocation = "Seattle, WA",
              companyFunding = Some("Series C - $100M"),
              companyTechnology = List("Competing Stack"),
              companyRevenue = Some("$30M"),
              companyGrowthRate = Some("25% YoY"),
              recentNewsAndAnnouncements = List(
                "Product Launch",
                "Market Expansion"
              ),
              companyLinkedInFollowers = 12000,
              companyFoundedYear = 2016,
              recentProfessionalAchievements = Some("Product of the Year Award"),
              educationalBackground = Some("MS Product Management"),
              sharedConnections = List("Industry Connections"),
              publishedArticles = List("Product Innovation Strategy"),
              conferenceAppearances = List("ProductCon 2023"),
              socialCauses = List("Tech Education"),
              awards = List("Product Innovation Award"),
              patentsField = List("Product Patents"),
              previousCompanies = List("Other Competitors"),
              commonInterests = List("Product Development")
            ),

            // 10. High-Profile - Executive Case
            ProspectEnrichmentData(
              firstName = "William",
              lastName = "Gates", // Intentionally similar to a well-known person
              company = "Future Ventures",
              designation = "Managing Partner",
              companySize = 50,
              industry = "Venture Capital",
              companyLocation = "Silicon Valley",
              companyFunding = Some("$2B AUM"),
              companyTechnology = List("Investment Platforms"),
              companyRevenue = None,
              companyGrowthRate = None,
              recentNewsAndAnnouncements = List(
                "Major Investment Announcements",
                "Industry Keynote Speaker"
              ),
              companyLinkedInFollowers = 1000000,
              companyFoundedYear = 2020,
              recentProfessionalAchievements = Some("Featured in Forbes 30 Under 30"),
              educationalBackground = Some("Harvard Business School"),
              sharedConnections = List("High-profile VCs", "Tech Leaders"),
              publishedArticles = List(
                "Future of Tech Investment",
                "Innovation in Venture Capital"
              ),
              conferenceAppearances = List("Davos 2023", "TED Talk"),
              socialCauses = List("Climate Tech", "Education"),
              awards = List("Influential VC of the Year"),
              patentsField = List(),
              previousCompanies = List("Top Tech Companies", "Investment Banks"),
              commonInterests = List("Technology Trends", "Impact Investing")
            )
          )
           */

          /*

          val testProspects3 = List(
            // 1. Tech Company - Standard Case
            ProspectEnrichmentData(
              firstName = "John",
              lastName = "Smith",
              company = "TechCorp Solutions",
              designation = "VP of Engineering",
              companySize = Some(500),
              industry = Some("Software Development"),
              companyLocation = Some("San Francisco, CA"),
              companyFunding = Some("Series B - $50M"),
              companyTechnology = List("React", "Node.js", "AWS", "MongoDB"),
              companyRevenue = Some("$50M-$100M"),
              companyGrowthRate = Some("40% YoY"),
              recentNewsAndAnnouncements = List(
                "Launched new AI-powered platform",
                "Expanded to European market"
              ),
              companyLinkedInFollowers = Some(15000),
              companyFoundedYear = Some(2015),
              recentProfessionalAchievements = Some("Speaker at AWS re:Invent 2023"),
              educationalBackground = Some("MS Computer Science, Stanford"),
              sharedConnections = List("Jane Doe", "Mike Johnson"),
              publishedArticles = List(
                "The Future of Cloud Computing",
                "Building Scalable Systems"
              ),
              conferenceAppearances = List("TechCrunch Disrupt 2023"),
              socialCauses = List("Tech Education for Underprivileged"),
              awards = List("Top 40 Under 40 Tech Leaders"),
              patentsField = List("Distributed Systems Architecture"),
              previousCompanies = List("Google", "Microsoft"),
              commonInterests = List("AI/ML", "Cloud Architecture")
            ),


            // 2. Healthcare - Different Target Persona Case
            ProspectEnrichmentData(
              firstName = "Sarah",
              lastName = "Johnson",
              company = "Memorial Healthcare",
              designation = "CEO", // Different from target persona (Head of Purchase)
              companySize = Some(2000),
              industry = Some("Healthcare"),
              companyLocation = Some("Chicago, IL"),
              companyFunding = None,
              companyTechnology = List("Epic Systems", "Cerner"),
              companyRevenue = Some("$200M-$500M"),
              companyGrowthRate = Some("15% YoY"),
              recentNewsAndAnnouncements = List(
                "New Emergency Wing Opening",
                "Green Energy Initiative Announcement"
              ),
              companyLinkedInFollowers = Some(25000),
              companyFoundedYear = Some(1985),
              recentProfessionalAchievements = Some("Healthcare Leadership Award 2023"),
              educationalBackground = Some("MD, MBA"),
              sharedConnections = List("Hospital Administrators Network"),
              publishedArticles = List("Future of Healthcare Management"),
              conferenceAppearances = List("Healthcare Innovation Summit 2023"),
              socialCauses = List("Healthcare Accessibility"),
              awards = List("Excellence in Healthcare Management"),
              patentsField = List(),
              previousCompanies = List("Cleveland Clinic", "Kaiser Permanente"),
              commonInterests = List("Healthcare Innovation", "Sustainability")
            ),

            // 3. Startup - Minimal Information Case
            ProspectEnrichmentData(
              firstName = "Mike",
              lastName = "Chen",
              company = "InnovateTech",
              designation = "Founder",
              companySize = Some(10),
              industry = Some("Technology"),
              companyLocation = Some("Austin, TX"),
              companyFunding = None,
              companyTechnology = List(),
              companyRevenue = None,
              companyGrowthRate = None,
              recentNewsAndAnnouncements = List(),
              companyLinkedInFollowers = Some(500),
              companyFoundedYear = Some(2023),
              recentProfessionalAchievements = None,
              educationalBackground = None,
              sharedConnections = List(),
              publishedArticles = List(),
              conferenceAppearances = List(),
              socialCauses = List(),
              awards = List(),
              patentsField = List(),
              previousCompanies = List(),
              commonInterests = List()
            ),

            // 4. Enterprise - Rich Information Case
            ProspectEnrichmentData(
              firstName = "Elizabeth",
              lastName = "Taylor",
              company = "Global Enterprises Inc",
              designation = "Chief Innovation Officer",
              companySize = Some(50000),
              industry = Some("Conglomerate"),
              companyLocation = Some("New York, NY"),
              companyFunding = Some("Public - NYSE: GEI"),
              companyTechnology = List("SAP", "Oracle", "Salesforce", "Microsoft Stack"),
              companyRevenue = Some("$5B+"),
              companyGrowthRate = Some("8% YoY"),
              recentNewsAndAnnouncements = List(
                "Digital Transformation Initiative",
                "Sustainability Goals 2030",
                "Innovation Hub Launch",
                "Strategic Partnership with Tech Giants"
              ),
              companyLinkedInFollowers = Some(1000000),
              companyFoundedYear = Some(1950),
              recentProfessionalAchievements = Some("Fortune 500 Innovation Award, Digital Transformation Leader of the Year"),
              educationalBackground = Some("PhD in Computer Science, MBA from Harvard"),
              sharedConnections = List("Multiple C-level executives", "Industry leaders"),
              publishedArticles = List(
                "Digital Transformation at Scale",
                "Enterprise Innovation Framework",
                "Future of Work"
              ),
              conferenceAppearances = List("World Economic Forum", "Forbes Innovation Summit"),
              socialCauses = List("Climate Action", "Gender Equality in Tech"),
              awards = List("Innovation Leader 2023", "Digital Pioneer Award"),
              patentsField = List("Enterprise Architecture", "Digital Transformation"),
              previousCompanies = List("McKinsey", "IBM", "Accenture"),
              commonInterests = List("Digital Transformation", "Innovation", "Sustainability")
            ),

            // 5. Non-Profit - Different Industry Case
            ProspectEnrichmentData(
              firstName = "David",
              lastName = "Wilson",
              company = "Global Education Initiative",
              designation = "Operations Director",
              companySize = Some(150),
              industry = Some("Non-Profit"),
              companyLocation = Some("Washington, DC"),
              companyFunding = Some("Grant-funded"),
              companyTechnology = List("Basic CRM", "Google Workspace"),
              companyRevenue = None,
              companyGrowthRate = None,
              recentNewsAndAnnouncements = List("Educational Impact Report 2023"),
              companyLinkedInFollowers = Some(5000),
              companyFoundedYear = Some(2010),
              recentProfessionalAchievements = Some("Expanded to 10 new countries"),
              educationalBackground = Some("MA in Non-Profit Management"),
              sharedConnections = List("NGO Network"),
              publishedArticles = List("Sustainable Non-Profit Operations"),
              conferenceAppearances = List("Social Impact Summit"),
              socialCauses = List("Education Access", "Poverty Alleviation"),
              awards = List("Social Impact Award"),
              patentsField = List(),
              previousCompanies = List("UNICEF", "Save the Children"),
              commonInterests = List("Social Impact", "Education")
            ),

            // 6. International - Language/Culture Case
            ProspectEnrichmentData(
              firstName = "Hans",
              lastName = "Mueller",
              company = "Deutsche Innovation GmbH",
              designation = "Technischer Direktor",
              companySize = Some(300),
              industry = Some("Manufacturing"),
              companyLocation = Some("Berlin, Germany"),
              companyFunding = Some("Private"),
              companyTechnology = List("Siemens", "SAP"),
              companyRevenue = Some("€40M"),
              companyGrowthRate = Some("20% YoY"),
              recentNewsAndAnnouncements = List(
                "Industry 4.0 Implementation",
                "Expansion to Asian Markets"
              ),
              companyLinkedInFollowers = Some(8000),
              companyFoundedYear = Some(2005),
              recentProfessionalAchievements = Some("German Innovation Award 2023"),
              educationalBackground = Some("Diplom-Ingenieur, TU Munich"),
              sharedConnections = List(),
              publishedArticles = List("Industrie 4.0 Transformation"),
              conferenceAppearances = List("Hannover Messe 2023"),
              socialCauses = List("Sustainable Manufacturing"),
              awards = List("German Engineering Excellence"),
              patentsField = List("Industrial Automation"),
              previousCompanies = List("Siemens", "Bosch"),
              commonInterests = List("Industry 4.0", "Automation")
            ),

            // 7. Regulated Industry - Compliance Case
            ProspectEnrichmentData(
              firstName = "Patricia",
              lastName = "Martinez",
              company = "SecureBank Financial",
              designation = "Chief Compliance Officer",
              companySize = Some(5000),
              industry = Some("Banking"),
              companyLocation = Some("Miami, FL"),
              companyFunding = Some("Public - NYSE: SBF"),
              companyTechnology = List("Oracle Financial", "Bloomberg Terminal"),
              companyRevenue = Some("$1B+"),
              companyGrowthRate = Some("5% YoY"),
              recentNewsAndAnnouncements = List(
                "Digital Banking Initiative",
                "Regulatory Compliance Achievement"
              ),
              companyLinkedInFollowers = Some(50000),
              companyFoundedYear = Some(1980),
              recentProfessionalAchievements = Some("Banking Compliance Excellence Award"),
              educationalBackground = Some("JD, MBA"),
              sharedConnections = List("Banking Network"),
              publishedArticles = List("Future of Banking Compliance"),
              conferenceAppearances = List("Financial Regulation Summit"),
              socialCauses = List("Financial Literacy"),
              awards = List("Risk Management Excellence"),
              patentsField = List(),
              previousCompanies = List("JPMorgan", "Wells Fargo"),
              commonInterests = List("RegTech", "Financial Innovation")
            ),

            // 8. Small Business - Limited Budget Case
            ProspectEnrichmentData(
              firstName = "Robert",
              lastName = "Brown",
              company = "Local Retail Solutions",
              designation = "Owner",
              companySize = Some(25),
              industry = Some("Retail"),
              companyLocation = Some("Portland, OR"),
              companyFunding = None,
              companyTechnology = List("Shopify", "QuickBooks"),
              companyRevenue = Some("$2M"),
              companyGrowthRate = None,
              recentNewsAndAnnouncements = List("Local Business Award Winner"),
              companyLinkedInFollowers = Some(200),
              companyFoundedYear = Some(2018),
              recentProfessionalAchievements = Some("Opened second location"),
              educationalBackground = Some("BS Business"),
              sharedConnections = List(),
              publishedArticles = List(),
              conferenceAppearances = List(),
              socialCauses = List("Local Community Support"),
              awards = List("Local Business Excellence"),
              patentsField = List(),
              previousCompanies = List("Retail Chain Store"),
              commonInterests = List("Small Business Growth")
            ),

            // 9. Competitor Adjacent - Sensitive Case
            ProspectEnrichmentData(
              firstName = "Amanda",
              lastName = "Lee",
              company = "CompeteTech Solutions",
              designation = "Product Director",
              companySize = Some(400),
              industry = Some("Software"),
              companyLocation = Some("Seattle, WA"),
              companyFunding = Some("Series C - $100M"),
              companyTechnology = List("Competing Stack"),
              companyRevenue = Some("$30M"),
              companyGrowthRate = Some("25% YoY"),
              recentNewsAndAnnouncements = List(
                "Product Launch",
                "Market Expansion"
              ),
              companyLinkedInFollowers = Some(12000),
              companyFoundedYear = Some(2016),
              recentProfessionalAchievements = Some("Product of the Year Award"),
              educationalBackground = Some("MS Product Management"),
              sharedConnections = List("Industry Connections"),
              publishedArticles = List("Product Innovation Strategy"),
              conferenceAppearances = List("ProductCon 2023"),
              socialCauses = List("Tech Education"),
              awards = List("Product Innovation Award"),
              patentsField = List("Product Patents"),
              previousCompanies = List("Other Competitors"),
              commonInterests = List("Product Development")
            ),

            // 10. High-Profile - Executive Case
            ProspectEnrichmentData(
              firstName = "William",
              lastName = "Gates", // Intentionally similar to a well-known person
              company = "Future Ventures",
              designation = "Managing Partner",
              companySize = Some(50),
              industry = Some("Venture Capital"),
              companyLocation = Some("Silicon Valley"),
              companyFunding = Some("$2B AUM"),
              companyTechnology = List("Investment Platforms"),
              companyRevenue = None,
              companyGrowthRate = None,
              recentNewsAndAnnouncements = List(
                "Major Investment Announcements",
                "Industry Keynote Speaker"
              ),
              companyLinkedInFollowers = Some(1000000),
              companyFoundedYear = Some(2020),
              recentProfessionalAchievements = Some("Featured in Forbes 30 Under 30"),
              educationalBackground = Some("Harvard Business School"),
              sharedConnections = List("High-profile VCs", "Tech Leaders"),
              publishedArticles = List(
                "Future of Tech Investment",
                "Innovation in Venture Capital"
              ),
              conferenceAppearances = List("Davos 2023", "TED Talk"),
              socialCauses = List("Climate Tech", "Education"),
              awards = List("Influential VC of the Year"),
              patentsField = List(),
              previousCompanies = List("Top Tech Companies", "Investment Banks"),
              commonInterests = List("Technology Trends", "Impact Investing")
            )

          )

          val testProspects = List(
            // 1. Large Hospital Network CEO
            ProspectEnrichmentData(
              firstName = "Sarah",
              lastName = "Johnson",
              company = "Memorial Healthcare Network",
              designation = "CEO",
              companySize = Some(5000),
              industry = Some("Healthcare"),
              companyLocation = Some("Chicago, IL"),
              companyFunding = Some("Public - NYSE: MHN"),
              companyTechnology = List("Epic Systems", "Cerner", "Philips Medical Equipment"),
              companyRevenue = Some("$2B+"),
              companyGrowthRate = Some("12% YoY"),
              recentNewsAndAnnouncements = List(
                "New Emergency Wing Opening",
                "Green Energy Initiative Announcement",
                "Healthcare Innovation Center Launch"
              ),
              companyLinkedInFollowers = Some(100000),
              companyFoundedYear = Some(1975),
              recentProfessionalAchievements = Some("Healthcare CEO of the Year 2023"),
              educationalBackground = Some("MD, MBA - Harvard"),
              sharedConnections = List("Hospital Administrators Network", "Healthcare Leaders Forum"),
              publishedArticles = List("Future of Sustainable Healthcare", "Digital Transformation in Hospitals"),
              conferenceAppearances = List("Healthcare Innovation Summit 2023", "Medical Leadership Conference"),
              socialCauses = List("Healthcare Accessibility", "Environmental Sustainability"),
              awards = List("Excellence in Healthcare Management", "Sustainability Leadership Award"),
              patentsField = List(),
              previousCompanies = List("Cleveland Clinic", "Mayo Clinic"),
              commonInterests = List("Healthcare Innovation", "Sustainability", "Medical Technology")
            ),

            // 2. Regional Hospital Operations Head
            ProspectEnrichmentData(
              firstName = "Michael",
              lastName = "Chen",
              company = "Pacific Regional Medical Center",
              designation = "Head of Operations",
              companySize = Some(2000),
              industry = Some("Healthcare"),
              companyLocation = Some("San Francisco, CA"),
              companyFunding = Some("Private"),
              companyTechnology = List("Epic Systems", "GE Healthcare Equipment"),
              companyRevenue = Some("$500M-$1B"),
              companyGrowthRate = Some("8% YoY"),
              recentNewsAndAnnouncements = List(
                "Facility Modernization Project",
                "Energy Efficiency Initiative"
              ),
              companyLinkedInFollowers = Some(50000),
              companyFoundedYear = Some(1980),
              recentProfessionalAchievements = Some("Operational Excellence Award 2023"),
              educationalBackground = Some("MBA Healthcare Administration"),
              sharedConnections = List("Healthcare Operations Network"),
              publishedArticles = List("Optimizing Hospital Operations", "Sustainable Healthcare Practices"),
              conferenceAppearances = List("Healthcare Operations Summit"),
              socialCauses = List("Healthcare Efficiency", "Environmental Impact"),
              awards = List("Healthcare Operations Innovation"),
              patentsField = List(),
              previousCompanies = List("Kaiser Permanente", "Sutter Health"),
              commonInterests = List("Operational Efficiency", "Healthcare Technology")
            ),

            // 3. Children's Hospital Facilities Director
            ProspectEnrichmentData(
              firstName = "Elizabeth",
              lastName = "Martinez",
              company = "Children's Hope Hospital",
              designation = "Facilities Director",
              companySize = Some(1500),
              industry = Some("Healthcare"),
              companyLocation = Some("Boston, MA"),
              companyFunding = Some("Non-profit"),
              companyTechnology = List("Siemens Medical Equipment", "Building Management Systems"),
              companyRevenue = Some("$300M-$500M"),
              companyGrowthRate = Some("5% YoY"),
              recentNewsAndAnnouncements = List(
                "New Pediatric Wing Construction",
                "Renewable Energy Implementation"
              ),
              companyLinkedInFollowers = Some(30000),
              companyFoundedYear = Some(1990),
              recentProfessionalAchievements = Some("Healthcare Facility Management Excellence"),
              educationalBackground = Some("MS Facility Management"),
              sharedConnections = List("Healthcare Facility Managers"),
              publishedArticles = List("Sustainable Hospital Design"),
              conferenceAppearances = List("Healthcare Facilities Symposium"),
              socialCauses = List("Children's Healthcare", "Environmental Sustainability"),
              awards = List("Facility Management Innovation"),
              patentsField = List(),
              previousCompanies = List("Boston Medical Center"),
              commonInterests = List("Sustainable Building", "Healthcare Infrastructure")
            ),

            // 4. University Hospital Administrator
            ProspectEnrichmentData(
              firstName = "Robert",
              lastName = "Thompson",
              company = "University Medical Center",
              designation = "Head of Purchase",
              companySize = Some(4000),
              industry = Some("Software Technology"),
              companyLocation = Some("Houston, TX"),
              companyFunding = Some("State-funded"),
              companyTechnology = List("Epic Systems", "Advanced Medical Equipment"),
              companyRevenue = Some("$1.5B+"),
              companyGrowthRate = Some("10% YoY"),
              recentNewsAndAnnouncements = List(
                "Research Center Expansion",
                "Sustainability Initiative Launch"
              ),
              companyLinkedInFollowers = Some(75000),
              companyFoundedYear = Some(1965),
              recentProfessionalAchievements = Some("Healthcare Procurement Excellence"),
              educationalBackground = Some("MBA, Healthcare Management"),
              sharedConnections = List("Academic Medical Centers Network"),
              publishedArticles = List("Healthcare Resource Management"),
              conferenceAppearances = List("Academic Medical Conference 2023"),
              socialCauses = List("Medical Education", "Sustainability"),
              awards = List("Procurement Innovation Award"),
              patentsField = List(),
              previousCompanies = List("MD Anderson", "Baylor Medical"),
              commonInterests = List("Medical Research", "Sustainable Healthcare")
            ),

            // 5. Specialty Hospital Executive
            ProspectEnrichmentData(
              firstName = "Patricia",
              lastName = "Wong",
              company = "Cardiac Care Specialists Hospital",
              designation = "Operations Director",
              companySize = Some(800),
              industry = Some("Healthcare"),
              companyLocation = Some("Los Angeles, CA"),
              companyFunding = Some("Private Equity Backed"),
              companyTechnology = List("Specialized Cardiac Equipment", "Cerner"),
              companyRevenue = Some("$200M-$400M"),
              companyGrowthRate = Some("15% YoY"),
              recentNewsAndAnnouncements = List(
                "New Cardiac Wing Opening",
                "Energy Management System Upgrade"
              ),
              companyLinkedInFollowers = Some(25000),
              companyFoundedYear = Some(2000),
              recentProfessionalAchievements = Some("Specialty Hospital Excellence Award"),
              educationalBackground = Some("MHA, Healthcare Administration"),
              sharedConnections = List("Specialty Hospitals Network"),
              publishedArticles = List("Specialized Healthcare Management"),
              conferenceAppearances = List("Cardiac Care Summit 2023"),
              socialCauses = List("Heart Disease Prevention", "Green Healthcare"),
              awards = List("Healthcare Management Excellence"),
              patentsField = List(),
              previousCompanies = List("Cedars-Sinai", "UCLA Medical"),
              commonInterests = List("Medical Innovation", "Sustainable Operations")
            ),

            // 6. Rural Hospital Network Leader
            ProspectEnrichmentData(
              firstName = "James",
              lastName = "Miller",
              company = "Rural Healthcare Network",
              designation = "Head of Surgery",
              companySize = Some(1200),
              industry = Some("Healthcare"),
              companyLocation = Some("Des Moines, IA"),
              companyFunding = Some("Government Backed"),
              companyTechnology = List("Telehealth Systems", "Basic Medical Equipment"),
              companyRevenue = Some("$150M-$300M"),
              companyGrowthRate = Some("6% YoY"),
              recentNewsAndAnnouncements = List(
                "Rural Healthcare Initiative",
                "Solar Power Implementation"
              ),
              companyLinkedInFollowers = Some(15000),
              companyFoundedYear = Some(1995),
              recentProfessionalAchievements = Some("Rural Healthcare Innovation Award"),
              educationalBackground = Some("MPH, Healthcare Management"),
              sharedConnections = List("Rural Healthcare Alliance"),
              publishedArticles = List("Rural Healthcare Challenges"),
              conferenceAppearances = List("Rural Health Summit"),
              socialCauses = List("Rural Healthcare Access", "Sustainability"),
              awards = List("Community Impact Award"),
              patentsField = List(),
              previousCompanies = List("Community Health Systems"),
              commonInterests = List("Rural Healthcare", "Renewable Energy")
            ),

            /*
            // 7. Rehabilitation Hospital Director
            ProspectEnrichmentData(
              firstName = "David",
              lastName = "Garcia",
              company = "Recovery Medical Center",
              designation = "Facility Director",
              companySize = Some(600),
              industry = Some("Healthcare"),
              companyLocation = Some("Phoenix, AZ"),
              companyFunding = Some("Private"),
              companyTechnology = List("Rehabilitation Equipment", "EMR Systems"),
              companyRevenue = Some("$100M-$200M"),
              companyGrowthRate = Some("9% YoY"),
              recentNewsAndAnnouncements = List(
                "Rehabilitation Wing Expansion",
                "Energy Conservation Project"
              ),
              companyLinkedInFollowers = Some(20000),
              companyFoundedYear = Some(2005),
              recentProfessionalAchievements = Some("Healthcare Facility Innovation"),
              educationalBackground = Some("BS Healthcare Administration"),
              sharedConnections = List("Rehabilitation Centers Network"),
              publishedArticles = List("Modern Rehabilitation Facilities"),
              conferenceAppearances = List("Rehabilitation Healthcare Summit"),
              socialCauses = List("Patient Recovery", "Environmental Impact"),
              awards = List("Facility Excellence Award"),
              patentsField = List(),
              previousCompanies = List("Select Medical", "Kindred Healthcare"),
              commonInterests = List("Rehabilitation Innovation", "Sustainable Healthcare")
            ),

            // 8. Mental Health Hospital Executive
            ProspectEnrichmentData(
              firstName = "Jennifer",
              lastName = "Taylor",
              company = "Behavioral Health Institute",
              designation = "Operations Head",
              companySize = Some(400),
              industry = Some("Healthcare"),
              companyLocation = Some("Seattle, WA"),
              companyFunding = Some("Non-profit"),
              companyTechnology = List("Mental Health EMR", "Facility Management Systems"),
              companyRevenue = Some("$75M-$150M"),
              companyGrowthRate = Some("7% YoY"),
              recentNewsAndAnnouncements = List(
                "Mental Health Wing Addition",
                "Facility Modernization"
              ),
              companyLinkedInFollowers = Some(18000),
              companyFoundedYear = Some(2010),
              recentProfessionalAchievements = Some("Mental Healthcare Excellence"),
              educationalBackground = Some("MSW, Healthcare Management"),
              sharedConnections = List("Mental Health Facilities Network"),
              publishedArticles = List("Modern Mental Healthcare Facilities"),
              conferenceAppearances = List("Mental Health Facilities Summit"),
              socialCauses = List("Mental Health Awareness", "Sustainable Operations"),
              awards = List("Healthcare Innovation Award"),
              patentsField = List(),
              previousCompanies = List("Universal Health Services"),
              commonInterests = List("Mental Healthcare", "Facility Efficiency")
            ),

            // 9. Emergency Care Network Director
            ProspectEnrichmentData(
              firstName = "William",
              lastName = "Anderson",
              company = "Emergency Care Network",
              designation = "Head of Purchase",
              companySize = Some(1800),
              industry = Some("Healthcare"),
              companyLocation = Some("Miami, FL"),
              companyFunding = Some("Private Equity"),
              companyTechnology = List("Emergency Medical Systems", "Advanced Life Support"),
              companyRevenue = Some("$400M-$600M"),
              companyGrowthRate = Some("11% YoY"),
              recentNewsAndAnnouncements = List(
                "Emergency Response Center Expansion",
                "Backup Power Systems Upgrade"
              ),
              companyLinkedInFollowers = Some(35000),
              companyFoundedYear = Some(1998),
              recentProfessionalAchievements = Some("Emergency Care Innovation Award"),
              educationalBackground = Some("MBA Healthcare"),
              sharedConnections = List("Emergency Care Network"),
              publishedArticles = List("Emergency Healthcare Management"),
              conferenceAppearances = List("Emergency Care Summit"),
              socialCauses = List("Emergency Healthcare Access", "Environmental Impact"),
              awards = List("Healthcare Excellence Award"),
              patentsField = List(),
              previousCompanies = List("HCA Healthcare", "Tenet Healthcare"),
              commonInterests = List("Emergency Medicine", "Sustainable Operations")
            ),

            // 10. Cancer Treatment Center Executive
            ProspectEnrichmentData(
              firstName = "Rachel",
              lastName = "Kim",
              company = "Comprehensive Cancer Center",
              designation = "Operations Director",
              companySize = Some(900),
              industry = Some("Healthcare"),
              companyLocation = Some("Baltimore, MD"),
              companyFunding = Some("Research Grants"),
              companyTechnology = List("Cancer Treatment Equipment", "Research Systems"),
              companyRevenue = Some("$250M-$400M"),
              companyGrowthRate = Some("13% YoY"),
              recentNewsAndAnnouncements = List(
                "Treatment Center Expansion",
                "Renewable Energy Implementation"
              ),
              companyLinkedInFollowers = Some(40000),
              companyFoundedYear = Some(2008),
              recentProfessionalAchievements = Some("Healthcare Operations Excellence"),
              educationalBackground = Some("PhD Healthcare Management"),
              sharedConnections = List("Cancer Treatment Centers Network"),
              publishedArticles = List("Modern Cancer Care Facilities"),
              conferenceAppearances = List("Cancer Care Summit 2023"),
              socialCauses = List("Cancer Research", "Environmental Sustainability"),
              awards = List("Healthcare Innovation Excellence"),
              patentsField = List(),
              previousCompanies = List("Johns Hopkins", "Dana-Farber"),
              commonInterests = List("Cancer Treatment", "Sustainable Healthcare")
            )
             */
          )

          // Define campaign contexts
          val campaignContexts = List(
            // 1. Solar Panel Solutions for Hospitals
            CampaignOffer(
              offerDetails = "Free installation and first-year maintenance of solar panel system with 24/7 emergency support",
              targetPersona = "Head of Purchase/Commercial Operations",
              valueProposition =
                """
                  |1. Reduce electricity costs by 30-40%
                  |2. Ensure uninterrupted power supply for critical medical equipment
                  |3. Meet sustainability goals with green energy
                  |4. Zero upfront costs with immediate cost savings
                  |""".stripMargin
            ),
            // 2. SaaS Product for Software Companies
            CampaignOffer(
//              offerType = "Premium Trial",
              offerDetails = "2 months free access to enterprise features with dedicated support",
              targetPersona = "VP of Engineering",
              valueProposition = "Improve development efficiency by 40% with AI-powered code reviews and automated quality checks"
            )
          )


           */

          // Campaign level data,
          /*
            We help (head of operations) in (hospitals and healthcare) to (reduce electricity costs and ensure uninterrupted power supply.)
            by (providing solar panels that can charge generators and pass back supply to the grid and lowering your electricity bill).
           My campaign offer is (Free installation and first-year maintenance of solar panel system with 24/7 emergency support).
             With this campaign, I'm reaching out because (prospect's company raised funds) . Our prospects are (Head). I want the campaign written in
             (English) with a (conversational) tone with (2 follow-up steps)
           */

          /*
            In my prospects level data I want to consider these 5 columsn.

          - first_name,
          - company name,
          - designation,
          - companyFunding,
          - companyTechnology ,
          - companyRevenue,
          - companyGrowthRate

           */

          // Define step sequences for each campaign
          val campaignSteps = Map(
//            "solar_panel" -> List(
//              StepContext(1, 4, "email",
//                "Schedule initial assessment call",
//                "Highlight rising electricity costs and power reliability issues in healthcare"
//              ),
//              StepContext(2, 4, "email",
//                "Share hospital success story",
//                "Present case study of similar hospital's success with solar implementation"
//              ),
//              StepContext(3, 4, "email",
//                "Offer custom savings calculation",
//                "Propose free energy audit and ROI calculation"
//              ),
//              StepContext(4, 4, "email",
//                "Schedule solution presentation",
//                "Present complete solution with implementation timeline"
//              )
//            ),
//            "saas_product" -> List(
//              StepContext(1, 4, "linkedin_connection",
//                "Connect with personalized note",
//                "Establish initial connection"
//              ),
//              StepContext(2, 4, "email",
//                "Share relevant content",
//                "Engage with development efficiency pain points"
//              ),
//              StepContext(3, 4, "linkedin_message",
//                "Share case study",
//                "Demonstrate success metrics from similar companies"
//              ),
//              StepContext(4, 4, "email",
//                "Schedule demo",
//                "Show platform capabilities live"
//              )
//            )
          )

          // Generate communications for all prospects
//          val results = for {
//            allCommunications <- Future.sequence(
//              testProspects.map { prospect =>
//                // Determine appropriate campaign based on prospect's industry/role
//                val campaign = prospect.industry.map(_.toLowerCase) match {
//                  case Some(industry) if industry.contains("healthcare") || industry.contains("hospital") =>
//                    campaignContexts.head // Solar Panel campaign
//                  case _ =>
//                    campaignContexts.head // SaaS Product campaign
//                }
//
//                aiHyperPersonalizedGenerator.generateCampaignSequence3(prospect, campaign, OrgId(23))
//              }
//            )
//          } yield {
//            // Print results for each prospect
//            testProspects.zip(allCommunications).foreach {
//              case (prospect, communications) =>
//                println(s"\nSequence for ${prospect.firstName} ${prospect.lastName} at ${prospect.company}")
//                println("=" * 80)
//
//                communications.foreach { comm =>
//                  println(s"\nStep ${comm.stepNumber} (${comm.channel}):")
//                  println("-" * 40)
//                  println(comm.content)
//
//                  // Print any responses if they exist
//                  comm.response.foreach { response =>
//                    println("\nResponse:")
//                    println(response)
//                  }
//                }
//                println("=" * 80)
//            }
//            allCommunications
//          }

          // Handle the results
//          Await.ready(results, Duration.Inf).value match {
//            case None =>
//              Logger.error("Generation timeout")
//              println("Generation timeout")
//
//            case Some(Failure(exception)) =>
//              Logger.error("Error generating sequences", exception)
//              println(s"Error: ${exception.getMessage}")
//
//            case Some(Success(generatedSequences)) =>
//              Logger.info(s"Successfully generated ${generatedSequences.length} sequences")
//              println(s"Successfully generated ${generatedSequences.length} sequences")
//          }

          // Log completion
//          Logger.info(s"Successfully generated sequences for ${testProspects.length} prospects")


        case "generate_content_function" =>

          val campaignId: Long = 3
          val campaignOwnerId = 2
          val teamId: Long = 505
          val prospectId = 7L
          val prospectOwnerId = 2
          val aDate = DateTime.now()

          val prospectObjectInternal = ProspectObjectInternal(

            owner_name = "Dhruv",
            owner_email = "<EMAIL>",

            email_domain = Some("smartreach.io"),
            invalid_email = None,

            last_contacted_at = Some(aDate),
            last_replied_at = None,
            last_opened_at = None,
            last_call_made_at = None,

            list_id = None,

            prospect_category_id_custom = 1,
            prospect_category_label_color = "red",

            prospect_source = None,


            prospect_account_id = None, //table -> prospect_accounts.id
            prospect_account_uuid = None,
            prospect_account = None, //table -> prospect_accounts.name


            total_opens = 0,
            total_clicks = 0,

            active_campaigns = None,

            /* only in case of specific campaign prospect table in frontend */
            current_campaign_id = None,

            magic_columns = List.empty,

            tags = None,
            flags =Json.obj(),
            latest_reply_sentiment = None
          )

          val prospectObject = ProspectObject(
            id = prospectId,
            owner_id = prospectOwnerId,
            team_id = teamId,

            first_name = Some("Shashank Prospect"),
            last_name = Some("dwivedi prospect"),

            email = Some("<EMAIL>"),

            custom_fields = Json.obj(),

            list = None,

            job_title = None,
            company = None,
            linkedin_url = Some("https://linkedin.com/in/aditya-sadana"),
            phone = None,
            phone_2 = None,
            phone_3 = None,

            city = None,
            state = None,
            country = None,
            timezone = None,

            prospect_category = "categoryProspect", // display name

            last_contacted_at = None,
            last_contacted_at_phone = None,

            created_at = aDate,


            /* internal columns only for smartreach website, not for public api */
            internal = prospectObjectInternal,
            latest_reply_sentiment_uuid = None,
            current_step_type = None,
            latest_task_done_at = None,
            prospect_uuid = None,
            owner_uuid = AccountUuid("acc_aa_abcdegfhi"),
            updated_at = aDate
          )

          val campaignForScheduling: CampaignForScheduling.CampaignForSchedulingEmail = models.CampaignForScheduling.CampaignForSchedulingEmail(
            campaign_id = campaignId,
            campaign_owner_id = campaignOwnerId,
            team_id = teamId,
            org_id = 1,
            campaign_name = "Animesh Kumar",
            ai_generation_context = Some(CampaignAIGenerationContext(
              team_type = "sales",
              industry = "indusrry",
              motive = "motive",
              solution = "solution",
              reason_of_reaching_out = "reason_of_reaching_out",
              prospects_designation = "prospects_designation",
              language = "language",
              tone = "tone",
              campaign_offer = "campaign_offer",
              max_number_of_steps = 1
            )),
            status = CampaignStatus.RUNNING,
            campaign_type_data = CampaignTypeData.MultiChannelCampaignData(head_step_id = 3),
            sending_holiday_calendar_id = Some(123),
            campaign_email_setting = CampaignEmailSettingForScheduler(
              sender_email_settings_id = 1,
              receiver_email_settings_id = 1,
              campaign_email_settings_id = CampaignEmailSettingsId(123),
              emailServiceProvider = EmailServiceProvider.OTHER
            ),
            append_followups = false,
            open_tracking_enabled = false,
            click_tracking_enabled = false,
            opt_out_msg = "opt out {{unsubscribe_link}}",
            opt_out_is_text = false,
            timezone = "IN",
            daily_from_time = 1,
            daily_till_time = 1,
            days_preference = List(),
            email_priority = CampaignEmailPriority.EQUAL,
            max_emails_per_prospect_per_day = 100,
            max_emails_per_prospect_per_week = 1000,
            max_emails_per_prospect_account_per_day = 100,
            max_emails_per_prospect_account_per_week = 1000,
            campaign_max_emails_per_day = 1000,
            softstart_setting = None,
            mark_completed_after_days = 1,
            latest_email_scheduled_at = None,
            from_email = "<EMAIL>",
            from_name = "Rachel",
            reply_to_email = "<EMAIL>",
            reply_to_name = "Monica",
            min_delay_seconds = 1,
            max_delay_seconds = 1,
            enable_email_validation = true,
            rep_mail_server_id = 1,
            via_gmail_smtp = None,
            prospects_remaining_to_be_scheduled_exists = Some(true),
            count_of_sender_emails = 1,
            selected_calendar_data = None
          )

          val emailStep = AutoEmailStep(
            subject = "variant subject",
            body = "Variant body",
          )

          val campaignStepVariantForScheduling = CampaignStepVariantForScheduling(
            id = 1,
            step_id = 3,
            campaign_id = campaignId,
            template_id = None,
            step_data = emailStep,
            step_label = None,
            step_delay = 10,
            notes = Some("Test Notes"),
            priority = Some(Normal),
            active = true,
            scheduled_count = 1
          )

          val internalMergeTagValuesForProspect = InternalMergeTagValuesForProspect(
            sender_name = "Monica Geller",
            sender_first_name = "Monica",
            sender_last_name = "Geller",
            unsubscribe_link = Some("dummy_link"),
            previous_subject = None,
            signature = None,
            sender_phone_number = None,
            calendar_link = None
          )

          val campaignStepWithChildren = CampaignStepWithChildren(
            id = 3,
            label = None,
            campaign_id = campaignId,
            delay = 10,
            step_type = CampaignStepType.AutoEmailStep,
            created_at = DateTime.parse("2022-03-21T11:58:03.294Z"),
            children = List(2, 3, 4),
            variants = Seq(
              campaignStepVariantForScheduling
            )
          )

          val prospectForSchedulingEmail = ProspectDataForChannelScheduling.EmailChannelProspectForScheduling(
            prospect = prospectObject,
            current_step_status_data = None,
            current_step_id = None, // make it some values and then check
            email_checked = false,
            email_sent_for_validation = false, // can try making it true
            email_sent_for_validation_at = None, // when above one is true, set this too.
          )

          val prospectForSchedulingLinkedin = ProspectDataForChannelScheduling.LinkedinChannelProspectForScheduling(
            prospect = prospectObject,
            current_step_status_data = None,
            current_step_id = None // make it some values and then check
          )

          val stepType = StepType(
            step_type_value = CampaignStepType.AutoEmailStep.toKey
          )

          val channelStepTypeDataForScheduling = emailChannelScheduler.ChannelStepTypeDataForScheduling(
            campaignStepType = CampaignStepType.AutoEmailStep,
            totalScheduledForStepTypeTillNow = 0,
            channelStepTypeDailyLimit = 7,
            remainingToBeScheduledFromChannelStepType = 11,
            campaignStepTypeLimitHasBeenReachedForToday = false,
            stepType = stepType
          )

          val prospectsFoundForSchedulingByStepType = emailChannelScheduler.ProspectsFoundForSchedulingByStepType(
            prospects = List(prospectForSchedulingEmail),
            step_type = channelStepTypeDataForScheduling
          )

          val prospectFoundForSchedulingByStepType = emailChannelScheduler.ProspectFoundForSchedulingByStepType(
            prospectForScheduling = prospectForSchedulingEmail,
            step_type = channelStepTypeDataForScheduling
          )

          val prospectsFoundByStepType: emailChannelScheduler.ProspectsFoundByStepType = Map(
            CampaignStepType.AutoEmailStep -> prospectsFoundForSchedulingByStepType
          )

          val flattenedProspects = emailChannelScheduler.flattenProspectsFoundForSchedulingByStepType(
            prospectsFoundByStepType = prospectsFoundByStepType
          )


          val stepsMappedById = Map(3L -> campaignStepWithChildren)

          val scheduleCampaign = emailChannelScheduler.ScheduleCampaign(
            markedCompletedIds = Seq(),
            campaign = campaignForScheduling,
            stepsMappedById = stepsMappedById,
            campaignStepsStructure = CampaignStepsStructure.MultichannelCampaignStepsStructure(
              orderedStepIds = Vector(3)
            ),
            prospects = flattenedProspects,
            distinctTimezones = Set()
          )

          val generateScheduleTaskData = emailChannelScheduler.GenerateScheduleTaskData(
            currentCampaign = scheduleCampaign,
            nextStep = campaignStepWithChildren,
            currentProspect = prospectFoundForSchedulingByStepType,
            currentVariant = campaignStepVariantForScheduling,
            schedulerDateTime = aDate.plusSeconds(60),
          )


//         val res =  aiHyperPersonalizedGenerator.generateContentForTask(
//           step_id = StepId(id = generateScheduleTaskData.currentVariant.step_id),
//           step_data = generateScheduleTaskData.currentVariant.step_data,
//           prospect_object = generateScheduleTaskData.currentProspect.prospectForScheduling.prospect,
//           campaign_ai_generation_context = generateScheduleTaskData.currentCampaign.campaign.ai_generation_context,
//           campaign_id = CampaignId(id = generateScheduleTaskData.currentCampaign.campaign.campaign_id),
//           sender_email_settings_id = EmailSettingId(emailSettingId = generateScheduleTaskData.currentCampaign.campaign.campaign_email_setting.sender_email_settings_id),
//             previous_communications = Seq(),
//             orderedSteps = Seq(),
//            orgId = OrgId(id = 2)
//          )

//          Await.ready(res, Duration.Inf)
        case "hyper_personalized_communication_v2" =>
          // Create a sample request with realistic test data for an email step
          val standardFields = StandardProspectFields(
            firstName = Some("Alex"),
            lastName = Some("Johnson"),
            email = Some("<EMAIL>"),
            jobTitle = Some("VP of Engineering"),
            company = Some("TechCorp"),
            linkedinUrl = Some("https://linkedin.com/in/alexjohnson"),
            phone = None,
            city = Some("Boston"),
            state = Some("MA"),
            country = Some("USA"),
            timezone = None
          )

          val campaignContext = CampaignAIGenerationContext(
            prospects_designation = "VP of Engineering",
            solution = "DevOps Automation Platform",
            reason_of_reaching_out = "Streamline deployment processes",
            campaign_offer = "Free DevOps assessment",
            team_type = "Engineering Leadership",
            industry = "Technology",
            motive = "Improve deployment efficiency",
            max_number_of_steps = 4,
            language = "english",
            tone = "formal"
          )

          val stepContext = StepContext(
            stepNumber = 1,
            totalSteps = 4,
            channel = EmailChannel, // Specifically testing an email channel
            callToAction = "Schedule a 20-minute discovery call",
            step_context = "Initial outreach introducing our DevOps automation platform"
          )

          val res = srAiApi.generateHyperPersonalizedCommunicationV2(
            prospectData = new ProspectFieldsResult(standardFields, customFields = Map(
              "deployment_frequency" -> Some("Weekly"),
              "team_size" -> Some("50-100 engineers"),
              "current_stack" -> Some("Jenkins, GitHub")
            )),
            campaignContext = campaignContext,
            stepContext = stepContext,
            previousCommunications = List(), // Empty for first communication
            orgId = OrgId(0)
          )

          Await.ready(res, Duration.Inf).value match {
            case None =>
              println("Hyper-Personalized Communication V2 - None")

            case Some(Failure(exception)) =>
              println(s"Hyper-Personalized Communication V2 Failure - ${LogHelpers.getStackTraceAsString(exception)}")

            case Some(Success(llmResponseData)) =>
              println("Hyper-Personalized Communication V2 Success:")
              
              // Extract subject and body from LlmResponseData output
              val subject = (llmResponseData.output \ "final_output" \ "subject").asOpt[String]
                .orElse((llmResponseData.output \ "subject").asOpt[String])
                .getOrElse("Generated Subject")
              
              val body = (llmResponseData.output \ "final_output" \ "body").asOpt[String]
                .orElse((llmResponseData.output \ "body").asOpt[String])
                .getOrElse("Generated Body")
              
              println(s"Subject: $subject")
              println(s"Body: $body")
              
              // Print additional audit information if available
              val promptInfo = (llmResponseData.prompt \ "system_prompt").asOpt[String]
                .map(sp => s"System Prompt Length: ${sp.length} characters")
                .getOrElse("No system prompt info")
              println(s"Audit Info: $promptInfo")
          }


      }

    }

  }

}
