package utils.testapp

import api.accounts.TeamId
import play.api.Logging
import scalikejdbc.config.DBs

object FindEmailToReplyToV3 extends Logging with TestAppTrait {

  def main(args: Array[String]): Unit = {

    if (args.length > 0) {
      DBs.setupAll()

      val applicationName = args(0)

      applicationName match {

        case "test_findEmailToReplyToV3" =>

          val emailThreadId = 783860L
          val teamId = TeamId(id = 80)

          val result = emailScheduledDAO.findEmailToReplyToV3(emailThreadId = emailThreadId, teamId = teamId)
          println(result)
      }
    }

  }

}