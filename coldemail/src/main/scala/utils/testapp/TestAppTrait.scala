package utils.testapp

import org.apache.pekko.actor.ActorSystem
import org.apache.pekko.stream.Materializer
import common_auth_middleware.{CommonAuthApiDI, CommonAuthServiceDI, UserRedisKeyService_DI}
import io.sr.llm.{AwsBedrockLlama3Api_DI, SrLLMService_DI}
import play.api.libs.ws.ahc.AhcWSClient
import utils.Helpers
import utils.dependencyinjectionutils.Calendar.{CalendarAppApiDI, CalendarAppDaoDI, CalendarAppServiceDI}
import utils.dependencyinjectionutils.ChannelSchedulers.{EmailMessageDataDAO_DI, EmailScheduledDAOService_DI, EmailSchedulerJedisService_DI, SrRedisHashSetBasedLockServiceV2_DI, SrRedisHashSetServiceV2_DI, SrRedisSimpleLockServiceV2_DI}
import utils.dependencyinjectionutils.*
import utils.dependencyinjectionutils.Campaign.{CampaignStepServiceDI, CampaignTestStepServiceDI}
import utils.dependencyinjectionutils.DripHealthCheck.{DripCampaignHealthCheckDAO_DI, DripCampaignHealthCheckService_DI}
import utils.dependencyinjectionutils.Intercom.{MQReadOldEmailForSync_DI, MqCampaignSpamMonitorService_DI, OldEmailSyncService_DI}
import utils.dependencyinjectionutils.LinkedinSettingDI.{LinkedinSettingServiceDI, MqCaptainDataCookieFailurePublisherDI, PlanLimitServiceDI}
import utils.dependencyinjectionutils.ProductOnboardingDI.ProductOnboardingDAO_DI
import utils.dependencyinjectionutils.ProductOnboardingDI.RollingUpdate.SrRollingUpdateCoreService_DI
import utils.dependencyinjectionutils.SmsSettingDI.SmsSettingDAODI
import utils.dependencyinjectionutils.SpamTest.SpamTestDAO_DI
import utils.dependencyinjectionutils.Tasks.{TaskDAO_DI, TaskDaoServiceDI, TaskServiceDI}
import utils.dependencyinjectionutils.Team.*

import scala.concurrent.ExecutionContext

object TestAppExecutionContext {


  // we do not want to create a separate execution context for each Spec during the tests
  // as that causes OutOfMemoryErrors. Therefore, we use a single ExecutionContext here defined
  // inside a singleton object
  lazy val ec: ExecutionContext = Helpers.genFixedThreadPoolEC(threads = 500)


  lazy val actorSystem: ActorSystem = ActorSystem()

  lazy val actorMaterializer: Materializer = Materializer(actorSystem)

  lazy val wsClient: AhcWSClient = AhcWSClient()(actorMaterializer)

}

trait TestAppTrait extends


  /* 18-Jun-2025: Do not add these traits here, since the TestAppTrait is
  added to all the test specs classes, and these end up creating a separate
  actor systems for each spec. This can cause out of memory errors.

    with WorkerActorSystemDI
    with WorkerWSClientDI
    with WorkerActorMaterializerDI

  */


  CreateInCRMJedisDAO_DI
//  with MQOpenTrackerDI
  with ProspectFeedDAO_DI
  with SrMailServerDao_DI
  with SrMailServerServiceDI
  with SrMailServerRedisService_DI
  with CampaignProspectStepScheduleLogsDAO_DI
  with MQOpenTrackerPublisher_DI
  with AccountInvitationServiceDI
  with AccountAddServiceDI
  with EmailValidationDAOService_DI
  with MQCampaignAISequenceGeneratorDI
  with MQDomainServiceProviderDNSService_DI
  with WorkflowCrmSettingsServiceDI
  with SrDNSUtil_DI
  with MQActivityTriggerPublisher_DI
  with MqUpdateProspectCategoryForGivenTeamOfOrgForGlobalDNC_DI
  with BlacklistServiceV2_DI
  with CampaignAISequenceServiceDI
  with EmailValidationApiToolsRecordDAODI
  with MQClickTrackerDI
  with ReportDAO_DI
  with GDPRDAO_DI
  with ProspectEmailsDAOService_DI
  with OrganizationBillingDAO_DI
  with TopReportsDaoJedisServiceDI
  with DomainPublicDNSDAO_DI
  with MqResetCacheForReportDI
  with SpamTestDAO_DI
  with ReportDaoJedisServiceDI
  with PhantomBusterAgentServiceDI
  with DeBounceEmailValidationApiDI
  with ListCleanEmailValidationApiDI
  with UserCheckApiServiceDI
  with LlmAuditLogDAO_DI
  with CloudStorageDI
  with GptApiService_DI
  with EmailAccountServiceDI
  with MqUpdateLatestTaskDoneOrSkippedAtDI
  with WorkflowCrmSettingsDAO_DI
  with EmailAccountTestServiceDI
  with EmailSenderServiceDI
  with LinkedinSessionCreatorDI
  with BrightDataServiceDI
  with BrightDataApiDI
  with PlanLimitServiceDI
  with AccountOrgBillingRelatedServiceDI
  with AccountOrgBillingRelatedInfoDAODI
  with CustomerSupportReadOnlyServices_DI
  with GeneralModuleServiceDI
  with MqInsertTeamsMetadataDI
  with TeamsMetadataServiceDI
  with TeamsMetadataDAODI
  with GeneralModuleDI
  with SrRedisSimpleLockServiceV2_DI
  with GeneralSettingServiceDI
  with PhantomBusterProxyDAO_DI
  with PhantomBusterProxyServiceDI
  with PhantomBusterAgentsDAODI
  with ProspectTagServiceDI
  with MqAiContentGenerationPublisher_DI
  with PhantomBusterExecutionLogsDAO_DI
  with PhantomBusterExecutionLogsServiceDI
  with PhantomBusterApiKeysServiceDI
  with PhantomBusterApiKeysDAODI
  with ProspectTagDAO_DI
  with MqRefactorLinkedinUrlMigration_DI
  with CampaignProspectService2DI
  with SrUuidUtilsDI
  with LinkedinTaskServiceDI
  with MQUnsubscriberTrackerDI
  with MQDoNotContactDI
  with MQDoNotContactPublisher_DI
  with MQProspectDeleterDI
  with MQWebhookEmailInvalidDI
    with CalendlyOAuthDI
  with LinkedinMessagesServiceDI
  with LinkedinMessagesDAODI
  with LinkedinMessageThreadsServiceDI
  with LinkedinConnectionsServiceDI
  with LinkedinConnectionDAODI
  with LinkedinMessageThreadsDAODI
  with DeleteService_DI
  with EmailSettingDAOServiceDI
  with EmailSettingJedisServiceDI
  with LinkedinSettingServiceDI
  with CaptainDataServiceDI
  with MqCaptainDataConversationExtractionPublisher_DI
  with MqCaptainDataCookieFailurePublisherDI
  with MqCaptainDataMessageExtractionPublisher_DI
  with Template.TemplateUtilsDI
  with CampaignStepServiceDI
  with GPTServiceDI
  with GPTInboxServiceDI
  with AISequenceGeneratorDI
  with SrRateLimiterDI
  with SrRateLimiterDAO_DI
  with TemplateDAO_DI
  with GPTApiDI
  with MQWebhookCompletedDI
  with ChannelSchedulers.LinkedinChannelSchedulerDI
  with LinkedinSettingDAO_DI
  with CampaignDAOService_DI
  with SrRandomUtilsDI
  with SrShuffleUtilsDI
  with ReportServiceDI
  with CampaignTagDAO_DI
  with OpportunityService_DI
  with OpportunityDAO_DI
  with LeadFinderMetaDataDAO_DI
  with OpportunityDAOService_DI
  with OpportunityJedisDAO_DI
  with OpportunityStatusCommonService_DI
  with AnnualizedValueService_DI
  with ReportDaoService_DI
  with DownloadReportDAO_DI
  with CampaignSendReportsDAO_DI
  with RolePermissionDataDAOV2_DI
  with MQRepliedEventDI
  with CampaignProspectTimezonesJedisServiceDI
  with MQProspectCompletedEventDI
  with MQProspectUnPauseEventDI
  with MQListStatsUpdaterDI
//  with ScyllaCluster_DI
//  with ScyllaDbConnection_DI
  with ReplyFilteringServiceDI
  with EmailReplyTrackingModelV2DI
  with MqSrAiApiPublisher_DI
  with CampaignTemplateServiceDI
  with EmailServiceDI
  with OutlookApiSendEmailServiceDI
  with OutlookApiReplyTrackingServiceDI
  with GmailEmailApiDI
  with GmailApiReplyTrackingServiceDI
  with GmailApiSendEmailServiceDI
//  with CacheEventScyllaService_DI
//  with CacheEventScyllaDAO_DI
  with GmailEmailSendServiceDI
  with GmailSmtpEmailApiDI
  with OutlookEmailApiDI
//  with ScyllaRunSafely_DI
  with MicrosoftOAuthDI
  with SendGridServiceDI
  with SendGridEmailApiDI
  with SmtpImapSendEmailServiceDI
  with SmtpImapReplyTrackingServiceDI
  with SmtpImapEmailApiDI
  with GoogleOAuthDI
  with EmailHandleErrorServiceDI
  with MqCampaignSchedulingMetadataMigration_DI
  //    with MQMBLEmailValidatorDI
  with MqIPBlacklistCheckDI
  with SrBlackListCheckCron_DI
  with NewUserDripCamapignServiceDI
  with WebhookDAO_DI
  with WebhookUtils_V2_DI
  with ProspectQueryDI
  with AccountDAO_DI
  with SrUserFeatureUsageEventServiceDI

  with MqOrgDataDeleterDI
  with MigrationUtils_DI
  with MigrationDAO_DI
  with MigrationService_DI

  with BlacklistServiceDI
  with BlacklistDAO_DI

  with RepTrackingHostServiceDI


  with MQTriggerDI
  with ProspectAddEventDAO_DI
  with CampaignProspectDAO_DI
  with DBCounterDAO_DI
  with SrDBQueryCounterService_DI
  with CampaignDAO_DI
  with TriggerDAO_DI
  with EmailScheduledDAO_DI
  with EmailScheduledServiceDI
  with EmailSettingDAO_DI
  with ProspectUpdateCategoryTemp_DI
  with ProspectUpdateCategoryTemp2_DI
  with EmailThreadDAO_DI
  with EmailReplyTrackingModelDI
  with TriggerServiceDI
  with HandlePushTriggerEventServiceDI
  with HandleSyncTriggerEventServiceDI
  with LeadStatusServiceDI
  with SyncTriggerMQServiceDI
  with HandleActivityTriggerEventServiceDI
  with ActivityTriggerMQServiceDI
  with HandleAddToDNCTriggerEventServiceDI
  with CampaignCacheServiceDI
  with CampaignsMissingMergeTagServiceDI
    with AgencyDashboardCacheServiceDI
  with CacheServiceJedisDI
  with EmailNotificationServiceDI
  with EmailNotificationDAO_DI
  with MailgunServiceDI
  with MailgunEmailApiDI
  with AccountServiceDI
  with SrResourceDI.SrResourceDaoServiceDI
  with SrResourceDI.SrResourceWithoutTenantDaoServiceDI
  with SrResourceDI.SrCacheDI
  with SrUuidInternalDataServiceDI
  with ProspectServiceDI
  with CampaignProspectServiceDI
  with DripLogJedisDAO_DI
    with EmailSettingTagsJedisServiceDI
  with CampaignServiceDI
  with InternalSchedulerRunLogDAO_DI
  with CampaignSendingVolumeLogsDAO_DI
  with TransformAndSaveEmailValidationResultDI
  with EmailValidationBatchRequestModelDI
  with EmailValidationModelDI
  with EmailDeliveryAnalysisServiceDI


  with ProspectAccountDAO1DI
  with AccountDAOV2_DI
  with OrganizationDAO_DI
  with FreeEmailDomainListService_DI
  with FreeEmailDomainListDAO_DI

  with EventLogDAO_DI
  with EventLogService_DI
  with EventLogFindServiceDI
  with WorkflowAttemptDAO_DI
  with WorkFlowAttemptService_DI
  with AccessTokenService_DI
  with ClientAccountAccessLogDAO_DI
  with UserRedisKeyService_DI
  with EmailValidationServiceDI
  with BouncerEmailValidationApiDI
  with TagServiceDI
  with ProspectUploadServiceDI
  with UploadCsvCronServiceDI
  with SRCsvParserUtilsDI
  with CustomCSVParserDI
  with UnivocityParserDI
  with SRCsvReaderUtilsDI
  with ProspectColumnDef_DI
  with ProspectColumnDefDAO_DI
  with ProspectsEmailsDAO_DI
  with ProspectServiceV2_DI
  with ResetUserCacheUtilDI
  with AccountDAOService_DI
  with CacheServiceDI
  with EmailMessageContactModel_DI
  with TeamsDAO_DI
  with DBUtils_DI
  with EmailSendingStatusDAO_DI
  with SrInternalFeatureUsageDaoDI
  with BlacklistProspectCheckDAO_DI
  with CampaignProspectDAO_2DI
  with GenerateTempId_DI
  with Intercom.MqIntercomUpdaterDI
  with Intercom.IntercomUpdateServiceDI
  with Intercom.IntercomApiDI
  with TemplateServiceDI
  with EmailServiceCompanionDI
  with CampaignStepVariantDAO_DI
  with CampaignStepDAO_DI
  with CampaignEditedPreviewEmailDAO_DI
  with PhishingCheckServiceDI
  with MqCampaignSpamMonitorService_DI
  with SpamMonitorService_DI
  with DomainInfoWhoisDAO_DI
  with DomainDataService_DI
  with ApiLayerAPIService_DI
  with RapidAPIService_DI
  with EmailSendingStatusService_DI
  with SupportAccessToUserAccountDAO_DI
  with MqUnSnoozeSnoozedTasks_DI
  with TaskDAO_DI
  with MqUpdateSubAccountCallingCredit_DI
  with CallServiceDI
  with LeadFinderDAO_DI
  with PusherServiceDI
  with TwilioDialerServiceDI
  with MqWhatsAppChannelScheduler_DI
  with WhatsAppChannelSchduler_DI
  with WhatsappSettingDI.WhatsappSettingDAODI
  with TaskServiceDI
  with TaskDaoServiceDI
//  with Tasks.TaskCacheDAO_DI
//  with Tasks.TaskCacheService_DI
//  with EventFramework.SrEventServiceDI
//  with EventFramework.KafkaServiceDI
  with MQGeneralChannelSchedulerDI
  with ChannelSchedulers.GeneralChannelSchedularDI
  with GeneralSettingDAO_DI
  with MQLinkedinChannelSchedulerDI
  with MqUpdateCampaignSchedulingMetadata_DI
  with MQReadOldEmailForSync_DI
  with TeamInboxDAO_DI
  with TeamInboxService_DI
  with LinkedinTeamInboxDAO_DI
  with OldEmailSyncService_DI
  with MqCampaignStepContentCheckServiceDI
  with ReplyTrackerService_DI
  with MqHandleEventLogDI
  with EmailReplyTrackingDAOService_DI
  with ReplySentimentService_DI
  with ReplySentimentLLMDAO_DI
  with ReplySentimentDAO_DI
  with ReplySentimentJedisDAO_DI
  with MergeTagService_DI
  with ProspectDAOService_DI
  with ProspectDAOServiceV2_DI
  with CampaignProspectDAOService_DI
  with OrganizationDAOService_DI
  with ProspectDAO_DI
  with ReplySentimentDAOService_DI
  with AssociateProspectToEmailThreadService_DI
  with MQAssociateProspectToOldEmails_DI
  with HubspotApiDI
  with HubSpotOAuth_DI
  with PipedriveOAuth_DI
  with SalesforceOAuth_DI
  with ZohoOAuth_DI
  with ZohoApiDI
  with SalesForceApiDI
  with PipedriveApiDI
  with ZohoRecruitOAuth_DI
  with IntegrationTypeService_DI
  with SRTriggerAllowedCombos_DI
  with TriggerServiceV2_DI
  with TriggerJedisDAO_DI
  with TriggerDAOService_DI
  with TIntegrationCRMService_DI
  with InboxV3ServiceDI
  with LinkedinMessagesDAOService_DI
  with EmailSettingService_DI
  with MqCampaignSendReportDI
  with MqPreEmailValidationDI
  with TeamServiceDI
  with TeamDAOService_DI
  with TeamSRAIFlagsJedisDAO_DI
  with SrUuidServiceDI
  with MqSmsChannelScheduler_DI
  with SmsChannelScheduler_DI
  with SmsSettingDAODI
  with CampaignSchedulingMetadataDAO_DI
  with MqCallChannelScheduler_DI
  with CallChannelScheduler_DI
  with CallDAO_DI
  with EmailThreadDAOService_DI
  with InboxV3DAOService_DI
  with MqInternalAdoptionReportUpdaterDI
  with InternalAdoptionReportUpdateServiceDI
  with OrgMetadataDAO_DI
  with OrgMetadataServiceDI
  with MqEmailAccountDeleterDI
  with MQNewProcessWorkflowAttemptService_DI
  with ProcessAttemptService_DI
  with MqInternalTeamBasedMigration_DI
  with WorkFlowAttemptJedisDAOService_DI
  with CRMSettingsForWorkFlowAttemptJedisDAO_DI
  with WorkFlowAttemptSetJedisDAO_DI
  with WorkflowJedisService_DI
  with WorkflowRateLimitJedisDAOService_DI
  with WorkflowAttemptRateLimitSetJedisDAO_DI
  with CRMSettingsRateLimitJedisDAO_DI
  with MqExecuteDueLinkedinTasksDI
  with PhantomBusterServiceDI
  with PhantomBusterApiDI
  with CaptainDataApiDI
  with LinkedinActionsPublishServiceDI
  with Campaign.ChannelSettingServiceDI
  with MQUnsnoozeSnoozedEmails_DI
  with EmailMessageDataDAO_DI
  with MqUuidMigrationScript_DI
  with MigrationFuncDI
  with CampaignEmailSettingsDAO_DI
  with ApiLayerServiceDI
  with ApiLayerDAO_DI
  with MqStopInactiveCampaign_DI
  with ProspectTagDAOLegacy_DI
  with CallUtilsDI
  with EmailsScheduledDeleteService_DI
  with MqCampaignsProspectsMigrationScript_DI
  with CallHistoryLogDAO_DI
  with MqUpdateSubAccountCallHistory_DI
  with OpportunitiesDefaultSetupService_DI
  with PipelineDAO_DI
  with OpportunityStatusDAO_DI
  with MqOpportunitiesDefaultSetupMigration_DI
  with FirstPromoterDI
  with TeamsPipelineConfigDAO_DI
  with TeamsPipelineConfigService_DI
  with OrganizationService_DI
  with OrganizationJedisCacheDao_DI
  with CalendarAppServiceDI
  with CalendarAppDaoDI
//  with MultichannelTestUtilDI
//
  with CalendarAppApiDI
  with CampaignProspectAssignDI
  with EmailScheduledDAOService_DI

  with Campaign.CampaignStartServiceDI
  with MQEmailSchedulerV2DI
  with ChannelSchedulers.EmailChannelSchedulerDI
  with SrDateTimeUtils_DI
  with TeamsListingPaginationService_DI
  with SrRollingUpdateCoreService_DI
  with EmailSchedulerJedisService_DI
  with SchedulerIntegrityServiceDI
  with SchedulerIntegrityReportDaoDI
  with SelectAndPublishForDeletionServiceDI
  with ProspectEventDAO_DI
  with MqUpdateUuidMigrationScript_DI
  with MqSchedulerIntegrityCheckDI
  with InboxPlacementCheckDAO_DI
  with DomainChecksDAO_DI
  with DomainHealthCheckDAO_DI
  with ProspectAccountServiceDI
  with CampaignStepDaoServiceDI
//  with MqDeletionAndRevertV2DI
  with MqDeletionAndRevertV2Publisher_DI
  with SrAPIKeyTypeDAO_DI
  with CredentialsAuthService_DI
  with AccountOTPService_DI
  with SRGoogleRecaptchaServices_DI
  with AccountOTPDBDAOService_DI
  with GoogleRecaptchaApiService_DI
  with OTPJedisDAOService_DI
  with AuthUtilsDI
  with LoggingActionDI
  with AuthServiceDI
  with CommonAuthApiDI
  with CommonAuthServiceDI
  with SRCommonAuthRedisServiceDI
//  with ActorMaterializerDI
  with CampaignTestStepServiceDI
  with OutlookUtilsService_DI
  with AuthDAODI
  with LeadFinderUploadDaoDI
//  with LeadFinderUploadServiceDI
  with ProductOnboardingDAO_DI
//  with UploadLeadFinderCsvCronServiceDI
  with CustomTrackingDomainServiceDAO_DI
  with PotentialDuplicateProspectService_DI
  with PotentialDuplicateProspectsDAO_DI
//  with UploadLeadFinderCsvLinkedinCronServiceDI
  with MqPublisherHandleEventLogDI
  with NewSignUpReportDAO_DI
  with CampaignApiService_DI
  with ProspectCategoryServiceDI
  with LeadFinderService_DI
  with CampaignTagServiceDI
  with NotesDAO_DI
  with NotesService_DI
  with NotesPaginationService_DI
  with LeadFinderValidationService_DI
  with MqPotentialDuplicateProspects_DI
  with MqZapmailEmailAccountCreationPublisher_DI
  with ProspectEventDAOService_DI
  with AwsBedrockLlama3Api_DI
  with BatchCreateTeamInboxes_DI
  with SrLLMService_DI
  with IndependentStepSchedulerService_DI
  with ProspectColumnService_DI
  with EmailHealthCheckDAO_DI
  with EmailInfraService_DI
  with SocialAuthServiceDI
  with EmailInfraDao_DI
  with MailDosoService_DI
  with MailDosoApi_DI
  with ZapMailAPI_DI
  with EmailHealthCheckService_DI
  with EmailAccountUploadService_DI
  with EmailHealthDnsCheckService_DI
  with ProspectDaddy.PdLeadsService_DI
  with ProspectDaddy.PdLeadsDAO_DI
  with EmailBodyServiceDI
  with ProspectDaddy.AnyMailFinderAPI_DI
  with InternalLeadFinderDataUpdateCronDI
  with MqInternalLeadFinderDataDI
  with PurchasedDomainsAndEmailsDeleterService_DI
  with MqPurchasedEmailsDeleter_DI
  with MqPurchasedDomainsDeleter_DI
  with MqHandleAttemptCreationDI
  with AIHyperPersonalizedGeneratorDI
  with MqHandleAttemptCreationPublisherDI
  with AccountCreateServiceDI 
  with WarmupHeroService_DI
  with WarmupHeroApi_DI
    with CalendlyWebhookServiceDI
    with ProspectBatchActionServiceDI
    with ApiService_DI
    with BlacklistApiService_DI
    with CallApiService_DI
    with TeamsApiService_DI
    with ProspectApiService_DI
    with ProspectEventService_DI
  with DripCampaignHealthCheckDAO_DI
  with MqForSaveReplySentimentPublisher_DI
  with MqPauseCampaignOnReplySentimentSelectDI
  with MqExtractLinkedinConnectionResultsDI
  with MqDeleteLinkedinSettingDI
  with DripCampaignHealthCheckService_DI
  with PhantomBusterTeamServiceDI
  with ReplySentimentClassificationService_DI
  with SrAiLockJedisDAO_DI
  with SrAiApi_DI
  with EmailCommonServiceDI
  with ContentAnalysisService_DI
  with ContentGenerationService_DI
  with MqAutoUpdateProspectCategoryPublisherDI
  with CampaignGenerationService_DI
  {

  given workerActorSystem: ActorSystem = TestAppExecutionContext.actorSystem
  given actorMaterializer: Materializer = TestAppExecutionContext.actorMaterializer

  given wSClient: AhcWSClient = TestAppExecutionContext.wsClient

  given playDefaultExecutionContext: ExecutionContext = TestAppExecutionContext.ec

}

//trait ActorMaterializerDI {
//
//  // actorSystem is initialized in play BuiltinComponents
//  val actorSystem: ActorSystem
//
//  val actorMaterializer = Materializer(actorSystem)
//}
