package utils.testapp

import api.AppConfig
import api.accounts.TeamId
import api.accounts.models.OrgId
import api.prospects.models.ProspectCategory
import api.team_inbox.model.{ReplySentimentChannelType, ReplySentimentSubCategory, ReplySentimentTypeData}
import api.team_inbox.service.TeamInboxService
import org.apache.pekko.actor.ActorSystem
import play.api.Logging
import scalikejdbc.{DB, DBSession}
import scalikejdbc.config.DBs
import scalikejdbc.scalikejdbcSQLInterpolationImplicitDef
import utils.SRLogger
import utils.dbutils.SQLUtils
import utils_deploy.rolling_updates.models.SrRollingUpdateFeature

import scala.concurrent.ExecutionContext
import scala.util.{Failure, Success, Try}


object MigratingToNewReplySentimentForAffectedTeams extends Logging {
  def main(args: Array[String]): Unit = {
    if (args.length > 0) {
      DBs.setupAll()

      val applicationName = args(0)

      applicationName match {
        case "run_migration" =>
          implicit val system: ActorSystem = ActorSystem()
          implicit val executionContext: ExecutionContext = system.dispatcher
          given logger: SRLogger = new SRLogger("MigratingToNewReplySentimentForAffectedTeams")

          object MigrationRunner extends TestAppTrait {


            def runMigration(): Unit = {


              val affectedTeamIds: List[Long] = List(505L, 6412L, 11770L, 17128L)

                  if (affectedTeamIds.isEmpty) {
                    println("No affected team IDs specified. Migration will not run.")
                    return
                  }

                  println(s"Starting migration for ${affectedTeamIds.size} teams")

                  // Process each team using scala.util.Try
                  val results = affectedTeamIds.map { teamId =>
                    println(s"About to process team ID: $teamId")
                    val result = processSingleTeam(TeamId(teamId))
                    println(s"Finished processing team ID: $teamId with result: $result")
                    result
                  }

                  // Check if any migrations failed
                  val failures = results.collect { case Failure(e) => e }
                  if (failures.nonEmpty) {
                    println(s"${failures.size} team migrations failed out of ${affectedTeamIds.size}")
                    failures.foreach(e => println(s"Migration failure: ${e.getMessage}"))
                  } else {
                    println(s"Successfully migrated all ${affectedTeamIds.size} teams")
                  }
                
              }

            


            private def processSingleTeam(teamId: TeamId): Try[Unit] = {
              println(s"Processing team ID: ${teamId.id}")

              // Use scala.util.Try for all error handling
              val dbSessionTry = Try(dbUtils.startLocalTx())

              dbSessionTry.flatMap { dbAndSession =>
                val db = dbAndSession.db
                implicit val session: DBSession = dbAndSession.session
                println(s"Started transaction for team ${teamId.id}")

                val result = for {
                  // Step 1: Delete existing reply sentiments for the team
                  deletedCount <- deleteExistingReplySentiments(teamId)
                  _ = println(s"Deleted $deletedCount existing reply sentiments for team ${teamId.id}")

                  // Step 2: Get prospect categories for the team
                  _ = println(s"Retrieving prospect categories for team ${teamId.id}")
                  prospectCategories <- prospectDAO.getProspectCategories(teamId)
                  _ = println(s"Retrieved ${prospectCategories.size} prospect categories for team ${teamId.id}")

                  // Step 3: Create new reply sentiments
                  newReplySentimentsEnabled = true // Let the DB handle rollbacks automatically
                  _ = println(s"Generating default reply sentiment types for team ${teamId.id}")
                  defaultSentiments = TeamInboxService.defaultReplySentimentTypes(
                    prospect_categories = prospectCategories,
                    newReplySentimentsEnabled = newReplySentimentsEnabled
                  )
                  _ = println(s"Generated ${defaultSentiments.size} default sentiment types for team ${teamId.id}")

                  // Add each sentiment type and collect results
                  addResults <- Try {
                    println(s"Adding ${defaultSentiments.size} reply sentiments for team ${teamId.id}")
                    val results = defaultSentiments.map { sentimentType =>
                      val result = addReplySentiment(teamId, sentimentType)
                      println(s"Added sentiment type ${sentimentType.getReplySentimentType} with result: $result")
                      result
                    }

                    // Check for failures
                    val failures = results.collect { case Failure(e) => e }
                    if (failures.nonEmpty) {
                      println(s"Failed to add some sentiment types: ${failures.head.getMessage}")
                      throw failures.head // This will be caught by the outer Try
                    }

                    results.collect { case Success(id) => id }
                  }
                } yield {
                  // Success path
                  println(s"Committing transaction for team ${teamId.id}")
                  dbUtils.commitAndCloseSession(db)
                  println(s"Successfully migrated team ${teamId.id} with ${defaultSentiments.size} reply sentiments")
                  ()
                }

                // Handle any failures
                result.recoverWith { case e: Exception =>
                  println(s"Failed to migrate team ${teamId.id}")
                  e.printStackTrace()
                  // Just close the session, let DB handle rollback
                  Try(dbUtils.closeSession(db)) match {
                    case Success(_) => println(s"Successfully closed DB session for team ${teamId.id}")
                    case Failure(closeEx) => println(s"Error closing session: ${closeEx.getMessage}")
                  }
                  Failure(e)
                }
              }.recoverWith { case e: Exception =>
                println(s"Failed to start transaction for team ${teamId.id}: ${e.getMessage}")
                e.printStackTrace()
                Failure(e)
              }
            }


            private def findAllTeamIdByOrgId(orgId: OrgId): Try[List[Long]] = Try {
              println(s"Fetching all teams for org  ${orgId.id}")
              val result =DB.readOnly { implicit session =>
                sql"""
                     Select t.id from teams t where t.org_id = ${orgId.id} ;
                  """
                  .map(rs => rs.long("id"))
                  .list
                  .apply()
              }
              result
            }

            private def deleteExistingReplySentiments(teamId: TeamId)(implicit session: DBSession): Try[Int] = Try {
              println(s"Executing SQL to delete existing reply sentiments for team ${teamId.id}")
              val result = sql"""
                DELETE FROM reply_sentiments_for_teams
                WHERE team_id = ${teamId.id}
              """.update.apply()
              result
            }


            private def addReplySentiment(
                                           teamId: TeamId,
                                           sentimentType: ReplySentimentTypeData
                                         )(implicit session: DBSession): Try[Option[Long]] = {
              println(s"Adding reply sentiment '${sentimentType.getReplySentimentType}' for team ${teamId.id}")
              val result = replySentimentDAOService.addReplySentimentsForATeam(
                team_id = teamId.id,
                replySentimentType = sentimentType,
                uuid = srUuidUtils.generateReplySentimentUuid()
              )
              println(s"Result of adding sentiment '${sentimentType.getReplySentimentType}': $result")
              result
            }
          }

          // Run the migration using scala.util.Try
          println("Starting migration runner")
          Try(MigrationRunner.runMigration()) match {
            case Success(_) =>
              println("Migration completed successfully")
            case Failure(e) =>
              println("Migration failed with exception:")
              e.printStackTrace()
          }

          // Always terminate the actor system
          println("Terminating actor system")
          system.terminate()

        case "migrate_not_classified_teams" =>
          implicit val system: ActorSystem = ActorSystem()
          implicit val executionContext: ExecutionContext = system.dispatcher
          given logger: SRLogger = new SRLogger("MigratingToNewReplySentimentForAffectedTeams")

          object MigrationRunner extends TestAppTrait {

            def runMigration(): Unit = {
              val startTime = System.currentTimeMillis()
              println("=== MIGRATION START ===")

              val affectedTeamIds: List[Long] = getTeamIds()


              if (affectedTeamIds.isEmpty) {
                println("No affected team IDs specified. Migration will not run.")
                println("No affected team IDs specified. Migration will not run.")
                return
              }

              println(s"Starting migration for ${affectedTeamIds.size} teams: ${affectedTeamIds.mkString(", ")}")
              println(s"Starting migration for ${affectedTeamIds.size} teams")

              // Process each team with detailed tracking
              val results = affectedTeamIds.zipWithIndex.map { case (teamId, index) =>
                val teamStartTime = System.currentTimeMillis()
                println(s"Processing team ${index + 1}/${affectedTeamIds.size}: ID $teamId")
                println(s"About to process team ID: $teamId (${index + 1}/${affectedTeamIds.size})")

                val result = processSingleTeam(TeamId(teamId))
                val teamEndTime = System.currentTimeMillis()
                val teamDuration = teamEndTime - teamStartTime

                result match {
                  case Success(_) =>
                    println(s"Successfully processed team ID: $teamId in ${teamDuration}ms")
                    println(s"✓ Finished processing team ID: $teamId successfully in ${teamDuration}ms")
                  case Failure(e) =>
                    println(s"Failed to process team ID: $teamId in ${teamDuration}ms  ${e.printStackTrace()}")
                    println(s"✗ Failed processing team ID: $teamId in ${teamDuration}ms - Error: ${e.printStackTrace()}")
                }

                teamId -> result
              }

              // Comprehensive results analysis
              val successes = results.collect { case (teamId, Success(_)) => teamId }
              val failures = results.collect { case (teamId, Failure(e)) => teamId -> e }
              val totalDuration = System.currentTimeMillis() - startTime

              // Log final summary
              println(s"=== MIGRATION SUMMARY ===")
              println(s"Total teams processed: ${affectedTeamIds.size}")
              println(s"Successful migrations: ${successes.size}")
              println(s"Failed migrations: ${failures.size}")
              println(s"Total migration time: ${totalDuration}ms")

              if (successes.nonEmpty) {
                println(s"Successfully migrated teams: ${successes.mkString(", ")}")
              }

              if (failures.nonEmpty) {
                println(s"Failed team migrations: ${failures.map(_._1).mkString(", ")}")
                failures.foreach { case (teamId, exception) =>
                  println(s"Team $teamId failure details: ${exception.printStackTrace()}")
                }

                println(s"${failures.size} team migrations failed out of ${affectedTeamIds.size}")
                failures.foreach { case (teamId, e) =>
                  println(s"Migration failure for team $teamId: ${e.getMessage}")
                }
              } else {
                println(s"✓ Successfully migrated all ${affectedTeamIds.size} teams")
              }

              println("=== MIGRATION END ===")
            }

            private def processSingleTeam(teamId: TeamId): Try[Unit] = Try {
              println(s"Starting processing for team ID: ${teamId.id}")
              println(s"Processing team ID: ${teamId.id}")

              val listOfReplySentimentsToAdd = List(
                ReplySentimentTypeData.PositiveData(
                  replySentimentSubCategory = ReplySentimentSubCategory.EmailPositiveSpeakToSales,
                ),

                ReplySentimentTypeData.ObjectionData(
                  replySentimentSubCategory = ReplySentimentSubCategory.EmailNegativeOtherReason,
                ),
//               
              )

              println(s"Adding ${listOfReplySentimentsToAdd.size} reply sentiments for team ${teamId.id}")

              // Use a single transaction for all sentiments for better performance and atomicity
              val sentimentResults = DB localTx { implicit session =>
                listOfReplySentimentsToAdd.zipWithIndex.map { case (sentimentType, index) =>
                  logger.debug(s"Adding sentiment ${index + 1}/${listOfReplySentimentsToAdd.size}: '${sentimentType.getReplySentimentType}' for team ${teamId.id}")

                  val result = addReplySentiment(teamId = teamId, sentimentType = sentimentType)

                  result match {
                    case Success(Some(id)) =>
                      logger.debug(s"Successfully added sentiment '${sentimentType.getReplySentimentType}' with ID: $id")
                    case Success(None) =>
                      println(s"Sentiment '${sentimentType.getReplySentimentType}' was added but no ID returned")
                    case Failure(e) =>
                      println(s"Failed to add sentiment '${sentimentType.getReplySentimentType}': ${e.printStackTrace()}")
                      throw e // Re-throw to fail the entire transaction
                  }

                  result
                }
              }

              // Check if all sentiments were added successfully
              val failedSentiments = sentimentResults.collect { case Failure(e) => e }
              val successfulSentiments = sentimentResults.collect { case Success(_) => () }

              if (failedSentiments.nonEmpty) {
                val errorMsg = s"Failed to add ${failedSentiments.size} out of ${listOfReplySentimentsToAdd.size} sentiments for team ${teamId.id}"
                println(errorMsg)
                throw new RuntimeException(errorMsg)
              }

              println(s"Successfully added all ${successfulSentiments.size} reply sentiments for team ${teamId.id}")
            }.recover { case e =>
              println(s"Error processing team ${teamId.id}: ${e.printStackTrace()}")
              throw e
            }

            private def  getTeamIds() : List[Long] = Try {
              DB readOnly{ implicit session => {
                sql"""
                      SELECT DISTINCT(rsft.team_id)
                      FROM reply_sentiments_for_teams rsft
                      INNER JOIN teams t ON t.id = rsft.team_id
                      WHERE t.org_id <= ${AppConfig.latestOrgIdForReplySentiments}
                      AND rsft.team_id NOT IN ${SQLUtils.generateSQLValuesClause(AppConfig.listOfTeamIdsWhichHadNotClassified)}
                        ;

                   """
                  .map(rs =>{
                    rs.long("team_id")
                  })
                  .list
                  .apply()

              }}} match {
              case Failure(e) => {
                println(s"An Error occurred while fetching  teamids ${e.printStackTrace()}")
                List()
              }

              case Success(value) => {
                value
              }
            }

            private def addReplySentiment(
                                           teamId: TeamId,
                                           sentimentType: ReplySentimentTypeData
                                         )(implicit session: DBSession): Try[Option[Long]] = Try {

              val sentimentTypeName = sentimentType.getReplySentimentType
              logger.debug(s"Attempting to add reply sentiment '$sentimentTypeName' for team ${teamId.id}")

              val uuid = srUuidUtils.generateReplySentimentUuid()
              logger.debug(s"Generated UUID: $uuid for sentiment '$sentimentTypeName'")

              val result = replySentimentDAOService.addReplySentimentsForATeam(
                team_id = teamId.id,
                replySentimentType = sentimentType,
                uuid = uuid
              )

              result match {
                case Success(Some(id)) =>
                  logger.debug(s"Successfully added sentiment '$sentimentTypeName' with ID: $id for team ${teamId.id}")
                case Success(None) =>
                  println(s"Sentiment '$sentimentTypeName' added but no ID returned for team ${teamId.id}")
                case Failure(e) =>
                  println(s"Database error adding sentiment '$sentimentTypeName' for team ${teamId.id}: ${e.printStackTrace()}")
                  throw e
              }

              result.get // Extract the Option[Long] from Success
            }.recover { case e =>
              println(s"Failed to add reply sentiment '$sentimentType.getReplySentimentType' for team ${teamId.id} exception:: ${e.printStackTrace()}")
              throw e
            }
          }

          // Run the migration with comprehensive error handling
          println("Initializing migration runner")
          println("Starting migration runner")

          val migrationResult = Try(MigrationRunner.runMigration())

          migrationResult match {
            case Success(_) =>
              println("Migration completed successfully")
              println("✓ Migration completed successfully")
            case Failure(e) =>
              println(s"Migration failed with exception:: ${e.printStackTrace()}")
              println("✗ Migration failed with exception:")
              e.printStackTrace()
          }

          // Always terminate the actor system
          println("Terminating actor system")
          system.terminate()

        case other =>
          println(s"Unknown application name: $other")
      }
    } else {
      println("No arguments provided. Please specify 'run_migration' as the first argument.")
    }
  }
}