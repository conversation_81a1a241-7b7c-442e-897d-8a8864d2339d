package utils.testapp

import api.integrations.IntegrationTPAccessTokenResponse
import api.triggers.{IntegrationModuleType, IntegrationType, Trigger}
import org.apache.pekko.actor.ActorSystem
import org.apache.pekko.stream.Materializer
import play.api.Logging
import scalikejdbc.config.DBs
import utils.SRLogger
import utils.security.EncryptionHelpers
import utils.testapp.TestAppTrait

import scala.concurrent.ExecutionContext
import scala.util.{Failure, Success}

/*
This object holds any sort internal test function used to test features/changes manually during development.
 */
object TestAppCrmIntegrations extends Logging {

  def main(args: Array[String]): Unit = {

    if (args.length > 0) {
      DBs.setupAll()


      val applicationName = args(0)

      applicationName match {

        case "test_hubspot" =>
          implicit val system = ActorSystem()
          implicit val materializer = Materializer(system)
          implicit val actorContext: ExecutionContext = system.dispatcher
          given logger: SRLogger = new SRLogger("TestApp : test_hubspot ")
          object Test extends TestAppTrait {
            def init() = {

              hubspotOAuth.findOneByEmailAndModule(
                accessTokenData = IntegrationTPAccessTokenResponse.FullTokenData(
                  access_token =
                    EncryptionHelpers.decryptWorkflowCRMCredential(
                      "mvs5TiRZ4PSQceeYrNw04LFCQ_g2MnCQL-Vk0mwBQ12eozK7_bu2Rf06j47kWTfQYjEf5SJEwBXkKHFIak8JCfHZKn1EI-kmnXgOzCuCXgDrt83oN_WhPazmv1mHvGixtRAztbufFR-ZcY925Rayax8wIXcsDZHwolmNTmOtuZ504XUhDEaR-JNVBKvxBbS6afYutLS7xE6bKVIqq1_BncqMPLvOtL9CU1bsZBwQqTune37Ie0yYVGEwKv1xgCnpowT8Z67wACJncx8FrjpwTHwFEB9tJuchVTfCYh6uSqU"
                    ),
                  refresh_token = None,
                  expires_in = None,
                  expires_at = None,
                  token_type = None,
                  api_domain = None, //this is only for zoho case bcz zoho has multi DC API's URLS with  (.eu, .au., .in, .com)
                  is_sandbox = None
                ),
                email = "<EMAIL>",
                module = IntegrationModuleType.CONTACTS,
                statusColumn = None
              )(ws = wSClient, ec = actorContext, system = system,
                Logger = logger
              )
            }
          }

          Test.init()
          Thread.sleep(10000)



        case "print_rs" =>
          val trigger = new Trigger
          val Logger = new SRLogger("print_rs")

          trigger.findIntegrationByTeamIdAndCRMTypeAndModuleType(
            team_id = 53,
            crm_type = IntegrationType.SALESFORCE,
            module_type = IntegrationModuleType.CONTACTS,
            logger = Logger) match {
            case Success(result) => println(s"result ====== $result")
            case Failure(err) => println(s"err ====== $err")
          }

          println(s"done running")
          Thread.sleep(5000)

        /*
      case "trigger_demo" =>

        val triggerId = 948
        val accountIds: Seq[Long] = Seq(4003)
        val teamId = 3741
        val crmIntegrationService = SalesforceOAuth
        val module = IntegrationModuleType.LEADS
        val logRequestId = "trigger_demo"

        val t = Trigger.find(id = triggerId, accountIds = accountIds, teamId = teamId).get.get
//
//          val msg = MQTriggerMsg(
//            accountId = t.owner_id,
//            teamId = t.team_id,
//            campaignId = t.campaign_id,
//            prospectIds = Seq(),
//            clickedUrl = None,
//            campaignName = None,
//            emailScheduledIds = None,
//            event = t.event.get.toString,
//            prospectCategoryIdCustom = None
//          )

        given Logger = new SRLogger(logRequestId = logRequestId)

        MQTrigger._syncContactDataFromCRM(
          crmIntegrationService = crmIntegrationService,
          syncModule = module,
          teamId = teamId,
          accountId = accountIds.head,
          trigger = t,
          action = t.actions.get.head
        )
          .map(res => {
            Logger.info(s"INSIDE map $res")
          })
          .recover {
            case e => {

              Logger.info(s"${Helpers.getStackTraceAsString(e)}")

            }

          }
        */


        case "trigger_demo1" =>

          //          val triggerId = 556
          //          val accountIds: Seq[Long] = Seq(42)
          //          val teamId = 24
          //          val crmIntegrationService = SalesforceOAuth
          //          val module = IntegrationModuleType.CONTACTS
          val logRequestId = "trigger_demo"

        //          val t = Trigger.find(id = triggerId, accountIds = accountIds, teamId = teamId).get.get
        //
        //          val msg = MQTriggerMsg(
        //            accountId = t.owner_id,
        //            teamId = t.team_id,
        //            campaignId = t.campaign_id,
        //            prospectIds = Seq(),
        //            clickedUrl = None,
        //            campaignName = None,
        //            emailScheduledIds = None,
        //            event = t.event.get.toString,
        //            prospectCategoryIdCustom = None
        //          )

        //          given Logger = new SRLogger(logRequestId = logRequestId)


        //          val d = MQTriggerMsg(
        //            accountId = 2,
        //            teamId = 2,
        //            campaignId = None,
        //            prospectIds = Seq(2832606),
        //            clickedUrl = None,
        //            campaignName = None,
        //            emailScheduledIds = None,
        //            prospectCategoryIdCustom = Some(21),
        //            event = EventType.PROSPECT_CATEGORY_UPDATED.toString
        //          )

        //          MQTrigger.processMessage(message = d)
        //          MQTrigger._syncContactDataFromCRM(
        //            crmIntegrationService = crmIntegrationService,
        //            syncModule = module,
        //            teamId = teamId,
        //            accountId = accountIds.head,
        //            trigger = t,
        //            action = t.actions.get.head
        //          )
        //            .map(res => {
        //              Logger.info(s"INSIDE map $res")
        //            })
        //            .recover {
        //              case e => {
        //
        //                Logger.info(s"${Helpers.getStackTraceAsString(e)}")
        //
        //              }
        //
        //            }



        //        case "event_test" =>
        //
        //          val a = Account.find(id = 2).get
        ////          MQRepliedEvent.publishRepliedProspects(
        ////            accountId = a.id,
        ////            teamId = 2,
        ////            cpReplieds = Seq(
        ////              CPReplied(campaignId = 74, prospectId = 474131, replied = true, replied_at = DateTime.now())
        ////            ),
        ////            reply_handling = ReplyHandling.PAUSE_SPECIFIC_CAMPAIGN_ON_REPLY,
        ////            doerAccountName = s"${a.first_name + " " + a.last_name}"
        ////          )
        //
        //          MQRepliedEvent.processMessage(msg = MQWebhookRepliedMsg(
        //            accountId = a.id,
        //            teamId = 2,
        //            cpReplieds = Seq(
        //              CPReplied(campaignId = 74, prospectId = 474131, replied = true, replied_at = DateTime.now(), campaignName = None)
        //            ),
        //            reply_handling = ReplyHandling.PAUSE_SPECIFIC_CAMPAIGN_ON_REPLY,
        //            doerAccountName = s"${a.first_name + " " + a.last_name}"
        //          ))
        /*
      case "zr" =>

        val logRequestId = "********"

        given Logger = new SRLogger(logRequestId = logRequestId)

        val accountId = 2
        val teamId = 2
        val crmIntegrationService = ZohoRecruitOAuth
        val integrationModuleType: IntegrationModuleType = IntegrationModuleType.CONTACTS
        crmIntegrationService.fetchTokensFromDB(accountId = accountId, teamId = teamId, integration_type = IntegrationType.ZOHO_RECRUIT) match {

          case Failure(e) =>

            Logger.fatal("crmIntegrationService.fetchTokensFromDB", err = e)

            Future.successful(0)

          case Success(accessTokenData) =>

            if (accessTokenData.nonEmpty && accessTokenData.get.refresh_token.nonEmpty) {


              crmIntegrationService.refreshAccessToken(email = "", accessTokenData = accessTokenData.get, teamId = teamId, accountId = accountId)
                .flatMap(accessTokenResponse => {


                  Trigger.findFieldMapping(teamId = teamId, integration_type = IntegrationType.ZOHO_RECRUIT, module_type = integrationModuleType) match {

                    case Failure(e) =>

                      Logger.fatal("Trigger.findFieldMapping", err = e)

                      Future.successful(0)

                    case Success(None) =>

                      Logger.fatal("Trigger.findFieldMapping None")

                      Future.successful(0)

                    case Success(Some(fieldMapping)) =>

                      val prospects = Prospect.findByIds(ids = Seq(410335), teamId = teamId)

                      val crmCreateFuture = integrationModuleType match {

                        case IntegrationModuleType.CONTACTS =>

                          ZohoRecruitOAuth.createOrUpdateBatchContacts(accessTokenData = accessTokenResponse, contacts = prospects, fieldMapping = fieldMapping, accountId = accountId, teamId = teamId)

                        case IntegrationModuleType.CANDIDATES |
                             IntegrationModuleType.LEADS =>

                          Future.failed(new Exception(s"Invalid integrationModuleType: $integrationModuleType"))

                      }


                      crmCreateFuture.flatMap { res =>

                        if (res.isDefined) {

                          Future.successful(res.get)

                        } else {

                          Future.successful(0)

                      }
                    }

                  }


                })

            } else {

              Logger.fatal("FATAL Tokens not found")

              Future.successful(0)

            }

        }

      case "zrf" =>

        val logRequestId = "********"

        given Logger = new SRLogger(logRequestId = logRequestId)

        val accountId = 2
        val teamId = 2
        val crmIntegrationService = ZohoRecruitOAuth
        val integrationModuleType: IntegrationModuleType = IntegrationModuleType.CONTACTS
        crmIntegrationService.fetchTokensFromDB(accountId = accountId, teamId = teamId, integration_type = IntegrationType.ZOHO_RECRUIT) match {

          case Failure(e) =>

            Logger.fatal("crmIntegrationService.fetchTokensFromDB", err = e)

            Future.successful(0)

          case Success(accessTokenData) =>

            if (accessTokenData.nonEmpty && accessTokenData.get.refresh_token.nonEmpty) {


              crmIntegrationService.refreshAccessToken(email = "", accessTokenData = accessTokenData.get, teamId = teamId, accountId = accountId)
                .flatMap(accessTokenResponse => {


                  Trigger.findFieldMapping(teamId = teamId, integration_type = IntegrationType.ZOHO_RECRUIT, module_type = integrationModuleType) match {

                    case Failure(e) =>

                      Logger.fatal("Trigger.findFieldMapping", err = e)

                      Future.successful(0)

                    case Success(None) =>

                      Logger.fatal("Trigger.findFieldMapping None")

                      Future.successful(0)

                    case Success(Some(fieldMapping)) =>

                      val prospects = Prospect.findByIds(ids = Seq(410335), teamId = teamId)

                      val crmCreateFuture = integrationModuleType match {

                        case IntegrationModuleType.CONTACTS =>

                          ZohoRecruitOAuth.createOrUpdateBatchContacts(accessTokenData = accessTokenResponse, contacts = prospects, fieldMapping = fieldMapping, accountId = accountId, teamId = teamId)

                        case IntegrationModuleType.CANDIDATES =>

                          crmIntegrationService.createOrUpdateBatchLeads(accessTokenData = accessTokenResponse, leads = prospects,  fieldMapping = fieldMapping, accountId = accountId, teamId = teamId)

                        case IntegrationModuleType.LEADS =>
                          Future.failed(new Exception(s"Invalid integrationModuleType: $integrationModuleType"))

                      }


                      crmCreateFuture.flatMap { res =>

                        if (res.isDefined) {

                          Future.successful(res.get)

                        } else {

                          Future.successful(0)

                        }
                      }

                  }


                })

            } else {

              Logger.fatal("FATAL Tokens not found")

              Future.successful(0)

            }

        }
        */

        /*
  case "sf_query" =>

    given Logger = new SRLogger(logRequestId = "sf_query")

    SalesforceOAuth.fetchTokensFromDB(teamId = 3741, integration_type = SalesforceOAuth.name) match {

      case Failure(e) =>

        Logger.fatal(msg = "crmIntegrationService.fetchTokensFromDB", err = e)

        Future.successful(0)

      case Success(accessTokenData) =>

        if (accessTokenData.nonEmpty && accessTokenData.get.refresh_token.nonEmpty) {


          SalesforceOAuth.refreshAccessToken(email = "", accessTokenData = accessTokenData.get, teamId = 3741)
            .flatMap(accessTokenResponse => {


//                    val contactUrl = s"https://ap16.salesforce.com/services/data/v47.0/sobjects/Contact/listviews/00B2w00000FxHW8EAN/describe" //headlapbs
//                    val contactUrl = s"https://ap16.salesforce.com/services/data/v47.0/query/?q=SELECT+Name,+Account.Name,+Phone,+Email,+Title,+Owner.Alias,+Id,+CreatedDate,+LastModifiedDate,+SystemModstamp,+Account.Id,+AccountId,+Owner.Id,+OwnerId+FROM+Contact+WHERE+LastModifiedDate+>+2014-07-20T01:02:03Z+AND+((NOT+FirstName+like+'%25Sathish%25')+OR+FirstName+like+'%25Jeswanth%25')+ORDER+BY+LastModifiedDate+DESC+LIMIT+1" //headlapbs
              val contactUrl = s"https://ap16.salesforce.com/services/data/v47.0/query/?q=SELECT+COUNT()+FROM+Lead+WHERE+(+IsConverted+=+false+AND+RecordTypeId+=+'0123t000000JF06'+AND+HasOptedOutOfEmail+=+false+AND+Seamless_List_Name__c+=+'Local_Realtor_Region9')" //headlapbs
//                    val contactUrl = s"https://ap16.salesforce.com/services/data/v47.0/query/?q=SELECT+Id+FROM+Contact+USING+SCOPE+mine+WHERE+Name+LIKE+'Sathish%'" //headlapbs
//                    val contactUrl = s"https://ap16.salesforce.com/services/data/v47.0/query/?q=SELECT+Id+FROM+Contact+WHERE+USING+SCOPE+mine+WHERE+LastModifiedDate+>+'2021-05-03T12:08:42.000+0000'+AND+(FirstName+like+'%25Sathish%25'+OR+FirstName+like+'%25Jeswanth%25')+AND+IsEmailBounced+=+false+ORDER+BY+LastModifiedDate+DESC+LIMIT+1" //headlapbs
//                    val contactUrl = s"https://ap16.salesforce.com/services/data/v47.0/query/?q=SELECT+Id+FROM+Contact+USING+SCOPE+mine" //headlapbs


//                    val contactUrl = s"https://ap16.salesforce.com/services/data/v47.0/sobjects/Lead/listviews/00B2w000002gLTFEA2/results"
//                    val contactUrl = s"https://ap16.salesforce.com/services/data/v47.0/sobjects/Lead/listviews/00B3t00000CKFCWEA5/results"
//                    val contactUrl = s"https://ap16.salesforce.com/services/data/v47.0/query/?q=SELECT+Id+From+Lead+WHERE+ListViewId+=+'00B3t00000CKFCWEA5'+ORDER+BY+CreatedDate+LIMIT+1"
//                    val contactUrl = s"https://ap16.salesforce.com/services/data/v47.0/query/?q=SELECT+Id+From+Lead+USING+ListView+=+'Seamless_List_Name__c'+LIMIT+1"
//                      val contactUrl = s"https://ap16.salesforce.com/services/data/v47.0/query/?q=SELECT+Id+FROM+Lead+WHERE+Seamless_List_Name__c+=+'Local_Insurance_Region10'+LIMIT+1"
//                    val contactUrl = s"https://ap16.salesforce.com/services/data/v47.0/query/?q=SELECT+Id+FROM+Lead+WHERE+filterName+=+'00B3t00000CKFCWEA5'+ORDER+BY+LastModifiedDate+LIMIT+1"
//                    val contactUrl = s"https://ap16.salesforce.com/services/data/v47.0/query/?q=SELECT+Id+FROM+Lead+WHERE+listViewIdOrName+=+'00B3t00000CKFCWEA5'+ORDER+BY+LastModifiedDate+LIMIT+1"
//                    val contactUrl = s"https://ap16.salesforce.com/services/data/v47.0/query/?q=SELECT+Id,Seamless_List_Name__c+FROM+Lead+WHERE+Seamless_List_Name__c+=+'Local_Insurance_Region10'+ORDER+BY+LastModifiedDate+LIMIT+1"
//                    val contactUrl = s"https://ap16.salesforce.com/services/data/v47.0/query/?q=SELECT+Id,Seamless_List_Name__c+FROM+Lead+WHERE+Seamless_List_Name__c+=+'AllOpenLeads'+ORDER+BY+LastModifiedDate+LIMIT+1"
//                    val contactUrl = s"https://ap16.salesforce.com/services/data/v47.0/query/?q=SELECT+*+FROM+Lead+WHERE+Seamless_List_Name__c+=+'AllLeads'+ORDER+BY+LastModifiedDate+LIMIT+1"
//                    val contactUrl = s"https://ap16.salesforce.com/services/data/v47.0/query/?q=SELECT+Name+FROM+ListView+WHERE+Id+=+'00B3t00000CKFCWEA5'"
//                    val contactUrl = s"https://ap16.salesforce.com/services/data/v47.0/query/?q=SELECT+Name+FROM+ListView+WHERE+Id+=+'00B3t00000CKFCWEA5'"
//                      val contactUrl = s"https://ap16.salesforce.com/services/data/v47.0/ui-api/list-ui/00B3t00000CKFCWEA5"
//                      val contactUrl = s"https://ap16.salesforce.com/services/data/v47.0/query/?q=SELECT+Id+From+Lead+ORDER+BY+CreatedDate+LIMIT+1"
//                      val contactUrl = s"https://ap16.salesforce.com/services/data/v47.0/query/?q=SELECT+ListView+From+Contact"
//                      val contactUrl = s"https://ap16.salesforce.com/services/data/v47.0/query/?q=SELECT+Id,Name,DeveloperName,NamespacePrefix,SobjectType,IsSoqlCompatible,CreatedDate,CreatedBy.Name,LastModifiedDate,LastModifiedBy.Name,LastViewedDate,LastReferencedDate+FROM+ListView"
//                      val contactUrl = s"https://ap16.salesforce.com/services/data/v52.0/query/?q=FIND+Acme}+IN+ALL+FIELDS+RETURNING+Account(Id,Name+USING+ListView=MVPCustomers)"
//                      val contactUrl = s"https://ap16.salesforce.com/services/data/v47.0/jobs/query"
//                      val contactUrl = s"https://ap16.salesforce.com//services/data/vXX.X/query/?explain=list view"
//                                        val contactUrl =s"https://ap16.salesforce.com/services/data/v47.0/sobjects/Lead/listviews/describe"
//                                        val contactUrl =s"https://ap16.salesforce.com/services/data/v47.0/sobjects/ListView/defaultValues?recordTypeId&fields"


//

              Logger.info(s"\n\n\n\n\n start $contactUrl \n\n\n\n")
              wSClient.url(contactUrl)
//                      .withQueryString(
//                        "limit" -> s"1",
//                        "offset" -> s"1"
//                      )
                .withHeaders(
                  "Authorization" -> s"Bearer ${accessTokenResponse.access_token}",
                  "Content-Type" -> "application/json"
                )
                .get
                .map(response => {

                  Logger.info(s"\n\n\n\n\n response: ${response.body} \n\n\n\n")

                })

//                    Logger.info(s"\n\n\n\n\n end \n\n\n\n")

            })
        }
    }
    */

        
        
      }
    }

  }
}
