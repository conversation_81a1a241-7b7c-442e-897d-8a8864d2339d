package utils.testapp

import api.accounts.TeamId
import api.accounts.models.OrgId
import api.tasks.models.{ChangeStatusPermissionCheck, NewTask, TaskCreatedVia, TaskData, TaskPriority, TaskStatus, TaskType, UpdateTaskStatus}
import org.joda.time.DateTime
import scalikejdbc.config.DBs
import utils.SRLogger

import scala.concurrent.Await
import scala.concurrent.duration.Duration
import org.joda.time.DateTime
import org.joda.time.format.DateTimeFormat
import play.api.Logger
import utils.helpers.LogHelpers

object Test_TaskService extends TestAppTrait {

  def main(args: Array[String]): Unit = {

    given logger: SRLogger = new SRLogger("Test_TaskPgDAO")

    if (args.length > 0) {
      DBs.setupAll()

      val applicationName = args(0)

      applicationName match {

        case "create_500_tasks" =>

          val assignee_id = Some(69L)
          val prospect_id = Some(8704L)
          val team_id = TeamId(61L)

          val newTask: NewTask = NewTask(
            campaign_id = None,
            campaign_name = None,
            step_id = None,
            step_label = None,
            created_via =  TaskCreatedVia.Manual,
            is_opening_step = None,
            task_type =  TaskType.CallTask,
            is_auto_task = false,
            task_data = TaskData.CallTaskData(
              body = "testapp se banaya hun",
              recording_link = None,
            ),
            status =  TaskStatus.Due(
              due_at = DateTime.now()
            ),
            assignee_id = assignee_id,
            prospect_id = prospect_id,
            priority =  TaskPriority.Normal,
            emailsScheduledUuid = None,
            note = Some("note hun main"),
          )

          for (i <- 1 to 500){

            val task = taskService.createTask(
              task_data = newTask.copy(status = TaskStatus.Due(
                due_at = DateTime.now()
              )),
              accountId = assignee_id.get,
              teamId = team_id.id,
            )

            Await.result(task, Duration.Inf)

            task.map(rs => rs match {
              case Left(value) => println(s"error while creating task :: ${value}")
              case Right(value) => println(s" created task successfully :: ${value}")
            })
              .recover(err => {
                println(s"err :: ${err}")
              })

          }

          println("created all tasks ")


        case "change_status_of_task" =>

          logger.info("BEFORE test")

              println("Enter doer (account_id who is changing the status):")
              val doer_input: String = scala.io.StdIn.readLine()
              val doer = doer_input.toLong
              println(s" Your input for doer --> ${doer}")

              println("Enter task_id:")
              val task_id: String = scala.io.StdIn.readLine()
              println(s" Your input for task_id --> ${task_id}")

              println("Enter status type (due/snoozed/done/skipped/archive):")
              val status_type_input: String = scala.io.StdIn.readLine().toLowerCase

              val task_status: UpdateTaskStatus = status_type_input match {
                case "due" =>
                  println("Enter due_at (yyyy-MM-dd HH:mm:ss):")
                  val due_at_input: String = scala.io.StdIn.readLine()
                  val formatter = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss")
                  val due_at = formatter.parseDateTime(due_at_input)
                  UpdateTaskStatus.Due(due_at = due_at)

                case "snoozed" =>
                  println("Enter snoozed_till (yyyy-MM-dd HH:mm:ss):")
                  val snoozed_till_input: String = scala.io.StdIn.readLine()
                  val formatter = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss")
                  val snoozed_till = formatter.parseDateTime(snoozed_till_input)
                  UpdateTaskStatus.Snoozed(snoozed_till = snoozed_till)

                case "done" =>
                  UpdateTaskStatus.Done()

                case "skipped" =>
                  UpdateTaskStatus.Skipped()

                case "archive" =>
                  UpdateTaskStatus.Archive()

                case _ =>
                  throw new IllegalArgumentException(s"Invalid status type: $status_type_input")
              }
              println(s" Your input for task_status --> ${task_status}")


              println("Enter org_id:")
              val org_id_input: String = scala.io.StdIn.readLine()
              val orgId = OrgId(id = org_id_input.toLong)
              println(s" Your input for org_id --> ${org_id_input}")

              println("Enter team_id:")
              val team_id_input: String = scala.io.StdIn.readLine()
              val teamId = team_id_input.toLong
              println(s" Your input for team_id --> ${team_id_input}")

              println("Enter permitted account IDs (comma-separated):")
              val permitted_accounts_input: String = scala.io.StdIn.readLine()
              val permittedAccountIds = permitted_accounts_input.split(",").map(_.trim.toLong).toSeq
              println(s" Your input for permitted account IDs --> ${permittedAccountIds.mkString(", ")}")


              val result = taskService.changeStatus(
                  task_id = task_id,
                  task_status = task_status,
                  orgId = orgId,
                  team_id = teamId,
                  changeStatusPermissionCheck = ChangeStatusPermissionCheck.ManualTaskCheck(
                    doer = Some(doer),
                    permittedAccountIds = permittedAccountIds,
                  )
                ).map {
                case Left(error) =>
                  println(s"changeStatusManualProcess :: error : ${error}")
                  logger.error(s"changeStatusManualProcess :: error : ${error}")
                  error

                case Right(success) =>
                  println(s"changeStatusManualProcess :: success : ${success}")
                  logger.info(s"changeStatusManualProcess :: success : ${success}")
                  success
              }
                .recover{e =>
                  logger.error("changeStatusManualProcess :: error : " + e)
                  e
                 }


              // Wait for the Future to complete
              Await.result(result, Duration.Inf)
              logger.info(s"AFTER test : ${result}")


      }

    }

  }





}
