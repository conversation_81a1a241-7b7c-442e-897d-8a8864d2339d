package utils.testapp

import api.accounts.TeamId
import api.campaigns.CampaignFollowUpSetting
import play.api.Logging
import scalikejdbc.config.DBs
import sr_scheduler.CampaignStatus
import utils.dependencyinjectionutils.Tasks.TaskDAO_DI
import utils.dependencyinjectionutils.{BlacklistProspectCheckDAO_DI, CacheServiceJedisDI, CampaignDAO_DI, CampaignProspectDAO_2DI, CampaignSchedulingMetadataDAO_DI, DBCounterDAO_DI, DBUtils_DI, EmailThreadDAO_DI, FreeEmailDomainListDAO_DI, FreeEmailDomainListService_DI, LinkedinTaskServiceDI, MergeTagService_DI, OrganizationDAO_DI, ProspectAccountDAO1DI, ProspectAddEventDAO_DI, ProspectColumnDefDAO_DI, ProspectColumnDef_DI, ProspectDAOService_DI, ProspectDAO_DI, ProspectQueryDI, ProspectsEmailsDAO_DI, ReplySentimentDAOService_DI, ReplySentimentDAO_DI, ReplySentimentJedisDAO_DI, SrDBQueryCounterService_DI, SrUuidUtilsDI}
import utils.testapp.Test_updateAppendFollowUps.logger

import scala.util.{Failure, Success}

object Test_updateStatus extends Logging{
  def main(args: Array[String]): Unit = {

    if (args.length > 0) {
      DBs.setupAll()

      val applicationName = args(0)

      applicationName match {

        case "test_updateStatus" =>

          logger.info("BEFORE test")

          object Test extends TestAppTrait {

            def init() = {

              val id: Long = 234L
              val team_id: TeamId = TeamId(id = 132)
              val status = CampaignStatus.ARCHIVED
              campaignDAO.updateStatus(
                id = id,
                newStatus = status,
                team_id = team_id
              ) match {
                case Failure(exception) =>
                  logger.info(s"Failure  $exception")
                case Success(value) =>
                  logger.info(s"Success $value :: TeamId:${value.get.team_id}" +
                    s":: cid: ${value.get.campaign_id}")
              }
            }
          }
          Test.init()
          logger.info("AFTER test")
      }
    }

  }

}
