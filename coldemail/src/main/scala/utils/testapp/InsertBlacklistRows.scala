package utils.testapp

import api.accounts.models.{AccountId, OrgId}
import api.accounts.TeamId
import api.blacklist.models.BlacklistTypeData.TeamLevelBlacklistData
import api.blacklist.models.{BlacklistEmailAndUuid, BlacklistUuid}
import api.blacklist.{BlacklistCreateUpdateFormWithUuid, DomainsWithExcludedEmailsWithUuid}
import play.api.Logging
import scalikejdbc.config.DBs
import utils.SRLogger


object InsertBlacklistRows extends TestAppTrait with Logging {

  def main(args: Array[String]): Unit = {

    if (args.length > 0) {
      DBs.setupAll()

      val applicationName = args(0)

      applicationName match {

        case "test_insertBlacklistRows" =>


          val Logger = new SRLogger("test_hasOptedOutV2")

          val accountId = 140L
          val teamId = 132L
          val addedByName = "Parth"
          val taId = 249L

          val emails: Seq[BlacklistEmailAndUuid] = Seq(BlacklistEmailAndUuid(email = "<EMAIL>", blacklistUuid = BlacklistUuid(srUuidUtils.generateBlacklistUuid())))
          val domains_with_excluded_emails:  Seq[DomainsWithExcludedEmailsWithUuid] = Seq(DomainsWithExcludedEmailsWithUuid(
            domain = "test.com",
            blacklistUuid = BlacklistUuid(srUuidUtils.generateBlacklistUuid()),
            excluded_emails = Seq("<EMAIL>", "<EMAIL>"))
          )

          val data: BlacklistCreateUpdateFormWithUuid = new BlacklistCreateUpdateFormWithUuid(emails = emails, domains_with_excluded_emails = domains_with_excluded_emails, phones = Seq())

          Logger.info("start test")

          val result = blacklistDAO.insertBlacklistRowsForTeam(
           teamLevelBlacklistData = TeamLevelBlacklistData(
             accountId = AccountId(accountId),
            teamId = TeamId(teamId),
            taId = taId,
            addedByName = addedByName,
            opted_out_from_campaign_id = None,
            opted_out_from_campaign_name = None,
            data = data,
           org_id = OrgId(123L))
          )

          println(result)

          Logger.info("end test")
      }
    }

  }

}