package utils.testapp
import api.accounts.TeamId
import api.campaigns.services.CampaignId
import api.scheduler_report.{ReportData, ReportType}
import org.joda.time.DateTime
import play.api.Logging
import scalikejdbc.config.DBs
import utils.SRLogger

import scala.util.{Failure, Success, Try}

object SchedulerIntegrityCheckTestApp extends Logging {

  def main(args: Array[String]): Unit = {

    if (args.length > 0) {
      DBs.setupAll()


      //      implicit lazy val system = ActorSystem()
      //      implicit lazy val materializer = ActorMaterializer()
      //      implicit lazy val wSClient: AhcWSClient = AhcWSClient()
      //      implicit lazy val actorContext: ExecutionContext = system.dispatcher


      val applicationName = args(0)

      applicationName match {

        case "checkIfReportTypeHasRunInLastOneDay" =>

          object main extends TestAppTrait {
            def init(): Try[Unit] = Try {

              implicit val sr_logger: SRLogger = new SRLogger("checkIfReportTypeHasRunInLastOneDay")

              val res = schedulerIntegrityReportDao.checkIfReportTypeHasRunInLastOneDay(reportType = ReportType.WRONG_STEP_ID)

              res match {

                case Failure(e) =>

                  println(s" error while getting inconsistent campaign ${e.printStackTrace()}")

                case Success(value) =>

                  println(s"Succsss :: ${value.toString}")

              }

            }

          }
          main.init()


        case "createEntryInReports" =>

          object main extends TestAppTrait {
            def init(): Try[Unit] = Try {

              implicit val sr_logger: SRLogger = new SRLogger("checkIfReportTypeHasRunInLastOneDay")

              val res = schedulerIntegrityReportDao.createEntryInReports(reportType = ReportType.WRONG_STEP_ID,entryCreatedAt = DateTime.now())

              res match {

                case Failure(e) =>

                  println(s" error while getting inconsistent campaign ${e.printStackTrace()}")

                case Success(value) =>

                  println(s"Succsss :: ${value.toString}")

              }

            }

          }
          main.init()

        case "updatePushedToQueue" =>


          object main extends TestAppTrait {
            def init(): Try[Unit] = Try {

              implicit val sr_logger: SRLogger = new SRLogger("updatePushedToQueue")

              val res = schedulerIntegrityReportDao.updatePushedToQueue(reportId = 1941L)

              res match {

                case Failure(e) =>

                  println(s" error while getting inconsistent campaign ${e.printStackTrace()}")

                case Success(value) =>

                  println(s"Succsss :: ${value.toString}")

              }

            }

          }
          main.init()


        case "updateDataIntegrityCheckQueueStatusToCompleted" =>

          object main extends TestAppTrait {
            def init(): Try[Unit] = Try {

              implicit val sr_logger: SRLogger = new SRLogger("updateDataIntegrityCheckQueueStatusToCompleted")

              val res = schedulerIntegrityReportDao.updateDataIntegrityCheckQueueStatusToCompleted(reportId = 1941L,reportData = Some(ReportData.WrongStepIdData(
                message = "Shubham Ran it via test app",
                report_type = ReportType.WRONG_STEP_ID,
                campaign_id = List(CampaignId(id = 1L)),
                team_id = List(TeamId(1L))
              ) ))

              res match {

                case Failure(e) =>

                  println(s" error while getting inconsistent campaign ${e.printStackTrace()}")

                case Success(value) =>

                  println(s"Succsss :: ${value.toString}")

              }

            }

          }
          main.init()

        case "getDueTasksForStoppedOrArchivedCampaign" =>

          object main extends TestAppTrait {
            def init(): Try[Unit] = Try {

              implicit val sr_logger: SRLogger = new SRLogger("updateDataIntegrityCheckQueueStatusToCompleted")

              val res = campaignDAO.getDueTasksForStoppedOrArchivedCampaign

              res match {

                case Failure(e) =>

                  println(s" error while getting inconsistent campaign ${e.printStackTrace()}")

                case Success(value) =>

                  println(s"Succsss :: ${value.toString}")

              }

            }

          }
          main.init()

        case "getCampaignsWhoseProspectHaveOptedOutButEmailsArePresent" =>

          object main extends TestAppTrait {
            def init(): Try[Unit] = Try {

              implicit val sr_logger: SRLogger = new SRLogger("getCampaignsWhoseProspectHaveOptedOutButEmailsArePresent")

              val res = campaignDAO.getCampaignsWhoseProspectHaveOptedOutButEmailsArePresent

              res match {

                case Failure(e) =>

                  println(s" error while getting inconsistent campaign ${e.printStackTrace()}")

                case Success(value) =>

                  println(s"Succsss :: ${value.toString}")

              }

            }

          }
          main.init()

        case "getCampaignsWhoseProspectHaveOptedOutButDueTasksArePresent" =>

          object main extends TestAppTrait {
            def init(): Try[Unit] = Try {

              implicit val sr_logger: SRLogger = new SRLogger("getCampaignsWhoseProspectHaveOptedOutButDueTasksArePresent")

              val res = campaignDAO.getCampaignsWhoseProspectHaveOptedOutButDueTasksArePresent

              res match {

                case Failure(e) =>

                  println(s" error while getting inconsistent campaign ${e.printStackTrace()}")

                case Success(value) =>

                  println(s"Succsss :: ${value.toString}")

              }

            }

          }
          main.init()

      }

    }
  }
}
