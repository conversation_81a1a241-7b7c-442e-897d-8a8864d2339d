package utils.testapp

import api.AppConfig
import api.accounts.email.models.{EmailProvidedBy, EmailServiceProvider}
import api.accounts.{Account, TeamId, TeamMember}
import api.accounts.models.AccountId
import api.campaigns.models.SearchParams
import api.emails.{EmailSettingDAO, EmailSettingForm}
import api.emails.models.EmailSettingUuid
import api.prospects.InferredQueryTimeline
import api.search.SearchQuery
import io.smartreach.esp.api.emails.EmailSettingId
import org.joda.time.DateTime
import play.api.Logging
import scalikejdbc.config.DBs
import utils.SRLogger
import utils.cronjobs.email_setting_deletion.model.EmailSettingStatus
import utils.dependencyinjectionutils.Tasks.TaskDAO_DI
import utils.dependencyinjectionutils._
import utils.uuid.SrUuidUtils

import scala.util.{Failure, Success}

object Test_EmailSettingsDAO extends Logging with TestAppTrait {

  def main(args: Array[String]): Unit = {

    if (args.length > 0) {
      DBs.setupAll()

      val applicationName = args(0)

      given Logger: SRLogger = new SRLogger("Test_EmailSettingsDAO")

      applicationName match {

        case "sendingEmailAccountsForScheduling" =>

          logger.info("BEFORE test")
          val srUuidUtils = new SrUuidUtils
          val emailSettingDAO = new EmailSettingDAO(
            srUuidUtils = srUuidUtils
          )

          val result = emailSettingDAO.sendingEmailAccountsForScheduling(
            senderEmailSettingIds = Seq(194),
          )
          logger.info(s"AFTER test  $result")


        case "findAll" =>

          logger.info("BEFORE test")
          val srUuidUtils = new SrUuidUtils
          val emailSettingDAO = new EmailSettingDAO(
            srUuidUtils = srUuidUtils
          )

          val resultNone = emailSettingDAO.findAll(
            accountIds = Seq(123),
            teamIds = Seq()
          )

          val resultSome = emailSettingDAO.findAll(
            accountIds = Seq(123),
            teamIds = Seq(3)
          )

          logger.info(s"AFTER test  $resultNone")

        case "fetchInboxesForReplyTrackingQueue" =>

          logger.info("BEFORE test")
          val srUuidUtils = new SrUuidUtils
          val emailSettingDAO = new EmailSettingDAO(
            srUuidUtils = srUuidUtils
          )

          val result = emailSettingDAO.fetchInboxesForReplyTrackingQueue(
            logger = new SRLogger("fetchInboxesForReplyTrackingQueue"),
          )
          logger.info(s"AFTER test  $result")

        case "fetchTeamInboxesForReplyTrackingQueue" =>

          logger.info("BEFORE test")
          val srUuidUtils = new SrUuidUtils
          val emailSettingDAO = new EmailSettingDAO(
            srUuidUtils = srUuidUtils
          )

          val result = emailSettingDAO.fetchTeamInboxesForReplyTrackingQueue(
            logger = new SRLogger("fetchTeamInboxesForReplyTrackingQueue"),
          )
          logger.info(s"AFTER test  $result")


        case "findForScheduling" =>

          logger.info("BEFORE test")
          val srUuidUtils = new SrUuidUtils
          val emailSettingDAO = new EmailSettingDAO(
            srUuidUtils = srUuidUtils
          )

          val result = emailSettingDAO.findForScheduling(
            srLogger = new SRLogger("fetchTeamInboxesForReplyTrackingQueue"),
            channelId = 123
          )
          logger.info(s"AFTER test  $result")

        case "fetchEmailAccountForScheduling" =>

          logger.info("BEFORE test")
          val srUuidUtils = new SrUuidUtils
          val emailSettingDAO = new EmailSettingDAO(
            srUuidUtils = srUuidUtils
          )

          val result = emailSettingDAO.fetchEmailAccountForScheduling()
          logger.info(s"AFTER test  $result")

        case "getAllEmailSettingsInTeam" =>

          logger.info("BEFORE test")
          val srUuidUtils = new SrUuidUtils
          val emailSettingDAO = new EmailSettingDAO(
            srUuidUtils = srUuidUtils
          )

          val result = emailSettingDAO.getAllEmailSettingsInTeam(
            team_id = 321
          )
          logger.info(s"AFTER test  $result")


        case "findByEmailAndTeamId" =>

          logger.info("BEFORE test")
          val srUuidUtils = new SrUuidUtils
          val emailSettingDAO = new EmailSettingDAO(
            srUuidUtils = srUuidUtils
          )

          val result = emailSettingDAO.findByEmailAndTeamId(
            teamId = 321, email = "<EMAIL>"
          )
          logger.info(s"AFTER test  $result")


        case "findAllByEmailAndTeamId" =>

          logger.info("BEFORE test")
          val srUuidUtils = new SrUuidUtils
          val emailSettingDAO = new EmailSettingDAO(
            srUuidUtils = srUuidUtils
          )

          val result = emailSettingDAO.findAllByEmailAndTeamId(
            teamId = 321, email = "<EMAIL>"
          )
          logger.info(s"AFTER test  $result")


        case "find" =>

          logger.info("BEFORE test")
          val srUuidUtils = new SrUuidUtils
          val emailSettingDAO = new EmailSettingDAO(
            srUuidUtils = srUuidUtils
          )

          val result = emailSettingDAO.find(
            id = 468
          )
          logger.info(s"AFTER test  $result")


        case "findV2" =>

          logger.info("BEFORE test")
          val srUuidUtils = new SrUuidUtils
          val emailSettingDAO = new EmailSettingDAO(
            srUuidUtils = srUuidUtils
          )

          val result = emailSettingDAO.find(
            id = EmailSettingId(468), teamId = TeamId(444)
          )
          logger.info(s"AFTER test  $result")


        case "findV3" =>

          logger.info("BEFORE test")
          val srUuidUtils = new SrUuidUtils
          val emailSettingDAO = new EmailSettingDAO(
            srUuidUtils = srUuidUtils
          )

          val result = emailSettingDAO.find(
            id = 468, accountIds = Seq(79L), teamId = 444L
          )
          logger.info(s"AFTER test  $result")

        case "findV4" =>

          logger.info("BEFORE test")
          val srUuidUtils = new SrUuidUtils
          val emailSettingDAO = new EmailSettingDAO(
            srUuidUtils = srUuidUtils
          )

          val result = emailSettingDAO.find(
            senderEmailSettingIds = Seq(EmailSettingId(468L)), teamId = TeamId(444)
          )
          logger.info(s"AFTER test  $result")


        case "findV5" =>

          logger.info("BEFORE test")
          val srUuidUtils = new SrUuidUtils
          val emailSettingDAO = new EmailSettingDAO(
            srUuidUtils = srUuidUtils
          )

          val result = emailSettingDAO.find(
            ids = Seq(EmailSettingId(468)), permittedAccountIds = Seq(79L), teamId = 444L
          )
          logger.info(s"AFTER test  $result")


        case "findV6" =>

          logger.info("BEFORE test")
          val srUuidUtils = new SrUuidUtils
          val emailSettingDAO = new EmailSettingDAO(
            srUuidUtils = srUuidUtils
          )

          val result = emailSettingDAO.find(
            emailSettingUuids = Seq(EmailSettingUuid("eml_set_2d5V88cDWZKDkr4iCo4xJY8lKyj")), teamId = TeamId(444)
          )
          logger.info(s"AFTER test  $result")


        case "findByIds" =>

          logger.info("BEFORE test")
          val srUuidUtils = new SrUuidUtils
          val emailSettingDAO = new EmailSettingDAO(
            srUuidUtils = srUuidUtils
          )

          val result = emailSettingDAO.findByIds(
            ids = Seq(464), teamId = 444, permittedAccountIds = Seq(79)
          )
          logger.info(s"AFTER test  $result")
        case "findSendersForReceiver" =>

          logger.info("BEFORE test")
          val srUuidUtils = new SrUuidUtils
          val emailSettingDAO = new EmailSettingDAO(
            srUuidUtils = srUuidUtils
          )

          val result = emailSettingDAO.findSendersForReceiver(
            esIds = Seq(464),
          )
          logger.info(s"AFTER test  $result")

        case "getActiveSenderEmailSettingIds" =>

          logger.info("BEFORE test")
          val srUuidUtils = new SrUuidUtils
          val emailSettingDAO = new EmailSettingDAO(
            srUuidUtils = srUuidUtils
          )

          val result = emailSettingDAO.getActiveSenderEmailSettingIds(
            orgId = 59,
          )
          logger.info(s"AFTER test  $result")


        case "insert_email_setting" =>

          logger.info("BEFORE test")

          val account = accountService.find(id = 576).get
          val teamMember = account.teams.flatMap(_.access_members).filter(_.email == account.email).head

          emailAccountService.createEmail(
            data = EmailSettingForm(
              email = "<EMAIL>",
              service_provider = EmailServiceProvider.OTHER,

              smtp_username = Some("<EMAIL>"),
              smtp_password = Some("password"),
              smtp_host = Some(AppConfig.Maildoso.SMTP_HOST),
              smtp_port = Some(AppConfig.Maildoso.SMTP_PORT),

              imap_username = Some("<EMAIL>"),
              imap_password = Some("password"),
              imap_host = Some("imap.hesperus.maildoso.com"),
              imap_port = Some(993),

              email_domain = Some("usesmartreach.click"),
              api_key = None,
              mailgun_region = None,

              quota_per_day = 100,
              min_delay_seconds = EmailServiceProvider.OTHER.min_delay_seconds,
              max_delay_seconds = EmailServiceProvider.OTHER.max_delay_seconds,

              can_send = true,
              can_receive = true,

              cc_emails = None,
              bcc_emails = None,
              provided_by = EmailProvidedBy.Maildoso,

              first_name = "Balaji",
              last_name = "Ramesh",
              status = EmailSettingStatus.InActive,

              platform_email_id = Some("f788747a-954a-43fb-a4b7-671f7b3ff18c-3"),
              email_tag = None
            ),
            account = account,
            ta = teamMember
          ) match {
            case Right(value) =>

              println(s"Successfully created email setting for email: ${value}, teamId: ${value.team_id}")
            //
            case Left(value) =>

              logger.error(s"Error occurred while creating email setting for emai :: value: ${value}")
          }


      }
    }

  }

}

