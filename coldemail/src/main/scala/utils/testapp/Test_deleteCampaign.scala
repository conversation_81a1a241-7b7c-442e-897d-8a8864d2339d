package utils.testapp

import api.accounts.TeamId
import api.campaigns.CampaignUpdateNameForm
import play.api.Logging
import scalikejdbc.AutoSession
import scalikejdbc.config.DBs
import utils.dependencyinjectionutils.Tasks.TaskDAO_DI
import utils.dependencyinjectionutils.{BlacklistProspectCheckDAO_DI, CacheServiceJedisDI, CampaignDAO_DI, CampaignProspectDAO_2DI, CampaignSchedulingMetadataDAO_DI, DBCounterDAO_DI, DBUtils_DI, EmailThreadDAO_DI, FreeEmailDomainListDAO_DI, FreeEmailDomainListService_DI, LinkedinTaskServiceDI, MergeTagService_DI, OrganizationDAO_DI, ProspectAccountDAO1DI, ProspectAddEventDAO_DI, ProspectColumnDefDAO_DI, ProspectColumnDef_DI, ProspectDAOService_DI, ProspectDAO_DI, ProspectQueryDI, ProspectsEmailsDAO_DI, ReplySentimentDAOService_DI, ReplySentimentDAO_DI, ReplySentimentJedisDAO_DI, SrDBQueryCounterService_DI, SrUuidUtilsDI}
import utils.testapp.Test_updateName.logger

import scala.util.{Failure, Success}

object Test_deleteCampaign extends Logging {

  implicit val session: AutoSession.type = AutoSession

  def main(args: Array[String]): Unit = {

    if (args.length > 0) {
      DBs.setupAll()

      val applicationName = args(0)

      applicationName match {

        case "test_deleteCampaign" =>

          logger.info("BEFORE test")

          object Test extends TestAppTrait {

              def init() = {

                val id: Long = 236L
                val team_id: TeamId = TeamId(id = 132)
                campaignDAO.deleteCampaign(
                  campaignId = id,
                  team_id = team_id
                ) match {
                  case Failure(exception) =>
                    logger.info(s"Failure  $exception")
                  case Success(value) =>
                    logger.info(s"Success $value")
                }
              }
            }
          Test.init()
          logger.info("AFTER test")
      }
    }
  }
}

