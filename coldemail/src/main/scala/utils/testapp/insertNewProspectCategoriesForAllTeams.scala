package utils.testapp

import play.api.Logging
import scalikejdbc.config.DBs

import scala.util.{Failure, Success}

object insertNewProspectCategoriesForAllTeams extends Logging with TestAppTrait {

  def main(args: Array[String]): Unit = {

    if (args.length > 0) {

      DBs.setupAll()

      val applicationName = args(0)

      applicationName match {

        case "insertNewProspectCategoriesForAllTeams" =>

          migrationUtils.migrateOlderAccountsIntoProspectCategoryCustom match {

            case Failure(e) =>

              logger.error(message = s"FAILED MIGRATION", error = e)

            case Success(value) =>

              println(s"SUCCESS MIGRATION -- $value")

              logger.info(s"Success MIGRATION ${value.toList}")

          }

      }

    }

  }

}
