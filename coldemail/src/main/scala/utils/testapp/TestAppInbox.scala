package utils.testapp

import io.smartreach.esp.api.emails.{AccessToken, EmailSettingId, InternetMessageId, OutlookMessageId}
import io.smartreach.esp.api.microsoftOAuth.EmailSettingUpdateAccessToken
import io.smartreach.esp.utils.email.{OutlookReceiveEmailService, OutlookUtilsApi, OutlookUtilsService}
import org.apache.pekko.actor.ActorSystem
import org.apache.pekko.stream.Materializer
import play.api.Logging
import play.api.libs.ws.ahc.AhcWSClient
import scalikejdbc.config.DBs
import utils.SRLogger

import scala.concurrent.{Await, ExecutionContext}
import scala.util.{Success, Try}

/*
This object holds any sort internal test function used to test features/changes manually during development.
 */
object TestAppInbox extends Logging {

  def main(args: Array[String]): Unit = {

    if (args.length > 0) {
      DBs.setupAll()


      val applicationName = args(0)

      applicationName match {

        case "unreadEmailProspects" =>
        
        /*
          val prospectColumnDef = new ProspectColumnDef
          val emailThreadDAO = new EmailThreadDAO
          val prospectQuery = new  ProspectQuery(
            prospectColumnDef = prospectColumnDef
          )
          val prospectAddEventDAO = new  ProspectAddEventDAO
          val freeEmailDomainListDAO = new  FreeEmailDomainListDAO
          val cacheServiceJedis = new  CacheServiceJedis
          val freeEmailDomainListService = new  FreeEmailDomainListService(
            freeEmailDomainListDAO = freeEmailDomainListDAO,
            cacheServiceJedis = cacheServiceJedis)
          val campaignProspectDAO_2 = new CampaignProspectDAO_2
          val prospectDAO = new Prospect(prospectQuery = prospectQuery,
            prospectAddEventDAO = prospectAddEventDAO,
            prospectColumnDef = prospectColumnDef,
            campaignProspectDAO_2 = campaignProspectDAO_2
          )

          val dbCounterDAO = new  DBCounterDAO(cacheServiceJedis = cacheServiceJedis)
          val srDBQueryCounterService = new  SrDBQueryCounterService(dbCounterDAO = dbCounterDAO)
          val campaignDAO = new  CampaignDAO(prospectDAO = prospectDAO,
            srDBQueryCounterService = srDBQueryCounterService)
          val emailSettingDAO = new EmailSettingDAO(srDBQueryCounterService = srDBQueryCounterService)
          val inboxV3Service:  InboxV3Service = new InboxV3Service( emailSettingDAO = emailSettingDAO,
            emailThreadDAO = emailThreadDAO,
            campaignDAO = campaignDAO
          )
          val replied_at_beforeTime = DateTime.now()

          val logger = new SRLogger("testApp")
*/
        /*
        val convs = inboxV3Service.getNextConversationsForProspects(
          teamId = 4,
          folder = MailboxFolder.Prospects,
          permittedOwnerIds = Seq(47),
          replied_at_beforeTime = replied_at_beforeTime,
          page_size = 20,
          Logger = logger
        );
        Logger.info(s"convs: ${convs}")

         */

        case "vg/markAsImportantAndOpen" =>

          val logger: SRLogger = new SRLogger("testing connection")

          implicit val  system: ActorSystem = ActorSystem()
          implicit val  materializer: Materializer = Materializer(system)
          implicit val  ws: AhcWSClient = AhcWSClient()
          implicit val  ec: ExecutionContext = system.dispatcher

          logger.info("markAsImportantAndOpen")
          object main {

            def init(): Unit = {

              val resFut = OutlookUtilsApi.markAsImportantAndOpen(
                outlookMessageId = OutlookMessageId(id = "messageId"),
                accessToken = AccessToken(id = "accessToken"),
                Logger = logger
              )

              val res = Await.result(resFut, scala.concurrent.duration.Duration.Inf)

              logger.warn(s"markAsImportantAndOpen: $res")
            }
          }

          main.init()


        case "vg/moveToInbox" =>

          val logger: SRLogger = new SRLogger("testing connection")

          implicit val  system: ActorSystem = ActorSystem()
          implicit val  materializer: Materializer = Materializer(system)
          implicit val  ws: AhcWSClient = AhcWSClient()
          implicit val  ec: ExecutionContext = system.dispatcher

          logger.info("moveToInbox")
          object main {

            def init(): Unit = {

              val resFut = OutlookUtilsApi.moveToInbox(
                outlookMessageId = OutlookMessageId(id = "messageId"),
                accessToken = AccessToken(id = "accessToken"),
                Logger = logger
              )

              val res = Await.result(resFut, scala.concurrent.duration.Duration.Inf)

              logger.warn(s"moveToInbox: message_id: ${res.message_id} :: outlook_msg_id: ${res.outlook_msg_id} :: outlook_conversation_id: ${res.outlook_conversation_id}")
            }
          }

          main.init()

        case "guru/getFolderTest" =>
          val logger = new SRLogger("guru/getFolderTest");
          implicit val  system: ActorSystem = ActorSystem()
          implicit val  materializer = Materializer(system)
          implicit val  wSClient: AhcWSClient = AhcWSClient()
          implicit val  actorContext: ExecutionContext = system.dispatcher

          val emailutil = new OutlookUtilsService()

          val data = emailutil.getAllFoldersViaAPI(AccessToken("<ACCESS_TOKEN>"), logger)
          logger.warn(Await.result(data, scala.concurrent.duration.Duration.Inf).toString)


        case "guru/receiveMailByID" =>
          val logger = new SRLogger("guru/remail");
          implicit val  system: ActorSystem = ActorSystem()
          implicit val  materializer = Materializer(system)
          implicit val  wSClient: AhcWSClient = AhcWSClient()
          implicit val  actorContext: ExecutionContext = system.dispatcher

          object receiveoutlook extends OutlookReceiveEmailService {
            override def updateAccessTokenAndRefreshToken(emailSettingId: EmailSettingId, data: EmailSettingUpdateAccessToken): Try[Option[EmailSettingId]] = {
              Success(Some(EmailSettingId(2)));
            }
          }

          val data = receiveoutlook.getMessageByInternetIDViaAPI(
            accessToken = AccessToken("<ACCESS_TOKEN>"),
            internetMessageId = InternetMessageId("<INTERNET_MESSAGE_ID>"),
            Logger = logger
          );

          logger.warn(Await.result(data, scala.concurrent.duration.Duration.Inf).toString)



      }
    }

  }
}
