package utils.testapp

import scalikejdbc.config.DBs
import utils.SRLogger
import utils.aws.S3

import scala.util.{Failure, Success}


object Test_AWSPreSigner extends TestAppTrait {

  def main(args: Array[String]): Unit = {

    if (args.length > 0) {

      DBs.setupAll()

      given Logger: SRLogger = new SRLogger(logRequestId = "Test_AWSPreSigner")

      val applicationName = args(0)

      applicationName match {

        case "test_pre_sign" =>

          val filename = "some-file-name"

          S3.genPresignedUrl(filename = filename) match {
            case Failure(exception) =>

              println(exception)

            case Success(value) =>

              println("PRE SIGNED URL GENERATOR:")

              println(value)

          }

      }

    }

  }

}
