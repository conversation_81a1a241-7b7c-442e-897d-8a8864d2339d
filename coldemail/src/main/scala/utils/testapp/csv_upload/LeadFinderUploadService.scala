/* 17-jun-2025: [DO_NOT_REMOVE] [LEADFINDERUPLOAD] was slowing down compilation. Only needed for the Lead database upload.

package utils.testapp.csv_upload

import api.AppConfig
import api.prospects.UploadServiceTrait
import com.google.common.hash.Hashing
import org.joda.time.DateTime
import play.api.libs.json.{JsError, JsSuccess, JsValue, Json}
import utils.GCP.CloudStorage
import utils.filedownloader.FileDownloader
import utils.sr_csv_parser.{CSVParseFileType, SRCsvParserRawResult, SRCsvReaderUtils, SRCustomCSVParserResult, UnivocityParser}
import utils.testapp.csv_upload_utils.{LinkedinEducationDataUtil, LinkedinExperienceDataUtils}
import utils.{Helpers, SRLogger}
import utils.uuid.SrUuidUtils

import sys.process._
import java.io.File
import java.nio.charset.StandardCharsets
import scala.concurrent.duration.DurationInt
import scala.concurrent.{Await, ExecutionContext, Future}
import scala.util.{Failure, Success, Try}


case class CsvQueueCreateFormData(
                                   list_name: Option[String],
                                   file_url: String,
                                   column_map: JsValue,
                                   dt_src: String
                                 )

case class CsvQueueData(
                         id: Long,
                         account_id: Long,
                         team_id: Long,
                         ta_id: Long,
                         loggedIn_id: Long,
                         file_name: String,
                         file_url: String,
                         created_at: DateTime,
                         uploaded_at: Option[DateTime],
                         has_been_uploaded: Boolean,
                         column_map: JsValue,
                         error: Option[String],
                         error_at: Option[DateTime],
                         dt_src: Option[String]
                       )

case class LinkedinEducationData(
                                  degrees_0: Option[String],
                                  degrees_1: Option[String],
                                  degrees_2: Option[String],
                                  degrees_3: Option[String],
                                  end_date: Option[String],
                                  gpa: Option[String],
                                  majors_0: Option[String],
                                  majors_1: Option[String],
                                  majors_2: Option[String],
                                  majors_3: Option[String],
                                  majors_4: Option[String],
                                  minors: Option[String],
                                  minors_0: Option[String],
                                  minors_1: Option[String],
                                  school_domain: Option[String],
                                  school_facebook_url: Option[String],
                                  school: Option[String],
                                  school_id: Option[String],
                                  school_linkedin_id: Option[String],
                                  school_linkedin_url: Option[String],
                                  school_location_continent: Option[String],
                                  school_location_country: Option[String],
                                  school_location_locality: Option[String],
                                  school_location_name: Option[String],
                                  school_location_region: Option[String],
                                  school_name: Option[String],
                                  school_twitter_url: Option[String],
                                  school_type: Option[String],
                                  school_website: Option[String],
                                  start_date: Option[String],
                                  summary: Option[String],
                                  degrees: Option[String],
                                  majors: Option[String],
                                  school_location: Option[String],
                                  education_data_hash: Option[String],
                                )

case class LinkedinExperienceData(
                                   company_facebook_url: Option[String],
                                   company_founded: Option[String],
                                   company_id: Option[String],
                                   company_industry: Option[String],
                                   company_linkedin_id: Option[String],
                                   company_linkedin_url: Option[String],
                                   company_location: Option[String],
                                   company_location_address_line_2: Option[String],
                                   company_location_continent: Option[String],
                                   company_location_country: Option[String],
                                   company_location_geo: Option[String],
                                   company_location_locality: Option[String],
                                   company_location_metro: Option[String],
                                   company_location_name: Option[String],
                                   company_location_postal_code: Option[String],
                                   company_location_region: Option[String],
                                   company_location_street_address: Option[String],
                                   company_name: Option[String],
                                   company_size: Option[String],
                                   company_twitter_url: Option[String],
                                   company_website: Option[String],
                                   end_date: Option[String],
                                   is_primary: Option[String],
                                   location_names: Option[String],
                                   location_names_0: Option[String],
                                   location_names_1: Option[String],
                                   location_names_2: Option[String],
                                   location_names_3: Option[String],
                                   start_date: Option[String],
                                   summary: Option[String],
                                   title_levels: Option[String],
                                   title_levels_0: Option[String],
                                   title_levels_1: Option[String],
                                   title_levels_2: Option[String],
                                   title_name: Option[String],
                                   title_role: Option[String],
                                   title_sub_role: Option[String],
                                   experience_data_hash: Option[String]
                                 )

case class LinkedinAdditionalData(
                                   certification_end_date: Option[String],
                                   certification_name: Option[String],
                                   certification_organization: Option[String],
                                   certification_start_date: Option[String],
                                   interest: Option[String],
                                   job_title_level: Option[String],
                                   language_name: Option[String],
                                   language_proficiency: Option[String],
                                   location_name: Option[String],
                                   phone_number: Option[String],
                                   profile_id: Option[String],
                                   profiles_network: Option[String],
                                   profile_url: Option[String],
                                   profile_username: Option[String],
                                   region: Option[String],
                                   skill: Option[String],
                                   country: Option[String],
                                   email_address: Option[String],
                                   email_type: Option[String],
                                   street_addresses_address_line_2: Option[String],
                                   street_addresses_continent: Option[String],
                                   street_addresses_country: Option[String],
                                   street_addresses_geo: Option[String],
                                   street_addresses_locality: Option[String],
                                   street_addresses_metro: Option[String],
                                   street_addresses_name: Option[String],
                                   street_addresses_postal_code: Option[String],
                                   street_addresses_region: Option[String],
                                   street_addresses_street_address: Option[String],
                                   additional_data_hash: Option[String]
                                 )



case class CsvUploadColLinkedin(
                                 anzsic_2006_code: Option[String],
                                 anzsic_2006_description: Option[String],
                                 address: Option[String],
                                 address_1: Option[String],
                                 address_2: Option[String],
                                 address_line_1: Option[String],
                                 address_line_2: Option[String],
                                 address_line_3: Option[String],
                                 app_using: Option[String],
                                 assets__usd: Option[String],
                                 birth_date: Option[String],
                                 birth_year: Option[String],
                                 business_description: Option[String],
                                 city: Option[String],
                                 company: Option[String],
                                 company_city: Option[String],
                                 company_country: Option[String],
                                 company_name: Option[String],
                                 company_state: Option[String],
                                 company_street_address: Option[String],
                                 company_zip_postal_code: Option[String],
                                 company_division_name: Option[String],
                                 company_domain_name: Option[String],
                                 company_phone_number: Option[String],
                                 contact: Option[String],
                                 contact_level: Option[String],
                                 contact_name: Option[String],
                                 contact_title: Option[String],
                                 countries: Option[String],
                                 country: Option[String],
                                 country_region: Option[String],
                                 county: Option[String],
                                 dnb_hoovers_industry: Option[String],
                                 direct_marketing_status: Option[String],
                                 direct_phone: Option[String],
                                 direct_phone_number: Option[String],
                                 email: Option[String],
                                 email_domain: Option[String],
                                 email_address: Option[String],
                                 emails: Option[String],
                                 employee_size: Option[String],
                                 employees: Option[String],
                                 employees__all_sites: Option[String],
                                 employees__single_site: Option[String],
                                 employees_range: Option[String],
                                 encrypted_email_address: Option[String],
                                 entity_type: Option[String],
                                 fax: Option[String],
                                 first_name: Option[String],
                                 full_name: Option[String],
                                 gender: Option[String],
                                 highest_level_job_function: Option[String],
                                 isic_rev_4_code: Option[String],
                                 isic_rev_4_description: Option[String],
                                 industry: Option[String],
                                 industry_type: Option[String],
                                 industry_hierarchical_category: Option[String],
                                 industry_label: Option[String],
                                 job_function: Option[String],
                                 job_title: Option[String],
                                 job_title_hierarchy_level: Option[String],
                                 last_name: Option[String],
                                 liabilities__usd: Option[String],
                                 linkedin_url: Option[String],
                                 linkedin_links: Option[String],
                                 locality: Option[String],
                                 location: Option[String],
                                 management_level: Option[String],
                                 middle_name: Option[String],
                                 nace_rev_2_code: Option[String],
                                 nace_rev_2_description: Option[String],
                                 naics: Option[String],
                                 naics_2012_code: Option[String],
                                 naics_2012_description: Option[String],
                                 naics1: Option[String],
                                 naics2: Option[String],
                                 ownership_type: Option[String],
                                 parent_company: Option[String],
                                 parent_country_region: Option[String],
                                 person_city: Option[String],
                                 person_pro_url: Option[String],
                                 person_state: Option[String],
                                 person_zip: Option[String],
                                 phone: Option[String],
                                 phone_number: Option[String],
                                 postal_code: Option[String],
                                 pre_tax_profit__usd: Option[String],
                                 query_name: Option[String],
                                 revenue: Option[String],
                                 revenue__usd: Option[String],
                                 revenue__in_u: Option[String],
                                 revenue__in_000s: Option[String],
                                 revenue_range: Option[String],
                                 sic: Option[String],
                                 sic_code: Option[String],
                                 sic1: Option[String],
                                 sic2: Option[String],
                                 sales: Option[String],
                                 salutation: Option[String],
                                 secondary_industry_hierarchical_category: Option[String],
                                 secondary_industry_label: Option[String],
                                 source_count: Option[String],
                                 state: Option[String],
                                 state_or_province: Option[String],
                                 suffix: Option[String],
                                 ticker: Option[String],
                                 title: Option[String],
                                 titlecode: Option[String],
                                 total_employees: Option[String],
                                 tradestyle: Option[String],
                                 uk_sic_2007_code: Option[String],
                                 uk_sic_2007_description: Option[String],
                                 url: Option[String],
                                 us_sic_1987_code: Option[String],
                                 us_sic_1987_description: Option[String],
                                 ultimate_parent_company: Option[String],
                                 ultimate_parent_country_region: Option[String],
                                 website: Option[String],
                                 zip: Option[String],
                                 zoom_individual_id: Option[String],
                                 zoom_company_id: Option[String],
                                 zoominfo_industry: Option[String],
                                 source_data_id_1: Option[String],
                                 company_address: Option[String],
                                 company_founded: Option[String],
                                 company_industry: Option[String],
                                 company_linkedin_id: Option[String],
                                 company_linkedin_url: Option[String],
                                 company_meta_description: Option[String],
                                 company_meta_emails: Option[String],
                                 company_meta_keywords: Option[String],
                                 company_meta_phones: Option[String],
                                 company_meta_title: Option[String],
                                 company_size: Option[String],
                                 company_type: Option[String],
                                 email_format: Option[String],
                                 facebook_id: Option[String],
                                 facebook_url: Option[String],
                                 facebook_username: Option[String],
                                 github_url: Option[String],
                                 github_username: Option[String],
                                 source_data_id_2: Option[String],
                                 inferred_salary: Option[String],
                                 inferred_years_experience: Option[String],
                                 job_company_facebook_url: Option[String],
                                 job_company_founded: Option[String],
                                 job_company_id: Option[String],
                                 job_company_industry: Option[String],
                                 job_company_linkedin_id: Option[String],
                                 job_company_linkedin_url: Option[String],
                                 job_company_location_address_line_2: Option[String],
                                 job_company_location_continent: Option[String],
                                 job_company_location_country: Option[String],
                                 job_company_location_geo: Option[String],
                                 job_company_location_locality: Option[String],
                                 job_company_location_metro: Option[String],
                                 job_company_location_name: Option[String],
                                 job_company_location_postal_code: Option[String],
                                 job_company_location_region: Option[String],
                                 job_company_location_street_address: Option[String],
                                 job_company_name: Option[String],
                                 job_company_size: Option[String],
                                 job_company_twitter_url: Option[String],
                                 job_company_website: Option[String],
                                 job_last_updated: Option[String],
                                 job_start_date: Option[String],
                                 job_summary: Option[String],
                                 job_title_role: Option[String],
                                 job_title_sub_role: Option[String],
                                 linkedin_connections: Option[String],
                                 linkedin_id: Option[String],
                                 linkedin_username: Option[String],
                                 list_name: Option[String],
                                 location_address_line_2: Option[String],
                                 location_continent: Option[String],
                                 location_country: Option[String],
                                 location_geo: Option[String],
                                 location_last_updated: Option[String],
                                 location_locality: Option[String],
                                 location_metro: Option[String],
                                 location_name: Option[String],
                                 location_postal_code: Option[String],
                                 location_region: Option[String],
                                 location_street_address: Option[String],
                                 middle_initial: Option[String],
                                 mobile_phone: Option[String],
                                 person_business_email: Option[String],
                                 person_company_name: Option[String],
                                 person_first_name: Option[String],
                                 person_headline: Option[String],
                                 person_job_title: Option[String],
                                 person_last_name: Option[String],
                                 person_linkedin_id: Option[String],
                                 person_linkedin_url: Option[String],
                                 person_location: Option[String],
                                 person_personal_email: Option[String],
                                 person_phone: Option[String],
                                 query: Option[String],
                                 summary: Option[String],
                                 twitter_url: Option[String],
                                 twitter_username: Option[String],
                                 version_status_contains_0: Option[String],
                                 version_status_contains_1: Option[String],
                                 version_status_contains_2: Option[String],
                                 version_status_current_version: Option[String],
                                 version_status_previous_version: Option[String],
                                 version_status_status: Option[String],
                                 work_email: Option[String],
                                 prospect_data_hash: String,
                                 education_data_list: Option[Map[Long, LinkedinEducationData]],
                                 experience_data_list: Option[Map[Long, LinkedinExperienceData]],
                                 additional_data_list: Option[Map[Long, LinkedinAdditionalData]]
                               )

class LeadFinderUploadService(
                               srUuidUtils: SrUuidUtils,
                               srCsvReaderUtils: SRCsvReaderUtils,
                               leadFinderUploadDao: LeadFinderUploadDao,
                               univocityParser: UnivocityParser
                             ) extends UploadServiceTrait {

  val csvParserExecutablePath = if (!AppConfig.isLocalDevDomain) s"/mnt/disks/jvm-disk-main-worker/ubuntu/srbuild/resources/sr_csv_parser/csv2_ubuntu.exe" else AppConfig.localCsvParserExecutablePath

  val parserName: String = "CustomCSVParser"

  def uploadCsvDetails(
                        accountId: Long,
                        teamId: Long,
                        ta_id: Long,
                        loggedIn_id: Long,
                        data: Seq[CsvQueueCreateFormData]
                      ): Try[Seq[CsvQueueData]] = {

    val uuid = srUuidUtils.generateLeadFinderProspectQueueUuid()
    leadFinderUploadDao.insertCsvUploadDetails(
      accountId = accountId,
      teamId = teamId,
      ta_id = ta_id,
      loggedIn_id = loggedIn_id,
      data = data,
    )
  }

  def uploadLinkedinCsvDetails(
                                accountId: Long,
                                teamId: Long,
                                ta_id: Long,
                                loggedIn_id: Long,
                                data: Seq[CsvQueueCreateFormData],
                              ): Try[Seq[CsvQueueData]] = {

    val uuid = srUuidUtils.generateLeadFinderProspectQueueUuid()
    leadFinderUploadDao.insertLinkedinCsvUploadDetails(
      accountId = accountId,
      teamId = teamId,
      ta_id = ta_id,
      loggedIn_id = loggedIn_id,
      data = data,
    )
  }

  def getCsvUploadCol(
                       mappingFromClient: Map[String, String],
                       csvRow: Map[String, String]
                     ): CsvUploadCol = {

    var addressOpt: Option[String] = None
    var address1Opt: Option[String] = None
    var address2Opt: Option[String] = None
    var addressLine1Opt: Option[String] = None
    var addressLine2Opt: Option[String] = None
    var addressLine3Opt: Option[String] = None
    var anzsic2006CodeOpt: Option[String] = None
    var anzsic2006DescriptionOpt: Option[String] = None
    var appUsingOpt: Option[String] = None
    var assetsUsdOpt: Option[String] = None
    var birthDateOpt: Option[String] = None
    var birthYearOpt: Option[String] = None
    var businessDescriptionOpt: Option[String] = None
    var cityOpt: Option[String] = None
    var companyOpt: Option[String] = None
    var companyAddressOpt: Option[String] = None
    var companyCityOpt: Option[String] = None
    var companyCountryOpt: Option[String] = None
    var companyDivisionNameOpt: Option[String] = None
    var companyDomainNameOpt: Option[String] = None
    var companyFoundedOpt: Option[String] = None
    var companyIndustryOpt: Option[String] = None
    var companyLinkedinIdOpt: Option[String] = None
    var companyLinkedinUrlOpt: Option[String] = None
    var companyMetaDescriptionOpt: Option[String] = None
    var companyMetaEmailsOpt: Option[String] = None
    var companyMetaKeywordsOpt: Option[String] = None
    var companyMetaPhonesOpt: Option[String] = None
    var companyMetaTitleOpt: Option[String] = None
    var companyNameOpt: Option[String] = None
    var companyPhoneNumberOpt: Option[String] = None
    var companySizeOpt: Option[String] = None
    var companyStateOpt: Option[String] = None
    var companyStreetAddressOpt: Option[String] = None
    var companyTypeOpt: Option[String] = None
    var companyZipPostalCodeOpt: Option[String] = None
    var contactOpt: Option[String] = None
    var contactLevelOpt: Option[String] = None
    var contactNameOpt: Option[String] = None
    var contactTitleOpt: Option[String] = None
    var countriesOpt: Option[String] = None
    var countryOpt: Option[String] = None
    var countryRegionOpt: Option[String] = None
    var countyOpt: Option[String] = None
    var dnbHooversIndustryOpt: Option[String] = None
    var directMarketingStatusOpt: Option[String] = None
    var directPhoneOpt: Option[String] = None
    var directPhoneNumberOpt: Option[String] = None
    var emailOpt: Option[String] = None
    var emailAddressOpt: Option[String] = None
    var emailDomainOpt: Option[String] = None
    var emailFormatOpt: Option[String] = None
    var emailsOpt: Option[String] = None
    var employeeSizeOpt: Option[String] = None
    var employeesOpt: Option[String] = None
    var employeesAllSitesOpt: Option[String] = None
    var employeesSingleSiteOpt: Option[String] = None
    var employeesRangeOpt: Option[String] = None
    var encryptedEmailAddressOpt: Option[String] = None
    var entityTypeOpt: Option[String] = None
    var faxOpt: Option[String] = None
    var firstNameOpt: Option[String] = None
    var fullNameOpt: Option[String] = None
    var genderOpt: Option[String] = None
    var highestLevelJobFunctionOpt: Option[String] = None
    var industryOpt: Option[String] = None
    var industryHierarchicalCategoryOpt: Option[String] = None
    var industryLabelOpt: Option[String] = None
    var industryTypeOpt: Option[String] = None
    var isicRev4CodeOpt: Option[String] = None
    var isicRev4DescriptionOpt: Option[String] = None
    var jobFunctionOpt: Option[String] = None
    var jobTitleOpt: Option[String] = None
    var jobTitleHierarchyLevelOpt: Option[String] = None
    var lastNameOpt: Option[String] = None
    var liabilitiesUsdOpt: Option[String] = None
    var linkedinLinksOpt: Option[String] = None
    var linkedinUrlOpt: Option[String] = None
    var listNameOpt: Option[String] = None
    var localityOpt: Option[String] = None
    var locationOpt: Option[String] = None
    var managementLevelOpt: Option[String] = None
    var middleNameOpt: Option[String] = None
    var naicsOpt: Option[String] = None
    var naics1Opt: Option[String] = None
    var naics2Opt: Option[String] = None
    var naics2012CodeOpt: Option[String] = None
    var naics2012DescriptionOpt: Option[String] = None
    var naceRev2CodeOpt: Option[String] = None
    var naceRev2DescriptionOpt: Option[String] = None
    var ownershipTypeOpt: Option[String] = None
    var parentCompanyOpt: Option[String] = None
    var parentCountryRegionOpt: Option[String] = None
    var personBusinessEmailOpt: Option[String] = None
    var personCityOpt: Option[String] = None
    var personCompanyNameOpt: Option[String] = None
    var personFirstNameOpt: Option[String] = None
    var personHeadlineOpt: Option[String] = None
    var personJobTitleOpt: Option[String] = None
    var personLastNameOpt: Option[String] = None
    var personLinkedinIdOpt: Option[String] = None
    var personLinkedinUrlOpt: Option[String] = None
    var personLocationOpt: Option[String] = None
    var personPersonalEmailOpt: Option[String] = None
    var personPhoneOpt: Option[String] = None
    var personProUrlOpt: Option[String] = None
    var personStateOpt: Option[String] = None
    var personZipOpt: Option[String] = None
    var phoneOpt: Option[String] = None
    var phoneNumberOpt: Option[String] = None
    var postalCodeOpt: Option[String] = None
    var preTaxProfitUsdOpt: Option[String] = None
    var queryOpt: Option[String] = None
    var queryNameOpt: Option[String] = None
    var revenueOpt: Option[String] = None
    var revenueIn000sOpt: Option[String] = None
    var revenueInUOpt: Option[String] = None
    var revenueUsdOpt: Option[String] = None
    var revenueRangeOpt: Option[String] = None
    var salutationOpt: Option[String] = None
    var salesOpt: Option[String] = None
    var secondaryIndustryHierarchicalCategoryOpt: Option[String] = None
    var secondaryIndustryLabelOpt: Option[String] = None
    var sicOpt: Option[String] = None
    var sic1Opt: Option[String] = None
    var sic2Opt: Option[String] = None
    var sicCodeOpt: Option[String] = None
    var sourceCountOpt: Option[String] = None
    var stateOpt: Option[String] = None
    var stateOrProvinceOpt: Option[String] = None
    var suffixOpt: Option[String] = None
    var tickerOpt: Option[String] = None
    var titleOpt: Option[String] = None
    var titlecodeOpt: Option[String] = None
    var totalEmployeesOpt: Option[String] = None
    var tradestyleOpt: Option[String] = None
    var ukSic2007CodeOpt: Option[String] = None
    var ukSic2007DescriptionOpt: Option[String] = None
    var ultimateParentCompanyOpt: Option[String] = None
    var ultimateParentCountryRegionOpt: Option[String] = None
    var urlOpt: Option[String] = None
    var usSic1987CodeOpt: Option[String] = None
    var usSic1987DescriptionOpt: Option[String] = None
    var websiteOpt: Option[String] = None
    var zipOpt: Option[String] = None
    var zoomCompanyIdOpt: Option[String] = None
    var zoomIndividualIdOpt: Option[String] = None
    var zoominfoIndustryOpt: Option[String] = None
    var prospectDataConcat: String = ""


    mappingFromClient.foreach(header => {

      header._2 match {

        case "address" =>
          addressOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "address_1" =>
          address1Opt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "address_2" =>
          address2Opt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "address_line_1" =>
          addressLine1Opt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "address_line_2" =>
          addressLine2Opt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "address_line_3" =>
          addressLine3Opt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "anzsic_2006_code" =>
          anzsic2006CodeOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "anzsic_2006_description" =>
          anzsic2006DescriptionOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "app_using" =>
          appUsingOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "assets__usd_" =>
          assetsUsdOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "birth_date" =>
          birthDateOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "birth_year" =>
          birthYearOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "business_description" =>
          businessDescriptionOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "city" =>
          cityOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "company" =>
          companyOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "company_address" =>
          companyAddressOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "company_city" =>
          companyCityOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "company_country" =>
          companyCountryOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "company_division_name" =>
          companyDivisionNameOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "company_domain_name" =>
          companyDomainNameOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "company_founded" =>
          companyFoundedOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "company_industry" =>
          companyIndustryOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "company_linkedin_id" =>
          companyLinkedinIdOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "company_linkedin_url" =>
          companyLinkedinUrlOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "company_meta_description" =>
          companyMetaDescriptionOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "company_meta_emails" =>
          companyMetaEmailsOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "company_meta_keywords" =>
          companyMetaKeywordsOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "company_meta_phones" =>
          companyMetaPhonesOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "company_meta_title" =>
          companyMetaTitleOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "company_name" =>
          companyNameOpt = if (csvRow.get(header._1).contains(""))
            None
          else {
            prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
            csvRow.get(header._1)
          }
        case "company_phone_number" =>
          companyPhoneNumberOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "company_size" =>
          companySizeOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "company_state" =>
          companyStateOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "company_street_address" =>
          companyStreetAddressOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "company_type" =>
          companyTypeOpt = if (csvRow.get(header._1).contains(""))
            None
          else {
            prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
            csvRow.get(header._1)
          }
        case "company_zip_postal_code" =>
          companyZipPostalCodeOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "contact" =>
          contactOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "contact_level" =>
          contactLevelOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "contact_name" =>
          contactNameOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "contact_title" =>
          contactTitleOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "countries" =>
          countriesOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "country" =>
          countryOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "country_region" =>
          countryRegionOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "county" =>
          countyOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "dnb_hoovers_industry" =>
          dnbHooversIndustryOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "direct_marketing_status" =>
          directMarketingStatusOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "direct_phone" =>
          directPhoneOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "direct_phone_number" =>
          directPhoneNumberOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "email" =>
          emailOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "email_address" =>
          emailAddressOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "email_domain" =>
          emailDomainOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "email_format" =>
          emailFormatOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "emails" =>
          emailsOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "employee_size" =>
          employeeSizeOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "employees" =>
          employeesOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "employees__all_sites_" =>
          employeesAllSitesOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "employees__single_site_" =>
          employeesSingleSiteOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "employees_range" =>
          employeesRangeOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "encrypted_email_address" =>
          encryptedEmailAddressOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "entity_type" =>

          entityTypeOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "fax" =>
          faxOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "first_name" =>
          firstNameOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "full_name" =>
          fullNameOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "gender" =>
          genderOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "highest_level_job_function" =>
          highestLevelJobFunctionOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "industry" =>
          industryOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "industry_hierarchical_category" =>
          industryHierarchicalCategoryOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "industry_label" =>
          industryLabelOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "industry_type" =>
          industryTypeOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "isic_rev_4_code" =>
          isicRev4CodeOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "isic_rev_4_description" =>
          isicRev4DescriptionOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "job_function" =>
          jobFunctionOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "job_title" =>
          jobTitleOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "job_title_hierarchy_level" =>
          jobTitleHierarchyLevelOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "last_name" =>
          lastNameOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "liabilities__usd_" =>
          liabilitiesUsdOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "linkedin_links" =>
          linkedinLinksOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "linkedin_url" =>
          linkedinUrlOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "list_name" =>
          listNameOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "locality" =>
          localityOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "location" =>
          locationOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "management_level" =>
          managementLevelOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "middle_name" =>
          middleNameOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "naics" =>
          naicsOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "naics1" =>
          naics1Opt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "naics2" =>
          naics2Opt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "naics_2012_code" =>
          naics2012CodeOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "naics_2012_description" =>
          naics2012DescriptionOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "nace_rev_2_code" =>
          naceRev2CodeOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "nace_rev_2_description" =>
          naceRev2DescriptionOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "ownership_type" =>
          ownershipTypeOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "parent_company" =>
          parentCompanyOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "parent_country_region" =>
          parentCountryRegionOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "person_business_email" =>
          personBusinessEmailOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "person_city" =>
          personCityOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "person_company_name" =>
          personCompanyNameOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "person_first_name" =>
          personFirstNameOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "person_headline" =>
          personHeadlineOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "person_job_title" =>
          personJobTitleOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "person_last_name" =>
          personLastNameOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "person_linkedin_id" =>
          personLinkedinIdOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "person_linkedin_url" =>
          personLinkedinUrlOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "person_location" =>
          personLocationOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "person_personal_email" =>
          personPersonalEmailOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "person_phone" =>
          personPhoneOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "person_pro_url" =>
          personProUrlOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "person_state" =>
          personStateOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "person_zip" =>
          personZipOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "phone" =>
          phoneOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "phone_number" =>
          phoneNumberOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "postal_code" =>
          postalCodeOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "pre_tax_profit__usd_" =>
          preTaxProfitUsdOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "query" =>
          queryOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "query_name" =>
          queryNameOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "revenue" =>
          revenueOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "revenue__in_000s_" =>
          revenueIn000sOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "revenue__in_u_" =>
          revenueInUOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "revenue__usd_" =>
          revenueUsdOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "revenue_range" =>
          revenueRangeOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "salutation" =>
          salutationOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "sales" =>
          salesOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "secondary_industry_hierarchical_category" =>
          secondaryIndustryHierarchicalCategoryOpt = if (csvRow.get(header._1).contains(""))
            None
          else {
            prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
            csvRow.get(header._1)
          }
        case "secondary_industry_label" =>
          secondaryIndustryLabelOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "sic" =>
          sicOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "sic1" =>
          sic1Opt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "sic2" =>
          sic2Opt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "sic_code" =>
          sicCodeOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "source_count" =>
          sourceCountOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "state" =>
          stateOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "state_or_province" =>
          stateOrProvinceOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "suffix" =>
          suffixOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "ticker" =>
          tickerOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "title" =>
          titleOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "titlecode" =>
          titlecodeOpt = if (csvRow.get(header._1).contains(""))
            None
          else {
            prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
            csvRow.get(header._1)
          }
        case "total_employees" =>
          totalEmployeesOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "tradestyle" =>
          tradestyleOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "uk_sic_2007_code" =>
          ukSic2007CodeOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "uk_sic_2007_description" =>
          ukSic2007DescriptionOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "ultimate_parent_company" =>
          ultimateParentCompanyOpt = if (csvRow.get(header._1).contains(""))
            None
          else {
            prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
            csvRow.get(header._1)
          }
        case "ultimate_parent_country_region" =>
          ultimateParentCountryRegionOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "url" =>
          urlOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "us_sic_1987_code" =>
          usSic1987CodeOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "us_sic_1987_description" =>
          usSic1987DescriptionOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "website" =>
          websiteOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "zip" =>
          zipOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "zoom_company_id" =>
          zoomCompanyIdOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "zoom_individual_id" =>
          zoomIndividualIdOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "zoominfo_industry" =>
          zoominfoIndustryOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case _ =>
          println(s"11111 ${header._2}")
          None

      }


    })
    val prospectDataHash = Hashing.sha256().hashString(prospectDataConcat, StandardCharsets.UTF_8).toString

    CsvUploadCol(
      address = addressOpt,
      address_1 = address1Opt,
      address_2 = address2Opt,
      address_line_1 = addressLine1Opt,
      address_line_2 = addressLine2Opt,
      address_line_3 = addressLine3Opt,
      anzsic_2006_code = anzsic2006CodeOpt,
      anzsic_2006_description = anzsic2006DescriptionOpt,
      app_using = appUsingOpt,
      assets__usd = assetsUsdOpt,
      birth_date = birthDateOpt,
      birth_year = birthYearOpt,
      business_description = businessDescriptionOpt,
      city = cityOpt,
      company = companyOpt,
      company_address = companyAddressOpt,
      company_city = companyCityOpt,
      company_country = companyCountryOpt,
      company_division_name = companyDivisionNameOpt,
      company_domain_name = companyDomainNameOpt,
      company_founded = companyFoundedOpt,
      company_industry = companyIndustryOpt,
      company_linkedin_id = companyLinkedinIdOpt,
      company_linkedin_url = companyLinkedinUrlOpt,
      company_meta_description = companyMetaDescriptionOpt,
      company_meta_emails = companyMetaEmailsOpt,
      company_meta_keywords = companyMetaKeywordsOpt,
      company_meta_phones = companyMetaPhonesOpt,
      company_meta_title = companyMetaTitleOpt,
      company_name = companyNameOpt,
      company_phone_number = companyPhoneNumberOpt,
      company_size = companySizeOpt,
      company_state = companyStateOpt,
      company_street_address = companyStreetAddressOpt,
      company_type = companyTypeOpt,
      company_zip_postal_code = companyZipPostalCodeOpt,
      contact = contactOpt,
      contact_level = contactLevelOpt,
      contact_name = contactNameOpt,
      contact_title = contactTitleOpt,
      countries = countriesOpt,
      country = countryOpt,
      country_region = countryRegionOpt,
      county = countyOpt,
      dnb_hoovers_industry = dnbHooversIndustryOpt,
      direct_marketing_status = directMarketingStatusOpt,
      direct_phone = directPhoneOpt,
      direct_phone_number = directPhoneNumberOpt,
      email = emailOpt,
      email_address = emailAddressOpt,
      email_domain = emailDomainOpt,
      email_format = emailFormatOpt,
      emails = emailsOpt,
      employee_size = employeeSizeOpt,
      employees = employeesOpt,
      employees__all_sites = employeesAllSitesOpt,
      employees__single_site = employeesSingleSiteOpt,
      employees_range = employeesRangeOpt,
      encrypted_email_address = encryptedEmailAddressOpt,
      entity_type = entityTypeOpt,
      fax = faxOpt,
      first_name = firstNameOpt,
      full_name = fullNameOpt,
      gender = genderOpt,
      highest_level_job_function = highestLevelJobFunctionOpt,
      industry = industryOpt,
      industry_hierarchical_category = industryHierarchicalCategoryOpt,
      industry_label = industryLabelOpt,
      industry_type = industryTypeOpt,
      isic_rev_4_code = isicRev4CodeOpt,
      isic_rev_4_description = isicRev4DescriptionOpt,
      job_function = jobFunctionOpt,
      job_title = jobTitleOpt,
      job_title_hierarchy_level = jobTitleHierarchyLevelOpt,
      last_name = lastNameOpt,
      liabilities__usd = liabilitiesUsdOpt,
      linkedin_links = linkedinLinksOpt,
      linkedin_url = linkedinUrlOpt,
      list_name = listNameOpt,
      locality = localityOpt,
      location = locationOpt,
      management_level = managementLevelOpt,
      middle_name = middleNameOpt,
      naics = naicsOpt,
      naics1 = naics1Opt,
      naics2 = naics2Opt,
      naics_2012_code = naics2012CodeOpt,
      naics_2012_description = naics2012DescriptionOpt,
      nace_rev_2_code = naceRev2CodeOpt,
      nace_rev_2_description = naceRev2DescriptionOpt,
      ownership_type = ownershipTypeOpt,
      parent_company = parentCompanyOpt,
      parent_country_region = parentCountryRegionOpt,
      person_business_email = personBusinessEmailOpt,
      person_city = personCityOpt,
      person_company_name = personCompanyNameOpt,
      person_first_name = personFirstNameOpt,
      person_headline = personHeadlineOpt,
      person_job_title = personJobTitleOpt,
      person_last_name = personLastNameOpt,
      person_linkedin_id = personLinkedinIdOpt,
      person_linkedin_url = personLinkedinUrlOpt,
      person_location = personLocationOpt,
      person_personal_email = personPersonalEmailOpt,
      person_phone = personPhoneOpt,
      person_pro_url = personProUrlOpt,
      person_state = personStateOpt,
      person_zip = personZipOpt,
      phone = phoneOpt,
      phone_number = phoneNumberOpt,
      postal_code = postalCodeOpt,
      pre_tax_profit__usd = preTaxProfitUsdOpt,
      query = queryOpt,
      query_name = queryNameOpt,
      revenue = revenueOpt,
      revenue__in_000s = revenueIn000sOpt,
      revenue__in_u = revenueInUOpt,
      revenue__usd = revenueUsdOpt,
      revenue_range = revenueRangeOpt,
      salutation = salutationOpt,
      sales = salesOpt,
      secondary_industry_hierarchical_category = secondaryIndustryHierarchicalCategoryOpt,
      secondary_industry_label = secondaryIndustryLabelOpt,
      sic = sicOpt,
      sic1 = sic1Opt,
      sic2 = sic2Opt,
      sic_code = sicCodeOpt,
      source_count = sourceCountOpt,
      state = stateOpt,
      state_or_province = stateOrProvinceOpt,
      suffix = suffixOpt,
      ticker = tickerOpt,
      title = titleOpt,
      titlecode = titlecodeOpt,
      total_employees = totalEmployeesOpt,
      tradestyle = tradestyleOpt,
      uk_sic_2007_code = ukSic2007CodeOpt,
      uk_sic_2007_description = ukSic2007DescriptionOpt,
      ultimate_parent_company = ultimateParentCompanyOpt,
      ultimate_parent_country_region = ultimateParentCountryRegionOpt,
      url = urlOpt,
      us_sic_1987_code = usSic1987CodeOpt,
      us_sic_1987_description = usSic1987DescriptionOpt,
      website = websiteOpt,
      zip = zipOpt,
      zoom_company_id = zoomCompanyIdOpt,
      zoom_individual_id = zoomIndividualIdOpt,
      zoominfo_industry = zoominfoIndustryOpt,
      prospect_data_hash = prospectDataHash
    )


  }

  def getCsvUploadColLinkedin(
                               mappingFromClient: Map[String, String],
                               csvRow: Map[String, String]
                             ): CsvUploadColLinkedin = {

    var anzsic_2006_codeOpt: Option[String] = None
    var anzsic_2006_descriptionOpt: Option[String] = None
    var addressOpt: Option[String] = None
    var address_1Opt: Option[String] = None
    var address_2Opt: Option[String] = None
    var address_line_1Opt: Option[String] = None
    var address_line_2Opt: Option[String] = None
    var address_line_3Opt: Option[String] = None
    var app_usingOpt: Option[String] = None
    var assets__usd_Opt: Option[String] = None
    var birth_dateOpt: Option[String] = None
    var birth_yearOpt: Option[String] = None
    var business_descriptionOpt: Option[String] = None
    var cityOpt: Option[String] = None
    var companyOpt: Option[String] = None
    var company_cityOpt: Option[String] = None
    var company_countryOpt: Option[String] = None
    var company_nameOpt: Option[String] = None
    var company_stateOpt: Option[String] = None
    var company_street_addressOpt: Option[String] = None
    var company_zip_postal_codeOpt: Option[String] = None
    var company_division_nameOpt: Option[String] = None
    var company_domain_nameOpt: Option[String] = None
    var company_phone_numberOpt: Option[String] = None
    var contactOpt: Option[String] = None
    var contact_levelOpt: Option[String] = None
    var contact_nameOpt: Option[String] = None
    var contact_titleOpt: Option[String] = None
    var countriesOpt: Option[String] = None
    var countryOpt: Option[String] = None
    var country_regionOpt: Option[String] = None
    var countyOpt: Option[String] = None
    var dnb_hoovers_industryOpt: Option[String] = None
    var direct_marketing_statusOpt: Option[String] = None
    var direct_phoneOpt: Option[String] = None
    var direct_phone_numberOpt: Option[String] = None
    var emailOpt: Option[String] = None
    var email_domainOpt: Option[String] = None
    var email_addressOpt: Option[String] = None
    var emailsOpt: Option[String] = None
    var employee_sizeOpt: Option[String] = None
    var employeesOpt: Option[String] = None
    var employees__all_sites_Opt: Option[String] = None
    var employees__single_site_Opt: Option[String] = None
    var employees_rangeOpt: Option[String] = None
    var encrypted_email_addressOpt: Option[String] = None
    var entity_typeOpt: Option[String] = None
    var faxOpt: Option[String] = None
    var first_nameOpt: Option[String] = None
    var full_nameOpt: Option[String] = None
    var genderOpt: Option[String] = None
    var highest_level_job_functionOpt: Option[String] = None
    var isic_rev_4_codeOpt: Option[String] = None
    var isic_rev_4_descriptionOpt: Option[String] = None
    var industryOpt: Option[String] = None
    var industry_typeOpt: Option[String] = None
    var industry_hierarchical_categoryOpt: Option[String] = None
    var industry_labelOpt: Option[String] = None
    var job_functionOpt: Option[String] = None
    var job_titleOpt: Option[String] = None
    var job_title_hierarchy_levelOpt: Option[String] = None
    var last_nameOpt: Option[String] = None
    var liabilities__usd_Opt: Option[String] = None
    var linkedin_urlOpt: Option[String] = None
    var linkedin_linksOpt: Option[String] = None
    var localityOpt: Option[String] = None
    var locationOpt: Option[String] = None
    var management_levelOpt: Option[String] = None
    var middle_nameOpt: Option[String] = None
    var nace_rev_2_codeOpt: Option[String] = None
    var nace_rev_2_descriptionOpt: Option[String] = None
    var naicsOpt: Option[String] = None
    var naics_2012_codeOpt: Option[String] = None
    var naics_2012_descriptionOpt: Option[String] = None
    var naics1Opt: Option[String] = None
    var naics2Opt: Option[String] = None
    var ownership_typeOpt: Option[String] = None
    var parent_companyOpt: Option[String] = None
    var parent_country_regionOpt: Option[String] = None
    var person_cityOpt: Option[String] = None
    var person_pro_urlOpt: Option[String] = None
    var person_stateOpt: Option[String] = None
    var person_zipOpt: Option[String] = None
    var phoneOpt: Option[String] = None
    var phone_numberOpt: Option[String] = None
    var postal_codeOpt: Option[String] = None
    var pre_tax_profit__usd_Opt: Option[String] = None
    var query_nameOpt: Option[String] = None
    var revenueOpt: Option[String] = None
    var revenue__usd_Opt: Option[String] = None
    var revenue__in_u_Opt: Option[String] = None
    var revenue__in_000s_Opt: Option[String] = None
    var revenue_rangeOpt: Option[String] = None
    var sicOpt: Option[String] = None
    var sic_codeOpt: Option[String] = None
    var sic1Opt: Option[String] = None
    var sic2Opt: Option[String] = None
    var salesOpt: Option[String] = None
    var salutationOpt: Option[String] = None
    var secondary_industry_hierarchical_categoryOpt: Option[String] = None
    var secondary_industry_labelOpt: Option[String] = None
    var source_countOpt: Option[String] = None
    var stateOpt: Option[String] = None
    var state_or_provinceOpt: Option[String] = None
    var suffixOpt: Option[String] = None
    var tickerOpt: Option[String] = None
    var titleOpt: Option[String] = None
    var titlecodeOpt: Option[String] = None
    var total_employeesOpt: Option[String] = None
    var tradestyleOpt: Option[String] = None
    var uk_sic_2007_codeOpt: Option[String] = None
    var uk_sic_2007_descriptionOpt: Option[String] = None
    var urlOpt: Option[String] = None
    var us_sic_1987_codeOpt: Option[String] = None
    var us_sic_1987_descriptionOpt: Option[String] = None
    var ultimate_parent_companyOpt: Option[String] = None
    var ultimate_parent_country_regionOpt: Option[String] = None
    var websiteOpt: Option[String] = None
    var zipOpt: Option[String] = None
    var zoom_individual_idOpt: Option[String] = None
    var zoom_company_idOpt: Option[String] = None
    var zoominfo_industryOpt: Option[String] = None
    var source_data_id_1Opt: Option[String] = None
    var company_addressOpt: Option[String] = None
    var company_foundedOpt: Option[String] = None
    var company_industryOpt: Option[String] = None
    var company_linkedin_idOpt: Option[String] = None
    var company_linkedin_urlOpt: Option[String] = None
    var company_meta_descriptionOpt: Option[String] = None
    var company_meta_emailsOpt: Option[String] = None
    var company_meta_keywordsOpt: Option[String] = None
    var company_meta_phonesOpt: Option[String] = None
    var company_meta_titleOpt: Option[String] = None
    var company_sizeOpt: Option[String] = None
    var company_typeOpt: Option[String] = None
    var email_formatOpt: Option[String] = None
    var facebook_idOpt: Option[String] = None
    var facebook_urlOpt: Option[String] = None
    var facebook_usernameOpt: Option[String] = None
    var github_urlOpt: Option[String] = None
    var github_usernameOpt: Option[String] = None
    var source_data_id_2Opt: Option[String] = None
    var inferred_salaryOpt: Option[String] = None
    var inferred_years_experienceOpt: Option[String] = None
    var job_company_facebook_urlOpt: Option[String] = None
    var job_company_foundedOpt: Option[String] = None
    var job_company_idOpt: Option[String] = None
    var job_company_industryOpt: Option[String] = None
    var job_company_linkedin_idOpt: Option[String] = None
    var job_company_linkedin_urlOpt: Option[String] = None
    var job_company_location_address_line_2Opt: Option[String] = None
    var job_company_location_continentOpt: Option[String] = None
    var job_company_location_countryOpt: Option[String] = None
    var job_company_location_geoOpt: Option[String] = None
    var job_company_location_localityOpt: Option[String] = None
    var job_company_location_metroOpt: Option[String] = None
    var job_company_location_nameOpt: Option[String] = None
    var job_company_location_postal_codeOpt: Option[String] = None
    var job_company_location_regionOpt: Option[String] = None
    var job_company_location_street_addressOpt: Option[String] = None
    var job_company_nameOpt: Option[String] = None
    var job_company_sizeOpt: Option[String] = None
    var job_company_twitter_urlOpt: Option[String] = None
    var job_company_websiteOpt: Option[String] = None
    var job_last_updatedOpt: Option[String] = None
    var job_start_dateOpt: Option[String] = None
    var job_summaryOpt: Option[String] = None
    var job_title_roleOpt: Option[String] = None
    var job_title_sub_roleOpt: Option[String] = None
    var linkedin_connectionsOpt: Option[String] = None
    var linkedin_idOpt: Option[String] = None
    var linkedin_usernameOpt: Option[String] = None
    var list_nameOpt: Option[String] = None
    var location_address_line_2Opt: Option[String] = None
    var location_continentOpt: Option[String] = None
    var location_countryOpt: Option[String] = None
    var location_geoOpt: Option[String] = None
    var location_last_updatedOpt: Option[String] = None
    var location_localityOpt: Option[String] = None
    var location_metroOpt: Option[String] = None
    var location_nameOpt: Option[String] = None
    var location_postal_codeOpt: Option[String] = None
    var location_regionOpt: Option[String] = None
    var location_street_addressOpt: Option[String] = None
    var middle_initialOpt: Option[String] = None
    var mobile_phoneOpt: Option[String] = None
    var person_business_emailOpt: Option[String] = None
    var person_company_nameOpt: Option[String] = None
    var person_first_nameOpt: Option[String] = None
    var person_headlineOpt: Option[String] = None
    var person_job_titleOpt: Option[String] = None
    var person_last_nameOpt: Option[String] = None
    var person_linkedin_idOpt: Option[String] = None
    var person_linkedin_urlOpt: Option[String] = None
    var person_locationOpt: Option[String] = None
    var person_personal_emailOpt: Option[String] = None
    var person_phoneOpt: Option[String] = None
    var queryOpt: Option[String] = None
    var summaryOpt: Option[String] = None
    var twitter_urlOpt: Option[String] = None
    var twitter_usernameOpt: Option[String] = None
    var version_status_contains_0Opt: Option[String] = None
    var version_status_contains_1Opt: Option[String] = None
    var version_status_contains_2Opt: Option[String] = None
    var version_status_current_versionOpt: Option[String] = None
    var version_status_previous_versionOpt: Option[String] = None
    var version_status_statusOpt: Option[String] = None
    var work_emailOpt: Option[String] = None

    var prospectDataConcat: String = ""

    val patternForLinkedinInData = "([A-Z_a-z]+)_(\\d+)_([A-Za-z_\\d]+)".r

    val patternForLinkedinAdditionalData = "([A-Z_a-z]+)_(\\d+)".r

    var linkedinEducationDataMap: Map[Long, LinkedinEducationData] = Map()

    var linkedinExperienceDataMap: Map[Long, LinkedinExperienceData] = Map()

    var linkedinAdditionalDataMap: Map[Long, LinkedinAdditionalData] = Map()

    val linkedinAdditionalDataDefault: LinkedinAdditionalData = LinkedinAdditionalData(
      certification_end_date = None,
      certification_name = None,
      certification_organization = None,
      certification_start_date = None,
      interest = None,
      job_title_level = None,
      language_name = None,
      language_proficiency = None,
      location_name = None,
      phone_number = None,
      profile_id = None,
      profiles_network = None,
      profile_url = None,
      profile_username = None,
      region = None,
      skill = None,
      street_addresses_address_line_2 = None,
      street_addresses_continent = None,
      street_addresses_country = None,
      street_addresses_geo = None,
      street_addresses_locality = None,
      street_addresses_metro = None,
      street_addresses_name = None,
      street_addresses_postal_code = None,
      street_addresses_region = None,
      street_addresses_street_address = None,
      additional_data_hash = None,
      country = None,
      email_address = None,
      email_type = None,
    )

    mappingFromClient.foreach(header => {

      header._2 match {
        case "anzsic_2006_code" =>
          anzsic_2006_codeOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "anzsic_2006_description" =>
          anzsic_2006_descriptionOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "address" =>
          addressOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "address_1" =>
          address_1Opt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "address_2" =>
          address_2Opt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "address_line_1" =>
          address_line_1Opt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "address_line_2" =>
          address_line_2Opt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "address_line_3" =>
          address_line_3Opt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "app_using" =>
          app_usingOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "assets__usd_" =>
          assets__usd_Opt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "birth_date" =>
          birth_dateOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "birth_year" =>
          birth_yearOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "business_description" =>
          business_descriptionOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "city" =>
          cityOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "company" =>
          companyOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "company_city" =>
          company_cityOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "company_country" =>
          company_countryOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "company_name" =>
          company_nameOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "company_state" =>
          company_stateOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "company_street_address" =>
          company_street_addressOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "company_zip_postal_code" =>
          company_zip_postal_codeOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "company_division_name" =>
          company_division_nameOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "company_domain_name" =>
          company_domain_nameOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "company_phone_number" =>
          company_phone_numberOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "contact" =>
          contactOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "contact_level" =>
          contact_levelOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "contact_name" =>
          contact_nameOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "contact_title" =>
          contact_titleOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "countries" =>
          countriesOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "country" =>
          countryOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "country_region" =>
          country_regionOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "county" =>
          countyOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "dnb_hoovers_industry" =>
          dnb_hoovers_industryOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "direct_marketing_status" =>
          direct_marketing_statusOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "direct_phone" =>
          direct_phoneOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "direct_phone_number" =>
          direct_phone_numberOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "email" =>
          emailOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "email_domain" =>
          email_domainOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "email_address" =>
          email_addressOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "emails" =>
          emailsOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "employee_size" =>
          employee_sizeOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "employees" =>
          employeesOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "employees__all_sites_" =>
          employees__all_sites_Opt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "employees__single_site_" =>
          employees__single_site_Opt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "employees_range" =>
          employees_rangeOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "encrypted_email_address" =>
          encrypted_email_addressOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "entity_type" =>
          entity_typeOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "fax" =>
          faxOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "first_name" =>
          first_nameOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "full_name" =>
          full_nameOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "gender" =>
          genderOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "highest_level_job_function" =>
          highest_level_job_functionOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "isic_rev_4_code" =>
          isic_rev_4_codeOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "isic_rev_4_description" =>
          isic_rev_4_descriptionOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "industry" =>
          industryOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "industry_type" =>
          industry_typeOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "industry_hierarchical_category" =>
          industry_hierarchical_categoryOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "industry_label" =>
          industry_labelOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "job_function" =>
          job_functionOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "job_title" =>
          job_titleOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "job_title_hierarchy_level" =>
          job_title_hierarchy_levelOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "last_name" =>
          last_nameOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "liabilities__usd_" =>
          liabilities__usd_Opt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "linkedin_url" =>
          linkedin_urlOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "linkedin_links" =>
          linkedin_linksOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "locality" =>
          localityOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "location" =>
          locationOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "management_level" =>
          management_levelOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "middle_name" =>
          middle_nameOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "nace_rev_2_code" =>
          nace_rev_2_codeOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "nace_rev_2_description" =>
          nace_rev_2_descriptionOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "naics" =>
          naicsOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "naics_2012_code" =>
          naics_2012_codeOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "naics_2012_description" =>
          naics_2012_descriptionOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }
        case "naics1" =>
          naics1Opt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "naics2" =>
          naics2Opt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "ownership_type" =>
          ownership_typeOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "parent_company" =>
          parent_companyOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "parent_country_region" =>
          parent_country_regionOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "person_city" =>
          person_cityOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "person_pro_url" =>
          person_pro_urlOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "person_state" =>
          person_stateOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "person_zip" =>
          person_zipOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "phone" =>
          phoneOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "phone_number" =>
          phone_numberOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "postal_code" =>
          postal_codeOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "pre_tax_profit__usd_" =>
          pre_tax_profit__usd_Opt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "query_name" =>
          query_nameOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "revenue" =>
          revenueOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "revenue__usd_" =>
          revenue__usd_Opt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "revenue__in_u_" =>
          revenue__in_u_Opt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "revenue__in_000s_" =>
          revenue__in_000s_Opt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "revenue_range" =>
          revenue_rangeOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "sic" =>
          sicOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "sic_code" =>
          sic_codeOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "sic1" =>
          sic1Opt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "sic2" =>
          sic2Opt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "sales" =>
          salesOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "salutation" =>
          salutationOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "secondary_industry_hierarchical_category" =>
          secondary_industry_hierarchical_categoryOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "secondary_industry_label" =>
          secondary_industry_labelOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "source_count" =>
          source_countOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "state" =>
          stateOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "state_or_province" =>
          state_or_provinceOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "suffix" =>
          suffixOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "ticker" =>
          tickerOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "title" =>
          titleOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "titlecode" =>
          titlecodeOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "total_employees" =>
          total_employeesOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "tradestyle" =>
          tradestyleOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "uk_sic_2007_code" =>
          uk_sic_2007_codeOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "uk_sic_2007_description" =>
          uk_sic_2007_descriptionOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "url" =>
          urlOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "us_sic_1987_code" =>
          us_sic_1987_codeOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "us_sic_1987_description" =>
          us_sic_1987_descriptionOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "ultimate_parent_company" =>
          ultimate_parent_companyOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "ultimate_parent_country_region" =>
          ultimate_parent_country_regionOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "website" =>
          websiteOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "zip" =>
          zipOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "zoom_individual_id" =>
          zoom_individual_idOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "zoom_company_id" =>
          zoom_company_idOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "zoominfo_industry" =>
          zoominfo_industryOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "_id" =>
          source_data_id_1Opt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "company_address" =>
          company_addressOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "company_founded" =>
          company_foundedOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "company_industry" =>
          company_industryOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "company_linkedin_id" =>
          company_linkedin_idOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "company_linkedin_url" =>
          company_linkedin_urlOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "company_meta_description" =>
          company_meta_descriptionOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "company_meta_emails" =>
          company_meta_emailsOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "company_meta_keywords" =>
          company_meta_keywordsOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "company_meta_phones" =>
          company_meta_phonesOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "company_meta_title" =>
          company_meta_titleOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "company_size" =>
          company_sizeOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "company_type" =>
          company_typeOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "email_format" =>
          email_formatOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "facebook_id" =>
          facebook_idOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "facebook_url" =>
          facebook_urlOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "facebook_username" =>
          facebook_usernameOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "github_url" =>
          github_urlOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "github_username" =>
          github_usernameOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "id" =>
          source_data_id_2Opt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "inferred_salary" =>
          inferred_salaryOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "inferred_years_experience" =>
          inferred_years_experienceOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "job_company_facebook_url" =>
          job_company_facebook_urlOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "job_company_founded" =>
          job_company_foundedOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "job_company_id" =>
          job_company_idOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "job_company_industry" =>
          job_company_industryOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "job_company_linkedin_id" =>
          job_company_linkedin_idOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "job_company_linkedin_url" =>
          job_company_linkedin_urlOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "job_company_location_address_line_2" =>
          job_company_location_address_line_2Opt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "job_company_location_continent" =>
          job_company_location_continentOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "job_company_location_country" =>
          job_company_location_countryOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "job_company_location_geo" =>
          job_company_location_geoOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "job_company_location_locality" =>
          job_company_location_localityOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "job_company_location_metro" =>
          job_company_location_metroOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "job_company_location_name" =>
          job_company_location_nameOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "job_company_location_postal_code" =>
          job_company_location_postal_codeOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "job_company_location_region" =>
          job_company_location_regionOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "job_company_location_street_address" =>
          job_company_location_street_addressOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "job_company_name" =>
          job_company_nameOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "job_company_size" =>
          job_company_sizeOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "job_company_twitter_url" =>
          job_company_twitter_urlOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "job_company_website" =>
          job_company_websiteOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "job_last_updated" =>
          job_last_updatedOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "job_start_date" =>
          job_start_dateOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "job_summary" =>
          job_summaryOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "job_title_role" =>
          job_title_roleOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "job_title_sub_role" =>
          job_title_sub_roleOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "linkedin_connections" =>
          linkedin_connectionsOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "linkedin_id" =>
          linkedin_idOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "linkedin_username" =>
          linkedin_usernameOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "list_name" =>
          list_nameOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "location_address_line_2" =>
          location_address_line_2Opt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "location_continent" =>
          location_continentOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "location_country" =>
          location_countryOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "location_geo" =>
          location_geoOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "location_last_updated" =>
          location_last_updatedOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "location_locality" =>
          location_localityOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "location_metro" =>
          location_metroOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "location_name" =>
          location_nameOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "location_postal_code" =>
          location_postal_codeOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "location_region" =>
          location_regionOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "location_street_address" =>
          location_street_addressOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "middle_initial" =>
          middle_initialOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "mobile_phone" =>
          mobile_phoneOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "person_business_email" =>
          person_business_emailOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "person_company_name" =>
          person_company_nameOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "person_first_name" =>
          person_first_nameOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "person_headline" =>
          person_headlineOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "person_job_title" =>
          person_job_titleOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "person_last_name" =>
          person_last_nameOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "person_linkedin_id" =>
          person_linkedin_idOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "person_linkedin_url" =>
          person_linkedin_urlOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "person_location" =>
          person_locationOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "person_personal_email" =>
          person_personal_emailOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "person_phone" =>
          person_phoneOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "query" =>
          queryOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "summary" =>
          summaryOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "twitter_url" =>
          twitter_urlOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "twitter_username" =>
          twitter_usernameOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "version_status_contains_0" =>
          version_status_contains_0Opt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "version_status_contains_1" =>
          version_status_contains_1Opt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "version_status_contains_2" =>
          version_status_contains_2Opt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "version_status_current_version" =>
          version_status_current_versionOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "version_status_previous_version" =>
          version_status_previous_versionOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "version_status_status" =>
          version_status_statusOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case "work_email" =>
          work_emailOpt =
            if (csvRow.get(header._1).contains(""))
              None
            else {
              prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
              csvRow.get(header._1)
            }

        case patternForLinkedinInData(linkedinDataType, no, column) =>
          linkedinDataType match {

            case "education" =>
              val result = LinkedinEducationDataUtil.constructDataForLinkedinEducation(
                column = column,
                linkedinEducationDataMap = linkedinEducationDataMap,
                csvRow = csvRow,
                header = header,
                prospectDataConcat = prospectDataConcat,
                no = no
              )

              linkedinEducationDataMap = result._1
              prospectDataConcat = result._2

            case "experience" =>
              val result = LinkedinExperienceDataUtils.constructDataForLinkedinExperience(
                column = column,
                linkedinExperienceDataMap = linkedinExperienceDataMap,
                csvRow = csvRow,
                header = header,
                prospectDataConcat = prospectDataConcat,
                no = no
              )

              linkedinExperienceDataMap = result._1
              prospectDataConcat = result._2

            case "certifications" =>
              column match {
                case "end_date" =>
                  if (linkedinAdditionalDataMap.contains(no.toLong)) {
                    if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
                      prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
                      val linkedinExperienceDataUpdated = linkedinAdditionalDataMap(no.toInt).copy(
                        certification_end_date = csvRow.get(header._1)
                      )
                      linkedinAdditionalDataMap += (no.toLong -> linkedinExperienceDataUpdated)
                    }
                  } else {
                    if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
                      prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
                      linkedinAdditionalDataMap += (no.toLong -> linkedinAdditionalDataDefault.copy(
                        certification_end_date = csvRow.get(header._1)
                      ))
                    }
                  }

                case "name" =>
                  if (linkedinAdditionalDataMap.contains(no.toLong)) {
                    if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
                      prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
                      val linkedinExperienceDataUpdated = linkedinAdditionalDataMap(no.toInt).copy(
                        certification_name = csvRow.get(header._1)
                      )
                      linkedinAdditionalDataMap += (no.toLong -> linkedinExperienceDataUpdated)
                    }
                  } else {
                    if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
                      prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
                      linkedinAdditionalDataMap += (no.toLong -> linkedinAdditionalDataDefault.copy(
                        certification_name = csvRow.get(header._1)
                      ))
                    }
                  }

                case "organization" =>
                  if (linkedinAdditionalDataMap.contains(no.toLong)) {
                    if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
                      prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
                      val linkedinExperienceDataUpdated = linkedinAdditionalDataMap(no.toInt).copy(
                        certification_organization = csvRow.get(header._1)
                      )
                      linkedinAdditionalDataMap += (no.toLong -> linkedinExperienceDataUpdated)
                    }
                  } else {
                    if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
                      prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
                      linkedinAdditionalDataMap += (no.toLong -> linkedinAdditionalDataDefault.copy(
                        certification_organization = csvRow.get(header._1)
                      ))
                    }
                  }

                case "start_date" =>
                  if (linkedinAdditionalDataMap.contains(no.toLong)) {
                    if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
                      prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
                      val linkedinExperienceDataUpdated = linkedinAdditionalDataMap(no.toInt).copy(
                        certification_start_date = csvRow.get(header._1)
                      )
                      linkedinAdditionalDataMap += (no.toLong -> linkedinExperienceDataUpdated)
                    }
                  } else {
                    if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
                      prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
                      linkedinAdditionalDataMap += (no.toLong -> linkedinAdditionalDataDefault.copy(
                        certification_start_date = csvRow.get(header._1)
                      ))
                    }
                  }

                case _ =>
                  println(s"11111 ${header._2}")
                  None
              }
            case "languages" =>
              column match {
                case "name" =>
                  if (linkedinAdditionalDataMap.contains(no.toLong)) {
                    if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
                      prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
                      val linkedinExperienceDataUpdated = linkedinAdditionalDataMap(no.toInt).copy(
                        language_name = csvRow.get(header._1)
                      )
                      linkedinAdditionalDataMap += (no.toLong -> linkedinExperienceDataUpdated)
                    }
                  } else {
                    if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
                      prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
                      linkedinAdditionalDataMap += (no.toLong -> linkedinAdditionalDataDefault.copy(
                        language_name = csvRow.get(header._1)
                      ))
                    }
                  }
                case "proficiency" =>
                  if (linkedinAdditionalDataMap.contains(no.toLong)) {
                    if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
                      prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
                      val linkedinExperienceDataUpdated = linkedinAdditionalDataMap(no.toInt).copy(
                        language_proficiency = csvRow.get(header._1)
                      )
                      linkedinAdditionalDataMap += (no.toLong -> linkedinExperienceDataUpdated)
                    }
                  } else {
                    if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
                      prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
                      linkedinAdditionalDataMap += (no.toLong -> linkedinAdditionalDataDefault.copy(
                        language_proficiency = csvRow.get(header._1)
                      ))
                    }
                  }

                case _ =>
                  println(s"11111 ${header._2}")
                  None

              }
            case "profiles" =>
              column match {
                case "id" =>
                  if (linkedinAdditionalDataMap.contains(no.toLong)) {
                    if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
                      prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
                      val linkedinExperienceDataUpdated = linkedinAdditionalDataMap(no.toInt).copy(
                        profile_id = csvRow.get(header._1)
                      )
                      linkedinAdditionalDataMap += (no.toLong -> linkedinExperienceDataUpdated)
                    }
                  } else {
                    if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
                      prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
                      linkedinAdditionalDataMap += (no.toLong -> linkedinAdditionalDataDefault.copy(
                        profile_id = csvRow.get(header._1)
                      ))
                    }
                  }
                case "network" =>
                  if (linkedinAdditionalDataMap.contains(no.toLong)) {
                    if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
                      prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
                      val linkedinExperienceDataUpdated = linkedinAdditionalDataMap(no.toInt).copy(
                        profiles_network = csvRow.get(header._1)
                      )
                      linkedinAdditionalDataMap += (no.toLong -> linkedinExperienceDataUpdated)
                    }
                  } else {
                    if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
                      prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
                      linkedinAdditionalDataMap += (no.toLong -> linkedinAdditionalDataDefault.copy(
                        profiles_network = csvRow.get(header._1)
                      ))
                    }
                  }
                case "url" =>
                  if (linkedinAdditionalDataMap.contains(no.toLong)) {
                    if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
                      prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
                      val linkedinExperienceDataUpdated = linkedinAdditionalDataMap(no.toInt).copy(
                        profile_url = csvRow.get(header._1)
                      )
                      linkedinAdditionalDataMap += (no.toLong -> linkedinExperienceDataUpdated)
                    }
                  } else {
                    if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
                      prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
                      linkedinAdditionalDataMap += (no.toLong -> linkedinAdditionalDataDefault.copy(
                        profile_url = csvRow.get(header._1)
                      ))
                    }
                  }
                case "username" =>
                  if (linkedinAdditionalDataMap.contains(no.toLong)) {
                    if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
                      prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
                      val linkedinExperienceDataUpdated = linkedinAdditionalDataMap(no.toInt).copy(
                        profile_username = csvRow.get(header._1)
                      )
                      linkedinAdditionalDataMap += (no.toLong -> linkedinExperienceDataUpdated)
                    }
                  } else {
                    if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
                      prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
                      linkedinAdditionalDataMap += (no.toLong -> linkedinAdditionalDataDefault.copy(
                        profile_username = csvRow.get(header._1)
                      ))
                    }
                  }

                case _ =>
                  println(s"11111 ${header._2}")
                  None
              }

            case "street_addresses" =>
              column match {
                case "address_line_2" =>
                  if (linkedinAdditionalDataMap.contains(no.toLong)) {
                    if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
                      prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
                      val linkedinExperienceDataUpdated = linkedinAdditionalDataMap(no.toInt).copy(
                        street_addresses_address_line_2 = csvRow.get(header._1)
                      )
                      linkedinAdditionalDataMap += (no.toLong -> linkedinExperienceDataUpdated)
                    }
                  } else {
                    if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
                      prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
                      linkedinAdditionalDataMap += (no.toLong -> linkedinAdditionalDataDefault.copy(
                        street_addresses_address_line_2 = csvRow.get(header._1)
                      ))
                    }
                  }

                case "continent" =>
                  if (linkedinAdditionalDataMap.contains(no.toLong)) {
                    if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
                      prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
                      val linkedinExperienceDataUpdated = linkedinAdditionalDataMap(no.toInt).copy(
                        street_addresses_continent = csvRow.get(header._1)
                      )
                      linkedinAdditionalDataMap += (no.toLong -> linkedinExperienceDataUpdated)
                    }
                  } else {
                    if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
                      prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
                      linkedinAdditionalDataMap += (no.toLong -> linkedinAdditionalDataDefault.copy(
                        street_addresses_continent = csvRow.get(header._1)
                      ))
                    }
                  }

                case "country" =>
                  if (linkedinAdditionalDataMap.contains(no.toLong)) {
                    if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
                      prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
                      val linkedinExperienceDataUpdated = linkedinAdditionalDataMap(no.toInt).copy(
                        street_addresses_country = csvRow.get(header._1)
                      )
                      linkedinAdditionalDataMap += (no.toLong -> linkedinExperienceDataUpdated)
                    }
                  } else {
                    if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
                      prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
                      linkedinAdditionalDataMap += (no.toLong -> linkedinAdditionalDataDefault.copy(
                        street_addresses_country = csvRow.get(header._1)
                      ))
                    }
                  }

                case "geo" =>
                  if (linkedinAdditionalDataMap.contains(no.toLong)) {
                    if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
                      prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
                      val linkedinExperienceDataUpdated = linkedinAdditionalDataMap(no.toInt).copy(
                        street_addresses_geo = csvRow.get(header._1)
                      )
                      linkedinAdditionalDataMap += (no.toLong -> linkedinExperienceDataUpdated)
                    }
                  } else {
                    if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
                      prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
                      linkedinAdditionalDataMap += (no.toLong -> linkedinAdditionalDataDefault.copy(
                        street_addresses_geo = csvRow.get(header._1)
                      ))
                    }
                  }

                case "locality" =>
                  if (linkedinAdditionalDataMap.contains(no.toLong)) {
                    if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
                      prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
                      val linkedinExperienceDataUpdated = linkedinAdditionalDataMap(no.toInt).copy(
                        street_addresses_locality = csvRow.get(header._1)
                      )
                      linkedinAdditionalDataMap += (no.toLong -> linkedinExperienceDataUpdated)
                    }
                  } else {
                    if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
                      prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
                      linkedinAdditionalDataMap += (no.toLong -> linkedinAdditionalDataDefault.copy(
                        street_addresses_locality = csvRow.get(header._1)
                      ))
                    }
                  }

                case "metro" =>
                  if (linkedinAdditionalDataMap.contains(no.toLong)) {
                    if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
                      prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
                      val linkedinExperienceDataUpdated = linkedinAdditionalDataMap(no.toInt).copy(
                        street_addresses_metro = csvRow.get(header._1)
                      )
                      linkedinAdditionalDataMap += (no.toLong -> linkedinExperienceDataUpdated)
                    }
                  } else {
                    if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
                      prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
                      linkedinAdditionalDataMap += (no.toLong -> linkedinAdditionalDataDefault.copy(
                        street_addresses_metro = csvRow.get(header._1)
                      ))
                    }
                  }

                case "name" =>
                  if (linkedinAdditionalDataMap.contains(no.toLong)) {
                    if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
                      prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
                      val linkedinExperienceDataUpdated = linkedinAdditionalDataMap(no.toInt).copy(
                        street_addresses_name = csvRow.get(header._1)
                      )
                      linkedinAdditionalDataMap += (no.toLong -> linkedinExperienceDataUpdated)
                    }
                  } else {
                    if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
                      prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
                      linkedinAdditionalDataMap += (no.toLong -> linkedinAdditionalDataDefault.copy(
                        street_addresses_name = csvRow.get(header._1)
                      ))
                    }
                  }

                case "postal_code" =>
                  if (linkedinAdditionalDataMap.contains(no.toLong)) {
                    if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
                      prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
                      val linkedinExperienceDataUpdated = linkedinAdditionalDataMap(no.toInt).copy(
                        street_addresses_postal_code = csvRow.get(header._1)
                      )
                      linkedinAdditionalDataMap += (no.toLong -> linkedinExperienceDataUpdated)
                    }
                  } else {
                    if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
                      prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
                      linkedinAdditionalDataMap += (no.toLong -> linkedinAdditionalDataDefault.copy(
                        street_addresses_postal_code = csvRow.get(header._1)
                      ))
                    }
                  }

                case "region" =>
                  if (linkedinAdditionalDataMap.contains(no.toLong)) {
                    if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
                      prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
                      val linkedinExperienceDataUpdated = linkedinAdditionalDataMap(no.toInt).copy(
                        street_addresses_region = csvRow.get(header._1)
                      )
                      linkedinAdditionalDataMap += (no.toLong -> linkedinExperienceDataUpdated)
                    }
                  } else {
                    if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
                      prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
                      linkedinAdditionalDataMap += (no.toLong -> linkedinAdditionalDataDefault.copy(
                        street_addresses_region = csvRow.get(header._1)
                      ))
                    }
                  }

                case "street_address" =>
                  if (linkedinAdditionalDataMap.contains(no.toLong)) {
                    if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
                      prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
                      val linkedinExperienceDataUpdated = linkedinAdditionalDataMap(no.toInt).copy(
                        street_addresses_street_address = csvRow.get(header._1)
                      )
                      linkedinAdditionalDataMap += (no.toLong -> linkedinExperienceDataUpdated)
                    }
                  } else {
                    if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
                      prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
                      linkedinAdditionalDataMap += (no.toLong -> linkedinAdditionalDataDefault.copy(
                        street_addresses_street_address = csvRow.get(header._1)
                      ))
                    }
                  }

                case _ =>
                  println(s"11111 ${header._2}")
                  None
              }

            case "emails" =>
              column match {
                case "address" =>
                  if (linkedinAdditionalDataMap.contains(no.toLong)) {
                    if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
                      prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
                      val linkedinExperienceDataUpdated = linkedinAdditionalDataMap(no.toInt).copy(
                        email_address = csvRow.get(header._1)
                      )
                      linkedinAdditionalDataMap += (no.toLong -> linkedinExperienceDataUpdated)
                    }
                  } else {
                    if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
                      prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
                      linkedinAdditionalDataMap += (no.toLong -> linkedinAdditionalDataDefault.copy(
                        email_address = csvRow.get(header._1)
                      ))
                    }
                  }

                case "type" =>
                  if (linkedinAdditionalDataMap.contains(no.toLong)) {
                    if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
                      prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
                      val linkedinExperienceDataUpdated = linkedinAdditionalDataMap(no.toInt).copy(
                        email_address = csvRow.get(header._1)
                      )
                      linkedinAdditionalDataMap += (no.toLong -> linkedinExperienceDataUpdated)
                    }
                  } else {
                    if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
                      prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
                      linkedinAdditionalDataMap += (no.toLong -> linkedinAdditionalDataDefault.copy(
                        email_address = csvRow.get(header._1)
                      ))
                    }
                  }

                case _ =>
                  println(s"11111 ${header._2}")
                  None
              }

            case _ =>
              println(s"11111 ${header._2}")
              None
          }

        case patternForLinkedinAdditionalData(linkedinDataType, no) =>
          linkedinDataType match {
            case "interests" =>
              if (linkedinAdditionalDataMap.contains(no.toLong)) {
                if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
                  prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
                  val linkedinExperienceDataUpdated = linkedinAdditionalDataMap(no.toInt).copy(
                    interest = csvRow.get(header._1)
                  )
                  linkedinAdditionalDataMap += (no.toLong -> linkedinExperienceDataUpdated)
                }
              } else {
                if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
                  prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
                  linkedinAdditionalDataMap += (no.toLong -> linkedinAdditionalDataDefault.copy(
                    interest = csvRow.get(header._1)
                  ))
                }
              }
            case "job_title_levels" =>
              if (linkedinAdditionalDataMap.contains(no.toLong)) {
                if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
                  prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
                  val linkedinExperienceDataUpdated = linkedinAdditionalDataMap(no.toInt).copy(
                    job_title_level = csvRow.get(header._1)
                  )
                  linkedinAdditionalDataMap += (no.toLong -> linkedinExperienceDataUpdated)
                }
              } else {
                if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
                  prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
                  linkedinAdditionalDataMap += (no.toLong -> linkedinAdditionalDataDefault.copy(
                    job_title_level = csvRow.get(header._1)
                  ))
                }
              }
            case "location_names" =>
              if (linkedinAdditionalDataMap.contains(no.toLong)) {
                if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
                  prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
                  val linkedinExperienceDataUpdated = linkedinAdditionalDataMap(no.toInt).copy(
                    location_name = csvRow.get(header._1)
                  )
                  linkedinAdditionalDataMap += (no.toLong -> linkedinExperienceDataUpdated)
                }
              } else {
                if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
                  prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
                  linkedinAdditionalDataMap += (no.toLong -> linkedinAdditionalDataDefault.copy(
                    location_name = csvRow.get(header._1)
                  ))
                }
              }
            case "phone_numbers" =>
              if (linkedinAdditionalDataMap.contains(no.toLong)) {
                if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
                  prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
                  val linkedinExperienceDataUpdated = linkedinAdditionalDataMap(no.toInt).copy(
                    phone_number = csvRow.get(header._1)
                  )
                  linkedinAdditionalDataMap += (no.toLong -> linkedinExperienceDataUpdated)
                }
              } else {
                if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
                  prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
                  linkedinAdditionalDataMap += (no.toLong -> linkedinAdditionalDataDefault.copy(
                    phone_number = csvRow.get(header._1)
                  ))
                }
              }
            case "regions" =>
              if (linkedinAdditionalDataMap.contains(no.toLong)) {
                if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
                  prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
                  val linkedinExperienceDataUpdated = linkedinAdditionalDataMap(no.toInt).copy(
                    region = csvRow.get(header._1)
                  )
                  linkedinAdditionalDataMap += (no.toLong -> linkedinExperienceDataUpdated)
                }
              } else {
                if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
                  prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
                  linkedinAdditionalDataMap += (no.toLong -> linkedinAdditionalDataDefault.copy(
                    region = csvRow.get(header._1)
                  ))
                }
              }
            case "skills" =>
              if (linkedinAdditionalDataMap.contains(no.toLong)) {
                if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
                  prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
                  val linkedinExperienceDataUpdated = linkedinAdditionalDataMap(no.toInt).copy(
                    skill = csvRow.get(header._1)
                  )
                  linkedinAdditionalDataMap += (no.toLong -> linkedinExperienceDataUpdated)
                }
              } else {
                if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
                  prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
                  linkedinAdditionalDataMap += (no.toLong -> linkedinAdditionalDataDefault.copy(
                    skill = csvRow.get(header._1)
                  ))
                }
              }

            case "countries" =>
              if (linkedinAdditionalDataMap.contains(no.toLong)) {
                if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
                  prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
                  val linkedinExperienceDataUpdated = linkedinAdditionalDataMap(no.toInt).copy(
                    country = csvRow.get(header._1)
                  )
                  linkedinAdditionalDataMap += (no.toLong -> linkedinExperienceDataUpdated)
                }
              } else {
                if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
                  prospectDataConcat = s"${prospectDataConcat}_${csvRow.get(header._1)}"
                  linkedinAdditionalDataMap += (no.toLong -> linkedinAdditionalDataDefault.copy(
                    country = csvRow.get(header._1)
                  ))
                }
              }

            case _ =>
              println(s"11111 ${header._2}")
              None
          }

        case _ =>
          println(s"11111 ${header._2}")
          None

      }
    })

    val prospectDataHash = Hashing.sha256().hashString(prospectDataConcat, StandardCharsets.UTF_8).toString

    val linkedinEducationData = if (linkedinEducationDataMap.nonEmpty) {
      linkedinEducationDataMap = linkedinEducationDataMap.map(p => (p._1, p._2.copy(
        education_data_hash = Some(prospectDataHash)
      )))
      Some(linkedinEducationDataMap)
    } else {
      None
    }

    val linkedinExperienceData = if (linkedinExperienceDataMap.nonEmpty) {
      linkedinExperienceDataMap = linkedinExperienceDataMap.map(p => (p._1, p._2.copy(
        experience_data_hash = Some(prospectDataHash)
      )))
      Some(linkedinExperienceDataMap)
    } else {
      None
    }

    val linkedinAdditionalData = if (linkedinAdditionalDataMap.nonEmpty) {
      linkedinAdditionalDataMap = linkedinAdditionalDataMap.map(p => (p._1, p._2.copy(
        additional_data_hash = Some(prospectDataHash)
      )))
      Some(linkedinAdditionalDataMap)
    } else {
      None
    }

    CsvUploadColLinkedin(
      anzsic_2006_code = anzsic_2006_codeOpt,
      anzsic_2006_description = anzsic_2006_descriptionOpt,
      address = addressOpt,
      address_1 = address_1Opt,
      address_2 = address_2Opt,
      address_line_1 = address_line_1Opt,
      address_line_2 = address_line_2Opt,
      address_line_3 = address_line_3Opt,
      app_using = app_usingOpt,
      assets__usd = assets__usd_Opt,
      birth_date = birth_dateOpt,
      birth_year = birth_yearOpt,
      business_description = business_descriptionOpt,
      city = cityOpt,
      company = companyOpt,
      company_city = company_cityOpt,
      company_country = company_countryOpt,
      company_name = company_nameOpt,
      company_state = company_stateOpt,
      company_street_address = company_street_addressOpt,
      company_zip_postal_code = company_zip_postal_codeOpt,
      company_division_name = company_division_nameOpt,
      company_domain_name = company_domain_nameOpt,
      company_phone_number = company_phone_numberOpt,
      contact = contactOpt,
      contact_level = contact_levelOpt,
      contact_name = contact_nameOpt,
      contact_title = contact_titleOpt,
      countries = countriesOpt,
      country = countryOpt,
      country_region = country_regionOpt,
      county = countyOpt,
      dnb_hoovers_industry = dnb_hoovers_industryOpt,
      direct_marketing_status = direct_marketing_statusOpt,
      direct_phone = direct_phoneOpt,
      direct_phone_number = direct_phone_numberOpt,
      email = emailOpt,
      email_domain = email_domainOpt,
      email_address = email_addressOpt,
      emails = emailsOpt,
      employee_size = employee_sizeOpt,
      employees = employeesOpt,
      employees__all_sites = employees__all_sites_Opt,
      employees__single_site = employees__single_site_Opt,
      employees_range = employees_rangeOpt,
      encrypted_email_address = encrypted_email_addressOpt,
      entity_type = entity_typeOpt,
      fax = faxOpt,
      first_name = first_nameOpt,
      full_name = full_nameOpt,
      gender = genderOpt,
      highest_level_job_function = highest_level_job_functionOpt,
      isic_rev_4_code = isic_rev_4_codeOpt,
      isic_rev_4_description = isic_rev_4_descriptionOpt,
      industry = industryOpt,
      industry_type = industry_typeOpt,
      industry_hierarchical_category = industry_hierarchical_categoryOpt,
      industry_label = industry_labelOpt,
      job_function = job_functionOpt,
      job_title = job_titleOpt,
      job_title_hierarchy_level = job_title_hierarchy_levelOpt,
      last_name = last_nameOpt,
      liabilities__usd = liabilities__usd_Opt,
      linkedin_url = linkedin_urlOpt,
      linkedin_links = linkedin_linksOpt,
      locality = localityOpt,
      location = locationOpt,
      management_level = management_levelOpt,
      middle_name = middle_nameOpt,
      nace_rev_2_code = nace_rev_2_codeOpt,
      nace_rev_2_description = nace_rev_2_descriptionOpt,
      naics = naicsOpt,
      naics_2012_code = naics_2012_codeOpt,
      naics_2012_description = naics_2012_descriptionOpt,
      naics1 = naics1Opt,
      naics2 = naics2Opt,
      ownership_type = ownership_typeOpt,
      parent_company = parent_companyOpt,
      parent_country_region = parent_country_regionOpt,
      person_city = person_cityOpt,
      person_pro_url = person_pro_urlOpt,
      person_state = person_stateOpt,
      person_zip = person_zipOpt,
      phone = phoneOpt,
      phone_number = phone_numberOpt,
      postal_code = postal_codeOpt,
      pre_tax_profit__usd = pre_tax_profit__usd_Opt,
      query_name = query_nameOpt,
      revenue = revenueOpt,
      revenue__usd = revenue__usd_Opt,
      revenue__in_u = revenue__in_u_Opt,
      revenue__in_000s = revenue__in_000s_Opt,
      revenue_range = revenue_rangeOpt,
      sic = sicOpt,
      sic_code = sic_codeOpt,
      sic1 = sic1Opt,
      sic2 = sic2Opt,
      sales = salesOpt,
      salutation = salutationOpt,
      secondary_industry_hierarchical_category = secondary_industry_hierarchical_categoryOpt,
      secondary_industry_label = secondary_industry_labelOpt,
      source_count = source_countOpt,
      state = stateOpt,
      state_or_province = state_or_provinceOpt,
      suffix = suffixOpt,
      ticker = tickerOpt,
      title = titleOpt,
      titlecode = titlecodeOpt,
      total_employees = total_employeesOpt,
      tradestyle = tradestyleOpt,
      uk_sic_2007_code = uk_sic_2007_codeOpt,
      uk_sic_2007_description = uk_sic_2007_descriptionOpt,
      url = urlOpt,
      us_sic_1987_code = us_sic_1987_codeOpt,
      us_sic_1987_description = us_sic_1987_descriptionOpt,
      ultimate_parent_company = ultimate_parent_companyOpt,
      ultimate_parent_country_region = ultimate_parent_country_regionOpt,
      website = websiteOpt,
      zip = zipOpt,
      zoom_individual_id = zoom_individual_idOpt,
      zoom_company_id = zoom_company_idOpt,
      zoominfo_industry = zoominfo_industryOpt,
      source_data_id_1 = source_data_id_1Opt,
      company_address = company_addressOpt,
      company_founded = company_foundedOpt,
      company_industry = company_industryOpt,
      company_linkedin_id = company_linkedin_idOpt,
      company_linkedin_url = company_linkedin_urlOpt,
      company_meta_description = company_meta_descriptionOpt,
      company_meta_emails = company_meta_emailsOpt,
      company_meta_keywords = company_meta_keywordsOpt,
      company_meta_phones = company_meta_phonesOpt,
      company_meta_title = company_meta_titleOpt,
      company_size = company_sizeOpt,
      company_type = company_typeOpt,
      email_format = email_formatOpt,
      facebook_id = facebook_idOpt,
      facebook_url = facebook_urlOpt,
      facebook_username = facebook_usernameOpt,
      github_url = github_urlOpt,
      github_username = github_usernameOpt,
      source_data_id_2 = source_data_id_2Opt,
      inferred_salary = inferred_salaryOpt,
      inferred_years_experience = inferred_years_experienceOpt,
      job_company_facebook_url = job_company_facebook_urlOpt,
      job_company_founded = job_company_foundedOpt,
      job_company_id = job_company_idOpt,
      job_company_industry = job_company_industryOpt,
      job_company_linkedin_id = job_company_linkedin_idOpt,
      job_company_linkedin_url = job_company_linkedin_urlOpt,
      job_company_location_address_line_2 = job_company_location_address_line_2Opt,
      job_company_location_continent = job_company_location_continentOpt,
      job_company_location_country = job_company_location_countryOpt,
      job_company_location_geo = job_company_location_geoOpt,
      job_company_location_locality = job_company_location_localityOpt,
      job_company_location_metro = job_company_location_metroOpt,
      job_company_location_name = job_company_location_nameOpt,
      job_company_location_postal_code = job_company_location_postal_codeOpt,
      job_company_location_region = job_company_location_regionOpt,
      job_company_location_street_address = job_company_location_street_addressOpt,
      job_company_name = job_company_nameOpt,
      job_company_size = job_company_sizeOpt,
      job_company_twitter_url = job_company_twitter_urlOpt,
      job_company_website = job_company_websiteOpt,
      job_last_updated = job_last_updatedOpt,
      job_start_date = job_start_dateOpt,
      job_summary = job_summaryOpt,
      job_title_role = job_title_roleOpt,
      job_title_sub_role = job_title_sub_roleOpt,
      linkedin_connections = linkedin_connectionsOpt,
      linkedin_id = linkedin_idOpt,
      linkedin_username = linkedin_usernameOpt,
      list_name = list_nameOpt,
      location_address_line_2 = location_address_line_2Opt,
      location_continent = location_continentOpt,
      location_country = location_countryOpt,
      location_geo = location_geoOpt,
      location_last_updated = location_last_updatedOpt,
      location_locality = location_localityOpt,
      location_metro = location_metroOpt,
      location_name = location_nameOpt,
      location_postal_code = location_postal_codeOpt,
      location_region = location_regionOpt,
      location_street_address = location_street_addressOpt,
      middle_initial = middle_initialOpt,
      mobile_phone = mobile_phoneOpt,
      person_business_email = person_business_emailOpt,
      person_company_name = person_company_nameOpt,
      person_first_name = person_first_nameOpt,
      person_headline = person_headlineOpt,
      person_job_title = person_job_titleOpt,
      person_last_name = person_last_nameOpt,
      person_linkedin_id = person_linkedin_idOpt,
      person_linkedin_url = person_linkedin_urlOpt,
      person_location = person_locationOpt,
      person_personal_email = person_personal_emailOpt,
      person_phone = person_phoneOpt,
      query = queryOpt,
      summary = summaryOpt,
      twitter_url = twitter_urlOpt,
      twitter_username = twitter_usernameOpt,
      version_status_contains_0 = version_status_contains_0Opt,
      version_status_contains_1 = version_status_contains_1Opt,
      version_status_contains_2 = version_status_contains_2Opt,
      version_status_current_version = version_status_current_versionOpt,
      version_status_previous_version = version_status_previous_versionOpt,
      version_status_status = version_status_statusOpt,
      work_email = work_emailOpt,
      prospect_data_hash = prospectDataHash,
      education_data_list = linkedinEducationData,
      experience_data_list = linkedinExperienceData,
      additional_data_list = linkedinAdditionalData,
    )
  }

  def parseFileUsingExecutable(
                                localFilePath: String
                              )(implicit ec: ExecutionContext): Try[String] = Try {

    val parsing = Future {
      s"$csvParserExecutablePath $localFilePath" !!
    }

    Await.result(
      parsing,
      atMost = 600000.millis
    )

  }


  def parseCSVFile(

                    localFilePath: String,
                    srlogger: SRLogger

                  )(implicit ec: ExecutionContext): Try[SRCustomCSVParserResult] = {

    val logger = srlogger.appendLogRequestId(s"CustomCSVParser.parseCSVFile")

    logger.info("started parsing")
    // val executablePath = playEnv.getFile("sr_csv_parser/csv2.exe").getAbsolutePath

    for {

      parserRawOutput <- parseFileUsingExecutable(
        localFilePath = localFilePath
      )

      parserResult <- {
        Json.parse(parserRawOutput).validate[SRCustomCSVParserResult] match {

          case JsError(errors) =>
            logger.fatal(s"Json.validate JSError: $errors ")

            Failure(
              new Exception(s"CSVParser ParseError: errors: $errors")
            )

          case JsSuccess(value, _) =>

            logger.info(s"parse success: value.errors: ${value.errors}")
            logger.info(s"parse success: value.parsed_data.length: ${value.parsed_data.length}")
            logger.info(s"parse success: value.header: ${value.header}")
            logger.info(s"parse success: value.total_errors: ${value.total_errors}")
            logger.info(s"parse success: value.total_records: ${value.total_records}")
            logger.info(s"parse success: error %: ${SRCustomCSVParserResult.parseErrorPercent(result = value)}")
            logger.info(s"parse success: value.successfully_parsed: ${value.successfully_parsed}")
            logger.info(s"parse success: value.expected_fields: ${value.expected_fields}")

            // if more than 2% of rows had errors while parsing, send it as a parse-error exception for the whole file
            // checking total_errors absolute value also, because many smaller files are falling through with just the
            // newline error which is a irrelevant one
            val errorPercent = SRCustomCSVParserResult.parseErrorPercent(
              result = value
            )
            if (errorPercent > 2 && value.total_errors > 2) {

              val errorMsg = SRCustomCSVParserResult.errorMessageForUser(
                result = value
              )

              if (errorMsg.isEmpty) {
                logger.fatal(s"SRCSVParserResult.errorMessageForUser is empty (SHOULD NEVER HAPPEN): result errors: ${value.errors} :: total_errors: ${value.total_errors}")
              }

              Failure(new Exception(
                errorMsg
                  .getOrElse(s"Errors while parsing error: ${value.total_errors}")
              ))

            } else {

              Success(value)

            }

        }
      }

    } yield {
      parserResult
    }
  }

  def getRowsUsingParserCustomParser(
                                      fileAbsolutePath: String,
                                      logger: SRLogger
                                    )(implicit ec: ExecutionContext): Try[SRCsvParserRawResult] = {

    for {
      customParserRes <- parseCSVFile(
        localFilePath = fileAbsolutePath,
        srlogger = logger
      )
      _ <- Try {
        println(s"customParserRes ${customParserRes}")
      }

      rowMapFromCSV <- LeadFinderUploadService.createMapFromCSVHeaderAndRows(
        csvHeaderRow = customParserRes.header,
        dataRows = customParserRes.parsed_data,
        logger = logger
      )

    } yield {

      val parserContext = customParserRes
        .copy(
          errors = customParserRes.errors.map(_.take(10)),
          parsed_data = Seq(Seq())
        )

      val parserContextJson = Json.toJson(
        parserContext
      )

      SRCsvParserRawResult(
        parserName = parserName,
        rowMapFromCSV = rowMapFromCSV,
        parserContext = Some(parserContextJson)
      )


    }


  }

  def getRowsUsingParserWithFallback(
                                      fileAbsolutePath: String,
                                      logger: SRLogger
                                    )(implicit ec: ExecutionContext): Try[SRCsvParserRawResult] = {

    getRowsUsingParserCustomParser(
      fileAbsolutePath = fileAbsolutePath,
      logger = logger
    ) match {
      case Failure(exception) =>
        logger.error("getRowsUsingParserWithFallback fallback: failed parsing via customCSVParser, trying univocityParser", err = exception)

        univocityParser.getRowsUsingParser(
          fileAbsolutePath = fileAbsolutePath,
          logger = logger
        )

      case Success(value) =>
        Success(value)
    }

  }

  def getFileAbsolutePathByDownloadingIfNecessary(
                                                   fileObj: CSVParseFileType
                                                 ): Try[String] = {

    fileObj match {

      case f: CSVParseFileType.FileInstance =>

        Try {
          f.file.getAbsolutePath
        }


      case f: CSVParseFileType.FileURL =>

        for {
          localFileName <- Try {
            val p = f.fileUrl.getPath

            p.split("/").last

          }

          fileAbsolutePath <- FileDownloader.downloadFromUrl(
            url = f.fileUrl,
            localFilename = localFileName
          )

        } yield {
          fileAbsolutePath
        }

    }

  }

  def deleteFileFromLocal(
                           fileAbsolutePath: String
                         ): Try[Boolean] = Try {

    new File(fileAbsolutePath).delete()

  }

  def parseCSVForLeadFinder(
                             fileObj: CSVParseFileType,
                             deleteLocalFileAfterParsing: Boolean = true,
                             logger: SRLogger
                           )(implicit ec: ExecutionContext): Try[SRCsvParserRawResult] = Try {

    var fileAbsPath: Option[String] = None

    try {

      val absPath = if (!AppConfig.isLocalDevDomain) {
        getFileAbsolutePathByDownloadingIfNecessary(
          fileObj = fileObj
        ).get
      } else {
        fileObj match {
          case CSVParseFileType.FileInstance(file) =>
            file.getPath
          case CSVParseFileType.FileURL(fileUrl) =>
            fileUrl.getPath
        }
      }

      fileAbsPath = Some(absPath)

      getRowsUsingParserWithFallback(
        fileAbsolutePath = absPath,
        logger = logger
      )

    } catch {

      case e: Throwable => {

        logger.fatal("error while parsing file", err = e)

        Failure(e)
      }

    } finally {

      if (deleteLocalFileAfterParsing && fileAbsPath.isDefined) {

        // delete the local file after parsing

        deleteFileFromLocal(
          fileAbsolutePath = fileAbsPath.get
        ) match {
          case Failure(e) =>

            logger.fatal(s"error while deleting file with path: ${fileAbsPath}", err = e)


          case Success(_) =>

            logger.info("file deleted successfully after parsing")

        }
      }
    }

  }.flatten

}


object LeadFinderUploadService {
  def createMapFromCSVHeaderAndRows(

                                     csvHeaderRow: Seq[String],
                                     dataRows: Seq[Seq[String]],
                                     logger: SRLogger

                                   ): Try[Seq[Map[String, String]]] = Try {

    val headerRowTrimmed = csvHeaderRow.map(_.trim)
    val totalColumns = headerRowTrimmed.size

    dataRows
      .zipWithIndex
      .map { case (row, rowIndex) => {

        if (row.size < totalColumns) {
          logger.error(s"Bad Row [not enough columns]: rowIndex: $rowIndex")
        }


        headerRowTrimmed
          .zipWithIndex
          .collect {
            case (headerName, index) if Option(headerName).exists(_.trim.nonEmpty) =>
              (headerName.trim, index)
          }
          .groupBy(_._1) // Group by header names
          .view
          .mapValues { indices =>
            indices.collectFirst { case (_, index) if Option(row(index)).exists(_.trim.nonEmpty) =>
              row(index).trim
            }
          }
          .collect {
            case (headerName, Some(value)) => headerName -> value
          }
          .toMap


      }
      }
  }
}
*/
