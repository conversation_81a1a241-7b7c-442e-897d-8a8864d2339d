package utils.testapp.csv_upload

case class CsvUploadCol(
  address: Option[String] = None,
  address_1: Option[String] = None,
  address_2: Option[String] = None,
  address_line_1: Option[String] = None,
  address_line_2: Option[String] = None,
  address_line_3: Option[String] = None,
  anzsic_2006_code: Option[String] = None,
  anzsic_2006_description: Option[String] = None,
  app_using: Option[String] = None,
  assets__usd: Option[String] = None,
  birth_date: Option[String] = None,
  birth_year: Option[String] = None,
  business_description: Option[String] = None,
  city: Option[String] = None,
  company: Option[String] = None,
  company_address: Option[String] = None,
  company_city: Option[String] = None,
  company_country: Option[String] = None,
  company_division_name: Option[String] = None,
  company_domain_name: Option[String] = None,
  company_founded: Option[String] = None,
  company_industry: Option[String] = None,
  company_linkedin_id: Option[String] = None,
  company_linkedin_url: Option[String] = None,
  company_meta_description: Option[String] = None,
  company_meta_emails: Option[String] = None,
  company_meta_keywords: Option[String] = None,
  company_meta_phones: Option[String] = None,
  company_meta_title: Option[String] = None,
  company_name: Option[String] = None,
  company_phone_number: Option[String] = None,
  company_size: Option[String] = None,
  company_state: Option[String] = None,
  company_street_address: Option[String] = None,
  company_type: Option[String] = None,
  company_zip_postal_code: Option[String] = None,
  contact: Option[String] = None,
  contact_level: Option[String] = None,
  contact_name: Option[String] = None,
  contact_title: Option[String] = None,
  countries: Option[String] = None,
  country: Option[String] = None,
  country_region: Option[String] = None,
  county: Option[String] = None,
  dnb_hoovers_industry: Option[String] = None,
  direct_marketing_status: Option[String] = None,
  direct_phone: Option[String] = None,
  direct_phone_number: Option[String] = None,
  email: Option[String] = None,
  email_address: Option[String] = None,
  email_domain: Option[String] = None,
  email_format: Option[String] = None,
  emails: Option[String] = None,
  employee_size: Option[String] = None,
  employees: Option[String] = None,
  employees__all_sites: Option[String] = None,
  employees__single_site: Option[String] = None,
  employees_range: Option[String] = None,
  encrypted_email_address: Option[String] = None,
  entity_type: Option[String] = None,
  fax: Option[String] = None,
  first_name: Option[String] = None,
  full_name: Option[String] = None,
  gender: Option[String] = None,
  highest_level_job_function: Option[String] = None,
  industry: Option[String] = None,
  industry_hierarchical_category: Option[String] = None,
  industry_label: Option[String] = None,
  industry_type: Option[String] = None,
  isic_rev_4_code: Option[String] = None,
  isic_rev_4_description: Option[String] = None,
  job_function: Option[String] = None,
  job_title: Option[String] = None,
  job_title_hierarchy_level: Option[String] = None,
  last_name: Option[String] = None,
  liabilities__usd: Option[String] = None,
  linkedin_links: Option[String] = None,
  linkedin_url: Option[String] = None,
  list_name: Option[String] = None,
  locality: Option[String] = None,
  location: Option[String] = None,
  management_level: Option[String] = None,
  middle_name: Option[String] = None,
  naics: Option[String] = None,
  naics1: Option[String] = None,
  naics2: Option[String] = None,
  naics_2012_code: Option[String] = None,
  naics_2012_description: Option[String] = None,
  nace_rev_2_code: Option[String] = None,
  nace_rev_2_description: Option[String] = None,
  ownership_type: Option[String] = None,
  parent_company: Option[String] = None,
  parent_country_region: Option[String] = None,
  person_business_email: Option[String] = None,
  person_city: Option[String] = None,
  person_company_name: Option[String] = None,
  person_first_name: Option[String] = None,
  person_headline: Option[String] = None,
  person_job_title: Option[String] = None,
  person_last_name: Option[String] = None,
  person_linkedin_id: Option[String] = None,
  person_linkedin_url: Option[String] = None,
  person_location: Option[String] = None,
  person_personal_email: Option[String] = None,
  person_phone: Option[String] = None,
  person_pro_url: Option[String] = None,
  person_state: Option[String] = None,
  person_zip: Option[String] = None,
  phone: Option[String] = None,
  phone_number: Option[String] = None,
  postal_code: Option[String] = None,
  pre_tax_profit__usd: Option[String] = None,
  query: Option[String] = None,
  query_name: Option[String] = None,
  revenue: Option[String] = None,
  revenue__in_000s: Option[String] = None,
  revenue__in_u: Option[String] = None,
  revenue__usd: Option[String] = None,
  revenue_range: Option[String] = None,
  salutation: Option[String] = None,
  sales: Option[String] = None,
  secondary_industry_hierarchical_category: Option[String] = None,
  secondary_industry_label: Option[String] = None,
  sic: Option[String] = None,
  sic1: Option[String] = None,
  sic2: Option[String] = None,
  sic_code: Option[String] = None,
  source_count: Option[String] = None,
  state: Option[String] = None,
  state_or_province: Option[String] = None,
  suffix: Option[String] = None,
  ticker: Option[String] = None,
  title: Option[String] = None,
  titlecode: Option[String] = None,
  total_employees: Option[String] = None,
  tradestyle: Option[String] = None,
  uk_sic_2007_code: Option[String] = None,
  uk_sic_2007_description: Option[String] = None,
  ultimate_parent_company: Option[String] = None,
  ultimate_parent_country_region: Option[String] = None,
  url: Option[String] = None,
  us_sic_1987_code: Option[String] = None,
  us_sic_1987_description: Option[String] = None,
  website: Option[String] = None,
  zip: Option[String] = None,
  zoom_company_id: Option[String] = None,
  zoom_individual_id: Option[String] = None,
  zoominfo_industry: Option[String] = None,
  prospect_data_hash: String
)
