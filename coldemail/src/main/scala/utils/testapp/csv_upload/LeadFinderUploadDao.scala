package utils.testapp.csv_upload

//import org.postgresql.util.PGobject
//import play.api.libs.json.{JsValue, <PERSON>son}
//import scalikejdbc.interpolation.SQLSyntax
//import scalikejdbc.jodatime.JodaWrappedResultSet.fromWrappedResultSetToJodaWrappedResultSet
import scalikejdbc.{DB, WrappedResultSet, scalikejdbcSQLInterpolationImplicitDef}
import utils.{Helpers, SRLogger}
import utils.dbutils.{DBUtils, SQLUtils}
import utils.uuid.SrUuidUtils

import scala.util.{Failure, Success, Try}
import api.lead_finder.models.LeadUuid

class LeadFinderUploadDao(
                           srUuidUtils: SrUuidUtils
                         ) {
/* 17-jun-2025: [DO_NOT_REMOVE] [LEADFINDERUPLOAD] was slowing down compilation. Only needed for the Lead database upload.


  def fromDb(rs: WrappedResultSet): CsvQueueData = CsvQueueData(
    id = rs.long("id"),
    team_id = rs.long("team_id"),
    account_id = rs.long("account_id"),
    ta_id = rs.long("ta_id"),
    loggedIn_id = rs.long("loggedin_id"),
    file_url = rs.string("file_url"),
    file_name = rs.string("file_name"),
    has_been_uploaded = rs.boolean("has_been_uploaded"),
    created_at = rs.jodaDateTime("created_at"),
    uploaded_at = rs.jodaDateTimeOpt("uploaded_at"),
    column_map = Json.parse(rs.any("column_map").asInstanceOf[PGobject].getValue),
    error = rs.stringOpt("error"),
    error_at = rs.jodaDateTimeOpt("error_at"),
    dt_src = rs.stringOpt("dt_src")
  )

  def insertCsvUploadDetails(
                              accountId: Long,
                              teamId: Long,
                              ta_id: Long,
                              loggedIn_id: Long,
                              data: Seq[CsvQueueCreateFormData]
                            ): Try[List[CsvQueueData]] = Try {
    data
      .grouped(100)
      .toList
      .flatMap(csvDataGrouped => {
        var parameters = List[Any]()
        val valuePlaceholder = csvDataGrouped.map( csvData => {
            val filenameEncoded = csvData.file_url.split("/", 0).last
            val filename = java.net.URLDecoder.decode(filenameEncoded, java.nio.charset.StandardCharsets.UTF_8.name())
            val uuid = srUuidUtils.generateLeadFinderProspectUuid()
            parameters = parameters ::: List(
              uuid,
              accountId,
              teamId,
              ta_id,
              loggedIn_id,
              filename,
              csvData.file_url,
              csvData.column_map.toString,
              csvData.dt_src
            )

            sqls"""
                (
                  ?,
                  ?,
                  ?,
                  ?,
                  ?,
                  ?,
                  ?,
                  to_json(?::json),
                  ?
                )
              """
        }).reduce((vp1, vp2) => sqls"$vp1, $vp2")

        DB autoCommit { implicit session =>
          sql"""
            INSERT INTO lead_finder_csv_queue
            (
               uuid,
               account_id,
               team_id,
               ta_id,
               loggedIn_id,
               file_name,
               file_url,
               column_map,
               dt_src
            )
            VALUES $valuePlaceholder
            ON CONFLICT DO NOTHING
            RETURNING *;
          """
            .bind(parameters*)
            .map(fromDb)
            .list
            .apply()
        }
      })
  }

  def insertLinkedinCsvUploadDetails(
                                      accountId: Long,
                                      teamId: Long,
                                      ta_id: Long,
                                      loggedIn_id: Long,
                                      data: Seq[CsvQueueCreateFormData]
                                    ): Try[List[CsvQueueData]] = Try {
    data
      .grouped(100)
      .toList
      .flatMap(csvDataGrouped => {
        var parameters = List[Any]()
        val valuePlaceholder = csvDataGrouped.map(csvData => {
          val filenameEncoded = csvData.file_url.split("/", 0).last
          val filename = java.net.URLDecoder.decode(filenameEncoded, java.nio.charset.StandardCharsets.UTF_8.name())
          val uuid = srUuidUtils.generateLeadFinderProspectUuid()
          parameters = parameters ::: List(
            uuid,
            accountId,
            teamId,
            ta_id,
            loggedIn_id,
            filename,
            csvData.file_url,
            csvData.column_map.toString,
            csvData.dt_src
          )

          sqls"""
                (
                  ?,
                  ?,
                  ?,
                  ?,
                  ?,
                  ?,
                  ?,
                  to_json(?::json),
                  ?
                )
              """
        }).reduce((vp1, vp2) => sqls"$vp1, $vp2")

        DB autoCommit { implicit session =>
          sql"""
            INSERT INTO lead_finder_csv_linkedin_queue
            (
               uuid,
               account_id,
               team_id,
               ta_id,
               loggedIn_id,
               file_name,
               file_url,
               column_map,
               dt_src
            )
            VALUES $valuePlaceholder
            ON CONFLICT DO NOTHING
            RETURNING *;
          """
            .bind(parameters*)
            .map(fromDb)
            .list
            .apply()
        }
      })
  }

  def find(): Try[Option[CsvQueueData]] = Try {
    DB.readOnly { implicit session =>
      sql"""
      SELECT * FROM lead_finder_csv_queue
      WHERE has_been_uploaded = FALSE AND error IS NULL ORDER BY created_at asc LIMIT 1;
    """
        .map(fromDb)
        .single
        .apply()

    }
  }


  def findLinkedinFilesFromQueue(): Try[Option[CsvQueueData]] = Try {
    DB.readOnly { implicit session =>
      sql"""
      SELECT * FROM lead_finder_csv_linkedin_queue
      WHERE has_been_uploaded = FALSE AND error IS NULL ORDER BY created_at asc LIMIT 1;
    """
        .map(fromDb)
        .single
        .apply()

    }
  }

  def findAll():Try[List[String]] = Try {
    DB.readOnly { implicit session =>
      sql"""
      SELECT * FROM lead_finder_csv_queue
    """
        .map(rs => rs.string("file_url"))
        .list
        .apply()

    }
  }

  def findAllLinkedin(): Try[List[String]] = Try {
    DB.readOnly { implicit session =>
      sql"""
      SELECT * FROM lead_finder_csv_linkedin_queue
    """
        .map(rs => rs.string("file_url"))
        .list
        .apply()

    }
  }

*/

 def isDomainPresentInVerifiedDomain(domain: String): Try[Boolean] = Try {
    DB.readOnly { implicit session =>
      sql"""
      SELECT EXISTS (
        SELECT 1 FROM lead_finder_verified_business_domains
        WHERE sitename = $domain
      ) AS domain_exists
    """
        .map(rs => rs.boolean("domain_exists"))
        .single
        .apply()
        .getOrElse(false)
    }
  }



  private def findByProspectDataHash(prospect_data_hash: Seq[String]):Try[List[String]] = Try {
    DB.readOnly( implicit session =>
      sql"""
           SELECT prospect_data_hash
           FROM lead_finder_prospects
           WHERE prospect_data_hash in ${SQLUtils.generateSQLValuesClause(prospect_data_hash.toList)}
         """
          .map(rs => rs.string("prospect_data_hash"))
          .list
          .apply()
    )
  }




  def insertCsvParsedCsvData(
                                accountId: Long,
                                teamId: Long,
                                ta_id: Long,
                                data: Seq[CsvUploadCol],
                                fileName: String,
                                dataSource: Option[String]
                              ): Try[List[Long]] = Try {
      var sqlQueryExecutionCount = 0
      data
      .grouped(200)
      .toList
      .flatMap(csvDataGrouped => {
        var parameters = List[Any]()
        val filterCsvDataGroupedDistinct = csvDataGrouped.distinct
        val hashAlreadyPresentInTable = findByProspectDataHash(
          prospect_data_hash = filterCsvDataGroupedDistinct.map(p=>p.prospect_data_hash)
        ).get
        val filterDuplicatedHash = filterCsvDataGroupedDistinct.filter(
          p => {
            !hashAlreadyPresentInTable.contains(p.prospect_data_hash) && p.person_business_email.isDefined
          }
        )
        var listOfCSVUploadColumnForWhichAnErrorOccurred: List[CsvUploadCol] = List()
        val filteredValidData: Seq[CsvUploadCol] = filterDuplicatedHash.filter(data =>{
          Helpers.getDomainFromEmail(email = data.person_business_email.get) match {
            case Some(domain) => {
              isDomainPresentInVerifiedDomain(
              domain = domain
            ) match {
              case Success(value) => value
              case Failure(exception) => {
                println(s"Error occurred isDomainPresentInVerifiedDomain ${exception.printStackTrace()}")
                println("Not inserting , adding it to the list  and moving forward")
                listOfCSVUploadColumnForWhichAnErrorOccurred = listOfCSVUploadColumnForWhichAnErrorOccurred ++ List(data)
                false
              }
            }
            }
            case None => {
              println(s"Domain Not found for the given email:${data.person_business_email}")
              false
            }
          }



        })

        println(s"listOfCSVUploadColumnForWhichAnErrorOccurred:: ${listOfCSVUploadColumnForWhichAnErrorOccurred}")

        if(filteredValidData.isEmpty){
            Seq()
          } else {
            val valuePlaceholder = filteredValidData.map(csvData => {
              val uuid = srUuidUtils.generateLeadFinderProspectUuid()
              parameters = parameters ::: List(
                uuid,
                accountId,
                teamId,
                ta_id,
                fileName,
                csvData.address,
                csvData.address_1,
                csvData.address_2,
                csvData.address_line_1,
                csvData.address_line_2,
                csvData.address_line_3,
                csvData.anzsic_2006_code,
                csvData.anzsic_2006_description,
                csvData.app_using,
                csvData.assets__usd,
                csvData.birth_date,
                csvData.birth_year,
                csvData.business_description,
                csvData.city,
                csvData.company,
                csvData.company_address,
                csvData.company_city,
                csvData.company_country,
                csvData.company_division_name,
                csvData.company_domain_name,
                csvData.company_founded,
                csvData.company_industry,
                csvData.company_linkedin_id,
                csvData.company_linkedin_url,
                csvData.company_meta_description,
                csvData.company_meta_emails,
                csvData.company_meta_keywords,
                csvData.company_meta_phones,
                csvData.company_meta_title,
                csvData.company_name,
                csvData.company_phone_number,
                csvData.company_size,
                csvData.company_state,
                csvData.company_street_address,
                csvData.company_type,
                csvData.company_zip_postal_code,
                csvData.contact,
                csvData.contact_level,
                csvData.contact_name,
                csvData.contact_title,
                csvData.countries,
                csvData.country,
                csvData.country_region,
                csvData.county,
                csvData.dnb_hoovers_industry,
                csvData.direct_marketing_status,
                csvData.direct_phone,
                csvData.direct_phone_number,
                csvData.email,
                csvData.email_address,
                csvData.email_domain,
                csvData.email_format,
                csvData.emails,
                csvData.employee_size,
                csvData.employees,
                csvData.employees__all_sites,
                csvData.employees__single_site,
                csvData.employees_range,
                csvData.encrypted_email_address,
                csvData.entity_type,
                csvData.fax,
                csvData.first_name,
                csvData.full_name,
                csvData.gender,
                csvData.highest_level_job_function,
                csvData.industry,
                csvData.industry_hierarchical_category,
                csvData.industry_label,
                csvData.industry_type,
                csvData.isic_rev_4_code,
                csvData.isic_rev_4_description,
                csvData.job_function,
                csvData.job_title,
                csvData.job_title_hierarchy_level,
                csvData.last_name,
                csvData.liabilities__usd,
                csvData.linkedin_links,
                csvData.linkedin_url,
                csvData.list_name,
                csvData.locality,
                csvData.location,
                csvData.management_level,
                csvData.middle_name,
                csvData.naics,
                csvData.naics1,
                csvData.naics2,
                csvData.naics_2012_code,
                csvData.naics_2012_description,
                csvData.nace_rev_2_code,
                csvData.nace_rev_2_description,
                csvData.ownership_type,
                csvData.parent_company,
                csvData.parent_country_region,
                csvData.person_business_email,
                csvData.person_city,
                csvData.person_company_name,
                csvData.person_first_name,
                csvData.person_headline,
                csvData.person_job_title,
                csvData.person_last_name,
                csvData.person_linkedin_id,
                csvData.person_linkedin_url,
                csvData.person_location,
                csvData.person_personal_email,
                csvData.person_phone,
                csvData.person_pro_url,
                csvData.person_state,
                csvData.person_zip,
                csvData.phone,
                csvData.phone_number,
                csvData.postal_code,
                csvData.pre_tax_profit__usd,
                csvData.query,
                csvData.query_name,
                csvData.revenue,
                csvData.revenue__in_000s,
                csvData.revenue__in_u,
                csvData.revenue__usd,
                csvData.revenue_range,
                csvData.salutation,
                csvData.sales,
                csvData.secondary_industry_hierarchical_category,
                csvData.secondary_industry_label,
                csvData.sic,
                csvData.sic1,
                csvData.sic2,
                csvData.sic_code,
                csvData.source_count,
                csvData.state,
                csvData.state_or_province,
                csvData.suffix,
                csvData.ticker,
                csvData.title,
                csvData.titlecode,
                csvData.total_employees,
                csvData.tradestyle,
                csvData.uk_sic_2007_code,
                csvData.uk_sic_2007_description,
                csvData.ultimate_parent_company,
                csvData.ultimate_parent_country_region,
                csvData.url,
                csvData.us_sic_1987_code,
                csvData.us_sic_1987_description,
                csvData.website,
                csvData.zip,
                csvData.zoom_company_id,
                csvData.zoom_individual_id,
                csvData.zoominfo_industry,
                csvData.prospect_data_hash,
                dataSource,
              )

              sqls"""
            (
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?,
              ?
            )

          """
            }).reduce((vp1, vp2) => sqls"$vp1, $vp2")

            println(s"sqlQueryExecutionCount: $sqlQueryExecutionCount")
            sqlQueryExecutionCount = sqlQueryExecutionCount + 1
            DB autoCommit { implicit session =>
              sql"""
            INSERT INTO lead_finder_prospects
            (
              uuid,
              account_id,
              team_id ,
              ta_id,
              file_name,
              address,
              address_1,
              address_2,
              address_line_1,
              address_line_2,
              address_line_3,
              anzsic_2006_code,
              anzsic_2006_description,
              app_using,
              assets__usd_,
              birth_date,
              birth_year,
              business_description,
              city,
              company,
              company_address,
              company_city,
              company_country,
              company_division_name,
              company_domain_name,
              company_founded,
              company_industry,
              company_linkedin_id,
              company_linkedin_url,
              company_meta_description,
              company_meta_emails,
              company_meta_keywords,
              company_meta_phones,
              company_meta_title,
              company_name,
              company_phone_number,
              company_size,
              company_state,
              company_street_address,
              company_type,
              company_zip_postal_code,
              contact,
              contact_level,
              contact_name,
              contact_title,
              countries,
              country,
              country_region,
              county,
              dnb_hoovers_industry,
              direct_marketing_status,
              direct_phone,
              direct_phone_number,
              email,
              email_address,
              email_domain,
              email_format,
              emails,
              employee_size,
              employees,
              employees__all_sites_,
              employees__single_site_,
              employees_range,
              encrypted_email_address,
              entity_type,
              fax,
              first_name,
              full_name,
              gender,
              highest_level_job_function,
              industry,
              industry_hierarchical_category,
              industry_label,
              industry_type,
              isic_rev_4_code,
              isic_rev_4_description,
              job_function,
              job_title,
              job_title_hierarchy_level,
              last_name,
              liabilities__usd_,
              linkedin_links,
              linkedin_url,
              list_name,
              locality,
              location,
              management_level,
              middle_name,
              naics,
              naics1,
              naics2,
              naics_2012_code,
              naics_2012_description,
              nace_rev_2_code,
              nace_rev_2_description,
              ownership_type,
              parent_company,
              parent_country_region,
              person_business_email,
              person_city,
              person_company_name,
              person_first_name,
              person_headline,
              person_job_title,
              person_last_name,
              person_linkedin_id,
              person_linkedin_url,
              person_location,
              person_personal_email,
              person_phone,
              person_pro_url,
              person_state,
              person_zip,
              phone,
              phone_number,
              postal_code,
              pre_tax_profit__usd_,
              query,
              query_name,
              revenue,
              revenue__in_000s_,
              revenue__in_u_,
              revenue__usd_,
              revenue_range,
              salutation,
              sales,
              secondary_industry_hierarchical_category,
              secondary_industry_label,
              sic,
              sic1,
              sic2,
              sic_code,
              source_count,
              state,
              state_or_province,
              suffix,
              ticker,
              title,
              titlecode,
              total_employees,
              tradestyle,
              uk_sic_2007_code,
              uk_sic_2007_description,
              ultimate_parent_company,
              ultimate_parent_country_region,
              url,
              us_sic_1987_code,
              us_sic_1987_description,
              website,
              zip,
              zoom_company_id,
              zoom_individual_id,
              zoominfo_industry,
              prospect_data_hash,
              dt_src
            )
            VALUES $valuePlaceholder
            ON CONFLICT DO NOTHING
            RETURNING id;
          """
                .bind(parameters*)
                .map(rs => rs.long("id"))
                .list
                .apply()
            }
          }

      }
      )

  }

  /* 17-jun-2025: [DO_NOT_REMOVE] [LEADFINDERUPLOAD] was slowing down compilation. Only needed for the Lead database upload.


  private def findByLinkedinProspectDataHash(prospect_data_hash: Seq[String]): Try[List[String]] = Try {
    DB.readOnly(implicit session =>
      sql"""
           SELECT prospect_data_hash
           FROM li_prospect_data
           WHERE prospect_data_hash in ${SQLUtils.generateSQLValuesClause(prospect_data_hash.toList)}
         """
        .map(rs => rs.string("prospect_data_hash"))
        .list
        .apply()
    )
  }

  def insertLinkedinCsvParsedCsvData(
                                      accountId: Long,
                                      teamId: Long,
                                      ta_id: Long,
                                      data: Seq[CsvUploadColLinkedin],
                                      csvQueueData: CsvQueueData,
                                    ): Try[List[List[Long]]] = Try {
    var sqlQueryExecutionCount = 0
    data
      .grouped(100)
      .toList
      .flatMap(csvDataGrouped => {
        var parameters = List[Any]()
        val filterCsvDataGroupedDistinct = csvDataGrouped.distinct
        val hashAlreadyPresentInTable = findByLinkedinProspectDataHash(
          prospect_data_hash = filterCsvDataGroupedDistinct.map(p => p.prospect_data_hash)
        ).get
        val filterDuplicatedHash = filterCsvDataGroupedDistinct.filter(
          p => !hashAlreadyPresentInTable.contains(p.prospect_data_hash)
        )
        val lead_finder_prospects_data =
          if (filterDuplicatedHash.isEmpty) {
            Seq()
          } else {
            val valuePlaceholder = filterDuplicatedHash.map(csvData => {
              val uuid = srUuidUtils.generateLeadFinderProspectUuid()
              parameters = parameters ::: List(
                uuid,
                accountId,
                teamId,
                ta_id,
                csvQueueData.file_name,
                csvData.anzsic_2006_code,
                csvData.anzsic_2006_description,
                csvData.address,
                csvData.address_1,
                csvData.address_2,
                csvData.address_line_1,
                csvData.address_line_2,
                csvData.address_line_3,
                csvData.app_using,
                csvData.assets__usd,
                csvData.birth_date,
                csvData.birth_year,
                csvData.business_description,
                csvData.city,
                csvData.company,
                csvData.company_city,
                csvData.company_country,
                csvData.company_name,
                csvData.company_state,
                csvData.company_street_address,
                csvData.company_zip_postal_code,
                csvData.company_division_name,
                csvData.company_domain_name,
                csvData.company_phone_number,
                csvData.contact,
                csvData.contact_level,
                csvData.contact_name,
                csvData.contact_title,
                csvData.countries,
                csvData.country,
                csvData.country_region,
                csvData.county,
                csvData.dnb_hoovers_industry,
                csvData.direct_marketing_status,
                csvData.direct_phone,
                csvData.direct_phone_number,
                csvData.email,
                csvData.email_domain,
                csvData.email_address,
                csvData.emails,
                csvData.employee_size,
                csvData.employees,
                csvData.employees__all_sites,
                csvData.employees__single_site,
                csvData.employees_range,
                csvData.encrypted_email_address,
                csvData.entity_type,
                csvData.fax,
                csvData.first_name,
                csvData.full_name,
                csvData.gender,
                csvData.highest_level_job_function,
                csvData.isic_rev_4_code,
                csvData.isic_rev_4_description,
                csvData.industry,
                csvData.industry_type,
                csvData.industry_hierarchical_category,
                csvData.industry_label,
                csvData.job_function,
                csvData.job_title,
                csvData.job_title_hierarchy_level,
                csvData.last_name,
                csvData.liabilities__usd,
                csvData.linkedin_url,
                csvData.linkedin_links,
                csvData.locality,
                csvData.location,
                csvData.management_level,
                csvData.middle_name,
                csvData.nace_rev_2_code,
                csvData.nace_rev_2_description,
                csvData.naics,
                csvData.naics_2012_code,
                csvData.naics_2012_description,
                csvData.naics1,
                csvData.naics2,
                csvData.ownership_type,
                csvData.parent_company,
                csvData.parent_country_region,
                csvData.person_city,
                csvData.person_pro_url,
                csvData.person_state,
                csvData.person_zip,
                csvData.phone,
                csvData.phone_number,
                csvData.postal_code,
                csvData.pre_tax_profit__usd,
                csvData.query_name,
                csvData.revenue,
                csvData.revenue__usd,
                csvData.revenue__in_u,
                csvData.revenue__in_000s,
                csvData.revenue_range,
                csvData.sic,
                csvData.sic_code,
                csvData.sic1,
                csvData.sic2,
                csvData.sales,
                csvData.salutation,
                csvData.secondary_industry_hierarchical_category,
                csvData.secondary_industry_label,
                csvData.source_count,
                csvData.state,
                csvData.state_or_province,
                csvData.suffix,
                csvData.ticker,
                csvData.title,
                csvData.titlecode,
                csvData.total_employees,
                csvData.tradestyle,
                csvData.uk_sic_2007_code,
                csvData.uk_sic_2007_description,
                csvData.url,
                csvData.us_sic_1987_code,
                csvData.us_sic_1987_description,
                csvData.ultimate_parent_company,
                csvData.ultimate_parent_country_region,
                csvData.website,
                csvData.zip,
                csvData.zoom_individual_id,
                csvData.zoom_company_id,
                csvData.zoominfo_industry,
                csvData.source_data_id_1,
                csvData.company_address,
                csvData.company_founded,
                csvData.company_industry,
                csvData.company_linkedin_id,
                csvData.company_linkedin_url,
                csvData.company_meta_description,
                csvData.company_meta_emails,
                csvData.company_meta_keywords,
                csvData.company_meta_phones,
                csvData.company_meta_title,
                csvData.company_size,
                csvData.company_type,
                csvData.email_format,
                csvData.facebook_id,
                csvData.facebook_url,
                csvData.facebook_username,
                csvData.github_url,
                csvData.github_username,
                csvData.source_data_id_2,
                csvData.inferred_salary,
                csvData.inferred_years_experience,
                csvData.job_company_facebook_url,
                csvData.job_company_founded,
                csvData.job_company_id,
                csvData.job_company_industry,
                csvData.job_company_linkedin_id,
                csvData.job_company_linkedin_url,
                csvData.job_company_location_address_line_2,
                csvData.job_company_location_continent,
                csvData.job_company_location_country,
                csvData.job_company_location_geo,
                csvData.job_company_location_locality,
                csvData.job_company_location_metro,
                csvData.job_company_location_name,
                csvData.job_company_location_postal_code,
                csvData.job_company_location_region,
                csvData.job_company_location_street_address,
                csvData.job_company_name,
                csvData.job_company_size,
                csvData.job_company_twitter_url,
                csvData.job_company_website,
                csvData.job_last_updated,
                csvData.job_start_date,
                csvData.job_summary,
                csvData.job_title_role,
                csvData.job_title_sub_role,
                csvData.linkedin_connections,
                csvData.linkedin_id,
                csvData.linkedin_username,
                csvData.list_name,
                csvData.location_address_line_2,
                csvData.location_continent,
                csvData.location_country,
                csvData.location_geo,
                csvData.location_last_updated,
                csvData.location_locality,
                csvData.location_metro,
                csvData.location_name,
                csvData.location_postal_code,
                csvData.location_region,
                csvData.location_street_address,
                csvData.middle_initial,
                csvData.mobile_phone,
                csvData.person_business_email,
                csvData.person_company_name,
                csvData.person_first_name,
                csvData.person_headline,
                csvData.person_job_title,
                csvData.person_last_name,
                csvData.person_linkedin_id,
                csvData.person_linkedin_url,
                csvData.person_location,
                csvData.person_personal_email,
                csvData.person_phone,
                csvData.query,
                csvData.summary,
                csvData.twitter_url,
                csvData.twitter_username,
                csvData.version_status_contains_0,
                csvData.version_status_contains_1,
                csvData.version_status_contains_2,
                csvData.version_status_current_version,
                csvData.version_status_previous_version,
                csvData.version_status_status,
                csvData.work_email,
                csvData.prospect_data_hash,
                csvQueueData.dt_src
              )

              sqls"""
              (
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?
              )
              """
            }).reduce((vp1, vp2) => sqls"$vp1, $vp2")
            println(s"sqlQueryExecutionCount: $sqlQueryExecutionCount")
            sqlQueryExecutionCount = sqlQueryExecutionCount + 1
            DB autoCommit { implicit session =>
              sql"""
                INSERT INTO li_prospect_data
                (
                  uuid,
                  account_id,
                  team_id ,
                  ta_id,
                  file_name,
                  anzsic_2006_code,
                  anzsic_2006_description,
                  address,
                  address_1,
                  address_2,
                  address_line_1,
                  address_line_2,
                  address_line_3,
                  app_using,
                  assets__usd_,
                  birth_date,
                  birth_year,
                  business_description,
                  city,
                  company,
                  company_city,
                  company_country,
                  company_name,
                  company_state,
                  company_street_address,
                  company_zip_postal_code,
                  company_division_name,
                  company_domain_name,
                  company_phone_number,
                  contact,
                  contact_level,
                  contact_name,
                  contact_title,
                  countries,
                  country,
                  country_region,
                  county,
                  dnb_hoovers_industry,
                  direct_marketing_status,
                  direct_phone,
                  direct_phone_number,
                  email,
                  email_domain,
                  email_address,
                  emails,
                  employee_size,
                  employees,
                  employees__all_sites_,
                  employees__single_site_,
                  employees_range,
                  encrypted_email_address,
                  entity_type,
                  fax,
                  first_name,
                  full_name,
                  gender,
                  highest_level_job_function,
                  isic_rev_4_code,
                  isic_rev_4_description,
                  industry,
                  industry_type,
                  industry_hierarchical_category,
                  industry_label,
                  job_function,
                  job_title,
                  job_title_hierarchy_level,
                  last_name,
                  liabilities__usd_,
                  linkedin_url,
                  linkedin_links,
                  locality,
                  location,
                  management_level,
                  middle_name,
                  nace_rev_2_code,
                  nace_rev_2_description,
                  naics,
                  naics_2012_code,
                  naics_2012_description,
                  naics1,
                  naics2,
                  ownership_type,
                  parent_company,
                  parent_country_region,
                  person_city,
                  person_pro_url,
                  person_state,
                  person_zip,
                  phone,
                  phone_number,
                  postal_code,
                  pre_tax_profit__usd_,
                  query_name,
                  revenue,
                  revenue__usd_,
                  revenue__in_u_,
                  revenue__in_000s_,
                  revenue_range,
                  sic,
                  sic_code,
                  sic1,
                  sic2,
                  sales,
                  salutation,
                  secondary_industry_hierarchical_category,
                  secondary_industry_label,
                  source_count,
                  state,
                  state_or_province,
                  suffix,
                  ticker,
                  title,
                  titlecode,
                  total_employees,
                  tradestyle,
                  uk_sic_2007_code,
                  uk_sic_2007_description,
                  url,
                  us_sic_1987_code,
                  us_sic_1987_description,
                  ultimate_parent_company,
                  ultimate_parent_country_region,
                  website,
                  zip,
                  zoom_individual_id,
                  zoom_company_id,
                  zoominfo_industry,
                  source_data_id_1,
                  company_address,
                  company_founded,
                  company_industry,
                  company_linkedin_id,
                  company_linkedin_url,
                  company_meta_description,
                  company_meta_emails,
                  company_meta_keywords,
                  company_meta_phones,
                  company_meta_title,
                  company_size,
                  company_type,
                  email_format,
                  facebook_id,
                  facebook_url,
                  facebook_username,
                  github_url,
                  github_username,
                  source_data_id_2,
                  inferred_salary,
                  inferred_years_experience,
                  job_company_facebook_url,
                  job_company_founded,
                  job_company_id,
                  job_company_industry,
                  job_company_linkedin_id,
                  job_company_linkedin_url,
                  job_company_location_address_line_2,
                  job_company_location_continent,
                  job_company_location_country,
                  job_company_location_geo,
                  job_company_location_locality,
                  job_company_location_metro,
                  job_company_location_name,
                  job_company_location_postal_code,
                  job_company_location_region,
                  job_company_location_street_address,
                  job_company_name,
                  job_company_size,
                  job_company_twitter_url,
                  job_company_website,
                  job_last_updated,
                  job_start_date,
                  job_summary,
                  job_title_role,
                  job_title_sub_role,
                  linkedin_connections,
                  linkedin_id,
                  linkedin_username,
                  list_name,
                  location_address_line_2,
                  location_continent,
                  location_country,
                  location_geo,
                  location_last_updated,
                  location_locality,
                  location_metro,
                  location_name,
                  location_postal_code,
                  location_region,
                  location_street_address,
                  middle_initial,
                  mobile_phone,
                  person_business_email,
                  person_company_name,
                  person_first_name,
                  person_headline,
                  person_job_title,
                  person_last_name,
                  person_linkedin_id,
                  person_linkedin_url,
                  person_location,
                  person_personal_email,
                  person_phone,
                  query,
                  summary,
                  twitter_url,
                  twitter_username,
                  version_status_contains_0,
                  version_status_contains_1,
                  version_status_contains_2,
                  version_status_current_version,
                  version_status_previous_version,
                  version_status_status,
                  work_email,
                  prospect_data_hash,
                  dt_src
                )
                VALUES $valuePlaceholder
                ON CONFLICT DO NOTHING
                RETURNING id,prospect_data_hash;
              """
                .bind(parameters*)
                .map(rs => (rs.string("prospect_data_hash"), rs.long("id")))
                .list
                .apply()
            }
          }

        if (lead_finder_prospects_data.isEmpty) {
          List(List())
        } else {
          val filterEducationCol = filterDuplicatedHash.filter(p => p.education_data_list.isDefined)
          val filterExperienceCol = filterDuplicatedHash.filter(p => p.experience_data_list.isDefined)
          val filterAdditionalCol = filterDuplicatedHash.filter(p => p.additional_data_list.isDefined)
          filterEducationCol
            .grouped(50)
            .toList
            .map(csvData => {
            var parameters_1 = List[Any]()
            val valuePlaceholder_1 = csvData.map(prospectData=>{prospectData.education_data_list.get.map(certData => {
              val uuid = srUuidUtils.generateLeadFinderProspectUuid()
              val lead_finder_prospects_id = lead_finder_prospects_data.filter(p => p._1 == certData._2.education_data_hash.get).head._2
              parameters_1 = parameters_1 ::: List(
                lead_finder_prospects_id,
                uuid,
                accountId,
                teamId,
                ta_id,
                certData._1,
                certData._2.degrees_0,
                certData._2.degrees_1,
                certData._2.degrees_2,
                certData._2.degrees_3,
                certData._2.end_date,
                certData._2.gpa,
                certData._2.majors_0,
                certData._2.majors_1,
                certData._2.majors_2,
                certData._2.majors_3,
                certData._2.majors_4,
                certData._2.minors,
                certData._2.minors_0,
                certData._2.minors_1,
                certData._2.school_domain,
                certData._2.school_facebook_url,
                certData._2.school,
                certData._2.school_id,
                certData._2.school_linkedin_id,
                certData._2.school_linkedin_url,
                certData._2.school_location_continent,
                certData._2.school_location_country,
                certData._2.school_location_locality,
                certData._2.school_location_name,
                certData._2.school_location_region,
                certData._2.school_name,
                certData._2.school_twitter_url,
                certData._2.school_type,
                certData._2.school_website,
                certData._2.start_date,
                certData._2.summary,
                certData._2.degrees,
                certData._2.majors,
                certData._2.school_location,
                certData._2.education_data_hash
              )
              sqls"""
              (
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?
              )"""
            }).reduce((vp1, vp2) => sqls"$vp1, $vp2")}).reduce((s1, s2) => s1.append(SQLSyntax.createUnsafely(",").append(s2)))
            DB autoCommit { implicit session =>
              sql"""
              INSERT INTO li_education
              (
                li_prospect_data_id,
                uuid,
                account_id,
                team_id,
                ta_id,
                education_no,
                degrees_0,
                degrees_1,
                degrees_2,
                degrees_3,
                end_date,
                gpa,
                majors_0,
                majors_1,
                majors_2,
                majors_3,
                majors_4,
                minors,
                minors_0,
                minors_1,
                school_domain,
                school_facebook_url,
                school,
                school_id,
                school_linkedin_id,
                school_linkedin_url,
                school_location_continent,
                school_location_country,
                school_location_locality,
                school_location_name,
                school_location_region,
                school_name,
                school_twitter_url,
                school_type,
                school_website,
                start_date,
                summary,
                degrees,
                majors,
                school_location,
                education_data_hash
               )
              VALUES $valuePlaceholder_1
              ON CONFLICT DO NOTHING
              RETURNING id;
              """
                .bind(parameters_1*)
                .map(rs => rs.long("id"))
                .list
                .apply()
            }

          })

          filterExperienceCol
            .grouped(50)
            .toList
            .map(csvData => {
            var parameters_1 = List[Any]()
            val valuePlaceholder_1 = csvData.map(prospectData => {prospectData.experience_data_list.get.map(certData => {
              val uuid = srUuidUtils.generateLeadFinderProspectUuid()
              val lead_finder_prospects_id = lead_finder_prospects_data.filter(p => p._1 == certData._2.experience_data_hash.get).head._2
              parameters_1 = parameters_1 ::: List(
                lead_finder_prospects_id,
                uuid,
                accountId,
                teamId,
                ta_id,
                certData._1,
                certData._2.company_facebook_url,
                certData._2.company_founded,
                certData._2.company_id,
                certData._2.company_industry,
                certData._2.company_linkedin_id,
                certData._2.company_linkedin_url,
                certData._2.company_location,
                certData._2.company_location_address_line_2,
                certData._2.company_location_continent,
                certData._2.company_location_country,
                certData._2.company_location_geo,
                certData._2.company_location_locality,
                certData._2.company_location_metro,
                certData._2.company_location_name,
                certData._2.company_location_postal_code,
                certData._2.company_location_region,
                certData._2.company_location_street_address,
                certData._2.company_name,
                certData._2.company_size,
                certData._2.company_twitter_url,
                certData._2.company_website,
                certData._2.end_date,
                certData._2.is_primary,
                certData._2.location_names,
                certData._2.location_names_0,
                certData._2.location_names_1,
                certData._2.location_names_2,
                certData._2.location_names_3,
                certData._2.start_date,
                certData._2.summary,
                certData._2.title_levels,
                certData._2.title_levels_0,
                certData._2.title_levels_1,
                certData._2.title_levels_2,
                certData._2.title_name,
                certData._2.title_role,
                certData._2.title_sub_role,
                certData._2.experience_data_hash
              )
              sqls"""
              (
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?
              )"""
            }).reduce((vp1, vp2) => sqls"$vp1, $vp2")}).reduce((s1, s2) => s1.append(SQLSyntax.createUnsafely(",").append(s2)))


            DB autoCommit { implicit session =>
              sql"""
              INSERT INTO li_experience
              (
                li_prospect_data_id,
                uuid,
                account_id,
                team_id,
                ta_id,
                experience_no,
                company_facebook_url,
                company_founded,
                company_id,
                company_industry,
                company_linkedin_id,
                company_linkedin_url,
                company_location,
                company_location_address_line_2,
                company_location_continent,
                company_location_country,
                company_location_geo,
                company_location_locality,
                company_location_metro,
                company_location_name,
                company_location_postal_code,
                company_location_region,
                company_location_street_address,
                company_name,
                company_size,
                company_twitter_url,
                company_website,
                end_date,
                is_primary,
                location_names,
                location_names_0,
                location_names_1,
                location_names_2,
                location_names_3,
                start_date,
                summary,
                title_levels,
                title_levels_0,
                title_levels_1,
                title_levels_2,
                title_name,
                title_role,
                title_sub_role,
                experience_data_hash
               )
              VALUES $valuePlaceholder_1
              ON CONFLICT DO NOTHING
              RETURNING id;
              """
                .bind(parameters_1*)
                .map(rs => rs.long("id"))
                .list
                .apply()
            }

          })

          filterAdditionalCol
            .grouped(50)
            .toList
            .map(csvData => {
            var parameters_1 = List[Any]()
            val valuePlaceholder_1 = csvData.map(prospectData=>{prospectData.additional_data_list.get.map(certData => {
              val uuid = srUuidUtils.generateLeadFinderProspectUuid()
              val lead_finder_prospects_id = lead_finder_prospects_data.filter(p => p._1 == certData._2.additional_data_hash.get).head._2
              parameters_1 = parameters_1 ::: List(
                lead_finder_prospects_id,
                uuid,
                accountId,
                teamId,
                ta_id,
                certData._1,
                certData._2.certification_end_date,
                certData._2.certification_name,
                certData._2.certification_organization,
                certData._2.certification_start_date,
                certData._2.interest,
                certData._2.job_title_level,
                certData._2.language_name,
                certData._2.language_proficiency,
                certData._2.location_name,
                certData._2.phone_number,
                certData._2.profile_id,
                certData._2.profiles_network,
                certData._2.profile_url,
                certData._2.profile_username,
                certData._2.region,
                certData._2.skill,
                certData._2.country,
                certData._2.email_address,
                certData._2.email_type,
                certData._2.street_addresses_address_line_2,
                certData._2.street_addresses_continent,
                certData._2.street_addresses_country,
                certData._2.street_addresses_geo,
                certData._2.street_addresses_locality,
                certData._2.street_addresses_metro,
                certData._2.street_addresses_name,
                certData._2.street_addresses_postal_code,
                certData._2.street_addresses_region,
                certData._2.street_addresses_street_address,
                certData._2.additional_data_hash
              )
              sqls"""
              (
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ?
              )"""
            }).reduce((vp1, vp2) => sqls"$vp1, $vp2")}).reduce((s1, s2) => s1.append(SQLSyntax.createUnsafely(",").append(s2)))


            DB autoCommit { implicit session =>
              sql"""
              INSERT INTO li_prospect_data_additional
              (
                li_prospect_data_id,
                uuid,
                account_id,
                team_id,
                ta_id,
                additional_data_no,
                certification_end_date,
                certification_name,
                certification_organization,
                certification_start_date,
                interest,
                job_title_level,
                language_name,
                language_proficiency,
                location_name,
                phone_number,
                profile_id,
                profile_network,
                profile_url,
                profile_username,
                region,
                skill,
                country,
                email_address,
                email_type,
                street_addresses_address_line_2,
                street_addresses_continent,
                street_addresses_country,
                street_addresses_geo,
                street_addresses_locality,
                street_addresses_metro,
                street_addresses_name,
                street_addresses_postal_code,
                street_addresses_region,
                street_addresses_street_address,
                additional_data_hash
               )
              VALUES $valuePlaceholder_1
              ON CONFLICT DO NOTHING
              RETURNING id;
              """
                .bind(parameters_1*)
                .map(rs => rs.long("id"))
                .list
                .apply()
            }

          })

        }

      }
      )

  }

  def updateOnFail(
                    id: Long,
                    charsetDetected: Option[String],
                    error: String,
                    logger: SRLogger
                  ): Try[Int] = {
    DBUtils.autoCommit(
      logger = logger.appendLogRequestId("CsvQueue.updateOnFail")
    ) { implicit session =>
      sql"""
         UPDATE lead_finder_csv_queue SET error = $error, error_at = now(), encoding = $charsetDetected WHERE id = $id;
        """.update.apply()
    }
  }

  def updateOnFailLinkedin(
                        id: Long,
                        charsetDetected: Option[String],
                        error: String,
                        logger: SRLogger
                      ): Try[Int] = {
    DBUtils.autoCommit(
      logger = logger.appendLogRequestId("CsvQueue.updateOnFail")
    ) { implicit session =>
      sql"""
         UPDATE lead_finder_csv_linkedin_queue SET error = $error, error_at = now(), encoding = $charsetDetected WHERE id = $id;
        """.update.apply()
    }
  }

  def update(
              id: Long,
              charsetDetected: Option[String],
              result: JsValue,
              parserName: Option[String],
              parserContext: Option[JsValue],
              logger: SRLogger
            ): Try[Int] = {

    logger.info(s"CsvQueue.update called: csv_$id  :: parserName: $parserName :: parserContext: $parserContext :: result: $result ")

    DBUtils.autoCommit(
      logger = logger.appendLogRequestId("CsvQueue.update")
    ) { implicit session =>

      val parserContextSql: SQLSyntax = if (parserContext.isEmpty) sqls" " else {

        sqls" parser_context = to_jsonb(${parserContext.get.toString}::jsonb), "

      }

      sql"""
        UPDATE lead_finder_csv_queue SET
      has_been_uploaded = true,
      encoding = $charsetDetected,

      parser_name = $parserName,

      $parserContextSql

      result = to_json(${result.toString()}::json)

      WHERE id = $id;
"""
        .update.apply()
    }
  }

  def updateLinkedin(
                      id: Long,
                      charsetDetected: Option[String],
                      result: JsValue,
                      parserName: Option[String],
                      parserContext: Option[JsValue],
                      logger: SRLogger
                    ): Try[Int] = {

    logger.info(s"CsvQueue.update called: csv_$id  :: parserName: $parserName :: parserContext: $parserContext :: result: $result ")

    DBUtils.autoCommit(
      logger = logger.appendLogRequestId("CsvQueue.update")
    ) { implicit session =>

      val parserContextSql: SQLSyntax = if (parserContext.isEmpty) sqls" " else {

        sqls" parser_context = to_jsonb(${parserContext.get.toString}::jsonb), "

      }

      sql"""
        UPDATE lead_finder_csv_linkedin_queue SET
      has_been_uploaded = true,
      encoding = $charsetDetected,

      parser_name = $parserName,

      $parserContextSql

      result = to_json(${result.toString()}::json)

      WHERE id = $id;
"""
        .update.apply()
    }
  }
*/

}
