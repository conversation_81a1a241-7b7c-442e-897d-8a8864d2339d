package utils.testapp

import api.accounts.{ReplyHandling, TeamId}
import api.campaigns.services.CampaignId
import api.emails.EmailScheduledDAO
import api.emails.models.{DeletionReason, MqDeleteAndRevertDataMsgV2}
import io.smartreach.esp.api.emails.EmailSettingId
import play.api.Logging
import scalikejdbc.DBSession
import scalikejdbc.config.DBs
import utils.SRLogger
import utils.email.models.DeleteEmailsScheduledType
import utils.mq.delete_and_revert.MQDeletionAndRevertDataV2

object SelectForDeleteTestApp extends  Logging {

  def main(args: Array[String]): Unit = {
    if (args.length > 0) {
      DBs.setupAll()

      val applicationName = args(0)


      applicationName match {
        case "test_select_query_by_campaign_id" =>

          object  Test extends TestAppTrait {
            def init() = {
              given logger: SRLogger = new SRLogger("test_delete_task_unsent_by_campaignid")
              val dbAndSession = dbUtils.startLocalTx()

              val db = dbAndSession.db
              given session: DBSession = dbAndSession.session
              val result =  emailScheduledDAOService
                .selectEmailScheduledToDeleteBasedOnDeletionType(
                  deleteEmailsScheduledType = DeleteEmailsScheduledType.DeleteUnsentByCampaignId(
                    campaignId = 640L,
                    teamId = TeamId(id = 15L),
                    senderEmailSettingIds = Seq(EmailSettingId(emailSettingId = 111L)),
                  )
                )

              println(s"result -> ${result}")

              dbUtils.commitAndCloseSession(db = db)
            }

          }
          Test.init()





        case "test_select_query_by_email_setting_id" =>

          object Test extends TestAppTrait {
            def init() = {
              given logger: SRLogger = new SRLogger("test_select_query_by_email_setting_id")
              val dbAndSession = dbUtils.startLocalTx()

              val db = dbAndSession.db
              given session: DBSession = dbAndSession.session
              val result = emailScheduledDAOService
                .selectEmailScheduledToDeleteBasedOnDeletionType(
                  deleteEmailsScheduledType = DeleteEmailsScheduledType.DeleteAllUnSentByEmailSettingId(

                    emailSettingData = Map(
                      TeamId(id = 15L) -> Seq(EmailSettingId(111L))
                    ) ,
                  )
                )

              println(s"result -> ${result}")

              dbUtils.commitAndCloseSession(db = db)
            }

          }
          Test.init()

        case "test_select_query_by_email_setting_id_seq_empty_es_id" =>

          object Test extends TestAppTrait {
            def init() = {
              given logger: SRLogger = new SRLogger("test_select_query_by_email_setting_id_seq_empty_es_id")
              val dbAndSession = dbUtils.startLocalTx()

              val db = dbAndSession.db
              given session: DBSession = dbAndSession.session
              val result = emailScheduledDAOService
                .selectEmailScheduledToDeleteBasedOnDeletionType(
                  deleteEmailsScheduledType = DeleteEmailsScheduledType.DeleteAllUnSentByEmailSettingId(

                    emailSettingData = Map(
                      TeamId(id = 15L) -> Seq()
                    ),
                  )
                )

              println(s"result -> ${result}")

              dbUtils.commitAndCloseSession(db = db)
            }

          }
          Test.init()

        case "test_select_query_by_prospect_id_PAUSE_ALL_PROSPECT_ACCOUNT_CAMPAIGNS_ON_REPLY_empty_prospect_id" =>

          object Test extends TestAppTrait {
            def init() = {
              given logger: SRLogger = new SRLogger("test_select_query_by_prospect_id_PAUSE_ALL_PROSPECT_ACCOUNT_CAMPAIGNS_ON_REPLY_empty_prospect_id")
              val dbAndSession = dbUtils.startLocalTx()

              val db = dbAndSession.db
              given session: DBSession = dbAndSession.session
              val result = emailScheduledDAOService
                .selectEmailScheduledToDeleteBasedOnDeletionType(
                  deleteEmailsScheduledType = DeleteEmailsScheduledType.DeleteUnsentByProspectId(

                    campaignId = 307L,
                    prospectIds = Seq(8421),
                    prospectAccountIds = None,
                    replyHandling = ReplyHandling.PAUSE_ALL_PROSPECT_ACCOUNT_CAMPAIGNS_ON_REPLY,

                    teamId = TeamId(id = 15L),
                  )
                )

              println(s"result -> ${result}")

              dbUtils.commitAndCloseSession(db = db)
            }

          }
          Test.init()

        case "test_select_query_by_prospect_id_PAUSE_ALL_PROSPECT_ACCOUNT_CAMPAIGNS_ON_REPLY" =>

          object Test extends TestAppTrait {
            def init() = {
              given logger: SRLogger = new SRLogger("test_select_query_by_prospect_id_PAUSE_ALL_PROSPECT_ACCOUNT_CAMPAIGNS_ON_REPLY")
              val dbAndSession = dbUtils.startLocalTx()

              val db = dbAndSession.db
              given session: DBSession = dbAndSession.session
              val result = emailScheduledDAOService
                .selectEmailScheduledToDeleteBasedOnDeletionType(
                  deleteEmailsScheduledType = DeleteEmailsScheduledType.DeleteUnsentByProspectId(

                    campaignId = 1L,
                    prospectIds = Seq(1L),
                    prospectAccountIds = None,
                    replyHandling = ReplyHandling.PAUSE_ALL_PROSPECT_ACCOUNT_CAMPAIGNS_ON_REPLY,
                    teamId = TeamId(id = 1L),
                  )
                )

              println(s"result -> ${result}")

              dbUtils.commitAndCloseSession(db = db)
            }

          }
          Test.init()

        case "test_select_query_by_prospect_id_PAUSE_SPECIFIC_CAMPAIGN_ON_REPLY" =>

          object Test extends TestAppTrait {
            def init() = {
              given logger: SRLogger = new SRLogger("test_select_query_by_prospect_id_PAUSE_SPECIFIC_CAMPAIGN_ON_REPLY")
              val dbAndSession = dbUtils.startLocalTx()

              val db = dbAndSession.db
              given session: DBSession = dbAndSession.session
              val result = emailScheduledDAOService
                .selectEmailScheduledToDeleteBasedOnDeletionType(
                  deleteEmailsScheduledType = DeleteEmailsScheduledType.DeleteUnsentByProspectId(

                    campaignId = 607L,
                    prospectIds = Seq(8421L),
                    prospectAccountIds = None,
                      replyHandling = ReplyHandling.PAUSE_SPECIFIC_CAMPAIGN_ON_REPLY,
                    teamId = TeamId(id = 15L),
                  )
                )

              println(s"result -> ${result}")

              dbUtils.commitAndCloseSession(db = db)
            }

          }
          Test.init()


        case "test_select_query_by_prospect_id_PAUSE_ALL_PROSPECT_CAMPAIGNS_ON_REPLY" =>

          object Test extends TestAppTrait {
            def init() = {
              given logger: SRLogger = new SRLogger("test_select_query_by_prospect_id_PAUSE_ALL_PROSPECT_CAMPAIGNS_ON_REPLY")
              val dbAndSession = dbUtils.startLocalTx()

              val db = dbAndSession.db
              given session: DBSession = dbAndSession.session
              val result = emailScheduledDAOService
                .selectEmailScheduledToDeleteBasedOnDeletionType(
                  deleteEmailsScheduledType = DeleteEmailsScheduledType.DeleteUnsentByProspectId(

                    campaignId = 607L,
                    prospectIds = Seq(15L),
                    prospectAccountIds = Some(Seq(15L)),
                      replyHandling = ReplyHandling.PAUSE_ALL_PROSPECT_CAMPAIGNS_ON_REPLY,
                    teamId = TeamId(id = 15L),
                  )
                )

              println(s"result -> ${result}")

              dbUtils.commitAndCloseSession(db = db)
            }

          }
          Test.init()

        case "test_select_query_email_scheduled_id" =>

          object Test extends TestAppTrait {
            def init() = {
              given logger: SRLogger = new SRLogger("test_select_query_email_scheduled_id")
              val dbAndSession = dbUtils.startLocalTx()

              val db = dbAndSession.db
              given session: DBSession = dbAndSession.session
              val result = emailScheduledDAOService
                .selectEmailScheduledToDeleteBasedOnDeletionType(
                  deleteEmailsScheduledType = DeleteEmailsScheduledType.DeleteUnsentByEmailScheduledId(

                    emailScheduledId = 95193L,
                    teamId= TeamId(id = 15L),
                    senderEmailSettingId = Some(111L),
                  )
                )

              println(s"result -> ${result}")

              dbUtils.commitAndCloseSession(db = db)
            }

          }
          Test.init()
        case "test_select_query_email_setting_id_except_manual" =>

          object Test extends TestAppTrait {
            def init() = {
              given logger: SRLogger = new SRLogger("test_select_query_email_setting_id_except_manual")
              val dbAndSession = dbUtils.startLocalTx()

              val db = dbAndSession.db
              given session: DBSession = dbAndSession.session
              val result = emailScheduledDAOService
                .selectEmailScheduledToDeleteBasedOnDeletionType(
                  deleteEmailsScheduledType = DeleteEmailsScheduledType.DeleteUnSentByEmailSettingIdNotManual(

                    emailSettingData = Map(
                      TeamId(id = 15L) -> Seq(EmailSettingId(emailSettingId =  111L))
                    )
                  )
                )

              println(s"result -> ${result}")

              dbUtils.commitAndCloseSession(db = db)
            }

          }
          Test.init()

        case "test_deleting_by_service_layer" =>

          object Test extends TestAppTrait {
            def init() = {
              given logger: SRLogger = new SRLogger("test_select_query_email_setting_id_except_manual")
              val dbAndSession = dbUtils.startLocalTx()

              val db = dbAndSession.db
              given session: DBSession = dbAndSession.session
              emailsScheduledDeleteService.processMessageForDeletionAndRevert(
                msg = MQDeletionAndRevertDataV2(
                  deletion_reason =  DeletionReason.PreviewUpdatedForEmail,
                  deleteAndRevertData =  MqDeleteAndRevertDataMsgV2.TaskAndEmailMessage(
                    task_id = "task_2eokCFhkk6Wxu4S4Lu9q3BESLfv",
                    email_scheduled_id = 95612L,
                    team_id = TeamId(id = 15),
                    sender_email_setting_id = Some(111L)
                  ),
                  reverted_by =  "shsahsnak",
                  revert_log_trace_id =  logger.logTraceId,
                )
              )

              dbUtils.commitAndCloseSession(db = db)
            }

          }
          Test.init()

      }

    }

  }



}
