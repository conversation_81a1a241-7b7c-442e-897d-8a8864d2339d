package utils.testapp

import api.CacheServiceJedis
import api.accounts.TeamId
import api.accounts.dao.AccountOrgBillingRelatedInfoDAO
import api.accounts.service.AccountOrgBillingRelatedService
import api.campaigns.CampaignProspectDAO
import api.campaigns.dao.{CampaignProspectDAO_2, CampaignSchedulingMetadataDAO}
import api.campaigns.services.MergeTagService
import api.columns.{ProspectColumnDef, ProspectColumnDefDAO}
import api.emails.{EmailScheduledDAO, EmailSettingDAO, EmailThreadDAO}
import api.free_email_domain.dao.FreeEmailDomainListDAO
import api.prospects.dao.{ProspectAddEventDAO, ProspectDAO, ProspectsEmailsDAO}
import api.prospects.dao_service.ProspectDAOService
import api.prospects.service.ProspectServiceV2
import api.search.ProspectQuery
import api.tasks.pgDao.TaskPgDAO
import api.tasks.services.LinkedinTaskService
import api.team_inbox.dao.{ReplySentimentDAO, ReplySentimentJedisDAO}
import api.team_inbox.dao_service.ReplySentimentDAOService
import app_services.db_query_counter.SrDBQueryCounterService
import app_services.db_query_counter.dao.DBCounterDAO
import play.api.Logging
import scalikejdbc.config.DBs
import utils.SRLogger
import utils.dbutils.DBUtils
import utils.emailvalidation.EmailValidationModel
import utils.uuid.SrUuidUtils

object FindForWebhooksV2 extends Logging with TestAppTrait {

  def main(args: Array[String]): Unit = {

    if (args.length > 0) {
      DBs.setupAll()

      val applicationName = args(0)

      applicationName match {

        case "test_findForWebhooksV2" =>

          val emailScheduledIds = Seq(1112933L, 1112725L, 1112726L)
          val teamId = TeamId(id = 80)
          given Logger: SRLogger = new SRLogger("test_findForWebhooksV2")

          val result = emailScheduledDAOService.findForWebhooksV2(
            emailScheduledIds = emailScheduledIds,
            onlyNewReplies = false,
            teamId = teamId
          )
          println(result)
      }
    }

  }

}