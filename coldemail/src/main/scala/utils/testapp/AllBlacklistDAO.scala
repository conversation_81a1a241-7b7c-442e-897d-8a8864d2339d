package utils.testapp

import api.accounts.models.OrgId
import api.accounts.TeamId
import api.blacklist.models.BlacklistFindApiLevel

import play.api.Logging
import scalikejdbc.config.DBs
import utils.SRLogger


import scala.util.{Failure, Success}

object AllBlacklistDAO extends TestAppTrait with Logging {

  def main(args: Array[String]): Unit = {

    if (args.length > 0) {
      DBs.setupAll()

      val Logger = new SRLogger("test_hasOptedOutV2")

      val accountId = 140L
      val teamId = 132L
      val addedByName = "Parth Gupta"
      val taId = 249L


      val applicationName = args(0)

      applicationName match {

        case "test_findAll" =>

          println("starting test_findAll...")

          val result = blacklistDAO.findAll(
            SRLogger = Logger,
            level = BlacklistFindApiLevel.Team(team_id=TeamId(teamId),org_id=OrgId(123), ta_id = taId),
            searchKeyword = None
          )

          result match {
            case Failure(exception) =>
              println(exception.getMessage)

            case Success(value) =>
              println(value)
          }

          println("test_findAll ended")

        case "test__deleteBlacklistRowByEmail" =>
          val email: String = "<EMAIL>"

          println("starting test__deleteBlacklistRowByEmail...")

          val result = blacklistDAO._deleteBlacklistRowByEmail(
            email = email,
            teamId = teamId
          )

          println(result)

          println("test__deleteBlacklistRowByEmail ended")

        case "test__deleteBlacklistRows" =>

          val ids: Seq[Long] = Seq(66, 67)

          println("starting test__deleteBlacklistRows...")

          val result = blacklistDAO._deleteBlacklistRows(
            ids = ids,
            teamId = teamId
          )

          println(result)

          println("test__deleteBlacklistRows ended")

        case "test_checkBlacklistByDomains" =>
          val domains: Seq[String] = Seq("abc.com")

          println("starting test_checkBlacklistByDomains...")

          val result = blacklistDAO.checkBlacklistByDomains(
            teamId = teamId,
            domains = domains,
            org_id = 123
          )

          println(result)

          println("test_checkBlacklistByDomains ended")

        case "test__deleteBlacklistRowsByEmail" =>
          val emails: Seq[String] = Seq("<EMAIL>")

          println("starting test__deleteBlacklistRowsByEmail...")

          val result = blacklistDAO._deleteBlacklistRowsByEmail(emails, teamId)

          println(result)

          println("test__deleteBlacklistRowsByEmail ended")

        case "test_findBlacklistByEmails" =>
          val emails: Seq[String] = Seq("<EMAIL>")

          println("starting test_findBlacklistByEmails...")

          val result = blacklistDAO.findBlacklistByEmails(teamId, emails)

          println(result)

          println("test_findBlacklistByEmails ended")

        case "test_checkBlacklistById" =>
          val id: Long = 38

          println("starting test_checkBlacklistById...")

          val result = blacklistDAO.checkBlacklistByIds(teamId, Seq(id))

          println(result)

          println("test_checkBlacklistById ended")

        case "test_checkEmailAlreadyExistsInExceptionList" =>
          val emails: Seq[String] = Seq("<EMAIL>")

          println("starting test_checkEmailAlreadyExistsInExceptionList...")

          val result = blacklistDAO.checkEmailAlreadyExistsInExceptionList(teamId, emails)

          println(result)

          println("test_checkEmailAlreadyExistsInExceptionList ended")

      }
    }

  }

}