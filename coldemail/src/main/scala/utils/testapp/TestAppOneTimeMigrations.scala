//package utils.testapp
//
//import api.AppConfig
//import api.accounts.email.models.EmailServiceProvider
//import api.accounts.models.{AccountId, AccountStatus, AccountV2, OrgId}
//import api.accounts.{Account, AccountDAO, TeamId}
//import api.blacklist.Blacklist
//import api.blacklist.models.BlacklistLevel
//import api.campaigns.CampaignCreateForm
//import api.campaigns.models.CampaignType
//import api.campaigns.services.CampaignId
//import api.emails.models.DeletionReason
//import api.emails.{EmailSettingForm, ThreadAndTeamId}
//import api.gpt.CreateStepsRequest
//import api.internal_support.service.DeleteAccountError
//import api.migration_utils.MigrateNewEncryptionKey
//import api.prospects.dao.NewlyCreatedProspect
//import api.prospects.models.{ProspectCategory, ProspectCategoryId, ProspectCategoryUpdateFlow, ProspectId}
//import api.prospects.ProspectBasicForBatchUpdate
//import api.team_inbox.service.ReplySentimentUuid
//import org.apache.pekko.actor.ActorSystem
//import org.apache.pekko.stream.Materializer
//import org.joda.time.DateTime
//import play.api.Logging
//import play.api.libs.ws.ahc.AhcWSClient
//import scalikejdbc.config.DBs
//import scalikejdbc.{DB, DBSession, scalikejdbcSQLInterpolationImplicitDef}
//import utils.{Helpers, SRLogger}
//import utils.TestApp.logger
//import utils.customersupport.CustomerSupportUtils
//import utils.dependencyinjectionutils.*
//import utils.emailvalidation.EmailValidationService
//import utils.helpers.LogHelpers
//import utils.mq.email.AssociateProspectsData
//import utils.mq.{UpdateUuidMigrationData, UpdatedAtMigrationData, UuidMigrationData}
//import utils.testapp.TestAppTrait
//import utils.uuid.{SrUuidUtils, TableNames}
//
//import java.util.concurrent.Executors
//import scala.concurrent.{Await, ExecutionContext, ExecutionContextExecutor, Future}
//import scala.util.{Failure, Success, Try}
//
//
///*
//This object holds any sort internal test function used to test features/changes manually during development.
// */
//object TestAppOneTimeMigrations extends Logging {
//
//  def main(args: Array[String]): Unit = {
//
//    if (args.length > 0) {
//      DBs.setupAll()
//
//
//      val applicationName = args(0)
//
//      applicationName match {
//
//        case "UpdateProspectCategoryForTid_13595" =>
//
//          object main extends TestAppTrait {
//            def runUpdateFromDNCToGivenCategory(
//              orgId: OrgId,
//              teamId: TeamId,
//              ownerAccountId: AccountId,
//              prospectIds: Seq[Long],
//              emails: Seq[String],
//              permittedAccountIds: Seq[Long],
//              newProspectCategory: ProspectCategory.Value
//            )(using Logger: SRLogger) = {
//              val domains = emails.map { email =>
//                val (_, domain) = EmailValidationService.getLowercasedNameAndDomainFromEmail(email = email)
//                domain
//              }
//              val result = for {
//                ownerAccount: Account <- accountService.find(id = ownerAccountId.id)(Logger)
//
//                dnc_to_delete: Seq[Blacklist] <- blacklistDAO.checkBlacklistByDomains(
//                  teamId = teamId.id,
//                  domains = domains,
//                  org_id = orgId.id
//                ).map { list =>
//                  list.flatMap { domain =>
//                    if (domain.blacklist_level == BlacklistLevel.TeamLevel) {
//                      Seq(domain)
//                    } else Seq()
//                  }
//                }
//
//                deleteDNC <- Try {
//                  blacklistDAO._deleteBlacklistRows(
//                    ids = dnc_to_delete.map(_.id),
//                    teamId = teamId.id
//                  )
//                }
//
//                prospects: Seq[ProspectBasicForBatchUpdate] <- prospectServiceV2.fetchProspectsByIdsForBatchUpdate(
//                  prospectsIds = prospectIds,
//                  permittedAccountIds = permittedAccountIds,
//                  teamId = teamId.id,
//                  logger = Logger
//                )
//                updateToCategory: ProspectCategoryId <- prospectServiceV2.getProspectCategoryId(
//                  teamId = teamId,
//                  text_id = newProspectCategory,
//                  account = Some(ownerAccount)
//                )
//                updateCategory: Int <- prospectUpdateCategoryTemp.updateBatchCategory(
//                  prospects = prospects,
//                  doerAccountId = ownerAccountId.id,
//                  accountName = Helpers.getAccountName(a = ownerAccount),
//                  teamId = teamId.id,
//                  taId = ownerAccount.teams.find(_.team_id == teamId.id).get.access_members.find(_.user_id == ownerAccountId.id).get.ta_id,
//                  prospectCategoryUpdateFlow = ProspectCategoryUpdateFlow.AdminUpdate(
//                    old_prospect_category_id = None,
//                    new_prospect_category_id = updateToCategory
//                  ),
//                  account = ownerAccount,
//                  logger = Logger,
//                  auditRequestLogId = None
//                )
//
//              } yield updateCategory
//
//              result match {
//                case Success(value) => Logger.info(s"Updated category for tid_${teamId.id} prospects - $prospectIds")
//                case Failure(exception) => Logger.fatal(s"Failed to update category for tid_${teamId.id} prospects - $prospectIds", exception)
//              }
//            }
//
//            def init() = {
//
//              given Logger: SRLogger = new SRLogger("UpdateProspectCategoryForTid_13595")
//              val orgId = OrgId(4443)
//              val teamId = TeamId(13595)
//              val ownerAccountId = AccountId(5221)
//              val interestedProspectIds: Seq[Long] = Seq(
//                **********, **********, **********, **********, **********, **********, **********, **********,
//                **********, **********, **********, **********, **********, **********, **********, **********,
//                **********, **********, **********, **********, **********, **********, **********, **********,
//                **********, **********, **********, **********, **********, **********, **********, **********,
//                **********, **********, **********, **********, **********, **********, **********, **********,
//                **********, **********, **********, **********, **********, **********, **********, **********,
//                **********, **********, **********, **********, **********, **********, **********, **********,
//                **********, **********, **********, **********, **********, **********, **********, **********,
//                1731519310, 1728257366, 1728258459, 1734608427, 1729198907, 1728812689, 1729198029, 1735016431,
//                1728257433)
//              val interestedEmails = Seq(
//                "<EMAIL>", "<EMAIL>", "<EMAIL>",
//                "<EMAIL>", "<EMAIL>", "<EMAIL>",
//                "<EMAIL>", "<EMAIL>", "<EMAIL>",
//                "<EMAIL>", "<EMAIL>", "<EMAIL>",
//                "<EMAIL>", "<EMAIL>", "<EMAIL>",
//                "<EMAIL>", "<EMAIL>", "<EMAIL>",
//                "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
//                "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
//                "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
//                "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
//                "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
//                "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
//                "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
//                "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
//                "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
//                "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
//                "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
//                "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
//                "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
//                "<EMAIL>", "<EMAIL>"
//              )
//
//              runUpdateFromDNCToGivenCategory(
//                orgId = orgId,
//                teamId = teamId,
//                ownerAccountId = ownerAccountId,
//                prospectIds = interestedProspectIds,
//                emails = interestedEmails,
//                permittedAccountIds = Seq(5221, 7718, 12877, 14730, 14733, 14764, 14898, 15402, 15510, 16561, 17450, 18184, 18196),
//                newProspectCategory = ProspectCategory.INTERESTED
//              )
//
//              val notNowProspectIds: Seq[Long] = Seq(
//                **********, **********, **********,
//                **********, **********, **********,
//                **********, **********, **********,
//                **********, **********, **********,
//                **********, **********, **********,
//                **********, **********, **********
//              )
//              val notNowEmails = Seq(
//                "<EMAIL> ", "<EMAIL>", "<EMAIL>",
//                "<EMAIL>", "<EMAIL>", "<EMAIL>",
//                "<EMAIL>", "<EMAIL>", "<EMAIL>",
//                "<EMAIL>", "<EMAIL>", "<EMAIL>",
//                "<EMAIL>", "<EMAIL>", "<EMAIL>",
//                "<EMAIL>", "<EMAIL>", "<EMAIL>"
//              )
//
//              runUpdateFromDNCToGivenCategory(
//                orgId = orgId,
//                teamId = teamId,
//                ownerAccountId = ownerAccountId,
//                prospectIds = notNowProspectIds,
//                emails = notNowEmails,
//                permittedAccountIds = Seq(5221, 7718, 12877, 14730, 14733, 14764, 14898, 15402, 15510, 16561, 17450, 18184, 18196),
//                newProspectCategory = ProspectCategory.NOT_NOW
//              )
//
//              val notCategorisedProspectIds: Seq[Long] = Seq(
//                **********, **********, **********,
//                **********, **********, **********,
//                **********, **********, **********,
//                **********
//              )
//              val notCategorisedEmails = Seq(
//                "<EMAIL>", "<EMAIL>", "<EMAIL>",
//                "<EMAIL>", "<EMAIL>", "<EMAIL>",
//                "<EMAIL>", "<EMAIL>", "<EMAIL>",
//                "<EMAIL>"
//              )
//
//              runUpdateFromDNCToGivenCategory(
//                orgId = orgId,
//                teamId = teamId,
//                ownerAccountId = ownerAccountId,
//                prospectIds = notCategorisedProspectIds,
//                emails = notCategorisedEmails,
//                permittedAccountIds = Seq(5221, 7718, 12877, 14730, 14733, 14764, 14898, 15402, 15510, 16561, 17450, 18184, 18196),
//                newProspectCategory = ProspectCategory.NOT_CATEGORIZED
//              )
//
//            }
//          }
//          main.init()
//
//        case "UpdateProspectCategoryFor_orgId_7593" =>
//
//          object main extends TestAppTrait {
//            def runUpdateFromDNCToGivenCategory(
//              orgId: OrgId,
//              teamId: TeamId,
//              ownerAccountId: AccountId,
//              domains: Seq[String],
//              permittedAccountIds: Seq[Long],
//              newProspectCategory: ProspectCategory.Value,
//              oldProspectCategory: ProspectCategory.Value
//            )(using Logger: SRLogger) = {
//
//              val result = for {
//                ownerAccount: Account <- accountService.find(id = ownerAccountId.id)(Logger)
//
//                dnc_to_delete: Seq[Blacklist] <- blacklistDAO.checkBlacklistByDomains(
//                  teamId = teamId.id,
//                  domains = domains,
//                  org_id = orgId.id
//                ).map { list =>
//                  list.flatMap { domain =>
//                    if (domain.blacklist_level == BlacklistLevel.TeamLevel) {
//                      Seq(domain)
//                    } else Seq()
//                  }
//                }
//
//                deleteDNC <- Try {
//                  blacklistDAO._deleteBlacklistRows(
//                    ids = dnc_to_delete.map(_.id),
//                    teamId = teamId.id
//                  )
//                }
//                updateFromCategory: ProspectCategoryId <- prospectServiceV2.getProspectCategoryId(
//                  teamId = teamId,
//                  text_id = oldProspectCategory,
//                  account = Some(ownerAccount)
//                )
//                prospectIds: List[Long] <- Try {
//                  DB readOnly { implicit session =>
//
//                    sql"""
//                         select pe.prospect_id from prospects_emails pe
//                         inner join prospects p on p.id = pe.prospect_id and p.team_id = pe.team_id
//                          where
//                          pe.email_domain in (${domains})
//                          and pe.team_id = ${teamId.id}
//                          AND p.prospect_category_id_custom = ${updateFromCategory.id}
//                       """
//                      .map(rs => rs.long("prospect_id"))
//                      .list
//                      .apply()
//                  }
//                }
//                prospects: Seq[ProspectBasicForBatchUpdate] <- prospectServiceV2.fetchProspectsByIdsForBatchUpdate(
//                  prospectsIds = prospectIds,
//                  permittedAccountIds = permittedAccountIds,
//                  teamId = teamId.id,
//                  logger = Logger
//                )
//                updateToCategory: ProspectCategoryId <- prospectServiceV2.getProspectCategoryId(
//                  teamId = teamId,
//                  text_id = newProspectCategory,
//                  account = Some(ownerAccount)
//                )
//                updateCategory: Int <- prospectUpdateCategoryTemp.updateBatchCategory(
//                  prospects = prospects,
//                  doerAccountId = ownerAccountId.id,
//                  accountName = Helpers.getAccountName(a = ownerAccount),
//                  teamId = teamId.id,
//                  taId = ownerAccount.teams.find(_.team_id == teamId.id).get.access_members.find(_.user_id == ownerAccountId.id).get.ta_id,
//                  prospectCategoryUpdateFlow = ProspectCategoryUpdateFlow.AdminUpdate(
//                    old_prospect_category_id = None,
//                    new_prospect_category_id = updateToCategory
//                  ),
//                  account = ownerAccount,
//                  logger = Logger,
//                  auditRequestLogId = None
//                )
//
//              } yield {
//                Logger.info(s"Updated category for tid_${teamId.id} from prospect_category_id_custom $oldProspectCategory($updateFromCategory) to $newProspectCategory($updateToCategory) prospectIds - $prospectIds")
//                updateCategory
//              }
//
//              result match {
//                case Success(value) => Logger.info(s"Updated category for tid_${teamId.id} domains - $domains")
//                case Failure(exception) => Logger.fatal(s"Failed to update category for tid_${teamId.id} domains - $domains", exception)
//              }
//            }
//
//            def runForAllDomains(
//              orgId: OrgId,
//              teamId: TeamId,
//              ownerAccountId: AccountId,
//              permittedAccountIds: Seq[Long],
//              newProspectCategory: ProspectCategory.Value,
//              oldProspectCategory: ProspectCategory.Value,
//              count: Int
//            )(using Logger: SRLogger): Success[Long] = {
//
//              val domains = DB readOnly { implicit session =>
//                sql"""
//                     select name from blacklist
//                     where team_id = ${teamId.id}
//                     and do_not_contact_type = 'domain'
//                     limit 100
//                   """
//                  .map(rs => rs.string("name"))
//                  .list
//                  .apply()
//              }
//
//              if (domains.isEmpty) {
//                Success(0)
//              } else {
//                runUpdateFromDNCToGivenCategory(
//                  orgId = orgId,
//                  teamId = teamId,
//                  ownerAccountId = ownerAccountId,
//                  domains = domains,
//                  permittedAccountIds = permittedAccountIds,
//                  newProspectCategory = newProspectCategory,
//                  oldProspectCategory = oldProspectCategory
//                )
//                runForAllDomains(
//                  orgId = orgId,
//                  teamId = teamId,
//                  ownerAccountId = ownerAccountId,
//                  permittedAccountIds = permittedAccountIds,
//                  newProspectCategory = newProspectCategory,
//                  oldProspectCategory = oldProspectCategory,
//                  count = count + domains.length
//                )
//              }
//
//            }
//
//            def init() = {
//
//              given Logger: SRLogger = new SRLogger("UpdateProspectCategoryFor_orgId_7593")
//              val orgId = OrgId(7593)
//              val ownerAccountId = AccountId(8909)
//
//              case class TeamMember(
//                team_id: Long,
//                account_id: Long
//              )
//              val data = List(
//                TeamMember(team_id = 8450, account_id = 15099),
//                TeamMember(team_id = 8450, account_id = 10549),
//                TeamMember(team_id = 8450, account_id = 13490),
//                TeamMember(team_id = 8450, account_id = 10548),
//                TeamMember(team_id = 8450, account_id = 10550),
//                TeamMember(team_id = 8450, account_id = 12769),
//                TeamMember(team_id = 8450, account_id = 10547),
//                TeamMember(team_id = 8450, account_id = 8909),
//                TeamMember(team_id = 8450, account_id = 14093),
//                TeamMember(team_id = 8450, account_id = 15100),
//                TeamMember(team_id = 8450, account_id = 15413),
//                TeamMember(team_id = 8450, account_id = 15098),
//                TeamMember(team_id = 8451, account_id = 13101),
//                TeamMember(team_id = 8451, account_id = 13023),
//                TeamMember(team_id = 8451, account_id = 13006),
//                TeamMember(team_id = 8451, account_id = 8910),
//                TeamMember(team_id = 8451, account_id = 8911),
//                TeamMember(team_id = 8451, account_id = 13024),
//                TeamMember(team_id = 8451, account_id = 8909),
//                TeamMember(team_id = 8451, account_id = 8912),
//                TeamMember(team_id = 8536, account_id = 9211),
//                TeamMember(team_id = 8536, account_id = 8909),
//                TeamMember(team_id = 8536, account_id = 8999),
//                TeamMember(team_id = 8664, account_id = 9145),
//                TeamMember(team_id = 8664, account_id = 9149),
//                TeamMember(team_id = 8664, account_id = 8909),
//                TeamMember(team_id = 8955, account_id = 11558),
//                TeamMember(team_id = 8955, account_id = 8909),
//                TeamMember(team_id = 8955, account_id = 11557),
//                TeamMember(team_id = 8955, account_id = 9443),
//                TeamMember(team_id = 8955, account_id = 9442),
//                TeamMember(team_id = 9046, account_id = 9534),
//                TeamMember(team_id = 9046, account_id = 8909),
//                TeamMember(team_id = 9046, account_id = 9535),
//                TeamMember(team_id = 9047, account_id = 9536),
//                TeamMember(team_id = 9047, account_id = 8909),
//                TeamMember(team_id = 9047, account_id = 9537),
//                TeamMember(team_id = 9048, account_id = 9538),
//                TeamMember(team_id = 9048, account_id = 8909),
//                TeamMember(team_id = 9048, account_id = 9539),
//                TeamMember(team_id = 9070, account_id = 9565),
//                TeamMember(team_id = 9070, account_id = 8909),
//                TeamMember(team_id = 9070, account_id = 9566),
//                TeamMember(team_id = 9188, account_id = 9706),
//                TeamMember(team_id = 9188, account_id = 8909),
//                TeamMember(team_id = 9188, account_id = 9707),
//                TeamMember(team_id = 9267, account_id = 8909),
//                TeamMember(team_id = 9267, account_id = 9797),
//                TeamMember(team_id = 9267, account_id = 9796),
//                TeamMember(team_id = 9324, account_id = 8909),
//                TeamMember(team_id = 9324, account_id = 9909),
//                TeamMember(team_id = 9324, account_id = 9908),
//                TeamMember(team_id = 9324, account_id = 16634),
//                TeamMember(team_id = 9436, account_id = 10643),
//                TeamMember(team_id = 9436, account_id = 17207),
//                TeamMember(team_id = 9436, account_id = 10644),
//                TeamMember(team_id = 9436, account_id = 8909),
//                TeamMember(team_id = 9436, account_id = 10035),
//                TeamMember(team_id = 9436, account_id = 10034),
//                TeamMember(team_id = 9633, account_id = 11839),
//                TeamMember(team_id = 9633, account_id = 10250),
//                TeamMember(team_id = 9633, account_id = 10584),
//                TeamMember(team_id = 9633, account_id = 10249),
//                TeamMember(team_id = 9633, account_id = 8909),
//                TeamMember(team_id = 9819, account_id = 10440),
//                TeamMember(team_id = 9819, account_id = 10439),
//                TeamMember(team_id = 9819, account_id = 8909),
//                TeamMember(team_id = 9830, account_id = 8909),
//                TeamMember(team_id = 9830, account_id = 10455),
//                TeamMember(team_id = 9830, account_id = 10454),
//                TeamMember(team_id = 9830, account_id = 10453),
//                TeamMember(team_id = 9830, account_id = 10452),
//                TeamMember(team_id = 9855, account_id = 8909),
//                TeamMember(team_id = 9855, account_id = 10483),
//                TeamMember(team_id = 9855, account_id = 10482),
//                TeamMember(team_id = 9859, account_id = 8909),
//                TeamMember(team_id = 9859, account_id = 10489),
//                TeamMember(team_id = 9859, account_id = 10488),
//                TeamMember(team_id = 9865, account_id = 10498),
//                TeamMember(team_id = 9865, account_id = 8909),
//                TeamMember(team_id = 9865, account_id = 10497),
//                TeamMember(team_id = 9882, account_id = 8909),
//                TeamMember(team_id = 9882, account_id = 11753),
//                TeamMember(team_id = 9882, account_id = 10521),
//                TeamMember(team_id = 9882, account_id = 10522),
//                TeamMember(team_id = 9882, account_id = 10523),
//                TeamMember(team_id = 9882, account_id = 10527),
//                TeamMember(team_id = 9882, account_id = 11754),
//                TeamMember(team_id = 9903, account_id = 10570),
//                TeamMember(team_id = 9903, account_id = 10569),
//                TeamMember(team_id = 9903, account_id = 8909),
//                TeamMember(team_id = 9904, account_id = 10555),
//                TeamMember(team_id = 9904, account_id = 10554),
//                TeamMember(team_id = 9904, account_id = 8909),
//                TeamMember(team_id = 9919, account_id = 8909),
//                TeamMember(team_id = 9919, account_id = 10568),
//                TeamMember(team_id = 9919, account_id = 10567),
//                TeamMember(team_id = 9932, account_id = 10586),
//                TeamMember(team_id = 9932, account_id = 10585),
//                TeamMember(team_id = 9932, account_id = 8909),
//                TeamMember(team_id = 10056, account_id = 10741),
//                TeamMember(team_id = 10056, account_id = 10742),
//                TeamMember(team_id = 10056, account_id = 11445),
//                TeamMember(team_id = 10056, account_id = 10740),
//                TeamMember(team_id = 10056, account_id = 8909),
//                TeamMember(team_id = 10075, account_id = 8909),
//                TeamMember(team_id = 10075, account_id = 10764),
//                TeamMember(team_id = 10075, account_id = 10763),
//                TeamMember(team_id = 10075, account_id = 11158),
//                TeamMember(team_id = 10076, account_id = 10816),
//                TeamMember(team_id = 10076, account_id = 8909),
//                TeamMember(team_id = 10076, account_id = 10817),
//                TeamMember(team_id = 10093, account_id = 10783),
//                TeamMember(team_id = 10093, account_id = 10785),
//                TeamMember(team_id = 10093, account_id = 8909),
//                TeamMember(team_id = 10093, account_id = 10784),
//                TeamMember(team_id = 10302, account_id = 8909),
//                TeamMember(team_id = 10302, account_id = 11043),
//                TeamMember(team_id = 10302, account_id = 11022),
//                TeamMember(team_id = 10321, account_id = 11111),
//                TeamMember(team_id = 10321, account_id = 8909),
//                TeamMember(team_id = 10321, account_id = 11042),
//                TeamMember(team_id = 10355, account_id = 11085),
//                TeamMember(team_id = 10355, account_id = 11786),
//                TeamMember(team_id = 10355, account_id = 11785),
//                TeamMember(team_id = 10355, account_id = 8909),
//                TeamMember(team_id = 10355, account_id = 11086),
//                TeamMember(team_id = 10417, account_id = 11264),
//                TeamMember(team_id = 10417, account_id = 8909),
//                TeamMember(team_id = 10417, account_id = 11159),
//                TeamMember(team_id = 10417, account_id = 11160),
//                TeamMember(team_id = 10417, account_id = 11161),
//                TeamMember(team_id = 10417, account_id = 11162),
//                TeamMember(team_id = 10417, account_id = 11163),
//                TeamMember(team_id = 10417, account_id = 11164),
//                TeamMember(team_id = 10417, account_id = 11165),
//                TeamMember(team_id = 10417, account_id = 11263),
//                TeamMember(team_id = 10417, account_id = 11265),
//                TeamMember(team_id = 10651, account_id = 11427),
//                TeamMember(team_id = 10651, account_id = 8909),
//                TeamMember(team_id = 10651, account_id = 11426),
//                TeamMember(team_id = 10875, account_id = 11904),
//                TeamMember(team_id = 10875, account_id = 11903),
//                TeamMember(team_id = 10875, account_id = 11662),
//                TeamMember(team_id = 10875, account_id = 11661),
//                TeamMember(team_id = 10875, account_id = 8909),
//                TeamMember(team_id = 11175, account_id = 12497),
//                TeamMember(team_id = 11175, account_id = 12010),
//                TeamMember(team_id = 11175, account_id = 12003),
//                TeamMember(team_id = 11175, account_id = 17931),
//                TeamMember(team_id = 11175, account_id = 8909),
//                TeamMember(team_id = 11175, account_id = 15751),
//                TeamMember(team_id = 11175, account_id = 12496),
//                TeamMember(team_id = 11175, account_id = 13204),
//                TeamMember(team_id = 11338, account_id = 12378),
//                TeamMember(team_id = 11338, account_id = 12376),
//                TeamMember(team_id = 11338, account_id = 8909),
//                TeamMember(team_id = 11878, account_id = 8909),
//                TeamMember(team_id = 11878, account_id = 12808),
//                TeamMember(team_id = 11878, account_id = 12809),
//                TeamMember(team_id = 11957, account_id = 8909),
//                TeamMember(team_id = 11957, account_id = 12902),
//                TeamMember(team_id = 11957, account_id = 12940),
//                TeamMember(team_id = 11958, account_id = 12903),
//                TeamMember(team_id = 11958, account_id = 12904),
//                TeamMember(team_id = 11958, account_id = 8909),
//                TeamMember(team_id = 11990, account_id = 12938),
//                TeamMember(team_id = 11990, account_id = 12939),
//                TeamMember(team_id = 11990, account_id = 8909),
//                TeamMember(team_id = 12050, account_id = 8909),
//                TeamMember(team_id = 12050, account_id = 13177),
//                TeamMember(team_id = 12050, account_id = 13176),
//                TeamMember(team_id = 12050, account_id = 13065),
//                TeamMember(team_id = 12050, account_id = 13007),
//                TeamMember(team_id = 12354, account_id = 13375),
//                TeamMember(team_id = 12354, account_id = 8909),
//                TeamMember(team_id = 12354, account_id = 13376),
//                TeamMember(team_id = 12450, account_id = 13482),
//                TeamMember(team_id = 12450, account_id = 8909),
//                TeamMember(team_id = 12450, account_id = 13479),
//                TeamMember(team_id = 12821, account_id = 13868),
//                TeamMember(team_id = 12821, account_id = 13869),
//                TeamMember(team_id = 12821, account_id = 8909),
//                TeamMember(team_id = 12936, account_id = 14005),
//                TeamMember(team_id = 12936, account_id = 14006),
//                TeamMember(team_id = 12936, account_id = 8909),
//                TeamMember(team_id = 12936, account_id = 14007),
//                TeamMember(team_id = 12936, account_id = 14004),
//                TeamMember(team_id = 13265, account_id = 8909),
//                TeamMember(team_id = 13265, account_id = 14376),
//                TeamMember(team_id = 13265, account_id = 14377),
//                TeamMember(team_id = 13872, account_id = 8909),
//                TeamMember(team_id = 13872, account_id = 15037),
//                TeamMember(team_id = 13872, account_id = 15040),
//                TeamMember(team_id = 13872, account_id = 15036),
//                TeamMember(team_id = 14118, account_id = 8909),
//                TeamMember(team_id = 14118, account_id = 15298),
//                TeamMember(team_id = 14118, account_id = 15794),
//                TeamMember(team_id = 14118, account_id = 16734),
//                TeamMember(team_id = 14118, account_id = 15297),
//                TeamMember(team_id = 14118, account_id = 16644),
//                TeamMember(team_id = 14118, account_id = 18318),
//                TeamMember(team_id = 14118, account_id = 16645),
//                TeamMember(team_id = 14188, account_id = 8909),
//                TeamMember(team_id = 14188, account_id = 15383),
//                TeamMember(team_id = 14188, account_id = 15384),
//                TeamMember(team_id = 14287, account_id = 15495),
//                TeamMember(team_id = 14287, account_id = 8909),
//                TeamMember(team_id = 14287, account_id = 15494),
//                TeamMember(team_id = 14290, account_id = 8909),
//                TeamMember(team_id = 14290, account_id = 15530),
//                TeamMember(team_id = 14290, account_id = 15531),
//                TeamMember(team_id = 14406, account_id = 15622),
//                TeamMember(team_id = 14406, account_id = 15624),
//                TeamMember(team_id = 14406, account_id = 8909),
//                TeamMember(team_id = 14406, account_id = 16328),
//                TeamMember(team_id = 14867, account_id = 8909),
//                TeamMember(team_id = 14867, account_id = 16138),
//                TeamMember(team_id = 14867, account_id = 16137),
//                TeamMember(team_id = 15107, account_id = 16403),
//                TeamMember(team_id = 15107, account_id = 16400),
//                TeamMember(team_id = 15107, account_id = 8909),
//                TeamMember(team_id = 15428, account_id = 16832),
//                TeamMember(team_id = 15428, account_id = 18486),
//                TeamMember(team_id = 15428, account_id = 16737),
//                TeamMember(team_id = 15428, account_id = 8909),
//                TeamMember(team_id = 15767, account_id = 8909),
//                TeamMember(team_id = 15767, account_id = 17094),
//                TeamMember(team_id = 15767, account_id = 17095),
//                TeamMember(team_id = 15906, account_id = 17243),
//                TeamMember(team_id = 15906, account_id = 8909),
//                TeamMember(team_id = 15906, account_id = 17244),
//                TeamMember(team_id = 15960, account_id = 17304),
//                TeamMember(team_id = 15960, account_id = 20104),
//                TeamMember(team_id = 15960, account_id = 17443),
//                TeamMember(team_id = 15960, account_id = 8909),
//                TeamMember(team_id = 15960, account_id = 17303),
//                TeamMember(team_id = 15990, account_id = 17354),
//                TeamMember(team_id = 15990, account_id = 19650),
//                TeamMember(team_id = 15990, account_id = 19649),
//                TeamMember(team_id = 15990, account_id = 17355),
//                TeamMember(team_id = 15990, account_id = 17818),
//                TeamMember(team_id = 15990, account_id = 8909),
//                TeamMember(team_id = 15990, account_id = 18039),
//                TeamMember(team_id = 15990, account_id = 18038),
//                TeamMember(team_id = 15991, account_id = 17356),
//                TeamMember(team_id = 15991, account_id = 8909),
//                TeamMember(team_id = 16220, account_id = 8909),
//                TeamMember(team_id = 16220, account_id = 17930),
//                TeamMember(team_id = 16220, account_id = 17673),
//                TeamMember(team_id = 16220, account_id = 17654),
//                TeamMember(team_id = 16410, account_id = 8909),
//                TeamMember(team_id = 16410, account_id = 17786),
//                TeamMember(team_id = 16410, account_id = 17785),
//                TeamMember(team_id = 16503, account_id = 17907),
//                TeamMember(team_id = 16503, account_id = 8909),
//                TeamMember(team_id = 16503, account_id = 17906),
//                TeamMember(team_id = 17014, account_id = 18414),
//                TeamMember(team_id = 17014, account_id = 8909),
//                TeamMember(team_id = 17014, account_id = 18428),
//                TeamMember(team_id = 17224, account_id = 8909),
//                TeamMember(team_id = 17224, account_id = 18625),
//                TeamMember(team_id = 17228, account_id = 18629),
//                TeamMember(team_id = 17228, account_id = 18630),
//                TeamMember(team_id = 17228, account_id = 19856),
//                TeamMember(team_id = 17228, account_id = 8909),
//                TeamMember(team_id = 17659, account_id = 8909),
//                TeamMember(team_id = 17659, account_id = 19063),
//                TeamMember(team_id = 17659, account_id = 20064),
//                TeamMember(team_id = 17809, account_id = 8909),
//                TeamMember(team_id = 17809, account_id = 19214),
//                TeamMember(team_id = 17809, account_id = 19217),
//                TeamMember(team_id = 17972, account_id = 19382),
//                TeamMember(team_id = 17972, account_id = 19383),
//                TeamMember(team_id = 17972, account_id = 8909),
//                TeamMember(team_id = 17973, account_id = 8909),
//                TeamMember(team_id = 17973, account_id = 19418),
//                TeamMember(team_id = 17973, account_id = 19417),
//                TeamMember(team_id = 18244, account_id = 8909),
//                TeamMember(team_id = 18244, account_id = 20725),
//                TeamMember(team_id = 18244, account_id = 19701),
//                TeamMember(team_id = 18244, account_id = 19699),
//                TeamMember(team_id = 18244, account_id = 20728),
//                TeamMember(team_id = 19124, account_id = 8909),
//                TeamMember(team_id = 19124, account_id = 20627),
//                TeamMember(team_id = 19124, account_id = 20628),
//                TeamMember(team_id = 19476, account_id = 21081),
//                TeamMember(team_id = 19476, account_id = 21032),
//                TeamMember(team_id = 19476, account_id = 8909),
//                TeamMember(team_id = 19476, account_id = 20998),
//                TeamMember(team_id = 19529, account_id = 8909),
//                TeamMember(team_id = 19529, account_id = 21058),
//                TeamMember(team_id = 19529, account_id = 21054)
//              )
//              data
//                .groupBy(_.team_id)
//                .map { grouped =>
//                  val (teamId, teamMember) = grouped
//                  runForAllDomains(
//                    orgId = orgId,
//                    teamId = TeamId(teamId),
//                    ownerAccountId = ownerAccountId,
//                    permittedAccountIds = teamMember.map(_.account_id),
//                    newProspectCategory = ProspectCategory.NOT_CATEGORIZED,
//                    oldProspectCategory = ProspectCategory.DO_NOT_CONTACT,
//                    count = 0
//                  )
//                }
//            }
//          }
//          main.init()
//
//
//
//        /*
//                case "drip_campaign_teamId_migration" =>
//                  val migration_teamId = 6412
//                  val count_in_prospects = DB autoCommit { implicit session =>
//                    sql"""
//                        SELECT count(*) from prospects where team_id = $migration_teamId;
//                       """
//                      .map(rs=> rs.int("count"))
//                      .single.apply()
//                  }
//
//                  val prospects_emails_list = DB autoCommit { implicit session =>
//                    sql"""
//                        INSERT INTO prospects_emails (email, prospect_id, team_id, account_id, email_format_valid, email_checked, email_checked_at,
//                         invalid_email, email_validation_id, email_domain, email_sent_for_validation, email_sent_for_validation_at, email_bounced,
//                         email_bounced_campaign, email_bounced_at, force_send_invalid_email, force_send_invalid_email_at, email_bounced_earlier,
//                         email_bounce_type, email_bounce_reason, created_at, updated_at)
//
//                         SELECT email, id, team_id, account_id, email_format_valid, email_checked, email_checked_at,
//                         invalid_email, email_validation_id, email_domain, email_sent_for_validation, email_sent_for_validation_at, email_bounced,
//                         email_bounced_campaign, email_bounced_at, force_send_invalid_email, force_send_invalid_email_at, email_bounced_earlier,
//                         email_bounce_type, email_bounce_reason, created_at, updated_at FROM prospects
//                         WHERE team_id = $migration_teamId
//
//                         ON CONFLICT(team_id, prospect_id, lower(email)) DO UPDATE SET
//
//                         email = excluded.email, prospect_id = excluded.prospect_id , team_id = excluded.team_id, account_id = excluded.account_id,
//                         email_format_valid = excluded.email_format_valid, email_checked = excluded.email_checked, email_checked_at = excluded.email_checked_at,
//                         invalid_email = excluded.invalid_email, email_validation_id = excluded.email_validation_id, email_domain = excluded.email_domain,
//                         email_sent_for_validation = excluded.email_sent_for_validation, email_sent_for_validation_at = excluded.email_sent_for_validation_at,
//                         email_bounced = excluded.email_bounced, email_bounced_campaign = excluded.email_bounced_campaign, email_bounced_at = excluded.email_bounced_at,
//                         force_send_invalid_email = excluded.force_send_invalid_email, force_send_invalid_email_at = excluded.force_send_invalid_email_at,
//                         email_bounced_earlier = excluded.email_bounced_earlier, email_bounce_type = excluded.email_bounce_type,
//                         email_bounce_reason = excluded.email_bounce_reason, created_at = excluded.created_at, updated_at = excluded.updated_at
//
//                         RETURNING id;
//                      """
//                      .map(rs => rs.long("id"))
//                      .list.apply()
//                  }
//
//
//                  DB autoCommit { implicit session =>
//                    sql"""
//                        UPDATE prospects_emails
//                        SET is_primary = true where team_id = $migration_teamId;
//                      """
//                      .update.apply()
//                  }
//
//                  val count_in_prospects_emails = DB autoCommit { implicit session =>
//                    sql"""
//                       SELECT count(*) from prospects_emails where team_id = $migration_teamId;
//                     """
//                      .map(rs=> rs.int("count"))
//                      .single.apply()
//                  }
//
//                  val logger = new SRLogger("prospects_emails migration")
//
//                  logger.info(s"prospects_count: $count_in_prospects \n migrated count: ${prospects_emails_list.length} \n prospects_emails_count $count_in_prospects_emails")
//        */
//
//
//
//        case "EmailSendStatusMigration" =>
//        /*
//          val active = true //make false for second go
//          val email_send_status = SendEmailStatus.Allowed //make Blocked for second go
//
//            val orgToBeAddedInThisCycle = DB readOnly(implicit session =>{
//              sql"""
//        SELECT
//          organizations.id,
//          organizations.name,
//          organizations.owner_account_id,
//          organizations.trial_ends_at,
//          organizations.plan_type,
//          organizations.plan_name,
//          organizations.additional_spam_tests,
//          organizations.metadata
//        FROM organizations
//          WHERE organizations.active = $active
//            ORDER BY id
//
//       """
//                .map(Organization.fromDb)
//                .list
//                .apply()
//            })
//
//
//
//            if (orgToBeAddedInThisCycle.isEmpty) {
//              logger.info("Nothing to add")
//            } else {
//              var valueParameters = List[Any]()
//              val valuePlaceholder: SQLSyntax = orgToBeAddedInThisCycle
//                .map(org => {
//
//                  valueParameters = valueParameters ::: List(
//                    EmailSendingEntityType.Organization(1L).toString,
//                    org.id,
//                    email_send_status.toString
//                  )
//
//                  sqls"""
//                  (
//                    ?,
//                    ?,
//                    ?
//                   )
//                  """
//
//                })
//                .reduce((vp1, vp2) => sqls"$vp1, $vp2")
//
//
//              val email_status_added = DB autoCommit { implicit session =>
//                sql"""
//                INSERT INTO email_send_statuses (entity_type, org_id, email_send_status)
//
//                 SELECT entity_type, org_id, email_send_status FROM (VALUES $valuePlaceholder)
//                 AS org(entity_type, org_id, email_send_status)
//                 returning id
//              """
//                  .bind(valueParameters*)
//                  .map(rs => rs.long("id"))
//                  .list.apply()
//              }
//
//              logger.info(s"email_status_added - ${email_status_added.length}")
//
//
//            }
//
//          */
//
//
//        /*
//        case "adding_email_settings_bulk" =>
//
//          case class BulkAddEmailData(
//            email: String,
//            first_name: String,
//            last_name: String,
//            imap_username: String,
//            imap_host: String,
//            imap_port: Int,
//            smtp_username: String,
//            smtp_host: String,
//            smtp_port: Int,
//            daily_limit: Int
//          )
//          implicit val system: ActorSystem = ActorSystem()
//          implicit val  materializer = Materializer(system)
//          given logger: SRLogger = new SRLogger("TestApp : adding_email_settings_bulk ")
//
//          val data_from_sheet = List(
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.co.uk",993,"<EMAIL>","mail.supervisioninschools.co.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.co.uk",993,"<EMAIL>","mail.supervisioninschools.co.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.co.uk",993,"<EMAIL>","mail.supervisioninschools.co.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.co.uk",993,"<EMAIL>","mail.supervisioninschools.co.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.co.uk",993,"<EMAIL>","mail.supervisioninschools.co.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.co.uk",993,"<EMAIL>","mail.supervisioninschools.co.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.co.uk",993,"<EMAIL>","mail.supervisioninschools.co.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.co.uk",993,"<EMAIL>","mail.supervisioninschools.co.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.org",993,"<EMAIL>","mail.supervisioninschool.org",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.org",993,"<EMAIL>","mail.supervisioninschool.org",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.org",993,"<EMAIL>","mail.supervisioninschool.org",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.org",993,"<EMAIL>","mail.supervisioninschool.org",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.org",993,"<EMAIL>","mail.supervisioninschool.org",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.org",993,"<EMAIL>","mail.supervisioninschool.org",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.org",993,"<EMAIL>","mail.supervisioninschool.org",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.org",993,"<EMAIL>","mail.supervisioninschool.org",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.org",993,"<EMAIL>","mail.supervisioninschool.org",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.org",993,"<EMAIL>","mail.supervisioninschool.org",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.org",993,"<EMAIL>","mail.supervisioninschool.org",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.org.uk",993,"<EMAIL>","mail.supervisioninschool.org.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.org.uk",993,"<EMAIL>","mail.supervisioninschool.org.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.org.uk",993,"<EMAIL>","mail.supervisioninschool.org.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.org.uk",993,"<EMAIL>","mail.supervisioninschool.org.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.org.uk",993,"<EMAIL>","mail.supervisioninschool.org.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.org.uk",993,"<EMAIL>","mail.supervisioninschool.org.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.org.uk",993,"<EMAIL>","mail.supervisioninschool.org.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.org.uk",993,"<EMAIL>","mail.supervisioninschool.org.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.org.uk",993,"<EMAIL>","mail.supervisioninschool.org.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.org.uk",993,"<EMAIL>","mail.supervisioninschool.org.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.org.uk",993,"<EMAIL>","mail.supervisioninschool.org.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.net",993,"<EMAIL>","mail.supervisioninschool.net",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.net",993,"<EMAIL>","mail.supervisioninschool.net",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.net",993,"<EMAIL>","mail.supervisioninschool.net",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.net",993,"<EMAIL>","mail.supervisioninschool.net",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.net",993,"<EMAIL>","mail.supervisioninschool.net",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.net",993,"<EMAIL>","mail.supervisioninschool.net",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.net",993,"<EMAIL>","mail.supervisioninschool.net",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.net",993,"<EMAIL>","mail.supervisioninschool.net",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.net",993,"<EMAIL>","mail.supervisioninschool.net",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.net",993,"<EMAIL>","mail.supervisioninschool.net",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.net",993,"<EMAIL>","mail.supervisioninschool.net",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.live",993,"<EMAIL>","mail.supervisioninschool.live",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.live",993,"<EMAIL>","mail.supervisioninschool.live",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.live",993,"<EMAIL>","mail.supervisioninschool.live",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.live",993,"<EMAIL>","mail.supervisioninschool.live",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.live",993,"<EMAIL>","mail.supervisioninschool.live",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.live",993,"<EMAIL>","mail.supervisioninschool.live",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.live",993,"<EMAIL>","mail.supervisioninschool.live",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.live",993,"<EMAIL>","mail.supervisioninschool.live",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.live",993,"<EMAIL>","mail.supervisioninschool.live",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.live",993,"<EMAIL>","mail.supervisioninschool.live",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.live",993,"<EMAIL>","mail.supervisioninschool.live",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.co",993,"<EMAIL>","mail.supervisioninschool.co",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.co",993,"<EMAIL>","mail.supervisioninschool.co",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.co",993,"<EMAIL>","mail.supervisioninschool.co",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.co",993,"<EMAIL>","mail.supervisioninschool.co",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.co",993,"<EMAIL>","mail.supervisioninschool.co",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.co",993,"<EMAIL>","mail.supervisioninschool.co",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.co",993,"<EMAIL>","mail.supervisioninschool.co",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.co",993,"<EMAIL>","mail.supervisioninschool.co",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.co",993,"<EMAIL>","mail.supervisioninschool.co",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.co",993,"<EMAIL>","mail.supervisioninschool.co",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.co",993,"<EMAIL>","mail.supervisioninschool.co",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.college",993,"<EMAIL>","mail.supervisioninschool.college",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.college",993,"<EMAIL>","mail.supervisioninschool.college",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.college",993,"<EMAIL>","mail.supervisioninschool.college",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.college",993,"<EMAIL>","mail.supervisioninschool.college",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.college",993,"<EMAIL>","mail.supervisioninschool.college",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.college",993,"<EMAIL>","mail.supervisioninschool.college",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.college",993,"<EMAIL>","mail.supervisioninschool.college",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.college",993,"<EMAIL>","mail.supervisioninschool.college",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.digital",993,"<EMAIL>","mail.supervisioninschool.digital",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.digital",993,"<EMAIL>","mail.supervisioninschool.digital",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.digital",993,"<EMAIL>","mail.supervisioninschool.digital",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.digital",993,"<EMAIL>","mail.supervisioninschool.digital",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.digital",993,"<EMAIL>","mail.supervisioninschool.digital",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.digital",993,"<EMAIL>","mail.supervisioninschool.digital",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.digital",993,"<EMAIL>","mail.supervisioninschool.digital",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.digital",993,"<EMAIL>","mail.supervisioninschool.digital",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.site",993,"<EMAIL>","mail.supervisioninschool.site",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.site",993,"<EMAIL>","mail.supervisioninschool.site",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.site",993,"<EMAIL>","mail.supervisioninschool.site",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.site",993,"<EMAIL>","mail.supervisioninschool.site",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.site",993,"<EMAIL>","mail.supervisioninschool.site",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.site",993,"<EMAIL>","mail.supervisioninschool.site",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.site",993,"<EMAIL>","mail.supervisioninschool.site",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.site",993,"<EMAIL>","mail.supervisioninschool.site",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.info",993,"<EMAIL>","mail.supervisioninschool.info",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.info",993,"<EMAIL>","mail.supervisioninschool.info",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.info",993,"<EMAIL>","mail.supervisioninschool.info",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.info",993,"<EMAIL>","mail.supervisioninschool.info",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.info",993,"<EMAIL>","mail.supervisioninschool.info",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.info",993,"<EMAIL>","mail.supervisioninschool.info",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.info",993,"<EMAIL>","mail.supervisioninschool.info",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.info",993,"<EMAIL>","mail.supervisioninschool.info",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.online",993,"<EMAIL>","mail.supervisioninschool.online",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.online",993,"<EMAIL>","mail.supervisioninschool.online",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.online",993,"<EMAIL>","mail.supervisioninschool.online",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.online",993,"<EMAIL>","mail.supervisioninschool.online",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.online",993,"<EMAIL>","mail.supervisioninschool.online",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.online",993,"<EMAIL>","mail.supervisioninschool.online",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.online",993,"<EMAIL>","mail.supervisioninschool.online",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.online",993,"<EMAIL>","mail.supervisioninschool.online",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.support",993,"<EMAIL>","mail.supervisioninschool.support",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.support",993,"<EMAIL>","mail.supervisioninschool.support",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.support",993,"<EMAIL>","mail.supervisioninschool.support",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.support",993,"<EMAIL>","mail.supervisioninschool.support",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.support",993,"<EMAIL>","mail.supervisioninschool.support",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.support",993,"<EMAIL>","mail.supervisioninschool.support",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.support",993,"<EMAIL>","mail.supervisioninschool.support",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.support",993,"<EMAIL>","mail.supervisioninschool.support",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.team",993,"<EMAIL>","mail.supervisioninschool.team",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.team",993,"<EMAIL>","mail.supervisioninschool.team",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.team",993,"<EMAIL>","mail.supervisioninschool.team",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.team",993,"<EMAIL>","mail.supervisioninschool.team",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.team",993,"<EMAIL>","mail.supervisioninschool.team",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.team",993,"<EMAIL>","mail.supervisioninschool.team",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.team",993,"<EMAIL>","mail.supervisioninschool.team",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschool.team",993,"<EMAIL>","mail.supervisioninschool.team",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.uk",993,"<EMAIL>","mail.supervisioninschools.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.uk",993,"<EMAIL>","mail.supervisioninschools.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.uk",993,"<EMAIL>","mail.supervisioninschools.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.uk",993,"<EMAIL>","mail.supervisioninschools.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.uk",993,"<EMAIL>","mail.supervisioninschools.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.uk",993,"<EMAIL>","mail.supervisioninschools.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.uk",993,"<EMAIL>","mail.supervisioninschools.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.uk",993,"<EMAIL>","mail.supervisioninschools.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.uk",993,"<EMAIL>","mail.supervisioninschools.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.uk",993,"<EMAIL>","mail.supervisioninschools.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.uk",993,"<EMAIL>","mail.supervisioninschools.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.org.uk",993,"<EMAIL>","mail.supervisioninschools.org.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.org.uk",993,"<EMAIL>","mail.supervisioninschools.org.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.org.uk",993,"<EMAIL>","mail.supervisioninschools.org.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.org.uk",993,"<EMAIL>","mail.supervisioninschools.org.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.org.uk",993,"<EMAIL>","mail.supervisioninschools.org.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.org.uk",993,"<EMAIL>","mail.supervisioninschools.org.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.org.uk",993,"<EMAIL>","mail.supervisioninschools.org.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.org.uk",993,"<EMAIL>","mail.supervisioninschools.org.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.org.uk",993,"<EMAIL>","mail.supervisioninschools.org.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.org.uk",993,"<EMAIL>","mail.supervisioninschools.org.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.org.uk",993,"<EMAIL>","mail.supervisioninschools.org.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.org",993,"<EMAIL>","mail.supervisioninschools.org",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.org",993,"<EMAIL>","mail.supervisioninschools.org",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.org",993,"<EMAIL>","mail.supervisioninschools.org",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.org",993,"<EMAIL>","mail.supervisioninschools.org",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.org",993,"<EMAIL>","mail.supervisioninschools.org",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.org",993,"<EMAIL>","mail.supervisioninschools.org",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.org",993,"<EMAIL>","mail.supervisioninschools.org",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.org",993,"<EMAIL>","mail.supervisioninschools.org",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.org",993,"<EMAIL>","mail.supervisioninschools.org",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.org",993,"<EMAIL>","mail.supervisioninschools.org",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.org",993,"<EMAIL>","mail.supervisioninschools.org",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.net",993,"<EMAIL>","mail.supervisioninschools.net",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.net",993,"<EMAIL>","mail.supervisioninschools.net",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.net",993,"<EMAIL>","mail.supervisioninschools.net",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.net",993,"<EMAIL>","mail.supervisioninschools.net",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.net",993,"<EMAIL>","mail.supervisioninschools.net",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.net",993,"<EMAIL>","mail.supervisioninschools.net",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.net",993,"<EMAIL>","mail.supervisioninschools.net",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.net",993,"<EMAIL>","mail.supervisioninschools.net",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.net",993,"<EMAIL>","mail.supervisioninschools.net",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.net",993,"<EMAIL>","mail.supervisioninschools.net",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.net",993,"<EMAIL>","mail.supervisioninschools.net",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.live",993,"<EMAIL>","mail.supervisioninschools.live",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.live",993,"<EMAIL>","mail.supervisioninschools.live",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.live",993,"<EMAIL>","mail.supervisioninschools.live",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.live",993,"<EMAIL>","mail.supervisioninschools.live",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.live",993,"<EMAIL>","mail.supervisioninschools.live",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.live",993,"<EMAIL>","mail.supervisioninschools.live",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.live",993,"<EMAIL>","mail.supervisioninschools.live",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.live",993,"<EMAIL>","mail.supervisioninschools.live",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.live",993,"<EMAIL>","mail.supervisioninschools.live",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.live",993,"<EMAIL>","mail.supervisioninschools.live",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.live",993,"<EMAIL>","mail.supervisioninschools.live",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.co",993,"<EMAIL>","mail.supervisioninschools.co",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.co",993,"<EMAIL>","mail.supervisioninschools.co",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.co",993,"<EMAIL>","mail.supervisioninschools.co",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.co",993,"<EMAIL>","mail.supervisioninschools.co",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.co",993,"<EMAIL>","mail.supervisioninschools.co",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.co",993,"<EMAIL>","mail.supervisioninschools.co",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.co",993,"<EMAIL>","mail.supervisioninschools.co",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.co",993,"<EMAIL>","mail.supervisioninschools.co",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.co",993,"<EMAIL>","mail.supervisioninschools.co",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.co",993,"<EMAIL>","mail.supervisioninschools.co",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.co",993,"<EMAIL>","mail.supervisioninschools.co",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.college",993,"<EMAIL>","mail.supervisioninschools.college",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.college",993,"<EMAIL>","mail.supervisioninschools.college",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.college",993,"<EMAIL>","mail.supervisioninschools.college",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.college",993,"<EMAIL>","mail.supervisioninschools.college",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.college",993,"<EMAIL>","mail.supervisioninschools.college",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.college",993,"<EMAIL>","mail.supervisioninschools.college",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.college",993,"<EMAIL>","mail.supervisioninschools.college",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.college",993,"<EMAIL>","mail.supervisioninschools.college",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.digital",993,"<EMAIL>","mail.supervisioninschools.digital",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.digital",993,"<EMAIL>","mail.supervisioninschools.digital",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.digital",993,"<EMAIL>","mail.supervisioninschools.digital",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.digital",993,"<EMAIL>","mail.supervisioninschools.digital",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.digital",993,"<EMAIL>","mail.supervisioninschools.digital",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.digital",993,"<EMAIL>","mail.supervisioninschools.digital",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.digital",993,"<EMAIL>","mail.supervisioninschools.digital",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.digital",993,"<EMAIL>","mail.supervisioninschools.digital",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.digital",993,"<EMAIL>","mail.supervisioninschools.digital",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.info",993,"<EMAIL>","mail.supervisioninschools.info",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.info",993,"<EMAIL>","mail.supervisioninschools.info",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.info",993,"<EMAIL>","mail.supervisioninschools.info",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.info",993,"<EMAIL>","mail.supervisioninschools.info",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.info",993,"<EMAIL>","mail.supervisioninschools.info",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.info",993,"<EMAIL>","mail.supervisioninschools.info",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.info",993,"<EMAIL>","mail.supervisioninschools.info",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.info",993,"<EMAIL>","mail.supervisioninschools.info",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.online",993,"<EMAIL>","mail.supervisioninschools.online",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.online",993,"<EMAIL>","mail.supervisioninschools.online",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.online",993,"<EMAIL>","mail.supervisioninschools.online",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.online",993,"<EMAIL>","mail.supervisioninschools.online",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.online",993,"<EMAIL>","mail.supervisioninschools.online",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.online",993,"<EMAIL>","mail.supervisioninschools.online",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.online",993,"<EMAIL>","mail.supervisioninschools.online",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.online",993,"<EMAIL>","mail.supervisioninschools.online",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.site",993,"<EMAIL>","mail.supervisioninschools.site",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.site",993,"<EMAIL>","mail.supervisioninschools.site",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.site",993,"<EMAIL>","mail.supervisioninschools.site",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.site",993,"<EMAIL>","mail.supervisioninschools.site",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.site",993,"<EMAIL>","mail.supervisioninschools.site",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.site",993,"<EMAIL>","mail.supervisioninschools.site",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.site",993,"<EMAIL>","mail.supervisioninschools.site",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.site",993,"<EMAIL>","mail.supervisioninschools.site",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.support",993,"<EMAIL>","mail.supervisioninschools.support",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.support",993,"<EMAIL>","mail.supervisioninschools.support",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.support",993,"<EMAIL>","mail.supervisioninschools.support",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.support",993,"<EMAIL>","mail.supervisioninschools.support",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.support",993,"<EMAIL>","mail.supervisioninschools.support",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.support",993,"<EMAIL>","mail.supervisioninschools.support",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.support",993,"<EMAIL>","mail.supervisioninschools.support",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.support",993,"<EMAIL>","mail.supervisioninschools.support",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.website",993,"<EMAIL>","mail.supervisioninschools.website",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.website",993,"<EMAIL>","mail.supervisioninschools.website",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.website",993,"<EMAIL>","mail.supervisioninschools.website",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.website",993,"<EMAIL>","mail.supervisioninschools.website",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.website",993,"<EMAIL>","mail.supervisioninschools.website",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.website",993,"<EMAIL>","mail.supervisioninschools.website",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.website",993,"<EMAIL>","mail.supervisioninschools.website",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.website",993,"<EMAIL>","mail.supervisioninschools.website",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.com",993,"<EMAIL>","mail.supervisioninschools.com",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.com",993,"<EMAIL>","mail.supervisioninschools.com",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.com",993,"<EMAIL>","mail.supervisioninschools.com",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.com",993,"<EMAIL>","mail.supervisioninschools.com",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.com",993,"<EMAIL>","mail.supervisioninschools.com",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.com",993,"<EMAIL>","mail.supervisioninschools.com",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.com",993,"<EMAIL>","mail.supervisioninschools.com",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninschools.com",993,"<EMAIL>","mail.supervisioninschools.com",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.uk",993,"<EMAIL>","mail.supervisioninsch.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.uk",993,"<EMAIL>","mail.supervisioninsch.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.uk",993,"<EMAIL>","mail.supervisioninsch.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.uk",993,"<EMAIL>","mail.supervisioninsch.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.uk",993,"<EMAIL>","mail.supervisioninsch.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.uk",993,"<EMAIL>","mail.supervisioninsch.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.uk",993,"<EMAIL>","mail.supervisioninsch.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.uk",993,"<EMAIL>","mail.supervisioninsch.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.uk",993,"<EMAIL>","mail.supervisioninsch.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.uk",993,"<EMAIL>","mail.supervisioninsch.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.uk",993,"<EMAIL>","mail.supervisioninsch.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.support",993,"<EMAIL>","mail.supervisioninsch.support",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.support",993,"<EMAIL>","mail.supervisioninsch.support",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.support",993,"<EMAIL>","mail.supervisioninsch.support",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.support",993,"<EMAIL>","mail.supervisioninsch.support",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.support",993,"<EMAIL>","mail.supervisioninsch.support",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.support",993,"<EMAIL>","mail.supervisioninsch.support",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.support",993,"<EMAIL>","mail.supervisioninsch.support",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.support",993,"<EMAIL>","mail.supervisioninsch.support",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.support",993,"<EMAIL>","mail.supervisioninsch.support",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.support",993,"<EMAIL>","mail.supervisioninsch.support",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.support",993,"<EMAIL>","mail.supervisioninsch.support",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.org.uk",993,"<EMAIL>","mail.supervisioninsch.org.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.org.uk",993,"<EMAIL>","mail.supervisioninsch.org.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.org.uk",993,"<EMAIL>","mail.supervisioninsch.org.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.org.uk",993,"<EMAIL>","mail.supervisioninsch.org.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.org.uk",993,"<EMAIL>","mail.supervisioninsch.org.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.org.uk",993,"<EMAIL>","mail.supervisioninsch.org.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.org.uk",993,"<EMAIL>","mail.supervisioninsch.org.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.org.uk",993,"<EMAIL>","mail.supervisioninsch.org.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.org.uk",993,"<EMAIL>","mail.supervisioninsch.org.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.org.uk",993,"<EMAIL>","mail.supervisioninsch.org.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.org.uk",993,"<EMAIL>","mail.supervisioninsch.org.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.org",993,"<EMAIL>","mail.supervisioninsch.org",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.org",993,"<EMAIL>","mail.supervisioninsch.org",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.org",993,"<EMAIL>","mail.supervisioninsch.org",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.org",993,"<EMAIL>","mail.supervisioninsch.org",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.org",993,"<EMAIL>","mail.supervisioninsch.org",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.org",993,"<EMAIL>","mail.supervisioninsch.org",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.org",993,"<EMAIL>","mail.supervisioninsch.org",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.org",993,"<EMAIL>","mail.supervisioninsch.org",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.org",993,"<EMAIL>","mail.supervisioninsch.org",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.org",993,"<EMAIL>","mail.supervisioninsch.org",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.org",993,"<EMAIL>","mail.supervisioninsch.org",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.online",993,"<EMAIL>","mail.supervisioninsch.online",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.online",993,"<EMAIL>","mail.supervisioninsch.online",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.online",993,"<EMAIL>","mail.supervisioninsch.online",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.online",993,"<EMAIL>","mail.supervisioninsch.online",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.online",993,"<EMAIL>","mail.supervisioninsch.online",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.online",993,"<EMAIL>","mail.supervisioninsch.online",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.online",993,"<EMAIL>","mail.supervisioninsch.online",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.online",993,"<EMAIL>","mail.supervisioninsch.online",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.online",993,"<EMAIL>","mail.supervisioninsch.online",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.online",993,"<EMAIL>","mail.supervisioninsch.online",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.online",993,"<EMAIL>","mail.supervisioninsch.online",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.live",993,"<EMAIL>","mail.supervisioninsch.live",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.live",993,"<EMAIL>","mail.supervisioninsch.live",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.live",993,"<EMAIL>","mail.supervisioninsch.live",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.live",993,"<EMAIL>","mail.supervisioninsch.live",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.live",993,"<EMAIL>","mail.supervisioninsch.live",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.live",993,"<EMAIL>","mail.supervisioninsch.live",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.live",993,"<EMAIL>","mail.supervisioninsch.live",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.live",993,"<EMAIL>","mail.supervisioninsch.live",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.live",993,"<EMAIL>","mail.supervisioninsch.live",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.live",993,"<EMAIL>","mail.supervisioninsch.live",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.live",993,"<EMAIL>","mail.supervisioninsch.live",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.co.uk",993,"<EMAIL>","mail.supervisioninsch.co.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.co.uk",993,"<EMAIL>","mail.supervisioninsch.co.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.co.uk",993,"<EMAIL>","mail.supervisioninsch.co.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.co.uk",993,"<EMAIL>","mail.supervisioninsch.co.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.co.uk",993,"<EMAIL>","mail.supervisioninsch.co.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.co.uk",993,"<EMAIL>","mail.supervisioninsch.co.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.co.uk",993,"<EMAIL>","mail.supervisioninsch.co.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.co.uk",993,"<EMAIL>","mail.supervisioninsch.co.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.co.uk",993,"<EMAIL>","mail.supervisioninsch.co.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.co.uk",993,"<EMAIL>","mail.supervisioninsch.co.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.co.uk",993,"<EMAIL>","mail.supervisioninsch.co.uk",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.college",993,"<EMAIL>","mail.supervisioninsch.college",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.college",993,"<EMAIL>","mail.supervisioninsch.college",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.college",993,"<EMAIL>","mail.supervisioninsch.college",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.college",993,"<EMAIL>","mail.supervisioninsch.college",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.college",993,"<EMAIL>","mail.supervisioninsch.college",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.college",993,"<EMAIL>","mail.supervisioninsch.college",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.college",993,"<EMAIL>","mail.supervisioninsch.college",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.college",993,"<EMAIL>","mail.supervisioninsch.college",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.digital",993,"<EMAIL>","mail.supervisioninsch.digital",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.digital",993,"<EMAIL>","mail.supervisioninsch.digital",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.digital",993,"<EMAIL>","mail.supervisioninsch.digital",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.digital",993,"<EMAIL>","mail.supervisioninsch.digital",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.digital",993,"<EMAIL>","mail.supervisioninsch.digital",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.digital",993,"<EMAIL>","mail.supervisioninsch.digital",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.digital",993,"<EMAIL>","mail.supervisioninsch.digital",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.digital",993,"<EMAIL>","mail.supervisioninsch.digital",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.info",993,"<EMAIL>","mail.supervisioninsch.info",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.info",993,"<EMAIL>","mail.supervisioninsch.info",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.info",993,"<EMAIL>","mail.supervisioninsch.info",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.info",993,"<EMAIL>","mail.supervisioninsch.info",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.info",993,"<EMAIL>","mail.supervisioninsch.info",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.info",993,"<EMAIL>","mail.supervisioninsch.info",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.info",993,"<EMAIL>","mail.supervisioninsch.info",465,25),
//            BulkAddEmailData("<EMAIL>","Julia","Davey","<EMAIL>","mail.supervisioninsch.info",993,"<EMAIL>","mail.supervisioninsch.info",465,25),
//          )
//
//          object Insert extends TestAppTrait {
//
//
//            def init() = {
//              println("Enter password:")
//              val password: String = scala.io.StdIn.readLine()
//              println(s"Entered password: $password")
//
//              data_from_sheet.map { d =>
//                val data = EmailSettingForm(
//                  email = d.email,
//
//                  service_provider = EmailServiceProvider.OTHER,
//
//                  smtp_username = Some(d.smtp_username),
//                  smtp_password = Some(password),
//                  smtp_host = Some(d.smtp_host),
//                  smtp_port = Some(d.smtp_port),
//
//                  imap_username = Some(d.imap_username),
//                  imap_password = Some(password),
//                  imap_host = Some(d.imap_host),
//                  imap_port = Some(d.imap_port),
//
//                  // for mailgun
//                  email_domain = None,
//                  api_key = None,
//                  mailgun_region = None,
//
//                  quota_per_day = d.daily_limit,
//                  min_delay_seconds = 300,
//                  max_delay_seconds = 500,
//
//
//                  can_send = true,
//                  can_receive = true,
//
//                  cc_emails = None,
//                  bcc_emails = None,
//
//                  first_name = d.first_name,
//                  last_name = d.last_name,
//
//                  platform_email_id = None
//                )
//                emailAccountService.createEmailViaForm(
//                  accountId = 22513,
//                  teamId = 20924,
//                  taId = 32488,
//                  data = data,
//                  orgId = OrgId(24)
//                ) match {
//                  case Right(value) => logger.info(s"SUCCESS added for ${d.email}")
//                  case Left(value) => logger.fatal(s"FAILED to add for ${d.email} $value")
//                }
//              }
//
//            }
//          }
//          Insert.init()
//          Thread.sleep(10000)
//
//          */
//
//        case "insertSavedProspectsCount" =>
//          given Logger: SRLogger = new SRLogger("TestApp :: insertSavedProspectsCount :: ")
//
//          object main extends TestAppTrait {
//            def init() = {
//              teamDAO.getTeamsOrderByOrgPlanType match {
//                case Failure(e) =>
//                  Logger.fatal("Error while fetching teams", e)
//
//                case Success(teams) =>
//                  teams.map(teamId => {
//                    mqInsertTeamsMetadata.publish(msg = teamId) match {
//                      case Failure(e) =>
//                        Logger.fatal(s"Unable to publish $teamId to MqInsertTeamsMetadata", e)
//
//                      case Success(_) =>
//                        Logger.info(s"Published $teamId to MqInsertTeamsMetadata")
//                    }
//                  })
//              }
//            }
//          }
//
//          main.init()
//
//
//
//        /*
//      // sbt "coldemail/runMain utils.TestApp createCSV"
//      case "createAndUploadCSVToGCP" =>
//        object CreateCSV extends PhantomBusterServiceDI
//          with Team.TeamServiceDI
//          with PhantomBusterAgentServiceDI
//          with PhantomBusterAgentsDAODI
//          with TaskDaoServiceDI
//          with ProspectDAOService_DI
//          with LinkedinSettingServiceDI
//          with LinkedinMessagesServiceDI
//          with LinkedinMessageThreadsServiceDI
//          with CampaignServiceDI
//          with AccountServiceDI
//          with CampaignCacheServiceDI
//          with CampaignDAO_DI
//          with CampaignProspectDAO_DI
//          with CampaignSendReportsDAO_DI
//          with CampaignStepVariantDAO_DI
//          with ChannelSettingServiceDI
//          with DBUtils_DI
//          with EmailScheduledDAO_DI
//          with EmailSendingStatusDAO_DI
//          with EmailSettingDAO_DI
//          with EmailSettingJedisServiceDI
//          with ReplySentimentService_DI
//          with ResetUserCacheUtilDI
//          with SrUserFeatureUsageEventServiceDI
//          with SrUuidUtilsDI
//          with TaskServiceDI
//          with TriggerDAO_DI
//          with LinkedinMessageThreadsDAODI
//          with LinkedinMessagesDAODI
//          with LinkedinSettingDAO_DI
//          with EmailThreadDAO_DI
//          with MergeTagService_DI
//          with ProspectColumnDefDAO_DI
//          with ProspectDAO_DI
//          with ProspectQueryDI
//          with ReplySentimentDAOService_DI
//          with AccountDAO_DI
//          with ProspectAddEventDAO_DI
//          with SrEventServiceDI
//          with TaskCacheDAO_DI
//          with TaskDAO_DI
//          with SRFeatureFlagsServiceDI
//          with TeamsDAO_DI
//          with AccessTokenService_DI
//          with OrganizationDAO_DI
//          with SupportAccessToUserAccountDAO_DI
//          with UserRedisKeyService_DI
//          with CampaignStepDAO_DI
//          with PhishingCheckServiceDI
//          with ProspectServiceV2_DI
//          with CacheServiceJedisDI
//          with CampaignProspectDAO_2DI
//          with ReplySentimentDAO_DI
//          with ReplySentimentJedisDAO_DI
//          with CacheServiceDI
//          with OrgMetadataDAO_DI
//          with KafkaServiceDI
//          with SrInternalFeatureUsageDaoDI
//          with TaskCacheService_DI
//          with SrDBQueryCounterService_DI
//          with ClientAccountAccessLogDAO_DI
//          with EmailValidationModelDI
//          with ProspectColumnDef_DI
//          with ProspectsEmailsDAO_DI
//          with DBCounterDAO_DI
//          with ScyllaRunSafely_DI
//          with ScyllaCluster_DI
//          with ScyllaDbConnection_DI
//          with ActivityTriggerMQServiceDI
//          with CallDAO_DI
//          with EventLogService_DI
//          with EventLogDAO_DI
//          with MqHandleEventLogDI
//          with EventLogFindServiceDI
//          with HandleActivityTriggerEventServiceDI
//          with HandlePushTriggerEventServiceDI
//          with WorkFlowAttemptService_DI
//          with CampaignProspectServiceDI
//          with TagServiceDI
//          with EmailNotificationServiceDI
//          with LeadStatusServiceDI
//          with SRTriggerAllowedCombos_DI
//          with TIntegrationCRMService_DI
//          with WorkflowAttemptDAO_DI
//          with CampaignProspectTimezonesJedisServiceDI
//          with MailgunServiceDI
//          with IntegrationTypeService_DI
//          with HubSpotOAuth_DI
//          with PipedriveOAuth_DI
//          with SalesforceOAuth_DI
//          with ZohoOAuth_DI
//          with ZohoRecruitOAuth_DI
//          with TriggerDAOService_DI
//          with TriggerServiceV2_DI
//          with TriggerJedisDAO_DI
//          with PhantomBusterApiDI {
//          def init() = {
//            phantomBusterService.createUploadAndGenerateSignedURLForCSV(
//              rows = Seq(Seq("<PROFILE_URL>")),
//              headerRow = Seq("Profile"),
//              filename = "Test.csv"
//            )
//          }
//        }
//
//        CreateCSV.init()
//         */
//
//
//        case "new_migration_prospects_emails" =>
//        /*
//                  val ecTeam = Helpers.genFixedThreadPoolEC(threads = 3)
//                  val ecProspect = Helpers.genFixedThreadPoolEC(threads = 15)
//
//                  val migration_teamIds = List(1256, 1257, 1258, 1259, 126, 1260, 1261, 1262, 1263, 1264, 1265, 1266, 1267, 1268,
//            1269, 127, 1270, 1271, 1272, 1273, 1274, 1275, 1276, 1277, 1278, 1279, 128, 1280, 1281, 1282, 1283, 1284, 1285, 1286, 1287, 1288, 1289, 129,
//            1290, 1291, 1292, 1293, 1294, 1295, 1296, 1297, 1298, 1299, 130, 1300, 1301, 1302, 1303, 1305, 1306, 1307, 1308, 1309, 131, 1310, 1311, 1312,
//            1315, 1316, 1317, 1318, 1319, 132, 1321, 1322, 1323, 1324, 1325, 1326, 1327, 1328, 1329, 133, 1330, 1331, 1332, 1333, 1334, 1335, 1336, 1337,
//            1338, 1339, 134, 1340, 1341, 1342, 1343, 1344, 1345, 1346, 1347, 1348, 1349, 135, 1350, 1351, 1352, 1353, 1354, 1355, 1356, 1357, 1358, 1359,
//            136, 1360, 1361, 1362, 1363, 1364, 1365, 1367, 1368, 1369, 137, 1370, 1371, 1372, 1373, 1374, 1375, 1376, 1377, 1378, 1379, 138, 1380, 1381,
//            1382, 1383, 1384, 1385, 1386, 1387, 1388, 1389, 139, 1391, 1392, 1393, 1394, 1395, 1396, 1398, 1399, 140, 1400, 1401, 1403, 1404, 1405, 1406,
//            1407, 1408, 1409, 141, 1410, 1411, 1412, 1413, 1414, 1415, 1416, 1417, 1419, 142, 1420, 1421, 1422, 1423, 1424, 1425, 1426, 1427, 1428, 1429,
//            143, 1430, 1431, 1432, 1433, 1434, 1435, 1436, 1437, 1438, 1439, 144, 1440, 1441, 1442, 1443, 1444, 1445, 1446, 1447, 1448, 1449, 145, 1450,
//            1451, 1452, 1454, 1455, 1456, 1457, 1458, 1459, 146, 1460, 1461, 1462, 1464, 1465, 1466, 1467, 1468, 1469, 147, 1470, 1472, 1473, 1474, 1475,
//            1476, 1477, 1478, 1479, 148, 1480, 1481, 1482, 1484, 1485, 1487, 1488, 1489, 149, 1490, 1491, 1492, 1493, 1494, 1495, 1496, 1497, 1498, 1499,
//            15, 150, 1500, 1501, 1502, 1503, 1504, 1505, 1506, 1507, 1508, 1509, 151, 1510, 1511, 1512, 1515, 1519, 152, 1522, 1528, 153, 1532, 1534, 1537,
//            1539, 154, 1540, 1541, 1542, 1543, 1544, 1545, 1546, 1547, 1548, 155, 1559, 156, 1560, 1561, 1562, 1563, 1564, 1565, 1566, 1567, 1568, 1569,
//            157, 1570, 1571, 1572, 1573, 1575, 1576, 1578, 158, 1580, 1581, 1582, 1583, 1584, 1585, 1586, 1587, 1588, 1589, 159, 1590, 1591, 1592, 1593,
//            1594, 1595, 1596, 1597, 1598, 1599, 16, 160, 1600, 1601, 1602, 1603, 1604, 1605, 1606, 1607, 1608, 1609, 161, 1610, 1611, 1612, 1613, 1614,
//            1616, 1617, 1618, 1619, 162, 1620, 1622, 1623, 1624, 1625, 1626, 1627, 1628, 1629, 163, 1630, 1631, 1633, 1634, 1635, 1636, 1637, 1638, 1639,
//            164, 1640, 1641, 1642, 1644, 1645, 1646, 1647, 1648, 1649, 165, 1650, 1651, 1652, 1653, 1654, 1655, 1656, 1657, 1658, 1659, 166, 1660, 1661, 1662,
//            1663, 1664, 1665, 1667, 1668, 1669, 167, 1670, 1671, 1673, 1674, 1675, 1676, 1677, 1678, 1679, 168, 1680, 1681, 1682,
//            1684, 1686, 1687, 1688, 1689, 169, 1690, 1691, 1693, 1694, 1695, 1696, 1697, 1698, 1699, 17, 170, 1700, 1701, 1702, 1703, 1709, 171, 1710, 1711,
//            1712, 1713, 1714, 1715, 1716, 1717, 1718, 1719, 172, 1720, 1722, 1723, 1724, 1725, 1726, 1727, 1728, 1729, 173, 1730, 1731, 1734, 1735, 1736, 1737,
//            1738, 1739, 174, 1740, 1741, 1742, 1743, 1744, 1745, 1747, 1748, 1749, 175, 1750, 1751, 1752, 1753, 1754, 1755, 1756, 1757, 1758, 1759, 176, 1760,
//            1761, 1762, 1763, 1764, 1765, 1766, 1767, 1768, 1769, 177, 1770, 1771, 1772, 1773, 1774, 1775, 1776, 1777, 1778, 1779, 178, 1780, 1781, 1782, 1783,
//            1784, 1785, 1786, 1787, 1788, 1789, 179, 1790, 1791, 1792, 1793, 1794, 1795, 1796, 1797, 1798, 1799, 18, 180, 1800, 1801, 1802, 1804, 1805, 1806, 1807,
//            1808, 1809, 181, 1810, 1811, 1812, 1813, 1814, 1815, 1818, 1819, 182, 1820, 1821, 1822, 1823, 1824, 1825, 1826, 1827, 1828, 1829, 183, 1830, 1831, 1833,
//            1837, 1838, 1839, 184, 1840, 1841, 1842, 1843, 1844, 1845, 1846, 1847, 1848, 1849, 185, 1850, 1851, 1852, 1853, 1854, 1855, 1856, 1857, 1858, 1859, 186,
//            1860, 1861, 1862, 1863, 1864, 1865, 1866, 1867, 1868, 1869, 187, 1870, 1871, 1872, 1873, 1874, 1875, 1876, 1877, 1878, 1879, 188, 1880, 1882, 1883, 1884,
//            1885, 1886, 1887, 1888, 1889, 189, 1890, 1891, 1892, 1893, 1894, 1895, 1896, 1897, 1899, 19, 190, 1900, 1901, 1902, 1903, 1904, 1906, 1907, 1908, 1909,
//            191, 1910, 1911, 1916, 1917, 1918, 1919, 192, 1920, 1921, 1922, 1923, 1924, 1925, 1926, 1927, 1928, 1929, 193, 1930, 1931, 1932, 1933, 1934, 1935, 1937,
//            1938, 1939, 194, 1940, 1941, 1942, 1943, 1944, 1945, 1946, 1947, 1948, 1949, 195, 1950, 1951, 1952, 1953, 1954, 1955, 1956, 1957, 1958, 1959, 196, 1960,
//            1962, 1963, 1964, 1966, 1967, 1968, 1969, 1970, 1971, 1972, 1973, 1974, 1975, 1976, 1977, 1978, 1979, 198, 1980, 1981, 1982, 1983, 1984, 1985, 1986, 1987,
//            1988, 1989, 199, 1990, 1991, 1992, 1993, 1994, 1995, 1997, 1998, 1999, 20, 200, 2000, 2001, 2002, 2003, 2004, 2005, 2006, 2007, 2009, 201, 2010, 2011, 2012,
//            2013, 2014, 2015, 2016, 2017, 2018, 2019, 202, 2020, 2021, 2022, 2023, 2024, 2025, 2026, 2027, 2028, 2029, 203, 2030, 2031, 2033, 2034, 2036, 2037, 2039,
//            204, 2041, 2042, 2043, 2044, 2047, 2048, 2049, 205, 2050, 2051, 2052, 2053, 2055, 2056, 2057, 2058, 206, 2060, 2061, 2062, 2063, 2064, 2065, 2066, 2067,
//            2068, 2069, 207, 2072, 2074, 2075, 2076, 2077, 2078, 208, 2080, 2081, 2082, 2084, 2085, 2086, 2087, 2088, 2089, 209, 2090, 2091, 2092, 2095, 2096, 2097,
//            2098, 2099, 21, 210, 2100, 2101, 2102, 2103, 2104, 2105, 2106, 2108, 2109, 211, 2111, 2112, 2113, 2114, 2115, 2116, 2117, 2118, 2119, 212, 2120, 2121, 2122,
//            2123, 2124, 2125, 2126, 2128, 2129, 213, 2130, 2131, 2132, 2134, 2135, 2136, 2137, 2138, 2139, 214, 2140, 2141, 2142, 2143, 2145, 2146, 2147, 2148, 2149, 215,
//            2150, 2151, 2152, 2153, 2154, 2155, 2156, 2157, 2158, 2159, 216, 2160, 2161, 2162, 2163, 2164, 2165, 2166, 2167, 2168, 2169, 217, 2170, 2171, 2172, 2173, 2174,
//            2175, 2176, 2177, 2178, 2179, 218, 2180, 2181, 2182, 2183, 2184, 2185, 2186, 2187, 2188, 2189, 219, 2190, 2191, 2192, 2193, 2194, 2195, 2196, 2197, 2198, 2199, 22, 220, 2200, 2201, 2202, 2203, 2204, 2205, 2206, 2208, 221, 2210, 2211, 2212, 2213, 2214, 2215, 2217, 2218, 2219, 222, 2220, 2221, 2222, 2223, 2224, 2225, 2227, 223, 2230, 2231, 2232, 2233, 2234, 2235, 2237, 2238, 2239, 224, 2240, 2241, 2242, 2243, 2244, 2246, 2247, 225, 2250, 2251, 2252, 2253, 2254, 2255, 2256, 2257, 2258, 2259, 226, 2260, 2261, 2262, 2263, 2264, 2265, 2266, 2267, 2268, 2269, 227, 2270, 2271, 2272, 2273, 2274, 2275, 2276, 2277, 2278, 228, 2280, 2282, 2283, 2284, 2285, 2286, 2287, 2288, 2289, 229, 2290, 2291, 2292, 2293, 2294, 2295, 2297, 2298, 2299, 23, 230, 2300, 2301, 2302, 2303, 2304, 2305, 2307, 2308, 2309, 231, 2311, 2312, 2313, 2314, 2315, 2316, 2317, 2318, 2319, 232, 2320, 2321, 2322, 2323, 2324, 2325, 2326, 2327, 2328, 2329, 233, 2330, 2332, 2333, 2334, 2335, 2336, 2337, 2338, 234, 2340, 2341, 2342, 2343, 2344, 2345, 2346, 2347, 2348, 235, 2350, 2351, 2353, 2355, 2356, 2357, 2358, 2359, 236, 2360, 2361, 2362, 2363, 2364, 2365, 2366, 2367, 2368, 2369, 237, 2370, 2371, 2372, 2373, 2374, 2375, 2376, 2377, 2378, 2379, 238, 2380, 2381, 2383, 2384, 2385, 2386, 2387, 2388, 2389, 239, 2391, 2393, 2394, 2396, 2397, 2399, 240, 2400, 2401, 2402, 2405, 2406, 2407, 2408, 2409, 241, 2410, 2411, 2414, 2415, 2416, 2418, 2419, 2420, 2421, 2423, 2424, 2425, 2426, 2427, 2428, 2429, 243, 2430, 2431, 2432, 2434, 2435, 2436, 2437, 2438, 244, 2440, 2442, 2443, 2444, 2445, 2447, 2448, 2449, 245, 2450, 2451, 2453, 2454, 2455, 2458, 2459, 246, 2460, 2463, 2464, 2465, 2466, 2467, 2469, 247, 248, 2482, 2483, 2484, 2485, 2486, 2487, 2488, 2489, 249, 2490, 2492, 2493, 2494, 2495, 2496, 2498, 25, 250, 2500, 2501, 2502, 2503, 2505, 2507, 2508, 251, 2510, 2511, 2512, 2513, 2514, 2515, 2516, 2517, 2518, 2519, 252, 2520, 2521, 2522, 2524, 2525, 2526, 2527, 2528, 2529, 253, 2530, 2532, 2534, 2535, 2537, 2539, 254, 2541, 2542, 2543, 2544, 2545, 2546, 2547, 2548, 2549, 255, 2550, 2552, 2553, 2554, 2555, 2556, 2557, 2558, 2559, 256, 2561, 2562, 2563, 2564, 2565, 2566, 2567, 2568, 2569, 257, 2570, 2571, 2572, 2573, 2574, 2575, 2577, 2578, 2579, 258, 2580, 2583, 2584, 2585, 259, 2590, 2591, 2592, 2594, 2595, 2596, 26, 260, 2600, 2601, 2602, 2603, 2604, 2605, 2608, 261, 2611, 2612, 2613, 2615, 2616, 2617, 2618, 2619, 262, 2620, 2621, 2622, 2623, 2624, 2625, 2627, 2628, 2629, 263, 2630, 2631, 2632, 2633, 2634, 2635, 2637, 2638, 2639, 264, 2640, 2641, 2643, 2644, 2645, 2646, 2647, 2648, 2649, 2650, 2652, 2653, 2654, 2655, 2656, 2657, 2658, 2659, 266, 2660, 2661, 2662, 2663, 2664, 2665, 2666, 2667, 2669, 267, 2670, 2671, 2672, 2673, 2674, 2676, 2677, 2678, 2679, 268, 2680, 2681, 2682, 2683, 2684, 2685, 2686, 2687, 269, 2690, 2691, 2692, 2693, 2694, 2695, 2696, 2697, 2698, 2699, 27, 270, 2700, 2701, 2702, 2703, 2705, 2706, 2707, 2708, 271, 2710, 2711, 2712, 2713, 2714, 2715, 2716, 2717, 2718, 2719, 272, 2721, 2722, 2723, 2724, 2725, 2726, 2727, 2728, 2729, 273, 2730, 2731, 2732, 2733, 2735, 2736, 2737, 2738, 2739, 274, 2740, 2741, 2742, 2743, 2744, 2745, 2746, 2747, 2748, 2749, 275, 2750, 2751, 2752, 2753, 2755, 2756, 2757, 276, 2760, 2761, 2762, 2763, 2764, 2765, 2767, 2768, 2769, 277, 2770, 2771, 2772, 2773, 2774, 2775, 2776, 2777, 2778, 2779, 278, 2780, 2781, 2783, 2784, 2785, 2786, 2789, 279, 2791, 2793, 2794, 2795, 2796, 2798, 2799, 28, 280, 2800, 2801, 2802, 2803, 2804, 2805, 2806, 2808, 2809, 281, 2810, 2811, 2812, 2814, 2815, 2816, 2817, 2818, 2819, 282, 2821, 2822, 2823, 2824, 2825, 2826, 2827, 2829, 283, 2830, 2831, 2837, 2838, 2839, 284, 2840, 2841, 2842, 2844, 2845, 2847, 2848, 2849, 285, 2850, 2851, 2853, 2854, 2855, 2857, 2858, 2859, 2860, 2861, 2862, 2864, 2865, 2866, 2867, 2868, 2869, 287, 2870, 2871, 2872, 2873, 2874, 2875, 2876, 2877, 2878, 2879, 288, 2880, 2881, 2882, 2885, 2886, 2888, 2889, 289, 2890, 2891, 2892, 2893, 2894, 2895, 2896, 2897, 2898, 2899, 29, 290, 2900, 2901, 2902, 2903, 2904, 2905, 2906, 2907, 2908, 2909, 291, 2910, 2911, 2912, 2913, 2914, 2915, 2916, 2918, 2919, 292, 2922, 2923, 2924, 2925, 2926, 2927, 2928, 293, 2930, 2931, 2932, 2934, 2935, 2936, 2937, 294, 2941, 2942, 2943, 2946, 2947, 2948, 2949, 295, 2952, 2953, 2955, 2956, 2957, 2958, 2959, 296, 2960, 2961, 2962, 2963, 2964, 2965, 2967, 2968, 2969, 297, 2970, 2971, 2972, 2973, 2974, 2975, 2976, 2977, 2978, 2979, 298, 2981, 2982, 2983, 2985, 2986, 2987, 2988, 299, 2990, 2991, 2992, 2993, 2994, 2995, 2996, 2997, 2998, 2999, 30, 300, 3000, 3001, 3002, 3003, 3005, 3006, 3007, 3008, 3009, 301, 3010, 3011, 3012, 3013, 3014, 3015, 3016, 3017, 3018, 3019, 302, 3020, 3021, 3022, 3023, 3024, 3025, 3026, 3027, 3029, 303, 3030, 3033, 3034, 3035, 3036, 3037, 3040, 3043, 3044, 3045, 3046, 3047, 3048, 3049, 305, 3051, 3052, 3054, 3055, 3056, 3057, 3058, 3059, 306, 3060, 3061, 3062, 3063, 3064, 3065, 3066, 3067, 3068, 3070, 3071, 3072, 3073, 3074, 3075, 3076, 3078, 3079, 3080, 3082, 3083, 3084, 3085, 3086, 3087, 3088, 3089, 3090, 3092, 3093, 3094, 3095, 3096, 3097, 3098, 3099, 31, 310, 3100, 3101, 3102, 3103, 3104, 3105, 3106, 3107, 3108, 3110, 3111, 3112, 3113, 3115, 3116, 3118, 3119, 312, 3120, 3121, 3124, 3125, 3126, 3127, 3128, 3129, 313, 3130, 3131, 3132, 3133, 3134, 3135, 3136, 3137, 3138, 3139, 314, 3140, 3141, 3142, 3143, 3144, 3145, 3146, 3148, 3149, 315, 3150, 3151, 3152, 3153, 3154, 3155, 3156, 3157, 3158, 3159, 316, 3161, 3162, 3163, 3164, 3165, 3166, 3167, 3168, 3169, 317, 3170, 3171, 3172, 3173, 3174, 3175, 3176, 3177, 3178, 3179, 318, 3180, 3181, 3183, 3184, 3185, 3187, 3188, 3189, 319, 3190, 3191, 3192, 3193, 3194, 3195, 3196, 3197, 3198, 3199, 32, 320, 3203, 3204, 3205, 3206, 3207, 3208, 3209, 321, 3210, 3211, 3212, 3213, 3214, 3215, 3216, 3217, 3218, 3219, 322, 3220, 3221, 3222, 3223, 3226, 3227, 3229, 323, 3230, 3233, 3234, 3235, 3236, 3237, 3238, 324, 3240, 3241, 3244, 3245, 3246, 3247, 3249, 325, 3250, 3251, 3252, 3253, 3254, 3255, 3256, 3257, 3258, 3259, 3260, 3261, 3262, 3263, 3264, 3265, 3266, 3267, 3268, 3269, 327, 3270, 3271, 3272, 3274, 3275, 3276, 3278, 3279, 328, 3280, 3281, 3282, 3283, 3284, 3285, 3286, 3287, 3288, 3289, 329, 3290, 3291, 3292, 3293, 3294, 3297, 3298, 3299, 33, 330, 3300, 3301, 3302, 3303, 3304, 3305, 3306, 3307, 3308, 3309, 331, 3310, 3311, 3312, 3313, 3315, 3316, 3317, 3318, 3319, 332, 3324, 3326, 3327, 3328, 3329, 333, 3330, 3331, 3332, 3333, 3334, 3336, 3337, 3338, 3339, 334, 3340, 3341, 3343, 3344, 3346, 3348, 3349, 335, 3350, 3352, 3353, 3354, 3355, 3356, 3357, 3358, 3359, 336, 3361, 3362, 3363, 3364, 3365, 3366, 3367, 3369, 337, 3370, 3371, 3372, 3373, 3374, 3375, 3377, 3378, 3379, 3380, 3381, 3382, 3383, 3384, 3385, 3386, 3387, 3388, 3389, 339, 3390, 3391, 3392, 3393, 3396, 3397, 3398, 3399, 34, 340, 3401, 3402, 3404, 3405, 3406, 3407, 3408, 3409, 341, 3410, 3411, 3412, 3413, 3414, 3415, 3416, 3417, 342, 3420, 3421, 3422, 3423, 3424, 3425, 3426, 3427, 3429, 343, 3430, 3431, 3432, 3433, 3434, 3435, 3436, 3437, 3438, 344, 3440, 3441, 3442, 3443, 3444, 3445, 3446, 3449, 3450, 3451, 3453, 3454, 3456, 3457, 3458, 3459, 3460, 3461, 3462, 3463, 3465, 3466, 3467, 3468, 3469, 347, 3470, 3471, 3472, 3473, 3474, 3477, 3478, 3479, 348, 3480, 3481, 3482, 3483, 3484, 3486, 3488, 3489, 349, 3490, 3491, 3492, 3493, 3494, 3495, 3496, 3499, 35, 350, 3500, 3502, 3503, 3504, 351, 3512, 3513, 3514, 3515, 3516, 3517, 3518, 3519, 352, 3520, 3521, 3522, 3524, 3525, 3526, 3527, 3528, 3529, 353, 3530, 3531, 3532, 3533, 3534, 3536, 3537, 3539, 354, 3540, 3541, 3542, 3543, 3545, 3546, 3547, 3548, 355, 3550, 3551, 3553, 3554, 3555, 3557, 3558, 3559, 3560, 3561, 3563, 3564, 3565, 3566, 3567, 3568, 3569, 3570, 3571, 3572, 3573, 3574, 3575, 3576, 3577, 3578, 3579, 358, 3580, 3581, 3582, 3583, 3584, 3585, 3586, 3587, 3588, 3589, 3590, 3591, 3592, 3593, 3594, 3595, 3596, 3597, 3599, 36, 360, 3600, 3602, 3603, 3604, 3606, 3607, 3608, 3609, 361, 3611, 3612, 3613, 3615, 3616, 3617, 3618, 362, 3620, 3621, 3622, 3625, 3626, 3627, 3628, 3629, 363, 3630, 3631, 3633, 3634, 3635, 3636, 3637, 3639, 364, 3642, 3643, 3645, 3647, 3648, 3649, 365, 3650, 3651, 3652, 3653, 3654, 3655, 3656, 3657, 3658, 3659, 366, 3660, 3662, 3663, 3665, 3666, 3667, 3669, 367, 3670, 3671, 3672, 3674, 3675, 3676, 3677, 3679, 368, 3680, 3681, 3682, 3683, 3685, 3686, 3687, 3688, 3689, 369, 3690, 3691, 3692, 3693, 3694, 3695, 3696, 3697, 3699, 37, 370, 3700, 3701, 3702, 3703, 3704, 3705, 3706, 3707, 3708, 371, 3710, 3711, 3712, 3713, 3716, 3717, 3718, 3719, 372, 3720, 3721, 3722, 3724, 3726, 3727, 3728, 3729, 373, 3730, 3731, 3732, 3733, 3735, 3736, 3737, 3738, 3739, 374, 3740, 3742, 3743, 3745, 3746, 3747, 3748, 3749, 375, 3750, 3751, 3752, 3754, 3756, 3757, 3758, 3759, 376, 3760, 3761, 3762, 3763, 3764, 3765, 3766, 3768, 3769, 377, 3770, 3772, 3773, 3774, 3775, 3776, 3777, 378, 3781, 3782, 3783, 379, 3790, 3791, 3792, 3793, 3794, 3795, 3796, 3797, 3798, 3799, 38, 380, 3800, 3801, 3805, 3806, 3807, 3808, 381, 3811, 3812, 3813, 3814, 3815, 3816, 3817, 3818, 3819, 382, 3821, 3822, 3824, 3825, 3828, 3829, 383, 3830, 3831, 3832, 3833, 3834, 3835, 3836, 3837, 3838, 3839, 384, 3840, 3841, 3844, 3845, 3846, 3847, 3848, 3849, 385, 3850, 3851, 3852, 3853, 3854, 3855, 3856, 3857, 3858, 3859, 386, 3860, 3861, 3863, 3865, 3866, 3867, 3869, 387, 3870, 3871, 3872, 3874, 3875, 3876, 3879, 3880, 3881, 3882, 3884, 3885, 3887, 3888, 3889, 389, 3890, 39, 390, 3901, 3904, 3905, 3906, 3907, 3908, 3909, 3911, 3912, 3914, 3915, 3916, 3917, 3918, 3919, 392, 3921, 3922, 3923, 3924, 3925, 3926, 3927, 3928, 3930, 3931, 3936, 3937, 3939, 3940, 3941, 3945, 3946, 3947, 3948, 3950, 3951, 3952, 3953, 3954, 3955, 3956, 3957, 3959, 3960, 3961, 3962, 3963, 3964, 3965, 3966, 3967, 3968, 3969, 3970, 3972, 3975, 3976, 3977, 3978, 3980, 3981, 3982, 3983, 3984, 3985, 3987, 3988, 3991, 3992, 3993, 3994, 3996, 3998, 3999, 4, 40, 4000, 4001, 4002, 4003, 4004, 4005, 4006, 4007, 4008, 4009, 4011, 4012, 4013, 4014, 4015, 4016, 4018, 4019, 402, 4020, 4021, 4022, 4023, 4025, 4026, 4028, 403, 4030, 4031, 4032, 4033, 4034, 4035, 4036, 4037, 4038, 4039, 404, 4040, 4041, 4042, 4043, 4044, 4045, 4046, 4047, 4048, 4049, 405, 4051, 4052, 4053, 4054, 4055, 4056, 4057, 4058, 4059, 406, 4060, 4061, 4062, 4063, 4064, 4065, 4066, 4067, 4068, 4069, 4070, 4071, 4072, 4073, 4074, 4075, 4076, 4078, 4079, 408, 4080, 4081, 4082, 4083, 4084, 4085, 4087, 4089, 409, 4090, 4091, 4092, 4093, 4095, 4096, 4097, 4098, 4099, 41, 4100, 4101, 4103, 4104, 4105, 4106, 4107, 4108, 4109, 411, 4111, 4112, 4113, 4114, 4115, 4117, 4118, 4119, 412, 4120, 4121, 4122, 4124, 4125, 4126, 4128, 4129, 413, 4130, 4131, 4132, 4133, 4135, 4137, 4138, 4139, 414, 4140, 4141, 4142, 4143, 4147, 4148, 415, 4150, 4152, 4153, 4155, 4157, 4158, 4159, 4161, 4162, 4164, 4165, 4167, 4168, 4169, 417, 4170, 4172, 4173, 4174, 4175, 4176, 4178, 4179, 4180, 4182, 4183, 4184, 4185, 4186, 4187, 4188, 4189, 419, 4190, 4191, 4192, 4194, 4195, 4196, 4197, 4198, 4199, 42, 420, 4200, 4201, 4202, 4203, 4204, 4205, 4206, 4208, 4209, 421, 4210, 4211, 4212, 4213, 4214, 4215, 4216, 4217, 4218, 4219, 422, 4221, 4222, 4223, 4224, 4225, 4227, 4228, 4229, 4230, 4231, 4232, 4234, 4235, 4236, 4237, 4238, 4248, 4250, 4251, 4252, 4253, 4254, 4255, 4256, 4258, 4261, 4262, 4263, 4264, 4265, 4266, 4267, 4268, 4269, 4270, 4271, 4272, 4273, 4276, 4277, 4279, 428, 4280, 4281, 4282, 4283, 4284, 4285, 4286, 4287, 4288, 4289, 429, 4290, 4291, 4292, 4293, 4294, 4295, 4296, 4297, 4298, 4299, 43, 4300, 4301, 4302, 4303, 4304, 4306, 4307, 4309, 431, 4311, 4312, 4313, 4314, 4316, 4317, 4318, 4319, 432, 4320, 4321, 4322, 4324, 4325, 4326, 4327, 4328, 433, 4331, 4332, 4333, 4334, 4335, 4336, 4337, 4339, 434, 4340, 4341, 4343, 4344, 4346, 4347, 4349, 435, 4350, 4351, 4352, 4353, 4354, 4355, 4356, 4357, 4359, 4360, 4361, 4362, 4363, 4364, 4365, 4366, 4367, 4368, 4369, 437, 4370, 4371, 4372, 4373, 4374, 4375, 4376, 4377, 4378, 4379, 438, 4380, 4381, 4382, 4384, 4385, 4386, 4387, 4388, 4389, 439, 4390, 4391, 4393, 4398, 44, 440, 4400, 4401, 4402, 4404, 4405, 4406, 4407, 4408, 4409, 441, 4410, 4411, 4412, 4413, 4414, 4415, 4416, 4417, 442, 4420, 4421, 4422, 4423, 4424, 4425, 4426, 4427, 4428, 4429, 443, 4430, 4431, 4432, 4433, 4434, 4435, 4436, 4437, 4439, 444, 4441, 4442, 4443, 4444, 4445, 4446, 4447, 4448, 4449, 445, 4450, 4451, 4452, 4453, 4454, 4455, 4456, 4457, 4458, 4459, 446, 4460, 4461, 4462, 4463, 4464, 4465, 4466, 4467, 4469, 447, 4470, 4471, 4472, 4473, 4474, 4477, 4478, 448, 4480, 4481, 4482, 4483, 4484, 4485, 4486, 4487, 4488, 449, 4491, 4492, 4493, 4494, 4495, 4496, 4497, 4498, 4499, 45, 450, 4500, 4501, 4502, 4503, 4504, 4505, 4506, 4507, 4508, 4509, 451, 4510, 4511, 4512, 4514, 4515, 4516, 4517, 4518, 4519, 452, 4520, 4521, 4522, 4523, 4524, 4525, 4526, 4527, 4528, 4529, 453, 4530, 4531, 4532, 4533, 4534, 4535, 4536, 4537, 4538, 4539, 454, 4542, 4543, 4544, 4545, 4546, 4547, 4548, 4549, 4550, 4551, 4552, 4553, 4555, 4556, 4557, 4558, 4559, 4563, 4564, 4566, 4569, 4570, 4571, 4572, 4573, 4574, 4575, 4576, 4577, 4578, 4579, 4580, 4581, 4582, 4584, 4586, 4587, 4588, 4589, 459, 4590, 4591, 4592, 4594, 4595, 4597, 4598, 4599, 46, 460, 4600, 4601, 4602, 4603, 4606, 4610, 4611, 4612, 4613, 4614, 4616, 4617, 4618, 4619, 462, 4620, 4623, 4624, 4625, 4626, 4627, 4629, 463, 4630, 4632, 4633, 4634, 4635, 4636, 4637, 4638, 4639, 464, 4640, 4641, 4642, 4643, 4645, 4646, 4648, 465, 4653, 4654, 4655, 4656, 4657, 4658, 4659, 4660, 4661, 4662, 4664, 4665, 4668, 4669, 467, 4670, 4671, 4672, 4674, 4675, 4676, 4677, 4678, 4679, 468, 4680, 4681, 4682, 4683, 4684, 4685, 4686, 4688, 4689, 469, 4692, 4693, 4695, 4697, 4698, 47, 470, 4700, 4701, 4702, 4704, 4706, 4707, 4708, 4709, 471, 4710, 4712, 4713, 4714, 4715, 4716, 472, 4721, 4727, 4728, 4729, 473, 4731, 4732, 4733, 4734, 4736, 4737, 4738, 4739, 474, 4740, 4741, 4743, 4744, 4745, 4746, 4747, 4748, 475, 4750, 4751, 4753, 4754, 4755, 4756, 4757, 4759, 476, 4760, 4761, 4762, 4763, 4765, 4766, 4767, 4768, 4769, 477, 4770, 4771, 4772, 4773, 4774, 4775, 4776, 4777, 4778, 4779, 478, 4781, 4782, 4783, 4785, 4786, 4787, 4789, 479, 4790, 4791, 4792, 4793, 4794, 4796, 4797, 4799, 48, 480, 4800, 4801, 4802, 4804, 4805, 4806, 4807, 481, 4810, 4811, 4812, 4813, 4814, 4815, 4816, 4818, 482, 4820, 4821, 4828, 4830, 4831, 4833, 4834, 4835, 4836, 4838, 4839, 484, 4840, 4841, 4842, 4847, 4848, 4849, 485, 4850, 4851, 4852, 4853, 4854, 4855, 4856, 4858, 4859, 486, 4860, 4861, 4862, 4863, 4864, 4865, 4866, 4868, 4869, 487, 4871, 4872, 4873, 4874, 4875, 4876, 4877, 4878, 4879, 488, 4881, 4884, 4885, 4886, 4887, 4888, 4889, 489, 4892, 4894, 4895, 4896, 4897, 4898, 4899, 49, 490, 4900, 4901, 4902, 4903, 4904, 4905, 4906, 4907, 4908, 4909, 491, 4910, 4911, 4912, 4913, 4914, 4915, 4916, 4918, 4919, 492, 4921, 4922, 4924, 4925, 4926, 4927, 4928, 4929, 493, 4930, 4931, 4932, 4933, 4934, 4935, 4936, 4937, 4938, 494, 4940, 4941, 4942, 4943, 4945, 4947, 4948, 4949, 495, 4950, 4951, 4952, 4953, 4954, 4955, 4956, 4957, 4958, 496, 4960, 4961, 4962, 4963, 4965, 4966, 4967, 4969, 497, 4970, 4971, 4972, 4973, 4974, 4975, 4976, 4977, 4978, 4979, 498, 4980, 4981, 4982, 4983, 4984, 4985, 4986, 4987, 4988, 4989, 499, 4990, 4991, 4992, 4993, 4994, 4996, 4997, 4998, 4999, 5, 50, 500, 5000, 5001, 5002, 5003, 5004, 5005, 5006, 5009, 501, 5010, 5011, 5012, 5013, 5014, 5015, 5016, 5017, 5018, 5019, 502, 5020, 5021, 5022, 5023, 5024, 5025, 5026, 5027, 5029, 503, 5030, 5031, 5032, 5033, 5034, 5035, 5036, 5037, 5038, 504, 5040, 5041, 5042, 5043, 5044, 5045, 5046, 5052, 5053, 5054, 5055, 5056, 5057, 5058, 5059, 506, 5060, 5063, 5064, 5066, 5067, 5068, 5069, 507, 5072, 5073, 5074, 5075, 5076, 5077, 5078, 5079, 508, 5080, 5081, 5082, 5083, 5084, 5085, 5086, 5087, 5088, 5089, 509, 5090, 5093, 5095, 5097, 5098, 5099, 51, 510, 5100, 5101, 5102, 5103, 5104, 5105, 5106, 5108, 5109, 511, 5110, 5111, 5112, 5113, 5114, 5115, 5116, 5117, 5118, 5119, 512, 5120, 5121, 5123, 5124, 5125, 5126, 5128, 5129, 513, 5130, 5131, 5133, 5137, 5138, 5139, 514, 5140, 5141, 5142, 5143, 5144, 5146, 5147, 5148, 5149, 515, 5150, 5151, 5152, 5153, 5154, 5155, 5156, 5157, 5158, 5159, 516, 5160, 5161, 5162, 5163, 5164, 5166, 5167, 5168, 517, 5170, 5171, 5173, 5174, 5175, 5177, 5179, 518, 5180, 5181, 5183, 5184, 5185, 5186, 5187, 5188, 519, 5190, 5191, 5192, 5193, 5194, 5195, 5196, 5197, 5198, 5199, 52, 5200, 5201, 5202, 5203, 5204, 5206, 5208, 5209, 521, 5210, 5211, 5212, 5213, 5214, 5215, 5216, 5217, 5218, 5219, 5222, 5224, 5225, 5226, 5227, 5228, 5229, 523, 5230, 5231, 5232, 5234, 5235, 5236, 5237, 5238, 5239, 524, 5240, 5241, 5242, 5243, 5244, 5245, 5246, 5247, 5249, 525, 5251, 5252, 5253, 5254, 5255, 5256, 5258, 5259, 526, 5260, 5261, 5262, 5263, 5264, 5266, 5267, 5268, 5270, 5271, 5272, 5273, 5274, 5275, 5276, 5277, 5278, 5279, 5280, 5282, 5283, 5284, 5285, 5287, 5288, 529, 5292, 5293, 5294, 5295, 5296, 5297, 53, 530, 5300, 5301, 5303, 5304, 5305, 5307, 5308, 5309, 531, 5310, 5311, 5312, 5313, 5314, 5316, 5319, 532, 5320, 5321, 5322, 5323, 5325, 5326, 5327, 5328, 533, 5330, 5331, 5332, 5335, 5336, 5337, 5339, 534, 5340, 5341, 5342, 5343, 5344, 5345, 5347, 5349, 535, 5350, 5351, 5352, 5353, 5354, 5355, 5356, 5357, 5358, 5359, 536, 5360, 5361, 5362, 5364, 5365, 5367, 5368, 5369, 537, 5370, 5371, 5372, 5373, 5374, 5375, 5377, 5378, 538, 5380, 5381, 5382, 5383, 5384, 5385, 5386, 5387, 5389, 539, 5390, 5391, 5393, 5394, 5395, 5396, 5397, 5398, 5399, 54, 540, 5400, 5401, 5402, 5404, 5405, 5406, 5407, 5408, 541, 5411, 5412, 5414, 5415, 5416, 5417, 5418, 5419, 5420, 5421, 5422, 5424, 5425, 5426, 5427, 5428, 5429, 543, 5430, 5431, 5432, 5433, 5435, 5437, 5438, 5439, 544, 5440, 5441, 5442, 5444, 5445, 5446, 5447, 5448, 5449, 545, 5450, 5451, 5452, 5453, 5455, 5456, 5457, 5458, 546, 5460, 5461, 5462, 5463, 5465, 5466, 5468, 5469, 547, 5470, 5471, 5474, 5475, 5476, 5478, 548, 5480, 5481, 5482, 5483, 5485, 5487, 5488, 5489, 549, 5490, 5491, 5492, 5493, 5494, 5497, 5499, 55, 550, 5500, 5501, 5502, 5504, 5505, 5506, 5507, 5509, 551, 5511, 5512, 5514, 5515, 5516, 5518, 5519, 552, 5521, 5522, 5523, 5524, 5525, 5526, 5527, 5528, 5529, 553, 5530, 5531, 5532, 5533, 5534, 5535, 5536, 5537, 5538, 5539, 554, 5540, 5541, 5542, 5544, 5545, 5546, 5547, 5548, 5549, 555, 5550, 5551, 5552, 5553, 5554, 5555, 5556, 5557, 5558, 5559, 556, 5560, 5561, 5562, 5563, 5564, 5565, 5567, 5568, 5569, 5570, 5571, 5572, 5573, 5575, 5576, 5582, 5583, 5584, 5585, 5586, 5587, 5588, 5589, 559, 5590, 5591, 5592, 5593, 5594, 5595, 5596, 5597, 5598, 56, 560, 5601, 5603, 5605, 5607, 5608, 5609, 561, 5610, 5611, 5612, 5613, 5614, 5616, 5618, 562, 5620, 5621, 5622, 5623, 5624, 5625, 5626, 5627, 5628, 5629, 563, 5630, 5631, 5632, 5633, 5634, 5635, 5636, 5637, 5638, 564, 5640, 5641, 5642, 5643, 5644, 5645, 5646, 5647, 5648, 5649, 565, 5651, 5652, 5653, 5654, 5655, 5656, 5657, 5658, 5659, 566, 5660, 5661, 5662, 5664, 5665, 5666, 5667, 5669, 567, 5671, 5672, 5675, 5676, 5677, 5678, 5680, 5681, 5682, 5683, 5684, 5685, 5686, 5689, 569, 5690, 5691, 5692, 5693, 5695, 5696, 5697, 5698, 5699, 57, 570, 5700, 5701, 5702, 5703, 5704, 5705, 5706, 5707, 5708, 5709, 571, 5710, 5711, 5712, 5713, 5714, 5715, 5716, 5717, 5718, 5719, 572, 5720, 5721, 5722, 5723, 5724, 5725, 5726, 5727, 5728, 5729, 573, 5731, 5732, 5733, 5734, 5735, 5736, 5737, 5739, 574, 5740, 5741, 5742, 5743, 5744, 5745, 5747, 5749, 575, 5750, 5752, 5754, 5755, 5756, 5757, 5758, 5759, 576, 5760, 5761, 5762, 5763, 5764, 5765, 5767, 5769, 577, 5771, 5772, 5773, 5774, 5775, 5776, 5777, 5778, 5779, 578, 5781, 5782, 5783, 5784, 5785, 5786, 5787, 579, 5791, 5792, 5793, 5794, 5795, 5796, 5797, 5798, 5799, 580, 5800, 5801, 5802, 5803, 5804, 5805, 5806, 5807, 5808, 5809, 581, 5810, 5813, 5814, 5815, 582, 5820, 5821, 5822, 5823, 5824, 5825, 5826, 5827, 5828, 5829, 583, 5830, 5831, 5832, 5833, 5835, 5836, 5837, 5838, 5839, 584, 5840, 5841, 5843, 5844, 5845, 5846, 5847, 5849, 585, 5850, 5851, 5852, 5853, 5854, 5855, 5856, 5857, 5858, 5859, 586, 5861, 5862, 5863, 5864, 5865, 5866, 5867, 5868, 5869, 5870, 5871, 5873, 5875, 5876, 5878, 5879, 588, 5880, 5881, 5882, 5883, 5884, 5885, 5886, 5888, 589, 5890, 5891, 5894, 5895, 5896, 5897, 5899, 59, 590, 5900, 5901, 5902, 5903, 5904, 5905, 5906, 5907, 5908, 5909, 591, 5910, 5911, 5913, 5915, 5916, 5917, 5918, 5919, 592, 5920, 5921, 5922, 5923, 5924, 5927, 5929, 593, 5930, 5931, 5932, 5933, 5934, 5935, 5936, 5937, 5938, 5939, 594, 5941, 5942, 5943, 5944, 5945, 5947, 5948, 595, 5950, 5951, 5952, 5955, 5956, 5958, 5959, 596, 5960, 5961, 5962, 5963, 5964, 5965, 5966, 5967, 5968, 597, 5970, 5971, 5972, 5974, 5977, 598, 5980, 5981, 5982, 5983, 5984, 5985, 5986, 5987, 5988, 5989, 599, 5990, 5991, 5992, 5993, 5994, 5995, 5996, 5997, 5998, 5999, 6, 60, 600, 6000, 6001, 6002, 6003, 6004, 6005, 6006, 6007, 6008, 6009, 601, 6010, 6011, 6012, 6013, 6014, 6015, 6016, 6017, 6018, 6019, 602, 6020, 6021, 6022, 6023, 6024, 6025, 6026, 6027, 6028, 6029, 603, 6030, 6031, 6032, 6033, 6034, 6035, 6036, 6037, 6038, 6039, 604, 6040, 6041, 6042, 6043, 6044, 6045, 6046, 6047, 6048, 6049, 605, 6050, 6051, 6052, 6053, 6054, 6055, 6056, 6057, 6058, 6059, 606, 6060, 6061, 6062, 6063, 6064, 6065, 6066, 6067, 6068, 6069, 607, 6070, 6071, 6072, 6073, 6074, 6075, 6076, 6077, 6078, 6079, 608, 6080, 6083, 6084, 6085, 6086, 6089, 609, 6091, 6092, 6093, 6095, 6096, 6097, 6099, 61, 610, 6100, 6102, 6103, 6104, 6105, 6106, 6107, 6108, 6109, 611, 6110, 6111, 6113, 6114, 6115, 6116, 6117, 6118, 612, 6121, 6122, 6123, 6124, 6125, 6126, 6127, 613, 6136, 6137, 6138, 6139, 614, 6141, 6142, 6143, 6144, 6145, 6146, 6147, 6148, 6149, 615, 6150, 6151, 6152, 6153, 6154, 6155, 6156, 6157, 6158, 6159, 616, 6160, 6161, 6162, 6163, 6164, 6165, 6166, 6168, 6169, 617, 6170, 6171, 6172, 6173, 6174, 6175, 6176, 6177, 6178, 6179, 618, 6180, 6181, 6182, 6183, 6184, 6185, 6186, 6187, 6188, 6189, 619, 6190, 6191, 6192, 6193, 6194, 6195, 6196, 6197, 6198, 6199, 62, 620, 6200, 6201, 6202, 6203, 6204, 6207, 6208, 621, 6210, 6211, 6212, 6213, 6214, 6215, 6216, 6217, 6218, 6219, 622, 6220, 6221, 6222, 6223, 6224, 6225, 6227, 6232, 6233, 6234, 6235, 6236, 6237, 6238, 6239, 624, 6240, 6242, 6244, 6245, 6246, 6247, 6248, 6249, 625, 6250, 6251, 6252, 6255, 6258, 6259, 626, 6263, 6264, 6265, 6266, 6267, 6268, 6269, 627, 6270, 6271, 6272, 6273, 6274, 6275, 6276, 6277, 6279, 628, 6285, 6286, 6287, 6288, 6289, 629, 6290, 6291, 6292, 6293, 6294, 6295, 6297, 6298, 6299, 63, 630, 6300, 6301, 6302, 6304, 6305, 6306, 6307, 6308, 6309, 631, 6310, 6311, 6312, 6313, 6314, 6315, 6316, 6317, 6318, 632, 6320, 6321, 6322, 6324, 6326, 6327, 6328, 6329, 633, 6330, 6331, 6332, 6334, 6336, 6337, 6338, 6339, 634, 6340, 6341, 6342, 6343, 6344, 6345, 6346, 6347, 6349, 635, 6350, 6351, 6352, 6353, 6354, 6355, 6356, 6357, 6358, 636, 6361, 6362, 6363, 6364, 6365, 6366, 6367, 6368, 637, 6371, 6372, 6373, 6374, 6375, 6376, 6377, 638, 6382, 6383, 6384, 6385, 6386, 6387, 6388, 6389, 639, 6390, 6391, 6392, 6393, 6394, 6395, 6396, 6397, 6398, 6399, 64, 640, 6400, 6405, 6406, 6407, 6408, 6409, 641, 6410, 6411, 6413, 6414, 6415, 6416, 6417, 6419, 642, 6420, 6421, 6429, 643, 6430, 6431, 6432, 6433, 6434, 6439, 644, 6440, 6441, 6442, 6449, 645, 6450, 6451, 6452, 6453, 6454, 6455, 6456, 6457, 6458, 6459, 646, 6461, 6462, 6463, 6464, 6465, 6466, 6467, 647, 6470, 6471, 6472, 6473, 6474, 6475, 6476, 6477, 6478, 6479, 648, 6480, 6481, 6482, 6484, 6485, 6486, 6487, 6488, 6489, 649, 6490, 6497, 65, 650, 6500, 6508, 6509, 651, 6510, 6511, 6512, 6513, 6516, 6517, 6518, 6519, 652, 6520, 6521, 6522, 6523, 6524, 6525, 6526, 6527, 6528, 6529, 653, 6531, 6532, 6533, 6535, 6536, 6537, 6538, 6539, 654, 6540, 6541, 6543, 6545, 6546, 6547, 655, 6552, 6553, 6554, 6555, 6558, 6559, 656, 6560, 6561, 6562, 6566, 657, 6571, 6572, 6573, 6574, 6575, 6576, 6577, 6578, 6579, 658, 6580, 6581, 6582, 6583, 6584, 6585, 659, 6590, 6593, 6594, 6595, 6597, 6598, 6599, 66, 660, 6600, 6601, 6603, 6604, 6605, 6606, 6607, 6608, 6609, 661, 6610, 6611, 6612, 6613, 6614, 6615, 6618, 6619, 662, 6620, 6621, 6622, 6624, 6625, 6626, 6627, 6628, 6629, 663, 6630, 6631, 6632, 6633, 6634, 6636, 6639, 664, 6640, 6642, 6643, 6644, 6645, 6646, 6647, 6648, 665, 6650, 6651, 6652, 6653, 6654, 6655, 6656, 6657, 6658, 6659, 666, 6660, 6661, 6663, 6665, 6667, 6668, 6669, 667, 6671, 6673, 6677, 6678, 6679, 668, 6682, 6684, 6685, 6687, 6688, 6689, 669, 6690, 6691, 6695, 6696, 6697, 6698, 67, 670, 6700, 6701, 6706, 6707, 6708, 671, 6713, 6714, 6716, 6717, 6718, 6719, 672, 6720, 6722, 6723, 6724, 6725, 6726, 6727, 6728, 6729, 673, 6730, 6731, 6735, 6736, 6737, 6739, 674, 6740, 6741, 6742, 6743, 6744, 6745, 6747, 6748, 6749, 675, 6750, 6751, 6752, 6753, 6754, 6755, 6756, 6757, 6758, 6759, 676, 6760, 6762, 6763, 6764, 6765, 6766, 6768, 677, 6772, 6773, 6774, 6775, 6776, 6777, 6778, 6779, 6780, 6782, 6783, 6784, 6785, 6786, 6787, 6788, 6789, 679, 6790, 6792, 6793, 6794, 6795, 6796, 6797, 6798, 68, 680, 6801, 6802, 6803, 6804, 6805, 6806, 6809, 681, 6810, 6812, 6813, 6814, 6815, 6817, 6818, 6819, 682, 6820, 6823, 6824, 6825, 6826, 6827, 6828, 6829, 683, 6830, 6831, 6832, 6833, 6835, 6836, 6837, 6839, 684, 6840, 6841, 6842, 6843, 6844, 6846, 6847, 6848, 6849, 685, 6850, 6851, 6852, 6853, 6854, 6855, 6856, 6857, 6858, 6859, 686, 6861, 6862, 6863, 6864, 6866, 6868, 687, 6873, 6874, 6875, 6876, 6877, 688, 6881, 6883, 6884, 6885, 6887, 6888, 6889, 689, 6890, 6891, 6892, 6893, 6894, 6897, 69, 690, 6900, 6902, 6904, 6905, 6906, 6907, 6908, 691, 6910, 6913, 6914, 6915, 6917, 6918, 6919, 692, 6920, 6921, 6922, 6923, 6925, 6926, 6927, 6928, 6929, 693, 6930, 6931, 6932, 6933, 6934, 6935, 6936, 6938, 6939, 694, 6940, 6941, 6942, 6945, 6946, 6947, 6948, 695, 6950, 6953, 6954, 6955, 6956, 6957, 6958, 6959, 696, 6960, 6961, 6962, 6963, 6964, 6965, 6966, 6968, 6969, 697, 6970, 6971, 6972, 6973, 6975, 6976, 6977, 6978, 6979, 698, 6980, 6981, 6982, 6983, 6984, 6985, 6986, 6987, 6988, 6989, 699, 6991, 6992, 6993, 6994, 6995, 6997, 6998, 7, 70, 700, 7000, 7002, 7003, 7004, 7006, 7007, 7008, 7009, 701, 7010, 7011, 7012, 7013, 7014, 7015, 7016, 7017, 7018, 7019, 702, 7020, 7021, 7022, 7023, 7024, 7025, 7026, 7028, 7029, 703, 7030, 7031, 7032, 7033, 7034, 7035, 7036, 7037, 7038, 7039, 704, 7040, 7043, 7044, 7045, 705, 7050, 7051, 7052, 7053, 7054, 7055, 7056, 7057, 7058, 706, 7061, 7062, 7063, 7065, 7066, 7068, 7069, 707, 7070, 7071, 7072, 7073, 7074, 7075, 7076, 7077, 7078, 708, 7082, 7083, 7086, 7087, 7088, 709, 7092, 7093, 7094, 7095, 7096, 7097, 7098, 7099, 71, 710, 7100, 7101, 7102, 7103, 7105, 7106, 7108, 7109, 711, 7110, 7111, 7112, 7113, 7115, 7116, 7117, 7118, 712, 7121, 7123, 7124, 7126, 7127, 7128, 7129, 713, 7130, 7131, 7132, 7133, 7134, 7135, 7136, 7137, 7139, 714, 7140, 7141, 7142, 7143, 7144, 7145, 7146, 7147, 7148, 7149, 715, 7150, 7151, 7152, 7153, 7154, 7155, 7156, 7157, 7158, 7159, 716, 7160, 7161, 7162, 7163, 7164, 7166, 7167, 717, 7170, 7171, 7172, 7173, 7175, 7176, 7178, 7179, 7180, 7181, 7182, 7183, 7186, 7189, 7190, 7191, 7192, 7193, 7194, 7195, 7198, 7199, 72, 7200, 7201, 7203, 7204, 7205, 7206, 7207, 7208, 7209, 7211, 7212, 7213, 7215, 7216, 7222, 7223, 7224, 7225, 7227, 7228, 7229, 7230, 7231, 7232, 7233, 7234, 7235, 7236, 7237, 7238, 7239, 7240, 7241, 7242, 7243, 7244, 7247, 7249, 7252, 7253, 7254, 7255, 7256, 7257, 7258, 7259, 7260, 7261, 7262, 7263, 7264, 7266, 7267, 7268, 7269, 7271, 7272, 7274, 7275, 7276, 7277, 7278, 7279, 7280, 7281, 7282, 7283, 7284, 7285, 7286, 7287, 7288, 7289, 7290, 7291, 7292, 7293, 7295, 7296, 7297, 7298, 7299, 73, 7300, 7301, 7303, 7304, 7306, 7307, 7308, 7310, 7311, 7312, 7313, 7314, 7316, 7317, 7319, 7321, 7322, 7323, 7324, 7325, 7326, 7327, 7328, 7329, 7330, 7331, 7332, 7333, 7334, 7335, 7336, 7337, 7338, 7339, 7340, 7342, 7343, 7344, 7345, 7346, 7347, 7348, 7349, 7350, 7351, 7352, 7353, 7354, 7355, 7356, 7357, 7358, 7359, 7360, 7361, 7362, 7363, 7364, 7365, 7366, 7368, 7370, 7371, 7372, 7373, 7374, 7375, 7376, 7377, 7378, 7379, 7381, 7382, 7386, 7387, 7389, 7390, 7392, 7393, 7395, 7396, 7397, 7399, 74, 7400, 7401, 7402, 7403, 7405, 7407, 7409, 7410, 7411, 7412, 7413, 7414, 7415, 7417, 7418, 7421, 7422, 7423, 7424, 7425, 7426, 7427, 7428, 7429, 7430, 7431, 7432, 7433, 7434, 7435, 7436, 7437, 7439, 7440, 7441, 7442, 7443, 7444, 7446, 7448, 7449, 7452, 7453, 7454, 7455, 7456, 7457, 7458, 7459, 7460, 7461, 7462, 7463, 7464, 7465, 7466, 7467, 7468, 7469, 7471, 7473, 7474, 7475, 7478, 7479, 7480, 7481, 7482, 7484, 7486, 7489, 7490, 7491, 7492, 7493, 7494, 7495, 7497, 7498, 75, 7500, 7501, 7502, 7503, 7504, 7505, 7506, 7507, 7508, 7509, 7510, 7511, 7512, 7513, 7514, 7515, 7516, 7519, 7520, 7522, 7525, 7526, 7527, 7529, 7530, 7531, 7533, 7534, 7535, 7536, 7538, 7539, 7541, 7542, 7543, 7544, 7545, 7547, 7548, 7549, 7550, 7551, 7553, 7554, 7555, 7556, 7558, 7559, 7560, 7562, 7563, 7564, 7565, 7566, 7567, 7568, 7570, 7571, 7572, 7573, 7574, 7576, 7577, 7578, 7579, 7582, 7583, 7585, 7586, 7588, 7589, 7590, 7591, 7592, 7593, 7594, 7595, 7596, 7597, 7598, 7599, 76, 7600, 7601, 7602, 7605, 7606, 7607, 7608, 7609, 7610, 7611, 7612, 7614, 7615, 7616, 7619, 7621, 7622, 7624, 7625, 7626, 7628, 7629, 7631, 7632, 7633, 7634, 7635, 7637, 7638, 7639, 7640, 7641, 7643, 7645, 7647, 7649, 7650, 7651, 7652, 7653, 7654, 7656, 7657, 7660, 7663, 7665, 7666, 7667, 7671, 7672, 7673, 7674, 7676, 7677, 7678, 7679, 7680, 7682, 7683, 7684, 7685, 7687, 7689, 7690, 7691, 7692, 7694, 7695, 7696, 7698, 77, 7701, 7703, 7704, 7707, 7708, 7709, 7711, 7712, 7713, 7714, 7715, 7716, 7717, 7718, 7719, 7720, 7722, 7724, 7725, 7726, 7727, 7729, 7732, 7735, 7737, 7738, 7739, 7740, 7741, 7742, 7744, 7745, 7746, 7748, 7749, 7750, 7751, 7752, 7753, 7754, 7756, 7758, 7760, 7762, 7763, 7764, 7765, 7766, 7767, 7768, 7769, 7771, 7772, 7773, 7775, 7776, 7778, 7779, 7780, 7781, 7782, 7783, 7784, 7785, 7786, 7787, 7788, 7789, 7790, 7791, 7792, 7793, 7794, 7796, 7797, 7798, 7799, 78, 7800, 7801, 7804, 7806, 7807, 7808, 7809, 7810, 7811, 7812, 7813, 7814, 7815, 7816, 7817, 7818, 7819, 7820, 7821, 7822, 7823, 7825, 7826, 7827, 7828, 7830, 7831, 7832, 7834, 7837, 7838, 7839, 7841, 7842, 7847, 7848, 7849, 7850, 7853, 7854, 7855, 7856, 7857, 7859, 7860, 7862, 7863, 7864, 7865, 7866, 7869, 7870, 7875, 7879, 7881, 7882, 7883, 7884, 7885, 7886, 7887, 7888, 7889, 7891, 7892, 7893, 7894, 7895, 7896, 7897, 7898, 7899, 79, 7900, 7901, 7902, 7903, 7904, 7905, 7906, 7907, 7909, 7910, 7911, 7913, 7915, 7917, 7918, 7919, 7920, 7921, 7922, 7923, 7925, 7928, 7929, 7930, 7931, 7932, 7939, 7940, 7941, 7942, 7946, 7947, 7948, 7949, 7950, 7951, 7952, 7953, 7954, 7955, 7956, 7957, 7958, 7960, 7962, 7963, 7965, 7966, 7967, 7968, 7969, 7971, 7972, 7975, 7977, 7978, 7979, 7980, 7981, 7982, 7984, 7985, 7986, 7987, 7988, 7989, 7991, 7992, 7994, 7995, 7996, 7998, 80, 8000, 8001, 8002, 8003, 8004, 8005, 8006, 8007, 8008, 8009, 8010, 8011, 8012, 8013, 8014, 8015, 8016, 8017, 8019, 8022, 8023, 8024, 8025, 8026, 8027, 8028, 8030, 8031, 8032, 8033, 8035, 8036, 8037, 8038, 8039, 8040, 8041, 8042, 8043, 8046, 8047, 8050, 8053, 8054, 8055, 8056, 8057, 8059, 8060, 8061, 8062, 8063, 8064, 8065, 8066, 8067, 8068, 8069, 8070, 8071, 8072, 8073, 8074, 8075, 8076, 8078, 8079, 8080, 8081, 8082, 8084, 8085, 8086, 8087, 8088, 8089, 8090, 8092, 8093, 8094, 8095, 8096, 8097, 8099, 81, 8101, 8102, 8104, 8105, 8106, 8107, 8108, 8109, 8111, 8112, 8113, 8115, 8116, 8117, 8118, 8119, 8120, 8121, 8122, 8123, 8125, 8127, 8128, 8130, 8131, 8132, 8133, 8134, 8135, 8136, 8139, 8140, 8141, 8143, 8144, 8145, 8146, 8148, 8150, 8151, 8152, 8153, 8154, 8155, 8156, 8157, 8159, 8160, 8161, 8162, 8163, 8165, 8169, 8170, 8171, 8172, 8173, 8175, 8176, 8177, 8178, 8179, 8180, 8181, 8182, 8183, 8184, 8185, 8186, 8188, 8190, 8191, 8192, 8193, 8195, 8196, 8198, 82, 8201, 8203, 8204, 8205, 8206, 8207, 8208, 8209, 8210, 8211, 8212, 8213, 8214, 8215, 8216, 8218, 8220, 8222, 8224, 8225, 8227, 8228, 8229, 8230, 8232, 8234, 8235, 8236, 8237, 8238, 8239, 8240, 8241, 8244, 8246, 8247, 8249, 8250, 8251, 8252, 8253, 8254, 8255, 8256, 8257, 8258, 8259, 8261, 8262, 8263, 8264, 8265, 8266, 8267, 8268, 8269, 8270, 8271, 8272, 8273, 8274, 8275, 8278, 8279, 8280, 8281, 8282, 8283, 8285, 8286, 8287, 8289, 8290, 8291, 8292, 8293, 8294, 8296, 8297, 8298, 8299, 83, 8300, 8301, 8302, 8303, 8304, 8305, 8306, 8307, 8311, 8312, 8313, 8314, 8315, 8316, 8317, 8318, 8319, 8320, 8321, 8322, 8323, 8324, 8325, 8326, 8327, 8328, 8330, 8331, 8333, 8337, 8338, 8340, 8341, 8342, 8343, 8344, 8345, 8346, 8347, 8348, 8349, 8350, 8351, 8353, 8354, 8355, 8356, 8357, 8358, 8359, 8360, 8361, 8362, 8364, 8365, 8366, 8367, 8368, 8369, 8370, 8371, 8372, 8373, 8374, 8375, 8376, 8377, 8378, 8379, 8380, 8381, 8382, 8383, 8386, 8387, 8389, 8390, 8391, 8392, 8393, 8394, 8395, 8396, 8397, 8398, 8399, 84, 8400, 8401, 8402, 8403, 8404, 8405, 8406, 8407, 8408, 8409, 8410, 8411, 8412, 8413, 8414, 8415, 8416, 8417, 8418, 8419, 8420, 8421, 8422, 8423, 8424, 8425, 8426, 8427, 8428, 8429, 8430, 8431, 8432, 8433, 8434, 8435, 8436, 8437, 8438, 8439, 8440, 8441, 8442, 8443, 8444, 8445, 8446, 8447, 8448, 8449, 8450, 8451, 8452, 8453, 8454, 8455, 8456, 8457, 8458, 8459, 8460, 8461, 8462, 8463, 8464, 8465, 8466, 8467, 8468, 8469, 8470, 8471, 8472, 8473, 8474, 8475, 8476, 8477, 8478, 8479, 8480, 8481, 8482, 8483, 8484, 8485, 8486, 8487, 8488, 8489, 8490, 8491, 8492, 8493, 8494, 8495, 8496, 8497, 8498, 8499, 85, 8500, 8501, 8502, 8503, 8504, 8505, 8506, 8507, 8508, 8509, 8510, 8511, 8512, 8513, 8514, 8515, 8516, 8517, 8518, 8519, 8520, 8521, 8522, 8523, 8524, 8525, 8526, 8527, 8528, 8529, 8530, 8531, 8532, 8533, 8534, 8535, 8536, 8537, 8538, 8539, 8540, 8541, 8542, 8543, 8545, 8546, 8547, 8548, 8549, 8550, 8551, 8552, 8553, 8554, 8555, 8556, 8557, 8558, 8559, 8560, 8561, 8562, 8563, 8564, 8565, 8566, 8567, 8568, 8569, 8570, 8571, 8572, 8573, 8574, 8575, 8576, 8577, 8578, 8579, 8580, 8581, 8582, 8583, 8584, 8585, 8586, 8587,
//            8588, 8589, 8590, 8591, 8592, 8593, 8594, 8595, 8596, 8597, 8598, 8599, 86, 8600, 8601, 8602, 8603, 8604, 87, 88, 89, 9, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99)
//
//                  val logger = new SRLogger("new_migration_prospects_emails")
//
//                  val migration_res: Future[Unit] = Future
//                    .sequence(
//                      migration_teamIds.map { teamId =>
//                        MigrateProspectEmails.do_migrate(
//                          teamId, ecProspect
//                        )
//                      }
//                    )(implicitly, executor = ecTeam)
//                    .map(res => {
//                      logger.info("Migration completed for all teamIds")
//                    })(ecTeam)
//                    .recover { case e =>
//
//                      logger.info(s"ERROR while migrating: ${e}")
//                    }(ecTeam)
//        */
//
//        case "campaign_email_sending_status" =>
//        /*
//      case class CampaignForMigration(
//                                     campaign_id: Long,
//                                     org_id: Long,
//                                     team_id: Long
//                                     )
//        object CampaignForMigration{
//          def fromDb(rs: WrappedResultSet) ={
//            CampaignForMigration(
//              campaign_id = rs.long("campaign_id"),
//              org_id = rs.long("org_id")
//              team_id = rs.long("team_id")
//              )
//          }
//        }
//
//        val lowestCampaignId = DB readOnly{ implicit session =>
//          sql"""SELECT id FROM campaigns
//               ORDER BY id ASC
//               LIMIT 1
//               """
//            .map(rs => rs.long("id"))
//            .single
//            .apply()
//            .get
//        }
//        var currentStartingId = lowestCampaignId
//        val limit = 1000
//        var totalNumberOfCampaignInTheCycle = limit
//        var totalMigrated = 0
//        while (totalNumberOfCampaignInTheCycle == limit) {
//          val campaignsToBeAddedInEmailSendStatus = DB readOnly{ implicit session =>
//            sql"""SELECT campaigns.id campaign_id, accounts.org_id org_id, campaigns.team_id team_id
//          FROM campaigns
//          INNER JOIN accounts on accounts.id = campaigns.account_id
//          AND campaigns.id >= $currentStartingId
//          ORDER BY campaigns.id
//          LIMIT $limit
//          """
//              .map(rs => CampaignForMigration.fromDb(rs))
//              .list
//              .apply()
//          }
//          totalNumberOfCampaignInTheCycle = campaignsToBeAddedInEmailSendStatus.length
//          if (totalNumberOfCampaignInTheCycle == limit){
//            currentStartingId = campaignsToBeAddedInEmailSendStatus.maxBy(_.campaign_id).campaign_id + 1
//          }
//          logger.info(s"campaignsToBeAddedInEmailSendStatus - ${campaignsToBeAddedInEmailSendStatus.length}")
//          if (campaignsToBeAddedInEmailSendStatus.isEmpty) {
//            logger.info("Nothing to add")
//          } else {
//            var valueParameters = List[Any]()
//            val valuePlaceholder: SQLSyntax = campaignsToBeAddedInEmailSendStatus
//              .map(c => {
//                valueParameters = valueParameters ::: List(
//                  c.campaign_id,
//                  c.org_id,
//                  c.team_id,
//                  EmailSendingEntityType.Campaign.toString,
//                  EmailSendStatus.Allowed.toString
//                )
//                sqls"""
//           (
//              ?,
//              ?,
//              ?,
//              ?,
//              ?
//            )
//          """
//              })
//              .reduce((vp1, vp2) => sqls"$vp1, $vp2")
//            val addedCampaign = DB autoCommit { implicit session =>
//              sql"""
//              INSERT INTO email_send_statuses (
//              campaign_id,
//              org_id,
//              team_id,
//              entity_type,
//              email_send_status
//              )
//               SELECT
//                campaign_id,
//              org_id,
//              team_id,
//              entity_type,
//              email_send_status
//               FROM (VALUES $valuePlaceholder)
//               AS campaigns(
//              campaign_id,
//              org_id,
//              team_id,
//              entity_type,
//              email_send_status
//            )
//            ON CONFLICT DO NOTHING
//               RETURNING id;
//            """
//                .bind(valueParameters*)
//                .map(rs => rs.long("id"))
//                .list.apply()
//            }
//            logger.info(s"addedCampaign - ${addedCampaign.length}")
//            totalMigrated = totalMigrated + addedCampaign.length
//          }
//        }
//        logger.info(s"totalMigrated - ${totalMigrated}")
//
//
//         */
//
//        case "migration_reply_sentiment" =>
//        /*
//        val srUuidUtils = new SrUuidUtils
//
//        val teamIdFromTeamInboxInDb = DB readOnly{ implicit session =>
//          sql"""SELECT es.team_id
//        FROM team_inbox ti
//        INNER JOIN email_settings es on es.id = ti.email_setting_id
//        WHERE (select count(*) from reply_sentiments_for_teams where team_id = es.team_id) = 0;
//        """
//            .map(_.long("team_id"))
//            .list
//            .apply()
//        }
//
//        if (teamIdFromTeamInboxInDb.isEmpty) {
//          logger.info("Nothing to add")
//        } else {
//          var valueParameters = List[Any]()
//          val valuePlaceholder: SQLSyntax = teamIdFromTeamInboxInDb
//            .flatMap(team_id =>  TeamInboxService.defaultReplySentimentTypes.map {replySentiment =>
//              valueParameters = valueParameters ::: List(
//                srUuidUtils.generateReplySentimentUuid,
//              team_id,
//                replySentiment.toString,
//                replySentiment.getSubcategoryName
//              )
//              sqls"""
//         (
//            ?,
//            ?,
//            ?,
//            ?
//          )
//        """
//            })
//            .reduce((vp1, vp2) => sqls"$vp1, $vp2")
//          val addedReplySentiment = DB autoCommit { implicit session =>
//            sql"""
//            INSERT INTO reply_sentiments_for_teams
//              (
//              uuid,
//                team_id,
//                reply_sentiment_type,
//                reply_sentiment_name
//              )
//             SELECT
//             uuid,
//              team_id,
//              reply_sentiment_type,
//              reply_sentiment_name
//             FROM (VALUES $valuePlaceholder)
//             AS temp(
//             uuid,
//             team_id,
//              reply_sentiment_type,
//              reply_sentiment_name
//          )
//          ON CONFLICT DO NOTHING
//             RETURNING id;
//          """
//              .bind(valueParameters*)
//              .map(rs => rs.long("id"))
//              .list.apply()
//          }
//          logger.info(s"added reply sentiment - ${addedReplySentiment.length}")
//        }
//        */
//
//
//        case "syncPreviousReplies" =>
//
//
//          val  system: ActorSystem = ActorSystem()
//          val  actorContext: ExecutionContext = system.dispatcher
//
//          object CSU extends TestAppTrait {
//
//            val csu = new CustomerSupportUtils(
//              mqActivityTriggerPublisher = mqActivityTriggerPublisher
//            )
//
//            def mainFn() = {
//
//              csu.syncPreviousReplies(
//                teamId = TeamId(12589),
//                accountId = AccountId(2473)
//              )
//
//
//            }
//
//          }
//
//          CSU
//            .mainFn()
//            .map(_ => logger.info("mainFn completed"))(actorContext)
//            .recover { case e => logger.error(s"mainFn error: ${LogHelpers.getStackTraceAsString(e)}") }(actorContext)
//
//
//        /*
//     case "test_read" =>
//
//       Logger.info("start test read")
//
//       MQReplyTracker.processMessage(msg = MQReplyTrackerMessage(emailSettingId = 2827, senderMessageIdSuffix = ""))
//         .map { res =>
//           Logger.info("\n\n\n\n----------\ndone\n---------\n\n\n\n\n\n")
//         }
//         .recover { case e =>
//           Logger.info(s"\n\n\n\n----------\nERROR: $e\n---------\n\n\n\n\n\n")
//
//         }
//
//       */
//
//
//        /*
//        case "tempMigrateAccountIDTeamIdByCampaignIdInProspects_events_table" =>
//
//          for (i <- 302 to 320) {
//
//            val startEsId = i * 25000
//            val endEsId = (i + 1) * 25000
//
//            Logger.info(s"$startEsId :: $endEsId :: START :: $i")
//
//            val startTime = DateTime.now()
//
//            EmailScheduled.tempMigrateAccountIDTeamIdByCampaignIdInProspects_events_table(
//              startEsId = startEsId,
//              endEsId = endEsId
//            ) match {
//
//              case Failure(e) =>
//                Logger.error(s"$startEsId :: $endEsId :: ${Helpers.getStackTraceAsString(e)}")
//
//              case Success(totalUpdated) =>
//
//                val endTime = DateTime.now()
//
//                val totalSecondsTaken = Seconds.secondsBetween(endTime, startTime)
//
//                Logger.info(s"$startEsId :: $endEsId :: DONE :: update: $totalUpdated in $totalSecondsTaken secs")
//            }
//
//          }
//
//
//        case "migrateorgtrackingkey" =>
//
//          MigrationUtils.tempMigrateOrganizationToTrackingKey() match {
//
//              case Failure(e) =>
//                Logger.error(s"ERROR ${Helpers.getStackTraceAsString(e)}")
//
//              case Success(totalUpdated) =>
//
//                Logger.info(s"DONE: ${totalUpdated}")
//            }
//
//        case "deloldem" =>
//
//          for (i <- 60 to 120) {
//
//            EmailScheduled.deleteOldIrrelevantEmails(daysAgo = i) match {
//              case Failure(e) =>
//                Logger.error(s"ERROR ${Helpers.getStackTraceAsString(e)}")
//
//              case Success(deleted) =>
//                Logger.info(s"deleted: $deleted for $i days ago")
//
//
//            }
//
//
//          }
//
//
//        case "updateaccemailthreads" =>
//
//          for (i <- 719 to 1000) {
//
//            EmailScheduled.updateAccountIdForOldEmailThreads(daysAgo = i) match {
//              case Failure(e) =>
//                Logger.error(s"ERROR ${Helpers.getStackTraceAsString(e)}")
//
//              case Success(updated) =>
//                Logger.info(s"updated: $updated for $i days ago")
//
//
//            }
//
//
//          }
//
//        case "pcp" =>
//
//          for (i <- 1 to 37) {
//
//            MigrationUtils.updatedProspectCategoryIdCustom(i = i) match {
//              case Failure(e) =>
//                Logger.error(s"ERROR ${Helpers.getStackTraceAsString(e)}")
//
//              case Success(updated) =>
//                Logger.info(s"Success  ${updated}")
//
//
//            }
//
//
//          }
//          */
//
//        /*
//        case "migrateemc" =>
//          val prospectAccountDAO1: ProspectAccountDAO1 = new ProspectAccountDAO1
//          val prospectDAO : Prospect = new Prospect(prospectAccountDAO1 = prospectAccountDAO1)
//          val migrationUtils: MigrationUtils = new MigrationUtils(prospectDAO)
//
//          val emails = migrationUtils.findEmailSettingsForMigratingEmailMsgContacts()
//
////          val emails = Seq(
////            (27, "<EMAIL>", 33),
////            (26, "<EMAIL>", 33)
////          )
//
//          for { em <- emails } {
//
//            Logger.info(s"=================================")
//            Logger.info(s"=================================")
//            Logger.info(s"starting for ${em._2}")
//            Logger.info(s"=================================")
//            Logger.info(s"=================================")
//            migrationUtils.migrateEmailMessageContacts(
//
//              inboxEmailSettingId = em._1,
//              teamId = em._3,
//              inboxEmailAddress = em._2
//
//            ) match {
//              case Failure(e) =>
//                Logger.error(s"ERROR ${Helpers.getStackTraceAsString(e)}")
//
//              case Success(deleted) =>
//                Logger.info(s"=================================")
//                Logger.info(s"=================================")
//                Logger.info(s"done with success for ${em._2}")
//                Logger.info(s"=================================")
//                Logger.info(s"=================================")
//
//
//
//            }
//          }
//          */
//
//        case "test_fix_12_dec" =>
//          val srUuidUtils = new SrUuidUtils
//          val accountDao = new AccountDAO(
//            srUuidUtils = srUuidUtils
//          )
//          val result = accountDao.getAgencyTeamLevelStats(
//            orgId = OrgId(id = 2),
//            tids = List(TeamId(id = 2), TeamId(id = 47)),
//            aid = AccountId(id = 2),
//            from = Some(DateTime.now().minusYears(2)),
//            till = Some(DateTime.now()),
//            positive_reply_sentiments = List(
//              ReplySentimentUuid("reply_sentiment_2H4hionCdu8sCQO3Z1hffYF8nwI"),
//              ReplySentimentUuid("reply_sentiment_2H4hilRVf8VwKb1jOj5nl3B1VOe"),
//              ReplySentimentUuid("reply_sentiment_2H4hioq2Fdf8fylRmc9oNS3itGh"),
//              ReplySentimentUuid("reply_sentiment_2H4hirDt3FhmQDNC0AZtccr8hS9"),
//              ReplySentimentUuid("reply_sentiment_2H4hiq6nH6TiakeRas5Nz5IUBk3"),
//              ReplySentimentUuid("reply_sentiment_2H4himxpDn4VxrQn8zYeB0H9k9i"),
//              ReplySentimentUuid("reply_sentiment_2H4hirVwytP3OBSlCnKTUOGoNJI"),
//              ReplySentimentUuid("reply_sentiment_2H4hirdgsrK8kX0fxJBzEMrilSS")
//            )
//          )
//
//          logger.info(s"RESULT ----------- $result")
//
//
//
//
//
//
//        case "enc_smtppass" =>
//          MigrateNewEncryptionKey.migrate(
//            tableName = "email_settings",
//            oldColumnName = "smtp_password",
//            newColumnName = "smtp_password_enc",
//            encKey = AppConfig.EncKeys.emailSettingsCredentialEncKey
//          ) match {
//
//            case Failure(e) =>
//              logger.error(s"FATAL: ${LogHelpers.getStackTraceAsString(e)}")
//
//            case Success(_) =>
//              logger.info("done with it")
//          }
//        case "enc_imappass" =>
//          MigrateNewEncryptionKey.migrate(
//            tableName = "email_settings",
//            oldColumnName = "imap_password",
//            newColumnName = "imap_password_enc",
//            encKey = AppConfig.EncKeys.emailSettingsCredentialEncKey
//          ) match {
//
//            case Failure(e) =>
//              logger.error(s"FATAL: ${LogHelpers.getStackTraceAsString(e)}")
//
//            case Success(_) =>
//              logger.info("done with it")
//          }
//        case "enc_api_key" =>
//          MigrateNewEncryptionKey.migrate(
//            tableName = "email_settings",
//            oldColumnName = "api_key",
//            newColumnName = "api_key_enc",
//            encKey = AppConfig.EncKeys.emailSettingsCredentialEncKey
//          ) match {
//
//            case Failure(e) =>
//              logger.error(s"FATAL: ${LogHelpers.getStackTraceAsString(e)}")
//
//            case Success(_) =>
//              logger.info("done with it")
//          }
//        case "enc_oauth2_access_token" =>
//          MigrateNewEncryptionKey.migrate(
//            tableName = "email_settings",
//            oldColumnName = "oauth2_access_token",
//            newColumnName = "oauth2_access_token_enc",
//            encKey = AppConfig.EncKeys.emailSettingsCredentialEncKey
//          ) match {
//
//            case Failure(e) =>
//              logger.error(s"FATAL: ${LogHelpers.getStackTraceAsString(e)}")
//
//            case Success(_) =>
//              logger.info("done with it")
//          }
//        case "enc_oauth2_refresh_token" =>
//          MigrateNewEncryptionKey.migrate(
//            tableName = "email_settings",
//            oldColumnName = "oauth2_refresh_token",
//            newColumnName = "oauth2_refresh_token_enc",
//            encKey = AppConfig.EncKeys.emailSettingsCredentialEncKey
//          ) match {
//
//            case Failure(e) =>
//              logger.error(s"FATAL: ${LogHelpers.getStackTraceAsString(e)}")
//
//            case Success(_) =>
//              logger.info("done with it")
//          }
//        case "enc_email_dataplatform" =>
//          val columns = List("uplead_api_key",
//            "hunter_api_key",
//            "dropcontact_api_key",
//            "anymailfinder_api_key",
//            "clearbit_api_key",
//            "aeroleads_api_key")
//
//          columns.foreach { col =>
//            MigrateNewEncryptionKey.migrate(
//              tableName = "teams",
//              oldColumnName = col,
//              newColumnName = s"${col}_enc",
//              encKey = AppConfig.EncKeys.emailDataplatformCredentialEncKey
//            ) match {
//
//              case Failure(e) =>
//                logger.error(s"FATAL: ${LogHelpers.getStackTraceAsString(e)}")
//
//              case Success(_) =>
//                logger.info("done with it")
//            }
//          }
//        case "enc_workflow_crm_settings" =>
//          val columns = List("oauth2_refresh_token",
//            "oauth2_access_token")
//
//          columns.foreach { col =>
//            MigrateNewEncryptionKey.migrate(
//              tableName = "workflow_crm_settings",
//              oldColumnName = col,
//              newColumnName = s"${col}_enc",
//              encKey = AppConfig.EncKeys.workflowCRMSettingsCredentialEncKey
//            ) match {
//
//              case Failure(e) =>
//                logger.error(s"FATAL: ${LogHelpers.getStackTraceAsString(e)}")
//
//              case Success(_) =>
//                logger.info("done with it")
//            }
//          }
//        case "enc_user_api_key" =>
//          MigrateNewEncryptionKey.migrate(
//            tableName = "accounts",
//            oldColumnName = "user_api_key",
//            newColumnName = "user_api_key_enc",
//            encKey = AppConfig.EncKeys.accountsCredentialEncKey
//          ) match {
//
//            case Failure(e) =>
//              logger.error(s"FATAL: ${LogHelpers.getStackTraceAsString(e)}")
//
//            case Success(_) =>
//              logger.info("done with it")
//          }
//        case "enc_sr_pd_api_key" =>
//          MigrateNewEncryptionKey.migrate(
//            tableName = "accounts",
//            oldColumnName = "sr_pd_api_key",
//            newColumnName = "sr_pd_api_key_enc",
//            encKey = AppConfig.EncKeys.accountsCredentialEncKey
//          ) match {
//
//            case Failure(e) =>
//              logger.error(s"FATAL: ${LogHelpers.getStackTraceAsString(e)}")
//
//            case Success(_) =>
//              logger.info("done with it")
//          }
//
//        case "enc_gauthkey" =>
//          MigrateNewEncryptionKey.migrate(
//            tableName = "accounts",
//            oldColumnName = "gauthkey",
//            newColumnName = "gauthkey_enc",
//            encKey = AppConfig.EncKeys.accountsCredentialEncKey
//          ) match {
//
//            case Failure(e) =>
//              logger.error(s"FATAL: ${LogHelpers.getStackTraceAsString(e)}")
//
//            case Success(_) =>
//              logger.info("done with it")
//          }
//
//        case "enc_dkim_records" =>
//          MigrateNewEncryptionKey.migrate(
//            tableName = "dkim_records",
//            oldColumnName = "private_key",
//            newColumnName = "private_key_enc",
//            encKey = AppConfig.EncKeys.dkimRecordsCredentialEncKey
//          ) match {
//
//            case Failure(e) =>
//              logger.error(s"FATAL: ${LogHelpers.getStackTraceAsString(e)}")
//
//            case Success(_) =>
//              logger.info("done with it")
//          }
//        case "enc_teams_accounts" =>
//          MigrateNewEncryptionKey.migrate(
//            tableName = "teams_accounts",
//            oldColumnName = "api_key",
//            newColumnName = "api_key_enc",
//            encKey = AppConfig.EncKeys.teamsAccountCredentialEncKey
//          ) match {
//
//            case Failure(e) =>
//              logger.error(s"FATAL: ${LogHelpers.getStackTraceAsString(e)}")
//
//            case Success(_) =>
//              logger.info("done with it")
//          }
//        case "enc_organizations" =>
//          MigrateNewEncryptionKey.migrate(
//            tableName = "organizations",
//            oldColumnName = "warmupbox_key",
//            newColumnName = "warmupbox_key_enc",
//            encKey = AppConfig.EncKeys.organizationsCredentialEncKey
//          ) match {
//
//            case Failure(e) =>
//              logger.error(s"FATAL: ${LogHelpers.getStackTraceAsString(e)}")
//
//            case Success(_) =>
//              logger.info("done with it")
//          }
//
//
//
//        case "migrate_prospect_category_correctly_actual_update" =>
//
//          given logger: SRLogger = new SRLogger("migrate_prospect_category_correctly :: migrating :: ")
//
//          object main extends TestAppTrait {
//            def init() = {
//              migrationUtils.migrateProspectsToCorrectCategory(
//                only_log = false
//              )
//            }
//          }
//          val res = main.init()
//          res match {
//
//            case Failure(err) =>
//              logger.error(s"Testapp error migrate_prospect_category_correctly_actual_update : ${err}", err)
//
//            case Success(data) =>
//
//              logger.info(s"Testapp migrate_prospect_category_correctly_actual_update : ${data}")
//
//          }
//
//
//        case "migrate_latest_sent_by_admin_at" =>
//
//          object main extends TestAppTrait {
//            def init() = {
//              migrationUtils.migrateSentEmailsFromAdminToSentFolder(
//                teamId = TeamId(id = 16266)
//              )
//            }
//          }
//          main.init()
//
//        case "migrate_prospect_category_correctly_only_log" =>
//
//          given logger: SRLogger = new SRLogger("migrate_prospect_category_correctly :: migrating :: ")
//
//          object main extends TestAppTrait {
//            def init() = {
//              migrationUtils.migrateProspectsToCorrectCategory(
//                only_log = true
//              )
//            }
//          }
//          val res = main.init()
//          res match {
//
//            case Failure(err) =>
//
//              logger.error(s"Testapp error migrate_prospect_category_correctly_only_log : ${err}", err)
//
//
//            case Success(data) =>
//              logger.info(s"Testapp error migrate_prospect_category_correctly_only_log : ${data}")
//
//          }
//
//
//
//
//        case "update_uuid" =>
//
//          implicit val ec: ExecutionContextExecutor = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(20))
//          given logger: SRLogger = new SRLogger(logRequestId = "TestApp.emails_scheduled_uuid_fix ::")
//
//          object main extends MqUuidMigrationScript_DI
//            with TestAppTrait
//            with SrUuidUtilsDI
//            with DBUtils_DI {
//
//
//            def init(
//              table_name: Option[TableNames],
//              active_teams: Boolean
//            )(using logger: SRLogger) = Try {
//
//
//              table_name match {
//                case Some(name) =>
//                  name match {
//                    case TableNames.EmailsScheduled =>
//                      migrationFunc.getTeamIdsInTable(tableNames = name, active_teams = active_teams) match {
//
//                        case Failure(exception) => Failure(exception = exception)
//
//                        case Success(team_ids) => team_ids.map(team_id =>
//                          mqUpdateUuidMigrationScript.publish(UpdateUuidMigrationData(
//                            teamId = team_id,
//                            table_name = name
//                          ))
//                        )
//                      }
//
//                    case TableNames.ProspectAccounts =>
//                      migrationFunc.getTeamIdsInTable(tableNames = name, active_teams = active_teams) match {
//
//                        case Failure(exception) => Failure(exception = exception)
//
//                        case Success(team_ids) => team_ids.map(team_id =>
//                          mqUpdateUuidMigrationScript.publish(UpdateUuidMigrationData(
//                            teamId = team_id,
//                            table_name = name
//                          ))
//                        )
//                      }
//                    case _ =>
//                      Failure(throw new Exception("Update uuid not enable for the selected table"))
//                  }
//                case None => Failure(throw new Exception("Wrong number input"))
//              }
//            }
//          }
//
//
//          print(
//            """Run migration for:
//              |1.emails_scheduled-active_teams
//              |2.emails_scheduled-inactive_teams
//              |3.prospect_accounts-active_teams
//              |4.prospect_accounts-inactive_teams
//              |Enter the number: """.stripMargin)
//
//          var choice = scala.io.StdIn.readInt()
//
//          logger.info(s"choice::: $choice")
//
//          val (table_name: Option[TableNames], active_teams: Boolean) = if (choice == 1) {
//            (Some(TableNames.EmailsScheduled), true)
//          } else if (choice == 2) {
//            (Some(TableNames.EmailsScheduled), false)
//          } else if (choice == 3) {
//            (Some(TableNames.ProspectAccounts), true)
//          } else if (choice == 4) {
//            (Some(TableNames.ProspectAccounts), false)
//          } else {
//            (None, false)
//          }
//
//          main.init(active_teams = active_teams, table_name = table_name) match {
//            case Success(_) => logger.info("success")
//            case Failure(exception) => logger.error("error", err = exception)
//          }
//
//        case "Add_updated_at_to_table" =>
//
//          object main extends MqUpdatedAtMigrationScript_DI
//            with MigrationFuncDI
//            with TestAppTrait
//            with SrUuidUtilsDI
//            with DBUtils_DI {
//            def init(
//              table_name: Option[TableNames],
//              active_teams: Boolean
//            )(using logger: SRLogger): Try[Seq[Unit]] = {
//              table_name match {
//                case Some(name) =>
//                  name match {
//                    case TableNames.ProspectAccounts =>
//                      migrationFunc.getTeamIdsInTable(tableNames = name, active_teams = active_teams) match {
//
//                        case Failure(exception) => Failure(exception = exception)
//
//                        case Success(team_ids) =>
//                          val x = team_ids.map(tid => {
//                            migrationFunc.checkIfUpdatedAtNullForTeam(
//                              teamId = tid,
//                              tableNames = name
//                            ).flatMap(shouldBePublished => {
//                              if (shouldBePublished) {
//                                mqUpdatedAtMigrationScript.publish(UpdatedAtMigrationData(
//                                  table_name = name, teamId = Some(tid))
//                                )
//                              }
//                              else {
//                                logger.info(s"Skipping team: ${tid.id}")
//                                Success((): Unit)
//                              }
//                            })
//                          })
//                          Helpers.seqTryToTrySeq(x)
//                      }
//                    case _ => Failure(throw new Exception("Table currently not added for updated_at"))
//                  }
//                case None => Failure(throw new Exception("Wrong number input"))
//              }
//            }
//          }
//          implicit val ec: ExecutionContextExecutor = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(20))
//          given logger: SRLogger = new SRLogger(logRequestId = "TestApp.UpdatedAt ::")
//
//          print(
//            """Run migration for:
//              |1.prospect_accounts-active_teams
//              |2.prospect_accounts-inactive_teams
//              |Enter the number: """.stripMargin)
//
//          var choice = scala.io.StdIn.readInt()
//
//          logger.info(s"choice::: $choice")
//
//          val (table_name: Option[TableNames], active_teams: Boolean) =
//            if (choice == 1) {
//              (Some(TableNames.ProspectAccounts), true)
//            } else if (choice == 2) {
//              (Some(TableNames.ProspectAccounts), false)
//            } else {
//              (None, false)
//            }
//
//          main.init(table_name = table_name, active_teams = active_teams) match {
//            case Success(_) => logger.info("success")
//            case Failure(exception) => logger.error("error", err = exception)
//          }
//
//
//        case "add_team_id_for_campaigns_prospects" =>
//          object main extends MqCampaignsProspectsMigrationScript_DI
//            with MigrationFuncDI
//            with TestAppTrait
//            with SrUuidUtilsDI
//            with DBUtils_DI {
//
//
//            def init(
//              active_teams: Boolean
//            )(using logger: SRLogger): Try[Seq[Unit]] = {
//
//
//              migrationFunc.getCampaignIdsInTable(
//                //tableNames = "campaigns_prospects",
//                active_teams = false) match {
//
//                case Failure(exception) => Failure(exception = exception)
//
//                case Success(campaign_ids) =>
//                  //                    mqCampaignsProspectsMigrationScript.publish(CampaignIdMigrationData(
//                  //                      table_name = "campaigns_prospects", campaignId = campaign_ids)).map(x => Seq(x))
//
//                  val x = campaign_ids.map(migration_data =>
//                    migrationFunc.checkIfTeamIdIsNullForCampaign(campaign_id = migration_data.campaignId)
//                      .flatMap(shouldBePublished => {
//                        if (shouldBePublished) {
//                          logger.info(s"publishing campaign:: ${migration_data.campaignId}")
//                          mqCampaignsProspectsMigrationScript.publish(
//
//                            //table_name = "campaigns_prospects",
//                            migration_data
//                          )
//                        } else {
//                          Success((): Unit)
//                        }
//                      })
//                  )
//
//                  Helpers.seqTryToTrySeq(x)
//              }
//
//            }
//          }
//
//          implicit val ec: ExecutionContextExecutor = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(2))
//          given logger: SRLogger = new SRLogger(logRequestId = "TestApp.add_team_id_for_campaigns_prospects ::")
//
//          main.init(active_teams = true) match {
//            case Success(_) => logger.info("success")
//            case Failure(exception) => logger.error("error", err = exception)
//          }
//
//
//        case "add_uuid_for_tables" =>
//
//
//          object main extends MqUuidMigrationScript_DI
//            with TestAppTrait
//            with SrUuidUtilsDI
//            with DBUtils_DI {
//
//
//            def init(
//              table_name: Option[TableNames],
//              active_teams: Boolean
//            )(using logger: SRLogger): Try[Seq[Unit]] = {
//
//              table_name match {
//                case Some(name) =>
//                  name match {
//
//                    case TableNames.Accounts =>
//                      mqUuidMigrationScript.publish(UuidMigrationData(
//                        table_name = name, teamId = None)).map(x => Seq(x))
//
//                    case TableNames.Teams =>
//                      mqUuidMigrationScript.publish(UuidMigrationData(
//                        table_name = name, teamId = None)).map(x => Seq(x))
//
//                    case TableNames.Campaigns =>
//                      mqUuidMigrationScript.publish(UuidMigrationData(
//                        table_name = name, teamId = None)).map(x => Seq(x))
//
//                    case TableNames.LinkedinMessageThreads =>
//                      mqUuidMigrationScript.publish(UuidMigrationData(
//                        table_name = name, teamId = None)).map(x => Seq(x))
//
//                    case TableNames.LinkedinMessages =>
//                      mqUuidMigrationScript.publish(UuidMigrationData(
//                        table_name = name, teamId = None)).map(x => Seq(x))
//
//                    case TableNames.Tags =>
//                      mqUuidMigrationScript.publish(UuidMigrationData(
//                        table_name = name, teamId = None
//                      )).map(x => Seq(x))
//
//                    case TableNames.TagsListForCampaigns =>
//                      mqUuidMigrationScript.publish(UuidMigrationData(
//                        table_name = name, teamId = None
//                      )).map(x => Seq(x))
//
//                    case TableNames.EmailThreads =>
//
//                      migrationFunc.getTeamIdsInTable(tableNames = name, active_teams = active_teams) match {
//
//                        case Failure(exception) => Failure(exception = exception)
//
//                        case Success(team_ids) =>
//
//                          val x = team_ids.map(tid => {
//                            migrationFunc.checkIfUuidsAreNullForTeam(
//                              teamId = tid,
//                              tableNames = TableNames.EmailThreads
//                            ).flatMap(shouldBePublished => {
//                              if (shouldBePublished) {
//                                mqUuidMigrationScript.publish(UuidMigrationData(
//                                  table_name = name, teamId = Some(tid))
//                                )
//                              }
//                              else {
//                                logger.info(s"Skipping team: ${tid.id}")
//                                Success((): Unit)
//                              }
//                            })
//                          })
//
//                          Helpers.seqTryToTrySeq(x)
//                      }
//
//                    case TableNames.Prospects =>
//
//                      migrationFunc.getTeamIdsInTable(tableNames = name, active_teams = active_teams) match {
//
//                        case Failure(exception) => Failure(exception = exception)
//
//                        case Success(team_ids) =>
//
//                          val x = team_ids.map(tid => {
//                            migrationFunc.checkIfUuidsAreNullForTeam(
//                              teamId = tid,
//                              tableNames = TableNames.Prospects
//                            ).flatMap(shouldBePublished => {
//                              if (shouldBePublished) {
//                                logger.info(s"pushing team : ${tid.id}")
//                                mqUuidMigrationScript.publish(UuidMigrationData(
//                                  table_name = name, teamId = Some(tid))
//                                )
//                              }
//                              else {
//                                //                                logger.info(s"Skipping team: ${tid.id}")
//                                Success((): Unit)
//                              }
//                            })
//                          })
//
//                          Helpers.seqTryToTrySeq(x)
//                      }
//
//                    case TableNames.EmailsScheduled =>
//
//                      migrationFunc.getTeamIdsInTable(tableNames = name, active_teams = active_teams) match {
//
//                        case Failure(exception) => Failure(exception = exception)
//
//                        case Success(team_ids) =>
//                          val x = team_ids.map(tid => {
//                            migrationFunc.checkIfUuidsAreNullForTeam(
//                              teamId = tid,
//                              tableNames = TableNames.EmailsScheduled
//                            ).flatMap(shouldBePublished => {
//                              if (shouldBePublished) {
//                                mqUuidMigrationScript.publish(UuidMigrationData(
//                                  table_name = name, teamId = Some(tid))
//                                )
//                              }
//                              else {
//                                logger.info(s"Skipping team: ${tid.id}")
//                                Success((): Unit)
//                              }
//                            })
//                          })
//                          Helpers.seqTryToTrySeq(x)
//                      }
//
//                    case TableNames.Blacklist =>
//
//                      migrationFunc.getTeamIdsInTable(tableNames = name, active_teams = active_teams) match {
//
//                        case Failure(exception) => Failure(exception = exception)
//
//                        case Success(team_ids) =>
//                          val x = team_ids.map(tid => {
//                            migrationFunc.checkIfUuidsAreNullForTeam(
//                              teamId = tid,
//                              tableNames = name
//                            ).flatMap(shouldBePublished => {
//                              if (shouldBePublished) {
//                                mqUuidMigrationScript.publish(UuidMigrationData(
//                                  table_name = name, teamId = Some(tid))
//                                )
//                              }
//                              else {
//                                logger.info(s"Skipping team: ${tid.id}")
//                                Success((): Unit)
//                              }
//                            })
//                          })
//                          Helpers.seqTryToTrySeq(x)
//                      }
//
//                    case TableNames.EmailSettings =>
//                      migrationFunc.getTeamIdsInTable(tableNames = name, active_teams = active_teams) match {
//
//                        case Failure(exception) => Failure(exception = exception)
//
//                        case Success(team_ids) =>
//                          val x = team_ids.map(tid => {
//                            migrationFunc.checkIfUuidsAreNullForTeam(
//                              teamId = tid,
//                              tableNames = name
//                            ).flatMap(shouldBePublished => {
//                              if (shouldBePublished) {
//                                mqUuidMigrationScript.publish(UuidMigrationData(
//                                  table_name = name, teamId = Some(tid))
//                                )
//                              }
//                              else {
//                                logger.info(s"Skipping team: ${tid.id}")
//                                Success((): Unit)
//                              }
//                            })
//                          })
//                          Helpers.seqTryToTrySeq(x)
//                      }
//
//                    case TableNames.Organizations =>
//                      mqUuidMigrationScript.publish(UuidMigrationData(
//                        table_name = name, teamId = None
//                      )).map(x => Seq(x))
//
//                    case TableNames.ProspectAccounts =>
//                      migrationFunc.getTeamIdsInTable(tableNames = name, active_teams = active_teams) match {
//
//                        case Failure(exception) => Failure(exception = exception)
//
//                        case Success(team_ids) =>
//                          val x = team_ids.map(tid => {
//                            migrationFunc.checkIfUuidsAreNullForTeam(
//                              teamId = tid,
//                              tableNames = name
//                            ).flatMap(shouldBePublished => {
//                              if (shouldBePublished) {
//                                mqUuidMigrationScript.publish(UuidMigrationData(
//                                  table_name = name, teamId = Some(tid))
//                                )
//                              }
//                              else {
//                                logger.info(s"Skipping team: ${tid.id}")
//                                Success((): Unit)
//                              }
//                            })
//                          })
//                          Helpers.seqTryToTrySeq(x)
//                      }
//                    case TableNames.CampaignsProspects =>
//                      Failure(throw new Exception("Wrong number table CampaignsProspects"))
//                  }
//                case None => Failure(throw new Exception("Wrong number input"))
//
//              }
//            }
//          }
//
//          implicit val ec: ExecutionContextExecutor = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(20))
//          given logger: SRLogger = new SRLogger(logRequestId = "TestApp.UuidAddition ::")
//
//          print(
//            """Run migration for:
//              |1.teams
//              |2.accounts
//              |3.campaigns
//              |4.linkedin_message_threads
//              |5.email_threads-active_teams
//              |6.email_threads-inactive_teams
//              |7.emails_scheduled-active_teams
//              |8.emails_scheduled-inactive_teams
//              |9.tags
//              |10.tags_list_for_campaigns
//              |11.linkedin_messages
//              |12.prospects-active_teams
//              |13.prospects-inactive_teams
//              |14.blacklist-active_teams
//              |15.blacklist-inactive_teams
//              |16.email_settings-active_teams
//              |17.email-settings-inactive_teams
//              |18.organizations
//              |19.prospect_accounts-active_teams
//              |20.prospect_accounts-inactive_teams
//              |Enter the number: """.stripMargin)
//
//          var choice = scala.io.StdIn.readInt()
//
//          logger.info(s"choice::: $choice")
//
//          val (table_name: Option[TableNames], active_teams: Boolean) = if (choice == 1) {
//            (Some(TableNames.Teams), true)
//          } else if (choice == 2) {
//            (Some(TableNames.Accounts), true)
//          } else if (choice == 3) {
//            (Some(TableNames.Campaigns), true)
//          } else if (choice == 4) {
//            (Some(TableNames.LinkedinMessageThreads), true)
//          } else if (choice == 5) {
//            (Some(TableNames.EmailThreads), true)
//          } else if (choice == 6) {
//            (Some(TableNames.EmailThreads), false)
//          } else if (choice == 7) {
//            (Some(TableNames.EmailsScheduled), true)
//          } else if (choice == 8) {
//            (Some(TableNames.EmailsScheduled), false)
//          } else if (choice == 9) {
//            (Some(TableNames.Tags), true)
//          } else if (choice == 10) {
//            (Some(TableNames.TagsListForCampaigns), true)
//          } else if (choice == 11) {
//            (Some(TableNames.LinkedinMessages), true)
//          } else if (choice == 12) {
//            (Some(TableNames.Prospects), true)
//          } else if (choice == 13) {
//            (Some(TableNames.Prospects), false)
//          } else if (choice == 14) {
//            (Some(TableNames.Blacklist), true)
//          } else if (choice == 15) {
//            (Some(TableNames.Blacklist), false)
//          } else if (choice == 16) {
//            (Some(TableNames.EmailSettings), true)
//          } else if (choice == 17) {
//            (Some(TableNames.EmailSettings), false)
//          } else if (choice == 18) {
//            (Some(TableNames.Organizations), true)
//          } else if (choice == 19) {
//            (Some(TableNames.ProspectAccounts), true)
//          } else if (choice == 20) {
//            (Some(TableNames.ProspectAccounts), false)
//          } else {
//            (None, false)
//          }
//
//          main.init(table_name = table_name, active_teams = active_teams) match {
//            case Success(_) => logger.info("success")
//            case Failure(exception) => logger.error("error", err = exception)
//          }
//
//
//        case "runInternalTeamBasedMigration" =>
//
//          val logger = new SRLogger("runInternalTeamBasedMigration")
//
//          object main extends TestAppTrait {
//
//            def init() = {
//
//              teamService.getTeamsOrderByOrgPlanType match {
//                case Success(teams) =>
//                  teams.map { teamId =>
//
//
//                    mqInternalTeamBasedMigration.publish(teamId) match {
//                      case Success(value) => logger.info(s"Published tid_$teamId for replySentimentAdditionForTeam")
//                      case Failure(exception) => logger.fatal(s"failed to publish tid_$teamId for replySentimentAdditionForTeam", exception)
//                    }
//                  }
//                case Failure(exception) => logger.fatal(s"Error while getTeamsForNotAllSavedMQ", exception)
//              }
//
//            }
//
//          }
//
//          val res = main.init()
//          println(s"main : $res")
//
//
//        case "runInternalTeamBasedMigrationV2" =>
//
//          val logger = new SRLogger("runInternalTeamBasedMigrationV2")
//
//          object main extends TestAppTrait {
//
//            def init() = {
//
//              teamService.getAllActiveInactiveTeamsAcrossOrgForMigration match {
//                case Success(teams) =>
//                  teams.map { teamId =>
//
//
//                    mqInternalTeamBasedMigration.publish(teamId) match {
//                      case Success(value) => logger.info(s"Published tid_$teamId for replySentimentAdditionForTeam")
//                      case Failure(exception) => logger.fatal(s"failed to publish tid_$teamId for replySentimentAdditionForTeam", exception)
//                    }
//                  }
//                case Failure(exception) => logger.fatal(s"Error while getTeamsForNotAllSavedMQ", exception)
//              }
//
//            }
//
//          }
//
//          val res = main.init()
//          println(s"main : $res")
//
//
//        /*
//       case "campaignWiseMigrationForCampaignProspectStepId" =>
//
//         given Logger = new SRLogger(s"campaignWiseMigrationForCampaignProspectStepId")
//
//         val prospectColumnDef = new ProspectColumnDef(new ProspectColumnDefDAO)
//         val prospectQuery = new ProspectQuery(
//           prospectColumnDef = prospectColumnDef
//         )
//         val prospectAddEventDAO = new ProspectAddEventDAO
//         val freeEmailDomainListDAO = new FreeEmailDomainListDAO
//         val cacheServiceJedis = new CacheServiceJedis
//         val campaignProspectDAO_2 = new CampaignProspectDAO_2
//         val prospectsEmailsDAO = new ProspectsEmailsDAO
//         val campaignSchedulingMetadataDAO = new CampaignSchedulingMetadataDAO
//
//         val mergeTagService = new MergeTagService(
//           campaignProspectDAO_2 = campaignProspectDAO_2,
//           campaignSchedulingMetadataDAO = campaignSchedulingMetadataDAO
//         )
//         val replySentimentDAO = new ReplySentimentDAO
//         val replySentimentJedisDAO = new ReplySentimentJedisDAO(
//           cacheServiceJedis = cacheServiceJedis
//         )
//         val replySentimentDAOService = new ReplySentimentDAOService(
//           replySentimentDAO = replySentimentDAO,
//           replySentimentJedisDAO = replySentimentJedisDAO
//         )
//
//         val prospectDAO = new ProspectDAO
//         val dbUtils = new DBUtils
//         val emailThreadDAO = new EmailThreadDAO
//
//         val dbCounterDAO = new DBCounterDAO(cacheServiceJedis)
//         val srDBQueryCounterService = new SrDBQueryCounterService(dbCounterDAO = dbCounterDAO)
//
//         val taskPgDAO = new TaskPgDAO(
//           srDBQueryCounterService = srDBQueryCounterService
//         )
//
//         val linkedinTaskService = new LinkedinTaskService(
//           taskPgDAO = taskPgDAO
//         )
//
//         val prospectDAOService = new ProspectDAOService(
//           prospectDAO = prospectDAO,
//           prospectQuery = prospectQuery,
//           linkedinTaskService = linkedinTaskService,
//           prospectColumnDef = prospectColumnDef,
//           mergeTagService = mergeTagService,
//           replySentimentDAOService = replySentimentDAOService,
//           emailThreadDAO = emailThreadDAO,
//           dbUtils = dbUtils
//         )
//
//         val emailValidationModel = new EmailValidationModel
//
//         val accountOrgBillingRelatedInfoDAO = new AccountOrgBillingRelatedInfoDAO()
//
//         val accountOrgBillingRelatedService = new AccountOrgBillingRelatedService(accountOrgBillingRelatedInfoDAO)
//
//         val prospectServiceV2 = new ProspectServiceV2(
//           prospectDAOService = prospectDAOService,
//           prospectsEmailsDAO = prospectsEmailsDAO,
//           prospectAddEventDAO = prospectAddEventDAO,
//           prospectColumnDef = prospectColumnDef,
//           campaignProspectDAO_2 = campaignProspectDAO_2,
//           accountOrgBillingRelatedService = accountOrgBillingRelatedService,
//           mergeTagService = mergeTagService,
//           emailValidationModel = emailValidationModel
//         )
//         val rolePermissionDataDAOV2 = new RolePermissionDataDAOV2()
//         val organizationDAO = new OrganizationDAO()
//         val accountDAOV2 = new AccountDAOV2(organizationDAO)
//
//         val srUuidUtils = new SrUuidUtils
//         val accountDAO = new AccountDAO(
//           srUuidUtils = srUuidUtils
//         )
//
//         val migrationDAO = new MigrationDAO
//
//         val migrationService = new MigrationService(
//           migrationDAO = migrationDAO,
//           accountDAO = accountDAO
//         )
//
//         val emailMessageContactModel = new EmailMessageContactModel
//         val campaignProspectDAO = new CampaignProspectDAO(
//           prospectDAOService = prospectDAOService, prospectQuery = prospectQuery, srDBQueryCounterService = srDBQueryCounterService, prospectAddEventDAO = prospectAddEventDAO, prospectServiceV2 = prospectServiceV2
//         )
//         val migrationUtils = new MigrationUtils(
//           rolePermissionDataDAOV2 = rolePermissionDataDAOV2,
//           accountDAOV2 = accountDAOV2,
//           prospectServiceV2 = prospectServiceV2,
//           emailMessageContactModel = emailMessageContactModel,
//           migrationService = migrationService,
//           campaignProspectDAO = campaignProspectDAO,
//           replySentimentDAOService = replySentimentDAOService,
//           srUuidUtils = srUuidUtils,
//           dbUtils = dbUtils
//         )
//
//         val result = AppConfigUtil.teams_for_campaign_prospects_step_id_migration map { teamId =>
//           migrationUtils.campaignWiseMigrationForCampaignProspectStepId(teamId = teamId)
//         }
//
//         case "completeInCompleteConferenceAndCalls" =>
//
//           given Logger = new SRLogger(s"completeInCompleteConferenceAndCalls")
//
//           val prospectColumnDef = new ProspectColumnDef(new ProspectColumnDefDAO)
//           val prospectQuery = new ProspectQuery(
//             prospectColumnDef = prospectColumnDef
//           )
//           val prospectAddEventDAO = new ProspectAddEventDAO
//           val freeEmailDomainListDAO = new FreeEmailDomainListDAO
//           val cacheServiceJedis = new CacheServiceJedis
//           val campaignProspectDAO_2 = new CampaignProspectDAO_2
//           val prospectsEmailsDAO = new ProspectsEmailsDAO
//           val campaignSchedulingMetadataDAO = new CampaignSchedulingMetadataDAO
//
//           val mergeTagService = new MergeTagService(
//             campaignProspectDAO_2 = campaignProspectDAO_2,
//             campaignSchedulingMetadataDAO = campaignSchedulingMetadataDAO
//           )
//           val replySentimentDAO = new ReplySentimentDAO
//           val replySentimentJedisDAO = new ReplySentimentJedisDAO(
//             cacheServiceJedis = cacheServiceJedis
//           )
//           val replySentimentDAOService = new ReplySentimentDAOService(
//             replySentimentDAO = replySentimentDAO,
//             replySentimentJedisDAO = replySentimentJedisDAO
//           )
//
//           val prospectDAO = new ProspectDAO
//           val dbUtils = new DBUtils
//           val emailThreadDAO = new EmailThreadDAO
//
//           val dbCounterDAO = new DBCounterDAO(cacheServiceJedis)
//           val srDBQueryCounterService = new SrDBQueryCounterService(dbCounterDAO = dbCounterDAO)
//
//           val taskPgDAO = new TaskPgDAO(
//             srDBQueryCounterService = srDBQueryCounterService
//           )
//
//           val linkedinTaskService = new LinkedinTaskService(
//             taskPgDAO = taskPgDAO
//           )
//
//           val prospectDAOService = new ProspectDAOService(
//             prospectDAO = prospectDAO,
//             prospectQuery = prospectQuery,
//             linkedinTaskService = linkedinTaskService,
//             prospectColumnDef = prospectColumnDef,
//             mergeTagService = mergeTagService,
//             replySentimentDAOService = replySentimentDAOService,
//             emailThreadDAO = emailThreadDAO,
//             dbUtils = dbUtils
//           )
//
//           val emailValidationModel = new EmailValidationModel
//
//           val accountOrgBillingRelatedInfoDAO = new AccountOrgBillingRelatedInfoDAO()
//
//           val accountOrgBillingRelatedService = new AccountOrgBillingRelatedService(accountOrgBillingRelatedInfoDAO)
//
//           val prospectServiceV2 = new ProspectServiceV2(
//             prospectDAOService = prospectDAOService,
//             prospectsEmailsDAO = prospectsEmailsDAO,
//             prospectAddEventDAO = prospectAddEventDAO,
//             prospectColumnDef = prospectColumnDef,
//             campaignProspectDAO_2 = campaignProspectDAO_2,
//             accountOrgBillingRelatedService = accountOrgBillingRelatedService,
//             mergeTagService = mergeTagService,
//             emailValidationModel = emailValidationModel
//           )
//           val rolePermissionDataDAOV2 = new RolePermissionDataDAOV2()
//           val organizationDAO = new OrganizationDAO()
//           val accountDAOV2 = new AccountDAOV2(organizationDAO)
//
//           val srUuidUtils = new SrUuidUtils
//           val accountDAO = new AccountDAO(
//             srUuidUtils = srUuidUtils
//           )
//
//           val migrationDAO = new MigrationDAO
//
//           val migrationService = new MigrationService(
//             migrationDAO = migrationDAO,
//             accountDAO = accountDAO
//           )
//
//           val emailMessageContactModel = new EmailMessageContactModel
//           val campaignProspectDAO = new CampaignProspectDAO(
//             prospectDAOService = prospectDAOService, prospectQuery = prospectQuery, srDBQueryCounterService = srDBQueryCounterService, prospectAddEventDAO = prospectAddEventDAO, prospectServiceV2 = prospectServiceV2
//           )
//           val migrationUtils = new MigrationUtils(
//             rolePermissionDataDAOV2 = rolePermissionDataDAOV2,
//             accountDAOV2 = accountDAOV2,
//             prospectServiceV2 = prospectServiceV2,
//             emailMessageContactModel = emailMessageContactModel,
//             migrationService = migrationService,
//             campaignProspectDAO = campaignProspectDAO,
//             replySentimentDAOService = replySentimentDAOService,
//             srUuidUtils = srUuidUtils,
//             dbUtils = dbUtils
//           )
//
//           val result = migrationUtils.completeInProgressConferenceAndCalls()
//
//           result match {
//
//               case Failure(e) =>
//
//                 Logger.error("Error while completed conference and calls", e)
//
//               case Success((conf, calls)) =>
//
//                 Logger.info(s"completed Conference : ${conf}, completed calls : ${calls}")
//
//           }
//*/
//
//
//        case "deleteTasksAndRevertProspectForCampaign" =>
//          given Logger: SRLogger = new SRLogger("TestApp :: deleteTasksAndRevertProspectForCampaign :: ")
//
//          val campaignId = CampaignId(128405)
//          val prospectIds = List(
//            1722593051,
//            1722593055,
//            1722593045,
//            1722593046,
//            1722593047,
//            1722593063,
//            1722593053,
//            1722593056,
//            1722593043,
//            1722593059,
//            1722593044,
//            1722593058,
//            1722593085,
//            1722593010,
//            1722593006,
//            1722593011,
//            1722593012,
//            1722593005,
//            1722593004,
//            1722592984,
//            1722592997,
//            1722593001,
//            1722593000,
//            1722592995,
//            1722592994,
//            1722592992,
//            1722592986,
//            1722592996,
//            1722593016,
//            1722593018,
//            1722593017,
//            1722593035,
//            1722593020,
//            1722593029,
//            1722593023,
//            1722593024,
//            1722593032,
//            1722593037,
//            1722593013,
//            1722592961,
//            1722592930,
//            1722592934,
//            1722592941,
//            1722592970,
//            1722592967,
//            1722592948,
//            1722592933,
//            1722592953,
//            1722592935,
//            1722592974,
//            1722592966,
//            1722592937,
//            1722592946,
//            1722592955,
//            1722592949,
//            1722592964,
//            1722592962,
//            1722592969,
//            1722592965,
//            1722592929,
//            1722592947,
//            1722592958,
//            1722592910,
//            1722592902,
//            1722592882,
//            1722592915,
//            1722592908,
//            1722592914,
//            1722592866,
//            1722592925,
//            1722592916,
//            1722592887,
//            1722592875,
//            1722592913,
//            1722592901,
//            1722592872,
//            1722592870,
//            1722592890,
//            1722592922,
//            1722592858,
//            1722592805,
//            1722592819,
//            1722592848,
//            1722592826,
//            1722592839,
//            1722592815,
//            1722592822,
//            1722592801,
//            1722592821,
//            1722592841,
//            1722592825,
//            1722592832,
//            1722592855,
//            1722592831,
//            1722592823,
//            1722592812,
//            1722592849,
//            1722592811,
//            1722592820,
//            1722592809,
//            1722592803,
//            1722592864,
//            1722592865,
//            1722592863,
//            1722561139,
//            1722561140,
//            1722561135,
//            1722592777,
//            1722592772,
//            1722592759,
//            1722592742,
//            1722592752,
//            1722561153,
//            1722592780,
//            1722592775,
//            1722592756,
//            1722592746,
//            1722592741,
//            1722561145,
//            1722561148,
//            1722592781,
//            1722592785,
//            1722592769,
//            1722592758,
//            1722592760,
//            1722592747,
//            1722592790,
//            1722592789,
//            1722592791,
//            1722561080,
//            1722561098,
//            1722561095,
//            1722561077,
//            1722561076,
//            1722561111,
//            1722561113,
//            1722561110,
//            1722561101,
//            1722561104,
//            1722561106,
//            1722561096,
//            1722561078,
//            1722561102,
//            1722561100,
//            1722561079,
//            1722561092,
//            1722561084,
//            1722561128,
//            1722561085,
//            1722561130,
//            1722561119,
//            1722561091,
//            1722561131,
//            1722561124,
//            1722561093,
//            1722561089,
//            1722561094,
//            1722561088,
//            1722561009,
//            1722561058,
//            1722561023,
//            1722561015,
//            1722561065,
//            1722561057,
//            1722561063,
//            1722561011,
//            1722561068,
//            1722561044,
//            1722561040,
//            1722561013,
//            1722561018,
//            1722561035,
//            1722561066,
//            1722561051,
//            1722561036,
//            1722561067,
//            1722561054,
//            1722561046,
//            1722561024,
//            1722561031,
//            1722561041,
//            1722561064,
//            1722561069,
//            1722561038,
//            1722561032,
//            1722561042,
//            1722561070,
//            1722561071,
//            1722561074,
//            1722560953,
//            1722560931,
//            1722560966,
//            1722560974,
//            1722560956,
//            1722560938,
//            1722560952,
//            1722560968,
//            1722560984,
//            1722560971,
//            1722560959,
//            1722560965,
//            1722560950,
//            1722560985,
//            1722560981,
//            1722560955,
//            1722560970,
//            1722560946,
//            1722560935,
//            1722560982,
//            1722560957,
//            1722560936,
//            1722560940,
//            1722560954,
//            1722560961,
//            1722560941,
//            1722560976,
//            1722560983,
//            1722560979,
//            1722560987,
//            1722560989,
//            1722560991,
//            1722560993,
//            1722560994,
//            1722560995,
//            1722560996,
//            1722560988,
//            1722560992,
//            1722560990,
//            1722560920,
//            1722560921,
//            1722560887,
//            1722560888,
//            1722560889,
//            1722560890,
//            1722560891,
//            1722560892,
//            1722560894,
//            1722560895,
//            1722560896,
//            1722560899,
//            1722560915,
//            1722560901,
//            1722560907,
//            1722560913,
//            1722560906,
//            1722560914,
//            1722560900,
//            1722560926,
//            1722560929,
//            1722560927,
//            1720958055,
//            1722516714,
//            1722516808,
//            1722516750,
//            1722516833,
//            1722516831,
//            1722516770,
//            1722516806,
//            1722516816,
//            1722516835,
//            1722516772,
//            1722516840,
//            1722516821,
//            1722516839,
//
//          )
//
//          val teamId = TeamId(6412)
//
//          object main extends TestAppTrait {
//            def init() = {
//              val dbAndSession = dbUtils.startLocalTx()
//              val db = dbAndSession.db
//              implicit val session: DBSession = dbAndSession.session
//
//              val revertedCount = emailsScheduledDeleteService.deleteTasksAndRevertMultichannel(
//                campaignId = campaignId,
//                prospectIds = prospectIds.map(ProspectId(_)),
//                teamId = teamId,
//                deletion_reason = DeletionReason.Other(reason = "deleteTasksAndRevertProspectForCampaign")
//              )
//
//              dbUtils.commitAndCloseSession(db = db)
//              revertedCount
//
//            }
//          }
//
//          Logger.info(s"Reverted prospects count: ${main.init()}")
//
//
//        case "vg/deleteAccount" =>
//
//          val logger: SRLogger = new SRLogger("deleteAccount")
//
//          logger.info("deleteAccount")
//
//          object main extends TestAppTrait {
//
//            def init(): Unit = {
//
//              migrationUtils.deleteAccount(
//                AccountV2(
//                  id = 16,
//                  email = "<EMAIL>",
//                  first_name = "delete",
//                  last_name = "test",
//                  company = "smartreach",
//                  org_role = None,
//                  org_id = 17,
//                  active = true,
//                  account_status = AccountStatus.ScheduledForDeletion,
//                  scheduled_for_deletion_at = None
//                ), Logger = logger
//              ) match {
//                case Left(api.accounts.DeleteAccountError.SQLErrorWhileDeletingAccountData(err)) =>
//                  logger.fatal(s"Error while deleting data related to account- $err")
//                  throw err
//                case Left(api.accounts.DeleteAccountError.AccountOfOwner) =>
//                  logger.fatal("ATTEMPT TO DELETE ACCOUNT OF OWNER FORBIDDEN")
//                case Right(true) =>
//                  logger.info(s"Success")
//                case Right(false) =>
//                  logger.fatal(s"IMPOSSIBLE CASE ")
//              }
//            }
//
//          }
//
//          main.init()
//
//        case "associate_sec_prospect_to_old_emails" =>
//
//          object associate extends TestAppTrait {
//
//            def init(): Unit = {
//              val logger: SRLogger = new SRLogger("testing connection")
//              logger.info("Starting the program")
//
//              val prospect_seq_11001: Seq[(Long, String)] = Seq((**********, "<EMAIL>"),
//                (**********, "<EMAIL>"),
//                (**********, "<EMAIL>"),
//                (**********, "<EMAIL>"),
//                (**********, "<EMAIL>"),
//                (**********, "<EMAIL>"),
//                (**********, "<EMAIL>"),
//                (**********, "<EMAIL>"),
//                (**********, "<EMAIL>"),
//                (1724542592, "<EMAIL>"),
//                (1724542956, "<EMAIL>"),
//                (1724543021, "<EMAIL>"),
//                (1724543031, "<EMAIL>"),
//                (1724543063, "<EMAIL>"))
//
//              val new_prospects_11001: Seq[NewlyCreatedProspect] = prospect_seq_11001.map(p => {
//                NewlyCreatedProspect(
//                  prospect_id = p._1, email = p._2
//                )
//              })
//
//              val associateProspectsData_11001: AssociateProspectsData = AssociateProspectsData(
//                new_prospects = new_prospects_11001, team_id = 11001L, campaign_ids = None
//              )
//
//              mqAssociateProspectToOldEmails.processMessage(associateProspectsData_11001)
//
//              val prospect_seq_6281: Seq[(Long, String)] = Seq((1724013851, "<EMAIL>"),
//                (1723665815, "<EMAIL>"),
//                (1724772590, "<EMAIL>"),
//                (1723665806, "<EMAIL>"),
//                (1723665823, "<EMAIL>"),
//                (1723665447, "<EMAIL>"),
//                (1723665664, "<EMAIL>"),
//                (1724473910, "<EMAIL>"),
//                (1723665737, "<EMAIL>"),
//                (1724014032, "<EMAIL>"),
//                (1723665517, "<EMAIL>"),
//                (1713051934, "<EMAIL>"),
//                (1724127210, "<EMAIL>"),
//                (1724127041, "<EMAIL>"),
//                (1722978852, "<EMAIL>"),
//                (1724901510, "<EMAIL>"),
//                (1724126952, "<EMAIL>"),
//                (1723665488, "<EMAIL>"),
//                (1723665759, "<EMAIL>"),
//                (1723665548, "<EMAIL>"),
//                (1723665446, "<EMAIL>"),
//                (1723665436, "<EMAIL>"),
//                (1723665516, "<EMAIL>"),
//                (1723665620, "<EMAIL>"),
//                (1723665625, "<EMAIL>"),
//                (1723665740, "<EMAIL>"),
//                (1723665592, "<EMAIL>"),
//                (1724474115, "<EMAIL>"),
//                (1724014046, "<EMAIL>"),
//                (1724474139, "<EMAIL>"),
//                (1724473997, "<EMAIL>"),
//                (1723665512, "<EMAIL>"),
//                (1714822742, "<EMAIL>"),
//                (1724772581, "<EMAIL>"),
//                (1724772675, "<EMAIL>"),
//                (1724772633, "<EMAIL>"),
//                (1724901462, "<EMAIL>"))
//
//              val new_prospects_6281: Seq[NewlyCreatedProspect] = prospect_seq_6281.map(p => {
//                NewlyCreatedProspect(
//                  prospect_id = p._1, email = p._2
//                )
//              })
//
//              val associateProspectsData_6281: AssociateProspectsData = AssociateProspectsData(
//                new_prospects = new_prospects_6281, team_id = 6281L, campaign_ids = None
//              )
//
//              mqAssociateProspectToOldEmails.processMessage(associateProspectsData_6281)
//
//              val prospect_seq_6412: Seq[(Long, String)] = Seq((1711566640, "<EMAIL>"))
//
//              val new_prospects_6412: Seq[NewlyCreatedProspect] = prospect_seq_6412.map(p => {
//                NewlyCreatedProspect(
//                  prospect_id = p._1, email = p._2
//                )
//              })
//
//              val associateProspectsData_6412: AssociateProspectsData = AssociateProspectsData(
//                new_prospects = new_prospects_6412, team_id = 6412L, campaign_ids = None
//              )
//
//              mqAssociateProspectToOldEmails.processMessage(associateProspectsData_6412)
//
//              val prospect_seq_8219: Seq[(Long, String)] = Seq((1716692724, "<EMAIL>"),
//                (1713472996, "<EMAIL>"))
//
//              val new_prospects_8219: Seq[NewlyCreatedProspect] = prospect_seq_8219.map(p => {
//                NewlyCreatedProspect(
//                  prospect_id = p._1, email = p._2
//                )
//              })
//
//              val associateProspectsData_8219: AssociateProspectsData = AssociateProspectsData(
//                new_prospects = new_prospects_8219, team_id = 8219L, campaign_ids = None
//              )
//
//              mqAssociateProspectToOldEmails.processMessage(associateProspectsData_8219)
//
//              val prospect_seq_10163: Seq[(Long, String)] = Seq((1724246591, "<EMAIL>"),
//                (1724329296, "<EMAIL>"),
//                (1715694623, "<EMAIL>"),
//                (1714428282, "<EMAIL>"),
//                (1724474705, "<EMAIL>"),
//                (1724469434, "<EMAIL>"))
//
//              val new_prospects_10163: Seq[NewlyCreatedProspect] = prospect_seq_10163.map(p => {
//                NewlyCreatedProspect(
//                  prospect_id = p._1, email = p._2
//                )
//              })
//
//              val associateProspectsData_10163: AssociateProspectsData = AssociateProspectsData(
//                new_prospects = new_prospects_10163, team_id = 10163L, campaign_ids = None
//              )
//
//              mqAssociateProspectToOldEmails.processMessage(associateProspectsData_10163)
//
//              val prospect_seq_11185: Seq[(Long, String)] = Seq((1723671216, "<EMAIL>"),
//                (1719807345, "<EMAIL>"),
//                (1723670754, "<EMAIL>"),
//                (1722846025, "<EMAIL>"))
//
//              val new_prospects_11185: Seq[NewlyCreatedProspect] = prospect_seq_11185.map(p => {
//                NewlyCreatedProspect(
//                  prospect_id = p._1, email = p._2
//                )
//              })
//
//              val associateProspectsData_11185: AssociateProspectsData = AssociateProspectsData(
//                new_prospects = new_prospects_11185, team_id = 11185L, campaign_ids = None
//              )
//
//              mqAssociateProspectToOldEmails.processMessage(associateProspectsData_11185)
//
//              val prospect_seq_11705: Seq[(Long, String)] = Seq((1723898284, "<EMAIL>"))
//
//              val new_prospects_11705: Seq[NewlyCreatedProspect] = prospect_seq_11705.map(p => {
//                NewlyCreatedProspect(
//                  prospect_id = p._1, email = p._2
//                )
//              })
//
//              val associateProspectsData_11705: AssociateProspectsData = AssociateProspectsData(
//                new_prospects = new_prospects_11705, team_id = 11705L, campaign_ids = None
//              )
//
//              mqAssociateProspectToOldEmails.processMessage(associateProspectsData_11705)
//
//              val prospect_seq_12215: Seq[(Long, String)] = Seq((1724127348, "<EMAIL>"),
//                (1724212270, "<EMAIL>"),
//                (1723150418, "<EMAIL>"),
//                (1723150479, "<EMAIL>"),
//                (1723150466, "<EMAIL>"),
//                (1723150480, "<EMAIL>"),
//                (1724212941, "<EMAIL>"),
//                (1724212895, "<EMAIL>"),
//                (1724212280, "<EMAIL>"),
//                (1724127288, "<EMAIL>"),
//                (1724127307, "<EMAIL>"),
//                (1723149621, "<EMAIL>"),
//                (1724127327, "<EMAIL>"),
//                (1723150063, "<EMAIL>"),
//                (1723150781, "<EMAIL>"),
//                (1724212224, "<EMAIL>"),
//                (1724127243, "<EMAIL>"))
//
//              val new_prospects_12215: Seq[NewlyCreatedProspect] = prospect_seq_12215.map(p => {
//                NewlyCreatedProspect(
//                  prospect_id = p._1, email = p._2
//                )
//              })
//
//              val associateProspectsData_12215: AssociateProspectsData = AssociateProspectsData(
//                new_prospects = new_prospects_12215, team_id = 12215L, campaign_ids = None
//              )
//
//              mqAssociateProspectToOldEmails.processMessage(associateProspectsData_12215)
//
//              val prospect_seq_12226: Seq[(Long, String)] = Seq((1724154267, "<EMAIL>"),
//                (1723980533, "<EMAIL>"),
//                (1724154269, "<EMAIL>"),
//                (1723357476, "<EMAIL>"),
//                (1723980526, "<EMAIL>"),
//                (1723357517, "<EMAIL>"))
//
//              val new_prospects_12226: Seq[NewlyCreatedProspect] = prospect_seq_12226.map(p => {
//                NewlyCreatedProspect(
//                  prospect_id = p._1, email = p._2
//                )
//              })
//
//              val associateProspectsData_12226: AssociateProspectsData = AssociateProspectsData(
//                new_prospects = new_prospects_12226, team_id = 12226L, campaign_ids = None
//              )
//
//              mqAssociateProspectToOldEmails.processMessage(associateProspectsData_12226)
//
//              val prospect_seq_12611: Seq[(Long, String)] = Seq((1724856097, "<EMAIL>"),
//                (1724191314, "<EMAIL>"))
//
//              val new_prospects_12611: Seq[NewlyCreatedProspect] = prospect_seq_12611.map(p => {
//                NewlyCreatedProspect(
//                  prospect_id = p._1, email = p._2
//                )
//              })
//
//              val associateProspectsData_12611: AssociateProspectsData = AssociateProspectsData(
//                new_prospects = new_prospects_12611, team_id = 12611L, campaign_ids = None
//              )
//
//              mqAssociateProspectToOldEmails.processMessage(associateProspectsData_12611)
//
//              val prospect_seq_12617: Seq[(Long, String)] = Seq((1724737887, "<EMAIL>"),
//                (1725102720, "<EMAIL>"),
//                (1724738153, "<EMAIL>"),
//                (1725102982, "<EMAIL>"),
//                (1724293036, "<EMAIL>"),
//                (1724737994, "<EMAIL>"))
//
//              val new_prospects_12617: Seq[NewlyCreatedProspect] = prospect_seq_12611.map(p => {
//                NewlyCreatedProspect(
//                  prospect_id = p._1, email = p._2
//                )
//              })
//
//              val associateProspectsData_12617: AssociateProspectsData = AssociateProspectsData(
//                new_prospects = new_prospects_12617, team_id = 12617L, campaign_ids = None
//              )
//
//              mqAssociateProspectToOldEmails.processMessage(associateProspectsData_12617)
//
//              val prospect_seq_12789: Seq[(Long, String)] = Seq((1724237685, "<EMAIL>"))
//
//              val new_prospects_12789: Seq[NewlyCreatedProspect] = prospect_seq_12789.map(p => {
//                NewlyCreatedProspect(
//                  prospect_id = p._1, email = p._2
//                )
//              })
//
//              val associateProspectsData_12789: AssociateProspectsData = AssociateProspectsData(
//                new_prospects = new_prospects_12789, team_id = 12789L, campaign_ids = None
//              )
//
//              mqAssociateProspectToOldEmails.processMessage(associateProspectsData_12789)
//
//
//            }
//
//          }
//
//          associate.init()
//
//        //        case "leadInfo" =>
//        //          leadFinderDAO.getLeadDetails(companyTypes = Seq(LeadCompanyType("Privately Held"),LeadCompanyType("Public Company"))) match {
//        //            case Success(value) =>
//        //              println(value)
//        //            case Failure(exception) =>
//        //              println(s"Failure ${exception}")
//        //          }
//
//        //sbt -J-Xmx2G -J-Xms2G "coldemail/testOnly leadInfo"
//
//        case "teamIdInTeamInboxMigration" =>
//        /*
//                  val dbUtils = new DBUtils
//                  val dbAndSession = dbUtils.startLocalTx()
//
//                  val db = dbAndSession.db
//                  val session = dbAndSession.session
//
//                  val result = DB autoCommit { implicit session =>
//                    sql"""
//                          update team_inbox
//                          set team_id = (select email_settings.team_id from email_settings
//                          where email_settings.id = team_inbox.email_setting_id)
//                          returning id;
//                       """
//                      .map(_.long("id"))
//                      .list
//                      .apply()
//
//                  }
//
//                  logger.info(s"Total ${result.length} inboxes updated")
//
//                  dbUtils.closeSession(db = db)
//        */
//
//        /*
//        case "migrateUpdatedAtEmailThreads" =>
//
//          val prospectColumnDef = new ProspectColumnDef(new ProspectColumnDefDAO)
//          val prospectQuery = new ProspectQuery(
//            prospectColumnDef = prospectColumnDef
//          )
//          val prospectAddEventDAO = new ProspectAddEventDAO
//          val freeEmailDomainListDAO = new FreeEmailDomainListDAO
//          val cacheServiceJedis = new CacheServiceJedis
//          val campaignProspectDAO_2 = new CampaignProspectDAO_2
//          val prospectsEmailsDAO = new ProspectsEmailsDAO
//
//          val campaignSchedulingMetadataDAO = new CampaignSchedulingMetadataDAO
//
//          val mergeTagService = new MergeTagService(
//            campaignProspectDAO_2 = campaignProspectDAO_2,
//            campaignSchedulingMetadataDAO = campaignSchedulingMetadataDAO
//          )
//          val replySentimentDAO = new ReplySentimentDAO
//          val replySentimentJedisDAO = new ReplySentimentJedisDAO(
//            cacheServiceJedis = cacheServiceJedis
//          )
//          val replySentimentDAOService = new ReplySentimentDAOService(
//            replySentimentDAO = replySentimentDAO,
//            replySentimentJedisDAO = replySentimentJedisDAO
//          )
//
//          val prospectDAO = new ProspectDAO
//          val dbUtils = new DBUtils
//          val emailThreadDAO = new EmailThreadDAO
//
//          val dbCounterDAO = new DBCounterDAO(cacheServiceJedis)
//          val srDBQueryCounterService = new SrDBQueryCounterService(dbCounterDAO = dbCounterDAO)
//
//          val taskPgDAO = new TaskPgDAO(
//            srDBQueryCounterService = srDBQueryCounterService
//          )
//
//          val linkedinTaskService = new LinkedinTaskService(
//            taskPgDAO = taskPgDAO
//          )
//
//          val prospectDAOService = new ProspectDAOService(
//            prospectDAO = prospectDAO,
//            prospectQuery = prospectQuery,
//            linkedinTaskService = linkedinTaskService,
//            prospectColumnDef = prospectColumnDef,
//            mergeTagService = mergeTagService,
//            replySentimentDAOService = replySentimentDAOService,
//            emailThreadDAO = emailThreadDAO,
//            dbUtils = dbUtils
//          )
//
//          val emailValidationModel = new EmailValidationModel
//
//          val accountOrgBillingRelatedInfoDAO = new AccountOrgBillingRelatedInfoDAO()
//
//          val accountOrgBillingRelatedService = new AccountOrgBillingRelatedService(accountOrgBillingRelatedInfoDAO)
//
//          val prospectServiceV2 = new ProspectServiceV2(
//            prospectDAOService = prospectDAOService,
//            prospectsEmailsDAO = prospectsEmailsDAO,
//            prospectAddEventDAO = prospectAddEventDAO,
//            prospectColumnDef = prospectColumnDef,
//            campaignProspectDAO_2 = campaignProspectDAO_2,
//            accountOrgBillingRelatedService = accountOrgBillingRelatedService,
//            mergeTagService = mergeTagService,
//            emailValidationModel = emailValidationModel
//          )
//          val rolePermissionDataDAOV2 = new RolePermissionDataDAOV2()
//          val organizationDAO = new OrganizationDAO()
//          val accountDAOV2 = new AccountDAOV2(organizationDAO)
//
//          val emailMessageContactModel = new EmailMessageContactModel
//
//          val srUuidUtils = new SrUuidUtils
//          val accountDAO = new AccountDAO(
//            srUuidUtils = srUuidUtils
//          )
//
//          val migrationDAO = new MigrationDAO
//
//          val migrationService = new MigrationService(
//            migrationDAO = migrationDAO,
//            accountDAO = accountDAO
//          )
//
//          val campaignProspectDAO = new CampaignProspectDAO(
//            prospectDAOService = prospectDAOService, prospectQuery = prospectQuery, srDBQueryCounterService = srDBQueryCounterService, prospectAddEventDAO = prospectAddEventDAO, prospectServiceV2 = prospectServiceV2
//          )
//          val migrationUtils = new MigrationUtils(
//            rolePermissionDataDAOV2 = rolePermissionDataDAOV2,
//            accountDAOV2 = accountDAOV2,
//            prospectServiceV2 = prospectServiceV2,
//            emailMessageContactModel = emailMessageContactModel,
//            migrationService = migrationService,
//            campaignProspectDAO = campaignProspectDAO,
//            replySentimentDAOService = replySentimentDAOService,
//            srUuidUtils = srUuidUtils,
//            dbUtils = dbUtils
//          )
//
//          val team_ids: List[Long] = List(
//            10690, 10691, 10693, 10699, 10702, 10714, 10720, 10748, 10762, 10763, 10776, 10792, 10794, 10796, 10811,
//            10836, 10851, 10852, 10854, 10873, 10875, 10878, 10881, 10882, 10893, 10894, 10896, 10901, 10909, 10921,
//            10932, 10935, 10936, 10948, 10951, 10958, 10960, 10962, 10963, 10964, 10977, 10988, 10989, 10999, 11003,
//            11006, 11009, 11012, 11016, 11017, 11018, 11027, 11033, 11036, 11038, 11044, 11053, 11054, 11072, 11084,
//            11086, 11091, 11096, 11099, 11105, 11110, 11113, 11114, 11116, 11118, 11119, 11125, 11127, 11128, 11130,
//            11131, 11138, 11140, 11145, 11153, 11158, 11169, 11175, 11181, 11187, 11191, 11194, 11195, 11197, 11211,
//            11216, 11232, 11234, 11235, 11239, 11253, 11254, 11257, 11265, 11266, 11268, 11269, 11270, 11272, 11275,
//            11276, 11292, 11293, 11305, 11310, 11313, 11320, 11321, 11331, 11334, 11338, 11361, 11366, 11368, 11369,
//            11372, 11375, 11376, 11384, 11390, 11393, 11394, 11395, 11397, 11406, 11409, 11414, 11417, 11419, 11428,
//            11433, 11434, 11435, 11448, 11451, 11455, 11469, 11486, 11499, 11507, 11509, 11510, 11511, 11512, 11513,
//            11538, 11546, 11587, 11602, 11606, 11623, 11638, 11660, 11666, 11667, 11670, 11675, 11679, 11680, 11681,
//            11683, 11690, 11692, 11705, 11715, 11716, 11734, 11742, 11743, 11748, 11756, 11758, 11764, 11768, 11770,
//            11771, 11775, 11776, 11777, 11779, 11780, 11787, 11789, 11790, 11805, 11826, 11829, 11840, 11845, 11860,
//            11862, 11863, 11864, 11872, 11876, 11877, 11878, 11884, 11885, 11887, 11891, 11894, 11896, 11900, 11904,
//            11912, 11913, 11918, 11927, 11928, 11933, 11938, 11939, 11940, 11941, 11952, 11957, 11958, 11963, 11965,
//            11966, 11967, 11970, 11972, 11975, 11981, 11983, 11984, 11990, 11993, 11994, 12006, 12012, 12016, 12023,
//            12025, 12027, 12030, 12035, 12042, 12043, 12046, 12050, 12057, 12062, 12067, 12076, 12077, 12078, 12079,
//            12094, 12103, 12121, 12124, 12126, 12129, 12135, 12136, 12137, 12139, 12144, 12145, 12172, 12179, 12180,
//            12181, 12183, 12192, 12201, 12202, 12204, 12208, 12220, 12226, 12248, 12258, 12263, 12269, 12273, 12278,
//            12279, 12282, 12283, 12284, 12285, 12288, 12292, 12297, 12299, 12310, 12313, 12321, 12354, 12356, 12359,
//            12361, 12363, 12368, 12369, 12383, 12384, 12389, 12390, 12392, 12399, 12401, 12404, 12405, 12409, 12410,
//            12425, 12433, 12438, 12447, 12450, 12453, 12454, 12458, 12463, 12482, 12489, 12491, 12492, 12501, 12506,
//            12508, 12511, 12524, 12525, 12530, 12537, 12538, 12545, 12546, 12547, 12550, 12552, 12576, 12578, 12579,
//            12587, 12589, 12590, 12610, 12611, 12616, 12624, 12642, 12655, 12656, 12657, 12661, 12664, 12668, 12673,
//            12675, 12680, 12682, 12693, 12703, 12711, 12712, 12713, 12714, 12715, 12716, 12717, 12718, 12719, 12720,
//            12721, 12722, 12723, 12724, 12725, 12726, 12727, 12728, 12729, 12730, 12731, 12732, 12733, 12734, 12735,
//            12736, 12737, 12738, 12739, 12740, 12741, 12742, 12743, 12744, 12745, 12746, 12747, 12748, 12749, 12750,
//            12751, 12752, 12753, 12754, 12755, 12756, 12757, 12758, 12759, 12760, 12761, 12762, 12763, 12764, 12765,
//            12766, 12767, 12768, 12769, 12770, 12771, 12772, 12773, 12774, 12775, 12776, 12777, 12778, 12779, 12780,
//            12781, 12782, 12783, 12784, 12785, 12786, 12787, 12788, 12789, 12790, 12791, 12792, 12793, 12794, 12795,
//            12796, 12797, 12798, 12799, 12800, 12801, 12802, 12803, 12805, 12806, 12807, 12808, 12809, 12810, 12811,
//            12812, 12813, 12814, 12815, 12817, 12818, 12820, 12821, 12822, 12823, 12824, 12825, 12826, 12827, 12828,
//            12829, 12830, 12831, 12832, 12833, 12834, 12835, 12836, 12837, 12838, 12839, 12840, 12841, 12842, 12843,
//            12844, 12845, 12846, 12847, 12848, 12849, 12850, 12851, 12852, 12853
//         )
//
//          migrationUtils.migrateUpdatedAtEmailThreads(team_ids_to_be_migrated = team_ids)
//
//        case "migrateUpdatedAtProspects" =>
//
//          val prospectColumnDef = new ProspectColumnDef(new ProspectColumnDefDAO)
//          val prospectQuery = new ProspectQuery(
//            prospectColumnDef = prospectColumnDef
//          )
//          val prospectAddEventDAO = new ProspectAddEventDAO
//          val freeEmailDomainListDAO = new FreeEmailDomainListDAO
//          val cacheServiceJedis = new CacheServiceJedis
//          val campaignProspectDAO_2 = new CampaignProspectDAO_2
//          val prospectsEmailsDAO = new ProspectsEmailsDAO
//          val campaignSchedulingMetadataDAO = new CampaignSchedulingMetadataDAO
//
//          val mergeTagService = new MergeTagService(
//            campaignProspectDAO_2 = campaignProspectDAO_2,
//            campaignSchedulingMetadataDAO = campaignSchedulingMetadataDAO
//          )
//          val replySentimentDAO = new ReplySentimentDAO
//          val replySentimentJedisDAO = new ReplySentimentJedisDAO(
//            cacheServiceJedis = cacheServiceJedis
//          )
//          val replySentimentDAOService = new ReplySentimentDAOService(
//            replySentimentDAO = replySentimentDAO,
//            replySentimentJedisDAO = replySentimentJedisDAO
//          )
//
//          val prospectDAO = new ProspectDAO
//          val dbUtils = new DBUtils
//          val emailThreadDAO = new EmailThreadDAO
//
//          val dbCounterDAO = new DBCounterDAO(cacheServiceJedis)
//          val srDBQueryCounterService = new SrDBQueryCounterService(dbCounterDAO = dbCounterDAO)
//
//          val taskPgDAO = new TaskPgDAO(
//            srDBQueryCounterService = srDBQueryCounterService
//          )
//
//          val linkedinTaskService = new LinkedinTaskService(
//            taskPgDAO = taskPgDAO
//          )
//
//          val prospectDAOService = new ProspectDAOService(
//            prospectDAO = prospectDAO,
//            prospectQuery = prospectQuery,
//            linkedinTaskService = linkedinTaskService,
//            prospectColumnDef = prospectColumnDef,
//            mergeTagService = mergeTagService,
//            replySentimentDAOService = replySentimentDAOService,
//            emailThreadDAO = emailThreadDAO,
//            dbUtils = dbUtils
//          )
//
//          val emailValidationModel = new EmailValidationModel
//
//          val accountOrgBillingRelatedInfoDAO = new AccountOrgBillingRelatedInfoDAO()
//
//          val accountOrgBillingRelatedService = new AccountOrgBillingRelatedService(accountOrgBillingRelatedInfoDAO)
//
//          val prospectServiceV2 = new ProspectServiceV2(
//            prospectDAOService = prospectDAOService,
//            prospectsEmailsDAO = prospectsEmailsDAO,
//            prospectAddEventDAO = prospectAddEventDAO,
//            prospectColumnDef = prospectColumnDef,
//            campaignProspectDAO_2 = campaignProspectDAO_2,
//            accountOrgBillingRelatedService = accountOrgBillingRelatedService,
//            mergeTagService = mergeTagService,
//            emailValidationModel = emailValidationModel
//          )
//          val rolePermissionDataDAOV2 = new RolePermissionDataDAOV2()
//          val organizationDAO = new OrganizationDAO()
//          val accountDAOV2 = new AccountDAOV2(organizationDAO)
//
//          val emailMessageContactModel = new EmailMessageContactModel
//
//          val srUuidUtils = new SrUuidUtils
//          val accountDAO = new AccountDAO(
//            srUuidUtils = srUuidUtils
//          )
//
//          val migrationDAO = new MigrationDAO
//
//          val migrationService = new MigrationService(
//            migrationDAO = migrationDAO,
//            accountDAO = accountDAO
//          )
//
//          val campaignProspectDAO = new CampaignProspectDAO(
//            prospectDAOService = prospectDAOService, prospectQuery = prospectQuery, srDBQueryCounterService = srDBQueryCounterService, prospectAddEventDAO = prospectAddEventDAO, prospectServiceV2 = prospectServiceV2
//          )
//          val migrationUtils = new MigrationUtils(
//            rolePermissionDataDAOV2 = rolePermissionDataDAOV2,
//            accountDAOV2 = accountDAOV2,
//            prospectServiceV2 = prospectServiceV2,
//            emailMessageContactModel = emailMessageContactModel,
//            migrationService = migrationService,
//            campaignProspectDAO = campaignProspectDAO,
//            replySentimentDAOService = replySentimentDAOService,
//            srUuidUtils = srUuidUtils,
//            dbUtils = dbUtils
//          )
//
//          given logger: SRLogger = new SRLogger("migrateUpdatedAtProspects")
//          val team_ids: List[Long] = List(
//            2, 88, 81, 24, 718, 505, 1366, 1483, 1402
//          )
//
//          team_ids.foreach(t => {
//            migrationUtils.migrateProspectsUpdatedAt(team_id = t)
//          })
//
//          logger.info(s"updated_at for prospects Migration completed for all team_ids")
//
//        case "getInsertNewThreadQuery_old" =>
//
//          val logger = new SRLogger("getInsertNewThreadQuery testApp:: ")
//          val emailSentId = 1L
//          val to_email1 = "<EMAIL>"
//          val to_email2 = "<EMAIL>"
//          val from_email = "<EMAIL>"
//          val from_name = "Fromname"
//          val subject = "email subject"
//          val textBody = " email textBody"
//          val htmlBody = "email htmlBody"
//          val message_id = "email msgId"
//          val account_id = 12L
//          val team_id = 47L
//
//          val sender_email_settings_id = 179L
//
//          val emailToBeSent = EmailToBeSent(
//            to_emails = List(IEmailAddress(email = to_email1), IEmailAddress(email = to_email2)),
//            from_email = from_email,
//            cc_emails = Seq(),
//            bcc_emails = Seq(),
//            from_name = from_name,
//            reply_to_email = Some(from_email),
//            reply_to_name = Some(from_name),
//
//            subject = subject,
//            textBody = textBody,
//            htmlBody = htmlBody,
//            message_id = Some(message_id),
//            references_header = None,
//
//            in_reply_to_id = None,
//            in_reply_to_references_header = None,
//            in_reply_to_sent_at = None,
//
//            sender_email_settings_id = sender_email_settings_id,
//
//            email_thread_id = Some(1L),
//            gmail_msg_id = None,
//            gmail_thread_id = None,
//
//            outlook_msg_id = None,
//            outlook_conversation_id = None,
//            outlook_response_json = None,
//
//            gmail_fbl = None,
//            list_unsubscribe_header = None,
//            hasCustomTrackingDomain = false,
//            rep_smtp_reverse_dns_host = None
//
//          )
//
//
//          val newEmailThread = NewEmailThreadV3(
//            temp_thread_id = 0,
//            uuid = EmailThreadUuid("email_thread_243vcjd432"),
//            owner_id = AccountId(id = account_id),
//            team_id = TeamId(id = team_id),
//            campaign_id = Some(CampaignId(id = 111L)),
//            campaign_name = Some("4 march campaign"),
//            inbox_email = emailToBeSent.from_email,
//            inbox_email_settings_id = EmailSettingId(emailSettingId = emailToBeSent.sender_email_settings_id),
//            subject = emailToBeSent.subject,
//            latest_email_id = Some(emailSentId),
//            gmail_msg_id = emailToBeSent.gmail_msg_id,
//            gmail_thread_id = emailToBeSent.gmail_thread_id,
//            outlook_msg_id = emailToBeSent.outlook_msg_id,
//            outlook_conversation_id = emailToBeSent.outlook_conversation_id,
//            internal_tracking_note = InternalTrackingNote.NONE,
//            has_prospect = false,
//            folder_type = FolderType.NON_PROSPECTS
//          )
//
//          val validCampaignById: Map[Long, (Long, String)] = Map(111L -> (111L, "4 march campaign"))
//
//          val insertQuery = EmailThreadDAO.getInsertNewThreadQuery(
//            threadGroup = Seq(newEmailThread),
//            validCampaignById = validCampaignById,
//            adminReplyFromSRInbox = false,
//            SRLogger = logger
//          )
//
//          val result = DB autoCommit { implicit session =>
//
//            insertQuery
//              .map(rs => {
//
//                SavedEmailThread(
//                  id = rs.long("id"),
//                  temp_thread_id = rs.longOpt("temp_thread_id"),
//                  folder_type = rs.stringOpt("folder_type")
//                )
//
//              })
//              .list
//              .apply()
//          }
//
//          println(s"result::::\n\n $result")
//*/
//
//        case "oldThreadsAutomaticUnsnooze" =>
//
//          object unsnooze extends TestAppTrait {
//
//            def init(): Unit = {
//              val logger: SRLogger = new SRLogger("testing connection")
//              logger.info("Starting the program")
//
//              //teams of org_id 4443
//              val team_ids = List(
//                4890, 5484, 6281, 8029, 8219, 9504, 10166, 10744, 10889, 11001, 11271, 11488, 11641,
//                11815, 11980, 12215, 12617
//              )
//
//              val threadsList = DB readOnly { implicit session =>
//
//                sql"""
//                  select id, team_id
//                  from email_threads
//
//                  where snoozed_till < now() - interval '1 days'
//                  and team_id in (${team_ids})
//                  ;
//                 """
//                  .map(rs => ThreadAndTeamId(
//                    thread_id = rs.int("id"), teamId = rs.long("team_id")
//                  ))
//                  .list
//                  .apply()
//              }
//
//              val result = inboxV3Service.unsnoozeThreadAutomatically(threadsAndTeamIds = threadsList)
//              Await.result(result, scala.concurrent.duration.Duration.Inf)
//              println(s"responseText: $result")
//              logger.warn("Finished the program")
//            }
//
//          }
//
//          unsnooze.init()
//
//
//      }
//    }
//
//  }
//}
