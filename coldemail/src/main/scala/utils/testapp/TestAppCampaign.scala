package utils.testapp

import api.accounts.models.{AccountId, OrgId}
import api.accounts.{Account, OrganizationWithCurrentData, TeamAccount, TeamId}
import api.campaigns.models.*
import api.campaigns.services.{CampaignId, ChannelSettingData}
import api.campaigns.*
import api.gpt.CreateStepsRequest
import api.prospects.AssignProspectsToCampaignResponse
import api.spammonitor.dao.{EmailSendingStatusDAO, UpdateEmailSendingStatusForm}
import api.spammonitor.model.{EmailSendingEntityType, EmailSendingEntityTypeData, SendEmailStatusData}
import io.smartreach.esp.api.emails.EmailSettingId
import io.sr.billing_common.models.PlanID.TRIAL
import org.apache.pekko.actor.ActorSystem
import org.apache.pekko.stream.Materializer
import org.joda.time.DateTime
import play.api.Logging
import play.api.libs.json.{<PERSON>s<PERSON><PERSON><PERSON>, <PERSON><PERSON>}
import play.api.libs.ws.ahc.AhcWSClient
import scalikejdbc.config.DBs
import scalikejdbc.{DB, scalikejdbcSQLInterpolationImplicitDef}
import sr_scheduler.CampaignStatus
import sr_scheduler.models.{ChannelData, ChannelType}
import utils.SRLogger
import utils.helpers.LogHelpers
import utils.mq.channel_scheduler.channels.{NextStepFinderForDrip, ScheduleTasksData}
import utils.testapp.TestAppTrait

import scala.concurrent.duration.*
import scala.concurrent.{Await, ExecutionContext, Future}
import scala.util.{Failure, Success, Try}


/*
This object holds any sort internal test function used to test features/changes manually during development.
 */
object TestAppCampaign extends Logging {

  def main(args: Array[String]): Unit = {

    if (args.length > 0) {
      DBs.setupAll()


      val applicationName = args(0)

      applicationName match {

        case "createStepsWithAI" =>
          implicit val system = ActorSystem()
          implicit val materializer = Materializer(system)
          implicit val wSClient: AhcWSClient = AhcWSClient()
          val actorContext: ExecutionContext = system.dispatcher
          given logger: SRLogger = new SRLogger("TESTAPP")

          object main extends TestAppTrait {
            def init() = {
              campaignService.createCampaign(
                  orgId = 2L,
                  accountId = 2L,
                  teamId = 2L,
                  taId = 2L,
                  data = CampaignCreateForm(
                    name = None,
                    timezone = Some("Asia/Kokata"),
                    campaign_owner_id = Some(2L),
                    campaign_type = CampaignType.MultiChannel // Sequence type campaign is getting created
                  ),
                  permittedAccountIdsForEditCampaigns = Seq(2L),
                  ownerFirstName = "Guru"
                )
                .flatMap {
                  case Left(e) =>
                    println(e.toString)
                    Future.failed(new Exception(s"Campaign Creation failed $e"))

                  case Right(campaign) =>
                    aiSequenceGenerator.createStepsInCampaign(
                      createStepsRequest = CreateStepsRequest(
                        team_type = "sales",
                        industry = "healthcare",
                        motive = "acquire new customers",
                        solution = "performance marketing",
                        reason_of_reaching_out = "no specific reason",
                        prospects_designation = "Manager",
                        language = "English",
                        tone = "conversational",
                        number_of_steps = 4
                      ),
                      orgId = OrgId(2L),
                      accountId = AccountId(2L),
                      campaignId = CampaignId(campaign.id),
                      teamId = TeamId(2L),
                      taId = 2L
                    )
                }
            }
          }

          Await.result(main.init(), 180000.millis)


        case "testDripCampaign" =>
          given logger: SRLogger = new SRLogger("TestApp :: ")

          object main extends TestAppTrait {
            def init(): Future[ScheduleTasksData] = {

              val teamId = 12L
              val orgId = 12L
              val accountId = 12L
              val taId = 12L
              val prospectIds = Seq(28L, 29L)
              val emailSettingId = 50L
              val callSettingUuid = "phone_number_2QvvSURD81Z2UDzldek2aLzVjgS"

              for {
                campaignWithStatsAndEmail: CampaignWithStatsAndEmail <- campaignService.createCampaign(
                    orgId = orgId,
                    accountId = accountId,
                    teamId = teamId,
                    taId = taId,
                    data = CampaignCreateForm(
                      name = None,
                      timezone = Some("Asia/Kolkata"),
                      campaign_owner_id = Some(accountId),
                      campaign_type = CampaignType.MultiChannel // Multichannel type campaign is getting created
                    ),
                    permittedAccountIdsForEditCampaigns = Seq(accountId),
                    ownerFirstName = "Aditya"
                  )
                  .flatMap {
                    case Left(err) =>
                      Future.failed(new Exception(s"Campaign Creation Failed :: $err"))

                    case Right(value) =>
                      Future.successful(value)
                  }

                createVariant1 <- campaignStepService.createVariant(
                    orgId = orgId,
                    data = CampaignStepVariantCreateOrUpdate(
                      parent_id = 0L,
                      step_data = CampaignStepData.ManualEmailStep(
                        subject = "Subject",
                        body = "body"
                      ),
                      step_delay = 0,
                      notes = None,
                      priority = None,
                    ),
                    teamId = teamId,
                    userId = accountId,
                    taId = taId,
                    stepId = 0,
                    campaignId = campaignWithStatsAndEmail.id,
                    campaignHeadStepId = None
                  )
                  .flatMap {
                    case Left(err) =>
                      Future.failed(new Exception(s"Error while creating variant :: $err"))

                    case Right(value) =>
                      Future.successful(value)
                  }

                createVariant2 <- campaignStepService.createVariant(
                    orgId = orgId,
                    data = CampaignStepVariantCreateOrUpdate(
                      parent_id = createVariant1.step_id,
                      step_data = CampaignStepData.CallTaskData(
                        body = "Call Body"
                      ),
                      step_delay = 86400,
                      notes = None,
                      priority = None,
                    ),
                    teamId = teamId,
                    userId = accountId,
                    taId = taId,
                    stepId = 0,
                    campaignId = campaignWithStatsAndEmail.id,
                    campaignHeadStepId = Some(createVariant1.step_id)
                  )
                  .flatMap {
                    case Left(err) =>
                      Future.failed(new Exception(s"Error while creating variant :: $err"))

                    case Right(value) =>
                      Future.successful(value)
                  }

                createVariant3 <- campaignStepService.createVariant(
                    orgId = orgId,
                    data = CampaignStepVariantCreateOrUpdate(
                      parent_id = createVariant1.step_id,
                      step_data = CampaignStepData.ManualEmailStep(
                        subject = "Subject",
                        body = "body"
                      ),
                      step_delay = 86400,
                      notes = None,
                      priority = None,
                    ),
                    teamId = teamId,
                    userId = accountId,
                    taId = taId,
                    stepId = 0,
                    campaignId = campaignWithStatsAndEmail.id,
                    campaignHeadStepId = Some(createVariant1.step_id)
                  )
                  .flatMap {
                    case Left(err) =>
                      Future.failed(new Exception(s"Error while creating variant :: $err"))

                    case Right(value) =>
                      Future.successful(value)
                  }

                account: Account <- Future.fromTry {
                  accountService.find(id = accountId)
                }

                assignProspect: AssignProspectsToCampaignResponse <- prospectService.assignProspectsToCampaign(
                  teamId = teamId,
                  campaignId = Some(campaignWithStatsAndEmail.id),
                  prospectIds = prospectIds,
                  permittedAccountIdsForEditingCampaigns = Seq(accountId),
                  permittedAccountIdsForEditingProspects = Seq(accountId),
                  account = account,
                  ignore_prospects_in_other_campaigns = IgnoreProspectsInOtherCampaigns.DoNotIgnore,
                  accountId = accountId,
                  Logger = logger
                ) match {
                  case Left(err) =>
                    Future.failed(new Exception(s"Error while assigning prospects :: $err"))

                  case Right(value) =>
                    Future.successful(value)
                }

                _: List[ChannelSettingData] <- campaignService.updateAccountSetting(
                    channel_setting_data = ChannelSettingData(
                      channel_type = ChannelType.CallChannel,
                      channel_setting_uuid = ChannelSettingUuid(callSettingUuid),
                      team_id = TeamId(teamId),
                      campaign_id = CampaignId(campaignWithStatsAndEmail.id)
                    ),
                    team_id = teamId,
                    campaign_id = campaignWithStatsAndEmail.id
                  )
                  .flatMap {
                    case Left(err) =>
                      Future.failed(new Exception(s"Error while updating call setting :: $err"))

                    case Right(value) =>
                      Future.successful(value)
                  }

                campaignAfterUpdatingEmailSetting: CampaignWithStatsAndEmail <- Future.fromTry {
                  campaignService.updateEmailSettingsV2(
                      id = CampaignId(campaignWithStatsAndEmail.id),
                      teamId = TeamId(teamId),
                      data = List(CampaignEmailSettingsV2(
                        sender_email_settings_id = emailSettingId,
                        receiver_email_settings_id = emailSettingId
                      )),
                      campaign_status = CampaignStatus.NOT_STARTED,
                        planID = TRIAL,
                      orgId = OrgId(orgId)
                    )
                    .flatMap {
                        case Left(err) =>
                            Failure(new Exception(s"Error while updating email settings :: $err"))

                      case Right(None) => Failure(new Exception("updateEmailSettingsV2 failed"))
                      case Right(Some(value)) => Success(value)
                    }
                }

                organizationWithCurrentData: OrganizationWithCurrentData <- Future.fromTry {
                  organizationService.getOrgWithCurrentData(
                      orgId = OrgId(orgId)
                    )
                    .flatMap {
                      case None =>
                        Failure(new Exception("Organization not found"))

                      case Some(org) =>
                        Success(org)
                    }
                }

                teamAccount: TeamAccount <- Future {
                  account.teams.find(_.team_id == teamId).get
                }

                dripCampaign: Int <- Future {
                  val nodes = Json.toJson(List(
                    Json.obj("id" -> createVariant1.step_id.toString, "position" -> Json.obj("x" -> 0, "y" -> 0), "data" -> Json.obj("label" -> "1", "type" -> "step")),
                    Json.obj("id" -> "condition_1", "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "has_phone_number", "type" -> "condition")),
                    Json.obj("id" -> createVariant2.step_id.toString, "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "2", "type" -> "step")),
                    Json.obj("id" -> createVariant3.step_id.toString, "position" -> Json.obj("x" -> 100, "y" -> 100), "data" -> Json.obj("label" -> "3", "type" -> "step")),
                  ))

                  val edges = Json.toJson(List(
                    Json.obj("id" -> "1-2", "source" -> createVariant1.step_id.toString, "target" -> "condition_1", "label" -> "no_condition"),
                    Json.obj("id" -> "2-3", "source" -> "condition_1", "target" -> createVariant2.step_id.toString, "label" -> "yes"),
                    Json.obj("id" -> "2-4", "source" -> "condition_1", "target" -> createVariant3.step_id.toString, "label" -> "no")
                  ))

                  DB autoCommit { implicit session =>
                    sql"""
                         UPDATE campaigns
                         SET
                            campaign_type = ${CampaignType.Drip.toString},
                            drip_campaign_nodes = ${nodes.toString}::jsonb,
                            drip_campaign_edges = ${edges.toString}::jsonb
                         WHERE
                            id = ${campaignWithStatsAndEmail.id}
                         AND team_id = $teamId
                         ;
                         """
                      .update
                      .apply()
                  }
                }

                campaign: Campaign <- campaignService.findCampaignForCampaignUtilsOnly(
                  id = campaignAfterUpdatingEmailSetting.id,
                  teamId = TeamId(teamId)
                ) match {
                  case None => Future.failed(new Exception("Campaign not found"))
                  case Some(c) => Future.successful(c)
                }

                startCampaign <- campaignStartService.startStopCampaign(
                    status = CampaignStatus.RUNNING,
                    schedule_start_at = None,
                    time_zone = None,
                    campaign = campaign,
                    org = organizationWithCurrentData,
                    team = Some(teamAccount),
                    userId = AccountId(12L),
                    teamId = TeamId(12L),
                    Logger = logger
                  )
                  .flatMap {
                    case Left(err) =>
                      Future.failed(new Exception(s"Error while starting campaign :: $err"))

                    case Right(value) =>
                      Future.successful(value)
                  }

                scheduleTaskData: ScheduleTasksData <- emailChannelScheduler.scheduleTasksForChannel(
                  channelData = ChannelData.EmailChannelData(emailSettingId = emailSettingId),
                  teamId = teamId,
                  accountService,
                  emailNotificationService,
                  campaignService,
                  campaignProspectDAO,
                  campaignProspectService,
                  campaignStepVariantDAO,
                  campaignStepDAO,
                  srShuffleUtils,
                  emailServiceCompanion,
                  templateService,
                  taskDAO,
                  taskService,
                  campaignEditedPreviewEmailDAO,
                  mqWebhookCompleted,
                  srRedisSimpleLockServiceV2,
                  campaignsMissingMergeTagService,
                  accountOrgBillingRelatedService,
                  srRollingUpdateCoreService,
                  calendarAppService
                )

                _: Seq[Long] <- Future.fromTry {
                  campaignProspectDAO._updateScheduledStatus(
                    scheduledCampaignProspectData = Seq(CampaignProspectUpdateScheduleStatus(
                      current_step_status_for_scheduler_data = CurrentStepStatusForSchedulerData.Done(
                        done_at = DateTime.now().minusDays(2)
                      ),
                      current_step_type = CampaignStepType.ManualEmailStep,
                      current_step_task_id = "",
                      step_id = createVariant1.step_id,
                      campaign_id = campaign.id,
                      prospect_id = prospectIds.head,
                      email_message_id = None,
                      current_campaign_email_settings_id = None
                    ), CampaignProspectUpdateScheduleStatus(
                      current_step_status_for_scheduler_data = CurrentStepStatusForSchedulerData.Done(
                        done_at = DateTime.now().minusDays(2)
                      ),
                      current_step_type = CampaignStepType.ManualEmailStep,
                      current_step_task_id = "",
                      step_id = createVariant1.step_id,
                      campaign_id = campaign.id,
                      prospect_id = prospectIds(1),
                      email_message_id = None,
                      current_campaign_email_settings_id = None
                    ))
                  )
                }

                _: Int <- Future {
                  DB autoCommit { implicit session =>
                    sql"""
                         UPDATE campaigns_prospects
                         SET last_scheduled = now() - interval '2 days'
                         WHERE campaign_id = ${campaign.id}
                         AND prospect_id IN (${prospectIds})
                         AND team_id = ${teamId}
                         ;
                         """
                      .update
                      .apply()
                  }
                }

                _: Int <- Future {
                  DB autoCommit { implicit session =>
                    sql"""
                         UPDATE email_settings
                         SET latest_email_scheduled_at = now() - interval '2 days',
                         last_read_for_replies = now()
                         WHERE id = $emailSettingId
                         AND team_id = ${teamId}
                         ;
                         """
                      .update
                      .apply()
                  }
                }

                _: Int <- Future {
                  DB autoCommit { implicit session =>
                    sql"""
                         UPDATE prospects_metadata
                         SET last_touched_at = now() - interval '2 days',
                         last_prospects_touched_count_updated_at = now() - interval '2 days'
                         WHERE prospect_id IN ($prospectIds)
                         AND team_id = ${teamId}
                         ;
                         """
                      .update
                      .apply()
                  }
                }

                _: Int <- Future {
                  DB autoCommit { implicit session =>
                    sql"""
                         UPDATE campaigns
                         SET last_scheduled_at = now() - interval '2 days'
                         WHERE id = ${campaign.id}
                         AND team_id = $teamId
                         ;
                         """
                      .update
                      .apply()
                  }
                }

                scheduleTaskData1: ScheduleTasksData <- callChannelScheduler.scheduleTasksForChannel(
                  channelData = ChannelData.CallChannelData(callSettingUuid = callSettingUuid),
                  teamId = teamId,
                  accountService,
                  emailNotificationService,
                  campaignService,
                  campaignProspectDAO,
                  campaignProspectService,
                  campaignStepVariantDAO,
                  campaignStepDAO,
                  srShuffleUtils,
                  emailServiceCompanion,
                  templateService,
                  taskDAO,
                  taskService,
                  campaignEditedPreviewEmailDAO,
                  mqWebhookCompleted,
                  srRedisSimpleLockServiceV2,
                  campaignsMissingMergeTagService,
                  accountOrgBillingRelatedService,
                  srRollingUpdateCoreService,
                  calendarAppService
                )

                _: Int <- Future {
                  DB autoCommit { implicit session =>
                    sql"""
                         UPDATE campaigns
                         SET last_scheduled_at = now() - interval '2 days'
                         WHERE id = ${campaign.id}
                         AND team_id = $teamId
                         ;
                         """
                      .update
                      .apply()
                  }
                }

                scheduleTaskData2: ScheduleTasksData <- emailChannelScheduler.scheduleTasksForChannel(
                  channelData = ChannelData.EmailChannelData(emailSettingId = emailSettingId),
                  teamId = teamId,
                  accountService,
                  emailNotificationService,
                  campaignService,
                  campaignProspectDAO,
                  campaignProspectService,
                  campaignStepVariantDAO,
                  campaignStepDAO,
                  srShuffleUtils,
                  emailServiceCompanion,
                  templateService,
                  taskDAO,
                  taskService,
                  campaignEditedPreviewEmailDAO,
                  mqWebhookCompleted,
                  srRedisSimpleLockServiceV2,
                  campaignsMissingMergeTagService,
                  accountOrgBillingRelatedService,
                  srRollingUpdateCoreService,
                  calendarAppService
                )
              } yield {
                scheduleTaskData1
              }

            }
          }

          implicit val system = ActorSystem()
          implicit val materializer = Materializer(system)
          implicit val wSClient: AhcWSClient = AhcWSClient()
          val ec: ExecutionContext = system.dispatcher

          val resFut = main.init()
            .map(res => {
              println(res)
            })(ec)
            .recover {
              case e =>
                println(e.toString)
                println(LogHelpers.getStackTraceAsString(e))
            }(ec)

          Await.result(resFut, 600000.millis)

        case "getHeadStep" =>
          implicit val system = ActorSystem()
          implicit val materializer = Materializer(system)
          implicit val wSClient: AhcWSClient = AhcWSClient()
          implicit val actorContext: ExecutionContext = system.dispatcher
          given logger: SRLogger = new SRLogger("TestApp :: ")

          val nodes = List(
            Json.obj("id" -> "1", "position" -> Json.obj("x" -> 0, "y" -> 0), "data" -> Json.obj("label" -> "1", "type" -> "step")),
            Json.obj("id" -> "condition_1", "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "has_phone_number", "type" -> "condition")),
            Json.obj("id" -> "2", "position" -> Json.obj("x" -> -100, "y" -> 100), "data" -> Json.obj("label" -> "2", "type" -> "step")),
            Json.obj("id" -> "3", "position" -> Json.obj("x" -> 100, "y" -> 100), "data" -> Json.obj("label" -> "3", "type" -> "step")),
          )

          val edges = List(
            Json.obj("id" -> "1-2", "source" -> "1", "target" -> "condition_1", "label" -> "no_condition"),
            Json.obj("id" -> "2-3", "source" -> "condition_1", "target" -> "2", "label" -> "yes"),
            Json.obj("id" -> "2-4", "source" -> "condition_1", "target" -> "3", "label" -> "no")
          )

          val res = NextStepFinderForDrip.getLastSteps(
            edges = edges,
            head_node_id = "1"
          )

          println(Await.result(res, 60000.millis))

        case "findChildrenOfParentStepStringJSON" =>

          implicit val system = ActorSystem()
          implicit val materializer = Materializer(system)
          implicit val wSClient: AhcWSClient = AhcWSClient()
          implicit val actorContext: ExecutionContext = system.dispatcher

          val nodesJSON = Json.parse(
            """
              |[{"id":"496641","data":{"type":"step","label":"auto_linkedin_view_profile"},"type":"auto_linkedin_view_profile","position":{"x":0,"y":0}},{"id":"496642","data":{"type":"step","label":"auto_send_linkedin_connection_request"},"type":"auto_send_linkedin_connection_request","position":{"x":0,"y":0}},{"id":"PBfJT","data":{"type":"condition","label":"linkedIn_profile_connected"},"type":"linkedIn_profile_connected","position":{"x":0,"y":0}},{"id":"496648","data":{"type":"step","label":"auto_send_linkedin_message"},"type":"auto_send_linkedin_message","position":{"x":0,"y":0}},{"id":"496653","data":{"type":"step","label":"auto_linkedin_view_profile"},"type":"auto_linkedin_view_profile","position":{"x":0,"y":0}},{"id":"sLYGi","data":{"type":"condition","label":"linkedIn_message_not_sent"},"type":"linkedIn_message_not_sent","position":{"x":0,"y":0}},{"id":"496889","data":{"type":"step","label":"send_email"},"type":"send_email","position":{"x":0,"y":0}},{"id":"496909","data":{"type":"step","label":"general_task"},"type":"general_task","position":{"x":0,"y":0}},{"id":"tmzwT","data":{"type":"condition","label":"linkedIn_profile_connected"},"type":"linkedIn_profile_connected","position":{"x":0,"y":0}},{"id":"IuINH","data":{"type":"condition","label":"has_phone_number"},"type":"has_phone_number","position":{"x":0,"y":0}},{"id":"496654","data":{"type":"step","label":"send_sms"},"type":"send_sms","position":{"x":0,"y":0}},{"id":"496859","data":{"type":"step","label":"send_email"},"type":"send_email","position":{"x":0,"y":0}},{"id":"nuo9f","data":{"type":"condition","label":"has_replied_interested"},"type":"has_replied_interested","position":{"x":0,"y":0}},{"id":"496890","data":{"type":"step","label":"general_task"},"type":"general_task","position":{"x":0,"y":0}},{"id":"496895","data":{"type":"step","label":"send_email"},"type":"send_email","position":{"x":0,"y":0}},{"id":"eJLGx","data":{"type":"condition","label":"has_replied"},"type":"has_replied","position":{"x":0,"y":0}},{"id":"NPCE9","data":{"type":"condition","label":"linkedIn_profile_connected"},"type":"linkedIn_profile_connected","position":{"x":0,"y":0}},{"id":"496897","data":{"type":"step","label":"manual_send_email"},"type":"manual_send_email","position":{"x":0,"y":0}},{"id":"Ht3aQ","data":{"type":"condition","label":"has_replied_interested"},"type":"has_replied_interested","position":{"x":0,"y":0}},{"id":"496910","data":{"type":"step","label":"general_task"},"type":"general_task","position":{"x":0,"y":0}},{"id":"Af3j1","data":{"type":"condition","label":"has_phone_number"},"type":"has_phone_number","position":{"x":0,"y":0}},{"id":"496917","data":{"type":"step","label":"general_task"},"type":"general_task","position":{"x":0,"y":0}}]
              |""".stripMargin
          ).validate[List[JsValue]].get

          val edgesJSON = Json.parse(
            """
              |[{"id":"496641-496642","type":"nonConditionalEdge","label":"no_condition","style":{"opacity":1},"source":"496641","target":"496642","selected":false},{"id":"496642-PBfJT","type":"nonConditionalEdge","label":"no_condition","style":{"opacity":1},"source":"496642","target":"PBfJT","selected":false},{"id":"PBfJT-496648","type":"yesEdge","label":"yes","style":{"opacity":1},"source":"PBfJT","target":"496648","selected":false},{"id":"PBfJT-496653","type":"noEdge","label":"no","style":{"opacity":1},"source":"PBfJT","target":"496653","selected":false},{"id":"496648-sLYGi","type":"nonConditionalEdge","label":"no_condition","style":{"opacity":1},"source":"496648","target":"sLYGi","selected":false},{"id":"sLYGi-496889","type":"yesEdge","label":"yes","style":{"opacity":1},"source":"sLYGi","target":"496889"},{"id":"sLYGi-496909","type":"noEdge","label":"no","style":{"opacity":1},"source":"sLYGi","target":"496909"},{"id":"496653-tmzwT","type":"nonConditionalEdge","label":"no_condition","style":{"opacity":1},"source":"496653","target":"tmzwT","selected":false},{"id":"IuINH-496654","type":"yesEdge","label":"yes","style":{"opacity":1},"source":"IuINH","target":"496654"},{"id":"496859-IuINH","type":"nonConditionalEdge","label":"no_condition","style":{"opacity":1},"source":"496859","target":"IuINH"},{"id":"496889-nuo9f","type":"nonConditionalEdge","label":"no_condition","style":{"opacity":1},"source":"496889","target":"nuo9f"},{"id":"nuo9f-496890","type":"yesEdge","label":"yes","style":{"opacity":1},"source":"nuo9f","target":"496890"},{"id":"tmzwT-496859","type":"yesEdge","label":"yes","style":{"opacity":1},"source":"tmzwT","target":"496859"},{"id":"tmzwT-496895","type":"noEdge","label":"no","style":{"opacity":1},"source":"tmzwT","target":"496895"},{"id":"496895-eJLGx","type":"nonConditionalEdge","label":"no_condition","style":{"opacity":1},"source":"496895","target":"eJLGx"},{"id":"eJLGx-NPCE9","type":"noEdge","label":"no","style":{"opacity":1},"source":"eJLGx","target":"NPCE9"},{"id":"NPCE9-496897","type":"noEdge","label":"no","style":{"opacity":1},"source":"NPCE9","target":"496897"},{"id":"496909-Ht3aQ","type":"nonConditionalEdge","label":"no_condition","style":{"opacity":1},"source":"496909","target":"Ht3aQ"},{"id":"Ht3aQ-496910","type":"yesEdge","label":"yes","style":{"opacity":1},"source":"Ht3aQ","target":"496910"},{"id":"496897-Af3j1","type":"nonConditionalEdge","label":"no_condition","style":{"opacity":1},"source":"496897","target":"Af3j1"},{"id":"Af3j1-496917","type":"yesEdge","label":"yes","style":{"opacity":1},"source":"Af3j1","target":"496917"}]
              |""".stripMargin
          ).validate[List[JsValue]].get

          val res = NextStepFinderForDrip.findNextStep(
            edges = edgesJSON,
            nodes = nodesJSON,
            parent_step_id = 496641
          )

          Await.ready(res, Duration.Inf).value match {

            case None =>

              println("findNextStep String JSON - None found")

            case Some(Failure(exception)) =>

              println(s"findNextStep String JSON - Failed - ${LogHelpers.getStackTraceAsString(exception)}")

            case Some(Success(value)) =>

              println(s"findNextStep String JSON - Success - $value")

          }

        case "findChildrenOfParentStep" =>
          implicit val system = ActorSystem()
          implicit val materializer = Materializer(system)
          implicit val wSClient: AhcWSClient = AhcWSClient()
          implicit val actorContext: ExecutionContext = system.dispatcher

          val nodes = List(
            Json.obj("id" -> "1", "data" -> Json.obj("label" -> "1", "type" -> "step")),
            Json.obj("id" -> "2", "data" -> Json.obj("label" -> "has_email", "type" -> "condition")),
            Json.obj("id" -> "3", "data" -> Json.obj("label" -> "3", "type" -> "step")),
            Json.obj("id" -> "4", "data" -> Json.obj("label" -> "has_linkedin_url", "type" -> "condition")),
            Json.obj("id" -> "5", "data" -> Json.obj("label" -> "5", "type" -> "step")),
            Json.obj("id" -> "6", "data" -> Json.obj("label" -> "6", "type" -> "step"))
          )

          val edges = List(
            Json.obj("id" -> "1-2", "source" -> "1", "target" -> "2", "label" -> "next"),
            Json.obj("id" -> "2-3", "source" -> "2", "target" -> "3", "label" -> "yes"),
            Json.obj("id" -> "2-4", "source" -> "2", "target" -> "4", "label" -> "no"),
            Json.obj("id" -> "4-5", "source" -> "4", "target" -> "5", "label" -> "yes"),
            Json.obj("id" -> "4-6", "source" -> "4", "target" -> "6", "label" -> "no")
          )

          val res = NextStepFinderForDrip.findNextStep(
            edges = edges,
            nodes = nodes,
            parent_step_id = 1
          )

          Await.result(res.map(result => println(result)), 60000.millis)

        //  sbt "coldemail/runMain utils.TestApp createIsLatestEntryForCampaign"
        case "createIsLatestEntryForCampaign" =>
          val emailSendStatusDAO = new EmailSendingStatusDAO()
          val logger = new SRLogger("createIsLatestEntryForCampaign :: ")

          Try {
            DB readOnly { implicit session =>
              sql"""
                   SELECT c.id, c.team_id, t.org_id FROM campaigns c
                    INNER JOIN teams t ON t.id = c.team_id
                    INNER JOIN organizations org ON org.id = t.org_id
                    WHERE NOT EXISTS (
                      SELECT * FROM email_send_statuses ess2
                      WHERE c.team_id = ess2.team_id
                      AND c.id = ess2.campaign_id
                      AND ess2.org_id = t.org_id
                      AND ess2.entity_type = ${EmailSendingEntityType.Campaign.toString}
                      AND ess2.is_latest
                    )
                    AND t.active
                    AND org.active
                    ;
                   """
                .map(rs => {
                  EmailSendingEntityTypeData.CampaignData(
                    orgId = OrgId(rs.long("org_id")),
                    campaignId = rs.long("id"),
                    teamId = rs.long("team_id")
                  )
                })
                .list
                .apply()
            }
          } match {
            case Failure(e) =>
              logger.error("Error while finding campaigns that don't have is_latest entry", e)

            case Success(data) =>
              logger.info(s"Adding is_latest entry for $data")
              data.map(entityData => {
                emailSendStatusDAO.addingEmailSendingStatusTry(
                  updateEmailSendingStatusForm = UpdateEmailSendingStatusForm(
                    entityType = entityData,
                    orgId = entityData.getOrgId(),
                    sendEmailStatus = SendEmailStatusData.AllowedData()
                  )
                ) match {
                  case Failure(e) =>
                    logger.error(s"Error while adding email Sending Status entry for ${entityData}", e)

                  case Success(_) =>
                    logger.info(s"Added Entry for $entityData")
                }
              })
          }


        case "getSentProspectCountForEmailAccount" =>
          object GetSentProspectCountForEmailAccount extends TestAppTrait {
            def init: Try[Map[EmailSettingId, Int]] = campaignProspectDAO.getSentOrScheduledProspectsCountForEmail(
              emailSettingIds = Seq(EmailSettingId(1)),
              accountTimezone = "Asia/Kolkata",
              countOnlySentEmails = true,
              logger = new SRLogger(logRequestId = "From TestApp"),
              TeamId(1L)
            )
          }

          val result = GetSentProspectCountForEmailAccount.init
          println(result)



        // sbt "coldemail/runMain utils.TestApp getSentAndScheduledProspectCountForEmailAccount"
        case "getSentAndScheduledProspectCountForEmailAccount" =>
          object GetSentOrScheduledProspectCountForEmailAccount extends TestAppTrait {
            def init: Try[Map[EmailSettingId, Int]] = campaignProspectDAO.getSentOrScheduledProspectsCountForEmail(
              emailSettingIds = Seq(EmailSettingId(1)),
              accountTimezone = "Asia/Kolkata",
              logger = new SRLogger(logRequestId = "From TestApp"),
              teamId = TeamId(1L)
            )

          }

          val result = GetSentOrScheduledProspectCountForEmailAccount.init
          println(result)


        // sbt "coldemail/runMain utils.TestApp getSentProspectCountForCampaign"
        case "getSentProspectCountForCampaign" =>
          object GetSentProspectCountForCampaign extends TestAppTrait {
            def init = campaignProspectDAO.getScheduledProspectsCountForCampaign(
              Logger = new SRLogger("From TestApp"),
              emailSettingIds = Seq(EmailSettingId(3)),
              teamId = 11,
              campaignIdAndTimezone = Seq((19, "Asia/Kolkata")),
              countOnlySentProspects = true
            )
          }

          val result = GetSentProspectCountForCampaign.init
          println(result)


        // sbt "coldemail/runMain utils.TestApp getSentAndScheduledProspectCountForCampaign"
        case "getSentAndScheduledProspectCountForCampaign" =>
          object GetSentOrScheduledProspectCountForCampaign extends TestAppTrait {
            def init = campaignProspectDAO.getScheduledProspectsCountForCampaign(
              Logger = new SRLogger("From TestApp"),
              emailSettingIds = Seq(EmailSettingId(3)),
              teamId = 11,
              campaignIdAndTimezone = Seq((19, "Asia/Kolkata")),
            )

          }

          val result = GetSentOrScheduledProspectCountForCampaign.init
          println(result)

        case "publishToCampaignAISequence" =>
          given logger: SRLogger = new SRLogger("TestApp :: publishToCampaignAISequence ::")

          object main extends TestAppTrait {
            def init() = Try {
              campaignAISequenceService.triggerCampaignAISequenceGeneration(
                campaignId = CampaignId(265L),
                teamId = TeamId(12L),
                orgId = OrgId(12L),
                accountId = AccountId(12L),
                taId = 12L,
                createStepsRequest = CreateStepsRequest(
                  team_type = "Sales Team",
                  industry = "Banking",
                  motive = "acquire new customers",
                  solution = "performance marketing",
                  reason_of_reaching_out = "no specific reason",
                  prospects_designation = "Vice President",
                  language = "English",
                  tone = "Conversational",
                  number_of_steps = 5
                )
              )
            }
          }

          main.init() match {
            case Failure(e) =>
              logger.error("Error while publishing to mqCampaignAISequenceGenerator", e)

            case Success(v) =>
              logger.info("Publish Successful")
          }
      }
    }

  }
}
