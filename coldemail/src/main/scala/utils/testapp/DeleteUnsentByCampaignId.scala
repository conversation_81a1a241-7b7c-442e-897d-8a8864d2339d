package utils.testapp

import api.accounts.TeamId
import api.emails.models.DeletionReason
import io.smartreach.esp.api.emails.EmailSettingId
import play.api.Logging
import scalikejdbc.config.DBs
import utils.SRLogger
import utils.email.models.DeleteEmailsScheduledType

object DeleteUnsentByCampaignId extends Logging {

  def main(args: Array[String]): Unit = {

    if (args.length > 0) {
      DBs.setupAll()

      val applicationName = args(0)

      applicationName match {

        case "test_deleteUnsentByCampaignId" =>

         object main extends TestAppTrait {

           def init() = {

             val emailThreadId = 783860L
             val teamId = TeamId(id = 80)
             val senderEmailSettingIds: Seq[EmailSettingId] = Seq(EmailSettingId(1L))
             val campaignId = 374L
             given Logger: SRLogger = new SRLogger("test_deleteUnsentByCampaignId")

             val result = selectAndPublishForDeletionService.selectAndPublishForDeletion(
               deletion_reason = DeletionReason.Other(reason = "test_deleteUnsentByCampaignId"),
               deleteEmailsScheduledType = DeleteEmailsScheduledType.DeleteUnsentByCampaignId(
                 campaignId = campaignId,
                 teamId = teamId,
                 senderEmailSettingIds = senderEmailSettingIds,
               )
             )
             println(result)
             
           }
         }

          main.init()
      }
    }

  }

}