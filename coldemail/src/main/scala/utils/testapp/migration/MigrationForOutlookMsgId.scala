package utils.testapp.migration

import api.accounts.{CountOfMigration, IdsToUpdate, MigrationRecursiveService, TeamId}
import api.campaigns.services.CampaignId
import api.prospects.models.ProspectId
import org.joda.time.DateTime
import play.api.Logging
import scalikejdbc.*
import scalikejdbc.config.DBs
import scalikejdbc.interpolation.SQLSyntax
import utils.SRLogger
import utils.testapp.TestAppTrait
import utils.testapp.__GetOptedOutProspectDetails.emailScheduledDAO
import utils.uuid.{IdTeamId, TableNames}

import scala.concurrent.duration.Duration
import scala.concurrent.{Await, Future}
import scala.util.Try

object MigrationForOutlookMsgId extends Logging with TestAppTrait {

  def main(args: Array[String]): Unit = {

    if (args.length > 0) {
      DBs.setupAll()

      val applicationName = args(0)

      applicationName match {

        case "migrate_outlook_msg_id" =>

          given logger: SRLogger = new utils.SRLogger("MigrationForOutlookMsgId") //.Logger

          def getTableIds(
                           ids_to_update_from: IdsToUpdate,
                           count_per_cycle: CountOfMigration,
                           table_name: TableNames
                         ): Try[List[IdsToUpdate]] = Try {
            DB readOnly { implicit session =>

               sql"""
                select tb.id
                from emails_scheduled tb
                where tb.id > ${ids_to_update_from.id}
                AND outlook_msg_id_old is not null
                AND scheduled_at > now() - interval '7 days'
                ORDER BY id ASC
                LIMIT ${count_per_cycle.count}
                ;
              """
                .map(rs => IdsToUpdate(rs.long("id")))
                .list
                .apply()
            }
          }

          def updateTableUuids(
                                ids: List[IdsToUpdate],
                                table_name: TableNames
                              ): Try[CountOfMigration] = Try {

            val count = DB autoCommit { implicit session =>
                sql"""
                    UPDATE email_message_data ems
                    SET outlook_msg_id = es.outlook_msg_id_old
                    FROM emails_scheduled es
                    WHERE ems.es_id = es.id
                    AND es.id IN (${ids.map(_.id)})
                    RETURNING ems.es_id;
                """
                .map(_.long("es_id"))
                .list
                .apply()
                .length
            }

            CountOfMigration(count)

          }

          val result = Future{
            MigrationRecursiveService.recursiveMigrationMethod(
              getIdsToMigrateMethod = getTableIds,
              updateTheIds = updateTableUuids,
              total_updated = CountOfMigration(0),
              ids_to_update_from = IdsToUpdate(551000117),
              count_per_cycle = CountOfMigration(1000),
              table_name = TableNames.EmailsScheduled
            )
          }

          Await.result(result, Duration.Inf)



      }
    }

  }

}
