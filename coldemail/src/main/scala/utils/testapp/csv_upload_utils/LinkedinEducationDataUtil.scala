/* 17-jun-2025: [DO_NOT_REMOVE] [LEADFINDERUPLOAD] was slowing down compilation. Only needed for the Lead database upload.

package utils.testapp.csv_upload_utils

import scalikejdbc.metadata.Column
import utils.testapp.csv_upload.LinkedinEducationData

object LinkedinEducationDataUtil {
  
  def constructDataForLinkedinEducation(
                                        column: String,
                                        linkedinEducationDataMap: Map[Long,LinkedinEducationData],
                                        csvRow: Map[String, String],
                                        header: (String,String),
                                        prospectDataConcat: String,
                                        no: String
                                       ): (Map[Long,LinkedinEducationData], String) = {
    
    val linkedinEducationDataDefault: LinkedinEducationData = LinkedinEducationData(
      degrees_0 = None,
      degrees_1 = None,
      degrees_2 = None,
      degrees_3 = None,
      end_date = None,
      gpa = None,
      majors_0 = None,
      majors_1 = None,
      majors_2 = None,
      majors_3 = None,
      majors_4 = None,
      minors = None,
      minors_0 = None,
      minors_1 = None,
      school_domain = None,
      school_facebook_url = None,
      school_id = None,
      school_linkedin_id = None,
      school_linkedin_url = None,
      school_location_continent = None,
      school_location_country = None,
      school_location_locality = None,
      school_location_name = None,
      school_location_region = None,
      school_name = None,
      school_twitter_url = None,
      school_type = None,
      school_website = None,
      start_date = None,
      summary = None,
      degrees = None,
      majors = None,
      school_location = None,
      education_data_hash = None,
      school = None
    )
    
    var linkedinEducationDataMapResult = linkedinEducationDataMap
    var prospectDataConcatResult = prospectDataConcat
    column match {
      case "degrees_0" =>
        if (linkedinEducationDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinEducationDataUpdated = linkedinEducationDataMapResult(no.toInt).copy(
              degrees_0 = csvRow.get(header._1)
            )
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataDefault.copy(
              degrees_0 = csvRow.get(header._1)
            ))

          }
        }

      case "degrees_1" =>
        if (linkedinEducationDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinEducationDataUpdated = linkedinEducationDataMapResult(no.toInt).copy(
              degrees_1 = csvRow.get(header._1)
            )
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataDefault.copy(
              degrees_1 = csvRow.get(header._1)
            ))

          }
        }

      case "degrees_2" =>
        if (linkedinEducationDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinEducationDataUpdated = linkedinEducationDataMapResult(no.toInt).copy(
              degrees_2 = csvRow.get(header._1)
            )
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataDefault.copy(
              degrees_2 = csvRow.get(header._1)
            ))

          }
        }

      case "degrees_3" =>
        if (linkedinEducationDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinEducationDataUpdated = linkedinEducationDataMapResult(no.toInt).copy(
              degrees_3 = csvRow.get(header._1)
            )
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataDefault.copy(
              degrees_3 = csvRow.get(header._1)
            ))

          }
        }

      case "end_date" =>
        if (linkedinEducationDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinEducationDataUpdated = linkedinEducationDataMapResult(no.toInt).copy(
              end_date = csvRow.get(header._1)
            )
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataDefault.copy(
              end_date = csvRow.get(header._1)
            ))

          }
        }

      case "gpa" =>
        if (linkedinEducationDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinEducationDataUpdated = linkedinEducationDataMapResult(no.toInt).copy(
              gpa = csvRow.get(header._1)
            )
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataDefault.copy(
              gpa = csvRow.get(header._1)
            ))

          }
        }

      case "majors_0" =>
        if (linkedinEducationDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinEducationDataUpdated = linkedinEducationDataMapResult(no.toInt).copy(
              majors_0 = csvRow.get(header._1)
            )
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataDefault.copy(
              majors_0 = csvRow.get(header._1)
            ))

          }
        }

      case "majors_1" =>
        if (linkedinEducationDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinEducationDataUpdated = linkedinEducationDataMapResult(no.toInt).copy(
              majors_1 = csvRow.get(header._1)
            )
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataDefault.copy(
              majors_1 = csvRow.get(header._1)
            ))

          }
        }

      case "majors_2" =>
        if (linkedinEducationDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinEducationDataUpdated = linkedinEducationDataMapResult(no.toInt).copy(
              majors_2 = csvRow.get(header._1)
            )
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataDefault.copy(
              majors_2 = csvRow.get(header._1)
            ))

          }
        }

      case "majors_3" =>
        if (linkedinEducationDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinEducationDataUpdated = linkedinEducationDataMapResult(no.toInt).copy(
              majors_3 = csvRow.get(header._1)
            )
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataDefault.copy(
              majors_3 = csvRow.get(header._1)
            ))

          }
        }

      case "majors_4" =>
        if (linkedinEducationDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinEducationDataUpdated = linkedinEducationDataMapResult(no.toInt).copy(
              majors_4 = csvRow.get(header._1)
            )
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataDefault.copy(
              majors_4 = csvRow.get(header._1)
            ))

          }
        }

      case "minors" =>
        if (linkedinEducationDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinEducationDataUpdated = linkedinEducationDataMapResult(no.toInt).copy(
              minors = csvRow.get(header._1)
            )
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataDefault.copy(
              minors = csvRow.get(header._1)
            ))

          }
        }

      case "minors_0" =>
        if (linkedinEducationDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinEducationDataUpdated = linkedinEducationDataMapResult(no.toInt).copy(
              minors_0 = csvRow.get(header._1)
            )
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataDefault.copy(
              minors_0 = csvRow.get(header._1)
            ))

          }
        }

      case "minors_1" =>
        if (linkedinEducationDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinEducationDataUpdated = linkedinEducationDataMapResult(no.toInt).copy(
              minors_1 = csvRow.get(header._1)
            )
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataDefault.copy(
              minors_1 = csvRow.get(header._1)
            ))

          }
        }

      case "school_domain" =>
        if (linkedinEducationDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinEducationDataUpdated = linkedinEducationDataMapResult(no.toInt).copy(
              school_domain = csvRow.get(header._1)
            )
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataDefault.copy(
              school_domain = csvRow.get(header._1)
            ))

          }
        }

      case "school_facebook_url" =>
        if (linkedinEducationDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinEducationDataUpdated = linkedinEducationDataMapResult(no.toInt).copy(
              school_facebook_url = csvRow.get(header._1)
            )
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataDefault.copy(
              school_facebook_url = csvRow.get(header._1)
            ))

          }
        }

      case "school" =>
        if (linkedinEducationDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinEducationDataUpdated = linkedinEducationDataMapResult(no.toInt).copy(
              school = csvRow.get(header._1)
            )
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataDefault.copy(
              school = csvRow.get(header._1)
            ))

          }
        }

      case "school_id" =>
        if (linkedinEducationDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinEducationDataUpdated = linkedinEducationDataMapResult(no.toInt).copy(
              school_id = csvRow.get(header._1)
            )
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataDefault.copy(
              school_id = csvRow.get(header._1)
            ))

          }
        }

      case "school_linkedin_id" =>
        if (linkedinEducationDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinEducationDataUpdated = linkedinEducationDataMapResult(no.toInt).copy(
              school_linkedin_id = csvRow.get(header._1)
            )
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataDefault.copy(
              school_linkedin_id = csvRow.get(header._1)
            ))

          }
        }

      case "school_linkedin_url" =>
        if (linkedinEducationDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinEducationDataUpdated = linkedinEducationDataMapResult(no.toInt).copy(
              school_linkedin_url = csvRow.get(header._1)
            )
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataDefault.copy(
              school_linkedin_url = csvRow.get(header._1)
            ))

          }
        }

      case "school_location_continent" =>
        if (linkedinEducationDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinEducationDataUpdated = linkedinEducationDataMapResult(no.toInt).copy(
              school_location_continent = csvRow.get(header._1)
            )
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataDefault.copy(
              school_location_continent = csvRow.get(header._1)
            ))

          }
        }

      case "school_location_country" =>
        if (linkedinEducationDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinEducationDataUpdated = linkedinEducationDataMapResult(no.toInt).copy(
              school_location_country = csvRow.get(header._1)
            )
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataDefault.copy(
              school_location_country = csvRow.get(header._1)
            ))

          }
        }

      case "school_location_locality" =>
        if (linkedinEducationDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinEducationDataUpdated = linkedinEducationDataMapResult(no.toInt).copy(
              school_location_locality = csvRow.get(header._1)
            )
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataDefault.copy(
              school_location_locality = csvRow.get(header._1)
            ))

          }
        }

      case "school_location_name" =>
        if (linkedinEducationDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinEducationDataUpdated = linkedinEducationDataMapResult(no.toInt).copy(
              school_location_name = csvRow.get(header._1)
            )
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataDefault.copy(
              school_location_name = csvRow.get(header._1)
            ))

          }
        }

      case "school_location_region" =>
        if (linkedinEducationDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinEducationDataUpdated = linkedinEducationDataMapResult(no.toInt).copy(
              school_location_region = csvRow.get(header._1)
            )
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataDefault.copy(
              school_location_region = csvRow.get(header._1)
            ))

          }
        }

      case "school_name" =>
        if (linkedinEducationDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinEducationDataUpdated = linkedinEducationDataMapResult(no.toInt).copy(
              school_name = csvRow.get(header._1)
            )
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataDefault.copy(
              school_name = csvRow.get(header._1)
            ))

          }
        }

      case "school_twitter_url" =>
        if (linkedinEducationDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinEducationDataUpdated = linkedinEducationDataMapResult(no.toInt).copy(
              school_twitter_url = csvRow.get(header._1)
            )
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataDefault.copy(
              school_twitter_url = csvRow.get(header._1)
            ))

          }
        }

      case "school_type" =>
        if (linkedinEducationDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinEducationDataUpdated = linkedinEducationDataMapResult(no.toInt).copy(
              school_type = csvRow.get(header._1)
            )
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataDefault.copy(
              school_type = csvRow.get(header._1)
            ))

          }
        }

      case "school_website" =>
        if (linkedinEducationDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinEducationDataUpdated = linkedinEducationDataMapResult(no.toInt).copy(
              school_website = csvRow.get(header._1)
            )
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataDefault.copy(
              school_website = csvRow.get(header._1)
            ))

          }
        }

      case "start_date" =>
        if (linkedinEducationDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinEducationDataUpdated = linkedinEducationDataMapResult(no.toInt).copy(
              start_date = csvRow.get(header._1)
            )
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataDefault.copy(
              start_date = csvRow.get(header._1)
            ))

          }
        }

      case "summary" =>
        if (linkedinEducationDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinEducationDataUpdated = linkedinEducationDataMapResult(no.toInt).copy(
              summary = csvRow.get(header._1)
            )
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataDefault.copy(
              summary = csvRow.get(header._1)
            ))

          }
        }

      case "degrees" =>
        if (linkedinEducationDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinEducationDataUpdated = linkedinEducationDataMapResult(no.toInt).copy(
              degrees = csvRow.get(header._1)
            )
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataDefault.copy(
              degrees = csvRow.get(header._1)
            ))

          }
        }

      case "majors" =>
        if (linkedinEducationDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinEducationDataUpdated = linkedinEducationDataMapResult(no.toInt).copy(
              majors = csvRow.get(header._1)
            )
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataDefault.copy(
              majors = csvRow.get(header._1)
            ))

          }
        }

      case "school_location" =>
        if (linkedinEducationDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinEducationDataUpdated = linkedinEducationDataMapResult(no.toInt).copy(
              school_location = csvRow.get(header._1)
            )
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinEducationDataMapResult += (no.toLong -> linkedinEducationDataDefault.copy(
              school_location = csvRow.get(header._1)
            ))

          }
        }

      case _ =>
        println(s"education_table ${header._2}")
        None

    }
    (linkedinEducationDataMapResult, prospectDataConcatResult)
  }

}
*/
