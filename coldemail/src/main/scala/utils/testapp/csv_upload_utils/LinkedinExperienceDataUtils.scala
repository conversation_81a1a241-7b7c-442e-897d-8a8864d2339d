/* 17-jun-2025: [DO_NOT_REMOVE] [LEADFINDERUPLOAD] was slowing down compilation. Only needed for the Lead database upload.

package utils.testapp.csv_upload_utils

import utils.testapp.csv_upload.LinkedinExperienceData

object LinkedinExperienceDataUtils {

  def constructDataForLinkedinExperience(
                                         column: String,
                                         linkedinExperienceDataMap: Map[Long, LinkedinExperienceData],
                                         csvRow: Map[String, String],
                                         header: (String, String),
                                         prospectDataConcat: String,
                                         no: String
                                       ): (Map[Long, LinkedinExperienceData], String) = {

    val linkedinExperienceDataDefault = LinkedinExperienceData(
      company_facebook_url = None,
      company_founded = None,
      company_id = None,
      company_industry = None,
      company_linkedin_id = None,
      company_linkedin_url = None,
      company_location = None,
      company_location_address_line_2 = None,
      company_location_continent = None,
      company_location_country = None,
      company_location_geo = None,
      company_location_locality = None,
      company_location_metro = None,
      company_location_name = None,
      company_location_postal_code = None,
      company_location_region = None,
      company_location_street_address = None,
      company_name = None,
      company_size = None,
      company_twitter_url = None,
      company_website = None,
      end_date = None,
      is_primary = None,
      location_names = None,
      location_names_0 = None,
      location_names_1 = None,
      location_names_2 = None,
      location_names_3 = None,
      start_date = None,
      summary = None,
      title_levels = None,
      title_levels_0 = None,
      title_levels_1 = None,
      title_levels_2 = None,
      title_name = None,
      title_role = None,
      title_sub_role = None,
      experience_data_hash = None
    )

    var linkedinExperienceDataMapResult = linkedinExperienceDataMap
    var prospectDataConcatResult = prospectDataConcat
    column match {
      case "company_facebook_url" =>
        if (linkedinExperienceDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinExperienceDataUpdated = linkedinExperienceDataMapResult(no.toInt).copy(
              company_facebook_url = csvRow.get(header._1)
            )
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataDefault.copy(
              company_facebook_url = csvRow.get(header._1)
            ))
          }
        }

      case "company_founded" =>
        if (linkedinExperienceDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinExperienceDataUpdated = linkedinExperienceDataMapResult(no.toInt).copy(
              company_founded = csvRow.get(header._1)
            )
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataDefault.copy(
              company_founded = csvRow.get(header._1)
            ))
          }
        }

      case "company_id" =>
        if (linkedinExperienceDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinExperienceDataUpdated = linkedinExperienceDataMapResult(no.toInt).copy(
              company_id = csvRow.get(header._1)
            )
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataDefault.copy(
              company_id = csvRow.get(header._1)
            ))
          }
        }

      case "company_industry" =>
        if (linkedinExperienceDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinExperienceDataUpdated = linkedinExperienceDataMapResult(no.toInt).copy(
              company_industry = csvRow.get(header._1)
            )
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataDefault.copy(
              company_industry = csvRow.get(header._1)
            ))
          }
        }

      case "company_linkedin_id" =>
        if (linkedinExperienceDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinExperienceDataUpdated = linkedinExperienceDataMapResult(no.toInt).copy(
              company_linkedin_id = csvRow.get(header._1)
            )
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataDefault.copy(
              company_linkedin_id = csvRow.get(header._1)
            ))
          }
        }

      case "company_linkedin_url" =>
        if (linkedinExperienceDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinExperienceDataUpdated = linkedinExperienceDataMapResult(no.toInt).copy(
              company_linkedin_url = csvRow.get(header._1)
            )
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataDefault.copy(
              company_linkedin_url = csvRow.get(header._1)
            ))
          }
        }

      case "company_location" =>
        if (linkedinExperienceDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinExperienceDataUpdated = linkedinExperienceDataMapResult(no.toInt).copy(
              company_location = csvRow.get(header._1)
            )
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataDefault.copy(
              company_location = csvRow.get(header._1)
            ))
          }
        }

      case "company_location_address_line_2" =>
        if (linkedinExperienceDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinExperienceDataUpdated = linkedinExperienceDataMapResult(no.toInt).copy(
              company_location_address_line_2 = csvRow.get(header._1)
            )
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataDefault.copy(
              company_location_address_line_2 = csvRow.get(header._1)
            ))
          }
        }

      case "company_location_continent" =>
        if (linkedinExperienceDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinExperienceDataUpdated = linkedinExperienceDataMapResult(no.toInt).copy(
              company_location_continent = csvRow.get(header._1)
            )
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataDefault.copy(
              company_location_continent = csvRow.get(header._1)
            ))
          }
        }

      case "company_location_country" =>
        if (linkedinExperienceDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinExperienceDataUpdated = linkedinExperienceDataMapResult(no.toInt).copy(
              company_location_country = csvRow.get(header._1)
            )
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataDefault.copy(
              company_location_country = csvRow.get(header._1)
            ))
          }
        }

      case "company_location_geo" =>
        if (linkedinExperienceDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinExperienceDataUpdated = linkedinExperienceDataMapResult(no.toInt).copy(
              company_location_geo = csvRow.get(header._1)
            )
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataDefault.copy(
              company_location_geo = csvRow.get(header._1)
            ))
          }
        }

      case "company_location_locality" =>
        if (linkedinExperienceDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinExperienceDataUpdated = linkedinExperienceDataMapResult(no.toInt).copy(
              company_location_locality = csvRow.get(header._1)
            )
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataDefault.copy(
              company_location_locality = csvRow.get(header._1)
            ))
          }
        }
      case "company_location_metro" =>
        if (linkedinExperienceDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinExperienceDataUpdated = linkedinExperienceDataMapResult(no.toInt).copy(
              company_location_metro = csvRow.get(header._1)
            )
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataDefault.copy(
              company_location_metro = csvRow.get(header._1)
            ))
          }
        }
      case "company_location_name" =>
        if (linkedinExperienceDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinExperienceDataUpdated = linkedinExperienceDataMapResult(no.toInt).copy(
              company_location_name = csvRow.get(header._1)
            )
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataDefault.copy(
              company_location_name = csvRow.get(header._1)
            ))
          }
        }
      case "company_location_postal_code" =>
        if (linkedinExperienceDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinExperienceDataUpdated = linkedinExperienceDataMapResult(no.toInt).copy(
              company_location_postal_code = csvRow.get(header._1)
            )
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataDefault.copy(
              company_location_postal_code = csvRow.get(header._1)
            ))
          }
        }

      case "company_location_region" =>
        if (linkedinExperienceDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinExperienceDataUpdated = linkedinExperienceDataMapResult(no.toInt).copy(
              company_location_region = csvRow.get(header._1)
            )
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataDefault.copy(
              company_location_region = csvRow.get(header._1)
            ))
          }
        }

      case "company_location_street_address" =>
        if (linkedinExperienceDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinExperienceDataUpdated = linkedinExperienceDataMapResult(no.toInt).copy(
              company_location_street_address = csvRow.get(header._1)
            )
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataDefault.copy(
              company_location_street_address = csvRow.get(header._1)
            ))
          }
        }

      case "company_name" =>
        if (linkedinExperienceDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinExperienceDataUpdated = linkedinExperienceDataMapResult(no.toInt).copy(
              company_name = csvRow.get(header._1)
            )
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataDefault.copy(
              company_name = csvRow.get(header._1)
            ))
          }
        }

      case "company_size" =>
        if (linkedinExperienceDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinExperienceDataUpdated = linkedinExperienceDataMapResult(no.toInt).copy(
              company_size = csvRow.get(header._1)
            )
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataDefault.copy(
              company_size = csvRow.get(header._1)
            ))
          }
        }

      case "company_twitter_url" =>
        if (linkedinExperienceDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinExperienceDataUpdated = linkedinExperienceDataMapResult(no.toInt).copy(
              company_twitter_url = csvRow.get(header._1)
            )
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataDefault.copy(
              company_twitter_url = csvRow.get(header._1)
            ))
          }
        }

      case "company_website" =>
        if (linkedinExperienceDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinExperienceDataUpdated = linkedinExperienceDataMapResult(no.toInt).copy(
              company_website = csvRow.get(header._1)
            )
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataDefault.copy(
              company_website = csvRow.get(header._1)
            ))
          }
        }

      case "end_date" =>
        if (linkedinExperienceDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinExperienceDataUpdated = linkedinExperienceDataMapResult(no.toInt).copy(
              end_date = csvRow.get(header._1)
            )
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataDefault.copy(
              end_date = csvRow.get(header._1)
            ))
          }
        }

      case "is_primary" =>
        if (linkedinExperienceDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinExperienceDataUpdated = linkedinExperienceDataMapResult(no.toInt).copy(
              is_primary = csvRow.get(header._1)
            )
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataDefault.copy(
              is_primary = csvRow.get(header._1)
            ))
          }
        }

      case "location_names" =>
        if (linkedinExperienceDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinExperienceDataUpdated = linkedinExperienceDataMapResult(no.toInt).copy(
              location_names = csvRow.get(header._1)
            )
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataDefault.copy(
              location_names = csvRow.get(header._1)
            ))
          }
        }

      case "location_names_0" =>
        if (linkedinExperienceDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinExperienceDataUpdated = linkedinExperienceDataMapResult(no.toInt).copy(
              location_names_0 = csvRow.get(header._1)
            )
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataDefault.copy(
              location_names_0 = csvRow.get(header._1)
            ))
          }
        }

      case "location_names_1" =>
        if (linkedinExperienceDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinExperienceDataUpdated = linkedinExperienceDataMapResult(no.toInt).copy(
              location_names_1 = csvRow.get(header._1)
            )
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataDefault.copy(
              location_names_1 = csvRow.get(header._1)
            ))
          }
        }

      case "location_names_2" =>
        if (linkedinExperienceDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinExperienceDataUpdated = linkedinExperienceDataMapResult(no.toInt).copy(
              location_names_2 = csvRow.get(header._1)
            )
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataDefault.copy(
              location_names_2 = csvRow.get(header._1)
            ))
          }
        }

      case "location_names_3" =>
        if (linkedinExperienceDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinExperienceDataUpdated = linkedinExperienceDataMapResult(no.toInt).copy(
              location_names_3 = csvRow.get(header._1)
            )
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataDefault.copy(
              location_names_3 = csvRow.get(header._1)
            ))
          }
        }

      case "start_date" =>
        if (linkedinExperienceDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinExperienceDataUpdated = linkedinExperienceDataMapResult(no.toInt).copy(
              start_date = csvRow.get(header._1)
            )
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataDefault.copy(
              start_date = csvRow.get(header._1)
            ))
          }
        }

      case "summary" =>
        if (linkedinExperienceDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinExperienceDataUpdated = linkedinExperienceDataMapResult(no.toInt).copy(
              summary = csvRow.get(header._1)
            )
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataDefault.copy(
              summary = csvRow.get(header._1)
            ))
          }
        }

      case "title_levels" =>
        if (linkedinExperienceDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinExperienceDataUpdated = linkedinExperienceDataMapResult(no.toInt).copy(
              title_levels = csvRow.get(header._1)
            )
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataDefault.copy(
              title_levels = csvRow.get(header._1)
            ))
          }
        }

      case "title_levels_0" =>
        if (linkedinExperienceDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinExperienceDataUpdated = linkedinExperienceDataMapResult(no.toInt).copy(
              title_levels_0 = csvRow.get(header._1)
            )
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataDefault.copy(
              title_levels_0 = csvRow.get(header._1)
            ))
          }
        }

      case "title_levels_1" =>
        if (linkedinExperienceDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinExperienceDataUpdated = linkedinExperienceDataMapResult(no.toInt).copy(
              title_levels_1 = csvRow.get(header._1)
            )
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataDefault.copy(
              title_levels_1 = csvRow.get(header._1)
            ))
          }
        }

      case "title_levels_2" =>
        if (linkedinExperienceDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinExperienceDataUpdated = linkedinExperienceDataMapResult(no.toInt).copy(
              title_levels_2 = csvRow.get(header._1)
            )
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataDefault.copy(
              title_levels_2 = csvRow.get(header._1)
            ))
          }
        }

      case "title_name" =>
        if (linkedinExperienceDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinExperienceDataUpdated = linkedinExperienceDataMapResult(no.toInt).copy(
              title_name = csvRow.get(header._1)
            )
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataDefault.copy(
              title_name = csvRow.get(header._1)
            ))
          }
        }

      case "title_role" =>
        if (linkedinExperienceDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinExperienceDataUpdated = linkedinExperienceDataMapResult(no.toInt).copy(
              title_role = csvRow.get(header._1)
            )
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataDefault.copy(
              title_role = csvRow.get(header._1)
            ))
          }
        }

      case "title_sub_role" =>
        if (linkedinExperienceDataMapResult.contains(no.toLong)) {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            val linkedinExperienceDataUpdated = linkedinExperienceDataMapResult(no.toInt).copy(
              title_sub_role = csvRow.get(header._1)
            )
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataUpdated)
          }
        } else {
          if (!(!csvRow.contains(header._1) || csvRow.get(header._1).contains(""))) {
            prospectDataConcatResult = s"${prospectDataConcatResult}_${csvRow.get(header._1)}"
            linkedinExperienceDataMapResult += (no.toLong -> linkedinExperienceDataDefault.copy(
              title_sub_role = csvRow.get(header._1)
            ))
          }
        }
      case _ =>
        println(s"experiance_table ${header._2}")
    }
    (linkedinExperienceDataMapResult,prospectDataConcatResult)
    
  }

}
*/
