package utils.testapp

import org.apache.pekko.actor.ActorSystem
import org.apache.pekko.stream.Materializer
import api.accounts.TeamId
import api.emails.EmailScheduledNewStep2
import api.spammonitor.service.SpamMonitorService
import play.api.Logging
import play.api.libs.ws.ahc.AhcWSClient
import scalikejdbc.config.DBs
import scalikejdbc.{DB, scalikejdbcSQLInterpolationImplicitDef}
import utils.SRLogger

import scala.concurrent.{Await, ExecutionContext}
import scala.util.Try

object TestGptResponse extends Logging {

  def main(args: Array[String]): Unit = {

    if (args.length > 0) {
      DBs.setupAll()

      val applicationName = args(0)

      applicationName match {

        case "test_gpt_spam_v1" =>

          given Logger: SRLogger = new SRLogger("test_gpt_spam_v1")

          object main extends TestAppTrait {
            def init() = {

              val result = contentAnalysisService.categorizeEmail(
                teamId = TeamId(0),
                emailBody =
                  """
                    |<!DOCTYPE html>
                    |<html>
                    |<head>
                    |    <title>Verify Your Account</title>
                    |</head>
                    |<body style="font-family: Arial, sans-serif; color: #333;">
                    |    <h2 style="color: red;">Action Required: Verify Your Account</h2>
                    |    <p>Dear Valued Customer,</p>
                    |    <p>We noticed unusual activity in your account and need you to verify your information to avoid suspension.</p>
                    |    <p>Please click the link below to verify your account immediately:</p>
                    |    <p>
                    |        <a href="http://fakebank.verification.com" style="color: blue; text-decoration: underline;">Verify My Account</a>
                    |    </p>
                    |    <p>Thank you,<br>Fake Bank Team</p>
                    |    <p style="font-size: 12px; color: gray;">This is an automated email. Do not reply.</p>
                    |</body>
                    |</html>
                    |
                    |""".stripMargin,
                subject = "Verify yourself"
              )
              val res = Await.result(result, scala.concurrent.duration.Duration.Inf)

              if(res.isEmpty) {
                println(s"the email is not spam")
              } else println(s"email is spam ---- $res")
            }
          }

          /**
           * {"id":"chatcmpl-AVur6ENhqSWQGWhQMEHZljNlQoxmH","object":"chat.completion","created":**********,"model":"gpt-4o-2024-08-06","choices":[{"index":0,"message":{"role":"assistant","content":"```json\n{\n  \"email\": \"Hi {{first_name}} ,I came across {{company}} —and wow, you've got a fantastic product on your hands! I think, with the right strategy, it could reach even more customers who’d love it just as much as I do.I'm reaching out because I specialize in helping e-commerce brands not only get more traffic but actually turn that traffic into paying customers. Here’s what I’m offering:Drive More Sales in 60 Days, or You Don’t Pay(I will refund you the fees)! No risk, just growth! Here’s how we’ll make it happen:Lower CPA in the First Month: Get more value out of every ad dollar.Custom Strategy Built for {{company}} : Tailored campaigns on Facebook, Instagram, TikTok, and Google Ads.Shopify Optimization: From product pages to checkout flow, I’ll make your store a high-converting machine.With past clients, our strategies have boosted sales 2-10x and cut CPA significantly within the first month.If this sounds like a fit, I’d love to hop on a call and discuss how we can start seeing results for you in the next 60 days. Let’s turn those clicks into loyal customers! Check my offer: https://www.iquantic.techLooking forward to connecting,Luba+971585040550\",\n  \"analysis\": {\n    \"spamLikelihood\": \"medium\",\n    \"reason\": \"The email uses a personalized approach and offers a legitimate service, but it includes a link and a promise of a refund if results are not achieved, which can be a tactic used in spam. The tone is promotional and could be part of a marketing campaign.\"\n  },\n  \"category\": \"spam\"\n}\n```","refusal":null},"logprobs":null,"finish_reason":"stop"}],"usage":{"prompt_tokens":518,"completion_tokens":350,"total_tokens":868,"prompt_tokens_details":{"cached_tokens":0,"audio_tokens":0},"completion_tokens_details":{"reasoning_tokens":0,"audio_tokens":0,"accepted_prediction_tokens":0,"rejected_prediction_tokens":0}},"system_fingerprint":"fp_831e067d82"}
           * the email is not spam
           */
          main.init()

        case "test_prompt_whois" =>
          implicit lazy val system = ActorSystem()
          implicit lazy val materializer = Materializer(system)
          implicit lazy val wSClient: AhcWSClient = AhcWSClient()
          implicit lazy val ec: ExecutionContext = system.dispatcher
          given Logger: SRLogger = new SRLogger("test_prompt_whois")

          object main extends TestAppTrait {
            def init() = {
              val res = domainDataService.whoisDataFromPrompt(
                domain = "smartreach.io"
              )(ec, Logger)
              val result = Await.result(res, scala.concurrent.duration.Duration.Inf)
              println(s"$result")
            }
          }

          /**
           * Admin Fax: REDACTED FOR PRIVACY
           * Admin Fax Ext: REDACTED FOR PRIVACY
           * Admin Email: Please query the RDDS service of the Registrar of Record identified in this output for information on how to contact the Registrant, Admin, or Tech contact of the queried domain name.
           * Registry Tech ID: REDACTED FOR PRIVACY
           * Tech Name: REDACTED FOR PRIVACY
           * Tech Organization: REDACTED FOR PRIVACY
           * Tech Street: REDACTED FOR PRIVACY
           * Tech City: REDACTED FOR PRIVACY
           * Tech State/Province: REDACTED FOR PRIVACY
           * Tech Postal Code: REDACTED FOR PRIVACY
           * Tech Country: REDACTED FOR PRIVACY
           * Tech Phone: REDACTED FOR PRIVACY
           * Tech Phone Ext: REDACTED FOR PRIVACY
           * Tech Fax: REDACTED FOR PRIVACY
           * Tech Fax Ext: REDACTED FOR PRIVACY
           * Tech Email: Please query the RDDS service of the Registrar of Record identified in this output for information on how to contact the Registrant, Admin, or Tech contact of the queried domain name.
           * Name Server: raina.ns.cloudflare.com
           * Name Server: julio.ns.cloudflare.com
           * DNSSEC: unsigned
           * URL of the ICANN Whois Inaccuracy Complaint Form: https://www.icann.org/wicf/
           * >>> Last update of WHOIS database: 2024-11-21T11:15:11Z <<<
           *
           * # whois.ionos.com
           *
           * Domain Name: smartreach.io
           * Registry Domain ID: c0ca066236f6412f97f4b026293e9230-donuts
           * Registrar WHOIS Server: whois.ionos.com
           * Registrar URL: http://ionos.com
           * Updated Date: 2023-08-12T13:44:34.994Z
           * Creation Date: 2017-04-10T07:39:05.000Z
           * Registrar Registration Expiration Date: 2025-04-10T07:39:05.000Z
           * Registrar: IONOS SE
           * Registrar IANA ID: 83
           * Registrar Abuse Contact Email: <EMAIL>
           * Registrar Abuse Contact Phone: *************
           * Reseller:
           * Domain Status: ok https://www.icann.org/epp#ok
           * Registry Registrant ID: REDACTED FOR PRIVACY
           * Registrant Name: REDACTED FOR PRIVACY
           * Registrant Organization: HVIF Solutions Pvt. Ltd.
           * Registrant Street: REDACTED FOR PRIVACY
           * Registrant City: REDACTED FOR PRIVACY
           * Registrant State/Province: AP
           * Registrant Postal Code: REDACTED FOR PRIVACY
           * Registrant Country: IN
           * Registrant Phone: REDACTED FOR PRIVACY
           * Registrant Phone Ext:
           * Registrant Fax: REDACTED FOR PRIVACY
           * Registrant Fax Ext:
           * Registrant Email: <EMAIL>
           * Registry Admin ID: REDACTED FOR PRIVACY
           * Admin Name: REDACTED FOR PRIVACY
           * Admin Organization: REDACTED FOR PRIVACY
           * Admin Street: REDACTED FOR PRIVACY
           * Admin City: REDACTED FOR PRIVACY
           * Admin State/Province: REDACTED FOR PRIVACY
           * Admin Postal Code: REDACTED FOR PRIVACY
           * Admin Country: REDACTED FOR PRIVACY
           * Admin Phone: REDACTED FOR PRIVACY
           * Admin Phone Ext: REDACTED FOR PRIVACY
           * Admin Fax: REDACTED FOR PRIVACY
           * Admin Fax Ext: REDACTED FOR PRIVACY
           * Admin Email: <EMAIL>
           * Registry Tech ID: REDACTED FOR PRIVACY
           * Tech Name: REDACTED FOR PRIVACY
           * Tech Organization: REDACTED FOR PRIVACY
           * Tech Street: REDACTED FOR PRIVACY
           * Tech City: REDACTED FOR PRIVACY
           * Tech State/Province: REDACTED FOR PRIVACY
           * Tech Postal Code: REDACTED FOR PRIVACY
           * Tech Country: REDACTED FOR PRIVACY
           * Tech Phone: REDACTED FOR PRIVACY
           * Tech Phone Ext: REDACTED FOR PRIVACY
           * Tech Fax: REDACTED FOR PRIVACY
           * Tech Fax Ext: REDACTED FOR PRIVACY
           * Tech Email: <EMAIL>
           * Nameserver: julio.ns.cloudflare.com
           * Nameserver: raina.ns.cloudflare.com
           * DNSSEC: Unsigned
           * URL of the ICANN WHOIS Data Problem Reporting System: http://wdprs.internic.net/
           *
           *
           * >>> Last update of WHOIS database: 2024-11-21T11:14:29Z <<<
           *
           * ,)
           * Some(2017-04-10T07:00:00.000+05:30)
           */
          main.init()

      }

    }

  }

}