/* 17-jun-2025: [DO_NOT_REMOVE] [LEADFINDERUPLOAD] was slowing down compilation. Only needed for the Lead database upload.

package utils.testapp

import play.api.Logging
import play.api.libs.json.{JsObject, JsValue, <PERSON>son}
import scalikejdbc.config.DBs
import utils.SRLogger
import utils.testapp.csv_upload.{ColumnMap4_1, ColumnMap4_2, CsvQueueCreateFormData}

import scala.util.{Failure, Random, Success, Try}



//case class CsvDetailsForUploadIntoCsvQueue(
//                                            file_url: String,
//                                            column_map: JsValue
//                                          )

object CsvUploadTestAppZ extends Logging with TestAppTrait {

  def main(args: Array[String]): Unit = {

    if (args.length > 0) {
      DBs.setupAll()

      val applicationName = args(0)

      applicationName match {
        case "upload_csv_details_lead_finder_queue" =>

          given logger: SRLogger = new SRLogger("test_upload_csv_details")

          object main {

            def init(
                      csvDetailsForUploadIntoCsvQueue: Seq[CsvDetailsForUploadIntoCsvQueue]
                    )(using logger: SRLogger)= Try{

              val csvQueueCreateFormData = csvDetailsForUploadIntoCsvQueue.map(data =>
                CsvQueueCreateFormData(
                  list_name = Some(Random.alphanumeric.take(5).mkString("")),
                  file_url = data.file_url,
                  column_map = data.column_map,
                  dt_src = "zi"
                )
              )
              val res1 = leadFinderUploadService.uploadCsvDetails(
                accountId = 6939,
                teamId = 6412,
                ta_id = 20165,
                loggedIn_id = 6939,
                data = csvQueueCreateFormData
              )

              assert(res1.isSuccess);
            }

          }

          val column_map_z: JsObject = Json.obj(
            "" -> "",
            "ANZSIC 2006 Code" -> "anzsic_2006_code",
            "ANZSIC 2006 Description" -> "anzsic_2006_description",
            "Address" -> "address",
            "Address 1" -> "address_1",
            "Address 2" -> "address_2",
            "Address Line 1" -> "address_line_1",
            "Address Line 2" -> "address_line_2",
            "Address Line 3" -> "address_line_3",
            "App Using" -> "app_using",
            "Assets (USD)" -> "assets__usd_",
            "Business Description" -> "business_description",
            "City" -> "city",
            "Company" -> "company",
            "Company City" -> "company_city",
            "Company Country" -> "company_country",
            "Company Name" -> "company_name",
            "Company State" -> "company_state",
            "Company Street address" -> "company_street_address",
            "Company ZIP/Postal code" -> "company_zip_postal_code",
            "Company division name" -> "company_division_name",
            "Company domain name" -> "company_domain_name",
            "Company phone number" -> "company_phone_number",
            "Contact" -> "contact",
            "Contact Level" -> "contact_level",
            "Contact Name" -> "contact_name",
            "Contact Title" -> "contact_title",
            "Country" -> "country",
            "Country/Region" -> "country_region",
            "County" -> "county",
            "D&B Hoovers Industry" -> "company_industry",
            "Direct Marketing Status" -> "direct_marketing_status",
            "Direct Phone" -> "direct_phone",
            "Direct Phone Number" -> "direct_phone_number",
            "Email" -> "person_business_email",
            "Email Domain" -> "email_domain",
            "Email address" -> "email_address",
            "Employees" -> "employees",
            "Employees (All Sites)" -> "employees__all_sites_", // use this for employee size
            "Employees (Single Site)" -> "employees__single_site_",
            "Employees Range" -> "employees_range",
            "Encrypted Email Address" -> "encrypted_email_address",
            "Entity Type" -> "entity_type",
            "Fax" -> "fax",
            "First Name" -> "person_first_name",
            "Highest Level Job Function" -> "highest_level_job_function",
            "ISIC Rev 4 Code" -> "isic_rev_4_code",
            "ISIC Rev 4 Description" -> "isic_rev_4_description",
            "Industry" -> "industry",
            "Industry Type" -> "industry_type",
            "Industry hierarchical category" -> "industry_hierarchical_category",
            "Industry label" -> "industry_label",
            "Job Function" -> "job_function",
            "Job title" -> "job_title",
            "Job title hierarchy level" -> "job_title_hierarchy_level",
            "Last Name" -> "person_last_name",
            "Liabilities (USD)" -> "liabilities__usd_",
            "Linkedin Links" -> "linkedin_links",
            "Management Level" -> "management_level",
            "Middle Name" -> "middle_name",
            "NACE Rev 2 Code" -> "nace_rev_2_code",
            "NACE Rev 2 Description" -> "nace_rev_2_description",
            "NAICS" -> "naics",
            "NAICS 2012 Code" -> "naics_2012_code",
            "NAICS 2012 Description" -> "naics_2012_description",
            "NAICS1" -> "naics1",
            "NAICS2" -> "naics2",
            "Ownership Type" -> "company_type",
            "Parent Company" -> "parent_company",
            "Parent Country/Region" -> "parent_country_region", // part of person_location
            "Person City" -> "person_city", // part of person_location
            "Person Pro URL" -> "person_pro_url",
            "Person State" -> "person_state", // part of person_location
            "Person Zip" -> "person_zip",
            "Phone" -> "person_phone",
            "Postal Code" -> "postal_code",
            "Pre Tax Profit (USD)" -> "pre_tax_profit__usd_",
            "Query Name" -> "query_name",
            "Revenue" -> "revenue",
            "Revenue (USD)" -> "revenue__usd_",
            "Revenue (in 000s)" -> "revenue__in_000s_",
            "Revenue Range" -> "revenue_range",
            "SIC" -> "sic",
            "SIC Code" -> "sic_code",
            "SIC1" -> "sic1",
            "SIC2" -> "sic2",
            "Sales" -> "sales",
            "Salutation" -> "salutation",
            "Secondary industry hierarchical category" -> "secondary_industry_hierarchical_category",
            "Secondary industry label" -> "secondary_industry_label",
            "Source count" -> "source_count",
            "State" -> "state",
            "State Or Province" -> "state_or_province",
            "Suffix" -> "suffix",
            "Ticker" -> "ticker",
            "Title" -> "person_job_title",
            "TitleCode" -> "titlecode",
            "Total Employees" -> "total_employees",
            "Tradestyle" -> "tradestyle",
            "UK SIC 2007 Code" -> "uk_sic_2007_code",
            "UK SIC 2007 Description" -> "uk_sic_2007_description",
            "URL" -> "url",
            "US SIC 1987 Code" -> "us_sic_1987_code",
            "US SIC 1987 Description" -> "us_sic_1987_description",
            "Ultimate Parent Company" -> "ultimate_parent_company",
            "Ultimate Parent Country/Region" -> "ultimate_parent_country_region",
            "Website" -> "website",
            "Zip" -> "zip",
            "Zoom Individual ID" -> "zoom_individual_id",
            "Zoom company ID" -> "zoom_company_id",
            "Zoominfo Industry" -> "zoominfo_industry",
          )



          val file_url = args(1)
          println(s"Received file url as ${file_url}")
          val csvDetailsForUploadIntoCsvQueue = Seq(
            CsvDetailsForUploadIntoCsvQueue(
              // Use This url for the file on internet
              // file_url = "https://storage.googleapis.com/ld-zip/nxd/flattened/01_29_sample_prv_sws.csv",
              // Use this url for local file
              // file_url = "/Users/<USER>/Desktop/zi_test_12.csv",

              file_url = file_url,
              column_map = column_map_z
            )
          )

          leadFinderUploadDao.findAll() match {
            case Failure(err)=>
              logger.error("Error occurred while finding records from csv ")
            case Success(data)=>
              val filtered_list = csvDetailsForUploadIntoCsvQueue.filter(p => !data.contains(p.file_url))
              println(filtered_list)
              main.init(csvDetailsForUploadIntoCsvQueue = filtered_list) match {
                case Success(_) => logger.info("success")
                case Failure(exception) => logger.error("error", err = exception)
              }
          }

        case "leadFinderCsvUploadExecuteCron" =>
          given srLogger: SRLogger = new SRLogger("::::testApp lea finder upload csv executeCron::::")

          object CSU
            extends TestAppTrait {

            def mainFn() = {

              uploadLeadFinderCsvCronService.executeCron()

            }

          }

          val result = CSU
            .mainFn()

      }

    }
  }

}
*/
