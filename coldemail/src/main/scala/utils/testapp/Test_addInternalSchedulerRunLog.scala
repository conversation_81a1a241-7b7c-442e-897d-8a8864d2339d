package utils.testapp

import api.accounts.TeamId
import api.campaigns.models.{InternalSchedulerRunLog, InternalSchedulerRunLogData}
import org.joda.time.DateTime
import scalikejdbc.config.DBs
import utils.mq.channel_scheduler.channels.model.SchedulerSteps
import utils.srlogging.play.Logging

import scala.util.{Failure, Success}

object Test_addInternalSchedulerRunLog extends Logging {

  def main(args: Array[String]): Unit = {

    if (args.length > 0) {
      DBs.setupAll()

      val applicationName = args(0)

      applicationName match {

        case "test_addInternalSchedulerRunLog" =>

          logger.info("BEFORE test")

          object Test extends TestAppTrait {

            def init() = {

              val team_id: TeamId = TeamId(id = 15)
              val internalSchedulerRunLog = InternalSchedulerRunLog(
                team_id =  team_id,
                internal_scheduler_run_log_data =  InternalSchedulerRunLogData.WhatsappChannelSchedulerLog(
                  whatsapp_setting_uuid = "whatsapp_account_2MoqpjVZehFuLi3gyuCdgx8n3BM"
                ),

                started_at = DateTime.now(),
                completed_at = DateTime.now().plusMinutes(2),
                saved_tasks_count = 2,
                reached_scheduler_step = SchedulerSteps.PostSchedulingSuccess,
                log_trace_id = "Test_log_trace_id"
              )
              internalSchedulerRunLogDAO.addInternalSchedulerRunLog(
                internalSchedulerRunLog = internalSchedulerRunLog
              ) match {
                case Failure(exception) =>
                  logger.info(s"Failure  $exception")
                case Success(value) =>
                  logger.info(s"Success $value")
              }
            }
          }
          Test.init()
          logger.info("AFTER test")
      }
    }
  }

}
