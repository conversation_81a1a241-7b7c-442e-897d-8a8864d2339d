package utils.testapp

import api.accounts.AccountDAO
import api.accounts.models.{AccountId, OrgId}
import play.api.Logging
import scalikejdbc.config.DBs
import utils.TestApp.logger
import utils.helpers.LogHelpers
import utils.testapp.TestAppTrait
import utils.uuid.SrUuidUtils

import scala.util.{Failure, Success}

/*
This object holds any sort internal test function used to test features/changes manually during development.
 */
object TestAppAccount extends Logging {

  def main(args: Array[String]): Unit = {

    if (args.length > 0) {
      DBs.setupAll()


      val applicationName = args(0)

      applicationName match {


        case "accountDAOTransaction" =>
        //          val organizationDAO = new OrganizationDAO()
        //          val accountDAO = new AccountDAOV2(organizationDAO)
        //          val googleAdsDAO = new GoogleAdsDAO
        //          val emailSendingStatusDAO = new EmailSendingStatusDAO
        //          val srInternalFeatureUsageDAO = new SrInternalFeatureUsageDAO
        //          val srUserFeatureUsageEventService = new SrUserFeatureUsageEventService(srInternalFeatureUsageDAO = srInternalFeatureUsageDAO)
        //          val emailSendingStatusService = new EmailSendingStatusService(emailSendingStatusDAO, srUserFeatureUsageEventService = srUserFeatureUsageEventService, organizationDAO = organizationDAO)
        //          val csvQueueDAO = new CsvQueueDAO
        //          val csvQueueService = new CsvQueueService(csvQueueDAO)
        //          val freeEmailDomainListService = new FreeEmailDomainListService(
        //            freeEmailDomainListDAO = new FreeEmailDomainListDAO, cacheServiceJedis = new CacheServiceJedis
        //          )
        //          val newAuthFlowService = new NewAuthFlowService(
        //          freeEmailDomainListService = freeEmailDomainListService,
        //          accountDAO = new AccountDAO,
        //          newAuthGoogleSignupService = new NewAuthGoogleSignupService,
        //          newAuthMicrosoftSignupService = new NewAuthMicrosoftSignupService,
        //          spamMonitorService: SpamMonitorService,
        //          accountService: AccountService,
        //          implicit val ec: ExecutionContext
        //      )
        //          val supportService = new InternalSupportServices(
        //            organizationDAO, googleAdsDAO,
        //            emailSendingStatusService, csvQueueService,
        //
        //          )
        //          val transferOwnerData = TransferOwnerData(
        //            oldEmail = "<EMAIL>",
        //            newEmail = "<EMAIL>",
        //            orgName = "Test Company"
        //          )
        //          supportService.transferOwnership(transferOwnerData) match {
        //            case Right(b) => logger.info(">>>>>>>>>>>>>>>>>>accountDAOTransaction _____" + b)
        //            case Left(a) => logger.info(">>>>>>>>>>>>>>>>>>accountDAOTransaction _____" + a)
        //          }


        /*

                case "NewAccountAddingTest" =>

                  val logger = new SRLogger("NewAccountAddingTest")
                  val accountCreateForm = AccountCreateForm(
                    first_name = "a",
                    last_name = "b",
                    company = "smartreach",
                    email = "<EMAIL>",
                    password = None,
                    invite_code = None,
                    timezone = None, country_code = "IN",
                    rs_code_used = None,
                    g_response = None
                  )
                  val cacheServiceJedis = new CacheServiceJedis
                  val clientAccountAccessLogDAO = new ClientAccountAccessLogDAO
                  val accessTokenService = new AccessTokenService(
                    cacheServiceJedis = cacheServiceJedis,
                    clientAccountAccessLogDAO = clientAccountAccessLogDAO
                  )
                  val userRedisKeyService = new UserRedisKeyService(cacheServiceJedis: CacheServiceJedis)

                  val teamDAO = new TeamsDAO
                  val organizationDAO = new OrganizationDAO
                  val srUuidUtils = new SrUuidUtils
                  val accountDAO = new AccountDAO(
                    srUuidUtils = srUuidUtils
                  )
                  val dbUtils = new DBUtils
                  val emailSendingStatusDAO = new EmailSendingStatusDAO
                  val supportAccessToUserAccountDAO = new SupportAccessToUserAccountDAO
                  val freeEmailDomainListDAO = new FreeEmailDomainListDAO

                  val scyllaDbConnection = new ScyllaDbConnection
                  val scyllaCluster = scyllaDbConnection.initialize()
                  val scyllaRunSafely = new ScyllaRunSafely(
                    scyllaCluster = scyllaCluster
                  )

                  val prospectColumnDef = new ProspectColumnDef(new ProspectColumnDefDAO)
                  val prospectQuery = new ProspectQuery(
                    prospectColumnDef = prospectColumnDef
                  )
                  val prospectAddEventDAO = new ProspectAddEventDAO
                  val campaignProspectDAO_2 = new CampaignProspectDAO_2
                  val prospectDAO = new ProspectDAO
                  val mergeTagService = new MergeTagService(campaignProspectDAO_2)
                  val replySentimentDAO = new ReplySentimentDAO
                  val replySentimentJedisDAO = new ReplySentimentJedisDAO(
                    cacheServiceJedis = cacheServiceJedis
                  )
                  val replySentimentDAOService = new ReplySentimentDAOService(
                    replySentimentDAO = replySentimentDAO,
                    replySentimentJedisDAO = replySentimentJedisDAO
                  )
                  val emailThreadDAO = new EmailThreadDAO

                  val dbCounterDAO = new DBCounterDAO(cacheServiceJedis)
                  val srDBQueryCounterService = new SrDBQueryCounterService(dbCounterDAO = dbCounterDAO)

                  val taskPgDAO = new TaskPgDAO(
                    srDBQueryCounterService = srDBQueryCounterService
                  )

                  val linkedinTaskService = new LinkedinTaskService(
                    taskPgDAO = taskPgDAO
                  )

                  val prospectDAOService = new ProspectDAOService(
                    prospectDAO = prospectDAO,
                    prospectQuery = prospectQuery,
                    linkedinTaskService = linkedinTaskService,
                    prospectColumnDef = prospectColumnDef,
                    mergeTagService = mergeTagService,
                    replySentimentDAOService = replySentimentDAOService,
                    emailThreadDAO = emailThreadDAO,
                    dbUtils = dbUtils
                  )
                  val campaignDAO = new CampaignDAO(
                    srDBQueryCounterService = srDBQueryCounterService,
                    srUuidUtils = srUuidUtils
                  )


                  val emailSettingDAO = new EmailSettingDAO()
                  val prospectsEmailsDAO = new ProspectsEmailsDAO

                  val emailValidationModel = new EmailValidationModel

                  val prospectServiceV2 = new ProspectServiceV2(
                    prospectDAOService = prospectDAOService,
                    prospectsEmailsDAO = prospectsEmailsDAO,
                    prospectAddEventDAO = prospectAddEventDAO,
                    prospectColumnDef = prospectColumnDef,
                    mergeTagService = mergeTagService,
                    campaignProspectDAO_2 = campaignProspectDAO_2,
                    emailValidationModel = emailValidationModel
                  )

                  val campaignProspectDAO = new CampaignProspectDAO(
                    prospectDAOService = prospectDAOService,
                    prospectQuery = prospectQuery,
                    srDBQueryCounterService = srDBQueryCounterService,
                    prospectAddEventDAO = prospectAddEventDAO,
                    prospectServiceV2 = prospectServiceV2
                  )
                  val emailScheduledDAO = new EmailScheduledDAO(
                    emailSettingDAO = emailSettingDAO,
                    emailThreadDAO = emailThreadDAO,
        //            accountDAO = accountDAO,
                    prospectAddEventDAO = prospectAddEventDAO,
                    campaignProspectDAO = campaignProspectDAO,
                    prospectServiceV2 = prospectServiceV2,
                    srUuidUtils = srUuidUtils
                  )

                  val kafkaService = new KafkaProducerService


                  val taskScyllaCacheDAO = new TaskScyllaCacheDAO(
                    scyllaRunSafely = scyllaRunSafely
                  )
                  val taskCacheDAO = new TaskCacheDAO(
                    taskScyllaCacheDAO = taskScyllaCacheDAO
                  )
                  val srEventService = new SrEventService(
                    kafkaService = kafkaService
                  )

                  val accountService = new AccountService(
                    dbUtils = dbUtils,
                    teamDAO = teamDAO,
                    accountDAO = accountDAO,
                    cacheServiceJedis = cacheServiceJedis,
                    accessTokenService = accessTokenService,
                    userRedisKeyService = userRedisKeyService,
                    organizationDAO = organizationDAO,
                    emailSendingStatusDAO = emailSendingStatusDAO,
                    supportAccessToUserAccountDAO = supportAccessToUserAccountDAO,
                    replySentimentDAOService = replySentimentDAOService,
                    srUuidUtils = srUuidUtils
                  )
                  val rolePermissionDataDAOV2 = new RolePermissionDataDAOV2()

                  val utmData = UtmData(
                    utm_source = None,
                    utm_medium = None,
                    utm_campaign = None,
                    utm_term = None,
                    utm_content = None,
                    utm_device = None,
                    utm_agid = None,
                    utm_match_type = None,
                    utm_placement = None,
                    utm_network = None
                  )

                  accountService.create(
                    logger = logger,
                    data = accountCreateForm,
                    orgKeyName = "smartreach",

                    signup_type = SignupType.Google,
                    sendEmailStatus = SendEmailStatusData.AllowedData(),
                    admitadCreatedAt = None,
                    utmData = utmData,
                    firstPromoterCreatedAt = None
                  )(rolePermissionDataDAOV2) match {
                    case Failure(err) =>
                      logger.error(s"Error while creating account - $err")
                    case Success(None) =>
                      logger.error("Impossible error account created but not found")
                    case Success(Some(account)) =>
                      logger.info(s"Account created - $account")

                  }
        */



        case "CheckChangeOrgName" =>
          val srUuidUtils = new SrUuidUtils
          val accountDAO = new AccountDAO(
            srUuidUtils = srUuidUtils
          )
          accountDAO.updateProfileDuringOnboardingForOwner(
            accountId = AccountId(id = 7),
            first_name = "P",
            last_name = "B",
            newOrgName = "Test Company",
            org_id = OrgId(id = 2),
            onboarding_phone_number = "+************",
            is_phone_number_valid = true
          ) match {
            case Failure(err) =>
              logger.error(s"Error while change ________________________________$err")
            case Success(None) =>
              logger.error("Impossible error________________________________ ")
            case Success(Some(orgId)) =>
              logger.info(s"DONE DELETING _________________________________ ${orgId.id}")
          }

        /*
      case "deleteOrgTest" =>
        implicit lazy val system: ActorSystem = ActorSystem()
        implicit lazy val materializer = Materializer(system)
        implicit lazy val wSClient: AhcWSClient = AhcWSClient()
        implicit lazy val actorContext: ExecutionContext = system.dispatcher
        object DeleteOrgAnimesh
          extends DeleteOrgSRCron_DI
            with OrganizationDAO_DI
            with TaskDAO_DI
            with AccountOrgBillingRelatedServiceDI
            with AccountOrgBillingRelatedInfoDAODI
            with CampaignSchedulingMetadataDAO_DI
            with LinkedinTaskServiceDI
            with MigrationUtils_DI
            with ProspectQueryDI
            with ProspectAddEventDAO_DI
            with AccountDAOV2_DI
            with ProspectAccountDAO1DI
            with FreeEmailDomainListDAO_DI
            with FreeEmailDomainListService_DI
            with CacheServiceJedisDI
            with ProspectColumnDef_DI
            with ProspectColumnDefDAO_DI
            with ProspectsEmailsDAO_DI
            with ProspectServiceV2_DI
            with EmailMessageContactModel_DI
            with BlacklistProspectCheckDAO_DI
            with CampaignProspectDAO_2DI
            with MergeTagService_DI
            with ProspectDAOService_DI
            with EmailThreadDAO_DI
            with ReplySentimentDAO_DI
            with ReplySentimentJedisDAO_DI
            with ReplySentimentDAOService_DI
            with ProspectDAO_DI
            with RolePermissionDataDAOV2_DI
            with EmailValidationModelDI
            with DBUtils_DI
            with AccountDAO_DI
            with SrUuidUtilsDI
            with MigrationDAO_DI
            with MigrationService_DI
            with CampaignProspectDAO_DI
            with SrDBQueryCounterService_DI
            with DBCounterDAO_DI {
          def init = {
            val logRequestId = "DeleteOrgAnimesh"
            given Logger = new SRLogger(logRequestId)
            deleteOrgSRCron.executeCron(Logger)
          }
          }
        DeleteOrgAnimesh.init

      case "deleteAccountTest" =>
        implicit lazy val system: ActorSystem = ActorSystem()
        implicit lazy val materializer = Materializer(system)
        implicit lazy val wSClient: AhcWSClient = AhcWSClient()
        implicit lazy val actorContext: ExecutionContext = system.dispatcher
        object DeleteAccountAnimesh
          extends DeleteAccountSrCron_DI
            with OrganizationDAO_DI
            with CampaignSchedulingMetadataDAO_DI
            with TaskDAO_DI
            with AccountOrgBillingRelatedServiceDI
            with AccountOrgBillingRelatedInfoDAODI
            with LinkedinTaskServiceDI
            with MigrationUtils_DI
            with ProspectQueryDI
            with ProspectAddEventDAO_DI
            with AccountDAOV2_DI
            with ProspectAccountDAO1DI
            with FreeEmailDomainListDAO_DI
            with FreeEmailDomainListService_DI
            with CacheServiceJedisDI
            with ProspectColumnDef_DI
            with ProspectColumnDefDAO_DI
            with RolePermissionDataDAOV2_DI
            with ProspectsEmailsDAO_DI
            with ProspectServiceV2_DI
            with EmailMessageContactModel_DI
            with BlacklistProspectCheckDAO_DI
            with CampaignProspectDAO_2DI
            with MergeTagService_DI
            with ProspectDAOService_DI
            with EmailThreadDAO_DI
            with ReplySentimentDAO_DI
            with ReplySentimentJedisDAO_DI
            with ReplySentimentDAOService_DI
            with ProspectDAO_DI
            with EmailValidationModelDI
            with DBUtils_DI
            with AccountDAO_DI
            with SrUuidUtilsDI
            with MigrationDAO_DI
            with MigrationService_DI
            with CampaignProspectDAO_DI
            with SrDBQueryCounterService_DI
            with DBCounterDAO_DI {
          def init = {
            val logRequestId = "DeleteOrgAnimesh"
            given Logger = new SRLogger(logRequestId)
            deleteAccountSrCron.executeCron(Logger)
          }
        }
        DeleteAccountAnimesh.init
         */



        case "feedDataToDeleteOldOrgData" =>

          object OrgDataDeleter extends TestAppTrait {

            def init = {

              mqOrgDataDeleter
                .feedDataForDeletingOrgData() match {

                case Failure(e) =>
                  logger.error(s"feedDataForDeletingOrgData: ${LogHelpers.getStackTraceAsString(e)}")

                case Success(_) =>
                  logger.info("feedDataForDeletingOrgData successfully completed")

              }
            }
          }

          OrgDataDeleter.init
          
          
      }
    }

  }
}
