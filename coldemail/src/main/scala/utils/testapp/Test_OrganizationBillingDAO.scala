package utils.testapp

import api.billing.PaymentGateway
import io.sr.billing_common.models.SrPlanLimits
import org.joda.time.DateTime
import scalikejdbc.config.DBs
import utils.SRLogger

import scala.util.{Failure, Success}

object Test_OrganizationBillingDAO extends TestAppTrait {

  def main(args: Array[String]): Unit = {

    if (args.length > 0) {

      DBs.setupAll()

      val applicationName = args(0)

      given logger: SRLogger = new SRLogger(logRequestId = "Test_ProspectEventDAO")

      applicationName match {

        case "addSubscription" =>

          organizationBillingDAO.addSubscription(
            paymentGateway = PaymentGateway.STRIPE,
            pgCustomerId = "some_stripe_cus_id_173",
            pgSubscriptionId = "some_sub_id",
            sr_plan_name = "base-one-monthly-usd-v4-199",
            planLimits = SrPlanLimits(
              base_licence_count = 1,
              additional_licence_count = 0,
              total_sending_email_account = 5,
              max_prospects_contacted = 2000,
              max_client_teams = 4,
              max_prospects_saved = 234561,
              max_crm_integrations = 2,
              max_calling_seats = 3,
              max_purchased_domains = 0,
              max_purchased_email_accounts = 0,
              max_purchased_zapmail_domains = 0,
              max_purchased_zapmail_email_accounts = 0,
              max_automated_linkedin_accounts = 1,
            ),
            defaultLeadFinderCredits = 200,
            maxManualLinkedinAccounts = 45,
            currentCycleStartedAt = DateTime.now(),
            nextBillingDate = DateTime.now().plusMonths(1)
          ) match {

            case Failure(exception) =>

              println(exception)

            case Success(None) =>

              println("Failed to update subscription org not found")

            case Success(Some(organization)) =>

              println(s"Successfully updated subscription - $organization")

          }

      }

    }

  }

}
