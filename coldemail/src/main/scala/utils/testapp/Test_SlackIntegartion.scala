package utils.testapp


import api.accounts.TeamId
import api.accounts.models.AccountId
import api.columns.ColumnDef
import api.prospects.models.ProspectId
import api.sr_audit_logs.models.EventType
import org.apache.pekko.actor.ActorSystem
import org.apache.pekko.stream.Materializer
import org.joda.time.DateTime
import play.api.libs.json.{JsObject, Json}
import play.api.libs.ws.ahc.AhcWSClient
import scalikejdbc.config.DBs
import utils.mq.webhook.MQWebhookCompletedMsg
import utils.{SRLogger, StringUtils}
import api.sr_audit_logs.services.MqHandleEventLogMessage

import scala.concurrent.duration.DurationInt
import scala.concurrent.{Await, ExecutionContext}



object Test_SlackIntegartion extends TestAppTrait {

  def main(args: Array[String]): Unit = {

    if (args.length > 0) {

      DBs.setupAll()

      given Logger: SRLogger = new SRLogger(logRequestId = "Test_SlackIntegration")
      implicit lazy val system = ActorSystem()
      implicit lazy val materializer = Materializer(system)
      implicit lazy val wSClient: AhcWSClient = AhcWSClient()
      implicit lazy val executionContext: ExecutionContext = system.dispatcher

      val applicationName = args(0)

      applicationName match {


        // ex: sbt -J-Xmx2G -J-Xms2G "~ coldemail/runMain utils.testapp.Test_SlackIntegartion send_slack_message_via_webhook"

        case "send_slack_message_via_webhook" =>
          val from_email = "<EMAIL>"

          val notificationText = s"✉️ Email sent \n📧 From: ${from_email} \n📨 To: ${from_email} \n📝 Subject: $from_email \n⏰ Sent at: ${from_email}"

          // "channel" -> "#test-channel", can override this
          val jsonData: Seq[JsObject] = Seq(Json.obj(
            "metadata" -> Json.obj(
              "event_type" -> "activity.email.sent",
              "version" -> "v2",
              "webhook_id" -> 5284,
              "webhook_url" -> "*********************************************************************************",
              "received_at" -> 1733835625041L
            ),
            "username" -> "webhook",
            "webhook_url" -> "*********************************************************************************",

            "text" -> notificationText,
          ), Json.obj(
            "metadata" -> Json.obj(
              "event_type" -> "activity.email.sent",
              "version" -> "v2",
              "webhook_id" -> 5284,
              "webhook_url" -> "*********************************************************************************",
              "received_at" -> 1733835625041L
            ),
            "username" -> "webhook",
            "webhook_url" -> "*********************************************************************************",

            "text" -> "okay2 \nhello22"))

          val res = webhookUtils_V2.__calWebhooks(
            prospectWebookData = jsonData,
            webHookUrl = "*********************************************************************************",
            log = (msg => Logger.info(s"callNewRepliesHooks: hook: :: msg: $msg "))

          )(wSClient, executionContext)


          val result = Await.result(res, 20.seconds)

          println(result)

        // ex: sbt -J-Xmx2G -J-Xms2G "~ coldemail/runMain utils.testapp.Test_SlackIntegartion call_update_category_webhooks"

//        case "call_update_category_webhooks" =>
//
//          val res = webhookUtils.callProspectCategoryUpdateHooks(
//            accountId = AccountId(2),
//            teamId = TeamId(2),
//            oldCategory = "oldCategory",
//            newCategory = "newCategory",
//            prospectId = ProspectId(3913450),
//            Logger = Logger
//          )(executionContext, wsClient)
//
//          val result = Await.result(res, 60.seconds)
//
//          println(result)


        // ex: sbt -J-Xmx2G -J-Xms2G "~ coldemail/runMain utils.testapp.Test_SlackIntegartion call_custom_category_webhooks"

        case "call_custom_category_webhooks" =>
          val res = mqWebhookCompleted.processMessage(
            MQWebhookCompletedMsg(
              accountId = 2,
              teamId = 2,
              campaignId= 31,
              campaignName = Some("Some Campaign"),
              prospectIds= Seq(
                3913450
              )
            )
          )(wSClient,executionContext,system)

          val result = Await.result(res, 60.seconds)

          println(result)

        // ex: sbt -J-Xmx2G -J-Xms2G "~ coldemail/runMain utils.testapp.Test_SlackIntegartion create_event_log"

        case "create_event_log" =>
          val res = mqHandleEventLogConsumer.processMessage(
            message = MqHandleEventLogMessage(
              team_id = 2,
              event_log_id = "evt_1685964501715_2_2_22QYSt3Izt"
            )
          )(wSClient,executionContext,system)

          val result = Await.result(res, 60.seconds)

          println(result)

        case _ =>
          println("Unknown command")
      }
    }
  }
}