package utils.testapp

import api.accounts.TeamId
import api.accounts.models.{AccountId, OrgId}
import api.captain_data.{Captain<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CaptainDataUserCreateRequest, CaptainDataUserUID}
import api.linkedin.models.CaptainDataWorkflowName.EXTRACT_LINKEDIN_CONNECTIONS
import api.linkedin.models.{CreateOrUpdateLinkedinAccountSettings,CDJobUid, CaptainDataWebhookResponse, CaptainDataWorkflowName, CaptainDataWorkflowStatus, LinkedinSettingId, LinkedinSettingUuid}
import api.phantombuster.{LinkedinMessageThreadId, LinkedinMessageThreadUrl, PhantomBusterApi, PhantomBusterApiKey, PhantomBusterContainerId, PhantomBusterService}
import api.tasks.models.{CommonLinkedinTaskDetails, TaskType, TasksGroupedByTypeAndLinkedinSetting}
import org.apache.pekko.actor.ActorSystem
import org.apache.pekko.stream.Materializer
import play.api.Logging
import play.api.libs.ws.WSClient
import play.api.libs.ws.ahc.AhcWSClient
import scalikejdbc.config.DBs
import scalikejdbc.{DB, scalikejdbcSQLInterpolationImplicitDef}
import utils.SRLogger
import utils.TestApp.logger
import utils.mq.channel_scheduler.MqLinkedinChannelSchedulerMsg
import utils.timezones.TimezoneUtils

import scala.concurrent.{Await, ExecutionContext, Future}
import scala.util.{Failure, Success}

/*
This object holds any sort internal test function used to test features/changes manually during development.
 */
object TestAppLinkedin extends Logging {

  def main(args: Array[String]): Unit = {

    if (args.length > 0) {
      DBs.setupAll()


      val applicationName = args(0)

      applicationName match {

        //sbt "coldemail/runMain utils.testapp.TestAppLinkedin createUser_CaptainData"
        case "createUser_CaptainData" =>
          object main extends TestAppTrait {
            val request = CaptainDataUserCreateRequest(
              email = "<EMAIL>",
              fullName = "Akula Guru",
              country = "IN",
              timezone = "IST"
            )


            def init = captainDataApi.createUser(
              request = request
              )
              .map(value => logger.info(s"Successful ${value}"))
              .recover { case e => logger.error(e.getMessage, e) }
          }

          Await.result(main.init, scala.concurrent.duration.Duration.Inf)

        //sbt "coldemail/runMain utils.testapp.TestAppLinkedin removeUser_CaptainData"
        case "removeUser_CaptainData" =>
          object main extends TestAppTrait {

            def init = captainDataApi.deleteUser(
                userUid = CaptainDataUserUID(uid="fdfcf2c3-a469-4125-916b-a18d20591a76")
              )
              .map(value => logger.info(s"Successful ${value}"))
              .recover { case e => logger.error(e.getMessage, e) }
          }

          Await.result(main.init, scala.concurrent.duration.Duration.Inf)


        //sbt "coldemail/runMain utils.testapp.TestAppLinkedin removeWorkflow_CaptainData"
        case "removeWorkflow_CaptainData" =>
          object main extends TestAppTrait {

            val linkedinSettingUuid = LinkedinSettingUuid(uuid = "linkedin_account_2vX40r8aZe8zQxIF2sque2sR8rn")

            def init = captainDataApi.deleteJobSchedule(
                job_name = s"Extract LinkedIn Connections - ${linkedinSettingUuid.uuid}"
              )
              .map(value => logger.info(s"Successful ${value}"))
              .recover { case e => logger.error(e.getMessage, e) }
          }

          Await.result(main.init, scala.concurrent.duration.Duration.Inf)

        //sbt "coldemail/runMain utils.testapp.TestAppLinkedin addLinkedinAccount_CaptainData"
        case "addLinkedinAccount_CaptainData" =>
          object main extends TestAppTrait {

            def init = captainDataApi.addLinkedInAccount(
                name = "Smartreach account",
                liAtCookie = "AQEDAVbk6AMEQOwqAAABlNp7w00AAAGU_ohHTVYAwb9p-kLBB5ZxZMOFuovKJ9HRPQqJIlPxp9-ZZAj0DsKs2DFRubrlX3R7WndCVRmvVuAGKrJ6n21QnmXtZ7sAt35PhNvrFJmV_-zBMEXpaoGDaGlf",
                liACookie = None,
                userUid = CaptainDataUserUID(uid="a5033c9f-019d-49a4-9f2b-70bd151516ef")
              )
              .map(value => logger.info(s"Successful ${value}"))
              .recover { case e => logger.error(e.getMessage, e) }
          }

          Await.result(main.init, scala.concurrent.duration.Duration.Inf)

        //sbt "coldemail/runMain utils.testapp.TestAppLinkedin updateLinkedinAccount_CaptainData"
        case "updateLinkedinAccount_CaptainData" =>
          object main extends TestAppTrait {

            def init = captainDataApi.updateLinkedInAccount(
                accountUid = CaptainDataAccountUID(uid="b4c2897c-57af-416c-ac16-797d31f6fafc"),
                name = "Smartreach account",
                liAtCookie = "AQEDAVbk6AMFnUsiAAABlN_LvVkAAAGVA9hBWVYAZb4YaNIuamWEJcPoEj9IiktuvrKCDwTCm6iOFZXSaz_LrUzp7danLXYOy0yOd2IwWG-N_b5s6QoQSKVC2U3p9Pp2yXD0jWZHJq8qe60_g0meaJsa",
                liACookie = None,
                userUid = CaptainDataUserUID(uid="fdfcf2c3-a469-4125-916b-a18d20591a76")
              )
              .map(value => logger.info(s"Successful ${value}"))
              .recover { case e => logger.error(e.getMessage, e) }
          }

          Await.result(main.init, scala.concurrent.duration.Duration.Inf)

        //sbt "coldemail/runMain utils.testapp.TestAppLinkedin view_LinkedinAccount_CaptainData"
        case "view_LinkedinAccount_CaptainData" =>
          object main extends TestAppTrait {

            def init = captainDataApi.viewLinkedInProfile(
                accountUid = CaptainDataAccountUID(uid="b4c2897c-57af-416c-ac16-797d31f6fafc"),
                linkedinProfileUrl = "https://www.linkedin.com/in/akhilesh-betanamudi/"
              )
              .map(value => logger.info(s"Successful ${value}"))
              .recover { case e => logger.error(e.getMessage, e) }
          }

          Await.result(main.init, scala.concurrent.duration.Duration.Inf)

        //sbt "coldemail/runMain utils.testapp.TestAppLinkedin extract_linkedinConnections_CaptainData"
        case "extract_linkedinConnections_CaptainData" =>
          object main extends TestAppTrait {

            def init = captainDataApi.extractLinkedInConnections(
                accountUid = CaptainDataAccountUID(uid="9ac40399-0aea-42a3-b40f-710258b56df9"),
                team_id = TeamId(id = 36L),
                linkedinSettingUuid = LinkedinSettingUuid(uuid = "linkedin_account_2vX40r8aZe8zQxIF2sque2sR8rn"),
                extract_all = true
              )
              .map(value => logger.info(s"Successful ${value}"))
              .recover { case e => logger.error(e.getMessage, e) }
          }

          Await.result(main.init, scala.concurrent.duration.Duration.Inf)


        //sbt "coldemail/runMain utils.testapp.TestAppLinkedin extract_linkedinConnectionsResults_CaptainData"
        case "extract_linkedinConnectionsResults_CaptainData" =>
          object main extends TestAppTrait {

            def init = captainDataApi.extractLinkedInConnectionsResults(
                job_uid = CaptainDataJobUID(uid = "6be38899-27a6-42c7-b329-949856b0868b")
              )
              .map(value => {

                println(value)
                logger.info(s"Successful ${value}")
              })
              .recover { case e => logger.error(e.getMessage, e) }
          }

          Await.result(main.init, scala.concurrent.duration.Duration.Inf)

        //sbt "coldemail/runMain utils.testapp.TestAppLinkedin extract_linkedinConnectionsResults_CaptainData_test"
        case "extract_linkedinConnectionsResults_CaptainData_test" =>
          object main extends TestAppTrait {

            given logger: SRLogger = new SRLogger(logRequestId = "TestApp :: extract_linkedinConnectionsResults_CaptainData_test")


            def init = linkedinConnectionsService.handleExtractLinkedinConnectionGetResults(
                data = CaptainDataWebhookResponse(
                  job_uid= CDJobUid(uid = "4a658639-363f-4559-a09a-4bbbd0d28808"),
                  workflow_name = EXTRACT_LINKEDIN_CONNECTIONS,
                  error = None
                )
              )
              .map(value => {

                println(value)
                logger.info(s"Successful ${value}")
              })
              .recover { case e => logger.error(e.getMessage, e) }
          }

          Await.result(main.init, scala.concurrent.duration.Duration.Inf)

        //sbt "coldemail/runMain utils.testapp.TestAppLinkedin extract_linkedinConnectionsResults_CaptainData_test"
        case "normalise_linkedin_profile_url_profile_connections" =>
          object main extends TestAppTrait {

            given logger: SRLogger = new SRLogger(logRequestId = "TestApp :: normalise_linkedin_profile_url_profile_connections")


            def init = Future.fromTry(linkedinConnectionsService.normalizeAndUpdateLinkedinProfileUrls(
                teamId = TeamId(id = 6412L),
                linkedinSettingUuid = LinkedinSettingUuid(uuid = "linkedin_account_2NgNtrpnpGY2Ej97mGVWipKZaM8") //Balaji's account
              ))
              .map(value => {

                logger.info(s"Successfully updated rows :: ${value} ")
              })
              .recover { case e => logger.error(e.getMessage, e) }
          }

          Await.result(main.init, scala.concurrent.duration.Duration.Inf)


          //sbt "coldemail/runMain utils.testapp.TestAppLinkedin testLinkedin_CaptainData"
          case "testLinkedin_CaptainData" =>
            object main extends TestAppTrait {
              val logger = new SRLogger("testLinkedin_CaptainData")
              def init = linkedinActionsPublishService.fetchAndProcessLinkedinAccounts(
                publishLogger = logger
              ).recover {
              case e => logger.error("Error while publishing PhantomBuster Actions", e)
              }
            }

            Await.result(main.init, scala.concurrent.duration.Duration.Inf)

        // sbt "coldemail/runMain utils.testapp.TestAppLinkedin testLinkedin_taskexecution_CaptainData"
        case "testLinkedin_taskexecution_CaptainData" =>
          object main extends TestAppTrait {
            val logger = new SRLogger("testLinkedin_taskexecution_CaptainData")
            def init = mqExecuteDueLinkedinTasks.processMessage(
              msg = TasksGroupedByTypeAndLinkedinSetting(
                commonLinkedinTaskDetails = CommonLinkedinTaskDetails(
                  teamId = TeamId(id = 2),
                  linkedinSettingUuid = "linkedin_account_2vZTvjq2a7sjlwkNKuKxsb0PPXv",
                  taskType = TaskType.AutoLinkedinConnectionRequest,
                  orgId = OrgId(id = 2)
                ),
                taskIds = Seq("task_2vitYW9c4XTDzGIntLItN6FdJeW")
              )
            ).recover {
              case e => logger.error("Error while publishing PhantomBuster Actions", e)
            }
          }
          Await.result(main.init, scala.concurrent.duration.Duration.Inf)

        //sbt "coldemail/runMain utils.testapp.TestAppLinkedin sendLinkedInConnection_CaptainData"
        case "sendLinkedInConnection_CaptainData" =>
          object main extends TestAppTrait {

            def init = captainDataApi.sendLinkedInConnection(
                accountUid = CaptainDataAccountUID(uid="b4c2897c-57af-416c-ac16-797d31f6fafc"),
                messageOpt = Some(""),
                linkedinProfileUrl = "https://www.linkedin.com/in/vivek-gandharkar/"
              )
              .map(value => logger.info(s"Successful ${value}"))
              .recover { case e => logger.error(e.getMessage, e) }
          }

          Await.result(main.init, scala.concurrent.duration.Duration.Inf)

        case "sendLinkedInMessage_CaptainData" =>
          object main extends TestAppTrait {

            def init = captainDataApi.sendLinkedInMessage(
                accountUid = CaptainDataAccountUID(uid="b4c2897c-57af-416c-ac16-797d31f6fafc"),
                linkedinProfileUrl = "https://www.linkedin.com/in/akuladatta/",
                message = "Hi, How are you?"

              )
              .map(value => logger.info(s"Successful ${value}"))
              .recover { case e => logger.error(e.getMessage, e) }
          }

          Await.result(main.init, scala.concurrent.duration.Duration.Inf)

        // sbt "coldemail/runMain utils.testapp.TestAppLinkedin extract_Linkedin_conversations_CaptainData"
        case "extract_Linkedin_conversations_CaptainData" =>
          object main extends TestAppTrait {

            def init = captainDataApi.retrieveLinkedInConversations(
                accountUid = CaptainDataAccountUID(uid="b17aafdb-e78f-46d2-9f5b-455d9a06a56e"),
                accountId = AccountId(id = 2),
                linkedinSettingId = LinkedinSettingId(id = 118),
                repetitiveMinutes = 60,
                fromTimeInMinutes = 60,
                teamId = TeamId(id = 2)
              )
              .map(value => logger.info(s"Successful ${value}"))
              .recover { case e => logger.error(e.getMessage, e) }
          }

          Await.result(main.init, scala.concurrent.duration.Duration.Inf)

        // sbt "coldemail/runMain utils.testapp.TestAppLinkedin handle_conversation_extraction_CaptainData"
        case "handle_conversation_extraction_CaptainData" =>
          object main extends TestAppTrait {
            given logger: SRLogger = new SRLogger(logRequestId = "TestApp :: handle_conversation_extraction_CaptainData")

            def init = linkedinSettingService.handleCaptainDataWebhookStatusUpdate(
                data = CaptainDataWebhookResponse(
                  job_uid = CDJobUid(uid="4ebdfa5b-de0e-43c2-861e-03886cea88cf"),
                  workflow_name = CaptainDataWorkflowName.EXTRACT_LINKEDIN_CONVERSATIONS,
                  error = None
                ),
                status = CaptainDataWorkflowStatus.SUCCESSFUL
              ).map(value => logger.info(s"Successful ${value}"))
              .recover { case e => logger.error(e.getMessage, e) }
          }

          Await.result(main.init, scala.concurrent.duration.Duration.Inf)

        // sbt "coldemail/runMain utils.testapp.TestAppLinkedin handle_message_extraction_CaptainData"
        case "handle_message_extraction_CaptainData" =>
          object main extends TestAppTrait {
            given logger: SRLogger = new SRLogger(logRequestId = "TestApp :: handle_message_extraction_CaptainData")

            def init = linkedinSettingService.handleCaptainDataWebhookStatusUpdate(
                data = CaptainDataWebhookResponse(
                  job_uid = CDJobUid(uid="aec9da01-3b3a-47b7-986f-47ae8552779e"),
                  workflow_name = CaptainDataWorkflowName.EXTRACT_LINKEDIN_MESSAGES,
                  error = None
                ),
                status = CaptainDataWorkflowStatus.SUCCESSFUL
              ).map(value => logger.info(s"Successful ${value}"))
              .recover { case e => logger.error(e.getMessage, e) }
          }
          Await.result(main.init, scala.concurrent.duration.Duration.Inf)

        case "retrieveLinkedinMessages_CaptainData" =>
          object main extends TestAppTrait {

            def init = captainDataApi.retrieveLinkedInMessages(
                accountUid = CaptainDataAccountUID(uid="b17aafdb-e78f-46d2-9f5b-455d9a06a56e"),
                linkedinMessageThreadUrl = LinkedinMessageThreadUrl(url="https://www.linkedin.com/messaging/thread/2-MGVhYzg4MjktYjhlYy00NmUwLTgzN2ItZDNmMDY2NTkzNWNiXzAxMg==/"),
                fromTimeInMinutes = 0,
                linkedinSettingId = LinkedinSettingId(id = 118),
                accountId = AccountId(id = 2),
                teamId = TeamId(id = 2),
                threadsTableId = 1
              )
              .map(value => logger.info(s"Successful ${value}"))
              .recover { case e => logger.error(e.getMessage, e) }
          }

          Await.result(main.init, scala.concurrent.duration.Duration.Inf)

        case "updateLinkedinCaptainDataAccountDetails" =>
          object main extends TestAppTrait {

            given logger: SRLogger = new SRLogger(logRequestId = "TestApp :: updateLinkedinCaptainDataAccountDetails")

            def init = linkedinSettingDAO.updateLinkedinCaptainDataAccountDetails(
                captainDataAccountUID = CaptainDataAccountUID(uid="b4c2897c-57af-416c-ac16-797d31f6fafc"),
                linkedinAccountUuid = LinkedinSettingUuid(uuid="linkedin_account_2rsgLDThxuc3I0Cjway6Ql2hJB1"),
                linkedinProfileUrl = "https://www.linkedin.com/in/akuladatta/",
                initial_sync_li_connection_job_uid = None,
                teamId = TeamId(id=2)
              ).map(value => {
                println(s"Successful ${value}")
                logger.info(s"Successful ${value}")
              })
              .recover { case e => logger.error(e.getMessage, e) }
          }

          main.init

        case "test_timezone_from_country" =>
          object main extends TestAppTrait {

            given logger: SRLogger = new SRLogger(logRequestId = "TestApp :: test_timezone_from_country")

            def init = TimezoneUtils.getTimezoneForCountry(
                country = Some("India")
              ).map(value => {
                println(s"Successful ${value}")
                logger.info(s"Successful ${value}")
              })

          }
          main.init


        case "parse_retrieve_message_job_response_CaptainData" =>
          object main extends TestAppTrait {

            given logger: SRLogger = new SRLogger(logRequestId = "TestApp :: parse_retrieve_message_job_response_CaptainData")

            def init = captainDataApi.retrieveLinkedinMessagesByJobUid(
                jobUid = CaptainDataJobUID(uid="53b06bc7-9351-4e9b-ad93-d67e85eba90f")
              ).map(value => {
                println(s"Successful ${value}")
                logger.info(s"Successful ${value}")
              })
              .recover { case e => logger.error(e.getMessage, e) }
          }

          Await.result(main.init, scala.concurrent.duration.Duration.Inf)

        case "parse_send_message_job_response_CaptainData" =>
          implicit val  logger: SRLogger = new SRLogger(logRequestId = "TestApp :: parse_job_response_CaptainData")

          object main extends TestAppTrait {

            def init = captainDataApi.parseJobResponseByTask(
                jobUid = CaptainDataJobUID(uid="8b0fd0a4-efdc-44b9-9ea8-d6f7ee338096"),
                task = TaskType.AutoLinkedinMessage
              )
              .map(value => {
                println(s"Successful ${value}")
                logger.info(s"Successful ${value}")
              })
              .recover { case e => logger.error(e.getMessage, e) }
          }

          Await.result(main.init, scala.concurrent.duration.Duration.Inf)

        //sbt "coldemail/runMain utils.testapp.TestAppLinkedin parse_job_response_CaptainData"
        case "parse_connection_request_job_response_CaptainData" =>
          implicit val  logger: SRLogger = new SRLogger(logRequestId = "TestApp :: parse_job_response_CaptainData")

          object main extends TestAppTrait {

            def init = captainDataApi.parseJobResponseByTask(
                jobUid = CaptainDataJobUID(uid="b8f9043e-55c0-4eae-b1c2-360064f2a60a"),
                task = TaskType.AutoLinkedinConnectionRequest
              )
              .map(value => {
                println(s"Successful ${value}")
                logger.info(s"Successful ${value}")
              })
              .recover { case e => logger.error(e.getMessage, e) }
          }

          Await.result(main.init, scala.concurrent.duration.Duration.Inf)



        //sbt "coldemail/runMain utils.testapp.TestAppLinkedin parse_job_response_CaptainData"
        case "parse_job_response_CaptainData" =>
          implicit val  logger: SRLogger = new SRLogger(logRequestId = "TestApp :: parse_job_response_CaptainData")

          object main extends TestAppTrait {

            def init = captainDataApi.parseJobResponseByTask(
                jobUid = CaptainDataJobUID(uid="62bbb47e-e012-4e52-ae75-572b80f43bf2"),
                task = TaskType.AutoViewLinkedinProfile
              )
              .map(value => {
                logger.info(s"Successful ${value}")
              })
              .recover { case e => logger.error(e.getMessage, e) }
          }

          Await.result(main.init, scala.concurrent.duration.Duration.Inf)


        case "startLinkedinSchedulerMsgConsumer" =>

          object main extends TestAppTrait {


            def init = mqLinkedinChannelScheduler.startConsumer() match {
              case Failure(e) => logger.error(e.getMessage, e)
              case Success(value) => logger.info(s"startConsumer Succeeded: $value")
            }
          }

          main.init

        case "TestnowQuery" =>
          println(DB readOnly { implicit session =>
            sql"""
                 SELECT * FROM linkedin_settings;
                 """
              .map(rs => rs.string("uuid"))
              .list
              .apply()
          })

        // sbt "coldemail/runMain utils.testapp.TestAppLinkedin handle_linkedin_conversation_extraction_successful_CaptainDataAPI"
        case "handle_linkedin_conversation_extraction_successful_CaptainDataAPI" =>
          object main extends TestAppTrait {
            given logger: SRLogger = new SRLogger(logRequestId = "TestApp :: handle_linkedin_conversation_extraction_successful_CaptainDataAPI")

            def init = captainDataApi.getLinkedinThreadsFromJobUidWithPagination(
                jobUid = CaptainDataJobUID(uid = "19b07224-e2cc-4944-a0a0-5e3da7b6445c"),
                page = None
              ).map(value => {
                println(s"Successful conversation extraction: ${value.length}")
                logger.info(s"Successful conversation extraction: ${value}")
              })
              .recover { case e => logger.error(e.getMessage, e) }
          }

          Await.result(main.init, scala.concurrent.duration.Duration.Inf)

        case "processLinkedinSchedulerMsg" =>

          object main extends TestAppTrait {

            def init = mqLinkedinChannelScheduler.processMessage(msg = MqLinkedinChannelSchedulerMsg(12L, "linkedin_account_2RYrcWkyDEb347jToQwxGSpydsz"))
              .map(value => logger.info(s"processMessage Successful ${value}"))
              .recover { case e => logger.error(e.getMessage, e) }
          }

          Await.result(main.init, scala.concurrent.duration.Duration.Inf)


        // sbt "coldemail/runMain utils.TestApp fetchContainerResultObject"
        case "fetchContainerResultObject" =>
          implicit val  system: ActorSystem = ActorSystem()
          implicit val  materializer = Materializer(system)
          implicit val  wSClient: AhcWSClient = AhcWSClient()
          implicit val  actorContext: ExecutionContext = system.dispatcher
          implicit val  logger: SRLogger = new SRLogger(logRequestId = "TestApp :: fetchContainerResultObject")

          val phantomBusterApi = new PhantomBusterApi()

          val res = phantomBusterApi.fetchContainerResultObject(
            apiKey = PhantomBusterApiKey(api_key = "<PHANTOMBUSTER_API_KEY>"),
            containerId = PhantomBusterContainerId(id = "<PHANTOMBUSTER_CONTAINER_ID>")
          )

          Await.result(
            res.map {
                case None => println("Container is still running")

                case Some(jsObj) =>
                  println(PhantomBusterService.parseInboxScraperContainerResult(jsObj))

              }
              .recover { case e => println(s"${e.getMessage}, ${e.getStackTrace.mkString("Array(", ", ", ")")}") }, scala.concurrent.duration.Duration.Inf)

        //  sbt "coldemail/runMain utils.TestApp fetchAllAgentsInPhantomBuster"
        case "fetchAllAgentsInPhantomBuster" =>
          implicit val  system: ActorSystem = ActorSystem()
          implicit val  materializer: Materializer = Materializer(system)
          implicit val  ws: WSClient = AhcWSClient()
          implicit val  ec: ExecutionContext = system.dispatcher
          implicit val  logger: SRLogger = new SRLogger(logRequestId = "TestApp fetchAllPhantoms ::")

          val phantomBusterApi = new PhantomBusterApi()

          val response = phantomBusterApi.fetchAllPhantoms(
            apiKey = PhantomBusterApiKey(api_key = "<PHANTOMBUSTER_API_KEY>")
          )

          Await.result(response.map(res => println(s"\n$res")).recover { case e => println(s"${e.toString}") }, scala.concurrent.duration.Duration.Inf)

        /*
                //  sbt "coldemail/runMain utils.TestApp launchPhantomBusterAgent"
                case "launchPhantomBusterAgent" =>
                  implicit lazy val system: ActorSystem = ActorSystem()
                  implicit lazy val materializer: Materializer = Materializer(system)
                  implicit lazy val ws: WSClient = AhcWSClient()
                  implicit lazy val ec: ExecutionContext = system.dispatcher
                  implicit lazy val logger: SRLogger = new SRLogger(logRequestId = "TestApp launchPhantomBusterAgent ::")

                  val phantomBusterApi = new PhantomBusterApi()

                  val data = PhantomBusterService.createDataToLaunchAgent(
                    taskType = TaskType.AutoViewLinkedinProfile,
                    inmailSubject = None,
                    inmailBody = None,
                    agentId = PhantomBusterAgentId(id = "<PHANTOMBUSTER_AGENT_ID>"),
                    linkedinProfileOrSpreadSheetUrl = "<LINKEDIN_PROFILE_URL>",
                    linkedinSessionCookie = "<LINKEDIN_SESSION_COOKIE>"
                  )

                  val response = phantomBusterApi.launchAgent(
                    apiKey = PhantomBusterApiKey(api_key = "<PHANTOMBUSTER_API_KEY>"),
                    data = data.get
                  )

                  Await.result(response.map(res => println(s"\n$res")).recover { case e => println(s"${e.toString}") }, scala.concurrent.duration.Duration.Inf)
        */

        /*
      //  sbt "coldemail/runMain utils.TestApp fetchContainerStatus"
      case "fetchContainerStatus" =>
        implicit lazy val system: ActorSystem = ActorSystem()
        implicit lazy val materializer: Materializer = Materializer(system)
        implicit lazy val ws: WSClient = AhcWSClient()
        implicit lazy val ec: ExecutionContext = system.dispatcher
        implicit lazy val logger: SRLogger = new SRLogger(logRequestId = "TestApp fetchAllPhantoms ::")

        val phantomBusterApi = new PhantomBusterApi()

        val response = phantomBusterApi.fetchContainerStatus(
          apiKey = PhantomBusterApiKey(api_key = "<PHANTOMBUSTER_API_KEY>"),
          containerId = PhantomBusterContainerId(id = "<PHANTOMBUSTER_CONTAINER_ID>")
        )

        Await.result(response.map(res => println(s"\n$res")).recover { case e => println(s"${e.toString}") }, scala.concurrent.duration.Duration.Inf)
*/


        // sbt "coldemail/runMain utils.testapp.TestAppLinkedin test_exclude_step_types"
        case "test_exclude_step_types" =>
          given logger: SRLogger = new SRLogger("TestAppLinkedin :: test_exclude_step_types")

          object main extends TestAppTrait {
            def init = Future {
              campaignProspectDAO.getCampaignsWhoseReplyWillBeTrackedInThisMessageThread(
                teamId = TeamId(2L),
                messageThreadId = LinkedinMessageThreadId(2L)
              ) match {
                case Failure(exception) =>
                  logger.error(s"Failed to find prospects: $exception")
                case Success(value) =>
                  println(s"Successfully found prospects: $value")
                  logger.info(s"Successfully found prospects: $value")
              }
            }
          }

          Await.result(main.init, scala.concurrent.duration.Duration.Inf)

        //sbt "coldemail/runMain utils.testapp.TestAppLinkedin updateLinkedinAccountSettings_test"
        case "updateLinkedinAccountSettings_test" =>
          object main extends TestAppTrait {
            given logger: SRLogger = new SRLogger("TestApp :: updateLinkedinAccountSettings_test")

            def init = linkedinSettingDAO.updateLinkedinAccountSettings(
              data = CreateOrUpdateLinkedinAccountSettings(
                first_name = "Parth",
                last_name = "Gupta",
                email = "<EMAIL>",
                password = None,
                profile_url = "https://www.linkedin.com/in/parth7729/",
                country = Some("India"),
                owner_id = 93L,
                view_profile_limit_per_day = 100,
                inmail_limit_per_day = 50,
                message_limit_per_day = 50,
                connection_request_limit_per_day = 25,
                allow_automated_task = Some(true),
                li_at_cookie = Some("AQEDAVbk6AMFnUsiAAABlN_LvVkAAAGVA9hBWVYAZb4YaNIuamWEJcPoEj9IiktuvrKCDwTCm6iOFZXSaz_LrUzp7danLXYOy0yOd2IwWG-N_b5s6QoQSKVC2U3p9Pp2yXD0jWZHJq"),
                li_a_cookie = None
              ),
              uuid = "linkedin_account_2hgnmtlXWOroefKt6ojCxqYdD89",
              teamId = 80L
            ).map(value => {
              println(s"Successfully updated LinkedIn account settings: ${value}")
              logger.info(s"Successfully updated LinkedIn account settings: ${value}")
            })
            .recover { case e =>
              logger.error(s"Failed to update LinkedIn account settings: ${e.getMessage}", e)
            }
          }

          main.init

        //sbt "coldemail/runMain utils.testapp.TestAppLinkedin normalizeLinkedinProfileUrls"
        case "normalizeLinkedinProfileUrls" =>
          object main extends TestAppTrait {
            implicit val logger: SRLogger = new SRLogger(logRequestId = "TestApp :: normalizeLinkedinProfileUrls")

            def init = linkedinSettingService.normalizeLinkedinProfileUrls()
              .map(updatedCount => {
                logger.info(s"Successfully normalized ${updatedCount} LinkedIn profile URLs")
              })
              .recover { case e => logger.error(s"Error normalizing LinkedIn profile URLs: ${e.getMessage}", e) }
          }

          Await.result(main.init, scala.concurrent.duration.Duration.Inf)

        //sbt "coldemail/runMain utils.testapp.TestAppLinkedin normalizeLinkedinProfileUrlsForTeam"
        case "normalizeLinkedinProfileUrlsForTeam" =>
          object main extends TestAppTrait {
            implicit val logger: SRLogger = new SRLogger(logRequestId = "TestApp :: normalizeLinkedinProfileUrlsForTeam")

            // You can change the team ID as needed
            val teamId = TeamId(id = 2)

            def init = linkedinSettingService.normalizeLinkedinProfileUrls(Some(teamId))
              .map(updatedCount => {
                logger.info(s"Successfully normalized ${updatedCount} LinkedIn profile URLs for team ${teamId.id}")
              })
              .recover { case e => logger.error(s"Error normalizing LinkedIn profile URLs for team ${teamId.id}: ${e.getMessage}", e) }
          }

          Await.result(main.init, scala.concurrent.duration.Duration.Inf)

        //sbt "coldemail/runMain utils.testapp.TestAppLinkedin test_handleCookieFailureIfAny"
        case "test_handleCookieFailureIfAny" =>
          object main extends TestAppTrait {
            given logger: SRLogger = new SRLogger(logRequestId = "TestApp :: test_handleCookieFailureIfAny")

            def init = {
              println("Enter Captain Data Job UID:")
              val jobUid = CaptainDataJobUID(uid = scala.io.StdIn.readLine())

              println("Enter error message:")
              val errorMsg = scala.io.StdIn.readLine()

              val result = linkedinSettingService.handleCookieFailureIfAny(
                jobUid = jobUid,
                errorMsg = errorMsg
              )

              println(s"Cookie failure detected: ${result}")
              logger.info(s"Cookie failure test result: ${result}")

            }
          }

          main.init

        // sbt "coldemail/runMain utils.testapp.TestAppLinkedin handle_linkedin_conversation_extraction_successful_CaptainData"
        case "handle_linkedin_conversation_extraction_successful_CaptainData" =>
          object main extends TestAppTrait {
            given logger: SRLogger = new SRLogger(logRequestId = "TestApp :: handle_linkedin_conversation_extraction_successful_CaptainData")

            def init = captainDataService.handleLinkedinConversationExtraction(
                jobUid = CDJobUid(uid = "e3b14552-7f5a-45f1-9d2c-27717302b8f6"),
                status = CaptainDataWorkflowStatus.SUCCESSFUL,
                error = None
              ).map(value => {
                println(s"Successful conversation extraction: ${value}")
                logger.info(s"Successful conversation extraction: ${value}")
              })
              .recover { case e => logger.error(e.getMessage, e) }
          }

          Await.result(main.init, scala.concurrent.duration.Duration.Inf)

      }
    }

  }
}
