package utils.testapp

import api.emails.EmailSettingDAO
import api.scheduler.model.SendNewEmailManuallyIntegrationTestData
import api.scheduler.model.SendNewEmailManuallyIntegrationTestData.InputData
import io.smartreach.esp.api.emails.*
import io.smartreach.esp.api.microsoftOAuth.{MicrosoftInvalidGrantException, MicrosoftOAuthSettings}
import io.smartreach.esp.utils.email.{OutlookEmailSendDetails, OutlookJustLogException, OutlookSendEmailService}
import play.api.Logging
import play.api.libs.json.{JsObject, Json}
import play.api.libs.ws.WSBodyWritables.*
import play.api.libs.ws.WSResponse
import scalikejdbc.config.DBs
import scalikejdbc.{scalikejdbcSQLInterpolationImplicitDef, sqls}
import utils.security.EncryptionHelpers
import utils.testapp.TestAppTrait
import utils.uuid.SrUuidUtils
import utils.{ISRLogger, SRAppConfig, SRLogger}

import scala.concurrent.duration.Duration
import scala.concurrent.{Await, Future}


/*
This object holds any sort internal test function used to test features/changes manually during development.
 */
object TestAppEmail extends Logging {

  def main(args: Array[String]): Unit = {

    if (args.length > 0) {
      DBs.setupAll()


      val applicationName = args(0)

      applicationName match {

        case "test_without_update" =>
          val logger: SRLogger = new SRLogger("TestApp : test_without_update ")
          object Test extends TestAppTrait {

            def _createMsg(
              createUrl: String,
              newMsgCreateUrl: String,
              accessToken: String,
              outlookEmailSendDetails: OutlookEmailSendDetails,
              Logger: ISRLogger,
              body: JsObject
            ): Future[WSResponse] = {

              wSClient.url(createUrl)
                .addHttpHeaders(
                  "Authorization" -> accessToken,
                  "Prefer" -> "IdType=ImmutableId"
                )
                .post(Json.stringify(body))
                .flatMap(
                  replyMsgResp => {

                    if (replyMsgResp.status != 201) {

                      Logger.debug(s"_createMsg: replyMsgResp.body: ${replyMsgResp.body} and status: ${replyMsgResp.status}")

                      Logger.debug(s"_createMsg: headers: ${replyMsgResp.headers} replyMsgResp.json: ${replyMsgResp.json}")

                      val error = (replyMsgResp.json \ "error" \ "code").asOpt[String].getOrElse("")
                      val cantBeReplyError = error == "ErrorInvalidOperation" || error == "ErrorItemNotFound"
                      val accessDeniedError = error == "ErrorAccessDenied"

                      if (!cantBeReplyError) {

                        Logger.error(s"FATAL OutlookApiService CreateMessageResponse replyMsgResp cantBeReplyError: $cantBeReplyError :: error: $replyMsgResp ::: ${replyMsgResp.json} :: $createUrl :: $outlookEmailSendDetails")

                        if (accessDeniedError) {

                          throw MicrosoftInvalidGrantException(s"There was an error while OutlookApiService CreateMessageResponse Error Code: ${error}")

                        } else {

                          throw new Exception(s"There was an error while OutlookApiService CreateMessageResponse Error Code: ${error}")

                        }

                      } else {

                        Logger.error(s"FATAL OutlookApiService CreateMessageResponse replyMsgResp cantBeReplyError: $cantBeReplyError  :: TRYING NEXT :: error: $replyMsgResp ::: ${replyMsgResp.json} :: $createUrl :: $outlookEmailSendDetails")

                        wSClient.url(newMsgCreateUrl)
                          .addHttpHeaders(
                            "Authorization" -> accessToken,
                            "Prefer" -> "IdType=ImmutableId"
                          )
                          .post(Json.obj())
                          .map(
                            newMsgResp => {
                              if (newMsgResp.status != 201) {

                                val errorCode = (newMsgResp.json \ "error" \ "code").asOpt[String].getOrElse("")

                                Logger.error(s"FATAL OutlookApiService CreateMessageResponse newMsgResp error: $newMsgResp ::: ${newMsgResp.json} :: $newMsgCreateUrl :: $outlookEmailSendDetails")

                                if (errorCode == "ErrorAccessDenied") {

                                  throw MicrosoftInvalidGrantException(s"There was an error while OutlookApiService CreateMessageResponse Error Code: ${error}")

                                } else {

                                  throw new Exception(s"There was an error while OutlookApiService CreateMessageResponse Error Code: ${error}")
                                }


                              } else {

                                newMsgResp

                              }
                            }
                          )
                      }
                    } else {
                      Logger.debug(s"_createMsg: headers: ${replyMsgResp.headers} replyMsgResp.json: ${replyMsgResp.json}")

                      Future.successful(replyMsgResp)

                    }
                  }
                )
            }

            def sendEmailViaOutlookApi(
              outlookEmailSendDetails: OutlookEmailSendDetails,
              emailToBeSent: SREmailToBeSent,
              microsoftOAuthSettings: MicrosoftOAuthSettings
            ): Future[EmailSentResponse.MSSendEmailRes] = {

              outlookEmailSendDetails.oauth2_refresh_token match {


                // FIXME: instead of error tell the user no refresh token
                case None => Future.failed(OutlookJustLogException(s"[OutlookApiService]: No refresh token"))

                case Some(refreshToken) =>

                  microsoftOAuth.refreshAccessToken(
                    email = outlookEmailSendDetails.from_email,
                    emailSettingId = outlookEmailSendDetails.sender_email_setting_id,
                    refreshToken = refreshToken,
                    s = microsoftOAuthSettings
                  ).flatMap(
                    res => {

                      val accessToken = res.access_token

                      val newMsgUrl = "https://graph.microsoft.com/v1.0/me/messages"

                      val omsgId = outlookEmailSendDetails.in_reply_to_outlook_msg_id
                      var createUrl = newMsgUrl
                      if (omsgId.isDefined && omsgId.get.trim.nonEmpty) {
                        createUrl = s"https://graph.microsoft.com/v1.0/me/messages/${omsgId.get}/createReply"
                      }

                      val msgBody = emailToBeSent.htmlBody
                      // val toName = emailToBeSent.to_name.getOrElse("")

                      val toRecipients: Seq[JsObject] = OutlookSendEmailService.toJsonEmailAddress(emails = emailToBeSent.to_emails)

                      var body = Json.obj(

                        "subject" -> emailToBeSent.subject,

                        "body" -> Json.obj(
                          "contentType" -> "HTML",
                          "content" -> msgBody
                        ),

                        "from" -> Json.obj(
                          "emailAddress" -> Json.obj(
                            "address" -> emailToBeSent.from_email,
                            "name" -> emailToBeSent.from_name
                          )
                        ),

                        "toRecipients" -> Json.toJson(toRecipients)

                        /*
                    "toRecipients" -> Json.arr(
                      Json.obj(
                        "emailAddress" -> Json.obj(
                          "address" -> emailToBeSent.to_email,
                          "name" -> toName)
                      )
                    )
                    */
                      )


                      //add cc emails
                      if (emailToBeSent.cc_emails.nonEmpty) {

                        val toCCEmails: Seq[JsObject] = OutlookSendEmailService.toJsonEmailAddress(emails = emailToBeSent.cc_emails)
                        body = body ++ Json.obj("ccRecipients" -> Json.toJson(toCCEmails))

                      }


                      //add bcc emails
                      if (emailToBeSent.bcc_emails.nonEmpty) {

                        val toBCCEmails: Seq[JsObject] = OutlookSendEmailService.toJsonEmailAddress(emails = emailToBeSent.bcc_emails)
                        body = body ++ Json.obj("bccRecipients" -> Json.toJson(toBCCEmails))

                      }

                      //add ReplyTo Email and Name
                      if (emailToBeSent.reply_to_email.isDefined && emailToBeSent.reply_to_email.get.trim.nonEmpty && emailToBeSent.from_email.trim.toLowerCase != emailToBeSent.reply_to_email.get.trim.toLowerCase) {
                        val replyTo = Json.arr(
                          Json.obj(
                            "emailAddress" -> Json.obj(
                              "address" -> emailToBeSent.reply_to_email.get,
                              "name" -> emailToBeSent.reply_to_name.get
                            )
                          )
                        )

                        body = body ++ Json.obj("replyTo" -> replyTo)
                      }


                      _createMsg(
                        createUrl = createUrl,
                        newMsgCreateUrl = newMsgUrl,
                        accessToken = accessToken,
                        outlookEmailSendDetails = outlookEmailSendDetails,
                        Logger = logger,
                        body = body
                      ).flatMap(
                        createResp => {

                          logger.debug(s"_createMsg: headers: ${createResp.headers} replyMsgResp.json: ${createResp.json}")

                          if (createResp.status != 201) {

                            logger.error(s"FATAL OutlookApiService CreateMessageResponse error: $createResp ::: ${createResp.json} :: $createUrl :: $outlookEmailSendDetails")

                            throw new Exception("OutlookApiService Error")

                          } else {

                            Thread.sleep(500)

                            val createdId = (createResp.json \ "id").as[String]

                            val sendUrl = s"https://graph.microsoft.com/v1.0/me/messages/${createdId}/send"

                            //SEND
                            wSClient.url(sendUrl)
                              .addHttpHeaders(
                                "Authorization" -> accessToken,
                                "Prefer" -> "IdType=ImmutableId"

                                /*
                           28-March-2022: passing content-length header broke this api in play 2.7+
                           (campaign emails stopped going for office365 users),
                           therefore we are commenting it out below
                          */
                                // "Content-Length" -> msgBody.length.toString
                              )
                              .post(Json.obj())
                              .map(
                                sendResp => {

                                  logger.debug(s"sendResp: headers: ${sendResp.headers} sendResp.json: ${sendResp.json}")

                                  if (sendResp.status != 202) {

                                    logger.error(s"FATAL OutlookApiService SendMessageResponse error: $sendResp ::: ${sendResp.body} ::: msgbody: $body ::: sendUrl: $sendUrl")

                                    throw new Exception("Outlook ")


                                  } else {

                                    val internetMessageIdOpt = (createResp.json \ "internetMessageId").asOpt[String]
                                      .map { id =>
                                        InternetMessageId(id = id)
                                      }

                                    EmailSentResponse.MSSendEmailRes(
                                      message_id = internetMessageIdOpt,
                                      outlook_msg_id = None, // This will be updated in OutlookMsgIdFetchAndUpdateCronService.scala
                                      outlook_conversation_id = (createResp.json \ "conversationId").asOpt[String],
                                      outlook_response_json = Some(createResp.json)
                                    )

                                  }

                                }
                              )
                          }


                        }
                      )

                    }
                  )
              }
            }

            def init() = {

              sendEmailViaOutlookApi(
                outlookEmailSendDetails = OutlookEmailSendDetails(
                  from_email = "<EMAIL>",
                  oauth2_refresh_token = Some("4C64YkCm1xHQh4F5H75IiSdRBIO1TT8rQjCypn2AArUrqVQmOXzQZoGxAxBxRL4H5tNPU3kdHGxyAuS-VYIU5CdrXe0l7Eizm0WWK3bI2X962C5GBJWxPtfNq9pT4rEqv1UBjQG7ZnRmtQAk0BLZCG6ROA2eik-Br9zgVmUnUZUgOWdzeNhJdPjoM8pej1coDKfVKx4XzGFtU49-UZyRFySEVQHIF79o5EglTOjF5--zJnWpwdPppy8g_fiPn6aDwjG0aA6oL7SbBIpf-4YtkBFewoBbFncJbPiwJTrbrXMTnNvRa84uK4jbBcbZwZtOTyqZOLVsbB8YfPddd-gIfXvpU8RXlYS2nWjo66yhHucng_VoIxu-9TGkVsQNljoBvlWUTQsmRGdvEy5uxWucgj2TYnCByohmCNbNtNr6DrrQ6dNw1HXNVYDTB4h5XXjjIt6wnHMv3pBmiev9GYQJgzt5cf71wr46MBgUzI4cctXTey_p4kkLtMCCNgsCDGbL-H47THF_Ea6qUnPSK0LlJfr-7heLsX7yvLYGhYA8TU3KrCPQUp-atcfjnYynaJNV6bHnxFiRTiaUtOAD6qB2otQsQ1AlGVnzt0DNFXHYA5oyKSFq3utDqZfmChZ3OYPxYwovGM2kVLTJU-y2fCK-nkovOymI0NWjFolZhCvBWa25SUeNqYBdGnQRcVhOavdIqnfObstYJxMs6VqlTrtlOpgHl0arsbik9qDnkNx0Wqe3_oGR0vMLC8tQWwJTQ-Jz0tyCbZD7YE8xYBaMYvmWexl_1CWfdva5HJrejAav4K-plKtxTirEwGaB5YjuQKi1pcv4Pp_XSttyZbgI0mSCLuadJCY3_W5WG7QbqUp9pfmvv5567iVBWufyKnN0qOyvG8p3RLDiGnu0VTEpKIUzKmBZdLxxphLjksUUkcili2rSAVXC4toDTMA6MhixqXsAiTyKz4Qwce3c6TaNmsCJmZP8fFC09zC-oaq3hAsDv8OcVz_tBbov8yq-iPoHv29LJio45P18d81aEdLl90Q0XCwCDkucfVwBFgs2o5CJCh56zUa6R9wHXSbTA8YrLz7Ksou5Aj0FMm5g_J96MDgVNbKbEUwPxofuwveFaj1dVxo").map(EncryptionHelpers.decryptEmailSettingCredential),
                  sender_email_setting_id = EmailSettingId(29719L),
                  in_reply_to_outlook_msg_id = None,
                ),
                emailToBeSent = SREmailToBeSent(
                  to_emails = Seq(IEmailAddress(
                    name = Some("animesh"), email = "<EMAIL>"
                  )),
                  from_email = "<EMAIL>",
                  cc_emails = Seq(IEmailAddress(email = "<EMAIL>")),
                  bcc_emails = Seq(IEmailAddress(email = "<EMAIL>")),
                  from_name = "neil",
                  reply_to_email = Some("<EMAIL>"),
                  reply_to_name = None,
                  subject = "Hi subject",
                  textBody = "Hi body",
                  htmlBody =
                    s"""
                       |<table>
                       |              <tr>
                       |                <th>CRM Type</th>
                       |                <th>Success</th>
                       |              </tr>
                       |               <tr>
                       |            <td>abck</td>
                       |            <td>dcbh</td>
                       |          </tr>
                       |            </table>
                       |""".stripMargin,
                  message_id = None,
                  references_header = None,
                  sender_email_settings_id = 29719
                ),
                microsoftOAuthSettings = SRAppConfig.microsoftOAuthSettings
              )

            }
          }


          val result = Test.init()

          Await.result(result, Duration.Inf)

/*

        case "send_manual_email_without_campaign_flow" =>

          object CreateAndScheduleCampaign extends TestAppTrait {

            def init = multichannelTestUtil.sendManualEmailWithoutCampaignIntegrationTest(
              data = SendNewEmailManuallyIntegrationTestData.InputData.inputData.data,
              org_id = SendNewEmailManuallyIntegrationTestData.InputData.inputData.org_id,
              accountId = SendNewEmailManuallyIntegrationTestData.InputData.inputData.account_id,
              teamId = SendNewEmailManuallyIntegrationTestData.InputData.inputData.teamId,
              loggedinAccount = SendNewEmailManuallyIntegrationTestData.InputData.inputData.loggedinInAccount,
              auditRequestLogId = SendNewEmailManuallyIntegrationTestData.InputData.inputData.auditRequestLog,
              permittedAccountIds = Seq(SendNewEmailManuallyIntegrationTestData.InputData.inputData.loggedinInAccount.internal_id),
              version = SendNewEmailManuallyIntegrationTestData.InputData.inputData.version,
              tiid = SendNewEmailManuallyIntegrationTestData.InputData.inputData.tiid
            )
          }

          val result = CreateAndScheduleCampaign.init

          println(s"result ${result}")
          Await.result(result, scala.concurrent.duration.Duration.Inf)

        case "send_manual_email_with_campaign_flow" =>

          object CreateAndScheduleCampaign extends TestAppTrait {

            def init = multichannelTestUtil.sendManualEmailWithCampaignIntegrationTest(
              data = InputData.inputData.data.copy(to = Seq(IEmailAddress(
                name = None,
                email = "<EMAIL>")
              ),
                campaign_step_id = Some(642)

              ),
              org_id = SendNewEmailManuallyIntegrationTestData.InputData.inputData.org_id,
              accountId = SendNewEmailManuallyIntegrationTestData.InputData.inputData.account_id,
              teamId = SendNewEmailManuallyIntegrationTestData.InputData.inputData.teamId,
              loggedinAccount = SendNewEmailManuallyIntegrationTestData.InputData.inputData.loggedinInAccount,
              auditRequestLogId = SendNewEmailManuallyIntegrationTestData.InputData.inputData.auditRequestLog,
              permittedAccountIds = Seq(SendNewEmailManuallyIntegrationTestData.InputData.inputData.loggedinInAccount.internal_id),
              version = SendNewEmailManuallyIntegrationTestData.InputData.inputData.version,
              tiid = SendNewEmailManuallyIntegrationTestData.InputData.inputData.tiid
            )
          }

          val result = CreateAndScheduleCampaign.init

          println(s"result ${result}")
          Await.result(result, scala.concurrent.duration.Duration.Inf)

*/

        case "testemailparse" =>

        //          val a = IEmailAddress.parse("Len Ashack <<EMAIL>\u200B>")
        //
        //          logger.info(s"response: ${a}")


        //          //parse("Len Ashack <<EMAIL>\u200B>")
        //          //parse("Len Ashack <<EMAIL>>")
        //
        //          //val result = parseEmailWithIllegalCharInDomain("\uD835\uDC1A\uD835\uDC27\uD835\uDC32\uD835\uDC2C\uD835\uDC2C\uD835\uDC1E\uD835\uDC1E\uD835\uDC32\uD835\uDC28\uD835\uDC20\uD835\uDC1A\uD835\uDC1E\uD835\uDC2D\uD835\uDC2B\uD835\uDC1E\uD835\uDC2D\uD835\uDC2B\uD835\uDC1A\uD835\uDC22\uD835\uDC2D\uD835\uDC1E\uD835\uDC2C@\uD835\uDC20\uD835\uDC26\uD835\uDC1A\uD835\uDC22\uD835\uDC25.\uD835\uDC1C\uD835\uDC28\uD835\uDC26")
        //          val result = parseEmailWithIllegalCharInDomain("steven.tomas889@gmail.com_mod")
        //
        //          logger.info(s"parse result: $result")

        /*case "test_upload" =>

        val csvData = CsvQueue.findById(1183).get

        Logger.info(s"\n\n find the goddamn csv: $csvData \n\n")
        val inputStreamReader = UploadService.getReaderForCSVUrl(csvData.file_url).get._1

        val mappingFromClient = csvData.column_map.as[Map[String, String]]

        /*UploadService.uploadCSVViaCronV2(
          accountId = csvData.account_id,
          teamId = csvData.team_id,
          taId = csvData.ta_id,
          campaignId = csvData.campaign_id,
          forceUpdateProspects = csvData.force_update_prospects.getOrElse(false),
          mappingFromClient = mappingFromClient,
          fileName = csvData.file_name,
          inputStreamReader = inputStreamReader,
          account = None

        ) match {
          case Failure(e) =>
            Logger.error(s"errrrrror: ${Helpers.getStackTraceAsString(e)}")

          case Success(_) => Logger.info("success")
        }*/

      case "test_toptal" =>

        val file = new File("/Users/<USER>/Downloads/top_csv.csv")


        val inputStreamReader = UploadService.getReaderForCSVFile(
          file = file
          // encoding = encoding
        ).get

        Logger.info(s"found fileL: ${file.getAbsolutePath} :: ${file.getName} :: $file ::\n\n$inputStreamReader")


        UploadService.uploadCSVViaCronV2(
          accountId = 1429,
          teamId = 1377,
          taId = 1487,
          campaignId = Some(7966), // 7966
          forceUpdateProspects = false,
          mappingFromClient = Map[String, String](
            "list" -> "outreach-contacts-1",
            "email" -> "email",
            "first_name" -> "first_name",
            "last_name" -> "last_name",
            "company" -> "full_name",
            "created_at" -> "created",
            "outreach_stage" -> "opted_out"
          ),
          fileName = "outreach-contacts-1",
          inputStreamReader = inputStreamReader,
          account = None

        ) match {
          case Failure(e) =>
            Logger.error(s"errrrrror: ${Helpers.getStackTraceAsString(e)}")

          case Success(_) => Logger.info("success")
        }

      case "test_fs" =>
        FastspringService.getCustomerData(pgCustomerId ="RrRPd7kwQgOaMrJPyedCHA")
          .map { res =>
            Logger.info(s"\n\n====\n\n ${Json.toJson(res)}\n\n----------\ndone\n---------\n\n\n\n\n\n")
          }
          .recover { case e =>
          Logger.error(s"error: ${Helpers.getStackTraceAsString(e)}")}

        FastspringService.createAccount(
          orgId = 0,
          orgName = "hello world",
          firstName = "pratt",
          lastName = "bhatt",
          email = "<EMAIL>"
        )
        */


        case "test__findEmailSettingsSQL" =>
          val srUuidUtils = new SrUuidUtils
          val emailSettingDAO = new EmailSettingDAO(
            srUuidUtils = srUuidUtils
          )

          val result = emailSettingDAO.__findEmailSettingsSQL(
            sqls"where es.team_id = 59;"
          )
          println(s"result --------------- $result")




      }
    }

  }
}
