package utils.testapp

import api.accounts.TeamId
import api.accounts.models.{AccountId, OrgId}
import api.call.models.CallLogResource.{CallLogUUID, NewCallLog}
import api.call.models.{AddCallingCreditDetails, CallDetails, CallSID, CallType, CallingServiceProvider, InitialCallParticipationModeData, SubAccountUuid, TwlSubAccountSid, UsageDetails, WebhookValidateData, PhoneNumber as PhoneNumberValueClass}
import api.call.service.*
import api.twilio.service.TwilioUsageTrigger
import api_layer_models.CurrencyType
import com.twilio.Twilio
import com.twilio.`type`.PhoneNumber
import com.twilio.http.TwilioRestClient
import com.twilio.rest.api.v2010.account.conference.Participant
import com.twilio.rest.api.v2010.account.usage.{Record, Trigger as TwilioTrigger}
import com.twilio.rest.api.v2010.account.{Call, Message, ValidationRequest}
import com.twilio.twiml.VoiceResponse
import com.twilio.twiml.voice.{Conference, Dial, Play}
import org.apache.pekko.actor.ActorSystem
import org.apache.pekko.stream.Materializer
import org.joda.time.DateTime
import play.api.Logging
import scalikejdbc.config.DBs
import utils.SRLogger
import utils.TestApp.logger
import utils.testapp.TestAppTrait

import java.net.URI
import java.time.LocalDate
import java.util.concurrent.Executors
import scala.concurrent.{Await, ExecutionContext, ExecutionContextExecutor, Future}
import scala.util.Try

/*
This object holds any sort internal test function used to test features/changes manually during development.
 */
object TestAppTwilio extends Logging {

  def main(args: Array[String]): Unit = {

    if (args.length > 0) {
      DBs.setupAll()


      //      implicit lazy val system = ActorSystem()
      //      implicit lazy val materializer = ActorMaterializer()
      //      implicit lazy val wSClient: AhcWSClient = AhcWSClient()
      //      implicit lazy val actorContext: ExecutionContext = system.dispatcher


      val applicationName = args(0)

      applicationName match {

        case "twilioTesting" =>

          object main {

            def init()(implicit ec: ExecutionContext): Future[Unit] = Future {
              val ACCOUNT_SID = "**********************************"
              val AUTH_TOKEN = "e116edf67c24cc85b17d6d64db89c2e3"

              Twilio.init(ACCOUNT_SID, AUTH_TOKEN)

              val from = new PhoneNumber("+***********")
              val to = new PhoneNumber("+************") // who are we texting
              val body = "hi how are you?" //readLine("What do you want to say? ")

              val message = Message.creator(to, from, body).create()
              println(s"Message sent to $to with ID ${message.getSid}")

            }

          }
          implicit val ec: ExecutionContextExecutor = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(20))

          val res = main.init()
          Await.result(res, scala.concurrent.duration.Duration.Inf)

        case "twilioTesting_sms" =>

          object main {


            implicit val system: ActorSystem = ActorSystem()
            implicit val materializer: Materializer = Materializer(system) // Create Materializer directly
            implicit val executionContext: ExecutionContextExecutor = system.dispatcher

            def init()(implicit ec: ExecutionContext): Future[Unit] = Future {


              val ACCOUNT_SID = "ACCOUNT_SID"
              val AUTH_TOKEN = "AUTH_TOKEN"

              Twilio.init(ACCOUNT_SID, AUTH_TOKEN)

              val message: Message = Message.creator(
                  new PhoneNumber("PHONE NUMBER 1"),
                  new PhoneNumber("***********"),
                  "Hello")
                .create();

              //          println(s"Message sent to $to with ID ${message.getSid}")
              //

            }

          }
          implicit val ec: ExecutionContextExecutor = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(20))

          val res = main.init()
          Await.result(res, scala.concurrent.duration.Duration.Inf)

        case "twilioTesting_voicecall" =>

          object main {

            import java.net.URI

            def init()(implicit ec: ExecutionContext): Future[Unit] = Future {
              val ACCOUNT_SID = "ACCOUNT_SID"
              val AUTH_TOKEN = "AUTH_TOKEN"

              Twilio.init(ACCOUNT_SID, AUTH_TOKEN)

              val from = new PhoneNumber("from number")
              val to_tulika = new PhoneNumber("to number")

              // Make initial call with a webhook that keeps the call open
              val call = Call.creator(
                to_tulika,
                from,
                URI.create("http://demo.twilio.com/docs/voice.xml")
              ).create()

              println(s"Call went to $to_tulika with ID ${call.getSid}")

              // Wait for call to connect (adjust time as needed)
              Thread.sleep(20000)

              // Trigger voice drop
              try {
                // Create TwiML for the recording
                val response = new VoiceResponse.Builder()
                  .play(new Play.Builder("https://cadet-ram-4675.twil.io/assets/harvard.wav").build())
                  .build()

                // Update the call to play the recording
                val updatedCall = Call.updater(call.getSid)
                  .setTwiml(response.toXml())
                  .update()

                println(s"Voice drop triggered for call ${call.getSid}")
              } catch {
                case e: Exception =>
                  println(s"Failed to trigger voice drop: ${e.getMessage}")
              }
            }
          }
          implicit val ec: ExecutionContextExecutor = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(20))

          val res = main.init()
          Await.result(res, scala.concurrent.duration.Duration.Inf)

        case "twilioTesting_verify_callerId" =>

          /*
            STEPS TO VERIFY CALLERID FOR LOCAL TESTING:
            1. You'll need two twilio subaccounts for this.
              a. subaccount A (any account)
              b. subaccount B (for the account where you want to verify the callerId)
            2. Buy a twilio number and enable call forwarding in a number bought via subaccount A
            3. add the subaccount B account sid and auth token in the test app.
            4. add the phone number bought via subaccount A in the number field for validation request in testApp
            5. run the sbt command :  sbt "coldemail/runMain utils.TestApp twilioTesting_verify_callerId"
            6. You will receive a call in the forwarding number set. Verify the callerId with the code getting
            displayed in the command prompt.
            7. Once the callerId is verified successfully, you'll hear the confirmation message in the call.
            8. Now you can place calls from the number bought via subaacount A, from the setup of subaccount B.

            basecamp link for the above:
            https://3.basecamp.com/5674686/buckets/********/messages/**********#__recording_7939869734
         */

          object main {


            implicit val system: ActorSystem = ActorSystem()
            implicit val materializer: Materializer = Materializer(system) // Create Materializer directly
            implicit val executionContext: ExecutionContextExecutor = system.dispatcher


            def init()(implicit ec: ExecutionContext): Future[Unit] = Future {


              val ACCOUNT_SID = "ACCOUNT_SID"
              val AUTH_TOKEN = "AUTH_TOKEN"

              Twilio.init(ACCOUNT_SID, AUTH_TOKEN)


              val validationRequest: ValidationRequest =
                ValidationRequest.creator(new PhoneNumber("phone number"))
                  .setFriendlyName("some number")
                  .create();

              println(s"validation code --> ${validationRequest.getValidationCode}")


            }

          }
          implicit val ec: ExecutionContextExecutor = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(20))

          val res = main.init()
          Await.result(res, scala.concurrent.duration.Duration.Inf)


        case "twilioTesting_call" =>

          object main {

            import java.net.URI

            def init()(implicit ec: ExecutionContext): Future[Unit] = Future {
              val ACCOUNT_SID = "TWILIO_ACCOUNT_SID"
              val AUTH_TOKEN = "TWILIO_AUTH_TOKEN"

              Twilio.init(ACCOUNT_SID, AUTH_TOKEN)

              val from = new PhoneNumber("TWILIO_NUMBER") // twilio account number
              //          val to_shashank = new PhoneNumber("SHASHANK_NUMBER") // who are we texting Shashank
              val to_tulika = new PhoneNumber("+TULIKA_NUMBER") // who are we texting Tulika
              //          val body = "hi how are you?" //readLine("What do you want to say? ")

              //          val message = Message.creator(to, from, body).create()
              val call = Call.creator(to_tulika, from, URI.create("http://demo.twilio.com/docs/classic.mp3")).create()

              //          println(s"Message sent to $to with ID ${message.getSid}")
              println(s"Call went to $to_tulika with ID ${call.getSid}")

            }

          }
          implicit val ec: ExecutionContextExecutor = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(20))

          val res = main.init()
          Await.result(res, scala.concurrent.duration.Duration.Inf)

        case "twilio_trigger_creation" =>


          object main {

            import java.net.URI

            def init()(implicit ec: ExecutionContext): Future[Unit] = Future {
              val ACCOUNT_SID = "TWILIO_SUB_ACCOUNT_SID"
              val AUTH_TOKEN = "TWILIO_SUB_ACCOUNT_AUTH_TOKEN"

              Twilio.init(ACCOUNT_SID, AUTH_TOKEN)

              val trigger = TwilioTrigger
                .creator(
                  new URI("https://delete-me.com"),
                  "+1",
                  TwilioTrigger.UsageCategory.TOTALPRICE
                )
                .setFriendlyName("Subaccount Stoppage +1")
                //                .setRecurring(TwilioTrigger.Recurring.MONTHLY)
                .setTriggerBy(TwilioTrigger.TriggerField.PRICE)
                .create()

              val res = TwilioUsageTrigger(
                trigger_id = trigger.getSid,
                trigger_name = trigger.getFriendlyName,
                trigger_webhook = trigger.getCallbackUrl.toString,
                trigger_value = trigger.getTriggerValue,
                trigger_current_value = trigger.getCurrentValue,
                trigger_recurring = "recurring", //trigger.getRecurring.toString,
                trigger_by = trigger.getTriggerBy.toString,
                trigger_usage_category = trigger.getUsageCategory.toString,
                trigger_last_fired = DateTime.now(), //DateTime.parse(trigger.getDateFired.toLocalDate.toString), // Fixme call
                sub_account_sid = "Hi, I'm good Boy"
              )


              println(s"triggerId : -> ${trigger.getSid} triggerData : $trigger res => $res")


              //          println(s"Message sent to $to with ID ${message.getSid}")
              //              println(s"Call went to $to_tulika with ID ${call.getSid}")

            }

          }
          implicit val ec: ExecutionContextExecutor = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(20))

          val res = main.init()
          Await.result(res, scala.concurrent.duration.Duration.Inf)



        case "twilio_usage_details_of_subaccount" =>


          object main {

            //            import java.net.URI

            def init()(implicit ec: ExecutionContext): Future[Unit] = Future {
              val ACCOUNT_SID = "$TWILIO-SUBACCOUNT-SID"
              val AUTH_TOKEN = "$TWILIO-AUTH-TOKEN"
              val from_time = DateTime.now().minusMonths(1)

              Twilio.init(ACCOUNT_SID, AUTH_TOKEN)

              val usage = Record.reader()
                .setCategory(Record.Category.TOTALPRICE)
                .setStartDate(LocalDate.of(from_time.getYear, from_time.getMonthOfYear, from_time.getDayOfMonth))
                .setEndDate(LocalDate.now())
                .limit(1)
                .read()

              var usageDetail: List[UsageDetails] = List()

              usage.forEach(usageDetails => {


                val usage_of_subAccount = UsageDetails(
                  sub_account_sid = TwlSubAccountSid(id = usageDetails.getAccountSid),
                  total_usage_in_cents = usageDetails.getUsage.toFloat.toLong,
                  usage_unit = CurrencyType.fromKey(usageDetails.getUsageUnit).get,
                  usage_from = DateTime.parse(usageDetails.getStartDate().toString),
                  usage_till = DateTime.parse(usageDetails.getAsOf())
                )

                usageDetail = usageDetail ++ List(usage_of_subAccount)


              })


              println(s"usageDetails : -> ${usageDetail.head}")


              //          println(s"Message sent to $to with ID ${message.getSid}")
              //              println(s"Call went to $to_tulika with ID ${call.getSid}")

            }

          }
          implicit val ec: ExecutionContextExecutor = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(20))

          val res = main.init()
          Await.result(res, scala.concurrent.duration.Duration.Inf)


        //        case "twilio_updating_credit" =>
        //
        //          object main extends
        //                CallServiceDI
        //                with CallDAO_DI
        //                with SrUuidUtilsDI
        //                with TwilioDialerServiceDI
        //                with ProspectAddEventDAO_DI
        //                with PusherServiceDI
        //                with OrganizationDAO_DI
        //                with ProspectDAO_DI
        //                with ResetUserCacheUtilDI
        //                with AccountDAO_DI
        //                with CacheServiceDI
        //                with AccountServiceDI
        //          {
        //
        ////            import java.net.URI
        //
        //            def init(): Try[Unit] = Try {
        //
        //              given logger: SRLogger = new SRLogger("[MqTestApp Logger]")
        //
        //              val result = callService.updateSubAccountCredit(
        //                sub_account_uuid = SubAccountUuid(uuid = "sub_account_2RHGwX7F4VEd1LxePp4gEqmjBBC"),
        //                org_id = OrgId(id = 20),
        //              )
        ////              val ACCOUNT_SID = "$TWILIO-SUBACCOUNT-SID"
        ////              val AUTH_TOKEN = "$TWILIO-AUTH-TOKEN"
        ////              val from_time = DateTime.now().minusMonths(1)
        ////
        ////              Twilio.init(ACCOUNT_SID, AUTH_TOKEN)
        ////
        ////              val usage = Record.reader()
        ////                .setCategory(Record.Category.TOTALPRICE)
        ////                .setStartDate(LocalDate.of(from_time.getYear, from_time.getMonthOfYear, from_time.getDayOfMonth))
        ////                .setEndDate(LocalDate.now())
        ////                .limit(1)
        ////                .read()
        ////
        ////              var usageDetail: List[UsageDetails] = List()
        ////
        ////              usage.forEach(usageDetails => {
        ////
        ////
        ////                val usage_of_subAccount = UsageDetails(
        ////                  sub_account_sid = usageDetails.getAccountSid,
        ////                  total_usage = usageDetails.getUsage.toLong,
        ////                  usage_unit = usageDetails.getUsageUnit,
        ////                  usage_from = DateTime.parse(usageDetails.getStartDate().toString),
        ////                  usage_till = DateTime.parse(usageDetails.getAsOf())
        ////                )
        ////
        ////                usageDetail = usageDetail ++ List(usage_of_subAccount)
        ////
        ////
        ////              })
        //
        //
        //              println(s"usageDetails : -> $result")
        //
        //
        //              //          println(s"Message sent to $to with ID ${message.getSid}")
        //              //              println(s"Call went to $to_tulika with ID ${call.getSid}")
        //
        //            }
        //
        //          }
        ////          implicit val ec = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(20))
        //
        //          val res = main.init()
        //          print(s"res -> $res")
        ////          Await.result(res, scala.concurrent.duration.Duration.Inf)
        //
        //        case "twilio_trigger_webhook_testing" =>
        //
        //          object main extends
        //            CallServiceDI
        //            with CallDAO_DI
        //            with SrUuidUtilsDI
        //            with ProspectAddEventDAO_DI
        //            with PusherServiceDI
        //            with TwilioDialerServiceDI
        //            with OrganizationDAO_DI
        //            with ProspectDAO_DI
        //          {
        //
        //            import java.net.URI
        //
        //            def init(): Try[Unit] = Try {
        //
        //              given logger: SRLogger = new SRLogger("[MqTestApp Logger]")
        //
        //              val twilio_usage_limit_crossed_details = TwilioUsageLimitCrossedDetails(
        //                 twl_sub_account_sid = TwlSubAccountSid(
        //                   id = "$Twilio_SubAccount_Sid"
        //                 ),
        //                 twl_usage_trigger_sid = "", // Fixme use uuid
        //                 date_fired = DateTime.now(),
        //                 trigger_value = 300,
        //                 current_value = 450,
        //                 idempotency_token = "hi there ", // no use can store and match if its different than previous one, then only proceed ahead
        //               )
        //
        //              val result = callService.handleUsageLimitCrossedWebhook(
        //                webhook_details = twilio_usage_limit_crossed_details,
        //                org_id = OrgId(id = 20),
        //              )
        //
        //
        //              println(s"usageDetails : -> ${result}")
        //
        //
        //              //          println(s"Message sent to $to with ID ${message.getSid}")
        //              //              println(s"Call went to $to_tulika with ID ${call.getSid}")
        //
        //            }
        //
        //          }
        //          //          implicit val ec = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(20))
        //
        //          val res = main.init()
        //          print(s"res -> ${res}")
        //        //          Await.result(res, scala.concurrent.duration.Duration.Inf)
        //        case "twilio_add_credit" =>
        //
        //          object main extends
        //            CallServiceDI
        //            with CallDAO_DI
        //            with SrUuidUtilsDI
        //            with TwilioDialerServiceDI
        //            with ProspectAddEventDAO_DI
        //            with PusherServiceDI
        //            with OrganizationDAO_DI
        //            with ProspectDAO_DI
        //
        //          {
        //
        //            import java.net.URI
        //
        //            def init(): Try[Unit] = Try {
        //
        //              given logger: SRLogger = new SRLogger("[MqTestApp Logger]")
        //
        ////              val twilio_usage_limit_crossed_details = TwilioUsageLimitCrossedDetails(
        ////                twl_sub_account_sid = TwlSubAccountSid(
        ////                  id = "$Twilio_SubAccount_Sid"
        ////                ),
        ////                twl_usage_trigger_sid = "", // Fixme use uuid
        ////                date_fired = DateTime.now(),
        ////                trigger_value = "+30",
        ////                current_value = "+45",
        ////                idempotency_token = "hi there ", // no use can store and match if its different than previous one, then only proceed ahead
        ////              )
        //
        //              val new_credit = AddCallingCreditDetails(
        //                org_id =  OrgId(id = 20),
        //                credit_added = 40,
        //                credit_unit = "USD",
        //                credit_added_at = DateTime.now()
        //              )
        //
        //              val  result = callService.addCallingCredits(
        //                credit_details =  new_credit,
        //                org_id = OrgId(id = 20),
        //              )
        //
        //
        //              println(s"usageDetails : -> ${result}")
        //
        //
        //              //          println(s"Message sent to $to with ID ${message.getSid}")
        //              //              println(s"Call went to $to_tulika with ID ${call.getSid}")
        //
        //            }
        //
        //          }
        //          //          implicit val ec = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(20))
        //
        //          val res = main.init()
        //          print(s"res -> ${res}")
        //        //          Await.result(res, scala.concurrent.duration.Duration.Inf)
        //
        //        case "create_sub_account_api_key" =>
        //
        //          object main extends
        //            CallServiceDI
        //            with CallDAO_DI
        //            with SrUuidUtilsDI
        //            with ProspectAddEventDAO_DI
        //            with PusherServiceDI
        //            with TwilioDialerServiceDI
        //            with OrganizationDAO_DI
        //            with ProspectDAO_DI
        //          {
        //
        //            import java.net.URI
        //
        //            def init(): Try[Unit] = Try {
        //
        //              given logger: SRLogger = new SRLogger("[MqTestApp Logger]")
        //
        //              //              val twilio_usage_limit_crossed_details = TwilioUsageLimitCrossedDetails(
        //              //                twl_sub_account_sid = TwlSubAccountSid(
        //              //                  id = "$Twilio_SubAccount_Sid"
        //              //                ),
        //              //                twl_usage_trigger_sid = "", // Fixme use uuid
        //              //                date_fired = DateTime.now(),
        //              //                trigger_value = "+30",
        //              //                current_value = "+45",
        //              //                idempotency_token = "hi there ", // no use can store and match if its different than previous one, then only proceed ahead
        //              //              )
        //
        //              val new_credit = AddCallingCreditDetails(
        //                org_id = OrgId(id = 20),
        //                credit_added = 40,
        //                credit_unit = "USD",
        //                credit_added_at = DateTime.now()
        //              )
        //
        //              val result = twilioDialerService.createApiKeySecretForSubAccount(
        //                sub_account_sid = TwlSubAccountSid(id = "$TwimlSubAccountSID"),
        //                sub_account_auth_token = TwlAuthToken(token = "TwlAuthToken"),
        //                sub_account_uuid = SubAccountUuid(uuid = "uuid")
        //              )
        //
        //              result match {
        //
        //                case Failure(e) =>
        //
        //                  println(s"\n\nerrror -> ${e}\n\n")
        //
        //
        //
        //                case Success(data) =>
        //
        //                  println(data)
        //              }
        //
        //
        ////              println(s"usageDetails : -> ${result}")
        //
        //
        //              //          println(s"Message sent to $to with ID ${message.getSid}")
        //              //              println(s"Call went to $to_tulika with ID ${call.getSid}")
        //
        //            }
        //
        //          }
        //          //          implicit val ec = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(20))
        //
        //          val res = main.init()
        //          print(s"res -> ${res}")
        //        //          Await.result(res, scala.concurrent.duration.Duration.Inf)
        //
        //        case "webhook_security_testing" =>
        //
        //          object main extends
        //            CallServiceDI
        //            with CallDAO_DI
        //            with SrUuidUtilsDI
        //            with ProspectAddEventDAO_DI
        //            with PusherServiceDI
        //            with TwilioDialerServiceDI
        //            with OrganizationDAO_DI
        //            with ProspectDAO_DI
        //          {
        //
        //            import java.net.URI
        //
        //            def init(): Try[Unit] = Try {
        //
        //              given logger: SRLogger = new SRLogger("[MqTestApp Logger]")
        //
        //              val has_params = new util.HashMap[String, String]()
        //
        //              val twl_auth_token = "Twl_Auth_token"
        //
        //              has_params.put("CallSid", "CAde0eea1c5d158b098c6b27199e7f9bf")
        //              has_params.put("Caller", "client%3Aphone_number_2SA1jweUzVz0xRN7yazzM3P6QIK") // done
        //              has_params.put("Called", "")
        //              has_params.put("ApplicationSid", "AP3a876ce7039aa5912644006183a5629b")
        //              has_params.put("To", "%2B************")
        //              has_params.put("CallStatus", "ringing")
        //              has_params.put("AccountSid", "**********************************")
        //              has_params.put("From", "client%3Aphone_number_2SA1jweUzVz0xRN7yazzM3P6QIK")
        //              has_params.put("ApiVersion", "2010-04-01")
        //              has_params.put("Direction", "inbound")

        //              val auth_token =  TwlAuthToken(token = "TWL_AUTH_TOKEN")

        //              val result: Try[Boolean] = twilioDialerService.authenticateTwilioWebhook(
        //                sub_account_auth_token = TwlAuthToken(token = "TWL_AUTH_TOKEN"),
        //                params = has_params,
        //                webhook_url = "https://devapi2.sreml.com/api/v2/voice/twilio/create",
        //                twilio_webhook_signature  = "0DKWpIsxKXXpHDLJDzFWXeRBDoQ="
        //              )

        //              val url = "https://example.com/myapp"

        // Store the application/x-www-form-urlencoded parameters from Twilio's request as a variable
        // In practice, this MUST include all received parameters, not a
        // hardcoded list of parameters that you receive today. New parameters
        // may be added without notice.
        //              val params = new util.HashMap[String, String]();
        //              params.put("CallSid", "*****************")
        //              params.put("Caller", "+***********")
        //              params.put("Digits", "1234")
        //              params.put("From", "+***********")
        //              params.put("To", "+18005551212")

        // Store the X-Twilio-Signature header attached to the request as a variable
        //              val twilioSignature = "W9hNMVs4Nk0MIPS0fuPTIEOXVlU="


        //              val request_validator = new RequestValidator(twl_auth_token)

        //              logger.info("[WebhookAuthenticator] validating twilio webhook url twilioDialerService")
        // Check if the incoming signature is valid for your application URL and the incoming parameters
        //              val res = request_validator.validate(url, params, twilioSignature)
        //              val res = request_validator.validate(
        //                "https://devapi2.sreml.com/api/v2/voice/twilio/create"
        //                , has_params
        //                , twilioSignature
        //              )

        //              logger.debug(s"\nres after validation: ${res}\n")
        //              logger.debug(s"\nres2 after validation: ${res}\n")

        //              val base_url = "http://9bd4-2401-4900-1f3d-54c0-1028-a20c-b2e6-2dad.ngrok.io"
        //
        //              val request_validator_java = new RequestValidatorJava(
        //                twl_auth_token
        //              )

        /*

        val twl_auth_token = "6aed691b5e248f474f8e7b66981133ca"

         has_params.put("CallSid", "CAde0eea1c5d158b098c6b27199e7f9bf")
         has_params.put("Caller", "client%3Aphone_number_2SA1jweUzVz0xRN7yazzM3P6QIK") // done
         has_params.put("Called", "")
         has_params.put("ApplicationSid", "AP3a876ce7039aa5912644006183a5629b")
         has_params.put("To", "%2B************")
         has_params.put("CallStatus", "ringing")
         has_params.put("AccountSid", "**********************************")
         has_params.put("From", "client%3Aphone_number_2SA1jweUzVz0xRN7yazzM3P6QIK")
         has_params.put("ApiVersion", "2010-04-01")
         has_params.put("Direction", "inbound")

         when url => "https://9bd4-2401-4900-1f3d-54c0-1028-a20c-b2e6-2dad.ngrok.io/api/v2/voice/twilio/create"

        string a => rD5fO610A2flWyl6w1wtWGlkVpQ=
        string b => W9hNMVs4Nk0MIPS0fuPTIEOXVlU=
        string a => Zeps++kmxoTzKFRwVYnzsz0TiNk=
        string b => W9hNMVs4Nk0MIPS0fuPTIEOXVlU=

        url -> http://9bd4-2401-4900-1f3d-54c0-1028-a20c-b2e6-2dad.ngrok.io/api/v2/voice/twilio/create
        string a => WydDB8Ow1ivy0cYZQizpS8gqCMo=
        string b => W9hNMVs4Nk0MIPS0fuPTIEOXVlU=
                ----
        string a => AKNCHCjGDmAqKR0z9s4tkPuBVdE=
        string b => W9hNMVs4Nk0MIPS0fuPTIEOXVlU=


         */

        //              request_validator_java.validate(
        //                base_url + "/api/v2/voice/twilio/create"
        //                , has_params
        //                , twilioSignature
        ////              )
        //              println(1)
        //
        //            }


        //              val result2: Try[Boolean] = twilioDialerService.authenticateTwilioWebhook(
        //                sub_account_auth_token = TwlAuthToken(token = "TWILIO_AUTH_TOKEN"),
        //                params = params,
        //                webhook_url = "https://example.com/myapp",
        //                twilio_webhook_signature = twilioSignature
        //              )


        //              val twilio_usage_limit_crossed_details = TwilioUsageLimitCrossedDetails(
        //                twl_sub_account_sid = TwlSubAccountSid(
        //                  id = "$Twilio_SubAccount_Sid"
        //                ),
        //                twl_usage_trigger_sid = "", // Fixme use uuid
        //                date_fired = DateTime.now(),
        //                trigger_value = "+30",
        //                current_value = "+45",
        //                idempotency_token = "hi there ", // no use can store and match if its different than previous one, then only proceed ahead
        //              )

        //              val new_credit = AddCallingCreditDetails(
        //                org_id = OrgId(id = 20),
        //                credit_added = 40,
        //                credit_unit = "USD",
        //                credit_added_at = DateTime.now()
        //              )

        //              val result = twilioDialerService.createApiKeySecretForSubAccount(
        //                sub_account_sid = TwlSubAccountSid(id = "$TwimlSubAccountSID"),
        //                sub_account_auth_token = TwlAuthToken(token = "TwlAuthToken"),
        //                sub_account_uuid = SubAccountUuid(uuid = "uuid")
        //              )

        //              result match {
        //
        //                case Failure(e) =>
        //
        //                  logger.error(s"\n\nerrror -> ${e}\n\n",e)
        //
        //
        //                case Success(data) =>
        //
        //                  logger.debug(s"validated successfully : ${data}")
        //
        ////                  data
        //              }


        //              println(s"usageDetails : -> ${result}")


        //          println(s"Message sent to $to with ID ${message.getSid}")
        //              println(s"Call went to $to_tulika with ID ${call.getSid}")

        //            }
        //          implicit val ec = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(20))

        //          val res = main.init()
        //          print(s"res -> ${res}")
        //          Await.result(res, scala.concurrent.duration.Duration.Inf)
        case "testing_conference" =>

          val test = Try {

            val ACCOUNT_SID = "TWL_ACCOUNT_SID"
            val AUTH_TOKEN = "TWL_AUTH_TOKEN"

            Twilio.init(ACCOUNT_SID, AUTH_TOKEN)

            //        val from = new PhoneNumber("TWILIO_NUMBER") // twilio account number
            //        val to_shashank = new PhoneNumber("SHASHANK_NUMBER") // who are we texting Shashank
            //        val to_tulika = new PhoneNumber("+TULIKA_NUMBER") // who are we texting Tulika
            //        val body = "hi how are you?" //readLine("What do you want to say? ")

            //          val message = Message.creator(to, from, body).create()
            //        val call = Call.creator(to_tulika, from, URI.create("http://demo.twilio.com/docs/classic.mp3")).create()

            val conference_name = "Test_Conference"

            val conference = new Conference
            .Builder(conference_name)
              .startConferenceOnEnter(true)

            // outbound calls logic
            val dialBuilder = new Dial.Builder().conference(
              conference.build()
            )
            //          .callerId("+***********")
            // System.getenv("TWILIO_CALLER_ID")
            //      val dialBuilderWithReceiver = addChildReceiver(dialBuilder, call_details.to.phone_number)
            val res = new VoiceResponse.Builder().dial(dialBuilder.build).build.toXml()

            //Expectation:
            /*
              transfer testing:
              call from customer to twilio then I wanted to transfer that call to someone else

              * addNewParticipant
              * deleteCurrentParticipant

             */

            // Animesh
            val participant_a: Participant =
              Participant
                .creator(conference_name,
                  new PhoneNumber("+19896569228"), // user-1
                  //              new PhoneNumber("+919404809192")
                  new PhoneNumber("+919404809192") // Prachi number prospect
                )
                .setLabel("animesh")
                .setEarlyMedia(true)
                .setBeep("onEnter")
                .setEndConferenceOnExit(true)
                .setStatusCallback(URI.create("https://conference.requestcatcher.com/test"))
                //          .setStatusCallbackEvent(util.Arrays.asList("ringing"))
                .setRecord(true).create()

            logger.info(s" \nparticipants_a joined the call [ Animesh ]\n ")


            val part_b: Participant =
              Participant
                .creator(conference_name,
                  new PhoneNumber("+19896569228"), // user-1
                  //              new PhoneNumber("+919404809192")
                  new PhoneNumber("+************") // Prateek number
                )
                .setLabel("prateek")
                .setEarlyMedia(true)
                .setBeep("onEnter")
                //            .setEndConferenceOnExit(true)
                .setStatusCallback(URI.create("https://conference.requestcatcher.com/test"))
                //          .setStatusCallbackEvent(util.Arrays.asList("ringing"))
                .setRecord(true).create()


            //                logger.info(s"[Conference] conference_id : ${participant_a.getConferenceSid} voice response: ${res} : ")


            logger.info(s"\n participants_b joined the call [ Prateek ] \n  Animesh And Prateek Can talk to each other \n")

            Thread.sleep(40000)

            // removed shashank
            logger.info(s"\n removed shasahnk \n  adding tulika \n")


            Participant.deleter(
              part_b.getConferenceSid,
              part_b.getCallSid
            ).delete()

            logger.info("\n30 seconds passed adding participant_3 [ Shashank ]  in call-listening mode\n")

            // call-listening
            // shashank will only listen
            val user_3: Participant =
              Participant
                .creator(conference_name,
                  new PhoneNumber("+19896569228"), // user-1
                  //                            new PhoneNumber("client:phone_number_2SjdmkVUNIdV6poQ1Gw6eYeScal") // listener
                  new PhoneNumber("+917300933129")
                  //              new PhoneNumber("+919404809192") PRACHI
                )
                .setLabel("ShashankExtension")
                .setEarlyMedia(true)
                //              .setMuted(true)
                .setBeep("onEnter")
                .setStatusCallback(URI.create("https://conference.requestcatcher.com/test"))
                //          .setStatusCallbackEvent(util.Arrays.asList("ringing"))
                .setRecord(true).create()

            //        Thread.sleep(15000)

            //        logger.info("\n15 seconds have passed changing shashank from listening to whisper mode " +
            //          "\n Prateeek will hear shashank but not animesh, and shasahnk will hear both prateek and animesh")

            // Call_listening - after 12 sec

            // no one would be able to hear me.

            /*
              1.    call_1   2.
              1.    call_2   3.

              delete


             */

            //        val call_whispering = Participant
            //          .updater(user_3.getConferenceSid,
            //            user_3.getCallSid)
            //          .setMuted(false)
            //          .setCoaching(true)
            //          .setCallSidToCoach(part_b.getCallSid)
            //          .update
            //
            //        Thread.sleep(15000)
            //
            //        logger.info("\n15 seconds have passed changing shashank in Barge-in mode " +
            //          "\n Prateeek and Animesh will hear shashank \n ")
            //
            //
            //        val call_listening = Participant
            //          .updater(user_3.getConferenceSid,
            //            user_3.getCallSid)
            //          .setCoaching(false)
            //          .setMuted(false)
            //          .update


            // Call whispering - after 12 sec

            // only Prateek will be able to here me


            // Shashank


            //        logger.info(s" participants_b id : ${participant_b} ")

            //        Thread.sleep(15000)
            //        val participant_c: Participant =
            //          Participant
            //            .creator(conference_name,
            //              new PhoneNumber("+***********"),
            //              new PhoneNumber("+************")
            //              //              new PhoneNumber("+919545266389")
            //            )
            //            .setLabel("customer-3")
            //            .setEarlyMedia(true)
            //            .setCoaching(true)
            //            .setCallSidToCoach(participant_b.getCallSid)
            //            .setBeep("onEnter")
            //            .setStatusCallback(URI.create("https://conference.requestcatcher.com/test"))
            //            //          .setStatusCallbackEvent(util.Arrays.asList("ringing"))
            //            .setRecord(true).create()
            //
            //        logger.info(s" participants_c id : ${participant_c} ")

          }
        //
        //          test match {
        //
        //            case Failure(er) =>
        //
        //              logger.error("\n\nerror while testing conference\n\n", er)
        //
        //
        //            case Success(data) =>
        //
        //              logger.info(s"\n successfully created conference \n")
        //
        //          }
        //        case "testing_call_notes_save" =>
        //
        //          object main extends CallServiceDI
        //            with CallDAO_DI
        //            with SrUuidUtilsDI
        //            with ProspectAddEventDAO_DI
        //            with PusherServiceDI
        //            with TwilioDialerServiceDI
        //            with OrganizationDAO_DI
        //            with ProspectDAO_DI
        //          {
        //
        //            def init() = {
        //
        //              val call_sid = CallSID(sid = "call_sid")
        //              val call_note = CallNote(note = "notes")
        //
        //              callService.updateCallNote(
        //                call_note = call_note,
        //                call_sid = call_sid,
        //                team_id = TeamId(id = 20)
        //              ) match {
        //                case Left(err) =>
        //
        //                  println("\nerr :",err)
        //
        //                case Right(d) =>
        //
        //                  println("\n sucess :",d.note)
        //
        //              }
        //
        //            }
        //
        //          }
        //
        //          val res = main.init()
        //          println("main : ", res)
        //
        //
        //        case "call_feedback" =>
        //
        //          object main extends CallServiceDI
        //            with CallDAO_DI
        //            with SrUuidUtilsDI
        //            with TwilioDialerServiceDI
        //            with ProspectAddEventDAO_DI
        //            with PusherServiceDI
        //            with OrganizationDAO_DI
        //            with ProspectDAO_DI
        //          {
        //
        //            def init() = {
        //
        //              val call_sid = CallSID(sid = "call_sid")
        //              val call_note = CallNote(note = "notes")
        //
        //              val feedback_reason = List(CallFeedBackReasons.DroppedCall, CallFeedBackReasons.UnSolicitedCall, CallFeedBackReasons.PostDialDelay)
        //
        //              val call_feedback = CallFeedBack(
        //                call_sid = call_sid,
        //                feedback_type =  CallFeedBackType.Positive,
        //                feedback_reason =  feedback_reason,
        //                user_comment =  Some("")
        //              )
        //
        //              callService.saveCallFeedBack(
        //                call_feedback = call_feedback,
        //                call_sid = call_sid,
        //                team_id = TeamId(id = 20)
        //              ) match {
        //                case Left(err) =>
        //
        //                  println("\nerr :", err)
        //
        //                case Right(d) =>
        //
        //                  println("\n sucess :", d)
        //
        //              }
        //
        //            }
        //
        //          }
        //
        //          val res = main.init()
        //          println("main : ", res)
        //

        //        case "call_participant_mode_update" =>
        //
        //          object main extends
        //            CallServiceDI
        //            with CallDAO_DI
        //            with SrUuidUtilsDI
        //            with TwilioDialerServiceDI
        //            with ProspectAddEventDAO_DI
        //            with PusherServiceDI
        //            with OrganizationDAO_DI
        //            with ProspectDAO_DI
        //          {
        //            def init(): Try[Unit] = Try {
        //
        //              val result = callService.updateCallParticipantMode(
        //                org_id = OrgId(20L),
        //                teamId = TeamId(15L),
        //                participant_account_id = AccountId(35L),
        //                prospect_id_in_conference = Some(ProspectId(8429L)),
        //                new_participant_mode = CallParticipationMode.WhisperMode
        //              )
        //
        //
        //              println(s"Update Result : -> ${result}")
        //
        //            }
        //
        //          }
        //
        //          main.init()
        //
        //          test match {
        //
        //            case Failure(er) =>
        //
        //              logger.error("\n\nerror while testing conference\n\n", er)
        //
        //
        //            case Success(data) =>
        //
        //              logger.info(s"\n successfully created conference \n")
        //
        //          }
        //        case "testing_call_notes_save" =>
        //
        //          object main extends CallServiceDI
        //            with CallDAO_DI
        //            with SrUuidUtilsDI
        //            with ProspectAddEventDAO_DI
        //            with PusherServiceDI
        //            with TwilioDialerServiceDI
        //            with OrganizationDAO_DI
        //            with ProspectDAO_DI
        //          {
        //
        //            def init() = {
        //
        //              val call_sid = CallSID(sid = "call_sid")
        //              val call_note = CallNote(note = "notes")
        //
        //              callService.updateCallNote(
        //                call_note = call_note,
        //                call_sid = call_sid,
        //                team_id = TeamId(id = 20)
        //              ) match {
        //                case Left(err) =>
        //
        //                  println("\nerr :",err)
        //
        //                case Right(d) =>
        //
        //                  println("\n sucess :",d.note)
        //
        //              }
        //
        //            }
        //
        //          }
        //
        //          val res = main.init()
        //          println("main : ", res)



        //        case "twilio_subaccount_creation" =>
        //
        //
        //          object main {
        //
        ////            import java.net.URI
        //
        //            def init()(implicit ec: ExecutionContext): Future[Unit] = Future {
        //              val ACCOUNT_SID = "TWILIO_ACCOUNT_SID"
        //              val AUTH_TOKEN = "TWILIO_AUTH_TOKEN"
        //
        //              Twilio.init(ACCOUNT_SID, AUTH_TOKEN)
        //
        //              val account = Account.creator
        //                //      .setFriendlyName("Submarine")  //
        //                .create()
        //
        //              println(s"account created account : $account sid:  ${account.getSid}")
        //
        //
        //              //          println(s"Message sent to $to with ID ${message.getSid}")
        ////              println(s"Call went to $to_tulika with ID ${call.getSid}")
        //
        //            }
        //
        //          }
        //          implicit val ec = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(20))
        //
        //          val res = main.init()
        //          Await.result(res, scala.concurrent.duration.Duration.Inf)


        //        case "call_feedback" =>
        //
        //          object main extends CallServiceDI
        //            with CallDAO_DI
        //            with SrUuidUtilsDI
        //            with TwilioDialerServiceDI
        //            with ProspectAddEventDAO_DI
        //            with PusherServiceDI
        //            with OrganizationDAO_DI
        //            with ProspectDAO_DI
        //          {
        //
        //            def init() = {
        //
        //              val call_sid = CallSID(sid = "call_sid")
        //              val call_note = CallNote(note = "notes")
        //
        //              val feedback_reason = List(CallFeedBackReasons.DroppedCall, CallFeedBackReasons.UnSolicitedCall, CallFeedBackReasons.PostDialDelay)
        //
        //              val call_feedback = CallFeedBack(
        //                call_sid = call_sid,
        //                feedback_type =  CallFeedBackType.Positive,
        //                feedback_reason =  feedback_reason,
        //                user_comment =  Some("")
        //              )
        //
        //              callService.saveCallFeedBack(
        //                call_feedback = call_feedback,
        //                call_sid = call_sid,
        //                team_id = TeamId(id = 20)
        //              ) match {
        //                case Left(err) =>
        //
        //                  println("\nerr :", err)
        //
        //                case Right(d) =>
        //
        //                  println("\n sucess :", d)
        //
        //              }
        //
        //            }
        //
        //          }
        //
        //          val res = main.init()
        //          println("main : ", res)
        //
        //
        //        case "call_participant_mode_update" =>
        //
        //          object main extends
        //            CallServiceDI
        //            with CallDAO_DI
        //            with SrUuidUtilsDI
        //            with TwilioDialerServiceDI
        //            with ProspectAddEventDAO_DI
        //            with PusherServiceDI
        //            with OrganizationDAO_DI
        //            with ProspectDAO_DI
        //          {
        //            def init(): Try[Unit] = Try {
        //
        //              val result = callService.updateCallParticipantMode(
        //                org_id = OrgId(20L),
        //                teamId = TeamId(15L),
        //                participant_account_id = AccountId(35L),
        //                prospect_id_in_conference = Some(ProspectId(8429L)),
        //                new_participant_mode = CallParticipationMode.WhisperMode
        //              )
        //
        //
        //              println(s"Update Result : -> ${result}")
        //
        //            }
        //
        //          }
        //
        //          main.init()
        case "test_incoming_calls_notification" =>
          trait ActorMaterializerDI {

            // actorSystem is initialized in play BuiltinComponents
            val actorSystem: ActorSystem

            val  actorMaterializer = Materializer(actorSystem)
          }

          object main extends TestAppTrait {

            //              implicit lazy val playDefaultExecutionContext: ExecutionContext = controllerComponents.executionContext

            //              override lazy val dynamicEvolutions = new DynamicEvolutions

            //              override lazy val httpFilters: Seq[EssentialFilter] = filters.filters

            //              override val scyllaCluster: Cluster = scyllaDbConnection.initialize()

            //              val playDefaultExecutionContext: scala.concurrent.ExecutionContext = ???
            given  executionContext: ExecutionContext = workerActorSystem.dispatcher
            given  logger2: SRLogger = new SRLogger("testing warning sending emails")


            //              applicationLifecycle.addStopHook { () =>
            //                logger.info("The app is about to stop")
            //                DBs.closeAll()
            //                scyllaCluster.close()
            //                Future.successful(())
            //              }
            def init() = {

              //                val org_call_warning_details = OrgCallWarningErrorDetails(
              //                  warning_message = "calling features suspended, add more credits to start using again",
              //                  warning_at = DateTime.now(),
              //                  warning_code = AccountWarningCodeType.CallingFeatureSuspended
              //                )
              val sub_account_uuid = SubAccountUuid(
                uuid = "sub_account_2XZd2NfFg4F768FyyJAGThWUM0l"
              )

              val call_logs = NewCallLog(
                sub_account_uuid = sub_account_uuid,
                team_id = Some(TeamId(id = 15)),
                uuid = CallLogUUID(uuid = "call_log_uuid_hun_main"),
                user_id = Some(AccountId(id = 17)),
                service_provider = CallingServiceProvider.TWILIO,
                call_sid = CallSID(sid = "call_sid_hun_main"),
                is_outgoing = true,
                start_time = DateTime.now(),
                end_time = DateTime.now().plusMinutes(30),
                from_number = "+************",
                to_number = "+9234562348u3",
                duration = 1800L,
                price = -400,
                price_unit = CurrencyType.USD

              )

              val res = callService.handleIncomingCall(
                call_details = CallDetails(
                  call_sid = CallSID("hi_call_sid_hun_main"),
                  is_incoming = true,
                  from = PhoneNumberValueClass(
                    phone_number = "+************"
                  ),
                  to = PhoneNumberValueClass(phone_number = "+***********"
                  ),
                  calling_device = None,
                  call_type = CallType.TWL_BASIC,
                  call_participant_data = InitialCallParticipationModeData.InitiatorModeData
                ),
                validate_data = WebhookValidateData(
                  twilio_signature = Some("TwilioSignature"),
                  webhook_url = "url",
                  params = Map()
                )
              )

              res match {

                case Left(err) =>

                  logger2.error(s"error: ${err}")

                //                    case Rig(UpdateCallLogsError.ErrorWhileAttachingTeamIdToCallLog(e)) =>
                //                      logger2.error(s"error: ",e)
                //
                //
                //                    case Left(e) =>
                //
                //                          logger2.error(s"error: ${e}")


                case Right(data) =>

                  logger.info(s"success: ${data}")

              }

              //                val res = callService.addWarningMessageInOrgCallingCredit(
              //                  call_credit_remaining = None,
              //                  org_call_warning_details =  org_call_warning_details,
              //                  org_id = OrgId(id = 20),
              //                  ws =  wsClient,
              //                  ec =  playDefaultExecutionContext,
              //                  subAccountDetails = su
              //                )(logger2)

              //                res match {
              //
              //                  case Left(err) =>
              //
              //                    logger2.info(s"\n\nerror : ${err}\n\n")
              //
              //                    1
              //
              //                  case Right(data) =>
              //
              //                    logger2.info(s"\n\ndata after execution, ${data}\n\n")
              //
              //                    2
              //                }
            }
          }

          main.init()

        case "addCallingCreditsToOrg" =>

          trait ActorMaterializerDI {

            // actorSystem is initialized in play BuiltinComponents
            val actorSystem: ActorSystem

            val  actorMaterializer = Materializer(actorSystem)
          }

          object main extends TestAppTrait {

            //              implicit lazy val playDefaultExecutionContext: ExecutionContext = controllerComponents.executionContext

            //              override lazy val dynamicEvolutions = new DynamicEvolutions

            //              override lazy val httpFilters: Seq[EssentialFilter] = filters.filters

            //            override val scyllaCluster: Cluster = scyllaDbConnection.initialize()

            implicit val  logger2: SRLogger = new SRLogger("testing warning sending emails")


            //              applicationLifecycle.addStopHook { () =>
            //                logger.info("The app is about to stop")
            //                DBs.closeAll()
            //                scyllaCluster.close()
            //                Future.successful(())
            //              }
            def init() = {

              //              val org_call_warning_details = OrgCallWarningErrorDetails(
              //                warning_message = "calling features suspended, add more credits to start using again",
              //                warning_at = DateTime.now(),
              //                warning_code = AccountWarningCodeType.CallingFeatureSuspended
              //              )

              val org_id = OrgId(id = 23)
              val credit_details = AddCallingCreditDetails(
                org_id = org_id,
                credit_added_dollar = 50,
                credit_added_cents = 5000, // cents
                credit_unit = CurrencyType.USD,
                credit_added_at = DateTime.now()
              )


              val res = callService.addCallingCredits(
                org_id = org_id,
                credit_details = credit_details
              )(logger = logger2, ec = playDefaultExecutionContext, ws = wSClient)

              println(res)

              //                val res = callService.addWarningMessageInOrgCallingCredit(
              //                  call_credit_remaining = None,
              //                  org_call_warning_details =  org_call_warning_details,
              //                  org_id = OrgId(id = 20),
              //                  ws =  wsClient,
              //                  ec =  playDefaultExecutionContext,
              //                  subAccountDetails = su
              //                )(logger2)

              //                res match {
              //
              //                  case Left(err) =>
              //
              //                    logger2.info(s"\n\nerror : ${err}\n\n")
              //
              //                    1
              //
              //                  case Right(data) =>
              //
              //                    logger2.info(s"\n\ndata after execution, ${data}\n\n")
              //
              //                    2
              //                }
            }
          }

          main.init()
      }
    }

  }
}
