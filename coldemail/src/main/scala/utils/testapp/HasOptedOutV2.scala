package utils.testapp

import api.CacheServiceJedis
import api.accounts.EmailScheduledIdOrTaskId.EmailScheduledId
import api.accounts.TeamId
import api.accounts.dao.AccountOrgBillingRelatedInfoDAO
import api.accounts.service.AccountOrgBillingRelatedService
import api.campaigns.CampaignProspectDAO
import api.campaigns.dao.{CampaignProspectDAO_2, CampaignSchedulingMetadataDAO}
import api.campaigns.services.{CampaignId, MergeTagService}
import api.columns.{ProspectColumnDef, ProspectColumnDefDAO}
import api.emails.{EmailScheduledDAO, EmailSettingDAO, EmailThreadDAO}
import api.free_email_domain.dao.FreeEmailDomainListDAO
import api.prospects.dao.{ProspectAddEventDAO, ProspectDAO, ProspectsEmailsDAO}
import api.prospects.dao_service.ProspectDAOService
import api.prospects.models.ProspectId
import api.prospects.service.ProspectServiceV2
import api.search.ProspectQuery
import api.tasks.pgDao.TaskPgDAO
import api.tasks.services.LinkedinTaskService
import api.team_inbox.dao.{ReplySentimentDAO, ReplySentimentJedisDAO}
import api.team_inbox.dao_service.ReplySentimentDAOService
import app_services.db_query_counter.SrDBQueryCounterService
import app_services.db_query_counter.dao.DBCounterDAO
import play.api.Logging
import scalikejdbc.config.DBs
import utils.SRLogger
import utils.dbutils.DBUtils
import utils.emailvalidation.EmailValidationModel
import utils.uuid.SrUuidUtils

object HasOptedOutV2 extends Logging {

  def main(args: Array[String]): Unit = {

    if (args.length > 0) {
      DBs.setupAll()

      val applicationName = args(0)

      applicationName match {

        case "test_hasOptedOutV2" =>

          object main extends TestAppTrait {

            def init() = {


              val teamId = 80L
              val Logger = new SRLogger("test_hasOptedOutV2")
              val emailScheduledId = 1112931L
              val stepId = -1L
              val traceReqId = None

              val result = emailScheduledService.hasOptedOutV2(
                Logger = Logger,
                emailScheduledId = EmailScheduledId(emailScheduledId),
//                stepId = stepId,
                traceReqId = traceReqId)
              println(result)

            }

          }
          
          main.init()
      }
    }

  }

}