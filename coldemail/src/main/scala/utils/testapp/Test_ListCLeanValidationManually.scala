package utils.testapp

import org.apache.pekko.actor.ActorSystem
import org.apache.pekko.stream.Materializer
import api.AppConfig
import api.campaigns.models.FindCampaignResultPublicApi
import play.api.libs.json.{<PERSON>s<PERSON><PERSON><PERSON>, JsSuc<PERSON>, <PERSON>son}
import play.api.libs.ws
import play.api.libs.ws.WSResponse
import play.api.libs.ws.ahc.AhcWSClient
import utils.SRLogger
import utils.emailvalidation.BatchRequestResponse
import utils.testapp.Test_TaskPgDAO.listCleanEmailValidationApi

import scala.concurrent.duration.{Duration, SECONDS}
import scala.concurrent.{Await, ExecutionContext, Future}
import scala.io.Source
import scala.util.{Failure, Success, Try}
import play.api.libs.ws.DefaultBodyWritables._


object Test_ListCLeanValidationManually {
    def main(args: Array[String]): Unit = {
        if (args.length > 0) {

            implicit lazy val actorContext: ExecutionContext = system.dispatcher
            implicit lazy val system: ActorSystem = ActorSystem()
            implicit lazy val materializer: Materializer = Materializer(system)
            implicit lazy val wSClient: AhcWSClient = AhcWSClient()

            val applicationName = args(0);

            val logger = new SRLogger("test_ListCleantest")

            applicationName match {
                case "test_creatBatchRequestForListClean" =>

                        println("test_creatBatchRequestForListClean started")

                    val url = s"https://api.listclean.xyz/v1/verify/email/{email}"

                    val final_result: Future[String] = wSClient.url(url)
                      .addHttpHeaders("X-Auth-Token" -> "API_KEY")
                      .get()
                      .flatMap(res => {


                          if (res.status == 200) {
                              println(res.json)

                              Future.successful("1")


                          } else {
                              println(res.json)
                              Future.successful("0")

                          }

                      })
                    Await.result(
                        final_result,
                        Duration.create(500, SECONDS)
                    )
                    assert(final_result.isCompleted)

                case "test_ListCleanTestForBatchEmail"=>


                    println("test_ListCleanTestForBatchEmails started")

                    val emailList=Seq("<EMAIL>","<EMAIL>","<EMAIL>")

                    val url = s"https://api.listclean.xyz/v1/verify/email/batch"
                    val postJsonData = Json.obj("emails" -> emailList)

                    val final_result: Future[String] = wSClient.url(url)
                      .addHttpHeaders("Content-type"->"application/json",
                          "X-Auth-Token" -> "API_KEY")
                      .post(Json.stringify(postJsonData))
                      .flatMap(res => {


                          if (res.status == 200) {
                              println(res.json)

                              Future.successful("1")


                          } else {
                              println(res.json)
                              Future.successful("0")

                          }

                      })
                    Await.result(
                        final_result,
                        Duration.create(500, SECONDS)
                    )
                    assert(final_result.isCompleted)

                case "test_ListCleanresults_for_list"=>
                    println("test_ListCleanfetch results started")

                    val listid="47609"

                    val url = s"https://api.listclean.xyz/v1/lists/$listid"

                    val final_result: Future[String] = wSClient.url(url)
                      .addHttpHeaders("X-Auth-Token" -> "API_KEY")
                      .get()
                      .flatMap(res => {


                          if (res.status == 200) {
                              println(res.json)

                              Future.successful("1")


                          } else {
                              println(res.json)
                              Future.successful("0")

                          }

                      })
                    Await.result(
                        final_result,
                        Duration.create(500, SECONDS)
                    )
                    assert(final_result.isCompleted)

                case "test_gettingthestatusforBatchsent_clean"=>

                    println("test_gettingthestatusforBatchsent started")

                    val url=s"https://api.listclean.xyz/v1/downloads/47609/clean?X-Auth-Token=API_KEY"




                    val final_result = wSClient.url(url)
                      .get()

                    val result:WSResponse=Await.result(
                        final_result,Duration.create(500,SECONDS)
                    )

                    if(result.status == 200){
                        val source = Source.fromURL(url)

                        val responseJson=source.getLines().drop(1).foldLeft(Json.arr()) { case (array, line) => {
                            val values = line.split(",");
                            array ++ Json.arr(
                                Json.obj(
                                    "email" -> values(0),
                                    "result" -> values(1),
                                    "reason" -> values(2)
                                )
                            )
                        }
                        }
                        source.close()
                        println(responseJson)
                        assert(true)
                    }else{
                        println(s"Error response : ${result.status},${result.body}")
                        assert(false)
                    }


                case "test_gettingthestatusforBatchsent_dirty"=>

                    println("test_gettingthestatusforBatchsent started")

                    val url=s"https://api.listclean.xyz/v1/downloads/47609/dirty?X-Auth-Token=API_KEY"





                    val final_result = wSClient.url(url)
                      .get()

                    val result:WSResponse=Await.result(
                        final_result,Duration.create(500,SECONDS)
                    )

                    if(result.status == 200){
                        val source = Source.fromURL(url)

                        val responseJson=source.getLines().drop(1).foldLeft(Json.arr()) { case (array, line) => {
                            val values = line.split(",");
                            array ++ Json.arr(
                                Json.obj(
                                    "email" -> values(0),
                                    "result" -> values(1),
                                    "reason" -> values(2)
                                )
                            )
                        }
                        }
                        source.close()
                        println(responseJson)
                        assert(true)
                    }else{
                        println(s"Error response : ${result.status},${result.body}")
                        assert(false)
                    }

                case "test_gettingthestatusforBatchsent_unknown"=>

                    println("test_gettingthestatusforBatchsent started")

                    val url=s"https://api.listclean.xyz/v1/downloads/47609/unknown?X-Auth-Token=API_KEY"





                    val final_result = wSClient.url(url)
                      .get()

                    val result:WSResponse=Await.result(
                        final_result,Duration.create(500,SECONDS)
                    )

                    if(result.status == 200){
                        val source = Source.fromURL(url)

                        val responseJson=source.getLines().drop(1).foldLeft(Json.arr()) { case (array, line) => {
                            val values = line.split(",");
                            array ++ Json.arr(
                                Json.obj(
                                    "email" -> values(0),
                                    "result" -> values(1),
                                    "reason" -> values(2)
                                )
                            )
                        }
                        }
                        source.close()
                        println(responseJson)
                        assert(true)
                    }else{
                        println(s"Error response : ${result.status},${result.body}")
                        assert(false)
                    }

                case "test_fetchingResultsForBatchSentAll"=>

                    val csvUrls = Seq(
                        "https://api.listclean.xyz/v1/downloads/47609/clean?X-Auth-Token=API_Key",
                        "https://api.listclean.xyz/v1/downloads/47609/dirty?X-Auth-Token=API_Key",
                        "https://api.listclean.xyz/v1/downloads/47609/unknown?X-Auth-Token=API_KEY"
                    )

                    val combinedResponseJson = csvUrls.foldLeft(Json.arr()) { (combinedArray, csvUrl) =>
                        val responseJsonForUrl = Try {
                            val final_result = wSClient.url(csvUrl)
                              .get()
                            val result:WSResponse=Await.result(
                                final_result,Duration.create(500,SECONDS)
                            )

                            if (result.status == 200) {
                                val source = Source.fromURL(csvUrl)
                                val responseJson = source.getLines().drop(1).foldLeft(Json.arr()) { case (array, line) =>
                                    val values = line.split(",")
                                    array :+ Json.obj(
                                        "email" -> values(0),
                                        "result" -> values(1),
                                        "reason" -> values(2)
                                    )
                                }
                                source.close()
                                responseJson
                            } else {
                                println(s"Error response from URL $csvUrl: ${result.status},${result.body}")
                                assert(false)
                                Json.arr()
                            }
                        }

                        combinedArray ++ responseJsonForUrl.getOrElse(Json.arr())
                    }

                    println(combinedResponseJson)
                    assert(true)

                case "test_actualfetchingfunction" =>

                    println("test_actualfetchingfunction started")
                    val requestId="48541"


                    val result= listCleanEmailValidationApi.fetchBatchRequestResults(requestId)(ws = wSClient,ec = actorContext,srLogger = logger)

                    val emailValidationResults = Await.result(result, Duration.Inf)

                    emailValidationResults match {
                        case emailValidationResults if emailValidationResults.nonEmpty =>
                            println("Batch request results successfully fetched:")
                            emailValidationResults.foreach(println)
                            assert(emailValidationResults.nonEmpty)

                        case _ =>
                            println("Failed to fetch batch request results")
                            assert(false, "Failed to fetch batch request results")
                    }


                case "test_createBatchRequest" =>

                    println("test_createbatchRequest started")
                    val emailList = Seq("<EMAIL>")


                    val result= listCleanEmailValidationApi.createBatchRequest(emailList)(ws = wSClient,ec = actorContext,srLogger = logger)

                    val batchRequestResponse = Await.result(result,Duration.Inf)

                    batchRequestResponse match {
                        case batchRequestResponse: BatchRequestResponse =>
                            println(s"success : $batchRequestResponse")
//                        case _=>
//                            println(s"Failed to create BatchRequest")
                    }


                case "test_checkIfBatchRequestCompleted"=>
                    println("test_checkIfBatchRequestCompleted started")
                    val request_Id= "48519"

                    val result = listCleanEmailValidationApi.checkIfBatchRequestCompleted(request_Id)(ws = wSClient,ec = actorContext,srLogger = logger)


                    val isCompleted = Await.result(result,Duration.Inf)

                    isCompleted match {
                        case iscompleted =>
                            println(s"Is validation completed: $iscompleted")



                    }






















            }
        }
    }

}
