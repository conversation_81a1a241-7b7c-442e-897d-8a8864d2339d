package utils.testapp

import api.campaigns.models.{CampaignEmailSettingsId, CampaignStepType}
import org.joda.time.DateTime
import play.api.Logging
import scalikejdbc.config.DBs
import sr_scheduler.models.EmailScheduledNew
import utils.SRLogger

object Test_EmailMessageData extends Logging {

  def main(args: Array[String]): Unit = {

    if (args.length > 0) {
      DBs.setupAll()

      val applicationName = args(0)

      applicationName match {

        case "saveEmailsToBeScheduledAndUpdateCampaignDataV2" =>

          object main extends TestAppTrait {
            def init() = {

              val dbAndSession = dbUtils.startLocalTx()

              val db = dbAndSession.db
              val session = dbAndSession.session
              val emailsToBeScheduled = Vector(
                EmailScheduledNew(
                  campaign_id = None,
                  step_id = None,
                  is_opening_step = false,
                  prospect_id = None,
                  prospect_account_id = None,
                  added_at = DateTime.now,
                  scheduled_at = DateTime.now,
                  sender_email_settings_id = 183,
                  template_id = None,
                  variant_id = None,
                  rep_mail_server_id = 1,
                  via_gmail_smtp = None,
                  step_type = CampaignStepType.ManualEmailStep,

                  team_id = 59,
                  account_id = 51,
                  campaign_name = None,
                  step_name = None,
                  receiver_email_settings_id = 183,

                  to_email = "<EMAIL>",
                  to_name = Some("Animesh"),

                  from_email = "<EMAIL>",
                  from_name = "Animesh",

                  reply_to_email = Some("<EMAIL>"),
                  reply_to_name = Some("Animesh"),

                  rep_tracking_host_id = 1,

                  scheduled_from_campaign = false,
                  scheduled_manually = true,

                  body = None,
                  base_body = None,
                  text_body = None,
                  subject = None,

                  has_open_tracking = false, // will be reset below in step2
                  has_click_tracking = false, // will be reset below in step2
                  has_unsubscribe_link = false, // will be reset below in step2
                  list_unsubscribe_header = None, // will be reset below in step2
                  gmail_fbl = None, // will be reset below in step2

                  email_thread_id = None,

                  pushed_to_rabbitmq = false, // pushed via PushToRabbitmqCronService
                  campaign_email_settings_id = CampaignEmailSettingsId(329),
                  uuid = srUuidUtils.generateEmailsScheduledUuid()
                )
              )

              val result = for {
                saved_emails <- emailScheduledDAO.saveEmailsToBeScheduledAndUpdateCampaignDataV2(
                  emailsToBeScheduled = emailsToBeScheduled,
                  campaign_email_setting_id = CampaignEmailSettingsId(329),
                  emailSendingFlow = None,
                  Logger = new SRLogger("")
                )(session = session)
                _ <- emailMessageDataDAO.addingInEmailMessageDataTableForScheduler(saved = saved_emails)(session)
              } yield saved_emails


              dbUtils.commitAndCloseSession(db = db)

              logger.info(s"AFTER test  $result")
            }
          }
          
          main.init()

      }
    }

  }

}