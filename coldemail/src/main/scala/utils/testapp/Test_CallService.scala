package utils.testapp

import api.CONSTANTS
import api.accounts.{OrgPlan, OrganizationWithCurrentData, TeamId}
import api.accounts.models.{AccountId, OrgId}
import api.call.controller.CreateOrUpdateCallAccountData
import api.call.models.{AddCallingCreditDetails, CallingServiceProvider, PhoneNumber, PhoneNumberUuid, PhoneSID, PhoneType, SubAccountUuid}
import api_layer_models.CurrencyType
import com.twilio.rest.api.v2010.account.IncomingPhoneNumber
import io.sr.billing_common.models.PlanType
import org.joda.time.DateTime
import play.api.Logging
import scalikejdbc.config.DBs
import utils.helpers.LogHelpers
import utils.{Helpers, SRLogger}

import scala.concurrent.Await
import scala.concurrent.duration.Duration

object Test_CallService extends Logging {

  def main(args: Array[String]): Unit = {

    if (args.length > 0) {
      DBs.setupAll()

      val applicationName = args(0)

      applicationName match {

        case "addNumberToDbManualProcess" =>

          logger.info("BEFORE test")

          object Test extends TestAppTrait {

            def init() = {

              /*
                org_id 15548

                team_id 17384

                account_id 18789

               */

              val orgId = OrgId(id = 15548)
              val accountId = AccountId(id = 18789)
              val teamId = TeamId(id = 17384)
              val addNumberData = CreateOrUpdateCallAccountData(
                first_name = "Rob",
                last_name = "Peterski",
                country_code = "AU", // callUtils.allowedCountryList
                phone_type = PhoneType.Local,
                enable_forward = false,
                enable_call_recording = false,
                forward_number = None,
                call_limit = 50,
                phone_uuid = None,
                caller_id = None
              )

              val phoneUUID = PhoneNumberUuid(
                phone_number_uuid = srUuidUtils.generatePhoneNumberUuid
              )

              val phoneNumber = PhoneNumber(
                phone_number = "+***********"
              )

              val phoneSID = PhoneSID(
                phone_sid = "PN892c76e89ffa8c4dadceb1722e402cc1"
              )

              val orgSubAccountUUID = SubAccountUuid(
                uuid = "sub_account_2jP1JpYaNnPCaGVL63JYOqzpRDO"
              )

              val price = 115
              val price_unit = "USD"


              given Logger: SRLogger = new SRLogger("addNumberToDbManualProcess :: adding number ")


              val result = callService.addNumberToDBAndResetCache(
                orgID = orgId,
                accountId = accountId,
                teamId = teamId,
                addNumberData = addNumberData,
                phoneUUID = phoneUUID,
                callingServiceProvider = CallingServiceProvider.TWILIO,
                phoneNumber = phoneNumber,
                phoneNumberSID = phoneSID,
                orgSubAccountUUID = orgSubAccountUUID,
                phone_number_cost_cents = price,
                price_unit = price_unit

              ) match {

                case Left(e) =>

                  Logger.error(s"addNumberToDbManualProcess :: error : ${e}")


                case Right(data) =>

                  Logger.info(s"addNumberToDbManualProcess :: adding success :: ${data}")

              }

              Logger.info(s"result $result")
            }
          }
          Test.init()
          logger.info("AFTER test")


        case "create_sub_account_for_user" =>

          logger.info("BEFORE test")

          object Test extends TestAppTrait {

            def init() = {

              given Logger: SRLogger = new SRLogger("create_sub_account_for_user :: ")

              println("Enter org_id:")
              val org_id_input: String = scala.io.StdIn.readLine() // Read the entire line as a string
              val orgId = OrgId(id = org_id_input.toLong)
              println(s"you have input following :: org_id_input :: ${org_id_input}")

              for {

                org_with_current_data: Option[OrganizationWithCurrentData] <- organizationService.getOrgWithCurrentData(
                  orgId = orgId
                )


              } yield {
                val create_sub_account: Unit = callService.createSubAccountIfNotExist(
                  org_id = orgId,
                  org_plan = org_with_current_data.get.plan
                ) match {

                  case Left(e) =>
                    println(s"create_subaccount_for_user :: error : ${e}")
                    Logger.fatal(s"create_subaccount_for_user :: error : ${e}")

                  case Right(subaccountdetails) =>
                    println(s"create_subaccount_for_user :: error : ${subaccountdetails}")
                    Logger.info(s"create_subaccount_for_user :: adding success :: ${subaccountdetails}")

                }

                create_sub_account
              }

            }
          }
          Test.init()
          logger.info("AFTER test")


        case "add_credit_to_sub_account" =>

          logger.info("BEFORE test")

          object Test extends TestAppTrait {

            def init() = {

              given Logger: SRLogger = new SRLogger("add_credit_to_sub_account :: ")

              println("Enter org_id:")
              val org_id_input = scala.io.StdIn.readLine() // Read the entire line as a string
              val orgId = OrgId(id = org_id_input.toLong)
              println(s"you have input following :: org_id_input :: ${org_id_input} string length :: ${org_id_input.length}")

              println("Enter amount to be added in cents (1 dollar = 100 cents ): ")
              val amount_in_cents_input = scala.io.StdIn.readLine() // Read the entire line as a string
              println(s"Enter amount picked_up : ${amount_in_cents_input.toLong}")

              val add_calling_crdits_details = AddCallingCreditDetails(
                org_id = orgId,
                credit_added_dollar = amount_in_cents_input.toLong / 100,
                credit_added_cents = amount_in_cents_input.toLong,
                credit_unit = CurrencyType.USD,
                credit_added_at = DateTime.now()
              )
              println(s"going to save this :: ${add_calling_crdits_details}")

              val res = callService.addCallingCredits(
                org_id = orgId,
                credit_details = add_calling_crdits_details
              )

              val result = Await.result(res, Duration.Inf)

              result match {

                case Left(e) =>
                  println(s"Error while adding credit :: ${e}")
                  Logger.error(s"Error while adding credit :: ${e}")


                case Right(data) =>

                  println(s"Success while adding credit :: ${data}")
                  Logger.info(s"Credit Added successfully :: ${data}")

              }

            }
          }
          Test.init()
          logger.info("AFTER test")


        case "dynamic_addition_of_calling_number" =>

          logger.info("BEFORE test")

          object Test extends TestAppTrait {

            def init() = {

              /*
                org_id 15548

                team_id 17384

                account_id 18789

               */

              println("Enter org_id:")
              val org_id_input: String = scala.io.StdIn.readLine() // Read the entire line as a string
              val orgId = OrgId(id = org_id_input.toLong)

              println(s" Your input for org_id  --> ${org_id_input}")


              println("Enter account_id ( whom number will be assigned) :")
              val account_id_input: String = scala.io.StdIn.readLine() // Read the entire line as a string
              val accountId = AccountId(id = account_id_input.toLong)
              println(s" Your input for account_id  --> ${account_id_input}")


              println("Enter team_id :")
              val team_id_input: String = scala.io.StdIn.readLine() // Read the entire line as a string
              val teamId = TeamId(id = team_id_input.toLong)
              println(s" Your input for team_id  --> ${team_id_input}")

              println("Enter first_name :")
              val first_name: String = scala.io.StdIn.readLine() // Read the entire line as a string
              println(s" Your input for first_name  --> ${first_name}")

              println("Enter last_name :")
              val last_name: String = scala.io.StdIn.readLine() // Read the entire line as a string
              println(s" Your input for first_name  --> ${last_name}")

              println("Enter phone_number_country_code [caps] :")
              val phone_number_country_code: String = scala.io.StdIn.readLine() // Read the entire line as a string
              println(s" Your input for phone_number_country_code  --> ${phone_number_country_code}")


              val addNumberData = CreateOrUpdateCallAccountData(
                first_name = first_name,
                last_name = last_name,
                country_code = phone_number_country_code, // callUtils.allowedCountryList
                phone_type = PhoneType.Local,
                enable_forward = false,
                enable_call_recording = true,
                forward_number = None,
                call_limit = 50,
                phone_uuid = None,
                caller_id = None
              )

              val phoneUUID = PhoneNumberUuid(
                phone_number_uuid = srUuidUtils.generatePhoneNumberUuid
              )

              println("Enter phone_number ( with + ) :")
              val phone_number: String = scala.io.StdIn.readLine() // Read the entire line as a string
              println(s" Your input for phone_number  --> ${phone_number}")

              val phoneNumber = PhoneNumber(
                phone_number = phone_number
              )

              println("Enter phone_sid (from twilio ) :")
              val phone_sid: String = scala.io.StdIn.readLine() // Read the entire line as a string
              println(s" Your input for phone_sid  --> ${phone_sid}")

              val phoneSID = PhoneSID(
                phone_sid = phone_sid
              )

              println("Enter sub_account_uuid (from twilio ) :")
              val sub_account_uuid: String = scala.io.StdIn.readLine() // Read the entire line as a string
              println(s" Your input for sub_account_uuid  --> ${sub_account_uuid}")

              val orgSubAccountUUID = SubAccountUuid(
                uuid = sub_account_uuid
              )

              println("Enter price of number in cents (from twilio ) :")
              val price_of_number: String = scala.io.StdIn.readLine() // Read the entire line as a string
              println(s" Your input for price of number in cents --> ${price_of_number}")

              val priceOfNumber: Double = price_of_number.toDouble


              given Logger: SRLogger = new SRLogger("addNumberToDbManualProcess :: adding number ")


              val result = callService.addNumberToDBAndResetCache(
                orgID = orgId,
                accountId = accountId,
                teamId = teamId,
                addNumberData = addNumberData,
                phoneUUID = phoneUUID,
                callingServiceProvider = CallingServiceProvider.TWILIO,
                phoneNumber = phoneNumber,
                phoneNumberSID = phoneSID,
                orgSubAccountUUID = orgSubAccountUUID,
                phone_number_cost_cents = priceOfNumber,
                price_unit = "USD",

              ) match {

                case Left(e) =>

                  println(s"addNumberToDbManualProcess :: error : ${e}")
                  Logger.error(s"addNumberToDbManualProcess :: error : ${e}")


                case Right(data) =>

                  println(s"addNumberToDbManualProcess :: adding success  : ${data}")
                  Logger.info(s"addNumberToDbManualProcess :: adding success :: ${data}")

              }

              Logger.info(s"result $result")
            }
          }
          Test.init()
          logger.info("AFTER test")

      }
    }

  }

}
