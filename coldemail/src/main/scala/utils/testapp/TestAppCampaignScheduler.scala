package utils.testapp

import api.CacheServiceJedis
import api.accounts.TeamId
import api.accounts.models.OrgId
import api.campaigns.models.{CampaignStepType, InactiveCampaignCheckType}
import api.campaigns.services.CampaignId
import api.campaigns.{CampaignDAO, CampaignIdAndTeamId}
import api.prospects.models.ProspectDataForChannelScheduling
import app_services.db_query_counter.SrDBQueryCounterService
import app_services.db_query_counter.dao.DBCounterDAO
import play.api.Logging
import scalikejdbc.config.DBs
import sr_scheduler.models.ChannelType
import utils.SRLogger
import utils.TestApp.logger
import utils.dependencyinjectionutils.*
import utils.mq.MqCampaignSendReportMsg
import utils.mq.channel_scheduler.SchedulerMapStepIdAndDelay
import utils.testapp.TestAppTrait
import utils.uuid.SrUuidUtils

import scala.concurrent.Await
import scala.util.{Failure, Success, Try}

/*
This object holds any sort internal test function used to test features/changes manually during development.
 */
object TestAppCampaignScheduler extends Logging {

  def main(args: Array[String]): Unit = {

    if (args.length > 0) {
      DBs.setupAll()


      val applicationName = args(0)

      applicationName match {


        // sbt "coldemail/runMain utils.TestApp createAndScheduleCampaign"
        case "createAndScheduleCampaign" =>

          object CreateAndScheduleCampaign extends TestAppTrait {

            def init = multichannelTestUtil.createAndScheduleCampaign()
          }

          val result = CreateAndScheduleCampaign.init
          Await.result(result, scala.concurrent.duration.Duration.Inf)



        case "fetchNewProspectsV3Multichannel" =>

          given logger: SRLogger = new SRLogger("TESTAPP fetchNewProspectsV3Multichannel")
          object FetchProspectsV3Multichannel extends TestAppTrait {
            def init: Try[List[ProspectDataForChannelScheduling]] = {

              val allowedProspectTimezones = List("America/New_York")
              val prospectIdGreaterThan = None
              val campaignId = 234L
              val team_id = 132L
              val limit = 100
              val org_id = OrgId(10)
              val channelRelevantStepIdAndDelay = Vector(SchedulerMapStepIdAndDelay(
                is_head_step_in_the_campaign = true,
                currentStepType = CampaignStepType.AutoEmailStep,
                nextStepType = CampaignStepType.AutoEmailStep,
                currentStepId = 218545,
                delayTillNextStep = 86400
              ),
                SchedulerMapStepIdAndDelay(
                  is_head_step_in_the_campaign = false,
                  currentStepType = CampaignStepType.AutoEmailStep,
                  nextStepType = CampaignStepType.AutoEmailStep,
                  currentStepId = 218626,
                  delayTillNextStep = 345600
                ),
                SchedulerMapStepIdAndDelay(
                  is_head_step_in_the_campaign = false,
                  currentStepType = CampaignStepType.AutoEmailStep,
                  nextStepType = CampaignStepType.AutoEmailStep,
                  currentStepId = 219091,
                  delayTillNextStep = 432000
                ),
                SchedulerMapStepIdAndDelay(
                  is_head_step_in_the_campaign = false,
                  currentStepType = CampaignStepType.AutoEmailStep,
                  nextStepType = CampaignStepType.AutoEmailStep,
                  currentStepId = 219171,
                  delayTillNextStep = 518400
                ),
                SchedulerMapStepIdAndDelay(
                  is_head_step_in_the_campaign = false,
                  currentStepType = CampaignStepType.AutoEmailStep,
                  nextStepType = CampaignStepType.AutoEmailStep,
                  currentStepId = 219139,
                  delayTillNextStep = 604800
                ),
                SchedulerMapStepIdAndDelay(
                  is_head_step_in_the_campaign = false,
                  currentStepType = CampaignStepType.AutoEmailStep,
                  nextStepType = CampaignStepType.AutoEmailStep,
                  currentStepId = 219110,
                  delayTillNextStep = 604800
                ),
                SchedulerMapStepIdAndDelay(
                  is_head_step_in_the_campaign = false,
                  currentStepType = CampaignStepType.AutoEmailStep,
                  nextStepType = CampaignStepType.AutoEmailStep,
                  currentStepId = 219100,
                  delayTillNextStep = 604800
                ),
                SchedulerMapStepIdAndDelay(
                  is_head_step_in_the_campaign = false,
                  currentStepType = CampaignStepType.AutoEmailStep,
                  nextStepType = CampaignStepType.AutoEmailStep,
                  currentStepId = 219172,
                  delayTillNextStep = 604800
                ))
              val newProspectsInCampaign = true
              val sendOnlyToProspectsWhoWereSentInCurrentCycle = None

              campaignProspectDAO.fetchProspectsV3Multichannel(
                channelType = ChannelType.EmailChannel,
                allowedProspectTimezones = allowedProspectTimezones,
                prospectIdGreaterThan = prospectIdGreaterThan,
                campaignId = campaignId,
                teamId = TeamId(team_id),
                limit = limit,
                channelRelevantStepIdAndDelay = channelRelevantStepIdAndDelay,
                newProspectsInCampaign = newProspectsInCampaign,
                firstStepIsMagicContent = true,
                sendOnlyToProspectsWhoWereSentInCurrentCycle = sendOnlyToProspectsWhoWereSentInCurrentCycle,
                campaign_email_setting = None,
                orgId = org_id,
                //                emailNotCompulsoryEnabled = false,
                useModifiedQueryForDripCampaign = false,
                enable_magic_column = true,
              )
            }

          }

          val result = FetchProspectsV3Multichannel.init
          println(result)

        case "fetchOldProspectsV3Multichannel" =>

          given logger: SRLogger = new SRLogger("TESTAPP fetchOldProspectsV3Multichannel")
          object FetchProspectsV3Multichannel extends TestAppTrait {
            def init: Try[List[ProspectDataForChannelScheduling]] = {

              val allowedProspectTimezones = List("America/New_York")
              val prospectIdGreaterThan = None
              val campaignId = 83094L
              val team_id = 11432L
              val limit = 100
              val org_id = OrgId(10)

              val channelRelevantStepIdAndDelay = Vector(SchedulerMapStepIdAndDelay(
                is_head_step_in_the_campaign = true,
                currentStepType = CampaignStepType.AutoEmailStep,
                nextStepType = CampaignStepType.AutoEmailStep,
                currentStepId = 218545,
                delayTillNextStep = 86400
              ),
                SchedulerMapStepIdAndDelay(
                  is_head_step_in_the_campaign = false,
                  currentStepType = CampaignStepType.AutoEmailStep,
                  nextStepType = CampaignStepType.AutoEmailStep,
                  currentStepId = 218626,
                  delayTillNextStep = 345600
                ),
                SchedulerMapStepIdAndDelay(
                  is_head_step_in_the_campaign = false,
                  currentStepType = CampaignStepType.AutoEmailStep,
                  nextStepType = CampaignStepType.AutoEmailStep,
                  currentStepId = 219091,
                  delayTillNextStep = 432000
                ),
                SchedulerMapStepIdAndDelay(
                  is_head_step_in_the_campaign = false,
                  currentStepType = CampaignStepType.AutoEmailStep,
                  nextStepType = CampaignStepType.AutoEmailStep,
                  currentStepId = 219171,
                  delayTillNextStep = 518400
                ),
                SchedulerMapStepIdAndDelay(
                  is_head_step_in_the_campaign = false,
                  currentStepType = CampaignStepType.AutoEmailStep,
                  nextStepType = CampaignStepType.AutoEmailStep,
                  currentStepId = 219139,
                  delayTillNextStep = 604800
                ),
                SchedulerMapStepIdAndDelay(
                  is_head_step_in_the_campaign = false,
                  currentStepType = CampaignStepType.AutoEmailStep,
                  nextStepType = CampaignStepType.AutoEmailStep,
                  currentStepId = 219110,
                  delayTillNextStep = 604800
                ),
                SchedulerMapStepIdAndDelay(
                  is_head_step_in_the_campaign = false,
                  currentStepType = CampaignStepType.AutoEmailStep,
                  nextStepType = CampaignStepType.AutoEmailStep,
                  currentStepId = 219100,
                  delayTillNextStep = 604800
                ),
                SchedulerMapStepIdAndDelay(
                  is_head_step_in_the_campaign = false,
                  currentStepType = CampaignStepType.AutoEmailStep,
                  nextStepType = CampaignStepType.AutoEmailStep,
                  currentStepId = 219172,
                  delayTillNextStep = 604800
                ))
              val newProspectsInCampaign = false
              val sendOnlyToProspectsWhoWereSentInCurrentCycle = None

              campaignProspectDAO.fetchProspectsV3Multichannel(
                channelType = ChannelType.EmailChannel,
                allowedProspectTimezones = allowedProspectTimezones,
                prospectIdGreaterThan = prospectIdGreaterThan,
                campaignId = campaignId,
                teamId = TeamId(team_id),
                limit = limit,
                channelRelevantStepIdAndDelay = channelRelevantStepIdAndDelay,
                newProspectsInCampaign = newProspectsInCampaign,
                firstStepIsMagicContent = true,
                sendOnlyToProspectsWhoWereSentInCurrentCycle = sendOnlyToProspectsWhoWereSentInCurrentCycle,
                campaign_email_setting = None,
                orgId = org_id,
                //                emailNotCompulsoryEnabled = false,
                enable_magic_column = true,
                useModifiedQueryForDripCampaign = false
              )
            }

          }

          val result = FetchProspectsV3Multichannel.init
          println(result)


        case "fetchOldProspectsV3Multichannel_limit5000" =>

          given logger: SRLogger = new SRLogger("TESTAPP fetchOldProspectsV3Multichannel")
          object FetchProspectsV3Multichannel extends TestAppTrait {
            def init: Try[List[ProspectDataForChannelScheduling]] = {

              val allowedProspectTimezones = List("America/New_York")
              val prospectIdGreaterThan = None
              val campaignId = 234L
              val team_id = 132L
              val limit = 5000
              val org_id: OrgId = OrgId(10)
              val channelRelevantStepIdAndDelay = Vector(SchedulerMapStepIdAndDelay(
                is_head_step_in_the_campaign = true,
                currentStepType = CampaignStepType.AutoEmailStep,
                nextStepType = CampaignStepType.AutoEmailStep,
                currentStepId = 218545,
                delayTillNextStep = 86400
              ),
                SchedulerMapStepIdAndDelay(
                  is_head_step_in_the_campaign = false,
                  currentStepType = CampaignStepType.AutoEmailStep,
                  nextStepType = CampaignStepType.AutoEmailStep,
                  currentStepId = 218626,
                  delayTillNextStep = 345600
                ),
                SchedulerMapStepIdAndDelay(
                  is_head_step_in_the_campaign = false,
                  currentStepType = CampaignStepType.AutoEmailStep,
                  nextStepType = CampaignStepType.AutoEmailStep,
                  currentStepId = 219091,
                  delayTillNextStep = 432000
                ),
                SchedulerMapStepIdAndDelay(
                  is_head_step_in_the_campaign = false,
                  currentStepType = CampaignStepType.AutoEmailStep,
                  nextStepType = CampaignStepType.AutoEmailStep,
                  currentStepId = 219171,
                  delayTillNextStep = 518400
                ),
                SchedulerMapStepIdAndDelay(
                  is_head_step_in_the_campaign = false,
                  currentStepType = CampaignStepType.AutoEmailStep,
                  nextStepType = CampaignStepType.AutoEmailStep,
                  currentStepId = 219139,
                  delayTillNextStep = 604800
                ),
                SchedulerMapStepIdAndDelay(
                  is_head_step_in_the_campaign = false,
                  currentStepType = CampaignStepType.AutoEmailStep,
                  nextStepType = CampaignStepType.AutoEmailStep,
                  currentStepId = 219110,
                  delayTillNextStep = 604800
                ),
                SchedulerMapStepIdAndDelay(
                  is_head_step_in_the_campaign = false,
                  currentStepType = CampaignStepType.AutoEmailStep,
                  nextStepType = CampaignStepType.AutoEmailStep,
                  currentStepId = 219100,
                  delayTillNextStep = 604800
                ),
                SchedulerMapStepIdAndDelay(
                  is_head_step_in_the_campaign = false,
                  currentStepType = CampaignStepType.AutoEmailStep,
                  nextStepType = CampaignStepType.AutoEmailStep,
                  currentStepId = 219172,
                  delayTillNextStep = 604800
                ))
              val newProspectsInCampaign = false
              val sendOnlyToProspectsWhoWereSentInCurrentCycle = None

              campaignProspectDAO.fetchProspectsV3Multichannel(
                channelType = ChannelType.EmailChannel,
                allowedProspectTimezones = allowedProspectTimezones,
                prospectIdGreaterThan = prospectIdGreaterThan,
                campaignId = campaignId,
                teamId = TeamId(team_id),
                limit = limit,
                channelRelevantStepIdAndDelay = channelRelevantStepIdAndDelay,
                newProspectsInCampaign = newProspectsInCampaign,
                firstStepIsMagicContent = true,
                sendOnlyToProspectsWhoWereSentInCurrentCycle = sendOnlyToProspectsWhoWereSentInCurrentCycle,
                campaign_email_setting = None,
                orgId = org_id,
                //                emailNotCompulsoryEnabled = false,
                enable_magic_column = true,
                useModifiedQueryForDripCampaign = false
              )
            }

          }

          val result = FetchProspectsV3Multichannel.init
          println(result)

        /*
      case "fetchProspects" =>

        implicit val ec = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(150))
        given Logger = new SRLogger("fetchProspects TESTAPP")
        implicit lazy val system: ActorSystem = ActorSystem()
        implicit lazy val materializer = Materializer(system)

        val prospectDAO = new ProspectDAO

        val cacheServiceJedis = new CacheServiceJedis

        val dbUtils = new DBUtils

        val prospectAddEventDAO = new ProspectAddEventDAO
        val campaignProspectDAO_2 = new CampaignProspectDAO_2
        val replySentimentDAO = new ReplySentimentDAO
        val emailThreadDAO = new EmailThreadDAO
        val prospectsEmailsDAO = new ProspectsEmailsDAO

        val emailValidationModel = new EmailValidationModel
        val campaignSchedulingMetadataDAO = new CampaignSchedulingMetadataDAO

        val mergeTagService = new MergeTagService(
          campaignProspectDAO_2 = campaignProspectDAO_2,
          campaignSchedulingMetadataDAO = campaignSchedulingMetadataDAO
        )

        val replySentimentJedisDAO = new ReplySentimentJedisDAO(
          cacheServiceJedis = cacheServiceJedis
        )
        val dbCounterDAO = new DBCounterDAO(
          cacheServiceJedis = cacheServiceJedis
        )

        val prospectColumnDef = new ProspectColumnDef(new ProspectColumnDefDAO)
        val prospectQuery = new ProspectQuery(
          prospectColumnDef = prospectColumnDef
        )

        val replySentimentDAOService = new ReplySentimentDAOService(
          replySentimentDAO = replySentimentDAO,
          replySentimentJedisDAO = replySentimentJedisDAO
        )
        val srDBQueryCounterService = new SrDBQueryCounterService(
          dbCounterDAO = dbCounterDAO
        )

        val taskPgDAO = new TaskPgDAO(
          srDBQueryCounterService = srDBQueryCounterService
        )

        val linkedinTaskService = new LinkedinTaskService(
          taskPgDAO = taskPgDAO
        )

        val prospectDAOService = new ProspectDAOService(
          prospectDAO = prospectDAO,
          prospectQuery = prospectQuery,
          linkedinTaskService = linkedinTaskService,
          prospectColumnDef = prospectColumnDef,
          mergeTagService = mergeTagService,
          replySentimentDAOService = replySentimentDAOService,
          emailThreadDAO = emailThreadDAO,
          dbUtils = dbUtils
        )

        val prospectServiceV2 = new ProspectServiceV2(
          prospectDAOService = prospectDAOService,
          prospectsEmailsDAO = prospectsEmailsDAO,
          prospectAddEventDAO = prospectAddEventDAO,
          prospectColumnDef = prospectColumnDef,
          campaignProspectDAO_2 = campaignProspectDAO_2,
          mergeTagService = mergeTagService,
          emailValidationModel = emailValidationModel
        )

        val campaignProspectDAO = new CampaignProspectDAO(
          prospectDAOService = prospectDAOService,
          prospectQuery = prospectQuery,
          srDBQueryCounterService = srDBQueryCounterService,
          prospectAddEventDAO = prospectAddEventDAO,
          prospectServiceV2 = prospectServiceV2
        )
        object EmailChannelScheduler extends MultichannelTestUtilDI
          with CalendarAppServiceDI
          with CalendarAppDaoDI
          with CalendarAppApiDI
          with CampaignProspectAssignDI
          with SrUuidUtilsDI
          with EmailMessageDataDAO_DI
          with SrUuidServiceDI
          with PhantomBusterAgentServiceDI
          with EmailScheduledDAOService_DI
          with MqCampaignSchedulingMetadataMigration_DI
          with LinkedinMessageThreadsDAODI
          with LinkedinMessagesDAODI
          with PhantomBusterApiKeysServiceDI
          with PhantomBusterApiKeysDAODI
          with MQProspectDeleterDI
          with GeneralSettingDAO_DI
          with PhantomBusterAgentsDAODI
          with CampaignSchedulingMetadataDAO_DI
          with GeneralSettingServiceDI
          with LinkedinTaskServiceDI
          with PhantomBusterApiDI
          with EmailValidationDAOService_DI
          with CampaignProspectService2DI
          with Campaign.ChannelSettingServiceDI
          with CampaignServiceDI
          with ProspectServiceDI
          with ProspectDAOService_DI
          with CampaignProspectDAO_DI
          with EmailSettingDAOServiceDI
          with EmailSettingJedisServiceDI
          with EmailScheduledDAO_DI
          with EmailServiceCompanionDI
          with ProspectAccountDAO1DI
          with OrgMetadataServiceDI
          with CampaignStepDAO_DI
          with EmailSettingDAO_DI
          with CampaignSendReportsDAO_DI
          with CampaignStepVariantDAO_DI
          with TemplateServiceDI
          with CampaignEditedPreviewEmailDAO_DI
          with CampaignDAO_DI
          with ResetUserCacheUtilDI
          with TriggerDAO_DI
          with SrUserFeatureUsageEventServiceDI
          with EmailSendingStatusDAO_DI
          with CampaignCacheServiceDI
          with AccountServiceDI
          with SrResourceDI.SrResourceDaoServiceDI
          with SrResourceDI.SrResourceWithoutTenantDaoServiceDI
          with SrResourceDI.SrCacheDI
          with SrUuidInternalDataServiceDI
          with ReplySentimentService_DI
          with AccessTokenService_DI
          with AccountDAO_DI
          with CacheServiceJedisDI
          with OrganizationDAO_DI
          with SupportAccessToUserAccountDAO_DI
          with TeamsDAO_DI
          with UserRedisKeyService_DI
          with SrDBQueryCounterService_DI
          with CampaignProspectDAO_2DI
          with MergeTagService_DI
          with ProspectQueryDI
          with PhishingCheckServiceDI
          with ProspectAddEventDAO_DI
          with ProspectServiceV2_DI
          with FreeEmailDomainListService_DI
          with ProspectColumnDef_DI
          with CampaignDAOService_DI
          with CampaignEmailSettingsDAO_DI
          with BlacklistProspectCheckDAO_DI
          with CampaignProspectServiceDI
          with EmailValidationServiceDI
          with EventLogService_DI
          with GenerateTempId_DI
          with AssociateProspectToEmailThreadService_DI
          with MQAssociateProspectToOldEmails_DI
          with MQTriggerDI
          with ProspectUpdateCategoryTemp_DI
          with ProspectsEmailsDAO_DI
          with TagServiceDI
          with DBUtils_DI
          with EmailThreadDAO_DI
          with ProspectDAO_DI
          with ReplySentimentDAOService_DI
          with CacheServiceDI
          with SrInternalFeatureUsageDaoDI
          with ClientAccountAccessLogDAO_DI
          with BouncerEmailValidationApiDI
          with DeBounceEmailValidationApiDI
          with CloudStorageDI
          with EmailValidationBatchRequestModelDI
          with EmailValidationModelDI
          with MQWebhookEmailInvalidDI
          with EventLogDAO_DI
          with MqHandleEventLogDI
          with FreeEmailDomainListDAO_DI
          with InboxV3ServiceDI
          with BlacklistDAO_DI
          with ProspectUpdateCategoryTemp2_DI
          with ReplySentimentDAO_DI
          with ReplySentimentJedisDAO_DI
          with DBCounterDAO_DI
          with EmailMessageContactModel_DI
          with EmailReplyTrackingModelV2DI
          with EmailSettingService_DI
          with EmailServiceDI
          with MQWebhookProspectCategoryDI
          with EmailReplyTrackingDAOService_DI
          with EmailReplyTrackingModelDI
//            with CacheEventScyllaService_DI
          with EmailHandleErrorServiceDI
          with GmailApiServiceDI
          // with GmailServiceDI
          with MailgunServiceDI
          with MQOpenTrackerDI
          with MQWebhookCompletedDI
          with OutlookApiServiceDI
          with SendGridServiceDI
          with SmtpImapServiceDI
          with HandlePushTriggerEventServiceDI
          with WebhookDAO_DI
          with WebhookUtils_V2_DI
          with TransformAndSaveEmailValidationResultDI
//            with CacheEventScyllaDAO_DI
          with GoogleOAuthDI
          with EmailNotificationServiceDI
          with EmailNotificationDAO_DI
          with IntegrationTypeService_DI
          with LeadStatusServiceDI
          with SRTriggerAllowedCombos_DI
          with TriggerServiceDI
          with WorkFlowAttemptService_DI
          with ActivityTriggerMQServiceDI
          with MicrosoftOAuthDI
          with ReplyFilteringServiceDI
          with HandleActivityTriggerEventServiceDI
//            with ScyllaRunSafely_DI
          with HubSpotOAuth_DI
          with PipedriveOAuth_DI
          with SalesforceOAuth_DI
          with ZohoOAuth_DI
          with ZohoRecruitOAuth_DI
          with MQUnsubscriberTrackerDI
          with EmailDeliveryAnalysisServiceDI
          with WorkflowAttemptDAO_DI
          with MQDoNotContactDI
//            with ScyllaCluster_DI
          with TriggerServiceV2_DI
          with BlacklistServiceDI
//            with ScyllaDbConnection_DI
          with CampaignStepServiceDI
          with CampaignTemplateServiceDI
          with TemplateUtilsDI
          with TemplateDAO_DI
          with ChannelSchedulers.LinkedinChannelSchedulerDI
          with SrShuffleUtilsDI
          with TaskDAO_DI
          with TaskServiceDI
          with LinkedinSettingDAO_DI
          with TaskDaoServiceDI
          with SrEventServiceDI
          with KafkaServiceDI
//            with TaskCacheDAO_DI
//            with TaskCacheService_DI
          with Campaign.CampaignStartServiceDI
          with MQEmailSchedulerV2DI
          with ChannelSchedulers.EmailChannelSchedulerDI
          with SrRandomUtilsDI
          with TriggerDAOService_DI
          with TriggerJedisDAO_DI
          with CampaignProspectTimezonesJedisServiceDI
          with Team.TeamServiceDI
          with CampaignsMissingMergeTagServiceDI
          with ProspectColumnDefDAO_DI
          with TIntegrationCRMService_DI
          with EventLogFindServiceDI
          with SRFeatureFlagsServiceDI
          with EmailThreadDAOService_DI
          with InboxV3DAOService_DI
          with OrgMetadataDAO_DI
          with CallDAO_DI
          with SrRedisHashSetBasedLockServiceV2_DI
          with SrRedisSimpleLockServiceV2_DI
          with SrRedisHashSetServiceV2_DI
          with EmailSchedulerJedisService_DI
          with ProspectTagDAOLegacy_DI
          with RepTrackingHostServiceDI
          with EmailsScheduledDeleteService_DI
          with OpportunitiesDefaultSetupService_DI
          with PipelineDAO_DI
          with OpportunityStatusDAO_DI
          with TeamsPipelineConfigDAO_DI
          with TeamsPipelineConfigService_DI
          with WhatsappSettingDI.WhatsappSettingDAODI
          with SmsSettingDI.SmsSettingDAODI
          with OrganizationService_DI
          with OrganizationJedisCacheDao_DI {

          def init = {
            val campaignForSchedulingEmail = CampaignForScheduling.CampaignForSchedulingEmail(
              campaign_id = 83094L,
              campaign_owner_id = 12300L,
              team_id = 11432L,
              org_id = 10224L,
              campaign_name = "Cold Lead: Machinery",
              status = CampaignStatus.RUNNING,

              head_step_id = 218545L,

              // settings

              sending_holiday_calendar_id = None,

              // in CampaignForScheduling, email_settings would be there because we ignore campaigns which do not have them

              campaign_email_setting = CampaignEmailSettingForScheduler(
                sender_email_settings_id = 22410,
                receiver_email_settings_id = 23626,
                campaign_email_settings_id = CampaignEmailSettingsId(122L),
              ),
              append_followups = false,
              open_tracking_enabled = true,
              click_tracking_enabled = true,
              opt_out_msg = "If you don't want to hear from me, just let me know and I'll take you off my potential customer list.",
              opt_out_is_text = true,

              timezone = "America/New_York",
              daily_from_time = 25200, // time since beginning of day in seconds
              daily_till_time = 59400, // time since beginning of day in seconds

              // Sunday is the first day
              days_preference = List(false, true, true, true, true, true, false),


              email_priority = CampaignEmailPriority.EQUAL,

              max_emails_per_prospect_per_day = 1,
              max_emails_per_prospect_per_week = 3,

              max_emails_per_prospect_account_per_day = 10,
              max_emails_per_prospect_account_per_week = 20,

              campaign_max_emails_per_day = 500,

              // warm up
              softstart_setting = Some(CampaignWarmupSetting(
                warmup_started_at = DateTime.parse("2023-02-27T14:27:00.94Z"),
                warmup_length_in_days = 10,
                warmup_starting_email_count = 20
              )),

              mark_completed_after_days = 1,

              latest_email_scheduled_at = None,


              from_email = "<EMAIL>",
              from_name = "Doug Wilkes",

              reply_to_email = "<EMAIL>",
              reply_to_name = "Doug Wilkes",

              min_delay_seconds = 30,
              max_delay_seconds = 90,

              enable_email_validation = true,

              rep_mail_server_id = 13,
              via_gmail_smtp = Some(false),
              prospects_remaining_to_be_scheduled_exists = Some(true),
              count_of_sender_emails = 1,
              selected_calendar_data = None
            )

            val channelRelevantStepIdAndDelay = Vector(SchedulerMapStepIdAndDelay(
              is_head_step_in_the_campaign = true,
              currentStepType = CampaignStepType.AutoEmailStep,
              nextStepType = CampaignStepType.AutoEmailStep,
              currentStepId = 218545,
              delayTillNextStep = 86400
            ),
              SchedulerMapStepIdAndDelay(
                is_head_step_in_the_campaign = false,
                currentStepType = CampaignStepType.AutoEmailStep,
                nextStepType = CampaignStepType.AutoEmailStep,
                currentStepId = 218626,
                delayTillNextStep = 345600
              ),
              SchedulerMapStepIdAndDelay(
                is_head_step_in_the_campaign = false,
                currentStepType = CampaignStepType.AutoEmailStep,
                nextStepType = CampaignStepType.AutoEmailStep,
                currentStepId = 219091,
                delayTillNextStep = 432000
              ),
              SchedulerMapStepIdAndDelay(
                is_head_step_in_the_campaign = false,
                currentStepType = CampaignStepType.AutoEmailStep,
                nextStepType = CampaignStepType.AutoEmailStep,
                currentStepId = 219171,
                delayTillNextStep = 518400
              ),
              SchedulerMapStepIdAndDelay(
                is_head_step_in_the_campaign = false,
                currentStepType = CampaignStepType.AutoEmailStep,
                nextStepType = CampaignStepType.AutoEmailStep,
                currentStepId = 219139,
                delayTillNextStep = 604800
              ),
              SchedulerMapStepIdAndDelay(
                is_head_step_in_the_campaign = false,
                currentStepType = CampaignStepType.AutoEmailStep,
                nextStepType = CampaignStepType.AutoEmailStep,
                currentStepId = 219110,
                delayTillNextStep = 604800
              ),
              SchedulerMapStepIdAndDelay(
                is_head_step_in_the_campaign = false,
                currentStepType = CampaignStepType.AutoEmailStep,
                nextStepType = CampaignStepType.AutoEmailStep,
                currentStepId = 219100,
                delayTillNextStep = 604800
              ),
              SchedulerMapStepIdAndDelay(
                is_head_step_in_the_campaign = false,
                currentStepType = CampaignStepType.AutoEmailStep,
                nextStepType = CampaignStepType.AutoEmailStep,
                currentStepId = 219172,
                delayTillNextStep = 604800
              ))

            emailChannelScheduler.fetchProspects(
              channelType = ChannelType.EmailChannel,
              campaignProspectDAO = campaignProspectDAO,

              scheduleFromTime = DateTime.now().minusDays(10),
              campaign = campaignForSchedulingEmail,
              timeZonesInCampaign = Success(List("America/New_York")),
              prospectsToFetch = 100,
              channelRelevantStepIdAndDelay = channelRelevantStepIdAndDelay,
              newProspectsInCampaign = false,
              sendOnlyToProspectsWhoWereSentInCurrentCycle = None

            )
          }
        }

        val result = EmailChannelScheduler.init
        println(result)
        */

        /*
        case "fetchProspects_5000" =>

          implicit val ec = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(150))
          given Logger = new SRLogger("fetchProspects TESTAPP")
          implicit lazy val system: ActorSystem = ActorSystem()
          implicit lazy val materializer = Materializer(system)

          val prospectDAO = new ProspectDAO

          val cacheServiceJedis = new CacheServiceJedis

          val dbUtils = new DBUtils

          val prospectAddEventDAO = new ProspectAddEventDAO
          val campaignProspectDAO_2 = new CampaignProspectDAO_2
          val replySentimentDAO = new ReplySentimentDAO
          val emailThreadDAO = new EmailThreadDAO
          val prospectsEmailsDAO = new ProspectsEmailsDAO

          val emailValidationModel = new EmailValidationModel

          val campaignSchedulingMetadataDAO = new CampaignSchedulingMetadataDAO

          val mergeTagService = new MergeTagService(
            campaignProspectDAO_2 = campaignProspectDAO_2,
            campaignSchedulingMetadataDAO = campaignSchedulingMetadataDAO
          )

          val replySentimentJedisDAO = new ReplySentimentJedisDAO(
            cacheServiceJedis = cacheServiceJedis
          )
          val dbCounterDAO = new DBCounterDAO(
            cacheServiceJedis = cacheServiceJedis
          )

          val prospectColumnDef = new ProspectColumnDef(new ProspectColumnDefDAO)
          val prospectQuery = new ProspectQuery(
            prospectColumnDef = prospectColumnDef
          )

          val replySentimentDAOService = new ReplySentimentDAOService(
            replySentimentDAO = replySentimentDAO,
            replySentimentJedisDAO = replySentimentJedisDAO
          )
          val srDBQueryCounterService = new SrDBQueryCounterService(
            dbCounterDAO = dbCounterDAO
          )

          val taskPgDAO = new TaskPgDAO(
            srDBQueryCounterService = srDBQueryCounterService
          )

          val linkedinTaskService = new LinkedinTaskService(
            taskPgDAO = taskPgDAO
          )

          val prospectDAOService = new ProspectDAOService(
            prospectDAO = prospectDAO,
            prospectQuery = prospectQuery,
            linkedinTaskService = linkedinTaskService,
            prospectColumnDef = prospectColumnDef,
            mergeTagService = mergeTagService,
            replySentimentDAOService = replySentimentDAOService,
            emailThreadDAO = emailThreadDAO,
            dbUtils = dbUtils
          )

          val prospectServiceV2 = new ProspectServiceV2(
            prospectDAOService = prospectDAOService,
            prospectsEmailsDAO = prospectsEmailsDAO,
            prospectAddEventDAO = prospectAddEventDAO,
            prospectColumnDef = prospectColumnDef,
            campaignProspectDAO_2 = campaignProspectDAO_2,
            mergeTagService = mergeTagService,
            emailValidationModel = emailValidationModel
          )

          val campaignProspectDAO = new CampaignProspectDAO(
            prospectDAOService = prospectDAOService,
            prospectQuery = prospectQuery,
            srDBQueryCounterService = srDBQueryCounterService,
            prospectAddEventDAO = prospectAddEventDAO,
            prospectServiceV2 = prospectServiceV2
          )
          object EmailChannelScheduler extends MultichannelTestUtilDI
            with CalendarAppServiceDI
            with CalendarAppDaoDI
            with CalendarAppApiDI
            with CampaignProspectAssignDI
            with SrUuidUtilsDI
            with EmailMessageDataDAO_DI
            with EmailScheduledDAOService_DI
            with SrUuidServiceDI
            with PhantomBusterAgentServiceDI
            with LinkedinMessageThreadsDAODI
            with MqCampaignSchedulingMetadataMigration_DI
            with PhantomBusterApiKeysServiceDI
            with PhantomBusterApiKeysDAODI
            with GeneralSettingDAO_DI
            with LinkedinMessagesDAODI
            with GeneralSettingServiceDI
            with PhantomBusterAgentsDAODI
            with MQProspectDeleterDI
            with LinkedinTaskServiceDI
            with EmailValidationDAOService_DI
            with CampaignSchedulingMetadataDAO_DI
            with CampaignProspectService2DI
            with PhantomBusterApiDI
            with Campaign.ChannelSettingServiceDI
            with CampaignServiceDI
            with ProspectServiceDI
            with ProspectDAOService_DI
            with CampaignProspectDAO_DI
            with EmailSettingDAOServiceDI
            with CampaignDAOService_DI
            with CampaignEmailSettingsDAO_DI
            with EmailSettingJedisServiceDI
            with EmailScheduledDAO_DI
            with EmailServiceCompanionDI
            with ProspectAccountDAO1DI
            with OrgMetadataServiceDI
            with CampaignStepDAO_DI
            with EmailSettingDAO_DI
            with CampaignSendReportsDAO_DI
            with CampaignStepVariantDAO_DI
            with TemplateServiceDI
            with CampaignEditedPreviewEmailDAO_DI
            with CampaignDAO_DI
            with ResetUserCacheUtilDI
            with TriggerDAO_DI
            with SrUserFeatureUsageEventServiceDI
            with EmailSendingStatusDAO_DI
            with CampaignCacheServiceDI
            with AccountServiceDI
            with SrResourceDI.SrResourceDaoServiceDI
            with SrResourceDI.SrResourceWithoutTenantDaoServiceDI
            with SrResourceDI.SrCacheDI
            with SrUuidInternalDataServiceDI
            with ReplySentimentService_DI
            with AccessTokenService_DI
            with AccountDAO_DI
            with CacheServiceJedisDI
            with OrganizationDAO_DI
            with SupportAccessToUserAccountDAO_DI
            with TeamsDAO_DI
            with UserRedisKeyService_DI
            with SrDBQueryCounterService_DI
            with CampaignProspectDAO_2DI
            with MergeTagService_DI
            with ProspectQueryDI
            with PhishingCheckServiceDI
            with ProspectAddEventDAO_DI
            with ProspectServiceV2_DI
            with FreeEmailDomainListService_DI
            with ProspectColumnDef_DI
            with BlacklistProspectCheckDAO_DI
            with CampaignProspectServiceDI
            with EmailValidationServiceDI
            with EventLogService_DI
            with GenerateTempId_DI
            with AssociateProspectToEmailThreadService_DI
            with MQAssociateProspectToOldEmails_DI
            with MQTriggerDI
            with ProspectUpdateCategoryTemp_DI
            with ProspectsEmailsDAO_DI
            with TagServiceDI
            with DBUtils_DI
            with EmailThreadDAO_DI
            with ProspectDAO_DI
            with ReplySentimentDAOService_DI
            with CacheServiceDI
            with SrInternalFeatureUsageDaoDI
            with ClientAccountAccessLogDAO_DI
            with BouncerEmailValidationApiDI
            with DeBounceEmailValidationApiDI
            with CloudStorageDI
            with EmailValidationBatchRequestModelDI
            with EmailValidationModelDI
            with MQWebhookEmailInvalidDI
            with EventLogDAO_DI
            with MqHandleEventLogDI
            with FreeEmailDomainListDAO_DI
            with InboxV3ServiceDI
            with BlacklistDAO_DI
            with ProspectUpdateCategoryTemp2_DI
            with ReplySentimentDAO_DI
            with ReplySentimentJedisDAO_DI
            with DBCounterDAO_DI
            with EmailMessageContactModel_DI
            with EmailReplyTrackingModelV2DI
            with EmailSettingService_DI
            with EmailServiceDI
            with MQWebhookProspectCategoryDI
            with EmailReplyTrackingDAOService_DI
            with EmailReplyTrackingModelDI
//            with CacheEventScyllaService_DI
            with EmailHandleErrorServiceDI
            with GmailApiServiceDI
            // with GmailServiceDI
            with MailgunServiceDI
            with MQOpenTrackerDI
            with MQWebhookCompletedDI
            with OutlookApiServiceDI
            with SendGridServiceDI
            with SmtpImapServiceDI
            with HandlePushTriggerEventServiceDI
            with WebhookDAO_DI
            with WebhookUtils_V2_DI
            with TransformAndSaveEmailValidationResultDI
//            with CacheEventScyllaDAO_DI
            with GoogleOAuthDI
            with EmailNotificationServiceDI
            with EmailNotificationDAO_DI
            with IntegrationTypeService_DI
            with LeadStatusServiceDI
            with SRTriggerAllowedCombos_DI
            with TriggerServiceDI
            with WorkFlowAttemptService_DI
            with ActivityTriggerMQServiceDI
            with MicrosoftOAuthDI
            with ReplyFilteringServiceDI
            with HandleActivityTriggerEventServiceDI
//            with ScyllaRunSafely_DI
            with HubSpotOAuth_DI
            with PipedriveOAuth_DI
            with SalesforceOAuth_DI
            with ZohoOAuth_DI
            with ZohoRecruitOAuth_DI
            with MQUnsubscriberTrackerDI
            with EmailDeliveryAnalysisServiceDI
            with WorkflowAttemptDAO_DI
            with MQDoNotContactDI
//            with ScyllaCluster_DI
            with TriggerServiceV2_DI
            with BlacklistServiceDI
//            with ScyllaDbConnection_DI
            with CampaignStepServiceDI
            with CampaignTemplateServiceDI
            with TemplateUtilsDI
            with TemplateDAO_DI
            with ChannelSchedulers.LinkedinChannelSchedulerDI
            with SrShuffleUtilsDI
            with TaskDAO_DI
            with TaskServiceDI
            with LinkedinSettingDAO_DI
            with TaskDaoServiceDI
            with SrEventServiceDI
            with KafkaServiceDI
//            with TaskCacheDAO_DI
//            with TaskCacheService_DI
            with Campaign.CampaignStartServiceDI
            with MQEmailSchedulerV2DI
            with ChannelSchedulers.EmailChannelSchedulerDI
            with SrRandomUtilsDI
            with TriggerDAOService_DI
            with TriggerJedisDAO_DI
            with CampaignProspectTimezonesJedisServiceDI
            with Team.TeamServiceDI
            with CampaignsMissingMergeTagServiceDI
            with ProspectColumnDefDAO_DI
            with TIntegrationCRMService_DI
            with EventLogFindServiceDI
            with SRFeatureFlagsServiceDI
            with EmailThreadDAOService_DI
            with InboxV3DAOService_DI
            with OrgMetadataDAO_DI
            with CallDAO_DI
            with SrRedisHashSetBasedLockServiceV2_DI
            with SrRedisSimpleLockServiceV2_DI
            with SrRedisHashSetServiceV2_DI
            with EmailSchedulerJedisService_DI
            with ProspectTagDAOLegacy_DI
            with RepTrackingHostServiceDI
            with EmailsScheduledDeleteService_DI
            with OpportunitiesDefaultSetupService_DI
            with PipelineDAO_DI
            with OpportunityStatusDAO_DI
            with TeamsPipelineConfigDAO_DI
            with TeamsPipelineConfigService_DI
            with WhatsappSettingDI.WhatsappSettingDAODI
            with SmsSettingDI.SmsSettingDAODI
            with OrganizationService_DI
            with OrganizationJedisCacheDao_DI {

            def init = {
              val campaignForSchedulingEmail = CampaignForScheduling.CampaignForSchedulingEmail(
                campaign_id = 83094L,
                campaign_owner_id = 12300L,
                team_id = 11432L,
                org_id = 10224L,
                campaign_name = "Cold Lead: Machinery",
                status = CampaignStatus.RUNNING,

                head_step_id = 218545L,

                // settings

                sending_holiday_calendar_id = None,

                // in CampaignForScheduling, email_settings would be there because we ignore campaigns which do not have them
                campaign_email_setting = CampaignEmailSettingForScheduler(
                  sender_email_settings_id = 22410,
                  receiver_email_settings_id = 23626,
                  campaign_email_settings_id = CampaignEmailSettingsId(122L),
                ),

                append_followups = false,
                open_tracking_enabled = true,
                click_tracking_enabled = true,
                opt_out_msg = "If you don't want to hear from me, just let me know and I'll take you off my potential customer list.",
                opt_out_is_text = true,

                timezone = "America/New_York",
                daily_from_time = 25200, // time since beginning of day in seconds
                daily_till_time = 59400, // time since beginning of day in seconds

                // Sunday is the first day
                days_preference = List(false, true, true, true, true, true, false),


                email_priority = CampaignEmailPriority.EQUAL,

                max_emails_per_prospect_per_day = 1,
                max_emails_per_prospect_per_week = 3,

                max_emails_per_prospect_account_per_day = 10,
                max_emails_per_prospect_account_per_week = 20,

                campaign_max_emails_per_day = 500,

                // warm up
                softstart_setting = Some(CampaignWarmupSetting(
                  warmup_started_at = DateTime.parse("2023-02-27T14:27:00.94Z"),
                  warmup_length_in_days = 10,
                  warmup_starting_email_count = 20
                )),

                mark_completed_after_days = 1,

                latest_email_scheduled_at = None,


                from_email = "<EMAIL>",
                from_name = "Doug Wilkes",

                reply_to_email = "<EMAIL>",
                reply_to_name = "Doug Wilkes",

                min_delay_seconds = 30,
                max_delay_seconds = 90,

                enable_email_validation = true,

                rep_mail_server_id = 13,
                via_gmail_smtp = Some(false),
                prospects_remaining_to_be_scheduled_exists = Some(true),
                count_of_sender_emails = 1,
                selected_calendar_data = None
              )

              val channelRelevantStepIdAndDelay = Vector(SchedulerMapStepIdAndDelay(
                is_head_step_in_the_campaign = true,
                currentStepType = CampaignStepType.AutoEmailStep,
                nextStepType = CampaignStepType.AutoEmailStep,
                currentStepId = 218545,
                delayTillNextStep = 86400
              ),
                SchedulerMapStepIdAndDelay(
                  is_head_step_in_the_campaign = false,
                  currentStepType = CampaignStepType.AutoEmailStep,
                  nextStepType = CampaignStepType.AutoEmailStep,
                  currentStepId = 218626,
                  delayTillNextStep = 345600
                ),
                SchedulerMapStepIdAndDelay(
                  is_head_step_in_the_campaign = false,
                  currentStepType = CampaignStepType.AutoEmailStep,
                  nextStepType = CampaignStepType.AutoEmailStep,
                  currentStepId = 219091,
                  delayTillNextStep = 432000
                ),
                SchedulerMapStepIdAndDelay(
                  is_head_step_in_the_campaign = false,
                  currentStepType = CampaignStepType.AutoEmailStep,
                  nextStepType = CampaignStepType.AutoEmailStep,
                  currentStepId = 219171,
                  delayTillNextStep = 518400
                ),
                SchedulerMapStepIdAndDelay(
                  is_head_step_in_the_campaign = false,
                  currentStepType = CampaignStepType.AutoEmailStep,
                  nextStepType = CampaignStepType.AutoEmailStep,
                  currentStepId = 219139,
                  delayTillNextStep = 604800
                ),
                SchedulerMapStepIdAndDelay(
                  is_head_step_in_the_campaign = false,
                  currentStepType = CampaignStepType.AutoEmailStep,
                  nextStepType = CampaignStepType.AutoEmailStep,
                  currentStepId = 219110,
                  delayTillNextStep = 604800
                ),
                SchedulerMapStepIdAndDelay(
                  is_head_step_in_the_campaign = false,
                  currentStepType = CampaignStepType.AutoEmailStep,
                  nextStepType = CampaignStepType.AutoEmailStep,
                  currentStepId = 219100,
                  delayTillNextStep = 604800
                ),
                SchedulerMapStepIdAndDelay(
                  is_head_step_in_the_campaign = false,
                  currentStepType = CampaignStepType.AutoEmailStep,
                  nextStepType = CampaignStepType.AutoEmailStep,
                  currentStepId = 219172,
                  delayTillNextStep = 604800
                ))

              emailChannelScheduler.fetchProspects(
                channelType = ChannelType.EmailChannel,
                campaignProspectDAO = campaignProspectDAO,

                scheduleFromTime = DateTime.now().minusDays(10),
                campaign = campaignForSchedulingEmail,
                timeZonesInCampaign = Success(List("America/New_York")),
                prospectsToFetch = 5000,
                channelRelevantStepIdAndDelay = channelRelevantStepIdAndDelay,
                newProspectsInCampaign = false,
                sendOnlyToProspectsWhoWereSentInCurrentCycle = None

              )
            }
          }

          val result = EmailChannelScheduler.init
          println(result)
          */

        /*
 // sbt "coldemail/runMain utils.TestApp scheduleGeneralTask"
 case "scheduleGeneralTask" =>
   implicit val ec = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(150))
   given Logger = new SRLogger("addingInEmailSentEvent")
   implicit lazy val system = ActorSystem()
   implicit lazy val materializer = Materializer(system)
   implicit lazy val wSClient: AhcWSClient = AhcWSClient()

   val generalSettingDAO = new GeneralSettingDAO
   val kafkaService = new KafkaProducerService


   val srUuidUtils = new SrUuidUtils
   val prospectDAO = new ProspectDAO
   val accountDAO = new AccountDAO(
     srUuidUtils = srUuidUtils
   )

   val cacheServiceJedis = new CacheServiceJedis
   val clientAccountAccessLogDAO = new ClientAccountAccessLogDAO
   val teamsDAO = new TeamsDAO

   val organizationDAO = new OrganizationDAO
   val dbUtils = new DBUtils
   val emailSendingStatusDAO = new EmailSendingStatusDAO
   val supportAccessToUserAccountDAO = new SupportAccessToUserAccountDAO
   val mailgunService = new MailgunService
   val prospectAddEventDAO = new ProspectAddEventDAO
   val campaignProspectDAO_2 = new CampaignProspectDAO_2
   val triggerDAO = new Trigger
   val replySentimentDAO = new ReplySentimentDAO
   val emailThreadDAO = new EmailThreadDAO
   val prospectsEmailsDAO = new ProspectsEmailsDAO

   val emailValidationModel = new EmailValidationModel
   val emailSettingDAO = new EmailSettingDAO()
   val phishingCheckService = new PhishingCheckService
   val templateService = new TemplateService
   val srInternalFeatureUsageDAO = new SrInternalFeatureUsageDAO
   val campaignSendReportsDAO = new CampaignSendReportsDAO()
   val webhookDAO = new Webhook
   val workflowAttemptDAO = new WorkflowAttemptDAO
   val tagService = new TagService
   val srTriggerAllowedCombos = new SRTriggerAllowedCombos
   val eventLogDAO = new EventLogDAO
   val srShuffleUtils = new SrShuffleUtils()

   val scyllaDbConnection = new ScyllaDbConnection
   val scyllaCluster = scyllaDbConnection.initialize()
   val scyllaRunSafely = new ScyllaRunSafely(
     scyllaCluster = scyllaCluster
   )
   val taskScyllaCacheDAO = new TaskScyllaCacheDAO(
     scyllaRunSafely = scyllaRunSafely
   )

   val srEventService = new SrEventService(
     kafkaService = kafkaService
   )

   val userRedisKeyService = new UserRedisKeyService(
     cacheServiceJedis = cacheServiceJedis
   )
   val accessTokenService = new AccessTokenService(
     cacheServiceJedis = cacheServiceJedis,
     clientAccountAccessLogDAO = clientAccountAccessLogDAO
   )

   val cacheService = new CacheService(
     cacheServiceJedis = cacheServiceJedis
   )

   val mergeTagService = new MergeTagService(
     campaignProspectDAO_2 = campaignProspectDAO_2
   )

   val replySentimentJedisDAO = new ReplySentimentJedisDAO(
     cacheServiceJedis = cacheServiceJedis
   )
   val dbCounterDAO = new DBCounterDAO(
     cacheServiceJedis = cacheServiceJedis
   )

   val srUserFeatureUsageEventService = new SrUserFeatureUsageEventService(
     srInternalFeatureUsageDAO = srInternalFeatureUsageDAO
   )
   val campaignCacheService = new CampaignCacheService(
     cacheServiceJedis = cacheServiceJedis
   )

   val emailSettingJedisService = new EmailSettingJedisService(
     cacheServiceJedis = cacheServiceJedis
   )
   val triggerJedisDAO = new TriggerJedisDAO(
     cacheServiceJedis = cacheServiceJedis
   )

   val workFlowAttemptService = new WorkFlowAttemptService(
     attemptDAO = workflowAttemptDAO,
     dbUtils = dbUtils,
     srUuidUtils = srUuidUtils

   )
   val campaignProspectJedisService = new CampaignProspectTimezonesJedisService(
     cacheServiceJedis = cacheServiceJedis
   )
   val eventLogFindService = new EventLogFindService(
     eventLogDAO = eventLogDAO
   )
   val prospectColumnDef = new ProspectColumnDef (new ProspectColumnDefDAO)
   val prospectQuery = new ProspectQuery(
     prospectColumnDef = prospectColumnDef
   )


   val taskCacheDAO = new TaskCacheDAO(
     taskScyllaCacheDAO = taskScyllaCacheDAO
   )
   val replySentimentDAOService = new ReplySentimentDAOService(
     replySentimentDAO = replySentimentDAO,
     replySentimentJedisDAO = replySentimentJedisDAO
   )
   val srDBQueryCounterService = new SrDBQueryCounterService(
     dbCounterDAO = dbCounterDAO
   )

   val campaignEditedPreviewEmailDAO = new CampaignEditedPreviewEmailDAO(
     campaignProspectDAO_2 = campaignProspectDAO_2,
     mergeTagService = mergeTagService
   )

   val triggerDAOService = new TriggerDAOService(
     triggerJedisDAO = triggerJedisDAO,
     triggerDAO = triggerDAO
   )

   val campaignDAO = new CampaignDAO(
     srDBQueryCounterService = srDBQueryCounterService,
     srUuidUtils = srUuidUtils
   )
   val taskPgDAO = new TaskPgDAO(
     srDBQueryCounterService = srDBQueryCounterService
   )

   val linkedinTaskService = new LinkedinTaskService(
     taskPgDAO = taskPgDAO
   )

   val prospectDAOService = new ProspectDAOService(
     prospectDAO = prospectDAO,
     prospectQuery = prospectQuery,
     linkedinTaskService = linkedinTaskService,
     prospectColumnDef = prospectColumnDef,
     mergeTagService = mergeTagService,
     replySentimentDAOService = replySentimentDAOService,
     emailThreadDAO = emailThreadDAO,
     dbUtils = dbUtils
   )



   val channelSettingService = new ChannelSettingService(
     campaignDAO = campaignDAO,
   )


   val prospectServiceV2 = new ProspectServiceV2(
     prospectDAOService = prospectDAOService,
     prospectsEmailsDAO = prospectsEmailsDAO,
     prospectAddEventDAO = prospectAddEventDAO,
     prospectColumnDef = prospectColumnDef,
     campaignProspectDAO_2 = campaignProspectDAO_2,
     mergeTagService = mergeTagService,
     emailValidationModel = emailValidationModel
   )


   val campaignProspectDAO = new CampaignProspectDAO(
     prospectDAOService = prospectDAOService,
     prospectQuery = prospectQuery,
     srDBQueryCounterService = srDBQueryCounterService,
     prospectAddEventDAO = prospectAddEventDAO,
     prospectServiceV2 = prospectServiceV2
   )




   val accountService = new AccountService(
     accountDAO = accountDAO,
     cacheServiceJedis = cacheServiceJedis,
     accessTokenService = accessTokenService,
     userRedisKeyService = userRedisKeyService,
     teamDAO = teamsDAO,
     organizationDAO = organizationDAO,
     dbUtils = dbUtils,
     emailSendingStatusDAO = emailSendingStatusDAO,
     supportAccessToUserAccountDAO = supportAccessToUserAccountDAO,
     replySentimentDAOService = replySentimentDAOService,
     srUuidUtils = srUuidUtils
   )

   val triggerServiceV2 = new TriggerServiceV2(
     triggerDAO = triggerDAO,

     accountService = accountService
   )

   val salesforceOAuth = new SalesforceOAuth(
     triggerDAO = triggerDAO,
     triggerServiceV2 = triggerServiceV2,
     triggerDAOService = triggerDAOService
   )
   val zohoOAuth = new ZohoOAuth(
     triggerDAO = triggerDAO,
     triggerServiceV2 = triggerServiceV2,
     triggerDAOService = triggerDAOService
   )
   val zohoRecruitOAuth = new ZohoRecruitOAuth(
     triggerDAO = triggerDAO,
     triggerServiceV2 = triggerServiceV2,
     triggerDAOService = triggerDAOService
   )
   val pipedriveOAuth = new PipedriveOAuth(
     prospectColumnDef = prospectColumnDef,
     triggerDAO = triggerDAO,
     triggerServiceV2 = triggerServiceV2,
     triggerDAOService = triggerDAOService
   )
   val hubspotOAuth = new HubSpotOAuth(
     triggerDAO = triggerDAO,
     triggerServiceV2 = triggerServiceV2,
     triggerDAOService = triggerDAOService
   )
   val integrationTypeService = new IntegrationTypeService(
     salesforceOAuth = salesforceOAuth,
     zohoOAuth = zohoOAuth,
     zohoRecruitOAuth = zohoRecruitOAuth,
     pipedriveOAuth = pipedriveOAuth,
     hubspotOAuth = hubspotOAuth
   )
   val tIntegrationCRMService = new TIntegrationCRMService(
     integrationTypeService = integrationTypeService
   )


   val resetUserCacheUtil = new ResetUserCacheUtil(
     accountService = accountService,
     cacheService = cacheService
   )

   val emailNotificationDAO = new EmailNotificationDAO(
     srDBQueryCounterService = srDBQueryCounterService
   )

   val emailNotificationService = new EmailNotificationService(
     accountService = accountService,
     mailgunService = mailgunService,
     emailNotificationDAO = emailNotificationDAO
   )
   val campaignsMissingMergeTagService = new CampaignsMissingMergeTagService(
     campaignProspectDAO = campaignProspectDAO,
     campaignCacheService = campaignCacheService
   )
   val taskDaoService = new TaskDaoService(
     srEventService = srEventService,
     taskPgDAO = taskPgDAO,
     srUuidUtils = srUuidUtils,
     taskCacheDAO = taskCacheDAO,
     prospectDAO = prospectDAO,
     campaignProspectDAO = campaignProspectDAO,
     prospectAddEventDAO = prospectAddEventDAO,
     accountDAO = accountDAO
   )

   val emailScheduledDAO = new EmailScheduledDAO(
     emailSettingDAO = emailSettingDAO,
     emailThreadDAO = emailThreadDAO,
     prospectAddEventDAO = prospectAddEventDAO,
     campaignProspectDAO = campaignProspectDAO,
     prospectServiceV2 = prospectServiceV2,
     srUuidUtils = srUuidUtils
   )

   val webhookUtils_V2 = new WebhookUtils_V2(
     prospectDAOService = prospectDAOService,
     accountService = accountService,
     accountDAO = accountDAO,
     emailScheduledDAO = emailScheduledDAO,
     prospectColumnDef = prospectColumnDef,
     prospectServiceV2 = prospectServiceV2
   )
   val campaignStepDAO = new CampaignStepDAO(
     campaignDAO = campaignDAO,
     campaignProspectDAO = campaignProspectDAO,
     emailScheduledDAO = emailScheduledDAO
   )
   val emailServiceCompanion = new EmailServiceCompanion(
     templateService = templateService,
     campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO,
     emailScheduledDAO = emailScheduledDAO
   )

   val leadStatusService = new LeadStatusService(
     triggerDAO = triggerDAO,
     emailScheduledDAO = emailScheduledDAO,
     prospectDAOService = prospectDAOService,
     accountService = accountService,
     tIntegrationCRMService = tIntegrationCRMService
   )


   val campaignStepVariantDAO = new CampaignStepVariantDAO(
     emailScheduledDAO = emailScheduledDAO,
     campaignStepDAO = campaignStepDAO,
     campaignProspectDAO_2 = campaignProspectDAO_2,
     srDBQueryCounterService = srDBQueryCounterService,
     phishingCheckService = phishingCheckService
   )

   val webhookUtils = new WebhookUtils(
     webhookUtils_V2 = webhookUtils_V2,
     prospectDAOService = prospectDAOService,
     emailScheduledDAO = emailScheduledDAO,
     webhookDAO = webhookDAO
   )
   val handlePushTriggerEventService = new HandlePushTriggerEventService(
     accountService = accountService,
     emailNotificationService = emailNotificationService,
     prospectDAOService = prospectDAOService,
     emailScheduledDAO = emailScheduledDAO,
     srTriggerAllowedCombos = srTriggerAllowedCombos,
     triggerDAO = triggerDAO,
     leadStatusService = leadStatusService,
     tIntegrationCRMService = tIntegrationCRMService
   )

   val conferenceDAO = new ConferenceDAO()

   val taskService = new TaskService(
     taskDaoService = taskDaoService,
     campaignProspectDAO = campaignProspectDAO,
     dbUtils = dbUtils,
     prospectDAO = prospectDAO,
     prospectDAOService = prospectDAOService,
     callDAO = conferenceDAO
   )

   val campaignEmailSettingsDAO = new CampaignEmailSettingsDAO(
     srUuidUtils = srUuidUtils
   )
   val campaignDAOService = new CampaignDAOService(
     campaignDAO = campaignDAO,
     campaignEmailSettingsDAO = campaignEmailSettingsDAO,
     dbUtils = dbUtils
   )


   val campaignService = new CampaignService(
     channelSettingService = channelSettingService,
     resetUserCacheUtil = resetUserCacheUtil,
     triggerDAO = triggerDAO,
     campaignProspectDAO = campaignProspectDAO,
     prospectDAOService = prospectDAOService,
     emailSettingJedisService = emailSettingJedisService,
     campaignStepVariantDAO = campaignStepVariantDAO,
     emailScheduledDAO = emailScheduledDAO,
     emailSettingDAO = emailSettingDAO,
     campaignSendReportsDAO = campaignSendReportsDAO,
     campaignDAO = campaignDAO,
     campaignDAOService = campaignDAOService,
     srUserFeatureUsageEventService = srUserFeatureUsageEventService,
     emailSendingStatusDAO = emailSendingStatusDAO,
     campaignCacheService = campaignCacheService,
     accountService = accountService,
     replySentimentDAOService = replySentimentDAOService,
     taskService = taskService,
     dbUtils = dbUtils,
     srUuidUtils = srUuidUtils,
     campaignStepDAO = campaignStepDAO
   )

   val campaignProspectService = new CampaignProspectService(
     //            prospectDAO = prospectDAO,
     campaignService = campaignService,
     prospectAddEventDAO = prospectAddEventDAO,
     prospectServiceV2 = prospectServiceV2,
     campaignProspectDAO = campaignProspectDAO,
     campaignProspectTimzonesJedisService = campaignProspectJedisService,
     srUserFeatureUsageEventService = srUserFeatureUsageEventService,
     campaignCacheService = campaignCacheService,
     dbUtils = dbUtils,
     prospectDAOService = prospectDAOService
   )

   val handleActivityTriggerEventService = new HandleActivityTriggerEventService(
     triggerDAO = triggerDAO,
     emailNotificationService = emailNotificationService,
     emailScheduledDAO = emailScheduledDAO,
     prospectDAOService = prospectDAOService,
     campaignService = campaignService,
     campaignProspectDAO = campaignProspectDAO,
     campaignProspectService = campaignProspectService,
     accountService = accountService,
     //            triggerService = triggerService,
     leadStatusService = leadStatusService,
     tagService = tagService,
     prospectServiceV2 = prospectServiceV2,
     srTriggerAllowedCombos = srTriggerAllowedCombos,
     tIntegrationCRMService = tIntegrationCRMService,
     emailThreadDAO = emailThreadDAO
   )
   val mqHandleEventLog = new MqHandleEventLog(
     workFlowAttemptService = workFlowAttemptService,
     triggerDAO = triggerDAO,
     eventLogFindService = eventLogFindService,
     handleActivityTriggerEventService = handleActivityTriggerEventService,
     handlePushTriggerEventService = handlePushTriggerEventService
   )
   val eventLogService = new EventLogService(
     eventLogDAO = eventLogDAO,
     mqHandleEventLog = mqHandleEventLog,
     srUuidUtils = srUuidUtils
   )

   val generalChannelScheduler = new GeneralChannelScheduler(
     generalSettingDAO = generalSettingDAO,
     taskDAO = taskPgDAO,
     taskService = taskService
   )



   val mqActivityTriggerService = new MQActivityTriggerPublisher(
     emailScheduledDAO = emailScheduledDAO,
     prospectDAOService = prospectDAOService,
     eventLogService = eventLogService
   )
   val mqWebhookCompleted = new MQWebhookCompleted(
     webhookUtils = webhookUtils,
     mqActivityTriggerService = mqActivityTriggerService
   )


   val result = generalChannelScheduler.scheduleTasksForChannel(
     channelData = ChannelData.GeneralChannelData(
       generalChannelSettingUuid = "general_channel_setting_32rjnvsvcjj3"
     ),
     teamId = 3L,
     accountService = accountService,
     //accountDAO = accountDAO,
     emailNotificationService = emailNotificationService,
     campaignService = campaignService,
     campaignProspectDAO = campaignProspectDAO,
     campaignStepVariantDAO = campaignStepVariantDAO,
     campaignStepDAO = campaignStepDAO,
     srShuffleUtils = srShuffleUtils,
     emailServiceCompanion = emailServiceCompanion,
     templateService = templateService,
     taskService = taskService,
     taskDAO = taskPgDAO,
     campaignProspectService = campaignProspectService,
     campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO,
     campaignsMissingMergeTagService = campaignsMissingMergeTagService,
     mqWebhookCompleted = mqWebhookCompleted
   )

   Await.result(result, scala.concurrent.duration.Duration.Inf)
*/


        /*
        case "scheduleSmsTask"  =>
          implicit val ec = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(150))
          given Logger = new SRLogger("addingInEmailSentEvent")
          implicit lazy val system = ActorSystem()
          implicit lazy val materializer = Materializer(system)
          implicit lazy val wSClient: AhcWSClient = AhcWSClient()

          val smsSettingDAO: SmsSettingDAO =  new SmsSettingDAO
          val cacheServiceJedis = new CacheServiceJedis

          val kafkaService = new KafkaProducerService
          val dbCounterDAO = new DBCounterDAO(
            cacheServiceJedis = cacheServiceJedis
          )
          val srDBQueryCounterService = new SrDBQueryCounterService(
            dbCounterDAO = dbCounterDAO
          )
          val taskPgDAO = new TaskPgDAO(
            srDBQueryCounterService = srDBQueryCounterService
          )
          val scyllaDbConnection = new ScyllaDbConnection
          val scyllaCluster = scyllaDbConnection.initialize()
          val scyllaRunSafely = new ScyllaRunSafely(
            scyllaCluster = scyllaCluster
          )
          val taskScyllaCacheDAO = new TaskScyllaCacheDAO(
            scyllaRunSafely = scyllaRunSafely
          )
          val taskCacheDAO = new TaskCacheDAO(
            taskScyllaCacheDAO = taskScyllaCacheDAO
          )
          val srEventService = new SrEventService(
            kafkaService = kafkaService
          )

          //          val taskService = new TaskService(
          //            taskDaoService = taskDaoService,
          //            campaignProspectDAO = campaignProspectDAO
          //          )

          val srUuidUtils = new SrUuidUtils
          val accountDAO = new AccountDAO(
            srUuidUtils = srUuidUtils
            )
          val clientAccountAccessLogDAO = new ClientAccountAccessLogDAO
          val userRedisKeyService = new UserRedisKeyService(
            cacheServiceJedis = cacheServiceJedis
          )
          val accessTokenService = new AccessTokenService(
            cacheServiceJedis = cacheServiceJedis,
            clientAccountAccessLogDAO = clientAccountAccessLogDAO
          )
          val teamsDAO = new TeamsDAO
          val organizationDAO = new OrganizationDAO
          val dbUtils = new DBUtils
          val emailSendingStatusDAO = new EmailSendingStatusDAO
          val supportAccessToUserAccountDAO = new SupportAccessToUserAccountDAO
          val mailgunService = new MailgunService
          val cacheService = new CacheService(
            cacheServiceJedis = cacheServiceJedis
          )

          val triggerDAO = new Trigger

          val prospectColumnDef = new ProspectColumnDef(new ProspectColumnDefDAO)
          val prospectQuery = new ProspectQuery(
            prospectColumnDef = prospectColumnDef
          )
          val prospectAddEventDAO = new ProspectAddEventDAO
          val campaignProspectDAO_2 = new CampaignProspectDAO_2
          val prospectDAO = new ProspectDAO
          val mergeTagService = new MergeTagService(campaignProspectDAO_2)

          val replySentimentDAO = new ReplySentimentDAO
          val replySentimentJedisDAO = new ReplySentimentJedisDAO(
            cacheServiceJedis = cacheServiceJedis
          )
          val replySentimentDAOService = new ReplySentimentDAOService(
            replySentimentDAO = replySentimentDAO,
            replySentimentJedisDAO = replySentimentJedisDAO
          )

          val emailThreadDAO = new EmailThreadDAO
          val linkedinTaskService = new LinkedinTaskService(
            taskPgDAO = taskPgDAO
          )

          val prospectDAOService = new ProspectDAOService(
            prospectDAO = prospectDAO,
            prospectQuery = prospectQuery,
            linkedinTaskService = linkedinTaskService,
            prospectColumnDef = prospectColumnDef,
            mergeTagService = mergeTagService,
            replySentimentDAOService = replySentimentDAOService,
            emailThreadDAO = emailThreadDAO,
            dbUtils = dbUtils
          )
          val prospectsEmailsDAO = new ProspectsEmailsDAO

          val emailValidationModel = new EmailValidationModel

          val prospectServiceV2 = new ProspectServiceV2(
            prospectDAOService = prospectDAOService,
            prospectsEmailsDAO = prospectsEmailsDAO,
            prospectAddEventDAO = prospectAddEventDAO,
            prospectColumnDef = prospectColumnDef,
            campaignProspectDAO_2 = campaignProspectDAO_2,
            mergeTagService = mergeTagService,
            emailValidationModel = emailValidationModel
          )
          val campaignProspectDAO = new CampaignProspectDAO(
            prospectDAOService = prospectDAOService,
            prospectQuery = prospectQuery,
            srDBQueryCounterService = srDBQueryCounterService,
            prospectAddEventDAO = prospectAddEventDAO,
            prospectServiceV2 = prospectServiceV2
          )


          val taskDaoService = new TaskDaoService(
            srEventService = srEventService,
            taskPgDAO = taskPgDAO,
            taskCacheDAO = taskCacheDAO,
            srUuidUtils = srUuidUtils,
            prospectDAO = prospectDAO,
            campaignProspectDAO = campaignProspectDAO,
            prospectAddEventDAO = prospectAddEventDAO,
            accountDAO = accountDAO
          )

          val conferenceDAO = new ConferenceDAO()

          val taskService = new TaskService(
            taskDaoService = taskDaoService,
            campaignProspectDAO = campaignProspectDAO,
            dbUtils = dbUtils,
            prospectDAO = prospectDAO,
            prospectDAOService = prospectDAOService,
            callDAO = conferenceDAO
          )

          val emailSettingDAO = new EmailSettingDAO()
          val emailScheduledDAO = new EmailScheduledDAO(
            emailSettingDAO = emailSettingDAO,
            emailThreadDAO = emailThreadDAO,
            //            accountDAO = accountDAO,
            prospectAddEventDAO = prospectAddEventDAO,
            campaignProspectDAO = campaignProspectDAO,
            //            prospectDAO = prospectDAO,
            prospectServiceV2 = prospectServiceV2,
            srUuidUtils = srUuidUtils
            //            emailMessageContactModel = emailMessageContactModel
          )
          val campaignDAO = new CampaignDAO(
            srDBQueryCounterService = srDBQueryCounterService,
            srUuidUtils = srUuidUtils
          )
          val campaignStepDAO = new CampaignStepDAO(
            campaignDAO = campaignDAO,
            campaignProspectDAO = campaignProspectDAO,
            emailScheduledDAO = emailScheduledDAO
          )
          val phishingCheckService = new PhishingCheckService

          val campaignStepVariantDAO = new CampaignStepVariantDAO(
            emailScheduledDAO = emailScheduledDAO,
            campaignStepDAO = campaignStepDAO,
            campaignProspectDAO_2 = campaignProspectDAO_2,
            srDBQueryCounterService = srDBQueryCounterService,
            phishingCheckService = phishingCheckService
          )

          val campaignEditedPreviewEmailDAO = new CampaignEditedPreviewEmailDAO(
            campaignProspectDAO_2 = campaignProspectDAO_2,
            mergeTagService = mergeTagService
          )

          val templateService = new TemplateService

          val accountService = new AccountService(
            accountDAO = accountDAO,
            cacheServiceJedis = cacheServiceJedis,
            accessTokenService = accessTokenService,
            userRedisKeyService = userRedisKeyService,
            teamDAO = teamsDAO,
            organizationDAO = organizationDAO,
            dbUtils = dbUtils,
            emailSendingStatusDAO = emailSendingStatusDAO,
            supportAccessToUserAccountDAO = supportAccessToUserAccountDAO,
            replySentimentDAOService = replySentimentDAOService,
            srUuidUtils = srUuidUtils
          )

          val emailServiceCompanion = new EmailServiceCompanion(
            templateService = templateService,
            campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO,
            emailScheduledDAO = emailScheduledDAO,
          )

          val srInternalFeatureUsageDAO = new SrInternalFeatureUsageDAO
          val srUserFeatureUsageEventService = new SrUserFeatureUsageEventService(
            srInternalFeatureUsageDAO = srInternalFeatureUsageDAO
          )
          val campaignCacheService = new CampaignCacheService(
            cacheServiceJedis = cacheServiceJedis
          )


          val srRandomUtils = new SrRandomUtils()

          val resetUserCacheUtil = new ResetUserCacheUtil(
            accountService = accountService,
            cacheService = cacheService
          )

          val campaignSendReportsDAO = new CampaignSendReportsDAO()

          val emailSettingJedisService = new EmailSettingJedisService(
            cacheServiceJedis = cacheServiceJedis
          )
          val channelSettingService = new ChannelSettingService(
            campaignDAO = campaignDAO,
          )
          val campaignEmailSettingsDAO = new CampaignEmailSettingsDAO(
            srUuidUtils = srUuidUtils
          )
          val campaignDAOService = new CampaignDAOService(
            campaignDAO = campaignDAO,
            campaignEmailSettingsDAO = campaignEmailSettingsDAO,
            dbUtils = dbUtils
          )

          val campaignService = new CampaignService(
            channelSettingService = channelSettingService,
            resetUserCacheUtil = resetUserCacheUtil,
            triggerDAO = triggerDAO,
            campaignProspectDAO = campaignProspectDAO,
            prospectDAOService = prospectDAOService,
            campaignStepVariantDAO = campaignStepVariantDAO,
            emailScheduledDAO = emailScheduledDAO,
            emailSettingJedisService = emailSettingJedisService,
            emailSettingDAO = emailSettingDAO,
            campaignSendReportsDAO = campaignSendReportsDAO,
            // emailServiceCompanion = emailServiceCompanion,
            campaignDAO = campaignDAO,
            campaignDAOService = campaignDAOService,
            srUserFeatureUsageEventService = srUserFeatureUsageEventService,
            emailSendingStatusDAO = emailSendingStatusDAO,
            campaignCacheService = campaignCacheService,
            accountService = accountService,
            replySentimentDAOService = replySentimentDAOService,
            taskService = taskService,
            dbUtils = dbUtils,
            srUuidUtils = srUuidUtils,
            campaignStepDAO = campaignStepDAO
          )

          val emailNotificationDAO = new EmailNotificationDAO(
            srDBQueryCounterService = srDBQueryCounterService
          )


          val emailNotificationService = new EmailNotificationService(
            accountService = accountService,
            mailgunService = mailgunService,
            emailNotificationDAO = emailNotificationDAO
          )
          val webhookUtils_V2 = new WebhookUtils_V2(
            prospectDAOService = prospectDAOService,
            accountService = accountService,
            accountDAO = accountDAO,
            emailScheduledDAO = emailScheduledDAO,
            prospectColumnDef = prospectColumnDef,
            prospectServiceV2 = prospectServiceV2
          )
          val webhookDAO = new Webhook
          val webhookUtils = new WebhookUtils(
            webhookUtils_V2 = webhookUtils_V2,
            prospectDAOService = prospectDAOService,
            emailScheduledDAO = emailScheduledDAO,
            webhookDAO = webhookDAO
          )
          val triggerServiceV2 = new TriggerServiceV2(
            triggerDAO = triggerDAO,

            accountService = accountService
          )
          val triggerJedisDAO = new TriggerJedisDAO(
            cacheServiceJedis = cacheServiceJedis
          )
          val triggerDAOService = new TriggerDAOService(
            triggerJedisDAO = triggerJedisDAO,
            triggerDAO = triggerDAO
          )
          val salesforceOAuth = new SalesforceOAuth(
            triggerDAO = triggerDAO,
            triggerServiceV2 = triggerServiceV2,
            triggerDAOService = triggerDAOService
          )
          val zohoOAuth = new ZohoOAuth(
            triggerDAO = triggerDAO,
            triggerServiceV2 = triggerServiceV2,
            triggerDAOService = triggerDAOService
          )
          val zohoRecruitOAuth = new ZohoRecruitOAuth(
            triggerDAO = triggerDAO,
            triggerServiceV2 = triggerServiceV2,
            triggerDAOService = triggerDAOService
          )
          val pipedriveOAuth = new PipedriveOAuth(
            prospectColumnDef = prospectColumnDef,
            triggerDAO = triggerDAO,
            triggerServiceV2 = triggerServiceV2,
            triggerDAOService = triggerDAOService
          )
          val hubspotOAuth = new HubSpotOAuth(
            triggerDAO = triggerDAO,
            triggerServiceV2 = triggerServiceV2,
            triggerDAOService = triggerDAOService
          )
          val integrationTypeService = new IntegrationTypeService(
            salesforceOAuth = salesforceOAuth,
            zohoOAuth = zohoOAuth,
            zohoRecruitOAuth = zohoRecruitOAuth,
            pipedriveOAuth = pipedriveOAuth,
            hubspotOAuth = hubspotOAuth
          )
          val tIntegrationCRMService = new TIntegrationCRMService(
            integrationTypeService = integrationTypeService
          )

          val workflowAttemptDAO = new WorkflowAttemptDAO
          //val srUuidUtils = new SrUuidUtils
          val workFlowAttemptService = new WorkFlowAttemptService(
            attemptDAO = workflowAttemptDAO,
            dbUtils = dbUtils,
            srUuidUtils = srUuidUtils
          )
          val leadStatusService = new LeadStatusService(
            triggerDAO = triggerDAO,
            emailScheduledDAO = emailScheduledDAO,
            prospectDAOService = prospectDAOService,
            accountService = accountService,
            tIntegrationCRMService = tIntegrationCRMService
          )
          val tagService = new TagService

          val campaignProspectJedisService = new CampaignProspectTimezonesJedisService(
            cacheServiceJedis = cacheServiceJedis
          )
          val campaignProspectService = new CampaignProspectService(
            //            prospectDAO = prospectDAO,
            campaignService = campaignService,
            prospectAddEventDAO = prospectAddEventDAO,
            prospectServiceV2 = prospectServiceV2,
            campaignProspectDAO = campaignProspectDAO,
            campaignProspectTimzonesJedisService = campaignProspectJedisService,
            srUserFeatureUsageEventService = srUserFeatureUsageEventService,
            dbUtils = dbUtils,
            campaignCacheService = campaignCacheService,
            prospectDAOService = prospectDAOService
          )
          val srTriggerAllowedCombos = new SRTriggerAllowedCombos(
          )
          val handleActivityTriggerEventService = new HandleActivityTriggerEventService(
            triggerDAO = triggerDAO,
            emailNotificationService = emailNotificationService,
            emailScheduledDAO = emailScheduledDAO,
            prospectDAOService = prospectDAOService,
            campaignService = campaignService,
            campaignProspectDAO = campaignProspectDAO,
            campaignProspectService = campaignProspectService,
            accountService = accountService,
            //            triggerService = triggerService,
            leadStatusService = leadStatusService,
            tagService = tagService,
            prospectServiceV2 = prospectServiceV2,
            srTriggerAllowedCombos = srTriggerAllowedCombos,
            tIntegrationCRMService = tIntegrationCRMService,
            emailThreadDAO = emailThreadDAO
          )
          val eventLogDAO = new EventLogDAO
          val eventLogFindService = new EventLogFindService(
            eventLogDAO = eventLogDAO
          )
          val handlePushTriggerEventService = new HandlePushTriggerEventService(
            accountService = accountService,
            emailNotificationService = emailNotificationService,
            prospectDAOService = prospectDAOService, emailScheduledDAO = emailScheduledDAO,
            srTriggerAllowedCombos = srTriggerAllowedCombos, triggerDAO = triggerDAO,
            leadStatusService = leadStatusService,
            tIntegrationCRMService = tIntegrationCRMService
          )
          val mqHandleEventLog = new MqHandleEventLog(
            workFlowAttemptService = workFlowAttemptService,
            triggerDAO = triggerDAO,
            eventLogFindService = eventLogFindService,
            handleActivityTriggerEventService = handleActivityTriggerEventService,
            handlePushTriggerEventService = handlePushTriggerEventService
          )

          val eventLogService = new EventLogService(
            eventLogDAO = eventLogDAO, mqHandleEventLog = mqHandleEventLog,
            srUuidUtils = srUuidUtils
          )
          val mqActivityTriggerService = new MQActivityTriggerPublisher(
            emailScheduledDAO = emailScheduledDAO,
            prospectDAOService = prospectDAOService,
            eventLogService = eventLogService
          )
          val mqWebhookCompleted = new MQWebhookCompleted(
            webhookUtils = webhookUtils,
            mqActivityTriggerService = mqActivityTriggerService
          )

          val smsChannelScheduler = new SmsChannelScheduler(
            smsSettingDAO = smsSettingDAO,
            taskService = taskService,
            srRandomUtils = srRandomUtils,
            taskPgDAO = taskPgDAO
          )
          val srShuffleUtils = new SrShuffleUtils()

          val campaignsMissingMergeTagService = new CampaignsMissingMergeTagService(
            campaignProspectDAO = campaignProspectDAO,
            campaignCacheService = campaignCacheService
          )

          val result = smsChannelScheduler.scheduleTasksForChannel(
            channelData = ChannelData.SmsChannelData(
              smsSettingUuid = "sms_channel_setting_32rjnvsvcjj3"
            ),
            teamId = 3L,
            accountService = accountService,
            //accountDAO = accountDAO,
            emailNotificationService = emailNotificationService,
            campaignService = campaignService,
            campaignProspectDAO = campaignProspectDAO,
            campaignStepVariantDAO = campaignStepVariantDAO,
            campaignStepDAO = campaignStepDAO,
            srShuffleUtils = srShuffleUtils,
            emailServiceCompanion = emailServiceCompanion,
            templateService = templateService,
            taskService = taskService,
            taskDAO = taskPgDAO,
            campaignProspectService = campaignProspectService,
            campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO,
            mqWebhookCompleted = mqWebhookCompleted,
            campaignsMissingMergeTagService = campaignsMissingMergeTagService
          )

          Await.result(result, scala.concurrent.duration.Duration.Inf)
        */


        case "test_sc" =>
        /*
        ScheduleEmailCronServiceV2.scheduleEmailAccount(emailSettingId = 4058, logBase = "prod___4058098")
          .map { res =>
            Logger.info(s"\n\n====\n\nres success: $res\n\n----------\ndone\n---------\n\n\n\n\n\n")
          }
          .recover { case e =>
            Logger.error(s"error: ${Helpers.getStackTraceAsString(e)}")}
//          val doNotContactEC = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(150))

        MQDoNotContact.startConsumer()(ws = wSClient, ec = doNotContactEC) match {
          case Failure(e) =>
            Logger.error(s"errrrrror: ${Helpers.getStackTraceAsString(e)}")

          case Success(_) => Logger.info("success")

        }
        */


        // sbt "coldemail/runMain utils.TestApp fetchStuckCampaigns"
        case "fetchStuckCampaigns" =>

          object FetchStuckCampaigns extends CampaignDAO_DI
            with SrDBQueryCounterService_DI
            with DBCounterDAO_DI
            with CacheServiceJedisDI
            with SrUuidUtilsDI {
            def init = campaignDAO.fetchStuckCampaigns(false)
          }

          println(FetchStuckCampaigns.init)
        case "fetchStuckCampaigns_new" =>

          object FetchStuckCampaigns extends CampaignDAO_DI
            with SrDBQueryCounterService_DI
            with DBCounterDAO_DI
            with CacheServiceJedisDI
            with SrUuidUtilsDI {
            def init = campaignDAO.fetchStuckCampaigns(true)
          }

          println(FetchStuckCampaigns.init)

        case "fetchRunningCampaignsDataForLowSending" =>
          object FetchStuckCampaigns extends CampaignDAO_DI
            with SrDBQueryCounterService_DI
            with DBCounterDAO_DI
            with CacheServiceJedisDI
            with SrUuidUtilsDI {
            def init = campaignDAO.fetchRunningCampaignsDataForLowSending(
              MqCampaignSendReportMsg(
                campaignId = CampaignId(1203), teamId = TeamId(505)
              ))
          }

          println(FetchStuckCampaigns.init)



        /*
        case "cp_count_scheduler" =>

          val logger = new SRLogger("hello")

          CampaignProspectDAO
            .getScheduledProspectsCountForCampaign(

              Logger = logger,
              emailSettingId = 10226,

              campaignIdAndTimezone = Seq((39822, "Asia/Kolkata"),(40329, "Asia/Kolkata"),(36447, "Asia/Qatar"),(36802, "Asia/Dubai"),(39074, "Asia/Kolkata"),(36239, "Asia/Qatar"),(38295, "Asia/Tokyo"),(38486, "Asia/Bangkok"),(38293, "Asia/Riyadh"),(35664, "Asia/Kolkata"),(38571, "Asia/Dubai"),(38425, "Asia/Dubai"),(39823, "Asia/Dubai"),(39137, "Asia/Kolkata"),(39581, "Asia/Kolkata"),(38485, "Asia/Dubai"),(38430, "Asia/Kolkata"),(39826, "Asia/Dubai"),(36468, "Asia/Singapore"),(40038, "Asia/Kolkata"),(38847, "Asia/Dubai"),(40209, "Asia/Kolkata"),(36423, "Asia/Kolkata"),(36455, "Asia/Bangkok"),(36532, "Asia/Dubai"),(39806, "Asia/Riyadh"),(40210, "Asia/Kolkata"),(35651, "Asia/Kolkata"),(38838, "Asia/Kolkata"),(39236, "Asia/Kolkata"),(35744, "Africa/Johannesburg"),(36356, "Asia/Kolkata"),(36429, "Asia/Dubai"),(36246, "Asia/Qatar"),(37060, "Asia/Qatar"),(39081, "Asia/Kolkata"),(39825, "Asia/Dubai"),(40117, "Asia/Kolkata"),(37058, "Asia/Kolkata"),(36336, "Asia/Dubai"),(35672, "Asia/Kolkata"),(38388, "Asia/Dubai"),(35674, "Asia/Riyadh"),(36643, "Asia/Dubai"),(39037, "Asia/Dubai"),(39827, "Asia/Dubai"),(36351, "Asia/Singapore"),(39235, "Asia/Kolkata"),(40112, "Asia/Kolkata"),(35741, "Asia/Ho_Chi_Minh"),(36445, "Asia/Dubai"),(37059, "Asia/Kolkata"),(38566, "Asia/Dubai")),

              emailAccountQuotaPerDay = 500,
              emailAccountDonotEnfore24HourLimitTill = None

            ) match {

            case Failure(e) =>
              logger.fatal(s"${Helpers.getStackTraceAsString(e)}")

            case Success(b) =>
              logger.info(s"\n\n\n\ngetScheduledProspectsCountForCampaign value: ${b}")

          }
          */



        case "getInactiveCampaignsForStopping" =>
          val cacheServiceJedis = new CacheServiceJedis
          val dbCounterDAO = new DBCounterDAO(cacheServiceJedis = cacheServiceJedis)
          val srDBQueryCounterService = new SrDBQueryCounterService(dbCounterDAO = dbCounterDAO)
          val srUuidUtils = new SrUuidUtils
          val campaignDAO = new CampaignDAO(
            srDBQueryCounterService = srDBQueryCounterService,
            srUuidUtils = srUuidUtils
          )
          //          val result1 = campaignDAO.getInactiveCampaignsForStopping(None)
          //          println(result1)
          val result2 = campaignDAO.getInactiveCampaignsForStopping(
            inactiveCampaignCheckType = InactiveCampaignCheckType.OnHoldCheck,
            campaignIdAndTeamId = CampaignIdAndTeamId(campaign_id = 133029, team_id = 14754),
            stepTypesInCampaign = Set(CampaignStepType.AutoEmailStep))
          println(result2)



        case "test_explain_analyse" =>
          logger.info("BEFORE test_explain_analyse")

          object Test extends TestAppTrait {

            def init() = {

              val logger: SRLogger = new SRLogger("test_explain_analyse")
              logger.info("inside init")
              campaignDAO.findCampaignsForSchedulingEA(
                emailSettingId = 55,
                logger,
                team_id = TeamId(90)
              ) match {
                case Failure(exception) =>
                  logger.info(s"Failure  $exception")
                case Success(value) =>
                  println(s"Success $value")
              }
            }


          }
          Test.init()
          logger.info("AFTER test_explain_analyse")


      }
    }

  }
}
