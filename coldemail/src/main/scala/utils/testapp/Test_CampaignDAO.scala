package utils.testapp

import api.accounts.models.AccountId
import api.accounts.TeamId
import api.campaigns.models.{LinkedinSettingSenderDetails, SearchParams}
import api.campaigns.services.CampaignId
import api.prospects.InferredQueryTimeline
import api.search.{SearchQuery, SearchQueryMainClause}
import io.smartreach.esp.api.emails.EmailSettingId
import org.joda.time.DateTime
import org.postgresql.util.PGobject
import play.api.Logging
import play.api.libs.json.Json
import scalikejdbc.{DB, scalikejdbcSQLInterpolationImplicitDef}
import scalikejdbc.config.DBs
import sr_scheduler.models.ChannelType
import utils.SRLogger
import utils.dependencyinjectionutils.Tasks.TaskDAO_DI
import utils.dependencyinjectionutils.{AccountDAO_DI, BlacklistProspectCheckDAO_DI, CacheServiceJedisDI, CampaignDAO_DI, CampaignProspectDAO_2DI, CampaignSchedulingMetadataDAO_DI, DBCounterDAO_DI, DBUtils_DI, EmailThreadDAO_DI, FreeEmailDomainListDAO_DI, FreeEmailDomainListService_DI, LinkedinTaskServiceDI, MergeTagService_DI, OrganizationDAO_DI, ProspectAccountDAO1DI, ProspectAddEventDAO_DI, ProspectColumnDefDAO_DI, ProspectColumnDef_DI, ProspectDAOService_DI, ProspectDAO_DI, ProspectQueryDI, ProspectsEmailsDAO_DI, ReplySentimentDAOService_DI, ReplySentimentDAO_DI, ReplySentimentJedisDAO_DI, SrDBQueryCounterService_DI, SrUuidUtilsDI}

import scala.util.{Failure, Success, Try}

object Test_CampaignDAO extends Logging {

  def main(args: Array[String]): Unit = {

    if (args.length > 0) {
      DBs.setupAll()

      val applicationName = args(0)

      applicationName match {

        case "findAll" =>

          logger.info("BEFORE test")

          object Test extends TestAppTrait {

            def init() = {


              val result = campaignDAO.findAll(
                permittedAccountIds = Seq(90),
                doNotContactCategoryId = 0,
                loggedinAccountId =90,
                showCampaignTags = false,
                teamId = 2,
                includeArchived = false,
                listOfPositiveUuid = List()

              )
              logger.info(s"result $result")
            }
          }
          Test.init()
          logger.info("AFTER test")

        case "findCampaignForCampaignUtilsOnly" =>

          logger.info("BEFORE test")

          object Test extends TestAppTrait {

            def init() = {


              val result = campaignDAO.findCampaignForCampaignUtilsOnly(
                id = 988,
                teamId = TeamId(2),

              )
              logger.info(s"result $result")
            }
          }
          Test.init()
          logger.info("AFTER test")

        case "query" =>

          logger.info("BEFORE test")


          object Test extends TestAppTrait {

            def init() = {

              given Logger: SRLogger= new SRLogger("Test_CampaignDAO")

              val account = accountService.find(id = AccountId(79).id)

              val result = campaignDAO.query(
                account = account.get,
                teamId = 80,
                accountIds = Seq(79),
                orgId = 59,
                loggedinAccountId = 79,

                data = SearchQuery(
                  query = Some(
                    SearchQueryMainClause(
                      search = None,
                      owner_ids = Seq(79),
                      clause = "AND",
                      filters = Seq()
                    )
                  ),
                  page = Option(1),
                  sort = None,
                  is_campaign = None,
                  older_than = None,
                  newer_than = None
                ),
                Logger = new SRLogger("query")
              )
              println(s"result $result")
            }
          }
          Test.init()
          logger.info("AFTER test")

        case "getCampaignBasicInfoV3" =>

          logger.info("BEFORE test")


          object Test extends TestAppTrait {

            def init() = {

              val result = campaignDAO.getCampaignBasicInfoV3(
                teamId = 2,
                accountIds = Seq(90),
                orgId = 2,
                loggedinAccountId = 90,
                search_params = SearchParams(
                  name = None,
                  sender_email_setting = None,
                  receiver_email_setting = None,
                  status = None,
                  range = InferredQueryTimeline.Range.After(DateTime.now().minusDays(100)),
                  is_first = false
                ),
                Logger = new SRLogger("query")
              )
              logger.info(s"result $result")
            }
          }
          Test.init()
          logger.info("AFTER test")

        case "getCampaignBasicInfoV3_sender" =>

          logger.info("BEFORE test")


          object Test extends TestAppTrait {

            def init() = {


              val result = campaignDAO.getCampaignBasicInfoV3(
                teamId = 2,
                accountIds = Seq(90),
                orgId = 2,
                loggedinAccountId = 90,
                search_params = SearchParams(
                  name = None,
                  sender_email_setting = Some("<EMAIL>"),
                  receiver_email_setting = None,
                  status = None,
                  range = InferredQueryTimeline.Range.After(DateTime.now().minusDays(100)),
                  is_first = false
                ),
                Logger = new SRLogger("query")
              )
              logger.info(s"result $result")
            }
          }
          Test.init()
          logger.info("AFTER test")

        case "getCampaignBasicInfoV3_receiver" =>

          logger.info("BEFORE test")


          object Test extends TestAppTrait {

            def init() = {


              val result = campaignDAO.getCampaignBasicInfoV3(
                teamId = 2,
                accountIds = Seq(90),
                orgId = 2,
                loggedinAccountId = 90,
                search_params = SearchParams(
                  name = None,
                  sender_email_setting = None,
                  receiver_email_setting = Some("<EMAIL>"),
                  status = None,
                  range = InferredQueryTimeline.Range.After(DateTime.now().minusDays(100)),
                  is_first = false
                ),
                Logger = new SRLogger("query")
              )
              logger.info(s"result $result")
            }
          }
          Test.init()
          logger.info("AFTER test")

        case "getCampaignBasicInfo" =>

          logger.info("BEFORE test")


          object Test extends TestAppTrait {

            def init() = {


              val result = campaignDAO.getCampaignBasicInfo(
                teamId = 2,
                cId = 984
              )
              logger.info(s"result $result")
            }
          }
          Test.init()
          logger.info("AFTER test")

        case "getCampaignStatsById" =>

          logger.info("BEFORE test")

          object Test extends TestAppTrait {

            def init() = {


              val result = campaignDAO.getCampaignStatsById(
                cId = 234L,
                teamId = 132L,
                doNotContactCategoryId = 0L,
                listOfPositiveUuid = List(),
                campaignHasEmailStep = true,
                Logger = new SRLogger("getCampaignStatsById")
              )
              logger.info(s"result $result")
              result match {
                case Success(value) =>
                  logger.info(s"CountOfInvalidEmails: ${value.current_failed_email_validation}")
                case Failure(exception) =>
                  logger.info(s"Failure  $exception")
              }
            }
          }
          Test.init()
          logger.info("AFTER test")


        case "testing_production_failure" =>

          val team_id = 15
          val campaign_id = 303


          val a = DB readOnly { implicit session =>

            sql"""
                 |select id,(
                 |        SELECT json_agg(
                 |          json_build_object(
                 |            'channel_setting_uuid', ccs.channel_settings_uuid,
                 |            'team_id', c.team_id,
                 |            'email', ls.email,
                 |            'first_name', ls.first_name,
                 |            'last_name',  ls.last_name,
                 |            'linkedin_profile_url', ls.profile_url,
                 |            'automation_enabled', ls.captain_data_account_id IS NOT NULL
                 |          )) AS campaign_linkedin_settings
                 |        FROM linkedin_settings ls
                 |        LEFT JOIN campaign_channel_settings ccs on ( ccs.channel_settings_uuid = ls.uuid AND ccs.channel_type = ${ChannelType.LinkedinChannel.toString})
                 |        WHERE ccs.team_id = c.team_id AND ccs.campaign_id = c.id
                 | ) AS campaign_linkedin_settings
                 |
                 | from campaigns c
                 | where c.id = ${campaign_id} and c.team_id = ${team_id}
                 |""".stripMargin
              .map(rs =>{

                val campaign_linkedin_settings = rs
                  .anyOpt("campaign_linkedin_settings")
                  .map(r => Json
                    .parse(r.asInstanceOf[PGobject].getValue)
                    .validate[List[LinkedinSettingSenderDetails]].get
                  )
                  .getOrElse(List())

                campaign_linkedin_settings

              }
            )
              .single
              .apply()

          }


          println(s"result after : ${a}")


        case "findOneForSendingTests" =>

          object Test extends TestAppTrait {
            def init() = {
              Try{
                spamTestDAO.findOneForSendingTests()
              } match {
                case Success(value) => println(s"Success $value")
                case Failure(err) => println(s"failed err - $err")
              }
            }
          }

          Test.init()


        case "getSpamTestsDoneInCurrentCycle" =>

          object Test extends TestAppTrait {
            def init() = {
              Try{
                spamTestDAO.getSpamTestsDoneInCurrentCycle(orgId = 59)
              } match {
                case Success(value) => println(s"Success $value")
                case Failure(err) => println(s"failed err - $err")
              }
            }
          }

          Test.init()



        case "findTeamInboxesForAccount" =>

          object Test extends TestAppTrait {
            def init() = {
               teamInboxDAO.findTeamInboxesForAccount(teamId = 59, account_id = 123)  match {
                case Success(value) => println(s"Success $value")
                case Failure(err) => println(s"failed err - $err")
              }
            }
          }

          Test.init()


        case "getTeamInboxDetails" =>

          object Test extends TestAppTrait {
            def init() = {
              teamInboxDAO.getTeamInboxDetails(org_id = 59, team_inbox_id = 123)  match {
                case Success(value) => println(s"Success $value")
                case Failure(err) => println(s"failed err - $err")
              }
            }
          }

          Test.init()


        case "getAllTeamInboxesInTeam" =>

          object Test extends TestAppTrait {
            def init() = {
              teamInboxDAO.getAllTeamInboxesInTeam(org_id = 59, team_id = 123)  match {
                case Success(value) => println(s"Success $value")
                case Failure(err) => println(s"failed err - $err")
              }
            }
          }

          Test.init()


        case "getTeamInboxForSync" =>

          object Test extends TestAppTrait {
            def init() = {
              
              given logger: SRLogger = new SRLogger("getTeamInboxForSync")

              teamInboxDAO.getTeamInboxForSync()  match {
                case Success(value) => println(s"Success $value")
                case Failure(err) => println(s"failed err - $err")
              }
            }
          }

          Test.init()



      }
    }

  }

}

