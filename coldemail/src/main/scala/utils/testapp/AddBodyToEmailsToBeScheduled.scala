package utils.testapp

import api.CacheServiceJedis
import api.accounts.TeamId
import api.accounts.dao.AccountOrgBillingRelatedInfoDAO
import api.accounts.service.AccountOrgBillingRelatedService
import api.campaigns.CampaignProspectDAO
import api.campaigns.dao.{CampaignProspectDAO_2, CampaignSchedulingMetadataDAO}
import api.campaigns.services.MergeTagService
import api.columns.{ProspectColumnDef, ProspectColumnDefDAO}
import api.emails.dao_service.EmailScheduledDAOService
import api.emails.{EmailMessageDataDAO, EmailScheduledDAO, EmailScheduledNewStep2, EmailSettingDAO, EmailThreadDAO}
import api.free_email_domain.dao.FreeEmailDomainListDAO
import api.prospects.dao.{ProspectAddEventDAO, ProspectDAO, ProspectsEmailsDAO}
import api.prospects.dao_service.ProspectDAOService
import api.prospects.service.ProspectServiceV2
import api.search.ProspectQuery
import api.tasks.pgDao.TaskPgDAO
import api.tasks.services.LinkedinTaskService
import api.team_inbox.dao.{ReplySentimentDAO, ReplySentimentJedisDAO}
import api.team_inbox.dao_service.ReplySentimentDAOService
import app_services.db_query_counter.SrDBQueryCounterService
import app_services.db_query_counter.dao.DBCounterDAO
import play.api.Logging
import scalikejdbc.{DB, scalikejdbcSQLInterpolationImplicitDef}
import scalikejdbc.config.DBs
import utils.SRLogger
import utils.dbutils.DBUtils
import utils.emailvalidation.EmailValidationModel
import utils.uuid.SrUuidUtils

import scala.util.Try

object AddBodyToEmailsToBeScheduled extends Logging {

  def main(args: Array[String]): Unit = {

    if (args.length > 0) {
      DBs.setupAll()

      val applicationName = args(0)

      applicationName match {

        case "test_addBodyToEmailsToBeScheduled" =>

          given Logger: SRLogger = new SRLogger("test_addBodyToEmailsToBeScheduled")
          
          object main extends TestAppTrait {
            def init() = {
              val selected_es = DB readOnly { implicit session =>
                sql"""
           select * from emails_scheduled where team_id = 80 and account_id = 393 and scheduled_at > now() - interval '1 days'
         """
                  .map(rs => EmailScheduledNewStep2(
                    email_scheduled_id = rs.long("id"),
                    subject = rs.string("subject"),
                    body = rs.string("body"),
                    base_body = rs.string("base_body"),
                    text_body = rs.string("text_body"),

                    has_open_tracking = rs.booleanOpt("has_open_tracking").getOrElse(false),
                    has_click_tracking = rs.booleanOpt("has_click_tracking").getOrElse(false),
                    has_unsubscribe_link = rs.booleanOpt("has_unsubscribe_link").getOrElse(false),
                    is_edited_preview_email = rs.booleanOpt("is_edited_preview_email").getOrElse(false),

                    list_unsubscribe_header = rs.stringOpt("list_unsubscribe_header"),
                    gmail_fbl = rs.stringOpt("gmail_fbl"), //gmailFBLHeader
                  ))
                  .list
                  .apply()
              }

              val result: Try[Seq[Long]] = emailScheduledDAOService.addBodyToEmailsToBeScheduled(
                emails = selected_es,
                teamId = TeamId(id = 80L),
                Logger = Logger
              )

              println(result)
            }
          }
          
          main.init()
      }
    }

  }

}