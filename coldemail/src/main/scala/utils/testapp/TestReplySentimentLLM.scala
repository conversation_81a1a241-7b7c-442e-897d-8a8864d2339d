package utils.testapp

import api.accounts.TeamId
import api.emails.{ERIntermediateValidProspect, EmailReplySavedV3}
import api.llm.dao.LlmAuditLogDAO
import api.prospects.models.ProspectCategory
import api.sr_ai.models.AiApiOptions
import api.team_inbox.model.ReplySentimentChannelType.AllChannelSentiments
import api.team_inbox.model.ReplySentimentTypeData
import api.team_inbox.service.ReplySentimentForTeam
import eventframework.MessageObject
import eventframework.MessageObject.EmailMessageObject
import io.smartreach.esp.api.emails.IEmailAddress
import org.apache.pekko.actor.ActorSystem
import org.joda.time.DateTime
import play.api.Logging
import play.api.libs.json.{JsValue, Json}
import play.api.libs.ws.ahc.AhcWSClient
import scalikejdbc.config.DBs
import utils.SRLogger
import utils.email.EmailReplyStatus
import utils.helpers.LogHelpers

import scala.concurrent.{Await, ExecutionContext, Future}
import scala.concurrent.duration.*
import scala.util.{Failure, Success, Try}
import java.io.File

object TestReplySentimentLLM extends Logging {

  def main(args: Array[String]): Unit = {

    if (args.length > 0) {

      DBs.setupAll()

      val applicationName = args(0)

      applicationName match {

        case "testReplySentimentAnalysis" =>

          object main extends TestAppTrait {

            def init() = {
              implicit val system: ActorSystem = ActorSystem()
              implicit val wSClient: AhcWSClient = AhcWSClient()
              implicit val actorContext: ExecutionContext = system.dispatcher

              given logger: SRLogger = new SRLogger("TestReplySentimentLLM")

              // Create a demo-interested email
              val demoEmail = createDemoRequestEmail()

              // Create AI configuration for Gemini
              val aiConfig = AiApiOptions.getGoogleAiApiOptions()
              println(s"Created AI config with model: ${aiConfig.model}")

              // Call the sentiment analysis method
              println("Calling analyzeEmailConversation...")
              
              // Wrap in try-catch to handle errors
              Try {
                val resultFuture = srAiApi.analyzeEmailConversation(
                  messages = demoEmail,
                  aiConfig = aiConfig,
                  teamId = TeamId(0), primaryProspectId = 0L,
                  replySentimentsForTeam = List("Interested", "Not Interested")
                )
                
                // Wait for the result
                val timeout = 30.seconds
                println(s"Waiting up to $timeout for result...")
                
                // Use Await.ready to wait without throwing exceptions on timeout
                Await.ready(resultFuture, timeout)
                
                // Handle the result
                resultFuture.value match {
                  case Some(data) =>
                    
                    val sentimentData = data.get.toOption.get._1.get

                    val reply_sentimments_for_team: List[ReplySentimentForTeam] = replySentimentService.getReplySentimentsForTeam(
                      team_id = 60, // update team id according to your DB
                      reply_sentiment_channel_type = AllChannelSentiments
                    ).get

                    val selectedSentiment = reply_sentimments_for_team.find { p =>

                      println(s"DB Checking sentiment: ${p.reply_sentiment.getReplySentimentType} :: ${p.reply_sentiment.getDisplayNameForType}")

                      println(s"Checking sentiment: ${sentimentData.getReplySentimentType} :: ${sentimentData.getDisplayNameForType} ")

                      p.reply_sentiment.getDisplayNameForType == sentimentData.getDisplayNameForType &&
                        p.reply_sentiment.getReplySentimentType == sentimentData.getReplySentimentType &&
                        p.reply_sentiment.getSubcategoryName == sentimentData.getSubcategoryName
                    }


                    println(s"Selected sentiment: ${selectedSentiment.get.uuid}")

                    println("========== SENTIMENT ANALYSIS RESULT ==========")
                    println(s"Sentiment Type: ${sentimentData.getReplySentimentType}")
                    println(s"Sentiment Name: ${sentimentData.getSubcategoryName}")
                    println(s"Sentiment DATA: ${sentimentData}")
                    println("===============================================")
                  case None =>
                    println("No sentiment detected in the email")
                }
              } match {
                case Success(_) => //Do nothing
                case Failure(e) =>
                  println(s"Exception running analysis: ${e.getMessage}")
                  e.printStackTrace()
              }

              println("Test completed")
              
              // Shutdown system after test
              system.terminate()
            }
          }

          main.init()
      }
    }
  }

  /*
   * Creates a sequence of demo messages for testing the conversation analysis
   */
  private def createDemoRequestEmail(): Seq[EmailMessageObject] = {
    val now = DateTime.now()
    
    // Create a sequence of messages representing a conversation
    Seq(
      // Initial outreach from sales team
      MessageObject.EmailMessageObject(
        uuid = Some(java.util.UUID.randomUUID().toString),
        from_user = true,
        from = IEmailAddress(email = "<EMAIL>", name = Some("Sales Team")),
        reply_to = None,
        to = Seq(IEmailAddress(email = "<EMAIL>", name = Some("John Smith"))),
        cc_emails = Some(Seq()),
        bcc_emails = Some(Seq()),
        subject = "Boost Your Team's Energy with Our Fresh Fruit Juices!",
        body =
          """
            |Dear Finance Team,
            |
            |I hope this message finds you well. In the fast-paced world of banking, maintaining energy and focus is crucial. That's why we're excited to introduce our premium range of fresh fruit juices, designed to invigorate your team and enhance productivity.
            |
            |Our juices are crafted from the finest, locally-sourced fruits, ensuring a burst of natural energy and essential nutrients in every sip. With no added sugars or preservatives, they offer a healthy alternative to traditional office beverages, promoting wellness and vitality among your staff.
            |
            |Imagine the convenience of having a refreshing, health-boosting drink available right in your office. Our flexible delivery options ensure that your team can enjoy these benefits without any hassle, keeping them refreshed and ready to tackle the day’s challenges.
            |
            |By choosing our fresh fruit juices, you’re not just investing in a product; you’re investing in the well-being and efficiency of your team. Let us help you create a healthier, more dynamic work environment.
            |
            |We’d love to discuss how we can tailor our offerings to meet your needs. Please feel free to reach out for more information or to arrange a tasting session.
            |
            |Warm regards,
            |Vivek
            |""".stripMargin,
        body_preview = "Dear Finance Team,...",
        sent_at = now.minusDays(3)
      ),
      
      // Prospect's reply showing interest
      MessageObject.EmailMessageObject(
        uuid = Some(java.util.UUID.randomUUID().toString),
        from_user = false,
        from = IEmailAddress(email = "<EMAIL>", name = Some("John Smith")),
        reply_to = Some(IEmailAddress(email = "<EMAIL>", name = Some("Sales Team"))),
        to = Seq(IEmailAddress(email = "<EMAIL>", name = Some("Sales Team"))),
        cc_emails = Some(Seq()),
        bcc_emails = Some(Seq()),
        subject = "Re: Boost Your Team's Energy with Our Fresh Fruit Juices!",
        body =
          """
            |Dear Vivek,
            |
            |Thank you for reaching out and for sharing this exciting initiative with us.
            |
            |Your range of fresh fruit juices sounds like a fantastic way to support wellness and boost energy in the workplace. We’re always looking for ways to enhance our team’s well-being, and your offering aligns perfectly with that goal.
            |
            |We would be very interested in arranging a demo or tasting session to explore the options further. Please let us know a convenient time and any details we should prepare in advance.
            |
            |Looking forward to it!
            |
            |Best regards,
            |Neomi
            |
            |
            |""".stripMargin,
        body_preview = "Dear Vivek...",
        sent_at = now.minusDays(2)
      )
    )
  }
} 