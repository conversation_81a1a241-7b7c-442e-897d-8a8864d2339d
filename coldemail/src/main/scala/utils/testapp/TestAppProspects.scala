package utils.testapp

import api.accounts.{AccountUuid, TeamId}
import api.columns.ProspectColumnDefUtils
import api.prospects.dao.{ProspectDAO, ProspectsEmailsDAO}
import api.prospects.{ProspectBounceResponseInfo, ProspectUuid}
import api.reports.DownloadReportDAO
import api.team_inbox.service.ReplySentimentUuid
import api.triggers.TriggerFields
import eventframework.{ProspectObject, ProspectObjectInternal}
import org.apache.pekko.actor.ActorSystem
import org.joda.time.DateTime
import play.api.Logging
import play.api.libs.json.Json
import scalikejdbc.config.DBs
import sr_scheduler.models.ChannelType
import utils.SRLogger
import utils.TestApp.logger
import utils.cronjobs.spammonitor.ActiveCampaignImpl
import utils.dbutils.DBUtils
import utils.sr_csv_parser.CustomCSVParser
import utils.testapp.TestAppTrait

import scala.concurrent.ExecutionContext
import scala.util.{Failure, Success}

/*
This object holds any sort internal test function used to test features/changes manually during development.
 */
object TestAppProspects extends Logging {

  def main(args: Array[String]): Unit = {

    if (args.length > 0) {
      DBs.setupAll()


      val applicationName = args(0)

      applicationName match {



        /*
        case "prachi_prospects_emails_reads"=>
          val prospectColumnDef = new ProspectColumnDef (new ProspectColumnDefDAO)
          val prospectQuery = new  ProspectQuery(
            prospectColumnDef = prospectColumnDef
          )
          val prospectAddEventDAO = new  ProspectAddEventDAO
          val cacheServiceJedis = new  CacheServiceJedis

          val campaignProspectDAO_2 = new CampaignProspectDAO_2

          val prospectsEmailsDAO = new ProspectsEmailsDAO
          val prospectDAO = new ProspectDAO
          val campaignSchedulingMetadataDAO = new CampaignSchedulingMetadataDAO

          val mergeTagService = new MergeTagService(
            campaignProspectDAO_2 = campaignProspectDAO_2,
            campaignSchedulingMetadataDAO = campaignSchedulingMetadataDAO
          )
          val replySentimentDAO = new ReplySentimentDAO
          val replySentimentJedisDAO = new ReplySentimentJedisDAO(
            cacheServiceJedis = cacheServiceJedis
          )
          val replySentimentDAOService = new ReplySentimentDAOService(
            replySentimentDAO = replySentimentDAO,
            replySentimentJedisDAO = replySentimentJedisDAO
          )

          val dbCounterDAO = new DBCounterDAO(cacheServiceJedis = cacheServiceJedis)

          val srDBQueryCounterService = new SrDBQueryCounterService(
            dbCounterDAO = dbCounterDAO
          )

          val taskPgDAO = new TaskPgDAO(
            srDBQueryCounterService = srDBQueryCounterService
          )

          val dbUtils = new DBUtils
          val emailThreadDAO = new EmailThreadDAO
          val linkedinTaskService = new LinkedinTaskService(
            taskPgDAO = taskPgDAO
          )

          val prospectDAOService = new ProspectDAOService(
            prospectDAO = prospectDAO,
            prospectQuery = prospectQuery,
            linkedinTaskService = linkedinTaskService,
            prospectColumnDef = prospectColumnDef,
            mergeTagService = mergeTagService,
            replySentimentDAOService = replySentimentDAOService,
            emailThreadDAO = emailThreadDAO,
            dbUtils = dbUtils
          )

          val emailValidationModel = new EmailValidationModel

          val accountOrgBillingRelatedInfoDAO = new AccountOrgBillingRelatedInfoDAO()

          val accountOrgBillingRelatedService = new AccountOrgBillingRelatedService(accountOrgBillingRelatedInfoDAO)

          val prospectServiceV2 = new ProspectServiceV2(
            prospectDAOService = prospectDAOService,
            prospectsEmailsDAO = prospectsEmailsDAO,
            prospectAddEventDAO = prospectAddEventDAO,
            prospectColumnDef = prospectColumnDef,
            campaignProspectDAO_2 = campaignProspectDAO_2,
            accountOrgBillingRelatedService = accountOrgBillingRelatedService,
            mergeTagService = mergeTagService,
            emailValidationModel = emailValidationModel
          )

          given logger = new SRLogger("testApp prachi_prospects_emails_reads")

          val res1 = prospectServiceV2.findProspectIdsMatchingBlacklistedEmailsAndDomains(47,List(),List("somedomain.com"),List())(Logger = logger)
//          val res2 = prospectDAO.findProspectIdsMatchingExcludedEmailsAndDNC(47L, List("<EMAIL>"), 359, logger)
//          val res3 = prospectDAO.findFromMaster(47L, 4418245, Logger = logger)
//          val res4 = prospectDAO.findOneForZapierSample(Seq(49), 47, EventType.PROSPECT_COMPLETED, logger)
//          val res5 = prospectDAO.findOneForZapierUpdatedProspectCategorySample(Seq(49), 47, logger)
//          val res6 = prospectDAO.findOneForZapierEmailInvalidSample(Seq(49), 47, logger)

          val prospectUpdateFormData = ProspectUpdateFormData(
                                                              email = "<EMAIL>",
                                                              first_name = Some("Prachi"),
                                                              last_name = Some("Mane"),
                                                              custom_fields = Json.obj(),

                                                              prospect_category_id_custom = None,

                                                              list = None,
                                                              company = None,
                                                              city = None,
                                                              country = None,
                                                              timezone = None,

                                                              state = None,
                                                              job_title = None,
                                                              phone = None,
                                                              linkedin_url = None,

                                                              prospect_account_id = Some(2100745)
                                                              )

          val res7 = prospectServiceV2.updateForEmail(Seq(49), 49, 47, "<EMAIL>", prospectUpdateFormData, logger)

          val res8 = prospectServiceV2.fetchProspectsByProspectCategoryIdCustom(359, 47, logger)

          val res9 = prospectServiceV2.checkIfUserHasPermissionForProspectsByEmail(Seq("<EMAIL>", "<EMAIL>"), Seq(49), 47, logger)
          val res10 = prospectServiceV2.checkIfUserHasPermissionForProspectsByEmail(Seq(), Seq(49), 47, logger)


          val res11 = prospectServiceV2.fetchProspectsByIdsForBatchUpdate(Seq(4418244), Seq(49), 47, logger)

          val res13 = prospectServiceV2.getProspectBasicDetailsById(Seq(4418244), 47, logger)
          val res12 = prospectServiceV2.getProspectFullNameById(Seq(4418244), 47, logger)

          val res12empty = prospectServiceV2.getProspectFullNameById(Seq(), 47, logger)
          val res13empty = prospectServiceV2.getProspectBasicDetailsById(Seq(), 47, logger)

          val res14 = prospectServiceV2.hasAlreadyValidatedButNotSentProspects(47, logger)

          val res15 = prospectServiceV2._hasOpened(4418244, DateTime.now(), logger)
          val res16 = prospectServiceV2._hasClicked(4418242, DateTime.now(), logger)

          val res17 = prospectServiceV2.prospectForceSentCount(49, 47)

          logger.info(s"findProspectIdsMatchingBlacklistedEmailsAndDomains res: $res1\n")
          logger.info(s"updateForEmail res: $res7\n")

          logger.info(s"fetchProspectsByProspectCategoryIdCustom res: $res8")

          logger.info(s"checkIfUserHasPermissionForProspectsByEmail res: $res9")
          logger.info(s"checkIfUserHasPermissionForProspectsByEmail Empty res: $res10")

          logger.info(s"fetchProspectsByIdsForBatchUpdate res: $res11")
          logger.info(s"getProspectFullNameById res: $res12")
          logger.info(s"getProspectBasicDetailsById res: $res13")
          logger.info(s"getProspectFullNameById empty! res: $res12empty")
          logger.info(s"getProspectBasicDetailsById empty! res: $res13empty")

          logger.info(s"hasAlreadyValidatedButNotSentProspects res: $res14")

          logger.info(s"_hasOpened res: $res15")
          logger.info(s"_hasOpened res: $res16")
          logger.info(s"prospectForceSentCount res: $res17")

        case "createFromUploadMultiRowInsertNew" =>
/*
          val prospectColumnDef = new ProspectColumnDef
          val prospectQuery = new  ProspectQuery(
            prospectColumnDef = prospectColumnDef
          )
          val prospectAddEventDAO = new  ProspectAddEventDAO
          val campaignProspectDAO_2 = new CampaignProspectDAO_2

          val prospectDAO = new Prospect(prospectQuery = prospectQuery,
            prospectAddEventDAO = prospectAddEventDAO,
            prospectColumnDef = prospectColumnDef,
            campaignProspectDAO_2 = campaignProspectDAO_2
          )

          val resultEmpty = prospectDAO.updateProspects(
            ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads = false,
            prospectCustomColumnDefs = Seq(),
            listIdOpt = None,
            prospectsToBeUpdated = Iterable(),
            teamId = 47
          )

          val columnDef = ColumnDef(
            id = Some(101),
            created_at = Some(DateTime.now()),
            display_name = "custom input",
            name = "custom input",
            field_type = FieldTypeEnum.TEXT,
          )

          val prospectCreateFormdata = ProspectCreateFormData(
            email = "<EMAIL>",
            first_name = Some("test1"),
            last_name = Some("test1"),
            custom_fields = Json.obj("custom input" -> "custom input"),

            owner_id = Some(49),

            list = None,
            company = Some("testcompany"),
            city = Some("kolkata"),
            country = Some("India"),
            timezone = Some("Asia/Kolkata"),
            created_at = Some(DateTime.now().minusDays(1)),

            state = None,
            job_title = None,
            phone = None,
            linkedin_url = None
          )

          val result1 = prospectDAO.updateProspects(
            ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads = false,
            prospectCustomColumnDefs = Seq(columnDef),
            listIdOpt = None,
            prospectsToBeUpdated = Iterable((prospectCreateFormdata, 4418254)),
            teamId = 47
          )

          val result2 = prospectDAO.updateProspects(
            ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads = true,
            prospectCustomColumnDefs = Seq(columnDef),
            listIdOpt = None,
            prospectsToBeUpdated = Iterable((prospectCreateFormdata.copy(email = "<EMAIL>", first_name = Some("")), 4418253)),
            teamId = 47
          )

          val logger = new SRLogger("TestApp createFromUploadMultiRowInsertNew")

          logger.info(s"updateProspects resultEmpty: $resultEmpty")
          logger.info(s"updateProspects ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads false: $result1")
          logger.info(s"updateProspects ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads true: $result2")
*/
*/


        /*
        case "scheduler_test" =>

//          UploadCsvCronService.start()

          MQReplyTracker.publish(MQReplyTrackerMessage(emailSettingId = 275, senderMessageIdSuffix = "mail@aN93aiSrkyierDWBW9xJMA")) match {

            case Failure(e) => Logger.error(s"[ReadEmailCronService] executeOnce error: emailSettingId:  :: ${Helpers.getStackTraceAsString(e)}")

            case Success(_) => Logger.info(s"[ReadEmailCronService] executeOnce success: emailScheduledId")

          }
          */



        case "csvUploadExecuteCron" =>

          given srLogger: SRLogger = new SRLogger("::::testApp upload csv executeCron::::")

          object CSU
            extends TestAppTrait {

            def mainFn() = {

              uploadCsvCronService.executeCron()


            }

          }

          val result = CSU
            .mainFn()

        case "srcsv" =>

          implicit val  system: ActorSystem = ActorSystem()
          implicit val  actorContext: ExecutionContext = system.dispatcher

          //          val file1 = "https://sr-prospects-csv.s3.amazonaws.com/1627902257527_family%20law%20attorney_email-new-003.csv"
          //          val file2 = "https://sr-prospects-csv.s3.amazonaws.com/1633689088668_smartreach%20oct%203.csv"

          //          val file3 = "https://sr-prospects-csv.s3.amazonaws.com/1633668806822_Liste%20FE.csv"
          //
          val logger = new SRLogger("srcsv test")
          //
          //          new CustomCSVParser().downloadAndParseCSVFile(
          //            csvFileUrl = file1,
          //            logger = logger
          //          ) match {
          //
          //            case Failure(e) =>
          //              Logger.error(s"Error while parsing: $e")
          //
          //            case Success(data) =>
          //              Logger.info(s"Successfully parsed, errorMessage: ${data.errorMsgForUser} :: len: ${data.parserResult.parsed_data.length}")
          //
          //          }


          new CustomCSVParser().parseFileUsingExecutable(
            localFilePath = "/Users/<USER>/Documents/projects/csv-parser-flex-bison/csv-test-files/field-length-exceeded-error.csv"
            //            logger = logger
          ) match {

            case Failure(e) =>
              logger.error(s"Error while parsing: $e")

            case Success(data) =>
              logger.info(s"Successfully parsed, ${data.length}")

          }

        /*
      case "updateEmailValidationData" =>
        val prospectColumnDef = new ProspectColumnDef
        val prospectQuery = new  ProspectQuery(
          prospectColumnDef = prospectColumnDef
        )
        val prospectAddEventDAO = new  ProspectAddEventDAO
        val freeEmailDomainListDAO = new  FreeEmailDomainListDAO
        val cacheServiceJedis = new  CacheServiceJedis
        val freeEmailDomainListService = new  FreeEmailDomainListService(
          freeEmailDomainListDAO = freeEmailDomainListDAO,
          cacheServiceJedis = cacheServiceJedis)
        val prospectAccountDAO1 = new  ProspectAccountDAO1(freeEmailDomainListService = freeEmailDomainListService)

        val prospectsEmailsDAO = new ProspectsEmailsDAO
        val prospectDAO = new Prospect(prospectQuery = prospectQuery,
          prospectAddEventDAO = prospectAddEventDAO,
//            prospectAccountDAO1 = prospectAccountDAO1,
          prospectColumnDef = prospectColumnDef,
//            prospectsEmailsDAO = prospectsEmailsDAO
        )
        val data: MBLApiResponse = MBLApiResponse(
          email = "<EMAIL>",
          did_you_mean = "<EMAIL>",
          format_valid = true,
          user = Some("email"),
          domain = Some("email.in"),
          mx_found = Some(false),
          smtp_check = false,
          catch_all = Some(false),
          role = Some(true),
          disposable = Some(false),
          free = Some(true),
          score = 111.1f
        )

        val prospectServiceV2 = new ProspectServiceV2(
          prospectDAO = prospectDAO,
          prospectsEmailsDAO = prospectsEmailsDAO,
          prospectAddEventDAO = prospectAddEventDAO,
          prospectColumnDef = prospectColumnDef
        )


        prospectServiceV2.updateEmailValidationData(data,47, false, 130L, new SRLogger("testapp"))
*/

        /*
        case "updateEmailValidationDataV2" =>
          val prospectColumnDef = new ProspectColumnDef(new ProspectColumnDefDAO)
          val prospectQuery = new  ProspectQuery(
            prospectColumnDef = prospectColumnDef
          )
          val prospectAddEventDAO = new  ProspectAddEventDAO
          val freeEmailDomainListDAO = new  FreeEmailDomainListDAO
          val cacheServiceJedis = new  CacheServiceJedis

          val campaignProspectDAO_2 = new CampaignProspectDAO_2
          val prospectsEmailsDAO = new ProspectsEmailsDAO
          val campaignSchedulingMetadataDAO = new CampaignSchedulingMetadataDAO

          val mergeTagService = new MergeTagService(
            campaignProspectDAO_2 = campaignProspectDAO_2,
            campaignSchedulingMetadataDAO = campaignSchedulingMetadataDAO
          )
          val replySentimentDAO = new ReplySentimentDAO
          val replySentimentJedisDAO = new ReplySentimentJedisDAO(
            cacheServiceJedis = cacheServiceJedis
          )
          val replySentimentDAOService = new ReplySentimentDAOService(
            replySentimentDAO = replySentimentDAO,
            replySentimentJedisDAO = replySentimentJedisDAO
          )

          val prospectDAO = new ProspectDAO
          val dbUtils = new DBUtils
          val emailThreadDAO = new EmailThreadDAO

          val dbCounterDAO = new DBCounterDAO(cacheServiceJedis)
          val srDBQueryCounterService = new SrDBQueryCounterService(dbCounterDAO = dbCounterDAO)

          val taskPgDAO = new TaskPgDAO(
            srDBQueryCounterService = srDBQueryCounterService
          )

          val linkedinTaskService = new LinkedinTaskService(
            taskPgDAO = taskPgDAO
          )

          val prospectDAOService = new ProspectDAOService(
            prospectDAO = prospectDAO,
            prospectQuery = prospectQuery,
            linkedinTaskService = linkedinTaskService,
            prospectColumnDef = prospectColumnDef,
            mergeTagService = mergeTagService,
            replySentimentDAOService = replySentimentDAOService,
            emailThreadDAO = emailThreadDAO,
            dbUtils = dbUtils
          )

          val data = Seq(ProspectEmailValidationResultWithTeamIdAndAnalysisId(
            emailDeliveryAnalysisId = Some(EmailDeliveryAnalysisId(1234L)),
            teamId = TeamId(id = 123L),
            email = "<EMAIL>",
            isValid = false,
            emailValidationId = 132L
          ))
          val data2 = Seq()

          val emailValidationModel = new EmailValidationModel

          val accountOrgBillingRelatedInfoDAO = new AccountOrgBillingRelatedInfoDAO()

          val accountOrgBillingRelatedService = new AccountOrgBillingRelatedService(accountOrgBillingRelatedInfoDAO)

          val prospectServiceV2 = new ProspectServiceV2(
            prospectDAOService = prospectDAOService,
            prospectsEmailsDAO = prospectsEmailsDAO,
            prospectAddEventDAO = prospectAddEventDAO,
            prospectColumnDef = prospectColumnDef,
            campaignProspectDAO_2 = campaignProspectDAO_2,
            accountOrgBillingRelatedService = accountOrgBillingRelatedService,
            mergeTagService = mergeTagService,
            emailValidationModel = emailValidationModel
          )

          val Logger = new SRLogger("TestApp updateEmailValidationDataV2")

          val res1 = prospectServiceV2.updateEmailValidationDataV2(data, Logger)
          val res2 = prospectServiceV2.updateEmailValidationDataV2(data2, Logger)

          Logger.info(s"ProspectEmailValidationResult: $res1 || Empty res: $res2")


        case "dropEmailsScheduledNotPartOfAnyTeam" =>
          object main extends TestAppTrait {
            def init() = {
              given Logger = new SRLogger("dropEmailsScheduledNotPartOfAnyTeam")
              migrationUtils.dropEmailsScheduledNotPartOfAnyTeam() match {
                case Right(value) => Logger.debug(s"SUCCESS dropEmailsScheduledNotPartOfAnyTeam")
                case Left(err) =>
                  err match {
                    case RecursiveMigrationMethodError.GetIdsForMigrationError(error) =>
                      Logger.fatal(s"Failed to get ids for deleting", error)
                    case RecursiveMigrationMethodError.UpdateTheIdsError(error) =>
                      Logger.fatal(s"Failed to delete selected ids", error)

                  }
              }
            }
          }


        case "dropEmailsMessageDataBefore22Jan" =>
          object main extends TestAppTrait {
            def init() = {
              given Logger = new SRLogger("dropEmailsMessageDataBefore22Jan")
              migrationUtils.deleteFromEmailMessageDataBefore22Jan() match {
                case Success(value) => Logger.info("Successfully deleted data")
                case Failure(exception) => Logger.fatal("Failed to delete", exception)
              }
              }
            }

          main.init()
*/



        case "animesh_prospects_emails_dao" =>

          val prospectsEmailsDAO = new ProspectsEmailsDAO
          given Logger: SRLogger = new SRLogger("AnimeshTestApp")
          val prospectBounceResponseInfo = ProspectBounceResponseInfo(prospectId = 4418178, prospectEmail = "<EMAIL>", bouncedAt = DateTime.now(), campaignId = None, bounceType = None, bounceReason = None)
          val downloadReportDAO = new DownloadReportDAO
          val activeCampaignImpl = new ActiveCampaignImpl
          val result1 = prospectsEmailsDAO.markAsInvalidV2(id = 4418178, team_id = 58)
          val result2 = prospectsEmailsDAO.markAsSentForValidatingEmailV2(Seq((4418178, "<EMAIL>")), TeamId(58))
          //          val result3 = prospectsEmailsDAO.updateProspectEmailHadBouncedEarlierV2( "<EMAIL>",  58)(Logger)
          val result4 = prospectsEmailsDAO._updateEmailValidationDataAfterHardBouncingV3_PEV2(Seq(prospectBounceResponseInfo), 58)
          val result5 = prospectsEmailsDAO.markProspectAsValidToForceSendEmailsV2(List(4418178L), 58)
          val result6 = prospectsEmailsDAO.assignToTeamMemberV2(List(4418178L), 49, 58)
          val result7 = prospectsEmailsDAO.forceChangeOwnershipFromUploadV2(49, 58, List(4418178L))
          val result8 = prospectsEmailsDAO.findByIdOrEmailV2(Seq(4418179L), Seq("<EMAIL>"), 58, Logger)
          val result9 = prospectsEmailsDAO.forceChangeOwnershipFromUploadV2(49, 58, List())
          val result10 = prospectsEmailsDAO.assignToTeamMemberV2(List(), 49, 58)
          val result11 = prospectsEmailsDAO.markProspectAsValidToForceSendEmailsV2(List(), 58)
          val result12 = prospectsEmailsDAO.markAsSentForValidatingEmailV2(Seq(), TeamId(58))
          val result13 = prospectsEmailsDAO._updateEmailValidationDataAfterHardBouncingV3_PEV2(Seq(), 58)
          val result14 = prospectsEmailsDAO.findByIdOrEmailV2(Seq(), Seq(), 58, Logger)
          val result15 = downloadReportDAO.getDownloadReportData(campaignIds = Seq(132), accountIds = Seq(49, 80), 58, till = DateTime.now().getMillis, from = DateTime.now().minusMonths(5).getMillis)
          val result16 = activeCampaignImpl.getEmailSendResultForBounceCheck(132, 58)
          val result17 = downloadReportDAO.getProspectEngagementReportData(58, Seq(49, 80), Seq(122, 123), DateTime.now().minusMonths(5).getMillis, DateTime.now().getMillis)

          Logger.info(s"Result markAsInvalidV2 ____________________________ $result1")

          Logger.info(s"Result markAsSentForValidatingEmailV2 ____________________________ $result2")
          //          Logger.info(s"Result updateProspectEmailHadBouncedEarlierV2 ____________________________ $result3")
          Logger.info(s"Result _updateEmailValidationDataAfterBouncingV3_PEV2 ____________________________ $result4")

          Logger.info(s"Result markProspectAsValidToForceSendEmailsV2 ____________________________ $result5")

          Logger.info(s"Result assignToTeamMemberV2 ____________________________ $result6")

          Logger.info(s"Result forceChangeOwnershipFromUpload ____________________________ $result7")

          Logger.info(s"Result findByIdOrEmailV2 ____________________________ $result8")

          Logger.info(s"Result forceChangeOwnershipFromUpload EMPTY ____________________________ $result9")

          Logger.info(s"Result assignToTeamMemberV2 EMPTY ____________________________ $result10")

          Logger.info(s"Result markProspectAsValidToForceSendEmailsV2 EMPTY ____________________________ $result11")

          Logger.info(s"Result markAsSentForValidatingEmailV2 EMPTY ____________________________ $result12")

          Logger.info(s"Result _updateEmailValidationDataAfterBouncingV3_PEV2 EMPTY ____________________________ $result13")

          Logger.info(s"Result findByIdOrEmailV2 EMPTY ____________________________ $result14")

          Logger.info(s"Result getDownloadReportData ____________________________ $result15")

          Logger.info(s"Result getEmailSendResultForBounceCheck ____________________________ $result16")

          Logger.info(s"Result getProspectEngagementReportData ____________________________ $result17")



        case "_getProspectFieldValue_check" =>
          given Logger: SRLogger = new SRLogger("_getProspectFieldValue_check")
          val prospectObjectInternal = ProspectObjectInternal(
            owner_name = "",
            owner_email = "",
            list_id = None,
            email_domain = Some(""),
            invalid_email = None,
            prospect_category_label_color = "",
            prospect_category_id_custom = 0,
            prospect_source = None,
            prospect_account = None,
            prospect_account_id = None,
            prospect_account_uuid = None,
            last_contacted_at = None,
            last_replied_at = None,
            last_opened_at = None,
              last_call_made_at = None,
            total_opens = 0,
            total_clicks = 0,

            magic_columns = List(),

            tags = None,
            active_campaigns = None,
            /* only in case of specific campaign prospect table in frontend */
            current_campaign_id = None,

            flags = Json.obj(),
            latest_reply_sentiment = None
          )
          val prospectObject = ProspectObject(
            id = 1,
            owner_id = 1,
            team_id = 1,
            first_name = Some("Animesh"),
            last_name = Some("Kumar"),
            email = Some("<EMAIL>"),
            custom_fields = Json.obj(),
            list = None,
            job_title = None,
            company = Some("AK"),
            linkedin_url = None,
            phone = None,
            phone_2 = None,
            phone_3 = None,
            city = None,
            state = None,
            country = None,
            timezone = None,
            prospect_category = "Dont know this",
            last_contacted_at = None,
            last_contacted_at_phone = None,
            created_at = DateTime.now().minusMonths(5),
            internal = prospectObjectInternal,
            latest_reply_sentiment_uuid = None,
            current_step_type = None,
            latest_task_done_at = None,
            prospect_uuid = Some(ProspectUuid("prs_aa_abcdefghi")),
            owner_uuid = AccountUuid("acc_aa_abcdegfhi"),
            updated_at = DateTime.now()
          )
          val fieldString = "[{\"sr_field\": \"email\", \"tp_field\": \"Email\", \"field_type\": \"text\", \"field_label\": \"Email\", \"tp_field_label\": \"Email\", \"tp_field_default_value\": \"\"}, {\"sr_field\": \"first_name\", \"tp_field\": \"FirstName\", \"field_type\": \"text\", \"field_label\": \"First name\", \"tp_field_label\": \"First Name\", \"tp_field_default_value\": \"\"}, {\"sr_field\": \"last_name\", \"tp_field\": \"LastName\", \"field_type\": \"text\", \"field_label\": \"Last name\", \"tp_field_label\": \"Last Name\", \"tp_field_default_value\": \"\"}, {\"sr_field\": \"\", \"tp_field\": \"City\", \"field_type\": \"text\", \"field_label\": \"\", \"tp_field_label\": \"City\", \"tp_field_default_value\": \"\"}, {\"sr_field\": \"\", \"tp_field\": \"Company\", \"field_type\": \"text\", \"field_label\": \"\", \"tp_field_label\": \"Company\", \"tp_field_default_value\": \"\"}, {\"sr_field\": \"\", \"tp_field\": \"Country\", \"field_type\": \"text\", \"field_label\": \"\", \"tp_field_label\": \"Country\", \"tp_field_default_value\": \"\"}, {\"sr_field\": \"\", \"tp_field\": \"CreatedDate\", \"field_type\": \"date\", \"field_label\": \"\", \"tp_field_label\": \"Created Date\", \"tp_field_default_value\": \"\"}, {\"sr_field\": \"\", \"tp_field\": \"Jigsaw\", \"field_type\": \"text\", \"field_label\": \"\", \"tp_field_label\": \"Data.com Key\", \"tp_field_default_value\": \"\"}, {\"sr_field\": \"\", \"tp_field\": \"EmailBouncedDate\", \"field_type\": \"date\", \"field_label\": \"\", \"tp_field_label\": \"Email Bounced Date\", \"tp_field_default_value\": \"\"}, {\"sr_field\": \"\", \"tp_field\": \"EmailBouncedReason\", \"field_type\": \"text\", \"field_label\": \"\", \"tp_field_label\": \"Email Bounced Reason\", \"tp_field_default_value\": \"\"}, {\"sr_field\": \"\", \"tp_field\": \"Name\", \"field_type\": \"text\", \"field_label\": \"\", \"tp_field_label\": \"Full Name\", \"tp_field_default_value\": \"\"}, {\"sr_field\": \"\", \"tp_field\": \"JigsawContactId\", \"field_type\": \"text\", \"field_label\": \"\", \"tp_field_label\": \"Jigsaw Contact ID\", \"tp_field_default_value\": \"\"}, {\"sr_field\": \"\", \"tp_field\": \"LastModifiedDate\", \"field_type\": \"date\", \"field_label\": \"\", \"tp_field_label\": \"Last Modified Date\", \"tp_field_default_value\": \"\"}, {\"sr_field\": \"\", \"tp_field\": \"LastReferencedDate\", \"field_type\": \"date\", \"field_label\": \"\", \"tp_field_label\": \"Last Referenced Date\", \"tp_field_default_value\": \"\"}, {\"sr_field\": \"\", \"tp_field\": \"LastViewedDate\", \"field_type\": \"date\", \"field_label\": \"\", \"tp_field_label\": \"Last Viewed Date\", \"tp_field_default_value\": \"\"}, {\"sr_field\": \"\", \"tp_field\": \"Id\", \"field_type\": \"number\", \"field_label\": \"\", \"tp_field_label\": \"Lead ID\", \"tp_field_default_value\": \"\"}, {\"sr_field\": \"\", \"tp_field\": \"Magento_Customer_ID__c\", \"field_type\": \"text\", \"field_label\": \"\", \"tp_field_label\": \"Magento Customer ID\", \"tp_field_default_value\": \"\"}, {\"sr_field\": \"\", \"tp_field\": \"MiddleName\", \"field_type\": \"text\", \"field_label\": \"\", \"tp_field_label\": \"Middle Name\", \"tp_field_default_value\": \"\"}, {\"sr_field\": \"\", \"tp_field\": \"Secondary_Email__c\", \"field_type\": \"text\", \"field_label\": \"\", \"tp_field_label\": \"Secondary Email\", \"tp_field_default_value\": \"\"}, {\"sr_field\": \"\", \"tp_field\": \"State\", \"field_type\": \"text\", \"field_label\": \"\", \"tp_field_label\": \"State/Province\", \"tp_field_default_value\": \"\"}, {\"sr_field\": \"\", \"tp_field\": \"Suffix\", \"field_type\": \"text\", \"field_label\": \"\", \"tp_field_label\": \"Suffix\", \"tp_field_default_value\": \"\"}, {\"sr_field\": \"\", \"tp_field\": \"SystemModstamp\", \"field_type\": \"date\", \"field_label\": \"\", \"tp_field_label\": \"System Modstamp\", \"tp_field_default_value\": \"\"}, {\"sr_field\": \"\", \"tp_field\": \"Title\", \"field_type\": \"text\", \"field_label\": \"\", \"tp_field_label\": \"Title\", \"tp_field_default_value\": \"\"}, {\"sr_field\": \"\", \"tp_field\": \"Triad_Brand_Name__c\", \"field_type\": \"text\", \"field_label\": \"\", \"tp_field_label\": \"Triad Brand Name\", \"tp_field_default_value\": \"\"}, {\"sr_field\": \"\", \"tp_field\": \"PostalCode\", \"field_type\": \"text\", \"field_label\": \"\", \"tp_field_label\": \"Zip/Postal Code\", \"tp_field_default_value\": \"\"}]"

          val fields: Seq[TriggerFields] = Json.parse(fieldString).validate[Seq[TriggerFields]].get
          var contactObj = Json.obj()
          fields.foreach(field =>
            contactObj = contactObj ++ Json.obj(
              field.tp_field ->
                ProspectColumnDefUtils._getProspectFieldValue(
                  p = prospectObject,
                  field = field
                ))
          )
          contactObj ++ Json.obj("referenceId" -> prospectObject.id)

          Logger.info(s"constructed data -> $contactObj")



        case "test_latest_r_s_prospects" =>
          val prospectDAO = new ProspectDAO
          val dbUtils = new DBUtils
          val dbAndSession = dbUtils.startLocalTx()

          val db = dbAndSession.db
          val session = dbAndSession.session
          prospectDAO.addingLatestReplySentimentForProspect(
            team_id = 58,
            reply_sentiment_uuid = ReplySentimentUuid("reply_sentiment_2H1knwWwd7UspLUbuRUAnsPvBah"),
            prospectIds = List(4418177)
          )(session) match {
            case Failure(err) => logger.error(err.getMessage)
            case Success(_) => logger.info("success")
          }
          dbUtils.commitAndCloseSession(db = db)

        case "test__updateProspectLastContactedAt" =>

          val prospectDAO = new ProspectDAO
          val dbUtils = new DBUtils
          val dbAndSession = dbUtils.startLocalTx()

          val db = dbAndSession.db
          val session = dbAndSession.session
          prospectDAO._updateProspectLastContactedAt(
            team_id = TeamId(id = 9),
            savedRepliesWithProspectIdOnly = Seq((4, DateTime.now())),
            channel = ChannelType.EmailChannel
          ) match {
            case Failure(err) => logger.error(err.getMessage)
            case Success(_) => logger.info("success")
          }
        //          dbUtils.closeSession(db = db)

        //        case "updateScyllaDbRow" =>
        //          implicit val ec = Helpers.genFixedThreadPoolEC(threads = 10)
        //          object main extends TaskCacheDAO_DI
        //            with TaskCacheService_DI
        //            with TaskCacheDatabase_DI
        //            with ScyllaDbConnection_DI
        //            with TaskDAO_DI
        //          {
        //            def init() = {
        //
        //              given logger: SRLogger = new SRLogger("testing connection")
        //              logger.info("inside init")
        //              taskCacheDAO.updateTaskById(
        //               taskId = "randomString",
        //                task_json = Json.toJson("hello").toString()
        //              )
        //                .map(task => {
        //
        //                  println(s"\n\n\n\n task created ${task} \n\n\n\n")
        //                  task
        //                }
        //                ).recover{
        //                case e => println("\n\n\n error while creating task \n\n\n\n")
        //              }
        //            }
        //
        //            override val connector: CassandraConnection = scyllaDbConnection.initialize()
        //          }
        //
        //          main.init()



      }
    }

  }
}
