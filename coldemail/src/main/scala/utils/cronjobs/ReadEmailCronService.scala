package utils.cronjobs

import org.apache.pekko.actor.ActorSystem
import api.AppConfig
import api.accounts.email.models.{EmailProvidedBy, EmailServiceProvider}
import api.emails.EmailSettingDAO
import play.api.libs.ws.WSClient
import utils.helpers.LogHelpers
import utils.{<PERSON><PERSON>, SRLogger}
import utils.mq.replytracker.mq.{MQReplyTracker, MQReplyTrackerMessage, MQReplyTrackerPublisher}

import scala.concurrent.ExecutionContext
import scala.util.{Failure, Success}


class ReadEmailCronService(
                            emailSettingDAO: EmailSettingDAO,
                            mqReplyTrackerPublisher: MQReplyTrackerPublisher
) extends SRCronTrait {

  override val cronName: String = "ReadEmailCronService"
  override val cronIntervalInSeconds: Int = AppConfig.cronReadEmailIntervalInSeconds

  /*
  def start()(implicit system: ActorSystem, ec: ExecutionContext, wSClient: WSClient) = {

    Logger.info(s"[ReadEmailCronService] start schedule")

    val interval = AppConfig.cronReadEmailIntervalInSeconds * 1000.milliseconds

    system.scheduler.schedule(1000.milliseconds, interval)(execute())

  }
  */

  /*
    def execute()(implicit ec: ExecutionContext, wSClient: WSClient) = {

      Logger.info(s"[ReadEmailCronService] execute")

      val emailSettings: Seq[(EmailSetting, String)] = EmailSetting.fetchAllReceivers()

      //    run(emailSettings)
      //      .map(_ => Logger.info(s"[ReadEmailCronService] success"))
      //      .recover { case e => Logger.error(s"[ReadEmailCronService] error: ${e.getMessage}") }

      val futureSeq = emailSettings.map { case (esetting, senderMessageIdSuffix) =>
        EmailService.processReceiveEmailRequest(emailSetting = esetting, senderMessageIdSuffix = senderMessageIdSuffix)
              .map { res =>
                res.recover { case e =>
                  Logger.error(s"[ReadEmailCronService] single email failed:: ${esetting.id} :: ${esetting.email} :: $senderMessageIdSuffix :: ${Helpers.getStackTraceAsString(e)}")
                  throw ReadEmailFailed(s"email:: ${esetting.id} :: ${esetting.email} :: $senderMessageIdSuffix", e)
                }
              }

      }

      Await.result(Future.sequence(futureSeq)
        .map { allResults =>

          allResults.foreach(res =>

            res
              .map { case (emailSetting, replies) => Logger.info(s"[ReadEmailCronService] success: emailSetting: ${emailSetting.id} :: replies: ${replies.size}") }
              .recover { case NonFatal(e) => Logger.error(s"[ReadEmailCronService] one email error: ${Helpers.getStackTraceAsString(e)}") }

          )

        }
        //      .map { allResults =>
        //
        //        allResults.foreach {
        //          case Failure(e) => Logger.error(s"[ReadEmailCronService] error: $e")
        //          case Success((emailSetting, replies)) => Logger.info(s"[ReadEmailCronService] success: emailSetting: ${emailSetting.id} :: replies: ${replies.size}")
        //        }
        //
        //      }
        .recover { case NonFatal(e) => Logger.error(s"[ReadEmailCronService] error: ${Helpers.getStackTraceAsString(e)}") },

        Duration.Inf)

    }
  */

  def executeCron(
    
  )(
    using logger: SRLogger,
    system: ActorSystem,
    ws: WSClient,
    ec: ExecutionContext
  ): Unit = {

    logger.info(s"execute")

    emailSettingDAO.fetchInboxesForReplyTrackingQueue(
      logger = logger
    ) match {

      case Failure(err) => logger.fatal(s"execute fetchInboxesForReplyTrackingQueue error: ${LogHelpers.getStackTraceAsString(err)}")

      case Success(emailSettings) =>

        val team_inbox_esets = emailSettingDAO.fetchTeamInboxesForReplyTrackingQueue(
          logger = logger
        ).get

        val all_email_settings: Set[(Long, EmailServiceProvider, Option[EmailProvidedBy])] = (emailSettings ++ team_inbox_esets).toSet

        logger.info(s"execute success: found all: $all_email_settings")

        all_email_settings foreach { emIdAndService =>
          
          val (emId, service, provided_by) = emIdAndService

          mqReplyTrackerPublisher.publish(MQReplyTrackerMessage(emailSettingId = emId, senderMessageIdSuffix = "", serviceProvider = service, providedBy = provided_by)) match {

            case Failure(e) => logger.fatal(s"execute error: emailSettingId: $emId :: ${LogHelpers.getStackTraceAsString(e)}")

            case Success(_) => logger.info(s"execute success: emailSettingId: $emId")

          }

        }

    }


  }


}
