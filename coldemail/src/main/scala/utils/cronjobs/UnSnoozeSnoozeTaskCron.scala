package utils.cronjobs

import org.apache.pekko.actor.ActorSystem
import api.AppConfig
import api.tasks.models.{Task, TaskMessage}
import api.tasks.pgDao.TaskPgDAO
import org.joda.time.DateTime
import play.api.libs.ws.WSClient
import utils.{Help<PERSON>, SRLogger}
import utils.mq.multichannel.MqUnSnoozeSnoozedTasks
import utils.helpers.LogHelpers
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService

import scala.concurrent.{Await, ExecutionContext, Future}
import scala.util.{Failure, Success}

class UnSnoozeSnoozeTaskCron(
                              taskPgDAO: TaskPgDAO,
                              mqUnSnoozingSnoozedTasks: MqUnSnoozeSnoozedTasks
                            ) extends SRCronTrait {

  override val cronName: String = "UnSnoozeSnoozeTaskCron"
  override val cronIntervalInSeconds: Int = AppConfig.cronFiveMinutelyIntervalInSeconds

  def executeCron(

                 )(
                   using logger: SRLogger,
                   system: ActorSystem,
                   ws: WSClient,
                   ec: ExecutionContext
                 ) = {

    val snoozedTasksToBeUnSnoozed: Future[List[Task]] = taskPgDAO.getSnoozedTasksBeforeTime(
      tillTime = DateTime.now(),
      orgId = None,
//      emailNotCompulsoryEnabled = false
    )

    val result = snoozedTasksToBeUnSnoozed
      .map(result => {

        result.foreach { msg =>
          mqUnSnoozingSnoozedTasks.publish(
            message = TaskMessage(
              task_id = msg.task_id,
              team_id = msg.team_id,
              status = msg.status
            ) // Task - id and status
          ) match {
            case Failure(e) =>
              logger.fatal(s"execute error: mqUnSnoozingSnoozedTasks unSnoozingTask TaskId: ${msg.task_id} :: ${LogHelpers.getStackTraceAsString(e)}")
            case Success(_) =>
              logger.info(s"execute success: mqUnSnoozingSnoozedTasks unSnoozingTask TaskId: ${msg.task_id}")
          }

        }
      })
      .recover{case e => logger.fatal(s"[UnSnoozeSnoozeTaskCronError]: Error while running cron : ${LogHelpers.getStackTraceAsString(e)}")}

    Await.result(result, scala.concurrent.duration.Duration.Inf)

  }
}
