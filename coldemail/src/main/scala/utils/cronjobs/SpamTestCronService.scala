package utils.cronjobs

import org.apache.pekko.actor.ActorSystem
import api.AppConfig
import api.accounts.{AccountDAO, AccountService}
import api.campaigns.models.{CampaignStepData, CampaignStepType}
import api.campaigns.services.CampaignService
import api.campaigns.CampaignStepVariantDAO
import api.emails.EmailSettingDAO
import api.prospects.dao_service.ProspectDAOService
import api.spamtest.{SpamTest, SpamTestDAO, UpdateSpamTest}
import api.prospects.ProspectAccountDAO1
import api.rep_tracking_hosts.service.RepTrackingHostService
import io.smartreach.esp.api.emails.IEmailAddress
import org.joda.time.DateTime
import play.api.libs.ws.WSClient
import sr_scheduler.CampaignStatus
import utils.email.{EmailOptionsForGetBodies, EmailSendDetail, EmailServiceCompanion}
import utils.email_notification.service.EmailNotificationService
import utils.helpers.LogHelpers
import utils.{EmailSpamTester, Help<PERSON>, <PERSON>Logger, SpamTestReportResponseException, SpamTestReportResponseNotFoundException}

import scala.concurrent.{Await, ExecutionContext, Future}
import scala.util.{Failure, Success, Try}


class SpamTestCronService(
                           emailSettingDAO: EmailSettingDAO,
                           emailNotificationService: EmailNotificationService,
                           accountService: AccountService,
                           emailSpamTester: EmailSpamTester,
                           emailServiceCompanion: EmailServiceCompanion,
                           campaignStepVariantDAO: CampaignStepVariantDAO,
                           prospectDAOService: ProspectDAOService,
                           prospectAccountDAO: ProspectAccountDAO1,
                           spamTestDao: SpamTestDAO,
                           campaignService: CampaignService,
                           repTrackingHostService: RepTrackingHostService
                         ) extends SRCronTrait {
  val cronName: String = "SpamTestCronService"
  val cronIntervalInSeconds: Int = AppConfig.cronSpamTestIntervalInSeconds

  def executeCron()(
    using logger: SRLogger,
    system: ActorSystem,
    ws: WSClient,
    ec: ExecutionContext
  ) = {

    logger.info(s"${cronName} executeCron started execution")
    val result = for {
      createSpamTestF <- createSpamTest()

      spamTestCheckResultF <- spamTestCheckResults()
    } yield (createSpamTestF, spamTestCheckResultF)

    Await.result(result, scala.concurrent.duration.Duration.Inf)


  }

  def createSpamTest()(
    implicit system: ActorSystem,
    ws: WSClient,
    ec: ExecutionContext,
    logger: SRLogger
  )
  : Future[Option[SpamTest]] = {

    logger.info(s"${cronName} createSpamTest started running")

    spamTestDao.findOneForSendingTests() match {

      case None =>
        logger.info("[SpamTestCronService] No spam test found for sending test emails to")
        Future.successful(None)


      case Some(st) =>
        if (st.campaign_id.isEmpty || st.step_id.isEmpty) {

          val errorMsg = s"[SpamTestCronService] ignore spam test because of " +
            s"empty campaign_id or step_id or team_id: ${st.id} :: $st :: ${st.teamId}"

          logger.info(errorMsg)

          spamTestDao.updateError(st.id, errorMsg) match {

            case Failure(e1) =>
              logger.error(s"[SpamTestCronService] [FATAL] error while updating cron error: ${LogHelpers.getStackTraceAsString(e1)}")
              Future.successful(None)


            case Success(_) =>
              logger.info(s"[SpamTestCronService] cron error updated: ${st.id}")
              Future.successful(None)


          }


        } else {
          logger.info(s"[SpamTestCronService] found one spam test: ${st.id} :: $st")

          campaignService.findBasicDetails(
            id = st.campaign_id.get,
            teamId = st.teamId
          ) match {
            case Failure(error) =>
              logger.error(s"[SpanTestCronService] [Fatal] error while " +
                s"fetching campaign: ${LogHelpers.getStackTraceAsString(error)}")
              Future.successful(None)

            case Success(campaign) =>
              val prospectOpt = prospectDAOService.findOneByCampaignId(
                campaignId = st.campaign_id.get,
                teamId = campaign.get.team_id,
                Logger = logger
              )


              if (prospectOpt.isEmpty) {

                val errorMsg = s"[SpamTestCronService] error no prospect found for this test: $st"


                logger.info(errorMsg)

                spamTestDao.updateError(st.id, errorMsg) match {

                  case Failure(e1) =>
                    logger.error(s"[SpamTestCronService] [FATAL] error while updating cron error: ${LogHelpers.getStackTraceAsString(e1)}")
                    Future.successful(None)


                  case Success(_) =>
                    logger.info(s"[SpamTestCronService] cron error updated: ${st.id}")
                    Future.successful(None)


                }

              } else {

                logger.info(s"[SpamTestCronService] found one prospect: ${st.id} :: prospect: ${prospectOpt.get.id}")


                // must start with MT username
                // REF: https://www.mail-tester.com/manager/api-documentation.html
                val testName = st.test_name
                val prospect = prospectOpt.get
                val prospectAccount = prospect.internal.prospect_account_id.flatMap(paId => {
                  prospectAccountDAO.find(
                    id = paId,
                    teamId = prospect.team_id
                  )
                })

                val es = emailSettingDAO.find(st.email_settings_id).get
                // campaignService.find is required since campaign.settings is used later
                val campaign = campaignService.find(id = st.campaign_id.get, teamId = es.team_id.id, account = None).get // FIXME VALUECLASS
                // val step = CampaignStep.findById(stepId = st.step_id.get, campaignId = campaign.id).get
                val stepId = st.step_id.get
                val variants = campaignStepVariantDAO.findByStepId(stepId = stepId)

                val subjectAndBody = CampaignStepData.getSubjectAndBodyFromStepData(
                  stepData = variants.head.step_data
                )

                val (subject, body) = (subjectAndBody.subject, subjectAndBody.body)

                // val account = Account.find(id = es.account_id).get

                val customTrackingDomain = es.custom_tracking_domain
                val defaultTrackingDomain = es.default_tracking_domain
                val allTrackingDomainsUsed = repTrackingHostService.getRepTrackingHosts().get.map(_.host_url)

                accountService.find(id = campaign.owner_id) match {
                  case Failure(exception) =>
                    Future.successful(None)
                  case Success(acc) =>

                    emailServiceCompanion.getBodies(
                      editedPreviewEmail = None,
                      org_id = 0, // ok for spam test
                      head_step_id = campaign.head_step_id,
                      email_scheduled_id = 0, // ok for spam test
                      campaign_id = Some(campaign.id),
                      step_id = Some(stepId),
                      calendarAccountData = acc.calendar_account_data,
                      prospect = prospect,
                      emailOptions = EmailOptionsForGetBodies(
                        for_editable_preview = false,
                        editedPreviewEmailAlreadyChecked = false,
                        custom_tracking_domain = customTrackingDomain,
                        default_tracking_domain = defaultTrackingDomain,
                        default_unsubscribe_domain = es.default_unsubscribe_domain,
                        email_sender_name = es.sender_name,
                        sender_first_name = es.first_name,
                        sender_last_name = es.last_name,
                        signature = Some(es.signature),

                        opt_out_msg = campaign.settings.opt_out_msg,
                        opt_out_is_text = campaign.settings.opt_out_is_text,
                        append_followups = campaign.settings.append_followups,

                        subjectTemplate = subject,
                        bodyTemplate = body,
                        manualEmail = false,

                        trackOpens = campaign.settings.open_tracking_enabled,
                        trackClicks = campaign.settings.click_tracking_enabled,

                        previousEmails = Seq(), // We dont need the previus emails appended for this test,
                        allTrackingDomainsUsed = allTrackingDomainsUsed,
                      ),
                      selectedCalendarData = campaign.settings.selected_calendar_data
                    ) match {

                      case Failure(e) =>

                        val errorMsg = s"Error while creating bodies. Please contact support"


                        spamTestDao.updateError(st.id, errorMsg) match {

                          case Failure(e1) =>
                            logger.error(s"[SpamTestCronService] [FATAL] error while updating cron error: ${LogHelpers.getStackTraceAsString(e1)}")
                            Future.successful(None)


                          case Success(_) =>
                            logger.info(s"[SpamTestCronService] cron error updated: ${st.id}")
                            Future.successful(None)


                        }

                      case Success(bodies) =>

                        Try {
                          val selectedCampaignEmailSettingOpt = campaign.settings.campaign_email_settings.find(ces => ces.sender_email_setting_id.emailSettingId == st.email_settings_id)
                          selectedCampaignEmailSettingOpt match {
                            case None =>
                              val errorMsg = s"Error while finding receiver. Please contact support"


                              spamTestDao.updateError(
                                id = st.id,
                                errorMsg = errorMsg) match {

                                case Failure(e1) =>
                                  logger.error(s"[SpamTestCronService] [FATAL] error while updating cron error: ${LogHelpers.getStackTraceAsString(e1)}")
                                  Future.successful(None)


                                case Success(_) =>
                                  logger.info(s"[SpamTestCronService] cron error updated: ${st.id}")
                                  Future.successful(None)


                              }
                            case Some(selectedCampaignEmailSetting) =>

                              val emailSendDetail: EmailSendDetail = EmailSendDetail(

                                sender_email_settings_id = es.id.get.emailSettingId, // FIXME VALUECLASS
                                receiving_email_settings_id = selectedCampaignEmailSetting.receiver_email_setting_id.emailSettingId,
                                sender_message_id_suffix = es.message_id_suffix,

                                id = 0,
                                org_id = 0, // ok for spam test
                                team_id = campaign.team_id,
                                account_id = campaign.owner_id,
                                prospect_id = Some(prospect.id),
                                sendEmailFromCampaignDetails = None,

                                subject = bodies.subject,
                                body = bodies.htmlBody,
                                text_body = bodies.textBody,

                                scheduled_from_campaign = false,

                                service_provider = es.service_provider,
                                via_gmail_smtp = es.via_gmail_smtp,

                                smtp_username = es.smtp_username,
                                smtp_host = es.smtp_host,
                                smtp_port = es.smtp_port,
                                smtp_password = es.smtp_password,
                                oauth2_refresh_token = es.oauth2_refresh_token,
                                oauth2_access_token = es.oauth2_access_token,
                                oauth2_access_token_expires_at = es.oauth2_access_token_expires_at,
                                custom_tracking_domain = customTrackingDomain,
                                // bulk_sender = account.org.settings.bulk_sender,

                                gmail_fbl = None,
                                list_unsubscribe_header = None,

                                // for mailgun
                                email_domain = es.email_domain,
                                api_key = es.api_key,
                                mailgun_region = es.mailgun_region,

                                from_email = es.email,
                                from_name = Helpers.getSenderName(es),
                                reply_to_email = None,
                                reply_to_name = None,
                                cc_emails = Seq(),
                                bcc_emails = Seq(),

                                // VERY IMPORTANT NOTE: this should be overriden with seedList email / mail tester email before sending

                                to_emails = Seq(IEmailAddress(
                                  email = es.email,
                                  name = None
                                )),

                                in_reply_to_id = None,
                                in_reply_to_references_header = None,
                                in_reply_to_sent_at = None,
                                in_reply_to_subject = None,
                                in_reply_to_outlook_msg_id = None,

                                email_thread_id = None,
                                gmail_thread_id = None,

                                //                  append_followups = campaign.settings.append_followups,
                                //                  signature = campaign.settings.signature,

                                sender_email_setting_paused_till = es.paused_till,

                                // Verified through all the sending providers ,
                                // send_plain_text_email flag been used in each email sending flow
                                send_plain_text_email = campaign.settings.send_plain_text_email,

                                //  not add open/click/unsubscribe tracking links on spam test
                                //                  opt_out_msg = "",
                                //                  opt_out_is_text = true,
                                //                  open_tracking_enabled = false,
                                //                  click_tracking_enabled = false,
                                //                  sr_tracking_host = defaultTrackingDomain,
                                rep_tracking_host_id = es.rep_tracking_host_id
                                // opt_out_msg = campaign.settings.opt_out_msg,
                                // opt_out_is_text = campaign.settings.opt_out_is_text,
                                // open_tracking_enabled = campaign.settings.open_tracking_enabled,
                                // click_tracking_enabled = campaign.settings.click_tracking_enabled

                              )


                              logger.info(s"[SpamTestCronService] got body: ${st.id} :: ${st.test_type}")


                              logger.info(s"[SpamTestCronService] create MT test: ${st.id} :: ${st.test_type} :: ${es.rep_mail_server_host}")


                              emailSpamTester.sendEmailToMT(
                                  testName = testName,
                                  emailSendDetail = emailSendDetail,
                                  repMailServerHost = es.rep_mail_server_host,
                                  repMailServerReverseDns = es.rep_mail_server_reverse_dns
                                ).flatMap { sentToMT =>
                                  logger.info(s"[SpamTestCronService] sendEmailToMT success: ${st.id} :: ${st.test_type}")


                                  val updateSpamTest = UpdateSpamTest(
                                    body = bodies.htmlBody,
                                    subject = bodies.subject,


                                    mt_id = Some(testName),
                                    mt_created_at = Some(DateTime.now())
                                  )

                                  Future.fromTry(spamTestDao.update(st.id, updateSpamTest, team_id = st.teamId))

                                }
                                .recover { case e =>
                                  logger.fatal(s"[SpamTestCronService] sendEmailToMT error: ${st.id} :: ${st.test_type} : ${LogHelpers.getStackTraceAsString(e)}")

                                  spamTestDao.updateError(st.id, e.getMessage()) match {

                                    case Failure(e1) =>
                                      logger.error(s"[SpamTestCronService] [FATAL] error while updating cron error: ${LogHelpers.getStackTraceAsString(e1)}")
                                      throw e

                                    case Success(_) =>
                                      logger.info(s"[SpamTestCronService] cron error updated: ${st.id} :: ${e.getMessage}")
                                      throw e

                                  }


                                }
                          }


                        } match {
                          case Failure(e) =>

                            spamTestDao.updateError(
                              id = st.id,
                              errorMsg = Option(e.getMessage).getOrElse(s"Error while sending spam test email")
                            ) match {

                              case Failure(e1) =>
                                logger.error(s"[SpamTestCronService] [FATAL] error sending spam test email: ${LogHelpers.getStackTraceAsString(e1)}")
                                Future.successful(None)


                              case Success(_) =>
                                logger.info(s"[SpamTestCronService] cron error updated [3]: ${st.id}")
                                Future.successful(None)


                            }

                          case Success(stOpt) =>
                            stOpt

                        }
                    }
                }

              }
          }

        }
    }

  }

  def spamTestCheckResults()(
    implicit system: ActorSystem,
    ws: WSClient,
    ec: ExecutionContext,
    logger: SRLogger): Future[Option[SpamTest]] = {

    spamTestDao.findOneForCheckingResults() match {

      case None => logger.info(s"[SpamTestCronService] No spam tests to check results for")
        Future.successful(None)

      case Some(data) =>
        val campaignId = data.campaign_id
        logger.info(s"[SpamTestCronService] check results for ${data.id} ::: ${data.email} ::: ${data.test_type.toString}")


        val spamTestsCheckResultsF = for {

          mtResult <- emailSpamTester.getMTResult(data.mt_id.get)

          updated <- Future.fromTry(spamTestDao.updateResults(data.id, mtResults = mtResult, team_id = data.teamId))

        } yield {

          updated
        }
        spamTestsCheckResultsF.flatMap { spamTestOpt =>

          logger.info(s"[SpamTestCronService] checked: $spamTestOpt")

          if (spamTestOpt.isDefined) {
            sendSpamTestReportNotification(
              campaignId = spamTestOpt.get.campaign_id.get,
              subject = "Your spam test report is ready!",
              isError = false
            ) match {

              case Failure(e) =>
                logger.error(s"[SpamTestCronService] Error while sending spamTest report mail to Admin: ${e.getMessage}: ${spamTestOpt.get}")
                Future.successful(None)
              case Success(_) =>
                logger.info(s"[SpamTestCronService] SpamTest report mail sent successfully to Admin: ${spamTestOpt.get}")
                Future.successful(spamTestOpt)
            }
          } else {
            Future.successful(None)
          }

          // sending spam test report email to ADMIN

        }.recover {
          case e: SpamTestReportResponseException =>

            spamTestDao.updateError(id = data.id, errorMsg = "Error while fetching spam test report. Please contact support") match {

              case Failure(err) =>
                logger.debug(s"[SpamTestCronService] [FATAL] error while updating cron error: ${LogHelpers.getStackTraceAsString(err)}")
                None

              case Success(res) =>

                sendSpamTestReportNotification(
                  campaignId = campaignId.get,
                  subject = "[Action Required] Problem with your Spam Test",
                  isError = true
                ) match {
                  case Failure(e) =>
                    logger.error(s"[SpamTestCronService] Error while sending spamTest report mail to Admin: ${e.getMessage}: ${data}")
                    None
                  case Success(_) =>
                    logger.info(s"[SpamTestCronService] SpamTest Error report mail sent successfully to Admin: ${data}")
                    Some(data)

                }

            }

          case e: SpamTestReportResponseNotFoundException => None


        }


    }


  }

  def sendSpamTestReportNotification(
                                      campaignId: Long,
                                      subject: String,
                                      isError: Boolean
                                    )(implicit ws: WSClient, ec: ExecutionContext, logger: SRLogger): Try[Unit] = {
    // sending spam test report email to ADMIN
    val notificationDataGet = spamTestDao.getSpamTestNotificationData(campaignId)

    if (notificationDataGet.isEmpty) {
      logger.info(s"[SpamTestCronService] Account not found , campaign_id: ${campaignId}")
      Failure(SpamTestReportResponseException("Account not found Exception"))
    } else {

      val notificationData = notificationDataGet.get

      val dasboardDomain = AppConfig.dashboardDomain
      val aid = notificationData.account_id
      val tid = notificationData.team_id

      val campaignUrl = if (notificationData.campaign_status == CampaignStatus.NOT_STARTED) {
        s"$dasboardDomain/dashboard/campaigns/${campaignId}/preview?aid=$aid&tid=$tid&spam_test=true"
      } else {
        s"$dasboardDomain/dashboard/campaigns/${campaignId}/spam_test?aid=$aid&tid=$tid"
      }
      val accountFullName = s"${notificationData.first_name} ${notificationData.last_name}"
      val accountFirstName = notificationData.first_name

      val body = if (isError) {
        views.html.emails.spamTestReportError(campaignUrl, accountFirstName).toString()
      } else {
        views.html.emails.spamTestReport(campaignUrl, accountFirstName).toString()
      }

      emailNotificationService.sendMailFromAdmin(
        toEmail = notificationData.email,
        toName = Some(accountFullName),
        subject = subject,
        body = body)
    }
  }


}
