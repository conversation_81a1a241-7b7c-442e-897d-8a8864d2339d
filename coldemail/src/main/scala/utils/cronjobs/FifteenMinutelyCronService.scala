package utils.cronjobs

import org.apache.pekko.actor.ActorSystem
import api.AppConfig
import api.accounts.TeamId
import api.accounts.dao_service.AccountDAOService
import api.accounts.models.{AccountId, OrgId}
import api.campaigns.dao.{InboxPlacementCheckDAO, InboxPlacementCheckTrackedEmails}
import api.campaigns.services.CampaignSendReportService
import api.emails.{EmailSetting, EmailSettingDAO, SendNewManualEmailV3}
import api.prospects.{InboxV3Service, SendNewEmailManuallyError}
import api.reports.NewSignupReportService
import api.reports.services.{DomainBlacklistCheckReportService, InboxPlacementCheckLogsReportService}
import api.scheduler_report.ReportType
import api.scheduler_report.ReportType.EMAIL_VALIDATION_APIS
import io.smartreach.esp.api.emails.IEmailAddress
import org.joda.time.DateTime
import play.api.libs.ws.WSClient
import utils.emailvalidation.{EmailValidationApiToolsRecordDAO, EmailValidationDailyReportService, EmailValidationService}
import utils.emailvalidation.models.{EmailValidationToolStatus, EmailValidationToolStatusType, EmailValidationToolV2}
import utils.health.SrEmailSendingStoppedCheckService
import utils.mq.inbox_placement.MqAnalyzeInboxPlacementCheckEmail
import utils.{SRLogger, StringUtils}

import scala.concurrent.ExecutionContext
import scala.util.{Failure, Random, Success}

class FifteenMinutelyCronService(
                                  campaignSendReportService: CampaignSendReportService,
                                  srEmailSendingStoppedCheckService: SrEmailSendingStoppedCheckService,
                                  emailValidationApiToolsRecordDAO: EmailValidationApiToolsRecordDAO,
                                  emailValidationService: EmailValidationService,
                                  inboxPlacementCheckLogsReportService: InboxPlacementCheckLogsReportService,
                                  emailValidationDailyReportService: EmailValidationDailyReportService,
                                  mqAnalyzeInboxPlacementCheckEmail: MqAnalyzeInboxPlacementCheckEmail,
                                  inboxPlacementCheckDAO: InboxPlacementCheckDAO,
                                  newSignupReportService: NewSignupReportService,
                                  domainBlacklistCheckReportService: DomainBlacklistCheckReportService,
                                  inboxV3Service: InboxV3Service,
                                  emailSettingDAO: EmailSettingDAO,
                                  accountDAOService: AccountDAOService
                                ) extends SRCronTrait {

  override val cronName: String = "FifteenMinutelyCronService"
  override val cronIntervalInSeconds: Int = AppConfig.cronFifteenMinutesInSeconds

  def ifAccountIsProd()(using logger: SRLogger, ec: ExecutionContext, ws: WSClient) = {

    if (AppConfig.isProd) {

      srEmailSendingStoppedCheckService
        .checkIfEmailsAreGoing()
        .map(result => {

          logger.debug(s"srEmailSendingStoppedCheckService success: $result")

        })
        .recover { case e =>

          logger.fatal("srEmailSendingStoppedCheckService", err = e)

        }
    }
  }


    def isFoundToolWithError()(using logger :SRLogger,ec : ExecutionContext, ws: WSClient) = {
        emailValidationApiToolsRecordDAO.getToolsWithLatestStatusAsError(EmailValidationToolStatusType.Error) match {

            case Failure(e) =>
                logger.fatal(s"failed emailValidationApiToolsRecordDAO.getToolsWithLatestStatusAsError - $e")

            case Success(apisWithErrorStatus)=>

                logger.info(s"Found some APIs with Error status - ${apisWithErrorStatus}")

                apisWithErrorStatus.map(api =>
                    emailValidationService.checkAndTryToMakeAPICallToCheckAPisUp(api
                      )(
                          ws = ws,ec =ec, srLogger = logger)
                      .map(requestId =>
                          logger.info(s"emailValidationService.checkAndTryToMakeAPICallToCheckAPisUp Done For ${api}")
                      ).recover {case e =>

                          logger.fatal(s"emailValidationService.checkAndTryToMakeAPICallToCheckAPisUp Error ",e)

                      }


                )


        }
    }


//  def pushToAnalyzeInboxPlacementCheckLogsMQ(
//                                              logger: SRLogger
//                                            ): Unit = {
//
//    inboxPlacementCheckDAO.getTrackedEmailLogsToAnalyze() match {
//      case Failure(exception) => logger.error(s"pushToAnalyzeInboxPlacementCheckLogsMQ: getTrackedEmailLogsToAnalyze failed", exception)
//      case Success(list) => list.foreach(data => {
//        mqAnalyzeInboxPlacementCheckEmail.publish(msg = data)
//      })
//    }
//
//  }

    def senderWorkerServerCheck(implicit ec: ExecutionContext, ws: WSClient): Unit = {

        val logRequestId = StringUtils.genLogTraceId + "_senderWorkerServerCheck"
        given Logger: SRLogger = new SRLogger(logRequestId = logRequestId)
        /*for staging testing credentials :
        acc_id - 392
        team_id - 634
        org_id - 351


        for prod :
        acc_id - 12178
        team_id - 6412
        org_id - 23
         */

        val accountId: AccountId = AccountId(12178)
        val orgId: OrgId = OrgId(23)
        val teamId: TeamId = TeamId(6412)
        accountDAOService.findAccountFromDbIgnoringCache(
            id = accountId
        ) match {

            case Failure(e) =>
                Logger.error(s"failed to fetch the account - ${e}")

            case Success(None) =>
                Logger.error(s"No account found for this account id ${accountId.id}")

            case Success(account) =>

                Logger.info(s"successfully fetched account ${account}")

                emailSettingDAO.findForSenderWorkerCheck(orgId = orgId, teamId = teamId,accountId = accountId) match {
                    case Failure(exception) =>
                        Logger.error(s"failed emailSettingDAO.findForSenderWorkerCheck - ${exception}")

                    case Success(emailSettings) =>

                        Logger.info(s"Successfully fetched the email settings ${emailSettings}")


                        val fetchedIds: Seq[Long] = emailSettings.flatMap(_.id.map(_.emailSettingId))
                        val missingIds: Seq[Long] = AppConfig.senderCronTestEmailSettingIdList.filterNot(fetchedIds.contains)

                        if (missingIds.nonEmpty) {
                            Logger.error(s"The following expected email setting IDs were not active: ${missingIds.mkString(", ")}")
                        }

                        emailSettings.foreach { es =>

                            val receiverEmailSetting: EmailSetting = {
                                val availableReceivers = emailSettings.filterNot(_.id == es.id)

                                val randomIndex = Random.nextInt(availableReceivers.size)
                                availableReceivers(randomIndex)
                            }

                            val emailAddress: Seq[IEmailAddress] = Seq(
                                IEmailAddress(
                                    name = None,
                                    email = receiverEmailSetting.email
                                )
                            )


                            val data: SendNewManualEmailV3 = SendNewManualEmailV3(
                                email_thread_id = None,
                                campaign_step_id = None,
                                sender_email_setting_id = es.id.get.emailSettingId,
                                receiver_email_setting_id = Some(es.id.get.emailSettingId),
                                to = emailAddress,
                                cc_emails = None,
                                bcc_emails = None,
                                body = "Testing the sender worker ",
                                subject = s"Sending mail for ${
                                    es.rep_mail_server_host
                                }",
                                enable_open_tracking = None,
                                enable_click_tracking = None,
                                mark_as_done = None
                            )

                            inboxV3Service.sendNewEmailManually(
                                  data = data,
                                  org_id = orgId.id,
                                  accountId = accountId.id,
                                  teamId = teamId.id,
                                  loggedinAccount = account.get,
                                  auditRequestLogId = logRequestId,
                                  permittedAccountIds = Seq(accountId.id),
                                  version = "v2",
                                  tiid = Some("all_campaigns")
                              ).map {
                                  case Left(SendNewEmailManuallyError.ValidateInboxError(e)) =>

                                      Logger.error(s"sendNewEmailManually ::ValidateInboxError :: rep_server_id :: ${es.rep_mail_server_id} error :: ${e}")


                                  case Left(SendNewEmailManuallyError.ToEmailEmpty(e)) =>
                                      Logger.error(s"sendNewEmailManually ::ToEmailEmpty :: rep_server_id :: ${es.rep_mail_server_id} error :: ${e}")


                                  case Left(SendNewEmailManuallyError.ApiNotFound(e)) =>
                                      Logger.error(s"sendNewEmailManually ::ApiNotFound :: rep_server_id :: ${es.rep_mail_server_id} error :: ${e}")


                                  case Left(SendNewEmailManuallyError.ApiBadRequest(e)) =>
                                      Logger.shouldNeverHappen(s"sendNewEmailManually ::ApiBadRequest :: rep_server_id :: ${es.rep_mail_server_id} error :: ${e}")


                                  case Left(SendNewEmailManuallyError.InvalidTemplateSyntax(e)) =>
                                      Logger.error(s"sendNewEmailManually ::InvalidTemplateSyntax :: rep_server_id :: ${es.rep_mail_server_id} error :: ${e}")


                                  case Left(SendNewEmailManuallyError.InvalidMergeTag(e)) =>
                                      Logger.error(s"sendNewEmailManually ::InvalidMergeTag :: rep_server_id :: ${es.rep_mail_server_id} error :: ${e}")


                                  case Left(SendNewEmailManuallyError.APIManualEmailSentButSavingFailed(e)) =>
                                      Logger.error(s"sendNewEmailManually ::APIManualEmailSentButSavingFailed :: rep_server_id :: ${es.rep_mail_server_id} error :: ${e}")


                                  case Left(SendNewEmailManuallyError.FatalWhileSending(e)) =>
                                      Logger.error(s"sendNewEmailManually ::FatalWhileSending :: rep_server_id :: ${es.rep_mail_server_id} error :: ${e}")

                                  case Right(msg) =>
                                      Logger.info(s"email sent successfully ${msg}")

                              }
                              .recover { e =>

                                  Logger.error(s"sendNewEmailManually :: sending failed :: rep_server_id :: ${es.rep_mail_server_id} error :: ${e}")

                              }
                            Thread.sleep(scala.util.Random.between(4000, 6000))
                        }
                }
        }
    }



  override def executeCron(

                          )(
                            using logger: SRLogger,
                            system: ActorSystem,
                            ws: WSClient,
                            ec: ExecutionContext
                          ): Unit = {


    campaignSendReportService.checkAndSendReportForLast15Mins() match {
      case Failure(e) =>
        logger.fatal(s"failed campaignSendReportService.checkAndSendReportForLast15Mins", e)

      case Success(_) =>
      //Do nothing
    }

    ifAccountIsProd()



      /* Cron call to check if the emailvalidation report exists for last day
         If not exists then will fetch the total data of the validation apis from the emailValidationApiToollogs table
         and then insert into the scheduler integrity log table and also send an email of the report
       */



      emailValidationDailyReportService.getValidationReportDataAndInsertIntoReportTable(
          validationReportType = EMAIL_VALIDATION_APIS
      ) match {
          case Failure(exception) =>
              logger.fatal(s"emailValidationDailyReportService.getValidationReportDataAndInsertIntoReportTable Error Occurred - $exception")

          case Success(_) =>

      }

    val today: String = DateTime.now().dayOfWeek().getAsShortText()


//    if(today != "Sun") {
      inboxPlacementCheckLogsReportService.getInbpCheckLogsDataAndInsertIntoReportTable(
        reportType = ReportType.INBOX_PLACEMENT_CHECK_LOGS
      ) match {
        case Failure(exception) =>
          logger.fatal(s"inboxPlacementCheckLogsReportService.getInbpCheckLogsDataAndInsertIntoReportTable Error Occurred", exception)

        case Success(_) =>
          logger.success(s"Inboxplacementcheck report sent successfully")

      }
//    }

    domainBlacklistCheckReportService.getDomainBlacklistCheckDataAndInsertIntoReportTable(
        reportType = ReportType.DOMAIN_BLACKLIST_REPORT
    ) match {
        case Failure(exception) =>
            logger.fatal(s"domainBlacklistCheckReportService.getDomainBlacklistCheckDataAndInsertIntoReportTable Error Occured",err = exception)

        case Success(_)=>

    }


    newSignupReportService.getNewSignupReport(
        reportType = ReportType.NEW_SIGNUP_REPORT
    ) match {
        case Failure(exception) =>
            logger.fatal(s"newSignupReportService.getNewSignupReport Error Occured",err = exception)

        case Success(_) =>
            logger.success(s"NewSignup Report sent successfully")
    }


       // Here every fifteen minutes the test API call will be made to those Api tool whose status is detected as Error

      val checkifEmailValidationApiErrorisResolved  = logger.appendLogRequestId(s"evsctmacu_${StringUtils.genLogTraceId}")


      isFoundToolWithError()




//    pushToAnalyzeInboxPlacementCheckLogsMQ(logger = logger)


      senderWorkerServerCheck()




  }

}
