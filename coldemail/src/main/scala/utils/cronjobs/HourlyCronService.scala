package utils.cronjobs

import org.apache.pekko.actor.ActorSystem
import api.AppConfig
import api.accounts.TeamId
import api.accounts.models.FindCampaignIdsBy
import api.billing.v2.services.OrgDeactivateService
import api.call.models.ParentCallingAlertData
import api.call.service.CallService
import api.campaigns.dao.{DomainChecksDAO, InboxPlacementCheckDAO, InboxPlacementCheckDetails}
import api.columns.services.ProspectColumnService
import api.domain_health.DomainHealthCheckDAO
import api.emails.{EmailAddressHost, EmailScheduledDAO, EmailSettingDAO}
import api.emails.models.DeletionReason
import api.emails.services.SelectAndPublishForDeletionService
import api.linkedin.LinkedinSettingService
import api.spammonitor.service.SpamMonitorService
import api.tasks.services.{TaskDaoService, TaskService}
import api.tracking_host.{CnameCheckMsg, <PERSON>q<PERSON>name<PERSON>ptime<PERSON>he<PERSON>, MqInactiveCustomRecordDeleterMsg, MqInactiveDomainCustomTrackingDeleter}
import api.tracking_host.dao.CustomTrackingDomainDAO
import io.smartreach.esp.api.emails.EmailSettingId
import org.joda.time.DateTime
import play.api.libs.ws.WSClient
import utils.customersupport.services.InternalAdoptionReportUpdateQueueingService
import utils.email.models.DeleteEmailsScheduledType
import utils.{Helpers, SRLogger, StringUtils}
import utils.helpers.LogHelpers
import utils.intercom.services.IntercomUpdateQueueingService
import utils.mq.delete_org_data.MqOrgDataDeleter
import utils.mq.domain_health.{DomainHealthBlacklistCheckMsg, MqDomainHealthBlacklistCheck}
import utils.mq.inbox_placement.{MqSendEmailForInboxPlacementCheck, SendEmailDetailsForInboxPlacementCheck}
import utils.srinternalcampaigns.NewUserDripCampaignService

import scala.concurrent.ExecutionContext
import scala.util.{Failure, Success, Try}


class HourlyCronService(
                         taskService: TaskService,
                         emailScheduledDAO: EmailScheduledDAO,
                         intercomUpdateQueueingService: IntercomUpdateQueueingService,
                         newUserDripCampaignService: NewUserDripCampaignService,
                         internalAdoptionReportUpdateQueueingService: InternalAdoptionReportUpdateQueueingService,
                         orgDeactivateService: OrgDeactivateService,
                         callService: CallService,
                         selectAndPublishForDeletionService: SelectAndPublishForDeletionService,
                         inboxPlacementCheckDAO: InboxPlacementCheckDAO,
                         domainChecksDAO: DomainChecksDAO,
                         mqSendEmailForInboxPlacementCheck: MqSendEmailForInboxPlacementCheck,
                         customTrackingDomainDAO: CustomTrackingDomainDAO,
                         mqCnameUptimeCheck: MqCnameUptimeCheck,
                         emailSettingDAO: EmailSettingDAO,
                         mqDomainHealthBlacklistCheck: MqDomainHealthBlacklistCheck,
                         domainHealthCheckDAO: DomainHealthCheckDAO,
                         prospectColumnService: ProspectColumnService,
                         mqInactiveDomainCustomTrackingDeleter: MqInactiveDomainCustomTrackingDeleter,
                         mqOrgDataDeleter: MqOrgDataDeleter,
                         spamMonitorService: SpamMonitorService
                       ) extends SRCronTrait {

  override val cronName: String = "HourlyCronService"
  override val cronIntervalInSeconds: Int = AppConfig.cronHourlyCronServiceIntervalInSeconds

  /*
  def start()(implicit system: ActorSystem, ec: ExecutionContext, wSClient: AhcWSClient) = {

    logger.info(s"[HourlyCronService] start schedule")

    val interval = 1 * 60 * 60 * 1000.milliseconds

    system.scheduler.scheduleWithFixedDelay(60000.milliseconds, interval) { () => execute() }

  }

   */


  //def execute()(implicit system: ActorSystem, ec: ExecutionContext, wSClient: AhcWSClient) =
  def executeCron(
                   
                 )(
                   using logger: SRLogger,
                   system: ActorSystem,
                   ws: WSClient,
                   ec: ExecutionContext
                 ): Unit = {


    ifAccountIsProd()

    // delete emails that were supposed to be sent more than 5 hours ago
    deleteStuckEmails()

    //queues user's campaign email sending account domains for inbox placement check
    val today: String = DateTime.now().dayOfWeek().getAsShortText()


    //    if(today != "Sun") {
//    queueDomainsForInboxPlacementChecks()
    //    }
    //queues the domain to do the blacklist check
    queueDomainsForBlacklistCheck()

    queueDomainPointersForCnameUptimeCheck()

      queueDomainsForCustomTrackingDeletion()

    spamMonitorService.warningToUnderReviewUpdate() match {
      case Success(value) => //DO NOTHING
      case Failure(exception) => logger.shouldNeverHappen(s"failed warningToUnderReviewUpdate", Some(exception))
    }


    orgDeactivateService.hourlyCronService() match {

      case Failure(err) =>

        logger.fatal(s"HourlyCronService: Error ", err)


      case Success(bool) =>

        if (bool) {

          logger.info("HourlyCronService ran successfully")

        } else {

          logger.info("HourlyCronService:  error false found")
        }
    }

    taskService.failLinkedinTasksWhichAreStuck()
      .map(tasks => {
        logger.info(s"Updated status of ${tasks.length} stuck tasks to failed: $tasks")
      })
      .recover {
        case e =>
          logger.error("Error while updating status of stuck tasks to failed", e)
      }


    val isMainAccountCreditEnough: Try[ParentCallingAlertData] = callService.checkIfMainAccountCallingBalanceIsSufficient()

    isMainAccountCreditEnough match {

      case Failure(err) =>

        logger.fatal(s"[HourlyCronService] isMainAccountCreditEnough err : ", err)

      case Success(data) =>


        if (data.is_requirement_satisfied) {

          logger.info("[HourlyCronService] isMainAccountCreditEnough success requirement is satisfied")

        } else {

          logger.fatal(s"[HourlyCronService] isMainAccountCreditEnough main account balance below requirement : ${
            data.main_account_balance.remaining_credit
          } , requirement: ${
            data.total_sub_account_requirement.remaining_credit
          }")


        }
    }

    mqOrgDataDeleter.feedDataForDeletingOrgData()
  }


  def ifAccountIsProd()(using logger: SRLogger, ec: ExecutionContext, ws: WSClient) = {

    if (AppConfig.isProd) {

      /*
      * NEW DRIP
      * */
      newUserDripCampaignService.assignOrgsToRelevantDripCampaign(Logger = logger).map(res =>

        logger.info(s"[HourlyCronService] success newUserDripCampaignService.assignOrgsToRelevantDripCampaign $res")

      ).recover { case e =>

        logger.error(s"[HourlyCronService] [FATAL] error newUserDripCampaignService.assignOrgsToRelevantDripCampaign :: ${LogHelpers.getStackTraceAsString(e)}")
      }


      /*
      * OLD DRIP
      * DRIP_V2 OLD DRIP should be commented after 14 days of releasing new DRIP
      * */
      /*
      newUserDripCampaignsDAO.execute.map(res =>

        logger.info(s"[HourlyCronService] success NewUserDripCampaigns.execute $res")

      ).recover { case e =>

        logger.error(s"[HourlyCronService] [FATAL] error NewUserDripCampaigns.execute :: ${Helpers.getStackTraceAsString(e)}")
      }
       */

      prospectColumnService.notifyIfMagicColumnAreStuckForMoreThanAFewHours match {

        case Failure(exception) =>

          logger.error(
            msg = "Failed to notifyIfMagicColumnAreStuckForMoreThanAFewHours",
            err = exception,
          )

        case Success(colDefsProspectIds) =>

          logger.info(
            msg = s"Successfully notified about pending magic column generations - $colDefsProspectIds",
          )

      }

      intercomUpdateQueueingService
        .queueOrgIdsToUpdateIntercomData() match {
        case Failure(e) =>

          logger.fatal("queueOrgIdsToUpdateIntercomData", err = e)

        case Success(_) =>
          logger.debug("queueOrgIdsToUpdateIntercomData success")
      }

      internalAdoptionReportUpdateQueueingService.queueOrgIdsToUpdateInternalAdoptionReport() match {
        case Failure(err) =>
          logger.fatal("queueOrgIdsToUpdateInternalSRUsageData", err = err)
        case Success(_) =>
          logger.debug("queueOrgIdsToUpdateInternalSRUsageData success")
      }

    }

  }


  def deleteStuckEmails()(using logger: SRLogger) = {

    logger.info(s"[HourlyCronService] deleteStuckEmails start")

    emailScheduledDAO.findStuckEmailAccountsForMoreThan2Hours() match {

      case Failure(e) => logger.error(s"[HourlyCronService] [FATAL] error deleteStuckEmails EmailScheduled.findStuckEmailAccountsForMoreThan5Hours ${
        LogHelpers.getStackTraceAsString(e)
      }")

      case Success(emailsStuck) =>

        logger.info(s"[HourlyCronService] deleteStuckEmails email settings found: $emailsStuck")

        val manualEmailsStuck = emailsStuck.filter(em => em.scheduledManually)

        if (manualEmailsStuck.nonEmpty) {
          logger.error(s"[HourlyCronService] [FATAL] error deleteStuckEmails SKIPPING-DELETING manualEmailsStuck $manualEmailsStuck")
        }

        val deleteCampaignEmailsForEmailSettingData: Map[TeamId, Seq[EmailSettingId]] = emailsStuck
          .filterNot(_.scheduledManually) // leave scheduled manually emails, dont delete those automatically
          .filter(_.senderEmailSettingId.isDefined)
          .groupBy(_.teamId)
          .map {
            case (teamId, group) =>
              teamId -> group.map(g => EmailSettingId(g.senderEmailSettingId.get))
          }

        // rest of the emails where emailSettingId is null
        val deleteCampaignEmailsForEmailScheduledId = emailsStuck
          .filterNot(_.scheduledManually) // leave scheduled manually emails, dont delete those automatically

        /*
        27 Feb 2024:
          removing below code because there was duplicate delete calls happening.
        ---- Main Context -----

        Saw that below `emailsScheduledDeleteService.selectAndPublishForDeletion`
        was deleting emails by email_setting_id ( here we were deleting all emails which were not sent )
        and we wanted to delete emails which were selected in fetchStuckEmails function. hence, removing this.
        checked inside revert is happening in both function

        ---- Context End ------

        Commented out this code
          emailsScheduledDeleteService.selectAndPublishForDeletion(
            deleteEmailsScheduledType = DeleteEmailsScheduledType.DeleteUnSentByEmailSettingId(
              emailSettingData = deleteCampaignEmailsForEmailSettingData,
              Logger = logger
            )
          ) match {

            case Failure(e) =>
              logger.error(s"[HourlyCronService] [FATAL] error deleteStuckEmails EmailScheduled.deleteUnsentByEmailSettingId $deleteCampaignEmailsForEmailSettingData ${LogHelpers.getStackTraceAsString(e)}")


            case Success(_) => logger.info(s"[HourlyCronService] success deleteStuckEmails EmailScheduled.deleteUnsentByEmailSettingId: $deleteCampaignEmailsForEmailSettingData")

          }
         */


        deleteCampaignEmailsForEmailScheduledId.foreach(es => {
          selectAndPublishForDeletionService.selectAndPublishForDeletion(
            deletion_reason = DeletionReason.DeleteStuckEmails,
            deleteEmailsScheduledType = DeleteEmailsScheduledType.DeleteUnsentByEmailScheduledId(
              emailScheduledId = es.emailScheduledId,
              teamId = es.teamId,
              senderEmailSettingId = es.senderEmailSettingId,
            )
          ) match {

            case Failure(e) =>
              logger.error(s"[HourlyCronService] [FATAL] error deleteStuckEmails EmailScheduled.deleteUnsentByEmailScheduledId ${
                es.emailScheduledId
              } ${
                LogHelpers.getStackTraceAsString(e)
              }")


            case Success(_) => logger.info(s"[HourlyCronService] success deleteStuckEmails EmailScheduled.deleteUnsentByEmailScheduledId: ${
              es.emailScheduledId
            }")

          }
        })


        /*
        * Select query for cross-verification if all emails_scheduled got deleted correctly or not.
        * */

        emailScheduledDAO.selectEmailThatCouldHaveBeenDeleted(
          emailSettingData = deleteCampaignEmailsForEmailSettingData
        ) match {

          case Failure(e) =>
            logger.error(s"[HourlyCronService] [FATAL] error deleteStuckEmails EmailScheduled.selectEmailThatCouldHaveBeenDeleted $deleteCampaignEmailsForEmailSettingData ${LogHelpers.getStackTraceAsString(e)}")

          case Success(data) =>

            if (data.nonEmpty) {

              logger.warn(s"[HourlyCronService] success deleteStuckEmails EmailScheduled.selectEmailThatCouldHaveBeenDeleted: These emails were left ${data} ")

            } else {

              logger.debug("[HourlyCronService] deleteStuckEmails cross-verification successfully passed")
            }

        }


    }
  }


//  def queueDomainsForInboxPlacementChecks()(using logger: SRLogger) = {
//    logger.info(msg = "Start queueDomainsForInboxPlacementChecks")
//    val receiverEmailSettingId: Long = AppConfig.RollingUpdates.getReceiverEmailSettingIdForInboxPlacementCheck()
//
//    val res = for {
//      inboxPlacementCheckDetailsList: List[InboxPlacementCheckDetails] <- inboxPlacementCheckDAO.getUncheckedDomainForInboxPlacementCheck
//
//      _: List[Long] <- inboxPlacementCheckDAO.queueDomainForInboxPlacementCheck(
//        inboxPlacementCheckDetails = inboxPlacementCheckDetailsList,
//        receiver_email_setting_id = EmailSettingId(receiverEmailSettingId)
//      )
//
//      _: List[String] <- domainChecksDAO.markDomainAsQueuedForInboxPlacementCheck(
//        domains = inboxPlacementCheckDetailsList.map(_.email_domain)
//      )
//
//    } yield {
//      inboxPlacementCheckDetailsList.map(i => {
//        mqSendEmailForInboxPlacementCheck.publish(message = SendEmailDetailsForInboxPlacementCheck(
//          inboxPlacementCheckDetails = i, receiver_email_setting_id = EmailSettingId(receiverEmailSettingId)
//        ))
//      })
//    }
//
//    res match {
//      case Failure(exception) => logger.shouldNeverHappen("Error queueDomainsForInboxPlacementChecks", Some(exception))
//      case Success(mqResult) =>
//        Helpers.seqTryToTrySeq(mqResult.toSeq) match {
//          case Failure(exception) => logger.shouldNeverHappen("Error while publishing data to mqSendEmailForInboxPlacementCheck", Some(exception))
//          case Success(_) => //do nothing
//        }
//    }
//
//  }


  def queueDomainsForBlacklistCheck()(using logger: SRLogger) = {
    logger.info(msg = "Start queueDomainsForBlacklistCheck")

    emailSettingDAO.getDomainsForBlacklistCheck match {
      case Failure(exception) =>
        logger.error(s"[HourlyCronService] error queueDomainsForBlacklistCheck emailSettingDAO.getDomainsForBlacklistCheck", exception)

      case Success(domainsToBeChecked) =>
        if (domainsToBeChecked.nonEmpty) {

          domainHealthCheckDAO.createAndUpdatePushedToQueue(domainList = domainsToBeChecked) match {
            case Failure(exception) =>
              logger.error(s"[HourlyCronService] error queueDomainsForBlacklistCheck domainHealthCheckDAO.createAndUpdatePushedToQueue", exception)

            case Success(_) =>
              logger.info(s"[HourlyCronService] added the domains to the domain_health_check table successfully.")

              domainsToBeChecked.map { domain =>
                mqDomainHealthBlacklistCheck.publish(
                  DomainHealthBlacklistCheckMsg(domain = domain)
                ) match {
                  case Failure(exception) =>
                    logger.shouldNeverHappen("[HourlyCronService] queueDomainsForBlacklistCheck Error occured while publishing to mqDomainHealthBlacklistCheck", err = Some(exception))

                  case Success(_) =>
                    logger.info("[HourlyCronService] queueDomainsForBlacklistCheck successfully pushed to queue for Blacklist Check")
                }

              }
          }

        } else {
          logger.info("No domains found to be tested for Blacklist Check")
        }
    }
  }

  def queueDomainPointersForCnameUptimeCheck()(using logger: SRLogger) = {
    logger.info("Start queueDomainPointersForCnameUptimeCheck")

    customTrackingDomainDAO.getDomainPointerNameForTesting match {
      case Failure(exception) =>
        logger.shouldNeverHappen(s"[HourlyCronService] error for customTrackingDomainDAO.getDomainPointerNameForTesting : $exception")
      case Success(domainPointerNamesList) =>
        if (domainPointerNamesList.nonEmpty) {
          domainPointerNamesList.map { domainPointerName =>
            mqCnameUptimeCheck.publish(
              CnameCheckMsg(domainPointerName = domainPointerName)
            ) match {
              case Success(_) =>
                logger.info(s"[HourlyCronService] domainPointerName is pushed to queue for uptime check")

              case Failure(exception) =>
                logger.shouldNeverHappen(s"[HourlyCronService] queueDomainPointersForCnameUptimeCheck error occured while publishing to mq ")
            }

          }
        } else {
          logger.info("No domainPointers found to test")
        }

    }
  }


    def queueDomainsForCustomTrackingDeletion()(using logger: SRLogger) = {

        customTrackingDomainDAO.fetchDomainsForRemoval() match {
            case Failure(exception) =>
                logger.shouldNeverHappen(s"[HourlyCronService] queueDomainsForCustomTrackingDeletion error for customTrackingDomainDAO.fetchDomainsForRemoval : $exception")
            case Success(domains) =>
                if (domains.nonEmpty) {
//                    logger.info(s"[HourlyCronService] queueDomainsForCustomTrackingDeletion inactive domain is pushed to queue for deletion ${domains}")

                    domains.map { inactiveDomain =>
                        customTrackingDomainDAO.updateQueuedForDeletion(
                            cloudflareIdentifier = inactiveDomain.cloudflareIdentifier,
                          domain = inactiveDomain.domainHost
                      ) match {
                          case Success(_) =>
                              mqInactiveDomainCustomTrackingDeleter.publish (
                      MqInactiveCustomRecordDeleterMsg (message = inactiveDomain)
                      ) match {
                      case Success (_) =>
                      logger.info (s"[HourlyCronService] queueDomainsForCustomTrackingDeletion inactive domain is pushed to queue for deletion ${inactiveDomain.domainHost}")

                      case Failure (exception) =>
                      logger.shouldNeverHappen (s"[HourlyCronService] queueDomainsForCustomTrackingDeletion error occured while publishing to mq ")
                      }
                          case Failure(e) =>
                              logger.shouldNeverHappen (s"[HourlyCronService] queueDomainsForCustomTrackingDeletion customTrackingDomainDAO.updateQueuedForDeletion error occured  ")
                      }

                    }
                } else {
                    logger.info("[HourlyCronService] queueDomainsForCustomTrackingDeletion No inactive domains found to delete")
                }

        }
    }




}
