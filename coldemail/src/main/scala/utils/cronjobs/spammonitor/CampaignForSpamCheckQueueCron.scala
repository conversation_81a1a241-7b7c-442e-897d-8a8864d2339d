package utils.cronjobs.spammonitor

import org.apache.pekko.actor.ActorSystem
import api.AppConfig
import api.campaigns.CampaignStepDAO
import api.spammonitor.service.SpamMonitorService
import play.api.libs.ws.WSClient
import utils.{Helpers, SRLogger}
import utils.cronjobs.SRCronTrait
import utils.mq.spammonitor.{MqCampaignSpamMonitorService, MqCampaignStepContentCheckService}
import utils.helpers.LogHelpers

import scala.concurrent.ExecutionContext
import scala.util.{Failure, Success}

class CampaignForSpamCheckQueueCron(
                                     spamMonitorService: SpamMonitorService,
                                     campaignStepDAO: CampaignStepDAO,
                                     mqCampaignStepContentCheckService: MqCampaignStepContentCheckService,
                                     mqCampaignSpamMonitorService: MqCampaignSpamMonitorService
                                   ) extends SRCronTrait {
  override val cronName: String = "CampaignForSpamCheckQueueCron"
  override val cronIntervalInSeconds: Int = AppConfig.campaignForSpamCheckQueueCronIntervalInSeconds

  override def executeCron()(using logger: SRLogger, system: ActorSystem, ws: WSClient, ec: ExecutionContext): Unit = {
    
    spamMonitorService.getLast24HoursForCampaignsForCron() match {
      case Failure(err) =>
        logger.fatal(s"Error while getting email send status for Campaign Spam Monitor", err)
      case Success(result) =>

        result.foreach { msg =>
          mqCampaignSpamMonitorService.publish(
            message = msg
          ) match {
            case Failure(e) =>
              logger.fatal(s"execute error: spamMonitorService EmailSendingStatus campaignId: ${msg.entity_type.getEntityId()} :: ${LogHelpers.getStackTraceAsString(e)}")
            case Success(_) =>
              logger.info(s"execute success: spamMonitorService EmailSendingStatus campaignId: ${msg.entity_type.getEntityId()}")
          }

        }
    }

    campaignStepDAO.getCampaignStepsToCheckContent() match {
      case Failure(err) =>
        logger.fatal(err.getMessage, err)

      case Success(stepAndCampaignAndTeamIdList) =>
        if (stepAndCampaignAndTeamIdList.isEmpty) {
          logger.info("No stepIds for content check.")
        }
        else {
          stepAndCampaignAndTeamIdList.foreach(stepIdAndCampaignAndTeamId => {
            mqCampaignStepContentCheckService.publish(msg = stepIdAndCampaignAndTeamId) match {
              case Failure(err) =>
                logger.error(s"Error while publishing $stepIdAndCampaignAndTeamId to MqCampaignStepContentCheckService", err)

              case Success(_) =>
                logger.info(s"published $stepIdAndCampaignAndTeamId to MqCampaignStepContentCheckService.")
            }
          })
        }
    }

  }
}
