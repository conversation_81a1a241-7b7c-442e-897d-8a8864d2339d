package utils.cronjobs.spammonitor

import org.joda.time.DateTime
import utils.cronjobs.spammonitor.SpamMonitorCheckService.BOUNCE_THRESHOLD
import utils.helpers.LogHelpers
import utils.{Help<PERSON>, SRLogger}
import utils.mq.spammonitor.{MQSpamMonitor, MQSpamMonitorMessage}

import scala.util.{Failure, Success, Try}


object EmailBounceSendStatus extends Enumeration {
  type EmailBounceSendStatus = Value

  val BadIp = Value("BadIp")
  val SenderNotAvailable = Value("SenderNotAvailable")
  val InvalidEmail = Value("InvalidEmail")
}

case class SendResultForBounceCheck(
                                     //id: Long,
                                     //sendStatus: EmailBounceSendStatus.Value
                                     checked_via_tool: String,
                                     reason: String,
                                     validation_result: Int,
                                     email_bounce_type: Option[String],
                                     actual_result: Int
                                   )

case class CampaignForBounceCheck(
                                   id: Long,
                                   previousCheckForCampaignAt: DateTime,
                                   emailBatchForChecks: List[SendResultForBounceCheck]
                                 )

case class EmailSendStatus(
                            ev_id: Long,
                            checked_via_tool: String,
                            reason: String,
                            email_bounce_type: Option[String],
                            campaign_id: Long,
                            prospect_id: Long,
                            team_id: Long
                          )

case class ActiveCampaign(
                           campaign_id: Long,
                           team_id: Long
                         )

case class CampaignProspectsPrimaryKey(
                                        campaign_id: Long,
                                        prospect_id: Long
                                      )


object BounceCheckResult extends Enumeration {
  type BounceCheckResult = Value

  val BatchSizeTooSmall = Value("BatchSizeTooSmall")
  val TooCloseToPreviousMonitorRun = Value("TooCloseToPreviousMonitorRun")
  val UserBounceRateTooHigh = Value("UserBounceRateTooHigh")
  val AllGood = Value("AllGood")
}

case class EmailSendStatusCount(
                                 invalidEmail: Int,
                                 badIp: Int,
                                 senderNotAvailable: Int
                               )

object SpamMonitorCheckService {
  val BOUNCE_THRESHOLD: Float = 25.0f
}

class SpamMonitorCheckService(
                               val activeCampaignService: ActiveCampaignService,
                               val emailDeliveryAnalysisService: EmailDeliveryAnalysisService
                             ) {


  def checkBounceLogicAcrossCampaigns(): Try[Unit] = Try {
    // 1. run a query to get all the active campaign ids
    val activeCampaigns = activeCampaignService.getActiveCampaignsAcrossAllAccounts

    activeCampaigns.foreach(activeCampaign => {

      println(s"running active campaign check for campaign: $activeCampaign\n")
      //      runBounceCheckOnCampaign(activeCampaign)
      //      runBounceCheckOnCampaign1(activeCampaign = activeCampaign.campaign_id,
      //        teamId = activeCampaign.team_id);
      //      Thread.sleep(5000) // Give DB room to breathe
      MQSpamMonitor.publish(MQSpamMonitorMessage(
        campaignId = activeCampaign.campaign_id,
        teamId = activeCampaign.team_id
      )).get
    })
  }

  case class AnalysisResult(
                             spamAbsolute: Int,
                             spamPerc: Float,
                             addressNotDelivered: Int,
                             totalSent: Int
                           )

  /*
  checked_via_tool |    reason     | validation_result | email_bounce_type | actual_result
------------------+---------------+-------------------+-------------------+---------------
 bcr              | deliverable   |                 5 | address_not_found |             5
 bcr              | deliverable   |                 2 | spam              |             2
 bcr              | deliverable   |                91 |                   |             0
   */
  def calcEmailMetrics(input: List[SendResultForBounceCheck]): AnalysisResult = {
    val spamAbsolute = input.filter(rec =>
        rec.email_bounce_type.isDefined && rec.email_bounce_type.get == "spam")
      .map(_.actual_result).sum
    val totalCount = input.map(_.actual_result).sum
    val address_not_found =
      input.filter(rec =>
          rec.email_bounce_type.isDefined && rec.email_bounce_type.get == "address_not_found")
        .map(_.actual_result).sum

    AnalysisResult(
      spamAbsolute = spamAbsolute,
      spamPerc = if (totalCount > 0) spamAbsolute.toFloat / totalCount * 100 else -1,
      addressNotDelivered = address_not_found,
      totalSent = totalCount
    )
  }

  def calcEmailMetrics1(input: List[EmailSendStatus]): AnalysisResult = {
    println(s"Enter calcEmailMetrics1: input.length ${input.length}")
    val spamAbsolute = input.filter(rec => {
        //println(s" email_bounce_type: ${rec.email_bounce_type}")
        val cond = rec.email_bounce_type.isDefined && rec.email_bounce_type.get == "spam"
        if (cond) println(s"Found spam ${rec.ev_id}")
        cond
      }
      )
      .map(v => 1).sum
    val totalCount = input.map(v => 1).sum
    val address_not_found =
      input.filter(rec =>
          rec.email_bounce_type.isDefined && rec.email_bounce_type.get == "address_not_found")
        .map(v => 1).sum
    AnalysisResult(
      spamAbsolute = spamAbsolute,
      spamPerc = if (totalCount > 0) spamAbsolute.toFloat / totalCount * 100 else -1,
      addressNotDelivered = address_not_found,
      totalSent = totalCount
    )
  }

  /*
   * We have mapped the email validation result to the following cases
   * 1. Deliverable
   * 2. Risky
   * 3. Unknown
   * 4. Undeliverable
   *
   * Among each of the cases - Deliverable, Risky, Unknown
   * we calculate the actual result of sending the email
   * and calculate the ratio based on that.
   *
   * coldemail_production=> \i bounce-metric.sql
 checked_via_tool |    reason     | validation_result | email_bounce_type | actual_result
------------------+---------------+-------------------+-------------------+---------------
 bcr              | deliverable   |                 5 | address_not_found |             5
 bcr              | deliverable   |                 2 | spam              |             2
 bcr              | deliverable   |                91 |                   |             0
 bcr              | risky         |                 3 | address_not_found |             3
 bcr              | risky         |                38 |                   |             0
 bcr              | undeliverable |                 1 | recip_conn_issue  |             1
 bcr              | undeliverable |                 8 |                   |             0
(7 rows)

   * In the above case - for deliverable emails - the spam ratio is 2/ 98 - close to 2 %
   * In case of spam - it's the absolutes and not the percentages that matters.
   * hence 2 - 3 spam emails may be enough to require us to pause the campaign
   *
   * Deliverable calculation
   * perc_spam = 2/98 * 100
   * abs_spam = 2
   * perc_address_not_found = 5/98
   *
   * Risky Calculation
   * abs_spam = 0
   * perc_address_not_found = 3/41
   *
   *
   * We can make a similar calculation in the case of risky and unknown emails
   *
   * If the validation detects an email as Undeliverable - then it will not be
   * attempted in the first place. Hence we should never get any rows in category
   * undeliverable.
   *
   */


  /**
   * Commented out on 4-May-2022 Because Not being used
   */
  /*
   * Use case : When an email is sent - we need to analyse the bounce/spam marking done by the end inbox. SpamMonitorService
   */
  //  def runBounceCheckOnCampaign(activeCampaign: Long): List[BounceCheckResult.Value] = {
  //    println("Enter runBounceCheckOnCampaign")
  //    // 1. get last batch for bounce check
  //    // 2a. if the count of emails sent is below the threshold
  //    //     log skip this app as not meeting minimum threshold
  //    var res: List[BounceCheckResult.Value] = List()
  //
  //
  //    // Calc percentage in Deliverable
  ////    val deliverable: List[SendResultForBounceCheck] =
  ////    val       risky: List[SendResultForBounceCheck]
  ////    val     unknown: List[SendResultForBounceCheck]
  //
  //
  //    activeCampaignService.getCampaignForBounceCheck(activeCampaign) match {
  //      case Success(campaignForBounceCheck) => {
  //        val deliverable = campaignForBounceCheck.emailBatchForChecks.
  //          filter(sendStatusCount => sendStatusCount.reason == "deliverable")
  //        val risky = campaignForBounceCheck.emailBatchForChecks.
  //          filter(sendStatusCount => sendStatusCount.reason == "risky")
  //        val unknown = campaignForBounceCheck.emailBatchForChecks.
  //          filter(sendStatusCount => sendStatusCount.reason == "unknown")
  //        val deliverableMetrics = calcEmailMetrics(input = deliverable)
  //        val riskyMetrics = calcEmailMetrics(input = risky)
  //        val unknownMetrics = calcEmailMetrics(input = unknown)
  //
  //        if (deliverableMetrics.spamAbsolute >= 3) {
  //          println("We should pause the Deliverable emails in this campaign  ")
  //        } else {
  //          println(" Deliverable emails check fine  ")
  //        }
  //
  //        if (riskyMetrics.spamAbsolute >= 3) {
  //          println("We should pause the risky emails in this campaign  ")
  //        } else {
  //          println(" Risky emails check fine  ")
  //        }
  //
  //        if (unknownMetrics.spamAbsolute >= 3) {
  //          println("We should pause the unknown emails in this campaign  ")
  //        } else {
  //          println(" Unknown emails check fine  ")
  //        }
  //
  //      }
  //      case Failure(exception) => {
  //        println("runBounceCheckOnCampaign - failed")
  //      }
  //
  //    }
  //
  //
  //
  //    /*if (campaignForBounceCheck.emailBatchForChecks.size < 50) {
  //
  //      val bounceCheckResult = BounceCheckResult.BatchSizeTooSmall
  //      println("Exit  runBounceCheckOnCampaign - BatchSizeTooSmall")
  //      // actually even if the sample is too small , we can pause the
  //      // campaign because of spam. hence we should not return early
  //
  //      //      return res
  //
  //      res = res :+ BounceCheckResult.BatchSizeTooSmall
  //    }
  //
  //    var sendStatusCount: EmailSendStatusCount = EmailSendStatusCount(
  //      invalidEmail = 0,
  //      badIp = 0,
  //      senderNotAvailable = 0
  //    )
  //
  //    campaignForBounceCheck
  //      .emailBatchForChecks
  //      .foreach(emailForBounceCheck => {
  //
  //        sendStatusCount = emailForBounceCheck.sendStatus match {
  //
  //          case EmailBounceSendStatus.BadIp =>
  //
  //            sendStatusCount.copy(
  //              badIp = sendStatusCount.badIp + 1
  //            )
  //
  //          case EmailBounceSendStatus.InvalidEmail =>
  //
  //            sendStatusCount.copy(
  //              invalidEmail = sendStatusCount.invalidEmail + 1
  //            )
  //
  //          case EmailBounceSendStatus.SenderNotAvailable =>
  //
  //            sendStatusCount.copy(
  //              senderNotAvailable = sendStatusCount.senderNotAvailable + 1
  //            )
  //
  //        }
  //
  //
  //    })*/
  //
  //    // now we have the data we need
  //
  //    /*if (sendStatusCount.invalidEmail > 3) {
  //      val bounceCheckResult = BounceCheckResult.UserBounceRateTooHigh
  //      println("UserBounceRateTooHigh")
  //      res = res :+ bounceCheckResult
  //    }
  //
  //    if (res.isEmpty) { // if no errors upto now , means we are good.
  //      val bounceCheckResult = BounceCheckResult.AllGood
  //      res = res :+ bounceCheckResult
  //    }*/
  //
  //    println("Exit runBounceCheckOnCampaign ")
  //
  //    res
  //  }

  def pauseDeliverableForCampaign(campaignId: Long, teamId: Long) = ()

  // PROSPECTS_EMAILS_TODO_READ_CLEANED
  /*
   * Use case : When an email is sent - we need to analyse the bounce/spam marking done by the end inbox. SpamMonitorService
   */
  def runBounceCheckOnCampaign1(activeCampaign: Long, teamId: Long, logger: SRLogger)
  : Try[List[BounceCheckResult.Value]] = Try {
    given loggerFn: SRLogger = logger.appendLogRequestId("runBounceCheckOnCampaign1")
    activeCampaignService.getEmailSendResultForBounceCheck(
      campaignId = activeCampaign, teamId = teamId) match {
      case Failure(exception) => {
        loggerFn.fatal(s" failed : ${LogHelpers.getStackTraceAsString(exception)}")
        throw exception
      }
      case Success(listEmailSendStatus) if listEmailSendStatus.isEmpty => {
        loggerFn.info(s" No new records to anlyse skipping ${activeCampaign}, teamid: ${teamId}")
        List()
      }
      case Success(listEmailSendStatus) =>
        doAnalysisSuccess(
          activeCampaign = activeCampaign,
          teamId = teamId,
          listEmailSendStatus = listEmailSendStatus,
          logger = logger).get
    }
  }


  private def doAnalysisSuccess(activeCampaign: Long, teamId: Long,
                                listEmailSendStatus: List[EmailSendStatus], logger: SRLogger)
  : Try[List[BounceCheckResult.Value]] = Try {
    val emailDeliveryAnalysisRecord = doAnalysisAux(
      activeCampaign = activeCampaign,
      teamId = teamId,
      listEmailSendStatus = listEmailSendStatus,
      logger = logger)
    updateAnalysisInDB(
      activeCampaign = activeCampaign,
      teamId = teamId,
      listEmailSendStatus = listEmailSendStatus,
      emailDeliveryAnalysisRecord = emailDeliveryAnalysisRecord,
      logger = logger)
    var res: List[BounceCheckResult.Value] = List()
    res
  }

  private def updateAnalysisInDB(activeCampaign: Long, teamId: Long,
                                 listEmailSendStatus: List[EmailSendStatus],
                                 emailDeliveryAnalysisRecord: EmailDeliveryAnalysisRecord,
                                 logger: SRLogger)
  : Try[EmailDeliveryAnalysisRecord] = Try {
    val loggerFn = logger.appendLogRequestId("updateAnalysisInDB")
    emailDeliveryAnalysisService.
      createEmailValidationAnalysisRecord(emailDeliveryAnalysisRecord) match {
      case Failure(exception) =>
        // todo fix the log
        loggerFn.info("unable to create EmailValidationAnalysis Entry")
        throw exception;
      case Success(analysisId) => {
        // use the analysis ID here for update
        // we can pause the campaign here itself if needed
        val campaignProspects: List[CampaignProspectsPrimaryKey] =
          listEmailSendStatus.map(r =>
            CampaignProspectsPrimaryKey(campaign_id = r.campaign_id,
              prospect_id = r.prospect_id)).toList
        emailDeliveryAnalysisService.updateEmailValidationWithAnalysisId(
          analysisId = analysisId,
          ids = campaignProspects) match {
          case Failure(exception) => loggerFn.fatal(" someone else locked the table")
          case Success(value) => ()
        }
        if (emailDeliveryAnalysisRecord.should_pause_deliverable) {
          loggerFn.warn("should_pause_deliverable")
          pauseDeliverableForCampaign(campaignId = activeCampaign, teamId = teamId)
        }

        // just recording it - we want the side effect
        val nUpdated = activeCampaignService.updateAnalysisIdForCampaign(
          campaignId = activeCampaign,
          teamId = teamId,
          analysed_at = emailDeliveryAnalysisRecord.analysed_at).get
        emailDeliveryAnalysisRecord
      }
    }
  }

  private def doAnalysisAux(activeCampaign: Long, teamId: Long,
                            listEmailSendStatus: List[EmailSendStatus],
                            logger: SRLogger)
  : EmailDeliveryAnalysisRecord = {
    val loggerFn = logger.appendLogRequestId(
      s"doAnalysisAux cmp_id: ${activeCampaign}, team_id = ${teamId} ")
    val deliverable = listEmailSendStatus.
      filter(emailStatus => emailStatus.reason == "deliverable")
    val risky = listEmailSendStatus.
      filter(sendStatusCount => sendStatusCount.reason == "risky")
    val unknown = listEmailSendStatus.
      filter(sendStatusCount => sendStatusCount.reason == "unknown")
    val deliverableMetrics = calcEmailMetrics1(deliverable)
    val riskyMetrics = calcEmailMetrics1(risky)
    val unknownMetrics = calcEmailMetrics1(unknown)
    loggerFn.info(s"deliverableMetrics : $deliverableMetrics")
    loggerFn.info(s"riskyMetrics : $riskyMetrics")
    loggerFn.info(s"unknownMetrics : $unknownMetrics")

    val deliverableScore = ""

    val emailDeliveryAnalysisRecord = EmailDeliveryAnalysisRecord(
      id = -1, team_id = teamId, campaign_id = activeCampaign,
      validation_start_id = listEmailSendStatus.head.ev_id,
      validation_end_id = listEmailSendStatus.last.ev_id,
      sample_size = listEmailSendStatus.length,
      n_spam_deliverable = deliverableMetrics.spamAbsolute,
      n_spam_risky = riskyMetrics.spamAbsolute,
      n_spam_unknown = unknownMetrics.spamAbsolute,
      n_bounced_deliverable = deliverableMetrics.addressNotDelivered,
      n_bounced_risky = riskyMetrics.addressNotDelivered,
      n_bounced_unknown = unknownMetrics.addressNotDelivered,
      total_deliverable = deliverableMetrics.totalSent,
      total_risky = riskyMetrics.totalSent,
      total_unknown = unknownMetrics.totalSent,
      campaign_health = EmailDeliveryAnalysisRecord.campaign_health(
        n_spam_deliverable = deliverableMetrics.spamAbsolute,
        n_spam_risky = riskyMetrics.spamAbsolute,
        n_spam_unknown = unknownMetrics.spamAbsolute
      ),
      analysed_at = DateTime.now,
      should_pause_deliverable = EmailDeliveryAnalysisRecord.should_pause_deliverable(
        n_spam_deliverable = deliverableMetrics.spamAbsolute,
        n_bounced_deliverable = deliverableMetrics.addressNotDelivered
      ),
      should_pause_unknown = EmailDeliveryAnalysisRecord.should_pause_unknown(
        n_spam_unknown = unknownMetrics.spamAbsolute,
        n_bounced_unknown = unknownMetrics.addressNotDelivered
      ),
      should_pause_risky = EmailDeliveryAnalysisRecord.should_pause_risky(
        n_spam_risky = riskyMetrics.spamAbsolute,
        n_bounced_risky = riskyMetrics.addressNotDelivered
      )

    )
    // for now raise the alarm
    if (emailDeliveryAnalysisRecord.should_pause_deliverable) {
      loggerFn.warn("should_pause_deliverable")
    }

    if (emailDeliveryAnalysisRecord.should_pause_risky) {
      loggerFn.warn("should_pause_risky")
    }

    if (emailDeliveryAnalysisRecord.should_pause_unknown) {
      loggerFn.warn("should_pause_unknown")
    }

    val tot_spam = emailDeliveryAnalysisRecord.n_spam_risky +
      emailDeliveryAnalysisRecord.n_spam_deliverable +
      emailDeliveryAnalysisRecord.n_spam_unknown
    if (tot_spam >= 3) {
      loggerFn.warn(s"Should pause campaign as tot_spam ${tot_spam} >= 3")
    }

    val tot_bounced = emailDeliveryAnalysisRecord.n_bounced_risky +
      emailDeliveryAnalysisRecord.n_bounced_deliverable +
      emailDeliveryAnalysisRecord.n_bounced_unknown
    val bounce_perc: Float =
      (tot_bounced.toFloat / emailDeliveryAnalysisRecord.sample_size.toFloat) * 100.0F

    if (bounce_perc >= BOUNCE_THRESHOLD) {
      loggerFn.warn(s"should pause campaign as total_bounced: ${tot_bounced}, bounce_perc: ${bounce_perc} > BOUNCE_THRESHOLD ${BOUNCE_THRESHOLD}")
    }

    // I need to pull out this analysis id - so i can update the records

    //println(s"emailDeliveryAnalysisRecord:  $emailDeliveryAnalysisRecord")
    //        println(s"createEmailValidationAnalysisRecord:  ${emailDeliveryAnalysisService.createEmailValidationAnalysisRecord(emailDeliveryAnalysisRecord)}")
    emailDeliveryAnalysisRecord
  }
}
