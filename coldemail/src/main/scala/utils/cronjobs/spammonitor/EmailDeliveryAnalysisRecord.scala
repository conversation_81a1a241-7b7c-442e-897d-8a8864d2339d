package utils.cronjobs.spammonitor

import org.joda.time.DateTime

case class EmailDeliveryAnalysisRecord(
                         id: Long,
                         team_id: Long,
                         campaign_id: Long,
                         validation_start_id: Long,
                         validation_end_id: Long,
                         sample_size: Int,
                         n_spam_deliverable: Int,
                         n_spam_risky: Int,
                         n_spam_unknown: Int,
                         n_bounced_deliverable: Int,
                         n_bounced_risky: Int,
                         n_bounced_unknown: Int,
                         total_deliverable: Int,
                         total_risky: Int,
                         total_unknown: Int,
                         campaign_health: String,
                         analysed_at: DateTime,
         should_pause_deliverable: <PERSON><PERSON><PERSON>,
         should_pause_unknown: <PERSON><PERSON>an,
         should_pause_risky: <PERSON><PERSON><PERSON>
                         ) {


  // case class will have all these params
  // when putting into DB , call the companion methods and set the values into case class
  // when pulling from the DB - since the values are already computed just pull from DB



}

object EmailDeliveryAnalysisRecord {

  def should_pause_deliverable(n_spam_deliverable: Int,
          n_bounced_deliverable: Int)
  : <PERSON><PERSON>an =
    if (n_spam_deliverable >= 3 || n_bounced_deliverable >= 15)  true else false


  def should_pause_unknown(n_spam_unknown :Int, n_bounced_unknown: Int)
  : Boolean =
    if (n_spam_unknown >= 3 || n_bounced_unknown >= 15)  true else false

  def should_pause_risky(n_spam_risky : Int, n_bounced_risky :Int): Boolean =
    if (n_spam_risky >= 3 || n_bounced_risky >= 15)  true else false

  def campaign_health(n_spam_deliverable: Int
          , n_spam_risky: Int
          , n_spam_unknown: Int)
  : String = {
    // dlvrbl_GRN_risky_ORG_unknwn_GRN
    val deliverable_health : String = if (n_spam_deliverable >= 3) {
      "dlvrbl_RED"
    } else if (n_spam_deliverable >= 1 && n_spam_deliverable < 3) {
      "dlvrbl_ORG"
    } else {
      "dlvrbl_GRN"
    }

    val risky_health : String = if (n_spam_risky >= 3) {
      "_risky_RED"
    } else if (n_spam_risky >= 1 && n_spam_risky < 3) {
      "_risky_ORG"
    } else {
      "_risky_GRN"
    }

    val unknown_health: String = if (n_spam_unknown >= 3) {
      "_unknwn_RED"
    } else if (n_spam_unknown >= 1 && n_spam_unknown < 3) {
      "_unknwn_ORG"
    } else {
      "_unknwn_GRN"
    }

    var combined_health = deliverable_health + risky_health + unknown_health;

    if (combined_health == "dlvrbl_GRN_risky_GRN_unknwn_GRN") {
      combined_health = "all_GRN"
    } else if (combined_health == "dlvrbl_RED_risky_RED_unknwn_RED") {
      combined_health = "all_RED"
    }

    combined_health
  }

}
