package utils.cronjobs

import org.apache.pekko.actor.ActorSystem
import api.AppConfig
import api.call.models.SubAccountUuid
import api.call.service.CallService
import api.campaigns.services.CampaignService
import play.api.libs.ws.WSClient
import utils.SRLogger
import utils.helpers.LogHelpers
import utils.mq.call.MqUpdateSubAccountCallHistory
import utils.mq.channel_scheduler.channels.IndependentStepSchedulerService
import utils.mq.channel_scheduler.{MQIndependentStepScheduler, MqIndependentStepSchedulerMsg}

import scala.concurrent.ExecutionContext
import scala.util.{Failure, Success, Try}

class IndependentStepSchedulerCron(
                                    campaignService: CampaignService,
                                    mqIndependentStepScheduler: MQIndependentStepScheduler
                              ) extends SRCronTrait  {

  override val cronName: String = "IndependentStepSchedulerCron"
  override val cronIntervalInSeconds: Int = AppConfig.cronIndependentStepSchedulerCronIntervalInSeconds

  def executeCron(
                   
                 )(
                   using logger: SRLogger,
                   system: ActorSystem,
                   ws: WSClient,
                   ec: ExecutionContext
                 ) = {

    val listOfCampaignsForPushingToMq: Try[List[MqIndependentStepSchedulerMsg]] = campaignService.getCampaignsForIndependentStepSchedulerMQ()

    listOfCampaignsForPushingToMq match {

      case Failure(e) =>

        logger.fatal(s"[IndependentStepSchedulerCron]: Error while fetching campaign_id_msg to publish to queue : ${LogHelpers.getStackTraceAsString(e)}")


      case Success(listOfCampaignIds) =>

        listOfCampaignIds.foreach { campaign_id_msg =>

          val result = for {

            // Unit
            addingToTheQueue <- mqIndependentStepScheduler.publish(
              msg = campaign_id_msg
            )

            _: Long <- campaignService.mqIndependentStepSchedulerUpdatePushedToMqToTrue(
              mqIndependentMsg = campaign_id_msg
            )

          } yield {
            addingToTheQueue
          }
          result match {
            case Failure(e) =>
              logger.fatal(s"execute error: IndependentStepSchedulerCron publishing to queue campaign_id_msg: ${campaign_id_msg} :: ${LogHelpers.getStackTraceAsString(e)}")
            case Success(_) =>
              logger.info(s"execute success: IndependentStepSchedulerCron published to queue campaign_id_msg: ${campaign_id_msg}")
          }

        }
    }
  }



}
