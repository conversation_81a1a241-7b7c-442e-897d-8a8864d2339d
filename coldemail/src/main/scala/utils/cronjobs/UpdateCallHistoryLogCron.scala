package utils.cronjobs

import org.apache.pekko.actor.ActorSystem
import api.AppConfig
import api.call.models.SubAccountUuid
import api.call.service.CallService
import play.api.libs.ws.WSClient
import utils.SRLogger
import utils.helpers.LogHelpers
import utils.mq.call.MqUpdateSubAccountCallHistory

import scala.concurrent.ExecutionContext
import scala.util.{Failure, Success, Try}

class UpdateCallHistoryLogCron(
                                callService: CallService,
                                mqUpdateSubAccountCallHistory: MqUpdateSubAccountCallHistory
                              ) extends SRCronTrait  {

  override val cronName: String = "UpdateCallHistoryLogCron"
  override val cronIntervalInSeconds: Int = AppConfig.cronUpdateSubAccountCallHistoryCronIntervalInSeconds

  def executeCron(

                 )(
                   using logger: SRLogger,
                   system: ActorSystem,
                   ws: WSClient,
                   ec: ExecutionContext
                 ) = {

    val listOfSubAccountsForPushingToMq: Try[List[SubAccountUuid]] = callService.getSubAccountsForUpdatingCallHistory()

    listOfSubAccountsForPushingToMq match {

      case Failure(e) =>

        logger.fatal(s"[UpdateCallHistoryLogCron]: Error while fetching sub-accounts to publish to queue : ${LogHelpers.getStackTraceAsString(e)}")


      case Success(listOfSubAccounts) =>

        listOfSubAccounts.foreach { sub_account_uuid =>

          val result = for {

            // Unit
            addingToTheQueue <- mqUpdateSubAccountCallHistory.publish(
              msg = sub_account_uuid
            )

            _: Long <- callService.updateAddedToCallHistoryLogsMq(
              sub_account_uuid = sub_account_uuid
            )

          } yield {
            addingToTheQueue
          }
          result match {
            case Failure(e) =>
              logger.fatal(s"execute error: MqUpdateSubAccountCallHistory publishing to queue sub-account-uuid: ${sub_account_uuid} :: ${LogHelpers.getStackTraceAsString(e)}")
            case Success(_) =>
              logger.info(s"execute success: MqUpdateSubAccountCallHistory published to queue sub-account-uuid: ${sub_account_uuid}")
          }

        }
    }
  }



}
