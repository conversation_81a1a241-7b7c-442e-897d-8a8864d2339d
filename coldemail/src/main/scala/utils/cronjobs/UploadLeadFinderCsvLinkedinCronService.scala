/* 17-jun-2025: [DO_NOT_REMOVE] [LEADFINDERUPLOAD] was slowing down compilation. Only needed for the Lead database upload.

package utils.cronjobs

import org.apache.pekko.actor.ActorSystem
import api.AppConfig
import play.api.libs.json.Json
import play.api.libs.ws.WSClient
import utils.GCP.CloudStorage
import utils.sr_csv_parser.CSVParseFileType
import utils.{Helpers, SRLogger}
import utils.testapp.csv_upload.{LeadFinderUploadDao, LeadFinderUploadService}

import scala.concurrent.ExecutionContext
import scala.util.{Failure, Success, Try}

class UploadLeadFinderCsvLinkedinCronService(
                                              leadFinderUploadService: LeadFinderUploadService,
                                              leadFinderUploadDao: LeadFinderUploadDao
                                            ) extends SRCronTrait {

  override val cronName: String = "UploadLeadFinderCsvLinkedinCronService"
  override val cronIntervalInSeconds: Int = AppConfig.cronUploadLeadFinderCsvLinkedinIntervalInSeconds

  var uploading = false


  def executeCron()(
    using logger: SRLogger,
    system: ActorSystem,
    ws: WSClient,
    ec: ExecutionContext
  ): Unit = {
    if (uploading) {

      logger.info(s"another file is being uploaded now, will check later again")

    } else {
      leadFinderUploadDao.findLinkedinFilesFromQueue() match {

        case Failure(err) =>
          logger.fatal(s"CsvQueue.find", err = err)

        case Success(None) =>
          logger.info(s"nothing found in csv queue")

        case Success(Some(csvData)) =>

          uploading = true

          logger.info(s"found csv start upload: $csvData")

          val res = for {

            filenameEncoded <- Try {
              csvData.file_url.split("/").last.split("_").last
            }

            filename <- Try {
              java.net.URLDecoder.decode(filenameEncoded, java.nio.charset.StandardCharsets.UTF_8.name())
            }

            signedUrl <- Try {

              val fullFileNameEncoded = csvData.file_url.split("/").last
              val fileNameDecoded = java.net.URLDecoder.decode(fullFileNameEncoded, java.nio.charset.StandardCharsets.UTF_8.name())

              if (!AppConfig.isLocalDevDomain) {
                CloudStorage.genSignedUrlForLeadFinderCsvDownload(filename = fileNameDecoded).get
              } else {
                fileNameDecoded
              }
            }

            fileUrl <- {
              logger.info(s"found signedUrl: $signedUrl")
              if (!AppConfig.isLocalDevDomain) {
                Helpers.stringToURL(
                  href = signedUrl,
                  orgIdOpt = None
                )
              } else {
                Helpers.stringToURL(
                  href = AppConfig.localCsvFilePath + signedUrl,
                  orgIdOpt = None
                )
              }
            }


            parserResult <- {
              logger.info(s"found fileUrl: $fileUrl")
              logger.info(s"parsing started")
              leadFinderUploadService.parseCSVForLeadFinder(

                fileObj = CSVParseFileType.FileURL(
                  fileUrl = fileUrl
                ),
                logger = logger
              )
            }

            mappingFromClient <- Try {
              logger.info(s"parsing completed !!!!")
              csvData.column_map.as[Map[String, String]]
            }

          } yield {

            (filename, parserResult, mappingFromClient)
          }

          res match {
            case Failure(err) =>
              uploading = false

              logger.fatal(s"FATAL FATAL error uploadInputRes NOT SENDING FAILED EMAIL CHECK IMMEDIATELY", err = err)

              if (AppConfig.isProd) { //adding error in db only if it's prod because queue get triggered in dev.sreml and local csv testing face issues
                leadFinderUploadDao.updateOnFailLinkedin(
                  id = csvData.id,
                  charsetDetected = None,
                  error = Option(err.getMessage).getOrElse("").replaceAll("\u0000", ""),
                  logger = logger
                ) match {
                  case Failure(err) =>
                    logger.fatal(s"failed while updating Error 1: ${csvData.id}")
                  case Success(_) => {
                    logger.info(s"success while updating Error 1: ${csvData.id}")
                  }
                }
              } else {
                /**
                 * When we upload csv from local then it ads record in csv_queue
                 * and staging dev.sreml also start reading it from queue but it repeatedly fail
                 * because the path for csv is local path. In this case we are ignoring the error
                 */
                logger.info(s"Ignoring error while parsing csv locally")
              }

            case Success(value) =>
              logger.info("raed file, going to parse and upload")
              val rowMapFromCSV = value._2.rowMapFromCSV
              logger.info("column mapping started")
              val csvUploadRows = rowMapFromCSV.map(row => {
                leadFinderUploadService.getCsvUploadColLinkedin(
                  mappingFromClient = value._3,
                  csvRow = row
                )

              })
              logger.info("column mapping done!!!!")
              leadFinderUploadDao.insertLinkedinCsvParsedCsvData(
                data = csvUploadRows,
                accountId = csvData.account_id,
                teamId = csvData.team_id,
                ta_id = csvData.ta_id,
                csvQueueData = csvData
              ) match {
                case Failure(err) =>
                  logger.fatal(s"Error While Inserting dat into table", err = err)
                  uploading = false
                  leadFinderUploadDao.updateOnFailLinkedin(
                    id = csvData.id,
                    charsetDetected = value._2.charsetDetected,
                    error = Option(err.getMessage).getOrElse("").replaceAll("\u0000", ""),
                    logger = logger
                  ) match {
                    case Failure(err) =>
                      logger.fatal(s"failed while updating Error: ${csvData.id}")
                    case Success(success) => {
                      logger.info(s"success while updating Error: ${csvData.id}")
                    }
                  }

                case Success(idToHashMap) =>
                  leadFinderUploadDao.updateLinkedin(
                    id = csvData.id,
                    charsetDetected = value._2.charsetDetected,
                    result = Json.toJson(idToHashMap),
                    parserName = Some(value._2.parserName),
                    parserContext = value._2.parserContext,
                    logger = logger
                  ) match {
                    case Failure(err) =>
                      uploading = false
                      logger.fatal(s"failed while update: ${csvData.id}")
                    case Success(success) => {
                      uploading = false
                      logger.info(s"success while update: ${csvData.id}")
                    }
                  }
              }

          }
      }
    }
  }

}
*/
