package utils.cronjobs

import api.accounts.TeamId
import api.accounts.models.AccountId
import api.campaigns.services.CampaignId
import api.columns.ProspectColGenStatus
import api.columns.ProspectColGenStatus.Failed
import api.llm.dao.{LlmAuditLogDAO, LlmAuditLogData, UpdateStatusData}
import api.llm.models.LlmFlow
import api.prospects.models.ProspectId
import api.sr_ai.dao.SrAiLockJedisDAO
import api.sr_ai.models.{MqSrAiApiMessage, MqSrAiApiMessageWithLogId}
import api.sr_ai.mq.MqSrAiApiPublisher
import io.sr.billing_common.models.PlanID
import org.apache.pekko.actor.ActorSystem
import play.api.libs.ws.WSClient
import utils.SRLogger
import utils.cronjobs.SRCronTrait
import utils.helpers.LogHelpers

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}
import scala.collection.mutable.ListBuffer

/**
 * MARK: - What it does: Cron job that processes pending LLM audit logs for SR AI API calls
 * MARK: - Why it is like this: Handles retry logic for failed AI API calls and ensures all pending requests are eventually processed
 * 
 * This cron job:
 * - Runs periodically to fetch pending LLM audit logs for Reply Sentiment and Out of Office classification
 * - Republishes failed messages up to 5 attempts
 * - Updates audit log status appropriately (PENDING -> QUEUED)
 * - Uses bulk operations for efficiency
 */
class SrAiApiCron(
  llmAuditLogDAO: LlmAuditLogDAO,
  mqSrAiApiPublisher: MqSrAiApiPublisher,
  srAiLockJedisDAO: SrAiLockJedisDAO
) extends SRCronTrait {

  override val cronName: String = "SrAiApiCron"
  override val cronIntervalInSeconds: Int = 60 // Runs every 60 seconds

  /**
   * MARK: - What it does: Main execution method that processes pending audit logs for both flows
   * MARK: - Why it is like this: Consolidates processing for both Reply Sentiment and OOO flows for efficiency
   */
  def executeCron()(
    implicit logger: SRLogger,
    system: ActorSystem,
    ws: WSClient,
    ec: ExecutionContext
  ): Unit = {
    logger.info(s"[$cronName]: Starting cron run.")
    
    // Process both Reply Sentiment and Out of Office classification flows
    val flows = List(LlmFlow.ReplySentiment, LlmFlow.OutOfOfficeClassification)
    
    flows.foreach { flow =>
      if (!srAiLockJedisDAO.checkLock(aiModel = flow.model)) {
        processFlowLogs(flow)
      } else {
        logger.info(s"[$cronName]: Model ${flow.model} is locked, skipping ${flow.toString} processing")
      }
    }
  }

  /**
   * MARK: - What it does: Processes pending logs for a specific LLM flow
   * MARK: - Why it is like this: Allows handling different flow types with the same retry logic
   */
  private def processFlowLogs(flow: LlmFlow)(implicit logger: SRLogger, ec: ExecutionContext): Unit = {
    
    val pendingLogsFuture: Future[Seq[LlmAuditLogData[MqSrAiApiMessage]]] =
      Future.fromTry(
        llmAuditLogDAO.getAttempts[MqSrAiApiMessage](flow = flow)
      ).map(_.toSeq)

    pendingLogsFuture.foreach { logs =>

      val updatesToPerform = ListBuffer[UpdateStatusData]()

      logs.foreach { log =>

        if (log.updated_count >= 5) {
          val updateData = UpdateStatusData(
            id = log.id,
            teamId = log.team_id,
            newStatus = Failed
          )

          llmAuditLogDAO.updateStatus(
            updateStatusData = List(updateData),
            updateCount = true
          ) match {
            case Success(value) => //Do nothing
            case Failure(exception) =>
              logger.shouldNeverHappen(s"Failed to update audit log status for logId: ${log.id}", Some(exception))

          }
          logger.shouldNeverHappen(s"[$cronName]: Log ID [${log.id}] has updated_count >= 5 (value: ${log.updated_count}). " +
            "This means it has failed 5 or more times. Skipping as per current logic.")
        } else {
          val messageWithLogId = MqSrAiApiMessageWithLogId(
            message = log.queue_message,
            logId = log.id
          )


          mqSrAiApiPublisher.publish(
            msg = messageWithLogId
          ) match {
            case Success(_) =>

              val newCount = log.updated_count + 1

              updatesToPerform += UpdateStatusData(
                id = log.id,
                teamId = log.team_id,
                newStatus = ProspectColGenStatus.Queued
              )
              
            case Failure(ex) =>
              logger.fatal(s"[$cronName]: Failed to publish ${flow.toString} message for log ID [${log.id}] to MQ: ${LogHelpers.getStackTraceAsString(ex)}")
          }
        }
      }

      // Bulk update status for successfully published messages
      if (updatesToPerform.nonEmpty) {
        
        llmAuditLogDAO.updateStatus(updatesToPerform.toList, updateCount = true) match {
          case Success(updatedRowCount) =>
            if (updatedRowCount != updatesToPerform.size) {
              logger.warn(s"[$cronName]: Bulk update rows affected ($updatedRowCount) does not match count of logs intended for update (${updatesToPerform.size}).")
            }
            
          case Failure(ex) =>
            logger.fatal(s"[$cronName]: Failed to bulk update ${flow.toString} status to QUEUED for logs: ${updatesToPerform.map(_.id).mkString(", ")}. Error: ${LogHelpers.getStackTraceAsString(ex)}")
        }
      } else {
      }
    }

    pendingLogsFuture.recover {
      case e: Throwable =>
        logger.fatal(s"[$cronName]: Critical error fetching pending ${flow.toString} LLM audit logs: ${LogHelpers.getStackTraceAsString(e)}")
    }
  }
} 