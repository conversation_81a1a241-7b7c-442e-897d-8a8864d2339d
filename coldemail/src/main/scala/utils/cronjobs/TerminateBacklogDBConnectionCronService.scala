package utils.cronjobs

import org.apache.pekko.actor.ActorSystem
import api.AppConfig
import api.accounts.AccountDAO
import play.api.libs.ws.WSClient
import utils.helpers.LogHelpers
import utils.{Help<PERSON>, SRLogger}

import scala.concurrent.ExecutionContext
import scala.util.{Failure, Success}


class TerminateBacklogDBConnectionCronService(
  accountDAO: AccountDAO
) extends SRCronTrait {

  override val cronName: String = "TerminateBacklogDBConnectionCronService"
  override val cronIntervalInSeconds: Int = AppConfig.cronTerminateBacklogDBConnectionCronServiceIntervalInSeconds


  /*
  def start()(implicit system: ActorSystem, ec: ExecutionContext, wSClient: AhcWSClient) = {

    logger.info(s"[TerminateBacklogDBConnectionCronService] start schedule")

    val interval = 1 * 60 * 1000.milliseconds

    system.scheduler.scheduleWithFixedDelay(60000.milliseconds, interval) { () => execute() }

  }
   */


  //def execute()(implicit system: ActorSystem, ec: ExecutionContext, wSClient: AhcWSClient)
  def executeCron(
    
  )(
    using logger: SRLogger,
    system: ActorSystem,
    ws: WSClient,
    ec: ExecutionContext
  ): Unit
  = {

    logger.info(s"[TerminateBacklogDBConnectionCronService] execute called")

    accountDAO.terminateBacklogDBConnections() match {

      case Failure(e) =>

        logger.error(s"[TerminateBacklogDBConnectionCronService] [FATAL] error ${LogHelpers.getStackTraceAsString(e)}")

      case Success(connections) =>

        connections.foreach { connectionData =>

          logger.warn(s"[FATAL] [TerminateBacklogDBConnectionCronService] success query terminated: ${connectionData.query.get} running since ${connectionData.running_since.get}::  ${connectionData}")

        }
    }


  }

}
