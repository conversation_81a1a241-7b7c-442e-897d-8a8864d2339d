package utils.cronjobs.sender_volume_alert.model

import api.accounts.TeamId
import api.campaigns.models.LowCampaignSendingVolumeReason
import api.campaigns.services.{CampaignId, CampaignPauseReason}
import org.joda.time.DateTime

case class CampaignSendingVolumeLogs(
                                      campaign_id: CampaignId,
                                      team_id: TeamId,
                                      date_in_campaign_timezone: String,
                                      campaign_timezone: String,
                                      consecutive_delay_in_seconds: Long,
                                      warmup_is_on: <PERSON>olean,
                                      warmup_started_at: Option[DateTime],
                                      warmup_starting_email_count: Option[Int],
                                      campaign_start_time: DateTime,
                                      pushed_for_checking_at_minutes_since_campaign_start_time: Int,
                                      actual_minutes_since_campaign_start_time_during_computing: Int,
                                      added_at: DateTime,
                                      current_sent_count: Int,
                                      expected_sent_count_till_now: Int,
                                      current_sent_percent: Float,
                                      total_scheduled_for_today_till_now: Int,
                                      possible_issue_if_any: Option[LowCampaignSendingVolumeReason]
                                    )


case class CampaignSendingVolumeLogsForReport(
                                               login_email: String,
                                               team_name: String,
                                               campaign_name: String,
                                               campaign_id: CampaignId,
                                               team_id: TeamId,
                                               campaign_start_time: DateTime,
                                               current_sent_count: Int,
                                               expected_sent_count_till_now: Int,
                                               total_scheduled_for_today_till_now: Int,
                                               possible_issue_if_any: Option[LowCampaignSendingVolumeReason]
                                             )

