package utils.cronjobs

import org.apache.pekko.actor.ActorSystem
import api.AppConfig
import api.emails.EmailScheduledDAO
import api.emails.models.EmailServiceProviderSendEmail.getEmailServiceProviderSendEmail
import org.joda.time.{DateTime, DateTimeZone}
import play.api.libs.ws.WSClient
import utils.helpers.LogHelpers
import utils.mq.email.mq_email.MQEmailPublisher
import utils.{Helpers, SRLogger}
import utils.mq.email.MQEmailMessage

import scala.concurrent.ExecutionContext
import scala.util.{Failure, Success, Try}


class PushToRabbitMqCronServiceV2(
  mqEmailPublisher: MQEmailPublisher,
  emailScheduledDAO: EmailScheduledDAO
) extends SRCronTrait {

  override val cronName: String = "PushToRabbitMqCronServiceV2"
  override val cronIntervalInSeconds: Int = AppConfig.cronPushToRabbitMqV2IntervalInSeconds


  /*
  def start()(implicit system: ActorSystem, ec: ExecutionContext) = {

    logger.info(s"[PushToRabbitMqCronServiceV2] start schedule")

    val interval = AppConfig.cronPushToRabbitMqV2IntervalInSeconds * 1000.milliseconds

    system.scheduler.scheduleWithFixedDelay(1000.milliseconds, interval) { () => execute() }

  }

   */

  /*
  Added on 25th April 2022

  if `emailScheduledDAO.findAndUpdateEmailIdsForQueueingCampaignEmailsToRabbitMQ` takes longer than
  `AppConfig.cronPushToRabbitMqIntervalInSeconds` to run, then we need to avoid executing this cron
  until the previous run has completed. Otherwise this just causes a cascading DB issue.
   */
  var runLock = false

  def executeCron()(
    using logger: SRLogger,
    system: ActorSystem,
    ws: WSClient,
    ec: ExecutionContext
  ): Unit = {

    if (runLock) {

      logger.warn("[PushToRabbitMqCronServiceV2] execute IGNORING because of runLock (the previous run has not completed yet)")

    } else Try {

      runLock = true

      // NOTE: https://stackoverflow.com/questions/32255919/akka-scheduler-stops-on-exception-is-it-expected

      logger.info(s"[PushToRabbitMqCronServiceV2] execute runLock is false")

      val scheduleForNextSeconds = AppConfig.cronPushToRabbitMqIntervalInSeconds

      // schedule for next 30 seconds
      val scheduleTill = DateTime.now(DateTimeZone.UTC).plusSeconds(scheduleForNextSeconds)

      val emailsToBePushedToQueue = emailScheduledDAO.findAndUpdateEmailIdsForQueueingCampaignEmailsToRabbitMQV2(scheduleTill)
        .get

      logger.info(s"[PushToRabbitMqCronServiceV2] executeOnce found emailsToBePushedToQueue: ${emailsToBePushedToQueue.length}")

      emailsToBePushedToQueue foreach { em =>

        val emailScheduledId = em.emailScheduledId
        val sendingServerIP = em.sendingServerIP

        mqEmailPublisher.publish(

          message = MQEmailMessage(
            emailScheduledId = emailScheduledId,
            logRequestId = None,
            emailServiceProviderSendEmail = getEmailServiceProviderSendEmail(em.service_provider),
            team_id = em.teamId
          ),

          sendingIP = sendingServerIP
        ) match {

          case Failure(e) => logger.error(s"[PushToRabbitMqCronServiceV2] executeOnce publish error: emailScheduledId: $emailScheduledId :: $sendingServerIP :: ${LogHelpers.getStackTraceAsString(e)}")

          case Success(_) => logger.info(s"[PushToRabbitMqCronServiceV2] executeOnce publish success: emailScheduledId: $emailScheduledId :: $sendingServerIP")

        }

      }

      logger.info(s"[PushToRabbitMqCronService] DONE executeOnce DONE emailsToBePushedToQueue: ${emailsToBePushedToQueue.length}")


    } match {

      case Failure(e) =>

        runLock = false

        logger.error(s"[PushToRabbitMqCronService] executeOnce error: FATAL :: ${LogHelpers.getStackTraceAsString(e)}")

      case Success(_) =>

        runLock = false

        logger.info(s"[PushToRabbitMqCronService] executeOnce success")

    }
  }
}
