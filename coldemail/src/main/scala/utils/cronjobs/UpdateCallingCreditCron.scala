package utils.cronjobs

import org.apache.pekko.actor.ActorSystem
import api.AppConfig
import api.accounts.models.OrgId
import api.call.models.{SubAccountDetailsForUpdate, UpdateCallingCreditMessage}
import api.call.service.CallService
import play.api.libs.ws.WSClient
import utils.SRLogger
import utils.helpers.LogHelpers
import utils.mq.call.MqUpdateSubAccountCallingCredit

import scala.concurrent.ExecutionContext
import scala.util.{Failure, Success, Try}


/*
  Hi, This cron is used for updating remaining calling credits in db for a organization.

  * It runs hourly,
  * It fetches sub-account details for an organizations that are currently `active`:
      * it fetches using callService.getSubAccountsForUpdatingCredit function

  What are we trying to achieve using this cron ?

    We are trying to maintain remaining balance for sub-account

  Why ?

    So, that we can show the users that this much credit is left in their account and they need to recharge, and we can
    use remaining balance to allow / reject any request in future if needed.


  How are we doing it ?

    We are adding the call_credits in db, when we are also maintaining the remaining credits for the user
    when, but for maintaining the remaining balance we need to fetch the usage details from twilio ( 3rd party ) again
    and again. as we are using 3rd party services to provide calling feature.

    for doing so we have this cron which fetches sub-account from our db, and create a message
    `UpdateCallingCreditMessage`

    i.e,

    case class SubAccountDetailsForUpdate(
                                       sub_account_uuid: SubAccountUuid,
                                       org_id: OrgId
                                     )

    case class UpdateCallingCreditMessage(
                                           sub_account: SubAccountDetailsForUpdate
                                  )


    Queue slowly processes the pushed msgs,

    and queue fetches the usage balance of each of these org_ids, and then updates the balance in db.


    so this is the total use case,

    if we don't do it. we won't be able to show the remaining balance to the users


 */
class UpdateCallingCreditCron(
                               callService: CallService,
                               mqUpdateSubAccountCallingCredit: MqUpdateSubAccountCallingCredit

                             ) extends SRCronTrait {

  override val cronName: String = "UpdateCallingCreditCron"
  override val cronIntervalInSeconds: Int = AppConfig.cronHourlyCronServiceIntervalInSeconds

  def executeCron(

                 )(
                   using logger: SRLogger,
                   system: ActorSystem,
                   ws: WSClient,
                   ec: ExecutionContext
                 ) = {

    val subAccountsDetailsForUpdateCredit: Try[List[SubAccountDetailsForUpdate]] = callService.getSubAccountsForUpdatingCredit()

    subAccountsDetailsForUpdateCredit match {

      case Failure(e) =>

        logger.fatal(s"[UpdateCallingCreditCron]: Error while fetching sub-accounts to publish to queue : ${LogHelpers.getStackTraceAsString(e)}")


      case Success(subAccounts) =>

        subAccounts.foreach { msg =>

          val result = for {

            addingToTheQueue: Unit <- mqUpdateSubAccountCallingCredit.publish(
              msg = UpdateCallingCreditMessage(
                sub_account = msg
              )
            )

            _: Long <- callService.updateAddedToCallingCreditQueue(
              sub_account_uuid = msg.sub_account_uuid,
              org_id = msg.org_id
            )

          } yield {
            addingToTheQueue
          }
          result match {
            case Failure(e) =>
              logger.fatal(s"execute error: MqUpdateSubAccountCallingCredit publishing to queue sub-account-id: ${msg.sub_account_uuid} :: ${LogHelpers.getStackTraceAsString(e)}")
            case Success(_) =>
              logger.info(s"execute success: MqUpdateSubAccountCallingCredit published to queue sub-account-id: ${msg.sub_account_uuid}")
          }

        }
    }
  }

}
