package utils.cronjobs

import org.apache.pekko.actor.ActorSystem
import api.AppConfig
import api.campaigns.services.CampaignService
import api.phantombuster.PhantomBusterApiKeysService
import api.rep_mail_servers.services.SrMailServerService
import play.api.libs.ws.WSClient
import utils.cronjobs.linkedin_automation.LinkedinAutomationReportService
import utils.helpers.LogHelpers
import utils.mq.email.MQUnsnoozeSnoozedEmails
import utils.{SRLogger, StringUtils}
import utils.email_notification.service.EmailNotificationService
import utils.mq.spam.MQServerReportCheck

import scala.concurrent.ExecutionContext
import scala.util.{Failure, Success}


class DailyCronService(
                        campaignService: CampaignService,
                        phantomBusterApiKeysService: PhantomBusterApiKeysService,
                        emailNotificationService: EmailNotificationService,
                        linkedinAutomationReportService: LinkedinAutomationReportService,
                        mqServerReportCheck: MQServerReportCheck,
                      ) extends SRCronTrait  {

  /*
  def start()(implicit system: ActorSystem, ec: ExecutionContext) = {

    logger.info(s"[DailyCronService] start schedule")

    val interval = 12 * 60 * 60 * 1000.milliseconds

    system.scheduler.scheduleWithFixedDelay(60000.milliseconds, interval) { () => execute() }

  }

   */

  override val cronName: String = "DailyCronService"
  override val cronIntervalInSeconds: Int = AppConfig.cronDailyCronServiceIntervalInSeconds


  def executeCron(
                   
  )(
      using logger: SRLogger,
      system: ActorSystem,
      ws: WSClient,
      ec: ExecutionContext
  ): Unit = {
    val logRequestId = s"${StringUtils.genLogTraceId} [DailyCronService] "

    logger.info(s"$logRequestId execute")


    phantomBusterApiKeysService.resetExecutionTimeOnResetDate() match {
      case Failure(e) =>
        logger.fatal("Error while resetting execution time", e)

      case Success(ids) =>
        logger.info(s"Updated execution time for api keys with ids: $ids")
    }

    phantomBusterApiKeysService.fetchAccountsWithLowExecutionTimeLeft() match {
      case Failure(e) =>
        logger.fatal("Error while fetching phantombuster accounts with low execution time left.", e)

      case Success(emailsList) =>
        emailNotificationService.sendLowExecutionTimeLeftForPhantomBusterAccountsEmail(
          emails = emailsList
        )(ws, ec, logger)
    }

    campaignService.sendReportForInternalSchedulerRunLogForLast24Hours()(wsClient = ws, ec = ec, logger = logger) match {
      case Success(value) => //DO NOTHING
      case Failure(exception) => logger.fatal(s"Failed to sendReportForInternalSchedulerRunLogForLast15Mins", exception)
    }

    linkedinAutomationReportService.getLinkedinAutomationReport()(ws,ec,logger)
      .recover {
        case e =>
          logger.fatal("Error while sending Linkedin Automation Report", e)
      }

    {
      given localSrLogger: SRLogger = logger.appendLogRequestId("mqServerReportCheck.publishAll")

      mqServerReportCheck.publishAll()

    }

  }

}
