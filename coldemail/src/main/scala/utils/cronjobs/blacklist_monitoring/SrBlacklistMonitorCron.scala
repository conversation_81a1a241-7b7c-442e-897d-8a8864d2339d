package utils.cronjobs.blacklist_monitoring

import api.AppConfig
import org.apache.pekko.actor.ActorSystem
import api.rep_mail_servers.models.RepMailServer
import app_services.blacklist_monitoring.models.BlacklistCheckStatus
import app_services.blacklist_monitoring.services.SrBlacklistCheckService
import org.joda.time.DateTime
import org.joda.time.format.{DateTimeFormat, DateTimeFormatter}
import play.api.libs.ws.WSClient
import utils.SRLogger
import utils.cronjobs.SRCronTrait
import utils.email_notification.service.EmailNotificationService

import scala.concurrent.duration.DurationInt
import scala.concurrent.{Await, ExecutionContext, Future}

class SrBlacklistMonitorCron(
  srBlacklistCheckService: SrBlacklistCheckService,
  emailNotificationService: EmailNotificationService
) extends SRCronTrait {

  override val cronName: String = "SrBlacklistMonitorCron"
  override val cronIntervalInSeconds: Int = 3600

  override def executeCron()(
    using logger: SRLogger,
    system: ActorSystem,
    ws: WSClient,
    ec: ExecutionContext
  ): Unit = {
    
    // check only in production environment 
    if (AppConfig.isProd) {

      val res = for {

        checkResults <- srBlacklistCheckService
          .checkAllBlacklists(
            logger = logger
          )

        _ <- if (checkResults.nonEmpty) {

          sendEmailNotification(
            checkResults = checkResults,
            logger = logger
          )

        } else {

          Future.successful(true)

        }

      } yield {

        true

      }

      // FIXME: executeCron needs to accept Futures only
      Await.result(
        res,
        atMost = 10000.millis
      )

    }

  }


  private def sendEmailNotification(
    checkResults: Seq[(RepMailServer, BlacklistCheckStatus)],
    logger: SRLogger
  )(
    implicit ws: WSClient,
    ec: ExecutionContext
  ): Future[Boolean] = {
    val date = {

      val dtf: DateTimeFormatter = DateTimeFormat.forPattern("dd MMM YYYY")

      dtf.print(DateTime.now())

    }

    val subject = {

      val anyInBlacklist = checkResults.exists(_._2 == BlacklistCheckStatus.FAILED)

      val subKey = s"SrBlacklistMonitor :: $date ::"

      if (anyInBlacklist) {
        s"[ACTION REQUIRED] $subKey: IP/Domain found in Blacklist"
      } else {
        s"$subKey: IP/Domain checks look fine (checked ${checkResults.length} servers)"
      }

    }


    val body = {

      val overallResultHtml = checkResults
        .sortBy(res => {
          res._2 match {

            // sort all failed checks first

            case BlacklistCheckStatus.PASSED => 3

            case BlacklistCheckStatus.WARNING => 2

            case BlacklistCheckStatus.FAILED => 1

            case BlacklistCheckStatus.NOT_FOUND => 4

          }
        })
        .map(res => {

          val server = res._1
          val blacklistStatus = res._2

          val resultString = s"${server.reverse_dns} (${server.public_ip}): ${blacklistStatus.toString}"

          resultString

        })
        .mkString("<br/><br/>")


      s"""
         |Blacklist check results as on $date
         |<br/>
         |<br/>
         |$overallResultHtml
         |""".stripMargin
    }


    emailNotificationService
      .sendMailFromAdminFut(
        toEmail = "<EMAIL>",
        ccEmail = Some("<EMAIL>,<EMAIL>"),
        subject = subject,
        body = body,
        toName = None
      )(logger = logger, wsClient = ws, ec = ec)
      .map(_ => true)

  }


}
