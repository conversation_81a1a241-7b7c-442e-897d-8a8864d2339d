package utils.cronjobs

import api.AppConfig
import api.accounts.TeamId
import api.accounts.service.OrganizationService
import api.columns.ProspectColGenStatus
import api.columns.ProspectColGenStatus.Failed
import api.llm.dao.{LlmAuditLogDAO, LlmAuditLogData, UpdateStatusData}
import api.llm.models.LlmFlow
import api.sr_ai.dao.SrAiLockJedisDAO
import org.apache.pekko.actor.ActorSystem
import play.api.libs.ws.WSClient
import utils.SRLogger
import utils.cronjobs.SRCronTrait
import utils.helpers.LogHelpers
import utils.mq.ai_content_generation.{MqAiContentGenerationPublisher, MqAiContentGenerationRequest}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}
import scala.collection.mutable.ListBuffer
import scala.math.Integral.Implicits.infixIntegralOps

// MARK: - Placeholder Definitions
// These are placeholders and should be defined in their appropriate packages (e.g., models, services).
// Ensure these align with your actual project structure and definitions.

// package api.llm.models (or similar, reflecting where your LlmAuditLogData is defined)
// /**
//  * Represents the structure of an LLM audit log entry as expected by this cron job.
//  * This is a simplified placeholder. The actual LlmAuditLogService.getPendingLogs
//  * should return objects that conform to this structure, especially ensuring `id` is Long
//  * and `queueMessage` contains the base MqAiContentGenerationRequest data.
//  *
//  * @param id Unique identifier for the log entry (must be Long).
//  * @param status Current status of the log (e.g., PENDING, QUEUED, SUCCESS, FAILED).
//  * @param updated_count Number of times this log entry has been processed/updated.
//  * @param queueMessage The base MqAiContentGenerationRequest object retrieved from the audit log.
//  *                     Its own logId field will be overridden by this LlmAuditLogForCron's id.
//  */
// case class LlmAuditLogForCron(
//   id: Long,
//   status: String, // Example: "PENDING". Actual type might be ProspectColGenStatus.
//   updated_count: Int,
//   queueMessage: MqAiContentGenerationRequest // Assumes the service deserializes this
// )

// package api.llm.services (or similar, reflecting where your LlmAuditLogService is defined)
// /**
//  * Service trait for managing LLM audit logs.
//  */
// trait LlmAuditLogService {
//   /**
//    * Fetches a list of pending LLM audit logs, prepared for the cron.
//    * @param limit The maximum number of logs to fetch.
//    * @param ec ExecutionContext for asynchronous operations.
//    * @return A Future sequence of LlmAuditLogForCron objects.
//    */
//   def getPendingLogsForCron(limit: Int)(implicit ec: ExecutionContext): Future[Seq[LlmAuditLogForCron]]
//
//   /**
//    * Updates the status and count of an LLM audit log.
//    * @param logId The ID of the log to update (must be Long).
//    * @param status The new status (e.g. "QUEUED").
//    * @param newCount The new updated_count.
//    * @param ec ExecutionContext for asynchronous operations.
//    * @return A Future of Unit.
//    */
//   def updateLogStatusAndCount(logId: Long, status: String, newCount: Int)(implicit ec: ExecutionContext): Future[Unit]
//
//   // Optional: Consider adding a method to mark logs as permanently failed after max attempts.
//   // def markAsMaxAttemptsReached(logId: Long, count: Int)(implicit ec: ExecutionContext): Future[Unit]
// }

// utils.mq.ai_content_generation.MqAiContentGenerationRequest is defined in its own file.
// It now includes `logId: Long`.


// MARK: - AiContentGenerationCron
/**
  * This cron job is responsible for processing pending LLM audit logs for AI content generation.
  *
  * - It runs periodically (e.g., every 60 seconds).
  * - Fetches pending LLM audit logs.
  * - If a log's update count is less than 5, it pushes a request to the AI Content Generation MQ
  *   and updates the log status to "QUEUED" and increments its update count.
  * - If a log's update count is 5 or more, it logs a warning and skips the entry,
  *   noting that a final handling strategy for max attempts is pending.
  */
class AiContentGenerationCron(
                               llmAuditLogDAO: LlmAuditLogDAO, // To be injected (e.g., from services.LlmAuditLogService)
                               mqPublisher: MqAiContentGenerationPublisher, // To be injected
                               srAiLockJedisDAO: SrAiLockJedisDAO,
                               organizationService: OrganizationService
) extends SRCronTrait {

  override val cronName: String = "AiContentGenerationCron"
  override val cronIntervalInSeconds: Int = 60 // Runs every 60 seconds. Adjust as per requirements.

  /**
   * What it does:
   * Executes the main logic of the cron job.
   * Fetches pending logs and processes them based on their update count.
   *
   * Why it is like this:
   * This method encapsulates the core responsibilities of the cron:
   * identifying tasks (pending logs), deciding eligibility (update count),
   * and dispatching them (to MQ) or flagging them (max attempts).
   * It uses asynchronous operations to avoid blocking and handles potential errors gracefully.
   */
  def executeCron()(
    implicit logger: SRLogger,
    system: ActorSystem,
    ws: WSClient, // Retained for consistency with SRCronTrait, may not be directly used if LlmAuditLogService handles its own client needs.
    ec: ExecutionContext
  ): Unit = {
    logger.info(s"[$cronName]: Starting cron run.")
    if(!srAiLockJedisDAO.checkLock(aiModel = LlmFlow.MagicColumnFlow.model)) {

      // Correctly convert Try[List[...]] to Future[Seq[...]]
      val pendingLogsFuture: Future[Seq[LlmAuditLogData[MqAiContentGenerationRequest]]] =
        Future.fromTry(
          llmAuditLogDAO.getAttempts[MqAiContentGenerationRequest](flow = LlmFlow.MagicColumnFlow)
        ).map(_.toSeq) // .toSeq is robust as List is a Seq

      pendingLogsFuture.foreach { logs =>
        if (logs.isEmpty) {
          logger.info(s"[$cronName]: No pending LLM audit logs found to process.")
        } else {
          logger.info(s"[$cronName]: Found ${logs.size} pending LLM audit log(s) to process.")
        }

        val updatesToPerform = ListBuffer[UpdateStatusData]()
        val successfullyPublishedLogDetails = ListBuffer[(Long, Int)]() // Stores (logId, newCount)

        logs
          .groupBy(_.queue_message.orgId)
          .foreach {logsWithOrg =>

            val (orgId, logsForOrg) = logsWithOrg
            val totalCredits: Long = organizationService.getRemainingLeadFinderCredits(orgId).getOrElse(0)
            val howManyCanWePushBasedOnCredits: Int = Math.floor(totalCredits / AppConfig.LeadFinderCharges.singleAiContentGeneration).toInt
            logsForOrg
              .take(howManyCanWePushBasedOnCredits)
              .foreach { log =>
              // Log an entry for visibility on which log is being processed.
              // "if the updated_count is more than 0, that means it has been attempted previously and failed" - This is an observation.
              logger.info(s"[$cronName]: Processing log ID [${log.id}], current status: ${log.status}, updated_count: ${log.updated_count}.")

              // 2. If the updated count of a particular entry is more than 5 (i.e., >= 5 attempts failed)
              if (log.updated_count >= 5) {
                val updateData = UpdateStatusData(
                  id = log.id,
                  teamId = log.team_id,
                  newStatus = Failed
                )

                llmAuditLogDAO.updateStatus(
                  updateStatusData = List(updateData),
                  updateCount = true
                ) match {
                  case Success(value) => //Do nothing
                  case Failure(exception) =>
                    logger.shouldNeverHappen(s"Failed to update audit log status for logId: ${log.id}", Some(exception))

                }
                logger.warn(s"[$cronName]: Log ID [${log.id}] has updated_count >= 5 (value: ${log.updated_count}). " +
                  "This means it has failed 5 or more times. Skipping as per current logic. " +
                  "A decision on how to permanently handle such entries (e.g., mark as 'MAX_ATTEMPTS_FAILED') is pending.")
                // Optionally: Add to a different bulk update list to mark as FAILED
                // val failedUpdateData = UpdateStatusData(id = log.id, teamId = log.team_id, newStatus = ProspectColGenStatus.Failed) // Ensure Failed status exists
                // updatesToPerformForFailed += failedUpdateData // (requires another list and update call)
              } else {
                // 3. If the updated count is less than 5, then push it to mq for content generation, update the status to queued

                // Construct the MQ request using the data from the audit log's queueMessage field,
                // and override/set the logId with the audit log's own ID.
                val baseMqRequest = log.queue_message
                val requestToSend = baseMqRequest.copy(logId = Some(log.id))

                logger.info(s"[$cronName]: Attempting to publish message for log ID [${log.id}] (updated_count: ${log.updated_count}) with payload: $requestToSend to AI Content Generation MQ.")

                mqPublisher.publishMessage(requestToSend) match {
                  case Success(_) =>
                    logger.info(s"[$cronName]: Successfully published message for log ID [${log.id}]. Adding to bulk update for status QUEUED.")

                    val newCount = log.updated_count + 1

                    updatesToPerform += UpdateStatusData(
                      id = log.id, // Use log.id from the fetched LlmAuditLogData
                      teamId = log.team_id, // Use log.team_id from the fetched LlmAuditLogData
                      newStatus = ProspectColGenStatus.Queued // Ensure Queued status exists
                    )
                    successfullyPublishedLogDetails += ((log.id, newCount))
                  case Failure(ex) =>
                    logger.error(s"[$cronName]: Failed to publish message for log ID [${log.id}] to MQ: ${LogHelpers.getStackTraceAsString(ex)}")
                  // If publishing to MQ fails, the log remains in its current state (e.g., PENDING).
                  // The updated_count is not incremented here, as the attempt to process via MQ did not initiate.
                  // It will be picked up in a future cron run if it's still PENDING.
                }
              }
            }
          }

        if (updatesToPerform.nonEmpty) {
          logger.info(s"[$cronName]: Attempting to bulk update status to QUEUED for ${updatesToPerform.size} logs: ${updatesToPerform.map(_.id).mkString(", ")}")
          llmAuditLogDAO.updateStatus(updatesToPerform.toList, updateCount = true) match {
            case Success(updatedRowCount) =>
              logger.info(s"[$cronName]: Successfully bulk updated status to QUEUED. Rows affected: $updatedRowCount.")
              if (updatedRowCount != updatesToPerform.size) {
                logger.warn(s"[$cronName]: Bulk update rows affected ($updatedRowCount) does not match count of logs intended for update (${updatesToPerform.size}).")
              }
              successfullyPublishedLogDetails.foreach { case (logId, newCount) =>
                // This logging confirms inclusion in the batch; actual row update depends on DAO logic and potential race conditions if items were modified elsewhere.
                logger.info(s"[$cronName]: Log ID [$logId] was part of bulk update attempt. Intended new count: $newCount.")
              }
            case Failure(ex) =>
              logger.error(s"[$cronName]: Failed to bulk update status to QUEUED for logs: ${updatesToPerform.map(_.id).mkString(", ")}. Error: ${LogHelpers.getStackTraceAsString(ex)}")
          }
        } else {
          logger.info(s"[$cronName]: No logs were successfully published to MQ, no bulk status update needed.")
        }
      }

      pendingLogsFuture.recover {
        case e: Throwable =>
          logger.fatal(s"[$cronName]: Critical error fetching pending LLM audit logs: ${LogHelpers.getStackTraceAsString(e)}")
      }
      () // Explicitly return Unit
    } else {
      //model is locked so do nothing
    }
  }
} 