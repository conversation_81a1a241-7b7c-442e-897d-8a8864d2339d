package utils.cronjobs

import org.apache.pekko.actor.ActorSystem
import api.AppConfig
import api.emails.EmailSettingDAO
import api.emails.daos.EmailHealthCheckDAO
import api.linkedin.LinkedinSettingService
import api.prospects.dao_service.ProspectDAOService
import api.tracking_host.services.CustomTrackingDomainService
import play.api.libs.ws.WSClient
import utils.emailvalidation.models.EmailValidationToolV2
import utils.emailvalidation.models.EmailValidationToolV2.{BOUNCER, DEBOUNCE, LISTCLEAN}
import utils.{Help<PERSON>, SRLogger, StringUtils}
import utils.emailvalidation.{EmailValidationBatchRequestModel, EmailValidationModel, EmailValidationService}
import utils.helpers.LogHelpers
import utils.mq.email.{EmailHealthCheckSettingRecord, MqEmailHealthCheck}
import utils.mq.statsupdater.{MQListStatsUpdater, MQListStatsUpdaterMsg}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}


class OneMinutelyCronService(
                              prospectDAOService: ProspectDAOService,
                              mqListStatsUpdater: MQListStatsUpdater,
                              emailValidationService: EmailValidationService,
                              linkedinSettingService: LinkedinSettingService,
                              emailValidationBatchRequestModel: EmailValidationBatchRequestModel,
                              emailValidationModel:EmailValidationModel,
                              customTrackingDomainService: CustomTrackingDomainService,
                              emailHealthCheckDAO: EmailHealthCheckDAO,
                              emailSettingDAO: EmailSettingDAO,
                              mqEmailHealthCheck: MqEmailHealthCheck
) extends SRCronTrait {

  override val cronName: String = "OneMinutelyCronService"
  override val cronIntervalInSeconds: Int = AppConfig.cronOneMinutelyIntervalInSeconds

  def executeCron(
    
  )(
    using logger: SRLogger,
    system: ActorSystem,
    ws: WSClient,
    ec: ExecutionContext
  ) = {


   val StatusCheckandAPIAssigningLogger= logger
     .appendLogRequestId(s"evscta_${StringUtils.genLogTraceId}")

   emailValidationModel.getHighAndMediumPriorityEmailsForToolAssignment() match {
        case Success(emailList)=>

                emailValidationService.checkStatusAndAssignToolForEmailValidation(
                    emailList = emailList,
                    primaryValidationTool = EmailValidationToolV2.BOUNCER
                )(ws = ws, ec = ec, srLogger = StatusCheckandAPIAssigningLogger) match {
                    case Success(value) =>
                        StatusCheckandAPIAssigningLogger.info(s"Tools were assigned to High and medium priority emails succesfully Tool:${value.validationTool} and emails_Assigned: ${value.emails_Assigned}")
                    case Failure(exception) =>
                        StatusCheckandAPIAssigningLogger.fatal(s"${exception.getMessage}")
                }


        case Failure(exception)=>
            StatusCheckandAPIAssigningLogger.fatal(s"Error while fetching record $exception")

   }




   emailValidationModel.getLowPriorityEmailsForToolAssignment() match {
    case Success(emailList)=>
        emailValidationService.checkStatusAndAssignToolForEmailValidation(
                    emailList = emailList,
                    primaryValidationTool = EmailValidationToolV2.DEBOUNCE
                )(ws = ws,ec=ec,srLogger = StatusCheckandAPIAssigningLogger) match {

                    case Success(value) =>
                        StatusCheckandAPIAssigningLogger.info(s"Tools were assigned to low priority emails : ${value}")
                    case Failure(exception) =>
                        StatusCheckandAPIAssigningLogger.fatal(s"${exception.getMessage}")
                }


//        if(emailList.size<=20){
//
//            emailValidationService.checkStatusAndAssignToolForEmailValidation(
//                emailList = emailList,
//                primaryValidationTool = EmailValidationToolV2.LISTCLEAN
//            )(ws = ws,ec=ec,srLogger = StatusCheckandAPIAssigningLogger) match {
//
//                case Success(value) =>
//                    StatusCheckandAPIAssigningLogger.info(s"Tools were assigned to low priority emails : ${value}")
//                case Failure(exception) =>
//                    StatusCheckandAPIAssigningLogger.fatal(s"${exception.getMessage}")
//            }
//
//        }else {
//            /* NOTE: Currently we are sending the low priority emails 50% to listclean and 50% toDebounce
//                     This will change in future with regards to the performance of these APIs.
//             */
//            val n = (emailList.size) / 2
//            val (emailList_1, emailList_2) = emailList.splitAt(n)
//
//            emailValidationService.checkStatusAndAssignToolForEmailValidation(
//                emailList = emailList_1,
//                primaryValidationTool = EmailValidationToolV2.LISTCLEAN
//            )(ws = ws,ec=ec,srLogger = StatusCheckandAPIAssigningLogger) match {
//
//                case Success(value) =>
//                    StatusCheckandAPIAssigningLogger.info(s"Tools were assigned to low priority emails : ${value}")
//                case Failure(exception) =>
//                    StatusCheckandAPIAssigningLogger.fatal(s"${exception.getMessage}")
//            }
//            emailValidationService.checkStatusAndAssignToolForEmailValidation(
//                emailList = emailList_2,
//                primaryValidationTool = EmailValidationToolV2.DEBOUNCE
//            )(ws = ws,ec=ec,srLogger = StatusCheckandAPIAssigningLogger) match {
//
//                case Success(value) =>
//                    StatusCheckandAPIAssigningLogger.info(s"Tools were assigned to low priority emails : ${value}")
//                case Failure(exception) =>
//                    StatusCheckandAPIAssigningLogger.fatal(s"${exception.getMessage}")
//            }
//        }

    case Failure(exception)=>
     StatusCheckandAPIAssigningLogger.fatal(s"Error while fetching record $exception")

   }

    val findAndMakeBatchRequestLogger = logger
      .appendLogRequestId(s"evbr_${StringUtils.genLogTraceId}")

    emailValidationService.findAndMakeBatchRequest()(
      srLogger = findAndMakeBatchRequestLogger,
      ws = ws,
      ec = ec
    )
      .map(_ =>
        findAndMakeBatchRequestLogger.info("done")
      )
      .recover { case e =>

        findAndMakeBatchRequestLogger.fatal("findAndMakeBatchRequest fatal", e)
      }

    /////////////


    val checkStatusOfBatchRequestLogger = logger
      .appendLogRequestId(s"evbrst_${StringUtils.genLogTraceId}")

    emailValidationBatchRequestModel.findForCheckingStatus match {
      case Failure(e) =>

        checkStatusOfBatchRequestLogger.fatal(s"findForCheckingStatus", err = e)

      case Success(batchRequests) =>

        checkStatusOfBatchRequestLogger.info(s"findForCheckingStatus: total found: ${batchRequests.length}")

        val seqFut = batchRequests.map { case (checkStatusBatchRequestData) =>

          checkStatusOfBatchRequestLogger.info(s"checkStatusOfBatchRequestAndUpdateValidationResponse start: srid_${checkStatusBatchRequestData.id}")

          // call for validation goes from here - we need to
          // enrich it with campaign_id, etc
          emailValidationService.checkStatusOfBatchRequestAndUpdateValidationResponse(
            serviceRequestId = checkStatusBatchRequestData.service_request_id,
              checked_via_tool = checkStatusBatchRequestData.checked_via_tool
          )(
            srLogger = checkStatusOfBatchRequestLogger,
            ws = ws,
            ec = ec
          )
            .map(_ =>
              checkStatusOfBatchRequestLogger.info(s"checkStatusOfBatchRequestAndUpdateValidationResponse done: srid_${checkStatusBatchRequestData.id}")
            )
            .recover { case e =>

              checkStatusOfBatchRequestLogger.fatal(s"checkStatusOfBatchRequestAndUpdateValidationResponse error: srid_${checkStatusBatchRequestData.id}", e)
            }

        }

        Future.sequence(seqFut)
          .map(_ => checkStatusOfBatchRequestLogger.info("overall done with execute"))
          .recover { case e => checkStatusOfBatchRequestLogger.fatal("overall error", err = e) }



    }


    prospectDAOService.getActiveListIdsForUpdatingListStats match {

      case Failure(e) =>

        logger.fatal(s"Prospect.getActiveListIdsForListStats ${LogHelpers.getStackTraceAsString(e)}")

      case Success(lists) =>

        lists.foreach { case (listId, teamId) => {

          mqListStatsUpdater.publish(message = MQListStatsUpdaterMsg(listId = listId, teamId = teamId))

        }
        }
    }

//    Date: 02-Jun-2025 : Commenting this code as we are not using phantom buster for linkedin automation
//    linkedinSettingService.fetchAndUpdateStatusOfLinkedinAccountsToRecreateSession() match {
//      case Failure(e) =>
//        logger.fatal("Error while fetching and updating status of linkedin accounts to recreate session.", e)
//
//      case Success(accountsList) =>
//        accountsList.foreach(uuidAndTeamId => {
//          mqLinkedinRecreateSession.publish(
//            msg = uuidAndTeamId
//          ) match {
//            case Failure(e) =>
//              logger.fatal(s"Error while publishing $uuidAndTeamId to recreate session.", e)
//
//            case Success(_) =>
//          }
//        })
//    }

    ///////////


    customTrackingDomainService.validateAndActivateCustomTrackingDomain()(logger = logger, ws = ws, ex = ec)
      .map(res => {
        logger.debug("cloudflareService.validateAndActivateCustomTrackingDomain done")
      })
      .recover { case e =>
        logger.fatal(s"cloudflareService.validateAndActivateCustomTrackingDomain error: ${LogHelpers.getStackTraceAsString(e)}")
      }
      
      
      //////////
      
      queueEmailsToFetchHealthResults()

  }


    def queueEmailsToFetchHealthResults()(using logger: SRLogger): Unit = {
        logger.info("start queueEmailsToFetchHealthResults")
        emailHealthCheckDAO.getEmailsToFetchHealthResults match {
            case Failure(exception) =>
                logger.error(s"Error while fetching emails to fetch health results", exception)

            case Success(emails) =>
                if (emails.nonEmpty) {
                    emailHealthCheckDAO.updateEmailHealthCheckStatusToQueued(recordIds = emails.map(_.id)) match {
                        case Failure(exception) =>
                            logger.error(s"Error while updating email health check status to queued", exception)

                        case Success(_) =>
                            logger.info(s"Successfully updated email health check status to queued")

                            emails.foreach { emailRecord =>


                                mqEmailHealthCheck.publish(
                                    EmailHealthCheckSettingRecord(emailRecord)
                                ) match {
                                    case Failure(exception) =>
                                        logger.error(s"Error while publishing email health check record to mqEmailHealthCheck", exception)
                                    case Success(_) =>
                                        logger.info(s"Successfully published email health check record to mqEmailHealthCheck")
                                }


                            }

                    }
                } else {
                    logger.info("No emails found to be tested for Health Check")
                }
        }

    }

}
