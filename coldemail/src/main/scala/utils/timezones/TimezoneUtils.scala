package utils.timezones

import api.call.models.TimezoneToCountryCodeList.timezoneToCountryMap
import org.joda.time.{DateTime, DateTimeZone}
import play.api.libs.json.{Json, Reads}

case class CountryCodeTimezone(
  country: String,
  region: String,
  timezone: String
)

object CountryCodeTimezone {
  implicit val reads: Reads[CountryCodeTimezone] = Json.reads[CountryCodeTimezone]
}

object TimezoneUtils {

  private val iso2CodeToTz: Map[String, String] = {


    //    val stream = new FileInputStream("/Users/<USER>/Projects/coldemail/app/utils/timezones/countries_iso2_to_timezone.json")
    //    val json = try {
    //      Json.parse(stream)
    //    } finally {
    //      stream.close()
    //    }
    //
    //    val list = json.as[Vector[CountryCodeTimezone]]
    //      .map(l => l.copy(country = s""""${l.country}"""", region = s""""${l.region}"""", timezone = s""""${l.timezone}""""))


    var map = Map[String, String]()

    TimezoneData.countryCodeToTimezone.foreach(l => map += l.country -> l.timezone)

    map

  }

  //  val countryCodeToName = {
  //
  //
  //    val stream = new FileInputStream("/Users/<USER>/Projects/coldemail/app/utils/timezones/countries_iso2_to_iso3.json")
  //    val json = try {
  //      Json.parse(stream)
  //    } finally {
  //      stream.close()
  //    }
  //
  //
  //    json.as[Map[String, String]]
  //      .map(l => (s""""${l._1}"""", s""""${l._2}""""))
  //  }

  // map of lower cased country name to timezone
  def getCountryNameFromISO2CountryCode(cc: String): Option[String] = {


    TimezoneData.countryCodeToName.find(c => c._1.toLowerCase.trim == cc.toLowerCase.trim).map(c => c._2)

  }

  def getISO2CountryCodeFromCountryName(cc: String): Option[String] = {


    TimezoneData.countryCodeToName.find(c => c._2.toLowerCase.trim == cc.toLowerCase.trim).map(c => c._1)

  }

  // map of lower cased country name to timezone
  val countryToTimezone: Map[String, Option[String]] = {

    var map: Map[String, Option[String]] = Map()

    iso2CodeToTz.foreach(c => map += c._1.toLowerCase -> Some(c._2))

    TimezoneData.countryCodeToName.foreach(c => map += c._2.toLowerCase() -> iso2CodeToTz.get(c._1))


    TimezoneData.countryCodeToISO3.foreach(c => map += c._2.toLowerCase() -> iso2CodeToTz.get(c._1))

    map
  }

  def isCountryAndTimezoneValid(countryCode: String, timezone: String): Boolean = {
    timezoneToCountryMap.get(timezone).exists(_.contains(countryCode))
  }


  private val usaNames = Seq("us", "u s", "united states", "united states of america", "usa", "u.s.a.", "u.s.a")

  def getTimezoneForCountry(country: Option[String]): Option[String] = {

    // do not auto-guess timezone for US: LA vs NY timezones is making some users furious:
    // https://app.intercom.com/a/apps/xmya8oga/inbox/inbox/all/conversations/26638347338

    if (country.isDefined && usaNames.contains(country.get.toLowerCase.trim)) {
      None
    } else {
      country flatMap { c => countryToTimezone.get(c.toLowerCase()).flatten }
    }
  }

  // private val lowerCasedTimezones = TimezoneData.googleCalTzs.map(_.toLowerCase)

  private val lowercaseTzToActualTz: Map[String, String] = TimezoneData.googleCalTzs
    .map(tz => {

      (tz.toLowerCase -> tz)

    })
    .toMap

  def isValidTimezone(tz: String): Boolean = {

    checkAndReturnValidTimezone(
      tz = tz
    ).isDefined

  }

  final def checkAndReturnValidTimezone(tz: String): Option[String] = {


    val actualTz: Option[String] = lowercaseTzToActualTz.get(tz.toLowerCase.trim)

    /*
    if (lowerCasedTimezones.contains(tz.toLowerCase.trim)) {

      Some(tz.trim)
      */

    if (actualTz.isDefined) {

      actualTz

    } else if (TimezoneData.deprecatedTimezoneAliasesLowercased.contains(tz.toLowerCase.trim)) {
      // if timezone is deprecated

      Some(
        TimezoneData.deprecatedTimezoneAliasesLowercased(tz.toLowerCase.trim).trim
      )

    } else {

      None

    }

  }

  def getDayOfWeekInTimezone(
                            currTime: DateTime = DateTime.now(), // passing this param for testing
                            timezone: String
                            ): Int = {
    // we are doing a modulo-7 (% 7) because in postgres Sunday == 0 and in JodaTime Sunday == 7
    currTime
      .withZone(DateTimeZone.forID(timezone))
      .getDayOfWeek() % 7
  }


  def getStartOfDayWithTimezone(timezone: String): DateTime = DateTime
    .now()
    .withZone(DateTimeZone.forID(timezone))
    .withTimeAtStartOfDay()
}
