package utils.sr_csv_parser

import play.api.libs.json.JsValue
import utils.SRLogger

import scala.concurrent.ExecutionContext
import scala.util.Try

case class SRCsvParserRawResult(

  parserName: String,
  rowMapFromCSV: Seq[Map[String, String]],
  charsetDetected: Option[String] = None,
  parserContext: Option[JsValue] = None

)

trait SRCsvParserTrait {

  protected val parserName: String

  def getRowsUsingParser(
    fileAbsolutePath: String,
    logger: SRLogger
  )(implicit ec: ExecutionContext): Try[SRCsvParserRawResult]

  protected def createMapFromCSVHeaderAndRows(

    csvHeaderRow: Seq[String],
    dataRows: Seq[Seq[String]],
    logger: SRLogger

  ): Try[Seq[Map[String, String]]] = Try {

    val headerRowTrimmed = csvHeaderRow.map(_.trim)
    val totalColumns = headerRowTrimmed.size

    dataRows
      .zipWithIndex
      .map { case (row, rowIndex) => {

        if (row.size < totalColumns) {
          logger.error(s"Bad Row [not enough columns]: rowIndex: $rowIndex")
        }

        headerRowTrimmed
          .zipWithIndex
          .map { case (headerName, index) => {

            (headerName, row(index))

          }
          }
          .filter { case (headername, rowVal) => headername != null && headername.trim.nonEmpty } // ignore columns with empty headers
          .toMap


      }
      }
  }




}