package utils.affiliate

import api.AppConfig
import api.accounts.AccountDAO
import api.accounts.dao.OrganizationBillingDAO
import api.accounts.models.{AccountId, OrgId}
import api.billing.PaymentGateway
import com.typesafe.config.{Config, ConfigFactory}
import play.api.Logging
import play.api.libs.json.JsValue
import play.api.libs.ws.WSClient
import utils.SRLogger
import utils.email_notification.service.EmailNotificationService
import utils.payment.api_services.PGPlanInterval

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}

case class AffiliateException(message: String, cause: Throwable = None.orNull)
  extends Exception(message, cause)

trait SRAffiliate extends Logging{

  val BASE_URL: String

  def trackSignup(
                   orgId: OrgId,
                   affiliateCookieValue: String,
                   realIP: Option[String],
                   logger: SRLogger,
                   firstPromoterReferrerAccountDetails: Option[FirstPromoterReferrerAccountDetails],
                   admitadGclidCookieValue: Option[String]
  )(implicit ws: WSClient, ec: ExecutionContext): Future[JsValue]


  def trackAffiliateEvent(
                           orgId: OrgId,
                           orgName: String,
                           planIntervalOpt: Option[PGPlanInterval.Value],
                           affiliateEvent: AffiliateEvent,
                           pgCustomerId: String,
                           pg: PaymentGateway.Value,
                           chargeId: String,
                           amountInCents: Long,
                           logger: SRLogger,
                           fpReferrerTrackId: Option[String],
                           admitad_uid: Option[String],
                           admitad_gclid: Option[String]
                         )(implicit ws: WSClient, ec: ExecutionContext): Future[TrackAffiliateResponseStatus]

}
