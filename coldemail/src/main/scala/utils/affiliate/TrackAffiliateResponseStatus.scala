package utils.affiliate

import play.api.libs.json.{Format, Js<PERSON>rror, JsResult, JsString, JsSuccess, JsValue}

import scala.util.{Failure, Success, Try}

sealed trait TrackAffiliateResponseStatus

object TrackAffiliateResponseStatus {

  private val success = "Success"
  private val failure = "Failure"

  case object ResSuccess extends TrackAffiliateResponseStatus {
    override def toString = success
  }

  case object ResFailure extends TrackAffiliateResponseStatus {
    override def toString = failure
  }

  def fromString(key: String): Try[TrackAffiliateResponseStatus] = Try {
    key match {
      case `success` => ResSuccess
      case `failure` => ResFailure
    }
  }

  implicit def format: Format[TrackAffiliateResponseStatus] = new Format[TrackAffiliateResponseStatus] {

    override def reads(json: JsValue): JsResult[TrackAffiliateResponseStatus] = {
      fromString(json.as[String]) match {
        case Failure(e) => JsError(e.toString)
        case Success(value) => JsSuccess(value)
      }
    }

    override def writes(o: TrackAffiliateResponseStatus): JsValue = JsString(o.toString)
  }
}

