package utils.mq.limit_check

import api.accounts.models.OrgId
import api.accounts.service.AccountOrgBillingRelatedService
import io.sr.billing_common.models.AddonLicenceType
import org.apache.pekko.actor.ActorSystem
import play.api.libs.ws.WSClient
import utils.SRLogger
import utils.mq.services.{MQConfig, MQConsumer, MQPublisherService}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.Try

case class MqLimitValidatorMsg(
  orgId: OrgId ,
  addonLicenceType: Option[AddonLicenceType]
  )

class MqLimitValidator(
                                     accountOrgBillingRelatedService: AccountOrgBillingRelatedService,
                                     mqLimitValidatorPublisher: MqLimitValidatorPublisher
                                   ) extends  MQConsumer[MqLimitValidatorMsg] {

  val queueBaseName: String = MQConfig.prospectsContactedLimitCheckQueueBaseName
  val prefetchCount: Int = MQConfig.prospectsContactedLimitCheckQueuePrefetchCount
  override protected val mqPublisherService: MQPublisherService[MqLimitValidatorMsg] = mqLimitValidatorPublisher


  def startConsumer()(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem): Try[String] = {
    startMsgConsumer(queueBaseName = queueBaseName, prefetchCount = prefetchCount)
  }
  override def processMessage(msg: MqLimitValidatorMsg)(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem): Future[Any] = {

    given Logger: SRLogger = new SRLogger(logRequestId = s"MqLimitValidator :: processMessage :: $msg ::")
    
    Future.fromTry {
      accountOrgBillingRelatedService.validateOrgLimitsAndAddWarning(
        orgId = msg.orgId,
        addonLicenceType = msg.addonLicenceType
      )
    }
      .map(_ => {
        Logger.info(s"checkProspectsContactedLimit ran for orgId_${msg.orgId.id}")
      })
      .recover{
        case e =>
          Logger.fatal("Error in checkProspectsContactedLimit", e)
      }
    
  }

}
