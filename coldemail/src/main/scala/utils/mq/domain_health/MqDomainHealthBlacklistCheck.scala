package utils.mq.domain_health

import org.apache.pekko.actor.ActorSystem
import api.AppConfig
import api.domain_health.{DomainHealthCheckDAO, DomainHealthCheckService}
import api.emails.{EmailAddressHost, EmailSettingDAO}
import app_services.blacklist_monitoring.models.{BlacklistCheckResult, BlacklistCheckType}
import app_services.blacklist_monitoring.services.MxToolBoxService
import play.api.libs.ws.WSClient
import utils.SRLogger
import utils.mq.services.{MQConfig, SimpleMqServiceTrait}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.Failure

case class DomainHealthBlacklistCheckMsg(
                                          domain: EmailAddressHost
                                        )

class MqDomainHealthBlacklistCheck(
                                    domainHealthCheckDAO: DomainHealthCheckDAO,
                                    domainHealthCheckService: DomainHealthCheckService
                                  ) extends SimpleMqServiceTrait[DomainHealthBlacklistCheckMsg] {

  override val queueBaseName: String = MQConfig.domainHealthCheckBaseName
  override val prefetchCount: Int = MQConfig.domainHealthCheckPrefetchCount

  override def processMessage(msg: DomainHealthBlacklistCheckMsg)(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem): Future[Any] = {

    val domainToCheck: String = msg.domain.emailAddressHost

    implicit lazy val logger: SRLogger = new SRLogger(logRequestId = s"MqDomainHealthBlacklistCheck :: processMessage :: $msg :: ")

    val res = for {

      domainBlacklistResult: BlacklistCheckResult <- MxToolBoxService.checkBlacklistStatus(
        domainOrIP = BlacklistCheckType.Domain(domain = msg.domain.emailAddressHost),
        logger = logger

      )

      isCriticalBlacklisted = {
        val failureDescription = domainBlacklistResult.failureDescription
        if (failureDescription.isDefined) {
          val wordsToCheck = AppConfig.criticalSpamBlacklist
          wordsToCheck.exists(word => failureDescription.exists(_.contains(word)))
        } else {
          false
        }
      }

      _ <- if (isCriticalBlacklisted) {
        Future.fromTry(domainHealthCheckService.sendBlacklistNotification(
          domain = domainToCheck,
          blacklistNames = domainBlacklistResult.failureDescription.get
        )(ws = ws, ec = ec, logger = logger))

      } else {
        Future.successful(())
      }


      updatedDomainBlacklistResult <- Future.fromTry(domainHealthCheckDAO.updateBlacklistResult(
        domainBlacklistCheckResult = domainBlacklistResult,
        domain = domainToCheck,
        isFailedForCriticalBlacklist = isCriticalBlacklisted
      ))


    } yield {
      updatedDomainBlacklistResult
    }

    res.map { _ =>
        logger.success(s"Blacklist Check for the domain is done successfully")
      }
      .recover(e => {
        logger.error(s"Failed to do BlackList check for the domain ${domainToCheck}", e)
      })

  }

}
