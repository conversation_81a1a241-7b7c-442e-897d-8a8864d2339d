package utils.mq.call

import org.apache.pekko.actor.ActorSystem
import api.accounts.TeamId
import api.accounts.models.AccountId
import api.notes.models.{CreateNotesForm, NoteId}
import api.prospects.models.ProspectId
import api.tasks.services.TaskUuid
import io.sr.billing_common.models.PlanType
import org.joda.time.DateTime
import play.api.libs.ws.WSClient
import scalikejdbc.interpolation.SQLSyntax
import scalikejdbc.jodatime.JodaWrappedResultSet._
import scalikejdbc.{DB, DBSession, WrappedResultSet, scalikejdbcSQLInterpolationImplicitDef}
import utils.{SRLogger, StringUtils}
import utils.dbutils.DBUtils
import utils.mq.services.{MQConfig, SimpleMqServiceTrait}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.Try

case class CallNoteIdMigrationData(teamId: TeamId)

case class CallParticipantToBeUpdatedData(
                                            call_participant_uuid: String,
                                            call_note: String,
                                            added_by: AccountId,
                                            prospectId: Option[ProspectId],
                                            taskUuid: Option[TaskUuid],
                                            updated_at: Option[DateTime]
                                          )

object CallParticipantToBeUpdatedData {

  def fromDb(rs: WrappedResultSet): CallParticipantToBeUpdatedData = {
    CallParticipantToBeUpdatedData(
      call_participant_uuid = rs.string("call_participant_uuid"),
      updated_at = rs.jodaDateTimeOpt("call_note_updated_at"),
      call_note = rs.string("call_note"),
      added_by = AccountId(rs.long("added_by")),
      taskUuid = rs.stringOpt("task_uuid").map(TaskUuid(_)),
      prospectId = rs.longOpt("prospect_id").map(ProspectId(_)),
    )
  }

}

class MQUpdateCallNoteId(dbUtils: DBUtils) extends SimpleMqServiceTrait[CallNoteIdMigrationData] {

  override val queueBaseName: String = MQConfig.callNotesMigrationScriptQueueBaseName
  override val prefetchCount: Int = MQConfig.callNotesMigrationScriptPrefetchCount

  override def processMessage(msg: CallNoteIdMigrationData)(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem): Future[Unit] = {

    val logTraceId = s"${StringUtils.genLogTraceId}_th_${Thread.currentThread().getName}"
    given Logger: SRLogger = new SRLogger(
      logRequestId = s"$logTraceId call_note migration for table::call_participants_logs",
      customLogTraceId = Some(logTraceId)
    )

    Logger.info(msg = s"[MQUpdateCallNoteId] started for team_id :: ${msg.teamId.id}")

    migrationScript(teamId = msg.teamId)

  }


  def migrationScript(
                       teamId: TeamId
                     )(implicit ec: ExecutionContext, Logger: SRLogger): Future[Unit] = {

    val dbAndSession = dbUtils.startLocalTx()

    implicit val session: DBSession = dbAndSession.session
    val db = dbAndSession.db

    // There are less than 1000 records in the DB so its safe to have this func non tailrec.
    def runMigration(
                      total_count: Int = 0,
                      startTime: DateTime = DateTime.now()
                    ): Future[Unit] = {

      for {

        callParticipantsLogsToBeUpdated: Seq[CallParticipantToBeUpdatedData] <- getCallParticipantsLogsToBeUpdated(teamId = teamId)

        savedCallNotesSeq: Seq[Boolean] <- Future.sequence(callParticipantsLogsToBeUpdated.map(callParticipantData => {
          saveCallNote(
            callParticipantToBeUpdatedData = callParticipantData,
            teamId = teamId
          )
        }))

      } yield {

        val rowsUpdated = savedCallNotesSeq.length

        if(rowsUpdated > 0) {
          val updated_count = total_count + rowsUpdated
          runMigration(
            total_count = updated_count,
            startTime = startTime
          )
        } else {
          val millisTaken = DateTime.now().getMillis - startTime.getMillis
          Logger.info(msg = s"[MQUpdateCallNoteId] successful for team_id::$teamId total_rows_updated::$total_count total_millis::$millisTaken")
        }

      }

    }

    Logger.info(msg = s"[MQUpdateCallNoteId] started for teamId::$teamId")

    val res: Future[Unit] = runMigration()
      .map { _ =>

        dbUtils.commitAndCloseSession(db = db)

      }
      .recover { case err =>

        Logger.error(msg = s"[MQUpdateCallNoteId] failed for team_id::$teamId", err = err)

        dbUtils.commitAndCloseSession(db = db)

      }


    res

  }


  def saveCallNote(
                    callParticipantToBeUpdatedData: CallParticipantToBeUpdatedData,
                    teamId: TeamId
                  )(implicit session: DBSession, ec: ExecutionContext): Future[Boolean] = {

    val createNotesForm = CreateNotesForm(
      note = callParticipantToBeUpdatedData.call_note,
      added_by = callParticipantToBeUpdatedData.added_by,
      team_id = teamId,
      prospect_id = callParticipantToBeUpdatedData.prospectId,
      task_uuid = callParticipantToBeUpdatedData.taskUuid
    )

    for {

      createdNoteId: NoteId <- createNote(
        createNotesForm = createNotesForm,
        updated_at = callParticipantToBeUpdatedData.updated_at
      )

      updatedCallParticipantsLogsNoteId: NoteId <- updateCallParticipantsLogsWithNoteId(
        callParticipantUuid = callParticipantToBeUpdatedData.call_participant_uuid,
        noteId = createdNoteId,
        teamId = teamId
      )

    } yield {

      createdNoteId == updatedCallParticipantsLogsNoteId

    }

  }

  def getCallParticipantsLogsToBeUpdated(
                                          teamId: TeamId
                                        )(implicit session: DBSession, ec: ExecutionContext): Future[Seq[CallParticipantToBeUpdatedData]] = Future {

    sql"""
         SELECT
          cpl.call_participant_uuid,
          cpl.call_note,
          t.assignee_id as added_by,
          ccl.primary_prospect_id as prospect_id,
          ccl.task_uuid as task_uuid,
          cpl.call_note_updated_at

         FROM call_participants_logs cpl
          INNER JOIN call_conference_logs ccl ON ( ccl.conference_uuid = cpl.call_conference_uuid AND ccl.team_id = cpl.team_id )
          INNER JOIN tasks t ON ( t.task_id = ccl.task_uuid AND t.team_id = ccl.team_id )

         WHERE
          cpl.call_note IS NOT NULL
          AND cpl.note_id IS NULL
          AND t.assignee_id IS NOT NULL
          AND cpl.team_id = ${teamId.id}

        LIMIT 1000;
       """
      .map(CallParticipantToBeUpdatedData.fromDb)
      .list
      .apply()

  }

  def createNote(
                  createNotesForm: CreateNotesForm,
                  updated_at: Option[DateTime]
                )(implicit session: DBSession, ec: ExecutionContext): Future[NoteId] = Future {

    sql"""
     INSERT INTO notes(
       note,
       added_by,
       team_id,
       prospect_id,
       task_uuid,
       created_at
     )
     VALUES(
      ${createNotesForm.note.trim},
      ${createNotesForm.added_by.id},
      ${createNotesForm.team_id.id},
      ${createNotesForm.prospect_id.map(_.id)},
      ${createNotesForm.task_uuid.map(_.uuid)},
      $updated_at
     )
     RETURNING id;
   """
      .map(rs => NoteId(rs.long("id")))
      .single
      .apply()
      .get

  }

  def updateCallParticipantsLogsWithNoteId(
                                            callParticipantUuid: String,
                                            noteId: NoteId,
                                            teamId: TeamId
                                          )(implicit session: DBSession, ec: ExecutionContext): Future[NoteId] = Future {

    sql"""
         UPDATE call_participants_logs
          SET note_id = ${noteId.id}
         WHERE
          call_participant_uuid = $callParticipantUuid
          AND team_id = ${teamId.id}
          RETURNING note_id;
       """
      .map(rs => NoteId(rs.long("note_id")))
      .single
      .apply()
      .get

  }

  def getTeamIdsInTable(active_team: Boolean): Try[List[TeamId]] = Try {
    DB readOnly { implicit session =>

      val whereClause = MQUpdateCallNoteId.whereClauseToPushTeamToQueue(active_team = active_team)

      sql"""
           SELECT t.id AS team_id from teams t
           INNER JOIN organizations o ON t.org_id = o.id
           WHERE
           $whereClause

           ORDER BY
           CASE o.plan_type
            WHEN ${PlanType.PAID.toString} then 1
            WHEN ${PlanType.TRIAL.toString} then 2
            WHEN ${PlanType.INACTIVE.toString} then 3
           end ;
           """
        .map(rs => TeamId(rs.long("team_id")))
        .list
        .apply()
    }
  }

}

object MQUpdateCallNoteId {

  def whereClauseToPushTeamToQueue(active_team: Boolean): SQLSyntax = {
    if (active_team)
      sqls"""
          NOT o.plan_type = ${PlanType.INACTIVE.toString}
           """
    else
      sqls"""
            o.plan_type = ${PlanType.INACTIVE.toString}
          """
  }

}