package utils.mq.trackingapp

import org.apache.pekko.actor.ActorSystem
import api.accounts.EmailScheduledIdOrTaskId.EmailScheduledId
import api.accounts.TeamId
import api.campaigns.services.CampaignId
import api.emails.EmailsScheduledUuid
import api.emails.models.EmailTrackingApiRequestData
import api.emails.services.EmailScheduledService
import api.prospects.models.ProspectId
import api.sr_audit_logs.models.{EventType, ProspectIdWithOldProspectDeduplicationColumn}
import play.api.libs.ws.WSClient
import utils.helpers.LogHelpers
import utils.mq.services.{MQConfig, MQService}
import utils.mq.webhook.mq_activity_trigger.{MQActivityTriggerMsgForm, MQActivityTriggerPublisher}
import utils.{SRLogger, StringUtils}
import utils.mq.do_not_contact.{MQDoNotContactConsumer, MQDoNotContactMessage, MQDoNotContactPublisher}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

case class MQUnsubscribeTrackerMessage(
  email_scheduled_id: Option[String],
  emailsScheduledUuid: Option[EmailsScheduledUuid],
  teamId: Option[TeamId],
  list_unsub: Boolean,
  trace_reqid: Option[String]
)



class MQUnsubscribeTracker(
                            emailScheduledService: EmailScheduledService,
                            mqDoNotContactPublisher: MQDoNotContactPublisher,
                            mqActivityTriggerPublisher: MQActivityTriggerPublisher
) extends MQService[MQUnsubscribeTrackerMessage] {

  val queueBaseName: String = MQConfig.unsubscribeTrackerQueueBaseName
  val prefetchCount: Int = MQConfig.unsubscribeTrackerPrefetchCount

  def publish(msg: MQUnsubscribeTrackerMessage) = {
    publishMsg(message = msg, queueBaseName = queueBaseName, prefetchCount = prefetchCount)
  }
  def startConsumer()(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem): Try[String] = {
    startMsgConsumer(queueBaseName = queueBaseName, prefetchCount = prefetchCount)
  }

  def processMessage(msg: MQUnsubscribeTrackerMessage)(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem): Future[Int] = {

    val logRequestId = StringUtils.genLogTraceId

    val Logger = new SRLogger(s"$logRequestId :: MQUnubscribeTracker.processMessage $msg ")

    val traceRequestDBId: Option[Long] = msg.trace_reqid.flatMap(reqId => {
      EmailTrackingApiRequestData.getEmailTrackingApiRequestIdFromTraceId(
        traceReqId = reqId
      ).toOption.flatten
    })

    if (msg.emailsScheduledUuid.isEmpty && msg.email_scheduled_id.isEmpty) {
      Logger.shouldNeverHappen("both uuid and id are empty")
    } else if (msg.emailsScheduledUuid.nonEmpty && msg.email_scheduled_id.nonEmpty) {
      Logger.shouldNeverHappen(s"both uuid and id are present: ${msg.emailsScheduledUuid} and ${msg.email_scheduled_id}")
    }

    if (msg.emailsScheduledUuid.isEmpty && msg.teamId.nonEmpty) {
      Logger.shouldNeverHappen("uuid is empty but teamId is present")
    } else if (msg.emailsScheduledUuid.nonEmpty && msg.teamId.isEmpty) {
      Logger.shouldNeverHappen("uuid is present but teamId is empty")
    }

    emailScheduledService.hasOptedOutV2(
      Logger = Logger,
//      stepId = msg.step_id.toLong,
      emailScheduledId = msg.email_scheduled_id.map(id => EmailScheduledId(id.toLong)),
      emailsScheduledUuidAndTeamId = msg.emailsScheduledUuid.map(uuid => (uuid, msg.teamId.get)), //if uuid present then team_id always present
      traceReqId = traceRequestDBId
    ) match {

      case Failure(e) =>
        Logger.error(s"[FATAL] MQUnubscribeTracker.processMessage hasOptedOutV2: msg ($msg): ${LogHelpers.getStackTraceAsString(e)}")

        Future.failed(e)

      case Success((count, prospectDetails)) =>


        if (prospectDetails.isEmpty) {

          Future.successful(0)

        } else {

          val d = prospectDetails.get

          val addToBlacklistTry = if (d.add_prospect_to_dnc_on_opt_out) {

            mqDoNotContactPublisher.publish(msg = MQDoNotContactMessage(
              prospect_emails = Seq(d.prospect_email),
              prospect_domains_with_excluded_emails = Seq(),
              is_req_via_dnc_form = false,
              account_id = d.account_id,
              account_name = d.account_name,
              team_id = d.team_id,
              ta_id = d.ta_id,
              campaign_id = d.campaign_id,
              campaign_name = d.campaign_name
            ))

          } else Try {}

          addToBlacklistTry match {

            case Failure(e) =>
              Logger.error(s"[FATAL] MQUnubscribeTracker.processMessage MQDoNotContact.publish $d :: msg: $msg :: ${LogHelpers.getStackTraceAsString(e)}")

              Future.successful(0)

            case Success(_) =>
              Logger.info(s"MQUnubscribeTracker.processMessage  msg: $msg :: success MQDoNotContact.publish $d")

              val message = MQActivityTriggerMsgForm(
                accountId = d.account_id,
                teamId = d.team_id,
                campaignId = d.campaign_id,
                prospectIds = Seq(ProspectIdWithOldProspectDeduplicationColumn(
                  prospectId = ProspectId(d.prospect_id),
                  oldProspectDeduplicationColumn = None //NONE BECAUSE IT IS NOT UPDATE PROSPECT EVENT

                )),
                clickedUrl = None,
                campaignName = d.campaign_name,
                emailScheduledIds = None,
                event = EventType.PROSPECT_OPTED_OUT.toString,
                threadId = None,
                replySentimentUuid = None
              )
              mqActivityTriggerPublisher.publishEvents(message = message)

              Future.successful(0)

          }

        }


    }

  }
}
