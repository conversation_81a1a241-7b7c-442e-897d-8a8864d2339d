package utils.mq.webhook

import api.accounts.TeamId
import api.accounts.models.AccountId
import api.sr_audit_logs.models.{CreateEventLogError, EventDataType, EventType}
import api.sr_audit_logs.services.EventLogService
import api.triggers.IntegrationType
import play.api.Logging
import utils.SRLogger
import utils.cronjobs.AttemptRetryCronService
import utils.uuid.SrUuidUtils

import scala.util.Failure

class SyncTriggerMQService(
  eventLogService: EventLogService
) extends Logging {

  def publishSyncTrigger(message: EventDataType.SyncEventDataType, integrationType: IntegrationType, eventType: EventType, teamId: TeamId, accountId: AccountId)(implicit  Logger: SRLogger) = {

        given logger: SRLogger= Logger.appendLogRequestId("SyncTriggerMQService.publishSyncTrigger")

          val eventLogRes: Either[CreateEventLogError, String] = {
            eventLogService.createEventLog(
              event_data_type = message,
              teamId = teamId.id,
              accountId = accountId.id
            )
          }

          eventLogRes match {

            case Left(err) =>
              err match {
                case CreateEventLogError.SQLException(error) =>
                  logger.fatal(s"CRITICAL: createEventLog for event_log_id_for_created_ids failed:  tid_${teamId.id} ", error)

                case CreateEventLogError.CreateEventLogFailedInsert(message) =>
                  logger.fatal(s"CRITICAL: createEventLog for event_log_id_for_created_ids failed: message $message  tid_${teamId.id}")

              }

            case Right(eventLogids) =>{
              if(teamId.id == 16247){
                logger.debug(s"publishSyncTrigger  created eventLog with eventLogid ${eventLogids} msg ${message} tid_${teamId.id}")
              }
            }
              //DO NOTHING
          }

  }

}
