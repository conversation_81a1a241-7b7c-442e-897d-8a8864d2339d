package utils.mq.webhook

import api.AppConfig
import org.apache.pekko.actor.ActorSystem
import api.accounts.{AccountService, TeamId}
import api.campaigns.models.IgnoreProspectsInOtherCampaigns
import api.campaigns.services.CampaignService
import api.columns.ProspectColumnDef
import api.integrations.services.TIntegrationCRMService
import api.prospects.models.{SrProspectColumns, UpdateProspectType}
import api.prospects.{CreateOrUpdateProspectsResult, ProspectService}
import api.sr_audit_logs.models.{EventType, WorkflowAttemptInternalServerError, WorkflowAttemptTryErrorReason}
import api.triggers.{HandleCRMContactDataArguments, IntegrationModuleType, IntegrationType, SRTriggerActionType, SRTriggerAllowedCombos, Trigger, TriggerAction, TriggerInDB, TriggerUtils}
import org.joda.time.DateTime
import play.api.libs.ws.WSClient
import utils.email_notification.service.EmailNotificationService
import utils.mq.webhook.model.TriggerSource
import utils.{Help<PERSON>, SRLogger, StringUtils}
import utils.mq.do_not_contact.{MQDoNotContactConsumer, MQDoNotContactMessage, MQDoNotContactPublisher}
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}


class HandleSyncTriggerEventService(
                                     protected val triggerDAO: Trigger,
                                     protected val emailNotificationService: EmailNotificationService,
                                     campaignService: CampaignService,
                                     prospectService: ProspectService,
                                     srTriggerAllowedCombos: SRTriggerAllowedCombos,
                                     protected val accountService: AccountService,
                                     mqDoNotContactPublisher: MQDoNotContactPublisher,
                                     prospectColumnDef: ProspectColumnDef,
                                     tIntegrationCRMService: TIntegrationCRMService,
                                     srRollingUpdateCoreService: SrRollingUpdateCoreService
) extends HandleTriggerEventTrait[Long] {

  protected override val logName: String = "HandleSyncTriggerEventService"


  def processEventMessageForNewAuditFlow(
                                          triggerId: Long
                                        )(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem,Loggger:SRLogger): Future[Either[WorkflowAttemptTryErrorReason, Seq[Int]]] = {

    val logRequestId = s"$logName processEventMessageForNewAuditFlow ${StringUtils.genLogTraceId}:: wf_${triggerId}"

    implicit val srLogger: SRLogger = Loggger.appendLogRequestId(logRequestId)

    srLogger.info("START")

    triggerDAO.findById(
      id = triggerId
    ) match {

      case Failure(e) =>
        srLogger.fatal(s"triggerDAO.findById error", err = e)

        Future.successful(Left(WorkflowAttemptTryErrorReason.SRInternalServerError(WorkflowAttemptInternalServerError.SqlException(e))))

      case Success(None) =>

        srLogger.fatal(s"triggerDAO.findById error not trigger found with")
        Future(Left(WorkflowAttemptTryErrorReason.SRInternalServerError(WorkflowAttemptInternalServerError.TriggerNotFound(s"triggerId : ${triggerId}"))))

      case Success(Some(trigger)) =>

        if(AppConfig.Debug_Logs_For_Teams.allowLogging(teamId =  16247)){
          srLogger.debug(s"found triggers are ${trigger}")
        }

        val teamId = trigger.team_id

        val accountId = trigger.owner_id

        _handleFoundTriggers(
          foundTriggers = Seq(trigger),
          teamId = teamId,
          accountId = accountId,
          msg = triggerId,
          eventTypeTrigger = trigger.event.get
        )(ws, ec, system, srLogger)

    }

  }

  // PROSPECTS_EMAILS_TODO_INSERT / PROSPECTS_EMAILS_TODO_UPDATE
  // sync contact/lead data from CRM to SmartReach
  // Use case : CREATE_OR_UPDATE_PROSPECT_IN_SMARTREACH
  //removed private to write test cases
  def _syncContactDataFromCRM(
                               integrationType: IntegrationType,
                                      syncModule: IntegrationModuleType,
                                      teamId: Long,
                                      accountId: Long,
                                      trigger: TriggerInDB,
                                      action: TriggerAction
                                     )(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem,
                                       Logger: SRLogger
                                     ): Future[Either[WorkflowAttemptTryErrorReason, Int]] = {

    Logger.info(s"starting _syncContactDataFromCRM for wf_${trigger.id} :: trigger: $trigger :: action: $action")

    tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken(
      teamId = teamId,
      integration_type = integrationType
    )
      .flatMap {

        case Left(err) =>
          Logger.fatal(s"_syncContactDataFromCRM fetchTokensFromDBAndRefreshAccessToken error ${err.message}")

            Future.successful(Left(TIntegrationCRMService.getWorkflowAttemptTryErrorReasonFromFetchTokensFromDBAndRefreshAccessTokenError(err)))

        case Right(accessTokenResponse) =>

        accountService.find(id = accountId) match {

          case Failure(e) =>

            Logger.fatal(s"accountService.find :: Invalid account Id: $accountId", err = e)


              Future.successful(Left(WorkflowAttemptTryErrorReason.SRInternalServerError(WorkflowAttemptInternalServerError.SqlException(e))))


          case Success(account) =>

            // case Some(account) =>

            //NOTE: this campaign_id_in_action used to assign prospects which is synced from the CRM to that campaign, So before that here checking campaign is found or deleted
            val campaign_id_in_action = if (
              action.campaign_id.isDefined
                && action.campaign_id.get == -1 // FIXME: this case should not even be there, this must be an option None type instead of -1 case
            ) {
              None
            } else {
              action.campaign_id
            }
            //TODO: using toOption here because the campaignService.findBasicDetails call returns a Try[Option]
            // need to handle this in a better way
            if (campaign_id_in_action.isDefined && campaignService
              .findBasicDetails(id = campaign_id_in_action.get,teamId = TeamId(teamId)).get.isEmpty) {

              Logger.fatal(s"Campaign.findBasicDetails ::Selected SmartReach campaign was not found. Please check if you have deleted that campaign, and restart the workflow. campaign_id: ${campaign_id_in_action.get}")

              Future.failed(new Exception("Selected SmartReach campaign was not found. Please check if you have deleted that campaign, and restart the workflow."))

            } else {

              val srCustomFields: Seq[String] = prospectColumnDef.findCustomColumns(teamId = teamId).map(_.name)


              val syncResponseF = syncModule match {

                case IntegrationModuleType.CONTACTS |
                     IntegrationModuleType.LEADS |
                     IntegrationModuleType.CANDIDATES =>


                  def __createProspectsFromRecentContacts(data: HandleCRMContactDataArguments): Try[Boolean] = {

                    val forceUpdateDuplicateProspects = true
                    val ignoreProspectsInOtherCampaigns = action.ignore_prospects_in_other_campaigns match {
                      case None =>
                        if (action.ignore_prospects_active_in_other_campaigns.getOrElse("no") == "yes") {
                          IgnoreProspectsInOtherCampaigns.IgnoreProspectsActiveInOtherCampaigns
                        } else {
                          IgnoreProspectsInOtherCampaigns.DoNotIgnore
                        }
                      case Some(value) =>
                        IgnoreProspectsInOtherCampaigns.fromKey(value).getOrElse(IgnoreProspectsInOtherCampaigns.DoNotIgnore)
                    }

                    val org_id = account.org.id


                    val updateProspectType: UpdateProspectType = if (forceUpdateDuplicateProspects) {
                      UpdateProspectType.ForceUpdate
                    } else {
                      UpdateProspectType.None
                    }

                    val tryResult: Try[CreateOrUpdateProspectsResult] =

                      /**
                       * In this flow forceUpdateDuplicateProspects is always true
                       * so we will update the data other than 4 deduplication columns 
                       */
                      prospectService.createOrUpdateProspects(
                        ownerAccountId = accountId,
                        teamId = teamId,
                        listName = None,
                        prospects = data.prospectsToBeCreated,
                        updateProspectType = updateProspectType,
                        ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads = true,
                        doerAccount = account,
                        prospectSource = Some(TIntegrationCRMService.getProspectSourceFromIntegrationType(integrationType = integrationType)),
                        prospectAccountId = None,
                        prospect_tags = None,
                        campaign_id = campaign_id_in_action,
                        ignoreProspectInOtherCampaign = ignoreProspectsInOtherCampaigns,
                        deduplicationColumns = Some(Seq(    //27-Jan-25: some prospects in CRM were without email in customer's account so we will find duplicates based on all deduplication columns
                          SrProspectColumns.Email,
                          SrProspectColumns.Phone,
                          SrProspectColumns.LinkedinUrl,
                          SrProspectColumns.CompanyFirstnameLastname
                        )),

                        /**
                         * FIXME AUDITLOGFIXNEEDED auditRequestLogId None Bcz for now we are not covering this path
                         * this will be updated with request.auditRequestLogId replacing with None when this path covered
                         * */
                        auditRequestLogId = None,
                        SRLogger = Logger.appendLogRequestId(
                          appendLogReqId = s"$logName._syncProspects crm: $integrationType :: trigger_id_${trigger.id} "
                        ),
                        triggerPath = TriggerSource.CRM
                      )


                    if (tryResult.isSuccess) {
                      val result = tryResult.get
                      Logger.info(s"ProspectService.createOrUpdateProspects result: campaign_id: ${action.campaign_id} : action_type: ${action.action_type} action_app_type: ${action.action_app_type} ::  result.created_ids length: ${result.created_ids.length} :: result.updated_ids length: ${result.updated_ids.length} :: result.assigned_ids: ${result.assigned_ids} :: result.duplicate_ids length: ${result.duplicate_prospects_data.length} :: result.duplicate_ids_with_edit_permission length: ${result.duplicate_ids_with_edit_permission.length} :: result.duplicates_ignored_for_no_edit_permission length:  ${result.duplicates_ignored_for_no_edit_permission.length} :: result.invalid_emails length: ${result.invalid_emails.length} ::result.ignored_internal_emails length: ${result.ignored_internal_emails.length}")
                    }

                    tryResult
                      .map(_ => true)
                  }

                  tIntegrationCRMService.getRecentContacts(
                    handleProspectData = __createProspectsFromRecentContacts,
                    crm_type = integrationType,
                    module_type = syncModule,
                    accessTokenData = accessTokenResponse,
                    team_id = trigger.team_id,
                    owner_id = trigger.owner_id,
                    last_sync_at = action.last_sync_at,
                    updatedLastSyncedAtTimeAfterFirstFetch = None,
                    srCustomFieldNames = srCustomFields,

                    page = 0,

                    tp_filter_id = trigger.tp_filter_id,
                    srRollingUpdateCoreService = srRollingUpdateCoreService
                  )


              }

              syncResponseF.flatMap { syncResponse =>

                    triggerDAO.updateInQueueForSync(id = trigger.id) match {

                      case Failure(e) =>

                        Logger.fatal(s"_syncContactDataFromCRM :: updateInQueueForSync error data: $trigger :: ${e.getMessage}")

                          Future.successful(Left(WorkflowAttemptTryErrorReason.SRInternalServerError(WorkflowAttemptInternalServerError.SqlException(e))))

                      case Success(None) =>

                        Logger.fatal(s"_syncContactDataFromCRM :: updateInQueueForSync None error data: $trigger")


                          Future.successful(Left(WorkflowAttemptTryErrorReason.SRInternalServerError(WorkflowAttemptInternalServerError.UpdateSentNone(s"_syncContactDataFromCRM :: updateInQueueForSync None error data: $trigger"))))

                      case Success(Some(_)) =>

                        triggerDAO.updateLastSyncAt(
                          id = trigger.id,
                          action = action,
                          last_sync_at = syncResponse,
                          logger = Logger
                        ) match {

                          case Failure(e) =>

                            Logger.fatal(s"_syncContactDataFromCRM :: updateLastSyncAt error data: $trigger :: ${e.getMessage}")
                              Future.successful(Left(WorkflowAttemptTryErrorReason.SRInternalServerError(WorkflowAttemptInternalServerError.SqlException(e))))

                          case Success(None) =>

                            Logger.fatal(s"_syncContactDataFromCRM :: updateLastSyncAt None error data: $trigger")

                              Future.successful(Left(WorkflowAttemptTryErrorReason.SRInternalServerError(WorkflowAttemptInternalServerError.UpdateSentNone(s"_syncContactDataFromCRM :: updateLastSyncAt None error data: $trigger"))))

                          case Success(Some(_)) =>

                            Logger.info(s"Prospect assigned successfully")

                            Future.successful(Right(1))

                        }
                    }

              }
            }
        }
      }
      .recover { case e =>

        val msg = s"SmartReach is not able to connect to your $integrationType account. Please connect / integrate the $integrationType account under Settings -> Team Settings -> Integrations"

        Logger.fatal(msg, err = e)

        throw new Exception(msg)

      }

  }


  private def _addToDoNotContactList(
                                      integrationType: IntegrationType,
                                     syncModule: IntegrationModuleType,
                                     teamId: Long,
                                     accountId: Long,
                                     trigger: TriggerInDB,
                                     action: TriggerAction
                                    )(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem,
                                      Logger: SRLogger
                                    ): Future[Either[WorkflowAttemptTryErrorReason, Int]] = {

    tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken(
      teamId = teamId,
      integration_type = integrationType
    )
      .flatMap {

        case Left(err) =>
          Logger.fatal(s"_addToDoNotContactList fetchTokensFromDBAndRefreshAccessToken error ${err.message}")
            Future.successful(Left(TIntegrationCRMService.getWorkflowAttemptTryErrorReasonFromFetchTokensFromDBAndRefreshAccessTokenError(err)))

        case Right(accessTokenResponse) =>

        accountService.find(id = accountId) match {

          case Failure(e) =>

            Logger.fatal(s"$logName.accountService.find :: Invalid account Id: $accountId", err = e)

              Future.successful(Left(WorkflowAttemptTryErrorReason.SRInternalServerError(WorkflowAttemptInternalServerError.SqlException(e))))

          case Success(account) =>

            val ta = account.teams.find(_.team_id == teamId).get.access_members.find(_.user_id == accountId).get

            val accountName = Helpers.getAccountName(a = account)

            val srCustomFields: Seq[String] = prospectColumnDef.findCustomColumns(teamId = teamId).map(_.name)


            val syncResponseF = syncModule match {

              case IntegrationModuleType.CONTACTS |
                   IntegrationModuleType.LEADS |
                   IntegrationModuleType.CANDIDATES =>


                def __pusblishAddToDoNotContactList(data: HandleCRMContactDataArguments) = {

                  val tryResult = mqDoNotContactPublisher.publishMultiple(msg = MQDoNotContactMessage(
                    prospect_emails = data.prospectsToBeCreated.filter(_.email.isDefined).map(p => p.email.get),
                    prospect_domains_with_excluded_emails = Seq(),
                    is_req_via_dnc_form = false,
                    account_id = ta.user_id,
                    account_name = accountName,
                    team_id = ta.team_id,
                    ta_id = ta.ta_id,
                    campaign_id = None,
                    campaign_name = None

                  ))

                  if (tryResult.isSuccess) {
                    Logger.info(s"$logName._addToDoNotContactList MQDoNotContactMessage published successfully: action: $action")
                  }

                  tryResult
                    .map(_ => true)
                }

                tIntegrationCRMService.getRecentContacts(
                  handleProspectData = __pusblishAddToDoNotContactList,
                  crm_type = integrationType,
                  module_type = syncModule,
                  accessTokenData = accessTokenResponse,
                  team_id = trigger.team_id,
                  owner_id = trigger.owner_id,
                  last_sync_at = action.last_sync_at,
                  updatedLastSyncedAtTimeAfterFirstFetch = None,
                  srCustomFieldNames = srCustomFields,

                  page = 0,

                  tp_filter_id = trigger.tp_filter_id,
                  srRollingUpdateCoreService = srRollingUpdateCoreService
                )


            }

            syncResponseF.flatMap { syncResponse =>

              triggerDAO.updateInQueueForSync(id = trigger.id) match {

                case Failure(e) =>

                  Logger.fatal(s"$logName._addToDoNotContactList :: updateInQueueForSync error data: $trigger :: ${e.getMessage}")

                    Future.successful(Left(WorkflowAttemptTryErrorReason.SRInternalServerError(WorkflowAttemptInternalServerError.SqlException(e))))



                case Success(None) =>

                  Logger.fatal(s"$logName._addToDoNotContactList :: updateInQueueForSync None error data: $trigger")
                    Future.successful(Left(WorkflowAttemptTryErrorReason.SRInternalServerError(WorkflowAttemptInternalServerError.UpdateSentNone(s"_syncContactDataFromCRM :: updateInQueueForSync None error data: $trigger"))))

                case Success(Some(_)) =>

                  triggerDAO.updateLastSyncAt(
                    id = trigger.id,
                    action = action,
                    last_sync_at = syncResponse,
                    logger = Logger
                  ) match {

                    case Failure(e) =>

                      Logger.fatal(s"$logName._addToDoNotContactList :: updateLastSyncAt error data: $trigger :: ${e.getMessage}")
                        Future.successful(Left(WorkflowAttemptTryErrorReason.SRInternalServerError(WorkflowAttemptInternalServerError.SqlException(e))))

                    case Success(None) =>

                      Logger.fatal(s"$logName._addToDoNotContactList :: updateLastSyncAt None error data: $trigger")

                        Future.successful(Left(WorkflowAttemptTryErrorReason.SRInternalServerError(WorkflowAttemptInternalServerError.UpdateSentNone(s"_syncContactDataFromCRM :: updateInQueueForSync None error data: $trigger"))))

                    case Success(Some(_)) =>

                      Logger.info(s"$logName._addToDoNotContactList Trigger.updateLastSyncAt success")

                      Future.successful(Right(1))

                  }
              }


            }
        }
      }
      .recover { case e =>

        val msg = s"SmartReach is not able to connect to your $integrationType account. Please connect / integrate the $integrationType account under Settings -> Team Settings -> Integrations"

        Logger.fatal(msg, err = e)

        throw new Exception(msg)

      }


  }

  // PROSPECTS_EMAILS_TODO_INSERT / PROSPECTS_EMAILS_TODO_UPDATE
  /* Use case 1 : ADD_TO_DO_NOT_CONTACT_LIST
   * Use case 2 : CREATE_OR_UPDATE_PROSPECT_IN_SMARTREACH
   */

  //removing protected to add test cases
  def _handleEventAction(

    msg: Long,
    teamId: Long,
    accountId: Long,
    t: TriggerInDB,
    ac: TriggerAction,
    parentEventTypeTrigger: EventType,

  )(
    implicit ws: WSClient,
    ec: ExecutionContext,
    system: ActorSystem,
    Logger: SRLogger
  ): Future[Either[WorkflowAttemptTryErrorReason, Int]] = {

    (t.event.get, ac.action_type) match {


      case (
        EventType.HUBSPOT_PROSPECT_SYNC |
        EventType.ZOHO_PROSPECT_SYNC |
        EventType.ZOHO_RECRUIT_PROSPECT_SYNC |
        EventType.PIPEDRIVE_PROSPECT_SYNC |
        EventType.SALESFORCE_PROSPECT_SYNC |

        EventType.ZOHO_LEAD_SYNC |
        EventType.SALESFORCE_LEAD_SYNC |

        EventType.ZOHO_RECRUIT_CANDIDATE_SYNC,

        SRTriggerActionType.CREATE_OR_UPDATE_PROSPECT_IN_SMARTREACH
        ) =>

        val eventType = t.event.get

        // TODO: REMOVE THIS LOG, ONLY FOR CHECKING SOME TEMPORARY ISSUE
        val lastRanAt = t.last_ran_at
        if (lastRanAt.isDefined && lastRanAt.get.isAfter(DateTime.now.minusMinutes(15))) {
          Logger.info(s"SYNCSHOULDBEINGORED : action: $ac : trigger: $t")
        }

        Future.fromTry(srTriggerAllowedCombos.getIntegrationServiceByEvent(eventType = eventType))
          .flatMap(integrationType => {

            val contactModuleType = TriggerUtils.getIntegrationModuleTypeByEvent(
              eventType = eventType,
              Logger = Logger
            ).get

            _syncContactDataFromCRM(
              integrationType = integrationType,
              syncModule = contactModuleType,
              teamId = teamId,
              accountId = accountId,
              trigger = t,
              action = ac

            )

          })


      case (

        EventType.HUBSPOT_PROSPECT_SYNC |
        EventType.ZOHO_PROSPECT_SYNC |
        EventType.ZOHO_RECRUIT_PROSPECT_SYNC |
        EventType.PIPEDRIVE_PROSPECT_SYNC |
        EventType.SALESFORCE_PROSPECT_SYNC |

        EventType.ZOHO_LEAD_SYNC |
        EventType.SALESFORCE_LEAD_SYNC |

        EventType.ZOHO_RECRUIT_CANDIDATE_SYNC,

        SRTriggerActionType.ADD_TO_DO_NOT_CONTACT_LIST
        ) =>

        val eventType = t.event.get

        Future.fromTry(srTriggerAllowedCombos.getIntegrationServiceByEvent(eventType = eventType))
          .flatMap(integrationType => {

            val contactModuleType = TriggerUtils.getIntegrationModuleTypeByEvent(
              eventType = eventType,
              Logger = Logger
            ).get

            _addToDoNotContactList(
              integrationType = integrationType,
              syncModule = contactModuleType,
              teamId = teamId,
              accountId = accountId,
              trigger = t,
              action = ac
            )

          })


      case _ =>

        Logger.fatal(s"FATAL $logName: msg: $msg :: UNSUPPORTED TRIGGER EVENT :: $t")

        Future(Right(0))
    }
  }



}
