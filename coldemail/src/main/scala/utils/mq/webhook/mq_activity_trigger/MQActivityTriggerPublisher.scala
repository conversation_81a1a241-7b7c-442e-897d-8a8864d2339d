package utils.mq.webhook.mq_activity_trigger

import api.emails.EmailScheduledDAO
import api.prospects.dao.ProspectDAO
import api.sr_audit_logs.models.{OldProspectDeduplicationColumn, ProspectIdWithOldProspectDeduplicationColumn}
import api.triggers.TriggerUtils
import utils.{SRLogger, StringUtils}
import utils.mq.services.{MQConfig, SimpleMqPublisherServiceTrait}
import api.prospects.models.ProspectId

import scala.util.{Failure, Success, Try}

class MQActivityTriggerPublisher(
                                  emailScheduledDAO: EmailScheduledDAO,
                                  prospectDAO: ProspectDAO
                                ) extends SimpleMqPublisherServiceTrait[MqActivityTriggerMsgFormBase] {
  val queueBaseName: String = MQConfig.workflowActivityToActivity
  val prefetchCount: Int = MQConfig.workflowActivityToActivityPrefetchCount


  def publishMessage(msg: MqActivityTriggerMsgFormBase): Try[Unit] = {
    publishMsg(message = msg, queueBaseName = queueBaseName, prefetchCount = prefetchCount)
  }

  def publishEvents(message: MQActivityTriggerMsgForm) = {

    // TODO: this (logRequestId) should be passed from the controller etc.
    val _logRequestId = s"${StringUtils.genLogTraceId} :: MQTrigger.publishEvents : aid_${message.accountId} tid_${message.teamId} ev_${message.event} ::  "
    val Logger = new SRLogger(logRequestId = _logRequestId)

    Logger.info(s"INIT $message")

    if (message.emailScheduledIds.isDefined &&
      message.emailScheduledIds.get.nonEmpty &&
      message.prospectIds.nonEmpty &&
      message.prospectIds.length != message.emailScheduledIds.get.length
    ) {
      Logger.fatal(s"FATAL publishEvents has both emailScheduledIds and prospectIds and they are not the same in number")

    }

    val msgs: List[MQActivityTriggerMsgForm] = if (message.emailScheduledIds.isDefined && message.emailScheduledIds.get.nonEmpty) {

      Logger.info(s"nonEmpty emailIds :msg: $message")

      emailScheduledDAO.findAllForMQTriggerPublish(
        emailScheduledIds = message.emailScheduledIds.get.distinct,
        teamId = message.teamId
      ) match {
        case Failure(e) =>

          Logger.error(s"FATAL (returning empty) :: EmailScheduled.findAllForMQTriggerPublish", err = e)

          List()

        case Success(emails) =>

          if (emails.length != message.emailScheduledIds.get.distinct.length) {
            Logger.error(s"emails.length != message.emailScheduledIds.get.distinct.length :: emails: $emails :: message.emailScheduledIds : ${message.emailScheduledIds.get.distinct.length}")
          }

          emails
            .groupBy(_.prospect_owner_id)
            .map { case (prospectOwnerId, emailsForOwner) => {

              message.copy(
                // override accountId with the prospectOwnerId
                accountId = prospectOwnerId,
                emailScheduledIds = Some(emailsForOwner.map(_.email_scheduled_id))
              )
            }
            }
            .toList
      }


    } else if (message.prospectIds.nonEmpty) {
      Logger.info(s"nonEmpty prospectIds :msg: $message")

      val prospectMap: Map[ProspectId, Option[OldProspectDeduplicationColumn]] =
        message.prospectIds.map(prospect => prospect.prospectId -> prospect.oldProspectDeduplicationColumn).toMap

      prospectDAO.findAllForMQTriggerPublish(
        prospectIds = message.prospectIds.map(_.prospectId.id).distinct,
        teamId = message.teamId
      ) match {
        case Failure(e) =>

          Logger.fatal(s"(returning empty) :: Prospect.findAllForMQTriggerPublish", err = e)

          List()

        case Success(prospects) =>

          if (prospects.length != message.prospectIds.distinct.length) {
            Logger.fatal(s"prospects.length != message.prospectIds.distinct.length :: prospects: $prospects :: message.prospectIds.distinct : ${message.prospectIds.distinct}")
          }

          prospects
            .groupBy(_.prospect_owner_id)
            .map { case (prospectOwnerId, prospectsForOwner) => {

              message.copy(
                // override accountId with the prospectOwnerId
                accountId = prospectOwnerId,
                prospectIds = prospectsForOwner.map(p => ProspectIdWithOldProspectDeduplicationColumn(prospectId = ProspectId(p.prospect_id), prospectMap.getOrElse(ProspectId(p.prospect_id), None)))
              )
            }
            }
            .toList
      }

    } else {

      Logger.info(s"empty both :msg: $message")

      List(message)
    }

    Logger.info(s"AFTER_OWNER_CHECK total msgs: ${msgs.length} :msgs: $msgs")


    msgs.foreach(msg => {
      publishInBatchOf100(message = msg, SRLogger = Logger)
    })

  }
  private def publishInBatchOf100(message: MQActivityTriggerMsgForm, SRLogger: SRLogger) = {

    val Logger = SRLogger.appendLogRequestId(s"publishInBatchOf100 prospectIds len: ${message.prospectIds.size}")

    val isEmailActivityTrigger = TriggerUtils.isEmailActivityTrigger(event = message.event)

    val res = if (message.emailScheduledIds.isDefined && message.emailScheduledIds.get.length > 100) {

      Logger.info(s">100 emailIds")
      Try {

        message.emailScheduledIds.get
          .grouped(100)
          .map(eids => {
            publishMessage(msg = message.copy(emailScheduledIds = Some(eids)))
          })
          .toSeq
          .foreach(_.get)
      }

    } else if (message.prospectIds.length > 100) {

      Logger.info(s">100 prospectIds")

      Try {

        message.prospectIds
          .grouped(100)
          .map(pids => {

            publishMessage(msg = message.copy(prospectIds = pids))

          })
          .toSeq
          .foreach(_.get)
      }
    } else {

      Logger.info(s"<100 both, straight push")
      publishMessage(msg = message)


    }


    //    val logMsg = _logMsg(fn = s"MQTrigger.publishInBatchOf100", message = message)
    //
    //    if (res.isFailure) {
    //
    //      Logger.fatal(s"publishEvents: res: $res:  message: $message")
    //
    //    } else {
    //
    //      Logger.info(s"MQTrigger.publishInBatchOf100: $message")
    //
    //    }

    res
  }


}
