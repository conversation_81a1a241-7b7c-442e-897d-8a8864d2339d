package utils.mq.webhook.mq_handle_attempt_creation

import utils.mq.services.{MQConfig, SimpleMqPublisherServiceTrait}

import scala.util.Try


class MqHandleAttemptCreationPublisher extends SimpleMqPublisherServiceTrait[MqAttemptCreationMsg] {
  override val queueBaseName: String = MQConfig.handleAttemptCreation
  override val prefetchCount: Int = MQConfig.handleAttemptCreationPrefetchCount


  def publishMessage(msg: MqAttemptCreationMsg): Try[Unit] = {
    publishMsg(message = msg, queueBaseName = queueBaseName, prefetchCount = prefetchCount)
  }

}
