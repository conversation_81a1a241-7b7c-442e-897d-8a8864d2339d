package utils.mq.webhook

import org.apache.pekko.actor.ActorSystem
import api.accounts.AccountService
import api.integrations.services.TIntegrationCRMService
import api.sr_audit_logs.models.{WorkflowAttemptInternalServerError, WorkflowAttemptTryErrorReason}
import api.triggers.{CRMIntegrationInDB, HandleCRMContactDataArguments, IntegrationModuleType, IntegrationType, IntegrationTypeService, Trigger}
import org.joda.time.DateTime
import play.api.libs.ws.WSClient
import utils.{Helpers, SRLogger}
import utils.mq.do_not_contact.{MQDoNotContactConsumer, MQDoNotContactMessage, MQDoNotContactPublisher}
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}


class HandleAddToDNCTriggerEventService(
                                         protected val triggerDAO: Trigger,
                                         protected val accountService: AccountService,
                                         mqDoNotContactPublisher: MQDoNotContactPublisher,
                                         tIntegrationCRMService: TIntegrationCRMService,
                                         srRollingUpdateCoreService: SrRollingUpdateCoreService
                                       ) {

  val logName: String = "HandleAddToDNCTriggerEventService"

  def processEventMessageNewFlow(
                                  integration: CRMIntegrationInDB
                                )(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem, srLogger: SRLogger): Future[Either[WorkflowAttemptTryErrorReason, Seq[Int]]] = {


    if (integration.crm_filters_for_add_to_do_not_contact_in_sr.isDefined && integration.crm_filters_for_add_to_do_not_contact_in_sr.get.nonEmpty) {


      val triggerFutures = integration.crm_filters_for_add_to_do_not_contact_in_sr.get
        .map(f => {

          val resFuture = _addToDoNotContactList(
            crm_type = integration.crm,
            module_type = integration.module,
            teamId = integration.team_id,
            accountId = integration.owner_id,
            module_id = integration.module_id,
            tp_filter_id = Some(f.filter_id),
            last_sync_at = f.last_sync_at
          )

          resFuture

        })

      Future.sequence(triggerFutures)
        .map(res => {

          //              srLogger.info(s"Completed workflow: res: $res")

          Helpers.seqEitherToEitherSeq(res)

        })
        .recover { case e =>
          srLogger.fatal(s"FATAL workflow", err = e)

          Left(WorkflowAttemptTryErrorReason.SRInternalServerError(WorkflowAttemptInternalServerError.FutureException(e)))

        }


    } else {

      Future(Right(Seq(0)))

    }

  }

  def _addToDoNotContactList(
                              crm_type: IntegrationType,
                              module_type: IntegrationModuleType,
                              module_id: Long,
                              teamId: Long,
                              accountId: Long,
                              tp_filter_id: Option[String],
                              last_sync_at: Option[DateTime]
                            )(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem,
                              Logger: SRLogger
                            ): Future[Either[WorkflowAttemptTryErrorReason, Int]] = {

    tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken(
        teamId = teamId,
        integration_type = crm_type
      )
      .flatMap {

        case Left(err) =>
          Logger.fatal(s"_addToDoNotContactList fetchTokensFromDBAndRefreshAccessToken error ${err.message}")
          Future.successful(Left(TIntegrationCRMService.getWorkflowAttemptTryErrorReasonFromFetchTokensFromDBAndRefreshAccessTokenError(err)))


        case Right(accessTokenResponse) =>

          accountService.find(id = accountId) match {

            case Failure(e) =>

              Logger.fatal(s"$logName.accountService.find :: Invalid account Id: ${accountId}", err = e)

              Future.successful(Left(WorkflowAttemptTryErrorReason.SRInternalServerError(WorkflowAttemptInternalServerError.SqlException(e))))

            case Success(account) =>

              val ta = account.teams.find(_.team_id == teamId).get.access_members.find(_.user_id == accountId).get

              val accountName = Helpers.getAccountName(a = account)

              val syncResponseF = module_type match {

                case IntegrationModuleType.CONTACTS |
                     IntegrationModuleType.LEADS |
                     IntegrationModuleType.CANDIDATES =>


                  def __pusblishAddToDoNotContactList(data: HandleCRMContactDataArguments) = {

                    val tryResult = mqDoNotContactPublisher.publishMultiple(msg = MQDoNotContactMessage(
                      prospect_emails = data.prospectsToBeCreated.filter(_.email.isDefined).map(p => p.email.get),
                      prospect_domains_with_excluded_emails = Seq(),
                      is_req_via_dnc_form = false,
                      account_id = ta.user_id,
                      account_name = accountName,
                      team_id = ta.team_id,
                      ta_id = ta.ta_id,
                      campaign_id = None,
                      campaign_name = None

                    ))

                    if (tryResult.isSuccess) {
                      Logger.info(s"$logName._addToDoNotContactList MQDoNotContactMessage published successfully: action:")
                    }

                    tryResult
                      .map(_ => true)
                  }

                  tIntegrationCRMService.getRecentContacts(
                    handleProspectData = __pusblishAddToDoNotContactList,
                    crm_type = crm_type,
                    module_type = module_type,
                    accessTokenData = accessTokenResponse,
                    team_id = teamId,
                    owner_id = accountId,
                    last_sync_at = last_sync_at,
                    updatedLastSyncedAtTimeAfterFirstFetch = None,
                    srCustomFieldNames = Seq(), //srCustomFieldNames should not required for add to DNC

                    page = 0,

                    tp_filter_id = tp_filter_id,
                    srRollingUpdateCoreService = srRollingUpdateCoreService
                  )


              }

              syncResponseF.flatMap { syncResponse =>

                triggerDAO.updateInQueueForAddToDNCAndLastRan(
                  team_id = teamId,
                  module_id = module_id,
                  logger = Logger
                ) match {

                  case Failure(e) =>

                    Logger.fatal(s"$logName._addToDoNotContactList :: updateInQueueForAddToDNC error module_id: $module_id :: ${e.getMessage}")
                    Future.successful(Left(WorkflowAttemptTryErrorReason.SRInternalServerError(WorkflowAttemptInternalServerError.SqlException(e))))

                  case Success(None) =>

                    Logger.fatal(s"$logName._addToDoNotContactList :: updateInQueueForAddToDNC None error module_id: $module_id")

                    Future.successful(Left(WorkflowAttemptTryErrorReason.SRInternalServerError(WorkflowAttemptInternalServerError.InvalidModuleError(s"$logName._addToDoNotContactList :: updateInQueueForAddToDNC None error module_id: $module_id"))))

                  case Success(Some(_)) =>

                    Logger.info(s"$logName._addToDoNotContactList updateInQueueForAddToDNC success")

                    triggerDAO.updateAddToDoNotContactLastSyncAt(
                      team_id = teamId,
                      module_id = module_id,
                      filter_id = tp_filter_id.get,
                      last_sync_at = syncResponse,
                      logger = Logger
                    ) match {

                      case Failure(e) =>

                        Logger.fatal(s"$logName._addToDoNotContactList :: updateAddToDoNotContactLastSyncAt error module_id: $module_id :: ${e.getMessage}")
                        Future.successful(Left(WorkflowAttemptTryErrorReason.SRInternalServerError(WorkflowAttemptInternalServerError.SqlException(e))))

                      case Success(None) =>

                        Logger.fatal(s"$logName._addToDoNotContactList :: updateAddToDoNotContactLastSyncAt None error module_id: $module_id")

                        Future.successful(Left(WorkflowAttemptTryErrorReason.SRInternalServerError(WorkflowAttemptInternalServerError.InvalidModuleError(s"$logName._addToDoNotContactList :: updateInQueueForAddToDNC None error module_id: $module_id"))))

                      case Success(Some(_)) =>

                        Logger.info(s"$logName._addToDoNotContactList updateAddToDoNotContactLastSyncAt success")

                        Future.successful(Right(1))

                    }

                }


              }
          }
      }
      .recover { case e => {

        val msg = s"SmartReach is not able to connect to your ${crm_type} account. Please connect / integrate the ${crm_type} account under Settings -> Team Settings -> Integrations"

        Logger.warn(msg, err = e)

        throw new Exception(msg)

      }

      }


  }

}
