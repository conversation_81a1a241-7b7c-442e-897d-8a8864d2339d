package utils.mq.integrity_check

import org.apache.pekko.actor.ActorSystem
import api.campaigns.services.CampaignService
import api.scheduler_report.ReportType
import play.api.libs.ws.WSClient
import utils.SRLogger
import utils.mq.services.{MQConfig, SimpleMqServiceTrait}
import scala.concurrent.Future

import scala.concurrent.ExecutionContext
import scala.util.{Failure, Success}

case class MqSchedulerIntegrityCheckData(
                                          reportType: ReportType,
                                          log_trace_id: String,
                                          reportId: Long
                                        )
class MqSchedulerIntegrityCheck(
                                 campaignService: CampaignService,
                               ) extends SimpleMqServiceTrait[MqSchedulerIntegrityCheckData]{
  val queueBaseName: String = MQConfig.schedulerIntegrityCheckQueueBaseName
  val prefetchCount: Int = MQConfig.schedulerIntegrityCheckPrefetchCount



  override def processMessage(msg: MqSchedulerIntegrityCheckData)(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem) ={

    implicit lazy val logger: SRLogger = new
        SRLogger(logRequestId = s"MqSchedulerIntegrityCheck :: processMessage :: $msg :: ", customLogTraceId = Some(msg.log_trace_id))

    Future.successful(
      campaignService.reportInconsistentCampaignData( reportId = msg.reportId,reportType = msg.reportType)
    ).recover {
      case e =>
        logger.error(e.getMessage, e)
    }

  }

}
