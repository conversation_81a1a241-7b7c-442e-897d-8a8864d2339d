package utils.mq

import org.apache.pekko.actor.ActorSystem
import api.accounts.TeamId
import api.campaigns.models.{CampaignStepType, InactiveCampaignCheckType}
import api.campaigns.{CampaignDAO, CampaignIdAndTeamId}
import api.campaigns.services.{CampaignDAOService, CampaignId}
import play.api.libs.ws.WSClient
import utils.SRLogger
import utils.cronjobs.InactiveCampaignData
import utils.email_notification.service.EmailNotificationService
import utils.mq.services.{MQConfig, SimpleMqServiceTrait}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

class MqStopInactiveCampaign(
                            campaignDAO: CampaignDAO,
//                            emailNotificationService: EmailNotificationService,
                            campaignDAOService: CampaignDAOService
                            ) extends SimpleMqServiceTrait[CampaignIdAndTeamId]{
  override val queueBaseName: String = MQConfig.stopInactiveCampaignQueueBaseName
  override val prefetchCount: Int = MQConfig.stopInactiveCampaignPrefetchCount

  override def processMessage(msg: CampaignIdAndTeamId)(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem): Future[Any] = Future{


    given Logger: SRLogger = new SRLogger(s"MqStopInactiveCampaign tid_${msg.team_id} cid_${msg.campaign_id}")


    val result = for {
      stepTypesInCampaign: Set[CampaignStepType] <- Try{campaignDAOService.findOrderedSteps(
          campaignId = msg.campaign_id,
          teamId = TeamId(msg.team_id)
        )
        .map(_.step_type)
        .toSet
      }
      inactive: Option[InactiveCampaignData] <- campaignDAO
        .getInactiveCampaignsForStopping(
          InactiveCampaignCheckType.ThirtyDayCheck,
          campaignIdAndTeamId = msg,
          stepTypesInCampaign = stepTypesInCampaign
        )
      pause: Seq[CampaignId] <- if(inactive.nonEmpty) {
        campaignDAO.pauseAllCampaignsWithIdAndTeamId(
          campaignIds = Seq(CampaignId(msg.campaign_id)),
          team_id = TeamId(msg.team_id),
          status_changed_by = queueBaseName)
      } else Success(Seq())

    } yield {
//      if(inactive.nonEmpty) {
//        emailNotificationService.sendMailFromAdmin(
//          toEmail = "<EMAIL>",
//          toName = None,
//          subject = "Pausing Campaign that is finished after 30 days",
//          body =
//            s"""
//              |Hey Team,<br/>
//              |We are pausing the following campaign that has reached the 30 day mark with no activity.<br/>
//              | Please check if it is done running.<br/>
//              | campaign id = ${inactive.head.campaign_id.id}<br/>
//              | team id = ${inactive.head.team_id.id}<br/>
//              | owner email = ${inactive.head.campaign_owner_email}<br/>
//              | org = ${inactive.head.org_name}<br/>
//              | team_name = ${inactive.head.team_name}<br/>
//              | campaign_name = ${inactive.head.campaign_name}<br/>
//              |""".stripMargin,
//          additionalBccEmails = Seq()
//        )
//
//      }

      pause
    }
    result match {
      case Success(value) => value.foreach(cid => logger.debug(s"stopped inactive campaign cid_${cid} tid_${msg.team_id}"))

      case Failure(exception) => logger.error(s"FATAL error while pausing campaign ${Option(exception.getMessage).getOrElse("")}")
    }
  }

}
