package utils.mq.prospect_category

import api.accounts.TeamId
import api.accounts.models.AccountId
import api.prospects.models.{ProspectCategory, ProspectId}
import api.team_inbox.service.ReplySentimentUuid
import utils.mq.services.{MQConfig, SimpleMqPublisherServiceTrait}
import utils.mq.spammonitor.MQSpamMonitor.publishMsg

import scala.util.Try

case class MqAutoUpdateProspectCategoryMsg(
                                            teamId: TeamId,
                                            prospectIds: Seq[ProspectId],
                                            doerAccountId: AccountId,
                                            newProspectCategory: Option[ProspectCategory.Value],
                                            replySentimentUuid: Option[ReplySentimentUuid]
                                          )

class MqAutoUpdateProspectCategoryPublisher extends SimpleMqPublisherServiceTrait[MqAutoUpdateProspectCategoryMsg] {

  val queueBaseName: String = MQConfig.autoUpdateProspectCategoryQueueBaseName
  val prefetchCount: Int = MQConfig.autoUpdateProspectCategoryPrefetchCount
}
