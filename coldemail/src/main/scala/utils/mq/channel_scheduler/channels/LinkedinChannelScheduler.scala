package utils.mq.channel_scheduler.channels

import api.AppConfig
import api.accounts.models.OrgId
import api.accounts.{Account, TeamId}
import api.campaigns.models.CampaignStepType
import api.tasks.pgDao.TaskPgDAO
import api.campaigns.CampaignProspectDAO
import api.campaigns.models.CampaignStepType.ManualEmailMagicContent
import api.campaigns.services.{CampaignProspectService, CampaignService}
import api.columns.InternalMergeTagValuesForProspect
import api.linkedin.LinkedinSettingDAO
import api.prospects.ProspectAccount
import api.prospects.models.{ProspectDataForChannelScheduling, ProspectId}
import eventframework.ProspectObject
import org.joda.time.{DateTime, DateTimeZone, Seconds}
import sr_scheduler.models.CampaignForScheduling.CampaignEmailSettingForScheduler
import sr_scheduler.models.{CampaignForScheduling, ChannelDataForScheduling, ChannelType, ScheduledProspectsCountForCampaign}
import utils.mq.channel_scheduler.{MqCampaignSchedulingMetadataMigration, SchedulerMapStepIdAndDelay}
import utils.templating.TemplateService
import utils.{SRLogger, SrLoggerTrait}
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService

import scala.util.{Success, Try}

case class StepType(step_type_value: String) extends AnyVal {
  def getCampaignStepType: CampaignStepType = CampaignStepType.fromKey(step_type_value).get
}
case class Count(count_value: Int) extends AnyVal

case class StepTypeAndCount(
                           stepType: StepType,
                           count: Count
                           )

class LinkedinChannelScheduler(
                               linkedinSettingDAO: LinkedinSettingDAO,
                               taskDAO: TaskPgDAO,
//                               taskService: TaskService,
                               val mqCampaignSchedulingMetadataMigration: MqCampaignSchedulingMetadataMigration
                              ) extends ChannelSchedulerTrait {

  override type CampaignForSchedulingType = CampaignForScheduling.CampaignForSchedulingLinkedin

  case class LinkedinChannelScheduledProspectsCountForCampaign(

                                                               // for the followup-priority (previously EmailPriority), we need these counts for every channel
                                                               counts: Seq[ScheduledProspectsCountForCampaign],
                                                               campaign: CampaignForSchedulingType

                                                             ) extends ChannelScheduledProspectsCountForCampaign


  override type ChannelDataForSchedulingType = ChannelDataForScheduling.LinkedinChannelDataForScheduling

  override type ChannelScheduledProspectsCountForCampaignType = LinkedinChannelScheduledProspectsCountForCampaign

  override type ProspectDataForChannelSchedulingType = ProspectDataForChannelScheduling.LinkedinChannelProspectForScheduling

  override def getChannelForScheduling(
                                        channelId: ChannelId,
                                        teamId: TeamId,
                                        logger: SrLoggerTrait
                                      ): Option[ChannelDataForSchedulingType] = {
    // TODO Multichannel: Pass Team Id to below function
    logger.info("\n\ngetChannelForScheduling linkedin")
    linkedinSettingDAO.getChannelDataForScheduling(
      linkedinSettingId = channelId,
      teamId = teamId
    )
  }

  override def findCampaignsForSchedulingChannel(
                                                  channelDataForScheduling: ChannelDataForSchedulingType,
                                                  campaignService: CampaignService,
                                                )(
                                                  implicit Logger: SRLogger
                                                ): Try[Seq[CampaignForSchedulingType]] = {
    // TODO Multichannel: extract TeamId from ChannelDataForScheduling and pass it to below function
    Logger.info("\n\nfindCampaignsForSchedulingChannel linkedin_channel")
    campaignService.findCampaignForSchedulingTasks(
      channelStepType = CampaignStepType.LinkedinInmail.channelStepType,  // Using Inmail just to address this is Linkedin Channel
      channelSettingUuid = channelDataForScheduling.linkedinSetting.uuid,
      Logger = Logger
    ).map {
      _.asInstanceOf[Seq[CampaignForSchedulingType]]
    }
  }


  override def updateLastScheduledForChannelAndSetInQueueForSchedulingToFalse(
                                              channelDataForScheduling: ChannelDataForSchedulingType,
                                              latestScheduledAt: Option[DateTime]

                                            )(
                                              implicit Logger: SRLogger
                                            ): Try[Int] = {

    // TODO Multichannel: extract TeamId from ChannelDataForScheduling and pass it to below function
    Logger.info("\n\nupdateLastScheduledForChannel linkedin_channel")
    linkedinSettingDAO.updateLastScheduled(
      linkedinSettingId = channelDataForScheduling.linkedinSetting.uuid,
      latestTaskScheduledAt = latestScheduledAt
    )
  }

  override def findChannelSpecificMissingFields(
                                                 prospect: ProspectObject,
                                                 internalMergeTags: InternalMergeTagValuesForProspect,
                                                 step_type: CampaignStepType,
                                                 templateService: TemplateService
                                      )(using logger: SRLogger): Try[Seq[String]] = Try{

    val input = templateService.getOverallMergeTags(
      prospect = prospect,
      internalMergeTags = internalMergeTags,
      channel = step_type.channelType
    )

    step_type match {


      case CampaignStepType.LinkedinConnectionRequest  |
           CampaignStepType.LinkedinViewProfile |
           CampaignStepType.LinkedinInmail |
           CampaignStepType.LinkedinMessage =>

        Seq()


      case CampaignStepType.AutoLinkedinConnectionRequest |
           CampaignStepType.AutoLinkedinInmail |
           CampaignStepType.AutoLinkedinMessage |
           CampaignStepType.AutoLinkedinViewProfile =>

        if (!input.containsKey("linkedin_url") || input.getOrDefault("linkedin_url", null) == null) {
          Seq("linkedin_url")
        }
        else {
          Seq()
        }


      case CampaignStepType.AutoEmailStep |
           CampaignStepType.AutoEmailMagicContent |
           CampaignStepType.ManualEmailMagicContent |
           CampaignStepType.ManualEmailStep |
           CampaignStepType.WhatsappMessage |
           CampaignStepType.SmsMessage |
           CampaignStepType.CallStep |
           CampaignStepType.MoveToAnotherCampaignStep |
           CampaignStepType.GeneralTask  =>

        logger.shouldNeverHappen(s"findChannelSpecificMissingFields :: IMPOSSIBLE different step type found in LinkedinChannelScheduler ${step_type}")

        Seq()

    }

  }


  override def getConsecutiveTaskScheduleDelay(
                                                channelDataForScheduling: ChannelDataForSchedulingType
                                              ): Int = AppConfig.linkedinScheduleTaskDelay // This is manual task so we are hardcoding it.

  override def getLatestTaskScheduledAt(
                                          channelTasks: Vector[GenerateScheduleTaskData]
                                        ): Option[DateTime] = {

    if (channelTasks.nonEmpty) {
      Some(channelTasks.last.schedulerDateTime)
    } else {
      None
    }

  }

  override def calculateMaxToBeScheduledForNextHour(
                                                     channelDataForScheduling: ChannelDataForSchedulingType,
                                                     scheduleFromTime: DateTime,
                                                     scheduleTillTime: DateTime,
                                                   ): Int = {
    val minConsecutiveEmailDelay = AppConfig.MultiChannel.linkedin_minConsecutiveEmailDelay
    Seconds.secondsBetween(scheduleFromTime, scheduleTillTime).getSeconds / minConsecutiveEmailDelay

  }


  override def getScheduledProspectsCountForCampaign(
                                                      channelDataForScheduling: ChannelDataForSchedulingType,
                                                      campaigns: Seq[CampaignForSchedulingType],
                                                      campaignProspectDAO: CampaignProspectDAO,
                                                      stepTypes : Seq[CampaignStepType]
                                                    )(
                                                      implicit logger: SRLogger
                                                    ): Try[Seq[ChannelScheduledProspectsCountForCampaignType]] = {
    logger.info("\n\ngetScheduledProspectsCountForCampaign linkedin_channel")

    taskDAO.getScheduledProspectsCountForChannelType(
      channelType = channelDataForScheduling.channelType,
      ownerAccountId = channelDataForScheduling.channelOwnerAccountId,
      campaignIdAndTimezone = campaigns.map(c => (c.campaign_id, c.timezone)),
      teamId = channelDataForScheduling.linkedinSetting.team_id,
      stepTypes = stepTypes,
      Logger = logger
    )
      .map(campaignCountResults => {

        val countResults = campaigns
          .map(c => {

            val campaignCountsAllStepTypes = stepTypes
              .map(campaignStepType => {

                ChannelSchedulerTrait.findScheduledProspectsCountForStepType(
                  campaignId = c.campaign_id,
                  campaignStepType = campaignStepType,
                  campaignCounts = campaignCountResults
                )
              })

            LinkedinChannelScheduledProspectsCountForCampaign(
              campaign = c,
              counts = campaignCountsAllStepTypes
            )
          })


        countResults
      })

  }




  override def getScheduleFromTime(
                                    channelDataForScheduling: ChannelDataForSchedulingType
                                  ): DateTime = {
    val scheduleFromTime = channelDataForScheduling.linkedinSetting.latest_task_scheduled_at

    if (scheduleFromTime.isDefined && scheduleFromTime.get.isAfterNow) {
      scheduleFromTime.get
    }
    else DateTime.now(DateTimeZone.UTC)
  }


  override def getChannelTaskLimitPerDay(
                                          channelDataForScheduling: ChannelDataForSchedulingType,
                                          campaignStepType: CampaignStepType,
                                        ): Int = {

    val li = channelDataForScheduling.linkedinSetting

    campaignStepType match {

      case CampaignStepType.LinkedinConnectionRequest | CampaignStepType.AutoLinkedinConnectionRequest =>
        li.linkedin_connection_request_limit_per_day

      case CampaignStepType.LinkedinMessage | CampaignStepType.AutoLinkedinMessage =>
        li.linkedin_message_limit_per_day

      case CampaignStepType.LinkedinInmail | CampaignStepType.AutoLinkedinInmail =>
        li.linkedin_inmail_limit_per_day

      case CampaignStepType.LinkedinViewProfile | CampaignStepType.AutoLinkedinViewProfile =>
        li.linkedin_view_profile_limit_per_day

        // This case would not appear

      case CampaignStepType.WhatsappMessage => 0

      case CampaignStepType.GeneralTask | CampaignStepType.MoveToAnotherCampaignStep => 0

      case CampaignStepType.SmsMessage => 0

      case CampaignStepType.CallStep => 0

      case CampaignStepType.AutoEmailStep => 0

      case CampaignStepType.ManualEmailStep => 0

      case ManualEmailMagicContent | CampaignStepType.AutoEmailMagicContent => 0

    }


  }


  override def getCampaignDailyLimitForChannel(
                                                channelCountData: ChannelScheduledProspectsCountForCampaignType,
                                                channelDataForScheduling: ChannelDataForSchedulingType,
                                                campaignStepType: CampaignStepType
                                              ): Int = {

    getChannelTaskLimitPerDay(
      channelDataForScheduling = channelDataForScheduling,
      campaignStepType = campaignStepType
    )

  }

  override def filterProspectsByChannelSpecificChecks(
                                                       prospectIds: Set[Long],
                                                       team_id: TeamId,
                                                       campaignForScheduling: CampaignForSchedulingType,
                                                       campaignProspectDAO: CampaignProspectDAO,
                                                       previousSelectedProspectIds: Set[ProspectId]
                                                     )(
                                                       using Logger: SRLogger

                                                     ): Try[Set[Long]] = {
    // No filtering for Linkedin Tasks
    Success(prospectIds)
  }


  override def filterProspectsIfNeededForCampaign(
                                                   account: Account,
                                                   prospectsFoundForChecking: ProspectsFoundByStepType,
                                                   campaign: CampaignForSchedulingType,
                                                   teamId: TeamId,
                                                   Logger: SRLogger,
                                                 ): Try[ProspectsFoundByStepType] = {
    // No filtering for Linkedin Tasks
    Success(prospectsFoundForChecking)
  }

  override def fetchProspectsV3Multichannel(
                                             channelType: ChannelType,
                                             allowedProspectTimezones: List[String],
                                             prospectIdGreaterThan: Option[Long] = None, // for first page of results
                                             campaignId: Long,
                                             team_id: Long,
                                             limit: Int,
                                             channelRelevantStepIdAndDelay: Vector[SchedulerMapStepIdAndDelay],
                                             newProspectsInCampaign: Boolean,
                                             firstStepIsMagicContent: Boolean,
                                             sendOnlyToProspectsWhoWereSentInCurrentCycle: Option[DateTime],
                                             campaignProspectDAO: CampaignProspectDAO,
                                             useModifiedQueryForDripCampaign: Boolean,
                                             campaignProspectService: CampaignProspectService,
                                             campaign: CampaignForSchedulingType,
                                             campaign_email_setting: Option[CampaignEmailSettingForScheduler],
                                             org_id: OrgId,
//                                             emailNotCompulsoryEnabled : Boolean
                                           )(using Logger: SRLogger): Try[List[ProspectDataForChannelSchedulingType]] = {
    campaignProspectService
      .fetchProspectsV3MultichannelWithEmailOptionalCheck(
        channelType = channelType,
        prospectIdGreaterThan = prospectIdGreaterThan,
        allowedProspectTimezones = allowedProspectTimezones,
        campaignId = campaignId,
        teamId = TeamId(team_id),
        channelRelevantStepIdAndDelay = channelRelevantStepIdAndDelay,

        limit = limit,
        newProspectsInCampaign = newProspectsInCampaign,
        firstStepIsMagicContent = firstStepIsMagicContent,
        useModifiedQueryForDripCampaign = useModifiedQueryForDripCampaign,

        sendOnlyToProspectsWhoWereSentInCurrentCycle = sendOnlyToProspectsWhoWereSentInCurrentCycle,
        campaign_email_setting = None, //campaign don't have campaign_email_settings_id for linkedin
        org_id = org_id,
//        emailNotCompulsoryEnabled = emailNotCompulsoryEnabled,
        head_step_id = campaign.campaign_type_data.head_id
      ).map(_.asInstanceOf[List[ProspectDataForChannelScheduling.LinkedinChannelProspectForScheduling]])
  }
}
