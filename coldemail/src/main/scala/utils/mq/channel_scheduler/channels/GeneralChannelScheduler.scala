package utils.mq.channel_scheduler.channels

import api.accounts.models.OrgId
import api.accounts.{Account, TeamId}
import api.campaigns.models.CampaignStepType
import api.tasks.pgDao.TaskPgDAO
import api.campaigns.CampaignProspectDAO
import api.campaigns.services.{CampaignProspectService, CampaignService}
import api.general.GeneralSettingDAO
import api.prospects.models.{ProspectDataForChannelScheduling, ProspectId}
import org.joda.time.{DateTime, DateTimeZone, Seconds}
import sr_scheduler.models.CampaignForScheduling.CampaignEmailSettingForScheduler
import sr_scheduler.models.{CampaignForScheduling, ChannelDataForScheduling, ChannelType, ScheduledProspectsCountForCampaign}
import utils.mq.channel_scheduler.{MqCampaignSchedulingMetadataMigration, SchedulerMapStepIdAndDelay}
import utils.{SRLogger, SrLoggerTrait}
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService

import scala.util.{Success, Try}

class GeneralChannelScheduler(
                             generalSettingDAO: GeneralSettingDAO,
                             taskDAO: TaskPgDAO,
//                             taskService: TaskService,
                             val mqCampaignSchedulingMetadataMigration: MqCampaignSchedulingMetadataMigration
                             ) extends ChannelSchedulerTrait {

  override type ChannelDataForSchedulingType = ChannelDataForScheduling.GeneralChannelDataForScheduling

  override type ProspectDataForChannelSchedulingType = ProspectDataForChannelScheduling.GeneralChannelProspectForScheduling
  case class GeneralChannelScheduledProspectsCountForCampaign(
                                                               campaign: CampaignForSchedulingType,

                                                               // for the followup-priority (previously EmailPriority), we need these counts for every channel
                                                               counts: Seq[ScheduledProspectsCountForCampaign]

                                                             ) extends ChannelScheduledProspectsCountForCampaign



  override type ChannelScheduledProspectsCountForCampaignType = GeneralChannelScheduledProspectsCountForCampaign

  override type CampaignForSchedulingType = CampaignForScheduling.CampaignForSchedulingGeneral

  override def getChannelForScheduling(
                                        channelId: ChannelId,
                                        teamId: TeamId,
                                        logger: SrLoggerTrait
                                      ): Option[ChannelDataForSchedulingType] = {
    logger.info("\n\ngetChannelForScheduling")
    generalSettingDAO.getChannelDataForScheduling(
      generalChannelSettingUuid = channelId,
      teamId = teamId
    )
  }

  override def findCampaignsForSchedulingChannel(
                                                  channelDataForScheduling: ChannelDataForSchedulingType,
                                                  campaignService: CampaignService,
                                                )(
                                                  implicit Logger: SRLogger
                                                ): Try[Seq[CampaignForSchedulingType]] = {
    campaignService.findCampaignForSchedulingTasks(
      channelStepType = CampaignStepType.GeneralTask.channelStepType,
      channelSettingUuid = channelDataForScheduling.generalSetting.uuid,
      Logger = Logger
    ).map {
      _.asInstanceOf[Seq[CampaignForSchedulingType]]
    }
  }


  override def updateLastScheduledForChannelAndSetInQueueForSchedulingToFalse(
                                              channelDataForScheduling: ChannelDataForSchedulingType,
                                              latestScheduledAt: Option[DateTime]

                                            )(
                                              implicit Logger: SRLogger
                                            ): Try[Int] = {

    generalSettingDAO.updateLastScheduled(
      ownerAccountId = channelDataForScheduling.channelOwnerAccountId,
      latestTaskScheduledAt = latestScheduledAt
    )
  }

  override def getConsecutiveTaskScheduleDelay(
                                                channelDataForScheduling: ChannelDataForSchedulingType
                                              ): Int = 60 // This is manual task so we are hardcoding it.

  override def getLatestTaskScheduledAt(
                                          channelTasks: Vector[GenerateScheduleTaskData]
                                        ): Option[DateTime] = {

    if (channelTasks.nonEmpty) {
      Some(channelTasks.last.schedulerDateTime)
    } else {
      None
    }

  }


  override def calculateMaxToBeScheduledForNextHour(
                                                     channelDataForScheduling: ChannelDataForSchedulingType,
                                                     scheduleFromTime: DateTime,
                                                     scheduleTillTime: DateTime,
                                                   ): Int = {

    val minConsecutiveEmailDelay = 60 // FIXME: remove harcoded value
    Seconds.secondsBetween(scheduleFromTime, scheduleTillTime).getSeconds / minConsecutiveEmailDelay

  }


  override def getScheduledProspectsCountForCampaign(
                                                      channelDataForScheduling: ChannelDataForSchedulingType,
                                                      campaigns: Seq[CampaignForSchedulingType],
                                                      campaignProspectDAO: CampaignProspectDAO,
                                                      stepTypes: Seq[CampaignStepType]
                                                    )(
                                                      implicit logger: SRLogger
                                                    ): Try[Seq[ChannelScheduledProspectsCountForCampaignType]] = {
    // TODO Multichannel: extract TeamId from ChannelDataForScheduling and pass it to below function

    taskDAO.getScheduledProspectsCountForChannelType(
      channelType = channelDataForScheduling.channelType,
      ownerAccountId = channelDataForScheduling.channelOwnerAccountId,
      campaignIdAndTimezone = campaigns.map(c => (c.campaign_id, c.timezone)),
      teamId = channelDataForScheduling.channelTeamId,
      stepTypes = stepTypes,
      Logger = logger
    )
      .map(campaignCountResults => {

        val countResults = campaigns
          .map(c => {

            val campaignCountsAllStepTypes = stepTypes
              .map(campaignStepType => {

                ChannelSchedulerTrait.findScheduledProspectsCountForStepType(
                  campaignId = c.campaign_id,
                  campaignStepType = campaignStepType,
                  campaignCounts = campaignCountResults
                )
              })

            GeneralChannelScheduledProspectsCountForCampaign(
              campaign = c,
              counts = campaignCountsAllStepTypes
            )
          })


        countResults
      })

  }



  override def getScheduleFromTime(
                                    channelDataForScheduling: ChannelDataForSchedulingType
                                  ): DateTime = {
    val scheduleFromTime = channelDataForScheduling.generalSetting.latest_task_scheduled_at

    if (scheduleFromTime.isDefined && scheduleFromTime.get.isAfterNow) {
      scheduleFromTime.get
    }
    else DateTime.now(DateTimeZone.UTC)
  }


  override def getChannelTaskLimitPerDay(
                                          channelDataForScheduling: ChannelDataForSchedulingType,
                                          campaignStepType: CampaignStepType
                                        ): Int = {
    
    channelDataForScheduling.generalSetting.quota_per_day
    
  }


  override def getCampaignDailyLimitForChannel(
                                                channelCountData: ChannelScheduledProspectsCountForCampaignType,
                                                channelDataForScheduling: ChannelDataForSchedulingType,
                                                campaignStepType: CampaignStepType
                                              ): Int = {

    getChannelTaskLimitPerDay(
      channelDataForScheduling = channelDataForScheduling,
      campaignStepType = campaignStepType
    )

  }
  
  override def filterProspectsByChannelSpecificChecks(
                                                       prospectIds: Set[Long],
                                                       team_id: TeamId,
                                                       campaignForScheduling: CampaignForSchedulingType,
                                                       campaignProspectDAO: CampaignProspectDAO,
                                                       previousSelectedProspectIds: Set[ProspectId]
                                                     )(
                                                       using Logger: SRLogger

                                                     ): Try[Set[Long]] = {
    // No filtering for General Tasks
    Success(prospectIds)
  }


  override def filterProspectsIfNeededForCampaign(
                                                   account: Account,
                                                   prospectsFoundForChecking: ProspectsFoundByStepType,
                                                   campaign: CampaignForSchedulingType,
                                                   teamId: TeamId,
                                                   Logger: SRLogger,
                                                 ): Try[ProspectsFoundByStepType] = {
    // No filtering for General Tasks
    Success(prospectsFoundForChecking)
  }

  override def fetchProspectsV3Multichannel(
                                             channelType: ChannelType,
                                             allowedProspectTimezones: List[String],
                                             prospectIdGreaterThan: Option[Long] = None, // for first page of results
                                             campaignId: Long,
                                             team_id: Long,
                                             limit: Int,
                                             channelRelevantStepIdAndDelay: Vector[SchedulerMapStepIdAndDelay],
                                             newProspectsInCampaign: Boolean,
                                             firstStepIsMagicContent: Boolean,
                                             sendOnlyToProspectsWhoWereSentInCurrentCycle: Option[DateTime],
                                             campaignProspectDAO: CampaignProspectDAO,
                                             useModifiedQueryForDripCampaign: Boolean,
                                             campaignProspectService: CampaignProspectService,
                                             campaign: CampaignForSchedulingType,
                                             campaign_email_setting: Option[CampaignEmailSettingForScheduler],
                                             org_id: OrgId,
//                                             emailNotCompulsoryEnabled : Boolean
                                           )(using Logger: SRLogger): Try[List[ProspectDataForChannelSchedulingType]] = {
    campaignProspectService
      .fetchProspectsV3MultichannelWithEmailOptionalCheck(
        channelType = channelType,
        prospectIdGreaterThan = prospectIdGreaterThan,
        allowedProspectTimezones = allowedProspectTimezones,
        campaignId = campaignId,
        teamId = TeamId(team_id),
        channelRelevantStepIdAndDelay = channelRelevantStepIdAndDelay,

        limit = limit,
        newProspectsInCampaign = newProspectsInCampaign,
        firstStepIsMagicContent = firstStepIsMagicContent,
        useModifiedQueryForDripCampaign = useModifiedQueryForDripCampaign,

        sendOnlyToProspectsWhoWereSentInCurrentCycle = sendOnlyToProspectsWhoWereSentInCurrentCycle,
        campaign_email_setting = None, //campaign don't have campaign_email_settings_id for general channel
        org_id = org_id,
//        emailNotCompulsoryEnabled = emailNotCompulsoryEnabled,
        head_step_id = campaign.campaign_type_data.head_id
      ).map(_.asInstanceOf[List[ProspectDataForChannelScheduling.GeneralChannelProspectForScheduling]])
  }
}
