package utils.mq.channel_scheduler.channels.service

import api.campaigns.services.CampaignId
import utils.{<PERSON><PERSON>, SRLogger}
import utils.cache_utils.CacheKeyGen
import utils.cache_utils.model.CacheIdKeyForLock
import utils.cache_utils.service.{SrRedisHashSetBasedLockServiceV2, SrRedisSimpleLockServiceV2}

import scala.util.Try

class EmailSchedulerJedisService(
                                  srRedisSimpleLockServiceV2: SrRedisSimpleLockServiceV2
                                ) {

  def checkLockForSchedulingCampaign(
                                      campaignId: CampaignId
                                    )(using Logger: SRLogger): Try[Boolean] = {
    val lockKey: CacheIdKeyForLock = CacheKeyGen.getCacheKeyForEmailSchedulerCampaignLock(campaignId = campaignId)
    srRedisSimpleLockServiceV2.checkLock(
      cacheKey = lockKey
    )
  }

  def releaseLockForCampaignScheduling(
                                        campaignIds: Set[CampaignId]
                                      )(using Logger: SRLogger): Try[Map[Boolean, Set[CampaignId]]] = {
    val result = campaignIds.map{ campaignId =>
      val lockKey: CacheIdKeyForLock = CacheKeyGen.getCacheKeyForEmailSchedulerCampaignLock(campaignId = campaignId)

      srRedisSimpleLockServiceV2.releaseLock(
        cacheKey = lockKey
      ).map{
        case false => Seq()
        case true => Seq(campaignId)
      }
    }
    val releaseCampaignsTry = Helpers
      .seqTryToTrySeq(result.toSeq) // Try[Seq[Boolean]]
      .map(_.flatten)
      .map(_.toSet)

    releaseCampaignsTry.map{ releaseCampaigns =>
      Map(
        true -> releaseCampaigns,
        false -> campaignIds.diff(releaseCampaigns)
      )
    }

  }

  def acquireLockAndAddToSetForCampaignScheduling(additionalIdsToLock: Set[CampaignId], expireInSeconds: Int)(using Logger: SRLogger): Try[Set[CampaignId]] = {
    val result = additionalIdsToLock.map { id =>
      val lockKey: CacheIdKeyForLock = CacheKeyGen.getCacheKeyForEmailSchedulerCampaignLock(campaignId = id)
        srRedisSimpleLockServiceV2.acquireLock(
          cacheKey = lockKey,
          expireInSeconds = expireInSeconds
        ).map{
          case false => Seq()
          case true => Seq(id)
        }
    }
    Helpers
      .seqTryToTrySeq(result.toSeq) // Try[Seq[Seq[CampaignId]]]
      .map(_.flatten.toSet) //Try[Set[CampaignId]]

  }
}
