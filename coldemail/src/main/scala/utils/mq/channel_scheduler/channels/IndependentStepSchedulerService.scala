package utils.mq.channel_scheduler.channels
import api.accounts.{Account, AccountService, TeamId}
import api.accounts.models.{AccountId, OrgId}
import api.campaigns.{CPAssignResult, CampaignDAO, CampaignProspectDAO, CampaignStepVariantDAO, CampaignStepWithChildren}
import api.campaigns.models.{CampaignStepData, CampaignStepType, CampaignStepsStructure, CampaignTypeData, IgnoreProspectsInOtherCampaigns}
import api.campaigns.services.{CampaignId, CampaignProspectService, CampaignService}
import api.prospects.models.{ProspectDataForChannelScheduling, ProspectId}
import org.joda.time.DateTime
import sr_scheduler.models.{CampaignForScheduling, ChannelType}
import utils.{Help<PERSON>, SRLogger, SrLoggerTrait}
import utils.mq.channel_scheduler.{ChannelSchedulerService, FetchCampaignStepsData, MqCampaignSchedulingMetadataMigration, SchedulerMapStepIdAndDelay}
import utils.mq.services.MQDoNotNackException
import utils.srlogging.sr.Logger
import utils.testapp.Test_TaskPgDAO.campaignStepVariantDAO

import scala.collection.immutable
import scala.concurrent.duration.DurationInt
import scala.concurrent.{Await, ExecutionContext}
import scala.util.{Failure, Success, Try}

class IndependentStepSchedulerService(
                                     campaignProspectDAO: CampaignProspectDAO,
                                     campaignProspectService: CampaignProspectService,
                                     campaignService: CampaignService,
                                     campaignDAO: CampaignDAO,
                                     campaignStepVariantDAO: CampaignStepVariantDAO,
                                     accountService: AccountService
                                     ) {


  def schedulerDripActivityTypes(campaign_id: CampaignId, team_id: TeamId) = {

    1

    /*
      1. fetch prospects and get next steps for these prospects. If next step is of drip-activity type then
        mast kalandar.

      2. Execute the actions for these drip activity types

      3.

     */




  }

  private def createProspectIdWithNextStepIdMap(
                                                 prospects: List[ProspectDataForChannelScheduling.IndependentChannelProspectForScheduling],
                                                 campaignTypeData: CampaignTypeData.DripCampaignData,
                                                 stepsMappedById: Map[Long, CampaignStepWithChildren],
                                                 campaignHeadStepId: String,
                                                 campaignId: CampaignId,
                                                 campaignProspectDAO: CampaignProspectDAO,
                                                 campaignProspectService: CampaignProspectService
                                               )(implicit ec: ExecutionContext, Logger: SRLogger): Map[ProspectId, CampaignStepWithChildren] = {

    var prospectIdWithStepIdMap: Map[ProspectId, CampaignStepWithChildren] = Map()

    prospects.foreach(p => {
      val stepsWithConditionsFut = p.current_step_id match {
        case Some(stepId) =>
          //          println(s"stepId ------- $stepId")
          NextStepFinderForDrip.findNextStep(
            edges = campaignTypeData.edges,
            nodes = campaignTypeData.nodes,
            parent_step_id = stepId
          )

        case None =>
          NextStepFinderForDrip.getHeadStepsWithCondition(
            edges = campaignTypeData.edges,
            nodes = campaignTypeData.nodes,
            head_node_id = campaignHeadStepId,
//            pid = p.prospect.id,
//            cid = campaignId.id,
//            from = "IndependentStepSchedulerService.createProspectIdWithNextStepIdMap"
          )
      }

      //FIXME drip since this is happening inside the foreach there will be slowness as we will wait for each result individually
      val stepsWithConditions = Await.result(stepsWithConditionsFut, 60000.millis)

      val foundStep = stepsWithConditions.find(sc => {
        val stepData = stepsMappedById(sc.child_step_id)
        val lastSentSteps = campaignProspectDAO.getPreviouslySentStepsForProspect(
          prospectId = ProspectId(p.prospect.id),
          teamId = TeamId(p.prospect.team_id),
          campaignId = campaignId
        ).get

        Logger.info(s"createProspectIdWithNextStepIdMap :: matchMultipleConditionsWithProspect :: prospect_id : ${p.prospect.id} :: lastSentStepsCount : ${lastSentSteps.size}")

        val isProspectConnectedToLinkedin: Boolean = campaignProspectService.getLinkedInProfileConnectedUuid(
          campaignId = campaignId,
          linkedinProfileUrl = p.prospect.linkedin_url,
          teamId = TeamId(p.prospect.team_id)
        ).get.isDefined

        ChannelSchedulerService.matchMultipleConditionsWithProspect(
          prospectObject = p.prospect,
          conditions = sc.condition,
          lastSentSteps = lastSentSteps,
          isProspectConnectedToLinkedin = isProspectConnectedToLinkedin,
          stepData = stepData
        ) match {
          case Left(value) =>
            //            println(s"values ------- $value")
            campaignProspectService.addIgnoreLogAndUpdateNextCheckForSchedulingAt(
              prospects = List((p, value.next_check_for_scheduling_at, value)),
              campaignId = campaignId,
              head_step_id = campaignHeadStepId,
              teamId = TeamId(p.prospect.team_id),
            )

            false
          case Right(matchResult) =>
            //            println(s"values ------- $value")
            Logger.info(s"createProspectIdWithNextStepIdMap :: matchMultipleConditionsWithProspect :: prospect_id : ${p.prospect.id} :: matched : ${matchResult}")

            matchResult
        }
      })

      //      println(s"foundStep1 --- $foundStep")
      foundStep match {
        case None => //Do Nothing
        case Some(stepWithConditions) =>
          prospectIdWithStepIdMap += ProspectId(p.prospect.id) -> stepsMappedById(stepWithConditions.child_step_id)
      }
    })
    //    println(s"prospectIdWithStepIdMap1 --- $prospectIdWithStepIdMap")

    prospectIdWithStepIdMap
  }

  def fetchCampaignStepsForDripCampaign(
                                         c: CampaignForScheduling.IndependentStepScheduling ,
                                          account: Account
                                       )(using Logger: SRLogger, ec: ExecutionContext): Try[FetchCampaignStepsData] = {

    c.campaign_type_data match {
      case data: CampaignTypeData.EmailChannelData => Failure(new Exception("Impossible! Independent StepScheduler is only present in drip campaign"))
      case data: CampaignTypeData.MultiChannelCampaignData=> Failure(new Exception("Impossible! Independent StepScheduler is only present in drip campaign"))
      case data: CampaignTypeData.MagicContentData => Failure(new Exception("Impossible! Independent StepScheduler is only present in drip campaign"))
      case data: CampaignTypeData.DripCampaignData=>

        // nxd notes:  IOAction inside loop (flatmap)
        // this returns step of all channel / step-types
        val campaignSteps: Seq[CampaignStepWithChildren] = campaignStepVariantDAO.findByCampaignIdForSchedule(
          campaignId = c.campaign_id,
          isABTestingEnabled = account.org.settings.enable_ab_testing,
          logger = Logger
        )


        val stepsMappedById: Map[Long, CampaignStepWithChildren] = campaignSteps.map(s => s.id -> s).toMap

        //    val campaignStepData_for_logging: Seq[CampaignStepDataForLogging] = campaignSteps.map(cs =>
        //      CampaignStepDataForLogging(
        //        campaign_id = cs.campaign_id,
        //        label = cs.label,
        //        step_type = cs.step_type,
        //        id = cs.id)
        //    )

        val allCampaignSteps: Vector[SchedulerMapStepIdAndDelay] = ChannelSchedulerService.getMapOfStepIdAndRequiredDelayForDripCampaign(
          headStepId = data.head_node_id,
          edges = data.edges,
          nodes = data.nodes,
          stepsMappedById = stepsMappedById,
//          from = "IndependentStepSchedulerService.fetchCampaignStepsForDripCampaign"
        )

        val campaignsStepsRelevantToChannel: Vector[SchedulerMapStepIdAndDelay] = ChannelSchedulerService.filterCampaignStepsRelevantToChannel(
          allCampaignSteps = allCampaignSteps,
          channelType = ChannelType.IndependentChannel
        )

        Success(FetchCampaignStepsData(
          stepsMappedById = stepsMappedById,
          campaignStepsStructure = CampaignStepsStructure.DripCampaignStepsStructure(
            edges = data.edges,
            nodes = data.nodes,
            head_node_id = data.head_node_id
          ),
          allCampaignSteps = allCampaignSteps,
          relevantCampaignStepsForChannel = campaignsStepsRelevantToChannel,
        ))

    }



  }

  def executeIndependentSteps(
                               p: Map[ProspectId, CampaignStepWithChildren],
                               org_id: OrgId,
                               doerAccountId: AccountId,
                               doerAccountName: String,
                               accountId: Long,
                               team_id: TeamId,
                               current_campaign_id: CampaignId,
                               move_to_campaign_name: String,
                               ignoreProspectsInOtherCampaigns: IgnoreProspectsInOtherCampaigns
                             )(using logger: SRLogger): Try[Seq[CPAssignResult]] = {

    val res = p.map(data => {

      data._2.variants.head.step_data match {
        case _: CampaignStepData.AutoEmailStep =>           Failure(new Exception("Impossible Only Independent StepScheduler related steps should come here"))

        case _: CampaignStepData.ManualEmailStep=>           Failure(new Exception("Impossible Only Independent StepScheduler related steps should come here"))

        case _: CampaignStepData.LinkedinConnectionRequestData =>   Failure(new Exception("Impossible Only Independent StepScheduler related steps should come here"))

        case _: CampaignStepData.LinkedinMessageData=>           Failure(new Exception("Impossible Only Independent StepScheduler related steps should come here"))

        case _: CampaignStepData.LinkedinInmailData =>           Failure(new Exception("Impossible Only Independent StepScheduler related steps should come here"))

        case _: CampaignStepData.LinkedinViewProfile =>          Failure(new Exception("Impossible Only Independent StepScheduler related steps should come here"))

        case _: CampaignStepData.AutoLinkedinConnectionRequest =>  Failure(new Exception("Impossible Only Independent StepScheduler related steps should come here"))

        case _: CampaignStepData.AutoLinkedinMessage =>           Failure(new Exception("Impossible Only Independent StepScheduler related steps should come here"))

        case _: CampaignStepData.AutoLinkedinInmail =>           Failure(new Exception("Impossible Only Independent StepScheduler related steps should come here"))

        case _: CampaignStepData.AutoLinkedinViewProfile =>    Failure(new Exception("Impossible Only Independent StepScheduler related steps should come here"))

        case _: CampaignStepData.GeneralTaskData =>           Failure(new Exception("Impossible Only Independent StepScheduler related steps should come here"))

        case _: CampaignStepData.WhatsappMessageData =>      Failure(new Exception("Impossible Only Independent StepScheduler related steps should come here"))

        case _: CampaignStepData.SmsMessageData =>           Failure(new Exception("Impossible Only Independent StepScheduler related steps should come here"))

        case _: CampaignStepData.CallTaskData=>           Failure(new Exception("Impossible Only Independent StepScheduler related steps should come here"))

        case _: CampaignStepData.ManualEmailMagicContentStep | _: CampaignStepData.AutoEmailMagicContentStep => Failure(new Exception(s"Step_type : ${data._2.variants.head.step_data.step_type.toKey} ::  Impossible Only Independent StepScheduler related steps should come here"))

        case anotherCampaignStepData: CampaignStepData.MoveToAnotherCampaignStepData=>

        campaignService.getCampaignBasicInfo(
          campaginId = anotherCampaignStepData.move_to_another_campaign_id.id,
          teamId = team_id.id,
        ) match {
          case Failure(exception) =>

            logger.error(s"executeIndependentSteps :: campaign_id : ${current_campaign_id.id} :: teamId : ${team_id.id}", exception)
            Failure(exception)

          case Success(None) =>

            logger.error(s"executeIndependentSteps None found :: campaign_id : ${current_campaign_id.id} :: teamId : ${team_id.id}")
            Failure(new Exception("Move to another campaign details not found."))

          case Success(Some(value)) =>

           val res =  campaignProspectService.moveToAnotherCampaign(
              prospect_id = data._1,
              orgId = org_id,
              doerAccountId = doerAccountId.id,
              doerAccountName = doerAccountName,
              accountId = accountId,
              team_id = team_id,
              current_campaign_id = current_campaign_id,
              move_to_campaign_id = anotherCampaignStepData.move_to_another_campaign_id,
              move_to_campaign_name = value.name,
              ignoreProspectsInOtherCampaigns = ignoreProspectsInOtherCampaigns
            )

            logger.info(s"executeIndependentSteps moveToAnotherCampaign :: prospect_id : ${data._1} :: " +
              s"current_campaign_id: ${current_campaign_id.id} :: other_campaign_id: ${anotherCampaignStepData.move_to_another_campaign_id.id}" +
              s" teamId : ${team_id.id}")

            res

        }

      }
    }).toSeq

    Helpers.seqTryToTrySeq(res)
  }


/*

      1. create_step_tyep, create_channel_type.
      2. then create a campaign with those step-types,
      3. Then try to scheduler these campaigns with the new scheduler that I'm creating.
      4. I need one scheduler

     */

  def preExecutionStepsAndChecks(
                            campaign_id:CampaignId,
                            team_id: TeamId,
                            org_id: OrgId
                            )(using logger: SRLogger, ec: ExecutionContext) = {

    // suppose from mq I have the campaign, Id and prospect id,
    // now, I want the prospect ids from that campaign.
    // now, I will filter out prospects that are not suitable.
    // now, I will fetchNextStepId for these prospects and create a map of it.
    // now, I will take the action

    // Fetch Prospects
    for {
//      campaign_steps <- {campaignService.getCampaignBasicInfo(
//        teamId = team_id.id,
//        campaginId = campaign_id.id
//      ).get}

      campaign_data_for_scheduling <- campaignDAO.findCampaignsForSchedulingIndependentSteps(
        campaign_id = campaign_id,
        team_id = team_id,
        Logger = logger
      ) match {
        case Failure(exception) =>
          logger.error(s"preExecutionStepsAndChecks Error findCampaignsForSchedulingIndependentSteps exception :: campaign_id : ${campaign_id} :: team_id : ${team_id} :: org_id:  ${org_id}", exception)
          Failure(exception)
        case Success(None) =>

          logger.error(s"preExecutionStepsAndChecks Error findCampaignsForSchedulingIndependentSteps None :: campaign_id : ${campaign_id} :: team_id : ${team_id} :: org_id:  ${org_id}")

          Failure(MQDoNotNackException(s"Error findCampaignsForSchedulingIndependentSteps None found campaign_id: ${campaign_id}"))

        case Success(Some(campaignData)) =>

          logger.info(s"preExecutionStepsAndChecks findCampaignsForSchedulingIndependentSteps found :: campaign_id: ${campaignData.campaign_id}")
          Success(campaignData)
      }

      account <- {
        accountService.find(
        id = campaign_data_for_scheduling.campaign_owner_id
      )}

      independent_step_type <- {
        fetchCampaignStepsForDripCampaign(
        c = campaign_data_for_scheduling,
        account = account
      )
      }

      // New prospects
      prospects_new <- {

        val head_step_type: Option[CampaignStepType] = independent_step_type.allCampaignSteps.find(_.is_head_step_in_the_campaign).map(_.currentStepType)
        val firstStepIsMagicContent: Boolean = head_step_type.isDefined && (head_step_type.get == CampaignStepType.AutoEmailMagicContent || head_step_type.get == CampaignStepType.ManualEmailMagicContent)
        val newProspects = campaignProspectDAO.fetchProspectsV3Multichannel(
        channelType = ChannelType.IndependentChannel,
        allowedProspectTimezones = List(campaign_data_for_scheduling.timezone), // Fixme-drip-activity-type
        prospectIdGreaterThan = None, // for first page of results
        campaignId = campaign_id.id,
        teamId = team_id,
        limit = 1000, // Fixme-drip-activity-type
        channelRelevantStepIdAndDelay = independent_step_type.allCampaignSteps,
        newProspectsInCampaign = true, // Fixme-drip-activity-type
        firstStepIsMagicContent = firstStepIsMagicContent,
        sendOnlyToProspectsWhoWereSentInCurrentCycle = None,
        useModifiedQueryForDripCampaign = true,
        campaign_email_setting = None,
        orgId = org_id,
        enable_magic_column = account.org.org_metadata.enable_magic_column.getOrElse(false),
         //emailNotCompulsoryEnabled = false // Fixme-drip-activity-type
      )

        logger.info(s"preExecutionStepsAndChecks :: fetchProspectsV3Multichannel new prospects :: campaign_id: ${campaign_id.id} :: count: ${newProspects.map(_.size).getOrElse(0)}")
        newProspects
      }

      // Followup prospects
      prospects_followup <- {

        val head_step_type: Option[CampaignStepType] = independent_step_type.allCampaignSteps.find(_.is_head_step_in_the_campaign).map(_.currentStepType)
        val firstStepIsMagicContent: Boolean = head_step_type.isDefined && (head_step_type.get == CampaignStepType.AutoEmailMagicContent || head_step_type.get == CampaignStepType.ManualEmailMagicContent)
        val followupProspects = campaignProspectDAO.fetchProspectsV3Multichannel(
        channelType = ChannelType.IndependentChannel,
        allowedProspectTimezones = List(campaign_data_for_scheduling.timezone), // Fixme-drip-activity-type
        prospectIdGreaterThan = None, // for first page of results
        campaignId = campaign_id.id,
        teamId = team_id,
        limit = 1000, // Fixme-drip-activity-type
        channelRelevantStepIdAndDelay = independent_step_type.allCampaignSteps,
        newProspectsInCampaign = false, // Fixme-drip-activity-type
        firstStepIsMagicContent = firstStepIsMagicContent,
        sendOnlyToProspectsWhoWereSentInCurrentCycle = None,
        useModifiedQueryForDripCampaign = true,
        campaign_email_setting = None,
        orgId = org_id,
        enable_magic_column = account.org.org_metadata.enable_magic_column.getOrElse(false),
         //emailNotCompulsoryEnabled = false // Fixme-drip-activity-type
      )

        logger.info(s"preExecutionStepsAndChecks :: fetchProspectsV3Multichannel followup prospects :: campaign_id: ${campaign_id.id} :: count: ${followupProspects.map(_.size).getOrElse(0)}")
        followupProspects
      }




      prospectIdAndNextStepMap <- {

        val prospectStepMapping = campaign_data_for_scheduling.campaign_type_data match {
          case _: CampaignTypeData.MultiChannelCampaignData => Failure(new Exception("Impossible!  Independent Step-Scheduler is only present in drip campaign"))
          case _: CampaignTypeData.EmailChannelData => Failure(new Exception("Impossible! Independent Step-Scheduler is only present in drip campaign"))
          case _: CampaignTypeData.MagicContentData => Failure(new Exception("Impossible! Independent Step-Scheduler is only present in drip campaign"))
          case data: CampaignTypeData.DripCampaignData =>

            val totalProspects = prospects_new ++ prospects_followup

            logger.info(s"prospectIdAndNextStepMap :: total prospects count: ${totalProspects.size} :: campaign_id: ${campaign_id.id}")

            val prospectStepMap: Map[ProspectId, CampaignStepWithChildren] = createProspectIdWithNextStepIdMap(
              prospects = totalProspects.distinct.map(prospectData => ProspectDataForChannelScheduling.IndependentChannelProspectForScheduling(
                prospect = prospectData.prospect,
                current_step_status_data = prospectData.current_step_status_data,
                current_step_id = prospectData.current_step_id
              )),
              campaignTypeData = data,
              stepsMappedById = independent_step_type.stepsMappedById,
              campaignHeadStepId = data.head_node_id,
              campaignId = campaign_id,
              campaignProspectDAO = campaignProspectDAO,
              campaignProspectService = campaignProspectService
            )

            logger.info(s"prospectStepMap :: prospect count: ${prospectStepMap.size} :: campaign_id: ${campaign_id.id} :: team_id: ${team_id.id}")

            // Log prospect_id -> (step_id, step_type) mappings
            if (prospectStepMap.nonEmpty) {
              val mappingLog = prospectStepMap.map { case (prospectId, step) =>
                s"${prospectId.id} -> (${step.id}, ${step.step_type.toKey})"
              }.mkString(", ")
              logger.info(s"Prospect to step mappings: $mappingLog :: campaign_id: ${campaign_id.id}")
            }

            val filteredProspects: Map[ProspectId, CampaignStepWithChildren] = prospectStepMap
              .filter(prospectStep => prospectStep._2.step_type.channelType == ChannelType.IndependentChannel)

            logger.info(s"filtered prospects count: ${filteredProspects.size} :: campaign_id: ${campaign_id.id} :: team_id: ${team_id.id}")

            // Log filtered prospect_id -> (step_id, step_type) mappings
            if (filteredProspects.nonEmpty) {
              val filteredMappingLog = filteredProspects.map { case (prospectId, step) =>
                s"${prospectId.id} -> (${step.id}, ${step.step_type.toKey})"
              }.mkString(", ")
              logger.info(s"Filtered prospect to step mappings: $filteredMappingLog :: campaign_id: ${campaign_id.id}")
            }

            Success(filteredProspects)
        }

        logger.info(s"preExecutionStepsAndChecks :: prospectStepMapping :: prospect count: ${prospectStepMapping.map(_.size).getOrElse(0)} :: campaign_id: ${campaign_id.id}")

        prospectStepMapping
      }

      // Assumption all the prospect till here have their current step completed. and is of independent channel type.
      // so I can directly execute the action types.
      stepExecutionResults <- {
        val executionResults = executeIndependentSteps(
          p = prospectIdAndNextStepMap,
          org_id = org_id,
          doerAccountId = AccountId(id = account.internal_id),
          doerAccountName = account.first_name.getOrElse("") + " " +  account.last_name.getOrElse(""),
          accountId = account.internal_id,
          team_id = team_id,
          current_campaign_id = campaign_id,
          move_to_campaign_name = "dummy campaign name", // Fixme-drip-activity-type
          ignoreProspectsInOtherCampaigns =  IgnoreProspectsInOtherCampaigns.IgnoreProspectsActiveInOtherCampaigns
        )

        logger.info(s"preExecutionStepsAndChecks :: executeIndependentSteps results count: ${executionResults.map(_.size).getOrElse(0)} :: campaign_id: ${campaign_id.id}")

        executionResults
      }

      // update_campaigns_prospects to correct step_id and type
      /*
        22-Nov-2024:
        *  move this before executing steps. *
       */
      campaignProspectUpdates: Seq[Int] <- {

        Helpers.seqTryToTrySeq(prospectIdAndNextStepMap.groupBy(_._2.id).map(groupedProspects => {
          val prospectIds = groupedProspects._2.keys.toList
          val stepDetails = groupedProspects._2.values.head

          val updatedProspectsCount = campaignProspectDAO.updateCampaignProspectStatusToDoneForIndependentStepScheduler(
            team_id = team_id,
            campaignId = campaign_id,
            prospectIds = prospectIds,
            step_details_that_is_completed = stepDetails
          )

          logger.info(s"updateCampaignProspectStatusToDoneForIndependentStepScheduler :: prospect count: ${prospectIds.size} :: step_id: ${stepDetails.id} :: updated count: ${updatedProspectsCount}")

          // Log prospect_ids for this step group
          if (prospectIds.nonEmpty) {
            val prospectIdsLog = prospectIds.map(_.id).mkString(", ")
            logger.info(s"Prospects updated for step ${stepDetails.id} (${stepDetails.step_type.toKey}): $prospectIdsLog :: campaign_id: ${campaign_id.id}")
          }

          updatedProspectsCount
        }).toSeq) match {
          case Failure(exception) =>

            logger.error(s"updateCampaignProspectStatusToDoneForIndependentStepScheduler :: Error while updating :: campaign_id: ${campaign_id} :: team_id : ${team_id}", exception)
            Failure(exception)

          case Success(updateCounts) =>

            logger.info(s"updateCampaignProspectStatusToDoneForIndependentStepScheduler :: total updated ${updateCounts.sum} :: campaign_id: ${campaign_id} :: team_id : ${team_id}")
            Success(updateCounts)

        }}


    }yield{

      stepExecutionResults

    }

  }


}

