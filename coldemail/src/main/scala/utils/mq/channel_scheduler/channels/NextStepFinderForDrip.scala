package utils.mq.channel_scheduler.channels

import api.AppConfig
import api.AppConfig.{isDevDomain, isProd, isTest}
import api.campaigns.CampaignStepWithChildren
import api.campaigns.models.CampaignTypeData.DripCampaignData
import api.phantombuster.CommandExecutionResult
import play.api.libs.json.{<PERSON>s<PERSON><PERSON><PERSON>, JsResult, JsSuccess, JsValue, Json, OFormat, Reads, Writes}
import utils.{CommandExecutor, SRLogger}

import scala.concurrent.duration.DurationInt
import scala.concurrent.{Await, ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

sealed trait CampaignStepCondition {
  def toString: String
  def getOppositeCondition: CampaignStepCondition
}

object CampaignStepCondition {
  //prospect level conditions
  private val hasEmail = "has_email"
  private val doesNotHaveEmail = "does_not_have_email"

  private val validEmail = "valid_email"
  private val invalidEmail = "invalid_email"

  private val hasLinkedinUrl = "has_linkedin_url"
  private val doesNotHaveLinkedinUrl = "does_not_have_linkedin_url"

  private val hasPhoneNumber = "has_phone_number"
  private val doesNotHavePhoneNumber = "does_not_have_phone_number"

  //neutral conditions
  private val noCondition = "no_condition"
  private val impossibleCondition = "impossible_condition"

  //Step conditions
  private val bounced = "bounced"
  private val delivered = "delivered"

  private val has_opened = "has_opened"
  private val not_opened = "not_opened"

  private val linkedIn_message_not_sent = "linkedIn_message_not_sent"
  private val linkedIn_message_sent = "LinkedIn_message_sent"

  private val linkedIn_profile_connected = "linkedIn_profile_connected"
  private val linkedIn_profile_not_connected = "linkedIn_profile_not_connected"


  private val call_not_picked = "call_not_picked"
  private val call_picked = "call_picked"

  private val has_replied = "has_replied"
  private val has_replied_interested = "has_replied_interested"
  private val has_replied_do_not_contact = "has_replied_do_not_contact"
  private val has_not_replied = "has_not_replied"



  //Prospect condition -------------------------------------------------------------------------------------------------
  case object HasEmail extends CampaignStepCondition {
    override def toString: String = hasEmail
    override def getOppositeCondition: CampaignStepCondition = DoesNotHaveEmail
  }

  case object DoesNotHaveEmail extends CampaignStepCondition {
    override def toString: String = doesNotHaveEmail
    override def getOppositeCondition: CampaignStepCondition = HasEmail
  }


  case object ValidEmail extends CampaignStepCondition {
    override def toString: String = validEmail
    override def getOppositeCondition: CampaignStepCondition = InvalidEmail
  }
  case object InvalidEmail extends CampaignStepCondition {
    override def toString: String = invalidEmail
    override def getOppositeCondition: CampaignStepCondition = ValidEmail
  }


  case object HasLinkedinUrl extends CampaignStepCondition {
    override def toString: String = hasLinkedinUrl
    override def getOppositeCondition: CampaignStepCondition = DoesNotHaveLinkedinUrl
  }

  case object DoesNotHaveLinkedinUrl extends CampaignStepCondition {
    override def toString: String = doesNotHaveLinkedinUrl
    override def getOppositeCondition: CampaignStepCondition = HasLinkedinUrl
  }


  case object HasPhoneNumber extends CampaignStepCondition {
    override def toString: String = hasPhoneNumber
    override def getOppositeCondition: CampaignStepCondition = DoesNotHavePhoneNumber
  }

  case object DoesNotHavePhoneNumber extends CampaignStepCondition {
    override def toString: String = doesNotHavePhoneNumber
    override def getOppositeCondition: CampaignStepCondition = HasPhoneNumber
  }

  //neutral condition --------------------------------------------------------------------------------------------------

  case object NoCondition extends CampaignStepCondition {
    override def toString: String = noCondition
    override def getOppositeCondition: CampaignStepCondition = CampaignStepCondition.ImpossibleCondition
  }

  case object ImpossibleCondition extends CampaignStepCondition {
    override def toString: String = impossibleCondition
    override def getOppositeCondition: CampaignStepCondition = CampaignStepCondition.NoCondition
  }

  //Step level condition -----------------------------------------------------------------------------------------------

  case object Bounced extends CampaignStepCondition {
    override def toString: String = bounced
    override def getOppositeCondition: CampaignStepCondition = Delivered
  }
  case object Delivered extends CampaignStepCondition {
    override def toString: String = delivered
    override def getOppositeCondition: CampaignStepCondition = Bounced
  }


  case object Opened extends CampaignStepCondition {
    override def toString: String = has_opened
    override def getOppositeCondition: CampaignStepCondition = NotOpened
  }
  case object NotOpened extends CampaignStepCondition {
    override def toString: String = not_opened
    override def getOppositeCondition: CampaignStepCondition = Opened
  }
  case object UntilOpened extends CampaignStepCondition {
    override def toString: String = has_opened
    override def getOppositeCondition: CampaignStepCondition = ImpossibleCondition
  }
  case object LinkedInMessageFailed extends CampaignStepCondition {
    override def toString: String = linkedIn_message_not_sent
    override def getOppositeCondition: CampaignStepCondition = LinkedInMessageSent
  }
  
  case object LinkedInMessageSent extends CampaignStepCondition {
    override def toString: String = linkedIn_message_sent
    override def getOppositeCondition: CampaignStepCondition = LinkedInMessageFailed
  }

  case object LinkedInProfileConnected extends CampaignStepCondition {
    override def toString: String = linkedIn_profile_connected

    override def getOppositeCondition: CampaignStepCondition = LinkedinProfileNotConnected
  }

  case object LinkedinProfileNotConnected extends CampaignStepCondition {
    override def toString: String = linkedIn_profile_not_connected

    override def getOppositeCondition: CampaignStepCondition = LinkedInProfileConnected
  }

  case object CallNotPicked extends CampaignStepCondition {
    override def toString: String = call_not_picked
    override def getOppositeCondition: CampaignStepCondition = CallPicked
  }

  case object CallPicked extends CampaignStepCondition {
    override def toString: String = call_picked
    override def getOppositeCondition: CampaignStepCondition = CallNotPicked
  }

  sealed trait HasReplied extends CampaignStepCondition

  case class HasNotReplied(opposite: HasReplied) extends CampaignStepCondition {
    override def toString: String = has_not_replied

    override def getOppositeCondition: CampaignStepCondition = opposite
  }

  case object UntilHasReplied extends HasReplied {
    override def toString: String = has_replied

    override def getOppositeCondition: CampaignStepCondition = CampaignStepCondition.ImpossibleCondition
  }
  case object HasRepliedWithin extends HasReplied {
    override def toString: String = has_replied

    override def getOppositeCondition: CampaignStepCondition = CampaignStepCondition.HasNotReplied(HasRepliedWithin)
  }


  case object HasRepliedInterested extends HasReplied {
    override def toString: String = has_replied_interested

    override def getOppositeCondition: CampaignStepCondition = CampaignStepCondition.HasNotReplied(HasRepliedInterested)
  }


  case object HasRepliedDoNotContact extends HasReplied {
    override def toString: String = has_replied_do_not_contact

    override def getOppositeCondition: CampaignStepCondition = CampaignStepCondition.HasNotReplied(HasRepliedDoNotContact)
  }

  def fromString(key: String, is_within: Boolean = true): Try[CampaignStepCondition] = Try {
    key match {
      case `hasEmail` => HasEmail
      case `doesNotHaveEmail` => DoesNotHaveEmail
      case `hasLinkedinUrl` => HasLinkedinUrl
      case `doesNotHaveLinkedinUrl` => DoesNotHaveLinkedinUrl
      case `hasPhoneNumber` => HasPhoneNumber
      case `doesNotHavePhoneNumber` => DoesNotHavePhoneNumber
      case `noCondition` => NoCondition
      case `impossibleCondition` => ImpossibleCondition
      case `validEmail` => ValidEmail
      case `invalidEmail` => InvalidEmail
      case `bounced` => Bounced
      case `delivered` => Delivered
      case `has_opened` =>
        if(is_within)
        Opened else UntilOpened
      case `not_opened` => NotOpened
      case `has_replied` => if(is_within)
        HasRepliedWithin else UntilHasReplied

      case `has_replied_do_not_contact` => HasRepliedDoNotContact
      case `has_replied_interested` => HasRepliedInterested
      case `has_not_replied` => HasNotReplied(HasRepliedWithin) // this is a default we are putting we should not use the opposite for this
      case `linkedIn_message_not_sent` => LinkedInMessageFailed
      case `linkedIn_message_sent` => LinkedInMessageSent
      case `linkedIn_profile_connected` => LinkedInProfileConnected
      case `linkedIn_profile_not_connected` => LinkedinProfileNotConnected
      case `call_not_picked` => CallNotPicked
      case `call_picked` => CallPicked
    }
  }
}

case class ChildStepIdWithCondition(child_step_id: Long, condition: List[CampaignStepCondition])

case class DataForNode(
                        label: String,
                        type_of_node: String
                      )

object DataForNode {
  implicit val writes: Writes[DataForNode] = new Writes[DataForNode] {
    override def writes(o: DataForNode): JsValue = Json.obj(
      "label" -> o.label,
      "type" -> o.type_of_node
    )
  }
  implicit val reads: Reads[DataForNode] = new Reads[DataForNode] {
    override def reads(json: JsValue): JsResult[DataForNode] = Try {
      DataForNode(
        label = (json \ "label").as[String],
        type_of_node = (json \ "type").as[String]
      )
    } match {
      case Failure(e) => JsError(e.getMessage)
      case Success(data) => JsSuccess(data)
    }
  }

}

case class NodePosition(
                         x: Double,
                         y: Double
                       )

object NodePosition {
  given formats: OFormat[NodePosition] = Json.format[NodePosition]

}
case class NodeData(
                     id: String,
                     type_of_node: String,
                     data: DataForNode,
                     position: NodePosition
                   )

object NodeData {
  implicit val writes: Writes[NodeData] = new Writes[NodeData] {
    override def writes(o: NodeData): JsValue = Json.obj(
      "id" -> o.id,
      "type" -> o.type_of_node,
      "data" -> o.data,
      "position" -> o.position
    )
  }
  implicit val reads: Reads[NodeData] = new Reads[NodeData] {
    override def reads(json: JsValue): JsResult[NodeData] = Try {
      NodeData(
        id = (json \ "id").as[String],
        type_of_node = (json \ "type").as[String],
        data = (json \ "data").as[DataForNode],
        position = (json \ "position").as[NodePosition],
      )
    } match {
      case Failure(e) => JsError(e.getMessage)
      case Success(data) => JsSuccess(data)
    }
  }
}
object NextStepFinderForDrip {

  def getPath(
               fileName: Option[String]
             )(implicit ec: ExecutionContext): Future[String] = {
    val dir = if (isProd) {
      if(isTest){
        "/src/main/resources/puppeteer"
      }else {
        "/resources/puppeteer"
      }
    }
    else if (isDevDomain) {
      "/resources/puppeteer"
    }
    else {
      "/src/main/resources/puppeteer"
    }
    CommandExecutor.runCommandInBash("pwd")
      .map(res => {
        val path =if(fileName.isDefined) {
          s"${res.output.trim}$dir/${fileName.get}"
        } else s"${res.output.trim}$dir"
//        println(s"path:::: $path")
        path
      })
  }
  private def getAllConditions(conditionsWithLabels: List[String]): List[CampaignStepCondition] = {
    conditionsWithLabels.map(cl => {
      val conditionWithLabel = cl.split("___")
      if (conditionWithLabel.last == "yes") {
        CampaignStepCondition.fromString(conditionWithLabel.head).get
      } else if(conditionWithLabel.last == "until") {
        CampaignStepCondition.fromString(conditionWithLabel.head, is_within = false).get
      }
      else if (conditionWithLabel.last == "no") {
        CampaignStepCondition.fromString(conditionWithLabel.head).get.getOppositeCondition
      }
      else {
        CampaignStepCondition.NoCondition
      }
    })
  }

  private def parseNextStepsWithCondition(cmdRes: CommandExecutionResult): Future[List[ChildStepIdWithCondition]] = {
//    println(outputString)

    if (cmdRes.exitValue != 0) {

      Future.failed(
        new Exception(s"Failed to to parse next steps with condition. cmdRes: $cmdRes.")
      )

    } else {

      val outputString = cmdRes.output

      val children = outputString.split("\n").filter(_.startsWith("stepWithCondition_____"))
      val res = children.map(
          child => {
            val childStepIdWithCondition = child.replace("stepWithCondition_____", "").split("____")

            ChildStepIdWithCondition(
              child_step_id = childStepIdWithCondition.head.toLong,
              condition = getAllConditions(childStepIdWithCondition.drop(1).toList)
            )
          }
        )
        .toList

      //    println(res)
     Future.successful(res)
    }
  }

  def getHeadStepsWithCondition(
                                 edges: List[JsValue],
                                 nodes: List[JsValue],
                                 head_node_id: String,
//                                 pid: Long,
//                                 cid: Long,
//                                 from: String
                               )(using ec: ExecutionContext, Logger: Option[SRLogger] = None): Future[List[ChildStepIdWithCondition]] = {
//    println(s"getting head step id for pid - $pid, cid - $cid, from method - $from head_node_id - $head_node_id")
    val nodeWithoutBulk = removeBulkFromNode(nodes = nodes)
getPath(fileName = None)
  .flatMap(basePath => {
    getPath(
      fileName = Some(AppConfig.getHeadStepsNodeFileName)
    ).flatMap(path => {
      val command = s"node $path '[${nodeWithoutBulk.map(n => Json.stringify(n)).mkString(",")}]' '[${edges.map(e => Json.stringify(e)).mkString(",")}]' '$head_node_id' '${basePath}'"
      //    println(command)

//      if(Logger.isDefined) {
//        Logger.get.doNotTruncate(s"[drip_debug_Balaji] getHeadStepsWithCondition command: $command")
//      }

      CommandExecutor.runCommandInBash(command)
        .flatMap(res => {
          parseNextStepsWithCondition(cmdRes = res)
        })
    })
  })



  }

  // Runs the js file stored in coldemail/src/main/resources/puppeteer/get-child-steps.js with arguments
  def findNextStep(
                    edges: List[JsValue],
                    nodes: List[JsValue],
                    parent_step_id: Long
                  )(implicit ec: ExecutionContext): Future[List[ChildStepIdWithCondition]] = {
    // command will look something like this:
    // node <path-of-(get-child-steps.js)> '[{...}, {...}]' '[{...}, {...}]' '<parent_step_id>'
    val nodeWithoutBulk = removeBulkFromNode(nodes = nodes)

//    println(s"nodeWithoutBulk ----- $nodeWithoutBulk")

    getPath(
      fileName = Some(AppConfig.getChildStepsNodeFileName)
    ).flatMap(path => {
      val command = s"node ${path} '[${nodeWithoutBulk.map(n => Json.stringify(n)).mkString(",")}]' '[${edges.map(e => Json.stringify(e)).mkString(",")}]' '$parent_step_id' ' '"
      //    println(command)
      CommandExecutor.runCommandInBash(command)
        .flatMap(res => {
          parseNextStepsWithCondition(cmdRes = res)
        })
    })
  }

  def getLastSteps(
                    head_node_id: String,
                    edges: List[JsValue]
                  )(implicit ec: ExecutionContext, Logger: SRLogger): Future[List[Long]] = {

    // FIXME Drip (Tests): Figure out how to pass dynamic path for js file that would work in every environment.
    getPath(
      fileName = Some(AppConfig.getLastStepsNodeFileName)
    ).flatMap(path => {
      val command = s"node ${path} '[${edges.map(e => Json.stringify(e)).mkString(",")}]' '$head_node_id'"

//      Logger.debug(s"[NextStepFinderForDrip.getLastSteps] command : $command")

      CommandExecutor.runCommandInBash(command)
        .map(res => {
//            print(res.output)
//          Logger.debug(s"[NextStepFinderForDrip.getLastSteps] CommandExecutor.runCommandInBash.res : ${res.output}")

          val finalRes = res.output.split("\n")
            .find(_.startsWith("last_nodes_____"))
            .map(_.replace("last_nodes_____", "").split("_").toList)
            .getOrElse(List())
            .map(_.toLong)

//          Logger.debug(s"[NextStepFinderForDrip.getLastSteps] finalRes : $finalRes")

          finalRes
        })
    })

  }

  def getDuplicateCampaign(
                            old_head_node_id: String,
                            old_edges: List[JsValue],
                            old_nodes: List[JsValue],
                            ordered_step_ids_for_old_campaign: List[Long],
                            ordered_step_ids_for_new_campaign: List[Long]
                          )(implicit ec: ExecutionContext, Logger: SRLogger): Future[DripCampaignData] = {

    for {

      new_head_node_id <- getDuplicateCampaignDataParse(
        string = s"\"$old_head_node_id\"",
        ordered_step_ids_for_old_campaign = ordered_step_ids_for_old_campaign,
        ordered_step_ids_for_new_campaign = ordered_step_ids_for_new_campaign
      ).map(data => data
      .replace('"', ' ').trim)
      new_edges <- {
        val data = old_edges.map{edge =>
          getDuplicateCampaignDataParse(
            string = edge.toString,
            ordered_step_ids_for_old_campaign = ordered_step_ids_for_old_campaign,
            ordered_step_ids_for_new_campaign = ordered_step_ids_for_new_campaign
          ).map(data => Json.parse(data))
        }
        Future.sequence(data)
      }

      new_nodes <- {
        val data = old_nodes.map{node =>
          getDuplicateCampaignDataParse(
            string = node.toString,
            ordered_step_ids_for_old_campaign = ordered_step_ids_for_old_campaign,
            ordered_step_ids_for_new_campaign = ordered_step_ids_for_new_campaign
          ).map(a => Json.parse(a))
        }
        Future.sequence(data)
      }
    } yield {
      DripCampaignData(
        nodes = new_nodes,
        edges = new_edges,
        head_node_id = new_head_node_id
      )
    }



  }



  private def getDuplicateCampaignDataParse(
                                             string: String,
                                             ordered_step_ids_for_old_campaign: List[Long],
                                             ordered_step_ids_for_new_campaign: List[Long]
                                           )(implicit ec: ExecutionContext, Logger: SRLogger): Future[String] = {

    // FIXME Drip (Tests): Figure out how to pass dynamic path for js file that would work in every environment.
    getPath(fileName = Some(AppConfig.getDuplicateCampaignFileName))
      .flatMap(pathRes => {
        val command = s"node ${pathRes} '$string' '${ordered_step_ids_for_old_campaign.map(_.toString).reduce((a, b) => s"$a,$b")}' '${ordered_step_ids_for_new_campaign.map(_.toString).reduce((a, b) => s"$a,$b")}'"

//        Logger.debug(s"[NextStepFinderForDrip.getDuplicateCampaignDataParse] command : $command")
        CommandExecutor.runCommandInBash("node -v")
          .flatMap(resLog => {
//            Logger.debug(s"resLog ---- ${resLog.output}")
            CommandExecutor.runCommandInBash(command)
              .map(res => {
                print(res.output)
//                Logger.debug(s"[NextStepFinderForDrip.getDuplicateCampaignDataParse] CommandExecutor.runCommandInBash.res : ${res.output}")

                val finalRes = res.output.split("\n")
                  .find(_.startsWith("update_data____"))
                  .map(_.replace("update_data____", ""))
                  .getOrElse("")

//                Logger.debug(s"[NextStepFinderForDrip.getDuplicateCampaignDataParse] finalRes : $finalRes")

                finalRes
              })
          })


      })

  }
  
  def removeBulkFromNode(
                        nodes: Seq[JsValue]
                        ): Seq[JsValue] = {
    nodes.map(n => Json.toJson(n.validate[NodeData].get))
  }

}
