package utils.mq.channel_scheduler.channels

import scala.jdk.OptionConverters.*
import org.apache.pekko.stream.Materializer
import api.AppConfig
import api.accounts.models.OrgId
import api.accounts.service.AccountOrgBillingRelatedService
import io.sr.billing_common.models.PlanID
import api.accounts.{Account, AccountService, AccountWarningCodeType, OrganizationWithCurrentData, TeamId}
import api.calendar_app.CalendarAppService
import api.calendar_app.models.CalendarAccountData
import api.campaigns.models.CampaignStepData.getSubjectAndBodyFromStepData
import api.campaigns.models.CampaignType.Drip
import api.campaigns.models.{CampaignSetNextToBeScheduledAtData, CampaignStepData, CampaignStepType, CampaignStepsStructure, CampaignType, CampaignTypeData, CurrentStepStatusForScheduler, CurrentStepStatusForSchedulerData}
import api.columns.{ColumnDefsProspectsDetails, InternalMergeTagValuesForProspect, MagicColumnResponse, ProspectColGenStatus, ProspectColumnDef}
import api.campaigns.{CPCompleted, Campaign, CampaignEditedPreviewEmailDAO, CampaignIdAndTeamId, CampaignProspectDAO, CampaignProspectUpdateScheduleStatus, CampaignStepDAO, CampaignStepVariantDAO, CampaignStepVariantForScheduling, CampaignStepWithChildren, UpdateNextScheduleAtData, models}
import api.campaigns.services.CampaignProspectSchedulerService.getCurrentSecondOfDay
import api.campaigns.services.{CampaignId, CampaignProspectService, CampaignService, CampaignsMissingMergeTagService}
import api.emails.{CampaignProspectStepScheduleLogDataForIgnore, CampaignProspectStepScheduleLogsDAO, RejectionReasonForCampaignProspectStepSchedule}
import org.joda.time.{DateTime, DateTimeZone, Days, Duration}
import sr_scheduler.models.{CampaignForScheduling, CampaignWarmupSetting, ChannelData, ChannelDataForScheduling, ChannelType, ScheduledProspectsCountForCampaign, SelectedCalendarData}
import api.tasks.models.{NewTask, TaskCreatedVia, TaskData, TaskPriority, TaskStatus, TaskType}
import api.tasks.pgDao.TaskPgDAO
import api.tasks.services.{CreateTaskError, TaskService}
import eventframework.ProspectObject
import utils.email.{EmailHelper, EmailServiceCompanion}
import utils.mq.channel_scheduler.{ChannelSchedulerService, FetchCampaignStepsData, LastSentStepData, MqCampaignSchedulingMetadataMigration, SchedulerMapStepIdAndDelay}
import utils.templating.TemplateService
import utils.{Helpers, SRLogger, SrLoggerTrait}
import play.api.libs.ws.WSClient
import utils.mq.webhook.MQWebhookCompleted
import utils.shuffle.SrShuffleUtils
import utils.timezones.TimezoneUtils
import api.prospects.ProspectAccount
import api.prospects.models.{ProspectDataForChannelScheduling, ProspectId, ProspectTouchedType}
import org.apache.pekko.actor.ActorSystem
import play.api.libs.json.JsValue
import sr_scheduler.models.CampaignForScheduling.CampaignEmailSettingForScheduler
import sr_scheduler.models.ChannelType.getChannelName
import utils.cache_utils.SrRedisKeyRegister
import utils.cache_utils.model.CacheIdKeyForLock
import utils.cache_utils.service.SrRedisSimpleLockServiceV2
import utils.email_notification.service.{EmailNotificationDelayNotPassedException, EmailNotificationService}

import scala.concurrent.{Await, ExecutionContext, Future}
import scala.util.{Failure, Random, Success, Try}
import utils.helpers.LogHelpers
import utils.mq.channel_scheduler.channels.model.{CampaignRejectedForSchedulingReason, NextCheckForSchedulingIntervalType, SchedulerSteps}
import utils_deploy.rolling_updates.models.{SrRollingUpdateCohort, SrRollingUpdateFeature}
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService

import scala.collection.mutable.ArrayBuffer
import scala.concurrent.duration.DurationInt

case class PendingOrMissingMergeTags(
                                      missingMergeTags: List[String],
                                      pendingMagicMergeTags: List[String],
                                      skipSchedulingProspect: Boolean,
                                    )

case class ChannelId(
                      id: String,
                    )

case class CampaignStepContent(
                                body: String,
                                subject: String,
                                notes: String
                              )

case class TasksDoneOrScheduledFromCampaignToday(
                                                  firstSteps: Int,
                                                  followUpSteps: Int
                                                )

case class ScheduleTasksData(
                              saved_tasks_count: Int,
                              latest_task_scheduled_at: Option[DateTime],
                              reached_scheduler_step: SchedulerSteps
                            )

case class OverallAndFetchLimits(
                                  fetchLimitForFollowup: Int,
                                  fetchLimitForFirstStep: Int,
                                  overallFetchLimit: Int,
                                  overallDailyQuota: Int
                                )


trait ChannelSchedulerTrait {

  type ProspectDataForChannelSchedulingType <: ProspectDataForChannelScheduling

  type ChannelDataForSchedulingType <: ChannelDataForScheduling

  type ChannelScheduledProspectsCountForCampaignType <: ChannelScheduledProspectsCountForCampaign

  type CampaignForSchedulingType <: CampaignForScheduling
  protected val lockForCampaignsBeingScheduledNowExpiresInSeconds = 60 * 30 //locking for 30 mins

  val mqCampaignSchedulingMetadataMigration: MqCampaignSchedulingMetadataMigration

  trait ChannelScheduledProspectsCountForCampaign {
    def campaign: CampaignForSchedulingType


    // for the followup-priority (previously EmailPriority), we need these counts for every channel
    def counts: Seq[ScheduledProspectsCountForCampaign]

  }

  type ProspectsFoundByStepType = Map[CampaignStepType, ProspectsFoundForSchedulingByStepType]

  case class CampaignDetailsForScheduling(
                                           channelScheduledProspectsCountForCampaign: ChannelScheduledProspectsCountForCampaignType,
                                           fetchCampaignStepsDataResult: FetchCampaignStepsData,
                                           markedCompletedIds: Seq[CPCompleted],
                                           prospectsFoundForCampaign: ProspectsFoundByStepType,
                                           allowedTimezones: Set[String]
                                         )

  case class ProspectsFoundForSchedulingByStepType(
                                                    prospects: List[ProspectDataForChannelSchedulingType],
                                                    step_type: ChannelStepTypeDataForScheduling
                                                  )

  case class ProspectFoundForSchedulingByStepType(
                                                   prospectForScheduling: ProspectDataForChannelSchedulingType,
                                                   step_type: ChannelStepTypeDataForScheduling
                                                 )

  case class ScheduleCampaign(
                               markedCompletedIds: Seq[CPCompleted],
                               campaign: CampaignForSchedulingType,
                               stepsMappedById: Map[Long, CampaignStepWithChildren],
                               campaignStepsStructure: CampaignStepsStructure,
                               prospects: Seq[ProspectFoundForSchedulingByStepType],
                               distinctTimezones: Set[String]
                             )

  case class GenerateScheduleTaskData(
                                       currentCampaign: ScheduleCampaign,
                                       nextStep: CampaignStepWithChildren,
                                       currentProspect: ProspectFoundForSchedulingByStepType,
                                       currentVariant: CampaignStepVariantForScheduling,
                                       schedulerDateTime: DateTime,
                                     )

  case class ChannelStepTypeDataForScheduling(
                                               campaignStepType: CampaignStepType,
                                               totalScheduledForStepTypeTillNow: Int,
                                               channelStepTypeDailyLimit: Int,
                                               remainingToBeScheduledFromChannelStepType: Int,
                                               campaignStepTypeLimitHasBeenReachedForToday: Boolean,
                                               stepType: StepType
                                             )

  case class ProspectsToCreateTasksResult(
                                           foundNewProspects: List[ProspectDataForChannelSchedulingType],
                                           foundFollowUpProspects: List[ProspectDataForChannelSchedulingType],
                                         )

  case class FilteredProspectsFoundForScheduling(
                                                  prospectsFound: List[ProspectDataForChannelSchedulingType],
                                                  prospectsFoundCountFromInitialDbChecks: Int,
                                                  maxProspectIdFoundFromInitialDbChecks: Option[Long]
                                                )

  def getChannelForScheduling(
                               channelId: ChannelId,
                               teamId: TeamId,
                               logger: SrLoggerTrait
                             ): Option[ChannelDataForSchedulingType]


  def findCampaignsForSchedulingChannel(
                                         channelDataForScheduling: ChannelDataForSchedulingType,
                                         campaignService: CampaignService,
                                       )(
                                         implicit Logger: SRLogger
                                       ): Try[Seq[CampaignForSchedulingType]]


  def filterCampaignsForSchedulingChannelFilteredByAllowedTimezones(

                                                                     campaignProspectService: CampaignProspectService,
                                                                     initiallyFoundCampaignsForScheduling: Seq[CampaignForSchedulingType],

                                                                   )(

                                                                     implicit Logger: SRLogger

                                                                   ): Try[Seq[CampaignForSchedulingType]] = Try {


    initiallyFoundCampaignsForScheduling
      .filter(c => {

        val allowedTimezones = getAllowedTimezonesForCampaign(
          campaignProspectService = campaignProspectService,
          campaign = c,
        )

        allowedTimezones match {
          case Failure(exception) =>

            Logger.fatal("findCampaignsForSchedulingChannelFilteredByAllowedTimezones getAllowedTimezonesForCampaign failed", err = exception)

            // passing it anyways
            true

          case Success(allowedTzs) =>

            if (allowedTzs.isEmpty) {

              Logger.debug(s"findCampaignsForSchedulingChannelFilteredByAllowedTimezones no tzs for campaign: cid_${c.campaign_id} :: ignoring campaign")

              false

            } else {

              true
            }

        }


      })


  }

  def findCampaignsForSchedulingChannelAfterLock(
                                                  srRollingUpdateCoreService: SrRollingUpdateCoreService,
                                                  campaignProspectService: CampaignProspectService,
                                                  channelDataForScheduling: ChannelDataForSchedulingType,
                                                  campaignService: CampaignService,
                                                  channelData: ChannelData,
                                                )(
                                                  implicit Logger: SRLogger
                                                ): Try[Seq[CampaignForSchedulingType]] = {

    for {
      initiallyFoundCampaignsForScheduling: Seq[CampaignForSchedulingType] <- {

        findCampaignsForSchedulingChannel(

          channelDataForScheduling = channelDataForScheduling,
          campaignService = campaignService,

        )
      }

      campaignsForSchedulingAfterTzFilter: Seq[CampaignForSchedulingType] <- {


        filterCampaignsForSchedulingChannelFilteredByAllowedTimezones(
          campaignProspectService = campaignProspectService,
          initiallyFoundCampaignsForScheduling = initiallyFoundCampaignsForScheduling,
        )

      }

      lockedAcquiredForCampaignIds: Set[Long] <- acquireLockForCampaignsBeingScheduledNow(
        campaignIds = campaignsForSchedulingAfterTzFilter.map(c => CampaignId(c.campaign_id)).toSet,
      ).map(_.map(_.id))

    } yield {

      val rejectedCampaignIds: Set[Long] = initiallyFoundCampaignsForScheduling
        .map(_.campaign_id)
        .toSet
        .diff(lockedAcquiredForCampaignIds)

      /*
        universe : 1,3,5,7,9,11,13,15,17,19
        campaignsForSchedulingAfterTzFilter: 3, 5, 7
        outOfTzRejectedCampaignIds = universe.diff(campaignsForSchedulingAfterTzFilter) = 1, 9, 11, 13, 15, 17, 19

        lockedAcquiredForCampaignIds: 3, 7 [It must be from this set only : campaignsForSchedulingAfterTzFilter]

        lockNotAcquiredCampaignIds = universe.diff(outOfTzRejectedCampaignIds). diff(lockedAcquiredForCampaignIds)
                                   = (1,3,5,7,9,11,13,15,17,19).diff(1, 9, 11, 13, 15, 17, 19).diff(5)
                                   = (3,5,7).diff(5)
                                   = 3, 7

       */
      val universe: Set[Long] = initiallyFoundCampaignsForScheduling
        .map(_.campaign_id)
        .toSet

      val outOfTzRejectedCampaignIds: Set[Long] = universe.diff(campaignsForSchedulingAfterTzFilter.map(_.campaign_id).toSet)
      val lockNotAcquiredCampaignIds: Set[Long] = universe.diff(outOfTzRejectedCampaignIds).diff(lockedAcquiredForCampaignIds)

      val combinedRejectedCampaignIds: Set[Long] = (outOfTzRejectedCampaignIds ++ lockNotAcquiredCampaignIds)


      if (rejectedCampaignIds != combinedRejectedCampaignIds) {

        Logger.shouldNeverHappen(s"rejectedCampaignIds != combinedRejectedCampaignIds :: rejectedCampaignIds: $rejectedCampaignIds :: universe: $universe :: outOfTzRejectedCampaignIds: $outOfTzRejectedCampaignIds :: lockNotAcquiredCampaignIds: $lockNotAcquiredCampaignIds :: combinedRejectedCampaignIds: $combinedRejectedCampaignIds")

      }

      /*
      if (rejectedCampaignIds.nonEmpty) {

        updateScheduleAtForCampaignsRejectedByFilters(

          channelDataForScheduling = channelDataForScheduling,
          campaignService = campaignService,
          rejectedCampaignIds = rejectedCampaignIds.map(CampaignId(_)),
          teamId = TeamId(channelDataForScheduling.channelTeamId),
          channelData = channelData


        ) match {

          case Success(_) =>

            Logger.info(s"findCampaignsForSchedulingChannelAfterLock: updateScheduleAtForCampaignsRejectedByFilters done for ${rejectedCampaignIds.map(id => s"cid_$id")}")

          case Failure(exception) =>

            Logger.fatal("findCampaignsForSchedulingChannelAfterLock: updateScheduleAtForCampaignsRejectedByFilters failed to update", err = exception)

        }
      }

       */

      if (outOfTzRejectedCampaignIds.nonEmpty) {

        val logMsg = s"findCampaignsForSchedulingChannelAfterLock: updateScheduleAtForCampaignsRejectedByFilters reason: CurrentTimeNotInCampaignTimezone for ${outOfTzRejectedCampaignIds.map(id => s"cid_$id")}"

        val outOfTzRejectedCampaigns: Seq[CampaignForSchedulingType] = initiallyFoundCampaignsForScheduling
          .filter(c => {

            outOfTzRejectedCampaignIds.contains(c.campaign_id)

          })

        updateScheduleAtForCampaignsRejectedByFilters(
          channelDataForScheduling = channelDataForScheduling,
          campaignService = campaignService,
          campaignProspectService = campaignProspectService,
          rejectedCampaigns = outOfTzRejectedCampaigns,
          teamId = TeamId(channelDataForScheduling.channelTeamId),
          channelData = channelData,
          rejectionReason = CampaignRejectedForSchedulingReason.CurrentTimeNotInCampaignTimezone
        ) match {
          case Success(_) =>
            Logger.info(s"done $logMsg")
          case Failure(exception) =>
            Logger.fatal(s"failed to update : $logMsg", err = exception)
        }

      }

      if (lockNotAcquiredCampaignIds.nonEmpty) {

        val logMsg = s"findCampaignsForSchedulingChannelAfterLock: updateScheduleAtForCampaignsRejectedByFilters reason: CouldNotAcquireCampaignLock for ${lockNotAcquiredCampaignIds.map(id => s"cid_$id")}"

        val lockNotAcquiredCampaigns: Seq[CampaignForSchedulingType] = initiallyFoundCampaignsForScheduling
          .filter(c => {

            lockNotAcquiredCampaignIds.contains(c.campaign_id)

          })

        updateScheduleAtForCampaignsRejectedByFilters(
          channelDataForScheduling = channelDataForScheduling,
          campaignService = campaignService,
          campaignProspectService = campaignProspectService,
          rejectedCampaigns = lockNotAcquiredCampaigns,
          teamId = TeamId(channelDataForScheduling.channelTeamId),
          channelData = channelData,
          rejectionReason = CampaignRejectedForSchedulingReason.CouldNotAcquireCampaignLock
        ) match {
          case Success(_) =>
            Logger.info(s"done $logMsg")
          case Failure(exception) =>
            Logger.fatal(s"failed to update $logMsg", err = exception)
        }

      }


      if (channelDataForScheduling.channelType == ChannelType.EmailChannel) {
        Logger.debug(s"campaigns selected for scheduling channel type- ${channelDataForScheduling.channelType}  selected campaigns- $lockedAcquiredForCampaignIds rejected campaigns - ${rejectedCampaignIds} :: outOfTzRejectedCampaignIds: $outOfTzRejectedCampaignIds :: lockNotAcquiredCampaignIds: $lockNotAcquiredCampaignIds :: allCampaignIds: $universe")
      }

      campaignsForSchedulingAfterTzFilter.filter(c => lockedAcquiredForCampaignIds.contains(c.campaign_id))
    }
  }


  def updateLastScheduledForChannelAndSetInQueueForSchedulingToFalse(
                                                                      channelDataForScheduling: ChannelDataForSchedulingType,
                                                                      latestScheduledAt: Option[DateTime]

                                                                    )(
                                                                      implicit Logger: SRLogger
                                                                    ): Try[Int]

  def releaseLockForCampaignsBeingScheduledNow(campaignIds: Seq[CampaignId])(
    implicit Logger: SRLogger
  ): Try[Map[Boolean, Set[CampaignId]]] = Success(Map(true -> campaignIds.toSet))

  private def releaseLockForCampaignsBeingScheduledNowAndLog(campaignIds: Seq[CampaignId], startTime: DateTime)(
    implicit Logger: SRLogger
  ): Try[Map[Boolean, Set[CampaignId]]] = releaseLockForCampaignsBeingScheduledNow(campaignIds = campaignIds) match {
    case Success(value) =>
      value.foreach { case (k, v) =>
        if (!k && v.nonEmpty) {
          val releaseLockTime = DateTime.now()
          val timeTakenFromLockToRelease = new Duration(startTime, releaseLockTime).toStandardSeconds.getSeconds
          Logger.fatal(s"Failed to releaseLockForCampaignsBeingScheduledNow channel type - ${ChannelType.EmailChannel}  for campaignIds - ${v} time taken - $timeTakenFromLockToRelease")
        }
      }
      Success(value)
    case Failure(exception) =>
      Logger.fatal(s"Failed releaseLockForCampaignsBeingScheduledNow channel type -  ${ChannelType.EmailChannel} campaignId - $campaignIds", exception)
      Failure(exception)
  }

  def acquireLockForCampaignsBeingScheduledNow(campaignIds: Set[CampaignId])(
    implicit Logger: SRLogger
  ): Try[Set[CampaignId]] = Success(campaignIds) // if we are not adding to redis, we don't need to filter out already locked campaigns, so returning all the ids



  // MULTICHANNEL IMPLEMENTATION NOTE: ENSURE THAT ITS A EXHAUSTIVE MAP (ALL STEP TYPES) RETURNED FROM THIS FUNCTION
  // If lets say no LinkedinMessage tasks were scheduled and we do not send LinkedinMessage -> 0
  // in the below map, then the campaign will not scheduled any LinkedinMessage steps going forwards
  // so its absolutely necessary for each stepType to be present in this Map
  // CHECK OTHER IMPLEMENTATIONS -> FOLLOW THE SAME PATTERN

  // NOTE: This method is overridden only in EmailChannelScheduler
  def getSentOrScheduledProspectsCountForChannel(
                                                  channelDataForScheduling: ChannelDataForSchedulingType,
                                                  campaignProspectDAO: CampaignProspectDAO,
                                                  taskDAO: TaskPgDAO
                                                )(
                                                  implicit logger: SRLogger
                                                ): Try[Map[CampaignStepType, StepTypeAndCount]] = {

    logger.info("\n\ngetSentOrScheduledProspectsCountForChannel")


    // we are not passing "[AbstractChannelStepType]" in other schedulers because scala is able to type-infer the abstract type as well
    taskDAO.getSentOrScheduledProspectsCount(
        channelType = channelDataForScheduling.channelType,
        ownerAccountId = channelDataForScheduling.channelOwnerAccountId,
        account_timezone = channelDataForScheduling.account_timezone
      )
      .map(countMap => {


        // MULTICHANNEL IMPLEMENTATION NOTE: ENSURE THAT ITS A EXHAUSTIVE MAP BELOW
        // If lets say no LinkedinMessage tasks were scheduled and we do not send LinkedinMessage -> 0
        // in the below map, then the campaign will not scheduled any LinkedinMessage steps going forwards
        // so its absolutely necessary for each stepType to be present in this Map
        Map(

          CampaignStepType.LinkedinConnectionRequest -> StepTypeAndCount(StepType(CampaignStepType.LinkedinConnectionRequest.toKey),
            Count(countMap.getOrElse(CampaignStepType.LinkedinConnectionRequest, 0))),

          CampaignStepType.LinkedinInmail -> StepTypeAndCount(StepType(CampaignStepType.LinkedinInmail.toKey),
            Count(countMap.getOrElse(CampaignStepType.LinkedinInmail, 0))),

          CampaignStepType.LinkedinViewProfile -> StepTypeAndCount(StepType(CampaignStepType.LinkedinViewProfile.toKey),
            Count(countMap.getOrElse(CampaignStepType.LinkedinViewProfile, 0))),

          CampaignStepType.LinkedinMessage -> StepTypeAndCount(StepType(CampaignStepType.LinkedinMessage.toKey),
            Count(countMap.getOrElse(CampaignStepType.LinkedinMessage, 0))),

          CampaignStepType.AutoLinkedinInmail -> StepTypeAndCount(StepType(CampaignStepType.AutoLinkedinInmail.toKey),
            Count(countMap.getOrElse(CampaignStepType.AutoLinkedinInmail, 0))),

          CampaignStepType.AutoLinkedinMessage -> StepTypeAndCount(StepType(CampaignStepType.AutoLinkedinMessage.toKey),
            Count(countMap.getOrElse(CampaignStepType.AutoLinkedinMessage, 0))),

          CampaignStepType.AutoLinkedinConnectionRequest -> StepTypeAndCount(StepType(CampaignStepType.AutoLinkedinConnectionRequest.toKey),
            Count(countMap.getOrElse(CampaignStepType.AutoLinkedinConnectionRequest, 0))),

          CampaignStepType.AutoLinkedinViewProfile -> StepTypeAndCount(StepType(CampaignStepType.AutoLinkedinViewProfile.toKey),
            Count(countMap.getOrElse(CampaignStepType.AutoLinkedinViewProfile, 0))),

          CampaignStepType.WhatsappMessage -> StepTypeAndCount(StepType(CampaignStepType.WhatsappMessage.toKey),
            Count(countMap.getOrElse(CampaignStepType.WhatsappMessage, 0))),

          CampaignStepType.GeneralTask -> StepTypeAndCount(StepType(CampaignStepType.GeneralTask.toKey),
            Count(countMap.getOrElse(CampaignStepType.GeneralTask, 0))),

          CampaignStepType.ManualEmailStep -> StepTypeAndCount(StepType(CampaignStepType.ManualEmailStep.toKey),
            Count(countMap.getOrElse(CampaignStepType.ManualEmailStep, 0))),

          CampaignStepType.ManualEmailMagicContent -> StepTypeAndCount(StepType(CampaignStepType.ManualEmailMagicContent.toKey),
            Count(countMap.getOrElse(CampaignStepType.ManualEmailMagicContent, 0))),

          CampaignStepType.SmsMessage -> StepTypeAndCount(StepType(CampaignStepType.SmsMessage.toKey),
            Count(countMap.getOrElse(CampaignStepType.SmsMessage, 0))),

          CampaignStepType.CallStep -> StepTypeAndCount(StepType(CampaignStepType.CallStep.toKey),
            Count(countMap.getOrElse(CampaignStepType.CallStep, 0))),

          // We won't get Auto Email Step Count because auto emails are not stored in Tasks Table
          CampaignStepType.AutoEmailStep -> StepTypeAndCount(StepType(CampaignStepType.AutoEmailStep.toKey),
            Count(countMap.getOrElse(CampaignStepType.AutoEmailStep, 0))),

          CampaignStepType.AutoEmailMagicContent -> StepTypeAndCount(StepType(CampaignStepType.AutoEmailMagicContent.toKey),
            Count(countMap.getOrElse(CampaignStepType.AutoEmailMagicContent, 0))),

        )

      })

  }


  def calculateMaxToBeScheduledForNextHour(
                                            channelDataForScheduling: ChannelDataForSchedulingType,
                                            scheduleFromTime: DateTime,
                                            scheduleTillTime: DateTime,
                                          ): Int

  def getLatestTaskScheduledAt(
                                channelTasks: Vector[GenerateScheduleTaskData]
                              ): Option[DateTime]

  def getScheduledProspectsCountForCampaign(
                                             channelDataForScheduling: ChannelDataForSchedulingType,
                                             campaigns: Seq[CampaignForSchedulingType],
                                             campaignProspectDAO: CampaignProspectDAO,
                                             stepTypes: Seq[CampaignStepType]
                                           )(
                                             implicit logger: SRLogger
                                           ): Try[Seq[ChannelScheduledProspectsCountForCampaignType]]


  def countTotalTasksDoneOrScheduledFromCampaignToday(
                                                       channelScheduledProspectsCountForCampaign: ChannelScheduledProspectsCountForCampaignType,
                                                       campaignStepType: CampaignStepType
                                                     )(
                                                       implicit Logger: SRLogger
                                                     ): TasksDoneOrScheduledFromCampaignToday = {

    val countDataForACampaign = channelScheduledProspectsCountForCampaign


    val c = countDataForACampaign.campaign


    /*
     MULTICHANNEL_REFACTOR:
     Two types of priority:

     1. Step Priority == Task Priority


     2. Opening/Follow-up Priority:
     if we decide to go with this Priority: this has to be modified - we can also drop this Priority feature for now

     */
    val campaignCounts: ScheduledProspectsCountForCampaign = ChannelSchedulerTrait.findScheduledProspectsCountForStepType(
      campaignId = c.campaign_id,
      campaignStepType = campaignStepType,
      campaignCounts = countDataForACampaign.counts
    )

    val newCampaignSentCount = campaignCounts.newCount

    val followupCampaignSentCount = campaignCounts.followupCount

    val newCampaignCountNotSent = campaignCounts.newCountNotSent

    val followupCampaignCountNotSent = campaignCounts.followupCountNotSent


    // the minimum delay between steps is 1 day. So a prospect can get max one email from a campaign in a day.

    TasksDoneOrScheduledFromCampaignToday(
      firstSteps = newCampaignSentCount + newCampaignCountNotSent,
      followUpSteps = followupCampaignSentCount + followupCampaignCountNotSent
    )

  }

  final def countTasksToBeScheduledByStepType(
                                               //tasks: Vector[GenerateScheduleTaskData],
    tasks: ArrayBuffer[GenerateScheduleTaskData],
                                               stepType: CampaignStepType
                                             ): Int = {

    tasks.count(_.nextStep.step_type == stepType)

  }


  final def flattenProspectsFoundForSchedulingByStepType(

                                                          prospectsFoundByStepType: ProspectsFoundByStepType

                                                        ): Seq[ProspectFoundForSchedulingByStepType] = {

    prospectsFoundByStepType
      .flatMap { case (stepType, prospectsFoundForSchedulingByStepType: ProspectsFoundForSchedulingByStepType) =>

        prospectsFoundForSchedulingByStepType
          .prospects
          .map(p => {

            ProspectFoundForSchedulingByStepType(
              prospectForScheduling = p,
              step_type = prospectsFoundForSchedulingByStepType.step_type
            )
          })


      }
      .toSeq

  }

  // checks if we have already hit the step Type limit for today, and returns if we can continue scheduling more tasks for this stepType
  def checkIfCanScheduleMoreForStepType(

                                         //tasksToBeScheduled: Vector[GenerateScheduleTaskData],
    tasksToBeScheduled: ArrayBuffer[GenerateScheduleTaskData],
                                         nextStep: CampaignStepWithChildren,
                                         channelStepTypeDataForScheduling: Map[CampaignStepType, ChannelStepTypeDataForScheduling],
                                         logger: SRLogger,

                                       ): Boolean = {


    // db call to check how many prospects are in pending_approval state ->
    // we will subtract them from the limits,

    val tasksReadyToBeScheduledForStepType: Int = countTasksToBeScheduledByStepType(
      tasks = tasksToBeScheduled,
      stepType = nextStep.step_type
    )

    val remainingToBeScheduledFromChannelStepType: Int = channelStepTypeDataForScheduling
      .get(key = nextStep.step_type)
      .map(_.remainingToBeScheduledFromChannelStepType)
      .getOrElse(0)

    val canScheduleMore = tasksReadyToBeScheduledForStepType < remainingToBeScheduledFromChannelStepType

    if (!canScheduleMore) {

      logger.info(s"can not schedule more for step type: ${nextStep.step_type} because limit hit: tasksReadyToBeScheduledForStepType: $tasksReadyToBeScheduledForStepType :: remainingToBeScheduledFromChannelStepType: $remainingToBeScheduledFromChannelStepType")

    }

    canScheduleMore

  }




  def getChannelTasksToBeScheduled(
                                    channelDataForScheduling: ChannelDataForSchedulingType,
                                    channelStepTypeDataForScheduling: Map[CampaignStepType, ChannelStepTypeDataForScheduling],
                                    campaignListForScheduling: Seq[ScheduleCampaign],
                                    scheduleFromTime: DateTime,
                                    scheduleTillTime: DateTime,
                                    maxToBeScheduledForNextHour: Int,
                                    srShuffleUtils: SrShuffleUtils,
                                    emailServiceCompanion: EmailServiceCompanion, // FIXME Multichannel LATER: This is email specific
                                    templateService: TemplateService,
                                    campaignProspectDAO: CampaignProspectDAO,
                                    campaignEditedPreviewEmailDAO: CampaignEditedPreviewEmailDAO, // FIXME Multichannel LATER: This is email specific
                                    campaignsMissingMergeTagService: CampaignsMissingMergeTagService,
                                    campaignProspectService: CampaignProspectService,
                                    isCalendarEnabled: Boolean,
                                    calendarAccountData: Option[CalendarAccountData],
                                    Logger: SRLogger

                                  )(using logger: SRLogger,
                                    ec: ExecutionContext): Try[Vector[GenerateScheduleTaskData]] = Try {


    // we are calculating the overall remaining-limit as additional check on the while-loop
    val remainingToBeScheduledFromChannel: Int = channelStepTypeDataForScheduling
      .map(_._2.remainingToBeScheduledFromChannelStepType)
      .sum

    //    Logger.debug("Entry getChannelTasksToBeScheduled")
    Logger.info(s"getChannelTasksToBeScheduled: $maxToBeScheduledForNextHour :: $remainingToBeScheduledFromChannel")


    // shuffle the list of campaigns
    //
    // when users run >10-15 campaigns from the same email account,
    // the last campaigns get ignored and do not send any emails
    // shuffling the list of campaigns, will help get emails sent
    // from last campaigns
    // nxd notes: we have seen from data there can be upto 53 campaigns per inbox
    var campaigns: Seq[ScheduleCampaign] = srShuffleUtils.shuffleList(campaignListForScheduling)


    var delayInSec: Int = 0


    // MULTICHANNEL_REFACTOR: EmailScheduledNew should become a trait type
    //var tasksToBeScheduled: Vector[GenerateScheduleTaskData] = Vector[GenerateScheduleTaskData]()
    var tasksToBeScheduled: ArrayBuffer[GenerateScheduleTaskData] = ArrayBuffer[GenerateScheduleTaskData]()

    val totalCampaigns = campaigns.size

    var currentCampaignIndex = 0
    var totalProspectsChecked = 0

    /*
      case class ScheduleCampaign(
                               markedCompletedIds: Seq[CPCompleted],
                               campaign: CampaignForSchedulingType,
                               stepsMappedById: Map[Long, CampaignStepWithChildren],
                               orderedStepIds: Vector[Long],
                               prospects: Seq[ProspectFoundForSchedulingByStepType],
                             )
    */

    /*
    Campaignid : 1
    AutoEmailStep - [1,3,5,7]
                     ^
    ViewProfileLinkedInStep - [ 2,4,6,8]
    ConnectProfileLinkedInStep - [ 9,11,13,17]
    SMSStep [ 10,12,14,16]

    Campaignid : 2
    AutoEmailStep - [11,13,15,17]
                         ^
    ViewProfileLinkedInStep - [ 12,14,16,18]
                                       ^
    ConnectProfileLinkedInStep - [ 19,111,113,117]
                                   ^
    SMSStep [ 110,112,114,116]
                ^
    */

    // BEGIN::: campaignProspectsMap and campaignProspectIdsMap :::: both should be constructed from same data source (campaigns)
    // Map[campaign_id, list[prospects flattened]]
    //val campaignProspectsMap: Map[Long, Seq[ProspectFoundForSchedulingByStepType]] = campaigns
    val campaignProspectsMap: Map[Long, Seq[ProspectFoundForSchedulingByStepType]] = campaigns
      .map(c =>
        c.campaign.campaign_id -> c.prospects
      )
      .toMap

    val campaignProspectIdsMap: Map[Long, scala.collection.mutable.LinkedHashSet[Long]] = campaigns
      .map(c =>
        c.campaign.campaign_id -> c.prospects.map(_.prospectForScheduling.prospect.id)
      )
      .toMap
      .map((k, v) => k -> scala.collection.mutable.LinkedHashSet(v*))
    // END::: campaignProspectsMap and campaignProspectIdsMap :::: both should be constructed from same data source (campaigns)

    // Map[campaign_id, list[prospects flattened size]]
    val totalProspects = campaignProspectsMap.map(_._2.size).sum

    var prospectsWithMissingMergeTags: Set[Long] = Set()

    //    Logger.info(s"campaignProspectsMap: $campaignProspectsMap")

    // checkedTillProspectIndexInsideCurrentCampaign -> in the currentCampaign -> prospects, this is the index of checked prospects who are eligible to be sent emails from the campaign

    while (
      (totalProspectsChecked < totalProspects) // invariant
        && (tasksToBeScheduled.size < maxToBeScheduledForNextHour) // invariant
        && (tasksToBeScheduled.size < remainingToBeScheduledFromChannel) // invariant
        && (scheduleFromTime.plusSeconds(delayInSec)).isBefore(scheduleTillTime) // invariant
    ) {

      val currentCampaign = campaigns(currentCampaignIndex)
      val currentCampaignId = currentCampaign.campaign.campaign_id

      //      Logger.info(s"currentCampaignId: $currentCampaignId")



      //      Logger.info(s"\n\ncurrentProspectOpt: $currentProspectOpt")


      val currentProspectOpt: Option[ProspectFoundForSchedulingByStepType] = {


        val prospectsInCurrentCampaign: Seq[ProspectFoundForSchedulingByStepType] = campaignProspectsMap(currentCampaignId)

        val prospectIdsInCurrentCampaign: scala.collection.mutable.LinkedHashSet[Long] = campaignProspectIdsMap(currentCampaignId)

        //      Logger.info(s"prospectsInCurrentCampaign: $prospectsInCurrentCampaign\n\n :: tasksToBeScheduled : $tasksToBeScheduled")
        // MULTICAMPAIGN DONE: (IGNORING FOR NOW) FOR PROSPECT: same prospect should not be sent emails simultaneously

        /**
         * 5-Apr-2025: Scheduler optimization
         * Earlier we were passing the entire seq of the prospects for the campaign to this function
         * As the tasks were getting created we had to skip more and more prospects to find one which qualified for the next task
         * because they were already present in the tasksToBeScheduled list since they were scheduled in the previous iteration of this loop
         *
         * Now we are using a mutable set to hold all the candidate prospects which qualify for the campaign
         * Every time a prospect evaluated for the campaign we remove it from the set hence every time we are qualifying a prospect for campaign
         * it does not have to skipped over
         *
         */

        val currentProspectIdOpt: Option[Long] = prospectIdsInCurrentCampaign
          .find(prospectId => {
            !prospectsWithMissingMergeTags.contains(prospectId)
          })


        if(currentProspectIdOpt.isDefined) {

          // WARNING!: mutation
          /** 5-Apr-2025: if we found the prospect whether it was a merge tag error or a task was created
           * remove it from prospectsInCurrentCampaign
           commented out line and the next line are equivalent
           prospectsInCurrentCampaign is a reference to campaignProspectIdsMap(currentCampaignId)
           so removing from either one is same thing.
           We are mutating from global variable to show that the side effect actually happens on global variable
           // prospectIdsInCurrentCampaign.remove(currentProspectIdOpt.get)
           */
          campaignProspectIdsMap(currentCampaignId).remove(currentProspectIdOpt.get)

          prospectsInCurrentCampaign.find(_.prospectForScheduling.prospect.id == currentProspectIdOpt.get)

        } else None
      }

      if (currentProspectOpt.isDefined) {

        val currentProspect = currentProspectOpt.get

        // WARNING!: mutation
        totalProspectsChecked = totalProspectsChecked + 1

        //        Logger.info(s"\n\ncurrentProspect: $currentProspect")

        val lastSentSteps = if (currentCampaign.campaignStepsStructure.campaign_type == Drip) {
          campaignProspectDAO.getPreviouslySentStepsForProspect(
            prospectId = ProspectId(currentProspect.prospectForScheduling.prospect.id),
            teamId = TeamId(currentProspect.prospectForScheduling.prospect.team_id),
            campaignId = CampaignId(currentCampaignId)
          ).get
        } else List()

        val isProspectConnectedToLinkedin: Boolean = if (currentCampaign.campaignStepsStructure.campaign_type == Drip){
          campaignProspectService.getLinkedInProfileConnectedUuid(
            campaignId = CampaignId(currentCampaignId),
            linkedinProfileUrl = currentProspect.prospectForScheduling.prospect.linkedin_url,
            teamId =TeamId(currentProspect.prospectForScheduling.prospect.team_id)
          ).get.isDefined
        }else {
          false
        }

        ChannelSchedulerService.getNextStepId(
          currentStepId = currentProspect.prospectForScheduling.current_step_id,
          prospectObjectOpt = Some(currentProspect.prospectForScheduling.prospect),
          campaignStepsStructure = currentCampaign.campaignStepsStructure,
          lastSentSteps = lastSentSteps,
          isProspectConnectedToLinkedin = isProspectConnectedToLinkedin,
          stepsMappedById = currentCampaign.stepsMappedById
        ) match {

          case Left(value) =>

            currentCampaign.campaign.campaign_type_data match {

              case CampaignTypeData.MultiChannelCampaignData(_)
                   | CampaignTypeData.EmailChannelData(_)
                   | CampaignTypeData.MagicContentData(_) =>

                // For email only, multichannel and magic campaigns, preserving the old behavior.

                campaignProspectService.addIgnoreLogAndUpdateNextCheckForSchedulingAt(
                  prospects = List((currentProspect.prospectForScheduling, DateTime.now().plusMinutes(10), value)),
                  campaignId = CampaignId(currentCampaign.campaign.campaign_id),
                  head_step_id = currentCampaign.campaign.campaign_type_data.head_id,
                  teamId = TeamId(currentProspect.prospectForScheduling.prospect.team_id),
                )

              case dcd: CampaignTypeData.DripCampaignData =>

                campaignProspectService.handleDripNextStepNotFound(
                  currentProspectForScheduling = currentProspect.prospectForScheduling,
                  currentCampaignId = CampaignId(id = currentCampaignId),
                  rejectionReason = value,
                  markCompletedAfterDays = currentCampaign.campaign.mark_completed_after_days,
                  currentDripCampaignTypeData = dcd,
                )

            }

          case Right(nextStepId) =>

            // if the status is approved that means we needt to schedule the actual email in emails_scheduled table for the current_step
            // so, we will take the current step from here

            val nextStep = currentCampaign.stepsMappedById(nextStepId)

            val canScheduleMoreForStepType: Boolean = checkIfCanScheduleMoreForStepType(

              tasksToBeScheduled = tasksToBeScheduled,
              nextStep = nextStep,
              channelStepTypeDataForScheduling = channelStepTypeDataForScheduling,
              logger = Logger,

            )

            if (!canScheduleMoreForStepType) {

              // do nothing

            } else {


              //Doing shuffle bcz in case sending 1 email per 48 hours or above, it picks first variant only by doing shuffle we can avoid first variant picking every time
              val variants = Random.shuffle(nextStep.variants)

              val currentVariant: CampaignStepVariantForScheduling = if (variants.length > 1) {

                val minCountVariant = variants.minBy(_.scheduled_count)

                val updatedStepsMappedById = currentCampaign.stepsMappedById + (nextStepId -> currentCampaign.stepsMappedById(nextStepId).copy(variants = variants.map(vari => {

                  if (vari.id == minCountVariant.id) {
                    vari.copy(scheduled_count = minCountVariant.scheduled_count + 1)
                  } else {
                    vari
                  }

                })))

                val updatedScheduleCam = currentCampaign.copy(stepsMappedById = updatedStepsMappedById)

                // WARNING!: mutation
                campaigns = campaigns.updated(currentCampaignIndex, updatedScheduleCam)

                minCountVariant

              } else {

                variants(0)

              }


              // check all parameters are there
              val absentTagsFoundSeq = checkAndReturnMissingFieldsForChannel(
                channelDataForScheduling = channelDataForScheduling,
                nextStepId = nextStepId,
                currentCampaign = currentCampaign,
                currentProspect = currentProspect.prospectForScheduling,
                currentVariant = currentVariant,
                emailServiceCompanion = emailServiceCompanion, // FIXME Multichannel LATER: This is email specific
                templateService = templateService,
                campaignProspectDAO = campaignProspectDAO,
                campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO, // FIXME Multichannel LATER: This is email specific
                campaignsMissingMergeTagService = campaignsMissingMergeTagService,
                isCalendarEnabled = isCalendarEnabled,
                calendarAccountData = calendarAccountData,
                Logger = Logger
              )


              absentTagsFoundSeq match {
                case Failure(e) =>

                  Logger.fatal(s"error while absentTagsFoundSeq  ScheduleEmailCronServiceV2: ${LogHelpers.getStackTraceAsString(e)}")

                case Success(pendingOrMissingMergeTags: PendingOrMissingMergeTags) =>

                  if (pendingOrMissingMergeTags.skipSchedulingProspect) {

                    // WARNING!: mutation
                    prospectsWithMissingMergeTags = prospectsWithMissingMergeTags ++ Set(currentProspect.prospectForScheduling.prospect.id)

                    Logger.debug("missing merge tag error, skipping")

                  } else {

                    val consecutiveTaskDelay = getConsecutiveTaskScheduleDelay(
                      channelDataForScheduling = channelDataForScheduling
                    )

                    // WARNING!: mutation
                    delayInSec += consecutiveTaskDelay

                    val schedulerDateTime: DateTime = scheduleFromTime.plusSeconds(delayInSec)

                    //                    Logger.info(s"(campaign ${currentCampaign.campaign.campaign_id}) getChannelTasksToBeScheduled: SchedulING ${currentProspect.prospectForScheduling.prospect.email} for step ${nextStep.id} at $schedulerDateTime :: delayInSec: $delayInSec")

                    // val isPersonalEmail: Boolean = FreeEmailDomainList.checkIfFreeEmailService(email = currentProspect.prospect.email)


                    // generate scheduled task
                    val task = GenerateScheduleTaskData(
                      currentCampaign = currentCampaign,
                      nextStep = nextStep,
                      currentProspect = currentProspect,
                      currentVariant = currentVariant,
                      schedulerDateTime = schedulerDateTime,
                    )

                    // WARNING!: mutation
                    //tasksToBeScheduled = tasksToBeScheduled :+ task
                    tasksToBeScheduled +=  task


                    Logger.info(s"(campaign ${currentCampaign.campaign.campaign_id}) getChannelTasksToBeScheduled: task::curr_var::step_id ${task.currentVariant.step_id} task::curr_var::step_label  ${task.currentVariant.step_label} task::curr_var::step_type ${task.currentVariant.step_data.step_type} task::curr_var::id ${task.currentVariant.id} ::: SchedulING ${currentProspect.prospectForScheduling.prospect.email} for step ${nextStep.id} at $schedulerDateTime :: delayInSec: $delayInSec")


                  }

              }


            }
        }

      }

      // go to next campaign
      if (currentCampaignIndex < (totalCampaigns - 1)) {
        currentCampaignIndex = currentCampaignIndex + 1
      } else {
        currentCampaignIndex = 0
      }

    }

    //      val checkedTillProspectIndexInsideCurrentCampaign: Int = campaignProspectCheckedIndexMap(currentCampaignId)

    tasksToBeScheduled.toVector


  }

  def getConsecutiveTaskScheduleDelay(
                                       channelDataForScheduling: ChannelDataForSchedulingType
                                     ): Int


  def getScheduleTillTime(
                           channelDataForScheduling: ChannelDataForSchedulingType
                         ): DateTime = {

    // schedule for max next hour
    val tillTime = DateTime.now(DateTimeZone.UTC).plusHours(3)

    tillTime

  }


  def getScheduleFromTime(
                           channelDataForScheduling: ChannelDataForSchedulingType
                         ): DateTime


  def getChannelTaskLimitPerDay(
                                 channelDataForScheduling: ChannelDataForSchedulingType,
                                 campaignStepType: CampaignStepType,
                               ): Int


  def getCampaignDailyLimitForChannel(
                                       channelCountData: ChannelScheduledProspectsCountForCampaignType,
                                       channelDataForScheduling: ChannelDataForSchedulingType,
                                       campaignStepType: CampaignStepType
                                     ): Int

  /**
   * This function gets called inside the getChannelTasksToBeScheduled and is used to return the missing merge tags for channel.
   * If a prospect has missing merge tags then task related to those prospect are skipped, else task associated with the prospect are
   * added to task to be scheduled
   * It is eventually used by scheduleTaskForChannel  to schedule task every 30 second.
   *
   *
   */
  final def checkAndReturnMissingFieldsForChannel(
                                                   channelDataForScheduling: ChannelDataForSchedulingType,
                                                   nextStepId: Long,
                                                   currentCampaign: ScheduleCampaign,
                                                   currentProspect: ProspectDataForChannelSchedulingType,
                                                   currentVariant: CampaignStepVariantForScheduling,
                                                   emailServiceCompanion: EmailServiceCompanion,
                                                   templateService: TemplateService,
                                                   campaignEditedPreviewEmailDAO: CampaignEditedPreviewEmailDAO,
                                                   campaignProspectDAO: CampaignProspectDAO,
                                                   campaignsMissingMergeTagService: CampaignsMissingMergeTagService,
                                                   isCalendarEnabled: Boolean,
                                                   calendarAccountData: Option[CalendarAccountData],
                                                   Logger: SrLoggerTrait
                                                 )(using logger: SRLogger): Try[PendingOrMissingMergeTags] = {

    //The calendar_link and unsubscibe_link are used to call the emailServiceCompanion.getInternalMerge tag , inside checkAndReturnMissingMergeTags
    // the internalmergetags found are then used to check the missing merge tags  by a call to this templateService.checkMissingMergeTags.

    checkAndReturnMissingFields(
      sender_name = channelDataForScheduling.firstName + " " + channelDataForScheduling.lastName,
      sender_first_name = channelDataForScheduling.firstName,
      sender_last_name = channelDataForScheduling.lastName,
      // only for passing the check below, we know the campaign, prospect, step and emailScheduled ids would exist in this case.,
      //ie. this flow is only to check the missing merge tags , and since all the above ids are always available for this case the unsubscribe_link will
      // be always available
      unsubscribe_link = Some("dummy_link"),
      // calendar_link may not be present for campaign with enable_calendar not present
      // if calendar_link get Passed as None, even then it is fine because calendar_link will neither be part of body or
      // or subject as it is causing
      calendar_link =
        if (isCalendarEnabled) {
          EmailHelper._makeCalendarLinkUrl(
            prospectId = ProspectId(id = currentProspect.prospect.id),
            teamId = TeamId(id = currentCampaign.campaign.team_id),
            sender_name = channelDataForScheduling.firstName + " " + channelDataForScheduling.lastName,
            campaignId = CampaignId(id = currentCampaign.campaign.campaign_id),
            stepId = currentVariant.step_id,
            calendarAccountData = calendarAccountData,
            selectedCalendarData = currentCampaign.campaign.selected_calendar_data
          ).toOption
        } else {
          None
        }

      ,
      nextStepId = nextStepId,
      currentCampaign = currentCampaign,
      currentProspect = currentProspect,
      signature = channelDataForScheduling.signature,
      step_data = currentVariant.step_data,
      currentVariant = currentVariant,
      campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO,
      emailServiceCompanion = emailServiceCompanion, // Fixme multichannel: These dependencies are email specific
      templateService = templateService, // Fixme multichannel: These dependencies are email specific
      campaignProspectDAO = campaignProspectDAO,
      campaignsMissingMergeTagService = campaignsMissingMergeTagService,

      Logger = Logger
    )
  }

  // TODO: Implement it for whatsapp and caller
  def findChannelSpecificMissingFields(
                                        prospect: ProspectObject,
                                        internalMergeTags: InternalMergeTagValuesForProspect,
                                        step_type: CampaignStepType,
                                        templateService: TemplateService
                                      )(using logger: SRLogger): Try[Seq[String]] = {

    Success(Seq())

  }


  final def checkAndReturnMissingFields(
                                         sender_name: String,
                                         sender_first_name: String,
                                         sender_last_name: String,
                                         unsubscribe_link: Option[String],
                                         calendar_link: Option[String],
                                         nextStepId: Long,
                                         currentCampaign: ScheduleCampaign,
                                         currentProspect: ProspectDataForChannelSchedulingType,
                                         signature: Option[String],
                                         step_data: CampaignStepData,
                                         currentVariant: CampaignStepVariantForScheduling,
                                         emailServiceCompanion: EmailServiceCompanion,
                                         templateService: TemplateService,
                                         campaignEditedPreviewEmailDAO: CampaignEditedPreviewEmailDAO,
                                         campaignProspectDAO: CampaignProspectDAO,
                                         campaignsMissingMergeTagService: CampaignsMissingMergeTagService,
                                         Logger: SrLoggerTrait
                                       )(using logger: SRLogger): Try[PendingOrMissingMergeTags] = {

    val headStepId = currentCampaign.campaign.campaign_type_data match {

      case data: CampaignTypeData.EmailChannelData => data.head_step_id

      case data: CampaignTypeData.MultiChannelCampaignData => data.head_step_id

      case data: CampaignTypeData.MagicContentData => data.head_step_id

      case data: CampaignTypeData.DripCampaignData =>
        /*

          What we need is a check here that some step has been sent via drip campaign and  it is not equal
          current step Id.


          Keeping the headStepId as 0 for drip currently handles the missing merge tags but we still need to test
          the flow by adding some missing merge tags in subject or body.


         FIXME DRIP:
         What we need is a check here that some step has been sent via drip campaign and  it is not equal
         current step Id.
         Keeping the headStepId as 0 for drip currently handles the missing merge tags but we still need to test
         the flow by adding some missing merge tags in subject or body.

        */
        0L // FIXME DRIP: Used to get previous sent steps for previous_subject merge tag
    }

    val teamId = TeamId(id = currentProspect.prospect.team_id)

    val subjectAndBody = CampaignStepData.getSubjectAndBodyFromStepData(
      stepData = step_data
    )

    campaignsMissingMergeTagService.checkPendingMagicColumn(
      teamId = teamId,
      prospect = currentProspect.prospect,
      campaignId = CampaignId(id = currentCampaign.campaign.campaign_id),
      subject = subjectAndBody.subject,
      body = subjectAndBody.body,
      channelType = currentVariant.step_data.step_type.channelType,
    ) match {

      case Failure(exception) =>

        Logger.shouldNeverHappen(
          msg = s"Failed to check pending magic columns. teamId: $teamId :: prospectId: ${currentProspect.prospect.id} :: channelType: ${currentVariant.step_data.step_type.channelType}",
          err = Some(exception),
        )

        Failure(exception)

      case Success(pendingCols) =>

        if (pendingCols.nonEmpty) {

          Success(
            PendingOrMissingMergeTags(
              pendingMagicMergeTags = pendingCols.map(_.column_name),
              missingMergeTags = List(),
              skipSchedulingProspect = true,
            )
          )

        } else {

          // TODO Multichannel: campaign_edited_preview_emails does not have team_id.
          val absentTagsFoundSeq = for {


            //finds internal Merge tags

            internalMergeTags: InternalMergeTagValuesForProspect <- emailServiceCompanion.getInternalMergeTag(
              sender_name = sender_name,
              sender_first_name = sender_first_name,
              sender_last_name = sender_last_name,
              unsubscribe_link = unsubscribe_link,
              stepId = Some(nextStepId),
              head_step_id = Some(headStepId),
              previousEmails = Seq(),
              campaignId = currentCampaign.campaign.campaign_id,
              prospectId = currentProspect.prospect.id,
              teamId = TeamId(id = currentProspect.prospect.team_id),
              signature = signature, // TODO: checkme
              calendar_link = calendar_link
            )


            // nxd notes: io Action

            (subject: String, body: String) <- Success {
              val subjectAndBody = CampaignStepData.getSubjectAndBodyFromStepData(
                stepData = step_data
              )

              (subjectAndBody.subject, subjectAndBody.body)
            }


            // it firsts find tags present in body template and subject template and checks if tag is present in the overall merge,
            // it returns all the tags which are not present
            missingTagsInBodyAndSubject: Seq[String] <- templateService.checkMissingMergeTags(
              bodyTemplate = body,
              subjectTemplate = subject,
              prospect = currentProspect.prospect,
              internalMergeTags = internalMergeTags,
              channel = step_data.step_type.channelType
            )

            channelSpecificMissingFields: Seq[String] <- findChannelSpecificMissingFields(
              prospect = currentProspect.prospect,
              internalMergeTags = internalMergeTags,
              step_type = step_data.step_type,
              templateService = templateService
            )

            errCols: List[MagicColumnResponse] <- campaignsMissingMergeTagService.checkMagicColumnWithErrors(
              teamId = teamId,
              prospect = currentProspect.prospect,
              subject = subject,
              body = body,
              channelType = currentVariant.step_data.step_type.channelType,
            )

            absentTagsFounds: Seq[String] <- Try {

              val absentTags = missingTagsInBodyAndSubject ++ channelSpecificMissingFields ++ errCols.map(_.column_name)

              absentTags.distinct

            }


            hasMissingMergeTags: Boolean <- if (absentTagsFounds.isEmpty) Success(false) else {

              // check if user edited the Preview of this email step for the given prospect,
              // if edited, no need to add missing merge-tag error even if absentTags are there
              val previewHasBeenEdited: Boolean = {

                val stepId = currentVariant.step_id
                val prospectId = currentProspect.prospect.id


                campaignEditedPreviewEmailDAO.find(
                  stepId = Some(stepId),
                  prospectId = prospectId,
                  campaignId = None
                ) match {

                  case Failure(editCheckErr) =>

                    Logger.fatal(s"error while addMissingErrorMessage [CampaignEditedPreviewEmail.find] [ADDING MISSING MERGE_TAG ERROR FOR SAFETY: returning false] ScheduleEmailCronServiceV2: ${LogHelpers.getStackTraceAsString(editCheckErr)}")

                    false

                  case Success(editedEmails) =>

                    if (editedEmails.isEmpty) false else {

                      Logger.info(s"found edited-preview, so ignoring addMissingErrorMessage [CampaignEditedPreviewEmail.find]: prospectId: $prospectId :: prospectEmail: ${currentProspect.prospect.email} :: stepId: $stepId :: absentTagsFounds: $absentTagsFounds")

                      true
                    }

                }
              }

              // TODO: check this condition in context of failed magic columns
              val hasMissingMergeTagError = if (previewHasBeenEdited) false else true

              Success(hasMissingMergeTagError)

            }


            // add the error message in db
            _: Int <- {

              if (hasMissingMergeTags && absentTagsFounds.nonEmpty) {

                Logger.info(s"ADDING addMissingErrorMessage ScheduleEmailCronServiceV2: cid_${currentCampaign.campaign.campaign_id} :: pid_${currentProspect.prospect.id} absentTags: $absentTagsFounds")

                // It updates the campaign_prospects table with the  list of missing merge tags for respective prospects and reset campaign stats
                campaignsMissingMergeTagService.addMissingOrInvalidFieldsAndResetCache(
                  campaignId = CampaignId(
                    id = currentCampaign.campaign.campaign_id
                  ),
                  prospectId = ProspectId(
                    id = currentProspect.prospect.id
                  ),
                  teamId = TeamId(
                    id = currentCampaign.campaign.team_id
                  ),
                  absentTagsFounds = absentTagsFounds
                )
                //          campaignProspectDAO.addMissingErrorMessage(
                //            campaignId = currentCampaign.campaign.campaign_id,
                //            prospectId = currentProspect.prospect.id,
                //            fields = absentTagsFounds
                //          )

              } else {

                Success(0)

              }


            }
          } yield {

            PendingOrMissingMergeTags(
              pendingMagicMergeTags = List(),
              missingMergeTags = absentTagsFounds.toList,
              skipSchedulingProspect = hasMissingMergeTags,
            )

          }

          absentTagsFoundSeq

        }

    }

  }

  final def getRemainingToBeSentCountForTheDay(
                                                //dailyQuota: Int,
                                                // channel related
                                                //channelLevelTaskCountLimitPerDay: Int,
                                                //totalChannelTasksScheduledCount: Int,
                                                remainingToBeSentCountForChannelStepType: Int,
                                                remainingToBeSentCountFromCampaign: Int,

                                                //totalTasksDoneOrScheduledFromCampaignToday: Int,

                                                Logger: SRLogger,

                                              )(
                                                implicit logger: SRLogger
                                              ): Int = {

    //    Logger.debug(s"Entry getRemainingToBeSentCountForTheDay")


    //val totalCampaignScheduledCount = totalTasksDoneOrScheduledFromCampaignToday

    //val remainingToBeSentCountFromEA = channelLevelTaskCountLimitPerDay - totalChannelTasksScheduledCount
    //val remainingToBeSentCountFromCampaign = dailyQuota - totalTasksDoneOrScheduledFromCampaignToday

    val remainingToBeSentCount = if (
      Math.min(remainingToBeSentCountFromCampaign, remainingToBeSentCountForChannelStepType) < 0
    ) 0
    else Math.min(remainingToBeSentCountFromCampaign, remainingToBeSentCountForChannelStepType)


    Logger.info(s" Exit getRemainingToBeSentCountForTheDay:   ::   ::  :: remainingToBeSentCountFromCampaign: $remainingToBeSentCountFromCampaign :: remainingToBeSentCountFromEA: $remainingToBeSentCountForChannelStepType :: remainingToBeSentCount: $remainingToBeSentCount")


    remainingToBeSentCount
  }

  // returns savedTaskIds
  def saveTasksToBeScheduledAndUpdateCampaignDataV2(
                                                     channelTasks: Vector[GenerateScheduleTaskData],
                                                     channelDataForScheduling: ChannelDataForSchedulingType,
                                                     scheduleCampaigns: Seq[ScheduleCampaign],
                                                     orgId: Long,
                                                     taskService: TaskService,
                                                     templateService: TemplateService,
                                                     accountOrgBillingRelatedService: AccountOrgBillingRelatedService,
                                                     calendarAccountData: Option[CalendarAccountData]
                                                   )(implicit ec: ExecutionContext,
                                                     ws: WSClient,
                                                     system: ActorSystem,
                                                     materializer: Materializer,
                                                     Logger: SRLogger): Future[Seq[CampaignProspectUpdateScheduleStatus]] = {

    Logger.info(s"\n\nsaveTasksToBeScheduledAndUpdateCampaignDataV2: $channelTasks")

    val res = channelTasks.map(
      task => {

        val checkAndUpdateProspectsContactedCountTry: Try[Int] = task.currentVariant.step_data.step_type match {
          case CampaignStepType.GeneralTask |
               CampaignStepType.CallStep |
               CampaignStepType.LinkedinInmail |
               CampaignStepType.LinkedinConnectionRequest |
               CampaignStepType.LinkedinMessage |
               CampaignStepType.LinkedinViewProfile |
               CampaignStepType.WhatsappMessage |
               CampaignStepType.SmsMessage =>

            accountOrgBillingRelatedService.checkAndUpdateProspectsContacted(
              prospectId = ProspectId(task.currentProspect.prospectForScheduling.prospect.id),
              teamId = TeamId(task.currentCampaign.campaign.team_id),
              prospectTouchedType = ProspectTouchedType.ManualTaskScheduled,
              channelType = task.currentVariant.step_data.step_type.channelType,
              updateLastContactedAt = false,
              logger = Logger,
            )

          // Incrementing will be handled at sending side for them
          case CampaignStepType.AutoLinkedinConnectionRequest |
               CampaignStepType.AutoEmailMagicContent |
               CampaignStepType.ManualEmailMagicContent |
               CampaignStepType.AutoLinkedinMessage |
               CampaignStepType.AutoLinkedinInmail |
               CampaignStepType.AutoLinkedinViewProfile |
               CampaignStepType.ManualEmailStep |
               CampaignStepType.MoveToAnotherCampaignStep |
               CampaignStepType.AutoEmailMagicContent |
               CampaignStepType.ManualEmailMagicContent |
               CampaignStepType.AutoEmailStep =>

            Success(1)

        }

        val subjectAndBody = getSubjectAndBodyFromStepData(task.currentVariant.step_data)

        val (init_subject, init_body) = (subjectAndBody.subject, subjectAndBody.body)
        val prospect: ProspectObject = task.currentProspect.prospectForScheduling.prospect
        val channel: ChannelType = task.currentVariant.step_data.step_type.channelType
        val calendarLink = if (task.currentCampaign.campaign.selected_calendar_data.isDefined) {
          EmailHelper._makeCalendarLinkUrl(
            prospectId = ProspectId(id = prospect.id),
            teamId = TeamId(id = prospect.team_id),
            sender_name = channelDataForScheduling.firstName + " " + channelDataForScheduling.lastName,
            campaignId = CampaignId(id = task.currentCampaign.campaign.campaign_id),
            stepId = task.currentVariant.step_id,
            calendarAccountData = calendarAccountData,
            selectedCalendarData = task.currentCampaign.campaign.selected_calendar_data
          ).toOption
        } else {
          None
        }


        val internalMergeTags = InternalMergeTagValuesForProspect(
          unsubscribe_link = None, //currently unsubscribe link only for email channel
          sender_name = channelDataForScheduling.firstName + " " + channelDataForScheduling.lastName,
          sender_first_name = channelDataForScheduling.firstName,
          sender_last_name = channelDataForScheduling.lastName,
          previous_subject = None,
          signature = channelDataForScheduling.signature,
          sender_phone_number = channelDataForScheduling.sender_phone_number,
          calendar_link = calendarLink
        )
        val campaignStepContent: Try[CampaignStepContent] = {
          for {
            _: Int <- checkAndUpdateProspectsContactedCountTry

            subject: String <- templateService.render(
              template = init_subject,
              prospect = prospect,
              internalMergeTags = internalMergeTags,
              channel = channel
            )
            body: String <- templateService.render(
              template = init_body,
              prospect = prospect,
              internalMergeTags = internalMergeTags,
              channel = channel
            )
            notes: String <- task.currentVariant.notes
            match {
              case None => Success("")

              case Some(note) =>
                templateService.render(
                  template = note,
                  prospect = prospect,
                  internalMergeTags = internalMergeTags,
                  channel = channel
                )
            }
          }
          yield {
            val data = CampaignStepContent(
              subject = subject,
              body = body,
              notes = notes
            )
            data
          }

        } match {
          case Failure(e) =>
            Logger.error("Error while replacing merge tags", err = e) //We are logging the error message here
            Failure(e)

          case Success(data: CampaignStepContent) =>
            Success(data)

        }

        campaignStepContent match {
          case Failure(_) => Future.successful(Left(CreateTaskError.ErrorWhileCreatingTask))


          case Success(campaignStepContent: CampaignStepContent) =>


            //proceeding with the remaining logic the same way, only if render is successful
            val (subject, body, notes) = (campaignStepContent.subject, campaignStepContent.body, campaignStepContent.notes)


            val campaignStepType = task.currentVariant.step_data.step_type

            val data : Try[(TaskType, TaskData)] = campaignStepType match {

              case CampaignStepType.WhatsappMessage =>
                Success((TaskType.SendWhatsAppMessage, TaskData.SendWhatsAppMessageData(body)))

              case CampaignStepType.GeneralTask =>
                Success((TaskType.GeneralTask, TaskData.GeneralTaskData(task_notes = task.currentVariant.notes.getOrElse(""))))

              case CampaignStepType.LinkedinConnectionRequest =>
                Success((TaskType.SendLinkedinConnectionRequest, TaskData.LinkedinConnectionRequestData(request_message = Some(body))))

              case CampaignStepType.LinkedinViewProfile =>
                Success((TaskType.ViewLinkedinProfile, TaskData.ViewLinkedinProfileData()))

              case CampaignStepType.LinkedinInmail =>
                Success((TaskType.SendLinkedinInMail, TaskData.SendLinkedinInMailData(subject = Some(subject), body = body)))

              case CampaignStepType.LinkedinMessage =>
                Success((TaskType.SendLinkedinMessage, TaskData.SendLinkedinMessageData(body = body)))

              case CampaignStepType.AutoLinkedinViewProfile =>
                Success((TaskType.AutoViewLinkedinProfile, TaskData.AutoViewLinkedinProfile()))

              case CampaignStepType.AutoLinkedinInmail =>
                Success((TaskType.AutoLinkedinInmail, TaskData.AutoLinkedinInmail(subject = subject, body = body)))

              case CampaignStepType.AutoLinkedinConnectionRequest =>
                Success((TaskType.AutoLinkedinConnectionRequest, TaskData.AutoLinkedinConnectionRequest(body = Some(body))))

              case CampaignStepType.AutoLinkedinMessage =>
                Success((TaskType.AutoLinkedinMessage, TaskData.AutoLinkedinMessage(body = body)))


              // These case would not occur as of now. Adding to resolve error
              case CampaignStepType.SmsMessage =>
                Success((TaskType.SendSms, TaskData.SendSmsData(body)))

              // These case would not occur as of now. Adding to resolve error
              case CampaignStepType.CallStep =>
                Success((TaskType.CallTask, TaskData.CallTaskData(body, recording_link = None)))

              //              case CampaignStepType.PhoneMessage =>
              //                (TaskType.SendSms, TaskData.SendSmsData(body))

              case CampaignStepType.AutoEmailStep | CampaignStepType.AutoEmailMagicContent =>

                Logger.shouldNeverHappen(s"${campaignStepType.toKey} should be scheduled only by EmailChannelScheduler: pid_${task.currentProspect.prospectForScheduling.prospect.id} :: cid_${task.currentCampaign.campaign.campaign_id}")
                /*
                  28-August-2024
                    email_message_id is passed as None, because we've not saved emails_scheduled entry yet.
                    so we don't have id to keep here.
                 */
                Success((TaskType.SendEmail, TaskData.SendEmailData(subject, body, None)))

              case CampaignStepType.ManualEmailStep | CampaignStepType.ManualEmailMagicContent =>

                Logger.shouldNeverHappen(s"${campaignStepType.toKey} should be scheduled only by EmailChannelScheduler: pid_${task.currentProspect.prospectForScheduling.prospect.id} :: cid_${task.currentCampaign.campaign.campaign_id}")
                /*
                  28-August-2024
                    email_message_id is passed as None, because we've not saved emails_scheduled entry yet.
                    so we don't have id to keep here.
                */
                Success((TaskType.SendEmail, TaskData.SendEmailData(subject, body, None)))

              case CampaignStepType.MoveToAnotherCampaignStep =>
                Logger.shouldNeverHappen(s"MoveToAnotherCampaignStep should be only scheduled with independent scheduler: pid_${task.currentProspect.prospectForScheduling.prospect.id} :: cid_${task.currentCampaign.campaign.campaign_id}")

                Failure(new Exception("MoveToAnotherCampaignStep should be only scheduled by independent scheduler"))
            }


            //            Logger.info(s"subject : $subject :: body : $body :: taskType : $taskType :: taskData : $taskData :: task : $task")

            data match {
              case Failure(_) => Future.successful(Left(CreateTaskError.ErrorWhileCreatingTask))
              case Success((taskType: TaskType, taskData: TaskData)) =>

                val headStepId: Long = task.currentCampaign.campaign.campaign_type_data match {

                  case data: CampaignTypeData.EmailChannelData => data.head_step_id

                  case data: CampaignTypeData.MultiChannelCampaignData => data.head_step_id

                  case data: CampaignTypeData.MagicContentData => data.head_step_id

                  case data: CampaignTypeData.DripCampaignData =>
                    /*
                        Scenario 1:

                        Need integration tests for this one
                        suppose we
                            hasphone
                         yes/       \ no
                     call_step        has_linkedin
                                    /             \
                                Linkedin step      Mark prospect as completed

                       Now if on day1 prospect(with linkedin_url, No phone no) got scheduled but next day
                       prospect got updated with a valid phone number.


                       Therefore when we are trying to find the next step for same prospect again , what happens here?

                       Now if on day1 prospect(with linkedin_url, No phone no) got scheduled but next day
                       prospect got updated with a valid phone number.
                       Therefore when we are trying to find the next step for same prospect again , what happens here?

                     */
                    0L // FIXME DRIP: used to determine is_opening_step
                }
                taskService.createTask(
                    task_data = NewTask(
                      campaign_id = Some(task.currentCampaign.campaign.campaign_id),
                      campaign_name = Some(task.currentCampaign.campaign.campaign_name),
                      step_id = Some(task.currentVariant.step_id),
                      step_label = task.currentVariant.step_label,
                      created_via = TaskCreatedVia.Scheduler,
                      is_opening_step = Some(headStepId == task.currentVariant.step_id),
                      task_type = taskType,
                      is_auto_task = false,
                      task_data = taskData,
                      status = TaskStatus.Due(
                        due_at = task.schedulerDateTime
                      ),
                      assignee_id = Some(channelDataForScheduling.channelOwnerAccountId),
                      prospect_id = Some(task.currentProspect.prospectForScheduling.prospect.id),
                      priority = task.currentVariant.priority.getOrElse(TaskPriority.Normal),
                      emailsScheduledUuid = None,
                      note = Some(notes),
                    ),
                    accountId = channelDataForScheduling.channelOwnerAccountId,
                    teamId = task.currentCampaign.campaign.team_id
                  )
                  .map { savedTaskData =>

                    savedTaskData
                      .map(savedTaskId => {

                        val cpStatusData = CampaignProspectUpdateScheduleStatus(
                          current_step_status_for_scheduler_data = CurrentStepStatusForSchedulerData.Due(due_at = task.schedulerDateTime),
                          current_step_type = task.currentVariant.step_data.step_type,

                          current_step_task_id = savedTaskId,

                          campaign_id = task.currentCampaign.campaign.campaign_id,
                          prospect_id = task.currentProspect.prospectForScheduling.prospect.id,
                          step_id = task.currentVariant.step_id,
                          email_message_id = None,
                          current_campaign_email_settings_id = None //this is none as this is not a email step
                        )

                        cpStatusData
                      })

                  }
            }
        }

      })

    val result: Future[Vector[CampaignProspectUpdateScheduleStatus]] = Future
      .sequence(res)
      .map(savedTaskResults => {
        savedTaskResults
          .flatMap {
            case aSavedTask@Left(CreateTaskError.DueDateIsOutOfBoundsError(status)) =>
              Logger.fatal(s"Error while saving task DueDateIsOutOfBoundsError (ignoring): $aSavedTask")
              Vector()

            case aSavedTask@Left(CreateTaskError.AssigneeDontHavePermissionForEditTask) =>
              Logger.fatal(s"Error while saving task AssigneeDontHavePermissionForEditTask (ignoring): $aSavedTask")
              Vector()
            // We are not failing the entire operation if a task save fails.
            case aSavedTask@Left(CreateTaskError.ErrorWhileCreatingTask) =>
              Logger.fatal(s"Error While saving task ErrorWhileCreatingTask (ignoring) : $aSavedTask")
              Vector()

            case aSavedTask@Left(CreateTaskError.LinkedinMessageBodyTooLong) =>
              Logger.fatal(s"Error While saving task LinkedinMessageBodyTooLong (ignoring) : $aSavedTask")
              Vector()

            case aSavedTask@Left(CreateTaskError.LinkedinConnectionRequestAddNoteTooLong) =>
              Logger.fatal(s"Error While saving task LinkedinConnectionRequestAddNoteTooLong (ignoring) : $aSavedTask")
              Vector()

            case aSavedTask@Left(CreateTaskError.LinkedinInMailSubjectTooLong) =>
              Logger.fatal(s"Error While saving task LinkedinInMailSubjectTooLong (ignoring) : $aSavedTask")
              Vector()

            case aSavedTask@Left(CreateTaskError.LinkedinAccountNotActive) =>
              Logger.fatal(s"Error While saving task LinkedinAccountNotActive (ignoring) : $aSavedTask")
              Vector()

            case aSavedTask@Left(CreateTaskError.LinkedinInMailBodyTooLong) =>
              Logger.fatal(s"Error While saving task LinkedinInMailBodyTooLong (ignoring) : $aSavedTask")
              Vector()

            case aSavedTask@Left(CreateTaskError.WhatsAppMessageBodyTooLong) =>
              Logger.fatal(s"Error While saving task WhatsAppMessageBodyTooLong (ignoring) : $aSavedTask")
              Vector()

            case aSavedTask@Left(CreateTaskError.MightBeDuplicateTaskError(err)) =>
              Logger.fatal(s"Error While saving task MightBeDuplicateTaskError (ignoring) MightBeDuplicateTaskError : $aSavedTask", err)
              Vector()

            // The below two cases will not occur in this flow
            case aSavedTask@Left(CreateTaskError.DontHavePermissionToCreateTaskForProspect) =>
              Logger.fatal(s"IMPOSSIBLE Error While saving task DontHavePermissionToCreateTaskForProspect (ignoring) : $aSavedTask")
              Vector()

            case aSavedTask@Left(CreateTaskError.FailedToValidateProspect(e)) =>
              Logger.fatal(s"IMPOSSIBLE Error While saving task FailedToValidateProspect (ignoring) : $aSavedTask", e)
              Vector()

            case Right(value: CampaignProspectUpdateScheduleStatus) =>
              Vector(value)
          }
      })

    result

  }


  def filterProspectsByChannelSpecificChecks(
                                              prospectIds: Set[Long],
                                              team_id: TeamId,
                                              campaignForScheduling: CampaignForSchedulingType,
                                              campaignProspectDAO: CampaignProspectDAO,
                                              previousSelectedProspectIds: Set[ProspectId]
                                            )(
                                              using Logger: SRLogger

                                            ): Try[Set[Long]]


  def filterProspectsIfNeededForCampaign(
                                          account: Account,
                                          prospectsFoundForChecking: ProspectsFoundByStepType,
                                          campaign: CampaignForSchedulingType,
                                          teamId: TeamId,
                                          Logger: SRLogger,
                                        ): Try[ProspectsFoundByStepType]


  final def fetchProspects(
                            channelType: ChannelType,
                            campaignProspectDAO: CampaignProspectDAO,
                            campaignProspectService: CampaignProspectService,

                            scheduleFromTime: DateTime,
                            campaign: CampaignForSchedulingType,
                            allowedTimezonesTry: Try[Set[String]],
                            prospectsToFetch: Int,
                            channelRelevantStepIdAndDelay: Vector[SchedulerMapStepIdAndDelay],
                            newProspectsInCampaign: Boolean,
                            firstStepIsMagicContent: Boolean,
                            sendOnlyToProspectsWhoWereSentInCurrentCycle: Option[DateTime],
                            org_id: OrgId,
                            srRollingUpdateCoreService: SrRollingUpdateCoreService
                          )(using Logger: SRLogger): Try[List[ProspectDataForChannelSchedulingType]] = {

    val team_id = campaign.team_id
    val campaignId = campaign.campaign_id
    Logger.debug(s"[fetchProspects] allowedTimezones: $allowedTimezonesTry")

    // Sunday is the first entry
    val daysPreference: List[Boolean] = campaign.days_preference

    val startOfDayInSeconds: Int = campaign.daily_from_time
    val endOfDayInSeconds: Int = campaign.daily_till_time

    for {

      allowedTimezones: Set[String] <- {

        /**
         * 21-feb-2024: we are already doing this filterAllowedTimezones, much earlier in the chain and allowedTimezonesTry
         * is the result of the earlier filter. So there should be no difference between allowedTimezonesTry and redundantFilteredList
         * below. We will remove this redundant check after a while. Currently, all existing test cases pass because of keeping this
         * redundant check here.
         */
        val redundantFilteredList = ChannelSchedulerTrait
          .filterAllowedTimezones(

            timeZonesInCampaign = allowedTimezonesTry,

            daysPreference = daysPreference,

            startOfDayInSeconds = startOfDayInSeconds,

            endOfDayInSeconds = endOfDayInSeconds,

          )(
            Logger = Logger.appendLogRequestId("insideFetchProspects")
          )

        if (allowedTimezonesTry != redundantFilteredList) {

          Logger.fatal(s"llowedTimezonesTry != redundantFilteredList :: allowedTimezonesTry: $allowedTimezonesTry :: redundantFilteredList: $redundantFilteredList")

        }

        redundantFilteredList
      }

      prospects: List[ProspectDataForChannelSchedulingType] <- Try {

        Logger.debug(s"[fetchProspects] allowedTimezones After InterSect: $allowedTimezones")

        if (allowedTimezones.isEmpty) {

          Logger.info(s"[fetchProspects] allowedTimezones.isEmpty")

          List()

        } else {

          //var prospectsRetrieved: List[ProspectDataForChannelSchedulingType] = List()
          /*
          prospectsRetrieved: Initialized as an empty List[ProspectDataForChannelSchedulingType].
          filterResult.prospectsFound: A List[ProspectDataForChannelSchedulingType] from FilteredProspectsFoundForScheduling.
          ++ Operation: Concatenates prospectsRetrieved with filterResult.prospectsFound, assigning the result back to prospectsRetrieved.
          Garbage Generation Analysis
          Since prospectsRetrieved is a List (an immutable, singly-linked list in Scala), the ++ operation has the following characteristics:

          Performance:
          ++ for two Lists is O(n) where n is the length of the first list (prospectsRetrieved). This is because it must traverse and copy the entire prospectsRetrieved list to append filterResult.prospectsFound to its end, reusing the latter as the tail.
          Each iteration creates a new List containing all previous elements plus the new ones.
          Garbage:
          Every ++ operation discards the old prospectsRetrieved and allocates a new List. If the loop runs many times (e.g., up to 100 iterations for MultiChannel or Email campaigns), this generates multiple intermediate List objects that become garbage.
          Example: If prospectsToFetch = 1000, and each iteration fetches 10 prospects, you’d have ~100 iterations, creating 100 intermediate Lists (sizes 0, 10, 20, ..., 990), all but the last becoming garbage.
           */
          val prospectsRetrieved = scala.collection.mutable.ArrayBuffer[ProspectDataForChannelSchedulingType]()
          var maxProspectIdChecked: Option[Long] = None

          var isEndOfList: Boolean = false

          val maxWhileLoopCounterAllowed: Int = campaign.campaign_type_data.campaign_type match {
            case CampaignType.MultiChannel => 100

            case CampaignType.Email => 100

            case CampaignType.Drip => 1

            case CampaignType.MagicContent => 100 // Same as MultiChannel
          }

          var whileLoopCounter: Int = 0

          Logger.debug(s"[fetchProspects] before while prospectsToFetch: $prospectsToFetch")

          while (
            prospectsRetrieved.length < prospectsToFetch &&
              !isEndOfList
              && whileLoopCounter < maxWhileLoopCounterAllowed
          ) {


            val prospectsToFetchInThisLoop: Int = prospectsToFetch - prospectsRetrieved.length

            whileLoopCounter += 1

            if (whileLoopCounter >= 100) {
              Logger.fatal(s"[fetchProspects]: Counter Reached 100: campaignId: $campaignId and teamId: $team_id")
            }

            given loopLogger: SRLogger = Logger.appendLogRequestId(s" whileLoopCounter: $whileLoopCounter : prospectsToFetchInThisLoop: $prospectsToFetchInThisLoop :: prospectsToFetch: $prospectsToFetch ")


            //              Logger.debug("[fetchProspects] Inside While Loop ")

            val filterResultTry: Try[FilteredProspectsFoundForScheduling] = for {

              prospectsFromInitialDbChecks: List[ProspectDataForChannelSchedulingType] <- {

                /*
                  Drip Campaign Note:
                    With this we are sure that we are Not touching the old sequential campaign flow as we have the
                    following check while fetching prospects.
                    this variable is used in CampaignProspectDao.fetchProspectsV3MultichannelQuery
                 */

                val useModifiedQueryForDripCampaign = campaign.campaign_type_data.campaign_type match {
                  case CampaignType.MultiChannel => false
                  case CampaignType.Email => false
                  case CampaignType.Drip => true
                  case CampaignType.MagicContent => false // Same as MultiChannel
                }

                //                val useModifiedQueryForDripCampaign = false

                //                  Logger.debug(s"[fetchProspects] before fetchProspectsV3Multichannel")

                val result: Try[List[ProspectDataForChannelSchedulingType]] = fetchProspectsV3Multichannel(
                  channelType = channelType,
                  prospectIdGreaterThan = maxProspectIdChecked,
                  allowedProspectTimezones = allowedTimezones.toList,
                  campaignId = campaignId,
                  team_id = team_id,
                  channelRelevantStepIdAndDelay = channelRelevantStepIdAndDelay,

                  limit = prospectsToFetchInThisLoop,
                  newProspectsInCampaign = newProspectsInCampaign,
                  firstStepIsMagicContent = firstStepIsMagicContent,

                  sendOnlyToProspectsWhoWereSentInCurrentCycle = sendOnlyToProspectsWhoWereSentInCurrentCycle,
                  campaignProspectDAO = campaignProspectDAO,
                  useModifiedQueryForDripCampaign = useModifiedQueryForDripCampaign,
                  campaignProspectService = campaignProspectService,
                  campaign = campaign,
                  campaign_email_setting = None,
                  org_id = org_id,
//                  emailNotCompulsoryEnabled = srRollingUpdateCoreService.checkIfTeamCanReceiveEarlyUpdates(teamId = TeamId(team_id), feature = SrRollingUpdateFeature.EmailNotCompulsory)
                )
                  .map(prospects => {

                    loopLogger.debug(s"[fetchProspects] after: prospectsFromInitialDbChecks: ${prospects.length}")

                    prospects
                  })


                result
              }
              // sending holiday calendar check

              prospectsFilteredBySendingHoliday: List[ProspectDataForChannelSchedulingType] <- {

                //                  Logger.debug(s"[fetchProspects] before filterProspectWhoHaveHoliday:")

                campaignProspectDAO
                  .filterProspectWhoHaveHoliday(
                    prospectIds = prospectsFromInitialDbChecks.map(_.prospect.id),
                    scheduleFromTime = scheduleFromTime,
                    campaignTimezone = campaign.timezone,
                    campaignSendingHolidayCalendarId = campaign.sending_holiday_calendar_id,
                    team_id = team_id,
                  )
                  .map(prospectIdsWhoHaveHolidays => {

                    // reject prospects who are in holidays
                    val result = prospectsFromInitialDbChecks
                      .filterNot(p => {

                        prospectIdsWhoHaveHolidays
                          .contains(p.prospect.id)

                      })

                    loopLogger.debug(s"[fetchProspects] after prospectsFilteredBySendingHoliday: ${result.length}")

                    result

                  })

              }

              // channel-specific (email) filters

              finalFilteredProspectIds: Set[Long] <- {

                //                  Logger.debug("[fetchProspects] Before filterProspectsByChannelSpecificChecks")

                val previousSelectedProspectIds: Set[ProspectId] = prospectsRetrieved.map(_.prospect.id).map(ProspectId(_)).toSet

                val result_filter = filterProspectsByChannelSpecificChecks(
                  campaignProspectDAO = campaignProspectDAO,

                  prospectIds = prospectsFilteredBySendingHoliday
                    .map(_.prospect.id)
                    .toSet,

                  team_id = TeamId(team_id),
                  campaignForScheduling = campaign,

                  previousSelectedProspectIds = previousSelectedProspectIds

                )
                  .map(prospects => {

                    loopLogger.debug(s"[fetchProspects] after filterProspectsByChannelSpecificChecks/finalFilteredProspectIds: ${prospects.size}")

                    prospects
                  })

                result_filter

              }

              finalFilteredProspects: List[ProspectDataForChannelSchedulingType] <- Try {

                prospectsFilteredBySendingHoliday
                  .filter(p => {
                    finalFilteredProspectIds
                      .contains(p.prospect.id)
                  })

              }

            } yield {

              //                Logger.debug("[fetchProspects] : In yeild")

              FilteredProspectsFoundForScheduling(

                prospectsFound = finalFilteredProspects,

                prospectsFoundCountFromInitialDbChecks = prospectsFromInitialDbChecks.size,

                maxProspectIdFoundFromInitialDbChecks = if (prospectsFromInitialDbChecks.isEmpty) None
                else {
                  Some(prospectsFromInitialDbChecks.map(_.prospect.id).max)
                }
              )
            }

            //              Logger.debug("[fetchProspects] : after for and yield")

            // TODO : handle this .get more gracefully.

            val filterResult = filterResultTry.get

            // if from the initial DB check itself we are not getting enough number of prospects, then we have hit the end of the list
            isEndOfList = filterResult.prospectsFoundCountFromInitialDbChecks < prospectsToFetchInThisLoop

            maxProspectIdChecked = filterResult.maxProspectIdFoundFromInitialDbChecks

            if (!isEndOfList && filterResult.prospectsFound.isEmpty) {
              loopLogger.debug(s"!isEndOfList && filterResult.prospectsFound.isEmpty condition is true, campaignId: $campaignId , teamId: $team_id")
            }

            if (filterResult.prospectsFound.nonEmpty) {
              // see note that the definition of the list in this function - why we moved to array buffer
              //prospectsRetrieved = prospectsRetrieved ++ filterResult.prospectsFound
              prospectsRetrieved ++= filterResult.prospectsFound

              //                maxProspectIdChecked = filterResult.maxProspectIdFoundInFirstFilter

            }
          }

          prospectsRetrieved.toList

        }
      }

    } yield {

      //        Logger.debug(s"[fetchProspects] : outer Yield $prospects")

      prospects

    }

  }

  //we are overriding this in email channel because of sender rotation
  def fetchProspectsV3Multichannel(
                                    channelType: ChannelType,
                                    allowedProspectTimezones: List[String],
                                    prospectIdGreaterThan: Option[Long] = None, // for first page of results
                                    campaignId: Long,
                                    team_id: Long,
                                    limit: Int,
                                    channelRelevantStepIdAndDelay: Vector[SchedulerMapStepIdAndDelay],
                                    newProspectsInCampaign: Boolean,
                                    firstStepIsMagicContent: Boolean,
                                    sendOnlyToProspectsWhoWereSentInCurrentCycle: Option[DateTime],
                                    campaignProspectDAO: CampaignProspectDAO,
                                    useModifiedQueryForDripCampaign: Boolean,
                                    campaignProspectService: CampaignProspectService,
                                    campaign: CampaignForSchedulingType,
                                    campaign_email_setting: Option[CampaignEmailSettingForScheduler],
                                    org_id: OrgId,
//                                    emailNotCompulsoryEnabled: Boolean
                                  )(using Logger: SRLogger): Try[List[ProspectDataForChannelSchedulingType]]

  def isCampaignFirstStep(
                           fetchCampaignStepsDataResult: FetchCampaignStepsData,
                           channelType: ChannelType,
                           schedulingForStepType: CampaignStepType,
                         )(using Logger: SRLogger): Boolean = {


    if (fetchCampaignStepsDataResult.allCampaignSteps.isEmpty) {

      Logger.debug(s"[getProspectsToCreateTasks]: if allCampaignSteps.isEmpty : isThisChannelFirstStep : true")

      // NOTE: allCampaignSteps will be empty if the campaign has only 1 step
      true

    }
    else {

      /*

       nxd: i think is the bug

       in the if condition we have the following :

      fetchCampaignStepsDataResult.allCampaignSteps.isEmpty - means this is the first step
      that implies that the real first step is chopped off
      balaji's campaign looks like this

        id   | campaign_id | delay  |            step_type             |          created_at           |       label
--------+-------------+--------+----------------------------------+-------------------------------+--------------------
234013 |       87863 |  86400 | send_email                       | 2023-03-28 11:49:22.067508+00 | Day 1: Opening
234014 |       87863 |  86400 | linkedin_view_profile            | 2023-03-28 11:51:16.000199+00 | Day 2: Follow up 1
234015 |       87863 |  86400 | send_linkedin_connection_request | 2023-03-28 11:56:25.860268+00 | Day 3: Follow up 2
234020 |       87863 |  86400 | send_email                       | 2023-03-28 12:14:22.156532+00 | Day 4: Follow up 3
234146 |       87863 | 172800 | send_email                       | 2023-03-28 14:11:18.924922+00 | Day 6: Follow up 4
234707 |       87863 | 172800 | send_email                       | 2023-03-29 14:22:34.66118+00  | Day 9: Follow up 6
234702 |       87863 |  86400 | send_linkedin_message            | 2023-03-29 14:20:54.205749+00 | Day 7: Follow up 5
(7 rows)

       if first step is chopped off - now the first element is a linked in step. so it wont match with email channel type

       so i think the bug lies here.

       we are seeing is firstStep == false - but we cant be sure it's for campaign id 87863 -
        because there are other campaigns paired with it. The enriched logs will help us here tomorrow.
      */

      //      println(s"\n\nfetchCampaignStepsDataResult -> ${fetchCampaignStepsDataResult}\n\n")


      val result_10 = fetchCampaignStepsDataResult.allCampaignSteps.headOption.exists(firstStep => {
        // Prevents fetching same prospect for two different step types of same channel.


        val res = (firstStep.currentStepType == schedulingForStepType && firstStep.currentStepType.channelType == channelType)
        if (res) {
          Logger.info(s"found first step for channel : first_step_step_type ${firstStep.currentStepType} first_step_channel_type: ${firstStep.currentStepType.channelType} ")
        }
        res
      })


      Logger.debug(s"[getProspectsToCreateTasks]: else part allCampaignSteps.isEmpty ==false : result : $result_10")

      result_10

    }
  }

  final def getProspectsToCreateTasks(
                                       fetchLimitForFirstStep: Int,
                                       fetchLimitForFollowup: Int,
                                       scheduleFromTime: DateTime,
                                       campaign: CampaignForSchedulingType,
                                       //allCampaignSteps: Vector[SchedulerMapStepIdAndDelay],
                                       fetchCampaignStepsDataResult: FetchCampaignStepsData,


                                       schedulingForStepType: CampaignStepType,

                                       allowedTimezones: Try[Set[String]],

                                       sendOnlyToProspectsWhoWereSentInCurrentCycle: Option[DateTime],

                                       campaignProspectDAO: CampaignProspectDAO,
                                       campaignProspectService: CampaignProspectService,
                                       channelType: ChannelType,
                                       isThisChannelFirstStep: Boolean,
                                       org_id: OrgId,
                                       srRollingUpdateCoreService: SrRollingUpdateCoreService
                                     )(
                                       implicit Logger: SRLogger
                                     ): Try[ProspectsToCreateTasksResult] = {

    /*
    NOTE: allCampaignSteps will be empty if the campaign has only 1 step

    if (allCampaignSteps.isEmpty) {
      Success(List[ProspectForScheduling]())
    } else*/
    for {
      /*
     if (fetchCampaignStepsDataResult.allCampaignSteps.isEmpty)
     {

       Logger.debug(s"[getProspectsToCreateTasks]: if allCampaignSteps.isEmpty : isThisChannelFirstStep : true")

       // NOTE: allCampaignSteps will be empty if the campaign has only 1 step
       true

     }
     else
     {

       /*

        nxd: i think is the bug

        in the if condition we have the following :

       fetchCampaignStepsDataResult.allCampaignSteps.isEmpty - means this is the first step
       that implies that the real first step is chopped off
       balaji's campaign looks like this

         id   | campaign_id | delay  |            step_type             |          created_at           |       label
      --------+-------------+--------+----------------------------------+-------------------------------+--------------------
       234013 |       87863 |  86400 | send_email                       | 2023-03-28 11:49:22.067508+00 | Day 1: Opening
       234014 |       87863 |  86400 | linkedin_view_profile            | 2023-03-28 11:51:16.000199+00 | Day 2: Follow up 1
       234015 |       87863 |  86400 | send_linkedin_connection_request | 2023-03-28 11:56:25.860268+00 | Day 3: Follow up 2
       234020 |       87863 |  86400 | send_email                       | 2023-03-28 12:14:22.156532+00 | Day 4: Follow up 3
       234146 |       87863 | 172800 | send_email                       | 2023-03-28 14:11:18.924922+00 | Day 6: Follow up 4
       234707 |       87863 | 172800 | send_email                       | 2023-03-29 14:22:34.66118+00  | Day 9: Follow up 6
       234702 |       87863 |  86400 | send_linkedin_message            | 2023-03-29 14:20:54.205749+00 | Day 7: Follow up 5
      (7 rows)

        if first step is chopped off - now the first element is a linked in step. so it wont match with email channel type

        so i think the bug lies here.

        we are seeing is firstStep == false - but we cant be sure it's for campaign id 87863 -
         because there are other campaigns paired with it. The enriched logs will help us here tomorrow.
       */


       val result_10 = fetchCampaignStepsDataResult.allCampaignSteps.headOption.exists(firstStep => {
         // Prevents fetching same prospect for two different step types of same channel.
         val res = (firstStep.currentStepType == schedulingForStepType && firstStep.currentStepType.channelType == channelType)
         if (res) {
           Logger.info(s"found first step for channel : first_step_step_type ${firstStep.currentStepType} first_step_channel_type: ${firstStep.currentStepType.channelType} ")
         }
         res
       })

       Logger.debug(s"[getProspectsToCreateTasks]: else part allCampaignSteps.isEmpty ==false : result : $result_10")

       result_10

     }
     */

      //      campaignsStepsRelevantToChannel: Vector[SchedulerMapStepIdAndDelay] <- Try{ EmailSchedulerService.filterCampaignStepsRelevantToChannel(
      //        allCampaignSteps = allCampaignSteps,
      //        channelType = channelType
      //      )}

      channelRelevantStepIdAndDelay: Vector[SchedulerMapStepIdAndDelay] <- Try {
        Logger.info(s"isThisChannelFirstStep: $isThisChannelFirstStep cid_${campaign.campaign_id} step_type_${schedulingForStepType.channelStepType}, step_type_${schedulingForStepType}  opt_first_step_${fetchCampaignStepsDataResult.allCampaignSteps.headOption}")
        fetchCampaignStepsDataResult.relevantCampaignStepsForChannel
          .filter(_.nextStepType == schedulingForStepType)
      }

      // should be less than daily quota
      // should adhere to email prioirty
      // should compensate for lack of followups / new prospects on a particular day

      // PROSPECTS_EMAILS_TODO_READ_CLEANED
      foundFollowUpProspects: List[ProspectDataForChannelSchedulingType] <- {
        given localSrLogger: SRLogger = Logger.appendLogRequestId(s"newProspectsInCampaign: false")

        localSrLogger.debug(s"[getProspectsToCreateTasks]: fetch followup Limit : $fetchLimitForFollowup")
        fetchProspects(
          channelType = channelType,
          campaignProspectDAO = campaignProspectDAO,
          campaignProspectService = campaignProspectService,

          scheduleFromTime = scheduleFromTime,
          campaign = campaign,
          allowedTimezonesTry = allowedTimezones,
          channelRelevantStepIdAndDelay = channelRelevantStepIdAndDelay,

          prospectsToFetch = fetchLimitForFollowup,
          newProspectsInCampaign = false,
          firstStepIsMagicContent = false, // since this is not the first step we dont need this as followup is not getting effected by this check

          sendOnlyToProspectsWhoWereSentInCurrentCycle = sendOnlyToProspectsWhoWereSentInCurrentCycle,
          org_id = org_id,
          srRollingUpdateCoreService = srRollingUpdateCoreService
        )
      }

      //        Logger.debug(s"[getProspectsToCreateTasks]: after foundFollowUpProspects :$foundFollowUpProspects")


      // get new prospects for Opening / first email

      // PROSPECTS_EMAILS_TODO_READ_CLEANED
      /*
    if we are in the first step , then we will try and pull new prospects - the list may be exhausted
    if we are not in the first step , then we will never try for new prospects
    */
      foundNewProspects: List[ProspectDataForChannelSchedulingType] <- {
        Logger.info(s"channelRelevantStepIdAndDelay: $channelRelevantStepIdAndDelay :: schedulingForStepType: $schedulingForStepType")
        if (isThisChannelFirstStep) {
          val head_step_type: Option[CampaignStepType] = fetchCampaignStepsDataResult.stepsMappedById.headOption.map(_._2.step_type)
          val firstStepIsMagicContent: Boolean = head_step_type.isDefined && (head_step_type.get == CampaignStepType.AutoEmailMagicContent || head_step_type.get == CampaignStepType.ManualEmailMagicContent)

          given localSrLogger: SRLogger = Logger.appendLogRequestId(s"newProspectsInCampaign: true")

          val campaign_ids: List[Long] =  campaignProspectService.getCampaignsToLogDripFor().map(_.id)

          if(campaign_ids.contains(campaign.campaign_id)){

            Logger.doNotTruncate(s"getProspectsToCreateTasks :: fetchCampaignStepsDataResult :: ${fetchCampaignStepsDataResult} :: allCampaignSteps :: ${fetchCampaignStepsDataResult.allCampaignSteps} :: head_step_type :: ${head_step_type} :: firstStepIsMagicContent :: ${firstStepIsMagicContent}")

          }

          fetchProspects(
            channelType = channelType,
            campaignProspectDAO = campaignProspectDAO,
            campaignProspectService = campaignProspectService,

            scheduleFromTime = scheduleFromTime,
            campaign = campaign,
            allowedTimezonesTry = allowedTimezones,
            channelRelevantStepIdAndDelay = channelRelevantStepIdAndDelay,

            prospectsToFetch = fetchLimitForFirstStep,
            newProspectsInCampaign = true,
            firstStepIsMagicContent = firstStepIsMagicContent,
            sendOnlyToProspectsWhoWereSentInCurrentCycle = sendOnlyToProspectsWhoWereSentInCurrentCycle,
            org_id = org_id,
            srRollingUpdateCoreService = srRollingUpdateCoreService
          )
        } else {
          Success(List[ProspectDataForChannelSchedulingType]())
        }
      }

    } yield {

      val prospects = ProspectsToCreateTasksResult(
        foundNewProspects = foundNewProspects,
        foundFollowUpProspects = foundFollowUpProspects,
      )


      Logger.info(s"aid_${campaign.campaign_owner_id} :: (campaign ${campaign.campaign_id})  getProspectsToSendEmailsTo :: foundFollowUpProspects: ${foundFollowUpProspects.size} :: foundNewProspects: ${foundNewProspects.size} :: sendOnlyToProspectsWhoWereSentInCurrentCycle: $sendOnlyToProspectsWhoWereSentInCurrentCycle :: fetchFollowupLimit: $fetchLimitForFollowup :: fetchNewUsersLimit: $fetchLimitForFirstStep")

      prospects
    }
  }

  case class CampaignStepDataForLogging(
                                         campaign_id: Long,
                                         label: Option[String],
                                         step_type: CampaignStepType,
                                         id: Long
                                       ) {
    override def toString: String = {
      //super.toString
      s"{ \"CampaignStepDataForLogging\":  {\"campaign_id\": ${campaign_id}, \"label\": \"${label}\", \"step_type\": \"${step_type}\", \"id\": ${id} } }"
    }
  }

  def helperFetchCampaignStepsData(
                                    head_step_id: Long,
                                    campaignStepVariantDAO: CampaignStepVariantDAO,
                                    campaign_id: CampaignId,
                                    enable_ab_testing: Boolean,
                                    channelType: ChannelType,
                                    campaign_type: CampaignType

                                  )(using Logger: SRLogger, ec: ExecutionContext): Success[FetchCampaignStepsData] = {
    val headStepId: Long = head_step_id


    // nxd notes:  IOAction inside loop (flatmap)
    // this returns step of all channel / step-types
    val campaignSteps: Seq[CampaignStepWithChildren] = campaignStepVariantDAO.findByCampaignIdForSchedule(
      campaignId = campaign_id.id,
      isABTestingEnabled = enable_ab_testing,
      logger = Logger
    )


    val stepsMappedById = campaignSteps.map(s => s.id -> s).toMap

    val campaignStepData_for_logging: Seq[CampaignStepDataForLogging] = campaignSteps.map(cs =>
      CampaignStepDataForLogging(
        campaign_id = cs.campaign_id,
        label = cs.label,
        step_type = cs.step_type,
        id = cs.id)
    )



    //Logger.debug(s"headStepId: $headStepId, campaignSteps: ${ campaignStepData_for_logging} stepsMappedById: $stepsMappedById\n\n")

    // nxd notes:  NOT an IOAction  - pure fn
    val orderedStepIds: Vector[Long] = CampaignStepDAO.getOrderedSteps(campaignSteps, head_step_id).map(_.id).toVector

    val allCampaignSteps: Vector[SchedulerMapStepIdAndDelay] = ChannelSchedulerService.getMapOfStepIdAndRequiredDelay(
      headStepId = headStepId,
      orderedStepIds = orderedStepIds,
      stepsMappedById = stepsMappedById
    )


    val campaignsStepsRelevantToChannel: Vector[SchedulerMapStepIdAndDelay] = ChannelSchedulerService.filterCampaignStepsRelevantToChannel(
      allCampaignSteps = allCampaignSteps,
      channelType = channelType
    )

    val campaign_step_structure: CampaignStepsStructure = campaign_type match {
      case CampaignType.Email => CampaignStepsStructure.EmailCampaignStepsStructure(
        orderedStepIds = orderedStepIds
      )
      case CampaignType.MultiChannel => CampaignStepsStructure.MultichannelCampaignStepsStructure(
        orderedStepIds = orderedStepIds
      )
      case CampaignType.Drip =>
        Logger.shouldNeverHappen(s"helperFetchCampaignStepsData :: campaign_type drip passed :: ")

        CampaignStepsStructure.MultichannelCampaignStepsStructure(
          orderedStepIds = orderedStepIds
        )
      case CampaignType.MagicContent => CampaignStepsStructure.MultichannelCampaignStepsStructure(
        orderedStepIds = orderedStepIds
      )
    }


    val result_fetchCampaignStepsData: FetchCampaignStepsData = FetchCampaignStepsData(
      stepsMappedById = stepsMappedById,
      campaignStepsStructure = campaign_step_structure,
      allCampaignSteps = allCampaignSteps,
      relevantCampaignStepsForChannel = campaignsStepsRelevantToChannel
    )


    //    Logger.debug(s"Exit fetchCampaignStepsData: $result_fetchCampaignStepsData")

    Success(result_fetchCampaignStepsData)
  }


  final def fetchCampaignStepsData(
                                    c: CampaignForSchedulingType,
                                    account: Account,
                                    channelType: ChannelType,
                                    campaignStepVariantDAO: CampaignStepVariantDAO,
                                    campaignStepDAO: CampaignStepDAO
                                  )(
                                    using Logger: SRLogger,
                                    ec: ExecutionContext
                                  ): Try[FetchCampaignStepsData] = {

    c.campaign_type_data match {

      case data: CampaignTypeData.EmailChannelData =>

        helperFetchCampaignStepsData(
          head_step_id = data.head_step_id,
          campaignStepVariantDAO = campaignStepVariantDAO,
          campaign_id = CampaignId(id = c.campaign_id),
          enable_ab_testing = account.org.settings.enable_ab_testing,
          channelType = channelType,
          campaign_type = CampaignType.Email
        )


      case data: CampaignTypeData.MultiChannelCampaignData =>

        helperFetchCampaignStepsData(
          head_step_id = data.head_step_id,
          campaignStepVariantDAO = campaignStepVariantDAO,
          campaign_id = CampaignId(id = c.campaign_id),
          enable_ab_testing = account.org.settings.enable_ab_testing,
          channelType = channelType,
          campaign_type = CampaignType.MultiChannel
        )

      case data: CampaignTypeData.DripCampaignData =>

        // nxd notes:  IOAction inside loop (flatmap)
        // this returns step of all channel / step-types
        val campaignSteps: Seq[CampaignStepWithChildren] = campaignStepVariantDAO.findByCampaignIdForSchedule(
          campaignId = c.campaign_id,
          isABTestingEnabled = account.org.settings.enable_ab_testing,
          logger = Logger
        )


        val stepsMappedById: Map[Long, CampaignStepWithChildren] = campaignSteps.map(s => s.id -> s).toMap

        val campaignStepData_for_logging: Seq[CampaignStepDataForLogging] = campaignSteps.map(cs =>
          CampaignStepDataForLogging(
            campaign_id = cs.campaign_id,
            label = cs.label,
            step_type = cs.step_type,
            id = cs.id)
        )

        val allCampaignSteps: Vector[SchedulerMapStepIdAndDelay] = ChannelSchedulerService.getMapOfStepIdAndRequiredDelayForDripCampaign(
          headStepId = data.head_node_id,
          edges = data.edges,
          nodes = data.nodes,
          stepsMappedById = stepsMappedById,
          //          from = "ChannelSchedulerTrait.fetchCampaignStepsData"
        )

        val campaignsStepsRelevantToChannel: Vector[SchedulerMapStepIdAndDelay] = ChannelSchedulerService.filterCampaignStepsRelevantToChannel(
          allCampaignSteps = allCampaignSteps,
          channelType = channelType
        )

        Success(FetchCampaignStepsData(
          stepsMappedById = stepsMappedById,
          campaignStepsStructure = CampaignStepsStructure.DripCampaignStepsStructure(
            edges = data.edges,
            nodes = data.nodes,
            head_node_id = data.head_node_id
          ),
          allCampaignSteps = allCampaignSteps,
          relevantCampaignStepsForChannel = campaignsStepsRelevantToChannel,
        ))

      case data: CampaignTypeData.MagicContentData => // For MagicContent
        helperFetchCampaignStepsData(
          head_step_id = data.head_step_id,
          campaignStepVariantDAO = campaignStepVariantDAO,
          campaign_id = CampaignId(id = c.campaign_id),
          enable_ab_testing = account.org.settings.enable_ab_testing,
          channelType = channelType,
          campaign_type = CampaignType.MagicContent
        )
    }
  }

  private def getDistinctStepTypesToBeScheduledInCampaign(
                                                           campaign_ids: List[Long],
                                                           campaignStepDAO: CampaignStepDAO
                                                         ): Map[Long, List[CampaignStepType]] = {

    campaignStepDAO.getDistinctStepTypesInCampaigns(
      campaign_ids = campaign_ids
    )

  }

  final def getDailyQuotaConsideringWarmup(
                                            warmupSettingOpt: Option[CampaignWarmupSetting],
                                            channelOrCampaignMinDailyQuota: Int,
                                          ): Int = {


    // if warm up, restrict to warm up emails limit


    val dailyQuota = if (warmupSettingOpt.isDefined) {

      val warmupSetting = warmupSettingOpt.get

      ChannelSchedulerTrait.getWarmupQuota(
        warmupSetting = warmupSetting,
        channelOrCampaignMinDailyQuota = channelOrCampaignMinDailyQuota,
      )

    } else channelOrCampaignMinDailyQuota

    dailyQuota
  }

  def getFinalDailyQuotaAlsoConsideringSenderRotationRoundRobin(
                                                                 warmupSettingOpt: Option[CampaignWarmupSetting],
                                                                 channelOrCampaignMinDailyQuota: Int,
                                                                 campaign: CampaignForSchedulingType,
                                                                 campaignProspectService: CampaignProspectService,
                                                                 campaignStepType: CampaignStepType,
                                                                 isFirstStep: Boolean
                                                               )(using Logger: SRLogger): Try[Int] = Try {
    getDailyQuotaConsideringWarmup(
      warmupSettingOpt = warmupSettingOpt,
      channelOrCampaignMinDailyQuota = channelOrCampaignMinDailyQuota
    )
  }

  final def computeChannelStepTypeDataForScheduling(
                                                     channelDataForScheduling: ChannelDataForSchedulingType,
                                                     campaignStepType: CampaignStepType,
                                                     stepType: StepType,
                                                     totalScheduledForStepTypeTillNow: Int,
                                                   ): ChannelStepTypeDataForScheduling = {

    val channelStepTypeDailyLimit: Int = getChannelTaskLimitPerDay(
      channelDataForScheduling = channelDataForScheduling,
      campaignStepType = campaignStepType
    )

    val campaignStepTypeLimitHasBeenReachedForToday = channelStepTypeDailyLimit <= totalScheduledForStepTypeTillNow

    // has to be a positive number
    val remainingToBeScheduledFromChannelStepType: Int = if (
      channelStepTypeDailyLimit - totalScheduledForStepTypeTillNow > 0
    ) {
      channelStepTypeDailyLimit - totalScheduledForStepTypeTillNow
    } else 0


    val data = ChannelStepTypeDataForScheduling(
      campaignStepType = campaignStepType,
      channelStepTypeDailyLimit = channelStepTypeDailyLimit,
      totalScheduledForStepTypeTillNow = totalScheduledForStepTypeTillNow,
      remainingToBeScheduledFromChannelStepType = remainingToBeScheduledFromChannelStepType,
      campaignStepTypeLimitHasBeenReachedForToday = campaignStepTypeLimitHasBeenReachedForToday,
      stepType = stepType
    )

    data
  }

  def flattenStepTypes(campaignStepTypesMap: Map[Long, List[CampaignStepType]]): Set[CampaignStepType] = {

    var allStepTypes: Set[CampaignStepType] = Set()

    campaignStepTypesMap.map {
      case (_: Long, campaignStepTypeList: List[CampaignStepType]) =>
        allStepTypes = Set.concat(allStepTypes, campaignStepTypeList.toSet)
    }

    allStepTypes

  }

  def computeChannelStepTypeDataForSchedulingForAllStepTypes(

                                                              totalScheduledFromChannelTillNowMap: Map[CampaignStepType, StepTypeAndCount],
                                                              channelDataForScheduling: ChannelDataForSchedulingType,

                                                            ): Map[CampaignStepType, ChannelStepTypeDataForScheduling] = {

    val channelStepTypeDataForScheduling = totalScheduledFromChannelTillNowMap
      .map { case (campaignStepType: CampaignStepType, totalScheduledForStepTypeTillNow) =>


        val data = computeChannelStepTypeDataForScheduling(
          campaignStepType = campaignStepType,
          totalScheduledForStepTypeTillNow = totalScheduledForStepTypeTillNow.count.count_value,
          stepType = totalScheduledForStepTypeTillNow.stepType,
          channelDataForScheduling = channelDataForScheduling,
        )

        (campaignStepType, data)

      }

    channelStepTypeDataForScheduling
  }

  def fetchLimitForChannel(
                            remainingToBeSentCountForStepType: Int,
                            maxToBeScheduledForNextHour: Int,
                            campaign: CampaignForSchedulingType
                          ): Int = {

    if (List(remainingToBeSentCountForStepType, maxToBeScheduledForNextHour).min < 0)
      0 else List(remainingToBeSentCountForStepType, maxToBeScheduledForNextHour).min
  }


  /*
   Drip Campaign Note:

     Gets Called Only At One place getProspectsByStepType in ChannelScheduler trait .
     and it is only called for the DripCampaignFlow inside this check
     case data: CampaignTypeData.DripCampaignData
  */
  private def getOverallAndFetchLimits(
                                        channelDataForScheduling: ChannelDataForSchedulingType,
                                        c: ChannelScheduledProspectsCountForCampaignType,
                                        campaignStepType: CampaignStepType,
                                        maxToBeScheduledForNextHour: Int,
                                        stepTypeLimit: ChannelStepTypeDataForScheduling,
                                        campaignProspectService: CampaignProspectService
                                      )(using Logger: SRLogger) = {

    val campaignDailyQuota: Int = getCampaignDailyLimitForChannel(
      channelCountData = c,
      channelDataForScheduling = channelDataForScheduling,
      campaignStepType = campaignStepType
    )

    val channelOrCampaignMinDailyQuota: Int = Math.min(stepTypeLimit.channelStepTypeDailyLimit, campaignDailyQuota)


    //  we need to extract this out of EmailChannelScheduler to ChannelScheduler and rename it to getDailyQuotaConsideringWarmup
    // NOTE MULTICHANNEL: We need to add channel limits for other channels. Currently we only have it for Email Channel

    // MULTICHANNEL_REFACTOR FIXME: need to look at this logic, what if first and only step is not from channel

    val dailyQuotaForFirstStep = getFinalDailyQuotaAlsoConsideringSenderRotationRoundRobin(
      warmupSettingOpt = c.campaign.softstart_setting,
      channelOrCampaignMinDailyQuota = channelOrCampaignMinDailyQuota,
      campaign = c.campaign,
      campaignProspectService = campaignProspectService,
      campaignStepType = campaignStepType,
      isFirstStep = true
    ).get // fixme: remove .get

    val dailyQuotaForFollowupStep = getFinalDailyQuotaAlsoConsideringSenderRotationRoundRobin(
      warmupSettingOpt = c.campaign.softstart_setting,
      channelOrCampaignMinDailyQuota = channelOrCampaignMinDailyQuota,
      campaign = c.campaign,
      campaignProspectService = campaignProspectService,
      campaignStepType = campaignStepType,
      isFirstStep = false
    ).get // fixme: remove .get

    val totalTasksDoneOrScheduledFromCampaignToday = countTotalTasksDoneOrScheduledFromCampaignToday(
      channelScheduledProspectsCountForCampaign = c,
      campaignStepType = campaignStepType
    )

    //Logger.info(s"isThisChannelFirstStep: cid_${campaign.campaign_id} step_type_${schedulingForStepType.channelStepType}, step_id_${schedulingForStepType}  opt_first_step_${fetchCampaignStepsDataResult.allCampaignSteps.headOption}")
    Logger.debug(s"After getDailyQuotaConsideringWarmup dailyQuotaForFirstStep: ${dailyQuotaForFirstStep} dailyQuotaForFollowupStep: ${dailyQuotaForFollowupStep} Before getRemainingToBeSentCountForTheDay cid_${c.campaign.campaign_id} channel_type_${campaignStepType.channelType}, channel_step_type_${campaignStepType.channelStepType} ")

    val remainingToBeSentCountForStepTypeForFirstStep: Int = getRemainingToBeSentCountForTheDay(
      remainingToBeSentCountFromCampaign = dailyQuotaForFirstStep - totalTasksDoneOrScheduledFromCampaignToday.firstSteps,
      //dailyQuota = dailyQuota,
      //channelLevelTaskCountLimitPerDay = channelDailyTaskLimit,
      remainingToBeSentCountForChannelStepType = stepTypeLimit.remainingToBeScheduledFromChannelStepType,

      //totalChannelTasksScheduledCount = totalScheduledFromChannelTillNow,
      //totalTasksDoneOrScheduledFromCampaignToday = totalTasksDoneOrScheduledFromCampaignToday,

      Logger = Logger
    )

    val remainingToBeSentCountForStepTypeForFollowupStep: Int = getRemainingToBeSentCountForTheDay(
      remainingToBeSentCountFromCampaign = dailyQuotaForFollowupStep - totalTasksDoneOrScheduledFromCampaignToday.followUpSteps,
      //dailyQuota = dailyQuota,
      //channelLevelTaskCountLimitPerDay = channelDailyTaskLimit,
      remainingToBeSentCountForChannelStepType = stepTypeLimit.remainingToBeScheduledFromChannelStepType,

      //totalChannelTasksScheduledCount = totalScheduledFromChannelTillNow,
      //totalTasksDoneOrScheduledFromCampaignToday = totalTasksDoneOrScheduledFromCampaignToday,

      Logger = Logger
    )
    //        Logger.debug(s"After getRemainingToBeSentCountForTheDay $remainingToBeSentCountForStepType Before prospectsFoundForCampaign")

    val fetchLimitForFirstStep = fetchLimitForChannel(
      remainingToBeSentCountForStepType = remainingToBeSentCountForStepTypeForFirstStep,
      maxToBeScheduledForNextHour = maxToBeScheduledForNextHour,
      campaign = c.campaign
    )

    val fetchLimitForFollowup = fetchLimitForChannel(
      remainingToBeSentCountForStepType = remainingToBeSentCountForStepTypeForFollowupStep,
      maxToBeScheduledForNextHour = maxToBeScheduledForNextHour,
      campaign = c.campaign
    )

    //when it is not first step we are taking the dailyQuota as if no sender rotation
    val overallDailyQuota: Int = dailyQuotaForFollowupStep

    val overallFetchLimit: Int = {

      val overallTotalTasksDoneOrScheduledFromCampaignToday = totalTasksDoneOrScheduledFromCampaignToday.firstSteps + totalTasksDoneOrScheduledFromCampaignToday.followUpSteps

      val overallRemainingToBeSentCountForStepType = getRemainingToBeSentCountForTheDay(

        remainingToBeSentCountFromCampaign = overallDailyQuota - overallTotalTasksDoneOrScheduledFromCampaignToday,

        remainingToBeSentCountForChannelStepType = stepTypeLimit.remainingToBeScheduledFromChannelStepType,

        Logger = Logger
      )

      val overallFetchLimitCalc = fetchLimitForChannel(
        remainingToBeSentCountForStepType = overallRemainingToBeSentCountForStepType,
        maxToBeScheduledForNextHour = maxToBeScheduledForNextHour,
        campaign = c.campaign
      )

      overallFetchLimitCalc
    }

    OverallAndFetchLimits(
      fetchLimitForFollowup = fetchLimitForFollowup,
      fetchLimitForFirstStep = fetchLimitForFirstStep,
      overallFetchLimit = overallFetchLimit,
      overallDailyQuota = overallDailyQuota
    )

  }


  /**
   *
   * Drip Campaign Note :
   * This is called at 3 places but all inside one function  ChannelSchedulerTrait.getProspectsByStepType
   * and all 3 calls are inside the drip campaign check that is
   *
   * case data: CampaignTypeData.DripCampaignData =>
   *
   * so we are good here .
   *
   *
   * For each prospect we find the next StepId here ,
   * so if the current step id for prospect is NULL we get the headstep
   * else we find the next step.
   *
   * Ultimately we are trying to get Next step  against  each prosepect id  which matches the condition .
   *
    * revirified on 24-jan-2025:
    *
    * its called from
    * 1. line : 3093 -> ChannelSchedulerTrait under drip Campaign type at 3067
    * 2. line : 3128 -> ChannelSchedulerTrait under drip Campaign type at 3067
    * 3. line : 3197 -> ChannelSchedulerTrait under drip Campaign type at 3067
   *
   *
   * @param prospects
   * @param campaignTypeData
   * @param stepsMappedById
   * @param campaignHeadStepId
   * @param ec
   * @return
   */
  private def createProspectIdWithNextStepIdMap(
                                                 prospects: List[ProspectDataForChannelSchedulingType],
                                                 campaignTypeData: CampaignTypeData.DripCampaignData,
                                                 markCompletedAfterDays: Int,
                                                 stepsMappedById: Map[Long, CampaignStepWithChildren],
                                                 campaignHeadStepId: String,
                                                 campaignId: CampaignId,
                                                 campaignProspectDAO: CampaignProspectDAO,
                                                 campaignProspectService: CampaignProspectService
                                               )(implicit ec: ExecutionContext, Logger: SRLogger): Map[ProspectId, CampaignStepWithChildren] = {

    var prospectIdWithStepIdMap: Map[ProspectId, CampaignStepWithChildren] = Map()

    prospects.foreach(p => {
      if(campaignProspectService.getCampaignsToLogDripFor().contains(campaignId)) {
        Logger.doNotTruncate(s"[drip_debug_Balaji] createProspectIdWithNextStepIdMap ENTER current_prospect_id: ${p.prospect.id} current_step_id: ${p.current_step_id}")
      }

      val stepsWithConditionsFut = p.current_step_id match {
        case Some(stepId) =>
          //          println(s"stepId ------- $stepId")
          NextStepFinderForDrip.findNextStep(
            edges = campaignTypeData.edges,
            nodes = campaignTypeData.nodes,
            parent_step_id = stepId
          )

        case None =>
          NextStepFinderForDrip.getHeadStepsWithCondition(
            edges = campaignTypeData.edges,
            nodes = campaignTypeData.nodes,
            head_node_id = campaignHeadStepId,
            //            pid = p.prospect.id,
            //            cid = campaignId.id,
            //            from = "ChannelSchedulerTrait.createProspectIdWithNextStepIdMap"
          )
      }

      //FIXME drip since this is happening inside the foreach there will be slowness as we will wait for each result individually
      Await.ready(stepsWithConditionsFut, 60000.millis).value match {

        case None =>

          Logger.error(
            msg = s"createProspectIdWithNextStepIdMap - Failed stepsWithConditionsFut. edges: ${campaignTypeData.edges} :: nodes: ${campaignTypeData.nodes} :: stepId: ${p.current_step_id} :: campaignHeadStepId: $campaignHeadStepId"
          )

        case Some(Failure(exception)) =>

          Logger.error(
            msg = s"createProspectIdWithNextStepIdMap - Failed stepsWithConditionsFut. edges: ${campaignTypeData.edges} :: nodes: ${campaignTypeData.nodes} :: stepId: ${p.current_step_id} :: campaignHeadStepId: $campaignHeadStepId",
            err = exception,
          )

        case Some(Success(stepsWithConditions)) =>

          if (campaignProspectService.getCampaignsToLogDripFor().contains(campaignId)) {
            Logger.doNotTruncate(s"[drip_debug_Balaji] createProspectIdWithNextStepIdMap stepsWithConditions: $stepsWithConditions")
          }

          val foundStep = stepsWithConditions.find(sc => {
            val stepData = stepsMappedById(sc.child_step_id)
            val lastSentSteps = campaignProspectDAO.getPreviouslySentStepsForProspect(
              prospectId = ProspectId(p.prospect.id),
              teamId = TeamId(p.prospect.team_id),
              campaignId = campaignId
            ).get
            if (campaignProspectService.getCampaignsToLogDripFor().contains(campaignId)) {
              Logger.doNotTruncate(s"[drip_debug_Balaji] createProspectIdWithNextStepIdMap sc: $sc lastSentSteps: $lastSentSteps stepData: $stepData")
            }

            val isProspectConnectedToLinkedin: Boolean = campaignProspectService.getLinkedInProfileConnectedUuid(
              campaignId = campaignId,
              linkedinProfileUrl = p.prospect.linkedin_url,
              teamId = TeamId(p.prospect.team_id)
            ).get.isDefined

            ChannelSchedulerService.matchMultipleConditionsWithProspect(
              prospectObject = p.prospect,
              conditions = sc.condition,
              lastSentSteps = lastSentSteps,
              isProspectConnectedToLinkedin = isProspectConnectedToLinkedin,
              stepData = stepData
            ) match {
              case Left(value) =>
                //            println(s"values ------- $value")
                campaignProspectService.addIgnoreLogAndUpdateNextCheckForSchedulingAt(
                  prospects = List((p, value.next_check_for_scheduling_at, value)),
                  campaignId = campaignId,
                  head_step_id = campaignHeadStepId,
                  teamId = TeamId(p.prospect.team_id),
                )

                false
              case Right(value) =>
                //            println(s"values ------- $value")
                value
            }
          })

          //      println(s"foundStep1 --- $foundStep")
          foundStep match {
            case None =>
              if (campaignProspectService.getCampaignsToLogDripFor().contains(campaignId)) {
                Logger.doNotTruncate(s"[drip_debug_Balaji] createProspectIdWithNextStepIdMap EXIT foundStep: None")
              }
              //Do Nothing
              campaignProspectService.addIgnoreLogAndUpdateNextCheckForSchedulingAt(
                prospects = List((p, DateTime.now().plusHours(1), RejectionReasonForCampaignProspectStepSchedule.NoStepMatched(DateTime.now().plusHours(1)))),
                campaignId = campaignId,
                head_step_id = campaignHeadStepId,
                teamId = TeamId(p.prospect.team_id),
              )

            case Some(stepWithConditions) =>

              if (campaignProspectService.getCampaignsToLogDripFor().contains(campaignId)) {
                Logger.doNotTruncate(s"[drip_debug_Balaji] createProspectIdWithNextStepIdMap EXIT foundStep: $stepWithConditions")
              }
              prospectIdWithStepIdMap += ProspectId(p.prospect.id) -> stepsMappedById(stepWithConditions.child_step_id)
          }
      }

    })
    //    println(s"prospectIdWithStepIdMap1 --- $prospectIdWithStepIdMap")

    prospectIdWithStepIdMap
  }

  def getProspectsByStepType(
                              channelDataForScheduling: ChannelDataForSchedulingType,
                              stepTypesThatCanBeScheduledByCampaign: Map[CampaignStepType, ChannelStepTypeDataForScheduling],
                              c: ChannelScheduledProspectsCountForCampaignType,
                              maxToBeScheduledForNextHour: Int,
                              scheduleFromTime: DateTime,
                              fetchCampaignStepsDataResult: FetchCampaignStepsData,
                              allowedTimezones: Try[Set[String]],
                              sendOnlyToProspectsWhoWereSentInCurrentCycle: Option[DateTime],
                              campaignProspectDAO: CampaignProspectDAO,
                              campaignProspectService: CampaignProspectService,
                              channelData: ChannelData,
                              campaign: CampaignForSchedulingType,
                              srRollingUpdateCoreService: SrRollingUpdateCoreService
                            )(using Logger: utils.SRLogger, ec: ExecutionContext): Try[ProspectsFoundByStepType] = Try {

    var prospectsByStepType: ProspectsFoundByStepType = Map()

    c.campaign.campaign_type_data match {
      case _: CampaignTypeData.MultiChannelCampaignData | _: CampaignTypeData.EmailChannelData | _: CampaignTypeData.MagicContentData =>
        stepTypesThatCanBeScheduledByCampaign
          .map { case (campaignStepType: CampaignStepType, stepTypeLimit: ChannelStepTypeDataForScheduling) =>

            val campaignDailyQuota: Int = getCampaignDailyLimitForChannel(
              channelCountData = c,
              channelDataForScheduling = channelDataForScheduling,
              campaignStepType = campaignStepType
            )

            val channelOrCampaignMinDailyQuota: Int = Math.min(stepTypeLimit.channelStepTypeDailyLimit, campaignDailyQuota)


            //  we need to extract this out of EmailChannelScheduler to ChannelScheduler and rename it to getDailyQuotaConsideringWarmup
            // NOTE MULTICHANNEL: We need to add channel limits for other channels. Currently we only have it for Email Channel

            // MULTICHANNEL_REFACTOR FIXME: need to look at this logic, what if first and only step is not from channel

            val dailyQuotaForFirstStep = getFinalDailyQuotaAlsoConsideringSenderRotationRoundRobin(
              warmupSettingOpt = c.campaign.softstart_setting,
              channelOrCampaignMinDailyQuota = channelOrCampaignMinDailyQuota,
              campaign = campaign,
              campaignProspectService = campaignProspectService,
              campaignStepType = campaignStepType,
              isFirstStep = true
            ).get // fixme: remove .get

            val dailyQuotaForFollowupStep = getFinalDailyQuotaAlsoConsideringSenderRotationRoundRobin(
              warmupSettingOpt = c.campaign.softstart_setting,
              channelOrCampaignMinDailyQuota = channelOrCampaignMinDailyQuota,
              campaign = campaign,
              campaignProspectService = campaignProspectService,
              campaignStepType = campaignStepType,
              isFirstStep = false
            ).get // fixme: remove .get

            val totalTasksDoneOrScheduledFromCampaignToday = countTotalTasksDoneOrScheduledFromCampaignToday(
              channelScheduledProspectsCountForCampaign = c,
              campaignStepType = campaignStepType
            )

            //Logger.info(s"isThisChannelFirstStep: cid_${campaign.campaign_id} step_type_${schedulingForStepType.channelStepType}, step_id_${schedulingForStepType}  opt_first_step_${fetchCampaignStepsDataResult.allCampaignSteps.headOption}")
            Logger.debug(s"After getDailyQuotaConsideringWarmup dailyQuotaForFirstStep: ${dailyQuotaForFirstStep} dailyQuotaForFollowupStep: ${dailyQuotaForFollowupStep} Before getRemainingToBeSentCountForTheDay cid_${c.campaign.campaign_id} channel_type_${campaignStepType.channelType}, channel_step_type_${campaignStepType.channelStepType} ")

            val remainingToBeSentCountForStepTypeForFirstStep: Int = getRemainingToBeSentCountForTheDay(
              remainingToBeSentCountFromCampaign = dailyQuotaForFirstStep - totalTasksDoneOrScheduledFromCampaignToday.firstSteps,
              //dailyQuota = dailyQuota,
              //channelLevelTaskCountLimitPerDay = channelDailyTaskLimit,
              remainingToBeSentCountForChannelStepType = stepTypeLimit.remainingToBeScheduledFromChannelStepType,

              //totalChannelTasksScheduledCount = totalScheduledFromChannelTillNow,
              //totalTasksDoneOrScheduledFromCampaignToday = totalTasksDoneOrScheduledFromCampaignToday,

              Logger = Logger
            )

            val remainingToBeSentCountForStepTypeForFollowupStep: Int = getRemainingToBeSentCountForTheDay(
              remainingToBeSentCountFromCampaign = dailyQuotaForFollowupStep - totalTasksDoneOrScheduledFromCampaignToday.followUpSteps,
              //dailyQuota = dailyQuota,
              //channelLevelTaskCountLimitPerDay = channelDailyTaskLimit,
              remainingToBeSentCountForChannelStepType = stepTypeLimit.remainingToBeScheduledFromChannelStepType,

              //totalChannelTasksScheduledCount = totalScheduledFromChannelTillNow,
              //totalTasksDoneOrScheduledFromCampaignToday = totalTasksDoneOrScheduledFromCampaignToday,

              Logger = Logger
            )
            //        Logger.debug(s"After getRemainingToBeSentCountForTheDay $remainingToBeSentCountForStepType Before prospectsFoundForCampaign")

            val fetchLimitForFirstStep = fetchLimitForChannel(
              remainingToBeSentCountForStepType = remainingToBeSentCountForStepTypeForFirstStep,
              maxToBeScheduledForNextHour = maxToBeScheduledForNextHour,
              campaign = c.campaign
            )

            val fetchLimitForFollowup = fetchLimitForChannel(
              remainingToBeSentCountForStepType = remainingToBeSentCountForStepTypeForFollowupStep,
              maxToBeScheduledForNextHour = maxToBeScheduledForNextHour,
              campaign = c.campaign
            )

            //when it is not first step we are taking the dailyQuota as if no sender rotation
            val overallDailyQuota: Int = dailyQuotaForFollowupStep

            val overallFetchLimit: Int = {

              val overallTotalTasksDoneOrScheduledFromCampaignToday = totalTasksDoneOrScheduledFromCampaignToday.firstSteps + totalTasksDoneOrScheduledFromCampaignToday.followUpSteps

              val overallRemainingToBeSentCountForStepType = getRemainingToBeSentCountForTheDay(

                remainingToBeSentCountFromCampaign = overallDailyQuota - overallTotalTasksDoneOrScheduledFromCampaignToday,

                remainingToBeSentCountForChannelStepType = stepTypeLimit.remainingToBeScheduledFromChannelStepType,

                Logger = Logger
              )

              val overallFetchLimitCalc = fetchLimitForChannel(
                remainingToBeSentCountForStepType = overallRemainingToBeSentCountForStepType,
                maxToBeScheduledForNextHour = maxToBeScheduledForNextHour,
                campaign = c.campaign
              )

              overallFetchLimitCalc
            }


            val isThisChannelFirstStep = isCampaignFirstStep(
              fetchCampaignStepsDataResult = fetchCampaignStepsDataResult,
              schedulingForStepType = campaignStepType,
              channelType = channelData.channelType
            )

            val prospectsFoundForCampaignInternal: List[ProspectDataForChannelSchedulingType] = getProspectsToCreateTasks(
              fetchLimitForFirstStep = fetchLimitForFirstStep,
              fetchLimitForFollowup = fetchLimitForFollowup,
              scheduleFromTime = scheduleFromTime,
              campaign = c.campaign,

              allowedTimezones = allowedTimezones,

              fetchCampaignStepsDataResult = fetchCampaignStepsDataResult,

              schedulingForStepType = campaignStepType,

              sendOnlyToProspectsWhoWereSentInCurrentCycle = sendOnlyToProspectsWhoWereSentInCurrentCycle,

              campaignProspectDAO = campaignProspectDAO,
              campaignProspectService = campaignProspectService,
              channelType = channelData.channelType,
              isThisChannelFirstStep = isThisChannelFirstStep,
              org_id = channelDataForScheduling.channelOrgId,
              srRollingUpdateCoreService = srRollingUpdateCoreService
            ) match {
              case Failure(err) =>
                Logger.error(s" scheduleEmailAccount Unable to get prospects: from getProspectsToCreateTasks :: err -", err)
                throw err
              case Success(getProspectsToCreateTasksResult) =>

                //Logger.debug(s" getProspectsToCreateTasks Success: c_id:${c.campaign.campaign_id} getProspectsToCreateTasksResult : foundNewProspects: ${getProspectsToCreateTasksResult.foundNewProspects.map(_.prospect.id)} :: foundFollowUpProspects: ${getProspectsToCreateTasksResult.foundFollowUpProspects.map(_.prospect.id)} ")
                Logger.debug(s" getProspectsToCreateTasks Success: c_id:${c.campaign.campaign_id} getProspectsToCreateTasksResult : foundNewProspects: ${getProspectsToCreateTasksResult.foundNewProspects.size} :: foundFollowUpProspects: ${getProspectsToCreateTasksResult.foundFollowUpProspects.size} ")

                val foundNewProspects = getProspectsToCreateTasksResult.foundNewProspects
                val foundFollowUpProspects = getProspectsToCreateTasksResult.foundFollowUpProspects

                val campaign = c.campaign
                val channelCountData = ChannelSchedulerTrait.findScheduledProspectsCountForStepType(
                  campaignId = campaign.campaign_id,
                  campaignStepType = campaignStepType,
                  campaignCounts = c.counts
                )

                // FIXME MULTICHANNEL LATER: this should live in some other place
                val prospects = EmailChannelScheduler.takeProspectsBasedOnEmailPriority(
                  foundFollowUpProspects = foundFollowUpProspects,
                  foundNewProspects = foundNewProspects,
                  dailyQuota = overallDailyQuota,
                  fetchLimitForFirstStep = fetchLimitForFirstStep,
                  fetchLimitForFollowupStep = fetchLimitForFollowup,
                  overallFetchLimit = overallFetchLimit,
                  followUpCampaignSentCount = channelCountData.followupCount,
                  newCampaignSentCount = channelCountData.newCount,
                  campaignEmailPriority = campaign.email_priority,
                  campaignId = campaign.campaign_id,
                  logger = Logger
                )

                Logger.info(s"found Total prospects after priority: ${prospects.size} :: maxToBeScheduledForNextHour: $maxToBeScheduledForNextHour ::  dailyQuotaForFirstStep: $dailyQuotaForFirstStep :: dailyQuotaForFollowupStep: $dailyQuotaForFollowupStep :: remainingToBeSentCountForStepTypeForFollowupStep: $remainingToBeSentCountForStepTypeForFollowupStep :: remainingToBeSentCountForStepTypeForFirstStep: $remainingToBeSentCountForStepTypeForFirstStep")

                prospects
            }

            val prospectsFoundForSchedulingByStepType = ProspectsFoundForSchedulingByStepType(
              prospects = prospectsFoundForCampaignInternal,
              step_type = stepTypeLimit
            )

            prospectsByStepType += (campaignStepType -> prospectsFoundForSchedulingByStepType)

            prospectsByStepType
          }

      case data: CampaignTypeData.DripCampaignData =>
        getProspectsToCreateTasks(
          fetchLimitForFirstStep = 200, // While loop inside this will run once for drip which will fetch all prospects
          fetchLimitForFollowup = 200,
          scheduleFromTime = scheduleFromTime,
          campaign = c.campaign,

          allowedTimezones = allowedTimezones,

          fetchCampaignStepsDataResult = fetchCampaignStepsDataResult,

          schedulingForStepType = CampaignStepType.AutoEmailStep, // Doesn't matter for Drip

          sendOnlyToProspectsWhoWereSentInCurrentCycle = sendOnlyToProspectsWhoWereSentInCurrentCycle,

          campaignProspectDAO = campaignProspectDAO,
          campaignProspectService = campaignProspectService,
          channelType = channelData.channelType,
          isThisChannelFirstStep = true,
          org_id = channelDataForScheduling.channelOrgId,
          srRollingUpdateCoreService = srRollingUpdateCoreService
        )
          .map(prospectsToCreateTasksResult => {
            val campaign = c.campaign

            val unfilteredFoundNewProspects = prospectsToCreateTasksResult.foundNewProspects

            // we find the map of prospect id against the nextStepId which matches the give condition in the Drip
            val newProspectIdAndNextStepIdMap: Map[ProspectId, CampaignStepWithChildren] = createProspectIdWithNextStepIdMap(
              prospects = unfilteredFoundNewProspects,
              campaignTypeData = data,
              stepsMappedById = fetchCampaignStepsDataResult.stepsMappedById,
              campaignHeadStepId = data.head_node_id,
              campaignProspectDAO = campaignProspectDAO,
              campaignId = CampaignId(campaign.campaign_id),
              markCompletedAfterDays = c.campaign.mark_completed_after_days,
              campaignProspectService = campaignProspectService
            )

            if(campaignProspectService.getCampaignsToLogDripFor().map(_.id).contains(c.campaign.campaign_id)){
              Logger.doNotTruncate(s"[drip_debug_Balaji] channel_type: ${channelData.channelType} newProspectIdAndNextStepIdMap => ${newProspectIdAndNextStepIdMap.map(d => (s" Prospect_id: ${d._1.id}, next_step_id: ${d._2.id}, step_type: ${d._2.step_type} "))}")
            }
            //            println(s"newProspectIdAndNextStepIdMap2 => $newProspectIdAndNextStepIdMap")

            // We are filtering out all the prospect where the channel type matches is being matched with the scheduler
            // channel type.
            // ie. we keep only the prospect which has a next step with this scheduler can schedule
            val foundNewProspects = unfilteredFoundNewProspects.filter(p => {

              newProspectIdAndNextStepIdMap
                .get(ProspectId(p.prospect.id)) //Option[CampaignStepWithChildren]
                .map(_.step_type.channelType) // Option[ChannelType]
                .contains(channelData.channelType) //Bool
            })

            if(campaignProspectService.getCampaignsToLogDripFor().map(_.id).contains(c.campaign.campaign_id)){
              Logger.doNotTruncate(s"[drip_debug_Balaji] channel_type: ${channelData.channelType} foundNewProspects: $foundNewProspects")
            }

            val unfilteredFoundFollowUpProspects = prospectsToCreateTasksResult.foundFollowUpProspects
            // we find the map of prospect id against the nextStepId  for the follow up prospects which matches the give condition in the Drip

            val followupProspectIdAndNextStepIdMap: Map[ProspectId, CampaignStepWithChildren] = createProspectIdWithNextStepIdMap(
              prospects = unfilteredFoundFollowUpProspects,
              campaignTypeData = data,
              stepsMappedById = fetchCampaignStepsDataResult.stepsMappedById,
              campaignHeadStepId = data.head_node_id,
              campaignProspectDAO = campaignProspectDAO,
              campaignId = CampaignId(campaign.campaign_id),
              markCompletedAfterDays = c.campaign.mark_completed_after_days,
              campaignProspectService = campaignProspectService
            )

            if(campaignProspectService.getCampaignsToLogDripFor().map(_.id).contains(c.campaign.campaign_id)) {
              Logger.doNotTruncate(s"[drip_debug_Balaji] channel_type: ${channelData.channelType} followupProspectIdAndNextStepIdMap => ${followupProspectIdAndNextStepIdMap.map(d => (s" Prospect_id: ${d._1.id}, next_step_id: ${d._2.id}, step_type: ${d._2.step_type} "))}")
            }

            //            println(s"followupProspectIdAndNextStepIdMap3 => $followupProspectIdAndNextStepIdMap")

            // We are filtering out all the prospect where the channel type matches is being matched with the scheduler
            // channel type.
            // ie. we keep only the prospect which has a next step with this scheduler can schedule

            val foundFollowUpProspects = unfilteredFoundFollowUpProspects.filter(p => {
              followupProspectIdAndNextStepIdMap
                .get(ProspectId(p.prospect.id)) //Option[CampaignStepWithChildren]
                .map(_.step_type.channelType) // Option[ChannelType]
                .contains(channelData.channelType) //Bool
            })

            if(campaignProspectService.getCampaignsToLogDripFor().map(_.id).contains(c.campaign.campaign_id)){
              Logger.doNotTruncate(s"[drip_debug_Balaji] channel_type: ${channelData.channelType} foundFollowUpProspects: $foundFollowUpProspects")
            }


            /* FIXME DRIP: Not Getting used anywhere we can check in the follow up prs */
            val prospectIdAndNextStepIdMap: Map[ProspectId, CampaignStepWithChildren] = newProspectIdAndNextStepIdMap ++ followupProspectIdAndNextStepIdMap

            var acceptedProspects: List[ProspectDataForChannelSchedulingType] = List()

            stepTypesThatCanBeScheduledByCampaign.foreach {
              case (campaignStepType: CampaignStepType, stepTypeLimit: ChannelStepTypeDataForScheduling) =>
                val overallAndFetchLimits = getOverallAndFetchLimits(
                  channelDataForScheduling = channelDataForScheduling,
                  c = c,
                  campaignStepType = campaignStepType,
                  maxToBeScheduledForNextHour = maxToBeScheduledForNextHour,
                  stepTypeLimit = stepTypeLimit,
                  campaignProspectService = campaignProspectService
                )

                val channelCountData = ChannelSchedulerTrait.findScheduledProspectsCountForStepType(
                  campaignId = campaign.campaign_id,
                  campaignStepType = campaignStepType,
                  campaignCounts = c.counts
                )

                // FIXME MULTICHANNEL LATER: this should live in some other place
                val prospects = EmailChannelScheduler.takeProspectsBasedOnEmailPriority(
                  foundFollowUpProspects = foundFollowUpProspects,
                  foundNewProspects = foundNewProspects,
                  dailyQuota = overallAndFetchLimits.overallDailyQuota,
                  fetchLimitForFirstStep = overallAndFetchLimits.fetchLimitForFirstStep,
                  fetchLimitForFollowupStep = overallAndFetchLimits.fetchLimitForFollowup,
                  overallFetchLimit = overallAndFetchLimits.overallFetchLimit,
                  followUpCampaignSentCount = channelCountData.followupCount,
                  newCampaignSentCount = channelCountData.newCount,
                  campaignEmailPriority = campaign.email_priority,
                  campaignId = campaign.campaign_id,
                  logger = Logger
                )

                val prospectIdAndNextStepIdMap: Map[ProspectId, CampaignStepWithChildren] = createProspectIdWithNextStepIdMap(
                  prospects = prospects,
                  campaignTypeData = data,
                  stepsMappedById = fetchCampaignStepsDataResult.stepsMappedById,
                  campaignHeadStepId = data.head_node_id,
                  campaignProspectDAO = campaignProspectDAO,
                  campaignId = CampaignId(campaign.campaign_id),
                  markCompletedAfterDays = c.campaign.mark_completed_after_days,
                  campaignProspectService = campaignProspectService
                )

                //                println(s"prospectIdAndNextStepIdMap4 => $prospectIdAndNextStepIdMap")

                if (campaignStepType.channelType == channelData.channelType) {

                  val prospectsAfterLimitCheck = prospects.filter(p => {
                    prospectIdAndNextStepIdMap(ProspectId(p.prospect.id)).step_type == campaignStepType
                  })

                  //                  println(s"prospectsAfterLimitCheck => $prospectsAfterLimitCheck")

                  val prospectsAfterDelayCheck = if (campaign.campaign_type_data.campaign_type == CampaignType.Drip) {
                    prospectsAfterLimitCheck
                  } else {
                    prospectsAfterLimitCheck.filter(p => {
                      p.current_step_status_data match {
                        case Some(value) =>
                          value match {
                            case _: CurrentStepStatusForSchedulerData.Due => false

                            case doneStatusData: CurrentStepStatusForSchedulerData.Done =>
                              DateTime.now().isAfter(doneStatusData.done_at.plusSeconds(prospectIdAndNextStepIdMap(ProspectId(p.prospect.id)).delay))

                            case skippedStatusData: CurrentStepStatusForSchedulerData.Skipped =>
                              DateTime.now().isAfter(skippedStatusData.skipped_at.plusSeconds(prospectIdAndNextStepIdMap(ProspectId(p.prospect.id)).delay))

                            case pendingApprovalData: CurrentStepStatusForSchedulerData.PendingApproval =>
                              // We will filter out the prospects which are in pending_approval state
                              false

                            case approvedData: CurrentStepStatusForSchedulerData.Approved =>

                              true

                            case inQueueData: CurrentStepStatusForSchedulerData.AiContentQueued =>
                              // We will filter out the prospects which are in inQueueData state
                              false
                          }

                        case None => true
                      }
                    })
                  }

                  //                  println(s"prospectsAfterDelayCheck => $prospectsAfterDelayCheck")

                  val limitedProspects = prospectsAfterDelayCheck
                    .filter(_.current_step_id.isEmpty)
                    .take(overallAndFetchLimits.fetchLimitForFirstStep) ++
                    prospectsAfterDelayCheck.filter(_.current_step_id.isDefined)
                      .take(overallAndFetchLimits.fetchLimitForFollowup)

                  //                  println(s"limitedProspects => $limitedProspects")

                  acceptedProspects = acceptedProspects ++ limitedProspects

                  prospectsByStepType += (campaignStepType -> ProspectsFoundForSchedulingByStepType(
                    prospects = limitedProspects,
                    step_type = stepTypeLimit
                  ))
                }

            }

            val allProspects = foundNewProspects ++ foundFollowUpProspects

            val eliminatedProspectIds = allProspects.map(_.prospect.id).filter(p => {
              !acceptedProspects.map(_.prospect.id).contains(p)
            })

            campaignProspectDAO.updateNextCheckForSchedulingAt(
              data = eliminatedProspectIds.map { p =>
                UpdateNextScheduleAtData(
                  campaignId = CampaignId(campaign.campaign_id),
                  prospectId = ProspectId(p),
                  teamId = TeamId(campaign.team_id),
                  nexToBeCheckedAt = ChannelSchedulerTrait.nextCheckForSchedulingIntervalForCampaignProspect(
                    nextCheckForSchedulingIntervalType = NextCheckForSchedulingIntervalType.fromCampaignType(campaignType = campaign.campaign_type_data.campaign_type),
                  )
                )
              }

            ).get
          })

    }

    prospectsByStepType

  }

  def getProspectsByStepTypeForCampaign(
                                         allowedTimezones: Try[Set[String]],
                                         c: ChannelScheduledProspectsCountForCampaignType,
                                         channelDataForScheduling: ChannelDataForSchedulingType,
                                         stepTypesThatCanBeScheduled: Map[CampaignStepType, ChannelStepTypeDataForScheduling],
                                         allStepTypesToBeScheduledByCampaign: Map[Long, List[CampaignStepType]],
                                         maxToBeScheduledForNextHour: Int,
                                         scheduleFromTime: DateTime,
                                         fetchCampaignStepsDataResult: FetchCampaignStepsData,
                                         sendOnlyToProspectsWhoWereSentInCurrentCycle: Option[DateTime],
                                         channelData: ChannelData,
                                         campaignProspectDAO: CampaignProspectDAO,
                                         campaignProspectService: CampaignProspectService,
                                         calendarAppService: CalendarAppService,
                                         srRollingUpdateCoreService: SrRollingUpdateCoreService
                                       )(using logger: SRLogger, ec: ExecutionContext): ProspectsFoundByStepType = {


    val stepTypesThatCanBeScheduledByCampaign = stepTypesThatCanBeScheduled
      .filter(stepTypesAndChannelData => allStepTypesToBeScheduledByCampaign(c.campaign.campaign_id).contains(stepTypesAndChannelData._1))

    //val touchTypeDailyTaskLimit : Map[AbstractChannelStepType, Int] = Map()

    // touchtype multichannel : notes :
    //      this will not be touched because it is independent of the
    //      touchtype and setup by the customer for the campaign.
    // Here someone is saying - for this campaign  max 50 emails should go
    // Here someone is saying - for this campaign  max 20 view profile actions should be made

    //                              Logger.debug(s"Before EmailChannelScheduler.getDailyQuotaConsideringEmailWarmup: settings -> ${c.campaign.softstart_setting}")

    //                              Logger.debug(s"After campaignDailyQuota : $campaignDailyQuota")


    // === begin of extraction getProspectsByStepType ===

    val prospectsByStepType: ProspectsFoundByStepType = getProspectsByStepType(
      channelDataForScheduling = channelDataForScheduling,
      stepTypesThatCanBeScheduledByCampaign = stepTypesThatCanBeScheduledByCampaign,
      c = c,
      maxToBeScheduledForNextHour = maxToBeScheduledForNextHour,
      scheduleFromTime = scheduleFromTime,
      fetchCampaignStepsDataResult = fetchCampaignStepsDataResult,
      allowedTimezones = allowedTimezones,
      sendOnlyToProspectsWhoWereSentInCurrentCycle = sendOnlyToProspectsWhoWereSentInCurrentCycle,
      campaignProspectDAO = campaignProspectDAO,
      channelData = channelData,
      campaign = c.campaign,
      campaignProspectService = campaignProspectService,
      srRollingUpdateCoreService = srRollingUpdateCoreService
    )
      .get


    prospectsByStepType
    // === end of extraction ===

  }

  private def channelLevelCheckAndValidation(
                                              channelData: ChannelData,
                                              teamId: Long,
                                              cacheIdForChannelLevelLimit: CacheIdKeyForLock,
                                              channelId: ChannelId,
                                              srRedisSimpleLockServiceV2: SrRedisSimpleLockServiceV2
                                            )(using Logger: SRLogger): Try[Option[ChannelDataForSchedulingType]] = {


    /*
     nxd notes:
        if the channel is email channel_id is email_setting

        we parse the channel_id in to email_setting_id (Long) and then fetch this from the DB

      ChannelDataForScheduling.EmailChannelDataForScheduling(
        emailSetting = emailSettingData,
        channelTeamId = emailSettingData.team_id,
        account_timezone = emailSettingData.account_timezone,
        channelOwnerAccountId = emailSettingData.account_id
      )
     */


    srRedisSimpleLockServiceV2.checkLock(
      cacheKey = cacheIdForChannelLevelLimit
    ) match {
      case Failure(e) =>
        Logger.fatal(s"checkLock failed for ${cacheIdForChannelLevelLimit.id}", e)
        Failure(e)

      case Success(true) =>
        // todo : we should fail this operation , we are mapping over this in MQEmailScheduler.
        // todo : right now we are logging the error so it's fine
        Logger.info(s"$channelData ignored because channel limit lock")
        Success(None)
      case Success(false) =>
        getChannelForScheduling(
          channelId = channelId,
          teamId = TeamId(teamId),
          logger = Logger
        ) match {
          case None =>
            Logger.error(s"scheduleTasksForChannel getChannelForScheduling NONE found")
            Success(None)

          case Some(value) =>
            Success(Some(value))

        }
    }
  }

  private def addChannelLevelMetadataAndWarnings(
                                                  campaigns: Seq[CampaignForSchedulingType],
                                                  channelDataForScheduling: ChannelDataForSchedulingType,
                                                  account: Account,
                                                  accountService: AccountService,
                                                  emailNotificationService: EmailNotificationService
                                                )(
                                                  implicit ws: WSClient,
                                                  ec: ExecutionContext,
                                                  materializer: Materializer,
                                                  Logger: SRLogger,
                                                ): Unit = {
    campaigns.filterNot(_.prospects_remaining_to_be_scheduled_exists.isDefined).foreach { camp =>
      mqCampaignSchedulingMetadataMigration.publish(msg = CampaignIdAndTeamId(
        campaign_id = camp.campaign_id,
        team_id = camp.team_id
      ))
    }
  }

  private def getAllowedTimezonesForCampaign(
                                              campaignProspectService: CampaignProspectService,
                                              campaign: CampaignForSchedulingType,
                                            )(
                                              implicit Logger: SRLogger
                                            ): Try[Set[String]] = {

    val allowedTimezones: Try[Set[String]] = {

      val timeZonesInCampaign: Try[Set[String]] = campaignProspectService
        .getAllDistinctTimezones(
          campaignId = campaign.campaign_id,
          teamId = campaign.team_id,
          campaignTimezone = campaign.timezone
        )

      val filteredByAllowedTimezone = ChannelSchedulerTrait
        .filterAllowedTimezones(

          timeZonesInCampaign = timeZonesInCampaign,

          daysPreference = campaign.days_preference,

          startOfDayInSeconds = campaign.daily_from_time,

          endOfDayInSeconds = campaign.daily_till_time
        )(
          Logger = Logger.appendLogRequestId("after filterOutCampaignsAlreadyFullyScheduled")
        )

      filteredByAllowedTimezone

    }

    allowedTimezones
  }

  final def scheduleTasksForChannel(
                                     channelData: ChannelData,
                                     teamId: Long,
                                     accountService: AccountService,
                                     //accountDAO: AccountDAO,
                                     emailNotificationService: EmailNotificationService,
                                     campaignService: CampaignService,
                                     campaignProspectDAO: CampaignProspectDAO,
                                     campaignProspectService: CampaignProspectService,
                                     campaignStepVariantDAO: CampaignStepVariantDAO,
                                     campaignStepDAO: CampaignStepDAO,
                                     srShuffleUtils: SrShuffleUtils,
                                     emailServiceCompanion: EmailServiceCompanion,
                                     templateService: TemplateService,
                                     taskDAO: TaskPgDAO,
                                     taskService: TaskService,
                                     campaignEditedPreviewEmailDAO: CampaignEditedPreviewEmailDAO,
                                     mqWebhookCompleted: MQWebhookCompleted,
                                     srRedisSimpleLockServiceV2: SrRedisSimpleLockServiceV2,
                                     campaignsMissingMergeTagService: CampaignsMissingMergeTagService,
                                     accountOrgBillingRelatedService: AccountOrgBillingRelatedService,
                                     srRollingUpdateCoreService: SrRollingUpdateCoreService,
                                     calendarAppService: CalendarAppService
                                   )(
                                     implicit ws: WSClient,
                                     ec: ExecutionContext,
                                     system: ActorSystem,
                                     materializer: Materializer,
                                     Logger: SRLogger,
                                   ): Future[ScheduleTasksData] = Future {

    val channelId = ChannelSchedulerService.getChannelIdFromChannelData(
      channelData = channelData
    )
    val cacheIdForChannelLevelLimit = ChannelSchedulerTrait.getCacheIdForChannelLimitLock(
      channelType = channelData.channelType,
      channelId = channelId,
      teamId = teamId
    )

    val noScheduleData = ScheduleTasksData(
      saved_tasks_count = 0,
      latest_task_scheduled_at = None,
      reached_scheduler_step = SchedulerSteps.Other
    )
    channelLevelCheckAndValidation(
      channelData = channelData,
      teamId = teamId,
      cacheIdForChannelLevelLimit = cacheIdForChannelLevelLimit,
      channelId = channelId,
      srRedisSimpleLockServiceV2 = srRedisSimpleLockServiceV2) match {
      case Failure(e) =>
        Future.failed(e)

      case Success(None) =>

        // todo : we should fail this operation , we are mapping over this in MQEmailScheduler.
        // todo : right now we are logging the error so it's fine
        Future.successful(noScheduleData.copy(
          reached_scheduler_step = SchedulerSteps.InitialRedisLock
        ))

      case Success(Some(channelDataForScheduling)) =>
        //Logger.info(s"\n\nchannelDataForScheduling: $channelDataForScheduling")


        //        Logger.debug("Before Find Campaigns For Scheduling Channel")

        /*
           At this point we find campaigns - for email case - using the email setting id.
         */
        findCampaignsForSchedulingChannelAfterLock(

          srRollingUpdateCoreService = srRollingUpdateCoreService,
          campaignProspectService = campaignProspectService,
          channelDataForScheduling = channelDataForScheduling,
          channelData = channelData,
          campaignService = campaignService

        ) match {

          case Failure(e) =>

            Logger.debug("Failure case : findCampaignsForSchedulingChannel")
            Future.failed(e)


          case Success(Seq()) =>

            // if no campaigns found for scheduling, no need to dig deeper
            // this helps avoid unnecessarily calling the cpu heavy getSentOrScheduledProspectsCountForEmail query


            Logger.info(s"no campaigns found for scheduling: prevented 1 heavy count query (getSentOrScheduledProspectsCountForEmail)")


            //            Logger.debug("Before updateLastScheduledForChannel")
            updateLastScheduledForChannelAndSetInQueueForSchedulingToFalse(
              channelDataForScheduling = channelDataForScheduling,
              latestScheduledAt = None
            ) match {

              case Failure(e) =>
                Logger.fatal(s"[no campaigns found] error while ${getChannelName(channelDataForScheduling.channelType)}._updateLastScheduled", err = e)

                Future.successful(noScheduleData.copy(
                  reached_scheduler_step = SchedulerSteps.NoCampaignsFoundAfterLockError,
                ))


              case Success(_) =>
                Logger.info(s"[no campaigns found] ${getChannelName(channelDataForScheduling.channelType)}._updateLastScheduled done")

                Future.successful(noScheduleData.copy(
                  reached_scheduler_step = SchedulerSteps.NoCampaignsFoundAfterLock,

                ))

            }


          case Success(campaigns) =>

            Logger.info(s"found campaigns: ${campaigns.map(_.campaign_id).mkString(",")}")
            val startTime = DateTime.now()

            // nxd notes: this is currently cached in Redis.
            val account = accountService.find(id = channelDataForScheduling.channelOwnerAccountId).get // Todo remove logger from This

            addChannelLevelMetadataAndWarnings(
              campaigns = campaigns,
              channelDataForScheduling = channelDataForScheduling,
              account = account,
              accountService = accountService,
              emailNotificationService = emailNotificationService
            )


            val scheduleFromTime: DateTime = getScheduleFromTime(
              channelDataForScheduling = channelDataForScheduling
            )

            val scheduleTillTime: DateTime = getScheduleTillTime(
              channelDataForScheduling = channelDataForScheduling
            )

            val result: Future[ScheduleTasksData] = if (scheduleTillTime.isBefore(scheduleFromTime)) {

              Logger.info(s"already scheduled for next interval")
              Future.successful(noScheduleData.copy(
                reached_scheduler_step = SchedulerSteps.ScheduleTillTimeBeforeFromTime
              ))

            } else {

              // get email account scheduled counts
              //              Logger.debug(s"Before getSentOrScheduledProspectsCountForChannel : channelDataForScheduling: $channelDataForScheduling :: campaignProspectDAO: $campaignProspectDAO")
              getSentOrScheduledProspectsCountForChannel(
                channelDataForScheduling = channelDataForScheduling,
                campaignProspectDAO = campaignProspectDAO,
                taskDAO = taskDAO
              ) match {

                case Failure(e) =>

                  Logger.debug("getSentOrScheduledProspectsCountForChannel Failed")
                  Future.failed(e)

                case Success(totalScheduledFromChannelTillNowMap) =>

                  channelData match {
                    case ChannelData.EmailChannelData(emailSettingId) =>
                      Logger.debug(s"sent or scheduled count till now -- ${totalScheduledFromChannelTillNowMap.map(a => s"${a._1} -> ${a._2.count}")}, channelDataForScheduling -- ${channelDataForScheduling.channelType} ${channelDataForScheduling.channelIdForLog}")

                    case _ => //NO LOG
                  }


                  //                  Logger.debug("getSentOrScheduledProspectsCountForChannel Success Case")

                  /*
                  campaignStepType = campaignStepType,
                  totalScheduledForStepTypeTillNow = totalScheduledForStepTypeTillNow.count.count_value,
                  stepType = totalScheduledForStepTypeTillNow.stepType,
                  channelDataForScheduling = channelDataForScheduling,
                   */
                  val channelStepTypeDataForScheduling: Map[CampaignStepType, ChannelStepTypeDataForScheduling] = computeChannelStepTypeDataForSchedulingForAllStepTypes(
                    channelDataForScheduling = channelDataForScheduling,
                    totalScheduledFromChannelTillNowMap = totalScheduledFromChannelTillNowMap,
                  )

                  /*
                    In the case of email : there are multiple campaigns for that email setting.
                    we get all the step types

                   */

                  val allStepTypesToBeScheduledByCampaign = getDistinctStepTypesToBeScheduledInCampaign(
                    // nxd notes: the scheduler is always running in the context of a channel
                    //             we can can restrict only to the channel type we are
                    //             currently running in the context of.
                    campaign_ids = campaigns.map(_.campaign_id).toList,
                    campaignStepDAO = campaignStepDAO
                  )

                  val allStepTypesToBeScheduled: Set[CampaignStepType] = flattenStepTypes(allStepTypesToBeScheduledByCampaign)

                  val stepTypesThatCanBeScheduled: Map[CampaignStepType, ChannelStepTypeDataForScheduling] = channelStepTypeDataForScheduling
                    .filter(step => step._1.channelType == channelData.channelType)
                    .filter(!_._2.campaignStepTypeLimitHasBeenReachedForToday)
                    .filter(stepsLimit => allStepTypesToBeScheduled.contains(stepsLimit._2.stepType.getCampaignStepType))


                  val stepTypes = stepTypesThatCanBeScheduled.map(_._2.stepType.getCampaignStepType).toSeq

                  if (stepTypesThatCanBeScheduled.isEmpty) {


                    // TODO: show the user this error message on the All Campaigns page
                    Logger.info(s"apply: channel quota (stepTypesThatCanBeScheduled :: $stepTypesThatCanBeScheduled) reached for the day for channel : $channelData")

                    srRedisSimpleLockServiceV2.acquireLock(
                      cacheKey = cacheIdForChannelLevelLimit,
                      expireInSeconds = AppConfig.SchedulerConfig.acquireLockIfLimitReachedForTimeInSeconds
                    ) match {
                      case Failure(e) =>
                        Logger.fatal(s"acquireLock failed for ${cacheIdForChannelLevelLimit.id}", e)

                      case Success(false) =>
                        Logger.error(s"Unable to acquire lock $cacheIdForChannelLevelLimit")

                      case Success(true) =>
                        Logger.info(s"Acquired lock $cacheIdForChannelLevelLimit")
                    }

                    Future.successful(noScheduleData.copy(
                      reached_scheduler_step = SchedulerSteps.StepTypesThatCanBeScheduledIsEmpty
                    ))


                  } else {

                    //                    Logger.debug("Before calculateMaxToBeScheduledForNextHour")

                    val maxToBeScheduledForNextHour: Int = calculateMaxToBeScheduledForNextHour(
                      channelDataForScheduling = channelDataForScheduling,
                      scheduleFromTime = scheduleFromTime,
                      scheduleTillTime = scheduleTillTime,
                    )

                    Logger.debug(s"scheduleTasksForChannel :  calculateMaxToBeScheduledForNextHour: ${maxToBeScheduledForNextHour}")


                    getScheduledProspectsCountForCampaign(

                      channelDataForScheduling = channelDataForScheduling,
                      campaigns = campaigns,
                      campaignProspectDAO = campaignProspectDAO,
                      stepTypes = stepTypes

                    ) match {
                      case Failure(e) =>

                        val err = s"(CampaignProspect.getScheduledProspectsCount failed : ${LogHelpers.getStackTraceAsString(e)}"
                        Logger.fatal(err)

                        throw e

                      case Success(foundCampaignScheduledCounts) =>

                        /*
                        val new_count = foundCampaignScheduledCounts.flatMap(_.counts)

                        if (new_count.nonEmpty) {
                          val v2 =  new_count.head
                          if (v2.newCount == 0 &&
                              v2.followupCount == 0 &&
                              v2.followupCountNotSent == 0 &&
                              v2.newCountNotSent == 0) {
                            Logger.info(
                              s"""scheduleTasksForChannel foundCampaignScheduledCounts : cid_${v2.campaignId} all counts are 0""".stripMargin)
                          }
                        }
                        */

                        //Logger.debug(s"CampaignProspect.getScheduledProspectsCount Success : ${foundCampaignScheduledCounts.map(_.counts)}")


                        // pure fn
                        val sendOnlyToProspectsWhoWereSentInCurrentCycle: Option[DateTime] = ChannelSchedulerTrait.checkSendOnlyToProspectsWhoWereSentInCurrentCycle(
                          account = account,
                          Logger = Logger
                        )

                        val campaignsToBeScheduled = filterOutCampaignsAlreadyFullyScheduled(
                          foundCampaignScheduledCounts = foundCampaignScheduledCounts,
                          stepTypes = stepTypes,
                          channelDataForScheduling = channelDataForScheduling
                        )

                        val campaignsToBeScheduledIds: Set[Long] = campaignsToBeScheduled
                          .map(_.campaign.campaign_id)
                          .toSet


                        val campaignsThatHaveHitDailyLimit: Seq[ChannelScheduledProspectsCountForCampaignType] = foundCampaignScheduledCounts
                          .filterNot(c => {

                            val cid: Long = c.campaign.campaign_id

                            campaignsToBeScheduledIds.contains(cid)

                          })

                        if (campaignsThatHaveHitDailyLimit.nonEmpty) {

                          val campaignsRejectedBecauseTheyHaveHitDailyLimit: Seq[CampaignForSchedulingType] = campaignsThatHaveHitDailyLimit
                            .map(_.campaign)

                          Logger.warn(s"campaignsRejectedBecauseTheyHaveHitDailyLimit :: ${campaignsRejectedBecauseTheyHaveHitDailyLimit.map(c => s"cid_${c.campaign_id}")}")

                          val teamId: TeamId = TeamId(channelDataForScheduling.channelTeamId)

                          val logMsg = s"campaignsRejectedBecauseTheyHaveHitDailyLimit: updateLastAndNextToBeScheduledAt reason: CampaignHasHitDailyLimit for ${campaignsRejectedBecauseTheyHaveHitDailyLimit.map(id => s"cid_${id.campaign_id}")}"

                          updateScheduleAtForCampaignsRejectedByFilters(
                            channelDataForScheduling = channelDataForScheduling,
                            campaignService = campaignService,
                            campaignProspectService = campaignProspectService,
                            rejectedCampaigns = campaignsRejectedBecauseTheyHaveHitDailyLimit,
                            teamId = teamId,
                            channelData = channelData,
                            rejectionReason = CampaignRejectedForSchedulingReason.CampaignHasHitDailyLimit
                          ) match {
                            case Success(_) =>
                              Logger.info(s"done $logMsg")

                            case Failure(exception) =>
                              Logger.fatal(s"failed to update $logMsg", err = exception)
                          }


                        }


                        val scheduledCountsByCampaign: Seq[CampaignDetailsForScheduling] = campaignsToBeScheduled.map(c => {

                          val campaign: CampaignForSchedulingType = c.campaign

                          val allowedTimezones: Try[Set[String]] = {

                            getAllowedTimezonesForCampaign(
                              campaign = campaign,
                              campaignProspectService = campaignProspectService,
                            ).map(_.toSet)

                          }

                          getCampaignDetailsForScheduling(
                            allowedTimezones = allowedTimezones,
                            c = c,
                            channelData = channelData,
                            Logger = Logger,
                            account: Account,
                            channelDataForScheduling = channelDataForScheduling,
                            stepTypesThatCanBeScheduled = stepTypesThatCanBeScheduled,
                            allStepTypesToBeScheduledByCampaign = allStepTypesToBeScheduledByCampaign,
                            maxToBeScheduledForNextHour = maxToBeScheduledForNextHour,
                            scheduleFromTime = scheduleFromTime,
                            sendOnlyToProspectsWhoWereSentInCurrentCycle = sendOnlyToProspectsWhoWereSentInCurrentCycle,
                            campaignStepVariantDAO = campaignStepVariantDAO,
                            campaignStepDAO = campaignStepDAO,
                            campaignProspectDAO = campaignProspectDAO,
                            campaignProspectService = campaignProspectService,
                            mqWebhookCompleted = mqWebhookCompleted,
                            calendarAppService = calendarAppService,
                            srRollingUpdateCoreService = srRollingUpdateCoreService
                          )

                        })


                        val scheduleCampaignsTry: Try[Seq[ScheduleCampaign]] = {

                          //                          Logger.debug("Before scheduledCountsByCampaign ")

                          val res: Seq[Try[ScheduleCampaign]] = scheduledCountsByCampaign
                            .map(c => {


                              filterProspectsIfNeededForCampaign(
                                account = account,
                                teamId = TeamId(channelDataForScheduling.channelTeamId),
                                prospectsFoundForChecking = c.prospectsFoundForCampaign,
                                campaign = c.channelScheduledProspectsCountForCampaign.campaign,
                                Logger = Logger
                              )
                                .map(prospectsForScheduling => {

                                  val flattenedProspects = flattenProspectsFoundForSchedulingByStepType(
                                    prospectsFoundByStepType = prospectsForScheduling
                                  )

                                  val distinctTimezone: Try[Set[String]] = {
                                    campaignProspectService
                                      .getAllDistinctTimezones(
                                        campaignId = c.channelScheduledProspectsCountForCampaign.campaign.campaign_id,
                                        teamId = c.channelScheduledProspectsCountForCampaign.campaign.team_id,
                                        campaignTimezone = c.channelScheduledProspectsCountForCampaign.campaign.timezone

                                      ).map(_.toSet)
                                  }
                                  ScheduleCampaign(
                                    markedCompletedIds = c.markedCompletedIds,
                                    campaign = c.channelScheduledProspectsCountForCampaign.campaign,
                                    stepsMappedById = c.fetchCampaignStepsDataResult.stepsMappedById,
                                    campaignStepsStructure = c.fetchCampaignStepsDataResult.campaignStepsStructure,
                                    prospects = flattenedProspects,
                                    distinctTimezones = distinctTimezone.getOrElse(Set())
                                  )

                                })

                            })

                          val debug_resultAre = Helpers.seqTryToTrySeq(
                            res
                          )

                          //                          Logger.debug(s"After scheduledCountsByCampaign $res")


                          debug_resultAre

                        }


                        // =================== BEGIN =====================

                        scheduleCampaignsTry match {
                          case Failure(e) =>


                            Logger.fatal(s"Error while fetching scheduleCampaigns", err = e)
                            Future.failed(e)


                          case Success(scheduleCampaigns) =>


                            // has 2 io actions inside a loop

                            val channelTasksToBeScheduled = getChannelTasksToBeScheduled(
                              channelDataForScheduling = channelDataForScheduling,
                              channelStepTypeDataForScheduling = stepTypesThatCanBeScheduled,
                              campaignListForScheduling = scheduleCampaigns,
                              scheduleFromTime = scheduleFromTime,
                              scheduleTillTime = scheduleTillTime,
                              maxToBeScheduledForNextHour = maxToBeScheduledForNextHour,
                              srShuffleUtils = srShuffleUtils,
                              emailServiceCompanion = emailServiceCompanion,
                              templateService = templateService,
                              campaignProspectDAO = campaignProspectDAO,
                              campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO,
                              campaignProspectService = campaignProspectService,
                              campaignsMissingMergeTagService = campaignsMissingMergeTagService,
                              isCalendarEnabled = account.org.org_metadata.enable_calendar.getOrElse(false),
                              calendarAccountData = account.calendar_account_data,
                              Logger = Logger

                            ).get

                            Logger.debug(s"Channel Tasks To be Scheduled size: ${channelTasksToBeScheduled.size} Before saveTasksToBeScheduledAndUpdateCampaignDataV2")


                            val savedChannelTaskIdsFut: Future[Seq[CampaignProspectUpdateScheduleStatus]] = saveTasksToBeScheduledAndUpdateCampaignDataV2(
                              channelTasks = channelTasksToBeScheduled,
                              channelDataForScheduling = channelDataForScheduling,
                              scheduleCampaigns = scheduleCampaigns,
                              orgId = account.org.id,
                              templateService = templateService,
                              taskService = taskService,
                              accountOrgBillingRelatedService = accountOrgBillingRelatedService,
                              calendarAccountData = account.calendar_account_data
                            )
                              .map(scheduledCampaignProspectData => {

                                campaignProspectDAO._updateScheduledStatus(
                                  scheduledCampaignProspectData = scheduledCampaignProspectData
                                ) match {
                                  case Failure(exception) =>
                                    Logger.error(s"updateScheduledStatus DB failure :: ${exception.getMessage} :: scheduledCampaignProspectDataLength: ${scheduledCampaignProspectData.length} ", exception)

                                  case Success(updateCPprospectIds) =>
                                    if (scheduledCampaignProspectData.length != updateCPprospectIds.length) {
                                      Logger.fatal(s"saveEmailsToBeScheduledAndUpdateCampaignDataV2 :: scheduledCampaignProspectDataLength: ${scheduledCampaignProspectData.length} :: updateCPprospectIds: ${updateCPprospectIds.length}")
                                    }
                                }

                                scheduledCampaignProspectData

                              })


                            savedChannelTaskIdsFut.map(savedChannelTaskIds => {

                              //                              Logger.debug(s"savedChannelTaskIds : $savedChannelTaskIds")

                              postSchedulingProcess(
                                channelDataForScheduling = channelDataForScheduling,
                                campaignService = campaignService,
                                scheduleCampaigns = scheduleCampaigns,
                                teamId = teamId,
                                channelData = channelData,
                                savedChannelTaskIds = savedChannelTaskIds,
                                channelTasksToBeScheduled = channelTasksToBeScheduled
                              )
                            })
                        }


                      // =================== END =====================


                    }
                  }
              }
            }

            result.map(res => {
              releaseLockForCampaignsBeingScheduledNowAndLog(campaignIds = campaigns.map(c => CampaignId(c.campaign_id)), startTime = startTime)
              res
            }).recover(e => {
              releaseLockForCampaignsBeingScheduledNowAndLog(campaignIds = campaigns.map(c => CampaignId(c.campaign_id)), startTime = startTime)
              throw e
            })
        }
    }
  }.flatMap(identity)

  private def filterOutCampaignsAlreadyFullyScheduled(
                                                       foundCampaignScheduledCounts: Seq[ChannelScheduledProspectsCountForCampaignType],
                                                       stepTypes: Seq[CampaignStepType],
                                                       channelDataForScheduling: ChannelDataForSchedulingType
                                                     )(using Logger: SRLogger): Seq[ChannelScheduledProspectsCountForCampaignType] = {
    foundCampaignScheduledCounts

      .filter(channelCountData => {

        stepTypes.exists(campaignStepType => {

          val totalTasksDoneOrScheduledFromCampaignToday = countTotalTasksDoneOrScheduledFromCampaignToday(
            channelScheduledProspectsCountForCampaign = channelCountData,
            campaignStepType = campaignStepType
          )


          val campaignDailyLimitForChannel: Int = getCampaignDailyLimitForChannel(
            channelCountData = channelCountData,
            channelDataForScheduling = channelDataForScheduling,
            campaignStepType = campaignStepType
          )

          if (campaignDailyLimitForChannel <= totalTasksDoneOrScheduledFromCampaignToday.firstSteps + totalTasksDoneOrScheduledFromCampaignToday.followUpSteps) {

            // TODO: show the user this error message on the All Campaigns page
            Logger.info(s"(campaign ${channelCountData.campaign.campaign_id}) apply: Max emails ($campaignDailyLimitForChannel :: $totalTasksDoneOrScheduledFromCampaignToday) reached for the day for campaign")
            //add lock for channel level lock for limit met

            false
          } else {

            true

          }
        })

      })
  }

  private def updateLastAndNextToBeScheduledAt(
                                                channelDataForScheduling: ChannelDataForSchedulingType,
                                                campaignService: CampaignService,
                                                campaigns: Seq[CampaignForSchedulingType],
                                                teamId: Long,
                                                channelData: ChannelData,
                                                nextToBeScheduledAtData: CampaignSetNextToBeScheduledAtData,
                                              )(using Logger: SRLogger): Try[Boolean] = {

    val campaignIds: Seq[Long] = campaigns.map(_.campaign_id)

    val result = for {

      _: Int <- campaignService._updateLastScheduled(
        campaignIds = campaignIds,
        Logger = Logger,
        team_id = TeamId(teamId)
      )
      setNextToBeScheduledAt: Int <- campaignService.setNextToBeScheduledAt(
        flowData = nextToBeScheduledAtData,
        Logger = Logger,
        team_id = TeamId(id = channelDataForScheduling.channelTeamId),
      )
    } yield {
      true
    }

    result
  }

  /**
   * 28-Mar-2024 : campaigns could get filtered out because of hitting daily sending limit, allowed-timezones check etc
   *
   * In those cases, we just set the last_schedule_done_at / next_to_be_scheduled_at on the campaign_email_settings /
   * campaign_channel_settings tables, so that those campaigns dont get picked up too frequently, and respect the min
   * intervals set via last_schedule_done_at / next_to_be_scheduled_at columns.
   */
  private def updateScheduleAtForCampaignsRejectedByFilters(

                                                             channelDataForScheduling: ChannelDataForSchedulingType,
                                                             campaignService: CampaignService,
                                                             campaignProspectService: CampaignProspectService,
                                                             rejectedCampaigns: Seq[CampaignForSchedulingType],
                                                             teamId: TeamId,
                                                             channelData: ChannelData,
                                                             rejectionReason: CampaignRejectedForSchedulingReason

                                                           )(
                                                             implicit Logger: SRLogger
                                                           ): Try[Boolean] = {

    val campaignDataWithAllTheTimezone: Set[(CampaignForScheduling, Set[String])] = rejectedCampaigns
      .toSet
      .map(campaign => {
        val timeZonesInCampaign: Set[String] = campaignProspectService
          .getAllDistinctTimezones(
            campaignId = campaign.campaign_id,
            teamId = campaign.team_id,
            campaignTimezone = campaign.timezone

          ).map(_.toSet) match {
          case Success(value) => value
          case Failure(exception) =>
            Logger.shouldNeverHappen(s"Failed to getAllDistinctTimezones", Some(exception))
            Set()
        }
        (campaign, timeZonesInCampaign)
      })

    val nextToBeScheduledAtData = CampaignSetNextToBeScheduledAtData
      .SchedulerRejectedFlow(

        campaignDataWithAllTheTimezone = campaignDataWithAllTheTimezone,

        rejectedForSchedulingReason = rejectionReason,
        channelType = channelData.channelType

      )

    updateLastAndNextToBeScheduledAt(

      channelDataForScheduling = channelDataForScheduling,
      campaignService = campaignService,
      campaigns = rejectedCampaigns,
      teamId = teamId.id,
      channelData = channelData,
      nextToBeScheduledAtData = nextToBeScheduledAtData,

    )
  }

  private def postSchedulingProcess(
                                     channelDataForScheduling: ChannelDataForSchedulingType,
                                     campaignService: CampaignService,
                                     scheduleCampaigns: Seq[ScheduleCampaign],
                                     teamId: Long,
                                     channelData: ChannelData,
                                     savedChannelTaskIds: Seq[CampaignProspectUpdateScheduleStatus],
                                     channelTasksToBeScheduled: Vector[GenerateScheduleTaskData]
                                   )(using Logger: SRLogger): ScheduleTasksData = {
    val latestTaskScheduledAt: Option[DateTime] = getLatestTaskScheduledAt(
      channelTasks = channelTasksToBeScheduled
    )

    val nextToBeScheduledAtData = CampaignSetNextToBeScheduledAtData
      .SchedulerSuccessFlow(

        campaignDataWithAllTheTimezone = scheduleCampaigns.map(a => (a.campaign, a.distinctTimezones)).toSet,

        channelType = channelData.channelType,
        total_scheduled = savedChannelTaskIds.length

      )

    val result = for {
      _: Int <- updateLastScheduledForChannelAndSetInQueueForSchedulingToFalse(
        channelDataForScheduling = channelDataForScheduling,
        latestScheduledAt = latestTaskScheduledAt
      )

      setNextToBeScheduledAt: Boolean <- updateLastAndNextToBeScheduledAt(
        channelDataForScheduling = channelDataForScheduling,
        campaignService = campaignService,
        campaigns = scheduleCampaigns.map(_.campaign),
        teamId = teamId,
        channelData = channelData,
        nextToBeScheduledAtData = nextToBeScheduledAtData,
      )
    } yield {
      setNextToBeScheduledAt
    }
    result match {

      case Failure(e) =>
        Logger.fatal(s"${LogHelpers.getStackTraceAsString(e)}")
        ScheduleTasksData(
          saved_tasks_count = savedChannelTaskIds.length,
          latest_task_scheduled_at = latestTaskScheduledAt,
          reached_scheduler_step = SchedulerSteps.PostSchedulingError
        )


      case Success(_) =>

        Logger.info(s"DONE :: savedChannelTaskIds: ${savedChannelTaskIds.length}")
        ScheduleTasksData(
          saved_tasks_count = savedChannelTaskIds.length,
          latest_task_scheduled_at = latestTaskScheduledAt,
          reached_scheduler_step = SchedulerSteps.PostSchedulingSuccess
        )

    }
  }

  private def getCampaignDetailsForScheduling(
                                               allowedTimezones: Try[Set[String]],
                                               c: ChannelScheduledProspectsCountForCampaignType,
                                               channelData: ChannelData,
                                               Logger: SRLogger,
                                               account: Account,
                                               channelDataForScheduling: ChannelDataForSchedulingType,
                                               stepTypesThatCanBeScheduled: Map[CampaignStepType, ChannelStepTypeDataForScheduling],
                                               allStepTypesToBeScheduledByCampaign: Map[Long, List[CampaignStepType]],
                                               maxToBeScheduledForNextHour: Int,
                                               scheduleFromTime: DateTime,
                                               sendOnlyToProspectsWhoWereSentInCurrentCycle: Option[DateTime],
                                               campaignStepVariantDAO: CampaignStepVariantDAO,
                                               campaignStepDAO: CampaignStepDAO,
                                               campaignProspectDAO: CampaignProspectDAO,
                                               campaignProspectService: CampaignProspectService,
                                               mqWebhookCompleted: MQWebhookCompleted,
                                               calendarAppService: CalendarAppService,
                                               srRollingUpdateCoreService: SrRollingUpdateCoreService
                                             )(implicit ec: ExecutionContext): CampaignDetailsForScheduling = {

    given logger: SRLogger = Logger.appendLogRequestId(s"cid_${c.campaign.campaign_id}, tid_${c.campaign.team_id}, tz_${c.campaign.timezone}")

    // pass channel data here
    /*
        FetchCampaignStepsData(
          stepsMappedById = stepsMappedById,
          orderedStepIds = orderedStepIds,
          allCampaignSteps = allCampaignSteps,
          relevantCampaignStepsForChannel = campaignsStepsRelevantToChannel
        )
     */
    val fetchCampaignStepsDataResult: FetchCampaignStepsData = fetchCampaignStepsData(
      c = c.campaign,
      account = account,
      channelType = channelData.channelType,
      campaignStepVariantDAO = campaignStepVariantDAO,
      campaignStepDAO = campaignStepDAO
    )
      .get // FIXME: handle this correctly

    //                            logger.debug(s"Before markedCompletedIds")

    val lastStepIds: List[Long] = fetchCampaignStepsDataResult.campaignStepsStructure match {

      case data: CampaignStepsStructure.EmailCampaignStepsStructure =>
        List(data.orderedStepIds.last)

      case data: CampaignStepsStructure.MultichannelCampaignStepsStructure =>
        List(data.orderedStepIds.last)

      case data: CampaignStepsStructure.DripCampaignStepsStructure =>

        val startTime = DateTime.now()
        val res = NextStepFinderForDrip.getLastSteps(
          head_node_id = data.head_node_id,
          edges = data.edges
        )


        val finalRes = Await.result(res, 60000.millis)

        val endTime = DateTime.now()
        val totalTimeTakenInMillis = new Duration(startTime, endTime).getMillis

//        logger.debug(s"[NextStepFinderForDrip.getLastSteps] totalTimeTakenInMillis : $totalTimeTakenInMillis")
//        logger.debug(s"[NextStepFinderForDrip.getLastSteps] edges : ${data.edges}, head_node_id : ${data.head_node_id}")
//        logger.debug(s"[NextStepFinderForDrip.getLastSteps] finalRes : $finalRes")

        finalRes

    }

    val markedCompletedIds = campaignProspectDAO.findAndMarkCompletedProspects(
      lastStepIds = lastStepIds,
      teamId = TeamId(c.campaign.team_id),
      markCompletedAfterDays = c.campaign.mark_completed_after_days
    ).get

    //                            Logger.debug("Before mqWebhookCompleted.publishCompletedProspects")

    mqWebhookCompleted.publishCompletedProspects(
      accountId = channelDataForScheduling.channelOwnerAccountId,
      teamId = channelDataForScheduling.channelTeamId,
      cpCompleteds = markedCompletedIds
    ) match {
      case Failure(exception) =>
        logger.fatal(s"publishCompletedProspects failed cid_${c.campaign.campaign_id} :: markedCompletedIds: $markedCompletedIds")

      case Success(value) =>
      // do nothing
    }

    //                            Logger.debug("After mqWebhookCompleted.publishCompletedProspects")


    //                            Logger.debug(s"After markedCompletedIds: $markedCompletedIds Before campaignDailyQuota")

    val prospectsFoundForCampaign: ProspectsFoundByStepType = getProspectsByStepTypeForCampaign(
      allowedTimezones = allowedTimezones,
      c = c,
      channelDataForScheduling = channelDataForScheduling,
      stepTypesThatCanBeScheduled = stepTypesThatCanBeScheduled,
      allStepTypesToBeScheduledByCampaign = allStepTypesToBeScheduledByCampaign,
      maxToBeScheduledForNextHour = maxToBeScheduledForNextHour,
      scheduleFromTime = scheduleFromTime,
      fetchCampaignStepsDataResult = fetchCampaignStepsDataResult,
      sendOnlyToProspectsWhoWereSentInCurrentCycle = sendOnlyToProspectsWhoWereSentInCurrentCycle,
      channelData = channelData,
      campaignProspectDAO = campaignProspectDAO,
      campaignProspectService = campaignProspectService,
      calendarAppService = calendarAppService,
      srRollingUpdateCoreService = srRollingUpdateCoreService
    )

    // what we want - Seq[List[ProspectForScheduling]]
    // where each list is for one touch type

    val res = CampaignDetailsForScheduling(
      markedCompletedIds = markedCompletedIds,
      fetchCampaignStepsDataResult = fetchCampaignStepsDataResult,
      channelScheduledProspectsCountForCampaign = c,

      prospectsFoundForCampaign = prospectsFoundForCampaign,
      allowedTimezones = allowedTimezones.getOrElse(Set())

    )

    //                            Logger.debug(s"scheduledCountsByCampaign res : $res")

    res


  }

}

object ChannelSchedulerTrait {

  def filterAllowedTimezones(

                              timeZonesInCampaign: Try[Set[String]],
                              daysPreference: List[Boolean],
                              startOfDayInSeconds: Int,
                              endOfDayInSeconds: Int,

                            )(
                              implicit Logger: SRLogger,
                            ): Try[Set[String]] = {

    for {

      timezonesInCampaign: Set[String] <- timeZonesInCampaign
        .map(initialTzs => {

          initialTzs
            .map(tz => {

              val tzCheckRes = TimezoneUtils.checkAndReturnValidTimezone(tz = tz)

              if (tzCheckRes.isEmpty) {

                Logger.fatal(s"allowedTzsFilteredByDaysPreference checkAndReturnValidTimezone: invalid prospect tz: $tz :: These prospects will be ignored [ALERT]")

              } else {

                if (tzCheckRes.get != tz) {

                  Logger.debug(s"allowedTzsFilteredByDaysPreference checkAndReturnValidTimezone: invalid prospect tz: got fixed: tz: $tz :: tzCheckRes: ${tzCheckRes.get}")

                }
              }

              tzCheckRes

            })
            .filter(_.isDefined)
            .map(_.get)
            .toSet


        })

      allowedTzsFilteredByDaysPreference: Set[String] <- Try {

        timezonesInCampaign

          .map(tz => {

            val dayOfWeek: Int = TimezoneUtils.getDayOfWeekInTimezone(timezone = tz)

            // Logger.debug(s"[fetchProspects] dayOfWeek as per TimeZone and current Time: $dayOfWeek")


            (tz, dayOfWeek)

          })
          .filter(tzWithDayOfWeek => {

            val dayOfWeek: Int = tzWithDayOfWeek._2

            val allowed: Boolean = daysPreference(dayOfWeek)

            // Logger.debug(s"[fetchProspects] timezone allowed with Day Preference: $allowed")

            allowed

          })
          .map(_._1)

      }

      allowedTzsFilteredByScheduleWindow: Set[String] <- Try {


        val result_tzs = timezonesInCampaign
          .map(tz => {

            val currentSecondOfDay = getCurrentSecondOfDay(
              timezone = tz
            )

            (tz, currentSecondOfDay)
          })
          .filter { case (tz, currentSecondOfDayInTz) =>


            startOfDayInSeconds <= currentSecondOfDayInTz &&
              currentSecondOfDayInTz < endOfDayInSeconds


          }
          .map(_._1)

        Logger.debug(s"[fetchProspects] allowed timezones Filtered by Scheduled Window: $result_tzs")

        result_tzs


      }


      allowedTimezones: Set[String] <- Try {

        allowedTzsFilteredByDaysPreference
          .intersect(
            allowedTzsFilteredByScheduleWindow
          )

      }


    } yield {

      allowedTimezones
    }
  }

  def getWarmupQuota(
                      warmupSetting: CampaignWarmupSetting,
                      channelOrCampaignMinDailyQuota: Int,
                    ): Int = {

    val warmupStartDate: DateTime = warmupSetting.warmup_started_at

    val warmupLengthInDays = warmupSetting.warmup_length_in_days

    val startingNumberOfEmails = warmupSetting.warmup_starting_email_count
    // val warmupEndDate = warmupStartDate.get.plusDays(warmupLengthInDays)
    val today = DateTime.now()
    val daysSinceWarmupStart = Days.daysBetween(warmupStartDate, today).getDays

    if (
      daysSinceWarmupStart >= 0 &&
        (daysSinceWarmupStart < warmupLengthInDays) &&
        (startingNumberOfEmails < channelOrCampaignMinDailyQuota)
    ) {

      //        (startingNumberOfEmails + Math.pow(daysSinceWarmupStart.toFloat / warmupLengthInDays, 2.5) * (emailAccountOrCampaignDailyQuota - startingNumberOfEmails)).toInt

      (
        startingNumberOfEmails +
          (daysSinceWarmupStart.toFloat / warmupLengthInDays) * (channelOrCampaignMinDailyQuota - startingNumberOfEmails)

        ).toInt

    } else channelOrCampaignMinDailyQuota

  }

  def getCacheIdForChannelLimitLock(channelType: ChannelType, channelId: ChannelId, teamId: Long): CacheIdKeyForLock = {
    // NOTE: channelId is long for email_channel and string (uuid) for other channels.
    CacheIdKeyForLock(s"${SrRedisKeyRegister.srChannelLimitLockNamespace.namespace}_teamId_${teamId}_${channelType.toString}_${channelId.id}")
  }

  def findScheduledProspectsCountForStepType(
                                              campaignId: Long,
                                              campaignStepType: CampaignStepType,
                                              campaignCounts: Seq[ScheduledProspectsCountForCampaign]
                                            ): ScheduledProspectsCountForCampaign = {

    campaignCounts.find(cc => {
        cc.campaignId == campaignId &&
          campaignStepType == cc.campaignStepType
      })
      .getOrElse(
        ScheduledProspectsCountForCampaign(
          campaignId = campaignId,
          campaignStepType = campaignStepType,
          newCount = 0,
          followupCount = 0,
          newCountNotSent = 0,
          followupCountNotSent = 0
        )
      )

  }


  def checkSendOnlyToProspectsWhoWereSentInCurrentCycle(
                                                         account: Account,
                                                         Logger: SRLogger
                                                       ): Option[DateTime] = {


    val sendOnlyToProspectsWhoWereSentInCurrentCycle: Option[DateTime] = if (account.org.plan.new_prospects_paused_till.isDefined) {
      if (account.org.plan.current_cycle_started_at.isAfter(account.org.plan.new_prospects_paused_till.get)) {

        // not possible
        Logger.error(s"emailSetting.current_cycle_started_at.isAfter(account.org.plan.new_prospects_paused_till.get) :: should not be possible FATAL :: ${account.org.id}")

        None
      } else {

        Logger.info(s"sendOnlyToProspectsWhoWereSentInCurrentCycle: true :: ${account.org.plan.current_cycle_started_at} :: ${account.org.plan.new_prospects_paused_till}")

        Some(account.org.plan.current_cycle_started_at)
      }
    } else {

      Logger.debug("sendOnlyToProspectsWhoWereSentInCurrentCycle : None")
      None
    }


    sendOnlyToProspectsWhoWereSentInCurrentCycle
  }

  def nextCheckForSchedulingIntervalForCampaignProspect(
                                                         nextCheckForSchedulingIntervalType: NextCheckForSchedulingIntervalType
                                                       ): DateTime = {

    nextCheckForSchedulingIntervalType match {

      case NextCheckForSchedulingIntervalType.DripCampaign |
           NextCheckForSchedulingIntervalType.PendingMagicColumns =>

        DateTime.now().plusMinutes(15)

      case NextCheckForSchedulingIntervalType.NonDripCampaign =>

        DateTime.now().plusHours(23) //FIXME: this will have a logic later

    }

  }

}
