package utils.mq.channel_scheduler.channels

import api.accounts.models.OrgId
import api.accounts.{Account, TeamId}
import api.campaigns.models.{CampaignStepData, CampaignStepType, ChannelStepType, CurrentStepStatusForScheduler}
import api.campaigns.{CampaignEditedPreviewEmailDAO, CampaignProspectDAO, CampaignProspectUpdateScheduleStatus, CampaignStepVariantForScheduling, CampaignStepWithChildren}
import api.campaigns.services.{CampaignId, CampaignProspectService, CampaignService}
import api.prospects.models.{ProspectDataForChannelScheduling, ProspectId}
import api.tasks.models.{NewTask, TaskCreatedVia, TaskData, TaskPriority, TaskStatus, TaskType}
import api.tasks.services.{CreateTaskError, TaskService}
import api.whatsapp.WhatsappSettingDAO
import org.joda.time.{DateTime, DateTimeZone, Seconds}
import sr_scheduler.models.{CampaignForScheduling, ChannelDataForScheduling, ChannelType, ScheduledProspectsCountForCampaign}
import utils.email.EmailServiceCompanion
import utils.mq.channel_scheduler.channels.{ChannelId, ChannelSchedulerTrait, Count, StepType, StepTypeAndCount}
import utils.templating.TemplateService
import utils.{SRLogger, SrLoggerTrait}
import api.tasks.pgDao.TaskPgDAO
import sr_scheduler.models.CampaignForScheduling.CampaignEmailSettingForScheduler
import utils.mq.channel_scheduler.{MqCampaignSchedulingMetadataMigration, SchedulerMapStepIdAndDelay}
import utils.random.SrRandomUtils
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Success, Try}

class WhatsAppChannelScheduler (
                                 whatsAppSettingDAO: WhatsappSettingDAO,
//                                 taskService: TaskService,
                                 srRandomUtils: SrRandomUtils,
                                 taskPgDAO: TaskPgDAO,
                                 val mqCampaignSchedulingMetadataMigration: MqCampaignSchedulingMetadataMigration
                               ) extends ChannelSchedulerTrait {

  override type ChannelDataForSchedulingType = ChannelDataForScheduling.WhatsAppChannelDataForScheduling

  override type ProspectDataForChannelSchedulingType = ProspectDataForChannelScheduling.WhatsappChannelProspectForScheduling

  case class WhatsAppChannelScheduledProspectsCountForCampaign(

                                                             counts: Seq[ScheduledProspectsCountForCampaign],
                                                             campaign: CampaignForSchedulingType

                                                           ) extends ChannelScheduledProspectsCountForCampaign


  override type ChannelScheduledProspectsCountForCampaignType = WhatsAppChannelScheduledProspectsCountForCampaign

  override type CampaignForSchedulingType = CampaignForScheduling.CampaignForSchedulingWhatsApp

  override def getChannelForScheduling(
                                        channelId: ChannelId,
                                        teamId: TeamId,
                                        logger: SrLoggerTrait
                                      ): Option[ChannelDataForSchedulingType] = {

        whatsAppSettingDAO.getChannelForSchedulingWhatsApp(
          channelId = channelId,
          teamId = teamId,
          srLogger = logger
        ) // WhatsApp Setting Data will be needed here

  }

  override def findCampaignsForSchedulingChannel(
                                                  channelDataForScheduling: ChannelDataForSchedulingType,
                                                  campaignService: CampaignService,
                                                )(
                                                  implicit Logger: SRLogger
                                                ): Try[Seq[CampaignForSchedulingType]] = {

    campaignService.findCampaignForSchedulingTasks(
      channelStepType = CampaignStepType.WhatsappMessage.channelStepType,
      channelSettingUuid = channelDataForScheduling.whatsappSetting.uuid,
      Logger = Logger
    ).map {
      _.asInstanceOf[Seq[CampaignForSchedulingType]]
    }

  }


  override def updateLastScheduledForChannelAndSetInQueueForSchedulingToFalse(
                                              channelDataForScheduling: ChannelDataForSchedulingType,
                                              latestScheduledAt: Option[DateTime]
                                            )(
                                              implicit Logger: SRLogger
                                            ): Try[Int] = {
      whatsAppSettingDAO._updateLastScheduled(
        whatsAppSettingId = channelDataForScheduling.whatsappSetting.uuid,
        latestTaskScheduledAt = latestScheduledAt,
        Logger = Logger,
        team_id = channelDataForScheduling.whatsappSetting.team_id
      )
  }

    override def getConsecutiveTaskScheduleDelay(
                                                channelDataForScheduling: ChannelDataForSchedulingType
                                              ): Int = {

    val whatsAppSetting = channelDataForScheduling.whatsappSetting

    val minConsecutiveWhatsAppDelay: Int = whatsAppSetting.min_delay_seconds
    val maxConsecutiveWhatsAppDelay: Int = whatsAppSetting.max_delay_seconds

    val consecutiveWhatsAppDelay = srRandomUtils.getRandomDelay(
      minDelay = minConsecutiveWhatsAppDelay,
      maxDelay = maxConsecutiveWhatsAppDelay
    )

    consecutiveWhatsAppDelay

  }

  override def getLatestTaskScheduledAt(
                                          channelTasks: Vector[GenerateScheduleTaskData]
                                        ): Option[DateTime] = {

    /*channelTasks match {
      case emailTasks: ChannelScheduledTaskNew.NewBatchEmailScheduled =>
  */
    val latestWhatsAppScheduledAt = if (channelTasks.nonEmpty) Some(channelTasks.last.schedulerDateTime) else None

    latestWhatsAppScheduledAt

    /*}*/

  }

  override def calculateMaxToBeScheduledForNextHour(
                                                     channelDataForScheduling: ChannelDataForSchedulingType,
                                                     scheduleFromTime: DateTime,
                                                     scheduleTillTime: DateTime,
                                                   ): Int = {

    val minConsecutiveWhatsAppDelay = channelDataForScheduling.whatsappSetting.min_delay_seconds

    val maxToBeScheduledForNextHour = Seconds.secondsBetween(scheduleFromTime, scheduleTillTime).getSeconds / minConsecutiveWhatsAppDelay

    maxToBeScheduledForNextHour

  }


  override def getScheduledProspectsCountForCampaign(
                                                      channelDataForScheduling: ChannelDataForSchedulingType,
                                                      campaigns: Seq[CampaignForSchedulingType],
                                                      campaignProspectDAO: CampaignProspectDAO,
                                                      stepTypes: Seq[CampaignStepType]
                                                    )(
                                                      implicit logger: SRLogger
                                                    ): Try[Seq[ChannelScheduledProspectsCountForCampaignType]] = {
    // TODO Multichannel: extract TeamId from ChannelDataForScheduling and pass it to below function


    taskPgDAO.getScheduledProspectsCountForChannelType(
      channelType = channelDataForScheduling.channelType,
      ownerAccountId = channelDataForScheduling.channelOwnerAccountId,
      campaignIdAndTimezone = campaigns.map(c => (c.campaign_id, c.timezone)),
      teamId = channelDataForScheduling.whatsappSetting.team_id,
      stepTypes = stepTypes,
      Logger = logger,
    )
      .map(campaignCountResults => {

        val countResults = campaigns
          .map(c => {

            val campaignCountsAllStepTypes = stepTypes
              .map(campaignStepType => {

                ChannelSchedulerTrait.findScheduledProspectsCountForStepType(
                  campaignId = c.campaign_id,
                  campaignStepType = campaignStepType,
                  campaignCounts = campaignCountResults
                )
              })

            WhatsAppChannelScheduledProspectsCountForCampaign(
              campaign = c,
              counts = campaignCountsAllStepTypes
            )
          })


        countResults

      })

  }
  

  override def getScheduleFromTime(
                                    channelDataForScheduling: ChannelDataForSchedulingType
                                  ): DateTime = {

    val whatsAppSetting_latest_scheduled_at = channelDataForScheduling.whatsappSetting.latest_task_scheduled_at

    val scheduleFromTime = if (whatsAppSetting_latest_scheduled_at.isDefined && whatsAppSetting_latest_scheduled_at.get.isAfterNow) {
      whatsAppSetting_latest_scheduled_at.get
    } else DateTime.now(DateTimeZone.UTC)

    scheduleFromTime

  }


  override def getChannelTaskLimitPerDay(
                                          channelDataForScheduling: ChannelDataForSchedulingType,
                                          campaignStepType: CampaignStepType,
                                        ): Int = {

    // there is only one possible step type for whatsapp channel
    channelDataForScheduling.whatsappSetting.whatsapp_message_limit_per_day

  }


  override def getCampaignDailyLimitForChannel(
                                                channelCountData: ChannelScheduledProspectsCountForCampaignType,
                                                channelDataForScheduling: ChannelDataForSchedulingType,
                                                campaignStepType: CampaignStepType
                                              ): Int = {

    getChannelTaskLimitPerDay(
      channelDataForScheduling = channelDataForScheduling,
      campaignStepType = campaignStepType
    )

  }
  
  override def filterProspectsByChannelSpecificChecks(
                                                       prospectIds: Set[Long],
                                                       team_id: TeamId,
                                                       campaignForScheduling: CampaignForSchedulingType,
                                                       campaignProspectDAO: CampaignProspectDAO,
                                                       previousSelectedProspectIds: Set[ProspectId]
                                                     )(
                                                       using Logger: SRLogger

                                                     ): Try[Set[Long]] = {
    Success(prospectIds)  // Todo Fixme MultiChannel LATER: discuss filters (maxTasksPerDayFromChannel setting might be needed)
  }

  override def filterProspectsIfNeededForCampaign(
                                                   account: Account,
                                                   prospectsFoundForChecking: ProspectsFoundByStepType,
                                                   campaign: CampaignForSchedulingType,
                                                   teamId: TeamId,
                                                   Logger: SRLogger,
                                                 ): Try[ProspectsFoundByStepType] = {

    // Todo Fixme Multichannel: discuss filters
    Success(prospectsFoundForChecking)
  }

  override def fetchProspectsV3Multichannel(
                                             channelType: ChannelType,
                                             allowedProspectTimezones: List[String],
                                             prospectIdGreaterThan: Option[Long] = None, // for first page of results
                                             campaignId: Long,
                                             team_id: Long,
                                             limit: Int,
                                             channelRelevantStepIdAndDelay: Vector[SchedulerMapStepIdAndDelay],
                                             newProspectsInCampaign: Boolean,
                                             firstStepIsMagicContent: Boolean,
                                             sendOnlyToProspectsWhoWereSentInCurrentCycle: Option[DateTime],
                                             campaignProspectDAO: CampaignProspectDAO,
                                             useModifiedQueryForDripCampaign: Boolean,
                                             campaignProspectService: CampaignProspectService,
                                             campaign: CampaignForSchedulingType,
                                             campaign_email_setting: Option[CampaignEmailSettingForScheduler],
                                             org_id: OrgId,
//                                             emailNotCompulsoryEnabled : Boolean
                                           )(using Logger: SRLogger): Try[List[ProspectDataForChannelSchedulingType]] = {
    campaignProspectService
      .fetchProspectsV3MultichannelWithEmailOptionalCheck(
        channelType = channelType,
        prospectIdGreaterThan = prospectIdGreaterThan,
        allowedProspectTimezones = allowedProspectTimezones,
        campaignId = campaignId,
        teamId = TeamId(team_id),
        channelRelevantStepIdAndDelay = channelRelevantStepIdAndDelay,

        limit = limit,
        newProspectsInCampaign = newProspectsInCampaign,
        firstStepIsMagicContent = firstStepIsMagicContent,
        useModifiedQueryForDripCampaign = useModifiedQueryForDripCampaign,

        sendOnlyToProspectsWhoWereSentInCurrentCycle = sendOnlyToProspectsWhoWereSentInCurrentCycle,
        campaign_email_setting = None, //campaign don't have campaign_email_settings_id for whatsapp
        org_id = org_id,
//        emailNotCompulsoryEnabled = emailNotCompulsoryEnabled,
        head_step_id = campaign.campaign_type_data.head_id
      ).map(_.asInstanceOf[List[ProspectDataForChannelScheduling.WhatsappChannelProspectForScheduling]])
  }
}
