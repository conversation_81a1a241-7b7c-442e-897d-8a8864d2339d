package utils.mq.channel_scheduler.channels

import api.AppConfig
import api.accounts.{Account, AccountService, TeamId}
import api.campaigns.models.CampaignStepData.getSubjectAndBodyFromStepData
import api.campaigns.models.{CampaignEmailSettingsId, CampaignProspectInternalNote, CampaignStepData, CampaignStepType, CampaignStepsStructure, CampaignTypeData, ChannelStepType, CurrentStepStatusForScheduler, CurrentStepStatusForSchedulerData, SubjectAndBody}
import api.campaigns.services.{CampaignDAOService, CampaignId, CampaignProspectService, CampaignService, CampaignTemplateService}
import api.campaigns.{CampaignEditedPreviewEmailDAO, CampaignProspectDAO, CampaignProspectUpdateScheduleStatus, CampaignStepVariantForScheduling, CampaignStepWithChildren, EmailsScheduledCount, PreviousFollowUp}
import api.emails.{EmailMessageDataDAO, EmailScheduledDAO, EmailScheduledNewAfterSaving, EmailScheduledNewStep2, EmailSettingDAO, EmailsScheduledUuid}
import api.prospects.models.{EmailForValidation, ProspectAccountsId, ProspectDataForChannelScheduling, ProspectId, ProspectTouchedType, StepId}
import api.prospects.ProspectForValidation
import api.tasks.models.{NewTask, TaskCreatedVia, TaskData, TaskPriority, TaskStatus, TaskType}
import api.tasks.pgDao.TaskPgDAO
import api.tasks.services.{CreateTaskError, TaskService}
import api.accounts.models.OrgId
import api.accounts.service.AccountOrgBillingRelatedService
import api.calendar_app.models.CalendarAccountData
import api.campaigns.models.CampaignStepType.{AutoEmailMagicContent, AutoEmailStep, ManualEmailMagicContent}
import api.columns.InternalMergeTagValuesForProspect
import api.emails.dao_service.EmailScheduledDAOService
import api.gpt.CreateStepsRequest
import api.gpt.ai_hyperpersonalized.AIHyperPersonalizedGenerator
import api.rep_tracking_hosts.service.RepTrackingHostService
import api.sr_ai.models.OpenAiModel
import eventframework.{ProspectFieldsResult, ProspectObject}
import io.smartreach.esp.api.emails.EmailSettingId
import org.apache.pekko.actor.ActorSystem
import org.apache.pekko.stream.Materializer
import org.joda.time.{DateTime, DateTimeZone, Days, Seconds}
import play.api.libs.json.JsObject
import play.api.libs.ws.WSClient
import sr_scheduler.models.CampaignForScheduling.CampaignEmailSettingForScheduler
import sr_scheduler.models.{CampaignAIGenerationContext, CampaignEmailPriority, CampaignForScheduling, CampaignWarmupSetting, ChannelDataForScheduling, ChannelType, EmailScheduledNew3, EmailSettingCreateEmailSchedule, GeneratedContent, ScheduledProspectsCountForCampaign, ScheduledProspectsCountForCampaignEmail, SelectedCalendarData, SendingMode}
import utils.dateTime.SrDateTimeUtils
import utils.email.{EmailHelper, EmailOptionsForGetBodies, EmailServiceCompanion, UnSubscribeLinkHelper}
import utils.templating.TemplateService
import utils.emailvalidation.EmailValidationService
import utils.emailvalidation.models.{EmailValidationPriority, EmailsForValidationWithInitiator, IdsOrEmailsForValidation}
import utils.featureflags.services.OrgMetadataService
import utils.{Helpers, ParseUtils, SRLogger, SrLoggerTrait}
import utils.mq.channel_scheduler.{MqCampaignSchedulingMetadataMigration, SchedulerMapStepIdAndDelay}
import utils.random.SrRandomUtils
import utils.helpers.LogHelpers
import utils.mq.channel_scheduler.channels.EmailChannelScheduler.{getEmailsCountThatCanBeScheduled, isMagicContentTask}
import utils.mq.channel_scheduler.channels.service.EmailSchedulerJedisService
import utils.timezones.TimezoneUtils
import utils_deploy.rolling_updates.models.{SrRollingUpdateCohort, SrRollingUpdateFeature}
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService
import utils.mq.ai_content_generation.{MqAiContentGenerationPublisher, MqAiContentGenerationRequest}
import utils.uuid.SrUuidUtils

// Added imports for LlmAuditLogDAO and related models
import api.llm.dao.{LlmAuditLogDAO, LlmAuditLogInsertData}
import api.columns.ProspectColGenStatus
import api.llm.models.LlmFlow
import api.sr_ai.models.AiModel
import play.api.libs.json.Json
// End added imports

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}


case class StepInfo(
                     stepNumber: Int,
                     totalSteps: Int,
                     stepType: CampaignStepType,
                     context: String
                   )

sealed trait ContentGenerationError extends Exception

case class ContentValidationError(message: String) extends ContentGenerationError

case class ContentGenerationFailure(message: String) extends ContentGenerationError

class EmailChannelScheduler(
                             emailScheduledDAOService: EmailScheduledDAOService,
                             emailValidationService: EmailValidationService,
                             emailServiceCompanion: EmailServiceCompanion,
                             //                             accountService: AccountService,
                             srRandomUtils: SrRandomUtils,
                             orgMetadataService: OrgMetadataService,
                             emailSettingDAO: EmailSettingDAO,
                             emailSchedulerJedisService: EmailSchedulerJedisService,
                             repTrackingHostService: RepTrackingHostService,
                             srDateTimeUtils: SrDateTimeUtils,
                             campaignProspectService: CampaignProspectService,
                             aiHyperPersonalizedService: AIHyperPersonalizedGenerator,
                             campaignDAOService: CampaignDAOService,
                             //                             dbUtils: DBUtils,
                             val mqCampaignSchedulingMetadataMigration: MqCampaignSchedulingMetadataMigration,
                             srRollingUpdateCoreService: SrRollingUpdateCoreService,
                             llmAuditLogDAO: LlmAuditLogDAO, // Added LlmAuditLogDAO
                             srUuidUtils: SrUuidUtils
                           )
  extends ChannelSchedulerTrait {

  override type ProspectDataForChannelSchedulingType = ProspectDataForChannelScheduling.EmailChannelProspectForScheduling

  override type CampaignForSchedulingType = CampaignForScheduling.CampaignForSchedulingEmail

  case class EmailChannelScheduledProspectsCountForCampaign(

                                                             counts: Seq[ScheduledProspectsCountForCampaign],
                                                             campaign: CampaignForSchedulingType

                                                           ) extends ChannelScheduledProspectsCountForCampaign


  override type ChannelDataForSchedulingType = ChannelDataForScheduling.EmailChannelDataForScheduling

  override type ChannelScheduledProspectsCountForCampaignType = EmailChannelScheduledProspectsCountForCampaign


  override def getChannelForScheduling(
                                        channelId: ChannelId,
                                        teamId: TeamId,
                                        logger: SrLoggerTrait
                                      ): Option[ChannelDataForSchedulingType] = {


    ParseUtils.parseLong(s = channelId.id) match {

      case None =>
        logger.fatal(s"email getChannelForScheduling invalid channelId: $channelId")
        None


      case Some(emailSettingId) =>

        //        logger.debug(s"getChannelForScheduling Some(emailSettingId): $emailSettingId")


        val debug_findForScheduling = emailSettingDAO
          .findForScheduling(
            channelId = emailSettingId,
            srLogger = logger
          )
          .map(emailSettingData => {
            ChannelDataForScheduling.EmailChannelDataForScheduling(
              emailSetting = emailSettingData,
              channelTeamId = emailSettingData.team_id,
              account_timezone = emailSettingData.account_timezone,
              channelOwnerAccountId = emailSettingData.account_id,
              channelOrgId = emailSettingData.org_id
            )
          })

        //        logger.debug(s"getChannelForScheduling found: $debug_findForScheduling")

        debug_findForScheduling


    }
  }

  /**
   * Calculates the final daily quota for email scheduling, considering warmup settings, sender rotation,
   * and pending approvals for AutoEmailMagicContent steps.
   *
   * The limit calculation works as follows:
   * 1. Determine the base daily quota considering warmup settings and the minimum of campaign or channel limits.
   * 2. For the first step, adjust the quota based on sender rotation (round-robin) to ensure fair distribution across sender emails.
   * 3. If the provided campaign step type is AutoEmailMagicContent, subtract the count of pending approval prospects from the quota to avoid over-scheduling.
   *
   * Example:
   * - Base daily quota is 100 emails (from warmup or campaign/channel limits).
   * - Campaign has 2 sender emails, and only 10 new prospects are available (less than needed to meet the limit).
   * - Sender rotation adjusts the quota to 5 emails per sender (total 10) to match available prospects.
   * - If the step type is AutoEmailMagicContent and there are 3 pending approvals, the final quota is reduced to 7 emails.
   * - This reduced quota of 7 is distributed as evenly as possible between the 2 sender emails: one sender gets 4 emails, and the other gets 3.
   */
  override def getFinalDailyQuotaAlsoConsideringSenderRotationRoundRobin(
                                                                          warmupSettingOpt: Option[CampaignWarmupSetting],
                                                                          channelOrCampaignMinDailyQuota: Int,
                                                                          campaign: CampaignForSchedulingType,
                                                                          campaignProspectService: CampaignProspectService,
                                                                          campaignStepType: CampaignStepType,
                                                                          isFirstStep: Boolean
                                                                        )(using Logger: SRLogger): Try[Int] = {

                                                                          /*
                                                                          campaign_limit -> suppose 20,
                                                                          and we have sent 10 emails,
                                                                          accordign to campaign limit we can send 10 more emails,

                                                                          and for email suppose the limit is 100 and we have sent 40 emails,
                                                                          so we can send 60 more emails,

                                                                          so in this case the limit should be the minimum of (campaign_limit, email_limit) => 10


                                                                          the other thing is round robin,
                                                                          so we have only 4 prospects and we have 4 sendign emails so each prospect should send 1 email

                                                                          */


    val dailyQuotaWithoutSenderRotationRoundRobin = getDailyQuotaConsideringWarmup(
      warmupSettingOpt = warmupSettingOpt,
      channelOrCampaignMinDailyQuota = channelOrCampaignMinDailyQuota
    )

    // Step 1: First adjust for MagicContent PendingApprovals by subtracting pending approvals from the total quota
    val quotaAfterMagicContentAdjustment: Try[Int] = if (EmailChannelScheduler.isPendingApprovalAdjustmentNeeded(stepType = campaignStepType)) {

      Logger.debug(s"Step type is AutoEmailMagicContent. Fetching pending approval count to adjust quota ($dailyQuotaWithoutSenderRotationRoundRobin).")

      campaignProspectService.getPendingApprovalCountForCampaign(
        campaignId = CampaignId(campaign.campaign_id),
        campaignStepType = campaignStepType,
        teamId = TeamId(campaign.team_id)
      ).map { pendingApprovalCount =>

        val adjustedQuota = dailyQuotaWithoutSenderRotationRoundRobin - pendingApprovalCount
        Logger.debug(s"AutoMagicContent adjustment: Subtracting pending approvals ($pendingApprovalCount). Adjusted quota: $adjustedQuota")
        Math.max(0, adjustedQuota) // Ensure quota is not negative

      }
    } else {
      // Not an AutoMagicContent step, keep the quota as is
      Success(dailyQuotaWithoutSenderRotationRoundRobin)
    }

    // Step 2: Apply sender rotation logic (only if isFirstStep) to the already adjusted quota
    val finalResult: Try[Int] = quotaAfterMagicContentAdjustment.flatMap { adjustedQuota =>
      if (isFirstStep) {
        val least_amount_of_prospects_needed_to_meet_limit = List(campaign.campaign_max_emails_per_day * campaign.count_of_sender_emails, adjustedQuota * campaign.count_of_sender_emails).min

        campaignProspectService.getSenderRotationStats(
          campaignId = CampaignId(campaign.campaign_id),
          teamId = TeamId(campaign.team_id),
          least_amount_of_prospects_needed_to_meet_limit = least_amount_of_prospects_needed_to_meet_limit,
          start_time_for_campaign_for_the_day = TimezoneUtils
            .getStartOfDayWithTimezone(timezone = campaign.timezone) //we are taking start of day for the timezone of campaign
        ).map { senderRotationStats =>
          val count_of_new_prospects_for_the_day = senderRotationStats.prospects_not_sent_any_emails
          Logger.debug(s"campaignProspectService.getSenderRotationStats count_of_new_prospects_for_the_day:  $count_of_new_prospects_for_the_day")

          // Apply sender rotation logic to the adjusted quota
          if (count_of_new_prospects_for_the_day < least_amount_of_prospects_needed_to_meet_limit
            && campaign.count_of_sender_emails > 0 //ensuring we don't divide by zero
          ) {
            val count = Math.floor(count_of_new_prospects_for_the_day.toDouble / campaign.count_of_sender_emails).toInt

            if (count == 0) {
              1 // if count_of_new_prospects_for_the_day is less than campaign.count_of_sender_emails we can't divide the emails equally
            } else {
              count
            }
          } else {
            adjustedQuota
          }
        }
      } else {
        // Not the first step, no sender rotation logic applied
        Success(adjustedQuota)
      }
    }

    Logger.info(s"getFinalDailyQuotaAlsoConsideringSenderRotationRoundRobin - Final Result: ${finalResult.getOrElse("Error")}, BaseQuota: $dailyQuotaWithoutSenderRotationRoundRobin, MinQuota: $channelOrCampaignMinDailyQuota, IsFirstStep: $isFirstStep, StepType: $campaignStepType")
    finalResult
  }

  override def findCampaignsForSchedulingChannel(
                                                  channelDataForScheduling: ChannelDataForScheduling.EmailChannelDataForScheduling,
                                                  campaignService: CampaignService
                                                )(
                                                  implicit Logger: SRLogger
                                                ): Try[Seq[CampaignForSchedulingType]] = {

    campaignService.findCampaignsForSchedulingEA(
      emailSettingId = channelDataForScheduling.emailSetting.id,
      Logger = Logger,
      team_id = TeamId(channelDataForScheduling.channelTeamId)
    )

  }

  override def updateLastScheduledForChannelAndSetInQueueForSchedulingToFalse(
                                                                               channelDataForScheduling: ChannelDataForSchedulingType,
                                                                               latestScheduledAt: Option[DateTime]
                                                                             )(
                                                                               implicit Logger: SRLogger
                                                                             ): Try[Int] = {

    Try {

      //      Logger.debug("Before emailSettingDAO._updateLastScheduled")

      val result = emailSettingDAO._updateLastScheduled(
        emailSettingId = channelDataForScheduling.emailSetting.id,
        latestEmailScheduledAt = latestScheduledAt,
        Logger = Logger
      )

      //      Logger.debug(s"after emailSettingDAO._updateLastScheduled result : $result")

      result

    }


  }

  override def fetchProspectsV3Multichannel(
                                             channelType: ChannelType,
                                             allowedProspectTimezones: List[String],
                                             prospectIdGreaterThan: Option[Long] = None, // for first page of results
                                             campaignId: Long,
                                             team_id: Long,
                                             limit: Int,
                                             channelRelevantStepIdAndDelay: Vector[SchedulerMapStepIdAndDelay],
                                             newProspectsInCampaign: Boolean,
                                             firstStepIsMagicContent: Boolean,
                                             sendOnlyToProspectsWhoWereSentInCurrentCycle: Option[DateTime],
                                             campaignProspectDAO: CampaignProspectDAO,
                                             useModifiedQueryForDripCampaign: Boolean,
                                             campaignProspectService: CampaignProspectService,
                                             campaign: CampaignForSchedulingType,
                                             campaign_email_setting: Option[CampaignEmailSettingForScheduler],
                                             org_id: OrgId,
//                                             emailNotCompulsoryEnabled: Boolean
                                           )(using Logger: SRLogger): Try[List[ProspectDataForChannelSchedulingType]] = {
    campaignProspectService
      .fetchProspectsV3MultichannelWithEmailOptionalCheck(
        channelType = channelType,
        prospectIdGreaterThan = prospectIdGreaterThan,
        allowedProspectTimezones = allowedProspectTimezones,
        campaignId = campaignId,
        teamId = TeamId(team_id),
        channelRelevantStepIdAndDelay = channelRelevantStepIdAndDelay,

        limit = limit,
        newProspectsInCampaign = newProspectsInCampaign,
        firstStepIsMagicContent = firstStepIsMagicContent,
        useModifiedQueryForDripCampaign = useModifiedQueryForDripCampaign,

        sendOnlyToProspectsWhoWereSentInCurrentCycle = sendOnlyToProspectsWhoWereSentInCurrentCycle,
        campaign_email_setting = Some(campaign.campaign_email_setting),
        org_id = org_id,
//        emailNotCompulsoryEnabled = emailNotCompulsoryEnabled,
        head_step_id = campaign.campaign_type_data.head_id
      ).map(_.asInstanceOf[List[ProspectDataForChannelScheduling.EmailChannelProspectForScheduling]])
  }

  override def releaseLockForCampaignsBeingScheduledNow(campaignIds: Seq[CampaignId])(
    implicit Logger: SRLogger
  ): Try[Map[Boolean, Set[CampaignId]]] = emailSchedulerJedisService.releaseLockForCampaignScheduling(campaignIds = campaignIds.toSet)

  override def acquireLockForCampaignsBeingScheduledNow(campaignIds: Set[CampaignId])(
    implicit Logger: SRLogger
  ): Try[Set[CampaignId]] = emailSchedulerJedisService
    .acquireLockAndAddToSetForCampaignScheduling(
      additionalIdsToLock = campaignIds,
      expireInSeconds = lockForCampaignsBeingScheduledNowExpiresInSeconds
    )


  override def getSentOrScheduledProspectsCountForChannel(
                                                           channelDataForScheduling: ChannelDataForSchedulingType,
                                                           campaignProspectDAO: CampaignProspectDAO,
                                                           taskDAO: TaskPgDAO,
                                                         )(
                                                           implicit logger: SRLogger
                                                         ): Try[Map[CampaignStepType, StepTypeAndCount]] = {

    val emailSetting = channelDataForScheduling.emailSetting
    val teamId = TeamId(channelDataForScheduling.channelTeamId)
    campaignProspectDAO.getSentOrScheduledProspectsCountForEmail(
        emailSettingIds = Seq(EmailSettingId(emailSetting.id)),
        accountTimezone = channelDataForScheduling.account_timezone,
        logger = logger,
        teamId = teamId
      )
      .map(countMap => {

        val count = countMap.find(p => p._1.emailSettingId == emailSetting.id).map(_._2)

        // MULTICHANNEL IMPLEMENTATION NOTE: ENSURE THAT ITS A EXHAUSTIVE MAP BELOW
        // If lets say no GeneralTask tasks were scheduled and we do not send AutoEmailStep -> 0
        // in the below map, then the campaign will not scheduled any AutoEmailStep steps going forwards
        // so its absolutely necessary for each stepType to be present in this Map
        Map(
          CampaignStepType.AutoEmailStep -> StepTypeAndCount(StepType(CampaignStepType.AutoEmailStep.toKey), Count(count.getOrElse(0))),
          CampaignStepType.ManualEmailStep -> StepTypeAndCount(StepType(CampaignStepType.ManualEmailStep.toKey), Count(count.getOrElse(0))),
          CampaignStepType.ManualEmailMagicContent -> StepTypeAndCount(StepType(CampaignStepType.ManualEmailMagicContent.toKey), Count(count.getOrElse(0))),
          CampaignStepType.AutoEmailMagicContent -> StepTypeAndCount(StepType(CampaignStepType.AutoEmailMagicContent.toKey), Count(count.getOrElse(0)))
        )

      })

  }


  override def calculateMaxToBeScheduledForNextHour(
                                                     channelDataForScheduling: ChannelDataForSchedulingType,
                                                     scheduleFromTime: DateTime,
                                                     scheduleTillTime: DateTime,
                                                   ): Int = {

    val minConsecutiveEmailDelay = channelDataForScheduling.emailSetting.min_delay_seconds

    val maxToBeScheduledForNextHour = Seconds.secondsBetween(scheduleFromTime, scheduleTillTime).getSeconds / minConsecutiveEmailDelay

    maxToBeScheduledForNextHour

  }

  override def getScheduledProspectsCountForCampaign(
                                                      channelDataForScheduling: ChannelDataForSchedulingType,
                                                      campaigns: Seq[CampaignForSchedulingType],
                                                      campaignProspectDAO: CampaignProspectDAO,
                                                      stepTypes: Seq[CampaignStepType]
                                                    )(
                                                      implicit logger: SRLogger
                                                    ): Try[Seq[ChannelScheduledProspectsCountForCampaignType]] = {


    val emailSettingId = channelDataForScheduling.emailSetting.id

    //    logger.debug(s"Enter getScheduledProspectsCountForCampaign campaigns: $campaigns :: campaignProspectDAO: $campaigns channelDataForScheduling: $channelDataForScheduling")

    campaignProspectDAO.getScheduledProspectsCountForCampaign(

        Logger = logger,
        emailSettingIds = Seq(EmailSettingId(emailSettingId)),

        campaignIdAndTimezone = campaigns.map(c => (c.campaign_id, c.timezone)),

        teamId = channelDataForScheduling.emailSetting.team_id

      )
      .map(campaignCountResults => {

        val countResults = campaigns
          .map(c => {

            val counts = campaignCountResults.find(counts => counts.campaignId == c.campaign_id).getOrElse(
              ScheduledProspectsCountForCampaignEmail(
                campaignId = c.campaign_id,
                newCount = 0,
                followupCount = 0,
                newCountNotSent = 0,
                followupCountNotSent = 0
              )
            )

            // We have same Limit for both Email Step Types as of now. Therefore, we will be using combined prospect count for both.
            val campaignCountsAllStepTypes = Seq(
              ScheduledProspectsCountForCampaign(
                campaignId = c.campaign_id,
                campaignStepType = CampaignStepType.AutoEmailStep,
                newCount = counts.newCount,
                followupCount = counts.followupCount,
                newCountNotSent = counts.newCountNotSent,
                followupCountNotSent = counts.followupCountNotSent
              ),
              ScheduledProspectsCountForCampaign(
                campaignId = c.campaign_id,
                campaignStepType = CampaignStepType.ManualEmailStep,
                newCount = counts.newCount,
                followupCount = counts.followupCount,
                newCountNotSent = counts.newCountNotSent,
                followupCountNotSent = counts.followupCountNotSent
              )
            )

            EmailChannelScheduledProspectsCountForCampaign(
              campaign = c,
              counts = campaignCountsAllStepTypes
            )
          })


        //        logger.debug(s"Exit getScheduledProspectsCountForCampaign: $countResults")


        countResults

      })


  }


  override def getScheduleFromTime(
                                    channelDataForScheduling: ChannelDataForSchedulingType
                                  ): DateTime = {

    val emailSetting = channelDataForScheduling.emailSetting

    val scheduleFromTime = if (emailSetting.latest_email_scheduled_at.isDefined && emailSetting.latest_email_scheduled_at.get.isAfterNow) {
      emailSetting.latest_email_scheduled_at.get
    } else DateTime.now(DateTimeZone.UTC)

    scheduleFromTime

  }


  override def getChannelTaskLimitPerDay(
                                          channelDataForScheduling: ChannelDataForSchedulingType,
                                          campaignStepType: CampaignStepType,
                                        ): Int = {

    channelDataForScheduling.emailSetting.quota_per_day

  }


  override def getCampaignDailyLimitForChannel(
                                                channelCountData: ChannelScheduledProspectsCountForCampaignType,
                                                channelDataForScheduling: ChannelDataForSchedulingType,
                                                campaignStepType: CampaignStepType
                                              ): Int = {

    val c = channelCountData.campaign

    c.campaign_max_emails_per_day

  }

  override def getLatestTaskScheduledAt(
                                         channelTasks: Vector[GenerateScheduleTaskData]
                                       ): Option[DateTime] = {

    /*channelTasks match {
      case emailTasks: ChannelScheduledTaskNew.NewBatchEmailScheduled =>
  */
    val latestEmailScheduledAt = if (channelTasks.nonEmpty) Some(channelTasks.last.schedulerDateTime) else None

    latestEmailScheduledAt

    /*}*/

  }

  /**
    * Generates a new email schedule with support for magic content.
    *
    * @param currentCampaign   Campaign information
    * @param currentVariant    Current step variant
    * @param nextStep          Optional next step
    * @param currentProspect   Prospect information
    * @param emailSetting      Email settings
    * @param schedulerDateTime Scheduled time
    * @param generatedContent  Optional generated content (subject, body) for magic content steps
    * @return New email schedule
    */
  def generateEmailScheduledNew(
                                 currentCampaign: ScheduleCampaign,
                                 nextStep: CampaignStepWithChildren,
                                 currentProspect: ProspectFoundForSchedulingByStepType,
                                 currentVariant: CampaignStepVariantForScheduling,
                                 emailSetting: EmailSettingCreateEmailSchedule,
                                 schedulerDateTime: DateTime,
                                 calendarAccountData: Option[CalendarAccountData],
                                 generatedContent: Option[GeneratedContent]
                               )(implicit Logger: SRLogger): Try[EmailScheduledNew3] = {

    val c = currentCampaign.campaign
    val p = currentProspect.prospectForScheduling.prospect
    val appendFollowups: Boolean = c.append_followups
    val allTrackingDomainsUsed = repTrackingHostService.getRepTrackingHosts().get.map(_.host_url)

    val emailsScheduledUuid = srUuidUtils.generateEmailsScheduledUuid()

    val emails_to_save_try: Try[EmailScheduledNew3] = {

      var prevSentEmails: Seq[PreviousFollowUp] = Seq()

      var failedToFetchPreviousEmails: Option[Throwable] = None
      val result = for {

        variant: CampaignStepVariantForScheduling <- currentCampaign.stepsMappedById(nextStep.id).variants.find(v => v.id == currentVariant.id) match {
          case Some(value) => Success(value)
          case None => Failure(new Throwable(s"No variant for em.variant_id ${currentVariant.id}"))
        }

        prospectForSchedule: ProspectDataForChannelSchedulingType <- currentCampaign.prospects.find(pr => pr.prospectForScheduling.prospect.id == p.id).map(_.prospectForScheduling) match {
          case Some(value) => Success(value)
          case None => Failure(new Throwable(s"NO prospectForSchedule found for campaign_id ${c.campaign_id} prospect_id ${p.id}"))
        }


        variantSubjectAndBody: SubjectAndBody <- {

              Success(
                CampaignStepData.getSubjectAndBodyFromStepData(
                  stepData = variant.step_data,
                  generated_content = generatedContent
                )
              )

        }

        variantSubject: String <- Success(variantSubjectAndBody.subject)
        variantBody: String <- Success(variantSubjectAndBody.body)

      } yield {

        if (appendFollowups || CampaignTemplateService.containsPrevSubjectMergeTag(s = variantSubject)) {

          emailScheduledDAOService.getPreviousSentSteps(
            campaignId = c.campaign_id,
            prospectId = prospectForSchedule.prospect.id,
            teamId = TeamId(id = prospectForSchedule.prospect.team_id)
          ) match {
            case Failure(e) =>
              Logger.error(s"(campaign ${c.campaign_id}) FATAL Error (emailScheduledId: ${emailsScheduledUuid}) Fetching previous emails: $e")
              failedToFetchPreviousEmails = Some(e)
            //                                EmailService.sendEmailToProspect(emailSendDetail, testProspect.get, manualEmail = false, previousEmails = prevSentEmails )
            case Success(previousEmails) =>
              Logger.info(s"(campaign ${c.campaign_id}) (emailScheduledId: ${emailsScheduledUuid}) Fetched previous sent emails total sent : ${previousEmails.length}")
              prevSentEmails = previousEmails
          }
        }

        val trackOpens = c.open_tracking_enabled
        val trackClicks = c.click_tracking_enabled

        val headStepId = c.campaign_type_data match {
          case CampaignTypeData.EmailChannelData(head_step_id) => head_step_id
          case CampaignTypeData.MultiChannelCampaignData(head_step_id) => head_step_id
          case CampaignTypeData.MagicContentData(head_step_id) => head_step_id
          case CampaignTypeData.DripCampaignData(nodes, edges, head_node_id) => 0L // FIXME DRIP: To be passed in getBodies
        }

        val (
          subject:String,
          html_body:String,
          base_body:String,
          text_body:String,
          is_edited_preview_email:Boolean,
          has_unsubscribe_link:Boolean
          ) = if (generatedContent.isEmpty) {
          emailServiceCompanion.getBodies(
            editedPreviewEmail = None,
            org_id = c.org_id,
            calendarAccountData = calendarAccountData,
            head_step_id = Some(headStepId),
            emailsScheduledUuid = EmailsScheduledUuid(emailsScheduledUuid),
            campaign_id = Some(c.campaign_id),
            step_id = Some(nextStep.id),
            prospect = p,
            emailOptions = EmailOptionsForGetBodies(
              for_editable_preview = false,
              editedPreviewEmailAlreadyChecked = false,

              custom_tracking_domain = emailSetting.custom_tracking_domain,
              default_tracking_domain = emailSetting.default_tracking_domain,
              default_unsubscribe_domain = emailSetting.default_unsubscribe_domain,
              email_sender_name = emailSetting.sender_name,
              sender_first_name = emailSetting.first_name,
              sender_last_name = emailSetting.last_name,
              signature = emailSetting.signature,

              opt_out_msg = c.opt_out_msg,
              opt_out_is_text = c.opt_out_is_text,
              append_followups = appendFollowups,
              bodyTemplate = variantBody,
              subjectTemplate = variantSubject,

              previousEmails = prevSentEmails,

              manualEmail = false,
              trackOpens = trackOpens,
              trackClicks = trackClicks,

              allTrackingDomainsUsed = allTrackingDomainsUsed
            ),
            selectedCalendarData = c.selected_calendar_data
          ) match {

            case Failure(e) =>

              Logger.error(s"(campaign ${c.campaign_id}) (emailScheduledId: ${emailsScheduledUuid}) FATAL getBodiesV2 failed::: ${LogHelpers.getStackTraceAsString(e)}")

              throw e

            case Success(bodies) =>

              /*val isBulkSender = emailSetting.bulk_sender
            val teamId = emailSetting.team_id
            val heaplabsTeamTid = 24


            var hasGmailFBLHeader = false*/


              /*val gmailFBLHeader = if (!hasGmailFBLHeader) None else Some(EmailHelper.getGmailFBLId(
              campaignId = campaignId,
              accountId = emailSetting.account_id,
              prospectId = prospectId
            ))*/
              (
                bodies.subject,
                bodies.htmlBody,
                bodies.baseBody,
                bodies.textBody,
                bodies.isEditedPreviewEmail,
                bodies.has_unsubscribe_link
              )
          }
        } else {
          val content: GeneratedContent = generatedContent.get
          (
            content.subject,
            content.body,
            content.base_body,
            content.text_body,
            false,
            false //fixme: should save in tasks table along with other fields
          )
        }


        val reply_to_email = if (c.campaign_email_setting.sender_email_settings_id == c.campaign_email_setting.receiver_email_settings_id) None else Some(c.reply_to_email)
        val reply_to_name = if (c.campaign_email_setting.sender_email_settings_id == c.campaign_email_setting.receiver_email_settings_id) None else Some(c.reply_to_name)


        val listUnsubscribeHeader = if (
          !has_unsubscribe_link ||
            AppConfig.disableListUnsubscribeHeaderForOrgIds.contains(c.org_id)
        ) None else Some(UnSubscribeLinkHelper.constructUnsubHeader(
          emailsScheduledUuid = EmailsScheduledUuid(emailsScheduledUuid),
          teamId = TeamId(c.team_id),
          replyToEmail = reply_to_email.getOrElse(c.from_email) // this must be the Reply To Email inbox
        ))

        EmailScheduledNew3(
          campaign_id = Some(c.campaign_id),
          step_id = Some(nextStep.id),
          is_opening_step = headStepId == nextStep.id,
          prospect_id = Some(p.id),
          prospect_account_id = p.internal.prospect_account_id,
          added_at = srDateTimeUtils.getDateTimeNow(),
          scheduled_at = schedulerDateTime,
          sender_email_settings_id = c.campaign_email_setting.sender_email_settings_id,
          template_id = currentVariant.template_id,
          variant_id = Some(currentVariant.id),
          rep_mail_server_id = currentCampaign.campaign.rep_mail_server_id,
          via_gmail_smtp = c.via_gmail_smtp,
          step_type = currentVariant.step_data.step_type,

          team_id = currentCampaign.campaign.team_id,
          account_id = currentCampaign.campaign.campaign_owner_id,
          campaign_name = Some(currentCampaign.campaign.campaign_name),
          step_name = nextStep.label,
          receiver_email_settings_id = c.campaign_email_setting.receiver_email_settings_id,

          to_email = p.email.get, //TODO: EMAIL_OPTIONAL check the path and test code (fetchProspectsV3Multichannel fetches prospects in email flow via inner join to prospect_emails table so .get will never fail for old path)
          to_name = Helpers.getProspectNameForSendingEmail(
            firstName = p.first_name,
            lastName = p.last_name
          ),

          from_email = c.from_email,
          from_name = c.from_name,

          reply_to_email = reply_to_email,
          reply_to_name = reply_to_name,

          rep_tracking_host_id = emailSetting.rep_tracking_host_id,

          scheduled_from_campaign = true,
          scheduled_manually = false,

          subject = subject,
          body = html_body,
          base_body = base_body,
          text_body = text_body,

          has_open_tracking = c.open_tracking_enabled,
          has_click_tracking = c.click_tracking_enabled,
          has_unsubscribe_link = has_unsubscribe_link,
          list_unsubscribe_header = listUnsubscribeHeader,
          gmail_fbl = None,

          email_thread_id = None,

          pushed_to_rabbitmq = false, // pushed via PushToRabbitmqCronService
          campaign_email_settings_id = c.campaign_email_setting.campaign_email_settings_id,
          generatedContent = generatedContent,
          is_edited_preview_email = is_edited_preview_email,
          uuid = emailsScheduledUuid
        )

      }

      result

    }

    emails_to_save_try
  }

  private def processManualEmailStep(
                                         t: GenerateScheduleTaskData,
                                         accountOrgBillingRelatedService: AccountOrgBillingRelatedService,
                                         channelDataForScheduling: ChannelDataForSchedulingType,
                                         taskService: TaskService,
                                         templateService: TemplateService,
                                         calendarAccountData: Option[CalendarAccountData],
                                         generatedContent: Option[GeneratedContent] = None
                                    )(implicit ec: ExecutionContext, Logger: SRLogger): Future[Either[CreateTaskError, String]] = {

    val checkAndIncrementProspectsContactedTry: Try[Int] = accountOrgBillingRelatedService.checkAndUpdateProspectsContacted(
      prospectId = ProspectId(t.currentProspect.prospectForScheduling.prospect.id),
      teamId = TeamId(t.currentCampaign.campaign.team_id),
      prospectTouchedType = ProspectTouchedType.ManualTaskScheduled,
      channelType = ChannelType.EmailChannel,
      updateLastContactedAt = false,
      logger = Logger,
    )

//    val subjectAndBody = getSubjectAndBodyFromStepData(t.currentVariant.step_data)
//    val (init_subject, init_body) = (subjectAndBody.subject, subjectAndBody.body)

    // Use generated content if available, otherwise use step data

    val subjectAndBody: SubjectAndBody = getSubjectAndBodyFromStepData(
      stepData =  t.currentVariant.step_data,
      generated_content = generatedContent
    )

    val (init_subject, init_body) = (subjectAndBody.subject, subjectAndBody.body)

    // Logger.info(s"Manual Task subject: $init_subject :: Body: $init_body ")
    val prospect: ProspectObject = t.currentProspect.prospectForScheduling.prospect
    val channel: ChannelType = t.currentVariant.step_data.step_type.channelType

    val calendarLink = if (t.currentCampaign.campaign.selected_calendar_data.isDefined) {
      EmailHelper._makeCalendarLinkUrl(
        prospectId = ProspectId(id = prospect.id),
        teamId = TeamId(id = prospect.team_id),
        sender_name = channelDataForScheduling.firstName + " " + channelDataForScheduling.lastName,
        campaignId = CampaignId(id = t.currentCampaign.campaign.campaign_id),
        stepId = t.currentVariant.step_id,
        calendarAccountData = calendarAccountData,
        selectedCalendarData = t.currentCampaign.campaign.selected_calendar_data
      ) match {
        case Failure(e) => {
          Logger.warn(s"Error Occurred while making calendar Link, Sending None ${e.printStackTrace()}")
          None
        }
        case Success(value) => Some(value)
      }
    } else {
      None
    }

    val internalMergeTags = InternalMergeTagValuesForProspect(
      unsubscribe_link = None, //currently unsubscribe link only for email channel
      sender_name = channelDataForScheduling.firstName + " " + channelDataForScheduling.lastName,
      sender_first_name = channelDataForScheduling.firstName,
      sender_last_name = channelDataForScheduling.lastName,
      previous_subject = None,
      signature = channelDataForScheduling.signature,
      sender_phone_number = channelDataForScheduling.sender_phone_number,
      calendar_link = calendarLink
    )
    val campaignStepContent: Future[Either[CreateTaskError, String]] = {
      for {
        _: Int <- checkAndIncrementProspectsContactedTry

        subject: String <- templateService.render(
          template = init_subject,
          prospect = prospect,
          internalMergeTags = internalMergeTags,
          channel = channel
        )
        body: String <- templateService.render(
          template = init_body,
          prospect = prospect,
          internalMergeTags = internalMergeTags,
          channel = channel
        )
        notes: String <- t.currentVariant.notes match {
          case None => Success("")

          case Some(note) =>
            templateService.render(
              template = note,
              prospect = prospect,
              internalMergeTags = internalMergeTags,
              channel = channel
            )
        }
      }
      yield {
        val data = CampaignStepContent(
          subject = subject,
          body = body,
          notes = notes
        )
        data
      }

    } match {
      case Failure(e) =>
        Logger.error("Error while replacing merge tags", err = e) //We are logging the error message here
        Future.failed(e)

      case Success(data: CampaignStepContent) =>

        val headStepId = t.currentCampaign.campaign.campaign_type_data match {
          case CampaignTypeData.EmailChannelData(head_step_id) => head_step_id
          case CampaignTypeData.MultiChannelCampaignData(head_step_id) => head_step_id
          case CampaignTypeData.MagicContentData(head_step_id) => head_step_id
          case CampaignTypeData.DripCampaignData(nodes, edges, head_node_id) => 0L // FIXME DRIP: Used to determine is_opening_step
        }

        taskService.createTask(
          task_data = NewTask(
            campaign_id = Some(t.currentCampaign.campaign.campaign_id),
            campaign_name = Some(t.currentCampaign.campaign.campaign_name),
            step_id = Some(t.currentVariant.step_id),
            step_label = t.currentVariant.step_label,
            created_via = TaskCreatedVia.Scheduler,
            is_opening_step = Some(headStepId == t.currentVariant.step_id),
            task_type = TaskType.SendEmail,
            is_auto_task = false,
            task_data = TaskData.SendEmailData(
              subject = data.subject,
              body = data.body,
              email_message_id = None
            ),
            status = TaskStatus.Due(
              due_at = t.schedulerDateTime
            ),
            assignee_id = Some(channelDataForScheduling.channelOwnerAccountId),
            prospect_id = Some(t.currentProspect.prospectForScheduling.prospect.id),
            priority = t.currentVariant.priority.getOrElse(TaskPriority.Normal),
            emailsScheduledUuid = None,
            note = Some(data.notes),
          ),
          accountId = channelDataForScheduling.channelOwnerAccountId,
          teamId = t.currentCampaign.campaign.team_id
        )

    }

    campaignStepContent

  }

  private def processAndSaveEmails(
                                    emailsToBeScheduled: Seq[EmailScheduledNew3],
                                    emailSetting: EmailSettingCreateEmailSchedule,
                                    scheduleCampaigns: Seq[ScheduleCampaign],
                                    orgId: Long,
                                    calendarAccountData: Option[CalendarAccountData]
                                  )(implicit ec: ExecutionContext, Logger: SRLogger): Future[Seq[CampaignProspectUpdateScheduleStatus]] = {

    if(emailsToBeScheduled.isEmpty) {
      Logger.debug(s"emailsToBeScheduled.isEmpty No emails to be saved")
      Future(Seq())
    } else {

      val savedEmailsWithBodyStep: Seq[EmailScheduledNewAfterSaving] = emailsToBeScheduled
        .groupBy(_.campaign_email_settings_id.id)
        .flatMap { case (ces_id, es) =>

          emailScheduledDAOService.saveEmailsToBeScheduledAndUpdateCampaignDataV2(
            emailsToBeScheduled = es.toVector,
            campaign_email_setting_id = CampaignEmailSettingsId(ces_id),
            emailSendingFlow = None,
            Logger = Logger
          ).get
        }.toSeq

      if (emailsToBeScheduled.size != savedEmailsWithBodyStep.size) {
        // this should ideally never happen

        val toBeSavedForProspectIds: Set[Long] = emailsToBeScheduled
          .filter(_.prospect_id.isDefined)
          .map(_.prospect_id.get)
          .toSet

        val savedForProspectIds: Set[Long] = savedEmailsWithBodyStep
          .filter(_.prospect_id.isDefined)
          .map(_.prospect_id.get)
          .toSet

        val unsavedForProspectIds: Set[Long] = toBeSavedForProspectIds.diff(savedForProspectIds)

        val unsavedEmails = emailsToBeScheduled.filter(em => {
          em.prospect_id.isDefined &&
            unsavedForProspectIds.contains(em.prospect_id.get)
        })

        Logger.fatal(s"saveEmailsToBeScheduledAndUpdateCampaignDataV2 :: not all saved :: Unsaved AutoEmailSteps count: ${unsavedEmails.count(_.step_type == CampaignStepType.AutoEmailStep)} :: Unsaved ManualEmailStep Count: ${unsavedEmails.count(_.step_type == CampaignStepType.ManualEmailStep)} :: emailsToBeScheduled: ${emailsToBeScheduled.size} :: emails_saved: ${savedEmailsWithBodyStep.size} :: emailsToBeScheduledpidsidcid: ${emailsToBeScheduled.map(em => (em.prospect_id, em.step_id, em.campaign_id, em.scheduled_at))} :: savedEmailsStep1pidsidcid: ${savedEmailsWithBodyStep.map(em => (em.prospect_id, em.step_id, em.campaign_id, em.scheduled_at))} :: unsavedEmails: ${unsavedEmails.map(_.copy(body = "dummy", base_body = "dummy", text_body = "dummy"))}")
        Logger.shouldNeverHappen(s"saveEmailsToBeScheduledAndUpdateCampaignDataV2 :: not all saved :: unsavedEmails: ${unsavedEmails.map(_.copy(body = "dummy", base_body = "dummy", text_body = "dummy")).map(uE => s"prospect_id: ${uE.prospect_id}, team_id : ${uE.team_id}, campaign_id : ${uE.campaign_id}, step_id: ${uE.step_id}, step_type:${uE.step_type},   prospect_account_id : ${uE.prospect_account_id}")}")

        /*
          1-may-2024:
          storing not all saved in internal notes of campaign_prospects.
          this will help us filter out these prospects directly from db and we can run some checks about the current
          state of prospect which raised `not-all-saved` condition.
         */

        val internal_note_data: Seq[CampaignProspectInternalNote] = unsavedEmails.map(uE => {

          CampaignProspectInternalNote(
            team_id = TeamId(id = uE.team_id),
            campaign_id = uE.campaign_id.map(CampaignId(_)).get, // as this is scheduler flow we are expecting this to be present
            prospect_id = uE.prospect_id.map(ProspectId(_)).get, // as this is scheduler flow we are expecting this to be present
            internal_note = "not_all_saved"
          )

        })

        val total_internal_notes_saved: Int = campaignProspectService.updateInternalNotesForCampaignProspect(
          data = internal_note_data
        )

        ()

      }

      val scheduledCampaignProspectData = savedEmailsWithBodyStep
        .map(em => {
          CampaignProspectUpdateScheduleStatus(
            current_step_status_for_scheduler_data = CurrentStepStatusForSchedulerData.Due(due_at = em.scheduled_at),
            current_step_type = em.step_type,

            current_step_task_id = em.email_scheduled_id.toString, //this doesn't look correct adding email_scheduled_id as step_task_id
            step_id = em.step_id.get,
            campaign_id = em.campaign_id.get,
            prospect_id = em.prospect_id.get,
            email_message_id = Some(em.email_scheduled_id),
            current_campaign_email_settings_id = Some(em.campaign_email_setting_id)
          )
        })
      Future.successful(scheduledCampaignProspectData)
    }
  }


  override def saveTasksToBeScheduledAndUpdateCampaignDataV2(
                                                              channelTasks: Vector[GenerateScheduleTaskData],
                                                              channelDataForScheduling: ChannelDataForSchedulingType,
                                                              scheduleCampaigns: Seq[ScheduleCampaign],
                                                              orgId: Long,
                                                              taskService: TaskService,
                                                              templateService: TemplateService,
                                                              accountOrgBillingRelatedService: AccountOrgBillingRelatedService,
                                                              calendarAccountData: Option[CalendarAccountData]
                                                            )(implicit ec: ExecutionContext,
                                                              ws: WSClient,
                                                              system: ActorSystem,
                                                              materializer: Materializer,
                                                              Logger: SRLogger): Future[Seq[CampaignProspectUpdateScheduleStatus]] = {

    Logger.info("saveTasksToBeScheduledAndUpdateCampaignDataV2 Email")

    // Separate magic and regular tasks BEFORE checking the flag
    val (magicTasks, regularTasks) = channelTasks.partition(t =>
      t.currentVariant.step_data.step_type match {
        case CampaignStepType.AutoEmailMagicContent | CampaignStepType.ManualEmailMagicContent => true
        case _ => false
      }
    )

    val magic_content_allowed = orgMetadataService.getOrgMetadata(orgId = OrgId(id = orgId)).get.show_magic_content_steps.getOrElse(false)

    if (magic_content_allowed) {
      // If magic content is allowed, process magic tasks
      for {

        regularTaskResults: Seq[CampaignProspectUpdateScheduleStatus] <- handleRegularTasks(
          regularTasks = regularTasks,
          channelDataForScheduling = channelDataForScheduling,
          scheduleCampaigns  = scheduleCampaigns,
          orgId = orgId,
          taskService = taskService,
          templateService = templateService,
          accountOrgBillingRelatedService  = accountOrgBillingRelatedService,
          calendarAccountData = calendarAccountData
        )

        magicTaskResults: Seq[CampaignProspectUpdateScheduleStatus] <- handleMagicTasks(
          magicTasks = magicTasks,
          channelDataForScheduling = channelDataForScheduling,
          scheduleCampaigns = scheduleCampaigns,
          orgId = orgId,
          taskService = taskService,
          templateService = templateService,
          accountOrgBillingRelatedService = accountOrgBillingRelatedService,
          calendarAccountData = calendarAccountData
        )

      } yield magicTaskResults ++ regularTaskResults

    } else {

      // Log magic tasks when magic content is not allowed
      magicTasks.foreach { magicTask =>
        Logger.warn(s"Magic content task ignored for campaign ${magicTask.currentCampaign.campaign.campaign_id} " +
          s"with step type ${magicTask.currentVariant.step_data.step_type} " +
          "as magic content is not enabled for this organization")
      }


      // Process only regular tasks and return their results
      handleRegularTasks(
        regularTasks = regularTasks,
        channelDataForScheduling = channelDataForScheduling,
        scheduleCampaigns = scheduleCampaigns,
        orgId =  orgId,
        taskService =  taskService,
        templateService = templateService,
        accountOrgBillingRelatedService = accountOrgBillingRelatedService,
        calendarAccountData =  calendarAccountData
      )
    }
  }

  private def handleMagicTasks(
                                magicTasks: Vector[GenerateScheduleTaskData],
                                channelDataForScheduling: ChannelDataForSchedulingType,
                                scheduleCampaigns: Seq[ScheduleCampaign],
                                orgId: Long,
                                taskService: TaskService,
                                templateService: TemplateService,
                                accountOrgBillingRelatedService: AccountOrgBillingRelatedService,
                                calendarAccountData: Option[CalendarAccountData]
                              )(
                                implicit ec: ExecutionContext,
                                ws: WSClient,
                                system: ActorSystem,
                                materializer: Materializer,
                                Logger: SRLogger
                              ): Future[Seq[CampaignProspectUpdateScheduleStatus]] = {

    if(magicTasks.isEmpty) {
      Future.successful(Seq())
    } else {

      // 1. Partition tasks based on prospect's current step status
      val (approvedTasks, tasksToQueueOrPending) = magicTasks.partition { task =>
        task.currentProspect.prospectForScheduling.current_step_status_data match {
          case Some(_: CurrentStepStatusForSchedulerData.Approved) =>
            Logger.debug(s"Task for p:${task.currentProspect.prospectForScheduling.prospect.id}, s:${task.currentVariant.step_id} has defined Approved status. Scheduling.")
            true // Only schedule if status is Approved
          case Some(status: CurrentStepStatusForSchedulerData.Due) =>
            Logger.debug(s"Task for p:${task.currentProspect.prospectForScheduling.prospect.id}, s:${task.currentVariant.step_id} has defined Due status. Queueing/Pending.")
            false
          case Some(status: CurrentStepStatusForSchedulerData.Done) =>
             Logger.debug(s"Task for p:${task.currentProspect.prospectForScheduling.prospect.id}, s:${task.currentVariant.step_id} has defined Done status. Queueing/Pending.")
             false
          case Some(status: CurrentStepStatusForSchedulerData.Skipped) =>
             Logger.debug(s"Task for p:${task.currentProspect.prospectForScheduling.prospect.id}, s:${task.currentVariant.step_id} has defined Skipped status. Queueing/Pending.")
             false
          case Some(status: CurrentStepStatusForSchedulerData.PendingApproval) =>
             Logger.shouldNeverHappen(s"Impossible!! We are not picking up PendingApproval Prospects. Task for p:${task.currentProspect.prospectForScheduling.prospect.id}, s:${task.currentVariant.step_id} has defined PendingApproval status. Queueing/Pending.")
             false
          case Some(status: CurrentStepStatusForSchedulerData.AiContentQueued) =>
             Logger.shouldNeverHappen(s"Impossible!! We are not picking up AiContentQueued Prospects. Task for p:${task.currentProspect.prospectForScheduling.prospect.id}, s:${task.currentVariant.step_id} has defined AiContentQueued status. Queueing/Pending.")
             false
          case None =>
             Logger.debug(s"Task for p:${task.currentProspect.prospectForScheduling.prospect.id}, s:${task.currentVariant.step_id} has no defined status (None). Queueing/Pending.")
             false // No status defined means queue/pending
        }
      }

      // 2. Process tasksToQueueOrPending: Publish to MQ
      val queuedStatuses = _queueMagicTasksForGeneration(tasksToQueueOrPending)

      // 3. Process approvedTasks: Fetch content from TaskService and schedule
      val scheduledStatusesFuture = _handleApprovedMagicTasks(
        approvedTasks = approvedTasks,
        channelDataForScheduling = channelDataForScheduling,
        scheduleCampaigns = scheduleCampaigns,
        orgId = orgId,
        taskService = taskService,
        templateService = templateService,
        accountOrgBillingRelatedService = accountOrgBillingRelatedService,
        calendarAccountData = calendarAccountData
      )

      // 4. Combine results
      scheduledStatusesFuture.map { scheduledStatuses =>
        queuedStatuses ++ scheduledStatuses
      }
    }
  }

  /**
   * Handles magic content tasks that already have an 'Approved' status.
   * Fetches the approved content from the corresponding task and schedules the email.
   */
  private def _handleApprovedMagicTasks(
                                         approvedTasks: Vector[GenerateScheduleTaskData],
                                         channelDataForScheduling: ChannelDataForSchedulingType,
                                         scheduleCampaigns: Seq[ScheduleCampaign],
                                         orgId: Long,
                                         taskService: TaskService,
                                         templateService: TemplateService,
                                         accountOrgBillingRelatedService: AccountOrgBillingRelatedService,
                                         calendarAccountData: Option[CalendarAccountData]
                                       )
                                       (implicit
                                        ec: ExecutionContext,
                                        ws: WSClient,
                                        system: ActorSystem,
                                        materializer: Materializer,
                                        Logger: SRLogger
                                       ): Future[Seq[CampaignProspectUpdateScheduleStatus]] = {
    if (approvedTasks.isEmpty) {
      Future.successful(Seq.empty)
    } else {
      Logger.info(s"_handleApprovedMagicTasks :: Processing ${approvedTasks.size} approved magic content tasks...")

      // Fetch content for approved tasks
      val approvedContentFutures: Vector[Future[Option[(GenerateScheduleTaskData, GeneratedContent)]]] = approvedTasks.map { task =>
        val campaignId = CampaignId(task.currentCampaign.campaign.campaign_id)
        val prospectId = ProspectId(task.currentProspect.prospectForScheduling.prospect.id)
        val stepId = StepId(task.currentVariant.step_id)
        val teamId = TeamId(task.currentCampaign.campaign.team_id)

        taskService.findMagicContentTaskByDetails( // Fetch the *approved* content
          campaignId = campaignId,
          stepId = stepId,
          prospectId = prospectId,
          teamId = teamId
        ).map {
          case Some(taskData) => taskData match {
            case data: TaskData.LinkedinConnectionRequestData => None
            case data: TaskData.SendLinkedinMessageData => None
            case data: TaskData.SendEmailData => None
            case data: TaskData.GeneralTaskData => None
            case data: TaskData.SendSmsData => None
            case data: TaskData.CallTaskData => None
            case data: TaskData.SendWhatsAppMessageData => None
            case data: TaskData.SendLinkedinInMailData => None
            case data: TaskData.ViewLinkedinProfileData => None
            case data: TaskData.AutoLinkedinInmail => None
            case data: TaskData.AutoLinkedinMessage => None
            case data: TaskData.AutoLinkedinConnectionRequest => None
            case data: TaskData.AutoViewLinkedinProfile => None
            case data: TaskData.AutoEmailMagicContentData =>
              Logger.info(s"_handleApprovedMagicTasks :: Fetched AutoEmailMagicContentData for c:${campaignId.id}, s:${stepId.id}, p:${prospectId.id}. prospect_name: ${task.currentProspect.prospectForScheduling.prospect.first_name.getOrElse("N/A")}. Subject: ${EmailChannelScheduler.truncate(data.generated_subject)}, Body: ${EmailChannelScheduler.truncate(data.generated_body)}")
              Some((
                task,
                GeneratedContent(
                  subject = data.generated_subject,
                  body = data.generated_body,
                  base_body = data.email_scheduled_base_body,
                  text_body = data.email_scheduled_text_body
                )
              ))

            case data: TaskData.ManualEmailMagicContentData =>
              Logger.info(s"_handleApprovedMagicTasks :: Fetched ManualEmailMagicContentData for c:${campaignId.id}, s:${stepId.id}, p:${prospectId.id}. prospect_name: ${task.currentProspect.prospectForScheduling.prospect.first_name.getOrElse("N/A")}. Subject: ${EmailChannelScheduler.truncate(data.generated_subject)}, Body: ${EmailChannelScheduler.truncate(data.generated_body)}")
              Some((
                task,
                GeneratedContent(
                  subject = data.generated_subject,
                  body = data.generated_body,
                  base_body = data.email_scheduled_base_body,
                  text_body = data.email_scheduled_text_body
                )
              ))
          }

          case None =>
            Logger.warn(s"_handleApprovedMagicTasks :: Content NOT FOUND via TaskService for c:${campaignId.id}, s:${stepId.id}, p:${prospectId.id} despite Approved status. Skipping scheduling.")
            None

        }.recover { case e =>
          Logger.error(s"_handleApprovedMagicTasks :: Error fetching approved content via TaskService for c:${campaignId.id}, s:${stepId.id}, p:${prospectId.id}. Skipping scheduling.", e)
          None
        }
      }

      Future.sequence(approvedContentFutures).flatMap { approvedContentResults =>
        val tasksWithApprovedContentPairs: Vector[(GenerateScheduleTaskData, GeneratedContent)] = approvedContentResults.flatten.toVector

        if (tasksWithApprovedContentPairs.isEmpty) {
          Logger.info(s"_handleApprovedMagicTasks :: tasksWithApprovedContentPairs is Empty")
          Future.successful(Seq.empty)
        } else {
          Logger.info(s"_handleApprovedMagicTasks :: Scheduling emails for ${tasksWithApprovedContentPairs.size} approved magic tasks with fetched content.")
          tasksWithApprovedContentPairs.foreach { case (task, content) =>
            Logger.info(s"_handleApprovedMagicTasks :: Paired task c:${task.currentCampaign.campaign.campaign_id}, s:${task.currentVariant.step_id}, p:${task.currentProspect.prospectForScheduling.prospect.id}. prospect_name: ${task.currentProspect.prospectForScheduling.prospect.first_name.getOrElse("N/A")} with Subject: ${EmailChannelScheduler.truncate(content.subject)}, Body: ${EmailChannelScheduler.truncate(content.body)}")
          }
          val emailSetting: EmailSettingCreateEmailSchedule = channelDataForScheduling.emailSetting

          // Create Manual Tasks if needed
          val manualTaskCreationFutures = tasksWithApprovedContentPairs.collect {
            case (task, contentTuple) if task.currentVariant.step_data.step_type == CampaignStepType.ManualEmailMagicContent =>
              processManualEmailStep(
                t = task,
                channelDataForScheduling = channelDataForScheduling,
                accountOrgBillingRelatedService = accountOrgBillingRelatedService,
                taskService = taskService,
                templateService = templateService,
                calendarAccountData = calendarAccountData,
                generatedContent = Some(contentTuple) // Use fetched approved content
              )
          }

          Future.sequence(manualTaskCreationFutures).flatMap { manualTaskResults =>
            manualTaskResults.foreach {
              case Left(err) => Logger.error(s"_handleApprovedMagicTasks :: Failed to create manual task for approved magic step: $err")
              case Right(taskId) => Logger.info(s"_handleApprovedMagicTasks :: Successfully created manual task $taskId for approved magic step.")
            }

            // Generate EmailScheduledNew for ALL approved tasks
            val emailsToBeScheduledWithApprovedContent: Try[Seq[EmailScheduledNew3]] = {
              val emailsToBeScheduledWithApprovedContentSeqTry: Seq[Try[EmailScheduledNew3]] = tasksWithApprovedContentPairs.map { case (task, contentTuple) =>
                Logger.info(s"_handleApprovedMagicTasks :: Generating EmailScheduledNew for c:${task.currentCampaign.campaign.campaign_id}, s:${task.currentVariant.step_id}, p:${task.currentProspect.prospectForScheduling.prospect.id}. prospect_name: ${task.currentProspect.prospectForScheduling.prospect.first_name.getOrElse("N/A")} with Subject: ${EmailChannelScheduler.truncate(contentTuple.subject)}, Body: ${EmailChannelScheduler.truncate(contentTuple.body)}")
                generateEmailScheduledNew(
                  currentCampaign = task.currentCampaign,
                  currentVariant = task.currentVariant,
                  nextStep = task.nextStep,
                  currentProspect = task.currentProspect,
                  emailSetting = emailSetting,
                  schedulerDateTime = task.schedulerDateTime,
                  calendarAccountData = calendarAccountData,
                  generatedContent = Some(contentTuple) // Use fetched approved content
                )
              }.toSeq

              Helpers.seqTryToTrySeq(emailsToBeScheduledWithApprovedContentSeqTry)

            }
            
            emailsToBeScheduledWithApprovedContent match {
              case Failure(exception) =>
                Logger.error(s"faild emails_to_save_try --- ${Try(exception.getMessage)}")
                Future.failed(exception)
              case Success(emailsToBeScheduledWithApprovedContentResult) =>

                emailsToBeScheduledWithApprovedContentResult.foreach(es => {
                  Logger.debug(s"_handleApprovedMagicTasks :: emailsToBeScheduledWithApprovedContent ::  Generated content :: c:${es.campaign_id}, s:${es.step_id}, p:${es.prospect_id} Subject :: ${EmailChannelScheduler.truncate(es.subject)} :: Body :: ${EmailChannelScheduler.truncate(es.body)}")
                })
                // Save emails
                processAndSaveEmails(
                  emailsToBeScheduled = emailsToBeScheduledWithApprovedContentResult,
                  emailSetting = emailSetting,
                  scheduleCampaigns = scheduleCampaigns,
                  orgId = orgId,
                  calendarAccountData = calendarAccountData
                )
                
              
            }

          }
        }
      }
    }
  }

  /**
   * Handles processing and saving emails for campaigns in CoPilot mode.
   * In CoPilot mode, emails require additional handling for content approval.
   *
   * @param tasks Tasks to process in CoPilot mode
   * @param contentMap Generated AI content map
   * @param emailSetting Email settings
   * @param scheduleCampaigns Campaign data
   * @param orgId Organization ID
   * @param calendarAccountData Optional calendar data
   * @return Sequence of campaign prospect update statuses
   */
  /*
  private def handleProcessAndSaveEmailsForCopilotMode(
                                                        tasks: Vector[GenerateScheduleTaskData],
                                                        contentMap: Map[String, (String, String)],
                                                        emailSetting: EmailSettingCreateEmailSchedule,
                                                        scheduleCampaigns: Seq[ScheduleCampaign],
                                                        orgId: Long,
                                                        calendarAccountData: Option[CalendarAccountData]
                                                      )(implicit
                                                        ec: ExecutionContext,
                                                        ws: WSClient,
                                                        system: ActorSystem,
                                                        materializer: Materializer,
                                                        Logger: SRLogger
                                                      ): Future[Seq[CampaignProspectUpdateScheduleStatus]] = {

    Logger.info(s"Processing ${tasks.size} tasks in CoPilot mode")

    if (tasks.isEmpty) {
      Future.successful(Seq.empty)
    } else {
      val allTrackingDomainsUsed = repTrackingHostService.getRepTrackingHosts().get.map(_.host_url)

      // Process each task and save them to campaign_edited_preview_emails table
      Future.sequence(tasks.map { task =>
        val taskId = createTaskContentId(task)
        val campaignId = task.currentCampaign.campaign.campaign_id
        val prospectId = task.currentProspect.prospectForScheduling.prospect.id
        val stepId = task.currentVariant.step_id
        val emailSettingId = CampaignEmailSettingsId(id = task.currentCampaign.campaign.campaign_email_setting.sender_email_settings_id)
        // Use a valid account ID for the editor
        val editedByAccountId = task.currentCampaign.campaign.team_id

        // Get content from content map or use original content
          val (subject, body) = contentMap.getOrElse(taskId, {
          // If not in the content map, get the original content from the variant
          val variantSubjectAndBody = CampaignStepData.getSubjectAndBodyFromStepData(
            stepData = task.currentVariant.step_data
          )
          (variantSubjectAndBody.subject, variantSubjectAndBody.body)
        })

        // Save to campaignEditedPreviewEmailDAO instead of emails_scheduled table
//          Logger.error(s"Error saving preview email in CoPilot mode for campaign $campaignId, prospect $prospectId, step $stepId", e)
          // FIXME_MAGIC_CONTENT
          Future.successful(
            CampaignProspectUpdateScheduleStatus(
              current_step_status_for_scheduler_data = CurrentStepStatusForSchedulerData.PendingApproval(
                pending_approval_at = DateTime.now()
              ),
              current_step_type = task.currentVariant.step_data.step_type,
              // Some how dont update the current_step_task_id from this flow
              current_step_task_id = taskId,
              step_id = stepId,
              campaign_id = campaignId,
              prospect_id = prospectId,
              email_message_id = None,
              current_campaign_email_settings_id = Some(emailSettingId)
            )
          )
      })
    }
  }

  */

  private def handleRegularTasks(
                                  regularTasks: Vector[GenerateScheduleTaskData],
                                  channelDataForScheduling: ChannelDataForSchedulingType,
                                  scheduleCampaigns: Seq[ScheduleCampaign],
                                  orgId: Long,
                                  taskService: TaskService,
                                  templateService: TemplateService,
                                  accountOrgBillingRelatedService: AccountOrgBillingRelatedService,
                                  calendarAccountData: Option[CalendarAccountData]
                                )(
                                  implicit ec: ExecutionContext,
                                  ws: WSClient,
                                  system: ActorSystem,
                                  materializer: Materializer,
                                  Logger: SRLogger
                                ): Future[Seq[CampaignProspectUpdateScheduleStatus]] = {

    val emailSetting: EmailSettingCreateEmailSchedule = channelDataForScheduling.emailSetting

    val emailsToBeScheduledTry :Try[Seq[EmailScheduledNew3]] = {
      val emailsToBeScheduledSeqTry = regularTasks.map(t => {
        // Process manual email step if applicable
        if (t.currentVariant.step_data.step_type == CampaignStepType.ManualEmailStep || t.currentVariant.step_data.step_type == CampaignStepType.ManualEmailMagicContent) {
          // For ManualEmailMagicContent, generatedContent will be None here, but processManualEmailStep handles fetching it if approved or using original.
          // For regular ManualEmailStep, generatedContent is not applicable.
          val subjectAndBody = CampaignStepData.getSubjectAndBodyFromStepData(t.currentVariant.step_data) // Get original subject/body
          Logger.info(s"handleRegularTasks :: Preparing for ManualEmailStep/ManualEmailMagicContent (before processManualEmailStep) for c:${t.currentCampaign.campaign.campaign_id}, s:${t.currentVariant.step_id}, p:${t.currentProspect.prospectForScheduling.prospect.id}. Original Subject: ${EmailChannelScheduler.truncate(subjectAndBody.subject)}, Original Body: ${EmailChannelScheduler.truncate(subjectAndBody.body)}")
          processManualEmailStep(
            t = t,
            channelDataForScheduling = channelDataForScheduling,
            accountOrgBillingRelatedService = accountOrgBillingRelatedService,
            taskService = taskService,
            templateService = templateService,
            calendarAccountData = calendarAccountData
          )
        }

        // Generate email scheduled new
        generateEmailScheduledNew(
          currentCampaign = t.currentCampaign,
          currentVariant = t.currentVariant,
          nextStep = t.nextStep,
          currentProspect = t.currentProspect,
          emailSetting = emailSetting,
          schedulerDateTime = t.schedulerDateTime,
          generatedContent = None, // none for regular tasks
          calendarAccountData = calendarAccountData
        )
      }).toSeq

      Helpers.seqTryToTrySeq(emailsToBeScheduledSeqTry)
    }
    
    emailsToBeScheduledTry match {
      case Failure(exception) =>
        Logger.error(s"faild emails_to_save_try --- ${Try(exception.getMessage)}")
        Future.failed(exception)
      case Success(emailsToBeScheduled) =>
        // Process and save emails
        processAndSaveEmails(
          emailsToBeScheduled = emailsToBeScheduled,
          emailSetting = emailSetting,
          scheduleCampaigns = scheduleCampaigns,
          orgId = orgId,
          calendarAccountData = calendarAccountData
        )
    }

  }

//  /**
//    * Generates content map for magic content tasks using AI service.
//    *
//    * @param tasks Vector of tasks requiring magic content
//    * @param orgId Organization ID for AI service configuration
//    * @return Future Map of task IDs to (subject, body) tuples
//    */
//  def generateMagicContentMap(
//                                       tasks: Vector[GenerateScheduleTaskData],
//                                       orgId: OrgId
//                                     )(implicit
//                                       ec: ExecutionContext,
//                                       ws: WSClient,
//                                       system: ActorSystem,
//                                       materializer: Materializer,
//                                       Logger: SRLogger
//                                     ): Future[Map[String, (String, String)]] = {
//
//
//
//    if (tasks.isEmpty) {
//      Future.successful(Map.empty)
//    } else {
//      Logger.info(s"Generating AI content for ${tasks.length} tasks")
//
//      val getOrderedSteps: Seq[CampaignStepWithChildren] = campaignDAOService.findOrderedSteps(
//        campaignId = tasks.head.currentCampaign.campaign.campaign_id, teamId = TeamId(id = tasks.head.currentCampaign.campaign.team_id)
//      )
//
//      // Process tasks and collect successful results
//      Future.sequence(
//        tasks.map { task =>
//          val taskId = createTaskContentId(
//            task =  task
//          )
//
//          val campaignId = task.currentCampaign.campaign.campaign_id
//          val prospectId = task.currentProspect.prospectForScheduling.prospect.id
//
//
//          emailScheduledDAOService.getPreviousSentSteps(
//            campaignId = task.currentCampaign.campaign.campaign_id,
//            prospectId = task.currentProspect.prospectForScheduling.prospect.id,
//            teamId =  TeamId(id = task.currentCampaign.campaign.team_id)
//          ) match {
//            case Failure(e) =>
//              Logger.shouldNeverHappen(s"Failed :: generateMagicContentMap emailScheduledDAO.getPreviousSentSteps :: (campaign $campaignId) FATAL Error prospect_id: ${task.currentProspect.prospectForScheduling.prospect.id} (task_current_variant_id: ${task.currentVariant.id}) :: Fetching previous emails", Some(e))
//              Future.successful(None)
//            //                                EmailService.sendEmailToProspect(emailSendDetail, testProspect.get, manualEmail = false, previousEmails = prevSentEmails )
//            case Success(previousEmails) =>
//              Logger.debug(s"generateMagicContentMap emailScheduledDAO.getPreviousSentSteps (campaign $campaignId) :: prospect_id: ${task.currentProspect.prospectForScheduling.prospect.id} (task_current_variant_id: ${task.currentVariant.id}) ::  Fetched previous sent emails total sent : ${previousEmails.length}")
//
//              val pe = previousEmails.sortBy(pfu => pfu.sent_at.getMillis).reverse
//
//              aiHyperPersonalizedService.generateContentForTask(
//                step_id = StepId(id = task.currentVariant.step_id),
//                step_data = task.currentVariant.step_data,
//                prospect_object = task.currentProspect.prospectForScheduling.prospect,
//                campaign_ai_generation_context = task.currentCampaign.campaign.ai_generation_context,
//                campaign_id = CampaignId(id = task.currentCampaign.campaign.campaign_id),
//                sender_email_settings_id = EmailSettingId(emailSettingId= task.currentCampaign.campaign.campaign_email_setting.sender_email_settings_id),
//                orgId = orgId,
//                previous_communications = pe,
//                orderedSteps = getOrderedSteps
//              )
//              .map(content => Some(taskId -> content))
//              .recover { case e =>
//                Logger.shouldNeverHappen(s"Failed to generate content for task ${task.currentVariant.step_id}, unique_task_id_for_mapping: ${taskId}", Some(e))
//                None // Skip failed tasks
//              }
//          }
//
//        }
//      ).map(_.flatten.toMap) // Remove None values and convert to Map
//    }
//  }




  /**
    * Validates generated content against requirements
    */
  def validateGeneratedContent(
                                        content: (String, String),
                                        task: GenerateScheduleTaskData
                                      )(implicit ec: ExecutionContext, Logger: SRLogger): Future[(String, String)] = {

    val MaxSubjectLength = 100
    val MaxBodyLength = 5000

    val (subject, body) = content

    if (subject.isEmpty || body.isEmpty) {
      Future.failed(ContentValidationError("Generated content cannot be empty"))
    } else if (subject.length > MaxSubjectLength) {
      Future.failed(ContentValidationError(s"Subject exceeds maximum length of $MaxSubjectLength"))
    } else if (body.length > MaxBodyLength) {
      Future.failed(ContentValidationError(s"Body exceeds maximum length of $MaxBodyLength"))
    } else {
      Future.successful(content)
    }
  }

  /* override def checkAndReturnMissingMergeTagsForChannel(
     channelDataForScheduling: ChannelDataForSchedulingType,
     nextStepId: Long,
     currentCampaign: ScheduleCampaign,
     currentProspect: ProspectForScheduling,
     currentVariant: CampaignStepVariantForScheduling,
     emailServiceCompanion: EmailServiceCompanion,
     templateService: TemplateService,
     campaignEditedPreviewEmailDAO: CampaignEditedPreviewEmailDAO,
     campaignProspectDAO: CampaignProspectDAO,

     Logger: SrLoggerTrait
   ): Try[(Seq[String], Boolean)] = {

     val emailSetting = channelDataForScheduling.emailSetting

     val emailSenderName: String = emailSetting.sender_name
     val sender_first_name: String = emailSetting.first_name
     val sender_last_name: String = emailSetting.last_name


     checkAndReturnMissingMergeTags(
       sender_name = emailSenderName,
       sender_first_name = sender_first_name,
       sender_last_name = sender_last_name,
       unsubscribe_link = Some("dummy_link"), // only for passing the check below, we know the campaign, prospect, step and emailScheduled ids would exist in this case.,
       nextStepId = nextStepId,
       currentCampaign = currentCampaign,
       currentProspect = currentProspect,
       signature = emailSetting.signature, // TODO: checkme
       step_data = currentVariant.step_data,
       currentVariant = currentVariant,
       campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO,
       emailServiceCompanion = emailServiceCompanion,
       templateService = templateService,
       campaignProspectDAO = campaignProspectDAO,

       Logger = Logger
     )


   }*/

  def filterProspectsBySentCountBasedOnProspectAccountLimit(
                                                             prospectIds: Set[ProspectId],
                                                             campaignTimezone: String,
                                                             maxEmailsPerProspectAccountPerDay: Int,
                                                             maxEmailsPerProspectAccountPerWeek: Int,
                                                             team_id: TeamId,
                                                             campaignProspectDAO: CampaignProspectDAO,
                                                             previousSelectedProspectIds: Set[ProspectId]
                                                           )(using logger: SRLogger): Try[Set[Long]] = {

    for {
      prospectIdsToAccountIdsMap: Map[ProspectId, Option[ProspectAccountsId]] <- campaignProspectDAO
        .mapProspectIdsWithProspectAccountIds(
          prospectIds = prospectIds,
          teamId = team_id
        )

      previousSelectedProspectIdsToAccountIdsMap: Map[ProspectId, Option[ProspectAccountsId]] <- campaignProspectDAO
        .mapProspectIdsWithProspectAccountIds(
          prospectIds = previousSelectedProspectIds,
          teamId = team_id
        )

      selectedProspectAccountIdToCountMap: Map[ProspectAccountsId, Int] <- Try {
        previousSelectedProspectIdsToAccountIdsMap
          .values
          .flatten
          .groupBy(p => p)
          .view.mapValues(prospectAccountsList => prospectAccountsList.size)
          .toMap
      }

      allProspectAccountIds: List[ProspectAccountsId] <- Try(prospectIdsToAccountIdsMap.values.toList.filter(_.isDefined).map(_.get).distinct)

      emailsScheduledCountsForProspectAccounts: Map[ProspectAccountsId, EmailsScheduledCount] <- {

        campaignProspectDAO.getEmailsScheduledInLast24HoursAnd7DaysForAProspectAccount(
          prospectAccountIds = allProspectAccountIds,
          campaignTimezone = campaignTimezone,
          teamId = team_id
        )
      }

      emailsThatCanBeScheduledForProspectAccount: Map[ProspectAccountsId, Int] <- Try {
        allProspectAccountIds.map(
            prospectAccountId => {

              val previouslyProspectsSelectedCount = if (selectedProspectAccountIdToCountMap.contains(prospectAccountId))
                selectedProspectAccountIdToCountMap(prospectAccountId)
              else 0

              if (emailsScheduledCountsForProspectAccounts.contains(prospectAccountId)) {

                val emailsScheduledCount = emailsScheduledCountsForProspectAccounts(prospectAccountId)

                (prospectAccountId, getEmailsCountThatCanBeScheduled(
                  emailsScheduledInLast24Hours = emailsScheduledCount.scheduled_in_last_24hours,
                  emailsScheduledInLastWeek = emailsScheduledCount.scheduled_in_last_week,
                  dailyLimit = maxEmailsPerProspectAccountPerDay,
                  weeklyLimit = maxEmailsPerProspectAccountPerWeek,
                  previouslyProspectsSelectedCount = previouslyProspectsSelectedCount,
                  prospectAccountId = prospectAccountId
                ))
              }
              else {

                val finalMaxEmailsPerProspectAccountPerDay = {
                  val res = maxEmailsPerProspectAccountPerDay - previouslyProspectsSelectedCount
                  if (res < 0) {
                    logger.warn(s"[EmailChannelScheduler.filterProspectsBySentCountBasedOnProspectAccountLimit] finalMaxEmailsPerProspectAccountPerDay < 0 : $res, prospect_account_id: ${prospectAccountId.id}, maxEmailsPerProspectAccountPerDay: $maxEmailsPerProspectAccountPerDay, previouslyProspectsSelectedCount: $previouslyProspectsSelectedCount")
                    0
                  } else res
                }

                val finalMaxEmailsPerProspectAccountPerWeek = {
                  val res = maxEmailsPerProspectAccountPerWeek - previouslyProspectsSelectedCount
                  if (res < 0) {
                    logger.warn(s"[EmailChannelScheduler.filterProspectsBySentCountBasedOnProspectAccountLimit] finalMaxEmailsPerProspectAccountPerWeek < 0 : $res, prospect_account_id: ${prospectAccountId.id}, maxEmailsPerProspectAccountPerWeek: $maxEmailsPerProspectAccountPerWeek, previouslyProspectsSelectedCount: $previouslyProspectsSelectedCount")
                    0
                  } else res
                }

                (prospectAccountId, Math.min(
                  finalMaxEmailsPerProspectAccountPerDay,
                  finalMaxEmailsPerProspectAccountPerWeek
                ))
              }
            }
          )
          .toMap
      }

      prospectIdsThatCanBeScheduled: Set[ProspectId] <- Try {

        var countMap = emailsThatCanBeScheduledForProspectAccount

        prospectIdsToAccountIdsMap.map {
            case (prospectId, prospectAccountIdOpt) =>
              if (prospectAccountIdOpt.isEmpty) {
                Some(prospectId)
              }
              else if (countMap(prospectAccountIdOpt.get) > 0) {
                countMap = countMap + (prospectAccountIdOpt.get -> (countMap(prospectAccountIdOpt.get) - 1))
                Some(prospectId)
              }
              else {
                None
              }
          }
          .toSet
          .filter(_.isDefined)
          .map(_.get)
      }

    } yield {
      prospectIdsThatCanBeScheduled.map(_.id)
    }

  }


  override def filterProspectsByChannelSpecificChecks(
                                                       prospectIds: Set[Long],
                                                       team_id: TeamId,
                                                       campaignForScheduling: CampaignForSchedulingType,
                                                       campaignProspectDAO: CampaignProspectDAO,
                                                       previousSelectedProspectIds: Set[ProspectId]
                                                     )(
                                                       using Logger: SRLogger

                                                     ): Try[Set[Long]] = {

    var logstatement = s"filterProspectsByChannelSpecificChecks, pids : ${prospectIds.size}, sample - ${prospectIds.take(5)} cid_${campaignForScheduling.campaign_id} "

    val campaignTimezone = campaignForScheduling.timezone

    val maxEmailsPerProspectPerDay = campaignForScheduling.max_emails_per_prospect_per_day

    val maxEmailsPerProspectPerWeek = campaignForScheduling.max_emails_per_prospect_per_week

    val maxEmailsPerProspectAccountPerDay = campaignForScheduling.max_emails_per_prospect_account_per_day

    val maxEmailsPerProspectAccountPerWeek = campaignForScheduling.max_emails_per_prospect_account_per_week

    logstatement = logstatement + (
      s"maxEmlPPPD : ${maxEmailsPerProspectPerDay}, maxEmlPPPW : ${maxEmailsPerProspectPerWeek}," +
      s"maxEmlPPAPD : ${maxEmailsPerProspectAccountPerDay}, maxEmlPPAPW : ${maxEmailsPerProspectAccountPerWeek},")

    for {

      prospectIdsFilteredByEmailValidationStatus: Set[Long] <- {

        val validation_status = campaignProspectDAO
          .filterByProspectValidationStatus(
            prospectIds = prospectIds,
            team_id = team_id,
          )
        logstatement = logstatement + s" validation_status : ${validation_status.map(_.take(5))}"
        validation_status
      }

      prospectIdsAfterEmailValidationStatusFilter: Set[Long] <- Try {

        val after_validation = prospectIds
          .intersect(
            prospectIdsFilteredByEmailValidationStatus
          )
        logstatement = logstatement + s" after_validation : ${after_validation.take(5)}"
        after_validation

      }

      prospectIdsFilteredBySentCountBasedOnProspectLimit: Set[Long] <- {

        // FIXME MULTICHANNEL LATER: do we need a similar maxTasksPerProspectPerDay for other channels
        val pids_prospect_limit = campaignProspectDAO
          .filterProspectsBySentCountBasedOnProspectLimit(
            prospectIds = prospectIdsAfterEmailValidationStatusFilter,
            campaignTimezone = campaignTimezone,
            maxEmailsPerProspectPerWeek = maxEmailsPerProspectPerWeek,
            maxEmailsPerProspectPerDay = maxEmailsPerProspectPerDay,
            team_id = team_id,
          )

        logstatement = logstatement + s" pids_prospect_limit : ${pids_prospect_limit.map(_.take(5))}"

        pids_prospect_limit

      }

      prospectIdsFilteredBySentCountBasedOnProspectLimitFilter: Set[Long] <- Try {

        val email_validation_status_intersect_prospect_limit = prospectIdsAfterEmailValidationStatusFilter
          .intersect(
            prospectIdsFilteredBySentCountBasedOnProspectLimit
          )

        logstatement = logstatement + s" email_validation_status_intersect_prospect_limit : ${email_validation_status_intersect_prospect_limit.take(5)}"
        email_validation_status_intersect_prospect_limit

      }

      filterBasedOnProspectAccountLimit: Boolean <- {
        val filterOnPALflag = orgMetadataService.filterProspectsBasedOnDomainLimit(
          orgId = OrgId(campaignForScheduling.org_id)
        )

        logstatement = logstatement + s" filterOnPALflag : ${filterOnPALflag}"
        filterOnPALflag

      }

      prospectIdsFilteredBySentCountBasedOnProspectAccountLimit: Set[Long] <- {

        if (filterBasedOnProspectAccountLimit) {
          val filterOnPAL = filterProspectsBySentCountBasedOnProspectAccountLimit(
            prospectIds = prospectIdsFilteredBySentCountBasedOnProspectLimitFilter.map(ProspectId(_)),
            campaignTimezone = campaignTimezone,
            maxEmailsPerProspectAccountPerDay = maxEmailsPerProspectAccountPerDay,
            maxEmailsPerProspectAccountPerWeek = maxEmailsPerProspectAccountPerWeek,
            team_id = team_id,
            campaignProspectDAO = campaignProspectDAO,
            previousSelectedProspectIds = previousSelectedProspectIds
          )
          logstatement = logstatement + s" filterOnPAL : ${filterOnPAL.map(_.take(5))} - filter true"
          filterOnPAL
        }
        else {
          Success(prospectIdsFilteredBySentCountBasedOnProspectLimitFilter)
        }

      }


      finalFilteredProspectIds: Set[Long] <- Try {

        val prosp_limt_filter_intersect_prosp_acc_limit = prospectIdsFilteredBySentCountBasedOnProspectLimitFilter
          .intersect(
            prospectIdsFilteredBySentCountBasedOnProspectAccountLimit
          )
        logstatement = logstatement + s" prosp_limt_filter_intersect_prosp_acc_limit : ${prosp_limt_filter_intersect_prosp_acc_limit.take(5)} "
        prosp_limt_filter_intersect_prosp_acc_limit

      }

    } yield {

      logstatement = logstatement +s" finalFilteredProspectIds : ${finalFilteredProspectIds.take(5)} "
      Logger.info(logstatement)
      finalFilteredProspectIds
    }

  }

  override def getConsecutiveTaskScheduleDelay(
                                                channelDataForScheduling: ChannelDataForSchedulingType
                                              ): Int = {

    val emailSetting = channelDataForScheduling.emailSetting

    val minConsecutiveEmailDelay: Int = emailSetting.min_delay_seconds
    val maxConsecutiveEmailDelay: Int = emailSetting.max_delay_seconds

    val consecutiveEmailDelay = srRandomUtils.getRandomDelay(
      minDelay = minConsecutiveEmailDelay,
      maxDelay = maxConsecutiveEmailDelay
    )

    consecutiveEmailDelay

  }

  override def filterProspectsIfNeededForCampaign(
                                                   account: Account,
                                                   prospectsFoundForChecking: ProspectsFoundByStepType,
                                                   campaign: CampaignForSchedulingType,
                                                   teamId: TeamId,
                                                   Logger: SRLogger,
                                                 ): Try[ProspectsFoundByStepType] = Try {
    
    given srLogger: SRLogger = Logger // fixme

    //    Logger.debug("Entry filterProspectsIfNeededForCampaign")

    // if email not checked and have not been added to queue for validation, check emails

    val prospectsForValidation: Seq[ProspectForValidation] = if (!campaign.enable_email_validation) {

      Seq()

    } else {

      prospectsFoundForChecking
        .flatMap(_._2.prospects)
        .filter(p => !p.email_checked)
        .map(p => {

          val pr = p.prospect

          ProspectForValidation(
            id = pr.id,
            email = pr.email.get, //TODO: EMAIL_OPTIONAL check the path and test code (fetchProspectsV3Multichannel fetches prospects in email flow via inner join to prospect_emails table so .get will never fail)
            email_checked = p.email_checked,
            email_sent_for_validation = p.email_sent_for_validation,
            email_sent_for_validation_at = p.email_sent_for_validation_at
          )
        })
        .toSeq
    }
    /* in emailValidationService.sendProspectsForValidation to add a check
       1. we are not currently in between the campaign start and end time
       2. the time from now to campaign to start is more than or equal to 4 hrs then set priority to low else
          keep it medium
     */
    val currentTime: DateTime = DateTime.now(DateTimeZone.forID(campaign.timezone))
    val emailValidationPriority: EmailValidationPriority = EmailValidationService.setEmailValidationPriorityOnBasisOfCampaignStartTime(
      campaign = campaign,
      currentTimeAccordingToCampaignTimezone = currentTime
    )
    val debug_sendProspectsForValidation = emailValidationService.sendProspectsForValidation(
      priority = emailValidationPriority,
      logger = Logger,
      accountId = account.internal_id,
      teamId = teamId,
      orgId = account.org.id,
      isAgency = account.org.is_agency,
      idsOrEmailsForValidation = IdsOrEmailsForValidation.EntitiesForValidation(
        emailsForValidations = EmailsForValidationWithInitiator.ProspectEmailsForValidation(
          entitiesForValidation = prospectsForValidation.map(EmailForValidation.fromProspectForValidation),
          initiatorCampaignId = CampaignId(id = campaign.campaign_id)
        )
      ),
    ) match {
      case Failure(e) =>
        Logger.error(s"(campaign ${campaign.campaign_id}) FATAL error scheduleProspectsForValidation: ${LogHelpers.getStackTraceAsString(e)}")

        throw e

      case Success(totalUpdatedProspects) =>
        if(totalUpdatedProspects > 0) {
          Logger.info(s"(campaign ${campaign.campaign_id}) marked $totalUpdatedProspects prospects as sent_for_validation ")
        }

        val prospectsForScheduling = if (campaign.enable_email_validation) {

          prospectsFoundForChecking
            .map {
              case (stepType, dataForSchedulingType) =>
                val updatedData = dataForSchedulingType.copy(
                  prospects = dataForSchedulingType.prospects
                    .filter(_.email_checked)
                )

                (stepType, updatedData)
            }
            .toMap
        } else {
          prospectsFoundForChecking
        }

        prospectsForScheduling

    }

    //    Logger.debug(s"Exit filterProspectsIfNeededForCampaign : $debug_sendProspectsForValidation")

    debug_sendProspectsForValidation


  }

  /**
    * Splits tasks into magic content and regular tasks
    *
    * @param tasks Vector of tasks to split
    * @return Tuple of (magicContentTasks, regularTasks)
    */
  def splitMagicContentTasks(tasks: Vector[GenerateScheduleTaskData]): (Vector[GenerateScheduleTaskData], Vector[GenerateScheduleTaskData]) = {
    tasks.partition(task => isMagicContentTask(task.currentVariant.step_data.step_type))
  }

  /**
    * Creates a unique task identifier for content mapping
    *
    * @param task The task to create an ID for
    * @return String identifier in format "campaignId_stepId_prospectId"
    */
  def createTaskContentId(task: GenerateScheduleTaskData): String = {
    EmailChannelScheduler.generateTaskId(
      campaignId = task.currentCampaign.campaign.campaign_id,
      stepId = task.currentVariant.step_id,
      prospectId = task.currentProspect.prospectForScheduling.prospect.id
    )
  }


  /**
   * Publishes requests to the message queue for generating AI content for magic tasks.
   * Updates the prospect status to AiContentQueued.
   */
  private def _queueMagicTasksForGeneration(
                                             tasksToQueue: Vector[GenerateScheduleTaskData]
                                           )
                                           (implicit
                                            ec: ExecutionContext,
                                            Logger: SRLogger
                                           ): Seq[CampaignProspectUpdateScheduleStatus] = {
    if (tasksToQueue.isEmpty) {
      Seq.empty
    } else {

      val queuedStatusesTrys = tasksToQueue.map { task =>
        Try {
          val campaign = task.currentCampaign.campaign
          val prospect = task.currentProspect.prospectForScheduling.prospect
          val variant = task.currentVariant
          val stepData = variant.step_data

          val request: MqAiContentGenerationRequest = MqAiContentGenerationRequest(
            campaignId = CampaignId(campaign.campaign_id),
            stepId = StepId(variant.step_id),
            prospectId = ProspectId(prospect.id),
            variantId = variant.id,
            campaignEmailSettingsId = campaign.campaign_email_setting.campaign_email_settings_id,
            senderEmailSettingsId = EmailSettingId(campaign.campaign_email_setting.sender_email_settings_id),
            orgId = OrgId(campaign.org_id),
            teamId = TeamId(campaign.team_id),
            scheduledAt = task.schedulerDateTime,
            logId = None
          )

          // The MqAiContentGenerationRequest is assumed to be created with a temporary logId (e.g., 0L)
          // as the actual DB ID is not known until after this insertion. The cron job will update it.
          val auditLogData: LlmAuditLogInsertData = LlmAuditLogInsertData(
            team_id = request.teamId, // Assuming 'request' is MqAiContentGenerationRequest and has teamId
            prospect_id = ProspectId(id = prospect.id), // prospect.id is ProspectId
            llm_tool = LlmFlow.MagicColumnFlow.model,
            status = ProspectColGenStatus.Pending,
            flow = LlmFlow.MagicColumnFlow,
            column_def_id = None,
            consumed_token_count_prompt_input = None,
            consumed_token_count_generation_output = None,
            request_log_id = Logger.logRequestId,
            queue_message = Json.toJson(request) // Store the MqAiContentGenerationRequest as JSON
          )

          llmAuditLogDAO.insertLlmAuditLog(
            logs = List(auditLogData)
          ) match {
            case Success(ids) if ids.nonEmpty =>
              Logger.debug(s"Successfully inserted LLM audit log for AI content generation. Log ID(s): ${ids.mkString(", ")}. c:${campaign.campaign_id}, s:${variant.step_id}, p:${prospect.id}")
              CampaignProspectUpdateScheduleStatus(
                current_step_status_for_scheduler_data = CurrentStepStatusForSchedulerData.AiContentQueued(
                  ai_content_queued_at = srDateTimeUtils.getDateTimeNow()
                ),
                current_step_type = stepData.step_type,
                current_step_task_id = "", // Task ID might be empty until generation/approval
                step_id = variant.step_id,
                campaign_id = campaign.campaign_id,
                prospect_id = prospect.id,
                email_message_id = None,
                current_campaign_email_settings_id = Some(campaign.campaign_email_setting.campaign_email_settings_id)
              )
            case Success(_) => // Should not happen if we insert one item and get an empty list of IDs
              val errorMsg = s"LLM audit log insertion for AI content generation reported success but returned no IDs. c:${campaign.campaign_id}, s:${variant.step_id}, p:${prospect.id}"
              Logger.error(errorMsg)
              throw new RuntimeException(errorMsg)
            case Failure(e) =>
              Logger.error(s"Failed to insert LLM audit log for AI content generation request. c:${campaign.campaign_id}, s:${variant.step_id}, p:${prospect.id}", e)
              throw e // Rethrow the original exception
          }
        }
      }
      val (queuedSuccesses, queuedFailures) = queuedStatusesTrys.partition(_.isSuccess)
      queuedFailures.foreach {
        case Failure(e) => Logger.shouldNeverHappen("_queueMagicTasksForGeneration :: Error processing magic task for MQ publishing", Some(e))
        case Success(_) => Logger.shouldNeverHappen("_queueMagicTasksForGeneration :: Already Partitioned success differently")
      }
      queuedSuccesses.collect { case Success(status) =>

        Logger.info(s"_queueMagicTasksForGeneration :: Published successfully to mq status  StepId : ${status.step_id} :: ProspectId :: ${status.prospect_id} ")
        status

      }.toSeq
    }
  }

}

object EmailChannelScheduler {

  // Helper for logging
  private def truncate(str: String, maxLength: Int = 100): String = {
    if (str == null) "null"
    else if (str.length > maxLength) str.substring(0, maxLength) + "..."
    else str
  }



  def isPendingApprovalAdjustmentNeeded(stepType: CampaignStepType): Boolean = {
    stepType match {
      case CampaignStepType.AutoEmailMagicContent => true
      case AutoEmailStep |
           CampaignStepType.ManualEmailStep |
           CampaignStepType.LinkedinConnectionRequest |
           CampaignStepType.LinkedinMessage |
           CampaignStepType.LinkedinInmail |
           CampaignStepType.LinkedinViewProfile |
           CampaignStepType.AutoLinkedinConnectionRequest |
           CampaignStepType.AutoLinkedinMessage |
           CampaignStepType.GeneralTask |
           CampaignStepType.WhatsappMessage |
           CampaignStepType.SmsMessage |
           CampaignStepType.CallStep |
           CampaignStepType.MoveToAnotherCampaignStep |
           CampaignStepType.ManualEmailMagicContent |
           CampaignStepType.AutoLinkedinInmail |
           CampaignStepType.AutoLinkedinViewProfile

      => false
    }
  }



  /**
    * Determines if a step type is a magic content type (Auto or Manual)
    *
    * @param stepType The campaign step type to check
    * @return true if the step type is either AutoEmailMagicContent or ManualEmailMagicContent
    */
  def isMagicContentTask(stepType: CampaignStepType): Boolean = {
    stepType match {
      case CampaignStepType.AutoEmailMagicContent | CampaignStepType.ManualEmailMagicContent => true
      case AutoEmailStep |
           CampaignStepType.ManualEmailStep |
           CampaignStepType.LinkedinConnectionRequest |
           CampaignStepType.LinkedinMessage |
           CampaignStepType.LinkedinInmail |
           CampaignStepType.LinkedinViewProfile |
           CampaignStepType.AutoLinkedinConnectionRequest |
           CampaignStepType.AutoLinkedinMessage |
           CampaignStepType.GeneralTask |
           CampaignStepType.WhatsappMessage |
            CampaignStepType.SmsMessage |
            CampaignStepType.CallStep |
            CampaignStepType.MoveToAnotherCampaignStep |

           CampaignStepType.AutoLinkedinInmail |
           CampaignStepType.AutoLinkedinViewProfile

      => false
    }
  }

  def generateTaskId(
                      campaignId: Long,
                      stepId: Long,
                      prospectId: Long
                    ): String = {
    s"${campaignId}_${stepId}_${prospectId}"
  }

  def getSubjectAndBodyForMagicContent(
                                        stepType: CampaignStepType,
                                        generatedContent: Option[(String, String)],
                                        originalStepData: CampaignStepData
                                      ): (String, String) = {
    stepType match {
      case CampaignStepType.AutoEmailMagicContent | CampaignStepType.ManualEmailMagicContent =>
        generatedContent.getOrElse {
          val subjectAndBody = getSubjectAndBodyFromStepData(originalStepData)
          (subjectAndBody.subject, subjectAndBody.body)
        }
      case _ =>
        val subjectAndBody = getSubjectAndBodyFromStepData(originalStepData)
        (subjectAndBody.subject, subjectAndBody.body)
    }
  }

  def getEmailsCountThatCanBeScheduled(
                                        emailsScheduledInLast24Hours: Int,
                                        emailsScheduledInLastWeek: Int,
                                        dailyLimit: Int,
                                        weeklyLimit: Int,
                                        previouslyProspectsSelectedCount: Int,
                                        prospectAccountId: ProspectAccountsId
                                      )(using Logger: SRLogger): Int = {


    val finalMaxEmailsPerProspectAccountPerDay = {
      val res = dailyLimit - emailsScheduledInLast24Hours - previouslyProspectsSelectedCount
      if (res < 0) {
        Logger.warn(s"[EmailChannelScheduler.filterProspectsBySentCountBasedOnProspectAccountLimit] getEmailsCountThatCanBeScheduled finalMaxEmailsPerProspectAccountPerDay < 0 : $res, prospect_account_id: ${prospectAccountId.id}, dailyLimit: $dailyLimit, emailsScheduledInLast24Hours: $emailsScheduledInLast24Hours, previouslyProspectsSelectedCount: $previouslyProspectsSelectedCount")
        0
      } else res
    }

    val finalMaxEmailsPerProspectAccountPerWeek = {
      val res = weeklyLimit - emailsScheduledInLastWeek - previouslyProspectsSelectedCount
      if (res < 0) {
        Logger.warn(s"[EmailChannelScheduler.filterProspectsBySentCountBasedOnProspectAccountLimit] getEmailsCountThatCanBeScheduled finalMaxEmailsPerProspectAccountPerWeek < 0 : $res, prospect_account_id: ${prospectAccountId.id}, weeklyLimit: $weeklyLimit, emailsScheduledInLastWeek: $emailsScheduledInLastWeek, previouslyProspectsSelectedCount: $previouslyProspectsSelectedCount")
        0
      } else res
    }

    finalMaxEmailsPerProspectAccountPerDay.min(finalMaxEmailsPerProspectAccountPerWeek)

  }

  def takeProspectsBasedOnEmailPriority[ProspectFoundForScheduling](

                                                                     foundFollowUpProspects: List[ProspectFoundForScheduling],
                                                                     foundNewProspects: List[ProspectFoundForScheduling],
                                                                     fetchLimitForFirstStep: Int,
                                                                     dailyQuota: Int,
                                                                     fetchLimitForFollowupStep: Int,
                                                                     followUpCampaignSentCount: Int,
                                                                     overallFetchLimit: Int,
                                                                     newCampaignSentCount: Int,
                                                                     campaignEmailPriority: CampaignEmailPriority.Value,
                                                                     campaignId: Long,
                                                                     logger: SRLogger

                                                                   ): List[ProspectFoundForScheduling] = {


    val ratioFollowup = campaignEmailPriority match {
      case CampaignEmailPriority.EQUAL => 0.5
      case CampaignEmailPriority.FIRST_EMAIL => 0.3
      case CampaignEmailPriority.FOLLOWUP_EMAILS => 0.7
    }

    val maxMoreFollowUps = Math.ceil(dailyQuota * ratioFollowup).toInt - followUpCampaignSentCount
    val maxMoreNewUsers = Math.ceil(dailyQuota * (1 - ratioFollowup)).toInt - newCampaignSentCount

    logger.info(s"[debugging drip] foundNewProspects: ${foundNewProspects.length} foundFollowUpProspects: ${foundFollowUpProspects.length} maxMoreFollowUps: $maxMoreFollowUps maxMoreNewUsers: $maxMoreNewUsers")

    val totalFound = foundFollowUpProspects.size + foundNewProspects.size

    val takeProspects = if (totalFound <= overallFetchLimit) {

      logger.info(s"[debugging drip] totalFound <= overallFetchLimit true path totalFound: $totalFound overallFetchLimit: $overallFetchLimit")

      foundFollowUpProspects ::: foundNewProspects
    } else {

      logger.info(s"[debugging drip] totalFound <= overallFetchLimit false path totalFound: $totalFound overallFetchLimit: $overallFetchLimit")

      (foundFollowUpProspects.size <= maxMoreFollowUps, foundNewProspects.size <= maxMoreNewUsers) match {

        case (true, true) =>
          logger.info(s"[debugging drip] totalFound <= overallFetchLimit false path case (true, true)")
          (foundFollowUpProspects.take(fetchLimitForFollowupStep) ::: foundNewProspects.take(fetchLimitForFirstStep))

        case (true, false) =>
          val takeNewUsers = foundNewProspects.take(if (fetchLimitForFirstStep - foundFollowUpProspects.size >= 0) fetchLimitForFirstStep - foundFollowUpProspects.size else 0)

          logger.info(s"[debugging drip] totalFound <= overallFetchLimit false path case (true, false) takeNewUsers: ${takeNewUsers.length}")
          foundFollowUpProspects ::: takeNewUsers

        case (false, true) =>
          val takeFollowups = foundFollowUpProspects.take(if (fetchLimitForFollowupStep - foundNewProspects.size >= 0) fetchLimitForFollowupStep - foundNewProspects.size else 0)

          logger.info(s"[debugging drip] totalFound <= overallFetchLimit false path case (false, true) takeNewUsers: ${takeFollowups.length}")
          takeFollowups ::: foundNewProspects

        case (false, false) =>
          logger.info(s"[debugging drip] totalFound <= overallFetchLimit false path case (false, false)")
          foundNewProspects.take(maxMoreNewUsers) ::: foundFollowUpProspects.take(maxMoreFollowUps)

      }


    }

    // 8-feb-2024: this safety check was added to prevent overshooting
    val prospects = if (takeProspects.size > overallFetchLimit) {

      logger.warn(s"takeProspectsBasedOnEmailPriority: overshooting safety limit check hit: takeProspects.size: ${takeProspects.size} > overallFetchLimit $overallFetchLimit")

      Helpers
        .shuffleList(takeProspects)
        .take(overallFetchLimit)
        .toList

    } else {

      takeProspects
    }

    logger.info(s"takeProspectsBasedOnEmailPriority: cid_$campaignId :: Total prospects: ${prospects.size} :: foundFollowUpProspects: ${foundFollowUpProspects.size} :: foundNewProspects: ${foundNewProspects.size} :: dailyQuota: $dailyQuota :: newCampaignSentCount: $newCampaignSentCount, :: followUpCampaignSentCount: $followUpCampaignSentCount, :: ratioFollowup: $ratioFollowup :: maxMoreFollowUps: $maxMoreFollowUps :: maxMoreNewUsers: $maxMoreNewUsers :: fetchFollowupLimit: $fetchLimitForFollowupStep :: fetchNewUsersLimit: $fetchLimitForFirstStep :: random: ${(foundFollowUpProspects.size <= maxMoreFollowUps, foundNewProspects.size <= maxMoreNewUsers)}")

    prospects
  }

  // email-channel specific function


}