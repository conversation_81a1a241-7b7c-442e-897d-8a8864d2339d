package utils.mq.channel_scheduler

import org.apache.pekko.actor.ActorSystem
import api.accounts.{AccountService, TeamId}
import api.accounts.service.AccountOrgBillingRelatedService
import api.calendar_app.CalendarAppService
import api.call.models.PhoneNumberUuid
import api.campaigns.models.{InternalSchedulerRunLog, InternalSchedulerRunLogData}
import api.campaigns.{CampaignEditedPreviewEmailDAO, CampaignProspectDAO, CampaignStepDAO, CampaignStepVariantDAO}
import api.campaigns.services.{CampaignProspectService, CampaignService, CampaignsMissingMergeTagService}
import api.emails.CampaignProspectStepScheduleLogsDAO
import api.tasks.pgDao.TaskPgDAO
import api.tasks.services.TaskService
import org.joda.time.{DateTime, Seconds}
import play.api.libs.ws.WSClient
import sr_scheduler.models.ChannelData
import utils.cache_utils.service.SrRedisSimpleLockServiceV2
import utils.email.EmailServiceCompanion
import utils.email_notification.service.EmailNotificationService
import utils.mq.channel_scheduler.channels.ChannelSchedulerTrait
import utils.{Helpers, SRLogger, StringUtils}
import utils.mq.channel_scheduler.channels.CallChannelScheduler
import utils.mq.webhook.MQWebhookCompleted
import utils.shuffle.SrShuffleUtils
import utils.templating.TemplateService
import utils.helpers.LogHelpers
import utils.mq.services.{MQConfig, SimpleMqServiceTrait}
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}


case class MqCallChannelSchedulerMsg(teamId: Long, callSettingUuid: String)

class MqCallChannelScheduler(
                              callChannelScheduler:CallChannelScheduler,
                              accountService: AccountService,
                              //accountDAO: AccountDAO,
                              emailNotificationService: EmailNotificationService,
                              campaignService: CampaignService,
                              campaignProspectDAO: CampaignProspectDAO,
                              campaignStepVariantDAO: CampaignStepVariantDAO,
                              campaignStepDAO: CampaignStepDAO,
                              srShuffleUtils: SrShuffleUtils,
                              emailServiceCompanion: EmailServiceCompanion,
                              templateService: TemplateService,
                              taskDAO: TaskPgDAO,
                              taskService: TaskService,
                              campaignProspectService: CampaignProspectService,
                              campaignEditedPreviewEmailDAO: CampaignEditedPreviewEmailDAO,
                              mqWebhookCompleted: MQWebhookCompleted,
                              srRedisSimpleLockServiceV2: SrRedisSimpleLockServiceV2,
                              campaignsMissingMergeTagService: CampaignsMissingMergeTagService,
  srRollingUpdateCoreService: SrRollingUpdateCoreService,
                              accountOrgBillingRelatedService: AccountOrgBillingRelatedService,
                              calendarAppService: CalendarAppService

                           ) extends SimpleMqServiceTrait[MqCallChannelSchedulerMsg]{

  val queueBaseName: String = MQConfig.callChannelSchedulerQueueBaseName
  val prefetchCount: Int = MQConfig.callChannelSchedulerPrefetchCount

  def processMessage(msg: MqCallChannelSchedulerMsg)(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem): Future[Any] = {

    val startTime = DateTime.now()


    val logBase = s"[MqCallChannelScheduler] processMessage Uuid: ${msg.callSettingUuid} :: "

    given Logger: SRLogger = new SRLogger(logRequestId = logBase)
    Logger.info(s"processMessage:: ${msg}: started at $startTime")


    val channelData = ChannelData.CallChannelData(
      callSettingUuid = msg.callSettingUuid,
    )

    val channelSchedulerService: ChannelSchedulerTrait = callChannelScheduler

    channelSchedulerService.scheduleTasksForChannel(
      channelData = channelData,
      teamId = msg.teamId,
      accountService = accountService,
      //accountDAO = accountDAO,
      emailNotificationService = emailNotificationService,
      campaignService = campaignService,
      campaignProspectDAO = campaignProspectDAO,
      campaignStepVariantDAO = campaignStepVariantDAO,
      campaignStepDAO = campaignStepDAO,
      srShuffleUtils = srShuffleUtils,
      emailServiceCompanion = emailServiceCompanion,
      templateService = templateService,
      taskDAO = taskDAO,
      taskService = taskService,
      campaignProspectService = campaignProspectService,
      campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO,
      mqWebhookCompleted = mqWebhookCompleted ,
      srRedisSimpleLockServiceV2 = srRedisSimpleLockServiceV2,
      campaignsMissingMergeTagService = campaignsMissingMergeTagService,
      accountOrgBillingRelatedService = accountOrgBillingRelatedService,
        srRollingUpdateCoreService = srRollingUpdateCoreService,
      calendarAppService = calendarAppService

    ) .map{ res =>

      val endTime = DateTime.now()
      val timeTaken = Seconds.secondsBetween(startTime, endTime).getSeconds
      val timeTakeMillis = endTime.getMillis - startTime.getMillis

      campaignService.addInternalSchedulerRunLog(
        internalSchedulerRunLog = InternalSchedulerRunLog(
          team_id = TeamId(msg.teamId),
          internal_scheduler_run_log_data = InternalSchedulerRunLogData.CallChannelSchedulerLog(
            phone_number_uuid = PhoneNumberUuid(phone_number_uuid = msg.callSettingUuid)
          ),
          started_at = startTime,
          completed_at = endTime,
          saved_tasks_count = res.saved_tasks_count,
          reached_scheduler_step = res.reached_scheduler_step,
          log_trace_id = Logger.logTraceId
        )
      ) match {
        case Success(_) => //DO NOTHING
        case Failure(exception) => Logger.fatal(s"CallChannelSchedulerLog Failed to addInternalSchedulerRunLog", exception)
      }

      Logger.info(s"scheduled:: ${msg}: (timeTakeMillis: $timeTakeMillis) :: took from $startTime to $endTime (timetaken: $timeTaken) :: $res")

      res.saved_tasks_count

    }.recover { case e =>

      val endTime = DateTime.now()
      val timeTaken = Seconds.secondsBetween(startTime, endTime).getSeconds
      val timeTakeMillis = endTime.getMillis - startTime.getMillis

      Logger.error(s"failed:: ${msg}: (timeTakeMillis: $timeTakeMillis) :: took from $startTime to $endTime (timetaken: $timeTaken) :: ${LogHelpers.getStackTraceAsString(e)}")

      0
    }



  }

}
