package utils.mq.channel_scheduler

import org.apache.pekko.actor.ActorSystem
import org.joda.time.{DateTime, Seconds}
import play.api.libs.ws.WSClient
import sr_scheduler.models.ChannelData
import utils.{Helpers, SRLogger, StringUtils}
import utils.mq.channel_scheduler.channels.{ChannelSchedulerTrait, LinkedinChannelScheduler}
import api.accounts.{AccountService, TeamId}
import api.accounts.service.AccountOrgBillingRelatedService
import api.calendar_app.CalendarAppService
import api.campaigns.models.{InternalSchedulerRunLog, InternalSchedulerRunLogData}
import utils.email.EmailServiceCompanion
import api.campaigns.services.{CampaignProspectService, CampaignService, CampaignsMissingMergeTagService}
import utils.templating.TemplateService
import utils.mq.webhook.MQWebhookCompleted
import api.campaigns.{CampaignEditedPreviewEmailDAO, CampaignProspectDAO, CampaignStepDAO, CampaignStepVariantDAO}
import api.emails.CampaignProspectStepScheduleLogsDAO
import api.tasks.pgDao.TaskPgDAO
import api.tasks.services.TaskService
import utils.cache_utils.service.SrRedisSimpleLockServiceV2
import utils.email_notification.service.EmailNotificationService
import utils.shuffle.SrShuffleUtils
import utils.helpers.LogHelpers
import utils.mq.services.{MQConfig, SimpleMqServiceTrait}
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}

case class MqLinkedinChannelSchedulerMsg(teamId: Long, linkedinSettingId: String)

class MqLinkedinChannelScheduler(
                                  linkedinChannelScheduler: LinkedinChannelScheduler,
                                  accountService: AccountService,
                                  //accountDAO: AccountDAO,
                                  emailNotificationService: EmailNotificationService,
                                  campaignService: CampaignService,
                                  campaignProspectDAO: CampaignProspectDAO,
                                  campaignStepVariantDAO: CampaignStepVariantDAO,
                                  campaignStepDAO: CampaignStepDAO,
                                  srShuffleUtils: SrShuffleUtils,
                                  emailServiceCompanion: EmailServiceCompanion,
                                  templateService: TemplateService,
                                  taskDAO: TaskPgDAO,
                                  taskService: TaskService,
                                  campaignProspectService: CampaignProspectService,
                                  campaignEditedPreviewEmailDAO: CampaignEditedPreviewEmailDAO,
                                  campaignsMissingMergeTagService: CampaignsMissingMergeTagService,
                                  srRedisSimpleLockServiceV2: SrRedisSimpleLockServiceV2,
                                  mqWebhookCompleted: MQWebhookCompleted,
                                  accountOrgBillingRelatedService: AccountOrgBillingRelatedService,
  srRollingUpdateCoreService: SrRollingUpdateCoreService,
                                  calendarAppService: CalendarAppService
                               ) extends SimpleMqServiceTrait[MqLinkedinChannelSchedulerMsg] {

  val queueBaseName: String = MQConfig.linkedinChannelSchedulerQueueBaseName
  val prefetchCount: Int = MQConfig.linkedinChannelSchedulerPrefetchCount


  // PROSPECTS_EMAILS_TODO_UPDATE / PROSPECTS_EMAILS_TODO_READ_CLEANED
  /*
   * Use Case: msg received from queue contains ownerAccount id - schedule tasks to be sent from ownerAccountId
   */
  def processMessage(msg: MqLinkedinChannelSchedulerMsg)(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem): Future[Any] = {

    val startTime = DateTime.now()


    val logBase = s"[MqLinkedinChannelScheduler] processMessage eset_${msg.linkedinSettingId} :: "

    given Logger: SRLogger = new SRLogger(logRequestId = logBase)

    Logger.info(s"processMessage:: ${msg}: started at $startTime")

    val channelData = ChannelData.LinkedinChannelData(
      linkedinSettingId = msg.linkedinSettingId,
    )

    val channelSchedulerService: ChannelSchedulerTrait = linkedinChannelScheduler // hardcoding to linkedinChannelScheduler

    channelSchedulerService.scheduleTasksForChannel(
      channelData = channelData,
      teamId = msg.teamId,
      accountService = accountService,
      //accountDAO = accountDAO,
      emailNotificationService = emailNotificationService,
      campaignService = campaignService,
      campaignProspectDAO = campaignProspectDAO,
      campaignStepVariantDAO = campaignStepVariantDAO,
      campaignStepDAO = campaignStepDAO,
      srShuffleUtils = srShuffleUtils,
      emailServiceCompanion = emailServiceCompanion,
      templateService = templateService,
      taskDAO = taskDAO,
      taskService = taskService,
      campaignProspectService = campaignProspectService,
      campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO,
      campaignsMissingMergeTagService = campaignsMissingMergeTagService,
        srRedisSimpleLockServiceV2 = srRedisSimpleLockServiceV2,
      mqWebhookCompleted = mqWebhookCompleted,
        accountOrgBillingRelatedService = accountOrgBillingRelatedService,
      srRollingUpdateCoreService = srRollingUpdateCoreService,
      calendarAppService = calendarAppService
    )
      .map { res =>

        val endTime = DateTime.now()
        val timeTaken = Seconds.secondsBetween(startTime, endTime).getSeconds
        val timeTakeMillis = endTime.getMillis - startTime.getMillis

        campaignService.addInternalSchedulerRunLog(
          internalSchedulerRunLog = InternalSchedulerRunLog(
            team_id = TeamId(msg.teamId),
            internal_scheduler_run_log_data = InternalSchedulerRunLogData.LinkedinChannelSchedulerLog(
              linkedin_setting_uuid = msg.linkedinSettingId
            ),
            started_at = startTime,
            completed_at = endTime,
            saved_tasks_count = res.saved_tasks_count,
            reached_scheduler_step = res.reached_scheduler_step,
            log_trace_id = Logger.logTraceId
          )
        ) match {
          case Success(_) => //DO NOTHING
          case Failure(exception) => Logger.fatal(s"LinkedinChannelSchedulerLog Failed to addInternalSchedulerRunLog", exception)
        }

        Logger.info(s"scheduled:: ${msg}: (timeTakeMillis: $timeTakeMillis) :: took from $startTime to $endTime (timetaken: $timeTaken) :: $res")

        res.saved_tasks_count

      }
      .recover { case e =>

        val endTime = DateTime.now()
        val timeTaken = Seconds.secondsBetween(startTime, endTime).getSeconds
        val timeTakeMillis = endTime.getMillis - startTime.getMillis

        Logger.error(s"failed:: ${msg}: (timeTakeMillis: $timeTakeMillis) :: took from $startTime to $endTime (timetaken: $timeTaken) :: ${LogHelpers.getStackTraceAsString(e)}")

        0
      }


  }

}