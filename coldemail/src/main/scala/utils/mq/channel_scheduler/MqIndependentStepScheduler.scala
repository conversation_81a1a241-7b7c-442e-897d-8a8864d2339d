package utils.mq.channel_scheduler

import org.apache.pekko.actor.ActorSystem
import api.campaigns.services.{CampaignId, CampaignService}
import api.accounts.TeamId
import api.accounts.models.OrgId
import api.campaigns.CPAssignResult
import org.joda.time.DateTime
import utils.{Help<PERSON>, SRLogger, StringUtils}
import play.api.libs.ws.WSClient
import utils.mq.channel_scheduler.channels.IndependentStepSchedulerService
import utils.mq.services.{MQConfig, SimpleMqServiceTrait}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}

case class MqIndependentStepSchedulerMsg(
                                          campaignId: CampaignId,
                                          teamId: TeamId,
                                          org_id: OrgId
                                        )

class MQIndependentStepScheduler(
                                         campaignService:  CampaignService,
                                         independentStepScheduler:IndependentStepSchedulerService
                                       ) extends SimpleMqServiceTrait[MqIndependentStepSchedulerMsg] {

  val queueBaseName: String = MQConfig.independentStepSchedulerBaseName
  val prefetchCount: Int = MQConfig.independentStepSchedulerPrefetchCount

  override def processMessage(
                               msg: MqIndependentStepSchedulerMsg
                             )(
                               implicit ws: WSClient,
                               ec: ExecutionContext,
                               system: ActorSystem
                             ): Future[Any] = {

    val startTime = DateTime.now()

    val logBase = s"( ${StringUtils.genLogTraceId} ) :: [MQIndependentStepScheduler] processMessage campaign_id: ${msg.campaignId}"

    given Logger: SRLogger = new SRLogger(logRequestId = logBase)

    Logger.info(s"processMessage:: ${msg}: started at $startTime")

    val res = independentStepScheduler.preExecutionStepsAndChecks(
      campaign_id = msg.campaignId,
      team_id = msg.teamId,
      org_id = msg.org_id
    ) match {

      case Failure(exception) =>

      Logger.error(s"preExecutionStepsAndChecks  error:: campaign_id : ${msg.campaignId.id} :: team_id : ${msg.teamId.id}  ", exception)

      Failure(exception)

      case Success(value) =>

        campaignService.mqIndependentStepSchedulerUpdatePushedToMqToFalse(
          campaign_id = msg.campaignId,
          team_id = msg.teamId
        ) match {
          case Failure(exception) =>

            Logger.error(s"mqIndependentStepSchedulerUpdatePushedToMqToFalse  error:: campaign_id : ${msg.campaignId.id} :: team_id : ${msg.teamId.id}  ", exception)

          case _ =>

        }

       Success(value)


    }

    Future.fromTry {
      res
    }
  }



}