package utils.mq.channel_scheduler

import api.accounts.{AccountWarningCodeType, TeamId}
import api.call.models.{CallStatus, ParticipantCallStatus}
import api.campaigns.models.CampaignStepsStructure
import api.campaigns.CampaignStepWithChildren
import sr_scheduler.models.{ChannelData, ChannelType}
import utils.mq.channel_scheduler.channels.{CampaignStepCondition, ChannelId, ChildStepIdWithCondition, NextStepFinderForDrip}
import api.campaigns.models.CampaignStepType
import api.campaigns.services.CampaignId
import api.emails.models.EmailReplyType
import api.emails.{CampaignProspectStepScheduleLogDataForIgnore, RejectionReasonForCampaignProspectStepSchedule}
import api.prospects.models.{ProspectCategory, ProspectId}
import api.tasks.models.TaskStatusType
import api.tasks.models.TaskStatusType.{Failed, Skipped}
import api.team_inbox.model.ReplySentimentSubCategory
import api.team_inbox.service.{ReplySentimentForTeam, TeamInboxService}
import eventframework.ProspectObject
import org.joda.time.DateTime
import play.api.libs.json.JsValue
import sr_scheduler.models.ChannelType.{CallChannel, EmailChannel, LinkedinChannel}
import utils.SRLogger
import utils.mq.channel_scheduler.TableTypeForScheduler.{EmailScheduled, Tasks}
import utils.mq.channel_scheduler.channels.CampaignStepCondition.InvalidEmail
import play.api.libs.json.{JsError, JsResult, JsString, JsSuccess, JsValue, Json, OFormat, Reads, Writes}

import scala.concurrent.duration.DurationInt
import scala.concurrent.{Await, ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

enum TableTypeForScheduler(val key: String) {
  case EmailScheduled extends TableTypeForScheduler("email_scheduled")
  case Tasks extends TableTypeForScheduler("tasks")


  override def toString: String = key
}

object TableTypeForScheduler {

  def fromKey(tableType: String): Try[TableTypeForScheduler] = Try {
    tableType match {
      case TableTypeForScheduler.EmailScheduled.key => TableTypeForScheduler.EmailScheduled
      case TableTypeForScheduler.Tasks.key => TableTypeForScheduler.Tasks
      case _ => throw new IllegalArgumentException(s"Invalid table type: $tableType")
    }
  }

  def toKey(value: TableTypeForScheduler): String = value.toString

  implicit val reads: Reads[TableTypeForScheduler] = new Reads[TableTypeForScheduler] {
    override def reads(json: JsValue): JsResult[TableTypeForScheduler] = {
      fromKey(json.as[String]) match {
        case Success(status) => JsSuccess(status)
        case Failure(d) => JsError(s"Invalid TableTypeForScheduler value: ${d.getMessage}")
      }
    }
  }

  implicit val writes: Writes[TableTypeForScheduler] = new Writes[TableTypeForScheduler] {
    override def writes(status: TableTypeForScheduler): JsValue = JsString(toKey(status))
  }
}

case class SchedulerMapStepIdAndDelay(
                                       is_head_step_in_the_campaign: Boolean,
                                       currentStepType: CampaignStepType,
                                       nextStepType: CampaignStepType,
                                       currentStepId: Long,
                                       delayTillNextStep: Int
                                     )

case class FetchCampaignStepsData(
                                   stepsMappedById: Map[Long, CampaignStepWithChildren],
                                   campaignStepsStructure: CampaignStepsStructure,
                                   allCampaignSteps: Vector[SchedulerMapStepIdAndDelay],
                                   relevantCampaignStepsForChannel: Vector[SchedulerMapStepIdAndDelay],
                                 )

case class LastSentStepData(
                             sent_id: String,
                             channel_type: ChannelType,
                             sent_at: DateTime,
                             bounced: Option[Boolean],
                             replied: Option[Boolean],
                             replied_at: Option[DateTime],
                             clicked: Option[Boolean],
                             opened: Option[Boolean],
                             opened_at: Option[DateTime],
                             reply_type: Option[EmailReplyType.Value],
                             failure_reason: Option[String],
                             call_status: Option[ParticipantCallStatus],
                             reply_sentiment: Option[String],
                             table_type: TableTypeForScheduler,
                             task_status: TaskStatusType
                           )


object ChannelSchedulerService {


  def getChannelIdFromChannelData(
                                   channelData: ChannelData
                                 ): ChannelId = {

    channelData match {
      case email: ChannelData.EmailChannelData =>

        ChannelId(
          id = email.emailSettingId.toString
        )

      case general: ChannelData.GeneralChannelData =>
        ChannelId(
          id = general.generalChannelSettingUuid
        )

      case whatsapp: ChannelData.WhatsAppChannelData =>
        ChannelId(
          id = whatsapp.whatsAppSettingUuid
        )

      case linkedin: ChannelData.LinkedinChannelData =>
        ChannelId(id = linkedin.linkedinSettingId)

      case sms: ChannelData.SmsChannelData =>
        ChannelId(id = sms.smsSettingUuid)

      case call: ChannelData.CallChannelData =>
        ChannelId(id = call.callSettingUuid)
    }
  }

  /*
  def getDayOfWeek: Int = {
    val day: Int = DateTime.now().withZone(DateTimeZone.UTC).getDayOfWeek

    // if Sunday, above day is 7, Monday is 1
    // for the query we assume Sunday is 0, and so on
    if (day == 7) 0 else day
  }
  */

  private def checkDelay(
                          lastSentSteps: List[LastSentStepData],
                          stepData: CampaignStepWithChildren
                        ): Boolean = {
    (lastSentSteps.isEmpty || lastSentSteps.head.sent_at.isBefore(DateTime.now().minusSeconds(stepData.delay))) //lastSentSteps is ordered by sent_at desc
  }

  def isOpened(
                prospectObject: ProspectObject,
                lastSentSteps: List[LastSentStepData]
              ): Boolean = {
    val latestEmailStep = lastSentSteps.find(_.channel_type == EmailChannel)
    (prospectObject.email.isDefined && prospectObject.email.get.nonEmpty) &&
      latestEmailStep.nonEmpty &&
      (latestEmailStep.get.opened.isDefined && latestEmailStep.get.opened.get)
  }

  def isOpenedWithin(
                      prospectObject: ProspectObject,
                      lastSentSteps: List[LastSentStepData],
                      delay: Int
                    ): Boolean = {
    val latestEmailStep = lastSentSteps.find(_.channel_type == EmailChannel)
    val opened = isOpened(
      prospectObject = prospectObject,
      lastSentSteps = lastSentSteps
    )

    println(s"opened: $opened :: $latestEmailStep")

    println(s"openedAt: ${latestEmailStep.get.opened_at}")
    println(s"sent_at: ${latestEmailStep.get.sent_at}")
    println(s"delay: $delay")
    println(s"sentAt: ${latestEmailStep.get.sent_at.plusSeconds(delay)}")

    opened &&
      latestEmailStep.isDefined &&
      latestEmailStep.get.opened_at.isDefined &&
      latestEmailStep.get.opened_at.get.isBefore(latestEmailStep.get.sent_at.plusSeconds(delay))
  }

  private def isTheConditionMatchWithProspect(
                                               prospectObject: ProspectObject,
                                               condition: CampaignStepCondition,
                                               lastSentSteps: List[LastSentStepData],
                                               stepData: CampaignStepWithChildren,
                                               isProspectConnectedToLinkedin: Boolean,
                                               consider_delay: Boolean
                                             ): Either[RejectionReasonForCampaignProspectStepSchedule, Boolean] = {
    val delayMet: Boolean = checkDelay(
      lastSentSteps = lastSentSteps,
      stepData = stepData
    )
    val delayMetAndConsiderDelay = delayMet || !consider_delay // delay is met or we dont consider delay
    val hasEmail: Boolean = prospectObject.email.isDefined && prospectObject.email.get.nonEmpty
    val latestEmailStepFromEmailScheduledTable = lastSentSteps.find(step => step.channel_type == EmailChannel && step.table_type == EmailScheduled)
    val latestEmailStepFromTaskTable = lastSentSteps.find(step => step.channel_type == EmailChannel && step.table_type == Tasks)
    val whenStepDelayWillMeet: DateTime = if (lastSentSteps.nonEmpty) {
      lastSentSteps.head.sent_at.plusSeconds(stepData.delay)
    } else DateTime.now().plusMinutes(60)

    /**
     * case 1: if the next step is not auto we can schedule it right away without waiting for 12 hours so that it can get sent if other conditions match
     * case 2: if the next step is auto and all the conditions are met, but there has not been 12 hours since the last step, we dont send
     * case 3: if the next step is auto and all the conditions are met, and there has been 12 hours since the last step, we can send
     * case 4: if the conditions are met and this is the first step, we send it
     */
    val check12HourDelayForAutomaticStep = !stepData.step_type.isAuto ||
      (stepData.step_type.isAuto &&
        (lastSentSteps.isEmpty ||
          lastSentSteps.head.sent_at.isBefore(DateTime.now().minusHours(12))
          )
        )

    if (check12HourDelayForAutomaticStep) {
      condition match {
        case CampaignStepCondition.HasEmail =>
          if (delayMetAndConsiderDelay) {
            Right(hasEmail)
          } else Left(RejectionReasonForCampaignProspectStepSchedule.StepDelayNotMet(whenStepDelayWillMeet))


        case CampaignStepCondition.DoesNotHaveEmail =>
          if (delayMetAndConsiderDelay) {
            Right(!hasEmail)
          } else Left(RejectionReasonForCampaignProspectStepSchedule.StepDelayNotMet(whenStepDelayWillMeet))

        case CampaignStepCondition.HasLinkedinUrl =>
          if (delayMetAndConsiderDelay) {
            Right(prospectObject.linkedin_url.isDefined && prospectObject.linkedin_url.get.nonEmpty)
          } else Left(RejectionReasonForCampaignProspectStepSchedule.StepDelayNotMet(whenStepDelayWillMeet))

        case CampaignStepCondition.DoesNotHaveLinkedinUrl =>
          if (delayMetAndConsiderDelay) {
            Right(prospectObject.linkedin_url.isEmpty || prospectObject.linkedin_url.get.isEmpty)
          } else Left(RejectionReasonForCampaignProspectStepSchedule.StepDelayNotMet(whenStepDelayWillMeet))

        case CampaignStepCondition.HasPhoneNumber =>
          if (delayMetAndConsiderDelay) {
            Right(prospectObject.phone.isDefined && prospectObject.phone.get.nonEmpty)
          } else Left(RejectionReasonForCampaignProspectStepSchedule.StepDelayNotMet(whenStepDelayWillMeet))

        case CampaignStepCondition.DoesNotHavePhoneNumber =>
          if (delayMetAndConsiderDelay) {
            Right(prospectObject.phone.isEmpty || prospectObject.phone.get.isEmpty)
          } else Left(RejectionReasonForCampaignProspectStepSchedule.StepDelayNotMet(whenStepDelayWillMeet))

        case CampaignStepCondition.NoCondition =>
          if (delayMetAndConsiderDelay) {
            Right(true)
          } else Left(RejectionReasonForCampaignProspectStepSchedule.StepDelayNotMet(whenStepDelayWillMeet))

        case CampaignStepCondition.ImpossibleCondition =>
          Right(false) // will always be false

        case CampaignStepCondition.InvalidEmail =>
          //our scheduler will never schedule an email unless we have run validation on an email
          //other schedulers (call, linkedIn etc) can pick a prospect that has invalid email
          if (delayMetAndConsiderDelay) {
            Right(hasEmail && prospectObject.internal.invalid_email.getOrElse(false))
          } else Left(RejectionReasonForCampaignProspectStepSchedule.StepDelayNotMet(whenStepDelayWillMeet))

        case CampaignStepCondition.ValidEmail =>
          if (delayMetAndConsiderDelay) {
            Right(hasEmail &&
              (!prospectObject.internal.invalid_email.getOrElse(false)))
          } else Left(RejectionReasonForCampaignProspectStepSchedule.StepDelayNotMet(whenStepDelayWillMeet))

        case CampaignStepCondition.Bounced =>
          if (delayMet) {
            Right(hasEmail &&
              (latestEmailStepFromEmailScheduledTable.nonEmpty &&
              latestEmailStepFromEmailScheduledTable.get.bounced.isDefined && latestEmailStepFromEmailScheduledTable.get.bounced.get)
            )
          } else Left(RejectionReasonForCampaignProspectStepSchedule.StepDelayNotMet(whenStepDelayWillMeet))

        case CampaignStepCondition.Delivered =>
          //delay is met, prospect has email, and it didn't bounce
          //(Check conditions, if task skipped, or marked as completed without actually doing it, should be considered delivered)
          if (delayMet) {
            Right(hasEmail &&
              ((latestEmailStepFromEmailScheduledTable.nonEmpty &&
                (latestEmailStepFromEmailScheduledTable.get.bounced.isDefined && !latestEmailStepFromEmailScheduledTable.get.bounced.get)) ||
                (latestEmailStepFromTaskTable.isDefined && latestEmailStepFromTaskTable.get.task_status == TaskStatusType.Skipped) ||
                (latestEmailStepFromEmailScheduledTable.isEmpty && latestEmailStepFromTaskTable.isDefined && latestEmailStepFromTaskTable.get.task_status == TaskStatusType.Done))
            )
          } else Left(RejectionReasonForCampaignProspectStepSchedule.StepDelayNotMet(whenStepDelayWillMeet))

        case CampaignStepCondition.NotOpened =>
          //the way opened and not opened works will change here
          // OLD - there is a delay check, and if the check is met, then only we check if the condition is true
          // NEW - this is the "not" path so if the delay is met we will consider this success
          val isOpen = isOpenedWithin(
            prospectObject = prospectObject,
            lastSentSteps = lastSentSteps,
            delay = stepData.delay
          )
          val notOpen = (delayMet && 
            (!isOpen ||
            (latestEmailStepFromTaskTable.isDefined && latestEmailStepFromTaskTable.get.task_status == TaskStatusType.Skipped) ||
            (latestEmailStepFromEmailScheduledTable.isEmpty && latestEmailStepFromTaskTable.isDefined && latestEmailStepFromTaskTable.get.task_status == TaskStatusType.Done)
          ))
          if (notOpen) {
            Right(true)
          } else Left(RejectionReasonForCampaignProspectStepSchedule
            .StepDelayNotMet(DateTime.now().plusMinutes(60)))


        case CampaignStepCondition.Opened =>
          //the way opened and not opened works will change here
          // OLD - there is a delay check, and if the check is met, then only we check if the condition is true
          // NEW - this is the "yes" path so if the delay is NOT met we will check if the condition is met, if not we send error
          // if the delay is met it is always false
          /**
           *
           *
           * OPEN WITHIN 3 days
           * /           \
           * yes         No
           * /               \
           * send email       call
           *
           *
           *
           * case 1 ----  So if the email is opened within 3 days the prospect will go to YES path
           * case 2 ----  Email is not opened in three days it will go to NO path
           * case 3 ---- Email is opened after the three days mark it will go in NO path
           */
          val isOpen = isOpenedWithin(
            prospectObject = prospectObject,
            lastSentSteps = lastSentSteps,
            delay = stepData.delay
          )

          println(s"isOpen: $isOpen")

          if (isOpen) {
            Right(true)
          } else if (delayMet) {
            Right(false)
          } else {
            Left(RejectionReasonForCampaignProspectStepSchedule.StepDelayNotMet(DateTime.now().plusMinutes(60)))
          }

        case CampaignStepCondition.UntilOpened =>
          //the way opened and not opened works will change here
          // OLD - there is a delay check, and if the check is met, then only we check if the condition is true
          // NEW - this is the "UNTIL" path so we will not consider delay and only pass this when the

          val isOpen = isOpened(
            prospectObject = prospectObject,
            lastSentSteps = lastSentSteps
          )
          if (isOpen) {
            Right(true)
          } else {
            if (delayMet) {
              Left(RejectionReasonForCampaignProspectStepSchedule.StepIdNotFound(DateTime.now().plusHours(1)))
            } else {
              if (whenStepDelayWillMeet.isBefore(DateTime.now())) {
                Left(RejectionReasonForCampaignProspectStepSchedule.StepDelayNotMet(DateTime.now().plusMinutes(60)))

              } else
                Left(RejectionReasonForCampaignProspectStepSchedule.StepDelayNotMet(whenStepDelayWillMeet))

            }
          }

        case CampaignStepCondition.LinkedInMessageSent =>
          //Later - we should come here and put a SHOULD NEVER HAPPEN.
          // and add validation - if there is no message sent step
          // we will not allow to save the camapign
          // but still detect this at runtime and pause the camapign even and propagete this to the end user.
          if (delayMet) {
            val latestLinkedInStep = lastSentSteps.find(_.channel_type == LinkedinChannel)
            Right((prospectObject.linkedin_url.isDefined && prospectObject.linkedin_url.get.nonEmpty) &&
              latestLinkedInStep.nonEmpty &&
              latestLinkedInStep.get.failure_reason.isEmpty)
          } else Left(RejectionReasonForCampaignProspectStepSchedule.StepDelayNotMet(whenStepDelayWillMeet))

        case CampaignStepCondition.LinkedInMessageFailed =>
          if (delayMet) {
            val latestLinkedInStep = lastSentSteps.find(_.channel_type == LinkedinChannel)
            Right((prospectObject.linkedin_url.isDefined && prospectObject.linkedin_url.get.nonEmpty) &&
              latestLinkedInStep.nonEmpty &&
              latestLinkedInStep.get.failure_reason.isDefined)
          } else Left(RejectionReasonForCampaignProspectStepSchedule.StepDelayNotMet(whenStepDelayWillMeet))

        case CampaignStepCondition.LinkedinProfileNotConnected =>
          //Later - we should come here and put a SHOULD NEVER HAPPEN.
          // and add validation - if there is no message sent step
          // we will not allow to save the camapign
          // but still detect this at runtime and pause the camapign even and propagete this to the end user.
          if (delayMet) {
            val latestLinkedInStep = lastSentSteps.find(_.channel_type == LinkedinChannel)
            Right((prospectObject.linkedin_url.isDefined && prospectObject.linkedin_url.get.nonEmpty) &&
              !isProspectConnectedToLinkedin)
          } else Left(RejectionReasonForCampaignProspectStepSchedule.StepDelayNotMet(whenStepDelayWillMeet))

        case CampaignStepCondition.LinkedInProfileConnected =>
          if (delayMet) {
            val latestLinkedInStep = lastSentSteps.find(_.channel_type == LinkedinChannel)
            Right((prospectObject.linkedin_url.isDefined && prospectObject.linkedin_url.get.nonEmpty) &&
              isProspectConnectedToLinkedin)
          } else Left(RejectionReasonForCampaignProspectStepSchedule.StepDelayNotMet(whenStepDelayWillMeet))

        case CampaignStepCondition.CallPicked =>
          //Later - we should come here and put a SHOULD NEVER HAPPEN.
          // and add validation - if there is no message sent step
          // we will not allow to save the camapign
          // but still detect this at runtime and pause the camapign even and propagete this to the end user.
          if (delayMet) {
            val latestCallStep = lastSentSteps.find(_.channel_type == CallChannel)
            Right(
              (prospectObject.phone.isDefined && prospectObject.phone.get.nonEmpty) &&
                latestCallStep.nonEmpty &&
                (
                  (
                    latestCallStep.get.call_status.isDefined &&
                      !(latestCallStep.get.call_status.get == ParticipantCallStatus.NO_ANSWER)
                    ) &&
                    (
                      latestCallStep.get.reply_sentiment.isDefined &&
                        latestCallStep.get.reply_sentiment.get == ReplySentimentSubCategory.CallNegativeConnectedHungUpRefusedToTalk.key
                      )
                  )
            )
          } else Left(RejectionReasonForCampaignProspectStepSchedule.StepDelayNotMet(whenStepDelayWillMeet))

        case CampaignStepCondition.CallNotPicked =>
          if (delayMet) {
            val latestCallStep = lastSentSteps.find(_.channel_type == CallChannel)
            Right(
              (prospectObject.phone.isDefined && prospectObject.phone.get.nonEmpty) &&
                lastSentSteps.nonEmpty &&
                (
                  (
                    latestCallStep.get.call_status.isDefined &&
                      (
                        latestCallStep.get.call_status.get == ParticipantCallStatus.NO_ANSWER ||
                          latestCallStep.get.task_status == TaskStatusType.Skipped
                        )
                    ) ||
                    (
                      latestCallStep.get.reply_sentiment.isDefined &&
                        latestCallStep.get.reply_sentiment.get == ReplySentimentSubCategory.CallNegativeConnectedHungUpRefusedToTalk.key
                      )
                  )
            )
          } else Left(RejectionReasonForCampaignProspectStepSchedule.StepDelayNotMet(whenStepDelayWillMeet))

        case data: CampaignStepCondition.HasReplied =>

          /**
           * we have added a sealed trait HasReplied with case objects
           *
           * UntilHasReplied --------- delay doesn't matter, we will go to next step only when there is a reply (any type)
           * HasRepliedWithin -------- if we are getting a reply (any type) and delay is NOT met we consider it or else we don't
           * HasRepliedInterested ---- if the delay is met and the reply is interested then we will go the YES path or else its NO
           * HasRepliedDoNotContact -- if the delay is met and the reply is DNC then we will go the YES path or else its NO
           */
          val replied = hasEmail &&
            latestEmailStepFromEmailScheduledTable.nonEmpty &&
            (latestEmailStepFromEmailScheduledTable.get.replied.isDefined && latestEmailStepFromEmailScheduledTable.get.replied.get)
          data match {
            case CampaignStepCondition.UntilHasReplied =>
              //UntilHasReplied --------- delay doesn't matter, we will go to next step only when there is a reply (any type)

              if (replied) {
                Right(true)
              } else {
                if (delayMet) {
                  Left(RejectionReasonForCampaignProspectStepSchedule.StepIdNotFound(DateTime.now().plusHours(1)))
                } else {
                  if (whenStepDelayWillMeet.isBefore(DateTime.now())) {
                    Left(RejectionReasonForCampaignProspectStepSchedule.StepDelayNotMet(DateTime.now().plusMinutes(60)))

                  } else
                    Left(RejectionReasonForCampaignProspectStepSchedule.StepDelayNotMet(whenStepDelayWillMeet))
                }
              }

            case CampaignStepCondition.HasRepliedWithin =>
              //HasRepliedWithin -------- if we are getting a reply (any type) and delay is NOT met we consider it or else we don't

              val repliedWithin = replied &&
                latestEmailStepFromEmailScheduledTable.get.replied_at.isDefined &&
                latestEmailStepFromEmailScheduledTable.get.replied_at.get.isBefore(whenStepDelayWillMeet)
              if (delayMet && !repliedWithin) {
                Right(false)
              } else {
                if (repliedWithin) {
                  Right(true)
                } else {
                  Left(RejectionReasonForCampaignProspectStepSchedule.StepDelayNotMet(whenStepDelayWillMeet))
                }
              }

            case CampaignStepCondition.HasRepliedInterested =>
              //HasRepliedInterested ---- if the delay is met and the reply is interested then we will go the YES path or else its NO

              if (delayMet) {
                val repliedInterested = replied &&
                  latestEmailStepFromEmailScheduledTable.get.reply_type.isDefined &&
                  (latestEmailStepFromEmailScheduledTable.get.reply_type.get == EmailReplyType.INTERESTED)
                Right(repliedInterested) //terminating case for the prospect

              } else {
                Left(RejectionReasonForCampaignProspectStepSchedule.StepDelayNotMet(whenStepDelayWillMeet))
              }

            case CampaignStepCondition.HasRepliedDoNotContact =>
              //HasRepliedDoNotContact -- if the delay is met and the reply is DNC then we will go the YES path or else its NO

              if (delayMet) {
                val repliedDoNotContact = replied &&
                  latestEmailStepFromEmailScheduledTable.get.reply_type.isDefined &&
                  (latestEmailStepFromEmailScheduledTable.get.reply_type.get == EmailReplyType.DO_NOT_CONTACT)

                Right(repliedDoNotContact)

              } else {
                Left(RejectionReasonForCampaignProspectStepSchedule.StepDelayNotMet(whenStepDelayWillMeet))
              }
          }

        case data: CampaignStepCondition.HasNotReplied =>

          /**
           * we have added a sealed trait HasReplied with case objects
           * So how the opposite for all will differ
           *
           * HasNotReplied for UntilHasReplied --------- this SHOULD NEVER get called since the opposite of until is Impossible case as in we have only one path forward and dont have yes or no path for this case
           * HasNotReplied for HasRepliedWithin -------- if delay is met we will send true here since we should be going to no path
           * HasNotReplied for HasRepliedInterested ---- if the delay is met and the reply is NOT interested then we will go the NO path or else its YES
           * HasNotReplied for HasRepliedDoNotContact -- if the delay is met and the reply is NOT DNC then we will go the NO path or else its YES
           */
          val replied = latestEmailStepFromEmailScheduledTable.nonEmpty &&
            (latestEmailStepFromEmailScheduledTable.get.replied.isDefined && latestEmailStepFromEmailScheduledTable.get.replied.get)
          data.opposite match {
            case CampaignStepCondition.UntilHasReplied =>
              //HasNotReplied for UntilHasReplied --------- this SHOULD NEVER get called since the opposite of until is Impossible case as in we have only one path forward and dont have yes or no path for this case
              Right(false)
            case CampaignStepCondition.HasRepliedWithin =>
              //HasNotReplied for HasRepliedWithin -------- if delay is met we will send true here since we should be going to no path

              if (delayMet) {
                Right(true)
              } else Left(RejectionReasonForCampaignProspectStepSchedule.StepIdNotFound(DateTime.now().plusMinutes(15)))

            case CampaignStepCondition.HasRepliedInterested =>
              //HasNotReplied for HasRepliedInterested ---- if the delay is met and the reply is NOT interested then we will go the NO path or else its YES
              if (delayMet) {
                val repliedInterested = hasEmail &&
                  replied &&
                  latestEmailStepFromEmailScheduledTable.get.reply_type.isDefined &&
                  latestEmailStepFromEmailScheduledTable.get.reply_type.get == EmailReplyType.INTERESTED
                Right(!repliedInterested)

              } else {
                Left(RejectionReasonForCampaignProspectStepSchedule.StepDelayNotMet(whenStepDelayWillMeet))
              }
            case CampaignStepCondition.HasRepliedDoNotContact =>
              //HasNotReplied for HasRepliedDoNotContact -- if the delay is met and the reply is NOT DNC then we will go the NO path or else its YES

              if (delayMet) {
                val repliedDoNotContact = hasEmail &&
                  replied &&
                  latestEmailStepFromEmailScheduledTable.get.reply_type.isDefined &&
                  (latestEmailStepFromEmailScheduledTable.get.reply_type.get == EmailReplyType.DO_NOT_CONTACT)

                Right(!repliedDoNotContact)

              } else {
                Left(RejectionReasonForCampaignProspectStepSchedule.StepDelayNotMet(whenStepDelayWillMeet))
              }
          }


      }
    } else Left(RejectionReasonForCampaignProspectStepSchedule.StepDelayNotMet(lastSentSteps.head.sent_at.plusHours(12)))
  }

  /**
   *
   * has_email
   * | yes
   * has_phone
   * | yes
   * has_linkedin
   * | yes
   * send email
   *
   * in this case
   * if the prospect has email, and linkedIn but no phone the email should not go
   * that is even if one condition fails the entire check should fail
   */
  def matchMultipleConditionsWithProspect(
                                           prospectObject: ProspectObject,
                                           conditions: List[CampaignStepCondition],
                                           lastSentSteps: List[LastSentStepData],
                                           isProspectConnectedToLinkedin: Boolean,
                                           stepData: CampaignStepWithChildren
                                         )(using logger: SRLogger): Either[RejectionReasonForCampaignProspectStepSchedule, Boolean] = {
    var error: Option[RejectionReasonForCampaignProspectStepSchedule] = None

    // if the condition set consists of Replied or open within we will not consider delay
    val considerDelay = !conditions.exists(a => a == CampaignStepCondition.HasRepliedWithin || a == CampaignStepCondition.Opened)
    val checkIfSomeConditionFailed = conditions.exists { condition =>
      //here we are checking if one of the conditions in the flow are failing
      // so if there "exists" a condition that is failing we need to stop the check and send a fail
      isTheConditionMatchWithProspect(
        prospectObject = prospectObject,
        condition = condition,
        lastSentSteps = lastSentSteps,
        stepData = stepData,
        isProspectConnectedToLinkedin = isProspectConnectedToLinkedin,
        consider_delay = considerDelay
      ) match {
        case Left(value) =>
          error = Some(value)
          true //sending true since the condition failed

        case Right(value) =>
          //if the condition passes we need to send false and move forward to next one
          //if the condition fails we need to send true since we found a failing condition
          !value
      }
    }

    val matched = if (checkIfSomeConditionFailed) {
      error match {
        case Some(value) => Left(value)
        case None => Right(false)
      }
    } else {
      Right(true)
    }

    if(List(163060, 164218, 163847).contains(stepData.campaign_id)){
      logger.doNotTruncate(s"[drip_debug_Balaji] prospectId => ${prospectObject.id} :: conditions => ${conditions} :: matched => $matched")
    }
    // println(s"prospectId => ${prospectObject.id} :: conditions => ${conditions} :: matched => $matched")
    matched
  }

  private def getNextStepHelperFunction(
                                         orderedStepIds: Vector[Long],
                                         currentStepId: Option[Long]
                                       ): Option[Long] = {

    currentStepId match {

      case None => orderedStepIds.headOption

      case Some(stepId) =>

        val currentStepIndex = orderedStepIds.indexOf(stepId)

        if ((orderedStepIds.length - 1) == currentStepIndex) {

          None

        } else {

          val nextStepIndex = currentStepIndex + 1

          val nextStepId = orderedStepIds(nextStepIndex)

          Some(nextStepId)
        }
    }
  }

  def getNextStepId(
                     currentStepId: Option[Long],
                     prospectObjectOpt: Option[ProspectObject],
                     campaignStepsStructure: CampaignStepsStructure,
                     lastSentSteps: List[LastSentStepData],
                     isProspectConnectedToLinkedin: Boolean,
                     stepsMappedById: Map[Long, CampaignStepWithChildren]
                   )(implicit ec: ExecutionContext, Logger: SRLogger): Either[RejectionReasonForCampaignProspectStepSchedule, Long] = {

    campaignStepsStructure match {

      case data: CampaignStepsStructure.EmailCampaignStepsStructure =>

        getNextStepHelperFunction(
          orderedStepIds = data.orderedStepIds,
          currentStepId = currentStepId
        ) match {
          case Some(value) => Right(value)
          case None => Left(RejectionReasonForCampaignProspectStepSchedule.LastStep(next_check_for_scheduling_at = DateTime.now()))
        }


      case data: CampaignStepsStructure.MultichannelCampaignStepsStructure =>

        getNextStepHelperFunction(
          orderedStepIds = data.orderedStepIds,
          currentStepId = currentStepId
        ) match {
          case Some(value) => Right(value)
          case None => Left(RejectionReasonForCampaignProspectStepSchedule.LastStep(next_check_for_scheduling_at = DateTime.now()))
        }


      case data: CampaignStepsStructure.DripCampaignStepsStructure =>

        getNextStepForDrip(
          currentStepId = currentStepId,
          prospectObjectOpt = prospectObjectOpt,
          lastSentSteps = lastSentSteps,
          isProspectConnectedToLinkedin = isProspectConnectedToLinkedin,
          stepsMappedById = stepsMappedById,
          data = data,
        )

    }


  }

  def getNextStepForDrip(
    currentStepId: Option[Long],
    prospectObjectOpt: Option[ProspectObject],
    lastSentSteps: List[LastSentStepData],
    isProspectConnectedToLinkedin: Boolean,
    data: CampaignStepsStructure.DripCampaignStepsStructure,
    stepsMappedById: Map[Long, CampaignStepWithChildren],
  )(
    implicit ec: ExecutionContext,
    Logger: SRLogger
  ): Either[RejectionReasonForCampaignProspectStepSchedule, Long] = {

    Logger.info(s"getNextStepId drip CampaignStepsStructure.DripCampaignStepsStructure BEGIN")

    currentStepId match {
      case Some(stepId) =>
        val childStepIdFut = NextStepFinderForDrip.findNextStep(
          edges = data.edges,
          nodes = data.nodes,
          parent_step_id = stepId
        )

        Await.ready(childStepIdFut, 60000.millis).value match {

          case None =>

            Logger.error(
              msg = s"getNextStepForDrip - Failed findNextStep. edges: ${data.edges} :: nodes: ${data.nodes} :: head_node_id: ${data.head_node_id}"
            )

            Left(
              RejectionReasonForCampaignProspectStepSchedule.FailedToParseOutput(DateTime.now().plusMinutes(5))
            )

          case Some(Failure(exception)) =>

            Logger.error(
              msg = s"getNextStepForDrip - Failed findNextStep. edges: ${data.edges} :: nodes: ${data.nodes} :: head_node_id: ${data.head_node_id}",
              err = exception,
            )

            Left(
              RejectionReasonForCampaignProspectStepSchedule.FailedToParseOutput(DateTime.now().plusMinutes(5))
            )

          case Some(Success(childStepIdList)) =>

            Logger.info(s"getNextStepId drip childStepIdList : ${childStepIdList} ")

            prospectObjectOpt match {
              case None =>
                Logger.shouldNeverHappen(s"got empty prospect for getNextStepId drip, campaignStepsStructure --- $data")
                Left(RejectionReasonForCampaignProspectStepSchedule.NoProspectFound(DateTime.now()))
              case Some(prospectObject) =>

                var selectedStepId: Option[Long] = None
                var error: Option[RejectionReasonForCampaignProspectStepSchedule] = None
                childStepIdList.find(
                  sc => {

                    println(s"getNextStepId drip  sc: $sc")

                    val stepData = stepsMappedById(sc.child_step_id)
                    matchMultipleConditionsWithProspect(
                      prospectObject = prospectObject,
                      isProspectConnectedToLinkedin = isProspectConnectedToLinkedin,
                      conditions = sc.condition,
                      lastSentSteps = lastSentSteps,
                      stepData = stepData
                    ) match {
                      case Left(value) =>
                        error = Some(value)
                      case Right(value) =>
                                              println(s"isMatching $value")
                        if (value) {
                          println(s"getNextStepId drip  selectedStepId - 1: $sc")
                          selectedStepId = Some(sc.child_step_id)
                        }
                    }
                    selectedStepId.isDefined
                  }
                )
                selectedStepId match {
                  case Some(value) => {
                    Logger.info(s"getNextStepId drip  selectedStepId - 1: $value")
                    Right(value)
                  }
                  case None => {
                    Logger.error(s"getNextStepId drip  selectedStepId is None  - 1: RejectionReasonForCampaignProspectStepSchedule.StepIdNotFound")
                    Left(error.getOrElse(RejectionReasonForCampaignProspectStepSchedule.StepIdNotFound(next_check_for_scheduling_at = DateTime.now().plusMinutes(15))))
                  }
                }

            }


        }

      case None =>
        val headStepsWithConditionFut: Future[List[ChildStepIdWithCondition]] = NextStepFinderForDrip.getHeadStepsWithCondition(
          edges = data.edges,
          nodes = data.nodes,
          head_node_id = data.head_node_id,
          //              pid = 0,
          //              cid = stepsMappedById.head._2.campaign_id,
          //              from = "ChannelSchedulerService.getNextStepId"
        )

        Await.ready(headStepsWithConditionFut, 60000.millis).value match {

          case None =>

            Logger.error(
              msg = s"getNextStepForDrip - Failed getHeadStepsWithCondition. edges: ${data.edges} :: nodes: ${data.nodes} :: head_node_id: ${data.head_node_id}"
            )

            Left(
              RejectionReasonForCampaignProspectStepSchedule.FailedToParseOutput(DateTime.now().plusMinutes(5))
            )

          case Some(Failure(exception)) =>

            Logger.error(
              msg = s"getNextStepForDrip - Failed getHeadStepsWithCondition. edges: ${data.edges} :: nodes: ${data.nodes} :: head_node_id: ${data.head_node_id}",
              err = exception,
            )

            Left(
              RejectionReasonForCampaignProspectStepSchedule.FailedToParseOutput(DateTime.now().plusMinutes(5))
            )

          case Some(Success(stepsList)) =>

            Logger.info(s"getNextStepId drip stepsList : ${stepsList} ")
            prospectObjectOpt match {
              case None => Left(RejectionReasonForCampaignProspectStepSchedule.NoProspectFound(DateTime.now()))
              case Some(prospectObject) =>
                var selectedStepId: Option[Long] = None
                var error: Option[RejectionReasonForCampaignProspectStepSchedule] = None

                stepsList.find(
                  sc => {
                    val stepData = stepsMappedById(sc.child_step_id)
                    matchMultipleConditionsWithProspect(
                      prospectObject = prospectObject,
                      conditions = sc.condition,
                      lastSentSteps = lastSentSteps,
                      isProspectConnectedToLinkedin = isProspectConnectedToLinkedin,
                      stepData = stepData
                    ) match {
                      case Left(value) => error = Some(value)
                      case Right(value) =>
                        if (value) {
                          selectedStepId = Some(sc.child_step_id)
                        }
                    }
                    selectedStepId.isDefined

                  }
                )
                selectedStepId match {
                  case Some(value) => {
                    Logger.info(s"getNextStepId drip  selectedStepId - 2: $value")
                    Right(value)
                  }
                  case None => {
                    Logger.error(s"getNextStepId drip  selectedStepId is None - 2: RejectionReasonForCampaignProspectStepSchedule.StepIdNotFound")
                    Left(error.getOrElse(RejectionReasonForCampaignProspectStepSchedule.StepIdNotFound(next_check_for_scheduling_at = DateTime.now().plusMinutes(15))))
                  }

                }
            }

        }

    }
  }

  /**
   * Used for follow ups
   *
   * Send follow up if next-step's delay time has passed since current-step was last scheduled,
   * for the campaign-prospect pair
   *
   * Returns Map of Step Id and DateTime when it should have been scheduled, so that we can
   * send the next step / follow up today / now
   *
   * FOR IMPLEMENTING PROSPECT LEVEL TIMEZONES LATER ON USE THIS: http://stackoverflow.com/a/23300611
   *
   * Earlier name of this method: getMapOfStepIdAndRequiredDelay
   */
  def getMapOfStepIdAndRequiredDelay(
                                      headStepId: Long,
                                      orderedStepIds: Vector[Long],
                                      stepsMappedById: Map[Long, CampaignStepWithChildren]

                                    )(implicit ec: ExecutionContext, Logger: SRLogger): Vector[SchedulerMapStepIdAndDelay] = {

    var vectorOfStepIdAndMaxLastScheduledTime: Vector[SchedulerMapStepIdAndDelay] = Vector()

    orderedStepIds.foreach { case currentStepId => {

      getNextStepId(
        currentStepId = Some(currentStepId),
        prospectObjectOpt = None,
        campaignStepsStructure = CampaignStepsStructure.MultichannelCampaignStepsStructure(
          orderedStepIds = orderedStepIds
        ),
        isProspectConnectedToLinkedin = false,
        lastSentSteps = List(),
        stepsMappedById = stepsMappedById
      ) match {

        case Right(nextStepId) =>

          val currentStep = stepsMappedById(currentStepId)

          val nextStep = stepsMappedById(nextStepId)

          val nextStepDelay = nextStep.delay

          vectorOfStepIdAndMaxLastScheduledTime = vectorOfStepIdAndMaxLastScheduledTime :+ SchedulerMapStepIdAndDelay(
            is_head_step_in_the_campaign = currentStepId == headStepId,
            currentStepType = currentStep.step_type,
            nextStepType = nextStep.step_type,
            currentStepId = currentStepId,
            delayTillNextStep = nextStepDelay
          )

        case Left(value) =>

          // this is the last step
          // last step should be ignored as there is no further email to be sent after this

          None
      }
    }
    }

    //    stepsMappedById.foreach { case (id, currentStep) =>
    //
    //      getNextStepId(Some(id), orderedStepIds) match {
    //
    //        case Some(nextStepId) =>
    //
    //          val nextStep = stepsMappedById(nextStepId)
    //
    //          val nextStepDelay = nextStep.delay
    //
    //          //        val lastStepShouldHaveBeenScheduledBefore = DateTime.now.minusSeconds(nextStepDelay)
    //
    //          vectorOfStepIdAndMaxLastScheduledTime = vectorOfStepIdAndMaxLastScheduledTime :+ SchedulerMapStepIdAndDelay(
    //            is_head_step_in_the_campaign = id == headStepId,
    //            currentStepType = currentStep.step_type,
    //            nextStepType = nextStep.step_type,
    //            currentStepId = id,
    //            delayTillNextStep = nextStepDelay
    //          )
    //
    //        case None =>
    //
    //          // this is the last step
    //          // last step should be ignored as there is no further email to be sent after this
    //
    //          None
    //
    //      }
    //
    //    }

    vectorOfStepIdAndMaxLastScheduledTime

  }

  // FIXME DRIP: Probably this will be removed
  // This is to create a case class of every step with their next_step_type info. We are trying to eliminate this in Drip with the new flow. Once the filtering prospects change in done, I will try to eliminate this change
  def getMapOfStepIdAndRequiredDelayForDripCampaign(
                                                     headStepId: String,
                                                     edges: List[JsValue],
                                                     nodes: List[JsValue],
                                                     stepsMappedById: Map[Long, CampaignStepWithChildren],
                                                     //                                                     from: String
                                                   )(implicit ec: ExecutionContext, Logger: SRLogger): Vector[SchedulerMapStepIdAndDelay] = {

    var vectorOfStepIdAndMaxLastScheduledTime: Vector[SchedulerMapStepIdAndDelay] = Vector()

    var queue = List(headStepId)
    while (queue.nonEmpty) {
      val currentStepIdPopped = queue.head
      queue = queue.drop(1)

      val isHeadStep = currentStepIdPopped == headStepId

      val res = if (isHeadStep) {
        NextStepFinderForDrip.getHeadStepsWithCondition(
          edges = edges,
          nodes = nodes,
          head_node_id = currentStepIdPopped,
          //          pid = 0,
          //          cid = stepsMappedById.head._2.campaign_id,
          //          from = s"${from}.ChannelSchedulerService.getMapOfStepIdAndRequiredDelayForDripCampaign"
        )
      }
      else {
        NextStepFinderForDrip.findNextStep(
          edges = edges,
          nodes = nodes,
          parent_step_id = currentStepIdPopped.toLong
        )
      }

      res
        .map(stepAndConditionList => {
          queue = queue ++ stepAndConditionList
            .filter(_.child_step_id.toString != currentStepIdPopped)
            .map(_.child_step_id.toString)
          //          println(s"currentStepIdPopped - $currentStepIdPopped :: new to queue -- ${stepAndConditionList.map(_.child_step_id.toString)}")

          stepAndConditionList.map(sc => {
            Try {
              currentStepIdPopped.toLong
            } match {
              case Failure(exception) =>
              // This is a condition node. Do Nothing

              case Success(currentStepId) =>
                val currentStep = stepsMappedById(currentStepId)

                val nextStep = stepsMappedById(sc.child_step_id)

                val nextStepDelay = nextStep.delay

                vectorOfStepIdAndMaxLastScheduledTime = vectorOfStepIdAndMaxLastScheduledTime :+ SchedulerMapStepIdAndDelay(
                  is_head_step_in_the_campaign = isHeadStep,
                  currentStepType = currentStep.step_type,
                  nextStepType = nextStep.step_type,
                  currentStepId = currentStepId,
                  delayTillNextStep = nextStepDelay
                )
            }

          })
        })

      Await.ready(res, 60000.millis).value match {

        case None =>

          Logger.error(
            msg = s"getMapOfStepIdAndRequiredDelayForDripCampaign - Failed res. edges: ${edges} :: nodes: ${nodes} :: currentStepIdPopped: $currentStepIdPopped"
          )

        case Some(Failure(exception)) =>

          Logger.error(
            msg = s"getMapOfStepIdAndRequiredDelayForDripCampaign - Failed res. edges: ${edges} :: nodes: ${nodes} :: currentStepIdPopped: $currentStepIdPopped",
            err = exception,
          )

        case Some(Success(childStepIdList)) =>

          ()

        }

    }

    vectorOfStepIdAndMaxLastScheduledTime

  }

  def filterCampaignStepsRelevantToChannel(
                                            allCampaignSteps: Vector[SchedulerMapStepIdAndDelay],
                                            channelType: ChannelType,
                                          ): Vector[SchedulerMapStepIdAndDelay] = {
    // val tot_steps = allCampaignSteps.length
    allCampaignSteps
      .filter(aCampaignStep => {
        aCampaignStep.nextStepType.channelType == channelType
        // suspected bug here why are we filtering based on next step channelType
      })
  }


}

