package utils.mq.channel_scheduler

import api.AppConfig
import org.apache.pekko.actor.ActorSystem
import org.joda.time.{DateTime, Seconds}
import play.api.libs.ws.WSClient
import sr_scheduler.models.{ChannelData, ChannelType}
import utils.{Help<PERSON>, SRLogger, StringUtils}
import utils.mq.channel_scheduler.channels.{ChannelSchedulerTrait, EmailChannelScheduler}
import api.accounts.service.AccountOrgBillingRelatedService
import api.accounts.{AccountService, TeamId}
import api.calendar_app.CalendarAppService
import api.campaigns.models.{InternalSchedulerRunLog, InternalSchedulerRunLogData}
import utils.email.EmailServiceCompanion
import api.campaigns.services.{CampaignProspectService, CampaignService, CampaignsMissingMergeTagService}
import utils.templating.TemplateService
import utils.mq.webhook.MQWebhookCompleted
import api.campaigns.{CampaignEditedPreviewEmailDAO, CampaignProspectDAO, CampaignStepDAO, CampaignStepVariantDAO}
import api.emails.CampaignProspectStepScheduleLogsDAO
import api.tasks.pgDao.TaskPgDAO
import api.tasks.services.TaskService
import io.smartreach.esp.api.emails.EmailSettingId
import utils.cache_utils.service.SrRedisSimpleLockServiceV2
import utils.email_notification.service.EmailNotificationService
import utils.shuffle.SrShuffleUtils
import utils.helpers.LogHelpers
import utils.mq.channel_scheduler.channels.model.SchedulerSteps
import utils.mq.services.{MQConfig, SimpleMqServiceTrait}
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService

import scala.util.{Failure, Success, Try}
import scala.concurrent.{ExecutionContext, Future}

case class MqEmailChannelSchedulerMsg(teamId: Long, emailSettingId: Long)

class MqEmailChannelScheduler(
                               emailChannelScheduler: EmailChannelScheduler,
                               accountService: AccountService,
                               //accountDAO: AccountDAO,
                               emailNotificationService: EmailNotificationService,
                               campaignService: CampaignService,
                               campaignProspectDAO: CampaignProspectDAO,
                               campaignStepVariantDAO: CampaignStepVariantDAO,
                               campaignStepDAO: CampaignStepDAO,
                               srShuffleUtils: SrShuffleUtils,
                               emailServiceCompanion: EmailServiceCompanion,
                               templateService: TemplateService,
                               taskService: TaskService,
                               taskDAO: TaskPgDAO,
                               campaignProspectService: CampaignProspectService,
                               campaignEditedPreviewEmailDAO: CampaignEditedPreviewEmailDAO,
                               campaignsMissingMergeTagService: CampaignsMissingMergeTagService,
                               srRedisSimpleLockServiceV2: SrRedisSimpleLockServiceV2,
                               mqWebhookCompleted: MQWebhookCompleted,
  srRollingUpdateCoreService: SrRollingUpdateCoreService,
                               accountOrgBillingRelatedService: AccountOrgBillingRelatedService,
                               calendarAppService: CalendarAppService,
                               campaignProspectStepScheduleLogsDAO: CampaignProspectStepScheduleLogsDAO
) extends SimpleMqServiceTrait[MqEmailChannelSchedulerMsg] {

  val queueBaseName: String = MQConfig.emailChannelSchedulerQueueBaseName
  val prefetchCount: Int = MQConfig.emailChannelSchedulerPrefetchCount
  override val logIfQueueSizeAbove = 1500

  // PROSPECTS_EMAILS_TODO_UPDATE / PROSPECTS_EMAILS_TODO_READ_CLEANED
  /*
   * Use Case: msg received from queue contains emailSetting id - schedule emails to be sent from emailSettingId
   */
  def processMessage(msg: MqEmailChannelSchedulerMsg)(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem): Future[Any] = {

    val startTime = DateTime.now()

    val logBase = s"[MqEmailChannelScheduler] processMessage eset_${msg.emailSettingId} :: "

    given Logger: SRLogger = new SRLogger(logRequestId = logBase)

    Logger.info(s"processMessage:: ${msg}: started at $startTime")

    val channelData = ChannelData.EmailChannelData(
      emailSettingId = msg.emailSettingId,
    )

    val channelSchedulerService: ChannelSchedulerTrait = emailChannelScheduler // hardcoding to emailChannelScheduler

    channelSchedulerService.scheduleTasksForChannel(
      channelData = channelData,
      teamId = msg.teamId,
      accountService = accountService,
      //accountDAO = accountDAO,
      emailNotificationService = emailNotificationService,
      campaignService = campaignService,
      campaignProspectDAO = campaignProspectDAO,
      campaignStepVariantDAO = campaignStepVariantDAO,
      campaignStepDAO = campaignStepDAO,
      srShuffleUtils = srShuffleUtils,
      emailServiceCompanion = emailServiceCompanion,
      templateService = templateService,
      taskDAO = taskDAO,
      taskService = taskService,
      campaignProspectService = campaignProspectService,
      campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO,
      campaignsMissingMergeTagService = campaignsMissingMergeTagService,
      srRedisSimpleLockServiceV2 = srRedisSimpleLockServiceV2,
      mqWebhookCompleted = mqWebhookCompleted,
        accountOrgBillingRelatedService = accountOrgBillingRelatedService,
      srRollingUpdateCoreService = srRollingUpdateCoreService,
      calendarAppService = calendarAppService
    )
      .map { res =>

        val endTime = DateTime.now()
        val timeTaken = Seconds.secondsBetween(startTime, endTime).getSeconds
        val timeTakeMillis = endTime.getMillis - startTime.getMillis

        Logger.info(s"scheduled:: ${msg}: exit_step: ${res.reached_scheduler_step} : (timeTakeMillis: $timeTakeMillis) :: took from $startTime to $endTime (timetaken: $timeTaken) :: $res")

        campaignService.addInternalSchedulerRunLog(
          internalSchedulerRunLog = InternalSchedulerRunLog(
            team_id = TeamId(msg.teamId),
            internal_scheduler_run_log_data = InternalSchedulerRunLogData.EmailChannelSchedulerLog(
              email_setting_id = EmailSettingId(msg.emailSettingId)
            ),
            started_at = startTime,
            completed_at = endTime,
            saved_tasks_count = res.saved_tasks_count,
            reached_scheduler_step = res.reached_scheduler_step,
            log_trace_id = Logger.logTraceId
          )
        ) match {
          case Success(_) => //DO NOTHING
          case Failure(exception) => Logger.fatal(s"Failed to addInternalSchedulerRunLog", exception)
        }
        res.saved_tasks_count

      }
      .recover { case e =>

        val endTime = DateTime.now()
        val timeTaken = Seconds.secondsBetween(startTime, endTime).getSeconds
        val timeTakeMillis = endTime.getMillis - startTime.getMillis

        Logger.error(s"failed:: ${msg}: (timeTakeMillis: $timeTakeMillis) :: took from $startTime to $endTime (timetaken: $timeTaken) :: ${LogHelpers.getStackTraceAsString(e)}")

        0
      }


  }

}