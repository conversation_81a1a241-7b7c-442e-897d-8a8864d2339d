package utils.mq.do_not_contact

import api.APIErrorResponse.ErrorResponseCreateUpdateBlacklistApi
import api.ServerErrorException
import api.blacklist.*
import org.apache.pekko.actor.ActorSystem
import play.api.Logging
import play.api.libs.ws.WSClient
import utils.mq.services.{MQConfig, MQPublisherService, MQService, SimpleMQConsumerService}
import utils.{<PERSON><PERSON>, SRLogger, StringUtils}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

case class MQDoNotContactMessage(
                                  prospect_emails: Seq[String],


                                  /**
      * 24th July 2021: we were getting a deserialization error with
      * prospect_domains_with_excluded_emails: Seq[DomainsWithExcludedEmails]
      *
      * Error was:
      * "[MQService] [FATAL] handleDelivery SerializationUtils.deserialize ERROR: queueName coldemailLocal.doNotContact,
      * deliveryTag: 1 :: java.lang.ClassCastException: cannot assign instance of
      * scala.collection.immutable.List$SerializationProxy to field
      * utils.mq.MQDoNotContactMessage.prospect_domains_with_excluded_emails of
      * type scala.collection.Seq in instance of utils.mq.MQDoNotContactMessage"
      *
      * As a fix, we are instead passing this as type to the queue:
      * prospect_domains_with_excluded_emails: Seq[(String, Seq[String])]
      *
      * where the tuple (String, Seq[String]) represents (domain, excluded_emails)
      *
      */

    prospect_domains_with_excluded_emails: Seq[(String, Seq[String])],
    is_req_via_dnc_form: Boolean, //is_req_via_dnc_form is true when request comes from front-end
    account_id: Long,
    account_name: String,
    team_id: Long,
    ta_id: Long,
    campaign_id: Option[Long],
    campaign_name: Option[String]
)

class MQDoNotContactConsumer(
  blacklistService: BlacklistService,
  mqDoNotContactPublisher: MQDoNotContactPublisher
) extends SimpleMQConsumerService[MQDoNotContactMessage]
  with Logging {

  val queueBaseName: String = MQConfig.doNotContactQueueBaseName
  val prefetchCount: Int = MQConfig.doNotContactPrefetchCount
  val mqPublisherService: MQPublisherService[MQDoNotContactMessage] = mqDoNotContactPublisher

  def publish(msg: MQDoNotContactMessage) = {
    mqPublisherService.publishMsg(message = msg, queueBaseName = queueBaseName, prefetchCount = prefetchCount)
  }

  // PROSPECTS_EMAILS_TODO_READ_CLEANED
  def processMessage(msg: MQDoNotContactMessage)(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem): Future[Int] = {

    val logBase = s"( ${StringUtils.genLogTraceId} ) :: [MQDoNotContact] processMessage :: "

    given Logger: SRLogger= new SRLogger(logRequestId = logBase)

    Logger.info(s"""msg: tid_${msg.team_id} aid_${msg.account_id} prospect_emails: ${msg.prospect_emails.size} : prospect_domains_with_excluded_emails: ${msg.prospect_emails.size}  is_req_via_dnc_form: ${msg.is_req_via_dnc_form}""")

    // val prospect = Prospect.findByIds(ids = Seq(msg.prospect_id), teamId = msg.team_id).head

    // val account = Account.find(id = msg.account_id).get
    // val accountName = Helpers.getAccountName(account)

    if(msg.prospect_emails.nonEmpty && msg.prospect_domains_with_excluded_emails.nonEmpty) {
      Logger.shouldNeverHappen("Both emails and domains cannot be passed")
      Future(0)
    } else if (msg.prospect_emails.isEmpty && msg.prospect_domains_with_excluded_emails.isEmpty) Future(0) else {

      val data: BlacklistCreateForm = if (msg.prospect_emails.nonEmpty) {
        BlacklistCreateEmailsForm(
          emails = msg.prospect_emails.map(_.trim.toLowerCase).distinct
        )
      } else if (msg.prospect_domains_with_excluded_emails.nonEmpty) {
        BlacklistCreateDomainsForm(
          domains = msg.prospect_domains_with_excluded_emails
            .map(d => DomainsWithExcludedEmails(
              domain = d._1,
              excluded_emails = d._2.map(_.trim.toLowerCase).distinct
            ))
        )
      } else {
        //fixme: pass phone numbers to mq msg
        BlacklistCreatePhoneForm(Seq())
      }

        blacklistService.createOrUpdateBlacklist(

          accountId = msg.account_id,

          addedByName = if (msg.campaign_id.isDefined) s"unsubscribed from campaign ${msg.campaign_name.getOrElse("")}" else msg.account_name,
          teamId = msg.team_id,
          taId = msg.ta_id,

          data = data,

          is_req_via_dnc_form = msg.is_req_via_dnc_form,

          opted_out_from_campaign_id = msg.campaign_id,
          opted_out_from_campaign_name = msg.campaign_name,
          account = None,
          level = None,

          /**
           * FIXME AUDITLOGFIXNEEDED: since we are not creating request from MQ's, Som moving foreword with Option auditRequestLogId,
           * it should be Non Option auditRequestLogId, request should created on top of this as pass as Non Option param
           * */
          auditRequestLogId = None,
          Logger = Logger

        ) match {
        case Right(_) =>

          Logger.info(s"MQDoNotContact.processMessage :: success BlacklistService.createOrUpdateBlacklist")
          Future.successful(1)

        case Left(err) =>
          err match {

            case CreateOrUpdateBlacklistError.DBFailure(error: Throwable) =>
              Logger.fatal(s"MQDoNotContact.processMessage BlacklistService.createOrUpdateBlacklist CreateOrUpdateBlacklistError.DBFailure", error)

            case CreateOrUpdateBlacklistError.AccountIDNotFoundError(error: Throwable) =>
              Logger.shouldNeverHappen(s"MQDoNotContact.processMessage BlacklistService.createOrUpdateBlacklist CreateOrUpdateBlacklistError.AccountIDNotFoundError $error")

            case CreateOrUpdateBlacklistError.BadRequestErrors(errors: Seq[ErrorResponseCreateUpdateBlacklistApi]) =>
              Logger.fatal(s"MQDoNotContact.processMessage BlacklistService.createOrUpdateBlacklist CreateOrUpdateBlacklistError.BadRequestErrors errors::$errors")
          }
          Future.successful(0)
      }
    }

  }
}
