package utils.mq.email

import org.apache.pekko.actor.ActorSystem
import play.api.libs.ws.WSClient
import api.prospects.InboxV3Service
import api.prospects.dao.NewlyCreatedProspect
import api.prospects.service.AssociateProspectToEmailThreadService
import org.joda.time.{DateTime, Seconds}

import scala.concurrent.{ExecutionContext, Future}
import utils.{<PERSON><PERSON>, SRLogger, StringUtils}
import utils.helpers.LogHelpers
import utils.mq.services.{MQConfig, SimpleMqServiceTrait}


case class AssociateProspectsData(
                                   new_prospects: Seq[NewlyCreatedProspect], 
                                   team_id: Long,
                                   campaign_ids: Option[Seq[Long]]
                                 )

class MQAssociateProspectToOldEmails(
                                    //  inboxV3Service: InboxV3Service,
                                      associateProspectToEmailThreadService : AssociateProspectToEmailThreadService
                                    ) extends SimpleMqServiceTrait[AssociateProspectsData] {

  val queueBaseName: String = MQConfig.associateProspectQueueBaseName
  val prefetchCount: Int = MQConfig.associateProspectPrefetchCount

  override def processMessage(msg: AssociateProspectsData)(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem): Future[Any] = {

    val team_id = msg.team_id
    val prospects = msg.new_prospects
    val prospect_ids = prospects.map(_.prospect_id)

    val logTraceId = s"${StringUtils.genLogTraceId}_th_${Thread.currentThread().getName}"
    given Logger: SRLogger= new SRLogger(
      logRequestId = s"$logTraceId [AssociateProspectToOldEmails] prospectIds: $prospect_ids",
      customLogTraceId = Some(logTraceId)
    )

    Logger.info(s"[MQAssociateProspectToOldEmails] start")

    val startTime = DateTime.now()

    associateProspectToEmailThreadService.associateNewProspectToEmailThread(
      prospects = prospects,
      team_id = team_id,
      campaign_ids = msg.campaign_ids
    )
      .map { res =>

        val endTime = DateTime.now()
        val timeTaken = Seconds.secondsBetween(startTime, endTime).getSeconds
        val timeTakeMillis = endTime.getMillis - startTime.getMillis

        Logger.info(s"Prospect associated:: ${msg}: (timeTakeMillis: $timeTakeMillis) :: took from $startTime to $endTime (timetaken: $timeTaken) :: $res")

        res

      }
      .recover { case e =>

        val endTime = DateTime.now()
        val timeTaken = Seconds.secondsBetween(startTime, endTime).getSeconds
        val timeTakeMillis = endTime.getMillis - startTime.getMillis

        Logger.error(s"Prospect association failed:: ${msg}: (timeTakeMillis: $timeTakeMillis) :: took from $startTime to $endTime (timetaken: $timeTaken) :: ${LogHelpers.getStackTraceAsString(e)}")

        0
      }
  }

}
