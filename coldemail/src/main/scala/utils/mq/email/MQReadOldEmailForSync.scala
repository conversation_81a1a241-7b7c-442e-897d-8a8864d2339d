package utils.mq.email

import org.apache.pekko.actor.ActorSystem
import io.sr.billing_common.models.PlanType
import api.{RedisConnectionIssue, RedisLockNotAcquired}
import api.emails.services.OldEmailSyncService
import api.team_inbox.model.InitialEmailSyncData
import org.joda.time.DateTime
import play.api.libs.ws.WSClient
import utils.mq.replytracker.ReplyTrackerService
import utils.mq.services.{MQConfig, MQDoNotNackException, SimpleMqServiceTrait}
import utils.{SRLogger, StringUtils}

import scala.concurrent.{ExecutionContext, Future}


case class TeamInboxForSync (
                              inbox_name: String,
                              team_inbox_id: Long,
                              email_setting_id: Long,
                              initial_sync_data: InitialEmailSyncData,
                              fromDate: DateTime,
                              tillDate: DateTime,
                              org_plan_type: PlanType,
                              created_at: DateTime //inbox created_at time
                            )
class MQReadOldEmailForSync (
                              replyTrackerService: ReplyTrackerService,
                              oldEmailSyncService: OldEmailSyncService
                            ) extends SimpleMqServiceTrait[TeamInboxForSync] {

  val queueBaseName: String = MQConfig.readOldEmailForSyncBaseName
  val prefetchCount: Int = MQConfig.readOldEmailForSyncPrefetchCount

  override def processMessage(msg: TeamInboxForSync)(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem): Future[Any] = {


    val esId = msg.email_setting_id

    val logTraceId = s"${StringUtils.genLogTraceId}_th_${Thread.currentThread().getName}"
    given Logger: SRLogger= new SRLogger(
      logRequestId = s"$logTraceId [SYNCINBOX] eset_$esId",
      customLogTraceId = Some(logTraceId)
    )

    Logger.info(s"[MQReadOldEmailForSync] start")
    val startTime = DateTime.now()

    Future.fromTry(
      replyTrackerService.acquireLockOnInboxForRead(
        esId = esId, SRLogger = Logger)
    )
      .flatMap(acquiredLock => {

        if (acquiredLock) Future.successful(true)
        else {
          logger.warn(s"Unable to acquire lock as other process has acquired it - esid_${esId}")
          Future.failed(
            RedisLockNotAcquired(
              message = "RedisLockNotAcquired"
            )
          )
        }


      })
      .flatMap(_ => {

        oldEmailSyncService.syncEmailForAGivenTimePeriod(msg = msg)

      })
      .flatMap(_ => Future.fromTry(
        replyTrackerService.releaseLockOnInboxForRead(
          esId = esId,
          SRLogger = Logger))
      )
      .recover {

        case e: RedisConnectionIssue =>
          Logger.error(s"[MQReadOldEmailForSync] error RedisConnectionIssue not-releasing-lock", err = e)

        case e: RedisLockNotAcquired =>
          Logger.error(s"[MQReadOldEmailForSync] error RedisLockNotAcquired not-releasing-lock", err = e)

        case e =>

          e match {

            case errForLog: MQDoNotNackException if !errForLog.logAsError =>

              Logger.debug(s"[MQReadOldEmailForSync] warn NonRedisError: ${e.getMessage}")

            case _ =>

              Logger.error(s"[MQReadOldEmailForSync] error NonRedisError", err = e)

          }


          replyTrackerService.releaseLockOnInboxForRead(
            esId = esId, SRLogger = Logger
          )
            .get

          throw e
      }
  }


}
