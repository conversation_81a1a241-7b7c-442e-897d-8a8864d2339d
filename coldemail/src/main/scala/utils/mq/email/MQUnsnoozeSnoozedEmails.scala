package utils.mq.email

import org.apache.pekko.actor.ActorSystem
import play.api.libs.ws.WSClient
import api.prospects.{ChangeArchiveOrSnoozeStatusOfConvError, InboxV3Service}
import api.emails.ThreadAndTeamId
import org.joda.time.{DateTime, Seconds}

import scala.concurrent.{ExecutionContext, Future}
import utils.{SRLogger, StringUtils}
import utils.helpers.LogHelpers
import utils.mq.services.{MQConfig, SimpleMqServiceTrait}

class MQUnsnoozeSnoozedEmails(
                               inboxV3Service: InboxV3Service
                             ) extends SimpleMqServiceTrait[List[ThreadAndTeamId]] {

  val queueBaseName: String = MQConfig.unsnoozeSnoozedEmailsQueueBaseName
  val prefetchCount: Int = MQConfig.unsnoozeSnoozedEmailsPrefetchCount

  override def processMessage(msg: List[ThreadAndTeamId])(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem): Future[Any] = {

    val logTraceId = s"${StringUtils.genLogTraceId}_MQUnsnoozeSnoozedEmails"
    given Logger: SRLogger= new SRLogger(
      logRequestId = s"$logTraceId [MQUnsnoozeSnoozedEmails]",
      customLogTraceId = Some(logTraceId)
    )

    Logger.info(s"[MQUnsnoozeSnoozedEmails] start")

    val startTime = DateTime.now()

    inboxV3Service.unsnoozeThreadAutomatically(
      threadsAndTeamIds = msg
    )
      .map { res =>

        val endTime = DateTime.now()
        val timeTaken = Seconds.secondsBetween(startTime, endTime).getSeconds
        val timeTakeMillis = endTime.getMillis - startTime.getMillis

        res match {
          case Left(ChangeArchiveOrSnoozeStatusOfConvError.DBFailure(e)) =>
            Logger.error(s"Emails unsnooze DBFailure::: (timeTakeMillis: $timeTakeMillis) :: took from $startTime to $endTime (timetaken: $timeTaken) :: ${LogHelpers.getStackTraceAsString(e)}")

          case Left(ChangeArchiveOrSnoozeStatusOfConvError.UnarchiveOrUnsnoozeFailed(errMsg)) =>
            Logger.error(s"Emails unsnooze Failed::: (timeTakeMillis: $timeTakeMillis) :: took from $startTime to $endTime (timetaken: $timeTaken) :: ${errMsg}")

          case Right(unsnooze_res) =>
            Logger.info(s"Emails unsnoozed:: ${unsnooze_res}: (timeTakeMillis: $timeTakeMillis) :: took from $startTime to $endTime (timetaken: $timeTaken)")
        }


        res

      }
      .recover { case e =>

        val endTime = DateTime.now()
        val timeTaken = Seconds.secondsBetween(startTime, endTime).getSeconds
        val timeTakeMillis = endTime.getMillis - startTime.getMillis

        Logger.error(s"Emails unsnooze failed:: ${msg}: (timeTakeMillis: $timeTakeMillis) :: took from $startTime to $endTime (timetaken: $timeTaken) :: ${LogHelpers.getStackTraceAsString(e)}")

        0
      }
  }

}

