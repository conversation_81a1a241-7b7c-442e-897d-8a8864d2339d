package utils.mq.captainData


import api.accounts.TeamId
import api.linkedin.LinkedinSettingService
import api.linkedin.models.LinkedinSettingUuid
import org.apache.pekko.actor.ActorSystem
import play.api.libs.ws.WSClient
import utils.SRLogger
import utils.mq.services.{MQConfig, SimpleMqServiceTrait}

import scala.util.{Failure, Success}
import scala.concurrent.{ExecutionContext, Future}

case class LinkedinSettingDeletionData(
                                 linkedinSettingUuid: LinkedinSettingUuid,
                                 teamId: TeamId
                               )

class MqDeleteLinkedinSetting(
                          linkedinSettingService: LinkedinSettingService
                        ) extends SimpleMqServiceTrait[LinkedinSettingDeletionData] {

  override val queueBaseName: String = MQConfig.handleMqLinkedinSettingDeletion
  override val prefetchCount: Int = MQConfig.handleMqLinkedinSettingDeletionPrefetchCount

  override def processMessage(
                               msg: LinkedinSettingDeletionData
                             )(
                               implicit ws: WSClient,
                               ec: ExecutionContext,
                               system: ActorSystem): Future[Any] = {

    implicit lazy val logger: SRLogger = new SRLogger(logRequestId = s"MqLinkedinDeletion :: processMessage :: $msg :: linkedinSettingUuid = ${msg.linkedinSettingUuid.uuid} :: teamId = ${msg.teamId.id}")

    linkedinSettingService.deleteLinkedinAccount(
        uuid = msg.linkedinSettingUuid.uuid,
        team_id = msg.teamId.id
      )
      .recover {
        case e =>
          logger.shouldNeverHappen(s"MqLinkedinDeletion :: error while processing message :: linkedinSettingUuid ${msg.linkedinSettingUuid.uuid} :: teamId ${msg.teamId.id} :: error:: ${e.getMessage}", err = Some(e))
      }
  }
}
