package utils.mq.captainData





import api.accounts.TeamId
import api.linkedin.{LinkedinConnectionsService, LinkedinSettingService}
import api.linkedin.models.{CaptainDataWebhookResponse, LinkedinSettingUuid}
import org.apache.pekko.actor.ActorSystem
import play.api.libs.ws.WSClient
import utils.SRLogger
import utils.mq.services.{MQConfig, SimpleMqServiceTrait}

import scala.util.{Failure, Success}
import scala.concurrent.{ExecutionContext, Future}


case class ExtractLinkedinConnectionResultsData(
                                           data: CaptainDataWebhookResponse
                                          )

class MqExtractLinkedinConnectionResults(
                                        linkedinConnectionsService: LinkedinConnectionsService
                                             
                                           ) extends SimpleMqServiceTrait[ExtractLinkedinConnectionResultsData] {

  override val queueBaseName: String = MQConfig.handleMqExtractLinkedinConnectionResults
  override val prefetchCount: Int = MQConfig.handleMqExtractLinkedinConnectionResultsPrefetchCount

  override def processMessage(
                               msg: ExtractLinkedinConnectionResultsData
                             )(
                               implicit ws: WSClient,
                               ec: ExecutionContext,
                               system: ActorSystem): Future[Any] = {

    implicit lazy val logger: SRLogger = new SRLogger(logRequestId = s"MqExtractLinkedinConnectionResults :: processMessage :: $msg :: job_uid = ${msg.data.job_uid.uid} ")

    linkedinConnectionsService.handleExtractLinkedinConnectionGetResults(
        data = msg.data
      )
      .recover {
        case e =>
          logger.shouldNeverHappen(s"MqExtractLinkedinConnectionResults :: error while processing message :: job_uid ${msg.data.job_uid.uid} :: error:: ${e.getMessage}", err = Some(e) )
      }

  }

  
}


