package utils.mq.inbox_placement

import org.apache.pekko.actor.ActorSystem
import api.campaigns.dao.{InboxPlacementCheckDAO, InboxPlacementCheckTrackedEmails}
import api.campaigns.models.InboxPlacementCheckLogId
import api.emails.{EmailHeaderForInboxPlacementCheckAnalysis, EmailScheduledDAO}
import play.api.libs.json.JsValue
import play.api.libs.ws.WSClient
import utils.SRLogger
import utils.mq.inbox_placement.MqAnalyzeInboxPlacementCheckEmail.getAnalyzedHeader
import utils.mq.services.{MQConfig, SimpleMqServiceTrait}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

case class AnalyzedHeader(
                           spf_is_valid: Option[Boolean],
                           dkim_is_valid: Option[Boolean],
                           dmarc_is_valid: Option[Boolean]
                         )

case class AnalyzedDataToBeUpdated(
                                    analyzedHeader: AnalyzedHeader,
                                    inbp_id: InboxPlacementCheckLogId,
                                    emailHeaders: JsValue
                                  )
object MqAnalyzeInboxPlacementCheckEmail {
  def getAnalyzedHeader(
                         emailHeader: JsValue
                       ): Try[AnalyzedHeader] = Try {
    val authResult: Option[String] = (emailHeader \ "Authentication-Results").asOpt[String]

    var spf_is_valid: Option[Boolean] = None
    var dkim_is_valid: Option[Boolean] = None
    var dmarc_is_valid: Option[Boolean] = None

    if (authResult.isDefined) {
      spf_is_valid = Some(authResult.get.contains("spf=pass"))
      dkim_is_valid = Some(authResult.get.contains("dkim=pass"))
      dmarc_is_valid = Some(authResult.get.contains("dmarc=pass"))
    }

    AnalyzedHeader(
      spf_is_valid = spf_is_valid,
      dkim_is_valid = dkim_is_valid,
      dmarc_is_valid = dmarc_is_valid
    )
  }
}

class MqAnalyzeInboxPlacementCheckEmail(
                                        emailScheduledDAO: EmailScheduledDAO,
                                        inboxPlacementCheckDAO: InboxPlacementCheckDAO
                                       ) extends SimpleMqServiceTrait[InboxPlacementCheckTrackedEmails] {

  override val queueBaseName: String = MQConfig.analyzeInboxPlacementCheckEmailBaseName
  override val prefetchCount: Int = MQConfig.analyzeInboxPlacementCheckEmailPrefetchCount

  override def processMessage(msg: InboxPlacementCheckTrackedEmails)(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem): Future[Int] = {

    implicit lazy val logger: SRLogger = new SRLogger(logRequestId = s"MqAnalyzeInboxPlacementCheckEmail :: processMessage :: $msg :: ")

    val res: Try[Int] = for {
      emailData: Option[EmailHeaderForInboxPlacementCheckAnalysis] <- emailScheduledDAO.getInboxPlacementCheckReceivedEmailData(
        inboxPlacementCheckTrackedEmails = msg
      )

      analyzedEmailHeader: AnalyzedHeader <- if (emailData.isDefined) {
        getAnalyzedHeader(emailHeader = emailData.get.emailHeaders)
      } else {
        Success(AnalyzedHeader(
          spf_is_valid = None, dkim_is_valid = None, dmarc_is_valid = None
        ))
      }

      analyzedDataToBeUpdated: AnalyzedDataToBeUpdated <- Try {
        AnalyzedDataToBeUpdated(
          analyzedHeader = analyzedEmailHeader, inbp_id = msg.id, emailHeaders = emailData.get.emailHeaders
        )
      }

      updatedAnalyzedData <- inboxPlacementCheckDAO.updateEmailHeaderAnalysis(
        analyzedDataToBeUpdated = analyzedDataToBeUpdated
      )

    } yield {
      updatedAnalyzedData
    }

    res match {
      case Failure(exception) =>
        logger.error(s"Error while analyzing email header data:", exception)
        Future.failed(exception)
      case Success(value) => Future.successful(value)
    }
  }
}
