package utils.mq.inbox_placement

import api.accounts.dao_service.AccountDAOService
import org.apache.pekko.actor.ActorSystem
import api.accounts.{Account, AccountEmail, AccountService, TeamId}
import api.accounts.models.{AccountId, OrgId}
import api.calendar_app.models.CalendarAccountData
import api.campaigns.{Campaign, CampaignBasicInfo, CampaignStepVariant, CampaignStepVariantDAO}
import api.campaigns.dao.{InboxPlacementCheckDAO, InboxPlacementCheckDetails}
import api.campaigns.models.{CampaignEmailSettingsId, CampaignStepData, CampaignStepType, GetTestStepsError, SubjectAndBody}
import api.emails.models.EmailSendingFlow
import api.emails.{EmailScheduledDAO, EmailSettingDAO, InboxPlacementCheckSentEmailScheduledDetails}
import api.campaigns.services.{CampaignDAOService, CampaignId, CampaignService, CampaignTestStepService}
import io.smartreach.esp.api.emails.EmailSettingId
import play.api.libs.ws.WSClient
import utils.helpers.LogHelpers
import utils.{Helpers, SRLogger}
import utils.mq.services.{MQConfig, MQService, SimpleMqServiceTrait}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

case class SendEmailDetailsForInboxPlacementCheck(
                                                   inboxPlacementCheckDetails: InboxPlacementCheckDetails,
                                                   receiver_email_setting_id: EmailSettingId
                                                 )

/*
SendEmailDetailsForInboxPlacementCheck
----------------
inboxPlacementCheckDetails: InboxPlacementCheckDetails(
  sender_email_setting_id: EmailSettingId,
  email_domain: EmailSettingDomain,
  campaign_id: CampaignId,
  campaign_name: String,
  team_id: TeamId,
  team_name: String,
  step_id: Long
)
receiver_email_setting_id: Long
--------------
 */
class MqSendEmailForInboxPlacementCheck(
                                         campaignDAOService: CampaignDAOService,
                                         accountDAOService: AccountDAOService,
                                         campaignStepVariantDAO: CampaignStepVariantDAO,
                                         emailSettingDAO: EmailSettingDAO,
                                         emailScheduledDAO: EmailScheduledDAO,
                                         inboxPlacementCheckDAO: InboxPlacementCheckDAO,
                                         campaignTestStepService: CampaignTestStepService
                                       ) extends MQService[SendEmailDetailsForInboxPlacementCheck] {

  val prefetchCount: Int = MQConfig.sendTestEmailForInboxPlacementPrefetchCount
  override val logIfQueueSizeAbove: Int = 4000
  def publish(message: SendEmailDetailsForInboxPlacementCheck) = {
    val queueBaseName: String = MQConfig.getSendEmailForInboxPlacementCheckQueueBaseName(rep_mail_server_id = message.inboxPlacementCheckDetails.rep_mail_server_id)

    publishMsg(message = message, queueBaseName = queueBaseName, prefetchCount = prefetchCount)
  }

  def startConsumer(rep_mail_server_id: Option[Long])(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem): Try[String] = {
    val queueBaseName: String = MQConfig.getSendEmailForInboxPlacementCheckQueueBaseName(rep_mail_server_id = rep_mail_server_id)

    startMsgConsumer(queueBaseName = queueBaseName, prefetchCount = prefetchCount)
  }
  override def processMessage(msg: SendEmailDetailsForInboxPlacementCheck)(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem): Future[Unit] = {

    implicit lazy val logger: SRLogger = new SRLogger(logRequestId = s"MqSendEmailForInboxPlacementCheck :: processMessage :: $msg :: ")

    val inbPlcmntCheckDetails: InboxPlacementCheckDetails = msg.inboxPlacementCheckDetails
    //1. fetch all details required for sending the email

    /**
     * campaign_id -> campaign object
     * campaign object -> owner_id
     * owner_id -> account
     * account -> owner_name, owner_email, ta_id, org_id
     *
     * CampaignStepVariantDAO - findByCampaignId -> body, subject, step_id
     * sender_email_setting_id - campaign_email_settings_id (get from campaign object -CampaignEmailSettings -> where sender_email_setting_id)
     */

    val sendTestEmailResult: Future[Either[GetTestStepsError, Unit]] = for {

      //1.1 get campaign object from campaign_id
      campaign: Option[Campaign] <- Future.fromTry(Try {
        campaignDAOService.findCampaignForCampaignUtilsOnly(
          teamId = inbPlcmntCheckDetails.team_id,
          id = inbPlcmntCheckDetails.campaign_id.id
        )
      })

      userAccount: Account <- Future.fromTry(accountDAOService.find(
        id = campaign.get.account_id
      ))

      campaignStepVariant: Seq[CampaignStepVariant] <- Future.fromTry(Try {
        campaignStepVariantDAO.findByStepId(
          stepId = inbPlcmntCheckDetails.step_id
        )
      })

      campaign_email_settings_id: CampaignEmailSettingsId <- Future.fromTry(Try {
        campaign.map(c => {
          c.settings.campaign_email_settings.filter(_.sender_email_setting_id.emailSettingId == inbPlcmntCheckDetails.sender_email_setting_id.emailSettingId)
        }).get.head.id
      })

      campaignStepData: CampaignStepData <- Future.fromTry(Try {
        campaignStepVariant.filter(_.step_id == inbPlcmntCheckDetails.step_id).head.step_data
      })

      campaignSubjectAndBody: SubjectAndBody <- Future.fromTry(Try {
        CampaignStepData.getSubjectAndBodyFromStepData(
          stepData = campaignStepData
        )
      })

      ta_id: Long <- Future.fromTry(Try {
        userAccount.teams.filter(_.team_id == inbPlcmntCheckDetails.team_id.id).head.access_members.filter(_.user_id == campaign.get.account_id).head.ta_id
      })

      toEmail: String <- Future.fromTry(Try {
        emailSettingDAO.find(id = msg.receiver_email_setting_id.emailSettingId).get.email
      })

      sentEmail: Either[GetTestStepsError, Unit] <- campaignTestStepService.testStep(
        stepId = Some(inbPlcmntCheckDetails.step_id),
        body = campaignSubjectAndBody.body,
        subject = campaignSubjectAndBody.subject,
        campaign = campaign.get,
        toEmail = toEmail,
        campaignId = inbPlcmntCheckDetails.campaign_id,
        stepType = CampaignStepType.AutoEmailStep,
        allTrackingDomainsUsed = Seq(),
        campaign_email_settings_id = campaign_email_settings_id,
        team_id = inbPlcmntCheckDetails.team_id,
        owner_id = AccountId(campaign.get.account_id),
        ta_id = ta_id,
        owner_name = Helpers.getAccountName(userAccount),
        owner_email = AccountEmail(userAccount.email),
        calendar_account_data = None,
        emailSendingFlow = Some(EmailSendingFlow.InboxPlacementOut),
        org_id = OrgId(userAccount.org.id)
      )

    } yield {
      sentEmail
    }


    //2. call testStep()

    sendTestEmailResult.map {
      case Left(err) =>
        logger.fatal(s"Send test email failed: ${err}")
        inboxPlacementCheckDAO.updateStatusForSendEmailFailure(
          teamId = inbPlcmntCheckDetails.team_id,
          senderEmailSettingId = inbPlcmntCheckDetails.sender_email_setting_id
        ) match {
          case Failure(exception) => logger.shouldNeverHappen(s"updateStatusForSendEmailFailure failed: ${LogHelpers.getStackTraceAsString(exception)}")
          case Success(value) =>
            logger.success("updateStatusForSendEmailFailure completed")
        }

      case Right(value) => logger.success(s"Send test email is successfull")
      //3. update internal_inbox_placement_check_logs status and other fields
        /**
         * Fields to be updated -
         * email_scheduled_id_outgoing
         * sent_message_id_header
         * check_status
         * sent_at
         * updated_at
         */

        val res: Try[Int] = for {
          emailScheduledDetails: InboxPlacementCheckSentEmailScheduledDetails <- emailScheduledDAO.getInboxPlacementCheckSentEmailScheduledDetails(
            teamId = inbPlcmntCheckDetails.team_id,
            senderEmailSettingId = inbPlcmntCheckDetails.sender_email_setting_id
          )

          updatedSentEmailData: Int <- inboxPlacementCheckDAO.updateSentEmailDetails(
            teamId = inbPlcmntCheckDetails.team_id,
            senderEmailSettingId = inbPlcmntCheckDetails.sender_email_setting_id,
            inboxPlacementCheckSentEmailScheduledDetails = emailScheduledDetails
          )
        } yield {
          updatedSentEmailData
        }

        res match {
          case Failure(exception) => logger.shouldNeverHappen(s"Update sent email data failed", Some(exception))
          case Success(_) =>
            Thread.sleep(scala.util.Random.between(30000, 60000))
            logger.success(s"Sent email data updated!")
        }
    }.recover(e => {
      logger.error("Send test email failed", e)
    })

  }
}
