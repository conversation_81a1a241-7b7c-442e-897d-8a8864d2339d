package utils.mq.intercom_updater


import org.apache.pekko.actor.ActorSystem
import play.api.libs.ws.WSClient
import utils.{SRLogger, StringUtils}
import utils.intercom.services.IntercomUpdateService
import utils.mq.services.{MQConfig, MQService}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.Try

case class MqIntercomUpdaterMsg(
  orgId: Long
)

class MqIntercomUpdater(
  intercomUpdateService: IntercomUpdateService
) extends MQService[MqIntercomUpdaterMsg] {

  val prefetchCount: Int = MQConfig.intercomUpdaterQueuePrefetchCount
  val queueBaseName: String = MQConfig.intercomUpdaterQueueBaseName


  def publish(message: MqIntercomUpdaterMsg) = {
    publishMsg(message = message, queueBaseName = queueBaseName, prefetchCount = prefetchCount)
  }

  def startConsumer()(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem): Try[String] = {
    startMsgConsumer(queueBaseName = queueBaseName, prefetchCount = prefetchCount)
  }

  def processMessage(
    msg: MqIntercomUpdaterMsg
  )(
    implicit ws: WSClient,
    ec: ExecutionContext,
    system: ActorSystem
  ): Future[Unit] = {

    given logger: SRLogger = new SRLogger(logRequestId = s"[MqIntercomUpdater] ${StringUtils.genLogTraceId} org_${msg.orgId}")

    intercomUpdateService
      .updateForOrgId(
        orgId = msg.orgId
      )
      .map(_ => {
        logger.info("updated data successfully")
      })
      .recover { case e =>

        logger.fatal(s"failed updating data", err = e)
      }

  }

}