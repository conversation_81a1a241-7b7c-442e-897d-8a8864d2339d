package utils.mq.multichannel

import org.apache.pekko.actor.ActorSystem
import api.accounts.TeamId
import api.linkedin.LinkedinSettingService
import api.phantombuster.{ExecuteAutoLinkedinTasksError, PhantomBusterService}
import api.tasks.models.TasksGroupedByTypeAndLinkedinSetting
import api.tasks.services.{TaskDaoService, TaskService}
import org.joda.time.DateTime
import play.api.libs.ws.WSClient
import utils.{SRLogger, StringUtils}
import utils.mq.services.{MQConfig, SimpleMqServiceTrait}

import scala.concurrent.{ExecutionContext, Future}

class MqExecuteDueLinkedinTasks(
                                 linkedinSettingService: LinkedinSettingService,
                                 taskService: TaskService
                               )
  extends SimpleMqServiceTrait[TasksGroupedByTypeAndLinkedinSetting] {

  val queueBaseName: String = MQConfig.dueLinkedinTasksQueueName
  val prefetchCount: Int = MQConfig.dueLinkedinPrefetchCount

  override def processMessage(
                               msg: TasksGroupedByTypeAndLinkedinSetting
                             )(
                               implicit ws: WSClient,
                               ec: ExecutionContext,
                               system: ActorSystem
                             ): Future[Any] = {
    
    implicit lazy val logger: SRLogger = new SRLogger(logRequestId = s"MqExecuteDueLinkedinTasks :: processMessage :: teamId_${msg.commonLinkedinTaskDetails.teamId} : ")
    
    linkedinSettingService.validateAndHandleCaptainDataTask(
        tasksGroupedByTypeAndLinkedinSetting = msg
      )
      .recover {
        case e =>
          logger.error(s"$msg :: ${e.getMessage}", e)

          val res = if (e.getMessage == ExecuteAutoLinkedinTasksError.AgentExecutionLimitReached.toString ||
                        e.getMessage == ExecuteAutoLinkedinTasksError.AgentNotFound.toString ||
                        e.getMessage == ExecuteAutoLinkedinTasksError.AllAccountsHaveExhaustedExecutionTimeLimit.toString) {
            taskService.moveTasksToDue(
              taskIds = msg.taskIds,
              teamId = msg.commonLinkedinTaskDetails.teamId
            )
          }
          else {
            Future.sequence {
              msg.taskIds.map(taskId => {
                taskService.failTask(
                  taskId = taskId,
                  teamId = msg.commonLinkedinTaskDetails.teamId,
                  failureReason = e.getMessage
                )
              })
            }
          }

          res
            .recover {
              case e =>
                logger.error(s"Error while updating task status: ${e.getMessage}", e)
            }

      }

  }

}
