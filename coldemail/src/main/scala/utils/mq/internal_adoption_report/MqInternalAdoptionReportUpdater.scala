package utils.mq.internal_adoption_report

import org.apache.pekko.actor.ActorSystem
import api.accounts.models.OrgId
import play.api.libs.ws.WSClient
import utils.{SRLogger, StringUtils}
import utils.customersupport.services.InternalAdoptionReportUpdateService
import utils.mq.services.{MQConfig, SimpleMqServiceTrait}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}

case class MqInternalAdoptionReportUpdaterMsg(
  orgId: OrgId
)

class MqInternalAdoptionReportUpdater(
  internalAdoptionReportUpdateService: InternalAdoptionReportUpdateService
)
  extends SimpleMqServiceTrait[MqInternalAdoptionReportUpdaterMsg] {

  val queueBaseName: String = MQConfig.internalAdoptionReportQueueBaseName
  val prefetchCount: Int = MQConfig.internalAdoptionReportPrefetchCount


  override def processMessage(
    msg: MqInternalAdoptionReportUpdaterMsg
  )(
    implicit ws: WSClient,
    ec: ExecutionContext,
    system: ActorSystem,
  ): Future[Unit] = Future {


    val logTraceId = s"${StringUtils.genLogTraceId}_th_${Thread.currentThread().getName}"
    given logger: SRLogger= new SRLogger(
      logRequestId = s"$logTraceId [MqInternalAdoptionReportUpdater] orgId: ${msg.orgId}",
      customLogTraceId = Some(logTraceId)
    )

    internalAdoptionReportUpdateService
      .updateForOrgId(
        orgId = msg.orgId
      )
      match {
        case Success(_) => {
          logger.info("updated data successfully")
        }
        case Failure(e) =>

          logger.fatal(s"failed updating data", err = e)
      }
      }

}
