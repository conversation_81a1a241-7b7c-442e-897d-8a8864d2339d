package utils.mq.captain_data

import api.linkedin.models.{CDJ<PERSON>U<PERSON>, CaptainDataWorkflowStatus}
import utils.mq.services.{MQConfig, SimpleMqPublisherServiceTrait}

import scala.util.Try

case class MqCaptainDataMessageExtractionMessage(
  jobUid: CDJobUid,
  status: CaptainDataWorkflowStatus,
  error: Option[String]
)

class MqCaptainDataMessageExtractionPublisher extends SimpleMqPublisherServiceTrait[MqCaptainDataMessageExtractionMessage] {
  override val queueBaseName: String = MQConfig.captainDataMessageExtractionQueueBaseName
  override val prefetchCount: Int = MQConfig.captainDataMessageExtractionPrefetchCount

  def publishMessage(msg: MqCaptainDataMessageExtractionMessage): Try[Unit] = {
    publishMsg(message = msg, queueBaseName = queueBaseName, prefetchCount = prefetchCount)
  }
}