package utils.mq.captain_data

import api.captain_data.CaptainDataJobUID
import utils.mq.services.{MQConfig, SimpleMqPublisherServiceTrait}

import scala.util.Try

case class MqCaptainDataCookieFailureMessage(
  jobUid: CaptainD<PERSON>JobUID
)

class MqCaptainDataCookieFailurePublisher extends SimpleMqPublisherServiceTrait[MqCaptainDataCookieFailureMessage] {
  override val queueBaseName: String = MQConfig.captainDataCookieFailureQueueBaseName
  override val prefetchCount: Int = MQConfig.captainDataCookieFailurePrefetchCount

  def publishMessage(msg: MqCaptainDataCookieFailureMessage): Try[Unit] = {
    publishMsg(message = msg, queueBaseName = queueBaseName, prefetchCount = prefetchCount)
  }
}