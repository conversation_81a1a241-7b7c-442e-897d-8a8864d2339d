package utils.mq.captain_data

import api.phantombuster.CaptainDataService
import org.apache.pekko.actor.ActorSystem
import org.joda.time.{DateTime, Seconds}
import play.api.libs.ws.WSClient
import utils.{SRLogger, StringUtils}
import utils.mq.services.{MQConfig, MQConsumer, SimpleMqPublisherServiceTrait}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.Try

class MqCaptainDataConversationExtractionConsumer(
                                                 mqCaptainDataConversationExtractionPublisher: MqCaptainDataConversationExtractionPublisher,
                                                 captainDataService: CaptainDataService
                                                 ) extends MQConsumer[MqCaptainDataConversationExtractionMessage] {

  override val mqPublisherService: SimpleMqPublisherServiceTrait[MqCaptainDataConversationExtractionMessage] =
    mqCaptainDataConversationExtractionPublisher

  val queueBaseName: String = MQConfig.captainDataConversationExtractionQueueBaseName
  val prefetchCount: Int = MQConfig.captainDataConversationExtractionPrefetchCount

  def startConsumer()(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem): Try[String] = {
    startMsgConsumer(queueBaseName = queueBaseName, prefetchCount = prefetchCount)
  }

  override def processMessage(
                               data: MqCaptainDataConversationExtractionMessage
                             )(
                               implicit ws: WSClient,
                               ec: ExecutionContext,
                               system: ActorSystem
                             ): Future[Boolean] = {
    val startTime = DateTime.now()

    val logTraceId = s"${StringUtils.genLogTraceId}_th_${Thread.currentThread().getName}"
    given logger: SRLogger = new SRLogger(
      logRequestId = s"$logTraceId MqCaptainDataConversationExtraction",
      customLogTraceId = Some(logTraceId)
    )
    logger.info(
      msg = s"start - jobUid: ${data.jobUid.uid} :: status: ${data.status} :: error: ${data.error}",
    )

    captainDataService.handleLinkedinConversationExtraction(
      jobUid = data.jobUid,
      status = data.status,
      error = data.error
    ).map{
      result => {
        val endTime = DateTime.now()
        val timeTaken = Seconds.secondsBetween(startTime, endTime).getSeconds
        val timeTakeMillis = endTime.getMillis - startTime.getMillis

        logger.info(
          msg = s"end - result: ${result}, jobUid: ${data.jobUid.uid} :: status: ${data.status} :: error: ${data.error}  (timeTakeMillis: $timeTakeMillis) :: took from $startTime to $endTime (time-taken: $timeTaken)",
        )
        true
      }
    }.recover {
      case e: Exception => {
        val endTime = DateTime.now()
        val timeTaken = Seconds.secondsBetween(startTime, endTime).getSeconds
        val timeTakeMillis = endTime.getMillis - startTime.getMillis

        logger.error(
          msg = s"end - error: ${e.getMessage}, jobUid: ${data.jobUid.uid} :: status: ${data.status} :: error: ${data.error} (timeTakeMillis: $timeTakeMillis) :: took from $startTime to $endTime (time-taken: $timeTaken)",
          err = e
        )
        false
      }
    }
  }
}