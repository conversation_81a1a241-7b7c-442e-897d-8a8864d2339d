package utils.mq.spammonitor

import org.apache.pekko.actor.ActorSystem
import api.accounts.TeamId
import api.prospects.models.StepId
import api.campaigns.services.CampaignId
import play.api.libs.ws.WSClient
import api.spammonitor.service.SpamMonitorService
import utils.SRLogger
import utils.mq.services.{MQConfig, SimpleMqServiceTrait}

import scala.concurrent.{ExecutionContext, Future}

case class StepAndCampaignAndTeamId(
                                     stepId: StepId,
                                     campaignId: CampaignId,
                                     teamId: TeamId)

//FIXME SRAiAPI move the publish to SrAiApiAuditLog
class MqCampaignStepContentCheckService(spamMonitorService: SpamMonitorService) extends SimpleMqServiceTrait[StepAndCampaignAndTeamId] {

  val queueBaseName: String = MQConfig.campaignStepContentCheckQueueName
  val prefetchCount: Int = MQConfig.campaignStepContentCheckPrefetchCount

  override def processMessage(msg: StepAndCampaignAndTeamId)(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem): Future[Any] = {

    implicit lazy val logger: SRLogger = new SRLogger(logRequestId = s"[MqCampaignStepContentCheckService] :: processMessage: ${msg} ::")

    spamMonitorService.checkContentAndUpdateStatus(
      stepId = msg.stepId,
      campaignId = msg.campaignId,
      teamId = msg.teamId
    )
      .recover{
        case e => logger.fatal(e.getMessage, e)
      }

  }
}
