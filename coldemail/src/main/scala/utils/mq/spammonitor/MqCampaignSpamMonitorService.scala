package utils.mq.spammonitor

import org.apache.pekko.actor.ActorSystem
import api.spammonitor.dao.EmailSendingStatus
import api.spammonitor.model.EmailSendingEntityTypeData
import api.spammonitor.service.SpamMonitorService
import org.joda.time.{DateTime, Duration}
import play.api.libs.ws.WSClient
import utils.mq.services.{MQConfig, MQService}
import utils.{SRLogger, StringUtils}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.Try

class MqCampaignSpamMonitorService(
                                    spamMonitorService: SpamMonitorService
                                  ) extends MQService[EmailSendingStatus] {
  val queueBaseName: String = MQConfig.campaignSpamMonitorServiceBaseName
  val prefetchCount: Int = MQConfig.campaignSpamMonitorServicePrefetchCount

  var countOfIteration: Long = 0

  def publish(message: EmailSendingStatus): Try[Unit] = {
    publishMsg(message = message, queueBaseName = queueBaseName, prefetchCount = prefetchCount)
  }

  def startConsumer()(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem): Try[String] = {
    startMsgConsumer(queueBaseName = queueBaseName, prefetchCount = prefetchCount)
  }

  def processMessage(msg: EmailSendingStatus)(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem): Future[Any] = Future{
    val logTraceId = s"${StringUtils.genLogTraceId} [MqCampaignSpamMonitorService]"
    given logger: SRLogger = new SRLogger(
      logRequestId = s"$logTraceId campaign_id_${msg.entity_type.getEntityId()}",
      customLogTraceId = Some(logTraceId)
    )
    val startTime = DateTime.now()

    msg.entity_type match {
      case data: EmailSendingEntityTypeData.CampaignData =>
        countOfIteration = countOfIteration + 1
        spamMonitorService.checkIfCampaignAllowedToSendEmail(
          campaignData = data,
          oldEmailSendingStatus = msg.send_email_status
        ).map { _ =>

          logger.debug(s"email sending status updated for campaign id = ${data.campaignId}")

          val finishTime = DateTime.now()

          val timeTaken = new Duration(startTime, finishTime).toStandardSeconds.getSeconds

          logger.debug(s"Iteration #$countOfIteration Success - Time taken to finish the run for spam monitor - ${timeTaken}")

        }.recover { case e =>

          logger.fatal(s"Error while updating email sending status for campaign id - ${data.campaignId}", e)

          val finishTime = DateTime.now()

          val timeTaken = new Duration(startTime, finishTime).toStandardSeconds.getSeconds

          logger.debug(s"Iteration #$countOfIteration Failed - Time taken to finish the run for spam monitor - ${timeTaken} ")

        }
      case _ => logger.fatal(s"Wrong Kind of entity type sent")
    }
  }

}
