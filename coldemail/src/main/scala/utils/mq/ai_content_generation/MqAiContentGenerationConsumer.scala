package utils.mq.ai_content_generation

import api.accounts.TeamId
import api.accounts.service.AccountOrgBillingRelatedService
import api.calendar_app.models.CalendarAccountData
import api.campaigns.{CampaignEditedPreviewEmailDAO, CampaignProspectDAO, CampaignProspectUpdateScheduleStatus}
import sr_scheduler.models.SendingMode
import api.campaigns.models.{CampaignStepData, CampaignStepType, CurrentStepStatusForSchedulerData}
import api.campaigns.services.{CampaignDAOService, CampaignProspectService, CampaignService}
import api.emails.EmailSettingDAO
import api.emails.dao_service.EmailScheduledDAOService
import api.gpt.ai_hyperpersonalized.AIHyperPersonalizedGenerator
import api.prospects.models.ProspectId
import api.rep_tracking_hosts.service.RepTrackingHostService
import api.tasks.services.TaskService
import eventframework.ProspectObject
import io.smartreach.esp.api.emails.EmailSettingId
import org.apache.pekko.actor.ActorSystem
import org.apache.pekko.stream.Materializer
import play.api.libs.ws.WSClient
import sr_scheduler.models.CampaignForScheduling
import utils.email.EmailServiceCompanion
import utils.helpers.LogHelpers
import utils.mq.services.{MQConfig, MQConsumer, MQDoNotNackException, SimpleMqPublisherServiceTrait}
import utils.templating.TemplateService
import utils.{SRLogger, StringUtils}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

/**
 * MQ Consumer for AI content generation requests.
 * It consumes messages and delegates processing to AiContentGenerationProcessor.
 */
class MqAiContentGenerationConsumer(
  // Inject the specific publisher trait implementation
  mqPublisher: MqAiContentGenerationPublisher,
  // Inject the processor service
  processor: AiContentGenerationProcessor
) extends MQConsumer[MqAiContentGenerationRequest] {

  // Required by MQConsumer trait
  override val mqPublisherService: SimpleMqPublisherServiceTrait[MqAiContentGenerationRequest] = mqPublisher

  // Queue configuration
  val queueBaseName: String = MQConfig.aiContentGenerationQueueBaseName
  val prefetchCount: Int = MQConfig.aiContentGenerationQueuePrefetchCount

  /**
   * Starts the RabbitMQ consumer listener for this queue.
   */
  def startConsumer()(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem): Try[String] = {
    startMsgConsumer(queueBaseName = queueBaseName, prefetchCount = prefetchCount)
  }

  /**
   * Processes a single consumed message by calling the AiContentGenerationProcessor.
   *
   * @param msg The consumed MqAiContentGenerationRequest message.
   * @param ws Implicit WSClient.
   * @param ec Implicit ExecutionContext.
   * @param system Implicit ActorSystem.
   * @return Future[Any] where success means ACK the message, failure means NACK.
   */
  override def processMessage(
    msg: MqAiContentGenerationRequest
  )(implicit
    ws: WSClient,
    ec: ExecutionContext,
    system: ActorSystem
  ): Future[Any] = {

    val logTraceId = StringUtils.genLogTraceId
    given logger: SRLogger = new SRLogger(s"$logTraceId [MqAiContentGenerationConsumer] c:${msg.campaignId.id}_s:${msg.stepId.id}_p:${msg.prospectId.id}")
    implicit val materializer: Materializer = Materializer(system)

    logger.info("Received message, delegating to AiContentGenerationProcessor.")

    processor.process(msg) // Delegate the actual work
      .map { result =>
        logger.info(s"Processor completed successfully. Result: $result")
        result // Indicate success for ACK
      }.recover {
        case e: NoSuchElementException =>
          logger.warn(s"Data not found during processing for msg: $msg - ${e.getMessage}")
          throw MQDoNotNackException(s"Data not found during processing for msg: $msg - ${e.getMessage}")
        case e: IllegalStateException =>
          logger.error(s"Illegal state encountered during processing for msg: $msg - ${e.getMessage}", e)
          throw MQDoNotNackException(s"Illegal state encountered during processing for msg: $msg - ${e.getMessage}")
        case e: RuntimeException =>
          logger.error(s"Runtime error during processing for msg: $msg - ${e.getMessage}", e.getCause)
          throw MQDoNotNackException(s"Runtime error during processing for msg: $msg - ${e.getMessage}")
        case e: Throwable =>
          logger.error(s"Unexpected error during processing for msg: $msg", e)
          throw MQDoNotNackException(s"Unexpected error during processing for msg: $msg")
      }
  }
} 