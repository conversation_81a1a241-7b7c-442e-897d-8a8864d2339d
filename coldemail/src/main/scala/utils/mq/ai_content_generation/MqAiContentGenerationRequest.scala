package utils.mq.ai_content_generation

import api.accounts.models.OrgId
import api.accounts.TeamId
import api.campaigns.models.CampaignEmailSettingsId
import api.campaigns.services.CampaignId
import api.prospects.models.{ProspectId, StepId}
import io.smartreach.esp.api.emails.EmailSettingId
import org.joda.time.DateTime
import play.api.libs.json.{Json, OFormat}
import play.api.libs.json.JodaWrites._
import play.api.libs.json.JodaReads._

/**
 * Message payload for requesting AI content generation via MQ.
 * Contains essential identifiers to fetch detailed data in the consumer.
 *
 * @param campaignId             ID of the campaign.
 * @param stepId                 ID of the campaign step.
 * @param prospectId             ID of the prospect.
 * @param variantId              ID of the specific step variant.
 * @param senderEmailSettingsId ID of the email settings used for sending.
 * @param orgId                  ID of the organization.
 * @param teamId                 ID of the team.
 * @param scheduledAt            The original intended schedule time for the email/task.
 * @param logId                  ID of the log.
 */
case class MqAiContentGenerationRequest(
                                         campaignId: CampaignId,
                                         stepId: StepId,
                                         prospectId: ProspectId,
                                         variantId: Long,
                                         senderEmailSettingsId: EmailSettingId,
                                         campaignEmailSettingsId: CampaignEmailSettingsId,
                                         orgId: OrgId,
                                         teamId: TeamId,
                                         scheduledAt: DateTime,
                                         logId: Option[Long]
)
object MqAiContentGenerationRequest {

  implicit val format: OFormat[MqAiContentGenerationRequest] = Json.format[MqAiContentGenerationRequest]
}