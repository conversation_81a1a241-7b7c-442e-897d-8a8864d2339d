package utils.mq.ai_content_generation

// --- Imports --- 
import api.accounts.TeamId
import api.accounts.service.AccountOrgBillingRelatedService
import api.calendar_app.models.CalendarAccountData
import api.campaigns.models.CampaignStepData.{AutoEmailStep, ManualEmailStep}
import api.campaigns.models.CampaignStepType.{AutoLinkedinConnectionRequest, AutoLinkedinInmail, AutoLinkedinMessage, AutoLinkedinViewProfile, CallStep, GeneralTask, LinkedinConnectionRequest, LinkedinInmail, LinkedinMessage, LinkedinViewProfile, MoveToAnotherCampaignStep, SmsMessage, WhatsappMessage}
import api.campaigns.{CampaignBasicInfo, CampaignEditedPreviewEmail, CampaignEditedPreviewEmailDAO, CampaignProspectDAO, CampaignProspectUpdateScheduleStatus, CampaignStepVariantForScheduling, CampaignStepWithChildren, PreviousFollowUp}
import api.campaigns.models.{CampaignEmailSettingsId, CampaignStepData, CampaignStepType, CurrentStepStatusForSchedulerData}
import sr_scheduler.models.{CampaignForScheduling, SelectedCalendarData, SendingMode}
import api.emails.{EmailSetting, EmailSettingDAO, EmailsScheduledUuid}
import api.campaigns.models.{CampaignEmailSettingsId, CampaignStepType, CurrentStepStatusForSchedulerData}
import api.campaigns.services.{CampaignDAOService, CampaignService}
import api.emails.dao_service.EmailScheduledDAOService
import api.emails.services.EmailSettingService
import api.gpt.ai_hyperpersonalized.{AIHyperPersonalizedGenerator, AiContentGenerationOutput}
import api.prospects.dao_service.ProspectDAOService
import eventframework.ProspectObject
import api.prospects.ProspectAccount
import api.prospects.dao_service.ProspectDAOService
import api.rep_tracking_hosts.service.RepTrackingHostService
import api.tasks.services.TaskService
import api.tasks.services.{CreateTaskError, TaskService}
import org.apache.pekko.actor.ActorSystem
import org.apache.pekko.stream.Materializer
import play.api.libs.ws.WSClient
import sr_scheduler.models
import sr_scheduler.models.SendingMode
import utils.SRLogger
import utils.dateTime.SrDateTimeUtils
import utils.SRLogger
import api.tasks.models.{NewTask, TaskCreatedVia, TaskData, TaskPriority, TaskStatus, TaskStatusType, TaskType}
import api.tasks.models.Task
import api.llm.dao.{LlmAuditLogDAO, UpdateStatusData}
import api.columns.ProspectColGenStatus
import api.llm.models.LlmFlow
import api.sr_ai.dao.SrAiLockJedisDAO
import utils.email.{EmailOptionsForGetBodies, EmailServiceBody, EmailServiceCompanion}
import utils.mq.services.MQDoNotNackException
import utils.uuid.SrUuidUtils

import scala.concurrent.{ExecutionContext, Future}
import scala.util.Success
import scala.util.control.NonFatal
import scala.util.Failure

// Define the case class to encapsulate task generation data
case class TaskGenerationResult(
                                 taskData: TaskData,
                                 targetStatusData: CurrentStepStatusForSchedulerData,
                                 taskStatus: TaskStatus
                               )

/**
 * Service responsible for the core logic of processing AI content generation requests.
 * This class is called by the MQ consumer service.
 */
class AiContentGenerationProcessor(
                                    // --- Dependencies (Same as previous consumer implementation) ---
                                    campaignDAOService: CampaignDAOService,
                                    prospectDAOService: ProspectDAOService,
                                    aiHyperPersonalizedService: AIHyperPersonalizedGenerator,
                                    emailScheduledDAOService: EmailScheduledDAOService,
                                    campaignService: CampaignService,
                                    campaignProspectDAO: CampaignProspectDAO,
                                    srDateTimeUtils: SrDateTimeUtils,
                                    emailSettingDAO: EmailSettingDAO,
                                    taskService: TaskService,
                                    llmAuditLogDAO: LlmAuditLogDAO,
                                    srUuidUtils: SrUuidUtils,
                                    emailServiceCompanion: EmailServiceCompanion,
                                    repTrackingHostService: RepTrackingHostService,
                                    srAiLockJedisDAO: SrAiLockJedisDAO
) {

  /**
   * What it does:
   * Helper method to update the LLM audit log.
   *
   * Why it is like this:
   * Encapsulates the logic for updating the audit log, making the main `process` method cleaner.
   * It logs the outcome of the update attempt. The `updateCount` is always true as per requirements.
   */
  private def updateLlmAuditLog(
    logId: Long, // Assuming MqAiContentGenerationRequest will have logId: Long
    teamId: TeamId,
    status: ProspectColGenStatus,
    logContext: String,
    updateCount: Boolean,
    aiContentGenerationOutput: Option[AiContentGenerationOutput]
  )(implicit Logger: SRLogger, ec: ExecutionContext): Future[Unit] = {

    // MARK: - What it does: Create UpdateStatusData with LLM response data when available
    // MARK: - Why it is like this: When AI content generation succeeds, we need to store the prompts, outputs, and token consumption for audit purposes
    val updateData = UpdateStatusData(
      id = logId,
      teamId = teamId,
      newStatus = status,
      prompt = aiContentGenerationOutput.map(_.llmResponseData.prompt),
      output = aiContentGenerationOutput.map(_.llmResponseData.output),
      consumedTokenCountPromptInput = aiContentGenerationOutput.flatMap(_.llmResponseData.consumedTokenCountPromptInput),
      consumedTokenCountGenerationOutput = aiContentGenerationOutput.flatMap(_.llmResponseData.consumedTokenCountGenerationOutput)
    )
    
    Future.fromTry(llmAuditLogDAO.updateStatus(List(updateData), updateCount = updateCount))
      .map { updateResult =>
        Logger.info(s"LLM Audit Log $logId for team $teamId update to $status ($logContext) attempted. DAO result: $updateResult")
      }
      .recover {
        case NonFatal(e) =>
          Logger.error(s"Failed to update LLM Audit Log $logId for team $teamId to $status ($logContext). Error: ${e.getMessage}", e)
          // Do not throw here, to let the original flow's success/failure propagate.
          // The error is logged for monitoring.
      }
  }

  /**
   * Processes a single AI content generation request.
   *
   * @param msg The request message containing IDs.
   * @param Logger An SRLogger instance for logging.
   * @param ws Implicit WSClient.
   * @param ec Implicit ExecutionContext.
   * @param system Implicit ActorSystem.
   * @param materializer Implicit Materializer.
   * @return A Future[Int] indicating the number of rows affected (or 1 on success).
   */
  def process( // Renamed from processMessage to avoid confusion
    msg: MqAiContentGenerationRequest
  )(
    implicit Logger: SRLogger, // Pass logger explicitly
    ws: WSClient,
    ec: ExecutionContext,
    system: ActorSystem,
    materializer: Materializer
  ): Future[Int] = {

    if(!srAiLockJedisDAO.checkLock(aiModel = LlmFlow.MagicColumnFlow.model)) {
      Logger.info(s"Processing AI content generation request for c:${msg.campaignId.id}, s:${msg.stepId.id}, p:${msg.prospectId.id}. Assuming logId: ${msg.logId} is present in msg.") // Added logId to log

      val originalProcessFuture: Future[(AiContentGenerationOutput, Int)] = for {
        // 1. Fetch necessary data and validate existence within the for-comprehension
        campaignOpt: Option[CampaignBasicInfo] <- Future.fromTry(campaignService.getCampaignBasicInfo(
          campaginId = msg.campaignId.id,
          teamId = msg.teamId.id
        ))
        campaign: CampaignBasicInfo <- campaignOpt match {
          case Some(c) => Future.successful(c)
          case None => Future.failed(new NoSuchElementException(s"Campaign not found: ${msg.campaignId.id} for team ${msg.teamId.id}"))
        }

        prospects: Seq[ProspectObject] <- Future.fromTry(prospectDAOService.findProspects(
          byProspectIds = Seq(msg.prospectId.id),
          byCampaignId = Some(msg.campaignId.id),
          teamId = msg.teamId.id,
          org_id = msg.orgId
        ))
        prospect: ProspectObject <- {
          if (prospects.length == 1) {
            Future.successful(prospects.head)
          } else if (prospects.isEmpty) {
            Future.failed(new NoSuchElementException(s"Prospect not found: ${msg.prospectId.id} in campaign ${msg.campaignId.id}"))
          } else { // prospects.length > 1
            Future.failed(new IllegalStateException(s"Found multiple prospects for id ${msg.prospectId.id} in campaign ${msg.campaignId.id}"))
          }
        }

        orderedSteps: Seq[CampaignStepWithChildren] <- Future.fromTry(Success(campaignDAOService.findOrderedSteps(
          campaignId = msg.campaignId.id,
          teamId = msg.teamId
        )))
        currentStep: CampaignStepWithChildren <- orderedSteps.find(_.id == msg.stepId.id) match {
          case Some(step) => Future.successful(step)
          case None => Future.failed(new NoSuchElementException(s"Step not found: ${msg.stepId.id} in campaign ${msg.campaignId.id}"))
        }
        variant: CampaignStepVariantForScheduling <- currentStep.variants.headOption match {
          case Some(v) => Future.successful(v)
          case None => Future.failed(new NoSuchElementException(s"No variants found for step ${msg.stepId.id}"))
        }

        stepType: CampaignStepType = variant.step_data.step_type
        _: Unit <- stepType match {
          case CampaignStepType.ManualEmailMagicContent | CampaignStepType.AutoEmailMagicContent =>
            Future.successful(())
          case other =>
            Logger.error(s"Unexpected step type encountered for AI generation: $other for step ${msg.stepId.id}")
            Future.failed(new IllegalArgumentException(s"Cannot process AI generation for step type $other"))
        }

        previousCommunications: Seq[PreviousFollowUp] <- Future.fromTry(
          emailScheduledDAOService.getPreviousSentSteps(
            campaignId = msg.campaignId.id,
            prospectId = msg.prospectId.id,
            teamId = msg.teamId
          ).map(_.sortBy(pfu => pfu.sent_at.getMillis).reverse)

        ).recoverWith { case e =>
          Logger.error("Failed to fetch previous communications", e)
          Future.failed(new RuntimeException("Failed to fetch previous communications", e))
        }

        email_setting_data: EmailSetting <- emailSettingDAO.find(id = msg.senderEmailSettingsId.emailSettingId) match {
          case Some(value) => Future.successful(value)
          case None => Future.failed(new NoSuchElementException(s"Email setting id not found :: ${msg.senderEmailSettingsId.emailSettingId}"))
        }

        aiContentGenrationOutput : AiContentGenerationOutput <- aiHyperPersonalizedService.generateContentForTask(
          step_id = msg.stepId,
          step_data = variant.step_data,
          prospect_object = prospect,
          campaign_ai_generation_context = campaign.ai_generation_context,
          campaign_id = msg.campaignId,
          sender_email_settings_id = Some(msg.senderEmailSettingsId),
          orgId = msg.orgId,
          previous_communications = previousCommunications,
          orderedSteps = orderedSteps,
          accountId = email_setting_data.owner_id,
          teamId = msg.teamId
        ).recoverWith { case e =>
          Logger.error("AI content generation failed", e)
          Future.failed(new RuntimeException("AI content generation failed", e))
        }

        isCopilotMode: Boolean = SendingMode.checkIsCopilot(
          sending_mode = campaign.settings.sending_mode
        )


        taskType: TaskType <- stepType match {
          case CampaignStepType.AutoEmailStep |
               CampaignStepType.ManualEmailStep |
               CampaignStepType.LinkedinConnectionRequest |
               CampaignStepType.LinkedinMessage |
               CampaignStepType.LinkedinInmail |
               CampaignStepType.LinkedinViewProfile |
               CampaignStepType.AutoLinkedinConnectionRequest |
               CampaignStepType.AutoLinkedinMessage |
               CampaignStepType.AutoLinkedinInmail |
               CampaignStepType.AutoLinkedinViewProfile |
               CampaignStepType.GeneralTask |
               CampaignStepType.WhatsappMessage |
               CampaignStepType.SmsMessage |
               CampaignStepType.CallStep |
               CampaignStepType.MoveToAnotherCampaignStep

          =>
            Logger.shouldNeverHappen(s"Incorrect step type found :: StepType: ${stepType} :: stepId: ${msg.stepId} :: teamId: ${msg.teamId} :: prospectId: ${msg.prospectId}")
            Future.failed(new Exception("Incorrect step type found"))
          case CampaignStepType.AutoEmailMagicContent => Future.successful(TaskType.AutoEmailMagicContent)
          case CampaignStepType.ManualEmailMagicContent => Future.successful(TaskType.ManualEmailMagicContent)

        }

        allTrackingDomainsUsed = repTrackingHostService.getRepTrackingHosts().get.map(_.host_url)

        emailsScheduledUuidForTask: EmailsScheduledUuid = EmailsScheduledUuid(uuid = srUuidUtils.generateEmailsScheduledUuid())

        emailSubjectAndBody: EmailServiceBody <- {
          emailServiceCompanion.getBodies(
            editedPreviewEmail = None,
            org_id = email_setting_data.org_id.id,
            calendarAccountData = None,
            selectedCalendarData = None,
            head_step_id = campaign.head_step_id,

            emailsScheduledUuid = emailsScheduledUuidForTask,
            campaign_id = Some(campaign.id),
            step_id = Some(currentStep.id),
            prospect = prospect,
            emailOptions = EmailOptionsForGetBodies(
              for_editable_preview = false,
              editedPreviewEmailAlreadyChecked = true,
              custom_tracking_domain = email_setting_data.custom_tracking_domain,
              default_tracking_domain = email_setting_data.default_tracking_domain,
              default_unsubscribe_domain = email_setting_data.default_unsubscribe_domain,
              signature = Some(email_setting_data.signature),
              opt_out_msg = campaign.settings.opt_out_msg,
              opt_out_is_text = campaign.settings.opt_out_is_text,
              append_followups = campaign.settings.append_followups,
              bodyTemplate = aiContentGenrationOutput.body,
              subjectTemplate = aiContentGenrationOutput.subject,
              trackClicks = campaign.settings.click_tracking_enabled,
              email_sender_name = email_setting_data.sender_name,
              sender_first_name = email_setting_data.first_name,
              sender_last_name = email_setting_data.last_name,
              manualEmail = false,
              trackOpens = campaign.settings.open_tracking_enabled,
              previousEmails = Seq(),
              allTrackingDomainsUsed = allTrackingDomainsUsed,
            )
          ) match {
            case Failure(exception) => Future.failed(exception)
            case Success(value) => Future.successful(value)
          }
        }

        taskGenResult: TaskGenerationResult <- stepType match {
          case CampaignStepType.ManualEmailMagicContent =>
            Logger.debug("Manual step generated. Setting status to Approved.")
            Future.successful(TaskGenerationResult(
              TaskData.ManualEmailMagicContentData(
                generated_subject = emailSubjectAndBody.subject,
                generated_body = emailSubjectAndBody.htmlBody,
                email_scheduled_text_body = emailSubjectAndBody.textBody,
                email_scheduled_base_body = emailSubjectAndBody.baseBody,
                email_message_id = None
              ),
              CurrentStepStatusForSchedulerData.Approved(),
              TaskStatus.Approved(done_at = srDateTimeUtils.getDateTimeNow(), done_by = Some(email_setting_data.owner_id.id))
            ))
          case CampaignStepType.AutoEmailMagicContent =>
            if (isCopilotMode) {
              Logger.debug("Auto step in CoPilot mode. Setting status to PendingApproval.")
              Future.successful(TaskGenerationResult(
                TaskData.AutoEmailMagicContentData(
                  generated_subject = emailSubjectAndBody.subject,
                  generated_body = emailSubjectAndBody.htmlBody,
                  email_scheduled_text_body = emailSubjectAndBody.textBody,
                  email_scheduled_base_body = emailSubjectAndBody.baseBody
                ),
                CurrentStepStatusForSchedulerData.PendingApproval(pending_approval_at = srDateTimeUtils.getDateTimeNow()),
                TaskStatus.PendingApproval(due_at = srDateTimeUtils.getDateTimeNow())
              ))
            } else {
              Logger.debug("Auto step in Autopilot mode. Setting status to Approved.")
              Future.successful(TaskGenerationResult(
                TaskData.AutoEmailMagicContentData(
                  generated_subject = emailSubjectAndBody.subject,
                  generated_body = emailSubjectAndBody.htmlBody,
                  email_scheduled_text_body = emailSubjectAndBody.textBody,
                  email_scheduled_base_body = emailSubjectAndBody.baseBody
                ),
                CurrentStepStatusForSchedulerData.Approved(),
                TaskStatus.Approved(
                  done_at = srDateTimeUtils.getDateTimeNow(),
                  done_by = Some(email_setting_data.owner_id.id)
                )
              ))
            }


          case CampaignStepType.AutoEmailStep |
               CampaignStepType.ManualEmailStep |
               CampaignStepType.LinkedinConnectionRequest |
               CampaignStepType.LinkedinMessage |
               CampaignStepType.LinkedinInmail |
               CampaignStepType.LinkedinViewProfile |
               CampaignStepType.AutoLinkedinConnectionRequest |
               CampaignStepType.AutoLinkedinMessage |
               CampaignStepType.AutoLinkedinInmail |
               CampaignStepType.AutoLinkedinViewProfile |
               CampaignStepType.GeneralTask |
               CampaignStepType.WhatsappMessage |
               CampaignStepType.SmsMessage |
               CampaignStepType.CallStep |
               CampaignStepType.MoveToAnotherCampaignStep
          =>
            Logger.shouldNeverHappen(s"Impossible case:: Only Magic Content step type should be handled :: TeamId:  ${msg.teamId} StepId: ${msg.stepId} prospectId: ${msg.prospectId} :: settingId: ${msg.senderEmailSettingsId.emailSettingId} :: campaignId: ${msg.campaignId.id}")
            Future.failed(new Exception("Impossible case:: Only Magic Content step type should be handled "))

        }


        newTaskData: NewTask = NewTask(
          campaign_id = Some(msg.campaignId.id),
          campaign_name = Some(campaign.name), // Added campaign name
          step_id = Some(msg.stepId.id),
          step_label = None, // Added step label
          created_via = TaskCreatedVia.AiGenerated, // Use the newly added type
          is_opening_step = None, // Added is_opening_step
          task_type = taskType,
          is_auto_task = false, // Approval task is manual
          task_data = taskGenResult.taskData,
          status = taskGenResult.taskStatus, // Use new status
          assignee_id = Some(email_setting_data.owner_id.id), // Assign to the team member initiating?
          prospect_id = Some(msg.prospectId.id),
          priority = TaskPriority.Normal, // Assuming this exists
          emailsScheduledUuid = Some(emailsScheduledUuidForTask),
          note = None
        )

        // Call TaskService to create the task....
        taskIdEither: Either[CreateTaskError, String] <- taskService.createTask(
          task_data = newTaskData,
          accountId = msg.teamId.id, // Using teamId as accountId based on service signature
          teamId = msg.teamId.id
        )

        taskId: String <- taskIdEither match {
          case Right(id) => Future.successful(id)
          case Left(error) => Future.failed(new RuntimeException(s"Task creation failed: $error"))
        }

        affectedCount: Int <- Future.fromTry(campaignProspectDAO._updateScheduledStatus(Seq(
          CampaignProspectUpdateScheduleStatus(
            current_step_status_for_scheduler_data = taskGenResult.targetStatusData,
            current_step_type = stepType,
            current_step_task_id = taskId,
            step_id = msg.stepId.id,
            campaign_id = msg.campaignId.id,
            prospect_id = msg.prospectId.id,
            email_message_id = None,
            current_campaign_email_settings_id = Some(msg.campaignEmailSettingsId)
          )
        ))).flatMap { resultSeq =>
          if (resultSeq.length == 1) {
            val count = resultSeq.head
            if (count > 0) {
              Future.successful(count.toInt)
            } else if (count == 0) {
              Logger.warn(s"DB update for prospect ${msg.prospectId.id} affected 0 rows. Status might already be set.")
              Future.successful(0)
            } else { // count < 0
              val errorMsg = s"DB update for prospect ${msg.prospectId.id} returned unexpected negative count: $count"
              Logger.error(errorMsg)
              Future.failed(new RuntimeException(errorMsg))
            }
          } else {
            val errorMsg = s"DB update for prospect ${msg.prospectId.id} returned an unexpected result sequence: $resultSeq"
            Logger.error(errorMsg)
            Future.failed(new RuntimeException(errorMsg))
          }
        }

        _: Unit <- if (affectedCount > 0) {
          Logger.info(s"Updated prospect ${msg.prospectId.id} status to ${taskGenResult.targetStatusData}. Rows affected: $affectedCount")
          Future.successful(())
        } else {
          Logger.warn(s"Update prospect ${msg.prospectId.id} status call affected 0 rows. Status might have been already set to ${taskGenResult.targetStatusData}.")
          Future.successful(())
        }


      } yield (aiContentGenrationOutput, affectedCount)

      /**
       * What it does:
       * The `transformWith` method provides a direct way to handle both success and failure cases of a Future.
       * It takes a function that receives a Try[T] (either Success or Failure) and returns a new Future.
       * This allows pattern matching on both success and failure outcomes in a single expression.
       *
       * Why it is like this:
       * - It's more concise than using separate map/flatMap and recover combinations
       * - It ensures we handle both success and failure paths with appropriate audit log updates
       * - In the success case: update audit log to COMPLETED and return original result
       * - In the failure case: update audit log to PENDING and propagate the original exception
       * - This approach ensures audit logs are updated regardless of process outcome
       *
       * Why it's better:
       * - More idiomatic in functional Scala compared to imperative try/catch blocks
       * - Centralizes both success and error handling in a single pattern match
       * - Avoids nested combinators which can lead to callback hell (map.flatMap.recover.recoverWith...)
       * - Maintains type safety throughout both paths with compiler checking
       * - Clearer intent: explicitly handling both outcomes in one place
       * - Reduces cognitive load by keeping related logic together
       * - Better performance: avoids creating intermediate Future instances that would be created
       * when chaining multiple operations like map.flatMap.recover
       *
       * The pattern matching below handles two scenarios:
       * 1. Success: Updates audit log to COMPLETED and returns the original affectedRows
       * 2. Failure: Updates audit log to PENDING and propagates the original exception,
       * with special handling for failures during the audit log update itself
       */
      // Handle LlmAuditLog update based on the outcome of originalProcessFuture
      originalProcessFuture.transformWith {
        case Success((aiContentGenerationOutput, affectedRows)) =>
          Logger.info(s"AI content generation successful for logId ${msg.logId}. Updating LLM audit log to COMPLETED.")
          updateLlmAuditLog(
            logId = msg.logId.get,
            teamId = msg.teamId,
            status = ProspectColGenStatus.Completed,
            logContext = "AI content generation successful",
            aiContentGenerationOutput = Some(aiContentGenerationOutput),
            updateCount = true
          )
            .map(_ => affectedRows) // After audit log update, return original affectedRows

        case Failure(originalException) =>
          Logger.error(s"AI content generation failed for logId ${msg.logId}. Updating LLM audit log to PENDING. Error: ${originalException.getMessage}", originalException)
          val updateCount = originalException.getMessage.contains("AI generation failed:")
          updateLlmAuditLog(
            logId = msg.logId.get,
            teamId = msg.teamId,
            status = ProspectColGenStatus.Pending,
            logContext = "AI content generation failed",
            aiContentGenerationOutput = None,
            updateCount = updateCount
          )
            .flatMap { _ =>
              Future.failed(originalException) // Propagate original exception from content generation
            }
            .recoverWith { // This recoverWith handles failures from updateLlmAuditLog itself
              case NonFatal(auditUpdateException) =>
                Logger.error(s"Additionally, failed to update LLM audit log to PENDING for logId ${msg.logId}. Audit Error: ${auditUpdateException.getMessage}", auditUpdateException)
                Future.failed(originalException) // Still propagate the original failure from content generation
            }
      }
    } else {
      updateLlmAuditLog(
        logId = msg.logId.get,
        teamId = msg.teamId,
        status = ProspectColGenStatus.Pending,
        logContext = "Ai is locked",
        aiContentGenerationOutput = None,
        updateCount = false
      )
      Future.failed(MQDoNotNackException("The model is locked we will try again later"))
    }
  }

}