package utils.mq.srevents

import org.apache.pekko.actor.ActorSystem
import api.accounts.ReplyHandling.ReplyHandling
import api.accounts.TeamId
import api.accounts.models.AccountId
import api.campaigns.models.CampaignProspectCompletedReason
import api.campaigns.services.{CampaignProspectService2, CampaignProspectDAOService}
import api.campaigns.{CPCompleted, CPCompletedEvent, CPMarkAsCompleted, CampaignDAO, CampaignProspectDAO}
import api.emails.models.DeletionReason
import api.emails.{EmailReplySavedV3Basic, EmailReplyTrackingModel}
import api.prospects.models.ProspectId
import api.sr_audit_logs.models.{EventType, ProspectIdWithOldProspectDeduplicationColumn}
import play.api.libs.ws.WSClient
import utils.helpers.LogHelpers
import utils.mq.services.{MQConfig, MQService}
import utils.mq.webhook.mq_activity_trigger.{MQActivityTriggerMsgForm, MQActivityTriggerPublisher}
import utils.{Helpers, SRLogger}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

case class MQProspectCompletedEventMsg(
  accountId: Long,
  teamId: Long,
  campaignId: Long,
  campaignName: Option[String],
  prospectIds: Seq[Long],
  logRequestId: String
)


class MQProspectCompletedEvent(
  emailReplyTrackingModel: EmailReplyTrackingModel,
  campaignProspectDAO: CampaignProspectDAO,
  campaignProspectDAOService: CampaignProspectDAOService,
  campaignProspectService2: CampaignProspectService2,
  mqActivityTriggerPublisher: MQActivityTriggerPublisher,
  campaignDAO: CampaignDAO
) extends MQService[MQProspectCompletedEventMsg] {

  val queueBaseName: String = MQConfig.completedEventQueueBaseName
  val prefetchCount: Int = MQConfig.completedEventQueuePrefetchCount

  def publish(message: MQProspectCompletedEventMsg) = {
    publishMsg(message = message, queueBaseName = queueBaseName, prefetchCount = prefetchCount)
  }

  def startConsumer()(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem): Try[String] = {
    startMsgConsumer(queueBaseName = queueBaseName, prefetchCount = prefetchCount)
  }

  def publishCompletedProspects(
    accountId: Long,
    teamId: Long,
    cpCompleteds: Seq[CPCompletedEvent],
    reply_handling: ReplyHandling,
    isFromRepliedFlow: Boolean,
    permittedAccountIds: Seq[Long],
    completed_reason: CampaignProspectCompletedReason,
    SRLogger: SRLogger
  )(using Logger: SRLogger) = Try {


    // NOTE: if this is called from reply-tracking / mark-as-replied: then use CampaignProspect._markAsCompleted
    // else use: CampaignProspect.pauseStatusChangedByAdmin

    val markedAsCompleted: Seq[CPCompleted] = if (isFromRepliedFlow) {

      // here we are considering different REPLY_HANDLING cases,
      // unlike the sql used for just "Mark as completed" (pauseStatusChangedByAdmin) below
      campaignProspectService2._markAsCompleted(
        cpData = cpCompleteds.map(c => CPMarkAsCompleted(

          email_reply_status = None, // only from the reply tracking flow
          completed_because_email_message_id = None, // this is present only from replyTracking flow

          campaign_id = Some(c.campaignId),
          prospect_id = c.prospectId,
          prospect_account_id = None //need to pass account id also to cover all cases including prospect account related
        )),
        replyHandling = reply_handling,
        teamId = teamId,
        SRLogger = SRLogger,
        /*
          24-jan-2024:
            isHardBounceFlow is set to false as this is batch action api path and user is doing it, so
            no change is done here with this flow.
        */
        isHardBounceFlow = false,
        completed_reason = completed_reason
      )
        .get

    } else {

      // in this sql, we ignore the replyHandling aspect, unlike the above case
      campaignProspectDAOService.pauseStatusChangedByAdmin(
        permittedAccountIds = permittedAccountIds,
        statusChangedByAccountId = AccountId(accountId),
        teamId = teamId,
        prospectEmails = Seq(),
        prospectIds = cpCompleteds.map(c => c.prospectId),
        newPauseStatus = true,
        campaignIds = cpCompleteds.map(c => c.campaignId),
        Logger = SRLogger
      )
        .get

    }


    // delete any unsent emails
    val campaignAndProspectIdsForDeleteUnsent = cpCompleteds.map(r => EmailReplySavedV3Basic(
      campaign_id = Some(r.campaignId),
      prospect_id_in_campaign = Some(r.prospectId),
      prospect_account_id_in_campaign = None //fixme inboxv3 status: in future we need to add prospect_account id also
    ))

    emailReplyTrackingModel._deleteUnsentByProspectIdsOnRepliesV3(
      emailsReceived = campaignAndProspectIdsForDeleteUnsent,
      replyHandling = reply_handling,
      teamId = TeamId(id = teamId),
      deletion_reason = DeletionReason.PublishCompletedProspects
    )


    val msgsForEachCampaign = markedAsCompleted
      .groupBy(_.campaignId)
      .map(r => {
        campaignDAO.getCampaignBasicInfo(
          teamId = teamId,
          cId = r._1
        ) match {
          case Failure(e) =>
            Logger.shouldNeverHappen(s"MQProspectCompletedEvent.publishCompletedProspects CampaignDAO.getCampaignBasicInfo failed to get campaignBasicInfo :: aid_$accountId :: cid_${r._1} :: tid_$teamId", err = Some(e))
            None
          case Success(None) =>
            Logger.shouldNeverHappen(s"MQProspectCompletedEvent.publishCompletedProspects CampaignDAO.getCampaignBasicInfo returned None :: aid_$accountId :: cid_${r._1} :: tid_$teamId")
            None
          case Success(Some(campaignBasicInfo)) =>
            Some(MQProspectCompletedEventMsg(
              accountId = accountId,
              teamId = teamId,
              campaignId = r._1,
              campaignName = Some(campaignBasicInfo.name),
              prospectIds = r._2.map(_.prospectId).distinct,
              logRequestId = SRLogger.logRequestId
            ))
        }
      })
      .filter(_.nonEmpty)
      .map(_.get)
      .filter(_.prospectIds.nonEmpty)
      .flatMap(r => {
        r.prospectIds
          .grouped(100)
          .map(groupedProspectIds => r.copy(prospectIds = groupedProspectIds))
      })
      .toSeq

    msgsForEachCampaign.foreach(msg => publish(message = msg).get)
  }

  def processMessage(msg: MQProspectCompletedEventMsg)(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem): Future[Unit] = Future {

    val logRequestId = msg.logRequestId
    val Logger = new SRLogger(logRequestId = s"$logRequestId")

    val message = MQActivityTriggerMsgForm(
      event = EventType.PROSPECT_COMPLETED.toString,
      accountId = msg.accountId,
      teamId = msg.teamId,
      campaignId = Some(msg.campaignId),
      prospectIds = msg.prospectIds.map(pid => ProspectIdWithOldProspectDeduplicationColumn(prospectId = ProspectId(pid), oldProspectDeduplicationColumn = None)),
      campaignName = msg.campaignName,
      emailScheduledIds = None,
      clickedUrl = None,
      threadId = None,
      replySentimentUuid = None
    )
    if (msg.prospectIds.nonEmpty) {
      mqActivityTriggerPublisher.publishEvents(message = message)
    }
  }

}

