package utils.mq.srevents

import api.accounts.models.AccountId
import api.campaigns.services.CampaignProspectDAOService
import org.apache.pekko.actor.ActorSystem
import api.campaigns.{CPUnPausedEvent, CampaignProspectDAO}
import play.api.libs.ws.WSClient
import utils.SRLogger
import utils.mq.services.{MQConfig, MQService}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.Try

case class MQProspectUnPauseEventMsg(
  accountId: Long,
  teamId: Long,
  campaignId: Long,
  campaignName: Option[String],
  prospectIds: Seq[Long],
  logRequestId: String
)


class MQProspectUnPauseEvent(
  campaignProspectDAOService: CampaignProspectDAOService
) extends MQService[MQProspectUnPauseEventMsg] {

  val queueBaseName: String = MQConfig.unpausedEventQueueBaseName
  val prefetchCount: Int = MQConfig.unpausedEventQueuePrefetchCount

  def publish(message: MQProspectUnPauseEventMsg) = {
    publishMsg(message = message, queueBaseName = queueBaseName, prefetchCount = prefetchCount)
  }

  def startConsumer()(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem): Try[String] = {
    startMsgConsumer(queueBaseName = queueBaseName, prefetchCount = prefetchCount)
  }

  def publishUnpauseProspects(
                               accountId: Long,
                               teamId: Long,
                               permittedAccountIds: Seq[Long],
                               cpUnpaused: Seq[CPUnPausedEvent],
                               Logger: SRLogger
  ) = Try {

    val cpUnpausedUpdated = campaignProspectDAOService.pauseStatusChangedByAdmin(
      permittedAccountIds = permittedAccountIds,
      statusChangedByAccountId = AccountId(accountId),
      teamId = teamId,
      prospectEmails = Seq(), // if prospectEmails is there, check by that and ignore prospectIds
      prospectIds = cpUnpaused.map(c => c.prospectId),
      newPauseStatus = false,
      campaignIds = cpUnpaused.map(c => c.campaignId),
      Logger = Logger
    ).get

    /*
    NOTE: not calling publish for now, because we dont have anything happening in processMessage now

    val msgsForEachCampaign = cpUnpausedUpdated
      .groupBy(_.campaignId)
      .map(p => {
        MQProspectUnPauseEventMsg(
          accountId = accountId,
          teamId = teamId,
          campaignId = p._1,
          campaignName = None,
          prospectIds = p._2.map(_.prospectId).distinct,
          logRequestId = logRequestId
        )
      })
      .filter(_.prospectIds.nonEmpty)
      .toSeq

    msgsForEachCampaign.foreach(msg => publish(message = msg).get)
    */
  }


  // FIXME status: add ProspectEvent in this processMessage
  def processMessage(msg: MQProspectUnPauseEventMsg)(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem): Future[Int] = {

    val logRequestId = msg.logRequestId
    val Logger = new SRLogger(logRequestId = s"$logRequestId")

    // fixme status: add ProspectEvent

    Future.successful(0)


  }

}

