package utils.mq

import org.apache.pekko.actor.ActorSystem
import api.AppConfig
import api.accounts.TeamId
import api.phantombuster.PhantomBusterService
import play.api.libs.ws.WSClient
import utils.SRLogger
import utils.mq.services.{MQConfig, SimpleMqServiceTrait}

import scala.concurrent.{ExecutionContext, Future}

case class MqLinkedinInboxScraperMsg(
                                      uuid: String,
                                      teamId: TeamId
                                    )

class MqLinkedinInboxScraper(phantomBusterService: PhantomBusterService) extends SimpleMqServiceTrait[MqLinkedinInboxScraperMsg] {

  override val queueBaseName: String = MQConfig.linkedinInboxScraperQueueName
  override val prefetchCount: Int = MQConfig.linkedinInboxScraperPrefetchCount

  override def processMessage(msg: MqLinkedinInboxScraperMsg)(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem): Future[Any] = {
    
    implicit lazy val logger: SRLogger = new SRLogger(logRequestId = s"MqLinkedinInboxScraper :: processMessage :: $msg :: ")
    
    phantomBusterService.startLinkedinInboxScraping(
        teamId = msg.teamId,
        linkedinSettingUuid = msg.uuid,
        maxThreadsToScrape = AppConfig.Linkedin_Automation.maxLinkedinThreadsToScrapeByCron
      )
      .recover {
        case e =>
          logger.error(e.getMessage, e)
      }
    
  }

}
