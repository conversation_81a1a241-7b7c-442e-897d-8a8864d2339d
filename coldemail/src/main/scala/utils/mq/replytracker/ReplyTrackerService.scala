package utils.mq.replytracker

import api.{CacheServiceJedis, RedisConnectionIssue}
import utils.SRLogger

import scala.util.{Failure, Success, Try}

class ReplyTrackerService(cacheServiceJedis: CacheServiceJedis) {

  def acquireLockOnInboxForRead( esId : Long, SRLogger: SRLogger  ): Try[Boolean] = {
    val lock_name = s"mqreplytracker:email_${esId}"
    cacheServiceJedis.acquireLock(
      cacheKey = lock_name,
      expireInSeconds = 180
    ) match {
      case Failure(e) =>

        SRLogger.fatal(s" acquireLockOnInboxForRead ERROR  - RedisConnIssue - failure for es_${esId} ")
        Failure(
          RedisConnectionIssue(message = "RedisConnIssue", cause = e)
        )


      case Success(v) =>
        SRLogger.info(s"acquireLockOnInboxForRead SUCCESS - for es_${esId} ")
        Success(v)

    }
  }

  def releaseLockOnInboxForRead( esId : Long, SRLogger: SRLogger  ): Try[Boolean] = {
    val lock_name = s"mqreplytracker:email_${esId}"
    cacheServiceJedis.releaseLock(
      cacheKey = lock_name) match {
      case Success(value) =>
        //SRLogger.info(s"releaseLockOnInboxForRead Successfully released lock on es_${esId}")
        Success(value)
      case Failure(e) =>
        SRLogger.error(s"releaseLockOnInboxForRead Unable to release lock es_${esId}")
        Failure(e)
    }
  }

}
