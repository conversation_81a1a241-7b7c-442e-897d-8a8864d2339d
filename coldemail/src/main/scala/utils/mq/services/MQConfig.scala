package utils.mq.services

import api.accounts.email.models
import api.accounts.email.models.ReplyTrackerName
import api.emails.models.EmailServiceProviderSendEmail
import com.typesafe.config.{Config, ConfigFactory}
import org.apache.commons.validator.routines.InetAddressValidator
import play.api.Logging

import scala.util.{Failure, Success, Try}

object MQConfig extends Logging {
  val config: Config = ConfigFactory.load()

  val host: String = config.getString("rabbitmq.host")
  val username: String = config.getString("rabbitmq.username")
  val password: String = config.getString("rabbitmq.password")
  val virtualHost: String = config.getString("rabbitmq.virtualHost")

  val prefix: String = config.getString("rabbitmq.prefix") + "."

  val openTrackerQueueBaseName: String = "openTracker"
  val openTrackerPrefetchCount: Int = 5

  val affiliateAutoApprovalQueueBaseName: String = "affiliateAutoApproval"
  val affiliateAutoApprovalPrefetchCount: Int = 1


  val zapmailMailboxCreationQueueBaseName: String = "zapmailMailboxCreation"
  val zapmailMailboxCreationPrefetchCount: Int = 1

  val captainDataConversationExtractionQueueBaseName: String = "captainDataConversationExtraction"
  val captainDataConversationExtractionPrefetchCount: Int = 1

  val captainDataMessageExtractionQueueBaseName = "captainDataMessageExtraction"
  val captainDataMessageExtractionPrefetchCount = 1
  
  val captainDataCookieFailureQueueBaseName = "captainDataCookieFailure"
  val captainDataCookieFailurePrefetchCount = 1


  val clickTrackerQueueBaseName: String = "clickTracker"
  val clickTrackerPrefetchCount: Int = 1

  val unsubscribeTrackerQueueBaseName: String = "unsubscribeTracker"
  val unsubscribeTrackerPrefetchCount: Int = 1

  val doNotContactQueueBaseName: String = "doNotContact"
  val doNotContactPrefetchCount: Int = 10
  
  val autoUpdateProspectCategoryQueueBaseName: String = "autoUpdateProspectCategory"
  val autoUpdateProspectCategoryPrefetchCount: Int = 1

//  val replyTrackerQueueBaseName: String = "replyTracker"
  val replyTrackerPrefetchCount: Int = 20

  def getReplyTrackerPrefetchCount(replyTrackerName: ReplyTrackerName): Int = {
    replyTrackerName match {
      case models.ReplyTrackerName.GmailApi => 20
      case models.ReplyTrackerName.GmailAsp => 20
      case models.ReplyTrackerName.Outlook => 20
      case models.ReplyTrackerName.OtherMailDoso => 20
      case models.ReplyTrackerName.OtherZapmail => 20
      case models.ReplyTrackerName.OtherUser => 100
    }
  }

  val listStatsUpdaterQueueBaseName: String = "listStatsUpdater"
  val listStatsUpdaterPrefetchCount: Int = 2

  val emailChannelSchedulerQueueBaseName: String = "emailScheduler"
  val emailChannelSchedulerPrefetchCount: Int = 10

  val campaignSendReportQueueBaseName: String = "campaignSendReport"
  val campaignSendReportPrefetchCount: Int = 10

  val independentStepSchedulerBaseName: String = "independentStepScheduler"
  val independentStepSchedulerPrefetchCount: Int = 10

  val campaignSendVolumeReportQueueBaseName: String = "campaignSendVolumeReport"
  val campaignSendVolumeReportPrefetchCount: Int = 2

  val generalChannelSchedulerQueueBaseName: String = "generalScheduler"
  val generalChannelSchedulerPrefetchCount: Int = 10

  val aiContentGenerationQueueBaseName : String = "aiContentGeneration"
  val aiContentGenerationQueuePrefetchCount : Int = 1

  val linkedinChannelSchedulerQueueBaseName: String = "linkedinScheduler"
  val linkedinChannelSchedulerPrefetchCount: Int = 10

  val spamIPBlacklistCheckQueueBaseName: String = "spamIPBlacklistCheck"
  val spamIPBlacklistCheckPrefetchCount: Int = 1  
  
  val serverReportCheckQueueBaseName: String = "serverReportCheck"
  val serverReportCheckPrefetchCount: Int = 1

  val insertTeamsMetadataQueueBaseName: String = "insertTeamsMetadata"
  val insertTeamsMetadataPrefetchCount: Int = 1

  val spamMonitorQueueBaseName: String = "spamMonitor"
  val spamMonitorPrefetchCount: Int = 1

  val campaignSchedulingMetadataMigrationQueueBaseName: String = "MqCampaignSchedulingMetadataMigration"
  val campaignSchedulingMetadataMigrationPrefetchCount: Int = 1

  val updateCampaignSchedulingMetadataQueueBaseName: String = "MQUpdateCampaignSchedulingMetadata"
  val updateCampaignSchedulingMetadataPrefetchCount: Int = 1


  val orgDataDeleterQueue: String = "orgDataDeleter"
  val orgDataDeleterPrefetchCount: Int = 10

  val mqSrAiApi: String = "MqSrAiApi"
  val mqSrAiApiPrefetchCount: Int = 10

  val campaignSpamMonitorServiceBaseName: String = "MqCampaignSpamMonitorService"
  val campaignSpamMonitorServicePrefetchCount: Int = 5

  val unSnoozeQueueBaseName: String = "MQUnSnoozingSnoozedTasks"
  val unSnoozeTasksPrefetchCount: Int = 10

  val readOldEmailForSyncBaseName: String = "MQReadOldEmailForSync"
  val readOldEmailForSyncPrefetchCount: Int = 5

  val whatsAppChannelSchedulerQueueBaseName: String = "whatsAppScheduler"
  val whatsAppChannelSchedulerPrefetchCount: Int = 20

  val smsChannelSchedulerQueueBaseName: String = "smsScheduler"
  val smsChannelSchedulerPrefetchCount: Int = 20

  val callChannelSchedulerQueueBaseName: String = "callScheduler"
  val callChannelSchedulerPrefetchCount: Int = 20

  val campaignsProspectsMigrationScriptQueueBaseName: String = "campaignsProspectsMigrationScript"
  val campaignsProspectsMigrationScriptPrefetchCount: Int = 3

  val prospectsContactedLimitCheckQueueBaseName: String = "prospectsContactedLimitCheck"
  val prospectsContactedLimitCheckQueuePrefetchCount: Int = 5


  val uuidMigrationScriptQueueBaseName: String = "uuidMigrationScript"
  val uuidMigrationScriptPrefetchCount: Int = 5

  val updateUuidMigrationScriptQueueBaseName: String = "updateUuidMigrationScript"
  val updateUuidMigrationScriptPrefetchCount: Int = 3

  val dncTypeMigrationScriptQueueBaseName: String = "dncTypeMigrationScript"
  val dncTypeMigrationScriptPrefetchCount: Int = 5

  val updateProspectCategoryForGivenTeamOfOrgForGlobalDNCQueueBaseName: String = "updateProspectCategoryForGivenTeamOfOrgForGlobalDNC"
  val updateProspectCategoryForGivenTeamOfOrgForGlobalDNCPrefetchCount: Int = 10

  val updatedAtMigrationScriptQueueBaseName: String = "updatedAtMigrationScript"
  val updatedAtScriptPrefetchCount: Int = 3

  val associateProspectQueueBaseName: String = "associateProspectToOldEmails"
  val associateProspectPrefetchCount: Int = 10

  val preEmailValidationQueueBaseName: String = "MQPreEmailValidation"
  val preEmailValidationPrefetchCount: Int = 1

  val campaignAISequenceGeneratorQueueBaseName: String = "MQCampaignAISequence"
  val campaignAISequenceGeneratorPrefetchCount: Int = 5

  val internalAdoptionReportQueueBaseName: String = "MqInternalAdoptionReport"
  val internalAdoptionReportPrefetchCount: Int = 1

  val updateSubAccountCallingCreditQueueBaseName: String = "MqUpdateSubAccountCallingCredit"
  val updateSubAccountCallingCreditPrefetchCount: Int = 1

  val updateSubAccountCallHistoryQueueBaseName: String = "MqUpdateSubAccountCallHistory"
  val updateSubAccountCallHistoryPrefetchCount: Int = 1

  val unsnoozeSnoozedEmailsQueueBaseName: String = "MQUnsnoozeSnoozedEmails"
  val unsnoozeSnoozedEmailsPrefetchCount: Int = 10

  val updateLatestTaskDoneOrSkippedAtQueueBaseName: String = "mqUpdateLatestTaskDoneOrSkippedAt"
  val updateLatestTaskDoneOrSkippedAtPrefetchCount: Int = 1

  val resetCacheForReportQueueBaseName: String = "mqResetCacheForReport"
  val resetCacheForReportPrefetchCount: Int = 1

  val internalTeamBasedMigrationBaseName: String = "MqInternalTeamBasedMigration"
  val internalTeamBasedMigrationPrefetchCount: Int = 3

  val domainServiceProviderDNSServiceBaseName: String = "MQDomainServiceProviderDNSService"
  val domainServiceProviderDNSServicePrefetchCount: Int = 1


  val schedulerIntegrityCheckQueueBaseName: String = "MqSchedulerIntegrityCheck"
  val schedulerIntegrityCheckPrefetchCount: Int = 10

  val deletionAndRevertV2QueueBaseName: String = "MqDeletionAndRevertV2"
  val deletionAndRevertV2PrefetchCount: Int = 10

  val findPotentialDuplicateProspectsBaseName: String = "MqFindPotentialDuplicateProspects"
  val findPotentialDuplicateProspectsPrefetchCount: Int = 2

  val callNotesMigrationScriptQueueBaseName: String = "callNotesMigrationScript"
  val callNotesMigrationScriptPrefetchCount: Int = 1

  def getEmailQueueBaseName(ip: String, emailServiceProviderSendEmail: EmailServiceProviderSendEmail): String = {

    val postfix: String = emailServiceProviderSendEmail match {
      case EmailServiceProviderSendEmail.OUTLOOK => "_outlook"
      case EmailServiceProviderSendEmail.OTHER => ""
    }

    val baseName = "sendEmail_" + ip + postfix


    Try {
      InetAddressValidator.getInstance().isValid(ip)
    } match {
      case Failure(e) =>
        logger.error(s"FATAL MQConfig.getEmailQueueBaseName checking validity [1] $ip :: $e")

        baseName

      case Success(isValid) =>

        if (!isValid) {
          logger.error(s"FATAL MQConfig.getEmailQueueBaseName checking validity [2] $ip :: Address is not valid :: ignoring")

          baseName

        } else {
          baseName
        }

    }

  }

  def getSendEmailForInboxPlacementCheckQueueBaseName(rep_mail_server_id: Option[Long]) = {

    rep_mail_server_id match {
      case Some(id) =>
        "mqSendEmailForInboxPlacementCheck_" + id
      case None =>
        "mqSendEmailForInboxPlacementCheck"

    }


  }
  val emailPrefetchCount: Int = 25
  /** TODO: increase thread pool size gradually to match prefetchCount
    *
    * 21-Mar-2024: we need to increase the number threads to this mq to atleast match the prefetchCount
    *
    * Instead of increasing it from 5 to 25 (current prefetch count) in one shot,
    * we are first increasing it from 5 to 10 on 21-Mar, and then will gradually increase it over time
    *
    * While investigating a customer issue, we noticed sometimes there is a gap of 10-15 minutes in email sending relative
    * to email-scheduled time. We suspect it could be related to lack of processing threads on this sending queue.
    */
  val senderWorkerProcessThreadPoolSize: Int = 10


  val dueLinkedinTasksQueueName = "executeDueLinkedinTasksQueue"
  val dueLinkedinPrefetchCount = 10

  /*
  val emailValidatorMBLQueue: String = "emailValidatorMBLQueue"
  val emailValidatorMBLPrefetchCount: Int = 10
  */

  val prospectDeleterQueueBaseName: String = "prospectDeleterQueue"
  val prospectDeleterPrefetchCount: Int = 3

  val emailAccountDeleterQueueBaseName: String = "emailAccountDeleterQueue"
  val emailAccountDeleterPrefetchCount: Int = 1

  val campaignStepContentCheckQueueName: String = "campaignStepContentCheckQueue"
  val campaignStepContentCheckPrefetchCount: Int = 5

  /*
  val campaignDeleterQueueBaseName: String = "campaignDeleterQueue"
  val campaignDeleterPrefetchCount: Int = 3
  */

  // webhook queues
  val webhookEmailInvalidQueueBaseName: String = "webhookEmailInvalidQueue"
  val webhookEmailInvalidPrefetchCount: Int = 3


  val webhookProspectCategoryUpdatedQueueBaseName: String = "webhookProspectCategoryUpdatedQueue"
  val webhookProspectCategoryUpdatedPrefetchCount: Int = 3

  val webhookCompletedQueueBaseName: String = "webhookCompletedQueue"
  val webhookCompletedPrefetchCount: Int = 3

  val linkedinInboxScraperQueueName = "LinkedinInboxScraperQueue"
  val linkedinInboxScraperPrefetchCount = 5

  val linkedinMessageThreadScraperQueueName = "LinkedinMessageThreadScraperQueue"
  val linkedinMessageThreadScraperPrefetchCount = 5

  val completedEventQueueBaseName: String = "completedEventQueue"
  val completedEventQueuePrefetchCount: Int = 3

  val unpausedEventQueueBaseName: String = "unpausedEventQueue"
  val unpausedEventQueuePrefetchCount: Int = 3

  val repliedEventQueueBaseName: String = "repliedEventQueue"
  val repliedEventQueuePrefetchCount: Int = 3

  val webhookTriggerQueueBaseName: String = "webhookTriggerQueue"
  val webhookTriggerPrefetchCount: Int = 25


  val intercomUpdaterQueueBaseName: String = "intercomUpdaterQueue"
  val intercomUpdaterQueuePrefetchCount: Int = 1

  val linkedinRecreateSessionQueueBaseName: String = "linkedinRecreateSession"
  val linkedinRecreateSessionPrefetchCount: Int = 1



  /*Activity activity workflow queues*/

  val workflowActivityToActivity: String = "workflowActivityToActivity"
  val workflowActivityToActivityPrefetchCount: Int = 5

  val handleEventLog: String = "handleEventLog"
  val handleEventLogPrefetchCount: Int = 20
  val handleEventLogLogIfQueueSizeAbove = 25000

  val newProcessWorkflowAttemptServiceQueueBaseName: String = "MQNewProcessWorkflowAttemptService"
  val newProcessWorkflowAttemptServicePrefetchCount: Int = 10


  val stopInactiveCampaignQueueBaseName: String = "MqStopInactiveCampaign"
  val stopInactiveCampaignPrefetchCount: Int = 10

  val opportunitiesSetupDefaultDataBaseName: String = "MqOpportunitiesSetupDefaultData"
  val opportunitiesSetupDefaultPrefetchCount: Int = 1

  val processAndGenerateMagicColumnBaseName: String = "MqProcessAndGenerateMagicColumn"
  val processAndGenerateMagicColumnPrefetchCount: Int = 1

  val processDripCampaignHealthCheckBaseName: String = "MqDripCampaignHealthCheck"
  val processDripCampaignHealthCheckPrefetchCount: Int = 1

  val sendTestEmailForInboxPlacementCheckBaseName: String = "MqSendEmailForInboxPlacementCheck"
  val sendTestEmailForInboxPlacementPrefetchCount: Int = 1

  val analyzeInboxPlacementCheckEmailBaseName: String = "MqAnalyzeInboxPlacementCheckEmail"
  val analyzeInboxPlacementCheckEmailPrefetchCount: Int = 1

  val mergeDuplicateProspectsBaseName: String = "MqMergeDuplicateProspects"
  val mergeDuplicateProspectsPrefetchCount: Int = 1

  val domainHealthCheckBaseName: String = "MqDomainHealthBlacklistCheck"
  val domainHealthCheckPrefetchCount: Int = 3

  val domainCnameUptimeBaseName: String = "MqCnameUptimeCheck"
  val domainCnameUptimeCheckPrefetchCount: Int =10
  
  val customTrackingDomainRecordDeleterBaseName: String = "MqInactiveDomainCustomTrackingDeleter"
  val customTrackingDomainRecordDeleterPrefetchCount: Int = 1
  
  val emailHealthCheckBaseName: String = "MqEmailHealthCheck"
  val emailHealthCheckPrefetchCount: Int = 1


  val purchasedDomainsDeleterBaseName: String = "MqPurchasedDomainsDeleter"
  val purchasedDomainsDeleterPrefetchCount: Int = 1

  val purchasedEmailsDeleterBaseName: String = "MqPurchasedEmailsDeleter"
  val purchasedEmailsDeleterPrefetchCount: Int = 1

  val internalLeadFinderDataUpdate: String = "MqInternalLeadFinderData"
  val internalLeadFinderDataUpdatePrefetchCount: Int = 1
  
  
  val manualEmailTaskStatusUpdate: String = "MqUpdateManualEmailTaskStatus"
  val manualEmailTaskStatusUpdaterPrefetchCount: Int = 1

  val handlePauseCampaignOnReplySentimentSelect: String = "MqPauseCampaignOnReplySentimentSelect"
  val handlePauseCampaignOnReplySentimentSelectPrefetchCount: Int = 1

  val handleMqExtractLinkedinConnectionResults: String = "MqExtractLinkedinConnectionResults"
  val handleMqExtractLinkedinConnectionResultsPrefetchCount: Int = 1

  val handleMqLinkedinSettingDeletion: String = "MqLinkedinSettingDeletion"
  val handleMqLinkedinSettingDeletionPrefetchCount: Int = 1


    val handleSaveReplySentiment: String = "MqSaveReplySentiment"
    val handleSaveReplySentimentPrefetchCount: Int = 1

    val handleAttemptCreation: String = "MqHandleAttemptCreation"
  val handleAttemptCreationPrefetchCount: Int = 1

  val exchangeType: String = config.getString("rabbitmq.exchangeType")
  val durable: Boolean = config.getBoolean("rabbitmq.durable")
  val autoAck: Boolean = config.getBoolean("rabbitmq.autoAck")
  val prefetchCount: Int = config.getInt("rabbitmq.prefetchCount")
  val connectionTimeout: Int = config.getInt("rabbitmq.connectionTimeout")
}
