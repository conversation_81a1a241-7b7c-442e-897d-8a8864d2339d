package utils.mq.services

import com.rabbitmq.client.{Channel, MessageProperties}
import org.apache.commons.lang3.SerializationUtils
import org.joda.time.{DateTime, Duration}
import play.api.Logging
import utils.helpers.LogHelpers

import scala.concurrent.blocking
import scala.util.{Failure, Success, Try}

trait MQPublisherService[T <: Serializable] extends Logging {

  // PUBLISHER //

  // alert for queue size //
  val logIfQueueSizeAbove: Int = 500

  private val timeForCheckingQueueSizeInSeconds: Int = 300

  private var sizeLastCheckedAt: Option[DateTime] = None

  // Option[Channel]
  private var channel: Option[Channel] = None

  private def checkIfWeCheckQueueSizeAndLog(channel: Channel, queueName: String): Unit = {

    sizeLastCheckedAt match {
      case None =>

        sizeLastCheckedAt = Some(DateTime.now())

        logIfQueueSizeIsAboveThreshold(channel = channel, queueName = queueName)

      case Some(value) =>
        val timeSinceLastCheckInSeconds: Long = new Duration(value, DateTime.now()).getStandardSeconds
        if (timeSinceLastCheckInSeconds > timeForCheckingQueueSizeInSeconds) {
          sizeLastCheckedAt = Some(DateTime.now())

          logIfQueueSizeIsAboveThreshold(channel = channel, queueName = queueName)
        }

    }

  }

  private def logIfQueueSizeIsAboveThreshold(channel: Channel, queueName: String): Unit = Try {
    //https://www.rabbitmq.com/api-guide.html#passive-declaration
    val response = channel.queueDeclarePassive(queueName)
    val count = response.getMessageCount
    count
  } match {
    case Success(value) =>
      if (value > logIfQueueSizeAbove) {
        logger.warn(s"[MQService] $queueName - Queue length above $logIfQueueSizeAbove! length - $value")
      }
    case Failure(exception) =>
      logger.error(s"[MQService] $queueName - Error while getting queue length ${LogHelpers.getStackTraceAsString(exception)}")
  }

  final def publishMsg(
    message: T,
    queueBaseName: String,
    prefetchCount: Int
  ): Try[Unit] = blocking {

    Try {

      val prefix = MQConfig.prefix

      val queueName = s"$prefix$queueBaseName"
      val exchangeName = s"$prefix${queueBaseName}ExchangeName"
      val routingKey = s"$prefix${queueBaseName}RoutingKey"
      // val consumerTag = s"$prefix${queueBaseName}ConsumerTag"

//      logger.info(s"[MQService] publish 1: with $queueName :: $message")


      // This one is not for human consumption
      // looks like this : coldemailProduction.webhookTriggerQueue :: [B@40922fbe
      //logger.info(s"[MQService] publish 2: with $queueName :: ${SerializationUtils.serialize(message)}")

      if (channel.isEmpty) {

        MQConnection.getChannel(queueName, exchangeName, routingKey, prefetchCount = prefetchCount) match {

          case Failure(e) =>
            logger.error(s"MQService FATAL error while creating channel: $queueName ::: ${LogHelpers.getStackTraceAsString(e)}")
            throw e

          case Success(ch) =>

            channel = Some(ch)

        }

      }

      if (channel.isEmpty) {

        logger.error(s"MQService fatal no channel: $queueName")

      } else {
        checkIfWeCheckQueueSizeAndLog(channel = channel.get, queueName = queueName)

        channel.get.basicPublish(
          exchangeName,
          routingKey,
          true,
          MessageProperties.PERSISTENT_TEXT_PLAIN,
          SerializationUtils.serialize(message)
        )

      }

    }

  }

}
