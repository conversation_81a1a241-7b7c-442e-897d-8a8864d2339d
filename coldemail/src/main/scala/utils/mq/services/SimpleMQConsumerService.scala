package utils.mq.services

import org.apache.pekko.actor.ActorSystem
import play.api.libs.ws.WSClient

import scala.concurrent.ExecutionContext
import scala.util.Try

trait SimpleMQConsumerService[T <: Serializable] extends MQConsumer[T] {
  val queueBaseName: String
  val prefetchCount: Int
  def startConsumer()(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem): Try[String] = {
    startMsgConsumer(queueBaseName = queueBaseName, prefetchCount = prefetchCount)
  }
}
