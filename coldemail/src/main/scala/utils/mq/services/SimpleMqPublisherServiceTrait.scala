package utils.mq.services

import scala.util.Try

trait SimpleMqPublisherServiceTrait[T <: Serializable]
  extends MQPublisherService[T] {

  val queueBaseName: String
  val prefetchCount: Int

  def publish(
               msg: T,
               baseName: String = queueBaseName,
               prefetch: Int = prefetchCount
             ): Try[Unit] = {

    publishMsg(
      message = msg,
      queueBaseName = baseName,
      prefetchCount = prefetch
    )
  }

}
