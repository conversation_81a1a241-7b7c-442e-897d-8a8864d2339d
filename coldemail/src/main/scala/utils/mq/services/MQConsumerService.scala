package utils.mq.services

import org.apache.pekko.actor.ActorSystem
import com.rabbitmq.client._
import org.apache.commons.lang3.SerializationUtils
import play.api.Logging
import play.api.libs.ws.WSClient
import utils.helpers.LogHelpers

import java.io.{PrintWriter, StringWriter}
import scala.annotation.nowarn
import scala.concurrent.{ExecutionContext, Future}
import scala.util.control.NonFatal
import scala.util.{Failure, Success, Try}

trait MQConsumerService[T <: Serializable] extends Logging {

  // CONSUMER //
  def publishMessage(
                      message: T, 
                      queueBaseName: String, 
                      prefetchCount: Int
                    ): Try[Unit]

  protected final def startMsgConsumer(
                                        queueBaseName: String,
                                        prefetchCount: Int
                                      )(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem): Try[String] = {

    val prefix = MQConfig.prefix

    val queueName = s"$prefix$queueBaseName"
    val exchangeName = s"$prefix${queueBaseName}ExchangeName"
    val routingKey = s"$prefix${queueBaseName}RoutingKey"
    val consumerTag = s"$prefix${queueBaseName}ConsumerTag"

    logger.info(s"[MQService] startConsumer: $queueName - $exchangeName - $routingKey - $prefetchCount")

    MQConnection.getChannel(queueName, exchangeName, routingKey, prefetchCount = prefetchCount).flatMap {
      channel =>

        val consumerCallback = getConsumerCallback(
          channel = channel,
          queueName = queueName,
          queueBaseName = queueBaseName,
          channelConsumerTag = consumerTag,
          prefetchCount = prefetchCount,
          processMessage = processMessage
        )

        basicConsume(channel, queueName, consumerTag, consumerCallback).map {
          consumerTag =>

            logger.info(s"[MQService] startConsumer started: $queueName")

            consumerTag

        }

    }


  }

  @nowarn("msg=abstract type pattern T is unchecked since it is eliminated by erasure")
  private final def getConsumerCallback(

                                         channel: Channel,
                                         queueBaseName: String,
                                         queueName: String,
                                         channelConsumerTag: String,
                                         prefetchCount: Int,
                                         processMessage: (T) => Future[Any]

                                       )(implicit ws: WSClient, ec: ExecutionContext): DefaultConsumer = new DefaultConsumer(channel: Channel) {

    override def handleDelivery(consumerTag: String, envelope: Envelope, properties: AMQP.BasicProperties, body: Array[Byte]) = Try {

      // log in failure case
      //logger.info(s"[MQService] handleDelivery: queueName: $queueName , consumerTag: $consumerTag")

      val deliveryTag: Long = envelope.getDeliveryTag
      Try {
        SerializationUtils.deserialize[T](body)
      } match {
        case Failure(e) =>
          logger.error(s"[MQService] [FATAL] handleDelivery SerializationUtils.deserialize ERROR: queueName $queueName, deliveryTag: $deliveryTag :: ${LogHelpers.getStackTraceAsString(e)}")

          // DO NOT REQUE
          channel.basicNack(deliveryTag, false, false)


        /*
           SBT_WARNING FIXME:
           [warn] /Users/<USER>/Documents/projects/coldemail/coldemail/src/main/scala/utils/mq/MQService.scala:166:29: abstract type pattern T is unchecked since it is eliminated by erasure
[warn]         case Success(mqMsg: T) =>
           */
        case Success(mqMsg: T) =>

          if (channelConsumerTag != consumerTag) {

            logger.error(s"[MQService] [NO REQUE] handleDelivery: queueName $queueName : incorrect consumerTag: $channelConsumerTag != $consumerTag ;; msg: $mqMsg, deliveryTag: $deliveryTag")

            // DO NOT REQUE
            channel.basicNack(deliveryTag, false, false)

            //      throw new Exception(s"[MQService] $queueName : handleDelivery: channelConsumerTag ($channelConsumerTag) is not equal to consumerTag ($consumerTag)")

          } else {

            //        Logger.info(s"[MQService] $queueName : correct consumerTag: $channelConsumerTag == $consumerTag")


            //logger.info(s"[MQService] handleDelivery: queueName: $queueName : Received message : $mqMsg")

            processMessage(mqMsg)
              .map {
                _ =>

                  //logger.info(s"[MQService] handleDelivery processMessage success: $queueName: Calling ack")

                  channel.basicAck(deliveryTag, false)
              }

              .recover {

                case c: MQDoNotNackException =>

                  val logMsg = s"[MQDoNotNackException] [MQService] handleDelivery: processMessage error: $queueName: Calling ack: ${c.getMessage} ::: " +
                    s"${if (c.getMessage.contains("error email is now paused")) "" else LogHelpers.getStackTraceAsString(c)}"

                  if (c.log) {
                    if (c.logAsError) {
                      logger.error(logMsg)
                    } else {
                      logger.debug(logMsg)
                    }
                  }

                  channel.basicAck(deliveryTag, false)

                case _: MqExceptionRequeMsgWithoutLoggingError =>
                  // this flow is only there because there could be cases where we dont want to the log the error, and just want to reque
                  // NonFatal case below logs the error as well


                  //channel.basicNack(deliveryTag, false, true)

                  // NOTE: reque at the end of the queue
                  publishMessage(mqMsg, queueBaseName = queueBaseName, prefetchCount = prefetchCount) match {

                    case Failure(exception) =>
                      logger.error(s"SHOULD_NEVER_HAPPEN publishMsg failed MQNackTryLater  msg::$mqMsg :: queue: $queueName :: ${LogHelpers.getStackTraceAsString(exception)}")

                    case Success(value) => // do nothing

                  }

                  channel.basicAck(deliveryTag, false)


                case NonFatal(e) =>

                  logger.error(s"[MQService] handleDelivery: processMessage error: $queueName: Calling nack: ${e.getMessage} ::: ${getStackTraceAsString(e)} ::: message: $mqMsg")

                  //channel.basicNack(deliveryTag, false, true)

                  // NOTE: reque at the end of the queue
                  publishMessage(mqMsg, queueBaseName = queueBaseName, prefetchCount = prefetchCount) match {

                    case Failure(exception) =>
                      logger.error(s"SHOULD_NEVER_HAPPEN publishMsg failed NonFatal  msg::$mqMsg :: queue: $queueName :: ${LogHelpers.getStackTraceAsString(exception)}")

                    case Success(value) => // do nothing

                  }

                  channel.basicAck(deliveryTag, false)


              }

          }

      }


    }
  }


  def processMessage(msg: T)(implicit ws: WSClient, ec: ExecutionContext, system: ActorSystem): Future[Any]


  private final def basicConsume(channel: Channel, queueName: String, consumerTag: String, consumerCallback: DefaultConsumer) = Try {
    channel.basicConsume(queueName, MQConfig.autoAck, consumerTag, consumerCallback)
  }

  private final def getStackTraceAsString(t: Throwable) = {
    val sw = new StringWriter
    t.printStackTrace(new PrintWriter(sw))
    sw.toString
  }
  
}
