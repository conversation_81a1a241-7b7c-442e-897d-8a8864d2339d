package utils.mq.services

import org.apache.pekko.actor.ActorSystem
import play.api.libs.ws.WSClient

import scala.concurrent.ExecutionContext
import scala.util.Try

trait SimpleMqServiceTrait[T <: Serializable]
  extends MQService[T] {

  val queueBaseName: String
  val prefetchCount: Int

  def publish(
    msg: T
  ): Try[Unit] = {

    publishMsg(
      message = msg,
      queueBaseName = queueBaseName,
      prefetchCount = prefetchCount
    )
  }

  def startConsumer(

  )(
    implicit ws: WSClient,
    ec: ExecutionContext,
    system: ActorSystem
  ): Try[String] = {

    startMsgConsumer(
      queueBaseName = queueBaseName,
      prefetchCount = prefetchCount
    )

  }

}
