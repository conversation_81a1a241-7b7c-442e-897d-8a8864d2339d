package utils

import java.security.{KeyF<PERSON>y, KeyPairGenerator}
import java.security.interfaces.{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, RSAP<PERSON><PERSON><PERSON><PERSON>}
import api.AppConfig
import api.accounts.models.{AccountId, OrgId}
import api.accounts.{Account, ProspectCategoriesInDB, TeamId, TeamMember}
import io.sr.billing_common.models.PlanID
import api.call.models.{NavigationLinksPrevNextResponse, ValidateCallHistoryReq}
import api.call.models.ValidateCallHistoryReq.ValidatedCallHistoryReqRange
import api.campaigns.models.{CampaignStepRelatedDataForProspect, NavigationTime, SearchParams}
import api.emails.models.EmailReplyType
import api.emails.{EmailSetting, EmailSettingForm, ThreadForProspectInternal}
import api.prospects.InferredQueryTimeline.Range
import api.prospects.models.{ProspectCategory, SrProspectColumns}
import api.prospects.{InferredQueryTimeline, NavigationLinks, ProspectsApiSearchParams}
import org.joda.time.{DateTime, DateTimeZone}
import org.xbill.DNS.{CNAMERecord, Lookup, MXRecord, TXTRecord, Type}
import play.api.Logging
import play.api.libs.ws.WSClient

import java.util.concurrent.Executors
import scala.jdk.CollectionConverters.*
import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Random, Success, Try}
import io.lemonlabs.uri.Url
import io.smartreach.esp.api.emailApi.models.DKIMRecord
import io.smartreach.esp.api.emails.EmailSettingId
import utils.helpers.LogHelpers

import java.net.{URI, URL}

case class CampaignStepDataForEmail (
                                      campaign_id : Option[Long],
                                      campaign_name : Option[String],
                                      step_name: Option[String],
                                      step_id: Option[Long]
                                    )

object Helpers extends Logging {

  def generateEmailPassword(): String = {
    val chars = ('a' to 'z').toList
    val capitalChars = ('A' to 'Z').toList
    val numbers = ('0' to '9').toList

    val passwordBuilder = new StringBuilder

    // Add a random lowercase letter
    passwordBuilder.append(Random.shuffle(chars).head)
    // Add a random uppercase letter
    passwordBuilder.append(Random.shuffle(capitalChars).head)
    // Add a random number
    passwordBuilder.append(Random.shuffle(numbers).head)

    // Fill the rest with random characters from all types
    val allChars = chars ++ capitalChars ++ numbers
    while (passwordBuilder.length < 12) {
      passwordBuilder.append(Random.shuffle(allChars).head)
    }

    passwordBuilder.toString
  }

  // REF: https://stackoverflow.com/a/15500248/5290001
  def seqTryToTrySeq[A](xs: Seq[Try[A]]): Try[Seq[A]] =
    Try(xs.map(_.get))

  def listTryToTryList[A](xs: List[Try[A]]): Try[List[A]] =
    Try(xs.map(_.get))

  def toInt(s: String): Option[Int] = {
    try {
      Some(s.toInt)
    } catch {
      case e: Exception => None
    }
  }

  def sanitizeOptionString(valueOpt: Option[String]): Option[String] = {
    valueOpt match {
      case Some(value) =>
        if (value.trim.isEmpty) {
          None
        }
        else {
          Some(value)
        }

      case None => None
    }
  }


  /*
  def __checkCName(host: String, srTrackingDomainForEmailAccount: String): List[String] ={
    val inetAddress = InetAddress.getByName(host)
    Logger.info(s"__checkCName: host: $host :: srTrackingDomainForEmailAccount: $srTrackingDomainForEmailAccount :: getHostName: ${inetAddress.getHostName} :: getHostAddress : ${inetAddress.getHostAddress}")

    val dirContext = new InitialDirContext

    val attributes = try {
      dirContext.getAttributes("dns://*******/%s" format host, Array[String]("A", "CNAME"))
    } catch {
      case e: NamingException =>
        e.printStackTrace()
        return Nil
    }

    val list = {
      val attributeEnumeration = attributes.getAll
      var list = List[Attribute]()
      while (attributeEnumeration.hasMore)
        list = attributeEnumeration.next :: list
      attributeEnumeration.close()
      list.reverse
    }

    list map (x => x.getID -> x.get.toString) flatMap {
      case ("A", x) => List(x)
      case ("CNAME", x) =>
        // if(x=="via.smartreach-mail.com."){
        // Need to check against full URL ending with a dot (.) e.g. "via.smartreach-mail.com."
        //if (allTrackingDomains.map(domain => s"$domain.").contains(x)) {
        if (x == s"$srTrackingDomainForEmailAccount.") {

          List(x)
        } else {
          __checkCName(x, srTrackingDomainForEmailAccount = srTrackingDomainForEmailAccount)
        }

      case (_, x) => Nil
    }
  }
  */

  def __checkCName(host: String): List[String] = {
    logger.info(s"__checkcname: $host")


    val records = new Lookup(host, Type.CNAME).run
    if (records != null) {

      val recordsSeq = records.toList

      recordsSeq
        .map(record => {

          val cnameRecord = record.asInstanceOf[CNAMERecord]

          logger.info(s"__checkcname: $host --> ${cnameRecord.getTarget}")

          Option(cnameRecord.getTarget).map(_.toString).getOrElse("")

        })
        .filterNot(r => r.isEmpty)

    } else {
      logger.info(s"__checkcname: $host:: No CNAME entry found")
      List()

    }


  }

  def getMxRecords(domain: String)(using Logger: SRLogger): List[String] = {
    Logger.info(s"__checkDomain: $domain")
    val records = new Lookup(domain, Type.MX).run
    if (records != null) {
      val recordsSeq = records.toList
      recordsSeq
        .map(record => {
          val mxRecord = record.asInstanceOf[MXRecord]

          Logger.info(s"__checkDomain: $domain --> ${mxRecord.getTarget}")

          Option(mxRecord.getTarget).map(_.toString).getOrElse("")
        })
        .filterNot(r => r.isEmpty)
    } else {
      Logger.info(s"__checkDomain: $domain:: ")
      List()
    }
  }

  def checkCName(host: String, srTrackingDomainForEmailAccount: String)(
    implicit logger: ISRLogger
  ): Option[String] = {

    logger.info(s"checkcname: $host :: $srTrackingDomainForEmailAccount")

    Try {
      __checkCName(host = host)
    } match {


      case Failure(e) =>
        logger.error(s"FATAL checkCname host: $host :: srTrackingDomainForEmailAccount: $srTrackingDomainForEmailAccount :: ${LogHelpers.getStackTraceAsString(e)}")

        None

      case Success(res: List[String]) =>

        logger.info(s"checkCname: $host :: $srTrackingDomainForEmailAccount :: found: $res")

        // Need to check against full URL ending with a dot (.) e.g. "via.smartreach-mail.com."
        //val isConfiguredCorrectly = res.nonEmpty && allTrackingDomains.map(domain => s"$domain.").contains(res.head)
        val isConfiguredCorrectly = res.nonEmpty && res.head == s"$srTrackingDomainForEmailAccount."

        if (isConfiguredCorrectly) res.headOption else {

          logger.warn(s"checkCname not configured correctly: host: $host :: srTrackingDomainForEmailAccount: $srTrackingDomainForEmailAccount")

          None
        }


    }


  }


  /*
  NOTE: This is not needed when we are already checking permittedAccountIds
  This actually causes a bug when the user may not be a Owner (i.e. no access_members) but still have the requisite permissions
  This bug was faced by Nouriel from NO BS
  def checkTeamMember(doer: Account, teamId: Long, checkAccountId: Long): Option[TeamMember] = {

    doer
      .teams
      .find(team => team.team_id == teamId)
      .flatMap(team => team.access_members.find(mem => mem.account_id == checkAccountId))

  }
  */

  def getAccountName(a: Account): String = {
    val first = a.first_name.map(_.trim)
    val last = a.last_name.map(_.trim)

    if (first.nonEmpty && last.nonEmpty) s"${first.get} ${last.get}"
    else if (first.nonEmpty) first.get
    else if (last.nonEmpty) last.get
    else ""

  }

  def getAccountName(firstName: String, lastName: String ): String = {
    val first = firstName.trim
    val last= lastName.trim

    if (first.length > 0 && last.length > 0) s"${first} ${last}"
    else if (first.length > 0) first
    else if (last.length > 0) last
    else ""

  }

  def getAccountName(a: TeamMember): String = {

    if (a.first_name.nonEmpty && a.last_name.nonEmpty) s"${a.first_name.getOrElse("")} ${a.last_name.getOrElse("")}"
    else if (a.first_name.nonEmpty) a.first_name.getOrElse("")
    else if (a.last_name.nonEmpty) a.last_name.getOrElse("")
    else ""

  }

  def getSenderName(a: EmailSetting): String = {

    if (a.first_name.trim.nonEmpty && a.last_name.trim.nonEmpty) s"${a.first_name.trim} ${a.last_name.trim}"
    else if (a.first_name.trim.nonEmpty) a.first_name.trim
    else ""

  }

  def getSenderName(a: EmailSettingForm): String = {

    if (a.first_name.trim.nonEmpty && a.last_name.trim.nonEmpty) s"${a.first_name.trim} ${a.last_name.trim}"
    else if (a.first_name.trim.nonEmpty) a.first_name.trim
    else ""

  }

  // used in Email Reply Tracking
  def getFirstnameLastnameFromFullname(fullname: String): (Option[String], Option[String]) = {

    if (fullname.trim.isEmpty) (None, None) else {

      val firstSpace = fullname.indexOf(" ")

      if (firstSpace == -1) {
        // only one word in name, firstname = fullname
        (Some(fullname), None)

      } else {

        val firstname = fullname.substring(0, firstSpace).trim
        val lastname = fullname.substring(firstSpace).trim

        (
          if (firstname.isEmpty) None else Some(firstname),

          if (lastname.isEmpty) None else Some(lastname)
        )

      }
    }
  }

  def getProspectNameForFeed(firstName: Option[String], lastName: Option[String], email: Option[String]): String = {
    val name = if(firstName.isDefined && lastName.isDefined) s"${firstName.get} ${lastName.get}"
    else if (firstName.isDefined) firstName.get
    else if(lastName.isDefined) lastName.get
    else ""

    if(name.trim().isEmpty) {
      email.get
    } else name
  }


  def getProspectNameForSendingEmail(firstName: Option[String], lastName: Option[String]): Option[String] = {
    val name = if(firstName.isDefined && lastName.isDefined) Some(s"${firstName.get} ${lastName.get}")
    else if (firstName.isDefined) Some(firstName.get)
    else None

    name
  }

  def campaignHasUnsubscribeLink(
    opt_out_is_text: Boolean,
    opt_out_msg: String,
    body_template: String
  ): Boolean = {

    (!opt_out_is_text && opt_out_msg.trim.nonEmpty) || body_template.contains("unsubscribe_link")

  }

  // REF: https://stackoverflow.com/a/********
  // shuffle a list / seq
  def shuffleList[T](list: Seq[T]): Seq[T] = {

    scala.util.Random.shuffle(list)

  }

  // used to identify email threading
  def subjectsMatching(sub1: String, sub2: String): Boolean = {
    // sub1.toLowerCase.replace("re:", "").replace("fwd:", "").trim == sub2.toLowerCase.replace("re:", "").replace("fwd:", "").trim
    sub1.toLowerCase.replace("re:", "").trim == sub2.toLowerCase.replace("re:", "").trim
  }


  /// START: URL / APP LINK UTILS ///

  def makeEmailAccountSettingUrl(
    emailSettingId: EmailSettingId,
    accountId: AccountId,
    teamId: TeamId
  ) = {

    s"${AppConfig.emailAccountSettingsUrl}?aid=${accountId.id}&tid=${teamId.id}&eid=${emailSettingId.emailSettingId}"

  }


  def makeEmailAccountDKIMSettingUrl(
    emailSettingId: EmailSettingId,
    accountId: AccountId,
    teamId: TeamId
  ) = {

    s"${AppConfig.emailAccountSettingsUrl}?aid=${accountId.id}&tid=${teamId.id}&did=${emailSettingId.emailSettingId}"
  }

  def makeTriggerPageUrl(
                                      triggerId: Long,
                                      accountId: AccountId,
                                      teamId: TeamId
                                    ) = {

    s"${AppConfig.workflowsPageUrl}/$triggerId?aid=${accountId.id}&tid=${teamId.id}"
  }


  /// END: URL / APP LINK UTILS ///


  //// START: DKIM UTILS ///

  def digDKIM(domain: String, selector: String): Try[String] = Try {

    val records = new Lookup(s"$selector._domainkey.$domain", Type.TXT).run
    if (records != null) {

      val recordsSeq = records.toList.headOption

      if (recordsSeq.isEmpty) {
        ""
      } else {

        val record = recordsSeq.get
        val dkimRecord = record.asInstanceOf[TXTRecord]
        val r = dkimRecord.getStrings.asScala.mkString


        val splits = r.split(";")
        val key = splits(2).split("=")(1).trim()

        val keyRemoveLastQuoteIfPresent = if (key.takeRight(1) == """"""") {
          key.dropRight(1)
        } else key

        keyRemoveLastQuoteIfPresent

        //Logger.info(s"__checkcname: arena-eventservices.com --> ${cnameRecord.getName} ::: ${cnameRecord.getAdditionalName} ::: ${cnameRecord.getStrings.asScala.mkString}")

        // Option(cnameRecord.getTarget).map(_.toString).getOrElse("")

      }
    } else ""
    /*val dirContext = new InitialDirContext()
    val attrs = dirContext.getAttributes("dns://*******/%s" format (selector + "._domainkey." + domain), Array[String]("TXT"))
    val txt = attrs.get("TXT")
    val r = txt.get.toString

    val splits = r.split(";")
    val key = splits(2).split("=")(1).trim()

    val keyRemoveLastQuoteIfPresent = if (key.takeRight(1) == """"""") {
      key.dropRight(1)
    } else key

    keyRemoveLastQuoteIfPresent*/
  }


  def generateDKIMRecord(domain: String): DKIMRecord = {
    val generator = KeyPairGenerator.getInstance("RSA")
    generator.initialize(1024)
    val keyPair = generator.generateKeyPair

    val publicKey = keyPair.getPublic.asInstanceOf[RSAPublicKey]
    val privateKey = keyPair.getPrivate.asInstanceOf[RSAPrivateKey]
    val public_key = jakarta.xml.bind.DatatypeConverter.printBase64Binary(publicKey.getEncoded)

    DKIMRecord(
      public_key = public_key,
      private_key = jakarta.xml.bind.DatatypeConverter.printBase64Binary(privateKey.getEncoded),
      domain = domain.toLowerCase.trim,
      record = "v=DKIM1; k=rsa; p=" + public_key,
      selector = "smartreach",
      active = false
    )

  }

  //// END: DKIM UTILS ///


  ///// START: 2FA ////

  // TODO: THIS SHOULD BE MOVED TO DB
  val twoFAEnableForOrgs: Seq[Long] = Seq(
    2956, // superhumansales
    1664 // Daniel Lacour
  )

  def is2FAAllowedForOrg(planID: PlanID, orgId: Long): Boolean = {

    planID match {

      case PlanID.ULTIMATE
           | PlanID.V3_AGENCY_BASIC
           | PlanID.V3_AGENCY_MC
           | PlanID.V3_ENTERPRISE
           | PlanID.V4_199
           | PlanID.V4_299
           | PlanID.V4_EMAIL_29
           | PlanID.V4_EMAIL_59
           | PlanID.V4_EMAIL_99
           | PlanID.V4_MC_39
           | PlanID.V4_MC_79
           | PlanID.V4_MC_129
           | PlanID.V4_TWO_EMAIL_29
           | PlanID.V4_TWO_EMAIL_79
           | PlanID.V4_TWO_EMAIL_129
           | PlanID.V5_EMAIL_29
           | PlanID.V5_EMAIL_89
           | PlanID.V5_EMAIL_199
           | PlanID.V5_EMAIL_499
           | PlanID.V5_MC_39
           | PlanID.V5_MC_99
           | PlanID.V5_MC_249
           | PlanID.V5_MC_599

      =>

        true

      case PlanID.TRIAL | PlanID.STANDARD | PlanID.PRO | PlanID.INACTIVE
           | PlanID.V3_BASIC
           | PlanID.V3_GROWTH =>

        twoFAEnableForOrgs.contains(orgId)
    }

  }

  // 2FA is shown to both trial/ultimate, but only ultimate can enable it
  def show2FASettingInFrontend(planID: PlanID, orgId: Long): Boolean = {

    planID match {

      case PlanID.ULTIMATE | PlanID.TRIAL
           | PlanID.V3_AGENCY_BASIC
           | PlanID.V3_AGENCY_MC
           | PlanID.V3_ENTERPRISE
           | PlanID.V4_199
           | PlanID.V4_299
           | PlanID.V4_EMAIL_29
           | PlanID.V4_EMAIL_59
           | PlanID.V4_EMAIL_99
           | PlanID.V4_MC_39
           | PlanID.V4_MC_79
           | PlanID.V4_MC_129
           | PlanID.V4_TWO_EMAIL_29
           | PlanID.V4_TWO_EMAIL_79
           | PlanID.V4_TWO_EMAIL_129
           | PlanID.V5_EMAIL_29
           | PlanID.V5_EMAIL_89
           | PlanID.V5_EMAIL_199
           | PlanID.V5_EMAIL_499
           | PlanID.V5_MC_39
           | PlanID.V5_MC_99
           | PlanID.V5_MC_249
           | PlanID.V5_MC_599 =>

        true

      case PlanID.PRO | PlanID.INACTIVE | PlanID.STANDARD
           | PlanID.V3_BASIC
           | PlanID.V3_GROWTH  =>

        twoFAEnableForOrgs.contains(orgId)
    }

  }

  ///// END: 2FA ////

  def getHourLabels(hour: Int): Try[(String, String)] = Try {

    if (hour > 23 || hour < 0) {

      throw new Exception(s"[FATAL] getHourLabels Invalid hour $hour")

    } else {

      val labels = Seq("12am", "1am", "2am", "3am", "4am", "5am", "6am", "7am", "8am", "9am", "10am", "11am", "12pm", "1pm", "2pm", "3pm", "4pm", "5pm", "6pm", "7pm", "8pm", "9pm", "10pm", "11pm", "HELLO")

      val from = labels(hour)
      val to = if(hour >= 23) labels(0) else labels(hour + 1)

      (from, s"$from - $to")

    }
  }

  def getPercent(value: Int, total: Int): Int = {
    Math.ceil(Math.ceil(value * 100) / total).toInt
  }


  def getPublicIPOfCurrentServer()(implicit ws: WSClient, ec: ExecutionContext): Future[String] = {
    ws.url("https://checkip.amazonaws.com/")
      .get()
      .map(res => {

        val publicIP = res.body.trim
        logger.info(s"getPublicIPOfCurrentServer Found: $publicIP")

        publicIP

      })
      .recover { case e =>

        logger.info(s"getPublicIPOfCurrentServer [FATAL]: ${LogHelpers.getStackTraceAsString(e)}")

        throw e
      }

  }



  def generateOrgTrackingSubdomainKey(orgName: String): String = {

    val key = orgName.toLowerCase.trim.replaceAll("[^a-zA-Z0-9]", "")

    if (key.length < 6) {

      key + "email"

    } else if (key.length > 16) {

      // if name is "One Nation Solutions Pvt. Ltd.", key should be "onenation" rather than "onenationsolutio"
      val words = orgName.trim.toLowerCase.split("\\s+")
        .map(w => w.toLowerCase.trim.replaceAll("[^a-zA-Z0-9]", ""))

      val wordCount = words.length

      if (wordCount == 1) {

        key.take(16)

      } else {

        // REF: https://stackoverflow.com/a/28167499
        val potentialKeys = words.drop(1)
          .scanLeft(words.head) {
            case (r, c) => r + c
          }
          .filter(k => k.length > 5 && k.length < 17)

        if (potentialKeys.nonEmpty) {
          potentialKeys.head
        } else {
          key.take(16)
        }

      }

    } else {
      key
    }
  }


  // Used to not expose the full api keys in the api response
  def maskApiKey(
    key: String
  ): String = {

    val trimmedKey = key.trim

    val maskedApiKey = if (trimmedKey.isEmpty) {
      ""
    } else {

      if (trimmedKey.length > 10) {

        s"${trimmedKey.take(2)}***********${trimmedKey.takeRight(4)}"

      } else if (trimmedKey.length > 5) {

        s"${trimmedKey.take(1)}***********${trimmedKey.takeRight(1)}"

      } else {

        "*****"

      }

    }

    maskedApiKey
  }


  def getDialCodeNum(mobileCountryCodeStr: String): String = {

    if (mobileCountryCodeStr.trim.startsWith("+")) {
      mobileCountryCodeStr.trim.substring(1).trim
    } else mobileCountryCodeStr.trim

  }



  def getRandomElement[A](seq: Seq[A]): A = {
    seq(scala.util.Random.nextInt(seq.length))
  }


  // NOTE: https://stackoverflow.com/a/********
  // this helper is used do sequential execution of Futures in scala
  def seqFutures[T, U](items: IterableOnce[T])(yourfunction: T => Future[U])(implicit ec: ExecutionContext): Future[List[U]] = {
    items.iterator.foldLeft(Future.successful[List[U]](Nil)) {
      (f, item) => f.flatMap {
        x => yourfunction(item).map(_ :: x)
      }
    } map (_.reverse)
  }

  def maxSpamTests(
    totalSendingEmailAccounts: Int,
    additionalSpamTests: Int
  ) = {
    Math.max(100, AppConfig.baseSpamTestsPerEmailAccount * totalSendingEmailAccounts + additionalSpamTests)
  }

  def getProspectCategories(team_id: Long, account: Account, Logger: SRLogger): Seq[ProspectCategoriesInDB] = {

    val categories = account.teams
      .find(t => t.team_id == team_id)
      .map(_.prospect_categories_custom)

    if (categories.isEmpty) {
      Logger.fatal(s"Helpers.getProspectCategories empty categories:: team_id: ${team_id} :: account: ${account} ")
    }

    categories.getOrElse(Seq())

  }

  def getCategoryById(
    team_id: Long,
    account: Account,
    categoryId: Long,
    Logger: SRLogger
  ): Option[ProspectCategoriesInDB] = {

    val categories = getProspectCategories(team_id = team_id, account = account, Logger = Logger)

    if (categories.isEmpty) {
      Logger.fatal(s"Prospect.getCategoryId num empty categories:: categoryId: $categoryId")
    }

    val cate = categories.find(c => c.id == categoryId)

    if (cate.isEmpty) {
      Logger.fatal(s"Prospect.getCategoryId num NOT FOUND categoryId: ${categoryId}  :: categories: $categories")
    }

    cate

  }

  def getCategoryByTextId(
    team_id: Long,
    account: Account,
    textId: ProspectCategory.Value,
    Logger: SRLogger
  ): Option[ProspectCategoriesInDB] = {

    val categories = getProspectCategories(team_id = team_id, account = account, Logger = Logger)

    if (categories.isEmpty) {
      Logger.fatal(s"Helpers.getCategoryByTextId empty categories:: textId: $textId")
    }

    val cate = categories.find(c => c.text_id == textId.toString)

    if (cate.isEmpty) {
      Logger.fatal(s"Helpers.getCategoryByTextId NOT FOUND textId: $textId  :: categories: $categories")
    }

    cate

  }

  // REF: http://biercoff.com/easily-measuring-code-execution-time-in-scala/
  // Used to log slow functions
  def logTimeTaken[R](
    Logger: SRLogger,
    thresholdInMillis: Int = 100
  )(block: => R): R = {
    val startTime = DateTime.now.getMillis

    val result = block    // call-by-name

    val endTime = DateTime.now.getMillis

    val timeTaken = endTime - startTime

    val majorThreshold = 2500
    val criticalThreshold = 10000

    val timespikeMsg = s"[TIMESPIKE] timeTaken > $thresholdInMillis ms :: exactTimeTaken: $timeTaken"

    if (timeTaken >= criticalThreshold) {

      Logger.warn(s"[CRITICAL] $timespikeMsg")

    } else if (timeTaken >= majorThreshold) {

      Logger.warn(s"[MAJOR] $timespikeMsg")

    } else if (timeTaken > thresholdInMillis) {

      Logger.warn(timespikeMsg)

    }

    result
  }

  def getClientIPFromGCPLoadbalancer(
    request: play.api.mvc.Request[Any]
  ): Option[String] = {

    Try {

      // REF: https://cloud.google.com/load-balancing/docs/https#x-forwarded-for_header
      val xForwardedHeader = request.headers.get("X-Forwarded-For")

      if (
        xForwardedHeader.nonEmpty
          && xForwardedHeader.get.nonEmpty
          && xForwardedHeader.get.split(",").nonEmpty
      ) {
        xForwardedHeader.get.split(",").headOption
      } else None

    } match {
      case Failure(e) =>
        logger.error(s"FATAL getClientIPFromGCPLoadbalancer (returning None): ${LogHelpers.getStackTraceAsString(e)} ")
        None

      case Success(clientIP) =>
        clientIP
    }

  }

  def getClientIPFromGCPLoadbalancerFallbackToRemoteAddr(
    request: play.api.mvc.Request[Any]
  )(
    implicit logger: SRLogger
  ): String = {

    Try {

      val ipFromGCP = getClientIPFromGCPLoadbalancer(
        request = request
      )

      if (ipFromGCP.isDefined) {
        ipFromGCP
      } else {
        Option(request.remoteAddress)
      }

    } match {
      case Failure(e) =>
        logger.fatal(s"FATAL ERROR getClientIPFromGCPLoadbalancerFallbackToRemoteAddr (returning 0.0.0.0)", err = e)
        "0.0.0.0"

      case Success(None) =>
        logger.fatal(s"FATAL None getClientIPFromGCPLoadbalancerFallbackToRemoteAddr (returning 0.0.0.0): ${request.remoteAddress}")
        "0.0.0.0"

      case Success(Some(clientIP)) =>
        clientIP
    }

  }

  def genFixedThreadPoolEC(
    threads: Int
  ): ExecutionContext = {

    ExecutionContext.fromExecutor(
      Executors.newFixedThreadPool(threads)
    )

  }

  /* NOT USED: ColumnDef.parseCustomFields handles this work

  OLD NOTE:
  replaceEmptyStringsWithNullInJSONObjWhileUpdatingProspectsORAccounts:

  while updating prospects from API/ CSV,
  even if ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads was true,
  custom_fields was getting updated with sql:

                        custom_fields = prospects.custom_fields || EXCLUDED.custom_fields,

   this caused all custom_fields to reset to null / empty if api passed those values as empty.

   In order to handle this correctly, we are now first sanitizing the custom_fields using the function below
   to replace all empty strings with null,
   and then we use the postgres "jsonb_strip_nulls" method to ignore nulls if ignoreNullOrEmptyValuesWhileUpdatingViaApiCallsAndCsvUploads is true
   */
  /*
  def replaceEmptyStringsWithNullInJSONObjWhileUpdatingProspectsORAccounts(obj: JsValue): JsValue = {

    val updatedJSONObj = Json.toJson(obj.as[JsObject].value.map { case (key, value) =>

      val newVal = value match {
        case JsString(str) => if (str.trim.isEmpty) JsNull else JsString(str)
        case _ => value
      }

      (key -> newVal)

    })

    updatedJSONObj
  }
  */

  def trimToLower(str: String): String = {
    str.trim.toLowerCase
  }

  // REF: https://stackoverflow.com/questions/1347646/postgres-error-on-insert-error-invalid-byte-sequence-for-encoding-utf8-0x0
  def sanitiseStringForDB(str: String) = {
    str.replaceAll("\u0000", "")
  }

  def getPrevLinkForConvThread(uri:Url, page_data: NavigationLinks):Option[String]={

    val prev_link = page_data.prev.map(prv =>
      uri
        .replaceParams("newer_than", prv.getMillis.toString)
        .removeParams("older_than"))

    prev_link.map(_.toString())
  }
  def getNextLinkForConvThread(uri: Url, page_data: NavigationLinks):Option[String]={

    val next_link = page_data.next.map(nxt =>
      uri
        .replaceParams("older_than", nxt.getMillis.toString)
        .removeParams("newer_than"))

    next_link.map(_.toString())

  }

  def getNextLinkForCampaignsApi(uri: Url, page_data: NavigationTime, search_params: SearchParams): Option[String] = {

    val next_link = page_data.next.map(nxt =>
      search_params.range match {
        case Range.Before(_) =>{
            uri
              .replaceParams("older_than", nxt)
              .removeParams("newer_than")
        }
        case Range.After(_) =>  {
            uri
              .replaceParams("newer_than", nxt)
              .removeParams("older_than")
        }

       })


    next_link.map(_.toString())

  }

  //fixme: PUBLIC_API: use single function for all listing apis
  def getNextLinkForListingApi(
                                uri: Url,
                                page_data: NavigationTime,
                                search_params: ProspectsApiSearchParams
                              ): Option[String] = {

    val next_link = page_data.next.map(nxt =>
      search_params.range match {
        case Range.Before(_) => {
          uri
            .replaceParams("older_than", nxt)
            .removeParams("newer_than")
        }
        case Range.After(_) => {
          uri
            .replaceParams("newer_than", nxt)
            .removeParams("older_than")
        }

      })


    next_link.map(_.toString())

  }

  def getNavigationLinksForListingApi(
                                uri: Url,
                                page_data: NavigationLinks
                              ): NavigationLinksPrevNextResponse = {

    val next_link = page_data.next.map(nxt =>
      uri
        .removeParams("newer_than")
        .removeParams("older_than")
        .addParam("older_than", nxt.getMillis)
    )

    val prev_link = page_data.prev.map(prev =>
      uri
        .removeParams("newer_than")
        .removeParams("older_than")
        .addParam("newer_than", prev.getMillis)
    )

    NavigationLinksPrevNextResponse(
      prev = prev_link.map(_.toString()),
      next = next_link.map(_.toString())
    )

  }



    def getLinkForPaginationCallLogApi(
                                        uri: Url,
                                        navigationLinks: NavigationLinks,
                                        validated: ValidateCallHistoryReq.ValidatedCallHistoryReqRange
                                      ): NavigationLinksPrevNextResponse = {

      val next_link = navigationLinks.next.map(nxt =>
        validated.timeline match {
          case InferredQueryTimeline.Range.Before(_) => {
            uri
              .removeParams("newer_than")
              .removeParams("older_than")
              .addParam("older_than", nxt.getMillis)

          }
          case InferredQueryTimeline.Range.After(_) => {
            uri
              .removeParams("newer_than")
              .removeParams("older_than")
              .addParam("older_than", nxt.getMillis)
          }

        })

      val prev_link = navigationLinks.prev.map(prev =>
        validated.timeline match {
          case InferredQueryTimeline.Range.Before(_) => {
            uri
              .removeParams("newer_than")
              .removeParams("older_than")
              .addParam("newer_than", prev.getMillis)
          }
          case InferredQueryTimeline.Range.After(_) => {
            uri
              .removeParams("older_than")
              .removeParams("newer_than")
              .addParam("newer_than", prev.getMillis)
          }
        })


      NavigationLinksPrevNextResponse(
        prev = prev_link.map(_.toString()),
        next = next_link.map(_.toString())
      )
    }

  def checkProspectCategoryAutoReplyAndOoo(reply_type: EmailReplyType.Value): Boolean = {
    (reply_type == EmailReplyType.AUTO_REPLY ) || (reply_type == EmailReplyType.OUT_OF_OFFICE)
  }

  def startOfTheDay(timeZone: String): DateTime = {
    DateTime.now()
      .withZone(DateTimeZone.forID(timeZone))
      .withTimeAtStartOfDay()
  }

  def endOfTheDay(timeZone: String): DateTime = {
    DateTime.now()
      .withZone(DateTimeZone.forID(timeZone))
      .withTimeAtStartOfDay()
      .withHourOfDay(23)
      .plusMinutes(59)
      .plusSeconds(59)
      .plusMillis(999)
  }

  def assigneeValidation(
                        assigneeId: Option[Long],
                        permittedAccountIds: Seq[Long]
                        ): Boolean = {
    if (assigneeId.isDefined && !permittedAccountIds.contains(assigneeId.get)) {
      true
    } else {
      false
    }
  }

  def stringToLong(str : String): Try[Long] = Try {
    str.toLong
  }

  //  https://stackoverflow.com/a/7234561
  def seqEitherToEitherSeq[A, B](xs: Seq[Either[A, B]]): Either[A,Seq[B]] = {
    val left: Option[A] = xs collectFirst { case Left(x) => x }
    if (left.isDefined) {
      Left(left.get)
    } else {
      Right(xs collect { case Right(x) => x })
    }
  }

  /**
    *
    * Use this in test cases when it is required to compare two queries
    *
    * @param expectedQuery
    * @param actualQuery
    * @return
    */
  def compareSQLQueriesInTest(expectedQuery: String, actualQuery: String): Boolean = {
    val split = expectedQuery.split(s"\\s+")
    val rhs = split.reduce((a1, a2) => {
      a1 + " " + a2
    })
    val lhs = actualQuery.split("\\s+").reduce((s1, s2) => {
      s1 + " " + s2
    })

    lhs == rhs
  }


  def generateRandomString(length: Int): String = {
    val characterSet: String = "abcdefghijklmnopqrstuvwxyz0123456789"

    val random = new Random
    (1 to length).map { _ =>
      characterSet(random.nextInt(characterSet.length))
    }.mkString
  }

  def getTimeLineRange(
                        parsedParams: Map[String, Seq[String]]
                      ): Try[(Boolean, InferredQueryTimeline.Range)] = {

    val older_than: Option[Long] = if (parsedParams.contains("older_than")) {
      Some(parsedParams("older_than").head.toLong)
    } else None

    val newer_than: Option[Long] = if (parsedParams.contains("newer_than")) {
      Some(parsedParams("newer_than").head.toLong)
    } else None

    val timezoneCode = DateTimeZone.forID("UTC")

    val timeLineValidation =
      if (older_than.isDefined && newer_than.isDefined) {
        Failure(new Exception("older than and newer than cannot be defined together"))
      } else if (older_than.isDefined) {
        val dateTime = older_than.get
        val df = new DateTime(dateTime)
        if (df.getYear <= 2000)
          Failure(new Exception("Invalid date provided"))
        else
          Success(false, InferredQueryTimeline.Range.Before(df.toDateTime(timezoneCode)))
      } else if (newer_than.isDefined) {
        val dateTime = newer_than.get
        val df = new DateTime(dateTime)
        if (df.getYear <= 2000)
          Failure(new Exception("Invalid date provided"))
        else
          Success(false, InferredQueryTimeline.Range.After(df.toDateTime(timezoneCode)))
      } else {
        Success(true, InferredQueryTimeline.Range.Before(DateTime.now().toDateTime(timezoneCode)))
      }
    timeLineValidation
  }

  


  /**
    *
    */
  def getCampaignStepDataForEmail(
                                   emailThread: Option[ThreadForProspectInternal.EmailThreadForProspectInternal],
                                   campaign_step_details: Option[CampaignStepRelatedDataForProspect]
                                 ): CampaignStepDataForEmail = {

    val isExtensionFlow = campaign_step_details.isDefined

    if (isExtensionFlow) {

      val campaign_step_details_get = campaign_step_details.get

      CampaignStepDataForEmail(
        campaign_id = Some(campaign_step_details_get.campaign_id.id),
        campaign_name = Some(campaign_step_details_get.campaign_name.name),
        step_name = Some(campaign_step_details_get.step_label.label),
        step_id = Some(campaign_step_details_get.current_step_id.step_id)

      )

    } else {

      CampaignStepDataForEmail(
        campaign_id = emailThread.flatMap(_.campaign_id),
        campaign_name = emailThread.flatMap(_.campaign_name),
        step_name = None,
        step_id = None
      )
    }

  }

  def checkIfCypressTestingEmailAccount(email: String): Boolean = {
    (AppConfig.CypressTestingEmailAccount.prodEmailAccount == email && AppConfig.isProd) || (AppConfig.CypressTestingEmailAccount.stagingEmailAccount.contains(email)  && !AppConfig.isProd)
  }

  def stringToURL(href: String, orgIdOpt: Option[OrgId])(using Logger: SRLogger): Try[URL] = {
    if(orgIdOpt.isEmpty) {
      Try(new URL(href))   // old method
    } else {

      val orgId = orgIdOpt.get

      if ((AppConfig.isProd && AppConfig.orgs_for_new_URI_change_prod.contains(orgId.id)) ||
        (!AppConfig.isProd && AppConfig.orgs_for_new_URI_change_staging.contains(orgId.id))
      ) {
        Try(new URI(href).toURL) match { // new method
          case Failure(exception) =>

            Logger.shouldNeverHappen(
              msg = s"Failed to create URI from href: $href :: orgIdOpt: $orgIdOpt",
              err = Some(exception)
            )

            Failure(exception)

          case Success(url) =>

            // remove this later
            Logger.info(
              msg = s"Successfully created URI from href: $href :: orgIdOpt: $orgIdOpt"
            )

            Success(url)

        }
      } else {
        Try(new URL(href))
      }

    }

  }

  //filters seq[Option[String]] to Seq[String] having nonEmpty strings
  def filterSeqOfOptStringToSeqString(strSeq: Seq[Option[String]]): Seq[String] = {
    strSeq.filter(_.isDefined).map(_.get).filter(_.nonEmpty)
  }



  def getPercentWith1Decimal(
                              numerator: Long,
                            denominator: Long

                            ) :String = {

      // if emails validated == 0 then will return "-" in the percenatge parenthesis
      if(denominator == 0) {
          "(-)"
      } else {
          s"(${f"${numerator * 100.0 / denominator}%.1f"}%)"
      }
  }

  def areTwoOptionStringsSame(
                               newString: Option[String],
                               oldString: Option[String]
                             ): Boolean = {
    if ((newString.isDefined && oldString.isEmpty) ||
      (newString.isEmpty && oldString.isDefined)) {
      false
    } else if (newString.isDefined && oldString.isDefined) {
      newString.get.trim.toLowerCase == oldString.get.trim.toLowerCase
    } else {
      true
    }
  }

    def formatBoolean(value: Boolean): String = {
        if (value) "<td style='color:green;'>T</td>"
        else "<td style='color:red;'>F</td>"
    }


  def seqToCommaSeperatedString(values : Seq[String]) = {

    values.map(value => s"'${value}'").mkString(", ")
  }
  def getDomainFromEmail(email: String): Option[String] = {
    val emailRegex = "^[^@]+@(.+)$".r // Regex to capture everything after the @ symbol
    email match {
      case emailRegex(domain) => Some(domain)
      case _ => None // Return None if the input is not a valid email
    }
  }

  def generateSrProspectColumnList(deduplicationColumns: Option[String]): Try[Seq[SrProspectColumns]] = {
    if(deduplicationColumns.isDefined){

      val columns: List[String] = deduplicationColumns.get.split(",").toList.map(_.trim)

      val srColumns = columns.map(c => SrProspectColumns.fromString(c))

      seqTryToTrySeq(srColumns)
    } else {
      Success(Seq())
    }
  }

  // Function to parse PostgreSQL array strings
  def parsePostgresArray(arrayString: String): Seq[String] = {
    arrayString.stripPrefix("{").stripSuffix("}").split(",").map(_.trim).toSeq
  }


  def getLastSunday: DateTime = {
    val today = DateTime.now()

    val dayOfWeek = today.dayOfWeek().get()

    if(dayOfWeek == 7) {
      today
    } else  today.minusDays(dayOfWeek)


  }

  def checkIfStringIsDefinedAndNonEmpty(inputString: Option[String]): Boolean = {
    inputString.isDefined && inputString.get.nonEmpty
  }
  
  def getTeamMemberFromAccount(account: Account, team_id: TeamId):TeamMember = {

    val team_member: TeamMember = account
      .teams
      .flatMap(_.access_members)
      .filter(_.team_id == team_id.id)
      .filter(_.email.toLowerCase().trim == account.email.toLowerCase().trim).head

    team_member
  }

  def extractEmail(email: String): String = {
    val emailRegex = "^([a-zA-Z0-9._%+-]+)\\+[^@]+(@[a-zA-Z0-9.-]+)$".r
    email match {
      case emailRegex(localPart, domain) => s"$localPart$domain"
      case _ => email
    }
  }
}

