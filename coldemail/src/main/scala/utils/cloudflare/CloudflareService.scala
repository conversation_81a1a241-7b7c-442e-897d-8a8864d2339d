package utils.cloudflare

import api.AppConfig
import play.api.Logging
import play.api.libs.json.{Format, JsValue, Json, Reads}
import play.api.libs.ws.WSClient
import utils.cloudflare
import utils.enum_sr_utils.EnumUtils

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}
import play.api.libs.ws.WSBodyWritables.writeableOf_JsValue

sealed trait CreateCustomHostNameError

object CreateCustomHostNameError {
  case object InvalidZoneIdError extends CreateCustomHostNameError
  case object IdentifierNotFound extends CreateCustomHostNameError
  case object StatusNotFound extends CreateCustomHostNameError
  case object HostNameNotFound extends CreateCustomHostNameError
  case class CreateHostnameApiFailed(error: String) extends CreateCustomHostNameError
  case class InvalidStatusType(err:Throwable) extends  CreateCustomHostNameError
  case object certificateStatusNotFound extends CreateCustomHostNameError
}

sealed trait GetCustomHostNameError

object GetCustomHostNameError{
  case object InvalidZoneIdError extends GetCustomHostNameError
  case object IdentifierNotFound extends GetCustomHostNameError
  case object StatusNotFound extends GetCustomHostNameError
  case object HostNameNotFound extends GetCustomHostNameError
  case class  GetHostnameApiFailed(error: String) extends GetCustomHostNameError
  case class InvalidStatusType(err:Throwable) extends  GetCustomHostNameError

  case object certificateStatusNotFound extends GetCustomHostNameError

}

object CnameStatus extends Enumeration {
  type CnameStatus=Value
  val PENDING=Value("pending")
  val ACTIVE=Value("active")
  given format: Format[cloudflare.CnameStatus.Value] = EnumUtils.enumFormat(CnameStatus)
}

case class CustomHostnameData(
                               identifier: String,
                               hostname: String,
                               hostNameStatus:CnameStatus.Value,
                               certificateStatus: String,
                               validationErrors: Seq[JsValue]
                             )

object  CustomHostnameData {
  implicit val reads: Reads[CustomHostnameData] = Json.reads[CustomHostnameData]
}

class CloudflareService() extends Logging {

  private  val authKey=AppConfig.cloudflareAuthKey
  private  val authEmail=AppConfig.cloudflareAuthEmail

  /*Doc: https://api.cloudflare.com/#custom-hostname-for-a-zone-create-custom-hostname
  *
  * 5 July 2024 Additional Docs for reference :

  * Hostname validation : https://developers.cloudflare.com/cloudflare-for-platforms/cloudflare-for-saas/domain-support/hostname-validation/pre-validation/
  * Certificate validation : https://developers.cloudflare.com/cloudflare-for-platforms/cloudflare-for-saas/security/certificate-management/issue-and-validate/validate-certificates/http/
  * */

  def createCustomHostName(zoneId: String,
                           hostname: String
                          )(implicit ws: WSClient, ec: ExecutionContext):Future[Either[CreateCustomHostNameError,CustomHostnameData]] = {

    ws.url(s"https://api.cloudflare.com/client/v4/zones/$zoneId/custom_hostnames")
      .addHttpHeaders(
        "X-Auth-Email" -> s"${authEmail}",
        "X-Auth-Key" -> s"${authKey}",
        "Content-Type" -> "application/json"
      )
      .post(Json.obj("hostname" -> s"$hostname", "ssl" -> Json.obj(
        "method" -> "http",
        "type" -> "dv",
        "settings" -> Json.obj("http2" -> "on",
          "min_tls_version" -> "1.2",
          "tls_1_3" -> "on",
          "ciphers" -> Seq("ECDHE-RSA-AES128-GCM-SHA256", "AES128-SHA"),
          "early_hints" -> "on"),
        "bundle_method" -> "ubiquitous",
        "wildcard" -> false
      )))
      .map(res => {

        logger.logger.info(s"createCustomHostName res: ${res.body} ${res.status}")

        if(!(res.status >=200 && res.status<300)){
          val errorMessage = (res.json \ "errors" \\ "message").headOption.flatMap(_.asOpt[String])
          Left(CreateCustomHostNameError.CreateHostnameApiFailed(res.body))
        } else{
          val identifier = (res.json \ "result"\"id").asOpt[String]
          val hostname = (res.json\"result"\ "hostname").asOpt[String]
          val status = (res.json\"result"\"status").asOpt[String]
          val certificateStatus = (res.json \ "result" \ "ssl" \ "status").asOpt[String]
          val validationErrors = (res.json \ "result" \ "ssl" \ "validation_errors").asOpt[Seq[JsValue]].getOrElse(Seq.empty)
          logger.logger.info(
            "identifier: "+identifier.getOrElse("NA") +
              "\nhostname:"+ hostname.getOrElse("NA") +
              "\nstatus:" + status.getOrElse("NA") +
              "\ncertificate status:" + certificateStatus.getOrElse("NA")
          )
          if (identifier.nonEmpty && hostname.nonEmpty && status.nonEmpty && certificateStatus.nonEmpty) {
            Try {
              CnameStatus.withName(status.get)
            } match {
              case Failure(e) =>
                Left(CreateCustomHostNameError.InvalidStatusType(e))
              case Success(value) =>
                Right(
                  CustomHostnameData(
                    identifier = identifier.get,
                    hostname = hostname.get,
                    hostNameStatus = CnameStatus.withName(status.get),
                    certificateStatus = certificateStatus.get,
                      validationErrors = validationErrors

                  )
                )
            }
          }
          else if(identifier.isEmpty){
            Left(CreateCustomHostNameError.IdentifierNotFound)
          }
          else if(status.isEmpty){
            Left(CreateCustomHostNameError.StatusNotFound)
          }
          else if(certificateStatus.isEmpty){
              Left(CreateCustomHostNameError.certificateStatusNotFound)
          }
          else{
            Left(CreateCustomHostNameError.HostNameNotFound)
          }
        }
      })


  }

  def getCustomHostName(
                         zoneId: String,
                         identifier: String)(implicit ws: WSClient, ec: ExecutionContext):
  Future[Either[GetCustomHostNameError,CustomHostnameData]] = {

      ws.url(s"https://api.cloudflare.com/client/v4/zones/$zoneId/custom_hostnames/$identifier")
        .addHttpHeaders(
          "X-Auth-Email" -> s"$authEmail",
          "X-Auth-Key" -> s"$authKey",
          "Content-Type" -> "application/json"
        ).get().map(res => {

        logger.logger.info(s"getCustomHostName: ${res} :: ${res.body}")

        if (res.status != 200) {
          val errorMessage = (res.json \ "errors" \\ "message").headOption.flatMap(_.asOpt[String])
          Left(GetCustomHostNameError.GetHostnameApiFailed(error = errorMessage.getOrElse("Error while getCustomHostName")))
        } else {
          val identifier = (res.json \ "result" \ "id").asOpt[String]
          val hostname = (res.json \ "result" \ "hostname").asOpt[String]
          val status = (res.json \ "result" \ "status").asOpt[String]
          val certificateStatus = (res.json \ "result" \ "ssl" \ "status").asOpt[String]
          val validationErrors = (res.json \ "result" \ "ssl" \ "validation_errors").asOpt[Seq[JsValue]].getOrElse(Seq.empty)
          logger.logger.info(
            "identifier: " + identifier.getOrElse("NA") +
              "\n" + "hostname:" + hostname.getOrElse("NA") +
              "\nstatus:" + status.getOrElse("NA") +
              "\ncertificate status: " + certificateStatus.getOrElse("NA")
          )


          if (identifier.nonEmpty && hostname.nonEmpty && status.nonEmpty && certificateStatus.nonEmpty) {
            Try {
              CnameStatus.withName(status.get)
            } match {
              case Failure(e) =>
                Left(GetCustomHostNameError.InvalidStatusType(e))
              case Success(value) =>
                Right(
                  CustomHostnameData(
                    identifier = identifier.get,
                    hostname = hostname.get,
                    hostNameStatus = CnameStatus.withName(status.get),
                      certificateStatus = certificateStatus.get,
                      validationErrors = validationErrors
                  )
                )
            }

          }
          else if (identifier.isEmpty) {
            Left(GetCustomHostNameError.IdentifierNotFound)
          }
          else if (status.isEmpty) {
            Left(GetCustomHostNameError.StatusNotFound)
          }
          else if(certificateStatus.isEmpty){
              Left(GetCustomHostNameError.certificateStatusNotFound)
          }
          else {
            Left(GetCustomHostNameError.HostNameNotFound)
          }
        }
      })
  }

//    https: //api.cloudflare.com/client/v4/zones/$ZONE_ID/custom_hostnames/$CUSTOM_HOSTNAME_ID

  // api doc link : https://developers.cloudflare.com/api/resources/custom_hostnames/methods/delete/

  def removeCustomHostName(
                            zoneId: String,
                            identifier: String)
                          (implicit ws: WSClient, ec: ExecutionContext): Future[Either[String,Boolean]] = {
      ws.url(s"https://api.cloudflare.com/client/v4/zones/$zoneId/custom_hostnames/$identifier")
        .addHttpHeaders(
          "X-Auth-Email" -> s"$authEmail",
          "X-Auth-Key" -> s"$authKey",
          "Content-Type" -> "application/json"
        )
        .delete()
        .map(res => {
          if (res.status == 200) {
              logger.logger.info(s"removeCustomHostName: ${res} :: ${res.body}")
              Right(true)
          } else {
              logger.logger.error(s"removeCustomHostName error: ${res.body} :: status : ${res.status}")
            Left(res.body)
        }
  })
}
}
