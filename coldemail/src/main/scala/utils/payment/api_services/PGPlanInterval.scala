package utils.payment.api_services

import play.api.libs.json.Format
import utils.enum_sr_utils.EnumUtils
import utils.payment.api_services

object PGPlanInterval extends Enumeration {

  type PGPlanInterval = Value

  val MONTH = Value("month")
  val YEAR = Value("year")

  given format: Format[api_services.PGPlanInterval.Value] = EnumUtils.enumFormat(PGPlanInterval)


  def getPlanInterval(planNameInDB: String): Option[PGPlanInterval.Value] = {

    // check lowercase name, for example in razorpay, name is "Standard Monthly"
    val name = planNameInDB.toLowerCase()

    if (name.contains("annual")) Some(PGPlanInterval.YEAR)
    else if (name.contains("month")) Some(PGPlanInterval.MONTH)
    else None

  }

}