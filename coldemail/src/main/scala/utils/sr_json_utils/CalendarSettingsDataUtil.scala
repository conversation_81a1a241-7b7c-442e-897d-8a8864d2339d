package utils.sr_json_utils

import api.calendar_app.models.CalendarAccountData
import play.api.libs.json.{Format, Js<PERSON>rro<PERSON>, Js<PERSON>ath, JsResult, JsSuccess, <PERSON>sV<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>s}
import sr_scheduler.models.{CalendarTeam, SelectedCalendarData}
import utils.email.CalendarLinkData

import scala.util.{Failure, Success, Try}

object CalendarSettingsDataUtil {

  implicit def jsonFormatCalendarTeam: Format[CalendarTeam] = Json.format[CalendarTeam]


  implicit val optionCalendarTeamReads: Reads[Option[CalendarTeam]] = (
    (JsPath \ "calendar_team").readNullable[CalendarTeam]
    )


  def getCalendarLinkData(selectedCalendarData: Option[SelectedCalendarData]): Option[CalendarLinkData] = {


    if (selectedCalendarData.isDefined && selectedCalendarData.get.calendar_is_individual.isDefined) {

      val isIndividual = selectedCalendarData.get.calendar_is_individual.get

      Some(
        CalendarLinkData(
          isIndividualCalendar = isIndividual,
          calendarUsernameSlug = if (isIndividual) {
            Some(selectedCalendarData.get.calendar_selected_username_slug.get)
          } else {
            None
          },
          calendarTeam = if (isIndividual) {
            None
          } else {
            Some(CalendarTeam(
              selected_calendar_team_id = selectedCalendarData.get.calendar_team_id.get,
              selected_calendar_team_slug = selectedCalendarData.get.calendar_team_slug.get
            ))

          },
          selectedEventTypeSlug = selectedCalendarData.get.calendar_event_type_slug.get
        )
      )
    } else {
      None

    }


  }

}