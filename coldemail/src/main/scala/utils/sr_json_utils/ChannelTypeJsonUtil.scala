package utils.sr_json_utils

import sr_scheduler.models.ChannelType
import play.api.libs.json.{Format, JsError, JsResult, JsString, JsSuccess, JsValue}

import scala.util.{Failure, Success}

object ChannelTypeJsonUtil {


  implicit def jsonFormatChannelType: Format[ChannelType] = new Format[ChannelType] {

    override def writes(o: ChannelType): JsValue = {
      JsString(o.toString)
    }

    override def reads(json: JsValue): JsResult[ChannelType] = {


      ChannelType
        .fromKey(
          json.as[String]
        )

    } match {
      case Failure(e) => JsError(e.toString)
      case Success(v) => JsSuccess(v)
    }
    //    override def reads: Reads[CampaignStepType] = (o: JsValue) =>

  }

  //  given format = Json.format[CampaignStepType]
}
