package utils.sr_json_utils

import api.campaigns.models.CampaignStepType
import play.api.libs.json.{Format, JsError, JsResult, JsString, JsSuccess, JsValue}

import scala.util.{Failure, Success}

object CampaignStepTypeJsonUtil {


  implicit def jsonFormatCampaignStepType: Format[CampaignStepType] = new Format[CampaignStepType] {

    override def writes(o: CampaignStepType): JsValue = {
      JsString(o.toKey)
    }

    override def reads(json: JsValue): JsResult[CampaignStepType] = {


      CampaignStepType
        .fromKey(
          json.as[String]
        )

    } match {
      case Failure(e) => JsError(e.toString)
      case Success(v) => JsSuccess(v)
    }
    //    override def reads: Reads[CampaignStepType] = (o: JsValue) =>

  }

  //  given format = Json.format[CampaignStepType]
}
