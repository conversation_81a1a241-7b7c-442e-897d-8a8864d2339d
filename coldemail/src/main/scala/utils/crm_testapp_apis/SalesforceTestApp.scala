package utils.crm_testapp_apis

import org.apache.pekko.actor.ActorSystem
import api.accounts.AccountUuid
import api.integrations._
import api.sr_audit_logs.models.{OldProspectDeduplicationColumn, ProspectObjectWithOldProspectDeduplicationColumn}
import api.tags.models.{ProspectTag, ProspectTagUuid}
import api.triggers.{IntegrationModuleType, IntegrationType}
import eventframework.{ProspectObject, ProspectObjectInternal}
import org.joda.time.DateTime
import play.api.Logging
import play.api.libs.json.{JsValue, Json}
import play.api.libs.ws.{WSClient, WSResponse}
import play.api.libs.ws.ahc.AhcWSClient
import scalikejdbc.config.DBs
import utils.testapp.TestAppTrait
import utils.{Helpers, SRLogger}

import scala.concurrent.{ExecutionContext, Future}
import play.api.libs.ws.WSBodyWritables.writeableOf_JsValue


object SalesforceTestApp extends Logging {

  def main(args: Array[String]): Unit = {

    if (args.length > 0) {
      DBs.setupAll()

      val applicationName = args(0)


      applicationName match {


        case "salesforce_contact_creation_check" =>

          implicit lazy val system: ActorSystem = ActorSystem()
          given Logger: SRLogger = new SRLogger("SrLogger")

          object main extends TestAppTrait {


            object ProspectFixtures {

              private val dateTime_now = DateTime.now()
              private val owner_name = "owner_name"
              private val owner_email = "owner_email"
              private val email_domain = "email_domain"
              private val prospect_category_id_custom_3 = 3
              private val prospect_category_label_color = "color"
              private val account_id_34 = Some(34L)
              private val total_open_3 = 3
              private val total_click_4 = 4
              private val flag_32 = Json.obj()
              private val prospect_id_1 = 1
              private val owner_id_35 = 35L
              private val team_id_8 = 8L
              private val first_name = Option("first_name")
              private val last_name = Option("last_name")
              private val email = "email"
              private val custom_fields = Json.obj()
              private val list = Option("list")
              private val job_title = Option("job_title")
              private val company = Option("company")
              private val linkedin_url = Option("linkedin_url")
              private val phone = Option("+************")
              private val city = Option("city")
              private val state = Option("state")
              private val country = Option("country")
              private val timezone = Option("time_zone")
              private val prospect_category = "prospect_category"
              private val last_contacted_at = Option(dateTime_now)
              private val created_at = dateTime_now
              private val owner_uuid = AccountUuid("owner_uuid")
              private val updated_at = dateTime_now


              val prospectObjectInternal: ProspectObjectInternal = ProspectObjectInternal(
                owner_name = owner_name,
                magic_columns = List(),
                owner_email = owner_email,
                email_domain = Some(email_domain),
                invalid_email = None,
                last_contacted_at = None,
                last_replied_at = None,
                last_opened_at = None,
                  last_call_made_at = None,
                list_id = None,
                prospect_category_id_custom = prospect_category_id_custom_3,
                prospect_category_label_color = prospect_category_label_color,
                prospect_source = None,
                prospect_account_id = account_id_34,
                prospect_account = None,
                total_opens = total_open_3,
                total_clicks = total_click_4,
                active_campaigns = None,
                current_campaign_id = None,
                tags = Some(
                  List(
                    ProspectTag(tag_id = 123L, tag = "tag1", tag_uuid = ProspectTagUuid("tags_abcdefg")), ProspectTag(tag_id = 1234L, tag = "tag2", tag_uuid = ProspectTagUuid("tags_qwerty"))
                  )
                ),
                flags = flag_32,
                latest_reply_sentiment = None,
                prospect_account_uuid = None
              )
              val prospectObject: ProspectObject = ProspectObject(
                id = prospect_id_1,
                owner_id = owner_id_35,
                team_id = team_id_8,
                first_name = first_name,
                last_name = last_name,
                email = Some(email),
                custom_fields = custom_fields,
                list = list,
                job_title = job_title,
                company = company,
                linkedin_url = linkedin_url,
                phone = phone,
                phone_2 = None,
                phone_3 = None,
                city = city,
                state = state,
                country = country,
                timezone = timezone,
                prospect_category = prospect_category,
                last_contacted_at = last_contacted_at,
                last_contacted_at_phone = None,
                created_at = created_at,
                internal = prospectObjectInternal,
                latest_reply_sentiment_uuid = None,
                current_step_type = None,
                latest_task_done_at = None,
                prospect_uuid = None,
                owner_uuid = owner_uuid,
                updated_at = updated_at
              )


            }


            def init(): Future[Unit] = {
              val result = for {

                updateUserMappingForm <- Future.fromTry(trigger.findUserMapping(
                  teamId = 585L,
                  integration_type = IntegrationType.SALESFORCE
                ))

                updateFieldMappingForm <- Future.fromTry(trigger.findFieldMapping(
                  teamId = 585L,
                  integration_type = IntegrationType.SALESFORCE,
                  module_type = IntegrationModuleType.CONTACTS
                ))

                accessTokenData: Option[IntegrationTPAccessTokenResponse.FullTokenData] <- tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken(
                  teamId = 585L,
                  integration_type = IntegrationType.SALESFORCE
                )(
                  Logger = Logger,
                  ec = playDefaultExecutionContext,
                  ws = wSClient
                ).map {
                  case Left(eitherData) =>
                    println(s"updateUserMappingForm $updateUserMappingForm")
                    println(s"updateFieldMappingForm $updateFieldMappingForm")
                    println(s"eitherData is  defined $eitherData")
                    None
                  case Right(accessToken) =>
                    Some(accessToken)
                }


                createOrUpdate <- tIntegrationCRMService.createOrUpdateBatchContacts(
                  integrationType = IntegrationType.SALESFORCE,
                  moduleType = IntegrationModuleType.CONTACTS,
                  accessTokenData = accessTokenData.get,
                  contacts = Seq(
                    ProspectObjectWithOldProspectDeduplicationColumn(
                      prospectObject = ProspectFixtures.prospectObject.copy(
                        email = Some("<EMAIL>"),
                        first_name = Some("amarjeet"),
                        last_name = Some("sharma")
                      ),
                      oldProspectDeduplicationColumn = Some(OldProspectDeduplicationColumn(
                        email = Some("<EMAIL>"),
                        phone = None
                      ))
                    )

                  ),
                  fieldMapping = updateFieldMappingForm.get,
                  userMapping = updateUserMappingForm.get,
                  accountId = 392L,
                  teamId = 585L
                )(
                  ws = wSClient,
                  ec = playDefaultExecutionContext,
                  system = system,
                  Logger = Logger
                )

              } yield {
                createOrUpdate
              }

              result.map {
                case Left(value) =>
                  println("Its this flow")
                  value match {
                    case CreateOrUpdateBatchContactsError.CommonCRMAPIError(err) =>
                      println(s"Error occurred CommonCRMAPIError $err")
                    case CreateOrUpdateBatchContactsError.InvalidModuleError(message) =>
                      println(s"Invalid Module Error $message")
                    case CreateOrUpdateBatchContactsError.HubspotFailureBatchError(message, failureRecords) =>
                      println(s"HubspotFailureBatchError message: $message failureRecords:$failureRecords")
                    case CreateOrUpdateBatchContactsError.SalesForceFailureBatchError(message, failureRecords) =>
                      println(s"HubspotFailureBatchError message: $message failureRecords:$failureRecords")
                    case CreateOrUpdateBatchContactsError.CRMFindOneByEmailAndModuleError(err) =>
                      println(s"CRMFindOneByEmailAndModuleError : ${err.message}")
                    case hsf: CreateOrUpdateBatchContactsError.HubspotSearchFailureError =>
                      println(s"HubspotSearchFailureError message: ${hsf.message} failureRecords:${hsf.failureMessages}")
                  }
                case Right(value) =>
                  print("Its Right")
                  println(s"Updated Item :: ${value.toString()}")
              }.recover { case e =>
                println(s"Error Occurred ${e.printStackTrace()}")

              }
            }


          }
          main.init()


        case "salesforce_search_api" =>


          object main {

            implicit lazy val system: ActorSystem = ActorSystem()
            implicit lazy val wSClient: AhcWSClient = AhcWSClient()
            implicit lazy val actorContext: ExecutionContext = system.dispatcher

            def init(): Future[Unit] = {


              val phones = Helpers.seqToCommaSeperatedString(Seq("+91 9860841349", "+91 8208938156"))

              val emails = Helpers.seqToCommaSeperatedString(Seq("<EMAIL>", "<EMAIL>"))
              val soqlQuery = s"SELECT Id, Email, FirstName, LastName, Phone FROM Contact WHERE Phone IN ($phones) OR Email OR linkedin__c in ($emails) "
              val encodedQuery = java.net.URLEncoder.encode(soqlQuery, "UTF-8")
              println(s"encodedQuery is $encodedQuery")
              val baseContactUrl = "https://oneapphq-dev-ed.develop.lightning.force.com"
              val contactUrl = s"$baseContactUrl/services/data/v47.0/query/?q=$encodedQuery"
              val accessToken = "00DdM00000BQbx2!AQEAQAWkAor5RjmHfqXUxgzuciQBRLQ8gAS_QuXWABcCbWbyGJxqvYpKt6jBBjWnSuYEOWOVsoLg05qbhAmjOc_4VH2jDEKF"

              wSClient.url(contactUrl)
                .addHttpHeaders(
                  "Authorization" -> s"Bearer $accessToken",
                  "Content-Type" -> "application/json"
                )
                .get()
                .map(resp => {
                  println("Hello Brother")
                  println(resp.json)
                })
                .recover(e => {
                  println("Error Occurred")
                  println(e.printStackTrace())
                })

            }


          }
          main.init()


        case "salesforce_update_api" =>


          object main {
            implicit lazy val system: ActorSystem = ActorSystem()
            implicit lazy val ws: AhcWSClient = AhcWSClient()
            implicit lazy val actorContext: ExecutionContext = system.dispatcher


            def init(): Unit = {

              var conatrctArr = Json.obj()
              val salseForceContactObj = Json.arr(

                {

                  conatrctArr = conatrctArr ++ Json.obj(

                    "method" -> "POST",
                    "referenceId" -> "NewContact",
                    "url" -> "/services/data/v61.0/sobjects/Contact/",
                    "body" -> Json.obj(
                      "lastname" -> "Doe",
                      "firstname" -> "John",
                      "Phone" -> "1234567890"
                    )
                  )
                  conatrctArr
                }
              )


              val compositeRequestObj = Json.obj(
                "allOrNone" -> false,
                "compositeRequest" -> salseForceContactObj
              )


              val accessToken = "00DdM00000BQbx2!AQEAQNoxCHxoUZjU1T5FXlvy0wLvwxNuiZNjN5r_6G0A9049dnGiPzxm6BNxSl2qFqiUIRz0RDVwKyZ9y5IMWBcQwn3uTuhY"
              val baseContactUrl = "https://oneapphq-dev-ed.develop.my.salesforce.com"
              val contactUrl = s"$baseContactUrl/services/data/v61.0/composite"


              ws.url(contactUrl)
                .withHttpHeaders(
                  "Authorization" -> s"Bearer $accessToken",
                  "Content-Type" -> "application/json"
                )
                .post(compositeRequestObj)
                .map { response =>
                  println(s"Response status: ${response.status}")
                  println(s"Response body: ${response.body}")

                  if (response.status == 201) {
                    println("Account created successfully:")
                    println(Json.prettyPrint(response.json))
                  } else {
                    println(s"Failed to create Account: ${response.status} - ${response.body}")
                  }
                }
                .recover {
                  case ex: Exception =>
                    println(s"An error occurred: ${ex.getMessage}")
                }


            }

          }
          main.init()


        case "salesforce_test_new" =>
          implicit lazy val system: ActorSystem = ActorSystem()
          implicit lazy val wSClient: AhcWSClient = AhcWSClient()
          implicit lazy val actorContext: ExecutionContext = system.dispatcher
          given Logger: SRLogger = new SRLogger("SrLogger")
          object main {
            def batchSearchForProspects(
                                         moduleType: IntegrationModuleType,
                                         accessTokenData: String,
                                         contacts: Seq[ProspectObject],
                                       )(implicit ws: WSClient,
                                         ec: ExecutionContext,
                                         Logger: SRLogger
                                       ): Future[Either[SearchBatchContactsError, Seq[Option[BatchContactResponse]]]] = {

              moduleType match {

                case IntegrationModuleType.LEADS | IntegrationModuleType.CONTACTS =>
                  Future.successful("Contact")
                    .flatMap(salesforceModule => {

                      val soqlQuery = SalesforceOAuth.getSoQLQueryToFetchProspects(contacts, salesforceModule)

                      if (soqlQuery.isEmpty) {
                        Future.failed(new Exception("Please Try Again or contact Support"))
                      } else {
                        val encodedQuery = java.net.URLEncoder.encode(soqlQuery.get, "UTF-8")


                        val baseContactUrl = "https://oneapphq-dev-ed.develop.lightning.force.com"
                        val contactUrl = s"$baseContactUrl/services/data/v47.0/query/?q=$encodedQuery"
                        val accessToken = accessTokenData

                        Logger.appendLogRequestId("SalesforceOauth :: batchSearchForProspects")
                        ws.url(contactUrl)
                          .addHttpHeaders(
                            "Authorization" -> s"Bearer $accessToken",
                            "Content-Type" -> "application/json"
                          )
                          .get()
                          .flatMap(response => {

                            Logger.info(s"${response.json}")


                            /*Status 300 indicates duplicate records found*/
                            if (response.status == 200) {

                              // Logger.info(s"SalesforceOAuth findOneByEmailAndModule response :: ${response.body}")

                              //sample structure {"attributes":{"type":"Contact","url":"/services/data/v20.0/sobjects/Contact/0032w0000025uNZAAY"},"Id":"XXXX","Email":"<EMAIL>"}
                              //                  {"totalSize":4,"done":true,"records":[{"attributes":{"type":"Contact","url":"/services/data/v47.0/sobjects/Contact/003dM000005LIlRQAW"},"Email":"<EMAIL>","FirstName":"Rose","LastName":"Gonzalez","Phone":"(*************"},{"attributes":{"type":"Contact","url":"/services/data/v47.0/sobjects/Contact/003dM000005LIlSQAW"},"Email":"<EMAIL>","FirstName":"Sean","LastName":"Forbes","Phone":"(*************"}


                              Logger.info("response.status is 200")
                              val records = (response.json \ "records").as[Seq[JsValue]]
                              if (records.nonEmpty) {

                                Logger.info("Records are empty")
                                Future.successful(Right(records.map(record => {

                                  val contactEmail = (record \ "Email").asOpt[String]
                                  val contactPhone = (record \ "Phone").asOpt[String]
                                  val contactId = (record \ "Id").asOpt[String]
                                  val contactLinkedin = (record \ "Linkedin").asOpt[String]


                                  Logger.info(s"contactEmail $contactEmail")
                                  Logger.info(s"contactPhone $contactPhone")
                                  Logger.info(s"contactId $contactId")
                                  Logger.info(s"contactLinkedin $contactLinkedin")


                                  if (contactEmail.nonEmpty) {

                                    Logger.info(s"contact Email is nonEmpty $contactEmail")


                                    Some(
                                      BatchContactResponse(
                                        email = contactEmail,
                                        phone = contactPhone,
                                        linkedinUrl = contactLinkedin,
                                        error = None
                                      )


                                    )

                                  } else {

                                    Logger.info(s"SalesforceOAuth batchSearchForProspects not found: ${response.body} :: status: ${response.status} :: email: $contactEmail phone:: $contactPhone url :: $contactUrl")


                                    None


                                  }
                                })))

                              } else {

                                Logger.info(" Records are empty ")

                                Future.successful(Right(Seq()))

                              }


                            } else if (response.status == 404) {

                              Logger.info(s"SalesforceOAuth batchSearchForProspects  404 not found: ${response.body} :: status: ${response.status} :: emails: ${contacts.map(_.email)} :: phones:${contacts.map(_.phone)}url :: $contactUrl")

                              Future.successful(Left(SearchBatchContactsError.CommonCRMAPIError(CommonCRMAPIErrors.NotFoundError("Prospects Not Found"))))


                            } else {

                              Future.successful(Left(SearchBatchContactsError.CommonCRMAPIError(CommonCRMAPIErrors.NotFoundError("Prospects Not Found V2"))))


                            }

                          })(ec)
                      }
                    })(ec)

                case _ => Future.failed(new Exception("Wrong Crm Type"))
              }

            }
          }
          main.batchSearchForProspects(
            moduleType = IntegrationModuleType.CONTACTS,
            accessTokenData = "00DdM00000BQbx2!AQEAQBzBmQtIWuzFg3wQ3oI2v.obQ63jeQkJflDh_DdCiFNW5cvudcSSqeoIgSLYwkW0Ig7Pqllv8SIPVGx5CbaRBNMeKrN_",
            contacts = Seq(ProspectObject(
              id = 20L,
              owner_id = 392L,
              team_id = 599L,

              first_name = Some("Aman"),
              last_name = Some("Yadav"),

              email = None,

              custom_fields = Json.obj(),

              list = None,

              job_title = None,
              company = None,
              linkedin_url = None,
              phone = Some("+91 9235464902"),
              phone_2 = None,
              phone_3 = None,

              city = None,
              state = None,
              country = None,
              timezone = None,

              prospect_category = "Not Categorized", // display name

              last_contacted_at = None,
              last_contacted_at_phone = None,

              created_at = DateTime.now(),


              /* internal columns only for smartreach website, not for public api */
              internal = ProspectObjectInternal(
                magic_columns = List(),

                owner_name = "Shubham",
                owner_email = "<EMAIL>",

                email_domain = Some("smartreach.io"),
                invalid_email = Some(false),

                last_contacted_at = None, // FIXME: remove this from internal in backend and v1/v2 frontend
                last_replied_at = None,
                last_opened_at = None,
                  last_call_made_at = None,

                list_id = None,

                prospect_category_id_custom = 2L,
                prospect_category_label_color = "blue",

                prospect_source = None,


                prospect_account_id = None, //table -> prospect_accounts.id
                prospect_account_uuid = None,
                prospect_account = None, //table -> prospect_accounts.name


                total_opens = 0,
                total_clicks = 0,

                active_campaigns = None,

                /* only in case of specific campaign prospect table in frontend */
                current_campaign_id = None,

                tags = None,
                flags = Json.obj(),
                latest_reply_sentiment = None
              ),
              latest_reply_sentiment_uuid = None,
              current_step_type = None,
              latest_task_done_at = None,

              prospect_uuid = None,
              owner_uuid = AccountUuid("acc_2WNehEB9wfU1kCo55lJROPEBxR6"),
              updated_at = DateTime.now()
            ))
          )


        case "sales_batch_search_emails_new" =>
        //          implicit lazy val system = ActorSystem()
        //          implicit lazy val materializer = ActorMaterializer()
        //          implicit lazy val ws: AhcWSClient = AhcWSClient()
        //          implicit lazy val actorContext: ExecutionContext = system.dispatcher
        //          given Logger: SRLogger= new SRLogger("SrLogger")
        //          object main{
        //
        //
        //            def buildCreateContactObj(
        //                                       moduleType: IntegrationModuleType,
        //                                       prospects: Seq[ProspectObject],
        //                                       fieldMapping: UpdateFieldsMappingForm,
        //                                       userMapping: UpdateUserMappingForm,
        //                                       accountId: Long,
        //                                       teamId: Long) ={
        //
        //
        //              moduleType match {
        //                case IntegrationModuleType.CONTACTS |
        //                     IntegrationModuleType.LEADS =>
        //
        //                  val contactsObj = prospects.map(p => {
        //                    var contactObj = Json.obj()
        //                    fieldMapping.fields.get
        //                      .filter(f => f.sr_field.trim.nonEmpty && f.tp_field.trim.nonEmpty)
        //                      .foreach(f => {
        //                        contactObj = contactObj ++ Json.obj(
        //                          f.tp_field -> ProspectColumnDefUtils._getProspectFieldValue(p = p, field = f)
        //                        )
        //                      })
        //
        //                    //          val owner = findMappedUserByProspectOwnerIdForCRMContactObj(owner_id = p.owner_id, userMapping = userMapping)
        //                    //          if (owner.isDefined) {
        //                    //            contactObj = contactObj ++ Json.obj(
        //                    //              "AccountId" -> owner.get.tp_user_id
        //                    //            )
        //                    //          }
        //
        //                    contactObj ++ Json.obj("referenceId" -> p.id)
        //                  })
        //
        //                  Json.toJson(contactsObj)
        //
        //                case IntegrationModuleType.CANDIDATES =>
        //
        //                  Logger.fatal(s" buildCreateContactObj not supported for module: $moduleType")
        //
        //                  Json.obj()
        //              }
        //            }
        //            def isSuccessResponse(response: WSResponse): Boolean = response.status >= 200 && response.status < 300
        //
        //
        //            def createOrUpdateBatchContacts(moduleType: IntegrationModuleType,
        //                                            accessTokenData: String,
        //                                            contacts: Seq[ProspectObject],
        //                                            fieldMapping: UpdateFieldsMappingForm,
        //                                            userMapping: UpdateUserMappingForm,
        //                                            accountId: Long,
        //                                            teamId: Long,
        //                                            emailNotCompulsoryEnabled: Boolean
        //                                           )(implicit ws: WSClient,
        //                                             ec: ExecutionContext,
        //                                             system: ActorSystem,
        //                                             Logger: SRLogger
        //                                           ): Future[Either[CreateOrUpdateBatchContactsError, Seq[BatchContactResponse]]] = {
        //
        //
        //              Future.successful("Contact")
        //                .flatMap(sfModule => {
        //
        //                  val contactsObj = buildCreateContactObj(
        //                    moduleType = moduleType,
        //                    prospects = contacts,
        //                    fieldMapping = fieldMapping,
        //                    userMapping = userMapping,
        //                    accountId = accountId,
        //                    teamId = teamId
        //                  )
        //
        //
        //                  val baseContactUrl = "https://oneapphq-dev-ed.develop.my.salesforce.com"
        //                  val contactUrl = s"$baseContactUrl/services/data/v41.0/composite"
        //                  val accessToken = accessTokenData
        //
        //                  val contactsObjs = contactsObj.as[Seq[JsObject]]
        //                  var conatrctArr = Json.obj()
        //                  val salseForceContactObj = contactsObjs.map(contact => {
        //
        //                    val email = (contact \ "Email").asOpt[String]
        //
        //                    if (email.isDefined || emailNotCompulsoryEnabled) {
        //
        //                      val referenceId = (contact \ "referenceId").as[Int]
        //                      val body =if(true){
        //                        contact.as[JsObject] - "referenceId"
        //                      }else{
        //                        contact.as[JsObject] - "Email" - "referenceId"
        //                      }
        //                      val url = if(emailNotCompulsoryEnabled){
        //                        s"/services/data/v41.0/sobjects/${sfModule}/"
        //                      }else{
        //                        s"/services/data/v41.0/sobjects/$sfModule/Email/${email.get}"
        //                      }
        //
        //                      conatrctArr = conatrctArr ++ Json.obj(
        //                        "method" -> "POST",
        //                        "url" -> url,
        //                        "referenceId" -> referenceId.toString,
        //                        "body" -> body
        //                      )
        //                    }
        //
        //                    conatrctArr
        //
        //                  })
        //
        //                  println(s"${salseForceContactObj}")
        //
        //                  val compositeRequestObj = Json.obj("compositeRequest" -> salseForceContactObj)
        //
        //                  ws.url(contactUrl)
        //                    .addHttpHeaders(
        //                      "Authorization" -> s"Bearer $accessToken",
        //                      "Content-Type" -> "application/json"
        //                    )
        //                    .post(compositeRequestObj)
        //                    .map(response => {
        //
        //                      if (!isSuccessResponse(response)) {
        //
        //                        Logger.fatal(s"SalesforceOAuth createOrUpdateBatch error: ${response.body}")
        //
        //                        SalesforceAPIResponseStatusCodes.parseStatusCodes(response = response) match {
        //
        //                          case e: SalesforceAPIResponseStatusCodes.CommonCRMAPIError =>
        //                            Left(CreateOrUpdateBatchContactsError.CommonCRMAPIError(err = e.error))
        //
        //                          case e: SalesforceAPIResponseStatusCodes.UnknownError =>
        //                            Left(CreateOrUpdateBatchContactsError.CommonCRMAPIError(err = CommonCRMAPIErrors.UnknownErrorWithResponseBody(
        //                              msg = e.message,
        //                              responseBody = e.responseBody,
        //                              responseCode = e.responseCode
        //                            )))
        //
        //                          case e =>
        //                            Left(CreateOrUpdateBatchContactsError.CommonCRMAPIError(err = CommonCRMAPIErrors.UnknownError(msg = e.message)))
        //                        }
        //
        //                      } else {
        //
        //                        //TODO: Adding resopnse body for better logs need to remove after test
        //                        Logger.info(s"SalesforceOAuth createOrUpdateBatch response :: ${response.body}")
        //
        //                        val compositeResponse = (response.json \ "compositeResponse").as[Seq[JsObject]]
        //
        //                        val responseObject = compositeResponse
        //                          .zipWithIndex
        //                          .map { case (res, index) =>
        //                            //NOTE doing this bcz salesforce composite api treats all contacts objects as individual apis and returns individual api response
        //                            val error = SalesforceOAuth.__parseBatchContactSuccessResponse(errorObj = res)
        //
        //                            //Contacts which arrive in this path comes from ProspectDAOService.find() which is having inner join prospects_emails
        //                            //in the old path email cannot be none but in the new path(email optional) the email field may be none for some prospects
        //                            //TODO: For new path we may need to pass contacts fetched by linkedin_url/phone/company-firstname-lastname) so that we can isolate the cases
        //                            //NOTE: We may need to add org_id to check old/new paths
        //
        //                            BatchContactResponse(
        //                              email = contacts(index).email, //TODO: EMAIL_OPTIONAL check the path and test code (contacts coming fromDb so old path will always have email)
        //                              phone = None,
        //                              linkedinUrl = None,
        //                              error = error
        //                            )
        //                          }
        //
        //                        Right(responseObject)
        //                      }
        //
        //                    })(ec)
        //
        //
        //                })(ec)
        //            }
        //
        //
        //
        //          }
        //
        //          main.createOrUpdateBatchContacts(
        //            moduleType  = IntegrationModuleType.CONTACTS,
        //            accessTokenData = "00DdM00000BQbx2!AQEAQNoxCHxoUZjU1T5FXlvy0wLvwxNuiZNjN5r_6G0A9049dnGiPzxm6BNxSl2qFqiUIRz0RDVwKyZ9y5IMWBcQwn3uTuhY",
        //            contacts = Seq(ProspectObject(
        //              id = 20L,
        //              owner_id = 392L,
        //              team_id = 599L,
        //
        //              first_name = Some("Aman"),
        //              last_name = Some("Yadav"),
        //
        //              email = None,
        //
        //              custom_fields = Json.obj(),
        //
        //              list = None,
        //
        //              job_title = None,
        //              company = None,
        //              linkedin_url = None,
        //              phone = Some("+91 9235464902"),
        //
        //              city   = None,
        //              state= None,
        //              country= None,
        //              timezone= None,
        //
        //              prospect_category = "Not Categorized", // display name
        //
        //              last_contacted_at = None,
        //              created_at = DateTime.now(),
        //
        //
        //              /* internal columns only for smartreach website, not for public api */
        //              internal= ProspectObjectInternal(
        //                ta_id = 599L,
        //
        //                owner_name = "Shubham",
        //                owner_email = "<EMAIL>",
        //
        //                email_domain = Some("smartreach.io"),
        //                invalid_email = Some(false),
        //
        //                last_contacted_at = None, // FIXME: remove this from internal in backend and v1/v2 frontend
        //                last_replied_at = None,
        //                last_opened_at = None,
        //
        //                list_id = None,
        //
        //                prospect_category_id_custom = 2L,
        //                prospect_category_label_color = "blue",
        //
        //                prospect_source = None,
        //
        //
        //                prospect_account_id = None, //table -> prospect_accounts.id
        //                prospect_account_uuid = None,
        //                prospect_account = None, //table -> prospect_accounts.name
        //
        //
        //                total_opens = 0,
        //                total_clicks = 0,
        //
        //                active_campaigns = None,
        //
        //                /* only in case of specific campaign prospect table in frontend */
        //                current_campaign_id = None,
        //
        //                tags = None,
        //                flags = Json.obj(),
        //                latest_reply_sentiment = None
        //              ),
        //              latest_reply_sentiment_uuid= None,
        //              current_step_type= None,
        //              latest_task_done_at= None,
        //
        //              prospect_uuid= None,
        //              owner_uuid = AccountUuid("acc_2WNehEB9wfU1kCo55lJROPEBxR6"),
        //              updated_at = DateTime.now()
        //            )),
        //            fieldMapping = UpdateFieldsMappingForm(
        //              fields = Some(
        //                Seq(
        //                  TriggerFields(
        //                      sr_field = "email",
        //                      tp_field =  "Email",
        //                      field_type= "text",
        //                      field_label= "Email",
        //                      tp_field_label= Some("Email"),
        //                      tp_field_default_value= Some("")
        //                  ),
        //                  TriggerFields(
        //                    sr_field = "first_name",
        //                    tp_field =  "FirstName",
        //                    field_type= "text",
        //                    field_label= "First name",
        //                    tp_field_label= Some("First name"),
        //                    tp_field_default_value= Some("")
        //                  ),
        //                  TriggerFields(
        //                    sr_field = "last_name",
        //                    tp_field =  "LastName",
        //                    field_type= "text",
        //                    field_label= "Last name",
        //                    tp_field_label= Some("Last Name"),
        //                    tp_field_default_value= Some("")
        //                  ),
        //                  TriggerFields(
        //                    sr_field = "list",
        //                    tp_field =  "",
        //                    field_type= "text",
        //                    field_label= "Company",
        //                    tp_field_label= Some(""),
        //                    tp_field_default_value= Some("")
        //                  ),
        //                  TriggerFields(
        //                    sr_field = "state",
        //                    tp_field =  "",
        //                    field_type= "text",
        //                    field_label= "State",
        //                    tp_field_label= Some(""),
        //                    tp_field_default_value= Some("")
        //                  ),
        //                  TriggerFields(
        //                    sr_field = "phone",
        //                    tp_field =  "phone",
        //                    field_type= "text",
        //                    field_label= "Phone",
        //                    tp_field_label= Some(""),
        //                    tp_field_default_value= Some("")
        //                  ),
        //                  TriggerFields(
        //                    sr_field = "job_title",
        //                    tp_field =  "",
        //                    field_type= "text",
        //                    field_label= "Job title",
        //                    tp_field_label= Some(""),
        //                    tp_field_default_value= Some("")
        //                  ),
        //                  TriggerFields(
        //                    sr_field = "country",
        //                    tp_field =  "",
        //                    field_type= "text",
        //                    field_label= "Country",
        //                    tp_field_label= Some(""),
        //                    tp_field_default_value= Some("")
        //                  ),
        //                  TriggerFields(
        //                    sr_field = "linkedin_url",
        //                    tp_field =  "",
        //                    field_type= "text",
        //                    field_label= "LinkedIn",
        //                    tp_field_label= Some(""),
        //                    tp_field_default_value= Some("")
        //                  ),
        //                  TriggerFields(
        //                    sr_field = "timezone",
        //                    tp_field =  "",
        //                    field_type= "text",
        //                    field_label= "Time zone",
        //                    tp_field_label= Some(""),
        //                    tp_field_default_value= Some("")
        //                  )
        //                )
        //              )
        //
        //            ),
        //            userMapping = UpdateUserMappingForm(
        //              users = Some(
        //                Seq(
        //                  TriggerUsers(
        //                    sr_user_id = 392,
        //                    sr_user_email = "<EMAIL>",
        //                    tp_user_id= "005dM000007FS9hQAG",
        //                    tp_user_email = "<EMAIL>"
        //                )
        //                )
        //              )
        //            ),
        //            accountId = 1L,
        //            teamId = 599l,
        //            emailNotCompulsoryEnabled = true
        //          )

        //        case "salesforce_search_api_modified" =>
        //
        //          object main{
        //            implicit lazy val system = ActorSystem()
        //            implicit lazy val materializer = ActorMaterializer()
        //            implicit lazy val ws: AhcWSClient = AhcWSClient()
        //            implicit lazy val actorContext: ExecutionContext = system.dispatcher
        //            given Logger: SRLogger= new SRLogger("SrLogger")
        //
        //            def init() = {
        //
        //              val salesforceModule = "Contact"
        //
        //              case class PO(
        //                           phone : Option[String],
        //                           email : Option[String]
        //                           )
        //
        //              val prospect = PO(
        //                phone = Option("+91 9860841349"),
        //                email = Some("<EMAIL>")
        //              )
        //
        //              val phones = prospect.phone.getOrElse("");
        //              val emails = prospect.email.getOrElse("");
        //
        //              // FixMe : Bring Linkedin Later Here , either we have to create that custom column from smartreach side
        //              val soqlQuery =  if(prospect.phone.nonEmpty && prospect.email.nonEmpty){
        //                s"SELECT Id,Email, FirstName, LastName, Phone FROM ${salesforceModule} WHERE Phone IN ('$phones') OR Email in ('$emails')"
        //              }else if(prospect.email.nonEmpty){
        //                s"SELECT Id, Email, FirstName, LastName, Phone FROM ${salesforceModule}  Email in ($emails) "
        //              }else {
        //                s"SELECT Id, Email, FirstName, LastName, Phone FROM ${salesforceModule}  Phone IN ($phones) "
        //              }
        //              val encodedQuery = java.net.URLEncoder.encode(soqlQuery, "UTF-8")
        //
        //
        //              val baseContactUrl = "https://oneapphq-dev-ed.develop.lightning.force.com"
        //              val contactUrl = s"$baseContactUrl/services/data/v47.0/query/?q=$encodedQuery"
        //              val accessToken = "00DdM00000BQbx2!AQEAQAWkAor5RjmHfqXUxgzuciQBRLQ8gAS_QuXWABcCbWbyGJxqvYpKt6jBBjWnSuYEOWOVsoLg05qbhAmjOc_4VH2jDEKF"
        //
        //              val result = ws.url(contactUrl)
        //                .addHttpHeaders(
        //                  "Authorization" -> s"Bearer $accessToken",
        //                  "Content-Type" -> "application/json"
        //                )
        //                .get()
        //                .flatMap(response => {
        //
        //
        //                  /*Status 300 indicates duplicate records found*/
        //                  if (response.status == 300) {
        //
        //                    println("response is 300")
        //
        //                    /*e.g res ["/services/data/v47.0/sobjects/Lead/00Q6Q00001cubfTUAQ","/services/data/v47.0/sobjects/Lead/00Q6000000iPdk9EAC"]*/
        //                    // Logger.info(s"SalesforceOAuth findOneByEmailAndModule response :: ${response.body}")
        //
        //                    val contactsOrLeads = ( response.json \ "records" ).asOpt[Seq[String]]
        //
        //                    if (contactsOrLeads.isDefined && contactsOrLeads.get.nonEmpty) {
        //
        //                      val contactOrLeadUrl = contactsOrLeads.get.head
        //
        //                      val contactLeadId = contactOrLeadUrl.split("/").last
        //
        //                      Logger.info(s"SalesforceOAuth parseIdFromSalesforceContactOrLeadUrl contactLeadId :: ${contactLeadId}")
        //
        //                      val contactResponse = Some(
        //                        IntegrationContactResponse(
        //                          id = contactLeadId,
        //                          email = prospect.email,
        //                          status = None,
        //                          phone = prospect.phone
        //                        )
        //                      )
        //
        //
        //                        Future.successful(
        //                          Right(
        //                            contactResponse
        //                          )
        //                        )
        //
        //
        //
        //                    } else {
        //
        //                      Logger.fatal(s"SalesforceOAuth findOneByEmailAndModule not found: ${response.body} :: status: ${response.status} :: email: ${prospect.email} url :: ${contactUrl}")
        //
        //                      Future.successful(
        //                        None.asRight[FindOneByEmailAndModuleError]
        //                      )
        //
        //
        //                    }
        //
        //                  } else if (response.status == 200) {
        //
        //                    println("response is 200 ")
        //
        //                    // Logger.info(s"SalesforceOAuth findOneByEmailAndModule response :: ${response.body}")
        //
        //                    //sample structure {"attributes":{"type":"Contact","url":"/services/data/v20.0/sobjects/Contact/0032w0000025uNZAAY"},"Id":"XXXX","Email":"<EMAIL>"}
        //                    //                  {"totalSize":4,"done":true,"records":[{"attributes":{"type":"Contact","url":"/services/data/v47.0/sobjects/Contact/003dM000005LIlRQAW"},"Email":"<EMAIL>","FirstName":"Rose","LastName":"Gonzalez","Phone":"(*************"},{"attributes":{"type":"Contact","url":"/services/data/v47.0/sobjects/Contact/003dM000005LIlSQAW"},"Email":"<EMAIL>","FirstName":"Sean","LastName":"Forbes","Phone":"(*************"}
        //
        //                    println(s"response.json ${response.json}")
        //                    val records = (response.json \ "records").as[Seq[JsValue]]
        //                    println(s"records is ${records.toString()}")
        //                    println(s"records.head is ${records.head}")
        //                    println(s"records.head email is  ${(records.head \ "Email").asOpt[String] }")
        //                    println(s"records.head phone is  ${(records.head \ "Phone").asOpt[String] }")
        //
        //                    if( records.nonEmpty ) {
        //                      val contactEmail = (records.head \ "Email").asOpt[String]
        //                      val contactPhone = (records.head \ "Phone").asOpt[String]
        //                      val contactId = (records.head \ "Id").asOpt[String]
        //                      val leadStatusColumn =  None
        //
        //                      if (contactId.nonEmpty && contactEmail.nonEmpty) {
        //
        //                        Future.successful(
        //                          Right(
        //                            Some(
        //                              IntegrationContactResponse(
        //                                id = contactId.get,
        //                                email = contactEmail,
        //                                status = leadStatusColumn,
        //                                phone = contactPhone
        //                              )
        //                            )
        //                          )
        //                        )
        //
        //                      } else {
        //
        //                        Logger.info(s"SalesforceOAuth findOneByEmailAndModule not found: ${response.body} :: status: ${response.status} :: email: ${prospect.email} url :: ${contactUrl}")
        //
        //                        Future.successful(
        //                          None.asRight[FindOneByEmailAndModuleError]
        //                        )
        //
        //                      }
        //                    } else {
        //
        //                      Future.successful(
        //                        Right(None)
        //                      )
        //
        //                    }
        //
        //
        //                  } else if(response.status == 404) {
        //
        //                    Logger.info(s"SalesforceOAuth findOneByEmailAndModule not found: ${response.body} :: status: ${response.status} :: email: ${prospect.email} url :: ${contactUrl}")
        //                    Future.successful(
        //                      None.asRight[FindOneByEmailAndModuleError]
        //                    )
        //
        //                  } else {
        //
        //                    Logger.fatal(s"SalesforceOAuth findOneByEmailAndModule error: ${response.body} :: status: ${response.status} :: email: ${prospect.email} url :: ${contactUrl}")
        //
        //                    SalesforceAPIResponseStatusCodes.parseStatusCodes(response = response) match {
        //
        //                      case e: SalesforceAPIResponseStatusCodes.CommonCRMAPIError =>
        //                        Future.successful(
        //                          Left(FindOneByEmailAndModuleError.CommonCRMAPIError(err = e.error))
        //                        )
        //
        //                      case e =>
        //                        Future.successful(
        //                          Left(FindOneByEmailAndModuleError.CommonCRMAPIError(err = CommonCRMAPIErrors.UnknownErrorWithResponseBody(
        //                            msg = e.message,
        //                            responseBody = response.body,
        //                            responseCode = response.status
        //                          )))
        //                        )
        //
        //                    }
        //
        //                  }
        //
        //                })
        //                .map(resp => {
        //                  println("Hello Bro")
        //                  println(resp)
        //                })
        //                .recover(e => {
        //                  println("Error Occurred ")
        //                  println(e.printStackTrace())
        //                })
        //
        //            }
        //            }
        //            main.init()


        //        case "salesforceUltimateTestApp" =>
        //
        //
        //
        //
        //          val ACCESS_TOKEN = "00DdM00000BQbx2!AQEAQP964VMQ5Y92eaqctnEPD3SywSM8qdkJaf6XI.35PH76V_G.9DXrXXoIiHXq.rrw1yh2kY4Jea3TtS0jIIGq511jZL0W"
        //          val PROSPECTS: Seq[ProspectObject] = Seq(ProspectObject(
        //                        id = 21L,
        //                        owner_id = 392L,
        //                        team_id = 599L,
        //
        //                        first_name = Some("Rajat"),
        //                        last_name = Some("Popat"),
        //
        //                        email = Some("<EMAIL>"),
        //
        //                        custom_fields = Json.obj(),
        //
        //                        list = None,
        //
        //                        job_title = None,
        //                        company = None,
        //                        linkedin_url = None,
        //                        phone = Some("+91 9235464902"),
        //
        //                        city   = None,
        //                        state= None,
        //                        country= None,
        //                        timezone= None,
        //
        //                        prospect_category = "Not Categorized", // display name
        //
        //                        last_contacted_at = None,
        //                        created_at = DateTime.now(),
        //
        //
        //                        /* internal columns only for smartreach website, not for public api */
        //                        internal= ProspectObjectInternal(
        //                          ta_id = 599L,
        //
        //                          owner_name = "Shubham",
        //                          owner_email = "<EMAIL>",
        //
        //                          email_domain = Some("smartreach.io"),
        //                          invalid_email = Some(false),
        //
        //                          last_contacted_at = None, // FIXME: remove this from internal in backend and v1/v2 frontend
        //                          last_replied_at = None,
        //                          last_opened_at = None,
        //
        //                          list_id = None,
        //
        //                          prospect_category_id_custom = 2L,
        //                          prospect_category_label_color = "blue",
        //
        //                          prospect_source = None,
        //
        //
        //                          prospect_account_id = None, //table -> prospect_accounts.id
        //                          prospect_account_uuid = None,
        //                          prospect_account = None, //table -> prospect_accounts.name
        //
        //
        //                          total_opens = 0,
        //                          total_clicks = 0,
        //
        //                          active_campaigns = None,
        //
        //                          /* only in case of specific campaign prospect table in frontend */
        //                          current_campaign_id = None,
        //
        //                          tags = None,
        //                          flags = Json.obj(),
        //                          latest_reply_sentiment = None
        //                        ),
        //                        latest_reply_sentiment_uuid= None,
        //                        current_step_type= None,
        //                        latest_task_done_at= None,
        //
        //                        prospect_uuid= None,
        //                        owner_uuid = AccountUuid("acc_2WNehEB9wfU1kCo55lJROPEBxR6"),
        //                        updated_at = DateTime.now()
        //                      )
        //                      )
        //          val FIELDMAPPINGFORM =  UpdateFieldsMappingForm(
        //            fields = Some(
        //              Seq(
        //                TriggerFields(
        //                  sr_field = "email",
        //                  tp_field =  "Email",
        //                  field_type= "text",
        //                  field_label= "Email",
        //                  tp_field_label= Some("Email"),
        //                  tp_field_default_value= Some("")
        //                ),
        //                TriggerFields(
        //                  sr_field = "first_name",
        //                  tp_field =  "FirstName",
        //                  field_type= "text",
        //                  field_label= "First name",
        //                  tp_field_label= Some("First name"),
        //                  tp_field_default_value= Some("")
        //                ),
        //                TriggerFields(
        //                  sr_field = "last_name",
        //                  tp_field =  "LastName",
        //                  field_type= "text",
        //                  field_label= "Last name",
        //                  tp_field_label= Some("Last Name"),
        //                  tp_field_default_value= Some("")
        //                ),
        //                TriggerFields(
        //                  sr_field = "list",
        //                  tp_field =  "",
        //                  field_type= "text",
        //                  field_label= "Company",
        //                  tp_field_label= Some(""),
        //                  tp_field_default_value= Some("")
        //                ),
        //                TriggerFields(
        //                  sr_field = "state",
        //                  tp_field =  "",
        //                  field_type= "text",
        //                  field_label= "State",
        //                  tp_field_label= Some(""),
        //                  tp_field_default_value= Some("")
        //                ),
        //                TriggerFields(
        //                  sr_field = "phone",
        //                  tp_field =  "phone",
        //                  field_type= "text",
        //                  field_label= "Phone",
        //                  tp_field_label= Some(""),
        //                  tp_field_default_value= Some("")
        //                ),
        //                TriggerFields(
        //                  sr_field = "job_title",
        //                  tp_field =  "",
        //                  field_type= "text",
        //                  field_label= "Job title",
        //                  tp_field_label= Some(""),
        //                  tp_field_default_value= Some("")
        //                ),
        //                TriggerFields(
        //                  sr_field = "country",
        //                  tp_field =  "",
        //                  field_type= "text",
        //                  field_label= "Country",
        //                  tp_field_label= Some(""),
        //                  tp_field_default_value= Some("")
        //                ),
        //                TriggerFields(
        //                  sr_field = "linkedin_url",
        //                  tp_field =  "",
        //                  field_type= "text",
        //                  field_label= "LinkedIn",
        //                  tp_field_label= Some(""),
        //                  tp_field_default_value= Some("")
        //                ),
        //                TriggerFields(
        //                  sr_field = "timezone",
        //                  tp_field =  "",
        //                  field_type= "text",
        //                  field_label= "Time zone",
        //                  tp_field_label= Some(""),
        //                  tp_field_default_value= Some("")
        //                )
        //              )
        //            )
        //
        //          )
        //          val USERMAPPINGFORM =  UpdateUserMappingForm(
        //            users = Some(
        //              Seq(
        //                TriggerUsers(
        //                  sr_user_id = 392,
        //                  sr_user_email = "<EMAIL>",
        //                  tp_user_id= "005dM000007FS9hQAG",
        //                  tp_user_email = "<EMAIL>"
        //                )
        //              )
        //            )
        //          )
        //          val BASE_CONTACT_URL ="https://oneapphq-dev-ed.develop.my.salesforce.com"
        //
        //            implicit lazy val system = ActorSystem()
        //            implicit lazy val materializer = ActorMaterializer()
        //            implicit lazy val wSClient: AhcWSClient = AhcWSClient()
        //            given Logger: SRLogger= new SRLogger("SrLogger")
        //
        //
        //          object main {
        //
        //
        //
        //            def isSuccessResponse(response: WSResponse): Boolean = response.status >= 200 && response.status < 300
        //
        //
        //            def __parseBatchContactSuccessResponse(errorObj: JsValue)(using Logger: SRLogger): Option[BatchContactResponseTypes] = {
        //
        //              val httpStatusCode = (errorObj \ "httpStatusCode").as[Int]
        //
        //              if (httpStatusCode >= 300) {
        //
        //                val body = (errorObj \ "body").as[Seq[JsValue]]
        //                val errorCode: Option[String] = (body.head \ "errorCode").asOpt[String]
        //                val message = (errorObj \\ "message").headOption.flatMap(_.asOpt[String]).getOrElse("Unknown error")
        //                val errorObjStr = errorObj.toString
        //
        //                errorCode match {
        //
        //                  case None =>
        //                    //{"body":["/services/data/v41.0/sobjects/Lead/00Q2w00000dVbDvEAK","/services/data/v41.0/sobjects/Lead/00Q2w00000dVqiAEAS"],"httpHeaders":{},"httpStatusCode":300,"referenceId":"1731563262"}
        //                    //can be none for this kind of case
        //                    Logger.fatal(s"SalesforceOAuth.__parseBatchContactSuccessResponse failed to get errorCode errorObj - $errorObj")
        //                    Some(
        //                      BatchContactResponseTypes.UnknownError(message = message, errorObj = errorObjStr)
        //                    )
        //
        //                  case Some("INVALID_EMAIL_ADDRESS") =>
        //                    Some(
        //                      BatchContactResponseTypes.InvalidEmailError(message = message, errorObj = errorObjStr)
        //                    )
        //
        //                  case Some("LIMIT_EXCEEDED") =>
        //
        //                    Some(
        //                      BatchContactResponseTypes.TooManyRequestsError(message = message)
        //                    )
        //
        //                  case Some(value) =>
        //                    Some(
        //                      BatchContactResponseTypes.UnknownError(message = message, errorObj = errorObjStr)
        //                    )
        //                }
        //
        //              } else {
        //                None
        //              }
        //
        //            }
        //
        //            def getCRMModuleType(moduleType: IntegrationModuleType): Future[String] = {
        //              moduleType match {
        //
        //                case IntegrationModuleType.CONTACTS => Future.successful("Contact")
        //
        //                case IntegrationModuleType.LEADS => Future.successful("Lead")
        //
        //                case IntegrationModuleType.CANDIDATES => Future.failed(new Exception(s"Unsupported module: $moduleType"))
        //
        //              }
        //            }
        //
        //            def batchSearchForProspects(
        //                                         moduleType: IntegrationModuleType,
        //                                         accessTokenData: String,
        //                                         contacts: Seq[ProspectObject],
        //                                       )(implicit ws: WSClient,
        //                                         ec: ExecutionContext,
        //                                         system: ActorSystem,
        //                                         Logger: SRLogger
        //                                       ): Future[Either[SearchBatchContactsError, Seq[Option[BatchContactResponse]]]] = {
        //
        //              moduleType match {
        //
        //                case  IntegrationModuleType.LEADS | IntegrationModuleType.CONTACTS =>
        //                  getCRMModuleType(moduleType = moduleType)
        //                    .flatMap(salesforceModule => {
        //
        //                      val soqlQuery = SalesforceOAuth.getSoQLQueryToFetchProspects(contacts, salesforceModule)
        //
        //                      if(soqlQuery.isEmpty){
        //                        Future.failed(new Exception("Please Try Again or contact Support"))
        //                      } else {
        //                        val encodedQuery = java.net.URLEncoder.encode(soqlQuery.get, "UTF-8")
        //
        //
        //                        val baseContactUrl = BASE_CONTACT_URL
        //                        val contactUrl = s"$baseContactUrl/services/data/v47.0/query/?q=$encodedQuery"
        //                        val accessToken = accessTokenData
        //
        //                        Logger.appendLogRequestId("SalesforceOauth :: batchSearchForProspects")
        //                        ws.url(contactUrl)
        //                          .addHttpHeaders(
        //                            "Authorization" -> s"Bearer $accessToken",
        //                            "Content-Type" -> "application/json"
        //                          )
        //                          .get()
        //                          .flatMap(response => {
        //
        //                            Logger.info(s"${response.json}")
        //
        //
        //                            /*Status 300 indicates duplicate records found*/
        //                            if (response.status == 200) {
        //
        //                              // Logger.info(s"SalesforceOAuth findOneByEmailAndModule response :: ${response.body}")
        //
        //                              //sample structure {"attributes":{"type":"Contact","url":"/services/data/v20.0/sobjects/Contact/0032w0000025uNZAAY"},"Id":"XXXX","Email":"<EMAIL>"}
        //                              //                  {"totalSize":4,"done":true,"records":[{"attributes":{"type":"Contact","url":"/services/data/v47.0/sobjects/Contact/003dM000005LIlRQAW"},"Email":"<EMAIL>","FirstName":"Rose","LastName":"Gonzalez","Phone":"(*************"},{"attributes":{"type":"Contact","url":"/services/data/v47.0/sobjects/Contact/003dM000005LIlSQAW"},"Email":"<EMAIL>","FirstName":"Sean","LastName":"Forbes","Phone":"(*************"}
        //
        //
        //                              Logger.info("response.status is 200")
        //                              val records = (response.json \ "records").as[Seq[JsValue]]
        //                              if( records.nonEmpty ) {
        //
        //                                Logger.info("Records are empty")
        //                                Future.successful(Right(records.map( record =>{
        //
        //                                  val contactEmail = (record  \ "Email").asOpt[String]
        //                                  val contactPhone = (record  \ "Phone").asOpt[String]
        //                                  val contactId = (record \ "Id").asOpt[String]
        //                                  val contactLinkedin = (record \ "Linkedin").asOpt[String]
        //
        //
        //                                  Logger.info(s"contactEmail ${contactEmail}")
        //                                  Logger.info(s"contactPhone ${contactPhone}")
        //                                  Logger.info(s"contactId ${contactId}")
        //                                  Logger.info(s"contactLinkedin ${contactLinkedin}")
        //
        //
        //                                  if (contactId.nonEmpty && (contactEmail.nonEmpty || contactPhone.nonEmpty || contactLinkedin.nonEmpty)) {
        //
        //                                    Logger.info(s"contact Email is nonEmpty ${contactEmail}")
        //
        //
        //                                    Some(
        //                                      BatchContactResponse(
        //                                        email = contactEmail,
        //                                        phone = contactPhone,
        //                                        linkedinUrl = contactLinkedin,
        //                                        error = None,
        //                                        contactId = contactId
        //                                      )
        //
        //
        //                                    )
        //
        //                                  } else {
        //
        //                                    Logger.info(s"SalesforceOAuth batchSearchForProspects not found: ${response.body} :: status: ${response.status} :: email: ${contactEmail} phone:: ${contactPhone} url :: ${contactUrl}")
        //
        //
        //                                    None
        //
        //
        //                                  }})))
        //
        //                              } else {
        //
        //                                Logger.info(" Records are empty ")
        //
        //                                Future.successful(Right(Seq()))
        //
        //                              }
        //
        //
        //
        //                            } else if(response.status == 404) {
        //
        //                              Logger.info(s"SalesforceOAuth batchSearchForProspects  404 not found: ${response.body} :: status: ${response.status} :: emails: ${contacts.map(_.email)} :: phones:${contacts.map(_.phone)}url :: ${contactUrl}")
        //
        //                              Future.successful(Left(SearchBatchContactsError.CommonCRMAPIError(CommonCRMAPIErrors.NotFoundError("Prospects Not Found"))))
        //
        //
        //                            } else {
        //
        //                              Logger.fatal(s"SalesforceOAuth batchSearchForProspects neither 200 nor 404 error: ${response.body} :: status: ${response.status} :: emails: ${contacts.map(_.email)} :: phones:${contacts.map(_.phone)} ::url :: ${contactUrl}")
        //
        //                              SalesforceAPIResponseStatusCodes.parseStatusCodes(response = response) match {
        //
        //                                case e: SalesforceAPIResponseStatusCodes.CommonCRMAPIError => {
        //
        //                                  Logger.info(s"CommonCRMAPIError ${e.error}")
        //                                  Future.successful(Left(SearchBatchContactsError.CommonCRMAPIError(err = e.error)))
        //
        //                                }
        //
        //                                case e => {
        //                                  Logger.info(s"UnknownErrorWithResponseBody ${e.message}")
        //                                  Future.successful(Left(SearchBatchContactsError.CommonCRMAPIError(err = CommonCRMAPIErrors.UnknownErrorWithResponseBody(
        //                                    msg = e.message,
        //                                    responseBody = response.body,
        //                                    responseCode = response.status
        //                                  )))
        //
        //                                  )
        //                                }
        //
        //                              }
        //
        //                            }
        //
        //                          })(ec)
        //                      }})(ec)
        //              }}
        //
        //            def buildCreateContactObj(
        //                                               moduleType: IntegrationModuleType,
        //                                               prospect: ProspectObject,
        //                                               fieldMapping: UpdateFieldsMappingForm,
        //                                               userMapping: UpdateUserMappingForm,
        //                                               accountId: Long,
        //                                               teamId: Long
        //                                             )(
        //                                               implicit Logger: SRLogger
        //                                             ): JsValue = {
        //
        //              moduleType match {
        //                case IntegrationModuleType.CONTACTS |
        //                     IntegrationModuleType.LEADS =>
        //
        //
        //                  var contactObj = Json.obj()
        //
        //                  fieldMapping.fields.get
        //                    .filter(f => f.sr_field.trim.nonEmpty && f.tp_field.trim.nonEmpty)
        //                    .foreach(f => {
        //                      contactObj = contactObj ++ Json.obj(
        //                        f.tp_field -> ProspectColumnDefUtils._getProspectFieldValue(p = prospect, field = f)
        //                      )
        //                    })
        //
        //                  contactObj = contactObj ++ Json.obj("referenceId" -> prospect.id)
        //
        //
        //                  Json.toJson(contactObj)
        //
        //                case IntegrationModuleType.CANDIDATES =>
        //
        //                  Logger.fatal(s" buildCreateContactObj not supported for module: $moduleType")
        //
        //                  Json.obj()
        //              }
        //
        //            }
        //
        //
        //            def batchSearchAndReturnJsValue(
        //                                             moduleType: IntegrationModuleType,
        //                                             accessTokenData: String,
        //                                             contacts: Seq[ProspectObject],
        //                                             fieldMapping: UpdateFieldsMappingForm,
        //                                             userMapping: UpdateUserMappingForm,
        //                                             emailNotCompulsoryEnabled: Boolean,
        //                                             accountId: Long,
        //                                             teamId: Long
        //                                           )(implicit ws: WSClient,
        //                                             ec: ExecutionContext,
        //                                             system: ActorSystem,
        //                                             Logger: SRLogger
        //                                           ): Future[Either[SearchBatchContactsError, Seq[JsValue]]] = {
        //
        //              val result = batchSearchForProspects(
        //                moduleType = moduleType,
        //                accessTokenData = accessTokenData,
        //                contacts = contacts
        //              )(Logger = Logger.appendLogRequestId("batchSearchForProspects"), ws = ws, ec = ec, system = system) map {
        //                case Left(value) => {
        //                  Logger.info(s"batchSearchAndReturnJsValue :: batchSearchForProspectFailed ${value}")
        //                  Left(value)
        //                }
        //                case Right(value)  => {
        //
        //                  val prospectFieldToContactIdMap = value.filter(_.isDefined).map(_.get).zipWithIndex.flatMap { case (batchContact, index) =>
        //                    val resultingKeyValuePairs = Seq(
        //                      batchContact.email.map(_  -> batchContact.contactId.getOrElse("")),
        //                      batchContact.phone.map(_  -> batchContact.contactId.getOrElse("")),
        //                      batchContact.linkedinUrl.map(_  -> batchContact.contactId.getOrElse(""))
        //                    ).flatten
        //
        //                    resultingKeyValuePairs
        //                  }.toMap
        //
        //                  Logger.info(s"batchSearchAndReturnJsValue :: batchSearchForProspects ${value}")
        //
        //
        //                  val searchEmails = value.filter(_.isDefined).map(_.get.email).filter(_.isDefined).map(_.get.trim.toLowerCase)
        //                  val searchPhones = value.filter(_.isDefined).map(_.get.phone).filter(_.isDefined).map(_.get.trim)
        //                  val searchLinkedinUrl = value.filter(_.isDefined).map(_.get.linkedinUrl).filter(_.isDefined).map(_.get.trim)
        //
        //                  //Contacts which arrive in this path comes from ProspectDAOService.find() which is having inner join prospects_emails
        //                  //in the old path email cannot be none but in the new path(email optional) the email field may be none for some prospects
        //                  //TODO: For new path we may need to pass contacts fetched by linkedin_url/phone/company-firstname-lastname) so that we can isolate the cases
        //                  //NOTE: We may need to add org_id to check old/new paths
        //
        //                  Logger.info(s"searchEmails:: ${searchEmails} :: searchPhones:${searchPhones} ::  searchLinkedinUrl: ${searchLinkedinUrl}")
        //
        //                  //TODO: EMAIL_OPTIONAL check the path and test code (contacts coming fromDb so old path will always have email)
        //                  val newContacts = contacts.filterNot(contact =>
        //                    (contact.email.isDefined && searchEmails.contains(contact.email.get.trim.toLowerCase))
        //                      ||
        //                      (contact.phone.isDefined && searchPhones.contains(contact.phone.get.trim))
        //                      ||
        //                      (contact.linkedin_url.isDefined && searchLinkedinUrl.contains(contact.linkedin_url.get.trim))
        //
        //                  ).distinct.map(c => (c,None))
        //
        //
        //                  Logger.info(s"newContacts ${newContacts}")
        //
        //
        //
        //                  //TODO: EMAIL_OPTIONAL check the path and test code (contacts coming fromDb so old path will always have email)
        //                  val existingContacts = contacts.filter(contact =>
        //                    (contact.email.isDefined && searchEmails.contains(contact.email.get.trim.toLowerCase))
        //                      ||
        //                      (contact.phone.isDefined && searchPhones.contains(contact.phone.get.trim))
        //                      ||
        //                      (contact.linkedin_url.isDefined && searchLinkedinUrl.contains(contact.linkedin_url.get.trim))
        //                  ).distinct.map(c => {
        //
        //                    val contactId = if(c.email.isDefined && prospectFieldToContactIdMap.contains(c.email.get)){
        //                      prospectFieldToContactIdMap.get(c.email.get)
        //                    }else if(c.phone.isDefined && prospectFieldToContactIdMap.contains(c.phone.get)){
        //                      prospectFieldToContactIdMap.get(c.phone.get)
        //                    }else if(c.linkedin_url.isDefined && prospectFieldToContactIdMap.contains(c.linkedin_url.get)) {
        //                      prospectFieldToContactIdMap.get(c.linkedin_url.get)
        //                    }else{
        //                      None
        //                    }
        //                    (c,contactId)
        //                  })
        //
        //                  Logger.info(s"newContacts ${newContacts} :: existingContacts ${existingContacts}")
        //
        //
        //                  Right((newContacts,existingContacts))
        //                }
        //              }
        //
        //              result map {
        //                case Left(value) => Left(value)
        //                case Right(value) =>
        //
        //                  Logger.info(s"result map value is ${value}")
        //
        //
        //
        //                  val newContactsObj = value._1.map(c => {
        //                    buildCreateContactObj(
        //                      moduleType = moduleType,
        //                      prospect = c._1,
        //                      fieldMapping = fieldMapping,
        //                      userMapping = userMapping,
        //                      accountId = accountId,
        //                      teamId = teamId
        //                    )
        //                  })
        //
        //                  Logger.info(s"newContactsObj ${newContactsObj}")
        //
        //                  val existingContactsObj = value._2.map(c => {
        //                    val cobj = buildCreateContactObj(
        //                      moduleType = moduleType,
        //                      prospect = c._1,
        //                      fieldMapping = fieldMapping,
        //                      userMapping = userMapping,
        //                      accountId = accountId,
        //                      teamId = teamId
        //                    )
        //
        //                    val contactIdOpt = c._2
        //
        //                    contactIdOpt match {
        //                      case Some(contactId) => cobj.as[JsObject] ++ Json.obj("contactId" -> contactId)
        //                      case None => cobj
        //                    }
        //
        //
        //                  })
        //
        //                  Logger.info(s"existingContactsObj ${existingContactsObj}")
        //
        //                  val ultimatum = newContactsObj ++ existingContactsObj
        //
        //                  Logger.info(s"ultimatum ${ultimatum}")
        //
        //                  Right(ultimatum)
        //
        //
        //
        //
        //
        //
        //              }
        //            }
        //
        //
        //            def create(
        //                        moduleType: IntegrationModuleType,
        //                        accessTokenData: String,
        //                        contacts: Seq[ProspectObject],
        //                        fieldMapping: UpdateFieldsMappingForm,
        //                        userMapping: UpdateUserMappingForm,
        //                        accountId: Long,
        //                        teamId: Long,
        //                        emailNotCompulsoryEnabled: Boolean
        //                      )(implicit ws: WSClient,
        //                        ec: ExecutionContext,
        //                        system: ActorSystem,
        //                        Logger: SRLogger
        //                      ) = {
        //
        //
        //
        //              batchSearchAndReturnJsValue(
        //                moduleType: IntegrationModuleType,
        //                accessTokenData: String,
        //                contacts: Seq[ProspectObject],
        //                fieldMapping: UpdateFieldsMappingForm,
        //                userMapping: UpdateUserMappingForm,
        //                emailNotCompulsoryEnabled: Boolean,
        //                accountId: Long,
        //                teamId: Long
        //              )(Logger= Logger.appendLogRequestId("batchSearchAndReturnJsValue"),ws = ws,ec =ec,system = system).flatMap {
        //                case Left(value) => value match {
        //                  case SearchBatchContactsError.CommonCRMAPIError(err) =>{
        //                    Logger.info(s"CommonCRMAPIError :: ${err}")
        //                    Future.successful(Left(CreateOrUpdateBatchContactsError.CommonCRMAPIError(err)))
        //                  }
        //                  case SearchBatchContactsError.InvalidModuleError(message) => {
        //                    Logger.info(s"InvalidModuleError :: ${message}")
        //
        //                    Future.successful(Left(CreateOrUpdateBatchContactsError.InvalidModuleError(message)))
        //                  }
        //                  case SearchBatchContactsError.HubspotFailureBatchError(message, failureRecords) =>
        //                    Logger.info(s"HubspotFailureBatchError :: ${message} :: ${failureRecords}")
        //
        //                    Future.successful(Left(CreateOrUpdateBatchContactsError.SalesForceFailureBatchError(message,failureRecords)))
        //                  case SearchBatchContactsError.CRMFindOneByEmailAndModuleError(err) =>
        //                    Logger.info(s"CRMFindOneByEmailAndModuleError ${err}")
        //                    Future.successful(Left(CreateOrUpdateBatchContactsError.CRMFindOneByEmailAndModuleError(err)))
        //
        //                  case SearchBatchContactsError.SalesForceBatchError(message,failureRecords) =>
        //                    Logger.info(s"SalesForceBatchError :: ${message} :: ${failureRecords}")
        //                    Future.successful(Left(CreateOrUpdateBatchContactsError.SalesForceFailureBatchError(message,failureRecords)))
        //
        //
        //                }
        //                case Right(value) =>
        //
        //                  Logger.info(s"batchSearchAndReturnJsValue ${value.toString()}")
        //                  val result = getCRMModuleType(moduleType = moduleType)
        //                    .flatMap(sfModule => {
        //
        //                      //
        //
        //
        //                      val baseContactUrl = BASE_CONTACT_URL
        //                      val contactUrl = s"$baseContactUrl/services/data/v41.0/composite"
        //                      val accessToken = accessTokenData
        //
        //                      val contactsObjs = value
        //                      var conatrctArr = Json.obj()
        //
        //                      Logger.info(s"contactsObjs ${contactsObjs}")
        //                      val salseForceContactObj = contactsObjs.map(contact => {
        //
        //                        Logger.info(s"contact ${contact}")
        //
        //                        val email = (contact \ "Email").asOpt[String]
        //                        val contactId = (contact \ "contactId").asOpt[String]
        //
        //                        Logger.info(s"email ${email}")
        //
        //                        if (email.isDefined || emailNotCompulsoryEnabled) {
        //
        //                          val referenceId = (contact \ "referenceId").asOpt[Int]
        //                          Logger.info(s"referenceId ${referenceId}")
        //
        //                          val body =if(emailNotCompulsoryEnabled){
        //                            contact.as[JsObject] - "referenceId" - "contactId"
        //                          }else{
        //                            contact.as[JsObject] - "Email" - "referenceId"
        //                          }
        //
        //                          Logger.info(s"body ${body}")
        //                          val url = if(emailNotCompulsoryEnabled){
        //
        //                            if(contactId.isDefined) {
        //                              s"/services/data/v41.0/sobjects/${sfModule}/${contactId.get}"
        //                            }else{
        //                              s"/services/data/v41.0/sobjects/${sfModule}/"
        //                            }
        //                          }else{
        //                            s"/services/data/v41.0/sobjects/$sfModule/Email/${email.get}"
        //                          }
        //
        //                          Logger.info(s"url ${url}")
        //
        //
        //
        //                          conatrctArr = conatrctArr ++ Json.obj(
        //                            "method" -> (if(contactId.isDefined && contactId.get.trim.nonEmpty) "PATCH" else "POST"),
        //                            "url" -> url,
        //                            "body" -> body
        //                          ) ++ referenceId.map(id => Json.obj("referenceId" -> id.toString)).getOrElse(Json.obj())
        //
        //
        //                        }
        //
        //                        conatrctArr
        //
        //                      })
        //
        //                      Logger.info(s"salseForceContactObj ${salseForceContactObj}")
        //
        //
        //
        //                      val compositeRequestObj = Json.obj("compositeRequest" -> salseForceContactObj)
        //
        //                      Logger.info(s"compositeRequestObj ${compositeRequestObj}")
        //
        //
        //                      ws.url(contactUrl)
        //                        .addHttpHeaders(
        //                          "Authorization" -> s"Bearer $accessToken",
        //                          "Content-Type" -> "application/json"
        //                        )
        //                        .post(compositeRequestObj)
        //                        .map(response => {
        //
        //                          Logger.info(s"response is ${response.json}")
        //
        //                          val eitherResult = if (!isSuccessResponse(response)) {
        //
        //                            Logger.fatal(s"SalesforceOAuth createOrUpdateBatch error: ${response.body}")
        //
        //                            SalesforceAPIResponseStatusCodes.parseStatusCodes(response = response) match {
        //
        //                              case e: SalesforceAPIResponseStatusCodes.CommonCRMAPIError =>
        //                                Left(CreateOrUpdateBatchContactsError.CommonCRMAPIError(err = e.error))
        //
        //                              case e: SalesforceAPIResponseStatusCodes.UnknownError =>
        //                                Left(CreateOrUpdateBatchContactsError.CommonCRMAPIError(err = CommonCRMAPIErrors.UnknownErrorWithResponseBody(
        //                                  msg = e.message,
        //                                  responseBody = e.responseBody,
        //                                  responseCode = e.responseCode
        //                                )))
        //
        //                              case e =>
        //                                Left(CreateOrUpdateBatchContactsError.CommonCRMAPIError(err = CommonCRMAPIErrors.UnknownError(msg = e.message)))
        //                            }
        //
        //                          } else {
        //
        //                            //TODO: Adding resopnse body for better logs need to remove after test
        //                            Logger.info(s"SalesforceOAuth createOrUpdateBatch response :: ${response.body}")
        //
        //                            val compositeResponse = (response.json \ "compositeResponse").as[Seq[JsObject]]
        //
        //                            val responseObject = compositeResponse
        //                              .zipWithIndex
        //                              .map { case (res, index) =>
        //                                //NOTE doing this bcz salesforce composite api treats all contacts objects as individual apis and returns individual api response
        //                                val error = __parseBatchContactSuccessResponse(errorObj = res)
        //
        //                                //Contacts which arrive in this path comes from ProspectDAOService.find() which is having inner join prospects_emails
        //                                //in the old path email cannot be none but in the new path(email optional) the email field may be none for some prospects
        //                                //TODO: For new path we may need to pass contacts fetched by linkedin_url/phone/company-firstname-lastname) so that we can isolate the cases
        //                                //NOTE: We may need to add org_id to check old/new paths
        //
        //                                BatchContactResponse(
        //                                  email = contacts(index).email, //TODO: EMAIL_OPTIONAL check the path and test code (contacts coming fromDb so old path will always have email)
        //                                  phone = contacts(index).phone,
        //                                  linkedinUrl = contacts(index).linkedin_url,
        //                                  error = error
        //                                )
        //                              }
        //
        //                            Right(responseObject)
        //                          }
        //
        //                          Logger.info(s"eitherResult is ${eitherResult}")
        //                          eitherResult
        //
        //                        })(ec)
        //
        //
        //                    })(ec)
        //
        //                  result
        //              }(ec)
        //            }
        //          }
        //
        //
        //           val actorContext: ExecutionContext = system.dispatcher
        //
        //          main.create(
        //            moduleType = IntegrationModuleType.CONTACTS,
        //            accessTokenData = ACCESS_TOKEN ,
        //            contacts = PROSPECTS,
        //            fieldMapping = FIELDMAPPINGFORM,
        //            userMapping = USERMAPPINGFORM,
        //            accountId = 392L ,
        //            teamId = 599L,
        //            emailNotCompulsoryEnabled = true
        //          )(ws = wSClient, ec = actorContext,system = system, Logger = Logger).map{
        //            case Left(value) => println(value)
        //            case Right(value) => print(value)
        //          }(actorContext)


        case "salesforce_contact_test_api" =>

          implicit lazy val system: ActorSystem = ActorSystem()
          given Logger: SRLogger = new SRLogger("SrLogger")

          object main extends TestAppTrait {


            object ProspectFixtures {

              private val dateTime_now = DateTime.now()
              private val owner_name = "owner_name"
              private val owner_email = "owner_email"
              private val email_domain = "email_domain"
              private val prospect_category_id_custom_3 = 3
              private val prospect_category_label_color = "color"
              private val account_id_34 = Some(34L)
              private val total_open_3 = 3
              private val total_click_4 = 4
              private val flag_32 = Json.obj()
              private val prospect_id_1 = 1
              private val owner_id_35 = 35L
              private val team_id_8 = 8L
              private val first_name = Option("first_name")
              private val last_name = Option("last_name")
              private val email = "email"
              private val custom_fields = Json.obj()
              private val list = Option("list")
              private val job_title = Option("job_title")
              private val company = Option("company")
              private val linkedin_url = Option("linkedin_url")
              private val phone = Option("+************")
              private val city = Option("city")
              private val state = Option("state")
              private val country = Option("country")
              private val timezone = Option("time_zone")
              private val prospect_category = "prospect_category"
              private val last_contacted_at = Option(dateTime_now)
              private val created_at = dateTime_now
              private val owner_uuid = AccountUuid("owner_uuid")
              private val updated_at = dateTime_now


              val prospectObjectInternal: ProspectObjectInternal = ProspectObjectInternal(
                owner_name = owner_name,
                owner_email = owner_email,
                magic_columns = List(),
                email_domain = Some(email_domain),
                invalid_email = None,
                last_contacted_at = None,
                last_replied_at = None,
                last_opened_at = None,
                  last_call_made_at = None,
                list_id = None,
                prospect_category_id_custom = prospect_category_id_custom_3,
                prospect_category_label_color = prospect_category_label_color,
                prospect_source = None,
                prospect_account_id = account_id_34,
                prospect_account = None,
                total_opens = total_open_3,
                total_clicks = total_click_4,
                active_campaigns = None,
                current_campaign_id = None,
                tags = Some(
                  List(
                    ProspectTag(tag_id = 123L, tag = "tag1", tag_uuid = ProspectTagUuid("tags_abcdefg")), ProspectTag(tag_id = 1234L, tag = "tag2", tag_uuid = ProspectTagUuid("tags_qwerty"))
                  )
                ),
                flags = flag_32,
                latest_reply_sentiment = None,
                prospect_account_uuid = None
              )
              val prospectObject: ProspectObject = ProspectObject(
                id = prospect_id_1,
                owner_id = owner_id_35,
                team_id = team_id_8,
                first_name = first_name,
                last_name = last_name,
                email = Some(email),
                custom_fields = custom_fields,
                list = list,
                job_title = job_title,
                company = company,
                linkedin_url = linkedin_url,
                phone = phone,
                phone_2 = None,
                phone_3 = None,
                city = city,
                state = state,
                country = country,
                timezone = timezone,
                prospect_category = prospect_category,
                last_contacted_at = last_contacted_at,
                last_contacted_at_phone = None,
                created_at = created_at,
                internal = prospectObjectInternal,
                latest_reply_sentiment_uuid = None,
                current_step_type = None,
                latest_task_done_at = None,
                prospect_uuid = None,
                owner_uuid = owner_uuid,
                updated_at = updated_at
              )


            }

            def isSuccessResponse(response: WSResponse): Boolean = response.status >= 200 && response.status < 300


            def init(): Future[Unit] = {
              val result: Future[Seq[Either[CreateOrUpdateBatchContactsError, BatchContactResponse]]] = for {
                accessTokenData: Option[IntegrationTPAccessTokenResponse.FullTokenData] <- tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken(
                  teamId = 585L,
                  integration_type = IntegrationType.SALESFORCE
                )(
                  Logger = Logger,
                  ec = playDefaultExecutionContext,
                  ws = wSClient
                ).map {
                  case Left(eitherData) =>
                    None
                  case Right(accessToken) =>
                    Some(accessToken)
                }

                baseContactUrl = SalesforceOAuth.getBaseUrl(accessTokenData.get)

                contactUrl = s"$baseContactUrl/services/data/v41.0/composite"


                createOrUpdate: Seq[Either[CreateOrUpdateBatchContactsError, BatchContactResponse]] <- salesforceApi.batchSearchWithEmails(
                  accessToken = accessTokenData.get.access_token,
                  contactUrl = contactUrl,
                  contactsObjs = Seq(OldAndNewProspectObject(
                    oldProspectObject = OldProspectObject(ProspectObject.dummyProspectObj.copy(email = Some("<EMAIL>"))),
                    newProspectObject = NewProspectObject(ProspectObject.dummyProspectObj.copy(email = Some("<EMAIL>")))
                  ),
                    OldAndNewProspectObject(
                      oldProspectObject = OldProspectObject(ProspectObject.dummyProspectObj.copy(email = Some("<EMAIL>"))),
                      newProspectObject = NewProspectObject(ProspectObject.dummyProspectObj.copy(email = Some("<EMAIL>")))
                    ),
                    OldAndNewProspectObject(
                      oldProspectObject = OldProspectObject(ProspectObject.dummyProspectObj.copy(email = Some("<EMAIL>"))),
                      newProspectObject = NewProspectObject(ProspectObject.dummyProspectObj.copy(email = Some("<EMAIL>")))
                    ),
                    OldAndNewProspectObject(
                      oldProspectObject = OldProspectObject(ProspectObject.dummyProspectObj.copy(email = Some("<EMAIL>"))),
                      newProspectObject = NewProspectObject(ProspectObject.dummyProspectObj.copy(email = Some("<EMAIL>")))
                    ),
                    OldAndNewProspectObject(
                      oldProspectObject = OldProspectObject(ProspectObject.dummyProspectObj.copy(email = Some("<EMAIL>"))),
                      newProspectObject = NewProspectObject(ProspectObject.dummyProspectObj.copy(email = Some("<EMAIL>")))
                    ),
                    OldAndNewProspectObject(
                      oldProspectObject = OldProspectObject(ProspectObject.dummyProspectObj.copy(email = Some("<EMAIL>"))),
                      newProspectObject = NewProspectObject(ProspectObject.dummyProspectObj.copy(email = Some("<EMAIL>")))
                    ),
                    OldAndNewProspectObject(
                      oldProspectObject = OldProspectObject(ProspectObject.dummyProspectObj.copy(email = Some("<EMAIL>"))),
                      newProspectObject = NewProspectObject(ProspectObject.dummyProspectObj.copy(email = Some("<EMAIL>")))
                    )),
                  isSuccessResponse = isSuccessResponse,
                  sfModule = "Contact"
                )(
                  ws = wSClient,
                  ec = playDefaultExecutionContext,
                  Logger = Logger
                )

              } yield {
                createOrUpdate
              }

              result.map { resp =>
                println(s"Result from search ${resp}")
              }.recover { case e =>
                println(s"Error Occurred ${e.printStackTrace()}")

              }
            }


          }
          main.init()


      }

    }
  }
}
