package utils.crm_testapp_apis

import org.apache.pekko.actor.ActorSystem
import api.accounts.AccountUuid
import api.integrations.{CreateOrUpdateBatchContactsError, IntegrationTPAccessTokenResponse}
import api.sr_audit_logs.models.{OldProspectDeduplicationColumn, ProspectObjectWithOldProspectDeduplicationColumn}
import api.tags.models.{ProspectTag, ProspectTagUuid}
import api.triggers.{IntegrationModuleType, IntegrationType}
import eventframework.{ProspectObject, ProspectObjectInternal}
import org.joda.time.DateTime
import play.api.Logging
import play.api.libs.json.Json
import play.api.libs.ws.ahc.AhcWSClient
import scalikejdbc.config.DBs
import utils.SRLogger
import utils.testapp.TestAppTrait

import scala.concurrent.{ExecutionContext, Future}
import play.api.libs.ws.WSBodyWritables.writeableOf_JsValue


object PipedriveTestApp extends  Logging {

  def main(args: Array[String]): Unit = {

    if (args.length > 0) {
      DBs.setupAll()

      val applicationName = args(0)


      applicationName match {

        /*
         Example case to search on the basis of email or phone or linkedin
         */
        case "pipedrive_create_api" =>

          object main {

            implicit lazy val system: ActorSystem = ActorSystem()
            implicit lazy val wSClient: AhcWSClient = AhcWSClient()
            implicit lazy val actorContext: ExecutionContext = system.dispatcher

            def init(): Future[Unit] = {

              /* https://developers.hubspot.com/docs/api/crm/contacts */
              val contactUrl = "https://api.pipedrive.com/v1/persons"

              val contactObj  = Json.obj(
                "name" -> "Sasuke uchiha",
                "phone" -> Seq(
                  Json.obj(
                    "value" -> "+91 9860841349",
                    "primary" ->"true",
                    "label" -> "mobile"
                  )
                )
              )

              val accessToken = "f8351499d7ce169d765f5415300a8d3e92f4afb6"

              wSClient.url(contactUrl + s"?api_token=$accessToken")
                .addHttpHeaders(
                  "Content-Type" -> "application/json"
                )
                .post(contactObj)
                .map(rs => {
                  println(rs.json)
                })
            }


          }
          main.init()


        case "pipedrive_create_linkedin_url_api" =>

          object main {

            implicit lazy val system: ActorSystem = ActorSystem()
            implicit lazy val wSClient: AhcWSClient = AhcWSClient()
            implicit lazy val actorContext: ExecutionContext = system.dispatcher

            def init(): Future[Unit] = {


              val accessToken = "f8351499d7ce169d765f5415300a8d3e92f4afb6"

              //              wSClient.url(getPersonFieldsUrl + s"?api_token=${accessToken}")
//                .addHttpHeaders(
//                  "Content-Type" -> "application/json"
//                )
//                .get()
//                .map(rs => {
//                  println(rs.json \ "data")
//                })



              val contactUrl = "https://api.pipedrive.com/v1/persons"

              val contactObj  = Json.obj(
                "name" -> "Sasuke",
                 "linkedin_url" -> "https://www.linkedin.com/in/shubham-kudekar/"
              )


              wSClient.url(contactUrl + s"?api_token=$accessToken")
                .addHttpHeaders(
                  "Content-Type" -> "application/json"
                )
                .post(contactObj)
                .map(rs => {
                  println(rs.json)
                })
            }


          }
          main.init()

        case "pipedrive_search_api" =>

          object main {

            implicit lazy val system: ActorSystem = ActorSystem()
            implicit lazy val wSClient: AhcWSClient = AhcWSClient()
            implicit lazy val actorContext: ExecutionContext = system.dispatcher

            def init(): Future[Unit] = {

              /* https://developers.hubspot.com/docs/api/crm/contacts */
              val searchUrl = "https://api.pipedrive.com/v1/persons/search"

              val term = "+919860841349"
              val fields = "phone"

              val accessToken = "f8351499d7ce169d765f5415300a8d3e92f4afb6"

              wSClient.url(searchUrl + s"?api_token=$accessToken&term=$term&fields=$fields&exact_match=false")
                .addHttpHeaders(
                  "Content-Type" -> "application/json"
                )
                .get()
                .map(rs => {
                  println(rs.json)
                })
            }


          }
          main.init()


        case "pipedrive_search_test" =>
           object main {

             implicit lazy val system: ActorSystem = ActorSystem()
             implicit lazy val wSClient: AhcWSClient = AhcWSClient()
             implicit lazy val actorContext: ExecutionContext = system.dispatcher

             def init(): Future[Unit] = {

               val searchUrl = "https://api.pipedrive.com/v1/personFields"

               val accessToken = "7c6c596d930c505cb03db0630c4d2a37dd930b44"

               wSClient.url(searchUrl)
                 .addHttpHeaders(
                   "Content-Type" -> "application/json",
                   "Authorization" -> s"Bearer $accessToken"
                 )
                 .get()
                 .map(rs => {
                   println(rs.json)
                 })
             }


           }
           main.init()


        case "pipedrive_contact_creation_check" =>

          implicit lazy val system: ActorSystem = ActorSystem()
          given Logger: SRLogger = new SRLogger("SrLogger")

          object main extends  TestAppTrait {


            object ProspectFixtures {

              private val dateTime_now = DateTime.now()
              private val owner_name = "owner_name"
              private val owner_email = "owner_email"
              private val email_domain = "email_domain"
              private val prospect_category_id_custom_3 = 3
              private val prospect_category_label_color = "color"
              private val account_id_34 = Some(34L)
              private val total_open_3 = 3
              private val total_click_4 = 4
              private val flag_32 = Json.obj()
              private val prospect_id_1 = 1
              private val owner_id_35 = 35L
              private val team_id_8 = 8L
              private val first_name = Option("first_name")
              private val last_name = Option("last_name")
              private val email = "email"
              private val custom_fields = Json.obj()
              private val list = Option("list")
              private val job_title = Option("job_title")
              private val company = Option("company")
              private val linkedin_url = Option("linkedin_url")
              private val phone = Option("+************")
              private val city = Option("city")
              private val state = Option("state")
              private val country = Option("country")
              private val timezone = Option("time_zone")
              private val prospect_category = "prospect_category"
              private val last_contacted_at = Option(dateTime_now)
              private val created_at = dateTime_now
              private val owner_uuid = AccountUuid("owner_uuid")
              private val updated_at = dateTime_now


              val prospectObjectInternal: ProspectObjectInternal = ProspectObjectInternal(
                owner_name = owner_name,
                magic_columns = List(),
                owner_email = owner_email,
                email_domain = Some(email_domain),
                invalid_email = None,
                last_contacted_at = None,
                last_replied_at = None,
                last_opened_at = None,
                  last_call_made_at = None,
                list_id = None,
                prospect_category_id_custom = prospect_category_id_custom_3,
                prospect_category_label_color = prospect_category_label_color,
                prospect_source = None,
                prospect_account_id = account_id_34,
                prospect_account = None,
                total_opens = total_open_3,
                total_clicks = total_click_4,
                active_campaigns = None,
                current_campaign_id = None,
                tags = Some(
                  List(
                    ProspectTag(tag_id = 123L, tag = "tag1", tag_uuid = ProspectTagUuid("tags_abcdefg")), ProspectTag(tag_id = 1234L, tag = "tag2", tag_uuid = ProspectTagUuid("tags_qwerty"))
                  )
                ),
                flags = flag_32,
                latest_reply_sentiment = None,
                prospect_account_uuid = None
              )
              val  prospectObject: ProspectObject = ProspectObject(
                id = prospect_id_1,
                owner_id = owner_id_35,
                team_id = team_id_8,
                first_name = first_name,
                last_name = last_name,
                email = Some(email),
                custom_fields = custom_fields,
                list = list,
                job_title = job_title,
                company = company,
                linkedin_url = linkedin_url,
                phone = phone,
                phone_2 = None,
                phone_3 = None,
                city = city,
                state = state,
                country = country,
                timezone = timezone,
                prospect_category = prospect_category,
                last_contacted_at = last_contacted_at,
                last_contacted_at_phone = None,
                created_at = created_at,
                internal = prospectObjectInternal,
                latest_reply_sentiment_uuid = None,
                current_step_type = None,
                latest_task_done_at = None,
                prospect_uuid = None,
                owner_uuid = owner_uuid,
                updated_at = updated_at
              )


            }



            def init(): Future[Unit] = {
              val result = for {

                updateUserMappingForm <- Future.fromTry(trigger.findUserMapping(
                  teamId = 585L,
                  integration_type = IntegrationType.PIPEDRIVE
                ))

                updateFieldMappingForm <- Future.fromTry(trigger.findFieldMapping(
                  teamId = 585L,
                  integration_type = IntegrationType.PIPEDRIVE,
                  module_type = IntegrationModuleType.CONTACTS
                ))

                accessTokenData: Option[IntegrationTPAccessTokenResponse.FullTokenData] <- tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken(
                  teamId = 585L,
                  integration_type = IntegrationType.PIPEDRIVE
                )(
                  Logger = Logger,
                  ec = playDefaultExecutionContext,
                  ws = wSClient
                ).map {
                  case Left(eitherData) =>
                    println(s"updateUserMappingForm $updateUserMappingForm")
                    println(s"updateFieldMappingForm $updateFieldMappingForm")
                    println(s"eitherData is  defined $eitherData")
                    None
                  case Right(accessToken) =>
                    Some(accessToken)
                }


                createOrUpdate <- tIntegrationCRMService.createOrUpdateBatchContacts(
                  integrationType = IntegrationType.PIPEDRIVE,
                  moduleType = IntegrationModuleType.CONTACTS,
                  accessTokenData = accessTokenData.get,
                  contacts = Seq(
                    ProspectObjectWithOldProspectDeduplicationColumn(
                      prospectObject = ProspectFixtures.prospectObject.copy(
                        email = Some("<EMAIL>"),
                        first_name = Some("robin"),
                        last_name =Some("sharma")
                      ),
                      oldProspectDeduplicationColumn = Some(OldProspectDeduplicationColumn(
                        email = Some("<EMAIL>"),
                        phone = None
                      ))
                    )

                  ),
                  fieldMapping = updateFieldMappingForm.get,
                  userMapping = updateUserMappingForm.get,
                  accountId = 392L,
                  teamId = 585L
                )(
                  ws = wSClient,
                  ec = playDefaultExecutionContext,
                  system = system,
                  Logger = Logger
                )

              } yield {
                createOrUpdate
              }

              result.map {
                case Left(value) =>
                  println("Its this flow")
                  value match {
                    case CreateOrUpdateBatchContactsError.CommonCRMAPIError(err) =>
                      println(s"Error occurred CommonCRMAPIError $err")
                    case CreateOrUpdateBatchContactsError.InvalidModuleError(message) =>
                      println(s"Invalid Module Error $message")
                    case CreateOrUpdateBatchContactsError.HubspotFailureBatchError(message, failureRecords) =>
                      println(s"HubspotFailureBatchError message: $message failureRecords:$failureRecords")
                    case CreateOrUpdateBatchContactsError.SalesForceFailureBatchError(message, failureRecords) =>
                      println(s"HubspotFailureBatchError message: $message failureRecords:$failureRecords")
                    case CreateOrUpdateBatchContactsError.CRMFindOneByEmailAndModuleError(err) =>
                      println(s"CRMFindOneByEmailAndModuleError : ${err.message}")
                    case hsf: CreateOrUpdateBatchContactsError.HubspotSearchFailureError =>
                      println(s"HubspotSearchFailureError message: ${hsf.message} failureRecords:${hsf.failureMessages}")
                  }
                case Right(value) =>
                  print("Its Right")
                  println(s"Updated Item :: ${value.toString()}")
              }.recover{case e =>
                println(s"Error Occurred ${e.printStackTrace()}")

              }
            }


          }
          main.init()


      }




      }

  }
}

