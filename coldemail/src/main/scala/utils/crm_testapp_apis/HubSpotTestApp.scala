package utils.crm_testapp_apis

import org.apache.pekko.actor.ActorSystem
import api.accounts.AccountUuid
import api.integrations._
import api.integrations.crmapis.HubspotApi
import api.integrations.models.hubspot.HubspotAPIResponseStatusCodes
import api.sr_audit_logs.models.{OldProspectDeduplicationColumn, ProspectObjectWithOldProspectDeduplicationColumn}
import api.tags.models.{ProspectTag, ProspectTagUuid}
import api.triggers
import api.triggers.{IntegrationModuleType, IntegrationType}
import eventframework.{ProspectObject, ProspectObjectInternal}
import org.joda.time.DateTime
import play.api.Logging
import play.api.libs.json._
import play.api.libs.ws.ahc.AhcWSClient
import play.api.libs.ws.{WSClient, WSResponse}
import scalikejdbc.config.DBs
import utils.testapp.TestAppTrait
import utils.{FutureUtils, SRLogger}

import scala.concurrent.duration.{DurationInt, FiniteDuration}
import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}
import play.api.libs.ws.WSBodyWritables.writeableOf_JsValue




object HubSpotTestApp extends  Logging {

  def main(args: Array[String]): Unit = {

    if (args.length > 0) {
      DBs.setupAll()

      val applicationName = args(0)


      applicationName match {

        /*
         Example case to search on the basis of email or phone or linkedin
         */

        case "hubspot_old_search_api" =>



          given Logger: SRLogger = new SRLogger("SrLogger")
          object main  extends  TestAppTrait {


            object ProspectFixtures {

              private val dateTime_now = DateTime.now()
              private val owner_name = "owner_name"
              private val owner_email = "owner_email"
              private val email_domain = "email_domain"
              private val prospect_category_id_custom_3 = 3
              private val prospect_category_label_color = "color"
              private val account_id_34 = Some(34L)
              private val total_open_3 = 3
              private val total_click_4 = 4
              private val flag_32 = Json.obj()
              private val prospect_id_1 = 1
              private val owner_id_35 = 35L
              private val team_id_8 = 8L
              private val first_name = Option("first_name")
              private val last_name = Option("last_name")
              private val email = "email"
              private val custom_fields = Json.obj()
              private val list = Option("list")
              private val job_title = Option("job_title")
              private val company = Option("company")
              private val linkedin_url = Option("linkedin_url")
              private val phone = Option("+************")
              private val city = Option("city")
              private val state = Option("state")
              private val country = Option("country")
              private val timezone = Option("time_zone")
              private val prospect_category = "prospect_category"
              private val last_contacted_at = Option(dateTime_now)
              private val created_at = dateTime_now
              private val owner_uuid = AccountUuid("owner_uuid")
              private val updated_at = dateTime_now


              val prospectObjectInternal: ProspectObjectInternal = ProspectObjectInternal(
                owner_name = owner_name,
                owner_email = owner_email,
                email_domain = Some(email_domain),
                magic_columns = List(),
                invalid_email = None,
                last_contacted_at = None,
                last_replied_at = None,
                last_opened_at = None,
                  last_call_made_at = None,
                list_id = None,
                prospect_category_id_custom = prospect_category_id_custom_3,
                prospect_category_label_color = prospect_category_label_color,
                prospect_source = None,
                prospect_account_id = account_id_34,
                prospect_account = None,
                total_opens = total_open_3,
                total_clicks = total_click_4,
                active_campaigns = None,
                current_campaign_id = None,
                tags = Some(
                  List(
                    ProspectTag(tag_id = 123L, tag = "tag1", tag_uuid = ProspectTagUuid("tags_abcdefg")), ProspectTag(tag_id = 1234L, tag = "tag2", tag_uuid = ProspectTagUuid("tags_qwerty"))
                  )
                ),
                flags = flag_32,
                latest_reply_sentiment = None,
                prospect_account_uuid = None
              )
              val  prospectObject: ProspectObject = ProspectObject(
                id = prospect_id_1,
                owner_id = owner_id_35,
                team_id = team_id_8,
                first_name = first_name,
                last_name = last_name,
                email = Some(email),
                custom_fields = custom_fields,
                list = list,
                job_title = job_title,
                company = company,
                linkedin_url = linkedin_url,
                phone = phone,
                phone_2 = None,
                phone_3 = None,
                city = city,
                state = state,
                country = country,
                timezone = timezone,
                prospect_category = prospect_category,
                last_contacted_at = last_contacted_at,
                last_contacted_at_phone = None,
                created_at = created_at,
                internal = prospectObjectInternal,
                latest_reply_sentiment_uuid = None,
                current_step_type = None,
                latest_task_done_at = None,
                prospect_uuid = None,
                owner_uuid = owner_uuid,
                updated_at = updated_at
              )


            }

            def batchSearchForEmails(
                                      moduleType: IntegrationModuleType,
                                      accessTokenData: IntegrationTPAccessTokenResponse.FullTokenData,
                                      contacts: Seq[ProspectObject],
                                      isSuccessResponse: WSResponse => Boolean,
                                      retryOnServerError: (=> Future[WSResponse], FiniteDuration,SRLogger) => Future[WSResponse],
                                      retryOnAPIErrorDelay: FiniteDuration
                                    )(implicit ws: WSClient,
                                      ec: ExecutionContext,
                                      Logger: SRLogger
                                    ): Future[Either[SearchBatchContactsError, Seq[BatchContactResponse]]] = {

              moduleType match {

                case IntegrationModuleType.CONTACTS =>

                  val contactsObj = contacts
                    .map(obj => s"email=${obj.email.get} ")
                    .reduceLeft(_ + _)
                    .trim
                    .replace(" ", "&")

                  val contactUrl = s"https://api.hubapi.com/contacts/v1/contact/emails/batch?$contactsObj"
                  val accessToken = accessTokenData.access_token

                  Logger.info(s"batchSearchForEmails ::contactUrl : $contactUrl")
                  // MUST BE "def", NOT "val"
                  def wsReq = {

                    ws.url(contactUrl)
                      .addHttpHeaders(
                        "Authorization" -> s"Bearer $accessToken",
                        "Content-Type" -> "application/json"
                      )
                      .get()
                  }

                  retryOnServerError(
                    wsReq,
                    retryOnAPIErrorDelay,
                    Logger
                  )
                    .map(response => {

                      if (!isSuccessResponse(response)) {

                        Logger.fatal(s"HubSpotOAuth batchSearchForEmails error: ${response.body}")

                        HubspotAPIResponseStatusCodes.parseStatusCodes(response = response) match {

                          case e: HubspotAPIResponseStatusCodes.CommonCRMAPIError =>
                            Left(SearchBatchContactsError.CommonCRMAPIError(err = e.error))

                          case _: HubspotAPIResponseStatusCodes.BadRequestError =>

                            Logger.info(s"HubSpotOAuth batchSearchForEmails error :: ${response.body}")

                            val message = (response.json \ "message").as[String]
                            val failureMessages = (response.json \ "failureMessages").as[Seq[JsObject]]

                            val failureRecords = failureMessages
                              .map { res => {
                                val index = (res \ "index").as[Int]
                                val propertyValidationResult = (res \ "propertyValidationResult").as[JsObject]

                                val error = HubspotApi.__parseBatchContactSuccessResponse(errorObj = propertyValidationResult)

                                //Contacts which arrive in this path comes from ProspectDAOService.find() which is having inner join prospects_emails
                                //in the old path email cannot be none but in the new path(email optional) the email field may be none for some prospects
                                //TODO: For new path we may need to pass contacts fetched by linkedin_url/phone/company-firstname-lastname) so that we can isolate the cases
                                //NOTE: We may need to add org_id to check old/new paths
                                BatchContactResponse(
                                  email = contacts(index).email, //TODO: EMAIL_OPTIONAL check the path and test code (contacts coming fromDb so old path will always have email)
                                  phone= None,
                                  linkedinUrl = None,
                                  error = Some(error)
                                )

                              }
                              }

                            Left(SearchBatchContactsError.HubspotFailureBatchError(message = message, failureRecords = failureRecords))

                          case e: HubspotAPIResponseStatusCodes.UnknownError =>
                            Left(SearchBatchContactsError.CommonCRMAPIError(err = CommonCRMAPIErrors.UnknownErrorWithResponseBody(
                              msg = e.message,
                              responseBody = e.responseBody,
                              responseCode = e.responseCode
                            )))

                          case e =>
                            Left(SearchBatchContactsError.CommonCRMAPIError(err = CommonCRMAPIErrors.UnknownError(msg = e.message)))
                        }

                      } else {

                        //                  Logger.info(s"HubSpotOAuth batchSearchForEmails response :: ${response.json}")

                        println(s"Response from api is ${response.json}")
                        val validatedResponse = Try {
                          response.json.as[JsObject].values.map { item =>
                            //                      Logger.info(s"HubSpotOAuth batchSearchForEmails item :: ${item}")

                            val emailReads: Reads[String] = (JsPath \ "properties" \\ "email" \\ "value").read[String]
                            item.validate[String](emailReads).get
                          } //using UpdateOrgMetadata and not creating a new case class as we only need email
                        }

                        validatedResponse match {
                          case Success(value) =>
                            val resObj = value.map(c => {
                              BatchContactResponse(
                                email = Some(c), //TODO: EMAIL_OPTIONAL check the path and test code (contacts coming fromDb so old path will always have email)
                                phone= None,
                                linkedinUrl = None,
                                error = None
                              )
                            }).toSeq

                            //                      Logger.info(s"HubSpotOAuth batchSearchForEmails resObj :: ${resObj} :: validatedResponse : $validatedResponse")


                            println(s"response from resObj is $resObj")
                            Right(resObj)

                          case Failure(exception) => Logger.fatal(s"HubSpotOAuth batchSearchForEmails Error while parsing data from hubspot: $exception")

                            Left(SearchBatchContactsError.CommonCRMAPIError(err = CommonCRMAPIErrors.UnknownError(msg = exception.getMessage)))

                        }
                      }


                    })(ec)


                case IntegrationModuleType.LEADS |
                     IntegrationModuleType.CANDIDATES =>

                  Logger.fatal(s" batchSearchForEmails error: module: $moduleType not supported")

                  Future.successful(
                    Left(SearchBatchContactsError.InvalidModuleError(message = s"Invalid module $moduleType"))
                  )
              }
            }

            protected final def retryOnServerError(

                                                    f: => Future[WSResponse],
                                                    delay: FiniteDuration = 3.second,
                                                    Logger:SRLogger

                                                  ): Future[WSResponse] = {

              def f1: Future[WSResponse] = {
                Logger.info("retryOnServerError f1: request called")

                f.map(response => {

                  if (isServerErrorResponse(response)) {
                    Logger.error(s"retryOnServerError f1: ERROR response called: ${response.status} res body: ${response.body}")

                    throw new Exception(s" server error")
                  } else {
                    Logger.info(s"retryOnServerError f1: request success: status: ${response.status}")

                    response
                  }
                })
              }

              FutureUtils.retry(
                f = f1,
                delay = delay,
                retries = 2
              )(ec = playDefaultExecutionContext, s = actorSystem.scheduler, Logger = Logger)

            }

            protected final def isServerErrorResponse(response: WSResponse): Boolean = response.status >= 500 && response.status < 600

            protected final def isSuccessResponse(response: WSResponse): Boolean = response.status >= 200 && response.status < 300

            def init(): Unit = {

               for {

                updateUserMappingForm <- Future.fromTry(trigger.findUserMapping(
                  teamId = 585,
                  integration_type = IntegrationType.HUBSPOT
                ))

                updateFieldMappingForm <- Future.fromTry(trigger.findFieldMapping(
                  teamId = 585,
                  integration_type = IntegrationType.HUBSPOT,
                  module_type = IntegrationModuleType.CONTACTS
                ))

                accessTokenData: Option[IntegrationTPAccessTokenResponse.FullTokenData] <- tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken(
                  teamId = 585,
                  integration_type = IntegrationType.HUBSPOT
                )(
                  Logger = Logger,
                  ec = playDefaultExecutionContext,
                  ws = wSClient
                ).map {
                  case Left(eitherData) =>
                    println(s"updateUserMappingForm $updateUserMappingForm")
                    println(s"updateFieldMappingForm $updateFieldMappingForm")
                    println(s"eitherData is  defined $eitherData")
                    None
                  case Right(accessToken) =>
                    Some(accessToken)
                }(playDefaultExecutionContext)


                searchResults <-batchSearchForEmails(
                moduleType =IntegrationModuleType.CONTACTS,
                accessTokenData = accessTokenData.get ,
                contacts = Seq(ProspectFixtures.prospectObject.copy(email = Some("<EMAIL>"),first_name = Some("arnav"),last_name = Some("gupta")),ProspectFixtures.prospectObject.copy(email = Some("<EMAIL>"),first_name = Some("Pravan"),last_name = Some("Sharma"))),
                isSuccessResponse = isSuccessResponse,
                retryOnServerError =retryOnServerError,
                retryOnAPIErrorDelay = 10.seconds,
              )(wSClient,playDefaultExecutionContext,Logger)

            } yield {
                searchResults
              }
            }
          }

          main.init()

        case "hubspot_search_api" =>


          implicit lazy val system: ActorSystem = ActorSystem()
          implicit lazy val wSClient: AhcWSClient = AhcWSClient()
          implicit lazy val actorContext: ExecutionContext = system.dispatcher
          given Logger: SRLogger = new SRLogger("SrLogger")
          object main {
            def init(): Future[Unit] = {
              def batchSearchForProspect(
                                          moduleType: IntegrationModuleType,
                                          emails: Seq[String]
                                        )(implicit ws: WSClient,
                                          ec: ExecutionContext,
                                          system: ActorSystem,
                                          Logger: SRLogger
                                        ): Future[Unit] = {
                moduleType match {

                  case IntegrationModuleType.CONTACTS =>




                    val contactUrl = s"https://api.hubapi.com/crm/v3/objects/contacts/search"
                    val accessToken = "********************************************"

                    Logger.info(s"batchSearchForEmails ::contactUrl : $contactUrl")

                    ws.url(contactUrl)
                      .addHttpHeaders(
                        "Authorization" -> s"Bearer $accessToken",
                        "Content-Type" -> "application/json"
                      )
                      .post(Json.obj(
                        "filterGroups" -> Json.arr(
                          Json.obj(
                            "filters" -> Json.arr(
                              Json.obj(
                                "propertyName" -> "email",
                                "operator" -> "IN",
                                "values" -> Seq("<EMAIL>")
                              )
                            )
                          ),
                          Json.obj(
                            "filters" -> Json.arr(
                              Json.obj(
                                "propertyName" -> "phone",
                                "operator" -> "IN",
                                "values" -> Seq("+919822016925")
                              )
                            )
                          ),

                        ),
                        "properties" -> Json.arr("firstname", "lastname", "email", "phone", "linkedin_url", "hs_lead_status", "hubspot_owner_id")
                      ))
                      .map(response => {
                        println(response.json)
                      })(actorContext)


                  case IntegrationModuleType.LEADS |
                       IntegrationModuleType.CANDIDATES =>


                    Future.successful(
                      println("Wrong type")
                    )
                }
              }


              batchSearchForProspect(
                moduleType = IntegrationModuleType.CONTACTS,
                emails = Seq("<EMAIL>", "<EMAIL>")
              )(ws = wSClient,
                ec = actorContext,
                system = system,
                Logger = Logger)

            }
          }

          main.init()

        case "hubspot_contact_create_api" =>

          /*
            How Hubspot Handles Deduplication ::
            https://knowledge.hubspot.com/records/deduplication-of-records#automatic-deduplication-in-hubspot

           */


          implicit lazy val system: ActorSystem = ActorSystem()
          implicit lazy val wSClient: AhcWSClient = AhcWSClient()
          implicit lazy val actorContext: ExecutionContext = system.dispatcher

          object main {

            def init(): Future[Unit] = {

              /* https://developers.hubspot.com/docs/api/crm/contacts */
              val contactUrl = "https://api.hubapi.com/crm/v3/objects/contacts/batch/create"

              val anotherContactsObj = Json.obj(
                "inputs" -> Json.arr(
                  Json.obj("properties" ->
                    Json.obj(
                      "email" -> null,
                      "firstname" -> "Michael",
                      "lastname" -> "Chandler",
                      "hubspot_owner_id" -> "815365196"
                    )
                  )
                )
              )
              //              val anotherContactsObj = Json.obj(
              //                "inputs" -> Json.arr(
              //                  Json.arr(Json.obj (
              //                    "properties":Json.arr(
              //                    Json.obj ("email":null},{"firstname":"Rahul"},{"lastname":"Dravid"},{"hubspot_owner_id":"815365196"})
              //                  )
              //                  ))
              //              )
              //              )


              val accessToken = "********************************************"

              wSClient.url(contactUrl)
                .addHttpHeaders(
                  "Authorization" -> s"Bearer $accessToken",
                  "Content-Type" -> "application/json"
                )
                .post(anotherContactsObj)
                .map(rs => {
                  println(rs.json)
                })
            }


          }
          main.init()


        case "hubspot_test" =>

          implicit lazy val system: ActorSystem = ActorSystem()
          implicit lazy val wSClient: AhcWSClient = AhcWSClient()
          implicit lazy val actorContext: ExecutionContext = system.dispatcher
          given Logger: SRLogger = new SRLogger("SrLogger")


          object main {

            def findAllByProspectAndModule(
                                            module: IntegrationModuleType,
                                          )(implicit ws: WSClient,
                                            Logger: SRLogger
                                          ): Future[Either[FindOneByEmailAndModuleError, Seq[IntegrationContactResponse]]] = {

              Logger.info("HubSpotOAuth findAllByProspectAndModule called")
              //TODO: In Hubspot we're supporting CONTACTS only when we support LEADS this condition will be removed
              module match {

                case IntegrationModuleType.CONTACTS =>


                  val contactUrl = s"https://api.hubapi.com/crm/v3/objects/contacts/search"
                  val accessToken = "********************************************"


                  val prospectEmail = if (true) {
                    Seq("<EMAIL>")
                  } else {
                    Seq()
                  }
                  val prospectPhone = if (true) {
                    Seq("+919822016925")
                  } else {
                    Seq()
                  }


                  ws.url(contactUrl)
                    .addHttpHeaders(
                      "Authorization" -> s"Bearer $accessToken",
                      "Content-Type" -> "application/json"
                    )
                    .post(Json.obj(
                      "filterGroups" -> Json.arr(
                        Json.obj(
                          "filters" -> Json.arr(
                            Json.obj(
                              "propertyName" -> "email",
                              "operator" -> "IN",
                              "values" -> prospectEmail
                            )
                          )
                        ),
                        Json.obj(
                          "filters" -> Json.arr(
                            Json.obj(
                              "propertyName" -> "phone",
                              "operator" -> "IN",
                              "values" -> prospectPhone
                            )
                          )
                        )

                      ),
                      "properties" -> Json.arr("firstname", "lastname", "email", "phone", "linkedin_url", "hs_lead_status", "hubspot_owner_id")
                    ))
                    .map(response => {

                      println(s"response.json ${response.json}")

                      if (response.status == 200) {


                        val contacts = (response.json \ "results").as[Seq[JsValue]]
                        println(s"contacts $contacts")


                        val result = contacts.map(contact => {
                          val email = (contact \ "properties" \ "email").asOpt[String]
                          println(s"email $email")
                          val status = (contact \ "properties" \ "hs_lead_status").asOpt[String]
                          println(s"status $status")

                          val phone = (contact \ "properties" \ "phone").asOpt[String]
                          println(s"phone $phone")


                          val owner_id = (contact \ "properties" \ "hubspot_owner_id").asOpt[String]

                          println(s"owner_id $owner_id")

                          // commenting out large frequently running log
                          Logger.info(s"HubSpotOAuth findAllByProspectAndModule response.status :: ${response.status} ::email : $email status :: $status")

                          val id = (contact \ "id").as[String]
                          println(s"id $id")


                          IntegrationContactResponse(
                            id = id,
                            email = email,
                            status = status,
                            owner_id = owner_id,
                            phone = phone
                          )

                        })

                        Logger.info(s"HubspotOAuth findAllByProspectAndModule ::: result $result")
                        Right(result)


                      } else if (response.status == 404) {

                        Logger.info(s"HubSpotOAuth findAllByProspectAndModule not found: ${response.body} :: status: ${response.status} ::  url :: $contactUrl")

                        Right(Seq())

                      } else {

                        Logger.error(s"HubSpotOAuth findAllByProspectAndModule error: ${response.body} :: status: ${response.status} ::  url :: $contactUrl")

                        HubspotAPIResponseStatusCodes.parseStatusCodes(response = response) match {

                          case e: HubspotAPIResponseStatusCodes.CommonCRMAPIError =>
                            Left(FindOneByEmailAndModuleError.CommonCRMAPIError(err = e.error))

                          case e =>
                            Left(FindOneByEmailAndModuleError.CommonCRMAPIError(err = CommonCRMAPIErrors.UnknownErrorWithResponseBody(
                              msg = e.message,
                              responseBody = response.body,
                              responseCode = response.status
                            )))

                        }

                      }
                    })(actorContext)


                case IntegrationModuleType.LEADS |
                     IntegrationModuleType.CANDIDATES =>

                  Logger.fatal(s"Heloo findAllByProspectAndModule error: module: $module not supported")

                  Future.successful(
                    Left(FindOneByEmailAndModuleError.InvalidModuleError(msg = s"Invalid Module: $module"))
                  )

              }

            }

          }
          main.findAllByProspectAndModule(module = IntegrationModuleType.CONTACTS).map(res => {
            println(s"result is :: $res")
          }).recover { e => {
            println(s"Error occurred ${e.printStackTrace()}")
          }
          }


        case "hubspot_duplication_check" =>

          implicit lazy val system: ActorSystem = ActorSystem()
          given Logger: SRLogger = new SRLogger("SrLogger")

          object main extends  TestAppTrait {


            object ProspectFixtures {

              private val dateTime_now = DateTime.now()
              private val ta_id_28 = 28
              private val owner_name = "owner_name"
              private val owner_email = "owner_email"
              private val email_domain = "email_domain"
              private val prospect_category_id_custom_3 = 3
              private val prospect_category_label_color = "color"
              private val account_id_34 = Some(34L)
              private val total_open_3 = 3
              private val total_click_4 = 4
              private val flag_32 = Json.obj()
              private val prospect_id_1 = 1
              private val owner_id_35 = 35L
              private val team_id_8 = 8L
              private val first_name = Option("first_name")
              private val last_name = Option("last_name")
              private val email = "email"
              private val custom_fields = Json.obj()
              private val list = Option("list")
              private val job_title = Option("job_title")
              private val company = Option("company")
              private val linkedin_url = Option("linkedin_url")
              private val phone = Option("+************")
              private val city = Option("city")
              private val state = Option("state")
              private val country = Option("country")
              private val timezone = Option("time_zone")
              private val prospect_category = "prospect_category"
              private val last_contacted_at = Option(dateTime_now)
              private val created_at = dateTime_now
              private val owner_uuid = AccountUuid("owner_uuid")
              private val updated_at = dateTime_now


              val prospectObjectInternal: ProspectObjectInternal = ProspectObjectInternal(
                owner_name = owner_name,
                owner_email = owner_email,
                magic_columns = List(),
                email_domain = Some(email_domain),
                invalid_email = None,
                last_contacted_at = None,
                last_replied_at = None,
                last_opened_at = None,
                  last_call_made_at = None,
                list_id = None,
                prospect_category_id_custom = prospect_category_id_custom_3,
                prospect_category_label_color = prospect_category_label_color,
                prospect_source = None,
                prospect_account_id = account_id_34,
                prospect_account = None,
                total_opens = total_open_3,
                total_clicks = total_click_4,
                active_campaigns = None,
                current_campaign_id = None,
                tags = Some(
                  List(
                    ProspectTag(tag_id = 123L, tag = "tag1", tag_uuid = ProspectTagUuid("tags_abcdefg")), ProspectTag(tag_id = 1234L, tag = "tag2", tag_uuid = ProspectTagUuid("tags_qwerty"))
                  )
                ),
                flags = flag_32,
                latest_reply_sentiment = None,
                prospect_account_uuid = None
              )
              val  prospectObject: ProspectObject = ProspectObject(
                id = prospect_id_1,
                owner_id = owner_id_35,
                team_id = team_id_8,
                first_name = first_name,
                last_name = last_name,
                email = Some(email),
                custom_fields = custom_fields,
                list = list,
                job_title = job_title,
                company = company,
                linkedin_url = linkedin_url,
                phone = phone,
                phone_2 = None,
                phone_3 = None,
                city = city,
                state = state,
                country = country,
                timezone = timezone,
                prospect_category = prospect_category,
                last_contacted_at = last_contacted_at,
                last_contacted_at_phone = None,
                created_at = created_at,
                internal = prospectObjectInternal,
                latest_reply_sentiment_uuid = None,
                current_step_type = None,
                latest_task_done_at = None,
                prospect_uuid = None,
                owner_uuid = owner_uuid,
                updated_at = updated_at
              )


            }



            def init(): Future[Unit] = {
              println("This got Called")
              val result = for {

                updateUserMappingForm <- Future.fromTry(trigger.findUserMapping(
                  teamId = 599L,
                  integration_type = IntegrationType.HUBSPOT
                ))

                updateFieldMappingForm <- Future.fromTry(trigger.findFieldMapping(
                  teamId = 599L,
                  integration_type = IntegrationType.HUBSPOT,
                  module_type = IntegrationModuleType.CONTACTS
                ))

                accessTokenData: Option[IntegrationTPAccessTokenResponse.FullTokenData] <- tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken(
                  teamId = 599L,
                  integration_type = IntegrationType.HUBSPOT
                )(
                  Logger = Logger,
                  ec = playDefaultExecutionContext,
                  ws = wSClient
                ).map {
                  case Left(eitherData) =>
                    println(s"updateUserMappingForm $updateUserMappingForm")
                    println(s"updateFieldMappingForm $updateFieldMappingForm")
                    println(s"eitherData is  defined $eitherData")
                    None
                  case Right(accessToken) =>
                    Some(accessToken)
                }


                createOrUpdate <- tIntegrationCRMService.createOrUpdateBatchContacts(
                  integrationType = IntegrationType.HUBSPOT,
                  moduleType = IntegrationModuleType.CONTACTS,
                  accessTokenData = accessTokenData.get,
                  contacts = Seq(
                     ProspectObjectWithOldProspectDeduplicationColumn(
                     prospectObject =  ProspectFixtures.prospectObject.copy(
                      phone = Some("+************"),
                      email =None,
                      first_name = Some("dev"),
                      last_name = Some("gupta")),
                      oldProspectDeduplicationColumn = Some(
                        OldProspectDeduplicationColumn(
                          email = None,
                          phone = Some("+************")
                        )
                      )
                     )

                  ),
                  fieldMapping = updateFieldMappingForm.get,
                  userMapping = updateUserMappingForm.get,
                  accountId = 392L,
                  teamId = 599L
                )(
                  ws = wSClient,
                  ec = playDefaultExecutionContext,
                  system = system,
                  Logger = Logger
                )

              } yield {
                createOrUpdate
              }

              result.map {
                case Left(value) =>
                  println("Its this flow")
                  value match {
                    case CreateOrUpdateBatchContactsError.CommonCRMAPIError(err) =>
                      println(s"Error occurred CommonCRMAPIError $err")
                    case CreateOrUpdateBatchContactsError.InvalidModuleError(message) =>
                      println(s"Invalid Module Error $message")
                    case CreateOrUpdateBatchContactsError.HubspotFailureBatchError(message, failureRecords) =>
                      println(s"HubspotFailureBatchError message: $message failureRecords:$failureRecords")
                    case CreateOrUpdateBatchContactsError.SalesForceFailureBatchError(message, failureRecords) =>
                      println(s"HubspotFailureBatchError message: $message failureRecords:$failureRecords")
                    case CreateOrUpdateBatchContactsError.CRMFindOneByEmailAndModuleError(err) =>
                      println(s"CRMFindOneByEmailAndModuleError : ${err.message}")

                    case hsf: CreateOrUpdateBatchContactsError.HubspotSearchFailureError =>
                      println(s"HubspotSearchFailureError message: ${hsf.message} failureRecords:${hsf.failureMessages}")
                  }
                case Right(value) =>
                  print("Its Right")
                  println(s"Updated Item :: ${value.toString()}")
              }.recover{case e =>
                println(s"Error Occurred ${e.printStackTrace()}")

              }
            }


          }
          main.init()


        case "hubspot_contact_creation_check" =>

          implicit lazy val system: ActorSystem = ActorSystem()
          given Logger: SRLogger = new SRLogger("SrLogger")

          object main extends  TestAppTrait {


            object ProspectFixtures {

              private val dateTime_now = DateTime.now()
              private val ta_id_28 = 28
              private val owner_name = "owner_name"
              private val owner_email = "owner_email"
              private val email_domain = "email_domain"
              private val prospect_category_id_custom_3 = 3
              private val prospect_category_label_color = "color"
              private val account_id_34 = Some(34L)
              private val total_open_3 = 3
              private val total_click_4 = 4
              private val flag_32 = Json.obj()
              private val prospect_id_1 = 1
              private val owner_id_35 = 35L
              private val team_id_8 = 8L
              private val first_name = Option("first_name")
              private val last_name = Option("last_name")
              private val email = "email"
              private val custom_fields = Json.obj()
              private val list = Option("list")
              private val job_title = Option("job_title")
              private val company = Option("company")
              private val linkedin_url = Option("linkedin_url")
              private val phone = Option("+************")
              private val city = Option("city")
              private val state = Option("state")
              private val country = Option("country")
              private val timezone = Option("time_zone")
              private val prospect_category = "prospect_category"
              private val last_contacted_at = Option(dateTime_now)
              private val created_at = dateTime_now
              private val owner_uuid = AccountUuid("owner_uuid")
              private val updated_at = dateTime_now


              val prospectObjectInternal: ProspectObjectInternal = ProspectObjectInternal(
                owner_name = owner_name,
                magic_columns = List(),
                owner_email = owner_email,
                email_domain = Some(email_domain),
                invalid_email = None,
                last_contacted_at = None,
                last_replied_at = None,
                last_opened_at = None,
                  last_call_made_at = None,
                list_id = None,
                prospect_category_id_custom = prospect_category_id_custom_3,
                prospect_category_label_color = prospect_category_label_color,
                prospect_source = None,
                prospect_account_id = account_id_34,
                prospect_account = None,
                total_opens = total_open_3,
                total_clicks = total_click_4,
                active_campaigns = None,
                current_campaign_id = None,
                tags = Some(
                  List(
                    ProspectTag(tag_id = 123L, tag = "tag1", tag_uuid = ProspectTagUuid("tags_abcdefg")), ProspectTag(tag_id = 1234L, tag = "tag2", tag_uuid = ProspectTagUuid("tags_qwerty"))
                  )
                ),
                flags = flag_32,
                latest_reply_sentiment = None,
                prospect_account_uuid = None
              )
              val  prospectObject: ProspectObject = ProspectObject(
                id = prospect_id_1,
                owner_id = owner_id_35,
                team_id = team_id_8,
                first_name = first_name,
                last_name = last_name,
                email = Some(email),
                custom_fields = custom_fields,
                list = list,
                job_title = job_title,
                company = company,
                linkedin_url = linkedin_url,
                phone = phone,
                phone_2 = None,
                phone_3 = None,
                city = city,
                state = state,
                country = country,
                timezone = timezone,
                prospect_category = prospect_category,
                last_contacted_at = last_contacted_at,
                last_contacted_at_phone = None,
                created_at = created_at,
                internal = prospectObjectInternal,
                latest_reply_sentiment_uuid = None,
                current_step_type = None,
                latest_task_done_at = None,
                prospect_uuid = None,
                owner_uuid = owner_uuid,
                updated_at = updated_at
              )


            }



            def init(): Future[Unit] = {
              val result = for {

                updateUserMappingForm <- Future.fromTry(trigger.findUserMapping(
                  teamId = 585L,
                  integration_type = IntegrationType.HUBSPOT
                ))

                updateFieldMappingForm <- Future.fromTry(trigger.findFieldMapping(
                  teamId = 585L,
                  integration_type = IntegrationType.HUBSPOT,
                  module_type = IntegrationModuleType.CONTACTS
                ))

                accessTokenData: Option[IntegrationTPAccessTokenResponse.FullTokenData] <- tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken(
                  teamId = 585L,
                  integration_type = IntegrationType.HUBSPOT
                )(
                  Logger = Logger,
                  ec = playDefaultExecutionContext,
                  ws = wSClient
                ).map {
                  case Left(eitherData) =>
                    println(s"updateUserMappingForm $updateUserMappingForm")
                    println(s"updateFieldMappingForm $updateFieldMappingForm")
                    println(s"eitherData is  defined $eitherData")
                    None
                  case Right(accessToken) =>
                    Some(accessToken)
                }


                createOrUpdate <- tIntegrationCRMService.createOrUpdateBatchContacts(
                  integrationType = IntegrationType.HUBSPOT,
                  moduleType = IntegrationModuleType.CONTACTS,
                  accessTokenData = accessTokenData.get,
                  contacts = Seq(
                    ProspectObjectWithOldProspectDeduplicationColumn(
                      prospectObject = ProspectFixtures.prospectObject.copy(
                        email = Some("<EMAIL>"),
                        first_name = Some("arnav"),
                        last_name =Some("gupta")
                      ),
                      oldProspectDeduplicationColumn = Some(OldProspectDeduplicationColumn(
                        email = Some("<EMAIL>"),
                        phone = None
                      ))
                    )

                  ),
                  fieldMapping = updateFieldMappingForm.get,
                  userMapping = updateUserMappingForm.get,
                  accountId = 392L,
                  teamId = 599L
                )(
                  ws = wSClient,
                  ec = playDefaultExecutionContext,
                  system = system,
                  Logger = Logger
                )

              } yield {
                createOrUpdate
              }

              result.map {
                case Left(value) =>
                  println("Its this flow")
                  value match {
                    case CreateOrUpdateBatchContactsError.CommonCRMAPIError(err) =>
                      println(s"Error occurred CommonCRMAPIError $err")
                    case CreateOrUpdateBatchContactsError.InvalidModuleError(message) =>
                      println(s"Invalid Module Error $message")
                    case CreateOrUpdateBatchContactsError.HubspotFailureBatchError(message, failureRecords) =>
                      println(s"HubspotFailureBatchError message: $message failureRecords:$failureRecords")
                    case CreateOrUpdateBatchContactsError.SalesForceFailureBatchError(message, failureRecords) =>
                      println(s"HubspotFailureBatchError message: $message failureRecords:$failureRecords")
                    case CreateOrUpdateBatchContactsError.CRMFindOneByEmailAndModuleError(err) =>
                      println(s"CRMFindOneByEmailAndModuleError : ${err.message}")
                    case hsf: CreateOrUpdateBatchContactsError.HubspotSearchFailureError =>
                      println(s"HubspotSearchFailureError message: ${hsf.message} failureRecords:${hsf.failureMessages}")
                  }
                case Right(value) =>
                  print("Its Right")
                  println(s"Updated Item :: ${value.toString()}")
              }.recover{case e =>
                println(s"Error Occurred ${e.printStackTrace()}")

              }
            }


          }
          main.init()


        case "hubspot_search_check" =>

          given Logger: SRLogger = new SRLogger("SrLogger")

          object main extends  TestAppTrait {


            object ProspectFixtures {

              private val dateTime_now = DateTime.now()
              private val ta_id_28 = 28
              private val owner_name = "owner_name"
              private val owner_email = "owner_email"
              private val email_domain = "email_domain"
              private val prospect_category_id_custom_3 = 3
              private val prospect_category_label_color = "color"
              private val account_id_34 = Some(34L)
              private val total_open_3 = 3
              private val total_click_4 = 4
              private val flag_32 = Json.obj()
              private val prospect_id_1 = 1
              private val owner_id_35 = 35L
              private val team_id_8 = 8L
              private val first_name = Option("first_name")
              private val last_name = Option("last_name")
              private val email = "email"
              private val custom_fields = Json.obj()
              private val list = Option("list")
              private val job_title = Option("job_title")
              private val company = Option("company")
              private val linkedin_url = Option("linkedin_url")
              private val phone = Option("+************")
              private val city = Option("city")
              private val state = Option("state")
              private val country = Option("country")
              private val timezone = Option("time_zone")
              private val prospect_category = "prospect_category"
              private val last_contacted_at = Option(dateTime_now)
              private val created_at = dateTime_now
              private val owner_uuid = AccountUuid("owner_uuid")
              private val updated_at = dateTime_now


              val prospectObjectInternal: ProspectObjectInternal = ProspectObjectInternal(
                owner_name = owner_name,
                owner_email = owner_email,
                magic_columns = List(),
                email_domain = Some(email_domain),
                invalid_email = None,
                last_contacted_at = None,
                last_replied_at = None,
                last_opened_at = None,
                  last_call_made_at = None,
                list_id = None,
                prospect_category_id_custom = prospect_category_id_custom_3,
                prospect_category_label_color = prospect_category_label_color,
                prospect_source = None,
                prospect_account_id = account_id_34,
                prospect_account = None,
                total_opens = total_open_3,
                total_clicks = total_click_4,
                active_campaigns = None,
                current_campaign_id = None,
                tags = Some(
                  List(
                    ProspectTag(tag_id = 123L, tag = "tag1", tag_uuid = ProspectTagUuid("tags_abcdefg")), ProspectTag(tag_id = 1234L, tag = "tag2", tag_uuid = ProspectTagUuid("tags_qwerty"))
                  )
                ),
                flags = flag_32,
                latest_reply_sentiment = None,
                prospect_account_uuid = None
              )
              val  prospectObject: ProspectObject = ProspectObject(
                id = prospect_id_1,
                owner_id = owner_id_35,
                team_id = team_id_8,
                first_name = first_name,
                last_name = last_name,
                email = Some(email),
                custom_fields = custom_fields,
                list = list,
                job_title = job_title,
                company = company,
                linkedin_url = linkedin_url,
                phone = phone,
                phone_2 = None,
                phone_3 = None,
                city = city,
                state = state,
                country = country,
                timezone = timezone,
                prospect_category = prospect_category,
                last_contacted_at = last_contacted_at,
                last_contacted_at_phone = None,
                created_at = created_at,
                internal = prospectObjectInternal,
                latest_reply_sentiment_uuid = None,
                current_step_type = None,
                latest_task_done_at = None,
                prospect_uuid = None,
                owner_uuid = owner_uuid,
                updated_at = updated_at
              )


            }
            def isServerErrorResponse(response: WSResponse): Boolean = response.status >= 500 && response.status < 600
            def isSuccessResponse(response: WSResponse): Boolean = response.status >= 200 && response.status < 300





            def init(): Future[Unit] = {

              def retryOnServerError(

                                      f: => Future[WSResponse],
                                      delay: FiniteDuration = 3.second

                                    )(implicit ec: ExecutionContext, system: ActorSystem,
                                      Logger: SRLogger
                                    ): Future[WSResponse] = {

                def f1: Future[WSResponse] = {
                  Logger.info("retryOnServerError f1: request called")

                  f.map(response => {

                    if (isServerErrorResponse(response)) {
                      Logger.error(s"retryOnServerError f1: ERROR response called: ${response.status} res body: ${response.body}")

                      throw new Exception(s" server error")
                    } else {
                      Logger.info(s"retryOnServerError f1: request success: status: ${response.status}")

                      response
                    }
                  })(ec)
                }

                FutureUtils.retry(
                  f = f1,
                  delay = delay,
                  retries = 2
                )(ec = ec, s = system.scheduler, Logger = Logger)

              }
              val result = for {



                accessTokenData: Option[IntegrationTPAccessTokenResponse.FullTokenData] <- tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken(
                  teamId = 585L,
                  integration_type = IntegrationType.HUBSPOT
                )(
                  Logger = Logger,
                  ec = playDefaultExecutionContext,
                  ws = wSClient
                ).map {
                  case Left(eitherData) =>
                    println(s"eitherData is  defined $eitherData")
                    None
                  case Right(accessToken) =>
                    Some(accessToken)
                }


                createOrUpdate <- hubspotApi.batchSearchForEmails(
                  moduleType=  triggers.IntegrationModuleType.CONTACTS,
                  accessTokenData = accessTokenData.get,
                  contacts = Seq(ProspectFixtures.prospectObject.copy(email = Some("<EMAIL>"))),
                  isSuccessResponse = isSuccessResponse,
                  retryOnServerError = retryOnServerError,
                  retryOnAPIErrorDelay = 10.seconds
                )(
                  ws = wSClient,
                  ec = playDefaultExecutionContext,
                  system = actorSystem,
                  Logger = Logger
                )

              } yield {
                createOrUpdate
              }

              result.map {resp =>

                  println(s"Updated Item :: ${resp.toString}")
              }.recover{ case e =>
                println(s"Error Occurred ${e.printStackTrace()}")
              }
            }


          }
          main.init()

        case "get_access_token" =>
          object main extends TestAppTrait {

            def getAccessToken: Future[Option[IntegrationTPAccessTokenResponse.FullTokenData]] = {
              val Logger = new SRLogger("[GET_ACCESS_TOKEN")
              
              print("Enter the teamId: ")
              val teamId = scala.io.StdIn.readLine().toLong

              print("\nEnter integration type " + "\n" +
                "0:" + "hubspot" + "\n" +
                "1:" + "salesforce" + "\n"+
                "2:" + "pipedrive"  + "\n"+
                "3:" + "zoho"+"\n"
              )
              val choice = scala.io.StdIn.readLine().toInt
              
              val integrationType = if(choice == 0){
                IntegrationType.HUBSPOT
              }else if(choice == 1){
                IntegrationType.SALESFORCE
              }else if(choice == 2){
                IntegrationType.PIPEDRIVE
              }else {
                IntegrationType.ZOHO
              }

              tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken(
                teamId = teamId , // <NAME_EMAIL>
                integration_type = integrationType
              )(
                Logger = Logger,
                ec = playDefaultExecutionContext,
                ws = wSClient
              ).map {
                case Left(eitherData) =>
                  println(s"eitherData is  defined $eitherData")
                  None
                case Right(accessToken) =>
                  println(s"accessToken ${accessToken.access_token}  expires_at: ${accessToken.expires_at}   expires_in: ${accessToken.expires_in}")
                  Some(accessToken)
              }(playDefaultExecutionContext)

            }
          }
          main.getAccessToken


        case "testapp_1" => {
          implicit lazy val system: ActorSystem = ActorSystem()
          given Logger: SRLogger = new SRLogger("SrLogger")
          object main extends TestAppTrait {


            def init(accessTokenData: IntegrationTPAccessTokenResponse.FullTokenData): Future[Unit] = {
              val access_token = accessTokenData.access_token
              val accountUrl = s"https://api.hubapi.com/oauth/v1/access-tokens/$access_token"

              wSClient.url(accountUrl)

                .addHttpHeaders(
                  "Authorization" -> s"Bearer ${access_token}",
                  "Content-Type" -> "application/json"
                )
                .get()
                .flatMap(response => {

                  if (!(response.status >= 200 && response.status < 300)) {

                    Logger.error(s"HubSpotOAuth /me error: ${response.body} :: status: ${response.status} :: url :: ${accountUrl}")

                    throw new Exception(s"HubSpotOAuth /me error: ${response.body}")

                  } else {

                    val account = response.json

                    val validateData = account.validate[HubspotAccountResponse]

                    validateData match {

                      case JsError(e) =>

                        Logger.error(s"HubSpotOAuth /me validation error: $response ::: ${response.body} ::: $e")

                        throw new Exception(s"HubSpotOAuth /me  validation error: ${response.body}")

                      case JsSuccess(accountResponse, _) =>

                        var account = accountResponse
                        //NOTE doing getOwnerByEmail bcz owner_id is not getting in /me
                        hubspotOAuth.getOwnerByEmail(email = accountResponse.user, accessTokenData = accessTokenData
                        )(Logger = Logger.appendLogRequestId("getOwnerByEmail"), ws = wsClient, ec = playDefaultExecutionContext).map(owner => {

                          IntegrationTPUser(
                            id = account.user_id.toString,
                            email = account.user,
                            company_id = Some(account.hub_id.toString),
                            owner_id = Some(owner.ownerId.toString )
                          )

                        })
                    }
                  }

                }).map(resp => {
                  println(s"resp form test app 1 is ${resp}")
                }).recover(e => {
                  println(s"An error Occurred ${e.printStackTrace()}")
                })
            }
          }
          main.init(accessTokenData = IntegrationTPAccessTokenResponse.FullTokenData(
            access_token = "****",
            refresh_token = None,
            expires_in = None,
            expires_at = None,
            token_type = None,
            api_domain = None,
            is_sandbox = None
          ))
        }

        case "testapp_2" => {
          given Logger: SRLogger = new SRLogger("SrLogger")

          object main extends TestAppTrait {

            def me2(accessTokenData: IntegrationTPAccessTokenResponse.FullTokenData): Future[Unit] = {

              val access_token = accessTokenData.access_token
              val accountUrl = s"https://api.hubapi.com/oauth/v1/access-tokens/$access_token"

              wSClient.url(accountUrl)
                .addHttpHeaders(
                  "Authorization" -> s"Bearer ${access_token}",
                  "Content-Type" -> "application/json"
                )
                .get()
                .flatMap(response => {

                  if (!(response.status >= 200 && response.status < 300)) {

                    Logger.error(s"HubSpotOAuth /me error: ${response.body} :: status: ${response.status} :: url :: ${accountUrl}")

                    throw new Exception(s"HubSpotOAuth /me error: ${response.body}")

                  } else {

                    val account = response.json

                    val validateData = account.validate[HubspotAccountResponse]

                    validateData match {

                      case JsError(e) =>

                        Logger.error(s"HubSpotOAuth /me validation error: $response ::: ${response.body} ::: $e")

                        throw new Exception(s"HubSpotOAuth /me  validation error: ${response.body}")

                      case JsSuccess(accountResponse, _) =>

                        var account = accountResponse
                        //NOTE doing getOwnerByEmail bcz owner_id is not getting in /me
                        hubspotOAuth.getOwnerById(id = accountResponse.user_id.toString, accessTokenData = accessTokenData
                        )(Logger = Logger.appendLogRequestId("getOwnerById"), ws = wsClient, ec = playDefaultExecutionContext).map(owner => {

                          IntegrationTPUser(
                            id = account.user_id.toString,
                            email = account.user,
                            company_id = Some(account.hub_id.toString),
                            owner_id = Some(owner.id)
                          )

                        })
                    }
                  }

                }).map(resp => {
                  println(s"resp form test app 2 is ${resp}")
                }).recover(e => {
                  println(s"An error Occurred ${e.printStackTrace()}")
                })

            }

          }
          main.me2(accessTokenData = IntegrationTPAccessTokenResponse.FullTokenData(
            access_token = "****",
            refresh_token = None,
            expires_in = None,
            expires_at = None,
            token_type = None,
            api_domain = None,
            is_sandbox = None
          ))
        }
      }
      }
    }
}
