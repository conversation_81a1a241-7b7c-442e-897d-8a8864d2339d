package utils.crm_testapp_apis

import org.apache.pekko.actor.ActorSystem
import api.accounts.AccountUuid
import api.integrations.{CreateOrUpdateBatchContactsError, FindBatchError, IntegrationTPAccessTokenResponse}
import api.sr_audit_logs.models.{OldProspectDeduplicationColumn, ProspectObjectWithOldProspectDeduplicationColumn}
import api.tags.models.{ProspectTag, ProspectTagUuid}
import api.triggers.{IntegrationModuleType, IntegrationType, UpdateFieldsMappingForm, UpdateUserMappingForm}
import eventframework.{ProspectObject, ProspectObjectInternal}
import org.joda.time.DateTime
import play.api.Logging
import play.api.libs.json.Json
import scalikejdbc.config.DBs
import utils.testapp.TestAppTrait
import utils.{Helpers, SRLogger}

import scala.concurrent.Future


object ZohoAuthTestApp extends  Logging {

  def main(args: Array[String]): Unit = {

    if (args.length > 0) {
      DBs.setupAll()

      val applicationName = args(0)


      applicationName match {


        case "zoho_contact_creation_check" =>

          implicit lazy val system: ActorSystem = ActorSystem()
          given Logger: SRLogger = new SRLogger("SrLogger")

          object main extends  TestAppTrait {


            object ProspectFixtures {

              private val dateTime_now = DateTime.now()
              private val owner_name = "owner_name"
              private val owner_email = "owner_email"
              private val email_domain = "email_domain"
              private val prospect_category_id_custom_3 = 3
              private val prospect_category_label_color = "color"
              private val account_id_34 = Some(34L)
              private val total_open_3 = 3
              private val total_click_4 = 4
              private val flag_32 = Json.obj()
              private val prospect_id_1 = 1
              private val owner_id_35 = 35L
              private val team_id_8 = 8L
              private val first_name = Option("first_name")
              private val last_name = Option("last_name")
              private val email = "email"
              private val custom_fields = Json.obj()
              private val list = Option("list")
              private val job_title = Option("job_title")
              private val company = Option("company")
              private val linkedin_url = Option("linkedin_url")
              private val phone = Option("+************")
              private val city = Option("city")
              private val state = Option("state")
              private val country = Option("country")
              private val timezone = Option("time_zone")
              private val prospect_category = "prospect_category"
              private val last_contacted_at = Option(dateTime_now)
              private val created_at = dateTime_now
              private val owner_uuid = AccountUuid("owner_uuid")
              private val updated_at = dateTime_now


              val prospectObjectInternal: ProspectObjectInternal = ProspectObjectInternal(
                owner_name = owner_name,
                magic_columns = List(),
                owner_email = owner_email,
                email_domain = Some(email_domain),
                invalid_email = None,
                last_contacted_at = None,
                last_replied_at = None,
                last_opened_at = None,
                  last_call_made_at = None,
                list_id = None,
                prospect_category_id_custom = prospect_category_id_custom_3,
                prospect_category_label_color = prospect_category_label_color,
                prospect_source = None,
                prospect_account_id = account_id_34,
                prospect_account = None,
                total_opens = total_open_3,
                total_clicks = total_click_4,
                active_campaigns = None,
                current_campaign_id = None,
                tags = Some(
                  List(
                    ProspectTag(tag_id = 123L, tag = "tag1", tag_uuid = ProspectTagUuid("tags_abcdefg")), ProspectTag(tag_id = 1234L, tag = "tag2", tag_uuid = ProspectTagUuid("tags_qwerty"))
                  )
                ),
                flags = flag_32,
                latest_reply_sentiment = None,
                prospect_account_uuid = None
              )
              val  prospectObject: ProspectObject = ProspectObject(
                id = prospect_id_1,
                owner_id = owner_id_35,
                team_id = team_id_8,
                first_name = first_name,
                last_name = last_name,
                email = Some(email),
                custom_fields = custom_fields,
                list = list,
                job_title = job_title,
                company = company,
                linkedin_url = linkedin_url,
                phone = phone,
                phone_2 = None,
                phone_3 = None,
                city = city,
                state = state,
                country = country,
                timezone = timezone,
                prospect_category = prospect_category,
                last_contacted_at = last_contacted_at,
                last_contacted_at_phone = None,
                created_at = created_at,
                internal = prospectObjectInternal,
                latest_reply_sentiment_uuid = None,
                current_step_type = None,
                latest_task_done_at = None,
                prospect_uuid = None,
                owner_uuid = owner_uuid,
                updated_at = updated_at
              )


            }



            def init(): Future[Unit] = {
              val result = for {

                updateUserMappingForm <- Future.fromTry(trigger.findUserMapping(
                  teamId = 585L,
                  integration_type = IntegrationType.ZOHO
                ))

                updateFieldMappingForm <- Future.fromTry(trigger.findFieldMapping(
                  teamId = 585L,
                  integration_type = IntegrationType.ZOHO,
                  module_type = IntegrationModuleType.CONTACTS
                ))

                accessTokenData: Option[IntegrationTPAccessTokenResponse.FullTokenData] <- tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken(
                  teamId = 585L,
                  integration_type = IntegrationType.ZOHO
                )(
                  Logger = Logger,
                  ec = playDefaultExecutionContext,
                  ws = wSClient
                ).map {
                  case Left(eitherData) =>
                    println(s"updateUserMappingForm $updateUserMappingForm")
                    println(s"updateFieldMappingForm $updateFieldMappingForm")
                    println(s"eitherData is  defined $eitherData")
                    None
                  case Right(accessToken) =>
                    Some(accessToken)
                }


                createOrUpdate <- tIntegrationCRMService.createOrUpdateBatchContacts(
                  integrationType = IntegrationType.ZOHO,
                  moduleType = IntegrationModuleType.CONTACTS,
                  accessTokenData = accessTokenData.get,
                  contacts = Seq(
                    ProspectObjectWithOldProspectDeduplicationColumn(
                      prospectObject = ProspectFixtures.prospectObject.copy(
                        email = Some("<EMAIL>"),
                        first_name = Some("sourabh"),
                        last_name =Some("karmakar")
                      ),
                      oldProspectDeduplicationColumn = Some(OldProspectDeduplicationColumn(
                        email = None,
                        phone = Some("+************")
                      ))
                    )

                  ),
                  fieldMapping = updateFieldMappingForm.get,
                  userMapping = updateUserMappingForm.get,
                  accountId = 392L,
                  teamId = 585L
                )(
                  ws = wSClient,
                  ec = playDefaultExecutionContext,
                  system = system,
                  Logger = Logger
                )

              } yield {
                createOrUpdate
              }

              result.map {
                case Left(value) =>
                  println("Its this flow")
                  value match {
                    case CreateOrUpdateBatchContactsError.CommonCRMAPIError(err) =>
                      println(s"Error occurred CommonCRMAPIError $err")
                    case CreateOrUpdateBatchContactsError.InvalidModuleError(message) =>
                      println(s"Invalid Module Error $message")
                    case CreateOrUpdateBatchContactsError.HubspotFailureBatchError(message, failureRecords) =>
                      println(s"HubspotFailureBatchError message: $message failureRecords:$failureRecords")
                    case CreateOrUpdateBatchContactsError.SalesForceFailureBatchError(message, failureRecords) =>
                      println(s"HubspotFailureBatchError message: $message failureRecords:$failureRecords")
                    case CreateOrUpdateBatchContactsError.CRMFindOneByEmailAndModuleError(err) =>
                      println(s"CRMFindOneByEmailAndModuleError : ${err.message}")
                    case hsf: CreateOrUpdateBatchContactsError.HubspotSearchFailureError =>
                      println(s"HubspotSearchFailureError message: ${hsf.message} failureRecords:${hsf.failureMessages}")
                  }
                case Right(value) =>
                  print("Its Right")
                  println(s"Updated Item :: ${value.toString()}")
              }.recover{case e =>
                println(s"Error Occurred ${e.printStackTrace()}")

              }
            }


          }
          main.init()
        case "zoho_contact_search_check" =>

          implicit lazy val system: ActorSystem = ActorSystem()
          given Logger: SRLogger = new SRLogger("SrLogger")

          object main extends  TestAppTrait {


            object ProspectFixtures {

              private val dateTime_now = DateTime.now()
              private val owner_name = "owner_name"
              private val owner_email = "owner_email"
              private val email_domain = "email_domain"
              private val prospect_category_id_custom_3 = 3
              private val prospect_category_label_color = "color"
              private val account_id_34 = Some(34L)
              private val total_open_3 = 3
              private val total_click_4 = 4
              private val flag_32 = Json.obj()
              private val prospect_id_1 = 1
              private val owner_id_35 = 35L
              private val team_id_8 = 8L
              private val first_name = Option("first_name")
              private val last_name = Option("last_name")
              private val email = "email"
              private val custom_fields = Json.obj()
              private val list = Option("list")
              private val job_title = Option("job_title")
              private val company = Option("company")
              private val linkedin_url = Option("linkedin_url")
              private val phone = Option("+************")
              private val city = Option("city")
              private val state = Option("state")
              private val country = Option("country")
              private val timezone = Option("time_zone")
              private val prospect_category = "prospect_category"
              private val last_contacted_at = Option(dateTime_now)
              private val created_at = dateTime_now
              private val owner_uuid = AccountUuid("owner_uuid")
              private val updated_at = dateTime_now


              val prospectObjectInternal: ProspectObjectInternal = ProspectObjectInternal(
                owner_name = owner_name,
                magic_columns = List(),
                owner_email = owner_email,
                email_domain = Some(email_domain),
                invalid_email = None,
                last_contacted_at = None,
                last_replied_at = None,
                last_opened_at = None,
                  last_call_made_at = None,
                list_id = None,
                prospect_category_id_custom = prospect_category_id_custom_3,
                prospect_category_label_color = prospect_category_label_color,
                prospect_source = None,
                prospect_account_id = account_id_34,
                prospect_account = None,
                total_opens = total_open_3,
                total_clicks = total_click_4,
                active_campaigns = None,
                current_campaign_id = None,
                tags = Some(
                  List(
                    ProspectTag(tag_id = 123L, tag = "tag1", tag_uuid = ProspectTagUuid("tags_abcdefg")), ProspectTag(tag_id = 1234L, tag = "tag2", tag_uuid = ProspectTagUuid("tags_qwerty"))
                  )
                ),
                flags = flag_32,
                latest_reply_sentiment = None,
                prospect_account_uuid = None
              )
              val  prospectObject: ProspectObject = ProspectObject(
                id = prospect_id_1,
                owner_id = owner_id_35,
                team_id = team_id_8,
                first_name = first_name,
                last_name = last_name,
                email = Some(email),
                custom_fields = custom_fields,
                list = list,
                job_title = job_title,
                company = company,
                linkedin_url = linkedin_url,
                phone = phone,
                phone_2 = None,
                phone_3 = None,
                city = city,
                state = state,
                country = country,
                timezone = timezone,
                prospect_category = prospect_category,
                last_contacted_at = last_contacted_at,
                last_contacted_at_phone = None,
                created_at = created_at,
                internal = prospectObjectInternal,
                latest_reply_sentiment_uuid = None,
                current_step_type = None,
                latest_task_done_at = None,
                prospect_uuid = None,
                owner_uuid = owner_uuid,
                updated_at = updated_at
              )


            }



            def init(): Future[Unit] = {
              val result = for {

                updateUserMappingForm <- Future.fromTry(trigger.findUserMapping(
                  teamId = 585L,
                  integration_type = IntegrationType.ZOHO
                ))

                updateFieldMappingForm <- Future.fromTry(trigger.findFieldMapping(
                  teamId = 585L,
                  integration_type = IntegrationType.ZOHO,
                  module_type = IntegrationModuleType.CONTACTS
                ))

                accessTokenData: Option[IntegrationTPAccessTokenResponse.FullTokenData] <- tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken(
                  teamId = 585L,
                  integration_type = IntegrationType.ZOHO
                )(
                  Logger = Logger,
                  ec = playDefaultExecutionContext,
                  ws = wSClient
                ).map {
                  case Left(eitherData) =>
                    println(s"updateUserMappingForm $updateUserMappingForm")
                    println(s"updateFieldMappingForm $updateFieldMappingForm")
                    println(s"eitherData is  defined $eitherData")
                    None
                  case Right(accessToken) =>
                    println(s"Accesss token is ${accessToken}")
                    Some(accessToken)
                }


                foundContacts <- zohoOAuth.findAllByEmailsAndPhones(
                  module = IntegrationModuleType.CONTACTS,
                  accessTokenData = accessTokenData.get,
                  emails = Seq("<EMAIL>"),
                  phones = Seq("+************"),
                  apiDomain = accessTokenData.get.api_domain.get,
                  zohoModule = "Contacts"
                )(
                  ws = wSClient,
                  ec = playDefaultExecutionContext,
                  Logger = Logger
                )

              } yield {
                foundContacts
              }

              result.map {
                case Left(value) =>
                  println("Its this flow")
                  value match {
                    case FindBatchError.CommonCRMAPIError(err) => {
                      println(s"An error occurr ${err.toString}")
                    }
                    case FindBatchError.MalformedResponseStructureError(msg) => {
                      println(s"Malformed Response Received ${msg}")
                    }
                    case FindBatchError.InvalidModuleError(msg) => {
                      println(s"Invalid Module Error ${msg}")
                    }
                  }
                case Right(value) =>
                  print("Its Right")
                  println(s"Updated Item :: ${value.toString()}")
              }.recover{case e =>
                println(s"Error Occurred ${e.printStackTrace()}")

              }
            }


          }
          main.init()



        case "zoho_duplicate_contacts_check" =>

          implicit lazy val system: ActorSystem = ActorSystem()
          given Logger: SRLogger = new SRLogger("SrLogger")

          object main extends  TestAppTrait {


            object ProspectFixtures {

              private val dateTime_now = DateTime.now()
              private val owner_name = "owner_name"
              private val owner_email = "owner_email"
              private val email_domain = "email_domain"
              private val prospect_category_id_custom_3 = 3
              private val prospect_category_label_color = "color"
              private val account_id_34 = Some(34L)
              private val total_open_3 = 3
              private val total_click_4 = 4
              private val flag_32 = Json.obj()
              private val prospect_id_1 = 1
              private val owner_id_35 = 35L
              private val team_id_8 = 8L
              private val first_name = Option("Ashwin")
              private val last_name = Option("Gupta")
              private val email = "<EMAIL>"
              private val custom_fields = Json.obj()
              private val list = Option("list")
              private val job_title = Option("job_title")
              private val company = Option("company")
              private val linkedin_url = Option("linkedin_url")
              private val phone = Option("+************")
              private val city = Option("city")
              private val state = Option("state")
              private val country = Option("country")
              private val timezone = Option("time_zone")
              private val prospect_category = "prospect_category"
              private val last_contacted_at = Option(dateTime_now)
              private val created_at = dateTime_now
              private val owner_uuid = AccountUuid("owner_uuid")
              private val updated_at = dateTime_now


              val prospectObjectInternal: ProspectObjectInternal = ProspectObjectInternal(
                owner_name = owner_name,
                owner_email = owner_email,
                email_domain = Some(email_domain),
                invalid_email = None,
                last_contacted_at = None,
                last_replied_at = None,
                last_opened_at = None,
                  last_call_made_at = None,
                list_id = None,
                prospect_category_id_custom = prospect_category_id_custom_3,
                prospect_category_label_color = prospect_category_label_color,
                prospect_source = None,
                prospect_account_id = account_id_34,
                prospect_account = None,
                total_opens = total_open_3,
                total_clicks = total_click_4,
                active_campaigns = None,
                current_campaign_id = None,
                magic_columns = List(),
                tags = Some(
                  List(
                    ProspectTag(tag_id = 123L, tag = "tag1", tag_uuid = ProspectTagUuid("tags_abcdefg")), ProspectTag(tag_id = 1234L, tag = "tag2", tag_uuid = ProspectTagUuid("tags_qwerty"))
                  )
                ),
                flags = flag_32,
                latest_reply_sentiment = None,
                prospect_account_uuid = None
              )
              val  prospectObject: ProspectObject = ProspectObject(
                id = prospect_id_1,
                owner_id = owner_id_35,
                team_id = team_id_8,
                first_name = first_name,
                last_name = last_name,
                email = Some(email),
                custom_fields = custom_fields,
                list = list,
                job_title = job_title,
                company = company,
                linkedin_url = linkedin_url,
                phone = phone,
                phone_2 = None,
                phone_3 = None,
                city = city,
                state = state,
                country = country,
                timezone = timezone,
                prospect_category = prospect_category,
                last_contacted_at = last_contacted_at,
                last_contacted_at_phone = None,
                created_at = created_at,
                internal = prospectObjectInternal,
                latest_reply_sentiment_uuid = None,
                current_step_type = None,
                latest_task_done_at = None,
                prospect_uuid = None,
                owner_uuid = owner_uuid,
                updated_at = updated_at
              )


            }



            def init(): Future[Unit] = {
              println("This got Called")
              val result = for {

                updateUserMappingForm <- Future.fromTry(trigger.findUserMapping(
                  teamId = 599L,
                  integration_type = IntegrationType.ZOHO
                ))

                updateFieldMappingForm <- Future.fromTry(trigger.findFieldMapping(
                  teamId = 599L,
                  integration_type = IntegrationType.ZOHO,
                  module_type = IntegrationModuleType.CONTACTS
                ))

                accessTokenData: Option[IntegrationTPAccessTokenResponse.FullTokenData] <- tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken(
                  teamId = 599L,
                  integration_type = IntegrationType.ZOHO
                )(
                  Logger = Logger,
                  ec = playDefaultExecutionContext,
                  ws = wSClient
                ).map {
                  case Left(eitherData) =>
                    println(s"updateUserMappingForm $updateUserMappingForm")
                    println(s"updateFieldMappingForm $updateFieldMappingForm")
                    println(s"eitherData is  defined $eitherData")
                    None
                  case Right(accessToken) =>
                    Some(accessToken)
                }


                createOrUpdate1 <- tIntegrationCRMService.createOrUpdateBatchContacts(
                  integrationType = IntegrationType.ZOHO,
                  moduleType = IntegrationModuleType.CONTACTS,
                  accessTokenData = accessTokenData.get,
                  contacts = Seq(ProspectObjectWithOldProspectDeduplicationColumn(
                    prospectObject =  ProspectFixtures.prospectObject.copy(
                      phone = Some("+************"),
                      email =None,
                      first_name = Some("Kartik"),
                      last_name = Some("Arora")),
                    oldProspectDeduplicationColumn = None
                  )),
                  fieldMapping = updateFieldMappingForm.get,
                  userMapping = updateUserMappingForm.get,
                  accountId = 392L,
                  teamId = 599L
                )(
                  ws = wSClient,
                  ec = playDefaultExecutionContext,
                  system = system,
                  Logger = Logger
                )





                createOrUpdate2 <- tIntegrationCRMService.createOrUpdateBatchContacts(
                  integrationType = IntegrationType.ZOHO,
                  moduleType = IntegrationModuleType.CONTACTS,
                  accessTokenData = accessTokenData.get,
                  contacts = Seq(ProspectObjectWithOldProspectDeduplicationColumn(
                      prospectObject =  ProspectFixtures.prospectObject.copy(
                        phone = Some("+************"),
                        email =None,
                        first_name = Some("Kartik"),
                        last_name = Some("Arora")),
                      oldProspectDeduplicationColumn = None
                    )),
                  fieldMapping = updateFieldMappingForm.get,
                  userMapping = updateUserMappingForm.get,
                  accountId = 392L,
                  teamId = 599L
                )(
                  ws = wSClient,
                  ec = playDefaultExecutionContext,
                  system = system,
                  Logger = Logger
                )

                createOrUpdate3 <- tIntegrationCRMService.createOrUpdateBatchContacts(
                  integrationType = IntegrationType.ZOHO,
                  moduleType = IntegrationModuleType.CONTACTS,
                  accessTokenData = accessTokenData.get,
                  contacts = Seq(ProspectObjectWithOldProspectDeduplicationColumn(
                    prospectObject =  ProspectFixtures.prospectObject.copy(
                      phone = Some("+************"),
                      email =None,
                      first_name = Some("Kartik"),
                      last_name = Some("pratap")),
                    oldProspectDeduplicationColumn = Some(OldProspectDeduplicationColumn(
                      email = None,
                      phone = Some("+************")
                    ))
                  )),
                  fieldMapping = updateFieldMappingForm.get,
                  userMapping = updateUserMappingForm.get,
                  accountId = 392L,
                  teamId = 599L
                )(
                  ws = wSClient,
                  ec = playDefaultExecutionContext,
                  system = system,
                  Logger = Logger
                )

              } yield {
                Helpers.seqEitherToEitherSeq(Seq(createOrUpdate1,createOrUpdate2,createOrUpdate3))
              }

              result.map {


                case Left(value) =>
                  println("Its this flow")
                  value match {
                    case CreateOrUpdateBatchContactsError.CommonCRMAPIError(err) =>
                      println(s"Error occurred CommonCRMAPIError $err")
                    case CreateOrUpdateBatchContactsError.InvalidModuleError(message) =>
                      println(s"Invalid Module Error $message")
                    case CreateOrUpdateBatchContactsError.HubspotFailureBatchError(message, failureRecords) =>
                      println(s"HubspotFailureBatchError message: $message failureRecords:$failureRecords")
                    case CreateOrUpdateBatchContactsError.SalesForceFailureBatchError(message, failureRecords) =>
                      println(s"HubspotFailureBatchError message: $message failureRecords:$failureRecords")
                    case CreateOrUpdateBatchContactsError.CRMFindOneByEmailAndModuleError(err) =>
                      println(s"CRMFindOneByEmailAndModuleError : ${err.message}")
                    case hsf: CreateOrUpdateBatchContactsError.HubspotSearchFailureError =>
                      println(s"HubspotSearchFailureError message: ${hsf.message} failureRecords:${hsf.failureMessages}")
                  }
                case Right(value) =>
                  print("Its Right")
                  println(s"Updated Item :: ${value.toString()}")
              }.recover{case e =>
                println(s"Error Occurred ${e.printStackTrace()}")

              }
            }


          }
          main.init()


        case "zoho_findall_contacts_check" =>

          implicit lazy val system: ActorSystem = ActorSystem()
          given Logger: SRLogger = new SRLogger("SrLogger")

          object main extends  TestAppTrait {


            object ProspectFixtures {

              private val dateTime_now = DateTime.now()
              private val owner_name = "owner_name"
              private val owner_email = "owner_email"
              private val email_domain = "email_domain"
              private val prospect_category_id_custom_3 = 3
              private val prospect_category_label_color = "color"
              private val account_id_34 = Some(34L)
              private val total_open_3 = 3
              private val total_click_4 = 4
              private val flag_32 = Json.obj()
              private val prospect_id_1 = 1
              private val owner_id_35 = 35L
              private val team_id_8 = 8L
              private val first_name = Option("first_name")
              private val last_name = Option("last_name")
              private val email = "<EMAIL>"
              private val custom_fields = Json.obj()
              private val list = Option("list")
              private val job_title = Option("job_title")
              private val company = Option("company")
              private val linkedin_url = Option("linkedin_url")
              private val phone = Option("+************")
              private val city = Option("city")
              private val state = Option("state")
              private val country = Option("country")
              private val timezone = Option("time_zone")
              private val prospect_category = "prospect_category"
              private val last_contacted_at = Option(dateTime_now)
              private val created_at = dateTime_now
              private val owner_uuid = AccountUuid("owner_uuid")
              private val updated_at = dateTime_now


              val prospectObjectInternal: ProspectObjectInternal = ProspectObjectInternal(
                owner_name = owner_name,
                owner_email = owner_email,
                email_domain = Some(email_domain),
                invalid_email = None,
                last_contacted_at = None,
                last_replied_at = None,
                last_opened_at = None,
                  last_call_made_at = None,
                list_id = None,
                prospect_category_id_custom = prospect_category_id_custom_3,
                prospect_category_label_color = prospect_category_label_color,
                prospect_source = None,
                prospect_account_id = account_id_34,
                prospect_account = None,
                total_opens = total_open_3,
                total_clicks = total_click_4,
                active_campaigns = None,
                current_campaign_id = None,
                magic_columns = List(),
                tags = Some(
                  List(
                    ProspectTag(tag_id = 123L, tag = "tag1", tag_uuid = ProspectTagUuid("tags_abcdefg")), ProspectTag(tag_id = 1234L, tag = "tag2", tag_uuid = ProspectTagUuid("tags_qwerty"))
                  )
                ),
                flags = flag_32,
                latest_reply_sentiment = None,
                prospect_account_uuid = None
              )
              val  prospectObject: ProspectObject = ProspectObject(
                id = prospect_id_1,
                owner_id = owner_id_35,
                team_id = team_id_8,
                first_name = first_name,
                last_name = last_name,
                email = Some(email),
                custom_fields = custom_fields,
                list = list,
                job_title = job_title,
                company = company,
                linkedin_url = linkedin_url,
                phone = phone,
                phone_2 = None,
                phone_3 = None,
                city = city,
                state = state,
                country = country,
                timezone = timezone,
                prospect_category = prospect_category,
                last_contacted_at = last_contacted_at,
                last_contacted_at_phone = None,
                created_at = created_at,
                internal = prospectObjectInternal,
                latest_reply_sentiment_uuid = None,
                current_step_type = None,
                latest_task_done_at = None,
                prospect_uuid = None,
                owner_uuid = owner_uuid,
                updated_at = updated_at
              )


            }



            def init(): Future[Unit] = {
              println("********TestApp To Find all contacts which matches the phone or email*********")
              println("Enter Team Id")
              val teamId = scala.io.StdIn.readLong()
              println("Enter Email to search from ")
              val emailInLine = scala.io.StdIn.readLine()
              val emailToSearch = if(emailInLine.trim.nonEmpty){
                Some(emailInLine.trim)
              }else{
                None
              }

              println("Enter Phone to search from ")

              val phoneInLine = scala.io.StdIn.readLine()
              val phoneToSearch = if(phoneInLine.trim.nonEmpty){
                Some(phoneInLine.trim)
              }else{
                None
              }

              println(s"Search email provided ${emailToSearch}")
              println(s"Search phone provided ${phoneToSearch}")

              val result = for {

                updateUserMappingForm <- Future.fromTry(trigger.findUserMapping(
                  teamId = teamId,
                  integration_type = IntegrationType.ZOHO
                ))

                updateFieldMappingForm <- Future.fromTry(trigger.findFieldMapping(
                  teamId = teamId,
                  integration_type = IntegrationType.ZOHO,
                  module_type = IntegrationModuleType.CONTACTS
                ))

                accessTokenData: Option[IntegrationTPAccessTokenResponse.FullTokenData] <- tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken(
                  teamId = teamId,
                  integration_type = IntegrationType.ZOHO
                )(
                  Logger = Logger,
                  ec = playDefaultExecutionContext,
                  ws = wSClient
                ).map {
                  case Left(eitherData) =>
                    println(s"updateUserMappingForm $updateUserMappingForm")
                    println(s"updateFieldMappingForm $updateFieldMappingForm")
                    println(s"eitherData is  defined $eitherData")
                    None
                  case Right(accessToken) =>
                    Some(accessToken)
                }


                found <- tIntegrationCRMService.findAllByProspectAndModule(
                  integrationType = IntegrationType.ZOHO,
                  accessTokenData = accessTokenData.get,
                  prospect = ProspectFixtures.prospectObject.copy(
                    phone = phoneToSearch,
                    email = emailToSearch,
                    first_name = Some("Kartik"),
                    last_name = Some("Arora")),
                  module = IntegrationModuleType.CONTACTS,
                  statusColumn = None
                )(
                  ws = wSClient,
                  ec = playDefaultExecutionContext,
                  system = system,
                  Logger = Logger
                )

              } yield {
                found
              }

              result.map {
                case Left(value) =>
                  println("Its this flow")
                  value match {
                    case FindBatchError.CommonCRMAPIError(err) => println(s"CommonCRMAPIError $err")
                    case FindBatchError.MalformedResponseStructureError(msg) => println(s"MalformedResponseStructureError $msg")
                    case FindBatchError.InvalidModuleError(msg) => println(s"InvalidModuleError $msg")
                  }
                case Right(value) =>
                  print("Its Right")
                  println(s"Updated Item :: ${value.toString()}")
              }.recover{case e =>
                println(s"Error Occurred ${e.printStackTrace()}")

              }
            }


          }
          main.init()

        case "zoho_batch_update" =>

          implicit lazy val system: ActorSystem = ActorSystem()
          given Logger: SRLogger = new SRLogger("SrLogger")

          object main extends  TestAppTrait {


            object ProspectFixtures {

              private val dateTime_now = DateTime.now()
              private val owner_name = "owner_name"
              private val owner_email = "owner_email"
              private val email_domain = "email_domain"
              private val prospect_category_id_custom_3 = 3
              private val prospect_category_label_color = "color"
              private val account_id_34 = Some(34L)
              private val total_open_3 = 3
              private val total_click_4 = 4
              private val flag_32 = Json.obj()
              private val prospect_id_1 = 1
              private val owner_id_35 = 35L
              private val team_id_8 = 8L
              private val first_name = Option("first_name")
              private val last_name = Option("last_name")
              private val email = "<EMAIL>"
              private val custom_fields = Json.obj()
              private val list = Option("list")
              private val job_title = Option("job_title")
              private val company = Option("company")
              private val linkedin_url = Option("linkedin_url")
              private val phone = Option("+************")
              private val city = Option("city")
              private val state = Option("state")
              private val country = Option("country")
              private val timezone = Option("time_zone")
              private val prospect_category = "prospect_category"
              private val last_contacted_at = Option(dateTime_now)
              private val created_at = dateTime_now
              private val owner_uuid = AccountUuid("owner_uuid")
              private val updated_at = dateTime_now


              val prospectObjectInternal: ProspectObjectInternal = ProspectObjectInternal(
                owner_name = owner_name,
                owner_email = owner_email,
                email_domain = Some(email_domain),
                invalid_email = None,
                last_contacted_at = None,
                last_replied_at = None,
                last_opened_at = None,
                  last_call_made_at = None,
                list_id = None,
                prospect_category_id_custom = prospect_category_id_custom_3,
                prospect_category_label_color = prospect_category_label_color,
                prospect_source = None,
                prospect_account_id = account_id_34,
                prospect_account = None,
                total_opens = total_open_3,
                total_clicks = total_click_4,
                active_campaigns = None,
                current_campaign_id = None,
                magic_columns = List(),
                tags = Some(
                  List(
                    ProspectTag(tag_id = 123L, tag = "tag1", tag_uuid = ProspectTagUuid("tags_abcdefg")), ProspectTag(tag_id = 1234L, tag = "tag2", tag_uuid = ProspectTagUuid("tags_qwerty"))
                  )
                ),
                flags = flag_32,
                latest_reply_sentiment = None,
                prospect_account_uuid = None
              )
              val  prospectObject: ProspectObject = ProspectObject(
                id = prospect_id_1,
                owner_id = owner_id_35,
                team_id = team_id_8,
                first_name = first_name,
                last_name = last_name,
                email = Some(email),
                custom_fields = custom_fields,
                list = list,
                job_title = job_title,
                company = company,
                linkedin_url = linkedin_url,
                phone = phone,
                phone_2 = None,
                phone_3 = None,
                city = city,
                state = state,
                country = country,
                timezone = timezone,
                prospect_category = prospect_category,
                last_contacted_at = last_contacted_at,
                last_contacted_at_phone = None,
                created_at = created_at,
                internal = prospectObjectInternal,
                latest_reply_sentiment_uuid = None,
                current_step_type = None,
                latest_task_done_at = None,
                prospect_uuid = None,
                owner_uuid = owner_uuid,
                updated_at = updated_at
              )


            }



            def init(): Future[Unit] = {
              println("********TestApp To Find all contacts which matches the phone or email*********")
              println("Enter Team Id")
              val teamId = scala.io.StdIn.readLong()
              println("Enter Email to search from ")
              val emailInLine = scala.io.StdIn.readLine()
              val emailToSearch = if(emailInLine.trim.nonEmpty){
                Some(emailInLine.trim)
              }else{
                None
              }

              println("Enter Phone to search from ")

              val phoneInLine = scala.io.StdIn.readLine()
              val phoneToSearch = if(phoneInLine.trim.nonEmpty){
                Some(phoneInLine.trim)
              }else{
                None
              }

              println(s"Search email provided ${emailToSearch}")
              println(s"Search phone provided ${phoneToSearch}")

              val result = for {

                updateUserMappingForm <- Future.fromTry(trigger.findUserMapping(
                  teamId = teamId,
                  integration_type = IntegrationType.ZOHO
                ))

                updateFieldMappingForm <- Future.fromTry(trigger.findFieldMapping(
                  teamId = teamId,
                  integration_type = IntegrationType.ZOHO,
                  module_type = IntegrationModuleType.CONTACTS
                ))

                accessTokenData: Option[IntegrationTPAccessTokenResponse.FullTokenData] <- tIntegrationCRMService.fetchTokensFromDBAndRefreshAccessToken(
                  teamId = teamId,
                  integration_type = IntegrationType.ZOHO
                )(
                  Logger = Logger,
                  ec = playDefaultExecutionContext,
                  ws = wSClient
                ).map {
                  case Left(eitherData) =>
                    println(s"updateUserMappingForm $updateUserMappingForm")
                    println(s"updateFieldMappingForm $updateFieldMappingForm")
                    println(s"eitherData is  defined $eitherData")
                    None
                  case Right(accessToken) =>
                    Some(accessToken)
                }


                found <- tIntegrationCRMService.createOrUpdateBatchContacts(

                  integrationType = IntegrationType.ZOHO,
                  moduleType = IntegrationModuleType.CONTACTS,
                  accessTokenData = accessTokenData.get,
                  contacts = Seq(
                    ProspectObjectWithOldProspectDeduplicationColumn(
                      prospectObject  = ProspectObject.dummyProspectObj.copy(
                        email = Some("<EMAIL>"),
                        first_name=Some("anand"),
                        last_name = Some("sharma")

                      ),
                      oldProspectDeduplicationColumn  = Some(OldProspectDeduplicationColumn(
                        email=Some("<EMAIL>"),
                        phone=None
                      ))
                    ),
                    ProspectObjectWithOldProspectDeduplicationColumn(
                      prospectObject  = ProspectObject.dummyProspectObj.copy(
                        email = Some("<EMAIL>"),
                        first_name=Some("arjuna"),
                        last_name = Some("singh")
                      ),
                      oldProspectDeduplicationColumn  = Some(OldProspectDeduplicationColumn(
                        email=Some("<EMAIL>"),
                        phone=None
                      ))
                    ),

                  ),
                  fieldMapping = updateFieldMappingForm.get,
                  userMapping = updateUserMappingForm.get,
                  accountId = 392,
                  teamId = 633


                )(
                  ws = wSClient,
                  ec = playDefaultExecutionContext,
                  system = system,
                  Logger = Logger
                )

              } yield {
                found
              }

              result.map {
                case Left(value) => {
                  println("Its this flow")
                  value match {
                    case CreateOrUpdateBatchContactsError.SalesForceFailureBatchError(message, failureRecords) => println(s"SalesForceFailureBatchError msg ${message} failureRecords ${failureRecords}")
                    case CreateOrUpdateBatchContactsError.CRMFindOneByEmailAndModuleError(err) => println(s"CRMFindOneByEmailAndModuleError err is ${err.message}")
                    case CreateOrUpdateBatchContactsError.CommonCRMAPIError(err) => println(s"CommonCRMAPIError $err")
                    case CreateOrUpdateBatchContactsError.HubspotFailureBatchError(msg, failureRecords) => println(s"MalformedResponseStructureError $msg")
                    case CreateOrUpdateBatchContactsError.InvalidModuleError(msg) => println(s"InvalidModuleError $msg")
                    case CreateOrUpdateBatchContactsError.HubspotSearchFailureError(msg, failureMessages) => println(s"Error occurred, msg: ${msg} failureMessage: ${failureMessages}")
                  }
                }

                case Right(value) => {
                  print("Its Right")
                  println(s"Updated Item :: ${value.toString()}")
                }
              }.recover{case e =>
                println(s"Error Occurred ${e.printStackTrace()}")

              }
            }


          }
          main.init()


      }
    }
  }
}
