package utils

import api.accounts.TeamId

object AppConfigUtil {






  val teams_for_campaign_prospects_step_id_migration: Seq[TeamId] = Seq(
//  TeamId(24),
//    TeamId(9768),
//    TeamId(6412),
//    TeamId(5914),
//    TeamId(8049),
//    TeamId(2766),
//    TeamId(9922),
//    TeamId(11119),
//    TeamId(12016),
//    TeamId(2917),
//    TeamId(2917),
//    TeamId(2766),
//    TeamId(2766),
//    TeamId(12384),
//    TeamId(12384),
//    TeamId(11366),
//    TeamId(12384),
//    TeamId(5376),
//    TeamId(4260),
//    TeamId(2766),
//    TeamId(5914),
//    TeamId(2766),
//    TeamId(4041),
//    TeamId(5290),
//    TeamId(3225),
//    TeamId(10467),
//    TeamId(12384),
//    TeamId(2766),
//  TeamId(10960),
//    TeamId(11734),
//    TeamId(10176),
//    TeamId(9324),
//    TeamId(8199),
//    TeamId(9283),
    TeamId(11432),
    TeamId(12673),
    TeamId(11185)
    // adding team id that I got from the logs
  )

}
