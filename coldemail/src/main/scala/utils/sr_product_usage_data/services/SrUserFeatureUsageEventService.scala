package utils.sr_product_usage_data.services

import api.accounts.models.OrgId
import api.internal_support.service.FunnelStageError
import api.reports.models.{AudienceReportData, InternalAudienceReportType, InternalSRUsageResult, SrInternalFeatureUsage, SrInternalFeatureUsageDAO}
import org.joda.time.DateTime
import play.api.libs.json.{<PERSON><PERSON>, Writes}
import utils.SRLogger
import utils.sr_product_usage_data.models.{SrOrganizationFunnelStage, SrUserFeatureUsageEvent}

import scala.util.{Failure, Success, Try}

case class org_feature_usage_data(
                                   sr_internal_feature_usage: SrInternalFeatureUsage,
                                   current_stage: String
                              )

object org_feature_usage_data{
  implicit val writes: Writes[org_feature_usage_data] = Json.writes[org_feature_usage_data]
}

class SrUserFeatureUsageEventService(
                                      srInternalFeatureUsageDAO:SrInternalFeatureUsageDAO
                                    ) {

  def addFeatureUsageEvent(
    orgId: Long,
    event: SrUserFeatureUsageEvent
  ): Try[Int] = {

    val logger = new SRLogger(
      logRequestId = s"SrUserFeatureUsageEventService.addFeatureUsageEvent: "
    )

    logger.debug(s"called with orgId: $orgId :: event: $event")

    event match {
      case e: SrUserFeatureUsageEvent.UserOnboarded =>
        srInternalFeatureUsageDAO.addOrgOnboardingEventInDB(
            orgId = orgId,
            trial_started_at = e.trial_started_at
          )

      case e =>
        srInternalFeatureUsageDAO
        .updateOrgUseFeatureUsageStatusInDB(
          orgId = orgId,
          event = e
        )

    }

  }

  def getCurrentStage(org: SrInternalFeatureUsage):  SrUserFeatureUsageEvent = {


      val currentStage = {

        if (org.first_started_campaign_at.isDefined) {
          SrUserFeatureUsageEvent.StartedFirstCampaign(
            first_started_campaign_at = org.first_started_campaign_at.get
          )
        } else if (org.added_first_email_step_in_campaign_at.isDefined) {
          SrUserFeatureUsageEvent.AddedFirstEmailContentInCampaign(
            added_first_email_content_in_campaign_at = org.added_first_email_step_in_campaign_at.get
          )
        } else if (org.added_first_prospect_in_campaign_at.isDefined) {
          SrUserFeatureUsageEvent.AddedFirstProspectInCampaign(
            added_first_prospect_in_campaign_at = org.added_first_prospect_in_campaign_at.get
          )
        } else if (org.integrated_first_email_setting_in_campaign_at.isDefined) {
          SrUserFeatureUsageEvent.IntegratedFirstEmailSettingInCampaign(
            integrated_first_email_setting_in_campaign_at = org.integrated_first_email_setting_in_campaign_at.get
          )
        } else if (org.created_first_campaign_at.isDefined) {
          SrUserFeatureUsageEvent.CreatedFirstCampaign(
            created_first_campaign_at = org.created_first_campaign_at.get
          )
        } else if (org.orgDetails.trial_started_at.isDefined)  { // trial started at will be add only after onboarding
          SrUserFeatureUsageEvent.UserOnboarded(
            trial_started_at = org.orgDetails.trial_started_at.get
          )
        } else {
          if (org.is_account_verified) {
            SrUserFeatureUsageEvent.UserVerified(
              created_at = org.orgDetails.org_created_at
            )
          } else {
            SrUserFeatureUsageEvent.UserRegistered(
              created_at = org.orgDetails.org_created_at
            )
          }
        }
      }

      currentStage
  }

  def getFunnelStageForTrialUsers(days:Option[Int]):Either[FunnelStageError, List[org_feature_usage_data]] = {
    val reportStartFrom = days.map(days => {
      val today = new DateTime().withTimeAtStartOfDay()
      today.minusDays(days)
    })
    srInternalFeatureUsageDAO.getFunnelStage(reportStartFrom=reportStartFrom) match {
      case Success(orgs) =>

        val res = orgs.map{org =>
            val currentStage = getCurrentStage(org = org)
            val orgUsageDetails  = org_feature_usage_data(
              sr_internal_feature_usage = org,
              current_stage = currentStage.srUserFeatureUsageEventType.toKey
            )

            if (orgUsageDetails.current_stage=="user_registered" || orgUsageDetails.current_stage=="user_verified"){
              val updatedOrgDetails = orgUsageDetails.sr_internal_feature_usage.orgDetails.copy(plan_type = "abandoned",plan_id = "abandoned")
              val updatedInternalFeature = orgUsageDetails.sr_internal_feature_usage.copy(orgDetails=updatedOrgDetails)
              orgUsageDetails.copy(sr_internal_feature_usage=updatedInternalFeature)
            } else {
              orgUsageDetails
            }

        }
        Right(res)

      case Failure(e) =>
        Left(FunnelStageError.SQLError(err = e))
    }

  }


  def getAdoptionStageReport(logger: SRLogger): Try[List[InternalSRUsageResult]] = Try {
    srInternalFeatureUsageDAO.getInternalSRUsageData() match {
      case Success(internalSRUsageResults: List[InternalSRUsageResult]) =>
        internalSRUsageResults
      case Failure(exception) =>
        logger.error("srInternalFeatureUsageDAO.getInternalSRUsageData FAILED", err = exception)
        throw exception
    }
  }

  def getAudienceReportData(report_type: InternalAudienceReportType, logger: SRLogger): Try[List[AudienceReportData]] = Try {
    srInternalFeatureUsageDAO.getAudienceReportData(report_type = report_type) match {
      case Success(audienceReportData: List[AudienceReportData]) =>
        audienceReportData
      case Failure(exception) =>
        logger.error("srInternalFeatureUsageDAO.getAudienceReportData FAILED", err = exception)
        throw exception
    }
  }

  def getOrgFunnelStageForDripCampaigns():Try[List[SrOrganizationFunnelStage]] = Try {
    srInternalFeatureUsageDAO.getOrgsFunnelStageForDrips() match {
      case Failure(e) =>

        throw e

      case Success(orgs) =>

        val orgsFunnelStageList  = orgs
          .map(org => {

          val currentStage = getCurrentStage(org = org)

          val orgFunnelStage =  if (org.orgDetails.trial_started_at.isDefined) SrOrganizationFunnelStage(
            org_id = org.orgDetails.org_id,
            is_agency = org.orgDetails.is_agency,
            trial_started_at = org.orgDetails.trial_started_at.get,
            trial_ends_at = org.orgDetails.trial_ends_at,
            plan_type = org.orgDetails.plan_type,
            current_stage = currentStage,
            org_prospect_id_for_drip = org.org_prospect_id_for_drip
          ) else throw new Exception("Trail_Started_at should be defined for onboarding drip campaign")

            orgFunnelStage

        })

        orgsFunnelStageList

    }
  }

  def getCurrentOnboardingFunnelStage(orgId: Long): Try[Option[SrUserFeatureUsageEvent]] = Try{
    srInternalFeatureUsageDAO.getFunnelStageByOrgId(orgId = orgId) match {
      case Failure(err) => throw err
      case Success(None) => None
      case Success(Some(value)) => Some(getCurrentStage(org = value))
    }

  }

}
