package utils.sr_product_usage_data.models

import org.joda.time.DateTime
import utils.enum_sr_utils.SREnumJsonUtils

import scala.util.Try

sealed trait SrUserFeatureUsageEventType {
  def toKey: String
}

object SrUserFeatureUsageEventType extends SREnumJsonUtils[SrUserFeatureUsageEventType]{

  protected val enumName: String = "SrUserFeatureUsageEventType"

  val user_verified= "user_verified"
  val user_onboarded = "user_onboarded"
  val created_first_campaign = "created_first_campaign"
  val integrated_first_email_setting_in_campaign = "integrated_first_email_setting_in_campaign"
  val added_first_prospect_in_campaign = "added_first_prospect_in_campaign"
  val added_first_email_content_in_campaign = "added_first_email_content_in_campaign"
  val started_first_campaign = "started_first_campaign"
  val trial_expired = "trial_expired"
  val user_registered = "user_registered"

  case object UserVerifiedType extends SrUserFeatureUsageEventType {
    override def toKey: String = user_verified
  }

  case object UserRegisteredType extends SrUserFeatureUsageEventType {
    override def toKey: String = user_registered
  }

  case object UserOnboardedType extends SrUserFeatureUsageEventType {
    override def toKey: String = user_onboarded
  }

  case object CreatedFirstCampaignType extends SrUserFeatureUsageEventType {
    override def toKey: String = created_first_campaign
  }

  case object IntegratedFirstEmailSettingInCampaignType extends SrUserFeatureUsageEventType {
    override def toKey: String = integrated_first_email_setting_in_campaign
  }

  case object AddedFirstProspectInCampaignType extends SrUserFeatureUsageEventType {
    override def toKey: String = added_first_prospect_in_campaign
  }

  case object AddedFirstEmailContentInCampaignType extends SrUserFeatureUsageEventType {
    override def toKey: String = added_first_email_content_in_campaign
  }

  case object StartedFirstCampaignType extends SrUserFeatureUsageEventType {
    override def toKey: String = started_first_campaign
  }

  case object TrialExpiredType extends SrUserFeatureUsageEventType {
    override def toKey: String = trial_expired
  }



  override def fromKey(key: String): Try[SrUserFeatureUsageEventType] = Try{
    key match {
      case `user_onboarded` => UserOnboardedType
      case `created_first_campaign` => CreatedFirstCampaignType
      case `integrated_first_email_setting_in_campaign` => IntegratedFirstEmailSettingInCampaignType
      case `added_first_prospect_in_campaign` => AddedFirstProspectInCampaignType
      case `added_first_email_content_in_campaign` => AddedFirstEmailContentInCampaignType
      case `started_first_campaign` => StartedFirstCampaignType
      case `trial_expired` => TrialExpiredType

    }
  }
  override def toKey(value: SrUserFeatureUsageEventType): String = value.toKey
}

sealed trait SrUserFeatureUsageEvent {
  def srUserFeatureUsageEventType: SrUserFeatureUsageEventType
}
object SrUserFeatureUsageEvent {

  case class UserVerified(
                                      created_at: DateTime
                                      ) extends  SrUserFeatureUsageEvent{
    final val srUserFeatureUsageEventType = SrUserFeatureUsageEventType.UserVerifiedType
  }

  case class UserRegistered(
                                       created_at: DateTime
                                     ) extends SrUserFeatureUsageEvent {
    final val srUserFeatureUsageEventType = SrUserFeatureUsageEventType.UserRegisteredType
  }


  case class UserOnboarded(
    trial_started_at: DateTime
  ) extends SrUserFeatureUsageEvent {
    final val srUserFeatureUsageEventType = SrUserFeatureUsageEventType.UserOnboardedType
  }

  case class CreatedFirstCampaign(
    created_first_campaign_at: DateTime
  ) extends SrUserFeatureUsageEvent {
    final val srUserFeatureUsageEventType = SrUserFeatureUsageEventType.CreatedFirstCampaignType
  }


  case class IntegratedFirstEmailSettingInCampaign(
    integrated_first_email_setting_in_campaign_at: DateTime
  ) extends SrUserFeatureUsageEvent {
    final val srUserFeatureUsageEventType = SrUserFeatureUsageEventType.IntegratedFirstEmailSettingInCampaignType
  }

  case class AddedFirstProspectInCampaign(
    added_first_prospect_in_campaign_at: DateTime
  ) extends SrUserFeatureUsageEvent {
    final val srUserFeatureUsageEventType = SrUserFeatureUsageEventType.AddedFirstProspectInCampaignType
  }


  case class AddedFirstEmailContentInCampaign(
    added_first_email_content_in_campaign_at: DateTime
  ) extends SrUserFeatureUsageEvent {
    final val srUserFeatureUsageEventType = SrUserFeatureUsageEventType.AddedFirstEmailContentInCampaignType
  }

  case class StartedFirstCampaign(
    first_started_campaign_at: DateTime
  ) extends SrUserFeatureUsageEvent {
    final val srUserFeatureUsageEventType = SrUserFeatureUsageEventType.StartedFirstCampaignType
  }

  case class TrialExpired(trial_expired_at: DateTime) extends SrUserFeatureUsageEvent {
    final val srUserFeatureUsageEventType = SrUserFeatureUsageEventType.TrialExpiredType
  }

}