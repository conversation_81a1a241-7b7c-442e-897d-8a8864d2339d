package utils.email_notification.models

import api.accounts.TeamId
import api.accounts.models.{AccountId, OrgId}
import org.joda.time.DateTime

case class EmailNotificationLog(
                                 notification_type: NotificationType,
                                 org_id: OrgId,
                                 team_id: Option[TeamId],
                                 account_id: AccountId,
                                 sent_at: DateTime,
                                 sent_to_email_address: String
  )
