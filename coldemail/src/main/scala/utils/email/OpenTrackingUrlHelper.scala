package utils.email

import api.AppConfig
import api.accounts.TeamId
import api.accounts.models.OrgId
import api.emails.EmailsScheduledUuid
import play.api.Logging
import utils.StringUtils

import scala.util.Try

case class OpenTrackingEmailIdAndUuid(
                                       emailScheduledId: Option[Long],
                                       emailsScheduledUuidAndTeamId: Option[(EmailsScheduledUuid, TeamId)],
                                     )

object OpenTrackingUrlHelper {

  def constructOpenTrackingCode(
                                 emailsScheduledUuid: EmailsScheduledUuid,
                                 teamId: TeamId
                               ): String = {
    s"${teamId.id}${AppConfig.trackingLinksDelimiter}${emailsScheduledUuid.uuid}".toLowerCase
  }

  def constructOpenTrackingUrl(
                                emailsScheduledUuid: EmailsScheduledUuid,
                                teamId: TeamId,
                                customDomain: Option[String],
                                defaultTrackingDomain: String
                              ): String = {

    val code = constructOpenTrackingCode(
      emailsScheduledUuid = emailsScheduledUuid,
      teamId = teamId
    )

    val unSubEncoded: String = StringUtils.base32EncodeURIString(code)

    //    s"${AppConfig.applicationDomain}/assets/get_image.png?id=$code"

    val trackingDomain = if (customDomain.isDefined) s"https://${customDomain.get}" else s"https://$defaultTrackingDomain"

    s"$trackingDomain/ot2/$unSubEncoded/open"
  }

  def constructOpenTrackingImgHtml(
                              emailsScheduledUuid: EmailsScheduledUuid,
                              customDomain: Option[String],
                              defaultTrackingDomain: String,
                              teamId: TeamId,
                              orgId: OrgId
                            ): String = {

    val openTrackingLink = constructOpenTrackingUrl(
      emailsScheduledUuid = emailsScheduledUuid,
      teamId = teamId,
      customDomain = customDomain,
      defaultTrackingDomain = defaultTrackingDomain
    )

      s"""<img width="1" height="1" style="display: block;" alt="" src="${openTrackingLink}">"""

  }

  def deconstructOpenTrackingCode(decoded: String): Try[OpenTrackingEmailIdAndUuid] = Try {
    // New format: teamId + delimiter + emailsScheduledUuid
    val parts = decoded.split(AppConfig.trackingLinksDelimiter)
    if (parts.length != 2) {
      throw new IllegalArgumentException(s"Invalid tracking code format: $decoded")
    }
    OpenTrackingEmailIdAndUuid(
      emailScheduledId = None,
      emailsScheduledUuidAndTeamId = Some(EmailsScheduledUuid(parts(1)), TeamId(parts(0).toLong)),
    )
  }

  def deconstructOpenTrackingUrl(code: String): Try[OpenTrackingEmailIdAndUuid] = Try {
    val decoded = StringUtils.base32DecodeURIString(code)
    
    if (decoded.contains(AppConfig.trackingLinksDelimiter)) {

      deconstructOpenTrackingCode(decoded = decoded).get
    } else {
      // Old format: just emailScheduledId
      val emailScheduledId = decoded.toLong
      OpenTrackingEmailIdAndUuid(
        emailScheduledId = Some(emailScheduledId),
        emailsScheduledUuidAndTeamId = None
      )
    }
  }

}
