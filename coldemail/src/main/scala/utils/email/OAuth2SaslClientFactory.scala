package utils.email

/*
import java.util.Map
import javax.security.auth.callback.CallbackHandler
import javax.security.sasl.{SaslClient, SaslClientFactory}

import play.api.Logging
import utils.email.OAuth2SaslClientFactory._
*/

/* NOTE: 6-Dec-2023: This is used only in GmailService which is no longer used, after Gmail
deprecated imap / smtp access via Google Access Tokens last year. We have since moved to Gmail ASP
flow which goes directly via SmtpImapService instead.

opened it back on 2-oct-2024

When upgrading from Java17 from Java8, compilation is failing because of this with the following error,
so commenting it out:

[error] /Users/<USER>/Documents/projects/coldemail/coldemail/src/main/scala/utils/email/GmailService.scala:37:32: 
constructor Provider in class Provider is deprecated
[error]   class OAuth2Provider extends Provider("Google OAuth2 Provider", 1.0, "Provides the XOAUTH2 SASL Mechanism") {
[error]


 */

object OAuth2SaslClientFactory {

  val OAUTH_TOKEN_PROP: String = "mail.imaps.sasl.mechanisms.oauth2.oauthToken"

}

/*
class OAuth2SaslClientFactory extends SaslClientFactory with Logging {

  def createSaslClient(mechanisms: Array[String],
    authorizationId: String,
    protocol: String,
    serverName: String,
    props: Map[String, _],
    callbackHandler: CallbackHandler): SaslClient = {
    var matchedMechanism: Boolean = false
    for (i <- mechanisms.indices
         if "XOAUTH2".equalsIgnoreCase(mechanisms(i))) {
      matchedMechanism = true
      //break
    }
    if (!matchedMechanism) {
      logger.info("Failed to match any mechanisms")
      null
    }
    new OAuth2SaslClient(props.get(OAUTH_TOKEN_PROP).asInstanceOf[String],
      callbackHandler)
  }

  def getMechanismNames(props: Map[String, _]): Array[String] =
    Array("XOAUTH2")

}
*/
