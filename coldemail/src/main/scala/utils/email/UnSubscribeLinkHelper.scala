package utils.email

import api.AppConfig
import api.accounts.TeamId
import api.campaigns.services.CampaignId
import api.emails.EmailsScheduledUuid
import api.prospects.models.{ProspectId, StepId}
import utils.StringUtils
import utils.emailvalidation.EmailValidationService

import scala.util.Try


object UnSubscribeLinkHelper {

  def constructUnsubCode(
                          emailsScheduledUuid: EmailsScheduledUuid,
                          teamId: TeamId,
                        ): String = {
    s"${teamId.id}${AppConfig.trackingLinksDelimiter}${emailsScheduledUuid.uuid}".toLowerCase
  }

  def constructUnsubUrl(
                         emailsScheduledUuid: EmailsScheduledUuid,
                         teamId: TeamId,
                         customDomain: Option[String],
                         defaultUnsubscribeDomain: String
                       ): String = {

    val unsubscribeLinkId: String = constructUnsubCode(
      emailsScheduledUuid = emailsScheduledUuid,
      teamId = teamId
    )

    val unSubEncoded: String = StringUtils.base32EncodeURIString(unsubscribeLinkId)

    val trackingDomain = if (customDomain.isDefined) s"https://${customDomain.get}" else s"https://$defaultUnsubscribeDomain"

    s"$trackingDomain/tv4/$unSubEncoded/optout"
  }

  def deconstructUnsubCode(code: String): Try[DecryptedUnsubLinkV2] = {
    Try {
      val decoded = StringUtils.base32DecodeURIString(str = code)

      // Try new format first (with ___ delimiter)
      val newFormatFields = decoded.split(AppConfig.trackingLinksDelimiter, 2)
      if (newFormatFields.length == 2) {
        DecryptedUnsubLinkV2(
          emailScheduledId = None,
          emailsScheduledUuidAndTeamId = if (newFormatFields(1).isEmpty) None else Some((
            EmailsScheduledUuid(uuid = newFormatFields(1)),
            TeamId(id = newFormatFields(0).toLong)
          ))
        )
      } else {
        // Handle old format (with - delimiter)
        val fields: Array[String] = decoded.split("-", 4)
        if (fields.length != 4) {
          throw new Exception("Invalid old format: wrong number of fields")
        }
        
        DecryptedUnsubLinkV2(
          emailScheduledId = Some(fields(3).toLong),
          emailsScheduledUuidAndTeamId = None
        )
      }
    }
  }

  val LIST_UNSUBSCRIBE_EMAIL_ALIAS_KEY = "unsubscribesrmail"


  def constructUnsubHeader(
                            emailsScheduledUuid: EmailsScheduledUuid,
                            teamId: TeamId,
                            replyToEmail: String
                          ): String = {

    val emailSplit = EmailValidationService.getNameDomainAndAliasFromEmailV2(email = replyToEmail)
    val domain = emailSplit.domain
    val nameWithoutAlias = emailSplit.nameWithoutAlias

    val unsubCode = constructUnsubCode(
      emailsScheduledUuid = emailsScheduledUuid,
      teamId = teamId
    )

    s"<mailto:$nameWithoutAlias+${LIST_UNSUBSCRIBE_EMAIL_ALIAS_KEY}_${unsubCode}@$domain?subject=unsubscribe>"


  }

  /**
   * Verify whether incoming email (while reply tracking) is list-unsubscribe email
   */
  def checkIfSRListUnsubscribeEmail(toEmailAddresses: Seq[String]): Option[String] = {
    toEmailAddresses.find(em => {
      em.toLowerCase.contains(LIST_UNSUBSCRIBE_EMAIL_ALIAS_KEY.toLowerCase)
    })
  }

  def deconstructUnsubHeader(
                              unsubEmailAddress: String
                            ): Try[DecryptedUnsubLinkV2] = Try {

    // s"<mailto:$nameWithoutAlias+${LIST_UNSUBSCRIBE_EMAIL_ALIAS_KEY}_${prospectId}_${campaignId}_${stepId}_$emailScheduledId@$domain?subject=unsubscribe>"
    // example unsubEmailAddress: "<EMAIL>"

    // Remove mailto: and <> if present
    val cleanEmail = unsubEmailAddress.replaceAll("^<mailto:|>$", "")
    
    val srcode = s"${LIST_UNSUBSCRIBE_EMAIL_ALIAS_KEY}_"
    val srcodeIndex = cleanEmail.indexOf(srcode)
    if (srcodeIndex == -1) {
      throw new Exception("Invalid email format: missing unsubscribe alias")
    }

    val domainStartIndex = cleanEmail.lastIndexOf("@")
    if (domainStartIndex == -1) {
      throw new Exception("Invalid email format: missing domain")
    }

    val aliasPart = cleanEmail.substring(srcodeIndex + srcode.size, domainStartIndex)

    // Try new format first (with ___ delimiter)
    val newFormatFields = aliasPart.split(AppConfig.trackingLinksDelimiter, 2)
    if (newFormatFields.length == 2) {
      val teamId = newFormatFields(0).toLong
      val uuid = newFormatFields(1)

      DecryptedUnsubLinkV2(
        emailScheduledId = None,
        emailsScheduledUuidAndTeamId = if (uuid.isEmpty) None else Some((
          EmailsScheduledUuid(uuid = uuid),
          TeamId(id = teamId)
        ))
      )
    } else {
      // Handle old format (with _ delimiter)
      val fields = aliasPart.split("_")

      if (fields.length != 4) {
        throw new Exception(s"Invalid old format: wrong number of fields (${fields.length})")
      }

      val emailScheduledId = fields(3).toLong

      DecryptedUnsubLinkV2(
        emailScheduledId = Some(emailScheduledId),
        emailsScheduledUuidAndTeamId = None
      )
    }
  }


}
