package utils.email

import api.accounts.TeamId
import api.accounts.models.OrgId
import api.emails.EmailsScheduledUuid
import org.jsoup.Jsoup
import utils.SRLogger
//import scala.collection.convert.AsScalaExtensions.ListHasAsScala
import scala.jdk.CollectionConverters.CollectionHasAsScala


class EmailBodyService {

  def _getSignatureWithTrackingLinks(
                                      signature: String,
                                      emailsScheduledUuid: EmailsScheduledUuid,
                                      customDomain: Option[String],
                                      defaultTrackingDomain: String,
                                      allTrackingDomainsUsed: Seq[String],
                                      teamId: TeamId,
                                      orgId: OrgId
  )(using logger: SRLogger): String = {

    val doc = Jsoup.parse(signature)

    val links = doc.select("a[href]")

    for (link <- links.asScala) {
      val href = link.attr("href")
      val isSRLinkTry = EmailHelper.isSRTrackingOrUnsubscribeLink(href = href, allTrackingDomainsUsed = allTrackingDomainsUsed, orgId = orgId)
      val allowClickTracking = isSRLinkTry.isSuccess && !isSRLinkTry.get

      if (allowClickTracking) {
        link.attr("href",
          ClickTrackingUrlHelper.constructClickTrackingUrl(
            emailsScheduledUuid = emailsScheduledUuid,
            url = link.attr("href"),
            customDomain = customDomain,
            teamId = teamId,
            defaultTrackingDomain = defaultTrackingDomain
          )
        )
      }
    }

    doc.body().html()

    // doc.html()
  }

  // CUT PASTED from object EmailServiceTemp
  def _getBodyWithTrackingLinksV1(
                                   baseBody: String,
                                   emailsScheduledUuid: EmailsScheduledUuid,
                                   customDomain: Option[String],
                                   defaultTrackingDomain: String,
                                   allTrackingDomainsUsed: Seq[String],
                                   teamId: TeamId,
                                   orgId: OrgId
  )(using logger: SRLogger): String = {

    val doc = Jsoup.parse(baseBody)

    val links = doc.select("a[href]")

    for (link <- links.asScala) {
      val href = link.attr("href")
      val isSRLinkTry = EmailHelper.isSRTrackingOrUnsubscribeLink(href = href, allTrackingDomainsUsed = allTrackingDomainsUsed, orgId = orgId)
      val allowClickTracking = isSRLinkTry.isSuccess && !isSRLinkTry.get

      if (allowClickTracking) {
        link.attr("href",
          ClickTrackingUrlHelper.constructClickTrackingUrl(
            emailsScheduledUuid = emailsScheduledUuid,
            teamId = teamId,
            url = href,
            customDomain = customDomain,
            defaultTrackingDomain = defaultTrackingDomain
          )
        )
      }
    }

    doc.body().html()

    //doc.html()
  }

  // CUT PASTED from object EmailServiceTemp
  def _getBodyWithTrackingLinksV2(
                                   baseBody: String,
                                   emailsScheduledUuid: EmailsScheduledUuid,
                                   customDomain: Option[String],
                                   defaultTrackingDomain: String,
                                   allTrackingDomainsUsed: Seq[String],
                                   teamId: TeamId,
                                   orgId: OrgId
  )(using logger: SRLogger): String = {

    val doc = Jsoup.parse(baseBody)

    val links = doc.select("a[href]")

    for (link <- links.asScala) {
      val href = link.attr("href")
      val isSRLinkTry = EmailHelper.isSRTrackingOrUnsubscribeLink(href = href, allTrackingDomainsUsed = allTrackingDomainsUsed, orgId = orgId)
      val allowClickTracking = isSRLinkTry.isSuccess && !isSRLinkTry.get

      if (allowClickTracking) {
        link.attr("href",
          ClickTrackingUrlHelper.constructClickTrackingUrl(
            emailsScheduledUuid = emailsScheduledUuid,
            teamId = teamId,
            url = href,
            customDomain = customDomain,
            defaultTrackingDomain = defaultTrackingDomain
          )
        )
      }
    }

    //doc.body().html()

    doc.html()
  }

}
