package utils.email.services

import api.AppConfig
import api.accounts.{ReplyHandling, TeamId}
import api.accounts.ReplyHandling.ReplyHandling
import api.campaigns.dao.{InboxPlacementCheckDAO, TestEmailDetails}
import api.campaigns.services.CampaignId
import api.emails.dao_service.EmailScheduledDAOService
import api.emails.models.EmailMessageTrackedV4.{getCommonPropsEmailMessage, getPropsTrackedReply, updatePropsTrackedReply}
import api.emails.models.{EmailMessageTrackedV4, EmailReplyType, LandedFolderType, PropsTrackedReply}
import api.emails.{EmailScheduledDAO, EmailScheduledForCheckingReplies, EmailSetting, EmailSettingDAO, EmailThreadDAO, EmailThreadFoundForCheckingReplies}
import api.free_email_domain.service.FreeEmailDomainListService
import api.prospects.models.{ProspectCategory, ProspectId, StepId}
import api.prospects.service.ProspectEmailData
import io.smartreach.esp.api.emails.{EmailSettingId, IEmailAddress}
import io.smartreach.esp.utils.email.models.CommonPropsEmailMessage
import utils.SRLogger
import utils.email.{EmailHelper, EmailReplyStatus, UnSubscribeLinkHelper}
import utils.email.services.InternalTrackingNote.InternalTrackingNote
import utils.emailvalidation.EmailValidationService
import utils.helpers.LogHelpers
import utils.mq.trackingapp.{MQUnsubscribeTracker, MQUnsubscribeTrackerMessage}

import scala.collection.mutable.ListBuffer
import scala.concurrent.blocking
import scala.util.{Failure, Success, Try}
import scala.util.matching.Regex

case class ParsedMatchedData(
                              campaignId: Option[CampaignId],
                              stepId: Option[StepId],
                              prospectId: Option[ProspectId]
                            )

class ReplyFilteringService(
                             mqUnsubscribeTracker: MQUnsubscribeTracker,
                             emailScheduledDAOService: EmailScheduledDAOService,
                             freeEmailDomainListService: FreeEmailDomainListService,
                             inboxPlacementCheckDAO: InboxPlacementCheckDAO,
                             emailThreadDAO: EmailThreadDAO
                           ) {

  // copied from EmailReplyTrackingService.pushUnsubRequestToMQ to allow proper DI
  def pushUnsubRequestToMQ(
                            listUnsubscribeEmailAddress: String,
                            Logger: SRLogger
                          ) = {


    val unsubEmail: String = listUnsubscribeEmailAddress

    Logger.error(s"ReplyFilteringService READEMAIL isSRListUnsubscribeRequest email: $unsubEmail")


    UnSubscribeLinkHelper.deconstructUnsubHeader(unsubEmailAddress = unsubEmail.toLowerCase.trim) match {

      case Failure(e) =>

        Logger.fatal(s"[ReplyFilteringService] deconstructListUnsubscribeHeaderEmail: $unsubEmail", err = e)


      case Success(value) =>
        mqUnsubscribeTracker.publish(MQUnsubscribeTrackerMessage(
          email_scheduled_id = value.emailScheduledId.map(_.toString),
          emailsScheduledUuid = value.emailsScheduledUuidAndTeamId.map(_._1),
          teamId = value.emailsScheduledUuidAndTeamId.map(_._2),
          list_unsub = true,
          trace_reqid = None // used only via API calls
        )) match {

          case Failure(ex) =>
            Logger.fatal(s"[ReplyFilteringService] MQUnsubscribeTracker.publish: $value", err = ex)

          case Success(_) =>

            Logger.info(s"[ReplyFilteringService] success MQUnsubscribeTracker.publish: $value")

        }
    }
  }

  def checkIfInboxPlacementCheckEmail(
                                       inboxPlacementCheckLogsMsgIds: Seq[TestEmailDetails],
                                       message_id: String
                                     ): Option[TestEmailDetails] = {
    inboxPlacementCheckLogsMsgIds.find(_.message_id == message_id)
  }

  def checkMatchInReplyToHeader(inReplyToHeader: String,
                                patternNewSeq: Seq[Regex],
                                patternOld: Regex
                               ): Try[Option[(Regex.Match, ParsedMatchedData, InternalTrackingNote)]] = {

    val found: Option[Regex.Match] = EmailReplyTrackingService._findMatchForReplyHeader(

      patternNewSeq = patternNewSeq,
      patternOld = patternOld,


      headerStringToCheck = inReplyToHeader
    )

    //    found.map(v => {
    //      val res : ParsedMatchedData = parseDataInRegex(v).get
    //      //val (campaignId, stepId, prospectId)
    //      (v, res.campaignId, res.stepId, res.prospectId, InternalTrackingNote.NEW_MESSAGEIDREGEX_INREPLYTO_HEADER)
    //    })
    found match {
      case Some(v) =>
        val res: ParsedMatchedData = parseDataInRegex(v).get
        Success(Some(v, res, InternalTrackingNote.NEW_MESSAGEIDREGEX_INREPLYTO_HEADER))
      case None =>
        Success(None)

    }

  }


  def checkMatchInReferencesHeader(referencesHeader: String,
                                   patternNewSeq: Seq[Regex],
                                   patternOld: Regex
                                  ): Try[Option[(Regex.Match, ParsedMatchedData, InternalTrackingNote)]] = {

    val found = EmailReplyTrackingService._findMatchForReplyHeader(
      patternNewSeq = patternNewSeq,
      patternOld = patternOld,
      headerStringToCheck = referencesHeader
    )

    //    found.map(v => {
    //      val  res : ParsedMatchedData = parseDataInRegex(v)
    //      //val (campaignId, stepId, prospectId) = parseDataInRegex(v)
    //      (v, res.campaignId, res.stepId, res.prospectId, InternalTrackingNote.NEW_MESSAGEIDREGEX_REFERENCES_HEADER)
    //    })
    found match {
      case Some(v) =>
        val res: ParsedMatchedData = parseDataInRegex(v).get
        Success(Some(v, res, InternalTrackingNote.NEW_MESSAGEIDREGEX_REFERENCES_HEADER))
      case None =>
        Success(None)
    }

  }


  def checkInLastWeeksProspects(
                                 prospectEmailsPastWeek: Seq[EmailScheduledForCheckingReplies],
                                 commonPropsEmailMessage: CommonPropsEmailMessage
                               ): Option[(EmailScheduledForCheckingReplies, InternalTrackingNote)] = {

    val inReplyToHeader = commonPropsEmailMessage.in_reply_to_header
    val referencesHeader = commonPropsEmailMessage.references_header
    var internalTrackingNote = InternalTrackingNote.NONE
    val res = prospectEmailsPastWeek.find(p => {

      if (p.message_id.isEmpty
        || p.message_id.get.isEmpty

        // also ignore if reply was received_at before the given email was sent via campaign
        // only replies sent after a campaign is started for a prospect should be considered
        || (p.sent_at.isDefined && commonPropsEmailMessage.received_at.isBefore(p.sent_at.get))

      ) {

        false

      } else {

        var found = false
        if (inReplyToHeader.isDefined) {

          found = inReplyToHeader.get == p.message_id.get

          // to keep a record of whether the match was via the reply-to header
          if (found) {
            internalTrackingNote = InternalTrackingNote.NEW_LASTWEEK_MESSAGEID_INREPLYTO_HEADER
          }
        }

        if (!found && referencesHeader.isDefined) {

          found = referencesHeader.get.contains(p.message_id.get)

          if (found) {
            internalTrackingNote = InternalTrackingNote.NEW_LASTWEEK_MESSAGEID_REFERENCES_HEADER
          }
        }

        found

      }

    }).map(v => {
      (v, internalTrackingNote)
    })

    res

  }

  def checkProspectEmailedLastWeekViaToEmail(
                                              prospectEmailsPastWeek: Seq[EmailScheduledForCheckingReplies],
                                              commonPropsEmailMessage: CommonPropsEmailMessage
                                            ): Option[(EmailScheduledForCheckingReplies, InternalTrackingNote)] = {

    val from = commonPropsEmailMessage.from
    var internalTrackingNote = InternalTrackingNote.NONE
    prospectEmailsPastWeek.find(p => {
      if (
      // ignore if reply was received_at before the given email was sent via campaign
      // only replies sent after a campaign is started for a prospect should be considered
        p.sent_at.isDefined && commonPropsEmailMessage.received_at.isBefore(p.sent_at.get)) {

        false

      } else {

        // this to_email match is for scheduled_from_campaign emails only, so no need to check newinbox to_emails array
        // fixme newinbox we are not handling the case where admin is manually sending to multiple ppl and one of them is a prospect

        val from_email = from.email.toLowerCase.trim
        val found = p.to_email.toLowerCase.trim == from_email ||
          p.prospect_email.toLowerCase.trim == from_email

        if (found) {
          internalTrackingNote = InternalTrackingNote.NEW_LASTWEEK_TOEMAIL
        }

        found
      }
    }).map(v => {
      (v, internalTrackingNote)
    })

  }

  def checkForBounceInEmailBody(
                                 prospectEmailsPastWeek: Seq[EmailScheduledForCheckingReplies],
                                 commonPropsEmailMessage: CommonPropsEmailMessage
                               ): Option[(EmailScheduledForCheckingReplies, InternalTrackingNote)] = {
    val body = commonPropsEmailMessage.body
    var internalTrackingNote = InternalTrackingNote.NONE
    prospectEmailsPastWeek.find(p => {

      if (
      // ignore if reply was received_at before the given email was sent via campaign
      // only replies sent after a campaign is started for a prospect should be considered
      // give 5 minutes buffer time, sometimes the bounced email sent_at is slightly earlier than actual emails :P
        p.sent_at.isDefined && commonPropsEmailMessage.received_at.isBefore(p.sent_at.get.minusMinutes(5))) {

        false

      } else {

        val found = body.toLowerCase.contains(p.to_email.toLowerCase.trim)

        if (found) {
          internalTrackingNote = InternalTrackingNote.NEW_LASTWEEK_DELIVERYFAILED
        }
        found
      }

    }).map(v => {
      (v, internalTrackingNote)
    })

  }

  def verifyAndCheckViaDomainMatch(
                                    replyHandling: ReplyHandling,
                                    prospectEmailsPastWeek: Seq[EmailScheduledForCheckingReplies],
                                    commonPropsEmailMessage: CommonPropsEmailMessage,
                                    propsTrackedReply: PropsTrackedReply,
                                    Logger: SRLogger
                                  ): Try[Option[(EmailScheduledForCheckingReplies, InternalTrackingNote)]] = {

    replyHandling match {
      case ReplyHandling.PAUSE_ALL_PROSPECT_ACCOUNT_CAMPAIGNS_ON_REPLY =>
        checkViaDomainMatch(
          prospectEmailsPastWeek = prospectEmailsPastWeek,
          commonPropsEmailMessage = commonPropsEmailMessage,
          propsTrackedReply = propsTrackedReply,
          Logger = Logger
        )

      case ReplyHandling.PAUSE_ALL_PROSPECT_CAMPAIGNS_ON_REPLY | ReplyHandling.PAUSE_SPECIFIC_CAMPAIGN_ON_REPLY =>
        Success(None)

    }
  }

  def checkViaDomainMatch(
                           prospectEmailsPastWeek: Seq[EmailScheduledForCheckingReplies],
                           commonPropsEmailMessage: CommonPropsEmailMessage,
                           propsTrackedReply: PropsTrackedReply,
                           Logger: SRLogger): Try[Option[(EmailScheduledForCheckingReplies, InternalTrackingNote)]] =
    Try {

      given srLogger: SRLogger = Logger // fixme given

      var internalTrackingNote = InternalTrackingNote.NONE
      prospectEmailsPastWeek.find(p => {
        if (p.prospect_account_id.isEmpty ||
          // ignore if reply was received_at before the given email was sent via campaign
          // only replies sent after a campaign is started for a prospect should be considered
          p.sent_at.isDefined && commonPropsEmailMessage.received_at.isBefore(p.sent_at.get)) {

          (false)

        } else {

          if (propsTrackedReply.prospect_account_id_in_campaign.nonEmpty) {
            Logger.fatal("This case should never occur as match should have happened earlier")
          }
          // todo nxd . i want it to take this path and we currently are.
          val prospectAccountIdMatching = propsTrackedReply.prospect_account_id_in_campaign.isDefined &&
            p.prospect_account_id.isDefined &&
            // todo nxd: but now i want this condition to match:
            //   p.prospect_account_id.get == baseEmail.prospect_account_id_in_campaign.get
            p.prospect_account_id.get == propsTrackedReply.prospect_account_id_in_campaign.get

          // todo nxd: but baseEmail comes from newMsgsWithEmailThreadId which is Seq(EmailMessageTracked)
          //   which gets updated in __assignEmailThreadIdsForExistingEmailThreads


          val (_, currentSenderDomain) = EmailValidationService.getLowercasedNameAndDomainFromEmail(email = commonPropsEmailMessage.from.email)
          val (_, previouslySentProspectDomain) = EmailValidationService.getLowercasedNameAndDomainFromEmail(email = p.to_email)

          val isFreeEmailDomain = freeEmailDomainListService.isFreeEmailDomain(domain = currentSenderDomain)(Logger).get

          val prospectDomainMatching = !isFreeEmailDomain &&
            currentSenderDomain.trim.toLowerCase == previouslySentProspectDomain.trim.toLowerCase

          if (prospectAccountIdMatching) {

            internalTrackingNote = InternalTrackingNote.NEW_LASTWEEK_ACCOUNT

          } else if (prospectDomainMatching) {

            internalTrackingNote = InternalTrackingNote.NEW_LASTWEEK_DOMAIN

          }

          prospectAccountIdMatching || prospectDomainMatching

        }

      }).map(v => {
        (v, internalTrackingNote)
      })
    }


  sealed trait ReplyDetectedVia;

  object ReplyDetectedVia {
    case object EmailThreadFound extends ReplyDetectedVia

    case object CheckAgainstPatternInReplyToHeader extends ReplyDetectedVia

    case object CheckAgainstPatternReferencesHeader extends ReplyDetectedVia

    case object CheckAgainstMessageIdsSentLastWeek extends ReplyDetectedVia

    case object CheckAgainstToEmailsSentLastWeek extends ReplyDetectedVia

    case object CheckForDeliveryFailedInEmailBody extends ReplyDetectedVia

    case object CheckForReplyFromProspectAccountDomain extends ReplyDetectedVia

    case object CheckForInboxPlacementCheckTestEmailMessageIdMatch extends ReplyDetectedVia

    case object CheckForFromEmailInTeamEmails extends ReplyDetectedVia
  }

  sealed trait ReplyDetectedInfo {
    def internalTrackingNote: InternalTrackingNote

    def replyDetectedVia: ReplyDetectedVia
  }

  object ReplyDetectedInfo {

    case class ShouldBeCCEmail(
                                emailReplyStatus: EmailReplyStatus
                              ) extends ReplyDetectedInfo {
      final val internalTrackingNote = InternalTrackingNote.SHOULD_BE_CC_EMAIL
      final val replyDetectedVia = ReplyDetectedVia.CheckForFromEmailInTeamEmails
    }

    case class ShouldBeBccEmail(
                                 emailReplyStatus: EmailReplyStatus
                               ) extends ReplyDetectedInfo {
      final val internalTrackingNote = InternalTrackingNote.SHOULD_BE_BCC_EMAIL
      final val replyDetectedVia = ReplyDetectedVia.CheckForFromEmailInTeamEmails
    }

    case class ReplyDetectedViaThreadExistingInDB(
                                                   email_thread_id: Long,
                                                   internalTrackingNote: InternalTrackingNote
                                                 ) extends ReplyDetectedInfo {
      final val replyDetectedVia = ReplyDetectedVia.EmailThreadFound
    }

    case class ReplyDetectedViaReplyToHeaderInfo(

                                                  //messageIdRegexMatch: Regex.Match,
                                                  campaignId: Option[CampaignId],
                                                  stepId: Option[StepId],
                                                  prospectId: Option[ProspectId]

                                                ) extends ReplyDetectedInfo {
      final val internalTrackingNote = InternalTrackingNote.NEW_MESSAGEIDREGEX_INREPLYTO_HEADER
      final val replyDetectedVia = ReplyDetectedVia.CheckAgainstPatternInReplyToHeader
    }

    case class ReplyDetectedViaReferencesHeaderInfo(

                                                     //messageIdRegexMatch: Regex.Match,
                                                     campaignId: Option[CampaignId],
                                                     stepId: Option[StepId],
                                                     prospectId: Option[ProspectId]

                                                   ) extends ReplyDetectedInfo {
      final val internalTrackingNote = InternalTrackingNote.NEW_MESSAGEIDREGEX_REFERENCES_HEADER
      final val replyDetectedVia = ReplyDetectedVia.CheckAgainstPatternReferencesHeader
    }

    case class ReplyDetectedViaMessageIdsSentLastWeek(

                                                       emailScheduledForCheckingReplies: EmailScheduledForCheckingReplies,
                                                       internalTrackingNote: InternalTrackingNote

                                                     ) extends ReplyDetectedInfo {
      final val replyDetectedVia = ReplyDetectedVia.CheckAgainstMessageIdsSentLastWeek

    }

    case class ReplyDetectedViaToEmailsSentLastWeek(

                                                     emailScheduledForCheckingReplies: EmailScheduledForCheckingReplies

                                                   ) extends ReplyDetectedInfo {
      final val internalTrackingNote = InternalTrackingNote.NEW_LASTWEEK_TOEMAIL
      final val replyDetectedVia = ReplyDetectedVia.CheckAgainstToEmailsSentLastWeek
    }


    case class ReplyDetectedViaDeliveryFailedInEmailBody(

                                                          emailScheduledForCheckingReplies: EmailScheduledForCheckingReplies
                                                        ) extends ReplyDetectedInfo {
      final val internalTrackingNote = InternalTrackingNote.NEW_LASTWEEK_DELIVERYFAILED
      final val replyDetectedVia = ReplyDetectedVia.CheckForDeliveryFailedInEmailBody
    }

    case class ReplyDetectedViaProspectAccountDomain(

                                                      emailScheduledForCheckingReplies: EmailScheduledForCheckingReplies,
                                                      internalTrackingNote: InternalTrackingNote
                                                    ) extends ReplyDetectedInfo {
      final val replyDetectedVia = ReplyDetectedVia.CheckForReplyFromProspectAccountDomain

    }

    case class InboxPlacementCheckTestEmail() extends ReplyDetectedInfo {
      final val internalTrackingNote: InternalTrackingNote = InternalTrackingNote.INBOX_PLACEMENT_CHECK_TEST_EMAIL
      final val replyDetectedVia: ReplyDetectedVia = ReplyDetectedVia.CheckForInboxPlacementCheckTestEmailMessageIdMatch
    }

  }


  def parseDataInRegex(messageIdRegexMatch: Regex.Match): Try[ParsedMatchedData] = Try {


    val campaignIdFromPattern = messageIdRegexMatch.group(1).toLong
    val campaignId: Option[CampaignId] =
      if (campaignIdFromPattern == 0) None
      else Some(CampaignId(id = campaignIdFromPattern))


    val stepIdFromPattern = messageIdRegexMatch.group(2).toLong
    val stepId: Option[StepId] =
      if (stepIdFromPattern == 0) None
      else Some(StepId(id = stepIdFromPattern))

    val prospectIdFromPattern = messageIdRegexMatch.group(3).toLong
    val prospectId: Option[ProspectId] =
      if (prospectIdFromPattern == 0) None
      else Some(ProspectId(id = prospectIdFromPattern))

    ParsedMatchedData(
      campaignId = campaignId,
      stepId = stepId,
      prospectId = prospectId
    )

  }


  //  def convertTupleIDEmailReplyToEmailMessageTrackedV4(
  //          msg: (String,  EmailReply),
  //          senderEmails: Seq[String],
  //          inboxEmailSettingId: Int,
  //          teamId: Long,
  //          accountId: Int,
  //          foundInInboxFolder: Option[String],
  //          Logger: SRLogger
  //  ): Option[EmailMessageTrackedV4] =     {
  //
  //      val messageTrackedV4: Option[EmailMessageTrackedV4] = msg match {
  //        case (msg_id: String, apiTrackedReply: GmailReplyTrackedViaAPI) => {
  //          //val apiTrackedReply = apiTrackedReply.apiMsg
  //
  //          val commonPropsEmailMessage: CommonPropsEmailMessage = apiTrackedReply.commonPropsEmailMessage
  //          val byAccount = senderEmails.contains(Helpers.trimToLower(commonPropsEmailMessage.from
  //                  .email))
  //
  //          val emailStatus: EmailReplyStatus = EmailReplyStatus
  //                  .getEmailReplyStatusBeforeSaving(
  //                    emailBaseBody = commonPropsEmailMessage.base_body,
  //                    subject = commonPropsEmailMessage.subject,
  //                    fromEmail = commonPropsEmailMessage.from.email,
  //                    fromName = commonPropsEmailMessage.from.name.getOrElse(""),
  //                    toEmailAddresses = commonPropsEmailMessage.to_emails.map(_.email),
  //                    byAccount = byAccount,
  //                    fullHeaders = commonPropsEmailMessage.full_headers
  //                  )
  //
  //          val em = EmailMessageTrackedV4.GmailMessageTrackedV4(
  //            gmailReplyTrackedViaAPI = apiTrackedReply,
  //            propsTrackedReply = PropsTrackedReply(
  //              // =========
  //              inbox_email_setting_id = inboxEmailSettingId,
  //              email_status = emailStatus,
  //              sr_inbox_read = byAccount,
  //              scheduled_manually = byAccount,
  //
  //              campaign_id = None,
  //              step_id = None,
  //              prospect_id_in_campaign = None,
  //
  //              // NOTE: this will be populated after checking validProspects
  //              // inside EmailScheduled
  //              // .saveRepliesDeleteOtherScheduledEmailsAndUpdateRepliedStatusV2
  //              prospect_account_id_in_campaign = None,
  //              email_thread_id = None,
  //
  //              // will be assigned next
  //              tempThreadId = None,
  //              //                  original_inbox_folder = foundInInboxFolder,
  //
  //              internal_tracking_note = InternalTrackingNote.NONE,
  //
  //              team_id = teamId,
  //              account_id = accountId,
  //
  //              // these are set only in the save SQL function in EmailScheduled.scala
  //              campaign_name = None,
  //              step_name = None
  //            )
  //          )
  //
  //          Some(em)
  //
  //        }
  //
  //        case (msg_id: String, apiTrackedReply: OutlookReplyTrackedViaAPI) => {
  //          //val apiTrackedReply = apiTrackedReply.apiMsg
  //
  //          val commonPropsEmailMessage: CommonPropsEmailMessage = apiTrackedReply.commonPropsEmailMessage
  //          val byAccount = senderEmails.contains(commonPropsEmailMessage.from.email.trim.toLowerCase)
  //
  //          val emailStatus: EmailReplyStatus = EmailReplyStatus
  //                  .getEmailReplyStatusBeforeSaving(
  //                    emailBaseBody = commonPropsEmailMessage.base_body,
  //                    subject = commonPropsEmailMessage.subject,
  //                    fromEmail = commonPropsEmailMessage.from.email,
  //                    fromName = commonPropsEmailMessage.from.name.getOrElse(""),
  //                    toEmailAddresses = commonPropsEmailMessage.to_emails.map(_.email),
  //                    byAccount = byAccount,
  //                    fullHeaders = commonPropsEmailMessage.full_headers
  //                  )
  //
  //          val em = EmailMessageTrackedV4.OutlookMessageTrackedV4(
  //
  //            outlookReplyTrackedViaAPI = apiTrackedReply,
  //
  //
  //            // =================
  //            propsTrackedReply = PropsTrackedReply(
  //              inbox_email_setting_id = inboxEmailSettingId,
  //              email_status = emailStatus,
  //
  //
  //              sr_inbox_read = byAccount,
  //              scheduled_manually = byAccount,
  //
  //              campaign_id = None,
  //              step_id = None,
  //              prospect_id_in_campaign = None,
  //
  //              // NOTE: this will be populated after checking validProspects
  //              // inside EmailScheduled
  //              // .saveRepliesDeleteOtherScheduledEmailsAndUpdateRepliedStatusV2
  //              prospect_account_id_in_campaign = None,
  //              email_thread_id = None,
  //
  //              // will be assigned next
  //              tempThreadId = None,
  //              //                  original_inbox_folder = foundInInboxFolder,
  //
  //              internal_tracking_note = InternalTrackingNote.NONE,
  //
  //              team_id = teamId,
  //              account_id = accountId,
  //
  //              // these are set only in the save SQL function in EmailScheduled.scala
  //              campaign_name = None,
  //              step_name = None
  //            )
  //          )
  //
  //          Some(em)
  //
  //        }
  //
  //        case (msg_id: String, imapTrackedReply: ImapTrackedReply) =>
  //          EmailReplyTrackingService.__getNewImapMsgsWithHeadersV4(
  //            messageObj = (msg_id, imapTrackedReply),
  //            inboxEmailSettingId = inboxEmailSettingId,
  //            senderEmails = senderEmails,
  //            foundInInboxFolder = foundInInboxFolder,
  //            teamId = teamId,
  //            accountId = accountId,
  //            Logger = Logger
  //          )
  //
  //      }
  //      messageTrackedV4
  //    }

  // E. check existing email threads, assign email_thread_id to messages for existing threads in db
  def checkForRepliesInEmailThreadsTable(
                                          newMsgsWithHeadersAndTempThreadId: ListBuffer[EmailMessageTrackedV4],
                                          inboxEmailSettingId: Int,
                                          teamId: TeamId,
                                          inboxEmailSetting: EmailSetting,
  )(
    using Logger: SRLogger
  ): Try[ListBuffer[EmailMessageTrackedV4]] = Try {


    /*
    val replyToHeaders: Seq[String] = newMsgsWithHeadersAndTempThreadId
      .filter(_.in_reply_to_header.isDefined)
      .map(_.in_reply_to_header.get)

     */


    val replyToHeaders: Seq[String] =
      EmailMessageTrackedV4.getReplyToHeaders(
          trackedEmails = newMsgsWithHeadersAndTempThreadId)
        .toSeq

    /*
    val gmailThreadIds: Seq[String] = newMsgsWithHeadersAndTempThreadId
      .filter(_.gmail_thread_id.isDefined)
      .map(_.gmail_thread_id.get)
     */

    val gmailThreadIds: Seq[String] =
      EmailMessageTrackedV4.getGmailThreadIds(
        trackedEmails = newMsgsWithHeadersAndTempThreadId)

    /*
    val outlookConversationIds: Seq[String] = newMsgsWithHeadersAndTempThreadId
      .filter(_.outlook_conversation_id.isDefined)
      .map(_.outlook_conversation_id.get)
    */

    val outlookConversationIds: Seq[String] =
      EmailMessageTrackedV4.getOutlookConversationIds(
        trackedEmails = newMsgsWithHeadersAndTempThreadId
      )

    //ReplyTracking_ReceiveEmail_DAO_READ
    // check emails_scheduled table for thread ids
    val existingEmailThreadsFromEmailsScheduledTable: Seq[EmailThreadFoundForCheckingReplies] = emailScheduledDAOService.findEmailThreadIdsWhileReplyTracking(
      replyToHeaders = replyToHeaders,
      gmailThreadIds = gmailThreadIds,
      outlookConversationIds = outlookConversationIds,
      teamId = teamId,
      inboxEmailSettingId = inboxEmailSettingId
    ).get

    //ReplyTracking_ReceiveEmail_DAO_READ
    // check email_threads table for Gmail thread ids
    val existingGmailAndOutlookThreads: Seq[EmailThreadFoundForCheckingReplies] = emailThreadDAO.findGmailAndOutlookThreads(
      gmailThreadIds = gmailThreadIds,
      outlookConversationIds = outlookConversationIds,
      teamId = teamId,
      inboxEmailSettingId = inboxEmailSettingId
    ).get

    EmailReplyTrackingService.__assignEmailThreadIdsForExistingEmailThreadsV4(
        newMsgsWithHeadersAndTempThreadId = newMsgsWithHeadersAndTempThreadId.toSeq,
        existingEmailThreads = existingEmailThreadsFromEmailsScheduledTable,
        existingGmailAndOutlookThreads = existingGmailAndOutlookThreads,
        Logger = Logger
      )
      //                  .map(msg => getCommonPropsEmailMessage(msg).from.email)
      .filter(msg => {
        val email = getCommonPropsEmailMessage(msg).from.email
        email != null &&
          email.trim.nonEmpty &&
          email.contains("@")
      })


  }

  //  def excludeExistingMessagesInEmailsScheduled(
  //          msgsWithMessageIdHeader: Seq[(String, EmailReply)],
  //          inboxEmailSettingId: Int): Try[Seq[(String, EmailReply)]] = {
  //
  //    val allmessageIdHeaders: Seq[String] = msgsWithMessageIdHeader.map(_._1)
  //
  //    // DONE: why not check against receiver_email_setting_id instead of team_id ?
  //    // note: this data comes from emails_scheduled
  //    emailScheduledDAO.checkIfEmailIsAlreadySaved(
  //      messageIds = allmessageIdHeaders,
  //      inboxEmailSettingId = inboxEmailSettingId
  //    ).map(alreadySavedMessageIds =>
  //      msgsWithMessageIdHeader.filter(m => {
  //        // ignore msgs which are already saved
  //        !alreadySavedMessageIds.contains(m._1)
  //      })
  //    )
  //  }


  def updateLandedFolderForInboxPlacementCheckLogs(
                                                    testEmailDetails: TestEmailDetails,
                                                    originalInboxFolder: Option[String]
                                                  )(using logger: SRLogger): Unit = {
    if (originalInboxFolder.isDefined) {
      val landedFolderType: LandedFolderType = LandedFolderType.getLandedFolderTypeFromOriginalFolder(
        folder = originalInboxFolder.get
      )
      inboxPlacementCheckDAO.updateLandedFolderForReceiver(
        inpb_log_id = testEmailDetails.inbp_log_id,
        landedFolderType = landedFolderType,
        originalLandedFolder = originalInboxFolder.get
      ) match {
        case Failure(_) => logger.fatal(s"updateLandedFolderForReceiver failed")
        case Success(_) => //do nothing
      }
    }
  }

  def getInboxPlacementTestEmailDetails(
                                         emailSettingId: EmailSettingId
                                       ): Try[List[TestEmailDetails]] = {
    if (AppConfig.RollingUpdates.isReceiverEmailSettingIdForInboxPlacementCheck(emailSettingId.emailSettingId)) {
      inboxPlacementCheckDAO.getSentEmailDetails(
        receiver_email_setting_id = emailSettingId
      )
    } else {
      Success(List())
    }
  }

  /**
   *
   * @param ccEmails   -> it is cc emails in emailMessage object
   * @param inboxEmail -> it the email setting email for which we are reading the emailMessages
   *
   * @return true -> means inbox email setting email is in cc
   *         false -> means inbox email setting email is not in cc
   */
  def checkIfCCEmail(
                      inboxEmail: String,
                      ccEmails: Seq[IEmailAddress]
                    ): Boolean = {
    ccEmails.map(_.email).contains(inboxEmail)
  }

  /**
   *
   * @param fromEmail  -> it is the from email in emailMessage object
   * @param toEmails   -> it is to emails in emailMessage object
   * @param ccEmails   -> it is cc emails in emailMessage object
   * @param inboxEmail -> it the email setting email for which we are reading the emailMessages
   * @return true -> means inbox email setting email is in bcc
   *         false -> means inbox email setting email is either from email or to email or cc email of the emailMessage
   */

  def checkIfInboxEmailIsInBcc(
                                fromEmail: String,
                                toEmails: Seq[IEmailAddress],
                                ccEmails: Seq[IEmailAddress],
                                inboxEmail: String,
                              ): Boolean = {
    !(inboxEmail == fromEmail || toEmails.map(_.email).contains(inboxEmail) || ccEmails.map(_.email).contains(inboxEmail))
  }


  // TODO Review with Prateek and Sathish - removed final here
  def filterRepliesV4(
                       //    repliesTrackedViaApi: Boolean,
                       //messages: Vector[ReplyMessageObject],
                       //messages: Vector[EmailReply],
                       newMsgsWithHeaders: Seq[EmailMessageTrackedV4],
                       inboxEmailSetting: EmailSetting,
                       senderEmails: Seq[String],
                       senderMessageIdSufixes: Seq[String],
                       prospectEmailsPastWeek: Seq[EmailScheduledForCheckingReplies],
                       //foundInInboxFolder: Option[String],
                       Logger: SRLogger
                     ): Try[Vector[EmailMessageTrackedV4]] = blocking {
    Try {
      
      given srLogger: SRLogger = Logger // fixme given

      // Logger.info(s"\n\n------------\nfolder: $foundInInboxFolder: ${messages.length}\n----------------\n\n")

      if (newMsgsWithHeaders.isEmpty) {
        Vector()
      } else {

        val inboxEmailSettingId = inboxEmailSetting.id.get.emailSettingId // FIXME VALUECLASS
        // val accountId = inboxEmailSetting.owner_id // ********: commented as it doesn't used anywhere

        //val TRACKED_VIA_API = repliesTrackedViaApi


        // A. fetch message id headers
        /*
        val msgsWithMessageIdHeader: Seq[(String, EmailReply)] =
        messages.map(msg => {
          val (msg_id, emailReply): (Option[String], EmailReply) =
            EmailReply.extractMessageId(msg = msg, logger = Logger)


          (msg_id, emailReply)
        })
                // throw out anything that can fail get
                .filter(_._1.isDefined)
                .map(m => (m._1.get, m._2))



        // B. check if emails already saved, and then ignore msgs that are already there in DB
        val newMsgs: Seq[(String, EmailReply)] = excludeExistingMessagesInEmailsScheduled(
          msgsWithMessageIdHeader = msgsWithMessageIdHeader,
          inboxEmailSettingId = inboxEmailSettingId)
                .get



        // C. get baseEmails (EmailReplyTracked) structure
        val newMsgsWithHeaders: Seq[EmailMessageTrackedV4] = newMsgs.map( msg =>
          convertTupleIDEmailReplyToEmailMessageTrackedV4(
            msg = msg,
            senderEmails = senderEmails,
            inboxEmailSettingId = inboxEmailSettingId,
            teamId = teamId,
            accountId = accountId,
            foundInInboxFolder = foundInInboxFolder,
            Logger = Logger
          )
                )
          .filter(_.isDefined)
          .map(_.get)

         */


        // D. assign temporary email thread ids
        val newMsgsWithHeadersAndTempThreadId: ListBuffer[EmailMessageTrackedV4] = EmailReplyTrackingService.__assignTempThreadIdsForTrackedRepliesV4(
          allMsgsWithHeaders = newMsgsWithHeaders,
          Logger = Logger
        )



        // E. check existing email threads, assign email_thread_id to messages for existing threads in db

        // assign emailThreadId for existingEmailThreads
        // new emails wont have emailThreadIds after this step, for them we will create new email threads in
        // DB and assign those emailThreadIds later

        val newMsgsWithEmailThreadId: ListBuffer[EmailMessageTrackedV4] =
          checkForRepliesInEmailThreadsTable(
            newMsgsWithHeadersAndTempThreadId = newMsgsWithHeadersAndTempThreadId,
            inboxEmailSettingId = inboxEmailSettingId.toInt,
            teamId = inboxEmailSetting.team_id,
            inboxEmailSetting = inboxEmailSetting
          ).get

        //For InboxPlacementCheckLogsReport -
        val receivedTestEmailDetails: List[TestEmailDetails] = getInboxPlacementTestEmailDetails(
          emailSettingId = inboxEmailSetting.id.get
        ) match {
          case Failure(e) =>
            Logger.shouldNeverHappen(s"getInboxPlacementTestEmailDetails failed ${LogHelpers.getStackTraceAsString(e)}")
            List()
          case Success(list) => list
        }


        // F. track replies via all schemes
        //  1. check if email thread id already found
        //  2. check against patterns
        //  3. check against message ids sent last week
        //  4. check against to-emails sent last week
        //  5. if no prospect_id and email is categorized as delivery_failed, check body if its got email of any of the prospects sent last week
        //  6. check against prospect_account_id and domain
        //  7. check if inbox placement check logs table is having the message id
        //  8. checkIfFromEmailIsOfProspectOnly can bcc or cc

        val totalnewMsgsWithEmailThreadId = newMsgsWithEmailThreadId.size

        for {i <- 0 until totalnewMsgsWithEmailThreadId} {
          // newMsgsWithHeadersAndTempThreadId.foreach(baseEmail => {

          val baseEmail: EmailMessageTrackedV4 = newMsgsWithEmailThreadId(i)

          val commonPropsEmailMessage = getCommonPropsEmailMessage(baseEmail)
          val propsTrackedReply = getPropsTrackedReply(baseEmail)

          //Logger.info(s"\n\n fromEmail: ${message.getFrom()} \n\n")


          //val from = commonPropsEmailMessage.from
          // val subject = baseEmail.subject
          //val body = commonPropsEmailMessage.body
          //val replyType = propsTrackedReply.email_status.replyType


          // todo remove later : nxd i want all matches through this path
          //               EmailThreadFound    : which is effectively EmailReplyTrackingService.__assignEmailThreadIdsForExistingEmailThreads
          //                   : that function setting the email_thread_id

          //          var replyDetected: Option[ReplyDetectedVia] = None
          var replyDetectedInfo: Option[ReplyDetectedInfo] = None
          

          // 1. check if email thread id already found
          if (propsTrackedReply.email_thread_id.isDefined) {
            //            replyDetected = Some( ReplyDetectedVia.EmailThreadFound)
            replyDetectedInfo = Some(
              ReplyDetectedInfo.ReplyDetectedViaThreadExistingInDB(propsTrackedReply.email_thread_id.get,
                internalTrackingNote = propsTrackedReply.internal_tracking_note))
          }

          // 2. check against patterns
          var messageIdRegexMatch: Option[Regex.Match] = None


          if (replyDetectedInfo.isEmpty) {

            // message id pattern for identifying replies
            val patternOld = EmailHelper.getMessageIdRegExPatternOld

            val patternNewSeq = senderMessageIdSufixes.map(s => EmailHelper.getMessageIdRegExPattern(s))

            val inReplyToHeader = commonPropsEmailMessage.in_reply_to_header
            if (inReplyToHeader.isDefined) {

              checkMatchInReplyToHeader(
                inReplyToHeader = inReplyToHeader.get,
                patternNewSeq = patternNewSeq,
                patternOld = patternOld
              ).get.foreach {
                case (messageIdRegex, parsedMatchedData, itn) =>
                  messageIdRegexMatch = Some(messageIdRegex)


                  replyDetectedInfo = Some(ReplyDetectedInfo.ReplyDetectedViaReplyToHeaderInfo(

                    //messageIdRegexMatch = v1,
                    campaignId = parsedMatchedData.campaignId,
                    stepId = parsedMatchedData.stepId,
                    prospectId = parsedMatchedData.prospectId

                  ))

              }
            }

            val referencesHeader = commonPropsEmailMessage.references_header
            if ( /*messageIdRegexMatch.isEmpty && referencesHeader.isDefined && */
              replyDetectedInfo.isEmpty && referencesHeader.isDefined) {


              checkMatchInReferencesHeader(
                referencesHeader = referencesHeader.get,
                patternNewSeq = patternNewSeq,
                patternOld = patternOld
              ).get.foreach {

                case (messageIdRegex, parsedMatchedData, itn) =>
                  messageIdRegexMatch = Some(messageIdRegex)

                  //                  replyDetected = Some(ReplyDetectedVia.CheckAgainstPatternReferencesHeader)
                  replyDetectedInfo = Some(ReplyDetectedInfo.ReplyDetectedViaReferencesHeaderInfo(

                    //messageIdRegexMatch = v1,
                    campaignId = parsedMatchedData.campaignId,
                    stepId = parsedMatchedData.stepId,
                    prospectId = parsedMatchedData.prospectId

                  ))

              }

            }

          }


          // 3. check with message-id
          val prospectWasEmailedLastWeekViaMessageIDMatch: Option[EmailScheduledForCheckingReplies] =
            if (
            //isEmailThreadMatch || messageIdRegexMatch.isDefined
              replyDetectedInfo.isDefined
            ) None
            else {
              checkInLastWeeksProspects(
                prospectEmailsPastWeek = prospectEmailsPastWeek,
                commonPropsEmailMessage = commonPropsEmailMessage
              ).map {
                case (email, itn) =>
                  //                  replyDetected = Some(ReplyDetectedVia.CheckAgainstMessageIdsSentLastWeek)

                  replyDetectedInfo = Some(ReplyDetectedInfo.ReplyDetectedViaMessageIdsSentLastWeek(

                    emailScheduledForCheckingReplies = email,
                    internalTrackingNote = itn
                  ))

                  email
              }
            }


          // 4. check if to_email matches
          // MULTICAMPAIGN DONE
          val prospectWasEmailedLastWeekViaToEmailMatch =
            if (
            //isEmailThreadMatch ||
            //        messageIdRegexMatch.isDefined ||
            //        prospectWasEmailedLastWeekViaMessageIDMatch.isDefined
              replyDetectedInfo.isDefined
            ) None
            else {
              checkProspectEmailedLastWeekViaToEmail(
                prospectEmailsPastWeek = prospectEmailsPastWeek,
                commonPropsEmailMessage = commonPropsEmailMessage
              ).map {
                case (email, itn) =>
                  //                replyDetected = Some(ReplyDetectedVia.CheckAgainstToEmailsSentLastWeek)

                  replyDetectedInfo = Some(ReplyDetectedInfo.ReplyDetectedViaToEmailsSentLastWeek(

                    emailScheduledForCheckingReplies = email
                  ))

                  email

              }
            }

          // 5. if no prospect_id and email is categorized as delivery_failed, check body if its got email of any of the prospects sent last week
          // happens when the bounce message comes in a different thread like in Rediffmail etc
          val prospectDeliveryFailedCheckBody = if (
          //isEmailThreadMatch ||
          //  messageIdRegexMatch.isDefined ||
          //  prospectWasEmailedLastWeekViaMessageIDMatch.isDefined ||
          //  prospectWasEmailedLastWeekViaToEmailMatch.isDefined ||
            replyDetectedInfo.isDefined ||
              propsTrackedReply.prospect_id_in_campaign.isDefined ||
              propsTrackedReply.email_status.replyType != EmailReplyType.DELIVERY_FAILED

          ) None else {

            checkForBounceInEmailBody(
              prospectEmailsPastWeek = prospectEmailsPastWeek,
              commonPropsEmailMessage = commonPropsEmailMessage
            ).map {
              case (email, itn) =>

                //                replyDetected = Some(ReplyDetectedVia.CheckForDeliveryFailedInEmailBody)
                replyDetectedInfo = Some(ReplyDetectedInfo.ReplyDetectedViaDeliveryFailedInEmailBody(

                  emailScheduledForCheckingReplies = email
                ))


                email
            }
          }


          // 6. check if prospect_account_id and domain matches
          // MULTICAMPAIGN DONE

          // CHECKME: replyhandling check, should prospect_id be associated

          val prospectWasEmailedLastWeekViaToProspectAccountIdAndDomainMatch = if (
          //isEmailThreadMatch ||
          //  messageIdRegexMatch.isDefined ||
          //  prospectWasEmailedLastWeekViaMessageIDMatch.isDefined ||
          //  prospectWasEmailedLastWeekViaToEmailMatch.isDefined ||
          //  prospectDeliveryFailedCheckBody.isDefined
            replyDetectedInfo.isDefined
          ) None

          else {
            verifyAndCheckViaDomainMatch(
              replyHandling = inboxEmailSetting.reply_handling,
              prospectEmailsPastWeek = prospectEmailsPastWeek,
              commonPropsEmailMessage = commonPropsEmailMessage,
              propsTrackedReply = propsTrackedReply,
              Logger = Logger
            ).get.map {
              case (email, itn) =>
                //replyDetected = Some(ReplyDetectedVia.CheckForReplyFromProspectAccountDomain)

                replyDetectedInfo = Some(ReplyDetectedInfo.ReplyDetectedViaProspectAccountDomain(

                  emailScheduledForCheckingReplies = email,
                  internalTrackingNote = itn
                ))


                email

            }
          }

          // 7. check if inbox placement check logs table is having the message id


          val inboxPlacementCheckEmail: Unit = if (AppConfig.RollingUpdates.isReceiverEmailSettingIdForInboxPlacementCheck(inboxEmailSetting.id.get.emailSettingId)) {
            checkIfInboxPlacementCheckEmail(
              inboxPlacementCheckLogsMsgIds = receivedTestEmailDetails,
              message_id = commonPropsEmailMessage.message_id
            ).map(res => {
              updateLandedFolderForInboxPlacementCheckLogs(
                testEmailDetails = res,
                originalInboxFolder = commonPropsEmailMessage.original_inbox_folder
              )
            }) match {
              case Some(_) => replyDetectedInfo = Some(ReplyDetectedInfo.InboxPlacementCheckTestEmail())
              case None => //do nothing
            }

          }

          /**
           * 1-Mar-2025
           * So - to reach this point we have tried to detect replies in different ways and not detected any
           *
           * This is a last effort if all other known ways have failed
           * and this should always be last - after trying everything else
           *
           * All we are trying to do here is detect if the sender has a manual conversation with the prospect or the
           * sender has cc'd themselves in an email with the prospect via a campaign
           *
           * for example we had a <NAME_EMAIL> was CC'ding himself as
           * <EMAIL>
           * This was pausing and completing the campaign.
           *
           * What we are doing here is precisely detecting this and not pausing the campaign.
           *
           * This should have no effect on previous reply tracker as we go into this flow only if replyDetectedInfo is empty
           * all previous check will detect the reply as it was earlier
           */

          /**
           * neil@smartreach trying to sell to prachi@google
           *
           * 1 neil sends email and adds Prachi and himself in To:
           *   It is not possible because we don't allow adding ourself in to emails for campaign emails
           * 2 neil sends email and adds himself in CC but Prachi is in To:
           *   It will detect it as reply due to matching gmailThreadId and mark the campaign as completed
           * 3 neil sends email and adds himself in BCC but Prachi is in To:
           *    We are not reading bcc emails for message even though it is same inbox
           *   It will detect it as reply due to matching gmailThreadId and mark the campaign as completed
           */

          // 8. checkIfFromEmailIsOfProspectOnly can bcc or cc
          if (replyDetectedInfo.isEmpty) {

            val inboxEmailIsInCC: Boolean = checkIfCCEmail(
              ccEmails = commonPropsEmailMessage.cc_emails,
              inboxEmail = inboxEmailSetting.email
            )

            val inboxEmailInBCC: Boolean = checkIfInboxEmailIsInBcc(
              fromEmail = commonPropsEmailMessage.from.email,
              toEmails = commonPropsEmailMessage.to_emails,
              ccEmails = commonPropsEmailMessage.cc_emails,
              inboxEmail = inboxEmailSetting.email
            )
            if (inboxEmailIsInCC) {
              /**
               * 2 neil sends email and adds himself in CC but Prachi is in To:
               * *   It will detect it as reply due to matching gmailThreadId and mark the campaign as completed
               *
               * If this inbox is in cc of another eset of team then cc wil get detected
               */
//              println(s"\nFromEmail - ${commonPropsEmailMessage.from.email} :: ToEmails - ${commonPropsEmailMessage.to_emails.map(_.email)} :: InboxEmail - ${inboxEmailSetting.email} :: ccEmails - ${commonPropsEmailMessage.cc_emails.map(_.email)} :: sender inbox email is in cc")
              replyDetectedInfo = Some(ReplyDetectedInfo.ShouldBeCCEmail(
                emailReplyStatus = propsTrackedReply.email_status.copy(isReplied = false)
              ))
            } else if(inboxEmailInBCC){

              /**
               * 3 neil sends email and adds himself in BCC but Prachi is in To:
               * *    We are not reading bcc emails for message even though it is same inbox
               *      It will detect it as reply due to matching gmailThreadId and mark the campaign as completed
               *      
               *  If this inbox is in bcc of another eset of team then bcc wil get detected
               */

//              println(s"\nFromEmail - ${commonPropsEmailMessage.from.email} :: ToEmails - ${commonPropsEmailMessage.to_emails.map(_.email)} :: InboxEmail - ${inboxEmailSetting.email} :: sender inbox email is not in to emails or from email or cc emails so it should be bcc")
              replyDetectedInfo = Some(ReplyDetectedInfo.ShouldBeBccEmail(
                emailReplyStatus = propsTrackedReply.email_status.copy(isReplied = false)
              ))
            } else {

              /**
               * * 1 neil sends email and adds Prachi and himself in To:
               * *   It is not possible because we don't allow adding ourself in to emails for campaign emails
               */

//              println(s"\nFromEmail - ${commonPropsEmailMessage.from.email} :: ToEmails - ${commonPropsEmailMessage.to_emails.map(_.email)} :: InboxEmail - ${inboxEmailSetting.email} :: sender inbox email is not in cc/bcc means either it is manual email from inbox or prospect email")
              replyDetectedInfo = None
            }

          }

          // nxd notes: This is the point where the reply is detected
          // hence this is one place where we can mark the email
          // thread as from a prospect - from the automatic detection angle.
          // Hence there are 2 places in the code v3 (the old method) and v4
          // the new one replacing it.
          replyDetectedInfo match {

            case None =>
//             Logger.error(s"ReplyFilteringService FATAL filterReplies didnt match any pre-condition ($inboxEmailSettingId : $senderEmails):: baseEmail: $baseEmail")
            // no actions

            // 1. check if email thread id already found
            case Some(data: ReplyDetectedInfo.ReplyDetectedViaThreadExistingInDB) =>
              // no actions
              // PR review question - why no actions here
              // As per the old code - we only update the message based on conditions labelled 2 to 6
              // but not 1. This case 1 is brought in to maintain the completeness for exhaustive check
              Logger.info(s"ReplyFilteringService filterRepliesV4 replyMatchFound :: ReplyDetectedViaThreadExistingInDB internalTrackingNote: ${data.internalTrackingNote} :: messageIdRegexMatch ($inboxEmailSettingId : $senderEmails): ${commonPropsEmailMessage.message_id} :: ${propsTrackedReply.campaign_id.map(_.id)} :: ${propsTrackedReply.prospect_id_in_campaign.map(_.id)} :: ${propsTrackedReply.step_id.map(_.id)} :: ${commonPropsEmailMessage.subject} :: ${commonPropsEmailMessage.from.email} :: ${propsTrackedReply.scheduled_manually}")

            // 2. check against patterns
            case Some(data: ReplyDetectedInfo.ReplyDetectedViaReplyToHeaderInfo) =>
              // TODO nxd - this block is duplicated below and i will extract it as a method
              // propsTrackedReply has the value of i
              val prev_temp_thread_id = propsTrackedReply.tempThreadId
              val propsTrackedReplyNew: PropsTrackedReply = propsTrackedReply.copy(
                campaign_id = data.campaignId,
                step_id = data.stepId,
                prospect_id_in_campaign = data.prospectId,
                internal_tracking_note = data.internalTrackingNote
              )
              val new_temp_thread_id_if_changed = propsTrackedReplyNew.tempThreadId

              // todo - need to move this copying into a fn (or macro)
              //val propsTrackedReply: PropsTrackedReply = PropsTrackedReply
              // baseEmail - has the value of i initially
              val email = updatePropsTrackedReply(
                baseEmail = baseEmail,
                propsTrackedReplyNew = propsTrackedReplyNew)

              Logger.info(s"ReplyFilteringService filterRepliesV4 replyMatchFound :: ReplyDetectedViaReplyToHeaderInfo internalTrackingNote: ${data.internalTrackingNote} :: messageIdRegexMatch ($inboxEmailSettingId : $senderEmails): ${commonPropsEmailMessage.message_id} :: ${propsTrackedReply.campaign_id.map(_.id)} :: ${propsTrackedReply.prospect_id_in_campaign.map(_.id)} :: ${propsTrackedReply.step_id.map(_.id)} :: ${commonPropsEmailMessage.subject} :: ${commonPropsEmailMessage.from.email} :: ${propsTrackedReply.scheduled_manually}")

              // update list
              newMsgsWithEmailThreadId(i) = email
              Logger.info(s"__assignEmailThreadIdsForExistingEmailThreadsV4 INBOXDEBUG ReplyDetectedViaReplyToHeaderInfo prev_temp_thread_id :  ${prev_temp_thread_id}, new_temp_thread_id_if_changed = ${new_temp_thread_id_if_changed} ")

            case Some(data: ReplyDetectedInfo.ReplyDetectedViaReferencesHeaderInfo) =>
              // TODO nxd - this block is duplicated below and i will extract it as a method (duplicated above)



              val prev_temp_thread_id = propsTrackedReply.tempThreadId
              val propsTrackedReplyNew: PropsTrackedReply = propsTrackedReply.copy(
                campaign_id = data.campaignId,
                step_id = data.stepId,
                prospect_id_in_campaign = data.prospectId,
                internal_tracking_note = data.internalTrackingNote
              )
              val new_temp_thread_id_if_changed = propsTrackedReplyNew.tempThreadId

              // todo - need to move this copying into a fn (or macro)
              //val propsTrackedReply: PropsTrackedReply = PropsTrackedReply

              val email = updatePropsTrackedReply(
                baseEmail = baseEmail,
                propsTrackedReplyNew = propsTrackedReplyNew)

              Logger.info(s"ReplyFilteringService filterRepliesV4 replyMatchFound :: ReplyDetectedViaReferencesHeaderInfo internalTrackingNote: ${data.internalTrackingNote} :: messageIdRegexMatch ($inboxEmailSettingId : $senderEmails): ${commonPropsEmailMessage.message_id} :: ${propsTrackedReply.campaign_id.map(_.id)} :: ${propsTrackedReply.prospect_id_in_campaign.map(_.id)} :: ${propsTrackedReply.step_id.map(_.id)} :: ${commonPropsEmailMessage.subject} :: ${commonPropsEmailMessage.from.email} :: ${propsTrackedReply.scheduled_manually}")

              // update list
              newMsgsWithEmailThreadId(i) = email
              Logger.info(s"__assignEmailThreadIdsForExistingEmailThreadsV4 INBOXDEBUG ReplyDetectedViaReferencesHeaderInfo prev_temp_thread_id :  ${prev_temp_thread_id}, new_temp_thread_id_if_changed = ${new_temp_thread_id_if_changed} ")

            // 3. check with message-id
            case Some(ReplyDetectedInfo.ReplyDetectedViaMessageIdsSentLastWeek(
            emailScheduledForCheckingReplies,
            internalTrackingNote
            )) =>
              //val prospect = prospectWasEmailedLastWeekViaMessageIDMatch.get // old
              val prospect = emailScheduledForCheckingReplies // new from the data captured


              // todo - need to move this copying into a fn (or macro)
              val prev_temp_thread_id = propsTrackedReply.tempThreadId
              val propsTrackedReplyNew: PropsTrackedReply = propsTrackedReply.copy(
                campaign_id = prospect.campaign_id,
                step_id = prospect.step_id,
                prospect_id_in_campaign = Some(prospect.prospect_id),
                prospect_account_id_in_campaign = prospect.prospect_account_id,
                email_thread_id = EmailReplyTrackingService.__foundEmailThreadIdv4(
                  baseEmail = baseEmail,
                  prospect = prospect,
                  Logger = Logger
                ),
                internal_tracking_note = internalTrackingNote
              )


              val email = updatePropsTrackedReply(
                baseEmail = baseEmail,
                propsTrackedReplyNew = propsTrackedReplyNew)
              val new_temp_thread_id_if_changed = propsTrackedReplyNew.tempThreadId

              Logger.info(s"ReplyFilteringService filterRepliesV4 replyMatchFound :: ReplyDetectedViaMessageIdsSentLastWeek internalTrackingNote: $internalTrackingNote :: prospectWasEmailedLastWeekViaMessageIDMatch ($inboxEmailSettingId : $senderEmails): ${commonPropsEmailMessage.message_id} :: ${propsTrackedReply.campaign_id.map(_.id)} :: ${propsTrackedReply.prospect_id_in_campaign.map(_.id)} :: ${propsTrackedReply.step_id.map(_.id)} :: ${commonPropsEmailMessage.subject} :: ${commonPropsEmailMessage.from.email} :: ${propsTrackedReply.scheduled_manually}")

              // update list
              newMsgsWithEmailThreadId(i) = email
              Logger.info(s"__assignEmailThreadIdsForExistingEmailThreadsV4 INBOXDEBUG ReplyDetectedViaMessageIdsSentLastWeek prev_temp_thread_id :  ${prev_temp_thread_id}, new_temp_thread_id_if_changed = ${new_temp_thread_id_if_changed} ")

            // 6. check if prospect_account_id and domain matches
            case Some(data: ReplyDetectedInfo.ReplyDetectedViaProspectAccountDomain) =>
              //val prospect = prospectWasEmailedLastWeekViaToProspectAccountIdAndDomainMatch.get //old
              val prospect = data.emailScheduledForCheckingReplies // new from captured data


              val prev_temp_thread_id = propsTrackedReply.tempThreadId
              val propsTrackedReplyNew: PropsTrackedReply = propsTrackedReply.copy(
                campaign_id = prospect.campaign_id,
                step_id = prospect.step_id,
                prospect_id_in_campaign = Some(prospect.prospect_id),
                prospect_account_id_in_campaign = prospect.prospect_account_id,
                email_thread_id = EmailReplyTrackingService.__foundEmailThreadIdv4(
                  baseEmail = baseEmail,
                  prospect = prospect,
                  Logger = Logger
                ),
                internal_tracking_note = data.internalTrackingNote
              )


              val email = updatePropsTrackedReply(
                baseEmail = baseEmail,
                propsTrackedReplyNew = propsTrackedReplyNew)

              Logger.info(s"ReplyFilteringService filterRepliesV4 replyMatchFound :: ReplyDetectedViaProspectAccountDomain internalTrackingNote: ${data.internalTrackingNote} :: prospectWasEmailedLastWeekViaMessageIDMatch ($inboxEmailSettingId : $senderEmails): ${commonPropsEmailMessage.message_id} :: ${propsTrackedReply.campaign_id.map(_.id)} :: ${propsTrackedReply.prospect_id_in_campaign.map(_.id)} :: ${propsTrackedReply.step_id.map(_.id)} :: ${commonPropsEmailMessage.subject} :: ${commonPropsEmailMessage.from.email} :: ${propsTrackedReply.scheduled_manually}")
              val new_temp_thread_id_if_changed = propsTrackedReplyNew.tempThreadId
              // update list
              newMsgsWithEmailThreadId(i) = email
              Logger.info(s"__assignEmailThreadIdsForExistingEmailThreadsV4 INBOXDEBUG ReplyDetectedViaProspectAccountDomain prev_temp_thread_id :  ${prev_temp_thread_id}, new_temp_thread_id_if_changed = ${new_temp_thread_id_if_changed} ")

            // 4. check if to_email matches
            case Some(data: ReplyDetectedInfo.ReplyDetectedViaToEmailsSentLastWeek) =>
              //val prospect = prospectWasEmailedLastWeekViaToEmailMatch.get // old

              val prospect = data.emailScheduledForCheckingReplies // new captured data

              val prev_temp_thread_id = propsTrackedReply.tempThreadId
              val propsTrackedReplyNew: PropsTrackedReply = propsTrackedReply.copy(
                campaign_id = prospect.campaign_id,
                step_id = prospect.step_id,
                prospect_id_in_campaign = Some(prospect.prospect_id),
                prospect_account_id_in_campaign = prospect.prospect_account_id,
                email_thread_id = EmailReplyTrackingService.__foundEmailThreadIdv4(
                  baseEmail = baseEmail,
                  prospect = prospect,
                  Logger = Logger
                ),
                internal_tracking_note = data.internalTrackingNote
              )

              val email = updatePropsTrackedReply(
                baseEmail = baseEmail,
                propsTrackedReplyNew = propsTrackedReplyNew)
              val new_temp_thread_id_if_changed = propsTrackedReplyNew.tempThreadId
              Logger.info(s"ReplyFilteringService filterRepliesV4 replyMatchFound :: ReplyDetectedViaToEmailsSentLastWeek internalTrackingNote: ${data.internalTrackingNote} :: prospectWasEmailedLastWeekViaToEmailMatch ($inboxEmailSettingId : $senderEmails): ${commonPropsEmailMessage.message_id} :: ${propsTrackedReply.campaign_id.map(_.id)} :: ${propsTrackedReply.prospect_id_in_campaign.map(_.id)} :: ${propsTrackedReply.step_id.map(_.id)} :: ${commonPropsEmailMessage.subject} :: ${commonPropsEmailMessage.from.email} :: ${propsTrackedReply.scheduled_manually}")

              // update list
              newMsgsWithEmailThreadId(i) = email
              Logger.info(s"__assignEmailThreadIdsForExistingEmailThreadsV4 INBOXDEBUG ReplyDetectedViaToEmailsSentLastWeek prev_temp_thread_id :  ${prev_temp_thread_id}, new_temp_thread_id_if_changed = ${new_temp_thread_id_if_changed} ")

            // 5. if no prospect_id and email is categorized as delivery_failed, check body if its got email of any of the prospects sent last week
            case Some(data: ReplyDetectedInfo.ReplyDetectedViaDeliveryFailedInEmailBody) =>
              // track delivery failed prospects, even if the bounced emails come in different thread with the prospect's email inside the body

              // CHECKME: associate email_thread_id, gmail_thread_id
              //val prospect = prospectDeliveryFailedCheckBody.get // old
              val prospect = data.emailScheduledForCheckingReplies

              val prev_temp_thread_id = propsTrackedReply.tempThreadId
              val propsTrackedReplyNew: PropsTrackedReply = propsTrackedReply.copy(
                campaign_id = prospect.campaign_id,
                step_id = prospect.step_id,
                prospect_id_in_campaign = Some(prospect.prospect_id),
                prospect_account_id_in_campaign = prospect.prospect_account_id,
                internal_tracking_note = data.internalTrackingNote
              )


              val email = updatePropsTrackedReply(
                baseEmail = baseEmail,
                propsTrackedReplyNew = propsTrackedReplyNew)
              val new_temp_thread_id_if_changed = propsTrackedReplyNew.tempThreadId

              Logger.info(s"ReplyFilteringService filterRepliesV4 replyMatchFound :: ReplyDetectedViaDeliveryFailedInEmailBody internalTrackingNote: ${data.internalTrackingNote} :: prospectDeliveryFailedCheckBody ($inboxEmailSettingId : $senderEmails): ${commonPropsEmailMessage.message_id} :: ${propsTrackedReply.campaign_id.map(_.id)} :: ${propsTrackedReply.prospect_id_in_campaign.map(_.id)} :: ${propsTrackedReply.step_id.map(_.id)} :: ${commonPropsEmailMessage.subject} :: ${commonPropsEmailMessage.from.email} :: ${propsTrackedReply.scheduled_manually}")

              // update list
              newMsgsWithEmailThreadId(i) = email
              Logger.info(s"__assignEmailThreadIdsForExistingEmailThreadsV4 INBOXDEBUG ReplyDetectedViaDeliveryFailedInEmailBody prev_temp_thread_id :  ${prev_temp_thread_id}, new_temp_thread_id_if_changed = ${new_temp_thread_id_if_changed} ")

            case Some(data: ReplyDetectedInfo.InboxPlacementCheckTestEmail) =>

              val propsTrackedReplyNew: PropsTrackedReply = propsTrackedReply.copy(
                internal_tracking_note = data.internalTrackingNote
              )

              val email = updatePropsTrackedReply(
                baseEmail = baseEmail,
                propsTrackedReplyNew = propsTrackedReplyNew)

              Logger.info(s"ReplyFilteringService filterRepliesV4 :: InboxPlacementCheckTestEmail internalTrackingNote: ${data.internalTrackingNote} :: messageIdRegexMatch ($inboxEmailSettingId : $senderEmails): ${commonPropsEmailMessage.message_id} :: ${propsTrackedReply.campaign_id.map(_.id)} :: ${propsTrackedReply.step_id.map(_.id)} :: ${commonPropsEmailMessage.subject} :: ${commonPropsEmailMessage.from.email} :: ${propsTrackedReply.scheduled_manually}")

              newMsgsWithEmailThreadId(i) = email

            case Some(data: ReplyDetectedInfo.ShouldBeCCEmail) =>

              val propsTrackedReplyNew: PropsTrackedReply = propsTrackedReply.copy(
                internal_tracking_note = data.internalTrackingNote,
                email_status = data.emailReplyStatus
              )

              val email = updatePropsTrackedReply(
                baseEmail = baseEmail,
                propsTrackedReplyNew = propsTrackedReplyNew)

              Logger.info(s"ReplyFilteringService filterRepliesV4 :: ShouldBeCCEmail internalTrackingNote: ${data.internalTrackingNote} :: inbox and sender emails ($inboxEmailSettingId : $senderEmails): ${commonPropsEmailMessage.message_id} :: ${propsTrackedReply.campaign_id.map(_.id)} :: ${propsTrackedReply.step_id.map(_.id)} :: ${commonPropsEmailMessage.subject} :: ${commonPropsEmailMessage.from.email} :: ${propsTrackedReply.scheduled_manually} :: ${propsTrackedReply.email_status} :: pastWeekProspectEmails: ${prospectEmailsPastWeek.map(_.prospect_email)} :: toEmails: ${commonPropsEmailMessage.to_emails.map(_.email)} :: ccEmails: ${commonPropsEmailMessage.cc_emails.map(_.email)}")

              newMsgsWithEmailThreadId(i) = email

            case Some(data: ReplyDetectedInfo.ShouldBeBccEmail) =>

              val propsTrackedReplyNew: PropsTrackedReply = propsTrackedReply.copy(
                internal_tracking_note = data.internalTrackingNote,
                email_status = data.emailReplyStatus
              )

              val email = updatePropsTrackedReply(
                baseEmail = baseEmail,
                propsTrackedReplyNew = propsTrackedReplyNew)

              Logger.info(s"ReplyFilteringService filterRepliesV4 :: ShouldBeBccEmail internalTrackingNote: ${data.internalTrackingNote} :: inbox and sender emails ($inboxEmailSettingId : $senderEmails): ${commonPropsEmailMessage.message_id} :: ${propsTrackedReply.campaign_id.map(_.id)} :: ${propsTrackedReply.step_id.map(_.id)} :: ${commonPropsEmailMessage.subject} :: ${commonPropsEmailMessage.from.email} :: ${propsTrackedReply.scheduled_manually} :: ${propsTrackedReply.email_status} :: pastWeekProspectEmails: ${prospectEmailsPastWeek.map(_.prospect_email)} :: toEmails: ${commonPropsEmailMessage.to_emails.map(_.email)} :: ccEmails: ${commonPropsEmailMessage.cc_emails.map(_.email)}")

              newMsgsWithEmailThreadId(i) = email
          }


        }


        // L. push unsubscribe requests to queue
        newMsgsWithEmailThreadId
          .map(m => {
            UnSubscribeLinkHelper.checkIfSRListUnsubscribeEmail(
              toEmailAddresses = getCommonPropsEmailMessage(m).to_emails.map(_.email)
            )
          })
          .filter(em => em.isDefined)
          .foreach(em => {
            pushUnsubRequestToMQ( //ReplyTracking_ReceiveEmail_DAO_UPDATE_OptedOut
              listUnsubscribeEmailAddress = em.get,
              Logger = Logger
            )
          })


        newMsgsWithEmailThreadId.toVector
      }
    }
  }

}
