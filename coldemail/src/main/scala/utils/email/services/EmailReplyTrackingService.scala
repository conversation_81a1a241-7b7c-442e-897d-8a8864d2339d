package utils.email.services


import api.accounts.TeamId
import api.accounts.email.models.EmailServiceProvider
import api.accounts.models.AccountId
import api.campaigns.services.CampaignId
import api.emails.*
import api.emails.dao_service.EmailScheduledDAOService
import api.emails.models.{EmailMessageTrackedV4, PropsTrackedReply}
import api.emails.models.EmailMessageTrackedV4.{GmailMessageTrackedV4, ImapTrackedReplyV4, OutlookMessageTrackedV4, getCommonPropsEmailMessage, getGmailThreadId, getOutlookConversationId, getPropsTrackedReply}
import api.prospects.models.{ProspectAccountsId, ProspectId, StepId}
import com.sun.mail.gimap.{GmailFolder, GmailMessage}
import com.sun.mail.imap.IMAPFolder
import io.smartreach.esp.api.emails.{EmailSettingId, IEmailAddress}
import io.smartreach.esp.utils.email.models.EmailReply.{GmailReplyTrackedViaAPI, ImapTrackedReply, OutlookReplyTrackedViaAPI}
import io.smartreach.esp.utils.email.models.{CommonPropsEmailMessage, EmailReply, EmailServiceTempCommon, ImapParsedReply}
import io.smartreach.esp.utils.email.services.EmailReplyTrackingCommonService
import org.joda.time.DateTime
import play.api.libs.json.{JsValue, Json}
import play.api.libs.ws.WSClient
import utils.email.*
import utils.email.services.InternalTrackingNote.InternalTrackingNote
import utils.{Helpers, SRLogger}

import javax.mail.*
import javax.mail.search.*
import scala.collection.mutable.ListBuffer
import scala.concurrent.{ExecutionContext, Future, blocking}
import scala.util.matching.Regex
import scala.util.{Failure, Success, Try}

case class SRSmtpSendFailedWithSpamORRejectedWarning(message: String = "", cause: Throwable = None.orNull) extends Exception(message, cause)


object InternalTrackingNote extends Enumeration {
  type InternalTrackingNote = Value

  val NONE = Value("none")
  val EXISTING_GMAILTHREADID_IN_EMAILSSCHEDULED_TABLE = Value("e_gmail_ES")
  val EXISTING_GMAILTHREAD_IN_EMAILTHREAD_TABLE = Value("e_gmail_ET")
  val EXISTING_OUTLOOKCONVERSATIONID_IN_EMAILSSCHEDULED_TABLE = Value("e_outlook_ES")
  val EXISTING_OUTLOOKCONVERSATION_IN_EMAILTHREAD_TABLE = Value("e_outlook_ET")
  val EXISTING_INREPLYTO = Value("e_inreplyto")
  val EXISTING_REFERENCES = Value("e_references")

  val NEW_MESSAGEIDREGEX_INREPLYTO_HEADER = Value("n_mid_regex_inrepto")
  val NEW_MESSAGEIDREGEX_REFERENCES_HEADER = Value("n_mid_regex_ref")

  val NEW_LASTWEEK_MESSAGEID_INREPLYTO_HEADER = Value("n_lw_mid_inrepto")
  val NEW_LASTWEEK_MESSAGEID_REFERENCES_HEADER = Value("n_lw_mid_ref")

  val NEW_LASTWEEK_TOEMAIL = Value("n_lastweek_toemail")
  val NEW_LASTWEEK_ACCOUNT = Value("n_lw_acct")
  val NEW_LASTWEEK_DOMAIN = Value("n_lw_dom")
  val NEW_LASTWEEK_DELIVERYFAILED = Value("n_lastweek_bounced")
  /*Added on 25th Aug 2022 for customer support (Corey Lark)*/
  val REVERTED_BOUNCE_STATUS_CUSTOMER_SUPPORT = Value("reverted_bounce_status_customer_support")
  val INBOX_PLACEMENT_CHECK_TEST_EMAIL = Value("inbox_placement_check_test_email")
  
  val SHOULD_BE_CC_EMAIL = Value("should_be_cc_email")
  val SHOULD_BE_BCC_EMAIL = Value("should_be_bcc_email")

  // given format = EnumUtils.enumFormat(InternalTrackingNote)

}



/*
NOTE: [ReplyFilteringRefactor] idea

sealed trait EmailReplyTrackedMessage
object EmailReplyTrackedMessage {

  case class GmailReplyTracked() extends EmailReplyTrackedMessage // breakdown of ReplyTrackedViaAPI

  case class OutlookReplyTracked() extends EmailReplyTrackedMessage // breakdown of ReplyTrackedViaAPI

  // first iteration is, instead of having GmailReplyTracked vs OutlookReplyTracked, we will just have "ReplyTrackedViaAPI" itself here
  // so initially this trait will have two subtypes: ReplyTrackedViaAPI and ImapReplyTracked

  case class ImapReplyTracked(message: Message) extends EmailReplyTrackedMessage


}
trait EmailReplyTrackingService[T <: EmailReplyTracked] {
}
*/


trait EmailReplyTrackingService {


  // TODO - review with Prateek / Sathish
  // TODO/FIXME/WARNING - this is exposed only for the unit tests
  //    DO not direclty use this in production code
  val replyFilteringService: ReplyFilteringService
  val emailScheduledDAOService: EmailScheduledDAOService


  def receiveEmail(receiverEmailSetting: EmailSetting, senderEmailSettings: Seq[EmailSetting],

                   Logger: SRLogger,
                   fromDate: DateTime,
                   tillDate: Option[DateTime]
                  )(implicit wsClient: WSClient, ec: ExecutionContext): Future[Vector[EmailMessageTracked]]

  def findTotalNumberOfEmailsInInbox(receiverEmailSetting: EmailSetting,
                                     Logger: SRLogger,
                                    )(implicit wsClient: WSClient, ec: ExecutionContext): Future[Int]


  private final def _getAllNonEmptyFolders(
                                            inboxEmailSettingId: Long,
                                            store: Store,
                                            Logger: SRLogger
                                          ): Try[Seq[IMAPFolder]] = blocking {
    Try {

      val folders = store.getDefaultFolder.list("*").toSeq.asInstanceOf[Seq[IMAPFolder]]

      val nonEmptyFolders: Seq[IMAPFolder] = folders.filter(f => {

        val folderName: Option[String] = Try {
          f.getFullName
        } match {
          case Failure(e) =>

            val exceptionMsg = Option(e.getMessage).getOrElse("")

            if (
              exceptionMsg.contains("[NONEXISTENT] Invalid folder")
            ) {

              // log nothing

            } else {

              Logger.error(s"[TEmailService] getAllNonEmptyFolders ($inboxEmailSettingId) getFullName", err = e)

            }

            None

          case Success(c) => Option(c) // c could be "null" as well (JAVA).
        }

        val messageCount: Int = Try {
          f.getMessageCount
        } match {
          case Failure(e) =>

            val exceptionMsg = Option(e.getMessage).getOrElse("")

            if (
              exceptionMsg.contains("[NONEXISTENT] Invalid folder")
            ) {

              // log nothing

            } else {

              Logger.error(s"[TEmailService] getAllNonEmptyFolders ($inboxEmailSettingId :: $folderName) getMessageCount", err = e)

            }

            0

          case Success(c) => c
        }

        // Logger.info(s"folder: ${f.getFullName} :: ${f.getName} :: $messageCount :: ${f.getAttributes.toList.map(_.toString)}")

        messageCount > 0

      })

      nonEmptyFolders

    }
  }

  final def closeInbox(inbox: Folder): Try[Unit] = blocking {
    Try {
      inbox.close(false)
    }
  }

  final def closeImapConnection(store: Store): Try[Unit] = blocking {
    Try {
      store.close()
    }
  }


  final def getRecentImapMessages(
                                   inbox: Folder,
                                   emailAfterDate: DateTime,
                                   emailTillDate: Option[DateTime],
                                   isTestingSettings: Boolean = false,
                                   Logger: SRLogger
                                 ): Try[Vector[Message]] = blocking {
    Try {
      Logger.info(s"[TEmailService] getRecentMessages recent messages: ${inbox.getFullName} :: $emailAfterDate :: actual: ${emailAfterDate.withTimeAtStartOfDay().toDate}")

      //    val olderThan: SearchTerm = new ReceivedDateTerm(ComparisonTerm.LT, DateTime.now.toDate)

      // NOTE: The IMAP protocol only provides resolution to the day, no time.
      // REF: http://stackoverflow.com/questions/15967234/javamail-fetching-messages-for-the-last-hour
      val timeLimit = {
        val sentAtNewerThan: SearchTerm = new SentDateTerm(ComparisonTerm.GE, emailAfterDate.withTimeAtStartOfDay().toDate)
        val receivedAtNewerThan: SearchTerm = new ReceivedDateTerm(ComparisonTerm.GE, emailAfterDate.withTimeAtStartOfDay().toDate)

        val newerThan = new OrTerm(sentAtNewerThan, receivedAtNewerThan)

        if (emailTillDate.isDefined) {
          val sentAtOlderThan: SearchTerm = new SentDateTerm(ComparisonTerm.LE, emailTillDate.get.withTimeAtStartOfDay().plusDays(1).toDate)
          val receivedAtOlderThan: SearchTerm = new ReceivedDateTerm(ComparisonTerm.LE, emailTillDate.get.withTimeAtStartOfDay().plusDays(1).toDate)

          val olderThan = new OrTerm(sentAtOlderThan, receivedAtOlderThan)

          new AndTerm(newerThan, olderThan)
        } else {
          newerThan
        }
      }


      //    val andTerm: SearchTerm = new AndTerm(olderThan, newerThan)

      var messages: Array[Message] = inbox.search(timeLimit)

      if (isTestingSettings && messages.length > 500) {
        Logger.info(s"[TEmailService] getRecentMessages isTestingSettings: $isTestingSettings :: recent messages: ${inbox.getFullName} :: $emailAfterDate :: actual: ${emailAfterDate.withTimeAtStartOfDay().toDate} :: total: ${messages.size} taking only 500")

        messages = messages.take(500)
      }

      //    Logger.info(s"gmail got messages: ${messages.size}")
      // NOW, filter by exact time
      //    val messagesFilteredByExactTime = messages.filter(m => new DateTime(m.getSentDate).isAfter(emailAfterDate) || new DateTime(m.getReceivedDate).isAfter(emailAfterDate))

      //    Logger.info(s"gmail got messages filtered: ${messagesFilteredByExactTime.size}")

      //    messagesFilteredByExactTime.toVector

      val fetchProfile = new FetchProfile
      fetchProfile.add(FetchProfile.Item.ENVELOPE)


      // Removed fetchprofile info because on some outlook emails, the getContent was empty when using this, dafaq javamail
      // REF: https://stackoverflow.com/questions/33164207/javamail-fetchprofile-item-content-info-breaks-getcontent
      //        fetchProfile.add(FetchProfile.Item.CONTENT_INFO)
      fetchProfile.add("Content-Type")


      fetchProfile.add(FetchProfile.Item.FLAGS)


      // NOTE: commented out FetchProfileItem.MESSAGE: it was choking the server
      // when there were messages with huge attachments etc.
      // REF: https://javaee.github.io/javamail/docs/api/com/sun/mail/imap/IMAPFolder.FetchProfileItem.html
      //fetchProfile.add(IMAPFolder.FetchProfileItem.MESSAGE)
      fetchProfile.add(IMAPFolder.FetchProfileItem.HEADERS)


      fetchProfile.add("References")
      fetchProfile.add("Message-ID")
      fetchProfile.add("In-Reply-To")

      inbox match {
        case folder: GmailFolder =>

          fetchProfile.add(GmailFolder.FetchProfileItem.MSGID)
          fetchProfile.add(GmailFolder.FetchProfileItem.THRID)

          folder.fetch(messages, fetchProfile)
          messages.toVector.asInstanceOf[Vector[GmailMessage]]

        case _ =>

          inbox.fetch(messages, fetchProfile)
          messages.toVector

      }


    }
  }

  final def getTotalMessageCount(
                                  inbox: Folder,
                                  Logger: SRLogger
                                ): Try[Int] = blocking {
    Try {
      val totalCount = inbox.getMessageCount
      Logger.info(s"[TEmailService] getTotalMessageCount recent messages: ${inbox.getFullName} count: $totalCount")
      totalCount

    }
  }

  def excludeExistingMessagesInEmailsScheduled(
                                                msgsWithMessageIdHeader: Seq[(String, EmailReply)],
                                                inboxEmailSettingId: Int,
                                                teamId: TeamId
                                              )(using Logger: SRLogger): Try[Seq[(String, EmailReply)]] = {

    val allmessageIdHeaders: Seq[String] = msgsWithMessageIdHeader.map(_._1)

    // DONE: why not check against receiver_email_setting_id instead of team_id ?
    // note: this data comes from emails_scheduled
    emailScheduledDAOService.checkIfEmailIsAlreadySaved(
      messageIds = allmessageIdHeaders,
      inboxEmailSettingId = inboxEmailSettingId,
      teamId = teamId
    ).map(alreadySavedMessageIds =>
      msgsWithMessageIdHeader.filter(m => {
        // ignore msgs which are already saved
        !alreadySavedMessageIds.contains(m._1)
      })
    )
  }

  def convertTupleIDEmailReplyToEmailMessageTrackedV4(
                                                       msg: (String, EmailReply),
                                                       senderEmails: Seq[String],
                                                       inboxEmailSettingId: Int,
                                                       teamId: Long,
                                                       accountId: Int,
                                                       foundInInboxFolder: Option[String],
                                                       
                                                     )(
    using Logger: SRLogger
  ): Option[EmailMessageTrackedV4] = {

    val messageTrackedV4: Option[EmailMessageTrackedV4] = msg match {
      case (msg_id: String, apiTrackedReply: GmailReplyTrackedViaAPI) => {
        //val apiTrackedReply = apiTrackedReply.apiMsg

        val commonPropsEmailMessage: CommonPropsEmailMessage = apiTrackedReply.commonPropsEmailMessage
        val byAccount = senderEmails.contains(Helpers.trimToLower(commonPropsEmailMessage.from
          .email))

        val emailStatus: EmailReplyStatus = EmailReplyStatus
          .getEmailReplyStatusBeforeSaving(
            emailBaseBody = commonPropsEmailMessage.base_body,
            subject = commonPropsEmailMessage.subject,
            fromEmail = commonPropsEmailMessage.from.email,
            fromName = commonPropsEmailMessage.from.name.getOrElse(""),
            toEmailAddresses = commonPropsEmailMessage.to_emails.map(_.email),
            byAccount = byAccount,
            fullHeaders = commonPropsEmailMessage.full_headers,
            received_at = commonPropsEmailMessage.received_at
          )

        val em = EmailMessageTrackedV4.GmailMessageTrackedV4(
          gmailReplyTrackedViaAPI = apiTrackedReply,
          propsTrackedReply = PropsTrackedReply(
            // =========
            inbox_email_setting_id = EmailSettingId(emailSettingId = inboxEmailSettingId), // FIXME VALUECLASS
            email_status = emailStatus,
            sr_inbox_read = byAccount,
            scheduled_manually = byAccount,

            campaign_id = None,
            step_id = None,
            prospect_id_in_campaign = None,

            // NOTE: this will be populated after checking validProspects
            // inside EmailScheduled
            // .saveRepliesDeleteOtherScheduledEmailsAndUpdateRepliedStatusV2
            prospect_account_id_in_campaign = None,
            email_thread_id = None,

            // will be assigned next
            tempThreadId = None,
            //                  original_inbox_folder = foundInInboxFolder,

            internal_tracking_note = InternalTrackingNote.NONE,

            team_id = TeamId(id = teamId), // FIXME VALUECLASS
            account_id = AccountId(id = accountId), // FIXME VALUECLASS

            // these are set only in the save SQL function in EmailScheduled.scala
            campaign_name = None,
            step_name = None
          )
        )

        Some(em)

      }

      case (msg_id: String, apiTrackedReply: OutlookReplyTrackedViaAPI) => {
        //val apiTrackedReply = apiTrackedReply.apiMsg

        val commonPropsEmailMessage: CommonPropsEmailMessage = apiTrackedReply.commonPropsEmailMessage
        val byAccount = senderEmails.contains(commonPropsEmailMessage.from.email.trim.toLowerCase)

        val emailStatus: EmailReplyStatus = EmailReplyStatus
          .getEmailReplyStatusBeforeSaving(
            emailBaseBody = commonPropsEmailMessage.base_body,
            subject = commonPropsEmailMessage.subject,
            fromEmail = commonPropsEmailMessage.from.email,
            fromName = commonPropsEmailMessage.from.name.getOrElse(""),
            toEmailAddresses = commonPropsEmailMessage.to_emails.map(_.email),
            byAccount = byAccount,
            fullHeaders = commonPropsEmailMessage.full_headers,
            received_at = commonPropsEmailMessage.received_at
          )

        val em = EmailMessageTrackedV4.OutlookMessageTrackedV4(

          outlookReplyTrackedViaAPI = apiTrackedReply,


          // =================
          propsTrackedReply = PropsTrackedReply(
            inbox_email_setting_id = EmailSettingId(emailSettingId = inboxEmailSettingId), // FIXME VALUECLASS
            email_status = emailStatus,


            sr_inbox_read = byAccount,
            scheduled_manually = byAccount,

            campaign_id = None,
            step_id = None,
            prospect_id_in_campaign = None,

            // NOTE: this will be populated after checking validProspects
            // inside EmailScheduled
            // .saveRepliesDeleteOtherScheduledEmailsAndUpdateRepliedStatusV2
            prospect_account_id_in_campaign = None,
            email_thread_id = None,

            // will be assigned next
            tempThreadId = None,
            //                  original_inbox_folder = foundInInboxFolder,

            internal_tracking_note = InternalTrackingNote.NONE,

            team_id = TeamId(id = teamId), // FIXME VALUECLASS
            account_id = AccountId(id = accountId), // FIXME VALUECLASS

            // these are set only in the save SQL function in EmailScheduled.scala
            campaign_name = None,
            step_name = None
          )
        )

        Some(em)

      }

      case (msg_id: String, imapTrackedReply: ImapTrackedReply) =>
        EmailReplyTrackingService.__getNewImapMsgsWithHeadersV4(
          messageObj = (msg_id, imapTrackedReply),
          inboxEmailSettingId = inboxEmailSettingId,
          senderEmails = senderEmails,
          foundInInboxFolder = foundInInboxFolder,
          teamId = teamId,
          accountId = accountId,
        )

    }
    messageTrackedV4
  }

  def prefilterNewMessagesOnly(
                                messages: Vector[EmailReply],
                                inboxEmailSetting: EmailSetting,
                                senderEmails: Seq[String],
                                foundInInboxFolder: Option[String],
                                Logger: SRLogger
                              ): Try[Seq[EmailMessageTrackedV4]] =
    if (messages.isEmpty) Success(Seq[EmailMessageTrackedV4]()) else {

      given srLogger: SRLogger = Logger // fixme given
      
      val inboxEmailSettingId = inboxEmailSetting.id.get.emailSettingId // FIXME VALUECLASS
      val teamId = inboxEmailSetting.team_id.id // FIXME VALUECLASS
      val accountId = inboxEmailSetting.owner_id.id // FIXME VALUECLASS

      // A. fetch message id headers

      val msgsWithMessageIdHeader: Seq[(String, EmailReply)] =
        messages.map(msg => {
            val (msg_id, emailReply): (Option[String], EmailReply) =
              EmailReply.extractMessageId(msg = msg, logger = Logger)


            (msg_id, emailReply)
          })
          // throw out anything that can fail get
          .filter(_._1.isDefined)
          .map(m => (m._1.get, m._2))


      // ReplyTracking_ReceiveEmail_DAO_READ
      // B. check if emails already saved, and then ignore msgs that are already there in DB
      val newMsgs: Try[Seq[(String, EmailReply)]] = excludeExistingMessagesInEmailsScheduled(
        msgsWithMessageIdHeader = msgsWithMessageIdHeader,
        inboxEmailSettingId = inboxEmailSettingId.toInt,
        teamId = TeamId(id = teamId)
      )
      //.get


      // C. get baseEmails (EmailReplyTracked) structure
      val newMsgsWithHeaders: Try[Seq[EmailMessageTrackedV4]] = newMsgs
        .map(msgs =>
          msgs.map(msg => {
              convertTupleIDEmailReplyToEmailMessageTrackedV4(
                msg = msg,
                senderEmails = senderEmails,
                inboxEmailSettingId = inboxEmailSettingId.toInt,
                teamId = teamId,
                accountId = accountId.toInt,
                foundInInboxFolder = foundInInboxFolder,
              )
            })
            .filter(_.isDefined)
            .map(_.get)

        )
      newMsgsWithHeaders

    }

  final def getFilteredMessagesFromAllFolders(

                                               store: Store,
                                               emailAfterDate: DateTime,
                                               emailTillDate: Option[DateTime],
                                               inboxEmailSetting: EmailSetting,
                                               senderEmails: Seq[String],
                                               senderMessageIdSufixes: Seq[String],
                                               prospectEmailsPastWeek: Seq[EmailScheduledForCheckingReplies],
                                               Logger: SRLogger
                                             ): Try[Vector[EmailMessageTracked]] = blocking {
    Try {

      val res = _getAllNonEmptyFolders(
        inboxEmailSettingId = inboxEmailSetting.id.get.emailSettingId, // FIXME VALUECLASS
        store = store,
        Logger = Logger
      ) match {

        case Failure(e) =>

          Logger.fatal(s"[TEmailService] getFilteredMessagesFromAllFolders", err = e)

          throw e

        case Success(folders) =>

          val res2: Seq[Try[Seq[EmailMessageTrackedV4]]] = folders
            .filter(filterIMAPInbox)
            .map(f => {

              f.open(Folder.READ_ONLY)

              // Logger.info(s"FOLDER opened: ${f.getFullName} :: ${f.getAttributes.toList}")

              val folderName = Option(f.getFullName)
              val imapMessages = getRecentImapMessages(inbox = f, emailAfterDate = emailAfterDate, emailTillDate = emailTillDate,
                Logger = Logger
              ).get

              val fnamecheck = folderName.getOrElse("").trim.toLowerCase()

              val isSent: Boolean = fnamecheck.contains("sent")
              val isJunk: Boolean = fnamecheck.contains("junk")
              val isTrash: Boolean = fnamecheck.contains("trash")


              val messagesToCheckAndSave = if (
                isSent ||
                  isJunk ||
                  isTrash //||
              //fnamecheck.contains("spam")
              ) {


                if (imapMessages.size > 500) {

                  val logMsg = s"READEMAIL $emailAfterDate :: found more than 500 messages in: $folderName :: ${imapMessages.size} :: taking just 500 messages"

                  if (isSent) {

                    // logging as error only if we are skipping msgs from sent folder, and not trask/junk
                    Logger.error(logMsg)

                  } else {

                    Logger.debug(logMsg)

                  }


                  imapMessages.take(500)

                } else {

                  imapMessages
                }

              } else {

                if (imapMessages.size > 500) {
                  Logger.debug(s"READEMAIL $emailAfterDate :: found more than 500 messages [2] in: $folderName :: ${imapMessages.size} :: taking ALL messages")

                }

                imapMessages
              }

              /*
            val replies = filterReplies(
              messages = messagesToCheckAndSave.map(m => EmailReply.ImapTrackedReply(
                imapMsg = m
              )),
              inboxEmailSetting = inboxEmailSetting,
              senderEmails = senderEmails,
              senderMessageIdSufixes = senderMessageIdSufixes,
              prospectEmailsPastWeek = prospectEmailsPastWeek,
              foundInInboxFolder = folderName,
              Logger = Logger
            ).get
               */

              /*
              def prefilterNewMessagesOnly(
                                messages: Vector[EmailReply],
                                inboxEmailSetting: EmailSetting,
                                senderEmails: Seq[String],
                                foundInInboxFolder: Option[String],
                                Logger: SRLogger
                              ): Seq[EmailMessageTrackedV4]
               */
              val replies = prefilterNewMessagesOnly(
                messages = messagesToCheckAndSave.map(m => EmailReply.ImapTrackedReply(
                  imapMsg = m
                )),
                inboxEmailSetting = inboxEmailSetting,
                senderEmails = senderEmails,
                //senderMessageIdSufixes = senderMessageIdSufixes,
                //prospectEmailsPastWeek = prospectEmailsPastWeek,
                foundInInboxFolder = folderName,
                Logger = Logger
              )

              closeInbox(inbox = f)

              // Logger.info(s"FOLDER closed: ${f.getFullName} :: ${f.getAttributes.toList} :: found replies: ${replies.size} / ${messages.length}")

              replies
            })

          val repliesAcrossAllFolders: Try[Seq[EmailMessageTrackedV4]] = Helpers.seqTryToTrySeq(res2)
            .map(_.flatten)
          //.toVector
          repliesAcrossAllFolders match {
            case Success(replies) =>

              val replies2 = filterReplies(
                messages = replies,
                inboxEmailSetting = inboxEmailSetting,
                senderEmails = senderEmails,
                senderMessageIdSufixes = senderMessageIdSufixes,
                prospectEmailsPastWeek = prospectEmailsPastWeek,
                //foundInInboxFolder = folderName,
                Logger = Logger
              )

              //res
              replies2

            case Failure(exception) =>
              Failure(exception)

          }

      }

      res


    }
      .flatten
  }

  def filterIMAPInbox(folder: IMAPFolder) = {
    val name = Option(folder.getFullName).getOrElse("").trim.toLowerCase()


    if (
      name == "junk" ||
        name.contains("trash") ||
        name.contains("calendar") ||
        name.contains("deleted") ||
        // name.contains("spam") ||
        name.contains("draft")

    ) {

      false

    } else {

      true
    }


  }


  final def getMessageCountFromAllFolders(
                                           store: Store,
                                           inboxEmailSetting: EmailSetting,
                                           Logger: SRLogger
                                         ): Try[Int] = blocking {
    Try {

      val res = _getAllNonEmptyFolders(
        inboxEmailSettingId = inboxEmailSetting.id.get.emailSettingId, // FIXME VALUECLASS
        store = store,
        Logger = Logger
      ) match {

        case Failure(e) =>

          Logger.fatal(s"[TEmailService] getFilteredMessagesFromAllFolders", err = e)

          throw e

        case Success(folders) =>

          val res2: Seq[Try[Int]] = folders
            .filter(filterIMAPInbox)
            .map(f => Try {

              f.open(Folder.READ_ONLY)

              // Logger.info(s"FOLDER opened: ${f.getFullName} :: ${f.getAttributes.toList}")

              val messagesCountInTheFolder = if (inboxEmailSetting.service_provider == EmailServiceProvider.GMAIL_ASP) {
                //As we are not using GmailService, we use SMTP IMAP for Google too
                // but as google sends total email in \All, we were getting the wrong count
                // So we are just getting the count of the folder with \All for google
                if (f.getAttributes.head == """\All""") {
                  getTotalMessageCount(inbox = f, Logger = Logger)
                } else Success(0)
              } else {
                getTotalMessageCount(inbox = f, Logger = Logger)
              }

              closeInbox(inbox = f)

              messagesCountInTheFolder.get
            })

          val replyCountAcrossAllFolders: Try[Seq[Int]] = Helpers.seqTryToTrySeq(res2)

          //.toVector
          replyCountAcrossAllFolders match {
            case Success(replies) =>
              Success(replies.sum)

            case Failure(exception) =>
              Failure(exception)

          }

      }

      res


    }.flatten
  }


  def convertEmailMessageTrackedv4ToEmailMessageTracked(
                                                         emailMessageTrackedV4: EmailMessageTrackedV4):
  EmailMessageTracked = {
    val commonPropsEmailMessage = getCommonPropsEmailMessage(emailMessageTrackedV4)
    val propsTrackedReply = getPropsTrackedReply(emailMessageTrackedV4)

    val (gmail_msg_id: Option[String], gmail_thread_id: Option[String]) =
      emailMessageTrackedV4 match {

        case gmailMessageTrackedV4: GmailMessageTrackedV4 =>
          (Some(gmailMessageTrackedV4.gmailReplyTrackedViaAPI.gmail_msg_id),
            Some(gmailMessageTrackedV4.gmailReplyTrackedViaAPI.gmail_thread_id))

        case _: OutlookMessageTrackedV4 |
             _: ImapTrackedReplyV4 =>
          (None, None)
      }

    val (outlook_msg_id: Option[String], outlook_conversation_id: Option[String],
    outlook_response_json: Option[JsValue]) =
      emailMessageTrackedV4 match {

        case outlookMessageTrackedV4: OutlookMessageTrackedV4 =>
          (Some(outlookMessageTrackedV4.outlookReplyTrackedViaAPI.outlook_msg_id),
            Some(outlookMessageTrackedV4.outlookReplyTrackedViaAPI.outlook_converstation_id),
            Some(outlookMessageTrackedV4.outlookReplyTrackedViaAPI.outlook_response_json)
          )

        case _: GmailMessageTrackedV4 |
             _: ImapTrackedReplyV4 =>
          (None, None, None)
      }


    EmailMessageTracked(
      inbox_email_setting_id = propsTrackedReply.inbox_email_setting_id.emailSettingId, // FIXME VALUECLASS
      from = commonPropsEmailMessage.from,


      // v3 specific start
      to_emails = commonPropsEmailMessage.to_emails, // only for newinbox api
      // v3 specific end


      subject = commonPropsEmailMessage.subject,

      body = commonPropsEmailMessage.body,
      base_body = commonPropsEmailMessage.base_body,
      text_body = commonPropsEmailMessage.text_body,

      references_header = commonPropsEmailMessage.references_header,

      campaign_id = propsTrackedReply.campaign_id.map(_.id), // FIXME VALUECLASS
      step_id = propsTrackedReply.step_id.map(_.id), // FIXME VALUECLASS

      // FIXME VALUECLASS
      prospect_id_in_campaign = propsTrackedReply.prospect_id_in_campaign.map(_.id), // prospect_id for whom campaign should be paused

      // todo - catch this with a unit test - DONE
      prospect_account_id_in_campaign = propsTrackedReply.prospect_account_id_in_campaign.map(_.id), // FIXME VALUECLASS

      campaign_name = propsTrackedReply.campaign_name,
      step_name = propsTrackedReply.step_name,

      received_at = commonPropsEmailMessage.received_at,
      recorded_at = commonPropsEmailMessage.recorded_at,
      sr_inbox_read = propsTrackedReply.sr_inbox_read,
      original_inbox_folder = commonPropsEmailMessage.original_inbox_folder,
      email_status = propsTrackedReply.email_status,

      message_id = commonPropsEmailMessage.message_id,
      full_headers = commonPropsEmailMessage.full_headers,

      scheduled_manually = propsTrackedReply.scheduled_manually,

      reply_to = commonPropsEmailMessage.reply_to,

      email_thread_id = propsTrackedReply.email_thread_id,
      gmail_msg_id = gmail_msg_id,
      gmail_thread_id = gmail_thread_id,

      outlook_msg_id = outlook_msg_id,
      outlook_conversation_id = outlook_conversation_id,
      outlook_response_json = outlook_response_json,

      cc_emails = commonPropsEmailMessage.cc_emails,
      in_reply_to_header = commonPropsEmailMessage.in_reply_to_header,
      team_id = propsTrackedReply.team_id.id, // FIXME VALUECLASS
      account_id = propsTrackedReply.account_id.id, // FIXME VALUECLASS

      internal_tracking_note = propsTrackedReply.internal_tracking_note,

      tempThreadId = propsTrackedReply.tempThreadId // used temporarily for identifying email threads during reply tracking

    )
  }


  // May 2020: making filterReplies a parent holding function to allow safe experimentation (by account-id, team-id etc) of reply tracking
  final def filterReplies(
                           //messages: Vector[EmailReply],
                           messages: Seq[EmailMessageTrackedV4],
                           inboxEmailSetting: EmailSetting,
                           senderEmails: Seq[String],
                           senderMessageIdSufixes: Seq[String],
                           prospectEmailsPastWeek: Seq[EmailScheduledForCheckingReplies],
                           //foundInInboxFolder: Option[String],
                           Logger: SRLogger
                         ): Try[Vector[EmailMessageTracked]] = blocking {

    Logger.info(s"READEMAIL RUNNING V2 Filter replies for :: inbox: ${inboxEmailSetting.email} :: aid_${inboxEmailSetting.owner_id.id} :: tid_${inboxEmailSetting.team_id.id}")


    val result: Try[Vector[EmailMessageTrackedV4]] = replyFilteringService.filterRepliesV4(
      //repliesTrackedViaApi = repliesTrackedViaApi,
      //messages = messages,
      newMsgsWithHeaders = messages,
      inboxEmailSetting = inboxEmailSetting,
      senderEmails = senderEmails,
      senderMessageIdSufixes = senderMessageIdSufixes,
      prospectEmailsPastWeek = prospectEmailsPastWeek,
      //foundInInboxFolder = foundInInboxFolder,
      Logger = Logger.appendLogRequestId("filterRepliesV4")
    )

    /*
    result match {
      case Success(value) =>
        Success(value.map(emailMessageTrackedV4 =>
          convertEmailMessageTrackedv4ToEmailMessageTracked(emailMessageTrackedV4 = emailMessageTrackedV4)))
      case Failure(exception) =>  Failure(exception)
    }

     */

    result.map(msgs => {
      msgs.foreach(msg => {
        val propsTrackedReply = getPropsTrackedReply(msg)
        val commonPropsEmailMessage = getCommonPropsEmailMessage(msg)
        if (propsTrackedReply.tempThreadId.isEmpty) {
          Logger.fatal(s"filterReplies assertion that all new messages have a tempthreadid has failed msgId = message_id ${commonPropsEmailMessage.message_id} , references_header: ${commonPropsEmailMessage.references_header} , in_reply_to_header : ${commonPropsEmailMessage.in_reply_to_header}")
        }
      })
    }

    )

    result.map(value => {
      value.map(emailMessageTrackedV4 =>
        convertEmailMessageTrackedv4ToEmailMessageTracked(emailMessageTrackedV4 = emailMessageTrackedV4))
    })


  }

}

object EmailReplyTrackingService {


  final def _findMatchForReplyHeader(
                                      patternNewSeq: Seq[Regex],
                                      patternOld: Regex,
                                      headerStringToCheck: String

                                    ): Option[Regex.Match] = {

    var messageIdRegexMatch: Option[Regex.Match] = None

    // check for new pattern

    val matchOpt = patternNewSeq.flatMap(patt => patt.findAllMatchIn(headerStringToCheck).toList)

    if (matchOpt.nonEmpty) {

      //val matchOptTempChecking = patternNewTempSeq.flatMap(patt => patt.findAllMatchIn(headerStringToCheck).toList)

      //Logger.info(s"[TEmailService] message id match: $matchOpt :: new patter: $matchOptTempChecking :: goddamn reverse: ${matchOpt.reverse} :: ${matchOptTempChecking.reverse}")

      messageIdRegexMatch = Some(matchOpt.last)
    }


    // if no match found for new pattern, check for old pattern

    if (messageIdRegexMatch.isEmpty) {

      val matchOpt = patternOld.findAllMatchIn(headerStringToCheck).toList

      //          Logger.info(s"[TEmailService] receiveEmail emailSettingId ($emailSettingId): Header: $header")
      if (matchOpt.nonEmpty) {
        messageIdRegexMatch = Some(matchOpt.last)
      }

    }

    messageIdRegexMatch
  }


  final def __foundEmailThreadIdv4(
                                    baseEmail: EmailMessageTrackedV4,
                                    prospect: EmailScheduledForCheckingReplies,
                                    Logger: SRLogger
                                  ): Option[Long] = {

    val commonPropsEmailMessage = getCommonPropsEmailMessage(baseEmail)
    val propsTrackedReply = getPropsTrackedReply(baseEmail)

    if (propsTrackedReply.email_thread_id.isDefined) {

      Logger.info(s"__foundEmailThreadIdv4 replyMatchFound : match found via baseEmail.email_thread_id itself :: mid: ${commonPropsEmailMessage.message_id} ")

      propsTrackedReply.email_thread_id

    } else if (prospect.email_thread_id.isDefined &&
      (
        (
          getGmailThreadId(baseEmail).isDefined &&
            prospect.gmail_thread_id.isDefined &&
            getGmailThreadId(baseEmail).get == prospect.gmail_thread_id.get
          )

          || (
          getOutlookConversationId(baseEmail).isDefined &&
            prospect.outlook_conversation_id.isDefined &&
            getOutlookConversationId(baseEmail).get == prospect.outlook_conversation_id.get
          )

        )) {

      Logger.info(s"__foundEmailThreadIdv4 replyMatchFound : gmail/outlook: match found via prospect.email_thread_id :: baseEmail.email_thread_id was empty :: mid: ${commonPropsEmailMessage.message_id} ")

      prospect.email_thread_id

    } else if (prospect.email_thread_id.isDefined &&

      // fixme inboxv3: even if its a different inbox
      //  (e.g. when user uses different sending / receiving email accounts),
      //  it will still retain the email_thread_id (which is not correct).
      //  Need to compare here the inbox_email_setting_ids as well for the prospect's previous email and reply's inbox
      Helpers.subjectsMatching(sub1 = commonPropsEmailMessage.subject,
        sub2 = prospect.subject.getOrElse(""))) {

      Logger.info(s"__foundEmailThreadIdv4 replyMatchFound : match found via subject :: baseEmail.email_thread_id was empty :: mid: ${commonPropsEmailMessage.message_id}")

      prospect.email_thread_id

    } else {

      None

    }

  }

  def getCountsMoreThan1(emailMessages: ListBuffer[EmailMessageTrackedV4]): Map[Int, Int] = {
    /*
    val temp_id_map = scala.collection.mutable.HashMap[Int, Int]()
    emailMessages.foreach(em => {
      val propsTrackedReply = getPropsTrackedReply(em)

      propsTrackedReply.tempThreadId match {

        case  Some(t) =>

          if (temp_id_map.contains(t)) {
            val rhs = temp_id_map(t)
            temp_id_map(t) = rhs + 1
          } else {
            temp_id_map(t) = 1
          }
        case None =>
          if (temp_id_map.contains(-1)) {
            val rhs = temp_id_map(-1)
            temp_id_map(-1) = rhs + 1
          } else {
            temp_id_map(-1) = 1
          }

      }
    })
    val temp_id_map2 =  scala.collection.mutable.HashMap[Int, Int]()
    temp_id_map.foreach {
      case (k, v) =>
        if (v > 1)  {
          temp_id_map2(k) = v
        }
    }
    temp_id_map2

     */
    val res = emailMessages
      .map(em => {
        val propsTrackedReply = getPropsTrackedReply(em)
        val res = propsTrackedReply.tempThreadId match {
          case Some(thrid) => (thrid, em)
          case None => (-1, em)
        }
        res
      })
      .groupBy(v => v._1)
      .map(v => (v._1, v._2.length))
      .filter(v => v._2 > 1)


    //.groupBy(_ => _._1)
    res
  }

  final def __assignTempThreadIdsForTrackedRepliesV4(

                                                      allMsgsWithHeaders: Seq[EmailMessageTrackedV4],
                                                      Logger: SRLogger

                                                    ): ListBuffer[EmailMessageTrackedV4] = {
    var highest_tempThrdIdCounter = 0
    // assign temporary email thread ids
    // group messages into threads

    // https://stackoverflow.com/a/14443160
    val msgs: ListBuffer[EmailMessageTrackedV4] = ListBuffer.empty ++= allMsgsWithHeaders.toList

    val totalMsgs = msgs.size

    for (i <- 0 until totalMsgs) {
      val tempThrdIdCounter = i

      // println(s"i :: $i\n\n")

      val checkMsg = msgs(i)


      val (checkMsg_gmail_thread_id: Option[String],
      checkMsg_commonMsg: CommonPropsEmailMessage,
      checkMsg_propsTrackedReply: PropsTrackedReply) = (
        getGmailThreadId(checkMsg),
        getCommonPropsEmailMessage(checkMsg),
        getPropsTrackedReply(checkMsg)
      )

      if (checkMsg_propsTrackedReply.tempThreadId.isEmpty) {

        for (j <- 0 until totalMsgs) {

          val checkAgainstMessage = msgs(j)

          val (checkAgainst_gmail_thread_id: Option[String],
          checkAgainstCommonMsg: CommonPropsEmailMessage,
          checkAgainstPropsTrackedReply: PropsTrackedReply) = (
            getGmailThreadId(checkAgainstMessage),
            getCommonPropsEmailMessage(checkAgainstMessage),
            getPropsTrackedReply(checkAgainstMessage)
          )
          // ========

          // CHECKME: why not check for outlook_conversation_ids as well while matching threads

          // 1. check with gmailThreadId
          val matchingGmailThreadId = checkMsg_gmail_thread_id.isDefined &&
            checkAgainst_gmail_thread_id.nonEmpty &&
            checkMsg_gmail_thread_id.get == checkAgainst_gmail_thread_id.get

          // 2. check with in-reply-to header
          val matchingInReplyToHeader = checkAgainstCommonMsg.message_id.nonEmpty &&
            checkMsg_commonMsg.in_reply_to_header.isDefined &&
            checkAgainstCommonMsg.message_id == checkMsg_commonMsg.in_reply_to_header.get

          // 3. check with references header
          val matchingReferencesHeader = checkAgainstCommonMsg.message_id.nonEmpty &&
            checkMsg_commonMsg.references_header.isDefined &&
            checkMsg_commonMsg.references_header.get.contains(checkAgainstCommonMsg.message_id)


          if (matchingGmailThreadId || matchingInReplyToHeader || matchingReferencesHeader) {

            if (checkAgainstPropsTrackedReply.tempThreadId.isDefined) {

              // println(s"$i :: $j :: isDefined")

              /*
              msgs(i) = commonMsg.copy(
                 tempThreadId = checkAgainstMessage.tempThreadId
              )

               */
              msgs(i) = EmailMessageTrackedV4.copyTempThreadIdToMessage(
                // todo nxd : repro with Unit Test case -
                //     earlier code was wrong  - was this
                //     checkAgainstMessage instead of  checkMsg
                msg = checkMsg,
                tempThreadId = checkAgainstPropsTrackedReply.tempThreadId.get)
              if (i != j) {
                if (matchingGmailThreadId) {
                  Logger.info(s"__assignTempThreadIdsForTrackedRepliesV4 INBOXDEBUG 1a - matchingGmailThreadId - ${checkAgainst_gmail_thread_id} - tempThreadId : ${checkAgainstPropsTrackedReply.tempThreadId} msg_id: ${checkMsg_commonMsg.message_id} msg_against_id: ${checkAgainstCommonMsg.message_id}")
                }
                if (matchingInReplyToHeader) {
                  Logger.info(s"__assignTempThreadIdsForTrackedRepliesV4 INBOXDEBUG 1a - matchingInReplyToHeader - ${checkMsg_commonMsg.in_reply_to_header} - tempThreadId : ${checkAgainstPropsTrackedReply.tempThreadId}  msg_id: ${checkMsg_commonMsg.message_id} msg_against_id: ${checkAgainstCommonMsg.message_id}")
                }
                if (matchingReferencesHeader) {
                  Logger.info(s"__assignTempThreadIdsForTrackedRepliesV4 INBOXDEBUG 1a - matchingReferencesHeader - ${checkMsg_commonMsg.references_header} - tempThreadId : ${checkAgainstPropsTrackedReply.tempThreadId}  msg_id: ${checkMsg_commonMsg.message_id} msg_against_id: ${checkAgainstCommonMsg.message_id}")
                }

              }

            } else {

              //              msgs(i) = checkMsg.copy(
              //                tempThreadId = Some(tempThrdIdCounter)
              //              )

              msgs(i) = EmailMessageTrackedV4.copyTempThreadIdToMessage(
                msg = checkMsg,
                tempThreadId = tempThrdIdCounter)

              //              msgs(j) = checkAgainstMessage.copy(
              //                tempThreadId = Some(tempThrdIdCounter)
              //              )

              msgs(j) = EmailMessageTrackedV4.copyTempThreadIdToMessage(
                msg = checkAgainstMessage,
                tempThreadId = tempThrdIdCounter)

              if (i != j) {
                if (matchingGmailThreadId) {
                  Logger.info(s"__assignTempThreadIdsForTrackedRepliesV4 INBOXDEBUG 2a - matchingGmailThreadId - ${checkAgainst_gmail_thread_id} - tempThreadId : ${checkAgainstPropsTrackedReply.tempThreadId}  msg_id: ${checkMsg_commonMsg.message_id} msg_against_id: ${checkAgainstCommonMsg.message_id}")
                }
                if (matchingInReplyToHeader) {
                  Logger.info(s"__assignTempThreadIdsForTrackedRepliesV4 INBOXDEBUG 2a - matchingInReplyToHeader - ${checkMsg_commonMsg.in_reply_to_header} - tempThreadId : ${checkAgainstPropsTrackedReply.tempThreadId}  msg_id: ${checkMsg_commonMsg.message_id} msg_against_id: ${checkAgainstCommonMsg.message_id}")
                }
                if (matchingReferencesHeader) {
                  Logger.info(s"__assignTempThreadIdsForTrackedRepliesV4 INBOXDEBUG 2a - matchingReferencesHeader - ${checkMsg_commonMsg.references_header} - tempThreadId : ${checkAgainstPropsTrackedReply.tempThreadId}  msg_id: ${checkMsg_commonMsg.message_id} msg_against_id: ${checkAgainstCommonMsg.message_id}")
                }

              }

              // msgs = msgs.updated(j, msgs(j).copy(tempThreadId = Some(tempThrdIdCounter)))

              // println(s"$i :: $j :: isEmpty")


            }

          } else {

            // if it doesnt match anything
            //            msgs(i) = checkMsg.copy(
            //              tempThreadId = Some(tempThrdIdCounter)
            //            )

            msgs(i) = EmailMessageTrackedV4.copyTempThreadIdToMessage(
              msg = checkMsg,
              tempThreadId = tempThrdIdCounter)

          }


        }
      }

      highest_tempThrdIdCounter = tempThrdIdCounter

    }

    msgs.foreach(msg => {
      val propsTrackedReply = getPropsTrackedReply(msg)
      val commonPropsEmailMessage = getCommonPropsEmailMessage(msg)
      if (propsTrackedReply.tempThreadId.isEmpty) {
        Logger.fatal(s"__assignTempThreadIdsForTrackedRepliesV4 INBOXDEBUG assertion that all new messages have a tempthreadid has failed msgId = message_id ${commonPropsEmailMessage.message_id} , references_header: ${commonPropsEmailMessage.references_header} , in_reply_to_header : ${commonPropsEmailMessage.in_reply_to_header}")
      }
    })

    //val temp_id_map_1=  getCountsMoreThan1( emailMessages = msgs)
    //Logger.info(s"__assignTempThreadIdsForTrackedRepliesV4 INBOXDEBUG highest_tempThrdIdCounter : ${highest_tempThrdIdCounter}, temp_id_map_1 = ${temp_id_map_1} ")
    msgs
  }


  final def __getNewImapMsgsWithHeadersV4(
                                           messageObj: (String, EmailReply.ImapTrackedReply),
                                           inboxEmailSettingId: Long,
                                           senderEmails: Seq[String],
                                           foundInInboxFolder: Option[String],
                                           teamId: Long,
                                           accountId: Long,
                                           
                                         )(
    using Logger: SRLogger
  ): Option[EmailMessageTrackedV4.ImapTrackedReplyV4] = {


    //val newFullImapMsgs: EmailMessageTracked = newMsgsIntermediateWithHeadersAndTempThreadId
    //        .map(messageObj => {

    val message = messageObj._2.imapMsg
    val messageID = messageObj._1

    val fromEmailOpt: Option[IEmailAddress] = IEmailAddress.getFromEmailFromJavaMessage(
      message = message,
      Logger = Logger
    )

    val result = if (fromEmailOpt.isEmpty) {
      None
    } else {


      val from: IEmailAddress = fromEmailOpt.get

      val referencesHeader: Option[String] = EmailReplyTrackingCommonService._getMessageHeader(message = message, headerName = "References",
        Logger = Logger
      )


      val inReplyToHeader: Option[String] = EmailReplyTrackingCommonService._getMessageHeader(message = message, headerName = "In-Reply-To",
        Logger = Logger
      )



      // if gmail, get message and thread ids

      var gmailMsgId: Option[String] = None
      var gmailThrdId: Option[String] = None

      message match {
        case message1: GmailMessage =>

          gmailMsgId = Try {
            Option(message1.getMsgId).map(_.toString)
          }.getOrElse(None)


          gmailThrdId = Try {
            Option(message1.getThrId).map(_.toString)
          }.getOrElse(None)

        case _ =>
      }


      val receivedDate: DateTime = Try {

        val rd = message.getReceivedDate

        if (rd == null) {

          Logger.error(s"[TEmailService] getReceivedDate :: ${from.email}: getReceivedDate: $rd: is null")

          DateTime.now()

        } else {
          new DateTime(rd)
        }

      } match {
        case Failure(e) =>
          Logger.error(s"[TEmailService] getReceivedDate :: ${from.email}: getReceivedDate", err = e)
          DateTime.now()

        case Success(date) => date

      }


      val subject: String = Try {
        message.getSubject
      } match {
        case Failure(e) =>
          Logger.error(s"[TEmailService] error :: fromEmail: ${from.email} getSubject", err = e)

          ""

        case Success(value) => if (value == null) "" else value
      }


      val (mimeType, htmlBodyRaw) = EmailReplyTrackingCommonService.getTextFromMessage(
        message = message,
        Logger = Logger
      )

      /* was testing commons-email mimeparser

        val parser = new MimeMessageParser(message.asInstanceOf[MimeMessage])
        parser.parse()

        val htmlContent = parser.getHtmlContent
        // or
        val plainContent = parser.getPlainContent

        Logger.info(s"\n\n\n\n body: $htmlBodyRaw \n\n\n htmlc: $htmlContent \n\n plainC: $plainContent \n\n\n")


        */

      val emailBodyFormats = EmailServiceTempCommon.getBaseBodyAndTextBody(
        bodyRaw = htmlBodyRaw,
        mimeType = mimeType,
        Logger = Logger
      )

      val fullHeaders = EmailReplyTrackingCommonService.getAllHeaders(m = message) match {
        case Failure(e) =>

          e match {
            case e: javax.mail.MessageRemovedException =>
              Logger.error(s"[TEmailService] :: ${from.email}: fullHeaders", err = e)

            case _ =>
              Logger.fatal(s"[TEmailService] :: ${from.email}: fullHeaders", err = e)

          }

          Map[String, String]()

        case Success(hrs) => hrs
      }


      val byAccount = senderEmails.contains(from.email.trim.toLowerCase)


      /*
      val autoGeneratedReply: Boolean = (fullHeaders.getOrElse("auto-submitted", "").toLowerCase == "auto-generated") ||
        (fullHeaders.getOrElse("Auto-Submitted", "").toLowerCase == "auto-generated") ||
        (fullHeaders.getOrElse("Auto-Submitted", "").toLowerCase == "auto-replied") ||
        (fullHeaders.getOrElse("X-Autoreply", "").toLowerCase == "yes")
      */


      // for newinbox api
      val toEmails: Seq[IEmailAddress] = IEmailAddress.getToEmailsfromJavaMessage(
        message = message,
        Logger = Logger
      )

      val replyToEmailAddress: Option[IEmailAddress] = IEmailAddress.getReplyToEmailFromJavaMessage(
        message = message,
        Logger = Logger
      )

      val ccEmailAddresses: Seq[IEmailAddress] = IEmailAddress.getCCEmailsfromJavaMessage(
        message = message,
        Logger = Logger
      )

      /*
      val toCCEmails: Option[String] = if (ccEmailAddresses.isEmpty) None else {
        Some(IEmailAddress.stringify(emails = ccEmailAddresses))
      }
      */

      val full_headers = Json.toJson(fullHeaders)

      val emailStatus = EmailReplyStatus.getEmailReplyStatusBeforeSaving(
        emailBaseBody = emailBodyFormats.baseBody,
        subject = subject,
        fromEmail = from.email,
        fromName = from.name.getOrElse(""),
        toEmailAddresses = toEmails.map(_.email),
        byAccount = byAccount,
        fullHeaders = full_headers,
        received_at = receivedDate
      )

      val imap_reply = ImapParsedReply(
        commonPropsEmailMessage = CommonPropsEmailMessage(
          from = from,
          // for newinbox api. to_email, to_name are for oldinbox api
          to_emails = toEmails,
          cc_emails = ccEmailAddresses,
          reply_to = replyToEmailAddress,
          subject = Helpers.sanitiseStringForDB(subject),
          body = emailBodyFormats.htmlBody,
          base_body = emailBodyFormats.baseBody,
          text_body = emailBodyFormats.textBody,
          message_id = messageID,
          full_headers = full_headers,
          in_reply_to_header = inReplyToHeader,
          references_header = referencesHeader,
          received_at = receivedDate,
          recorded_at = DateTime.now,
          original_inbox_folder = foundInInboxFolder,
          autogeneratedReplyFromHeader = false
        )
      )

      val baseEmail = EmailMessageTrackedV4.ImapTrackedReplyV4(
        imapParsedReply = imap_reply,
        propsTrackedReply = PropsTrackedReply(
          inbox_email_setting_id = EmailSettingId(emailSettingId = inboxEmailSettingId), // FIXME VALUECLASS

          campaign_id = None,
          step_id = None,

          prospect_id_in_campaign = None,

          // NOTE: this will be populated after checking validProspects
          // inside EmailScheduled.saveRepliesDeleteOtherScheduledEmailsAndUpdateRepliedStatusV2
          prospect_account_id_in_campaign = None,

          // these are set only in the save SQL function below
          campaign_name = None,
          step_name = None,
          sr_inbox_read = byAccount,

          email_status = emailStatus,


          scheduled_manually = byAccount,

          email_thread_id = None,

          team_id = TeamId(id = teamId), // FIXME VALUECLASS
          account_id = AccountId(id = accountId), // FIXME VALUECLASS

          internal_tracking_note = InternalTrackingNote.NONE,
          // will be assigned next
          tempThreadId = None
        )


      )

      Some(baseEmail)

    }


    result
    //            })
    //            .filter(_.isDefined)
    //            .map(_.get)

    //newFullImapMsgs
  }


  case class EmailThreadMatchedResult(
                                       emailThreadMatched: EmailThreadFoundForCheckingReplies,
                                       internalTrackingNote: InternalTrackingNote
                                     )

  def checkViaGmailOrOutlookIds(mesg: EmailMessageTrackedV4,
                                existingEmailThreads: Seq[EmailThreadFoundForCheckingReplies])
  : Option[EmailThreadMatchedResult]
  = {
    mesg match {
      // 1. check by gmailThreadId in the emails_scheduled table
      case gmailMessageTrackedV4: GmailMessageTrackedV4 =>
        val foundEmailThread = existingEmailThreads.find(t =>
          t.gmail_thread_id.isDefined &&
            t.gmail_thread_id.get == gmailMessageTrackedV4.gmailReplyTrackedViaAPI.gmail_thread_id)
        if (foundEmailThread.isDefined) {
          Some(EmailThreadMatchedResult(
            emailThreadMatched = foundEmailThread.get,
            internalTrackingNote = InternalTrackingNote.EXISTING_GMAILTHREADID_IN_EMAILSSCHEDULED_TABLE))
        } else {
          None
        }

      // 2. check by outlook_conversation_id in the emails_scheduled table
      case outlookMessageTrackedV4: OutlookMessageTrackedV4 =>
        val foundEmailThread = existingEmailThreads.find(t =>
          t.outlook_conversation_id.isDefined &&
            t.outlook_conversation_id.get ==
              outlookMessageTrackedV4
                .outlookReplyTrackedViaAPI.outlook_converstation_id
        )
        if (foundEmailThread.isDefined) {
          Some(EmailThreadMatchedResult(
            emailThreadMatched = foundEmailThread.get,
            internalTrackingNote = InternalTrackingNote.EXISTING_OUTLOOKCONVERSATIONID_IN_EMAILSSCHEDULED_TABLE))
        } else {
          None
        }
      case _: ImapTrackedReplyV4 => // ignore
        None
    }
  }


  // 3. check by reply to header in the emails_scheduled table
  def checkByReplyToHeader(
                            in_reply_to_header: String,
                            existingEmailThreads: Seq[EmailThreadFoundForCheckingReplies])
  : Option[EmailThreadMatchedResult]
  = {
    val foundEmailThread = existingEmailThreads.find(t =>
      t.message_id.isDefined && t.message_id.get == in_reply_to_header)

    if (foundEmailThread.isDefined) {
      Some(EmailThreadMatchedResult(
        emailThreadMatched = foundEmailThread.get,
        internalTrackingNote = InternalTrackingNote.EXISTING_INREPLYTO))
    } else {
      None
    }

  }

  // 4. check by references header in the emails_scheduled table
  def checkByReferencesHeader(
                               references_header: String,
                               existingEmailThreads: Seq[EmailThreadFoundForCheckingReplies])
  : Option[EmailThreadMatchedResult]
  = {
    val foundEmailThread = existingEmailThreads.find(t =>
      t.message_id.isDefined && references_header.contains(t.message_id.get))

    if (foundEmailThread.isDefined) {
      Some(EmailThreadMatchedResult(
        emailThreadMatched = foundEmailThread.get,
        internalTrackingNote = InternalTrackingNote.EXISTING_REFERENCES
      ))
    } else {
      None
    }
  }

  // 5. and 6.  check by gmail_thread_id or outlook_conversation_id in the email_threads table
  def checkInEmailThreadsTable(
                                mesg: EmailMessageTrackedV4,
                                existingGmailAndOutlookThreads: Seq[EmailThreadFoundForCheckingReplies]
                              ): Option[EmailThreadMatchedResult] = {
    mesg match {

      // 5. check by gmail_thread_id in the email_threads table

      case gmailMessageTrackedV4: GmailMessageTrackedV4 =>
        val foundEmailThread = existingGmailAndOutlookThreads.find(t =>
          t.gmail_thread_id.isDefined && t.gmail_thread_id.get ==
            gmailMessageTrackedV4.gmailReplyTrackedViaAPI.gmail_thread_id)
        if (foundEmailThread.isDefined) {
          Some(EmailThreadMatchedResult(
            emailThreadMatched = foundEmailThread.get,
            // TODO - trap this with a Unit test case as well
            // TODO - give demo on 27-dec-2021 here
            internalTrackingNote = InternalTrackingNote.EXISTING_GMAILTHREAD_IN_EMAILTHREAD_TABLE
          ))
        } else {
          None
        }

      // 6. check by outlook_conversation_id in the email_threads table
      case outlookMessageTrackedV4: OutlookMessageTrackedV4 =>
        val foundEmailThread = existingGmailAndOutlookThreads.find(t =>
          t.outlook_conversation_id.isDefined && t.outlook_conversation_id.get ==
            outlookMessageTrackedV4.outlookReplyTrackedViaAPI.outlook_converstation_id)
        if (foundEmailThread.isDefined) {
          Some(EmailThreadMatchedResult(
            emailThreadMatched = foundEmailThread.get,
            internalTrackingNote = InternalTrackingNote.EXISTING_OUTLOOKCONVERSATION_IN_EMAILTHREAD_TABLE
          ))
        } else {
          None
        }

      case ImapTrackedReplyV4(imapParsedReply, propsTrackedReply) =>
        // ignore
        None
    }
  }


  // assign emailThreadId for existingEmailThreads, that are already saved in db
  final def __assignEmailThreadIdsForExistingEmailThreadsV4(

                                                             newMsgsWithHeadersAndTempThreadId: Seq[EmailMessageTrackedV4],
                                                             existingEmailThreads: Seq[EmailThreadFoundForCheckingReplies],
                                                             existingGmailAndOutlookThreads: Seq[EmailThreadFoundForCheckingReplies],
                                                             Logger: SRLogger

                                                           ): ListBuffer[EmailMessageTrackedV4] = {

    // https://stackoverflow.com/a/14443160
    val newMsgsWithEmailThreadId: ListBuffer[EmailMessageTrackedV4] = ListBuffer.empty ++= newMsgsWithHeadersAndTempThreadId.toList

    val totalNewMsgsWithTempThreadId = newMsgsWithEmailThreadId.size
    var n_tempThreadID_assignments = 0
    for {i <- 0 until totalNewMsgsWithTempThreadId} {

      val mesg: EmailMessageTrackedV4 = newMsgsWithEmailThreadId(i)

      var emailThreadMatched: Option[EmailThreadFoundForCheckingReplies] = None

      var internalTrackingNote: InternalTrackingNote.Value = InternalTrackingNote.NONE

      // ================== BEGIN check by gmailThreadId or
      // ================== BEGIN outlook_conversation_id in emails_scheduled
      /*
       * Note to PR reviewer - moved to checkViaGmailOrOutlookIds above
       */
      //(emailThreadMatched, internalTrackingNote)
      // 1. check by gmailThreadId in the emails_scheduled table
      // 2. check by outlook_conversation_id in the emails_scheduled table
      checkViaGmailOrOutlookIds(
        mesg = mesg,
        existingEmailThreads = existingEmailThreads
      ) match {
        case Some(matchedResult) =>
          emailThreadMatched = Some(matchedResult.emailThreadMatched)
          internalTrackingNote = matchedResult.internalTrackingNote
        case None =>
        // ignore: the above vars are already None
      }

      // ================== END check by gmailThreadId or
      // ================== END outlook_conversation_id in emails_scheduled


      // ================== BEGIN check by reply to header in the emails_scheduled table

      // 3. check by reply to header in the emails_scheduled table

      val commonPropsEmailMessage = getCommonPropsEmailMessage(mesg)

      if (emailThreadMatched.isEmpty && commonPropsEmailMessage.in_reply_to_header.isDefined) {
        val in_reply_to_header = commonPropsEmailMessage.in_reply_to_header.get

        /*
         * Note to PR reviewer - moved to checkByReplyToHeader above
         */

        checkByReplyToHeader(
          in_reply_to_header = in_reply_to_header,
          existingEmailThreads = existingEmailThreads
        ) match {
          case Some(matchedResult) =>
            emailThreadMatched = Some(matchedResult.emailThreadMatched)
            internalTrackingNote = matchedResult.internalTrackingNote
          case None =>
          // ignore: the above vars are already None
        }


        // ================== END check by reply to header in the emails_scheduled table
      }

      // 4. check by references header in the emails_scheduled table

      // ================== BEGIN check by references header in the emails_scheduled table
      if (emailThreadMatched.isEmpty && commonPropsEmailMessage.references_header.isDefined) {
        val references_header = commonPropsEmailMessage.references_header.get

        /*
         * Note to PR reviewer - moved to checkByReferencesHeader above
         */
        checkByReferencesHeader(
          references_header = references_header,
          existingEmailThreads = existingEmailThreads
        ) match {
          case Some(matchedResult) =>
            emailThreadMatched = Some(matchedResult.emailThreadMatched)
            internalTrackingNote = matchedResult.internalTrackingNote
          case None =>
          // ignore: the above vars are already None

        }

        // ================== END check by references header in the emails_scheduled table
      }



      /*
       * Note to PR reviewer - moved to checkInEmailThreadsTable above
      */

      // 5. and 6. from above combined
      // 5. check by gmail_thread_id in the email_threads table
      // 6. check by outlook_conversation_id in the email_threads table
      if (emailThreadMatched.isEmpty) {
        // BEGIN ============== 5. and 6.
        // ================== BEGIN check in the email_threads table

        checkInEmailThreadsTable(
          mesg = mesg,
          existingGmailAndOutlookThreads = existingGmailAndOutlookThreads
        ) match {
          case Some(matchedResult) =>
            emailThreadMatched = Some(matchedResult.emailThreadMatched)
            internalTrackingNote = matchedResult.internalTrackingNote
          case None =>
          // ignore: the above vars are already None

        }


        // ================== END check in the email_threads table
      }

      if (emailThreadMatched.isDefined) {

        // loop over messages
        // -- find messages with same tempThreadId and empty emailThreadId
        // -- update emailThreadId related fields for all of them
        // should be a ListBuffer

        val propsTrackedReply = getPropsTrackedReply(mesg)
        val messageTempThreadId = propsTrackedReply.tempThreadId

        for {j <- 0 until totalNewMsgsWithTempThreadId} {

          val checkAgainstMsg: EmailMessageTrackedV4 = newMsgsWithEmailThreadId(j)
          val checkAgainstMsgCommmonProps = getCommonPropsEmailMessage(checkAgainstMsg)
          val checkAgainstMsg_propsTrackedReply = getPropsTrackedReply(checkAgainstMsg)


          val checkAgainstMsg_email_thread_id = checkAgainstMsg_propsTrackedReply.email_thread_id

          if (
            checkAgainstMsg_propsTrackedReply.tempThreadId.get == messageTempThreadId.get &&
              checkAgainstMsg_email_thread_id.isEmpty
          ) {

            Logger.info(s"__assignEmailThreadIdsForExistingEmailThreadsV4 replyMatchFound :: internalTrackingNote: $internalTrackingNote :: found email_thread_id: ${emailThreadMatched.map(_.email_thread_id)} :: mid: ${checkAgainstMsgCommmonProps.message_id}")

            // fixme: newinbox in emailThreadMatched case, prospect may still be a new only so prospect_id needs to be ignored
            val prev_temp_thread_id = checkAgainstMsg_propsTrackedReply.tempThreadId


            val propsTrackedReplyNew: PropsTrackedReply = checkAgainstMsg_propsTrackedReply.copy(
              email_thread_id = emailThreadMatched.map(_.email_thread_id),
              prospect_id_in_campaign = emailThreadMatched.flatMap(_.primary_prospect_id).map(p => ProspectId(id = p)), // FIXME VALUECLASS
              prospect_account_id_in_campaign = emailThreadMatched.flatMap(_.primary_prospect_account_id).map(p => ProspectAccountsId(id = p)), // FIXME VALUECLASS
              step_id = emailThreadMatched.flatMap(_.step_id).map(s => StepId(id = s)), // FIXME VALUECLASS
              campaign_id = emailThreadMatched.flatMap(_.campaign_id).map(c => CampaignId(id = c)), // FIXME VALUECLASS
              internal_tracking_note = internalTrackingNote
              // note implicit copy of tempThreadId is happening here
            )
            val new_temp_thread_id_if_changed = propsTrackedReplyNew.tempThreadId
            Logger.info(s"__assignEmailThreadIdsForExistingEmailThreadsV4 INBOXDEBUG prev_temp_thread_id :  ${prev_temp_thread_id}, new_temp_thread_id_if_changed = ${new_temp_thread_id_if_changed} ")

            newMsgsWithEmailThreadId(j) = EmailMessageTrackedV4.updatePropsTrackedReply(
              baseEmail = newMsgsWithEmailThreadId(j),
              propsTrackedReplyNew = propsTrackedReplyNew
            )
            n_tempThreadID_assignments = n_tempThreadID_assignments + 1
          }


        }

      }

    }

    // we started out with a set of what we thought were new threads
    // but we checked in the db for their ids/gmailids/outlookids etc
    // and those which we found we marked their emailThreadIds
    // here our assertion should still hold - that all the threads should have a tempThreadId
    // although now some of those threads may have a emailthreadid because they were found in the db
    var highest_tempThreadId: Int = 0
    newMsgsWithEmailThreadId.foreach(msg => {
      val propsTrackedReply = getPropsTrackedReply(msg)
      val commonPropsEmailMessage = getCommonPropsEmailMessage(msg)
      if (propsTrackedReply.tempThreadId.isEmpty) {
        Logger.fatal(s"__assignEmailThreadIdsForExistingEmailThreadsV4 INBOXDEBUG : tempThreadId is empty  message_id ${commonPropsEmailMessage.message_id} , references_header: ${commonPropsEmailMessage.references_header} , in_reply_to_header : ${commonPropsEmailMessage.in_reply_to_header}")
      } else {
        val tmpid = getPropsTrackedReply(msg).tempThreadId.get
        if (tmpid > highest_tempThreadId) highest_tempThreadId = tmpid
      }
    })
    val temp_id_map_1 = getCountsMoreThan1(emailMessages = newMsgsWithEmailThreadId)
    Logger.info(s"__assignEmailThreadIdsForExistingEmailThreadsV4 INBOXDEBUG n_tempThreadID_assignments =  ${n_tempThreadID_assignments} , highest_tempThreadId : ${highest_tempThreadId}, temp_id_map_1 = ${temp_id_map_1}")
    newMsgsWithEmailThreadId
  }


  final def getEmailAfterDate(
                               emailSetting: EmailSetting,
                               Logger: SRLogger
                             ): DateTime = {

    val maxTrackSince = DateTime.now().minusDays(1)

    emailSetting.last_read_for_replies match {
      case Some(date) =>

        if (date.minusMinutes(5).isBefore(maxTrackSince)) {
          maxTrackSince
        } else {
          date.minusMinutes(5)
        }


      case None =>
        emailSetting.created_at match {
          case Some(date) =>

            if (date.minusMinutes(15).isBefore(maxTrackSince)) {
              maxTrackSince
            } else {
              date.minusMinutes(15)
            }

          case None =>

            Logger.fatal(s"[TEmailService] getEmailAfterDate emailSetting.created_at is NULL")

            DateTime.now().minusMinutes(15)
        }
    }
  }

}
