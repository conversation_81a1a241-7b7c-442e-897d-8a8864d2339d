package utils.email

import java.util.Date
import api.AppConfig
import api.accounts.TeamId
import api.accounts.models.{AccountId, OrgId}
import api.calendar_app.models.CalendarAccountData
import api.campaigns.services.CampaignId
import api.prospects.models.{ProspectId, StepId}
import org.jsoup.Jsoup
import play.api.Logging
import utils.{<PERSON><PERSON>, ParseUtils, SRLogger, StringUtils}
import utils.security.{EncryptionHelpers, EncryptionService}

import scala.util.{Failure, Success, Try}
import scala.util.matching.Regex
import org.joda.time.DateTime
import sr_scheduler.models.{CalendarTeam, SelectedCalendarData}
import utils.emailvalidation.EmailValidationService
import utils.helpers.LogHelpers

import scala.jdk.CollectionConverters._
import utils.sr_json_utils.CalendarSettingsDataUtil

// removed on ******** as no usage
//case class DecryptedUnsubLink(campaignId: Long, prospectId: Long, stepId: Long)
case class DecryptedEmailNotificationUnsubLink(
                                                accountId: AccountId,
                                                orgId: OrgId
                                              )

case class DecryptedUnsubLinkV2(
                                 emailScheduledId: Long
                               )

case class CalendarLinkData(
                             isIndividualCalendar:Boolean,
                             calendarUsernameSlug:Option[String],
                             calendarTeam: Option[CalendarTeam],
                             selectedEventTypeSlug: String
                           )


object EmailHelper extends Logging {


  def convertNewlinesToBrTags(str : String): String = {

    val res: String = str.replaceAll("\n", "<br>")

    res

  }


  def getOpenTrackingLink_V2(
                           emailScheduledId: Long,
                           customDomain: Option[String],
                           defaultTrackingDomain: String
                         ): String = {

    val code = StringUtils.base32EncodeURIString(s"$emailScheduledId").toLowerCase()

    logger.info(s"OPEN TRACKING LINK: $code :: emailScheduledId => $emailScheduledId :: customDomain => $customDomain")

    //    s"${AppConfig.applicationDomain}/assets/get_image.png?id=$code"

    val trackingDomain = if (customDomain.isDefined) s"https://${customDomain.get}" else s"https://$defaultTrackingDomain"

    s"$trackingDomain/ot2/$code/open"
  }

  val ctDelimiter = "___"


  def genClickTrackingUrl_V3(
                           emailScheduledId: Long,
                           url: String,
                           customDomain: Option[String],
                           defaultTrackingDomain: String
                         ): String = {

    val baseStr = StringUtils.base32EncodeURIString(s"$emailScheduledId$ctDelimiter$url").toLowerCase()
    val trackingDomain = if (customDomain.isDefined) s"https://${customDomain.get}" else s"https://$defaultTrackingDomain"

    s"$trackingDomain/ct3/$baseStr/click"


  }


  // new format
  def _deconstructClickTrackingUrlBase32_V2(code: String): Try[(String, Long)] = Try {
    val decoded = StringUtils.base32DecodeURIString(code)


    val splits = decoded.split(ctDelimiter, 2).toVector

    val emailScheduledId = ParseUtils.parseLong(splits(0))
    val decodedUrl = splits(1)

    (decodedUrl, emailScheduledId)


  } match {
    case Success(value) => value._2 match {
      case None => Failure(new Throwable(s"No emailScheduledId sent in code $code"))
      case Some(emailScheduledId) => Success((value._1, emailScheduledId))
    }
    case Failure(exception) => Failure(exception)
  }

  def deconstructClickTrackingUrlBase32(code: String): Try[(String, Long)] = {

      val tryPattern = _deconstructClickTrackingUrlBase32_V2(code = code)

      if (tryPattern.isSuccess) {

        tryPattern

      } else {

        logger.error(s"deconstructClickTrackingUrlBase32 ($code) failed for : tryPattern1: $tryPattern ")
        tryPattern

      }



  }

  def getEmailScheduledIdFromOpenTrackingCodeBase32_V2(code: String): Try[Long] = Try {
    StringUtils.base32DecodeURIString(code).toLong
  }

  def getOpenTrackingImgHtml(
    emailScheduledId: Long,
    customDomain: Option[String],
    defaultTrackingDomain: String,
    orgId: OrgId
  ): String = {
    // s"""<img style="display: none; visibility: hidden" alt="" src="${getOpenTrackingLink(emailScheduledId, customDomain)}" />"""
    if(AppConfig.RollingUpdates.orgIds_for_getOpenTrackingImgHtml_change.contains(orgId)) {
      s"""<img src="${getOpenTrackingLink_V2(emailScheduledId, customDomain,
        defaultTrackingDomain = defaultTrackingDomain)}">"""
    } else {
      s"""<img width="1" height="1" style="display: block;" alt="" src="${getOpenTrackingLink_V2(emailScheduledId, customDomain,
        defaultTrackingDomain = defaultTrackingDomain)}">"""
    }

    }


  def _makeUnsubscribeLinkV4(
    campaignId: Long,
    prospectId: Long,
    stepId: Long,
    emailScheduledId: Long,
    customDomain: Option[String],
    defaultUnsubscribeDomain: String
  ): String = {

    val unsubscribeLinkId: String = StringUtils.base32EncodeURIString(s"$campaignId-$prospectId-$stepId-$emailScheduledId").toLowerCase()

    val trackingDomain = if (customDomain.isDefined) s"https://${customDomain.get}" else s"https://$defaultUnsubscribeDomain"

    s"$trackingDomain/tv4/$unsubscribeLinkId/optout"
  }


  /**
    * The function is used to generate the calendarlink url which is used for calendar_link merge tag.
    * This function is currently not generic , that is the content of url are currently fixed.
    * It will be fixed when implementing the CalendarSettingsPage.
    * @param prospectId
    * @param teamId
    * @param sender_name
    * @return
    */  def _makeCalendarLinkUrl(
                          prospectId: ProspectId,
                          teamId : TeamId,
                          campaignId: CampaignId,
                          sender_name: String,
                          stepId: Long,
                          calendarAccountData: Option[CalendarAccountData],
                          selectedCalendarData: Option[SelectedCalendarData]
                          ):Try[String] = {

    val pidSidCidEncrypted:String = EncryptionHelpers.encryptCalendarInfo(value = s"pid=${prospectId.id}&cid=${campaignId.id}&sid=${stepId}")


    val calendarLinkData: Option[CalendarLinkData] = CalendarSettingsDataUtil.getCalendarLinkData(
        selectedCalendarData= selectedCalendarData
      )

    if(calendarLinkData.isDefined) {
      val calLinkData = calendarLinkData.get
      if (calLinkData.isIndividualCalendar && calLinkData.calendarUsernameSlug.isDefined) {
          Success(AppConfig.CalendarApp.baseUrl +s"/${calLinkData.calendarUsernameSlug.get}/${calLinkData.selectedEventTypeSlug}/?"+ pidSidCidEncrypted + s"&tid=${teamId.id
          }")
      } else {
        if (calLinkData.calendarTeam.isDefined) {
          Success(AppConfig.CalendarApp.baseUrl + s"/team/${calLinkData.calendarTeam.get.selected_calendar_team_slug}/${calLinkData.selectedEventTypeSlug}/?" + pidSidCidEncrypted + s"&tid=${teamId.id}")
        } else {
          Failure(new Exception("Please Save your Calendar Settings Again and Retry."))
        }
      }
    } else{
     Failure(new Exception("Please Select Some Calendar Settings and Save It.From Calendar Settings Page inside Team Settings."))
    }
  }


  def decodeBase32UnsubscribedLinkV4(code: String) = {

    Try {
      val fields: Array[String] = StringUtils.base32DecodeURIString(code).split("-")
      if (fields.length != 4) {
        throw new Exception("Invalid attempt")
      } else {
        DecryptedUnsubLinkV2(
          emailScheduledId = fields(3).toLong
        )
      }
    }

  }

  def getUnsubscribedLinkV2(
    campaignId: Option[Long],
    optOutMsg: String,
    optOutIsText: Boolean,
    optOutLinkUrl: Option[String],

    orgId: Long
  ): Try[String] = Try {

    if (campaignId.isEmpty) {
      // no unsubscribe link if campaign id is empty
      ""

    } else if (optOutIsText) {

      if (optOutMsg.trim.isEmpty) ""
      else {

        s"""
        <br/>
        $optOutMsg
        """

      }

    } else {

      if (optOutLinkUrl.isEmpty) ""
      else {

        val startIndex = optOutMsg.indexOf("{{")
        val endIndex = optOutMsg.indexOf("}}")

        if (startIndex == -1 || endIndex == -1 || startIndex >= endIndex) {

          throw new Exception("Invalid unsubscribe link")

        } else {

          val linkText = optOutMsg.substring(startIndex + 2, endIndex)
          val beginText = optOutMsg.substring(0, startIndex)
          val endText = optOutMsg.substring(endIndex + 2)

          //        s"""<br/>$beginText<a href="$applicationDomain/api/v1/campaigns/unsubscribe_v2?code=$unsubscribeLinkId" target='_blank'>$linkText</a>$endText"""


            s"""<br/>$beginText<a href="${optOutLinkUrl.get}">$linkText</a>$endText"""


        }
      }

    }

  }

  def genMessageIdSuffix(emailSettingId: Long):String = {
    val msgIdSuffixId = EncryptionService.encrypt("emailliame", s"$emailSettingId")
    s"mail@$msgIdSuffixId"
  }


  def decryptEmailNotificationUnsubscribedLink(code: String): Try[DecryptedEmailNotificationUnsubLink] = {

    Try {
      val key = AppConfig.ensettingsEncryptionKey
      val fields: Array[String] = EncryptionService.decrypt(key, code).split("-")
      if (fields.length != 3) {
        throw new Exception("Invalid attempt")
      } else {
        DecryptedEmailNotificationUnsubLink(
          accountId = AccountId(id = fields(1).toLong),
          orgId = OrgId(id = fields(2).toLong)
        )
      }
    }
  }

  def getEmailNotificationUnsubscribedLinkV2(accountId: Long, orgId: Long): Try[String] = Try { //Base32

    val time = DateTime.now().getMillis()
    val unsubscribeLinkId: String = StringUtils.base32EncodeURIString(s"$time-$accountId-$orgId").toLowerCase()
    unsubscribeLinkId

  }


  def decryptEmailNotificationUnsubscribedLinkV2(code: String): Try[DecryptedEmailNotificationUnsubLink] = {

    Try {
      val fields: Array[String] = StringUtils.base32DecodeURIString(code).split("-")
      if (fields.length != 3) {
        throw new Exception("Invalid attempt")
      } else {
        DecryptedEmailNotificationUnsubLink(
          accountId = AccountId(id = fields(1).toLong),
          orgId = OrgId(id = fields(2).toLong)
        )
      }
    }
  }


  def getMessageIdRegExPattern(suffix: String): Regex = {
    s"""\\d+\\.(\\d+)\\.(\\d+)\\.(\\d+)\\.$suffix""".r
  }

// removed on ******** as no usage
//  def isMatchingReplyToHeader(checkAgainstMessage: EmailMessageTracked,
//          checkMsg: EmailMessageTracked) : Boolean = {
//    checkAgainstMessage.message_id.nonEmpty &&
//            checkMsg.in_reply_to_header.isDefined &&
//            checkAgainstMessage.message_id == checkMsg.in_reply_to_header.get
//
//  }

// removed on ******** as no usage
//  def isMatchingReferencesHeader(checkAgainstMessage: EmailMessageTracked,
//          checkMsg: EmailMessageTracked): Boolean = {
//    checkAgainstMessage.message_id.nonEmpty &&
//            checkMsg.references_header.isDefined &&
//            checkMsg.references_header.get.contains(checkAgainstMessage.message_id)
//  }

  def getMessageId(campaignId: Option[Long], stepId: Long, prospectId: Long, suffix: String): String = {

    if (campaignId.isDefined) {

      // fixme inboxv3: randomize currentTimeLong param further for inbox use-case (prospect_id is being passed as 0)
      val currentTimeLong: Long = new Date().getTime
      val cid: Long = campaignId.getOrElse(0)
      val sid: Long = stepId
      val pid: Long = prospectId

      //INFO: Unique message Id is is <currentTime.stepId.prospectId.suffix>
      s"<$currentTimeLong.$cid.$sid.$pid.$suffix>"

    } else {

      // if no campaign id, generate random string
      val randomStr1 = StringUtils.genRandomAlphaNumericString30Chars
      val randomStr2 = StringUtils.genRandomAlphaNumericString30Chars
      s"<${new Date().getTime}.$randomStr1.$randomStr2>"


    }
  }

  // REF: https://support.google.com/mail/answer/6254652?hl=en
  // removed on ******** as no usage
//  def getGmailFBLId(
//    campaignId: Long,
//    accountId: Long,
//    prospectId: Long
//  ): String = {
//
//    val senderId = AppConfig.gmailFBLSenderId
//
//    s"$campaignId:$prospectId:$accountId:$senderId"
//
//  }


  val LIST_UNSUBSCRIBE_EMAIL_ALIAS_KEY = "unsubscribesrmail"

  def getListUnsubscribeHeader(
                                prospectId: Long,
                                campaignId: Long,
                                stepId: Long,
                                emailScheduledId: Long,
                                replyToEmail: String
                              ): String = {

    val emailSplit = EmailValidationService.getNameDomainAndAliasFromEmailV2(email = replyToEmail)
    val domain = emailSplit.domain
    val nameWithoutAlias = emailSplit.nameWithoutAlias


    s"<mailto:$nameWithoutAlias+${LIST_UNSUBSCRIBE_EMAIL_ALIAS_KEY}_${prospectId}_${campaignId}_${stepId}_$emailScheduledId@$domain?subject=unsubscribe>"


  }

  /**
    * Verify whether incoming email (while reply tracking) is list-unsubscribe email
    */
  def checkIfSRListUnsubscribeEmail(toEmailAddresses: Seq[String]): Option[String] = {
    toEmailAddresses.find(em => {
      em.toLowerCase.contains(EmailHelper.LIST_UNSUBSCRIBE_EMAIL_ALIAS_KEY.toLowerCase)
    })
  }

  def deconstructListUnsubscribeHeaderEmail(
    unsubEmailAddress: String
  ): Try[DecryptedUnsubLinkV2] = Try {

    // s"<mailto:$nameWithoutAlias+${LIST_UNSUBSCRIBE_EMAIL_ALIAS_KEY}_${prospectId}_${campaignId}_${stepId}_$emailScheduledId@$domain?subject=unsubscribe>"
    // example unsubEmailAddress: "<EMAIL>"

    val srcode = s"${LIST_UNSUBSCRIBE_EMAIL_ALIAS_KEY}_"
    val srcodeIndex = unsubEmailAddress.indexOf(srcode)
    val domainStartIndex = unsubEmailAddress.lastIndexOf("@")
    val aliasSplit = unsubEmailAddress.substring(srcodeIndex + srcode.size, domainStartIndex).split("_").toSeq

    val prospectId = ProspectId(id = aliasSplit(0).toLong)
    val campaignId = CampaignId(id = aliasSplit(1).toLong)
    val stepId = StepId(id = aliasSplit(2).toLong)
    val emailScheduledId = aliasSplit(3).toLong

    DecryptedUnsubLinkV2(
      emailScheduledId = emailScheduledId
    )

  }


  // todo: remove this
  def getMessageIdRegExPatternOld: Regex = {

    val suffix = AppConfig.emailMessageIdSuffix

    s"""\\d+\\.(\\d+)\\.(\\d+)\\.(\\d+)\\.$suffix""".r
  }





  // used to create slug for campaign steps and inbox view, to avoid styles being shown in the preview text
  def getTextPreviewSlugFromHtml(html: String): String = {

    val doc = Jsoup.parse(html)
    val text = doc.body().text()
    if (text == null) "" else text

  }

  def getCustomTrackingDomainFromUserInputTrackingHost(trackingDomain: Option[String]): Option[String] = trackingDomain.map(domain => "email." + domain)


  def __isSRTrackingOrUnsubscribeLink(fullUrl: String, host: String, path: String, allTrackingDomainsUsed: Seq[String]): Boolean = {


    val isUsingSRTrackingOrUnsubscribeDomain = allTrackingDomainsUsed.exists(domain => fullUrl.toLowerCase.contains(domain.toLowerCase.trim))

    isUsingSRTrackingOrUnsubscribeDomain ||
      (
        (
          host.startsWith("email.") ||
            host.startsWith("sr.") ||
            host.startsWith("go.")
          ) && (
          path.contains("/ot") ||
            path.contains("/ct") ||
            path.contains("/tv")
          )
        )
  }

  def isSRTrackingOrUnsubscribeLink(href: String, allTrackingDomainsUsed: Seq[String], orgId: OrgId)(using logger: SRLogger): Try[Boolean] = Try {
    Helpers.stringToURL(
      href = href,
      orgIdOpt = Some(orgId)
    ) match {

      case Failure(e) =>
        val exceptionMsg = Option(e.getMessage).getOrElse("")
         if(exceptionMsg.contains("unknown protocol")|| exceptionMsg.contains("no protocol")) {
           logger.warn(s"isSRTrackingOrUnsubscribeLink: $href :: ${exceptionMsg}")
         } else {
           logger.error(s"isSRTrackingOrUnsubscribeLink: $href :: ${LogHelpers.getStackTraceAsString(e)}")
         }
        throw e


      case Success(url) =>

        val host = url.getHost
        val path = url.getPath
        __isSRTrackingOrUnsubscribeLink(fullUrl = href, host = host, path = path, allTrackingDomainsUsed = allTrackingDomainsUsed)

    }
  }


  private def replaceGoogleProxySROpenTrackingUrl(html: String)= {

    val doc = Jsoup.parse(html)

    val links = doc.select("img[src]")

    for (link <- links.asScala) {
      val strLink = link.toString
      if(strLink.contains("googleusercontent") && strLink.contains("/ot2/")) {
        link.attr("src", "https://api.smartreach.io/dnt2/googleusercontentdnt/open")
      }
    }

    doc.html()
  }

  // replace open/click tracking links with dummy do-not-track links
  def disableTrackingLinks(body: String, allTrackingDomains: Seq[String], isTextBody: Boolean): String = {

    var updatedBody = if (isTextBody) body else replaceGoogleProxySROpenTrackingUrl(html = body)

    updatedBody = updatedBody
      .replaceAll("/ot2/", "/dnt2/")
      .replaceAll("/ct3/", "/dntc3/")

    updatedBody

    // cnames werent being properly handled earlier
    /*var updatedBody = body

    allTrackingDomains.foreach(trackingDomain => {

      updatedBody = updatedBody
        .replace(s"$trackingDomain/ot", s"$trackingDomain/dnt")
        // /ct2 -> /dntc2 is also handled here
        .replace(s"$trackingDomain/ct", s"$trackingDomain/dntc")
        .trim


    })


    updatedBody = EmailHelper.disableCustomTrackingLinks(html = updatedBody, allTrackingDomainsUsed = allTrackingDomains)

    updatedBody*/

  }

}
