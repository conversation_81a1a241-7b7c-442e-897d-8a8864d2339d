package utils.email

import api.AppConfig
import org.joda.time.DateTime

object SREmailErrors {

  val HUNDRED_YEARS: Int = 100 * 365 * 24 * 60 // 100 years

  sealed abstract class EmailSettingError {
    val defaultMsg: String
    val pauseForMinutes: Int

    def pauseTill: DateTime = DateTime.now().plusMinutes(pauseForMinutes)

  }

  case object GmailInvalidGrantError extends EmailSettingError {
    val defaultMsg = "Failed to connect to your Gmail account"
    val pauseForMinutes = HUNDRED_YEARS
  }

  case object MicrosoftInvalidGrantError extends EmailSettingError {
    val defaultMsg = "Failed to connect to your Office365 account"
    val pauseForMinutes = HUNDRED_YEARS
  }

  case object AuthenticationFailedError extends EmailSettingError {
    val defaultMsg = "Failed to connect to this email account. Please update account credentials in Settings -> Email Accounts."
    val pauseForMinutes = HUNDRED_YEARS
  }

  case object GmailSMTPFailedError extends EmailSettingError {
    val defaultMsg = "Gmail send error, will retry after 10 minutes"
    val pauseForMinutes = 10
  }

  case object SmtpSendFailedWithSpamORRejectedWarning extends EmailSettingError {
    val defaultMsg = s"Your email domain / host is being blocked by the recipient(s) for sending spam (Please contact support if you have any questions)"
    val pauseForMinutes = HUNDRED_YEARS
  }


  case object UnknownSendError extends EmailSettingError {
    val defaultMsg = "Error while sending email, will retry after 10 minutes"
    val pauseForMinutes = 10
  }

  case object GSuiteMailServiceNotEnabledError extends EmailSettingError {
    val defaultMsg = "G-Suite: Mail service not enabled. Please check your G-Suite account/subscription"
    val pauseForMinutes = HUNDRED_YEARS
  }

  case object GmailSendingLimitReachedError extends EmailSettingError {
    val defaultMsg = "Gmail is throttling your email sending with the 'Sending limit reached' message. Sending will be restarted in 24 hours."
    val pauseForMinutes = 24 * 60 // 24 Hours

  }

  case object OutlookServiceUnavailableError extends EmailSettingError {
    val defaultMsg = "Office365/Outlook api is unavailable now. Connection will be retried in 10 minutes."
    val pauseForMinutes = 10
  }

  case object OutlookAPIFatalError extends EmailSettingError {
    val defaultMsg = "We are facing problems connecting with your Office365/Outlook email account. Connection will be retried in 10 minutes."
    val pauseForMinutes = 10
  }

  case object OutlookBadOutBoundSendError extends EmailSettingError {
    val defaultMsg = s"We noticed multiple bounces with a “bad outbound sender error” from your Outlook account. You may refer this article: ${AppConfig.bad_outbound_sender_help_doc_link}. If you are still not sure, please contact us."
    val pauseForMinutes = HUNDRED_YEARS
  }

  case object EmailAPIInternalFailureError extends EmailSettingError {
    val defaultMsg = "We are facing problems connecting with your email account. Connection will be retried in 10 minutes."
    val pauseForMinutes = 10
  }

  case object EmailAPIAccessTokenTemporaryError extends EmailSettingError {
    val defaultMsg = "We are facing problems connecting with your email account. Connection will be retried in 10 minutes."
    val pauseForMinutes = 10
  }

  case object ConnectionTimeoutError extends EmailSettingError {
    val defaultMsg = "We are facing a problem (timeout) connecting with this email account. Connection will be retried in 10 minutes."
    val pauseForMinutes = 25 // fixme: three consecutive errors of this type, just block the sending completely until user reconnects
  }

  case object ReplyTrackingMessagingExceptionError extends EmailSettingError {
    val defaultMsg = "We are getting an error while tracking replies in your inbox; will retry in 10 minutes"
    val pauseForMinutes = 10
  }

  case object UnknownReplyTrackingError extends EmailSettingError {
    val defaultMsg = "There is an error while tracking replies in your inbox; will retry in 10 minutes"
    val pauseForMinutes = 10
  }

}
