package utils.email

import api.accounts.TeamId
import api.accounts.models.OrgId
import api.calendar_app.models.CalendarAccountData
import api.campaigns.models.PreviousFollowUpData
import api.campaigns.services.{CampaignId, CampaignTemplateService}
import api.campaigns.{CampaignEditedPreviewEmail, CampaignEditedPreviewEmailDAO, PreviousFollowUp}
import api.columns.InternalMergeTagValuesForProspect
import api.emails.{EmailScheduledDAO, EmailsScheduledUuid}
import api.emails.dao_service.EmailScheduledDAOService
import api.prospects.ProspectAccount
import api.prospects.models.ProspectId
import eventframework.ProspectObject
import io.smartreach.esp.utils.email.EmailHelperCommon
import play.api.Logging
import sr_scheduler.models.{ChannelType, SelectedCalendarData}
import utils.{Helpers, SRLogger}
import utils.email.EmailServiceTemp.{__makeAppendFollowupHtmlV1, __makeAppendFollowupHtmlV2, __makeHTMLBodyV1, __makeHTMLBodyV2}
import utils.helpers.LogHelpers
import utils.templating.TemplateService

import scala.util.{Failure, Success, Try}

case class EmailOptionsForGetBodies (
                                      // will not have appended followups, open, click tracking,
                                      // and unsubscribe links will all be {{unsubscribe_link}}
                                      for_editable_preview: Boolean,
                                      editedPreviewEmailAlreadyChecked: Boolean,
                                      custom_tracking_domain: Option[String],
                                      default_tracking_domain: String,
                                      default_unsubscribe_domain: String,
                                      opt_out_msg: String,
                                      opt_out_is_text: Boolean,
                                      append_followups: Boolean,
                                      signature: Option[String],
                                      bodyTemplate: String,
                                      subjectTemplate: String,
                                      email_sender_name: String,
                                      sender_first_name: String,
                                      sender_last_name: String,
                                      manualEmail: Boolean,
                                      trackOpens: Boolean,
                                      trackClicks: Boolean,
                                      isCampaignSendTestEmail: Boolean = false, // send test email for campaign
                                      previousEmails: Seq[PreviousFollowUp],
                                      allTrackingDomainsUsed: Seq[String]
                                    )
class EmailServiceCompanion(
                             templateService: TemplateService,
                             campaignEditedPreviewEmailDAO: CampaignEditedPreviewEmailDAO,
                             emailScheduledDAOService: EmailScheduledDAOService,
                             //emailService: EmailService
                             emailBodyService: EmailBodyService,
) extends Logging {


  def getBodies(

                 // to optimize the prospect preview api, we are passing this directly from there
                 editedPreviewEmail: Option[CampaignEditedPreviewEmail],
                 org_id: Long,
                 calendarAccountData: Option[CalendarAccountData],
                 selectedCalendarData: Option[SelectedCalendarData],
                 // TODO: check where this could be None
                 head_step_id: Option[Long],

                 emailsScheduledUuid: EmailsScheduledUuid,
                 campaign_id: Option[Long],
                 step_id: Option[Long],
                 prospect: ProspectObject,
                 emailOptions: EmailOptionsForGetBodies
  )( implicit logger:SRLogger): Try[EmailServiceBody] = {

    val editedPreviewEmailAlreadyChecked = emailOptions.editedPreviewEmailAlreadyChecked
    val for_editable_preview = emailOptions.for_editable_preview
    val custom_tracking_domain = emailOptions.custom_tracking_domain
    val default_tracking_domain = emailOptions.default_tracking_domain
    val default_unsubscribe_domain = emailOptions.default_unsubscribe_domain
    val opt_out_msg = emailOptions.opt_out_msg
    val opt_out_is_text = emailOptions.opt_out_is_text
    val append_followups = emailOptions.append_followups
    val signature = emailOptions.signature
    val bodyTemplate = emailOptions.bodyTemplate
    val subjectTemplate = emailOptions.subjectTemplate
    val email_sender_name = emailOptions.email_sender_name
    val sender_first_name = emailOptions.sender_first_name
    val sender_last_name = emailOptions.sender_last_name
    val manualEmail = emailOptions.manualEmail
    val trackOpens = emailOptions.trackOpens
    val trackClicks = emailOptions.trackClicks
    val previousEmails = emailOptions.previousEmails
    val allTrackingDomainsUsed = emailOptions.allTrackingDomainsUsed

    val optOutLinkUrl = if (campaign_id.isEmpty) None

    else if (for_editable_preview) {

      // while generating preview do not replace unsubscribe_link merge-tag, instead retain the same merge-tag again
      Some("{{unsubscribe_link}}")

    } else Some(UnSubscribeLinkHelper.constructUnsubUrl(
      emailsScheduledUuid = emailsScheduledUuid,
      teamId = TeamId(id = prospect.team_id),
      customDomain = custom_tracking_domain,
      defaultUnsubscribeDomain = default_unsubscribe_domain
    ))


    EmailHelper.getUnsubscribedLinkV2(
      optOutMsg = opt_out_msg.trim,
      optOutIsText = opt_out_is_text,
      campaignId = campaign_id,
      optOutLinkUrl = optOutLinkUrl,

      orgId = org_id
    ) flatMap { unsubHtml =>

      // Logger.info(s"[EmailService] processSendEmailRequest: getBodies: sender email id: ${data.sender_email_settings_id} : dns: ${data.dns_host}: $unsubscribeEmailHtml")

      val signatureHtml: String = if (
        signature.isEmpty ||
          signature.get.trim.isEmpty

      ) "" else {

        val withBreaks = s"""<br/>${signature.get.trim}"""

        if (trackClicks && !for_editable_preview) {

          emailBodyService._getSignatureWithTrackingLinks(
            signature = signature.get.trim,
            emailsScheduledUuid = emailsScheduledUuid,
            customDomain = custom_tracking_domain,
            defaultTrackingDomain = default_tracking_domain,
            allTrackingDomainsUsed = allTrackingDomainsUsed,
            teamId = TeamId(id = prospect.team_id),
            orgId = OrgId(org_id)
          )

        } else {
          withBreaks
        }

      }



      val calendarLinkUrl = if(selectedCalendarData.isDefined) {
        EmailHelper._makeCalendarLinkUrl(
          prospectId = ProspectId(id = prospect.id),
          teamId = TeamId(id = prospect.team_id),
          sender_name = email_sender_name,
          campaignId = CampaignId(id = campaign_id.get),
          stepId = step_id.getOrElse(0),
          calendarAccountData = calendarAccountData,
          selectedCalendarData = selectedCalendarData

        ).toOption
      }else{
        None
      }




      val internalMergeTags = getInternalMergeTag(
        isCampaignSendTestEmail = emailOptions.isCampaignSendTestEmail,
        sender_name = email_sender_name,
        sender_first_name = sender_first_name,
        sender_last_name = sender_last_name,
        unsubscribe_link = optOutLinkUrl,
        calendar_link = calendarLinkUrl,
        stepId = step_id,
        head_step_id = head_step_id,
        previousEmails = previousEmails,
        campaignId = campaign_id.get,
        prospectId = prospect.id,
        teamId = TeamId(id = prospect.team_id),
        signature = Some(signatureHtml)
      ).get


      var renderBodyTemplate = bodyTemplate
      var renderSubjectTemplate = subjectTemplate
      var isEditedPreviewEmail = false


      // if user has saved edited preview for this email step, use the edited body / subject
      if (step_id.isDefined) {

        if (editedPreviewEmail.isDefined) {

          renderBodyTemplate = editedPreviewEmail.head.editedBody
          renderSubjectTemplate = editedPreviewEmail.head.editedSubject
          isEditedPreviewEmail = true


        } else if (!editedPreviewEmailAlreadyChecked) {
          // to optimise prospect preview api
          // when already checked at the function caller level, dont check here


          campaignEditedPreviewEmailDAO.find(stepId = step_id, prospectId = prospect.id, campaignId = None) match {
            case Failure(e) =>
              logger.error(s"[EmailServiceCompanion] FATAL getBodies: for_editable_preview: $for_editable_preview :: prospect_id: ${prospect.id} :: ${LogHelpers.getStackTraceAsString(e)}")

            case Success(data) =>

              if (data.nonEmpty) {

                renderBodyTemplate = data.head.editedBody
                renderSubjectTemplate = data.head.editedSubject
                isEditedPreviewEmail = true


              }
          }
        }
      }


      templateService.render(
        template = renderBodyTemplate,
        prospect = prospect,
        internalMergeTags = internalMergeTags,
        channel = ChannelType.EmailChannel
      ) flatMap { templateBody =>


        templateService.render(
          template = renderSubjectTemplate,
          prospect = prospect,
          internalMergeTags = internalMergeTags,
          channel = ChannelType.EmailChannel
        ) map { subject =>

          // trackOpens should be allowed for manualEmail too
          // ignore open tracking for preview
          val openTrackingImgHtml: String = if (!trackOpens || for_editable_preview) {
            ""
          } else {
            OpenTrackingUrlHelper.constructOpenTrackingImgHtml(emailsScheduledUuid, customDomain = custom_tracking_domain,
              defaultTrackingDomain = default_tracking_domain, orgId = OrgId(org_id), teamId = TeamId(id = prospect.team_id))
          }


          ////


          // take full page html from frontend for the following orgs and newer orgs
          // dont add additional styles or DOCTYPES for these, whatever html comes from
          // frontend is good enough
          // fullpage plugin is a part of the tinymce texteditor used in the frontend
          val orgId = org_id
          val enableFullpagePluginForOrgIdAbove = 347
          val enableFullpagePlugin = (orgId >= enableFullpagePluginForOrgIdAbove)


          // trackClicks should be allowed for manualEmail too
          // ignore click tracking for preview
          val baseBody = if (trackClicks && !for_editable_preview) {

            if (enableFullpagePlugin) {

              emailBodyService._getBodyWithTrackingLinksV2(

                templateBody,
                emailsScheduledUuid = emailsScheduledUuid,
                customDomain = custom_tracking_domain,
                defaultTrackingDomain = default_tracking_domain,
                allTrackingDomainsUsed = allTrackingDomainsUsed,
                teamId = TeamId(id = prospect.team_id),
                orgId = OrgId(org_id)

              )

            } else {

              emailBodyService._getBodyWithTrackingLinksV1(

                templateBody,
                emailsScheduledUuid = emailsScheduledUuid,
                customDomain = custom_tracking_domain,
                defaultTrackingDomain = default_tracking_domain,
                allTrackingDomainsUsed = allTrackingDomainsUsed,
                teamId = TeamId(id = prospect.team_id),
                orgId = OrgId(org_id)

              )

            }
          } else templateBody


          val prevEmailHtml: String = if (for_editable_preview) {
            ""
          } else if (enableFullpagePlugin) {

            __makeAppendFollowupHtmlV2(
              isManualEmail = manualEmail,
              appendFollowups = append_followups,
              previousEmails = previousEmails
            )
          } else {

            __makeAppendFollowupHtmlV1(
              isManualEmail = manualEmail,
              appendFollowups = append_followups,
              previousEmails = previousEmails
            )
          }

          val unsubscribeEmailHtml = if (
            manualEmail ||

              // if user has edited the preview, do not add unsubscribe link again
              isEditedPreviewEmail

          ) "" else unsubHtml

          // if there is a signature merge tag, we don't need to add the signature at the end of the email, as it is already added by the merge tag
          // but if this is a edited preview, then also we cant add the signature, as with every edit, it will add a new signature.
          val appendSignature = !bodyTemplate.contains("{{signature}}") && editedPreviewEmail.isEmpty && !isEditedPreviewEmail

          val htmlBody = if (enableFullpagePlugin) {

            __makeHTMLBodyV2(
              baseBody = baseBody,
              prevEmailHtml = prevEmailHtml,
              openTrackingImgHtml = openTrackingImgHtml,
              unsubscribeEmailHtml = unsubscribeEmailHtml,
              signature = signatureHtml,
              appendSignature = appendSignature
            )
          } else {

            __makeHTMLBodyV1(
              baseBody = baseBody,
              prevEmailHtml = prevEmailHtml,
              openTrackingImgHtml = openTrackingImgHtml,
              unsubscribeEmailHtml = unsubscribeEmailHtml,
              signature = signatureHtml,
              appendSignature = appendSignature
            )
          }


          val textBody = EmailHelperCommon.getTextBodyFromHtmlBody(baseBody)

          EmailServiceBody(
            subject = subject,
            textBody = textBody,
            htmlBody = htmlBody,
            baseBody = templateBody,

            // if user has saved edited preview for this email step, use the edited body to check unsub link
            has_unsubscribe_link = Helpers.campaignHasUnsubscribeLink(opt_out_is_text = opt_out_is_text, opt_out_msg = opt_out_msg, body_template = renderBodyTemplate),

            isEditedPreviewEmail = isEditedPreviewEmail
          )

        }

      }
    }
  }


  def getInternalMergeTag(
                           sender_name: String,
                           sender_first_name: String,
                           sender_last_name: String,
                           unsubscribe_link: Option[String],
                           calendar_link: Option[String],
                           stepId: Option[Long],
                           head_step_id: Option[Long],
                           previousEmails: Seq[PreviousFollowUp],
                           campaignId: Long,
                           prospectId: Long,
                           teamId: TeamId,
                           isCampaignSendTestEmail: Boolean = false,
                           signature: Option[String]
                         )(using Logger: SRLogger): Try[InternalMergeTagValuesForProspect] = {

    var previousSubject: Option[String] = None

    if(head_step_id.isDefined && stepId != head_step_id){

      // 23rd Dec 2022: if its not a test email, i.e. its a real campaign email, then previousFollowUps cannot be empty (in case not headStepId)
      if(previousEmails.isEmpty && !isCampaignSendTestEmail){

        //        val emptyPreviousFollowup = new Throwable("No previous subject for the tag")
        emailScheduledDAOService.getPreviousSentSteps(
          campaignId = campaignId,
          prospectId = prospectId,
          teamId = teamId,
          get_skipped = true
        ) match {
          case Failure(a) => Failure(a)

          case Success(previousFollowUps) =>

            if(previousFollowUps.isEmpty) { //  Fixme if Email step is not headStep, and there are not previous email step then this is throwing handle this case
              //  we can pass is_multichannel flag to this function and add it as a check on line 357 so if the campaign is multichannel it will not throw
              // like multichannel campaigns can have email step as not headStep.
              // This was fixed on handled with below code. on 23 march 2023

              Success(InternalMergeTagValuesForProspect(
                sender_name = sender_name,
                sender_first_name = sender_first_name,
                sender_last_name = sender_last_name,
                unsubscribe_link = unsubscribe_link,
                previous_subject = None,
                signature = signature,
                sender_phone_number = None,
                calendar_link = calendar_link

              ))

            } else{

              val previousEmails = previousFollowUps.sortBy(pfu => pfu.sent_at.getMillis).reverse

              val previousFollowUpSubject: Seq[String] = previousEmails.map( pfu => {
                pfu.channel_follow_up_data match {


                  case data: PreviousFollowUpData.ManualEmailMagicFollowUp =>
                    data.generated_subject.getOrElse("")

                  case data: PreviousFollowUpData.AutoEmailMagicFollowUp =>

                    data.generated_subject.getOrElse("")

                  case data: PreviousFollowUpData.AutoEmailFollowUp =>

                    data.subject

                  case data: PreviousFollowUpData.ManualEmailFollowUp =>

                    data.subject

                  case _: PreviousFollowUpData.LinkedinInMailFollowUp
                       | _: PreviousFollowUpData.LinkedinMessageFollowUp
                       | _: PreviousFollowUpData.LinkedinConnectionRequestFollowUp
                       | _: PreviousFollowUpData.LinkedinViewProfileFollowUp
                       | _: PreviousFollowUpData.AutoLinkedinInMailFollowUp
                       | _: PreviousFollowUpData.AutoLinkedinMessageFollowUp
                       | _: PreviousFollowUpData.AutoLinkedinConnectionRequestFollowUp
                       | _: PreviousFollowUpData.AutoLinkedinViewProfileFollowUp
                       | _: PreviousFollowUpData.WhatsappFollowUp
                       | _: PreviousFollowUpData.SmsFollowUp
                       | _: PreviousFollowUpData.CallFollowUp
                       | _: PreviousFollowUpData.MoveToAnotherCampaignFollowUp
                       | _: PreviousFollowUpData.GeneralFollowUp
                  =>

                    throw new Exception("Impossible case: Step type can only be email types")

                }
              })

              previousSubject = previousFollowUpSubject
                .find(subject => {
                  !CampaignTemplateService.containsPrevSubjectMergeTag(s = subject)
                  })


              Success(InternalMergeTagValuesForProspect(  //FixMe:  This case class might be converted to sealed trait as well.
                sender_name = sender_name,
                sender_first_name = sender_first_name,
                sender_last_name = sender_last_name,
                unsubscribe_link = unsubscribe_link,
                previous_subject = previousSubject,
                signature = signature,
                sender_phone_number = None, //Email specific-> no phone number,
                calendar_link = calendar_link
              ))
            }

        }
      } else {

        val prevEmails = previousEmails.sortBy(pfu => pfu.sent_at.getMillis).reverse

        val previousFollowUpSubject: Seq[String] = prevEmails.map(pfu => pfu.channel_follow_up_data match {

          case data: PreviousFollowUpData.AutoEmailFollowUp =>

            data.subject

          case data: PreviousFollowUpData.ManualEmailFollowUp =>

            data.subject

          case data: PreviousFollowUpData.ManualEmailMagicFollowUp =>
            data.generated_subject.getOrElse("")

          case data: PreviousFollowUpData.AutoEmailMagicFollowUp =>

            data.generated_subject.getOrElse("")

          case _: PreviousFollowUpData.LinkedinInMailFollowUp
               | _: PreviousFollowUpData.LinkedinMessageFollowUp
               | _: PreviousFollowUpData.LinkedinConnectionRequestFollowUp
               | _: PreviousFollowUpData.LinkedinViewProfileFollowUp
               | _: PreviousFollowUpData.AutoLinkedinMessageFollowUp
               | _: PreviousFollowUpData.AutoLinkedinViewProfileFollowUp
               | _: PreviousFollowUpData.AutoLinkedinConnectionRequestFollowUp
               | _: PreviousFollowUpData.AutoLinkedinInMailFollowUp
               | _: PreviousFollowUpData.WhatsappFollowUp
               | _: PreviousFollowUpData.SmsFollowUp
               | _: PreviousFollowUpData.CallFollowUp
               | _: PreviousFollowUpData.MoveToAnotherCampaignFollowUp
               | _: PreviousFollowUpData.GeneralFollowUp
          =>

            throw new Exception("Impossible case: Step type can only be email types")

        })

        previousSubject = previousFollowUpSubject
          .find(subject => {
            !CampaignTemplateService.containsPrevSubjectMergeTag(s = subject)
          })


        Success(InternalMergeTagValuesForProspect(
          sender_name = sender_name,
          sender_first_name = sender_first_name,
          sender_last_name = sender_last_name,
          unsubscribe_link = unsubscribe_link,
          previous_subject = previousSubject,
          calendar_link = calendar_link,
          signature = signature,
          sender_phone_number = None
        ))
      }

    } else {
      Success(InternalMergeTagValuesForProspect(
        sender_name = sender_name,
        sender_first_name = sender_first_name,
        sender_last_name = sender_last_name,
        unsubscribe_link = unsubscribe_link,
        calendar_link = calendar_link,
        previous_subject = previousSubject,
        signature = signature,
        sender_phone_number = None
      ))
    }


  }
}
