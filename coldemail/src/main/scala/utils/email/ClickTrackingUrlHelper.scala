package utils.email

import api.AppConfig
import api.accounts.TeamId
import api.emails.EmailsScheduledUuid
import play.api.Logging
import utils.{ParseUtils, StringUtils}

import scala.util.{Failure, Success, Try}

case class ClickTrackingUrlAndEmail(
                                     clickTrackingUrl: String,
                                     emailScheduledId: Option[Long],
                                     emailsScheduledUuidAndTeamId: Option[(EmailsScheduledUuid, TeamId)],
                                   )

object ClickTrackingUrlHelper {
  
  def constructClickTrackingCode(
                                  emailsScheduledUuid: EmailsScheduledUuid,
                                  teamId: TeamId,
                                  url: String
                                ): String = {
    s"${teamId.id}${AppConfig.trackingLinksDelimiter}${emailsScheduledUuid.uuid}${AppConfig.trackingLinksDelimiter}".toLowerCase + url
  }


  def constructClickTrackingUrl(
                                 emailsScheduledUuid: EmailsScheduledUuid,
                                 teamId: TeamId,
                                 url: String,
                                 customDomain: Option[String],
                                 defaultTrackingDomain: String
                               ): String = {

    val baseStr: String = constructClickTrackingCode(
      emailsScheduledUuid = emailsScheduledUuid, 
      teamId = teamId, 
      url = url
    )

    val code = StringUtils.base32EncodeURIString(baseStr)
    
    val trackingDomain = if (customDomain.isDefined) s"https://${customDomain.get}" else s"https://$defaultTrackingDomain"

    s"$trackingDomain/ct3/$code/click"


  }


  // new format
  def _deconstructClickTrackingUrlBase32_V2(code: String): Try[(String, Long)] = Try {
    val decoded = StringUtils.base32DecodeURIString(code)


    val splits = decoded.split(AppConfig.trackingLinksDelimiter, 2).toVector

    val emailScheduledId = ParseUtils.parseLong(splits(0))
    val decodedUrl = splits(1)

    (decodedUrl, emailScheduledId)


  } match {
    case Success(value) => value._2 match {
      case None => Failure(new Throwable(s"No emailScheduledId sent in code $code"))
      case Some(emailScheduledId) => Success((value._1, emailScheduledId))
    }
    case Failure(exception) => Failure(exception)
  }

  def deconstructClickTrackingCode(decoded: String): Try[ClickTrackingUrlAndEmail] = Try {

    val splits = decoded.split(AppConfig.trackingLinksDelimiter, 2).toVector

    if (splits.length != 2) {
      throw new Exception("Invalid format: wrong number of fields")
    }

    val teamId = ParseUtils.parseLong(splits(0)).getOrElse(throw new Exception("Invalid teamId format"))
    val urlWithUuid = splits(1)

    // Extract the uuid and actual URL from the combined string
    val uuidAndUrl = urlWithUuid.split(AppConfig.trackingLinksDelimiter, 2)
    if (uuidAndUrl.length != 2) {
      throw new Exception("Invalid format: missing uuid prefix")
    }

    val uuid = uuidAndUrl(0)
    val url = uuidAndUrl(1)

    ClickTrackingUrlAndEmail(
      clickTrackingUrl = url,
      emailsScheduledUuidAndTeamId = Some(EmailsScheduledUuid(uuid = uuid), TeamId(id = teamId)),
      emailScheduledId = None,
    )
  }

  def deconstructClickTrackingUrl(code: String): Try[ClickTrackingUrlAndEmail] = {
    // First try the new format (v2)
    val tryPattern2: Try[ClickTrackingUrlAndEmail] =  {
      val decoded = StringUtils.base32DecodeURIString(code)

      deconstructClickTrackingCode(decoded)
    }

    if (tryPattern2.isSuccess) {
      tryPattern2
    } else {
      // Try the other old format (v1.2)
      val tryPattern1 = _deconstructClickTrackingUrlBase32_V2(code = code)
      if (tryPattern1.isSuccess) {
        val (url, emailScheduledId) = tryPattern1.get
        Success(ClickTrackingUrlAndEmail(
          clickTrackingUrl = url,
          emailScheduledId = Some(emailScheduledId),
          emailsScheduledUuidAndTeamId = None
        ))
      } else {
        Failure(new Exception(s"Failed to decode click tracking URL: $code"))
      }
    }
  }

}
