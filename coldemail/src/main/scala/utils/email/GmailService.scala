package utils.email

import org.apache.pekko.actor.ActorSystem
import api.accounts.models.OrgId

import java.util.Properties
import javax.mail.*
import api.accounts.{GoogleOAuth, TeamId}
import api.emails.*
import api.emails.dao_service.EmailScheduledDAOService
import api.emails.models.EmailMessageTrackedV4
import com.sun.mail.gimap.{GmailFolder, GmailStore}
import com.sun.mail.imap.IMAPFolder
import io.smartreach.esp.api.emailApi.GmailSmtpEmailApi
import io.smartreach.esp.api.emailApi.models.{AuthInfo, GmailSMTPAccessToken, UniqueLogIDForSender}
import io.smartreach.esp.api.emails.EmailInfo
import io.smartreach.esp.api.emails.EmailSentResponse.GmailSMTPApiSentResponse
import io.smartreach.esp.utils.email.models.EmailReply
import org.joda.time.DateTime
import play.api.Logging
import play.api.libs.ws.WSClient
import utils.email.services.{EmailReplyTrackingService, EmailSendingService}
import utils.email.services.ReplyFilteringService
import utils.helpers.LogHelpers
import utils.{ISRLogger, SRLogger}
import utils.mq.services.MQDoNotNackException

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}


class GmailService(
                    val emailScheduledDAOService: EmailScheduledDAOService,
                    googleOAuth: GoogleOAuth,
                    gmailSmtpEmailApi: GmailSmtpEmailApi,
                    val replyFilteringService: ReplyFilteringService

                  ) extends EmailReplyTrackingService
  with EmailSendingService
  with Logging {

  /**
   * // NOTE: 6-Dec-2023: This is used only in GmailService which is no longer used, after Gmail
   * deprecated imap / smtp access via Google Access Tokens last year. We have since moved to Gmail ASP
   * flow which goes directly via SmtpImapService instead.
   *
   * When upgrading from Java17 from Java8, compilation is failing because of this with the following error,
   * so commenting it out:
   *
   * [error] /Users/<USER>/Documents/projects/coldemail/coldemail/src/main/scala/utils/email/GmailService.scala:37:32:
   * constructor Provider in class Provider is deprecated
   * [error]   class OAuth2Provider extends Provider("Google OAuth2 Provider", 1.0, "Provides the XOAUTH2 SASL Mechanism") {
   * [error]
   * */

  /*
  class OAuth2Provider extends Provider("Google OAuth2 Provider", 1.0, "Provides the XOAUTH2 SASL Mechanism") {
    put("SaslClientFactory.XOAUTH2", "utils.email.OAuth2SaslClientFactory")
  }

  Security.addProvider(new OAuth2Provider())*/

  //

  def sendEmailService(
                        accessToken: String,
                        emailToBeSent: EmailToBeSent
                      )(
                        implicit ws: WSClient,
                        ec: ExecutionContext,
                        Logger: ISRLogger,
                        system: ActorSystem
                      ): Future[GmailSMTPApiSentResponse] = {
    gmailSmtpEmailApi.sendEmailService(
      authInfo =
        AuthInfo.GmailSmtpAuthInfo(
          accessToken = GmailSMTPAccessToken(
            accessToken = accessToken,
          ),
          rep_smtp_reverse_dns_host = emailToBeSent.rep_smtp_reverse_dns_host

        ),
      emailToBeSent = EmailToBeSent.convertEmailToBeSentToEmailInfo(emailToBeSent = emailToBeSent),
      uniqueLogId = UniqueLogIDForSender(s"eset_${emailToBeSent.sender_email_settings_id}") // For logging purpose only

    )
  }


  def sendEmail(
                 teamId: TeamId,
                 orgId: OrgId,
                 emailSendDetail: EmailSendDetail,
                 emailToBeSent: EmailToBeSent
               )(
                 implicit wsClient: WSClient,
                 ec: ExecutionContext,
                 Logger: SRLogger,
                 system: ActorSystem
               ): Future[EmailToBeSent]
  = {

    emailSendDetail.oauth2_refresh_token match {


      // FIXME: instead of error tell the user no refresh token
      case None => Future.failed(MQDoNotNackException(s"[GmailService] sendEmail scheduledEmailId (${emailSendDetail.id}): No refresh token"))

      case Some(refreshToken) =>

        val trySending = for {
          accessToken: String <- googleOAuth.checkDBAndGetAccessToken(
            email = emailSendDetail.from_email,
            emailSettingId = emailSendDetail.sender_email_settings_id,
            oauth2RefreshToken = refreshToken,
            oauth2AccessToken = emailSendDetail.oauth2_access_token.get,
            oauth2AccessTokenExpiresAt = emailSendDetail.oauth2_access_token_expires_at
          )(wsClient, ec).recoverWith { case error =>
            Logger.error(s"Failed to get access token: ${LogHelpers.getStackTraceAsString(error)}")
            Future.failed(error)
          }

          _: GmailSMTPApiSentResponse <- sendEmailService(
            accessToken = accessToken,
            emailToBeSent = emailToBeSent
          ).recoverWith { case error =>
            Logger.shouldNeverHappen(s"Failed to send email: ${LogHelpers.getStackTraceAsString(error)}")
            Future.failed(error)
          }
        } yield emailToBeSent

        trySending
      //          .andThen { case _ => wsClient.close() }

    }

  }

  /*
  def openImapConnection(email: String, accessToken: String) = Try {

    val props: Properties = new Properties()
    props.put("mail.imaps.sasl.enable", "true")
    props.put("mail.imaps.sasl.mechanisms", "XOAUTH2")
    props.put(OAuth2SaslClientFactory.OAUTH_TOKEN_PROP, accessToken)

    val session: Session = Session.getInstance(props)
    //    session.setDebug(true)

    val unusedUrlName: URLName = null
    val store: IMAPSSLStore = new IMAPSSLStore(session, unusedUrlName)

    val emptyPassword: String = ""
    store.connect(
      "imap.gmail.com",
      993,
      email,
      emptyPassword
    )

    store
  }
  */

  def openInbox(
                 store: GmailStore,
                 Logger: SRLogger
               ): Try[GmailFolder] = Try {

    //    val inbox: Folder = store.getFolder("INBOX")
    //    val inbox: GmailFolder = store.getFolder("[Gmail]/All Mail]").asInstanceOf[GmailFolder]


    val folders = store.getDefaultFolder.list("*").toSeq.asInstanceOf[Seq[IMAPFolder]]


    // REF: https://stackoverflow.com/questions/21427859/imap-list-extension-java
    // different languages have different folder name for [Gmail]/All Mail
    var inboxAll: Option[GmailFolder] = None
    val totalFolders = folders.size
    var currentFolderIndex = 0

    while (inboxAll.isEmpty && (totalFolders > currentFolderIndex)) {

      val folder = folders(currentFolderIndex)
      currentFolderIndex += 1

      if ((folder.getType & javax.mail.Folder.HOLDS_MESSAGES) != 0) {
        //        Logger.info(folder.getFullName + ":" + folder.getAttributes.mkString("  ::::  "))
        //        Logger.info(folder.getFullName + ": " + folder.getMessageCount + " :::::: " + folder.getAttributes.head)

        val isAllFolder = folder.getAttributes.head == """\All"""

        if (isAllFolder) {
          val folderName = folder.getFullName

          //          Logger.info(s"found all folder: $folderName")
          inboxAll = Some(store.getFolder(folderName).asInstanceOf[GmailFolder])
        }

      }
    }

    if (inboxAll.isEmpty) {
      Logger.error(s"[FATAL] all folder not found in gmail: $folders")
    }

    val inbox = inboxAll.get
    inbox.open(Folder.READ_ONLY)
    inbox

  }

  def openImapConnection(email: String, accessToken: String): Try[GmailStore] = Try {

    val props: Properties = new Properties()
    //    props.put("mail.store.protocol", "gimap")
    //    props.put("mail.gimap.sasl.enable", "true")
    //    props.put("mail.gimap.sasl.mechanisms", "XOAUTH2")

    props.put("mail.imap.ssl.enable", "true") // Gmail requires SSL for IMAP
    props.put("mail.imap.auth.mechanisms", "XOAUTH2") // Use XOAUTH2 mechanism
    props.put("mail.imap.auth", "true")
    props.put("mail.store.protocol", "imap")

    /* NOTE: 6-Dec-2023: This is used only in GmailService which is no longer used, after Gmail
    deprecated imap / smtp access via Google Access Tokens last year. We have since moved to Gmail ASP
    flow which goes directly via SmtpImapService instead. So, removing OAuth2SaslClientFactory
    props.put(OAuth2SaslClientFactory.OAUTH_TOKEN_PROP, accessToken)
     */


    val session: Session = Session.getInstance(props)
    //    session.setDebug(true)
    val store = session.getStore("imap").asInstanceOf[GmailStore]

    //    val emptyPassword: String = ""
    store.connect("imap.gmail.com", email, accessToken)

    store
  }

  // commented on 23102024 as this has no usage
  //  def fetchRecentMessages(
  //    emailAfterDate: DateTime,
  //    emailTillDate: Option[DateTime],
  //    email: String,
  //    accessToken: String,
  //    Logger: SRLogger
  //  ) = {
  //
  //    for {
  //      store <- openImapConnection(email = email, accessToken = accessToken)
  //      inbox <- openInbox(store = store, Logger = Logger)
  //      messages <- getRecentImapMessages(inbox = inbox, emailAfterDate = emailAfterDate, emailTillDate = emailTillDate, Logger = Logger)
  //      closeFolder <- closeInbox(inbox = inbox)
  //      closeConn <- closeImapConnection(store = store)
  //
  //    } yield {
  //      messages
  //    }
  //
  //  }


  //  def fetchRecentMessages(emailAfterDate: DateTime, email: String, accessToken: String) = Future {
  //
  //    val props: Properties = new Properties()
  //    props.put("mail.imaps.sasl.enable", "true")
  //    props.put("mail.imaps.sasl.mechanisms", "XOAUTH2")
  //    props.put(OAuth2SaslClientFactory.OAUTH_TOKEN_PROP, accessToken)
  //
  //    val session: Session = Session.getInstance(props)
  //    //    session.setDebug(true)
  //
  //    val unusedUrlName: URLName = null
  //    val store: IMAPSSLStore = new IMAPSSLStore(session, unusedUrlName)
  //
  //    val emptyPassword: String = ""
  //    store.connect(
  //      "imap.gmail.com",
  //      993,
  //      email,
  //      emptyPassword
  //    )
  //
  //    val inbox: Folder = store.getFolder("INBOX")
  //    inbox.open(Folder.READ_ONLY)
  //
  //    val messages = getRecentMessages(inbox, emailAfterDate)
  //
  //    inbox.close(true)
  //    store.close()
  //
  //    messages
  //  }

  def receiveEmail(emailSetting: EmailSetting, senderEmailSettings: Seq[EmailSetting],

                   Logger: SRLogger,
                   fromDate: DateTime,
                   tillDate: Option[DateTime]

                  )(implicit wsClient: WSClient, ec: ExecutionContext): Future[Vector[EmailMessageTracked]] = {

    emailSetting.oauth2_refresh_token match {

      case None =>

        Logger.fatal(s"[GmailService] receiveEmail: Missing Oauth refresh token")
        Future.failed(new Exception(s"${Logger.logRequestId} [GmailService] receiveEmail: Missing Oauth refresh token"))

      case Some(refreshToken) =>

        googleOAuth.checkDBAndGetAccessToken(

            email = emailSetting.email,
            emailSettingId = emailSetting.id.get.emailSettingId, // FIXME VALUECLASS
            oauth2RefreshToken = refreshToken,
            oauth2AccessToken = emailSetting.oauth2_access_token.get,
            oauth2AccessTokenExpiresAt = emailSetting.oauth2_access_token_expires_at

          )(wsClient, ec)
          .flatMap(accessToken => {


            Logger.info(s"[GmailService] receiveEmail: Fetching email new than ${fromDate.toDate}")

            val senderEmails = senderEmailSettings.map(_.email.toLowerCase)
            val senderSuffixes = senderEmailSettings.map(_.message_id_suffix)

            val tryGetFilteredMessages = for {
              store: GmailStore <- openImapConnection(emailSetting.email, accessToken)
              inbox: GmailFolder <- openInbox(store = store, Logger = Logger)
              imapMessages: Vector[Message] <- getRecentImapMessages(inbox = inbox, emailAfterDate = fromDate, emailTillDate = tillDate, Logger = Logger)
              replies: Seq[EmailMessageTrackedV4] <- prefilterNewMessagesOnly(
                messages = imapMessages.map(msg => EmailReply.ImapTrackedReply(imapMsg = msg)),
                inboxEmailSetting = emailSetting,
                senderEmails = senderEmails,
                //senderMessageIdSufixes = senderMessageIdSufixes,
                //prospectEmailsPastWeek = prospectEmailsPastWeek,
                foundInInboxFolder = Option(inbox.getFullName),
                Logger = Logger
              )
              filteredMessages: Vector[EmailMessageTracked] <- filterReplies(
                messages = replies,
                inboxEmailSetting = emailSetting,
                senderEmails = senderEmails,
                senderMessageIdSufixes = senderSuffixes,
                prospectEmailsPastWeek = emailScheduledDAOService.getSentEmailsForCheckingReplies(
                  senderEmailSettingsIds = senderEmailSettings.map(_.id.get.emailSettingId.toInt), // FIXME VALUECLASS
                  teamId = emailSetting.team_id.id, // FIXME VALUECLASS
                  logger = Logger
                ),

                //foundInInboxFolder = Option(inbox.getFullName),
                Logger = Logger

              )
              _: Unit <- closeInbox(inbox)
              _: Unit <- closeImapConnection(store)

            } yield {

              Logger.info(s"[GmailService] receiveEmail: Fetching email new than ${fromDate.toDate}: total got: ${imapMessages.length}: filtered got: ${filteredMessages.length}")

              filteredMessages
            }


            tryGetFilteredMessages.recover { case e => Logger.error(s"[GmailService] receiveEmail : DONE with error", err = e) }

            Logger.info(s"[GmailService] receiveEmail : DONE : failed: ${tryGetFilteredMessages.isFailure}")

            Future.fromTry(tryGetFilteredMessages)


          })
    }
  }

  def findTotalNumberOfEmailsInInbox(
                                      emailSetting: EmailSetting,
                                      Logger: SRLogger,
                                    )(implicit wsClient: WSClient, ec: ExecutionContext): Future[Int] = {

    emailSetting.oauth2_refresh_token match {

      case None =>

        Logger.fatal(s"[GmailService] findTotalNumberOfEmailsInInbox: Missing Oauth refresh token")
        Future.failed(new Exception(s"${Logger.logRequestId} [GmailService] findTotalNumberOfEmailsInInbox: Missing Oauth refresh token"))

      case Some(refreshToken) =>

        googleOAuth.checkDBAndGetAccessToken(

            email = emailSetting.email,
            emailSettingId = emailSetting.id.get.emailSettingId, // FIXME VALUECLASS
            oauth2RefreshToken = refreshToken,
            oauth2AccessToken = emailSetting.oauth2_access_token.get,
            oauth2AccessTokenExpiresAt = emailSetting.oauth2_access_token_expires_at

          )(wsClient, ec)
          .flatMap(accessToken => {

            val tryGetFilteredMessages = for {
              store: GmailStore <- openImapConnection(emailSetting.email, accessToken)
              inbox: GmailFolder <- openInbox(store = store, Logger = Logger)
              imapInboxMessagesCount: Int <- getTotalMessageCount(inbox = inbox, Logger = Logger)
              _: Unit <- closeInbox(inbox)
              _: Unit <- closeImapConnection(store)

            } yield {

              Logger.info(s"[GmailService] findTotalNumberOfEmailsInInbox: total email in inbox: $imapInboxMessagesCount")

              imapInboxMessagesCount
            }


            tryGetFilteredMessages.recover { case e => Logger.error(s"[GmailService] findTotalNumberOfEmailsInInbox : DONE with error", err = e) }

            Logger.info(s"[GmailService] findTotalNumberOfEmailsInInbox : DONE : failed: ${tryGetFilteredMessages.isFailure}")

            Future.fromTry(tryGetFilteredMessages)


          })
    }
  }
}
