package utils.filedownloader

import api.AppConfig
import api.accounts.TeamId

import java.io.FileOutputStream
import java.io.InputStream
import java.net.URL
import scala.util.Try

// REF: https://smarterco.de/java-download-file-from-url-to-temp-directory/
object FileDownloader {
  /**
    * Returns a path to the downloaded file
    *
    * @param url
    * @param localFilename
    * @return
    * @throws IOException
    */
  // @throws[IOException]
  def downloadFromUrl(
                       url: URL,
                       localFilename: String,
                       team_id: Option[TeamId] = None
                     ): Try[String] = Try {
    var is: InputStream = null
    var fos: FileOutputStream = null

    val tempDir = if(AppConfig.isProd) {
        "/mnt/disks/jvm-disk-main-worker/ubuntu/"
    } else System.getProperty("java.io.tmpdir")
    
    val outputPath = tempDir + "/" + localFilename

    try { //connect
      val urlConn = url.openConnection
      //get inputstream from connection
      is = urlConn.getInputStream
      fos = new FileOutputStream(outputPath)
      // 4KB buffer
      val buffer = new Array[Byte](4096)
      var length = is.read(buffer)
      // read from source and write into local file
      while (length > 0) {
        fos.write(buffer, 0, length)
        length = is.read(buffer)
      }
      outputPath
    } finally try if (is != null) is.close()
    finally if (fos != null) fos.close()
  }
}