package utils.dependencyinjectionutils

import api.CacheServiceJedis
import api.accounts.AccountService
import api.campaigns.{CampaignDAO, CampaignProspectDAO, CampaignSendReportsDAO, CampaignStepDAO, CampaignStepDAOService, CampaignStepVariantDAO}
import api.campaigns.dao.{CampaignSchedulingMetadataDAO, DripLogJedisDAO}
import api.campaigns.services.{CampaignAISequenceService, CampaignCacheService, CampaignDAOService, CampaignProspectService, CampaignProspectService2, CampaignProspectTimezonesJedisService, CampaignSendReportService, CampaignService, CampaignUpdateSettingsService}
import api.emails.CampaignProspectStepScheduleLogsDAO
import api.emails.daos.DomainPublicDNSDAO
import api.linkedin.LinkedinConnectionsDAO
import api.prospects.ProspectUpdateCategoryTemp
import api.prospects.dao.ProspectAddEventDAO
import api.prospects.dao_service.ProspectDAOService
import api.prospects.service.ProspectServiceV2
import api.sending_holiday_calendars.services.SendingHolidayService
import utils.cronjobs.CampaignSchedulingMetadataCheckCron
import utils.dbutils.DBUtils
import utils.email_notification.service.EmailNotificationService
import utils.mq.channel_scheduler.MqUpdateCampaignSchedulingMetadata
import utils.mq.{MQCampaignAISequenceGenerator, MqCampaignSendReport, MqStopInactiveCampaign, MqStuckCampaignAddLogAndReport}
import utils.mq.email.MQDomainServiceProviderDNSService
import utils.sr_product_usage_data.services.SrUserFeatureUsageEventService
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService


trait MqCampaignSendReportDI {
  lazy val campaignService: CampaignService

  lazy val mqCampaignSendReport: MqCampaignSendReport = new MqCampaignSendReport(
    campaignService = campaignService
  )
}

trait MqStuckCampaignAddLogAndReport_DI {
  lazy val campaignService: CampaignService

  lazy val mqStuckCampaignAddLogAndReport: MqStuckCampaignAddLogAndReport = new MqStuckCampaignAddLogAndReport(
    campaignService = campaignService
  )
}

trait MqStopInactiveCampaign_DI {
  lazy val campaignDAO: CampaignDAO
  //  lazy val emailNotificationService: EmailNotificationService
  lazy val campaignDAOService: CampaignDAOService
  lazy val mqStopInactiveCampaign: MqStopInactiveCampaign = new MqStopInactiveCampaign(
    campaignDAO = campaignDAO,
    //    emailNotificationService = emailNotificationService,
    campaignDAOService = campaignDAOService
  )
}
trait DripLogJedisDAO_DI {
  lazy val cacheServiceJedis: CacheServiceJedis
  lazy val dripLogJedisDAO: DripLogJedisDAO = new DripLogJedisDAO(
    cacheServiceJedis = cacheServiceJedis
  )
}

trait CampaignProspectServiceDI {
  lazy val prospectAddEventDAO: ProspectAddEventDAO
  //  lazy val prospectDAO: Prospect
  lazy val campaignService: CampaignService
  lazy val prospectServiceV2: ProspectServiceV2
  lazy val campaignProspectDAO: CampaignProspectDAO
  lazy val campaignProspectTimzonesJedisService: CampaignProspectTimezonesJedisService
  lazy val srUserFeatureUsageEventService: SrUserFeatureUsageEventService
  lazy val campaignCacheService: CampaignCacheService
  lazy val prospectDAOService: ProspectDAOService
  lazy val dbUtils: DBUtils
  lazy val campaignSchedulingMetadataDAO: CampaignSchedulingMetadataDAO
  lazy val prospectUpdateCategoryTemp: ProspectUpdateCategoryTemp
  lazy val domainPublicDNSDAO: DomainPublicDNSDAO
  lazy val mqDomainServiceProviderDNSService: MQDomainServiceProviderDNSService
  lazy val campaignProspectStepScheduleLogsDAO: CampaignProspectStepScheduleLogsDAO
  lazy val accountService: AccountService
  lazy val dripLogJedisDAO: DripLogJedisDAO
  lazy val linkedinConnectionsDAO: LinkedinConnectionsDAO

  lazy val campaignProspectService: CampaignProspectService = new CampaignProspectService(
    //    prospectDAO = prospectDAO,
    campaignService = campaignService,
    prospectAddEventDAO = prospectAddEventDAO,
    prospectServiceV2 = prospectServiceV2,
    dbUtils = dbUtils,
    campaignProspectDAO = campaignProspectDAO,
    campaignProspectTimzonesJedisService = campaignProspectTimzonesJedisService,
    srUserFeatureUsageEventService = srUserFeatureUsageEventService,
    campaignCacheService = campaignCacheService,
    prospectDAOService = prospectDAOService,
    campaignSchedulingMetadataDAO = campaignSchedulingMetadataDAO,
    prospectUpdateCategoryTemp = prospectUpdateCategoryTemp,
    domainPublicDNSDAO = domainPublicDNSDAO,
    linkedinConnectionsDAO = linkedinConnectionsDAO,
    mqDomainServiceProviderDNSService = mqDomainServiceProviderDNSService,
    campaignProspectStepScheduleLogsDAO = campaignProspectStepScheduleLogsDAO,
    accountService = accountService,
    dripLogJedisDAO = dripLogJedisDAO
  )
}

trait CampaignProspectService2DI {
  lazy val campaignProspectDAO: CampaignProspectDAO
  lazy val srRollingUpdateCoreService: SrRollingUpdateCoreService

  lazy val campaignProspectService2: CampaignProspectService2 = new CampaignProspectService2(
    srRollingUpdateCoreService = srRollingUpdateCoreService,
    campaignProspectDAO = campaignProspectDAO,

  )
}

trait CampaignCacheServiceDI {
  lazy val cacheServiceJedis: CacheServiceJedis

  lazy val campaignCacheService: CampaignCacheService = new CampaignCacheService(
    cacheServiceJedis = cacheServiceJedis
  )
}


trait CampaignAISequenceServiceDI {
  lazy val campaignDAOService: CampaignDAOService
  lazy val mqCampaignAISequenceGenerator: MQCampaignAISequenceGenerator

  lazy val campaignAISequenceService: CampaignAISequenceService = new CampaignAISequenceService(
    campaignDAOService = campaignDAOService,
    mqCampaignAISequenceGenerator = mqCampaignAISequenceGenerator
  )
}

trait CampaignSendReportsDAO_DI {
  lazy val campaignSendReportsDAO: CampaignSendReportsDAO = new CampaignSendReportsDAO()
}



trait CampaignStepDaoServiceDI {

  lazy val campaignStepDAO: CampaignStepDAO
  lazy val dbUtils: DBUtils
  lazy val campaignStepVariantDAO: CampaignStepVariantDAO

  lazy val campaignStepDAOService: CampaignStepDAOService = new CampaignStepDAOService(
    campaignStepDAO = campaignStepDAO,
    dbUtils = dbUtils,
    campaignStepVariantDAO = campaignStepVariantDAO,
  )

}


trait CampaignSendReportService_DI {
  lazy val campaignService: CampaignService
  lazy val mqCampaignSendReport: MqCampaignSendReport
  lazy val mqStuckCampaignAddLogAndReport: MqStuckCampaignAddLogAndReport
  lazy val emailNotificationService: EmailNotificationService

  lazy val campaignSendReportService: CampaignSendReportService = new CampaignSendReportService(
    campaignService = campaignService,
    mqCampaignSendReport = mqCampaignSendReport,
    mqStuckCampaignAddLogAndReport = mqStuckCampaignAddLogAndReport,
    emailNotificationService = emailNotificationService
  )
}

trait CampaignSchedulingMetadataCheckCron_DI {
  lazy val mqUpdateCampaignSchedulingMetadata: MqUpdateCampaignSchedulingMetadata
  lazy val campaignSchedulingMetadataDAO: CampaignSchedulingMetadataDAO

  lazy val campaignSchedulingMetadataCheckCron: CampaignSchedulingMetadataCheckCron = new CampaignSchedulingMetadataCheckCron(
    mqUpdateCampaignSchedulingMetadata = mqUpdateCampaignSchedulingMetadata,
    campaignSchedulingMetadataDAO = campaignSchedulingMetadataDAO
  )
}



trait CampaignUpdateSettingsServiceDI {
  lazy val campaignService: CampaignService
  lazy val sendingHolidayService: SendingHolidayService
  lazy val campaignProspectTimzonesJedisService: CampaignProspectTimezonesJedisService

  lazy val campaignUpdateSettingsService: CampaignUpdateSettingsService = new CampaignUpdateSettingsService(
    campaignProspectTimezonesJedisService = campaignProspectTimzonesJedisService,
    campaignService = campaignService,
    sendingHolidayService = sendingHolidayService
  )
}
