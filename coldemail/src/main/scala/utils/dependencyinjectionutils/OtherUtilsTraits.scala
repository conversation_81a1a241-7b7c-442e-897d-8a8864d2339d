package utils.dependencyinjectionutils

import api.{CacheService, CacheServiceJedis}
import api.accounts.{AccountDAO, AuthUtils}
import api.accounts.controller.FrontendController
import api.middleware.LoggingAction
import api.mobile_app.MobileAppController
import api.prospects.GenerateTempId
import api.timezones.TimeZoneController
import play.api.mvc.ControllerComponents
import utils.GCP.CloudStorage
import utils.cronjobs.TerminateBacklogDBConnectionCronService
import utils.dateTime.SrDateTimeUtils
import utils.dbutils.DBUtils
import utils.random.SrRandomUtils
import utils.shuffle.SrShuffleUtils
import utils.PusherService

// 25 April 2025: after the latest DependencyInjectionTraits file split, this file is no longer needed
// Do Not Add new traits to this file
// Add to other relevant files or create new files


trait SrRandomUtilsDI {
  lazy val srRandomUtils: SrRandomUtils = new SrRandomUtils
}

trait SrDateTimeUtils_DI {

  lazy val srDateTimeUtils: SrDateTimeUtils = new SrDateTimeUtils

}

trait DBUtils_DI {
  lazy val dbUtils: DBUtils = new DBUtils
}



trait GenerateTempId_DI {
  lazy val generateTempId: GenerateTempId = new GenerateTempId
}


trait CloudStorageDI {
  lazy val cloudStorage: CloudStorage = new CloudStorage()
}

trait TerminateBacklogDBConnectionCronServiceDI {
  lazy val accountDAO: AccountDAO

  lazy val terminateBacklogDBConnectionCronService: TerminateBacklogDBConnectionCronService = new TerminateBacklogDBConnectionCronService(
    accountDAO = accountDAO
  )
}


trait FrontendControllerDI {
  lazy val authUtils: AuthUtils
  lazy val loggingAction: LoggingAction

  lazy val frontendController: FrontendController = new FrontendController(
    loggingAction = loggingAction,
    authUtils = authUtils
  )
}

trait TimeZoneControllerDI {
  lazy val controllerComponents: ControllerComponents

  lazy val timeZoneController: TimeZoneController = new TimeZoneController(
    controllerComponents = controllerComponents
  )
}



trait CacheServiceDI {

  lazy val cacheServiceJedis: CacheServiceJedis

  lazy val cacheService: CacheService = new CacheService(
    cacheServiceJedis = cacheServiceJedis
  )

}


trait SrShuffleUtilsDI {
  lazy val srShuffleUtils: SrShuffleUtils = new SrShuffleUtils()
}

trait MobileAppControllerDI {
  lazy val controllerComponents: ControllerComponents

  lazy val mobileAppController: MobileAppController = new MobileAppController(
    controllerComponents = controllerComponents
  )
}

trait PusherServiceDI {
  lazy val pusherService = new PusherService()
}

