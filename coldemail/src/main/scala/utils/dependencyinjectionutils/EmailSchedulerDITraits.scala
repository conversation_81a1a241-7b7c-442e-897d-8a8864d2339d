package utils.dependencyinjectionutils

import api.CacheServiceJedis
import api.campaigns.{CampaignEditedPreviewEmailDAO, CampaignProspectDAO}
import api.emails.dao_service.EmailScheduledDAOService
import api.emails.{EmailHandleErrorService, EmailScheduledDAO, EmailSettingDAO, EmailThreadDAO}
import api.emails.services.{EmailScheduledService, SelectAndPublishForDeletionService}
import api.prospects.dao.ProspectAddEventDAO
import api.prospects.dao_service.ProspectEmailsDAOService
import api.tasks.pgDao.TaskPgDAO
import utils.cronjobs.ScheduleEmailCronServiceV2
import utils.email.{EmailBodyService, EmailServiceCompanion}
import utils.email_notification.service.EmailNotificationService
import utils.mq.channel_scheduler.{MqCallChannelScheduler, MqEmailChannelScheduler, MqGeneralC<PERSON>nelScheduler, MqLinkedinChannelScheduler, MqSmsChannelScheduler, MqWhatsAppChannelScheduler}
import utils.templating.TemplateService
import utils.uuid.SrUuidUtils
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService

trait EmailScheduledDAO_DI {
  //  lazy val prospectDAO: Prospect
  lazy val emailThreadDAO: EmailThreadDAO
  lazy val emailSettingDAO: EmailSettingDAO
  lazy val prospectAddEventDAO: ProspectAddEventDAO
  lazy val campaignProspectDAO: CampaignProspectDAO
  //  lazy val accountDAO: AccountDAO
  lazy val prospectEmailsDAOService: ProspectEmailsDAOService
  lazy val srUuidUtils: SrUuidUtils

  //  lazy val emailMessageContactModel: EmailMessageContactModel

  lazy val emailScheduledDAO: EmailScheduledDAO = new EmailScheduledDAO(
    emailThreadDAO = emailThreadDAO,
    emailSettingDAO = emailSettingDAO,
    prospectAddEventDAO = prospectAddEventDAO,
    campaignProspectDAO = campaignProspectDAO,
    //    accountDAO = accountDAO,
    //    prospectDAO = prospectDAO,
    prospectEmailsDAOService = prospectEmailsDAOService,
    srUuidUtils = srUuidUtils
    //    emailMessageContactModel = emailMessageContactModel
  )
}

trait EmailScheduledServiceDI {

  lazy val emailScheduledDAO: EmailScheduledDAO
  lazy val campaignProspectDAO: CampaignProspectDAO
  lazy val cacheServiceJedis: CacheServiceJedis
  lazy val selectAndPublishForDeletionService: SelectAndPublishForDeletionService

  lazy val emailScheduledService: EmailScheduledService = new EmailScheduledService(
    emailScheduledDAO = emailScheduledDAO,
    campaignProspectDAO = campaignProspectDAO,
    cacheServiceJedis = cacheServiceJedis,
    selectAndPublishForDeletionService = selectAndPublishForDeletionService
  )

}



trait ScheduleEmailCronServiceV2DI {
  lazy val mqEmailSchedulerV2: MqEmailChannelScheduler
  lazy val mqGeneralChannelScheduler: MqGeneralChannelScheduler
  lazy val emailSettingDAO: EmailSettingDAO
  lazy val mqWhatsAppChannelScheduler: MqWhatsAppChannelScheduler
  lazy val mqLinkedinChannelScheduler: MqLinkedinChannelScheduler
  lazy val mqSmsChannelScheduler: MqSmsChannelScheduler
  lazy val mqCallChannelScheduler: MqCallChannelScheduler
  lazy val taskDAO: TaskPgDAO

  lazy val scheduleEmailCronServiceV2: ScheduleEmailCronServiceV2 = new ScheduleEmailCronServiceV2(
    emailSettingDAO = emailSettingDAO,
    mqEmailSchedulerV2 = mqEmailSchedulerV2,
    mqWhatsAppChannelScheduler = mqWhatsAppChannelScheduler,
    mqLinkedinChannelScheduler = mqLinkedinChannelScheduler,
    mqGeneralChannelScheduler = mqGeneralChannelScheduler,
    mqSmsChannelScheduler = mqSmsChannelScheduler,
    mqCallChannelScheduler = mqCallChannelScheduler,
    taskPgDAO = taskDAO
  )
}



trait EmailHandleErrorServiceDI {
  lazy val emailScheduledDAO: EmailScheduledDAO
  lazy val emailSettingDAO: EmailSettingDAO
  lazy val emailNotificationService: EmailNotificationService
  lazy val srRollingUpdateCoreService: SrRollingUpdateCoreService
  lazy val selectAndPublishForDeletionService: SelectAndPublishForDeletionService

  lazy val emailHandleErrorService: EmailHandleErrorService = new EmailHandleErrorService(
    emailSettingDAO = emailSettingDAO,
    emailNotificationService = emailNotificationService,
    emailScheduledDAO = emailScheduledDAO,
    selectAndPublishForDeletionService = selectAndPublishForDeletionService,
    srRollingUpdateCoreService = srRollingUpdateCoreService
  )
}


trait EmailBodyServiceDI {
  lazy val emailBodyService: EmailBodyService = new EmailBodyService
}

trait EmailServiceCompanionDI {
  lazy val templateService: TemplateService
  lazy val  emailScheduledDAOService: EmailScheduledDAOService
  //  lazy val accountService: AccountService
  lazy val campaignEditedPreviewEmailDAO: CampaignEditedPreviewEmailDAO
  //  lazy val emailService: EmailService
  lazy val emailBodyService: EmailBodyService

  lazy val emailServiceCompanion: EmailServiceCompanion = new EmailServiceCompanion(
    templateService = templateService,
    campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO,
    emailScheduledDAOService = emailScheduledDAOService,
    emailBodyService = emailBodyService,
  )
}



