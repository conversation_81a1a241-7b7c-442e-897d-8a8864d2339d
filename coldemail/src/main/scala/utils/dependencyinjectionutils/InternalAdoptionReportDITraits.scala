package utils.dependencyinjectionutils

import api.accounts.dao.OrganizationDAO
import api.campaigns.CampaignDAO
import utils.customersupport.services.{InternalAdoptionReportUpdateQueueingService, InternalAdoptionReportUpdateService}
import utils.mq.internal_adoption_report.MqInternalAdoptionReportUpdater

trait InternalAdoptionReportUpdateServiceDI {

  lazy val campaignDAO: CampaignDAO

  lazy val internalAdoptionReportUpdateService: InternalAdoptionReportUpdateService = new InternalAdoptionReportUpdateService(
    campaignDAO = campaignDAO
  )
}

trait MqInternalAdoptionReportUpdaterDI {

  lazy val internalAdoptionReportUpdateService: InternalAdoptionReportUpdateService

  lazy val mqInternalAdoptionReportUpdater: MqInternalAdoptionReportUpdater = new MqInternalAdoptionReportUpdater(
    internalAdoptionReportUpdateService = internalAdoptionReportUpdateService
  )

}

trait InternalAdoptionReportUpdateQueueingServiceDI {

  lazy val organizationDAO: OrganizationDAO
  lazy val mqInternalAdoptionReportUpdater: MqInternalAdoptionReportUpdater

  lazy val internalAdoptionReportUpdateQueueingService: InternalAdoptionReportUpdateQueueingService = new InternalAdoptionReportUpdateQueueingService(
    organizationDAO = organizationDAO,
    mqInternalAdoptionReportUpdater = mqInternalAdoptionReportUpdater
  )

}