package utils.dependencyinjectionutils

import api.campaigns.CampaignProspectDAO
import api.campaigns.services.CampaignProspectService2
import api.emails.{EmailCommonService, EmailReplyTrackingModel, EmailReplyTrackingModelV2}
import api.emails.dao_service.{EmailReplyTrackingDAOService, EmailScheduledDAOService, EmailThreadDAOService}
import api.emails.models.EmailMessageContactModel
import api.prospects.{ProspectService, ProspectUpdateCategoryTemp}
import api.prospects.dao_service.ProspectDAOService
import api.prospects.service.ProspectServiceV2
import api.sr_ai.mq.MqSrAiApiPublisher
import api.team_inbox.dao.EmailTeamInboxDAO
import api.team_inbox.service.ReplySentimentService
import utils.uuid.SrUuidUtils
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService
import api.team.service.TeamService
import api.llm.dao.LlmAuditLogDAO


trait EmailReplyTrackingModelV2DI {
  lazy val emailReplyTrackingModel: EmailReplyTrackingModel
  lazy val prospectDAOService: ProspectDAOService
  lazy val prospectUpdateCategoryTemp: ProspectUpdateCategoryTemp
  lazy val emailMessageContactModel: EmailMessageContactModel
  lazy val emailReplyTrackingDAOService: EmailReplyTrackingDAOService
  lazy val replySentimentService: ReplySentimentService
  lazy val campaignProspectService2: CampaignProspectService2
  lazy val srUuidUtils: SrUuidUtils
  lazy val emailThreadDAOService: EmailThreadDAOService
  lazy val emailScheduledDAOService: EmailScheduledDAOService
  lazy val srRollingUpdateCoreService: SrRollingUpdateCoreService
  lazy val teamInboxDAO: EmailTeamInboxDAO
  lazy val emailCommonService: EmailCommonService
  lazy val teamService: TeamService
  lazy val llmAuditLogDAO: LlmAuditLogDAO

  lazy val emailReplyTrackingModelV2: EmailReplyTrackingModelV2 = new EmailReplyTrackingModelV2(
    campaignProspectService2 = campaignProspectService2,
    emailReplyTrackingModel = emailReplyTrackingModel,
    prospectDAOService = prospectDAOService,
    srUuidUtils = srUuidUtils,
    prospectUpdateCategoryTemp = prospectUpdateCategoryTemp,
    emailMessageContactModel = emailMessageContactModel,
    emailReplyTrackingDAOService = emailReplyTrackingDAOService,
    replySentimentService = replySentimentService,
    emailThreadDAOService = emailThreadDAOService,
    srRollingUpdateCoreService = srRollingUpdateCoreService,
    teamInboxDAO = teamInboxDAO,
    emailScheduledDAOService = emailScheduledDAOService,
    emailCommonService = emailCommonService,
    teamService = teamService,
    llmAuditLogDAO = llmAuditLogDAO
  )

}

trait EmailReplyTrackingDAOService_DI {
  lazy val prospectService: ProspectService
  lazy val prospectServiceV2: ProspectServiceV2
  lazy val emailReplyTrackingModel: EmailReplyTrackingModel
  lazy val campaignProspectDAO: CampaignProspectDAO
  lazy val emailReplyTrackingDAOService: EmailReplyTrackingDAOService = new EmailReplyTrackingDAOService(
    prospectService = prospectService,
    prospectServiceV2 = prospectServiceV2,
    emailReplyTrackingModel = emailReplyTrackingModel,
    campaignProspectDAO = campaignProspectDAO
  )
}

