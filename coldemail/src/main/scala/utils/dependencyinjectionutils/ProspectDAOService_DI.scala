package utils.dependencyinjectionutils

import api.accounts.AccountDAO
import api.accounts.dao_service.OrganizationDAOService
import api.campaigns.services.MergeTagService
import api.columns.ProspectColumnDef
import api.emails.EmailThreadDAO
import api.prospects.dao.ProspectDAO
import api.prospects.dao_service.ProspectDAOService
import api.search.ProspectQuery
import api.tasks.services.LinkedinTaskService
import api.team_inbox.dao_service.ReplySentimentDAOService
import api.teams_metadata.TeamsMetadataService
import utils.PlanLimitService
import utils.dbutils.DBUtils
import utils.mq.email.MQDomainServiceProviderDNSService
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService


trait ProspectDAOService_DI {
  lazy val prospectQuery: ProspectQuery
  lazy val prospectColumnDef: ProspectColumnDef
  lazy val prospectDAO: ProspectDAO
  lazy val mergeTagService: MergeTagService
  lazy val replySentimentDAOService: ReplySentimentDAOService
  lazy val emailThreadDAO: EmailThreadDAO
  lazy val dbUtils: DBUtils
  lazy val accountDAO: AccountDAO
  lazy val planLimitService: PlanLimitService
  lazy val teamsMetadataService: TeamsMetadataService
  lazy val linkedinTaskService: LinkedinTaskService
  lazy val organizationDAOService: OrganizationDAOService
  lazy val mqDomainServiceProviderDNSService: MQDomainServiceProviderDNSService
  lazy val srRollingUpdateCoreService: SrRollingUpdateCoreService

  lazy val prospectDAOService: ProspectDAOService = new ProspectDAOService(
    prospectDAO = prospectDAO,
    prospectQuery = prospectQuery,
    prospectColumnDef = prospectColumnDef,
    mergeTagService = mergeTagService,
    replySentimentDAOService = replySentimentDAOService,
    emailThreadDAO = emailThreadDAO,
    accountDAO = accountDAO,
    planLimitService = planLimitService,
    linkedinTaskService = linkedinTaskService,
    teamsMetadataService = teamsMetadataService,
    dbUtils = dbUtils,
    organizationDAOService = organizationDAOService,
    mqDomainServiceProviderDNSService = mqDomainServiceProviderDNSService,
    srRollingUpdateCoreService = srRollingUpdateCoreService
  )
}
