package utils.dependencyinjectionutils

import api.accounts.{AccountService, PermissionUtils}
import api.accounts.dao_service.OrganizationDAOService
import api.accounts.service.{AccountOrgBillingRelatedService, OrganizationService, ResetUserCacheUtil}
import api.campaigns.services.CampaignService
import api.captain_data.CaptainDataAPI
import api.linkedin.{LinkedinConnectionsDAO, LinkedinConnectionsService, LinkedinSettingController, LinkedinSettingDAO, LinkedinSettingService, LinkedinSettingUtils}
import api.linkedin_message_threads.{LinkedinMessageThreadsDAO, LinkedinMessageThreadsService}
import api.linkedin_messages.dao_service.LinkedinMessagesDAOService
import api.linkedin_messages.{LinkedinMessagesDAO, LinkedinMessagesService}
import api.middleware.LoggingAction
import api.phantombuster.{CaptainDataService, LinkedinSessionCreator, PhantomBusterApiKeysService, PhantomBusterService}
import api.phantombuster_proxy.PhantomBusterProxyService
import api.prospects.dao_service.ProspectDAOService
import api.tasks.services.TaskService
import api.teams_metadata.TeamsMetadataService
import play.api.libs.ws.WSClient
import play.api.mvc.ControllerComponents
import utils.PlanLimitService
import utils.cache_utils.service.SrRedisSimpleLockServiceV2
import utils.cronjobs.linkedin_automation.LinkedinAutomationReportService
import utils.email_notification.service.EmailNotificationService
import utils.mq.{MqLinkedinInboxScraper, MqLinkedinMessageThreadScraper, MqLinkedinRecreateSession}
import utils.mq.captainData.{MqDeleteLinkedinSetting, MqExtractLinkedinConnectionResults}
import utils.mq.captain_data.{MqCaptainDataConversationExtractionPublisher, MqCaptainDataMessageExtractionPublisher}
import utils.mq.captainData.MqExtractLinkedinConnectionResults
import utils.mq.captain_data.{MqCaptainDataConversationExtractionPublisher, MqCaptainDataCookieFailureConsumer, MqCaptainDataCookieFailurePublisher, MqCaptainDataMessageExtractionPublisher}
import utils.mq.multichannel.MqExecuteDueLinkedinTasks
import utils.proxy.brightdata.BrightDataService
import utils.random.SrRandomUtils
import utils.uuid.SrUuidUtils

object LinkedinSettingDI {

  trait LinkedinSettingControllerDI {
    lazy val loggingAction: LoggingAction
    lazy val controllerComponents: ControllerComponents
    lazy val linkedinSettingService: LinkedinSettingService
    lazy val permissionUtils: PermissionUtils
    lazy val linkedinSettingUtils: LinkedinSettingUtils
    lazy val phantomBusterService: PhantomBusterService
    lazy val wSClient: WSClient
    lazy val campaignService: CampaignService

    lazy val linkedinSettingController: LinkedinSettingController = new LinkedinSettingController(
      loggingAction = loggingAction,
      controllerComponents = controllerComponents,
      linkedinSettingService = linkedinSettingService,
      permissionUtils = permissionUtils,
      linkedinSettingUtils = linkedinSettingUtils,
      phantomBusterService = phantomBusterService,
      wSClient = wSClient,
      campaignService = campaignService
    )
  }

  // FIXME: move this to its own trait file. this doesnt belong here in LinkedinSettingDI
  trait PlanLimitServiceDI {
    lazy val organizationDAOService: OrganizationDAOService
    lazy val teamsMetadataService: TeamsMetadataService
    lazy val emailNotificationService: EmailNotificationService
    lazy val resetUserCacheUtil: ResetUserCacheUtil

    lazy val planLimitService: PlanLimitService = new PlanLimitService(
      organizationDAOService = organizationDAOService,
      teamsMetadataService = teamsMetadataService,
      resetUserCacheUtil = resetUserCacheUtil,
      emailNotificationService = emailNotificationService
    )
  }

  trait MqCaptainDataCookieFailureConsumerDI {
    lazy val mqCaptainDataCookieFailurePublisher: MqCaptainDataCookieFailurePublisher
    lazy val linkedinSettingService: LinkedinSettingService

    lazy val mqCaptainDataCookieFailureConsumer: MqCaptainDataCookieFailureConsumer = new MqCaptainDataCookieFailureConsumer(
      mqCaptainDataCookieFailurePublisher = mqCaptainDataCookieFailurePublisher,
      linkedinSettingService = linkedinSettingService
    )
  }

  trait MqCaptainDataCookieFailurePublisherDI {
    lazy val mqCaptainDataCookieFailurePublisher: MqCaptainDataCookieFailurePublisher = new MqCaptainDataCookieFailurePublisher
  }

  trait LinkedinSettingServiceDI {
    lazy val linkedinSettingDAO: LinkedinSettingDAO
    lazy val taskService: TaskService
    lazy val srUuidUtils: SrUuidUtils
    lazy val srRandomUtils: SrRandomUtils
    lazy val planLimitService: PlanLimitService
    lazy val phantomBusterProxyService: PhantomBusterProxyService
    lazy val mqExtractLinkedinConnectionResults: MqExtractLinkedinConnectionResults
    lazy val srRedisSimpleLockServiceV2: SrRedisSimpleLockServiceV2
    lazy val accountOrgBillingRelatedService: AccountOrgBillingRelatedService
    lazy val brightDataService: BrightDataService
    lazy val organizationService: OrganizationService
    lazy val captainDataApi: CaptainDataAPI
    lazy val captainDataService: CaptainDataService
    lazy val mqCaptainDataConversationExtractionPublisher: MqCaptainDataConversationExtractionPublisher
    lazy val mqCaptainDataMessageExtractionPublisher: MqCaptainDataMessageExtractionPublisher
    lazy val linkedinConnectionsDAO: LinkedinConnectionsDAO
    lazy val accountService: AccountService
    lazy val emailNotificationService: EmailNotificationService
    lazy val mqCaptainDataCookieFailurePublisher: MqCaptainDataCookieFailurePublisher

    lazy val linkedinSettingService: LinkedinSettingService = new LinkedinSettingService(
      linkedinSettingDAO = linkedinSettingDAO,
      taskService = taskService,
      organizationService = organizationService,
      accountService = accountService,
      emailNotificationService = emailNotificationService,
      planLimitService = planLimitService,
      accountOrgBillingRelatedService = accountOrgBillingRelatedService,
      srRandomUtils = srRandomUtils,
      mqExtractLinkedinConnectionResults = mqExtractLinkedinConnectionResults,
      brightDataService = brightDataService,
      phantomBusterProxyService = phantomBusterProxyService,
      srRedisSimpleLockServiceV2 = srRedisSimpleLockServiceV2,
      srUuidUtils = srUuidUtils,
      captainDataAPI = captainDataApi,
      captainDataService = captainDataService,
      linkedinConnectionsDAO = linkedinConnectionsDAO,
      mqCaptainDataConversationExtractionPublisher = mqCaptainDataConversationExtractionPublisher,
      mqCaptainDataMessageExtractionPublisher = mqCaptainDataMessageExtractionPublisher,
      mqCaptainDataCookieFailurePublisher = mqCaptainDataCookieFailurePublisher
    )
  }

  trait LinkedinSettingDAODI {
    lazy val linkedinSettingDAO: LinkedinSettingDAO = new LinkedinSettingDAO
  }

  trait LinkedinSettingUtilsDI {
    lazy val linkedinSettingService: LinkedinSettingService

    lazy val linkedinSettingUtils: LinkedinSettingUtils = new LinkedinSettingUtils(
      linkedinSettingService = linkedinSettingService
    )
  }

}

trait MqDeleteLinkedinSettingDI {
  lazy val linkedinSettingService: LinkedinSettingService

  lazy val mqDeleteLinkedinSetting: MqDeleteLinkedinSetting = new MqDeleteLinkedinSetting(
    linkedinSettingService = linkedinSettingService
  )
}


trait MqExecuteDueLinkedinTasksDI {
  lazy val linkedinSettingService: LinkedinSettingService
  lazy val taskService: TaskService

  lazy val mqExecuteDueLinkedinTasks: MqExecuteDueLinkedinTasks = new MqExecuteDueLinkedinTasks(
    linkedinSettingService = linkedinSettingService,
    taskService = taskService
  )
}


trait LinkedinSessionCreatorDI {
  lazy val linkedinSettingService: LinkedinSettingService
  lazy val srUuidUtils: SrUuidUtils

  lazy val linkedinSessionCreator: LinkedinSessionCreator = new LinkedinSessionCreator(
    srUuidUtils = srUuidUtils,
    linkedinSettingService = linkedinSettingService
  )
}



trait LinkedinMessagesServiceDI {
  lazy val linkedinMessagesDAO: LinkedinMessagesDAO
  lazy val srUuidUtils: SrUuidUtils

  lazy val linkedinMessagesService: LinkedinMessagesService = new LinkedinMessagesService(
    linkedinMessagesDAO = linkedinMessagesDAO,
    srUuidUtils = srUuidUtils
  )
}

trait LinkedinMessagesDAODI {
  lazy val linkedinMessagesDAO: LinkedinMessagesDAO = new LinkedinMessagesDAO()
}
trait LinkedinMessagesDAOService_DI {
  lazy val linkedinMessagesDAO: LinkedinMessagesDAO

  lazy val linkedinMessagesDAOService: LinkedinMessagesDAOService = new LinkedinMessagesDAOService(
    linkedinMessagesDAO = linkedinMessagesDAO
  )
}


// Date: 2025-06-02 : Commenting this code as we are not using phantom buster for linkedin automation
//trait MqLinkedinInboxScraperDI {
//  lazy val phantomBusterService: PhantomBusterService
//
//  lazy val mqLinkedinInboxScraper: MqLinkedinInboxScraper = new MqLinkedinInboxScraper(
//    phantomBusterService = phantomBusterService
//  )
//}



trait LinkedinMessageThreadsDAODI {
  lazy val linkedinMessageThreadsDAO: LinkedinMessageThreadsDAO = new LinkedinMessageThreadsDAO()
}

trait LinkedinMessageThreadsServiceDI {
  lazy val prospectDAOService: ProspectDAOService
  lazy val linkedinSettingService: LinkedinSettingService
  lazy val srUuidUtils: SrUuidUtils
  lazy val taskService: TaskService
  lazy val linkedinMessageThreadsDAO: LinkedinMessageThreadsDAO

  lazy val linkedinMessageThreadsService: LinkedinMessageThreadsService = new LinkedinMessageThreadsService(
    prospectDAOService = prospectDAOService,
    linkedinSettingService = linkedinSettingService,
    srUuidUtils = srUuidUtils,
    taskService = taskService,
    linkedinMessageThreadsDAO = linkedinMessageThreadsDAO
  )
}

trait LinkedinConnectionsServiceDI {
  lazy val prospectDAOService: ProspectDAOService
  lazy val srUuidUtils: SrUuidUtils
  lazy val taskService: TaskService
  lazy val linkedinConnectionsDAO: LinkedinConnectionsDAO
  lazy val captainDataApi: CaptainDataAPI
  lazy val linkedinSettingDAO: LinkedinSettingDAO

  lazy val linkedinConnectionsService: LinkedinConnectionsService = new LinkedinConnectionsService(
    prospectDAOService = prospectDAOService,
    srUuidUtils = srUuidUtils,
    taskService = taskService,
    linkedinConnectionsDAO = linkedinConnectionsDAO,
    captainDataApi = captainDataApi,
    linkedinSettingDAO = linkedinSettingDAO
  )

}

trait LinkedinConnectionDAODI {
  lazy val linkedinConnectionsDAO: LinkedinConnectionsDAO = new LinkedinConnectionsDAO()
}


trait LinkedinAutomationReportServiceDI {
  lazy val phantomBusterProxyService: PhantomBusterProxyService
  lazy val phantomBusterApiKeysService: PhantomBusterApiKeysService
  lazy val emailNotificationService: EmailNotificationService

  lazy val linkedinAutomationReportService: LinkedinAutomationReportService = new LinkedinAutomationReportService(
    phantomBusterProxyService = phantomBusterProxyService,
    phantomBusterApiKeysService = phantomBusterApiKeysService,
    emailNotificationService = emailNotificationService
  )
}


// Date: 2025-06-02 : Commenting this code as we are not using phantom buster for linkedin automation
//trait MqLinkedinMessageThreadScraperDI {
//  lazy val phantomBusterService: PhantomBusterService
//
//  lazy val mqLinkedinMessageThreadScraper: MqLinkedinMessageThreadScraper = new MqLinkedinMessageThreadScraper(
//    phantomBusterService = phantomBusterService
//  )
//}

// Date: 2025-06-02 : Commenting this code as we are not using phantom buster for linkedin automation
//trait MqLinkedinRecreateSessionDI {
//  lazy val phantomBusterService: PhantomBusterService
//
//  lazy val mqLinkedinRecreateSession: MqLinkedinRecreateSession = new MqLinkedinRecreateSession(
//    phantomBusterService = phantomBusterService
//  )
//}


trait LinkedinSettingDAO_DI {
  lazy val linkedinSettingDAO: LinkedinSettingDAO = new LinkedinSettingDAO
}


