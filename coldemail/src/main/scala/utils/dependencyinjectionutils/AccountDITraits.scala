package utils.dependencyinjectionutils

import api.accounts.AccountDAO
import api.accounts.dao.{AccountDAOV2, OrganizationDAO, OrganizationJedisCacheDao}
import api.accounts.dao_service.{AccountDAOService, OrganizationDAOService}
import utils.sr_resource.services.SrResourceWithoutTenantDaoService
import utils.uuid.SrUuidUtils


trait AccountDAOService_DI {
  lazy val accountDAO: AccountDAO
  lazy val srResourceWithoutTenantDaoService: SrResourceWithoutTenantDaoService
  lazy val organizationJedisCacheDao: OrganizationJedisCacheDao
  lazy val organizationDAOService: OrganizationDAOService
  lazy val accountDAOService: AccountDAOService = new AccountDAOService(
    accountDAO = accountDAO,
    srResourceWithoutTenantDaoService = srResourceWithoutTenantDaoService,
    organizationJedisCacheDao = organizationJedisCacheDao,
    organizationDAOService = organizationDAOService
  )
}


trait AccountDAO_DI {

  lazy val srUuidUtils: SrUuidUtils
  lazy val accountDAO: AccountDAO = new AccountDAO(
    srUuidUtils = srUuidUtils
  )
}


trait AccountDAOV2_DI {
  lazy val organizationDAO: OrganizationDAO
  lazy val accountDAOV2: AccountDAOV2 = new AccountDAOV2(
    organizationDAO = organizationDAO
  )
}