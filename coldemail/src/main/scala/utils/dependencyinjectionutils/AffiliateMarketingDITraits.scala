package utils.dependencyinjectionutils

import api.accounts.{AccountDAO, AccountService, PermissionUtils}
import api.accounts.dao.{OrganizationBillingDAO, OrganizationDAO}
import api.accounts.referral.ReferralController
import api.billing.v2.BillingAppService
import play.api.libs.ws.WSClient
import play.api.mvc.ControllerComponents
import utils.affiliate.{Admitad, AffiliateDAO, AffiliateTrackingService, FirstPromoter, ScheduleAffiliateApprovalsCronService}
import utils.customersupport.dao.InternalCSDReportDAO
import utils.customersupport.services.ScheduleUpdateAcquisitionReportService
import utils.email_notification.service.EmailNotificationService
import utils.mq.affiliate.{MQAffiliateAutoApprovalConsumer, MQAffiliateAutoApprovalPublisher}


trait AffiliateDAO_DI {
  lazy val affiliateDAO: AffiliateDAO = new AffiliateDAO
}

trait ReferralControllerDI {
  implicit lazy val wSClient: WSClient
  lazy val controllerComponents: ControllerComponents
  lazy val affiliateTrackingService: AffiliateTrackingService
  lazy val permissionUtils: PermissionUtils

  lazy val referralController: ReferralController = new ReferralController(
    controllerComponents = controllerComponents,
    affiliateTrackingService = affiliateTrackingService,
    permissionUtils = permissionUtils,
    wsClient = wSClient
  )
}

trait FirstPromoterDI {
  lazy val accountDAO: AccountDAO
  lazy val emailNotificationService: EmailNotificationService

  lazy val firstPromoter: FirstPromoter = new FirstPromoter(
    accountDAO = accountDAO,
    emailNotificationService = emailNotificationService
  )
}

trait AffiliateTrackingServiceDI {
  lazy val organizationBillingDAO: OrganizationBillingDAO
  lazy val firstPromoter: FirstPromoter
  lazy val admitad: Admitad
  lazy val organizationDAO: OrganizationDAO
  lazy val accountService: AccountService
  lazy val affiliateDAO: AffiliateDAO
  lazy val emailNotificationService: EmailNotificationService

  lazy val affiliateTrackingService: AffiliateTrackingService = new AffiliateTrackingService(
    organizationBillingDAO = organizationBillingDAO,
    firstPromoter = firstPromoter,
    organizationDAO = organizationDAO,
    admitad = admitad,
    accountService = accountService,
    affiliateDAO = affiliateDAO,
    emailNotificationService = emailNotificationService,
  )
}

trait AdmitadDI {
  lazy val accountDAO: AccountDAO

  lazy val admitad: Admitad = new Admitad(
    accountDAO = accountDAO,
  )
}

trait MQAffiliateAutoApprovalPublisher_DI {
  lazy val mqAffiliateAutoApprovalPublisher: MQAffiliateAutoApprovalPublisher = new MQAffiliateAutoApprovalPublisher
}

trait MQAffiliateAutoApprovalConsumer_DI {

  lazy val mqAffiliateAutoApprovalPublisher: MQAffiliateAutoApprovalPublisher
  lazy val affiliateTrackingService: AffiliateTrackingService
  lazy val billingAppService: BillingAppService

  lazy val mqAffiliateAutoApprovalConsumer: MQAffiliateAutoApprovalConsumer = new MQAffiliateAutoApprovalConsumer(
    mqAffiliateAutoApprovalPublisher = mqAffiliateAutoApprovalPublisher,
    affiliateTrackingService = affiliateTrackingService,
    billingAppService = billingAppService,
  )

}



trait ScheduleAffiliateApprovalsCronServiceDI {

  lazy val billingAppService: BillingAppService
  lazy val mqAffiliateAutoApprovalPublisher: MQAffiliateAutoApprovalPublisher

  lazy val scheduleAffiliateApprovalsCronService: ScheduleAffiliateApprovalsCronService = new ScheduleAffiliateApprovalsCronService(
    billingAppService = billingAppService,
    mqAffiliateAutoApprovalPublisher = mqAffiliateAutoApprovalPublisher,
  )

}

trait ScheduleUpdateAcquisitionReportServiceDI {
  lazy val billingAppService: BillingAppService
  lazy val internalCSDReportDAO: InternalCSDReportDAO

  lazy val scheduleUpdateAcquisitionReportService: ScheduleUpdateAcquisitionReportService = new ScheduleUpdateAcquisitionReportService(
    billingAppService = billingAppService,
    internalCSDReportDAO = internalCSDReportDAO
  )
}


