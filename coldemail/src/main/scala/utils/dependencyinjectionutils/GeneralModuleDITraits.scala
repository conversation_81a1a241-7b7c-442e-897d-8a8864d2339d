package utils.dependencyinjectionutils

import api.accounts.{AccountDAO, PermissionUtils}
import api.emails.EmailSettingDAO
import api.general.{GeneralSettingDAO, GeneralSettingService}
import api.generalmodule.{GeneralModule, GeneralModuleController, GeneralModuleService}
import api.prospects.dao.ProspectAddEventDAO
import api.prospects.service.ProspectServiceV2
import play.api.mvc.ControllerComponents

trait GeneralModuleDI {
  lazy val generalModule: GeneralModule = new GeneralModule
}

trait GeneralModuleServiceDI {
  lazy val prospectAddEventDAO: ProspectAddEventDAO
  lazy val accountDAO: AccountDAO
  lazy val generalModule: GeneralModule
  lazy val prospectServiceV2: ProspectServiceV2
  lazy val emailSettingDAO: EmailSettingDAO

  lazy val generalModuleService: GeneralModuleService = new GeneralModuleService(
    accountDAO = accountDAO,
    prospectAddEventDAO = prospectAddEventDAO,
    generalModule = generalModule,
    prospectServiceV2 = prospectServiceV2,
    emailSettingDAO = emailSettingDAO
  )
}


trait GeneralModuleControllerDI {

  lazy val generalModuleService: GeneralModuleService
  lazy val permissionUtils: PermissionUtils
  lazy val controllerComponents: ControllerComponents

  lazy val generalModuleController: GeneralModuleController = new GeneralModuleController(
    controllerComponents = controllerComponents,
    permissionUtils = permissionUtils,
    generalModuleService = generalModuleService
  )
}



trait GeneralSettingDAO_DI {
  lazy val generalSettingDAO: GeneralSettingDAO = new GeneralSettingDAO
}


trait GeneralSettingServiceDI {
  lazy val generalSettingDAO: GeneralSettingDAO

  lazy val generalSettingService: GeneralSettingService = new GeneralSettingService(
    generalSettingDAO = generalSettingDAO
  )
}

