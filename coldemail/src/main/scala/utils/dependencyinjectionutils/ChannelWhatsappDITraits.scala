package utils.dependencyinjectionutils

import play.api.mvc.ControllerComponents
import api.whatsapp.{WhatsappSettingService, WhatsappSettingUtils, WhatsappSettingController, WhatsappSettingDAO}
import utils.uuid.SrUuidUtils
import api.accounts.PermissionUtils
import utils.cache_utils.service.SrRedisSimpleLockServiceV2
import api.campaigns.services.CampaignService

object WhatsappSettingDI {

  trait WhatsappSettingControllerDI {
    lazy val controllerComponents: ControllerComponents
    lazy val whatsappSettingService: WhatsappSettingService
    lazy val whatsappSettingUtils: WhatsappSettingUtils
    lazy val permissionUtils: PermissionUtils
    lazy val campaignService: CampaignService

    lazy val whatsappSettingController: WhatsappSettingController = new WhatsappSettingController(
      controllerComponents = controllerComponents,
      whatsappSettingService = whatsappSettingService,
      whatsappSettingUtils = whatsappSettingUtils,
      permissionUtils = permissionUtils,
      campaignService = campaignService
    )
  }

  trait WhatsappSettingServiceDI {
    lazy val whatsappSettingDAO: WhatsappSettingDAO
    lazy val srUuidUtils: SrUuidUtils
    lazy val srRedisSimpleLockServiceV2: SrRedisSimpleLockServiceV2

    lazy val whatsappSettingService: WhatsappSettingService = new WhatsappSettingService(
      whatsappSettingDAO = whatsappSettingDAO,
      srRedisSimpleLockServiceV2 = srRedisSimpleLockServiceV2,
      srUuidUtils = srUuidUtils
    )
  }

  trait WhatsappSettingDAODI {
    lazy val whatsappSettingDAO: WhatsappSettingDAO = new WhatsappSettingDAO
  }

  trait WhatsappSettingUtilsDI {
    lazy val whatsappSettingService: WhatsappSettingService

    lazy val whatsappSettingUtils: WhatsappSettingUtils = new WhatsappSettingUtils(
      whatsappSettingService = whatsappSettingService
    )
  }
}