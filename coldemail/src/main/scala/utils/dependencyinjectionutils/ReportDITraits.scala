package utils.dependencyinjectionutils

import api.CacheServiceJedis
import api.accounts.{AccountService, PermissionUtils}
import api.accounts.dao.TeamsDAO
import api.campaigns.{CampaignDAO, CampaignStepVariantDAO}
import api.columns.ProspectColumnDef
import api.domain_health.DomainHealthCheckService
import api.pipelines.services.OpportunityService
import api.reports.{DownloadReportDAO, ReportController, ReportDAO, ReportDaoService, ReportService}
import api.reports.services.{DownloadReportService, InboxPlacementCheckLogsReportService, ReportDaoJedisService, TopReportsDaoJedisService}
import api.spamtest.SpamTestService
import api.tags.ProspectTagDAOLegacy
import api.tags.dao.CampaignTagDAO
import api.team_inbox.dao_service.ReplySentimentDAOService
import api.team_inbox.service.ReplySentimentService
import api.templates.TemplateDAO
import play.api.libs.ws.WSClient
import play.api.mvc.ControllerComponents
import utils.dbutils.DBUtils
import utils.mq.MqResetCacheForReport


trait ReportDaoJedisServiceDI {
  lazy val cacheServiceJedis: CacheServiceJedis

  lazy val reportDaoJedisService: ReportDaoJedisService = new ReportDaoJedisService(
    cacheServiceJedis = cacheServiceJedis
  )
}

trait TopReportsDaoJedisServiceDI {
  lazy val cacheServiceJedis: CacheServiceJedis

  lazy val topReportsDaoJedisService: TopReportsDaoJedisService = new TopReportsDaoJedisService(
    cacheServiceJedis = cacheServiceJedis
  )
}

trait ReportDaoService_DI {
  lazy val reportDAO: ReportDAO
  lazy val reportDaoJedisService: ReportDaoJedisService
  lazy val topReportsDaoJedisService: TopReportsDaoJedisService

  lazy val reportDaoService: ReportDaoService = new ReportDaoService(
    reportDAO = reportDAO,
    topReportsDaoJedisService = topReportsDaoJedisService,
    reportDaoJedisService = reportDaoJedisService
  )
}

trait ReportServiceDI {
  lazy val templateDAO: TemplateDAO
  lazy val downloadReportDAO: DownloadReportDAO
  lazy val campaignTagDAO: CampaignTagDAO
  lazy val reportDAO: ReportDAO
  lazy val prospectColumnDef: ProspectColumnDef
  lazy val replySentimentService: ReplySentimentService
  lazy val campaignDAO: CampaignDAO
  lazy val teamDAO: TeamsDAO
  lazy val dbUtils: DBUtils
  lazy val reportDaoService: ReportDaoService
  lazy val replySentimentDAOService: ReplySentimentDAOService
  lazy val accountService: AccountService
  lazy val opportunityService: OpportunityService
  lazy val campaignStepVariantDAO: CampaignStepVariantDAO


  lazy val reportService: ReportService = new ReportService(
    templateDAO = templateDAO,
    downloadReportDAO = downloadReportDAO,
    reportDAO = reportDAO,
    campaignTagDAO = campaignTagDAO,
    prospectColumnDef = prospectColumnDef,
    campaignDAO = campaignDAO,
    dbUtils = dbUtils,
    teamDAO = teamDAO,
    replySentimentService = replySentimentService,
    reportDaoService = reportDaoService,
    replySentimentDAOService = replySentimentDAOService,
    accountService = accountService,
    campaignStepVariantDAO = campaignStepVariantDAO,
    opportunityService = opportunityService
  )

}

trait DownloadReportServiceDI {
  lazy val downloadReportDAO: DownloadReportDAO
  lazy val campaignTagDAO: CampaignTagDAO
  lazy val prospectColumnDef: ProspectColumnDef

  lazy val downloadReportService: DownloadReportService = new DownloadReportService(
    downloadReportDAO = downloadReportDAO,
    campaignTagDAO = campaignTagDAO,
    prospectColumnDef = prospectColumnDef
  )
}

trait ReportControllerDI {

  lazy val reportService: ReportService
  lazy val downloadReportService: DownloadReportService
  lazy val reportDAO: ReportDAO
  lazy val spamTestService: SpamTestService
  lazy val permissionUtils: PermissionUtils
  lazy val controllerComponents: ControllerComponents
  lazy val prospectTagDAOLegacy: ProspectTagDAOLegacy
  lazy val domainHealthCheckService: DomainHealthCheckService
  lazy val inboxPlacementCheckLogsReportService: InboxPlacementCheckLogsReportService
  implicit lazy val wsClient: WSClient

  lazy val reportController: ReportController = new ReportController(
    permissionUtils = permissionUtils,
    reportService = reportService,
    spamTestService = spamTestService,
    domainHealthCheckService = domainHealthCheckService,
    downloadReportService = downloadReportService,
    controllerComponents = controllerComponents,
    reportDAO = reportDAO,
    prospectTagDAOLegacy = prospectTagDAOLegacy,
    inboxPlacementCheckLogsReportService = inboxPlacementCheckLogsReportService,
    wsClient = wsClient
  )
}

trait ReportDAO_DI {
  lazy val campaignStepVariantDAO: CampaignStepVariantDAO

  lazy val reportDAO: ReportDAO = new ReportDAO(
    campaignStepVariantDAO = campaignStepVariantDAO,
  )
}

trait DownloadReportDAO_DI {
  lazy val downloadReportDAO: DownloadReportDAO = new DownloadReportDAO
}


trait MqResetCacheForReportDI {
  lazy val reportDaoJedisService: ReportDaoJedisService

  lazy val mqResetCacheForReport: MqResetCacheForReport = new MqResetCacheForReport(
    reportDaoJedisService = reportDaoJedisService
  )
}



