package utils.dependencyinjectionutils

import api.accounts.AccountService
import api.accounts.service.ResetUserCacheUtil
import api.calendar_app.CalendarAppService
import api.call.DAO.CallDAO
import api.campaigns.{CampaignDAO, CampaignEditedPreviewEmailDAO, CampaignProspectDAO, CampaignStepDAO, CampaignStepDAOService, CampaignStepVariantDAO}
import api.campaigns.dao.{CampaignEmailSettingsDAO, CampaignProspectDAO_2, CampaignSchedulingMetadataDAO}
import api.campaigns.services.{CampaignCacheService, CampaignDAOService, CampaignPreviewService, CampaignProspectDAOService, CampaignService, CampaignStartService, CampaignStepService, CampaignTemplateService, CampaignTestStepService, CampaignsMissingMergeTagService, ChannelSettingService, MergeTagService}
import api.columns.ProspectColumnDef
import api.emails.{EmailScheduledDAO, EmailSettingDAO}
import api.emails.dao_service.EmailScheduledDAOService
import api.emails.services.SelectAndPublishForDeletionService
import api.general.{GeneralSettingDAO, GeneralSettingService}
import api.gpt.ai_hyperpersonalized.AIHyperPersonalizedGenerator
import api.linkedin.LinkedinSettingDAO
import api.prospects.ProspectAccountDAO1
import api.prospects.dao.ProspectAddEventDAO
import api.prospects.dao_service.{ProspectDAOService, ProspectDAOServiceV2, ProspectEmailsDAOService}
import api.prospects.service.ProspectServiceV2
import api.search.ProspectQuery
import api.sms.SmsSettingDAO
import api.spammonitor.dao.EmailSendingStatusDAO
import api.tasks.services.TaskService
import api.templates.TemplateUtils
import api.whatsapp.WhatsappSettingDAO
import app_services.db_query_counter.SrDBQueryCounterService
import utils.PlanLimitService
import utils.cronjobs.spammonitor.ActiveCampaignImpl
import utils.dbutils.DBUtils
import utils.email.{EmailSenderService, EmailServiceCompanion}
import utils.mq.channel_scheduler.MqEmailChannelScheduler
import utils.phishing.PhishingCheckService
import utils.sr_product_usage_data.services.SrUserFeatureUsageEventService
import utils.templating.TemplateService
import utils.uuid.SrUuidUtils
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService


object Campaign {

  trait ChannelSettingServiceDI {
    lazy val campaignDAO: CampaignDAO
    lazy val campaignEmailSettingsDAO: CampaignEmailSettingsDAO
    lazy val linkedinSettingDAO: LinkedinSettingDAO
    lazy val callDAO: CallDAO
    lazy val smsSettingDAO: SmsSettingDAO
    lazy val generalSettingDAO: GeneralSettingDAO
    lazy val whatsappSettingDAO: WhatsappSettingDAO
    lazy val emailSettingDAO: EmailSettingDAO

    lazy val channelSettingService: ChannelSettingService = new ChannelSettingService(
      campaignDAO = campaignDAO,
      campaignEmailSettingsDAO = campaignEmailSettingsDAO,
      linkedinSettingDAO = linkedinSettingDAO,
      callDAO = callDAO,
      smsSettingDAO = smsSettingDAO,
      whatsappSettingDAO = whatsappSettingDAO,
      generalSettingDAO = generalSettingDAO,
      emailSettingDAO = emailSettingDAO
    )
  }

  trait CampaignPreviewServiceDI {
    lazy val campaignEditedPreviewEmailDAO: CampaignEditedPreviewEmailDAO
    lazy val prospectDAOService: ProspectDAOService
    lazy val emailScheduledDAOService: EmailScheduledDAOService
    lazy val campaignStepVariantDAO: CampaignStepVariantDAO
    lazy val prospectAccountDAO: ProspectAccountDAO1
    lazy val channelSettingService: ChannelSettingService
    lazy val templateService: TemplateService

    lazy val emailSettingDAO: EmailSettingDAO
    lazy val emailServiceCompanion: EmailServiceCompanion
    lazy val taskService: TaskService
    lazy val accountService: AccountService
    lazy val campaignDAOService: CampaignDAOService
    lazy val calendarAppService: CalendarAppService
    lazy val campaignService: CampaignService
    lazy val aiHyperPersonalizedGenerator: AIHyperPersonalizedGenerator
    


    lazy val campaignPreviewService: CampaignPreviewService = new CampaignPreviewService(
      campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO,
      prospectDAOService = prospectDAOService,
      emailScheduledDAOService = emailScheduledDAOService,
      campaignStepVariantDAO = campaignStepVariantDAO,
      prospectAccountDAO = prospectAccountDAO,
      channelSettingService = channelSettingService,
      templateService = templateService,
      emailSettingDAO = emailSettingDAO,
      emailServiceCompanion = emailServiceCompanion,
      taskService = taskService,
      accountService = accountService,
      campaignDAOService = campaignDAOService,
      calendarAppService = calendarAppService,
      campaignService = campaignService,
      aiHyperPersonalizedGenerator = aiHyperPersonalizedGenerator
    )
  }

  trait CampaignStartServiceDI {
    lazy val resetUserCacheUtil: ResetUserCacheUtil
    //    lazy val phishingCheckService: PhishingCheckService
    lazy val mqEmailSchedulerV2: MqEmailChannelScheduler
    lazy val campaignService: CampaignService
    lazy val srUserFeatureUsageEventService: SrUserFeatureUsageEventService
    lazy val accountService: AccountService
    lazy val emailSettingDAO: EmailSettingDAO
    lazy val emailSendingStatusDAO: EmailSendingStatusDAO
    lazy val campaignStepVariantDAO: CampaignStepVariantDAO
    lazy val generalSettingService: GeneralSettingService
    lazy val campaignDAO: CampaignDAO
    lazy val planLimitService: PlanLimitService
    lazy val campaignSchedulingMetadataDAO: CampaignSchedulingMetadataDAO
    lazy val campaignDAOService: CampaignDAOService
    lazy val campaignCacheService: CampaignCacheService


    lazy val campaignStartService: CampaignStartService = new CampaignStartService(
      resetUserCacheUtil = resetUserCacheUtil,
      //      phishingCheckService = phishingCheckService,
      mqEmailSchedulerV2 = mqEmailSchedulerV2,
      campaignService = campaignService,
      srUserFeatureUsageEventService = srUserFeatureUsageEventService,
      accountService = accountService,
      emailSettingDAO = emailSettingDAO,
      planLimitService = planLimitService,
      emailSendingStatusDAO = emailSendingStatusDAO,
      campaignCacheService = campaignCacheService,
      campaignStepVariantDAO = campaignStepVariantDAO,
      generalSettingService = generalSettingService,
      campaignDAO = campaignDAO,
      campaignSchedulingMetadataDAO = campaignSchedulingMetadataDAO,
      campaignDAOService = campaignDAOService
    )
  }

  trait CampaignStepServiceDI {

    lazy val campaignTemplateService: CampaignTemplateService
    lazy val campaignStepDAO: CampaignStepDAO
    lazy val templateUtils: TemplateUtils
    lazy val campaignStepVariantDAO: CampaignStepVariantDAO
    lazy val srUserFeatureUsageEventService: SrUserFeatureUsageEventService
    lazy val campaignDAOService: CampaignDAOService
    lazy val campaignStepDAOService: CampaignStepDAOService
    lazy val campaignService: CampaignService

    lazy val campaignStepService: CampaignStepService = new CampaignStepService(
      campaignTemplateService = campaignTemplateService,
      campaignStepDAO = campaignStepDAO,
      templateUtils = templateUtils,
      campaignStepVariantDAO = campaignStepVariantDAO,
      srUserFeatureUsageEventService = srUserFeatureUsageEventService,
      campaignDAOService = campaignDAOService,
      campaignStepDAOService = campaignStepDAOService,
      campaignService = campaignService,
    )

  }

  trait CampaignTestStepServiceDI {

    lazy val prospectDAOService: ProspectDAOService
    lazy val campaignTemplateService: CampaignTemplateService
    lazy val prospectAccountDAO: ProspectAccountDAO1
    //    lazy val campaignStepDAO: CampaignStepDAO

    lazy val templateService: TemplateService
    lazy val emailSettingDAO: EmailSettingDAO
    lazy val emailSenderService: EmailSenderService
    lazy val emailServiceCompanion: EmailServiceCompanion
    lazy val emailScheduledDAOService: EmailScheduledDAOService

    lazy val campaignDAOService: CampaignDAOService
    lazy val srUuidUtils: SrUuidUtils

    lazy val campaignTestStepService: CampaignTestStepService = new CampaignTestStepService(
      prospectDAOService = prospectDAOService,
      campaignTemplateService = campaignTemplateService,
      prospectAccountDAO = prospectAccountDAO,
      //      campaignStepDAO = campaignStepDAO,
      templateService = templateService,
      emailSettingDAO = emailSettingDAO,
      emailScheduledDAOService = emailScheduledDAOService,
      emailSenderService = emailSenderService,
      emailServiceCompanion = emailServiceCompanion,
      campaignDAOService = campaignDAOService,
      srUuidUtils = srUuidUtils
    )
  }

}


trait CampaignsMissingMergeTagServiceDI {
  lazy val campaignProspectDAO: CampaignProspectDAO
  lazy val campaignCacheService: CampaignCacheService
  lazy val prospectColumnDef: ProspectColumnDef

  lazy val campaignsMissingMergeTagService: CampaignsMissingMergeTagService = new CampaignsMissingMergeTagService(
    campaignProspectDAO = campaignProspectDAO,
    campaignCacheService = campaignCacheService,
    prospectColumnDef = prospectColumnDef,
  )
}


trait CampaignEditedPreviewEmailDAO_DI {
  lazy val campaignProspectDAO_2: CampaignProspectDAO_2
  lazy val mergeTagService: MergeTagService

  lazy val campaignEditedPreviewEmailDAO: CampaignEditedPreviewEmailDAO = new CampaignEditedPreviewEmailDAO(
    campaignProspectDAO_2 = campaignProspectDAO_2,
    mergeTagService = mergeTagService
  )
}

trait CampaignStepDAO_DI {
  lazy val campaignDAO: CampaignDAO
  lazy val emailScheduledDAO: EmailScheduledDAO
  lazy val campaignProspectDAO: CampaignProspectDAO


  lazy val campaignStepDAO: CampaignStepDAO = new CampaignStepDAO(
    campaignDAO = campaignDAO,
    emailScheduledDAO = emailScheduledDAO,
    campaignProspectDAO = campaignProspectDAO
  )
}

trait CampaignDAO_DI {
  lazy val srUuidUtils: SrUuidUtils
  lazy val srDBQueryCounterService: SrDBQueryCounterService
  lazy val campaignDAO: CampaignDAO = new CampaignDAO(
    srDBQueryCounterService = srDBQueryCounterService,
    srUuidUtils = srUuidUtils
  )
}

trait CampaignProspectDAO_DI {
  lazy val prospectDAOService: ProspectDAOService
  lazy val prospectDAOServiceV2: ProspectDAOServiceV2
  lazy val prospectQuery: ProspectQuery
  lazy val srDBQueryCounterService: SrDBQueryCounterService
  lazy val prospectAddEventDAO: ProspectAddEventDAO
  lazy val prospectEmailsDAOService: ProspectEmailsDAOService
  lazy val srRollingUpdateCoreService: SrRollingUpdateCoreService

  lazy val campaignProspectDAO = new CampaignProspectDAO(
    prospectDAOService = prospectDAOService,
    prospectDAOServiceV2 = prospectDAOServiceV2,
    prospectQuery = prospectQuery,
    srDBQueryCounterService = srDBQueryCounterService,
    prospectAddEventDAO = prospectAddEventDAO,
    prospectEmailsDAOService = prospectEmailsDAOService,
    srRollingUpdateCoreService = srRollingUpdateCoreService
  )
}


trait CampaignStepVariantDAO_DI {
  lazy val emailScheduledDAO: EmailScheduledDAO
  lazy val campaignStepDAO: CampaignStepDAO
  lazy val srDBQueryCounterService: SrDBQueryCounterService
  lazy val phishingCheckService: PhishingCheckService
  lazy val campaignProspectDAO_2: CampaignProspectDAO_2


  lazy val campaignStepVariantDAO: CampaignStepVariantDAO = new CampaignStepVariantDAO(
    emailScheduledDAO = emailScheduledDAO,
    campaignStepDAO = campaignStepDAO,
    campaignProspectDAO_2 = campaignProspectDAO_2,
    srDBQueryCounterService = srDBQueryCounterService,
    phishingCheckService = phishingCheckService
  )

}

trait CampaignDAOService_DI {
  lazy val campaignDAO: CampaignDAO
  lazy val campaignEmailSettingsDAO: CampaignEmailSettingsDAO
  lazy val dbUtils: DBUtils
  lazy val campaignSchedulingMetadataDAO: CampaignSchedulingMetadataDAO
  lazy val campaignStepVariantDAO: CampaignStepVariantDAO
  lazy val campaignDAOService: CampaignDAOService = new CampaignDAOService(
    campaignDAO = campaignDAO,
    campaignEmailSettingsDAO = campaignEmailSettingsDAO,
    dbUtils = dbUtils,
    campaignSchedulingMetadataDAO = campaignSchedulingMetadataDAO,
    campaignStepVariantDAO = campaignStepVariantDAO
  )
}

trait CampaignProspectDAOService_DI {
  lazy val campaignProspectDAO: CampaignProspectDAO
  lazy val prospectDAOServiceV2: ProspectDAOServiceV2
  lazy val prospectEmailsDAOService: ProspectEmailsDAOService
  lazy val selectAndPublishForDeletionService: SelectAndPublishForDeletionService
  
  lazy val campaignProspectDAOService: CampaignProspectDAOService = new CampaignProspectDAOService(
    campaignProspectDAO = campaignProspectDAO,
    prospectDAOServiceV2 = prospectDAOServiceV2,
    selectAndPublishForDeletionService = selectAndPublishForDeletionService,
    prospectEmailsDAOService = prospectEmailsDAOService
  )
}

trait CampaignEmailSettingsDAO_DI {
  lazy val srUuidUtils: SrUuidUtils
  lazy val campaignEmailSettingsDAO: CampaignEmailSettingsDAO = new CampaignEmailSettingsDAO(
    srUuidUtils = srUuidUtils
  )

}


trait CampaignProspectDAO_2DI {
  lazy val campaignProspectDAO_2: CampaignProspectDAO_2 = new CampaignProspectDAO_2
}


trait CampaignTemplateServiceDI {
  lazy val templateService: TemplateService
  lazy val prospectColumnDef: ProspectColumnDef
  lazy val campaignDAOService: CampaignDAOService

  lazy val campaignTemplateService: CampaignTemplateService = new CampaignTemplateService(
    templateService = templateService,
    prospectColumnDef = prospectColumnDef,
    campaignDAOService = campaignDAOService,
  )
}


trait ActiveCampaignImplDI {
  lazy val activeCampaignService: ActiveCampaignImpl = new ActiveCampaignImpl
}


trait MergeTagService_DI {
  lazy val campaignProspectDAO_2: CampaignProspectDAO_2
  lazy val campaignSchedulingMetadataDAO: CampaignSchedulingMetadataDAO

  lazy val mergeTagService: MergeTagService = new MergeTagService(
    campaignProspectDAO_2 = campaignProspectDAO_2,
    campaignSchedulingMetadataDAO = campaignSchedulingMetadataDAO
  )
}





