package utils.dependencyinjectionutils

import api.accounts.{AccountService, PermissionUtils}
import api.accounts.service.OrganizationService
import api.dataplatforms.DataplatformController
import api.lead_finder.DAO.{LeadFinder<PERSON>O, LeadFinderMetaDataDAO}
import api.lead_finder.controller.LeadFinderController
import api.lead_finder.service.{LeadFinderBillingLogsPaginationService, LeadFinderService, LeadFinderValidationService}
import api.prospect_daddy.controller.ProspectorController
import api.prospect_daddy.dao.{AnyMailFinderAPI, PdLeadsDAO}
import api.prospect_daddy.service.PdLeadsService
import api.prospects.ProspectService
import api.prospects.dao.{ProspectDAO, ProspectsEmailsDAO}
import api.prospects.dao_service.ProspectDAOService
import api.prospects.service.ProspectServiceV2
import org.apache.pekko.actor.ActorSystem
import play.api.libs.ws.WSClient
import play.api.mvc.ControllerComponents
import utils.cronjobs.leadfinder.InternalLeadFinderDataUpdateCron
import utils.emailvalidation.EmailValidationService
import utils.mq.leadfinder.MqInternalLeadFinderData
import utils.testapp.csv_upload.LeadFinderUploadDao
import utils.uuid.SrUuidUtils
import utils.uuid.services.SrUuidService
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService


trait LeadFinderController_DI {
  lazy val leadFinderService: LeadFinderService
  lazy val permissionUtils: PermissionUtils
  lazy val controllerComponents: ControllerComponents
  lazy val leadFinderBillingLogsPaginationService: LeadFinderBillingLogsPaginationService

  lazy val leadFinderController: LeadFinderController = new LeadFinderController(
    leadFinderService = leadFinderService,
    permissionUtils = permissionUtils,
    controllerComponents = controllerComponents,
    leadFinderBillingLogsPaginationService = leadFinderBillingLogsPaginationService
  )
}


trait LeadFinderService_DI {
  lazy val leadFinderDAO: LeadFinderDAO
  lazy val prospectDAOService: ProspectDAOService
  lazy val prospectService: ProspectService
  lazy val organizationService: OrganizationService
  lazy val emailValidationService: EmailValidationService
  lazy val srRollingUpdateCoreService: SrRollingUpdateCoreService

  lazy val leadFinderService: LeadFinderService = new LeadFinderService(
    leadFinderDAO = leadFinderDAO,
    prospectDAOService = prospectDAOService,
    organizationService = organizationService,
    prospectService = prospectService,
    emailValidationService = emailValidationService,
    srRollingUpdateCoreService = srRollingUpdateCoreService
  )
}

trait LeadFinderUploadDaoDI {
  lazy val srUuidUtils: SrUuidUtils

  lazy val leadFinderUploadDao: LeadFinderUploadDao = new LeadFinderUploadDao(
    srUuidUtils = srUuidUtils
  )
}


/*
  17-jun-2025: [DO_NOT_REMOVE] [LEADFINDERUPLOAD] was slowing down compilation. Only needed for the Lead database upload.

trait LeadFinderUploadServiceDI {
  lazy val srUuidUtils: SrUuidUtils
  lazy val leadFinderUploadDao: LeadFinderUploadDao
  lazy val srCsvReaderUtils: SRCsvReaderUtils
  lazy val univocityParser: UnivocityParser
  lazy val leadFinderUploadService: LeadFinderUploadService = new LeadFinderUploadService(
    srUuidUtils = srUuidUtils,
    leadFinderUploadDao = leadFinderUploadDao,
    srCsvReaderUtils = srCsvReaderUtils,
    univocityParser = univocityParser
  )
}
*/


trait InternalLeadFinderDataUpdateCronDI {

  lazy val prospectsEmailsDAO: ProspectsEmailsDAO
  lazy val mqInternalLeadFinderData: MqInternalLeadFinderData
  lazy val leadFinderService: LeadFinderService

  lazy val internalLeadFinderDataUpdateCron: InternalLeadFinderDataUpdateCron = new InternalLeadFinderDataUpdateCron(
    leadFinderService = leadFinderService,
    prospectsEmailsDAO = prospectsEmailsDAO,
    mqInternalLeadFinderData = mqInternalLeadFinderData
  )
}

trait MqInternalLeadFinderDataDI {


  lazy val prospectDAO: ProspectDAO
  lazy val srRollingUpdateCoreService: SrRollingUpdateCoreService
  lazy val leadFinderService: LeadFinderService
  lazy val prospectsEmailsDAO: ProspectsEmailsDAO
  lazy val mqInternalLeadFinderData: MqInternalLeadFinderData = new MqInternalLeadFinderData(
    prospectDAO = prospectDAO,
    srRollingUpdateCoreService = srRollingUpdateCoreService,
    leadFinderService = leadFinderService,
    prospectsEmailsDAO = prospectsEmailsDAO
  )
}

trait LeadFinderDAO_DI {
  lazy val srUuidUtils: SrUuidUtils

  lazy val leadFinderDAO: LeadFinderDAO = new LeadFinderDAO(
    srUuidUtils = srUuidUtils
  )
}

trait LeadFinderValidationService_DI {

  lazy val leadFinderDAO: LeadFinderDAO
  lazy val prospectService: ProspectService
  lazy val accountService: AccountService
  lazy val srUuidService: SrUuidService
  lazy val prospectServiceV2: ProspectServiceV2

  lazy val leadFinderValidationService: LeadFinderValidationService = new LeadFinderValidationService(
    leadFinderDAO = leadFinderDAO,
    prospectService = prospectService,
    accountService = accountService,
    srUuidService = srUuidService,
    prospectServiceV2 = prospectServiceV2
  )

}

trait LeadFinderMetaDataDAO_DI {
  lazy val leadFinderMetaDataDAO: LeadFinderMetaDataDAO = new LeadFinderMetaDataDAO
}





trait LeadFinderBillingLogsPaginationService_DI {
  lazy val leadFinderDAO: LeadFinderDAO
  lazy val leadFinderBillingLogsPaginationService: LeadFinderBillingLogsPaginationService = new LeadFinderBillingLogsPaginationService(
    leadFinderDAO = leadFinderDAO
  )
}

object ProspectDaddy {

  trait AnyMailFinderAPI_DI {
    lazy val anyMailFinderAPI: AnyMailFinderAPI = new AnyMailFinderAPI()
  }


  trait PdLeadsDAO_DI {
    lazy val pdLeadsDAO: PdLeadsDAO = new PdLeadsDAO()
  }


  trait PdLeadsService_DI {
    lazy val pdLeadsDAO: PdLeadsDAO
    lazy val anyMailFinderAPI: AnyMailFinderAPI
    lazy val prospectService: ProspectService

    lazy val pdLeadsService: PdLeadsService = new PdLeadsService(
      pdLeadsDAO = pdLeadsDAO,
      anyMailFinderAPI = anyMailFinderAPI,
      prospectService = prospectService

    )
  }

  trait ProspectorController_DI {

    lazy val permissionUtils: PermissionUtils
    lazy val controllerComponents: ControllerComponents
    lazy val pdLeadsService: PdLeadsService

    lazy val wsClient: WSClient
    lazy val prospectorController: ProspectorController = new ProspectorController(
      permissionUtils = permissionUtils,
      pdLeadsService = pdLeadsService,
      controllerComponents = controllerComponents,
      wsClient = wsClient
    )
  }

}


trait DataplatformControllerDI {


  lazy val wSClient: WSClient
  lazy val actorSystem: ActorSystem
  lazy val permissionUtils: PermissionUtils
  lazy val controllerComponents: ControllerComponents


  lazy val dataplatformController: DataplatformController = new DataplatformController(
    controllerComponents = controllerComponents,
    permissionUtils = permissionUtils,
    wsClient = wSClient,
    system = actorSystem
  )

}
