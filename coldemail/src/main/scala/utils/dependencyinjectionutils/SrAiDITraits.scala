package utils.dependencyinjectionutils

import api.CacheServiceJedis
import api.campaigns.services.CampaignService
import api.emails.dao_service.EmailScheduledDAOService
import api.llm.dao.LlmAuditLogDAO
import api.prospects.dao_service.ProspectDAOService
import api.sr_ai.apis.SrAiApi
import api.sr_ai.dao.SrAiLockJedisDAO
import api.sr_ai.mq.{MqSrAiApiConsumer, MqSrAiApiPublisher}
import api.sr_ai.service.{CampaignGenerationService, ContentAnalysisService, ContentGenerationService, ReplySentimentClassificationService}
import api.team.service.TeamService
import api.team_inbox.service.ReplySentimentService
import api.accounts.service.OrganizationService
import utils.cronjobs.SrAiApiCron

trait SrAiLockJedisDAO_DI {
  lazy val cacheServiceJedis: CacheServiceJedis
  lazy val srAiLockJedisDAO = new SrAiLockJedisDAO(
    cacheServiceJedis = cacheServiceJedis
  )
}
trait SrAiApi_DI {

  lazy val llmAuditLogDAO: LlmAuditLogDAO
  lazy val srAiLockJedisDAO: SrAiLockJedisDAO

  lazy val srAiApi = new SrAiApi(
    llmAuditLogDAO = llmAuditLogDAO,
    srAiLockJedisDAO = srAiLockJedisDAO
  )

}

trait ReplySentimentClassificationService_DI {

  lazy val srAiApi: SrAiApi
  lazy val emailScheduledDAOService: EmailScheduledDAOService
  lazy val campaignService: CampaignService
  lazy val prospectDAOService: ProspectDAOService
  lazy val replySentimentService: ReplySentimentService
  lazy val organizationService: OrganizationService


  lazy val replySentimentClassificationService = new ReplySentimentClassificationService(
    srAiApi = srAiApi,
    emailScheduledDAOService = emailScheduledDAOService,
    campaignService = campaignService,
    prospectDAOService = prospectDAOService,
    replySentimentService = replySentimentService,
    organizationService = organizationService
  )

}

trait ContentGenerationService_DI {

  lazy val srAiApi: SrAiApi

  lazy val contentGenerationService = new ContentGenerationService(
    srAiApi = srAiApi,
  )

}

trait ContentAnalysisService_DI {

  lazy val srAiApi: SrAiApi

  lazy val contentAnalysisService = new ContentAnalysisService(
    srAiApi = srAiApi,
  )

}

trait CampaignGenerationService_DI {

  lazy val srAiApi: SrAiApi

  lazy val campaignGenerationService = new CampaignGenerationService(
    srAiApi = srAiApi,
  )

}


trait SrAiApiCron_DI {
  lazy val llmAuditLogDAO: LlmAuditLogDAO
  lazy val mqSrAiApiPublisher: MqSrAiApiPublisher
  lazy val srAiLockJedisDAO: SrAiLockJedisDAO
  lazy val srAiApiCron = new SrAiApiCron(
    llmAuditLogDAO = llmAuditLogDAO,
    mqSrAiApiPublisher = mqSrAiApiPublisher,
    srAiLockJedisDAO = srAiLockJedisDAO
  )

}
trait MqSrAiApiConsumer_DI {

  lazy val mqSrAiApiPublisher: MqSrAiApiPublisher
  lazy val replySentimentClassificationService: ReplySentimentClassificationService
  lazy val teamService: TeamService
  lazy val llmAuditLogDAO: LlmAuditLogDAO
  lazy val srAiLockJedisDAO: SrAiLockJedisDAO

  lazy val mqSrAiApiConsumer = new MqSrAiApiConsumer(
    mqSrAiApiPublisher = mqSrAiApiPublisher,
    replySentimentClassificationService = replySentimentClassificationService,
    teamService = teamService,
    llmAuditLogDAO = llmAuditLogDAO,
    srAiLockJedisDAO = srAiLockJedisDAO
  )

}

trait MqSrAiApiPublisher_DI {
  lazy val mqSrAiApiPublisher = new MqSrAiApiPublisher
}
