package utils.dependencyinjectionutils

import api.CacheServiceJedis
import api.integrations.dao.WorkflowAutomationReportingDAO
import api.integrations.services.WorkflowAutomationReportingService
import api.triggers.TriggerServiceV2
import utils.cronjobs.WorkflowAutomationReportingCronService
import utils.email_notification.service.EmailNotificationService


trait WorkflowAutomationReportingCronService_DI {
  lazy val workflowAutomationReportingService: WorkflowAutomationReportingService
  lazy val workflowAutomationReportingCronService: WorkflowAutomationReportingCronService = new WorkflowAutomationReportingCronService(
    workflowAutomationReportingService = workflowAutomationReportingService
  )
}

trait WorkflowAutomationReportingService_DI {
  lazy val triggerServiceV2: TriggerServiceV2
  lazy val emailNotificationService: EmailNotificationService
  lazy val workflowAutomationReportingDAO: WorkflowAutomationReportingDAO
  lazy val workflowAutomationReportingService: WorkflowAutomationReportingService = new WorkflowAutomationReportingService(
    triggerServiceV2 = triggerServiceV2,
    emailNotificationService = emailNotificationService,
    workflowAutomationReportingDAO = workflowAutomationReportingDAO
  )
}

trait WorkflowAutomationReportingDAO_DI {
  lazy val cacheServiceJedis: CacheServiceJedis
  lazy val workflowAutomationReportingDAO: WorkflowAutomationReportingDAO = new WorkflowAutomationReportingDAO(
    cacheServiceJedis = cacheServiceJedis
  )
}