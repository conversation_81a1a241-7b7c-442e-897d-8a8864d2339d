package utils.dependencyinjectionutils

import api.CacheServiceJedis
import api.accounts.dao.AccountDAOV2
import api.integrations.WebhookUtils_V2
import api.sr_audit_logs.dao.{CRMSettingsForWorkFlowAttemptJedisDAO, CRMSettingsRateLimitJedisDAO, EventLogDAO, RequestLogDAO, WorkFlowAttemptSetJedisDAO, WorkflowAttemptDAO, WorkflowAttemptRateLimitSetJedisDAO}
import api.sr_audit_logs.dao_service.{WorkFlowAttemptJedisDAOService, WorkflowRateLimitJedisDAOService}
import api.sr_audit_logs.services.{EventLogFindService, EventLogService, ProcessAttemptService, RequestLogService, WorkFlowAttemptService, WorkflowJedisService}
import api.triggers.Trigger
import org.apache.pekko.stream.Materializer
import utils.cronjobs.AttemptRetryCronService
import utils.dbutils.DBUtils
import utils.email_notification.service.EmailNotificationService
import utils.mq.webhook.handle_event_log.{MqHandleEventLogConsumer, MqHandleEventLogPublisher}
import utils.mq.webhook.mq_handle_attempt_creation.{MqHandleAttemptCreation, MqHandleAttemptCreationPublisher}
import utils.mq.webhook.{HandleActivityTriggerEventService, HandleAddToDNCTriggerEventService, HandlePushTriggerEventService, HandleSyncTriggerEventService, MQNewProcessWorkflowAttemptService}
import utils.uuid.SrUuidUtils

import scala.concurrent.ExecutionContext


trait AttemptRetryCronService_DI {
  lazy val workFlowAttemptService: WorkFlowAttemptService
  lazy val workFlowAttemptJedisDAOService: WorkFlowAttemptJedisDAOService
  lazy val mqNewProcessWorkflowAttemptService: MQNewProcessWorkflowAttemptService

  lazy val attemptRetryCronService: AttemptRetryCronService = new AttemptRetryCronService(
    workFlowAttemptService = workFlowAttemptService,
    workFlowAttemptJedisDAOService = workFlowAttemptJedisDAOService,
    mqNewProcessWorkflowAttemptService = mqNewProcessWorkflowAttemptService
  )

}

trait MQNewProcessWorkflowAttemptService_DI {
  lazy val workFlowAttemptJedisDAOService: WorkFlowAttemptJedisDAOService
  lazy val processAttemptService: ProcessAttemptService
  lazy val workFlowAttemptService: WorkFlowAttemptService

  lazy val mqNewProcessWorkflowAttemptService: MQNewProcessWorkflowAttemptService = new MQNewProcessWorkflowAttemptService(
    workFlowAttemptJedisDAOService = workFlowAttemptJedisDAOService,
    workFlowAttemptService = workFlowAttemptService,
    processAttemptService = processAttemptService
  )

}

trait ProcessAttemptService_DI {
  lazy val workFlowAttemptService: WorkFlowAttemptService
  lazy val handleActivityTriggerEventService: HandleActivityTriggerEventService
  lazy val trigger: Trigger
  lazy val handleSyncTriggerEventService: HandleSyncTriggerEventService
  lazy val handlePushTriggerEventService: HandlePushTriggerEventService
  lazy val handleAddToDNCTriggerEventService: HandleAddToDNCTriggerEventService
  lazy val workflowJedisService: WorkflowJedisService

  lazy val processAttemptService: ProcessAttemptService = new ProcessAttemptService(
    workFlowAttemptService = workFlowAttemptService,
    handleActivityTriggerEventService = handleActivityTriggerEventService,
    triggerDAO = trigger,
    handleSyncTriggerEventService = handleSyncTriggerEventService,
    handlePushTriggerEventService = handlePushTriggerEventService,
    handleAddToDNCTriggerEventService = handleAddToDNCTriggerEventService,
    workflowJedisService = workflowJedisService
  )

}

trait WorkflowJedisService_DI {
  lazy val workflowRateLimitJedisDAOService: WorkflowRateLimitJedisDAOService
  lazy val workflowJedisService: WorkflowJedisService = new WorkflowJedisService(
    workflowRateLimitJedisDAOService = workflowRateLimitJedisDAOService
  )
}

trait WorkflowRateLimitJedisDAOService_DI {
  lazy val crmSettingsRateLimitJedisDAO: CRMSettingsRateLimitJedisDAO
  lazy val workflowAttemptRateLimitSetJedisDAO: WorkflowAttemptRateLimitSetJedisDAO

  lazy val workflowRateLimitJedisDAOService: WorkflowRateLimitJedisDAOService = new WorkflowRateLimitJedisDAOService(
    crmSettingsRateLimitJedisDAO = crmSettingsRateLimitJedisDAO,
    workflowAttemptRateLimitSetJedisDAO = workflowAttemptRateLimitSetJedisDAO
  )
}

trait CRMSettingsRateLimitJedisDAO_DI {
  lazy val cacheServiceJedis: CacheServiceJedis
  lazy val crmSettingsRateLimitJedisDAO: CRMSettingsRateLimitJedisDAO = new CRMSettingsRateLimitJedisDAO(
    cacheServiceJedis = cacheServiceJedis
  )
}

trait WorkflowAttemptRateLimitSetJedisDAO_DI {
  lazy val cacheServiceJedis: CacheServiceJedis

  lazy val workflowAttemptRateLimitSetJedisDAO: WorkflowAttemptRateLimitSetJedisDAO = new WorkflowAttemptRateLimitSetJedisDAO(
    cacheServiceJedis = cacheServiceJedis
  )
}

trait WorkFlowAttemptJedisDAOService_DI {
  lazy val crmSettingsForWorkFlowAttemptJedisDAO: CRMSettingsForWorkFlowAttemptJedisDAO
  lazy val workFlowAttemptSetJedisDAO: WorkFlowAttemptSetJedisDAO
  lazy val workFlowAttemptJedisDAOService: WorkFlowAttemptJedisDAOService = new WorkFlowAttemptJedisDAOService(
    crmSettingsForWorkFlowAttemptJedisDAO = crmSettingsForWorkFlowAttemptJedisDAO,
    workFlowAttemptSetJedisDAO = workFlowAttemptSetJedisDAO
  )
}

trait WorkFlowAttemptSetJedisDAO_DI {
  lazy val cacheServiceJedis: CacheServiceJedis
  lazy val workFlowAttemptSetJedisDAO: WorkFlowAttemptSetJedisDAO = new WorkFlowAttemptSetJedisDAO(
    cacheServiceJedis = cacheServiceJedis
  )
}

trait CRMSettingsForWorkFlowAttemptJedisDAO_DI {
  lazy val cacheServiceJedis: CacheServiceJedis
  lazy val crmSettingsForWorkFlowAttemptJedisDAO: CRMSettingsForWorkFlowAttemptJedisDAO = new CRMSettingsForWorkFlowAttemptJedisDAO(
    cacheServiceJedis = cacheServiceJedis
  )
}


trait RequestLogService_DI {
  lazy val requestLogDAO: RequestLogDAO
  lazy val actorMaterializer: Materializer
  lazy val playDefaultExecutionContext: ExecutionContext
  lazy val requestLogService: RequestLogService = new RequestLogService(
    requestLogDAO = requestLogDAO,
    materializer = actorMaterializer,
    ec = playDefaultExecutionContext
  )
}

trait RequestLogDAO_DI {
  lazy val requestLogDAO: RequestLogDAO = new RequestLogDAO
}

trait EventLogDAO_DI {

  lazy val eventLogDAO: EventLogDAO = new EventLogDAO
}

trait EventLogService_DI {
  lazy val eventLogDAO: EventLogDAO
  lazy val mqHandleEventLogPublisher: MqHandleEventLogPublisher
  lazy val srUuidUtils: SrUuidUtils
  lazy val eventLogService: EventLogService = new EventLogService(
    eventLogDAO = eventLogDAO,
    mqHandleEventLogPublisher = mqHandleEventLogPublisher,
    srUuidUtils = srUuidUtils
  )
}

trait WorkflowAttemptDAO_DI {
  lazy val workflowAttemptDAO: WorkflowAttemptDAO = new WorkflowAttemptDAO
}

trait WorkFlowAttemptService_DI {
  lazy val workflowAttemptDAO: WorkflowAttemptDAO
  lazy val dbUtils: DBUtils
  lazy val srUuidUtils: SrUuidUtils
  lazy val workFlowAttemptService: WorkFlowAttemptService = new WorkFlowAttemptService(
    attemptDAO = workflowAttemptDAO,
    dbUtils = dbUtils,
    srUuidUtils = srUuidUtils
  )
}


trait EventLogFindServiceDI {
  lazy val eventLogDAO: EventLogDAO

  lazy val eventLogFindService: EventLogFindService = new EventLogFindService(
    eventLogDAO = eventLogDAO
  )
}

trait MqHandleEventLogDI {
  lazy val eventLogFindService: EventLogFindService
  lazy val trigger: Trigger
  lazy val workFlowAttemptService: WorkFlowAttemptService
  lazy val handleActivityTriggerEventService: HandleActivityTriggerEventService
  lazy val handlePushTriggerEventService: HandlePushTriggerEventService
  lazy val emailNotificationService: EmailNotificationService
  lazy val accountDAOV2: AccountDAOV2
  lazy val webhookUtils_V2: WebhookUtils_V2
  lazy val eventLogService: EventLogService
  lazy val mqHandleAttemptCreationPublisher: MqHandleAttemptCreationPublisher
  lazy val mqHandleEventLogPublisher: MqHandleEventLogPublisher

  lazy val mqHandleEventLogConsumer: MqHandleEventLogConsumer = new MqHandleEventLogConsumer(
    eventLogFindService = eventLogFindService,
    eventLogService = eventLogService,
    triggerDAO = trigger,
    workFlowAttemptService = workFlowAttemptService,
    handleActivityTriggerEventService = handleActivityTriggerEventService,
    handlePushTriggerEventService = handlePushTriggerEventService,
    emailNotificationService = emailNotificationService,
    accountDAOV2 = accountDAOV2,
    webhookUtils_V2 = webhookUtils_V2,
    mqHandleAttemptCreationPublisher = mqHandleAttemptCreationPublisher,
    mqHandleEventLogPublisher = mqHandleEventLogPublisher
  )
}

trait MqHandleAttemptCreationPublisherDI {
  lazy val mqHandleAttemptCreationPublisher: MqHandleAttemptCreationPublisher = new MqHandleAttemptCreationPublisher
}

trait MqHandleAttemptCreationDI {
  lazy val workFlowAttemptService: WorkFlowAttemptService
  lazy val mqHandleAttemptCreationPublisher: MqHandleAttemptCreationPublisher


  lazy val mqHandleAttemptCreation: MqHandleAttemptCreation = new MqHandleAttemptCreation(
    workflowAttemptService = workFlowAttemptService,
    mqHandleAttemptCreationPublisher = mqHandleAttemptCreationPublisher
  )
}

