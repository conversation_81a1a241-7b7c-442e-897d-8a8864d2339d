package utils.dependencyinjectionutils

import api.accounts.PermissionUtils
import api.accounts.dao.AccountOrgBillingRelatedInfoDAO
import api.call.DAO.CallDAO
import api.campaigns.CampaignProspectDAO
import api.emails.{EmailScheduledDAO, EmailThreadDAO}
import api.emails.models.EmailMessageContactModel
import api.linkedin_message_threads.LinkedinMessageThreadsDAO
import api.pipelines.dao.OpportunityDAO
import api.prospects.{DuplicateProspectsController, ProspectEventDAO}
import api.prospects.dao.{PotentialDuplicateProspectsDAO, ProspectAddEventDAO, ProspectsEmailsDAO}
import api.prospects.dao_service.ProspectDAOService
import api.prospects.service.PotentialDuplicateProspectService
import api.tags.ProspectTagDAOLegacy
import api.tasks.services.TaskService
import play.api.mvc.ControllerComponents
import utils.dbutils.DBUtils
import utils.mq.duplicate_prospects.MqPotentialDuplicateProspects
import utils.mq.merge_duplicate_prospects.MqMergeDuplicateProspects

trait PotentialDuplicateProspectService_DI {
  lazy val accountOrgBillingRelatedInfoDAO: AccountOrgBillingRelatedInfoDAO
  lazy val prospectDAOService: ProspectDAOService
  lazy val potentialDuplicateProspectsDAO: PotentialDuplicateProspectsDAO
  lazy val taskService: TaskService
  lazy val campaignProspectDAO: CampaignProspectDAO
  lazy val emailThreadDAO: EmailThreadDAO
  lazy val emailScheduledDAO: EmailScheduledDAO
  lazy val emailMessageContactModel: EmailMessageContactModel
  lazy val linkedinMessageThreadsDAO: LinkedinMessageThreadsDAO
  lazy val prospectTagDAOLegacy: ProspectTagDAOLegacy
  lazy val opportunityDAO: OpportunityDAO
  lazy val callDAO: CallDAO
  lazy val prospectEventDAO: ProspectEventDAO
  lazy val prospectAddEventDAO: ProspectAddEventDAO
  lazy val prospectsEmailsDAO: ProspectsEmailsDAO
  lazy val dbUtils: DBUtils

  lazy val potentialDuplicateProspectService: PotentialDuplicateProspectService = new PotentialDuplicateProspectService(
    prospectDAOService = prospectDAOService,
    accountOrgBillingRelatedInfoDAO = accountOrgBillingRelatedInfoDAO,
    campaignProspectDAO = campaignProspectDAO,
    taskService = taskService,
    emailThreadDAO = emailThreadDAO,
    emailScheduledDAO = emailScheduledDAO,
    emailMessageContactModel = emailMessageContactModel,
    linkedinMessageThreadsDAO = linkedinMessageThreadsDAO,
    prospectTagDAOLegacy = prospectTagDAOLegacy,
    opportunityDAO = opportunityDAO,
    dbUtils = dbUtils,
    callDAO = callDAO,
    prospectEventDAO = prospectEventDAO,
    prospectAddEventDAO = prospectAddEventDAO,
    prospectsEmailsDAO = prospectsEmailsDAO,
    potentialDuplicateProspectsDAO = potentialDuplicateProspectsDAO
  )
}


trait MqPotentialDuplicateProspects_DI {
  lazy val potentialDuplicateProspectService: PotentialDuplicateProspectService
  lazy val accountOrgBillingRelatedInfoDAO: AccountOrgBillingRelatedInfoDAO
  lazy val mqPotentialDuplicateProspects: MqPotentialDuplicateProspects = new MqPotentialDuplicateProspects(
    accountOrgBillingRelatedInfoDAO = accountOrgBillingRelatedInfoDAO,
    potentialDuplicateProspectService = potentialDuplicateProspectService
  )
}

trait MqMergeDuplicateProspects_DI {
  lazy val potentialDuplicateProspectService: PotentialDuplicateProspectService
  lazy val mqMergeDuplicateProspects: MqMergeDuplicateProspects = new MqMergeDuplicateProspects(
    potentialDuplicateProspectService = potentialDuplicateProspectService
  )
}

trait DuplicateProspectsController_DI {
  lazy val controllerComponents: ControllerComponents
  lazy val permissionUtils: PermissionUtils
  lazy val potentialDuplicateProspectService: PotentialDuplicateProspectService
  lazy val mqMergeDuplicateProspects: MqMergeDuplicateProspects

  lazy val duplicateProspectsController: DuplicateProspectsController = new DuplicateProspectsController(
    controllerComponents = controllerComponents,
    permissionUtils = permissionUtils,
    mqMergeDuplicateProspects = mqMergeDuplicateProspects,
    potentialDuplicateProspectService = potentialDuplicateProspectService
  )
}



trait PotentialDuplicateProspectsDAO_DI {
  lazy val potentialDuplicateProspectsDAO: PotentialDuplicateProspectsDAO = new PotentialDuplicateProspectsDAO
}



