package utils.dependencyinjectionutils

import api.CacheServiceJedis
import api.accounts.dao.TeamsDAO
import api.campaigns.CampaignProspectDAO
import api.campaigns.services.{CampaignProspectDAOService, CampaignService}
import api.emails.dao_service.{EmailScheduledDAOService, EmailThreadDAOService}
import api.linkedin.LinkedinConnectionsService
import api.linkedin_message_threads.LinkedinMessageThreadsDAO
import api.llm.dao.LlmAuditLogDAO
import api.prospects.dao.ProspectDAO
import api.prospects.dao_service.ProspectDAOService
import api.sr_ai.apis.SrAiApi
import api.tasks.services.TaskService
import api.team_inbox.dao.{ReplySentimentDAO, ReplySentimentJedisDAO}
import api.team_inbox.dao_service.ReplySentimentDAOService
import api.team_inbox.service.ReplySentimentService
import utils.dbutils.DBUtils
import utils.mq.campaigns.MqPauseCampaignOnReplySentimentSelect
import utils.mq.captainData.MqExtractLinkedinConnectionResults
import utils.mq.task.{MqForSaveReplySentimentConsumer, MqForSaveReplySentimentPublisher}
import utils.mq.webhook.MQWebhookCompleted
import utils.mq.webhook.mq_activity_trigger.MQActivityTriggerPublisher
import api.sr_ai.mq.{MqSrAiApiConsumer, MqSrAiApiPublisher}
import api.sr_ai.service.ReplySentimentClassificationService
import api.team.service.TeamService
import api.accounts.dao_service.OrganizationDAOService
import api.prospects.ProspectUpdateCategoryTemp
import utils.mq.prospect_category.MqAutoUpdateProspectCategoryPublisher
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService


trait ReplySentimentService_DI {
  lazy val emailThreadDAOService: EmailThreadDAOService
  lazy val campaignProspectDAO: CampaignProspectDAO
  lazy val dbUtils: DBUtils
  lazy val replySentimentDAOService: ReplySentimentDAOService
  lazy val prospectDAO: ProspectDAO
  lazy val mqActivityTriggerPublisher: MQActivityTriggerPublisher
  lazy val linkedinMessageThreadsDAO: LinkedinMessageThreadsDAO
  lazy val mqForSaveReplySentimentPublisher: MqForSaveReplySentimentPublisher
  lazy val mqAutoUpdateProspectCategoryPublisher: MqAutoUpdateProspectCategoryPublisher

  lazy val replySentimentService: ReplySentimentService = new ReplySentimentService(
    emailThreadDAOService = emailThreadDAOService,
    linkedinMessageThreadsDAO = linkedinMessageThreadsDAO,
    campaignProspectDAO = campaignProspectDAO,
    dbUtils = dbUtils,
    replySentimentDAOService = replySentimentDAOService,
    prospectDAO = prospectDAO,
    mqActivityTriggerPublisher = mqActivityTriggerPublisher,
    mqForSaveReplySentimentPublisher = mqForSaveReplySentimentPublisher,
    mqAutoUpdateProspectCategoryPublisher = mqAutoUpdateProspectCategoryPublisher,
  )
}

trait ReplySentimentDAOService_DI {
  lazy val replySentimentDAO: ReplySentimentDAO
  lazy val replySentimentJedisDAO: ReplySentimentJedisDAO
  lazy val organizationDAOService: OrganizationDAOService
  lazy val srRollingUpdateCoreService:SrRollingUpdateCoreService

  lazy val replySentimentDAOService: ReplySentimentDAOService = new ReplySentimentDAOService(
    replySentimentDAO = replySentimentDAO,
    replySentimentJedisDAO = replySentimentJedisDAO,
    organizationDAOService = organizationDAOService,
    srRollingUpdateCoreService = srRollingUpdateCoreService
  )
}

trait ReplySentimentDAO_DI {
  lazy val replySentimentDAO: ReplySentimentDAO = new ReplySentimentDAO
}

trait ReplySentimentJedisDAO_DI {
  lazy val cacheServiceJedis: CacheServiceJedis
  lazy val replySentimentJedisDAO: ReplySentimentJedisDAO = new ReplySentimentJedisDAO(
    cacheServiceJedis = cacheServiceJedis
  )
}


trait MqForSaveReplySentimentPublisher_DI {
  lazy val mqForSaveReplySentimentPublisher: MqForSaveReplySentimentPublisher = new MqForSaveReplySentimentPublisher
}

trait MqForSaveReplySentimentConsumer_DI {
  lazy val mqForSaveReplySentimentPublisher: MqForSaveReplySentimentPublisher
  lazy val taskService: TaskService

  lazy val mqForSaveReplySentimentConsumer: MqForSaveReplySentimentConsumer = new MqForSaveReplySentimentConsumer(
    mqForSaveReplySentimentPublisher = mqForSaveReplySentimentPublisher,
    taskService = taskService
  )
}

trait MqExtractLinkedinConnectionResultsDI {
  lazy val linkedinConnectionsService: LinkedinConnectionsService


  lazy val mqExtractLinkedinConnectionResults: MqExtractLinkedinConnectionResults = new MqExtractLinkedinConnectionResults(
    linkedinConnectionsService = linkedinConnectionsService

  )
}

trait MqPauseCampaignOnReplySentimentSelectDI {
  lazy val campaignProspectDAOService: CampaignProspectDAOService
  lazy val mqWebhookCompleted : MQWebhookCompleted

  lazy val mqPauseCampaignOnReplySentimentSelect: MqPauseCampaignOnReplySentimentSelect = new MqPauseCampaignOnReplySentimentSelect(
    campaignProspectDAOService: CampaignProspectDAOService,
    mqWebhookCompleted: MQWebhookCompleted
  )
}


