package utils.dependencyinjectionutils

import api.accounts.AccountService
import api.accounts.service.OrganizationService
import api.campaigns.dao.CampaignProspectDAO_2
import api.campaigns.services.{CampaignCacheService, CampaignProspectAssign, CampaignProspectService}
import api.columns.{ProspectColumnDef, ProspectColumnDefDAO}
import api.columns.services.ProspectColumnService
import api.gpt.GPTService
import api.llm.dao.LlmAuditLogDAO
import api.prospects.dao.{ProspectDAO, ProspectsEmailsDAO}
import api.prospects.{ProspectEventService, ProspectFeedDAO, ProspectService}
import api.prospects.dao_service.{ProspectDAOService, ProspectEmailsDAOService}
import api.prospects.service.{ProspectApiService, ProspectEventsListingPaginationService, ProspectServiceV2, ProspectsListingPaginationService}
import utils.PusherService
import utils.dbutils.DBUtils
import utils.mq.statsupdater.MQListStatsUpdater
import utils.templating.TemplateService
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService
import api.search.ProspectQuery

trait ProspectApiService_DI {
  lazy val prospectService: ProspectService
  lazy val prospectDAOService: ProspectDAOService
  lazy val prospectApiService: ProspectApiService = new ProspectApiService(
    prospectService = prospectService,
    prospectDAOService = prospectDAOService
  )
}



trait ProspectsListingPaginationService_DI {

  lazy val prospectDAOService: ProspectDAOService

  lazy val prospectsListingPaginationService: ProspectsListingPaginationService = new ProspectsListingPaginationService(
    prospectDAOService = prospectDAOService
  )
}



trait ProspectEventsListingPaginationService_DI {
  lazy val prospectEventService: ProspectEventService
  lazy val srRollingUpdateCoreService: SrRollingUpdateCoreService

  lazy val prospectEventsListingPaginationService: ProspectEventsListingPaginationService = new ProspectEventsListingPaginationService(
    prospectEventService = prospectEventService,
    srRollingUpdateCoreService = srRollingUpdateCoreService
  )
}


trait ProspectColumnDef_DI {
  lazy val prospectColumnDefDAO: ProspectColumnDefDAO
  lazy val templateService: TemplateService
  lazy val prospectColumnDef: ProspectColumnDef = new ProspectColumnDef(
    prospectColumnDefDAO = prospectColumnDefDAO,
    templateService = templateService,
  )
}

trait ProspectColumnDefDAO_DI {
  lazy val prospectColumnDefDAO: ProspectColumnDefDAO = new ProspectColumnDefDAO
}


trait ProspectColumnService_DI {

  lazy val prospectColumnDefDAO: ProspectColumnDefDAO
  lazy val prospectColumnDef: ProspectColumnDef
  lazy val prospectDAOService: ProspectDAOService
  lazy val accountService: AccountService
  lazy val gptService: GPTService
  lazy val templateService: TemplateService
  lazy val dbUtils: DBUtils
  lazy val organizationService: OrganizationService
  lazy val pusherService: PusherService

  lazy val prospectColumnService: ProspectColumnService = new ProspectColumnService(
    prospectColumnDefDAO = prospectColumnDefDAO,
    prospectDAOService = prospectDAOService,
    accountService = accountService,
    gptService = gptService,
    dbUtils = dbUtils,
    templateService = templateService,
    prospectColumnDef = prospectColumnDef,
    organizationService = organizationService,
    pusherService = pusherService,
  )

}





trait CampaignProspectAssignDI {
  lazy val campaignProspectService: CampaignProspectService
  lazy val campaignCacheService: CampaignCacheService

  lazy val campaignProspectAssign: CampaignProspectAssign = new CampaignProspectAssign(
    campaignProspectService = campaignProspectService,
    campaignCacheService = campaignCacheService
  )
}

trait ProspectFeedDAO_DI {
  lazy val prospectServiceV2: ProspectServiceV2
  lazy val srRollingUpdateCoreService: SrRollingUpdateCoreService

  lazy val prospectFeedDAO: ProspectFeedDAO = new ProspectFeedDAO(
    prospectServiceV2 = prospectServiceV2,
    srRollingUpdateCoreService = srRollingUpdateCoreService
  )
}


trait ProspectEmailsDAOService_DI {
  lazy val prospectsEmailsDAO: ProspectsEmailsDAO
  lazy val prospectEmailsDAOService: ProspectEmailsDAOService = new ProspectEmailsDAOService(
    prospectsEmailsDAO = prospectsEmailsDAO
  )
}


trait ProspectDAO_DI {
  lazy val prospectDAO: ProspectDAO = new ProspectDAO
}


trait ProspectsEmailsDAO_DI {
  lazy val prospectsEmailsDAO: ProspectsEmailsDAO = new ProspectsEmailsDAO
}


trait ProspectQueryDI {
  lazy val prospectColumnDef: ProspectColumnDef

  lazy val prospectQuery: ProspectQuery = new ProspectQuery(
    prospectColumnDef = prospectColumnDef
  )
}




trait MQListStatsUpdaterDI {
  lazy val prospectDAOService: ProspectDAOService
  lazy val mqListStatsUpdater: MQListStatsUpdater = new MQListStatsUpdater(
    prospectDAOService = prospectDAOService
  )
}


