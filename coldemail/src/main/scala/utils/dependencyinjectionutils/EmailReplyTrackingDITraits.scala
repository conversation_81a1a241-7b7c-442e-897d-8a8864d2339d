package utils.dependencyinjectionutils

import api.CacheServiceJedis
import api.campaigns.dao.InboxPlacementCheckDAO
import api.emails.dao_service.EmailScheduledDAOService
import api.emails.{EmailMessageDataDAO, EmailReplyTrackingModel, EmailScheduledDAO, EmailSettingDAO, EmailThreadDAO}
import api.emails.services.SelectAndPublishForDeletionService
import api.free_email_domain.service.FreeEmailDomainListService
import utils.cronjobs.ReadEmailCronService
import utils.email.EmailService
import utils.email.services.ReplyFilteringService
import utils.mq.replytracker.ReplyTrackerService
import utils.mq.replytracker.mq.{MQReplyTracker, MQReplyTrackerPublisher}
import utils.mq.trackingapp.MQUnsubscribeTracker
import utils.uuid.SrUuidUtils


trait EmailReplyTrackingModelDI {
  lazy val emailScheduledDAO: EmailScheduledDAO
  lazy val srUuidUtils: SrUuidUtils
  lazy val emailMessageDataDAO: EmailMessageDataDAO

  lazy val selectAndPublishForDeletionService: SelectAndPublishForDeletionService

  //  lazy val emailThreadDAO: EmailThreadDAO
  //  lazy val  prospectServiceV2: ProspectServiceV2

  lazy val emailReplyTrackingModel: EmailReplyTrackingModel = new EmailReplyTrackingModel(
    //    emailThreadDAO = emailThreadDAO,
    emailScheduledDAO = emailScheduledDAO,
    emailMessageDataDAO = emailMessageDataDAO,
    selectAndPublishForDeletionService = selectAndPublishForDeletionService
    //    srUuidUtils = srUuidUtils
    //    prospectServiceV2 = prospectServiceV2
  )

}


trait ReplyTrackerService_DI {
  lazy val cacheServiceJedis: CacheServiceJedis
  lazy val replyTrackerService: ReplyTrackerService = new ReplyTrackerService(
    cacheServiceJedis = cacheServiceJedis
  )
}

trait MQReplyTrackerPublisher_DI {
  lazy val mqReplyTrackerPublisher = new MQReplyTrackerPublisher
}

trait MQReplyTrackerDI {

  //  lazy val mqEmailSchedulerV2: MqEmailChannelScheduler
  lazy val emailService: EmailService
  lazy val emailSettingDAO: EmailSettingDAO
  lazy val replyTrackerService: ReplyTrackerService
  lazy val srUuidUtils: SrUuidUtils
  lazy val mqReplyTrackerPublisher: MQReplyTrackerPublisher

  lazy val mqReplyTracker: MQReplyTracker = new MQReplyTracker(
    srUuidUtils = srUuidUtils,
    emailSettingDAO = emailSettingDAO,
    //    mqEmailSchedulerV2 = mqEmailSchedulerV2,
    emailService = emailService,
    replyTrackerService = replyTrackerService,
    mqReplyTrackerPublisher = mqReplyTrackerPublisher
  )

}

trait ReplyFilteringServiceDI {

  lazy val emailScheduledDAOService: EmailScheduledDAOService
  lazy val mqUnsubscribeTracker: MQUnsubscribeTracker
  lazy val emailThreadDAO: EmailThreadDAO
  lazy val freeEmailDomainListService: FreeEmailDomainListService
  lazy val inboxPlacementCheckDAO: InboxPlacementCheckDAO

  lazy val replyFilteringService: ReplyFilteringService = new ReplyFilteringService(
    mqUnsubscribeTracker = mqUnsubscribeTracker,
    emailThreadDAO = emailThreadDAO,
    emailScheduledDAOService = emailScheduledDAOService,
    inboxPlacementCheckDAO = inboxPlacementCheckDAO,
    freeEmailDomainListService = freeEmailDomainListService
  )

}


trait ReadEmailCronServiceDI {
  lazy val mqReplyTrackerPublisher: MQReplyTrackerPublisher
  lazy val emailSettingDAO: EmailSettingDAO

  lazy val readEmailCronService: ReadEmailCronService = new ReadEmailCronService(
    emailSettingDAO = emailSettingDAO,
    mqReplyTrackerPublisher = mqReplyTrackerPublisher
  )
}