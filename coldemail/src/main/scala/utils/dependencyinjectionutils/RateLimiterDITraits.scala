package utils.dependencyinjectionutils

import api.CacheServiceJedis
import api.internal_reporting.{SrDBQueryCounterReportController, SrRateLimiterReportController}
import app_services.db_query_counter.SrDBQueryCounterService
import app_services.db_query_counter.dao.DBCounterDAO
import app_services.sr_rate_limiter.SrRateLimiter
import app_services.sr_rate_limiter.dao.SrRateLimiterDAO
import play.api.mvc.ControllerComponents


trait DBCounterDAO_DI {

  lazy val cacheServiceJedis: CacheServiceJedis
  lazy val dbCounterDAO: DBCounterDAO = new DBCounterDAO(
    cacheServiceJedis = cacheServiceJedis
  )
}

trait SrDBQueryCounterService_DI {
  lazy val dbCounterDAO: DBCounterDAO
  lazy val srDBQueryCounterService: SrDBQueryCounterService = new SrDBQueryCounterService(dbCounterDAO)
}








trait SrRateLimiterDAO_DI {
  lazy val cacheServiceJedis: CacheServiceJedis

  lazy val srRateLimiterDAO: SrRateLimiterDAO = new SrRateLimiterDAO(
    cacheServiceJedis = cacheServiceJedis
  )
}


trait SrRateLimiterReportControllerDI {
  lazy val srRateLimiter: SrRateLimiter
  lazy val controllerComponents: ControllerComponents

  lazy val srRateLimiterReportController: SrRateLimiterReportController = new SrRateLimiterReportController(
    controllerComponents = controllerComponents,
    srRateLimiter = srRateLimiter
  )

}

trait SrDBQueryCounterReportControllerDI {

  lazy val srDBQueryCounterService: SrDBQueryCounterService
  lazy val controllerComponents: ControllerComponents


  lazy val srDBQueryCounterReportController: SrDBQueryCounterReportController = new SrDBQueryCounterReportController(
    controllerComponents = controllerComponents,
    srDBQueryCounterService = srDBQueryCounterService
  )

}


trait SrRateLimiterDI {
  lazy val srRateLimiterDAO: SrRateLimiterDAO

  lazy val srRateLimiter: SrRateLimiter = new SrRateLimiter(
    srRateLimiterDAO = srRateLimiterDAO
  )

}

