package utils.dependencyinjectionutils

import api.ApiService
import api.accounts.dao.OrganizationDAO
import api.accounts.{AccountService, AuthUtils, PermissionUtils}
import api.blacklist.dao.BlacklistProspectCheckDAO
import api.blacklist.{BlacklistApiService, BlacklistController, BlacklistDAO, BlacklistListingPaginationService, BlacklistService, BlacklistServiceV2}
import api.emails.EmailSettingDAO
import api.prospects.ProspectUpdateCategoryTemp
import api.prospects.dao_service.ProspectDAOService
import api.prospects.service.ProspectServiceV2
import api.team.service.TeamService
import app_services.blacklist_monitoring.services.SrBlackListCheckCron
import play.api.mvc.ControllerComponents
import utils.email_notification.service.EmailNotificationService
import utils.mq.blacklist.{DncTypeMigrationFunc, MqDoNotContactType, MqUpdateProspectCategoryForGivenTeamOfOrgForGlobalDNC}
import utils.mq.do_not_contact.{MQDo<PERSON>otContactConsumer, MQDoNotContactPublisher}
import utils.uuid.SrUuidUtils
import utils.uuid.services.SrUuidService
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService


trait BlacklistControllerDI {

  lazy val mqDoNotContactPublisher: MQDoNotContactPublisher
  lazy val blacklistDAO: BlacklistDAO
  lazy val blacklistService: BlacklistService
  lazy val authUtils: AuthUtils
  lazy val permissionUtils: PermissionUtils
  lazy val controllerComponents: ControllerComponents
  lazy val blacklistListingPaginationService: BlacklistListingPaginationService
  lazy val apiService: ApiService

  lazy val blacklistController: BlacklistController = new BlacklistController(
    controllerComponents = controllerComponents,
    mqDoNotContactPublisher = mqDoNotContactPublisher,
    blacklistDAO = blacklistDAO,
    blacklistService = blacklistService,
    authUtils = authUtils,
    permissionUtils = permissionUtils,
    blacklistListingPaginationService = blacklistListingPaginationService,
    apiService = apiService
  )
}

trait BlacklistListingPaginationService_DI {
  lazy val blacklistDAO: BlacklistDAO
  lazy val srRollingUpdateCoreService: SrRollingUpdateCoreService

  lazy val blacklistListingPaginationService: BlacklistListingPaginationService = new BlacklistListingPaginationService(
    blacklistDAO = blacklistDAO,
    srRollingUpdateCoreService = srRollingUpdateCoreService
  )

}

trait BlacklistProspectCheckDAO_DI {
  lazy val blacklistProspectCheckDAO: BlacklistProspectCheckDAO = new BlacklistProspectCheckDAO
}


trait BlacklistDAO_DI {
  //  lazy val prospectDAO: ProspectDAO
  lazy val prospectServiceV2: ProspectServiceV2

  lazy val blacklistDAO: BlacklistDAO = new BlacklistDAO(
    //    prospectDAO = prospectDAO,
    prospectServiceV2 = prospectServiceV2
  )
}


trait BlacklistServiceDI {
  lazy val prospectDAOService: ProspectDAOService
  lazy val blacklistDAO: BlacklistDAO
  lazy val prospectUpdateCategoryTemp: ProspectUpdateCategoryTemp
  lazy val prospectServiceV2: ProspectServiceV2
  lazy val srUuidUtils: SrUuidUtils
  lazy val srUuidService: SrUuidService
  lazy val accountService: AccountService
  //  lazy val prospectCategoryService: ProspectCategoryService
  lazy val organizationDAO: OrganizationDAO
  lazy val mqUpdateProspectCategoryForGivenTeamOfOrgForGlobalDNC: MqUpdateProspectCategoryForGivenTeamOfOrgForGlobalDNC
  lazy val srRollingUpdateCoreService: SrRollingUpdateCoreService

  lazy val blacklistService: BlacklistService = new BlacklistService(
    prospectDAOService = prospectDAOService,
    prospectUpdateCategoryTemp = prospectUpdateCategoryTemp,
    blacklistDAO = blacklistDAO,
    prospectServiceV2 = prospectServiceV2,
    srUuidUtils = srUuidUtils,
    srUuidService = srUuidService,
    accountService = accountService,
    //    prospectCategoryService = prospectCategoryService,
    organizationDAO = organizationDAO,
    srRollingUpdateCoreService = srRollingUpdateCoreService,
    mqUpdateProspectCategoryForGivenTeamOfOrgForGlobalDNC = mqUpdateProspectCategoryForGivenTeamOfOrgForGlobalDNC
  )
}


trait MqUpdateProspectCategoryForGivenTeamOfOrgForGlobalDNC_DI {
  lazy val blacklistServiceV2: BlacklistServiceV2
  lazy val mqUpdateProspectCategoryForGivenTeamOfOrgForGlobalDNC: MqUpdateProspectCategoryForGivenTeamOfOrgForGlobalDNC = new MqUpdateProspectCategoryForGivenTeamOfOrgForGlobalDNC(
    blacklistServiceV2 = blacklistServiceV2
  )
}

trait BlacklistServiceV2_DI {
  lazy val prospectDAOService: ProspectDAOService
  lazy val prospectUpdateCategoryTemp: ProspectUpdateCategoryTemp
  lazy val prospectServiceV2: ProspectServiceV2
  lazy val srRollingUpdateCoreService: SrRollingUpdateCoreService

  lazy val blacklistServiceV2: BlacklistServiceV2 = new BlacklistServiceV2(
    prospectDAOService = prospectDAOService,
    prospectUpdateCategoryTemp = prospectUpdateCategoryTemp,
    srRollingUpdateCoreService = srRollingUpdateCoreService,
    prospectServiceV2 = prospectServiceV2
  )
}



trait SrBlackListCheckCron_DI {
  lazy val emailSettingDAO: EmailSettingDAO
  lazy val emailNotificationService: EmailNotificationService

  lazy val srBlackListCheckCron: SrBlackListCheckCron = new SrBlackListCheckCron(
    emailSettingDAO = emailSettingDAO,
    emailNotificationService = emailNotificationService
  )
}

trait MqDoNotContactType_DI {
  lazy val dncTypeMigrationFunc: DncTypeMigrationFunc

  lazy val mqDoNotContactType: MqDoNotContactType = new MqDoNotContactType(
    dncTypeMigrationFunc = dncTypeMigrationFunc
  )

}


trait MQDoNotContactPublisher_DI {
  lazy val mqDoNotContactPublisher: MQDoNotContactPublisher = new MQDoNotContactPublisher
}
trait MQDoNotContactDI {
  lazy val blacklistService: BlacklistService
  lazy val  mqDoNotContactPublisher: MQDoNotContactPublisher

  lazy val mqDoNotContact: MQDoNotContactConsumer = new MQDoNotContactConsumer(
    blacklistService = blacklistService,
    mqDoNotContactPublisher = mqDoNotContactPublisher
  )
}


trait BlacklistApiService_DI {

  lazy val teamService: TeamService

  lazy val blacklistApiService: BlacklistApiService = new BlacklistApiService(
    teamService = teamService
  )

}

