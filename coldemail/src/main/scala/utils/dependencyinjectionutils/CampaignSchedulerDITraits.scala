package utils.dependencyinjectionutils

import api.CacheServiceJedis
import api.campaigns.dao.{CampaignSchedulingMetadataDAO, CampaignSendingVolumeLogsDAO, InternalSchedulerRunLogDAO}
import api.campaigns.services.{CampaignProspectTimezonesJedisService, CampaignSendReportService, CampaignService}
import api.emails.CampaignProspectStepScheduleLogsDAO
import utils.cronjobs.CampaignSendReportCronService
import utils.mq.channel_scheduler.{MqCampaignSchedulingMetadataMigration, MqUpdateCampaignSchedulingMetadata}

trait CampaignProspectTimezonesJedisServiceDI {
  lazy val cacheServiceJedis: CacheServiceJedis

  lazy val campaignProspectTimzonesJedisService: CampaignProspectTimezonesJedisService = new CampaignProspectTimezonesJedisService(
    cacheServiceJedis = cacheServiceJedis
  )
}

trait CampaignSendReportCronServiceDI {
  lazy val campaignSendReportService: CampaignSendReportService

  lazy val campaignSendReportCronService: CampaignSendReportCronService = new CampaignSendReportCronService(
    campaignSendReportService = campaignSendReportService,
  )
}






trait InternalSchedulerRunLogDAO_DI {
  lazy val internalSchedulerRunLogDAO: InternalSchedulerRunLogDAO = new InternalSchedulerRunLogDAO
}

trait CampaignSendingVolumeLogsDAO_DI {
  lazy val campaignSendingVolumeLogsDAO: CampaignSendingVolumeLogsDAO = new CampaignSendingVolumeLogsDAO
}

trait CampaignSchedulingMetadataDAO_DI {
  lazy val campaignSchedulingMetadataDAO: CampaignSchedulingMetadataDAO = new CampaignSchedulingMetadataDAO
}


trait MqCampaignSchedulingMetadataMigration_DI {
  lazy val campaignSchedulingMetadataDAO: CampaignSchedulingMetadataDAO

  lazy val mqCampaignSchedulingMetadataMigration: MqCampaignSchedulingMetadataMigration = new MqCampaignSchedulingMetadataMigration(
    campaignSchedulingMetadataDAO = campaignSchedulingMetadataDAO
  )
}

trait MqUpdateCampaignSchedulingMetadata_DI {
  lazy val campaignService: CampaignService

  lazy val mqUpdateCampaignSchedulingMetadata: MqUpdateCampaignSchedulingMetadata = new MqUpdateCampaignSchedulingMetadata(
    campaignService = campaignService
  )
}

trait CampaignProspectStepScheduleLogsDAO_DI {
  lazy val campaignProspectStepScheduleLogsDAO: CampaignProspectStepScheduleLogsDAO = new CampaignProspectStepScheduleLogsDAO
}

