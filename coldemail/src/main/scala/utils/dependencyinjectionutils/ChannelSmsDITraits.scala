package utils.dependencyinjectionutils

import api.accounts.PermissionUtils
import api.sms.{SmsSettingController, SmsSettingDAO, SmsSettingService, SmsSettingUtils}
import play.api.mvc.ControllerComponents
import utils.cache_utils.service.SrRedisSimpleLockServiceV2
import utils.uuid.SrUuidUtils


object SmsSettingDI {
  trait SmsSettingControllerDI {
    lazy val controllerComponents: ControllerComponents
    lazy val smsSettingService: SmsSettingService
    lazy val permissionUtils: PermissionUtils
    lazy val smsSettingUtils: SmsSettingUtils

    lazy val smsSettingController: SmsSettingController = new SmsSettingController(
      controllerComponents = controllerComponents,
      smsSettingService = smsSettingService,
      smsSettingUtils = smsSettingUtils,
      permissionUtils = permissionUtils
    )
  }

  trait SmsSettingServiceDI {
    lazy val smsSettingDAO: SmsSettingDAO
    lazy val srUuidUtils: SrUuidUtils
    lazy val srRedisSimpleLockServiceV2: SrRedisSimpleLockServiceV2

    lazy val smsSettingService: SmsSettingService = new SmsSettingService(
      smsSettingDAO = smsSettingDAO,
      srRedisSimpleLockServiceV2 = srRedisSimpleLockServiceV2,
      srUuidUtils = srUuidUtils
    )
  }

  trait SmsSettingDAODI {
    lazy val smsSettingDAO: SmsSettingDAO = new SmsSettingDAO
  }

  trait SmsSettingUtilsDI {
    lazy val smsSettingService: SmsSettingService

    lazy val smsSettingUtils: SmsSettingUtils = new SmsSettingUtils(
      smsSettingService = smsSettingService
    )
  }

}