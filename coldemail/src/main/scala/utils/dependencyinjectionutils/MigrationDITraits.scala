package utils.dependencyinjectionutils

import api.accounts.{AccountDAO, MigrationDAO, MigrationService, MigrationUtils}
import api.prospects.ProspectAccountDAO1
import api.prospects.dao.ProspectDAO
import utils.dbutils.DBUtils
import utils.mq.{MqCampaignsProspectsMigrationScript, MqUpdateUuidMigrationScript, MqUpdatedAtMigrationScript, MqUuidMigrationScript}
import utils.mq.blacklist.DncTypeMigrationFunc
import utils.mq.email.MqInternalTeamBasedMigration
import utils.mq.multichannel.MqRefactorLinkedinUrlMigration
import utils.uuid.{MigrationFunc, SrUuidUtils}


trait DncTypeMigrationFunc_DI {

  lazy val dbUtils: DBUtils

  lazy val dncTypeMigrationFunc: DncTypeMigrationFunc = new DncTypeMigrationFunc(
    dbUtils = dbUtils
  )

}


trait MigrationDAO_DI {
  lazy val migrationDAO: MigrationDAO = new MigrationDAO
}

trait MigrationService_DI {

  lazy val migrationDAO: MigrationDAO
  lazy val accountDAO: AccountDAO

  lazy val migrationService: MigrationService = new MigrationService(
    migrationDAO = migrationDAO,
    accountDAO = accountDAO
  )
}


trait MqInternalTeamBasedMigration_DI {
  lazy val migrationUtils: MigrationUtils

  lazy val mqInternalTeamBasedMigration: MqInternalTeamBasedMigration = new MqInternalTeamBasedMigration(
    migrationUtils = migrationUtils
  )
}

trait MqUpdatedAtMigrationScript_DI {
  lazy val migrationFunc: MigrationFunc
  lazy val mqUpdatedAtMigrationScript: MqUpdatedAtMigrationScript = new MqUpdatedAtMigrationScript(
    migrationFunc = migrationFunc
  )
}

trait MqCampaignsProspectsMigrationScript_DI {
  lazy val migrationFunc: MigrationFunc
  lazy val mqCampaignsProspectsMigrationScript: MqCampaignsProspectsMigrationScript = new MqCampaignsProspectsMigrationScript(
    migrationFunc = migrationFunc
  )
}



trait MqUuidMigrationScript_DI {
  lazy val migrationFunc: MigrationFunc
  lazy val mqUuidMigrationScript: MqUuidMigrationScript = new MqUuidMigrationScript(
    migrationFunc = migrationFunc
  )
}

trait MqUpdateUuidMigrationScript_DI {
  lazy val migrationUtils: MigrationUtils
  lazy val mqUpdateUuidMigrationScript: MqUpdateUuidMigrationScript = new MqUpdateUuidMigrationScript(
    migrationUtils = migrationUtils
  )
}

trait MqRefactorLinkedinUrlMigration_DI {
  lazy val prospectDAO: ProspectDAO

  lazy val mqRefactorLinkedinUrl: MqRefactorLinkedinUrlMigration = new MqRefactorLinkedinUrlMigration(
    prospectDAO = prospectDAO
  )
}

trait MigrationFuncDI {
  lazy val srUuidUtils: SrUuidUtils
  lazy val dbUtils: DBUtils
  lazy val prospectAccountDAO: ProspectAccountDAO1

  lazy val migrationFunc: MigrationFunc = new MigrationFunc(
    srUuidUtils = srUuidUtils,
    dbUtils = dbUtils,
    prospectAccountDAO = prospectAccountDAO
  )

}

