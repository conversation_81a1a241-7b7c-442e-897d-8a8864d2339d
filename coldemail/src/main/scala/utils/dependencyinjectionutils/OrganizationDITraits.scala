package utils.dependencyinjectionutils

import api.CacheServiceJedis
import api.accounts.AccountService
import api.accounts.dao.{OrganizationBillingDAO, OrganizationDAO, OrganizationJedisCacheDao}
import api.accounts.dao_service.OrganizationDAOService
import api.accounts.service.OrganizationService
import api.billing.v2.services.OrgDeactivateService
import api.call.service.CallService
import api.campaigns.services.CampaignService
import utils.featureflags.dao.OrgMetadataDAO
import utils.featureflags.services.OrgMetadataService
import utils.uuid.services.SrUuidService

trait OrganizationService_DI {

  lazy val organizationDAO: OrganizationDAO
  lazy val organizationJedisCacheDao: OrganizationJedisCacheDao
  lazy val organizationBillingDAO: OrganizationBillingDAO
  lazy val srUuidService: SrUuidService
  lazy val organizationDAOService: OrganizationDAOService

  lazy val organizationService: OrganizationService = new OrganizationService(
    organizationDAO = organizationDAO,
    organizationJedisCacheDao = organizationJedisCacheDao,
    organizationBillingDAO = organizationBillingDAO,
    srUuidService = srUuidService,
    organizationDAOService = organizationDAOService
  )

}

trait OrganizationJedisCacheDao_DI {

  lazy val cacheServiceJedis: CacheServiceJedis

  lazy val organizationJedisCacheDao: OrganizationJedisCacheDao = new OrganizationJedisCacheDao(
    cacheServiceJedis = cacheServiceJedis
  )

}



trait OrgMetadataServiceDI {
  lazy val orgMetadataDAO: OrgMetadataDAO

  lazy val orgMetadataService: OrgMetadataService = new OrgMetadataService(
    orgMetadataDAO = orgMetadataDAO
  )
}




trait OrgDeactivateService_DI {

  lazy val campaignService: CampaignService
  lazy val accountService: AccountService
  lazy val organizationBillingDAO: OrganizationBillingDAO
  lazy val callService: CallService

  lazy val orgDeactivateService: OrgDeactivateService = new OrgDeactivateService(
    campaignService = campaignService,
    accountService = accountService,
    organizationBillingDAO = organizationBillingDAO,
    callService = callService
  )
}




trait OrganizationDAOService_DI {
  lazy val organizationDAO: OrganizationDAO
  lazy val organizationJedisCacheDao: OrganizationJedisCacheDao
  lazy val organizationDAOService: OrganizationDAOService = new OrganizationDAOService(
    organizationDAO = organizationDAO,
    organizationJedisCacheDao = organizationJedisCacheDao
  )
}



trait OrganizationDAO_DI {
  lazy val organizationDAO: OrganizationDAO = new OrganizationDAO
}


trait OrgMetadataDAO_DI {
  lazy val orgMetadataDAO: OrgMetadataDAO = new OrgMetadataDAO
}
