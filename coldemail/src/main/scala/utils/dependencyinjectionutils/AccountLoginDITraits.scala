package utils.dependencyinjectionutils

import api.CacheServiceJedis
import api.accounts.AccountService
import api.accounts.service.{AccountOTPDBDAOService, AccountOTPService, OTPJedisDAOService}
import api.google_recaptcha.service.{GoogleRecaptchaApiService, SRGoogleRecaptchaServices}
import play.api.libs.ws.WSClient

import scala.concurrent.ExecutionContext


trait AccountOTPService_DI {
  lazy val otpJedisDAOService: OTPJedisDAOService
  lazy val accountOTPDBDAOService: AccountOTPDBDAOService
  lazy val accountOTPService: AccountOTPService = new AccountOTPService(
    otpDBDAOService = accountOTPDBDAOService,
    otpJedisDAOService = otpJedisDAOService
  )
}

trait AccountOTPDBDAOService_DI {

  lazy val accountService: AccountService

  lazy val accountOTPDBDAOService: AccountOTPDBDAOService = new AccountOTPDBDAOService(
    accountService = accountService
  )
}

trait OTPJedisDAOService_DI {
  lazy val cacheServiceJedis: CacheServiceJedis

  lazy val otpJedisDAOService: OTPJedisDAOService = new OTPJedisDAOService(
    cacheServiceJedis = cacheServiceJedis
  )
}

trait SRGoogleRecaptchaServices_DI {
  lazy val cacheServiceJedis: CacheServiceJedis
  lazy val googleRecaptchaApiService: GoogleRecaptchaApiService
  lazy val playDefaultExecutionContext: ExecutionContext
  lazy val sRGoogleRecaptchaServices: SRGoogleRecaptchaServices = new SRGoogleRecaptchaServices(
    cacheServiceJedis = cacheServiceJedis,
    googleRecaptchaApiService = googleRecaptchaApiService,
    ec = playDefaultExecutionContext
  )
}

trait GoogleRecaptchaApiService_DI {
  lazy val wSClient: WSClient
  lazy val playDefaultExecutionContext: ExecutionContext
  lazy val googleRecaptchaApiService: GoogleRecaptchaApiService = new GoogleRecaptchaApiService()(
    ws = wSClient,
    ec = playDefaultExecutionContext
  )
}



