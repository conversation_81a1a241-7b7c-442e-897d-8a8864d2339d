package utils.dependencyinjectionutils

import api.CacheServiceJedis
import api.accounts.PermissionUtils
import api.columns.services.{MqProcessAndGenerateMagicColumn, ProspectColumnService}
import api.pipelines.controllers.{OpportunityController, OpportunityStatusController, PipelineController, TeamsPipelineConfigController}
import api.pipelines.dao.{OpportunityDAO, OpportunityJedisDAO, OpportunityStatusDAO, PipelineDAO, TeamsPipelineConfigDAO}
import api.pipelines.services.{AnnualizedValueService, MqOpportunitiesDefaultSetupMigration, OpportunitiesDefaultSetupService, OpportunityDAOService, OpportunityService, OpportunityStatusCommonService, OpportunityStatusDAOService, OpportunityStatusService, PipelineService, TeamsPipelineConfigService}
import api.pipelines.utils.OpportunityUtils
import api.prospects.dao_service.ProspectDAOService
import api_layer_service.ApiLayerService
import play.api.libs.ws.WSClient
import play.api.mvc.ControllerComponents
import utils.PusherService
import utils.dbutils.DBUtils
import utils.uuid.SrUuidUtils
import utils.uuid.services.SrUuidService


trait OpportunitiesDefaultSetupService_DI {

  lazy val pipelineDAO: PipelineDAO
  lazy val srUuidUtils: SrUuidUtils
  lazy val opportunityStatusDAO: OpportunityStatusDAO
  lazy val teamsPipelineConfigService: TeamsPipelineConfigService

  lazy val opportunitiesDefaultSetupService: OpportunitiesDefaultSetupService = new OpportunitiesDefaultSetupService(
    opportunityStatusDAO = opportunityStatusDAO,
    srUuidUtils = srUuidUtils,
    pipelineDAO = pipelineDAO,
    teamsPipelineConfigService = teamsPipelineConfigService
  )
}

trait MqOpportunitiesDefaultSetupMigration_DI {

  lazy val opportunitiesDefaultSetupService: OpportunitiesDefaultSetupService
  lazy val dbUtils: DBUtils

  lazy val mqOpportunitiesDefaultSetupMigration: MqOpportunitiesDefaultSetupMigration = new MqOpportunitiesDefaultSetupMigration(
    opportunitiesDefaultSetupService = opportunitiesDefaultSetupService,
    dbUtils = dbUtils
  )

}

trait MqProcessAndGenerateMagicColumn_DI {

  lazy val prospectColumnService: ProspectColumnService

  lazy val mqProcessAndGenerateMagicColumn: MqProcessAndGenerateMagicColumn = new MqProcessAndGenerateMagicColumn(
    prospectColumnService = prospectColumnService,
  )

}

trait TeamsPipelineConfigDAO_DI {
  lazy val teamsPipelineConfigDAO: TeamsPipelineConfigDAO = new TeamsPipelineConfigDAO
}

trait TeamsPipelineConfigService_DI {

  lazy val teamsPipelineConfigDAO: TeamsPipelineConfigDAO

  lazy val teamsPipelineConfigService: TeamsPipelineConfigService = new TeamsPipelineConfigService(
    teamsPipelineConfigDAO = teamsPipelineConfigDAO
  )

}

trait TeamsPipelineConfigController_DI {

  lazy val teamsPipelineConfigService: TeamsPipelineConfigService
  lazy val permissionUtils: PermissionUtils
  lazy val controllerComponents: ControllerComponents
  lazy val wsClient: WSClient

  lazy val teamsPipelineConfigController: TeamsPipelineConfigController = new TeamsPipelineConfigController(
    teamsPipelineConfigService = teamsPipelineConfigService,
    permissionUtils = permissionUtils,
    controllerComponents = controllerComponents,
    ws = wsClient
  )

}

trait PipelineDAO_DI {
  lazy val pipelineDAO: PipelineDAO = new PipelineDAO
}

trait PipelineService_DI {

  lazy val pipelineDAO: PipelineDAO
  lazy val srUuidUtils: SrUuidUtils
  lazy val dbUtils: DBUtils
  lazy val opportunitiesDefaultSetupService: OpportunitiesDefaultSetupService

  lazy val pipelineService: PipelineService = new PipelineService(
    pipelineDAO = pipelineDAO,
    srUuidUtils = srUuidUtils,
    dbUtils = dbUtils,
    opportunitiesDefaultSetupService = opportunitiesDefaultSetupService
  )
}

trait PipelineController_DI {

  lazy val pipelineService: PipelineService
  lazy val permissionUtils: PermissionUtils
  lazy val controllerComponents: ControllerComponents
  lazy val wSClient: WSClient

  lazy val pipelineController: PipelineController = new PipelineController(
    pipelineService = pipelineService,
    permissionUtils = permissionUtils,
    controllerComponents = controllerComponents,
    ws = wSClient
  )
}

trait OpportunityStatusDAO_DI {
  lazy val opportunityStatusDAO: OpportunityStatusDAO = new OpportunityStatusDAO
}

trait OpportunityStatusService_DI {

  lazy val opportunityStatusDAO: OpportunityStatusDAO
  lazy val srUuidUtils: SrUuidUtils
  lazy val srUuidService: SrUuidService
  lazy val pipelineService: PipelineService
  lazy val opportunityStatusDAOService: OpportunityStatusDAOService

  lazy val opportunityStatusService: OpportunityStatusService = new OpportunityStatusService(
    opportunityStatusDAO = opportunityStatusDAO,
    opportunityStatusDAOService = opportunityStatusDAOService,
    srUuidUtils = srUuidUtils,
    srUuidService = srUuidService,
    pipelineService = pipelineService
  )
}

trait OpportunityStatusDAOService_DI {

  lazy val opportunityStatusDAO: OpportunityStatusDAO
  lazy val dbUtils: DBUtils
  lazy val opportunityDAOService: OpportunityDAOService

  lazy val opportunityStatusDAOService: OpportunityStatusDAOService = new OpportunityStatusDAOService(
    opportunityStatusDAO = opportunityStatusDAO,
    dbUtils = dbUtils,
    opportunityDAOService = opportunityDAOService
  )
}

trait AnnualizedValueService_DI {

  lazy val opportunityStatusDAO: OpportunityStatusDAO
  lazy val pusherService: PusherService

  lazy val annualizedValueService: AnnualizedValueService = new AnnualizedValueService(
    opportunityStatusDAO = opportunityStatusDAO,
    pusherService = pusherService
  )
}

trait OpportunityStatusCommonService_DI {

  lazy val opportunityStatusDAO: OpportunityStatusDAO

  lazy val opportunityStatusCommonService: OpportunityStatusCommonService = new OpportunityStatusCommonService(
    opportunityStatusDAO = opportunityStatusDAO
  )
}

trait OpportunityStatusController_DI {

  lazy val opportunityStatusService: OpportunityStatusService
  lazy val permissionUtils: PermissionUtils
  lazy val controllerComponents: ControllerComponents
  lazy val opportunityUtils: OpportunityUtils
  lazy val wSClient: WSClient

  lazy val opportunityStatusController: OpportunityStatusController = new OpportunityStatusController(
    opportunityStatusService = opportunityStatusService,
    permissionUtils = permissionUtils,
    controllerComponents = controllerComponents,
    opportunityUtils = opportunityUtils,
    ws = wSClient
  )
}

trait OpportunityDAO_DI {
  lazy val opportunityDAO: OpportunityDAO = new OpportunityDAO
}


trait OpportunityDAOService_DI {

  lazy val opportunityDAO: OpportunityDAO
  lazy val srUuidUtils: SrUuidUtils
  lazy val annualizedValueService: AnnualizedValueService
  lazy val dbUtils: DBUtils

  lazy val opportunityDAOService: OpportunityDAOService = new OpportunityDAOService(
    annualizedValueService = annualizedValueService,
    opportunityDAO = opportunityDAO,
    dbUtils = dbUtils,
    srUuidUtils = srUuidUtils
  )
}

trait OpportunityJedisDAO_DI {

  lazy val cacheServiceJedis: CacheServiceJedis

  lazy val opportunityJedisDAO: OpportunityJedisDAO = new OpportunityJedisDAO(
    cacheServiceJedis = cacheServiceJedis
  )

}


trait OpportunityService_DI {

  lazy val opportunityDAO: OpportunityDAO
  lazy val srUuidService: SrUuidService
  lazy val opportunityDAOService: OpportunityDAOService
  lazy val pusherService: PusherService
  lazy val opportunityStatusCommonService: OpportunityStatusCommonService
  lazy val apiLayerService: ApiLayerService
  lazy val opportunityJedisDAO: OpportunityJedisDAO

  lazy val opportunityService: OpportunityService = new OpportunityService(
    opportunityDAO = opportunityDAO,
    srUuidService = srUuidService,
    pusherService = pusherService,
    opportunityDAOService = opportunityDAOService,
    opportunityStatusCommonService = opportunityStatusCommonService,
    apiLayerService = apiLayerService,
    opportunityJedisDAO = opportunityJedisDAO
  )
}

trait OpportunityUtils_DI {

  lazy val opportunityService: OpportunityService
  lazy val prospectDAOService: ProspectDAOService
  lazy val pipelineService: PipelineService

  lazy val opportunityUtils: OpportunityUtils = new OpportunityUtils(
    opportunityService = opportunityService,
    prospectDAOService = prospectDAOService,
    pipelineService = pipelineService
  )

}

trait OpportunityController_DI {

  lazy val opportunityService: OpportunityService
  lazy val opportunityUtils: OpportunityUtils
  lazy val permissionUtils: PermissionUtils
  lazy val controllerComponents: ControllerComponents
  lazy val wSClient: WSClient

  lazy val opportunityController: OpportunityController = new OpportunityController(
    opportunityService = opportunityService,
    opportunityUtils = opportunityUtils,
    permissionUtils = permissionUtils,
    controllerComponents = controllerComponents,
    ws = wSClient
  )
}