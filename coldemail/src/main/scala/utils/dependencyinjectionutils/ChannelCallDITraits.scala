package utils.dependencyinjectionutils

import api.ApiService
import api.accounts.{AccountDAO, PermissionUtils}
import api.accounts.dao.OrganizationDAO
import api.accounts.dao_service.OrganizationDAOService
import api.accounts.service.ResetUserCacheUtil
import api.call.DAO.{CallHistoryLogDAO, CallDAO}
import api.call.controller.CallController
import api.call.service.{CallApiService, CallService}
import api.campaigns.services.CampaignService
import api.middleware.LoggingAction
import api.notes.dao.NotesDAO
import api.prospects.dao.{ProspectAddEventDAO, ProspectDAO}
import api.prospects.dao_service.ProspectDAOService
import api.team.service.TeamService
import api.twilio.service.TwilioDialerService
import api_layer_service.ApiLayerService
import play.api.libs.ws.WSClient
import play.api.mvc.ControllerComponents
import utils.{CallUtils, PlanLimitService, PusherService}
import utils.cache_utils.service.SrRedisSimpleLockServiceV2
import utils.cronjobs.{UpdateCallHistoryLogCron, UpdateCallingCreditCron}
import utils.dbutils.DBUtils
import utils.email_notification.service.EmailNotificationService
import utils.mq.call.{MQUpdateCallNoteId, MqUpdateSubAccountCallHistory, MqUpdateSubAccountCallingCredit}
import utils.uuid.SrUuidUtils


trait CallControllerDI {

  lazy val permissionUtils: PermissionUtils
  lazy val controllerComponents: ControllerComponents
  lazy val twilioDialerService: TwilioDialerService
  lazy val callService: CallService
  lazy val emailNotificationService: EmailNotificationService
  lazy val campaignService: CampaignService
  implicit lazy val wSClient: WSClient
  lazy val loggingAction: LoggingAction
  lazy val apiService: ApiService


  lazy val callController: CallController = new CallController(
    controllerComponents = controllerComponents,
    permissionUtils = permissionUtils,
    twilioDialerService = twilioDialerService,
    callService = callService,
    emailNotificationService = emailNotificationService,
    campaignService = campaignService,
    loggingAction = loggingAction,
    wSClient = wSClient,
    apiService = apiService
  )

}

trait TwilioDialerServiceDI {
  lazy val twilioDialerService: TwilioDialerService = new TwilioDialerService
}

trait CallServiceDI {
  lazy val twilioDialerService: TwilioDialerService
  lazy val srUuidUtils: SrUuidUtils
  lazy val callDAO: CallDAO
  lazy val prospectAddEventDAO: ProspectAddEventDAO
  lazy val pusherService: PusherService
  lazy val organizationDAO: OrganizationDAO
  lazy val prospectDAO: ProspectDAO
  lazy val resetUserCacheUtil: ResetUserCacheUtil
  lazy val accountDAO: AccountDAO
  lazy val srRedisSimpleLockServiceV2: SrRedisSimpleLockServiceV2
  lazy val emailNotificationService: EmailNotificationService
  lazy val apiLayerService: ApiLayerService
  lazy val callUtils: CallUtils
  lazy val callHistoryLogDao: CallHistoryLogDAO
  lazy val planLimitService: PlanLimitService
  lazy val organizationDAOService: OrganizationDAOService
  lazy val prospectDAOService: ProspectDAOService
  lazy val notesDAO: NotesDAO

  lazy val callService: CallService = new CallService(
    twilioDialerService = twilioDialerService,
    srUuidUtils = srUuidUtils,
    callDAO = callDAO,
    prospectAddEventDAO = prospectAddEventDAO,
    pusherService = pusherService,
    organisationDAO = organizationDAO,
    prospectDAO = prospectDAO,
    resetCacheUtil = resetUserCacheUtil,
    accountDAO = accountDAO,
    organizationDAOService = organizationDAOService,
    planLimitService = planLimitService,
    emailNotificationService = emailNotificationService,
    apiLayerService = apiLayerService,
    srRedisSimpleLockServiceV2 = srRedisSimpleLockServiceV2,
    callUtils = callUtils,
    callHistoryLogDAO = callHistoryLogDao,
    prospectDAOService = prospectDAOService,
    notesDAO = notesDAO
  )
}

trait CallHistoryLogDAO_DI {

  lazy val callHistoryLogDao: CallHistoryLogDAO = new CallHistoryLogDAO()

}

trait CallUtilsDI {

  lazy val callUtils: CallUtils = new CallUtils()

}

trait CallApiService_DI {

  lazy val teamService: TeamService

  lazy val callApiService: CallApiService = new CallApiService(
    teamService = teamService
  )

}

trait CallDAO_DI {
  lazy val callDAO: CallDAO = new CallDAO
}


trait UpdateCallingCreditCron_DI {
  lazy val callService: CallService
  lazy val mqUpdateSubAccountCallingCredit: MqUpdateSubAccountCallingCredit
  lazy val updateCallingCreditCron: UpdateCallingCreditCron = new UpdateCallingCreditCron(
    callService = callService,
    mqUpdateSubAccountCallingCredit = mqUpdateSubAccountCallingCredit
  )
}



trait UpdateCallHistoryLogCron_DI {
  lazy val callService: CallService
  lazy val mqUpdateSubAccountCallHistory: MqUpdateSubAccountCallHistory
  lazy val updateCallHistoryLogCron: UpdateCallHistoryLogCron = new UpdateCallHistoryLogCron(
    callService = callService,
    mqUpdateSubAccountCallHistory = mqUpdateSubAccountCallHistory
  )
}

trait MqUpdateSubAccountCallingCredit_DI {
  lazy val callService: CallService
  lazy val mqUpdateSubAccountCallingCredit: MqUpdateSubAccountCallingCredit = new MqUpdateSubAccountCallingCredit(
    callService = callService
  )
}

trait MqUpdateSubAccountCallHistory_DI {
  lazy val callService: CallService
  lazy val mqUpdateSubAccountCallHistory: MqUpdateSubAccountCallHistory = new MqUpdateSubAccountCallHistory(
    callService = callService
  )
}


trait MQUpdateCallNoteId_DI {

  lazy val dbUtils: DBUtils

  lazy val mqUpdateCallNoteId: MQUpdateCallNoteId = new MQUpdateCallNoteId(
    dbUtils = dbUtils
  )

}



