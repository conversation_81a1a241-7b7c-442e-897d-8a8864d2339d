package utils.dependencyinjectionutils

import api.campaigns.services.{CampaignDAOService, CampaignProspectService, CampaignService}
import api.email_pre_validation.{MqPreEmailValidation, SrPreValidationCron}
import api.lead_finder.service.LeadFinderValidationService
import api.prospects.service.ProspectServiceV2
import api.scheduler_report.SchedulerIntegrityService
import utils.GCP.CloudStorage
import utils.cronjobs.spammonitor.EmailDeliveryAnalysisImpl
import utils.email_notification.service.EmailNotificationService
import utils.emailvalidation.{BouncerEmailValidationApi, DeBounceEmailValidationApi, EmailValidationApiToolsRecordDAO, EmailValidationBatchRequestModel, EmailValidationDailyReportService, EmailValidationModel, EmailValidationService, ListCleanEmailValidationApi, TransformAndSaveEmailValidationResult, TransformAndSaveEmailValidationResultTrait}
import utils.emailvalidation.dao_service.EmailValidationDAOService
import utils.mq.prospect_category.MqAutoUpdateProspectCategoryPublisher
import utils.mq.webhook.MQWebhookEmailInvalid
import utils.random.SrRandomUtils
import utils.uuid.SrUuidUtils

trait EmailValidationApiToolsRecordDAODI {
  lazy val emailValidationApiToolsRecordDAO: EmailValidationApiToolsRecordDAO = new EmailValidationApiToolsRecordDAO
}

trait TransformAndSaveEmailValidationResultDI {
  lazy val emailValidationModel: EmailValidationModel
  lazy val emailDeliveryAnalysisService: EmailDeliveryAnalysisImpl
  lazy val emailValidationBatchRequestModel: EmailValidationBatchRequestModel
  lazy val transformAndSaveEmailValidationResult: TransformAndSaveEmailValidationResult =
    new TransformAndSaveEmailValidationResult(
      emailValidationModel = emailValidationModel,
      emailDeliveryAnalysisService = emailDeliveryAnalysisService,
      emailValidationBatchRequestModel = emailValidationBatchRequestModel
    )
}

trait EmailValidationBatchRequestModelDI {
  lazy val emailValidationBatchRequestModel: EmailValidationBatchRequestModel =
    new EmailValidationBatchRequestModel
}

trait EmailValidationDAOService_DI {
  lazy val emailValidationModel: EmailValidationModel
  lazy val emailValidationApiToolsRecordDAO: EmailValidationApiToolsRecordDAO

  lazy val emailValidationDAOService: EmailValidationDAOService = new EmailValidationDAOService(
    emailValidationModel = emailValidationModel,
    emailValidationApiToolsRecordDAO = emailValidationApiToolsRecordDAO
  )
}

trait EmailValidationServiceDI {

  //  lazy val prospectDAO: Prospect
  lazy val emailValidationModel: EmailValidationModel
  lazy val transformAndSaveEmailValidationResult: TransformAndSaveEmailValidationResultTrait
  lazy val emailValidationBatchRequestModel: EmailValidationBatchRequestModel
  lazy val emailValidationApiToolsRecordDAO: EmailValidationApiToolsRecordDAO
  // val mqMBLEmailValidator: MQMBLEmailValidator
  lazy val bouncerEmailValidationApi: BouncerEmailValidationApi
  lazy val deBounceEmailValidationApi: DeBounceEmailValidationApi
  lazy val listCleanEmailValidationApi: ListCleanEmailValidationApi
  lazy val mqWebhookEmailInvalid: MQWebhookEmailInvalid
  lazy val prospectServiceV2: ProspectServiceV2
  lazy val emailValidationDAOService: EmailValidationDAOService
  lazy val cloudStorage: CloudStorage
  lazy val srRandomUtils: SrRandomUtils
  lazy val srUuidUtils: SrUuidUtils
  lazy val leadFinderValidationService: LeadFinderValidationService
  lazy val mqAutoUpdateProspectCategoryPublisher: MqAutoUpdateProspectCategoryPublisher

  lazy val emailValidationService: EmailValidationService = new EmailValidationService(
    //    prospectDAO = prospectDAO,
    emailValidationModel = emailValidationModel,
    transformAndSaveEmailValidationResult = transformAndSaveEmailValidationResult,
    emailValidationBatchRequestModel = emailValidationBatchRequestModel,
    emailValidationApiToolsRecordDAO = emailValidationApiToolsRecordDAO,
    emailValidationDAOService = emailValidationDAOService,
    bouncerEmailValidationApi = bouncerEmailValidationApi,
    deBouncerEmailValidationApi = deBounceEmailValidationApi,
    listCleanEmailValidationApi = listCleanEmailValidationApi,
    //mqMBLEmailValidator = mqMBLEmailValidator,
    mqWebhookEmailInvalid = mqWebhookEmailInvalid,
    prospectServiceV2 = prospectServiceV2,
    cloudStorage = cloudStorage,
    srRandomUtils = srRandomUtils,
    srUuidUtils = srUuidUtils,
    mqAutoUpdateProspectCategoryPublisher = mqAutoUpdateProspectCategoryPublisher,
    leadFinderValidationService = leadFinderValidationService,
  )

}

trait EmailValidationDailyReportService_DI {
  lazy val emailValidationApiToolsRecordDAO: EmailValidationApiToolsRecordDAO
  lazy val schedulerIntegrityService: SchedulerIntegrityService
  lazy val emailNotificationService: EmailNotificationService

  lazy val emailValidationDailyReportService: EmailValidationDailyReportService = new EmailValidationDailyReportService(
    emailValidationApiToolsRecordDAO = emailValidationApiToolsRecordDAO,
    schedulerIntegrityService = schedulerIntegrityService,
    emailNotificationService = emailNotificationService
  )
}

trait BouncerEmailValidationApiDI {
  lazy val emailValidationApiToolsRecordDAO: EmailValidationApiToolsRecordDAO

  lazy val bouncerEmailValidationApi: BouncerEmailValidationApi = new BouncerEmailValidationApi(
    emailValidationApiToolsRecordDAO = emailValidationApiToolsRecordDAO
  )
}

trait DeBounceEmailValidationApiDI {
  lazy val emailValidationApiToolsRecordDAO: EmailValidationApiToolsRecordDAO
  lazy val deBounceEmailValidationApi: DeBounceEmailValidationApi = new DeBounceEmailValidationApi(
    emailValidationApiToolsRecordDAO = emailValidationApiToolsRecordDAO
  )
}

trait ListCleanEmailValidationApiDI {
  lazy val emailValidationApiToolsRecordDAO: EmailValidationApiToolsRecordDAO
  lazy val listCleanEmailValidationApi: ListCleanEmailValidationApi = new ListCleanEmailValidationApi(
    emailValidationApiToolsRecordDAO = emailValidationApiToolsRecordDAO
  )
}

trait MqPreEmailValidationDI {
  lazy val campaignProspectService: CampaignProspectService
  lazy val campaignService: CampaignService
  lazy val emailValidationService: EmailValidationService
  lazy val campaignDAOService: CampaignDAOService

  lazy val mqPreEmailValidation: MqPreEmailValidation = new MqPreEmailValidation(
    campaignDAOService = campaignDAOService,
    campaignProspectService = campaignProspectService,
    campaignService = campaignService,
    emailValidationService = emailValidationService
  )

}

trait SrPreValidationCronDI {
  lazy val campaignService: CampaignService
  lazy val mqPreEmailValidation: MqPreEmailValidation
  lazy val campaignProspectService: CampaignProspectService

  lazy val srPreValidationCron: SrPreValidationCron = new SrPreValidationCron(
    campaignService = campaignService,
    mqPreEmailValidation = mqPreEmailValidation,
    campaignProspectService = campaignProspectService
  )

}
