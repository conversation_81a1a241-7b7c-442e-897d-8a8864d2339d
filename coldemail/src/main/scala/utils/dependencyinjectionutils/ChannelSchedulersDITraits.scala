package utils.dependencyinjectionutils

import api.CacheServiceJedis
import api.accounts.AccountService
import api.campaigns.{CampaignDAO, CampaignProspectDAO, CampaignStepVariantDAO}
import api.campaigns.services.{CampaignDAOService, CampaignProspectService, CampaignService}
import api.emails.{CampaignProspectStepScheduleLogsDAO, EmailMessageDataDAO, EmailScheduledDAO, EmailSettingDAO}
import api.emails.dao_service.EmailScheduledDAOService
import api.general.GeneralSettingDAO
import api.gpt.ai_hyperpersonalized.AIHyperPersonalizedGenerator
import api.linkedin.LinkedinSettingDAO
import api.llm.dao.LlmAuditLogDAO
import api.rep_tracking_hosts.service.RepTrackingHostService
import api.tasks.pgDao.TaskPgDAO
import utils.cache_utils.service.{SrRedisHashSetBasedLockServiceV2, SrRedisHashSetServiceV2, SrRedisSimpleLockServiceV2}
import utils.cronjobs.IndependentStepSchedulerCron
import utils.dateTime.SrDateTimeUtils
import utils.dbutils.DBUtils
import utils.email.EmailServiceCompanion
import utils.emailvalidation.EmailValidationService
import utils.featureflags.services.OrgMetadataService
import utils.mq.channel_scheduler.{MQIndependentStepScheduler, MqCampaignSchedulingMetadataMigration}
import utils.mq.channel_scheduler.channels.{EmailChannelScheduler, GeneralChannelScheduler, IndependentStepSchedulerService, LinkedinChannelScheduler}
import utils.mq.channel_scheduler.channels.service.EmailSchedulerJedisService
import utils.random.SrRandomUtils
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService
import utils.mq.ai_content_generation.MqAiContentGenerationPublisher
import utils.uuid.SrUuidUtils


object ChannelSchedulers {

  trait EmailChannelSchedulerDI {

    lazy val emailSettingDAO: EmailSettingDAO
    lazy val emailServiceCompanion: EmailServiceCompanion
    //    lazy val accountService: AccountService
    lazy val srRandomUtils: SrRandomUtils
    lazy val orgMetadataService: OrgMetadataService
    lazy val emailValidationService: EmailValidationService
    lazy val emailSchedulerJedisService: EmailSchedulerJedisService
    //    lazy val dbUtils: DBUtils

    lazy val repTrackingHostService: RepTrackingHostService
    lazy val mqCampaignSchedulingMetadataMigration: MqCampaignSchedulingMetadataMigration
    lazy val emailScheduledDAOService: EmailScheduledDAOService
    lazy val srDateTimeUtils: SrDateTimeUtils
    lazy val campaignProspectService: CampaignProspectService
    lazy val srRollingUpdateCoreService: SrRollingUpdateCoreService
    lazy val aiHyperPersonalizedGenerator : AIHyperPersonalizedGenerator
    lazy val campaignDAOService: CampaignDAOService
    // Add MqAiContentGenerationPublisher as a required dependency
    lazy val mqAiContentGenerationPublisher: MqAiContentGenerationPublisher
    lazy val llmAuditLogDAO : LlmAuditLogDAO
    lazy val srUuidUtils: SrUuidUtils


    final lazy val emailChannelScheduler: EmailChannelScheduler = new EmailChannelScheduler(
      emailValidationService = emailValidationService,
      emailServiceCompanion = emailServiceCompanion,
      //      accountService = accountService,
      srRandomUtils = srRandomUtils,
      orgMetadataService = orgMetadataService,
      emailSettingDAO = emailSettingDAO,
      emailScheduledDAOService = emailScheduledDAOService,
      emailSchedulerJedisService = emailSchedulerJedisService,
      repTrackingHostService = repTrackingHostService,
      srDateTimeUtils = srDateTimeUtils,
      //      dbUtils = dbUtils,
      mqCampaignSchedulingMetadataMigration = mqCampaignSchedulingMetadataMigration,
      aiHyperPersonalizedService = aiHyperPersonalizedGenerator,
      campaignProspectService = campaignProspectService,
      srRollingUpdateCoreService = srRollingUpdateCoreService,
      campaignDAOService = campaignDAOService,
      // Pass the publisher to the constructor
      llmAuditLogDAO = llmAuditLogDAO,
      srUuidUtils = srUuidUtils
    )


  }

  trait EmailMessageDataDAO_DI {
    lazy val emailMessageDataDAO: EmailMessageDataDAO = new EmailMessageDataDAO
  }

  trait EmailScheduledDAOService_DI {
    lazy val emailMessageDataDAO: EmailMessageDataDAO
    lazy val dbUtils: DBUtils
    lazy val emailScheduledDAO: EmailScheduledDAO
    lazy val campaignProspectStepScheduleLogsDAO: CampaignProspectStepScheduleLogsDAO
    lazy val srRollingUpdateCoreService: SrRollingUpdateCoreService


    lazy val emailScheduledDAOService: EmailScheduledDAOService = new EmailScheduledDAOService(
      emailMessageDataDAO = emailMessageDataDAO,
      dbUtils = dbUtils,
      emailScheduledDAO = emailScheduledDAO,
      campaignProspectStepScheduleLogsDAO = campaignProspectStepScheduleLogsDAO,
      srRollingUpdateCoreService = srRollingUpdateCoreService
    )
  }

  trait EmailSchedulerJedisService_DI {
    lazy val srRedisSimpleLockServiceV2: SrRedisSimpleLockServiceV2
    lazy val emailSchedulerJedisService: EmailSchedulerJedisService = new EmailSchedulerJedisService(
      srRedisSimpleLockServiceV2 = srRedisSimpleLockServiceV2
    )
  }

  trait SrRedisHashSetBasedLockServiceV2_DI {
    lazy val srRedisSimpleLockServiceV2: SrRedisSimpleLockServiceV2
    lazy val srRedisHashSetServiceV2: SrRedisHashSetServiceV2
    lazy val srRedisHashSetBasedLockServiceV2: SrRedisHashSetBasedLockServiceV2 = new SrRedisHashSetBasedLockServiceV2(
      srRedisSimpleLockServiceV2 = srRedisSimpleLockServiceV2,
      srRedisHashSetServiceV2 = srRedisHashSetServiceV2
    )
  }

  trait SrRedisSimpleLockServiceV2_DI {
    lazy val cacheServiceJedis: CacheServiceJedis
    lazy val srRedisSimpleLockServiceV2: SrRedisSimpleLockServiceV2 = new SrRedisSimpleLockServiceV2(
      cacheServiceJedis = cacheServiceJedis
    )
  }

  trait SrRedisHashSetServiceV2_DI {
    lazy val cacheServiceJedis: CacheServiceJedis

    lazy val srRedisHashSetServiceV2: SrRedisHashSetServiceV2 = new SrRedisHashSetServiceV2(
      cacheServiceJedis = cacheServiceJedis
    )
  }

  trait GeneralChannelSchedularDI {
    lazy val generalSettingDAO: GeneralSettingDAO
    lazy val taskDAO: TaskPgDAO
    //    lazy val taskService: TaskService
    lazy val mqCampaignSchedulingMetadataMigration: MqCampaignSchedulingMetadataMigration

    lazy val generalChannelScheduler: GeneralChannelScheduler = new GeneralChannelScheduler(
      generalSettingDAO = generalSettingDAO,
      taskDAO = taskDAO,
      mqCampaignSchedulingMetadataMigration = mqCampaignSchedulingMetadataMigration
      //      taskService = taskService
    )
  }

  trait LinkedinChannelSchedulerDI {
    lazy val linkedinSettingDAO: LinkedinSettingDAO
    lazy val taskDAO: TaskPgDAO
    //    lazy val taskService: TaskService
    lazy val mqCampaignSchedulingMetadataMigration: MqCampaignSchedulingMetadataMigration

    lazy val linkedinChannelScheduler: LinkedinChannelScheduler = new LinkedinChannelScheduler(
      linkedinSettingDAO = linkedinSettingDAO,
      taskDAO = taskDAO,
      mqCampaignSchedulingMetadataMigration = mqCampaignSchedulingMetadataMigration

      //      taskService = taskService
    )
  }
}

trait IndependentStepSchedulerService_DI {

  lazy val campaignProspectDAO: CampaignProspectDAO
  lazy val campaignProspectService: CampaignProspectService
  lazy val campaignService: CampaignService
  lazy val campaignDAO: CampaignDAO
  lazy val campaignStepVariantDAO: CampaignStepVariantDAO
  lazy val accountService: AccountService

  lazy val independentStepSchedulerService: IndependentStepSchedulerService = new IndependentStepSchedulerService(
    campaignProspectDAO = campaignProspectDAO,
    campaignProspectService = campaignProspectService,
    campaignService = campaignService,
    campaignDAO = campaignDAO,
    campaignStepVariantDAO = campaignStepVariantDAO,
    accountService = accountService
  )

}


trait IndependentStepSchedulerCron_DI {
  lazy val campaignService: CampaignService
  lazy val mqIndependentStepScheduler: MQIndependentStepScheduler
  lazy val independentStepSchedulerCron: IndependentStepSchedulerCron = new IndependentStepSchedulerCron(
    campaignService = campaignService,
    mqIndependentStepScheduler = mqIndependentStepScheduler
  )
}


trait MqIndependentStepScheduler_DI {
  lazy val campaignService: CampaignService
  lazy val independentStepSchedulerService: IndependentStepSchedulerService

  lazy val mqIndependentStepScheduler: MQIndependentStepScheduler = new MQIndependentStepScheduler(
    campaignService = campaignService,
    independentStepScheduler = independentStepSchedulerService
  )
}

