/* 18-jun-2025: was slowing down compilation.

package utils.dependencyinjectionutils

import api.accounts.{AccountDAO, AccountService}
import api.accounts.service.AccountOrgBillingRelatedService
import api.calendar_app.CalendarAppService
import api.campaigns.{CampaignDAO, CampaignEditedPreviewEmailDAO, CampaignProspectDAO, CampaignStepDAO, CampaignStepVariantDAO}
import api.campaigns.services.{CampaignProspectService, CampaignService, CampaignStartService, CampaignStepService, CampaignsMissingMergeTagService}
import api.emails.CampaignProspectStepScheduleLogsDAO
import api.emails.services.SelectAndPublishForDeletionService
import api.prospects.{InboxV3Service, ProspectService}
import api.tasks.pgDao.TaskPgDAO
import api.tasks.services.TaskService
import utils.cache_utils.service.SrRedisSimpleLockServiceV2
import utils.email.EmailServiceCompanion
import utils.email_notification.service.EmailNotificationService
import utils.mq.channel_scheduler.channels.LinkedinChannelScheduler
import utils.mq.webhook.MQWebhookCompleted
import utils.multichannel.MultichannelTestUtil
import utils.shuffle.SrShuffleUtils
import utils.templating.TemplateService
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService


trait MultichannelTestUtilDI {
  lazy val campaignService: CampaignService
  lazy val prospectService: ProspectService
  lazy val campaignStepService: CampaignStepService
  lazy val linkedinChannelScheduler: LinkedinChannelScheduler
  lazy val campaignStartService: CampaignStartService
  lazy val accountService: AccountService
  lazy val accountDAO: AccountDAO
  lazy val emailNotificationService: EmailNotificationService
  lazy val campaignProspectDAO: CampaignProspectDAO
  lazy val campaignProspectService: CampaignProspectService
  lazy val campaignStepVariantDAO: CampaignStepVariantDAO
  lazy val campaignStepDAO: CampaignStepDAO
  lazy val srShuffleUtils: SrShuffleUtils
  lazy val emailServiceCompanion: EmailServiceCompanion
  lazy val templateService: TemplateService
  lazy val taskDAO: TaskPgDAO
  lazy val campaignDAO: CampaignDAO
  lazy val taskService: TaskService
  lazy val campaignEditedPreviewEmailDAO: CampaignEditedPreviewEmailDAO
  lazy val campaignsMissingMergeTagService: CampaignsMissingMergeTagService
  lazy val srRedisSimpleLockServiceV2: SrRedisSimpleLockServiceV2
  lazy val mqWebhookCompleted: MQWebhookCompleted
  lazy val accountOrgBillingRelatedService: AccountOrgBillingRelatedService
  lazy val calendarAppService: CalendarAppService
  lazy val selectAndPublishForDeletionService: SelectAndPublishForDeletionService
  lazy val srRollingUpdateCoreService: SrRollingUpdateCoreService
  lazy val inboxV3Service: InboxV3Service
  lazy val campaignProspectStepScheduleLogsDAO: CampaignProspectStepScheduleLogsDAO


  lazy val multichannelTestUtil: MultichannelTestUtil = new MultichannelTestUtil(
    srRollingUpdateCoreService = srRollingUpdateCoreService,
    campaignService = campaignService,
    prospectService = prospectService,
    campaignStepService = campaignStepService,
    linkedinChannelScheduler = linkedinChannelScheduler,
    campaignStartService = campaignStartService,
    accountService = accountService,
    accountDAO = accountDAO,
    emailNotificationService = emailNotificationService,
    campaignProspectDAO = campaignProspectDAO,
    campaignProspectService = campaignProspectService,
    campaignStepVariantDAO = campaignStepVariantDAO,
    campaignStepDAO = campaignStepDAO,
    srShuffleUtils = srShuffleUtils,
    emailServiceCompanion = emailServiceCompanion,
    templateService = templateService,
    taskDAO = taskDAO,
    campaignDAO = campaignDAO,
    taskService = taskService,
    campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO,
    campaignsMissingMergeTagService = campaignsMissingMergeTagService,
    srRedisSimpleLockServiceV2 = srRedisSimpleLockServiceV2,
    mqWebhookCompleted = mqWebhookCompleted,
    accountOrgBillingRelatedService = accountOrgBillingRelatedService,
    calendarAppService = calendarAppService,
    selectAndPublishForDeletionService = selectAndPublishForDeletionService,
    inboxV3Service = inboxV3Service,
    campaignProspectStepScheduleLogsDAO = campaignProspectStepScheduleLogsDAO
  )
}*/