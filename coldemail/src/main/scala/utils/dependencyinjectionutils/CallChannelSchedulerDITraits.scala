package utils.dependencyinjectionutils

import api.accounts.AccountService
import api.accounts.service.AccountOrgBillingRelatedService
import api.calendar_app.CalendarAppService
import api.call.DAO.CallDAO
import api.campaigns.{CampaignEditedPreviewEmailDAO, CampaignProspectDAO, CampaignStepDAO, CampaignStepVariantDAO}
import api.campaigns.services.{CampaignProspectService, CampaignService, CampaignsMissingMergeTagService}
import api.tasks.pgDao.TaskPgDAO
import api.tasks.services.TaskService
import utils.cache_utils.service.SrRedisSimpleLockServiceV2
import utils.email.EmailServiceCompanion
import utils.email_notification.service.EmailNotificationService
import utils.mq.channel_scheduler.{MqCallChannelScheduler, MqCampaignSchedulingMetadataMigration}
import utils.mq.channel_scheduler.channels.CallChannelScheduler
import utils.mq.webhook.MQWebhookCompleted
import utils.random.SrRandomUtils
import utils.shuffle.SrShuffleUtils
import utils.templating.TemplateService
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService

trait CallChannelScheduler_DI {
  lazy val callDAO: CallDAO
  lazy val taskService: TaskService
  lazy val srRandomUtils: SrRandomUtils
  lazy val taskDAO: TaskPgDAO
  lazy val mqCampaignSchedulingMetadataMigration: MqCampaignSchedulingMetadataMigration


  lazy val callChannelScheduler: CallChannelScheduler = new CallChannelScheduler(
    callDAO = callDAO,
    taskService = taskService,
    srRandomUtils = srRandomUtils,
    taskPgDAO = taskDAO,
    mqCampaignSchedulingMetadataMigration = mqCampaignSchedulingMetadataMigration
  )

}

trait MqCallChannelScheduler_DI {
  lazy val callChannelScheduler: CallChannelScheduler
  lazy val accountService: AccountService
  lazy val emailNotificationService: EmailNotificationService
  lazy val campaignService: CampaignService
  lazy val campaignProspectDAO: CampaignProspectDAO
  lazy val campaignStepVariantDAO: CampaignStepVariantDAO
  lazy val campaignStepDAO: CampaignStepDAO
  lazy val emailServiceCompanion: EmailServiceCompanion
  lazy val templateService: TemplateService
  lazy val srShuffleUtils: SrShuffleUtils
  lazy val taskService: TaskService
  lazy val taskDAO: TaskPgDAO
  lazy val campaignProspectService: CampaignProspectService
  lazy val campaignEditedPreviewEmailDAO: CampaignEditedPreviewEmailDAO
  lazy val mqWebhookCompleted: MQWebhookCompleted
  lazy val srRedisSimpleLockServiceV2: SrRedisSimpleLockServiceV2
  lazy val campaignsMissingMergeTagService: CampaignsMissingMergeTagService
  lazy val accountOrgBillingRelatedService: AccountOrgBillingRelatedService
  lazy val calendarAppService: CalendarAppService
  lazy val srRollingUpdateCoreService: SrRollingUpdateCoreService


  lazy val mqCallChannelScheduler: MqCallChannelScheduler = new MqCallChannelScheduler(
    srRollingUpdateCoreService = srRollingUpdateCoreService,
    callChannelScheduler = callChannelScheduler,
    accountService = accountService,
    emailNotificationService = emailNotificationService,
    campaignService = campaignService,
    campaignProspectDAO = campaignProspectDAO,
    campaignStepVariantDAO = campaignStepVariantDAO,
    campaignStepDAO = campaignStepDAO,
    emailServiceCompanion = emailServiceCompanion,
    templateService = templateService,
    srShuffleUtils = srShuffleUtils,
    taskDAO = taskDAO,
    taskService = taskService,
    campaignProspectService = campaignProspectService,
    campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO,
    mqWebhookCompleted = mqWebhookCompleted,
    srRedisSimpleLockServiceV2 = srRedisSimpleLockServiceV2,
    campaignsMissingMergeTagService = campaignsMissingMergeTagService,
    accountOrgBillingRelatedService = accountOrgBillingRelatedService,
    calendarAppService = calendarAppService
  )

}
