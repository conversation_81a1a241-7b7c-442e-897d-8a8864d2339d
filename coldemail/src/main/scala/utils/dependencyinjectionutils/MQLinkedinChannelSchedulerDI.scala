package utils.dependencyinjectionutils

import api.accounts.AccountService
import api.accounts.service.AccountOrgBillingRelatedService
import api.calendar_app.CalendarAppService
import api.campaigns.{CampaignEditedPreviewEmailDAO, CampaignProspectDAO, CampaignStepDAO, CampaignStepVariantDAO}
import api.campaigns.services.{CampaignProspectService, CampaignService, CampaignsMissingMergeTagService}
import api.tasks.pgDao.TaskPgDAO
import api.tasks.services.TaskService
import utils.cache_utils.service.SrRedisSimpleLockServiceV2
import utils.email.EmailServiceCompanion
import utils.email_notification.service.EmailNotificationService
import utils.mq.channel_scheduler.MqLinkedinChannelScheduler
import utils.mq.channel_scheduler.channels.LinkedinChannelScheduler
import utils.mq.webhook.MQWebhookCompleted
import utils.shuffle.SrShuffleUtils
import utils.templating.TemplateService
import utils_deploy.rolling_updates.services.SrRollingUpdateCoreService

trait MQLinkedinChannelSchedulerDI {
  lazy val linkedinChannelScheduler: LinkedinChannelScheduler
  lazy val accountService: AccountService
  //val accountDAO: AccountDAO
  lazy val emailNotificationService: EmailNotificationService
  lazy val campaignService: CampaignService
  lazy val campaignProspectDAO: CampaignProspectDAO
  lazy val campaignStepVariantDAO: CampaignStepVariantDAO
  lazy val campaignStepDAO: CampaignStepDAO
  lazy val emailServiceCompanion: EmailServiceCompanion
  lazy val templateService: TemplateService
  lazy val srShuffleUtils: SrShuffleUtils
  lazy val taskService: TaskService
  lazy val taskDAO: TaskPgDAO
  lazy val campaignProspectService: CampaignProspectService
  lazy val campaignEditedPreviewEmailDAO: CampaignEditedPreviewEmailDAO
  lazy val campaignsMissingMergeTagService: CampaignsMissingMergeTagService
  lazy val srRedisSimpleLockServiceV2: SrRedisSimpleLockServiceV2
  lazy val mqWebhookCompleted: MQWebhookCompleted
  lazy val accountOrgBillingRelatedService: AccountOrgBillingRelatedService
  lazy val calendarAppService: CalendarAppService
  lazy val srRollingUpdateCoreService: SrRollingUpdateCoreService


  lazy val mqLinkedinChannelScheduler: MqLinkedinChannelScheduler = new MqLinkedinChannelScheduler(
    srRollingUpdateCoreService = srRollingUpdateCoreService,
    linkedinChannelScheduler = linkedinChannelScheduler,
    accountService = accountService,
    //accountDAO = accountDAO,
    emailNotificationService = emailNotificationService,
    campaignService = campaignService,
    campaignProspectDAO = campaignProspectDAO,
    campaignStepVariantDAO = campaignStepVariantDAO,
    campaignStepDAO = campaignStepDAO,
    emailServiceCompanion = emailServiceCompanion,
    templateService = templateService,
    srShuffleUtils = srShuffleUtils,
    taskDAO = taskDAO,
    taskService = taskService,
    campaignProspectService = campaignProspectService,
    campaignEditedPreviewEmailDAO = campaignEditedPreviewEmailDAO,
    campaignsMissingMergeTagService = campaignsMissingMergeTagService,
    srRedisSimpleLockServiceV2 = srRedisSimpleLockServiceV2,
    mqWebhookCompleted = mqWebhookCompleted,
    accountOrgBillingRelatedService = accountOrgBillingRelatedService,
    calendarAppService = calendarAppService

  )
}