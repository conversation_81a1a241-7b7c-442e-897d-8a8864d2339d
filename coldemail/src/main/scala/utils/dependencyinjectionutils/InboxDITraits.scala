package utils.dependencyinjectionutils

import api.accounts.service.AccountOrgBillingRelatedService
import api.accounts.{AccountService, PermissionUtils}
import api.campaigns.CampaignProspectDAO
import api.campaigns.dao.CampaignEmailSettingsDAO
import api.campaigns.services.CampaignService
import api.emails.{EmailCommonService, EmailReplyTrackingModelV2, EmailScheduledDAO, EmailSettingDAO, EmailThreadDAO}
import api.emails.dao_service.{EmailScheduledDAOService, EmailThreadDAOService}
import api.emails.models.EmailMessageContactModel
import api.linkedin_message_threads.LinkedinMessageThreadsDAO
import api.linkedin_messages.dao_service.LinkedinMessagesDAOService
import api.prospects.{Inbox, InboxController, InboxV3Service, ProspectAccountDAO1}
import api.prospects.dao_service.{InboxV3DAOService, ProspectDAOService}
import api.prospects.service.{AssociateProspectToEmailThreadService, InboxSendService, ProspectServiceV2}
import api.rep_tracking_hosts.service.RepTrackingHostService
import api.tasks.MqUpdateManualEmailTaskStatus
import api.tasks.pgDao.TaskPgDAO
import api.tasks.services.TaskService
import api.team.service.TeamService
import api.team_inbox.dao_service.ReplySentimentDAOService
import api.team_inbox.service.{InboxV3PaginationService, TeamInboxService}
import org.apache.pekko.actor.ActorSystem
import play.api.libs.ws.WSClient
import play.api.mvc.ControllerComponents
import utils.customersupport.BatchCreateTeamInboxes
import utils.dbutils.DBUtils
import utils.email.{EmailSenderService, EmailService}
import utils.mq.email.MQAssociateProspectToOldEmails
import utils.templating.TemplateService
import utils.uuid.services.SrUuidService
import utils.mq.webhook.mq_activity_trigger.MQActivityTriggerPublisher


trait InboxV3DAOService_DI {
  lazy val emailThreadDAO: EmailThreadDAO

  lazy val inboxV3DAOService: InboxV3DAOService = new InboxV3DAOService(
    emailThreadDAO = emailThreadDAO
  )
}

trait InboxV3ServiceDI {
  lazy val emailThreadDAOService: EmailThreadDAOService
  lazy val campaignService: CampaignService
  lazy val emailSettingDAO: EmailSettingDAO
  lazy val prospectServiceV2: ProspectServiceV2
  //  lazy val accountService: AccountService
  lazy val emailScheduledDAO: EmailScheduledDAO
  lazy val emailReplyTrackingModelV2: EmailReplyTrackingModelV2
  lazy val templateService: TemplateService
  lazy val emailService: EmailService
  lazy val prospectDAOService: ProspectDAOService
  lazy val prospectAccountDAO: ProspectAccountDAO1
  lazy val replySentimentDAOService: ReplySentimentDAOService
  lazy val dbUtils: DBUtils
  lazy val emailMessageContactModel: EmailMessageContactModel
  lazy val campaignProspectDAO: CampaignProspectDAO
  lazy val teamService: TeamService
  lazy val srUuidService: SrUuidService
  lazy val linkedinMessageThreadsDAO: LinkedinMessageThreadsDAO
  lazy val inboxV3DAOService: InboxV3DAOService
  lazy val repTrackingHostService: RepTrackingHostService
  lazy val linkedinMessagesDAOService: LinkedinMessagesDAOService
  lazy val teamInboxService: TeamInboxService
  lazy val emailScheduledDAOService: EmailScheduledDAOService
  lazy val mqActivityTriggerPublisher: MQActivityTriggerPublisher
  lazy val accountOrgBillingRelatedService: AccountOrgBillingRelatedService
  lazy val emailCommonService: EmailCommonService

  lazy val inboxV3Service: InboxV3Service = new InboxV3Service(
    emailSettingDAO = emailSettingDAO,
    emailThreadDAOService = emailThreadDAOService,
    linkedinMessageThreadsDAO = linkedinMessageThreadsDAO,
    campaignService = campaignService,
    linkedinMessagesDAOService = linkedinMessagesDAOService,
    prospectServiceV2 = prospectServiceV2,
    //    accountService = accountService,
    srUuidService = srUuidService,
    emailScheduledDAO = emailScheduledDAO,
    emailScheduledDAOService = emailScheduledDAOService,
    emailReplyTrackingModelV2 = emailReplyTrackingModelV2,
    templateService = templateService,
    emailService = emailService,
    prospectDAOService = prospectDAOService,
    prospectAccountDAO = prospectAccountDAO,
    dbUtils = dbUtils,
    emailMessageContactModel = emailMessageContactModel,
    replySentimentDAOService = replySentimentDAOService,
    campaignProspectDAO = campaignProspectDAO,
    teamService = teamService,
    inboxV3DAOService = inboxV3DAOService,
    repTrackingHostService = repTrackingHostService,
    teamInboxService = teamInboxService,
    mqActivityTriggerPublisher = mqActivityTriggerPublisher,
    accountOrgBillingRelatedService = accountOrgBillingRelatedService,
    emailCommonService = emailCommonService

  )
}

trait InboxSendServiceDI {
  lazy val emailScheduledDAO: EmailScheduledDAO
  lazy val taskService: TaskService
  lazy val emailSettingDAO: EmailSettingDAO
  lazy val emailSenderService: EmailSenderService
  lazy val emailService: EmailService
  lazy val repTrackingHostService: RepTrackingHostService
  lazy val campaignProspectDAO: CampaignProspectDAO
  lazy val campaignEmailSettingsDAO: CampaignEmailSettingsDAO

  lazy val inboxSendService: InboxSendService = new InboxSendService(
    emailScheduledDAO = emailScheduledDAO,
    taskService = taskService,
    emailSettingDAO = emailSettingDAO,
    emailService = emailService,
    repTrackingHostService = repTrackingHostService,
    emailSenderService = emailSenderService,
    campaignProspectDAO = campaignProspectDAO,
    campaignEmailSettingsDAO = campaignEmailSettingsDAO
  )
}

trait InboxV3PaginationService_DI {
  lazy val srUuidService: SrUuidService
  lazy val emailThreadDAOService: EmailThreadDAOService
  lazy val linkedinMessageThreadsDAO: LinkedinMessageThreadsDAO
  lazy val replySentimentDAOService: ReplySentimentDAOService
  lazy val teamService: TeamService

  lazy val inboxV3PaginationService: InboxV3PaginationService = new InboxV3PaginationService(
    srUuidService = srUuidService,
    emailThreadDAOService = emailThreadDAOService,
    linkedinMessageThreadsDAO = linkedinMessageThreadsDAO,
    replySentimentDAOService = replySentimentDAOService,
    teamService = teamService
  )

}

trait InboxControllerDI {
  lazy val campaignService: CampaignService
  lazy val prospectDAOService: ProspectDAOService
  lazy val inbox: Inbox
  lazy val emailScheduledDAO: EmailScheduledDAO
  lazy val wSClient: WSClient
  lazy val emailService: EmailService
  lazy val emailSenderService: EmailSenderService
  lazy val srUuidService: SrUuidService
  lazy val emailReplyTrackingModelV2: EmailReplyTrackingModelV2
  lazy val prospectAccountDAO: ProspectAccountDAO1
  lazy val emailThreadDAOService: EmailThreadDAOService
  lazy val emailSettingDAO: EmailSettingDAO
  lazy val templateService: TemplateService
  lazy val inboxV3Service: InboxV3Service
  lazy val accountService: AccountService
  lazy val permissionUtils: PermissionUtils
  lazy val controllerComponents: ControllerComponents
  lazy val prospectServiceV2: ProspectServiceV2
  lazy val emailMessageContactModel: EmailMessageContactModel
  lazy val repTrackingHostService: RepTrackingHostService
  lazy val linkedinMessageThreadsDAO: LinkedinMessageThreadsDAO
  lazy val teamInboxService: TeamInboxService
  lazy val inboxV3PaginationService: InboxV3PaginationService
  lazy val actorSystem: ActorSystem
  lazy val inboxSendService: InboxSendService
  lazy val mqUpdateManualEmailTaskStatus: MqUpdateManualEmailTaskStatus
  lazy val taskDAO: TaskPgDAO
  lazy val emailScheduledDAOService: EmailScheduledDAOService


  lazy val inboxController: InboxController = new InboxController(
    controllerComponents = controllerComponents,
    accountService = accountService,
    srUuidService = srUuidService,
    inbox = inbox,
    permissionUtils = permissionUtils,
    campaignService = campaignService,
    prospectDAOService = prospectDAOService,
    prospectAccountDAO = prospectAccountDAO,
    wSClient = wSClient,
    emailThreadDAOService = emailThreadDAOService,
    linkedinMessageThreadsDAO = linkedinMessageThreadsDAO,
    emailScheduledDAO = emailScheduledDAO,
    emailScheduledDAOService = emailScheduledDAOService,
    templateService = templateService,
    emailService = emailService,
    emailSenderService = emailSenderService,
    emailReplyTrackingModelV2 = emailReplyTrackingModelV2,
    emailSettingDAO = emailSettingDAO,
    inboxV3Service = inboxV3Service,
    prospectServiceV2 = prospectServiceV2,
    emailMessageContactModel = emailMessageContactModel,
    repTrackingHostService = repTrackingHostService,
    teamInboxService = teamInboxService,
    inboxV3PaginationService = inboxV3PaginationService,
    inboxSendService = inboxSendService,
    system = actorSystem,
    mqUpdateManualEmailTaskStatus = mqUpdateManualEmailTaskStatus,
    taskPgDAO = taskDAO
  )
}

trait BatchCreateTeamInboxes_DI {
  lazy val teamInboxService: TeamInboxService
  lazy val batchCreateTeamInboxes: BatchCreateTeamInboxes = new BatchCreateTeamInboxes(
    teamInboxService = teamInboxService,
  )
}

trait AssociateProspectToEmailThreadService_DI {
  lazy val emailThreadDAO: EmailThreadDAO
  lazy val dbUtils: DBUtils
  lazy val inboxV3DAOService: InboxV3DAOService
  lazy val emailMessageContactModel: EmailMessageContactModel
  lazy val emailScheduledDAO: EmailScheduledDAO
  lazy val campaignProspectDAO: CampaignProspectDAO
  lazy val prospectServiceV2: ProspectServiceV2
  lazy val associateProspectToEmailThreadService: AssociateProspectToEmailThreadService = new AssociateProspectToEmailThreadService(
    emailThreadDAO = emailThreadDAO,
    dbUtils = dbUtils,
    inboxV3DAOService = inboxV3DAOService,
    emailMessageContactModel = emailMessageContactModel,
    emailScheduledDAO = emailScheduledDAO,
    campaignProspectDAO = campaignProspectDAO,
    prospectServiceV2 = prospectServiceV2
  )
}

trait MQAssociateProspectToOldEmails_DI {

  lazy val associateProspectToEmailThreadService: AssociateProspectToEmailThreadService

  lazy val mqAssociateProspectToOldEmails: MQAssociateProspectToOldEmails = new MQAssociateProspectToOldEmails(
    associateProspectToEmailThreadService = associateProspectToEmailThreadService
  )
}




trait Inbox_DI {

  lazy val prospectDAOService: ProspectDAOService

  lazy val inbox: Inbox = new Inbox(
    prospectDAOService = prospectDAOService
  )

}

