package utils.dependencyinjectionutils

import api.CacheServiceJedis
import utils.uuid.SrUuidUtils
import utils.uuid.services.{SrUuidInternalDataService, SrUuidService}

trait SrUuidInternalDataServiceDI {

  lazy val cacheServiceJedis: CacheServiceJedis

  lazy val srUuidInternalDataService: SrUuidInternalDataService = new SrUuidInternalDataService(
    cacheServiceJedis = cacheServiceJedis
  )
}

trait SrUuidServiceDI {
  lazy val srUuidInternalDataService: SrUuidInternalDataService

  lazy val srUuidService: SrUuidService = new SrUuidService(
    uuidService = srUuidInternalDataService
  )
}


trait SrUuidUtilsDI {
  lazy val srUuidUtils: SrUuidUtils = new SrUuidUtils
}