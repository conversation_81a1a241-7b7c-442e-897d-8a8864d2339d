package utils.dependencyinjectionutils

import api.accounts.dao.TeamsDAO
import api.accounts.{AccountService, GoogleOAuth, MicrosoftOAuth, PermissionU<PERSON>s, SocialAuthController, SocialAuthService}
import api.accounts.service.ResetUserCacheUtil
import api.campaigns.services.CalendlyWebhookService
import api.emails.EmailSettingDAO
import api.emails.services.{EmailAccountService, EmailAccountTestService}
import api.free_email_domain.service.FreeEmailDomainListService
import api.integrations.{CalendlyOA<PERSON>, HubSpotOAuth, PipedriveOAuth, SalesforceOAuth, ZohoOAuth, ZohoRecruitOAuth}
import api.integrations.services.TIntegrationCRMService
import api.triggers.WorkflowCrmSettingsService
import api.triggers.dao.WorkflowCrmSettingsDAO
import play.api.libs.ws.WSClient
import play.api.mvc.ControllerComponents
import utils.email.GmailApiService

trait SocialAuthControllerDI {

  implicit lazy val wSClient: WSClient

  lazy val gmailApiService: GmailApiService

  lazy val salesforceOAuth: SalesforceOAuth
  lazy val zohoOAuth: ZohoOAuth
  lazy val zohoRecruitOAuth: ZohoRecruitOAuth
  lazy val pipedriveOAuth: PipedriveOAuth
  lazy val hubspotOAuth: HubSpotOAuth
  lazy val accountService: AccountService
  lazy val googleOAuth: GoogleOAuth
  lazy val microsoftOAuth: MicrosoftOAuth
  lazy val emailSettingDAO: EmailSettingDAO
  lazy val workflowCrmSettingsDAO: WorkflowCrmSettingsDAO
  //  lazy val authUtils: AuthUtils
  lazy val permissionUtils: PermissionUtils
  lazy val controllerComponents: ControllerComponents
  lazy val resetUserCacheUtil: ResetUserCacheUtil
  lazy val emailAccountService: EmailAccountService
  lazy val workflowCrmSettingsService: WorkflowCrmSettingsService
  lazy val freeEmailDomainListService: FreeEmailDomainListService
  lazy val tIntegrationCRMService: TIntegrationCRMService
  lazy val socialAuthService: SocialAuthService

  lazy val socialAuthController: SocialAuthController = new SocialAuthController(
    controllerComponents = controllerComponents,
    //    authUtils = authUtils,
    resetUserCacheUtil = resetUserCacheUtil,
    permissionUtils = permissionUtils,
    salesforceOAuth = salesforceOAuth,
    zohoOAuth = zohoOAuth,
    zohoRecruitOAuth = zohoRecruitOAuth,
    pipedriveOAuth = pipedriveOAuth,
    hubspotOAuth = hubspotOAuth,
    googleOAuth = googleOAuth,
    microsoftOAuth = microsoftOAuth,
    gmailApiService = gmailApiService,
    emailSettingDAO = emailSettingDAO,
    accountService = accountService,
    workflowCrmSettingsService = workflowCrmSettingsService,
    workflowCrmSettingsDAO = workflowCrmSettingsDAO,
    emailAccountService = emailAccountService,
    freeEmailDomainListService = freeEmailDomainListService,
    tIntegrationCRMService = tIntegrationCRMService,
    ws = wSClient,
    socialAuthService = socialAuthService
  )
}

trait SocialAuthServiceDI {

  lazy val salesforceOAuth: SalesforceOAuth
  lazy val zohoOAuth: ZohoOAuth
  lazy val zohoRecruitOAuth: ZohoRecruitOAuth
  lazy val pipedriveOAuth: PipedriveOAuth
  lazy val hubspotOAuth: HubSpotOAuth
  lazy val googleOAuth: GoogleOAuth
  lazy val microsoftOAuth: MicrosoftOAuth
  lazy val emailSettingDAO: EmailSettingDAO
  lazy val workflowCrmSettingsDAO: WorkflowCrmSettingsDAO
  lazy val freeEmailDomainListService: FreeEmailDomainListService
  lazy val emailAccountTestService: EmailAccountTestService
  lazy val tIntegrationCRMService: TIntegrationCRMService
  lazy val workflowCrmSettingsService: WorkflowCrmSettingsService
  lazy val resetUserCacheUtil: ResetUserCacheUtil
  lazy val gmailApiService: GmailApiService
  lazy val accountService: AccountService
  lazy val emailAccountService: EmailAccountService
  lazy val calendlyOAuth: CalendlyOAuth
  lazy val teamDAO: TeamsDAO
    lazy val calendlyWebhookService: CalendlyWebhookService

  lazy val socialAuthService: SocialAuthService = new SocialAuthService(
    freeEmailDomainListService = freeEmailDomainListService,
    emailSettingDAO = emailSettingDAO,
    googleOAuth = googleOAuth,
    microsoftOAuth = microsoftOAuth,
    workflowCrmSettingsDAO = workflowCrmSettingsDAO,
    hubspotOAuth = hubspotOAuth,
    zohoOAuth = zohoOAuth,
    zohoRecruitOAuth = zohoRecruitOAuth,
    pipedriveOAuth = pipedriveOAuth,
    salesforceOAuth = salesforceOAuth,
    emailAccountTestService = emailAccountTestService,
    tIntegrationCRMService = tIntegrationCRMService,
    workflowCrmSettingsService = workflowCrmSettingsService,
    resetUserCacheUtil = resetUserCacheUtil,
    gmailApiService = gmailApiService,
    accountService = accountService,
    emailAccountService = emailAccountService,
      calendlyOAuth = calendlyOAuth,
      teamDAO = teamDAO,
      calendlyWebhookService = calendlyWebhookService
  )
}


trait GoogleOAuthDI {
  lazy val emailSettingDAO: EmailSettingDAO

  lazy val googleOAuth: GoogleOAuth = new GoogleOAuth(
    emailSettingDAO = emailSettingDAO
  )
}

trait MicrosoftOAuthDI {
  lazy val emailSettingDAO: EmailSettingDAO

  lazy val microsoftOAuth: MicrosoftOAuth = new MicrosoftOAuth(
    emailSettingDAO = emailSettingDAO
  )
}

trait CalendlyOAuthDI {
    lazy val calendlyOAuth: CalendlyOAuth = new CalendlyOAuth()
}



