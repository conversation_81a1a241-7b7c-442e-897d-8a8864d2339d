package utils.dependencyinjectionutils

import api.accounts.AccountService
import api.campaigns.{CampaignDAO, CampaignProspectDAO}
import api.campaigns.services.CampaignCacheService
import api.emails.{EmailHandleErrorService, EmailReplyTrackingModelV2, EmailScheduledDAO, EmailSettingDAO, EmailThreadDAO}
import api.emails.dao_service.EmailThreadDAOService
import api.emails.models.EmailMessageContactModel
import api.emails.services.EmailSettingService
import api.linkedin_message_threads.LinkedinMessageThreadsDAO
import api.prospects.dao_service.ProspectDAOService
import api.prospects.service.ProspectServiceV2
import utils.email.{EmailBodyService, EmailService, GmailApiService, MailgunService, OutlookApiService, SendGridService, SmtpImapService}
import utils.mq.trackingapp.OpenTracker.MQOpenTrackerPublisher
import utils.mq.webhook.{MQWebhookCompleted, MQWebhookEmailInvalid}
import utils.mq.webhook.mq_activity_trigger.MQActivityTriggerPublisher
import utils.templating.TemplateService
import utils.uuid.SrUuidUtils


trait EmailServiceDI {

  lazy val emailBodyService: EmailBodyService
  lazy val prospectDAOService: ProspectDAOService
  lazy val emailScheduledDAO: EmailScheduledDAO
  lazy val mqActivityTriggerPublisher: MQActivityTriggerPublisher
  lazy val mqOpenTrackerPublisher: MQOpenTrackerPublisher
  lazy val mqWebhookEmailInvalid: MQWebhookEmailInvalid
  lazy val mqWebhookCompleted: MQWebhookCompleted
  lazy val emailReplyTrackingModelV2: EmailReplyTrackingModelV2
  lazy val accountService: AccountService
  lazy val emailHandleErrorService: EmailHandleErrorService
  lazy val outlookApiService: OutlookApiService
  lazy val gmailApiService: GmailApiService
  //  lazy val gmailService: GmailService
  lazy val mailgunService: MailgunService
  lazy val sendGridService: SendGridService
  lazy val smtpImapService: SmtpImapService
  lazy val emailSettingDAO: EmailSettingDAO
  //val accountDAO: AccountDAO
  lazy val campaignDAO: CampaignDAO
  lazy val templateService: TemplateService
  lazy val prospectServiceV2: ProspectServiceV2
  lazy val emailThreadDAO: EmailThreadDAO
  lazy val linkedinMessageThreadsDAO: LinkedinMessageThreadsDAO
  lazy val campaignProspectDAO: CampaignProspectDAO
  lazy val emailMessageContactModel: EmailMessageContactModel
  //  lazy val cacheEventScyllaService: CacheEventScyllaService
  lazy val campaignCacheService: CampaignCacheService
  lazy val emailThreadDAOService: EmailThreadDAOService
  lazy val emailSettingService: EmailSettingService
  lazy val srUuidUtils: SrUuidUtils

  lazy val emailService: EmailService = new EmailService(
    prospectDAOService = prospectDAOService,
    //accountDAO = accountDAO,
    emailSettingDAO = emailSettingDAO,
    emailScheduledDAO = emailScheduledDAO,
    emailThreadDAO = emailThreadDAO,
    linkedinMessageThreadsDAO = linkedinMessageThreadsDAO,
    srUuidUtils = srUuidUtils,
    campaignProspectDAO = campaignProspectDAO,
    campaignDAO = campaignDAO,
    emailReplyTrackingModelV2 = emailReplyTrackingModelV2,
    accountService = accountService,
    emailHandleErrorService = emailHandleErrorService,
    outlookApiService = outlookApiService,
    gmailApiService = gmailApiService,
    //    gmailService = gmailService,
    mailgunService = mailgunService,
    sendGridService = sendGridService,
    smtpImapService = smtpImapService,
    templateService = templateService,
    mqActivityTriggerPublisher = mqActivityTriggerPublisher,
    mqOpenTrackerPublisher = mqOpenTrackerPublisher,
    mqWebhookEmailInvalid = mqWebhookEmailInvalid,
    mqWebhookCompleted = mqWebhookCompleted,
    prospectServiceV2 = prospectServiceV2,
    emailMessageContactModel = emailMessageContactModel,
    //    cacheEventScyllaService = cacheEventScyllaService,
    campaignCacheService = campaignCacheService,
    emailThreadDAOService = emailThreadDAOService,
    emailSettingService = emailSettingService,
    emailBodyService = emailBodyService,

  )
}