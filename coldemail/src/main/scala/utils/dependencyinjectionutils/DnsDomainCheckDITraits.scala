package utils.dependencyinjectionutils

import api.campaigns.dao.{DomainChecksDAO, InboxPlacementCheckDAO}
import api.domain_health.{DomainHealthCheckDAO, DomainHealthCheckService}
import api.emails.daos.DomainPublicDNSDAO
import api.free_email_domain.service.UserCheckApiService
import api.spammonitor.dao.DomainInfoWhoisDAO
import api.tracking_host.dao.CustomTrackingDomainDAO
import api.tracking_host.services.CustomTrackingDomainService
import api.tracking_host.{MqCnameUptimeCheck, MqInactiveDomainCustomTrackingDeleter}
import io.smartreach.sr_dns_utils.SrDNSUtil
import utils.cloudflare.CloudflareService
import utils.cronjobs.spammonitor.{ActiveCampaignImpl, EmailDeliveryAnalysisService, EmailSpamMonitoringCron, SpamMonitorCheckService}
import utils.email_notification.service.EmailNotificationService
import utils.mq.email.MQDomainServiceProviderDNSService
import utils.uuid.SrUuidUtils

trait DomainPublicDNSDAO_DI {
  lazy val domainPublicDNSDAO: DomainPublicDNSDAO = new DomainPublicDNSDAO
}

trait SrDNSUtil_DI {
  lazy val srDNSUtil: SrDNSUtil = new SrDNSUtil
}

trait MQDomainServiceProviderDNSService_DI {
  lazy val srDNSUtil: SrDNSUtil
  lazy val domainPublicDNSDAO: DomainPublicDNSDAO
  lazy val mqDomainServiceProviderDNSService: MQDomainServiceProviderDNSService = new MQDomainServiceProviderDNSService(
    domainPublicDNSDAO = domainPublicDNSDAO,
    srDNSUtil = srDNSUtil
  )

}



trait UserCheckApiServiceDI {
  lazy val userCheckApiService: UserCheckApiService = new UserCheckApiService
}


trait DomainInfoWhoisDAO_DI {
  lazy val domainInfoWhoisDAO: DomainInfoWhoisDAO = new DomainInfoWhoisDAO
}
trait EmailSpamMonitoringCronDI {
  lazy val spamMonitorCheckService: SpamMonitorCheckService

  lazy val emailSpamMonitoringCron: EmailSpamMonitoringCron = new EmailSpamMonitoringCron(
    spamMonitor = spamMonitorCheckService
  )
}

trait InboxPlacementCheckDAO_DI {
  lazy val srUuidUtils: SrUuidUtils

  lazy val inboxPlacementCheckDAO: InboxPlacementCheckDAO = new InboxPlacementCheckDAO(
    srUuidUtils = srUuidUtils
  )

}

trait DomainChecksDAO_DI {
  lazy val domainChecksDAO: DomainChecksDAO = new DomainChecksDAO
}


trait DomainHealthCheckDAO_DI {
  lazy val domainHealthCheckDAO: DomainHealthCheckDAO = new DomainHealthCheckDAO
}


trait DomainHealthCheckService_DI {
  lazy val domainHealthCheckDAO: DomainHealthCheckDAO
  lazy val emailNotificationService: EmailNotificationService

  lazy val domainHealthCheckService: DomainHealthCheckService = new DomainHealthCheckService(
    domainHealthCheckDAO = domainHealthCheckDAO,
    emailNotificationService = emailNotificationService
  )
}



trait MqCnameUptimeCheck_DI {
  lazy val customTrackingDomainDAO: CustomTrackingDomainDAO
  lazy val customTrackingDomainService: CustomTrackingDomainService
  lazy val mqCnameUptimeCheck: MqCnameUptimeCheck = new MqCnameUptimeCheck(
    customTrackingDomainDAO = customTrackingDomainDAO,
    customTrackingDomainService = customTrackingDomainService
  )
}

trait MqInactiveDomainCustomTrackingDeleter_DI {
  lazy val customTrackingDomainDAO: CustomTrackingDomainDAO
  lazy val cloudflareService: CloudflareService
  lazy val emailNotificationService: EmailNotificationService
  lazy val mqInactiveDomainCustomTrackingDeleter: MqInactiveDomainCustomTrackingDeleter = new MqInactiveDomainCustomTrackingDeleter(
    customTrackingDomainDAO = customTrackingDomainDAO,
    cloudflareService = cloudflareService,
    emailNotificationService = emailNotificationService
  )
}




trait SpamMonitorCheckServiceDI {

  lazy val activeCampaignService: ActiveCampaignImpl
  lazy val emailDeliveryAnalysisService: EmailDeliveryAnalysisService

  lazy val spamMonitorCheckService: SpamMonitorCheckService = new SpamMonitorCheckService(
    activeCampaignService = activeCampaignService,
    emailDeliveryAnalysisService = emailDeliveryAnalysisService
  )
}
