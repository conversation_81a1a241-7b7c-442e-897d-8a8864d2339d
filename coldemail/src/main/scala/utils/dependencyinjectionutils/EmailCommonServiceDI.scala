package utils.dependencyinjectionutils
import api.emails.{EmailCommonService, EmailSettingDAO}
import api.emails.dao_service.EmailThreadDAOService

import api.emails.models.EmailMessageContactModel

trait EmailCommonServiceDI {

  lazy val emailMessageContactModel : EmailMessageContactModel
  lazy val emailThreadDAOService: EmailThreadDAOService

  lazy val emailSettingDAO: EmailSettingDAO


  lazy val emailCommonService: EmailCommonService = new EmailCommonService(
    emailMessageContactModel = emailMessageContactModel,
    emailThreadDAOService = emailThreadDAOService,
    emailSettingDAO = emailSettingDAO
  )
}
