package utils.dependencyinjectionutils

import api.accounts.PermissionUtils
import api.accounts.service.OrganizationService
import api.campaigns.services.CampaignService
import api.emails.{EmailSettingDAO, EmailThreadDAO}
import api.emails.dao_service.EmailThreadDAOService
import api.emails.models.EmailMessageContactModel
import api.linkedin_message_threads.{LinkedinMessageThreadsDAO, LinkedinMessageThreadsService}
import api.team_inbox.TeamInboxController
import api.team_inbox.dao.{EmailTeamInboxDAO, LinkedinTeamInboxDAO, ReplySentimentJedisDAO}
import api.team_inbox.dao_service.ReplySentimentDAOService
import api.team_inbox.service.{ReplySentimentService, TeamInboxService}
import play.api.libs.ws.WSClient
import play.api.mvc.ControllerComponents
import utils.dbutils.DBUtils
import utils.email.EmailService
import utils.uuid.SrUuidUtils
import utils.uuid.services.SrUuidService


trait TeamInboxDAO_DI {
  lazy val teamInboxDAO: EmailTeamInboxDAO = new EmailTeamInboxDAO
}

trait LinkedinTeamInboxDAO_DI {
  lazy val linkedinTeamInboxDAO: LinkedinTeamInboxDAO = new LinkedinTeamInboxDAO
}

trait TeamInboxService_DI {
  lazy val teamInboxDAO: EmailTeamInboxDAO
  lazy val linkedinTeamInboxDAO: LinkedinTeamInboxDAO
  lazy val emailSettingDAO: EmailSettingDAO
  lazy val emailService: EmailService
  lazy val replySentimentDAOService: ReplySentimentDAOService
  lazy val dbUtils: DBUtils
  lazy val srUuidUtils: SrUuidUtils
  lazy val linkedinMessageThreadsService: LinkedinMessageThreadsService
  lazy val replySentimentService: ReplySentimentService
  lazy val emailMessageContactModel: EmailMessageContactModel
  lazy val campaignService: CampaignService
  lazy val emailThreadDAO: EmailThreadDAO
  lazy val linkedinMessageThreadsDAO: LinkedinMessageThreadsDAO
  lazy val replySentimentJedisDAO: ReplySentimentJedisDAO
  lazy val organizationService : OrganizationService

  lazy val teamInboxService: TeamInboxService = new TeamInboxService(
    emailTeamInboxDAO = teamInboxDAO,
    linkedinTeamInboxDAO = linkedinTeamInboxDAO,
    emailSettingDAO = emailSettingDAO,
    emailService = emailService,
    linkedinMessageThreadsService = linkedinMessageThreadsService,
    replySentimentDAOService = replySentimentDAOService,
    dbUtils = dbUtils,
    srUuidUtils = srUuidUtils,
    replySentimentService = replySentimentService,
    emailMessageContactModel = emailMessageContactModel,
    campaignService = campaignService,
    emailThreadDAO = emailThreadDAO,
    linkedinMessageThreadsDAO = linkedinMessageThreadsDAO,
    replySentimentJedisDAO = replySentimentJedisDAO,
    organizationService = organizationService
  )
}

trait TeamInboxControlle_DI {
  lazy val controllerComponents: ControllerComponents
  lazy val wSClient: WSClient
  lazy val permissionUtils: PermissionUtils
  lazy val teamInboxService: TeamInboxService
  lazy val srUuidService: SrUuidService
  lazy val replySentimentService: ReplySentimentService
  lazy val linkedinMessageThreadsDAO: LinkedinMessageThreadsDAO
  lazy val emailThreadDAOService: EmailThreadDAOService

  lazy val teamInboxController: TeamInboxController = new TeamInboxController(
    controllerComponents = controllerComponents,
    wsClient = wSClient,
    srUuidService = srUuidService,
    permissionUtils = permissionUtils,
    teamInboxService = teamInboxService,
    emailThreadDAOService = emailThreadDAOService,
    linkedinMessageThreadsDAO = linkedinMessageThreadsDAO,
    replySentimentService = replySentimentService
  )
}
