package utils.dependencyinjectionutils

import api.accounts.AccountService
import api.accounts.dao.OrganizationDAO
import api.campaigns.{CampaignDAO, CampaignStepDAO}
import api.columns.ProspectColumnDefDAO
import api.columns.services.MqProcessAndGenerateMagicColumn
import api.emails.EmailSettingDAO
import api.emails.services.OldEmailSyncService
import api.spammonitor.service.SpamMonitorService
import api.team_inbox.dao.EmailTeamInboxDAO
import api.triggers.TriggerService
import utils.cronjobs.OldEmailSyncForTeamInboxCron
import utils.cronjobs.columns.ScheduleMagicColumnGenerationCron
import utils.cronjobs.spammonitor.CampaignForSpamCheckQueueCron
import utils.email.EmailService
import utils.email_notification.service.EmailNotificationService
import utils.intercom.dao.IntercomApi
import utils.intercom.services.{IntercomUpdateQueueingService, IntercomUpdateService}
import utils.mq.email.MQReadOldEmailForSync
import utils.mq.intercom_updater.MqIntercomUpdater
import utils.mq.replytracker.ReplyTrackerService
import utils.mq.spammonitor.{MqCampaignSpamMonitorService, MqCampaignStepContentCheckService}
import utils.sr_product_usage_data.services.SrUserFeatureUsageEventService
import utils.uuid.SrUuidUtils

object Intercom {

  trait IntercomApiDI {

    lazy val intercomApi: IntercomApi = new IntercomApi

  }

  trait IntercomUpdateServiceDI {

    lazy val intercomApi: IntercomApi
    lazy val accountService: AccountService
    lazy val srUserFeatureUsageEventService: SrUserFeatureUsageEventService
    lazy val organizationDAO: OrganizationDAO
    lazy val triggerService: TriggerService
    lazy val campaignDAO: CampaignDAO

    lazy val intercomUpdateService: IntercomUpdateService = new IntercomUpdateService(

      intercomApi = intercomApi,
      accountService = accountService,
      organizationDAO = organizationDAO,
      srUserFeatureUsageEventService = srUserFeatureUsageEventService,
      triggerService = triggerService,
      campaignDAO = campaignDAO

    )

  }


  trait MqIntercomUpdaterDI {

    lazy val intercomUpdateService: IntercomUpdateService

    lazy val mqIntercomUpdater: MqIntercomUpdater = new MqIntercomUpdater(
      intercomUpdateService = intercomUpdateService
    )
  }

  trait IntercomUpdateQueueingServiceDI {

    lazy val organizationDAO: OrganizationDAO
    lazy val mqIntercomUpdater: MqIntercomUpdater

    lazy val intercomUpdateQueueingService: IntercomUpdateQueueingService = new IntercomUpdateQueueingService(
      organizationDAO = organizationDAO,
      mqIntercomUpdater = mqIntercomUpdater,
    )

  }

// FIXME everything below doesnt belong here
  
  trait MqCampaignSpamMonitorService_DI {

    lazy val spamMonitorService: SpamMonitorService
    lazy val mqCampaignSpamMonitorService: MqCampaignSpamMonitorService = new MqCampaignSpamMonitorService(
      spamMonitorService = spamMonitorService
    )
  }

  trait CampaignForSpamCheckQueueCron_DI {
    lazy val spamMonitorService: SpamMonitorService
    lazy val campaignStepDAO: CampaignStepDAO
    lazy val mqCampaignStepContentCheckService: MqCampaignStepContentCheckService
    lazy val mqCampaignSpamMonitorService: MqCampaignSpamMonitorService
    lazy val campaignForSpamCheckQueueCron: CampaignForSpamCheckQueueCron = new CampaignForSpamCheckQueueCron(
      spamMonitorService = spamMonitorService,
      campaignStepDAO = campaignStepDAO,
      mqCampaignStepContentCheckService = mqCampaignStepContentCheckService,
      mqCampaignSpamMonitorService = mqCampaignSpamMonitorService
    )
  }

  trait ScheduleMagicColumnGenerationCron_DI {

    lazy val prospectColumnDefDAO: ProspectColumnDefDAO
    lazy val mqProcessAndGenerateMagicColumn: MqProcessAndGenerateMagicColumn

    lazy val scheduleMagicColumnGenerationCron: ScheduleMagicColumnGenerationCron = new ScheduleMagicColumnGenerationCron(
      prospectColumnDefDAO = prospectColumnDefDAO,
      mqProcessAndGenerateMagicColumn = mqProcessAndGenerateMagicColumn,
    )

  }



  trait OldEmailSyncForTeamInboxCron_DI {
    lazy val teamInboxDAO: EmailTeamInboxDAO
    lazy val mqReadOldEmailForSync: MQReadOldEmailForSync
    lazy val oldEmailSyncForTeamInboxCron: OldEmailSyncForTeamInboxCron = new OldEmailSyncForTeamInboxCron(
      teamInboxDAO = teamInboxDAO,
      mqReadOldEmailForSync = mqReadOldEmailForSync
    )
  }

  trait MQReadOldEmailForSync_DI {

    lazy val replyTrackerService: ReplyTrackerService
    lazy val oldEmailSyncService: OldEmailSyncService

    lazy val mqReadOldEmailForSync: MQReadOldEmailForSync = new MQReadOldEmailForSync(
      replyTrackerService = replyTrackerService,
      oldEmailSyncService = oldEmailSyncService
    )
  }

  trait OldEmailSyncService_DI {
    lazy val emailSettingDAO: EmailSettingDAO
    lazy val emailService: EmailService
    lazy val teamInboxDAO: EmailTeamInboxDAO
    lazy val emailNotificationService: EmailNotificationService
    lazy val accountService: AccountService
    lazy val srUuidUtils: SrUuidUtils

    lazy val oldEmailSyncService: OldEmailSyncService = new OldEmailSyncService(
      srUuidUtils = srUuidUtils,
      emailSettingDAO = emailSettingDAO,
      emailService = emailService,
      teamInboxDAO = teamInboxDAO,
      emailNotificationService = emailNotificationService,
      accountService = accountService
    )

  }
}