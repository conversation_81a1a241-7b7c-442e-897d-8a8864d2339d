package utils.dependencyinjectionutils

import utils.EmailSpamTester
import utils.cronjobs.spammonitor.EmailDeliveryAnalysisImpl
import utils.email.EmailService
import utils.emailvalidation.EmailValidationModel

trait EmailSpamTesterDI {
  lazy val emailService: EmailService
  lazy val emailSpamTester: EmailSpamTester = new EmailSpamTester(
    emailService = emailService
  )
}

trait EmailDeliveryAnalysisServiceDI {
  lazy val emailDeliveryAnalysisService: EmailDeliveryAnalysisImpl = new EmailDeliveryAnalysisImpl
}

trait EmailValidationModelDI {
  lazy val emailValidationModel: EmailValidationModel = new EmailValidationModel()
}