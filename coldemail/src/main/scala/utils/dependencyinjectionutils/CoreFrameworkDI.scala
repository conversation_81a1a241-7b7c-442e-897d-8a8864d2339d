package utils.dependencyinjectionutils

import org.apache.pekko.actor.ActorSystem
import org.apache.pekko.stream.Materializer
import play.api.libs.ws.ahc.AhcWSClient


trait WorkerActorMaterializerDI {

  lazy val workerActorSystem: ActorSystem
  lazy val workerActorMaterializer: Materializer = Materializer(workerActorSystem)


}

trait WorkerActorSystemDI {

  lazy val workerActorMaterializer: Materializer
  implicit lazy val workerActorSystem: ActorSystem = ActorSystem()

}

trait WorkerWSClientDI {

  implicit lazy val workerActorMaterializer: Materializer
  implicit lazy val wSClient: AhcWSClient = AhcWSClient()

}