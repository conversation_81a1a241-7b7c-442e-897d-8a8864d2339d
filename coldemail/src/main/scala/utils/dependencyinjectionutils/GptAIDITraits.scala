package utils.dependencyinjectionutils
import api.gpt.{<PERSON><PERSON><PERSON>, GP<PERSON>ontroller, GPTInboxService, GPTService}
import api.gpt.services.GptApiService
import utils.mq.MQCampaignAISequenceGenerator
import api.llm.dao.LlmAuditLogDAO
import app_services.sr_rate_limiter.SrRateLimiter
import api.prospects.InboxV3Service
import api.campaigns.services.{CampaignDAOService, CampaignStepService}
import api.campaigns.{CampaignDAO, CampaignEditedPreviewEmailDAO, CampaignProspectDAO}
import utils.PusherService
import api.gpt.ai_sequence.AISequenceGenerator
import api.gpt.ai_hyperpersonalized.AIHyperPersonalizedGenerator
import api.accounts.PermissionUtils
import play.api.mvc.ControllerComponents
import api.emails.dao_service.{EmailScheduledDAOService, EmailThreadDAOService}
import api.linkedin_message_threads.LinkedinMessageThreadsDAO
import api.prospects.dao_service.ProspectDAOService
import utils.uuid.services.SrUuidService
import api.team_inbox.service.TeamInboxService
import org.apache.pekko.stream.Materializer
import play.api.libs.ws.WSClient
import utils.dateTime.SrDateTimeUtils
import utils.mq.ai_content_generation.{AiContentGenerationProcessor, MqAiContentGenerationConsumer, MqAiContentGenerationPublisher}
import api.campaigns.services.CampaignService
import api.emails.EmailSettingDAO
import api.sr_ai.service.{CampaignGenerationService, ContentGenerationService}
import api.tasks.services.TaskService
import utils.cronjobs.AiContentGenerationCron
import api.sr_ai.apis.SrAiApi
import api.accounts.service.OrganizationService
import api.rep_tracking_hosts.service.RepTrackingHostService
import api.sr_ai.dao.SrAiLockJedisDAO
import utils.email.EmailServiceCompanion
import utils.uuid.SrUuidUtils


trait GPTApiDI {
  lazy val gptApi: GPTApi = new GPTApi()
}

trait MQCampaignAISequenceGeneratorDI {
  lazy val aiSequenceGenerator: AISequenceGenerator

  lazy val mqCampaignAISequenceGenerator: MQCampaignAISequenceGenerator = new MQCampaignAISequenceGenerator(
    aiSequenceGenerator = aiSequenceGenerator
  )
}

trait GptApiService_DI {
  lazy val gptApi: GPTApi
  lazy val llmAuditLogDAO: LlmAuditLogDAO
  lazy val gptApiService: GptApiService = new GptApiService(
    gptApi = gptApi,
    llmAuditLogDAO = llmAuditLogDAO
  )
}

trait GPTServiceDI {
  lazy val srRateLimiter: SrRateLimiter
  lazy val gptApiService: GptApiService
  lazy val contentGenerationService: ContentGenerationService

  lazy val gptService: GPTService = new GPTService(
    srRateLimiter = srRateLimiter,
    gptApiService = gptApiService,
    contentGenerationService = contentGenerationService,
  )
}

trait GPTInboxServiceDI {
  lazy val srRateLimiter: SrRateLimiter
  lazy val gptApiService: GptApiService
  lazy val inboxV3Service: InboxV3Service
  lazy val contentGenerationService: ContentGenerationService

  lazy val gptInboxService: GPTInboxService = new GPTInboxService(
    inboxV3Service = inboxV3Service,
    srRateLimiter = srRateLimiter,
    contentGenerationService = contentGenerationService,
    gptApiService = gptApiService
  )
}

trait AISequenceGeneratorDI {
  lazy val gptApiService: GptApiService
  lazy val campaignStepService: CampaignStepService
  lazy val campaignDAO: CampaignDAO
  lazy val pusherService: PusherService
  lazy val campaignGenerationService: CampaignGenerationService

  lazy val aiSequenceGenerator: AISequenceGenerator = new AISequenceGenerator(
    gptApiService = gptApiService,
    campaignStepService = campaignStepService,
    campaignDAO = campaignDAO,
    campaignGenerationService = campaignGenerationService,
    pusherService = pusherService
  )
}

trait AIHyperPersonalizedGeneratorDI {
  lazy val gptApiService: GptApiService
  lazy val campaignStepService: CampaignStepService
  lazy val campaignDAO: CampaignDAO
  lazy val pusherService: PusherService
  lazy val srAiApi: SrAiApi

  lazy val organizationService: OrganizationService

  lazy val aiHyperPersonalizedGenerator: AIHyperPersonalizedGenerator = new AIHyperPersonalizedGenerator(
    gptApiService = gptApiService,
    campaignStepService = campaignStepService,
    campaignDAO = campaignDAO,
    pusherService = pusherService,
    srAiApi = srAiApi,
    organizationService = organizationService
  )
}

trait GPTControllerDI {
  lazy val permissionUtils: PermissionUtils
  lazy val gptService: GPTService
  lazy val gptInboxService: GPTInboxService
  lazy val controllerComponents: ControllerComponents
  lazy val emailThreadDAOService: EmailThreadDAOService
  lazy val linkedinMessageThreadsDAO: LinkedinMessageThreadsDAO
  lazy val srUuidService: SrUuidService
  lazy val teamInboxService: TeamInboxService
  lazy val actorMaterializer: Materializer
  lazy val wSClient: WSClient

  lazy val gptController: GPTController = new GPTController(
    permissionUtils = permissionUtils,
    gptService = gptService,
    gptInboxService = gptInboxService,
    srUuidService = srUuidService,
    emailThreadDAOService = emailThreadDAOService,
    linkedinMessageThreadsDAO = linkedinMessageThreadsDAO,
    teamInboxService = teamInboxService,
    controllerComponents = controllerComponents,
    materializer = actorMaterializer,
    ws = wSClient
  )
}


trait LlmAuditLogDAO_DI {
  lazy val llmAuditLogDAO: LlmAuditLogDAO = new LlmAuditLogDAO
}



// --- DI Traits for AI Content Generation MQ ---

trait MqAiContentGenerationPublisher_DI {
  // Publisher usually has no dependencies other than MQ config/connection managed by base traits
  lazy val mqAiContentGenerationPublisher: MqAiContentGenerationPublisher = new MqAiContentGenerationPublisher
}

trait AiContentGenerationProcessor_DI {
  // Dependencies required by the processor logic
  lazy val campaignDAOService: CampaignDAOService
  lazy val aiHyperPersonalizedGenerator: AIHyperPersonalizedGenerator
  lazy val emailScheduledDAOService: EmailScheduledDAOService
  lazy val srDateTimeUtils: SrDateTimeUtils
  lazy val campaignService: CampaignService
  lazy val campaignProspectDAO: CampaignProspectDAO
  lazy val prospectDAOService: ProspectDAOService
  lazy val emailSettingDAO: EmailSettingDAO
  lazy val taskService: TaskService
  lazy val llmAuditLogDAO: LlmAuditLogDAO
  lazy val srAiLockJedisDAO: SrAiLockJedisDAO
  lazy val srUuidUtils: SrUuidUtils
  lazy val emailServiceCompanion: EmailServiceCompanion
  lazy val repTrackingHostService: RepTrackingHostService

  // TODO: Add other dependencies if needed (e.g., CalendarAccountData fetching service)
  /*
   campaignDAOService: CampaignDAOService,
  prospectDAOService: ProspectDAOService,
  aiHyperPersonalizedService: AIHyperPersonalizedGenerator,
  emailScheduledDAOService: EmailScheduledDAOService,
  campaignService: CampaignService,
  campaignEditedPreviewEmailDAO: CampaignEditedPreviewEmailDAO,
  campaignProspectDAO: CampaignProspectDAO,
  srDateTimeUtils: SrDateTimeUtils
   */

  lazy val aiContentGenerationProcessor: AiContentGenerationProcessor = new AiContentGenerationProcessor(
    campaignDAOService = campaignDAOService,
    campaignService = campaignService,
    prospectDAOService = prospectDAOService,
    aiHyperPersonalizedService = aiHyperPersonalizedGenerator,
    emailScheduledDAOService = emailScheduledDAOService,
    emailSettingDAO = emailSettingDAO,
    taskService = taskService,
    campaignProspectDAO = campaignProspectDAO,
    srDateTimeUtils = srDateTimeUtils,
    llmAuditLogDAO = llmAuditLogDAO,
    srUuidUtils = srUuidUtils,
    emailServiceCompanion = emailServiceCompanion,
    repTrackingHostService = repTrackingHostService,
    srAiLockJedisDAO = srAiLockJedisDAO
  )
}

trait MqAiContentGenerationConsumer_DI {
  // Consumer needs the Publisher (for trait compliance) and the Processor
  lazy val mqAiContentGenerationPublisher: MqAiContentGenerationPublisher
  lazy val aiContentGenerationProcessor: AiContentGenerationProcessor

  lazy val mqAiContentGenerationConsumer: MqAiContentGenerationConsumer = new MqAiContentGenerationConsumer(
    mqPublisher = mqAiContentGenerationPublisher,
    processor = aiContentGenerationProcessor
  )
}

// --- End AI Content Generation MQ DI ---


// --- DI Trait for AiContentGenerationCron ---
trait AiContentGenerationCron_DI {
  // This service needs to be provided by another DI trait (e.g., LlmAuditLogService_DI)
  // that defines and implements the actual LlmAuditLogService based on your project structure.
  // The AiContentGenerationCron currently has a placeholder for LlmAuditLogService trait.
  lazy val llmAuditLogDAO: LlmAuditLogDAO // Placeholder: replace with actual service or its DI trait
  lazy val mqAiContentGenerationPublisher: MqAiContentGenerationPublisher
  lazy val srAiLockJedisDAO: SrAiLockJedisDAO
  lazy val organizationService: OrganizationService

  lazy val aiContentGenerationCron: AiContentGenerationCron = new AiContentGenerationCron(
    llmAuditLogDAO = llmAuditLogDAO,
    mqPublisher = mqAiContentGenerationPublisher,
    srAiLockJedisDAO = srAiLockJedisDAO,
    organizationService = organizationService
  )
}
// --- End AiContentGenerationCron DI ---



