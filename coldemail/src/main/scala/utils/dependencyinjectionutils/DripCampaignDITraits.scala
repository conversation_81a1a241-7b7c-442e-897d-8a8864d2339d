package utils.dependencyinjectionutils

import api.accounts.AccountService
import api.accounts.dao.OrganizationDAO
import api.campaigns.{CampaignDAO, CampaignProspectDAO}
import api.campaigns.dao.{CampaignSendingVolumeLogsDAO, DripCampaignHealthCheckDAO}
import api.campaigns.services.{CampaignProspectService, CampaignService, DripCampaignHealthCheckService}
import api.prospects.ProspectService
import api.reports.models.SrInternalFeatureUsageDAO
import utils.cronjobs.campaigns.MonitorDripCampaignsCron
import utils.mq.campaigns.{MqDripCampaignHealthCheckConsumer, MqDripCampaignHealthCheckPublisher}
import utils.sr_product_usage_data.services.SrUserFeatureUsageEventService
import utils.srinternalcampaigns.NewUserDripCampaignService

object DripHealthCheck {

  trait DripCampaignHealthCheckDAO_DI {

    lazy val dripCampaignHealthCheckDAO: DripCampaignHealthCheckDAO = new DripCampaignHealthCheckDAO

  }

  trait DripCampaignHealthCheckService_DI {

    lazy val dripCampaignHealthCheckDAO: DripCampaignHealthCheckDAO
    lazy val campaignService: CampaignService
    lazy val campaignSendingVolumeLogsDAO: CampaignSendingVolumeLogsDAO

    lazy val dripCampaignHealthCheckService: DripCampaignHealthCheckService = new DripCampaignHealthCheckService(
      dripCampaignHealthCheckDAO = dripCampaignHealthCheckDAO,
      campaignService = campaignService,
      campaignSendingVolumeLogsDAO = campaignSendingVolumeLogsDAO,
    )

  }

  trait MqDripCampaignHealthCheckConsumer_DI {

    lazy val dripCampaignHealthCheckService: DripCampaignHealthCheckService
    lazy val mqDripCampaignHealthCheckPublisher: MqDripCampaignHealthCheckPublisher

    lazy val mqDripCampaignHealthCheckConsumer: MqDripCampaignHealthCheckConsumer = new MqDripCampaignHealthCheckConsumer(
      dripCampaignHealthCheckService = dripCampaignHealthCheckService,
      mqDripCampaignHealthCheckPublisher = mqDripCampaignHealthCheckPublisher,
    )

  }

  trait MqDripCampaignHealthCheckPublisher_DI {

    lazy val mqDripCampaignHealthCheckPublisher: MqDripCampaignHealthCheckPublisher = new MqDripCampaignHealthCheckPublisher

  }

  trait MonitorDripCampaignsCron_DI {

    lazy val dripCampaignHealthCheckService: DripCampaignHealthCheckService
    lazy val mqDripCampaignHealthCheckPublisher: MqDripCampaignHealthCheckPublisher

    lazy val monitorDripCampaignsCron: MonitorDripCampaignsCron = new MonitorDripCampaignsCron(
      dripCampaignHealthCheckService = dripCampaignHealthCheckService,
      mqDripCampaignHealthCheckPublisher = mqDripCampaignHealthCheckPublisher,
    )

  }

}

trait NewUserDripCamapignServiceDI {
  lazy val srInternalFeatureUsageDao: SrInternalFeatureUsageDAO
  lazy val prospectService: ProspectService
  lazy val srUserFeatureUsageEventService: SrUserFeatureUsageEventService
  lazy val accountService: AccountService
  lazy val campaignProspectService: CampaignProspectService
  lazy val campaignProspectDAO: CampaignProspectDAO
  lazy val organizationDAO: OrganizationDAO
  lazy val campaignDAO: CampaignDAO

  lazy val newUserDripCampaignService: NewUserDripCampaignService = new NewUserDripCampaignService(
    campaignProspectService = campaignProspectService,
    campaignProspectDAO = campaignProspectDAO,
    srInternalFeatureUsageDao = srInternalFeatureUsageDao,
    prospectService = prospectService,
    srUserFeatureUsageEventService = srUserFeatureUsageEventService,
    organizationDAO = organizationDAO,
    accountService = accountService,
    campaignDAO = campaignDAO
  )
}
