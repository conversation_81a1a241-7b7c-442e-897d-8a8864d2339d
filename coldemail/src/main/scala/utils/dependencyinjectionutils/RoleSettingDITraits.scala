package utils.dependencyinjectionutils

import api.accounts.{AccountDAO, AccountService, AuthUtils, PermissionUtils, RolePermissionDataDAOV2}
import api.accounts.dao.{OrganizationDAO, TeamsDAO}
import api.accounts.service.ResetUserCacheUtil
import api.emails.{EmailSettingDA<PERSON>, RoleSettingController}
import api.emails.services.RoleSettingService
import api.team.service.TeamService
import play.api.mvc.ControllerComponents
import utils.dbutils.DBUtils
import utils.featureflags.services.OrgMetadataService
import utils.uuid.services.SrUuidService

trait RoleSettingControllerDI {

  lazy val resetUserCacheUtil: ResetUserCacheUtil
  lazy val accountService: AccountService
  lazy val dbUtils: DBUtils
  lazy val teamDAO: TeamsDAO

  lazy val rolePermissionDataDAOV2: RolePermissionDataDAOV2
  lazy val emailSettingDAO: EmailSettingDAO

  lazy val authUtils: AuthUtils
  lazy val permissionUtils: PermissionUtils
  lazy val controllerComponents: ControllerComponents
  lazy val roleSettingService: RoleSettingService
  lazy val srUuidService: SrUuidService
  lazy val roleSettingController: RoleSettingController = new RoleSettingController(
    resetUserCacheUtil = resetUserCacheUtil,
    controllerComponents = controllerComponents,
    authUtils = authUtils,
    teamDAO = teamDAO,
    permissionUtils = permissionUtils,
    accountService = accountService,
    roleSettingService = roleSettingService,
    srUuidService = srUuidService,
    emailSettingDAO = emailSettingDAO,

    rolePermissionDataDAOV2 = rolePermissionDataDAOV2,
  )
}

trait RoleSettingServiceDI {
  lazy val dbUtils: DBUtils
  lazy val teamDAO: TeamsDAO
  lazy val accountDAO: AccountDAO
  lazy val teamService: TeamService
  lazy val organizationDAO: OrganizationDAO
  lazy val rolePermissionDataDAOV2: RolePermissionDataDAOV2
  lazy val resetUserCacheUtil: ResetUserCacheUtil
  lazy val orgMetadataService: OrgMetadataService

  lazy val roleSettingService: RoleSettingService = new RoleSettingService(
    organizationDAO = organizationDAO,
    orgMetadataService = orgMetadataService,
    dbUtils = dbUtils,
    teamDAO = teamDAO,
    teamService = teamService,
    accountDAO = accountDAO,
    rolePermissionDataDAOV2 = rolePermissionDataDAOV2,
    resetUserCacheUtil = resetUserCacheUtil
  )
}