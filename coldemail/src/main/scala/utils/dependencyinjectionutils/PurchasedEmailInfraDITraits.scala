package utils.dependencyinjectionutils

import api.accounts.{AccountService, AuthUtils, PermissionUtils, SocialAuthService}
import api.email_infra_integrations.controllers.EmailInfraController
import api.email_infra_integrations.dao.EmailInfraDAO
import api.email_infra_integrations.maildoso.MailDosoAPI
import api.email_infra_integrations.maildoso.service.MailDosoService
import api.email_infra_integrations.services.EmailInfraService
import api.email_infra_integrations.zapmail.ZapMailAPI
import api.emails.EmailSettingDAO
import api.emails.services.{EmailAccountService, EmailSettingService}
import api.middleware.LoggingAction
import play.api.libs.ws.WSClient
import play.api.mvc.ControllerComponents
import utils.{PlanLimitService, PusherService}
import utils.cronjobs.{PollMaildosoPurchaseTasksCron, PurchaseEmailsCron, PurchasedDomainAndEmailDeleterCron}
import utils.mq.emailInfra.{MqZapmailEmailAccountCreationConsumer, MqZapmailEmailAccountCreationPublisher}
import utils.mq.purchased_domains_and_emails_deleter.{MqPurchasedDomainsDeleter, MqPurchasedEmailsDeleter}
import utils.mq.purchased_domains_and_emails_deleter.service.PurchasedDomainsAndEmailsDeleterService
import utils.uuid.SrUuidUtils

trait PollMaildosoPurchaseTasksCron_DI {
  lazy val emailInfraService: EmailInfraService
  lazy val pollMaildosoPurchaseTasksCron: PollMaildosoPurchaseTasksCron = new PollMaildosoPurchaseTasksCron(
    emailInfraService = emailInfraService
  )
}

trait PurchasedDomainAndEmailDeleterCron_DI {
  lazy val emailInfraService: EmailInfraService
  lazy val purchasedDomainAndEmailDeleterCron: PurchasedDomainAndEmailDeleterCron = new PurchasedDomainAndEmailDeleterCron(
    emailInfraService = emailInfraService
  )
}


trait PurchaseDomainsAndEmailsCron_DI {
  lazy val emailInfraService : EmailInfraService
  lazy val purchaseDomainsAndEmailsCron: PurchaseEmailsCron = new PurchaseEmailsCron(
    emailInfraService = emailInfraService
  )
}

trait PurchasedDomainsAndEmailsDeleterService_DI {

  lazy val maildosoService: MailDosoService
  lazy val emailInfraDao: EmailInfraDAO
  lazy val emailSettingService: EmailSettingService
  lazy val zapmailApi: ZapMailAPI

  lazy val purchasedDomainsAndEmailsDeleterService: PurchasedDomainsAndEmailsDeleterService = new PurchasedDomainsAndEmailsDeleterService(
    maildosoService = maildosoService,
    zapMailAPI = zapmailApi,
    emailInfraDao = emailInfraDao,
    emailSettingService = emailSettingService
  )

}

trait MqPurchasedDomainsDeleter_DI {

  lazy val purchasedDomainsAndEmailsDeleterService: PurchasedDomainsAndEmailsDeleterService
  lazy val emailSettingService: EmailSettingService
  lazy val mqPurchasedEmailsDeleter: MqPurchasedEmailsDeleter

  lazy val mqPurchasedDomainsDeleter: MqPurchasedDomainsDeleter = new MqPurchasedDomainsDeleter(
    purchasedDomainsAndEmailsDeleterService = purchasedDomainsAndEmailsDeleterService,
    emailSettingService = emailSettingService,
    mqPurchasedEmailsDeleter = mqPurchasedEmailsDeleter
  )
}

trait MqPurchasedEmailsDeleter_DI {

  lazy val purchasedDomainsAndEmailsDeleterService: PurchasedDomainsAndEmailsDeleterService

  lazy val mqPurchasedEmailsDeleter: MqPurchasedEmailsDeleter = new MqPurchasedEmailsDeleter(
    purchasedDomainsAndEmailsDeleterService = purchasedDomainsAndEmailsDeleterService
  )
}

trait MqZapmailEmailAccountCreationPublisher_DI {
  lazy val mqZapmailEmailAccountCreationPublisher: MqZapmailEmailAccountCreationPublisher = new MqZapmailEmailAccountCreationPublisher
}

trait MqZapmailEmailAccountCreationConsumer_DI {

  lazy val mqZapmailEmailAccountCreationPublisher: MqZapmailEmailAccountCreationPublisher
  lazy val emailInfraService : EmailInfraService

  lazy val mqZapmailEmailAccountCreationConsumer: MqZapmailEmailAccountCreationConsumer = new MqZapmailEmailAccountCreationConsumer(
    emailInfraService = emailInfraService,
    mqZapmailEmailAccountCreationPublisher = mqZapmailEmailAccountCreationPublisher
  )
}



trait ZapMailAPI_DI {
  lazy val zapmailApi: ZapMailAPI = new ZapMailAPI()
}

trait MailDosoApi_DI {
  lazy val maildosoApi: MailDosoAPI = new MailDosoAPI()
}

trait MailDosoService_DI {
  lazy val maildosoApi: MailDosoAPI
  lazy val pusherService: PusherService

  lazy val maildosoService: MailDosoService = new MailDosoService(
    mailDosoAPI = maildosoApi,
    pusherService = pusherService
  )
}

trait EmailInfraDao_DI {
  lazy val srUuidUtils: SrUuidUtils

  lazy val emailInfraDao: EmailInfraDAO = new EmailInfraDAO(
    srUuidUtils = srUuidUtils
  )
}

trait EmailInfraService_DI {
  lazy val maildosoService: MailDosoService
  lazy val emailInfraDao: EmailInfraDAO
  lazy val emailSettingDAO: EmailSettingDAO
  lazy val planLimitService: PlanLimitService
  lazy val emailSettingService: EmailSettingService
  lazy val mqPurchasedDomainsDeleter: MqPurchasedDomainsDeleter
  lazy val mqPurchasedEmailsDeleter: MqPurchasedEmailsDeleter
  lazy val emailAccountService: EmailAccountService
  lazy val accountService: AccountService
  lazy val zapmailApi: ZapMailAPI
  lazy val mqZapmailEmailAccountCreationPublisher: MqZapmailEmailAccountCreationPublisher
  lazy val socialAuthService: SocialAuthService

  lazy val emailInfraService: EmailInfraService = new EmailInfraService(
    mailDosoService = maildosoService,
    emailSettingDAO = emailSettingDAO,
    emailInfraDAO = emailInfraDao,
    planLimitService = planLimitService,
    emailSettingService = emailSettingService,
    socialAuthService = socialAuthService,
    mqPurchasedDomainsDeleter = mqPurchasedDomainsDeleter,
    mqPurchasedEmailsDeleter = mqPurchasedEmailsDeleter,
    emailAccountService = emailAccountService,
    zapMailApi = zapmailApi,
    accountService = accountService,
    mqZapmailEmailAccountCreationPublisher = mqZapmailEmailAccountCreationPublisher
  )
}

trait EmailInfraControllerDI {

  lazy val controllerComponents: ControllerComponents
  //  lazy val emailInfraService: EmailInfraService // This is the service handling email infrastructure-related logic.
  implicit lazy val wSClient: WSClient
  lazy val loggingAction: LoggingAction
  lazy val permissionUtils: PermissionUtils
  lazy val authUtils: AuthUtils
  lazy val emailInfraService: EmailInfraService

  lazy val emailInfraController: EmailInfraController = new EmailInfraController(
    controllerComponents = controllerComponents,
    emailInfraService = emailInfraService,
    permissionUtils = permissionUtils,
    loggingAction = loggingAction,
    authUtils = authUtils,
    ws = wSClient
  )
}


/* 17-jun-2025: [DO_NOT_REMOVE] [LEADFINDERUPLOAD] was slowing down compilation. Only needed for the Lead database upload.

trait UploadLeadFinderCsvCronServiceDI {
  lazy val leadFinderUploadDao: LeadFinderUploadDao
  lazy val leadFinderUploadService: LeadFinderUploadService

  lazy val uploadLeadFinderCsvCronService: UploadLeadFinderCsvCronService = new UploadLeadFinderCsvCronService(
    leadFinderUploadService = leadFinderUploadService,
    leadFinderUploadDao = leadFinderUploadDao
  )
}

trait UploadLeadFinderCsvLinkedinCronServiceDI {
  lazy val leadFinderUploadDao: LeadFinderUploadDao
  lazy val leadFinderUploadService: LeadFinderUploadService

  lazy val uploadLeadFinderLinkedinCsvCronService: UploadLeadFinderCsvLinkedinCronService = new UploadLeadFinderCsvLinkedinCronService(
    leadFinderUploadService = leadFinderUploadService,
    leadFinderUploadDao = leadFinderUploadDao
  )
}
*/

