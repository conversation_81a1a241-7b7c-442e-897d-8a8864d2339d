package utils.dependencyinjectionutils

import api.emails.EmailSettingDAO
import api.tracking_host.dao.{CustomTrackingDomainDAO, RepTrackingHostDao}
import api.tracking_host.services.CustomTrackingDomainService
import utils.cloudflare.CloudflareService
import utils.email_notification.service.EmailNotificationService


trait CloudflareServiceDI {

  lazy val cloudflareService: CloudflareService = new CloudflareService()
}

trait CustomTrackingDomainServiceDI {

  lazy val cloudflareService: CloudflareService
  lazy val emailSettingDAO: EmailSettingDAO
  lazy val customTrackingDomainDAO: CustomTrackingDomainDAO
  lazy val emailNotificationService: EmailNotificationService
  lazy val repTrackingHostDao: RepTrackingHostDao

  lazy val customTrackingDomainService: CustomTrackingDomainService = new CustomTrackingDomainService(
    cloudflareService = cloudflareService,
    emailSettingDAO = emailSettingDAO,
    customTrackingDomainDAO = customTrackingDomainDAO,
    emailNotificationService = emailNotificationService,
    repTrackingHostDao = repTrackingHostDao
  )
}

trait CustomTrackingDomainServiceDAO_DI {

  lazy val customTrackingDomainDAO: CustomTrackingDomainDAO = new CustomTrackingDomainDAO()

}