package utils.intercom.services

import api.accounts.dao.{OrganizationDAO, OrganizationOnBoardingData}
import api.accounts.{Account, AccountService, OrganizationRole}
import api.campaigns.CampaignDAO
import api.integrations.CRMIntegrations
import api.triggers.TriggerService
import io.sr.billing_common.models.{SrPlanName, PlanID}
import play.api.libs.json.JsValue
import play.api.libs.ws.WSClient
import utils.SRLogger
import utils.intercom.dao.{IntercomApi, InternalSRUsageData}
import utils.sr_product_usage_data.models.SrUserFeatureUsageEventType
import utils.sr_product_usage_data.services.SrUserFeatureUsageEventService

import scala.concurrent.{ExecutionContext, Future}

class IntercomUpdateService(
  intercomApi: IntercomApi,
  accountService: AccountService,
  organizationDAO: OrganizationDAO,
  srUserFeatureUsageEventService: SrUserFeatureUsageEventService,
  triggerService: TriggerService,
  campaignDAO: CampaignDAO
) {


  def updateForOrgId(
    orgId: Long
  )(

    implicit wsClient: WSClient,
    ec: ExecutionContext,
    logger: SRLogger

  ): Future[Boolean] = {

    intercomApi
      .findCompany(
        orgId = orgId
      )
      .flatMap {

        case None =>

          logger.error(s"company not found on intercom: ignoring org_$orgId")

          Future.successful(
            false
          )

        case Some(_) =>

          for {

            acc: Account <- Future
              .fromTry(
                accountService.findOwnerAccountByOrgId(orgId = orgId)
              )

            currentStage: Option[SrUserFeatureUsageEventType] <- Future
              .fromTry(

                srUserFeatureUsageEventService
                  .getCurrentOnboardingFunnelStage(
                    orgId = acc.org.id
                  )

              )
              .map {

                case None =>

                  logger.warn("getCurrentOnboardingFunnelStage: not found")
                  None


                case Some(value) =>

                  Some(
                    value.srUserFeatureUsageEventType
                  )

              }


            onBoardingData: Option[OrganizationOnBoardingData]<- Future
              .fromTry (
                organizationDAO.getOrgOnBoardingDataByOrgID(orgId = orgId)
              )
              .map{
                case None =>
                  logger.warn("getOrgOnBoardingDataByOrgID: not found")
                  None

                case Some(onBoardingData) =>
                  Some(
                    onBoardingData
                  )

              }

            crmIntegration: CRMIntegrations <- Future.fromTry{
              triggerService.findAllCrmIntegrationByOrgId(orgId = orgId)
            }

            intercomAdditionalData:Option[InternalSRUsageData] <- Future.fromTry {
              campaignDAO.getInternalSRUsageDataByOrgID(orgId = orgId)
            }



            updateIntercomCompany: JsValue <- {

              val approxMonthlySpend: Int = PlanID.getApproximateMonthlySpend(
                planId = acc.org.plan.plan_id,
                isV2BusinessPlan = acc.org.plan.is_v2_business_plan,
                seats = acc.org.counts.total_sending_email_accounts
              )

              val isOrganizationOwner:Boolean = acc.org_role.isDefined && acc.org_role.get == OrganizationRole.OWNER

              intercomApi
                .updateCompany(
                  orgId = orgId,
                  approxMonthlySpend = approxMonthlySpend,
                  planId = acc.org.plan.plan_id,
                  planType = acc.org.plan.plan_type,

                  isBusinessPlan = PlanID.considerAsBusinessPlanForReporting(
                    totalSeats = acc.org.counts.total_sending_email_accounts,
                    planName = SrPlanName(name = acc.org.plan.plan_name),
                  ),

                  planName = acc.org.plan.plan_name,

                  totalEmailSettingsInPlan = acc.org.counts.total_sending_email_accounts,
                  intercomAdditionalData = intercomAdditionalData ,
                  isAgency = acc.org.is_agency,
                  currentOnboardingStage = currentStage,
                  orgOnBoardingData = onBoardingData,
                  crmIntegration = crmIntegration,
                  isOrganisationOwner = isOrganizationOwner

                )
            }

            _: Int <- Future.fromTry(

              organizationDAO.updatedIntercomData(
                orgId = orgId
              )

            )

          } yield {

            true

          }
      }



  }


}
