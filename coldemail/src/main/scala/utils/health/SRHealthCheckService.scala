package utils.health

import api.accounts.AccountDAO
import play.api.libs.ws.WSClient
import utils.email_notification.service.EmailNotificationService
import utils.helpers.LogHelpers
import utils.{Help<PERSON>, SRLogger}

import scala.concurrent.{ExecutionContext, Future}
import scala.concurrent.duration.DurationInt

class SRHealthCheckService(
                      accountDAO: AccountDAO,
                      emailNotificationService: EmailNotificationService,
                      implicit val wsClient: WSClient
                    ) {

  private given logger: SRLogger = new SRLogger(
    logRequestId = "SRHealthChecks"
  )

  def checkSenderWorkers()(

    implicit executionContext: ExecutionContext

  ): Future[Seq[Boolean]] = {
    Future.fromTry(
      accountDAO.getRepTrackingHosts()
    )
      .flatMap(hosts => {

        Future.sequence(

          hosts
            .filter(_.active)
            .map(host => {

              val hostUrl = s"https://${host.host_url}"

              logger.info(s"starting for host: $hostUrl")

              wsClient.url(hostUrl)
                .withRequestTimeout(2500.millis)
                .get()
                .flatMap(res => {
                  logger.info(s"checkSenderWorkers success host: $hostUrl response: $res")

                  if (res.status != 200) {

                    emailNotificationService.sendMailFromAdminFut(
                      subject = s"SR WORKER IS DOWN: ${host.host_url}",

                      body =
                        s"""
                      SRHealthChecks.checkSenderWorkers failing for $hostUrl.
                      <br/>
                      <br/>


                      host: $host
                      <br/>
                      <br/>

                      response status: ${res.status}
                      <br/>
                      <br/>

                      response: $res
                      """,

                      toEmail = "<EMAIL>",
                      toName = Some("Neil"),
                      ccEmail = Some("<EMAIL>")
                    ).map(_ => false)

                  } else {

                    Future.successful(true)

                  }

                })
                .recover { case e =>

                  logger.fatal(s"checkSenderWorkers host: $hostUrl error: ${LogHelpers.getStackTraceAsString(e)}")


                  emailNotificationService.sendMailFromAdminFut(
                    subject = s"SR WORKER IS DOWN: ${host.host_url}",

                    body =
                      s"""
                      SRHealthChecks.checkSenderWorkers failing for $hostUrl.

                      <br/>
                      <br/>

                      host: $host

                      <br/>
                      <br/>

                      request failed with exception:
                      <br/>
                      <br/>


                      ${LogHelpers.getStackTraceAsString(e)}

                      """,

                    toEmail = "<EMAIL>",
                    toName = Some("Neil"),
                    ccEmail = Some("<EMAIL>")
                  ).map(_ => false)

                  false
                }

            })

        )


      })


  }

}
