package utils.customersupport.services

import api.AppConfig
import api.accounts.models.OrgId
import org.joda.time.DateTime
import play.api.libs.json.{Json, Reads}
import utils.customersupport.dao.{CustomerAcquisitionReport, InternalCSDReportDAO}

import scala.util.{Failure, Success, Try}

case class AcquisitionFieldData(
  id: Long,
  users_count: Option[Int],
  new_users_count: Option[Int],
  demo_delivered: Option[Int],
  good_fit: Option[Int]
)

object AcquisitionFieldData {
  implicit val reads: Reads[AcquisitionFieldData] = Json.reads[AcquisitionFieldData]
}

case class UpdateAdoptionReportData(
  org_id: OrgId,
  src_done: Boolean
)

object UpdateAdoptionReportData {
  implicit val reads: Reads[UpdateAdoptionReportData] = Json.reads[UpdateAdoptionReportData]
}


class InternalCSDReportService(internalCSDReportDAO: InternalCSDReportDAO) {

  def getAcquisitionReport(
    startDate: DateTime, endDate: DateTime
  ): Try[List[CustomerAcquisitionReport]] = {

    internalCSDReportDAO.getAcquisitionReport(
      startDate = startDate,
      endDate = endDate
    )
  }

  def updateAcquisitionReport(
    acquisitionFieldData: AcquisitionFieldData
  ): Try[Int] = {

    if (
      acquisitionFieldData.users_count.isDefined
        || acquisitionFieldData.new_users_count.isDefined
        || acquisitionFieldData.demo_delivered.isDefined
        || acquisitionFieldData.good_fit.isDefined
    ) {

      InternalCSDReportService.validateAcquisitionReportOptionalFields(
        acquisitionFieldData = acquisitionFieldData
      ) match {
        case Some(errorMsg: String) =>
          Failure(new Exception(errorMsg))

        case None =>
          internalCSDReportDAO.updateAcquisitionReport(
            acquisitionFieldData = acquisitionFieldData
          )
      }
    } else {
      Failure(new Exception("No fields were provided for update"))
    }
  }

  def updateAdoptionReport(
    updateAdoptionReportData: UpdateAdoptionReportData
  ): Try[Int] = {

    internalCSDReportDAO.updateAdoptionReport(
      updateAdoptionReportData = updateAdoptionReportData
    )
  }

}

object InternalCSDReportService {
  def validateAcquisitionReportOptionalFields(acquisitionFieldData: AcquisitionFieldData): Option[String] = {

    val acquisitionReportOptionalFieldLimits = AppConfig.AcquisitionReportOptionalFieldLimits

    if (
      acquisitionFieldData.users_count.isDefined
        && (acquisitionFieldData.users_count.get < acquisitionReportOptionalFieldLimits.minOptionalFieldValue
        || acquisitionFieldData.users_count.get > acquisitionReportOptionalFieldLimits.maxUserAndNewUsersCountValue)
    ) {
      Some(
        s"users_count value should be between ${acquisitionReportOptionalFieldLimits.minOptionalFieldValue} and ${acquisitionReportOptionalFieldLimits.maxUserAndNewUsersCountValue}"
      )
    }
    else if (
      acquisitionFieldData.new_users_count.isDefined
        && (acquisitionFieldData.new_users_count.get < acquisitionReportOptionalFieldLimits.minOptionalFieldValue
        || acquisitionFieldData.new_users_count.get > acquisitionReportOptionalFieldLimits.maxUserAndNewUsersCountValue)
    ) {
      Some(
        s"new_users_count value should be between ${acquisitionReportOptionalFieldLimits.minOptionalFieldValue} and ${acquisitionReportOptionalFieldLimits.maxUserAndNewUsersCountValue}"
      )
    }
    else if (
      acquisitionFieldData.demo_delivered.isDefined
        && (acquisitionFieldData.demo_delivered.get < acquisitionReportOptionalFieldLimits.minOptionalFieldValue
        || acquisitionFieldData.demo_delivered.get > acquisitionReportOptionalFieldLimits.maxOptionalFieldValue)
    ) {
      Some(
        s"demo_delivered value should be between ${acquisitionReportOptionalFieldLimits.minOptionalFieldValue} and ${acquisitionReportOptionalFieldLimits.maxOptionalFieldValue}"
      )
    }
    else if (
      acquisitionFieldData.good_fit.isDefined
        && (acquisitionFieldData.good_fit.get < acquisitionReportOptionalFieldLimits.minOptionalFieldValue
        || acquisitionFieldData.good_fit.get > acquisitionReportOptionalFieldLimits.maxOptionalFieldValue)
    ) {
      Some(
        s"good_fit value should be between ${acquisitionReportOptionalFieldLimits.minOptionalFieldValue} and ${acquisitionReportOptionalFieldLimits.maxOptionalFieldValue}"
      )
    }
    else {
      None
    }
  }
}
