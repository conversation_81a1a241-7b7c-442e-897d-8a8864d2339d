package utils.customersupport.services

import org.apache.pekko.actor.ActorSystem
import play.api.libs.ws.WSClient
import utils.SRLogger
import utils.cronjobs.SRCronTrait
import api.AppConfig
import api.billing.v2.BillingAppService
import org.joda.time.{DateTime, DateTimeZone}
import utils.customersupport.dao.{CustomerAcquisitionBillingData, CustomerAcquisitionSRData, InternalCSDReportDAO}
import utils.helpers.LogHelpers

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}


case class CustomerAcquisitionDataCombined(
  trial_started_count: Int,
  num_of_agencies: Int,
  num_of_team_size_greater_than_21: Int,
  num_of_team_size_greater_than_50: Int,
  num_of_new_subs: Int,
  num_of_churn_subs: Int,
  num_of_active_subs: Int,
  new_mrr: Int,
  reactivation_mrr: Int,
  expansion_mrr: Int,
  contraction_mrr: Int,
  churn_mrr: Int,
  start_date: DateTime,
  end_date: DateTime
)


class ScheduleUpdateAcquisitionReportService(
  billingAppService: BillingAppService,
  internalCSDReportDAO: InternalCSDReportDAO
) extends SRCronTrait {

  override val cronName: String = "ScheduleUpdateAcquisitionReportService"
  override val cronIntervalInSeconds: Int = AppConfig.cronUpdateAcquisitionReportAfterInSeconds

  override def executeCron()
    (using logger: SRLogger, system: ActorSystem, ws: WSClient, ec: ExecutionContext): Unit = {

    Try {

      logger.info(s"[ScheduleUpdateAcquisitionReportService] execute intervalSecs: $cronIntervalInSeconds")

      val startDate =
        DateTime.now()
          .minusDays(1)
          .withZone(DateTimeZone.UTC)
          .withTimeAtStartOfDay()

      val endDate = DateTime.now().plusDays(1)

      val acquisitionReportExists: Boolean = internalCSDReportDAO.checkIfAcquisitionReportExists(
        startDate = startDate,
        endDate = endDate
      ) match {
        case Failure(exception) =>
          logger.error("Failed to check if acquisition report exists", err = exception)
          throw exception

        case Success(None) =>
          logger.fatal("checkIfAcquisitionReportExists returned NONE")
          false

        case Success(Some(acquisitionReportExists)) =>
          logger.info(s"Successfully fetched acquisitionReportExists: $acquisitionReportExists")
          acquisitionReportExists
      }

      if (acquisitionReportExists) {
        val successMsg = s"Acquisition Report for $startDate to $endDate already exists."

        logger.info(successMsg)
        Success(successMsg)
      } else {

        scheduleUpdateAcquisitionReport(startDate = startDate, endDate = endDate, logger = logger) match {

          case Failure(e) =>
            logger.error(s"[ScheduleUpdateAcquisitionReportService] error: $e ${if (e.getMessage.contains("No active")) "" else LogHelpers.getStackTraceAsString(e)}")

          case Success(_) =>
            logger.info(s"[ScheduleUpdateAcquisitionReportService] success")

        }
      }
    } match {

      case Failure(e) =>
        logger.error(s"[ScheduleUpdateAcquisitionReportService] final FATAL error: ${LogHelpers.getStackTraceAsString(e)}")

      case Success(_) =>
        logger.info(s"[ScheduleUpdateAcquisitionReportService] final success")

    }

  }

  private final def scheduleUpdateAcquisitionReport(startDate: DateTime, endDate: DateTime, logger: SRLogger)
    (implicit system: ActorSystem, wsClient: WSClient, ec: ExecutionContext): Try[Unit] = Try {

    logger.info(s"[ScheduleUpdateAcquisitionReportService] scheduleAdmitadCommissionsApproval started")

    val customerAcquisitionDataCombinedFutOpt: Future[Option[CustomerAcquisitionDataCombined]] = for {

      customerAcquisitionSRDataOpt: Option[CustomerAcquisitionSRData] <- Future.fromTry {
        internalCSDReportDAO.getCustomerAcquisitionData(
          startDate = startDate,
          endDate = endDate
        )
      }

      customerAcquisitionBillingData: CustomerAcquisitionBillingData <- billingAppService.getBillingAcquisitionData(
        startDate = startDate,
        endDate = endDate
      )(ws = wsClient, ec = ec, logger = logger)

    } yield {

      customerAcquisitionSRDataOpt.map { customerAcquisitionSRData =>
        CustomerAcquisitionDataCombined(
          trial_started_count = customerAcquisitionSRData.trial_started_count,
          num_of_agencies = customerAcquisitionSRData.num_of_agencies,
          num_of_team_size_greater_than_21 = customerAcquisitionSRData.num_of_team_size_greater_than_21,
          num_of_team_size_greater_than_50 = customerAcquisitionSRData.num_of_team_size_greater_than_50,
          num_of_new_subs = customerAcquisitionBillingData.num_of_new_subs,
          num_of_churn_subs = customerAcquisitionBillingData.num_of_churn_subs,
          num_of_active_subs = customerAcquisitionBillingData.num_of_active_subs,
          new_mrr = customerAcquisitionBillingData.new_mrr,
          reactivation_mrr = customerAcquisitionBillingData.reactivation_mrr,
          expansion_mrr = customerAcquisitionBillingData.expansion_mrr,
          contraction_mrr = customerAcquisitionBillingData.contraction_mrr,
          churn_mrr = customerAcquisitionBillingData.churn_mrr,
          start_date = startDate,
          end_date = endDate
        )
      }
    }

    customerAcquisitionDataCombinedFutOpt.map {
      case None =>
        logger.fatal("Customer Acquisition Report Not Found - NONE")

      case Some(customerAcquisitionDataCombined: CustomerAcquisitionDataCombined) =>
        logger.info("Customer Acquisition Update")

        internalCSDReportDAO.createAcquisitionReportEntry(
          customerAcquisitionDataCombined = customerAcquisitionDataCombined
        ) match {
          case Failure(exception) =>
            logger.error("internalCSDReportDAO.createAcquisitionReportEntry - FAILED", err = exception)

          case Success(None) =>
            logger.fatal("Acquisition Id Not Found - None")

          case Success(Some(acquisitionReportId: Long)) =>
            logger.success(s"Created Acquisition Report entry with Id: $acquisitionReportId")
        }
    }
      .recover {
        case e =>
          logger.error(s"customerAcquisitionReportFutOpt FAILED", err = e)
          Failure(e)
      }
  }
}
