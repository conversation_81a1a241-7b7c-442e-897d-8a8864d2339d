package utils.customersupport.services

import org.apache.pekko.actor.ActorSystem
import api.accounts.models.OrgId
import api.campaigns.CampaignDAO
import play.api.libs.ws.WSClient
import utils.SRLogger
import utils.intercom.dao.InternalSRUsageData

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}


case class InternalAdoptionReport(
  internalSRUsageDataWithScore: Map[String, Int],
  internalSRAdoptionScore: Int
)


class InternalAdoptionReportUpdateService(
  campaignDAO: CampaignDAO
) {

  def updateForOrgId(
    orgId: OrgId
  )(
    implicit wsClient: WSClient,
    ec: ExecutionContext,
    logger: SRLogger
  ): Try[Option[Boolean]] = {


    campaignDAO.getInternalSRUsageDataByOrgID(orgId = orgId.id).flatMap {
      case None => Success(None)
      case Some(internalSRUsageData: InternalSRUsageData) =>

        val internalAdoptionReport: InternalAdoptionReport = InternalAdoptionReportUpdateService.getAdoptionReportAndScore(
          internalSRUsageData
        )


        campaignDAO.updateInternalSRUsageData(
          internalAdoptionReport = internalAdoptionReport,
          orgId = orgId
        ).map(_ => Some(true))

    }


  }
}


object InternalAdoptionReportUpdateService {

  def getAdoptionReportAndScore(internalSRUsageData: InternalSRUsageData): InternalAdoptionReport = {

    val internalSRUsageDataWithScore: Map[String, Int] = Map(

      // Not in the report
      //      "using_spam_test" -> {
      //        if (internalSRUsageData.using_spam_test) 7
      //        else 0
      //      },
      //
      //      // Not in the report
      //      "using_ab_testing" -> {
      //        if (internalSRUsageData.using_ab_testing) 6
      //        else 0
      //      },
      //
      //      // Not in the report
      //      "using_merge_tags" -> {
      //        if (internalSRUsageData.using_merge_tags) 4
      //        else 0
      //      },
      //

      //
      //      // Not in the report
      //      "using_custom_column" -> {
      //        if (internalSRUsageData.using_custom_column) 2
      //        else 0
      //      },
      //
      //      // Not in the report
      //      "using_sending_holiday_calendar" -> {
      //        if (internalSRUsageData.using_sending_holiday_calendar) 2
      //        else 0
      //      },


      // Not in the report
      //      "using_campaign_warmup" -> {
      //        if (internalSRUsageData.using_campaign_warmup) 5
      //        else 0
      //      },


      "using_integration_via_zapier" -> {
        if (internalSRUsageData.using_integration_via_zapier) 10
        else 0
      },

      "using_webhooks" -> {
        if (internalSRUsageData.using_webhooks) 10
        else 0
      },

      "using_email_finder" -> {
        if (internalSRUsageData.using_email_finder) 10
        else 0
      },

      "using_prospect_daddy" -> {
        if (internalSRUsageData.using_prospect_daddy) 7
        else 0
      },

      "using_workflow_automation" -> {
        if (internalSRUsageData.using_workflow_automation) 10
        else 0
      },

      "tagging_reply_sentiments" -> {
        if (internalSRUsageData.tagging_reply_sentiments) 5
        else 0
      },

      "using_shared_inbox" -> {
        if (internalSRUsageData.using_shared_inbox) 5
        else 0
      },

      "using_multichannel" -> {
        if (internalSRUsageData.using_multichannel) 10
        else 0
      },

      "soft_start_greater_than_50_percent" -> {
        if (internalSRUsageData.soft_start_greater_than_50_percent) 5
        else 0
      },

      "invited_team_members" -> {
        if (internalSRUsageData.invited_team_members) 2
        else 0
      },

      "using_prospect_categorization" -> {
        if (internalSRUsageData.using_prospect_categorization) 5
        else 0
      },

      "using_spintax" -> {
        if (internalSRUsageData.using_spintax) 5
        else 0
      },

      "signed_up_for_referral" -> {
        if (internalSRUsageData.signed_up_for_referral) 10
        else 0
      },

    )

    val totalScore: Int = {

      val score = internalSRUsageDataWithScore
        .filter {
          case (key, value) =>
            key != "using_integration_via_zapier" && key != "using_webhooks"
        }
        .values
        .sum

      if (
        internalSRUsageDataWithScore("using_integration_via_zapier") != 0
          || internalSRUsageDataWithScore("using_webhooks") != 0
      ) {
        score + 10
      } else {
        score
      }
    }

    InternalAdoptionReport(
      internalSRUsageDataWithScore = internalSRUsageDataWithScore,
      internalSRAdoptionScore = totalScore
    )

  }

}
