package utils.templating

import api.CONSTANTS

import java.util
import api.CONSTANTS.API_MSGS
import api.columns.{InternalMergeTagValuesForProspect, ProspectColumnDefUtils}
import api.prospects.ProspectAccount
import eventframework.ProspectObject
import io.smartreach.esp.utils.EmailHelpers
import liqp.Template
import liqp.TemplateContext
import liqp.filters.Filter
import liqp.nodes.LNode
import liqp.tags.Tag
import org.antlr.runtime.tree.{CommonTree, Tree}
import play.api.Logging
import sr_scheduler.models.ChannelType
import utils.{Help<PERSON>, SRLogger}
import utils.helpers.LogHelpers

import scala.util.{Failure, Success, Try}

class TemplateService(
                       //  prospectAccountDAO: ProspectAccountDAO1,
                     ) extends Logging {

  case class InvalidTemplateSyntaxException(message: String = API_MSGS.INVALID_SYNTAX_IN_EMAIL, cause: Throwable = None.orNull) extends Exception(message, cause)

  case class InvalidMergeTagException(message: String, cause: Throwable = None.orNull) extends Exception(message, cause)

  // register default: checkout Liqp Readme on github
  Filter.registerFilter(new Filter("default") {
    override def apply(value: scala.Any, params: AnyRef*): AnyRef = {
      // if (value != null) value.toString else params.head.toString
      if (value == null || value == "") params.head.toString else value.toString
    }
  })


  /**
   * Added on 16th May 2023: Spintax support
   *
   * {% spin %} Hi there || Hello there  || How are you {% endspin %}
   *
   * will randomly choose one of the options there
   */
  Tag.registerTag(new Tag("spin") {
    def render(context: TemplateContext, nodes: LNode*): Any = {

      val allOptionsStr: String = nodes(1).render(context).toString

      TemplateService.handleSpinSyntax(
          text = allOptionsStr
        )
        .get

    }
  })

  /*
    Example AST:

    '- BLOCK
       |- PLAIN='<p>Hi'
       |- OUTPUT
       |  |- LOOKUP
       |  |  '- Id='first_name'
       |  '- FILTERS
       |     '- FILTER
       |        |- Id='default'
       |        '- PARAMS
       |           '- Str='there'
       |- PLAIN=''
       |- OUTPUT
       |  |- LOOKUP
       |  |  '- Id='last_name'
       |  '- FILTERS
       |- PLAIN=',</p><p>My name is john and I work as a business developer at'
       |- OUTPUT
       |  |- LOOKUP
       |  |  '- Id='company'
       |  '- FILTERS
       '- PLAIN='studio.</p>'
     */

  /*
  def findLookupTokensFromTemplate(template: String): Try[ListBuffer[String]] = Try {

    val ast = Template.parse(template).getAST

    var tokens: ListBuffer[String] = new ListBuffer[String]

    val childCount = ast.getChildCount

    for (i <- 0 until childCount) {
      val child = ast.getChild(i)

      child.getText match {

        case "OUTPUT" =>

          val output = child
          val lookupNode = output.getChild(0)

          val lookupTokenName = lookupNode.getChild(0).getText

          val lookupFilters = output.getChild(1)

          val totalFilters = lookupFilters.getChildCount


          totalFilters match {

            case 0 =>
              tokens += lookupTokenName

            case 1 =>
              val filter = lookupFilters.getChild(0)

              if (filter.getChildCount != 2 || (filter.getChild(0).getText != "default")) {

                throw new Exception("Invalid template filter")

              } else {

                val filterParam = filter.getChild(1).getChild(0)


                // param must be a double, long or string
                if (!Vector(33, 64, 91).contains(filterParam.getType)) {

                  throw new Exception("Invalid template params")

                } else {

                  // only default filters allowed

                  val defaultValue = filterParam.getText

                  // allow empty "" default values too
                  if (defaultValue == null) {

                    tokens += lookupTokenName

                  }


                }

              }

            case _ =>
              throw new Exception(s"Max one filter allowed :: $totalFilters")

          }


        case _ =>
        // ignore this case

      }

    }

    tokens
  }
  */


  // NOTE: ALWAYS USE THIS PARSE TEMPLATE STRINGS
  // SOMETIMES THERE ARE INVISIBLE UNICODE CONTROL CHARACTERS IN USERS THAT SCREW THE LIQP PARSER
  private def __parseTemplate(template: String): Template = {

    Try {
      val cleanedTemplate = EmailHelpers.removeInvisibleCharactersFromTemplate(txt = template)

      Template.parse(cleanedTemplate)
    } match {
      case Failure(e) =>

        logger.error(s"__parseTemplate: ${e.getMessage} :::::::bLA:: ${LogHelpers.getStackTraceAsString(e)}")

        if (
          e.getMessage != null &&
            (
              e.getMessage.toLowerCase.contains("unwantedtokenexception") ||
                e.getMessage.toLowerCase.contains("missingtokenexception") ||
                e.getMessage.toLowerCase.contains("mismatchedtokenexception")

              )
        ) {

          throw new Exception(CONSTANTS.API_MSGS.INVALID_SYNTAX_IN_EMAIL)

        } else {

          logger.error(s"FATAL __parseTemplate New error: template: $template ::: errstack: ${LogHelpers.getStackTraceAsString(e)}")

          throw e

        }


      case Success(t) =>
        t
    }
  }

  private def __getParamsForCheckingMissingMergeTag(template: String): Try[Seq[String]] = Try {

    val ast = __parseTemplate(template = template).getAST


    /*
    '- BLOCK
      |- PLAIN='Hi'
      |- OUTPUT
      |  |- LOOKUP
      |  |  '- Id='place1'
      |  '- FILTERS
      |     '- FILTER
      |        |- Id='default'
      |        '- PARAMS
      |           '- Str='fdsafdsa'
    */
    (0 until ast.getChildCount)
      .map(i => {
        ast.getChild(i)
      })
      .filter(node => {
        // we take only first-level OUTPUT nodes
        // we dont track Missing merge-tag-error if its inside a if/else block and so on (non-first-level nodes are ignored)
        node.getText == "OUTPUT"
      })
      .filter(outputNode => {

        // output node should have a Lookup node (c1) with the param
        // and a Filters node (c2) with each filter (c2_j) applied on the param
        // one of these filters (c2_j) might be a "default" filter

        val c1 = outputNode.getChild(0)

        val foundDefaultFilter = if (c1 == null || c1.getText != "LOOKUP") false else {

          val c2 = outputNode.getChild(1)

          if (c2 == null || c2.getText != "FILTERS" || c2.getChildCount == 0) false else {

            // one of the filters should be a default filter
            val hasDefaultFilter = (0 until c2.getChildCount)
              .exists(j => {
                val c2_j = c2.getChild(j)

                c2_j != null && c2_j.getChild(0).getText == "default"
              })

            hasDefaultFilter
          }
        }

        !foundDefaultFilter
      })
      .map(filteredOutputNode => {
        filteredOutputNode
          .getChild(0) // LOOKUP node
          .getChild(0) // id value of LOOKUP node (param name)
          .getText
      })
      .distinct
  }


  private def __getLookupsForTree(ast: Tree): Seq[String] = {

    val childCount = ast.getChildCount

    (0 until childCount)
      .flatMap(i => {
        val c = ast.getChild(i)

        if (c.getText == "LOOKUP") {
          Seq(c.getChild(0).getText)
        } else {

          if (c.getChildCount != 0) {
            __getLookupsForTree(ast = c)
          } else {
            Seq[String]()
          }
        }
      })
      .distinct
  }

  private def __getAllParamsUsedInTemplate(templateAst: CommonTree): Try[Seq[String]] = Try {
    __getLookupsForTree(ast = templateAst)
  }


  // NOTE: __getAllParamsUsedInTemplate: does not catch invalid filters: those are caught only by the render method
  //
  // e.g Hi {{first_name| fdsafsa: 'fdafsd'}},
  // isValid wont catch the 'fdsafsa' filter in this
  // However, ast.render will catch those
  def isValid(template: String, availableTagNames: Seq[String], channel: ChannelType): Try[Boolean] = Try {

    val t = __parseTemplate(template)

    __getAllParamsUsedInTemplate(templateAst = t.getAST) match {

      case Failure(e) =>

        logger.error(s"TemplateService.isValid FATAL error: ${e.getMessage} :: template: $template :: availableTagNames: $availableTagNames :: full error: ${LogHelpers.getStackTraceAsString(e)}")

        throw new InvalidTemplateSyntaxException(API_MSGS.INVALID_SYNTAX_IN_EMAIL)

      case Success(tagNamesToCheck) =>

        val absentTag = tagNamesToCheck.filter(t => !availableTagNames.contains(t))

        if (absentTag.nonEmpty) {

          throw new InvalidMergeTagException(s"Invalid merge-tags: ${absentTag.mkString(", ")}")

          false

        } else {

          val input = ProspectColumnDefUtils.getKVMapForDummyProspectTemplate(availableTagNames = availableTagNames, channel = channel)
          Try {
            t.render(input)
          } match {

            case Failure(e) =>

              logger.error(s"TemplateService.isValid render FATAL error: ${e.getMessage} :: template: $template :: availableTagNames: $availableTagNames :: full error: ${LogHelpers.getStackTraceAsString(e)}")

              throw new InvalidTemplateSyntaxException(API_MSGS.INVALID_SYNTAX_IN_EMAIL)

            case Success(_) =>
              true
          }
        }
    }
  }


  def getOverallMergeTags(prospect: ProspectObject,
                          internalMergeTags: InternalMergeTagValuesForProspect,
                          channel: ChannelType
                         )(using logger: SRLogger): util.HashMap[java.lang.String, Object] = {

    val prospectTags = ProspectColumnDefUtils.getKVMapForProspectTemplate(
      prospect = prospect,
      internalMergeTags = internalMergeTags,
      channel = channel
    )

    //    if(prospect.internal.prospect_account_id.isDefined) {
    //
    //      val prospectAccount = prospectAccountDAO.find(id = prospect.id, teamId = prospect.team_id)
    //      val accountTags = ProspectAccountsColumnDef.getKVMapForProspectAccountTemplate(
    //        prospectAccount = prospectAccount
    //      )
    //
    //      prospectTags.putAll(accountTags)
    //Comented out because we are not providing these tags
    //
    //    }

    prospectTags

  }

  def checkMissingMergeTags(
                             bodyTemplate: String,
                             subjectTemplate: String,
                             prospect: ProspectObject,
                             internalMergeTags: InternalMergeTagValuesForProspect,
                             channel: ChannelType,
                           )(using logger: SRLogger): Try[Seq[String]] = Try {

    val input = getOverallMergeTags(
      prospect = prospect,
      internalMergeTags = internalMergeTags,
      channel = channel
    )

    val errMsg = API_MSGS.INVALID_SYNTAX_IN_EMAIL
    __getParamsForCheckingMissingMergeTag(template = bodyTemplate) match {


      case Failure(e) =>

        logger.error(s"FATAL checkMissingMergeTags error b: bodyTemplate: $bodyTemplate :: subjectTemplate: $subjectTemplate :: p: $prospect :: i: $internalMergeTags :: e: ${LogHelpers.getStackTraceAsString(e)}")

        throw new InvalidTemplateSyntaxException(errMsg)


      case Success(bodyParams) =>
        __getParamsForCheckingMissingMergeTag(template = subjectTemplate) match {


          case Failure(e1) =>

            logger.error(s"FATAL checkMissingMergeTags error sub: bodyTemplate: $bodyTemplate :: subjectTemplate: $subjectTemplate :: p: $prospect :: i: $internalMergeTags :: e: ${LogHelpers.getStackTraceAsString(e1)}")

            throw new InvalidTemplateSyntaxException(errMsg)

          case Success(subjectParams) =>

            val tagNamesToCheck: Seq[String] = (bodyParams ++ subjectParams).distinct

            tagNamesToCheck.filter(tag => {

              val isAbsent = !input.containsKey(tag) || input.getOrDefault(tag, null) == null

              //      Logger.info(s"checking $tag : $isAbsent:: ${input.containsKey(tag)} :: ${input.getOrDefault(tag, null)}")

              isAbsent
            })
        }


    }

  }


  def render(
              template: String,
              prospect: ProspectObject,
              internalMergeTags: InternalMergeTagValuesForProspect,
              channel: ChannelType
            )(using logger: SRLogger): Try[String] = Try {

    val input = getOverallMergeTags(
      prospect = prospect,
      internalMergeTags = internalMergeTags,
      channel = channel
    )

    val renderedTemplate: String = __parseTemplate(template)
      //      .withRenderSettings(new RenderSettings.Builder().withStrictVariables(true).build())
      .render(input)

    renderedTemplate
  }

}

object TemplateService {

  def getSpinOptions(text: String): Try[List[String]] = {

    val spinSeparator: String = "\\|"

    val allOptions = text.split(spinSeparator).map(_.trim).toList

    println(s"\n\nallOptions: $allOptions\n\n")

    val hasEmptyOption = allOptions.exists(_.trim.isEmpty)


    if (hasEmptyOption) {

      Failure(new Exception("You have an empty text in one of the spin texts"))

    } else {

      Success(allOptions)

    }

  }

  def handleSpinSyntax(text: String): Try[String] = {

    getSpinOptions(text = text)
      .map(options => {

        Helpers.getRandomElement(options)

      })


  }

}
