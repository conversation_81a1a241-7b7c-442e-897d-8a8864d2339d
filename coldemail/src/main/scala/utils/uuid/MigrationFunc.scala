package utils.uuid

import api.accounts.TeamId
import api.campaigns.services.CampaignId
import api.prospects.ProspectAccountDAO1
import api.prospects.models.ProspectId
import org.joda.time.DateTime
import io.sr.billing_common.models.PlanType
import play.api.libs.json.{<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>s<PERSON><PERSON><PERSON>, JsString, JsSuccess, JsValue}
import scalikejdbc.interpolation.SQLSyntax
import scalikejdbc.{DB, DBSession, scalikejdbcSQLInterpolationImplicitDef}
import utils.SRLogger
import utils.dbutils.DBUtils
import utils.mq.CampaignIdMigrationData

import scala.annotation.tailrec
import scala.util.{Failure, Success, Try}


sealed trait TableNames{

  def toString: String

  def toSQL: SQLSyntax

}

object TableNames {

  private val accounts = "accounts"
  private val teams = "teams"
  private val campaigns = "campaigns"
  private val linkedin_message_threads = "linkedin_message_threads"
  private val email_threads = "email_threads"
  private val emails_scheduled = "emails_scheduled"
  private val tags = "tags"
  private val tags_list_for_campaigns = "tags_list_for_campaigns"
  private val linkedin_messages = "linkedin_messages"
  private val prospects = "prospects"
  private val blacklist = "blacklist"
  private val email_settings = "email_settings"
  private val organizations = "organizations"
  private val prospect_accounts = "prospect_accounts"
  private val campaigns_prospects = "campaigns_prospects"


  case object Accounts extends TableNames {
    override def toString: String = accounts

    override def toSQL: SQLSyntax = {
      sqls"""
           accounts
           """
    }
  }

  case object Teams extends TableNames {
    override def toString: String = teams

    override def toSQL: SQLSyntax =
      sqls"""
           teams
           """
  }


  case object Campaigns extends TableNames {
    override def toString: String = campaigns

    override def toSQL: SQLSyntax =
      sqls"""
           campaigns
           """
  }

  case object LinkedinMessageThreads extends TableNames {
    override def toString: String = linkedin_message_threads

    override def toSQL: SQLSyntax =
      sqls"""
           linkedin_message_threads
           """
  }

  case object CampaignsProspects extends TableNames {
    override def toString: String = campaigns_prospects

    override def toSQL: SQLSyntax =
      sqls"""
            campaigns_prospects
            """
  }

  case object EmailThreads extends TableNames {
    override def toString: String = email_threads

    override def toSQL: SQLSyntax =
      sqls"""
           email_threads
           """
  }

  case object EmailsScheduled extends TableNames {
    override def toString: String = emails_scheduled

    override def toSQL: SQLSyntax = {
      sqls"""
           emails_scheduled
           """
    }
  }

  case object Tags extends TableNames {
    override def toString: String = tags

    override def toSQL: SQLSyntax = {
      sqls"""
           tags
           """
    }
  }

  case object TagsListForCampaigns extends TableNames {
    override def toString: String = tags_list_for_campaigns

    override def toSQL: SQLSyntax = {
      sqls"""
           tags_list_for_campaigns
           """
    }
  }

  case object LinkedinMessages extends TableNames {
    override def toString: String = linkedin_messages

    override def toSQL: SQLSyntax = {
      sqls"""
           linkedin_messages
           """
    }
  }

  case object Prospects extends TableNames {
    override def toString: String = prospects

    override def toSQL: SQLSyntax = {
      sqls"""
           prospects
           """
    }
  }

  case object Blacklist extends TableNames {
    override def toString: String = blacklist

    override def toSQL: SQLSyntax = {
      sqls"""
            blacklist
          """
    }
  }

  case object EmailSettings extends TableNames {
    override def toString: String = email_settings

    override def toSQL: SQLSyntax = {
      sqls"""
            email_settings
          """
    }
  }

  case object Organizations extends TableNames {
    override def toString: String = organizations

    override def toSQL: SQLSyntax = {
      sqls"""
            organizations
          """
    }
  }

  case object ProspectAccounts extends TableNames {
    override def toString: String = prospect_accounts

    override def toSQL: SQLSyntax = {
      sqls"""
            prospect_accounts
          """
    }
  }

  def fromString(key: String): Try[TableNames] = Try{

    key match {
      case `accounts` => Accounts
      case `teams` => Teams
      case `campaigns` => Campaigns
      case `linkedin_message_threads` => LinkedinMessageThreads
      case `email_threads` => EmailThreads
      case `emails_scheduled` => EmailsScheduled
      case `linkedin_messages` => LinkedinMessages
      case `tags_list_for_campaigns` => TagsListForCampaigns
      case `tags` => Tags
      case `blacklist` => Blacklist
      case `email_settings` => EmailSettings
      case `organizations` => Organizations
      case `prospect_accounts` => ProspectAccounts
    }
  }

  given format: Format[TableNames] = new Format[TableNames] {
    override def writes(o: TableNames): JsValue = {
      JsString(o.toString)
    }

    override def reads(json: JsValue): JsResult[TableNames] = {
      fromString(json.as[String]) match {
        case Success(value) => JsSuccess(value = value)
        case Failure(_) => JsError(s"Invalid table name :: $json")

      }
    }
  }

}

case class IdUuid(
                   id: Long,
                   uuid: String
                 )

case class IdUpdatedAt(
                        id: Long,
                        updatedAt: DateTime
                      )

case class IdTeamId(
                   id: Long,
                   teamId: Option[Long]
                   )


case class CampaignIdProspectId(
  campaignId: CampaignId,
  prospectId: ProspectId
)


class MigrationFunc (
                    srUuidUtils: SrUuidUtils,
                    dbUtils: DBUtils,
                    prospectAccountDAO: ProspectAccountDAO1
                    ) {

  def selectIdWithoutUuid(
                           table_name: TableNames,
                           teamId: Option[TeamId]
                         )(implicit session: DBSession): Try[Seq[IdTeamId]] = Try {

      val whereQry = whereClause(
        teamId = teamId
      )

      sql"""
           ${getSelectQuery(table_name = table_name)}
           FROM
           ${table_name.toSQL}
           WHERE uuid is NULL
           ${whereQry}
            LIMIT 1000
            FOR NO KEY UPDATE
            SKIP LOCKED
           """
        .map(rs => {
          IdTeamId(
            id = rs.long("id"),
            teamId = if(table_name == TableNames.Campaigns)
              {

                //we only need the team id for campaign uuid generation
                //and not for teams and accounts
                rs.longOpt("team_id")
              }
              else{
              None
            }
          )
        })
        .list
        .apply()

  }

  def selectIdWithoutUpdatedAt(
                           table_name: TableNames,
                           teamId: Option[TeamId]
                         )(implicit session: DBSession): Try[Seq[IdTeamId]] = Try {

    val whereQry = whereClause(
      teamId = teamId
    )

    sql"""
           ${getSelectQuery(table_name = table_name)}
           FROM
           ${table_name.toSQL}
           WHERE updated_at is NULL
           ${whereQry}
            LIMIT 1000
            FOR NO KEY UPDATE
            SKIP LOCKED
           """
      .map(rs => {
        IdTeamId(
          id = rs.long("id"),
          teamId = if (table_name == TableNames.Campaigns) {

            //we only need the team id for campaign updated_at generation
            //and not for teams and accounts
            rs.longOpt("team_id")
          }
          else {
            None
          }
        )
      })
      .list
      .apply()

  }

  def selectIdWithoutTeamId(
    //table_name: TableNames,
    campaignId: CampaignId
  )(implicit session: DBSession): Try[Seq[CampaignIdProspectId]] = Try {


    sql"""
           SELECT campaign_id, prospect_id
           FROM
           campaigns_prospects
           WHERE team_id is NULL
           and campaign_id = ${campaignId.id}
            FOR NO KEY UPDATE
            SKIP LOCKED
            LIMIT 1000
           """
      .map(rs => {
        CampaignIdProspectId(
          campaignId = CampaignId(rs.long("campaign_id")),
          prospectId = ProspectId(rs.long("prospect_id"))
        )
      })
      .list
      .apply()

  }


  def whereClause(
                 teamId: Option[TeamId]
                 ): SQLSyntax  ={
    teamId match {
      case Some(tid) =>
        sqls"""
          AND team_id = ${tid.id}
            """
      case None => sqls""
    }
  }



  def getSelectQuery(table_name: TableNames): SQLSyntax = {
    if (table_name == TableNames.Campaigns) {
      sqls"""
           SELECT id, team_id
         """
    }
    else {
      sqls"""
           SELECT id
         """
    }
  }

  def getUuidFunction(table_name: TableNames, data: IdTeamId): String = {

    table_name match {
      case TableNames.Accounts => srUuidUtils.generateAccountUuid()
      case TableNames.Teams => srUuidUtils.generateTeamUuid()
      case TableNames.Campaigns => srUuidUtils.generateCampaignUuid()
      case TableNames.LinkedinMessageThreads => srUuidUtils.generateLinkedinMessageThreadUuid()
      case TableNames.EmailThreads => srUuidUtils.generateEmailThreadsUuid()
      case TableNames.EmailsScheduled => srUuidUtils.generateEmailsScheduledUuid()
      case TableNames.Tags => srUuidUtils.generateTagsUuid()
      case TableNames.TagsListForCampaigns => srUuidUtils.generateTagsUuid()
      case TableNames.LinkedinMessages => srUuidUtils.generateLinkedinMessagesUuid()
      case TableNames.Prospects => srUuidUtils.generateProspectUuid()
      case TableNames.Blacklist => srUuidUtils.generateBlacklistUuid()
      case TableNames.EmailSettings => srUuidUtils.generateEmailSettingUuid()
      case TableNames.Organizations => srUuidUtils.generateOrgUuid()
      case TableNames.ProspectAccounts => srUuidUtils.generateProspectAccountUuid()
      case TableNames.CampaignsProspects => throw new Throwable(s"Wrong table type CampaignsProspects")
    }

  }

  def getUpdatedAt(table_name: TableNames, ids: Seq[IdTeamId],teamId: Option[TeamId])(implicit session: DBSession): List[IdUpdatedAt] = {
    table_name match {
      case TableNames.ProspectAccounts => prospectAccountDAO.getCreatedAt(ids.map(idTeamId => idTeamId.id),teamId = teamId)
      case _ => List()
    }
  }

  def updateQuery(
                   table_name: TableNames,
                   idUuids: Seq[IdUuid],
                   team_id: Option[TeamId]
                 )(implicit session: DBSession,
                   logger: SRLogger): Try[List[Long]] = Try {

    val team_id_check: SQLSyntax = if (team_id.isDefined) {
      sqls" AND tb.team_id = ?"
    } else {
      sqls""
    }

    var valueParameters = List[Any]()

    val valuePlaceholder: SQLSyntax = idUuids
      .map(idUuid => {

        valueParameters = valueParameters ::: List(

          idUuid.uuid,
          idUuid.id
        )

        sqls"""
             (
                ?,
                ?
              )
            """

      })
      .reduce((vp1, vp2) => sqls"$vp1, $vp2")

    if (team_id.isDefined) {
      valueParameters = valueParameters ::: List(team_id.get.id)
    }

    sql"""
             UPDATE
             ${table_name.toSQL} tb
             SET
             uuid = temp.uuid
             FROM (
                VALUES $valuePlaceholder
              )
              AS temp(
                uuid,
                id
              )
             WHERE
             tb.id = temp.id
             ${team_id_check}
             RETURNING temp.id;
           """
      .bind(valueParameters*)
      .map(_.long("id"))
      .list
      .apply()


  }

  def runUpdateQuery(
                             table_name: TableNames,
                             idUuids: Seq[IdUuid],
                             team_id: Option[TeamId]
                           )(implicit session: DBSession,
                             logger: SRLogger):List[Long] = {

      for {
        updated_id <- updateQuery(
          table_name = table_name,
          idUuids = idUuids,
          team_id = team_id
        )
      } yield {
        updated_id
      }

  }.get

  def updateQueryForUpdatedAt(
                               table_name: TableNames,
                               idUpdatedAtMap: Seq[IdUpdatedAt],
                               team_id: Option[TeamId]
                             )(implicit session: DBSession,
                               logger: SRLogger): Try[List[Long]] = Try {
    val team_id_check: SQLSyntax = if (team_id.isDefined) {
      sqls" AND tb.team_id = ?"
    } else {
      sqls""
    }

    var valueParameters = List[Any]()
    val valuePlaceholder: SQLSyntax = idUpdatedAtMap
      .map(idUpdatedAt => {

        valueParameters = valueParameters ::: List(

          idUpdatedAt.updatedAt,
          idUpdatedAt.id
        )

        sqls"""
           (
              ?::timestamptz,
              ?
            )
          """

      })
      .reduce((vp1, vp2) => sqls"$vp1, $vp2")

    if (team_id.isDefined) {
      valueParameters = valueParameters ::: List(team_id.get.id)
    }

    sql"""
             UPDATE
             ${table_name.toSQL} tb
             SET
             updated_at = temp.updated_at
             FROM (
                VALUES $valuePlaceholder
              )
              AS temp(
                updated_at,
                id
              )
             WHERE
             tb.id = temp.id
             ${team_id_check}
             RETURNING temp.id;
           """
      .bind(valueParameters*)
      .map(_.long("id"))
      .list
      .apply()

  }
  def runQueryForUpdatedAt(
                            table_name: TableNames,
                            idUpdatedAtMap: Seq[IdUpdatedAt],
                            team_id: Option[TeamId]
                          )(implicit session: DBSession,
                            logger: SRLogger): List[Long] = {

    for {
      updated_id <- updateQueryForUpdatedAt(
        table_name = table_name,
        idUpdatedAtMap = idUpdatedAtMap,
        team_id = team_id
      )
    } yield {
      updated_id
    }

  }.get


  def updateQueryWithCampaignId(
    // table_name: String,
    idUuids: Seq[CampaignIdProspectId],
    campaignId: CampaignId,
    teamId: TeamId
  )(implicit session: DBSession): Try[List[CampaignIdProspectId]] = Try {



//    var valueParameters = List[Any]()
//
//    val valuePlaceholder: SQLSyntax = idUuids
//      .map(idUuid => {
//
//        valueParameters = valueParameters ::: List(
//
//          idUuid.campaignId,
//          idUuid.prospectId,
//          teamId.id
//        )
//
//        sqls"""
//             (
//                ?,
//                ?,
//                ?
//              )
//            """
//
//      })
//      .reduce((vp1, vp2) => sqls"$vp1, $vp2")


    sql"""
           UPDATE
           campaigns_prospects tb
           SET
           team_id = ${teamId.id}
           WHERE
           tb.prospect_id in (${idUuids.map(_.prospectId.id)})
           AND tb.campaign_id = ${campaignId.id}
           RETURNING tb.prospect_id, tb.campaign_id;
         """
//      .bind(valueParameters*)
      .map(rs =>
        CampaignIdProspectId(
          campaignId = CampaignId(rs.long("campaign_id")),
          prospectId = ProspectId(rs.long( "prospect_id")),
        )
      )
      .list
      .apply()
  }

  /*
  def runUpdateQueryForCmapampaignId(
    table_name: TableNames,
    idUuids: Seq[CampaignIdProspectId],
    campaign_id: CampaignId
  )(implicit session: DBSession): List[CampaignIdProspectId] = {

    for {
      updated_id <- updateQueryWithCampaignId(
        table_name = table_name,
        idUuids = idUuids,
        campaignId = campaign_id
      )
    } yield {
      updated_id
    }

  }.get
  */

  def getIdUuidMap(ids : Seq[IdTeamId], table_name: TableNames):Seq[IdUuid]= {
    ids.map(data => {
      IdUuid(
        id = data.id,
        uuid = getUuidFunction(table_name = table_name, data = data)
      )
    })
  }

  def getIdUpdatedAtMap(ids: Seq[IdTeamId], table_name: TableNames,teamId: Option[TeamId])(implicit session: DBSession): Seq[IdUpdatedAt] = {
     getUpdatedAt(table_name = table_name, ids = ids, teamId = teamId)
  }

  def whereClauseToPushTeamToQueue(active_teams: Boolean): SQLSyntax = {
    if (active_teams)
      sqls"""
          NOT o.plan_type = ${PlanType.INACTIVE.toString}
           """
    else
      sqls"""
            o.plan_type = ${PlanType.INACTIVE.toString}
          """
  }



  def getTeamIdsInTable(tableNames: TableNames, fetchAllTeams: Boolean = false, active_teams: Boolean): Try[List[TeamId]] = Try {
    DB readOnly { implicit session =>

      val whereClause = if (fetchAllTeams) {
        sqls""
      } else {
        sqls"WHERE ${whereClauseToPushTeamToQueue(active_teams = active_teams)}"
      }

      sql"""
           SELECT t.id AS team_id from teams t
           INNER JOIN organizations o ON t.org_id = o.id
           ${whereClause}

           ORDER BY
           CASE o.plan_type
            WHEN ${PlanType.PAID.toString} then 1
            WHEN ${PlanType.TRIAL.toString} then 2
            WHEN ${PlanType.INACTIVE.toString} then 3
           end ;
           """
        .map(rs => TeamId(rs.long("team_id")))
        .list
        .apply()
    }
  }



  def getCampaignIdsInTable(
    //tableNames: TableNames,
    active_teams: Boolean): Try[List[CampaignIdMigrationData]] = Try {
    DB readOnly { implicit session =>

      val whereClause = whereClauseToPushTeamToQueue(active_teams = active_teams)

      sql"""
           SELECT c.id as campaign_id, c.team_id  from campaigns c
           inner join teams t on t.id = c.team_id
           INNER JOIN organizations o ON t.org_id = o.id
           WHERE
           ${whereClause}

           ORDER BY
           CASE o.plan_type
            WHEN ${PlanType.PAID.toString} then 1
            WHEN ${PlanType.TRIAL.toString} then 2
            WHEN ${PlanType.INACTIVE.toString} then 3
           end ;
           """
        .map(rs =>
          CampaignIdMigrationData(
            campaignId = CampaignId(rs.long("campaign_id")),
            teamId = TeamId(rs.long("team_id"))
          )
        )
        .list
        .apply()
    }
  }

  def checkIfTeamIdIsNullForCampaign(
                                     campaign_id: CampaignId
                                    ): Try[Boolean] = Try{
    DB readOnly { implicit session =>

      sql"""
           SELECT EXISTS (
              SELECT
              campaign_id
              FROM
              campaigns_prospects WHERE
              campaign_id = ${campaign_id.id}
              AND
              team_id is NULL
              LIMIT 1
            );
         """
        .map(rs => rs.boolean("exists"))
        .single
        .apply()
        .get
    }
  }

  def checkIfUuidsAreNullForTeam(teamId: TeamId, tableNames: TableNames): Try[Boolean] = Try {

    DB readOnly {implicit session =>
      sql"""
           SELECT EXISTS(
              SELECT id FROM ${tableNames.toSQL}
              WHERE uuid IS NULL
              AND team_id = ${teamId.id}
           )
           ;
           """
        .map(rs => rs.boolean("exists"))
        .single
        .apply()
        .get

    }
  }

  def checkIfUpdatedAtNullForTeam(teamId: TeamId, tableNames: TableNames): Try[Boolean] = Try {

    DB readOnly { implicit session =>
      sql"""
           SELECT EXISTS(
              SELECT id FROM ${tableNames.toSQL}
              WHERE updated_at IS NULL
              AND team_id = ${teamId.id}
           )
           ;
           """
        .map(rs => rs.boolean("exists"))
        .single
        .apply()
        .get

    }
  }

  def addUuidsToTable(table_name: TableNames, teamId: Option[TeamId])(using logger: SRLogger): Try[Int] = {

    var total_ids_updated: Int = 0

    @tailrec
    def migrationScript(table_name: TableNames, teamId: Option[TeamId]): Try[Seq[Long]] = {
      val dbAndSession = dbUtils.startLocalTx()

      val db = dbAndSession.db
      implicit val session = dbAndSession.session

      if(teamId.isDefined){
        logger.info(s"UUID migration script started for table ${table_name} and team_id ${teamId}")
      } else {
        logger.info(s"UUID migration script started for table ${table_name}")
      }

      val res = for {
        idsToUpdate <- selectIdWithoutUuid(table_name = table_name, teamId = teamId)

        updatedIds <- Try {
          idsToUpdate
            .grouped(100)
            .flatMap(idGroup => {
              val idUuidMap = getIdUuidMap(ids = idGroup, table_name = table_name)
              val updatedIds = runUpdateQuery(idUuids = idUuidMap, table_name = table_name, team_id = teamId)
              updatedIds
            }).toSeq
        }
      } yield {

        updatedIds

      }
      dbUtils.commitAndCloseSession(db = db)
      res match {

        case Failure(exception) => Failure(exception = exception)

        case Success(seq) => {
          if (teamId.isDefined) {
            logger.info(s"UUID migration script ended for table ${table_name} , team_id ${teamId} total updated ids: ${seq.length}")
          } else {
            logger.info(s"UUID migration script ended for table ${table_name} and total updated ids: ${seq.length}")
          }

          if(seq.isEmpty){
            Success(Seq())
          }
          else{
            logger.info(s"Table name :: ${table_name.toString} total updated ids: ${seq.length}")
            total_ids_updated= total_ids_updated+seq.length
            migrationScript(table_name = table_name, teamId = teamId)
          }
        }
      }

    }

    val res = migrationScript(table_name = table_name, teamId = teamId)

    res match {

      case Failure(exception) => Failure(exception = exception)

      case Success(_) => Success(total_ids_updated)
    }

  }

  def addUpdatedAtToTable(table_name: TableNames, teamId: Option[TeamId])(using logger: SRLogger): Try[Int] = {

    var total_ids_updated: Int = 0

    @tailrec
    def migrationScript(table_name: TableNames, teamId: Option[TeamId]): Try[Seq[Long]] = {
      val dbAndSession = dbUtils.startLocalTx()

      val db = dbAndSession.db
      implicit val session = dbAndSession.session

      if (teamId.isDefined) {
        logger.info(s"UpdatedAt migration script started for table ${table_name} and team_id ${teamId}")
      } else {
        logger.info(s"UpdatedAt migration script started for table ${table_name}")
      }

      val res = for {
        idsToUpdate <- selectIdWithoutUpdatedAt(table_name = table_name, teamId = teamId)

        updatedIds <- Try {
          idsToUpdate
            .grouped(100)
            .flatMap(idGroup => {
              val idUpdatedAtMap = getIdUpdatedAtMap(ids = idGroup, table_name = table_name, teamId = teamId)
              val updatedIds = runQueryForUpdatedAt(idUpdatedAtMap = idUpdatedAtMap, table_name = table_name, team_id = teamId)
              updatedIds
            }).toSeq
        }
      } yield {

        updatedIds

      }
      dbUtils.commitAndCloseSession(db = db)
      res match {

        case Failure(exception) => Failure(exception = exception)

        case Success(seq) => {
          if (teamId.isDefined) {
            logger.info(s"UpdatedAt migration script ended for table ${table_name} , team_id ${teamId} total updated ids: ${seq.length}")
          } else {
            logger.info(s"UpdatedAt migration script ended for table ${table_name} and total updated ids: ${seq.length}")
          }

          if (seq.isEmpty) {
            Success(Seq())
          }
          else {
            logger.info(s"Table name :: ${table_name.toString} total updated ids: ${seq.length}")
            total_ids_updated = total_ids_updated + seq.length
            migrationScript(table_name = table_name, teamId = teamId)
          }
        }
      }

    }

    val res = migrationScript(table_name = table_name, teamId = teamId)

    res match {

      case Failure(exception) => Failure(exception = exception)

      case Success(_) => Success(total_ids_updated)
    }

  }

  def addTeamIdToTable(
    //table_name: TableNames,
    //campaignId: CampaignId,
    campaignIdMigrationData: CampaignIdMigrationData
  )
    (using logger: SRLogger): Try[Int] = {

    val campaignId = campaignIdMigrationData.campaignId

    var total_ids_updated: Int = 0

    @tailrec
    def migrationScript(/*table_name: String,*/ campaignIdMigrationData: CampaignIdMigrationData): Try[Seq[Long]] = {
      val dbAndSession = dbUtils.startLocalTx()

      val db = dbAndSession.db
      implicit val session = dbAndSession.session

//      if (teamId.isDefined) {
//        logger.info(s"UUID migration script started for table ${table_name} and team_id ${teamId}")
//      } else {
//        logger.info(s"UUID migration script started for table ${table_name}")
//      }

      val res = for {
        campaignIdProspectIdsToUpdate: Seq[CampaignIdProspectId] <- selectIdWithoutTeamId(
          campaignId = campaignIdMigrationData.campaignId)

        updatedIds <- Try {
          campaignIdProspectIdsToUpdate
            .grouped(100)
            .map(idGroup => {
              //val idUuidMap = getIdUuidMap(ids = idGroup, table_name = table_name)
              val updatedIds = updateQueryWithCampaignId(
                idUuids = idGroup,
                //table_name = table_name,
                campaignId = campaignIdMigrationData.campaignId,
                teamId = campaignIdMigrationData.teamId
              )
              updatedIds
            }).toSeq
        }
      } yield {

        updatedIds

      }
      dbUtils.commitAndCloseSession(db = db)
      res match {

        case Failure(exception) => Failure(exception = exception)

        case Success(seq) => {

          logger.info(s"UUID migration script ended for table campaigns_prospects , campaignId ${campaignId} total updated ids: ${seq.length}")

          if (seq.isEmpty) {
            Success(Seq())
          }
          else {
            //logger.info(s"Table name :: ${table_name.toString} total updated ids: ${seq.length}")
            logger.info(s"Table name :: campaigns_prospects total updated ids: ${seq.length}")
            total_ids_updated = total_ids_updated + seq.length
            migrationScript(/*table_name = table_name,*/ campaignIdMigrationData = campaignIdMigrationData)
          }
        }
      }
    }

    val res = migrationScript(/*table_name = "campaigns_prospects",*/ campaignIdMigrationData = campaignIdMigrationData)

    res match {

      case Failure(exception) => Failure(exception = exception)

      case Success(_) => Success(total_ids_updated)
    }

  }


}