package utils.uuid.services

import api.{CONSTANTS, CacheServiceJedis}
import utils.SRLogger
import utils.helpers.LogHelpers
import utils.uuid.dao.{SrUuidCache, SrUuidDao, SrUuidDbTable}
import utils.uuid.models.SrTenantId

import scala.util.{Failure, Success, Try}


class SrUuidInternalDataService(
                                 protected val cacheServiceJedis: CacheServiceJedis
                               )
  extends SrUuidDao
    with SrUuidCache {


  private def getUuidFromDbAndSetInCache(

                                          internalId: Long,
                                          tenantId: Option[SrTenantId],
                                          dbTable: SrUuidDbTable,

                                        )(
                                          implicit logger: SRLogger
                                        ): Try[String] = {

    getUuidFromDB(
      internalId = internalId,
      tenantId = tenantId,
      dbTable = dbTable
    )
      .flatMap(uuid => {

        setIdToUuidMappingInCache(
          internalId = internalId,
          uuid = uuid,
          tenantId = tenantId,
          dbTable = dbTable,
        )
          .map(_ => uuid)
          .recover {
            case e =>
              // even if setIdToUuidMappingInCache fails, we still need to return the id that came from db

              logger.fatal(s"setIdToUuidMappingInCache error: ${LogHelpers.getStackTraceAsString(e)}")

              uuid
          }

      })

  }


  private def getInternalIdFromDbAndSetInCache(

                                                uuid: String,

                                                tenantId: Option[SrTenantId],

                                                dbTable: SrUuidDbTable,

                                              )(
                                                implicit logger: SRLogger
                                              ): Try[Option[Long]] = {

    getInternalIdFromDB(
      uuid = uuid,
      tenantId = tenantId,
      dbTable = dbTable
    )
      .flatMap {
        case Some(internalId) => setIdToUuidMappingInCache(
          internalId = internalId,
          tenantId = tenantId,
          uuid = uuid,
          dbTable = dbTable,
        ).map(_ => Some(internalId) : Option[Long])
          .recover {
            case e =>
              // even if setIdToUuidMappingInCache fails, we still need to return the id that came from db

              logger.fatal(s"setIdToUuidMappingInCache error: ${LogHelpers.getStackTraceAsString(e)}")

              Some(internalId)
          }
        case None => {
          val res: Try[Option[Long]] = Success(None)
          res
        }
      }

  }


  private def idToUuidPrivate(

                               internalId: Long,

                               tenantId: Option[SrTenantId],
                               dbTable: SrUuidDbTable,

                             )(
                               implicit logger: SRLogger
                             ): Try[String] = {


    idToUuidFromCache(

      internalId = internalId,
      tenantId = tenantId,
      dbTable = dbTable,

    ) match {
      case Failure(err) =>

        logger.fatal(s"idToUuidFromCache error: ${LogHelpers.getStackTraceAsString(err)}")

        getUuidFromDbAndSetInCache(
          internalId = internalId,
          tenantId = tenantId,
          dbTable = dbTable
        )

      case Success(None) =>

        getUuidFromDbAndSetInCache(
          internalId = internalId,
          tenantId = tenantId,
          dbTable = dbTable
        )

      case Success(Some(uuidFromCache)) =>

        Success(uuidFromCache)
    }

  }

  final def idToUuid(

                      internalId: Long,

                      tenantId: SrTenantId,
                      dbTable: SrUuidDbTable,

                    )(
                      implicit logger: SRLogger
                    ): Try[String] = {

    idToUuidPrivate(
      internalId = internalId,
      tenantId = Some(tenantId),
      dbTable = dbTable,
    )

  }

  final def idToUuid(

                      internalIds: Seq[Long],

                      tenantId: SrTenantId,
                      dbTable: SrUuidDbTable,

                    )(
                      implicit logger: SRLogger
                    ): Try[Map[Long, String]] = {

    val res: Seq[Try[(Long, String)]] = internalIds
      .distinct
      .map(id => {

        idToUuidPrivate(
          internalId = id,
          tenantId = Some(tenantId),
          dbTable = dbTable,
        )
          .map(uuid => {
            (id, uuid)
          })

      })


    utils.Helpers
      .seqTryToTrySeq(res)
      .map(_.toMap)

  }

  /*
  should be only used in special cases like Organizations table, otherwise always use tenantId one above
  */
  final def idToUuidWithoutTenantId(

                                     internalId: Long,

                                     dbTable: SrUuidDbTable,

                                   )(
                                     implicit logger: SRLogger
                                   ): Try[String] = {

    idToUuidPrivate(
      internalId = internalId,
      tenantId = None,
      dbTable = dbTable,
    )

  }

  /*
  should be only used in special cases like Organizations table, otherwise always use tenantId one above
  */
  final def idToUuidWithoutTenantId(

                                     internalIds: Seq[Long],

                                     dbTable: SrUuidDbTable,

                                   )(
                                     implicit logger: SRLogger
                                   ): Try[Map[Long, String]] = {

    val res: Seq[Try[(Long, String)]] = internalIds
      .distinct
      .map(id => {

        idToUuidPrivate(
          internalId = id,
          tenantId = None,
          dbTable = dbTable,
        )
          .map(uuid => {
            (id, uuid)
          })

      })


    utils.Helpers
      .seqTryToTrySeq(res)
      .map(_.toMap)

  }


  private def uuidToInternalIdPrivate(

                                       uuid: String,

                                       tenantId: Option[SrTenantId],

                                       dbTable: SrUuidDbTable,

                                     )(
                                       implicit logger: SRLogger
                                     ): Try[Option[Long]] = {


    uuidToIdFromCache(

      uuid = uuid,
      tenantId = tenantId,

    ) match {
      case Failure(err) =>

        logger.fatal(s"uuidToIdFromCache error: ${LogHelpers.getStackTraceAsString(err)}")

        getInternalIdFromDbAndSetInCache(
          uuid = uuid,
          tenantId = tenantId,
          dbTable = dbTable
        )

      case Success(None) =>


        getInternalIdFromDbAndSetInCache(
          uuid = uuid,
          tenantId = tenantId,
          dbTable = dbTable
        )

      case Success(Some(internalIdFromCache)) =>

        Success(Some(internalIdFromCache))
    }

  }

  final def uuidToInternalId(

                              uuid: String,

                              tenantId: SrTenantId,

                              dbTable: SrUuidDbTable,

                            )(
                              implicit logger: SRLogger
                            ): Try[Option[Long]] = {

    uuidToInternalIdPrivate(

      uuid = uuid,
      tenantId = Some(tenantId),
      dbTable = dbTable,

    )

  }

  final def uuidToInternalId(

                              uuids: Seq[String],

                              tenantId: SrTenantId,

                              dbTable: SrUuidDbTable,

                            )(
                              implicit logger: SRLogger
                            ): Try[Map[String, Option[Long]]] = {

    val r: Seq[(String, Try[Option[Long]])] = uuidToInternalIdMap(
      uuids = uuids,
      tenantId = tenantId,
      dbTable = dbTable
    )

    //converting Seq[(String, Try[Long])] to Seq[Try[(String, Long)]]

    val res: Seq[Try[(String, Option[Long])]] = r.map(
      uuidTryId => uuidTryId._2.map(
        id => (uuidTryId._1, id)
      )
    )

    utils.Helpers
      .seqTryToTrySeq(res)
      .map(_.toMap)


  }

  final def uuidToInternalIdMap(

                                 uuids: Seq[String],

                                 tenantId: SrTenantId,

                                 dbTable: SrUuidDbTable,

                               )(
                                 implicit logger: SRLogger
                               ): Seq[(String, Try[Option[Long]])] = {

    val res: Seq[(String, Try[Option[Long]])] = uuids
      .distinct
      .map(uuid => {

        uuidToInternalIdPrivate(

          uuid = uuid,
          tenantId = Some(tenantId),
          dbTable = dbTable,

        ) match {

          case Failure(exception) => (uuid, Failure(exception = exception))
          case Success(value) => (uuid, Success(value = value))

        }

      })
    res

  }

  /*
  should be only used in special cases like Organizations table, otherwise always use tenantId one above
   */
  final def uuidToInternalIdWithoutTenantId(

                                             uuid: String,

                                             dbTable: SrUuidDbTable,

                                           )(
                                             implicit logger: SRLogger
                                           ): Try[Option[Long]] = {

    uuidToInternalIdPrivate(

      uuid = uuid,
      tenantId = None,
      dbTable = dbTable,

    )

  }


  /*
  should be only used in special cases like Organizations table, otherwise always use tenantId one above
   */
  final def uuidToInternalIdWithoutTenantId(

                                             uuids: Seq[String],

                                             dbTable: SrUuidDbTable,

                                           )(
                                             implicit logger: SRLogger
                                           ): Try[Map[String, Option[Long]]] = {

    val res: Seq[Try[(String, Option[Long])]] = uuids
      .distinct
      .map(uuid => {

        uuidToInternalIdPrivate(

          uuid = uuid,
          tenantId = None,
          dbTable = dbTable,

        )
          .map(internalId => {
            (uuid, internalId)
          })

      })

    utils.Helpers
      .seqTryToTrySeq(res)
      .map(_.toMap)


  }

  final def uuidToInternalIdFailIfNotFound(
                                            uuid: String,

                                            tenantId: SrTenantId,

                                            dbTable: SrUuidDbTable
                                          )(
                                            implicit logger: SRLogger
                                          ): Try[Long] = {

    uuidToInternalId(
      uuid = uuid,
      tenantId = tenantId,
      dbTable = dbTable
    ) match {
      case Success(idOpt) =>
        idOpt match {
          case Some(id) => Success(id)
          case None => Failure(new Exception(CONSTANTS.APP_ERR_MSGS.UUID_NOT_FOUND_ERR_MSG))
        }
      case Failure(exception) => Failure(exception)
    }

  }

}
