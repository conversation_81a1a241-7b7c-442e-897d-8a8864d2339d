package utils.uuid.dao

import api.CacheServiceJedis
import utils.cache_utils.CacheKeyGen
import utils.uuid.models.SrTenantId

import scala.util.Try

trait SrUuidCache {

  protected val cacheServiceJedis: CacheServiceJedis

  // these will hardly ever change, we can increase this even more, later
  private val expireInSeconds: Int = 86400


  protected def setIdToUuidMappingInCache(

    internalId: Long,
    dbTable: SrUuidDbTable,
    uuid: String,
    tenantId: Option[SrTenantId],

  ): Try[true] = {

    for {

      _: String <- {

        val id2UuidKey = CacheKeyGen.getId2UuidKey(
          dbTable = dbTable,
          internalId = internalId,
          tenantId = tenantId,
        )

        cacheServiceJedis.set(
          key = id2UuidKey,
          value = uuid,
          expireInSeconds = expireInSeconds
        )
      }

      _: String <- {

        val uuid2IdKey = CacheKeyGen.getUuid2IdKey(
          uuid = uuid,
          tenantId = tenantId,
        )

        cacheServiceJedis.set(
          key = uuid2IdKey,
          value = internalId,
          expireInSeconds = expireInSeconds
        )
      }

    } yield {

      true

    }

  }


  protected def idToUuidFromCache(

    internalId: Long,
    tenantId: Option[SrTenantId],
    dbTable: SrUuidDbTable

  ): Try[Option[String]] = {

    val key = CacheKeyGen.getId2UuidKey(
      dbTable = dbTable,
      tenantId = tenantId,
      internalId = internalId
    )

    cacheServiceJedis.get[String](
      key = key
    )
  }


  protected def uuidToIdFromCache(

    uuid: String,
    tenantId: Option[SrTenantId],

  ): Try[Option[Long]] = {

    val key = CacheKeyGen.getUuid2IdKey(
      tenantId = tenantId,
      uuid = uuid
    )

    cacheServiceJedis.get[Long](
      key = key
    )
  }


}
