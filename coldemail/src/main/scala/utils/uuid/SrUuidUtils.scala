package utils.uuid

import api.accounts.models.{AccountId, OrgId}
import api.accounts.TeamId
import api.linkedin.models.LinkedinSettingUuid
import api.linkedin_message_threads.GroupedLinkedinThreadsForMessageScraping
import api.phantombuster.PhantomBusterApiKeyDetails
import api.sr_audit_logs.models.{AttemptLogId, AttemptTriesLogId}
import api.tasks.models.{CommonLinkedinTaskDetails, TaskType}
import com.github.f4b6a3.uuid.UuidCreator
import com.github.ksuid.KsuidGenerator
import org.joda.time.DateTime
import utils.uuid.SrUuidUtils.Pre

import scala.util.Random
import java.security.SecureRandom
import java.util.UUID


object SrUuidUtils {

  // define all uuid prefixes here
  private object Pre {
    /*
     * Note: Key names here Must Be Same as value
     * */


    // uuid prefix e.g. task_12fefs4434ldn : "task" is the prefix
    val task = "task"

    val linkedin_account = "linkedin_account"

    val whatsapp_account = "whatsapp_account"

    val phone_number = "phone_number"

    val caller_id = "caller_id"

    val general_channel_setting = "general_channel_setting"

    val reply_sentiment = "reply_sentiment"

    val cmp_aa = "cmp_aa" // campaigns

    val prs_aa = "prs_aa" // prospects

    val acc = "acc" // account

    val sub_account = "sub_account" // twilio sub account

    val conference = "conference"

    val participant = "participant"
    
    val voicemail = "vmail"

    val campaign_email_settings = "campaign_email_settings"

    val team = "team"

    // start: audit log related
    val bw = "bw" // audit log: background request log id

    val req = "req" // audit log: request log id

    val req_mq = "req_mq" // audit log: mq request log id

    val atmpt = "atmpt" // audit log: attempt log id

    val atmpt_tr = "atmpt_tr" // audit log: attempt try log id

    // end: audit log related

    val pipeline = "pipeline"

    val linkedin_msg_threads = "linkedin_msg_threads"

    val email_thread = "email_thread"

    val opportunity_status = "opportunity_status"

    val opportunity = "opportunity"

    val emails_scheduled = "email_scheduled"

    val tags = "tags"

    val linkedin_messages = "linkedin_msgs"
    
    val linkedin_connections = "linkedin_connections"

    val call_log = "call_log"

    val blacklist = "blacklist"

    val email_settings = "eml_set"

    val email_setting_attempt = "eml_set_at"

    val organization = "org"

    val prospect_accounts = "prs_acc"

    val internal_inbox_placement_check_logs = "inbp"

    val lead_finder_prospect_queue_uuid = "lfpq"

    val lead_finder_prospect_uuid = "lfp"

    val lead_finder_billing_logs = "lfbl"

    val purchased_domain = "purchased_domain"

  }




}


class SrUuidUtils {

  private val ksuid_Generator: KsuidGenerator = new KsuidGenerator(new SecureRandom())

  private def uuidv7(): UUID = UuidCreator.getTimeOrderedEpoch

  private def ksuid(): String = ksuid_Generator.newKsuid().toString

  def generateTaskUuid(): String = s"${Pre.task}_${ksuid()}"

  def generateLinkedinAccountUuid: String = s"${Pre.linkedin_account}_${ksuid()}"

  def generateWhatsappAccountUuid: String = s"${Pre.whatsapp_account}_${ksuid()}"

  def generatePhoneNumberUuid: String = s"${Pre.phone_number}_${ksuid()}"

  def generateCallerIdUuid: String = s"${Pre.caller_id}_${ksuid()}"

  def generateGeneralChannelSettingUuid(): String = s"${Pre.general_channel_setting}_${ksuid()}"

  def generateReplySentimentUuid(): String = s"${Pre.reply_sentiment}_${ksuid()}"

  def generateCampaignUuid(): String = s"${Pre.cmp_aa}_${ksuid()}"
  
  def generateVoicemailUuid(): String = s"${Pre.voicemail}_${ksuid()}"

  def generateProspectUuid(): String = s"${Pre.prs_aa}_${ksuid()}"

  def generateAccountUuid(): String = s"${Pre.acc}_${ksuid()}"

  def generateSubAccountUuid(): String = s"${Pre.sub_account}_${ksuid()}"

  def generateConferenceUuid(): String= s"${Pre.conference}_${ksuid()}"

  def generateParticipantUuid(): String= s"${Pre.participant}_${ksuid()}"

  def generatePipelineUuid(): String = s"${Pre.pipeline}_${ksuid()}"

  def generateOpportunityStatusUuid(): String = s"${Pre.opportunity_status}_${ksuid()}"

  def generateOpportunityUuid(): String = s"${Pre.opportunity}_${ksuid()}"

  def generateCampaignEmailSettingsUuid(): String = s"${Pre.campaign_email_settings}_${ksuid()}"

  def generateTeamUuid(): String = s"${Pre.team}_${ksuid()}"

  def generateLinkedinMessageThreadUuid(): String = s"${Pre.linkedin_msg_threads}_${ksuid()}"

  def generateEmailThreadsUuid(): String = s"${Pre.email_thread}_${ksuid()}"

  def generateEmailsScheduledUuid(): String = s"${uuidv7()}".toLowerCase

  def generateTagsUuid(): String = s"${Pre.tags}_${ksuid()}"

  def generateLinkedinMessagesUuid(): String = s"${Pre.linkedin_messages}_${ksuid()}"
  
  def generateLinkedinConnectionsUuid(): String = s"${Pre.linkedin_connections}_${ksuid()}"

  def generateCallLogUuid(): String = s"${Pre.call_log}_${ksuid()}"

  def generateBlacklistUuid(): String = s"${Pre.blacklist}_${ksuid()}"

  def generateEmailSettingUuid(): String = s"${Pre.email_settings}_${ksuid()}"

  def generateEmailSettingAttemptUuid(): String = s"${Pre.email_setting_attempt}_${ksuid()}"

  def generateOrgUuid(): String = s"${Pre.organization}_${ksuid()}"

  def generateProspectAccountUuid(): String = s"${Pre.prospect_accounts}_${ksuid()}"

  def generateInternalInboxPlacementCheckLogsUuid(): String = s"${Pre.internal_inbox_placement_check_logs}_${ksuid()}"

  def generateLeadFinderProspectQueueUuid(): String = s"${Pre.lead_finder_prospect_queue_uuid}_${ksuid()}"

  def generateLeadFinderProspectUuid(): String = s"${Pre.lead_finder_prospect_uuid}_${ksuid()}"

  def generateLeadFinderBillingLogsUuid(): String = s"${Pre.lead_finder_billing_logs}_${ksuid()}"

  def generatePurchasedDomainsUuid(): String = s"${Pre.purchased_domain}_${ksuid()}"

  /**
   * This fn is for Background workers / crons only, and not api calls
   *
   * @param teamId
   * @param accountId
   * @return
   */
  def generateBackgroundRequestLogId(
    teamId: TeamId,
    orgId: OrgId
  ): String = {

    val preFix = Pre.bw

    val uuid = ksuid()

    val requestId = s"${preFix}_${orgId.id}_${teamId.id}_$uuid"

    requestId
  }

  /**
   * This fn is for API CALLS only.
   *
   * @return ex: req_1642675705646_42_24_4WNxCJjZqU
   */
  def generateRequestLogId(teamId: Long, accountId: Long): String = {

    val preFix = Pre.req

    val uuid = ksuid()

    val requestId = s"${preFix}_${teamId}_${accountId}_${uuid}"

    requestId
  }

  /**
   * This fn is for Background workers only, and not api calls / crons
   *
   * @return ex: req_mq_1642675fdsafds705646
   */
  def generateMqRequestLogId(): String = {

    val preFix = Pre.req_mq

    val uuid = ksuid()

    val requestId = s"${preFix}_${uuid}"

    requestId
  }


  /**
   * @return ex: atmpt_1642675705646_42_24_4WNxCJjZqU
   */
  def generateAttemptLogId(teamId: TeamId, accountId: AccountId): AttemptLogId = {
    val preFix = Pre.atmpt
    val mills = DateTime.now().getMillis
    val rand = Random.alphanumeric.take(10).mkString
    val attemptId = s"${preFix}_${mills}_${accountId}_${teamId}_$rand"
    AttemptLogId(attemptId)
  }

  /**
   * @return ex: atmpt_tr_1642675705646_42_24_4WNxCJjZqU
   */
  def generateAttemptTriesLogId(teamId: TeamId, accountId: AccountId): AttemptTriesLogId = {
    val preFix = Pre.atmpt_tr
    val mills = DateTime.now().getMillis
    val rand = Random.alphanumeric.take(10).mkString
    val attemptId = s"${preFix}_${mills}_${accountId}_${teamId}_$rand"
    AttemptTriesLogId(attemptId)
  }
  
  def generatePhantomBusterCSVFilename(
                                        commonLinkedinTaskDetails: CommonLinkedinTaskDetails
                                      ): String = {
    
    s"${commonLinkedinTaskDetails.taskType}_${commonLinkedinTaskDetails.teamId}_${commonLinkedinTaskDetails.linkedinSettingUuid}_${ksuid()}"
  }

  def generateDeBounceUploadCSVFilename(

                               ):String = {
    s"${ksuid()}.csv"
  }

  def generatePhantombusterTeamSecretKey(teamId: TeamId, apiKey: PhantomBusterApiKeyDetails): String = {
    
    apiKey match {
      case internalApiKey: PhantomBusterApiKeyDetails.InternalPhantomBusterApiKey =>
        s"${ksuid()}___team_${teamId.id}___internal_api_key_id_${internalApiKey.internalApiKeyId}"

      case externalApiKey: PhantomBusterApiKeyDetails.ExternalPhantomBusterApiKey =>
        s"${ksuid()}___team_${teamId.id}"
    }
    
  }
  
  def generateFileNameForLinkedinMessageThreadScraping(groupedLinkedinThreadsForMessageScraping: GroupedLinkedinThreadsForMessageScraping) = {
    
    s"LinkedinMessageThreadScraping_${groupedLinkedinThreadsForMessageScraping.teamId.id}_${groupedLinkedinThreadsForMessageScraping.linkedinSettingUuid}_${ksuid()}"
    
  }
  
  def generateFileNameForAfterLoginScreenshot(uuid: LinkedinSettingUuid, teamId: TeamId, username: String) = {
    
    s"${username}_team_${teamId.id}_${uuid.uuid}_${ksuid()}"
    
  }

}
