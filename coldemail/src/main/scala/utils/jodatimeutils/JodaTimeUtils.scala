package utils.jodatimeutils

import org.joda.time.{DateTime, DateTimeZone}

import java.time.LocalDate

object JodaTimeUtils {

  // joda implicit datetime ordering for finally sorting events
  // REF: https://stackoverflow.com/a/9061184
  def dateTimeOrdering: Ordering[DateTime] = Ordering.fromLessThan((a: DateTime, b: DateTime) => a.isBefore(b))

  def DateTimeToJavaTimeLocalDate(date_time: DateTime): LocalDate = {
    val dateTimeUtc = date_time.withZone(DateTimeZone.UTC) // DateTime to LocalDateCoversion
    val localDate = LocalDate.of(dateTimeUtc.getYear, dateTimeUtc.getMonthOfYear, dateTimeUtc.getDayOfMonth)
    localDate
  }

  def JavaTimeLocalDateToDateTime(localDate: LocalDate): DateTime = {
    new DateTime(DateTimeZone.UTC).withDate(
      localDate.getYear, localDate.getMonthValue, localDate.getDayOfMonth
    ).withTime(0, 0, 0, 0)
  }
}
