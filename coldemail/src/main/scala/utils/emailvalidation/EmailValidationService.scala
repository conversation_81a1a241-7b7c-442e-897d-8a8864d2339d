package utils.emailvalidation

import api.AppConfig
import api.prospects.service.ProspectServiceV2
import api.prospects.{ProspectForValidation, ProspectsWithInvalidEmail}
import api.prospects.models.{EmailForValidation, ProspectCategory, ProspectId}
import org.apache.commons.validator
import api.accounts.TeamId
import api.accounts.models.AccountId
import api.campaigns.services.CampaignId
import api.lead_finder.DAO.LeadIdAndEmail
import api.lead_finder.service.LeadFinderValidationService
import io.smartreach.esp.api.emails.IEmailAddress
import org.joda.time.{DateTime, DateTimeZone}
import play.api.libs.json.{JsVal<PERSON>, <PERSON>son}
import play.api.libs.ws.WSClient
import sr_scheduler.models.CampaignForScheduling
import utils.GCP.CloudStorage
import utils.emailvalidation.ValidationResultWithTeamIdAndAnalysisId.{ProspectEmailValidationResultWithTeamIdAndAnalysisId, ValidationResultWithTeamIdAndAnalysisIdWithoutEmailValidationId}
import utils.emailvalidation.models.{EmailValidationBatchRequestStatusV2, EmailValidationInitiator, EmailValidationToolStatus, EmailValidationToolStatusType, EmailsForValidationWithInitiator, IdsOrEmailsForValidation}
import utils.mq.prospect_category.{MqAutoUpdateProspectCategoryMsg, MqAutoUpdateProspectCategoryPublisher}

import scala.annotation.tailrec
//import utils.emailvalidation.EmailValidationService.getEmailValidationTool
//import utils.emailvalidation.EmailValidationTool.EmailValidationTool
import utils.emailvalidation.models.{EmailValidationPriority, EmailValidationToolV2, InternalInvalidIEmailAddressReasons}
import utils.random.SrRandomUtils
import utils.uuid.SrUuidUtils
import utils.{Helpers, SRLogger}

import javax.mail.Address
import scala.collection.immutable.List
//import utils.mq.emailvalidator.MQMBLEmailValidator
import utils.mq.webhook.{MQWebhookEmailInvalid, MQWebhookEmailInvalidMsg}
import utils.helpers.LogHelpers

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

import utils.emailvalidation.dao_service.EmailValidationDAOService

case class EmailSplit(
                       fullname: String,
                       nameWithoutAlias: String,
                       domain: String
                     )

case class ServiceProviderBatchRequestId(
                                          srBatchRequestIdBouncer: Option[Long],
                                          srBatchRequestIdDeBounce: Option[Long],
                                          srBatchRequestIdListClean: Option[Long]
                                        )

case class EmailValidationData(
                                email_validations_id: Long,
                                email: String,
                                initiator_team_id: TeamId,
                                checked_via_tool: EmailValidationToolV2,
                                validationInitiator: EmailValidationInitiator.EmailValidationInitiatorType,
                              )

case class EmailValidationToolAssignedRecord(
                                              validationTool: EmailValidationToolV2,
                                              emails_Assigned: Long
                                            )

class EmailValidationService(
                              emailValidationModel: EmailValidationModel,
                              transformAndSaveEmailValidationResult: TransformAndSaveEmailValidationResultTrait,
                              emailValidationBatchRequestModel: EmailValidationBatchRequestModel,
                              emailValidationApiToolsRecordDAO: EmailValidationApiToolsRecordDAO,
                              deBouncerEmailValidationApi: DeBounceEmailValidationApi,
                              bouncerEmailValidationApi: BouncerEmailValidationApi,
                              listCleanEmailValidationApi: ListCleanEmailValidationApi,
                              emailValidationDAOService: EmailValidationDAOService,
                              cloudStorage: CloudStorage,
                              srRandomUtils: SrRandomUtils,
                              srUuidUtils: SrUuidUtils,
                              //  mqMBLEmailValidator: MQMBLEmailValidator,
                              //  prospectDAO: Prospect,
                              prospectServiceV2: ProspectServiceV2,
                              mqWebhookEmailInvalid: MQWebhookEmailInvalid,
                              mqAutoUpdateProspectCategoryPublisher: MqAutoUpdateProspectCategoryPublisher,
                              leadFinderValidationService: LeadFinderValidationService
                            ) {


  def checkAndUpdateIfInvalidBasedOnJavaEmailAddressBatch(
                                                           emailsFound: List[EmailValidationData]
                                                         )(using Logger: SRLogger): Try[List[EmailValidationData]] = {
    val invalidEmails = emailsFound.filterNot(email => EmailValidationService.validateIEmailAddress(email = email.email))

    val emailValidationResultWithTeamIdAndAnalysisId = invalidEmails.map { email =>
      ProspectEmailValidationResultWithTeamIdAndAnalysisId(
        emailDeliveryAnalysisId = None,
        teamId = TeamId(email.initiator_team_id.id),
        email = email.email,
        isValid = false,
        emailValidationId = email.email_validations_id,
        validationInitiator = email.validationInitiator,
      )
    }

    for {

      updateEmailValidationDataV2: (Int, Seq[ProspectsWithInvalidEmail]) <- {

        val prospectEmailValidationResultWithTeamIdAndAnalysisId: List[ProspectEmailValidationResultWithTeamIdAndAnalysisId] =
          emailValidationResultWithTeamIdAndAnalysisId.filter { p =>
            p.validationInitiator == EmailValidationInitiator.InitiatedByCampaign
          }

        prospectServiceV2.updateEmailValidationDataV2(data = prospectEmailValidationResultWithTeamIdAndAnalysisId, Logger = Logger, emailValidationInitiatorType = EmailValidationInitiator.InitiatedByCampaign)

      }

      _: Int <- {

        /**
         * 23-Jul-2024
         *
         * For lead finder we should never get invalidEmails
         * from EmailValidationService.validateIEmailAddress
         * because we already do this check before sending emails for validation
         * in `leadFinderService.addLeadsIntoProspectList`.
         */

        val leadEmailValidationResultWithTeamIdAndAnalysisId: List[ProspectEmailValidationResultWithTeamIdAndAnalysisId] =
          emailValidationResultWithTeamIdAndAnalysisId.filter { p =>
            p.validationInitiator == EmailValidationInitiator.InitiatedByLeadFinder
          }

        leadFinderValidationService.updateLeadFinderValidationResults(
          data = leadEmailValidationResultWithTeamIdAndAnalysisId
        )

      }

      _: List[ProspectEmailValidationResultWithTeamId] <- {
        val validationResults = invalidEmails.map { email =>
          EmailValidationResultWithAnalysisId(
            emailDeliveryAnalysisId = None,
            email = email.email,
            fullJson = Json.obj(),
            isValid = false,
            srEmailValidityStatus = SREmailValidityStatus.SR_INFERRED_UNDELIVERABLE,
            internal_sr_validation_fail_reason = Some(InternalInvalidIEmailAddressReasons.FailedIEmailAddressParse)
          )
        }
        emailValidationModel.updateEmailValidationResultsFromBatchRequest(validationResults = validationResults, validationService = EmailValidationToolV2.SMARTREACH_INTERNAL)
      }
      _ <- if (updateEmailValidationDataV2._2.nonEmpty) {
        mqWebhookEmailInvalid.publish(
          message = MQWebhookEmailInvalidMsg(
            accountId = updateEmailValidationDataV2._2.head.accountId,
            teamId = updateEmailValidationDataV2._2.head.teamId,
            prospectIds = updateEmailValidationDataV2._2.map(_.prospectId)
          )
        )
      } else {
        Success({})
      }
    } yield {

      val validEmails = emailsFound.filterNot(invalidEmails.contains)
      validEmails
    }

  }

  def validateLeadEmailsBasedOnJavaEmailAddress(
                                                 emails: List[String],
                                                 teamId: TeamId,
                                               )(using Logger: SRLogger): Try[List[String]] = {

    val invalidEmails = emails.filterNot(email => EmailValidationService.validateIEmailAddress(email = email))

    val leadFinderValidationRes = invalidEmails.map { email =>
      ValidationResultWithTeamIdAndAnalysisIdWithoutEmailValidationId(
        emailDeliveryAnalysisId = None,
        teamId = teamId,
        email = email,
        isValid = false,
        validationInitiator = EmailValidationInitiator.InitiatedByLeadFinder,
      )
    }

    leadFinderValidationService.updateLeadFinderValidationResults(
      data = leadFinderValidationRes
    ) match {

      case Failure(exception) =>

        Logger.shouldNeverHappen(
          msg = s"updateLeadFinderValidationResults failed - validateLeadEmailsBasedOnJavaEmailAddress. teamId: $teamId :: emails: $emails :: invalidEmails: $invalidEmails",
          err = Some(exception)
        )

        Failure(exception)

      case Success(_) =>

        val validEmails = emails.filterNot(invalidEmails.contains)

        Success(validEmails)

    }

  }

  def checkAndTryToMakeAPICallToCheckAPisUp(
                                             apiToolWhomErrorisOccured: EmailValidationToolV2
                                           )(implicit ws: WSClient,
                                             ec: ExecutionContext,
                                             srLogger: SRLogger): Future[Long] = {


    //TODO: Added a dummy data but Need some sample emailvalidationdata to pass to the api call
    val emailsListToSentToCheck: List[EmailValidationData] = List(
      EmailValidationData(
        email_validations_id = 0,
        email = "<EMAIL>",
        initiator_team_id = TeamId(0L),
        checked_via_tool = apiToolWhomErrorisOccured,
        validationInitiator = EmailValidationInitiator.InitiatedByCampaign, // TODO: check this
      ),
    )

    for {
      srRequestId: Long <- apiToolWhomErrorisOccured match {
        case EmailValidationToolV2.BOUNCER =>

          makeBatchRequestWithBouncerApiAndReturnRequestId(emailsListToSentToCheck)(ws, ec, srLogger)


        case EmailValidationToolV2.DEBOUNCE =>



          makeBatchRequestWithDeBounceApiAndReturnRequestId(emailsListToSentToCheck)(ws, ec, srLogger)


        case EmailValidationToolV2.LISTCLEAN =>


          makeBatchRequestWithListCleanApiAndReturnRequestId(emailsListToSentToCheck)(ws, ec, srLogger)

        case EmailValidationToolV2.SMARTREACH_INTERNAL =>

          Future.failed(new Exception(s"${apiToolWhomErrorisOccured} - is a internal API no function exists to make a call"))


      }
    } yield {
      srRequestId
    }
  }


  /*
  24-Apr-2024:Logic for getToolServiceStatus:
  It takes a API Tool as an argument for which the status needs to be checked
  It will fetch the service status  from email_validation_api_tools_record_table table and match for different cases
       if Down

              return Down()
       if Processing
              call getRecord function to check whether the ttimetoconsider down has passed or not
              if passed
                    update status into email_validation_api_tools_record_table as Down
                    return Down()
              else
                   return Processing()

        if Available
             call getRecord function to check fromwhen it is available
             return Available(time_since_it_is_available)
        if Error
             will check whether the error was occured before 50 minutes(for now will change it when listclean api key is added)
             if time passed
                  then return Available(currentime)
             else
                  return Error()
         if no record found for the tool
              return Available(currenttime)

       if fetchservicestatus return error then will throw exception
   */
  def getToolServiceStatus(validationToolV2: EmailValidationToolV2)(implicit ws: WSClient,
                                                                    ec: ExecutionContext,
                                                                    srLogger: SRLogger): Try[EmailValidationToolStatus] = {
    val resultStatus = emailValidationApiToolsRecordDAO.fetchAPIServiceStatus(toolToCheckStatus = validationToolV2) match {
      case Success(Some(serviceRecord)) =>

        serviceRecord.serviceStatus match {


          case EmailValidationToolStatusType.Down =>

            srLogger.fatal(s"$validationToolV2 - is Down")
            Success(EmailValidationToolStatus.Down())


          case EmailValidationToolStatusType.Processing =>

            /*
    If the status is Processing then we need to check whether the certain time of the tool is passed . Like for Debounce we get status as Processing
    and 75 minutes are passed then we need to update the status to DOwn in the table and also return the status as DOwn() else return status as
    Processing()
     */

            serviceRecord.batchCreatedAt match {

              case Some(batchCreatedTime) =>
                if (batchCreatedTime.plusMinutes(validationToolV2.minTimeToBeConsideredAsDowninMinutes).isBeforeNow) {

                  serviceRecord.batchRequestId match {
                    case Some(requestId) =>
                      emailValidationApiToolsRecordDAO.updateServiceStatus(
                        id = serviceRecord.id,
                        batchRequestId = requestId,
                        newServiceStatus = EmailValidationToolStatusType.Down,
                        apiForWhichServiceStatusToBeUpdated = validationToolV2
                      )
                      srLogger.fatal(s"$validationToolV2 - went  Down")
                      Success(EmailValidationToolStatus.Down())

                    case None =>
                      srLogger.shouldNeverHappen(s"Not a possible case for API with latest status as Processing " +
                        s"batchRequestId must Be Present for tool with status Processing")

                      Success(EmailValidationToolStatus.Processing())


                  }
                }
                else {
                  Success(EmailValidationToolStatus.Processing())
                }

              case None =>
                srLogger.shouldNeverHappen(s"Not a possible case for API with latest status as Processing " +
                  s"batchCreatedAt must Be Present for tool with status Processing")

                Success(EmailValidationToolStatus.Processing())

            }

          case EmailValidationToolStatusType.Available =>
            serviceRecord.availableSinceTime match {
              case Some(availableTime) =>
                srLogger.debug(s"$validationToolV2 - is Available")
                Success(EmailValidationToolStatus.Available(available_since = availableTime))
              case None =>
                Success(EmailValidationToolStatus.Available(available_since = DateTime.now()))

            }


          case EmailValidationToolStatusType.Error =>



            srLogger.fatal(s"$validationToolV2 - has some Error")

            //checkAndTryToMakeAPICallToCheckAPisUp - will check if the api is up and
            // the next iteration will a call to the api if it comes into available state.
            //Will add an entry if error occured again and if call is made successfully then
            // the batch record will be added to the table which will signify that the error is resolved
            //and we can send emails for validation to the api
            //But for this iteration - we will fail the api service
            // in the next iteration the api service status will be fetched from the table


            //FIXME : Evaluate if we can move this error check function to a separate Cron

            Success(EmailValidationToolStatus.Error)


        }
      case Success(None) =>
        // This will be for any new APi Tool which don't have any record in our Db
        // so we will return the status as Available.

        Success(EmailValidationToolStatus.Available(DateTime.now()))

      case Failure(exception) =>
        Failure(exception)
    }

    resultStatus
  }

  /*
  9-Apr-2024 : def checkStatusAndAssignToolForEmailValidation Logic explanation with the help of scenario
  It takes EmailList and primaryValidationTool as an argument
      def switchAndAssignTool will be called if the primary tool is down
  COnsider emailList is list of emails and PrimaryValidationTool = BOUNCER
     willcheck the status for BOUNCER first
         if (status of BOUNCER ==down) OR (status of BOUNCER == Error) then:
               switchAndAssignTool(BOUNCER,List(LISTCLEAN,DEBOUNCE))
                      if (status of LISTCLEAN is also DOWN) OR (LISTCLEAN has Also Error status) then :
                              switchAndAssignTool(BOUNCER,List(DEBOUNCE))
                                       if status of DEBOUNCE == Available then:
                                                       change the status of emails sent to Bouncer as to_be_queued and
                                                       update the tool for those emails to Debounce
                                                       assign DEBOUNCE to emailList
                                       else if status of DEBOUNCE == Processing then :
                                                       return Success(0)
                                       else will return Failure("all tools are down')
                      else if LISTCLEAN is Processing then :
                             return Success(0)
                      else LISTCLEAN is available then:
                             change the status of emails sent to Bouncer as to_be_queued and
                             update the tool for those emails to LISTCLEAN
                             assign DEBOUNCE to emailList


           else if BOUNCER is Processing then :
               return Success(0)
           else BOUNCER is available then:
               assign BOUNCER to emailList


   */

  def checkStatusAndAssignToolForEmailValidation(
                                                  emailList: List[String],
                                                  primaryValidationTool: EmailValidationToolV2
                                                )(
                                                  implicit ws: WSClient,
                                                  ec: ExecutionContext,
                                                  srLogger: SRLogger
                                                ): Try[EmailValidationToolAssignedRecord] = {

    val timeToWait = AppConfig.timeToWaitInSeconds

    @tailrec
    def switchAndAssignTool(downTool: EmailValidationToolV2, backupToolList: List[EmailValidationToolV2]): Try[EmailValidationToolAssignedRecord] = {
      backupToolList match {
        case Nil =>
          srLogger.fatal("EmailValidationService.checkStatusAndAssignToolForEmailValidation : All the validation tools are down")
          Failure(new Exception("All the tools are down"))

        case backupTool :: restTools =>
          getToolServiceStatus(validationToolV2 = backupTool) match {
            case Success(EmailValidationToolStatus.Available(available_since)) =>
              srLogger.debug(s"$backupTool - is available so switching checked_via_tool from $downTool to $backupTool")
              emailValidationDAOService.changingValidationStatusTo_ToBeQueued(validationToolWhichIsDown = downTool)
              emailValidationDAOService.switchingValidationTooltoBackupTool(validationToolWhichIsDown = downTool, validationTool_Backup = backupTool)
              if (backupTool.minimumThresholdLimit <= emailList.size ||
                (emailList.size < backupTool.minimumThresholdLimit && available_since.plusSeconds(timeToWait).isBeforeNow)) {
                emailValidationDAOService.assigningToolForValidation(emailList = emailList, validationToolToBeAssigned = backupTool)
                Success(EmailValidationToolAssignedRecord(validationTool = backupTool, emails_Assigned = emailList.size))
              } else {
                srLogger.debug(s"$backupTool - Threshold Size:${backupTool.minimumThresholdLimit} actual list Size: ${emailList.size}")
                Success(EmailValidationToolAssignedRecord(validationTool = backupTool, emails_Assigned = 0))
              }
            case Success(EmailValidationToolStatus.Processing()) =>
              srLogger.debug(s"$backupTool - currently busy")
              Success(EmailValidationToolAssignedRecord(validationTool = backupTool, emails_Assigned = 0))
            case Success(EmailValidationToolStatus.Down()) =>
              srLogger.fatal(s"$backupTool - API Tool is Down")

              switchAndAssignTool(downTool = downTool, backupToolList = restTools)

            case Success(EmailValidationToolStatus.Error) =>
              srLogger.fatal(s"$backupTool - has got some error while making a batch request before")
              switchAndAssignTool(downTool = downTool, backupToolList = restTools)
            case Failure(exception) =>
              Failure(exception)
          }

      }


    }

    getToolServiceStatus(primaryValidationTool) match {
      case Success(EmailValidationToolStatus.Down()) =>
        srLogger.fatal(s"$primaryValidationTool - is currently Down")
        switchAndAssignTool(downTool = primaryValidationTool, backupToolList = primaryValidationTool.backupToolList)

      case Success(EmailValidationToolStatus.Error) =>
        srLogger.fatal(s"${primaryValidationTool} - has got some error while making a batch request before " +
          s"so switching to ${primaryValidationTool.backupToolList.head}")

        emailValidationDAOService.switchingValidationTooltoBackupTool(validationToolWhichIsDown = primaryValidationTool,
          validationTool_Backup = primaryValidationTool.backupToolList.head)

        switchAndAssignTool(downTool = primaryValidationTool, backupToolList = primaryValidationTool.backupToolList)

      case Success(EmailValidationToolStatus.Processing()) =>
        srLogger.debug(s"$primaryValidationTool - is currently busy ")
        Success(EmailValidationToolAssignedRecord(validationTool = primaryValidationTool, emails_Assigned = 0))
      case Success(EmailValidationToolStatus.Available(available_since)) =>
        srLogger.debug(s"$primaryValidationTool - is available assigning to the emailList")

        if (primaryValidationTool.minimumThresholdLimit <= emailList.size ||
          (emailList.size < primaryValidationTool.minimumThresholdLimit && available_since.plusSeconds(timeToWait).isBeforeNow)) {
          emailValidationDAOService.assigningToolForValidation(emailList = emailList, validationToolToBeAssigned = primaryValidationTool)
          Success(EmailValidationToolAssignedRecord(validationTool = primaryValidationTool, emails_Assigned = emailList.size))
        } else {
          srLogger.debug(s"$primaryValidationTool - Threshold Size:${primaryValidationTool.minimumThresholdLimit} actual list Size: ${emailList.size}")

          Success(EmailValidationToolAssignedRecord(validationTool = primaryValidationTool, emails_Assigned = 0))
        }

      case Failure(exception) =>
        Failure(exception)


    }

  }


  def makeBatchRequestWithBouncerApiAndReturnRequestId(
                                                        validAfterSRJavaCheck: List[EmailValidationData]
                                                      )(implicit ws: WSClient,
                                                        ec: ExecutionContext,
                                                        srLogger: SRLogger): Future[Long] = {


    val bouncerServiceEmails: Seq[String] = validAfterSRJavaCheck.map(_.email)

    for {
      serviceRequestIdBouncer: String <- bouncerEmailValidationApi.createBatchRequest(
        emails = bouncerServiceEmails
      ).map(_.requestId).recoverWith {
        case ex =>
          val errorMessage = ex.getMessage
          val parts = errorMessage.split(" :: ", 2)
          val failureMessage = parts.headOption.map(_.trim).getOrElse("Unknown status")
          val failureJsonResponse: JsValue = {
            parts.lift(1).map(Json.parse).getOrElse(Json.obj())
          } // Extract JSON part (default to empty JSON)

          srLogger.fatal(s"BouncerApiCallError recoverBlockLog - ${failureJsonResponse}")

          val failedRecordFuture: Future[Long] = Future.fromTry {
            emailValidationApiToolsRecordDAO.addErrorRecordWhileCreatingBatchRequest(
              errorOccuredApiTool = EmailValidationToolV2.BOUNCER,
              apiResponseStatus = failureMessage.toInt,
              apiErrorResponse = failureJsonResponse
            )
          }

          failedRecordFuture.flatMap { _ =>

            Future.failed(ex)

          }


      }

      srBatchRequestId: Long <- Future.fromTry(
        emailValidationBatchRequestModel.createBatchRequest(
          validationService = EmailValidationToolV2.BOUNCER,
          serviceRequestId = serviceRequestIdBouncer
        )
      )

      srRequestId: Long <- Future.fromTry(
        emailValidationApiToolsRecordDAO.addApiBatchRequest(
          requestId = serviceRequestIdBouncer,
          emailsSentCount = bouncerServiceEmails.size,
          apiTool = EmailValidationToolV2.BOUNCER
        )
      )

      _: List[Long] <- Future.fromTry(
        emailValidationModel.updateQueuedStatusOnBatchRequest(
          emailValidationIds = validAfterSRJavaCheck.map(_.email_validations_id),
          emailValidationBatchRequestId = srBatchRequestId

        ))


    } yield {
      srBatchRequestId
    }
  }

  def makeBatchRequestWithDeBounceApiAndReturnRequestId(
                                                         validAfterSRJavaCheck: List[EmailValidationData]
                                                       )(implicit ws: WSClient,
                                                         ec: ExecutionContext,
                                                         srLogger: SRLogger): Future[Long] = {

    val deBounceServiceEmails: Seq[String] = validAfterSRJavaCheck.map(_.email)

    val fileName = srUuidUtils.generateDeBounceUploadCSVFilename();
    for {
      fileUrl: String <- cloudStorage.createUploadAndGenerateURLForCSV(
        rows = deBounceServiceEmails.map(e => Seq(e)),
        headerRow = Seq("email"),
        filename = fileName,
        bucketName = AppConfig.deBounceCSVBucketName,
        signedUrlTtlInMinutes = 0
      )
      serviceRequestIdDeBounce: String <- deBouncerEmailValidationApi.createBatchRequest(
        fileUrl = fileUrl
      ).map(_.requestId).recoverWith {
        case ex =>
          val errorMessage = ex.getMessage
          val parts = errorMessage.split(" :: ", 2)
          val failureMessage = parts.headOption.map(_.trim).getOrElse("Unknown status")
          val failureJsonResponse: JsValue = {
            parts.lift(1).map(Json.parse).getOrElse(Json.obj())
          } // Extract JSON part (default to empty JSON)

          srLogger.fatal(s"Debounce Api batch call error recoverBlockLog - ${failureJsonResponse}")

          val failedRecordFuture: Future[Long] = Future.fromTry {
            emailValidationApiToolsRecordDAO.addErrorRecordWhileCreatingBatchRequest(
              errorOccuredApiTool = EmailValidationToolV2.DEBOUNCE,
              apiResponseStatus = failureMessage.toInt,
              apiErrorResponse = failureJsonResponse
            )
          }

          failedRecordFuture.flatMap { _ =>
            Future.failed(ex)

          }


      }

      srBatchRequestId: Long <- Future.fromTry(
        emailValidationBatchRequestModel.createBatchRequest(
          validationService = EmailValidationToolV2.DEBOUNCE,
          serviceRequestId = serviceRequestIdDeBounce
        )
      )

      srRequestId: Long <- Future.fromTry(
        emailValidationApiToolsRecordDAO.addApiBatchRequest(
          requestId = serviceRequestIdDeBounce,
          emailsSentCount = deBounceServiceEmails.size,
          apiTool = EmailValidationToolV2.DEBOUNCE
        )
      )

      _: List[Long] <- Future.fromTry(
        emailValidationModel.updateQueuedStatusOnBatchRequest(
          emailValidationIds = validAfterSRJavaCheck.map(_.email_validations_id),
          emailValidationBatchRequestId = srBatchRequestId
        ))


      checkDeleteBucket: Boolean <- cloudStorage.deleteForCSV(fileName, AppConfig.deBounceCSVBucketName)


    } yield {
      srBatchRequestId
    }
  }

  def makeBatchRequestWithListCleanApiAndReturnRequestId(
                                                          validAfterSRJavaCheck: List[EmailValidationData]
                                                        )(
                                                          implicit ws: WSClient,
                                                          ec: ExecutionContext,
                                                          srLogger: SRLogger): Future[Long] = {

    val listCleanServiceEmails = validAfterSRJavaCheck.map(_.email)
    for {
      serviceRequestIdListClean: String <- listCleanEmailValidationApi.createBatchRequest(
        emails = listCleanServiceEmails
      ).map(_.requestId).recoverWith {
        case ex =>
          val errorMessage = ex.getMessage
          val parts = errorMessage.split(" :: ", 2)
          val failureMessage = parts.headOption.map(_.trim).getOrElse("Unknown status")
          val failureJsonResponse: JsValue = {
            parts.lift(1).map(Json.parse).getOrElse(Json.obj())
          } // Extract JSON part (default to empty JSON)

          srLogger.fatal(s"Listclean Api batch call error recoverBlockLog - ${failureJsonResponse}")

          val failedRecordFuture: Future[Long] = Future.fromTry {
            emailValidationApiToolsRecordDAO.addErrorRecordWhileCreatingBatchRequest(
              errorOccuredApiTool = EmailValidationToolV2.LISTCLEAN,
              apiResponseStatus = failureMessage.toInt,
              apiErrorResponse = failureJsonResponse
            )
          }

          failedRecordFuture.flatMap { _ =>
            Future.failed(ex)

          }


      }

      srBatchRequestId: Long <- Future.fromTry(
        emailValidationBatchRequestModel.createBatchRequest(
          validationService = EmailValidationToolV2.LISTCLEAN,
          serviceRequestId = serviceRequestIdListClean
        )
      )

      srRequestId: Long <- Future.fromTry(
        emailValidationApiToolsRecordDAO.addApiBatchRequest(
          requestId = serviceRequestIdListClean,
          emailsSentCount = listCleanServiceEmails.size,
          apiTool = EmailValidationToolV2.LISTCLEAN
        )
      )

      _: List[Long] <- Future.fromTry(
        emailValidationModel.updateQueuedStatusOnBatchRequest(
          emailValidationIds = validAfterSRJavaCheck.map(_.email_validations_id),
          emailValidationBatchRequestId = srBatchRequestId
        ))

    } yield {
      srBatchRequestId
    }


  }


  def findAndMakeBatchRequest(

                             )(
                               implicit ws: WSClient,
                               ec: ExecutionContext,
                               srLogger: SRLogger

                             ): Future[ServiceProviderBatchRequestId] = {

    Future.fromTry {
        emailValidationBatchRequestModel
          .findQueuedBatchRequests(checkedViaTool = EmailValidationToolV2.DEBOUNCE)
          .map(list => list.nonEmpty)
          .flatMap(debounceHasQueuedRequest => {

            // in debounce, we can have only one queued request at a time
            emailValidationModel.findEmailsForMakingBatchRequest(
              considerDebounce = !debounceHasQueuedRequest
            )
          })
      }
      .flatMap(emailsFound => {

        if (emailsFound.isEmpty) {

          srLogger.info("no emails found to be queued")
          Future.successful(ServiceProviderBatchRequestId(srBatchRequestIdDeBounce = None, srBatchRequestIdBouncer = None, srBatchRequestIdListClean = None))
        } else {

          for {
            validAfterSRJavaCheck: List[EmailValidationData] <- Future.fromTry(
              checkAndUpdateIfInvalidBasedOnJavaEmailAddressBatch(emailsFound = emailsFound)
            )

            bouncerValidationData: List[EmailValidationData] <- Future.successful {
              validAfterSRJavaCheck.filter(e => e.checked_via_tool == EmailValidationToolV2.BOUNCER)
            }

            deBounceValidationData: List[EmailValidationData] <- Future.successful {
              validAfterSRJavaCheck.filter(e => e.checked_via_tool == EmailValidationToolV2.DEBOUNCE)
            }

            listCleanValidationData: List[EmailValidationData] <- Future.successful {
              validAfterSRJavaCheck.filter(e => e.checked_via_tool == EmailValidationToolV2.LISTCLEAN)
            }

            srBatchRequestIdBouncer: Long <- if (bouncerValidationData.nonEmpty) {

              makeBatchRequestWithBouncerApiAndReturnRequestId(

                validAfterSRJavaCheck = bouncerValidationData
              )

            } else {
              Future.successful(0.toLong)
            }


            srBatchRequestIdDeBounce: Long <- if (deBounceValidationData.nonEmpty) {

              makeBatchRequestWithDeBounceApiAndReturnRequestId(

                validAfterSRJavaCheck = deBounceValidationData
              )

            } else {
              Future.successful(0.toLong)
            }

            srBatchRequestIdListClean: Long <- if (listCleanValidationData.nonEmpty) {
              makeBatchRequestWithListCleanApiAndReturnRequestId(
                validAfterSRJavaCheck = listCleanValidationData
              )
            } else {
              Future.successful(0.toLong)
            }

          } yield {

            srLogger.debug(
              s"srBatchRequestIdBouncer: $srBatchRequestIdBouncer :: srBatchRequestIdDeBounce: $srBatchRequestIdDeBounce :: srBatchRequestIdListClean: $srBatchRequestIdListClean"
            )
            ServiceProviderBatchRequestId(
              srBatchRequestIdDeBounce = Option(srBatchRequestIdDeBounce),
              srBatchRequestIdBouncer = Option(srBatchRequestIdBouncer),
              srBatchRequestIdListClean = Option(srBatchRequestIdListClean)

            )
          }
        }
      })
  }

  // PROSPECTS_EMAILS_TODO_UPDATE

  def checkStatusOfBatchRequestAndUpdateValidationResponse(
                                                            serviceRequestId: String,
                                                            checked_via_tool: EmailValidationToolV2
                                                          )(
                                                            implicit ws: WSClient,
                                                            ec: ExecutionContext,
                                                            srLogger: SRLogger

                                                          ): Future[Boolean] = {


    // Currently we only have on batch-based email validation provider, so just hardcoding that below
    val emailValidationApiProvider = checked_via_tool match {
      case EmailValidationToolV2.BOUNCER => bouncerEmailValidationApi
      case EmailValidationToolV2.DEBOUNCE => deBouncerEmailValidationApi
      case EmailValidationToolV2.LISTCLEAN => listCleanEmailValidationApi
      case EmailValidationToolV2.SMARTREACH_INTERNAL => throw new Exception(s"No equivalent API file found for $checked_via_tool")

    }

    emailValidationApiProvider.checkIfBatchRequestCompleted(
        requestId = serviceRequestId
      )
      .flatMap(isCompleted => {
        if (!isCompleted) {

          Future.fromTry(
            emailValidationBatchRequestModel.updateBatchRequestStatus(
              serviceRequestId = serviceRequestId,
              newStatus = EmailValidationBatchRequestStatusV2.QUEUED,
              validationService = checked_via_tool
            )
          ).map(_ => false)

        } else {

          val logger = srLogger.appendLogRequestId("checkStatusOfBatchRequestAndUpdateValidationResponse ")

          for {

            // todo nxd: call made here to bouncer
            results <- {

              val res = emailValidationApiProvider.fetchBatchRequestResults(
                RequestId = serviceRequestId
              )

              // logger.debug(s"[REMOVELOG] step1 serviceRequestId: $serviceRequestId :: res: $res")

              res
            }

            _: Int <- Future.fromTry(
              emailValidationDAOService.getBatchResultsAndUpdateIntoTable(
                requestId = serviceRequestId,
                apiUsedForValidation = checked_via_tool,
                deliverableEmailsCount = results.count(_.srEmailValidityStatus == SREmailValidityStatus.DELIVERABLE),
                riskyEmailsCount = results.count(_.srEmailValidityStatus == SREmailValidityStatus.RISKY),
                unknownEmailsCount = results.count(_.srEmailValidityStatus == SREmailValidityStatus.UNKOWN),
                invalidEmailsCount = results.count(_.srEmailValidityStatus == SREmailValidityStatus.UNDELIVERABLE)
              )

            )

            // nxd:notes this is about updating the analysis
            // need to put our code here
            // which checks the analysis results
            // and updates risky/unknown to undeliverable
            // if the last analysis had a high bounce rate in that category
            prospectValidationResult <- Future.fromTry(
              transformAndSaveEmailValidationResult.saveEmailValidationApiResultV2(
                data = EmailValidationBatchResultForSaving(
                  validationResults = results,
                  validationService = checked_via_tool,
                  requestId = serviceRequestId
                ),
                logger = srLogger
              )
            )

            campaignInitiatedValidationResults: List[ProspectEmailValidationResultWithTeamIdAndAnalysisId] =
              prospectValidationResult
                .filter(_.validationInitiator == EmailValidationInitiator.InitiatedByCampaign)

            _ <- Future.fromTry(
              prospectServiceV2.updateEmailValidationDataV2(
                data = campaignInitiatedValidationResults,
                Logger = srLogger,
                emailValidationInitiatorType = EmailValidationInitiator.InitiatedByCampaign
              ).map(updatedEmails => {
                updatedEmails._2.map(prospectsWithInvalidEmail => {
                  mqWebhookEmailInvalid.publish(
                    MQWebhookEmailInvalidMsg(
                      accountId = prospectsWithInvalidEmail.accountId,
                      teamId = prospectsWithInvalidEmail.teamId,
                      prospectIds = Seq(prospectsWithInvalidEmail.prospectId)
                    )
                  )
                })
              })
            )

            leadFinderInitiatedValidationResults: List[ProspectEmailValidationResultWithTeamIdAndAnalysisId] =
              prospectValidationResult
                .filter(_.validationInitiator == EmailValidationInitiator.InitiatedByLeadFinder)

            _: Int <- Future.fromTry {

              leadFinderValidationService.updateLeadFinderValidationResults(
                data = leadFinderInitiatedValidationResults
              )

            }

            _ <- Future.fromTry(
              emailValidationBatchRequestModel.updateBatchRequestStatus(
                serviceRequestId = serviceRequestId,
                newStatus = EmailValidationBatchRequestStatusV2.COMPLETED,
                validationService = checked_via_tool
              )
            )


          } yield {
            true
          }


        }
      })
  }

  private def updateStatusToSentForValidation(
                                               teamId: TeamId,
                                               prospectsToBeScheduledForValidation: EmailsForValidationWithInitiator
                                             )(
                                               implicit logger: SRLogger,
                                             ): Try[Int] = {

    prospectsToBeScheduledForValidation match {

      case prospects: EmailsForValidationWithInitiator.ProspectEmailsForValidation =>

        // update prospect status as (sent for email validation)
        prospectServiceV2.markAsSentForValidatingEmail(
          prospectIdsAndEmails = prospects.entitiesForValidation.map(p => (p.id.id, p.email)),
          teamId = teamId,
          Logger = logger
        ) match {

          case Failure(e) =>

            logger.error(s"[EmailValidationService] sendProspectsForValidation FATAL markAsSentForValidatingEmail error: countProspectIdsPublishedForValidation: ${prospects.entitiesForValidation.length} :: ${LogHelpers.getStackTraceAsString(e)}")

            Failure(e)

          case Success(totalUpdatedProspects) =>

            logger.info(s"[EmailValidationService] sendProspectsForValidation markAsSentForValidatingEmail $totalUpdatedProspects :: pids: ${prospects.entitiesForValidation.size} prospects as sent_for_validation ")

            Success(totalUpdatedProspects)

        }


      case leads: EmailsForValidationWithInitiator.LeadEmailsForValidation =>

        leadFinderValidationService.markLeadEmailsSentForValidation(
          teamId = teamId,
          leadIdAndEmails = leads.entitiesForValidation.map(lf => LeadIdAndEmail(id = lf.id, email = lf.email))
        ) match {

          case Failure(e) =>

            logger.error(s"[EmailValidationService] sendProspectsForValidation FATAL markLeadEmailsSentForValidation error: countProspectIdsPublishedForValidation: ${leads.entitiesForValidation.length} :: leadValidationBatchReqId: ${leads.leadValidationBatchReqId} :: leads: ${leads.entitiesForValidation.map(_.email)} :: ${LogHelpers.getStackTraceAsString(e)}")

            Failure(e)

          case Success(totalUpdatedProspects) =>

            logger.info(s"[EmailValidationService] sendProspectsForValidation markLeadEmailsSentForValidation $totalUpdatedProspects :: pids: ${leads.entitiesForValidation.map(_.id)} leads as sent_for_validation ")

            Success(totalUpdatedProspects)

        }


    }

  }


  // send either argument
  // nxd notes : called from 3 places - we expect all 3 to have campaign_id
  //              ProspectController
  //              ProspectUpdateService
  //              SchedulerEmailCronService
  //  this is about updating the campaign id when first creating
  //  the email validation record.

  // PROSPECTS_EMAILS_TODO_UPDATE / PROSPECTS_EMAILS_TODO_READ_CLEANED
  /*
   *  Use case: ProspectController.createOrUpdateOne - end-user uploads a single prospect via the form
   *  Use case : All prospects uploaded via csv are 1st sent for validation
   */
  def sendProspectsForValidation(

                                  // 21-Nov-2023: for new campaigns and manually added prospects,
                                  // we will send highPriority as true, and validate them faster
                                  priority: EmailValidationPriority,

                                  logger: SRLogger,
                                  accountId: Long,
                                  teamId: TeamId,
                                  orgId: Long,
                                  isAgency: Boolean,

                                  // prospectsForValidation: Seq[ProspectForValidation] = Seq(),
                                  // prospectIdsForValidation: Seq[Long] = Seq(),
                                  //
                                  // initiatorWithValue: EmailValidationInitiator.EmailValidationInitiatorWithValue

                                  idsOrEmailsForValidation: IdsOrEmailsForValidation,

                                ): Try[Int] = Try {

    val emailsForValidationWithInitiator: EmailsForValidationWithInitiator = idsOrEmailsForValidation match {

      case prospectIdsForValidation: IdsOrEmailsForValidation.ProspectIdsForValidation =>

        val prospectsForValidation: Seq[ProspectForValidation] = prospectServiceV2.findFromMasterForValidating(
          prospectIds = prospectIdsForValidation.prospectIds.map(_.id),
          teamId = teamId,
          logger = logger
        ).get

        EmailsForValidationWithInitiator.ProspectEmailsForValidation(
          entitiesForValidation = prospectsForValidation.map(EmailForValidation.fromProspectForValidation),
          initiatorCampaignId = prospectIdsForValidation.initiatorCampaign
        )

      case entitiesForValidation: IdsOrEmailsForValidation.EntitiesForValidation =>

        entitiesForValidation.emailsForValidations

    }


    if (emailsForValidationWithInitiator.entitiesForValidation.isEmpty) {

      0

    } else {

      // extract emails from prospects to get prospectValidationResult of all the prospect emails
      val prospectEmails: Seq[String] = emailsForValidationWithInitiator.entitiesForValidation.map(p => p.email.trim.toLowerCase).distinct

      val tryOfPreviouslyValidatedEmailResults = for {

        previouslyValidatedEmailResults: Seq[ProspectEmailValidationResult] <-
          emailValidationDAOService.getPreviouslyValidatedEmailResults(prospectEmails = prospectEmails)

        previousEmailValidationAnalysis: Seq[ProspectEmailValidationResultWithTeamIdAndAnalysisId] =
          previouslyValidatedEmailResults.map { res =>
            ProspectEmailValidationResultWithTeamIdAndAnalysisId(
              emailDeliveryAnalysisId = None, // no analysis is done in this path
              email = res.email,
              emailValidationId = res.emailValidationId,
              isValid = res.isValid,

              validationInitiator = EmailValidationInitiator.getEmailValidationInitiatorTypeFromValue(
                initiatorWithValue = emailsForValidationWithInitiator.getInitiatorWithValue
              ),

              teamId = teamId,
            )
          }

        _: Int <- emailsForValidationWithInitiator.getInitiatorWithValue match {

          case _: EmailValidationInitiator.InitiatedByCampaign =>

            prospectServiceV2.updateEmailValidationDataV2(
              data = previousEmailValidationAnalysis,
              Logger = logger,
              emailValidationInitiatorType = EmailValidationInitiator.InitiatedByCampaign
            ).map(
              updatedEmails => {
                updatedEmails._2.map(
                  prospectsWithInvalidEmail => {
                    mqWebhookEmailInvalid.publish(
                      MQWebhookEmailInvalidMsg(
                        accountId = prospectsWithInvalidEmail.accountId,
                        teamId = prospectsWithInvalidEmail.teamId,
                        prospectIds = Seq(prospectsWithInvalidEmail.prospectId)
                      )
                    )
                  }
                )

                val prospectIdsWithInvalidEmail: Seq[ProspectId] = updatedEmails._2
                  .filter(_.invalidEmail)
                  .map(updatedEmail => ProspectId(updatedEmail.prospectId))

                mqAutoUpdateProspectCategoryPublisher.publish(
                  msg = MqAutoUpdateProspectCategoryMsg(
                    teamId = teamId,
                    doerAccountId = AccountId(accountId),
                    prospectIds = prospectIdsWithInvalidEmail,
                    newProspectCategory = Some(ProspectCategory.DELIVERY_FAILED), //Bad contact info
                    replySentimentUuid = None
                  )
                )
                updatedEmails._1
              }
            )

          case _: EmailValidationInitiator.InitiatedByLeadFinder =>

            leadFinderValidationService.updateLeadFinderValidationResults(
              data = previousEmailValidationAnalysis.toList
            )(logger = logger) match {

              case Failure(exception) =>

                logger.shouldNeverHappen(
                  msg = s"Failed to updateLeadFinderValidationResults with previousEmailValidationAnalysis - teamId: $teamId :: accountId: $accountId :: data: $previousEmailValidationAnalysis",
                  err = Some(exception),
                )

                Failure(exception)

              case Success(value) =>

                Success(value)

            }

        }

      } yield previouslyValidatedEmailResults

      val previouslyValidatedEmailResults: Seq[ProspectEmailValidationResult] =
        tryOfPreviouslyValidatedEmailResults match {
          case Failure(exception) =>
            logger.fatal(s"FATAL tryOfPreviouslyValidatedEmailResults failed", err = exception)
            Seq()

          case Success(previouslyValidatedEmailResults) =>
            logger.debug(
              s"found previouslyValidatedEmailResults, ignoring those: ${previouslyValidatedEmailResults.size}"
            )
            previouslyValidatedEmailResults
        }

      // list of emails to be filtered from prospectsToBeScheduledForValidation,
      // as they are already validated in the last 15 days.
      val previouslyValidatedEmails: Seq[String] =
        previouslyValidatedEmailResults.map(p => p.email.trim.toLowerCase)

      val prospectsToBeScheduledForValidation: EmailsForValidationWithInitiator = EmailValidationService.filterPreviouslyValidatedEmailsAndReconstruct(
        emailsForValidationWithInitiator = emailsForValidationWithInitiator,
        previouslyValidatedEmails = previouslyValidatedEmails,
      )

      if (prospectsToBeScheduledForValidation.entitiesForValidation.isEmpty) {

        emailsForValidationWithInitiator match {

          case prospects: EmailsForValidationWithInitiator.ProspectEmailsForValidation =>

            logger.error(s"FATAL sendProspectsForValidation: prospectsToBeScheduledForValidation.isEmpty: prospectIds: ${prospects.entitiesForValidation.map(_.id)}")


          case leads: EmailsForValidationWithInitiator.LeadEmailsForValidation =>

            logger.error(s"FATAL sendProspectsForValidation: prospectsToBeScheduledForValidation.isEmpty: leadIds: ${leads.entitiesForValidation.map(_.id)}")

        }

        0

      } else {

        /*
        val orgIdsForBouncer = Seq(
          2215 // leadroll
        )

        val prospectIdsPublishedForValidation: Seq[Long] = if (
          true // pushing all requests to BCR
          /*
          isAgency ||
            orgIdsForBouncer.contains(orgId)
          */
        ) {

          EmailValidationModel.addForBatchRequest(
            emails = prospectsToBeScheduledForValidation.map(_.email.toLowerCase.trim).distinct,
            validationService = EmailValidationTool.BOUNCER,
            doerAccountId = accountId,
            teamId = teamId
          ) match {

            case Failure(e) =>
              Logger.error(s"FATAL addForBatchRequest: ${Helpers.getStackTraceAsString(e)} :: prospectsToBeScheduledForValidation: $prospectsToBeScheduledForValidation")
              Seq()

            case Success(_) =>
              prospectsToBeScheduledForValidation.map(_.id)
          }


        } else {


          prospectsToBeScheduledForValidation
            .map { p =>

              MQMBLEmailValidator.publish(MQMBLEmailValidatorMessage(email = p.email, account_id = accountId, team_id = teamId)) match {

                case Failure(e) =>
                  Logger.error(s"[EmailValidationService] sendProspectsForValidation publish error: ${p.id} :: ${p.email} :: teamId: tid_$teamId :: ${Helpers.getStackTraceAsString(e)}")
                  None

                case Success(_) =>
                  Logger.info(s"[EmailValidationService] sendProspectsForValidation publish success: ${p.id} :: ${p.email} :: teamId: tid_$teamId")
                  Some(p.id)

              }

            }
            .filter(_.isDefined)
            .map(_.get)
        }
        */

        /*

         20-Mar-2024:The Api selection for emailvalidation will be handled by Cron while picking and sending for validation

        val emailValidationApiProvider: EmailValidationApiScheduler = {



          /* NOTE: 7-dec-2023: pass everything to bouncer, debounce is down
          9-dec-2023: debounce issue is resolved, so we are restarting sending there

           */
          val percentToBouncer: Double = {

            priority match {
            case EmailValidationPriority.Low =>

              // NOTE: sending 10% requests to bouncer
//                0.1
              1

            case EmailValidationPriority.Medium =>

              // NOTE: sending 50% requests to bouncer
//                0.5
              1
            case EmailValidationPriority.High =>

              // NOTE: sending 100% requests to bouncer
              1



            }
          }


          val randomValue = srRandomUtils.getRandomValueForSelectingEmailValidationProvider

          if(randomValue<percentToBouncer) bouncerEmailValidationApi else deBouncerEmailValidationApi

        }

         */

        emailValidationModel.addForBatchRequest(
          emails = prospectsToBeScheduledForValidation.entitiesForValidation.map(_.email.toLowerCase.trim).distinct,
          doerAccountId = accountId,
          teamId = teamId.id,
          priority = priority,
          initiatorWithValue = prospectsToBeScheduledForValidation.getInitiatorWithValue,
        ) match {

          case Failure(e) =>

            logger.error(s"FATAL addForBatchRequest: ${LogHelpers.getStackTraceAsString(e)} :: prospectsToBeScheduledForValidation: $prospectsToBeScheduledForValidation")

            // We were failing silently here by returning empty Seq()

            0

          case Success(_) =>

            updateStatusToSentForValidation(
              teamId = teamId,
              prospectsToBeScheduledForValidation = prospectsToBeScheduledForValidation,
            )(
              logger = logger,
            ) match {

              case Failure(e) =>

                throw e

              case Success(totalUpdatedProspects) =>

                totalUpdatedProspects

            }

        }
      }
    }
  }
}

object EmailValidationService {

  def filterPreviouslyValidatedEmailsAndReconstruct(
                                                     emailsForValidationWithInitiator: EmailsForValidationWithInitiator,
                                                     previouslyValidatedEmails: Seq[String]
                                                   ): EmailsForValidationWithInitiator = {

    emailsForValidationWithInitiator match {

      case p: EmailsForValidationWithInitiator.ProspectEmailsForValidation =>

        val emailsToBeSentForValidation = filterPreviouslyValidatedEmails(
          entitiesForValidation = p.entitiesForValidation,
          previouslyValidatedEmails = previouslyValidatedEmails
        )

        EmailsForValidationWithInitiator.ProspectEmailsForValidation(
          entitiesForValidation = p.entitiesForValidation.filter(x => emailsToBeSentForValidation.map(_.email).contains(x.email)),
          initiatorCampaignId = p.initiatorCampaignId
        )

      case lf: EmailsForValidationWithInitiator.LeadEmailsForValidation =>

        val emailsToBeSentForValidation = filterPreviouslyValidatedEmails(
          entitiesForValidation = lf.entitiesForValidation,
          previouslyValidatedEmails = previouslyValidatedEmails
        )

        EmailsForValidationWithInitiator.LeadEmailsForValidation(
          entitiesForValidation = lf.entitiesForValidation.filter(x => emailsToBeSentForValidation.map(_.email).contains(x.email)),
          leadValidationBatchReqId = lf.leadValidationBatchReqId
        )


    }

  }

  def filterPreviouslyValidatedEmails(
                                       entitiesForValidation: Seq[EmailForValidation],
                                       previouslyValidatedEmails: Seq[String]
                                     ): Seq[EmailForValidation] = {

    entitiesForValidation
      .filter(
        p =>
          !p.email_checked &&
            !(
              p.email_sent_for_validation &&

                // debounce validation can take 5-6 hours sometimes
                p.email_sent_for_validation_at.get.isAfter(DateTime.now().minusHours(6))
              )
      )
      .filter(p => !previouslyValidatedEmails.contains(p.email.trim.toLowerCase))

  }

  def validateIEmailAddress(email: String)(using Logger: SRLogger): Boolean = {

    val iEmailCheck = for {
      iEmail: Seq[IEmailAddress] <- IEmailAddress.parse(emailsStr = email)

      _: Array[Address] <- Try {
        IEmailAddress.toJavaAddressArray(iEmail)
      }
    } yield {
      true
    }

    iEmailCheck match {
      case Success(value) => value
      case Failure(exception) =>
        Logger.fatal(s"Failed the IEmailAddress validation", exception)
        false
    }
  }

  def validateEmailFormat(email: String): Boolean = validator.routines.EmailValidator.getInstance().isValid(email)

  def validateDomain(domain: String): Boolean = validator.routines.DomainValidator.getInstance().isValid(domain)

  //FIXME : Need to create a case class with members (emailname: String,emailDomain: String)
  def getLowercasedNameAndDomainFromEmail(
                                           email: String
                                         )(
                                           using Logger: SRLogger
                                         ): (String, String) = {

    Try {
      val splits = email.trim.toLowerCase.split('@')
      (splits(0), splits(1))
    } match {

      case Failure(e) =>
        Logger.error(s"getLowercasedNameAndDomainFromEmail: ${email}}", err = e)

        ("", "")

      case Success(value) =>
        value

    }

  }

  def getLowercasedCompanyNameFromEmail(
                                         email: String
                                       )(
                                         implicit Logger: SRLogger
                                       ): String = {
    val domain = getLowercasedNameAndDomainFromEmail(email = email)._2

    Try {
      val splits = domain.trim.toLowerCase.split('.')
      splits(0)
    } match {

      case Failure(e) =>
        Logger.error(s"getLowercasedCompanyNameFromDomain: ${domain}}", err = e)

        ""

      case Success(value) =>
        value

    }

  }

  def getNameDomainAndAliasFromEmailV2(email: String): EmailSplit = {

    val em = email.toLowerCase.trim

    val at = em.lastIndexOf("@")
    val fullname = em.substring(0, at)
    val domain = em.substring(at + 1)

    val aliasFrom = fullname.indexOf("+")
    val nameWithoutAlias = if (aliasFrom > -1) fullname.substring(0, aliasFrom) else fullname


    EmailSplit(
      fullname = fullname,
      nameWithoutAlias = nameWithoutAlias,
      domain = domain
    )

  }

  /*
  29 Jul 2024 :
  setEmailValidationPriorityOnBasisOfCampaignStartTime Function explanation :
  Function takes campaign details and current time according to the campaign timezone
   for eg if the campaign is of timezone usa and currently the time is 3.00 PM in usa then the
   currentTimeAccordingToCampaignTimezone = 15.00 from usa timezone

   currentDate - currentDate according to campaign timezone
   Priorities will be set as  -
   Case 1 :
    If currentTimeAccordingToCampaignTimezone is more than 4 hours before the campaign to start then the priority will be low
     for eg .) If campaign starts at 9.00 PM in usa timezone and current time is 3.00 PM in usa
              then the priority will be set as low
   Case 2 :
    If the currentTimeAccordingToCampaignTimezone is less than 4 hours then the priority will be medium
    for eg .) If campaign starts at 9.00 PM in usa timezone and current time is 7.00 PM in usa
              then the priority will be set as medium
   Case 3 :
    If the currentTimeAccordingToCampaignTimezone is in between the campaign start time and end time then the priority will be medium
    for eg .) If campaign starts at 9.00 PM in usa timezone and ends at 11.00 PM current time is 10.00 PM in usa
              then the priority will be set as medium
   */

  def setEmailValidationPriorityOnBasisOfCampaignStartTime(
                                                            campaign: CampaignForScheduling.CampaignForSchedulingEmail,
                                                            currentTimeAccordingToCampaignTimezone: DateTime
                                                          )(using logger: SRLogger): EmailValidationPriority = {

    val currentDate: DateTime = currentTimeAccordingToCampaignTimezone.withTimeAtStartOfDay()

    // Convert the dailyFromTime to a DateTime
    /*
    For campaignStartTime and campaignEndTime : To determine the campaignStartTime and EndTime there can be two cases
    Case 1 : The current time is after the campaignEndTime for that day,then in such we should consider the campaignStartTime
            of the next day
            For eg.) If a campaign starts at 25 July 9.00 AM and ends at 25 july 5.00 PM and the current time is 7.00PM
            then the campaignStartTime to be calculated
            should be  of 26 july 9.00 AM so the plusDays(1) is used.

     Case 2 : The current time is before the campaignEndTime for that day,then in such we should consider the campaignStartTime
            of the same day
            For eg.) If a campaign starts at 25 July 9.00 AM and ends at 25 july 5.00 PM and the current time is 2.00PM
            then the campaignStartTime to be calculated should be of 25 July only.
     */
    val (campaignStartTime: DateTime, campaignEndTime: DateTime) = if (currentDate.plusSeconds(campaign.daily_till_time).isBefore(currentTimeAccordingToCampaignTimezone)) {
      (currentDate.plusSeconds(campaign.daily_from_time).plusDays(1), // campaign start time
        currentDate.plusSeconds(campaign.daily_till_time).plusDays(1)) // campaign end time
    } else {
      (currentDate.plusSeconds(campaign.daily_from_time), // Campaign start time
        currentDate.plusSeconds(campaign.daily_till_time)) // campaign end time
    }

    val fourHoursBefore: DateTime = campaignStartTime.minusHours(4)

    val isValidationTimeBetweenCampaignStartAndEndTime: Boolean =
      if (currentTimeAccordingToCampaignTimezone.isAfter(campaignStartTime) && currentTimeAccordingToCampaignTimezone.isBefore(campaignEndTime)) {
        true
      } else {
        false
      }
    if (currentTimeAccordingToCampaignTimezone.isAfter(fourHoursBefore)) {
      logger.info(s"EmailValidationService.setEmailValidationPriorityOnBasisOfCampaignStartTime campaignId : ${campaign.campaign_id} campaign_starting_at : ${campaignStartTime} priority : ${EmailValidationPriority.Medium.toString}")
      EmailValidationPriority.Medium
    } else if (isValidationTimeBetweenCampaignStartAndEndTime) {
      logger.info(s"EmailValidationService.setEmailValidationPriorityOnBasisOfCampaignStartTime campaignId : ${campaign.campaign_id} campaign_starting_at : ${campaignStartTime} priority : ${EmailValidationPriority.Medium.toString}")
      EmailValidationPriority.Medium
    } else {
      logger.info(s"EmailValidationService.setEmailValidationPriorityOnBasisOfCampaignStartTime campaignId : ${campaign.campaign_id} campaign_starting_at : ${campaignStartTime} priority : ${EmailValidationPriority.Low.toString}")
      EmailValidationPriority.Low
    }
  }
}