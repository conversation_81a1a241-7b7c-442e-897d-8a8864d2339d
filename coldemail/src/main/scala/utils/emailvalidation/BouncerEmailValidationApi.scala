package utils.emailvalidation

import api.AppConfig
import api.accounts.TeamId
import api.accounts.models.AccountId
import api.campaigns.services.CampaignId
import api.prospects.ProspectForValidation
import play.api.Logging
import play.api.libs.json.{JsValue, Json}
import play.api.libs.ws.WSClient
import utils.emailvalidation.models.{EmailValidationPriority, EmailValidationToolV2}
import utils.{Help<PERSON>, SRLogger}
import utils.helpers.LogHelpers

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}
import play.api.libs.ws.WSBodyWritables._

case class BatchRequestResponse(
 requestId: String,
 response: JsValue,
 status: String
)

class BouncerEmailValidationApi(
  emailValidationApiToolsRecordDAO: EmailValidationApiToolsRecordDAO
)
  extends EmailValidationApiProvider
    with EmailValidationApiScheduler
    with Logging {

  /*
  case class ValidationResponseDomain(
    name: String,
    acceptAll: String,
    disposable: String,
    free: String
  )
  object ValidationResponseDomain {
    given format = Json.format[ValidationResponseDomain]
  }

  case class ValidationResponseAccount(
    role: String,
    disabled: String,
    fullMailbox: String
  )
  object ValidationResponseAccount {
    given format = Json.format[ValidationResponseAccount]
  }

  case class ValidationResponse(
    email: String,
    name: String,
    status: String,
    reason: String,
    domain: ValidationResponseDomain,
    account: ValidationResponseAccount,
    provider: String
  )
  object ValidationResponse {
    given format = Json.format[ValidationResponse]
  }
  */


  override def getInitialSREmailValidityStatusFromApiResponse(
    apiResponse: JsValue
  ): Try[SREmailValidityStatus] = Try {


    val reason = (apiResponse \ "reason").as[String]

    if (
      reason == "accepted_email"
    ) {

      SREmailValidityStatus.DELIVERABLE

    } else if (
      reason == "dns_error" ||
        reason == "invalid_domain" ||
        reason == "invalid_email" ||
        reason == "rejected_email" ||
        reason == "unavailable_smtp"
    ) {

      SREmailValidityStatus.UNDELIVERABLE

    } else if (
      reason == "low_deliverability" ||
        reason == "low_quality"
    ) {

      SREmailValidityStatus.RISKY

    } else if (
      reason == "unknown"
    ) {

      SREmailValidityStatus.UNKOWN

    } else {

      logger.error(s"unmatched bouncer reason: $reason")

      SREmailValidityStatus.UNKOWN

    }

  }

  override def getEmailValidationResult(
    apiResponse: JsValue
  ): Try[EmailValidationResult] = {

    getInitialSREmailValidityStatusFromApiResponse(
      apiResponse = apiResponse
    )
      .map(validityStatus => {
        val email = (apiResponse \ "email").as[String].toLowerCase.trim

        EmailValidationResult(
          email = email,
          fullJson = apiResponse,
          isValid = validityStatus != SREmailValidityStatus.UNDELIVERABLE,
          srEmailValidityStatus = validityStatus
        )
      })

  }


  def createBatchRequest(
    emails: Seq[String]
  )(
    implicit ws: WSClient,
    ec: ExecutionContext,
    srLogger: SRLogger

  ): Future[BatchRequestResponse] = {

    val log = srLogger.appendLogRequestId(
      appendLogReqId = "BouncerEmailValidationService.createBatchRequest"
    )

    val url = s"https://api.usebouncer.com/v1/email/verify/batch"

    val postJsonData = emails.foldLeft(Json.arr()) { case (array, email) =>

      array ++ Json.arr(
        Json.obj(
          "email" -> email
        )
      )
    }

    ws.url(url)
      .addHttpHeaders(
        "Content-Type" -> "application/json",
        "x-api-key" -> AppConfig.bouncerApiKey
      )
      .post(
        postJsonData
      )
      .map(response => {

        if (response.status != 200) {
          log.error(s"BouncerEVA.createBatchRequest error response called: ${response.status} res body: ${response.body}")


            throw new Exception(s"${response.status}  ::  ${response.json}")

        } else {

          val res = response.json
          val requestId = (res \ "requestId").as[String]
          val status = (res \ "status").as[String]


          BatchRequestResponse(
            requestId = requestId,
            response = res,
            status = status
          )
        }
      })

  }


  def checkIfBatchRequestCompleted(
    requestId: String
  )(
    implicit ws: WSClient,
    ec: ExecutionContext,
    srLogger: SRLogger

  ): Future[Boolean] = {

    val log = srLogger.appendLogRequestId(
      appendLogReqId = "BouncerEmailValidationService.checkBatchRequestStatus"
    )

    val url = s"https://api.usebouncer.com/v1/email/verify/batch/${requestId}/status"


    ws.url(url)
      .addHttpHeaders(
        "Content-Type" -> "application/json",
        "x-api-key" -> AppConfig.bouncerApiKey
      )
      .get()
      .map(response => {

        if (response.status != 200) {
          log.error(s"error response called: ${response.status} res body: ${response.body}")

          throw new Exception(s"${response.body}")

        } else {

          val res = response.json
          val requestId = (res \ "requestId").as[String]
          val status = (res \ "status").as[String]

          status == "completed"

        }
      })

  }


  def fetchBatchRequestResults(
    requestId: String
  )(
    implicit ws: WSClient,
    ec: ExecutionContext,
    srLogger: SRLogger

  ): Future[Seq[EmailValidationResult]] = {

    val log = srLogger.appendLogRequestId(
      appendLogReqId = "BouncerEmailValidationService.fetchBatchRequestResults"
    )

    val url = s"https://api.usebouncer.com/v1/email/verify/batch/${requestId}?download=all"

    ws.url(url)
      .addHttpHeaders(
        "Content-Type" -> "application/json",
        "x-api-key" -> AppConfig.bouncerApiKey
      )
      .get()
      .map(response => {

        if (response.status != 200) {
          log.error(s"error response called: ${response.status} res body: ${response.body}")

          throw new Exception(s"${response.body}")

        } else {

          val results = response.json.asOpt[Seq[JsValue]]

          if (results.isEmpty) {

            throw new Exception(s"Error parsing results: json response: ${response.json}")


          } else {


            results.get
              .map(emJs => {

                getEmailValidationResult(
                  apiResponse = emJs
                ).get

                /*
                val email = (emJs \ "email").as[String].toLowerCase.trim
                val status = (emJs \ "status").as[String]

                EmailValidationResult(
                  email = email,
                  fullJson = emJs,
                  isValid = status != "undeliverable"
                )
                */
              })

          }

        }
      })

  }

}
