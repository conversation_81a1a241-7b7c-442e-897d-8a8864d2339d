package utils.emailvalidation.dao_service

import org.joda.time.{DateTime, Minutes}
import utils.{Help<PERSON>, SRLogger}
import utils.emailvalidation.models.EmailValidationToolV2

import scala.util.{Failure, Success, Try}
import utils.emailvalidation.{EmailValidationApiToolsRecordDAO, EmailValidationModel, ProspectEmailValidationResult}

class EmailValidationDAOService(emailValidationModel: EmailValidationModel,
                                emailValidationApiToolsRecordDAO: EmailValidationApiToolsRecordDAO) {

  def getPreviouslyValidatedEmailResults(prospectEmails: Seq[String]): Try[Seq[ProspectEmailValidationResult]] = {

    val previouslyValidatedEmailResultsSeq =
      prospectEmails
        .grouped(50)
        .toSeq
        .map(pgrp => {
          emailValidationModel.getResultsForPreviouslyValidatedEmails(emails = pgrp)
        })

    Helpers.seqTryToTrySeq(previouslyValidatedEmailResultsSeq)
      .map(_.flatten)
  }


    def changingValidationStatusTo_ToBeQueued(
                                              validationToolWhichIsDown:EmailValidationToolV2
                                              )(using srLogger: SRLogger):Try[Long]=Try{
        emailValidationModel.getEmailsToChangeStatusToToBeQueued(validationToolWhichIsDown = validationToolWhichIsDown) match {
            case Success(emailList) =>
                emailList
                .grouped(500)
                .toList
                .map(emailBatch=>
                            emailValidationModel.updateProcessStatusTo_ToBeQueued(emails = emailBatch)
                ).map{
                      case Success(value)=>
                        value
                      case Failure(exception)=>
                          srLogger.fatal(s"sql exception, emailValidationModel.updateProcessStatusTo_ToBeQueued",exception)
                          0
                        //Ignoring the fails because the items will be repicked during the next cycle
                  }.sum

            case Failure(exception) =>
                srLogger.fatal(s"sql exception, emailValidationModel.getEmailsToChangeStatusToToBeQueued",exception)
                throw exception
        }
    }


    def switchingValidationTooltoBackupTool(
                                         validationToolWhichIsDown:EmailValidationToolV2,
                                         validationTool_Backup: EmailValidationToolV2)
                                           (using srLogger:SRLogger):Try[Int]=Try{
        emailValidationModel.getEmailsToSwitchToBackupVerificationTool(validationToolWhichIsDown = validationToolWhichIsDown) match {
            case Success(emailList)=>
                emailList
                .grouped(500)
                .toList
                .map(emailBatch=>
                        emailValidationModel.switchStuckEmailsToBackupVerificationTool(
                            emails = emailBatch,
                            validationToolWhichIsAvailable = validationTool_Backup)

                ).map{
                      case Success(value)=>
                                        value
                      case Failure(exception)=>
                          srLogger.fatal(s"sql exception, emailValidationModel.switchStuckEmailsToBackupVerificationTool",exception)
                          //Ignoring the fails because the items will be repicked during the next cycle
                        0
                  }.sum

            case Failure(exception)=>
                srLogger.fatal(s"sql exception, emailValidationModel.getEmailsToSwitchToBackupVerificationTool",exception)

                throw exception


        }




    }



    def assigningToolForValidation(emailList:List[String],
                                   validationToolToBeAssigned:EmailValidationToolV2)
                                  (using srLogger:SRLogger):Try[Int]=Try{

        if(emailList.isEmpty){
            0
        }else{
            emailList
              .grouped(500)
              .toList
              .map(emailBatch=>
                        emailValidationModel.assignToolForValidation(emails = emailBatch,
                            validationToolV2 = validationToolToBeAssigned)

              ).map{
                  case Success(value)=>value
                  case Failure(exception)=>
                      srLogger.fatal(s"sql exception, emailValidationModel.assignToolForValidation",exception)
                      //Ignoring the fails because the items will be repicked during the next cycle
                      0
              }.sum

        }


    }

    def getBatchResultsAndUpdateIntoTable(
                                           requestId: String,
                                           apiUsedForValidation:EmailValidationToolV2,
                                           deliverableEmailsCount:Long,
                                           riskyEmailsCount:Long,
                                           unknownEmailsCount:Long,
                                           invalidEmailsCount:Long
                                         ):Try[Int] = {

        val toolWasDownFor:Int = emailValidationApiToolsRecordDAO.getwentDownTime(apiToCheckAndGetDownTime = apiUsedForValidation) match {
            case Success(Some(downTime))=>
                 Minutes.minutesBetween(DateTime.now(),downTime).getMinutes

            case Success(None) =>
                 0

            case Failure(exception)=>
                throw new Exception("Failed to get records")



        }

        emailValidationApiToolsRecordDAO.updateBatchRequestResult(
                                                                 requestId = requestId,
                                                                 apiUsedForValidation = apiUsedForValidation,
                                                                 deliverableEmailsCount = deliverableEmailsCount,
                                                                 riskyEmailsCount = riskyEmailsCount,
                                                                 unknownEmailsCount = unknownEmailsCount,
                                                                 invalidEmailsCount = invalidEmailsCount,    //whose srValidityStatus is UNDELIVERABLE
                                                                 totalTimeAPIwasDown = toolWasDownFor
                                                                 ) match {

            case Success(record_updated)=>
                 Success(record_updated)

            case Failure(exception) =>
                Failure(exception)
        }




    }

}
