package utils.emailvalidation.models

import api.campaigns.services.CampaignId
import api.lead_finder.models.LeadValidationBatchReqId

import scala.util.Try

object EmailValidationInitiator {

  private val validation_initiated_by_campaign = "validation_initiated_by_campaign"
  private val validation_initiated_by_lead_finder = "validation_initiated_by_lead_finder"


  sealed trait EmailValidationInitiatorWithValue

  case class InitiatedByCampaign(
    initiatorCampaignId: CampaignId
  ) extends EmailValidationInitiatorWithValue {

    override def toString: String = validation_initiated_by_campaign

  }

  case class InitiatedByLeadFinder(
    leadValidationBatchReqId: LeadValidationBatchReqId
  ) extends EmailValidationInitiatorWithValue {

    override def toString: String = validation_initiated_by_lead_finder

  }


  // When reading from the DB, the value of `initiatorCampaignId`
  // can be `null` if the user deletes the campaign.
  // So, when reading from the DB, we will only read the `EmailValidationInitiatorType`.
  sealed trait EmailValidationInitiatorType

  case object InitiatedByCampaign extends EmailValidationInitiatorType {

    override def toString: String = validation_initiated_by_campaign

  }

  case object InitiatedByLeadFinder extends EmailValidationInitiatorType {

    override def toString: String = validation_initiated_by_lead_finder

  }

  def fromKey(key: String): Try[EmailValidationInitiatorType] = Try {

    key match {

      case `validation_initiated_by_campaign` => InitiatedByCampaign

      case `validation_initiated_by_lead_finder` => InitiatedByLeadFinder

    }

  }

  def getEmailValidationInitiatorTypeFromValue(
    initiatorWithValue: EmailValidationInitiatorWithValue
  ): EmailValidationInitiatorType = {

    initiatorWithValue match {

      case _: InitiatedByCampaign =>

        InitiatedByCampaign

      case _: InitiatedByLeadFinder =>

        InitiatedByLeadFinder

    }

  }

}
