package utils.emailvalidation.models

import api.campaigns.services.CampaignId
import api.prospects.models.ProspectId

sealed trait IdsOrEmailsForValidation

object IdsOrEmailsForValidation {

  case class ProspectIdsForValidation(

    prospectIds: Seq[ProspectId],

    initiatorCampaign: CampaignId,

  ) extends IdsOrEmailsForValidation


  case class EntitiesForValidation(

    emailsForValidations: EmailsForValidationWithInitiator,

  ) extends IdsOrEmailsForValidation

}
