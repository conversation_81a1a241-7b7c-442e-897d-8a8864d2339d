package utils.emailvalidation.models

import scala.util.{Failure, Success, Try}

sealed trait EmailValidationProcessStatusV2

object EmailValidationProcessStatusV2 {
    private val to_be_queued = "to_be_queued"
    private val queued = "queued"
    private val completed = "completed"
    private val error = "error"

    case object TO_BE_QUEUED extends EmailValidationProcessStatusV2{

        override def toString: String =to_be_queued

    }

    case object QUEUED extends EmailValidationProcessStatusV2{

        override def toString: String =queued

    }
    case object COMPLETED extends EmailValidationProcessStatusV2{

        override def toString: String = completed

    }

    case object ERROR extends EmailValidationProcessStatusV2{

        override def toString: String = error

    }

    def fromStatus(processStatus:String):Try[EmailValidationProcessStatusV2]={
        processStatus match {
            case `to_be_queued` =>Success(TO_BE_QUEUED)
            case `queued` => Success(QUEUED)
            case `completed` => Success(COMPLETED)
            case `error` => Success(ERROR)
            case _ => Failure(new Throwable(s"Unknown status $processStatus"))
        }
    }


}


