package utils.emailvalidation

import utils.{Help<PERSON>, SRLogger}
import utils.cronjobs.spammonitor.{EmailDeliveryAnalysisRecord, EmailDeliveryAnalysisService}
import utils.emailvalidation.ValidationResultWithTeamIdAndAnalysisId.ProspectEmailValidationResultWithTeamIdAndAnalysisId
import utils.emailvalidation.models.EmailDeliveryAnalysisId
import utils.helpers.LogHelpers

import scala.util.{Failure, Success, Try}

class TransformAndSaveEmailValidationResult(
        emailValidationModel: EmailValidationModel,
        emailDeliveryAnalysisService: EmailDeliveryAnalysisService,
        emailValidationBatchRequestModel: EmailValidationBatchRequestModel
)
        extends TransformAndSaveEmailValidationResultTrait {

  // todo:nxd  delete this after consulting with Prateek


  /*
  23 Feb 2023: we are not doing the analysis anymore
  */
  override def saveEmailValidationApiResultV2(
    data: EmailValidationAPIResultForSaving,
    logger: SRLogger
  ): Try[List[ProspectEmailValidationResultWithTeamIdAndAnalysisId]] = {


    data match {

      case d: EmailValidationBatchResultForSaving =>

        val validationResultDone = for {

          finalResult: List[ProspectEmailValidationResultWithTeamId] <- {

            emailValidationModel.updateEmailValidationResultsFromBatchRequest(
              validationResults = d.validationResults
                .map(ev => {

                  EmailValidationResultWithAnalysisId(

                    emailDeliveryAnalysisId = None,

                    email = ev.email,
                    fullJson = ev.fullJson,
                    isValid = ev.isValid,
                    srEmailValidityStatus = ev.srEmailValidityStatus,
                    internal_sr_validation_fail_reason = None
                  )

                }),
              validationService = d.validationService
            )
          }

        } yield {

          TransformAndSaveEmailValidationResult
            .getResultWithTeamAndAnalysisId(

              final_result = finalResult,

              transformed_rec = Seq(),

              logger = logger
            )


        }

        if (validationResultDone.isFailure) {

          logger.error(s"unable to process batch request ${d.requestId}")
          // TODO:nxd EmailValidationBatchRequest.updateBatchRequestOnError call should be here
          emailValidationBatchRequestModel.updateBatchRequestOnError(
            serviceRequestId = d.requestId,
            validationService = d.validationService,
            error = s"unable to process batch request ${d.requestId}")
          match {
            case Failure(e) => logger.fatal(s"unable to log batch error to DB:${LogHelpers.getStackTraceAsString(e)}")
            case Success(value) => ()
          }

        }

        validationResultDone

      case d: EmailValidationRealtimeResultForSaving =>

        val validationResultDone = for {

          final_result: List[ProspectEmailValidationResultWithTeamId] <- {

            val res = d.validationResult


            emailValidationModel.addEmailValidationResponse(

              validationResult = EmailValidationResultWithAnalysisId(
                emailDeliveryAnalysisId = None,
                email = res.email,
                fullJson = res.fullJson,
                isValid = res.isValid,
                srEmailValidityStatus = res.srEmailValidityStatus,
                internal_sr_validation_fail_reason = None
              ),

              validationService = d.validationService,
              teamId = d.initiatorTeamId,
              doerAccountId = d.initiatorAccountId,
              initiatorCampaignId = d.initiatorCampaignId
            ).map(_.toList)
          }
        } yield {

          TransformAndSaveEmailValidationResult
            .getResultWithTeamAndAnalysisId(
              final_result = final_result,
              transformed_rec = Seq(),

              logger = logger
            )

        }
        validationResultDone
    }

  }


/*
  override def saveEmailValidationApiResult(
          data: EmailValidationAPIResultForSaving,
          logger: SRLogger
  ): Try[List[ProspectEmailValidationResultWithTeamIdAndAnalysisId]] = {


    data match {

      case d: EmailValidationBatchResultForSaving =>

        // TODO:nxd : batch email: need to fetch with campaignId/teamId from
        //  email_validations table
        //  data.groupBy(_.campaignId)
        // TODO:nxd - im only pulling a batch of 500 - need to cover whatever
        //  was sent for validation

        val validationResultDone = for {

          // 1. fetch all the records from the DB
          emailValidationRecordsFromDB <- emailValidationModel
            .getEmailsByBatchRequest(d.requestId)


          // I need a set of CampaignIds for fast lookup
          campaignIds <- Try {
            emailValidationRecordsFromDB
              .filter(_.initiator_campaign_id.isDefined)
              .map(_.initiator_campaign_id.get)
              .toSet
          }


          // 2. map of email -> DB record - so we can get old of the campaign_id
          //             via email
          email_to_db_rec_map <- Try {
            emailValidationRecordsFromDB
              .map(
                v => (v.email, v)
              ).toMap
          }


          // next step would be get the - analyses for the campaigns from the DB
          analysisRecs <- emailDeliveryAnalysisService
            .getLatestAnalysisRecordForCampaigns(campaignIds)


          // make this able to lookup by campaign id
          analysisRecordMap <- Try {
            analysisRecs.map(edar =>
              (edar.campaign_id, edar)).toMap
          }


          transformed_validation_results <- Try {
            transformToUndeliverableIfNecessary(
              validationResults = d.validationResults,
              analysisRecordMap = analysisRecordMap,
              email_to_db_rec_map = email_to_db_rec_map,
              logger = logger)
          }

          finalResult <- emailValidationModel.updateEmailValidationResultsFromBatchRequest(
            validationResults = transformed_validation_results,
            validationService = d.validationService
          )

        } yield {

          TransformAndSaveEmailValidationResult
            .getResultWithTeamAndAnalysisId(

              final_result = finalResult,

              transformed_rec = transformed_validation_results,

              logger = logger
            )


        }

        if (validationResultDone.isFailure) {

          logger.error(s"unable to process batch request ${d.requestId}")
          // TODO:nxd EmailValidationBatchRequest.updateBatchRequestOnError call should be here
          emailValidationBatchRequestModel.updateBatchRequestOnError(
            serviceRequestId = d.requestId,
            validationService = d.validationService,
            error = s"unable to process batch request ${d.requestId}")
                  match {
            case Failure(e) => logger.fatal(s"unable to log batch error to DB:${LogHelpers.getStackTraceAsString(e)}")
            case Success(value) => ()
          }

        }

        validationResultDone

      case d: EmailValidationRealtimeResultForSaving =>


        val campaignIds = Set(d.initiatorCampaignId)

        // next step would be get the - analyses for the campaigns from the DB

        val validationResultDone = for {

           analysisRecs <- emailDeliveryAnalysisService
                   .getLatestAnalysisRecordForCampaigns(campaignIds)


           emailValidationRecordsFromDB <- emailValidationModel
            .getEmailValidationByEmail(d.validationResult.email) // todo:nxd 2 emails across campaigns to be handled

            email_to_db_rec_map <- Try {
              emailValidationRecordsFromDB
                      .map(v => (v.email, v)).toMap
            }

            analysisRecordMap <- Try {
              analysisRecs.map(edar => (edar.campaign_id, edar)).toMap
            }


            transformed_rec <- Try { transformToUndeliverableIfNecessary(
              validationResults = Seq (d.validationResult),
              analysisRecordMap = analysisRecordMap,
              email_to_db_rec_map = email_to_db_rec_map,
              logger = logger)
            }

            final_result <- emailValidationModel.addEmailValidationResponse(
              validationResult = transformed_rec.head,
              validationService = d.validationService,
              teamId = d.initiatorTeamId,
              doerAccountId = d.initiatorAccountId,
              initiatorCampaignId = d.initiatorCampaignId
            ).map(_.toList)
        } yield  {

          TransformAndSaveEmailValidationResult
            .getResultWithTeamAndAnalysisId(
              final_result = final_result,
              transformed_rec = transformed_rec,

              logger = logger
            )

        }
        validationResultDone
    }

  }
  */


    private def transformToUndeliverableIfNecessary(
          validationResults: Seq[EmailValidationResult],
          analysisRecordMap: Map[Long, EmailDeliveryAnalysisRecord],
          email_to_db_rec_map: Map[String, EmailValidationRecord],
          logger: SRLogger
  )
  : Seq[EmailValidationResultWithAnalysisId] = {
    validationResults.map(ev_result => {
      val validation_record = email_to_db_rec_map.get(ev_result.email)
      val rec: Option[EmailDeliveryAnalysisRecord] = validation_record.flatMap(vrec => {

        if (vrec.initiator_campaign_id.isEmpty) {
          None
        } else {
          analysisRecordMap.get(
            vrec.initiator_campaign_id.get
          )
        }
      })
    
      val validationId = validation_record.map(_.id)
      val initiatorCampaignId = validation_record.flatMap(_.initiator_campaign_id)
      val final_rec = rec match {
        case None =>
          logger.info(s"transformToUndeliverableIfNecessary analysis id for campaign : $initiatorCampaignId is not present :: validationId_$validationId")

          EmailValidationResultWithAnalysisId(
            emailDeliveryAnalysisId = None,
            email = ev_result.email,
            fullJson = ev_result.fullJson,
            isValid = ev_result.isValid,
            srEmailValidityStatus = ev_result.srEmailValidityStatus,
            internal_sr_validation_fail_reason = None
          )
        case Some(rec) =>

          val evWithAnalysisId = EmailValidationResultWithAnalysisId(
            emailDeliveryAnalysisId = Some(
              EmailDeliveryAnalysisId(
                id = rec.id
              )
            ),
            email = ev_result.email,
            isValid = ev_result.isValid,
            fullJson = ev_result.fullJson,
            srEmailValidityStatus = ev_result.srEmailValidityStatus,
            internal_sr_validation_fail_reason = None
          )

          if (ev_result.srEmailValidityStatus ==
                  SREmailValidityStatus.RISKY && rec.should_pause_risky) {
            logger.info(s"transformToUndeliverableIfNecessary campaign_id $initiatorCampaignId transforming RISKY to undeliverable because of analysis id : ${rec.id} :: validationId_$validationId")
            evWithAnalysisId.copy(
              srEmailValidityStatus =
                SREmailValidityStatus.SR_INFERRED_UNDELIVERABLE,
              isValid = false)
          }
//          else if (ev_result.srEmailValidityStatus ==
//            SREmailValidityStatus.UNKOWN && rec.should_pause_unknown) {
//            logger.info(s"transformToUndeliverableIfNecessary campaign_id $initiatorCampaignId transforming UNKOWN to undeliverable because of analysis id : ${rec.id} :: validationId_$validationId")
//            evWithAnalysisId.copy(srEmailValidityStatus =
//              SREmailValidityStatus.SR_INFERRED_UNDELIVERABLE,
//              isValid = false)
//          } else if (ev_result.srEmailValidityStatus ==
//            SREmailValidityStatus.DELIVERABLE &&
//            rec.should_pause_deliverable) {
//            logger.info(s"transformToUndeliverableIfNecessary campaign_id $initiatorCampaignId transforming DELIVERABLE to undeliverable because of analysis id : ${rec.id} :: validationId_$validationId")
//            evWithAnalysisId.copy(srEmailValidityStatus =
//              SREmailValidityStatus.SR_INFERRED_UNDELIVERABLE,
//              isValid = false)
//          }
          else {
            evWithAnalysisId
          }
      }
      final_rec


    })
  }
}

object TransformAndSaveEmailValidationResult {

 def getResultWithTeamAndAnalysisId(

   final_result: List[ProspectEmailValidationResultWithTeamId],

   transformed_rec: Seq[EmailValidationResultWithAnalysisId],

   logger: SRLogger

 ): List[ProspectEmailValidationResultWithTeamIdAndAnalysisId] = {

   final_result
     .map(res => {

       ProspectEmailValidationResultWithTeamIdAndAnalysisId(

         email = res.email,

         emailValidationId = res.emailValidationId,

         teamId = res.teamId,

         isValid = res.isValid,

         validationInitiator = res.validationInitiator,

         emailDeliveryAnalysisId = {

           transformed_rec
             .find(p => p.email.trim.toLowerCase == res.email.trim.toLowerCase) // CHECKME
             .flatMap(_.emailDeliveryAnalysisId)

         }
       )

     })
 }

}
