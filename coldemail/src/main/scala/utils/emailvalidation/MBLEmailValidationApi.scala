package utils.emailvalidation

import org.apache.pekko.actor.Scheduler
import api.AppConfig
import play.api.Logging
import play.api.libs.json.{Js<PERSON>rror, JsSuccess, <PERSON>sV<PERSON><PERSON>, <PERSON><PERSON>, Reads}
import play.api.libs.ws.WSClient
import utils.mq.services.MQDoNotNackException
import utils.{FutureUtils, SRLogger}

import scala.concurrent.duration._
import scala.concurrent.{ExecutionContext, Future}
import scala.util.Try


case class MBLApiResponse(
  email: String,
  did_you_mean: String,
  format_valid: <PERSON><PERSON>an,
  user: Option[String],
  domain: Option[String],
  mx_found: Option[Boolean],
  smtp_check: <PERSON>olean,
  catch_all: Option[<PERSON>olean],
  role: Option[Boolean],
  disposable: Option[Boolean],
  free: Option[Boolean],
  score: Float
)

object MBLApiResponse {
  implicit val reads: Reads[MBLApiResponse] = Json.reads[MBLApiResponse]
}

object MBLEmailValidationApi
  extends EmailValidationApiProvider
  with Logging {


  override def getInitialSREmailValidityStatusFromApiResponse(
    apiResponse: JsValue
  ): Try[SREmailValidityStatus] = Try {

    val format_valid = (apiResponse \ "format_valid").as[Boolean]
    val mx_found = (apiResponse \ "mx_found").asOpt[Boolean].getOrElse(false)
    val smtp_check = (apiResponse \ "smtp_check").as[Boolean]
    val catch_all = (apiResponse \ "catch_all").asOpt[Boolean].getOrElse(false)
    val disposable = (apiResponse \ "disposable").asOpt[Boolean].getOrElse(false)

    if (!format_valid) SREmailValidityStatus.UNDELIVERABLE
    else if (disposable) SREmailValidityStatus.UNDELIVERABLE
    else if (!mx_found) SREmailValidityStatus.UNDELIVERABLE
    else if (catch_all) SREmailValidityStatus.RISKY
    else if (smtp_check) SREmailValidityStatus.DELIVERABLE
    else if (!smtp_check) SREmailValidityStatus.RISKY
    else {

      logger.warn(s"MBLEmailValidationApi.getInitialSREmailValidityStatusFromApiResponse didnt match any prior status: apiResponse: $apiResponse")

      SREmailValidityStatus.UNKOWN
    }

  }

  override def getEmailValidationResult(
    apiResponse: JsValue
  ): Try[EmailValidationResult] = {

    getInitialSREmailValidityStatusFromApiResponse(
      apiResponse = apiResponse
    )
      .map(validityStatus => {

        val email = (apiResponse \ "email").as[String].trim.toLowerCase

          EmailValidationResult(
            email = email,
            fullJson = apiResponse,
            isValid = validityStatus != SREmailValidityStatus.UNDELIVERABLE,
            srEmailValidityStatus = validityStatus
          )
      })

  }


  def validateViaMBL(
    email: String
  )(
    implicit ws: WSClient,
    ec: ExecutionContext,
    scheduler: Scheduler,
    Logger: SRLogger

  ): Future[(MBLApiResponse, EmailValidationResult)] = {

    val url = s"https://apilayer.net/api/check?access_key=${AppConfig.mailboxlayerApiKey}&email=$email"
    // Logger.info(s"EmailValidator.validate before calling: $url")

    // MUST BE "def", NOT "val"
    def wsReq = {

      ws.url(url)
        .get()
        .map(response => {

          if (response.status != 200) {
            Logger.error(s" retryOnError [1] response called: ${response.status} res body: ${response.body}")

            throw new Exception(s"${response.body}")

          } else {

            val success = (response.json \ "success").asOpt[Boolean]

            if (success.isDefined && !success.get) {

              Logger.error(s" retryOnError [2] response called: ${response.status} res body: ${response.body}")

              throw new Exception(s"${response.body}")

            } else {

              Logger.info(s"MBLApiResponse try success")

              response
            }
          }
        })
    }

    FutureUtils.retry(
      f = wsReq,
      delay = 4.second,
      retries = 1
    )
      .flatMap(response => {

        Logger.info(s"MBLApiResponse response: $email ::: $response ::: ${response.body}")

        if (response.status != 200) {

          throw MQDoNotNackException(s"MBLApiResponse error: $email ::: $response ::: ${response.body}")

        } else {
          /*
          val success = (response.json \ "success").asOpt[Boolean]

          if (success.isDefined && !success.get) {

            throw MQDoNotNackException(s"MBLApiResponse error failed: $email ::: $response ::: ${response.body}")

          } else {
            */


          response.json.validate[MBLApiResponse] match {

            case JsError(e) =>
              Logger.fatal(s"MBLApiResponse: ${response.body} ::: $e")

              throw MQDoNotNackException(s"MBLApiResponse validation error: $email: $response : ${response.body}")


            //              val success = (response.json \ "success").get.as[Boolean]
            //
            //              if (!success) {
            //
            //                val errorType = (response.json \ "error" \ "type").get.as[String]
            //
            //                if (errorType == "rate_limit_reached") {
            //
            //                  throw new Exception("rate_limit_reached")
            //
            //                } else {
            //
            //                  throw new Exception(s"MBLApiResponse validation error: ${e.toString()}")
            //                }
            //
            //              } else {
            //
            //                throw new Exception(s"MBLApiResponse validation error: ${e.toString()}")
            //              }


            case JsSuccess(data, _) =>
              Logger.info(s"MBLApiResponse EmailValidator.validate success response: $email")

              /*
              val isInvalidEmail: Boolean = !data.format_valid ||
                !data.smtp_check ||
                !data.mx_found.getOrElse(false) ||
                data.disposable.getOrElse(false)

              val emailValidationResult = EmailValidationResult(
                email = data.email,
                fullJson = response.json,
                isValid = !isInvalidEmail
              )
              */

              Future.fromTry(
                getEmailValidationResult(
                  apiResponse = response.json
                )
              ).map(emailValidationResult => {

                (data, emailValidationResult)

              })

          }

          // }


        }

      })
      .recover { case e =>

        val logMsg = s"MBLApiResponse recover: email: $email :: error: $e"

        if (e.getMessage != null && e.getMessage.contains("999")) {
          // 999 error_code is mailboxlayer's internal server error type issue, no need to add fatal log for this

          Logger.error(logMsg)

        } else {

          Logger.fatal(logMsg)

        }


        throw MQDoNotNackException(s"MBLApiResponse validation error: $email")
      }
  }

}
