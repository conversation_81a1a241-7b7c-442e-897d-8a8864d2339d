package utils.emailvalidation

import api.AppConfig
import api.accounts.TeamId
import api.accounts.models.AccountId
import api.campaigns.services.CampaignId
import api.prospects.ProspectForValidation
import play.api.Logging
import play.api.libs.json.{<PERSON>s<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>}
import play.api.libs.ws.WSClient
import utils.SRLogger
import utils.emailvalidation.models.{EmailValidationPriority, EmailValidationToolV2}
import utils.helpers.LogHelpers

import scala.io.Source
import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

class DeBounceEmailValidationApi(
  emailValidationApiToolsRecordDAO: EmailValidationApiToolsRecordDAO
)
  extends EmailValidationApiProvider
    with EmailValidationApiScheduler
    with Logging {
  //code reference:- https://help.debounce.io/kb/understanding-results/result-codes/

    /*17-Jan-2025
    Debounce sample response :
   {"email":"<EMAIL>",
   "result":"Risky",
   "reason":"\"Deliverable",
   "code":" Role\""}
     */
  override def getInitialSREmailValidityStatusFromApiResponse(
                                                               apiResponse: JsValue
                                                             ): Try[SREmailValidityStatus] = Try {
    val code = (apiResponse \ "code").as[String].trim.stripSuffix("\"")

    if (
      code == "5" ||
      code == "Role"
    ) {

      SREmailValidityStatus.DELIVERABLE

    } else if (
        code == "1" ||
        code == "2" ||
        code == "3" ||
        code == "6"
    ) {

      SREmailValidityStatus.UNDELIVERABLE

    } else if (
        code == "8" ||
        code == "4"
    ) {

      SREmailValidityStatus.RISKY

    } else if (
        code == "7"
    ) {

      SREmailValidityStatus.UNKOWN

    } else {

      logger.error(s"unmatched debounce reason: $code , response : $apiResponse")

      SREmailValidityStatus.UNKOWN

    }
  }

  override def getEmailValidationResult(
                                         apiResponse: JsValue
                                       ): Try[EmailValidationResult] = {

    getInitialSREmailValidityStatusFromApiResponse(
      apiResponse = apiResponse
    )
      .map(validityStatus => {
        val email = (apiResponse \ "email").as[String].toLowerCase.trim

        EmailValidationResult(
          email = email,
          fullJson = apiResponse,
          isValid = validityStatus != SREmailValidityStatus.UNDELIVERABLE,
          srEmailValidityStatus = validityStatus
        )
      })

  }



  def extractResultsFromCsvAndConvertToJson(
     apiResponse: JsValue
    ):Try[JsArray] = Try{
    val csvUrl=(apiResponse \ "debounce" \ "download_link").as[String]

    val source = Source.fromURL(csvUrl)

    val responseJson=source.getLines().drop(1).foldLeft(Json.arr()) { case (array, line) => {
      val values = line.split(",");
      array ++ Json.arr(
        Json.obj(
          "email" -> values(0),
                  "result" -> values(1),
                  "reason" -> values(2),
                  "code" -> values(3)
        )
      )
    }
    }
    source.close()
    responseJson
  }
  // Reference:- https://developers.debounce.io/reference/upload
  def createBatchRequest(
      fileUrl:String
    )(
      implicit ws: WSClient,
      ec: ExecutionContext,
      srLogger: SRLogger
    ): Future[BatchRequestResponse] = {

    val log = srLogger.appendLogRequestId(
      appendLogReqId = "DeBounceEmailValidationService.createBatchRequest"
    )

    val url = s"https://bulk.debounce.io/v1/upload?api=${AppConfig.deBounceApiKey}&url=${fileUrl}"

    ws.url(url)
      .addHttpHeaders(
        "Content-Type" -> "application/json",
      ).get()
      .flatMap(response => {

        if (response.status != 200) {
          log.error(s"DebounceEVA.createBatchRequest  error response called: ${response.status} res body: ${response.body}")
//
            Future.failed(new Exception(s"${response.status}  ::  ${response.json}"))

        } else {
          val results = (response).json
          if ((results \ "debounce" \ "error").asOpt[String].isDefined) {
            Future.failed(new Exception(s"Error parsing results: json response: ${response.json}"))
          }
          else {

            val requestId = (results \ "debounce" \ "list_id").as[String]
            val status = (results \ "success").as[String]

            Future.successful(BatchRequestResponse(
              requestId = requestId,
              response = results,
              status = status
            ))
          }
        }
      })


  }

 // Reference:- https://developers.debounce.io/reference/status
  def checkIfBatchRequestCompleted(
                                    requestId: String
                                  )(
                                    implicit ws: WSClient,
                                    ec: ExecutionContext,
                                    srLogger: SRLogger

                                  ): Future[Boolean] = {

    val log = srLogger.appendLogRequestId(
      appendLogReqId = "DeBouncerEmailValidationService.checkBatchRequestStatus"
    )

    val url = s"https://bulk.debounce.io/v1/status/?list_id=${requestId}&api=${AppConfig.deBounceApiKey}"

    ws.url(url)
      .addHttpHeaders(
        "Content-Type" -> "application/json",
      )
      .get()
      .flatMap(response => {

        if (response.status != 200) {
          log.error(s"error response called: ${response.status} res body: ${response.body}")

          Future.failed(new Exception(s"${response.body}"))

        } else {
          val results = (response).json
          if ((results \ "debounce" \ "error").asOpt[String].isDefined) {
            Future.failed(new Exception(s"Error parsing results: json response: ${response.json}"))
          }
          else {
            val RequestId = (results \ "debounce" \ "list_id").as[String]
            val status = (results \ "debounce" \ "status").as[String]

            Future.successful(status == "completed")
          }
        }
      })

  }

  // Reference:- https://developers.debounce.io/reference/status
  def fetchBatchRequestResults(
                                RequestId: String
                              )(
                                implicit ws: WSClient,
                                ec: ExecutionContext,
                                srLogger: SRLogger

                              ): Future[Seq[EmailValidationResult]] = {

    val log = srLogger.appendLogRequestId(
      appendLogReqId = "DeBouncerEmailValidationService.fetchBatchRequestResults"
    )

    val url = s"https://bulk.debounce.io/v1/status/?list_id=${RequestId}&api=${AppConfig.deBounceApiKey}"

    ws.url(url)
      .addHttpHeaders(
        "Content-Type" -> "application/json"
      )
      .get()
      .flatMap(response => {

        if (response.status != 200) {
          log.error(s"error response called: ${response.status} res body: ${response.body}")

          Future.failed(new Exception(s"${response.body}"))

        } else {

          val results = (response).json
          /*
          * HTTP/1.1 200 OK
          {
             "debounce":{
                "error":"List ID is not valid."
             },
             "success":"0"
          }
          * we can get the above response if list id not valid
          * */
          if ((results \ "debounce" \ "error").asOpt[String].isDefined) {

            Future.failed(new Exception(s"Error parsing results: json response: ${response.json}"))

          } else {

            extractResultsFromCsvAndConvertToJson(results) match {
              case Failure(error) => Future.failed(new Exception(s"Unable to  Extract results from CSV ${error}"))
              case Success(array) => {
                val resultJsonArray=array.as[Seq[JsValue]]
                Future.sequence(resultJsonArray.map(emJs => Future.fromTry(getEmailValidationResult(apiResponse = emJs))))
              }


            }


          }

        }
      })

  }

}
