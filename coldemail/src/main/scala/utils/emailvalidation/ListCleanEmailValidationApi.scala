package utils.emailvalidation

import org.apache.pekko.http.scaladsl.model.DateTime
import api.AppConfig
import api.accounts.TeamId
import api.accounts.models.AccountId
import api.campaigns.services.CampaignId
import api.prospects.ProspectForValidation
import play.api.Logging
import play.api.libs.json.{<PERSON>s<PERSON><PERSON><PERSON>, <PERSON>s<PERSON><PERSON><PERSON>, <PERSON>son}
import play.api.libs.ws.WSClient
import utils.SRLogger
import utils.emailvalidation.models.{EmailValidationToolV2, ListCleanAPIEmailValidityStatus}

import scala.concurrent.{ExecutionContext, Future}
import scala.io.Source
import scala.util.{Failure, Success, Try}
import play.api.libs.ws.WSBodyWritables._

class ListCleanEmailValidationApi (
  emailValidationApiToolsRecordDAO: EmailValidationApiToolsRecordDAO
)
  extends EmailValidationApiProvider
  with EmailValidationApiScheduler
  with Logging {


    /*
    17-Apr-2024:
    1.once the request is made - where the job-number is stored ?  Database in a special table?

       Once the batch request is made tot he API it returns a List_id which is useful to check future operations of that Batch
       and the list_id is stored in the email_validation_batch_request_table and from their it can be retrieved to check for the
       further operations like checking the completion status of the batch and if completed then this particular id is used to fetch
       the results from the API.

       2.Do we retrieve the stored list_id and can re-query the api?
       Yes we can retrieve the list_id from email_validation_batch_request_table and can requery the api.

       3.If we are polling the API for whether the job is completed - what is the frequency of polling the API to find if the job is completed?
         THe OneMinutelyCron checks the completion status of the batch every minute.

       4.How are we handling parallelism - for example for debounce - we allow only 1 active API call. What are we doing in this case?
         So in the new flow for every validation API we are ensuring that the requests will be made sequentially like if the current queued request
         is done then only the next batch will be created and sent for validation.

     */

    //Refernce :- https://listclean.xyz/knowledge-base/category/email-validations/


    /*
    10-Apr-2024
      How is SREmailValidityStatus is decided for ListClean API
      ListClean API provides the batch result as status and reasons
      Status refers to the validity of the email whether it is deleverable or not
            THere are three Status for ListClean API -
                     Clean -for valid and delieverable emails
                        Clean status has three reasons -
                                    High Quality - Highly confident
                                    Medium Quality - Medium confident
                                    Low Quality - Low Confident

                        Dirty - emails with very bad delieverablity rate and non existing mails,blacklisted emails ,etc
                        Dirty can has multiple reasons

                        Unknown - Status which indicates that the emails are not validated due to some issue

            1. If the Status is Clean and has reason as High Quality then the SREmailValidityStatus will be DELIVERABLE
            2. If the Status is Clean and has reason other than High Quality then the SREmailValidityStatus will be RISKY
            3.If the Status is Dirty then the SREmailValidityStatus will be UNDELIVERABLE as these mails have bad deliverability rate
            4. If the Status is Unknown then the SREmailValidityStatus will be UNKNOWN
            5. If unmatched status then the SREmailValidityStatus will be UNKNOWN


     */
    override def getInitialSREmailValidityStatusFromApiResponse(
                                                                 apiResponse: JsValue
                                                               ): Try[SREmailValidityStatus] = Try {
        val status = (apiResponse \ "result").as[String]
        val reason = (apiResponse \ "reason").as[String]

        ListCleanAPIEmailValidityStatus.fromStatus(status) match {
            case Success(ListCleanAPIEmailValidityStatus.CLEAN)=>
                reason match {
                    case "High Quality"=>
                        SREmailValidityStatus.DELIVERABLE

                    case "Medium Quality"=>
                       SREmailValidityStatus.RISKY

                    case "Low Quality"=>
                        SREmailValidityStatus.RISKY

                }

            case Success(ListCleanAPIEmailValidityStatus.DIRTY) =>
                SREmailValidityStatus.UNDELIVERABLE

            case Success(ListCleanAPIEmailValidityStatus.UNKNOWN) =>
                SREmailValidityStatus.UNKOWN

            case Failure(exception)=>
               throw new Exception(s"Failed while matching status - ${exception.getMessage}")

        }

    }

    override def getEmailValidationResult(
                                           apiResponse: JsValue
                                         ): Try[EmailValidationResult] = {
        getInitialSREmailValidityStatusFromApiResponse(
            apiResponse = apiResponse
        )
          .map(validityStatus => {
              val email = (apiResponse \ "email").as[String].toLowerCase.trim

              EmailValidationResult(
                  email = email,
                  fullJson = apiResponse,
                  isValid = validityStatus != SREmailValidityStatus.UNDELIVERABLE,
                  srEmailValidityStatus = validityStatus
              )
          })
    }

       //Reference - https://api.listclean.xyz/#tag/lists/paths/~1downloads~1{list_id}~1{type}~1/get

    /*
    NOTE:
    The response from ListClean when called for the results of the batch the API
    is in the CSV file with LC_Email,LC_Status,LC_Reason,LC_MX,LC_MSP,"EMAIL" columns
    from which first we drop the header row and we extract the data from column
    LC_Email,LC_Status and LC_Reason
     */



    def extractResultsFromCsvAndConvertToJson(
                                               url:String
                                             )(
                                               implicit ws: WSClient,
                                               ec: ExecutionContext,
                                               srLogger: SRLogger
                                             ): Future[JsArray] = {
        val log = srLogger.appendLogRequestId(
            appendLogReqId = "ListCleanEmailValidationService.extractResultsFromCsvAndConvertToJson"
        )

            ws.url(url).get().flatMap { response =>
                if (response.status != 200) {
                    log.error(s"Error response called: ${response.status} res body: ${response.body}")
                    Future.failed(new Exception(response.body))
                } else {
                    val source = Source.fromURL(url)
                    val resultFuture=try {
                        val responseJson = source
                                            .getLines()
                                            .drop(1)  // dropping the header row from the CSV response
                                            .foldLeft(Json.arr()) { case (array, line) =>
                                                val values = line.split(",")
                                                   array :+ Json.obj(
                                                      "email" -> values(0),
                                                       "result" -> values(1),
                                                       "reason" -> values(2)
                            )
                        }
                        source.close()
                        Future.successful(responseJson)
                    } catch {
                        case e: Exception =>
                            source.close()
                            Future.failed(e)
                    }
                    resultFuture
                }
            }



        }



    //Reference:- https://api.listclean.xyz/#tag/verifications/paths/~1verify~1email~1batch/post

    def createBatchRequest(
                            emails: Seq[String]
                          )(
                            implicit ws: WSClient,
                            ec: ExecutionContext,
                            srLogger: SRLogger

                          ): Future[BatchRequestResponse] = {

        val log = srLogger.appendLogRequestId(
            appendLogReqId = "ListCleanEmailValidationService.createBatchRequest"
        )
        val url = s"https://api.listclean.xyz/v1/verify/email/batch"

        val postJsonData = Json.obj("emails" -> emails)

        ws.url(url)
          .addHttpHeaders("Content-type" -> "application/json",
              "X-Auth-Token" -> AppConfig.listCleanApiKey
          )
          .post(
              postJsonData
          )
          .flatMap(response => {
              if (response.status != 200) {
                  log.error(s" ListcleanEVA.createBatchRequest error response called: ${response.status} res body: ${response.body}")

                  Future.failed(new Exception(s"${response.status}  ::  ${response.json}"))
              } else {
                  val results = (response).json

                  val success = (results \ "success").as[Int]
                  success match {
                      case 0 =>
                          Future.failed(new Exception(s"${response.status}  ::  ${response.json}"))
                      case 1 =>
                          val requestId = (results \ "data"  \  "list_id").as[Int]

                          val status = (results \ "success").as[Int]


                          Future.successful(BatchRequestResponse(
                              response = results,
                              status = status.toString,
                              requestId = requestId.toString
                          ))

                      case _=>
                          Future.failed(new Exception(s"failed while creating Batch Request Unknown success value :${response.json}"))


                  }
              }
          })
    }

    //Refernce:- https://api.listclean.xyz/#tag/lists/paths/~1lists~1{list_id}/get

    def checkIfBatchRequestCompleted(
                                      requestId: String
                                    )(
                                      implicit ws: WSClient,
                                      ec: ExecutionContext,
                                      srLogger: SRLogger
                                    ): Future[Boolean] = {

        val log = srLogger.appendLogRequestId(
            appendLogReqId = "ListCleanEmailValidationService.checkBatchRequestStatus"
        )

        val url = s"https://api.listclean.xyz/v1/lists/$requestId"

        ws.url(url)
          .addHttpHeaders("X-Auth-Token" -> AppConfig.listCleanApiKey)
          .get()
          .flatMap(response => {

              if (response.status != 200) {
                  log.error(s"error response called: ${response.status} res body: ${response.body}")

                  Future.failed(new Exception(s"${response.body}"))
              }
              else {
                  val results = (response).json
                  val success = (results \ "success").as[Int]
                  success match {
                      case 0 =>
                          Future.failed(new Exception(s"failed while creating Batch Request :${response.json}"))
                      case 1 =>
                          val requestId = (results \ "data"  \ "list_id").as[Int]
                          val status = (results \ "data" \ "status").as[String]

                          Future.successful(status == "COMPLETED")

                  }

              }

          })


    }

    //Reference:- https://api.listclean.xyz/#tag/lists/paths/~1downloads~1{list_id}~1{type}~1/get

    def fetchBatchRequestResults(
                                  RequestId: String
                                )(
                                  implicit ws: WSClient,
                                  ec: ExecutionContext,
                                  srLogger: SRLogger
                                ): Future[Seq[EmailValidationResult]] = {

        val log = srLogger.appendLogRequestId(
            appendLogReqId = "ListCleanEmailValidationService.fetchBatchRequestResults"
        )

        val cleanEmailsUrl = s"https://api.listclean.xyz/v1/downloads/$RequestId/clean?X-Auth-Token=${AppConfig.listCleanApiKey}"
        val dirtyEmailsUrl = s"https://api.listclean.xyz/v1/downloads/$RequestId/dirty?X-Auth-Token=${AppConfig.listCleanApiKey}"
        val unknownEmailsUrl = s"https://api.listclean.xyz/v1/downloads/$RequestId/unknown?X-Auth-Token=${AppConfig.listCleanApiKey}"

        val resultFuture: Future[Seq[JsArray]] = for {
            cleanResults <- extractResultsFromCsvAndConvertToJson(cleanEmailsUrl)
            dirtyResults <- extractResultsFromCsvAndConvertToJson(dirtyEmailsUrl)
            unKnownResults <- extractResultsFromCsvAndConvertToJson(unknownEmailsUrl)
        } yield {
            Seq(cleanResults, dirtyResults, unKnownResults)
        }

        resultFuture.flatMap { responses =>
            val combinedResultJsonResponses = responses.foldLeft(Json.arr())(_ ++ _)
            val jsonValues = combinedResultJsonResponses.as[Seq[JsValue]]
            val validationResultsFuture = Future.sequence(jsonValues.map { emJs =>
                Future.fromTry(getEmailValidationResult(apiResponse = emJs))
            })
            validationResultsFuture.recoverWith { case error =>
                log.error(s"Unable to retrieve the result from the csv file: $error")
                Future.failed(new Exception(s"Failed while parsing data from csv: $error"))
            }
        }
        }



}
