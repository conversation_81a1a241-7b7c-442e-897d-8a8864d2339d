package utils.emailvalidation

import api.accounts.TeamId
import api.campaigns.services.CampaignId
import api.lead_finder.models.LeadValidationBatchReqId
import play.api.libs.json.{Format, JsValue, Json}
import scalikejdbc._
import utils.dbutils.SQLUtils
import utils.enum_sr_utils.EnumUtils
import api.prospects.ProspectBounceResponseInfo
import api.prospects.models.{ProspectEmailId, ProspectId}
import utils.emailvalidation
import utils.emailvalidation.models.{EmailDeliveryAnalysisId, EmailValidationInitiator, EmailValidationPriority, EmailValidationProcessStatusV2, EmailValidationToolV2, InternalInvalidIEmailAddressReasons}

import scala.concurrent.blocking
import scala.util.{Failure, Success, Try}



sealed trait ValidationResultWithTeamIdAndAnalysisId{
  val emailDeliveryAnalysisId: Option[EmailDeliveryAnalysisId]
  val teamId: TeamId
  val email: String
  val isValid: Boolean
  val validationInitiator: EmailValidationInitiator.EmailValidationInitiatorType
}

object ValidationResultWithTeamIdAndAnalysisId {
  case class ProspectEmailValidationResultWithTeamIdAndAnalysisId(
                                                                   emailDeliveryAnalysisId: Option[EmailDeliveryAnalysisId],
                                                                   teamId: TeamId,
                                                                   email: String,
                                                                   isValid: Boolean,
                                                                   emailValidationId: Long,
                                                                   validationInitiator: EmailValidationInitiator.EmailValidationInitiatorType,
                                                                 ) extends ValidationResultWithTeamIdAndAnalysisId

  case class ValidationResultWithTeamIdAndAnalysisIdWithoutEmailValidationId(
                                                                              emailDeliveryAnalysisId: Option[EmailDeliveryAnalysisId],
                                                                              teamId: TeamId,
                                                                              email: String,
                                                                              isValid: Boolean,
                                                                              validationInitiator: EmailValidationInitiator.EmailValidationInitiatorType,
                                                                            ) extends ValidationResultWithTeamIdAndAnalysisId

  def fromProspectEmailValidationResultWithTeamIdAndAnalysisId(
                                                                p: ProspectEmailValidationResultWithTeamIdAndAnalysisId
                                                              ): ValidationResultWithTeamIdAndAnalysisIdWithoutEmailValidationId = {

    ValidationResultWithTeamIdAndAnalysisIdWithoutEmailValidationId(
      emailDeliveryAnalysisId = p.emailDeliveryAnalysisId,
      teamId = p.teamId,
      email = p.email,
      isValid = p.isValid,
      validationInitiator = p.validationInitiator
    )

  }
}

case class EmailValidationResult(
  email: String,
  fullJson: JsValue,
  isValid: Boolean,
  srEmailValidityStatus: SREmailValidityStatus
)

case class EmailValidationResultWithAnalysisId(
  emailDeliveryAnalysisId: Option[EmailDeliveryAnalysisId],
  email: String,
  fullJson: JsValue,
  isValid: Boolean,
  srEmailValidityStatus: SREmailValidityStatus,
  internal_sr_validation_fail_reason: Option[InternalInvalidIEmailAddressReasons]
)

case class ProspectEmailValidationResultWithTeamId(
  teamId: TeamId,
  email: String,
  isValid: Boolean,
  emailValidationId: Long,
  validationInitiator: EmailValidationInitiator.EmailValidationInitiatorType,
)


case class ProspectEmailValidationResult(
  email: String,
  isValid: Boolean,
  emailValidationId: Long
)

case class EmailValidationRecord(
        id: Long,
        email: String,
        initiator_account_id: Long,
        initiator_team_id: Long,

        // sometimes the user may delete the campaign just after the validation process has started, so it needs to an Option
        initiator_campaign_id: Option[Long],
        email_validation_batch_request_id: Long,
        process_status: String
)


class EmailValidationModel extends EmailValidationModelTrait {
  implicit val session: AutoSession.type = AutoSession


  def addEmailValidationResponse(
                                  validationResult: EmailValidationResultWithAnalysisId,
                                  validationService: EmailValidationToolV2,
                                  doerAccountId: Long,
                                  teamId: Long,
                                  initiatorCampaignId: Long
  ): Try[Option[ProspectEmailValidationResultWithTeamId]] = blocking {

    val processStatus = EmailValidationProcessStatusV2.COMPLETED.toString

    DB autoCommit { implicit session =>

      Try {

        sql"""
        INSERT INTO email_validations
        (
          email,
          process_status,
          checked_via_tool,
          full_result,
          is_valid,
          sr_email_validity_status,
          initiator_account_id,
          initiator_team_id,
          initiator_campaign_id
        )
        VALUES (
          ${validationResult.email.trim.toLowerCase},
          $processStatus,
          ${validationService.toString},
          to_jsonb(${Json.toJson(validationResult.fullJson).toString()}::jsonb),
          ${validationResult.isValid},
          ${validationResult.srEmailValidityStatus.toString},
          $doerAccountId,
          $teamId,
          $initiatorCampaignId
        )
        ON CONFLICT DO NOTHING
        RETURNING id, email, is_valid, initiator_team_id;
      """
          .map(rs => ProspectEmailValidationResultWithTeamId(
            teamId = TeamId(id = rs.long("initiator_team_id")),
            email = rs.string("email"),
            isValid = rs.boolean("is_valid"),
            emailValidationId = rs.long("id"),
            validationInitiator = EmailValidationInitiator.InitiatedByCampaign, // TODO: check this
          ))
          .single
          .apply()
      }
    }

  }


  def addForBatchRequest(
                          emails: Seq[String],
                          doerAccountId: Long,
                          teamId: Long,
                          priority:EmailValidationPriority,
                          initiatorWithValue: EmailValidationInitiator.EmailValidationInitiatorWithValue,
  ): Try[List[(Long,String)]] = blocking {

    val processStatus = EmailValidationProcessStatusV2.TO_BE_QUEUED.toString

    val (
      initiatorCampaignIdOpt: Option[CampaignId],
      leadValidationBatchReqIdOpt: Option[LeadValidationBatchReqId]
      ) = initiatorWithValue match {

      case ibc: EmailValidationInitiator.InitiatedByCampaign =>

        (
          Some(ibc.initiatorCampaignId),
          None,
        )

      case ibf: EmailValidationInitiator.InitiatedByLeadFinder =>

        (
          None,
          Some(ibf.leadValidationBatchReqId),
        )

    }

    if(emails.isEmpty) {
      Success(List())
    } else {

    DB autoCommit { implicit session =>

      Try {

        var valueParameters = List[Any]()

        val valuePlaceholder: SQLSyntax = emails
          .map(em => {

            valueParameters = valueParameters ::: List(

              em,
              processStatus,
              Json.obj().toString(), // empty json object

              doerAccountId,
              teamId,

              initiatorWithValue.toString,
              initiatorCampaignIdOpt.map(_.id),
              leadValidationBatchReqIdOpt.map(_.id),

              priority.toString
            )

            sqls"""
              (
                ?,
                ?,
                to_jsonb(?::jsonb),


                ?,
                ?,

                ?,
                ?,
                ?,

                ?
              )

            """

          })
          .reduce((vp1, vp2) => sqls"$vp1, $vp2")

        sql"""
          INSERT INTO email_validations
          (
            email,
            process_status,
            full_result,

            initiator_account_id,
            initiator_team_id,

            validation_initiated_by,
            initiator_campaign_id,
            lead_validation_batch_request_id,

            priority
          )
          VALUES
            $valuePlaceholder

          ON CONFLICT DO NOTHING

          RETURNING id,email
        """
          .bind(valueParameters*)
          .map(rs => (
              rs.long("id"),
            rs.string("email")
            ))
          .list
          .apply()


      }
    }
    }

  }


  def updateEmailValidationResultsFromBatchRequest(
    validationResults: Seq[EmailValidationResultWithAnalysisId],
    validationService: EmailValidationToolV2
  ): Try[List[ProspectEmailValidationResultWithTeamId]] = blocking {

    val completedProcessStatus = EmailValidationProcessStatusV2.COMPLETED.toString
    if(validationResults.isEmpty) {
      Success(List())
    } else {
      Try {
        validationResults
          .grouped(500)
          .flatMap(validationResultsGroup => {
            DB autoCommit { implicit session =>
              var valueParameters = List[Any]()

              val valuePlaceholder: SQLSyntax = validationResultsGroup
                .map(e => {

                  valueParameters = valueParameters ::: List(

                    e.email,
                    completedProcessStatus,
                    Json.toJson(e.fullJson).toString(),
                    e.isValid,
                    e.srEmailValidityStatus.toString,

                    validationService.toString,
                    EmailValidationProcessStatusV2.QUEUED.toString,
                    e.internal_sr_validation_fail_reason.map(_.toString).orNull
                  )

                  sqls"""
                      (
                        ?,
                        ?,
                        ?,
                        ?,
                        ?,

                        ?,
                        ?,
                        ?
                      )

                    """

                })
                .reduce((vp1, vp2) => sqls"$vp1, $vp2")

              sql"""
                  UPDATE email_validations
                  SET

                      process_status = temp.status,
                      full_result = to_jsonb(temp.rawRes::jsonb),
                      is_valid = temp.isValid,
                      sr_email_validity_status = temp.sr_email_validity_status,
                      checked_at = now(),
                      internal_sr_validation_fail_reason = temp.internal_sr_validation_fail_reason

                  FROM (VALUES $valuePlaceholder)
                  AS temp(
                       emailaddress,
                       status,
                       rawRes,
                       isValid,
                       sr_email_validity_status,

                       validationService,
                       isQueued,
                       internal_sr_validation_fail_reason
                  )
                  WHERE
                    checked_via_tool = temp.validationService
                    AND email = temp.emailaddress
                    AND process_status = temp.isQueued

                  RETURNING id, email, is_valid, initiator_team_id, validation_initiated_by
                """
                .bind(valueParameters*)
                .map(rs => {

                  // If validation_initiated_by is null then we will consider it as InitiatedByCampaign
                  val validationInitiatedBy: String =
                    rs.stringOpt("validation_initiated_by")
                      .getOrElse(EmailValidationInitiator.InitiatedByCampaign.toString)

                  ProspectEmailValidationResultWithTeamId(
                    teamId = TeamId(id = rs.long("initiator_team_id")),
                    emailValidationId = rs.long("id"),
                    email = rs.string("email"),
                    isValid = rs.boolean("is_valid"),
                    validationInitiator = EmailValidationInitiator.fromKey(key = validationInitiatedBy).get,
                  )

                })
                .list
                .apply()
            }
          }).toList
      }
    }

  }

  def updatePreviouslyValidatedEmailsWithBounceResponse(
    hardBouncedReplies: Seq[ProspectBounceResponseInfo],
    prospectIdToProspectEmailId: Map[ProspectId, ProspectEmailId]
  ): Try[Int] = Try {

    if(hardBouncedReplies.isEmpty){
      0
    } else {

    DB autoCommit { implicit session =>

        EmailValidationModel.getUpdatePreviouslyValidatedEmailsWithBounceResponseQuery(
          hardBouncedReplies = hardBouncedReplies,
          prospectIdToProspectEmailId = prospectIdToProspectEmailId
        )
          .update
          .apply()

    }
    }

  }

  def findEmailsForMakingBatchRequest(
    considerDebounce: Boolean
  ): Try[List[EmailValidationData]] = Try {


    val checkViaDebounceSql = if (!considerDebounce) {

      sqls" AND NOT checked_via_tool = ${EmailValidationToolV2.DEBOUNCE.toString} "

    } else {

      // consider both

        sqls""
      /*
      sqls"""

        AND (
           checked_via_tool IN (

            ${EmailValidationTool.BOUNCER.toString},

            ${EmailValidationTool.DEBOUNCE.toString}

          )

        """
    */
    }


    DB readOnly { implicit session =>
      sql"""
          SELECT id, email, initiator_team_id, checked_via_tool, validation_initiated_by
          FROM email_validations
          WHERE process_status = ${EmailValidationProcessStatusV2.TO_BE_QUEUED.toString}

          $checkViaDebounceSql

          AND checked_via_tool IS NOT NULL

          -- emails that were queued first, should be validated first
          ORDER BY id ASC
          LIMIT 10000
        """
        .map { rs =>

          // If validation_initiated_by is null then we will consider it as InitiatedByCampaign
          val validationInitiatedBy: String =
            rs.stringOpt("validation_initiated_by")
              .getOrElse(EmailValidationInitiator.InitiatedByCampaign.toString)


          EmailValidationData(
            email_validations_id = rs.long("id"),
            email = rs.string("email"),
            initiator_team_id = TeamId(rs.long("initiator_team_id")),
            validationInitiator = EmailValidationInitiator.fromKey(key = validationInitiatedBy).get,
            checked_via_tool = EmailValidationToolV2.fromKey(rs.string("checked_via_tool")) match {
              case Success(tool) => tool
              case Failure(ex) => throw ex
            }
          )
        }
        .list
        .apply()

    }
  }


  def updateQueuedStatusOnBatchRequest(
    emailValidationIds: Seq[Long],
    emailValidationBatchRequestId: Long
  ): Try[List[Long]] = blocking {

    if (emailValidationIds.isEmpty) Success(List()) else {

      val processStatus = EmailValidationProcessStatusV2.QUEUED.toString

      DB autoCommit { implicit session =>

        Try {

          sql"""
            UPDATE email_validations SET

              process_status = $processStatus,
              email_validation_batch_request_id = $emailValidationBatchRequestId

            WHERE
              id IN ($emailValidationIds)
              AND process_status = ${EmailValidationProcessStatusV2.TO_BE_QUEUED.toString}
            RETURNING id;
          """
            .map(rs => rs.long("id"))
            .list
            .apply()

        }
      }
    }
  }



  def fromEmailValidations(rs: WrappedResultSet): EmailValidationRecord = {
    EmailValidationRecord(
      id = rs.long("id"),
      email = rs.string("email"),
      initiator_account_id = rs.long("initiator_account_id"),
      initiator_team_id = rs.long("initiator_team_id"),
      initiator_campaign_id = rs.longOpt("initiator_campaign_id"),
      email_validation_batch_request_id = rs.long("email_validation_batch_request_id"),
      process_status = rs.string("process_status")
    )

  }

  override def getEmailsByBatchRequest(batchRequestId: String): Try[Seq[EmailValidationRecord]]
  =  Try {
    //  todo:we can make this -- limit 500, enabled later.
    //  note: right now - we are pulling with limit 1000 for the api call, so this
    //        is the max limit that can come here
    DB.readOnly { implicit session =>
      sql"""

        select ev.id,
          ev.email, ev.initiator_account_id, ev.initiator_team_id, ev.initiator_campaign_id,
          ev.email_validation_batch_request_id, ev.process_status
          from email_validations ev
          join email_validation_batch_request evbr on evbr.id = ev.email_validation_batch_request_id

          where evbr.service_request_id = ${batchRequestId}
          and ev.process_status = ${EmailValidationProcessStatusV2.QUEUED.toString}
          order by id
         """.stripMargin
        .map(fromEmailValidations).list.apply()
    }

  }

  override def getEmailValidationByEmail(email: String): Try[Seq[EmailValidationRecord]]
  =  Try {
    DB.readOnly { implicit session =>
      sql"""
          select id, email, initiator_account_id, initiator_team_id, initiator_campaign_id,
             email_validation_batch_request_id, process_status
          from email_validations where email = ${email}
          and process_status = ${EmailValidationProcessStatusV2.QUEUED.toString}
         """.stripMargin
        .map(fromEmailValidations).list.apply()
    }
  }

  def getResultsForPreviouslyValidatedEmails(emails: Seq[String]): Try[Seq[ProspectEmailValidationResult]]
  = Try {
    if (emails.isEmpty) {
      Seq()
    }
    else {
      DB.readOnly { implicit session =>
        sql"""
        select id, email, is_valid
          from email_validations where email in ${
          SQLUtils.generateSQLValuesClause(
            emails.map(_.trim.toLowerCase)
          )
        }
            and checked_at > (current_date - interval '45 days')

            and process_status = ${EmailValidationProcessStatusV2.COMPLETED.toString}
      """.stripMargin
          .map(rs => {
            ProspectEmailValidationResult(
              emailValidationId = rs.long("id"),
              email = rs.string("email"),
              isValid = rs.boolean("is_valid")
            )
          })
          .list
          .apply()
      }
    }
  }

  def getEmailsToChangeStatusToToBeQueued(
                                           validationToolWhichIsDown:EmailValidationToolV2)
                                            :Try[List[String]]  = Try{
      DB.readOnly { implicit session=>
          sql"""
                  SELECT email
                  FROM email_validations
                  WHERE process_status = ${EmailValidationProcessStatusV2.QUEUED.toString}
                  AND checked_via_tool=${validationToolWhichIsDown.toString}
                """.map(rs=> rs.string("email")
          ).list.apply()
      }
  }

  def updateProcessStatusTo_ToBeQueued(emails:List[String]):Try[Int]=Try{

    val processStatus = EmailValidationProcessStatusV2.TO_BE_QUEUED.toString

    DB autoCommit { implicit session =>



      sql"""
            UPDATE email_validations SET

              process_status = ${processStatus}

            WHERE
               process_status = ${EmailValidationProcessStatusV2.QUEUED.toString}
               AND email IN ($emails)


          """
        .update
        .apply()



    }

  }

    def getEmailsToSwitchToBackupVerificationTool(
         validationToolWhichIsDown:EmailValidationToolV2):Try[List[String]]=Try {

        DB.readOnly { implicit session=>
            sql"""
                  SELECT email
                  FROM email_validations
                  WHERE process_status = ${EmailValidationProcessStatusV2.TO_BE_QUEUED.toString}
                  AND checked_via_tool=${validationToolWhichIsDown.toString}
                """.map(rs=> rs.string("email")
            ).list.apply()
        }

    }


  def switchStuckEmailsToBackupVerificationTool(
       emails:List[String],
       validationToolWhichIsAvailable: EmailValidationToolV2):Try[Int]={

Try {

    DB autoCommit { implicit session =>

            if(emails.isEmpty){
                0
            } else
                  {


                      sql"""
                            UPDATE email_validations SET

                            checked_via_tool = ${validationToolWhichIsAvailable.toString}

                            WHERE process_status = ${EmailValidationProcessStatusV2.TO_BE_QUEUED.toString}
                            AND email IN ($emails)

                        """
                        .update
                        .apply()



                  }





            }
    }
  }



  def getLowPriorityEmailsForToolAssignment(): Try[List[String]] = Try {
    DB readOnly { implicit session =>
      sql"""
        SELECT email
        FROM email_validations
        WHERE process_status=${EmailValidationProcessStatusV2.TO_BE_QUEUED.toString}
        AND priority=${EmailValidationPriority.Low.toString}
        AND checked_via_tool IS NULL
        ORDER BY created_at ASC
        LIMIT 6000
      """.map(rs=> rs.string("email")
      ).list.apply()
    }
  }


  def getHighAndMediumPriorityEmailsForToolAssignment(): Try[List[String]] = Try {
    DB readOnly { implicit session =>
      sql"""
        SELECT email
        FROM email_validations
        WHERE process_status=${EmailValidationProcessStatusV2.TO_BE_QUEUED.toString}
        AND priority!=${EmailValidationPriority.Low.toString}
        AND priority IS NOT NULL
        AND checked_via_tool IS NULL
        ORDER BY created_at ASC
        LIMIT 4000
      """.map(rs=> rs.string("email")
      ).list.apply()
    }
  }



  def assignToolForValidation(emails:List[String],validationToolV2: EmailValidationToolV2):Try[Int]={
    DB autoCommit { implicit session =>

      Try {

        sql"""
            UPDATE email_validations SET

              checked_via_tool = ${validationToolV2.toString}

            WHERE
               process_status = ${EmailValidationProcessStatusV2.TO_BE_QUEUED.toString}
               AND email in($emails)

          """
          .update
          .apply()


      }
    }
  }
}

object EmailValidationModel {

  def getUpdatePreviouslyValidatedEmailsWithBounceResponseQuery(
   hardBouncedReplies: Seq[ProspectBounceResponseInfo],
   prospectIdToProspectEmailId: Map[ProspectId, ProspectEmailId]
  ): SQL[Nothing, NoExtractor] = {

    var valueParameters = List[Any]()

    val valuePlaceholder: SQLSyntax = hardBouncedReplies
      .map(hbr => {

        val hardBounceReason =
          if (hbr.bounceReason.isDefined) hbr.bounceReason.get
          else null

        val hardBounceType =
          if (hbr.bounceType.isDefined) hbr.bounceType.get.toString
          else null

        val foundProspectEmail: Option[ProspectEmailId] = prospectIdToProspectEmailId
          .find { case (prospectId, prospectEmailId) => prospectId.id == hbr.prospectId } // FIXME VALUECLASS
          .map(_._2)

        val prospectEmailId =
          if (foundProspectEmail.isDefined)
            foundProspectEmail.get.id
          else
            null


        valueParameters = valueParameters ::: List(

          hbr.prospectId,
          hbr.prospectEmail,
          hardBounceReason,
          hbr.bouncedAt,
          hardBounceType,
          prospectEmailId

        )

        sqls"""
          (
            ?,
            ?,
            ?,
            ?,
            ?,
            ?
           )
        """

      })
      .reduce((vp1, vp2) => sqls"$vp1, $vp2")

    sql"""
      UPDATE email_validations
      SET
        hard_bounced = TRUE,
        hard_bounced_at = temp.bouncedAt::timestamptz,
        hard_bounced_reason = temp.bounceReason,
        hard_bounced_prospect_id = temp.prospectId,
        hard_bounced_type = temp.bounceType,
        hard_bounced_prospect_email_id = temp.prospectEmailId,

        is_valid = FALSE

      FROM (VALUES $valuePlaceholder)
      AS temp(
           prospectId,
           prospectEmail,
           bounceReason,
           bouncedAt,
           bounceType,
           prospectEmailId
      )

      WHERE
           email = temp.prospectEmail
           AND checked_at > (CURRENT_DATE - interval '60 days')
    """
      .bind(valueParameters*)
  }


}
