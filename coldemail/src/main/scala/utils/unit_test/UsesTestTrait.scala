package utils.unit_test

import scala.concurrent.{ExecutionContext, Future}

class UsesTestTrait {

  def usesTestTrait(traitTest: TraitTest, a_str: String): Int = {

    traitTest.impl_method(a_str, traitTest.nameInTrait)
  }

  def usesTestFutureTrait(traitTest: TraitTest, anInt: Int) (implicit ec: ExecutionContext)
  : Future[Int] =
  {
    traitTest.usesTestFutureTrait(traitTest, anInt = anInt)
            .map(res => {
                println(s"UsesTestTrait.usesTestFutureTrait future completed ${res} ")
                res * 2
              })
            .recover {
              case e =>
                println(s"UsesTestTrait.usesTestFutureTrait future exception : ${e}")
                throw e
            }
  }

}
