package utils.featureflags.services

import api.AppConfig
import api.accounts.models.OrgId
import api.accounts.{OrgMetadata, TeamMetaData}
import api.prospects.MailboxFolder
import api.accounts.{OrgMetadata, TeamId, TeamMetaData}
import io.sr.billing_common.models.PlanID.{INACTIVE, PRO, STANDARD, TRIAL, ULTIMATE, V3_AGENCY_BASIC, V3_AGENCY_MC, V3_BASIC, V3_ENTERPRISE, V3_GROWTH, V4_199, V4_299, V4_EMAIL_29, V4_EMAIL_59, V4_EMAIL_99, V4_MC_129, V4_MC_39, V4_MC_79, V4_TWO_EMAIL_129, V4_TWO_EMAIL_29, V4_TWO_EMAIL_79}
import io.sr.billing_common.models.{PlanID, PlanType}
import utils.SRAppConfig
//import api.accounts.dao.OrgMetadataDAO
import play.api.libs.json.{<PERSON><PERSON>, <PERSON><PERSON>}

import scala.util.Try


case class SrFeatureFlags(

                           ff_sending_holiday_calendar: Boolean,
                           ff_ab_testing: Boolean,
                           ff_reports_hot_prospect: Boolean,
                           ff_campaign_send_start_report: Boolean

)

object SrFeatureFlags {

  implicit def writes: Writes[SrFeatureFlags] = Json.writes[SrFeatureFlags]


  // cut-pasted old fn here
  def isAbtestingAllowed(planID: PlanID, forceEnableABTestingForOrg: Boolean, orgId: Long): Boolean = {

    isAbtestingAllowedForPlans(planID = planID) || forceEnableABTestingForOrg

  }

  def showNewCampaignStartUI(
                                      orgId: OrgId,
                                      orgMetadata: OrgMetadata,
                                      planID: PlanID
                                    ): Boolean = {

    val show_new_ui_flag_from_org_metadata = orgMetadata.show_new_create_campaign_model.getOrElse(false)

    if(show_new_ui_flag_from_org_metadata){

      true

    } else {

      /*
        27-sep-2024:

          Either plan should be multichannel or multichannel should be enabled for the org

        28-sep-2024:

          we are enabling email only campaign flow for all orgs

       */
      val is_multichannel = PlanID.isMultiChannelPlan(planId = planID, ff_multichannel = orgMetadata.ff_multichannel) || orgMetadata.ff_multichannel.getOrElse(false)

      (is_multichannel)


    }

  }

  private def isAbtestingAllowedForPlans(planID: PlanID): Boolean = {

    planID match {

      case PlanID.PRO
           | PlanID.ULTIMATE
           | PlanID.TRIAL
           | PlanID.V3_BASIC
           | PlanID.V3_GROWTH
           | PlanID.V3_AGENCY_BASIC
           | PlanID.V3_AGENCY_MC
           | PlanID.V3_ENTERPRISE
           | PlanID.V4_199
           | PlanID.V4_299
           | PlanID.V4_EMAIL_29
           | PlanID.V4_EMAIL_59
           | PlanID.V4_EMAIL_99
           | PlanID.V4_MC_39
           | PlanID.V4_MC_79
           | PlanID.V4_MC_129
           | PlanID.V4_TWO_EMAIL_29
           | PlanID.V4_TWO_EMAIL_79
           | PlanID.V4_TWO_EMAIL_129
           | PlanID.V5_EMAIL_29
           | PlanID.V5_EMAIL_89
           | PlanID.V5_EMAIL_199
           | PlanID.V5_EMAIL_499
           | PlanID.V5_MC_39
           | PlanID.V5_MC_99
           | PlanID.V5_MC_249
           | PlanID.V5_MC_599 =>

        true

      case PlanID.STANDARD | PlanID.INACTIVE =>
        false
    }

  }

  def isShowFullInboxAllowed(
                              inbox: MailboxFolder,
                              planID: PlanID,
                              show_full_inbox_v3: Boolean,
                              org_id: Long
                            ): Boolean = {

    inbox match {
      case folder: MailboxFolder.CampaignInboxFolder => false

      case folder: MailboxFolder.TeamInboxFolder =>

        val planIdCondition = planID match {
          case PlanID.ULTIMATE
               | PlanID.V3_GROWTH
               | PlanID.V3_ENTERPRISE
               | PlanID.V3_AGENCY_MC =>

            //AppConfig.inboxv3_org_ids.contains(org_id)

            true

          case PlanID.PRO | PlanID.TRIAL | PlanID.INACTIVE | PlanID.STANDARD
               | PlanID.V3_BASIC
               | PlanID.V3_AGENCY_BASIC
               | PlanID.V4_199 // FIXME PlanV4: Handle according to addon
               | PlanID.V4_299 // FIXME PlanV4: Handle according to addon
               | PlanID.V4_EMAIL_29 // FIXME PlanV4: Handle according to addon
               | PlanID.V4_EMAIL_59 // FIXME PlanV4: Handle according to addon
               | PlanID.V4_EMAIL_99 // FIXME PlanV4: Handle according to addon
               | PlanID.V4_MC_39 // FIXME PlanV4: Handle according to addon
               | PlanID.V4_MC_79 // FIXME PlanV4: Handle according to addon
               | PlanID.V4_MC_129 // FIXME PlanV4: Handle according to addon
               | PlanID.V4_TWO_EMAIL_29 // FIXME PlanV4: Handle according to addon
               | PlanID.V4_TWO_EMAIL_79 // FIXME PlanV4: Handle according to addon
               | PlanID.V4_TWO_EMAIL_129 // FIXME PlanV4: Handle according to addon
               | PlanID.V5_EMAIL_29
               | PlanID.V5_EMAIL_89
               | PlanID.V5_EMAIL_199
               | PlanID.V5_EMAIL_499
               | PlanID.V5_MC_39
               | PlanID.V5_MC_99
               | PlanID.V5_MC_249
               | PlanID.V5_MC_599 =>

            false
        }

        (planIdCondition || show_full_inbox_v3)
    }


  }


  def enableLeadFinder(org_id: Long, orgMetadata: OrgMetadata): Boolean = {

    true

    /*
    if (orgMetadata.enable_lead_finder.isDefined) {
      orgMetadata.enable_lead_finder.get
    } else {
      ( (AppConfig.isProd && (AppConfig.enable_lead_finder_org_ids.contains(org_id) || org_id >= 15374) )
        || AppConfig.isLocalDevDomain
        || AppConfig.isDevDomain
        )
    }
    */
  }

  def maxPhoneNumberToBuyLimitForOrg(calling_flag: Option[Boolean], total_email_limit: Int): Int = {
    calling_flag match {
      case None =>
        0

      case Some(flag) =>

        if(flag){
          total_email_limit // currently we are using the email limit as call number limit
        }else {
          0
        }
    }
  }




  private def isHotProspectsReportEnabled(
    planID: PlanID
  ): Boolean = {

    planID match {

      case PlanID.ULTIMATE
           | PlanID.V3_GROWTH
           | PlanID.V3_AGENCY_MC
           | PlanID.V3_ENTERPRISE
           | PlanID.V4_199
           | PlanID.V4_EMAIL_29
           | PlanID.V4_EMAIL_59
           | PlanID.V4_EMAIL_99
           | PlanID.V4_MC_39
           | PlanID.V4_MC_79
           | PlanID.V4_MC_129
           | PlanID.V4_299
           | PlanID.V4_TWO_EMAIL_29
           | PlanID.V4_TWO_EMAIL_79
           | PlanID.V4_TWO_EMAIL_129
           | PlanID.V5_EMAIL_29
           | PlanID.V5_EMAIL_89
           | PlanID.V5_EMAIL_199
           | PlanID.V5_EMAIL_499
           | PlanID.V5_MC_39
           | PlanID.V5_MC_99
           | PlanID.V5_MC_249
           | PlanID.V5_MC_599

        => true

      case PlanID.TRIAL
        | PlanID.INACTIVE
        | PlanID.STANDARD
        | PlanID.PRO
        | PlanID.V3_BASIC
        | PlanID.V3_AGENCY_BASIC

        => false
    }

  }

  private def isSendingHolidayCalendarEnabled(
    planID: PlanID,
    orgMetadata: OrgMetadata
  ): Boolean = {

   val planIdCheck: Boolean =  planID match {

      case PlanID.ULTIMATE
           | PlanID.V3_GROWTH
           | PlanID.V3_AGENCY_MC
           | PlanID.V3_ENTERPRISE
           | PlanID.V4_199
           | PlanID.V4_299
           | PlanID.V4_EMAIL_99
           | PlanID.V4_MC_129
           | PlanID.V4_TWO_EMAIL_79
           | PlanID.V4_TWO_EMAIL_129
           | PlanID.V5_EMAIL_89
           | PlanID.V5_EMAIL_199
           | PlanID.V5_EMAIL_499
           | PlanID.V5_MC_99
           | PlanID.V5_MC_249
           | PlanID.V5_MC_599

      => true

      case PlanID.PRO
           | PlanID.TRIAL
           | PlanID.INACTIVE
           | PlanID.STANDARD
           | PlanID.V3_BASIC
           | PlanID.V3_AGENCY_BASIC
           | PlanID.V4_EMAIL_29
           | PlanID.V4_EMAIL_59
           | PlanID.V4_MC_39
           | PlanID.V4_MC_79
           | PlanID.V4_TWO_EMAIL_29
           | PlanID.V5_EMAIL_29
           | PlanID.V5_MC_39

      => false
    }

    planIdCheck || orgMetadata.show_sending_holiday_calendar.getOrElse(false)

  }

  final def getFeatureFlags(
    planID: PlanID,
    isSupportAccount: Boolean,
    orgMetadata: OrgMetadata // Will be used in future inside ffCampaignSendStartReport check
  ): SrFeatureFlags = {

    val ffSendingHolidayCalendar = isSendingHolidayCalendarEnabled(
      planID = planID,
      orgMetadata = orgMetadata
    )

    val ffABTesting = isAbtestingAllowedForPlans(
      planID = planID,
    )

    val ff_reports_hot_prospect = isHotProspectsReportEnabled(
      planID = planID
    )

    val ffCampaignSendStartReport = if (!isSupportAccount) {
      false
    }
    else {
      true
    }

    val ff = SrFeatureFlags(
      ff_reports_hot_prospect = ff_reports_hot_prospect,
      ff_sending_holiday_calendar = ffSendingHolidayCalendar,
      ff_ab_testing = ffABTesting,
      ff_campaign_send_start_report = ffCampaignSendStartReport
    )

    ff
  }


  def isJune2024PermissionFlow(
                                is_simpler_perms_from_db: Option[Boolean],
                                org_id: OrgId,
                                planType: PlanType,
                          ): Boolean = {

    val isJune2024PermissionFlow: Boolean = if(is_simpler_perms_from_db.isDefined) is_simpler_perms_from_db.get
    else if (planType == PlanType.TRIAL) true
    else if (AppConfig.isProd && org_id.id > AppConfig.RollingUpdates.org_id_for_june_2024_simpler_perms_new_signups) true
    else if (!AppConfig.isProd) true
    else false

    isJune2024PermissionFlow

  }



  def isTeamInboxEnabled(
    orgId: OrgId,
    planId: PlanID,
    is_team_inbox_enabled: Option[Boolean],
  ): Boolean = {

    if (!AppConfig.isProd) {

      true

    } else {


      if (AppConfig.orgIdsUsingTeamInbox.contains(orgId.id)) {

        true

      } else {

        is_team_inbox_enabled.getOrElse(false)

      }

    }

  }


  def enableVoicedrop(
                                     orgId: OrgId,
                                     enable_voicedrop: Option[Boolean],
                                   ): Boolean = {

    if (!AppConfig.isProd) {

      true

    } else {
      


      enable_voicedrop.getOrElse(true)

      

    }

  }
  
  
  def enableCaptainDataLIAutomation(
                                     orgId: OrgId,
                                     enable_captain_data_automation: Option[Boolean],
                                   
                                   ): Boolean = {
    //    if(!AppConfig.isProd) {
    //
    //      true
    //
    //    }else{
    //
    //      enable_captain_data_automation.getOrElse(false)
    //
    //    }

    /*
          Date : 03-05-2025
          Enable captain data automation for all Orgs
         */
    true

  }

  def enableInternalEmailAccountsApiForWarmuphero(
    orgId: OrgId,
    enable_internal_email_accounts_api_for_warmuphero: Option[Boolean],
  ): Boolean = {

    if (!AppConfig.isProd) {

      true

    } else {

      /**
        * 30 Apr 2025
        *
        * Open internal email accounts API for WarmupHero,
        * to all organizations.
        */
      if (!AppConfig.orgsUsingSameApiKeyInMultipleWHAccounts.contains(orgId.id)) {

        true

      } else {

        enable_internal_email_accounts_api_for_warmuphero.getOrElse(false)

      }

    }

  }

  def enableWhAutoLogin(
    orgId: OrgId,
    enable_wh_auto_login: Option[Boolean],
  ): Boolean = {

    if (!AppConfig.isProd) {

      true

    } else {

      if (!AppConfig.orgsUsingSameApiKeyInMultipleWHAccounts.contains(orgId.id)) {

        true

      } else {

        enable_wh_auto_login.getOrElse(false)

      }

    }

  }

  def enableAiEmailGenerationForWarmuphero(
    enable_ai_email_generation_for_warmuphero: Option[Boolean],
  ): Boolean = {

    if (!AppConfig.isProd) {

      true

    } else {

      enable_ai_email_generation_for_warmuphero.getOrElse(false)

    }

  }


  def showPurchasedDomainsAndEmails(
    planId: PlanID,
    show_purchased_domains_and_emails: Boolean
  ): Boolean = {

    if (!AppConfig.isProd) {

      true

    } else {

      show_purchased_domains_and_emails || PlanID.showPurchasedDomainsAndEmails(planId = planId)

    }

  }


  def showLeadsSentForValidationBanner(
    show_leads_sent_for_validation_banner: Option[Boolean],
    orgId: OrgId,
  ): Boolean = {

    if (!AppConfig.isProd) {

      true

    } else {

      val enabledEmailValidationForLeadFinder = AppConfig.RollingUpdates.enableEmailValidationForLeadFinder(
        orgId = orgId
      )

      if (enabledEmailValidationForLeadFinder) {

        true

      } else {

        show_leads_sent_for_validation_banner.getOrElse(false)

      }

    }

  }


    def doShowEnableAgencyOption(planId: PlanID): Boolean = {
        planId match {

            case PlanID.V5_EMAIL_29 |
                 PlanID.V5_EMAIL_89 |
                 PlanID.V5_EMAIL_199 |
                 PlanID.V5_EMAIL_499 |
                 PlanID.V5_MC_39 |
                 PlanID.V5_MC_99 |
                 PlanID.V5_MC_249 |
                 PlanID.V5_MC_599 |
                 TRIAL |
                 INACTIVE =>
                true

            case STANDARD |
                 PRO |
                 ULTIMATE |
                 V3_BASIC |
                 V3_GROWTH |
                 V3_AGENCY_BASIC |
                 V3_AGENCY_MC |
                 V3_ENTERPRISE |
                 V4_199 |
                 V4_299 |
                 V4_EMAIL_29 |
                 V4_EMAIL_59 |
                 V4_EMAIL_99 |
                 V4_MC_39 |
                 V4_MC_79 |
                 V4_MC_129 |
                 V4_TWO_EMAIL_29 |
                 V4_TWO_EMAIL_79 |
                 V4_TWO_EMAIL_129  =>

                false

        }
    }

    def isAllowAgencyChange(planId:PlanID) :Boolean = {
        planId match {

            case PlanID.V5_EMAIL_199 |
                 PlanID.V5_EMAIL_499 |
                 PlanID.V5_MC_249 |
                 PlanID.V5_MC_599 |
                 TRIAL |
                 INACTIVE =>
                true

            case STANDARD |
                 PRO |
                 ULTIMATE |
                 V3_BASIC |
                 V3_GROWTH |
                 V3_AGENCY_BASIC |
                 V3_AGENCY_MC |
                 V3_ENTERPRISE |

                 V4_199 |
                 V4_299 |
                 V4_EMAIL_29 |
                 V4_EMAIL_59 |
                 V4_EMAIL_99 |
                 V4_MC_39 |
                 V4_MC_79 |
                 V4_MC_129 |
                 V4_TWO_EMAIL_29 |
                 V4_TWO_EMAIL_79 |
                 V4_TWO_EMAIL_129 |
                 PlanID.V5_EMAIL_29 |
                 PlanID.V5_EMAIL_89 |
                 PlanID.V5_MC_39 |
                 PlanID.V5_MC_99 =>

                false

        }
    }

  def getDaysLimitForInbox(
                            inbox: MailboxFolder
                          ): Int = {
    inbox match {
      case folder: MailboxFolder.TeamInboxFolder => AppConfig.days_limit_to_fetch_inbox_threads
      case folder: MailboxFolder.CampaignInboxFolder => AppConfig.campaign_inbox_days_limit_for_emails
    }
  }

  def showMagicContentSteps(
                             orgId: OrgId,
                             orgMetadata: OrgMetadata
                           ) = {

    orgMetadata.show_magic_content_steps

  }

  def useSrAiApi(
                  orgId: OrgId,
                  orgMetadata: OrgMetadata,
                  planID: PlanID
                ) = {
//    planID match {
//      case PlanID.V3_GROWTH
//           | PlanID.V3_AGENCY_BASIC
//           | PlanID.V3_AGENCY_MC
//           | PlanID.V3_ENTERPRISE
//           | PlanID.TRIAL
//           | PlanID.V4_199
//           | PlanID.V4_299
//           | PlanID.V4_TWO_EMAIL_79
//           | PlanID.V4_MC_79
//           | PlanID.V4_EMAIL_99
//           | PlanID.V4_TWO_EMAIL_129
//           | PlanID.V4_MC_129
//           | PlanID.V5_EMAIL_89
//           | PlanID.V5_EMAIL_199
//           | PlanID.V5_EMAIL_499
//           | PlanID.V5_MC_99
//           | PlanID.V5_MC_249
//           | PlanID.V5_MC_599
//
//      => false
//
//      case PlanID.INACTIVE
//           | PlanID.STANDARD
//           | PlanID.PRO
//           | PlanID.ULTIMATE
//           | PlanID.V3_BASIC
//           | PlanID.V4_EMAIL_29
//           | PlanID.V4_TWO_EMAIL_29
//           | PlanID.V4_MC_39
//           | PlanID.V4_EMAIL_59
//           | PlanID.V5_EMAIL_29
//           | PlanID.V5_MC_39

//      => 
    orgMetadata.allow_using_sr_ai_api.getOrElse(false)
//    }

  }

  def enableOauthForZapmail(
                             orgId: OrgId,
                             orgMetadata: OrgMetadata
                           ): Boolean = {
    orgMetadata.enable_oauth_for_zapmail.getOrElse(false)
  }



  def getEmailLimitForCampaign(planID: PlanID, orgId: OrgId) :Int = {
    if(AppConfig.email_setting_limit_in_campaign_for_org.contains(orgId)) {
      100
    } else {
      planID match {
          case PlanID.ULTIMATE
               | PlanID.V3_GROWTH
               | PlanID.V3_AGENCY_MC
               | PlanID.V3_ENTERPRISE
               | PlanID.V4_199
               | PlanID.V4_299
               | PlanID.V4_MC_79
               | PlanID.V4_EMAIL_99
               | PlanID.V4_MC_129
               | PlanID.V4_TWO_EMAIL_79
               | PlanID.V4_TWO_EMAIL_129
               | PlanID.V5_EMAIL_89
               | PlanID.V5_EMAIL_199
               | PlanID.V5_EMAIL_499
               | PlanID.V5_MC_99
               | PlanID.V5_MC_249
               | PlanID.V5_MC_599

          => AppConfig.email_setting_limit_in_campaign_for_plan_2

          case PlanID.PRO
               | PlanID.TRIAL
               | PlanID.INACTIVE
               | PlanID.STANDARD
               | PlanID.V3_BASIC
               | PlanID.V3_AGENCY_BASIC
               | PlanID.V4_EMAIL_29
               | PlanID.V4_EMAIL_59
               | PlanID.V4_MC_39
               | PlanID.V4_TWO_EMAIL_29
               | PlanID.V5_EMAIL_29
               | PlanID.V5_MC_39

          => AppConfig.email_setting_limit_in_campaign_for_plan_1
      }
  }
  }

  /*
  15 May 2025 :
     Enable calendly integration for 89+ plans
   */

    def enableCalendlyIntegration(planID: PlanID, orgId: OrgId): Boolean = {

        planID match {
            case PlanID.V4_199
                 | PlanID.V4_299
                 | PlanID.V4_EMAIL_99
                 | PlanID.V4_MC_129
                 | PlanID.V4_TWO_EMAIL_129
                 | PlanID.V5_EMAIL_89
                 | PlanID.V5_EMAIL_199
                 | PlanID.V5_EMAIL_499
                 | PlanID.V5_MC_99
                 | PlanID.V5_MC_249
                 | PlanID.V5_MC_599
                 | PlanID.TRIAL

            => true

            case PlanID.PRO
                 | PlanID.INACTIVE
                 | PlanID.STANDARD
                 | PlanID.V3_BASIC
                 | PlanID.V3_AGENCY_BASIC
                 | PlanID.V4_EMAIL_29
                 | PlanID.V4_EMAIL_59
                 | PlanID.V4_MC_39
                 | PlanID.V4_TWO_EMAIL_29
                 | PlanID.V5_EMAIL_29
                 | PlanID.V5_MC_39
                 | PlanID.V4_MC_79
                 | PlanID.V4_TWO_EMAIL_79
                 | PlanID.ULTIMATE
                 | PlanID.V3_GROWTH
                 | PlanID.V3_AGENCY_MC
                 | PlanID.V3_ENTERPRISE

            => false
        }
    }
}

